stages:
  - build
  - finish
cache:
  paths:
    - .m2/repository
  key: one-key-to-rule-them-all

#sonarqube-check:
#  tags:
#    - locaweb
#    - shell
#    - java8
#    - large
#  variables:
#    SONAR_USER_HOME: "${CI_PROJECT_DIR}/.sonar"  # Defines the location of the analysis task cache
#    GIT_DEPTH: "0"  # Tells git to fetch all the branches of the project, required by the analysis task
#  cache:
#    key: "${CI_JOB_NAME}"
#    paths:
#      - .sonar/cache
#  script:
#    - docker run --network host -v $(pwd)/.m2/repository:/root/.m2/repository -v $(pwd):/usr/src/zw -w /usr/src/zw registry.gitlab.com/plataformazw/docker-pacto/maven:3-jdk-11 mvn -T 1C verify sonar:sonar -Dsonar.host.url="${SONAR_HOST_URL}" -Dsonar.token=${SONAR_TOKEN} -Dsonar.projectKey=Plataformazw_zw_AYRj1Yg6xRBA3XfKrW61
#  allow_failure: true
#  only:
#    variables:
#      - $CHECK_QUALITY


docker-tomcat-build:
 stage: build
 interruptible: true
 tags:
   - locaweb
   - shell
   - java8
   - large
 only:
   - master
   - merge_requests
   - develop
   - release/*
   - tags
 except:
  changes:
    - .bumpversion.cfg
    - deploy-env.sh
#    - .gitlab-*.yml
 script:
   - docker login -u $CI_USER -p $CI_USER_PASS $CI_REGISTRY
   - cd docker
   - chmod +x build-push.sh
   - ./build-push.sh $CI_COMMIT_REF_SLUG tomcat
 after_script:
   - |
     if [ "$CI_JOB_STATUS" == "failed" ] && [[ "$CI_COMMIT_REF_NAME" == master* || "$CI_COMMIT_REF_NAME" == release* ]]; then
       curl -X POST -H "Content-Type: application/json" --data "{\"text\":\"🚨🚨 *ALERTA CRÍTICO: BUILD COM FALHA* 🚨🚨\n\n❗ *Projeto*: $CI_PROJECT_NAME\n❗ *Branch*: $CI_COMMIT_REF_NAME\n\n---\n\n⚠️ A pipeline apresentou uma *falha* durante o processo de build. \n➡️ Por favor, acesse a pipeline ($CI_PIPELINE_URL) para verificar os detalhes e resolver o problema.\"}" "https://chat.googleapis.com/v1/spaces/AAAABogbUp0/messages?key=AIzaSyDdI0hCZtE6vySjMm-WEfRq3CPzqKqqsHI&token=_kJcnaZJ24lMdOk7czpPoB963XWbTAMX8WNU6MZUR6Y";
     fi
 retry:
   max: 2
   when: runner_system_failure

docker-build-tag:
 stage: build
 interruptible: true
 tags:
   - locaweb
   - shell
   - java8
   - large
 only:
   variables:
      - $RELEASE_TAG

 script:
   - docker login -u $CI_USER -p $CI_USER_PASS $CI_REGISTRY
   - cd docker
   - chmod +x build-push.sh
   - ./build-push.sh $CI_COMMIT_REF_SLUG tomcat
 retry:
   max: 2
   when: runner_system_failure


tag-version:
  image: registry.gitlab.com/plataformazw/tag-versions:master
  interruptible: true
  tags:
    - docker
  stage: finish
  when: on_success
  only:
    variables:
      - $RELEASE_TAG
  script:
    - eval $(ssh-agent -s)
    - echo "$SSH_PRIVATE_KEY" | tr -d '\r' | ssh-add -
    - git config --global user.name "Pipeline"
    - git config --global user.email "<EMAIL>"
    - git tag -l | xargs git tag -d
    - bumpversion  patch
    - git remote show origin
    - git remote set-url --<NAME_EMAIL>:$CI_PROJECT_PATH
    - git remote show origin
    - git tag -f latest -m "Deploy tag"
    - GIT_SSH_COMMAND="ssh -o UserKnownHostsFile=/dev/null -o StrictHostKeyChecking=no" git push origin HEAD:$CI_COMMIT_REF_NAME
    - GIT_SSH_COMMAND="ssh -o UserKnownHostsFile=/dev/null -o StrictHostKeyChecking=no" git push -f --tags
  retry:
    max: 2
    when: runner_system_failure
  cache: []

include:
  - local: .gitlab-ci-stages.yml
