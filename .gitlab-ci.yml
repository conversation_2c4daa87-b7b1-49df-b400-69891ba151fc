stages:
  - build
  - finish
cache:
  paths:
    - .m2/repository
  key: one-key-to-rule-them-all

#sonarqube-check:
#  tags:
#    - locaweb
#    - shell
#    - java8
#    - large
#  variables:
#    SONAR_USER_HOME: "${CI_PROJECT_DIR}/.sonar"  # Defines the location of the analysis task cache
#    GIT_DEPTH: "0"  # Tells git to fetch all the branches of the project, required by the analysis task
#  cache:
#    key: "${CI_JOB_NAME}"
#    paths:
#      - .sonar/cache
#  script:
#    - docker run --network host -v $(pwd)/.m2/repository:/root/.m2/repository -v $(pwd):/usr/src/zw -w /usr/src/zw registry.gitlab.com/plataformazw/docker-pacto/maven:3-jdk-11 mvn -T 1C verify sonar:sonar -Dsonar.host.url="${SONAR_HOST_URL}" -Dsonar.token=${SONAR_TOKEN} -Dsonar.projectKey=Plataformazw_zw_AYRj1Yg6xRBA3XfKrW61
#  allow_failure: true
#  only:
#    variables:
#      - $CHECK_QUALITY

  
docker-tomcat-build:
 stage: build
 interruptible: true
 tags:
   - locaweb
   - shell
   - java8
   - large
 only:
   - master
   - merge_requests
   - develop
   - release/*
   - tags
 except:
  changes:
    - .bumpversion.cfg
    - deploy-env.sh
#    - .gitlab-*.yml
 script:
   - docker login -u $CI_USER -p $CI_USER_PASS $CI_REGISTRY
   - cd docker
   - chmod +x build-push.sh
   - ./build-push.sh $CI_COMMIT_REF_SLUG tomcat
 after_script:
   - |
     if [ "$CI_JOB_STATUS" == "failed" ] && [[ "$CI_COMMIT_REF_NAME" == master* || "$CI_COMMIT_REF_NAME" == release* ]]; then
       curl -X POST -H "Content-Type: application/json" --data "{\"text\":\"🚨🚨 *ALERTA CRÍTICO: BUILD COM FALHA* 🚨🚨\n\n❗ *Projeto*: $CI_PROJECT_NAME\n❗ *Branch*: $CI_COMMIT_REF_NAME\n\n---\n\n⚠️ A pipeline apresentou uma *falha* durante o processo de build. \n➡️ Por favor, acesse a pipeline ($CI_PIPELINE_URL) para verificar os detalhes e resolver o problema.\"}" "https://chat.googleapis.com/v1/spaces/AAAABogbUp0/messages?key=AIzaSyDdI0hCZtE6vySjMm-WEfRq3CPzqKqqsHI&token=_kJcnaZJ24lMdOk7czpPoB963XWbTAMX8WNU6MZUR6Y";
     fi
 retry:
   max: 2
   when: runner_system_failure

docker-build-tag:
 stage: build
 interruptible: true
 tags:
   - locaweb
   - shell
   - java8
   - large
 only:   
   variables:
      - $RELEASE_TAG 

 script:
   - docker login -u $CI_USER -p $CI_USER_PASS $CI_REGISTRY
   - cd docker
   - chmod +x build-push.sh
   - ./build-push.sh $CI_COMMIT_REF_SLUG tomcat
 retry:
   max: 2
   when: runner_system_failure

# TESTES AUTOMATIZADOS PARA GERAÇÃO DE TAG MOVIDOS PARA PROJETO TESTE-AUTO
########deploy:
########  stage: deploy
########  resource_group: $CI_PROJECT_ID
########  interruptible: true
########  needs:
########    - job: docker-build-tag
########  tags:
########    - swarm
########    - locaweb
########    - manager
########  only:
########    variables:
########      - $RELEASE_TAG
########  script:
########    - docker login -u "<EMAIL>" -p "$TKN_PIPE_TESTE_AUTO_READ" registry.gitlab.com
########    - chmod +x deploy-env.sh
########    - chmod +x deploy-server-pipeline.sh
########    - timeout 30 ./deploy-env.sh
########    - timeout 600 ./deploy-server-pipeline.sh
########  artifacts:
########    paths:
########      - .env
########  retry:
########    max: 2
########    when: runner_system_failure
########
########.job_teste_template:
########  stage: test
########  resource_group: $CI_JOB_NAME
########  interruptible: true
########  tags:
########    - locaweb
########    - shell
########    - large
########  needs:
########    - job: test-atualiza-db
########      artifacts: true
########  before_script:
########    - export $(grep -v '^#' .env | xargs)
########    - source .env
########    - docker login -u "<EMAIL>" -p "$TKN_PIPE_TESTE_AUTO_READ" registry.gitlab.com
########    - docker pull $IMAGE
########    - export HOSTS_LINE="$HOST_DNS:$HOST_PIPELINE"
########    - export RUN="docker run --add-host ${HOSTS_LINE} --rm --name=cypress-zw-$CI_PIPELINE_ID-$CI_JOB_NAME_SLUG "
########  variables:
########    CY_CONFIG_FILE: "./cypress.json"
########    CYPRESS_RECORD_KEY: "35487b56-a039-49c5-8480-333a43ebb9d9"
########  only:
########    variables:
########      - $RELEASE_TAG
########  retry:
########    max: 2
########    when: runner_system_failure
########  artifacts:
########    paths:
########      - .env
########
########include:
########  - local: '.gitlab-ci-tests.yml'

########clear-deploy:
########  stage: finish
########  when: always
########  tags:
########    - swarm
########    - locaweb
########    - manager
########  only:
########    variables:
########      - $RELEASE_TAG
########  artifacts:
########    paths:
########      - .env
########  script:
########    - source .env
########    - docker stack rm $STACK_NAME
########    - sleep 2m
########  retry:
########    max: 2
########    when: runner_system_failure
########  cache: []


zeta-stage_merge-develop:
  image: registry.gitlab.com/plataformazw/tag-versions:master
  interruptible: true
  tags:
    - docker
  stage: finish
  allow_failure: true
  only:
    - master
  script:
    - set +e                  #Sem isso, o script não funciona, pois configura o script para executar mesmo com erro.
    - eval $(ssh-agent -s)
    - echo "$SSH_PRIVATE_KEY" | tr -d '\r' | ssh-add -
    - git config --global user.name "Pipeline"
    - git config --global user.email "<EMAIL>"
    - git remote set-url --<NAME_EMAIL>:$CI_PROJECT_PATH
    - git checkout develop && git pull
    - PULL_RESULT=$(git pull origin master)
    - |
      if echo "$PULL_RESULT" | grep -q "Automatic merge failed"; then
        echo "Contém conflitos!!";
        curl -s --request POST --header "PRIVATE-TOKEN: $API_TOKEN" --data "source_branch=master&target_branch=develop&title=AutoMerge" "$CI_API_V4_URL/projects/$CI_PROJECT_ID/merge_requests";
        curl -s --request POST --header "Content-Type: application/json" --data '{"text": "*Conflitos no ZW!!*\n*Branch: develop*\nDevelop Guardian, cadê você?"}' "https://chat.googleapis.com/v1/spaces/AAAABogbUp0/messages?key=AIzaSyDdI0hCZtE6vySjMm-WEfRq3CPzqKqqsHI&token=_kJcnaZJ24lMdOk7czpPoB963XWbTAMX8WNU6MZUR6Y"
        exit 1
      else 
        echo $CI_COMMIT_REF_NAME;
        echo "Não contém conflito";
        GIT_SSH_COMMAND="ssh -o UserKnownHostsFile=/dev/null -o StrictHostKeyChecking=no" git push
      fi

omega-stage_merge-release:
  image: registry.gitlab.com/plataformazw/tag-versions:master
  interruptible: true
  tags:
    - docker
  stage: finish
  allow_failure: true
  only:
    - master
  script:
    - set +e                  #Sem isso, o script não funciona, pois configura o script para executar mesmo com erro.
    - eval $(ssh-agent -s)
    - echo "$SSH_PRIVATE_KEY" | tr -d '\r' | ssh-add -
    - git config --global user.name "Pipeline"
    - git config --global user.email "<EMAIL>"
    - git remote set-url --<NAME_EMAIL>:$CI_PROJECT_PATH
    - git checkout "release/RC"
    - git pull
    - PULL_RESULT=$(git pull origin master)
    - |
      if echo "$PULL_RESULT" | grep -q "Automatic merge failed"; then
        echo "Contém conflitos!!";
        curl -s --request POST --header "PRIVATE-TOKEN: $API_TOKEN" --data "source_branch=master&target_branch=release%2FRC&title=AutoMerge" "$CI_API_V4_URL/projects/$CI_PROJECT_ID/merge_requests";
        curl -s --request POST --header "Content-Type: application/json" --data '{"text": "*Conflitos no ZW!!*\n*Branch: release/RC*\nRelease Guardian, cadê você?"}' "https://chat.googleapis.com/v1/spaces/AAAABogbUp0/messages?key=AIzaSyDdI0hCZtE6vySjMm-WEfRq3CPzqKqqsHI&token=_kJcnaZJ24lMdOk7czpPoB963XWbTAMX8WNU6MZUR6Y"
        exit 1
      else 
        echo $CI_COMMIT_REF_NAME;
        echo "Não contém conflito";
        GIT_SSH_COMMAND="ssh -o UserKnownHostsFile=/dev/null -o StrictHostKeyChecking=no" git push --set-upstream origin release/RC
      fi

psi-stage_merge-edc:
  image: registry.gitlab.com/plataformazw/tag-versions:master
  interruptible: true
  tags:
    - docker
  stage: finish
  allow_failure: true
  only:
    - master
  script:
    - set +e                  #Sem isso, o script não funciona, pois configura o script para executar mesmo com erro.
    - eval $(ssh-agent -s)
    - echo "$SSH_PRIVATE_KEY" | tr -d '\r' | ssh-add -
    - git config --global user.name "Pipeline"
    - git config --global user.email "<EMAIL>"
    - git remote set-url --<NAME_EMAIL>:$CI_PROJECT_PATH
    - git checkout "release/RC-EDC" && git pull
    - PULL_RESULT=$(git pull origin master)
    - |
      if echo "$PULL_RESULT" | grep -q "Automatic merge failed"; then
        echo "Contém conflitos!!";
        curl -s --request POST --header "PRIVATE-TOKEN: $API_TOKEN" --data "source_branch=master&target_branch=release%2FRC-EDC&title=AutoMerge%20(Master_RC-EDC)" "$CI_API_V4_URL/projects/$CI_PROJECT_ID/merge_requests";
        curl -s --request POST --header "Content-Type: application/json" --data '{"text": "*Conflitos no ZW!!*\n*Branch: release/RC-EDC*\nGuardião da branch ENGENHARIA, apresente-se!?"}' "https://chat.googleapis.com/v1/spaces/AAAABogbUp0/messages?key=AIzaSyDdI0hCZtE6vySjMm-WEfRq3CPzqKqqsHI&token=_kJcnaZJ24lMdOk7czpPoB963XWbTAMX8WNU6MZUR6Y"
        exit 1
      else
        echo $CI_COMMIT_REF_NAME;
        echo "Não contém conflito";
        GIT_SSH_COMMAND="ssh -o UserKnownHostsFile=/dev/null -o StrictHostKeyChecking=no" git push
      fi

chi-stage_merge-gc:
  image: registry.gitlab.com/plataformazw/tag-versions:master
  interruptible: true
  tags:
    - docker
  stage: finish
  allow_failure: true
  only:
    - master
  script:
    - set +e                  #Sem isso, o script não funciona, pois configura o script para executar mesmo com erro.
    - eval $(ssh-agent -s)
    - echo "$SSH_PRIVATE_KEY" | tr -d '\r' | ssh-add -
    - git config --global user.name "Pipeline"
    - git config --global user.email "<EMAIL>"
    - git remote set-url --<NAME_EMAIL>:$CI_PROJECT_PATH
    - git checkout "release/RC-GC" && git pull
    - PULL_RESULT=$(git pull origin master)
    - |
      if echo "$PULL_RESULT" | grep -q "Automatic merge failed"; then
        echo "Contém conflitos!!";
        curl -s --request POST --header "PRIVATE-TOKEN: $API_TOKEN" --data "source_branch=master&target_branch=release%2FRC-GC&title=AutoMerge%20(Master_RC-GC)" "$CI_API_V4_URL/projects/$CI_PROJECT_ID/merge_requests";
        curl -s --request POST --header "Content-Type: application/json" --data '{"text": "*Conflitos no ZW!!*\n*Branch: release/RC-GC*\nGuardião da branch GC, apresente-se!?"}' "https://chat.googleapis.com/v1/spaces/AAAABogbUp0/messages?key=AIzaSyDdI0hCZtE6vySjMm-WEfRq3CPzqKqqsHI&token=_kJcnaZJ24lMdOk7czpPoB963XWbTAMX8WNU6MZUR6Y"
        exit 1
      else
        echo $CI_COMMIT_REF_NAME;
        echo "Não contém conflito";
        GIT_SSH_COMMAND="ssh -o UserKnownHostsFile=/dev/null -o StrictHostKeyChecking=no" git push
      fi

#fi-stage_merge-pratique:
#  image: registry.gitlab.com/plataformazw/tag-versions:master
#  interruptible: true
#  tags:
#    - docker
#  stage: finish
#  allow_failure: true
#  only:
#    - master
#  script:
#    - set +e                  #Sem isso, o script não funciona, pois configura o script para executar mesmo com erro.
#    - eval $(ssh-agent -s)
#    - echo "$SSH_PRIVATE_KEY" | tr -d '\r' | ssh-add -
#    - git config --global user.name "Pipeline"
#    - git config --global user.email "<EMAIL>"
#    - git remote set-url --<NAME_EMAIL>:$CI_PROJECT_PATH
#    - git checkout "release/RC-PRATIQUE" && git pull
#    - PULL_RESULT=$(git pull origin master)
#    - |
#      if echo "$PULL_RESULT" | grep -q "Automatic merge failed"; then
#        echo "Contém conflitos!!";
#        curl -s --request POST --header "PRIVATE-TOKEN: $API_TOKEN" --data "source_branch=master&target_branch=release%2FRC-PRATIQUE&title=AutoMerge%20(Master_RC-PRATIQUE)" "$CI_API_V4_URL/projects/$CI_PROJECT_ID/merge_requests";
#        curl -s --request POST --header "Content-Type: application/json" --data '{"text": "*Conflitos no ZW!!*\n*Branch: release/RC-PRATIQUE*\nGuardião da branch PRATIQUE, apresente-se!?"}' "https://chat.googleapis.com/v1/spaces/AAAABogbUp0/messages?key=AIzaSyDdI0hCZtE6vySjMm-WEfRq3CPzqKqqsHI&token=_kJcnaZJ24lMdOk7czpPoB963XWbTAMX8WNU6MZUR6Y"
#        exit 1
#      else
#        echo $CI_COMMIT_REF_NAME;
#        echo "Não contém conflito";
#        GIT_SSH_COMMAND="ssh -o UserKnownHostsFile=/dev/null -o StrictHostKeyChecking=no" git push
#      fi

upsilon-stage_merge-manaus:
  image: registry.gitlab.com/plataformazw/tag-versions:master
  interruptible: true
  tags:
    - docker
  stage: finish
  allow_failure: true
  only:
    - master
  script:
    - set +e                  #Sem isso, o script não funciona, pois configura o script para executar mesmo com erro.
    - eval $(ssh-agent -s)
    - echo "$SSH_PRIVATE_KEY" | tr -d '\r' | ssh-add -
    - git config --global user.name "Pipeline"
    - git config --global user.email "<EMAIL>"
    - git remote set-url --<NAME_EMAIL>:$CI_PROJECT_PATH
    - git checkout "release/rc-manaus" && git pull
    - PULL_RESULT=$(git pull origin master)
    - |
      if echo "$PULL_RESULT" | grep -q "Automatic merge failed"; then
        echo "Contém conflitos!!";
        curl -s --request POST --header "PRIVATE-TOKEN: $API_TOKEN" --data "source_branch=master&target_branch=release%2Frc-manaus&title=AutoMerge%20(Master_RC-MANAUS)" "$CI_API_V4_URL/projects/$CI_PROJECT_ID/merge_requests";
        curl -s --request POST --header "Content-Type: application/json" --data '{"text": "*Conflitos no ZW!!*\n*Branch: release/RC-MANAUS*\nGuardião da branch RC-MANAUS, apresente-se!?"}' "https://chat.googleapis.com/v1/spaces/AAAABogbUp0/messages?key=AIzaSyDdI0hCZtE6vySjMm-WEfRq3CPzqKqqsHI&token=_kJcnaZJ24lMdOk7czpPoB963XWbTAMX8WNU6MZUR6Y"
        exit 1
      else
        echo $CI_COMMIT_REF_NAME;
        echo "Não contém conflito";
        GIT_SSH_COMMAND="ssh -o UserKnownHostsFile=/dev/null -o StrictHostKeyChecking=no" git push
      fi

tau-stage_merge-corpo-saude:
  image: registry.gitlab.com/plataformazw/tag-versions:master
  interruptible: true
  tags:
    - docker
  stage: finish
  allow_failure: true
  only:
    - master
  script:
    - set +e                  #Sem isso, o script não funciona, pois configura o script para executar mesmo com erro.
    - eval $(ssh-agent -s)
    - echo "$SSH_PRIVATE_KEY" | tr -d '\r' | ssh-add -
    - git config --global user.name "Pipeline"
    - git config --global user.email "<EMAIL>"
    - git remote set-url --<NAME_EMAIL>:$CI_PROJECT_PATH
    - git checkout "release/rc-corpo-saude" && git pull
    - PULL_RESULT=$(git pull origin master)
    - |
      if echo "$PULL_RESULT" | grep -q "Automatic merge failed"; then
        echo "Contém conflitos!!";
        curl -s --request POST --header "PRIVATE-TOKEN: $API_TOKEN" --data "source_branch=master&target_branch=release%2Frc-corpo-saude&title=AutoMerge%20(Master_RC-corpo-saude)" "$CI_API_V4_URL/projects/$CI_PROJECT_ID/merge_requests";
        curl -s --request POST --header "Content-Type: application/json" --data '{"text": "*Conflitos no ZW!!*\n*Branch: release/rc-corpo-saude*\nGuardião da branch rc-corpo-saude, apresente-se!?"}' "https://chat.googleapis.com/v1/spaces/AAAABogbUp0/messages?key=AIzaSyDdI0hCZtE6vySjMm-WEfRq3CPzqKqqsHI&token=_kJcnaZJ24lMdOk7czpPoB963XWbTAMX8WNU6MZUR6Y"
        exit 1
      else
        echo $CI_COMMIT_REF_NAME;
        echo "Não contém conflito";
        GIT_SSH_COMMAND="ssh -o UserKnownHostsFile=/dev/null -o StrictHostKeyChecking=no" git push
      fi

#qui-stage_merge-live:
#  image: registry.gitlab.com/plataformazw/tag-versions:master
#  interruptible: true
#  tags:
#    - docker
#  stage: finish
#  allow_failure: true
#  only:
#    - master
#  script:
#    - set +e                  #Sem isso, o script não funciona, pois configura o script para executar mesmo com erro.
#    - eval $(ssh-agent -s)
#    - echo "$SSH_PRIVATE_KEY" | tr -d '\r' | ssh-add -
#    - git config --global user.name "Pipeline"
#    - git config --global user.email "<EMAIL>"
#    - git remote set-url --<NAME_EMAIL>:$CI_PROJECT_PATH
#    - git checkout "release/RC-LIVE" && git pull
#    - PULL_RESULT=$(git pull origin master)
#    - |
#      if echo "$PULL_RESULT" | grep -q "Automatic merge failed"; then
#        echo "Contém conflitos!!";
#        curl -s --request POST --header "PRIVATE-TOKEN: $API_TOKEN" --data "source_branch=master&target_branch=release%2FRC-LIVE&title=AutoMerge%20(Master_RC-LIVE)" "$CI_API_V4_URL/projects/$CI_PROJECT_ID/merge_requests";
#        curl -s --request POST --header "Content-Type: application/json" --data '{"text": "*Conflitos no ZW!!*\n*Branch: release/RC-LIVE*\nGuardião da branch LIVE, apresente-se!?"}' "https://chat.googleapis.com/v1/spaces/AAAABogbUp0/messages?key=AIzaSyDdI0hCZtE6vySjMm-WEfRq3CPzqKqqsHI&token=_kJcnaZJ24lMdOk7czpPoB963XWbTAMX8WNU6MZUR6Y"
#        exit 1
#      else
#        echo $CI_COMMIT_REF_NAME;
#        echo "Não contém conflito";
#        GIT_SSH_COMMAND="ssh -o UserKnownHostsFile=/dev/null -o StrictHostKeyChecking=no" git push
#      fi

tag-version:
  image: registry.gitlab.com/plataformazw/tag-versions:master
  interruptible: true
  tags:
    - docker
  stage: finish
  when: on_success
  only:
    variables:
      - $RELEASE_TAG
  script:
    - eval $(ssh-agent -s)
    - echo "$SSH_PRIVATE_KEY" | tr -d '\r' | ssh-add -
    - git config --global user.name "Pipeline"
    - git config --global user.email "<EMAIL>"
    - git tag -l | xargs git tag -d
    - bumpversion  patch
    - git remote show origin
    - git remote set-url --<NAME_EMAIL>:$CI_PROJECT_PATH
    - git remote show origin
    - git tag -f latest -m "Deploy tag"
    - GIT_SSH_COMMAND="ssh -o UserKnownHostsFile=/dev/null -o StrictHostKeyChecking=no" git push origin HEAD:$CI_COMMIT_REF_NAME
    - GIT_SSH_COMMAND="ssh -o UserKnownHostsFile=/dev/null -o StrictHostKeyChecking=no" git push -f --tags
  retry:
    max: 2
    when: runner_system_failure
  cache: []
#
