stages:
  - test

.job_teste_template:
  stage: test
  resource_group: $CI_JOB_NAME
  interruptible: true
  tags:
    - locaweb
    - shell
    - large
  needs:
    - job: test-atualiza-db
      artifacts: true
  before_script:
    - export $(grep -v '^#' .env | xargs)
    - source .env
    - docker login -u "<EMAIL>" -p "$TKN_PIPE_TESTE_AUTO_READ" registry.gitlab.com
    - docker pull $IMAGE
    - export HOSTS_LINE="$HOST_DNS:$HOST_PIPELINE"
    - export RUN="docker run --add-host ${HOSTS_LINE} --rm --name=cypress-zw-$CI_PIPELINE_ID-$CI_JOB_NAME_SLUG "
  variables:
    CY_CONFIG_FILE: "./cypress.json"
    CYPRESS_RECORD_KEY: "35487b56-a039-49c5-8480-333a43ebb9d9"
  only:
    - master
  except:
    changes:
      - .bumpversion.cfg
  retry:
    max: 2
    when: runner_system_failure
  artifacts:
    paths:
      - .env

.test-atualiza-db: &job0
  extends: .job_teste_template
  artifacts:
    paths:
      - .env
  script:
    - $RUN -v $PWD:/cy -w /zw -e CYPRESS_BASE_URL=$ZW_URL -e CYPRESS_RECORD_KEY=$CYPRESS_RECORD_KEY -e CYPRESS_COMMIT_INFO_AUTHOR=$CI_COMMIT_AUTHOR -e COMMIT_INFO_MESSAGE="$CI_COMMIT_MESSAGE" -e COMMIT_INFO_BRANCH=$CI_COMMIT_REF_NAME  $IMAGE run --headless --browser chrome --group Logar --spec "cypress/integration/0-Login/0-AtualizarBD.spec.js" --config baseUrl=$ZW_URL --config-file=$CY_CONFIG_FILE --record --parallel --ci-build-id $CI_PIPELINE_ID

