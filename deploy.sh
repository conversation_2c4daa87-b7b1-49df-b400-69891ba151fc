#!/bin/bash
IP_HOST=${1:-$(docker node inspect self --format '{{ .Status.Addr  }}')}

ZW_PORT=$(shuf -i 8000-9000 -n 1)
sed -i -e "s/ZW_PORT=.*/ZW_PORT=$ZW_PORT/g" .env

STACK_NAME=zw-$ZW_PORT 
sed -i -e "s/STACK_NAME=.*/STACK_NAME=$STACK_NAME/g" .env 

ZW_URL=http://${IP_HOST}:$ZW_PORT/ZillyonWeb
sed -i -e "s~ZW_URL=.*~ZW_URL=$ZW_URL~g" .env 

docker stack deploy -c docker-compose.yml $STACK_NAME --with-registry-auth
dockerize -wait $ZW_URL --timeout 5m -wait-retry-interval 30s
curl --request POST $ZW_URL/prest/versao?chave=teste