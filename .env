HOST_PIPELINE=*********
HOST_DNS=pipeline.pactosolucoes.com.br
IMAGE="registry.gitlab.com/plataformazw/qa/teste-auto:master"
DOCKERIZE="registry.gitlab.com/plataformazw/docker-pacto/dockerize:master"
ZW_PORT=10935
TR_PORT=11485
NTR_PORT=11476
OAMD_PORT=11217
LOGIN_PORT=11835
DISCOVERY_PORT=11064
API_PORT=10418
AUT_PORT=10856
BI_MS_PORT=10996
CAD_MS_PORT=11771
DASH_API_PORT=11508
ADM_PORT=10742
ZW_AUTO_PORT=10230
VENDAS_PORT=11229
FULL_REPORT_PORT=10997
PLANO_MS_PORT=11012
PRODUTO_MS_PORT=10349
PACTO_PAY_PORT=10892
PERSONAGEM_PORT=11191
PG_PORT=10724
CLUBE_VANTAGENS_MS_PORT=11192
RELATORIO_MS_PORT=11193
DYNAMODB_PORT=11266
ADM_CORE_MS_PORT=11267
NOVO_LOGIN_PORT=11268
ADM_MS_PORT=11269
ZW_URL=http://teste-wall.pactosolucoes.com.br:10935/ZillyonWeb
URL_ZW_INTEGRACAO=http://teste-wall.pactosolucoes.com.br:10935/ZillyonWeb
LOGIN_URL=http://teste-wall.pactosolucoes.com.br:11835/LoginApp
TREINO_URL=http://teste-wall.pactosolucoes.com.br:11485/TreinoWeb
VENDAS_URL=http://teste-wall.pactosolucoes.com.br:11229
API_URL=http://teste-wall.pactosolucoes.com.br:10418/API-ZillyonWeb
ZW_AUTO_URL=http://teste-wall.pactosolucoes.com.br:10230/zw-auto
OAMD_URL=http://teste-wall.pactosolucoes.com.br:11217/NewOAMD
URL_NOVO_LOGIN=http://teste-wall.pactosolucoes.com.br:11268
STACK_NAME=teste-auto-10935
