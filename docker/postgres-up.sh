PORT=${1:-"5432"}
CONTAINER_NAME=${2:-"postgres"}
BACKUP_FOLDER_NAME=${3:-"teste"}

POSTGRES_IMAGE="registry.gitlab.com/plataformazw/docker-pacto/postgres:9.4"

if [[ $PORT == 'auto' ]]; then
   PORT=""
fi

if [[ $BACKUP_FOLDER_NAME == 'auto' ]]; then
   BACKUP_FOLDER_NAME="teste"
fi

if [[ $BACKUP_FOLDER_NAME == 'teste' ]]; then
   BACKUP_DOWNLOAD="false"
else
   BACKUP_DOWNLOAD="true"
fi

if [[ $(docker network list --format {{.Name}} | grep -w pacto) ]]; then
    echo 'Usando Network pacto existente'
else
    docker network create pacto
fi

if [[ $(docker ps -a --format '{{.Names}}' | grep -w $CONTAINER_NAME) ]]; then
    echo "Removendo container postgres atual: $CONTAINER_NAME"
    docker rm -f $CONTAINER_NAME
fi

IP_PUBLICO=$(curl https://ipinfo.io/ip) > /dev/null 2>&1

if [[ $IP_PUBLICO == '*************' ]]; then
    ADD_HOST="--add-host cloudbackup.pactosolucoes.com.br:************"
fi

echo "Cirando container postgres: $CONTAINER_NAME"
docker run \
--rm $ADD_HOST \
--name $CONTAINER_NAME \
--network pacto \
-p $PORT:5432 \
-d \
$POSTGRES_IMAGE

docker exec $CONTAINER_NAME bash -c '/db/bin/wait-for-it.sh localhost:5432'
docker exec $CONTAINER_NAME bash -c "/db/bin/init.sh $BACKUP_FOLDER_NAME $BACKUP_DOWNLOAD"

CONTAINER_HOST_LOCAL=$(docker port $CONTAINER_NAME 5432/tcp | sed 's/0.0.0.0/localhost/g')
CONTAINER_HOST_PUBLICO=$(docker port $CONTAINER_NAME 5432/tcp | sed "s/0.0.0.0/$IP_PUBLICO/g")
echo "Host local $CONTAINER_HOST_LOCAL"
echo "Host publico $CONTAINER_HOST_PUBLICO"
