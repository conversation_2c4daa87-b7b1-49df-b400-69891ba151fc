#!/bin/bash

. ./globals

aws ec2 run-instances --image-id $ami --count 1 --instance-type t3.medium --key-name ClusterPacto --security-group-ids $sg --subnet-id $subnet --associate-public-ip-address --tag-specifications "ResourceType=instance,Tags=[{Key=Name,Value=$nome}]" --user-data file://user_data $profileLB > ec2_output.json
ec2=$(cat ec2_output.json | grep InstanceId | cut -d ':' -f2 | cut -d '"' -f2 | cut -d ',' -f2)

while true;
do
  state=$(aws ec2 describe-instances --instance-ids=$ec2 $profileLB | grep PublicIpAddress | cut -d '"' -f4)
  echo $state "|" $ec2
  if [ "$state" != "" ];then
    break
  fi
done
exit 0

#arquivos a serem criados
#~/.aws/config

#[default]
#output = json
#region = us-east-1
#[profile ec2-virginia-pacto]
#region = us-east-1
#output = json

#~/.aws/credentials

#[default]
#aws_access_key_id = AK**************
#aws_secret_access_key = SIde2************
#[ec2-virginia-pacto]
#aws_access_key_id = AKIA***************************
#aws_secret_access_key = D50Y*****************************
