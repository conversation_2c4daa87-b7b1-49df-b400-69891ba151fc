# Este docker-compose ? utilizado para criar ambientes de testes em servidores ec2 na Amazon Web Services
version: '3'
networks:
  pacto:
    ipam:
      config:
        - subnet: *********/16
services: 
  zw:
    image: registry.gitlab.com/plataformazw/zw/tomcat:master
    ports:
      - 8081:8080
      - 9001:9000
    links:
      - postgres
    environment: 
      JAVA_OPTS: "-Xms3g -Xmx3g -Xss256k -Duser.timezone=America/Sao_Paulo -Duser.language=pt -Duser.region=BR"
      DISCOVERY_URL: http://*********:8080
    depends_on: 
      - postgres
    restart: always
    networks:
      pacto:
        ipv4_address: *********
  autenticacao:
    image: registry.gitlab.com/plataformazw/autenticacao:master
    ports:
      - 8086:8080
      - 9006:9000
    environment:
      DISCOVERY_URL: http://*********:8080
    restart: always
    networks:
      pacto:
        ipv4_address: *********
  discovery:
    image: registry.gitlab.com/plataformazw/discovery-urls:master
    ports:
      - 8087:8080
      - 9007:9000
    depends_on:
      - postgres
    restart: always
    networks:
      pacto:
        ipv4_address: *********
  aragorn:
    image: registry.gitlab.com/pactopay/aragorn:master
    ports:
      - 8090:8080
    depends_on:
      - postgres
    restart: always
    networks:
      pacto:
        ipv4_address: *********0
  postgres:
    container_name: postgres
    image: registry.gitlab.com/plataformazw/docker-pacto/postgres:9.4  
    ports:
      - "5432:5432"
    networks:
      pacto:
        ipv4_address: ***********
    environment:
      INIT_DB: teste
      RESTORE_DB: "false"
      IP_HOST: postgres
      URL_ZW: http://*********:8080/ZillyonWeb
      URL_TREINO: http://localhost:8080/TreinoWeb
      URL_OAMD: http://*********:8080/NewOAMD
      URL_ZW_API: http://*********:8080/api
      URL_LOGIN: http://*********:8080/LoginApp
      URL_AUTENTICACAO: http://*********:8080
      URL_ARAGORN: http://*********0:8080
      URL_NOVO_TREINO: http://*********
      URL_VENDAS_2: http://***********