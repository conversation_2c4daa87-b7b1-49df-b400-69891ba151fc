#!/bin/bash

ZW_IMAGE_TAG="master"

sudo apt -y update
sudo apt -y install  apt-transport-https  ca-certificates  curl  gnupg-agent  software-properties-common
sudo curl -fsSL https://download.docker.com/linux/ubuntu/gpg | sudo apt-key add -
sudo add-apt-repository "deb [arch=amd64] https://download.docker.com/linux/ubuntu bionic stable"
sudo -y apt update
sudo apt -y install docker-ce docker-ce-cli containerd.io docker-compose
sudo apt -y install gnupg2 pass

sudo docker login registry.gitlab.com -u pipeline -p ********************

sudo curl --request GET --header 'PRIVATE-TOKEN: B6sWQDXJo9pH3BTJxii6' 'https://gitlab.com/api/v4/projects/11991911/repository/files/docker%2Faws%2Fdocker-compose.yml/raw?ref=master' --output /root/docker-compose.yml
sudo sed -i -E 's/zw\/tomcat\:master/tomcat\:$ZW_IMAGE_TAG/g' docker-compose.yml
sudo docker-compose -f /root/docker-compose.yml up -d postgres
sleep 60
sudo docker-compose -f /root/docker-compose.yml up -d