
SUFIX_CONTAINER_NAME=$(date +"%Y-%m-%dT%T.%3N" | sed -E 's/\.|:/-/g')

POSTGRES_CONTAINER_NAME=${1:-"auto"}
ZW_CONTAINER_NAME=${2:-"auto"}
# TAG da imagem zw existente no container registry do repositorio do zw
ZW_TAG=${3:-"auto"}
# glassfish ou tomcat
ZW_WEBSERVER=${4:-"auto"}
# nome da empresa/nome da pasta no cloud backup.
#Exemplo: em  http://cloudbackup.pactosolucoes.com.br:8072/backups/academias/21fitness/ BACKUP_FOLDER_NAME=21fitness
BACKUP_FOLDER_NAME=${5:-"auto"}

if [[ $POSTGRES_CONTAINER_NAME == 'auto' ]]; then
   POSTGRES_CONTAINER_NAME="postgres-$SUFIX_CONTAINER_NAME"
fi

if [[ $ZW_CONTAINER_NAME == 'auto' ]]; then
   ZW_CONTAINER_NAME="zw-$SUFIX_CONTAINER_NAME"
fi

if [[ $ZW_WEBSERVER == 'auto' ]]; then
   ZW_WEBSERVER="glassfish"
fi

if [[ $BACKUP_FOLDER_NAME == 'auto' ]]; then
   BACKUP_FOLDER_NAME="teste"
fi

echo "POSTGRES_CONTAINER_NAME=$POSTGRES_CONTAINER_NAME"
./postgres-up.sh auto $POSTGRES_CONTAINER_NAME $BACKUP_FOLDER_NAME

echo "ZW_CONTAINER_NAME=$ZW_CONTAINER_NAME"
./zw-up.sh $ZW_TAG auto $ZW_CONTAINER_NAME $POSTGRES_CONTAINER_NAME $ZW_WEBSERVER

