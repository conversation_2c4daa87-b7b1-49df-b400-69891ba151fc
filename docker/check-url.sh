#!/usr/bin/env bash

set -o pipefail

URL=${1:-"http://localhost:8080/ZillyonWeb/UpdateServlet?op=getLastActionTime"}
TIMEOUT=${2:-15}

STATUS=$(curl -o /dev/null --silent --head --write-out '%{http_code}' "$URL" )
TIME=0

while [ $STATUS != "200" ] && [ $TIME < $TIMEOUT ]; do
     STATUS=$(curl -o /dev/null --silent --head --write-out '%{http_code}' "$URL" )
     sleep 2s
     echo "$URL response status: $STATUS"

     TIME=$((TIME + 2))
done

if [ $STATUS -eq "200" ]; then
    echo "The boy is standing!"
    exit 1
else
    echo "URL check timeout after ${TIMEOUT}s"
fi

