#!/bin/bash

RAIZ="$CATALINA_HOME/webapps/$CONTEXT/WEB-INF"
LIB="$RAIZ/lib"
CLASSES="$RAIZ/classes"
CLASSPATH="$LIB/*:$CLASSES:."

chave="teste"
xms="128m"
xmx="128m"
class="servicos.RobotRunner"

while getopts p:c:s:x:?: option
do
    case "$option" in
        p) 
            class="${OPTARG}"
            ;;
        c)
            chave="${OPTARG}"
            ;;
        s)
            xms="${OPTARG}"
            ;;
        x)
            xmx="${OPTARG}"
            ;;
        *|h)
            echo "Parametros: run.sh"
            echo "-p <path da classe>"
            echo "-c <chave da empresa>"
            echo "-s <java flag xms>"
            echo "-x <java flag xmx>"
            echo "Usage: robot.sh -p servicos.RobotRunner -c 9fe5efa91ec0625b18d8f4cf8eddf95f -s 128m -x 128m"
            exit 1
        ;;
    esac
done

java -Xms"$xms" -Xmx"$xmx" -Dfile.encoding=ISO-8859-1 \
    -Dsun.net.client.defaultConnectTimeout=5000 \
    -Dsun.net.client.defaultReadTimeout=30000 \
    -cp "$CLASSPATH" $class $chave \
    >> /var/log/robo/$class-$chave.log
