#!/bin/bash

RAIZ="$CATALINA_HOME/webapps/$CONTEXT/WEB-INF"
LIB="$RAIZ/lib"
CLASSES="$RAIZ/classes"
CLASSPATH="$LIB/*:$CLASSES:."

chave="teste"
xms="128m"
xmx="128m"

while getopts c:s:x:?: option
do
    case "$option" in
        c)
            chave="${OPTARG}"
        ;;
        s)
            xms="${OPTARG}"
        ;;
        x)
            xmx="${OPTARG}"
        ;;
        *|h)
            echo "Parametros:"
            echo "-c <chave da empresa>"
            echo "-s <java flag xms>"
            echo "-x <java flag xmx>"
            echo "Usage: dw.sh -c 9fe5efa91ec0625b18d8f4cf8eddf95f -s 128m -x 128m"
            exit 1
        ;;
    esac
done

run.sh -p servicos.DWRunner $@