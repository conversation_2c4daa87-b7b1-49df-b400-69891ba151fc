#!/bin/bash
set -e

echo "Setup environment..."

sed -i -E "s~DISCOVERY_URL=.*$~DISCOVERY_URL=$DISCOVERY_URL~g" \
    $APP_DIR/WEB-INF/classes/servicos/propriedades/SuperControle.properties
sed -i -E "s~URL_HTTPS_PLATAFORMA_PACTO=.*$~URL_HTTPS_PLATAFORMA_PACTO=$URL_HTTPS_PLATAFORMA_PACTO~g" \
    $APP_DIR/WEB-INF/classes/servicos/propriedades/SuperControle.properties
sed -i -E "s~URL_HTTP_PLATAFORMA_PACTO=.*$~URL_HTTP_PLATAFORMA_PACTO=$URL_HTTP_PLATAFORMA_PACTO~g" \
    $APP_DIR/WEB-INF/classes/servicos/propriedades/SuperControle.properties
sed -i -E "s~urlOamd=.*$~urlOamd=$URL_OAMD~g" \
    $APP_DIR/WEB-INF/classes/servicos/propriedades/SuperControle.properties
sed -i -E "s~urlOamdSegura=.*$~urlOamdSegura=$URL_OAMD_SEGURA~g" \
    $APP_DIR/WEB-INF/classes/servicos/propriedades/SuperControle.properties
sed -i -E "s~smtpEmailRobo=.*$~smtpEmailRobo=$SMTP_EMAIL_ROBO~g" \
    $APP_DIR/WEB-INF/classes/servicos/propriedades/SuperControle.properties
sed -i -E "s~smtpEmailNoReply=.*$~smtpEmailNoReply=$SMTP_EMAIL_NOREPLY~g" \
    $APP_DIR/WEB-INF/classes/servicos/propriedades/SuperControle.properties
sed -i -E "s~smtpLoginRobo=.*$~smtpLoginRobo=$SMTP_LOGIN_ROBO~g" \
    $APP_DIR/WEB-INF/classes/servicos/propriedades/SuperControle.properties
sed -i -E "s~smtpSenhaRobo=.*$~smtpSenhaRobo=$SMTP_SENHA_ROBO~g" \
    $APP_DIR/WEB-INF/classes/servicos/propriedades/SuperControle.properties
sed -i -E "s~smtpServerRobo=.*$~smtpServerRobo=$SMTP_SERVER_ROBO~g" \
    $APP_DIR/WEB-INF/classes/servicos/propriedades/SuperControle.properties
sed -i -E "s~iniciarTLS=.*$~iniciarTLS=$SMTP_SERVER_ROBO_INICIAR_TLS~g" \
    $APP_DIR/WEB-INF/classes/servicos/propriedades/SuperControle.properties
sed -i -E "s~smtpConexaoSeguraRobo=.*$~smtpConexaoSeguraRobo=$SMTP_SERVER_ROBO_CONEXAO_SEGURA~g" \
    $APP_DIR/WEB-INF/classes/servicos/propriedades/SuperControle.properties
sed -i -E "s~urlVendasOnline=.*$~urlVendasOnline=$URL_VENDAS_ONLINE~g" \
    $APP_DIR/WEB-INF/classes/servicos/propriedades/SuperControle.properties
sed -i -E "s~urlBaseOptIn=.*$~urlBaseOptIn=$URL_BASE_OPTIN~g" \
        $APP_DIR/WEB-INF/classes/servicos/propriedades/SuperControle.properties
sed -i -E "s~myUpUrlBase.*=.*~myUpUrlBase=$MY_URL_UP_BASE~g" \
    $APP_DIR/WEB-INF/classes/servicos/propriedades/SuperControle.properties
sed -i -E "s~urlJenkins.*=.*~urlJenkins=$URL_JENKINS~g" \
    $APP_DIR/WEB-INF/classes/servicos/propriedades/SuperControle.properties
sed -i -E "s~urlMailing.*=.*~urlMailing=$URL_MAILING~g" \
    $APP_DIR/WEB-INF/classes/servicos/propriedades/SuperControle.properties
sed -i -E "s~biMs.*=.*~biMs=$BI_MS~g" \
    $APP_DIR/WEB-INF/classes/servicos/propriedades/SuperControle.properties
sed -i -E "s~urlAplicacao.*=.*~urlAplicacao=$URL_APLICACAO~g" \
    $APP_DIR/WEB-INF/classes/servicos/propriedades/SuperControle.properties
sed -i -E "s~urlNotificar.*=.*~urlNotificar=$URL_NOTIFICAR~g" \
    $APP_DIR/WEB-INF/classes/servicos/propriedades/SuperControle.properties
sed -i -E "s~urlZWAUTO.*=.*~urlZWAUTO=$URL_ZW_AUTO~g" \
    $APP_DIR/WEB-INF/classes/servicos/propriedades/SuperControle.properties
sed -i -E "s~urlAPI.*=.*~urlAPI=$URL_API~g" \
    $APP_DIR/WEB-INF/classes/servicos/propriedades/SuperControle.properties
sed -i -E "s~biMs.*=.*~biMs=$URL_BI_MS~g" \
    $APP_DIR/WEB-INF/classes/servicos/propriedades/SuperControle.properties
sed -i -E "s~debugJDBC.*=.*~debugJDBC=$DEBUG_JDBC~g" \
    $APP_DIR/WEB-INF/classes/servicos/propriedades/SuperControle.properties
sed -i -E "s~urlWebHookDiscordEnotasHabilitar.*=.*~urlWebHookDiscordEnotasHabilitar=$URL_WEBHOOK_DISCORD_ENOTAS_HABILITAR~g" \
    $APP_DIR/WEB-INF/classes/servicos/propriedades/SuperControle.properties
sed -i -E "s~urlServicoNotaFiscal.*=.*~urlServicoNotaFiscal=$URL_SERV_NOTA_FISCAL~g" \
    $APP_DIR/WEB-INF/classes/servicos/propriedades/SuperControle.properties
sed -i -E "s~ambienteDesenvolvimentoTeste.*=.*~ambienteDesenvolvimentoTeste=$AMBIENTE_DESENVOLVIMENTO_TESTE~g" \
    $APP_DIR/WEB-INF/classes/servicos/propriedades/SuperControle.properties
sed -i -E "s~enableMenuZwUI.*=.*~enableMenuZwUI=$ENABLE_MENU_ZW_UI~g" \
    $APP_DIR/WEB-INF/classes/servicos/propriedades/SuperControle.properties
sed -i -E "s~urlLoginHomologacao.*=.*~urlLoginHomologacao=$URL_LOGIN_HOMOLOGACAO~g" \
    $APP_DIR/WEB-INF/classes/servicos/propriedades/SuperControle.properties
sed -i -E "s~urlLogin.*=.*~urlLogin=$URL_LOGIN~g" \
    $APP_DIR/WEB-INF/classes/servicos/propriedades/SuperControle.properties
sed -i -E "s~<url-oamd>.*~<url-oamd>$URL_DATABASE_OAMD</url-oamd>~g" \
    $APP_DIR/WEB-INF/classes/negocio/facade/jdbc/utilitarias/cfgBD.xml
sed -i -E "s~urlRecursoEmpresa.*=.*$~urlRecursoEmpresa=$URL_RECURSO_EMPRESA~g" \
    $APP_DIR/WEB-INF/classes/servicos/propriedades/SuperControle.properties
sed -i -E "s~usarUrlRecursoEmpresa.*=.*$~usarUrlRecursoEmpresa=$USAR_URL_RECURSO_EMPRESA~g" \
    $APP_DIR/WEB-INF/classes/servicos/propriedades/SuperControle.properties
sed -i -E "s~permitirEnvioNotificacaoPushAula=.*$~permitirEnvioNotificacaoPushAula=$PERMITIR_ENVIO_NOTIFICACAO_PUSH_AULA~g" \
    $APP_DIR/WEB-INF/classes/servicos/propriedades/SuperControle.properties
sed -i -E "s~tokenMailgun.*=.*~tokenMailgun=$TOKEN_MAIL_GUN~g" \
    $APP_DIR/WEB-INF/classes/servicos/propriedades/SuperControle.properties
sed -i -E "s~habilitaMarketing.*=.*~habilitaMarketing=$HABILITA_MARKETING~g" \
        $APP_DIR/WEB-INF/classes/servicos/propriedades/SuperControle.properties
sed -i -E "s~habilitaClubeDeBeneficios.*=.*~habilitaClubeDeBeneficios=$HABILITA_CLUBE_DE_BENEFICIOS~g" \
        $APP_DIR/WEB-INF/classes/servicos/propriedades/SuperControle.properties
sed -i -E "s~tempoSegundosExpirarCacheBanners.*=.*~tempoSegundosExpirarCacheBanners=$TEMPO_SEGUNDOS_EXPIRAR_CACHE_BANNERS~g" \
        $APP_DIR/WEB-INF/classes/servicos/propriedades/SuperControle.properties
sed -i -E "s~servidorMemCached.*=.*~servidorMemCached=$SERVIDOR_MEMCACHED~g" \
        $APP_DIR/WEB-INF/classes/servicos/propriedades/SuperControle.properties
sed -i -E "s~urlMarketingMs.*=.*~urlMarketingMs=$URL_MARKETING_MS~g" \
        $APP_DIR/WEB-INF/classes/servicos/propriedades/SuperControle.properties
sed -i -E "s~domainMail.*=.*~domainMail=$DOMAIN_MAIL~g" \
    $APP_DIR/WEB-INF/classes/servicos/propriedades/SuperControle.properties
sed -i -E "s~validarVersaoBD.*=.*~validarVersaoBD=$VALIDAR_VERSAO_BD~g" \
    $APP_DIR/WEB-INF/classes/servicos/propriedades/SuperControle.properties
sed -i -E "s~uteisEmailSend.*=.*~uteisEmailSend=$UTEIS_EMAIL_SEND~g" \
    $APP_DIR/WEB-INF/classes/servicos/propriedades/SuperControle.properties
sed -i -E "s~urlNotificacaoAcesso.*=.*~urlNotificacaoAcesso=$ZAW_URL_NOTF_ACESSO~g" \
    $APP_DIR/WEB-INF/classes/servicos/propriedades/SuperControle.properties
sed -i -E "s~urlFotosNuvem.*=.*~urlFotosNuvem=$URL_FOTOS_NUVEM~g" \
    $APP_DIR/WEB-INF/classes/servicos/propriedades/SuperControle.properties
sed -i -E "s~typeMidiasService.*=.*~typeMidiasService=$TIPO_MIDIA~g" \
    $APP_DIR/WEB-INF/classes/servicos/propriedades/SuperControle.properties
sed -i -E "s~cancelarBoletoAoCancelarOuEstonarContrato.*=.*~cancelarBoletoAoCancelarOuEstonarContrato=$CANCELAR_BOLETO_AO_CANCELAR_OU_ESTORNAR_CONTRATO~g" \
    $APP_DIR/WEB-INF/classes/servicos/propriedades/SuperControle.properties
sed -i -E "s~urlMidiaSocial=.*$~urlMidiaSocial=$URL_MIDIA_SOCIAL~g" \
    $APP_DIR/WEB-INF/classes/servicos/propriedades/SuperControle.properties
sed -i -E "s~tempoSegundosExpiracaoTokenOperacao=.*$~tempoSegundosExpiracaoTokenOperacao=$TEMPO_SEGUNDOS_EXPIRACAO_TOKEN_OPERACAO~g" \
    $APP_DIR/WEB-INF/classes/servicos/propriedades/SuperControle.properties
sed -i -E "s~habilitarNicho.*=.*~habilitarNicho=$HABILITAR_NICHO~g" \
    $APP_DIR/WEB-INF/classes/servicos/propriedades/SuperControle.properties
sed -i -E "s~habilitarCacheInitNicho.*=.*~habilitarCacheInitNicho=$HABILITAR_CACHE_INIT_NICHO~g" \
    $APP_DIR/WEB-INF/classes/servicos/propriedades/SuperControle.properties
sed -i -E "s~validadeCacheNichoEmMinutos.*=.*~validadeCacheNichoEmMinutos=$VALIDADE_CACHE_NICHO_EM_MINUTOS~g" \
    $APP_DIR/WEB-INF/classes/servicos/propriedades/SuperControle.properties
sed -i -E "s~verifyControllersAfterPhase.*=.*~verifyControllersAfterPhase=$VERIFY_CONTROLLERS_AFTER_PHASE~g" \
    $APP_DIR/WEB-INF/classes/servicos/propriedades/SuperControle.properties
sed -i -E "s~habilitarFuncionalidadesBeta.*=.*~habilitarFuncionalidadesBeta=$HABILITAR_FUNCIONALIDADES_BETA~g" \
    $APP_DIR/WEB-INF/classes/servicos/propriedades/SuperControle.properties
sed -i -E "s~buscarConhecimentoUCP.*=.*~buscarConhecimentoUCP=$BUSCAR_CONHECIMENTO_UCP~g" \
    $APP_DIR/WEB-INF/classes/servicos/propriedades/SuperControle.properties
sed -i -E "s~maxGpt.*=.*~maxGpt=$MAX_GPT~g" \
    $APP_DIR/WEB-INF/classes/servicos/propriedades/SuperControle.properties
sed -i -E "s~redirectUriConnectPagBank=.*$~redirectUriConnectPagBank=$REDIRECT_URI_CONNECT_PAGBANK~g" \
    $APP_DIR/WEB-INF/classes/servicos/propriedades/SuperControle.properties
sed -i -E "s~validarTokenApiZW=.*$~validarTokenApiZW=$VALIDAR_TOKEN_API_ZW~g" \
    $APP_DIR/WEB-INF/classes/servicos/propriedades/SuperControle.properties
sed -i -E "s~MOCK_ARAGORN_CARDS=.*$~MOCK_ARAGORN_CARDS=$MOCK_ARAGORN_CARDS~g" \
    $APP_DIR/WEB-INF/classes/servicos/propriedades/SuperControle.properties

cat $APP_DIR/WEB-INF/classes/servicos/propriedades/SuperControle.properties

cat $APP_DIR/WEB-INF/classes/negocio/facade/jdbc/utilitarias/cfgBD.xml
