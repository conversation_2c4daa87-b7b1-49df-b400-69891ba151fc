#!/bin/bash
set -Eeo pipefail

asyncRun() {
    "$@" &
    pid="$!"
    trap "echo 'Stopping PID $pid'; kill -SIGTERM $pid" SIGINT SIGTERM

    # finalizar se for pressionado ctrl + c
    while kill -0 $pid > /dev/null 2>&1; do
        wait
    done
}

setup-env.sh
/etc/init.d/cron start &
if [ "$ENABLE_OTEL_JAVA_AGENT" = 'true' ]; then

    OTEL_JAVA_AGENT_OPTION="$OTEL_JAVA_AGENT_OPTION -javaagent:/otel/opentelemetry-javaagent.jar"
    OTEL_JAVA_AGENT_OPTION="$OTEL_JAVA_AGENT_OPTION -Dotel.exporter.otlp.endpoint=${OTEL_EXPORTER_OTLP_ENDPOINT}"
    OTEL_JAVA_AGENT_OPTION="$OTEL_JAVA_AGENT_OPTION -Dotel.resource.attributes=service.name=${OTEL_SERVICE_NAME}"

    echo "Enabling OpenTelemetry Java Agent: $OTEL_JAVA_AGENT_OPTION"
    export JAVA_OPTS="$JAVA_OPTS $OTEL_JAVA_AGENT_OPTION"
    echo "JAVA_OPTS=$JAVA_OPTS"
fi

# Se a variável de nome não for passada, usa um default
if [ -z "$NEW_RELIC_APP_NAME" ]; then
  echo "[New Relic] NEW_RELIC_APP_NAME não setada. Então não vou ativar o NR no container."
else
  echo "[New Relic] Aplicando NEW_RELIC_APP_NAME=$NEW_RELIC_APP_NAME"
  # Edita o app_name dentro do bloco common (usado de verdade)
  sed -i '/^common:/,/^[^[:space:]]/ s/^.*app_name:.*$/  app_name: "'"$NEW_RELIC_APP_NAME"'"/' /usr/local/newrelic/newrelic.yml
  # Edita enable_auto_app_naming no mesmo bloco
  sed -i '/^common:/,/^[^[:space:]]/ s/^.*enable_auto_app_naming:.*$/  enable_auto_app_naming: false/' /usr/local/newrelic/newrelic.yml


  echo "### Enabling NewRelic Java Agent: $NEW_RELIC_APP_NAME"
  export JAVA_OPTS="$JAVA_OPTS -javaagent:/usr/local/newrelic/newrelic.jar"
  echo "JAVA_OPTS=$JAVA_OPTS"
fi

asyncRun catalina.sh jpda run "$@"

