apiVersion: extensions/v1beta1
kind: Deployment
metadata:
  annotations:
    kompose.cmd: kompose convert
    kompose.version: 1.19.0 (f63a961c)
  creationTimestamp: null
  labels:
    io.kompose.service: zw
  name: zw
spec:
  replicas: 1
  strategy: {}
  template:
    metadata:
      annotations:
        kompose.cmd: kompose convert
        kompose.version: 1.19.0 (f63a961c)
      creationTimestamp: null
      labels:
        io.kompose.service: zw
    spec:
      imagePullSecrets:
        - name: gitlabregistry
      containers:
      - image: registry.gitlab.com/plataformazw/zw/tomcat:master
        name: zw
        ports:
        - containerPort: 8080
        resources: {}
      restartPolicy: Always
status: {}
