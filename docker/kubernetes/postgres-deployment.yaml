apiVersion: extensions/v1beta1
kind: Deployment
metadata:
  annotations:
    kompose.cmd: kompose convert
    kompose.version: 1.19.0 (f63a961c)
  creationTimestamp: null
  labels:
    io.kompose.service: postgres
  name: postgres
spec:
  replicas: 1
  strategy: {}
  template:
    metadata:
      annotations:
        kompose.cmd: kompose convert
        kompose.version: 1.19.0 (f63a961c)
      creationTimestamp: null
      labels:
        io.kompose.service: postgres
    spec:
      imagePullSecrets:
        - name: gitlabregistry
      containers:
      - env:
        - name: INIT_DB
          value: teste
        - name: IP_HOST
          value: postgres
        image: registry.gitlab.com/plataformazw/docker-pacto/postgres:9.4
        name: postgres
        ports:
        - containerPort: 5432
        resources: {}
      restartPolicy: Always
status: {}
