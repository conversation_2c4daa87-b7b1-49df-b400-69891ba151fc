TAG=${1:-"auto"}
PORT=${2:-"auto"}
CONTAINER_NAME=${3:-"auto"}
POSTGRES_CONTAINER_NAME=${4:-"auto"}
WEBSERVER=${5:-"auto"}

if [[ $TAG == 'auto' ]]; then
   TAG=$(git symbolic-ref --short HEAD | sed 's/\(\/\|#\)/-/g')
fi

if [[ $PORT == 'auto' ]]; then
   PORT=""
fi

if [[ $CONTAINER_NAME == 'auto' ]]; then
   CONTAINER_NAME="zw"
fi

if [[ $POSTGRES_CONTAINER_NAME == 'auto' ]]; then
   POSTGRES_CONTAINER_NAME="postgres"
fi

if [[ $WEBSERVER == 'auto' ]]; then
   WEBSERVER="glassfish"
fi

if [[ $WEBSERVER == "glassfish" ]]; then
    ZW_IMAGE="registry.gitlab.com/plataformazw/zw/glassfish:$TAG"
else
    ZW_IMAGE="registry.gitlab.com/plataformazw/zw:$TAG"
fi

if [[ $(docker network list --format {{.Name}} | grep  -w pacto) ]]; then
    echo 'Usando Network pacto existente'
else
    docker network create pacto
fi

if [[ $(docker ps -a --format '{{.Names}}' | grep -w $CONTAINER_NAME) ]]; then
    echo "Removendo container $CONTAINER_NAME atual"
    docker rm -f $CONTAINER_NAME
fi

echo "Cirando container $CONTAINER_NAME"

IP_CONTAINER_POSTGRES=$(docker inspect -f '{{range .NetworkSettings.Networks}}{{.IPAddress}}{{end}}' $POSTGRES_CONTAINER_NAME)
docker run \
--add-host postgres:${IP_CONTAINER_POSTGRES} \
-d \
--name $CONTAINER_NAME \
--network pacto \
--link $POSTGRES_CONTAINER_NAME \
-p $PORT:8080 \
$ZW_IMAGE

IP_PUBLICO=$(curl https://ipinfo.io/ip) > /dev/null 2>&1
CONTAINER_HOST_LOCAL=$(docker port $CONTAINER_NAME 8080/tcp | sed 's/0.0.0.0/localhost/g')
CONTAINER_HOST_PUBLICO=$(docker port $CONTAINER_NAME 8080/tcp | sed "s/0.0.0.0/$IP_PUBLICO/g" | sed 's/*************/********/g')
echo "ZW URL local http://$CONTAINER_HOST_LOCAL/ZillyonWeb"
echo "ZW URL public http://$CONTAINER_HOST_PUBLICO/ZillyonWeb"
