FROM glassfish:4.1-jdk8

ADD ZillyonWeb /webapps/ZillyonWeb

ENV DB_HOST=${DB_HOST:-"postgres"}
ENV JAVA_OPTS=${JAVA_OPTS:-"-Xms750m:-Xmx750m:-XX\:+UseG1GC:-Duser.timezone=America/Sao_Paulo:-Duser.language=pt:-Duser.region=BR"}
ENV GLASSFISH_ADMIN_PASSWORD=${GLASSFISH_ADMIN_PASSWORD:-"admin"}
ENV DEBUG=${DEBUG:-"false"}

RUN mkdir /opt/robo
RUN mkdir /var/log/robo
ADD crontab/robo/*.sh /opt/robo/
RUN chmod +x /opt/robo/*.sh
RUN ln -s /opt/robo/*.sh /usr/local/bin
ADD crontab/crontab /etc/crontab
ADD keys/* /keys/

# Refatorar para o discovery
ENV URL_HTTPS_PLATAFORMA_PACTO={URL_HTTPS_PLATAFORMA_PACTO:-"https://dev.pactosolucoes.com.br:8094"}
ENV URL_HTTP_PLATAFORMA_PACTO={URL_HTTP_PLATAFORMA_PACTO:-"http://dev.pactosolucoes.com.br:8094"}
ENV MY_URL_UP_BASE=${MY_URL_UP_BASE:-"https://app.pactosolucoes.com.br/ucp"}

EXPOSE 8080
EXPOSE 4848
ADD entrypoint.sh /usr/local/bin/entrypoint
RUN chmod +x /usr/local/bin/entrypoint
CMD ["entrypoint"]