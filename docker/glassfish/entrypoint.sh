#!/bin/bash
set -Eeo pipefail

echo "DB_HOST=$DB_HOST"
echo "JAVA_OPTS=$JAVA_OPTS"
echo "DEBUG=$DEBUG"

asadmin start-domain

if [ $(asadmin list-applications --type web | grep <PERSON>illyon<PERSON>eb | wc -l) -eq "0" ]; then

    echo "=======> Config glassfish"
    asadmin delete-jvm-options "-XX\:MaxPermSize=192m:\-client:-Xmx512m"
    asadmin create-jvm-options "$JAVA_OPTS"
    asadmin set server.network-config.protocols.protocol.http-listener-1.http.request-timeout-seconds=-1
    asadmin set server.network-config.protocols.protocol.http-listener-2.http.request-timeout-seconds=-1
    asadmin set server.thread-pools.thread-pool.http-thread-pool.max-thread-pool-size=200

    echo "=======> Deploy ZillyonWeb"
    asadmin -u admin deploy /webapps/ZillyonWeb
fi

echo "=======> Restarting glassfish"
asadmin stop-domain


asyncRun() {
    "$@" &
    pid="$!"
    trap "echo 'Stopping PID $pid'; kill -SIGTERM $pid" SIGINT SIGTERM

    # finalizar se for pressionado ctrl + c
    while kill -0 $pid > /dev/null 2>&1; do
        wait
    done
}

if [ "$DEBUG" = true ]; then
  DEBUG_PARAM="--debug"
fi

echo "AS_ADMIN_NEWPASSWORD=$GLASSFISH_ADMIN_PASSWORD" > password.txt
asyncRun /usr/local/glassfish4/bin/asadmin start-domain $DEBUG_PARAM --passwordfile password.txt --verbose
