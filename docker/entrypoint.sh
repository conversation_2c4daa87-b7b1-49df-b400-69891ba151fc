#!/bin/bash
set -Eeo pipefail

echo "DB_HOST=$DB_HOST"

sed -i "s/<servidor>postgres<\/servidor>/<servidor>${DB_HOST}<\/servidor>/g"  \
	/usr/local/tomcat/webapps/ZillyonWeb/WEB-INF/classes/negocio/facade/jdbc/utilitarias/cfgBD.xml
 
sed -i "s/\/\/postgres:/\/\/${DB_HOST}:/g"  \
        /usr/local/tomcat/webapps/ZillyonWeb/WEB-INF/classes/negocio/facade/jdbc/utilitarias/cfgBD.xml

asyncRun() {
    "$@" &
    pid="$!"
    trap "echo 'Stopping PID $pid'; kill -SIGTERM $pid" SIGINT SIGTERM

    # finalizar se for pressionado ctrl + c
    while kill -0 $pid > /dev/null 2>&1; do
        wait
    done
}

asyncRun catalina.sh jpda run $@
