#!/bin/bash
set -e

BRANCH=${1:-"auto"}
WEBSERVER=${2:-"tomcat"}
ARTIFACT_NAME=${3:-"ZillyonWeb"}

current_branch(){
    echo $(git branch | sed -n -e 's/^\* \(.*\)/\1/p' | sed -e 's/\//-/' | sed -e 's/#/-/') | awk '{print tolower($0)}'
}

if [[ $BRANCH == 'auto' ]]; then
    BRANCH=$(current_branch)
fi

if [[ $WEBSERVER = 'glassfish' ]]; then
  MAVEN_PROFILES="glassfish-local,docker"
  docker pull registry.gitlab.com/plataformazw/docker-pacto/glassfish:4
elif [[ $WEBSERVER = 'tomcat' ]]; then
   MAVEN_PROFILES="docker"
   docker pull registry.gitlab.com/plataformazw/docker-pacto/tomcat:8
else
    echo "Invalid WEBSERVER paramter"
    echo "Valid params: tomcat glassfish"
    exit 1
fi

IMAGE_NAME="registry.gitlab.com/plataformazw/zw/${WEBSERVER}:${BRANCH}"

cp -R keys $WEBSERVER
cp -R crontab $WEBSERVER
cd ../
sudo su - ${USER} -c "docker run \
    --rm \
    -v "$(pwd)":/usr/src/zw \
    -v "$(pwd)/.m2/repository":/root/.m2/repository \
    -w /usr/src/zw \
    registry.gitlab.com/plataformazw/docker-pacto/maven:3-jdk-8 mvn -T 1C -P $MAVEN_PROFILES clean package"

if [ ! -d "target/$ARTIFACT_NAME" ]; then
    echo "Docker build  error"
    exit 1
fi

rm -rf "$(pwd)/.m2/repository/br/com/pacto/ZillyonWeb"

cp -R target/$ARTIFACT_NAME docker/${WEBSERVER}
cd docker/${WEBSERVER}
docker build --cache-from $IMAGE_NAME --tag $IMAGE_NAME .
docker push $IMAGE_NAME

echo "Docker build  success"
exit 0
