{
    // See https://go.microsoft.com/fwlink/?LinkId=733558
    // for the documentation about the tasks.json format
    "version": "2.0.0",
    "tasks": [
        {
            "label": "package",
            "type": "shell",
            "command": "mvn -P desenv,generate-ws,docker package",
            "group": "build"
        },
        {
            "label": "compile",
            "type": "shell",
            "command": "mvn -B -P desenv,generate-ws,docker compile",
            "group": "build"
        }
    ]
}