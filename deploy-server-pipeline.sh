#!/bin/bash

export $(grep -v '^#' .env | xargs)
cat .env

echo "Removing old stacks..."
docker stack rm $(docker stack ls | grep -e "pipe-zw" | awk '{print $1}')
sleep 40s

docker container prune --force
docker volume prune --force

echo
docker pull registry.gitlab.com/plataformazw/docker-pacto/postgres:9.4
docker pull registry.gitlab.com/plataformazw/treino/tomcat:master
docker pull registry.gitlab.com/plataformazw/zw/tomcat:master
docker pull registry.gitlab.com/plataformazw/zw_ui/login:master:master

echo "Stopping old cypress-zw containers running..."
docker ps --format='{{.Names}}' |grep cypress-zw |awk '{print $1}' |xargs docker stop
ssh -o StrictHostKeyChecking=no ${USER}@z2-srv02 "docker ps --format='{{.Names}}' |grep cypress-zw |awk '{print \$1}' |xargs docker stop && docker pull registry.gitlab.com/plataformazw/treino/tomcat:master && docker pull registry.gitlab.com/plataformazw/zw/tomcat:master && docker pull registry.gitlab.com/plataformazw/docker-pacto/postgres:9.4"
ssh -o StrictHostKeyChecking=no ${USER}@z1-srv-p "docker ps --format='{{.Names}}' |grep cypress-zw |awk '{print \$1}' |xargs docker stop && docker pull registry.gitlab.com/plataformazw/treino/tomcat:master && docker pull registry.gitlab.com/plataformazw/zw/tomcat:master && docker pull registry.gitlab.com/plataformazw/docker-pacto/postgres:9.4"
ssh -o StrictHostKeyChecking=no ${USER}@z2-srv-p "docker ps --format='{{.Names}}' |grep cypress-zw |awk '{print \$1}' |xargs docker stop && docker pull registry.gitlab.com/plataformazw/treino/tomcat:master && docker pull registry.gitlab.com/plataformazw/zw/tomcat:master && docker pull registry.gitlab.com/plataformazw/docker-pacto/postgres:9.4"

sleep 40s

echo
docker stack deploy -c docker-compose-prepare.yml ${STACK_NAME} --with-registry-auth

echo
echo "Waiting services up..."

docker pull $DOCKERIZE

docker run --add-host "$HOST_DNS":"$HOST_PIPELINE" $DOCKERIZE -wait $ZW_URL --timeout 5m -wait-retry-interval 30s
docker run --add-host "$HOST_DNS":"$HOST_PIPELINE" $DOCKERIZE -wait $TREINO_URL/prest/config/reload --timeout 20m -wait-retry-interval 30s

set -e

echo
echo "Waiting 1 minute for postgres..."
sleep 60s


echo
curl --request POST $TREINO_URL/prest/config/teste/removeFactory
echo
curl --request POST $TREINO_URL/prest/config/reload
echo
curl --request POST $TREINO_URL/prest/config/teste/updateBD
echo
curl --request POST $ZW_URL/prest/versao?chave=teste

echo
curl -G "$ZW_URL/UpdateServlet?op=refreshConfig&key=teste"

echo
##############################################################
