.test-atualiza-db: &job0
  extends: .job_teste_template
  artifacts:
    paths:
      - .env
  script:    
    - $RUN -v $PWD:/cy -w /zw -e CYPRESS_BASE_URL=$ZW_URL -e CYPRESS_RECORD_KEY=$CYPRESS_RECORD_KEY -e COMMIT_INFO_MESSAGE="$CI_COMMIT_MESSAGE" -e COMMIT_INFO_BRANCH=$CI_COMMIT_REF_NAME  $IMAGE run --headless --browser chrome --group Logar --spec "cypress/integration/0-Login/0-AtualizarBD.spec.js" --config baseUrl=$ZW_URL --config-file=$CY_CONFIG_FILE --record --parallel --ci-build-id $CI_PIPELINE_ID

#.test-none: &job9999
#  extends: .job_teste_template
#  artifacts:
#    paths:
#      - .env
#  script:
#    - echo "==============TESTS DISABLED!!!!=============="
#    - $RUN -v $PWD:/cy -w /zw -e CYPRESS_BASE_URL=$ZW_URL -e CYPRESS_RECORD_KEY=$CYPRESS_RECORD_KEY -e COMMIT_INFO_MESSAGE="$CI_COMMIT_MESSAGE" -e COMMIT_INFO_BRANCH=$CI_COMMIT_REF_NAME $IMAGE run --headless --browser chrome --group Logar --spec "cypress/integration/0-Login/0-AtualizarBD.spec.js" --config baseUrl=$ZW_URL --config-file=$CY_CONFIG_FILE --record --parallel --ci-build-id $CI_PIPELINE_ID

.test-zw-adm: &job1
  extends: .job_teste_template
  artifacts:
    paths:
      - .env
  script:
    - $RUN -v $PWD:/cy -w /zw -e CYPRESS_BASE_URL=$ZW_URL -e CYPRESS_RECORD_KEY=$CYPRESS_RECORD_KEY -e COMMIT_INFO_MESSAGE="$CI_COMMIT_MESSAGE" -e COMMIT_INFO_BRANCH=$CI_COMMIT_REF_NAME  $IMAGE run --headless --browser chrome --group ADM --spec "cypress/integration/*-TelaPrincipal/**/*.spec.js" --config baseUrl=$ZW_URL --config-file=$CY_CONFIG_FILE --record --parallel --ci-build-id $CI_PIPELINE_ID

.test-crm-metas: &job2
  extends: .job_teste_template
  artifacts:
    paths:
      - .env
  script:
    - $RUN -v $PWD:/cy -w /zw -e CYPRESS_BASE_URL=$ZW_URL -e CYPRESS_RECORD_KEY=$CYPRESS_RECORD_KEY -e COMMIT_INFO_MESSAGE="$CI_COMMIT_MESSAGE" -e COMMIT_INFO_BRANCH=$CI_COMMIT_REF_NAME  $IMAGE run --headless --browser chrome --group CRM-METAS --spec "cypress/integration/*-CRM-METAS/**/*.spec.js" --config baseUrl=$ZW_URL --config-file=$CY_CONFIG_FILE --record --parallel --ci-build-id $CI_PIPELINE_ID

.test-cad: &job3
  extends: .job_teste_template
  artifacts:
    paths:
      - .env
  script:
    - $RUN -v $PWD:/cy -w /zw -e CYPRESS_BASE_URL=$ZW_URL -e CYPRESS_RECORD_KEY=$CYPRESS_RECORD_KEY -e COMMIT_INFO_MESSAGE="$CI_COMMIT_MESSAGE" -e COMMIT_INFO_BRANCH=$CI_COMMIT_REF_NAME  $IMAGE run --headless --browser chrome --group CADASTROS --spec "cypress/integration/*-Cadastros/**/*.spec.js" --config baseUrl=$ZW_URL --config-file=$CY_CONFIG_FILE --record --parallel --ci-build-id $CI_PIPELINE_ID

.test-bi: &job4
  extends: .job_teste_template
  artifacts:
    paths:
      - .env
  script:
    - $RUN -v $PWD:/cy -w /zw -e CYPRESS_BASE_URL=$ZW_URL -e CYPRESS_RECORD_KEY=$CYPRESS_RECORD_KEY -e COMMIT_INFO_MESSAGE="$CI_COMMIT_MESSAGE" -e COMMIT_INFO_BRANCH=$CI_COMMIT_REF_NAME  $IMAGE run --headless --browser chrome --group BI --spec "cypress/integration/*-Business/**/*.spec.js" --config baseUrl=$ZW_URL --config-file=$CY_CONFIG_FILE --record --parallel --ci-build-id $CI_PIPELINE_ID

.test-crm: &job5
  extends: .job_teste_template
  artifacts:
    paths:
      - .env
  script:
    - $RUN -v $PWD:/cy -w /zw -e CYPRESS_BASE_URL=$ZW_URL -e CYPRESS_RECORD_KEY=$CYPRESS_RECORD_KEY -e COMMIT_INFO_MESSAGE="$CI_COMMIT_MESSAGE" -e COMMIT_INFO_BRANCH=$CI_COMMIT_REF_NAME  $IMAGE run --headless --browser chrome --group CRM --spec "cypress/integration/*-CRM/**/*.spec.js" --config baseUrl=$ZW_URL --config-file=$CY_CONFIG_FILE --record --parallel --ci-build-id $CI_PIPELINE_ID

.test-layout: &job6
  extends: .job_teste_template
  artifacts:
    paths:
      - .env
  script:
    - $RUN -v $PWD:/cy -w /zw -e CYPRESS_BASE_URL=$ZW_URL -e CYPRESS_RECORD_KEY=$CYPRESS_RECORD_KEY -e COMMIT_INFO_MESSAGE="$CI_COMMIT_MESSAGE" -e COMMIT_INFO_BRANCH=$CI_COMMIT_REF_NAME  $IMAGE run --headless --browser chrome --group MENU --spec "cypress/integration/*-Layout/**/*.spec.js" --config baseUrl=$ZW_URL --config-file=$CY_CONFIG_FILE --record --parallel --ci-build-id $CI_PIPELINE_ID

.test-finan: &job7
  extends: .job_teste_template
  artifacts:
    paths:
      - .env
  script:
    - $RUN -v $PWD:/cy -w /zw -e CYPRESS_BASE_URL=$ZW_URL -e CYPRESS_RECORD_KEY=$CYPRESS_RECORD_KEY -e COMMIT_INFO_MESSAGE="$CI_COMMIT_MESSAGE" -e COMMIT_INFO_BRANCH=$CI_COMMIT_REF_NAME  $IMAGE run --headless --browser chrome --group FINANCEIRO --spec "cypress/integration/*-Financeiro/**/*.spec.js" --config baseUrl=$ZW_URL --config-file=$CY_CONFIG_FILE --parallel --record --ci-build-id $CI_PIPELINE_ID

.test-ms: &job8
  extends: .job_teste_template
  artifacts:
    paths:
      - .env
  script:
    - $RUN -v $PWD:/cy -w /zw -e CYPRESS_BASE_URL=$ZW_URL -e CYPRESS_RECORD_KEY=$CYPRESS_RECORD_KEY -e COMMIT_INFO_MESSAGE="$CI_COMMIT_MESSAGE" -e COMMIT_INFO_BRANCH=$CI_COMMIT_REF_NAME  $IMAGE run --headless --browser chrome --group ApiZwAcesso --spec "cypress/integration/*-MicroServicos/**/*.spec.js" --config baseUrl=$ZW_URL --config-file=$CY_CONFIG_FILE --record --ci-build-id $CI_PIPELINE_ID

.test-nzw: &job9
  extends: .job_teste_template
  artifacts:
    paths:
      - .env
  script:
    - $RUN -v $PWD:/cy -w /zw -e CYPRESS_BASE_URL=$ZW_URL -e CYPRESS_RECORD_KEY=$CYPRESS_RECORD_KEY -e COMMIT_INFO_MESSAGE="$CI_COMMIT_MESSAGE" -e COMMIT_INFO_BRANCH=$CI_COMMIT_REF_NAME  $IMAGE run --headless --browser chrome --group NZW --spec "cypress/integration/*-NZW-*/**/*.spec.js" --config baseUrl=$ZW_URL --config-file=$CY_CONFIG_FILE --record --parallel --ci-build-id $CI_PIPELINE_ID

.test-site: &job10
  extends: .job_teste_template
  artifacts:
    paths:
      - .env
  script:
    - $RUN -v $PWD:/cy -w /zw -e CYPRESS_BASE_URL=$ZW_URL -e CYPRESS_RECORD_KEY=$CYPRESS_RECORD_KEY -e COMMIT_INFO_MESSAGE="$CI_COMMIT_MESSAGE" -e COMMIT_INFO_BRANCH=$CI_COMMIT_REF_NAME  $IMAGE run --headless --browser chrome --group SITE --spec "cypress/integration/*-Site/**/*.spec.js" --config baseUrl=$ZW_URL --config-file=$CY_CONFIG_FILE --record --parallel --ci-build-id $CI_PIPELINE_ID

.test-zw-adm-cliente: &job11
  extends: .job_teste_template
  artifacts:
    paths:
      - .env
  script:
    - $RUN -v $PWD:/cy -w /zw -e CYPRESS_BASE_URL=$ZW_URL -e CYPRESS_RECORD_KEY=$CYPRESS_RECORD_KEY -e COMMIT_INFO_MESSAGE="$CI_COMMIT_MESSAGE" -e COMMIT_INFO_BRANCH=$CI_COMMIT_REF_NAME  $IMAGE run --headless --browser chrome --group ADM-CLIENTE --spec "cypress/integration/*-TelaPrincipal-Cliente/**/*.spec.js" --config baseUrl=$ZW_URL --config-file=$CY_CONFIG_FILE --record --parallel --ci-build-id $CI_PIPELINE_ID

.test-zw-adm-vendas: &job12
  extends: .job_teste_template
  artifacts:
    paths:
      - .env
  script:
    - $RUN -v $PWD:/cy -w /zw -e CYPRESS_BASE_URL=$ZW_URL -e CYPRESS_RECORD_KEY=$CYPRESS_RECORD_KEY -e COMMIT_INFO_MESSAGE="$CI_COMMIT_MESSAGE" -e COMMIT_INFO_BRANCH=$CI_COMMIT_REF_NAME  $IMAGE run --headless --browser chrome --group ADM-VENDAS --spec "cypress/integration/*-TelaPrincipal-OperacoesVendas/**/*.spec.js" --config baseUrl=$ZW_URL --config-file=$CY_CONFIG_FILE --record --parallel --ci-build-id $CI_PIPELINE_ID

.test-rodar-por-ultimo: &job13
  extends: .job_teste_template
  artifacts:
    paths:
      - .env
  script:
    - $RUN -v $PWD:/cy -w /zw -e CYPRESS_BASE_URL=$ZW_URL -e CYPRESS_RECORD_KEY=$CYPRESS_RECORD_KEY -e COMMIT_INFO_MESSAGE="$CI_COMMIT_MESSAGE" -e COMMIT_INFO_BRANCH=$CI_COMMIT_REF_NAME  $IMAGE run --headless --browser chrome --group RODAR-ULTIMO --spec "cypress/integration/*-RodarPorUltimo/**/*.spec.js" --config baseUrl=$ZW_URL --config-file=$CY_CONFIG_FILE --record --parallel --ci-build-id $CI_PIPELINE_ID
#
#test-none:
#  stage: test
#  <<: *job9999

test-atualiza-db:
  extends: .job_teste_template
  stage: test
  needs:
    - job: deploy
      artifacts: true
  artifacts:
    paths:
      - .env
  <<: *job0


#test-adm-1:
#  <<: *job1
#test-adm-2:
#  <<: *job1
#test-adm-3:
#  <<: *job1
#test-adm-4:
#  <<: *job1
#test-adm-5:
#  <<: *job1
#
#test-adm-cliente-1:
#  <<: *job11
#
#test-adm-cliente-2:
#  <<: *job11
#
#test-adm-cliente-3:
#  <<: *job11
#
#test-adm-cliente-4:
#  <<: *job11
#
#test-adm-vendas-1:
#  <<: *job12
#test-adm-vendas-2:
#  <<: *job12
#test-adm-vendas-3:
#  <<: *job12
#test-adm-vendas-4:
#  <<: *job12
#
#test-adm-vendas-5:
#  <<: *job12
#
#test-rodar-por-ultimo:
#  <<: *job13
#
#test-cadastros-1:
#  <<: *job3
#test-cadastros-2:
#  <<: *job3
#test-cadastros-3:
#  <<: *job3
#test-cadastros-4:
#  <<: *job3
#
#
#test-bi-1:
#  <<: *job4
#
#test-crm-1:
#  <<: *job5
#test-crm-2:
#  <<: *job5
#test-crm-3:
#  <<: *job5
#test-crm-4:
#  <<: *job5
#test-crm-5:
#  <<: *job5
#
#test-menu-1:
#  <<: *job6
#
#test-menu-2:
#  <<: *job6
#
#test-menu-3:
#  <<: *job6
#
#test-menu-4:
#  <<: *job6
#
#test-financeiro-1:
#  <<: *job7
#test-financeiro-2:
#  <<: *job7
#test-financeiro-3:
#  <<: *job7
#test-financeiro-4:
#  <<: *job7
#test-financeiro-5:
#  <<: *job7
#test-financeiro-6:
#  <<: *job7
#
#test-ApiZwAcesso-1:
#  <<: *job8