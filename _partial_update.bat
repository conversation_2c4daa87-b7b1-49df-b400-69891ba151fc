@echo off

rem git pull

set ROOT=.\target\classes
set TARGET_PATH="/opt/partial_update/atualizar/artifact/7.3"
set HOST="root@************"
set TARGET="%HOST%:%TARGET_PATH%"
set COMMAND="/opt/partial_update/atualizar/updateZW-ZONA-NOVA-Glassfish4.sh"
set OPT=" -o StrictHostKeyChecking=no -o StrictHostKeyChecking=no"

call mvn package -T1C -DskipTests

rem pscp -pw "%PWDZ%" "%ROOT%\controle\arquitetura\servico\ThreadUpdateServico.class" %TARGET%
rem pscp -pw "%PWDZ%" "%ROOT%\controle\arquitetura\servico\UpdateServico.class"       %TARGET%
rem pscp -pw "%PWDZ%" "%ROOT%\controle\arquitetura\servlet\UpdateServlet.class"       %TARGET%
rem pscp -pw "%PWDZ%" "%ROOT%\negocio\comuns\utilitarias\UteisServlet.class"          %TARGET%
rem pscp -pw "%PWDZ%" "%ROOT%\relatorio\controle\basico\ComissaoControle.class"       %TARGET%
pscp -pw "%PWDZ%" "%ROOT%\servicos\integracao\sendy\services\SendyImpl.class"  "%ROOT%\servicos\propriedades\PropsService.class"      %TARGET%

plink -batch -pw "%PWDZ%" %HOST% "pwd=%PWDZ% %COMMAND%"

