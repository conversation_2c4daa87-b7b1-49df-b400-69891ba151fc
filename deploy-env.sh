#!/bin/bash
export HOST_PIPELINE="*********"
export HOST_DNS="pipe-zw.pactosolucoes.com.br"

function getRandomPort() {
    valorTemp=$(shuf -i 10000-11999 |head -n 1)

    while [[ `lsof -i:$valorTemp |wc -l` -gt 1 ]]
    do
        valorTemp=$(shuf -i 12000-14999 |head -n 1)
    done

    echo "$valorTemp"
}

export ZW_PORT=12000
export STACK_NAME=pipe-zw-$ZW_PORT

export PG_PORT=12001
export PG_API_PORT=12002
export TR_PORT=12003
export DISCOVERY_PORT=12004
export API_PORT=12005
export AUT_PORT=12006
export BI_MS_PORT=12007
export CAD_MS_PORT=12008
export DASH_API_PORT=12009
export LOGIN_PORT=12010
export ADM_PORT=12011
export OAMD_PORT=12012
export ZW_AUTO_PORT=12013
export VENDAS_PORT=12014
export FULL_REPORT_PORT=12015
export PRODUTO_MS_PORT=12016
export PACTO_PAY_PORT=12017
export PERSONAGEM_PORT=12018
export PLANO_MS_PORT=12019
export NTR_PORT=12020
export CLUBE_VANTAGENS_MS_PORT=12021
export RELATORIO_MS_PORT=12022
export DYNAMODB_PORT=12023
export ADM_CORE_MS_PORT=12024
export NOVO_LOGIN_PORT=12025
export ADM_MS_PORT=12026


export ZW_URL=http://$HOST_DNS:$ZW_PORT/ZillyonWeb
export LOGIN_URL=http://$HOST_DNS:$LOGIN_PORT/LoginApp
export TREINO_URL=http://$HOST_DNS:$TR_PORT/TreinoWeb
export VENDAS_URL=http://$HOST_DNS:$VENDAS_PORT
export API_URL=http://$HOST_DNS:$API_PORT/API-ZillyonWeb
export ZW_AUTO_URL=http://${HOST_DNS}:${ZW_AUTO_PORT}/zw-auto
export OAMD_URL=http://${HOST_DNS}:${OAMD_PORT}/NewOAMD
export URL_NOVO_LOGIN=http://${HOST_DNS}:${NOVO_LOGIN_PORT}

sed -i -e "s/STACK_NAME=.*/STACK_NAME=$STACK_NAME/g" .env
sed -i -e "s~ZW_URL=.*~ZW_URL=$ZW_URL~g" .env
sed -i -e "s~URL_ZW_INTEGRACAO=.*~URL_ZW_INTEGRACAO=$ZW_URL~g" .env
sed -i -e "s~LOGIN_URL=.*~LOGIN_URL=$LOGIN_URL~g" .env
sed -i -e "s~TREINO_URL=.*~TREINO_URL=$TREINO_URL~g" .env
sed -i -e "s~VENDAS_URL=.*~VENDAS_URL=$VENDAS_URL~g" .env
sed -i -e "s~API_URL=.*~API_URL=$API_URL~g" .env
sed -i -e "s~ZW_AUTO_URL=.*~ZW_AUTO_URL=$ZW_AUTO_URL~g" .env
sed -i -e "s~OAMD_URL=.*~OAMD_URL=$OAMD_URL~g" .env
sed -i -e "s~URL_NOVO_LOGIN=.*~URL_NOVO_LOGIN=$URL_NOVO_LOGIN~g" .env

sed -i -e "s/ZW_PORT=.*/ZW_PORT=$ZW_PORT/g" .env
sed -i -e "s/TR_PORT=.*/TR_PORT=$TR_PORT/g" .env
sed -i -e "s/PLANO_MS_PORT=.*/PLANO_MS_PORT=$PLANO_MS_PORT/g" .env
sed -i -e "s/NTR_PORT=.*/NTR_PORT=$NTR_PORT/g" .env
sed -i -e "s/HOST_PIPELINE=.*/HOST_PIPELINE=$HOST_PIPELINE/g" .env
sed -i -e "s/HOST_DNS=.*/HOST_DNS=$HOST_DNS/g" .env
sed -i -e "s/DISCOVERY_PORT=.*/DISCOVERY_PORT=$DISCOVERY_PORT/g" .env
sed -i -e "s/API_PORT=.*/API_PORT=$API_PORT/g" .env
sed -i -e "s/AUT_PORT=.*/AUT_PORT=$AUT_PORT/g" .env
sed -i -e "s/BI_MS_PORT=.*/BI_MS_PORT=$BI_MS_PORT/g" .env
sed -i -e "s/CAD_MS_PORT=.*/CAD_MS_PORT=$CAD_MS_PORT/g" .env
sed -i -e "s/DASH_API_PORT=.*/DASH_API_PORT=$DASH_API_PORT/g" .env
sed -i -e "s/LOGIN_PORT=.*/LOGIN_PORT=$LOGIN_PORT/g" .env
sed -i -e "s/ADM_PORT=.*/ADM_PORT=$ADM_PORT/g" .env
sed -i -e "s/OAMD_PORT=.*/OAMD_PORT=$OAMD_PORT/g" .env
sed -i -e "s/ZW_AUTO_PORT=.*/ZW_AUTO_PORT=$ZW_AUTO_PORT/g" .env
sed -i -e "s/VENDAS_PORT=.*/VENDAS_PORT=$VENDAS_PORT/g" .env
sed -i -e "s/FULL_REPORT_PORT=.*/FULL_REPORT_PORT=$FULL_REPORT_PORT/g" .env
sed -i -e "s/PRODUTO_MS_PORT=.*/PRODUTO_MS_PORT=$PRODUTO_MS_PORT/g" .env
sed -i -e "s/PACTO_PAY_PORT=.*/PACTO_PAY_PORT=$PACTO_PAY_PORT/g" .env
sed -i -e "s/PERSONAGEM_PORT=.*/PERSONAGEM_PORT=$PERSONAGEM_PORT/g" .env
sed -i -e "s/PG_PORT=.*/PG_PORT=$PG_PORT/g" .env
sed -i -e "s/PG_API_PORT=.*/PG_API_PORT=$PG_API_PORT/g" .env
sed -i -e "s/CLUBE_VANTAGENS_MS_PORT=.*/CLUBE_VANTAGENS_MS_PORT=$CLUBE_VANTAGENS_MS_PORT/g" .env
sed -i -e "s/RELATORIO_MS_PORT=.*/RELATORIO_MS_PORT=$RELATORIO_MS_PORT/g" .env
sed -i -e "s/DYNAMODB_PORT=.*/DYNAMODB_PORT=$DYNAMODB_PORT/g" .env
sed -i -e "s/ADM_CORE_MS_PORT=.*/ADM_CORE_MS_PORT=$ADM_CORE_MS_PORT/g" .env
sed -i -e "s/NOVO_LOGIN_PORT=.*/NOVO_LOGIN_PORT=$NOVO_LOGIN_PORT/g" .env
sed -i -e "s/ADM_MS_PORT=.*/ADM_MS_PORT=$ADM_MS_PORT/g" .env


