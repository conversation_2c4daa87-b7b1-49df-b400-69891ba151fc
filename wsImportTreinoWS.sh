#!/bin/bash

ROOT="$(pwd)/target/generated/jax-wsCache/TreinoWS"
GENERATED="$ROOT"
PACKAGE="servicos.integracao.treino"
WSDL="http://localhost:8080/TreinoWeb/TreinoWS?wsdl"
SRC="$(pwd)/src/main/java/servicos/integracao/treino"
export JAVA_TOOL_OPTIONS=-Dfile.encoding=ISO8859_1


mkdir -p $GENERATED
mkdir -p $SRC

echo $WSDL

#wsimport $WSDL -d $GENERATED -extension -Xnocompile -Xendorsed -keep -verbose -target 2.1 -p servicos.integracao.treino -wsdllocation $WSDl
wsimport $WSDL -keep -Xnocompile -d $GENERATED -p servicos.integracao.treino


cp -rp $GENERATED/servicos/integracao/treino/*.java $SRC

rm -rf $GENERATED