# YAML anchors for reusable configurations
.job_config: &job_config
  image: registry.gitlab.com/plataformazw/tag-versions:master
  interruptible: true
  tags:
    - docker
  stage: finish
  allow_failure: true
  only:
    - master

.git_setup: &git_setup
  - set +e                  # Sem isso, o script não funciona, pois configura o script para executar mesmo com erro.
  - eval $(ssh-agent -s)
  - echo "$SSH_PRIVATE_KEY" | tr -d '\r' | ssh-add -
  - git config --global user.name "Pipeline"
  - git config --global user.email "<EMAIL>"
  - git remote set-url --<NAME_EMAIL>:$CI_PROJECT_PATH


# Base template for merge jobs
.merge_template:
  <<: *job_config
  script:
    - *git_setup
    - git fetch
    - git checkout "$TARGET_BRANCH"
    - git pull
    - PULL_RESULT=$(git pull origin master)
    - |
      if echo "$PULL_RESULT" | grep -q "Automatic merge failed"; then
        echo "Contém conflitos!!"
        curl -s --request POST --header "PRIVATE-TOKEN: $API_TOKEN" --data "source_branch=master&target_branch=$TARGET_BRANCH_ENCODED&title=$MR_TITLE" "$CI_API_V4_URL/projects/$CI_PROJECT_ID/merge_requests"
        curl -s --request POST --header "Content-Type: application/json" --data "$CHAT_MESSAGE" "$CHAT_WEBHOOK_URL"
        exit 1
      else
        echo $CI_COMMIT_REF_NAME
        echo "Não contém conflito"
        GIT_SSH_COMMAND="ssh -o UserKnownHostsFile=/dev/null -o StrictHostKeyChecking=no" git push
      fi

# Job definitions with specific variables
zeta-stage_merge-develop:
  extends: .merge_template
  variables:
    TARGET_BRANCH: "develop"
    TARGET_BRANCH_ENCODED: "develop"
    MR_TITLE: "AutoMerge"
    CHAT_WEBHOOK_URL: "https://chat.googleapis.com/v1/spaces/AAAABogbUp0/messages?key=AIzaSyDdI0hCZtE6vySjMm-WEfRq3CPzqKqqsHI&token=_kJcnaZJ24lMdOk7czpPoB963XWbTAMX8WNU6MZUR6Y"
    CHAT_MESSAGE: '{"text": "*Conflitos no ZW!!*\n*Branch: develop*\nPovão do Develop, cadê vocês?"}'

omega-stage_merge-release:
  extends: .merge_template
  variables:
    TARGET_BRANCH: "release/RC"
    TARGET_BRANCH_ENCODED: "release%2FRC"
    MR_TITLE: "AutoMerge"
    CHAT_WEBHOOK_URL: "https://chat.googleapis.com/v1/spaces/AAAABogbUp0/messages?key=AIzaSyDdI0hCZtE6vySjMm-WEfRq3CPzqKqqsHI&token=_kJcnaZJ24lMdOk7czpPoB963XWbTAMX8WNU6MZUR6Y"
    CHAT_MESSAGE: '{"text": "*Conflitos no ZW!!*\n*Branch: release/RC*\nGalera da Release, cadê vocês?"}'

chi-stage_merge-gc:
  extends: .merge_template
  variables:
    TARGET_BRANCH: "release/RC-GC"
    TARGET_BRANCH_ENCODED: "release%2FRC-GC"
    MR_TITLE: "AutoMerge%20(Master_RC-GC)"
    CHAT_WEBHOOK_URL: "https://chat.googleapis.com/v1/spaces/AAAABogbUp0/messages?key=AIzaSyDdI0hCZtE6vySjMm-WEfRq3CPzqKqqsHI&token=_kJcnaZJ24lMdOk7czpPoB963XWbTAMX8WNU6MZUR6Y"
    CHAT_MESSAGE: '{"text": "*Conflitos no ZW!!*\n*Branch: release/RC-GC*\nGuardião da branch GC, apresente-se!?"}'

psi-stage_merge-edc:
  extends: .merge_template
  variables:
    TARGET_BRANCH: "release/RC-EDC"
    TARGET_BRANCH_ENCODED: "release%2FRC-EDC"
    MR_TITLE: "AutoMerge%20(Master_RC-EDC)"
    CHAT_WEBHOOK_URL: "https://chat.googleapis.com/v1/spaces/AAAABogbUp0/messages?key=AIzaSyDdI0hCZtE6vySjMm-WEfRq3CPzqKqqsHI&token=_kJcnaZJ24lMdOk7czpPoB963XWbTAMX8WNU6MZUR6Y"
    CHAT_MESSAGE: '{"text": "*Conflitos no ZW!!*\n*Branch: release/RC-EDC*\nGuardião da branch ENGENHARIA, apresente-se!?"}'

upsilon-stage_merge-manaus:
  extends: .merge_template
  variables:
    TARGET_BRANCH: "release/rc-manaus"
    TARGET_BRANCH_ENCODED: "release%2Frc-manaus"
    MR_TITLE: "AutoMerge%20(Master_RC-MANAUS)"
    CHAT_WEBHOOK_URL: "https://chat.googleapis.com/v1/spaces/AAAABogbUp0/messages?key=AIzaSyDdI0hCZtE6vySjMm-WEfRq3CPzqKqqsHI&token=_kJcnaZJ24lMdOk7czpPoB963XWbTAMX8WNU6MZUR6Y"
    CHAT_MESSAGE: '{"text": "*Conflitos no ZW!!*\n*Branch: release/rc-manaus*\nGuardião da branch rc-manaus, apresente-se!?"}'
