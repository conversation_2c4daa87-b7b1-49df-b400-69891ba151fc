<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="ReceitaPorPeriodoSinteticoRel" pageWidth="680" pageHeight="878" columnWidth="625" leftMargin="19" rightMargin="36" topMargin="2" bottomMargin="2" whenResourceMissingType="Empty">
	<property name="ireport.zoom" value="2.1435888100000016"/>
	<property name="ireport.x" value="117"/>
	<property name="ireport.y" value="718"/>
	<property name="ireport.scriptlethandling" value="0"/>
	<property name="ireport.encoding" value="ISO-8859-1"/>
	<import value="net.sf.jasperreports.engine.*"/>
	<import value="java.util.*"/>
	<import value="net.sf.jasperreports.engine.data.*"/>
	<subDataset name="Teste"/>
	<parameter name="logoPadraoRelatorio" class="java.io.InputStream" isForPrompting="false"/>
	<parameter name="tituloRelatorio" class="java.lang.String" isForPrompting="false"/>
	<parameter name="nomeEmpresa" class="java.lang.String" isForPrompting="false"/>
	<parameter name="versaoSoftware" class="java.lang.String" isForPrompting="false"/>
	<parameter name="usuario" class="java.lang.String" isForPrompting="false"/>
	<parameter name="filtros" class="java.lang.String" isForPrompting="false"/>
	<parameter name="SUBREPORT_DIR" class="java.lang.String">
		<defaultValueExpression><![CDATA["E:\\desenvolvimentosv\\Projeto Pacto Solucoes\\ZillyonWeb\\src\\java\\relatorio\\designRelatorio\\financeiro\\"]]></defaultValueExpression>
	</parameter>
	<parameter name="SUBREPORT_DIR1" class="java.lang.String">
		<defaultValueExpression><![CDATA["E:\\desenvolvimentosv\\Projeto Pacto Solucoes\\ZillyonWeb\\src\\java\\relatorio\\designRelatorio\\financeiro\\"]]></defaultValueExpression>
	</parameter>
	<parameter name="dataIni" class="java.lang.String" isForPrompting="false"/>
	<parameter name="dataFim" class="java.lang.String" isForPrompting="false"/>
	<parameter name="qtdAV" class="java.lang.String" isForPrompting="false"/>
	<parameter name="qtdCA" class="java.lang.String" isForPrompting="false"/>
	<parameter name="qtdChequeAV" class="java.lang.String" isForPrompting="false"/>
	<parameter name="qtdChequePR" class="java.lang.String" isForPrompting="false"/>
	<parameter name="qtdOutro" class="java.lang.String" isForPrompting="false"/>
	<parameter name="valorAV" class="java.lang.Double" isForPrompting="false"/>
	<parameter name="valorCA" class="java.lang.Double" isForPrompting="false"/>
	<parameter name="valorChequeAV" class="java.lang.Double" isForPrompting="false"/>
	<parameter name="valorChequePR" class="java.lang.Double" isForPrompting="false"/>
	<parameter name="valorOutro" class="java.lang.Double" isForPrompting="false"/>
	<field name="valorTotalDinheiro" class="java.lang.Double"/>
	<field name="valorTotalChequeVista" class="java.lang.Double"/>
	<field name="valorTotalChequePrazo" class="java.lang.Double"/>
	<field name="valorTotalCartaoCredito" class="java.lang.Double"/>
	<field name="valorTotalCartaoDebito" class="java.lang.Double"/>
	<field name="valorTotalOutros" class="java.lang.Double"/>
	<field name="valorTotalFinal" class="java.lang.Double"/>
	<field name="qtdeTotalDinheiro" class="java.lang.Integer"/>
	<field name="qtdeTotalChequeVista" class="java.lang.Integer"/>
	<field name="qtdeTotalChequePrazo" class="java.lang.Integer"/>
	<field name="qtdeTotalCartaoCredito" class="java.lang.Integer"/>
	<field name="qtdeTotalCartaoDebito" class="java.lang.Integer"/>
	<field name="qtdeTotalOutros" class="java.lang.Integer"/>
	<field name="qtdeTotalFinal" class="java.lang.Integer"/>
	<field name="visaoDinheiro" class="java.lang.Boolean"/>
	<field name="dataIni" class="java.lang.String"/>
	<field name="dataFim" class="java.lang.String"/>
	<field name="visaoChequeVista" class="java.lang.Boolean"/>
	<field name="visaoCartaoDebito" class="java.lang.Boolean"/>
	<field name="visaoCartaoCredito" class="java.lang.Boolean"/>
	<field name="visaoOutros" class="java.lang.Boolean"/>
	<field name="visaoChequePrazo" class="java.lang.Boolean"/>
	<field name="qtdeTotalDevolucao" class="java.lang.Integer"/>
	<field name="valorTotalDevolucao" class="java.lang.Double"/>
	<field name="visaoDevolucao" class="java.lang.Boolean"/>
	<field name="visaoBoleto" class="java.lang.Boolean"/>
	<field name="valorTotalBoleto" class="java.lang.Double"/>
	<field name="qtdeTotalBoleto" class="java.lang.Integer"/>
	<field name="qtdeTotalEspecie" class="java.lang.Integer"/>
	<field name="valorTotalEspecie" class="java.lang.Double"/>
	<field name="valorTotalTransferenciaBancaria" class="java.lang.Double"/>
	<field name="qtdeTotalTransferenciaBancaria" class="java.lang.Integer"/>
	<field name="visaoTransferenciaBancaria" class="java.lang.Boolean"/>
	<field name="valorTotalPix" class="java.lang.Double"/>
	<field name="qtdeTotalPix" class="java.lang.Integer"/>
	<field name="visaoPix" class="java.lang.Boolean"/>
	<group name="Dinheiro">
		<groupExpression><![CDATA[]]></groupExpression>
		<groupHeader>
			<band height="50" splitType="Stretch">
				<printWhenExpression><![CDATA[$F{visaoDinheiro}]]></printWhenExpression>
				<staticText>
					<reportElement key="staticText-83" x="12" y="3" width="79" height="24"/>
					<textElement>
						<font size="18"/>
					</textElement>
					<text><![CDATA[Dinheiro]]></text>
				</staticText>
				<line>
					<reportElement key="line-1" x="1" y="3" width="624" height="2"/>
				</line>
				<textField isBlankWhenNull="false">
					<reportElement key="textField-217" x="284" y="19" width="58" height="14"/>
					<textElement/>
					<textFieldExpression class="java.lang.Integer"><![CDATA[$F{qtdeTotalDinheiro}]]></textFieldExpression>
				</textField>
				<textField pattern="R$ #,##0.00" isBlankWhenNull="false">
					<reportElement key="textField-223" x="458" y="19" width="115" height="14"/>
					<textElement/>
					<textFieldExpression class="java.lang.Double"><![CDATA[$F{valorTotalDinheiro}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement key="staticText-107" x="284" y="3" width="67" height="14"/>
					<textElement/>
					<text><![CDATA[Quantidade]]></text>
				</staticText>
				<staticText>
					<reportElement key="staticText-113" x="458" y="3" width="67" height="14"/>
					<textElement/>
					<text><![CDATA[Valor total]]></text>
				</staticText>
			</band>
		</groupHeader>
		<groupFooter>
			<band splitType="Stretch"/>
		</groupFooter>
	</group>
	<group name="CHVista">
		<groupExpression><![CDATA[]]></groupExpression>
		<groupHeader>
			<band height="50" splitType="Stretch">
				<printWhenExpression><![CDATA[$F{visaoChequeVista}]]></printWhenExpression>
				<line>
					<reportElement key="line-2" x="1" y="3" width="624" height="2"/>
				</line>
				<staticText>
					<reportElement key="staticText-88" x="12" y="3" width="261" height="24"/>
					<textElement>
						<font size="18"/>
					</textElement>
					<text><![CDATA[Ch. à Vista]]></text>
				</staticText>
				<textField isBlankWhenNull="false">
					<reportElement key="textField-218" x="284" y="19" width="58" height="14"/>
					<textElement/>
					<textFieldExpression class="java.lang.Integer"><![CDATA[$F{qtdeTotalChequeVista}]]></textFieldExpression>
				</textField>
				<textField pattern="R$ #,##0.00" isBlankWhenNull="false">
					<reportElement key="textField-224" x="458" y="19" width="115" height="14"/>
					<textElement/>
					<textFieldExpression class="java.lang.Double"><![CDATA[$F{valorTotalChequeVista}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement key="staticText-106" x="284" y="3" width="67" height="14"/>
					<textElement/>
					<text><![CDATA[Quantidade]]></text>
				</staticText>
				<staticText>
					<reportElement key="staticText-112" x="458" y="3" width="67" height="14"/>
					<textElement/>
					<text><![CDATA[Valor total]]></text>
				</staticText>
			</band>
		</groupHeader>
		<groupFooter>
			<band splitType="Stretch"/>
		</groupFooter>
	</group>
	<group name="CHPrazo">
		<groupExpression><![CDATA[]]></groupExpression>
		<groupHeader>
			<band height="50" splitType="Stretch">
				<printWhenExpression><![CDATA[$F{visaoChequePrazo}]]></printWhenExpression>
				<line>
					<reportElement key="line-3" x="1" y="3" width="624" height="2"/>
				</line>
				<staticText>
					<reportElement key="staticText-91" x="12" y="3" width="261" height="24"/>
					<textElement>
						<font size="18"/>
					</textElement>
					<text><![CDATA[Ch. à Prazo]]></text>
				</staticText>
				<textField isBlankWhenNull="false">
					<reportElement key="textField-219" x="284" y="19" width="58" height="14"/>
					<textElement/>
					<textFieldExpression class="java.lang.Integer"><![CDATA[$F{qtdeTotalChequePrazo}]]></textFieldExpression>
				</textField>
				<textField pattern="R$ #,##0.00" isBlankWhenNull="false">
					<reportElement key="textField-225" x="458" y="19" width="115" height="14"/>
					<textElement/>
					<textFieldExpression class="java.lang.Double"><![CDATA[$F{valorTotalChequePrazo}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement key="staticText-105" x="284" y="3" width="67" height="14"/>
					<textElement/>
					<text><![CDATA[Quantidade]]></text>
				</staticText>
				<staticText>
					<reportElement key="staticText-111" x="458" y="3" width="67" height="14"/>
					<textElement/>
					<text><![CDATA[Valor total]]></text>
				</staticText>
			</band>
		</groupHeader>
		<groupFooter>
			<band splitType="Stretch"/>
		</groupFooter>
	</group>
	<group name="CartaoDebito">
		<groupExpression><![CDATA[]]></groupExpression>
		<groupHeader>
			<band height="50" splitType="Stretch">
				<printWhenExpression><![CDATA[$F{visaoCartaoDebito}]]></printWhenExpression>
				<line>
					<reportElement key="line-4" x="1" y="3" width="624" height="2"/>
				</line>
				<staticText>
					<reportElement key="staticText-94" x="12" y="3" width="261" height="24"/>
					<textElement>
						<font size="18"/>
					</textElement>
					<text><![CDATA[Cartão de Débito]]></text>
				</staticText>
				<textField isBlankWhenNull="false">
					<reportElement key="textField-220" x="284" y="19" width="58" height="14"/>
					<textElement/>
					<textFieldExpression class="java.lang.Integer"><![CDATA[$F{qtdeTotalCartaoDebito}]]></textFieldExpression>
				</textField>
				<textField pattern="R$ #,##0.00" isBlankWhenNull="false">
					<reportElement key="textField-226" x="458" y="19" width="115" height="14"/>
					<textElement/>
					<textFieldExpression class="java.lang.Double"><![CDATA[$F{valorTotalCartaoDebito}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement key="staticText-104" x="284" y="3" width="67" height="14"/>
					<textElement/>
					<text><![CDATA[Quantidade]]></text>
				</staticText>
				<staticText>
					<reportElement key="staticText-110" x="458" y="3" width="67" height="14"/>
					<textElement/>
					<text><![CDATA[Valor total]]></text>
				</staticText>
			</band>
		</groupHeader>
		<groupFooter>
			<band splitType="Stretch"/>
		</groupFooter>
	</group>
	<group name="TransferenciaBancaria">
		<groupHeader>
			<band height="50" splitType="Stretch">
				<printWhenExpression><![CDATA[$F{visaoDinheiro}]]></printWhenExpression>
				<textField pattern="R$ #,##0.00" isBlankWhenNull="false">
					<reportElement key="textField-226" x="458" y="18" width="115" height="14"/>
					<textElement/>
					<textFieldExpression class="java.lang.Double"><![CDATA[$F{valorTotalTransferenciaBancaria}]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="false">
					<reportElement key="textField-220" x="284" y="18" width="58" height="14"/>
					<textElement/>
					<textFieldExpression class="java.lang.Integer"><![CDATA[$F{qtdeTotalTransferenciaBancaria}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement key="staticText-110" x="458" y="2" width="67" height="14"/>
					<textElement/>
					<text><![CDATA[Valor total]]></text>
				</staticText>
				<staticText>
					<reportElement key="staticText-104" x="284" y="2" width="67" height="14"/>
					<textElement/>
					<text><![CDATA[Quantidade]]></text>
				</staticText>
				<staticText>
					<reportElement key="staticText-94" x="12" y="2" width="261" height="24"/>
					<textElement>
						<font size="18"/>
					</textElement>
					<text><![CDATA[Transferência Bancária]]></text>
				</staticText>
				<line>
					<reportElement key="line-4" x="1" y="0" width="624" height="2"/>
				</line>
			</band>
		</groupHeader>
		<groupFooter>
			<band height="50"/>
		</groupFooter>
	</group>
	<group name="agrupadores">
		<groupExpression><![CDATA[]]></groupExpression>
		<groupHeader>
			<band height="50" splitType="Stretch">
				<printWhenExpression><![CDATA[$F{visaoCartaoCredito}]]></printWhenExpression>
				<line>
					<reportElement key="line-5" x="1" y="3" width="624" height="2"/>
				</line>
				<staticText>
					<reportElement key="staticText-97" x="12" y="3" width="261" height="24"/>
					<textElement>
						<font size="18"/>
					</textElement>
					<text><![CDATA[Cartão de Crédito]]></text>
				</staticText>
				<textField pattern="R$ #,##0.00" isBlankWhenNull="false">
					<reportElement key="textField-227" x="458" y="19" width="115" height="14"/>
					<textElement/>
					<textFieldExpression class="java.lang.Double"><![CDATA[$F{valorTotalCartaoCredito}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement key="staticText-108" x="458" y="3" width="67" height="14"/>
					<textElement/>
					<text><![CDATA[Valor total]]></text>
				</staticText>
				<staticText>
					<reportElement key="staticText-103" x="284" y="3" width="67" height="14"/>
					<textElement/>
					<text><![CDATA[Quantidade]]></text>
				</staticText>
				<textField isBlankWhenNull="false">
					<reportElement key="textField-221" x="285" y="19" width="58" height="14"/>
					<textElement/>
					<textFieldExpression class="java.lang.Integer"><![CDATA[$F{qtdeTotalCartaoCredito}]]></textFieldExpression>
				</textField>
			</band>
			<band height="50">
				<printWhenExpression><![CDATA[$F{visaoBoleto}]]></printWhenExpression>
				<staticText>
					<reportElement key="staticText-97" x="12" y="4" width="261" height="24"/>
					<textElement>
						<font size="18"/>
					</textElement>
					<text><![CDATA[Boleto]]></text>
				</staticText>
				<staticText>
					<reportElement key="staticText-103" x="284" y="4" width="67" height="14"/>
					<textElement/>
					<text><![CDATA[Quantidade]]></text>
				</staticText>
				<textField isBlankWhenNull="false">
					<reportElement key="textField-221" x="285" y="18" width="58" height="14"/>
					<textElement/>
					<textFieldExpression class="java.lang.Integer"><![CDATA[$F{qtdeTotalBoleto}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement key="staticText-108" x="457" y="4" width="67" height="14"/>
					<textElement/>
					<text><![CDATA[Valor total]]></text>
				</staticText>
				<textField pattern="R$ #,##0.00" isBlankWhenNull="false">
					<reportElement key="textField-227" x="458" y="18" width="115" height="14"/>
					<textElement/>
					<textFieldExpression class="java.lang.Double"><![CDATA[$F{valorTotalBoleto}]]></textFieldExpression>
				</textField>
				<line>
					<reportElement key="line-5" x="1" y="3" width="624" height="2"/>
				</line>
			</band>
			<band height="50">
				<printWhenExpression><![CDATA[$F{visaoPix}]]></printWhenExpression>
				<line>
					<reportElement key="line-5" x="1" y="0" width="624" height="2"/>
				</line>
				<staticText>
					<reportElement key="staticText-103" x="285" y="2" width="67" height="14"/>
					<textElement/>
					<text><![CDATA[Quantidade]]></text>
				</staticText>
				<textField isBlankWhenNull="false">
					<reportElement key="textField-221" x="285" y="16" width="58" height="14"/>
					<textElement/>
					<textFieldExpression class="java.lang.Integer"><![CDATA[$F{qtdeTotalPix}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement key="staticText-108" x="458" y="2" width="67" height="14"/>
					<textElement/>
					<text><![CDATA[Valor total]]></text>
				</staticText>
				<textField pattern="R$ #,##0.00" isBlankWhenNull="false">
					<reportElement key="textField-227" x="458" y="16" width="115" height="14"/>
					<textElement/>
					<textFieldExpression class="java.lang.Double"><![CDATA[$F{valorTotalPix}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement key="staticText-97" x="12" y="2" width="261" height="24"/>
					<textElement>
						<font size="18"/>
					</textElement>
					<text><![CDATA[Pix]]></text>
				</staticText>
			</band>
			<band height="50">
				<printWhenExpression><![CDATA[$F{visaoDevolucao}]]></printWhenExpression>
				<line>
					<reportElement key="line-5" x="1" y="0" width="624" height="2"/>
				</line>
				<staticText>
					<reportElement key="staticText-97" x="12" y="2" width="263" height="24"/>
					<textElement>
						<font size="15"/>
					</textElement>
					<text><![CDATA[Devolução de Dinheiro - Cancelamento]]></text>
				</staticText>
				<staticText>
					<reportElement key="staticText-103" x="284" y="2" width="67" height="14"/>
					<textElement/>
					<text><![CDATA[Quantidade]]></text>
				</staticText>
				<staticText>
					<reportElement key="staticText-108" x="458" y="2" width="67" height="14"/>
					<textElement/>
					<text><![CDATA[Valor total]]></text>
				</staticText>
				<textField isBlankWhenNull="false">
					<reportElement key="textField-221" x="285" y="16" width="58" height="14"/>
					<textElement/>
					<textFieldExpression class="java.lang.Integer"><![CDATA[$F{qtdeTotalDevolucao}]]></textFieldExpression>
				</textField>
				<textField pattern="- R$ #,##0.00" isBlankWhenNull="false">
					<reportElement key="textField-227" x="458" y="16" width="115" height="14"/>
					<textElement/>
					<textFieldExpression class="java.lang.Double"><![CDATA[$F{valorTotalDevolucao}]]></textFieldExpression>
				</textField>
			</band>
		</groupHeader>
		<groupFooter>
			<band splitType="Stretch"/>
		</groupFooter>
	</group>
	<background>
		<band splitType="Stretch"/>
	</background>
	<title>
		<band splitType="Stretch"/>
	</title>
	<pageHeader>
		<band height="74" splitType="Stretch">
			<staticText>
				<reportElement key="staticText-82" mode="Opaque" x="299" y="20" width="33" height="17"/>
				<box>
					<pen lineWidth="0.0" lineColor="#666666"/>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<leftPen lineWidth="0.0" lineColor="#666666"/>
					<bottomPen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="14" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[ a]]></text>
			</staticText>
			<image isUsingCache="true" onErrorType="Blank" evaluationTime="Page">
				<reportElement key="image-1" x="1" y="1" width="82" height="36" isPrintWhenDetailOverflows="true"/>
				<box>
					<topPen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
					<leftPen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
					<bottomPen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
				</box>
				<imageExpression class="java.io.InputStream"><![CDATA[$P{logoPadraoRelatorio}]]></imageExpression>
			</image>
			<textField pattern="dd/MM/yyyy HH.mm.ss" isBlankWhenNull="false">
				<reportElement key="dataRel-1" mode="Opaque" x="520" y="1" width="105" height="19" backcolor="#FFFFFF"/>
				<box bottomPadding="2">
					<topPen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
					<leftPen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
					<bottomPen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Verdana" size="8" isItalic="false" pdfFontName="Helvetica"/>
				</textElement>
				<textFieldExpression class="java.util.Date"><![CDATA[new Date()]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement key="textField-2" x="83" y="1" width="437" height="19"/>
				<box>
					<pen lineWidth="0.5" lineStyle="Solid"/>
					<topPen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
					<leftPen lineWidth="0.0" lineStyle="Solid"/>
					<bottomPen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.0" lineStyle="Solid"/>
				</box>
				<textElement textAlignment="Center">
					<font fontName="Arial" size="14" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{tituloRelatorio}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-25" x="520" y="20" width="60" height="17"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
					<bottomPen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right">
					<font fontName="Arial"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA["Pág: " + $V{PAGE_NUMBER} + " de "]]></textFieldExpression>
			</textField>
			<textField evaluationTime="Report" isBlankWhenNull="false">
				<reportElement key="textField-26" x="580" y="20" width="45" height="17"/>
				<box bottomPadding="2">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<bottomPen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
				</box>
				<textElement>
					<font fontName="Arial"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[" " + $V{PAGE_NUMBER} + ""]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-215" x="83" y="20" width="216" height="17"/>
				<box>
					<bottomPen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
				</box>
				<textElement textAlignment="Right">
					<font fontName="Arial" size="14" isBold="true" isItalic="true" pdfFontName="Helvetica-BoldOblique"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{dataIni}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-216" x="332" y="20" width="188" height="17"/>
				<box>
					<bottomPen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
				</box>
				<textElement textAlignment="Left">
					<font fontName="Arial" size="14" isBold="true" isItalic="true" pdfFontName="Helvetica-BoldOblique"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{dataFim}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-216" x="83" y="37" width="437" height="37"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="10" isBold="true" isItalic="true" pdfFontName="Helvetica-BoldOblique"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{filtros}]]></textFieldExpression>
			</textField>
		</band>
	</pageHeader>
	<columnHeader>
		<band splitType="Stretch"/>
	</columnHeader>
	<detail>
		<band height="29" splitType="Stretch">
			<staticText>
				<reportElement key="staticText-101" x="323" y="11" width="134" height="18"/>
				<textElement>
					<font size="12" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Quantidade]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-102" x="457" y="11" width="106" height="18"/>
				<textElement>
					<font size="12" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Valor total]]></text>
			</staticText>
		</band>
		<band height="27">
			<printWhenExpression><![CDATA[$F{qtdeTotalEspecie} > 0]]></printWhenExpression>
			<staticText>
				<reportElement key="staticText-101" mode="Transparent" x="213" y="5" width="110" height="18" forecolor="#000000" backcolor="#FFFFFF">
					<printWhenExpression><![CDATA[$F{qtdeTotalOutros} > 0]]></printWhenExpression>
				</reportElement>
				<textElement textAlignment="Left" verticalAlignment="Top" rotation="None" lineSpacing="Single" markup="none">
					<font fontName="SansSerif" size="12" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica-Bold" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
				</textElement>
				<text><![CDATA[Espécie]]></text>
			</staticText>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-229" x="323" y="5" width="122" height="21"/>
				<textElement>
					<font size="14" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.Integer"><![CDATA[$F{qtdeTotalEspecie}]]></textFieldExpression>
			</textField>
			<textField pattern="  R$ #,##0.00" isBlankWhenNull="false">
				<reportElement key="textField-230" x="457" y="5" width="158" height="21"/>
				<textElement>
					<font size="14" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.Double"><![CDATA[$F{valorTotalEspecie}]]></textFieldExpression>
			</textField>
		</band>
		<band height="28">
			<printWhenExpression><![CDATA[$F{visaoOutros}]]></printWhenExpression>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-229" x="323" y="5" width="122" height="21"/>
				<textElement>
					<font size="14" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.Integer"><![CDATA[$F{qtdeTotalOutros}]]></textFieldExpression>
			</textField>
			<textField pattern="  R$ #,##0.00" isBlankWhenNull="false">
				<reportElement key="textField-229" x="457" y="5" width="147" height="21"/>
				<textElement>
					<font size="14" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.Double"><![CDATA[$F{valorTotalOutros}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement key="staticText-101" mode="Transparent" x="213" y="5" width="110" height="18" forecolor="#000000" backcolor="#FFFFFF"/>
				<textElement textAlignment="Left" verticalAlignment="Top" rotation="None" lineSpacing="Single" markup="none">
					<font fontName="SansSerif" size="12" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica-Bold" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
				</textElement>
				<text><![CDATA[Outros]]></text>
			</staticText>
		</band>
		<band height="26">
			<printWhenExpression><![CDATA[$F{visaoDevolucao}]]></printWhenExpression>
			<textField pattern="- R$ #,##0.00" isBlankWhenNull="false">
				<reportElement key="textField-230" x="455" y="5" width="158" height="21"/>
				<textElement>
					<font size="14" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.Double"><![CDATA[$F{valorTotalDevolucao}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement key="staticText-101" mode="Transparent" x="213" y="5" width="110" height="18" forecolor="#000000" backcolor="#FFFFFF"/>
				<textElement textAlignment="Left" verticalAlignment="Top" rotation="None" lineSpacing="Single" markup="none">
					<font fontName="SansSerif" size="12" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica-Bold" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
				</textElement>
				<text><![CDATA[Devoluções]]></text>
			</staticText>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-229" x="323" y="5" width="122" height="21"/>
				<textElement>
					<font size="14" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.Integer"><![CDATA[$F{qtdeTotalDevolucao}]]></textFieldExpression>
			</textField>
		</band>
		<band height="50">
			<staticText>
				<reportElement key="staticText-101" mode="Transparent" x="260" y="5" width="63" height="18" forecolor="#000000" backcolor="#FFFFFF"/>
				<textElement textAlignment="Left" verticalAlignment="Top" rotation="None" lineSpacing="Single" markup="none">
					<font fontName="SansSerif" size="12" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica-Bold" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
				</textElement>
				<text><![CDATA[Total:]]></text>
			</staticText>
			<line>
				<reportElement key="line-5" x="213" y="2" width="412" height="2"/>
			</line>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-229" x="323" y="5" width="122" height="21"/>
				<textElement>
					<font size="14" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.Integer"><![CDATA[$F{qtdeTotalFinal}]]></textFieldExpression>
			</textField>
			<textField pattern="  R$ #,##0.00" isBlankWhenNull="false">
				<reportElement key="textField-230" x="458" y="5" width="158" height="21"/>
				<textElement>
					<font size="14" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.Double"><![CDATA[$F{valorTotalFinal}]]></textFieldExpression>
			</textField>
		</band>
	</detail>
	<columnFooter>
		<band splitType="Stretch"/>
	</columnFooter>
	<pageFooter>
		<band splitType="Stretch"/>
	</pageFooter>
	<lastPageFooter>
		<band height="32" splitType="Stretch">
			<textField pattern="" isBlankWhenNull="false">
				<reportElement key="textField-207" x="1" y="8" width="621" height="13"/>
				<box>
					<topPen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
					<leftPen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
					<bottomPen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="8" isItalic="true" pdfFontName="Helvetica-Oblique"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[" "+" Usuário:" + $P{usuario}]]></textFieldExpression>
			</textField>
		</band>
	</lastPageFooter>
	<summary>
		<band splitType="Stretch"/>
	</summary>
</jasperReport>
