<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="FluxoCaixa" language="groovy" pageWidth="595" pageHeight="842" columnWidth="555" leftMargin="20" rightMargin="20" topMargin="20" bottomMargin="20">
	<property name="ireport.zoom" value="2.1435888100000025"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="0"/>
	<parameter name="tituloRelatorio" class="java.lang.String"/>
	<parameter name="logoPadraoRelatorio" class="java.io.InputStream"/>
	<parameter name="saldoInicial" class="java.lang.String"/>
	<parameter name="totaisEntradas" class="java.lang.String"/>
	<parameter name="totaisSaidas" class="java.lang.String"/>
	<parameter name="totaisTotal" class="java.lang.String"/>
	<parameter name="totaisSaldo" class="java.lang.String"/>
	<field name="diaAnoApresentar" class="java.lang.String"/>
	<field name="entrada_Apresentar" class="java.lang.String"/>
	<field name="saida_Apresentar" class="java.lang.String"/>
	<field name="total_Apresentar" class="java.lang.String"/>
	<field name="saldoRealizado_Apresentar" class="java.lang.String"/>
	<field name="saldo_Apresentar" class="java.lang.String"/>
	<variable name="VALIDAR_SALDO_EXIBIR" class="java.lang.String">
		<variableExpression><![CDATA[$F{saldoRealizado_Apresentar} == null ? $F{saldo_Apresentar} : $F{saldoRealizado_Apresentar}]]></variableExpression>
	</variable>
	<background>
		<band splitType="Stretch"/>
	</background>
	<title>
		<band height="43">
			<image isUsingCache="true" onErrorType="Blank" evaluationTime="Page">
				<reportElement x="1" y="2" width="82" height="36"/>
				<imageExpression class="java.io.InputStream"><![CDATA[$P{logoPadraoRelatorio}]]></imageExpression>
			</image>
			<textField>
				<reportElement x="82" y="2" width="471" height="36"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="14" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{tituloRelatorio}]]></textFieldExpression>
			</textField>
		</band>
	</title>
	<pageHeader>
		<band height="22" splitType="Stretch">
			<staticText>
				<reportElement x="412" y="0" width="64" height="14"/>
				<textElement>
					<font isBold="true"/>
				</textElement>
				<text><![CDATA[Saldo Inicial:]]></text>
			</staticText>
			<textField>
				<reportElement x="476" y="0" width="77" height="20"/>
				<textElement/>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{saldoInicial}]]></textFieldExpression>
			</textField>
		</band>
	</pageHeader>
	<columnHeader>
		<band height="19">
			<staticText>
				<reportElement mode="Opaque" x="98" y="0" width="122" height="19" backcolor="#999999"/>
				<textElement verticalAlignment="Middle">
					<font isBold="true"/>
				</textElement>
				<text><![CDATA[   Entradas]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="220" y="0" width="120" height="19" backcolor="#999999"/>
				<textElement verticalAlignment="Middle">
					<font isBold="true"/>
				</textElement>
				<text><![CDATA[   Saidas]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="340" y="0" width="114" height="19" backcolor="#999999"/>
				<textElement verticalAlignment="Middle">
					<font isBold="true"/>
				</textElement>
				<text><![CDATA[   Total]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="454" y="0" width="101" height="19" backcolor="#999999"/>
				<textElement verticalAlignment="Middle">
					<font isBold="true"/>
				</textElement>
				<text><![CDATA[   Saldo]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="0" y="0" width="98" height="19" backcolor="#999999"/>
				<textElement verticalAlignment="Middle">
					<font isBold="true"/>
				</textElement>
				<text><![CDATA[   Data]]></text>
			</staticText>
		</band>
	</columnHeader>
	<detail>
		<band height="17">
			<textField pattern="">
				<reportElement x="2" y="1" width="81" height="14"/>
				<textElement/>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{diaAnoApresentar}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="100" y="1" width="100" height="14"/>
				<textElement/>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{entrada_Apresentar}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="220" y="1" width="100" height="14"/>
				<textElement/>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{saida_Apresentar}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="339" y="1" width="100" height="14"/>
				<textElement/>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{total_Apresentar}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="455" y="1" width="100" height="14"/>
				<textElement/>
				<textFieldExpression class="java.lang.String"><![CDATA[$V{VALIDAR_SALDO_EXIBIR}]]></textFieldExpression>
			</textField>
		</band>
	</detail>
	<columnFooter>
		<band height="15" splitType="Stretch">
			<textField>
				<reportElement mode="Opaque" x="98" y="0" width="122" height="15" backcolor="#999999"/>
				<textElement verticalAlignment="Middle"/>
				<textFieldExpression class="java.lang.String"><![CDATA[  $P{totaisEntradas}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement mode="Opaque" x="220" y="0" width="119" height="15" backcolor="#999999"/>
				<textElement verticalAlignment="Middle"/>
				<textFieldExpression class="java.lang.String"><![CDATA[   $P{totaisSaidas}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement mode="Opaque" x="339" y="0" width="115" height="15" backcolor="#999999"/>
				<textElement verticalAlignment="Middle"/>
				<textFieldExpression class="java.lang.String"><![CDATA[   $P{totaisTotal}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement mode="Opaque" x="454" y="0" width="100" height="15" backcolor="#999999"/>
				<textElement verticalAlignment="Middle"/>
				<textFieldExpression class="java.lang.String"><![CDATA[   $P{totaisSaldo}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement mode="Opaque" x="0" y="0" width="98" height="15" backcolor="#999999"/>
				<textElement verticalAlignment="Middle">
					<font isBold="true"/>
				</textElement>
				<text><![CDATA[   Totais]]></text>
			</staticText>
		</band>
	</columnFooter>
</jasperReport>
