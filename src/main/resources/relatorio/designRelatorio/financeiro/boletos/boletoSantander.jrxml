<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="boleto" pageWidth="595" pageHeight="842" columnWidth="590" leftMargin="5" rightMargin="0" topMargin="0" bottomMargin="0">
	<property name="ireport.scriptlethandling" value="0"/>
	<property name="ireport.encoding" value="UTF-8"/>
	<property name="ireport.zoom" value="1.5"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="0"/>
	<import value="net.sf.jasperreports.engine.*"/>
	<import value="java.util.*"/>
	<import value="net.sf.jasperreports.engine.data.*"/>
	<parameter name="cnpjEmpresa" class="java.lang.String"/>
	<parameter name="enderecoEmpresa" class="java.lang.String"/>
	<parameter name="SUBREPORT_DIR" class="java.lang.String"/>
	<queryString>
		<![CDATA[]]>
	</queryString>
	<field name="banco.numeroFormatted" class="java.lang.String"/>
	<field name="banco.linhaDigitavel" class="java.lang.String"/>
	<field name="banco.codigoBarras" class="java.lang.String"/>
	<field name="boleto.cedente" class="java.lang.String"/>
	<field name="boleto.localPagamento" class="java.lang.String"/>
	<field name="boleto.dataDocumento" class="java.lang.String"/>
	<field name="boleto.especieDocumento" class="java.lang.String"/>
	<field name="boleto.aceite" class="java.lang.String"/>
	<field name="boleto.dataProcessamento" class="java.lang.String"/>
	<field name="boleto.carteira" class="java.lang.String"/>
	<field name="boleto.dataVencimento" class="java.lang.String"/>
	<field name="boleto.valorBoleto" class="java.lang.String"/>
	<field name="boleto.nomeSacado" class="java.lang.String"/>
	<field name="boleto.cpfSacado" class="java.lang.String"/>
	<field name="boleto.nossoNumero" class="java.lang.String"/>
	<field name="boleto.enderecoSacado" class="java.lang.String"/>
	<field name="boleto.cepSacado" class="java.lang.String"/>
	<field name="boleto.cidadeSacado" class="java.lang.String"/>
	<field name="boleto.ufSacado" class="java.lang.String"/>
	<field name="banco.agenciaCodCedenteFormatted" class="java.lang.String"/>
	<field name="boleto.instrucao1" class="java.lang.String"/>
	<field name="banco.banco" class="java.lang.String"/>
	<field name="banco.nossoNumeroFormatted" class="java.lang.String"/>
	<field name="boleto.dvNossoNumero" class="java.lang.String"/>
	<field name="boleto.noDocumento" class="java.lang.String"/>
	<field name="boleto.responsavel" class="java.lang.String">
		<fieldDescription><![CDATA[boleto.responsavel]]></fieldDescription>
	</field>
	<field name="boleto.bairroSacado" class="java.lang.String"/>
	<detail>
		<band height="387" splitType="Stretch">
			<line>
				<reportElement key="line-20" x="0" y="115" width="524" height="1"/>
				<graphicElement>
					<pen lineWidth="1.0" lineStyle="Dashed"/>
				</graphicElement>
			</line>
			<line>
				<reportElement key="line-1" x="1" y="135" width="522" height="1"/>
				<graphicElement>
					<pen lineWidth="1.0" lineStyle="Solid"/>
				</graphicElement>
			</line>
			<line>
				<reportElement key="line-2" x="120" y="118" width="1" height="17"/>
				<graphicElement>
					<pen lineWidth="1.0" lineStyle="Solid"/>
				</graphicElement>
			</line>
			<line>
				<reportElement key="line-3" x="154" y="118" width="1" height="17"/>
				<graphicElement>
					<pen lineWidth="1.0" lineStyle="Solid"/>
				</graphicElement>
			</line>
			<line>
				<reportElement key="line-4" x="0" y="152" width="524" height="1"/>
				<graphicElement>
					<pen lineWidth="1.0" lineStyle="Solid"/>
				</graphicElement>
			</line>
			<line>
				<reportElement key="line-5" x="0" y="169" width="524" height="1"/>
				<graphicElement>
					<pen lineWidth="1.0" lineStyle="Solid"/>
				</graphicElement>
			</line>
			<line>
				<reportElement key="line-6" x="0" y="186" width="524" height="1"/>
				<graphicElement>
					<pen lineWidth="1.0" lineStyle="Solid"/>
				</graphicElement>
			</line>
			<line>
				<reportElement key="line-7" x="0" y="203" width="524" height="1"/>
				<graphicElement>
					<pen lineWidth="1.0" lineStyle="Solid"/>
				</graphicElement>
			</line>
			<line>
				<reportElement key="line-8" x="377" y="135" width="1" height="153"/>
				<graphicElement>
					<pen lineWidth="1.0" lineStyle="Solid"/>
				</graphicElement>
			</line>
			<line>
				<reportElement key="line-9" x="0" y="288" width="524" height="1"/>
				<graphicElement>
					<pen lineWidth="1.0" lineStyle="Solid"/>
				</graphicElement>
			</line>
			<line>
				<reportElement key="line-10" x="378" y="220" width="146" height="1"/>
				<graphicElement>
					<pen lineWidth="1.0" lineStyle="Solid"/>
				</graphicElement>
			</line>
			<line>
				<reportElement key="line-11" x="378" y="237" width="146" height="1"/>
				<graphicElement>
					<pen lineWidth="1.0" lineStyle="Solid"/>
				</graphicElement>
			</line>
			<line>
				<reportElement key="line-12" x="378" y="254" width="146" height="1"/>
				<graphicElement>
					<pen lineWidth="1.0" lineStyle="Solid"/>
				</graphicElement>
			</line>
			<line>
				<reportElement key="line-13" x="377" y="271" width="147" height="1"/>
				<graphicElement>
					<pen lineWidth="1.0" lineStyle="Solid"/>
				</graphicElement>
			</line>
			<line>
				<reportElement key="line-14" x="76" y="169" width="1" height="34"/>
				<graphicElement>
					<pen lineWidth="1.0" lineStyle="Solid"/>
				</graphicElement>
			</line>
			<line>
				<reportElement key="line-15" x="152" y="169" width="1" height="34"/>
				<graphicElement>
					<pen lineWidth="1.0" lineStyle="Solid"/>
				</graphicElement>
			</line>
			<line>
				<reportElement key="line-16" x="124" y="186" width="1" height="17"/>
				<graphicElement>
					<pen lineWidth="1.0" lineStyle="Solid"/>
				</graphicElement>
			</line>
			<line>
				<reportElement key="line-17" x="237" y="169" width="1" height="34"/>
				<graphicElement>
					<pen lineWidth="1.0" lineStyle="Solid"/>
				</graphicElement>
			</line>
			<line>
				<reportElement key="line-18" x="209" y="169" width="1" height="17"/>
				<graphicElement>
					<pen lineWidth="1.0" lineStyle="Solid"/>
				</graphicElement>
			</line>
			<line>
				<reportElement key="line-19" x="0" y="331" width="524" height="1"/>
				<graphicElement>
					<pen lineWidth="1.0" lineStyle="Solid"/>
				</graphicElement>
			</line>
			<staticText>
				<reportElement key="staticText-1" x="1" y="135" width="66" height="8"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="6" isBold="false" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[Local de pagamento]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-2" x="1" y="152" width="66" height="8"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="6" isBold="false" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[Beneficiário]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-3" x="1" y="169" width="66" height="8"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="6" isBold="false" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[Data do Documento]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-4" x="1" y="186" width="75" height="8"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="6" isBold="false" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[Nº da Conta / Respons.]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-5" x="1" y="205" width="75" height="8"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="6" isBold="false" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[Instruções :]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-6" x="79" y="169" width="66" height="8"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="6" isBold="false" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[Nº do Documento]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-7" x="155" y="169" width="42" height="8"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="6" isBold="false" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[Espécie Doc.]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-8" x="212" y="169" width="22" height="8"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="6" isBold="false" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[Aceite]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-9" x="240" y="169" width="137" height="8"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="6" isBold="false" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[Data do Processamento]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-10" x="79" y="186" width="35" height="8"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="6" isBold="false" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[Carteira]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-11" x="126" y="186" width="24" height="8"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="6" isBold="false" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[Espécie]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-12" x="155" y="186" width="66" height="8"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="6" isBold="false" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[Quantidade]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-13" x="240" y="186" width="66" height="8"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="6" isBold="false" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[Valor]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-14" x="380" y="135" width="100" height="8"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="6" isBold="false" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[Vencimento]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-15" x="380" y="152" width="100" height="8"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="6" isBold="false" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[Agência / Código Beneficiário]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-16" x="380" y="271" width="100" height="8"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="6" isBold="false" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[(=) Valor cobrado]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-17" x="380" y="254" width="100" height="8"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="6" isBold="false" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[(=) Outros acréscimos]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-18" x="380" y="237" width="100" height="8"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="6" isBold="false" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[(+) Juros / Multa]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-19" x="380" y="220" width="100" height="8"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="6" isBold="false" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[(-) Outros deduções]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-20" x="380" y="203" width="100" height="8"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="6" isBold="false" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[(-) Desconto / Abatimento]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-21" x="380" y="186" width="100" height="8"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="6" isBold="false" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[(=) Valor do Documento]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-22" x="380" y="169" width="100" height="8"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="6" isBold="false" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[Nosso Número]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-23" x="1" y="290" width="33" height="8"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="6" isBold="false" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[Pagador:]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-24" x="1" y="320" width="65" height="8"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="6" isBold="false" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[Pagador / Avalista :]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-25" x="357" y="320" width="57" height="8"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="6" isBold="false" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[Código de baixa]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-26" x="332" y="333" width="68" height="8"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="6" isBold="false" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[Autenticação mecânica]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-27" x="410" y="333" width="106" height="11"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="9" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Ficha de Compensação]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-28" mode="Opaque" x="234" y="192" width="7" height="8"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="6" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[X]]></text>
			</staticText>
			<image scaleImage="FillFrame" hAlign="Left" vAlign="Top">
				<reportElement key="barcode-1" mode="Opaque" x="1" y="333" width="319" height="35" forecolor="#000000" backcolor="#FFFFFF"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<graphicElement fill="Solid">
					<pen lineWidth="0.0" lineStyle="Solid"/>
				</graphicElement>
				<imageExpression class="java.awt.Image"><![CDATA[it.businesslogic.ireport.barcode.BcImage.getBarcodeImage(13,$F{banco.codigoBarras},false,false,null,0,0)]]></imageExpression>
			</image>
			<line>
				<reportElement key="line-22" x="0" y="1" width="524" height="1"/>
				<graphicElement>
					<pen lineWidth="1.0" lineStyle="Dashed"/>
				</graphicElement>
			</line>
			<line>
				<reportElement key="line-23" x="262" y="4" width="1" height="109"/>
				<graphicElement>
					<pen lineWidth="1.0" lineStyle="Dashed"/>
				</graphicElement>
			</line>
			<line>
				<reportElement key="line-24" x="2" y="22" width="256" height="1"/>
				<graphicElement>
					<pen lineWidth="1.0" lineStyle="Solid"/>
				</graphicElement>
			</line>
			<line>
				<reportElement key="line-25" x="155" y="5" width="1" height="17"/>
				<graphicElement>
					<pen lineWidth="1.0" lineStyle="Solid"/>
				</graphicElement>
			</line>
			<line>
				<reportElement key="line-26" x="121" y="5" width="1" height="17"/>
				<graphicElement>
					<pen lineWidth="1.0" lineStyle="Solid"/>
				</graphicElement>
			</line>
			<staticText>
				<reportElement key="staticText-29" x="157" y="5" width="100" height="15"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Top">
					<font size="9" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Recibo do Pagador]]></text>
			</staticText>
			<line>
				<reportElement key="line-27" x="385" y="5" width="1" height="17"/>
				<graphicElement>
					<pen lineWidth="1.0" lineStyle="Solid"/>
				</graphicElement>
			</line>
			<line>
				<reportElement key="line-28" x="266" y="22" width="256" height="1"/>
				<graphicElement>
					<pen lineWidth="1.0" lineStyle="Solid"/>
				</graphicElement>
			</line>
			<line>
				<reportElement key="line-29" x="419" y="5" width="1" height="17"/>
				<graphicElement>
					<pen lineWidth="1.0" lineStyle="Solid"/>
				</graphicElement>
			</line>
			<staticText>
				<reportElement key="staticText-30" x="421" y="6" width="100" height="15"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Top">
					<font size="9" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Recibo de Entrega]]></text>
			</staticText>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement key="textField-35" x="123" y="5" width="30" height="15" isPrintWhenDetailOverflows="true"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Top">
					<font isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{banco.numeroFormatted}]]></textFieldExpression>
			</textField>
			<line>
				<reportElement key="line-30" x="155" y="22" width="1" height="17"/>
				<graphicElement>
					<pen lineWidth="1.0" lineStyle="Solid"/>
				</graphicElement>
			</line>
			<line>
				<reportElement key="line-31" x="50" y="22" width="1" height="17"/>
				<graphicElement>
					<pen lineWidth="1.0" lineStyle="Solid"/>
				</graphicElement>
			</line>
			<line>
				<reportElement key="line-32" x="2" y="39" width="256" height="1"/>
				<graphicElement>
					<pen lineWidth="1.0" lineStyle="Solid"/>
				</graphicElement>
			</line>
			<line>
				<reportElement key="line-33" x="207" y="22" width="1" height="17"/>
				<graphicElement>
					<pen lineWidth="1.0" lineStyle="Solid"/>
				</graphicElement>
			</line>
			<staticText>
				<reportElement key="staticText-31" x="2" y="22" width="46" height="8"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="6" isBold="false" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[Vencimento]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-32" x="53" y="22" width="100" height="8"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="6" isBold="false" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[Agência / Código Beneficiário]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-33" x="158" y="22" width="24" height="8"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="6" isBold="false" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[Espécie]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-34" x="210" y="22" width="47" height="8"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="6" isBold="false" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[Quantidade]]></text>
			</staticText>
			<line>
				<reportElement key="line-34" x="266" y="22" width="256" height="1"/>
				<graphicElement>
					<pen lineWidth="1.0" lineStyle="Solid"/>
				</graphicElement>
			</line>
			<line>
				<reportElement key="line-35" x="419" y="22" width="1" height="17"/>
				<graphicElement>
					<pen lineWidth="1.0" lineStyle="Solid"/>
				</graphicElement>
			</line>
			<line>
				<reportElement key="line-36" x="314" y="22" width="1" height="17"/>
				<graphicElement>
					<pen lineWidth="1.0" lineStyle="Solid"/>
				</graphicElement>
			</line>
			<line>
				<reportElement key="line-37" x="266" y="39" width="256" height="1"/>
				<graphicElement>
					<pen lineWidth="1.0" lineStyle="Solid"/>
				</graphicElement>
			</line>
			<line>
				<reportElement key="line-38" x="471" y="22" width="1" height="17"/>
				<graphicElement>
					<pen lineWidth="1.0" lineStyle="Solid"/>
				</graphicElement>
			</line>
			<staticText>
				<reportElement key="staticText-35" x="266" y="22" width="46" height="8"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="6" isBold="false" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[Vencimento]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-36" x="317" y="22" width="100" height="8"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="6" isBold="false" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[Agência / Código Beneficiário]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-37" x="422" y="22" width="24" height="8"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="6" isBold="false" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[Espécie]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-38" x="474" y="22" width="47" height="8"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="6" isBold="false" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[Quantidade]]></text>
			</staticText>
			<line>
				<reportElement key="line-39" x="2" y="56" width="256" height="1"/>
				<graphicElement>
					<pen lineWidth="1.0" lineStyle="Solid"/>
				</graphicElement>
			</line>
			<line>
				<reportElement key="line-40" x="87" y="39" width="1" height="17"/>
				<graphicElement>
					<pen lineWidth="1.0" lineStyle="Solid"/>
				</graphicElement>
			</line>
			<line>
				<reportElement key="line-41" x="172" y="39" width="1" height="17"/>
				<graphicElement>
					<pen lineWidth="1.0" lineStyle="Solid"/>
				</graphicElement>
			</line>
			<staticText>
				<reportElement key="staticText-39" x="2" y="39" width="82" height="8"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="6" isBold="false" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[(=) Valor do Documento]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-40" x="90" y="39" width="80" height="8"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="6" isBold="false" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[(-) Desconto / Abatimento]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-41" x="175" y="39" width="82" height="8"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="6" isBold="false" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[(+) Juros / Multa]]></text>
			</staticText>
			<line>
				<reportElement key="line-42" x="87" y="56" width="1" height="17"/>
				<graphicElement>
					<pen lineWidth="1.0" lineStyle="Solid"/>
				</graphicElement>
			</line>
			<line>
				<reportElement key="line-43" x="2" y="73" width="256" height="1"/>
				<graphicElement>
					<pen lineWidth="1.0" lineStyle="Solid"/>
				</graphicElement>
			</line>
			<line>
				<reportElement key="line-44" x="172" y="56" width="1" height="17"/>
				<graphicElement>
					<pen lineWidth="1.0" lineStyle="Solid"/>
				</graphicElement>
			</line>
			<staticText>
				<reportElement key="staticText-42" x="2" y="56" width="82" height="8"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="6" isBold="false" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[(=) Valor cobrado]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-43" x="90" y="56" width="80" height="8"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="6" isBold="false" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[Nosso Número]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-44" x="175" y="56" width="66" height="8"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="6" isBold="false" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[Nº do Documento]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-45" x="266" y="39" width="82" height="8"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="6" isBold="false" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[(=) Valor do Documento]]></text>
			</staticText>
			<line>
				<reportElement key="line-45" x="351" y="39" width="1" height="17"/>
				<graphicElement>
					<pen lineWidth="1.0" lineStyle="Solid"/>
				</graphicElement>
			</line>
			<line>
				<reportElement key="line-46" x="266" y="56" width="256" height="1"/>
				<graphicElement>
					<pen lineWidth="1.0" lineStyle="Solid"/>
				</graphicElement>
			</line>
			<line>
				<reportElement key="line-47" x="1" y="85" width="256" height="1"/>
				<graphicElement>
					<pen lineWidth="1.0" lineStyle="Solid"/>
				</graphicElement>
			</line>
			<staticText>
				<reportElement key="staticText-46" x="2" y="73" width="88" height="6"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="5" isBold="false" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[Beneficiário/CPF/CNPJ/Endereço]]></text>
			</staticText>
			<line>
				<reportElement key="line-48" x="266" y="73" width="256" height="1"/>
				<graphicElement>
					<pen lineWidth="1.0" lineStyle="Solid"/>
				</graphicElement>
			</line>
			<staticText>
				<reportElement key="staticText-47" x="266" y="56" width="33" height="8"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="6" isBold="false" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[Pagador]]></text>
			</staticText>
			<line>
				<reportElement key="line-49" x="266" y="99" width="256" height="1"/>
				<graphicElement>
					<pen lineWidth="1.0" lineStyle="Solid"/>
				</graphicElement>
			</line>
			<staticText>
				<reportElement key="staticText-48" x="266" y="73" width="81" height="8"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="6" isBold="false" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[Assinatura do Recebedor]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-49" x="96" y="100" width="68" height="8"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Top">
					<font fontName="SansSerif" size="5" isBold="false" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[Autenticação mecânica]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-50" x="363" y="39" width="80" height="8"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="6" isBold="false" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[Nosso Número]]></text>
			</staticText>
			<line>
				<reportElement key="line-50" x="0" y="386" width="524" height="1"/>
				<graphicElement>
					<pen lineWidth="1.0" lineStyle="Dashed"/>
				</graphicElement>
			</line>
			<line>
				<reportElement key="line-51" x="1" y="99" width="256" height="1"/>
				<graphicElement>
					<pen lineWidth="1.0" lineStyle="Solid"/>
				</graphicElement>
			</line>
			<staticText>
				<reportElement key="staticText-51" x="2" y="86" width="33" height="6"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="5" isBold="false" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[Pagador]]></text>
			</staticText>
			<textField isBlankWhenNull="true">
				<reportElement key="textField-35" x="387" y="5" width="30" height="15"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Top">
					<font isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{banco.numeroFormatted}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement key="textField-35" x="123" y="118" width="30" height="15" isPrintWhenDetailOverflows="true"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Top">
					<font isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{banco.numeroFormatted}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="156" y="118" width="368" height="15"/>
				<textElement textAlignment="Center">
					<font isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{banco.linhaDigitavel}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="2" y="76" width="257" height="9"/>
				<textElement verticalAlignment="Bottom">
					<font fontName="Arial" size="4"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{boleto.cedente} + " / " + $P{cnpjEmpresa} + " / " + $P{enderecoEmpresa}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="5" y="160" width="372" height="9"/>
				<textElement verticalAlignment="Bottom">
					<font fontName="Arial" size="6"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{boleto.cedente} + " / " + $P{cnpjEmpresa} + " / " + $P{enderecoEmpresa}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="5" y="143" width="372" height="9"/>
				<textElement verticalAlignment="Bottom">
					<font fontName="Arial" size="7"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{boleto.localPagamento}]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement x="5" y="177" width="66" height="9"/>
				<textElement verticalAlignment="Bottom">
					<font fontName="Arial" size="7"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{boleto.dataDocumento}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="155" y="177" width="51" height="9"/>
				<textElement textAlignment="Center" verticalAlignment="Bottom">
					<font fontName="Arial" size="7"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{boleto.especieDocumento}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="211" y="177" width="24" height="9"/>
				<textElement textAlignment="Center" verticalAlignment="Bottom">
					<font fontName="Arial" size="7"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{boleto.aceite}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="240" y="177" width="137" height="9"/>
				<textElement verticalAlignment="Bottom">
					<font fontName="Arial" size="7"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{boleto.dataProcessamento}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="79" y="194" width="40" height="9"/>
				<textElement textAlignment="Center" verticalAlignment="Bottom">
					<font fontName="Arial" size="7"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{boleto.carteira}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="434" y="143" width="80" height="9"/>
				<textElement textAlignment="Right" verticalAlignment="Bottom">
					<font fontName="Arial" size="7"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{boleto.dataVencimento}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="434" y="194" width="80" height="9"/>
				<textElement textAlignment="Right" verticalAlignment="Bottom">
					<font fontName="Arial" size="7"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{boleto.valorBoleto}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="37" y="289" width="477" height="9"/>
				<textElement verticalAlignment="Bottom">
					<font fontName="Arial" size="7"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[(($F{boleto.responsavel} != null &&  !$F{boleto.responsavel}.isEmpty()) ? $F{boleto.responsavel} :  $F{boleto.nomeSacado}) + " CPF/CNPJ: " + $F{boleto.cpfSacado}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="2" y="30" width="46" height="9"/>
				<textElement textAlignment="Left" verticalAlignment="Bottom">
					<font fontName="Arial" size="7"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{boleto.dataVencimento}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="266" y="30" width="46" height="9"/>
				<textElement textAlignment="Left" verticalAlignment="Bottom">
					<font fontName="Arial" size="7"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{boleto.dataVencimento}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="2" y="47" width="74" height="9"/>
				<textElement textAlignment="Right" verticalAlignment="Bottom">
					<font fontName="Arial" size="7"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{boleto.valorBoleto}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="266" y="47" width="74" height="9"/>
				<textElement textAlignment="Right" verticalAlignment="Bottom">
					<font fontName="Arial" size="7"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{boleto.valorBoleto}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="266" y="64" width="256" height="9"/>
				<textElement verticalAlignment="Bottom">
					<font fontName="Arial" size="7"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{boleto.nomeSacado}]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement key="textField-50" x="90" y="63" width="79" height="9"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Bottom">
					<font fontName="Arial" size="7"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{banco.nossoNumeroFormatted}]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement key="textField-51" x="363" y="45" width="159" height="9"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Bottom">
					<font fontName="Arial" size="7"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{banco.nossoNumeroFormatted}]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement key="textField-49" x="386" y="176" width="128" height="9"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Bottom">
					<font fontName="Arial" size="7"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{boleto.nossoNumero}+($F{boleto.dvNossoNumero}.isEmpty() ? "" : ("-" + $F{boleto.dvNossoNumero}))]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="37" y="298" width="477" height="9"/>
				<textElement verticalAlignment="Bottom">
					<font fontName="Arial" size="7"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{boleto.enderecoSacado}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="37" y="307" width="42" height="9"/>
				<textElement verticalAlignment="Bottom">
					<font fontName="Arial" size="7"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[($F{boleto.cepSacado}.length()<=5?$F{boleto.cepSacado}+"-000":$F{boleto.cepSacado}.substring(0,5)+"-"+$F{boleto.cepSacado}.substring(5))]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="81" y="307" width="433" height="9"/>
				<textElement verticalAlignment="Bottom">
					<font fontName="Arial" size="7"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[($F{boleto.bairroSacado}==null?"":$F{boleto.bairroSacado}.trim() + "  " ) +
($F{boleto.cidadeSacado}==null?"":$F{boleto.cidadeSacado}.trim() + "  " ) +
($F{boleto.ufSacado}==null? "" : ( $F{boleto.ufSacado}))]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="434" y="160" width="80" height="9"/>
				<textElement textAlignment="Right" verticalAlignment="Bottom">
					<font fontName="Arial" size="7"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{banco.agenciaCodCedenteFormatted}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="2" y="91" width="256" height="8"/>
				<textElement verticalAlignment="Bottom">
					<font fontName="Arial" size="6"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{boleto.nomeSacado}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement key="textField-24" x="5" y="213" width="368" height="70"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Top">
					<font fontName="Arial" size="7"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{boleto.instrucao1}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="266" y="6" width="119" height="16"/>
				<textElement verticalAlignment="Top">
					<font fontName="Arial"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{banco.banco}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="130" y="194" width="15" height="9"/>
				<textElement textAlignment="Center" verticalAlignment="Bottom">
					<font fontName="Arial" size="7"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA["R$"]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="78" y="177" width="67" height="9"/>
				<textElement textAlignment="Center" verticalAlignment="Bottom">
					<font fontName="Arial" size="7"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{boleto.noDocumento}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="317" y="30" width="90" height="9"/>
				<textElement textAlignment="Right" verticalAlignment="Bottom">
					<font fontName="Arial" size="7"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{banco.agenciaCodCedenteFormatted}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="53" y="30" width="90" height="9"/>
				<textElement textAlignment="Right" verticalAlignment="Bottom">
					<font fontName="Arial" size="7"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{banco.agenciaCodCedenteFormatted}]]></textFieldExpression>
			</textField>
			<image scaleImage="FillFrame">
				<reportElement x="5" y="3" width="72" height="17"/>
				<imageExpression class="java.lang.String"><![CDATA[$P{SUBREPORT_DIR}  + "logos/logo-santander.gif"]]></imageExpression>
			</image>
			<image scaleImage="FillFrame">
				<reportElement x="5" y="117" width="72" height="17"/>
				<imageExpression class="java.lang.String"><![CDATA[$P{SUBREPORT_DIR}  + "logos/logo-santander.gif"]]></imageExpression>
			</image>
		</band>
	</detail>
</jasperReport>
