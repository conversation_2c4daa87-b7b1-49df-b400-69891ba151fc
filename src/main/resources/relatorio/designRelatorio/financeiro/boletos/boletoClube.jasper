¬í sr (net.sf.jasperreports.engine.JasperReport      'Ø L compileDatat Ljava/io/Serializable;L compileNameSuffixt Ljava/lang/String;L 
compilerClassq ~ xr -net.sf.jasperreports.engine.base.JRBaseReport      'Ø ,I PSEUDO_SERIAL_VERSION_UIDI bottomMarginI columnCountI 
columnSpacingI columnWidthZ ignorePaginationZ isFloatColumnFooterZ isSummaryNewPageZ  isSummaryWithPageHeaderAndFooterZ isTitleNewPageI 
leftMarginB orientationI 
pageHeightI 	pageWidthB 
printOrderI rightMarginI 	topMarginB whenNoDataTypeL 
backgroundt $Lnet/sf/jasperreports/engine/JRBand;L columnDirectiont 3Lnet/sf/jasperreports/engine/type/RunDirectionEnum;L columnFooterq ~ L columnHeaderq ~ [ datasetst ([Lnet/sf/jasperreports/engine/JRDataset;L defaultFontt *Lnet/sf/jasperreports/engine/JRReportFont;L defaultStylet %Lnet/sf/jasperreports/engine/JRStyle;L detailq ~ L 
detailSectiont 'Lnet/sf/jasperreports/engine/JRSection;[ fontst +[Lnet/sf/jasperreports/engine/JRReportFont;L formatFactoryClassq ~ L 
importsSett Ljava/util/Set;L languageq ~ L lastPageFooterq ~ L mainDatasett 'Lnet/sf/jasperreports/engine/JRDataset;L nameq ~ L noDataq ~ L orientationValuet 2Lnet/sf/jasperreports/engine/type/OrientationEnum;L 
pageFooterq ~ L 
pageHeaderq ~ L printOrderValuet 1Lnet/sf/jasperreports/engine/type/PrintOrderEnum;[ stylest &[Lnet/sf/jasperreports/engine/JRStyle;L summaryq ~ [ 	templatest /[Lnet/sf/jasperreports/engine/JRReportTemplate;L titleq ~ L whenNoDataTypeValuet 5Lnet/sf/jasperreports/engine/type/WhenNoDataTypeEnum;xp  wñ                                     p~r 1net.sf.jasperreports.engine.type.RunDirectionEnum          xr java.lang.Enum          xpt LTRppppppsr .net.sf.jasperreports.engine.base.JRBaseSection      'Ø [ bandst %[Lnet/sf/jasperreports/engine/JRBand;xpur %[Lnet.sf.jasperreports.engine.JRBand;Ý~ìÊ5  xp   sr +net.sf.jasperreports.engine.base.JRBaseBand      'Ø I PSEUDO_SERIAL_VERSION_UIDI heightZ isSplitAllowedL printWhenExpressiont *Lnet/sf/jasperreports/engine/JRExpression;L 	splitTypet Ljava/lang/Byte;L splitTypeValuet 0Lnet/sf/jasperreports/engine/type/SplitTypeEnum;xr 3net.sf.jasperreports.engine.base.JRBaseElementGroup      'Ø L childrent Ljava/util/List;L elementGroupt ,Lnet/sf/jasperreports/engine/JRElementGroup;xpsr java.util.ArrayListxÒÇa I sizexp   Ww   Wsr +net.sf.jasperreports.engine.base.JRBaseLine      'Ø I PSEUDO_SERIAL_VERSION_UIDB 	directionL directionValuet 4Lnet/sf/jasperreports/engine/type/LineDirectionEnum;xr 5net.sf.jasperreports.engine.base.JRBaseGraphicElement      'Ø I PSEUDO_SERIAL_VERSION_UIDL fillq ~ L 	fillValuet +Lnet/sf/jasperreports/engine/type/FillEnum;L linePent #Lnet/sf/jasperreports/engine/JRPen;L penq ~ xr .net.sf.jasperreports.engine.base.JRBaseElement      'Ø I PSEUDO_SERIAL_VERSION_UIDI heightZ isPrintInFirstWholeBandZ isPrintRepeatedValuesZ isPrintWhenDetailOverflowsZ isRemoveLineWhenBlankB positionTypeB stretchTypeI widthI xI yL 	backcolort Ljava/awt/Color;L defaultStyleProvidert 4Lnet/sf/jasperreports/engine/JRDefaultStyleProvider;L elementGroupq ~ "L 	forecolorq ~ ,L keyq ~ L modeq ~ L 	modeValuet +Lnet/sf/jasperreports/engine/type/ModeEnum;L parentStyleq ~ L parentStyleNameReferenceq ~ L positionTypeValuet 3Lnet/sf/jasperreports/engine/type/PositionTypeEnum;L printWhenExpressionq ~ L printWhenGroupChangest %Lnet/sf/jasperreports/engine/JRGroup;L 
propertiesMapt -Lnet/sf/jasperreports/engine/JRPropertiesMap;[ propertyExpressionst 3[Lnet/sf/jasperreports/engine/JRPropertyExpression;L stretchTypeValuet 2Lnet/sf/jasperreports/engine/type/StretchTypeEnum;xp  wñ                pq ~ q ~ #pt line-20pppp~r 1net.sf.jasperreports.engine.type.PositionTypeEnum          xq ~ t FIX_RELATIVE_TO_TOPpppp~r 0net.sf.jasperreports.engine.type.StretchTypeEnum          xq ~ t 
NO_STRETCH  wîppsr *net.sf.jasperreports.engine.base.JRBasePen      'Ø I PSEUDO_SERIAL_VERSION_UIDL 	lineColorq ~ ,L 	lineStyleq ~ L lineStyleValuet 0Lnet/sf/jasperreports/engine/type/LineStyleEnum;L 	lineWidtht Ljava/lang/Float;L penContainert ,Lnet/sf/jasperreports/engine/JRPenContainer;xp  wñpp~r .net.sf.jasperreports.engine.type.LineStyleEnum          xq ~ t DASHEDsr java.lang.FloatÚíÉ¢Û<ðì F valuexr java.lang.Number¬à  xp?  q ~ 4p  wñ ~r 2net.sf.jasperreports.engine.type.LineDirectionEnum          xq ~ t TOP_DOWNsq ~ &  wñ          
     pq ~ q ~ #pt line-1ppppq ~ 7ppppq ~ :  wîppsq ~ <  wñpp~q ~ At SOLIDsq ~ D?  q ~ Jp  wñ q ~ Hsq ~ &  wñ              x  pq ~ q ~ #pt line-2ppppq ~ 7ppppq ~ :  wîppsq ~ <  wñppq ~ Msq ~ D?  q ~ Pp  wñ q ~ Hsq ~ &  wñ                pq ~ q ~ #pt line-3ppppq ~ 7ppppq ~ :  wîppsq ~ <  wñppq ~ Msq ~ D?  q ~ Tp  wñ q ~ Hsq ~ &  wñ                'pq ~ q ~ #pt line-4ppppq ~ 7ppppq ~ :  wîppsq ~ <  wñppq ~ Msq ~ D?  q ~ Xp  wñ q ~ Hsq ~ &  wñ                8pq ~ q ~ #pt line-5ppppq ~ 7ppppq ~ :  wîppsq ~ <  wñppq ~ Msq ~ D?  q ~ \p  wñ q ~ Hsq ~ &  wñ                Ipq ~ q ~ #pt line-6ppppq ~ 7ppppq ~ :  wîppsq ~ <  wñppq ~ Msq ~ D?  q ~ `p  wñ q ~ Hsq ~ &  wñ                Zpq ~ q ~ #pt line-7ppppq ~ 7ppppq ~ :  wîppsq ~ <  wñppq ~ Msq ~ D?  q ~ dp  wñ q ~ Hsq ~ &  wñ             b  pq ~ q ~ #pt line-8ppppq ~ 7ppppq ~ :  wîppsq ~ <  wñppq ~ Msq ~ D?  q ~ hp  wñ q ~ Hsq ~ &  wñ                ¯pq ~ q ~ #pt line-9ppppq ~ 7ppppq ~ :  wîppsq ~ <  wñppq ~ Msq ~ D?  q ~ lp  wñ q ~ Hsq ~ &  wñ           ª  b  kpq ~ q ~ #pt line-10ppppq ~ 7ppppq ~ :  wîppsq ~ <  wñppq ~ Msq ~ D?  q ~ pp  wñ q ~ Hsq ~ &  wñ           ª  b  |pq ~ q ~ #pt line-11ppppq ~ 7ppppq ~ :  wîppsq ~ <  wñppq ~ Msq ~ D?  q ~ tp  wñ q ~ Hsq ~ &  wñ           ª  b  pq ~ q ~ #pt line-12ppppq ~ 7ppppq ~ :  wîppsq ~ <  wñppq ~ Msq ~ D?  q ~ xp  wñ q ~ Hsq ~ &  wñ           ª  b  pq ~ q ~ #pt line-13ppppq ~ 7ppppq ~ :  wîppsq ~ <  wñppq ~ Msq ~ D?  q ~ |p  wñ q ~ Hsq ~ &  wñ   "           L  8pq ~ q ~ #pt line-14ppppq ~ 7ppppq ~ :  wîppsq ~ <  wñppq ~ Msq ~ D?  q ~ p  wñ q ~ Hsq ~ &  wñ   "             8pq ~ q ~ #pt line-15ppppq ~ 7ppppq ~ :  wîppsq ~ <  wñppq ~ Msq ~ D?  q ~ p  wñ q ~ Hsq ~ &  wñ              |  Ipq ~ q ~ #pt line-16ppppq ~ 7ppppq ~ :  wîppsq ~ <  wñppq ~ Msq ~ D?  q ~ p  wñ q ~ Hsq ~ &  wñ   "           í  8pq ~ q ~ #pt line-17ppppq ~ 7ppppq ~ :  wîppsq ~ <  wñppq ~ Msq ~ D?  q ~ p  wñ q ~ Hsq ~ &  wñ              Ñ  8pq ~ q ~ #pt line-18ppppq ~ 7ppppq ~ :  wîppsq ~ <  wñppq ~ Msq ~ D?  q ~ p  wñ q ~ Hsq ~ &  wñ                Úpq ~ q ~ #pt line-19ppppq ~ 7ppppq ~ :  wîppsq ~ <  wñppq ~ Msq ~ D?  q ~ p  wñ q ~ Hsr 1net.sf.jasperreports.engine.base.JRBaseStaticText      'Ø L textq ~ xr 2net.sf.jasperreports.engine.base.JRBaseTextElement      'Ø %I PSEUDO_SERIAL_VERSION_UIDL borderq ~ L borderColorq ~ ,L bottomBorderq ~ L bottomBorderColorq ~ ,L 
bottomPaddingt Ljava/lang/Integer;L fontNameq ~ L fontSizeq ~ L horizontalAlignmentq ~ L horizontalAlignmentValuet 6Lnet/sf/jasperreports/engine/type/HorizontalAlignEnum;L isBoldt Ljava/lang/Boolean;L isItalicq ~ L 
isPdfEmbeddedq ~ L isStrikeThroughq ~ L isStyledTextq ~ L isUnderlineq ~ L 
leftBorderq ~ L leftBorderColorq ~ ,L leftPaddingq ~ L lineBoxt 'Lnet/sf/jasperreports/engine/JRLineBox;L lineSpacingq ~ L lineSpacingValuet 2Lnet/sf/jasperreports/engine/type/LineSpacingEnum;L markupq ~ L paddingq ~ L pdfEncodingq ~ L pdfFontNameq ~ L 
reportFontq ~ L rightBorderq ~ L rightBorderColorq ~ ,L rightPaddingq ~ L rotationq ~ L 
rotationValuet /Lnet/sf/jasperreports/engine/type/RotationEnum;L 	topBorderq ~ L topBorderColorq ~ ,L 
topPaddingq ~ L verticalAlignmentq ~ L verticalAlignmentValuet 4Lnet/sf/jasperreports/engine/type/VerticalAlignEnum;xq ~ +  wñ           B     pq ~ q ~ #pt staticText-1ppppq ~ 7ppppq ~ :  wñpppppt Arialsr java.lang.Integerâ ¤÷8 I valuexq ~ E   ppsr java.lang.BooleanÍ rÕúî Z valuexp ppppppppsr .net.sf.jasperreports.engine.base.JRBaseLineBox      'Ø L 
bottomPaddingq ~ L 	bottomPent +Lnet/sf/jasperreports/engine/base/JRBoxPen;L boxContainert ,Lnet/sf/jasperreports/engine/JRBoxContainer;L leftPaddingq ~ L leftPenq ~ ©L paddingq ~ L penq ~ ©L rightPaddingq ~ L rightPenq ~ ©L 
topPaddingq ~ L topPenq ~ ©xppsr 3net.sf.jasperreports.engine.base.JRBaseBoxBottomPen      'Ø  xr -net.sf.jasperreports.engine.base.JRBaseBoxPen      'Ø L lineBoxq ~ xq ~ <  wñsr java.awt.Color¥3u F falphaI valueL cst Ljava/awt/color/ColorSpace;[ 	frgbvaluet [F[ fvalueq ~ ±xp    ÿ   ppppq ~ Msq ~ D    q ~ «q ~ «q ~ ¡psr 1net.sf.jasperreports.engine.base.JRBaseBoxLeftPen      'Ø  xq ~ ­  wñsq ~ ¯    ÿ   ppppq ~ Msq ~ D    q ~ «q ~ «psq ~ ­  wñppppq ~ «q ~ «psr 2net.sf.jasperreports.engine.base.JRBaseBoxRightPen      'Ø  xq ~ ­  wñsq ~ ¯    ÿ   ppppq ~ Msq ~ D    q ~ «q ~ «psr 0net.sf.jasperreports.engine.base.JRBaseBoxTopPen      'Ø  xq ~ ­  wñsq ~ ¯    ÿ   ppppq ~ Msq ~ D    q ~ «q ~ «pppppt 	Helveticapppppppppp~r 2net.sf.jasperreports.engine.type.VerticalAlignEnum          xq ~ t MIDDLEt Local de pagamentosq ~   wñ           B     'pq ~ q ~ #pt staticText-2ppppq ~ 7ppppq ~ :  wñpppppt Arialq ~ ¥ppq ~ §ppppppppsq ~ ¨psq ~ ¬  wñsq ~ ¯    ÿ   ppppq ~ Msq ~ D    q ~ Éq ~ Éq ~ Æpsq ~ ´  wñsq ~ ¯    ÿ   ppppq ~ Msq ~ D    q ~ Éq ~ Épsq ~ ­  wñppppq ~ Éq ~ Épsq ~ ¹  wñsq ~ ¯    ÿ   ppppq ~ Msq ~ D    q ~ Éq ~ Épsq ~ ½  wñsq ~ ¯    ÿ   ppppq ~ Msq ~ D    q ~ Éq ~ Épppppt 	Helveticappppppppppq ~ Ãt Cedentesq ~   wñ           B     8pq ~ q ~ #pt staticText-3ppppq ~ 7ppppq ~ :  wñpppppt Arialq ~ ¥ppq ~ §ppppppppsq ~ ¨psq ~ ¬  wñsq ~ ¯    ÿ   ppppq ~ Msq ~ D    q ~ Üq ~ Üq ~ Ùpsq ~ ´  wñsq ~ ¯    ÿ   ppppq ~ Msq ~ D    q ~ Üq ~ Üpsq ~ ­  wñppppq ~ Üq ~ Üpsq ~ ¹  wñsq ~ ¯    ÿ   ppppq ~ Msq ~ D    q ~ Üq ~ Üpsq ~ ½  wñsq ~ ¯    ÿ   ppppq ~ Msq ~ D    q ~ Üq ~ Üpppppt 	Helveticappppppppppq ~ Ãt Data do Documentosq ~   wñ           K     Ipq ~ q ~ #pt staticText-4ppppq ~ 7ppppq ~ :  wñpppppt Arialq ~ ¥ppq ~ §ppppppppsq ~ ¨psq ~ ¬  wñsq ~ ¯    ÿ   ppppq ~ Msq ~ D    q ~ ïq ~ ïq ~ ìpsq ~ ´  wñsq ~ ¯    ÿ   ppppq ~ Msq ~ D    q ~ ïq ~ ïpsq ~ ­  wñppppq ~ ïq ~ ïpsq ~ ¹  wñsq ~ ¯    ÿ   ppppq ~ Msq ~ D    q ~ ïq ~ ïpsq ~ ½  wñsq ~ ¯    ÿ   ppppq ~ Msq ~ D    q ~ ïq ~ ïpppppt 	Helveticappppppppppq ~ Ãt NÂº da Conta / Respons.sq ~   wñ           K     \pq ~ q ~ #pt staticText-5ppppq ~ 7ppppq ~ :  wñpppppt Arialq ~ ¥ppq ~ §ppppppppsq ~ ¨psq ~ ¬  wñsq ~ ¯    ÿ   ppppq ~ Msq ~ D    q ~q ~q ~ ÿpsq ~ ´  wñsq ~ ¯    ÿ   ppppq ~ Msq ~ D    q ~q ~psq ~ ­  wñppppq ~q ~psq ~ ¹  wñsq ~ ¯    ÿ   ppppq ~ Msq ~ D    q ~q ~psq ~ ½  wñsq ~ ¯    ÿ   ppppq ~ Msq ~ D    q ~q ~pppppt 	Helveticappppppppppq ~ Ãt InstruÃ§Ãµes :sq ~   wñ           B   O  8pq ~ q ~ #pt staticText-6ppppq ~ 7ppppq ~ :  wñpppppt Arialq ~ ¥ppq ~ §ppppppppsq ~ ¨psq ~ ¬  wñsq ~ ¯    ÿ   ppppq ~ Msq ~ D    q ~q ~q ~psq ~ ´  wñsq ~ ¯    ÿ   ppppq ~ Msq ~ D    q ~q ~psq ~ ­  wñppppq ~q ~psq ~ ¹  wñsq ~ ¯    ÿ   ppppq ~ Msq ~ D    q ~q ~psq ~ ½  wñsq ~ ¯    ÿ   ppppq ~ Msq ~ D    q ~q ~pppppt 	Helveticappppppppppq ~ Ãt NÂº do Documentosq ~   wñ           *     8pq ~ q ~ #pt staticText-7ppppq ~ 7ppppq ~ :  wñpppppt Arialq ~ ¥ppq ~ §ppppppppsq ~ ¨psq ~ ¬  wñsq ~ ¯    ÿ   ppppq ~ Msq ~ D    q ~(q ~(q ~%psq ~ ´  wñsq ~ ¯    ÿ   ppppq ~ Msq ~ D    q ~(q ~(psq ~ ­  wñppppq ~(q ~(psq ~ ¹  wñsq ~ ¯    ÿ   ppppq ~ Msq ~ D    q ~(q ~(psq ~ ½  wñsq ~ ¯    ÿ   ppppq ~ Msq ~ D    q ~(q ~(pppppt 	Helveticappppppppppq ~ Ãt 
EspÃ©cie Doc.sq ~   wñ              Ô  8pq ~ q ~ #pt staticText-8ppppq ~ 7ppppq ~ :  wñpppppt Arialq ~ ¥ppq ~ §ppppppppsq ~ ¨psq ~ ¬  wñsq ~ ¯    ÿ   ppppq ~ Msq ~ D    q ~;q ~;q ~8psq ~ ´  wñsq ~ ¯    ÿ   ppppq ~ Msq ~ D    q ~;q ~;psq ~ ­  wñppppq ~;q ~;psq ~ ¹  wñsq ~ ¯    ÿ   ppppq ~ Msq ~ D    q ~;q ~;psq ~ ½  wñsq ~ ¯    ÿ   ppppq ~ Msq ~ D    q ~;q ~;pppppt 	Helveticappppppppppq ~ Ãt Aceitesq ~   wñ           X   ð  8pq ~ q ~ #pt staticText-9ppppq ~ 7ppppq ~ :  wñpppppt Arialq ~ ¥ppq ~ §ppppppppsq ~ ¨psq ~ ¬  wñsq ~ ¯    ÿ   ppppq ~ Msq ~ D    q ~Nq ~Nq ~Kpsq ~ ´  wñsq ~ ¯    ÿ   ppppq ~ Msq ~ D    q ~Nq ~Npsq ~ ­  wñppppq ~Nq ~Npsq ~ ¹  wñsq ~ ¯    ÿ   ppppq ~ Msq ~ D    q ~Nq ~Npsq ~ ½  wñsq ~ ¯    ÿ   ppppq ~ Msq ~ D    q ~Nq ~Npppppt 	Helveticappppppppppq ~ Ãt Data do Processamentosq ~   wñ           #   O  Ipq ~ q ~ #pt 
staticText-10ppppq ~ 7ppppq ~ :  wñpppppt Arialq ~ ¥ppq ~ §ppppppppsq ~ ¨psq ~ ¬  wñsq ~ ¯    ÿ   ppppq ~ Msq ~ D    q ~aq ~aq ~^psq ~ ´  wñsq ~ ¯    ÿ   ppppq ~ Msq ~ D    q ~aq ~apsq ~ ­  wñppppq ~aq ~apsq ~ ¹  wñsq ~ ¯    ÿ   ppppq ~ Msq ~ D    q ~aq ~apsq ~ ½  wñsq ~ ¯    ÿ   ppppq ~ Msq ~ D    q ~aq ~apppppt 	Helveticappppppppppq ~ Ãt Carteirasq ~   wñ              ~  Ipq ~ q ~ #pt 
staticText-11ppppq ~ 7ppppq ~ :  wñpppppt Arialq ~ ¥ppq ~ §ppppppppsq ~ ¨psq ~ ¬  wñsq ~ ¯    ÿ   ppppq ~ Msq ~ D    q ~tq ~tq ~qpsq ~ ´  wñsq ~ ¯    ÿ   ppppq ~ Msq ~ D    q ~tq ~tpsq ~ ­  wñppppq ~tq ~tpsq ~ ¹  wñsq ~ ¯    ÿ   ppppq ~ Msq ~ D    q ~tq ~tpsq ~ ½  wñsq ~ ¯    ÿ   ppppq ~ Msq ~ D    q ~tq ~tpppppt 	Helveticappppppppppq ~ Ãt EspÃ©ciesq ~   wñ           B     Ipq ~ q ~ #pt 
staticText-12ppppq ~ 7ppppq ~ :  wñpppppt Arialq ~ ¥ppq ~ §ppppppppsq ~ ¨psq ~ ¬  wñsq ~ ¯    ÿ   ppppq ~ Msq ~ D    q ~q ~q ~psq ~ ´  wñsq ~ ¯    ÿ   ppppq ~ Msq ~ D    q ~q ~psq ~ ­  wñppppq ~q ~psq ~ ¹  wñsq ~ ¯    ÿ   ppppq ~ Msq ~ D    q ~q ~psq ~ ½  wñsq ~ ¯    ÿ   ppppq ~ Msq ~ D    q ~q ~pppppt 	Helveticappppppppppq ~ Ãt 
Quantidadesq ~   wñ           B   ð  Ipq ~ q ~ #pt 
staticText-13ppppq ~ 7ppppq ~ :  wñpppppt Arialq ~ ¥ppq ~ §ppppppppsq ~ ¨psq ~ ¬  wñsq ~ ¯    ÿ   ppppq ~ Msq ~ D    q ~q ~q ~psq ~ ´  wñsq ~ ¯    ÿ   ppppq ~ Msq ~ D    q ~q ~psq ~ ­  wñppppq ~q ~psq ~ ¹  wñsq ~ ¯    ÿ   ppppq ~ Msq ~ D    q ~q ~psq ~ ½  wñsq ~ ¯    ÿ   ppppq ~ Msq ~ D    q ~q ~pppppt 	Helveticappppppppppq ~ Ãt Valorsq ~   wñ           d  e  pq ~ q ~ #pt 
staticText-14ppppq ~ 7ppppq ~ :  wñpppppt Arialq ~ ¥ppq ~ §ppppppppsq ~ ¨psq ~ ¬  wñsq ~ ¯    ÿ   ppppq ~ Msq ~ D    q ~­q ~­q ~ªpsq ~ ´  wñsq ~ ¯    ÿ   ppppq ~ Msq ~ D    q ~­q ~­psq ~ ­  wñppppq ~­q ~­psq ~ ¹  wñsq ~ ¯    ÿ   ppppq ~ Msq ~ D    q ~­q ~­psq ~ ½  wñsq ~ ¯    ÿ   ppppq ~ Msq ~ D    q ~­q ~­pppppt 	Helveticappppppppppq ~ Ãt 
Vencimentosq ~   wñ           d  e  'pq ~ q ~ #pt 
staticText-15ppppq ~ 7ppppq ~ :  wñpppppt Arialq ~ ¥ppq ~ §ppppppppsq ~ ¨psq ~ ¬  wñsq ~ ¯    ÿ   ppppq ~ Msq ~ D    q ~Àq ~Àq ~½psq ~ ´  wñsq ~ ¯    ÿ   ppppq ~ Msq ~ D    q ~Àq ~Àpsq ~ ­  wñppppq ~Àq ~Àpsq ~ ¹  wñsq ~ ¯    ÿ   ppppq ~ Msq ~ D    q ~Àq ~Àpsq ~ ½  wñsq ~ ¯    ÿ   ppppq ~ Msq ~ D    q ~Àq ~Àpppppt 	Helveticappppppppppq ~ Ãt AgÃªncia / CÃ³digo Cedentesq ~   wñ           d  e  pq ~ q ~ #pt 
staticText-16ppppq ~ 7ppppq ~ :  wñpppppt Arialq ~ ¥ppq ~ §ppppppppsq ~ ¨psq ~ ¬  wñsq ~ ¯    ÿ   ppppq ~ Msq ~ D    q ~Óq ~Óq ~Ðpsq ~ ´  wñsq ~ ¯    ÿ   ppppq ~ Msq ~ D    q ~Óq ~Ópsq ~ ­  wñppppq ~Óq ~Ópsq ~ ¹  wñsq ~ ¯    ÿ   ppppq ~ Msq ~ D    q ~Óq ~Ópsq ~ ½  wñsq ~ ¯    ÿ   ppppq ~ Msq ~ D    q ~Óq ~Ópppppt 	Helveticappppppppppq ~ Ãt (=) Valor cobradosq ~   wñ           d  e  pq ~ q ~ #pt 
staticText-17ppppq ~ 7ppppq ~ :  wñpppppt Arialq ~ ¥ppq ~ §ppppppppsq ~ ¨psq ~ ¬  wñsq ~ ¯    ÿ   ppppq ~ Msq ~ D    q ~æq ~æq ~ãpsq ~ ´  wñsq ~ ¯    ÿ   ppppq ~ Msq ~ D    q ~æq ~æpsq ~ ­  wñppppq ~æq ~æpsq ~ ¹  wñsq ~ ¯    ÿ   ppppq ~ Msq ~ D    q ~æq ~æpsq ~ ½  wñsq ~ ¯    ÿ   ppppq ~ Msq ~ D    q ~æq ~æpppppt 	Helveticappppppppppq ~ Ãt (=) Outros acrÃ©scimossq ~   wñ           d  e  |pq ~ q ~ #pt 
staticText-18ppppq ~ 7ppppq ~ :  wñpppppt Arialq ~ ¥ppq ~ §ppppppppsq ~ ¨psq ~ ¬  wñsq ~ ¯    ÿ   ppppq ~ Msq ~ D    q ~ùq ~ùq ~öpsq ~ ´  wñsq ~ ¯    ÿ   ppppq ~ Msq ~ D    q ~ùq ~ùpsq ~ ­  wñppppq ~ùq ~ùpsq ~ ¹  wñsq ~ ¯    ÿ   ppppq ~ Msq ~ D    q ~ùq ~ùpsq ~ ½  wñsq ~ ¯    ÿ   ppppq ~ Msq ~ D    q ~ùq ~ùpppppt 	Helveticappppppppppq ~ Ãt (+) Juros / Multasq ~   wñ           d  e  kpq ~ q ~ #pt 
staticText-19ppppq ~ 7ppppq ~ :  wñpppppt Arialq ~ ¥ppq ~ §ppppppppsq ~ ¨psq ~ ¬  wñsq ~ ¯    ÿ   ppppq ~ Msq ~ D    q ~q ~q ~	psq ~ ´  wñsq ~ ¯    ÿ   ppppq ~ Msq ~ D    q ~q ~psq ~ ­  wñppppq ~q ~psq ~ ¹  wñsq ~ ¯    ÿ   ppppq ~ Msq ~ D    q ~q ~psq ~ ½  wñsq ~ ¯    ÿ   ppppq ~ Msq ~ D    q ~q ~pppppt 	Helveticappppppppppq ~ Ãt (-) Outros deduÃ§Ãµessq ~   wñ           d  e  Zpq ~ q ~ #pt 
staticText-20ppppq ~ 7ppppq ~ :  wñpppppt Arialq ~ ¥ppq ~ §ppppppppsq ~ ¨psq ~ ¬  wñsq ~ ¯    ÿ   ppppq ~ Msq ~ D    q ~q ~q ~psq ~ ´  wñsq ~ ¯    ÿ   ppppq ~ Msq ~ D    q ~q ~psq ~ ­  wñppppq ~q ~psq ~ ¹  wñsq ~ ¯    ÿ   ppppq ~ Msq ~ D    q ~q ~psq ~ ½  wñsq ~ ¯    ÿ   ppppq ~ Msq ~ D    q ~q ~pppppt 	Helveticappppppppppq ~ Ãt (-) Desconto / Abatimentosq ~   wñ           d  e  Ipq ~ q ~ #pt 
staticText-21ppppq ~ 7ppppq ~ :  wñpppppt Arialq ~ ¥ppq ~ §ppppppppsq ~ ¨psq ~ ¬  wñsq ~ ¯    ÿ   ppppq ~ Msq ~ D    q ~2q ~2q ~/psq ~ ´  wñsq ~ ¯    ÿ   ppppq ~ Msq ~ D    q ~2q ~2psq ~ ­  wñppppq ~2q ~2psq ~ ¹  wñsq ~ ¯    ÿ   ppppq ~ Msq ~ D    q ~2q ~2psq ~ ½  wñsq ~ ¯    ÿ   ppppq ~ Msq ~ D    q ~2q ~2pppppt 	Helveticappppppppppq ~ Ãt (=) Valor do Documentosq ~   wñ           d  e  8pq ~ q ~ #pt 
staticText-22ppppq ~ 7ppppq ~ :  wñpppppt Arialq ~ ¥ppq ~ §ppppppppsq ~ ¨psq ~ ¬  wñsq ~ ¯    ÿ   ppppq ~ Msq ~ D    q ~Eq ~Eq ~Bpsq ~ ´  wñsq ~ ¯    ÿ   ppppq ~ Msq ~ D    q ~Eq ~Epsq ~ ­  wñppppq ~Eq ~Epsq ~ ¹  wñsq ~ ¯    ÿ   ppppq ~ Msq ~ D    q ~Eq ~Epsq ~ ½  wñsq ~ ¯    ÿ   ppppq ~ Msq ~ D    q ~Eq ~Epppppt 	Helveticappppppppppq ~ Ãt 
Nosso NÃºmerosq ~   wñ           !     ±pq ~ q ~ #pt 
staticText-23ppppq ~ 7ppppq ~ :  wñpppppt Arialq ~ ¥ppq ~ §ppppppppsq ~ ¨psq ~ ¬  wñsq ~ ¯    ÿ   ppppq ~ Msq ~ D    q ~Xq ~Xq ~Upsq ~ ´  wñsq ~ ¯    ÿ   ppppq ~ Msq ~ D    q ~Xq ~Xpsq ~ ­  wñppppq ~Xq ~Xpsq ~ ¹  wñsq ~ ¯    ÿ   ppppq ~ Msq ~ D    q ~Xq ~Xpsq ~ ½  wñsq ~ ¯    ÿ   ppppq ~ Msq ~ D    q ~Xq ~Xpppppt 	Helveticappppppppppq ~ Ãt Sacado :sq ~   wñ           A     Ïpq ~ q ~ #pt 
staticText-24ppppq ~ 7ppppq ~ :  wñpppppt Arialq ~ ¥ppq ~ §ppppppppsq ~ ¨psq ~ ¬  wñsq ~ ¯    ÿ   ppppq ~ Msq ~ D    q ~kq ~kq ~hpsq ~ ´  wñsq ~ ¯    ÿ   ppppq ~ Msq ~ D    q ~kq ~kpsq ~ ­  wñppppq ~kq ~kpsq ~ ¹  wñsq ~ ¯    ÿ   ppppq ~ Msq ~ D    q ~kq ~kpsq ~ ½  wñsq ~ ¯    ÿ   ppppq ~ Msq ~ D    q ~kq ~kpppppt 	Helveticappppppppppq ~ Ãt Sacador / Avalista :sq ~   wñ           9  e  Ïpq ~ q ~ #pt 
staticText-25ppppq ~ 7ppppq ~ :  wñpppppt Arialq ~ ¥ppq ~ §ppppppppsq ~ ¨psq ~ ¬  wñsq ~ ¯    ÿ   ppppq ~ Msq ~ D    q ~~q ~~q ~{psq ~ ´  wñsq ~ ¯    ÿ   ppppq ~ Msq ~ D    q ~~q ~~psq ~ ­  wñppppq ~~q ~~psq ~ ¹  wñsq ~ ¯    ÿ   ppppq ~ Msq ~ D    q ~~q ~~psq ~ ½  wñsq ~ ¯    ÿ   ppppq ~ Msq ~ D    q ~~q ~~pppppt 	Helveticappppppppppq ~ Ãt CÃ³digo de baixasq ~   wñ           D  L  Üpq ~ q ~ #pt 
staticText-26ppppq ~ 7ppppq ~ :  wñpppppt Arialq ~ ¥ppq ~ §ppppppppsq ~ ¨psq ~ ¬  wñsq ~ ¯    ÿ   ppppq ~ Msq ~ D    q ~q ~q ~psq ~ ´  wñsq ~ ¯    ÿ   ppppq ~ Msq ~ D    q ~q ~psq ~ ­  wñppppq ~q ~psq ~ ¹  wñsq ~ ¯    ÿ   ppppq ~ Msq ~ D    q ~q ~psq ~ ½  wñsq ~ ¯    ÿ   ppppq ~ Msq ~ D    q ~q ~pppppt 	Helveticappppppppppq ~ Ãt AutenticaÃ§Ã£o MecÃ¢nicasq ~   wñ           j    Üpq ~ q ~ #pt 
staticText-27ppppq ~ 7ppppq ~ :  wñpppppt Arialsq ~ ¤   	ppsq ~ ¦ppppppppsq ~ ¨psq ~ ¬  wñsq ~ ¯    ÿ   ppppq ~ Msq ~ D    q ~¦q ~¦q ~¡psq ~ ´  wñsq ~ ¯    ÿ   ppppq ~ Msq ~ D    q ~¦q ~¦psq ~ ­  wñppppq ~¦q ~¦psq ~ ¹  wñsq ~ ¯    ÿ   ppppq ~ Msq ~ D    q ~¦q ~¦psq ~ ½  wñsq ~ ¯    ÿ   ppppq ~ Msq ~ D    q ~¦q ~¦pppppt Helvetica-Boldppppppppppq ~ Ãt Ficha de CompensaÃ§Ã£osq ~   wñ              ê  Opq ~ q ~ #pt 
staticText-28p~r )net.sf.jasperreports.engine.type.ModeEnum          xq ~ t OPAQUEppq ~ 7ppppq ~ :  wñpppppt Arialq ~ ¥p~r 4net.sf.jasperreports.engine.type.HorizontalAlignEnum          xq ~ t CENTERq ~¥ppppppppsq ~ ¨psq ~ ¬  wñsq ~ ¯    ÿ   ppppq ~ Msq ~ D    q ~¿q ~¿q ~¶psq ~ ´  wñsq ~ ¯    ÿ   ppppq ~ Msq ~ D    q ~¿q ~¿psq ~ ­  wñppppq ~¿q ~¿psq ~ ¹  wñsq ~ ¯    ÿ   ppppq ~ Msq ~ D    q ~¿q ~¿psq ~ ½  wñsq ~ ¯    ÿ   ppppq ~ Msq ~ D    q ~¿q ~¿pppppt Helvetica-Boldppppppppppq ~ Ãt Xsr ,net.sf.jasperreports.engine.base.JRBaseImage      'Ø *I PSEUDO_SERIAL_VERSION_UIDI 
bookmarkLevelB evaluationTimeB hyperlinkTargetB 
hyperlinkTypeZ isLazyB onErrorTypeL anchorNameExpressionq ~ L borderq ~ L borderColorq ~ ,L bottomBorderq ~ L bottomBorderColorq ~ ,L 
bottomPaddingq ~ L evaluationGroupq ~ 0L evaluationTimeValuet 5Lnet/sf/jasperreports/engine/type/EvaluationTimeEnum;L 
expressionq ~ L horizontalAlignmentq ~ L horizontalAlignmentValueq ~ L hyperlinkAnchorExpressionq ~ L hyperlinkPageExpressionq ~ [ hyperlinkParameterst 3[Lnet/sf/jasperreports/engine/JRHyperlinkParameter;L hyperlinkReferenceExpressionq ~ L hyperlinkTooltipExpressionq ~ L isUsingCacheq ~ L 
leftBorderq ~ L leftBorderColorq ~ ,L leftPaddingq ~ L lineBoxq ~ L 
linkTargetq ~ L linkTypeq ~ L onErrorTypeValuet 2Lnet/sf/jasperreports/engine/type/OnErrorTypeEnum;L paddingq ~ L rightBorderq ~ L rightBorderColorq ~ ,L rightPaddingq ~ L 
scaleImageq ~ L scaleImageValuet 1Lnet/sf/jasperreports/engine/type/ScaleImageEnum;L 	topBorderq ~ L topBorderColorq ~ ,L 
topPaddingq ~ L verticalAlignmentq ~ L verticalAlignmentValueq ~  xq ~ (  wñ   #       ?     Üsq ~ ¯    ÿÿÿÿpppq ~ q ~ #sq ~ ¯    ÿ   pppt 	barcode-1pq ~¹ppq ~ 7ppppq ~ :  wîp~r )net.sf.jasperreports.engine.type.FillEnum          xq ~ t SOLIDsq ~ <  wñppq ~ Msq ~ D    q ~Ôp  wñ         ppppppp~r 3net.sf.jasperreports.engine.type.EvaluationTimeEnum          xq ~ t NOWsr 1net.sf.jasperreports.engine.base.JRBaseExpression      'Ø I id[ chunkst 0[Lnet/sf/jasperreports/engine/JRExpressionChunk;L valueClassNameq ~ L valueClassRealNameq ~ xp   	ur 0[Lnet.sf.jasperreports.engine.JRExpressionChunk;mYÏÞiK£U  xp   sr 6net.sf.jasperreports.engine.base.JRBaseExpressionChunk      'Ø B typeL textq ~ xpt <it.businesslogic.ireport.barcode.BcImage.getBarcodeImage(13,sq ~åt banco.codigoBarrassq ~åt ,false,false,null,0,0)t java.awt.Imagepp~q ~¼t LEFTpppppppppsq ~ ¨psq ~ ¬  wñsq ~ ¯    ÿ   ppppq ~ Msq ~ D    q ~ïq ~ïq ~Ôpsq ~ ´  wñsq ~ ¯    ÿ   ppppq ~ Msq ~ D    q ~ïq ~ïpsq ~ ­  wñppppq ~ïq ~ïpsq ~ ¹  wñsq ~ ¯    ÿ   ppppq ~ Msq ~ D    q ~ïq ~ïpsq ~ ½  wñsq ~ ¯    ÿ   ppppq ~ Msq ~ D    q ~ïq ~ïpp~r 0net.sf.jasperreports.engine.type.OnErrorTypeEnum          xq ~ t ERRORppppp~r /net.sf.jasperreports.engine.type.ScaleImageEnum          xq ~ t 
FILL_FRAMEpppp~q ~ Ât TOPsq ~ &  wñ                pq ~ q ~ #pt line-50ppppq ~ 7ppppq ~ :  wîppsq ~ <  wñppq ~ Bsq ~ D?  q ~p  wñ q ~ Hsr 0net.sf.jasperreports.engine.base.JRBaseTextField      'Ø I PSEUDO_SERIAL_VERSION_UIDI 
bookmarkLevelB evaluationTimeB hyperlinkTargetB 
hyperlinkTypeZ isStretchWithOverflowL anchorNameExpressionq ~ L evaluationGroupq ~ 0L evaluationTimeValueq ~ÐL 
expressionq ~ L hyperlinkAnchorExpressionq ~ L hyperlinkPageExpressionq ~ [ hyperlinkParametersq ~ÑL hyperlinkReferenceExpressionq ~ L hyperlinkTooltipExpressionq ~ L isBlankWhenNullq ~ L 
linkTargetq ~ L linkTypeq ~ L patternq ~ xq ~   wñ           #   x  pq ~ q ~ #pt textField-35ppppq ~ 7ppppq ~ :  wñppppppppq ~½q ~¥ppppppppsq ~ ¨psq ~ ¬  wñsq ~ ¯    ÿ   ppppq ~ Msq ~ D    q ~q ~q ~
psq ~ ´  wñsq ~ ¯    ÿ   ppppq ~ Msq ~ D    q ~q ~psq ~ ­  wñppppq ~q ~psq ~ ¹  wñsq ~ ¯    ÿ   ppppq ~ Msq ~ D    q ~q ~psq ~ ½  wñsq ~ ¯    ÿ   ppppq ~ Msq ~ D    q ~q ~pppppt Helvetica-Boldppppppppppq ~ Ã  wñ        ppq ~Þsq ~à   
uq ~ã   sq ~åt banco.numeroFormattedt java.lang.Stringppppppq ~¥pppsq ~	  wñ          p     pq ~ q ~ #ppppppq ~ 7ppppq ~ :  wñppppppppq ~½q ~¥ppppppppsq ~ ¨psq ~ ¬  wñppppq ~!q ~!q ~ psq ~ ´  wñppppq ~!q ~!psq ~ ­  wñppppq ~!q ~!psq ~ ¹  wñppppq ~!q ~!psq ~ ½  wñppppq ~!q ~!pppppt Helvetica-Boldppppppppppq ~ Ã  wñ        ppq ~Þsq ~à   uq ~ã   sq ~åt banco.linhaDigitavelt java.lang.Stringppppppppppsq ~	  wñ   	       [     /pq ~ q ~ #ppppppq ~ 7ppppq ~ :  wñpppppt Arialsq ~ ¤   pppppppppppsq ~ ¨psq ~ ¬  wñppppq ~0q ~0q ~-psq ~ ´  wñppppq ~0q ~0psq ~ ­  wñppppq ~0q ~0psq ~ ¹  wñppppq ~0q ~0psq ~ ½  wñppppq ~0q ~0pppppppppppppppp~q ~ Ât BOTTOM  wñ        ppq ~Þsq ~à   uq ~ã   sq ~åt boleto.cedentet java.lang.Stringppppppppppsq ~	  wñ   	       [     pq ~ q ~ #ppppppq ~ 7ppppq ~ :  wñpppppt Arialq ~/pppppppppppsq ~ ¨psq ~ ¬  wñppppq ~?q ~?q ~=psq ~ ´  wñppppq ~?q ~?psq ~ ­  wñppppq ~?q ~?psq ~ ¹  wñppppq ~?q ~?psq ~ ½  wñppppq ~?q ~?ppppppppppppppppq ~6  wñ        ppq ~Þsq ~à   
uq ~ã   sq ~åt boleto.localPagamentot java.lang.Stringppppppq ~¥pppsq ~	  wñ   	        B     @pq ~ q ~ #ppppppq ~ 7ppppq ~ :  wñpppppt Arialq ~/pppppppppppsq ~ ¨psq ~ ¬  wñppppq ~Lq ~Lq ~Jpsq ~ ´  wñppppq ~Lq ~Lpsq ~ ­  wñppppq ~Lq ~Lpsq ~ ¹  wñppppq ~Lq ~Lpsq ~ ½  wñppppq ~Lq ~Lppppppppppppppppq ~6  wñ        ppq ~Þsq ~à   uq ~ã   sq ~åt boleto.dataDocumentot java.lang.Stringppppppq ~¥ppt  sq ~	  wñ   	        3     @pq ~ q ~ #ppppppq ~ 7ppppq ~ :  wñpppppt Arialq ~/pq ~½pppppppppsq ~ ¨psq ~ ¬  wñppppq ~Zq ~Zq ~Xpsq ~ ´  wñppppq ~Zq ~Zpsq ~ ­  wñppppq ~Zq ~Zpsq ~ ¹  wñppppq ~Zq ~Zpsq ~ ½  wñppppq ~Zq ~Zppppppppppppppppq ~6  wñ        ppq ~Þsq ~à   uq ~ã   sq ~åt boleto.especieDocumentot java.lang.Stringppppppppppsq ~	  wñ   	           Ó  @pq ~ q ~ #ppppppq ~ 7ppppq ~ :  wñpppppt Arialq ~/pq ~½pppppppppsq ~ ¨psq ~ ¬  wñppppq ~gq ~gq ~epsq ~ ´  wñppppq ~gq ~gpsq ~ ­  wñppppq ~gq ~gpsq ~ ¹  wñppppq ~gq ~gpsq ~ ½  wñppppq ~gq ~gppppppppppppppppq ~6  wñ        ppq ~Þsq ~à   uq ~ã   sq ~åt 
boleto.aceitet java.lang.Stringppppppppppsq ~	  wñ   	        X   ð  @pq ~ q ~ #ppppppq ~ 7ppppq ~ :  wñpppppt Arialq ~/pppppppppppsq ~ ¨psq ~ ¬  wñppppq ~tq ~tq ~rpsq ~ ´  wñppppq ~tq ~tpsq ~ ­  wñppppq ~tq ~tpsq ~ ¹  wñppppq ~tq ~tpsq ~ ½  wñppppq ~tq ~tppppppppppppppppq ~6  wñ        ppq ~Þsq ~à   uq ~ã   sq ~åt boleto.dataProcessamentot java.lang.Stringppppppppppsq ~	  wñ   	        (   O  Qpq ~ q ~ #ppppppq ~ 7ppppq ~ :  wñpppppt Arialq ~/pq ~½pppppppppsq ~ ¨psq ~ ¬  wñppppq ~q ~q ~psq ~ ´  wñppppq ~q ~psq ~ ­  wñppppq ~q ~psq ~ ¹  wñppppq ~q ~psq ~ ½  wñppppq ~q ~ppppppppppppppppq ~6  wñ        ppq ~Þsq ~à   uq ~ã   sq ~åt boleto.carteirat java.lang.Stringppppppppppsq ~	  wñ   	        P    pq ~ q ~ #ppppppq ~ 7ppppq ~ :  wñpppppt Arialq ~/p~q ~¼t RIGHTpppppppppsq ~ ¨psq ~ ¬  wñppppq ~q ~q ~psq ~ ´  wñppppq ~q ~psq ~ ­  wñppppq ~q ~psq ~ ¹  wñppppq ~q ~psq ~ ½  wñppppq ~q ~ppppppppppppppppq ~6  wñ        ppq ~Þsq ~à   uq ~ã   sq ~åt boleto.dataVencimentot java.lang.Stringppppppppppsq ~	  wñ   	        P    Qpq ~ q ~ #ppppppq ~ 7ppppq ~ :  wñpppppt Arialq ~/pq ~pppppppppsq ~ ¨psq ~ ¬  wñppppq ~q ~q ~psq ~ ´  wñppppq ~q ~psq ~ ­  wñppppq ~q ~psq ~ ¹  wñppppq ~q ~psq ~ ½  wñppppq ~q ~ppppppppppppppppq ~6  wñ        ppq ~Þsq ~à   uq ~ã   sq ~åt boleto.valorBoletot java.lang.Stringppppppppppsq ~	  wñ   	       Ý   %  °pq ~ q ~ #ppppppq ~ 7ppppq ~ :  wñpppppt Arialq ~/pppppppppppsq ~ ¨psq ~ ¬  wñppppq ~ªq ~ªq ~¨psq ~ ´  wñppppq ~ªq ~ªpsq ~ ­  wñppppq ~ªq ~ªpsq ~ ¹  wñppppq ~ªq ~ªpsq ~ ½  wñppppq ~ªq ~ªppppppppppppppppq ~6  wñ        ppq ~Þsq ~à   uq ~ã   sq ~åt boleto.nomeSacadot java.lang.Stringppppppppppsq ~	  wñ   	          k  ?pq ~ q ~ #pt textField-49ppppq ~ 7ppppq ~ :  wñpppppt Arialq ~/pq ~pppppppppsq ~ ¨psq ~ ¬  wñsq ~ ¯    ÿ   ppppq ~ Msq ~ D    q ~¸q ~¸q ~µpsq ~ ´  wñsq ~ ¯    ÿ   ppppq ~ Msq ~ D    q ~¸q ~¸psq ~ ­  wñppppq ~¸q ~¸psq ~ ¹  wñsq ~ ¯    ÿ   ppppq ~ Msq ~ D    q ~¸q ~¸psq ~ ½  wñsq ~ ¯    ÿ   ppppq ~ Msq ~ D    q ~¸q ~¸ppppppppppppppppq ~6  wñ        ppq ~Þsq ~à   uq ~ã   sq ~åt boleto.nossoNumerot java.lang.Stringppppppq ~¥ppq ~Wsq ~ &  wñ          
      êpq ~ q ~ #pt line-1ppppq ~ 7ppppq ~ :  wîppsq ~ <  wñppq ~ Msq ~ D?  q ~Ëp  wñ q ~ Hsq ~	  wñ   	        Ì     öpq ~ q ~ #ppppppq ~ 7ppppq ~ :  wñpppppt Arialq ~/pppppppppppsq ~ ¨psq ~ ¬  wñppppq ~Ñq ~Ñq ~Ïpsq ~ ´  wñppppq ~Ñq ~Ñpsq ~ ­  wñppppq ~Ñq ~Ñpsq ~ ¹  wñppppq ~Ñq ~Ñpsq ~ ½  wñppppq ~Ñq ~Ñppppppppppppppppq ~6  wñ        ppq ~Þsq ~à   uq ~ã   sq ~åt boleto.nomeSacadot java.lang.Stringppppppppppsq ~   wñ           !     îpq ~ q ~ #pt 
staticText-23ppppq ~ 7ppppq ~ :  wñpppppt Arialq ~ ¥ppq ~ §ppppppppsq ~ ¨psq ~ ¬  wñsq ~ ¯    ÿ   ppppq ~ Msq ~ D    q ~ßq ~ßq ~Üpsq ~ ´  wñsq ~ ¯    ÿ   ppppq ~ Msq ~ D    q ~ßq ~ßpsq ~ ­  wñppppq ~ßq ~ßpsq ~ ¹  wñsq ~ ¯    ÿ   ppppq ~ Msq ~ D    q ~ßq ~ßpsq ~ ½  wñsq ~ ¯    ÿ   ppppq ~ Msq ~ D    q ~ßq ~ßpppppt 	Helveticappppppppppq ~ Ãt Sacado :sq ~   wñ           F  k  ëpq ~ q ~ #pt 
staticText-26ppppq ~ 7ppppq ~ :  wñpppppt Arialq ~ ¥pq ~½q ~ §ppppppppsq ~ ¨psq ~ ¬  wñsq ~ ¯    ÿ   ppppq ~ Msq ~ D    q ~òq ~òq ~ïpsq ~ ´  wñsq ~ ¯    ÿ   ppppq ~ Msq ~ D    q ~òq ~òpsq ~ ­  wñppppq ~òq ~òpsq ~ ¹  wñsq ~ ¯    ÿ   ppppq ~ Msq ~ D    q ~òq ~òpsq ~ ½  wñsq ~ ¯    ÿ   ppppq ~ Msq ~ D    q ~òq ~òpppppt 	Helveticappppppppppq ~ Ãt AutenticaÃ§Ã£o MecÃ¢nicasq ~ &  wñ           K  ¶  îpq ~ q ~ #ppppppq ~ 7ppppq ~ :  wîppsq ~ <  wñppppq ~p  wñ q ~ Hsq ~ &  wñ           K    îpq ~ q ~ #ppppppq ~ 7ppppq ~ :  wîppsq ~ <  wñppppq ~p  wñ q ~ Hsq ~ &  wñ               îpq ~ q ~ #ppppppq ~ 7ppppq ~ :  wîppsq ~ <  wñppppq ~p  wñ q ~ Hsq ~ &  wñ               îpq ~ q ~ #ppppppq ~ 7ppppq ~ :  wîppsq ~ <  wñppppq ~p  wñ q ~ Hsr ,net.sf.jasperreports.engine.base.JRBaseFrame      'Ø L borderq ~ L borderColorq ~ ,L bottomBorderq ~ L bottomBorderColorq ~ ,L 
bottomPaddingq ~ L childrenq ~ !L 
leftBorderq ~ L leftBorderColorq ~ ,L leftPaddingq ~ L lineBoxq ~ L paddingq ~ L rightBorderq ~ L rightBorderColorq ~ ,L rightPaddingq ~ L 	topBorderq ~ L topBorderColorq ~ ,L 
topPaddingq ~ xq ~ +  wñ  Ê             pq ~ q ~ #ppppppq ~ 7ppppq ~ :pppppsq ~ $    w    xpppsq ~ ¨psq ~ ¬  wñsq ~ ¯    ÿpppppsq ~ D@à  q ~
q ~
q ~psq ~ ´  wñsq ~ ¯    ÿÌÌÌpppppsq ~ D    q ~
q ~
psq ~ ­  wñppppq ~
q ~
psq ~ ¹  wñsq ~ ¯    ÿpppppsq ~ D@à  q ~
q ~
psq ~ ½  wñsq ~ ¯    ÿÌÌÌpppppsq ~ D    q ~
q ~
pppppppsq ~	  wñ   
        Ó  H   pq ~ q ~ #ppppppq ~ 7ppppq ~ :  wñpppppt Microsoft Sans Serifsq ~ ¤   ppq ~¥q ~¥pppppppsq ~ ¨psq ~ ¬  wñppppq ~q ~q ~psq ~ ´  wñppppq ~q ~psq ~ ­  wñppppq ~q ~psq ~ ¹  wñppppq ~q ~psq ~ ½  wñppppq ~q ~ppppppppppppppppq ~ Ã  wñ        ppq ~Þsq ~à   uq ~ã   sq ~åt empresaVO.fonet java.lang.Stringppppppppppsq ~	  wñ   
        Ó  H    pq ~ q ~ #ppppppq ~ 7ppppq ~ :  wñpppppt Microsoft Sans Serifq ~ppq ~¥q ~¥pppppppsq ~ ¨psq ~ ¬  wñppppq ~+q ~+q ~)psq ~ ´  wñppppq ~+q ~+psq ~ ­  wñppppq ~+q ~+psq ~ ¹  wñppppq ~+q ~+psq ~ ½  wñppppq ~+q ~+pppppt Helvetica-Boldppppppppppq ~ Ã  wñ        ppq ~Þsq ~à   uq ~ã   sq ~åt nomeEmpresat java.lang.Stringppppppppppsq ~Ï  wñ   '       '      pq ~ q ~ #sq ~ ¯    ÿÿÿÿpppt image-1pppp~q ~ 6t FLOATpppp~q ~ 9t RELATIVE_TO_BAND_HEIGHT  wîppsq ~ <  wñppppq ~7p  wñ         ppppppp~q ~Ýt PAGEsq ~à   uq ~ã   sq ~åt logoPadraoRelatoriot java.io.InputStreamppppppppq ~¥pppsq ~ ¨psq ~ ¬  wñppppq ~Fq ~Fq ~7psq ~ ´  wñppppq ~Fq ~Fpsq ~ ­  wñppppq ~Fq ~Fpsq ~ ¹  wñppppq ~Fq ~Fpsq ~ ½  wñppppq ~Fq ~Fpp~q ~ýt BLANKpppppq ~pppppsq ~	  wñ   
        Ó  H   
pq ~ q ~ #ppppppq ~ 7ppppq ~ :  wñpppppt Microsoft Sans Serifq ~ppq ~¥q ~¥pppppppsq ~ ¨psq ~ ¬  wñppppq ~Pq ~Pq ~Npsq ~ ´  wñppppq ~Pq ~Ppsq ~ ­  wñppppq ~Pq ~Ppsq ~ ¹  wñppppq ~Pq ~Ppsq ~ ½  wñppppq ~Pq ~Pppppppppppppppppq ~ Ã  wñ        ppq ~Þsq ~à   uq ~ã   sq ~åt empresaVO.enderecot java.lang.Stringppppppppppsq ~
  wñ  Ì       	       pq ~ q ~ #ppppppq ~ 7ppppq ~ :pppppsq ~ $   w   sq ~	  wñ              m  ¾pq ~ q ~[ppppppq ~ 7ppppq ~ :  wñpppppt Arialq ~pq ~pppppppppsq ~ ¨psq ~ ¬  wñppppq ~_q ~_q ~]psq ~ ´  wñppppq ~_q ~_psq ~ ­  wñppppq ~_q ~_psq ~ ¹  wñppppq ~_q ~_psq ~ ½  wñppppq ~_q ~_ppppppppppppppppq ~6  wñ        ppq ~Þsq ~à   uq ~ã   sq ~åt "Total: " + sq ~åt boleto.valorBoletot java.lang.Stringppppppppppsr 0net.sf.jasperreports.engine.base.JRBaseSubreport      'Ø L connectionExpressionq ~ L dataSourceExpressionq ~ L 
expressionq ~ L isUsingCacheq ~ [ 
parameterst 3[Lnet/sf/jasperreports/engine/JRSubreportParameter;L parametersMapExpressionq ~ [ returnValuest 5[Lnet/sf/jasperreports/engine/JRSubreportReturnValue;L runToBottomq ~ xq ~ +  wñ  ¡        ù      pq ~ q ~[ppppppq ~ 7ppppq ~ :psq ~à   uq ~ã   sq ~åt boleto.descricoesDTt (net.sf.jasperreports.engine.JRDataSourcepsq ~à   uq ~ã   sq ~åt 
SUBREPORT_DIRsq ~åt  + "boletoClube_itens.jasper"t java.lang.Stringpq ~¥ppppsq ~	  wñ           d     ¾pq ~ q ~[ppppppq ~ 7ppppq ~ :  wñpppppt Arialq ~pppppppppppsq ~ ¨psq ~ ¬  wñppppq ~~q ~~q ~|psq ~ ´  wñppppq ~~q ~~psq ~ ­  wñppppq ~~q ~~psq ~ ¹  wñppppq ~~q ~~psq ~ ½  wñppppq ~~q ~~ppppppppppppppppq ~6  wñ        ppq ~Þsq ~à   uq ~ã   sq ~åt boleto.instrucao3t java.lang.Stringppppppppppxpppsq ~ ¨psq ~ ¬  wñsq ~ ¯    ÿÌÌÌpppppsq ~ D?  q ~q ~q ~[psq ~ ´  wñsq ~ ¯    ÿÌÌÌpppppsq ~ D?  q ~q ~psq ~ ­  wñppppq ~q ~psq ~ ¹  wñsq ~ ¯    ÿÌÌÌpppppsq ~ D?  q ~q ~psq ~ ½  wñsq ~ ¯    ÿÌÌÌpppppsq ~ D?  q ~q ~pppppppsq ~	  wñ   	       Ý   %  ¹pq ~ q ~ #ppppppq ~ 7ppppq ~ :  wñpppppt Arialq ~/pppppppppppsq ~ ¨psq ~ ¬  wñppppq ~q ~q ~psq ~ ´  wñppppq ~q ~psq ~ ­  wñppppq ~q ~psq ~ ¹  wñppppq ~q ~psq ~ ½  wñppppq ~q ~ppppppppppppppppq ~6  wñ        ppq ~Þsq ~à    uq ~ã   sq ~åt boleto.enderecoSacadot java.lang.Stringppppppppppsq ~	  wñ   	       Ü   %  Âpq ~ q ~ #ppppppq ~ 7ppppq ~ :  wñpppppt Arialq ~/pppppppppppsq ~ ¨psq ~ ¬  wñppppq ~¦q ~¦q ~¤psq ~ ´  wñppppq ~¦q ~¦psq ~ ­  wñppppq ~¦q ~¦psq ~ ¹  wñppppq ~¦q ~¦psq ~ ½  wñppppq ~¦q ~¦ppppppppppppppppq ~6  wñ        ppq ~Þsq ~à   !uq ~ã   sq ~åt (sq ~åt boleto.bairroSacadosq ~åt 
==null?"":sq ~åt boleto.bairroSacadosq ~åt .trim() + " - " )
+ (sq ~åt boleto.cidadeSacadosq ~åt ==null ? "  ":sq ~åt boleto.cidadeSacadosq ~åt .trim() + " - " )
+ (sq ~åt boleto.ufSacadosq ~åt ==null ? "  " : ( sq ~åt boleto.ufSacadosq ~åt ) + " - ")
+ (sq ~åt boleto.cepSacadosq ~åt .trim().isEmpty() ? "" : (((sq ~åt boleto.cepSacadosq ~åt .length() <= 5)? sq ~åt boleto.cepSacadosq ~åt  + "-000" : sq ~åt boleto.cepSacadosq ~åt .substring(0,5)+"-"+sq ~åt boleto.cepSacadosq ~åt .substring(5))))t java.lang.Stringppppppq ~¥pppsq ~	  wñ   	        P    /pq ~ q ~ #ppppppq ~ 7ppppq ~ :  wñpppppt Arialq ~/pq ~pppppppppsq ~ ¨psq ~ ¬  wñppppq ~ßq ~ßq ~Ýpsq ~ ´  wñppppq ~ßq ~ßpsq ~ ­  wñppppq ~ßq ~ßpsq ~ ¹  wñppppq ~ßq ~ßpsq ~ ½  wñppppq ~ßq ~ßppppppppppppppppq ~6  wñ        ppq ~Þsq ~à   "uq ~ã   sq ~åt  banco.agenciaCodCedenteFormattedt java.lang.Stringppppppppppsq ~Ï  wñ   î       î      dpq ~ q ~ #sq ~ ¯    ÿÿÿÿpppt image-1ppppq ~:ppppq ~<  wîppsq ~ <  wñppppq ~êp  wñ         pppppppq ~?sq ~à   #uq ~ã   sq ~åt 
propagandat java.io.InputStreamppppppppq ~¥pppsq ~ ¨psq ~ ¬  wñppppq ~óq ~óq ~êpsq ~ ´  wñppppq ~óq ~ópsq ~ ­  wñppppq ~óq ~ópsq ~ ¹  wñppppq ~óq ~ópsq ~ ½  wñppppq ~óq ~óppq ~Lpppppq ~pppppsq ~	  wñ   F       T     epq ~ q ~ #pt textField-24ppppq ~ 7ppppq ~ :  wñpppppt Arialq ~/pq ~ípppppppppsq ~ ¨psq ~ ¬  wñsq ~ ¯    ÿ   ppppq ~ Msq ~ D    q ~üq ~üq ~ùpsq ~ ´  wñsq ~ ¯    ÿ   ppppq ~ Msq ~ D    q ~üq ~üpsq ~ ­  wñppppq ~üq ~üpsq ~ ¹  wñsq ~ ¯    ÿ   ppppq ~ Msq ~ D    q ~üq ~üpsq ~ ½  wñsq ~ ¯    ÿ   ppppq ~ Msq ~ D    q ~üq ~üppppppppppppppppq ~  wñ        ppq ~Þsq ~à   $uq ~ã   sq ~åt boleto.instrucao1t java.lang.Stringppppppq ~¥pppsr 0net.sf.jasperreports.engine.base.JRBaseRectangle      'Ø L radiusq ~ xq ~ (  wñ   /        ó    ±pq ~ q ~ #ppppppq ~ 7ppppq ~ :  wîppsq ~ <  wñppppq ~psq ~ ¤   
sq ~	  wñ   -        î     pq ~ q ~ #ppppppq ~ 7sq ~à   %uq ~ã   sq ~åt boleto.instrucao2sq ~åt .trim().length() > 0t java.lang.Booleanppppq ~ :  wñpppppt Arialq ~/ppq ~ §ppppppppsq ~ ¨psq ~ ¬  wñppppq ~q ~q ~psq ~ ´  wñppppq ~q ~psq ~ ­  wñppppq ~q ~psq ~ ¹  wñppppq ~q ~psq ~ ½  wñppppq ~q ~ppppppppppppppppq ~6  wñ        ppq ~Þsq ~à   &uq ~ã   sq ~åt "ACESSE:\r\n" +
sq ~åt boleto.instrucao2t java.lang.Stringppppppppppsq ~	  wñ           v     pq ~ q ~ #ppppppq ~ 7ppppq ~ :  wñpppppt Arialppq ~½pppppppppsq ~ ¨psq ~ ¬  wñppppq ~+q ~+q ~)psq ~ ´  wñppppq ~+q ~+psq ~ ­  wñppppq ~+q ~+psq ~ ¹  wñppppq ~+q ~+psq ~ ½  wñppppq ~+q ~+ppppppppppppppppq ~ Ã  wñ        ppq ~Þsq ~à   'uq ~ã   sq ~åt banco.bancot java.lang.Stringppppppppppsq ~	  wñ          ä     ³pq ~ q ~ #ppppppq ~ 7ppppq ~ :  wñpppppt Arialq ~pppppppppppsq ~ ¨psq ~ ¬  wñppppq ~8q ~8q ~6psq ~ ´  wñppppq ~8q ~8psq ~ ­  wñppppq ~8q ~8psq ~ ¹  wñppppq ~8q ~8psq ~ ½  wñppppq ~8q ~8ppppppppppppppppq ~ Ã  wñ       ppq ~Þsq ~à   (uq ~ã   sq ~åt boleto.instrucao4t java.lang.Stringppppppppppsq ~	  wñ          ä     Ãpq ~ q ~ #sq ~ ¯    ÿÿ  ppppppppq ~ 7ppppq ~ :  wñpppppt Arialq ~pppppppppppsq ~ ¨psq ~ ¬  wñppppq ~Fq ~Fq ~Cpsq ~ ´  wñppppq ~Fq ~Fpsq ~ ­  wñppppq ~Fq ~Fpsq ~ ¹  wñppppq ~Fq ~Fpsq ~ ½  wñppppq ~Fq ~Fppppppppppppppppq ~  wñ        ppq ~Þsq ~à   )uq ~ã   sq ~åt boleto.instrucao5t java.lang.Stringppppppppppxp  wñ  pp~r .net.sf.jasperreports.engine.type.SplitTypeEnum          xq ~ t STRETCHppsr java.util.HashSetºD¸·4  xpw   ?@     t "net.sf.jasperreports.engine.data.*t net.sf.jasperreports.engine.*t java.util.*xt javapsr .net.sf.jasperreports.engine.base.JRBaseDataset      'Ø I PSEUDO_SERIAL_VERSION_UIDZ isMainB whenResourceMissingType[ fieldst &[Lnet/sf/jasperreports/engine/JRField;L filterExpressionq ~ [ groupst &[Lnet/sf/jasperreports/engine/JRGroup;L nameq ~ [ 
parameterst *[Lnet/sf/jasperreports/engine/JRParameter;L 
propertiesMapq ~ 1L queryt %Lnet/sf/jasperreports/engine/JRQuery;L resourceBundleq ~ L scriptletClassq ~ [ 
scriptletst *[Lnet/sf/jasperreports/engine/JRScriptlet;[ 
sortFieldst *[Lnet/sf/jasperreports/engine/JRSortField;[ 	variablest )[Lnet/sf/jasperreports/engine/JRVariable;L whenResourceMissingTypeValuet >Lnet/sf/jasperreports/engine/type/WhenResourceMissingTypeEnum;xp  wñ ur &[Lnet.sf.jasperreports.engine.JRField;<ßÇN*òp  xp   sr ,net.sf.jasperreports.engine.base.JRBaseField      'Ø L descriptionq ~ L nameq ~ L 
propertiesMapq ~ 1L valueClassNameq ~ L valueClassRealNameq ~ xppt bancosr +net.sf.jasperreports.engine.JRPropertiesMap      'Ø L baseq ~ 1L propertiesListq ~ !L 
propertiesMapt Ljava/util/Map;xppppt java.lang.Objectpsq ~fpt banco.numeroFormattedsq ~ipppt java.lang.Stringpsq ~fpt banco.linhaDigitavelsq ~ipppt java.lang.Stringpsq ~fpt banco.codigoBarrassq ~ipppt java.lang.Stringpsq ~fpt boleto.cedentesq ~ipppt java.lang.Stringpsq ~fpt boleto.localPagamentosq ~ipppt java.lang.Stringpsq ~fpt boleto.dataDocumentosq ~ipppt java.lang.Stringpsq ~fpt boleto.especieDocumentosq ~ipppt java.lang.Stringpsq ~fpt 
boleto.aceitesq ~ipppt java.lang.Stringpsq ~fpt boleto.dataProcessamentosq ~ipppt java.lang.Stringpsq ~fpt boleto.carteirasq ~ipppt java.lang.Stringpsq ~fpt boleto.dataVencimentosq ~ipppt java.lang.Stringpsq ~fpt boleto.valorBoletosq ~ipppt java.lang.Stringpsq ~fpt boleto.nomeSacadosq ~ipppt java.lang.Stringpsq ~fpt boleto.nossoNumerosq ~ipppt java.lang.Stringpsq ~fpt boleto.enderecoSacadosq ~ipppt java.lang.Stringpsq ~fpt boleto.cepSacadosq ~ipppt java.lang.Stringpsq ~fpt boleto.cidadeSacadosq ~ipppt java.lang.Stringpsq ~fpt boleto.ufSacadosq ~ipppt java.lang.Stringpsq ~fpt boleto.descricoesDTsq ~ipppt java.lang.Objectpsq ~fpt  banco.agenciaCodCedenteFormattedsq ~ipppt java.lang.Stringpsq ~fpt boleto.instrucao1sq ~ipppt java.lang.Stringpsq ~fpt boleto.instrucao2sq ~ipppt java.lang.Stringpsq ~fpt banco.bancosq ~ipppt java.lang.Stringpsq ~fpt boleto.instrucao3sq ~ipppt java.lang.Stringpsq ~fpt boleto.instrucao4sq ~ipppt java.lang.Stringpsq ~fpt boleto.instrucao5sq ~ipppt java.lang.Stringpsq ~fpt boleto.bairroSacadosq ~ipppt java.lang.Stringpppt boletour *[Lnet.sf.jasperreports.engine.JRParameter;" *Ã`!  xp   sr 0net.sf.jasperreports.engine.base.JRBaseParameter      'Ø 	Z isForPromptingZ isSystemDefinedL defaultValueExpressionq ~ L descriptionq ~ L nameq ~ L nestedTypeNameq ~ L 
propertiesMapq ~ 1L valueClassNameq ~ L valueClassRealNameq ~ xpppt REPORT_PARAMETERS_MAPpsq ~ipppt 
java.util.Mappsq ~Üppt 
JASPER_REPORTpsq ~ipppt (net.sf.jasperreports.engine.JasperReportpsq ~Üppt REPORT_CONNECTIONpsq ~ipppt java.sql.Connectionpsq ~Üppt REPORT_MAX_COUNTpsq ~ipppt java.lang.Integerpsq ~Üppt REPORT_DATA_SOURCEpsq ~ipppq ~tpsq ~Üppt REPORT_SCRIPTLETpsq ~ipppt /net.sf.jasperreports.engine.JRAbstractScriptletpsq ~Üppt 
REPORT_LOCALEpsq ~ipppt java.util.Localepsq ~Üppt REPORT_RESOURCE_BUNDLEpsq ~ipppt java.util.ResourceBundlepsq ~Üppt REPORT_TIME_ZONEpsq ~ipppt java.util.TimeZonepsq ~Üppt REPORT_FORMAT_FACTORYpsq ~ipppt .net.sf.jasperreports.engine.util.FormatFactorypsq ~Üppt REPORT_CLASS_LOADERpsq ~ipppt java.lang.ClassLoaderpsq ~Üppt REPORT_URL_HANDLER_FACTORYpsq ~ipppt  java.net.URLStreamHandlerFactorypsq ~Üppt REPORT_FILE_RESOLVERpsq ~ipppt -net.sf.jasperreports.engine.util.FileResolverpsq ~Üppt REPORT_TEMPLATESpsq ~ipppt java.util.Collectionpsq ~Üppt SORT_FIELDSpsq ~ipppt java.util.Listpsq ~Üppt REPORT_VIRTUALIZERpsq ~ipppt )net.sf.jasperreports.engine.JRVirtualizerpsq ~Üppt IS_IGNORE_PAGINATIONpsq ~ipppq ~psq ~Ü  sq ~à    uq ~ã   sq ~åt "D:\\PactoJ\\Desenvolvimento\\Sistemas\\ZillyonWeb\\ramos\\Tijuca\\src\\main\\resources\\relatorio\\designRelatorio\\financeiro\\boletos\\"t java.lang.Stringppt 
SUBREPORT_DIRpsq ~ipppq ~$psq ~Ü ppt nomeEmpresapsq ~ipppt java.lang.Stringpsq ~Ü ppt empresaVO.enderecopsq ~ipppt java.lang.Stringpsq ~Ü ppt empresaVO.fonepsq ~ipppt java.lang.Stringpsq ~Ü ppt logoPadraoRelatoriopsq ~ipppt java.io.InputStreampsq ~Ü ppt 
propagandapsq ~ipppt java.io.InputStreampsq ~ipsq ~ $   w   t ireport.scriptlethandlingt ireport.encodingt ireport.zoomt 	ireport.xt 	ireport.yxsr java.util.HashMapÚÁÃ`Ñ F 
loadFactorI 	thresholdxp?@     w      q ~?t 2.0q ~>t UTF-8q ~@t 205q ~At 768q ~=t 0xsr ,net.sf.jasperreports.engine.base.JRBaseQuery      'Ø [ chunkst +[Lnet/sf/jasperreports/engine/JRQueryChunk;L languageq ~ xppt sqlppppur )[Lnet.sf.jasperreports.engine.JRVariable;bæ|,·D  xp   sr /net.sf.jasperreports.engine.base.JRBaseVariable      'Ø I PSEUDO_SERIAL_VERSION_UIDB calculationB 
incrementTypeZ isSystemDefinedB 	resetTypeL calculationValuet 2Lnet/sf/jasperreports/engine/type/CalculationEnum;L 
expressionq ~ L incrementGroupq ~ 0L incrementTypeValuet 4Lnet/sf/jasperreports/engine/type/IncrementTypeEnum;L incrementerFactoryClassNameq ~ L incrementerFactoryClassRealNameq ~ L initialValueExpressionq ~ L nameq ~ L 
resetGroupq ~ 0L resetTypeValuet 0Lnet/sf/jasperreports/engine/type/ResetTypeEnum;L valueClassNameq ~ L valueClassRealNameq ~ xp  wî   ~r 0net.sf.jasperreports.engine.type.CalculationEnum          xq ~ t SYSTEMpp~r 2net.sf.jasperreports.engine.type.IncrementTypeEnum          xq ~ t NONEppsq ~à   uq ~ã   sq ~åt new java.lang.Integer(1)q ~ìpt PAGE_NUMBERp~r .net.sf.jasperreports.engine.type.ResetTypeEnum          xq ~ t REPORTq ~ìpsq ~O  wî   q ~Uppq ~Xppsq ~à   uq ~ã   sq ~åt new java.lang.Integer(1)q ~ìpt 
COLUMN_NUMBERp~q ~_t PAGEq ~ìpsq ~O  wî   ~q ~Tt COUNTsq ~à   uq ~ã   sq ~åt new java.lang.Integer(1)q ~ìppq ~Xppsq ~à   uq ~ã   sq ~åt new java.lang.Integer(0)q ~ìpt REPORT_COUNTpq ~`q ~ìpsq ~O  wî   q ~ksq ~à   uq ~ã   sq ~åt new java.lang.Integer(1)q ~ìppq ~Xppsq ~à   uq ~ã   sq ~åt new java.lang.Integer(0)q ~ìpt 
PAGE_COUNTpq ~hq ~ìpsq ~O  wî   q ~ksq ~à   uq ~ã   sq ~åt new java.lang.Integer(1)q ~ìppq ~Xppsq ~à   uq ~ã   sq ~åt new java.lang.Integer(0)q ~ìpt COLUMN_COUNTp~q ~_t COLUMNq ~ìp~r <net.sf.jasperreports.engine.type.WhenResourceMissingTypeEnum          xq ~ t NULLq ~Ùp~r 0net.sf.jasperreports.engine.type.OrientationEnum          xq ~ t PORTRAITpp~r /net.sf.jasperreports.engine.type.PrintOrderEnum          xq ~ t VERTICALpppp~r 3net.sf.jasperreports.engine.type.WhenNoDataTypeEnum          xq ~ t NO_PAGESsr 6net.sf.jasperreports.engine.design.JRReportCompileData      'Ø L crosstabCompileDataq ~jL datasetCompileDataq ~jL mainDatasetCompileDataq ~ xpsq ~B?@     w       xsq ~B?@     w       xur [B¬óøTà  xp  0Êþº¾   . boleto_1483008901552_525615  ,net/sf/jasperreports/engine/fill/JREvaluator  parameter_REPORT_LOCALE 2Lnet/sf/jasperreports/engine/fill/JRFillParameter; parameter_JASPER_REPORT parameter_REPORT_VIRTUALIZER parameter_REPORT_TIME_ZONE parameter_SORT_FIELDS parameter_REPORT_FILE_RESOLVER parameter_logoPadraoRelatorio parameter_REPORT_SCRIPTLET parameter_REPORT_PARAMETERS_MAP parameter_REPORT_CONNECTION parameter_REPORT_CLASS_LOADER parameter_REPORT_DATA_SOURCE $parameter_REPORT_URL_HANDLER_FACTORY parameter_IS_IGNORE_PAGINATION parameter_SUBREPORT_DIR parameter_REPORT_FORMAT_FACTORY parameter_REPORT_MAX_COUNT parameter_empresaVO46endereco parameter_nomeEmpresa parameter_REPORT_TEMPLATES parameter_empresaVO46fone parameter_propaganda  parameter_REPORT_RESOURCE_BUNDLE field_boleto46cedente .Lnet/sf/jasperreports/engine/fill/JRFillField; field_boleto46enderecoSacado field_banco46codigoBarras 'field_banco46agenciaCodCedenteFormatted field_boleto46nomeSacado field_boleto46aceite field_banco46banco field_boleto46valorBoleto field_boleto46especieDocumento field_banco46numeroFormatted field_banco field_boleto46dataVencimento field_boleto46dataProcessamento field_boleto46ufSacado field_boleto46bairroSacado field_boleto46cepSacado field_boleto46dataDocumento field_boleto46instrucao5 field_boleto46instrucao3 field_banco46linhaDigitavel field_boleto46instrucao4 field_boleto46nossoNumero field_boleto46localPagamento field_boleto46cidadeSacado field_boleto46carteira field_boleto46instrucao1 field_boleto46instrucao2 field_boleto46descricoesDT variable_PAGE_NUMBER 1Lnet/sf/jasperreports/engine/fill/JRFillVariable; variable_COLUMN_NUMBER variable_REPORT_COUNT variable_PAGE_COUNT variable_COLUMN_COUNT <init> ()V Code @ A
  C  	  E  	  G  	  I 	 	  K 
 	  M  	  O  	  Q 
 	  S  	  U  	  W  	  Y  	  [  	  ]  	  _  	  a  	  c  	  e  	  g  	  i  	  k  	  m  	  o  	  q  	  s  	  u   	  w ! 	  y " 	  { # 	  } $ 	   % 	   & 	   ' 	   ( 	   ) 	   * 	   + 	   , 	   - 	   . 	   / 	   0 	   1 	   2 	   3 	   4 	   5 	  ¡ 6 	  £ 7 	  ¥ 8 	  § 9 	  © : ;	  « < ;	  ­ = ;	  ¯ > ;	  ± ? ;	  ³ LineNumberTable customizedInit 0(Ljava/util/Map;Ljava/util/Map;Ljava/util/Map;)V 
initParams (Ljava/util/Map;)V ¸ ¹
  º 
initFields ¼ ¹
  ½ initVars ¿ ¹
  À 
REPORT_LOCALE Â 
java/util/Map Ä get &(Ljava/lang/Object;)Ljava/lang/Object; Æ Ç Å È 0net/sf/jasperreports/engine/fill/JRFillParameter Ê 
JASPER_REPORT Ì REPORT_VIRTUALIZER Î REPORT_TIME_ZONE Ð SORT_FIELDS Ò REPORT_FILE_RESOLVER Ô logoPadraoRelatorio Ö REPORT_SCRIPTLET Ø REPORT_PARAMETERS_MAP Ú REPORT_CONNECTION Ü REPORT_CLASS_LOADER Þ REPORT_DATA_SOURCE à REPORT_URL_HANDLER_FACTORY â IS_IGNORE_PAGINATION ä 
SUBREPORT_DIR æ REPORT_FORMAT_FACTORY è REPORT_MAX_COUNT ê empresaVO.endereco ì nomeEmpresa î REPORT_TEMPLATES ð empresaVO.fone ò 
propaganda ô REPORT_RESOURCE_BUNDLE ö boleto.cedente ø ,net/sf/jasperreports/engine/fill/JRFillField ú boleto.enderecoSacado ü banco.codigoBarras þ  banco.agenciaCodCedenteFormatted  boleto.nomeSacado 
boleto.aceite banco.banco boleto.valorBoleto boleto.especieDocumento
 banco.numeroFormatted banco boleto.dataVencimento boleto.dataProcessamento boleto.ufSacado boleto.bairroSacado boleto.cepSacado boleto.dataDocumento boleto.instrucao5 boleto.instrucao3 banco.linhaDigitavel  boleto.instrucao4" boleto.nossoNumero$ boleto.localPagamento& boleto.cidadeSacado( boleto.carteira* boleto.instrucao1, boleto.instrucao2. boleto.descricoesDT0 PAGE_NUMBER2 /net/sf/jasperreports/engine/fill/JRFillVariable4 
COLUMN_NUMBER6 REPORT_COUNT8 
PAGE_COUNT: COLUMN_COUNT< evaluate (I)Ljava/lang/Object; 
Exceptions java/lang/ThrowableA {D:\PactoJ\Desenvolvimento\Sistemas\ZillyonWeb\ramos\Tijuca\src\main\resources\relatorio\designRelatorio\financeiro\boletos\C java/lang/IntegerE (I)V @G
FH getValue ()Ljava/lang/Object;JK
 ûL java/lang/StringN (it/businesslogic/ireport/barcode/BcImageP getBarcodeImage I(ILjava/lang/Object;ZZLjava/lang/String;II)Ljava/awt/image/BufferedImage;RS
QT
 ËL java/io/InputStreamW java/lang/StringBufferY Total: [ (Ljava/lang/String;)V @]
Z^ append ,(Ljava/lang/String;)Ljava/lang/StringBuffer;`a
Zb toString ()Ljava/lang/String;de
Zf (net/sf/jasperreports/engine/JRDataSourceh valueOf &(Ljava/lang/Object;)Ljava/lang/String;jk
Ol boletoClube_itens.jaspern  p trimre
Os  - u   w isEmpty ()Zyz
O{ length ()I}~
O -000 	substring (II)Ljava/lang/String;
O - (I)Ljava/lang/String;
O java/lang/Boolean (Z)Ljava/lang/Boolean;j
 	ACESSE:
 evaluateOld getOldValueK
 û evaluateEstimated 
SourceFile !     8                 	     
               
                                                                                                !     "     #     $     %     &     '     (     )     *     +     ,     -     .     /     0     1     2     3     4     5     6     7     8     9     : ;    < ;    = ;    > ;    ? ;     @ A  B      *· D*µ F*µ H*µ J*µ L*µ N*µ P*µ R*µ T*µ V*µ X*µ Z*µ \*µ ^*µ `*µ b*µ d*µ f*µ h*µ j*µ l*µ n*µ p*µ r*µ t*µ v*µ x*µ z*µ |*µ ~*µ *µ *µ *µ *µ *µ *µ *µ *µ *µ *µ *µ *µ *µ *µ *µ *µ  *µ ¢*µ ¤*µ ¦*µ ¨*µ ª*µ ¬*µ ®*µ °*µ ²*µ ´±    µ   ê :      	          ! " " ' # , $ 1 % 6 & ; ' @ ( E ) J * O + T , Y - ^ . c / h 0 m 1 r 2 w 3 | 4  5  6  7  8  9  :  ; ¤ < © = ® > ³ ? ¸ @ ½ A Â B Ç C Ì D Ñ E Ö F Û G à H å I ê J ï K ô L ù M þ N O P
 Q R S   ¶ ·  B   4     *+· »*,· ¾*-· Á±    µ       _  ` 
 a  b  ¸ ¹  B      *+Ã¹ É À ËÀ Ëµ F*+Í¹ É À ËÀ Ëµ H*+Ï¹ É À ËÀ Ëµ J*+Ñ¹ É À ËÀ Ëµ L*+Ó¹ É À ËÀ Ëµ N*+Õ¹ É À ËÀ Ëµ P*+×¹ É À ËÀ Ëµ R*+Ù¹ É À ËÀ Ëµ T*+Û¹ É À ËÀ Ëµ V*+Ý¹ É À ËÀ Ëµ X*+ß¹ É À ËÀ Ëµ Z*+á¹ É À ËÀ Ëµ \*+ã¹ É À ËÀ Ëµ ^*+å¹ É À ËÀ Ëµ `*+ç¹ É À ËÀ Ëµ b*+é¹ É À ËÀ Ëµ d*+ë¹ É À ËÀ Ëµ f*+í¹ É À ËÀ Ëµ h*+ï¹ É À ËÀ Ëµ j*+ñ¹ É À ËÀ Ëµ l*+ó¹ É À ËÀ Ëµ n*+õ¹ É À ËÀ Ëµ p*+÷¹ É À ËÀ Ëµ r±    µ   b    j  k $ l 6 m H n Z o l p ~ q  r ¢ s ´ t Æ u Ø v ê w ü x y  z2 {D |V }h ~z     ¼ ¹  B      *+ù¹ É À ûÀ ûµ t*+ý¹ É À ûÀ ûµ v*+ÿ¹ É À ûÀ ûµ x*+¹ É À ûÀ ûµ z*+¹ É À ûÀ ûµ |*+¹ É À ûÀ ûµ ~*+¹ É À ûÀ ûµ *+	¹ É À ûÀ ûµ *+¹ É À ûÀ ûµ *+
¹ É À ûÀ ûµ *+¹ É À ûÀ ûµ *+¹ É À ûÀ ûµ *+¹ É À ûÀ ûµ *+¹ É À ûÀ ûµ *+¹ É À ûÀ ûµ *+¹ É À ûÀ ûµ *+¹ É À ûÀ ûµ *+¹ É À ûÀ ûµ *+¹ É À ûÀ ûµ *+!¹ É À ûÀ ûµ *+#¹ É À ûÀ ûµ *+%¹ É À ûÀ ûµ *+'¹ É À ûÀ ûµ  *+)¹ É À ûÀ ûµ ¢*++¹ É À ûÀ ûµ ¤*+-¹ É À ûÀ ûµ ¦*+/¹ É À ûÀ ûµ ¨*+1¹ É À ûÀ ûµ ª±    µ   v       $  6  I  \  o      ¨  »  Î  á  ô   - @ S f y   ²  Å ¡Ø ¢ë £þ ¤ ¥  ¿ ¹  B        `*+3¹ É À5À5µ ¬*+7¹ É À5À5µ ®*+9¹ É À5À5µ °*+;¹ É À5À5µ ²*+=¹ É À5À5µ ´±    µ       ­  ® & ¯ 9 ° L ± _ ² >? @    B B  Ý    YMª  T       )   µ   ¼   È   Ô   à   ì   ø        4  B  P  ^  l  z      ¤  ²  À  Î  Ü  ê  ø      "  0  N  \  }      Æ  Ô  â  ð    -  ;  IDM§»FY·IM§»FY·IM§»FY·IM§w»FY·IM§k»FY·IM§_»FY·IM§S»FY·IM§G»FY·IM§;
*´ x¶MÀO¸UM§#*´ ¶MÀOM§*´ ¶MÀOM§*´ t¶MÀOM§ù*´  ¶MÀOM§ë*´ ¶MÀOM§Ý*´ ¶MÀOM§Ï*´ ~¶MÀOM§Á*´ ¶MÀOM§³*´ ¤¶MÀOM§¥*´ ¶MÀOM§*´ ¶MÀOM§*´ |¶MÀOM§{*´ ¶MÀOM§m*´ |¶MÀOM§_*´ n¶VÀOM§Q*´ j¶VÀOM§C*´ R¶VÀXM§5*´ h¶VÀOM§'»ZY\·_*´ ¶MÀO¶c¶gM§	*´ ª¶MÀiM§û»ZY*´ b¶VÀO¸m·_o¶c¶gM§Ú*´ ¶MÀOM§Ì*´ v¶MÀOM§¾»ZY*´ ¶MÀOÇ 	q§ #»ZY*´ ¶MÀO¶t¸m·_v¶c¶g¸m·_*´ ¢¶MÀOÇ 	x§ #»ZY*´ ¢¶MÀO¶t¸m·_v¶c¶g¶c*´ ¶MÀOÇ 	x§  »ZY*´ ¶MÀO¸m·_v¶c¶g¶c*´ ¶MÀO¶t¶| 	q§ g*´ ¶MÀO¶£ #»ZY*´ ¶MÀO¸m·_¶c¶g§ 6»ZY*´ ¶MÀO¶¸m·_¶c*´ ¶MÀO¶¶c¶g¶c¶gM§ *´ z¶MÀOM§ *´ p¶VÀXM§ u*´ ¦¶MÀOM§ g*´ ¨¶MÀO¶t¶ § ¸M§ H»ZY·_*´ ¨¶MÀO¶c¶gM§ **´ ¶MÀOM§ *´ ¶MÀOM§ *´ ¶MÀOM,°    µ  r \   º  ¼ ¸ À ¼ Á ¿ Å È Æ Ë Ê Ô Ë × Ï à Ð ã Ô ì Õ ï Ù ø Ú û Þ ß ã ä è é í4 î7 òB óE ÷P øS ü^ ýaloz}¤§²µÀ Ã$Î%Ñ)Ü*ß.ê/í3ø4û89	=>B"C%G0H3LNMQQ\R_V}W[\`aeÙfgBhÂeÆiÉmÔn×râsåwðxó|})-0;>ILW ? @    B B  Ý    YMª  T       )   µ   ¼   È   Ô   à   ì   ø        4  B  P  ^  l  z      ¤  ²  À  Î  Ü  ê  ø      "  0  N  \  }      Æ  Ô  â  ð    -  ;  IDM§»FY·IM§»FY·IM§»FY·IM§w»FY·IM§k»FY·IM§_»FY·IM§S»FY·IM§G»FY·IM§;
*´ x¶ÀO¸UM§#*´ ¶ÀOM§*´ ¶ÀOM§*´ t¶ÀOM§ù*´  ¶ÀOM§ë*´ ¶ÀOM§Ý*´ ¶ÀOM§Ï*´ ~¶ÀOM§Á*´ ¶ÀOM§³*´ ¤¶ÀOM§¥*´ ¶ÀOM§*´ ¶ÀOM§*´ |¶ÀOM§{*´ ¶ÀOM§m*´ |¶ÀOM§_*´ n¶VÀOM§Q*´ j¶VÀOM§C*´ R¶VÀXM§5*´ h¶VÀOM§'»ZY\·_*´ ¶ÀO¶c¶gM§	*´ ª¶ÀiM§û»ZY*´ b¶VÀO¸m·_o¶c¶gM§Ú*´ ¶ÀOM§Ì*´ v¶ÀOM§¾»ZY*´ ¶ÀOÇ 	q§ #»ZY*´ ¶ÀO¶t¸m·_v¶c¶g¸m·_*´ ¢¶ÀOÇ 	x§ #»ZY*´ ¢¶ÀO¶t¸m·_v¶c¶g¶c*´ ¶ÀOÇ 	x§  »ZY*´ ¶ÀO¸m·_v¶c¶g¶c*´ ¶ÀO¶t¶| 	q§ g*´ ¶ÀO¶£ #»ZY*´ ¶ÀO¸m·_¶c¶g§ 6»ZY*´ ¶ÀO¶¸m·_¶c*´ ¶ÀO¶¶c¶g¶c¶gM§ *´ z¶ÀOM§ *´ p¶VÀXM§ u*´ ¦¶ÀOM§ g*´ ¨¶ÀO¶t¶ § ¸M§ H»ZY·_*´ ¨¶ÀO¶c¶gM§ **´ ¶ÀOM§ *´ ¶ÀOM§ *´ ¶ÀOM,°    µ  r \  ¢ ¤ ¸¨ ¼© ¿­ È® Ë² Ô³ ×· à¸ ã¼ ì½ ïÁ øÂ ûÆÇËÌÐÑÕ4Ö7ÚBÛEßPàSä^åaélêoîzï}óôøùý¤þ§²µÀÃÎ
ÑÜßêíøû !	%&*"+%/0034N5Q9\:_>}?CDHIMÙNOBPÂMÆQÉUÔV×Zâ[å_ð`ódeij)i-k0o;p>tIuLyW ? @    B B  Ý    YMª  T       )   µ   ¼   È   Ô   à   ì   ø        4  B  P  ^  l  z      ¤  ²  À  Î  Ü  ê  ø      "  0  N  \  }      Æ  Ô  â  ð    -  ;  IDM§»FY·IM§»FY·IM§»FY·IM§w»FY·IM§k»FY·IM§_»FY·IM§S»FY·IM§G»FY·IM§;
*´ x¶MÀO¸UM§#*´ ¶MÀOM§*´ ¶MÀOM§*´ t¶MÀOM§ù*´  ¶MÀOM§ë*´ ¶MÀOM§Ý*´ ¶MÀOM§Ï*´ ~¶MÀOM§Á*´ ¶MÀOM§³*´ ¤¶MÀOM§¥*´ ¶MÀOM§*´ ¶MÀOM§*´ |¶MÀOM§{*´ ¶MÀOM§m*´ |¶MÀOM§_*´ n¶VÀOM§Q*´ j¶VÀOM§C*´ R¶VÀXM§5*´ h¶VÀOM§'»ZY\·_*´ ¶MÀO¶c¶gM§	*´ ª¶MÀiM§û»ZY*´ b¶VÀO¸m·_o¶c¶gM§Ú*´ ¶MÀOM§Ì*´ v¶MÀOM§¾»ZY*´ ¶MÀOÇ 	q§ #»ZY*´ ¶MÀO¶t¸m·_v¶c¶g¸m·_*´ ¢¶MÀOÇ 	x§ #»ZY*´ ¢¶MÀO¶t¸m·_v¶c¶g¶c*´ ¶MÀOÇ 	x§  »ZY*´ ¶MÀO¸m·_v¶c¶g¶c*´ ¶MÀO¶t¶| 	q§ g*´ ¶MÀO¶£ #»ZY*´ ¶MÀO¸m·_¶c¶g§ 6»ZY*´ ¶MÀO¶¸m·_¶c*´ ¶MÀO¶¶c¶g¶c¶gM§ *´ z¶MÀOM§ *´ p¶VÀXM§ u*´ ¦¶MÀOM§ g*´ ¨¶MÀO¶t¶ § ¸M§ H»ZY·_*´ ¨¶MÀO¶c¶gM§ **´ ¶MÀOM§ *´ ¶MÀOM§ *´ ¶MÀOM,°    µ  r \    ¸ ¼ ¿ È Ë Ô × à  ã¤ ì¥ ï© øª û®¯³´¸¹½4¾7ÂBÃEÇPÈSÌ^ÍaÑlÒoÖz×}ÛÜàáå¤æ§ê²ëµïÀðÃôÎõÑùÜúßþêÿíøû		
"%03NQ!\"_&}'+,015Ù67B8Â5Æ9É=Ô>×BâCåGðHóLMQR)Q-S0W;X>\I]LaWi     t _1483008901552_525615t 2net.sf.jasperreports.engine.design.JRJavacCompiler