¬í sr (net.sf.jasperreports.engine.JasperReport      'Ø L compileDatat Ljava/io/Serializable;L compileNameSuffixt Ljava/lang/String;L 
compilerClassq ~ xr -net.sf.jasperreports.engine.base.JRBaseReport      'Ø +I PSEUDO_SERIAL_VERSION_UIDI bottomMarginI columnCountI 
columnSpacingI columnWidthZ ignorePaginationZ isFloatColumnFooterZ isSummaryNewPageZ  isSummaryWithPageHeaderAndFooterZ isTitleNewPageI 
leftMarginB orientationI 
pageHeightI 	pageWidthB 
printOrderI rightMarginI 	topMarginB whenNoDataTypeL 
backgroundt $Lnet/sf/jasperreports/engine/JRBand;L columnFooterq ~ L columnHeaderq ~ [ datasetst ([Lnet/sf/jasperreports/engine/JRDataset;L defaultFontt *Lnet/sf/jasperreports/engine/JRReportFont;L defaultStylet %Lnet/sf/jasperreports/engine/JRStyle;L detailq ~ L 
detailSectiont 'Lnet/sf/jasperreports/engine/JRSection;[ fontst +[Lnet/sf/jasperreports/engine/JRReportFont;L formatFactoryClassq ~ L 
importsSett Ljava/util/Set;L languageq ~ L lastPageFooterq ~ L mainDatasett 'Lnet/sf/jasperreports/engine/JRDataset;L nameq ~ L noDataq ~ L orientationValuet 2Lnet/sf/jasperreports/engine/type/OrientationEnum;L 
pageFooterq ~ L 
pageHeaderq ~ L printOrderValuet 1Lnet/sf/jasperreports/engine/type/PrintOrderEnum;[ stylest &[Lnet/sf/jasperreports/engine/JRStyle;L summaryq ~ [ 	templatest /[Lnet/sf/jasperreports/engine/JRReportTemplate;L titleq ~ L whenNoDataTypeValuet 5Lnet/sf/jasperreports/engine/type/WhenNoDataTypeEnum;xp  wî             N           J  S          pppppppsr .net.sf.jasperreports.engine.base.JRBaseSection      'Ø [ bandst %[Lnet/sf/jasperreports/engine/JRBand;xpur %[Lnet.sf.jasperreports.engine.JRBand;Ý~ìÊ5  xp   sr +net.sf.jasperreports.engine.base.JRBaseBand      'Ø I PSEUDO_SERIAL_VERSION_UIDI heightZ isSplitAllowedL printWhenExpressiont *Lnet/sf/jasperreports/engine/JRExpression;L 	splitTypet Ljava/lang/Byte;L splitTypeValuet 0Lnet/sf/jasperreports/engine/type/SplitTypeEnum;xr 3net.sf.jasperreports.engine.base.JRBaseElementGroup      'Ø L childrent Ljava/util/List;L elementGroupt ,Lnet/sf/jasperreports/engine/JRElementGroup;xpsr java.util.ArrayListxÒÇa I sizexp   w   sr +net.sf.jasperreports.engine.base.JRBaseLine      'Ø I PSEUDO_SERIAL_VERSION_UIDB 	directionL directionValuet 4Lnet/sf/jasperreports/engine/type/LineDirectionEnum;xr 5net.sf.jasperreports.engine.base.JRBaseGraphicElement      'Ø I PSEUDO_SERIAL_VERSION_UIDL fillq ~ L 	fillValuet +Lnet/sf/jasperreports/engine/type/FillEnum;L linePent #Lnet/sf/jasperreports/engine/JRPen;L penq ~ xr .net.sf.jasperreports.engine.base.JRBaseElement      'Ø I PSEUDO_SERIAL_VERSION_UIDI heightZ isPrintInFirstWholeBandZ isPrintRepeatedValuesZ isPrintWhenDetailOverflowsZ isRemoveLineWhenBlankB positionTypeB stretchTypeI widthI xI yL 	backcolort Ljava/awt/Color;L defaultStyleProvidert 4Lnet/sf/jasperreports/engine/JRDefaultStyleProvider;L elementGroupq ~ L 	forecolorq ~ 'L keyq ~ L modeq ~ L 	modeValuet +Lnet/sf/jasperreports/engine/type/ModeEnum;L parentStyleq ~ L parentStyleNameReferenceq ~ L positionTypeValuet 3Lnet/sf/jasperreports/engine/type/PositionTypeEnum;L printWhenExpressionq ~ L printWhenGroupChangest %Lnet/sf/jasperreports/engine/JRGroup;L 
propertiesMapt -Lnet/sf/jasperreports/engine/JRPropertiesMap;[ propertyExpressionst 3[Lnet/sf/jasperreports/engine/JRPropertyExpression;L stretchTypeValuet 2Lnet/sf/jasperreports/engine/type/StretchTypeEnum;xp  wî          	     pq ~ q ~ pt line-20pppp~r 1net.sf.jasperreports.engine.type.PositionTypeEnum          xr java.lang.Enum          xpt FIX_RELATIVE_TO_TOPpppp~r 0net.sf.jasperreports.engine.type.StretchTypeEnum          xq ~ 2t 
NO_STRETCH  wîppsr *net.sf.jasperreports.engine.base.JRBasePen      'Ø I PSEUDO_SERIAL_VERSION_UIDL 	lineColorq ~ 'L 	lineStyleq ~ L lineStyleValuet 0Lnet/sf/jasperreports/engine/type/LineStyleEnum;L 	lineWidtht Ljava/lang/Float;L penContainert ,Lnet/sf/jasperreports/engine/JRPenContainer;xp  wîpp~r .net.sf.jasperreports.engine.type.LineStyleEnum          xq ~ 2t DASHEDsr java.lang.FloatÚíÉ¢Û<ðì F valuexr java.lang.Number¬à  xp?  q ~ /p  wî ~r 2net.sf.jasperreports.engine.type.LineDirectionEnum          xq ~ 2t TOP_DOWNsq ~ !  wî          
     pq ~ q ~ pt line-1ppppq ~ 3ppppq ~ 6  wîppsq ~ 8  wîpp~q ~ =t SOLIDsq ~ @?  q ~ Fp  wî q ~ Dsq ~ !  wî              p  pq ~ q ~ pt line-2ppppq ~ 3ppppq ~ 6  wîppsq ~ 8  wîppq ~ Isq ~ @?  q ~ Lp  wî q ~ Dsq ~ !  wî                pq ~ q ~ pt line-3ppppq ~ 3ppppq ~ 6  wîppsq ~ 8  wîppq ~ Isq ~ @?  q ~ Pp  wî q ~ Dsq ~ !  wî          	     ,pq ~ q ~ pt line-4ppppq ~ 3ppppq ~ 6  wîppsq ~ 8  wîppq ~ Isq ~ @?  q ~ Tp  wî q ~ Dsq ~ !  wî          	     =pq ~ q ~ pt line-5ppppq ~ 3ppppq ~ 6  wîppsq ~ 8  wîppq ~ Isq ~ @?  q ~ Xp  wî q ~ Dsq ~ !  wî          	     Npq ~ q ~ pt line-6ppppq ~ 3ppppq ~ 6  wîppsq ~ 8  wîppq ~ Isq ~ @?  q ~ \p  wî q ~ Dsq ~ !  wî          
     _pq ~ q ~ pt line-7ppppq ~ 3ppppq ~ 6  wîppsq ~ 8  wîppq ~ Isq ~ @?  q ~ `p  wî q ~ Dsq ~ !  wî             {  pq ~ q ~ pt line-8ppppq ~ 3ppppq ~ 6  wîppsq ~ 8  wîppq ~ Isq ~ @?  q ~ dp  wî q ~ Dsq ~ !  wî          	     ´pq ~ q ~ pt line-9ppppq ~ 3ppppq ~ 6  wîppsq ~ 8  wîppq ~ Isq ~ @?  q ~ hp  wî q ~ Dsq ~ !  wî             |  ppq ~ q ~ pt line-10ppppq ~ 3ppppq ~ 6  wîppsq ~ 8  wîppq ~ Isq ~ @?  q ~ lp  wî q ~ Dsq ~ !  wî             |  pq ~ q ~ pt line-11ppppq ~ 3ppppq ~ 6  wîppsq ~ 8  wîppq ~ Isq ~ @?  q ~ pp  wî q ~ Dsq ~ !  wî             |  pq ~ q ~ pt line-12ppppq ~ 3ppppq ~ 6  wîppsq ~ 8  wîppq ~ Isq ~ @?  q ~ tp  wî q ~ Dsq ~ !  wî             {  £pq ~ q ~ pt line-13ppppq ~ 3ppppq ~ 6  wîppsq ~ 8  wîppq ~ Isq ~ @?  q ~ xp  wî q ~ Dsq ~ !  wî   "           N  =pq ~ q ~ pt line-14ppppq ~ 3ppppq ~ 6  wîppsq ~ 8  wîppq ~ Isq ~ @?  q ~ |p  wî q ~ Dsq ~ !  wî                =pq ~ q ~ pt line-15ppppq ~ 3ppppq ~ 6  wîppsq ~ 8  wîppq ~ Isq ~ @?  q ~ p  wî q ~ Dsq ~ !  wî              ~  Npq ~ q ~ pt line-16ppppq ~ 3ppppq ~ 6  wîppsq ~ 8  wîppq ~ Isq ~ @?  q ~ p  wî q ~ Dsq ~ !  wî   "           ï  =pq ~ q ~ pt line-17ppppq ~ 3ppppq ~ 6  wîppsq ~ 8  wîppq ~ Isq ~ @?  q ~ p  wî q ~ Dsq ~ !  wî              Ó  =pq ~ q ~ pt line-18ppppq ~ 3ppppq ~ 6  wîppsq ~ 8  wîppq ~ Isq ~ @?  q ~ p  wî q ~ Dsq ~ !  wî          
     ßpq ~ q ~ pt line-19ppppq ~ 3ppppq ~ 6  wîppsq ~ 8  wîppq ~ Isq ~ @?  q ~ p  wî q ~ Dsr 1net.sf.jasperreports.engine.base.JRBaseStaticText      'Ø L textq ~ xr 2net.sf.jasperreports.engine.base.JRBaseTextElement      'Ø %I PSEUDO_SERIAL_VERSION_UIDL borderq ~ L borderColorq ~ 'L bottomBorderq ~ L bottomBorderColorq ~ 'L 
bottomPaddingt Ljava/lang/Integer;L fontNameq ~ L fontSizeq ~ L horizontalAlignmentq ~ L horizontalAlignmentValuet 6Lnet/sf/jasperreports/engine/type/HorizontalAlignEnum;L isBoldt Ljava/lang/Boolean;L isItalicq ~ L 
isPdfEmbeddedq ~ L isStrikeThroughq ~ L isStyledTextq ~ L isUnderlineq ~ L 
leftBorderq ~ L leftBorderColorq ~ 'L leftPaddingq ~ L lineBoxt 'Lnet/sf/jasperreports/engine/JRLineBox;L lineSpacingq ~ L lineSpacingValuet 2Lnet/sf/jasperreports/engine/type/LineSpacingEnum;L markupq ~ L paddingq ~ L pdfEncodingq ~ L pdfFontNameq ~ L 
reportFontq ~ L rightBorderq ~ L rightBorderColorq ~ 'L rightPaddingq ~ L rotationq ~ L 
rotationValuet /Lnet/sf/jasperreports/engine/type/RotationEnum;L 	topBorderq ~ L topBorderColorq ~ 'L 
topPaddingq ~ L verticalAlignmentq ~ L verticalAlignmentValuet 4Lnet/sf/jasperreports/engine/type/VerticalAlignEnum;xq ~ &  wî           B     pq ~ q ~ pt staticText-1ppppq ~ 3ppppq ~ 6  wîpppppt Arialsr java.lang.Integerâ ¤÷8 I valuexq ~ A   ppsr java.lang.BooleanÍ rÕúî Z valuexp ppppppppsr .net.sf.jasperreports.engine.base.JRBaseLineBox      'Ø L 
bottomPaddingq ~ L 	bottomPent +Lnet/sf/jasperreports/engine/base/JRBoxPen;L boxContainert ,Lnet/sf/jasperreports/engine/JRBoxContainer;L leftPaddingq ~ L leftPenq ~ ¥L paddingq ~ L penq ~ ¥L rightPaddingq ~ L rightPenq ~ ¥L 
topPaddingq ~ L topPenq ~ ¥xppsr 3net.sf.jasperreports.engine.base.JRBaseBoxBottomPen      'Ø  xr -net.sf.jasperreports.engine.base.JRBaseBoxPen      'Ø L lineBoxq ~ xq ~ 8  wîsr java.awt.Color¥3u F falphaI valueL cst Ljava/awt/color/ColorSpace;[ 	frgbvaluet [F[ fvalueq ~ ­xp    ÿ   ppppq ~ Isq ~ @    q ~ §q ~ §q ~ psr 1net.sf.jasperreports.engine.base.JRBaseBoxLeftPen      'Ø  xq ~ ©  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~ §q ~ §psq ~ ©  wîppppq ~ §q ~ §psr 2net.sf.jasperreports.engine.base.JRBaseBoxRightPen      'Ø  xq ~ ©  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~ §q ~ §psr 0net.sf.jasperreports.engine.base.JRBaseBoxTopPen      'Ø  xq ~ ©  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~ §q ~ §pppppt 	Helveticapppppppppp~r 2net.sf.jasperreports.engine.type.VerticalAlignEnum          xq ~ 2t MIDDLEt Local de pagamentosq ~   wî           B     ,pq ~ q ~ pt staticText-2ppppq ~ 3ppppq ~ 6  wîpppppt Arialq ~ ¡ppq ~ £ppppppppsq ~ ¤psq ~ ¨  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~ Åq ~ Åq ~ Âpsq ~ °  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~ Åq ~ Åpsq ~ ©  wîppppq ~ Åq ~ Åpsq ~ µ  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~ Åq ~ Åpsq ~ ¹  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~ Åq ~ Åpppppt 	Helveticappppppppppq ~ ¿t 
BeneficiÃ¡riosq ~   wî           B     =pq ~ q ~ pt staticText-3ppppq ~ 3ppppq ~ 6  wîpppppt Arialq ~ ¡ppq ~ £ppppppppsq ~ ¤psq ~ ¨  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~ Øq ~ Øq ~ Õpsq ~ °  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~ Øq ~ Øpsq ~ ©  wîppppq ~ Øq ~ Øpsq ~ µ  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~ Øq ~ Øpsq ~ ¹  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~ Øq ~ Øpppppt 	Helveticappppppppppq ~ ¿t Data do Documentosq ~   wî           K     Npq ~ q ~ pt staticText-4ppppq ~ 3ppppq ~ 6  wîpppppt Arialq ~ ¡ppq ~ £ppppppppsq ~ ¤psq ~ ¨  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~ ëq ~ ëq ~ èpsq ~ °  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~ ëq ~ ëpsq ~ ©  wîppppq ~ ëq ~ ëpsq ~ µ  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~ ëq ~ ëpsq ~ ¹  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~ ëq ~ ëpppppt 	Helveticappppppppppq ~ ¿t Uso do bancosq ~   wî          t     `pq ~ q ~ pt staticText-5ppppq ~ 3ppppq ~ 6  wîpppppt Arialq ~ ¡ppq ~ £ppppppppsq ~ ¤psq ~ ¨  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~ þq ~ þq ~ ûpsq ~ °  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~ þq ~ þpsq ~ ©  wîppppq ~ þq ~ þpsq ~ µ  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~ þq ~ þpsq ~ ¹  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~ þq ~ þpppppt 	Helveticappppppppppq ~ ¿t iInstruÃ§Ãµes (Todas as informaÃ§Ãµes deste boleto sÃ£o de exclusiva responsabilidade do beneficiÃ¡rio ) :sq ~   wî           B   Q  =pq ~ q ~ pt staticText-6ppppq ~ 3ppppq ~ 6  wîpppppt Arialq ~ ¡ppq ~ £ppppppppsq ~ ¤psq ~ ¨  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~q ~q ~psq ~ °  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~q ~psq ~ ©  wîppppq ~q ~psq ~ µ  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~q ~psq ~ ¹  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~q ~pppppt 	Helveticappppppppppq ~ ¿t NÂº do Documentosq ~   wî           *     =pq ~ q ~ pt staticText-7ppppq ~ 3ppppq ~ 6  wîpppppt Arialq ~ ¡ppq ~ £ppppppppsq ~ ¤psq ~ ¨  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~$q ~$q ~!psq ~ °  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~$q ~$psq ~ ©  wîppppq ~$q ~$psq ~ µ  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~$q ~$psq ~ ¹  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~$q ~$pppppt 	Helveticappppppppppq ~ ¿t 
EspÃ©cie Doc.sq ~   wî              Ö  =pq ~ q ~ pt staticText-8ppppq ~ 3ppppq ~ 6  wîpppppt Arialq ~ ¡ppq ~ £ppppppppsq ~ ¤psq ~ ¨  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~7q ~7q ~4psq ~ °  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~7q ~7psq ~ ©  wîppppq ~7q ~7psq ~ µ  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~7q ~7psq ~ ¹  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~7q ~7pppppt 	Helveticappppppppppq ~ ¿t Aceitesq ~   wî              ò  =pq ~ q ~ pt staticText-9ppppq ~ 3ppppq ~ 6  wîpppppt Arialq ~ ¡ppq ~ £ppppppppsq ~ ¤psq ~ ¨  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~Jq ~Jq ~Gpsq ~ °  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~Jq ~Jpsq ~ ©  wîppppq ~Jq ~Jpsq ~ µ  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~Jq ~Jpsq ~ ¹  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~Jq ~Jpppppt 	Helveticappppppppppq ~ ¿t Data do Processamentosq ~   wî           #   Q  Npq ~ q ~ pt 
staticText-10ppppq ~ 3ppppq ~ 6  wîpppppt Arialq ~ ¡ppq ~ £ppppppppsq ~ ¤psq ~ ¨  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~]q ~]q ~Zpsq ~ °  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~]q ~]psq ~ ©  wîppppq ~]q ~]psq ~ µ  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~]q ~]psq ~ ¹  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~]q ~]pppppt 	Helveticappppppppppq ~ ¿t Carteirasq ~   wî           +     Npq ~ q ~ pt 
staticText-11ppppq ~ 3ppppq ~ 6  wîpppppt Arialq ~ ¡ppq ~ £ppppppppsq ~ ¤psq ~ ¨  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~pq ~pq ~mpsq ~ °  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~pq ~ppsq ~ ©  wîppppq ~pq ~ppsq ~ µ  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~pq ~ppsq ~ ¹  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~pq ~ppppppt 	Helveticappppppppppq ~ ¿t 	EspÃ©cie sq ~   wî           0   ¯  Npq ~ q ~ pt 
staticText-12ppppq ~ 3ppppq ~ 6  wîpppppt Arialq ~ ¡ppq ~ £ppppppppsq ~ ¤psq ~ ¨  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~q ~q ~psq ~ °  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~q ~psq ~ ©  wîppppq ~q ~psq ~ µ  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~q ~psq ~ ¹  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~q ~pppppt 	Helveticappppppppppq ~ ¿t 
Quantidadesq ~   wî           B   ò  Npq ~ q ~ pt 
staticText-13ppppq ~ 3ppppq ~ 6  wîpppppt Arialq ~ ¡ppq ~ £ppppppppsq ~ ¤psq ~ ¨  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~q ~q ~psq ~ °  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~q ~psq ~ ©  wîppppq ~q ~psq ~ µ  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~q ~psq ~ ¹  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~q ~pppppt 	Helveticappppppppppq ~ ¿t Valorsr 0net.sf.jasperreports.engine.base.JRBaseRectangle      'Ø L radiusq ~ xq ~ #  wî             |  sq ~ «    ÿßßßpppq ~ q ~ sq ~ «    ÿßßßppppppppq ~ 3ppppq ~ 6  wîppsq ~ 8  wîppppq ~§ppsq ~   wî           d  ~  ,pq ~ q ~ pt 
staticText-15ppppq ~ 3ppppq ~ 6  wîpppppt Arialq ~ ¡ppq ~ £ppppppppsq ~ ¤psq ~ ¨  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~®q ~®q ~«psq ~ °  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~®q ~®psq ~ ©  wîppppq ~®q ~®psq ~ µ  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~®q ~®psq ~ ¹  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~®q ~®pppppt 	Helveticappppppppppq ~ ¿t  AgÃªncia / CÃ³digo BeneficiÃ¡riosq ~¦  wî             |  ¤sq ~ «    ÿßßßpppq ~ q ~ sq ~ «    ÿßßßppppppppq ~ 3ppppq ~ 6  wîppsq ~ 8  wîppppq ~¾ppsq ~   wî           d  ~  pq ~ q ~ pt 
staticText-17ppppq ~ 3ppppq ~ 6  wîpppppt Arialq ~ ¡ppq ~ £ppppppppsq ~ ¤psq ~ ¨  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~Åq ~Åq ~Âpsq ~ °  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~Åq ~Åpsq ~ ©  wîppppq ~Åq ~Åpsq ~ µ  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~Åq ~Åpsq ~ ¹  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~Åq ~Åpppppt 	Helveticappppppppppq ~ ¿t (=) Outros acrÃ©scimossq ~   wî           d  ~  pq ~ q ~ pt 
staticText-18ppppq ~ 3ppppq ~ 6  wîpppppt Arialq ~ ¡ppq ~ £ppppppppsq ~ ¤psq ~ ¨  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~Øq ~Øq ~Õpsq ~ °  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~Øq ~Øpsq ~ ©  wîppppq ~Øq ~Øpsq ~ µ  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~Øq ~Øpsq ~ ¹  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~Øq ~Øpppppt 	Helveticappppppppppq ~ ¿t (+) Juros / Multasq ~   wî           d  ~  ppq ~ q ~ pt 
staticText-19ppppq ~ 3ppppq ~ 6  wîpppppt Arialq ~ ¡ppq ~ £ppppppppsq ~ ¤psq ~ ¨  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~ëq ~ëq ~èpsq ~ °  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~ëq ~ëpsq ~ ©  wîppppq ~ëq ~ëpsq ~ µ  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~ëq ~ëpsq ~ ¹  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~ëq ~ëpppppt 	Helveticappppppppppq ~ ¿t (-) Outros deduÃ§Ãµessq ~   wî           d  ~  _pq ~ q ~ pt 
staticText-20ppppq ~ 3ppppq ~ 6  wîpppppt Arialq ~ ¡ppq ~ £ppppppppsq ~ ¤psq ~ ¨  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~þq ~þq ~ûpsq ~ °  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~þq ~þpsq ~ ©  wîppppq ~þq ~þpsq ~ µ  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~þq ~þpsq ~ ¹  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~þq ~þpppppt 	Helveticappppppppppq ~ ¿t (-) Desconto / Abatimentosq ~   wî           d  ~  Npq ~ q ~ pt 
staticText-21ppppq ~ 3ppppq ~ 6  wîpppppt Arialq ~ ¡ppq ~ £ppppppppsq ~ ¤psq ~ ¨  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~q ~q ~psq ~ °  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~q ~psq ~ ©  wîppppq ~q ~psq ~ µ  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~q ~psq ~ ¹  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~q ~pppppt 	Helveticappppppppppq ~ ¿t (=) Valor do Documentosq ~   wî           d  ~  pq ~ q ~ pt 
staticText-14ppppq ~ 3ppppq ~ 6  wîpppppt Arialq ~ ¡ppq ~ £ppppppppsq ~ ¤psq ~ ¨  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~$q ~$q ~!psq ~ °  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~$q ~$psq ~ ©  wîppppq ~$q ~$psq ~ µ  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~$q ~$psq ~ ¹  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~$q ~$pppppt 	Helveticappppppppppq ~ ¿t 
Vencimentosq ~   wî           !     ¶pq ~ q ~ pt 
staticText-23ppppq ~ 3ppppq ~ 6  wîpppppt Arialq ~ ¡ppq ~ £ppppppppsq ~ ¤psq ~ ¨  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~7q ~7q ~4psq ~ °  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~7q ~7psq ~ ©  wîppppq ~7q ~7psq ~ µ  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~7q ~7psq ~ ¹  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~7q ~7pppppt 	Helveticappppppppppq ~ ¿t Pagador:sq ~   wî           A     Ôpq ~ q ~ pt 
staticText-24ppppq ~ 3ppppq ~ 6  wîpppppt Arialq ~ ¡ppq ~ £ppppppppsq ~ ¤psq ~ ¨  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~Jq ~Jq ~Gpsq ~ °  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~Jq ~Jpsq ~ ©  wîppppq ~Jq ~Jpsq ~ µ  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~Jq ~Jpsq ~ ¹  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~Jq ~Jpppppt 	Helveticappppppppppq ~ ¿t Pagador / Avalista :sq ~   wî           9  g  Ôpq ~ q ~ pt 
staticText-25ppppq ~ 3ppppq ~ 6  wîpppppt Arialq ~ ¡ppq ~ £ppppppppsq ~ ¤psq ~ ¨  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~]q ~]q ~Zpsq ~ °  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~]q ~]psq ~ ©  wîppppq ~]q ~]psq ~ µ  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~]q ~]psq ~ ¹  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~]q ~]pppppt 	Helveticappppppppppq ~ ¿t CÃ³digo de baixasq ~   wî           D  O  ápq ~ q ~ pt 
staticText-26ppppq ~ 3ppppq ~ 6  wîpppppt Arialq ~ ¡ppq ~ £ppppppppsq ~ ¤psq ~ ¨  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~pq ~pq ~mpsq ~ °  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~pq ~ppsq ~ ©  wîppppq ~pq ~ppsq ~ µ  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~pq ~ppsq ~ ¹  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~pq ~ppppppt 	Helveticappppppppppq ~ ¿t AutenticaÃ§Ã£o mecÃ¢nicasq ~   wî           j    ápq ~ q ~ pt 
staticText-27ppppq ~ 3ppppq ~ 6  wîpppppt Arialsq ~     	ppsq ~ ¢ppppppppsq ~ ¤psq ~ ¨  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~q ~q ~psq ~ °  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~q ~psq ~ ©  wîppppq ~q ~psq ~ µ  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~q ~psq ~ ¹  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~q ~pppppt Helvetica-Boldppppppppppq ~ ¿t Ficha de CompensaÃ§Ã£osr 0net.sf.jasperreports.engine.base.JRBaseTextField      'Ø I PSEUDO_SERIAL_VERSION_UIDI 
bookmarkLevelB evaluationTimeB hyperlinkTargetB 
hyperlinkTypeZ isStretchWithOverflowL anchorNameExpressionq ~ L evaluationGroupq ~ +L evaluationTimeValuet 5Lnet/sf/jasperreports/engine/type/EvaluationTimeEnum;L 
expressionq ~ L hyperlinkAnchorExpressionq ~ L hyperlinkPageExpressionq ~ [ hyperlinkParameterst 3[Lnet/sf/jasperreports/engine/JRHyperlinkParameter;L hyperlinkReferenceExpressionq ~ L hyperlinkTooltipExpressionq ~ L isBlankWhenNullq ~ L 
linkTargetq ~ L linkTypeq ~ L patternq ~ xq ~   wî          '   t  
pq ~ q ~ pt textField-35ppppq ~ 3ppppq ~ 6  wîpppppppp~r 4net.sf.jasperreports.engine.type.HorizontalAlignEnum          xq ~ 2t CENTERq ~ppppppppsq ~ ¤psq ~ ¨  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~q ~q ~psq ~ °  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~q ~psq ~ ©  wîppppq ~q ~psq ~ µ  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~q ~psq ~ ¹  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~q ~pppppt Helvetica-Boldpppppppppp~q ~ ¾t TOP  wî       pp~r 3net.sf.jasperreports.engine.type.EvaluationTimeEnum          xq ~ 2t NOWsr 1net.sf.jasperreports.engine.base.JRBaseExpression      'Ø I id[ chunkst 0[Lnet/sf/jasperreports/engine/JRExpressionChunk;L valueClassNameq ~ L valueClassRealNameq ~ xp   ur 0[Lnet.sf.jasperreports.engine.JRExpressionChunk;mYÏÞiK£U  xp   sr 6net.sf.jasperreports.engine.base.JRBaseExpressionChunk      'Ø B typeL textq ~ xpt banco.numeroFormattedt java.lang.Stringppppppq ~pppsq ~  wî          p     
pq ~ q ~ ppppppq ~ 3ppppq ~ 6  wîppppppppq ~q ~ppppppppsq ~ ¤psq ~ ¨  wîppppq ~»q ~»q ~ºpsq ~ °  wîppppq ~»q ~»psq ~ ©  wîppppq ~»q ~»psq ~ µ  wîppppq ~»q ~»psq ~ ¹  wîppppq ~»q ~»pppppt Helvetica-Boldppppppppppp  wî        ppq ~¯sq ~±   	uq ~´   sq ~¶t banco.linhaDigitavelt java.lang.Stringppppppppppsq ~  wî   	       t     4pq ~ q ~ ppppppq ~ 3ppppq ~ 6  wîpppppt Arialsq ~     pppppppppppsq ~ ¤psq ~ ¨  wîppppq ~Êq ~Êq ~Çpsq ~ °  wîppppq ~Êq ~Êpsq ~ ©  wîppppq ~Êq ~Êpsq ~ µ  wîppppq ~Êq ~Êpsq ~ ¹  wîppppq ~Êq ~Êpppppppppppppppp~q ~ ¾t BOTTOM  wî        ppq ~¯sq ~±   
uq ~´   sq ~¶t boleto.cedentet java.lang.Stringppppppppppsq ~  wî   	       t     #pq ~ q ~ ppppppq ~ 3ppppq ~ 6  wîpppppt Arialq ~Épppppppppppsq ~ ¤psq ~ ¨  wîppppq ~Ùq ~Ùq ~×psq ~ °  wîppppq ~Ùq ~Ùpsq ~ ©  wîppppq ~Ùq ~Ùpsq ~ µ  wîppppq ~Ùq ~Ùpsq ~ ¹  wîppppq ~Ùq ~Ùppppppppppppppppq ~Ð  wî        ppq ~¯sq ~±   uq ~´   sq ~¶t boleto.localPagamentot java.lang.Stringppppppq ~pppsq ~  wî   	        B     Epq ~ q ~ ppppppq ~ 3ppppq ~ 6  wîpppppt Arialq ~Épppppppppppsq ~ ¤psq ~ ¨  wîppppq ~æq ~æq ~äpsq ~ °  wîppppq ~æq ~æpsq ~ ©  wîppppq ~æq ~æpsq ~ µ  wîppppq ~æq ~æpsq ~ ¹  wîppppq ~æq ~æppppppppppppppppq ~Ð  wî        ppq ~¯sq ~±   uq ~´   sq ~¶t boleto.dataDocumentot java.lang.Stringppppppq ~ppt  sq ~  wî   	        3     Epq ~ q ~ ppppppq ~ 3ppppq ~ 6  wîpppppt Arialq ~Épq ~pppppppppsq ~ ¤psq ~ ¨  wîppppq ~ôq ~ôq ~òpsq ~ °  wîppppq ~ôq ~ôpsq ~ ©  wîppppq ~ôq ~ôpsq ~ µ  wîppppq ~ôq ~ôpsq ~ ¹  wîppppq ~ôq ~ôppppppppppppppppq ~Ð  wî        ppq ~¯sq ~±   
uq ~´   sq ~¶t boleto.especieDocumentot java.lang.Stringppppppppppsq ~  wî   	           Õ  Epq ~ q ~ ppppppq ~ 3ppppq ~ 6  wîpppppt Arialq ~Épq ~pppppppppsq ~ ¤psq ~ ¨  wîppppq ~q ~q ~ÿpsq ~ °  wîppppq ~q ~psq ~ ©  wîppppq ~q ~psq ~ µ  wîppppq ~q ~psq ~ ¹  wîppppq ~q ~ppppppppppppppppq ~Ð  wî        ppq ~¯sq ~±   uq ~´   sq ~¶t 
boleto.aceitet java.lang.Stringppppppppppsq ~  wî   	           ò  Epq ~ q ~ ppppppq ~ 3ppppq ~ 6  wîpppppt Arialq ~Épppppppppppsq ~ ¤psq ~ ¨  wîppppq ~q ~q ~psq ~ °  wîppppq ~q ~psq ~ ©  wîppppq ~q ~psq ~ µ  wîppppq ~q ~psq ~ ¹  wîppppq ~q ~ppppppppppppppppq ~Ð  wî        ppq ~¯sq ~±   uq ~´   sq ~¶t boleto.dataProcessamentot java.lang.Stringppppppppppsq ~  wî   	        (   Q  Vpq ~ q ~ ppppppq ~ 3ppppq ~ 6  wîpppppt Arialq ~Épq ~pppppppppsq ~ ¤psq ~ ¨  wîppppq ~q ~q ~psq ~ °  wîppppq ~q ~psq ~ ©  wîppppq ~q ~psq ~ µ  wîppppq ~q ~psq ~ ¹  wîppppq ~q ~ppppppppppppppppq ~Ð  wî        ppq ~¯sq ~±   uq ~´   sq ~¶t boleto.carteirat java.lang.Stringppppppppppsq ~  wî   	            #pq ~ q ~ ppppppq ~ 3ppppq ~ 6  wîpppppt Arialq ~Ép~q ~t RIGHTpppppppppsq ~ ¤psq ~ ¨  wîppppq ~*q ~*q ~&psq ~ °  wîppppq ~*q ~*psq ~ ©  wîppppq ~*q ~*psq ~ µ  wîppppq ~*q ~*psq ~ ¹  wîppppq ~*q ~*ppppppppppppppppq ~Ð  wî        ppq ~¯sq ~±   uq ~´   sq ~¶t boleto.dataVencimentot java.lang.Stringppppppppppsq ~  wî   	        P  ´  Vpq ~ q ~ ppppppq ~ 3ppppq ~ 6  wîpppppt Arialq ~Épq ~(pppppppppsq ~ ¤psq ~ ¨  wîppppq ~7q ~7q ~5psq ~ °  wîppppq ~7q ~7psq ~ ©  wîppppq ~7q ~7psq ~ µ  wîppppq ~7q ~7psq ~ ¹  wîppppq ~7q ~7ppppppppppppppppq ~Ð  wî        ppq ~¯sq ~±   uq ~´   sq ~¶t boleto.valorBoletot java.lang.Stringppppppppppsq ~  wî   	       Ý   '  µpq ~ q ~ ppppppq ~ 3ppppq ~ 6  wîpppppt Arialq ~Épppppppppppsq ~ ¤psq ~ ¨  wîppppq ~Dq ~Dq ~Bpsq ~ °  wîppppq ~Dq ~Dpsq ~ ©  wîppppq ~Dq ~Dpsq ~ µ  wîppppq ~Dq ~Dpsq ~ ¹  wîppppq ~Dq ~Dppppppppppppppppq ~Ð  wî        ppq ~¯sq ~±   uq ~´   
sq ~¶t ((sq ~¶t boleto.responsavelsq ~¶t  != null &&  !sq ~¶t boleto.responsavelsq ~¶t .isEmpty()) ? sq ~¶t boleto.responsavelsq ~¶t  :  sq ~¶t boleto.nomeSacadosq ~¶t ) + " CPF/CNPJ: " + sq ~¶t boleto.cpfSacadot java.lang.Stringppppppppppsq ~¦  wî             |  >sq ~ «    ÿßßßpppq ~ q ~ sq ~ «    ÿßßßppppppppq ~ 3ppppq ~ 6  wîppsq ~ 8  wîppppq ~appsq ~  wî   	       Ý   '  ¾pq ~ q ~ ppppppq ~ 3ppppq ~ 6  wîpppppt Arialq ~Épppppppppppsq ~ ¤psq ~ ¨  wîppppq ~gq ~gq ~epsq ~ °  wîppppq ~gq ~gpsq ~ ©  wîppppq ~gq ~gpsq ~ µ  wîppppq ~gq ~gpsq ~ ¹  wîppppq ~gq ~gppppppppppppppppq ~Ð  wî        ppq ~¯sq ~±   uq ~´   sq ~¶t boleto.enderecoSacadot java.lang.Stringppppppppppsq ~  wî   	        *   '  Çpq ~ q ~ ppppppq ~ 3ppppq ~ 6  wîpppppt Arialq ~Épppppppppppsq ~ ¤psq ~ ¨  wîppppq ~tq ~tq ~rpsq ~ °  wîppppq ~tq ~tpsq ~ ©  wîppppq ~tq ~tpsq ~ µ  wîppppq ~tq ~tpsq ~ ¹  wîppppq ~tq ~tppppppppppppppppq ~Ð  wî        ppq ~¯sq ~±   uq ~´   	sq ~¶t (sq ~¶t boleto.cepSacadosq ~¶t 
.length()<=5?sq ~¶t boleto.cepSacadosq ~¶t +"-000":sq ~¶t boleto.cepSacadosq ~¶t .substring(0,5)+"-"+sq ~¶t boleto.cepSacadosq ~¶t .substring(5))t java.lang.Stringppppppq ~pppsq ~  wî   	       ±   S  Çpq ~ q ~ ppppppq ~ 3ppppq ~ 6  wîpppppt Arialq ~Épppppppppppsq ~ ¤psq ~ ¨  wîppppq ~q ~q ~psq ~ °  wîppppq ~q ~psq ~ ©  wîppppq ~q ~psq ~ µ  wîppppq ~q ~psq ~ ¹  wîppppq ~q ~ppppppppppppppppq ~Ð  wî        ppq ~¯sq ~±   uq ~´   	sq ~¶t (sq ~¶t boleto.cidadeSacadosq ~¶t 
==null?"":sq ~¶t boleto.cidadeSacadosq ~¶t .trim() + "  " ) + ( sq ~¶t boleto.ufSacadosq ~¶t ==null? "" : ( sq ~¶t boleto.ufSacadosq ~¶t ))t java.lang.Stringppppppq ~pppsq ~  wî   	            4pq ~ q ~ ppppppq ~ 3ppppq ~ 6  wîpppppt Arialq ~Épq ~(pppppppppsq ~ ¤psq ~ ¨  wîppppq ~®q ~®q ~¬psq ~ °  wîppppq ~®q ~®psq ~ ©  wîppppq ~®q ~®psq ~ µ  wîppppq ~®q ~®psq ~ ¹  wîppppq ~®q ~®ppppppppppppppppq ~Ð  wî        ppq ~¯sq ~±   uq ~´   sq ~¶t  banco.agenciaCodCedenteFormattedt java.lang.Stringppppppppppsq ~  wî   F       p     ipq ~ q ~ pt textField-24ppppq ~ 3ppppq ~ 6  wîpppppt Arialq ~Ép~q ~t LEFTpppppppppsq ~ ¤psq ~ ¨  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~¾q ~¾q ~¹psq ~ °  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~¾q ~¾psq ~ ©  wîppppq ~¾q ~¾psq ~ µ  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~¾q ~¾psq ~ ¹  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~¾q ~¾ppppppppppppppppq ~¬  wî        ppq ~¯sq ~±   uq ~´   sq ~¶t boleto.instrucao1t java.lang.Stringppppppq ~pppsq ~   wî   	             Vpq ~ q ~ ppppppq ~ 3ppppq ~ 6  wîpppppt Arialq ~Épq ~pppppppppsq ~ ¤psq ~ ¨  wîppppq ~Óq ~Óq ~Ñpsq ~ °  wîppppq ~Óq ~Ópsq ~ ©  wîppppq ~Óq ~Ópsq ~ µ  wîppppq ~Óq ~Ópsq ~ ¹  wîppppq ~Óq ~Óppt nonepppppppppppppq ~Ðt REALsq ~  wî   	        C   P  Epq ~ q ~ ppppppq ~ 3ppppq ~ 6  wîpppppt Arialq ~Épq ~pppppppppsq ~ ¤psq ~ ¨  wîppppq ~Ýq ~Ýq ~Ûpsq ~ °  wîppppq ~Ýq ~Ýpsq ~ ©  wîppppq ~Ýq ~Ýpsq ~ µ  wîppppq ~Ýq ~Ýpsq ~ ¹  wîppppq ~Ýq ~Ýppppppppppppppppq ~Ð  wî        ppq ~¯sq ~±   uq ~´   sq ~¶t boleto.noDocumentot java.lang.Stringppppppppppsq ~ !  wî              ­  Npq ~ q ~ pt line-15ppppq ~ 3ppppq ~ 6  wîppsq ~ 8  wîppq ~ Isq ~ @?  q ~èp  wî q ~ Dsr ,net.sf.jasperreports.engine.base.JRBaseImage      'Ø *I PSEUDO_SERIAL_VERSION_UIDI 
bookmarkLevelB evaluationTimeB hyperlinkTargetB 
hyperlinkTypeZ isLazyB onErrorTypeL anchorNameExpressionq ~ L borderq ~ L borderColorq ~ 'L bottomBorderq ~ L bottomBorderColorq ~ 'L 
bottomPaddingq ~ L evaluationGroupq ~ +L evaluationTimeValueq ~L 
expressionq ~ L horizontalAlignmentq ~ L horizontalAlignmentValueq ~ L hyperlinkAnchorExpressionq ~ L hyperlinkPageExpressionq ~ [ hyperlinkParametersq ~L hyperlinkReferenceExpressionq ~ L hyperlinkTooltipExpressionq ~ L isUsingCacheq ~ L 
leftBorderq ~ L leftBorderColorq ~ 'L leftPaddingq ~ L lineBoxq ~ L 
linkTargetq ~ L linkTypeq ~ L onErrorTypeValuet 2Lnet/sf/jasperreports/engine/type/OnErrorTypeEnum;L paddingq ~ L rightBorderq ~ L rightBorderColorq ~ 'L rightPaddingq ~ L 
scaleImageq ~ L scaleImageValuet 1Lnet/sf/jasperreports/engine/type/ScaleImageEnum;L 	topBorderq ~ L topBorderColorq ~ 'L 
topPaddingq ~ L verticalAlignmentq ~ L verticalAlignmentValueq ~ xq ~ #  wî           J     pq ~ q ~ ppppppq ~ 3ppppq ~ 6  wîppsq ~ 8  wîppppq ~ïp  wî         pppppppq ~¯sq ~±   uq ~´   sq ~¶t 
SUBREPORT_DIRsq ~¶t   + "logos/logo-sicredi.png"t java.lang.Stringppppppppppppsq ~ ¤psq ~ ¨  wîppppq ~øq ~øq ~ïpsq ~ °  wîppppq ~øq ~øpsq ~ ©  wîppppq ~øq ~øpsq ~ µ  wîppppq ~øq ~øpsq ~ ¹  wîppppq ~øq ~øpp~r 0net.sf.jasperreports.engine.type.OnErrorTypeEnum          xq ~ 2t ERRORppppp~r /net.sf.jasperreports.engine.type.ScaleImageEnum          xq ~ 2t 
FILL_FRAMEpppppsq ~   wî           d  ~  =pq ~ q ~ pt 
staticText-22ppppq ~ 3ppppq ~ 6  wîpppppt Arialq ~ ¡ppq ~ £ppppppppsq ~ ¤psq ~ ¨  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~q ~q ~psq ~ °  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~q ~psq ~ ©  wîppppq ~q ~psq ~ µ  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~q ~psq ~ ¹  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~q ~pppppt 	Helveticappppppppppq ~ ¿t 
Nosso NÃºmerosq ~   wî           d  ~  £pq ~ q ~ pt 
staticText-16ppppq ~ 3ppppq ~ 6  wîpppppt Arialq ~ ¡ppq ~ £ppppppppsq ~ ¤psq ~ ¨  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~q ~q ~psq ~ °  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~q ~psq ~ ©  wîppppq ~q ~psq ~ µ  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~q ~psq ~ ¹  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~q ~pppppt 	Helveticappppppppppq ~ ¿t (=) Valor cobradosq ~ì  wî           J      pq ~ q ~ ppppppq ~ 3ppppq ~ 6  wîppsq ~ 8  wîppppq ~*p  wî         pppppppq ~¯sq ~±   uq ~´   sq ~¶t 
SUBREPORT_DIRsq ~¶t   + "logos/logo-sicredi.png"t java.lang.Stringppppppppppppsq ~ ¤psq ~ ¨  wîppppq ~3q ~3q ~*psq ~ °  wîppppq ~3q ~3psq ~ ©  wîppppq ~3q ~3psq ~ µ  wîppppq ~3q ~3psq ~ ¹  wîppppq ~3q ~3ppq ~ÿpppppq ~pppppsq ~  wî          '   v   pq ~ q ~ pt textField-35ppppq ~ 3ppppq ~ 6  wîppppppppq ~q ~ppppppppsq ~ ¤psq ~ ¨  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~;q ~;q ~9psq ~ °  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~;q ~;psq ~ ©  wîppppq ~;q ~;psq ~ µ  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~;q ~;psq ~ ¹  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~;q ~;pppppt Helvetica-Boldppppppppppq ~¬  wî       ppq ~¯sq ~±   uq ~´   sq ~¶t banco.numeroFormattedt java.lang.Stringppppppq ~pppsq ~ !  wî                 pq ~ q ~ pt line-3ppppq ~ 3ppppq ~ 6  wîppsq ~ 8  wîppq ~ Isq ~ @?  q ~Op  wî q ~ Dsq ~ !  wî              r   pq ~ q ~ pt line-2ppppq ~ 3ppppq ~ 6  wîppsq ~ 8  wîppq ~ Isq ~ @?  q ~Sp  wî q ~ Dsq ~ !  wî          	      pq ~ q ~ pt line-1ppppq ~ 3ppppq ~ 6  wîppsq ~ 8  wîppq ~ Isq ~ @?  q ~Wp  wî q ~ Dsq ~ !  wî          
      )pq ~ q ~ pt line-4ppppq ~ 3ppppq ~ 6  wîppsq ~ 8  wîppq ~ Isq ~ @?  q ~[p  wî q ~ Dsq ~¦  wî                sq ~ «    ÿßßßpppq ~ q ~ sq ~ «    ÿßßßppppppppq ~ 3ppppq ~ 6  wîppsq ~ 8  wîppppq ~_ppsq ~   wî           d     pq ~ q ~ pt 
staticText-14ppppq ~ 3ppppq ~ 6  wîpppppt Arialq ~ ¡ppq ~ £ppppppppsq ~ ¤psq ~ ¨  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~fq ~fq ~cpsq ~ °  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~fq ~fpsq ~ ©  wîppppq ~fq ~fpsq ~ µ  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~fq ~fpsq ~ ¹  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~fq ~fpppppt 	Helveticappppppppppq ~ ¿t 
Vencimentosq ~  wî   	             pq ~ q ~ ppppppq ~ 3ppppq ~ 6  wîpppppt Arialq ~Épq ~(pppppppppsq ~ ¤psq ~ ¨  wîppppq ~xq ~xq ~vpsq ~ °  wîppppq ~xq ~xpsq ~ ©  wîppppq ~xq ~xpsq ~ µ  wîppppq ~xq ~xpsq ~ ¹  wîppppq ~xq ~xppppppppppppppppq ~Ð  wî        ppq ~¯sq ~±   uq ~´   sq ~¶t boleto.dataVencimentot java.lang.Stringppppppppppsq ~   wî           B      pq ~ q ~ pt staticText-1ppppq ~ 3ppppq ~ 6  wîpppppt Arialq ~ ¡ppq ~ £ppppppppsq ~ ¤psq ~ ¨  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~q ~q ~psq ~ °  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~q ~psq ~ ©  wîppppq ~q ~psq ~ µ  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~q ~psq ~ ¹  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~q ~pppppt 	Helveticappppppppppq ~ ¿t Local de pagamentosq ~  wî   	       t      pq ~ q ~ ppppppq ~ 3ppppq ~ 6  wîpppppt Arialq ~Épppppppppppsq ~ ¤psq ~ ¨  wîppppq ~q ~q ~psq ~ °  wîppppq ~q ~psq ~ ©  wîppppq ~q ~psq ~ µ  wîppppq ~q ~psq ~ ¹  wîppppq ~q ~ppppppppppppppppq ~Ð  wî        ppq ~¯sq ~±   uq ~´   sq ~¶t boleto.localPagamentot java.lang.Stringppppppq ~pppsq ~ !  wî          	      :pq ~ q ~ pt line-5ppppq ~ 3ppppq ~ 6  wîppsq ~ 8  wîppq ~ Isq ~ @?  q ~£p  wî q ~ Dsq ~   wî           B      )pq ~ q ~ pt staticText-2ppppq ~ 3ppppq ~ 6  wîpppppt Arialq ~ ¡ppq ~ £ppppppppsq ~ ¤psq ~ ¨  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~ªq ~ªq ~§psq ~ °  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~ªq ~ªpsq ~ ©  wîppppq ~ªq ~ªpsq ~ µ  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~ªq ~ªpsq ~ ¹  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~ªq ~ªpppppt 	Helveticappppppppppq ~ ¿t 
BeneficiÃ¡riosq ~  wî   	       s      1pq ~ q ~ ppppppq ~ 3ppppq ~ 6  wîpppppt Arialq ~Épppppppppppsq ~ ¤psq ~ ¨  wîppppq ~¼q ~¼q ~ºpsq ~ °  wîppppq ~¼q ~¼psq ~ ©  wîppppq ~¼q ~¼psq ~ µ  wîppppq ~¼q ~¼psq ~ ¹  wîppppq ~¼q ~¼ppppppppppppppppq ~Ð  wî        ppq ~¯sq ~±   uq ~´   sq ~¶t boleto.cedentet java.lang.Stringppppppppppsq ~ !  wî          
      pq ~ q ~ pt line-5ppppq ~ 3ppppq ~ 6  wîppsq ~ 8  wîppq ~ Isq ~ @?  q ~Çp  wî q ~ Dsq ~   wî           B      ;pq ~ q ~ pt staticText-3ppppq ~ 3ppppq ~ 6  wîpppppt Arialq ~ ¡ppq ~ £ppppppppsq ~ ¤psq ~ ¨  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~Îq ~Îq ~Ëpsq ~ °  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~Îq ~Îpsq ~ ©  wîppppq ~Îq ~Îpsq ~ µ  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~Îq ~Îpsq ~ ¹  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~Îq ~Îpppppt 	Helveticappppppppppq ~ ¿t Data do Documentosq ~  wî   	        H      Dpq ~ q ~ ppppppq ~ 3ppppq ~ 6  wîpppppt Arialq ~Épppppppppppsq ~ ¤psq ~ ¨  wîppppq ~àq ~àq ~Þpsq ~ °  wîppppq ~àq ~àpsq ~ ©  wîppppq ~àq ~àpsq ~ µ  wîppppq ~àq ~àpsq ~ ¹  wîppppq ~àq ~àppppppppppppppppq ~Ð  wî        ppq ~¯sq ~±    uq ~´   sq ~¶t boleto.dataDocumentot java.lang.Stringppppppq ~ppq ~ñsq ~   wî           B   Y   ;pq ~ q ~ pt staticText-6ppppq ~ 3ppppq ~ 6  wîpppppt Arialq ~ ¡ppq ~ £ppppppppsq ~ ¤psq ~ ¨  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~îq ~îq ~ëpsq ~ °  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~îq ~îpsq ~ ©  wîppppq ~îq ~îpsq ~ µ  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~îq ~îpsq ~ ¹  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~îq ~îpppppt 	Helveticappppppppppq ~ ¿t NÂº do Documentosq ~  wî   	        C   X   Cpq ~ q ~ ppppppq ~ 3ppppq ~ 6  wîpppppt Arialq ~Épq ~pppppppppsq ~ ¤psq ~ ¨  wîppppq ~ q ~ q ~þpsq ~ °  wîppppq ~ q ~ psq ~ ©  wîppppq ~ q ~ psq ~ µ  wîppppq ~ q ~ psq ~ ¹  wîppppq ~ q ~ ppppppppppppppppq ~Ð  wî        ppq ~¯sq ~±   !uq ~´   sq ~¶t boleto.noDocumentot java.lang.Stringppppppppppsq ~   wî           *   ¥   ;pq ~ q ~ pt staticText-7ppppq ~ 3ppppq ~ 6  wîpppppt Arialq ~ ¡ppq ~ £ppppppppsq ~ ¤psq ~ ¨  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~q ~q ~psq ~ °  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~q ~psq ~ ©  wîppppq ~q ~psq ~ µ  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~q ~psq ~ ¹  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~q ~pppppt 	Helveticappppppppppq ~ ¿t 
EspÃ©cie Doc.sq ~  wî   	        3   ¥   Cpq ~ q ~ ppppppq ~ 3ppppq ~ 6  wîpppppt Arialq ~Épq ~pppppppppsq ~ ¤psq ~ ¨  wîppppq ~ q ~ q ~psq ~ °  wîppppq ~ q ~ psq ~ ©  wîppppq ~ q ~ psq ~ µ  wîppppq ~ q ~ psq ~ ¹  wîppppq ~ q ~ ppppppppppppppppq ~Ð  wî        ppq ~¯sq ~±   "uq ~´   sq ~¶t boleto.especieDocumentot java.lang.Stringppppppppppsq ~   wî              Þ   ;pq ~ q ~ pt staticText-8ppppq ~ 3ppppq ~ 6  wîpppppt Arialq ~ ¡ppq ~ £ppppppppsq ~ ¤psq ~ ¨  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~.q ~.q ~+psq ~ °  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~.q ~.psq ~ ©  wîppppq ~.q ~.psq ~ µ  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~.q ~.psq ~ ¹  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~.q ~.pppppt 	Helveticappppppppppq ~ ¿t Aceitesq ~  wî   	           Ý   Cpq ~ q ~ ppppppq ~ 3ppppq ~ 6  wîpppppt Arialq ~Épq ~pppppppppsq ~ ¤psq ~ ¨  wîppppq ~@q ~@q ~>psq ~ °  wîppppq ~@q ~@psq ~ ©  wîppppq ~@q ~@psq ~ µ  wîppppq ~@q ~@psq ~ ¹  wîppppq ~@q ~@ppppppppppppppppq ~Ð  wî        ppq ~¯sq ~±   #uq ~´   sq ~¶t 
boleto.aceitet java.lang.Stringppppppppppsq ~   wî              ú   ;pq ~ q ~ pt staticText-9ppppq ~ 3ppppq ~ 6  wîpppppt Arialq ~ ¡ppq ~ £ppppppppsq ~ ¤psq ~ ¨  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~Nq ~Nq ~Kpsq ~ °  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~Nq ~Npsq ~ ©  wîppppq ~Nq ~Npsq ~ µ  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~Nq ~Npsq ~ ¹  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~Nq ~Npppppt 	Helveticappppppppppq ~ ¿t Data do Processamentosq ~  wî   	           ú   Cpq ~ q ~ ppppppq ~ 3ppppq ~ 6  wîpppppt Arialq ~Épppppppppppsq ~ ¤psq ~ ¨  wîppppq ~`q ~`q ~^psq ~ °  wîppppq ~`q ~`psq ~ ©  wîppppq ~`q ~`psq ~ µ  wîppppq ~`q ~`psq ~ ¹  wîppppq ~`q ~`ppppppppppppppppq ~Ð  wî        ppq ~¯sq ~±   $uq ~´   sq ~¶t boleto.dataProcessamentot java.lang.Stringppppppppppsq ~ !  wî          
      Mpq ~ q ~ pt line-6ppppq ~ 3ppppq ~ 6  wîppsq ~ 8  wîppq ~ Isq ~ @?  q ~kp  wî q ~ Dsq ~ !  wî          	      _pq ~ q ~ pt line-7ppppq ~ 3ppppq ~ 6  wîppsq ~ 8  wîppq ~ Isq ~ @?  q ~op  wî q ~ Dsq ~   wî           K      Npq ~ q ~ pt staticText-4ppppq ~ 3ppppq ~ 6  wîpppppt Arialq ~ ¡ppq ~ £ppppppppsq ~ ¤psq ~ ¨  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~vq ~vq ~spsq ~ °  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~vq ~vpsq ~ ©  wîppppq ~vq ~vpsq ~ µ  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~vq ~vpsq ~ ¹  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~vq ~vpppppt 	Helveticappppppppppq ~ ¿t Uso do bancosq ~   wî           #   X   Npq ~ q ~ pt 
staticText-10ppppq ~ 3ppppq ~ 6  wîpppppt Arialq ~ ¡ppq ~ £ppppppppsq ~ ¤psq ~ ¨  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~q ~q ~psq ~ °  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~q ~psq ~ ©  wîppppq ~q ~psq ~ µ  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~q ~psq ~ ¹  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~q ~pppppt 	Helveticappppppppppq ~ ¿t Carteirasq ~   wî           +      Npq ~ q ~ pt 
staticText-11ppppq ~ 3ppppq ~ 6  wîpppppt Arialq ~ ¡ppq ~ £ppppppppsq ~ ¤psq ~ ¨  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~q ~q ~psq ~ °  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~q ~psq ~ ©  wîppppq ~q ~psq ~ µ  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~q ~psq ~ ¹  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~q ~pppppt 	Helveticappppppppppq ~ ¿t 	EspÃ©cie sq ~   wî           0   ¹   Npq ~ q ~ pt 
staticText-12ppppq ~ 3ppppq ~ 6  wîpppppt Arialq ~ ¡ppq ~ £ppppppppsq ~ ¤psq ~ ¨  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~¯q ~¯q ~¬psq ~ °  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~¯q ~¯psq ~ ©  wîppppq ~¯q ~¯psq ~ µ  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~¯q ~¯psq ~ ¹  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~¯q ~¯pppppt 	Helveticappppppppppq ~ ¿t 
Quantidadesq ~   wî           B   ü   Npq ~ q ~ pt 
staticText-13ppppq ~ 3ppppq ~ 6  wîpppppt Arialq ~ ¡ppq ~ £ppppppppsq ~ ¤psq ~ ¨  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~Âq ~Âq ~¿psq ~ °  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~Âq ~Âpsq ~ ©  wîppppq ~Âq ~Âpsq ~ µ  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~Âq ~Âpsq ~ ¹  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~Âq ~Âpppppt 	Helveticappppppppppq ~ ¿t Valorsq ~ !  wî   #           T   ;pq ~ q ~ pt line-14ppppq ~ 3ppppq ~ 6  wîppsq ~ 8  wîppq ~ Isq ~ @?  q ~Òp  wî q ~ Dsq ~ !  wî   #           ÷   ;pq ~ q ~ pt line-17ppppq ~ 3ppppq ~ 6  wîppsq ~ 8  wîppq ~ Isq ~ @?  q ~Öp  wî q ~ Dsq ~ !  wî              Û   ;pq ~ q ~ pt line-18ppppq ~ 3ppppq ~ 6  wîppsq ~ 8  wîppq ~ Isq ~ @?  q ~Úp  wî q ~ Dsq ~ !  wî                 ;pq ~ q ~ pt line-15ppppq ~ 3ppppq ~ 6  wîppsq ~ 8  wîppq ~ Isq ~ @?  q ~Þp  wî q ~ Dsq ~  wî   	        (   [   Vpq ~ q ~ ppppppq ~ 3ppppq ~ 6  wîpppppt Arialq ~Épq ~pppppppppsq ~ ¤psq ~ ¨  wîppppq ~äq ~äq ~âpsq ~ °  wîppppq ~äq ~äpsq ~ ©  wîppppq ~äq ~äpsq ~ µ  wîppppq ~äq ~äpsq ~ ¹  wîppppq ~äq ~äppppppppppppppppq ~Ð  wî        ppq ~¯sq ~±   %uq ~´   sq ~¶t boleto.carteirat java.lang.Stringppppppppppsq ~   wî   	              Vpq ~ q ~ ppppppq ~ 3ppppq ~ 6  wîpppppt Arialq ~Épq ~pppppppppsq ~ ¤psq ~ ¨  wîppppq ~ñq ~ñq ~ïpsq ~ °  wîppppq ~ñq ~ñpsq ~ ©  wîppppq ~ñq ~ñpsq ~ µ  wîppppq ~ñq ~ñpsq ~ ¹  wîppppq ~ñq ~ñppt nonepppppppppppppq ~Ðt REALsq ~¦  wî                ;sq ~ «    ÿßßßpppq ~ q ~ sq ~ «    ÿßßßppppppppq ~ 3ppppq ~ 6  wîppsq ~ 8  wîppppq ~ùppsq ~   wî           d     )pq ~ q ~ pt 
staticText-15ppppq ~ 3ppppq ~ 6  wîpppppt Arialq ~ ¡ppq ~ £ppppppppsq ~ ¤psq ~ ¨  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~ q ~ q ~ýpsq ~ °  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~ q ~ psq ~ ©  wîppppq ~ q ~ psq ~ µ  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~ q ~ psq ~ ¹  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~ q ~ pppppt 	Helveticappppppppppq ~ ¿t  AgÃªncia / CÃ³digo BeneficiÃ¡riosq ~  wî   	             1pq ~ q ~ ppppppq ~ 3ppppq ~ 6  wîpppppt Arialq ~Épq ~(pppppppppsq ~ ¤psq ~ ¨  wîppppq ~q ~q ~psq ~ °  wîppppq ~q ~psq ~ ©  wîppppq ~q ~psq ~ µ  wîppppq ~q ~psq ~ ¹  wîppppq ~q ~ppppppppppppppppq ~Ð  wî        ppq ~¯sq ~±   &uq ~´   sq ~¶t  banco.agenciaCodCedenteFormattedt java.lang.Stringppppppppppsq ~   wî           d     _pq ~ q ~ pt 
staticText-20ppppq ~ 3ppppq ~ 6  wîpppppt Arialq ~ ¡ppq ~ £ppppppppsq ~ ¤psq ~ ¨  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~ q ~ q ~psq ~ °  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~ q ~ psq ~ ©  wîppppq ~ q ~ psq ~ µ  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~ q ~ psq ~ ¹  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~ q ~ pppppt 	Helveticappppppppppq ~ ¿t (-) Desconto / Abatimentosq ~ !  wî                pq ~ q ~ pt line-10ppppq ~ 3ppppq ~ 6  wîppsq ~ 8  wîppq ~ Isq ~ @?  q ~0p  wî q ~ Dsq ~ !  wî                pq ~ q ~ pt line-10ppppq ~ 3ppppq ~ 6  wîppsq ~ 8  wîppq ~ Isq ~ @?  q ~4p  wî q ~ Dsq ~   wî           d     qpq ~ q ~ pt 
staticText-19ppppq ~ 3ppppq ~ 6  wîpppppt Arialq ~ ¡ppq ~ £ppppppppsq ~ ¤psq ~ ¨  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~;q ~;q ~8psq ~ °  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~;q ~;psq ~ ©  wîppppq ~;q ~;psq ~ µ  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~;q ~;psq ~ ¹  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~;q ~;pppppt 	Helveticappppppppppq ~ ¿t (-) Outros deduÃ§Ãµessq ~   wî           d     pq ~ q ~ pt 
staticText-18ppppq ~ 3ppppq ~ 6  wîpppppt Arialq ~ ¡ppq ~ £ppppppppsq ~ ¤psq ~ ¨  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~Nq ~Nq ~Kpsq ~ °  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~Nq ~Npsq ~ ©  wîppppq ~Nq ~Npsq ~ µ  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~Nq ~Npsq ~ ¹  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~Nq ~Npppppt 	Helveticappppppppppq ~ ¿t (+) Juros / Multasq ~   wî           d     pq ~ q ~ pt 
staticText-17ppppq ~ 3ppppq ~ 6  wîpppppt Arialq ~ ¡ppq ~ £ppppppppsq ~ ¤psq ~ ¨  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~aq ~aq ~^psq ~ °  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~aq ~apsq ~ ©  wîppppq ~aq ~apsq ~ µ  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~aq ~apsq ~ ¹  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~aq ~apppppt 	Helveticappppppppppq ~ ¿t (=) Outros acrÃ©scimossq ~¦  wî                ¥sq ~ «    ÿßßßpppq ~ q ~ sq ~ «    ÿßßßppppppppq ~ 3ppppq ~ 6  wîppsq ~ 8  wîppppq ~qppsq ~ !  wî                ¤pq ~ q ~ pt line-10ppppq ~ 3ppppq ~ 6  wîppsq ~ 8  wîppq ~ Isq ~ @?  q ~up  wî q ~ Dsq ~   wî           d     ¤pq ~ q ~ pt 
staticText-16ppppq ~ 3ppppq ~ 6  wîpppppt Arialq ~ ¡ppq ~ £ppppppppsq ~ ¤psq ~ ¨  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~|q ~|q ~ypsq ~ °  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~|q ~|psq ~ ©  wîppppq ~|q ~|psq ~ µ  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~|q ~|psq ~ ¹  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~|q ~|pppppt 	Helveticappppppppppq ~ ¿t (=) Valor cobradosq ~   wî           d     :pq ~ q ~ pt 
staticText-22ppppq ~ 3ppppq ~ 6  wîpppppt Arialq ~ ¡ppq ~ £ppppppppsq ~ ¤psq ~ ¨  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~q ~q ~psq ~ °  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~q ~psq ~ ©  wîppppq ~q ~psq ~ µ  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~q ~psq ~ ¹  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~q ~pppppt 	Helveticappppppppppq ~ ¿t 
Nosso NÃºmerosq ~   wî          t      apq ~ q ~ pt staticText-5ppppq ~ 3ppppq ~ 6  wîpppppt Arialq ~ ¡ppq ~ £ppppppppsq ~ ¤psq ~ ¨  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~¢q ~¢q ~psq ~ °  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~¢q ~¢psq ~ ©  wîppppq ~¢q ~¢psq ~ µ  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~¢q ~¢psq ~ ¹  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~¢q ~¢pppppt 	Helveticappppppppppq ~ ¿t iInstruÃ§Ãµes (Todas as informaÃ§Ãµes deste boleto sÃ£o de exclusiva responsabilidade do beneficiÃ¡rio ) :sq ~  wî   F       p      lpq ~ q ~ pt textField-24ppppq ~ 3ppppq ~ 6  wîpppppt Arialq ~Épq ~¼pppppppppsq ~ ¤psq ~ ¨  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~µq ~µq ~²psq ~ °  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~µq ~µpsq ~ ©  wîppppq ~µq ~µpsq ~ µ  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~µq ~µpsq ~ ¹  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~µq ~µppppppppppppppppq ~¬  wî        ppq ~¯sq ~±   'uq ~´   sq ~¶t boleto.instrucao1t java.lang.Stringppppppq ~pppsq ~ !  wî          
      µpq ~ q ~ pt line-9ppppq ~ 3ppppq ~ 6  wîppsq ~ 8  wîppq ~ Isq ~ @?  q ~Èp  wî q ~ Dsq ~   wî           !   
   ·pq ~ q ~ pt 
staticText-23ppppq ~ 3ppppq ~ 6  wîpppppt Arialq ~ ¡ppq ~ £ppppppppsq ~ ¤psq ~ ¨  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~Ïq ~Ïq ~Ìpsq ~ °  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~Ïq ~Ïpsq ~ ©  wîppppq ~Ïq ~Ïpsq ~ µ  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~Ïq ~Ïpsq ~ ¹  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~Ïq ~Ïpppppt 	Helveticappppppppppq ~ ¿t Pagador:sq ~  wî   	       Ý   -   ·pq ~ q ~ ppppppq ~ 3ppppq ~ 6  wîpppppt Arialq ~Épppppppppppsq ~ ¤psq ~ ¨  wîppppq ~áq ~áq ~ßpsq ~ °  wîppppq ~áq ~ápsq ~ ©  wîppppq ~áq ~ápsq ~ µ  wîppppq ~áq ~ápsq ~ ¹  wîppppq ~áq ~áppppppppppppppppq ~Ð  wî        ppq ~¯sq ~±   (uq ~´   
sq ~¶t ((sq ~¶t boleto.responsavelsq ~¶t  != null &&  !sq ~¶t boleto.responsavelsq ~¶t .isEmpty()) ? sq ~¶t boleto.responsavelsq ~¶t  :  sq ~¶t boleto.nomeSacadosq ~¶t ) + " CPF/CNPJ: " + sq ~¶t boleto.cpfSacadot java.lang.Stringppppppppppsq ~  wî   	       Ý   0   Ãpq ~ q ~ ppppppq ~ 3ppppq ~ 6  wîpppppt Arialq ~Épppppppppppsq ~ ¤psq ~ ¨  wîppppq ~ q ~ q ~þpsq ~ °  wîppppq ~ q ~ psq ~ ©  wîppppq ~ q ~ psq ~ µ  wîppppq ~ q ~ psq ~ ¹  wîppppq ~ q ~ ppppppppppppppppq ~Ð  wî        ppq ~¯sq ~±   )uq ~´   sq ~¶t boleto.enderecoSacadot java.lang.Stringppppppppppsq ~  wî   	        *   1   Îpq ~ q ~ ppppppq ~ 3ppppq ~ 6  wîpppppt Arialq ~Épppppppppppsq ~ ¤psq ~ ¨  wîppppq ~
q ~
q ~psq ~ °  wîppppq ~
q ~
psq ~ ©  wîppppq ~
q ~
psq ~ µ  wîppppq ~
q ~
psq ~ ¹  wîppppq ~
q ~
ppppppppppppppppq ~Ð  wî        ppq ~¯sq ~±   *uq ~´   	sq ~¶t (sq ~¶t boleto.cepSacadosq ~¶t 
.length()<=5?sq ~¶t boleto.cepSacadosq ~¶t +"-000":sq ~¶t boleto.cepSacadosq ~¶t .substring(0,5)+"-"+sq ~¶t boleto.cepSacadosq ~¶t .substring(5))t java.lang.Stringppppppq ~pppsq ~  wî   	       ±   ]   Îpq ~ q ~ ppppppq ~ 3ppppq ~ 6  wîpppppt Arialq ~Épppppppppppsq ~ ¤psq ~ ¨  wîppppq ~*q ~*q ~(psq ~ °  wîppppq ~*q ~*psq ~ ©  wîppppq ~*q ~*psq ~ µ  wîppppq ~*q ~*psq ~ ¹  wîppppq ~*q ~*ppppppppppppppppq ~Ð  wî        ppq ~¯sq ~±   +uq ~´   	sq ~¶t (sq ~¶t boleto.cidadeSacadosq ~¶t 
==null?"":sq ~¶t boleto.cidadeSacadosq ~¶t .trim() + "  " ) + ( sq ~¶t boleto.ufSacadosq ~¶t ==null? "" : ( sq ~¶t boleto.ufSacadosq ~¶t ))t java.lang.Stringppppppq ~pppsq ~   wî           j  ¤   pq ~ q ~ pt 
staticText-27ppppq ~ 3ppppq ~ 6  wîpppppt Arialq ~ppq ~ppppppppsq ~ ¤psq ~ ¨  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~Hq ~Hq ~Epsq ~ °  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~Hq ~Hpsq ~ ©  wîppppq ~Hq ~Hpsq ~ µ  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~Hq ~Hpsq ~ ¹  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~Hq ~Hpppppt Helvetica-Boldppppppppppq ~ ¿t RECIBO DO PAGADORsq ~  wî   	             Dpq ~ q ~ pt textField-49ppppq ~ 3ppppq ~ 6  wîpppppt Arialq ~Épq ~(pppppppppsq ~ ¤psq ~ ¨  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~[q ~[q ~Xpsq ~ °  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~[q ~[psq ~ ©  wîppppq ~[q ~[psq ~ µ  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~[q ~[psq ~ ¹  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~[q ~[ppppppppppppppppq ~Ð  wî        ppq ~¯sq ~±   ,uq ~´   sq ~¶t banco.nossoNumeroFormattedt java.lang.Stringppppppq ~ppq ~ñsq ~  wî   	            Epq ~ q ~ pt textField-49ppppq ~ 3ppppq ~ 6  wîpppppt Arialq ~Épq ~(pppppppppsq ~ ¤psq ~ ¨  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~qq ~qq ~npsq ~ °  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~qq ~qpsq ~ ©  wîppppq ~qq ~qpsq ~ µ  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~qq ~qpsq ~ ¹  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~qq ~qppppppppppppppppq ~Ð  wî        ppq ~¯sq ~±   -uq ~´   sq ~¶t banco.nossoNumeroFormattedt java.lang.Stringppppppq ~ppq ~ñsq ~   wî           d     Mpq ~ q ~ pt 
staticText-21ppppq ~ 3ppppq ~ 6  wîpppppt Arialq ~ ¡ppq ~ £ppppppppsq ~ ¤psq ~ ¨  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~q ~q ~psq ~ °  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~q ~psq ~ ©  wîppppq ~q ~psq ~ µ  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~q ~psq ~ ¹  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~q ~pppppt 	Helveticappppppppppq ~ ¿t (=) Valor do Documentosq ~  wî   	        P  ·   Tpq ~ q ~ ppppppq ~ 3ppppq ~ 6  wîpppppt Arialq ~Épq ~(pppppppppsq ~ ¤psq ~ ¨  wîppppq ~q ~q ~psq ~ °  wîppppq ~q ~psq ~ ©  wîppppq ~q ~psq ~ µ  wîppppq ~q ~psq ~ ¹  wîppppq ~q ~ppppppppppppppppq ~Ð  wî        ppq ~¯sq ~±   .uq ~´   sq ~¶t boleto.valorBoletot java.lang.Stringppppppppppsq ~ !  wî   ²          ~   pq ~ q ~ pt line-8ppppq ~ 3ppppq ~ 6  wîppsq ~ 8  wîppq ~ Isq ~ @?  q ~¤p  wî q ~ Dsq ~ !  wî             ~   opq ~ q ~ pt line-10ppppq ~ 3ppppq ~ 6  wîppsq ~ 8  wîppq ~ Isq ~ @?  q ~¨p  wî q ~ Dsq ~ !  wî                pq ~ q ~ pt line-8ppppq ~ 3ppppq ~ 6  wîppsq ~ 8  wîppq ~ Isq ~ @?  q ~¬p  wî q ~ Dsq ~ !  wî            
   pq ~ q ~ pt line-8ppppq ~ 3ppppq ~ 6  wîppsq ~ 8  wîppq ~ Isq ~ @?  q ~°p  wî q ~ Dsq ~ !  wî               pq ~ q ~ pt line-19ppppq ~ 3ppppq ~ 6  wîppsq ~ 8  wîppq ~ Isq ~ @?  q ~´p  wî q ~ Dsq ~ì  wî   #       ?     ìsq ~ «    ÿÿÿÿpppq ~ q ~ sq ~ «    ÿ   pppt 	barcode-1p~r )net.sf.jasperreports.engine.type.ModeEnum          xq ~ 2t OPAQUEppq ~ 3ppppq ~ 6  wîp~r )net.sf.jasperreports.engine.type.FillEnum          xq ~ 2t SOLIDsq ~ 8  wîppq ~ Isq ~ @    q ~¸p  wî         pppppppq ~¯sq ~±   /uq ~´   sq ~¶t <it.businesslogic.ireport.barcode.BcImage.getBarcodeImage(13,sq ~¶t banco.codigoBarrassq ~¶t ,false,false,null,0,0)t java.awt.Imageppq ~¼pppppppppsq ~ ¤psq ~ ¨  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~Íq ~Íq ~¸psq ~ °  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~Íq ~Ípsq ~ ©  wîppppq ~Íq ~Ípsq ~ µ  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~Íq ~Ípsq ~ ¹  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~Íq ~Íppq ~ÿpppppq ~ppppq ~¬xp  wî  "pp~r .net.sf.jasperreports.engine.type.SplitTypeEnum          xq ~ 2t STRETCHppsr java.util.HashSetºD¸·4  xpw   ?@     t "net.sf.jasperreports.engine.data.*t net.sf.jasperreports.engine.*t java.util.*xt javapsr .net.sf.jasperreports.engine.base.JRBaseDataset      'Ø I PSEUDO_SERIAL_VERSION_UIDZ isMainB whenResourceMissingType[ fieldst &[Lnet/sf/jasperreports/engine/JRField;L filterExpressionq ~ [ groupst &[Lnet/sf/jasperreports/engine/JRGroup;L nameq ~ [ 
parameterst *[Lnet/sf/jasperreports/engine/JRParameter;L 
propertiesMapq ~ ,L queryt %Lnet/sf/jasperreports/engine/JRQuery;L resourceBundleq ~ L scriptletClassq ~ [ 
scriptletst *[Lnet/sf/jasperreports/engine/JRScriptlet;[ 
sortFieldst *[Lnet/sf/jasperreports/engine/JRSortField;[ 	variablest )[Lnet/sf/jasperreports/engine/JRVariable;L whenResourceMissingTypeValuet >Lnet/sf/jasperreports/engine/type/WhenResourceMissingTypeEnum;xp  wî ur &[Lnet.sf.jasperreports.engine.JRField;<ßÇN*òp  xp   sr ,net.sf.jasperreports.engine.base.JRBaseField      'Ø L descriptionq ~ L nameq ~ L 
propertiesMapq ~ ,L valueClassNameq ~ L valueClassRealNameq ~ xppt bancosr +net.sf.jasperreports.engine.JRPropertiesMap      'Ø L baseq ~ ,L propertiesListq ~ L 
propertiesMapt Ljava/util/Map;xppppt java.lang.Objectpsq ~ðpt banco.numeroFormattedsq ~ópppt java.lang.Stringpsq ~ðpt banco.linhaDigitavelsq ~ópppt java.lang.Stringpsq ~ðpt banco.codigoBarrassq ~ópppt java.lang.Stringpsq ~ðpt boleto.cedentesq ~ópppt java.lang.Stringpsq ~ðpt boleto.localPagamentosq ~ópppt java.lang.Stringpsq ~ðpt boleto.dataDocumentosq ~ópppt java.lang.Stringpsq ~ðpt boleto.especieDocumentosq ~ópppt java.lang.Stringpsq ~ðpt 
boleto.aceitesq ~ópppt java.lang.Stringpsq ~ðpt boleto.dataProcessamentosq ~ópppt java.lang.Stringpsq ~ðpt boleto.carteirasq ~ópppt java.lang.Stringpsq ~ðpt boleto.dataVencimentosq ~ópppt java.lang.Stringpsq ~ðpt boleto.valorBoletosq ~ópppt java.lang.Stringpsq ~ðpt boleto.valorTitulosq ~ópppt java.lang.Stringpsq ~ðpt boleto.nomeSacadosq ~ópppt java.lang.Stringpsq ~ðpt boleto.cpfSacadosq ~ópppt java.lang.Stringpsq ~ðpt boleto.nossoNumerosq ~ópppt java.lang.Stringpsq ~ðpt boleto.enderecoSacadosq ~ópppt java.lang.Stringpsq ~ðpt boleto.cepSacadosq ~ópppt java.lang.Stringpsq ~ðpt boleto.cidadeSacadosq ~ópppt java.lang.Stringpsq ~ðpt boleto.ufSacadosq ~ópppt java.lang.Stringpsq ~ðpt  banco.agenciaCodCedenteFormattedsq ~ópppt java.lang.Stringpsq ~ðpt banco.nossoNumeroFormattedsq ~ópppt java.lang.Stringpsq ~ðpt boleto.instrucao1sq ~ópppt java.lang.Stringpsq ~ðpt banco.bancosq ~ópppt java.lang.Stringpsq ~ðpt boleto.dvNossoNumerosq ~ópppt java.lang.Stringpsq ~ðpt boleto.noDocumentosq ~ópppt java.lang.Stringpsq ~ðt boleto.responsavelt boleto.responsavelsq ~ópppt java.lang.Stringpppt boletour *[Lnet.sf.jasperreports.engine.JRParameter;" *Ã`!  xp   sr 0net.sf.jasperreports.engine.base.JRBaseParameter      'Ø 	Z isForPromptingZ isSystemDefinedL defaultValueExpressionq ~ L descriptionq ~ L nameq ~ L nestedTypeNameq ~ L 
propertiesMapq ~ ,L valueClassNameq ~ L valueClassRealNameq ~ xpppt REPORT_PARAMETERS_MAPpsq ~ópppt 
java.util.Mappsq ~gppt 
JASPER_REPORTpsq ~ópppt (net.sf.jasperreports.engine.JasperReportpsq ~gppt REPORT_CONNECTIONpsq ~ópppt java.sql.Connectionpsq ~gppt REPORT_MAX_COUNTpsq ~ópppt java.lang.Integerpsq ~gppt REPORT_DATA_SOURCEpsq ~ópppt (net.sf.jasperreports.engine.JRDataSourcepsq ~gppt REPORT_SCRIPTLETpsq ~ópppt /net.sf.jasperreports.engine.JRAbstractScriptletpsq ~gppt 
REPORT_LOCALEpsq ~ópppt java.util.Localepsq ~gppt REPORT_RESOURCE_BUNDLEpsq ~ópppt java.util.ResourceBundlepsq ~gppt REPORT_TIME_ZONEpsq ~ópppt java.util.TimeZonepsq ~gppt REPORT_FORMAT_FACTORYpsq ~ópppt .net.sf.jasperreports.engine.util.FormatFactorypsq ~gppt REPORT_CLASS_LOADERpsq ~ópppt java.lang.ClassLoaderpsq ~gppt REPORT_URL_HANDLER_FACTORYpsq ~ópppt  java.net.URLStreamHandlerFactorypsq ~gppt REPORT_FILE_RESOLVERpsq ~ópppt -net.sf.jasperreports.engine.util.FileResolverpsq ~gppt REPORT_TEMPLATESpsq ~ópppt java.util.Collectionpsq ~gppt REPORT_VIRTUALIZERpsq ~ópppt )net.sf.jasperreports.engine.JRVirtualizerpsq ~gppt IS_IGNORE_PAGINATIONpsq ~ópppt java.lang.Booleanpsq ~g ppt cnpjEmpresapsq ~ópppt java.lang.Stringpsq ~g ppt enderecoEmpresapsq ~ópppt java.lang.Stringpsq ~g ppt 
SUBREPORT_DIRpsq ~ópppt java.lang.Stringpsq ~ópsq ~    w   t ireport.scriptlethandlingt ireport.encodingt ireport.zoomt 	ireport.xt 	ireport.yxsr java.util.HashMapÚÁÃ`Ñ F 
loadFactorI 	thresholdxp?@     w      q ~¸t 1.5q ~·t UTF-8q ~¹t 0q ~ºt 149q ~¶t 0xsr ,net.sf.jasperreports.engine.base.JRBaseQuery      'Ø [ chunkst +[Lnet/sf/jasperreports/engine/JRQueryChunk;L languageq ~ xppt sqlppppur )[Lnet.sf.jasperreports.engine.JRVariable;bæ|,·D  xp   sr /net.sf.jasperreports.engine.base.JRBaseVariable      'Ø I PSEUDO_SERIAL_VERSION_UIDB calculationB 
incrementTypeZ isSystemDefinedB 	resetTypeL calculationValuet 2Lnet/sf/jasperreports/engine/type/CalculationEnum;L 
expressionq ~ L incrementGroupq ~ +L incrementTypeValuet 4Lnet/sf/jasperreports/engine/type/IncrementTypeEnum;L incrementerFactoryClassNameq ~ L incrementerFactoryClassRealNameq ~ L initialValueExpressionq ~ L nameq ~ L 
resetGroupq ~ +L resetTypeValuet 0Lnet/sf/jasperreports/engine/type/ResetTypeEnum;L valueClassNameq ~ L valueClassRealNameq ~ xp  wî   ~r 0net.sf.jasperreports.engine.type.CalculationEnum          xq ~ 2t SYSTEMpp~r 2net.sf.jasperreports.engine.type.IncrementTypeEnum          xq ~ 2t NONEppsq ~±    uq ~´   sq ~¶t new java.lang.Integer(1)q ~wpt PAGE_NUMBERp~r .net.sf.jasperreports.engine.type.ResetTypeEnum          xq ~ 2t REPORTq ~wpsq ~È  wî   q ~Îppq ~Ñppsq ~±   uq ~´   sq ~¶t new java.lang.Integer(1)q ~wpt 
COLUMN_NUMBERp~q ~Øt PAGEq ~wpsq ~È  wî   ~q ~Ít COUNTsq ~±   uq ~´   sq ~¶t new java.lang.Integer(1)q ~wppq ~Ñppsq ~±   uq ~´   sq ~¶t new java.lang.Integer(0)q ~wpt REPORT_COUNTpq ~Ùq ~wpsq ~È  wî   q ~äsq ~±   uq ~´   sq ~¶t new java.lang.Integer(1)q ~wppq ~Ñppsq ~±   uq ~´   sq ~¶t new java.lang.Integer(0)q ~wpt 
PAGE_COUNTpq ~áq ~wpsq ~È  wî   q ~äsq ~±   uq ~´   sq ~¶t new java.lang.Integer(1)q ~wppq ~Ñppsq ~±   uq ~´   sq ~¶t new java.lang.Integer(0)q ~wpt COLUMN_COUNTp~q ~Øt COLUMNq ~wp~r <net.sf.jasperreports.engine.type.WhenResourceMissingTypeEnum          xq ~ 2t NULLq ~dp~r 0net.sf.jasperreports.engine.type.OrientationEnum          xq ~ 2t PORTRAITpp~r /net.sf.jasperreports.engine.type.PrintOrderEnum          xq ~ 2t VERTICALpppp~r 3net.sf.jasperreports.engine.type.WhenNoDataTypeEnum          xq ~ 2t NO_PAGESsr 6net.sf.jasperreports.engine.design.JRReportCompileData      'Ø L crosstabCompileDataq ~ôL datasetCompileDataq ~ôL mainDatasetCompileDataq ~ xpsq ~»?@     w       xsq ~»?@     w       xur [B¬óøTà  xp  1HÊþº¾   .v boleto_1565124890006_247606  ,net/sf/jasperreports/engine/fill/JREvaluator  parameter_enderecoEmpresa 2Lnet/sf/jasperreports/engine/fill/JRFillParameter; parameter_REPORT_LOCALE parameter_JASPER_REPORT parameter_REPORT_VIRTUALIZER parameter_REPORT_TIME_ZONE parameter_REPORT_FILE_RESOLVER parameter_REPORT_SCRIPTLET parameter_REPORT_PARAMETERS_MAP parameter_REPORT_CONNECTION parameter_REPORT_CLASS_LOADER parameter_REPORT_DATA_SOURCE $parameter_REPORT_URL_HANDLER_FACTORY parameter_IS_IGNORE_PAGINATION parameter_SUBREPORT_DIR parameter_REPORT_FORMAT_FACTORY parameter_REPORT_MAX_COUNT parameter_REPORT_TEMPLATES parameter_cnpjEmpresa  parameter_REPORT_RESOURCE_BUNDLE field_boleto46cedente .Lnet/sf/jasperreports/engine/fill/JRFillField; !field_banco46nossoNumeroFormatted field_boleto46enderecoSacado field_boleto46dvNossoNumero field_banco46codigoBarras 'field_banco46agenciaCodCedenteFormatted field_boleto46nomeSacado field_boleto46aceite field_banco46banco field_boleto46valorTitulo field_boleto46valorBoleto field_boleto46especieDocumento field_banco46numeroFormatted field_banco field_boleto46dataVencimento field_boleto46dataProcessamento field_boleto46ufSacado field_boleto46cepSacado field_boleto46dataDocumento field_banco46linhaDigitavel field_boleto46nossoNumero field_boleto46responsavel field_boleto46noDocumento field_boleto46localPagamento field_boleto46cpfSacado field_boleto46cidadeSacado field_boleto46carteira field_boleto46instrucao1 variable_PAGE_NUMBER 1Lnet/sf/jasperreports/engine/fill/JRFillVariable; variable_COLUMN_NUMBER variable_REPORT_COUNT variable_PAGE_COUNT variable_COLUMN_COUNT <init> ()V Code < =
  ?  	  A  	  C  	  E 	 	  G 
 	  I  	  K  	  M 
 	  O  	  Q  	  S  	  U  	  W  	  Y  	  [  	  ]  	  _  	  a  	  c  	  e  	  g  	  i  	  k  	  m  	  o  	  q   	  s ! 	  u " 	  w # 	  y $ 	  { % 	  } & 	   ' 	   ( 	   ) 	   * 	   + 	   , 	   - 	   . 	   / 	   0 	   1 	   2 	   3 	   4 	   5 	   6 7	   8 7	  ¡ 9 7	  £ : 7	  ¥ ; 7	  § LineNumberTable customizedInit 0(Ljava/util/Map;Ljava/util/Map;Ljava/util/Map;)V 
initParams (Ljava/util/Map;)V ¬ ­
  ® 
initFields ° ­
  ± initVars ³ ­
  ´ enderecoEmpresa ¶ 
java/util/Map ¸ get &(Ljava/lang/Object;)Ljava/lang/Object; º » ¹ ¼ 0net/sf/jasperreports/engine/fill/JRFillParameter ¾ 
REPORT_LOCALE À 
JASPER_REPORT Â REPORT_VIRTUALIZER Ä REPORT_TIME_ZONE Æ REPORT_FILE_RESOLVER È REPORT_SCRIPTLET Ê REPORT_PARAMETERS_MAP Ì REPORT_CONNECTION Î REPORT_CLASS_LOADER Ð REPORT_DATA_SOURCE Ò REPORT_URL_HANDLER_FACTORY Ô IS_IGNORE_PAGINATION Ö 
SUBREPORT_DIR Ø REPORT_FORMAT_FACTORY Ú REPORT_MAX_COUNT Ü REPORT_TEMPLATES Þ cnpjEmpresa à REPORT_RESOURCE_BUNDLE â boleto.cedente ä ,net/sf/jasperreports/engine/fill/JRFillField æ banco.nossoNumeroFormatted è boleto.enderecoSacado ê boleto.dvNossoNumero ì banco.codigoBarras î  banco.agenciaCodCedenteFormatted ð boleto.nomeSacado ò 
boleto.aceite ô banco.banco ö boleto.valorTitulo ø boleto.valorBoleto ú boleto.especieDocumento ü banco.numeroFormatted þ banco  boleto.dataVencimento boleto.dataProcessamento boleto.ufSacado boleto.cepSacado boleto.dataDocumento
 banco.linhaDigitavel boleto.nossoNumero boleto.responsavel boleto.noDocumento boleto.localPagamento boleto.cpfSacado boleto.cidadeSacado boleto.carteira boleto.instrucao1 PAGE_NUMBER /net/sf/jasperreports/engine/fill/JRFillVariable  
COLUMN_NUMBER" REPORT_COUNT$ 
PAGE_COUNT& COLUMN_COUNT( evaluate (I)Ljava/lang/Object; 
Exceptions java/lang/Throwable- java/lang/Integer/ (I)V <1
02 getValue ()Ljava/lang/Object;45
 ç6 java/lang/String8 java/lang/StringBuffer: isEmpty ()Z<=
9> valueOf &(Ljava/lang/Object;)Ljava/lang/String;@A
9B (Ljava/lang/String;)V <D
;E  CPF/CNPJ: G append ,(Ljava/lang/String;)Ljava/lang/StringBuffer;IJ
;K toString ()Ljava/lang/String;MN
;O length ()IQR
9S -000U 	substring (II)Ljava/lang/String;WX
9Y -[ (I)Ljava/lang/String;W]
9^  ` trimbN
9c   e
 ¿6 logos/logo-sicredi.pngh (it/businesslogic/ireport/barcode/BcImagej getBarcodeImage I(ILjava/lang/Object;ZZLjava/lang/String;II)Ljava/awt/image/BufferedImage;lm
kn evaluateOld getOldValueq5
 çr evaluateEstimated 
SourceFile !     4                 	     
               
                                                                                                !     "     #     $     %     &     '     (     )     *     +     ,     -     .     /     0     1     2     3     4     5     6 7    8 7    9 7    : 7    ; 7     < =  >  õ    	*· @*µ B*µ D*µ F*µ H*µ J*µ L*µ N*µ P*µ R*µ T*µ V*µ X*µ Z*µ \*µ ^*µ `*µ b*µ d*µ f*µ h*µ j*µ l*µ n*µ p*µ r*µ t*µ v*µ x*µ z*µ |*µ ~*µ *µ *µ *µ *µ *µ *µ *µ *µ *µ *µ *µ *µ *µ *µ *µ *µ  *µ ¢*µ ¤*µ ¦*µ ¨±    ©   Ú 6      	          ! " " ' # , $ 1 % 6 & ; ' @ ( E ) J * O + T , Y - ^ . c / h 0 m 1 r 2 w 3 | 4  5  6  7  8  9  :  ; ¤ < © = ® > ³ ? ¸ @ ½ A Â B Ç C Ì D Ñ E Ö F Û G à H å I ê J ï K ô L ù M þ N O   ª «  >   4     *+· ¯*,· ²*-· µ±    ©       [  \ 
 ]  ^  ¬ ­  >  »    W*+·¹ ½ À ¿À ¿µ B*+Á¹ ½ À ¿À ¿µ D*+Ã¹ ½ À ¿À ¿µ F*+Å¹ ½ À ¿À ¿µ H*+Ç¹ ½ À ¿À ¿µ J*+É¹ ½ À ¿À ¿µ L*+Ë¹ ½ À ¿À ¿µ N*+Í¹ ½ À ¿À ¿µ P*+Ï¹ ½ À ¿À ¿µ R*+Ñ¹ ½ À ¿À ¿µ T*+Ó¹ ½ À ¿À ¿µ V*+Õ¹ ½ À ¿À ¿µ X*+×¹ ½ À ¿À ¿µ Z*+Ù¹ ½ À ¿À ¿µ \*+Û¹ ½ À ¿À ¿µ ^*+Ý¹ ½ À ¿À ¿µ `*+ß¹ ½ À ¿À ¿µ b*+á¹ ½ À ¿À ¿µ d*+ã¹ ½ À ¿À ¿µ f±    ©   R    f  g $ h 6 i H j Z k l l ~ m  n ¢ o ´ p Æ q Ø r ê s ü t u  v2 wD xV y  ° ­  >      *+å¹ ½ À çÀ çµ h*+é¹ ½ À çÀ çµ j*+ë¹ ½ À çÀ çµ l*+í¹ ½ À çÀ çµ n*+ï¹ ½ À çÀ çµ p*+ñ¹ ½ À çÀ çµ r*+ó¹ ½ À çÀ çµ t*+õ¹ ½ À çÀ çµ v*+÷¹ ½ À çÀ çµ x*+ù¹ ½ À çÀ çµ z*+û¹ ½ À çÀ çµ |*+ý¹ ½ À çÀ çµ ~*+ÿ¹ ½ À çÀ çµ *+¹ ½ À çÀ çµ *+¹ ½ À çÀ çµ *+¹ ½ À çÀ çµ *+¹ ½ À çÀ çµ *+	¹ ½ À çÀ çµ *+¹ ½ À çÀ çµ *+
¹ ½ À çÀ çµ *+¹ ½ À çÀ çµ *+¹ ½ À çÀ çµ *+¹ ½ À çÀ çµ *+¹ ½ À çÀ çµ *+¹ ½ À çÀ çµ *+¹ ½ À çÀ çµ *+¹ ½ À çÀ çµ *+¹ ½ À çÀ çµ ±    ©   v       $  6  H  Z  l  ~    ¢  ´  Æ  Ø  ê  ý  # 6 I \ o   ¨ » Î á ô    ³ ­  >        `*+¹ ½ À!À!µ  *+#¹ ½ À!À!µ ¢*+%¹ ½ À!À!µ ¤*+'¹ ½ À!À!µ ¦*+)¹ ½ À!À!µ ¨±    ©       ¥  ¦ & § 9 ¨ L © _ ª *+ ,    . >      Mª  ~       /   Í   Ù   å   ñ   ý  	    !  -  ;  I  W  e  s        «  ¹  Ç    -    ù      #  D  e  s        «  ¹  Ç  Õ  ã  ñ  ÿ  
  e  s  Û  ?  M  [  i»0Y·3M§¨»0Y·3M§»0Y·3M§»0Y·3M§»0Y·3M§x»0Y·3M§l»0Y·3M§`»0Y·3M§T*´ ¶7À9M§F*´ ¶7À9M§8*´ h¶7À9M§**´ ¶7À9M§*´ ¶7À9M§*´ ~¶7À9M§ *´ v¶7À9M§ò*´ ¶7À9M§ä*´ ¶7À9M§Ö*´ ¶7À9M§È*´ |¶7À9M§º»;Y*´ ¶7À9Æ  *´ ¶7À9¶? *´ ¶7À9§ 
*´ t¶7À9¸C·FH¶L*´ ¶7À9¶L¶PM§b*´ l¶7À9M§T*´ ¶7À9¶T£ #»;Y*´ ¶7À9¸C·FV¶L¶P§ 6»;Y*´ ¶7À9¶Z¸C·F\¶L*´ ¶7À9¶_¶L¶PM§ì»;Y*´ ¶7À9Ç 	a§ #»;Y*´ ¶7À9¶d¸C·Ff¶L¶P¸C·F*´ ¶7À9Ç 	a§ 
*´ ¶7À9¶L¶PM§*´ r¶7À9M§z*´ ¶7À9M§l*´ ¶7À9M§^»;Y*´ \¶gÀ9¸C·Fi¶L¶PM§=»;Y*´ \¶gÀ9¸C·Fi¶L¶PM§*´ ¶7À9M§*´ ¶7À9M§ *´ ¶7À9M§ò*´ h¶7À9M§ä*´ ¶7À9M§Ö*´ ¶7À9M§È*´ ~¶7À9M§º*´ v¶7À9M§¬*´ ¶7À9M§*´ ¶7À9M§*´ r¶7À9M§*´ ¶7À9M§t»;Y*´ ¶7À9Æ  *´ ¶7À9¶? *´ ¶7À9§ 
*´ t¶7À9¸C·FH¶L*´ ¶7À9¶L¶PM§*´ l¶7À9M§*´ ¶7À9¶T£ #»;Y*´ ¶7À9¸C·FV¶L¶P§ 6»;Y*´ ¶7À9¶Z¸C·F\¶L*´ ¶7À9¶_¶L¶PM§ ¦»;Y*´ ¶7À9Ç 	a§ #»;Y*´ ¶7À9¶d¸C·Ff¶L¶P¸C·F*´ ¶7À9Ç 	a§ 
*´ ¶7À9¶L¶PM§ B*´ j¶7À9M§ 4*´ j¶7À9M§ &*´ |¶7À9M§ 
*´ p¶7À9¸oM,°    ©   b   ²  ´ Ð ¸ Ù ¹ Ü ½ å ¾ è Â ñ Ã ô Ç ý È  Ì	 Í Ñ Ò Ö! ×$ Û- Ü0 à; á> åI æL êW ëZ ïe ðh ôs õv ù ú þ ÿ «	®
¹¼ÇÊ"-0!"&ù'ü+,
015#6&:D;G?e@hDsEvIJNOST X«Y®]¹^¼bÇcÊgÕhØlãmæqñrôvÿw{
|ehsvÛÞ?BMP[^il£« p+ ,    . >      Mª  ~       /   Í   Ù   å   ñ   ý  	    !  -  ;  I  W  e  s        «  ¹  Ç    -    ù      #  D  e  s        «  ¹  Ç  Õ  ã  ñ  ÿ  
  e  s  Û  ?  M  [  i»0Y·3M§¨»0Y·3M§»0Y·3M§»0Y·3M§»0Y·3M§x»0Y·3M§l»0Y·3M§`»0Y·3M§T*´ ¶sÀ9M§F*´ ¶sÀ9M§8*´ h¶sÀ9M§**´ ¶sÀ9M§*´ ¶sÀ9M§*´ ~¶sÀ9M§ *´ v¶sÀ9M§ò*´ ¶sÀ9M§ä*´ ¶sÀ9M§Ö*´ ¶sÀ9M§È*´ |¶sÀ9M§º»;Y*´ ¶sÀ9Æ  *´ ¶sÀ9¶? *´ ¶sÀ9§ 
*´ t¶sÀ9¸C·FH¶L*´ ¶sÀ9¶L¶PM§b*´ l¶sÀ9M§T*´ ¶sÀ9¶T£ #»;Y*´ ¶sÀ9¸C·FV¶L¶P§ 6»;Y*´ ¶sÀ9¶Z¸C·F\¶L*´ ¶sÀ9¶_¶L¶PM§ì»;Y*´ ¶sÀ9Ç 	a§ #»;Y*´ ¶sÀ9¶d¸C·Ff¶L¶P¸C·F*´ ¶sÀ9Ç 	a§ 
*´ ¶sÀ9¶L¶PM§*´ r¶sÀ9M§z*´ ¶sÀ9M§l*´ ¶sÀ9M§^»;Y*´ \¶gÀ9¸C·Fi¶L¶PM§=»;Y*´ \¶gÀ9¸C·Fi¶L¶PM§*´ ¶sÀ9M§*´ ¶sÀ9M§ *´ ¶sÀ9M§ò*´ h¶sÀ9M§ä*´ ¶sÀ9M§Ö*´ ¶sÀ9M§È*´ ~¶sÀ9M§º*´ v¶sÀ9M§¬*´ ¶sÀ9M§*´ ¶sÀ9M§*´ r¶sÀ9M§*´ ¶sÀ9M§t»;Y*´ ¶sÀ9Æ  *´ ¶sÀ9¶? *´ ¶sÀ9§ 
*´ t¶sÀ9¸C·FH¶L*´ ¶sÀ9¶L¶PM§*´ l¶sÀ9M§*´ ¶sÀ9¶T£ #»;Y*´ ¶sÀ9¸C·FV¶L¶P§ 6»;Y*´ ¶sÀ9¶Z¸C·F\¶L*´ ¶sÀ9¶_¶L¶PM§ ¦»;Y*´ ¶sÀ9Ç 	a§ #»;Y*´ ¶sÀ9¶d¸C·Ff¶L¶P¸C·F*´ ¶sÀ9Ç 	a§ 
*´ ¶sÀ9¶L¶PM§ B*´ j¶sÀ9M§ 4*´ j¶sÀ9M§ &*´ |¶sÀ9M§ 
*´ p¶sÀ9¸oM,°    ©   b  ´ ¶ Ðº Ù» Ü¿ åÀ èÄ ñÅ ôÉ ýÊ Î	ÏÓÔØ!Ù$Ý-Þ0â;ã>çIèLìWíZñeòhös÷vûü  
«®¹¼ÇÊ"-0#$(ù)ü-.
237#8&<D=GAeBhFsGvKLPQUV Z«[®_¹`¼dÇeÊiÕjØnãoæsñtôxÿy}
~ehsvÛÞ?BMP[^ i¡l¥­ t+ ,    . >      Mª  ~       /   Í   Ù   å   ñ   ý  	    !  -  ;  I  W  e  s        «  ¹  Ç    -    ù      #  D  e  s        «  ¹  Ç  Õ  ã  ñ  ÿ  
  e  s  Û  ?  M  [  i»0Y·3M§¨»0Y·3M§»0Y·3M§»0Y·3M§»0Y·3M§x»0Y·3M§l»0Y·3M§`»0Y·3M§T*´ ¶7À9M§F*´ ¶7À9M§8*´ h¶7À9M§**´ ¶7À9M§*´ ¶7À9M§*´ ~¶7À9M§ *´ v¶7À9M§ò*´ ¶7À9M§ä*´ ¶7À9M§Ö*´ ¶7À9M§È*´ |¶7À9M§º»;Y*´ ¶7À9Æ  *´ ¶7À9¶? *´ ¶7À9§ 
*´ t¶7À9¸C·FH¶L*´ ¶7À9¶L¶PM§b*´ l¶7À9M§T*´ ¶7À9¶T£ #»;Y*´ ¶7À9¸C·FV¶L¶P§ 6»;Y*´ ¶7À9¶Z¸C·F\¶L*´ ¶7À9¶_¶L¶PM§ì»;Y*´ ¶7À9Ç 	a§ #»;Y*´ ¶7À9¶d¸C·Ff¶L¶P¸C·F*´ ¶7À9Ç 	a§ 
*´ ¶7À9¶L¶PM§*´ r¶7À9M§z*´ ¶7À9M§l*´ ¶7À9M§^»;Y*´ \¶gÀ9¸C·Fi¶L¶PM§=»;Y*´ \¶gÀ9¸C·Fi¶L¶PM§*´ ¶7À9M§*´ ¶7À9M§ *´ ¶7À9M§ò*´ h¶7À9M§ä*´ ¶7À9M§Ö*´ ¶7À9M§È*´ ~¶7À9M§º*´ v¶7À9M§¬*´ ¶7À9M§*´ ¶7À9M§*´ r¶7À9M§*´ ¶7À9M§t»;Y*´ ¶7À9Æ  *´ ¶7À9¶? *´ ¶7À9§ 
*´ t¶7À9¸C·FH¶L*´ ¶7À9¶L¶PM§*´ l¶7À9M§*´ ¶7À9¶T£ #»;Y*´ ¶7À9¸C·FV¶L¶P§ 6»;Y*´ ¶7À9¶Z¸C·F\¶L*´ ¶7À9¶_¶L¶PM§ ¦»;Y*´ ¶7À9Ç 	a§ #»;Y*´ ¶7À9¶d¸C·Ff¶L¶P¸C·F*´ ¶7À9Ç 	a§ 
*´ ¶7À9¶L¶PM§ B*´ j¶7À9M§ 4*´ j¶7À9M§ &*´ |¶7À9M§ 
*´ p¶7À9¸oM,°    ©   b  ¶ ¸ Ð¼ Ù½ ÜÁ åÂ èÆ ñÇ ôË ýÌ Ð	ÑÕÖÚ!Û$ß-à0ä;å>éIêLîWïZóeôhøsùvýþ «
®¹¼ÇÊ" -!0%&*ù+ü/0
459#:&>D?GCeDhHsIvMNRSWX \«]®a¹b¼fÇgÊkÕlØpãqæuñvôzÿ{
ehsvÛÞ?BMP[^¢i£l§¯ u    t _1565124890006_247606t 2net.sf.jasperreports.engine.design.JRJavacCompiler