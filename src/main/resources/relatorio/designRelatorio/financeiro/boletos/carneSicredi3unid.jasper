¬í sr (net.sf.jasperreports.engine.JasperReport      'Ø L compileDatat Ljava/io/Serializable;L compileNameSuffixt Ljava/lang/String;L 
compilerClassq ~ xr -net.sf.jasperreports.engine.base.JRBaseReport      'Ø +I PSEUDO_SERIAL_VERSION_UIDI bottomMarginI columnCountI 
columnSpacingI columnWidthZ ignorePaginationZ isFloatColumnFooterZ isSummaryNewPageZ  isSummaryWithPageHeaderAndFooterZ isTitleNewPageI 
leftMarginB orientationI 
pageHeightI 	pageWidthB 
printOrderI rightMarginI 	topMarginB whenNoDataTypeL 
backgroundt $Lnet/sf/jasperreports/engine/JRBand;L columnFooterq ~ L columnHeaderq ~ [ datasetst ([Lnet/sf/jasperreports/engine/JRDataset;L defaultFontt *Lnet/sf/jasperreports/engine/JRReportFont;L defaultStylet %Lnet/sf/jasperreports/engine/JRStyle;L detailq ~ L 
detailSectiont 'Lnet/sf/jasperreports/engine/JRSection;[ fontst +[Lnet/sf/jasperreports/engine/JRReportFont;L formatFactoryClassq ~ L 
importsSett Ljava/util/Set;L languageq ~ L lastPageFooterq ~ L mainDatasett 'Lnet/sf/jasperreports/engine/JRDataset;L nameq ~ L noDataq ~ L orientationValuet 2Lnet/sf/jasperreports/engine/type/OrientationEnum;L 
pageFooterq ~ L 
pageHeaderq ~ L printOrderValuet 1Lnet/sf/jasperreports/engine/type/PrintOrderEnum;[ stylest &[Lnet/sf/jasperreports/engine/JRStyle;L summaryq ~ [ 	templatest /[Lnet/sf/jasperreports/engine/JRReportTemplate;L titleq ~ L whenNoDataTypeValuet 5Lnet/sf/jasperreports/engine/type/WhenNoDataTypeEnum;xp  wî             ?        
   J  S    
     sr +net.sf.jasperreports.engine.base.JRBaseBand      'Ø I PSEUDO_SERIAL_VERSION_UIDI heightZ isSplitAllowedL printWhenExpressiont *Lnet/sf/jasperreports/engine/JRExpression;L 	splitTypet Ljava/lang/Byte;L splitTypeValuet 0Lnet/sf/jasperreports/engine/type/SplitTypeEnum;xr 3net.sf.jasperreports.engine.base.JRBaseElementGroup      'Ø L childrent Ljava/util/List;L elementGroupt ,Lnet/sf/jasperreports/engine/JRElementGroup;xpsr java.util.ArrayListxÒÇa I sizexp    w    xp  wî    pp~r .net.sf.jasperreports.engine.type.SplitTypeEnum          xr java.lang.Enum          xpt STRETCHppppppsr .net.sf.jasperreports.engine.base.JRBaseSection      'Ø [ bandst %[Lnet/sf/jasperreports/engine/JRBand;xpur %[Lnet.sf.jasperreports.engine.JRBand;Ý~ìÊ5  xp   sq ~ sq ~    gw   gsr +net.sf.jasperreports.engine.base.JRBaseLine      'Ø I PSEUDO_SERIAL_VERSION_UIDB 	directionL directionValuet 4Lnet/sf/jasperreports/engine/type/LineDirectionEnum;xr 5net.sf.jasperreports.engine.base.JRBaseGraphicElement      'Ø I PSEUDO_SERIAL_VERSION_UIDL fillq ~ L 	fillValuet +Lnet/sf/jasperreports/engine/type/FillEnum;L linePent #Lnet/sf/jasperreports/engine/JRPen;L penq ~ xr .net.sf.jasperreports.engine.base.JRBaseElement      'Ø I PSEUDO_SERIAL_VERSION_UIDI heightZ isPrintInFirstWholeBandZ isPrintRepeatedValuesZ isPrintWhenDetailOverflowsZ isRemoveLineWhenBlankB positionTypeB stretchTypeI widthI xI yL 	backcolort Ljava/awt/Color;L defaultStyleProvidert 4Lnet/sf/jasperreports/engine/JRDefaultStyleProvider;L elementGroupq ~ L 	forecolorq ~ -L keyq ~ L modeq ~ L 	modeValuet +Lnet/sf/jasperreports/engine/type/ModeEnum;L parentStyleq ~ L parentStyleNameReferenceq ~ L positionTypeValuet 3Lnet/sf/jasperreports/engine/type/PositionTypeEnum;L printWhenExpressionq ~ L printWhenGroupChangest %Lnet/sf/jasperreports/engine/JRGroup;L 
propertiesMapt -Lnet/sf/jasperreports/engine/JRPropertiesMap;[ propertyExpressionst 3[Lnet/sf/jasperreports/engine/JRPropertyExpression;L stretchTypeValuet 2Lnet/sf/jasperreports/engine/type/StretchTypeEnum;xp  wî           l       pq ~ q ~ %pppppp~r 1net.sf.jasperreports.engine.type.PositionTypeEnum          xq ~ t FIX_RELATIVE_TO_TOPpppp~r 0net.sf.jasperreports.engine.type.StretchTypeEnum          xq ~ t 
NO_STRETCH  wîppsr *net.sf.jasperreports.engine.base.JRBasePen      'Ø I PSEUDO_SERIAL_VERSION_UIDL 	lineColorq ~ -L 	lineStyleq ~ L lineStyleValuet 0Lnet/sf/jasperreports/engine/type/LineStyleEnum;L 	lineWidtht Ljava/lang/Float;L penContainert ,Lnet/sf/jasperreports/engine/JRPenContainer;xp  wîpppsr java.lang.FloatÚíÉ¢Û<ðì F valuexr java.lang.Number¬à  xp@   q ~ 5p  wî ~r 2net.sf.jasperreports.engine.type.LineDirectionEnum          xq ~ t TOP_DOWNsq ~ '  wî          Ì   s   pq ~ q ~ %ppppppq ~ 7ppppq ~ :  wîppsq ~ <  wîpppsq ~ A?q ~ Gp  wî q ~ Esq ~ '  wî              7   pq ~ q ~ %ppppppq ~ 7ppppq ~ :  wîppsq ~ <  wîpppsq ~ A@   q ~ Jp  wî q ~ Esq ~ '  wî              Ø   pq ~ q ~ %ppppppq ~ 7ppppq ~ :  wîppsq ~ <  wîpppsq ~ A@   q ~ Mp  wî q ~ Esq ~ '  wî          Ë   t   &pq ~ q ~ %ppppppq ~ 7ppppq ~ :  wîppsq ~ <  wîpppsq ~ A?  q ~ Pp  wî q ~ Esq ~ '  wî   ²          ½   pq ~ q ~ %ppppppq ~ 7ppppq ~ :  wîppsq ~ <  wîpppsq ~ A?   q ~ Sp  wî q ~ Esr 1net.sf.jasperreports.engine.base.JRBaseStaticText      'Ø L textq ~ xr 2net.sf.jasperreports.engine.base.JRBaseTextElement      'Ø %I PSEUDO_SERIAL_VERSION_UIDL borderq ~ L borderColorq ~ -L bottomBorderq ~ L bottomBorderColorq ~ -L 
bottomPaddingt Ljava/lang/Integer;L fontNameq ~ L fontSizeq ~ XL horizontalAlignmentq ~ L horizontalAlignmentValuet 6Lnet/sf/jasperreports/engine/type/HorizontalAlignEnum;L isBoldt Ljava/lang/Boolean;L isItalicq ~ ZL 
isPdfEmbeddedq ~ ZL isStrikeThroughq ~ ZL isStyledTextq ~ ZL isUnderlineq ~ ZL 
leftBorderq ~ L leftBorderColorq ~ -L leftPaddingq ~ XL lineBoxt 'Lnet/sf/jasperreports/engine/JRLineBox;L lineSpacingq ~ L lineSpacingValuet 2Lnet/sf/jasperreports/engine/type/LineSpacingEnum;L markupq ~ L paddingq ~ XL pdfEncodingq ~ L pdfFontNameq ~ L 
reportFontq ~ L rightBorderq ~ L rightBorderColorq ~ -L rightPaddingq ~ XL rotationq ~ L 
rotationValuet /Lnet/sf/jasperreports/engine/type/RotationEnum;L 	topBorderq ~ L topBorderColorq ~ -L 
topPaddingq ~ XL verticalAlignmentq ~ L verticalAlignmentValuet 4Lnet/sf/jasperreports/engine/type/VerticalAlignEnum;xq ~ ,  wî          C   v   pq ~ q ~ %ppppppq ~ 7ppppq ~ :  wîppppppsr java.lang.Integerâ ¤÷8 I valuexq ~ B   ppsr java.lang.BooleanÍ rÕúî Z valuexp ppppppppsr .net.sf.jasperreports.engine.base.JRBaseLineBox      'Ø L 
bottomPaddingq ~ XL 	bottomPent +Lnet/sf/jasperreports/engine/base/JRBoxPen;L boxContainert ,Lnet/sf/jasperreports/engine/JRBoxContainer;L leftPaddingq ~ XL leftPenq ~ eL paddingq ~ XL penq ~ eL rightPaddingq ~ XL rightPenq ~ eL 
topPaddingq ~ XL topPenq ~ exppsr 3net.sf.jasperreports.engine.base.JRBaseBoxBottomPen      'Ø  xr -net.sf.jasperreports.engine.base.JRBaseBoxPen      'Ø L lineBoxq ~ [xq ~ <  wîppppq ~ gq ~ gq ~ _psr 1net.sf.jasperreports.engine.base.JRBaseBoxLeftPen      'Ø  xq ~ i  wîppppq ~ gq ~ gpsq ~ i  wîppppq ~ gq ~ gpsr 2net.sf.jasperreports.engine.base.JRBaseBoxRightPen      'Ø  xq ~ i  wîppppq ~ gq ~ gpsr 0net.sf.jasperreports.engine.base.JRBaseBoxTopPen      'Ø  xq ~ i  wîppppq ~ gq ~ gpppppppppppppppppt LOCAL DO PAGAMENTOsq ~ V  wî           {  À   pq ~ q ~ %ppppppq ~ 7ppppq ~ :  wîppppppq ~ apppppppppppsq ~ dpsq ~ h  wîppppq ~ tq ~ tq ~ spsq ~ k  wîppppq ~ tq ~ tpsq ~ i  wîppppq ~ tq ~ tpsq ~ n  wîppppq ~ tq ~ tpsq ~ p  wîppppq ~ tq ~ tpppppppppppppppppt 
VENCIMENTOsq ~ '  wî          Ì   s   :pq ~ q ~ %ppppppq ~ 7ppppq ~ :  wîppsq ~ <  wîpppsq ~ A?  q ~ {p  wî q ~ Esq ~ V  wî          C   v   (pq ~ q ~ %ppppppq ~ 7ppppq ~ :  wîppppppq ~ apppppppppppsq ~ dpsq ~ h  wîppppq ~ q ~ q ~ ~psq ~ k  wîppppq ~ q ~ psq ~ i  wîppppq ~ q ~ psq ~ n  wîppppq ~ q ~ psq ~ p  wîppppq ~ q ~ pppppppppppppppppt (NOME DO BENEFICIÃRIO/CPF/CNPJ/ENDEREÃOsq ~ V  wî           {  À   (pq ~ q ~ %ppppppq ~ 7ppppq ~ :  wîppppppq ~ apppppppppppsq ~ dpsq ~ h  wîppppq ~ q ~ q ~ psq ~ k  wîppppq ~ q ~ psq ~ i  wîppppq ~ q ~ psq ~ n  wîppppq ~ q ~ psq ~ p  wîppppq ~ q ~ pppppppppppppppppt #AGÃNCIA / CÃDIGO DO BENEFICIÃRIOsq ~ '  wî          Ì   s   Npq ~ q ~ %ppppppq ~ 7ppppq ~ :  wîppsq ~ <  wîpppsq ~ A?   q ~ p  wî q ~ Esq ~ V  wî           @   v   <pq ~ q ~ %ppppppq ~ 7ppppq ~ :  wîppppppq ~ apppppppppppsq ~ dpsq ~ h  wîppppq ~ q ~ q ~ psq ~ k  wîppppq ~ q ~ psq ~ i  wîppppq ~ q ~ psq ~ n  wîppppq ~ q ~ psq ~ p  wîppppq ~ q ~ pppppppppppppppppt DATA DO DOCUMENTOsq ~ V  wî           A   ¸   <pq ~ q ~ %ppppppq ~ 7ppppq ~ :  wîppppppq ~ apppppppppppsq ~ dpsq ~ h  wîppppq ~ q ~ q ~ psq ~ k  wîppppq ~ q ~ psq ~ i  wîppppq ~ q ~ psq ~ n  wîppppq ~ q ~ psq ~ p  wîppppq ~ q ~ pppppppppppppppppt N. DO DOCUMENTOsq ~ V  wî           $   û   <pq ~ q ~ %ppppppq ~ 7ppppq ~ :  wîppppppq ~ apppppppppppsq ~ dpsq ~ h  wîppppq ~ ¢q ~ ¢q ~ ¡psq ~ k  wîppppq ~ ¢q ~ ¢psq ~ i  wîppppq ~ ¢q ~ ¢psq ~ n  wîppppq ~ ¢q ~ ¢psq ~ p  wîppppq ~ ¢q ~ ¢pppppppppppppppppt ESPÃCIEsq ~ V  wî           '  "   <pq ~ q ~ %ppppppq ~ 7ppppq ~ :  wîppppppq ~ apppppppppppsq ~ dpsq ~ h  wîppppq ~ ªq ~ ªq ~ ©psq ~ k  wîppppq ~ ªq ~ ªpsq ~ i  wîppppq ~ ªq ~ ªpsq ~ n  wîppppq ~ ªq ~ ªpsq ~ p  wîppppq ~ ªq ~ ªpppppppppppppppppt ACEITEsq ~ V  wî           n  K   <pq ~ q ~ %ppppppq ~ 7ppppq ~ :  wîppppppq ~ apppppppppppsq ~ dpsq ~ h  wîppppq ~ ²q ~ ²q ~ ±psq ~ k  wîppppq ~ ²q ~ ²psq ~ i  wîppppq ~ ²q ~ ²psq ~ n  wîppppq ~ ²q ~ ²psq ~ p  wîppppq ~ ²q ~ ²pppppppppppppppppt DATA PROCESSAMENTOsq ~ '  wî          Ì   s   cpq ~ q ~ %ppppppq ~ 7ppppq ~ :  wîppsq ~ <  wîpppsq ~ A?   q ~ ¹p  wî q ~ Esq ~ V  wî           )   u   Ppq ~ q ~ %ppppppq ~ 7ppppq ~ :  wîppppppq ~ apppppppppppsq ~ dpsq ~ h  wîppppq ~ ½q ~ ½q ~ ¼psq ~ k  wîppppq ~ ½q ~ ½psq ~ i  wîppppq ~ ½q ~ ½psq ~ n  wîppppq ~ ½q ~ ½psq ~ p  wîppppq ~ ½q ~ ½pppppppppppppppppt USO DO BANCOsq ~ V  wî              ¸   Ppq ~ q ~ %ppppppq ~ 7ppppq ~ :  wîppppppq ~ apppppppppppsq ~ dpsq ~ h  wîppppq ~ Åq ~ Åq ~ Äpsq ~ k  wîppppq ~ Åq ~ Åpsq ~ i  wîppppq ~ Åq ~ Åpsq ~ n  wîppppq ~ Åq ~ Åpsq ~ p  wîppppq ~ Åq ~ Åpppppppppppppppppt CARTEIRAsq ~ V  wî              Û   Ppq ~ q ~ %ppppppq ~ 7ppppq ~ :  wîppppppq ~ apppppppppppsq ~ dpsq ~ h  wîppppq ~ Íq ~ Íq ~ Ìpsq ~ k  wîppppq ~ Íq ~ Ípsq ~ i  wîppppq ~ Íq ~ Ípsq ~ n  wîppppq ~ Íq ~ Ípsq ~ p  wîppppq ~ Íq ~ Ípppppppppppppppppt MOEDAsq ~ V  wî           N   û   Ppq ~ q ~ %ppppppq ~ 7ppppq ~ :  wîppppppq ~ apppppppppppsq ~ dpsq ~ h  wîppppq ~ Õq ~ Õq ~ Ôpsq ~ k  wîppppq ~ Õq ~ Õpsq ~ i  wîppppq ~ Õq ~ Õpsq ~ n  wîppppq ~ Õq ~ Õpsq ~ p  wîppppq ~ Õq ~ Õpppppppppppppppppt 
QUANTIDADEsq ~ V  wî           n  K   Qpq ~ q ~ %ppppppq ~ 7ppppq ~ :  wîppppppq ~ apppppppppppsq ~ dpsq ~ h  wîppppq ~ Ýq ~ Ýq ~ Üpsq ~ k  wîppppq ~ Ýq ~ Ýpsq ~ i  wîppppq ~ Ýq ~ Ýpsq ~ n  wîppppq ~ Ýq ~ Ýpsq ~ p  wîppppq ~ Ýq ~ Ýpppppppppppppppppt VALORsq ~ '  wî          Ì   s   Åpq ~ q ~ %ppppppq ~ 7ppppq ~ :  wîppsq ~ <  wîpppsq ~ A?  q ~ äp  wî q ~ Esq ~ V  wî           Õ   v   epq ~ q ~ %ppppppq ~ 7ppppq ~ :  wîppppppq ~ apppppppppppsq ~ dpsq ~ h  wîppppq ~ èq ~ èq ~ çpsq ~ k  wîppppq ~ èq ~ èpsq ~ i  wîppppq ~ èq ~ èpsq ~ n  wîppppq ~ èq ~ èpsq ~ p  wîppppq ~ èq ~ èpppppppppppppppppt 2INFORMAÃÃES DE RESPONSABILIDADE DO BENEFICIÃRIOsq ~ V  wî           {  À   <pq ~ q ~ %ppppppq ~ 7ppppq ~ :  wîppppppq ~ apppppppppppsq ~ dpsq ~ h  wîppppq ~ ðq ~ ðq ~ ïpsq ~ k  wîppppq ~ ðq ~ ðpsq ~ i  wîppppq ~ ðq ~ ðpsq ~ n  wîppppq ~ ðq ~ ðpsq ~ p  wîppppq ~ ðq ~ ðpppppppppppppppppt 
NOSSO NÃMEROsq ~ V  wî           {  À   Ppq ~ q ~ %ppppppq ~ 7ppppq ~ :  wîppppppq ~ apppppppppppsq ~ dpsq ~ h  wîppppq ~ øq ~ øq ~ ÷psq ~ k  wîppppq ~ øq ~ øpsq ~ i  wîppppq ~ øq ~ øpsq ~ n  wîppppq ~ øq ~ øpsq ~ p  wîppppq ~ øq ~ øpppppppppppppppppt (=) VALOR DO DOCUMENTOsq ~ V  wî           ,   v   pq ~ q ~ %ppppppq ~ 7ppppq ~ :  wîppppppq ~ apppppppppppsq ~ dpsq ~ h  wîppppq ~ q ~ q ~ ÿpsq ~ k  wîppppq ~ q ~ psq ~ i  wîppppq ~ q ~ psq ~ n  wîppppq ~ q ~ psq ~ p  wîppppq ~ q ~ pppppppppppppppppt PAGADORsq ~ '  wî                pq ~ q ~ %ppppppq ~ 7ppppq ~ :  wîppsq ~ <  wîpppsq ~ A@   q ~p  wî q ~ Esq ~ '  wî   ³           s   pq ~ q ~ %ppppppq ~ 7ppppq ~ :  wîppsq ~ <  wîppppq ~
p  wî q ~ Esq ~ '  wî   ³          ?   pq ~ q ~ %ppppppq ~ 7ppppq ~ :  wîppsq ~ <  wîppppq ~p  wî q ~ Esq ~ '  wî             ¾   vpq ~ q ~ %ppppppq ~ 7ppppq ~ :  wîppsq ~ <  wîppppq ~p  wî q ~ Esq ~ '  wî             ¾   ±pq ~ q ~ %ppppppq ~ 7ppppq ~ :  wîppsq ~ <  wîppppq ~p  wî q ~ Esq ~ '  wî             ¾   pq ~ q ~ %ppppppq ~ 7ppppq ~ :  wîppsq ~ <  wîppppq ~p  wî q ~ Esq ~ V  wî           {  À   epq ~ q ~ %ppppppq ~ 7ppppq ~ :  wîppppppq ~ apppppppppppsq ~ dpsq ~ h  wîppppq ~q ~q ~psq ~ k  wîppppq ~q ~psq ~ i  wîppppq ~q ~psq ~ n  wîppppq ~q ~psq ~ p  wîppppq ~q ~pppppppppppppppppt (-) DESCONTO/ABATIMENTOsq ~ V  wî           {  À   pq ~ q ~ %ppppppq ~ 7ppppq ~ :  wîppppppq ~ apppppppppppsq ~ dpsq ~ h  wîppppq ~q ~q ~psq ~ k  wîppppq ~q ~psq ~ i  wîppppq ~q ~psq ~ n  wîppppq ~q ~psq ~ p  wîppppq ~q ~pppppppppppppppppt (+) JUROS/MULTAsq ~ V  wî           {  À    pq ~ q ~ %ppppppq ~ 7ppppq ~ :  wîppppppq ~ apppppppppppsq ~ dpsq ~ h  wîppppq ~%q ~%q ~$psq ~ k  wîppppq ~%q ~%psq ~ i  wîppppq ~%q ~%psq ~ n  wîppppq ~%q ~%psq ~ p  wîppppq ~%q ~%pppppppppppppppppt (+) OUTROS ACRÃSCIMOSsq ~ V  wî           {  À   ²pq ~ q ~ %ppppppq ~ 7ppppq ~ :  wîppppppq ~ apppppppppppsq ~ dpsq ~ h  wîppppq ~-q ~-q ~,psq ~ k  wîppppq ~-q ~-psq ~ i  wîppppq ~-q ~-psq ~ n  wîppppq ~-q ~-psq ~ p  wîppppq ~-q ~-pppppppppppppppppt (=) VALOR COBRADOsq ~ '  wî   ø           l   pq ~ q ~ %ppppppq ~ 7ppppq ~ :  wîppsq ~ <  wîppppq ~4p  wî q ~ Esq ~ '  wî   é               pq ~ q ~ %ppppppq ~ 7ppppq ~ :  wîppsq ~ <  wîppppq ~6p  wî q ~ Esq ~ '  wî           m       ûpq ~ q ~ %ppppppq ~ 7ppppq ~ :  wîppsq ~ <  wîppppq ~8p  wî q ~ Esq ~ '  wî             o    pq ~ q ~ %ppppppq ~ 7ppppq ~ :  wîppsq ~ <  wîpp~r .net.sf.jasperreports.engine.type.LineStyleEnum          xq ~ t DASHEDsq ~ A?   q ~:p  wî q ~ Esq ~ '  wî           k      &pq ~ q ~ %ppppppq ~ 7ppppq ~ :  wîppsq ~ <  wîppppq ~@p  wî q ~ Esq ~ '  wî           k      9pq ~ q ~ %ppppppq ~ 7ppppq ~ :  wîppsq ~ <  wîppppq ~Bp  wî q ~ Esq ~ '  wî           l       upq ~ q ~ %ppppppq ~ 7ppppq ~ :  wîppsq ~ <  wîppppq ~Dp  wî q ~ Esq ~ '  wî           k      pq ~ q ~ %ppppppq ~ 7ppppq ~ :  wîppsq ~ <  wîppppq ~Fp  wî q ~ Esq ~ '  wî           k      pq ~ q ~ %ppppppq ~ 7ppppq ~ :  wîppsq ~ <  wîppppq ~Hp  wî q ~ Esq ~ '  wî           k      °pq ~ q ~ %ppppppq ~ 7ppppq ~ :  wîppsq ~ <  wîppppq ~Jp  wî q ~ Esq ~ '  wî           k      Åpq ~ q ~ %ppppppq ~ 7ppppq ~ :  wîppsq ~ <  wîppppq ~Lp  wî q ~ Esq ~ '  wî           k      Øpq ~ q ~ %ppppppq ~ 7ppppq ~ :  wîppsq ~ <  wîppppq ~Np  wî q ~ Esq ~ '  wî           k      Mpq ~ q ~ %ppppppq ~ 7ppppq ~ :  wîppsq ~ <  wîppppq ~Pp  wî q ~ Esq ~ '  wî           k      apq ~ q ~ %ppppppq ~ 7ppppq ~ :  wîppsq ~ <  wîppppq ~Rp  wî q ~ Esq ~ V  wî           h      pq ~ q ~ %ppppppq ~ 7ppppq ~ :  wîppppppq ~ apppppppppppsq ~ dpsq ~ h  wîppppq ~Uq ~Uq ~Tpsq ~ k  wîppppq ~Uq ~Upsq ~ i  wîppppq ~Uq ~Upsq ~ n  wîppppq ~Uq ~Upsq ~ p  wîppppq ~Uq ~Upppppppppppppppppt 
VENCIMENTOsq ~ V  wî           i      <pq ~ q ~ %ppppppq ~ 7ppppq ~ :  wîppppppq ~ apppppppppppsq ~ dpsq ~ h  wîppppq ~]q ~]q ~\psq ~ k  wîppppq ~]q ~]psq ~ i  wîppppq ~]q ~]psq ~ n  wîppppq ~]q ~]psq ~ p  wîppppq ~]q ~]pppppppppppppppppt 
NOSSO NÃMEROsq ~ V  wî           i      (pq ~ q ~ %ppppppq ~ 7ppppq ~ :  wîppppppq ~ apppppppppppsq ~ dpsq ~ h  wîppppq ~eq ~eq ~dpsq ~ k  wîppppq ~eq ~epsq ~ i  wîppppq ~eq ~epsq ~ n  wîppppq ~eq ~epsq ~ p  wîppppq ~eq ~epppppppppppppppppt #AGÃNCIA / CÃDIGO DO BENEFICIÃRIOsq ~ V  wî           i       pq ~ q ~ %ppppppq ~ 7ppppq ~ :  wîppppppq ~ apppppppppppsq ~ dpsq ~ h  wîppppq ~mq ~mq ~lpsq ~ k  wîppppq ~mq ~mpsq ~ i  wîppppq ~mq ~mpsq ~ n  wîppppq ~mq ~mpsq ~ p  wîppppq ~mq ~mpppppppppppppppppt (+) OUTROS ACRÃSCIMOSsq ~ V  wî           i      Ppq ~ q ~ %ppppppq ~ 7ppppq ~ :  wîppppppq ~ apppppppppppsq ~ dpsq ~ h  wîppppq ~uq ~uq ~tpsq ~ k  wîppppq ~uq ~upsq ~ i  wîppppq ~uq ~upsq ~ n  wîppppq ~uq ~upsq ~ p  wîppppq ~uq ~upppppppppppppppppt (=) VALOR DO DOCUMENTOsq ~ V  wî           i      ²pq ~ q ~ %ppppppq ~ 7ppppq ~ :  wîppppppq ~ apppppppppppsq ~ dpsq ~ h  wîppppq ~}q ~}q ~|psq ~ k  wîppppq ~}q ~}psq ~ i  wîppppq ~}q ~}psq ~ n  wîppppq ~}q ~}psq ~ p  wîppppq ~}q ~}pppppppppppppppppt (=) VALOR COBRADOsq ~ V  wî           i      pq ~ q ~ %ppppppq ~ 7ppppq ~ :  wîppppppq ~ apppppppppppsq ~ dpsq ~ h  wîppppq ~q ~q ~psq ~ k  wîppppq ~q ~psq ~ i  wîppppq ~q ~psq ~ n  wîppppq ~q ~psq ~ p  wîppppq ~q ~pppppppppppppppppt (+) JUROS/MULTAsq ~ V  wî           i      epq ~ q ~ %ppppppq ~ 7ppppq ~ :  wîppppppq ~ apppppppppppsq ~ dpsq ~ h  wîppppq ~q ~q ~psq ~ k  wîppppq ~q ~psq ~ i  wîppppq ~q ~psq ~ n  wîppppq ~q ~psq ~ p  wîppppq ~q ~pppppppppppppppppt (-) DESCONTO/ABATIMENTOsq ~ V  wî           i      Çpq ~ q ~ %ppppppq ~ 7ppppq ~ :  wîppppppq ~ apppppppppppsq ~ dpsq ~ h  wîppppq ~q ~q ~psq ~ k  wîppppq ~q ~psq ~ i  wîppppq ~q ~psq ~ n  wîppppq ~q ~psq ~ p  wîppppq ~q ~pppppppppppppppppt NÃMERO DO DOC.sq ~ V  wî           i      Ûpq ~ q ~ %ppppppq ~ 7ppppq ~ :  wîppppppq ~ apppppppppppsq ~ dpsq ~ h  wîppppq ~q ~q ~psq ~ k  wîppppq ~q ~psq ~ i  wîppppq ~q ~psq ~ n  wîppppq ~q ~psq ~ p  wîppppq ~q ~pppppppppppppppppt PAGADORsq ~ '  wî              ¶   ;pq ~ q ~ %ppppppq ~ 7ppppq ~ :  wîppsq ~ <  wîppppq ~¤p  wî q ~ Esq ~ '  wî   (           ù   ;pq ~ q ~ %ppppppq ~ 7ppppq ~ :  wîppsq ~ <  wîppppq ~¦p  wî q ~ Esq ~ '  wî                 ;pq ~ q ~ %ppppppq ~ 7ppppq ~ :  wîppsq ~ <  wîppppq ~¨p  wî q ~ Esq ~ '  wî   (          I   ;pq ~ q ~ %ppppppq ~ 7ppppq ~ :  wîppsq ~ <  wîppppq ~ªp  wî q ~ Esq ~ '  wî              ¶   Opq ~ q ~ %ppppppq ~ 7ppppq ~ :  wîppsq ~ <  wîppppq ~¬p  wî q ~ Esq ~ '  wî              Ù   Opq ~ q ~ %ppppppq ~ 7ppppq ~ :  wîppsq ~ <  wîppppq ~®p  wî q ~ Esq ~ '  wî          ?       pq ~ q ~ %ppppppq ~ 7ppppq ~ :  wîppsq ~ <  wîppq ~=sq ~ A?   q ~°p  wî q ~ Esr 0net.sf.jasperreports.engine.base.JRBaseTextField      'Ø I PSEUDO_SERIAL_VERSION_UIDI 
bookmarkLevelB evaluationTimeB hyperlinkTargetB 
hyperlinkTypeZ isStretchWithOverflowL anchorNameExpressionq ~ L evaluationGroupq ~ 1L evaluationTimeValuet 5Lnet/sf/jasperreports/engine/type/EvaluationTimeEnum;L 
expressionq ~ L hyperlinkAnchorExpressionq ~ L hyperlinkPageExpressionq ~ [ hyperlinkParameterst 3[Lnet/sf/jasperreports/engine/JRHyperlinkParameter;L hyperlinkReferenceExpressionq ~ L hyperlinkTooltipExpressionq ~ L isBlankWhenNullq ~ ZL 
linkTargetq ~ L linkTypeq ~ L patternq ~ xq ~ W  wî         9     pq ~ q ~ %ppppppq ~ 7ppppq ~ :  wîppppppsq ~ `   ppsq ~ bppppppppsq ~ dpsq ~ h  wîppppq ~¹q ~¹q ~¶psq ~ k  wîppppq ~¹q ~¹psq ~ i  wîppppq ~¹q ~¹psq ~ n  wîppppq ~¹q ~¹psq ~ p  wîppppq ~¹q ~¹pppppppppppppppp~r 2net.sf.jasperreports.engine.type.VerticalAlignEnum          xq ~ t MIDDLE  wî        pp~r 3net.sf.jasperreports.engine.type.EvaluationTimeEnum          xq ~ t NOWsr 1net.sf.jasperreports.engine.base.JRBaseExpression      'Ø I id[ chunkst 0[Lnet/sf/jasperreports/engine/JRExpressionChunk;L valueClassNameq ~ L valueClassRealNameq ~ xp   ur 0[Lnet.sf.jasperreports.engine.JRExpressionChunk;mYÏÞiK£U  xp   sr 6net.sf.jasperreports.engine.base.JRBaseExpressionChunk      'Ø B typeL textq ~ xpt banco.linhaDigitavelt java.lang.Stringppppppppppsq ~³  wî   
        {  À   Dpq ~ q ~ %ppppppq ~ 7ppppq ~ :  wîppppppsq ~ `   p~r 4net.sf.jasperreports.engine.type.HorizontalAlignEnum          xq ~ t RIGHTpppppppppsq ~ dpsq ~ h  wîppppq ~Óq ~Óq ~Îpsq ~ k  wîppppq ~Óq ~Ópsq ~ i  wîppppq ~Óq ~Ópsq ~ n  wîppppq ~Óq ~Ópsq ~ p  wîppppq ~Óq ~Óppppppppppppppppp  wî        ppq ~Ãsq ~Å   	uq ~È   sq ~Êt banco.nossoNumeroFormattedt java.lang.Stringppppppppppsq ~³  wî   
        i      Dpq ~ q ~ %ppppppq ~ 7ppppq ~ :  wîppppppq ~Ïpppppppppppsq ~ dpsq ~ h  wîppppq ~ßq ~ßq ~Þpsq ~ k  wîppppq ~ßq ~ßpsq ~ i  wîppppq ~ßq ~ßpsq ~ n  wîppppq ~ßq ~ßpsq ~ p  wîppppq ~ßq ~ßppppppppppppppppp  wî        ppq ~Ãsq ~Å   
uq ~È   sq ~Êt banco.nossoNumeroFormattedt java.lang.Stringppppppppppsq ~³  wî   
       C   v   1pq ~ q ~ %ppppppq ~ 7ppppq ~ :  wîppppppq ~Ïpppppppppppsq ~ dpsq ~ h  wîppppq ~ëq ~ëq ~êpsq ~ k  wîppppq ~ëq ~ëpsq ~ i  wîppppq ~ëq ~ëpsq ~ n  wîppppq ~ëq ~ëpsq ~ p  wîppppq ~ëq ~ëppppppppppppppppp  wî        ppq ~Ãsq ~Å   uq ~È   sq ~Êt boleto.cedentet java.lang.Stringppppppppppsq ~³  wî   
        {  À   Ypq ~ q ~ %ppppppq ~ 7ppppq ~ :  wîppppppq ~Ïpq ~Ñpppppppppsq ~ dpsq ~ h  wîppppq ~÷q ~÷q ~öpsq ~ k  wîppppq ~÷q ~÷psq ~ i  wîppppq ~÷q ~÷psq ~ n  wîppppq ~÷q ~÷psq ~ p  wîppppq ~÷q ~÷ppppppppppppppppp  wî        ppq ~Ãsq ~Å   uq ~È   sq ~Êt boleto.valorBoletot java.lang.Stringppppppppppsq ~³  wî   
            ·   Ypq ~ q ~ %ppppppq ~ 7ppppq ~ :  wîppppppq ~Ïp~q ~Ðt CENTERpppppppppsq ~ dpsq ~ h  wîppppq ~q ~q ~psq ~ k  wîppppq ~q ~psq ~ i  wîppppq ~q ~psq ~ n  wîppppq ~q ~psq ~ p  wîppppq ~q ~ppppppppppppppppp  wî        ppq ~Ãsq ~Å   
uq ~È   sq ~Êt boleto.carteirat java.lang.Stringppppppppppsq ~³  wî   
        n  K   Ypq ~ q ~ %ppppppq ~ 7ppppq ~ :  wîppppppq ~Ïpq ~Ñpppppppppsq ~ dpsq ~ h  wîppppq ~q ~q ~psq ~ k  wîppppq ~q ~psq ~ i  wîppppq ~q ~psq ~ n  wîppppq ~q ~psq ~ p  wîppppq ~q ~ppppppppppppppppp  wî        ppq ~Ãsq ~Å   uq ~È   sq ~Êt boleto.valorBoletot java.lang.Stringppppppppppsq ~³  wî   
       C   v   §pq ~ q ~ %ppppppq ~ 7ppppq ~ :  wîppppppq ~Ïpppppppppppsq ~ dpsq ~ h  wîppppq ~q ~q ~psq ~ k  wîppppq ~q ~psq ~ i  wîppppq ~q ~psq ~ n  wîppppq ~q ~psq ~ p  wîppppq ~q ~ppppppppppppppppp  wî        ppq ~Ãsq ~Å   uq ~È   
sq ~Êt ((sq ~Êt boleto.responsavelsq ~Êt  != null &&  !sq ~Êt boleto.responsavelsq ~Êt .isEmpty()) ? sq ~Êt boleto.responsavelsq ~Êt  :  sq ~Êt boleto.nomeSacadosq ~Êt ) + " CPF/CNPJ: " + sq ~Êt boleto.cpfSacadot java.lang.Stringppppppppppsq ~³  wî   
       C   v   °pq ~ q ~ %ppppppq ~ 7ppppq ~ :  wîppppppq ~Ïpppppppppppsq ~ dpsq ~ h  wîppppq ~;q ~;q ~:psq ~ k  wîppppq ~;q ~;psq ~ i  wîppppq ~;q ~;psq ~ n  wîppppq ~;q ~;psq ~ p  wîppppq ~;q ~;ppppppppppppppppp  wî        ppq ~Ãsq ~Å   uq ~È   sq ~Êt boleto.enderecoSacadot java.lang.Stringppppppppppsq ~³  wî   
        $   ú   Epq ~ q ~ %ppppppq ~ 7ppppq ~ :  wîppppppq ~Ïpq ~pppppppppsq ~ dpsq ~ h  wîppppq ~Gq ~Gq ~Fpsq ~ k  wîppppq ~Gq ~Gpsq ~ i  wîppppq ~Gq ~Gpsq ~ n  wîppppq ~Gq ~Gpsq ~ p  wîppppq ~Gq ~Gppppppppppppppppp  wî        ppq ~Ãsq ~Å   uq ~È   sq ~Êt boleto.especieDocumentot java.lang.Stringppppppppppsq ~ V  wî           {  À   Épq ~ q ~ %ppppppq ~ 7ppppq ~ :  wîppppppq ~Ïppq ~¸ppppppppsq ~ dpsq ~ h  wîppppq ~Sq ~Sq ~Rpsq ~ k  wîppppq ~Sq ~Spsq ~ i  wîppppq ~Sq ~Spsq ~ n  wîppppq ~Sq ~Spsq ~ p  wîppppq ~Sq ~Spppppppppppppppppt "AutenticaÃ§Ã£o MecÃ¢nica-Ficha de sq ~³  wî   
        i      Xpq ~ q ~ %ppppppq ~ 7ppppq ~ :  wîppppppq ~Ïpppppppppppsq ~ dpsq ~ h  wîppppq ~[q ~[q ~Zpsq ~ k  wîppppq ~[q ~[psq ~ i  wîppppq ~[q ~[psq ~ n  wîppppq ~[q ~[psq ~ p  wîppppq ~[q ~[ppppppppppppppppp  wî        ppq ~Ãsq ~Å   uq ~È   sq ~Êt boleto.valorBoletot java.lang.Stringppppppppppsq ~³  wî           i      âpq ~ q ~ %ppppppq ~ 7ppppq ~ :  wîppppppq ~Ïpppppppppppsq ~ dpsq ~ h  wîppppq ~gq ~gq ~fpsq ~ k  wîppppq ~gq ~gpsq ~ i  wîppppq ~gq ~gpsq ~ n  wîppppq ~gq ~gpsq ~ p  wîppppq ~gq ~gppppppppppppppppp  wî        ppq ~Ãsq ~Å   uq ~È   sq ~Êt (sq ~Êt boleto.responsavelsq ~Êt  != null &&  !sq ~Êt boleto.responsavelsq ~Êt .isEmpty()) ? sq ~Êt boleto.responsavelsq ~Êt  :  sq ~Êt boleto.nomeSacadot java.lang.Stringppppppppppsq ~³  wî   
        {  À   pq ~ q ~ %ppppppq ~ 7ppppq ~ :  wîppppppq ~Ïpq ~Ñpppppppppsq ~ dpsq ~ h  wîppppq ~q ~q ~psq ~ k  wîppppq ~q ~psq ~ i  wîppppq ~q ~psq ~ n  wîppppq ~q ~psq ~ p  wîppppq ~q ~ppppppppppppppppp  wî        ppq ~Ãsq ~Å   uq ~È   sq ~Êt boleto.dataVencimentot java.lang.Stringppppppppppsq ~³  wî   
        i      pq ~ q ~ %ppppppq ~ 7ppppq ~ :  wîppppppq ~Ïpppppppppppsq ~ dpsq ~ h  wîppppq ~q ~q ~psq ~ k  wîppppq ~q ~psq ~ i  wîppppq ~q ~psq ~ n  wîppppq ~q ~psq ~ p  wîppppq ~q ~ppppppppppppppppp  wî        ppq ~Ãsq ~Å   uq ~È   sq ~Êt boleto.dataVencimentot java.lang.Stringppppppppppsq ~ '  wî          Ì   t   pq ~ q ~ %ppppppq ~ 7ppppq ~ :  wîppsq ~ <  wîppppq ~p  wî q ~ Esq ~³  wî   
        $   Û   pq ~ q ~ %ppppppq ~ 7ppppq ~ :  wîppppppppppppppppppsq ~ dpsq ~ h  wîppppq ~q ~q ~psq ~ k  wîppppq ~q ~psq ~ i  wîppppq ~q ~psq ~ n  wîppppq ~q ~psq ~ p  wîppppq ~q ~ppppppppppppppppp  wî        ppq ~Ãsq ~Å   uq ~È   sq ~Êt banco.numeroFormattedt java.lang.Stringppppppppppsq ~³  wî           1   ;   pq ~ q ~ %ppppppq ~ 7ppppq ~ :  wîppppppppppppppppppsq ~ dpsq ~ h  wîppppq ~§q ~§q ~¦psq ~ k  wîppppq ~§q ~§psq ~ i  wîppppq ~§q ~§psq ~ n  wîppppq ~§q ~§psq ~ p  wîppppq ~§q ~§ppppppppppppppppp  wî        ppq ~Ãsq ~Å   uq ~È   sq ~Êt banco.numeroFormattedt java.lang.Stringppppppppppsq ~³  wî   0       C   v   mpq ~ q ~ %ppppppq ~ 7ppppq ~ :  wîppppppq ~Ïpppppppppppsq ~ dpsq ~ h  wîppppq ~³q ~³q ~²psq ~ k  wîppppq ~³q ~³psq ~ i  wîppppq ~³q ~³psq ~ n  wîppppq ~³q ~³psq ~ p  wîppppq ~³q ~³ppppppppppppppppp  wî        ppq ~Ãsq ~Å   uq ~È   sq ~Êt boleto.instrucao1t java.lang.Stringppppppppppsq ~³  wî           5      pq ~ q ~ %ppppppq ~ 7ppppq ~ :  wîppppppppppppppppppsq ~ dpsq ~ h  wîppppq ~¿q ~¿q ~¾psq ~ k  wîppppq ~¿q ~¿psq ~ i  wîppppq ~¿q ~¿psq ~ n  wîppppq ~¿q ~¿psq ~ p  wîppppq ~¿q ~¿ppppppppppppppppp  wî        ppq ~Ãsq ~Å   uq ~È   sq ~Êt banco.bancot java.lang.Stringppppppppppsr ,net.sf.jasperreports.engine.base.JRBaseImage      'Ø *I PSEUDO_SERIAL_VERSION_UIDI 
bookmarkLevelB evaluationTimeB hyperlinkTargetB 
hyperlinkTypeZ isLazyB onErrorTypeL anchorNameExpressionq ~ L borderq ~ L borderColorq ~ -L bottomBorderq ~ L bottomBorderColorq ~ -L 
bottomPaddingq ~ XL evaluationGroupq ~ 1L evaluationTimeValueq ~´L 
expressionq ~ L horizontalAlignmentq ~ L horizontalAlignmentValueq ~ YL hyperlinkAnchorExpressionq ~ L hyperlinkPageExpressionq ~ [ hyperlinkParametersq ~µL hyperlinkReferenceExpressionq ~ L hyperlinkTooltipExpressionq ~ L isUsingCacheq ~ ZL 
leftBorderq ~ L leftBorderColorq ~ -L leftPaddingq ~ XL lineBoxq ~ [L 
linkTargetq ~ L linkTypeq ~ L onErrorTypeValuet 2Lnet/sf/jasperreports/engine/type/OnErrorTypeEnum;L paddingq ~ XL rightBorderq ~ L rightBorderColorq ~ -L rightPaddingq ~ XL 
scaleImageq ~ L scaleImageValuet 1Lnet/sf/jasperreports/engine/type/ScaleImageEnum;L 	topBorderq ~ L topBorderColorq ~ -L 
topPaddingq ~ XL verticalAlignmentq ~ L verticalAlignmentValueq ~ ^xq ~ )  wî   #       ?   v   Ösr java.awt.Color¥3u F falphaI valueL cst Ljava/awt/color/ColorSpace;[ 	frgbvaluet [F[ fvalueq ~Ðxp    ÿÿÿÿpppq ~ q ~ %sq ~Î    ÿ   pppt 	barcode-1p~r )net.sf.jasperreports.engine.type.ModeEnum          xq ~ t OPAQUEppq ~ 7ppppq ~ :  wîp~r )net.sf.jasperreports.engine.type.FillEnum          xq ~ t SOLIDsq ~ <  wîpp~q ~<t SOLIDsq ~ A    q ~Íp  wî         pppppppq ~Ãsq ~Å   uq ~È   sq ~Êt <it.businesslogic.ireport.barcode.BcImage.getBarcodeImage(13,sq ~Êt banco.codigoBarrassq ~Êt ,false,false,null,0,0)t java.awt.Imagepp~q ~Ðt LEFTpppppppppsq ~ dpsq ~ h  wîsq ~Î    ÿ   ppppq ~Ûsq ~ A    q ~éq ~éq ~Ípsq ~ k  wîsq ~Î    ÿ   ppppq ~Ûsq ~ A    q ~éq ~épsq ~ i  wîppppq ~éq ~épsq ~ n  wîsq ~Î    ÿ   ppppq ~Ûsq ~ A    q ~éq ~épsq ~ p  wîsq ~Î    ÿ   ppppq ~Ûsq ~ A    q ~éq ~épp~r 0net.sf.jasperreports.engine.type.OnErrorTypeEnum          xq ~ t ERRORppppp~r /net.sf.jasperreports.engine.type.ScaleImageEnum          xq ~ t 
FILL_FRAMEpppp~q ~¿t TOPsq ~³  wî   
             ºpq ~ q ~ %ppppppq ~ 7ppppq ~ :  wîppppppq ~Ïpppppppppppsq ~ dpsq ~ h  wîppppq ~ q ~ q ~ÿpsq ~ k  wîppppq ~ q ~ psq ~ i  wîppppq ~ q ~ psq ~ n  wîppppq ~ q ~ psq ~ p  wîppppq ~ q ~ ppppppppppppppppp  wî        ppq ~Ãsq ~Å   uq ~È   sq ~Êt (sq ~Êt boleto.cidadeSacadosq ~Êt 
==null?"":sq ~Êt boleto.cidadeSacadosq ~Êt .trim() + "  " ) + ( sq ~Êt boleto.ufSacadosq ~Êt ==null? "" : ( sq ~Êt boleto.ufSacadosq ~Êt ))
+ " - " +(((sq ~Êt boleto.responsavelsq ~Êt  != null &&  !sq ~Êt boleto.responsavelsq ~Êt .isEmpty()) ? sq ~Êt boleto.nomeSacadosq ~Êt  : ""))t java.lang.Stringppppppppppsq ~³  wî   
        &   v   ºpq ~ q ~ %ppppppq ~ 7ppppq ~ :  wîppppppq ~Ïpppppppppppsq ~ dpsq ~ h  wîppppq ~(q ~(q ~'psq ~ k  wîppppq ~(q ~(psq ~ i  wîppppq ~(q ~(psq ~ n  wîppppq ~(q ~(psq ~ p  wîppppq ~(q ~(ppppppppppppppppp  wî        ppq ~Ãsq ~Å   uq ~È   
sq ~Êt boleto.cepSacadosq ~Êt .length() != 0 ? (sq ~Êt boleto.cepSacadosq ~Êt 
.length()<=5?sq ~Êt boleto.cepSacadosq ~Êt +"-000":sq ~Êt boleto.cepSacadosq ~Êt .substring(0,5)+"-"+sq ~Êt boleto.cepSacadosq ~Êt .substring(5)):""t java.lang.Stringppppppppppsq ~³  wî   
        >   v   Fpq ~ q ~ %ppppppq ~ 7ppppq ~ :  wîppppppq ~Ïpppppppppppsq ~ dpsq ~ h  wîppppq ~Fq ~Fq ~Epsq ~ k  wîppppq ~Fq ~Fpsq ~ i  wîppppq ~Fq ~Fpsq ~ n  wîppppq ~Fq ~Fpsq ~ p  wîppppq ~Fq ~Fppppppppppppppppp  wî        ppq ~Ãsq ~Å   uq ~È   sq ~Êt boleto.dataDocumentot java.lang.Stringppppppppppsq ~³  wî   
       C   v   pq ~ q ~ %ppppppq ~ 7ppppq ~ :  wîppppppq ~Ïpppppppppppsq ~ dpsq ~ h  wîppppq ~Rq ~Rq ~Qpsq ~ k  wîppppq ~Rq ~Rpsq ~ i  wîppppq ~Rq ~Rpsq ~ n  wîppppq ~Rq ~Rpsq ~ p  wîppppq ~Rq ~Rppppppppppppppppp  wî        ppq ~Ãsq ~Å   uq ~È   sq ~Êt boleto.localPagamentot java.lang.Stringppppppppppsq ~³  wî   
        i      0pq ~ q ~ %ppppppq ~ 7ppppq ~ :  wîppppppq ~Ïpppppppppppsq ~ dpsq ~ h  wîppppq ~^q ~^q ~]psq ~ k  wîppppq ~^q ~^psq ~ i  wîppppq ~^q ~^psq ~ n  wîppppq ~^q ~^psq ~ p  wîppppq ~^q ~^ppppppppppppppppp  wî        ppq ~Ãsq ~Å   uq ~È   sq ~Êt  banco.agenciaCodCedenteFormattedt java.lang.Stringppppppppppsq ~³  wî   
        {  À   0pq ~ q ~ %ppppppq ~ 7ppppq ~ :  wîppppppq ~Ïpq ~Ñpppppppppsq ~ dpsq ~ h  wîppppq ~jq ~jq ~ipsq ~ k  wîppppq ~jq ~jpsq ~ i  wîppppq ~jq ~jpsq ~ n  wîppppq ~jq ~jpsq ~ p  wîppppq ~jq ~jppppppppppppppppp  wî        ppq ~Ãsq ~Å    uq ~È   sq ~Êt  banco.agenciaCodCedenteFormattedt java.lang.Stringppppppppppsq ~ '  wî                 Opq ~ q ~ %ppppppq ~ 7ppppq ~ :  wîppsq ~ <  wîppppq ~up  wî q ~ Esq ~ V  wî              ¡   Ppq ~ q ~ %ppppppq ~ 7ppppq ~ :  wîppppppq ~ apppppppppppsq ~ dpsq ~ h  wîppppq ~xq ~xq ~wpsq ~ k  wîppppq ~xq ~xpsq ~ i  wîppppq ~xq ~xpsq ~ n  wîppppq ~xq ~xpsq ~ p  wîppppq ~xq ~xpppppppppppppppppt CIPsq ~ V  wî           ,  Þ   Ópq ~ q ~ %ppppppq ~ 7ppppq ~ :  wîppppppq ~Ïppq ~¸ppppppppsq ~ dpsq ~ h  wîppppq ~q ~q ~psq ~ k  wîppppq ~q ~psq ~ i  wîppppq ~q ~psq ~ n  wîppppq ~q ~psq ~ p  wîppppq ~q ~pppppppppppppppppt 
CompensaÃ§Ã£osq ~ V  wî              Û   Zpq ~ q ~ %ppppppq ~ 7ppppq ~ :  wîppppppq ~ apq ~pppppppppsq ~ dpsq ~ h  wîppppq ~q ~q ~psq ~ k  wîppppq ~q ~psq ~ i  wîppppq ~q ~psq ~ n  wîppppq ~q ~psq ~ p  wîppppq ~q ~pppppppppppppppppt R$sq ~³  wî   
        j  K   Dpq ~ q ~ %ppppppq ~ 7ppppq ~ :  wîppppppq ~Ïpq ~pppppppppsq ~ dpsq ~ h  wîppppq ~q ~q ~psq ~ k  wîppppq ~q ~psq ~ i  wîppppq ~q ~psq ~ n  wîppppq ~q ~psq ~ p  wîppppq ~q ~ppppppppppppppppp  wî        ppq ~Ãsq ~Å   !uq ~È   sq ~Êt boleto.dataProcessamentot java.lang.Stringppppppppppsq ~ V  wî           '  "   Epq ~ q ~ %ppppppq ~ 7ppppq ~ :  wîppppppq ~ apq ~pppppppppsq ~ dpsq ~ h  wîppppq ~q ~q ~psq ~ k  wîppppq ~q ~psq ~ i  wîppppq ~q ~psq ~ n  wîppppq ~q ~psq ~ p  wîppppq ~q ~pppppppppppppppppt Nsq ~Ê  wî           J   |   pq ~ q ~ %ppppppq ~ 7ppppq ~ :  wîppsq ~ <  wîppppq ~£p  wî         pppppppq ~Ãsq ~Å   "uq ~È   sq ~Êt 
SUBREPORT_DIRsq ~Êt   + "logos/logo-sicredi.png"t java.lang.Stringppppppppppppsq ~ dpsq ~ h  wîppppq ~¬q ~¬q ~£psq ~ k  wîppppq ~¬q ~¬psq ~ i  wîppppq ~¬q ~¬psq ~ n  wîppppq ~¬q ~¬psq ~ p  wîppppq ~¬q ~¬ppq ~øpppppq ~ûpppppxp  wî  ppq ~ pppt javapsr .net.sf.jasperreports.engine.base.JRBaseDataset      'Ø I PSEUDO_SERIAL_VERSION_UIDZ isMainB whenResourceMissingType[ fieldst &[Lnet/sf/jasperreports/engine/JRField;L filterExpressionq ~ [ groupst &[Lnet/sf/jasperreports/engine/JRGroup;L nameq ~ [ 
parameterst *[Lnet/sf/jasperreports/engine/JRParameter;L 
propertiesMapq ~ 2L queryt %Lnet/sf/jasperreports/engine/JRQuery;L resourceBundleq ~ L scriptletClassq ~ [ 
scriptletst *[Lnet/sf/jasperreports/engine/JRScriptlet;[ 
sortFieldst *[Lnet/sf/jasperreports/engine/JRSortField;[ 	variablest )[Lnet/sf/jasperreports/engine/JRVariable;L whenResourceMissingTypeValuet >Lnet/sf/jasperreports/engine/type/WhenResourceMissingTypeEnum;xp  wî ur &[Lnet.sf.jasperreports.engine.JRField;<ßÇN*òp  xp   sr ,net.sf.jasperreports.engine.base.JRBaseField      'Ø L descriptionq ~ L nameq ~ L 
propertiesMapq ~ 2L valueClassNameq ~ L valueClassRealNameq ~ xpt banco.linhaDigitavelt banco.linhaDigitavelsr +net.sf.jasperreports.engine.JRPropertiesMap      'Ø L baseq ~ 2L propertiesListq ~ L 
propertiesMapt Ljava/util/Map;xppppt java.lang.Stringpsq ~¿t boleto.dataVencimentot boleto.dataVencimentosq ~Ãpppt java.lang.Stringpsq ~¿t boleto.nossoNumerot boleto.nossoNumerosq ~Ãpppt java.lang.Stringpsq ~¿t boleto.valorBoletot boleto.valorBoletosq ~Ãpppt java.lang.Stringpsq ~¿t boleto.cedentet boleto.cedentesq ~Ãpppt java.lang.Stringpsq ~¿t boleto.especieDocumentot boleto.especieDocumentosq ~Ãpppt java.lang.Stringpsq ~¿t boleto.carteirat boleto.carteirasq ~Ãpppt java.lang.Stringpsq ~¿t boleto.nomeSacadot boleto.nomeSacadosq ~Ãpppt java.lang.Stringpsq ~¿t boleto.enderecoSacadot boleto.enderecoSacadosq ~Ãpppt java.lang.Stringpsq ~¿t banco.codigoBarrast banco.codigoBarrassq ~Ãpppt java.lang.Stringpsq ~¿t boleto.instrucao1t boleto.instrucao1sq ~Ãpppt java.lang.Stringpsq ~¿t banco.bancot banco.bancosq ~Ãpppt java.lang.Stringpsq ~¿pt bancosq ~Ãpppt java.lang.Objectpsq ~¿t banco.numeroFormattedt banco.numeroFormattedsq ~Ãpppt java.lang.Stringpsq ~¿t boleto.cidadeSacadot boleto.cidadeSacadosq ~Ãpppt java.lang.Stringpsq ~¿t boleto.cepSacadot boleto.cepSacadosq ~Ãpppt java.lang.Stringpsq ~¿t boleto.ufSacadot boleto.ufSacadosq ~Ãpppt java.lang.Stringpsq ~¿t boleto.cpfSacadot boleto.cpfSacadosq ~Ãpppt java.lang.Stringpsq ~¿t boleto.dataDocumentot boleto.dataDocumentosq ~Ãpppt java.lang.Stringpsq ~¿t boleto.localPagamentot boleto.localPagamentosq ~Ãpppt java.lang.Stringpsq ~¿t boleto.agenciat boleto.agenciasq ~Ãpppt java.lang.Stringpsq ~¿t boleto.dvAgenciat boleto.dvAgenciasq ~Ãpppt java.lang.Stringpsq ~¿t boleto.contaCorrentet boleto.contaCorrentesq ~Ãpppt java.lang.Stringpsq ~¿t boleto.dvContaCorrentet boleto.dvContaCorrentesq ~Ãpppt java.lang.Stringpsq ~¿pt boleto.numConveniosq ~Ãpppt java.lang.Stringpsq ~¿pt boleto.dvNossoNumerosq ~Ãpppt java.lang.Stringpsq ~¿pt banco.nossoNumeroFormattedsq ~Ãpppt java.lang.Stringpsq ~¿pt  banco.agenciaCodCedenteFormattedsq ~Ãpppt java.lang.Stringpsq ~¿pt boleto.dataProcessamentosq ~Ãpppt java.lang.Stringpsq ~¿t boleto.responsavelt boleto.responsavelsq ~Ãpppt java.lang.Stringpppt bradescour *[Lnet.sf.jasperreports.engine.JRParameter;" *Ã`!  xp   sr 0net.sf.jasperreports.engine.base.JRBaseParameter      'Ø 	Z isForPromptingZ isSystemDefinedL defaultValueExpressionq ~ L descriptionq ~ L nameq ~ L nestedTypeNameq ~ L 
propertiesMapq ~ 2L valueClassNameq ~ L valueClassRealNameq ~ xpppt REPORT_PARAMETERS_MAPpsq ~Ãpppt 
java.util.Mappsq ~Uppt 
JASPER_REPORTpsq ~Ãpppt (net.sf.jasperreports.engine.JasperReportpsq ~Uppt REPORT_CONNECTIONpsq ~Ãpppt java.sql.Connectionpsq ~Uppt REPORT_MAX_COUNTpsq ~Ãpppt java.lang.Integerpsq ~Uppt REPORT_DATA_SOURCEpsq ~Ãpppt (net.sf.jasperreports.engine.JRDataSourcepsq ~Uppt REPORT_SCRIPTLETpsq ~Ãpppt /net.sf.jasperreports.engine.JRAbstractScriptletpsq ~Uppt 
REPORT_LOCALEpsq ~Ãpppt java.util.Localepsq ~Uppt REPORT_RESOURCE_BUNDLEpsq ~Ãpppt java.util.ResourceBundlepsq ~Uppt REPORT_TIME_ZONEpsq ~Ãpppt java.util.TimeZonepsq ~Uppt REPORT_FORMAT_FACTORYpsq ~Ãpppt .net.sf.jasperreports.engine.util.FormatFactorypsq ~Uppt REPORT_CLASS_LOADERpsq ~Ãpppt java.lang.ClassLoaderpsq ~Uppt REPORT_URL_HANDLER_FACTORYpsq ~Ãpppt  java.net.URLStreamHandlerFactorypsq ~Uppt REPORT_FILE_RESOLVERpsq ~Ãpppt -net.sf.jasperreports.engine.util.FileResolverpsq ~Uppt REPORT_TEMPLATESpsq ~Ãpppt java.util.Collectionpsq ~Uppt REPORT_VIRTUALIZERpsq ~Ãpppt )net.sf.jasperreports.engine.JRVirtualizerpsq ~Uppt IS_IGNORE_PAGINATIONpsq ~Ãpppt java.lang.Booleanpsq ~U ppt 
SUBREPORT_DIRpsq ~Ãpppt java.lang.Stringpsq ~Ãpsq ~    w   t ireport.zoomt 	ireport.xt 	ireport.yxsr java.util.HashMapÚÁÃ`Ñ F 
loadFactorI 	thresholdxp?@     w      q ~t 1.948717100000002q ~t 0q ~t 0xpppppur )[Lnet.sf.jasperreports.engine.JRVariable;bæ|,·D  xp   sr /net.sf.jasperreports.engine.base.JRBaseVariable      'Ø I PSEUDO_SERIAL_VERSION_UIDB calculationB 
incrementTypeZ isSystemDefinedB 	resetTypeL calculationValuet 2Lnet/sf/jasperreports/engine/type/CalculationEnum;L 
expressionq ~ L incrementGroupq ~ 1L incrementTypeValuet 4Lnet/sf/jasperreports/engine/type/IncrementTypeEnum;L incrementerFactoryClassNameq ~ L incrementerFactoryClassRealNameq ~ L initialValueExpressionq ~ L nameq ~ L 
resetGroupq ~ 1L resetTypeValuet 0Lnet/sf/jasperreports/engine/type/ResetTypeEnum;L valueClassNameq ~ L valueClassRealNameq ~ xp  wî   ~r 0net.sf.jasperreports.engine.type.CalculationEnum          xq ~ t SYSTEMpp~r 2net.sf.jasperreports.engine.type.IncrementTypeEnum          xq ~ t NONEppsq ~Å    uq ~È   sq ~Êt new java.lang.Integer(1)q ~ept PAGE_NUMBERp~r .net.sf.jasperreports.engine.type.ResetTypeEnum          xq ~ t REPORTq ~epsq ~¦  wî   q ~¬ppq ~¯ppsq ~Å   uq ~È   sq ~Êt new java.lang.Integer(1)q ~ept 
COLUMN_NUMBERp~q ~¶t PAGEq ~epsq ~¦  wî   ~q ~«t COUNTsq ~Å   uq ~È   sq ~Êt new java.lang.Integer(1)q ~eppq ~¯ppsq ~Å   uq ~È   sq ~Êt new java.lang.Integer(0)q ~ept REPORT_COUNTpq ~·q ~epsq ~¦  wî   q ~Âsq ~Å   uq ~È   sq ~Êt new java.lang.Integer(1)q ~eppq ~¯ppsq ~Å   uq ~È   sq ~Êt new java.lang.Integer(0)q ~ept 
PAGE_COUNTpq ~¿q ~epsq ~¦  wî   q ~Âsq ~Å   uq ~È   sq ~Êt new java.lang.Integer(1)q ~eppq ~¯ppsq ~Å   uq ~È   sq ~Êt new java.lang.Integer(0)q ~ept COLUMN_COUNTp~q ~¶t COLUMNq ~ep~r <net.sf.jasperreports.engine.type.WhenResourceMissingTypeEnum          xq ~ t NULLq ~Rp~r 0net.sf.jasperreports.engine.type.OrientationEnum          xq ~ t PORTRAITpp~r /net.sf.jasperreports.engine.type.PrintOrderEnum          xq ~ t VERTICALpppp~r 3net.sf.jasperreports.engine.type.WhenNoDataTypeEnum          xq ~ t NO_PAGESsr 6net.sf.jasperreports.engine.design.JRReportCompileData      'Ø L crosstabCompileDataq ~ÄL datasetCompileDataq ~ÄL mainDatasetCompileDataq ~ xpsq ~?@     w       xsq ~?@     w       xur [B¬óøTà  xp  +ÈÊþº¾   .x bradesco_1565125668080_685818  ,net/sf/jasperreports/engine/fill/JREvaluator  parameter_REPORT_LOCALE 2Lnet/sf/jasperreports/engine/fill/JRFillParameter; parameter_JASPER_REPORT parameter_REPORT_VIRTUALIZER parameter_REPORT_TIME_ZONE parameter_REPORT_FILE_RESOLVER parameter_REPORT_SCRIPTLET parameter_REPORT_PARAMETERS_MAP parameter_REPORT_CONNECTION parameter_REPORT_CLASS_LOADER parameter_REPORT_DATA_SOURCE $parameter_REPORT_URL_HANDLER_FACTORY parameter_IS_IGNORE_PAGINATION parameter_SUBREPORT_DIR parameter_REPORT_FORMAT_FACTORY parameter_REPORT_MAX_COUNT parameter_REPORT_TEMPLATES  parameter_REPORT_RESOURCE_BUNDLE !field_banco46nossoNumeroFormatted .Lnet/sf/jasperreports/engine/fill/JRFillField; field_boleto46cedente field_boleto46enderecoSacado field_boleto46agencia field_boleto46dvNossoNumero field_boleto46dvAgencia 'field_banco46agenciaCodCedenteFormatted field_banco46codigoBarras field_boleto46nomeSacado field_banco46banco field_boleto46valorBoleto field_boleto46especieDocumento field_banco46numeroFormatted field_banco field_boleto46dataVencimento field_boleto46dataProcessamento field_boleto46numConvenio field_boleto46ufSacado field_boleto46cepSacado field_boleto46dvContaCorrente field_boleto46dataDocumento field_banco46linhaDigitavel field_boleto46nossoNumero field_boleto46contaCorrente field_boleto46responsavel field_boleto46cpfSacado field_boleto46localPagamento field_boleto46cidadeSacado field_boleto46carteira field_boleto46instrucao1 variable_PAGE_NUMBER 1Lnet/sf/jasperreports/engine/fill/JRFillVariable; variable_COLUMN_NUMBER variable_REPORT_COUNT variable_PAGE_COUNT variable_COLUMN_COUNT <init> ()V Code < =
  ?  	  A  	  C  	  E 	 	  G 
 	  I  	  K  	  M 
 	  O  	  Q  	  S  	  U  	  W  	  Y  	  [  	  ]  	  _  	  a  	  c  	  e  	  g  	  i  	  k  	  m  	  o  	  q   	  s ! 	  u " 	  w # 	  y $ 	  { % 	  } & 	   ' 	   ( 	   ) 	   * 	   + 	   , 	   - 	   . 	   / 	   0 	   1 	   2 	   3 	   4 	   5 	   6 7	   8 7	  ¡ 9 7	  £ : 7	  ¥ ; 7	  § LineNumberTable customizedInit 0(Ljava/util/Map;Ljava/util/Map;Ljava/util/Map;)V 
initParams (Ljava/util/Map;)V ¬ ­
  ® 
initFields ° ­
  ± initVars ³ ­
  ´ 
REPORT_LOCALE ¶ 
java/util/Map ¸ get &(Ljava/lang/Object;)Ljava/lang/Object; º » ¹ ¼ 0net/sf/jasperreports/engine/fill/JRFillParameter ¾ 
JASPER_REPORT À REPORT_VIRTUALIZER Â REPORT_TIME_ZONE Ä REPORT_FILE_RESOLVER Æ REPORT_SCRIPTLET È REPORT_PARAMETERS_MAP Ê REPORT_CONNECTION Ì REPORT_CLASS_LOADER Î REPORT_DATA_SOURCE Ð REPORT_URL_HANDLER_FACTORY Ò IS_IGNORE_PAGINATION Ô 
SUBREPORT_DIR Ö REPORT_FORMAT_FACTORY Ø REPORT_MAX_COUNT Ú REPORT_TEMPLATES Ü REPORT_RESOURCE_BUNDLE Þ banco.nossoNumeroFormatted à ,net/sf/jasperreports/engine/fill/JRFillField â boleto.cedente ä boleto.enderecoSacado æ boleto.agencia è boleto.dvNossoNumero ê boleto.dvAgencia ì  banco.agenciaCodCedenteFormatted î banco.codigoBarras ð boleto.nomeSacado ò banco.banco ô boleto.valorBoleto ö boleto.especieDocumento ø banco.numeroFormatted ú banco ü boleto.dataVencimento þ boleto.dataProcessamento  boleto.numConvenio boleto.ufSacado boleto.cepSacado boleto.dvContaCorrente boleto.dataDocumento
 banco.linhaDigitavel boleto.nossoNumero boleto.contaCorrente boleto.responsavel boleto.cpfSacado boleto.localPagamento boleto.cidadeSacado boleto.carteira boleto.instrucao1 PAGE_NUMBER /net/sf/jasperreports/engine/fill/JRFillVariable  
COLUMN_NUMBER" REPORT_COUNT$ 
PAGE_COUNT& COLUMN_COUNT( evaluate (I)Ljava/lang/Object; 
Exceptions java/lang/Throwable- java/lang/Integer/ (I)V <1
02 getValue ()Ljava/lang/Object;45
 ã6 java/lang/String8 java/lang/StringBuffer: isEmpty ()Z<=
9> valueOf &(Ljava/lang/Object;)Ljava/lang/String;@A
9B (Ljava/lang/String;)V <D
;E  CPF/CNPJ: G append ,(Ljava/lang/String;)Ljava/lang/StringBuffer;IJ
;K toString ()Ljava/lang/String;MN
;O (it/businesslogic/ireport/barcode/BcImageQ getBarcodeImage I(ILjava/lang/Object;ZZLjava/lang/String;II)Ljava/awt/image/BufferedImage;ST
RU  W trimYN
9Z   \  - ^ length ()I`a
9b -000d 	substring (II)Ljava/lang/String;fg
9h -j (I)Ljava/lang/String;fl
9m
 ¿6 logos/logo-sicredi.pngp evaluateOld getOldValues5
 ãt evaluateEstimated 
SourceFile !     4                 	     
               
                                                                                                !     "     #     $     %     &     '     (     )     *     +     ,     -     .     /     0     1     2     3     4     5     6 7    8 7    9 7    : 7    ; 7     < =  >  õ    	*· @*µ B*µ D*µ F*µ H*µ J*µ L*µ N*µ P*µ R*µ T*µ V*µ X*µ Z*µ \*µ ^*µ `*µ b*µ d*µ f*µ h*µ j*µ l*µ n*µ p*µ r*µ t*µ v*µ x*µ z*µ |*µ ~*µ *µ *µ *µ *µ *µ *µ *µ *µ *µ *µ *µ *µ *µ *µ *µ *µ  *µ ¢*µ ¤*µ ¦*µ ¨±    ©   Ú 6      	          "  '   , ! 1 " 6 # ; $ @ % E & J ' O ( T ) Y * ^ + c , h - m . r / w 0 | 1  2  3  4  5  6  7  8 ¤ 9 © : ® ; ³ < ¸ = ½ > Â ? Ç @ Ì A Ñ B Ö C Û D à E å F ê G ï H ô I ù J þ K L   ª «  >   4     *+· ¯*,· ²*-· µ±    ©       X  Y 
 Z  [  ¬ ­  >      3*+·¹ ½ À ¿À ¿µ B*+Á¹ ½ À ¿À ¿µ D*+Ã¹ ½ À ¿À ¿µ F*+Å¹ ½ À ¿À ¿µ H*+Ç¹ ½ À ¿À ¿µ J*+É¹ ½ À ¿À ¿µ L*+Ë¹ ½ À ¿À ¿µ N*+Í¹ ½ À ¿À ¿µ P*+Ï¹ ½ À ¿À ¿µ R*+Ñ¹ ½ À ¿À ¿µ T*+Ó¹ ½ À ¿À ¿µ V*+Õ¹ ½ À ¿À ¿µ X*+×¹ ½ À ¿À ¿µ Z*+Ù¹ ½ À ¿À ¿µ \*+Û¹ ½ À ¿À ¿µ ^*+Ý¹ ½ À ¿À ¿µ `*+ß¹ ½ À ¿À ¿µ b±    ©   J    c  d $ e 6 f H g Z h l i ~ j  k ¢ l ´ m Æ n Ø o ê p ü q r  s2 t  ° ­  >  ¼    ,*+á¹ ½ À ãÀ ãµ d*+å¹ ½ À ãÀ ãµ f*+ç¹ ½ À ãÀ ãµ h*+é¹ ½ À ãÀ ãµ j*+ë¹ ½ À ãÀ ãµ l*+í¹ ½ À ãÀ ãµ n*+ï¹ ½ À ãÀ ãµ p*+ñ¹ ½ À ãÀ ãµ r*+ó¹ ½ À ãÀ ãµ t*+õ¹ ½ À ãÀ ãµ v*+÷¹ ½ À ãÀ ãµ x*+ù¹ ½ À ãÀ ãµ z*+û¹ ½ À ãÀ ãµ |*+ý¹ ½ À ãÀ ãµ ~*+ÿ¹ ½ À ãÀ ãµ *+¹ ½ À ãÀ ãµ *+¹ ½ À ãÀ ãµ *+¹ ½ À ãÀ ãµ *+¹ ½ À ãÀ ãµ *+	¹ ½ À ãÀ ãµ *+¹ ½ À ãÀ ãµ *+
¹ ½ À ãÀ ãµ *+¹ ½ À ãÀ ãµ *+¹ ½ À ãÀ ãµ *+¹ ½ À ãÀ ãµ *+¹ ½ À ãÀ ãµ *+¹ ½ À ãÀ ãµ *+¹ ½ À ãÀ ãµ *+¹ ½ À ãÀ ãµ *+¹ ½ À ãÀ ãµ ±    ©   ~    |  } $ ~ 6  H  Z  l  ~    ¢  ´  Æ  Ø  ê  ü  ! 4 G Z m   ¦ ¹ Ì ß ò   +   ³ ­  >        `*+¹ ½ À!À!µ  *+#¹ ½ À!À!µ ¢*+%¹ ½ À!À!µ ¤*+'¹ ½ À!À!µ ¦*+)¹ ½ À!À!µ ¨±    ©       ¢  £ & ¤ 9 ¥ L ¦ _ § *+ ,    . >  >    Mª  ý       "      ¥   ±   ½   É   Õ   á   í   ù      #  1  ?  M  [  ³  Á  Ï  Ý    #  1  ?  M  [  i        §  µ  Ã  Ñ  ß»0Y·3M§[»0Y·3M§O»0Y·3M§C»0Y·3M§7»0Y·3M§+»0Y·3M§»0Y·3M§»0Y·3M§*´ ¶7À9M§ù*´ d¶7À9M§ë*´ d¶7À9M§Ý*´ f¶7À9M§Ï*´ x¶7À9M§Á*´ ¶7À9M§³*´ x¶7À9M§¥»;Y*´ ¶7À9Æ  *´ ¶7À9¶? *´ ¶7À9§ 
*´ t¶7À9¸C·FH¶L*´ ¶7À9¶L¶PM§M*´ h¶7À9M§?*´ z¶7À9M§1*´ x¶7À9M§#*´ ¶7À9Æ  *´ ¶7À9¶? *´ ¶7À9§ 
*´ t¶7À9M§ë*´ ¶7À9M§Ý*´ ¶7À9M§Ï*´ |¶7À9M§Á*´ |¶7À9M§³*´ ¶7À9M§¥*´ v¶7À9M§
*´ r¶7À9¸VM§»;Y*´ ¶7À9Ç 	X§ #»;Y*´ ¶7À9¶[¸C·F]¶L¶P¸C·F*´ ¶7À9Ç 	X§ 
*´ ¶7À9¶L_¶L*´ ¶7À9Æ  *´ ¶7À9¶? *´ t¶7À9§ X¶L¶PM§ å*´ ¶7À9¶c j*´ ¶7À9¶c£ #»;Y*´ ¶7À9¸C·Fe¶L¶P§ <»;Y*´ ¶7À9¶i¸C·Fk¶L*´ ¶7À9¶n¶L¶P§ XM§ g*´ ¶7À9M§ Y*´ ¶7À9M§ K*´ p¶7À9M§ =*´ p¶7À9M§ /*´ ¶7À9M§ !»;Y*´ Z¶oÀ9¸C·Fq¶L¶PM,°    ©  * J   ¯  ±  µ ¥ ¶ ¨ º ± » ´ ¿ ½ À À Ä É Å Ì É Õ Ê Ø Î á Ï ä Ó í Ô ð Ø ù Ù ü Ý Þ
 â ã ç# è& ì1 í4 ñ? òB öM ÷P û[ ü^ ³¶ÁÄ
ÏÒÝà#&14#?$B(M)P-[.^2i3l78<á=<>BCG§HªLµM¸QÃRÆVÑWÔ[ß\â` h r+ ,    . >  >    Mª  ý       "      ¥   ±   ½   É   Õ   á   í   ù      #  1  ?  M  [  ³  Á  Ï  Ý    #  1  ?  M  [  i        §  µ  Ã  Ñ  ß»0Y·3M§[»0Y·3M§O»0Y·3M§C»0Y·3M§7»0Y·3M§+»0Y·3M§»0Y·3M§»0Y·3M§*´ ¶uÀ9M§ù*´ d¶uÀ9M§ë*´ d¶uÀ9M§Ý*´ f¶uÀ9M§Ï*´ x¶uÀ9M§Á*´ ¶uÀ9M§³*´ x¶uÀ9M§¥»;Y*´ ¶uÀ9Æ  *´ ¶uÀ9¶? *´ ¶uÀ9§ 
*´ t¶uÀ9¸C·FH¶L*´ ¶uÀ9¶L¶PM§M*´ h¶uÀ9M§?*´ z¶uÀ9M§1*´ x¶uÀ9M§#*´ ¶uÀ9Æ  *´ ¶uÀ9¶? *´ ¶uÀ9§ 
*´ t¶uÀ9M§ë*´ ¶uÀ9M§Ý*´ ¶uÀ9M§Ï*´ |¶uÀ9M§Á*´ |¶uÀ9M§³*´ ¶uÀ9M§¥*´ v¶uÀ9M§
*´ r¶uÀ9¸VM§»;Y*´ ¶uÀ9Ç 	X§ #»;Y*´ ¶uÀ9¶[¸C·F]¶L¶P¸C·F*´ ¶uÀ9Ç 	X§ 
*´ ¶uÀ9¶L_¶L*´ ¶uÀ9Æ  *´ ¶uÀ9¶? *´ t¶uÀ9§ X¶L¶PM§ å*´ ¶uÀ9¶c j*´ ¶uÀ9¶c£ #»;Y*´ ¶uÀ9¸C·Fe¶L¶P§ <»;Y*´ ¶uÀ9¶i¸C·Fk¶L*´ ¶uÀ9¶n¶L¶P§ XM§ g*´ ¶uÀ9M§ Y*´ ¶uÀ9M§ K*´ p¶uÀ9M§ =*´ p¶uÀ9M§ /*´ ¶uÀ9M§ !»;Y*´ Z¶oÀ9¸C·Fq¶L¶PM,°    ©  * J  q s w ¥x ¨| ±} ´ ½ À É Ì Õ Ø á ä í ð ù ü 
¤¥©#ª&®1¯4³?´B¸M¹P½[¾^Â³Ã¶ÇÁÈÄÌÏÍÒÑÝÒàÖ×Û#Ü&à1á4å?æBêMëPï[ð^ôiõlùúþáÿþ 	§
ªµ¸ÃÆÑÔßâ" * v+ ,    . >  >    Mª  ý       "      ¥   ±   ½   É   Õ   á   í   ù      #  1  ?  M  [  ³  Á  Ï  Ý    #  1  ?  M  [  i        §  µ  Ã  Ñ  ß»0Y·3M§[»0Y·3M§O»0Y·3M§C»0Y·3M§7»0Y·3M§+»0Y·3M§»0Y·3M§»0Y·3M§*´ ¶7À9M§ù*´ d¶7À9M§ë*´ d¶7À9M§Ý*´ f¶7À9M§Ï*´ x¶7À9M§Á*´ ¶7À9M§³*´ x¶7À9M§¥»;Y*´ ¶7À9Æ  *´ ¶7À9¶? *´ ¶7À9§ 
*´ t¶7À9¸C·FH¶L*´ ¶7À9¶L¶PM§M*´ h¶7À9M§?*´ z¶7À9M§1*´ x¶7À9M§#*´ ¶7À9Æ  *´ ¶7À9¶? *´ ¶7À9§ 
*´ t¶7À9M§ë*´ ¶7À9M§Ý*´ ¶7À9M§Ï*´ |¶7À9M§Á*´ |¶7À9M§³*´ ¶7À9M§¥*´ v¶7À9M§
*´ r¶7À9¸VM§»;Y*´ ¶7À9Ç 	X§ #»;Y*´ ¶7À9¶[¸C·F]¶L¶P¸C·F*´ ¶7À9Ç 	X§ 
*´ ¶7À9¶L_¶L*´ ¶7À9Æ  *´ ¶7À9¶? *´ t¶7À9§ X¶L¶PM§ å*´ ¶7À9¶c j*´ ¶7À9¶c£ #»;Y*´ ¶7À9¸C·Fe¶L¶P§ <»;Y*´ ¶7À9¶i¸C·Fk¶L*´ ¶7À9¶n¶L¶P§ XM§ g*´ ¶7À9M§ Y*´ ¶7À9M§ K*´ p¶7À9M§ =*´ p¶7À9M§ /*´ ¶7À9M§ !»;Y*´ Z¶oÀ9¸C·Fq¶L¶PM,°    ©  * J  3 5 9 ¥: ¨> ±? ´C ½D ÀH ÉI ÌM ÕN ØR áS äW íX ð\ ù] üab
fgk#l&p1q4u?vBzM{P[^³¶ÁÄÏÒÝà#&¢1£4§?¨B¬M­P±[²^¶i·l»¼ÀáÁÀÂÆÇË§ÌªÐµÑ¸ÕÃÖÆÚÑÛÔßßàâä ì w    t _1565125668080_685818t 2net.sf.jasperreports.engine.design.JRJavacCompiler