<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="Descontos" pageWidth="195" pageHeight="535" orientation="Landscape" columnWidth="195" leftMargin="0" rightMargin="0" topMargin="0" bottomMargin="0">
	<property name="ireport.scriptlethandling" value="0"/>
	<property name="ireport.encoding" value="UTF-8"/>
	<property name="ireport.zoom" value="2.0"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="0"/>
	<import value="net.sf.jasperreports.engine.*"/>
	<import value="java.util.*"/>
	<import value="net.sf.jasperreports.engine.data.*"/>
	<parameter name="moeda" class="java.lang.String">
		<defaultValueExpression><![CDATA["R$"]]></defaultValueExpression>
	</parameter>
	<field name="codigoContrato" class="java.lang.Integer"/>
	<field name="descricaoDesconto" class="java.lang.String"/>
	<field name="valorDesconto" class="java.lang.Double"/>
	<field name="porcentagemDesconto" class="java.lang.Double"/>
	<detail>
		<band height="12" splitType="Stretch">
			<printWhenExpression><![CDATA[(!$F{valorDesconto}.equals(0.0) && $F{porcentagemDesconto}.equals(0.0))]]></printWhenExpression>
			<textField>
				<reportElement x="1" y="1" width="30" height="10"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Microsoft Sans Serif" size="7"/>
				</textElement>
				<textFieldExpression class="java.lang.Integer"><![CDATA[$F{codigoContrato}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="30" y="1" width="100" height="10"/>
				<textElement verticalAlignment="Middle">
					<font size="7"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{descricaoDesconto}]]></textFieldExpression>
			</textField>
			<textField pattern="#,##0.00">
				<reportElement x="155" y="1" width="40" height="10"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Microsoft Sans Serif" size="7"/>
				</textElement>
				<textFieldExpression class="java.lang.Double"><![CDATA[( $F{valorDesconto}.equals(0.0) ?
    $F{porcentagemDesconto} :
    $F{valorDesconto} )]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement mode="Transparent" x="130" y="1" width="25" height="10" forecolor="#000000" backcolor="#FFFFFF"/>
				<textElement textAlignment="Right" verticalAlignment="Top" rotation="None" lineSpacing="Single" markup="none">
					<font fontName="SansSerif" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{moeda}]]></textFieldExpression>
			</textField>
		</band>
		<band height="12" splitType="Stretch">
			<printWhenExpression><![CDATA[($F{valorDesconto}.equals(0.0) && !$F{porcentagemDesconto}.equals(0.0))]]></printWhenExpression>
			<staticText>
				<reportElement x="177" y="1" width="15" height="10"/>
				<textElement textAlignment="Center">
					<font size="7"/>
				</textElement>
				<text><![CDATA[%]]></text>
			</staticText>
			<textField>
				<reportElement x="1" y="1" width="29" height="10"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Microsoft Sans Serif" size="7"/>
				</textElement>
				<textFieldExpression class="java.lang.Integer"><![CDATA[$F{codigoContrato}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="29" y="1" width="100" height="10"/>
				<textElement verticalAlignment="Middle">
					<font size="7"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{descricaoDesconto}]]></textFieldExpression>
			</textField>
			<textField pattern="#,##0.00">
				<reportElement x="128" y="1" width="50" height="10"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Microsoft Sans Serif" size="7"/>
				</textElement>
				<textFieldExpression class="java.lang.Double"><![CDATA[( $F{valorDesconto}.equals(0.0) ?
    $F{porcentagemDesconto} :
    $F{valorDesconto} )]]></textFieldExpression>
			</textField>
		</band>
	</detail>
</jasperReport>
