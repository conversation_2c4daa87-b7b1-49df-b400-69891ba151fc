¬í sr (net.sf.jasperreports.engine.JasperReport      'Ø L compileDatat Ljava/io/Serializable;L compileNameSuffixt Ljava/lang/String;L 
compilerClassq ~ xr -net.sf.jasperreports.engine.base.JRBaseReport      'Ø +I PSEUDO_SERIAL_VERSION_UIDI bottomMarginI columnCountI 
columnSpacingI columnWidthZ ignorePaginationZ isFloatColumnFooterZ isSummaryNewPageZ  isSummaryWithPageHeaderAndFooterZ isTitleNewPageI 
leftMarginB orientationI 
pageHeightI 	pageWidthB 
printOrderI rightMarginI 	topMarginB whenNoDataTypeL 
backgroundt $Lnet/sf/jasperreports/engine/JRBand;L columnFooterq ~ L columnHeaderq ~ [ datasetst ([Lnet/sf/jasperreports/engine/JRDataset;L defaultFontt *Lnet/sf/jasperreports/engine/JRReportFont;L defaultStylet %Lnet/sf/jasperreports/engine/JRStyle;L detailq ~ L 
detailSectiont 'Lnet/sf/jasperreports/engine/JRSection;[ fontst +[Lnet/sf/jasperreports/engine/JRReportFont;L formatFactoryClassq ~ L 
importsSett Ljava/util/Set;L languageq ~ L lastPageFooterq ~ L mainDatasett 'Lnet/sf/jasperreports/engine/JRDataset;L nameq ~ L noDataq ~ L orientationValuet 2Lnet/sf/jasperreports/engine/type/OrientationEnum;L 
pageFooterq ~ L 
pageHeaderq ~ L printOrderValuet 1Lnet/sf/jasperreports/engine/type/PrintOrderEnum;[ stylest &[Lnet/sf/jasperreports/engine/JRStyle;L summaryq ~ [ 	templatest /[Lnet/sf/jasperreports/engine/JRReportTemplate;L titleq ~ L whenNoDataTypeValuet 5Lnet/sf/jasperreports/engine/type/WhenNoDataTypeEnum;xp  wî              Ã            "   Ã          psr +net.sf.jasperreports.engine.base.JRBaseBand      'Ø I PSEUDO_SERIAL_VERSION_UIDI heightZ isSplitAllowedL printWhenExpressiont *Lnet/sf/jasperreports/engine/JRExpression;L 	splitTypet Ljava/lang/Byte;L splitTypeValuet 0Lnet/sf/jasperreports/engine/type/SplitTypeEnum;xr 3net.sf.jasperreports.engine.base.JRBaseElementGroup      'Ø L childrent Ljava/util/List;L elementGroupt ,Lnet/sf/jasperreports/engine/JRElementGroup;xpsr java.util.ArrayListxÒÇa I sizexp    w    xp  wî    pp~r .net.sf.jasperreports.engine.type.SplitTypeEnum          xr java.lang.Enum          xpt STRETCHsq ~ sq ~     w    xp  wî    ppq ~ ppppsr .net.sf.jasperreports.engine.base.JRBaseSection      'Ø [ bandst %[Lnet/sf/jasperreports/engine/JRBand;xpur %[Lnet.sf.jasperreports.engine.JRBand;Ý~ìÊ5  xp   sq ~ sq ~    
w   
sr 0net.sf.jasperreports.engine.base.JRBaseTextField      'Ø I PSEUDO_SERIAL_VERSION_UIDI 
bookmarkLevelB evaluationTimeB hyperlinkTargetB 
hyperlinkTypeZ isStretchWithOverflowL anchorNameExpressionq ~ L evaluationGroupt %Lnet/sf/jasperreports/engine/JRGroup;L evaluationTimeValuet 5Lnet/sf/jasperreports/engine/type/EvaluationTimeEnum;L 
expressionq ~ L hyperlinkAnchorExpressionq ~ L hyperlinkPageExpressionq ~ [ hyperlinkParameterst 3[Lnet/sf/jasperreports/engine/JRHyperlinkParameter;L hyperlinkReferenceExpressionq ~ L hyperlinkTooltipExpressionq ~ L isBlankWhenNullt Ljava/lang/Boolean;L 
linkTargetq ~ L linkTypeq ~ L patternq ~ xr 2net.sf.jasperreports.engine.base.JRBaseTextElement      'Ø %I PSEUDO_SERIAL_VERSION_UIDL borderq ~ L borderColort Ljava/awt/Color;L bottomBorderq ~ L bottomBorderColorq ~ /L 
bottomPaddingt Ljava/lang/Integer;L fontNameq ~ L fontSizeq ~ 0L horizontalAlignmentq ~ L horizontalAlignmentValuet 6Lnet/sf/jasperreports/engine/type/HorizontalAlignEnum;L isBoldq ~ -L isItalicq ~ -L 
isPdfEmbeddedq ~ -L isStrikeThroughq ~ -L isStyledTextq ~ -L isUnderlineq ~ -L 
leftBorderq ~ L leftBorderColorq ~ /L leftPaddingq ~ 0L lineBoxt 'Lnet/sf/jasperreports/engine/JRLineBox;L lineSpacingq ~ L lineSpacingValuet 2Lnet/sf/jasperreports/engine/type/LineSpacingEnum;L markupq ~ L paddingq ~ 0L pdfEncodingq ~ L pdfFontNameq ~ L 
reportFontq ~ L rightBorderq ~ L rightBorderColorq ~ /L rightPaddingq ~ 0L rotationq ~ L 
rotationValuet /Lnet/sf/jasperreports/engine/type/RotationEnum;L 	topBorderq ~ L topBorderColorq ~ /L 
topPaddingq ~ 0L verticalAlignmentq ~ L verticalAlignmentValuet 4Lnet/sf/jasperreports/engine/type/VerticalAlignEnum;xr .net.sf.jasperreports.engine.base.JRBaseElement      'Ø I PSEUDO_SERIAL_VERSION_UIDI heightZ isPrintInFirstWholeBandZ isPrintRepeatedValuesZ isPrintWhenDetailOverflowsZ isRemoveLineWhenBlankB positionTypeB stretchTypeI widthI xI yL 	backcolorq ~ /L defaultStyleProvidert 4Lnet/sf/jasperreports/engine/JRDefaultStyleProvider;L elementGroupq ~ L 	forecolorq ~ /L keyq ~ L modeq ~ L 	modeValuet +Lnet/sf/jasperreports/engine/type/ModeEnum;L parentStyleq ~ L parentStyleNameReferenceq ~ L positionTypeValuet 3Lnet/sf/jasperreports/engine/type/PositionTypeEnum;L printWhenExpressionq ~ L printWhenGroupChangesq ~ *L 
propertiesMapt -Lnet/sf/jasperreports/engine/JRPropertiesMap;[ propertyExpressionst 3[Lnet/sf/jasperreports/engine/JRPropertyExpression;L stretchTypeValuet 2Lnet/sf/jasperreports/engine/type/StretchTypeEnum;xp  wî   
        (      pq ~ q ~ 'pt textField-1p~r )net.sf.jasperreports.engine.type.ModeEnum          xq ~ t OPAQUEpp~r 1net.sf.jasperreports.engine.type.PositionTypeEnum          xq ~ t FIX_RELATIVE_TO_TOPpppp~r 0net.sf.jasperreports.engine.type.StretchTypeEnum          xq ~ t 
NO_STRETCH  wîpppppt Microsoft Sans Serifsr java.lang.Integerâ ¤÷8 I valuexr java.lang.Number¬à  xp   p~r 4net.sf.jasperreports.engine.type.HorizontalAlignEnum          xq ~ t RIGHTsr java.lang.BooleanÍ rÕúî Z valuexpppppppppsr .net.sf.jasperreports.engine.base.JRBaseLineBox      'Ø L 
bottomPaddingq ~ 0L 	bottomPent +Lnet/sf/jasperreports/engine/base/JRBoxPen;L boxContainert ,Lnet/sf/jasperreports/engine/JRBoxContainer;L leftPaddingq ~ 0L leftPenq ~ RL paddingq ~ 0L penq ~ RL rightPaddingq ~ 0L rightPenq ~ RL 
topPaddingq ~ 0L topPenq ~ Rxppsr 3net.sf.jasperreports.engine.base.JRBaseBoxBottomPen      'Ø  xr -net.sf.jasperreports.engine.base.JRBaseBoxPen      'Ø L lineBoxq ~ 2xr *net.sf.jasperreports.engine.base.JRBasePen      'Ø I PSEUDO_SERIAL_VERSION_UIDL 	lineColorq ~ /L 	lineStyleq ~ L lineStyleValuet 0Lnet/sf/jasperreports/engine/type/LineStyleEnum;L 	lineWidtht Ljava/lang/Float;L penContainert ,Lnet/sf/jasperreports/engine/JRPenContainer;xp  wîsr java.awt.Color¥3u F falphaI valueL cst Ljava/awt/color/ColorSpace;[ 	frgbvaluet [F[ fvalueq ~ ^xp    ÿfffpppp~r .net.sf.jasperreports.engine.type.LineStyleEnum          xq ~ t DOUBLEsr java.lang.FloatÚíÉ¢Û<ðì F valuexq ~ J    q ~ Tq ~ Tq ~ =psr 1net.sf.jasperreports.engine.base.JRBaseBoxLeftPen      'Ø  xq ~ V  wîsq ~ \    ÿfffppppq ~ asq ~ c    q ~ Tq ~ Tpsq ~ V  wîsq ~ \    ÿpppppsq ~ c    q ~ Tq ~ Tpsr 2net.sf.jasperreports.engine.base.JRBaseBoxRightPen      'Ø  xq ~ V  wîsq ~ \    ÿfffpppp~q ~ `t SOLIDsq ~ c    q ~ Tq ~ Tpsr 0net.sf.jasperreports.engine.base.JRBaseBoxTopPen      'Ø  xq ~ V  wîsq ~ \    ÿfffppppq ~ osq ~ c    q ~ Tq ~ Tpppppt 	Helveticapppppppppp~r 2net.sf.jasperreports.engine.type.VerticalAlignEnum          xq ~ t MIDDLE  wî        pp~r 3net.sf.jasperreports.engine.type.EvaluationTimeEnum          xq ~ t NOWsr 1net.sf.jasperreports.engine.base.JRBaseExpression      'Ø I id[ chunkst 0[Lnet/sf/jasperreports/engine/JRExpressionChunk;L valueClassNameq ~ L valueClassRealNameq ~ xp   	ur 0[Lnet.sf.jasperreports.engine.JRExpressionChunk;mYÏÞiK£U  xp   sr 6net.sf.jasperreports.engine.base.JRBaseExpressionChunk      'Ø B typeL textq ~ xpt valort java.lang.Doubleppppppsq ~ O ppt #,##0.00sr 1net.sf.jasperreports.engine.base.JRBaseStaticText      'Ø L textq ~ xq ~ .  wî   
              pq ~ q ~ 'pt staticText-7ppppq ~ Csq ~ }   
uq ~    sq ~ t !sq ~ t situacaosq ~ t 
.equals("CA")t java.lang.Booleanppppq ~ F  wîpppppt Microsoft Sans Serifq ~ Kpppppppppppsq ~ Qpsq ~ U  wîppppq ~ q ~ q ~ psq ~ e  wîppppq ~ q ~ psq ~ V  wîppppq ~ q ~ psq ~ l  wîppppq ~ q ~ psq ~ r  wîppppq ~ q ~ pppppt 	Helveticappppppppppq ~ xt Dtc:sq ~   wî   
        
   ^   
pq ~ q ~ 'pt staticText-2ppppq ~ Cppppq ~ F  wîpppppt Microsoft Sans Serifq ~ Kpppppppppppsq ~ Qpsq ~ U  wîppppq ~  q ~  q ~ psq ~ e  wîppppq ~  q ~  psq ~ V  wîppppq ~  q ~  psq ~ l  wîppppq ~  q ~  psq ~ r  wîppppq ~  q ~  pppppt 	Helveticappppppppppq ~ xt N:sq ~ )  wî   
        )       pq ~ q ~ 'pt textField-4pq ~ @ppq ~ Cppppq ~ F  wîpppppt Microsoft Sans Serifq ~ Kp~q ~ Lt LEFTq ~ Pppppppppsq ~ Qpsq ~ U  wîsq ~ \    ÿfffppppq ~ asq ~ c    q ~ ­q ~ ­q ~ ¨psq ~ e  wîsq ~ \    ÿfffppppq ~ asq ~ c    q ~ ­q ~ ­psq ~ V  wîsq ~ \    ÿpppppsq ~ c    q ~ ­q ~ ­psq ~ l  wîsq ~ \    ÿfffppppq ~ osq ~ c    q ~ ­q ~ ­psq ~ r  wîsq ~ \    ÿfffppppq ~ osq ~ c    q ~ ­q ~ ­pppppt 	Helveticappppppppppq ~ x  wî        ppq ~ {sq ~ }   uq ~    sq ~ t agenciat java.lang.Stringppppppq ~ pppsq ~   wî   
        
      
pq ~ q ~ 'pt staticText-6ppppq ~ Cppppq ~ F  wîpppppt Microsoft Sans Serifq ~ Kpppppppppppsq ~ Qpsq ~ U  wîppppq ~ Æq ~ Æq ~ Ãpsq ~ e  wîppppq ~ Æq ~ Æpsq ~ V  wîppppq ~ Æq ~ Æpsq ~ l  wîppppq ~ Æq ~ Æpsq ~ r  wîppppq ~ Æq ~ Æpppppt 	Helveticappppppppppq ~ xt C:sq ~ )  wî   
        7      
pq ~ q ~ 'pt textField-5pq ~ @ppq ~ Cppppq ~ F  wîpppppt Microsoft Sans Serifq ~ Kpq ~ «q ~ Pppppppppsq ~ Qpsq ~ U  wîsq ~ \    ÿfffppppq ~ asq ~ c    q ~ Ñq ~ Ñq ~ Îpsq ~ e  wîsq ~ \    ÿfffppppq ~ asq ~ c    q ~ Ñq ~ Ñpsq ~ V  wîsq ~ \    ÿpppppsq ~ c    q ~ Ñq ~ Ñpsq ~ l  wîsq ~ \    ÿfffppppq ~ osq ~ c    q ~ Ñq ~ Ñpsq ~ r  wîsq ~ \    ÿfffppppq ~ osq ~ c    q ~ Ñq ~ Ñpppppt 	Helveticappppppppppq ~ x  wî        ppq ~ {sq ~ }   uq ~    sq ~ t contat java.lang.Stringppppppq ~ pppsq ~   wî   
           }    pq ~ q ~ 'pt staticText-5ppppq ~ Cppppq ~ F  wîpppppt Microsoft Sans Serifq ~ Kpppppppppppsq ~ Qpsq ~ U  wîppppq ~ êq ~ êq ~ çpsq ~ e  wîppppq ~ êq ~ êpsq ~ V  wîppppq ~ êq ~ êpsq ~ l  wîppppq ~ êq ~ êpsq ~ r  wîppppq ~ êq ~ êpppppt 	Helveticappppppppppq ~ xt Ag:sq ~ )  wî   
        -      pq ~ q ~ 'pt textField-6p~q ~ ?t TRANSPARENTppq ~ Cppppq ~ F  wîpppppt Microsoft Sans Serifq ~ Kpq ~ «q ~ Pppppppppsq ~ Qpsq ~ U  wîsq ~ \    ÿfffppppq ~ asq ~ c    q ~ ÷q ~ ÷q ~ òpsq ~ e  wîsq ~ \    ÿfffppppq ~ asq ~ c    q ~ ÷q ~ ÷psq ~ V  wîsq ~ \    ÿpppppsq ~ c    q ~ ÷q ~ ÷psq ~ l  wîsq ~ \    ÿfffppppq ~ osq ~ c    q ~ ÷q ~ ÷psq ~ r  wîsq ~ \    ÿfffppppq ~ osq ~ c    q ~ ÷q ~ ÷pppppt 	Helveticappppppppppq ~ x  wî        ppq ~ {sq ~ }   
uq ~    sq ~ t situacaosq ~ t .equals("CA")?"":sq ~ t dataCompensacao_Apresentarsq ~ t .substring( 0, 10 )t java.lang.Stringppppppq ~ pppsq ~   wî   
                pq ~ q ~ 'pt staticText-4ppppq ~ Cppppq ~ F  wîpppppt Microsoft Sans Serifq ~ Kpppppppppppsq ~ Qpsq ~ U  wîppppq ~q ~q ~psq ~ e  wîppppq ~q ~psq ~ V  wîppppq ~q ~psq ~ l  wîppppq ~q ~psq ~ r  wîppppq ~q ~pppppt 	Helveticappppppppppq ~ xt  B:sq ~ )  wî   
        A   H   pq ~ q ~ 'pt textField-7pq ~ @ppq ~ Cppppq ~ F  wîpppppt Microsoft Sans Serifq ~ Kpq ~ «q ~ Pppppppppsq ~ Qpsq ~ U  wîsq ~ \    ÿfffppppq ~ asq ~ c    q ~!q ~!q ~psq ~ e  wîsq ~ \    ÿfffppppq ~ asq ~ c    q ~!q ~!psq ~ V  wîsq ~ \    ÿpppppsq ~ c    q ~!q ~!psq ~ l  wîsq ~ \    ÿfffppppq ~ osq ~ c    q ~!q ~!psq ~ r  wîsq ~ \    ÿfffppppq ~ osq ~ c    q ~!q ~!pppppt 	Helveticappppppppppq ~ x  wî        ppq ~ {sq ~ }   uq ~    sq ~ t ( sq ~ t cpfsq ~ t .equals("") ?  sq ~ t cnpjsq ~ t :sq ~ t cpfsq ~ t  )t java.lang.Stringppppppq ~ pppsq ~ )  wî   
        J   h   
pq ~ q ~ 'pt textField-2pq ~ @ppq ~ Cppppq ~ F  wîpppppt Microsoft Sans Serifq ~ Kpq ~ «q ~ Pppppppppsq ~ Qpsq ~ U  wîsq ~ \    ÿfffppppq ~ asq ~ c    q ~Fq ~Fq ~Cpsq ~ e  wîsq ~ \    ÿfffppppq ~ asq ~ c    q ~Fq ~Fpsq ~ V  wîsq ~ \    ÿpppppsq ~ c    q ~Fq ~Fpsq ~ l  wîsq ~ \    ÿfffppppq ~ osq ~ c    q ~Fq ~Fpsq ~ r  wîsq ~ \    ÿfffppppq ~ osq ~ c    q ~Fq ~Fpppppt 	Helveticappppppppppq ~ x  wî        ppq ~ {sq ~ }   uq ~    sq ~ t numerot java.lang.Stringppppppq ~ pppsq ~ )  wî   
        f       pq ~ q ~ 'pt textField-3pq ~ ôppq ~ Cppppq ~ F  wîpppppt Microsoft Sans Serifq ~ Kpq ~ «q ~ Pppppppppsq ~ Qpsq ~ U  wîsq ~ \    ÿfffppppq ~ asq ~ c    q ~_q ~_q ~\psq ~ e  wîsq ~ \    ÿfffppppq ~ asq ~ c    q ~_q ~_psq ~ V  wîsq ~ \    ÿpppppsq ~ c    q ~_q ~_psq ~ l  wîsq ~ \    ÿfffppppq ~ osq ~ c    q ~_q ~_psq ~ r  wîsq ~ \    ÿfffppppq ~ osq ~ c    q ~_q ~_pppppt 	Helveticappppppppppq ~ x  wî        ppq ~ {sq ~ }   uq ~    sq ~ t 
banco.nomet java.lang.Stringppppppq ~ pppsq ~ )  wî   
              sq ~ \    ÿÿÿÿpppq ~ q ~ 'sq ~ \    ÿ   pppppq ~ @ppq ~ Cppppq ~ F  wîpppppt Microsoft Sans Serifq ~ Kpq ~ Mq ~ Pq ~ q ~ q ~ pq ~ pppsq ~ Qpsq ~ U  wîppppq ~yq ~yq ~upsq ~ e  wîppppq ~yq ~ypsq ~ V  wîppppq ~yq ~ypsq ~ l  wîppppq ~yq ~ypsq ~ r  wîppppq ~yq ~yp~r 0net.sf.jasperreports.engine.type.LineSpacingEnum          xq ~ t SINGLEt nonept Cp1252t 	Helveticappppp~r -net.sf.jasperreports.engine.type.RotationEnum          xq ~ t NONEppppq ~ x  wî        ppq ~ {sq ~ }   uq ~    sq ~ t moedat java.lang.Stringppppppq ~ ppt #,##0.00xp  wî   pp~q ~ t PREVENTppsr java.util.HashSetºD¸·4  xpw   ?@     t "net.sf.jasperreports.engine.data.*t net.sf.jasperreports.engine.*t java.util.*xt javapsr .net.sf.jasperreports.engine.base.JRBaseDataset      'Ø I PSEUDO_SERIAL_VERSION_UIDZ isMainB whenResourceMissingType[ fieldst &[Lnet/sf/jasperreports/engine/JRField;L filterExpressionq ~ [ groupst &[Lnet/sf/jasperreports/engine/JRGroup;L nameq ~ [ 
parameterst *[Lnet/sf/jasperreports/engine/JRParameter;L 
propertiesMapq ~ :L queryt %Lnet/sf/jasperreports/engine/JRQuery;L resourceBundleq ~ L scriptletClassq ~ [ 
scriptletst *[Lnet/sf/jasperreports/engine/JRScriptlet;[ 
sortFieldst *[Lnet/sf/jasperreports/engine/JRSortField;[ 	variablest )[Lnet/sf/jasperreports/engine/JRVariable;L whenResourceMissingTypeValuet >Lnet/sf/jasperreports/engine/type/WhenResourceMissingTypeEnum;xp  wî ur &[Lnet.sf.jasperreports.engine.JRField;<ßÇN*òp  xp   	sr ,net.sf.jasperreports.engine.base.JRBaseField      'Ø L descriptionq ~ L nameq ~ L 
propertiesMapq ~ :L valueClassNameq ~ L valueClassRealNameq ~ xppt valorsr +net.sf.jasperreports.engine.JRPropertiesMap      'Ø L baseq ~ :L propertiesListq ~ L 
propertiesMapt Ljava/util/Map;xppppt java.lang.Doublepsq ~¢pt numerosq ~¥pppt java.lang.Stringpsq ~¢pt agenciasq ~¥pppt java.lang.Stringpsq ~¢pt 
banco.nomesq ~¥pppt java.lang.Stringpsq ~¢pt contasq ~¥pppt java.lang.Stringpsq ~¢pt dataCompensacao_Apresentarsq ~¥pppt java.lang.Stringpsq ~¢pt cpfsq ~¥pppt java.lang.Stringpsq ~¢pt cnpjsq ~¥pppt java.lang.Stringpsq ~¢pt situacaosq ~¥pppt java.lang.Stringpppt MovPagamento_chequesur *[Lnet.sf.jasperreports.engine.JRParameter;" *Ã`!  xp   sr 0net.sf.jasperreports.engine.base.JRBaseParameter      'Ø 	Z isForPromptingZ isSystemDefinedL defaultValueExpressionq ~ L descriptionq ~ L nameq ~ L nestedTypeNameq ~ L 
propertiesMapq ~ :L valueClassNameq ~ L valueClassRealNameq ~ xpppt REPORT_PARAMETERS_MAPpsq ~¥pppt 
java.util.Mappsq ~Ìppt 
JASPER_REPORTpsq ~¥pppt (net.sf.jasperreports.engine.JasperReportpsq ~Ìppt REPORT_CONNECTIONpsq ~¥pppt java.sql.Connectionpsq ~Ìppt REPORT_MAX_COUNTpsq ~¥pppt java.lang.Integerpsq ~Ìppt REPORT_DATA_SOURCEpsq ~¥pppt (net.sf.jasperreports.engine.JRDataSourcepsq ~Ìppt REPORT_SCRIPTLETpsq ~¥pppt /net.sf.jasperreports.engine.JRAbstractScriptletpsq ~Ìppt 
REPORT_LOCALEpsq ~¥pppt java.util.Localepsq ~Ìppt REPORT_RESOURCE_BUNDLEpsq ~¥pppt java.util.ResourceBundlepsq ~Ìppt REPORT_TIME_ZONEpsq ~¥pppt java.util.TimeZonepsq ~Ìppt REPORT_FORMAT_FACTORYpsq ~¥pppt .net.sf.jasperreports.engine.util.FormatFactorypsq ~Ìppt REPORT_CLASS_LOADERpsq ~¥pppt java.lang.ClassLoaderpsq ~Ìppt REPORT_URL_HANDLER_FACTORYpsq ~¥pppt  java.net.URLStreamHandlerFactorypsq ~Ìppt REPORT_FILE_RESOLVERpsq ~¥pppt -net.sf.jasperreports.engine.util.FileResolverpsq ~Ìppt REPORT_TEMPLATESpsq ~¥pppt java.util.Collectionpsq ~Ìppt REPORT_VIRTUALIZERpsq ~¥pppt )net.sf.jasperreports.engine.JRVirtualizerpsq ~Ìppt IS_IGNORE_PAGINATIONpsq ~¥pppq ~ psq ~Ì sq ~ }    uq ~    sq ~ t "R$"t java.lang.Stringppt moedapsq ~¥pppq ~psq ~¥psq ~    w   t ireport.scriptlethandlingt ireport.encodingt ireport.zoomt 	ireport.xt 	ireport.yxsr java.util.HashMapÚÁÃ`Ñ F 
loadFactorI 	thresholdxp?@     w      q ~t 1.8627639691774651q ~t UTF-8q ~t 0q ~t 0q ~t 0xpppppur )[Lnet.sf.jasperreports.engine.JRVariable;bæ|,·D  xp   sr /net.sf.jasperreports.engine.base.JRBaseVariable      'Ø I PSEUDO_SERIAL_VERSION_UIDB calculationB 
incrementTypeZ isSystemDefinedB 	resetTypeL calculationValuet 2Lnet/sf/jasperreports/engine/type/CalculationEnum;L 
expressionq ~ L incrementGroupq ~ *L incrementTypeValuet 4Lnet/sf/jasperreports/engine/type/IncrementTypeEnum;L incrementerFactoryClassNameq ~ L incrementerFactoryClassRealNameq ~ L initialValueExpressionq ~ L nameq ~ L 
resetGroupq ~ *L resetTypeValuet 0Lnet/sf/jasperreports/engine/type/ResetTypeEnum;L valueClassNameq ~ L valueClassRealNameq ~ xp  wî   ~r 0net.sf.jasperreports.engine.type.CalculationEnum          xq ~ t SYSTEMpp~r 2net.sf.jasperreports.engine.type.IncrementTypeEnum          xq ~ t NONEppsq ~ }   uq ~    sq ~ t new java.lang.Integer(1)q ~Üpt PAGE_NUMBERp~r .net.sf.jasperreports.engine.type.ResetTypeEnum          xq ~ t REPORTq ~Üpsq ~$  wî   q ~*ppq ~-ppsq ~ }   uq ~    sq ~ t new java.lang.Integer(1)q ~Üpt 
COLUMN_NUMBERp~q ~4t PAGEq ~Üpsq ~$  wî   ~q ~)t COUNTsq ~ }   uq ~    sq ~ t new java.lang.Integer(1)q ~Üppq ~-ppsq ~ }   uq ~    sq ~ t new java.lang.Integer(0)q ~Üpt REPORT_COUNTpq ~5q ~Üpsq ~$  wî   q ~@sq ~ }   uq ~    sq ~ t new java.lang.Integer(1)q ~Üppq ~-ppsq ~ }   uq ~    sq ~ t new java.lang.Integer(0)q ~Üpt 
PAGE_COUNTpq ~=q ~Üpsq ~$  wî   q ~@sq ~ }   uq ~    sq ~ t new java.lang.Integer(1)q ~Üppq ~-ppsq ~ }   uq ~    sq ~ t new java.lang.Integer(0)q ~Üpt COLUMN_COUNTp~q ~4t COLUMNq ~Üp~r <net.sf.jasperreports.engine.type.WhenResourceMissingTypeEnum          xq ~ t NULLq ~Ép~r 0net.sf.jasperreports.engine.type.OrientationEnum          xq ~ t PORTRAITsq ~ sq ~     w    xp  wî    ppq ~ sq ~ sq ~     w    xp  wî    ppq ~ ~r /net.sf.jasperreports.engine.type.PrintOrderEnum          xq ~ t VERTICALpsq ~ sq ~     w    xp  wî    ppq ~ pp~r 3net.sf.jasperreports.engine.type.WhenNoDataTypeEnum          xq ~ t ALL_SECTIONS_NO_DETAILsr 6net.sf.jasperreports.engine.design.JRReportCompileData      'Ø L crosstabCompileDataq ~¦L datasetCompileDataq ~¦L mainDatasetCompileDataq ~ xpsq ~?@     w       xsq ~?@     w       xur [B¬óøTà  xp  ÐÊþº¾   . î )MovPagamento_cheques_1588797166245_713831  ,net/sf/jasperreports/engine/fill/JREvaluator  parameter_REPORT_LOCALE 2Lnet/sf/jasperreports/engine/fill/JRFillParameter; parameter_JASPER_REPORT parameter_REPORT_VIRTUALIZER parameter_REPORT_TIME_ZONE parameter_REPORT_FILE_RESOLVER parameter_REPORT_SCRIPTLET parameter_REPORT_PARAMETERS_MAP parameter_REPORT_CONNECTION parameter_REPORT_CLASS_LOADER parameter_REPORT_DATA_SOURCE $parameter_REPORT_URL_HANDLER_FACTORY parameter_IS_IGNORE_PAGINATION parameter_REPORT_FORMAT_FACTORY parameter_REPORT_MAX_COUNT parameter_REPORT_TEMPLATES parameter_moeda  parameter_REPORT_RESOURCE_BUNDLE 
field_agencia .Lnet/sf/jasperreports/engine/fill/JRFillField;  field_dataCompensacao_Apresentar field_valor field_banco46nome field_conta field_situacao 	field_cpf 
field_cnpj field_numero variable_PAGE_NUMBER 1Lnet/sf/jasperreports/engine/fill/JRFillVariable; variable_COLUMN_NUMBER variable_REPORT_COUNT variable_PAGE_COUNT variable_COLUMN_COUNT <init> ()V Code ' (
  *  	  ,  	  .  	  0 	 	  2 
 	  4  	  6  	  8 
 	  :  	  <  	  >  	  @  	  B  	  D  	  F  	  H  	  J  	  L  	  N  	  P  	  R  	  T  	  V  	  X  	  Z  	  \   	  ^ ! "	  ` # "	  b $ "	  d % "	  f & "	  h LineNumberTable customizedInit 0(Ljava/util/Map;Ljava/util/Map;Ljava/util/Map;)V 
initParams (Ljava/util/Map;)V m n
  o 
initFields q n
  r initVars t n
  u 
REPORT_LOCALE w 
java/util/Map y get &(Ljava/lang/Object;)Ljava/lang/Object; { | z } 0net/sf/jasperreports/engine/fill/JRFillParameter  
JASPER_REPORT  REPORT_VIRTUALIZER  REPORT_TIME_ZONE  REPORT_FILE_RESOLVER  REPORT_SCRIPTLET  REPORT_PARAMETERS_MAP  REPORT_CONNECTION  REPORT_CLASS_LOADER  REPORT_DATA_SOURCE  REPORT_URL_HANDLER_FACTORY  IS_IGNORE_PAGINATION  REPORT_FORMAT_FACTORY  REPORT_MAX_COUNT  REPORT_TEMPLATES  moeda  REPORT_RESOURCE_BUNDLE  agencia ¡ ,net/sf/jasperreports/engine/fill/JRFillField £ dataCompensacao_Apresentar ¥ valor § 
banco.nome © conta « situacao ­ cpf ¯ cnpj ± numero ³ PAGE_NUMBER µ /net/sf/jasperreports/engine/fill/JRFillVariable · 
COLUMN_NUMBER ¹ REPORT_COUNT » 
PAGE_COUNT ½ COLUMN_COUNT ¿ evaluate (I)Ljava/lang/Object; 
Exceptions java/lang/Throwable Ä R$ Æ java/lang/Integer È (I)V ' Ê
 É Ë getValue ()Ljava/lang/Object; Í Î
 ¤ Ï java/lang/Double Ñ java/lang/String Ó CA Õ equals (Ljava/lang/Object;)Z × Ø
 Ô Ù java/lang/Boolean Û valueOf (Z)Ljava/lang/Boolean; Ý Þ
 Ü ß   á 	substring (II)Ljava/lang/String; ã ä
 Ô å
  Ï evaluateOld getOldValue é Î
 ¤ ê evaluateEstimated 
SourceFile !                      	     
               
                                                                                                ! "    # "    $ "    % "    & "     ' (  )  8      *· +*µ -*µ /*µ 1*µ 3*µ 5*µ 7*µ 9*µ ;*µ =*µ ?*µ A*µ C*µ E*µ G*µ I*µ K*µ M*µ O*µ Q*µ S*µ U*µ W*µ Y*µ [*µ ]*µ _*µ a*µ c*µ e*µ g*µ i±    j    !      	          ! " " ' # , $ 1 % 6 & ; ' @ ( E ) J * O + T , Y - ^ . c / h 0 m 1 r 2 w 3 | 4  5  6  7  8  9  :    k l  )   4     *+· p*,· s*-· v±    j       F  G 
 H  I  m n  )      3*+x¹ ~ À À µ -*+¹ ~ À À µ /*+¹ ~ À À µ 1*+¹ ~ À À µ 3*+¹ ~ À À µ 5*+¹ ~ À À µ 7*+¹ ~ À À µ 9*+¹ ~ À À µ ;*+¹ ~ À À µ =*+¹ ~ À À µ ?*+¹ ~ À À µ A*+¹ ~ À À µ C*+¹ ~ À À µ E*+¹ ~ À À µ G*+¹ ~ À À µ I*+¹ ~ À À µ K*+ ¹ ~ À À µ M±    j   J    Q  R $ S 6 T H U Z V l W ~ X  Y ¢ Z ´ [ Æ \ Ø ] ê ^ ü _ `  a2 b  q n  )   ß     £*+¢¹ ~ À ¤À ¤µ O*+¦¹ ~ À ¤À ¤µ Q*+¨¹ ~ À ¤À ¤µ S*+ª¹ ~ À ¤À ¤µ U*+¬¹ ~ À ¤À ¤µ W*+®¹ ~ À ¤À ¤µ Y*+°¹ ~ À ¤À ¤µ [*+²¹ ~ À ¤À ¤µ ]*+´¹ ~ À ¤À ¤µ _±    j   * 
   j  k $ l 6 m H n Z o l p ~ q  r ¢ s  t n  )        [*+¶¹ ~ À ¸À ¸µ a*+º¹ ~ À ¸À ¸µ c*+¼¹ ~ À ¸À ¸µ e*+¾¹ ~ À ¸À ¸µ g*+À¹ ~ À ¸À ¸µ i±    j       {  | $ } 6 ~ H  Z   Á Â  Ã     Å )  3    Mª            U   [   g   s            £   ¯   »   É   ç   õ    .  [  i  wÇM§*» ÉY· ÌM§» ÉY· ÌM§» ÉY· ÌM§» ÉY· ÌM§ ú» ÉY· ÌM§ î» ÉY· ÌM§ â» ÉY· ÌM§ Ö» ÉY· ÌM§ Ê*´ S¶ ÐÀ ÒM§ ¼*´ Y¶ ÐÀ ÔÖ¶ Ú § ¸ àM§ *´ O¶ ÐÀ ÔM§ *´ W¶ ÐÀ ÔM§ *´ Y¶ ÐÀ ÔÖ¶ Ú â§ *´ Q¶ ÐÀ Ô
¶ æM§ W*´ [¶ ÐÀ Ôâ¶ Ú *´ ]¶ ÐÀ Ô§ 
*´ [¶ ÐÀ ÔM§ **´ _¶ ÐÀ ÔM§ *´ U¶ ÐÀ ÔM§ *´ K¶ çÀ ÔM,°    j    &      X  [  ^  g  j  s  v     ¢  £  §  ¨  ¬ £ ­ ¦ ± ¯ ² ² ¶ » · ¾ » É ¼ Ì À ç Á ê Å õ Æ ø Ê Ë Ï. Ð1 Ô[ Õ^ Ùi Úl Þw ßz ã ë  è Â  Ã     Å )  3    Mª            U   [   g   s            £   ¯   »   É   ç   õ    .  [  i  wÇM§*» ÉY· ÌM§» ÉY· ÌM§» ÉY· ÌM§» ÉY· ÌM§ ú» ÉY· ÌM§ î» ÉY· ÌM§ â» ÉY· ÌM§ Ö» ÉY· ÌM§ Ê*´ S¶ ëÀ ÒM§ ¼*´ Y¶ ëÀ ÔÖ¶ Ú § ¸ àM§ *´ O¶ ëÀ ÔM§ *´ W¶ ëÀ ÔM§ *´ Y¶ ëÀ ÔÖ¶ Ú â§ *´ Q¶ ëÀ Ô
¶ æM§ W*´ [¶ ëÀ Ôâ¶ Ú *´ ]¶ ëÀ Ô§ 
*´ [¶ ëÀ ÔM§ **´ _¶ ëÀ ÔM§ *´ U¶ ëÀ ÔM§ *´ K¶ çÀ ÔM,°    j    &   ô  ö X ú [ û ^ ÿ g  j s v	 
      £ ¦ ¯ ²" »# ¾' É( Ì, ç- ê1 õ2 ø67;.<1@[A^EiFlJwKzOW  ì Â  Ã     Å )  3    Mª            U   [   g   s            £   ¯   »   É   ç   õ    .  [  i  wÇM§*» ÉY· ÌM§» ÉY· ÌM§» ÉY· ÌM§» ÉY· ÌM§ ú» ÉY· ÌM§ î» ÉY· ÌM§ â» ÉY· ÌM§ Ö» ÉY· ÌM§ Ê*´ S¶ ÐÀ ÒM§ ¼*´ Y¶ ÐÀ ÔÖ¶ Ú § ¸ àM§ *´ O¶ ÐÀ ÔM§ *´ W¶ ÐÀ ÔM§ *´ Y¶ ÐÀ ÔÖ¶ Ú â§ *´ Q¶ ÐÀ Ô
¶ æM§ W*´ [¶ ÐÀ Ôâ¶ Ú *´ ]¶ ÐÀ Ô§ 
*´ [¶ ÐÀ ÔM§ **´ _¶ ÐÀ ÔM§ *´ U¶ ÐÀ ÔM§ *´ K¶ çÀ ÔM,°    j    &  ` b Xf [g ^k gl jp sq vu v z {    £ ¦ ¯ ² » ¾ É Ì ç ê õ ø¢£§.¨1¬[­^±i²l¶w·z»Ã  í    t _1588797166245_713831t 2net.sf.jasperreports.engine.design.JRJavacCompiler