<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="CaixaPorOperadorRel" pageWidth="2910" pageHeight="3619" orientation="Landscape" columnWidth="2855" leftMargin="19" rightMargin="36" topMargin="2" bottomMargin="2" whenResourceMissingType="Empty">
	<property name="ireport.scriptlethandling" value="0"/>
	<property name="ireport.encoding" value="ISO-8859-1"/>
	<property name="ireport.zoom" value="1.0"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="0"/>
	<import value="net.sf.jasperreports.engine.*"/>
	<import value="java.util.*"/>
	<import value="net.sf.jasperreports.engine.data.*"/>
	<queryString>
		<![CDATA[]]>
	</queryString>
	<field name="cliente.codigoMatricula" class="java.lang.Integer"/>
	<field name="tipoVinculo" class="java.lang.String"/>
	<field name="cliente.situacaoClienteSinteticoVO.dataNascimento" class="java.util.Date"/>
	<field name="cliente.situacaoClienteSinteticoVO.profissao" class="java.lang.String"/>
	<field name="cliente.situacaoClienteSinteticoVO.situacao" class="java.lang.String"/>
	<field name="cliente.situacaoClienteSinteticoVO.codigoContrato" class="java.lang.Integer"/>
	<field name="cliente.situacaoClienteSinteticoVO.mnemonicoContrato" class="java.lang.String"/>
	<field name="cliente.situacaoClienteSinteticoVO.nomePlano" class="java.lang.String"/>
	<field name="cliente.situacaoClienteSinteticoVO.duracaoContratoMeses" class="java.lang.Integer"/>
	<field name="cliente.situacaoClienteSinteticoVO.dataVigenciaDeM" class="java.lang.Integer"/>
	<field name="cliente.situacaoClienteSinteticoVO.dataVigenciaDeY" class="java.lang.Integer"/>
	<field name="cliente.situacaoClienteSinteticoVO.dataVigenciaDeD" class="java.lang.Integer"/>
	<field name="cliente.situacaoClienteSinteticoVO.dataVigenciaAteM" class="java.lang.Integer"/>
	<field name="cliente.situacaoClienteSinteticoVO.dataVigenciaAteY" class="java.lang.Integer"/>
	<field name="cliente.situacaoClienteSinteticoVO.dataVigenciaAteD" class="java.lang.Integer"/>
	<field name="cliente.situacaoClienteSinteticoVO.dataVigenciaAteAjustadaM" class="java.lang.Integer"/>
	<field name="cliente.situacaoClienteSinteticoVO.dataVigenciaAteAjustadaY" class="java.lang.Integer"/>
	<field name="cliente.situacaoClienteSinteticoVO.dataLancamentoContratoD" class="java.lang.Integer"/>
	<field name="cliente.situacaoClienteSinteticoVO.dataLancamentoContratoM" class="java.lang.Integer"/>
	<field name="cliente.situacaoClienteSinteticoVO.dataLancamentoContratoY" class="java.lang.Integer"/>
	<field name="cliente.situacaoClienteSinteticoVO.dataRenovacaoContratoD" class="java.lang.Integer"/>
	<field name="cliente.situacaoClienteSinteticoVO.dataRenovacaoContratoM" class="java.lang.Integer"/>
	<field name="cliente.situacaoClienteSinteticoVO.dataRenovacaoContratoY" class="java.lang.Integer"/>
	<field name="cliente.situacaoClienteSinteticoVO.dataRematriculaContratoD" class="java.lang.Integer"/>
	<field name="cliente.situacaoClienteSinteticoVO.dataRematriculaContratoM" class="java.lang.Integer"/>
	<field name="cliente.situacaoClienteSinteticoVO.dataRematriculaContratoY" class="java.lang.Integer"/>
	<field name="cliente.situacaoClienteSinteticoVO.dataUltimoBVD" class="java.lang.Integer"/>
	<field name="cliente.situacaoClienteSinteticoVO.dataUltimoBVM" class="java.lang.Integer"/>
	<field name="cliente.situacaoClienteSinteticoVO.dataUltimoBVY" class="java.lang.Integer"/>
	<field name="cliente.situacaoClienteSinteticoVO.idade" class="java.lang.Integer"/>
	<field name="cliente.pessoa.nome" class="java.lang.String"/>
	<field name="cliente.situacaoClienteSinteticoVO.dataVigenciaAteAjustadaD" class="java.lang.Integer"/>
	<field name="cliente.situacaoClienteSinteticoVO.nrDiasUltimoAcesso" class="java.lang.Long"/>
	<field name="horaMaisAcesso" class="java.lang.String"/>
	<title>
		<band height="24">
			<staticText>
				<reportElement x="0" y="0" width="100" height="20"/>
				<textElement>
					<font isBold="true"/>
				</textElement>
				<text><![CDATA[Matrícula]]></text>
			</staticText>
			<staticText>
				<reportElement x="100" y="0" width="337" height="20"/>
				<textElement>
					<font isBold="true"/>
				</textElement>
				<text><![CDATA[Nome]]></text>
			</staticText>
			<staticText>
				<reportElement x="2450" y="0" width="102" height="20"/>
				<textElement>
					<font isBold="true"/>
				</textElement>
				<text><![CDATA[Idade]]></text>
			</staticText>
			<staticText>
				<reportElement x="2552" y="0" width="100" height="20"/>
				<textElement>
					<font isBold="true"/>
				</textElement>
				<text><![CDATA[Profissão]]></text>
			</staticText>
			<staticText>
				<reportElement x="2652" y="0" width="100" height="20"/>
				<textElement>
					<font isBold="true"/>
				</textElement>
				<text><![CDATA[Duração]]></text>
			</staticText>
			<staticText>
				<reportElement x="2752" y="0" width="100" height="20"/>
				<textElement>
					<font isBold="true"/>
				</textElement>
				<text><![CDATA[Modalidade]]></text>
			</staticText>
			<staticText>
				<reportElement x="437" y="0" width="102" height="20"/>
				<textElement>
					<font isBold="true"/>
				</textElement>
				<text><![CDATA[Situação Cliente]]></text>
			</staticText>
			<staticText>
				<reportElement x="689" y="0" width="152" height="20"/>
				<textElement>
					<font isBold="true"/>
				</textElement>
				<text><![CDATA[Mês Vigência Início]]></text>
			</staticText>
			<staticText>
				<reportElement x="841" y="0" width="131" height="20"/>
				<textElement>
					<font isBold="true"/>
				</textElement>
				<text><![CDATA[Ano Vigência Início]]></text>
			</staticText>
			<staticText>
				<reportElement x="539" y="0" width="150" height="20"/>
				<textElement>
					<font isBold="true"/>
				</textElement>
				<text><![CDATA[Dia Vigência Início]]></text>
			</staticText>
			<staticText>
				<reportElement x="1393" y="0" width="179" height="20"/>
				<textElement>
					<font isBold="true"/>
				</textElement>
				<text><![CDATA[Dia Rematricula Realizada]]></text>
			</staticText>
			<staticText>
				<reportElement x="1572" y="0" width="179" height="20"/>
				<textElement>
					<font isBold="true"/>
				</textElement>
				<text><![CDATA[Mês  Rematricula Realizada]]></text>
			</staticText>
			<staticText>
				<reportElement x="1751" y="0" width="191" height="20"/>
				<textElement>
					<font isBold="true"/>
				</textElement>
				<text><![CDATA[Ano Rematricula Realizada]]></text>
			</staticText>
			<staticText>
				<reportElement x="2171" y="0" width="105" height="20"/>
				<textElement>
					<font isBold="true"/>
				</textElement>
				<text><![CDATA[Dia Último BV]]></text>
			</staticText>
			<staticText>
				<reportElement x="2276" y="0" width="87" height="20"/>
				<textElement>
					<font isBold="true"/>
				</textElement>
				<text><![CDATA[Mês Último BV]]></text>
			</staticText>
			<staticText>
				<reportElement x="2363" y="0" width="87" height="20"/>
				<textElement>
					<font isBold="true"/>
				</textElement>
				<text><![CDATA[Ano Último BV]]></text>
			</staticText>
			<staticText>
				<reportElement x="972" y="0" width="140" height="20"/>
				<textElement>
					<font isBold="true"/>
				</textElement>
				<text><![CDATA[Dia Vigência Término]]></text>
			</staticText>
			<staticText>
				<reportElement x="1112" y="0" width="144" height="20"/>
				<textElement>
					<font isBold="true"/>
				</textElement>
				<text><![CDATA[Mês Vigência Término]]></text>
			</staticText>
			<staticText>
				<reportElement x="1256" y="0" width="137" height="20"/>
				<textElement>
					<font isBold="true"/>
				</textElement>
				<text><![CDATA[Ano Vigência Término]]></text>
			</staticText>
			<staticText>
				<reportElement x="1942" y="0" width="93" height="20"/>
				<textElement>
					<font isBold="true"/>
				</textElement>
				<text><![CDATA[Dias Ult. Acesso]]></text>
			</staticText>
			<staticText>
				<reportElement x="2035" y="0" width="136" height="20"/>
				<textElement>
					<font isBold="true"/>
				</textElement>
				<text><![CDATA[Período Mais Acessado]]></text>
			</staticText>
		</band>
	</title>
	<detail>
		<band height="20">
			<textField>
				<reportElement x="0" y="0" width="100" height="20"/>
				<textElement/>
				<textFieldExpression class="java.lang.Integer"><![CDATA[$F{cliente.codigoMatricula}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="2450" y="0" width="102" height="20"/>
				<textElement/>
				<textFieldExpression class="java.lang.Integer"><![CDATA[$F{cliente.situacaoClienteSinteticoVO.idade}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="2552" y="0" width="100" height="20"/>
				<textElement/>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{cliente.situacaoClienteSinteticoVO.profissao}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="2652" y="0" width="100" height="20"/>
				<textElement/>
				<textFieldExpression class="java.lang.Integer"><![CDATA[$F{cliente.situacaoClienteSinteticoVO.duracaoContratoMeses}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="2752" y="0" width="100" height="20"/>
				<textElement/>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{cliente.situacaoClienteSinteticoVO.mnemonicoContrato}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="437" y="0" width="102" height="20"/>
				<textElement/>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{cliente.situacaoClienteSinteticoVO.situacao}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="539" y="0" width="150" height="20"/>
				<textElement/>
				<textFieldExpression class="java.lang.Integer"><![CDATA[$F{cliente.situacaoClienteSinteticoVO.dataVigenciaDeD}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="841" y="0" width="131" height="20"/>
				<textElement/>
				<textFieldExpression class="java.lang.Integer"><![CDATA[$F{cliente.situacaoClienteSinteticoVO.dataVigenciaDeY}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="972" y="0" width="140" height="20"/>
				<textElement/>
				<textFieldExpression class="java.lang.Integer"><![CDATA[$F{cliente.situacaoClienteSinteticoVO.dataVigenciaAteAjustadaD}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="1112" y="0" width="144" height="20"/>
				<textElement/>
				<textFieldExpression class="java.lang.Integer"><![CDATA[$F{cliente.situacaoClienteSinteticoVO.dataVigenciaAteAjustadaM}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="1393" y="0" width="179" height="20"/>
				<textElement/>
				<textFieldExpression class="java.lang.Integer"><![CDATA[$F{cliente.situacaoClienteSinteticoVO.dataRematriculaContratoD}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="1572" y="0" width="179" height="20"/>
				<textElement/>
				<textFieldExpression class="java.lang.Integer"><![CDATA[$F{cliente.situacaoClienteSinteticoVO.dataRematriculaContratoM}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="1751" y="0" width="191" height="20"/>
				<textElement/>
				<textFieldExpression class="java.lang.Integer"><![CDATA[$F{cliente.situacaoClienteSinteticoVO.dataRematriculaContratoY}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="2171" y="0" width="105" height="20"/>
				<textElement/>
				<textFieldExpression class="java.lang.Integer"><![CDATA[$F{cliente.situacaoClienteSinteticoVO.dataUltimoBVD}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="2276" y="0" width="87" height="20"/>
				<textElement/>
				<textFieldExpression class="java.lang.Integer"><![CDATA[$F{cliente.situacaoClienteSinteticoVO.dataUltimoBVM}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="2363" y="0" width="87" height="20"/>
				<textElement/>
				<textFieldExpression class="java.lang.Integer"><![CDATA[$F{cliente.situacaoClienteSinteticoVO.dataUltimoBVY}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="689" y="0" width="152" height="20"/>
				<textElement/>
				<textFieldExpression class="java.lang.Integer"><![CDATA[$F{cliente.situacaoClienteSinteticoVO.dataVigenciaDeM}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="1256" y="0" width="137" height="20"/>
				<textElement/>
				<textFieldExpression class="java.lang.Integer"><![CDATA[$F{cliente.situacaoClienteSinteticoVO.dataVigenciaAteAjustadaY}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement stretchType="RelativeToBandHeight" x="100" y="0" width="337" height="20"/>
				<textElement/>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{cliente.pessoa.nome}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="1942" y="0" width="93" height="20"/>
				<textElement/>
				<textFieldExpression class="java.lang.Long"><![CDATA[$F{cliente.situacaoClienteSinteticoVO.nrDiasUltimoAcesso}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="2035" y="0" width="136" height="20"/>
				<textElement/>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{horaMaisAcesso}]]></textFieldExpression>
			</textField>
		</band>
	</detail>
</jasperReport>
