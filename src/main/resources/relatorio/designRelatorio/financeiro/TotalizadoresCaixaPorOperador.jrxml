<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="TotalizadoresCaixaPorOperador" pageWidth="500" pageHeight="535" orientation="Landscape" whenNoDataType="AllSectionsNoDetail" columnWidth="500" leftMargin="0" rightMargin="0" topMargin="0" bottomMargin="0">
	<property name="ireport.scriptlethandling" value="0"/>
	<property name="ireport.encoding" value="UTF-8"/>
	<property name="ireport.zoom" value="2.5937424601000028"/>
	<property name="ireport.x" value="128"/>
	<property name="ireport.y" value="0"/>
	<import value="net.sf.jasperreports.engine.*"/>
	<import value="java.util.*"/>
	<import value="net.sf.jasperreports.engine.data.*"/>
	<subDataset name="dataset1"/>
	<parameter name="qtdDV" class="java.lang.String"/>
	<parameter name="valorDV" class="java.lang.Double"/>
	<parameter name="moeda" class="java.lang.String"/>
	<field name="descricao" class="java.lang.String"/>
	<field name="qtd" class="java.lang.Integer"/>
	<field name="valor" class="java.lang.Double"/>
	<field name="total" class="java.lang.Boolean"/>
	<field name="titulo" class="java.lang.Boolean"/>
	<field name="labelTotal" class="java.lang.String"/>
	<background>
		<band splitType="Stretch"/>
	</background>
	<title>
		<band splitType="Stretch"/>
	</title>
	<pageHeader>
		<band splitType="Stretch"/>
	</pageHeader>
	<columnHeader>
		<band splitType="Stretch"/>
	</columnHeader>
	<detail>
		<band height="20">
			<staticText>
				<reportElement key="staticText-115" positionType="Float" mode="Opaque" x="384" y="4" width="36" height="14">
					<printWhenExpression><![CDATA[!$F{titulo} && !$F{total}]]></printWhenExpression>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Microsoft Sans Serif" size="11" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[ Valor:]]></text>
			</staticText>
			<textField pattern="" isBlankWhenNull="false">
				<reportElement key="textField-220" positionType="Float" mode="Opaque" x="325" y="3" width="35" height="14">
					<printWhenExpression><![CDATA[!$F{total} && !$F{titulo}]]></printWhenExpression>
				</reportElement>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Microsoft Sans Serif" size="11" isBold="false" isItalic="false" pdfFontName="Helvetica"/>
				</textElement>
				<textFieldExpression class="java.lang.Integer"><![CDATA[$F{qtd}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement key="staticText-114" positionType="Float" mode="Opaque" x="295" y="3" width="30" height="14">
					<printWhenExpression><![CDATA[!$F{total} && !$F{titulo}]]></printWhenExpression>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Microsoft Sans Serif" size="11" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[ Qtd:]]></text>
			</staticText>
			<textField pattern="#,##0.00" isBlankWhenNull="false">
				<reportElement key="textField-221" positionType="Float" mode="Opaque" x="420" y="3" width="74" height="14">
					<printWhenExpression><![CDATA[!$F{titulo} && !$F{total}]]></printWhenExpression>
				</reportElement>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Microsoft Sans Serif" size="11" isBold="false" isItalic="false" pdfFontName="Helvetica"/>
				</textElement>
				<textFieldExpression class="java.lang.Double"><![CDATA[$F{valor}]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="false">
				<reportElement key="textField-220" positionType="Float" mode="Opaque" x="7" y="1" width="208" height="14">
					<printWhenExpression><![CDATA[!$F{titulo}]]></printWhenExpression>
				</reportElement>
				<textElement verticalAlignment="Middle">
					<font fontName="Microsoft Sans Serif" size="11" isBold="false" isItalic="false" pdfFontName="Helvetica"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{descricao}]]></textFieldExpression>
			</textField>
			<line>
				<reportElement x="384" y="2" width="110" height="1">
					<printWhenExpression><![CDATA[$F{total}]]></printWhenExpression>
				</reportElement>
			</line>
			<textField pattern="" isBlankWhenNull="false">
				<reportElement key="textField-220" positionType="Float" mode="Opaque" x="4" y="-1" width="291" height="17">
					<printWhenExpression><![CDATA[$F{titulo}]]></printWhenExpression>
				</reportElement>
				<textElement verticalAlignment="Middle">
					<font fontName="Microsoft Sans Serif" size="11" isBold="true" isItalic="false" isUnderline="true" pdfFontName="Helvetica"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{descricao}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement key="staticText-116" positionType="Float" mode="Opaque" x="387" y="4" width="39" height="14">
					<printWhenExpression><![CDATA[$F{total}]]></printWhenExpression>
				</reportElement>
				<textElement textAlignment="Left" verticalAlignment="Middle" markup="none">
					<font fontName="Microsoft Sans Serif" size="11" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{moeda}]]></textFieldExpression>
			</textField>
			<textField pattern="#,##0.00" isBlankWhenNull="false">
				<reportElement key="textField-221" positionType="Float" mode="Opaque" x="420" y="4" width="74" height="14">
					<printWhenExpression><![CDATA[$F{total}]]></printWhenExpression>
				</reportElement>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Microsoft Sans Serif" size="11" isBold="true" isItalic="false" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.Double"><![CDATA[$F{valor}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="false">
				<reportElement key="textField-221" positionType="Float" stretchType="RelativeToBandHeight" mode="Opaque" x="186" y="3" width="198" height="14">
					<printWhenExpression><![CDATA[$F{total}]]></printWhenExpression>
				</reportElement>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Microsoft Sans Serif" size="11" isBold="true" isItalic="false" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{labelTotal}]]></textFieldExpression>
			</textField>
		</band>
	</detail>
	<columnFooter>
		<band splitType="Stretch"/>
	</columnFooter>
	<summary>
		<band splitType="Stretch"/>
	</summary>
</jasperReport>
