<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="RelatorioOrcamentarioSemestral" language="groovy" pageWidth="842" pageHeight="595" orientation="Landscape" columnWidth="802" leftMargin="20" rightMargin="20" topMargin="20" bottomMargin="20">
	<property name="ireport.zoom" value="1.5931540885518052"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="0"/>
	<style name="corFundoRetangulo" mode="Opaque" forecolor="#00FFFF" backcolor="#00FFFF">
		<conditionalStyle>
			<conditionExpression><![CDATA[$F{nivelArvore}.intValue() == 1]]></conditionExpression>
			<style mode="Opaque" forecolor="#C0C0C0" backcolor="#C0C0C0">
				<pen lineColor="#C0C0C0"/>
			</style>
		</conditionalStyle>
		<conditionalStyle>
			<conditionExpression><![CDATA[$F{nivelArvore}.intValue() == 2]]></conditionExpression>
			<style mode="Opaque" forecolor="#CCFFCC" backcolor="#CCFFCC">
				<pen lineColor="#98FB98"/>
			</style>
		</conditionalStyle>
		<conditionalStyle>
			<conditionExpression><![CDATA[$F{nivelArvore}.intValue() == 3]]></conditionExpression>
			<style mode="Opaque" forecolor="#FFFFFF" backcolor="#FFFFFF">
				<pen lineColor="#FFFFFF"/>
			</style>
		</conditionalStyle>
		<conditionalStyle>
			<conditionExpression><![CDATA[$F{nivelArvore}.intValue() == 4]]></conditionExpression>
			<style mode="Opaque" forecolor="#ADD8E6" backcolor="#ADD8E6">
				<pen lineColor="#ADD8E6"/>
			</style>
		</conditionalStyle>
		<conditionalStyle>
			<conditionExpression><![CDATA[$F{nivelArvore}.intValue() == 5]]></conditionExpression>
			<style mode="Opaque" forecolor="#F5DEB3" backcolor="#F5DEB3">
				<pen lineColor="#F5DEB3"/>
			</style>
		</conditionalStyle>
	</style>
	<parameter name="logoPadraoRelatorio" class="java.io.InputStream"/>
	<parameter name="tituloRelatorio" class="java.lang.String"/>
	<parameter name="data1" class="java.lang.String"/>
	<parameter name="data2" class="java.lang.String"/>
	<parameter name="data3" class="java.lang.String"/>
	<parameter name="data4" class="java.lang.String"/>
	<parameter name="data5" class="java.lang.String"/>
	<parameter name="data6" class="java.lang.String"/>
	<parameter name="mes1TotalFimPaginaReceitasPrevisto" class="java.lang.String"/>
	<parameter name="mes1TotalFimPaginaReceitasRealizado" class="java.lang.String"/>
	<parameter name="mes1TotalFimPaginaDespesasPrevisto" class="java.lang.String"/>
	<parameter name="mes1TotalFimPaginaDespesasRealizado" class="java.lang.String"/>
	<parameter name="mes1TotalFimPaginaInvestimentosPrevisto" class="java.lang.String"/>
	<parameter name="mes1TotalFimPaginaInvestimentosRealizado" class="java.lang.String"/>
	<parameter name="mes1TotalFimPaginaInvestimentosDespesasPrevisto" class="java.lang.String"/>
	<parameter name="mes1TotalFimPaginaInvestimentosDespesasRealizado" class="java.lang.String"/>
	<parameter name="mes1TotalFimPaginaSemInvestimentosPrevisto" class="java.lang.String"/>
	<parameter name="mes1TotalFimPaginaSemInvestimentosRealizado" class="java.lang.String"/>
	<parameter name="mes1TotalFimPaginaComInvestimentosPrevisto" class="java.lang.String"/>
	<parameter name="mes1TotalFimPaginaComInvestimentosRealizado" class="java.lang.String"/>
	<parameter name="mes2TotalFimPaginaReceitasPrevisto" class="java.lang.String"/>
	<parameter name="mes2TotalFimPaginaReceitasRealizado" class="java.lang.String"/>
	<parameter name="mes2TotalFimPaginaDespesasPrevisto" class="java.lang.String"/>
	<parameter name="mes2TotalFimPaginaDespesasRealizado" class="java.lang.String"/>
	<parameter name="mes2TotalFimPaginaInvestimentosPrevisto" class="java.lang.String"/>
	<parameter name="mes2TotalFimPaginaInvestimentosRealizado" class="java.lang.String"/>
	<parameter name="mes2TotalFimPaginaInvestimentosDespesasPrevisto" class="java.lang.String"/>
	<parameter name="mes2TotalFimPaginaInvestimentosDespesasRealizado" class="java.lang.String"/>
	<parameter name="mes2TotalFimPaginaSemInvestimentosPrevisto" class="java.lang.String"/>
	<parameter name="mes2TotalFimPaginaSemInvestimentosRealizado" class="java.lang.String"/>
	<parameter name="mes2TotalFimPaginaComInvestimentosPrevisto" class="java.lang.String"/>
	<parameter name="mes2TotalFimPaginaComInvestimentosRealizado" class="java.lang.String"/>
	<parameter name="mes3TotalFimPaginaReceitasPrevisto" class="java.lang.String"/>
	<parameter name="mes3TotalFimPaginaReceitasRealizado" class="java.lang.String"/>
	<parameter name="mes3TotalFimPaginaDespesasPrevisto" class="java.lang.String"/>
	<parameter name="mes3TotalFimPaginaDespesasRealizado" class="java.lang.String"/>
	<parameter name="mes3TotalFimPaginaInvestimentosPrevisto" class="java.lang.String"/>
	<parameter name="mes3TotalFimPaginaInvestimentosRealizado" class="java.lang.String"/>
	<parameter name="mes3TotalFimPaginaInvestimentosDespesasPrevisto" class="java.lang.String"/>
	<parameter name="mes3TotalFimPaginaInvestimentosDespesasRealizado" class="java.lang.String"/>
	<parameter name="mes3TotalFimPaginaSemInvestimentosPrevisto" class="java.lang.String"/>
	<parameter name="mes3TotalFimPaginaSemInvestimentosRealizado" class="java.lang.String"/>
	<parameter name="mes3TotalFimPaginaComInvestimentosPrevisto" class="java.lang.String"/>
	<parameter name="mes3TotalFimPaginaComInvestimentosRealizado" class="java.lang.String"/>
	<parameter name="mes4TotalFimPaginaReceitasPrevisto" class="java.lang.String"/>
	<parameter name="mes4TotalFimPaginaReceitasRealizado" class="java.lang.String"/>
	<parameter name="mes4TotalFimPaginaDespesasPrevisto" class="java.lang.String"/>
	<parameter name="mes4TotalFimPaginaDespesasRealizado" class="java.lang.String"/>
	<parameter name="mes4TotalFimPaginaInvestimentosPrevisto" class="java.lang.String"/>
	<parameter name="mes4TotalFimPaginaInvestimentosRealizado" class="java.lang.String"/>
	<parameter name="mes4TotalFimPaginaInvestimentosDespesasPrevisto" class="java.lang.String"/>
	<parameter name="mes4TotalFimPaginaInvestimentosDespesasRealizado" class="java.lang.String"/>
	<parameter name="mes4TotalFimPaginaSemInvestimentosPrevisto" class="java.lang.String"/>
	<parameter name="mes4TotalFimPaginaSemInvestimentosRealizado" class="java.lang.String"/>
	<parameter name="mes4TotalFimPaginaComInvestimentosPrevisto" class="java.lang.String"/>
	<parameter name="mes4TotalFimPaginaComInvestimentosRealizado" class="java.lang.String"/>
	<parameter name="mes5TotalFimPaginaReceitasPrevisto" class="java.lang.String"/>
	<parameter name="mes5TotalFimPaginaReceitasRealizado" class="java.lang.String"/>
	<parameter name="mes5TotalFimPaginaDespesasPrevisto" class="java.lang.String"/>
	<parameter name="mes5TotalFimPaginaDespesasRealizado" class="java.lang.String"/>
	<parameter name="mes5TotalFimPaginaInvestimentosPrevisto" class="java.lang.String"/>
	<parameter name="mes5TotalFimPaginaInvestimentosRealizado" class="java.lang.String"/>
	<parameter name="mes5TotalFimPaginaInvestimentosDespesasPrevisto" class="java.lang.String"/>
	<parameter name="mes5TotalFimPaginaInvestimentosDespesasRealizado" class="java.lang.String"/>
	<parameter name="mes5TotalFimPaginaSemInvestimentosPrevisto" class="java.lang.String"/>
	<parameter name="mes5TotalFimPaginaSemInvestimentosRealizado" class="java.lang.String"/>
	<parameter name="mes5TotalFimPaginaComInvestimentosPrevisto" class="java.lang.String"/>
	<parameter name="mes5TotalFimPaginaComInvestimentosRealizado" class="java.lang.String"/>
	<parameter name="mes6TotalFimPaginaReceitasPrevisto" class="java.lang.String"/>
	<parameter name="mes6TotalFimPaginaReceitasRealizado" class="java.lang.String"/>
	<parameter name="mes6TotalFimPaginaDespesasPrevisto" class="java.lang.String"/>
	<parameter name="mes6TotalFimPaginaDespesasRealizado" class="java.lang.String"/>
	<parameter name="mes6TotalFimPaginaInvestimentosPrevisto" class="java.lang.String"/>
	<parameter name="mes6TotalFimPaginaInvestimentosRealizado" class="java.lang.String"/>
	<parameter name="mes6TotalFimPaginaInvestimentosDespesasPrevisto" class="java.lang.String"/>
	<parameter name="mes6TotalFimPaginaInvestimentosDespesasRealizado" class="java.lang.String"/>
	<parameter name="mes6TotalFimPaginaSemInvestimentosPrevisto" class="java.lang.String"/>
	<parameter name="mes6TotalFimPaginaSemInvestimentosRealizado" class="java.lang.String"/>
	<parameter name="mes6TotalFimPaginaComInvestimentosPrevisto" class="java.lang.String"/>
	<parameter name="mes6TotalFimPaginaComInvestimentosRealizado" class="java.lang.String"/>
	<parameter name="totalPrevistoRealizadoFimPaginaReceitasPrevisto" class="java.lang.String"/>
	<parameter name="totalPrevistoRealizadoFimPaginaReceitasRealizado" class="java.lang.String"/>
	<parameter name="totalPrevistoRealizadoFimPaginaDespesasPrevisto" class="java.lang.String"/>
	<parameter name="totalPrevistoRealizadoFimPaginaDespesasRealizado" class="java.lang.String"/>
	<parameter name="totalPrevistoRealizadoFimPaginaInvestimentosPrevisto" class="java.lang.String"/>
	<parameter name="totalPrevistoRealizadoFimPaginaInvestimentosRealizado" class="java.lang.String"/>
	<parameter name="totalPrevistoRealizadoFimPaginaInvestimentosDespesasPrevisto" class="java.lang.String"/>
	<parameter name="totalPrevistoRealizadoFimPaginaInvestimentosDespesasRealizado" class="java.lang.String"/>
	<parameter name="totalPrevistoRealizadoFimPaginaSemInvestimentosPrevisto" class="java.lang.String"/>
	<parameter name="totalPrevistoRealizadoFimPaginaSemInvestimentosRealizado" class="java.lang.String"/>
	<parameter name="totalPrevistoRealizadoFimPaginaComInvestimentosPrevisto" class="java.lang.String"/>
	<parameter name="totalPrevistoRealizadoFimPaginaComInvestimentosRealizado" class="java.lang.String"/>
	<parameter name="saldoFimPaginaReceitas" class="java.lang.String"/>
	<parameter name="saldoFimPaginaDespesas" class="java.lang.String"/>
	<parameter name="saldoFimPaginaInvestimentos" class="java.lang.String"/>
	<parameter name="saldoFimPaginaInvestimentosDespesas" class="java.lang.String"/>
	<parameter name="saldoFimPaginaSemInvestimentos" class="java.lang.String"/>
	<parameter name="saldoFimPaginaComInvestimentos" class="java.lang.String"/>
	<parameter name="variacaoFimPaginaReceitas" class="java.lang.String"/>
	<parameter name="variacaoFimPaginaDespesas" class="java.lang.String"/>
	<parameter name="variacaoFimPaginaInvestimentos" class="java.lang.String"/>
	<parameter name="variacaoFimPaginaInvestimentosDespesas" class="java.lang.String"/>
	<parameter name="variacaoFimPaginaSemInvestimentos" class="java.lang.String"/>
	<parameter name="variacaoFimPaginaComInvestimentos" class="java.lang.String"/>
	<field name="codigoAgrupador" class="java.lang.String"/>
	<field name="nomeAgrupador" class="java.lang.String"/>
	<field name="mes1.totalPrevistoMesString" class="java.lang.String"/>
	<field name="mes1.totalRealizadoMesString" class="java.lang.String"/>
	<field name="totalPrevistoString" class="java.lang.String"/>
	<field name="totalRealizadoString" class="java.lang.String"/>
	<field name="saldoFinalString" class="java.lang.String"/>
	<field name="percPretendidoString" class="java.lang.String"/>
	<field name="mes2.totalPrevistoMesString" class="java.lang.String"/>
	<field name="mes2.totalRealizadoMesString" class="java.lang.String"/>
	<field name="mes3.totalPrevistoMesString" class="java.lang.String"/>
	<field name="mes3.totalRealizadoMesString" class="java.lang.String"/>
	<field name="nivelArvore" class="java.lang.Integer"/>
	<background>
		<band splitType="Stretch"/>
	</background>
	<pageHeader>
		<band height="76" splitType="Stretch">
			<image isUsingCache="true" onErrorType="Blank" evaluationTime="Page">
				<reportElement x="2" y="1" width="82" height="36"/>
				<imageExpression class="java.io.InputStream"><![CDATA[$P{logoPadraoRelatorio}]]></imageExpression>
			</image>
			<textField>
				<reportElement x="84" y="1" width="718" height="36"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="14" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{tituloRelatorio}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="170" y="37" width="40" height="15"/>
				<textElement verticalAlignment="Middle">
					<font size="7" isBold="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{data1}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="776" y="37" width="27" height="15"/>
				<textElement verticalAlignment="Middle">
					<font size="7" isBold="true"/>
				</textElement>
				<text><![CDATA[Variaç.]]></text>
			</staticText>
			<staticText>
				<reportElement x="669" y="37" width="27" height="15"/>
				<textElement verticalAlignment="Middle">
					<font size="7" isBold="true"/>
				</textElement>
				<text><![CDATA[Total]]></text>
			</staticText>
			<staticText>
				<reportElement x="736" y="37" width="25" height="15"/>
				<textElement verticalAlignment="Middle">
					<font size="7" isBold="true"/>
				</textElement>
				<text><![CDATA[Saldo]]></text>
			</staticText>
			<textField>
				<reportElement x="346" y="37" width="40" height="15"/>
				<textElement verticalAlignment="Middle">
					<font size="7" isBold="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{data2}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="530" y="37" width="40" height="15"/>
				<textElement verticalAlignment="Middle">
					<font size="7" isBold="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{data3}]]></textFieldExpression>
			</textField>
			<rectangle>
				<reportElement x="94" y="51" width="90" height="25" forecolor="#999999" backcolor="#FFFFFF"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="0" y="51" width="94" height="25" forecolor="#999999" backcolor="#FFFFFF"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="184" y="51" width="90" height="25" forecolor="#999999" backcolor="#FFFFFF"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="274" y="51" width="90" height="25" forecolor="#999999" backcolor="#FFFFFF"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="364" y="51" width="90" height="25" forecolor="#999999" backcolor="#FFFFFF"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="454" y="51" width="90" height="25" forecolor="#999999" backcolor="#FFFFFF"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="544" y="51" width="90" height="25" forecolor="#999999" backcolor="#FFFFFF"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="634" y="51" width="45" height="25" forecolor="#999999" backcolor="#FFFFFF"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="679" y="51" width="45" height="25" forecolor="#999999" backcolor="#FFFFFF"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="724" y="51" width="45" height="25" forecolor="#999999" backcolor="#FFFFFF"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="769" y="51" width="33" height="25" forecolor="#999999" backcolor="#FFFFFF"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<staticText>
				<reportElement x="96" y="54" width="86" height="20"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="7" isBold="true"/>
				</textElement>
				<text><![CDATA[Previsto]]></text>
			</staticText>
			<staticText>
				<reportElement x="186" y="54" width="86" height="20"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="7" isBold="true"/>
				</textElement>
				<text><![CDATA[Realizado]]></text>
			</staticText>
			<staticText>
				<reportElement x="276" y="54" width="86" height="20"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="7" isBold="true"/>
				</textElement>
				<text><![CDATA[Previsto]]></text>
			</staticText>
			<staticText>
				<reportElement x="366" y="54" width="86" height="20"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="7" isBold="true"/>
				</textElement>
				<text><![CDATA[Realizado]]></text>
			</staticText>
			<staticText>
				<reportElement x="456" y="54" width="86" height="20"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="7" isBold="true"/>
				</textElement>
				<text><![CDATA[Previsto]]></text>
			</staticText>
			<staticText>
				<reportElement x="546" y="54" width="86" height="20"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="7" isBold="true"/>
				</textElement>
				<text><![CDATA[Realizado]]></text>
			</staticText>
			<staticText>
				<reportElement x="636" y="54" width="40" height="20"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="7" isBold="true"/>
				</textElement>
				<text><![CDATA[Previsto]]></text>
			</staticText>
			<staticText>
				<reportElement x="681" y="54" width="40" height="20"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="7" isBold="true"/>
				</textElement>
				<text><![CDATA[Realizado]]></text>
			</staticText>
			<staticText>
				<reportElement x="2" y="54" width="90" height="20"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="7" isBold="true"/>
				</textElement>
				<text><![CDATA[Plano de Contas]]></text>
			</staticText>
		</band>
	</pageHeader>
	<detail>
		<band height="12" splitType="Stretch">
			<textField>
				<reportElement style="corFundoRetangulo" x="0" y="0" width="801" height="12"/>
				<textElement/>
				<textFieldExpression class="java.lang.Integer"><![CDATA[$F{nivelArvore}]]></textFieldExpression>
			</textField>
			<rectangle>
				<reportElement mode="Transparent" x="769" y="0" width="33" height="12" forecolor="#999999" backcolor="#999999"/>
				<graphicElement>
					<pen lineStyle="Solid" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement mode="Transparent" x="0" y="0" width="94" height="12" forecolor="#999999" backcolor="#999999"/>
				<graphicElement>
					<pen lineStyle="Solid" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement mode="Transparent" x="724" y="0" width="45" height="12" forecolor="#999999" backcolor="#999999"/>
				<graphicElement>
					<pen lineStyle="Solid" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement mode="Transparent" x="454" y="0" width="90" height="12" forecolor="#999999" backcolor="#999999"/>
				<graphicElement>
					<pen lineStyle="Solid" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement mode="Transparent" x="634" y="0" width="45" height="12" forecolor="#999999" backcolor="#999999"/>
				<graphicElement>
					<pen lineStyle="Solid" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement mode="Transparent" x="679" y="0" width="45" height="12" forecolor="#999999" backcolor="#999999"/>
				<graphicElement>
					<pen lineStyle="Solid" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement mode="Transparent" x="544" y="0" width="90" height="12" forecolor="#999999" backcolor="#999999"/>
				<graphicElement>
					<pen lineStyle="Solid" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement mode="Transparent" x="364" y="0" width="90" height="12" forecolor="#999999" backcolor="#999999"/>
				<graphicElement>
					<pen lineStyle="Solid" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement mode="Transparent" x="274" y="0" width="90" height="12" forecolor="#999999" backcolor="#999999"/>
				<graphicElement>
					<pen lineStyle="Solid" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement mode="Transparent" x="184" y="0" width="90" height="12" forecolor="#999999" backcolor="#999999"/>
				<graphicElement>
					<pen lineStyle="Solid" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement mode="Transparent" x="94" y="0" width="90" height="12" forecolor="#999999" backcolor="#999999"/>
				<graphicElement>
					<pen lineStyle="Solid" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<textField>
				<reportElement x="2" y="1" width="90" height="10"/>
				<textElement verticalAlignment="Middle">
					<font size="6"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{nomeAgrupador}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="95" y="1" width="86" height="10"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="6"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{mes1.totalPrevistoMesString}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="185" y="1" width="86" height="10"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="6"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{mes1.totalRealizadoMesString}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="275" y="1" width="88" height="10"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="6"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{mes2.totalPrevistoMesString}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="365" y="1" width="88" height="10"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="6"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{mes2.totalRealizadoMesString}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="455" y="1" width="88" height="10"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="6"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{mes3.totalPrevistoMesString}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="545" y="1" width="88" height="10"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="6"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{mes3.totalRealizadoMesString}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="635" y="1" width="42" height="10"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="6"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{totalPrevistoString}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="680" y="1" width="42" height="10"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="6"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{totalRealizadoString}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="725" y="1" width="42" height="10"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="6"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{saldoFinalString}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="770" y="1" width="31" height="10"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="6"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{percPretendidoString}]]></textFieldExpression>
			</textField>
		</band>
	</detail>
	<summary>
		<band height="120">
			<rectangle>
				<reportElement x="724" y="24" width="45" height="12" forecolor="#999999" backcolor="#CCCCCC"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="544" y="24" width="90" height="12" forecolor="#999999" backcolor="#CCCCCC"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="94" y="24" width="90" height="12" forecolor="#999999" backcolor="#CCCCCC"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="634" y="24" width="45" height="12" forecolor="#999999" backcolor="#CCCCCC"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="274" y="24" width="90" height="12" forecolor="#999999" backcolor="#CCCCCC"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="184" y="24" width="90" height="12" forecolor="#999999" backcolor="#CCCCCC"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="769" y="24" width="33" height="12" forecolor="#999999" backcolor="#CCCCCC"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="364" y="24" width="90" height="12" forecolor="#999999" backcolor="#CCCCCC"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="679" y="24" width="45" height="12" forecolor="#999999" backcolor="#CCCCCC"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="0" y="24" width="94" height="12" forecolor="#999999" backcolor="#CCCCCC"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="454" y="24" width="90" height="12" forecolor="#999999" backcolor="#CCCCCC"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<staticText>
				<reportElement x="186" y="25" width="86" height="10"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="7" isBold="true"/>
				</textElement>
				<text><![CDATA[Realizado]]></text>
			</staticText>
			<staticText>
				<reportElement x="96" y="25" width="86" height="10"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="7" isBold="true"/>
				</textElement>
				<text><![CDATA[Previsto]]></text>
			</staticText>
			<staticText>
				<reportElement x="669" y="7" width="27" height="15"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="7" isBold="true"/>
				</textElement>
				<text><![CDATA[Total]]></text>
			</staticText>
			<textField>
				<reportElement x="164" y="7" width="40" height="15"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="7" isBold="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{data1}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="736" y="7" width="25" height="15"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="7" isBold="true"/>
				</textElement>
				<text><![CDATA[Saldo]]></text>
			</staticText>
			<staticText>
				<reportElement x="456" y="25" width="86" height="10"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="7" isBold="true"/>
				</textElement>
				<text><![CDATA[Previsto]]></text>
			</staticText>
			<staticText>
				<reportElement x="773" y="7" width="27" height="15"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="7" isBold="true"/>
				</textElement>
				<text><![CDATA[Variaç.]]></text>
			</staticText>
			<staticText>
				<reportElement x="636" y="25" width="40" height="10"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="7" isBold="true"/>
				</textElement>
				<text><![CDATA[Previsto]]></text>
			</staticText>
			<textField>
				<reportElement x="344" y="7" width="40" height="15"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="7" isBold="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{data2}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="366" y="25" width="86" height="10"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="7" isBold="true"/>
				</textElement>
				<text><![CDATA[Realizado]]></text>
			</staticText>
			<staticText>
				<reportElement x="276" y="25" width="86" height="10"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="7" isBold="true"/>
				</textElement>
				<text><![CDATA[Previsto]]></text>
			</staticText>
			<staticText>
				<reportElement x="546" y="25" width="88" height="10"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="7" isBold="true"/>
				</textElement>
				<text><![CDATA[Realizado]]></text>
			</staticText>
			<textField>
				<reportElement x="525" y="7" width="40" height="15"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="7" isBold="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{data3}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="2" y="25" width="90" height="10"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="7" isBold="true"/>
				</textElement>
				<text><![CDATA[Resumo Geral]]></text>
			</staticText>
			<staticText>
				<reportElement x="681" y="25" width="40" height="10"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="7" isBold="true"/>
				</textElement>
				<text><![CDATA[Realizado]]></text>
			</staticText>
			<rectangle>
				<reportElement x="724" y="36" width="45" height="12" forecolor="#999999" backcolor="#FFFFFF"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="544" y="36" width="90" height="12" forecolor="#999999" backcolor="#FFFFFF"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="94" y="36" width="90" height="12" forecolor="#999999" backcolor="#FFFFFF"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="634" y="36" width="45" height="12" forecolor="#999999" backcolor="#FFFFFF"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="274" y="36" width="90" height="12" forecolor="#999999" backcolor="#FFFFFF"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="184" y="36" width="90" height="12" forecolor="#999999" backcolor="#FFFFFF"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="769" y="36" width="33" height="12" forecolor="#999999" backcolor="#FFFFFF"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="364" y="36" width="90" height="12" forecolor="#999999" backcolor="#FFFFFF"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="679" y="36" width="45" height="12" forecolor="#999999" backcolor="#FFFFFF"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="0" y="36" width="94" height="12" forecolor="#999999" backcolor="#FFFFFF"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="454" y="36" width="90" height="12" forecolor="#999999" backcolor="#FFFFFF"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="769" y="47" width="33" height="12" forecolor="#999999" backcolor="#FFFFFF"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="634" y="47" width="45" height="12" forecolor="#999999" backcolor="#FFFFFF"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="544" y="47" width="90" height="12" forecolor="#999999" backcolor="#FFFFFF"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="454" y="47" width="90" height="12" forecolor="#999999" backcolor="#FFFFFF"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="184" y="47" width="90" height="12" forecolor="#999999" backcolor="#FFFFFF"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="274" y="47" width="90" height="12" forecolor="#999999" backcolor="#FFFFFF"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="679" y="47" width="45" height="12" forecolor="#999999" backcolor="#FFFFFF"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="94" y="47" width="90" height="12" forecolor="#999999" backcolor="#FFFFFF"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="364" y="47" width="90" height="12" forecolor="#999999" backcolor="#FFFFFF"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="0" y="47" width="94" height="12" forecolor="#999999" backcolor="#FFFFFF"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="724" y="47" width="45" height="12" forecolor="#999999" backcolor="#FFFFFF"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="769" y="59" width="33" height="12" forecolor="#999999" backcolor="#FFFFFF"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="634" y="59" width="45" height="12" forecolor="#999999" backcolor="#FFFFFF"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="544" y="59" width="90" height="12" forecolor="#999999" backcolor="#FFFFFF"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="454" y="59" width="90" height="12" forecolor="#999999" backcolor="#FFFFFF"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="184" y="59" width="90" height="12" forecolor="#999999" backcolor="#FFFFFF"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="274" y="59" width="90" height="12" forecolor="#999999" backcolor="#FFFFFF"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="679" y="59" width="45" height="12" forecolor="#999999" backcolor="#FFFFFF"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="94" y="59" width="90" height="12" forecolor="#999999" backcolor="#FFFFFF"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="364" y="59" width="90" height="12" forecolor="#999999" backcolor="#FFFFFF"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="0" y="59" width="94" height="12" forecolor="#999999" backcolor="#FFFFFF"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="724" y="59" width="45" height="12" forecolor="#999999" backcolor="#FFFFFF"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="769" y="71" width="33" height="12" forecolor="#999999" backcolor="#FFFFFF"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="634" y="71" width="45" height="12" forecolor="#999999" backcolor="#FFFFFF"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="544" y="71" width="90" height="12" forecolor="#999999" backcolor="#FFFFFF"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="454" y="71" width="90" height="12" forecolor="#999999" backcolor="#FFFFFF"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="184" y="71" width="90" height="12" forecolor="#999999" backcolor="#FFFFFF"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="274" y="71" width="90" height="12" forecolor="#999999" backcolor="#FFFFFF"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="679" y="71" width="45" height="12" forecolor="#999999" backcolor="#FFFFFF"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="94" y="71" width="90" height="12" forecolor="#999999" backcolor="#FFFFFF"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="364" y="71" width="90" height="12" forecolor="#999999" backcolor="#FFFFFF"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="0" y="71" width="94" height="12" forecolor="#999999" backcolor="#FFFFFF"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="724" y="71" width="45" height="12" forecolor="#999999" backcolor="#FFFFFF"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="769" y="83" width="33" height="12" forecolor="#999999" backcolor="#CCFFCC"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="634" y="83" width="45" height="12" forecolor="#999999" backcolor="#CCFFCC"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="544" y="83" width="90" height="12" forecolor="#999999" backcolor="#CCFFCC"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="454" y="83" width="90" height="12" forecolor="#999999" backcolor="#CCFFCC"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="184" y="83" width="90" height="12" forecolor="#999999" backcolor="#CCFFCC"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="274" y="83" width="90" height="12" forecolor="#999999" backcolor="#CCFFCC"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="679" y="83" width="45" height="12" forecolor="#999999" backcolor="#CCFFCC"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="94" y="83" width="90" height="12" forecolor="#999999" backcolor="#CCFFCC"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="364" y="83" width="90" height="12" forecolor="#999999" backcolor="#CCFFCC"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="0" y="83" width="94" height="12" forecolor="#999999" backcolor="#CCFFCC"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="724" y="83" width="45" height="12" forecolor="#999999" backcolor="#CCFFCC"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="544" y="95" width="90" height="12" forecolor="#999999" backcolor="#CCFFCC"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="94" y="95" width="90" height="12" forecolor="#999999" backcolor="#CCFFCC"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="724" y="95" width="45" height="12" forecolor="#999999" backcolor="#CCFFCC"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="454" y="95" width="90" height="12" forecolor="#999999" backcolor="#CCFFCC"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="769" y="95" width="33" height="12" forecolor="#999999" backcolor="#CCFFCC"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="634" y="95" width="45" height="12" forecolor="#999999" backcolor="#CCFFCC"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="364" y="95" width="90" height="12" forecolor="#999999" backcolor="#CCFFCC"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="679" y="95" width="45" height="12" forecolor="#999999" backcolor="#CCFFCC"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="184" y="95" width="90" height="12" forecolor="#999999" backcolor="#CCFFCC"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="274" y="95" width="90" height="12" forecolor="#999999" backcolor="#CCFFCC"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="0" y="95" width="94" height="12" forecolor="#999999" backcolor="#CCFFCC"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="544" y="107" width="90" height="12" forecolor="#999999" backcolor="#CCFFCC"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="94" y="107" width="90" height="12" forecolor="#999999" backcolor="#CCFFCC"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="724" y="107" width="45" height="12" forecolor="#999999" backcolor="#CCFFCC"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="454" y="107" width="90" height="12" forecolor="#999999" backcolor="#CCFFCC"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="769" y="107" width="33" height="12" forecolor="#999999" backcolor="#CCFFCC"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="634" y="107" width="45" height="12" forecolor="#999999" backcolor="#CCFFCC"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="364" y="107" width="90" height="12" forecolor="#999999" backcolor="#CCFFCC"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="679" y="107" width="45" height="12" forecolor="#999999" backcolor="#CCFFCC"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="184" y="107" width="90" height="12" forecolor="#999999" backcolor="#CCFFCC"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="274" y="107" width="90" height="12" forecolor="#999999" backcolor="#CCFFCC"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="0" y="107" width="94" height="12" forecolor="#999999" backcolor="#CCFFCC"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<staticText>
				<reportElement x="2" y="84" width="90" height="9"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="7" isBold="true"/>
				</textElement>
				<text><![CDATA[Resultado Econômico]]></text>
			</staticText>
			<textField>
				<reportElement x="185" y="36" width="88" height="10"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="6"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{mes1TotalFimPaginaReceitasRealizado}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="95" y="36" width="88" height="10"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="6"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{mes1TotalFimPaginaReceitasPrevisto}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="4" y="36" width="59" height="10"/>
				<textElement verticalAlignment="Middle">
					<font size="7"/>
				</textElement>
				<text><![CDATA[Receitas]]></text>
			</staticText>
			<staticText>
				<reportElement x="4" y="48" width="59" height="10"/>
				<textElement verticalAlignment="Middle">
					<font size="7"/>
				</textElement>
				<text><![CDATA[Despesas]]></text>
			</staticText>
			<staticText>
				<reportElement x="4" y="60" width="59" height="10"/>
				<textElement verticalAlignment="Middle">
					<font size="7"/>
				</textElement>
				<text><![CDATA[Investimentos]]></text>
			</staticText>
			<staticText>
				<reportElement x="4" y="72" width="88" height="10"/>
				<textElement verticalAlignment="Middle">
					<font size="7"/>
				</textElement>
				<text><![CDATA[Investimentos + Despesas]]></text>
			</staticText>
			<staticText>
				<reportElement x="4" y="96" width="88" height="10"/>
				<textElement verticalAlignment="Middle">
					<font size="7"/>
				</textElement>
				<text><![CDATA[Sem Investimentos]]></text>
			</staticText>
			<staticText>
				<reportElement x="4" y="108" width="88" height="10"/>
				<textElement verticalAlignment="Middle">
					<font size="7"/>
				</textElement>
				<text><![CDATA[Com Investimentos]]></text>
			</staticText>
			<textField>
				<reportElement x="95" y="48" width="88" height="10"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="6"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{mes1TotalFimPaginaDespesasPrevisto}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="185" y="48" width="88" height="10"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="6"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{mes1TotalFimPaginaDespesasRealizado}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="95" y="60" width="88" height="10"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="6"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{mes1TotalFimPaginaInvestimentosPrevisto}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="185" y="60" width="88" height="10"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="6"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{mes1TotalFimPaginaInvestimentosRealizado}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="95" y="72" width="88" height="10"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="6"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{mes1TotalFimPaginaInvestimentosDespesasPrevisto}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="185" y="72" width="88" height="10"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="6"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{mes1TotalFimPaginaInvestimentosDespesasRealizado}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="95" y="96" width="88" height="10"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="6"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{mes1TotalFimPaginaSemInvestimentosPrevisto}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="185" y="96" width="88" height="10"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="6"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{mes1TotalFimPaginaSemInvestimentosRealizado}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="95" y="108" width="88" height="10"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="6"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{mes1TotalFimPaginaComInvestimentosPrevisto}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="185" y="108" width="88" height="10"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="6"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{mes1TotalFimPaginaComInvestimentosRealizado}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="275" y="36" width="88" height="10"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="6"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{mes2TotalFimPaginaReceitasPrevisto}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="365" y="36" width="88" height="10"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="6"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{mes2TotalFimPaginaReceitasRealizado}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="275" y="48" width="88" height="10"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="6"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{mes2TotalFimPaginaDespesasPrevisto}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="365" y="48" width="88" height="10"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="6"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{mes2TotalFimPaginaDespesasRealizado}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="275" y="60" width="88" height="10"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="6"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{mes2TotalFimPaginaInvestimentosPrevisto}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="365" y="60" width="88" height="10"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="6"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{mes2TotalFimPaginaInvestimentosRealizado}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="275" y="72" width="88" height="10"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="6"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{mes2TotalFimPaginaInvestimentosDespesasPrevisto}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="365" y="72" width="88" height="10"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="6"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{mes2TotalFimPaginaInvestimentosDespesasRealizado}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="275" y="96" width="88" height="10"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="6"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{mes2TotalFimPaginaSemInvestimentosPrevisto}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="365" y="96" width="88" height="10"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="6"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{mes2TotalFimPaginaSemInvestimentosRealizado}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="275" y="108" width="88" height="10"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="6"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{mes2TotalFimPaginaComInvestimentosPrevisto}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="365" y="108" width="88" height="10"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="6"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{mes2TotalFimPaginaComInvestimentosRealizado}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="455" y="36" width="88" height="10"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="6"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{mes3TotalFimPaginaReceitasPrevisto}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="545" y="36" width="88" height="10"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="6"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{mes3TotalFimPaginaReceitasRealizado}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="455" y="48" width="88" height="10"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="6"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{mes3TotalFimPaginaDespesasPrevisto}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="545" y="48" width="88" height="10"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="6"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{mes3TotalFimPaginaDespesasRealizado}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="455" y="60" width="88" height="10"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="6"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{mes3TotalFimPaginaInvestimentosPrevisto}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="545" y="60" width="88" height="10"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="6"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{mes3TotalFimPaginaInvestimentosRealizado}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="455" y="72" width="88" height="10"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="6"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{mes3TotalFimPaginaInvestimentosDespesasPrevisto}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="545" y="72" width="88" height="10"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="6"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{mes3TotalFimPaginaInvestimentosDespesasRealizado}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="455" y="96" width="88" height="10"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="6"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{mes3TotalFimPaginaSemInvestimentosPrevisto}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="545" y="96" width="88" height="10"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="6"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{mes3TotalFimPaginaSemInvestimentosRealizado}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="455" y="108" width="88" height="10"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="6"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{mes3TotalFimPaginaComInvestimentosPrevisto}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="545" y="108" width="88" height="10"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="6"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{mes3TotalFimPaginaComInvestimentosRealizado}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="635" y="36" width="42" height="10"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="6"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{totalPrevistoRealizadoFimPaginaReceitasPrevisto}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="680" y="36" width="42" height="10"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="6"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{totalPrevistoRealizadoFimPaginaReceitasRealizado}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="635" y="48" width="42" height="10"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="6"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{totalPrevistoRealizadoFimPaginaDespesasPrevisto}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="680" y="48" width="42" height="10"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="6"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{totalPrevistoRealizadoFimPaginaDespesasRealizado}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="635" y="60" width="42" height="10"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="6"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{totalPrevistoRealizadoFimPaginaInvestimentosPrevisto}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="680" y="60" width="42" height="10"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="6"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{totalPrevistoRealizadoFimPaginaInvestimentosRealizado}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="635" y="72" width="42" height="10"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="6"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{totalPrevistoRealizadoFimPaginaInvestimentosDespesasPrevisto}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="680" y="72" width="42" height="10"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="6"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{totalPrevistoRealizadoFimPaginaInvestimentosDespesasRealizado}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="635" y="96" width="42" height="10"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="6"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{totalPrevistoRealizadoFimPaginaSemInvestimentosPrevisto}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="680" y="96" width="42" height="10"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="6"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{totalPrevistoRealizadoFimPaginaSemInvestimentosRealizado}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="635" y="108" width="42" height="10"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="6"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{totalPrevistoRealizadoFimPaginaComInvestimentosPrevisto}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="680" y="108" width="42" height="10"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="6"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{totalPrevistoRealizadoFimPaginaComInvestimentosRealizado}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="725" y="36" width="42" height="10"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="6"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{saldoFimPaginaReceitas}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="725" y="48" width="42" height="10"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="6"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{saldoFimPaginaDespesas}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="725" y="60" width="42" height="10"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="6"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{saldoFimPaginaInvestimentos}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="725" y="72" width="42" height="10"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="6"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{saldoFimPaginaInvestimentosDespesas}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="725" y="96" width="42" height="10"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="6"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{saldoFimPaginaSemInvestimentos}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="725" y="108" width="42" height="10"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="6"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{saldoFimPaginaComInvestimentos}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="770" y="36" width="30" height="10"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="6"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{variacaoFimPaginaReceitas}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="770" y="48" width="30" height="10"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="6"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{variacaoFimPaginaDespesas}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="770" y="60" width="30" height="10"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="6"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{variacaoFimPaginaInvestimentos}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="770" y="72" width="30" height="10"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="6"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{variacaoFimPaginaInvestimentosDespesas}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="770" y="96" width="30" height="10"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="6"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{variacaoFimPaginaSemInvestimentos}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="770" y="108" width="30" height="10"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="6"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{variacaoFimPaginaComInvestimentos}]]></textFieldExpression>
			</textField>
		</band>
	</summary>
</jasperReport>
