¬í sr (net.sf.jasperreports.engine.JasperReport      'Ø L compileDatat Ljava/io/Serializable;L compileNameSuffixt Ljava/lang/String;L 
compilerClassq ~ xr -net.sf.jasperreports.engine.base.JRBaseReport      'Ø +I PSEUDO_SERIAL_VERSION_UIDI bottomMarginI columnCountI 
columnSpacingI columnWidthZ ignorePaginationZ isFloatColumnFooterZ isSummaryNewPageZ  isSummaryWithPageHeaderAndFooterZ isTitleNewPageI 
leftMarginB orientationI 
pageHeightI 	pageWidthB 
printOrderI rightMarginI 	topMarginB whenNoDataTypeL 
backgroundt $Lnet/sf/jasperreports/engine/JRBand;L columnFooterq ~ L columnHeaderq ~ [ datasetst ([Lnet/sf/jasperreports/engine/JRDataset;L defaultFontt *Lnet/sf/jasperreports/engine/JRReportFont;L defaultStylet %Lnet/sf/jasperreports/engine/JRStyle;L detailq ~ L 
detailSectiont 'Lnet/sf/jasperreports/engine/JRSection;[ fontst +[Lnet/sf/jasperreports/engine/JRReportFont;L formatFactoryClassq ~ L 
importsSett Ljava/util/Set;L languageq ~ L lastPageFooterq ~ L mainDatasett 'Lnet/sf/jasperreports/engine/JRDataset;L nameq ~ L noDataq ~ L orientationValuet 2Lnet/sf/jasperreports/engine/type/OrientationEnum;L 
pageFooterq ~ L 
pageHeaderq ~ L printOrderValuet 1Lnet/sf/jasperreports/engine/type/PrintOrderEnum;[ stylest &[Lnet/sf/jasperreports/engine/JRStyle;L summaryq ~ [ 	templatest /[Lnet/sf/jasperreports/engine/JRReportTemplate;L titleq ~ L whenNoDataTypeValuet 5Lnet/sf/jasperreports/engine/type/WhenNoDataTypeEnum;xp  wî            q           n  ¨    $    sr +net.sf.jasperreports.engine.base.JRBaseBand      'Ø I PSEUDO_SERIAL_VERSION_UIDI heightZ isSplitAllowedL printWhenExpressiont *Lnet/sf/jasperreports/engine/JRExpression;L 	splitTypet Ljava/lang/Byte;L splitTypeValuet 0Lnet/sf/jasperreports/engine/type/SplitTypeEnum;xr 3net.sf.jasperreports.engine.base.JRBaseElementGroup      'Ø L childrent Ljava/util/List;L elementGroupt ,Lnet/sf/jasperreports/engine/JRElementGroup;xpsr java.util.ArrayListxÒÇa I sizexp    w    xp  wî    pp~r .net.sf.jasperreports.engine.type.SplitTypeEnum          xr java.lang.Enum          xpt STRETCHsq ~ sq ~     w    xp  wî    ppq ~ sq ~ sq ~     w    xp  wî    ppq ~ ur ([Lnet.sf.jasperreports.engine.JRDataset;L6Í¬D  xp   sr .net.sf.jasperreports.engine.base.JRBaseDataset      'Ø I PSEUDO_SERIAL_VERSION_UIDZ isMainB whenResourceMissingType[ fieldst &[Lnet/sf/jasperreports/engine/JRField;L filterExpressionq ~ [ groupst &[Lnet/sf/jasperreports/engine/JRGroup;L nameq ~ [ 
parameterst *[Lnet/sf/jasperreports/engine/JRParameter;L 
propertiesMapt -Lnet/sf/jasperreports/engine/JRPropertiesMap;L queryt %Lnet/sf/jasperreports/engine/JRQuery;L resourceBundleq ~ L scriptletClassq ~ [ 
scriptletst *[Lnet/sf/jasperreports/engine/JRScriptlet;[ 
sortFieldst *[Lnet/sf/jasperreports/engine/JRSortField;[ 	variablest )[Lnet/sf/jasperreports/engine/JRVariable;L whenResourceMissingTypeValuet >Lnet/sf/jasperreports/engine/type/WhenResourceMissingTypeEnum;xp  wî  pppt Testeur *[Lnet.sf.jasperreports.engine.JRParameter;" *Ã`!  xp   sr 0net.sf.jasperreports.engine.base.JRBaseParameter      'Ø 	Z isForPromptingZ isSystemDefinedL defaultValueExpressionq ~ L descriptionq ~ L nameq ~ L nestedTypeNameq ~ L 
propertiesMapq ~ *L valueClassNameq ~ L valueClassRealNameq ~ xpppt REPORT_PARAMETERS_MAPpsr +net.sf.jasperreports.engine.JRPropertiesMap      'Ø L baseq ~ *L propertiesListq ~ L 
propertiesMapt Ljava/util/Map;xppppt 
java.util.Mappsq ~ 4ppt 
JASPER_REPORTpsq ~ 7pppt (net.sf.jasperreports.engine.JasperReportpsq ~ 4ppt REPORT_CONNECTIONpsq ~ 7pppt java.sql.Connectionpsq ~ 4ppt REPORT_MAX_COUNTpsq ~ 7pppt java.lang.Integerpsq ~ 4ppt REPORT_DATA_SOURCEpsq ~ 7pppt (net.sf.jasperreports.engine.JRDataSourcepsq ~ 4ppt REPORT_SCRIPTLETpsq ~ 7pppt /net.sf.jasperreports.engine.JRAbstractScriptletpsq ~ 4ppt 
REPORT_LOCALEpsq ~ 7pppt java.util.Localepsq ~ 4ppt REPORT_RESOURCE_BUNDLEpsq ~ 7pppt java.util.ResourceBundlepsq ~ 4ppt REPORT_TIME_ZONEpsq ~ 7pppt java.util.TimeZonepsq ~ 4ppt REPORT_FORMAT_FACTORYpsq ~ 7pppt .net.sf.jasperreports.engine.util.FormatFactorypsq ~ 4ppt REPORT_CLASS_LOADERpsq ~ 7pppt java.lang.ClassLoaderpsq ~ 4ppt REPORT_URL_HANDLER_FACTORYpsq ~ 7pppt  java.net.URLStreamHandlerFactorypsq ~ 4ppt REPORT_FILE_RESOLVERpsq ~ 7pppt -net.sf.jasperreports.engine.util.FileResolverpsq ~ 4ppt REPORT_TEMPLATESpsq ~ 7pppt java.util.Collectionpsq ~ 7ppppppppur )[Lnet.sf.jasperreports.engine.JRVariable;bæ|,·D  xp   sr /net.sf.jasperreports.engine.base.JRBaseVariable      'Ø I PSEUDO_SERIAL_VERSION_UIDB calculationB 
incrementTypeZ isSystemDefinedB 	resetTypeL calculationValuet 2Lnet/sf/jasperreports/engine/type/CalculationEnum;L 
expressionq ~ L incrementGroupt %Lnet/sf/jasperreports/engine/JRGroup;L incrementTypeValuet 4Lnet/sf/jasperreports/engine/type/IncrementTypeEnum;L incrementerFactoryClassNameq ~ L incrementerFactoryClassRealNameq ~ L initialValueExpressionq ~ L nameq ~ L 
resetGroupq ~ tL resetTypeValuet 0Lnet/sf/jasperreports/engine/type/ResetTypeEnum;L valueClassNameq ~ L valueClassRealNameq ~ xp  wî   ~r 0net.sf.jasperreports.engine.type.CalculationEnum          xq ~ t SYSTEMpp~r 2net.sf.jasperreports.engine.type.IncrementTypeEnum          xq ~ t NONEppsr 1net.sf.jasperreports.engine.base.JRBaseExpression      'Ø I id[ chunkst 0[Lnet/sf/jasperreports/engine/JRExpressionChunk;L valueClassNameq ~ L valueClassRealNameq ~ xp    ur 0[Lnet.sf.jasperreports.engine.JRExpressionChunk;mYÏÞiK£U  xp   sr 6net.sf.jasperreports.engine.base.JRBaseExpressionChunk      'Ø B typeL textq ~ xpt new java.lang.Integer(1)q ~ Fpt PAGE_NUMBERp~r .net.sf.jasperreports.engine.type.ResetTypeEnum          xq ~ t REPORTq ~ Fpsq ~ r  wî   q ~ yppq ~ |ppsq ~ ~   uq ~    sq ~ t new java.lang.Integer(1)q ~ Fpt 
COLUMN_NUMBERp~q ~ t PAGEq ~ Fpsq ~ r  wî   ~q ~ xt COUNTsq ~ ~   uq ~    sq ~ t new java.lang.Integer(1)q ~ Fppq ~ |ppsq ~ ~   uq ~    sq ~ t new java.lang.Integer(0)q ~ Fpt REPORT_COUNTpq ~ q ~ Fpsq ~ r  wî   q ~ sq ~ ~   uq ~    sq ~ t new java.lang.Integer(1)q ~ Fppq ~ |ppsq ~ ~   uq ~    sq ~ t new java.lang.Integer(0)q ~ Fpt 
PAGE_COUNTpq ~ q ~ Fpsq ~ r  wî   q ~ sq ~ ~   uq ~    sq ~ t new java.lang.Integer(1)q ~ Fppq ~ |ppsq ~ ~   uq ~    sq ~ t new java.lang.Integer(0)q ~ Fpt COLUMN_COUNTp~q ~ t COLUMNq ~ Fp~r <net.sf.jasperreports.engine.type.WhenResourceMissingTypeEnum          xq ~ t NULLpppsr .net.sf.jasperreports.engine.base.JRBaseSection      'Ø [ bandst %[Lnet/sf/jasperreports/engine/JRBand;xpur %[Lnet.sf.jasperreports.engine.JRBand;Ý~ìÊ5  xp   sq ~ sq ~    w   sr 1net.sf.jasperreports.engine.base.JRBaseStaticText      'Ø L textq ~ xr 2net.sf.jasperreports.engine.base.JRBaseTextElement      'Ø %I PSEUDO_SERIAL_VERSION_UIDL borderq ~ L borderColort Ljava/awt/Color;L bottomBorderq ~ L bottomBorderColorq ~ ÀL 
bottomPaddingt Ljava/lang/Integer;L fontNameq ~ L fontSizeq ~ ÁL horizontalAlignmentq ~ L horizontalAlignmentValuet 6Lnet/sf/jasperreports/engine/type/HorizontalAlignEnum;L isBoldt Ljava/lang/Boolean;L isItalicq ~ ÃL 
isPdfEmbeddedq ~ ÃL isStrikeThroughq ~ ÃL isStyledTextq ~ ÃL isUnderlineq ~ ÃL 
leftBorderq ~ L leftBorderColorq ~ ÀL leftPaddingq ~ ÁL lineBoxt 'Lnet/sf/jasperreports/engine/JRLineBox;L lineSpacingq ~ L lineSpacingValuet 2Lnet/sf/jasperreports/engine/type/LineSpacingEnum;L markupq ~ L paddingq ~ ÁL pdfEncodingq ~ L pdfFontNameq ~ L 
reportFontq ~ L rightBorderq ~ L rightBorderColorq ~ ÀL rightPaddingq ~ ÁL rotationq ~ L 
rotationValuet /Lnet/sf/jasperreports/engine/type/RotationEnum;L 	topBorderq ~ L topBorderColorq ~ ÀL 
topPaddingq ~ ÁL verticalAlignmentq ~ L verticalAlignmentValuet 4Lnet/sf/jasperreports/engine/type/VerticalAlignEnum;xr .net.sf.jasperreports.engine.base.JRBaseElement      'Ø I PSEUDO_SERIAL_VERSION_UIDI heightZ isPrintInFirstWholeBandZ isPrintRepeatedValuesZ isPrintWhenDetailOverflowsZ isRemoveLineWhenBlankB positionTypeB stretchTypeI widthI xI yL 	backcolorq ~ ÀL defaultStyleProvidert 4Lnet/sf/jasperreports/engine/JRDefaultStyleProvider;L elementGroupq ~ L 	forecolorq ~ ÀL keyq ~ L modeq ~ L 	modeValuet +Lnet/sf/jasperreports/engine/type/ModeEnum;L parentStyleq ~ L parentStyleNameReferenceq ~ L positionTypeValuet 3Lnet/sf/jasperreports/engine/type/PositionTypeEnum;L printWhenExpressionq ~ L printWhenGroupChangesq ~ tL 
propertiesMapq ~ *[ propertyExpressionst 3[Lnet/sf/jasperreports/engine/JRPropertyExpression;L stretchTypeValuet 2Lnet/sf/jasperreports/engine/type/StretchTypeEnum;xp  wî             C   pq ~ q ~ ¼pt staticText-101pppp~r 1net.sf.jasperreports.engine.type.PositionTypeEnum          xq ~ t FIX_RELATIVE_TO_TOPpppp~r 0net.sf.jasperreports.engine.type.StretchTypeEnum          xq ~ t 
NO_STRETCH  wîppppppsr java.lang.Integerâ ¤÷8 I valuexr java.lang.Number¬à  xp   ppsr java.lang.BooleanÍ rÕúî Z valuexpppppppppsr .net.sf.jasperreports.engine.base.JRBaseLineBox      'Ø L 
bottomPaddingq ~ ÁL 	bottomPent +Lnet/sf/jasperreports/engine/base/JRBoxPen;L boxContainert ,Lnet/sf/jasperreports/engine/JRBoxContainer;L leftPaddingq ~ ÁL leftPenq ~ ÜL paddingq ~ ÁL penq ~ ÜL rightPaddingq ~ ÁL rightPenq ~ ÜL 
topPaddingq ~ ÁL topPenq ~ Üxppsr 3net.sf.jasperreports.engine.base.JRBaseBoxBottomPen      'Ø  xr -net.sf.jasperreports.engine.base.JRBaseBoxPen      'Ø L lineBoxq ~ Äxr *net.sf.jasperreports.engine.base.JRBasePen      'Ø I PSEUDO_SERIAL_VERSION_UIDL 	lineColorq ~ ÀL 	lineStyleq ~ L lineStyleValuet 0Lnet/sf/jasperreports/engine/type/LineStyleEnum;L 	lineWidtht Ljava/lang/Float;L penContainert ,Lnet/sf/jasperreports/engine/JRPenContainer;xp  wîppppq ~ Þq ~ Þq ~ Îpsr 1net.sf.jasperreports.engine.base.JRBaseBoxLeftPen      'Ø  xq ~ à  wîppppq ~ Þq ~ Þpsq ~ à  wîppppq ~ Þq ~ Þpsr 2net.sf.jasperreports.engine.base.JRBaseBoxRightPen      'Ø  xq ~ à  wîppppq ~ Þq ~ Þpsr 0net.sf.jasperreports.engine.base.JRBaseBoxTopPen      'Ø  xq ~ à  wîppppq ~ Þq ~ Þpppppt Helvetica-Boldpppppppppppt 
Quantidadesq ~ ¾  wî           j  É   pq ~ q ~ ¼pt staticText-102ppppq ~ Ñppppq ~ Ô  wîppppppq ~ Øppq ~ Úppppppppsq ~ Ûpsq ~ ß  wîppppq ~ ñq ~ ñq ~ ïpsq ~ æ  wîppppq ~ ñq ~ ñpsq ~ à  wîppppq ~ ñq ~ ñpsq ~ é  wîppppq ~ ñq ~ ñpsq ~ ë  wîppppq ~ ñq ~ ñpppppt Helvetica-Boldpppppppppppt Valor totalxp  wî   ppq ~ sq ~ sq ~    w   sq ~ ¾  wî           n   Õ   sr java.awt.Color¥3u F falphaI valueL cst Ljava/awt/color/ColorSpace;[ 	frgbvaluet [F[ fvalueq ~ þxp    ÿÿÿÿpppq ~ q ~ ùsq ~ ü    ÿ   pppt staticText-101p~r )net.sf.jasperreports.engine.type.ModeEnum          xq ~ t TRANSPARENTppq ~ Ñsq ~ ~   ?uq ~    sq ~ t qtdeTotalOutrossq ~ t  > 0t java.lang.Booleanppppq ~ Ô  wîpppppt 	SansSerifq ~ Øp~r 4net.sf.jasperreports.engine.type.HorizontalAlignEnum          xq ~ t LEFTq ~ Úsq ~ Ù q ~q ~pq ~pppsq ~ Ûpsq ~ ß  wîppppq ~q ~q ~ ûpsq ~ æ  wîppppq ~q ~psq ~ à  wîppppq ~q ~psq ~ é  wîppppq ~q ~psq ~ ë  wîppppq ~q ~p~r 0net.sf.jasperreports.engine.type.LineSpacingEnum          xq ~ t SINGLEt nonept Cp1252t Helvetica-Boldppppp~r -net.sf.jasperreports.engine.type.RotationEnum          xq ~ t NONEpppp~r 2net.sf.jasperreports.engine.type.VerticalAlignEnum          xq ~ t TOPt EspÃ©ciesr 0net.sf.jasperreports.engine.base.JRBaseTextField      'Ø I PSEUDO_SERIAL_VERSION_UIDI 
bookmarkLevelB evaluationTimeB hyperlinkTargetB 
hyperlinkTypeZ isStretchWithOverflowL anchorNameExpressionq ~ L evaluationGroupq ~ tL evaluationTimeValuet 5Lnet/sf/jasperreports/engine/type/EvaluationTimeEnum;L 
expressionq ~ L hyperlinkAnchorExpressionq ~ L hyperlinkPageExpressionq ~ [ hyperlinkParameterst 3[Lnet/sf/jasperreports/engine/JRHyperlinkParameter;L hyperlinkReferenceExpressionq ~ L hyperlinkTooltipExpressionq ~ L isBlankWhenNullq ~ ÃL 
linkTargetq ~ L linkTypeq ~ L patternq ~ xq ~ ¿  wî           z  C   pq ~ q ~ ùpt 
textField-229ppppq ~ Ñppppq ~ Ô  wîppppppsq ~ Ö   ppq ~ Úppppppppsq ~ Ûpsq ~ ß  wîppppq ~*q ~*q ~'psq ~ æ  wîppppq ~*q ~*psq ~ à  wîppppq ~*q ~*psq ~ é  wîppppq ~*q ~*psq ~ ë  wîppppq ~*q ~*pppppt Helvetica-Boldppppppppppp  wî        pp~r 3net.sf.jasperreports.engine.type.EvaluationTimeEnum          xq ~ t NOWsq ~ ~   @uq ~    sq ~ t qtdeTotalEspeciet java.lang.Integerppppppq ~pppsq ~$  wî             É   pq ~ q ~ ùpt 
textField-230ppppq ~ Ñppppq ~ Ô  wîppppppq ~)ppq ~ Úppppppppsq ~ Ûpsq ~ ß  wîppppq ~;q ~;q ~9psq ~ æ  wîppppq ~;q ~;psq ~ à  wîppppq ~;q ~;psq ~ é  wîppppq ~;q ~;psq ~ ë  wîppppq ~;q ~;pppppt Helvetica-Boldppppppppppp  wî        ppq ~2sq ~ ~   Auq ~    sq ~ t valorTotalEspeciet java.lang.Doubleppppppq ~ppt 
  R$ #,##0.00xp  wî   sq ~ ~   >uq ~    sq ~ t qtdeTotalEspeciesq ~ t  > 0q ~pppsq ~ sq ~    w   sq ~$  wî           z  C   pq ~ q ~Npt 
textField-229ppppq ~ Ñppppq ~ Ô  wîppppppq ~)ppq ~ Úppppppppsq ~ Ûpsq ~ ß  wîppppq ~Rq ~Rq ~Ppsq ~ æ  wîppppq ~Rq ~Rpsq ~ à  wîppppq ~Rq ~Rpsq ~ é  wîppppq ~Rq ~Rpsq ~ ë  wîppppq ~Rq ~Rpppppt Helvetica-Boldppppppppppp  wî        ppq ~2sq ~ ~   Cuq ~    sq ~ t qtdeTotalOutrost java.lang.Integerppppppq ~pppsq ~$  wî             É   pq ~ q ~Npt 
textField-229ppppq ~ Ñppppq ~ Ô  wîppppppq ~)ppq ~ Úppppppppsq ~ Ûpsq ~ ß  wîppppq ~`q ~`q ~^psq ~ æ  wîppppq ~`q ~`psq ~ à  wîppppq ~`q ~`psq ~ é  wîppppq ~`q ~`psq ~ ë  wîppppq ~`q ~`pppppt Helvetica-Boldppppppppppp  wî        ppq ~2sq ~ ~   Duq ~    sq ~ t valorTotalOutrost java.lang.Doubleppppppq ~ppt 
  R$ #,##0.00sq ~ ¾  wî           n   Õ   sq ~ ü    ÿÿÿÿpppq ~ q ~Nsq ~ ü    ÿ   pppt staticText-101pq ~ppq ~ Ñppppq ~ Ô  wîpppppt 	SansSerifq ~ Øpq ~q ~ Úq ~q ~q ~pq ~pppsq ~ Ûpsq ~ ß  wîppppq ~rq ~rq ~mpsq ~ æ  wîppppq ~rq ~rpsq ~ à  wîppppq ~rq ~rpsq ~ é  wîppppq ~rq ~rpsq ~ ë  wîppppq ~rq ~rpq ~t nonept Cp1252t Helvetica-Boldpppppq ~ppppq ~!t Outrosxp  wî   sq ~ ~   Buq ~    sq ~ t visaoOutrosq ~pppsq ~ sq ~    w   sq ~$  wî             Ç   pq ~ q ~pt 
textField-230ppppq ~ Ñppppq ~ Ô  wîppppppq ~)ppq ~ Úppppppppsq ~ Ûpsq ~ ß  wîppppq ~q ~q ~psq ~ æ  wîppppq ~q ~psq ~ à  wîppppq ~q ~psq ~ é  wîppppq ~q ~psq ~ ë  wîppppq ~q ~pppppt Helvetica-Boldppppppppppp  wî        ppq ~2sq ~ ~   Fuq ~    sq ~ t valorTotalDevolucaot java.lang.Doubleppppppq ~ppt 
- R$ #,##0.00sq ~ ¾  wî           n   Õ   sq ~ ü    ÿÿÿÿpppq ~ q ~sq ~ ü    ÿ   pppt staticText-101pq ~ppq ~ Ñppppq ~ Ô  wîpppppt 	SansSerifq ~ Øpq ~q ~ Úq ~q ~q ~pq ~pppsq ~ Ûpsq ~ ß  wîppppq ~q ~q ~psq ~ æ  wîppppq ~q ~psq ~ à  wîppppq ~q ~psq ~ é  wîppppq ~q ~psq ~ ë  wîppppq ~q ~pq ~t nonept Cp1252t Helvetica-Boldpppppq ~ppppq ~!t DevoluÃ§Ãµessq ~$  wî           z  C   pq ~ q ~pt 
textField-229ppppq ~ Ñppppq ~ Ô  wîppppppq ~)ppq ~ Úppppppppsq ~ Ûpsq ~ ß  wîppppq ~¢q ~¢q ~ psq ~ æ  wîppppq ~¢q ~¢psq ~ à  wîppppq ~¢q ~¢psq ~ é  wîppppq ~¢q ~¢psq ~ ë  wîppppq ~¢q ~¢pppppt Helvetica-Boldppppppppppp  wî        ppq ~2sq ~ ~   Guq ~    sq ~ t qtdeTotalDevolucaot java.lang.Integerppppppq ~pppxp  wî   sq ~ ~   Euq ~    sq ~ t visaoDevolucaoq ~pppsq ~ sq ~    w   sq ~ ¾  wî           ?     sq ~ ü    ÿÿÿÿpppq ~ q ~²sq ~ ü    ÿ   pppt staticText-101pq ~ppq ~ Ñppppq ~ Ô  wîpppppt 	SansSerifq ~ Øpq ~q ~ Úq ~q ~q ~pq ~pppsq ~ Ûpsq ~ ß  wîppppq ~¹q ~¹q ~´psq ~ æ  wîppppq ~¹q ~¹psq ~ à  wîppppq ~¹q ~¹psq ~ é  wîppppq ~¹q ~¹psq ~ ë  wîppppq ~¹q ~¹pq ~t nonept Cp1252t Helvetica-Boldpppppq ~ppppq ~!t Total:sr +net.sf.jasperreports.engine.base.JRBaseLine      'Ø I PSEUDO_SERIAL_VERSION_UIDB 	directionL directionValuet 4Lnet/sf/jasperreports/engine/type/LineDirectionEnum;xr 5net.sf.jasperreports.engine.base.JRBaseGraphicElement      'Ø I PSEUDO_SERIAL_VERSION_UIDL fillq ~ L 	fillValuet +Lnet/sf/jasperreports/engine/type/FillEnum;L linePent #Lnet/sf/jasperreports/engine/JRPen;L penq ~ xq ~ È  wî             Õ   pq ~ q ~²pt line-5ppppq ~ Ñppppq ~ Ô  wîppsq ~ á  wîppppq ~Èp  wî ~r 2net.sf.jasperreports.engine.type.LineDirectionEnum          xq ~ t TOP_DOWNsq ~$  wî           z  C   pq ~ q ~²pt 
textField-229ppppq ~ Ñppppq ~ Ô  wîppppppq ~)ppq ~ Úppppppppsq ~ Ûpsq ~ ß  wîppppq ~Ðq ~Ðq ~Îpsq ~ æ  wîppppq ~Ðq ~Ðpsq ~ à  wîppppq ~Ðq ~Ðpsq ~ é  wîppppq ~Ðq ~Ðpsq ~ ë  wîppppq ~Ðq ~Ðpppppt Helvetica-Boldppppppppppp  wî        ppq ~2sq ~ ~   Huq ~    sq ~ t qtdeTotalFinalt java.lang.Integerppppppq ~pppsq ~$  wî             Ê   pq ~ q ~²pt 
textField-230ppppq ~ Ñppppq ~ Ô  wîppppppq ~)ppq ~ Úppppppppsq ~ Ûpsq ~ ß  wîppppq ~Þq ~Þq ~Üpsq ~ æ  wîppppq ~Þq ~Þpsq ~ à  wîppppq ~Þq ~Þpsq ~ é  wîppppq ~Þq ~Þpsq ~ ë  wîppppq ~Þq ~Þpppppt Helvetica-Boldppppppppppp  wî        ppq ~2sq ~ ~   Iuq ~    sq ~ t valorTotalFinalt java.lang.Doubleppppppq ~ppt 
  R$ #,##0.00xp  wî   2pppppsr java.util.HashSetºD¸·4  xpw   ?@     t "net.sf.jasperreports.engine.data.*t net.sf.jasperreports.engine.*t java.util.*xt javasq ~ sq ~    w   sq ~$  wî   
       m      pq ~ q ~ñpt 
textField-207ppppq ~ Ñppppq ~ Ô  wîpppppt Arialsq ~ Ö   pppq ~ Úpppppppsq ~ Ûpsq ~ ß  wîsq ~ ü    ÿfffpppp~r .net.sf.jasperreports.engine.type.LineStyleEnum          xq ~ t SOLIDsr java.lang.FloatÚíÉ¢Û<ðì F valuexq ~ ×?   q ~÷q ~÷q ~ópsq ~ æ  wîsq ~ ü    ÿfffppppq ~ûsq ~ý?   q ~÷q ~÷psq ~ à  wîppppq ~÷q ~÷psq ~ é  wîsq ~ ü    ÿfffppppq ~ûsq ~ý?   q ~÷q ~÷psq ~ ë  wîsq ~ ü    ÿfffppppq ~ûsq ~ý?   q ~÷q ~÷pppppt Helvetica-Obliquepppppppppp~q ~ t MIDDLE  wî        ppq ~2sq ~ ~   Juq ~    sq ~ t " "+" UsuÃ¡rio:" + sq ~ t usuariot java.lang.Stringppppppq ~ppt  xp  wî    ppq ~ sq ~ &  wî ur &[Lnet.sf.jasperreports.engine.JRField;<ßÇN*òp  xp   $sr ,net.sf.jasperreports.engine.base.JRBaseField      'Ø L descriptionq ~ L nameq ~ L 
propertiesMapq ~ *L valueClassNameq ~ L valueClassRealNameq ~ xppt valorTotalDinheirosq ~ 7pppt java.lang.Doublepsq ~pt valorTotalChequeVistasq ~ 7pppt java.lang.Doublepsq ~pt valorTotalChequePrazosq ~ 7pppt java.lang.Doublepsq ~pt valorTotalCartaoCreditosq ~ 7pppt java.lang.Doublepsq ~pt valorTotalCartaoDebitosq ~ 7pppt java.lang.Doublepsq ~pt valorTotalOutrossq ~ 7pppt java.lang.Doublepsq ~pt valorTotalFinalsq ~ 7pppt java.lang.Doublepsq ~pt qtdeTotalDinheirosq ~ 7pppt java.lang.Integerpsq ~pt qtdeTotalChequeVistasq ~ 7pppt java.lang.Integerpsq ~pt qtdeTotalChequePrazosq ~ 7pppt java.lang.Integerpsq ~pt qtdeTotalCartaoCreditosq ~ 7pppt java.lang.Integerpsq ~pt qtdeTotalCartaoDebitosq ~ 7pppt java.lang.Integerpsq ~pt qtdeTotalOutrossq ~ 7pppt java.lang.Integerpsq ~pt qtdeTotalFinalsq ~ 7pppt java.lang.Integerpsq ~pt 
visaoDinheirosq ~ 7pppt java.lang.Booleanpsq ~pt dataInisq ~ 7pppt java.lang.Stringpsq ~pt dataFimsq ~ 7pppt java.lang.Stringpsq ~pt visaoChequeVistasq ~ 7pppt java.lang.Booleanpsq ~pt visaoCartaoDebitosq ~ 7pppt java.lang.Booleanpsq ~pt visaoCartaoCreditosq ~ 7pppt java.lang.Booleanpsq ~pt visaoOutrossq ~ 7pppt java.lang.Booleanpsq ~pt visaoChequePrazosq ~ 7pppt java.lang.Booleanpsq ~pt qtdeTotalDevolucaosq ~ 7pppt java.lang.Integerpsq ~pt valorTotalDevolucaosq ~ 7pppt java.lang.Doublepsq ~pt visaoDevolucaosq ~ 7pppt java.lang.Booleanpsq ~pt visaoBoletosq ~ 7pppt java.lang.Booleanpsq ~pt valorTotalBoletosq ~ 7pppt java.lang.Doublepsq ~pt qtdeTotalBoletosq ~ 7pppt java.lang.Integerpsq ~pt qtdeTotalEspeciesq ~ 7pppt java.lang.Integerpsq ~pt valorTotalEspeciesq ~ 7pppt java.lang.Doublepsq ~pt valorTotalTransferenciaBancariasq ~ 7pppt java.lang.Doublepsq ~pt qtdeTotalTransferenciaBancariasq ~ 7pppt java.lang.Integerpsq ~pt visaoTransferenciaBancariasq ~ 7pppt java.lang.Booleanpsq ~pt 
valorTotalPixsq ~ 7pppq ~psq ~pt qtdeTotalPixsq ~ 7pppq ~psq ~pt visaoPixsq ~ 7pppq ~cppur &[Lnet.sf.jasperreports.engine.JRGroup;@£_zLýxê  xp   sr ,net.sf.jasperreports.engine.base.JRBaseGroup      'Ø I PSEUDO_SERIAL_VERSION_UIDB footerPositionZ isReprintHeaderOnEachPageZ isResetPageNumberZ isStartNewColumnZ isStartNewPageZ keepTogetherI minHeightToStartNewPageL 
countVariablet (Lnet/sf/jasperreports/engine/JRVariable;L 
expressionq ~ L footerPositionValuet 5Lnet/sf/jasperreports/engine/type/FooterPositionEnum;L groupFooterq ~ L groupFooterSectionq ~ L groupHeaderq ~ L groupHeaderSectionq ~ L nameq ~ xp  wî          sq ~ r  wî   q ~ sq ~ ~   
uq ~    sq ~ t new java.lang.Integer(1)q ~ Fppq ~ |ppsq ~ ~   uq ~    sq ~ t new java.lang.Integer(0)q ~ Fpt Dinheiro_COUNTq ~ª~q ~ t GROUPq ~ Fpsq ~ ~   pt java.lang.Objectp~r 3net.sf.jasperreports.engine.type.FooterPositionEnum          xq ~ t NORMALpsq ~ ·uq ~ º   sq ~ sq ~     w    xp  wî    ppq ~ psq ~ ·uq ~ º   sq ~ sq ~    w   sq ~ ¾  wî           O      pq ~ q ~Âpt 
staticText-83ppppq ~ Ñppppq ~ Ô  wîppppppsq ~ Ö   pppppppppppsq ~ Ûpsq ~ ß  wîppppq ~Çq ~Çq ~Äpsq ~ æ  wîppppq ~Çq ~Çpsq ~ à  wîppppq ~Çq ~Çpsq ~ é  wîppppq ~Çq ~Çpsq ~ ë  wîppppq ~Çq ~Çpppppppppppppppppt Dinheirosq ~Ã  wî          p      pq ~ q ~Âpt line-1ppppq ~ Ñppppq ~ Ô  wîppsq ~ á  wîppppq ~Îp  wî q ~Ìsq ~$  wî           :     pq ~ q ~Âpt 
textField-217ppppq ~ Ñppppq ~ Ô  wîppppppppppppppppppsq ~ Ûpsq ~ ß  wîppppq ~Óq ~Óq ~Ñpsq ~ æ  wîppppq ~Óq ~Ópsq ~ à  wîppppq ~Óq ~Ópsq ~ é  wîppppq ~Óq ~Ópsq ~ ë  wîppppq ~Óq ~Óppppppppppppppppp  wî        ppq ~2sq ~ ~   uq ~    sq ~ t qtdeTotalDinheirot java.lang.Integerppppppq ~pppsq ~$  wî           s  Ê   pq ~ q ~Âpt 
textField-223ppppq ~ Ñppppq ~ Ô  wîppppppppppppppppppsq ~ Ûpsq ~ ß  wîppppq ~àq ~àq ~Þpsq ~ æ  wîppppq ~àq ~àpsq ~ à  wîppppq ~àq ~àpsq ~ é  wîppppq ~àq ~àpsq ~ ë  wîppppq ~àq ~àppppppppppppppppp  wî        ppq ~2sq ~ ~   uq ~    sq ~ t valorTotalDinheirot java.lang.Doubleppppppq ~ppt R$ #,##0.00sq ~ ¾  wî           C     pq ~ q ~Âpt staticText-107ppppq ~ Ñppppq ~ Ô  wîppppppppppppppppppsq ~ Ûpsq ~ ß  wîppppq ~îq ~îq ~ìpsq ~ æ  wîppppq ~îq ~îpsq ~ à  wîppppq ~îq ~îpsq ~ é  wîppppq ~îq ~îpsq ~ ë  wîppppq ~îq ~îpppppppppppppppppt 
Quantidadesq ~ ¾  wî           C  Ê   pq ~ q ~Âpt staticText-113ppppq ~ Ñppppq ~ Ô  wîppppppppppppppppppsq ~ Ûpsq ~ ß  wîppppq ~÷q ~÷q ~õpsq ~ æ  wîppppq ~÷q ~÷psq ~ à  wîppppq ~÷q ~÷psq ~ é  wîppppq ~÷q ~÷psq ~ ë  wîppppq ~÷q ~÷pppppppppppppppppt Valor totalxp  wî   2sq ~ ~   uq ~    sq ~ t 
visaoDinheiroq ~ppq ~ t Dinheirosq ~§  wî          sq ~ r  wî   q ~ sq ~ ~   uq ~    sq ~ t new java.lang.Integer(1)q ~ Fppq ~ |ppsq ~ ~   
uq ~    sq ~ t new java.lang.Integer(0)q ~ Fpt 
CHVista_COUNTq ~q ~µq ~ Fpsq ~ ~   pq ~¸pq ~ºpsq ~ ·uq ~ º   sq ~ sq ~     w    xp  wî    ppq ~ psq ~ ·uq ~ º   sq ~ sq ~    w   sq ~Ã  wî          p      pq ~ q ~pt line-2ppppq ~ Ñppppq ~ Ô  wîppsq ~ á  wîppppq ~p  wî q ~Ìsq ~ ¾  wî                pq ~ q ~pt 
staticText-88ppppq ~ Ñppppq ~ Ô  wîppppppq ~Æpppppppppppsq ~ Ûpsq ~ ß  wîppppq ~q ~q ~psq ~ æ  wîppppq ~q ~psq ~ à  wîppppq ~q ~psq ~ é  wîppppq ~q ~psq ~ ë  wîppppq ~q ~pppppppppppppppppt Ch. Ã  Vistasq ~$  wî           :     pq ~ q ~pt 
textField-218ppppq ~ Ñppppq ~ Ô  wîppppppppppppppppppsq ~ Ûpsq ~ ß  wîppppq ~%q ~%q ~#psq ~ æ  wîppppq ~%q ~%psq ~ à  wîppppq ~%q ~%psq ~ é  wîppppq ~%q ~%psq ~ ë  wîppppq ~%q ~%ppppppppppppppppp  wî        ppq ~2sq ~ ~   uq ~    sq ~ t qtdeTotalChequeVistat java.lang.Integerppppppq ~pppsq ~$  wî           s  Ê   pq ~ q ~pt 
textField-224ppppq ~ Ñppppq ~ Ô  wîppppppppppppppppppsq ~ Ûpsq ~ ß  wîppppq ~2q ~2q ~0psq ~ æ  wîppppq ~2q ~2psq ~ à  wîppppq ~2q ~2psq ~ é  wîppppq ~2q ~2psq ~ ë  wîppppq ~2q ~2ppppppppppppppppp  wî        ppq ~2sq ~ ~   uq ~    sq ~ t valorTotalChequeVistat java.lang.Doubleppppppq ~ppt R$ #,##0.00sq ~ ¾  wî           C     pq ~ q ~pt staticText-106ppppq ~ Ñppppq ~ Ô  wîppppppppppppppppppsq ~ Ûpsq ~ ß  wîppppq ~@q ~@q ~>psq ~ æ  wîppppq ~@q ~@psq ~ à  wîppppq ~@q ~@psq ~ é  wîppppq ~@q ~@psq ~ ë  wîppppq ~@q ~@pppppppppppppppppt 
Quantidadesq ~ ¾  wî           C  Ê   pq ~ q ~pt staticText-112ppppq ~ Ñppppq ~ Ô  wîppppppppppppppppppsq ~ Ûpsq ~ ß  wîppppq ~Iq ~Iq ~Gpsq ~ æ  wîppppq ~Iq ~Ipsq ~ à  wîppppq ~Iq ~Ipsq ~ é  wîppppq ~Iq ~Ipsq ~ ë  wîppppq ~Iq ~Ipppppppppppppppppt Valor totalxp  wî   2sq ~ ~   uq ~    sq ~ t visaoChequeVistaq ~ppq ~ t CHVistasq ~§  wî          sq ~ r  wî   q ~ sq ~ ~   uq ~    sq ~ t new java.lang.Integer(1)q ~ Fppq ~ |ppsq ~ ~   uq ~    sq ~ t new java.lang.Integer(0)q ~ Fpt 
CHPrazo_COUNTq ~Uq ~µq ~ Fpsq ~ ~   pq ~¸pq ~ºpsq ~ ·uq ~ º   sq ~ sq ~     w    xp  wî    ppq ~ psq ~ ·uq ~ º   sq ~ sq ~    w   sq ~Ã  wî          p      pq ~ q ~gpt line-3ppppq ~ Ñppppq ~ Ô  wîppsq ~ á  wîppppq ~ip  wî q ~Ìsq ~ ¾  wî                pq ~ q ~gpt 
staticText-91ppppq ~ Ñppppq ~ Ô  wîppppppq ~Æpppppppppppsq ~ Ûpsq ~ ß  wîppppq ~nq ~nq ~lpsq ~ æ  wîppppq ~nq ~npsq ~ à  wîppppq ~nq ~npsq ~ é  wîppppq ~nq ~npsq ~ ë  wîppppq ~nq ~npppppppppppppppppt Ch. Ã  Prazosq ~$  wî           :     pq ~ q ~gpt 
textField-219ppppq ~ Ñppppq ~ Ô  wîppppppppppppppppppsq ~ Ûpsq ~ ß  wîppppq ~wq ~wq ~upsq ~ æ  wîppppq ~wq ~wpsq ~ à  wîppppq ~wq ~wpsq ~ é  wîppppq ~wq ~wpsq ~ ë  wîppppq ~wq ~wppppppppppppppppp  wî        ppq ~2sq ~ ~    uq ~    sq ~ t qtdeTotalChequePrazot java.lang.Integerppppppq ~pppsq ~$  wî           s  Ê   pq ~ q ~gpt 
textField-225ppppq ~ Ñppppq ~ Ô  wîppppppppppppppppppsq ~ Ûpsq ~ ß  wîppppq ~q ~q ~psq ~ æ  wîppppq ~q ~psq ~ à  wîppppq ~q ~psq ~ é  wîppppq ~q ~psq ~ ë  wîppppq ~q ~ppppppppppppppppp  wî        ppq ~2sq ~ ~   !uq ~    sq ~ t valorTotalChequePrazot java.lang.Doubleppppppq ~ppt R$ #,##0.00sq ~ ¾  wî           C     pq ~ q ~gpt staticText-105ppppq ~ Ñppppq ~ Ô  wîppppppppppppppppppsq ~ Ûpsq ~ ß  wîppppq ~q ~q ~psq ~ æ  wîppppq ~q ~psq ~ à  wîppppq ~q ~psq ~ é  wîppppq ~q ~psq ~ ë  wîppppq ~q ~pppppppppppppppppt 
Quantidadesq ~ ¾  wî           C  Ê   pq ~ q ~gpt staticText-111ppppq ~ Ñppppq ~ Ô  wîppppppppppppppppppsq ~ Ûpsq ~ ß  wîppppq ~q ~q ~psq ~ æ  wîppppq ~q ~psq ~ à  wîppppq ~q ~psq ~ é  wîppppq ~q ~psq ~ ë  wîppppq ~q ~pppppppppppppppppt Valor totalxp  wî   2sq ~ ~   uq ~    sq ~ t visaoChequePrazoq ~ppq ~ t CHPrazosq ~§  wî          sq ~ r  wî   q ~ sq ~ ~   uq ~    sq ~ t new java.lang.Integer(1)q ~ Fppq ~ |ppsq ~ ~   uq ~    sq ~ t new java.lang.Integer(0)q ~ Fpt CartaoDebito_COUNTq ~§q ~µq ~ Fpsq ~ ~   "pq ~¸pq ~ºpsq ~ ·uq ~ º   sq ~ sq ~     w    xp  wî    ppq ~ psq ~ ·uq ~ º   sq ~ sq ~    w   sq ~Ã  wî          p      pq ~ q ~¹pt line-4ppppq ~ Ñppppq ~ Ô  wîppsq ~ á  wîppppq ~»p  wî q ~Ìsq ~ ¾  wî                pq ~ q ~¹pt 
staticText-94ppppq ~ Ñppppq ~ Ô  wîppppppq ~Æpppppppppppsq ~ Ûpsq ~ ß  wîppppq ~Àq ~Àq ~¾psq ~ æ  wîppppq ~Àq ~Àpsq ~ à  wîppppq ~Àq ~Àpsq ~ é  wîppppq ~Àq ~Àpsq ~ ë  wîppppq ~Àq ~Àpppppppppppppppppt CartÃ£o de DÃ©bitosq ~$  wî           :     pq ~ q ~¹pt 
textField-220ppppq ~ Ñppppq ~ Ô  wîppppppppppppppppppsq ~ Ûpsq ~ ß  wîppppq ~Éq ~Éq ~Çpsq ~ æ  wîppppq ~Éq ~Épsq ~ à  wîppppq ~Éq ~Épsq ~ é  wîppppq ~Éq ~Épsq ~ ë  wîppppq ~Éq ~Éppppppppppppppppp  wî        ppq ~2sq ~ ~   $uq ~    sq ~ t qtdeTotalCartaoDebitot java.lang.Integerppppppq ~pppsq ~$  wî           s  Ê   pq ~ q ~¹pt 
textField-226ppppq ~ Ñppppq ~ Ô  wîppppppppppppppppppsq ~ Ûpsq ~ ß  wîppppq ~Öq ~Öq ~Ôpsq ~ æ  wîppppq ~Öq ~Öpsq ~ à  wîppppq ~Öq ~Öpsq ~ é  wîppppq ~Öq ~Öpsq ~ ë  wîppppq ~Öq ~Öppppppppppppppppp  wî        ppq ~2sq ~ ~   %uq ~    sq ~ t valorTotalCartaoDebitot java.lang.Doubleppppppq ~ppt R$ #,##0.00sq ~ ¾  wî           C     pq ~ q ~¹pt staticText-104ppppq ~ Ñppppq ~ Ô  wîppppppppppppppppppsq ~ Ûpsq ~ ß  wîppppq ~äq ~äq ~âpsq ~ æ  wîppppq ~äq ~äpsq ~ à  wîppppq ~äq ~äpsq ~ é  wîppppq ~äq ~äpsq ~ ë  wîppppq ~äq ~äpppppppppppppppppt 
Quantidadesq ~ ¾  wî           C  Ê   pq ~ q ~¹pt staticText-110ppppq ~ Ñppppq ~ Ô  wîppppppppppppppppppsq ~ Ûpsq ~ ß  wîppppq ~íq ~íq ~ëpsq ~ æ  wîppppq ~íq ~ípsq ~ à  wîppppq ~íq ~ípsq ~ é  wîppppq ~íq ~ípsq ~ ë  wîppppq ~íq ~ípppppppppppppppppt Valor totalxp  wî   2sq ~ ~   #uq ~    sq ~ t visaoCartaoDebitoq ~ppq ~ t CartaoDebitosq ~§  wî          sq ~ r  wî   q ~ sq ~ ~   uq ~    sq ~ t new java.lang.Integer(1)q ~ Fppq ~ |ppsq ~ ~   uq ~    sq ~ t new java.lang.Integer(0)q ~ Fpt TransferenciaBancaria_COUNTq ~ùq ~µq ~ Fppq ~ºpsq ~ ·uq ~ º   sq ~ sq ~     w    xp  wî   2ppppsq ~ ·uq ~ º   sq ~ sq ~    w   sq ~$  wî           s  Ê   pq ~ q ~
pt 
textField-226ppppq ~ Ñppppq ~ Ô  wîppppppppppppppppppsq ~ Ûpsq ~ ß  wîppppq ~q ~q ~psq ~ æ  wîppppq ~q ~psq ~ à  wîppppq ~q ~psq ~ é  wîppppq ~q ~psq ~ ë  wîppppq ~q ~ppppppppppppppppp  wî        ppq ~2sq ~ ~   'uq ~    sq ~ t valorTotalTransferenciaBancariat java.lang.Doubleppppppq ~ppt R$ #,##0.00sq ~$  wî           :     pq ~ q ~
pt 
textField-220ppppq ~ Ñppppq ~ Ô  wîppppppppppppppppppsq ~ Ûpsq ~ ß  wîppppq ~q ~q ~psq ~ æ  wîppppq ~q ~psq ~ à  wîppppq ~q ~psq ~ é  wîppppq ~q ~psq ~ ë  wîppppq ~q ~ppppppppppppppppp  wî        ppq ~2sq ~ ~   (uq ~    sq ~ t qtdeTotalTransferenciaBancariat java.lang.Integerppppppq ~pppsq ~ ¾  wî           C  Ê   pq ~ q ~
pt staticText-110ppppq ~ Ñppppq ~ Ô  wîppppppppppppppppppsq ~ Ûpsq ~ ß  wîppppq ~)q ~)q ~'psq ~ æ  wîppppq ~)q ~)psq ~ à  wîppppq ~)q ~)psq ~ é  wîppppq ~)q ~)psq ~ ë  wîppppq ~)q ~)pppppppppppppppppt Valor totalsq ~ ¾  wî           C     pq ~ q ~
pt staticText-104ppppq ~ Ñppppq ~ Ô  wîppppppppppppppppppsq ~ Ûpsq ~ ß  wîppppq ~2q ~2q ~0psq ~ æ  wîppppq ~2q ~2psq ~ à  wîppppq ~2q ~2psq ~ é  wîppppq ~2q ~2psq ~ ë  wîppppq ~2q ~2pppppppppppppppppt 
Quantidadesq ~ ¾  wî                pq ~ q ~
pt 
staticText-94ppppq ~ Ñppppq ~ Ô  wîppppppq ~Æpppppppppppsq ~ Ûpsq ~ ß  wîppppq ~;q ~;q ~9psq ~ æ  wîppppq ~;q ~;psq ~ à  wîppppq ~;q ~;psq ~ é  wîppppq ~;q ~;psq ~ ë  wîppppq ~;q ~;pppppppppppppppppt TransferÃªncia BancÃ¡riasq ~Ã  wî          p       pq ~ q ~
pt line-4ppppq ~ Ñppppq ~ Ô  wîppsq ~ á  wîppppq ~Bp  wî q ~Ìxp  wî   2sq ~ ~   &uq ~    sq ~ t 
visaoDinheiroq ~ppq ~ t TransferenciaBancariasq ~§  wî          sq ~ r  wî   q ~ sq ~ ~   uq ~    sq ~ t new java.lang.Integer(1)q ~ Fppq ~ |ppsq ~ ~   uq ~    sq ~ t new java.lang.Integer(0)q ~ Fpt agrupadores_COUNTq ~Jq ~µq ~ Fpsq ~ ~   )pq ~¸pq ~ºpsq ~ ·uq ~ º   sq ~ sq ~     w    xp  wî    ppq ~ psq ~ ·uq ~ º   sq ~ sq ~    w   sq ~Ã  wî          p      pq ~ q ~\pt line-5ppppq ~ Ñppppq ~ Ô  wîppsq ~ á  wîppppq ~^p  wî q ~Ìsq ~ ¾  wî                pq ~ q ~\pt 
staticText-97ppppq ~ Ñppppq ~ Ô  wîppppppq ~Æpppppppppppsq ~ Ûpsq ~ ß  wîppppq ~cq ~cq ~apsq ~ æ  wîppppq ~cq ~cpsq ~ à  wîppppq ~cq ~cpsq ~ é  wîppppq ~cq ~cpsq ~ ë  wîppppq ~cq ~cpppppppppppppppppt CartÃ£o de CrÃ©ditosq ~$  wî           s  Ê   pq ~ q ~\pt 
textField-227ppppq ~ Ñppppq ~ Ô  wîppppppppppppppppppsq ~ Ûpsq ~ ß  wîppppq ~lq ~lq ~jpsq ~ æ  wîppppq ~lq ~lpsq ~ à  wîppppq ~lq ~lpsq ~ é  wîppppq ~lq ~lpsq ~ ë  wîppppq ~lq ~lppppppppppppppppp  wî        ppq ~2sq ~ ~   +uq ~    sq ~ t valorTotalCartaoCreditot java.lang.Doubleppppppq ~ppt R$ #,##0.00sq ~ ¾  wî           C  Ê   pq ~ q ~\pt staticText-108ppppq ~ Ñppppq ~ Ô  wîppppppppppppppppppsq ~ Ûpsq ~ ß  wîppppq ~zq ~zq ~xpsq ~ æ  wîppppq ~zq ~zpsq ~ à  wîppppq ~zq ~zpsq ~ é  wîppppq ~zq ~zpsq ~ ë  wîppppq ~zq ~zpppppppppppppppppt Valor totalsq ~ ¾  wî           C     pq ~ q ~\pt staticText-103ppppq ~ Ñppppq ~ Ô  wîppppppppppppppppppsq ~ Ûpsq ~ ß  wîppppq ~q ~q ~psq ~ æ  wîppppq ~q ~psq ~ à  wîppppq ~q ~psq ~ é  wîppppq ~q ~psq ~ ë  wîppppq ~q ~pppppppppppppppppt 
Quantidadesq ~$  wî           :     pq ~ q ~\pt 
textField-221ppppq ~ Ñppppq ~ Ô  wîppppppppppppppppppsq ~ Ûpsq ~ ß  wîppppq ~q ~q ~psq ~ æ  wîppppq ~q ~psq ~ à  wîppppq ~q ~psq ~ é  wîppppq ~q ~psq ~ ë  wîppppq ~q ~ppppppppppppppppp  wî        ppq ~2sq ~ ~   ,uq ~    sq ~ t qtdeTotalCartaoCreditot java.lang.Integerppppppq ~pppxp  wî   2sq ~ ~   *uq ~    sq ~ t visaoCartaoCreditoq ~ppq ~ sq ~ sq ~    w   sq ~ ¾  wî                pq ~ q ~pt 
staticText-97ppppq ~ Ñppppq ~ Ô  wîppppppq ~Æpppppppppppsq ~ Ûpsq ~ ß  wîppppq ~q ~q ~psq ~ æ  wîppppq ~q ~psq ~ à  wîppppq ~q ~psq ~ é  wîppppq ~q ~psq ~ ë  wîppppq ~q ~pppppppppppppppppt Boletosq ~ ¾  wî           C     pq ~ q ~pt staticText-103ppppq ~ Ñppppq ~ Ô  wîppppppppppppppppppsq ~ Ûpsq ~ ß  wîppppq ~¨q ~¨q ~¦psq ~ æ  wîppppq ~¨q ~¨psq ~ à  wîppppq ~¨q ~¨psq ~ é  wîppppq ~¨q ~¨psq ~ ë  wîppppq ~¨q ~¨pppppppppppppppppt 
Quantidadesq ~$  wî           :     pq ~ q ~pt 
textField-221ppppq ~ Ñppppq ~ Ô  wîppppppppppppppppppsq ~ Ûpsq ~ ß  wîppppq ~±q ~±q ~¯psq ~ æ  wîppppq ~±q ~±psq ~ à  wîppppq ~±q ~±psq ~ é  wîppppq ~±q ~±psq ~ ë  wîppppq ~±q ~±ppppppppppppppppp  wî        ppq ~2sq ~ ~   .uq ~    sq ~ t qtdeTotalBoletot java.lang.Integerppppppq ~pppsq ~ ¾  wî           C  É   pq ~ q ~pt staticText-108ppppq ~ Ñppppq ~ Ô  wîppppppppppppppppppsq ~ Ûpsq ~ ß  wîppppq ~¾q ~¾q ~¼psq ~ æ  wîppppq ~¾q ~¾psq ~ à  wîppppq ~¾q ~¾psq ~ é  wîppppq ~¾q ~¾psq ~ ë  wîppppq ~¾q ~¾pppppppppppppppppt Valor totalsq ~$  wî           s  Ê   pq ~ q ~pt 
textField-227ppppq ~ Ñppppq ~ Ô  wîppppppppppppppppppsq ~ Ûpsq ~ ß  wîppppq ~Çq ~Çq ~Åpsq ~ æ  wîppppq ~Çq ~Çpsq ~ à  wîppppq ~Çq ~Çpsq ~ é  wîppppq ~Çq ~Çpsq ~ ë  wîppppq ~Çq ~Çppppppppppppppppp  wî        ppq ~2sq ~ ~   /uq ~    sq ~ t valorTotalBoletot java.lang.Doubleppppppq ~ppt R$ #,##0.00sq ~Ã  wî          p      pq ~ q ~pt line-5ppppq ~ Ñppppq ~ Ô  wîppsq ~ á  wîppppq ~Óp  wî q ~Ìxp  wî   2sq ~ ~   -uq ~    sq ~ t visaoBoletoq ~pppsq ~ sq ~    w   sq ~Ã  wî          p       pq ~ q ~Úpt line-5ppppq ~ Ñppppq ~ Ô  wîppsq ~ á  wîppppq ~Üp  wî q ~Ìsq ~ ¾  wî           C     pq ~ q ~Úpq ~§ppppq ~ Ñppppq ~ Ô  wîppppppppppppppppppsq ~ Ûpsq ~ ß  wîppppq ~àq ~àq ~ßpsq ~ æ  wîppppq ~àq ~àpsq ~ à  wîppppq ~àq ~àpsq ~ é  wîppppq ~àq ~àpsq ~ ë  wîppppq ~àq ~àpppppppppppppppppq ~®sq ~$  wî           :     pq ~ q ~Úpq ~°ppppq ~ Ñppppq ~ Ô  wîppppppppppppppppppsq ~ Ûpsq ~ ß  wîppppq ~çq ~çq ~æpsq ~ æ  wîppppq ~çq ~çpsq ~ à  wîppppq ~çq ~çpsq ~ é  wîppppq ~çq ~çpsq ~ ë  wîppppq ~çq ~çppppppppppppppppp  wî        ppq ~2sq ~ ~   1uq ~    sq ~ t qtdeTotalPixq ~»ppppppq ~pppsq ~ ¾  wî           C  Ê   pq ~ q ~Úpq ~½ppppq ~ Ñppppq ~ Ô  wîppppppppppppppppppsq ~ Ûpsq ~ ß  wîppppq ~òq ~òq ~ñpsq ~ æ  wîppppq ~òq ~òpsq ~ à  wîppppq ~òq ~òpsq ~ é  wîppppq ~òq ~òpsq ~ ë  wîppppq ~òq ~òpppppppppppppppppq ~Äsq ~$  wî           s  Ê   pq ~ q ~Úpq ~Æppppq ~ Ñppppq ~ Ô  wîppppppppppppppppppsq ~ Ûpsq ~ ß  wîppppq ~ùq ~ùq ~øpsq ~ æ  wîppppq ~ùq ~ùpsq ~ à  wîppppq ~ùq ~ùpsq ~ é  wîppppq ~ùq ~ùpsq ~ ë  wîppppq ~ùq ~ùppppppppppppppppp  wî        ppq ~2sq ~ ~   2uq ~    sq ~ t 
valorTotalPixq ~Ñppppppq ~ppq ~Òsq ~ ¾  wî                pq ~ q ~Úpq ~ppppq ~ Ñppppq ~ Ô  wîppppppq ~Æpppppppppppsq ~ Ûpsq ~ ß  wîppppq ~q ~q ~psq ~ æ  wîppppq ~q ~psq ~ à  wîppppq ~q ~psq ~ é  wîppppq ~q ~psq ~ ë  wîppppq ~q ~pppppppppppppppppt Pixxp  wî   2sq ~ ~   0uq ~    sq ~ t visaoPixq ~pppsq ~ sq ~    w   sq ~Ã  wî          p       pq ~ q ~pq ~Ôppppq ~ Ñppppq ~ Ô  wîppsq ~ á  wîppppq ~p  wî q ~Ìsq ~ ¾  wî                pq ~ q ~pt 
staticText-97ppppq ~ Ñppppq ~ Ô  wîppppppsq ~ Ö   pppppppppppsq ~ Ûpsq ~ ß  wîppppq ~q ~q ~psq ~ æ  wîppppq ~q ~psq ~ à  wîppppq ~q ~psq ~ é  wîppppq ~q ~psq ~ ë  wîppppq ~q ~pppppppppppppppppt &DevoluÃ§Ã£o de Dinheiro - Cancelamentosq ~ ¾  wî           C     pq ~ q ~pt staticText-103ppppq ~ Ñppppq ~ Ô  wîppppppppppppppppppsq ~ Ûpsq ~ ß  wîppppq ~q ~q ~psq ~ æ  wîppppq ~q ~psq ~ à  wîppppq ~q ~psq ~ é  wîppppq ~q ~psq ~ ë  wîppppq ~q ~pppppppppppppppppt 
Quantidadesq ~ ¾  wî           C  Ê   pq ~ q ~pt staticText-108ppppq ~ Ñppppq ~ Ô  wîppppppppppppppppppsq ~ Ûpsq ~ ß  wîppppq ~(q ~(q ~&psq ~ æ  wîppppq ~(q ~(psq ~ à  wîppppq ~(q ~(psq ~ é  wîppppq ~(q ~(psq ~ ë  wîppppq ~(q ~(pppppppppppppppppt Valor totalsq ~$  wî           :     pq ~ q ~pt 
textField-221ppppq ~ Ñppppq ~ Ô  wîppppppppppppppppppsq ~ Ûpsq ~ ß  wîppppq ~1q ~1q ~/psq ~ æ  wîppppq ~1q ~1psq ~ à  wîppppq ~1q ~1psq ~ é  wîppppq ~1q ~1psq ~ ë  wîppppq ~1q ~1ppppppppppppppppp  wî        ppq ~2sq ~ ~   4uq ~    sq ~ t qtdeTotalDevolucaot java.lang.Integerppppppq ~pppsq ~$  wî           s  Ê   pq ~ q ~pt 
textField-227ppppq ~ Ñppppq ~ Ô  wîppppppppppppppppppsq ~ Ûpsq ~ ß  wîppppq ~>q ~>q ~<psq ~ æ  wîppppq ~>q ~>psq ~ à  wîppppq ~>q ~>psq ~ é  wîppppq ~>q ~>psq ~ ë  wîppppq ~>q ~>ppppppppppppppppp  wî        ppq ~2sq ~ ~   5uq ~    sq ~ t valorTotalDevolucaot java.lang.Doubleppppppq ~ppt 
- R$ #,##0.00xp  wî   2sq ~ ~   3uq ~    sq ~ t visaoDevolucaoq ~pppt agrupadorest ReceitaPorPeriodoSinteticoReluq ~ 2   $sq ~ 4ppq ~ 6psq ~ 7pppq ~ :psq ~ 4ppq ~ <psq ~ 7pppq ~ >psq ~ 4ppq ~ @psq ~ 7pppq ~ Bpsq ~ 4ppq ~ Dpsq ~ 7pppq ~ Fpsq ~ 4ppq ~ Hpsq ~ 7pppq ~ Jpsq ~ 4ppq ~ Lpsq ~ 7pppq ~ Npsq ~ 4ppq ~ Ppsq ~ 7pppq ~ Rpsq ~ 4ppq ~ Tpsq ~ 7pppq ~ Vpsq ~ 4ppq ~ Xpsq ~ 7pppq ~ Zpsq ~ 4ppq ~ \psq ~ 7pppq ~ ^psq ~ 4ppq ~ `psq ~ 7pppq ~ bpsq ~ 4ppq ~ dpsq ~ 7pppq ~ fpsq ~ 4ppq ~ hpsq ~ 7pppq ~ jpsq ~ 4ppq ~ lpsq ~ 7pppq ~ npsq ~ 4ppt REPORT_VIRTUALIZERpsq ~ 7pppt )net.sf.jasperreports.engine.JRVirtualizerpsq ~ 4ppt IS_IGNORE_PAGINATIONpsq ~ 7pppq ~psq ~ 4  ppt logoPadraoRelatoriopsq ~ 7pppt java.io.InputStreampsq ~ 4  ppt tituloRelatoriopsq ~ 7pppt java.lang.Stringpsq ~ 4  ppt nomeEmpresapsq ~ 7pppt java.lang.Stringpsq ~ 4  ppt versaoSoftwarepsq ~ 7pppt java.lang.Stringpsq ~ 4  ppt usuariopsq ~ 7pppt java.lang.Stringpsq ~ 4  ppt filtrospsq ~ 7pppt java.lang.Stringpsq ~ 4 sq ~ ~    uq ~    sq ~ t p"E:\\desenvolvimentosv\\Projeto Pacto Solucoes\\ZillyonWeb\\src\\java\\relatorio\\designRelatorio\\financeiro\\"t java.lang.Stringppt 
SUBREPORT_DIRpsq ~ 7pppq ~psq ~ 4 sq ~ ~   uq ~    sq ~ t p"E:\\desenvolvimentosv\\Projeto Pacto Solucoes\\ZillyonWeb\\src\\java\\relatorio\\designRelatorio\\financeiro\\"t java.lang.Stringppt SUBREPORT_DIR1psq ~ 7pppq ~psq ~ 4  ppt dataInipsq ~ 7pppt java.lang.Stringpsq ~ 4  ppt dataFimpsq ~ 7pppt java.lang.Stringpsq ~ 4  ppt qtdAVpsq ~ 7pppt java.lang.Stringpsq ~ 4  ppt qtdCApsq ~ 7pppt java.lang.Stringpsq ~ 4  ppt qtdChequeAVpsq ~ 7pppt java.lang.Stringpsq ~ 4  ppt qtdChequePRpsq ~ 7pppt java.lang.Stringpsq ~ 4  ppt qtdOutropsq ~ 7pppt java.lang.Stringpsq ~ 4  ppt valorAVpsq ~ 7pppt java.lang.Doublepsq ~ 4  ppt valorCApsq ~ 7pppt java.lang.Doublepsq ~ 4  ppt 
valorChequeAVpsq ~ 7pppt java.lang.Doublepsq ~ 4  ppt 
valorChequePRpsq ~ 7pppt java.lang.Doublepsq ~ 4  ppt 
valorOutropsq ~ 7pppt java.lang.Doublepsq ~ 7psq ~    w   t ireport.zoomt 	ireport.xt 	ireport.yt ireport.scriptlethandlingt ireport.encodingxsr java.util.HashMapÚÁÃ`Ñ F 
loadFactorI 	thresholdxp?@     w      q ~Ît 2.1435888100000016q ~Òt 
ISO-8859-1q ~Ït 117q ~Ðt 718q ~Ñt 0xpppppuq ~ p   sq ~ r  wî   q ~ yppq ~ |ppsq ~ ~   uq ~    sq ~ t new java.lang.Integer(1)q ~ Fpq ~ pq ~ q ~ Fpsq ~ r  wî   q ~ yppq ~ |ppsq ~ ~   uq ~    sq ~ t new java.lang.Integer(1)q ~ Fpq ~ pq ~ q ~ Fpsq ~ r  wî   q ~ sq ~ ~   uq ~    sq ~ t new java.lang.Integer(1)q ~ Fppq ~ |ppsq ~ ~   uq ~    sq ~ t new java.lang.Integer(0)q ~ Fpq ~ pq ~ q ~ Fpsq ~ r  wî   q ~ sq ~ ~   uq ~    sq ~ t new java.lang.Integer(1)q ~ Fppq ~ |ppsq ~ ~   uq ~    sq ~ t new java.lang.Integer(0)q ~ Fpq ~ §pq ~ q ~ Fpsq ~ r  wî   q ~ sq ~ ~   uq ~    sq ~ t new java.lang.Integer(1)q ~ Fppq ~ |ppsq ~ ~   	uq ~    sq ~ t new java.lang.Integer(0)q ~ Fpq ~ ±pq ~ ²q ~ Fpq ~«q ~q ~Vq ~¨q ~úq ~K~q ~ ´t EMPTYq ~Op~r 0net.sf.jasperreports.engine.type.OrientationEnum          xq ~ t PORTRAITsq ~ sq ~     w    xp  wî    ppq ~ sq ~ sq ~    	w   	sq ~ ¾  wî           !  +   pq ~ q ~pt 
staticText-82p~q ~t OPAQUEppq ~ Ñppppq ~ Ô  wîpppppt Arialq ~)p~q ~
t CENTERq ~ Úq ~pq ~pq ~pppsq ~ Ûpsq ~ ß  wîsq ~ ü    ÿfffppppq ~ûsq ~ý?   q ~q ~q ~	psq ~ æ  wîsq ~ ü    ÿfffpppppsq ~ý    q ~q ~psq ~ à  wîsq ~ ü    ÿfffpppppsq ~ý    q ~q ~psq ~ é  wîsq ~ ü    ÿfffppppq ~ûsq ~ý    q ~q ~psq ~ ë  wîsq ~ ü    ÿfffppppq ~ûsq ~ý    q ~q ~pppppt Helvetica-Boldppppppppppq ~
t  asr ,net.sf.jasperreports.engine.base.JRBaseImage      'Ø *I PSEUDO_SERIAL_VERSION_UIDI 
bookmarkLevelB evaluationTimeB hyperlinkTargetB 
hyperlinkTypeZ isLazyB onErrorTypeL anchorNameExpressionq ~ L borderq ~ L borderColorq ~ ÀL bottomBorderq ~ L bottomBorderColorq ~ ÀL 
bottomPaddingq ~ ÁL evaluationGroupq ~ tL evaluationTimeValueq ~%L 
expressionq ~ L horizontalAlignmentq ~ L horizontalAlignmentValueq ~ ÂL hyperlinkAnchorExpressionq ~ L hyperlinkPageExpressionq ~ [ hyperlinkParametersq ~&L hyperlinkReferenceExpressionq ~ L hyperlinkTooltipExpressionq ~ L isUsingCacheq ~ ÃL 
leftBorderq ~ L leftBorderColorq ~ ÀL leftPaddingq ~ ÁL lineBoxq ~ ÄL 
linkTargetq ~ L linkTypeq ~ L onErrorTypeValuet 2Lnet/sf/jasperreports/engine/type/OnErrorTypeEnum;L paddingq ~ ÁL rightBorderq ~ L rightBorderColorq ~ ÀL rightPaddingq ~ ÁL 
scaleImageq ~ L scaleImageValuet 1Lnet/sf/jasperreports/engine/type/ScaleImageEnum;L 	topBorderq ~ L topBorderColorq ~ ÀL 
topPaddingq ~ ÁL verticalAlignmentq ~ L verticalAlignmentValueq ~ Çxq ~Å  wî   $       R      pq ~ q ~pt image-1ppppq ~ Ñppppq ~ Ô  wîppsq ~ á  wîppppq ~%p  wî         ppppppp~q ~1t PAGEsq ~ ~   6uq ~    sq ~ t logoPadraoRelatoriot java.io.InputStreamppppppppq ~ Úpppsq ~ Ûpsq ~ ß  wîsq ~ ü    ÿfffppppq ~ûsq ~ý?   q ~/q ~/q ~%psq ~ æ  wîsq ~ ü    ÿfffppppq ~ûsq ~ý?   q ~/q ~/psq ~ à  wîppppq ~/q ~/psq ~ é  wîsq ~ ü    ÿfffppppq ~ûsq ~ý?   q ~/q ~/psq ~ ë  wîsq ~ ü    ÿfffppppq ~ûsq ~ý?   q ~/q ~/pp~r 0net.sf.jasperreports.engine.type.OnErrorTypeEnum          xq ~ t BLANKpppppppppppsq ~$  wî           i     sq ~ ü    ÿÿÿÿpppq ~ q ~pt 	dataRel-1pq ~ppq ~ Ñppppq ~ Ô  wîpppppt Verdanaq ~öpq ~pq ~pppppppsq ~ Ûsq ~ Ö   sq ~ ß  wîsq ~ ü    ÿfffppppq ~ûsq ~ý?   q ~Dq ~Dq ~@psq ~ æ  wîsq ~ ü    ÿfffppppq ~ûsq ~ý?   q ~Dq ~Dpsq ~ à  wîppppq ~Dq ~Dpsq ~ é  wîsq ~ ü    ÿfffppppq ~ûsq ~ý?   q ~Dq ~Dpsq ~ ë  wîsq ~ ü    ÿfffppppq ~ûsq ~ý?   q ~Dq ~Dpppppt 	Helveticappppppppppq ~
  wî        ppq ~2sq ~ ~   7uq ~    sq ~ t 
new Date()t java.util.Dateppppppq ~ppt dd/MM/yyyy HH.mm.sssq ~$  wî          µ   S   pq ~ q ~pt textField-2ppppq ~ Ñppppq ~ Ô  wîpppppt Arialq ~)pq ~q ~ Úppppppppsq ~ Ûpsq ~ ß  wîsq ~ ü    ÿfffppppq ~ûsq ~ý?   q ~]q ~]q ~Zpsq ~ æ  wîppq ~ûsq ~ý    q ~]q ~]psq ~ à  wîppq ~ûsq ~ý?   q ~]q ~]psq ~ é  wîppq ~ûsq ~ý    q ~]q ~]psq ~ ë  wîsq ~ ü    ÿfffppppq ~ûsq ~ý?   q ~]q ~]pppppt Helvetica-Boldppppppppppp  wî        ppq ~2sq ~ ~   8uq ~    sq ~ t tituloRelatoriot java.lang.Stringppppppq ~ Úpppsq ~$  wî           <     pq ~ q ~pt textField-25ppppq ~ Ñppppq ~ Ô  wîpppppt Arialpp~q ~
t RIGHTpppppppppsq ~ Ûpsq ~ ß  wîsq ~ ü    ÿfffppppq ~ûsq ~ý?   q ~uq ~uq ~ppsq ~ æ  wîsq ~ ü    ÿfffppppq ~ûsq ~ý?   q ~uq ~upsq ~ à  wîppppq ~uq ~upsq ~ é  wîsq ~ ü    ÿ   ppppq ~ûsq ~ý    q ~uq ~upsq ~ ë  wîsq ~ ü    ÿ   ppppq ~ûsq ~ý    q ~uq ~uppppppppppppppppp  wî        ppq ~2sq ~ ~   9uq ~    sq ~ t "PÃ¡g: " + sq ~ t PAGE_NUMBERsq ~ t 	 + " de "t java.lang.Stringppppppq ~pppsq ~$  wî           -  D   pq ~ q ~pt textField-26ppppq ~ Ñppppq ~ Ô  wîpppppt Arialppppppppppppsq ~ Ûq ~Esq ~ ß  wîsq ~ ü    ÿfffppppq ~ûsq ~ý?   q ~q ~q ~psq ~ æ  wîsq ~ ü    ÿfffppppq ~ûsq ~ý    q ~q ~psq ~ à  wîppppq ~q ~psq ~ é  wîsq ~ ü    ÿfffppppq ~ûsq ~ý?   q ~q ~psq ~ ë  wîsq ~ ü    ÿ   ppppq ~ûsq ~ý    q ~q ~ppppppppppppppppp  wî        pp~q ~1t REPORTsq ~ ~   :uq ~    sq ~ t " " + sq ~ t PAGE_NUMBERsq ~ t  + ""t java.lang.Stringppppppq ~pppsq ~$  wî           Ø   S   pq ~ q ~pt 
textField-215ppppq ~ Ñppppq ~ Ô  wîpppppt Arialq ~)pq ~sq ~ Úq ~ Úpppppppsq ~ Ûpsq ~ ß  wîsq ~ ü    ÿfffppppq ~ûsq ~ý?   q ~«q ~«q ~¨psq ~ æ  wîppppq ~«q ~«psq ~ à  wîppppq ~«q ~«psq ~ é  wîppppq ~«q ~«psq ~ ë  wîppppq ~«q ~«pppppt Helvetica-BoldObliqueppppppppppp  wî        ppq ~2sq ~ ~   ;uq ~    sq ~ t dataInit java.lang.Stringppppppq ~pppsq ~$  wî           ¼  L   pq ~ q ~pt 
textField-216ppppq ~ Ñppppq ~ Ô  wîpppppt Arialq ~)pq ~q ~ Úq ~ Úpppppppsq ~ Ûpsq ~ ß  wîsq ~ ü    ÿfffppppq ~ûsq ~ý?   q ~¼q ~¼q ~¹psq ~ æ  wîppppq ~¼q ~¼psq ~ à  wîppppq ~¼q ~¼psq ~ é  wîppppq ~¼q ~¼psq ~ ë  wîppppq ~¼q ~¼pppppt Helvetica-BoldObliqueppppppppppp  wî        ppq ~2sq ~ ~   <uq ~    sq ~ t dataFimt java.lang.Stringppppppq ~pppsq ~$  wî   %       µ   S   %pq ~ q ~pt 
textField-216ppppq ~ Ñppppq ~ Ô  wîpppppt Arialsq ~ Ö   
pq ~q ~ Úq ~ Úpppppppsq ~ Ûpsq ~ ß  wîppppq ~Îq ~Îq ~Êpsq ~ æ  wîppppq ~Îq ~Îpsq ~ à  wîppppq ~Îq ~Îpsq ~ é  wîppppq ~Îq ~Îpsq ~ ë  wîppppq ~Îq ~Îpppppt Helvetica-BoldObliqueppppppppppq ~
  wî        ppq ~2sq ~ ~   =uq ~    sq ~ t filtrost java.lang.Stringppppppq ~pppxp  wî   Jppq ~ ~r /net.sf.jasperreports.engine.type.PrintOrderEnum          xq ~ t VERTICALpsq ~ sq ~     w    xp  wî    ppq ~ psq ~ sq ~     w    xp  wî    ppq ~ ~r 3net.sf.jasperreports.engine.type.WhenNoDataTypeEnum          xq ~ t NO_PAGESsr 6net.sf.jasperreports.engine.design.JRReportCompileData      'Ø L crosstabCompileDataq ~ 8L datasetCompileDataq ~ 8L mainDatasetCompileDataq ~ xpsq ~Ó?@     w       xsq ~Ó?@     w      q ~ 1ur [B¬óøTà  xp  Êþº¾   .  8ReceitaPorPeriodoSinteticoRel_Teste_1709063851737_364741  ,net/sf/jasperreports/engine/fill/JREvaluator  parameter_REPORT_LOCALE 2Lnet/sf/jasperreports/engine/fill/JRFillParameter; parameter_JASPER_REPORT parameter_REPORT_TIME_ZONE parameter_REPORT_FILE_RESOLVER parameter_REPORT_SCRIPTLET parameter_REPORT_PARAMETERS_MAP parameter_REPORT_CONNECTION parameter_REPORT_CLASS_LOADER parameter_REPORT_DATA_SOURCE $parameter_REPORT_URL_HANDLER_FACTORY parameter_REPORT_FORMAT_FACTORY parameter_REPORT_MAX_COUNT parameter_REPORT_TEMPLATES  parameter_REPORT_RESOURCE_BUNDLE variable_PAGE_NUMBER 1Lnet/sf/jasperreports/engine/fill/JRFillVariable; variable_COLUMN_NUMBER variable_REPORT_COUNT variable_PAGE_COUNT variable_COLUMN_COUNT <init> ()V Code  
    	    	  !  	  # 	 	  % 
 	  '  	  )  	  + 
 	  -  	  /  	  1  	  3  	  5  	  7  	  9  	  ;  	  =  	  ?  	  A  	  C LineNumberTable customizedInit 0(Ljava/util/Map;Ljava/util/Map;Ljava/util/Map;)V 
initParams (Ljava/util/Map;)V H I
  J 
initFields L I
  M initVars O I
  P 
REPORT_LOCALE R 
java/util/Map T get &(Ljava/lang/Object;)Ljava/lang/Object; V W U X 0net/sf/jasperreports/engine/fill/JRFillParameter Z 
JASPER_REPORT \ REPORT_TIME_ZONE ^ REPORT_FILE_RESOLVER ` REPORT_SCRIPTLET b REPORT_PARAMETERS_MAP d REPORT_CONNECTION f REPORT_CLASS_LOADER h REPORT_DATA_SOURCE j REPORT_URL_HANDLER_FACTORY l REPORT_FORMAT_FACTORY n REPORT_MAX_COUNT p REPORT_TEMPLATES r REPORT_RESOURCE_BUNDLE t PAGE_NUMBER v /net/sf/jasperreports/engine/fill/JRFillVariable x 
COLUMN_NUMBER z REPORT_COUNT | 
PAGE_COUNT ~ COLUMN_COUNT  evaluate (I)Ljava/lang/Object; 
Exceptions java/lang/Throwable  java/lang/Integer  (I)V  
   evaluateOld evaluateEstimated 
SourceFile !                      	     
               
                                                                   Ì     d*· *µ  *µ "*µ $*µ &*µ (*µ **µ ,*µ .*µ 0*µ 2*µ 4*µ 6*µ 8*µ :*µ <*µ >*µ @*µ B*µ D±    E   V       	          ! " " ' # , $ 1 % 6 & ; ' @ ( E ) J * O + T , Y - ^ . c   F G     4     *+· K*,· N*-· Q±    E       :  ; 
 <  =  H I    M     ý*+S¹ Y À [À [µ  *+]¹ Y À [À [µ "*+_¹ Y À [À [µ $*+a¹ Y À [À [µ &*+c¹ Y À [À [µ (*+e¹ Y À [À [µ **+g¹ Y À [À [µ ,*+i¹ Y À [À [µ .*+k¹ Y À [À [µ 0*+m¹ Y À [À [µ 2*+o¹ Y À [À [µ 4*+q¹ Y À [À [µ 6*+s¹ Y À [À [µ 8*+u¹ Y À [À [µ :±    E   >    E  F $ G 6 H H I Z J l K ~ L  M ¢ N ´ O Æ P Ø Q ê R ü S  L I           ±    E       [  O I          [*+w¹ Y À yÀ yµ <*+{¹ Y À yÀ yµ >*+}¹ Y À yÀ yµ @*+¹ Y À yÀ yµ B*+¹ Y À yÀ yµ D±    E       c  d $ e 6 f H g Z h              ë     Mª             -   9   E   Q   ]   i   u   » Y· M§ T» Y· M§ H» Y· M§ <» Y· M§ 0» Y· M§ $» Y· M§ » Y· M§ » Y· M,°    E   J    p  r 0 v 9 w < { E | H  Q  T  ]  `  i  l  u  x       ¡              ë     Mª             -   9   E   Q   ]   i   u   » Y· M§ T» Y· M§ H» Y· M§ <» Y· M§ 0» Y· M§ $» Y· M§ » Y· M§ » Y· M,°    E   J    ª  ¬ 0 ° 9 ± < µ E ¶ H º Q » T ¿ ] À ` Ä i Å l É u Ê x Î  Ï  Ó  Û              ë     Mª             -   9   E   Q   ]   i   u   » Y· M§ T» Y· M§ H» Y· M§ <» Y· M§ 0» Y· M§ $» Y· M§ » Y· M§ » Y· M,°    E   J    ä  æ 0 ê 9 ë < ï E ð H ô Q õ T ù ] ú ` þ i ÿ l u x 	 
       xuq ~è  >Êþº¾   . 2ReceitaPorPeriodoSinteticoRel_1709063851737_364741  ,net/sf/jasperreports/engine/fill/JREvaluator  parameter_JASPER_REPORT 2Lnet/sf/jasperreports/engine/fill/JRFillParameter; parameter_REPORT_TIME_ZONE parameter_valorCA parameter_usuario parameter_REPORT_FILE_RESOLVER parameter_REPORT_PARAMETERS_MAP parameter_qtdCA parameter_SUBREPORT_DIR1 parameter_REPORT_CLASS_LOADER $parameter_REPORT_URL_HANDLER_FACTORY parameter_REPORT_DATA_SOURCE parameter_IS_IGNORE_PAGINATION parameter_valorChequeAV parameter_qtdChequePR parameter_valorChequePR parameter_REPORT_MAX_COUNT parameter_REPORT_TEMPLATES parameter_valorOutro parameter_qtdAV parameter_dataIni parameter_REPORT_LOCALE parameter_qtdOutro parameter_REPORT_VIRTUALIZER parameter_logoPadraoRelatorio parameter_REPORT_SCRIPTLET parameter_REPORT_CONNECTION parameter_SUBREPORT_DIR parameter_dataFim parameter_REPORT_FORMAT_FACTORY parameter_tituloRelatorio parameter_nomeEmpresa parameter_qtdChequeAV parameter_valorAV  parameter_REPORT_RESOURCE_BUNDLE parameter_versaoSoftware parameter_filtros field_qtdeTotalDinheiro .Lnet/sf/jasperreports/engine/fill/JRFillField; field_valorTotalChequeVista field_valorTotalCartaoDebito field_visaoDevolucao field_qtdeTotalEspecie field_valorTotalBoleto field_qtdeTotalCartaoCredito field_qtdeTotalCartaoDebito field_qtdeTotalChequeVista field_visaoChequePrazo field_valorTotalFinal field_qtdeTotalBoleto field_qtdeTotalOutros 
field_dataIni $field_qtdeTotalTransferenciaBancaria field_valorTotalDevolucao %field_valorTotalTransferenciaBancaria field_qtdeTotalPix field_valorTotalEspecie field_visaoBoleto field_valorTotalChequePrazo field_visaoPix field_valorTotalDinheiro field_visaoChequeVista field_valorTotalOutros field_visaoCartaoCredito field_visaoDinheiro field_visaoCartaoDebito 
field_dataFim field_qtdeTotalFinal  field_visaoTransferenciaBancaria field_qtdeTotalChequePrazo field_qtdeTotalDevolucao field_valorTotalCartaoCredito field_visaoOutros field_valorTotalPix variable_PAGE_NUMBER 1Lnet/sf/jasperreports/engine/fill/JRFillVariable; variable_COLUMN_NUMBER variable_REPORT_COUNT variable_PAGE_COUNT variable_COLUMN_COUNT variable_Dinheiro_COUNT variable_CHVista_COUNT variable_CHPrazo_COUNT variable_CartaoDebito_COUNT $variable_TransferenciaBancaria_COUNT variable_agrupadores_COUNT <init> ()V Code [ \
  ^  	  `  	  b  	  d 	 	  f 
 	  h  	  j  	  l 
 	  n  	  p  	  r  	  t  	  v  	  x  	  z  	  |  	  ~  	    	    	    	    	    	    	    	    	    	     	   ! 	   " 	   # 	   $ 	   % 	   & 	    ' 	  ¢ ( 	  ¤ ) 	  ¦ * +	  ¨ , +	  ª - +	  ¬ . +	  ® / +	  ° 0 +	  ² 1 +	  ´ 2 +	  ¶ 3 +	  ¸ 4 +	  º 5 +	  ¼ 6 +	  ¾ 7 +	  À 8 +	  Â 9 +	  Ä : +	  Æ ; +	  È < +	  Ê = +	  Ì > +	  Î ? +	  Ð @ +	  Ò A +	  Ô B +	  Ö C +	  Ø D +	  Ú E +	  Ü F +	  Þ G +	  à H +	  â I +	  ä J +	  æ K +	  è L +	  ê M +	  ì N +	  î O P	  ð Q P	  ò R P	  ô S P	  ö T P	  ø U P	  ú V P	  ü W P	  þ X P	   Y P	  Z P	  LineNumberTable customizedInit 0(Ljava/util/Map;Ljava/util/Map;Ljava/util/Map;)V 
initParams (Ljava/util/Map;)V	

  
initFields


  initVars

  
JASPER_REPORT 
java/util/Map get &(Ljava/lang/Object;)Ljava/lang/Object; 0net/sf/jasperreports/engine/fill/JRFillParameter REPORT_TIME_ZONE valorCA usuario! REPORT_FILE_RESOLVER# REPORT_PARAMETERS_MAP% qtdCA' SUBREPORT_DIR1) REPORT_CLASS_LOADER+ REPORT_URL_HANDLER_FACTORY- REPORT_DATA_SOURCE/ IS_IGNORE_PAGINATION1 
valorChequeAV3 qtdChequePR5 
valorChequePR7 REPORT_MAX_COUNT9 REPORT_TEMPLATES; 
valorOutro= qtdAV? dataIniA 
REPORT_LOCALEC qtdOutroE REPORT_VIRTUALIZERG logoPadraoRelatorioI REPORT_SCRIPTLETK REPORT_CONNECTIONM 
SUBREPORT_DIRO dataFimQ REPORT_FORMAT_FACTORYS tituloRelatorioU nomeEmpresaW qtdChequeAVY valorAV[ REPORT_RESOURCE_BUNDLE] versaoSoftware_ filtrosa qtdeTotalDinheiroc ,net/sf/jasperreports/engine/fill/JRFillFielde valorTotalChequeVistag valorTotalCartaoDebitoi visaoDevolucaok qtdeTotalEspeciem valorTotalBoletoo qtdeTotalCartaoCreditoq qtdeTotalCartaoDebitos qtdeTotalChequeVistau visaoChequePrazow valorTotalFinaly qtdeTotalBoleto{ qtdeTotalOutros} qtdeTotalTransferenciaBancaria valorTotalDevolucao valorTotalTransferenciaBancaria qtdeTotalPix valorTotalEspecie visaoBoleto valorTotalChequePrazo visaoPix valorTotalDinheiro visaoChequeVista valorTotalOutros visaoCartaoCredito 
visaoDinheiro visaoCartaoDebito qtdeTotalFinal visaoTransferenciaBancaria qtdeTotalChequePrazo qtdeTotalDevolucao¡ valorTotalCartaoCredito£ visaoOutros¥ 
valorTotalPix§ PAGE_NUMBER© /net/sf/jasperreports/engine/fill/JRFillVariable« 
COLUMN_NUMBER­ REPORT_COUNT¯ 
PAGE_COUNT± COLUMN_COUNT³ Dinheiro_COUNTµ 
CHVista_COUNT· 
CHPrazo_COUNT¹ CartaoDebito_COUNT» TransferenciaBancaria_COUNT½ agrupadores_COUNT¿ evaluate (I)Ljava/lang/Object; 
Exceptions java/lang/ThrowableÄ eE:\desenvolvimentosv\Projeto Pacto Solucoes\ZillyonWeb\src\java\relatorio\designRelatorio\financeiro\Æ java/lang/IntegerÈ (I)V [Ê
ÉË getValue ()Ljava/lang/Object;ÍÎ
fÏ java/lang/BooleanÑ java/lang/DoubleÓ
Ï java/io/InputStreamÖ java/util/DateØ
Ù ^ java/lang/StringÛ java/lang/StringBufferÝ PÃ¡g: ß (Ljava/lang/String;)V [á
Þâ
¬Ï append ,(Ljava/lang/Object;)Ljava/lang/StringBuffer;åæ
Þç  de é ,(Ljava/lang/String;)Ljava/lang/StringBuffer;åë
Þì toString ()Ljava/lang/String;îï
Þð  ò intValue ()Iôõ
Éö valueOf (Z)Ljava/lang/Boolean;øù
Òú   UsuÃ¡rio:ü evaluateOld getOldValueÿÎ
f 
¬  evaluateEstimated getEstimatedValueÎ
¬ 
SourceFile !     S                 	     
               
                                                                                                     !     "     #     $     %     &     '     (     )     * +    , +    - +    . +    / +    0 +    1 +    2 +    3 +    4 +    5 +    6 +    7 +    8 +    9 +    : +    ; +    < +    = +    > +    ? +    @ +    A +    B +    C +    D +    E +    F +    G +    H +    I +    J +    K +    L +    M +    N +    O P    Q P    R P    S P    T P    U P    V P    W P    X P    Y P    Z P     [ \  ]      ¤*· _*µ a*µ c*µ e*µ g*µ i*µ k*µ m*µ o*µ q*µ s*µ u*µ w*µ y*µ {*µ }*µ *µ *µ *µ *µ *µ *µ *µ *µ *µ *µ *µ *µ *µ *µ *µ *µ *µ ¡*µ £*µ ¥*µ §*µ ©*µ «*µ ­*µ ¯*µ ±*µ ³*µ µ*µ ·*µ ¹*µ »*µ ½*µ ¿*µ Á*µ Ã*µ Å*µ Ç*µ É*µ Ë*µ Í*µ Ï*µ Ñ*µ Ó*µ Õ*µ ×*µ Ù*µ Û*µ Ý*µ ß*µ á*µ ã*µ å*µ ç*µ é*µ ë*µ í*µ ï*µ ñ*µ ó*µ õ*µ ÷*µ ù*µ û*µ ý*µ ÿ*µ*µ*µ±     V U      	          ! " " ' # , $ 1 % 6 & ; ' @ ( E ) J * O + T , Y - ^ . c / h 0 m 1 r 2 w 3 | 4  5  6  7  8  9  :  ; ¤ < © = ® > ³ ? ¸ @ ½ A Â B Ç C Ì D Ñ E Ö F Û G à H å I ê J ï K ô L ù M þ N O P
 Q R S T! U& V+ W0 X5 Y: Z? [D \I ]N ^S _X `] ab bg cl dq ev f{ g h i j k l m n£    ]   4     *+·*,·*-·±          z  { 
 |  } 	
  ]  U    ­*+¹ ÀÀµ a*+¹ ÀÀµ c*+ ¹ ÀÀµ e*+"¹ ÀÀµ g*+$¹ ÀÀµ i*+&¹ ÀÀµ k*+(¹ ÀÀµ m*+*¹ ÀÀµ o*+,¹ ÀÀµ q*+.¹ ÀÀµ s*+0¹ ÀÀµ u*+2¹ ÀÀµ w*+4¹ ÀÀµ y*+6¹ ÀÀµ {*+8¹ ÀÀµ }*+:¹ ÀÀµ *+<¹ ÀÀµ *+>¹ ÀÀµ *+@¹ ÀÀµ *+B¹ ÀÀµ *+D¹ ÀÀµ *+F¹ ÀÀµ *+H¹ ÀÀµ *+J¹ ÀÀµ *+L¹ ÀÀµ *+N¹ ÀÀµ *+P¹ ÀÀµ *+R¹ ÀÀµ *+T¹ ÀÀµ *+V¹ ÀÀµ *+X¹ ÀÀµ *+Z¹ ÀÀµ *+\¹ ÀÀµ ¡*+^¹ ÀÀµ £*+`¹ ÀÀµ ¥*+b¹ ÀÀµ §±       %      &  9  L  _  r      «  ¾  Ñ  ä  ÷ 
  0 C V i |  ¢ µ È Û î    ¡' ¢: £M ¤` ¥s ¦ § ¨¬ © 

  ]  U    ­*+d¹ ÀfÀfµ ©*+h¹ ÀfÀfµ «*+j¹ ÀfÀfµ ­*+l¹ ÀfÀfµ ¯*+n¹ ÀfÀfµ ±*+p¹ ÀfÀfµ ³*+r¹ ÀfÀfµ µ*+t¹ ÀfÀfµ ·*+v¹ ÀfÀfµ ¹*+x¹ ÀfÀfµ »*+z¹ ÀfÀfµ ½*+|¹ ÀfÀfµ ¿*+~¹ ÀfÀfµ Á*+B¹ ÀfÀfµ Ã*+¹ ÀfÀfµ Å*+¹ ÀfÀfµ Ç*+¹ ÀfÀfµ É*+¹ ÀfÀfµ Ë*+¹ ÀfÀfµ Í*+¹ ÀfÀfµ Ï*+¹ ÀfÀfµ Ñ*+¹ ÀfÀfµ Ó*+¹ ÀfÀfµ Õ*+¹ ÀfÀfµ ×*+¹ ÀfÀfµ Ù*+¹ ÀfÀfµ Û*+¹ ÀfÀfµ Ý*+¹ ÀfÀfµ ß*+R¹ ÀfÀfµ á*+¹ ÀfÀfµ ã*+¹ ÀfÀfµ å*+ ¹ ÀfÀfµ ç*+¢¹ ÀfÀfµ é*+¤¹ ÀfÀfµ ë*+¦¹ ÀfÀfµ í*+¨¹ ÀfÀfµ ï±       %   ±  ² & ³ 9 ´ L µ _ ¶ r ·  ¸  ¹ « º ¾ » Ñ ¼ ä ½ ÷ ¾
 ¿ À0 ÁC ÂV Ãi Ä| Å Æ¢ Çµ ÈÈ ÉÛ Êî Ë Ì Í' Î: ÏM Ð` Ñs Ò Ó Ô¬ Õ 
  ]       Ò*+ª¹ À¬À¬µ ñ*+®¹ À¬À¬µ ó*+°¹ À¬À¬µ õ*+²¹ À¬À¬µ ÷*+´¹ À¬À¬µ ù*+¶¹ À¬À¬µ û*+¸¹ À¬À¬µ ý*+º¹ À¬À¬µ ÿ*+¼¹ À¬À¬µ*+¾¹ À¬À¬µ*+À¹ À¬À¬µ±      2    Ý  Þ & ß 9 à L á _ â r ã  ä  å « æ ¾ ç Ñ è ÁÂ Ã    Å ]  µ    AMª  <       J  9  @  G  S  _  k  w        §  ³  ¿  Ë  ×  ã  ï  û        +  7  <  J  X  f  k  y        ¨  ¶  Ä  É  ×  å  ó        "  0  >  L  Z  h  v         ®  ¼  Ê  Ø  ã  ñ    3  A  O  ]  y    £  ±  ¿  Í  Û  é  ÷      !ÇM§ÿÇM§ø»ÉY·ÌM§ì»ÉY·ÌM§à»ÉY·ÌM§Ô»ÉY·ÌM§È»ÉY·ÌM§¼»ÉY·ÌM§°»ÉY·ÌM§¤»ÉY·ÌM§»ÉY·ÌM§»ÉY·ÌM§»ÉY·ÌM§t»ÉY·ÌM§h»ÉY·ÌM§\»ÉY·ÌM§P»ÉY·ÌM§D»ÉY·ÌM§8»ÉY·ÌM§,»ÉY·ÌM§ »ÉY·ÌM§»ÉY·ÌM§M§*´ Ý¶ÐÀÒM§õ*´ ©¶ÐÀÉM§ç*´ Õ¶ÐÀÔM§ÙM§Ô*´ ×¶ÐÀÒM§Æ*´ ¹¶ÐÀÉM§¸*´ «¶ÐÀÔM§ªM§¥*´ »¶ÐÀÒM§*´ ç¶ÐÀÉM§*´ Ñ¶ÐÀÔM§{M§v*´ ß¶ÐÀÒM§h*´ ·¶ÐÀÉM§Z*´ ­¶ÐÀÔM§L*´ Ý¶ÐÀÒM§>*´ É¶ÐÀÔM§0*´ Å¶ÐÀÉM§"M§*´ Û¶ÐÀÒM§*´ ë¶ÐÀÔM§*´ µ¶ÐÀÉM§ó*´ Ï¶ÐÀÒM§å*´ ¿¶ÐÀÉM§×*´ ³¶ÐÀÔM§É*´ Ó¶ÐÀÒM§»*´ Ë¶ÐÀÉM§­*´ ï¶ÐÀÔM§*´ ¯¶ÐÀÒM§*´ é¶ÐÀÉM§*´ Ç¶ÐÀÔM§u*´ ¶ÕÀ×M§g»ÙY·ÚM§\*´ ¶ÕÀÜM§N»ÞYà·ã*´ ñ¶äÀÉ¶èê¶í¶ñM§*»ÞYó·ã*´ ñ¶äÀÉ¶è¶ñM§*´ Ã¶ÐÀÜM§ þ*´ á¶ÐÀÜM§ ð*´ §¶ÕÀÜM§ â*´ ±¶ÐÀÉ¶÷ § ¸ûM§ Æ*´ Á¶ÐÀÉ¶÷ § ¸ûM§ ª*´ ±¶ÐÀÉM§ *´ Í¶ÐÀÔM§ *´ í¶ÐÀÒM§ *´ Á¶ÐÀÉM§ r*´ Ù¶ÐÀÔM§ d*´ ¯¶ÐÀÒM§ V*´ Ç¶ÐÀÔM§ H*´ é¶ÐÀÉM§ :*´ ã¶ÐÀÉM§ ,*´ ½¶ÐÀÔM§ »ÞYý·ã*´ g¶ÕÀÜ¶í¶ñM,°     b    ð  ò< ö@ ÷C ûG üJ SV_b
knwz#§$ª(³)¶-¿.Â2Ë3Î7×8Ú<ã=æAïBòFûGþKL
PQUV"Z+[._7`:d<e?iJjMnXo[sftixkyn}y~|¨«¶¹ÄÇ É¡Ì¥×¦Úªå«è¯ó°ö´µ¹º¾¿ Ã"Ä%È0É3Í>ÎAÒLÓO×ZØ]ÜhÝkávâyæçëìð ñ£õ®ö±ú¼û¿ÿÊ ÍØÛ	ã
æñô36AD"O#R'](`,y-|126£7¦;±<´@¿AÂEÍFÐJÛKÞOéPìT÷UúYZ^_c!d$h?p þÂ Ã    Å ]  µ    AMª  <       J  9  @  G  S  _  k  w        §  ³  ¿  Ë  ×  ã  ï  û        +  7  <  J  X  f  k  y        ¨  ¶  Ä  É  ×  å  ó        "  0  >  L  Z  h  v         ®  ¼  Ê  Ø  ã  ñ    3  A  O  ]  y    £  ±  ¿  Í  Û  é  ÷      !ÇM§ÿÇM§ø»ÉY·ÌM§ì»ÉY·ÌM§à»ÉY·ÌM§Ô»ÉY·ÌM§È»ÉY·ÌM§¼»ÉY·ÌM§°»ÉY·ÌM§¤»ÉY·ÌM§»ÉY·ÌM§»ÉY·ÌM§»ÉY·ÌM§t»ÉY·ÌM§h»ÉY·ÌM§\»ÉY·ÌM§P»ÉY·ÌM§D»ÉY·ÌM§8»ÉY·ÌM§,»ÉY·ÌM§ »ÉY·ÌM§»ÉY·ÌM§M§*´ Ý¶ÀÒM§õ*´ ©¶ÀÉM§ç*´ Õ¶ÀÔM§ÙM§Ô*´ ×¶ÀÒM§Æ*´ ¹¶ÀÉM§¸*´ «¶ÀÔM§ªM§¥*´ »¶ÀÒM§*´ ç¶ÀÉM§*´ Ñ¶ÀÔM§{M§v*´ ß¶ÀÒM§h*´ ·¶ÀÉM§Z*´ ­¶ÀÔM§L*´ Ý¶ÀÒM§>*´ É¶ÀÔM§0*´ Å¶ÀÉM§"M§*´ Û¶ÀÒM§*´ ë¶ÀÔM§*´ µ¶ÀÉM§ó*´ Ï¶ÀÒM§å*´ ¿¶ÀÉM§×*´ ³¶ÀÔM§É*´ Ó¶ÀÒM§»*´ Ë¶ÀÉM§­*´ ï¶ÀÔM§*´ ¯¶ÀÒM§*´ é¶ÀÉM§*´ Ç¶ÀÔM§u*´ ¶ÕÀ×M§g»ÙY·ÚM§\*´ ¶ÕÀÜM§N»ÞYà·ã*´ ñ¶ÀÉ¶èê¶í¶ñM§*»ÞYó·ã*´ ñ¶ÀÉ¶è¶ñM§*´ Ã¶ÀÜM§ þ*´ á¶ÀÜM§ ð*´ §¶ÕÀÜM§ â*´ ±¶ÀÉ¶÷ § ¸ûM§ Æ*´ Á¶ÀÉ¶÷ § ¸ûM§ ª*´ ±¶ÀÉM§ *´ Í¶ÀÔM§ *´ í¶ÀÒM§ *´ Á¶ÀÉM§ r*´ Ù¶ÀÔM§ d*´ ¯¶ÀÒM§ V*´ Ç¶ÀÔM§ H*´ é¶ÀÉM§ :*´ ã¶ÀÉM§ ,*´ ½¶ÀÔM§ »ÞYý·ã*´ g¶ÕÀÜ¶í¶ñM,°     b   y {<@CGJSV_bknwz¢£§¨¬§­ª±³²¶¶¿·Â»Ë¼ÎÀ×ÁÚÅãÆæÊïËòÏûÐþÔÕ
ÙÚÞß"ã+ä.è7é:í<î?òJóM÷Xø[üfýikny|¨«¶ ¹$Ä%Ç)É*Ì.×/Ú3å4è8ó9ö=>BCGH L"M%Q0R3V>WA[L\O`Za]ehfkjvkyoptuy z£~®±¼¿ÊÍØÛãæñô¡3¢6¦A§D«O¬R°]±`µy¶|º»¿£À¦Ä±Å´É¿ÊÂÎÍÏÐÓÛÔÞØéÙìÝ÷Þúâãçèì!í$ñ?ù Â Ã    Å ]  µ    AMª  <       J  9  @  G  S  _  k  w        §  ³  ¿  Ë  ×  ã  ï  û        +  7  <  J  X  f  k  y        ¨  ¶  Ä  É  ×  å  ó        "  0  >  L  Z  h  v         ®  ¼  Ê  Ø  ã  ñ    3  A  O  ]  y    £  ±  ¿  Í  Û  é  ÷      !ÇM§ÿÇM§ø»ÉY·ÌM§ì»ÉY·ÌM§à»ÉY·ÌM§Ô»ÉY·ÌM§È»ÉY·ÌM§¼»ÉY·ÌM§°»ÉY·ÌM§¤»ÉY·ÌM§»ÉY·ÌM§»ÉY·ÌM§»ÉY·ÌM§t»ÉY·ÌM§h»ÉY·ÌM§\»ÉY·ÌM§P»ÉY·ÌM§D»ÉY·ÌM§8»ÉY·ÌM§,»ÉY·ÌM§ »ÉY·ÌM§»ÉY·ÌM§M§*´ Ý¶ÐÀÒM§õ*´ ©¶ÐÀÉM§ç*´ Õ¶ÐÀÔM§ÙM§Ô*´ ×¶ÐÀÒM§Æ*´ ¹¶ÐÀÉM§¸*´ «¶ÐÀÔM§ªM§¥*´ »¶ÐÀÒM§*´ ç¶ÐÀÉM§*´ Ñ¶ÐÀÔM§{M§v*´ ß¶ÐÀÒM§h*´ ·¶ÐÀÉM§Z*´ ­¶ÐÀÔM§L*´ Ý¶ÐÀÒM§>*´ É¶ÐÀÔM§0*´ Å¶ÐÀÉM§"M§*´ Û¶ÐÀÒM§*´ ë¶ÐÀÔM§*´ µ¶ÐÀÉM§ó*´ Ï¶ÐÀÒM§å*´ ¿¶ÐÀÉM§×*´ ³¶ÐÀÔM§É*´ Ó¶ÐÀÒM§»*´ Ë¶ÐÀÉM§­*´ ï¶ÐÀÔM§*´ ¯¶ÐÀÒM§*´ é¶ÐÀÉM§*´ Ç¶ÐÀÔM§u*´ ¶ÕÀ×M§g»ÙY·ÚM§\*´ ¶ÕÀÜM§N»ÞYà·ã*´ ñ¶ÀÉ¶èê¶í¶ñM§*»ÞYó·ã*´ ñ¶ÀÉ¶è¶ñM§*´ Ã¶ÐÀÜM§ þ*´ á¶ÐÀÜM§ ð*´ §¶ÕÀÜM§ â*´ ±¶ÐÀÉ¶÷ § ¸ûM§ Æ*´ Á¶ÐÀÉ¶÷ § ¸ûM§ ª*´ ±¶ÐÀÉM§ *´ Í¶ÐÀÔM§ *´ í¶ÐÀÒM§ *´ Á¶ÐÀÉM§ r*´ Ù¶ÐÀÔM§ d*´ ¯¶ÐÀÒM§ V*´ Ç¶ÐÀÔM§ H*´ é¶ÐÀÉM§ :*´ ã¶ÐÀÉM§ ,*´ ½¶ÐÀÔM§ »ÞYý·ã*´ g¶ÕÀÜ¶í¶ñM,°     b    <@	C
GJSV_bkn!w"z&'+,015§6ª:³;¶?¿@ÂDËEÎI×JÚNãOæSïTòXûYþ]^
bcgh"l+m.q7r:v<w?{J|MX[fikny|£¨¤«¨¶©¹­Ä®Ç²É³Ì·×¸Ú¼å½èÁóÂöÆÇËÌÐÑ Õ"Ö%Ú0Û3ß>àAäLåOéZê]îhïkóvôyøùýþ £®±¼
¿ÊÍØÛãæ ñ!ô%&*3+6/A0D4O5R9]:`>y?|CDH£I¦M±N´R¿SÂWÍXÐ\Û]Þaébìf÷gúklpqu!v$z?     t _1709063851737_364741t 2net.sf.jasperreports.engine.design.JRJavacCompiler