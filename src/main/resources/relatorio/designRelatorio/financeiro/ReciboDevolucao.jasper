¬í sr (net.sf.jasperreports.engine.JasperReport      'Ø L compileDatat Ljava/io/Serializable;L compileNameSuffixt Ljava/lang/String;L 
compilerClassq ~ xr -net.sf.jasperreports.engine.base.JRBaseReport      'Ø ,I PSEUDO_SERIAL_VERSION_UIDI bottomMarginI columnCountI 
columnSpacingI columnWidthZ ignorePaginationZ isFloatColumnFooterZ isSummaryNewPageZ  isSummaryWithPageHeaderAndFooterZ isTitleNewPageI 
leftMarginB orientationI 
pageHeightI 	pageWidthB 
printOrderI rightMarginI 	topMarginB whenNoDataTypeL 
backgroundt $Lnet/sf/jasperreports/engine/JRBand;L columnDirectiont 3Lnet/sf/jasperreports/engine/type/RunDirectionEnum;L columnFooterq ~ L columnHeaderq ~ [ datasetst ([Lnet/sf/jasperreports/engine/JRDataset;L defaultFontt *Lnet/sf/jasperreports/engine/JRReportFont;L defaultStylet %Lnet/sf/jasperreports/engine/JRStyle;L detailq ~ L 
detailSectiont 'Lnet/sf/jasperreports/engine/JRSection;[ fontst +[Lnet/sf/jasperreports/engine/JRReportFont;L formatFactoryClassq ~ L 
importsSett Ljava/util/Set;L languageq ~ L lastPageFooterq ~ L mainDatasett 'Lnet/sf/jasperreports/engine/JRDataset;L nameq ~ L noDataq ~ L orientationValuet 2Lnet/sf/jasperreports/engine/type/OrientationEnum;L 
pageFooterq ~ L 
pageHeaderq ~ L printOrderValuet 1Lnet/sf/jasperreports/engine/type/PrintOrderEnum;[ stylest &[Lnet/sf/jasperreports/engine/JRStyle;L summaryq ~ [ 	templatest /[Lnet/sf/jasperreports/engine/JRReportTemplate;L titleq ~ L whenNoDataTypeValuet 5Lnet/sf/jasperreports/engine/type/WhenNoDataTypeEnum;xp  wñ            q           J  ¨    #    p~r 1net.sf.jasperreports.engine.type.RunDirectionEnum          xr java.lang.Enum          xpt LTRppppppsr .net.sf.jasperreports.engine.base.JRBaseSection      'Ø [ bandst %[Lnet/sf/jasperreports/engine/JRBand;xpur %[Lnet.sf.jasperreports.engine.JRBand;Ý~ìÊ5  xp   
sr +net.sf.jasperreports.engine.base.JRBaseBand      'Ø I PSEUDO_SERIAL_VERSION_UIDI heightZ isSplitAllowedL printWhenExpressiont *Lnet/sf/jasperreports/engine/JRExpression;L 	splitTypet Ljava/lang/Byte;L splitTypeValuet 0Lnet/sf/jasperreports/engine/type/SplitTypeEnum;xr 3net.sf.jasperreports.engine.base.JRBaseElementGroup      'Ø L childrent Ljava/util/List;L elementGroupt ,Lnet/sf/jasperreports/engine/JRElementGroup;xpsr java.util.ArrayListxÒÇa I sizexp   w   sr 1net.sf.jasperreports.engine.base.JRBaseStaticText      'Ø L textq ~ xr 2net.sf.jasperreports.engine.base.JRBaseTextElement      'Ø %I PSEUDO_SERIAL_VERSION_UIDL borderq ~ L borderColort Ljava/awt/Color;L bottomBorderq ~ L bottomBorderColorq ~ (L 
bottomPaddingt Ljava/lang/Integer;L fontNameq ~ L fontSizeq ~ )L horizontalAlignmentq ~ L horizontalAlignmentValuet 6Lnet/sf/jasperreports/engine/type/HorizontalAlignEnum;L isBoldt Ljava/lang/Boolean;L isItalicq ~ +L 
isPdfEmbeddedq ~ +L isStrikeThroughq ~ +L isStyledTextq ~ +L isUnderlineq ~ +L 
leftBorderq ~ L leftBorderColorq ~ (L leftPaddingq ~ )L lineBoxt 'Lnet/sf/jasperreports/engine/JRLineBox;L lineSpacingq ~ L lineSpacingValuet 2Lnet/sf/jasperreports/engine/type/LineSpacingEnum;L markupq ~ L paddingq ~ )L pdfEncodingq ~ L pdfFontNameq ~ L 
reportFontq ~ L rightBorderq ~ L rightBorderColorq ~ (L rightPaddingq ~ )L rotationq ~ L 
rotationValuet /Lnet/sf/jasperreports/engine/type/RotationEnum;L 	topBorderq ~ L topBorderColorq ~ (L 
topPaddingq ~ )L verticalAlignmentq ~ L verticalAlignmentValuet 4Lnet/sf/jasperreports/engine/type/VerticalAlignEnum;xr .net.sf.jasperreports.engine.base.JRBaseElement      'Ø I PSEUDO_SERIAL_VERSION_UIDI heightZ isPrintInFirstWholeBandZ isPrintRepeatedValuesZ isPrintWhenDetailOverflowsZ isRemoveLineWhenBlankB positionTypeB stretchTypeI widthI xI yL 	backcolorq ~ (L defaultStyleProvidert 4Lnet/sf/jasperreports/engine/JRDefaultStyleProvider;L elementGroupq ~ "L 	forecolorq ~ (L keyq ~ L modeq ~ L 	modeValuet +Lnet/sf/jasperreports/engine/type/ModeEnum;L parentStyleq ~ L parentStyleNameReferenceq ~ L positionTypeValuet 3Lnet/sf/jasperreports/engine/type/PositionTypeEnum;L printWhenExpressionq ~ L printWhenGroupChangest %Lnet/sf/jasperreports/engine/JRGroup;L 
propertiesMapt -Lnet/sf/jasperreports/engine/JRPropertiesMap;[ propertyExpressionst 3[Lnet/sf/jasperreports/engine/JRPropertyExpression;L stretchTypeValuet 2Lnet/sf/jasperreports/engine/type/StretchTypeEnum;xp  wñ   
        =      ,pq ~ q ~ #pt 
staticText-85p~r )net.sf.jasperreports.engine.type.ModeEnum          xq ~ t OPAQUEpp~r 1net.sf.jasperreports.engine.type.PositionTypeEnum          xq ~ t FLOATpppp~r 0net.sf.jasperreports.engine.type.StretchTypeEnum          xq ~ t 
NO_STRETCH  wñpppppt 	SansSerifsr java.lang.Integerâ ¤÷8 I valuexr java.lang.Number¬à  xp   	p~r 4net.sf.jasperreports.engine.type.HorizontalAlignEnum          xq ~ t LEFTsr java.lang.BooleanÍ rÕúî Z valuexpsq ~ J pq ~ Lpq ~ Lpppsr .net.sf.jasperreports.engine.base.JRBaseLineBox      'Ø L 
bottomPaddingq ~ )L 	bottomPent +Lnet/sf/jasperreports/engine/base/JRBoxPen;L boxContainert ,Lnet/sf/jasperreports/engine/JRBoxContainer;L leftPaddingq ~ )L leftPenq ~ NL paddingq ~ )L penq ~ NL rightPaddingq ~ )L rightPenq ~ NL 
topPaddingq ~ )L topPenq ~ Nxppsr 3net.sf.jasperreports.engine.base.JRBaseBoxBottomPen      'Ø  xr -net.sf.jasperreports.engine.base.JRBaseBoxPen      'Ø L lineBoxq ~ ,xr *net.sf.jasperreports.engine.base.JRBasePen      'Ø I PSEUDO_SERIAL_VERSION_UIDL 	lineColorq ~ (L 	lineStyleq ~ L lineStyleValuet 0Lnet/sf/jasperreports/engine/type/LineStyleEnum;L 	lineWidtht Ljava/lang/Float;L penContainert ,Lnet/sf/jasperreports/engine/JRPenContainer;xp  wñsr java.awt.Color¥3u F falphaI valueL cst Ljava/awt/color/ColorSpace;[ 	frgbvaluet [F[ fvalueq ~ Zxp    ÿfffpppp~r .net.sf.jasperreports.engine.type.LineStyleEnum          xq ~ t SOLIDsr java.lang.FloatÚíÉ¢Û<ðì F valuexq ~ E    q ~ Pq ~ Pq ~ 8psr 1net.sf.jasperreports.engine.base.JRBaseBoxLeftPen      'Ø  xq ~ R  wñsq ~ X    ÿfffppppq ~ ]sq ~ _    q ~ Pq ~ Ppsq ~ R  wñppppq ~ Pq ~ Ppsr 2net.sf.jasperreports.engine.base.JRBaseBoxRightPen      'Ø  xq ~ R  wñsq ~ X    ÿfffppppq ~ ]sq ~ _    q ~ Pq ~ Ppsr 0net.sf.jasperreports.engine.base.JRBaseBoxTopPen      'Ø  xq ~ R  wñsq ~ X    ÿfffppppq ~ ]sq ~ _    q ~ Pq ~ Ppppppt 	Helveticapppppppppp~r 2net.sf.jasperreports.engine.type.VerticalAlignEnum          xq ~ t MIDDLEt DT DevoluÃ§Ã£osq ~ &  wñ   
          ¤   ,pq ~ q ~ #pt 
staticText-88pq ~ ;ppq ~ >ppppq ~ A  wñpppppt 	SansSerifq ~ Fpq ~ Hq ~ Kq ~ Lpq ~ Lpq ~ Lpppsq ~ Mpsq ~ Q  wñsq ~ X    ÿfffppppq ~ ]sq ~ _    q ~ vq ~ vq ~ spsq ~ a  wñsq ~ X    ÿfffppppq ~ ]sq ~ _    q ~ vq ~ vpsq ~ R  wñppppq ~ vq ~ vpsq ~ f  wñsq ~ X    ÿfffppppq ~ ]sq ~ _    q ~ vq ~ vpsq ~ j  wñsq ~ X    ÿfffppppq ~ ]sq ~ _    q ~ vq ~ vpppppt 	Helveticappppppppppq ~ pt ! Nome do ResponsÃ¡vel DevoluÃ§Ã£osr ,net.sf.jasperreports.engine.base.JRBaseImage      'Ø *I PSEUDO_SERIAL_VERSION_UIDI 
bookmarkLevelB evaluationTimeB hyperlinkTargetB 
hyperlinkTypeZ isLazyB onErrorTypeL anchorNameExpressionq ~ L borderq ~ L borderColorq ~ (L bottomBorderq ~ L bottomBorderColorq ~ (L 
bottomPaddingq ~ )L evaluationGroupq ~ 4L evaluationTimeValuet 5Lnet/sf/jasperreports/engine/type/EvaluationTimeEnum;L 
expressionq ~ L horizontalAlignmentq ~ L horizontalAlignmentValueq ~ *L hyperlinkAnchorExpressionq ~ L hyperlinkPageExpressionq ~ [ hyperlinkParameterst 3[Lnet/sf/jasperreports/engine/JRHyperlinkParameter;L hyperlinkReferenceExpressionq ~ L hyperlinkTooltipExpressionq ~ L isUsingCacheq ~ +L 
leftBorderq ~ L leftBorderColorq ~ (L leftPaddingq ~ )L lineBoxq ~ ,L 
linkTargetq ~ L linkTypeq ~ L onErrorTypeValuet 2Lnet/sf/jasperreports/engine/type/OnErrorTypeEnum;L paddingq ~ )L rightBorderq ~ L rightBorderColorq ~ (L rightPaddingq ~ )L 
scaleImageq ~ L scaleImageValuet 1Lnet/sf/jasperreports/engine/type/ScaleImageEnum;L 	topBorderq ~ L topBorderColorq ~ (L 
topPaddingq ~ )L verticalAlignmentq ~ L verticalAlignmentValueq ~ /xr 5net.sf.jasperreports.engine.base.JRBaseGraphicElement      'Ø I PSEUDO_SERIAL_VERSION_UIDL fillq ~ L 	fillValuet +Lnet/sf/jasperreports/engine/type/FillEnum;L linePent #Lnet/sf/jasperreports/engine/JRPen;L penq ~ xq ~ 0  wñ   '       y      pq ~ q ~ #sq ~ X    ÿÿÿÿpppt image-1pppp~q ~ =t FIX_RELATIVE_TO_TOPppppq ~ A  wîppsq ~ S  wñppppq ~ p  wñ         ppppppp~r 3net.sf.jasperreports.engine.type.EvaluationTimeEnum          xq ~ t PAGEsr 1net.sf.jasperreports.engine.base.JRBaseExpression      'Ø I id[ chunkst 0[Lnet/sf/jasperreports/engine/JRExpressionChunk;L valueClassNameq ~ L valueClassRealNameq ~ xp   ur 0[Lnet.sf.jasperreports.engine.JRExpressionChunk;mYÏÞiK£U  xp   sr 6net.sf.jasperreports.engine.base.JRBaseExpressionChunk      'Ø B typeL textq ~ xpt logoPadraoRelatoriot java.io.InputStreamppppppppq ~ Kpppsq ~ Mpsq ~ Q  wñppppq ~  q ~  q ~ psq ~ a  wñppppq ~  q ~  psq ~ R  wñppppq ~  q ~  psq ~ f  wñppppq ~  q ~  psq ~ j  wñppppq ~  q ~  pp~r 0net.sf.jasperreports.engine.type.OnErrorTypeEnum          xq ~ t BLANKpppppppppppsr 0net.sf.jasperreports.engine.base.JRBaseRectangle      'Ø L radiusq ~ )xq ~   wñ   '       N   ~   sq ~ X    ÿðððpppq ~ q ~ #pt retDadosEmpresa1ppppq ~ ppppq ~ A  wîppsq ~ S  wñpppsq ~ _>  q ~ ªpsq ~ D   
sr 0net.sf.jasperreports.engine.base.JRBaseTextField      'Ø I PSEUDO_SERIAL_VERSION_UIDI 
bookmarkLevelB evaluationTimeB hyperlinkTargetB 
hyperlinkTypeZ isStretchWithOverflowL anchorNameExpressionq ~ L evaluationGroupq ~ 4L evaluationTimeValueq ~ L 
expressionq ~ L hyperlinkAnchorExpressionq ~ L hyperlinkPageExpressionq ~ [ hyperlinkParametersq ~ L hyperlinkReferenceExpressionq ~ L hyperlinkTooltipExpressionq ~ L isBlankWhenNullq ~ +L 
linkTargetq ~ L linkTypeq ~ L patternq ~ xq ~ '  wñ   
        Ü      pq ~ q ~ #ppppppq ~ ppppq ~ A  wñpppppt Microsoft Sans Serifpppq ~ Kq ~ Kpppppppsq ~ Mpsq ~ Q  wñppppq ~ ³q ~ ³q ~ ±psq ~ a  wñppppq ~ ³q ~ ³psq ~ R  wñppppq ~ ³q ~ ³psq ~ f  wñppppq ~ ³q ~ ³psq ~ j  wñppppq ~ ³q ~ ³pppppt Helvetica-Boldppppppppppp  wñ        pp~q ~ t NOWsq ~    uq ~    sq ~ t nomeEmpresat java.lang.Stringppppppppppsq ~ °  wñ   
        Ú      pq ~ q ~ #ppppppq ~ ppppq ~ A  wñpppppt Microsoft Sans Serifpppq ~ Kq ~ Kpppppppsq ~ Mpsq ~ Q  wñppppq ~ Ãq ~ Ãq ~ Ápsq ~ a  wñppppq ~ Ãq ~ Ãpsq ~ R  wñppppq ~ Ãq ~ Ãpsq ~ f  wñppppq ~ Ãq ~ Ãpsq ~ j  wñppppq ~ Ãq ~ Ãppppppppppppppppp  wñ        ppq ~ ºsq ~    
uq ~    sq ~ t empresaVO.enderecot java.lang.Stringppppppppppsq ~ °  wñ   
       ?      pq ~ q ~ #ppppppq ~ ppppq ~ A  wñpppppt Microsoft Sans Serifpppq ~ Kq ~ Kpppppppsq ~ Mpsq ~ Q  wñppppq ~ Ðq ~ Ðq ~ Îpsq ~ a  wñppppq ~ Ðq ~ Ðpsq ~ R  wñppppq ~ Ðq ~ Ðpsq ~ f  wñppppq ~ Ðq ~ Ðpsq ~ j  wñppppq ~ Ðq ~ Ðppppppppppppppppp  wñ        ppq ~ ºsq ~    uq ~    sq ~ t empresaVO.sitesq ~ t .toLowerCase()t java.lang.Stringppppppppppsq ~ °  wñ   
        e  b   pq ~ q ~ #ppppppq ~ ppppq ~ A  wñpppppt Microsoft Sans Serifpppq ~ Kq ~ Kpppppppsq ~ Mpsq ~ Q  wñppppq ~ ßq ~ ßq ~ Ýpsq ~ a  wñppppq ~ ßq ~ ßpsq ~ R  wñppppq ~ ßq ~ ßpsq ~ f  wñppppq ~ ßq ~ ßpsq ~ j  wñppppq ~ ßq ~ ßpppppt Helvetica-Boldppppppppppp  wñ        ppq ~ ºsq ~    uq ~    sq ~ t empresaVO.cnpjt java.lang.Stringppppppppppsq ~ ©  wñ   '          â   sq ~ X    ÿðððpppq ~ q ~ #pt retDadosRecibo1ppppq ~ ppppq ~ A  wîppsq ~ S  wñpppsq ~ _>  q ~ ëpq ~ ¯sq ~ °  wñ   
        Z  b   pq ~ q ~ #ppppppq ~ ppppq ~ A  wñpppppt Microsoft Sans Serifpppq ~ Kq ~ Kpppppppsq ~ Mpsq ~ Q  wñppppq ~ òq ~ òq ~ ðpsq ~ a  wñppppq ~ òq ~ òpsq ~ R  wñppppq ~ òq ~ òpsq ~ f  wñppppq ~ òq ~ òpsq ~ j  wñppppq ~ òq ~ òppppppppppppppppp  wñ        ppq ~ ºsq ~    uq ~    sq ~ t empresaVO.fonet java.lang.Stringppppppppppsr +net.sf.jasperreports.engine.base.JRBaseLine      'Ø I PSEUDO_SERIAL_VERSION_UIDB 	directionL directionValuet 4Lnet/sf/jasperreports/engine/type/LineDirectionEnum;xq ~   wñ          p      <pq ~ q ~ #ppppppq ~ ppppq ~ A  wîppsq ~ S  wñppppq ~ ÿp  wñ ~r 2net.sf.jasperreports.engine.type.LineDirectionEnum          xq ~ t TOP_DOWNsq ~ °  wñ           Å   §   Apq ~ q ~ #pt 
textField-229pq ~ ;ppq ~ ppppq ~ A  wñpppppt Microsoft Sans Serifq ~ Fpq ~ Hq ~ Kppppppppsq ~ Mpsq ~ Q  wñsq ~ X    ÿfffppppq ~ ]sq ~ _    q ~q ~q ~psq ~ a  wñsq ~ X    ÿfffppppq ~ ]sq ~ _    q ~q ~psq ~ R  wñppppq ~q ~psq ~ f  wñsq ~ X    ÿfffppppq ~ ]sq ~ _    q ~q ~psq ~ j  wñsq ~ X    ÿfffppppq ~ ]sq ~ _    q ~q ~pppppt Helvetica-Boldppppppppppq ~ p  wñ        ppq ~ ºsq ~    uq ~    sq ~ t .responsavelDevolucao.colaboradorVO.pessoa.nomet java.lang.Stringppppppq ~ Lpppsq ~ °  wñ                 @pq ~ q ~ #pt 
textField-229pq ~ ;ppq ~ ppppq ~ A  wñpppppt Microsoft Sans Serifq ~ Fpq ~ Hq ~ Kppppppppsq ~ Mpsq ~ Q  wñsq ~ X    ÿfffppppq ~ ]sq ~ _    q ~q ~q ~psq ~ a  wñsq ~ X    ÿfffppppq ~ ]sq ~ _    q ~q ~psq ~ R  wñppppq ~q ~psq ~ f  wñsq ~ X    ÿfffppppq ~ ]sq ~ _    q ~q ~psq ~ j  wñsq ~ X    ÿfffppppq ~ ]sq ~ _    q ~q ~pppppt Helvetica-Boldppppppppppq ~ p  wñ        ppq ~ ºsq ~    uq ~    sq ~ t 
dataFormatadat java.lang.Stringppppppq ~ Lppt  sq ~ °  wñ           E     Apq ~ q ~ #pt 
textField-229pq ~ ;ppq ~ ppppq ~ A  wñpppppt Microsoft Sans Serifq ~ Fpq ~ Hq ~ Kppppppppsq ~ Mpsq ~ Q  wñsq ~ X    ÿfffppppq ~ ]sq ~ _    q ~6q ~6q ~3psq ~ a  wñsq ~ X    ÿfffppppq ~ ]sq ~ _    q ~6q ~6psq ~ R  wñppppq ~6q ~6psq ~ f  wñsq ~ X    ÿfffppppq ~ ]sq ~ _    q ~6q ~6psq ~ j  wñsq ~ X    ÿfffppppq ~ ]sq ~ _    q ~6q ~6pppppt Helvetica-Boldppppppppppq ~ p  wñ        ppq ~ ºsq ~    uq ~    sq ~ t (sq ~ t contrato.codigosq ~ t .intValue() > 0 ? sq ~ t contrato.codigosq ~ t  : sq ~ t produtoVO.codigosq ~ t )t java.lang.Integerppppppq ~ Lpppsq ~ °  wñ   
        t  ó   sq ~ X    ÿðððpppq ~ q ~ #pt 
textField-229pq ~ ;ppq ~ ppppq ~ A  wñpppppt Microsoft Sans Serifsq ~ D   pq ~ Hq ~ Kppppppppsq ~ Mpsq ~ Q  wñsq ~ X    ÿfffppppq ~ ]sq ~ _    q ~[q ~[q ~Vpsq ~ a  wñsq ~ X    ÿfffppppq ~ ]sq ~ _    q ~[q ~[psq ~ R  wñppppq ~[q ~[psq ~ f  wñsq ~ X    ÿfffppppq ~ ]sq ~ _    q ~[q ~[psq ~ j  wñsq ~ X    ÿfffppppq ~ ]sq ~ _    q ~[q ~[pppppt Helvetica-Boldppppppppppq ~ p  wñ        ppq ~ ºsq ~    uq ~    sq ~ t valorMonetariot java.lang.Stringppppppq ~ Lpppsq ~ °  wñ   
             Upq ~ q ~ #ppppppq ~ pppp~q ~ @t RELATIVE_TO_BAND_HEIGHT  wñpppppppp~q ~ Gt 	JUSTIFIEDpppppppppsq ~ Mpsq ~ Q  wñppppq ~tq ~tq ~opsq ~ a  wñppppq ~tq ~tpsq ~ R  wñppppq ~tq ~tpsq ~ f  wñppppq ~tq ~tpsq ~ j  wñppppq ~tq ~tppppppppppppppppp  wñ       ppq ~ ºsq ~    uq ~    sq ~ t informacoest java.lang.Stringppppppppppsq ~ °  wñ   
        B  ÿ   ,pq ~ q ~ #pt 
textField-229pq ~ ;ppq ~ ppppq ~ A  wñpppppt 	SansSerifq ~ Fpq ~ Hq ~ Kq ~ Lpppppppsq ~ Mpsq ~ Q  wñsq ~ X    ÿfffppppq ~ ]sq ~ _    q ~q ~q ~psq ~ a  wñsq ~ X    ÿfffppppq ~ ]sq ~ _    q ~q ~psq ~ R  wñppppq ~q ~psq ~ f  wñsq ~ X    ÿfffppppq ~ ]sq ~ _    q ~q ~psq ~ j  wñsq ~ X    ÿfffppppq ~ ]sq ~ _    q ~q ~pppppt Helvetica-Boldppppppppppq ~ p  wñ        ppq ~ ºsq ~    uq ~    sq ~ t (sq ~ t contrato.codigosq ~ t 1.intValue() > 0 ? "Cod Contrato" : "Cod Produto")t java.lang.Stringppppppq ~ Kpppxp  wñ   epppsq ~ sq ~ $   w   
sq ~ °  wñ                pq ~ q ~ppppppq ~ ppppq ~p  wñppppppppq ~rpppppppppsq ~ Mpsq ~ Q  wñppppq ~q ~q ~psq ~ a  wñppppq ~q ~psq ~ R  wñppppq ~q ~psq ~ f  wñppppq ~q ~psq ~ j  wñppppq ~q ~ppppppppppppppppp  wñ       ppq ~ ºsq ~    uq ~    sq ~ t 	descricaot java.lang.Stringppppppppppsq ~ &  wñ   
        Ö      pq ~ q ~pt 
staticText-85pq ~ ;ppq ~ ppppq ~ A  wñpppppt 	SansSerifq ~ Fpq ~ Hq ~ Kq ~ Lpq ~ Lpq ~ Kpppsq ~ Mpsq ~ Q  wñsq ~ X    ÿfffppppq ~ ]sq ~ _    q ~«q ~«q ~¨psq ~ a  wñsq ~ X    ÿfffppppq ~ ]sq ~ _    q ~«q ~«psq ~ R  wñppppq ~«q ~«psq ~ f  wñsq ~ X    ÿfffppppq ~ ]sq ~ _    q ~«q ~«psq ~ j  wñsq ~ X    ÿfffppppq ~ ]sq ~ _    q ~«q ~«pppppt 	Helveticappppppppppq ~ pt Valores do Cancelamentoxp  wñ   $pppsq ~ sq ~ $   w   
sr 0net.sf.jasperreports.engine.base.JRBaseSubreport      'Ø L connectionExpressionq ~ L dataSourceExpressionq ~ L 
expressionq ~ L isUsingCacheq ~ +[ 
parameterst 3[Lnet/sf/jasperreports/engine/JRSubreportParameter;L parametersMapExpressionq ~ [ returnValuest 5[Lnet/sf/jasperreports/engine/JRSubreportReturnValue;L runToBottomq ~ +xq ~ 0  wñ   "      Ç      pq ~ q ~»pt subreport-1ppppq ~ sq ~    uq ~    sq ~ t apresentarChequest java.lang.Booleanppppq ~ Apsq ~    uq ~    sq ~ t listaChequet (net.sf.jasperreports.engine.JRDataSourcepsq ~    uq ~    sq ~ t 
SUBREPORT_DIRsq ~ t   + "MovPagamento_cheques.jasper"t java.lang.Stringpq ~ Kur 3[Lnet.sf.jasperreports.engine.JRSubreportParameter;[£À¾B  xp   sr 9net.sf.jasperreports.engine.base.JRBaseSubreportParameter      'Ø  xr 7net.sf.jasperreports.engine.base.JRBaseDatasetParameter      'Ø L 
expressionq ~ L nameq ~ xpsq ~    uq ~    sq ~ t 
SUBREPORT_DIRt java.lang.Objectpt 
SUBREPORT_DIRsq ~Õsq ~    uq ~    sq ~ t listaChequeq ~Üpt ListaChequepppsq ~ &  wñ   
        Ö      pq ~ q ~»pt 
staticText-85pq ~ ;ppq ~ sq ~    uq ~    sq ~ t apresentarChequesq ~Æppppq ~ A  wñpppppt 	SansSerifq ~ Fpq ~ Hq ~ Kq ~ Lpq ~ Lpq ~ Kpppsq ~ Mpsq ~ Q  wñsq ~ X    ÿfffppppq ~ ]sq ~ _    q ~ëq ~ëq ~äpsq ~ a  wñsq ~ X    ÿfffppppq ~ ]sq ~ _    q ~ëq ~ëpsq ~ R  wñppppq ~ëq ~ëpsq ~ f  wñsq ~ X    ÿfffppppq ~ ]sq ~ _    q ~ëq ~ëpsq ~ j  wñsq ~ X    ÿfffppppq ~ ]sq ~ _    q ~ëq ~ëpppppt 	Helveticappppppppppq ~ pt Cheques devolvidosxp  wñ   6sq ~    uq ~    sq ~ t apresentarChequesq ~Æpppsq ~ sq ~ $   w   
sq ~ &  wñ   
        Ö      pq ~ q ~ÿpt 
staticText-85pq ~ ;ppq ~ sq ~     uq ~    sq ~ t apresentarCartoesq ~Æppppq ~ A  wñpppppt 	SansSerifq ~ Fpq ~ Hq ~ Kq ~ Lpq ~ Lpq ~ Kpppsq ~ Mpsq ~ Q  wñsq ~ X    ÿfffppppq ~ ]sq ~ _    q ~q ~q ~psq ~ a  wñsq ~ X    ÿfffppppq ~ ]sq ~ _    q ~q ~psq ~ R  wñppppq ~q ~psq ~ f  wñsq ~ X    ÿfffppppq ~ ]sq ~ _    q ~q ~psq ~ j  wñsq ~ X    ÿfffppppq ~ ]sq ~ _    q ~q ~pppppt 	Helveticappppppppppq ~ pt CartÃµes estornadossq ~½  wñ          Æ      pq ~ q ~ÿpt subreport-2ppppq ~ sq ~    !uq ~    sq ~ t apresentarCartoesq ~Æppppq ~ Apsq ~    "uq ~    sq ~ t listaCartoesq ~Ëpsq ~    #uq ~    sq ~ t 
SUBREPORT_DIRsq ~ t & + "MovPagamento_cartaocredito.jasper"t java.lang.Stringppppppxp  wñ   3sq ~    uq ~    sq ~ t apresentarCartoesq ~Æpppsq ~ sq ~ $   
w   
sq ~ ý  wñ                +pq ~ q ~-ppppppq ~ ppppq ~ A  wîppsq ~ S  wñppppq ~/p  wñ q ~sq ~ ý  wñ            a   +pq ~ q ~-ppppppq ~ ppppq ~ A  wîppsq ~ S  wñppppq ~1p  wñ q ~sq ~ °  wñ            a   ,pq ~ q ~-ppppppq ~ ppppq ~ A  wñppppppsq ~ D   p~q ~ Gt CENTERq ~ Kppppppppsq ~ Mpsq ~ Q  wñppppq ~7q ~7q ~3psq ~ a  wñppppq ~7q ~7psq ~ R  wñppppq ~7q ~7psq ~ f  wñppppq ~7q ~7psq ~ j  wñppppq ~7q ~7ppppppppppppppppq ~ p  wñ        ppq ~ ºsq ~    $uq ~    sq ~ t "Cliente: " + (sq ~ t contrato.codigosq ~ t .intValue() > 0 ? sq ~ t contrato.pessoa.nomesq ~ t  : sq ~ t produtoVO.pessoa.nomesq ~ t )t java.lang.Stringppppppppppsq ~ °  wñ           <   5   Kpq ~ q ~-pt dataImpressao1p~q ~ :t TRANSPARENTppq ~ ppppq ~ A  wñpppppt 	SansSerifsq ~ D   pq ~5pq ~ Lpppppppsq ~ Mpsq ~ Q  wñppppq ~Tq ~Tq ~Npsq ~ a  wñppppq ~Tq ~Tpsq ~ R  wñppppq ~Tq ~Tpsq ~ f  wñppppq ~Tq ~Tpsq ~ j  wñppppq ~Tq ~Tpppppt 	Helveticappppppppppq ~ p  wñ        ppq ~ ºsq ~    %uq ~    sq ~ t 
new Date()t java.util.Dateppppppq ~ Lppt dd/MM/yyyy HH:mm:sssq ~ &  wñ           1  ò   Kpq ~ q ~-ppppppq ~ ppppq ~ A  wñppppppq ~Sp~q ~ Gt RIGHTq ~ Kppppppppsq ~ Mpsq ~ Q  wñppppq ~dq ~dq ~apsq ~ a  wñppppq ~dq ~dpsq ~ R  wñppppq ~dq ~dpsq ~ f  wñppppq ~dq ~dpsq ~ j  wñppppq ~dq ~dpppppppppppppppppt Data pagamento:sq ~ °  wñ                ,pq ~ q ~-ppppppq ~ ppppq ~ A  wñppppppq ~4pq ~5q ~ Kppppppppsq ~ Mpsq ~ Q  wñppppq ~lq ~lq ~kpsq ~ a  wñppppq ~lq ~lpsq ~ R  wñppppq ~lq ~lpsq ~ f  wñppppq ~lq ~lpsq ~ j  wñppppq ~lq ~lppppppppppppppppq ~ p  wñ        ppq ~ ºsq ~    &uq ~    sq ~ t "Resp. DevoluÃ§Ã£o: " + sq ~ t .responsavelDevolucao.colaboradorVO.pessoa.nomet java.lang.Stringppppppppppsq ~ °  wñ   "       d      pq ~ q ~-ppppppq ~ ppppq ~ A  wñppppppq ~ Fppq ~ Kq ~ Kpppppppsq ~ Mpsq ~ Q  wñppppq ~zq ~zq ~ypsq ~ a  wñppppq ~zq ~zpsq ~ R  wñppppq ~zq ~zpsq ~ f  wñppppq ~zq ~zpsq ~ j  wñppppq ~zq ~zpppppt Helvetica-BoldObliqueppppppppppq ~ p  wñ        ppq ~ ºsq ~    'uq ~    	sq ~ t "* Devolvemos a " + (sq ~ t contrato.codigosq ~ t .intValue() > 0 ? sq ~ t contrato.pessoa.nomesq ~ t  : sq ~ t produtoVO.pessoa.nomesq ~ t ) +", a quantia de " +sq ~ t valorPorExtensosq ~ t 0+", proveniente de devoluÃ§Ã£o de cancelamento."t java.lang.Stringppppppppppsq ~ °  wñ           H  (   Kpq ~ q ~-pt 
textField-229pq ~ ;ppq ~ ppppq ~ A  wñpppppt Microsoft Sans Serifq ~Spq ~ Hq ~ Kppppppppsq ~ Mpsq ~ Q  wñsq ~ X    ÿfffppppq ~ ]sq ~ _    q ~q ~q ~psq ~ a  wñsq ~ X    ÿfffppppq ~ ]sq ~ _    q ~q ~psq ~ R  wñppppq ~q ~psq ~ f  wñsq ~ X    ÿfffppppq ~ ]sq ~ _    q ~q ~psq ~ j  wñsq ~ X    ÿfffppppq ~ ]sq ~ _    q ~q ~pppppt Helvetica-Boldppppppppppq ~ p  wñ        ppq ~ ºsq ~    (uq ~    sq ~ t 
dataFormatadat java.lang.Stringppppppq ~ Lppq ~2sq ~ &  wñ           1      Lpq ~ q ~-ppppppq ~ ppppq ~ A  wñppppppq ~Sppq ~ Kppppppppsq ~ Mpsq ~ Q  wñppppq ~®q ~®q ~­psq ~ a  wñppppq ~®q ~®psq ~ R  wñppppq ~®q ~®psq ~ f  wñppppq ~®q ~®psq ~ j  wñppppq ~®q ~®pppppppppppppppppt Data impressÃ£o:sq ~ ý  wñ          p      _pq ~ q ~-ppppppq ~ ppppq ~ A  wîppsq ~ S  wñpp~q ~ \t DASHEDsq ~ _?À  q ~µp  wñ q ~xp  wñ   gpppsq ~ sq ~ $   w   sq ~ °  wñ   
        Ú      pq ~ q ~ºppppppq ~ ppppq ~ A  wñpppppt Microsoft Sans Serifpppq ~ Kq ~ Kpppppppsq ~ Mpsq ~ Q  wñppppq ~¾q ~¾q ~¼psq ~ a  wñppppq ~¾q ~¾psq ~ R  wñppppq ~¾q ~¾psq ~ f  wñppppq ~¾q ~¾psq ~ j  wñppppq ~¾q ~¾ppppppppppppppppp  wñ        ppq ~ ºsq ~    )uq ~    sq ~ t empresaVO.enderecot java.lang.Stringppppppppppsq ~ ©  wñ   '       N      sq ~ X    ÿðððpppq ~ q ~ºpt retDadosEmpresa1ppppq ~ ppppq ~ A  wîppsq ~ S  wñpppsq ~ _>  q ~Épq ~ ¯sq ~ °  wñ   
       ?      pq ~ q ~ºppppppq ~ ppppq ~ A  wñpppppt Microsoft Sans Serifpppq ~ Kq ~ Kpppppppsq ~ Mpsq ~ Q  wñppppq ~Ðq ~Ðq ~Îpsq ~ a  wñppppq ~Ðq ~Ðpsq ~ R  wñppppq ~Ðq ~Ðpsq ~ f  wñppppq ~Ðq ~Ðpsq ~ j  wñppppq ~Ðq ~Ðppppppppppppppppp  wñ        ppq ~ ºsq ~    *uq ~    sq ~ t empresaVO.sitesq ~ t .toLowerCase()t java.lang.Stringppppppppppsq ~ ©  wñ   '          ä   sq ~ X    ÿðððpppq ~ q ~ºpt retDadosRecibo1ppppq ~ ppppq ~ A  wîppsq ~ S  wñpppsq ~ _>  q ~Ýpq ~ ¯sq ~ °  wñ   
        Ü      pq ~ q ~ºppppppq ~ ppppq ~ A  wñpppppt Microsoft Sans Serifpppq ~ Kq ~ Kpppppppsq ~ Mpsq ~ Q  wñppppq ~äq ~äq ~âpsq ~ a  wñppppq ~äq ~äpsq ~ R  wñppppq ~äq ~äpsq ~ f  wñppppq ~äq ~äpsq ~ j  wñppppq ~äq ~äpppppt Helvetica-Boldppppppppppp  wñ        ppq ~ ºsq ~    +uq ~    sq ~ t nomeEmpresat java.lang.Stringppppppppppsq ~ °  wñ   
        e  d   pq ~ q ~ºppppppq ~ ppppq ~ A  wñpppppt Microsoft Sans Serifpppq ~ Kq ~ Kpppppppsq ~ Mpsq ~ Q  wñppppq ~òq ~òq ~ðpsq ~ a  wñppppq ~òq ~òpsq ~ R  wñppppq ~òq ~òpsq ~ f  wñppppq ~òq ~òpsq ~ j  wñppppq ~òq ~òpppppt Helvetica-Boldppppppppppp  wñ        ppq ~ ºsq ~    ,uq ~    sq ~ t empresaVO.cnpjt java.lang.Stringppppppppppsq ~ °  wñ   
        t  õ   sq ~ X    ÿðððpppq ~ q ~ºpt 
textField-229pq ~ ;ppq ~ ppppq ~ A  wñpppppt Microsoft Sans Serifq ~Zpq ~ Hq ~ Kppppppppsq ~ Mpsq ~ Q  wñsq ~ X    ÿfffppppq ~ ]sq ~ _    q ~q ~q ~þpsq ~ a  wñsq ~ X    ÿfffppppq ~ ]sq ~ _    q ~q ~psq ~ R  wñppppq ~q ~psq ~ f  wñsq ~ X    ÿfffppppq ~ ]sq ~ _    q ~q ~psq ~ j  wñsq ~ X    ÿfffppppq ~ ]sq ~ _    q ~q ~pppppt Helvetica-Boldppppppppppq ~ p  wñ        ppq ~ ºsq ~    -uq ~    sq ~ t valorMonetariot java.lang.Stringppppppq ~ Lpppsq ~   wñ   '       y      pq ~ q ~ºsq ~ X    ÿÿÿÿpppt image-1ppppq ~ ppppq ~ A  wîppsq ~ S  wñppppq ~p  wñ         pppppppq ~ sq ~    .uq ~    sq ~ t logoPadraoRelatoriot java.io.InputStreamppppppppq ~ Kpppsq ~ Mpsq ~ Q  wñppppq ~q ~q ~psq ~ a  wñppppq ~q ~psq ~ R  wñppppq ~q ~psq ~ f  wñppppq ~q ~psq ~ j  wñppppq ~q ~ppq ~ §pppppppppppsq ~ ý  wñ          p      ?pq ~ q ~ºppppppq ~ ppppq ~ A  wîppsq ~ S  wñppppq ~%p  wñ q ~sq ~ °  wñ           Å   §   Dpq ~ q ~ºpt 
textField-229pq ~ ;ppq ~ ppppq ~ A  wñpppppt Microsoft Sans Serifq ~ Fpq ~ Hq ~ Kppppppppsq ~ Mpsq ~ Q  wñsq ~ X    ÿfffppppq ~ ]sq ~ _    q ~*q ~*q ~'psq ~ a  wñsq ~ X    ÿfffppppq ~ ]sq ~ _    q ~*q ~*psq ~ R  wñppppq ~*q ~*psq ~ f  wñsq ~ X    ÿfffppppq ~ ]sq ~ _    q ~*q ~*psq ~ j  wñsq ~ X    ÿfffppppq ~ ]sq ~ _    q ~*q ~*pppppt Helvetica-Boldppppppppppq ~ p  wñ        ppq ~ ºsq ~    /uq ~    sq ~ t .responsavelDevolucao.colaboradorVO.pessoa.nomet java.lang.Stringppppppq ~ Lpppsq ~ °  wñ   
             Xpq ~ q ~ºppppppq ~ ppppq ~p  wñppppppppq ~rpppppppppsq ~ Mpsq ~ Q  wñppppq ~?q ~?q ~>psq ~ a  wñppppq ~?q ~?psq ~ R  wñppppq ~?q ~?psq ~ f  wñppppq ~?q ~?psq ~ j  wñppppq ~?q ~?ppppppppppppppppp  wñ       ppq ~ ºsq ~    0uq ~    sq ~ t informacoest java.lang.Stringppppppppppsq ~ &  wñ   
          ¤   .pq ~ q ~ºpt 
staticText-88pq ~ ;ppq ~ >ppppq ~ A  wñpppppt 	SansSerifq ~ Fpq ~ Hq ~ Kq ~ Lpq ~ Lpq ~ Lpppsq ~ Mpsq ~ Q  wñsq ~ X    ÿfffppppq ~ ]sq ~ _    q ~Mq ~Mq ~Jpsq ~ a  wñsq ~ X    ÿfffppppq ~ ]sq ~ _    q ~Mq ~Mpsq ~ R  wñppppq ~Mq ~Mpsq ~ f  wñsq ~ X    ÿfffppppq ~ ]sq ~ _    q ~Mq ~Mpsq ~ j  wñsq ~ X    ÿfffppppq ~ ]sq ~ _    q ~Mq ~Mpppppt 	Helveticappppppppppq ~ pt ! Nome do ResponsÃ¡vel DevoluÃ§Ã£osq ~ &  wñ   
        =      .pq ~ q ~ºpt 
staticText-85pq ~ ;ppq ~ >ppppq ~ A  wñpppppt 	SansSerifq ~ Fpq ~ Hq ~ Kq ~ Lpq ~ Lpq ~ Lpppsq ~ Mpsq ~ Q  wñsq ~ X    ÿfffppppq ~ ]sq ~ _    q ~`q ~`q ~]psq ~ a  wñsq ~ X    ÿfffppppq ~ ]sq ~ _    q ~`q ~`psq ~ R  wñppppq ~`q ~`psq ~ f  wñsq ~ X    ÿfffppppq ~ ]sq ~ _    q ~`q ~`psq ~ j  wñsq ~ X    ÿfffppppq ~ ]sq ~ _    q ~`q ~`pppppt 	Helveticappppppppppq ~ pt DT DevoluÃ§Ã£osq ~ °  wñ           E     Dpq ~ q ~ºpt 
textField-229pq ~ ;ppq ~ ppppq ~ A  wñpppppt Microsoft Sans Serifq ~ Fpq ~ Hq ~ Kppppppppsq ~ Mpsq ~ Q  wñsq ~ X    ÿfffppppq ~ ]sq ~ _    q ~sq ~sq ~ppsq ~ a  wñsq ~ X    ÿfffppppq ~ ]sq ~ _    q ~sq ~spsq ~ R  wñppppq ~sq ~spsq ~ f  wñsq ~ X    ÿfffppppq ~ ]sq ~ _    q ~sq ~spsq ~ j  wñsq ~ X    ÿfffppppq ~ ]sq ~ _    q ~sq ~spppppt Helvetica-Boldppppppppppq ~ p  wñ        ppq ~ ºsq ~    1uq ~    sq ~ t (sq ~ t contrato.codigosq ~ t .intValue() > 0 ? sq ~ t contrato.codigosq ~ t  : sq ~ t produtoVO.codigosq ~ t )t java.lang.Integerppppppq ~ Lpppsq ~ °  wñ                 Cpq ~ q ~ºpt 
textField-229pq ~ ;ppq ~ ppppq ~ A  wñpppppt Microsoft Sans Serifq ~ Fpq ~ Hq ~ Kppppppppsq ~ Mpsq ~ Q  wñsq ~ X    ÿfffppppq ~ ]sq ~ _    q ~q ~q ~psq ~ a  wñsq ~ X    ÿfffppppq ~ ]sq ~ _    q ~q ~psq ~ R  wñppppq ~q ~psq ~ f  wñsq ~ X    ÿfffppppq ~ ]sq ~ _    q ~q ~psq ~ j  wñsq ~ X    ÿfffppppq ~ ]sq ~ _    q ~q ~pppppt Helvetica-Boldppppppppppq ~ p  wñ        ppq ~ ºsq ~    2uq ~    sq ~ t 
dataFormatadat java.lang.Stringppppppq ~ Lppq ~2sq ~ °  wñ   
        B  ÿ   .pq ~ q ~ºpt 
textField-229pq ~ ;ppq ~ ppppq ~ A  wñpppppt 	SansSerifq ~ Fpq ~ Hq ~ Kq ~ Lpppppppsq ~ Mpsq ~ Q  wñsq ~ X    ÿfffppppq ~ ]sq ~ _    q ~­q ~­q ~ªpsq ~ a  wñsq ~ X    ÿfffppppq ~ ]sq ~ _    q ~­q ~­psq ~ R  wñppppq ~­q ~­psq ~ f  wñsq ~ X    ÿfffppppq ~ ]sq ~ _    q ~­q ~­psq ~ j  wñsq ~ X    ÿfffppppq ~ ]sq ~ _    q ~­q ~­pppppt Helvetica-Boldppppppppppq ~ p  wñ        ppq ~ ºsq ~    3uq ~    sq ~ t (sq ~ t contrato.codigosq ~ t 1.intValue() > 0 ? "Cod Contrato" : "Cod Produto")t java.lang.Stringppppppq ~ Kpppsq ~ °  wñ   
        Ú      pq ~ q ~ºppppppq ~ ppppq ~ A  wñpppppt Microsoft Sans Serifpppq ~ Kq ~ Kpppppppsq ~ Mpsq ~ Q  wñppppq ~Çq ~Çq ~Åpsq ~ a  wñppppq ~Çq ~Çpsq ~ R  wñppppq ~Çq ~Çpsq ~ f  wñppppq ~Çq ~Çpsq ~ j  wñppppq ~Çq ~Çppppppppppppppppp  wñ        ppq ~ ºsq ~    4uq ~    sq ~ t empresaVO.enderecot java.lang.Stringppppppppppxp  wñ   ipppsq ~ sq ~ $   w   
sq ~ °  wñ                pq ~ q ~Òppppppq ~ ppppq ~p  wñppppppppq ~rpppppppppsq ~ Mpsq ~ Q  wñppppq ~Õq ~Õq ~Ôpsq ~ a  wñppppq ~Õq ~Õpsq ~ R  wñppppq ~Õq ~Õpsq ~ f  wñppppq ~Õq ~Õpsq ~ j  wñppppq ~Õq ~Õppppppppppppppppp  wñ       ppq ~ ºsq ~    5uq ~    sq ~ t 	descricaot java.lang.Stringppppppppppsq ~ &  wñ   
        Ö      pq ~ q ~Òpt 
staticText-85pq ~ ;ppq ~ >ppppq ~ A  wñpppppt 	SansSerifq ~ Fpq ~ Hq ~ Kq ~ Lpq ~ Lpq ~ Kpppsq ~ Mpsq ~ Q  wñsq ~ X    ÿfffppppq ~ ]sq ~ _    q ~ãq ~ãq ~àpsq ~ a  wñsq ~ X    ÿfffppppq ~ ]sq ~ _    q ~ãq ~ãpsq ~ R  wñppppq ~ãq ~ãpsq ~ f  wñsq ~ X    ÿfffppppq ~ ]sq ~ _    q ~ãq ~ãpsq ~ j  wñsq ~ X    ÿfffppppq ~ ]sq ~ _    q ~ãq ~ãpppppt 	Helveticappppppppppq ~ pt CÃ¡lculo do Cancelamentoxp  wñ   ,pppsq ~ sq ~ $   w   
sq ~½  wñ   "      Ç      pq ~ q ~ópt subreport-3ppppq ~ sq ~    7uq ~    sq ~ t apresentarChequesq ~Æppppq ~ Apsq ~    :uq ~    sq ~ t listaCheque2q ~Ëpsq ~    ;uq ~    sq ~ t 
SUBREPORT_DIRsq ~ t   + "MovPagamento_cheques.jasper"t java.lang.Stringpq ~ Kuq ~Ó   sq ~Õsq ~    8uq ~    sq ~ t 
SUBREPORT_DIRq ~Üpt 
SUBREPORT_DIRsq ~Õsq ~    9uq ~    sq ~ t listaChequeq ~Üpt ListaChequepppsq ~ &  wñ   
        Ö      pq ~ q ~ópt 
staticText-85pq ~ ;ppq ~ sq ~    <uq ~    sq ~ t apresentarChequesq ~Æppppq ~ A  wñpppppt 	SansSerifq ~ Fpq ~ Hq ~ Kq ~ Lpq ~ Lpq ~ Kpppsq ~ Mpsq ~ Q  wñsq ~ X    ÿfffppppq ~ ]sq ~ _    q ~q ~q ~psq ~ a  wñsq ~ X    ÿfffppppq ~ ]sq ~ _    q ~q ~psq ~ R  wñppppq ~q ~psq ~ f  wñsq ~ X    ÿfffppppq ~ ]sq ~ _    q ~q ~psq ~ j  wñsq ~ X    ÿfffppppq ~ ]sq ~ _    q ~q ~pppppt 	Helveticappppppppppq ~ pt Cheques devolvidosxp  wñ   6sq ~    6uq ~    sq ~ t apresentarChequesq ~Æpppsq ~ sq ~ $   w   
sq ~ &  wñ   
        Ö      pq ~ q ~.pt 
staticText-85pq ~ ;ppq ~ sq ~    >uq ~    sq ~ t apresentarCartoesq ~Æppppq ~ A  wñpppppt 	SansSerifq ~ Fpq ~ Hq ~ Kq ~ Lpq ~ Lpq ~ Kpppsq ~ Mpsq ~ Q  wñsq ~ X    ÿfffppppq ~ ]sq ~ _    q ~7q ~7q ~0psq ~ a  wñsq ~ X    ÿfffppppq ~ ]sq ~ _    q ~7q ~7psq ~ R  wñppppq ~7q ~7psq ~ f  wñsq ~ X    ÿfffppppq ~ ]sq ~ _    q ~7q ~7psq ~ j  wñsq ~ X    ÿfffppppq ~ ]sq ~ _    q ~7q ~7pppppt 	Helveticappppppppppq ~ pt CartÃµes estornadossq ~½  wñ         Ç      pq ~ q ~.pt subreport-4ppppq ~ ppppq ~ Apsq ~    ?uq ~    sq ~ t 
listaCartoes2q ~Ëpsq ~    @uq ~    sq ~ t 
SUBREPORT_DIRsq ~ t & + "MovPagamento_cartaocredito.jasper"t java.lang.Stringppppppxp  wñ   2sq ~    =uq ~    sq ~ t apresentarCartoesq ~Æpppsq ~ sq ~ $   
w   
sq ~ °  wñ                .pq ~ q ~Xppppppq ~ ppppq ~ A  wñppppppq ~4pq ~5q ~ Kppppppppsq ~ Mpsq ~ Q  wñppppq ~[q ~[q ~Zpsq ~ a  wñppppq ~[q ~[psq ~ R  wñppppq ~[q ~[psq ~ f  wñppppq ~[q ~[psq ~ j  wñppppq ~[q ~[ppppppppppppppppq ~ p  wñ        ppq ~ ºsq ~    Auq ~    sq ~ t "Resp. DevoluÃ§Ã£o: " + sq ~ t .responsavelDevolucao.colaboradorVO.pessoa.nomet java.lang.Stringppppppppppsq ~ ý  wñ                -pq ~ q ~Xppppppq ~ ppppq ~ A  wîppsq ~ S  wñppppq ~hp  wñ q ~sq ~ °  wñ           H  )   Mpq ~ q ~Xpt 
textField-229pq ~ ;ppq ~ ppppq ~ A  wñpppppt Microsoft Sans Serifq ~Spq ~ Hq ~ Kppppppppsq ~ Mpsq ~ Q  wñsq ~ X    ÿfffppppq ~ ]sq ~ _    q ~mq ~mq ~jpsq ~ a  wñsq ~ X    ÿfffppppq ~ ]sq ~ _    q ~mq ~mpsq ~ R  wñppppq ~mq ~mpsq ~ f  wñsq ~ X    ÿfffppppq ~ ]sq ~ _    q ~mq ~mpsq ~ j  wñsq ~ X    ÿfffppppq ~ ]sq ~ _    q ~mq ~mpppppt Helvetica-Boldppppppppppq ~ p  wñ        ppq ~ ºsq ~    Buq ~    sq ~ t 
dataFormatadat java.lang.Stringppppppq ~ Lppq ~2sq ~ °  wñ            b   .pq ~ q ~Xppppppq ~ ppppq ~ A  wñppppppq ~4pq ~5q ~ Kppppppppsq ~ Mpsq ~ Q  wñppppq ~q ~q ~psq ~ a  wñppppq ~q ~psq ~ R  wñppppq ~q ~psq ~ f  wñppppq ~q ~psq ~ j  wñppppq ~q ~ppppppppppppppppq ~ p  wñ        ppq ~ ºsq ~    Cuq ~    sq ~ t "Cliente: " +(sq ~ t contrato.codigosq ~ t .intValue() > 0 ? sq ~ t contrato.pessoa.nomesq ~ t  : sq ~ t produtoVO.pessoa.nomesq ~ t )t java.lang.Stringppppppppppsq ~ &  wñ           1  ó   Mpq ~ q ~Xppppppq ~ ppppq ~ A  wñppppppq ~Spq ~bq ~ Kppppppppsq ~ Mpsq ~ Q  wñppppq ~q ~q ~psq ~ a  wñppppq ~q ~psq ~ R  wñppppq ~q ~psq ~ f  wñppppq ~q ~psq ~ j  wñppppq ~q ~pppppppppppppppppt Data pagamento:sq ~ &  wñ           1      Npq ~ q ~Xppppppq ~ ppppq ~ A  wñppppppq ~Sppq ~ Kppppppppsq ~ Mpsq ~ Q  wñppppq ~¢q ~¢q ~¡psq ~ a  wñppppq ~¢q ~¢psq ~ R  wñppppq ~¢q ~¢psq ~ f  wñppppq ~¢q ~¢psq ~ j  wñppppq ~¢q ~¢pppppppppppppppppt Data impressÃ£o:sq ~ ý  wñ          p      apq ~ q ~Xppppppq ~ ppppq ~ A  wîppsq ~ S  wñppq ~·sq ~ _?À  q ~©p  wñ q ~sq ~ °  wñ   "       d      pq ~ q ~Xppppppq ~ ppppq ~ A  wñppppppq ~ Fppq ~ Kq ~ Kpppppppsq ~ Mpsq ~ Q  wñppppq ~­q ~­q ~¬psq ~ a  wñppppq ~­q ~­psq ~ R  wñppppq ~­q ~­psq ~ f  wñppppq ~­q ~­psq ~ j  wñppppq ~­q ~­pppppt Helvetica-BoldObliqueppppppppppq ~ p  wñ        ppq ~ ºsq ~    Duq ~    	sq ~ t "* Devolvemos a " + (sq ~ t contrato.codigosq ~ t .intValue() > 0 ? sq ~ t contrato.pessoa.nomesq ~ t  : sq ~ t produtoVO.pessoa.nomesq ~ t )  +", a quantia de " +sq ~ t valorPorExtensosq ~ t 0+", proveniente de devoluÃ§Ã£o de cancelamento."t java.lang.Stringppppppppppsq ~ ý  wñ            b   -pq ~ q ~Xppppppq ~ ppppq ~ A  wîppsq ~ S  wñppppq ~Ép  wñ q ~sq ~ °  wñ           <   6   Mpq ~ q ~Xpt dataImpressao1pq ~Pppq ~ ppppq ~ A  wñpppppt 	SansSerifq ~Spq ~5pq ~ Lpppppppsq ~ Mpsq ~ Q  wñppppq ~Îq ~Îq ~Ëpsq ~ a  wñppppq ~Îq ~Îpsq ~ R  wñppppq ~Îq ~Îpsq ~ f  wñppppq ~Îq ~Îpsq ~ j  wñppppq ~Îq ~Îpppppt 	Helveticappppppppppq ~ p  wñ        ppq ~ ºsq ~    Euq ~    sq ~ t 
new Date()t java.util.Dateppppppq ~ Lppt dd/MM/yyyy HH:mm:ssxp  wñ   gpppppsr java.util.HashSetºD¸·4  xpw   ?@     t "net.sf.jasperreports.engine.data.*t net.sf.jasperreports.engine.*t java.util.*xt javapsr .net.sf.jasperreports.engine.base.JRBaseDataset      'Ø I PSEUDO_SERIAL_VERSION_UIDZ isMainB whenResourceMissingType[ fieldst &[Lnet/sf/jasperreports/engine/JRField;L filterExpressionq ~ [ groupst &[Lnet/sf/jasperreports/engine/JRGroup;L nameq ~ [ 
parameterst *[Lnet/sf/jasperreports/engine/JRParameter;L 
propertiesMapq ~ 5L queryt %Lnet/sf/jasperreports/engine/JRQuery;L resourceBundleq ~ L scriptletClassq ~ [ 
scriptletst *[Lnet/sf/jasperreports/engine/JRScriptlet;[ 
sortFieldst *[Lnet/sf/jasperreports/engine/JRSortField;[ 	variablest )[Lnet/sf/jasperreports/engine/JRVariable;L whenResourceMissingTypeValuet >Lnet/sf/jasperreports/engine/type/WhenResourceMissingTypeEnum;xp  wñ ur &[Lnet.sf.jasperreports.engine.JRField;<ßÇN*òp  xp   sr ,net.sf.jasperreports.engine.base.JRBaseField      'Ø L descriptionq ~ L nameq ~ L 
propertiesMapq ~ 5L valueClassNameq ~ L valueClassRealNameq ~ xppt 	descricaosr +net.sf.jasperreports.engine.JRPropertiesMap      'Ø L baseq ~ 5L propertiesListq ~ !L 
propertiesMapt Ljava/util/Map;xppppt java.lang.Stringpsq ~ípt 
dataDevolucaosq ~ðpppt java.util.Datepsq ~ípt .responsavelDevolucao.colaboradorVO.pessoa.nomesq ~ðpppt java.lang.Stringpsq ~ípt contrato.codigosq ~ðpppt java.lang.Integerpsq ~ípt valorPorExtensosq ~ðpppt java.lang.Stringpsq ~ípt contrato.pessoa.nomesq ~ðpppt java.lang.Stringpsq ~ípt valorDevolucaosq ~ðpppt java.lang.Doublepsq ~ípt valorMonetariosq ~ðpppt java.lang.Stringpsq ~ípt 
dataFormatadasq ~ðpppt java.lang.Stringpsq ~ípt listaChequesq ~ðpppt java.lang.Objectpsq ~ípt listaCartoessq ~ðpppt java.lang.Objectpsq ~ípt apresentarCartoessq ~ðpppt java.lang.Booleanpsq ~ípt apresentarChequessq ~ðpppt java.lang.Booleanpsq ~ípt listaCheque2sq ~ðpppt java.lang.Objectpsq ~ípt 
listaCartoes2sq ~ðpppt java.lang.Objectpsq ~ípt valorSaldoCCApresentarsq ~ðpppt java.lang.Stringpsq ~ípt valorContratoApresentarsq ~ðpppt java.lang.Stringpsq ~ípt informacoessq ~ðpppt java.lang.Stringpsq ~ípt produtoVO.codigosq ~ðpppt java.lang.Integerpsq ~ípt produtoVO.pessoa.nomesq ~ðpppt java.lang.Stringpppt ReciboDevolucaour *[Lnet.sf.jasperreports.engine.JRParameter;" *Ã`!  xp   5sr 0net.sf.jasperreports.engine.base.JRBaseParameter      'Ø 	Z isForPromptingZ isSystemDefinedL defaultValueExpressionq ~ L descriptionq ~ L nameq ~ L nestedTypeNameq ~ L 
propertiesMapq ~ 5L valueClassNameq ~ L valueClassRealNameq ~ xpppt REPORT_PARAMETERS_MAPpsq ~ðpppt 
java.util.Mappsq ~Cppt 
JASPER_REPORTpsq ~ðpppt (net.sf.jasperreports.engine.JasperReportpsq ~Cppt REPORT_CONNECTIONpsq ~ðpppt java.sql.Connectionpsq ~Cppt REPORT_MAX_COUNTpsq ~ðpppt java.lang.Integerpsq ~Cppt REPORT_DATA_SOURCEpsq ~ðpppq ~Ëpsq ~Cppt REPORT_SCRIPTLETpsq ~ðpppt /net.sf.jasperreports.engine.JRAbstractScriptletpsq ~Cppt 
REPORT_LOCALEpsq ~ðpppt java.util.Localepsq ~Cppt REPORT_RESOURCE_BUNDLEpsq ~ðpppt java.util.ResourceBundlepsq ~Cppt REPORT_TIME_ZONEpsq ~ðpppt java.util.TimeZonepsq ~Cppt REPORT_FORMAT_FACTORYpsq ~ðpppt .net.sf.jasperreports.engine.util.FormatFactorypsq ~Cppt REPORT_CLASS_LOADERpsq ~ðpppt java.lang.ClassLoaderpsq ~Cppt REPORT_URL_HANDLER_FACTORYpsq ~ðpppt  java.net.URLStreamHandlerFactorypsq ~Cppt REPORT_FILE_RESOLVERpsq ~ðpppt -net.sf.jasperreports.engine.util.FileResolverpsq ~Cppt REPORT_TEMPLATESpsq ~ðpppt java.util.Collectionpsq ~Cppt SORT_FIELDSpsq ~ðpppt java.util.Listpsq ~Cppt REPORT_VIRTUALIZERpsq ~ðpppt )net.sf.jasperreports.engine.JRVirtualizerpsq ~Cppt IS_IGNORE_PAGINATIONpsq ~ðpppq ~Æpsq ~C  ppt tituloRelatoriopsq ~ðpppt java.lang.Stringpsq ~C  ppt nomeEmpresapsq ~ðpppt java.lang.Stringpsq ~C  ppt versaoSoftwarepsq ~ðpppt java.lang.Stringpsq ~C  ppt usuariopsq ~ðpppt java.lang.Stringpsq ~C  ppt filtrospsq ~ðpppt java.lang.Stringpsq ~C sq ~     uq ~    sq ~ t p"E:\\desenvolvimentosv\\Projeto Pacto Solucoes\\ZillyonWeb\\src\\java\\relatorio\\designRelatorio\\financeiro\\"t java.lang.Stringppt 
SUBREPORT_DIRpsq ~ðpppq ~psq ~C sq ~    uq ~    sq ~ t p"E:\\desenvolvimentosv\\Projeto Pacto Solucoes\\ZillyonWeb\\src\\java\\relatorio\\designRelatorio\\financeiro\\"t java.lang.Stringppt SUBREPORT_DIR1psq ~ðpppq ~§psq ~C  ppt dataInipsq ~ðpppt java.lang.Stringpsq ~C  ppt dataFimpsq ~ðpppt java.lang.Stringpsq ~C  ppt qtdAVpsq ~ðpppt java.lang.Stringpsq ~C  ppt qtdCApsq ~ðpppt java.lang.Stringpsq ~C  ppt qtdChequeAVpsq ~ðpppt java.lang.Stringpsq ~C  ppt qtdChequePRpsq ~ðpppt java.lang.Stringpsq ~C  ppt qtdOutropsq ~ðpppt java.lang.Stringpsq ~C  ppt valorAVpsq ~ðpppt java.lang.Doublepsq ~C  ppt valorCApsq ~ðpppt java.lang.Doublepsq ~C  ppt 
valorChequeAVpsq ~ðpppt java.lang.Doublepsq ~C  ppt 
valorChequePRpsq ~ðpppt java.lang.Doublepsq ~C  ppt 
valorOutropsq ~ðpppt java.lang.Doublepsq ~C  ppt logoPadraoRelatoriopsq ~ðpppt java.io.InputStreampsq ~C ppt qtdCDpsq ~ðpppt java.lang.Stringpsq ~C ppt valorCDpsq ~ðpppt java.lang.Doublepsq ~C sq ~    uq ~    sq ~ t p"E:\\desenvolvimentosv\\Projeto Pacto Solucoes\\ZillyonWeb\\src\\java\\relatorio\\designRelatorio\\financeiro\\"t java.lang.Stringppt SUBREPORT_DIR2psq ~ðpppq ~ëpsq ~C ppt 	codRecibopsq ~ðpppt java.lang.Stringpsq ~C ppt empresaVO.cnpjpsq ~ðpppt java.lang.Stringpsq ~C ppt empresaVO.enderecopsq ~ðpppt java.lang.Stringpsq ~C ppt empresaVO.sitepsq ~ðpppt java.lang.Stringpsq ~C ppt empresaVO.fonepsq ~ðpppt java.lang.Stringpsq ~C ppt responsavelDevolucaopsq ~ðpppt java.lang.Stringpsq ~C ppt pessoapsq ~ðpppt java.lang.Stringpsq ~C ppt contratopsq ~ðpppt java.lang.Integerpsq ~C ppt 
dataDevolucaopsq ~ðpppt java.util.Datepsq ~C ppt valorDevolucaopsq ~ðpppt java.lang.Doublepsq ~C ppt valorPorExtensopsq ~ðpppt java.lang.Stringpsq ~C ppt 	descricaopsq ~ðpppt java.lang.Stringpsq ~C ppt produtopsq ~ðpppt java.lang.Integerpsq ~ðpsq ~ $   w   
t ireport.zoomt 	ireport.xt 	ireport.yt ireport.scriptlethandlingt ireport.encodingxsr java.util.HashMapÚÁÃ`Ñ F 
loadFactorI 	thresholdxp?@     w      q ~$t 1.6934217901613537q ~(t 
ISO-8859-1q ~%t 1q ~&t 0q ~'t 0xpppppur )[Lnet.sf.jasperreports.engine.JRVariable;bæ|,·D  xp   sr /net.sf.jasperreports.engine.base.JRBaseVariable      'Ø I PSEUDO_SERIAL_VERSION_UIDB calculationB 
incrementTypeZ isSystemDefinedB 	resetTypeL calculationValuet 2Lnet/sf/jasperreports/engine/type/CalculationEnum;L 
expressionq ~ L incrementGroupq ~ 4L incrementTypeValuet 4Lnet/sf/jasperreports/engine/type/IncrementTypeEnum;L incrementerFactoryClassNameq ~ L incrementerFactoryClassRealNameq ~ L initialValueExpressionq ~ L nameq ~ L 
resetGroupq ~ 4L resetTypeValuet 0Lnet/sf/jasperreports/engine/type/ResetTypeEnum;L valueClassNameq ~ L valueClassRealNameq ~ xp  wî   ~r 0net.sf.jasperreports.engine.type.CalculationEnum          xq ~ t SYSTEMpp~r 2net.sf.jasperreports.engine.type.IncrementTypeEnum          xq ~ t NONEppsq ~    uq ~    sq ~ t new java.lang.Integer(1)q ~Spt PAGE_NUMBERp~r .net.sf.jasperreports.engine.type.ResetTypeEnum          xq ~ t REPORTq ~Spsq ~2  wî   q ~8ppq ~;ppsq ~    uq ~    sq ~ t new java.lang.Integer(1)q ~Spt 
COLUMN_NUMBERp~q ~Bt PAGEq ~Spsq ~2  wî   ~q ~7t COUNTsq ~    uq ~    sq ~ t new java.lang.Integer(1)q ~Sppq ~;ppsq ~    uq ~    sq ~ t new java.lang.Integer(0)q ~Spt REPORT_COUNTpq ~Cq ~Spsq ~2  wî   q ~Nsq ~    uq ~    sq ~ t new java.lang.Integer(1)q ~Sppq ~;ppsq ~    uq ~    sq ~ t new java.lang.Integer(0)q ~Spt 
PAGE_COUNTpq ~Kq ~Spsq ~2  wî   q ~Nsq ~    	uq ~    sq ~ t new java.lang.Integer(1)q ~Sppq ~;ppsq ~    
uq ~    sq ~ t new java.lang.Integer(0)q ~Spt COLUMN_COUNTp~q ~Bt COLUMNq ~Sp~r <net.sf.jasperreports.engine.type.WhenResourceMissingTypeEnum          xq ~ t EMPTYq ~@p~r 0net.sf.jasperreports.engine.type.OrientationEnum          xq ~ t PORTRAITpp~r /net.sf.jasperreports.engine.type.PrintOrderEnum          xq ~ t VERTICALpppp~r 3net.sf.jasperreports.engine.type.WhenNoDataTypeEnum          xq ~ t NO_PAGESsr 6net.sf.jasperreports.engine.design.JRReportCompileData      'Ø L crosstabCompileDataq ~ñL datasetCompileDataq ~ñL mainDatasetCompileDataq ~ xpsq ~)?@     w       xsq ~)?@     w       xur [B¬óøTà  xp  ?)Êþº¾   .ð $ReciboDevolucao_1484920116661_661206  ,net/sf/jasperreports/engine/fill/JREvaluator  parameter_REPORT_TIME_ZONE 2Lnet/sf/jasperreports/engine/fill/JRFillParameter; parameter_REPORT_PARAMETERS_MAP parameter_qtdCA parameter_descricao parameter_REPORT_CLASS_LOADER parameter_REPORT_DATA_SOURCE $parameter_REPORT_URL_HANDLER_FACTORY parameter_IS_IGNORE_PAGINATION parameter_contrato parameter_valorPorExtenso parameter_valorChequeAV parameter_REPORT_TEMPLATES parameter_valorOutro parameter_dataIni parameter_qtdAV parameter_REPORT_VIRTUALIZER parameter_SORT_FIELDS parameter_REPORT_SCRIPTLET parameter_valorDevolucao parameter_empresaVO46cnpj parameter_tituloRelatorio parameter_empresaVO46site parameter_qtdChequeAV parameter_dataDevolucao  parameter_REPORT_RESOURCE_BUNDLE parameter_filtros parameter_valorCD parameter_produto parameter_JASPER_REPORT parameter_usuario parameter_valorCA parameter_REPORT_FILE_RESOLVER parameter_SUBREPORT_DIR1 parameter_qtdChequePR parameter_valorChequePR parameter_SUBREPORT_DIR2 parameter_REPORT_MAX_COUNT parameter_empresaVO46endereco parameter_codRecibo parameter_REPORT_LOCALE parameter_responsavelDevolucao parameter_qtdOutro parameter_logoPadraoRelatorio parameter_pessoa parameter_REPORT_CONNECTION parameter_SUBREPORT_DIR parameter_dataFim parameter_qtdCD parameter_REPORT_FORMAT_FACTORY parameter_nomeEmpresa parameter_valorAV parameter_empresaVO46fone parameter_versaoSoftware field_produtoVO46pessoa46nome .Lnet/sf/jasperreports/engine/fill/JRFillField; field_listaCheque2 field_listaCartoes2 field_descricao field_valorContratoApresentar field_valorDevolucao field_valorSaldoCCApresentar field_contrato46codigo field_dataFormatada field_apresentarCheques field_valorPorExtenso field_produtoVO46codigo field_informacoes field_listaCartoes field_listaCheque 7field_responsavelDevolucao46colaboradorVO46pessoa46nome field_dataDevolucao field_apresentarCartoes field_contrato46pessoa46nome field_valorMonetario variable_PAGE_NUMBER 1Lnet/sf/jasperreports/engine/fill/JRFillVariable; variable_COLUMN_NUMBER variable_REPORT_COUNT variable_PAGE_COUNT variable_COLUMN_COUNT <init> ()V Code V W
  Y  	  [  	  ]  	  _ 	 	  a 
 	  c  	  e  	  g 
 	  i  	  k  	  m  	  o  	  q  	  s  	  u  	  w  	  y  	  {  	  }  	    	    	    	    	    	    	    	     	   ! 	   " 	   # 	   $ 	   % 	   & 	   ' 	   ( 	   ) 	  ¡ * 	  £ + 	  ¥ , 	  § - 	  © . 	  « / 	  ­ 0 	  ¯ 1 	  ± 2 	  ³ 3 	  µ 4 	  · 5 	  ¹ 6 	  » 7 	  ½ 8 	  ¿ 9 	  Á : 	  Ã ; <	  Å = <	  Ç > <	  É ? <	  Ë @ <	  Í A <	  Ï B <	  Ñ C <	  Ó D <	  Õ E <	  × F <	  Ù G <	  Û H <	  Ý I <	  ß J <	  á K <	  ã L <	  å M <	  ç N <	  é O <	  ë P Q	  í R Q	  ï S Q	  ñ T Q	  ó U Q	  õ LineNumberTable customizedInit 0(Ljava/util/Map;Ljava/util/Map;Ljava/util/Map;)V 
initParams (Ljava/util/Map;)V ú û
  ü 
initFields þ û
  ÿ initVars û
  REPORT_TIME_ZONE 
java/util/Map get &(Ljava/lang/Object;)Ljava/lang/Object;	
 0net/sf/jasperreports/engine/fill/JRFillParameter REPORT_PARAMETERS_MAP qtdCA 	descricao REPORT_CLASS_LOADER REPORT_DATA_SOURCE REPORT_URL_HANDLER_FACTORY IS_IGNORE_PAGINATION contrato valorPorExtenso 
valorChequeAV  REPORT_TEMPLATES" 
valorOutro$ dataIni& qtdAV( REPORT_VIRTUALIZER* SORT_FIELDS, REPORT_SCRIPTLET. valorDevolucao0 empresaVO.cnpj2 tituloRelatorio4 empresaVO.site6 qtdChequeAV8 
dataDevolucao: REPORT_RESOURCE_BUNDLE< filtros> valorCD@ produtoB 
JASPER_REPORTD usuarioF valorCAH REPORT_FILE_RESOLVERJ SUBREPORT_DIR1L qtdChequePRN 
valorChequePRP SUBREPORT_DIR2R REPORT_MAX_COUNTT empresaVO.enderecoV 	codReciboX 
REPORT_LOCALEZ responsavelDevolucao\ qtdOutro^ logoPadraoRelatorio` pessoab REPORT_CONNECTIONd 
SUBREPORT_DIRf dataFimh qtdCDj REPORT_FORMAT_FACTORYl nomeEmpresan valorAVp empresaVO.foner versaoSoftwaret produtoVO.pessoa.nomev ,net/sf/jasperreports/engine/fill/JRFillFieldx listaCheque2z 
listaCartoes2| valorContratoApresentar~ valorSaldoCCApresentar contrato.codigo 
dataFormatada apresentarCheques produtoVO.codigo informacoes listaCartoes listaCheque .responsavelDevolucao.colaboradorVO.pessoa.nome apresentarCartoes contrato.pessoa.nome valorMonetario PAGE_NUMBER /net/sf/jasperreports/engine/fill/JRFillVariable 
COLUMN_NUMBER REPORT_COUNT 
PAGE_COUNT  COLUMN_COUNT¢ evaluate (I)Ljava/lang/Object; 
Exceptions java/lang/Throwable§ eE:\desenvolvimentosv\Projeto Pacto Solucoes\ZillyonWeb\src\java\relatorio\designRelatorio\financeiro\© java/lang/Integer« (I)V V­
¬® getValue ()Ljava/lang/Object;°±

² java/io/InputStream´ java/lang/String¶ toLowerCase ()Ljava/lang/String;¸¹
·º
y² intValue ()I½¾
¬¿ Cod ContratoÁ Cod ProdutoÃ java/lang/BooleanÅ (net/sf/jasperreports/engine/JRDataSourceÇ java/lang/StringBufferÉ valueOf &(Ljava/lang/Object;)Ljava/lang/String;ËÌ
·Í (Ljava/lang/String;)V VÏ
ÊÐ MovPagamento_cheques.jasperÒ append ,(Ljava/lang/String;)Ljava/lang/StringBuffer;ÔÕ
ÊÖ toStringØ¹
ÊÙ !MovPagamento_cartaocredito.jasperÛ 	Cliente: Ý java/util/Dateß
à Y Resp. DevoluÃ§Ã£o: â * Devolvemos a ä , a quantia de æ -, proveniente de devoluÃ§Ã£o de cancelamento.è evaluateOld getOldValueë±
yì evaluateEstimated 
SourceFile !     N                 	     
               
                                                                                                     !     "     #     $     %     &     '     (     )     *     +     ,     -     .     /     0     1     2     3     4     5     6     7     8     9     :     ; <    = <    > <    ? <    @ <    A <    B <    C <    D <    E <    F <    G <    H <    I <    J <    K <    L <    M <    N <    O <    P Q    R Q    S Q    T Q    U Q     V W  X  ß    *· Z*µ \*µ ^*µ `*µ b*µ d*µ f*µ h*µ j*µ l*µ n*µ p*µ r*µ t*µ v*µ x*µ z*µ |*µ ~*µ *µ *µ *µ *µ *µ *µ *µ *µ *µ *µ *µ *µ *µ *µ *µ *µ  *µ ¢*µ ¤*µ ¦*µ ¨*µ ª*µ ¬*µ ®*µ °*µ ²*µ ´*µ ¶*µ ¸*µ º*µ ¼*µ ¾*µ À*µ Â*µ Ä*µ Æ*µ È*µ Ê*µ Ì*µ Î*µ Ð*µ Ò*µ Ô*µ Ö*µ Ø*µ Ú*µ Ü*µ Þ*µ à*µ â*µ ä*µ æ*µ è*µ ê*µ ì*µ î*µ ð*µ ò*µ ô*µ ö±    ÷  B P      	          ! " " ' # , $ 1 % 6 & ; ' @ ( E ) J * O + T , Y - ^ . c / h 0 m 1 r 2 w 3 | 4  5  6  7  8  9  :  ; ¤ < © = ® > ³ ? ¸ @ ½ A Â B Ç C Ì D Ñ E Ö F Û G à H å I ê J ï K ô L ù M þ N O P
 Q R S T! U& V+ W0 X5 Y: Z? [D \I ]N ^S _X `] ab bg cl dq ev f{ g h i   ø ù  X   4     *+· ý*,· *-·±    ÷       u  v 
 w  x  ú û  X  Ü    ð*+¹ À
À
µ \*+¹ À
À
µ ^*+¹ À
À
µ `*+¹ À
À
µ b*+¹ À
À
µ d*+¹ À
À
µ f*+¹ À
À
µ h*+¹ À
À
µ j*+¹ À
À
µ l*+¹ À
À
µ n*+!¹ À
À
µ p*+#¹ À
À
µ r*+%¹ À
À
µ t*+'¹ À
À
µ v*+)¹ À
À
µ x*++¹ À
À
µ z*+-¹ À
À
µ |*+/¹ À
À
µ ~*+1¹ À
À
µ *+3¹ À
À
µ *+5¹ À
À
µ *+7¹ À
À
µ *+9¹ À
À
µ *+;¹ À
À
µ *+=¹ À
À
µ *+?¹ À
À
µ *+A¹ À
À
µ *+C¹ À
À
µ *+E¹ À
À
µ *+G¹ À
À
µ *+I¹ À
À
µ *+K¹ À
À
µ *+M¹ À
À
µ *+O¹ À
À
µ *+Q¹ À
À
µ  *+S¹ À
À
µ ¢*+U¹ À
À
µ ¤*+W¹ À
À
µ ¦*+Y¹ À
À
µ ¨*+[¹ À
À
µ ª*+]¹ À
À
µ ¬*+_¹ À
À
µ ®*+a¹ À
À
µ °*+c¹ À
À
µ ²*+e¹ À
À
µ ´*+g¹ À
À
µ ¶*+i¹ À
À
µ ¸*+k¹ À
À
µ º*+m¹ À
À
µ ¼*+o¹ À
À
µ ¾*+q¹ À
À
µ À*+s¹ À
À
µ Â*+u¹ À
À
µ Ä±    ÷   Ú 6      &  9  L  _  r      «  ¾  Ñ  ä  ÷ 
  0 C V i |  ¢ µ È Û î   ' : M `  s ¡ ¢ £¬ ¤¿ ¥Ò ¦å §ø ¨ © ª1 «D ¬W ­j ®} ¯ °£ ±¶ ²É ³Ü ´ï µ  þ û  X  å    }*+w¹ ÀyÀyµ Æ*+{¹ ÀyÀyµ È*+}¹ ÀyÀyµ Ê*+¹ ÀyÀyµ Ì*+¹ ÀyÀyµ Î*+1¹ ÀyÀyµ Ð*+¹ ÀyÀyµ Ò*+¹ ÀyÀyµ Ô*+¹ ÀyÀyµ Ö*+¹ ÀyÀyµ Ø*+¹ ÀyÀyµ Ú*+¹ ÀyÀyµ Ü*+¹ ÀyÀyµ Þ*+¹ ÀyÀyµ à*+¹ ÀyÀyµ â*+¹ ÀyÀyµ ä*+;¹ ÀyÀyµ æ*+¹ ÀyÀyµ è*+¹ ÀyÀyµ ê*+¹ ÀyÀyµ ì±    ÷   V    ½  ¾ & ¿ 9 À L Á _ Â r Ã  Ä  Å « Æ ¾ Ç Ñ È ä É ÷ Ê
 Ë Ì0 ÍC ÎV Ïi Ð| Ñ  û  X        `*+¹ ÀÀµ î*+¹ ÀÀµ ð*+¹ ÀÀµ ò*+¡¹ ÀÀµ ô*+£¹ ÀÀµ ö±    ÷       Ù  Ú & Û 9 Ü L Ý _ Þ ¤¥ ¦    ¨ X  Æ    zMª  u       E  %  ,  3  :  F  R  ^  j  v        ¨  ¶  Ä  Õ  ã  ñ  ÿ  
  8  F  T  q        ©  ´  Â  ã  ñ  ÿ  
    )  J      ®        /  =  K  Y  g  u    ®  ¼  Ù  ç  õ        *  8  Y  g  u      ²  Ð  Þ    mªM§LªM§EªM§>»¬Y·¯M§2»¬Y·¯M§&»¬Y·¯M§»¬Y·¯M§»¬Y·¯M§»¬Y·¯M§ö»¬Y·¯M§ê»¬Y·¯M§Þ*´ °¶³ÀµM§Ð*´ ¾¶³À·M§Â*´ ¦¶³À·M§´*´ ¶³À·¶»M§£*´ ¶³À·M§*´ Â¶³À·M§*´ ä¶¼À·M§y*´ Ö¶¼À·M§k*´ Ô¶¼À¬¶À *´ Ô¶¼À¬§ 
*´ Ü¶¼À¬M§@*´ ì¶¼À·M§2*´ Þ¶¼À·M§$*´ Ô¶¼À¬¶À 	Â§ ÄM§*´ Ì¶¼À·M§ù*´ Ø¶¼ÀÆM§ë*´ Ø¶¼ÀÆM§Ý*´ ¶¶³À·M§Ï*´ â¶¼M§Ä*´ â¶¼ÀÈM§¶»ÊY*´ ¶¶³À·¸Î·ÑÓ¶×¶ÚM§*´ Ø¶¼ÀÆM§*´ è¶¼ÀÆM§y*´ è¶¼ÀÆM§k*´ è¶¼ÀÆM§]*´ à¶¼ÀÈM§O»ÊY*´ ¶¶³À·¸Î·ÑÜ¶×¶ÚM§.»ÊYÞ·Ñ*´ Ô¶¼À¬¶À *´ ê¶¼À·§ 
*´ Æ¶¼À·¶×¶ÚM§ó»àY·áM§è»ÊYã·Ñ*´ ä¶¼À·¶×¶ÚM§Ê»ÊYå·Ñ*´ Ô¶¼À¬¶À *´ ê¶¼À·§ 
*´ Æ¶¼À·¶×ç¶×*´ Ú¶¼À·¶×é¶×¶ÚM§v*´ Ö¶¼À·M§h*´ ¦¶³À·M§Z*´ ¶³À·¶»M§I*´ ¾¶³À·M§;*´ ¶³À·M§-*´ ì¶¼À·M§*´ °¶³ÀµM§*´ ä¶¼À·M§*´ Þ¶¼À·M§õ*´ Ô¶¼À¬¶À *´ Ô¶¼À¬§ 
*´ Ü¶¼À¬M§Ê*´ Ö¶¼À·M§¼*´ Ô¶¼À¬¶À 	Â§ ÄM§*´ ¦¶³À·M§*´ Ì¶¼À·M§*´ Ø¶¼ÀÆM§u*´ Ø¶¼ÀÆM§g*´ ¶¶³À·M§Y*´ â¶¼M§N*´ È¶¼ÀÈM§@»ÊY*´ ¶¶³À·¸Î·ÑÓ¶×¶ÚM§*´ Ø¶¼ÀÆM§*´ è¶¼ÀÆM§*´ è¶¼ÀÆM§ õ*´ Ê¶¼ÀÈM§ ç»ÊY*´ ¶¶³À·¸Î·ÑÜ¶×¶ÚM§ Æ»ÊYã·Ñ*´ ä¶¼À·¶×¶ÚM§ ¨*´ Ö¶¼À·M§ »ÊYÞ·Ñ*´ Ô¶¼À¬¶À *´ ê¶¼À·§ 
*´ Æ¶¼À·¶×¶ÚM§ _»ÊYå·Ñ*´ Ô¶¼À¬¶À *´ ê¶¼À·§ 
*´ Æ¶¼À·¶×ç¶×*´ Ú¶¼À·¶×é¶×¶ÚM§ »àY·áM,°    ÷  :    æ  è( ì, í/ ñ3 ò6 ö: ÷= ûF üI RU^a
jmvy#¨$«(¶)¹-Ä.Ç2Õ3Ø7ã8æ<ñ=ôAÿBF
GK8L;PFQIUTVWZq[t_`deijn©o¬s´t·xÂyÅ}ã~æñôÿ
),JM ¡¥¦ª®«±¯°´µ¹º!¾/¿2Ã=Ä@ÈKÉNÍYÎ\ÒgÓj×uØxÜÝá®â±æ¼ç¿ëÙìÜðçñêõõöøúûÿ "	*
-8;Y\gjux"#'(,²-µ1Ð2Ó6Þ7á;<@mApExM ê¥ ¦    ¨ X  Æ    zMª  u       E  %  ,  3  :  F  R  ^  j  v        ¨  ¶  Ä  Õ  ã  ñ  ÿ  
  8  F  T  q        ©  ´  Â  ã  ñ  ÿ  
    )  J      ®        /  =  K  Y  g  u    ®  ¼  Ù  ç  õ        *  8  Y  g  u      ²  Ð  Þ    mªM§LªM§EªM§>»¬Y·¯M§2»¬Y·¯M§&»¬Y·¯M§»¬Y·¯M§»¬Y·¯M§»¬Y·¯M§ö»¬Y·¯M§ê»¬Y·¯M§Þ*´ °¶³ÀµM§Ð*´ ¾¶³À·M§Â*´ ¦¶³À·M§´*´ ¶³À·¶»M§£*´ ¶³À·M§*´ Â¶³À·M§*´ ä¶íÀ·M§y*´ Ö¶íÀ·M§k*´ Ô¶íÀ¬¶À *´ Ô¶íÀ¬§ 
*´ Ü¶íÀ¬M§@*´ ì¶íÀ·M§2*´ Þ¶íÀ·M§$*´ Ô¶íÀ¬¶À 	Â§ ÄM§*´ Ì¶íÀ·M§ù*´ Ø¶íÀÆM§ë*´ Ø¶íÀÆM§Ý*´ ¶¶³À·M§Ï*´ â¶íM§Ä*´ â¶íÀÈM§¶»ÊY*´ ¶¶³À·¸Î·ÑÓ¶×¶ÚM§*´ Ø¶íÀÆM§*´ è¶íÀÆM§y*´ è¶íÀÆM§k*´ è¶íÀÆM§]*´ à¶íÀÈM§O»ÊY*´ ¶¶³À·¸Î·ÑÜ¶×¶ÚM§.»ÊYÞ·Ñ*´ Ô¶íÀ¬¶À *´ ê¶íÀ·§ 
*´ Æ¶íÀ·¶×¶ÚM§ó»àY·áM§è»ÊYã·Ñ*´ ä¶íÀ·¶×¶ÚM§Ê»ÊYå·Ñ*´ Ô¶íÀ¬¶À *´ ê¶íÀ·§ 
*´ Æ¶íÀ·¶×ç¶×*´ Ú¶íÀ·¶×é¶×¶ÚM§v*´ Ö¶íÀ·M§h*´ ¦¶³À·M§Z*´ ¶³À·¶»M§I*´ ¾¶³À·M§;*´ ¶³À·M§-*´ ì¶íÀ·M§*´ °¶³ÀµM§*´ ä¶íÀ·M§*´ Þ¶íÀ·M§õ*´ Ô¶íÀ¬¶À *´ Ô¶íÀ¬§ 
*´ Ü¶íÀ¬M§Ê*´ Ö¶íÀ·M§¼*´ Ô¶íÀ¬¶À 	Â§ ÄM§*´ ¦¶³À·M§*´ Ì¶íÀ·M§*´ Ø¶íÀÆM§u*´ Ø¶íÀÆM§g*´ ¶¶³À·M§Y*´ â¶íM§N*´ È¶íÀÈM§@»ÊY*´ ¶¶³À·¸Î·ÑÓ¶×¶ÚM§*´ Ø¶íÀÆM§*´ è¶íÀÆM§*´ è¶íÀÆM§ õ*´ Ê¶íÀÈM§ ç»ÊY*´ ¶¶³À·¸Î·ÑÜ¶×¶ÚM§ Æ»ÊYã·Ñ*´ ä¶íÀ·¶×¶ÚM§ ¨*´ Ö¶íÀ·M§ »ÊYÞ·Ñ*´ Ô¶íÀ¬¶À *´ ê¶íÀ·§ 
*´ Æ¶íÀ·¶×¶ÚM§ _»ÊYå·Ñ*´ Ô¶íÀ¬¶À *´ ê¶íÀ·§ 
*´ Æ¶íÀ·¶×ç¶×*´ Ú¶íÀ·¶×é¶×¶ÚM§ »àY·áM,°    ÷  :   V X(\,]/a3b6f:g=kFlIpRqUu^vazj{mvy¨«¶¹ÄÇ¢Õ£Ø§ã¨æ¬ñ­ô±ÿ²¶
·»8¼;ÀFÁIÅTÆWÊqËtÏÐÔÕÙÚÞ©ß¬ã´ä·èÂéÅíãîæòñóô÷ÿøü
ý),JM®± $%)*!.//23=4@8K9N=Y>\BgCjGuHxLMQ®R±V¼W¿[Ù\Ü`çaêeõføjkoptu"y*z-~8;Y\gjux²µ¡Ð¢Ó¦Þ§á«¬°m±pµx½ î¥ ¦    ¨ X  Æ    zMª  u       E  %  ,  3  :  F  R  ^  j  v        ¨  ¶  Ä  Õ  ã  ñ  ÿ  
  8  F  T  q        ©  ´  Â  ã  ñ  ÿ  
    )  J      ®        /  =  K  Y  g  u    ®  ¼  Ù  ç  õ        *  8  Y  g  u      ²  Ð  Þ    mªM§LªM§EªM§>»¬Y·¯M§2»¬Y·¯M§&»¬Y·¯M§»¬Y·¯M§»¬Y·¯M§»¬Y·¯M§ö»¬Y·¯M§ê»¬Y·¯M§Þ*´ °¶³ÀµM§Ð*´ ¾¶³À·M§Â*´ ¦¶³À·M§´*´ ¶³À·¶»M§£*´ ¶³À·M§*´ Â¶³À·M§*´ ä¶¼À·M§y*´ Ö¶¼À·M§k*´ Ô¶¼À¬¶À *´ Ô¶¼À¬§ 
*´ Ü¶¼À¬M§@*´ ì¶¼À·M§2*´ Þ¶¼À·M§$*´ Ô¶¼À¬¶À 	Â§ ÄM§*´ Ì¶¼À·M§ù*´ Ø¶¼ÀÆM§ë*´ Ø¶¼ÀÆM§Ý*´ ¶¶³À·M§Ï*´ â¶¼M§Ä*´ â¶¼ÀÈM§¶»ÊY*´ ¶¶³À·¸Î·ÑÓ¶×¶ÚM§*´ Ø¶¼ÀÆM§*´ è¶¼ÀÆM§y*´ è¶¼ÀÆM§k*´ è¶¼ÀÆM§]*´ à¶¼ÀÈM§O»ÊY*´ ¶¶³À·¸Î·ÑÜ¶×¶ÚM§.»ÊYÞ·Ñ*´ Ô¶¼À¬¶À *´ ê¶¼À·§ 
*´ Æ¶¼À·¶×¶ÚM§ó»àY·áM§è»ÊYã·Ñ*´ ä¶¼À·¶×¶ÚM§Ê»ÊYå·Ñ*´ Ô¶¼À¬¶À *´ ê¶¼À·§ 
*´ Æ¶¼À·¶×ç¶×*´ Ú¶¼À·¶×é¶×¶ÚM§v*´ Ö¶¼À·M§h*´ ¦¶³À·M§Z*´ ¶³À·¶»M§I*´ ¾¶³À·M§;*´ ¶³À·M§-*´ ì¶¼À·M§*´ °¶³ÀµM§*´ ä¶¼À·M§*´ Þ¶¼À·M§õ*´ Ô¶¼À¬¶À *´ Ô¶¼À¬§ 
*´ Ü¶¼À¬M§Ê*´ Ö¶¼À·M§¼*´ Ô¶¼À¬¶À 	Â§ ÄM§*´ ¦¶³À·M§*´ Ì¶¼À·M§*´ Ø¶¼ÀÆM§u*´ Ø¶¼ÀÆM§g*´ ¶¶³À·M§Y*´ â¶¼M§N*´ È¶¼ÀÈM§@»ÊY*´ ¶¶³À·¸Î·ÑÓ¶×¶ÚM§*´ Ø¶¼ÀÆM§*´ è¶¼ÀÆM§*´ è¶¼ÀÆM§ õ*´ Ê¶¼ÀÈM§ ç»ÊY*´ ¶¶³À·¸Î·ÑÜ¶×¶ÚM§ Æ»ÊYã·Ñ*´ ä¶¼À·¶×¶ÚM§ ¨*´ Ö¶¼À·M§ »ÊYÞ·Ñ*´ Ô¶¼À¬¶À *´ ê¶¼À·§ 
*´ Æ¶¼À·¶×¶ÚM§ _»ÊYå·Ñ*´ Ô¶¼À¬¶À *´ ê¶¼À·§ 
*´ Æ¶¼À·¶×ç¶×*´ Ú¶¼À·¶×é¶×¶ÚM§ »àY·áM,°    ÷  :   Æ È(Ì,Í/Ñ3Ò6Ö:×=ÛFÜIàRáUå^æaêjëmïvðyôõùúþÿ¨«¶	¹
ÄÇÕØãæñô!ÿ"&
'+8,;0F1I5T6W:q;t?@DEIJN©O¬S´T·XÂYÅ]ã^æbñcôgÿhl
mqrv)w,{J|M®±!/2£=¤@¨K©N­Y®\²g³j·u¸x¼½Á®Â±Æ¼Ç¿ËÙÌÜÐçÑêÕõÖøÚÛßàäå"é*ê-î8ï;óYô\øgùjýuþx²
µÐÓÞá m!p%x- ï    t _1484920116661_661206t 2net.sf.jasperreports.engine.design.JRJavacCompiler