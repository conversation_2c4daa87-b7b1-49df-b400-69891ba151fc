¬í sr (net.sf.jasperreports.engine.JasperReport      'Ø L compileDatat Ljava/io/Serializable;L compileNameSuffixt Ljava/lang/String;L 
compilerClassq ~ xr -net.sf.jasperreports.engine.base.JRBaseReport      'Ø ,I PSEUDO_SERIAL_VERSION_UIDI bottomMarginI columnCountI 
columnSpacingI columnWidthZ ignorePaginationZ isFloatColumnFooterZ isSummaryNewPageZ  isSummaryWithPageHeaderAndFooterZ isTitleNewPageI 
leftMarginB orientationI 
pageHeightI 	pageWidthB 
printOrderI rightMarginI 	topMarginB whenNoDataTypeL 
backgroundt $Lnet/sf/jasperreports/engine/JRBand;L columnDirectiont 3Lnet/sf/jasperreports/engine/type/RunDirectionEnum;L columnFooterq ~ L columnHeaderq ~ [ datasetst ([Lnet/sf/jasperreports/engine/JRDataset;L defaultFontt *Lnet/sf/jasperreports/engine/JRReportFont;L defaultStylet %Lnet/sf/jasperreports/engine/JRStyle;L detailq ~ L 
detailSectiont 'Lnet/sf/jasperreports/engine/JRSection;[ fontst +[Lnet/sf/jasperreports/engine/JRReportFont;L formatFactoryClassq ~ L 
importsSett Ljava/util/Set;L languageq ~ L lastPageFooterq ~ L mainDatasett 'Lnet/sf/jasperreports/engine/JRDataset;L nameq ~ L noDataq ~ L orientationValuet 2Lnet/sf/jasperreports/engine/type/OrientationEnum;L 
pageFooterq ~ L 
pageHeaderq ~ L printOrderValuet 1Lnet/sf/jasperreports/engine/type/PrintOrderEnum;[ stylest &[Lnet/sf/jasperreports/engine/JRStyle;L summaryq ~ [ 	templatest /[Lnet/sf/jasperreports/engine/JRReportTemplate;L titleq ~ L whenNoDataTypeValuet 5Lnet/sf/jasperreports/engine/type/WhenNoDataTypeEnum;xp  wñ            q           J  ¨    #    p~r 1net.sf.jasperreports.engine.type.RunDirectionEnum          xr java.lang.Enum          xpt LTRsr +net.sf.jasperreports.engine.base.JRBaseBand      'Ø I PSEUDO_SERIAL_VERSION_UIDI heightZ isSplitAllowedL printWhenExpressiont *Lnet/sf/jasperreports/engine/JRExpression;L 	splitTypet Ljava/lang/Byte;L splitTypeValuet 0Lnet/sf/jasperreports/engine/type/SplitTypeEnum;xr 3net.sf.jasperreports.engine.base.JRBaseElementGroup      'Ø L childrent Ljava/util/List;L elementGroupt ,Lnet/sf/jasperreports/engine/JRElementGroup;xpsr java.util.ArrayListxÒÇa I sizexp   w   sr +net.sf.jasperreports.engine.base.JRBaseLine      'Ø I PSEUDO_SERIAL_VERSION_UIDB 	directionL directionValuet 4Lnet/sf/jasperreports/engine/type/LineDirectionEnum;xr 5net.sf.jasperreports.engine.base.JRBaseGraphicElement      'Ø I PSEUDO_SERIAL_VERSION_UIDL fillq ~ L 	fillValuet +Lnet/sf/jasperreports/engine/type/FillEnum;L linePent #Lnet/sf/jasperreports/engine/JRPen;L penq ~ xr .net.sf.jasperreports.engine.base.JRBaseElement      'Ø I PSEUDO_SERIAL_VERSION_UIDI heightZ isPrintInFirstWholeBandZ isPrintRepeatedValuesZ isPrintWhenDetailOverflowsZ isRemoveLineWhenBlankB positionTypeB stretchTypeI widthI xI yL 	backcolort Ljava/awt/Color;L defaultStyleProvidert 4Lnet/sf/jasperreports/engine/JRDefaultStyleProvider;L elementGroupq ~ L 	forecolorq ~ 'L keyq ~ L modeq ~ L 	modeValuet +Lnet/sf/jasperreports/engine/type/ModeEnum;L parentStyleq ~ L parentStyleNameReferenceq ~ L positionTypeValuet 3Lnet/sf/jasperreports/engine/type/PositionTypeEnum;L printWhenExpressionq ~ L printWhenGroupChangest %Lnet/sf/jasperreports/engine/JRGroup;L 
propertiesMapt -Lnet/sf/jasperreports/engine/JRPropertiesMap;[ propertyExpressionst 3[Lnet/sf/jasperreports/engine/JRPropertyExpression;L stretchTypeValuet 2Lnet/sf/jasperreports/engine/type/StretchTypeEnum;xp  wñ             $   npq ~ q ~ pppppp~r 1net.sf.jasperreports.engine.type.PositionTypeEnum          xq ~ t FIX_RELATIVE_TO_TOPpppp~r 0net.sf.jasperreports.engine.type.StretchTypeEnum          xq ~ t 
NO_STRETCH  wîppsr *net.sf.jasperreports.engine.base.JRBasePen      'Ø I PSEUDO_SERIAL_VERSION_UIDL 	lineColorq ~ 'L 	lineStyleq ~ L lineStyleValuet 0Lnet/sf/jasperreports/engine/type/LineStyleEnum;L 	lineWidtht Ljava/lang/Float;L penContainert ,Lnet/sf/jasperreports/engine/JRPenContainer;xp  wñppppq ~ /p  wñ ~r 2net.sf.jasperreports.engine.type.LineDirectionEnum          xq ~ t TOP_DOWNsr 0net.sf.jasperreports.engine.base.JRBaseTextField      'Ø I PSEUDO_SERIAL_VERSION_UIDI 
bookmarkLevelB evaluationTimeB hyperlinkTargetB 
hyperlinkTypeZ isStretchWithOverflowL anchorNameExpressionq ~ L evaluationGroupq ~ +L evaluationTimeValuet 5Lnet/sf/jasperreports/engine/type/EvaluationTimeEnum;L 
expressionq ~ L hyperlinkAnchorExpressionq ~ L hyperlinkPageExpressionq ~ [ hyperlinkParameterst 3[Lnet/sf/jasperreports/engine/JRHyperlinkParameter;L hyperlinkReferenceExpressionq ~ L hyperlinkTooltipExpressionq ~ L isBlankWhenNullt Ljava/lang/Boolean;L 
linkTargetq ~ L linkTypeq ~ L patternq ~ xr 2net.sf.jasperreports.engine.base.JRBaseTextElement      'Ø %I PSEUDO_SERIAL_VERSION_UIDL borderq ~ L borderColorq ~ 'L bottomBorderq ~ L bottomBorderColorq ~ 'L 
bottomPaddingt Ljava/lang/Integer;L fontNameq ~ L fontSizeq ~ CL horizontalAlignmentq ~ L horizontalAlignmentValuet 6Lnet/sf/jasperreports/engine/type/HorizontalAlignEnum;L isBoldq ~ AL isItalicq ~ AL 
isPdfEmbeddedq ~ AL isStrikeThroughq ~ AL isStyledTextq ~ AL isUnderlineq ~ AL 
leftBorderq ~ L leftBorderColorq ~ 'L leftPaddingq ~ CL lineBoxt 'Lnet/sf/jasperreports/engine/JRLineBox;L lineSpacingq ~ L lineSpacingValuet 2Lnet/sf/jasperreports/engine/type/LineSpacingEnum;L markupq ~ L paddingq ~ CL pdfEncodingq ~ L pdfFontNameq ~ L 
reportFontq ~ L rightBorderq ~ L rightBorderColorq ~ 'L rightPaddingq ~ CL rotationq ~ L 
rotationValuet /Lnet/sf/jasperreports/engine/type/RotationEnum;L 	topBorderq ~ L topBorderColorq ~ 'L 
topPaddingq ~ CL verticalAlignmentq ~ L verticalAlignmentValuet 4Lnet/sf/jasperreports/engine/type/VerticalAlignEnum;xq ~ &  wñ          C      qpq ~ q ~ ppppppq ~ 1ppppq ~ 4  wñppppppsr java.lang.Integerâ ¤÷8 I valuexr java.lang.Number¬à  xp   p~r 4net.sf.jasperreports.engine.type.HorizontalAlignEnum          xq ~ t CENTERsr java.lang.BooleanÍ rÕúî Z valuexpppppppppsr .net.sf.jasperreports.engine.base.JRBaseLineBox      'Ø L 
bottomPaddingq ~ CL 	bottomPent +Lnet/sf/jasperreports/engine/base/JRBoxPen;L boxContainert ,Lnet/sf/jasperreports/engine/JRBoxContainer;L leftPaddingq ~ CL leftPenq ~ SL paddingq ~ CL penq ~ SL rightPaddingq ~ CL rightPenq ~ SL 
topPaddingq ~ CL topPenq ~ Sxppsr 3net.sf.jasperreports.engine.base.JRBaseBoxBottomPen      'Ø  xr -net.sf.jasperreports.engine.base.JRBaseBoxPen      'Ø L lineBoxq ~ Exq ~ 6  wñppppq ~ Uq ~ Uq ~ Ipsr 1net.sf.jasperreports.engine.base.JRBaseBoxLeftPen      'Ø  xq ~ W  wñppppq ~ Uq ~ Upsq ~ W  wñppppq ~ Uq ~ Upsr 2net.sf.jasperreports.engine.base.JRBaseBoxRightPen      'Ø  xq ~ W  wñppppq ~ Uq ~ Upsr 0net.sf.jasperreports.engine.base.JRBaseBoxTopPen      'Ø  xq ~ W  wñppppq ~ Uq ~ Upppppppppppppppp~r 2net.sf.jasperreports.engine.type.VerticalAlignEnum          xq ~ t MIDDLE  wñ        pp~r 3net.sf.jasperreports.engine.type.EvaluationTimeEnum          xq ~ t NOWsr 1net.sf.jasperreports.engine.base.JRBaseExpression      'Ø I id[ chunkst 0[Lnet/sf/jasperreports/engine/JRExpressionChunk;L valueClassNameq ~ L valueClassRealNameq ~ xp   )ur 0[Lnet.sf.jasperreports.engine.JRExpressionChunk;mYÏÞiK£U  xp   sr 6net.sf.jasperreports.engine.base.JRBaseExpressionChunk      'Ø B typeL textq ~ xpt "Cliente: " + sq ~ kt nomesq ~ kt % + ", assinatura digital biomÃ©trica"t java.lang.Stringppppppppppsq ~ >  wñ   `       e       pq ~ q ~ ppppppq ~ 1ppppq ~ 4  wñppppppq ~ Lpppppppppppsq ~ Rpsq ~ V  wñppppq ~ tq ~ tq ~ spsq ~ Y  wñppppq ~ tq ~ tpsq ~ W  wñppppq ~ tq ~ tpsq ~ \  wñppppq ~ tq ~ tpsq ~ ^  wñppppq ~ tq ~ tpppppppppppppppp~q ~ `t BOTTOM  wñ        ppq ~ dsq ~ f   *uq ~ i   sq ~ kt assinaturaDigitalBiometriat java.lang.Stringppppppppppsq ~ !  wñ             ±   »pq ~ q ~ ppppppq ~ 1ppppq ~ 4  wîppsq ~ 6  wñppppq ~ p  wñ q ~ <sq ~ >  wñ           <     Ípq ~ q ~ pt dataImpressao1p~r )net.sf.jasperreports.engine.type.ModeEnum          xq ~ t TRANSPARENTppq ~ 1ppppq ~ 4  wñpppppt 	SansSerifsq ~ J   pq ~ Npsq ~ P pppppppsq ~ Rpsq ~ V  wñppppq ~ q ~ q ~ psq ~ Y  wñppppq ~ q ~ psq ~ W  wñppppq ~ q ~ psq ~ \  wñppppq ~ q ~ psq ~ ^  wñppppq ~ q ~ pppppt 	Helveticappppppppppq ~ a  wñ        ppq ~ dsq ~ f   +uq ~ i   sq ~ kt 
new Date()t java.util.Dateppppppq ~ ppt dd/MM/yyyy HH:mm:sssq ~ >  wñ             ²   ½pq ~ q ~ ppppppq ~ 1ppppq ~ 4  wñppppppq ~ Lpq ~ Nq ~ Qppppppppsq ~ Rpsq ~ V  wñppppq ~ q ~ q ~ psq ~ Y  wñppppq ~ q ~ psq ~ W  wñppppq ~ q ~ psq ~ \  wñppppq ~ q ~ psq ~ ^  wñppppq ~ q ~ ppppppppppppppppq ~ a  wñ        ppq ~ dsq ~ f   ,uq ~ i   sq ~ kt "Resp. OperaÃ§Ã£o: " + sq ~ kt responsavel.nomet java.lang.Stringppppppppppsr 1net.sf.jasperreports.engine.base.JRBaseStaticText      'Ø L textq ~ xq ~ B  wñ           1  S   Ípq ~ q ~ ppppppq ~ 1ppppq ~ 4  wñppppppq ~ ppq ~ Qppppppppsq ~ Rpsq ~ V  wñppppq ~ ¨q ~ ¨q ~ §psq ~ Y  wñppppq ~ ¨q ~ ¨psq ~ W  wñppppq ~ ¨q ~ ¨psq ~ \  wñppppq ~ ¨q ~ ¨psq ~ ^  wñppppq ~ ¨q ~ ¨pppppppppppppppppt Data impressÃ£o:sq ~ !  wñ          p      ßpq ~ q ~ ppppppq ~ 1ppppq ~ 4  wîppsq ~ 6  wñpp~r .net.sf.jasperreports.engine.type.LineStyleEnum          xq ~ t DASHEDsr java.lang.FloatÚíÉ¢Û<ðì F valuexq ~ K?À  q ~ ¯p  wñ q ~ <xp  wñ   àppppppppsr .net.sf.jasperreports.engine.base.JRBaseSection      'Ø [ bandst %[Lnet/sf/jasperreports/engine/JRBand;xpur %[Lnet.sf.jasperreports.engine.JRBand;Ý~ìÊ5  xp   sq ~ sq ~    w   sq ~ ¦  wñ   
        v      ,pq ~ q ~ »pt 
staticText-85p~q ~ t OPAQUEpp~q ~ 0t FLOATppppq ~ 4  wñpppppt 	SansSerifsq ~ J   	p~q ~ Mt LEFTq ~ Qq ~ pq ~ pq ~ pppsq ~ Rpsq ~ V  wñsr java.awt.Color¥3u F falphaI valueL cst Ljava/awt/color/ColorSpace;[ 	frgbvaluet [F[ fvalueq ~ Ëxp    ÿfffpppp~q ~ ±t SOLIDsq ~ ´    q ~ Çq ~ Çq ~ ½psq ~ Y  wñsq ~ É    ÿfffppppq ~ Ísq ~ ´    q ~ Çq ~ Çpsq ~ W  wñppppq ~ Çq ~ Çpsq ~ \  wñsq ~ É    ÿfffppppq ~ Ísq ~ ´    q ~ Çq ~ Çpsq ~ ^  wñsq ~ É    ÿfffppppq ~ Ísq ~ ´    q ~ Çq ~ Çpppppt 	Helveticappppppppppq ~ at 
OperaÃ§Ã£osq ~ ¦  wñ   
        ´  ¼   ,pq ~ q ~ »pt 
staticText-88pq ~ ¿ppq ~ Áppppq ~ 4  wñpppppt 	SansSerifq ~ Äpq ~ Åq ~ Qq ~ pq ~ pq ~ pppsq ~ Rpsq ~ V  wñsq ~ É    ÿfffppppq ~ Ísq ~ ´    q ~ ßq ~ ßq ~ Üpsq ~ Y  wñsq ~ É    ÿfffppppq ~ Ísq ~ ´    q ~ ßq ~ ßpsq ~ W  wñppppq ~ ßq ~ ßpsq ~ \  wñsq ~ É    ÿfffppppq ~ Ísq ~ ´    q ~ ßq ~ ßpsq ~ ^  wñsq ~ É    ÿfffppppq ~ Ísq ~ ´    q ~ ßq ~ ßpppppt 	Helveticappppppppppq ~ at 
 ResponsÃ¡velsr ,net.sf.jasperreports.engine.base.JRBaseImage      'Ø *I PSEUDO_SERIAL_VERSION_UIDI 
bookmarkLevelB evaluationTimeB hyperlinkTargetB 
hyperlinkTypeZ isLazyB onErrorTypeL anchorNameExpressionq ~ L borderq ~ L borderColorq ~ 'L bottomBorderq ~ L bottomBorderColorq ~ 'L 
bottomPaddingq ~ CL evaluationGroupq ~ +L evaluationTimeValueq ~ ?L 
expressionq ~ L horizontalAlignmentq ~ L horizontalAlignmentValueq ~ DL hyperlinkAnchorExpressionq ~ L hyperlinkPageExpressionq ~ [ hyperlinkParametersq ~ @L hyperlinkReferenceExpressionq ~ L hyperlinkTooltipExpressionq ~ L isUsingCacheq ~ AL 
leftBorderq ~ L leftBorderColorq ~ 'L leftPaddingq ~ CL lineBoxq ~ EL 
linkTargetq ~ L linkTypeq ~ L onErrorTypeValuet 2Lnet/sf/jasperreports/engine/type/OnErrorTypeEnum;L paddingq ~ CL rightBorderq ~ L rightBorderColorq ~ 'L rightPaddingq ~ CL 
scaleImageq ~ L scaleImageValuet 1Lnet/sf/jasperreports/engine/type/ScaleImageEnum;L 	topBorderq ~ L topBorderColorq ~ 'L 
topPaddingq ~ CL verticalAlignmentq ~ L verticalAlignmentValueq ~ Hxq ~ #  wñ   '       y      pq ~ q ~ »sq ~ É    ÿÿÿÿpppt image-1ppppq ~ 1ppppq ~ 4  wîppsq ~ 6  wñppppq ~ òp  wñ         ppppppp~q ~ ct PAGEsq ~ f   uq ~ i   sq ~ kt logoPadraoRelatoriot java.io.InputStreamppppppppq ~ Qpppsq ~ Rpsq ~ V  wñppppq ~ ýq ~ ýq ~ òpsq ~ Y  wñppppq ~ ýq ~ ýpsq ~ W  wñppppq ~ ýq ~ ýpsq ~ \  wñppppq ~ ýq ~ ýpsq ~ ^  wñppppq ~ ýq ~ ýpp~r 0net.sf.jasperreports.engine.type.OnErrorTypeEnum          xq ~ t BLANKpppppppppppsr 0net.sf.jasperreports.engine.base.JRBaseRectangle      'Ø L radiusq ~ Cxq ~ #  wñ   '       N   ~   sq ~ É    ÿðððpppq ~ q ~ »pt retDadosEmpresa1ppppq ~ 1ppppq ~ 4  wîppsq ~ 6  wñpppsq ~ ´>  q ~psq ~ J   
sq ~ >  wñ   
        Ü      pq ~ q ~ »ppppppq ~ 1ppppq ~ 4  wñpppppt Microsoft Sans Serifpppq ~ Qq ~ Qpppppppsq ~ Rpsq ~ V  wñppppq ~q ~q ~
psq ~ Y  wñppppq ~q ~psq ~ W  wñppppq ~q ~psq ~ \  wñppppq ~q ~psq ~ ^  wñppppq ~q ~pppppt Helvetica-Boldppppppppppp  wñ        ppq ~ dsq ~ f   uq ~ i   sq ~ kt nomeEmpresat java.lang.Stringppppppppppsq ~ >  wñ   
       ?      pq ~ q ~ »ppppppq ~ 1ppppq ~ 4  wñpppppt Microsoft Sans Serifpppq ~ Qq ~ Qpppppppsq ~ Rpsq ~ V  wñppppq ~q ~q ~psq ~ Y  wñppppq ~q ~psq ~ W  wñppppq ~q ~psq ~ \  wñppppq ~q ~psq ~ ^  wñppppq ~q ~ppppppppppppppppp  wñ        ppq ~ dsq ~ f   
uq ~ i   sq ~ kt empresaVO.enderecot java.lang.Stringppppppppppsq ~ >  wñ   
       ?      pq ~ q ~ »ppppppq ~ 1ppppq ~ 4  wñpppppt Microsoft Sans Serifpppq ~ Qq ~ Qpppppppsq ~ Rpsq ~ V  wñppppq ~*q ~*q ~(psq ~ Y  wñppppq ~*q ~*psq ~ W  wñppppq ~*q ~*psq ~ \  wñppppq ~*q ~*psq ~ ^  wñppppq ~*q ~*ppppppppppppppppp  wñ        ppq ~ dsq ~ f   uq ~ i   sq ~ kt empresaVO.sitesq ~ kt .toLowerCase()t java.lang.Stringppppppppppsq ~ >  wñ   
        e  b   pq ~ q ~ »ppppppq ~ 1ppppq ~ 4  wñpppppt Microsoft Sans Serifpppq ~ Qq ~ Qpppppppsq ~ Rpsq ~ V  wñppppq ~9q ~9q ~7psq ~ Y  wñppppq ~9q ~9psq ~ W  wñppppq ~9q ~9psq ~ \  wñppppq ~9q ~9psq ~ ^  wñppppq ~9q ~9pppppt Helvetica-Boldppppppppppp  wñ        ppq ~ dsq ~ f   uq ~ i   sq ~ kt empresaVO.cnpjt java.lang.Stringppppppppppsq ~  wñ   '          Ö   sq ~ É    ÿðððpppq ~ q ~ »pt retDadosRecibo1ppppq ~ 1ppppq ~ 4  wîppsq ~ 6  wñpppsq ~ ´>  q ~Epq ~sq ~ >  wñ   
        e  b   pq ~ q ~ »ppppppq ~ 1ppppq ~ 4  wñpppppt Microsoft Sans Serifpppq ~ Qq ~ Qpppppppsq ~ Rpsq ~ V  wñppppq ~Lq ~Lq ~Jpsq ~ Y  wñppppq ~Lq ~Lpsq ~ W  wñppppq ~Lq ~Lpsq ~ \  wñppppq ~Lq ~Lpsq ~ ^  wñppppq ~Lq ~Lppppppppppppppppp  wñ        ppq ~ dsq ~ f   uq ~ i   sq ~ kt empresaVO.fonet java.lang.Stringppppppppppsq ~ !  wñ          p      <pq ~ q ~ »ppppppq ~ 1ppppq ~ 4  wîppsq ~ 6  wñppppq ~Wp  wñ q ~ <sq ~ >  wñ           ´  ¼   >pq ~ q ~ »pt 
textField-229pq ~ ¿ppq ~ 1ppppq ~ 4  wñpppppt Microsoft Sans Serifq ~ Äpq ~ Åq ~ Qppppppppsq ~ Rpsq ~ V  wñsq ~ É    ÿfffppppq ~ Ísq ~ ´    q ~\q ~\q ~Ypsq ~ Y  wñsq ~ É    ÿfffppppq ~ Ísq ~ ´    q ~\q ~\psq ~ W  wñppppq ~\q ~\psq ~ \  wñsq ~ É    ÿfffppppq ~ Ísq ~ ´    q ~\q ~\psq ~ ^  wñsq ~ É    ÿfffppppq ~ Ísq ~ ´    q ~\q ~\pppppt Helvetica-Boldppppppppppq ~ a  wñ        ppq ~ dsq ~ f   uq ~ i   sq ~ kt responsavel.nomet java.lang.Stringppppppq ~ pppsq ~ >  wñ           P      >pq ~ q ~ »pt 
textField-229pq ~ ¿ppq ~ 1ppppq ~ 4  wñpppppt Microsoft Sans Serifq ~ Äpq ~ Åq ~ Qppppppppsq ~ Rpsq ~ V  wñsq ~ É    ÿfffppppq ~ Ísq ~ ´    q ~sq ~sq ~ppsq ~ Y  wñsq ~ É    ÿfffppppq ~ Ísq ~ ´    q ~sq ~spsq ~ W  wñppppq ~sq ~spsq ~ \  wñsq ~ É    ÿfffppppq ~ Ísq ~ ´    q ~sq ~spsq ~ ^  wñsq ~ É    ÿfffppppq ~ Ísq ~ ´    q ~sq ~spppppt Helvetica-Boldppppppppppq ~ a  wñ        ppq ~ dsq ~ f   uq ~ i   sq ~ kt contratot java.lang.Integerppppppq ~ pppsq ~ >  wñ            Ú   sq ~ É    ÿðððpppq ~ q ~ »pt 
textField-229pq ~ ¿ppq ~ 1pppp~q ~ 3t RELATIVE_TO_TALLEST_OBJECT  wñpppppt Microsoft Sans Serifsq ~ J   pq ~ Nq ~ Qppppppppsq ~ Rpsq ~ V  wñsq ~ É    ÿfffppppq ~ Ísq ~ ´    q ~q ~q ~psq ~ Y  wñsq ~ É    ÿfffppppq ~ Ísq ~ ´    q ~q ~psq ~ W  wñppppq ~q ~psq ~ \  wñsq ~ É    ÿfffppppq ~ Ísq ~ ´    q ~q ~psq ~ ^  wñsq ~ É    ÿfffppppq ~ Ísq ~ ´    q ~q ~pppppt Helvetica-Boldppppppppppq ~ a  wñ        ppq ~ dsq ~ f   uq ~ i   sq ~ kt tipoOperacao_Apresentart java.lang.Stringppppppq ~ pppsq ~ ¦  wñ   
        U   ×   ,pq ~ q ~ »pt 
staticText-85pq ~ ¿ppq ~ Áppppq ~ 4  wñpppppt 	SansSerifq ~ Äpq ~ Åq ~ Qq ~ pq ~ pq ~ pppsq ~ Rpsq ~ V  wñsq ~ É    ÿfffppppq ~ Ísq ~ ´    q ~¥q ~¥q ~¢psq ~ Y  wñsq ~ É    ÿfffppppq ~ Ísq ~ ´    q ~¥q ~¥psq ~ W  wñppppq ~¥q ~¥psq ~ \  wñsq ~ É    ÿfffppppq ~ Ísq ~ ´    q ~¥q ~¥psq ~ ^  wñsq ~ É    ÿfffppppq ~ Ísq ~ ´    q ~¥q ~¥pppppt 	Helveticappppppppppq ~ at Data LanÃ§amentosq ~ ¦  wñ   
        A  1   ,pq ~ q ~ »pt 
staticText-85pq ~ ¿ppq ~ Áppppq ~ 4  wñpppppt 	SansSerifq ~ Äpq ~ Åq ~ Qq ~ pq ~ pq ~ pppsq ~ Rpsq ~ V  wñsq ~ É    ÿfffppppq ~ Ísq ~ ´    q ~¸q ~¸q ~µpsq ~ Y  wñsq ~ É    ÿfffppppq ~ Ísq ~ ´    q ~¸q ~¸psq ~ W  wñppppq ~¸q ~¸psq ~ \  wñsq ~ É    ÿfffppppq ~ Ísq ~ ´    q ~¸q ~¸psq ~ ^  wñsq ~ É    ÿfffppppq ~ Ísq ~ ´    q ~¸q ~¸pppppt 	Helveticappppppppppq ~ at Data InÃ­ciosq ~ ¦  wñ   
        A  w   ,pq ~ q ~ »pt 
staticText-85pq ~ ¿ppq ~ Áppppq ~ 4  wñpppppt 	SansSerifq ~ Äpq ~ Åq ~ Qq ~ pq ~ pq ~ pppsq ~ Rpsq ~ V  wñsq ~ É    ÿfffppppq ~ Ísq ~ ´    q ~Ëq ~Ëq ~Èpsq ~ Y  wñsq ~ É    ÿfffppppq ~ Ísq ~ ´    q ~Ëq ~Ëpsq ~ W  wñppppq ~Ëq ~Ëpsq ~ \  wñsq ~ É    ÿfffppppq ~ Ísq ~ ´    q ~Ëq ~Ëpsq ~ ^  wñsq ~ É    ÿfffppppq ~ Ísq ~ ´    q ~Ëq ~Ëpppppt 	Helveticappppppppppq ~ at 
Data Finalsq ~ >  wñ           A  w   >pq ~ q ~ »pt 
textField-229pq ~ ¿ppq ~ 1ppppq ~ 4  wñpppppt Microsoft Sans Serifq ~ Äpq ~ Åq ~ Qppppppppsq ~ Rpsq ~ V  wñsq ~ É    ÿfffppppq ~ Ísq ~ ´    q ~Þq ~Þq ~Ûpsq ~ Y  wñsq ~ É    ÿfffppppq ~ Ísq ~ ´    q ~Þq ~Þpsq ~ W  wñppppq ~Þq ~Þpsq ~ \  wñsq ~ É    ÿfffppppq ~ Ísq ~ ´    q ~Þq ~Þpsq ~ ^  wñsq ~ É    ÿfffppppq ~ Ísq ~ ´    q ~Þq ~Þpppppt Helvetica-Boldppppppppppq ~ a  wñ        ppq ~ dsq ~ f   uq ~ i   sq ~ kt $dataFimEfetivacaoOperacao_Apresentart java.lang.Stringppppppq ~ ppt  sq ~ >  wñ           A  1   >pq ~ q ~ »pt 
textField-229pq ~ ¿ppq ~ 1ppppq ~ 4  wñpppppt Microsoft Sans Serifq ~ Äpq ~ Åq ~ Qppppppppsq ~ Rpsq ~ V  wñsq ~ É    ÿfffppppq ~ Ísq ~ ´    q ~öq ~öq ~ópsq ~ Y  wñsq ~ É    ÿfffppppq ~ Ísq ~ ´    q ~öq ~öpsq ~ W  wñppppq ~öq ~öpsq ~ \  wñsq ~ É    ÿfffppppq ~ Ísq ~ ´    q ~öq ~öpsq ~ ^  wñsq ~ É    ÿfffppppq ~ Ísq ~ ´    q ~öq ~öpppppt Helvetica-Boldppppppppppq ~ a  wñ        ppq ~ dsq ~ f   uq ~ i   sq ~ kt 'dataInicioEfetivacaoOperacao_Apresentart java.lang.Stringppppppq ~ ppq ~òsq ~ >  wñ           U   ×   >pq ~ q ~ »pt 
textField-229pq ~ ¿ppq ~ 1ppppq ~ 4  wñpppppt Microsoft Sans Serifq ~ Äpq ~ Åq ~ Qppppppppsq ~ Rpsq ~ V  wñsq ~ É    ÿfffppppq ~ Ísq ~ ´    q ~
q ~
q ~
psq ~ Y  wñsq ~ É    ÿfffppppq ~ Ísq ~ ´    q ~
q ~
psq ~ W  wñppppq ~
q ~
psq ~ \  wñsq ~ É    ÿfffppppq ~ Ísq ~ ´    q ~
q ~
psq ~ ^  wñsq ~ É    ÿfffppppq ~ Ísq ~ ´    q ~
q ~
pppppt Helvetica-Boldppppppppppq ~ a  wñ        ppq ~ dsq ~ f   uq ~ i   sq ~ kt dataOperacao_Apresentart java.lang.Stringppppppq ~ ppq ~òsq ~ >  wñ          v      >pq ~ q ~ »pt 
textField-229pq ~ ¿ppq ~ 1ppppq ~ 4  wñpppppt Microsoft Sans Serifq ~ Äpq ~ Åq ~ Qppppppppsq ~ Rpsq ~ V  wñsq ~ É    ÿfffppppq ~ Ísq ~ ´    q ~$q ~$q ~!psq ~ Y  wñsq ~ É    ÿfffppppq ~ Ísq ~ ´    q ~$q ~$psq ~ W  wñppppq ~$q ~$psq ~ \  wñsq ~ É    ÿfffppppq ~ Ísq ~ ´    q ~$q ~$psq ~ ^  wñsq ~ É    ÿfffppppq ~ Ísq ~ ´    q ~$q ~$pppppt Helvetica-Boldppppppppppq ~ a  wñ       ppq ~ dsq ~ f   uq ~ i   sq ~ kt tipoOperacao_Apresentart java.lang.Stringppppppq ~ pppsq ~ ¦  wñ   
        P      ,pq ~ q ~ »pt 
staticText-85pq ~ ¿ppq ~ Áppppq ~ 4  wñpppppt 	SansSerifq ~ Äpq ~ Åq ~ Qq ~ pq ~ pq ~ pppsq ~ Rpsq ~ V  wñsq ~ É    ÿfffppppq ~ Ísq ~ ´    q ~;q ~;q ~8psq ~ Y  wñsq ~ É    ÿfffppppq ~ Ísq ~ ´    q ~;q ~;psq ~ W  wñppppq ~;q ~;psq ~ \  wñsq ~ É    ÿfffppppq ~ Ísq ~ ´    q ~;q ~;psq ~ ^  wñsq ~ É    ÿfffppppq ~ Ísq ~ ´    q ~;q ~;pppppt 	Helveticappppppppppq ~ at Contratoxp  wñ   Kpppsq ~ sq ~    w   sq ~ >  wñ   
             $pq ~ q ~Kppppppq ~ 1pppp~q ~ 3t RELATIVE_TO_BAND_HEIGHT  wñppppppq ~ Lp~q ~ Mt 	JUSTIFIEDpppppppppsq ~ Rpsq ~ V  wñppppq ~Rq ~Rq ~Mpsq ~ Y  wñppppq ~Rq ~Rpsq ~ W  wñppppq ~Rq ~Rpsq ~ \  wñppppq ~Rq ~Rpsq ~ ^  wñppppq ~Rq ~Rppppppppppppppppp  wñ       ppq ~ dsq ~ f   uq ~ i   sq ~ kt descricaoCalculot java.lang.Stringppppppppppsq ~ ¦  wñ   
       Z      pq ~ q ~Kpt 
staticText-85pq ~ ¿ppq ~ Ásq ~ f   uq ~ i   sq ~ kt !sq ~ kt justificativaApresentarsq ~ kt 
.equals(null)t java.lang.Booleanppppq ~ 4  wñpppppt 	SansSerifq ~ Äp~q ~ Mt RIGHTq ~ Qq ~ pq ~ pq ~ pppsq ~ Rpsq ~ V  wñsq ~ É    ÿfffppppq ~ Ísq ~ ´    q ~kq ~kq ~]psq ~ Y  wñsq ~ É    ÿfffppppq ~ Ísq ~ ´    q ~kq ~kpsq ~ W  wñppppq ~kq ~kpsq ~ \  wñsq ~ É    ÿfffppppq ~ Ísq ~ ´    q ~kq ~kpsq ~ ^  wñsq ~ É    ÿfffppppq ~ Ísq ~ ´    q ~kq ~kpppppt Helvetica-Boldppppppppppq ~ at Justificativa:sq ~ >  wñ   
         e   pq ~ q ~Kppppppq ~ 1sq ~ f   uq ~ i   sq ~ kt !sq ~ kt justificativaApresentarsq ~ kt 
.equals(null)q ~gppppq ~N  wñppppppppq ~Ppppppppppsq ~ Rpsq ~ V  wñppppq ~q ~q ~{psq ~ Y  wñppppq ~q ~psq ~ W  wñppppq ~q ~psq ~ \  wñppppq ~q ~psq ~ ^  wñppppq ~q ~ppppppppppppppppp  wñ       ppq ~ dsq ~ f   uq ~ i   sq ~ kt justificativaApresentart java.lang.Stringppppppq ~ Qpppsq ~ >  wñ   
       "  C   $pq ~ q ~Kppppppq ~ 1ppppq ~N  wñppppppq ~ Lpq ~Ppppppppppsq ~ Rpsq ~ V  wñppppq ~q ~q ~psq ~ Y  wñppppq ~q ~psq ~ W  wñppppq ~q ~psq ~ \  wñppppq ~q ~psq ~ ^  wñppppq ~q ~ppppppppppppppppp  wñ       ppq ~ dsq ~ f   uq ~ i   sq ~ kt 
observacaot java.lang.Stringppppppppppsq ~ ¦  wñ   
        Z     pq ~ q ~Kpt 
staticText-85pq ~ ¿ppq ~ 1ppppq ~ 4  wñpppppt 	SansSerifq ~ Äpq ~iq ~ Qq ~ pq ~ pq ~ pppsq ~ Rpsq ~ V  wñsq ~ É    ÿfffppppq ~ Ísq ~ ´    q ~q ~q ~psq ~ Y  wñsq ~ É    ÿfffppppq ~ Ísq ~ ´    q ~q ~psq ~ W  wñppppq ~q ~psq ~ \  wñsq ~ É    ÿfffppppq ~ Ísq ~ ´    q ~q ~psq ~ ^  wñsq ~ É    ÿfffppppq ~ Ísq ~ ´    q ~q ~pppppt Helvetica-Boldppppppppppq ~ at ObservaÃ§Ãµes:sq ~ ¦  wñ   
        Z      pq ~ q ~Kpt 
staticText-85pq ~ ¿ppq ~ Áppppq ~ 4  wñpppppt 	SansSerifq ~ Äpq ~iq ~ Qq ~ pq ~ pq ~ pppsq ~ Rpsq ~ V  wñsq ~ É    ÿfffppppq ~ Ísq ~ ´    q ~±q ~±q ~®psq ~ Y  wñsq ~ É    ÿfffppppq ~ Ísq ~ ´    q ~±q ~±psq ~ W  wñppppq ~±q ~±psq ~ \  wñsq ~ É    ÿfffppppq ~ Ísq ~ ´    q ~±q ~±psq ~ ^  wñsq ~ É    ÿfffppppq ~ Ísq ~ ´    q ~±q ~±pppppt Helvetica-Boldppppppppppq ~ at DescriÃ§Ã£o CÃ¡lculo:xp  wñ   7pp~r .net.sf.jasperreports.engine.type.SplitTypeEnum          xq ~ t PREVENTsq ~ sq ~    w   sq ~ ¦  wñ   
        l      pq ~ q ~Äpt 
staticText-85pq ~ ¿ppq ~ Ásq ~ f   uq ~ i   sq ~ kt !reciboDevolucao.apresentarChequesq ~gppppq ~ 4  wñpppppt 	SansSerifq ~ Äpq ~ Åq ~ Qq ~ pq ~ pq ~ Qpppsq ~ Rpsq ~ V  wñsq ~ É    ÿfffppppq ~ Ísq ~ ´    q ~Íq ~Íq ~Æpsq ~ Y  wñsq ~ É    ÿfffppppq ~ Ísq ~ ´    q ~Íq ~Ípsq ~ W  wñppppq ~Íq ~Ípsq ~ \  wñsq ~ É    ÿfffppppq ~ Ísq ~ ´    q ~Íq ~Ípsq ~ ^  wñsq ~ É    ÿfffppppq ~ Ísq ~ ´    q ~Íq ~Ípppppt 	Helveticappppppppppq ~ at Cheques devolvidossr 0net.sf.jasperreports.engine.base.JRBaseSubreport      'Ø L connectionExpressionq ~ L dataSourceExpressionq ~ L 
expressionq ~ L isUsingCacheq ~ A[ 
parameterst 3[Lnet/sf/jasperreports/engine/JRSubreportParameter;L parametersMapExpressionq ~ [ returnValuest 5[Lnet/sf/jasperreports/engine/JRSubreportReturnValue;L runToBottomq ~ Axq ~ &  wñ   "      Ç      pq ~ q ~Äpt subreport-1ppppq ~ 1sq ~ f   uq ~ i   sq ~ kt !reciboDevolucao.apresentarChequesq ~gppppq ~ 4psq ~ f   "uq ~ i   sq ~ kt reciboDevolucao.listaChequet (net.sf.jasperreports.engine.JRDataSourcepsq ~ f   #uq ~ i   sq ~ kt SUBREPORT_DIR2sq ~ kt   + "MovPagamento_cheques.jasper"t java.lang.Stringpq ~ Qur 3[Lnet.sf.jasperreports.engine.JRSubreportParameter;[£À¾B  xp   sr 9net.sf.jasperreports.engine.base.JRBaseSubreportParameter      'Ø  xr 7net.sf.jasperreports.engine.base.JRBaseDatasetParameter      'Ø L 
expressionq ~ L nameq ~ xpsq ~ f    uq ~ i   sq ~ kt 
SUBREPORT_DIRt java.lang.Objectpt 
SUBREPORT_DIRsq ~ôsq ~ f   !uq ~ i   sq ~ kt reciboDevolucao.listaChequeq ~ûpt ListaChequepppxp  wñ   6sq ~ f   uq ~ i   sq ~ kt !reciboDevolucao.apresentarChequesq ~gpppsq ~ sq ~    w   sq ~ ¦  wñ   
        `      pq ~ q ~pt 
staticText-85pq ~ ¿ppq ~ 1sq ~ f   %uq ~ i   sq ~ kt !reciboDevolucao.apresentarCartoesq ~gppppq ~ 4  wñpppppt 	SansSerifq ~ Äpq ~ Åq ~ Qq ~ pq ~ pq ~ Qpppsq ~ Rpsq ~ V  wñsq ~ É    ÿfffppppq ~ Ísq ~ ´    q ~q ~q ~	psq ~ Y  wñsq ~ É    ÿfffppppq ~ Ísq ~ ´    q ~q ~psq ~ W  wñppppq ~q ~psq ~ \  wñsq ~ É    ÿfffppppq ~ Ísq ~ ´    q ~q ~psq ~ ^  wñsq ~ É    ÿfffppppq ~ Ísq ~ ´    q ~q ~pppppt 	Helveticappppppppppq ~ at CartÃµes estornadossq ~Ý  wñ          Æ      pq ~ q ~pt subreport-2ppppq ~ 1sq ~ f   &uq ~ i   sq ~ kt !reciboDevolucao.apresentarCartoesq ~gppppq ~ 4psq ~ f   'uq ~ i   sq ~ kt reciboDevolucao.listaCartoesq ~êpsq ~ f   (uq ~ i   sq ~ kt SUBREPORT_DIR2sq ~ kt & + "MovPagamento_cartaocredito.jasper"t java.lang.Stringppppppxp  wñ   3sq ~ f   $uq ~ i   sq ~ kt !reciboDevolucao.apresentarCartoesq ~gpppppsr java.util.HashSetºD¸·4  xpw   ?@     t "net.sf.jasperreports.engine.data.*t net.sf.jasperreports.engine.*t java.util.*xt javapsr .net.sf.jasperreports.engine.base.JRBaseDataset      'Ø I PSEUDO_SERIAL_VERSION_UIDZ isMainB whenResourceMissingType[ fieldst &[Lnet/sf/jasperreports/engine/JRField;L filterExpressionq ~ [ groupst &[Lnet/sf/jasperreports/engine/JRGroup;L nameq ~ [ 
parameterst *[Lnet/sf/jasperreports/engine/JRParameter;L 
propertiesMapq ~ ,L queryt %Lnet/sf/jasperreports/engine/JRQuery;L resourceBundleq ~ L scriptletClassq ~ [ 
scriptletst *[Lnet/sf/jasperreports/engine/JRScriptlet;[ 
sortFieldst *[Lnet/sf/jasperreports/engine/JRSortField;[ 	variablest )[Lnet/sf/jasperreports/engine/JRVariable;L whenResourceMissingTypeValuet >Lnet/sf/jasperreports/engine/type/WhenResourceMissingTypeEnum;xp  wñ ur &[Lnet.sf.jasperreports.engine.JRField;<ßÇN*òp  xp   sr ,net.sf.jasperreports.engine.base.JRBaseField      'Ø L descriptionq ~ L nameq ~ L 
propertiesMapq ~ ,L valueClassNameq ~ L valueClassRealNameq ~ xppt tipoOperacao_Apresentarsr +net.sf.jasperreports.engine.JRPropertiesMap      'Ø L baseq ~ ,L propertiesListq ~ L 
propertiesMapt Ljava/util/Map;xppppt java.lang.Stringpsq ~Gpt dataOperacao_Apresentarsq ~Jpppt java.lang.Stringpsq ~Gpt 'dataInicioEfetivacaoOperacao_Apresentarsq ~Jpppt java.lang.Stringpsq ~Gpt $dataFimEfetivacaoOperacao_Apresentarsq ~Jpppt java.lang.Stringpsq ~Gpt responsavel.nomesq ~Jpppt java.lang.Stringpsq ~Gpt contratosq ~Jpppt java.lang.Integerpsq ~Gpt justificativaApresentarsq ~Jpppt java.lang.Stringpsq ~Gpt 
observacaosq ~Jpppt java.lang.Stringpsq ~Gpt descricaoCalculosq ~Jpppt java.lang.Stringpsq ~Gpt nomesq ~Jpppt java.lang.Stringpsq ~Gpt reciboDevolucao.listaChequesq ~Jpppt java.lang.Objectpsq ~Gpt reciboDevolucao.listaCartoessq ~Jpppt java.lang.Objectpsq ~Gpt !reciboDevolucao.apresentarCartoessq ~Jpppt java.lang.Booleanpsq ~Gpt !reciboDevolucao.apresentarChequessq ~Jpppt java.lang.Booleanpsq ~Gpt assinaturaDigitalBiometriasq ~Jpppt java.lang.Stringpppt ComprovanteOperacaoContratour *[Lnet.sf.jasperreports.engine.JRParameter;" *Ã`!  xp   'sr 0net.sf.jasperreports.engine.base.JRBaseParameter      'Ø 	Z isForPromptingZ isSystemDefinedL defaultValueExpressionq ~ L descriptionq ~ L nameq ~ L nestedTypeNameq ~ L 
propertiesMapq ~ ,L valueClassNameq ~ L valueClassRealNameq ~ xpppt REPORT_PARAMETERS_MAPpsq ~Jpppt 
java.util.Mappsq ~ppt 
JASPER_REPORTpsq ~Jpppt (net.sf.jasperreports.engine.JasperReportpsq ~ppt REPORT_CONNECTIONpsq ~Jpppt java.sql.Connectionpsq ~ppt REPORT_MAX_COUNTpsq ~Jpppt java.lang.Integerpsq ~ppt REPORT_DATA_SOURCEpsq ~Jpppq ~êpsq ~ppt REPORT_SCRIPTLETpsq ~Jpppt /net.sf.jasperreports.engine.JRAbstractScriptletpsq ~ppt 
REPORT_LOCALEpsq ~Jpppt java.util.Localepsq ~ppt REPORT_RESOURCE_BUNDLEpsq ~Jpppt java.util.ResourceBundlepsq ~ppt REPORT_TIME_ZONEpsq ~Jpppt java.util.TimeZonepsq ~ppt REPORT_FORMAT_FACTORYpsq ~Jpppt .net.sf.jasperreports.engine.util.FormatFactorypsq ~ppt REPORT_CLASS_LOADERpsq ~Jpppt java.lang.ClassLoaderpsq ~ppt REPORT_URL_HANDLER_FACTORYpsq ~Jpppt  java.net.URLStreamHandlerFactorypsq ~ppt REPORT_FILE_RESOLVERpsq ~Jpppt -net.sf.jasperreports.engine.util.FileResolverpsq ~ppt REPORT_TEMPLATESpsq ~Jpppt java.util.Collectionpsq ~ppt SORT_FIELDSpsq ~Jpppt java.util.Listpsq ~ppt REPORT_VIRTUALIZERpsq ~Jpppt )net.sf.jasperreports.engine.JRVirtualizerpsq ~ppt IS_IGNORE_PAGINATIONpsq ~Jpppq ~gpsq ~  ppt tituloRelatoriopsq ~Jpppt java.lang.Stringpsq ~  ppt nomeEmpresapsq ~Jpppt java.lang.Stringpsq ~  ppt versaoSoftwarepsq ~Jpppt java.lang.Stringpsq ~  ppt usuariopsq ~Jpppt java.lang.Stringpsq ~ sq ~ f    uq ~ i   sq ~ kt ^"C:\\PactoJ\\ZillyonWebTronco\\src\\main\\resources\\relatorio\\designRelatorio\\financeiro\\"t java.lang.Stringppt 
SUBREPORT_DIRpsq ~Jpppq ~ápsq ~ sq ~ f   uq ~ i   sq ~ kt p"E:\\desenvolvimentosv\\Projeto Pacto Solucoes\\ZillyonWeb\\src\\java\\relatorio\\designRelatorio\\financeiro\\"t java.lang.Stringppt SUBREPORT_DIR1psq ~Jpppq ~épsq ~ sq ~ f   uq ~ i   sq ~ kt ^"C:\\PactoJ\\ZillyonWebTronco\\src\\main\\resources\\relatorio\\designRelatorio\\financeiro\\"t java.lang.Stringppt SUBREPORT_DIR2psq ~Jpppq ~ñpsq ~ ppt empresaVO.cnpjpsq ~Jpppt java.lang.Stringpsq ~ ppt empresaVO.enderecopsq ~Jpppt java.lang.Stringpsq ~ ppt empresaVO.sitepsq ~Jpppt java.lang.Stringpsq ~ ppt empresaVO.fonepsq ~Jpppt java.lang.Stringpsq ~  ppt logoPadraoRelatoriopsq ~Jpppt java.io.InputStreampsq ~  ppt 'dataInicioEfetivacaoOperacao_Apresentarpsq ~Jpppt java.lang.Stringpsq ~  ppt $dataFimEfetivacaoOperacao_Apresentarpsq ~Jpppt java.lang.Stringpsq ~  ppt dataOperacao_Apresentarpsq ~Jpppt java.lang.Stringpsq ~  ppt responsavel.nomepsq ~Jpppt java.lang.Stringpsq ~  ppt tipoOperacao_Apresentarpsq ~Jpppt java.lang.Stringpsq ~  ppt justificativaApresentarpsq ~Jpppt java.lang.Stringpsq ~  ppt 
observacaopsq ~Jpppt java.lang.Stringpsq ~  ppt descricaoCalculopsq ~Jpppt java.lang.Stringpsq ~ ppt pessoapsq ~Jpppt java.lang.Stringpsq ~ ppt contratopsq ~Jpppt java.lang.Integerpsq ~Jpsq ~    w   t ireport.zoomt 	ireport.xt 	ireport.yt ireport.scriptlethandlingt ireport.encodingxsr java.util.HashMapÚÁÃ`Ñ F 
loadFactorI 	thresholdxp?@     w      q ~2t 1.5q ~6t 
ISO-8859-1q ~3t 0q ~4t 81q ~5t 0xpppppur )[Lnet.sf.jasperreports.engine.JRVariable;bæ|,·D  xp   sr /net.sf.jasperreports.engine.base.JRBaseVariable      'Ø I PSEUDO_SERIAL_VERSION_UIDB calculationB 
incrementTypeZ isSystemDefinedB 	resetTypeL calculationValuet 2Lnet/sf/jasperreports/engine/type/CalculationEnum;L 
expressionq ~ L incrementGroupq ~ +L incrementTypeValuet 4Lnet/sf/jasperreports/engine/type/IncrementTypeEnum;L incrementerFactoryClassNameq ~ L incrementerFactoryClassRealNameq ~ L initialValueExpressionq ~ L nameq ~ L 
resetGroupq ~ +L resetTypeValuet 0Lnet/sf/jasperreports/engine/type/ResetTypeEnum;L valueClassNameq ~ L valueClassRealNameq ~ xp  wî   ~r 0net.sf.jasperreports.engine.type.CalculationEnum          xq ~ t SYSTEMpp~r 2net.sf.jasperreports.engine.type.IncrementTypeEnum          xq ~ t NONEppsq ~ f   uq ~ i   sq ~ kt new java.lang.Integer(1)q ~pt PAGE_NUMBERp~r .net.sf.jasperreports.engine.type.ResetTypeEnum          xq ~ t REPORTq ~psq ~@  wî   q ~Fppq ~Ippsq ~ f   uq ~ i   sq ~ kt new java.lang.Integer(1)q ~pt 
COLUMN_NUMBERp~q ~Pt PAGEq ~psq ~@  wî   ~q ~Et COUNTsq ~ f   uq ~ i   sq ~ kt new java.lang.Integer(1)q ~ppq ~Ippsq ~ f   uq ~ i   sq ~ kt new java.lang.Integer(0)q ~pt REPORT_COUNTpq ~Qq ~psq ~@  wî   q ~\sq ~ f   uq ~ i   sq ~ kt new java.lang.Integer(1)q ~ppq ~Ippsq ~ f   uq ~ i   sq ~ kt new java.lang.Integer(0)q ~pt 
PAGE_COUNTpq ~Yq ~psq ~@  wî   q ~\sq ~ f   	uq ~ i   sq ~ kt new java.lang.Integer(1)q ~ppq ~Ippsq ~ f   
uq ~ i   sq ~ kt new java.lang.Integer(0)q ~pt COLUMN_COUNTp~q ~Pt COLUMNq ~p~r <net.sf.jasperreports.engine.type.WhenResourceMissingTypeEnum          xq ~ t EMPTYq ~p~r 0net.sf.jasperreports.engine.type.OrientationEnum          xq ~ t PORTRAITpp~r /net.sf.jasperreports.engine.type.PrintOrderEnum          xq ~ t VERTICALpppp~r 3net.sf.jasperreports.engine.type.WhenNoDataTypeEnum          xq ~ t NO_PAGESsr 6net.sf.jasperreports.engine.design.JRReportCompileData      'Ø L crosstabCompileDataq ~KL datasetCompileDataq ~KL mainDatasetCompileDataq ~ xpsq ~7?@     w       xsq ~7?@     w       xur [B¬óøTà  xp  . Êþº¾   . 0ComprovanteOperacaoContrato_1566419978743_886917  ,net/sf/jasperreports/engine/fill/JREvaluator  parameter_descricaoCalculo 2Lnet/sf/jasperreports/engine/fill/JRFillParameter; !parameter_justificativaApresentar parameter_JASPER_REPORT parameter_REPORT_TIME_ZONE parameter_usuario parameter_REPORT_FILE_RESOLVER !parameter_tipoOperacao_Apresentar parameter_REPORT_PARAMETERS_MAP parameter_SUBREPORT_DIR1 parameter_REPORT_CLASS_LOADER $parameter_REPORT_URL_HANDLER_FACTORY parameter_REPORT_DATA_SOURCE parameter_contrato parameter_IS_IGNORE_PAGINATION parameter_SUBREPORT_DIR2 parameter_REPORT_MAX_COUNT parameter_empresaVO46endereco parameter_REPORT_TEMPLATES 1parameter_dataInicioEfetivacaoOperacao_Apresentar .parameter_dataFimEfetivacaoOperacao_Apresentar parameter_REPORT_LOCALE parameter_REPORT_VIRTUALIZER parameter_SORT_FIELDS parameter_logoPadraoRelatorio parameter_pessoa parameter_REPORT_SCRIPTLET parameter_REPORT_CONNECTION parameter_responsavel46nome parameter_observacao parameter_SUBREPORT_DIR parameter_empresaVO46cnpj parameter_REPORT_FORMAT_FACTORY parameter_tituloRelatorio parameter_empresaVO46site parameter_nomeEmpresa !parameter_dataOperacao_Apresentar parameter_empresaVO46fone  parameter_REPORT_RESOURCE_BUNDLE parameter_versaoSoftware  field_assinaturaDigitalBiometria .Lnet/sf/jasperreports/engine/fill/JRFillField; (field_reciboDevolucao46apresentarCheques field_descricaoCalculo field_justificativaApresentar field_tipoOperacao_Apresentar field_responsavel46nome field_observacao field_contrato #field_reciboDevolucao46listaCartoes (field_reciboDevolucao46apresentarCartoes -field_dataInicioEfetivacaoOperacao_Apresentar "field_reciboDevolucao46listaCheque *field_dataFimEfetivacaoOperacao_Apresentar 
field_nome field_dataOperacao_Apresentar variable_PAGE_NUMBER 1Lnet/sf/jasperreports/engine/fill/JRFillVariable; variable_COLUMN_NUMBER variable_REPORT_COUNT variable_PAGE_COUNT variable_COLUMN_COUNT <init> ()V Code C D
  F  	  H  	  J  	  L 	 	  N 
 	  P  	  R  	  T 
 	  V  	  X  	  Z  	  \  	  ^  	  `  	  b  	  d  	  f  	  h  	  j  	  l  	  n  	  p  	  r  	  t  	  v  	  x  	  z   	  | ! 	  ~ " 	   # 	   $ 	   % 	   & 	   ' 	   ( 	   ) 	   * 	   + 	   , 	   - .	   / .	   0 .	   1 .	   2 .	   3 .	    4 .	  ¢ 5 .	  ¤ 6 .	  ¦ 7 .	  ¨ 8 .	  ª 9 .	  ¬ : .	  ® ; .	  ° < .	  ² = >	  ´ ? >	  ¶ @ >	  ¸ A >	  º B >	  ¼ LineNumberTable customizedInit 0(Ljava/util/Map;Ljava/util/Map;Ljava/util/Map;)V 
initParams (Ljava/util/Map;)V Á Â
  Ã 
initFields Å Â
  Æ initVars È Â
  É descricaoCalculo Ë 
java/util/Map Í get &(Ljava/lang/Object;)Ljava/lang/Object; Ï Ð Î Ñ 0net/sf/jasperreports/engine/fill/JRFillParameter Ó justificativaApresentar Õ 
JASPER_REPORT × REPORT_TIME_ZONE Ù usuario Û REPORT_FILE_RESOLVER Ý tipoOperacao_Apresentar ß REPORT_PARAMETERS_MAP á SUBREPORT_DIR1 ã REPORT_CLASS_LOADER å REPORT_URL_HANDLER_FACTORY ç REPORT_DATA_SOURCE é contrato ë IS_IGNORE_PAGINATION í SUBREPORT_DIR2 ï REPORT_MAX_COUNT ñ empresaVO.endereco ó REPORT_TEMPLATES õ 'dataInicioEfetivacaoOperacao_Apresentar ÷ $dataFimEfetivacaoOperacao_Apresentar ù 
REPORT_LOCALE û REPORT_VIRTUALIZER ý SORT_FIELDS ÿ logoPadraoRelatorio pessoa REPORT_SCRIPTLET REPORT_CONNECTION responsavel.nome	 
observacao 
SUBREPORT_DIR
 empresaVO.cnpj REPORT_FORMAT_FACTORY tituloRelatorio empresaVO.site nomeEmpresa dataOperacao_Apresentar empresaVO.fone REPORT_RESOURCE_BUNDLE versaoSoftware assinaturaDigitalBiometria! ,net/sf/jasperreports/engine/fill/JRFillField# !reciboDevolucao.apresentarCheques% reciboDevolucao.listaCartoes' !reciboDevolucao.apresentarCartoes) reciboDevolucao.listaCheque+ nome- PAGE_NUMBER/ /net/sf/jasperreports/engine/fill/JRFillVariable1 
COLUMN_NUMBER3 REPORT_COUNT5 
PAGE_COUNT7 COLUMN_COUNT9 evaluate (I)Ljava/lang/Object; 
Exceptions java/lang/Throwable> SC:\PactoJ\ZillyonWebTronco\src\main\resources\relatorio\designRelatorio\financeiro\@ eE:\desenvolvimentosv\Projeto Pacto Solucoes\ZillyonWeb\src\java\relatorio\designRelatorio\financeiro\B java/lang/IntegerD (I)V CF
EG getValue ()Ljava/lang/Object;IJ
 ÔK java/io/InputStreamM java/lang/StringO toLowerCase ()Ljava/lang/String;QR
PS
$K equals (Ljava/lang/Object;)ZVW
PX java/lang/BooleanZ valueOf (Z)Ljava/lang/Boolean;\]
[^ (net/sf/jasperreports/engine/JRDataSource` java/lang/StringBufferb &(Ljava/lang/Object;)Ljava/lang/String;\d
Pe (Ljava/lang/String;)V Cg
ch MovPagamento_cheques.jasperj append ,(Ljava/lang/String;)Ljava/lang/StringBuffer;lm
cn toStringpR
cq !MovPagamento_cartaocredito.jaspers 	Cliente: u  , assinatura digital biomÃ©tricaw java/util/Datey
z F Resp. OperaÃ§Ã£o: | evaluateOld getOldValueJ
$ evaluateEstimated 
SourceFile !     ;                 	     
               
                                                                                                     !     "     #     $     %     &     '     (     )     *     +     ,     - .    / .    0 .    1 .    2 .    3 .    4 .    5 .    6 .    7 .    8 .    9 .    : .    ; .    < .    = >    ? >    @ >    A >    B >     C D  E  4    ,*· G*µ I*µ K*µ M*µ O*µ Q*µ S*µ U*µ W*µ Y*µ [*µ ]*µ _*µ a*µ c*µ e*µ g*µ i*µ k*µ m*µ o*µ q*µ s*µ u*µ w*µ y*µ {*µ }*µ *µ *µ *µ *µ *µ *µ *µ *µ *µ *µ *µ *µ *µ *µ *µ *µ *µ ¡*µ £*µ ¥*µ §*µ ©*µ «*µ ­*µ ¯*µ ±*µ ³*µ µ*µ ·*µ ¹*µ »*µ ½±    ¾   ö =      	          ! " " ' # , $ 1 % 6 & ; ' @ ( E ) J * O + T , Y - ^ . c / h 0 m 1 r 2 w 3 | 4  5  6  7  8  9  :  ; ¤ < © = ® > ³ ? ¸ @ ½ A Â B Ç C Ì D Ñ E Ö F Û G à H å I ê J ï K ô L ù M þ N O P
 Q R S T! U& V+   ¿ À  E   4     *+· Ä*,· Ç*-· Ê±    ¾       b  c 
 d  e  Á Â  E      Ð*+Ì¹ Ò À ÔÀ Ôµ I*+Ö¹ Ò À ÔÀ Ôµ K*+Ø¹ Ò À ÔÀ Ôµ M*+Ú¹ Ò À ÔÀ Ôµ O*+Ü¹ Ò À ÔÀ Ôµ Q*+Þ¹ Ò À ÔÀ Ôµ S*+à¹ Ò À ÔÀ Ôµ U*+â¹ Ò À ÔÀ Ôµ W*+ä¹ Ò À ÔÀ Ôµ Y*+æ¹ Ò À ÔÀ Ôµ [*+è¹ Ò À ÔÀ Ôµ ]*+ê¹ Ò À ÔÀ Ôµ _*+ì¹ Ò À ÔÀ Ôµ a*+î¹ Ò À ÔÀ Ôµ c*+ð¹ Ò À ÔÀ Ôµ e*+ò¹ Ò À ÔÀ Ôµ g*+ô¹ Ò À ÔÀ Ôµ i*+ö¹ Ò À ÔÀ Ôµ k*+ø¹ Ò À ÔÀ Ôµ m*+ú¹ Ò À ÔÀ Ôµ o*+ü¹ Ò À ÔÀ Ôµ q*+þ¹ Ò À ÔÀ Ôµ s*+ ¹ Ò À ÔÀ Ôµ u*+¹ Ò À ÔÀ Ôµ w*+¹ Ò À ÔÀ Ôµ y*+¹ Ò À ÔÀ Ôµ {*+¹ Ò À ÔÀ Ôµ }*+
¹ Ò À ÔÀ Ôµ *+¹ Ò À ÔÀ Ôµ *+¹ Ò À ÔÀ Ôµ *+¹ Ò À ÔÀ Ôµ *+¹ Ò À ÔÀ Ôµ *+¹ Ò À ÔÀ Ôµ *+¹ Ò À ÔÀ Ôµ *+¹ Ò À ÔÀ Ôµ *+¹ Ò À ÔÀ Ôµ *+¹ Ò À ÔÀ Ôµ *+¹ Ò À ÔÀ Ôµ *+ ¹ Ò À ÔÀ Ôµ ±    ¾   ¢ (   m  n $ o 6 p H q Z r l s ~ t  u ¢ v ´ w Æ x Ø y ê z ü { |  }2 ~D V h z   ² Å Ø ë þ  $ 7 J ] p   © ¼ Ï   Å Â  E  l    *+"¹ Ò À$À$µ *+&¹ Ò À$À$µ *+Ì¹ Ò À$À$µ *+Ö¹ Ò À$À$µ *+à¹ Ò À$À$µ *+
¹ Ò À$À$µ ¡*+¹ Ò À$À$µ £*+ì¹ Ò À$À$µ ¥*+(¹ Ò À$À$µ §*+*¹ Ò À$À$µ ©*+ø¹ Ò À$À$µ «*+,¹ Ò À$À$µ ­*+ú¹ Ò À$À$µ ¯*+.¹ Ò À$À$µ ±*+¹ Ò À$À$µ ³±    ¾   B       &  8  J   \ ¡ o ¢  £  ¤ § ¥ º ¦ Ì § ß ¨ ñ © ª «  È Â  E        `*+0¹ Ò À2À2µ µ*+4¹ Ò À2À2µ ·*+6¹ Ò À2À2µ ¹*+8¹ Ò À2À2µ »*+:¹ Ò À2À2µ ½±    ¾       ³  ´ & µ 9 ¶ L · _ ¸ ;< =    ? E  ÿ    {Mª  v       ,   Á   È   Ï   Ö   â   î   ú        *  6  D  R  `  q        ©  ·  Å  Ó  á  ï  ý    7  E  S  a  o  }      ¤  Å  Ó  á  ï  ý    B  P  [AM§±CM§ªAM§£»EY·HM§»EY·HM§»EY·HM§»EY·HM§s»EY·HM§g»EY·HM§[»EY·HM§O»EY·HM§C*´ w¶LÀNM§5*´ ¶LÀPM§'*´ i¶LÀPM§*´ ¶LÀP¶TM§*´ ¶LÀPM§ú*´ ¶LÀPM§ì*´ ¡¶UÀPM§Þ*´ ¥¶UÀEM§Ð*´ ¶UÀPM§Â*´ ¯¶UÀPM§´*´ «¶UÀPM§¦*´ ³¶UÀPM§*´ ¶UÀPM§*´ ¶UÀPM§|*´ ¶UÀP¶Y § ¸_M§_*´ ¶UÀP¶Y § ¸_M§B*´ ¶UÀPM§4*´ £¶UÀPM§&*´ ¶UÀ[M§*´ ¶UÀ[M§
*´ ¶UÀ[M§ ü*´ ¶LÀPM§ î*´ ­¶UM§ ã*´ ­¶UÀaM§ Õ»cY*´ e¶LÀP¸f·ik¶o¶rM§ ´*´ ©¶UÀ[M§ ¦*´ ©¶UÀ[M§ *´ ©¶UÀ[M§ *´ §¶UÀaM§ |»cY*´ e¶LÀP¸f·it¶o¶rM§ [»cYv·i*´ ±¶UÀP¶ox¶o¶rM§ 7*´ ¶UÀPM§ )»zY·{M§ »cY}·i*´ ¡¶UÀP¶o¶rM,°    ¾  r \   À  Â Ä Æ È Ç Ë Ë Ï Ì Ò Ð Ö Ñ Ù Õ â Ö å Ú î Û ñ ß ú à ý ä å	 é ê î ï! ó* ô- ø6 ù9 ýD þGRU`cq
t ©!¬%·&º*Å+È/Ó0Ö4á5ä9ï:ò>ý? CDH7I:MENHRSSVWaXd\o]ra}bfgklp¤q§uÅvÈzÓ{Öáäïòý !BEPS[^¢yª ~< =    ? E  ÿ    {Mª  v       ,   Á   È   Ï   Ö   â   î   ú        *  6  D  R  `  q        ©  ·  Å  Ó  á  ï  ý    7  E  S  a  o  }      ¤  Å  Ó  á  ï  ý    B  P  [AM§±CM§ªAM§£»EY·HM§»EY·HM§»EY·HM§»EY·HM§s»EY·HM§g»EY·HM§[»EY·HM§O»EY·HM§C*´ w¶LÀNM§5*´ ¶LÀPM§'*´ i¶LÀPM§*´ ¶LÀP¶TM§*´ ¶LÀPM§ú*´ ¶LÀPM§ì*´ ¡¶ÀPM§Þ*´ ¥¶ÀEM§Ð*´ ¶ÀPM§Â*´ ¯¶ÀPM§´*´ «¶ÀPM§¦*´ ³¶ÀPM§*´ ¶ÀPM§*´ ¶ÀPM§|*´ ¶ÀP¶Y § ¸_M§_*´ ¶ÀP¶Y § ¸_M§B*´ ¶ÀPM§4*´ £¶ÀPM§&*´ ¶À[M§*´ ¶À[M§
*´ ¶À[M§ ü*´ ¶LÀPM§ î*´ ­¶M§ ã*´ ­¶ÀaM§ Õ»cY*´ e¶LÀP¸f·ik¶o¶rM§ ´*´ ©¶À[M§ ¦*´ ©¶À[M§ *´ ©¶À[M§ *´ §¶ÀaM§ |»cY*´ e¶LÀP¸f·it¶o¶rM§ [»cYv·i*´ ±¶ÀP¶ox¶o¶rM§ 7*´ ¶ÀPM§ )»zY·{M§ »cY}·i*´ ¡¶ÀP¶o¶rM,°    ¾  r \  ³ µ Ä¹ Èº Ë¾ Ï¿ ÒÃ ÖÄ ÙÈ âÉ åÍ îÎ ñÒ úÓ ý×Ø	ÜÝáâ!æ*ç-ë6ì9ðDñGõRöUú`ûcÿq t	
©¬·ºÅÈ"Ó#Ö'á(ä,ï-ò1ý2 67;7<:@EAHESFVJaKdOoPrT}UYZ^_c¤d§hÅiÈmÓnÖrásäwïxò|ý} !BEPS[^y < =    ? E  ÿ    {Mª  v       ,   Á   È   Ï   Ö   â   î   ú        *  6  D  R  `  q        ©  ·  Å  Ó  á  ï  ý    7  E  S  a  o  }      ¤  Å  Ó  á  ï  ý    B  P  [AM§±CM§ªAM§£»EY·HM§»EY·HM§»EY·HM§»EY·HM§s»EY·HM§g»EY·HM§[»EY·HM§O»EY·HM§C*´ w¶LÀNM§5*´ ¶LÀPM§'*´ i¶LÀPM§*´ ¶LÀP¶TM§*´ ¶LÀPM§ú*´ ¶LÀPM§ì*´ ¡¶UÀPM§Þ*´ ¥¶UÀEM§Ð*´ ¶UÀPM§Â*´ ¯¶UÀPM§´*´ «¶UÀPM§¦*´ ³¶UÀPM§*´ ¶UÀPM§*´ ¶UÀPM§|*´ ¶UÀP¶Y § ¸_M§_*´ ¶UÀP¶Y § ¸_M§B*´ ¶UÀPM§4*´ £¶UÀPM§&*´ ¶UÀ[M§*´ ¶UÀ[M§
*´ ¶UÀ[M§ ü*´ ¶LÀPM§ î*´ ­¶UM§ ã*´ ­¶UÀaM§ Õ»cY*´ e¶LÀP¸f·ik¶o¶rM§ ´*´ ©¶UÀ[M§ ¦*´ ©¶UÀ[M§ *´ ©¶UÀ[M§ *´ §¶UÀaM§ |»cY*´ e¶LÀP¸f·it¶o¶rM§ [»cYv·i*´ ±¶UÀP¶ox¶o¶rM§ 7*´ ¶UÀPM§ )»zY·{M§ »cY}·i*´ ¡¶UÀP¶o¶rM,°    ¾  r \  ¦ ¨ Ä¬ È­ Ë± Ï² Ò¶ Ö· Ù» â¼ åÀ îÁ ñÅ úÆ ýÊË	ÏÐÔÕ!Ù*Ú-Þ6ß9ãDäGèRéUí`îcòqót÷øüý©¬·ºÅÈÓÖáäï ò$ý% )*.7/:3E4H8S9V=a>dBoCrG}HLMQRV¤W§[Å\È`ÓaÖeáfäjïkòoýp tu!yBzE~PS[^y     t _1566419978743_886917t 2net.sf.jasperreports.engine.design.JRJavacCompiler