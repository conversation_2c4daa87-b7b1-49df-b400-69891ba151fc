<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="frequenciaOcupacaoTurmas" pageWidth="842" pageHeight="595" orientation="Landscape" columnWidth="802" leftMargin="20" rightMargin="20" topMargin="20" bottomMargin="20">
	<property name="ireport.zoom" value="2.0"/>
	<property name="ireport.x" value="344"/>
	<property name="ireport.y" value="0"/>
	<parameter name="nomeEmpresa" class="java.lang.String" isForPrompting="false"/>
	<parameter name="logoPadraoRelatorio" class="java.io.InputStream" isForPrompting="false"/>
	<parameter name="dataIni" class="java.lang.String" isForPrompting="false"/>
	<parameter name="dataFim" class="java.lang.String" isForPrompting="false"/>
	<parameter name="enderecoEmpresa" class="java.lang.String" isForPrompting="false"/>
	<parameter name="cidadeEmpresa" class="java.lang.String" isForPrompting="false"/>
	<parameter name="SUBREPORT_DIR" class="java.lang.String">
		<defaultValueExpression><![CDATA["D:\\PactoJ\\Desenvolvimento\\Sistemas\\ZillyonWeb\\tronco-novo\\src\\java\\relatorio\\designRelatorio\\contrato\\"]]></defaultValueExpression>
	</parameter>
	<parameter name="filtros" class="java.lang.String" isForPrompting="false"/>
	<parameter name="CONT" class="java.lang.Integer" isForPrompting="false"/>
	<parameter name="usuario" class="java.lang.String" isForPrompting="false"/>
	<parameter name="versaoSoftware" class="java.lang.String" isForPrompting="false"/>
	<field name="aluno" class="java.lang.String"/>
	<field name="contrato" class="java.lang.Integer"/>
	<field name="dataContrato" class="java.util.Date"/>
	<field name="percOcupacao" class="java.lang.Double"/>
	<field name="percDesconto" class="java.lang.Double"/>
	<field name="horarioTurma.identificadorTurma" class="java.lang.String"/>
	<field name="horarioTurma.professor.pessoa.nome" class="java.lang.String"/>
	<field name="horarioTurma.ambiente.descricao" class="java.lang.String"/>
	<field name="horarioTurma.diaSemana_Apresentar" class="java.lang.String"/>
	<field name="horarioTurma.horaInicial" class="java.lang.String"/>
	<field name="horarioTurma.horaFinal" class="java.lang.String"/>
	<field name="horarioTurma.nrMaximoAluno" class="java.lang.Integer"/>
	<field name="horarioTurma.codigo" class="java.lang.Integer"/>
	<group name="turmas">
		<groupFooter>
			<band height="28">
				<printWhenExpression><![CDATA[!$P{nomeEmpresa}.isEmpty()]]></printWhenExpression>
				<staticText>
					<reportElement x="365" y="8" width="65" height="20"/>
					<textElement verticalAlignment="Middle">
						<font size="10"/>
					</textElement>
					<text><![CDATA[Totais:]]></text>
				</staticText>
				<line>
					<reportElement x="365" y="7" width="437" height="1"/>
				</line>
			</band>
		</groupFooter>
	</group>
	<background>
		<band splitType="Stretch"/>
	</background>
	<title>
		<band height="80">
			<printWhenExpression><![CDATA[!$P{nomeEmpresa}.isEmpty()]]></printWhenExpression>
			<image isUsingCache="true" onErrorType="Blank">
				<reportElement key="image-1" x="0" y="0" width="75" height="65" isPrintWhenDetailOverflows="true"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
				</box>
				<imageExpression class="java.io.InputStream"><![CDATA[$P{logoPadraoRelatorio}]]></imageExpression>
			</image>
			<staticText>
				<reportElement key="staticText-100" x="365" y="43" width="437" height="37"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="20" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Desconto por ocupação na turma]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-91" mode="Opaque" x="540" y="0" width="262" height="23"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Top" lineSpacing="Single">
					<font fontName="Microsoft Sans Serif" size="9" isBold="true" isItalic="true" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica-BoldOblique"/>
				</textElement>
				<text><![CDATA[Zillyon - Sistema Administrativo para Academias Desenvolvido por PACTO Soluções Tecnológicas Ltda.]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-92" mode="Opaque" x="691" y="24" width="111" height="12"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Top" lineSpacing="Single">
					<font fontName="Microsoft Sans Serif" size="9" isBold="true" isItalic="true" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica-BoldOblique"/>
				</textElement>
				<text><![CDATA[(0xx62) 3251-5820]]></text>
			</staticText>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-246" x="229" y="43" width="60" height="14"/>
				<textElement>
					<font isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{dataFim}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-247" x="145" y="1" width="285" height="14"/>
				<textElement>
					<font isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{nomeEmpresa}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-245" x="145" y="43" width="60" height="14"/>
				<textElement>
					<font isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{dataIni}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement key="staticText-103" mode="Opaque" x="205" y="43" width="24" height="14"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Microsoft Sans Serif" size="12" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[até]]></text>
			</staticText>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-249" x="145" y="29" width="285" height="14"/>
				<textElement>
					<font isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{cidadeEmpresa}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement key="staticText-102" mode="Opaque" x="87" y="15" width="58" height="14"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Microsoft Sans Serif" size="10" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Endereço:]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-102" mode="Opaque" x="87" y="43" width="58" height="14"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Microsoft Sans Serif" size="10" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Período:]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-102" mode="Opaque" x="87" y="1" width="58" height="14"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Microsoft Sans Serif" size="10" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Empresa:]]></text>
			</staticText>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-248" x="145" y="15" width="285" height="14"/>
				<textElement>
					<font isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{enderecoEmpresa}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement key="staticText-102" mode="Opaque" x="87" y="29" width="58" height="14"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Microsoft Sans Serif" size="10" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Cidade:]]></text>
			</staticText>
		</band>
	</title>
	<columnHeader>
		<band height="17">
			<staticText>
				<reportElement x="0" y="1" width="39" height="14"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Microsoft Sans Serif" size="9" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Contrato]]></text>
			</staticText>
			<staticText>
				<reportElement x="39" y="1" width="50" height="14"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Microsoft Sans Serif" size="9" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Data Contrato]]></text>
			</staticText>
			<staticText>
				<reportElement x="89" y="1" width="188" height="14"/>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Microsoft Sans Serif" size="9" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Aluno]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-96" mode="Transparent" x="647" y="1" width="35" height="14"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Microsoft Sans Serif" size="9" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Vagas]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-96" mode="Transparent" x="682" y="1" width="60" height="14"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Microsoft Sans Serif" size="9" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[%.Ocup.]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-82" mode="Transparent" x="277" y="1" width="100" height="14"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Microsoft Sans Serif" size="9" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Turma]]></text>
			</staticText>
			<line>
				<reportElement key="line-1" mode="Transparent" x="1" y="0" width="801" height="1"/>
				<graphicElement fill="Solid">
					<pen lineWidth="0.5" lineStyle="Solid"/>
				</graphicElement>
			</line>
			<line>
				<reportElement key="line-2" mode="Transparent" x="0" y="15" width="802" height="1"/>
				<graphicElement fill="Solid">
					<pen lineWidth="0.5" lineStyle="Double"/>
				</graphicElement>
			</line>
			<staticText>
				<reportElement key="staticText-84" mode="Transparent" x="377" y="1" width="140" height="14"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Microsoft Sans Serif" size="9" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Professor]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-96" mode="Transparent" x="577" y="1" width="70" height="14"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Microsoft Sans Serif" size="9" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Horário]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-81" mode="Transparent" x="517" y="1" width="60" height="14"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Microsoft Sans Serif" size="9" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Dia Semana]]></text>
			</staticText>
			<staticText>
				<reportElement x="742" y="1" width="60" height="14"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Microsoft Sans Serif" size="9" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[% Desconto]]></text>
			</staticText>
		</band>
	</columnHeader>
	<detail>
		<band height="17" splitType="Stretch">
			<textField>
				<reportElement x="0" y="0" width="39" height="17"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="9"/>
				</textElement>
				<textFieldExpression class="java.lang.Integer"><![CDATA[$F{contrato}]]></textFieldExpression>
			</textField>
			<textField pattern="dd/MM/yyyy">
				<reportElement x="39" y="0" width="50" height="17"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="9"/>
				</textElement>
				<textFieldExpression class="java.util.Date"><![CDATA[$F{dataContrato}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="89" y="0" width="188" height="17"/>
				<textElement verticalAlignment="Middle">
					<font size="9"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{aluno}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="277" y="0" width="100" height="17"/>
				<textElement verticalAlignment="Middle">
					<font size="9"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{horarioTurma.identificadorTurma}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="517" y="0" width="60" height="17"/>
				<textElement verticalAlignment="Middle">
					<font size="9"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{horarioTurma.diaSemana_Apresentar}]]></textFieldExpression>
			</textField>
			<textField pattern="#,##0.00 %">
				<reportElement x="682" y="0" width="60" height="17"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="9"/>
				</textElement>
				<textFieldExpression class="java.lang.Double"><![CDATA[$F{percOcupacao}/100]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="577" y="0" width="70" height="17"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="9"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{horarioTurma.horaInicial} +" - "+ $F{horarioTurma.horaFinal}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="377" y="0" width="140" height="17"/>
				<textElement verticalAlignment="Middle">
					<font size="9"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{horarioTurma.professor.pessoa.nome}]]></textFieldExpression>
			</textField>
			<textField pattern="#,##0.00 %">
				<reportElement x="742" y="0" width="60" height="17"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="9"/>
				</textElement>
				<textFieldExpression class="java.lang.Double"><![CDATA[$F{percDesconto}/100]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="647" y="0" width="35" height="17"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="9"/>
				</textElement>
				<textFieldExpression class="java.lang.Integer"><![CDATA[$F{horarioTurma.nrMaximoAluno}]]></textFieldExpression>
			</textField>
		</band>
	</detail>
	<pageFooter>
		<band height="18">
			<printWhenExpression><![CDATA[!$P{nomeEmpresa}.isEmpty()]]></printWhenExpression>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-243" x="675" y="1" width="83" height="17"/>
				<box bottomPadding="2">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right">
					<font fontName="Arial" size="10" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA["Página: " + $V{PAGE_NUMBER} + " de "]]></textFieldExpression>
			</textField>
			<line>
				<reportElement key="line-2" mode="Transparent" x="0" y="0" width="802" height="1"/>
				<graphicElement fill="Solid"/>
			</line>
			<textField pattern="EEE, d MMM yyyy HH:mm:ss Z" isBlankWhenNull="false">
				<reportElement key="dataRel-1" mode="Transparent" x="1" y="1" width="204" height="17" backcolor="#FFFFFF"/>
				<box bottomPadding="2">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Verdana" size="10" isBold="true" isItalic="false" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.util.Date"><![CDATA[new Date()]]></textFieldExpression>
			</textField>
			<textField evaluationTime="Report" isBlankWhenNull="false">
				<reportElement key="textField-242" x="758" y="1" width="45" height="17"/>
				<box bottomPadding="2">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
				</box>
				<textElement>
					<font fontName="Arial" size="10" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[" " + $V{PAGE_NUMBER} + ""]]></textFieldExpression>
			</textField>
		</band>
	</pageFooter>
</jasperReport>
