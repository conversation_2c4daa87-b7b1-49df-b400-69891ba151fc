¬í sr (net.sf.jasperreports.engine.JasperReport      'Ø L compileDatat Ljava/io/Serializable;L compileNameSuffixt Ljava/lang/String;L 
compilerClassq ~ xr -net.sf.jasperreports.engine.base.JRBaseReport      'Ø 'I bottomMarginI columnCountI 
columnSpacingI columnWidthZ ignorePaginationZ isFloatColumnFooterZ isSummaryNewPageZ  isSummaryWithPageHeaderAndFooterZ isTitleNewPageI 
leftMarginB orientationI 
pageHeightI 	pageWidthB 
printOrderI rightMarginI 	topMarginB whenNoDataTypeL 
backgroundt $Lnet/sf/jasperreports/engine/JRBand;L columnFooterq ~ L columnHeaderq ~ [ datasetst ([Lnet/sf/jasperreports/engine/JRDataset;L defaultFontt *Lnet/sf/jasperreports/engine/JRReportFont;L defaultStylet %Lnet/sf/jasperreports/engine/JRStyle;L detailq ~ L 
detailSectiont 'Lnet/sf/jasperreports/engine/JRSection;[ fontst +[Lnet/sf/jasperreports/engine/JRReportFont;L formatFactoryClassq ~ L 
importsSett Ljava/util/Set;L languageq ~ L lastPageFooterq ~ L mainDatasett 'Lnet/sf/jasperreports/engine/JRDataset;L nameq ~ L noDataq ~ L 
pageFooterq ~ L 
pageHeaderq ~ [ stylest &[Lnet/sf/jasperreports/engine/JRStyle;L summaryq ~ [ 	templatest /[Lnet/sf/jasperreports/engine/JRReportTemplate;L titleq ~ xp            +          J  S      sr +net.sf.jasperreports.engine.base.JRBaseBand      'Ø I PSEUDO_SERIAL_VERSION_UIDI heightZ isSplitAllowedL printWhenExpressiont *Lnet/sf/jasperreports/engine/JRExpression;L 	splitTypet Ljava/lang/Byte;xr 3net.sf.jasperreports.engine.base.JRBaseElementGroup      'Ø L childrent Ljava/util/List;L elementGroupt ,Lnet/sf/jasperreports/engine/JRElementGroup;xpsr java.util.ArrayListxÒÇa I sizexp    w   
xp  w&    psr java.lang.ByteN`îPõ B valuexr java.lang.Number¬à  xpppppppsr .net.sf.jasperreports.engine.base.JRBaseSection      'Ø [ bandst %[Lnet/sf/jasperreports/engine/JRBand;xpur %[Lnet.sf.jasperreports.engine.JRBand;Ý~ìÊ5  xp   sq ~ sq ~    w   
sr 0net.sf.jasperreports.engine.base.JRBaseTextField      'Ø I 
bookmarkLevelB evaluationTimeB hyperlinkTargetB 
hyperlinkTypeZ isStretchWithOverflowL anchorNameExpressionq ~ L evaluationGroupt %Lnet/sf/jasperreports/engine/JRGroup;L 
expressionq ~ L hyperlinkAnchorExpressionq ~ L hyperlinkPageExpressionq ~ [ hyperlinkParameterst 3[Lnet/sf/jasperreports/engine/JRHyperlinkParameter;L hyperlinkReferenceExpressionq ~ L hyperlinkTooltipExpressionq ~ L isBlankWhenNullt Ljava/lang/Boolean;L 
linkTargetq ~ L linkTypeq ~ L patternq ~ xr 2net.sf.jasperreports.engine.base.JRBaseTextElement      'Ø  L borderq ~ L borderColort Ljava/awt/Color;L bottomBorderq ~ L bottomBorderColorq ~ 'L 
bottomPaddingt Ljava/lang/Integer;L fontNameq ~ L fontSizeq ~ (L horizontalAlignmentq ~ L isBoldq ~ %L isItalicq ~ %L 
isPdfEmbeddedq ~ %L isStrikeThroughq ~ %L isStyledTextq ~ %L isUnderlineq ~ %L 
leftBorderq ~ L leftBorderColorq ~ 'L leftPaddingq ~ (L lineBoxt 'Lnet/sf/jasperreports/engine/JRLineBox;L lineSpacingq ~ L markupq ~ L paddingq ~ (L pdfEncodingq ~ L pdfFontNameq ~ L 
reportFontq ~ L rightBorderq ~ L rightBorderColorq ~ 'L rightPaddingq ~ (L rotationq ~ L 	topBorderq ~ L topBorderColorq ~ 'L 
topPaddingq ~ (L verticalAlignmentq ~ xr .net.sf.jasperreports.engine.base.JRBaseElement      'Ø I heightZ isPrintInFirstWholeBandZ isPrintRepeatedValuesZ isPrintWhenDetailOverflowsZ isRemoveLineWhenBlankB positionTypeB stretchTypeI widthI xI yL 	backcolorq ~ 'L defaultStyleProvidert 4Lnet/sf/jasperreports/engine/JRDefaultStyleProvider;L elementGroupq ~ L 	forecolorq ~ 'L keyq ~ L modeq ~ L parentStyleq ~ L parentStyleNameReferenceq ~ L printWhenExpressionq ~ L printWhenGroupChangesq ~ #L 
propertiesMapt -Lnet/sf/jasperreports/engine/JRPropertiesMap;[ propertyExpressionst 3[Lnet/sf/jasperreports/engine/JRPropertyExpression;xp   
       ×       pq ~ q ~  pppppppppppppppsr java.lang.Integerâ ¤÷8 I valuexq ~    ppppppppppsr .net.sf.jasperreports.engine.base.JRBaseLineBox      'Ø L 
bottomPaddingq ~ (L 	bottomPent +Lnet/sf/jasperreports/engine/base/JRBoxPen;L boxContainert ,Lnet/sf/jasperreports/engine/JRBoxContainer;L leftPaddingq ~ (L leftPenq ~ 2L paddingq ~ (L penq ~ 2L rightPaddingq ~ (L rightPenq ~ 2L 
topPaddingq ~ (L topPenq ~ 2xppsr 3net.sf.jasperreports.engine.base.JRBaseBoxBottomPen      'Ø  xr -net.sf.jasperreports.engine.base.JRBaseBoxPen      'Ø L lineBoxq ~ )xr *net.sf.jasperreports.engine.base.JRBasePen      'Ø L 	lineColorq ~ 'L 	lineStyleq ~ L 	lineWidtht Ljava/lang/Float;L penContainert ,Lnet/sf/jasperreports/engine/JRPenContainer;xppppq ~ 4q ~ 4q ~ .psr 1net.sf.jasperreports.engine.base.JRBaseBoxLeftPen      'Ø  xq ~ 6pppq ~ 4q ~ 4psq ~ 6pppq ~ 4q ~ 4psr 2net.sf.jasperreports.engine.base.JRBaseBoxRightPen      'Ø  xq ~ 6pppq ~ 4q ~ 4psr 0net.sf.jasperreports.engine.base.JRBaseBoxTopPen      'Ø  xq ~ 6pppq ~ 4q ~ 4pppppppppppppsq ~       ppsr 1net.sf.jasperreports.engine.base.JRBaseExpression      'Ø I id[ chunkst 0[Lnet/sf/jasperreports/engine/JRExpressionChunk;L valueClassNameq ~ L valueClassRealNameq ~ xp   ur 0[Lnet.sf.jasperreports.engine.JRExpressionChunk;mYÏÞiK£U  xp   sr 6net.sf.jasperreports.engine.base.JRBaseExpressionChunk      'Ø B typeL textq ~ xpt produto.descricaot java.lang.Stringppppppppppsq ~ "   
             pq ~ q ~  pppppppppppppppsq ~ /   sq ~ pppppppppsq ~ 1psq ~ 5pppq ~ Oq ~ Oq ~ Lpsq ~ ;pppq ~ Oq ~ Opsq ~ 6pppq ~ Oq ~ Opsq ~ >pppq ~ Oq ~ Opsq ~ @pppq ~ Oq ~ Opppppppppppppq ~ B      ppsq ~ C   uq ~ F   sq ~ Ht qtdeEstoqueAnteriort java.lang.Integerpppppppppt  sq ~ "   
       R  ×    pq ~ q ~  pppppppppppppppsq ~ /   q ~ Npppppppppsq ~ 1psq ~ 5pppq ~ ]q ~ ]q ~ [psq ~ ;pppq ~ ]q ~ ]psq ~ 6pppq ~ ]q ~ ]psq ~ >pppq ~ ]q ~ ]psq ~ @pppq ~ ]q ~ ]pppppppppppppq ~ B      ppsq ~ C   uq ~ F   sq ~ Ht qtdeBalancot java.lang.Integerpppppppppq ~ Zxp  w&   
pq ~ pppt javapsr .net.sf.jasperreports.engine.base.JRBaseDataset      'Ø Z isMainB whenResourceMissingType[ fieldst &[Lnet/sf/jasperreports/engine/JRField;L filterExpressionq ~ [ groupst &[Lnet/sf/jasperreports/engine/JRGroup;L nameq ~ [ 
parameterst *[Lnet/sf/jasperreports/engine/JRParameter;L 
propertiesMapq ~ ,L queryt %Lnet/sf/jasperreports/engine/JRQuery;L resourceBundleq ~ L scriptletClassq ~ [ 
scriptletst *[Lnet/sf/jasperreports/engine/JRScriptlet;[ 
sortFieldst *[Lnet/sf/jasperreports/engine/JRSortField;[ 	variablest )[Lnet/sf/jasperreports/engine/JRVariable;xpur &[Lnet.sf.jasperreports.engine.JRField;<ßÇN*òp  xp   sr ,net.sf.jasperreports.engine.base.JRBaseField      'Ø L descriptionq ~ L nameq ~ L 
propertiesMapq ~ ,L valueClassNameq ~ L valueClassRealNameq ~ xppt balanco.empresa.nomesr +net.sf.jasperreports.engine.JRPropertiesMap      'Ø L baseq ~ ,L propertiesListq ~ L 
propertiesMapt Ljava/util/Map;xppppt java.lang.Stringpsq ~ tpt balanco.canceladosq ~ wpppt java.lang.Booleanpsq ~ tpt balanco.dataCadastrosq ~ wpppt java.util.Datepsq ~ tpt balanco.empresa.cidade.nomesq ~ wpppt java.lang.Stringpsq ~ tpt balanco.empresa.enderecosq ~ wpppt java.lang.Stringpsq ~ tpt produto.descricaosq ~ wpppt java.lang.Stringpsq ~ tpt qtdeBalancosq ~ wpppt java.lang.Integerpsq ~ tpt qtdeEstoqueAnteriorsq ~ wpppt java.lang.Integerpsq ~ tpt balanco.usuarioCadastro.nomesq ~ wpppt java.lang.Stringpsq ~ tpt  balanco.usuarioCancelamento.nomesq ~ wpppt java.lang.Stringpsq ~ tpt balanco.dataCancelamentosq ~ wpppt java.util.Datepppt RelatorioBalancour *[Lnet.sf.jasperreports.engine.JRParameter;" *Ã`!  xp   sr 0net.sf.jasperreports.engine.base.JRBaseParameter      'Ø 	Z isForPromptingZ isSystemDefinedL defaultValueExpressionq ~ L descriptionq ~ L nameq ~ L nestedTypeNameq ~ L 
propertiesMapq ~ ,L valueClassNameq ~ L valueClassRealNameq ~ xpppt REPORT_PARAMETERS_MAPpsq ~ wpppt 
java.util.Mappsq ~ ¦ppt 
JASPER_REPORTpsq ~ wpppt (net.sf.jasperreports.engine.JasperReportpsq ~ ¦ppt REPORT_CONNECTIONpsq ~ wpppt java.sql.Connectionpsq ~ ¦ppt REPORT_MAX_COUNTpsq ~ wpppt java.lang.Integerpsq ~ ¦ppt REPORT_DATA_SOURCEpsq ~ wpppt (net.sf.jasperreports.engine.JRDataSourcepsq ~ ¦ppt REPORT_SCRIPTLETpsq ~ wpppt /net.sf.jasperreports.engine.JRAbstractScriptletpsq ~ ¦ppt 
REPORT_LOCALEpsq ~ wpppt java.util.Localepsq ~ ¦ppt REPORT_RESOURCE_BUNDLEpsq ~ wpppt java.util.ResourceBundlepsq ~ ¦ppt REPORT_TIME_ZONEpsq ~ wpppt java.util.TimeZonepsq ~ ¦ppt REPORT_FORMAT_FACTORYpsq ~ wpppt .net.sf.jasperreports.engine.util.FormatFactorypsq ~ ¦ppt REPORT_CLASS_LOADERpsq ~ wpppt java.lang.ClassLoaderpsq ~ ¦ppt REPORT_URL_HANDLER_FACTORYpsq ~ wpppt  java.net.URLStreamHandlerFactorypsq ~ ¦ppt REPORT_FILE_RESOLVERpsq ~ wpppt -net.sf.jasperreports.engine.util.FileResolverpsq ~ ¦ppt REPORT_VIRTUALIZERpsq ~ wpppt )net.sf.jasperreports.engine.JRVirtualizerpsq ~ ¦ppt IS_IGNORE_PAGINATIONpsq ~ wpppt java.lang.Booleanpsq ~ ¦ppt REPORT_TEMPLATESpsq ~ wpppt java.util.Collectionpsq ~ ¦ppt XML_DATA_DOCUMENTpsq ~ wpppt org.w3c.dom.Documentpsq ~ ¦ppt XML_DATE_PATTERNpsq ~ wpppt java.lang.Stringpsq ~ ¦ppt XML_NUMBER_PATTERNpsq ~ wpppq ~ îpsq ~ ¦ppt 
XML_LOCALEpsq ~ wpppq ~ Âpsq ~ ¦ppt 
XML_TIME_ZONEpsq ~ wpppq ~ Êpsq ~ ¦  ppt logoPadraoRelatoriopsq ~ wpppt java.io.InputStreampsq ~ wpsq ~    w   
t ireport.zoomt 	ireport.xt 	ireport.yxsr java.util.HashMapÚÁÃ`Ñ F 
loadFactorI 	thresholdxp?@     w      q ~ þt 1.0q ~ ÿt 0q ~ t 0xsr ,net.sf.jasperreports.engine.base.JRBaseQuery      'Ø [ chunkst +[Lnet/sf/jasperreports/engine/JRQueryChunk;L languageq ~ xppt xPathppppur )[Lnet.sf.jasperreports.engine.JRVariable;bæ|,·D  xp   sr /net.sf.jasperreports.engine.base.JRBaseVariable      'Ø 
B calculationB 
incrementTypeZ isSystemDefinedB 	resetTypeL 
expressionq ~ L incrementGroupq ~ #L incrementerFactoryClassNameq ~ L incrementerFactoryClassRealNameq ~ L initialValueExpressionq ~ L nameq ~ L 
resetGroupq ~ #L valueClassNameq ~ L valueClassRealNameq ~ xpppppsq ~ C    uq ~ F   sq ~ Ht new java.lang.Integer(1)q ~ ¶pt PAGE_NUMBERpq ~ ¶psq ~ppppsq ~ C   uq ~ F   sq ~ Ht new java.lang.Integer(1)q ~ ¶pt 
COLUMN_NUMBERpq ~ ¶psq ~sq ~ C   uq ~ F   sq ~ Ht new java.lang.Integer(1)q ~ ¶ppppsq ~ C   uq ~ F   sq ~ Ht new java.lang.Integer(0)q ~ ¶pt REPORT_COUNTpq ~ ¶psq ~sq ~ C   uq ~ F   sq ~ Ht new java.lang.Integer(1)q ~ ¶ppppsq ~ C   uq ~ F   sq ~ Ht new java.lang.Integer(0)q ~ ¶pt 
PAGE_COUNTpq ~ ¶psq ~sq ~ C   uq ~ F   sq ~ Ht new java.lang.Integer(1)q ~ ¶ppppsq ~ C   uq ~ F   sq ~ Ht new java.lang.Integer(0)q ~ ¶pt COLUMN_COUNTpq ~ ¶pq ~ £ppppppsq ~ sq ~    w   sr ,net.sf.jasperreports.engine.base.JRBaseImage      'Ø $I 
bookmarkLevelB evaluationTimeB hyperlinkTargetB 
hyperlinkTypeZ isLazyB onErrorTypeL anchorNameExpressionq ~ L borderq ~ L borderColorq ~ 'L bottomBorderq ~ L bottomBorderColorq ~ 'L 
bottomPaddingq ~ (L evaluationGroupq ~ #L 
expressionq ~ L horizontalAlignmentq ~ L hyperlinkAnchorExpressionq ~ L hyperlinkPageExpressionq ~ [ hyperlinkParametersq ~ $L hyperlinkReferenceExpressionq ~ L hyperlinkTooltipExpressionq ~ L isUsingCacheq ~ %L 
leftBorderq ~ L leftBorderColorq ~ 'L leftPaddingq ~ (L lineBoxq ~ )L 
linkTargetq ~ L linkTypeq ~ L paddingq ~ (L rightBorderq ~ L rightBorderColorq ~ 'L rightPaddingq ~ (L 
scaleImageq ~ L 	topBorderq ~ L topBorderColorq ~ 'L 
topPaddingq ~ (L verticalAlignmentq ~ xr 5net.sf.jasperreports.engine.base.JRBaseGraphicElement      'Ø L fillq ~ L linePent #Lnet/sf/jasperreports/engine/JRPen;L penq ~ xq ~ *   0     y        pq ~ q ~7sr java.awt.Color¥3u F falphaI valueL cst Ljava/awt/color/ColorSpace;[ 	frgbvaluet [F[ fvalueq ~?xp    ÿÿÿÿpppt image-1ppppppppsq ~ 7sq ~=    ÿÿÿÿpppppq ~<p      pppppppsq ~ C   uq ~ F   sq ~ Ht logoPadraoRelatoriot java.io.InputStreampppppppsr java.lang.BooleanÍ rÕúî Z valuexppppsq ~ 1psq ~ 5pppq ~Kq ~Kq ~<psq ~ ;pppq ~Kq ~Kpsq ~ 6pppq ~Kq ~Kpsq ~ >pppq ~Kq ~Kpsq ~ @pppq ~Kq ~Kpppppppppppsr 1net.sf.jasperreports.engine.base.JRBaseStaticText      'Ø L textq ~ xq ~ &   '       Ò   ì   pq ~ q ~7pppppppppppppppsq ~ /   sq ~ q ~Jppppppppsq ~ 1psq ~ 5pppq ~Uq ~Uq ~Rpsq ~ ;pppq ~Uq ~Upsq ~ 6pppq ~Uq ~Upsq ~ >pppq ~Uq ~Upsq ~ @pppq ~Uq ~Upppppppppppppq ~ Bt RelatÃ³rio de BalanÃ§osq ~ "          °   v   Apq ~ q ~7pppppppppppppppsq ~ /   ppppppppppsq ~ 1psq ~ 5pppq ~^q ~^q ~\psq ~ ;pppq ~^q ~^psq ~ 6pppq ~^q ~^psq ~ >pppq ~^q ~^psq ~ @pppq ~^q ~^pppppppppppppp      ppsq ~ C   	uq ~ F   sq ~ Ht balanco.dataCadastrot java.util.Dateppppppq ~Jppt dd/MM/yyyy HH:mm:sssq ~Q          u       Apq ~ q ~7pppppppppppppppsq ~ /   pq ~Jppppppppsq ~ 1psq ~ 5pppq ~lq ~lq ~jpsq ~ ;pppq ~lq ~lpsq ~ 6pppq ~lq ~lpsq ~ >pppq ~lq ~lpsq ~ @pppq ~lq ~lppppppppppppppt Data BalanÃ§o:sq ~Q          u       Opq ~ q ~7pppppppppppppppq ~]pq ~Jppppppppsq ~ 1psq ~ 5pppq ~tq ~tq ~spsq ~ ;pppq ~tq ~tpsq ~ 6pppq ~tq ~tpsq ~ >pppq ~tq ~tpsq ~ @pppq ~tq ~tppppppppppppppt SituaÃ§Ã£o:sq ~ "          ý   u   Opq ~ q ~7pppppppppppppppq ~]ppppppppppsq ~ 1psq ~ 5pppq ~|q ~|q ~{psq ~ ;pppq ~|q ~|psq ~ 6pppq ~|q ~|psq ~ >pppq ~|q ~|psq ~ @pppq ~|q ~|pppppppppppppp      ppsq ~ C   
uq ~ F   sq ~ Ht balanco.canceladosq ~ Ht 2.equals(new Boolean(true))  ? "Cancelado": "Ativo"t java.lang.Stringppppppppppsq ~Q          u      ]pq ~ q ~7pppppppppppppppq ~]pq ~Jppppppppsq ~ 1psq ~ 5pppq ~q ~q ~psq ~ ;pppq ~q ~psq ~ 6pppq ~q ~psq ~ >pppq ~q ~psq ~ @pppq ~q ~ppppppppppppppt UsuÃ¡rio Cadastro:sq ~ "          ý   u   ]pq ~ q ~7pppppppppppppppq ~]ppppppppppsq ~ 1psq ~ 5pppq ~q ~q ~psq ~ ;pppq ~q ~psq ~ 6pppq ~q ~psq ~ >pppq ~q ~psq ~ @pppq ~q ~pppppppppppppp      ppsq ~ C   uq ~ F   sq ~ Ht balanco.usuarioCadastro.nomet java.lang.Stringppppppppppsq ~Q          u      kpq ~ q ~7pppppsq ~ C   uq ~ F   sq ~ Ht balanco.dataCancelamentosq ~ Ht 1 != null ? new Boolean(true) : new Boolean(false)q ~ âppppppppppq ~]pq ~Jppppppppsq ~ 1psq ~ 5pppq ~¤q ~¤q ~psq ~ ;pppq ~¤q ~¤psq ~ 6pppq ~¤q ~¤psq ~ >pppq ~¤q ~¤psq ~ @pppq ~¤q ~¤ppppppppppppppt Data Cancelamento:sq ~ "          ý   u   kpq ~ q ~7pppppppppppppppq ~]ppppppppppsq ~ 1psq ~ 5pppq ~¬q ~¬q ~«psq ~ ;pppq ~¬q ~¬psq ~ 6pppq ~¬q ~¬psq ~ >pppq ~¬q ~¬psq ~ @pppq ~¬q ~¬pppppppppppppp      ppsq ~ C   
uq ~ F   sq ~ Ht balanco.dataCancelamentot java.util.Dateppppppq ~Jppt dd/MM/yyyy HH:mm:sssq ~Q          u      ypq ~ q ~7pppppsq ~ C   uq ~ F   sq ~ Ht balanco.dataCancelamentosq ~ Ht 1 != null ? new Boolean(true) : new Boolean(false)q ~ âppppppppppq ~]pq ~Jppppppppsq ~ 1psq ~ 5pppq ~¿q ~¿q ~¸psq ~ ;pppq ~¿q ~¿psq ~ 6pppq ~¿q ~¿psq ~ >pppq ~¿q ~¿psq ~ @pppq ~¿q ~¿ppppppppppppppt Usuario Cancelamento:sq ~ "          ý   u   ypq ~ q ~7pppppppppppppppq ~]ppppppppppsq ~ 1psq ~ 5pppq ~Çq ~Çq ~Æpsq ~ ;pppq ~Çq ~Çpsq ~ 6pppq ~Çq ~Çpsq ~ >pppq ~Çq ~Çpsq ~ @pppq ~Çq ~Çpppppppppppppp      ppsq ~ C   uq ~ F   sq ~ Ht  balanco.usuarioCancelamento.nomet java.lang.Stringppppppq ~Jppq ~ Zsq ~ "          q   {   pq ~ q ~7pt 
textField-208pppppppppppppppq ~Jppppppppsq ~ 1psq ~ 5pppq ~Ôq ~Ôq ~Òpsq ~ ;pppq ~Ôq ~Ôpsq ~ 6pppq ~Ôq ~Ôpsq ~ >pppq ~Ôq ~Ôpsq ~ @pppq ~Ôq ~Ôppppt Helvetica-Boldppppppppp      ppsq ~ C   uq ~ F   sq ~ Ht balanco.empresa.nomet java.lang.Stringppppppsq ~I pppsq ~ "          q   {   pq ~ q ~7pt 
textField-209pppppppppppppppq ~Jppppppppsq ~ 1psq ~ 5pppq ~ãq ~ãq ~ápsq ~ ;pppq ~ãq ~ãpsq ~ 6pppq ~ãq ~ãpsq ~ >pppq ~ãq ~ãpsq ~ @pppq ~ãq ~ãppppt Helvetica-Boldppppppppp      ppsq ~ C   uq ~ F   sq ~ Ht balanco.empresa.enderecot java.lang.Stringppppppq ~àpppsq ~ "          q   {   "pq ~ q ~7pt 
textField-210pppppppppppppppq ~Jppppppppsq ~ 1psq ~ 5pppq ~ñq ~ñq ~ïpsq ~ ;pppq ~ñq ~ñpsq ~ 6pppq ~ñq ~ñpsq ~ >pppq ~ñq ~ñpsq ~ @pppq ~ñq ~ñppppt Helvetica-Boldppppppppp      ppsq ~ C   uq ~ F   sq ~ Ht balanco.empresa.cidade.nomet java.lang.Stringppppppq ~àpppsq ~Q           $   pq ~ q ~7pt 
staticText-14sq ~ pppppppppppt Microsoft Sans Serifsq ~ /   	q ~ Nq ~Jq ~Jpq ~àpq ~àpppsq ~ 1psq ~ 5sq ~=    ÿfffpppsq ~  sr java.lang.FloatÚíÉ¢Û<ðì F valuexq ~     q ~q ~q ~ýpsq ~ ;sq ~=    ÿfffpppq ~sq ~    q ~q ~psq ~ 6pppq ~q ~psq ~ >sq ~=    ÿfffpppq ~sq ~    q ~q ~psq ~ @sq ~=    ÿfffpppq ~sq ~    q ~q ~sq ~  pppt Helvetica-BoldObliqueppppppppsq ~ t eZillyon - Sistema Administrativo para Academias Desenvolvido por PACTO SoluÃ§Ãµes TecnolÃ³gicas Ltda.sq ~ "                +pq ~ q ~7pt 
textField-212ppppppppppppt Arialsq ~ /   pq ~Jppppppppsq ~ 1sq ~ /   sq ~ 5sq ~=    ÿfffpppq ~sq ~    q ~q ~q ~psq ~ ;sq ~=    ÿfffpppq ~sq ~    q ~q ~psq ~ 6pppq ~q ~psq ~ >sq ~=    ÿfffpppq ~sq ~    q ~q ~psq ~ @sq ~=    ÿ   pppq ~sq ~    q ~q ~ppppt Helvetica-Boldppppppppp      ppsq ~ C   uq ~ F   sq ~ Ht " " + sq ~ Ht PAGE_NUMBERsq ~ Ht  + ""t java.lang.Stringppppppq ~àpppsq ~Q          `  Ê    pq ~ q ~7pt 
staticText-15q ~ÿpppppppppppt Microsoft Sans Serifsq ~ /   q ~ Nq ~Jq ~Jpq ~àpq ~àpppsq ~ 1psq ~ 5sq ~=    ÿfffpppq ~sq ~    q ~7q ~7q ~3psq ~ ;sq ~=    ÿfffpppq ~sq ~    q ~7q ~7psq ~ 6pppq ~7q ~7psq ~ >sq ~=    ÿfffpppq ~sq ~    q ~7q ~7psq ~ @sq ~=    ÿfffpppq ~sq ~    q ~7q ~7q ~pppt Helvetica-BoldObliqueppppppppq ~t (0xx62) 3251-5820sq ~ "          K  Ô   +pq ~ q ~7pt 
textField-211ppppppppppppt Arialsq ~ /   q ~ Nq ~Jppppppppsq ~ 1sq ~ /   sq ~ 5sq ~=    ÿfffpppq ~sq ~    q ~Kq ~Kq ~Gpsq ~ ;sq ~=    ÿfffpppq ~sq ~    q ~Kq ~Kpsq ~ 6pppq ~Kq ~Kpsq ~ >sq ~=    ÿ   pppq ~sq ~    q ~Kq ~Kpsq ~ @sq ~=    ÿ   pppq ~sq ~    q ~Kq ~Kppppt Helvetica-Boldppppppppp      ppsq ~ C   uq ~ F   sq ~ Ht "PÃ¡gina: " + sq ~ Ht PAGE_NUMBERsq ~ Ht 	 + " de "t java.lang.Stringppppppq ~àpppsq ~Q          d       pq ~ q ~7pppppppppppppppppq ~Jppppppppsq ~ 1psq ~ 5pppq ~eq ~eq ~dpsq ~ ;pppq ~eq ~epsq ~ 6pppq ~eq ~epsq ~ >pppq ~eq ~epsq ~ @pppq ~eq ~epppppppppppppq ~ Bt Produtosq ~Q               pq ~ q ~7ppppppppppppppppq ~ Nq ~Jppppppppsq ~ 1psq ~ 5pppq ~mq ~mq ~lpsq ~ ;pppq ~mq ~mpsq ~ 6pppq ~mq ~mpsq ~ >pppq ~mq ~mpsq ~ @pppq ~mq ~mpppppppppppppq ~ Bt Qtde. Estoque Anteriorsq ~Q          l  ¾   pq ~ q ~7ppppppppppppppppq ~ Nq ~Jppppppppsq ~ 1psq ~ 5pppq ~uq ~uq ~tpsq ~ ;pppq ~uq ~upsq ~ 6pppq ~uq ~upsq ~ >pppq ~uq ~upsq ~ @pppq ~uq ~upppppppppppppq ~ Bt Qtde. BalanÃ§osr +net.sf.jasperreports.engine.base.JRBaseLine      'Ø B 	directionxq ~:         +       pq ~ q ~7ppppppppppsq ~ 7pppq ~}pxp  w&   ppsr 6net.sf.jasperreports.engine.design.JRReportCompileData      'Ø L crosstabCompileDataq ~ xL datasetCompileDataq ~ xL mainDatasetCompileDataq ~ xpsq ~?@     w       xsq ~?@     w       xur [B¬óøTà  xp  vÊþº¾   .' %RelatorioBalanco_1363442212113_933087  ,net/sf/jasperreports/engine/fill/JREvaluator  parameter_REPORT_LOCALE 2Lnet/sf/jasperreports/engine/fill/JRFillParameter; parameter_JASPER_REPORT parameter_REPORT_VIRTUALIZER parameter_REPORT_TIME_ZONE parameter_REPORT_FILE_RESOLVER parameter_logoPadraoRelatorio parameter_REPORT_SCRIPTLET parameter_REPORT_PARAMETERS_MAP parameter_REPORT_CONNECTION parameter_REPORT_CLASS_LOADER parameter_REPORT_DATA_SOURCE $parameter_REPORT_URL_HANDLER_FACTORY parameter_IS_IGNORE_PAGINATION parameter_XML_DATE_PATTERN parameter_XML_DATA_DOCUMENT parameter_REPORT_FORMAT_FACTORY parameter_REPORT_MAX_COUNT parameter_XML_LOCALE parameter_REPORT_TEMPLATES parameter_XML_NUMBER_PATTERN  parameter_REPORT_RESOURCE_BUNDLE parameter_XML_TIME_ZONE (field_balanco46usuarioCancelamento46nome .Lnet/sf/jasperreports/engine/fill/JRFillField; field_qtdeEstoqueAnterior field_balanco46dataCancelamento field_qtdeBalanco  field_balanco46empresa46endereco field_balanco46empresa46nome $field_balanco46usuarioCadastro46nome field_produto46descricao field_balanco46cancelado field_balanco46dataCadastro $field_balanco46empresa46cidade46nome variable_PAGE_NUMBER 1Lnet/sf/jasperreports/engine/fill/JRFillVariable; variable_COLUMN_NUMBER variable_REPORT_COUNT variable_PAGE_COUNT variable_COLUMN_COUNT <init> ()V Code . /
  1  	  3  	  5  	  7 	 	  9 
 	  ;  	  =  	  ? 
 	  A  	  C  	  E  	  G  	  I  	  K  	  M  	  O  	  Q  	  S  	  U  	  W  	  Y  	  [  	  ]  	  _  	  a  	  c   	  e ! 	  g " 	  i # 	  k $ 	  m % 	  o & 	  q ' 	  s ( )	  u * )	  w + )	  y , )	  { - )	  } LineNumberTable customizedInit 0(Ljava/util/Map;Ljava/util/Map;Ljava/util/Map;)V 
initParams (Ljava/util/Map;)V  
   
initFields  
   initVars  
   
REPORT_LOCALE  
java/util/Map  get &(Ljava/lang/Object;)Ljava/lang/Object;     0net/sf/jasperreports/engine/fill/JRFillParameter  
JASPER_REPORT  REPORT_VIRTUALIZER  REPORT_TIME_ZONE  REPORT_FILE_RESOLVER  logoPadraoRelatorio  REPORT_SCRIPTLET   REPORT_PARAMETERS_MAP ¢ REPORT_CONNECTION ¤ REPORT_CLASS_LOADER ¦ REPORT_DATA_SOURCE ¨ REPORT_URL_HANDLER_FACTORY ª IS_IGNORE_PAGINATION ¬ XML_DATE_PATTERN ® XML_DATA_DOCUMENT ° REPORT_FORMAT_FACTORY ² REPORT_MAX_COUNT ´ 
XML_LOCALE ¶ REPORT_TEMPLATES ¸ XML_NUMBER_PATTERN º REPORT_RESOURCE_BUNDLE ¼ 
XML_TIME_ZONE ¾  balanco.usuarioCancelamento.nome À ,net/sf/jasperreports/engine/fill/JRFillField Â qtdeEstoqueAnterior Ä balanco.dataCancelamento Æ qtdeBalanco È balanco.empresa.endereco Ê balanco.empresa.nome Ì balanco.usuarioCadastro.nome Î produto.descricao Ð balanco.cancelado Ò balanco.dataCadastro Ô balanco.empresa.cidade.nome Ö PAGE_NUMBER Ø /net/sf/jasperreports/engine/fill/JRFillVariable Ú 
COLUMN_NUMBER Ü REPORT_COUNT Þ 
PAGE_COUNT à COLUMN_COUNT â evaluate (I)Ljava/lang/Object; 
Exceptions java/lang/Throwable ç java/lang/Integer é (I)V . ë
 ê ì getValue ()Ljava/lang/Object; î ï
  ð java/io/InputStream ò
 Ã ð java/util/Date õ java/lang/Boolean ÷ (Z)V . ù
 ø ú equals (Ljava/lang/Object;)Z ü ý
 ø þ 	Cancelado  Ativo java/lang/String java/lang/StringBuffer   (Ljava/lang/String;)V .


 Û ð append ,(Ljava/lang/Object;)Ljava/lang/StringBuffer;
 toString ()Ljava/lang/String;
 	PÃ¡gina:   de  ,(Ljava/lang/String;)Ljava/lang/StringBuffer;
 evaluateOld getOldValue ï
 Ã
 Û evaluateEstimated getEstimatedValue# ï
 Û$ 
SourceFile !     &                 	     
               
                                                                                                !     "     #     $     %     &     '     ( )    * )    + )    , )    - )     . /  0  w     Ã*· 2*µ 4*µ 6*µ 8*µ :*µ <*µ >*µ @*µ B*µ D*µ F*µ H*µ J*µ L*µ N*µ P*µ R*µ T*µ V*µ X*µ Z*µ \*µ ^*µ `*µ b*µ d*µ f*µ h*µ j*µ l*µ n*µ p*µ r*µ t*µ v*µ x*µ z*µ |*µ ~±       ¢ (      	          "  '   , ! 1 " 6 # ; $ @ % E & J ' O ( T ) Y * ^ + c , h - m . r / w 0 | 1  2  3  4  5  6  7  8 ¤ 9 © : ® ; ³ < ¸ = ½ > Â      0   4     *+· *,· *-· ±           J  K 
 L  M     0  »    K*+¹  À µ 4*+¹  À µ 6*+¹  À µ 8*+¹  À µ :*+¹  À µ <*+¹  À µ >*+¡¹  À µ @*+£¹  À µ B*+¥¹  À µ D*+§¹  À µ F*+©¹  À µ H*+«¹  À µ J*+­¹  À µ L*+¯¹  À µ N*+±¹  À µ P*+³¹  À µ R*+µ¹  À µ T*+·¹  À µ V*+¹¹  À µ X*+»¹  À µ Z*+½¹  À µ \*+¿¹  À µ ^±       ^    U  V  W - X < Y K Z Z [ i \ x ]  ^  _ ¥ ` ´ a Ã b Ò c á d ð e ÿ f g h, i; jJ k     0   ê     ¦*+Á¹  À Ãµ `*+Å¹  À Ãµ b*+Ç¹  À Ãµ d*+É¹  À Ãµ f*+Ë¹  À Ãµ h*+Í¹  À Ãµ j*+Ï¹  À Ãµ l*+Ñ¹  À Ãµ n*+Ó¹  À Ãµ p*+Õ¹  À Ãµ r*+×¹  À Ãµ t±       2    s  t  u - v < w K x Z y i z x {  |  } ¥ ~     0   x     L*+Ù¹  À Ûµ v*+Ý¹  À Ûµ x*+ß¹  À Ûµ z*+á¹  À Ûµ |*+ã¹  À Ûµ ~±                -  <  K   ä å  æ     è 0  ô    Mª            m   y            ©   µ   Á   Í   Û   é      @  N  r        ª  È  ì  ú  » êY· íM§» êY· íM§» êY· íM§» êY· íM§y» êY· íM§m» êY· íM§a» êY· íM§U» êY· íM§I*´ >¶ ñÀ óM§;*´ r¶ ôÀ öM§-*´ p¶ ôÀ ø» øY· û¶ ÿ 	§ M§*´ l¶ ôÀM§ ú*´ d¶ ôÀ öÆ » øY· û§ » øY· ûM§ Ö*´ d¶ ôÀ öM§ È*´ d¶ ôÀ öÆ » øY· û§ » øY· ûM§ ¤*´ `¶ ôÀM§ *´ j¶ ôÀM§ *´ h¶ ôÀM§ z*´ t¶ ôÀM§ l»Y	·*´ v¶
À ê¶¶M§ N»Y·*´ v¶
À ê¶¶¶M§ **´ n¶ ôÀM§ *´ b¶ ôÀ êM§ *´ f¶ ôÀ êM,°       Ê 2      p  y  |     £  ¤  ¨  ©   ­ © ® ¬ ² µ ³ ¸ · Á ¸ Ä ¼ Í ½ Ð Á Û Â Þ Æ é Ç ì Ë Ì Ð Ñ Õ@ ÖC ÚN ÛQ ßr àu ä å é ê î ï óª ô­ øÈ ùË ýì þïúý  å  æ     è 0  ô    Mª            m   y            ©   µ   Á   Í   Û   é      @  N  r        ª  È  ì  ú  » êY· íM§» êY· íM§» êY· íM§» êY· íM§y» êY· íM§m» êY· íM§a» êY· íM§U» êY· íM§I*´ >¶ ñÀ óM§;*´ r¶ À öM§-*´ p¶ À ø» øY· û¶ ÿ 	§ M§*´ l¶ ÀM§ ú*´ d¶ À öÆ » øY· û§ » øY· ûM§ Ö*´ d¶ À öM§ È*´ d¶ À öÆ » øY· û§ » øY· ûM§ ¤*´ `¶ ÀM§ *´ j¶ ÀM§ *´ h¶ ÀM§ z*´ t¶ ÀM§ l»Y	·*´ v¶!À ê¶¶M§ N»Y·*´ v¶!À ê¶¶¶M§ **´ n¶ ÀM§ *´ b¶ À êM§ *´ f¶ À êM,°       Ê 2    p# y$ |( ) - . 2 3  7 ©8 ¬< µ= ¸A ÁB ÄF ÍG ÐK ÛL ÞP éQ ìUVZ[_@`CdNeQirjunostxy}ª~­ÈËìïúý " å  æ     è 0  ô    Mª            m   y            ©   µ   Á   Í   Û   é      @  N  r        ª  È  ì  ú  » êY· íM§» êY· íM§» êY· íM§» êY· íM§y» êY· íM§m» êY· íM§a» êY· íM§U» êY· íM§I*´ >¶ ñÀ óM§;*´ r¶ ôÀ öM§-*´ p¶ ôÀ ø» øY· û¶ ÿ 	§ M§*´ l¶ ôÀM§ ú*´ d¶ ôÀ öÆ » øY· û§ » øY· ûM§ Ö*´ d¶ ôÀ öM§ È*´ d¶ ôÀ öÆ » øY· û§ » øY· ûM§ ¤*´ `¶ ôÀM§ *´ j¶ ôÀM§ *´ h¶ ôÀM§ z*´ t¶ ôÀM§ l»Y	·*´ v¶%À ê¶¶M§ N»Y·*´ v¶%À ê¶¶¶M§ **´ n¶ ôÀM§ *´ b¶ ôÀ êM§ *´ f¶ ôÀ êM,°       Ê 2  § © p­ y® |² ³ · ¸ ¼ ½  Á ©Â ¬Æ µÇ ¸Ë ÁÌ ÄÐ ÍÑ ÐÕ ÛÖ ÞÚ éÛ ìßàäåé@êCîNïQórôuøùýþª­È
Ëìïúý ( &    t _1363442212113_933087t 2net.sf.jasperreports.engine.design.JRJavacCompiler