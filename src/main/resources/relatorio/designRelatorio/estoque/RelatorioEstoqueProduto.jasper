¬í sr (net.sf.jasperreports.engine.JasperReport      'Ø L compileDatat Ljava/io/Serializable;L compileNameSuffixt Ljava/lang/String;L 
compilerClassq ~ xr -net.sf.jasperreports.engine.base.JRBaseReport      'Ø +I PSEUDO_SERIAL_VERSION_UIDI bottomMarginI columnCountI 
columnSpacingI columnWidthZ ignorePaginationZ isFloatColumnFooterZ isSummaryNewPageZ  isSummaryWithPageHeaderAndFooterZ isTitleNewPageI 
leftMarginB orientationI 
pageHeightI 	pageWidthB 
printOrderI rightMarginI 	topMarginB whenNoDataTypeL 
backgroundt $Lnet/sf/jasperreports/engine/JRBand;L columnFooterq ~ L columnHeaderq ~ [ datasetst ([Lnet/sf/jasperreports/engine/JRDataset;L defaultFontt *Lnet/sf/jasperreports/engine/JRReportFont;L defaultStylet %Lnet/sf/jasperreports/engine/JRStyle;L detailq ~ L 
detailSectiont 'Lnet/sf/jasperreports/engine/JRSection;[ fontst +[Lnet/sf/jasperreports/engine/JRReportFont;L formatFactoryClassq ~ L 
importsSett Ljava/util/Set;L languageq ~ L lastPageFooterq ~ L mainDatasett 'Lnet/sf/jasperreports/engine/JRDataset;L nameq ~ L noDataq ~ L orientationValuet 2Lnet/sf/jasperreports/engine/type/OrientationEnum;L 
pageFooterq ~ L 
pageHeaderq ~ L printOrderValuet 1Lnet/sf/jasperreports/engine/type/PrintOrderEnum;[ stylest &[Lnet/sf/jasperreports/engine/JRStyle;L summaryq ~ [ 	templatest /[Lnet/sf/jasperreports/engine/JRReportTemplate;L titleq ~ L whenNoDataTypeValuet 5Lnet/sf/jasperreports/engine/type/WhenNoDataTypeEnum;xp  wî            +           J  S        sr +net.sf.jasperreports.engine.base.JRBaseBand      'Ø I PSEUDO_SERIAL_VERSION_UIDI heightZ isSplitAllowedL printWhenExpressiont *Lnet/sf/jasperreports/engine/JRExpression;L 	splitTypet Ljava/lang/Byte;L splitTypeValuet 0Lnet/sf/jasperreports/engine/type/SplitTypeEnum;xr 3net.sf.jasperreports.engine.base.JRBaseElementGroup      'Ø L childrent Ljava/util/List;L elementGroupt ,Lnet/sf/jasperreports/engine/JRElementGroup;xpsr java.util.ArrayListxÒÇa I sizexp    w    xp  wî    pp~r .net.sf.jasperreports.engine.type.SplitTypeEnum          xr java.lang.Enum          xpt STRETCHppur ([Lnet.sf.jasperreports.engine.JRDataset;L6Í¬D  xp   sr .net.sf.jasperreports.engine.base.JRBaseDataset      'Ø I PSEUDO_SERIAL_VERSION_UIDZ isMainB whenResourceMissingType[ fieldst &[Lnet/sf/jasperreports/engine/JRField;L filterExpressionq ~ [ groupst &[Lnet/sf/jasperreports/engine/JRGroup;L nameq ~ [ 
parameterst *[Lnet/sf/jasperreports/engine/JRParameter;L 
propertiesMapt -Lnet/sf/jasperreports/engine/JRPropertiesMap;L queryt %Lnet/sf/jasperreports/engine/JRQuery;L resourceBundleq ~ L scriptletClassq ~ [ 
scriptletst *[Lnet/sf/jasperreports/engine/JRScriptlet;[ 
sortFieldst *[Lnet/sf/jasperreports/engine/JRSortField;[ 	variablest )[Lnet/sf/jasperreports/engine/JRVariable;L whenResourceMissingTypeValuet >Lnet/sf/jasperreports/engine/type/WhenResourceMissingTypeEnum;xp  wî  pppt dataset1ur *[Lnet.sf.jasperreports.engine.JRParameter;" *Ã`!  xp   sr 0net.sf.jasperreports.engine.base.JRBaseParameter      'Ø 	Z isForPromptingZ isSystemDefinedL defaultValueExpressionq ~ L descriptionq ~ L nameq ~ L nestedTypeNameq ~ L 
propertiesMapq ~ &L valueClassNameq ~ L valueClassRealNameq ~ xpppt REPORT_PARAMETERS_MAPpsr +net.sf.jasperreports.engine.JRPropertiesMap      'Ø L baseq ~ &L propertiesListq ~ L 
propertiesMapt Ljava/util/Map;xppppt 
java.util.Mappsq ~ 0ppt 
JASPER_REPORTpsq ~ 3pppt (net.sf.jasperreports.engine.JasperReportpsq ~ 0ppt REPORT_CONNECTIONpsq ~ 3pppt java.sql.Connectionpsq ~ 0ppt REPORT_MAX_COUNTpsq ~ 3pppt java.lang.Integerpsq ~ 0ppt REPORT_DATA_SOURCEpsq ~ 3pppt (net.sf.jasperreports.engine.JRDataSourcepsq ~ 0ppt REPORT_SCRIPTLETpsq ~ 3pppt /net.sf.jasperreports.engine.JRAbstractScriptletpsq ~ 0ppt 
REPORT_LOCALEpsq ~ 3pppt java.util.Localepsq ~ 0ppt REPORT_RESOURCE_BUNDLEpsq ~ 3pppt java.util.ResourceBundlepsq ~ 0ppt REPORT_TIME_ZONEpsq ~ 3pppt java.util.TimeZonepsq ~ 0ppt REPORT_FORMAT_FACTORYpsq ~ 3pppt .net.sf.jasperreports.engine.util.FormatFactorypsq ~ 0ppt REPORT_CLASS_LOADERpsq ~ 3pppt java.lang.ClassLoaderpsq ~ 0ppt REPORT_URL_HANDLER_FACTORYpsq ~ 3pppt  java.net.URLStreamHandlerFactorypsq ~ 0ppt REPORT_FILE_RESOLVERpsq ~ 3pppt -net.sf.jasperreports.engine.util.FileResolverpsq ~ 0ppt REPORT_TEMPLATESpsq ~ 3pppt java.util.Collectionpsq ~ 3ppppppppur )[Lnet.sf.jasperreports.engine.JRVariable;bæ|,·D  xp   sr /net.sf.jasperreports.engine.base.JRBaseVariable      'Ø I PSEUDO_SERIAL_VERSION_UIDB calculationB 
incrementTypeZ isSystemDefinedB 	resetTypeL calculationValuet 2Lnet/sf/jasperreports/engine/type/CalculationEnum;L 
expressionq ~ L incrementGroupt %Lnet/sf/jasperreports/engine/JRGroup;L incrementTypeValuet 4Lnet/sf/jasperreports/engine/type/IncrementTypeEnum;L incrementerFactoryClassNameq ~ L incrementerFactoryClassRealNameq ~ L initialValueExpressionq ~ L nameq ~ L 
resetGroupq ~ pL resetTypeValuet 0Lnet/sf/jasperreports/engine/type/ResetTypeEnum;L valueClassNameq ~ L valueClassRealNameq ~ xp  wî   ~r 0net.sf.jasperreports.engine.type.CalculationEnum          xq ~ t SYSTEMpp~r 2net.sf.jasperreports.engine.type.IncrementTypeEnum          xq ~ t NONEppsr 1net.sf.jasperreports.engine.base.JRBaseExpression      'Ø I id[ chunkst 0[Lnet/sf/jasperreports/engine/JRExpressionChunk;L valueClassNameq ~ L valueClassRealNameq ~ xp    ur 0[Lnet.sf.jasperreports.engine.JRExpressionChunk;mYÏÞiK£U  xp   sr 6net.sf.jasperreports.engine.base.JRBaseExpressionChunk      'Ø B typeL textq ~ xpt new java.lang.Integer(1)q ~ Bpt PAGE_NUMBERp~r .net.sf.jasperreports.engine.type.ResetTypeEnum          xq ~ t REPORTq ~ Bpsq ~ n  wî   q ~ uppq ~ xppsq ~ z   uq ~ }   sq ~ t new java.lang.Integer(1)q ~ Bpt 
COLUMN_NUMBERp~q ~ t PAGEq ~ Bpsq ~ n  wî   ~q ~ tt COUNTsq ~ z   uq ~ }   sq ~ t new java.lang.Integer(1)q ~ Bppq ~ xppsq ~ z   uq ~ }   sq ~ t new java.lang.Integer(0)q ~ Bpt REPORT_COUNTpq ~ q ~ Bpsq ~ n  wî   q ~ sq ~ z   uq ~ }   sq ~ t new java.lang.Integer(1)q ~ Bppq ~ xppsq ~ z   uq ~ }   sq ~ t new java.lang.Integer(0)q ~ Bpt 
PAGE_COUNTpq ~ q ~ Bpsq ~ n  wî   q ~ sq ~ z   uq ~ }   sq ~ t new java.lang.Integer(1)q ~ Bppq ~ xppsq ~ z   uq ~ }   sq ~ t new java.lang.Integer(0)q ~ Bpt COLUMN_COUNTp~q ~ t COLUMNq ~ Bp~r <net.sf.jasperreports.engine.type.WhenResourceMissingTypeEnum          xq ~ t NULLpppsr .net.sf.jasperreports.engine.base.JRBaseSection      'Ø [ bandst %[Lnet/sf/jasperreports/engine/JRBand;xpur %[Lnet.sf.jasperreports.engine.JRBand;Ý~ìÊ5  xp   sq ~ sq ~    w   sr 0net.sf.jasperreports.engine.base.JRBaseTextField      'Ø I PSEUDO_SERIAL_VERSION_UIDI 
bookmarkLevelB evaluationTimeB hyperlinkTargetB 
hyperlinkTypeZ isStretchWithOverflowL anchorNameExpressionq ~ L evaluationGroupq ~ pL evaluationTimeValuet 5Lnet/sf/jasperreports/engine/type/EvaluationTimeEnum;L 
expressionq ~ L hyperlinkAnchorExpressionq ~ L hyperlinkPageExpressionq ~ [ hyperlinkParameterst 3[Lnet/sf/jasperreports/engine/JRHyperlinkParameter;L hyperlinkReferenceExpressionq ~ L hyperlinkTooltipExpressionq ~ L isBlankWhenNullt Ljava/lang/Boolean;L 
linkTargetq ~ L linkTypeq ~ L patternq ~ xr 2net.sf.jasperreports.engine.base.JRBaseTextElement      'Ø %I PSEUDO_SERIAL_VERSION_UIDL borderq ~ L borderColort Ljava/awt/Color;L bottomBorderq ~ L bottomBorderColorq ~ ¿L 
bottomPaddingt Ljava/lang/Integer;L fontNameq ~ L fontSizeq ~ ÀL horizontalAlignmentq ~ L horizontalAlignmentValuet 6Lnet/sf/jasperreports/engine/type/HorizontalAlignEnum;L isBoldq ~ ½L isItalicq ~ ½L 
isPdfEmbeddedq ~ ½L isStrikeThroughq ~ ½L isStyledTextq ~ ½L isUnderlineq ~ ½L 
leftBorderq ~ L leftBorderColorq ~ ¿L leftPaddingq ~ ÀL lineBoxt 'Lnet/sf/jasperreports/engine/JRLineBox;L lineSpacingq ~ L lineSpacingValuet 2Lnet/sf/jasperreports/engine/type/LineSpacingEnum;L markupq ~ L paddingq ~ ÀL pdfEncodingq ~ L pdfFontNameq ~ L 
reportFontq ~ L rightBorderq ~ L rightBorderColorq ~ ¿L rightPaddingq ~ ÀL rotationq ~ L 
rotationValuet /Lnet/sf/jasperreports/engine/type/RotationEnum;L 	topBorderq ~ L topBorderColorq ~ ¿L 
topPaddingq ~ ÀL verticalAlignmentq ~ L verticalAlignmentValuet 4Lnet/sf/jasperreports/engine/type/VerticalAlignEnum;xr .net.sf.jasperreports.engine.base.JRBaseElement      'Ø I PSEUDO_SERIAL_VERSION_UIDI heightZ isPrintInFirstWholeBandZ isPrintRepeatedValuesZ isPrintWhenDetailOverflowsZ isRemoveLineWhenBlankB positionTypeB stretchTypeI widthI xI yL 	backcolorq ~ ¿L defaultStyleProvidert 4Lnet/sf/jasperreports/engine/JRDefaultStyleProvider;L elementGroupq ~ L 	forecolorq ~ ¿L keyq ~ L modeq ~ L 	modeValuet +Lnet/sf/jasperreports/engine/type/ModeEnum;L parentStyleq ~ L parentStyleNameReferenceq ~ L positionTypeValuet 3Lnet/sf/jasperreports/engine/type/PositionTypeEnum;L printWhenExpressionq ~ L printWhenGroupChangesq ~ pL 
propertiesMapq ~ &[ propertyExpressionst 3[Lnet/sf/jasperreports/engine/JRPropertyExpression;L stretchTypeValuet 2Lnet/sf/jasperreports/engine/type/StretchTypeEnum;xp  wî   
        .  /   pq ~ q ~ ¸pppppp~r 1net.sf.jasperreports.engine.type.PositionTypeEnum          xq ~ t FIX_RELATIVE_TO_TOPpppp~r 0net.sf.jasperreports.engine.type.StretchTypeEnum          xq ~ t 
NO_STRETCH  wîppppppsr java.lang.Integerâ ¤÷8 I valuexr java.lang.Number¬à  xp   p~r 4net.sf.jasperreports.engine.type.HorizontalAlignEnum          xq ~ t RIGHTpppppppppsr .net.sf.jasperreports.engine.base.JRBaseLineBox      'Ø L 
bottomPaddingq ~ ÀL 	bottomPent +Lnet/sf/jasperreports/engine/base/JRBoxPen;L boxContainert ,Lnet/sf/jasperreports/engine/JRBoxContainer;L leftPaddingq ~ ÀL leftPenq ~ ÚL paddingq ~ ÀL penq ~ ÚL rightPaddingq ~ ÀL rightPenq ~ ÚL 
topPaddingq ~ ÀL topPenq ~ Úxppsr 3net.sf.jasperreports.engine.base.JRBaseBoxBottomPen      'Ø  xr -net.sf.jasperreports.engine.base.JRBaseBoxPen      'Ø L lineBoxq ~ Âxr *net.sf.jasperreports.engine.base.JRBasePen      'Ø I PSEUDO_SERIAL_VERSION_UIDL 	lineColorq ~ ¿L 	lineStyleq ~ L lineStyleValuet 0Lnet/sf/jasperreports/engine/type/LineStyleEnum;L 	lineWidtht Ljava/lang/Float;L penContainert ,Lnet/sf/jasperreports/engine/JRPenContainer;xp  wîppppq ~ Üq ~ Üq ~ Ìpsr 1net.sf.jasperreports.engine.base.JRBaseBoxLeftPen      'Ø  xq ~ Þ  wîppppq ~ Üq ~ Üpsq ~ Þ  wîppppq ~ Üq ~ Üpsr 2net.sf.jasperreports.engine.base.JRBaseBoxRightPen      'Ø  xq ~ Þ  wîppppq ~ Üq ~ Üpsr 0net.sf.jasperreports.engine.base.JRBaseBoxTopPen      'Ø  xq ~ Þ  wîppppq ~ Üq ~ Üpppppppppppppppp~r 2net.sf.jasperreports.engine.type.VerticalAlignEnum          xq ~ t MIDDLE  wî        pp~r 3net.sf.jasperreports.engine.type.EvaluationTimeEnum          xq ~ t NOWsq ~ z   uq ~ }   sq ~ t estoquet java.lang.Integerpppppppppt  sq ~ º  wî   
        Æ   &    pq ~ q ~ ¸ppppppq ~ Îppppq ~ Ñ  wîppppppq ~ Õpppppppppppsq ~ Ùpsq ~ Ý  wîppppq ~ øq ~ øq ~ ÷psq ~ ä  wîppppq ~ øq ~ øpsq ~ Þ  wîppppq ~ øq ~ øpsq ~ ç  wîppppq ~ øq ~ øpsq ~ é  wîppppq ~ øq ~ øppppppppppppppppq ~ ì  wî       ppq ~ ïsq ~ z   uq ~ }   sq ~ t produto.descricaot java.lang.Stringppppppppppsq ~ º  wî   
        B   â   pq ~ q ~ ¸ppppppq ~ Îppppq ~ Ñ  wîppppppq ~ Õpq ~ ×pppppppppsq ~ Ùpsq ~ Ý  wîppppq ~q ~q ~psq ~ ä  wîppppq ~q ~psq ~ Þ  wîppppq ~q ~psq ~ ç  wîppppq ~q ~psq ~ é  wîppppq ~q ~ppppppppppppppppq ~ ì  wî        ppq ~ ïsq ~ z   uq ~ }   sq ~ t 
estoqueMinimot java.lang.Integerpppppppppq ~ ösq ~ º  wî   
        C  e   pq ~ q ~ ¸ppppppq ~ Îppppq ~ Ñ  wîppppppq ~ Õpq ~ ×pppppppppsq ~ Ùpsq ~ Ý  wîppppq ~q ~q ~psq ~ ä  wîppppq ~q ~psq ~ Þ  wîppppq ~q ~psq ~ ç  wîppppq ~q ~psq ~ é  wîppppq ~q ~ppppppppppppppppq ~ ì  wî        ppq ~ ïsq ~ z   uq ~ }   sq ~ t 
valorUnitariot java.lang.Doublepppppppppt #,##0.00sq ~ º  wî   
        U  ¬   pq ~ q ~ ¸ppppppq ~ Îppppq ~ Ñ  wîppppppq ~ Õpq ~ ×pppppppppsq ~ Ùpsq ~ Ý  wîppppq ~q ~q ~psq ~ ä  wîppppq ~q ~psq ~ Þ  wîppppq ~q ~psq ~ ç  wîppppq ~q ~psq ~ é  wîppppq ~q ~ppppppppppppppppq ~ ì  wî        ppq ~ ïsq ~ z   uq ~ }   sq ~ t valorTotalEstoquet java.lang.Doublepppppppppt #,##0.00sq ~ º  wî   
          
   pq ~ q ~ ¸ppppppq ~ Îppppq ~ Ñ  wîppppppq ~ Õpppppppppppsq ~ Ùpsq ~ Ý  wîppppq ~*q ~*q ~)psq ~ ä  wîppppq ~*q ~*psq ~ Þ  wîppppq ~*q ~*psq ~ ç  wîppppq ~*q ~*psq ~ é  wîppppq ~*q ~*ppppppppppppppppq ~ ì  wî        ppq ~ ïsq ~ z    uq ~ }   sq ~ t #(new DecimalFormat("0000")).format(sq ~ t produto.codigosq ~ t )t java.lang.Stringppppppppppsq ~ º  wî   
        )      pq ~ q ~ ¸ppppppq ~ Îppppq ~ Ñ  wîppppppq ~ Õpq ~ ×pppppppppsq ~ Ùpsq ~ Ý  wîppppq ~:q ~:q ~9psq ~ ä  wîppppq ~:q ~:psq ~ Þ  wîppppq ~:q ~:psq ~ ç  wîppppq ~:q ~:psq ~ é  wîppppq ~:q ~:ppppppppppppppppq ~ ì  wî        ppq ~ ïsq ~ z   !uq ~ }   sq ~ t pontost java.lang.Integerpppppppppq ~ öxp  wî   ppq ~ pppt javapsq ~ "  wî ur &[Lnet.sf.jasperreports.engine.JRField;<ßÇN*òp  xp   sr ,net.sf.jasperreports.engine.base.JRBaseField      'Ø L descriptionq ~ L nameq ~ L 
propertiesMapq ~ &L valueClassNameq ~ L valueClassRealNameq ~ xppt empresa.nomesq ~ 3pppt java.lang.Stringpsq ~Ipt empresa.cidade.nomesq ~ 3pppt java.lang.Stringpsq ~Ipt empresa.enderecosq ~ 3pppt java.lang.Stringpsq ~Ipt produto.descricaosq ~ 3pppt java.lang.Stringpsq ~Ipt estoquesq ~ 3pppt java.lang.Integerpsq ~Ipt 
estoqueMinimosq ~ 3pppt java.lang.Integerpsq ~Ipt "produto.categoriaProduto.descricaosq ~ 3pppt java.lang.Stringpsq ~Ipt produto.codigosq ~ 3pppt java.lang.Integerpsq ~Ipt 
valorUnitariosq ~ 3pppt java.lang.Doublepsq ~Ipt valorTotalEstoquesq ~ 3pppt java.lang.Doublepsq ~Ipt pontossq ~ 3pppq ~ Bppur &[Lnet.sf.jasperreports.engine.JRGroup;@£_zLýxê  xp   sr ,net.sf.jasperreports.engine.base.JRBaseGroup      'Ø I PSEUDO_SERIAL_VERSION_UIDB footerPositionZ isReprintHeaderOnEachPageZ isResetPageNumberZ isStartNewColumnZ isStartNewPageZ keepTogetherI minHeightToStartNewPageL 
countVariablet (Lnet/sf/jasperreports/engine/JRVariable;L 
expressionq ~ L footerPositionValuet 5Lnet/sf/jasperreports/engine/type/FooterPositionEnum;L groupFooterq ~ L groupFooterSectionq ~ L groupHeaderq ~ L groupHeaderSectionq ~ L nameq ~ xp  wî          sq ~ n  wî   q ~ sq ~ z   	uq ~ }   sq ~ t new java.lang.Integer(1)q ~ Bppq ~ xppsq ~ z   
uq ~ }   sq ~ t new java.lang.Integer(0)q ~ Bpt categoria_COUNTq ~z~q ~ t GROUPq ~ Bpsq ~ z   
uq ~ }   sq ~ t "produto.categoriaProduto.descricaot java.lang.Objectp~r 3net.sf.jasperreports.engine.type.FooterPositionEnum          xq ~ t NORMALpsq ~ ³ppsq ~ ³uq ~ ¶   sq ~ sq ~    w   sq ~ º  wî                pq ~ q ~ppppppq ~ Îppppq ~ Ñ  wîpppppppppsr java.lang.BooleanÍ rÕúî Z valuexpppppppppsq ~ Ùpsq ~ Ý  wîppppq ~q ~q ~psq ~ ä  wîppppq ~q ~psq ~ Þ  wîppppq ~q ~psq ~ ç  wîppppq ~q ~psq ~ é  wîppppq ~q ~ppppppppppppppppp  wî        ppq ~ ïsq ~ z   uq ~ }   sq ~ t "CATEGORIA: " + sq ~ t "produto.categoriaProduto.descricaot java.lang.Stringppppppppppsr +net.sf.jasperreports.engine.base.JRBaseLine      'Ø I PSEUDO_SERIAL_VERSION_UIDB 	directionL directionValuet 4Lnet/sf/jasperreports/engine/type/LineDirectionEnum;xr 5net.sf.jasperreports.engine.base.JRBaseGraphicElement      'Ø I PSEUDO_SERIAL_VERSION_UIDL fillq ~ L 	fillValuet +Lnet/sf/jasperreports/engine/type/FillEnum;L linePent #Lnet/sf/jasperreports/engine/JRPen;L penq ~ xq ~ Æ  wî          +       pq ~ q ~ppppppq ~ Îppppq ~ Ñ  wîppsq ~ ß  wîppppq ~©p  wî ~r 2net.sf.jasperreports.engine.type.LineDirectionEnum          xq ~ t TOP_DOWNxp  wî   pppt 	categoriat RelatorioEstoqueProdutouq ~ .   sq ~ 0ppq ~ 2psq ~ 3pppq ~ 6psq ~ 0ppq ~ 8psq ~ 3pppq ~ :psq ~ 0ppq ~ <psq ~ 3pppq ~ >psq ~ 0ppq ~ @psq ~ 3pppq ~ Bpsq ~ 0ppq ~ Dpsq ~ 3pppq ~ Fpsq ~ 0ppq ~ Hpsq ~ 3pppq ~ Jpsq ~ 0ppq ~ Lpsq ~ 3pppq ~ Npsq ~ 0ppq ~ Ppsq ~ 3pppq ~ Rpsq ~ 0ppq ~ Tpsq ~ 3pppq ~ Vpsq ~ 0ppq ~ Xpsq ~ 3pppq ~ Zpsq ~ 0ppq ~ \psq ~ 3pppq ~ ^psq ~ 0ppq ~ `psq ~ 3pppq ~ bpsq ~ 0ppq ~ dpsq ~ 3pppq ~ fpsq ~ 0ppq ~ hpsq ~ 3pppq ~ jpsq ~ 0ppt REPORT_VIRTUALIZERpsq ~ 3pppt )net.sf.jasperreports.engine.JRVirtualizerpsq ~ 0ppt IS_IGNORE_PAGINATIONpsq ~ 3pppt java.lang.Booleanpsq ~ 0ppt XML_DATA_DOCUMENTpsq ~ 3pppt org.w3c.dom.Documentpsq ~ 0ppt XML_DATE_PATTERNpsq ~ 3pppt java.lang.Stringpsq ~ 0ppt XML_NUMBER_PATTERNpsq ~ 3pppq ~Üpsq ~ 0ppt 
XML_LOCALEpsq ~ 3pppq ~ Npsq ~ 0ppt 
XML_TIME_ZONEpsq ~ 3pppq ~ Vpsq ~ 0  ppt logoPadraoRelatoriopsq ~ 3pppt java.io.InputStreampsq ~ 0 ppt tipoVisualizacaopsq ~ 3pppt java.lang.Integerpsq ~ 0 ppt 
nomeCategoriapsq ~ 3pppt java.lang.Stringpsq ~ 0 ppt 
statusProdutopsq ~ 3pppt java.lang.Stringpsq ~ 0  ppt 	ordenacaopsq ~ 3pppt java.lang.Stringpsq ~ 0  ppt 
valorImpressopsq ~ 3pppt java.lang.Stringpsq ~ 0 ppt 
totalizadorespsq ~ 3pppt java.lang.Objectpsq ~ 0  sq ~ z    uq ~ }   sq ~ t }"C:\\PactoJ\\Desenvolvimento\\Sistemas\\ZillyonWeb\\tronco-novo\\src\\main\\resources\\relatorio\\designRelatorio\\estoque\\"t java.lang.Stringppt 
SUBREPORT_DIRpsq ~ 3pppq ~psq ~ 3psq ~    w   t ireport.zoomt 	ireport.xt 	ireport.yxsr java.util.HashMapÚÁÃ`Ñ F 
loadFactorI 	thresholdxp?@     w      q ~t 1.5q ~
t 0q ~t 135xsr ,net.sf.jasperreports.engine.base.JRBaseQuery      'Ø [ chunkst +[Lnet/sf/jasperreports/engine/JRQueryChunk;L languageq ~ xppt xPathpppur *[Lnet.sf.jasperreports.engine.JRSortField;q.VZfç  xp   sr 0net.sf.jasperreports.engine.base.JRBaseSortField      'Ø I PSEUDO_SERIAL_VERSION_UIDB orderL nameq ~ L 
orderValuet 0Lnet/sf/jasperreports/engine/type/SortOrderEnum;xp  wî t "produto.categoriaProduto.descricao~r .net.sf.jasperreports.engine.type.SortOrderEnum          xq ~ t 	ASCENDINGuq ~ l   sq ~ n  wî   q ~ uppq ~ xppsq ~ z   uq ~ }   sq ~ t new java.lang.Integer(1)q ~ Bpq ~ pq ~ q ~ Bpsq ~ n  wî   q ~ uppq ~ xppsq ~ z   uq ~ }   sq ~ t new java.lang.Integer(1)q ~ Bpq ~ pq ~ q ~ Bpsq ~ n  wî   q ~ sq ~ z   uq ~ }   sq ~ t new java.lang.Integer(1)q ~ Bppq ~ xppsq ~ z   uq ~ }   sq ~ t new java.lang.Integer(0)q ~ Bpq ~ pq ~ q ~ Bpsq ~ n  wî   q ~ sq ~ z   uq ~ }   sq ~ t new java.lang.Integer(1)q ~ Bppq ~ xppsq ~ z   uq ~ }   sq ~ t new java.lang.Integer(0)q ~ Bpq ~ £pq ~ q ~ Bpsq ~ n  wî   q ~ sq ~ z   uq ~ }   sq ~ t new java.lang.Integer(1)q ~ Bppq ~ xppsq ~ z   uq ~ }   sq ~ t new java.lang.Integer(0)q ~ Bpq ~ ­pq ~ ®q ~ Bpq ~{sq ~ n  wî    ~q ~ tt SUMsq ~ z   uq ~ }   sq ~ t valorTotalEstoquet java.lang.Doubleppq ~ xpppt valorTotalGeralpq ~ q ~Npsq ~ n  wî    q ~Hsq ~ z   uq ~ }   sq ~ t estoquet java.lang.Integerppq ~ xpppt totalEstoquepq ~ q ~Upq ~ ±q ~¯sq ~ sq ~    w   sr 1net.sf.jasperreports.engine.base.JRBaseStaticText      'Ø L textq ~ xq ~ ¾  wî                
pq ~ q ~Wppppppq ~ Îppppq ~ Ñ  wîppppppsq ~ Ó   p~q ~ Öt CENTERq ~ppppppppsq ~ Ùpsq ~ Ý  wîppppq ~^q ~^q ~Zpsq ~ ä  wîppppq ~^q ~^psq ~ Þ  wîppppq ~^q ~^psq ~ ç  wîppppq ~^q ~^psq ~ é  wîppppq ~^q ~^pppppppppppppppppt INÃ£o hÃ¡ dados para serem exibidos ! verifique os parÃ¢metros informados.xp  wî   2ppp~r 0net.sf.jasperreports.engine.type.OrientationEnum          xq ~ t PORTRAITpsq ~ sq ~    w   sq ~Y  wî          ,      àpq ~ q ~hppppppq ~ Îpppp~q ~ Ðt RELATIVE_TO_BAND_HEIGHT  wîpppppppppq ~ppppppppsq ~ Ùpsq ~ Ý  wîppppq ~mq ~mq ~jpsq ~ ä  wîppppq ~mq ~mpsq ~ Þ  wîppppq ~mq ~mpsq ~ ç  wîppppq ~mq ~mpsq ~ é  wîppppq ~mq ~mpppppppppppppppp~q ~ ët BOTTOMt Codigosq ~Y  wî          =   â   ßpq ~ q ~hppppppq ~ Îpppp~q ~ Ðt RELATIVE_TO_TALLEST_OBJECT  wîppppppsq ~ Ó   
pq ~ ×q ~ppppppppsq ~ Ùpsq ~ Ý  wîppppq ~zq ~zq ~vpsq ~ ä  wîppppq ~zq ~zpsq ~ Þ  wîppppq ~zq ~zpsq ~ ç  wîppppq ~zq ~zpsq ~ é  wîppppq ~zq ~zppppppppppppppppq ~st Est. MÃ­nimosq ~Y  wî           .  ·   ßpq ~ q ~hppppppq ~ Îppppq ~ Ñ  wîppppppq ~ypq ~ ×q ~ppppppppsq ~ Ùpsq ~ Ý  wîppppq ~q ~q ~psq ~ ä  wîppppq ~q ~psq ~ Þ  wîppppq ~q ~psq ~ ç  wîppppq ~q ~psq ~ é  wîppppq ~q ~ppppppppppppppppq ~st 	Vl. Totalsq ~Y  wî           ©   -   àpq ~ q ~hppppppq ~ Îppppq ~ Ñ  wîpppppppppq ~ppppppppsq ~ Ùpsq ~ Ý  wîppppq ~q ~q ~psq ~ ä  wîppppq ~q ~psq ~ Þ  wîppppq ~q ~psq ~ ç  wîppppq ~q ~psq ~ é  wîppppq ~q ~ppppppppppppppppq ~st Produtosq ~Y  wî          G  $   àpq ~ q ~hppppppq ~ Îppppq ~w  wîppppppq ~ypq ~ ×q ~ppppppppsq ~ Ùpsq ~ Ý  wîppppq ~q ~q ~psq ~ ä  wîppppq ~q ~psq ~ Þ  wîppppq ~q ~psq ~ ç  wîppppq ~q ~psq ~ é  wîppppq ~q ~ppppppppppppppppq ~st 
Est. Atualsq ~Y  wî           >  r   ßpq ~ q ~hppppppq ~ Îppppq ~ Ñ  wîppppppq ~ypq ~ ×q ~ppppppppsq ~ Ùpsq ~ Ý  wîppppq ~q ~q ~psq ~ ä  wîppppq ~q ~psq ~ Þ  wîppppq ~q ~psq ~ ç  wîppppq ~q ~psq ~ é  wîppppq ~q ~ppppppppppppppppq ~st 
Vl. UnitÃ¡riosq ~ º  wî           q   {   pq ~ q ~hpt 
textField-208ppppq ~ Îppppq ~ Ñ  wîpppppppppq ~ppppppppsq ~ Ùpsq ~ Ý  wîppppq ~£q ~£q ~¡psq ~ ä  wîppppq ~£q ~£psq ~ Þ  wîppppq ~£q ~£psq ~ ç  wîppppq ~£q ~£psq ~ é  wîppppq ~£q ~£pppppt Helvetica-Boldppppppppppp  wî        ppq ~ ïsq ~ z   uq ~ }   sq ~ t empresa.nomet java.lang.Stringppppppsq ~ pppsq ~ º  wî           q   {   pq ~ q ~hpt 
textField-209ppppq ~ Îppppq ~ Ñ  wîpppppppppq ~ppppppppsq ~ Ùpsq ~ Ý  wîppppq ~²q ~²q ~°psq ~ ä  wîppppq ~²q ~²psq ~ Þ  wîppppq ~²q ~²psq ~ ç  wîppppq ~²q ~²psq ~ é  wîppppq ~²q ~²pppppt Helvetica-Boldppppppppppp  wî        ppq ~ ïsq ~ z   uq ~ }   sq ~ t empresa.enderecot java.lang.Stringppppppq ~¯pppsq ~ º  wî           q   {   &pq ~ q ~hpt 
textField-210ppppq ~ Îppppq ~ Ñ  wîpppppppppq ~ppppppppsq ~ Ùpsq ~ Ý  wîppppq ~Àq ~Àq ~¾psq ~ ä  wîppppq ~Àq ~Àpsq ~ Þ  wîppppq ~Àq ~Àpsq ~ ç  wîppppq ~Àq ~Àpsq ~ é  wîppppq ~Àq ~Àpppppt Helvetica-Boldppppppppppp  wî        ppq ~ ïsq ~ z   uq ~ }   sq ~ t empresa.cidade.nomet java.lang.Stringppppppq ~¯pppsq ~Y  wî            $   pq ~ q ~hpt 
staticText-14p~r )net.sf.jasperreports.engine.type.ModeEnum          xq ~ t OPAQUEppq ~ Îppppq ~ Ñ  wîpppppt Microsoft Sans Serifsq ~ Ó   	pq ~ ×q ~q ~pq ~¯pq ~¯pppsq ~ Ùpsq ~ Ý  wîsr java.awt.Color¥3u F falphaI valueL cst Ljava/awt/color/ColorSpace;[ 	frgbvaluet [F[ fvalueq ~×xp    ÿfffpppp~r .net.sf.jasperreports.engine.type.LineStyleEnum          xq ~ t SOLIDsr java.lang.FloatÚíÉ¢Û<ðì F valuexq ~ Ô    q ~Óq ~Óq ~Ìpsq ~ ä  wîsq ~Õ    ÿfffppppq ~Úsq ~Ü    q ~Óq ~Ópsq ~ Þ  wîppppq ~Óq ~Ópsq ~ ç  wîsq ~Õ    ÿfffppppq ~Úsq ~Ü    q ~Óq ~Ópsq ~ é  wîsq ~Õ    ÿfffppppq ~Úsq ~Ü    q ~Óq ~Óp~r 0net.sf.jasperreports.engine.type.LineSpacingEnum          xq ~ t SINGLEpppt Helvetica-BoldObliquepppppppppp~q ~ ët TOPt eZillyon - Sistema Administrativo para Academias Desenvolvido por PACTO SoluÃ§Ãµes TecnolÃ³gicas Ltda.sq ~ º  wî                /pq ~ q ~hpt 
textField-212ppppq ~ Îppppq ~ Ñ  wîpppppt Arialsq ~ Ó   ppq ~ppppppppsq ~ Ùsq ~ Ó   sq ~ Ý  wîsq ~Õ    ÿfffppppq ~Úsq ~Ü    q ~óq ~óq ~ïpsq ~ ä  wîsq ~Õ    ÿfffppppq ~Úsq ~Ü    q ~óq ~ópsq ~ Þ  wîppppq ~óq ~ópsq ~ ç  wîsq ~Õ    ÿfffppppq ~Úsq ~Ü    q ~óq ~ópsq ~ é  wîsq ~Õ    ÿ   ppppq ~Úsq ~Ü    q ~óq ~ópppppt Helvetica-Boldppppppppppp  wî        pp~q ~ ît REPORTsq ~ z   uq ~ }   sq ~ t " " + sq ~ t PAGE_NUMBERsq ~ t  + ""t java.lang.Stringppppppq ~¯pppsq ~Y  wî           `  Ê   pq ~ q ~hpt 
staticText-15pq ~Ïppq ~ Îppppq ~ Ñ  wîpppppt Microsoft Sans Serifq ~òpq ~ ×q ~q ~pq ~¯pq ~¯pppsq ~ Ùpsq ~ Ý  wîsq ~Õ    ÿfffppppq ~Úsq ~Ü    q ~q ~q ~psq ~ ä  wîsq ~Õ    ÿfffppppq ~Úsq ~Ü    q ~q ~psq ~ Þ  wîppppq ~q ~psq ~ ç  wîsq ~Õ    ÿfffppppq ~Úsq ~Ü    q ~q ~psq ~ é  wîsq ~Õ    ÿfffppppq ~Úsq ~Ü    q ~q ~pq ~épppt Helvetica-BoldObliqueppppppppppq ~ìt (0xx62) 3251-5820sq ~ º  wî           K  Ô   /pq ~ q ~hpt 
textField-211ppppq ~ Îppppq ~ Ñ  wîpppppt Arialq ~òpq ~ ×q ~ppppppppsq ~ Ùq ~ôsq ~ Ý  wîsq ~Õ    ÿfffppppq ~Úsq ~Ü    q ~$q ~$q ~!psq ~ ä  wîsq ~Õ    ÿfffppppq ~Úsq ~Ü    q ~$q ~$psq ~ Þ  wîppppq ~$q ~$psq ~ ç  wîsq ~Õ    ÿ   ppppq ~Úsq ~Ü    q ~$q ~$psq ~ é  wîsq ~Õ    ÿ   ppppq ~Úsq ~Ü    q ~$q ~$pppppt Helvetica-Boldppppppppppp  wî        ppq ~ ïsq ~ z   uq ~ }   sq ~ t "PÃ¡gina: " + sq ~ t PAGE_NUMBERsq ~ t 	 + " de "t java.lang.Stringppppppq ~¯pppsq ~ º  wî          1   u   xpq ~ q ~hppppppq ~ Îppppq ~ Ñ  wîppppppppppppppppppsq ~ Ùpsq ~ Ý  wîppppq ~=q ~=q ~<psq ~ ä  wîppppq ~=q ~=psq ~ Þ  wîppppq ~=q ~=psq ~ ç  wîppppq ~=q ~=psq ~ é  wîppppq ~=q ~=ppppppppppppppppq ~s  wî        ppq ~ ïsq ~ z   uq ~ }   sq ~ t tipoVisualizacaosq ~ t R.equals(new Integer(1)) ? "Todos Produtos" :"Somente Produtos com estoque mÃ­nimo"t java.lang.Stringppppppppppsq ~ º  wî          1   v   pq ~ q ~hppppppq ~ Îppppq ~ Ñ  wîppppppppppppppppppsq ~ Ùpsq ~ Ý  wîppppq ~Kq ~Kq ~Jpsq ~ ä  wîppppq ~Kq ~Kpsq ~ Þ  wîppppq ~Kq ~Kpsq ~ ç  wîppppq ~Kq ~Kpsq ~ é  wîppppq ~Kq ~Kppppppppppppppppq ~s  wî        ppq ~ ïsq ~ z   uq ~ }   sq ~ t 
nomeCategoriasq ~ t  == null ? "Todos" :  sq ~ t 
nomeCategoriat java.lang.Stringppppppppppsq ~Y  wî           u      ¡pq ~ q ~hppppppq ~ Îppppq ~ Ñ  wîpppppppppq ~ppppppppsq ~ Ùpsq ~ Ý  wîppppq ~[q ~[q ~Zpsq ~ ä  wîppppq ~[q ~[psq ~ Þ  wîppppq ~[q ~[psq ~ ç  wîppppq ~[q ~[psq ~ é  wîppppq ~[q ~[ppppppppppppppppq ~st Status do Produto:sq ~ º  wî          1   v   ¡pq ~ q ~hppppppq ~ Îppppq ~ Ñ  wîppppppppppppppppppsq ~ Ùpsq ~ Ý  wîppppq ~cq ~cq ~bpsq ~ ä  wîppppq ~cq ~cpsq ~ Þ  wîppppq ~cq ~cpsq ~ ç  wîppppq ~cq ~cpsq ~ é  wîppppq ~cq ~cppppppppppppppppq ~s  wî        ppq ~ ïsq ~ z   uq ~ }   sq ~ t 
statusProdutosq ~ t  == null ? "Todos" :  sq ~ t 
statusProdutot java.lang.Stringppppppppppsq ~ º  wî          1   v   µpq ~ q ~hppppppq ~ Îppppq ~ Ñ  wîppppppppppppppppppsq ~ Ùpsq ~ Ý  wîppppq ~sq ~sq ~rpsq ~ ä  wîppppq ~sq ~spsq ~ Þ  wîppppq ~sq ~spsq ~ ç  wîppppq ~sq ~spsq ~ é  wîppppq ~sq ~sppppppppppppppppq ~s  wî        ppq ~ ïsq ~ z   uq ~ }   sq ~ t 	ordenacaot java.lang.Stringppppppppppsq ~Y  wî           u      µpq ~ q ~hppppppq ~ Îppppq ~ Ñ  wîpppppppppq ~ppppppppsq ~ Ùpsq ~ Ý  wîppppq ~q ~q ~~psq ~ ä  wîppppq ~q ~psq ~ Þ  wîppppq ~q ~psq ~ ç  wîppppq ~q ~psq ~ é  wîppppq ~q ~ppppppppppppppppq ~st 
Ordenado por:sr ,net.sf.jasperreports.engine.base.JRBaseImage      'Ø *I PSEUDO_SERIAL_VERSION_UIDI 
bookmarkLevelB evaluationTimeB hyperlinkTargetB 
hyperlinkTypeZ isLazyB onErrorTypeL anchorNameExpressionq ~ L borderq ~ L borderColorq ~ ¿L bottomBorderq ~ L bottomBorderColorq ~ ¿L 
bottomPaddingq ~ ÀL evaluationGroupq ~ pL evaluationTimeValueq ~ »L 
expressionq ~ L horizontalAlignmentq ~ L horizontalAlignmentValueq ~ ÁL hyperlinkAnchorExpressionq ~ L hyperlinkPageExpressionq ~ [ hyperlinkParametersq ~ ¼L hyperlinkReferenceExpressionq ~ L hyperlinkTooltipExpressionq ~ L isUsingCacheq ~ ½L 
leftBorderq ~ L leftBorderColorq ~ ¿L leftPaddingq ~ ÀL lineBoxq ~ ÂL 
linkTargetq ~ L linkTypeq ~ L onErrorTypeValuet 2Lnet/sf/jasperreports/engine/type/OnErrorTypeEnum;L paddingq ~ ÀL rightBorderq ~ L rightBorderColorq ~ ¿L rightPaddingq ~ ÀL 
scaleImageq ~ L scaleImageValuet 1Lnet/sf/jasperreports/engine/type/ScaleImageEnum;L 	topBorderq ~ L topBorderColorq ~ ¿L 
topPaddingq ~ ÀL verticalAlignmentq ~ L verticalAlignmentValueq ~ Åxq ~¦  wî   0       y       pq ~ q ~hsq ~Õ    ÿÿÿÿpppt image-1pppp~q ~ Ít FLOATppppq ~k  wîppsq ~ ß  wîsq ~Õ    ÿÿÿÿppppppq ~p  wî         ppppppp~q ~ ît PAGEsq ~ z   uq ~ }   sq ~ t logoPadraoRelatoriot java.io.InputStreamppppppppq ~pppsq ~ Ùpsq ~ Ý  wîppppq ~q ~q ~psq ~ ä  wîppppq ~q ~psq ~ Þ  wîppppq ~q ~psq ~ ç  wîppppq ~q ~psq ~ é  wîppppq ~q ~pp~r 0net.sf.jasperreports.engine.type.OnErrorTypeEnum          xq ~ t BLANKpppppppppppsq ~Y  wî          )      @pq ~ q ~hppppppq ~ Îppppq ~ Ñ  wîppppppsq ~ Ó   pq ~\q ~ppppppppsq ~ Ùpsq ~ Ý  wîppppq ~¢q ~¢q ~ psq ~ ä  wîppppq ~¢q ~¢psq ~ Þ  wîppppq ~¢q ~¢psq ~ ç  wîppppq ~¢q ~¢psq ~ é  wîppppq ~¢q ~¢ppppppppppppppppq ~ ìt "RelatÃ³rio de PosiÃ§Ã£o de Estoquesq ~Y  wî           u      xpq ~ q ~hppppppq ~ Îppppq ~ Ñ  wîpppppppppq ~ppppppppsq ~ Ùpsq ~ Ý  wîppppq ~ªq ~ªq ~©psq ~ ä  wîppppq ~ªq ~ªpsq ~ Þ  wîppppq ~ªq ~ªpsq ~ ç  wîppppq ~ªq ~ªpsq ~ é  wîppppq ~ªq ~ªppppppppppppppppq ~st PosiÃ§Ã£o do Estoque:sq ~Y  wî           u      pq ~ q ~hppppppq ~ Îppppq ~ Ñ  wîpppppppppq ~ppppppppsq ~ Ùpsq ~ Ý  wîppppq ~²q ~²q ~±psq ~ ä  wîppppq ~²q ~²psq ~ Þ  wîppppq ~²q ~²psq ~ ç  wîppppq ~²q ~²psq ~ é  wîppppq ~²q ~²ppppppppppppppppq ~st Categoria Produto:sq ~¤  wî          +       ópq ~ q ~hppppppq ~ Îppppq ~ Ñ  wîppsq ~ ß  wîppppq ~¹p  wî q ~¬sq ~ º  wî           a  Ê   "sq ~Õ    ÿÿÿÿpppq ~ q ~hpt 	dataRel-1p~q ~Ît TRANSPARENTppq ~ Îppppq ~ Ñ  wîpppppt Arialq ~òpq ~ ×q ~q ~¯pq ~¯pppppsq ~ Ùsq ~ Ó    sq ~ Ý  wîsq ~Õ    ÿÿÿÿppppq ~Úsq ~Ü?   q ~Áq ~Áq ~»psq ~ ä  wîsq ~Õ    ÿÿÿÿppppq ~Úsq ~Ü?   q ~Áq ~Ápsq ~ Þ  wîsq ~Õ    ÿÿÿÿppppppq ~Áq ~Ápsq ~ ç  wîsq ~Õ    ÿÿÿÿppppq ~Úsq ~Ü?   q ~Áq ~Ápsq ~ é  wîsq ~Õ    ÿÿÿÿppppq ~Úsq ~Ü?   q ~Áq ~Ápppppt Helvetica-Boldppppppppppq ~ ì  wî        ppq ~ ïsq ~ z   uq ~ }   sq ~ t 
new Date()t java.util.Dateppppppq ~¯ppt dd/MM/yyyy HH.mm.sssq ~Y  wî           u      Épq ~ q ~hppppppq ~ Îppppq ~ Ñ  wîpppppppppq ~ppppppppsq ~ Ùpsq ~ Ý  wîppppq ~Ùq ~Ùq ~Øpsq ~ ä  wîppppq ~Ùq ~Ùpsq ~ Þ  wîppppq ~Ùq ~Ùpsq ~ ç  wîppppq ~Ùq ~Ùpsq ~ é  wîppppq ~Ùq ~Ùppppppppppppppppq ~st Valor Impresso:sq ~ º  wî          1   v   Épq ~ q ~hppppppq ~ Îppppq ~ Ñ  wîppppppppppppppppppsq ~ Ùpsq ~ Ý  wîppppq ~áq ~áq ~àpsq ~ ä  wîppppq ~áq ~ápsq ~ Þ  wîppppq ~áq ~ápsq ~ ç  wîppppq ~áq ~ápsq ~ é  wîppppq ~áq ~áppppppppppppppppq ~s  wî        ppq ~ ïsq ~ z   uq ~ }   sq ~ t 
valorImpressot java.lang.Stringppppppppppsq ~Y  wî           (  ü   ßpq ~ q ~hppppppq ~ Îppppq ~ Ñ  wîppppppq ~ypq ~ ×q ~ppppppppsq ~ Ùpsq ~ Ý  wîppppq ~íq ~íq ~ìpsq ~ ä  wîppppq ~íq ~ípsq ~ Þ  wîppppq ~íq ~ípsq ~ ç  wîppppq ~íq ~ípsq ~ é  wîppppq ~íq ~íppppppppppppppppq ~st Pontosxp  wî   ôppp~r /net.sf.jasperreports.engine.type.PrintOrderEnum          xq ~ t VERTICALpsq ~ sq ~    w   sr 0net.sf.jasperreports.engine.base.JRBaseSubreport      'Ø L connectionExpressionq ~ L dataSourceExpressionq ~ L 
expressionq ~ L isUsingCacheq ~ ½[ 
parameterst 3[Lnet/sf/jasperreports/engine/JRSubreportParameter;L parametersMapExpressionq ~ [ returnValuest 5[Lnet/sf/jasperreports/engine/JRSubreportReturnValue;L runToBottomq ~ ½xq ~ Æ  wî   6       (       pq ~ q ~÷ppppppq ~ Îppppq ~ Ñpsq ~ z   $uq ~ }   sq ~ t 
totalizadoresq ~ Fpsq ~ z   %uq ~ }   sq ~ t 
SUBREPORT_DIRsq ~ t 2 + "RelatorioEstoqueProduto_subTotalizador.jasper"t java.lang.Stringppur 3[Lnet.sf.jasperreports.engine.JRSubreportParameter;[£À¾B  xp   sr 9net.sf.jasperreports.engine.base.JRBaseSubreportParameter      'Ø  xr 7net.sf.jasperreports.engine.base.JRBaseDatasetParameter      'Ø L 
expressionq ~ L nameq ~ xpsq ~ z   "uq ~ }   sq ~ t totalEstoqueq ~pt totalestoquesq ~
sq ~ z   #uq ~ }   sq ~ t valorTotalGeralq ~pt 
totalgeralpppxp  wî   bppq ~ pp~r 3net.sf.jasperreports.engine.type.WhenNoDataTypeEnum          xq ~ t NO_DATA_SECTIONsr 6net.sf.jasperreports.engine.design.JRReportCompileData      'Ø L crosstabCompileDataq ~ 4L datasetCompileDataq ~ 4L mainDatasetCompileDataq ~ xpsq ~?@     w       xsq ~?@     w      q ~ -ur [B¬óøTà  xp  Êþº¾   .  4RelatorioEstoqueProduto_dataset1_1580836178142_30999  ,net/sf/jasperreports/engine/fill/JREvaluator  parameter_REPORT_LOCALE 2Lnet/sf/jasperreports/engine/fill/JRFillParameter; parameter_JASPER_REPORT parameter_REPORT_TIME_ZONE parameter_REPORT_FILE_RESOLVER parameter_REPORT_SCRIPTLET parameter_REPORT_PARAMETERS_MAP parameter_REPORT_CONNECTION parameter_REPORT_CLASS_LOADER parameter_REPORT_DATA_SOURCE $parameter_REPORT_URL_HANDLER_FACTORY parameter_REPORT_FORMAT_FACTORY parameter_REPORT_MAX_COUNT parameter_REPORT_TEMPLATES  parameter_REPORT_RESOURCE_BUNDLE variable_PAGE_NUMBER 1Lnet/sf/jasperreports/engine/fill/JRFillVariable; variable_COLUMN_NUMBER variable_REPORT_COUNT variable_PAGE_COUNT variable_COLUMN_COUNT <init> ()V Code  
    	    	  !  	  # 	 	  % 
 	  '  	  )  	  + 
 	  -  	  /  	  1  	  3  	  5  	  7  	  9  	  ;  	  =  	  ?  	  A  	  C LineNumberTable customizedInit 0(Ljava/util/Map;Ljava/util/Map;Ljava/util/Map;)V 
initParams (Ljava/util/Map;)V H I
  J 
initFields L I
  M initVars O I
  P 
REPORT_LOCALE R 
java/util/Map T get &(Ljava/lang/Object;)Ljava/lang/Object; V W U X 0net/sf/jasperreports/engine/fill/JRFillParameter Z 
JASPER_REPORT \ REPORT_TIME_ZONE ^ REPORT_FILE_RESOLVER ` REPORT_SCRIPTLET b REPORT_PARAMETERS_MAP d REPORT_CONNECTION f REPORT_CLASS_LOADER h REPORT_DATA_SOURCE j REPORT_URL_HANDLER_FACTORY l REPORT_FORMAT_FACTORY n REPORT_MAX_COUNT p REPORT_TEMPLATES r REPORT_RESOURCE_BUNDLE t PAGE_NUMBER v /net/sf/jasperreports/engine/fill/JRFillVariable x 
COLUMN_NUMBER z REPORT_COUNT | 
PAGE_COUNT ~ COLUMN_COUNT  evaluate (I)Ljava/lang/Object; 
Exceptions java/lang/Throwable  java/lang/Integer  (I)V  
   evaluateOld evaluateEstimated 
SourceFile !                      	     
               
                                                                   Ì     d*· *µ  *µ "*µ $*µ &*µ (*µ **µ ,*µ .*µ 0*µ 2*µ 4*µ 6*µ 8*µ :*µ <*µ >*µ @*µ B*µ D±    E   V       	          "  '   , ! 1 " 6 # ; $ @ % E & J ' O ( T ) Y * ^ + c   F G     4     *+· K*,· N*-· Q±    E       7  8 
 9  :  H I    M     ý*+S¹ Y À [À [µ  *+]¹ Y À [À [µ "*+_¹ Y À [À [µ $*+a¹ Y À [À [µ &*+c¹ Y À [À [µ (*+e¹ Y À [À [µ **+g¹ Y À [À [µ ,*+i¹ Y À [À [µ .*+k¹ Y À [À [µ 0*+m¹ Y À [À [µ 2*+o¹ Y À [À [µ 4*+q¹ Y À [À [µ 6*+s¹ Y À [À [µ 8*+u¹ Y À [À [µ :±    E   >    B  C $ D 6 E H F Z G l H ~ I  J ¢ K ´ L Æ M Ø N ê O ü P  L I           ±    E       X  O I          [*+w¹ Y À yÀ yµ <*+{¹ Y À yÀ yµ >*+}¹ Y À yÀ yµ @*+¹ Y À yÀ yµ B*+¹ Y À yÀ yµ D±    E       `  a $ b 6 c H d Z e              ë     Mª             -   9   E   Q   ]   i   u   » Y· M§ T» Y· M§ H» Y· M§ <» Y· M§ 0» Y· M§ $» Y· M§ » Y· M§ » Y· M,°    E   J    m  o 0 s 9 t < x E y H } Q ~ T  ]  `  i  l  u  x                     ë     Mª             -   9   E   Q   ]   i   u   » Y· M§ T» Y· M§ H» Y· M§ <» Y· M§ 0» Y· M§ $» Y· M§ » Y· M§ » Y· M,°    E   J    §  © 0 ­ 9 ® < ² E ³ H · Q ¸ T ¼ ] ½ ` Á i Â l Æ u Ç x Ë  Ì  Ð  Ø              ë     Mª             -   9   E   Q   ]   i   u   » Y· M§ T» Y· M§ H» Y· M§ <» Y· M§ 0» Y· M§ $» Y· M§ » Y· M§ » Y· M,°    E   J    á  ã 0 ç 9 è < ì E í H ñ Q ò T ö ] ÷ ` û i ü l  u x  
       xuq ~  (-Êþº¾   .m +RelatorioEstoqueProduto_1580836178142_30999  ,net/sf/jasperreports/engine/fill/JREvaluator  parameter_JASPER_REPORT 2Lnet/sf/jasperreports/engine/fill/JRFillParameter; parameter_ordenacao parameter_REPORT_TIME_ZONE parameter_REPORT_FILE_RESOLVER parameter_REPORT_PARAMETERS_MAP parameter_REPORT_CLASS_LOADER $parameter_REPORT_URL_HANDLER_FACTORY parameter_REPORT_DATA_SOURCE parameter_IS_IGNORE_PAGINATION parameter_XML_DATE_PATTERN parameter_XML_DATA_DOCUMENT parameter_REPORT_MAX_COUNT parameter_XML_LOCALE parameter_REPORT_TEMPLATES parameter_XML_NUMBER_PATTERN parameter_REPORT_LOCALE parameter_REPORT_VIRTUALIZER parameter_logoPadraoRelatorio parameter_REPORT_SCRIPTLET parameter_nomeCategoria parameter_totalizadores parameter_REPORT_CONNECTION parameter_tipoVisualizacao parameter_valorImpresso parameter_SUBREPORT_DIR parameter_REPORT_FORMAT_FACTORY parameter_statusProduto  parameter_REPORT_RESOURCE_BUNDLE parameter_XML_TIME_ZONE *field_produto46categoriaProduto46descricao .Lnet/sf/jasperreports/engine/fill/JRFillField; field_pontos field_valorUnitario field_valorTotalEstoque field_produto46codigo field_estoqueMinimo field_produto46descricao field_empresa46cidade46nome 
field_estoque field_empresa46endereco field_empresa46nome variable_PAGE_NUMBER 1Lnet/sf/jasperreports/engine/fill/JRFillVariable; variable_COLUMN_NUMBER variable_REPORT_COUNT variable_PAGE_COUNT variable_COLUMN_COUNT variable_categoria_COUNT variable_valorTotalGeral variable_totalEstoque <init> ()V Code 8 9
  ;  	  =  	  ?  	  A 	 	  C 
 	  E  	  G  	  I 
 	  K  	  M  	  O  	  Q  	  S  	  U  	  W  	  Y  	  [  	  ]  	  _  	  a  	  c  	  e  	  g  	  i  	  k  	  m  	  o   	  q ! 	  s " 	  u # $	  w % $	  y & $	  { ' $	  } ( $	   ) $	   * $	   + $	   , $	   - $	   . $	   / 0	   1 0	   2 0	   3 0	   4 0	   5 0	   6 0	   7 0	   LineNumberTable customizedInit 0(Ljava/util/Map;Ljava/util/Map;Ljava/util/Map;)V 
initParams (Ljava/util/Map;)V   ¡
  ¢ 
initFields ¤ ¡
  ¥ initVars § ¡
  ¨ 
JASPER_REPORT ª 
java/util/Map ¬ get &(Ljava/lang/Object;)Ljava/lang/Object; ® ¯ ­ ° 0net/sf/jasperreports/engine/fill/JRFillParameter ² 	ordenacao ´ REPORT_TIME_ZONE ¶ REPORT_FILE_RESOLVER ¸ REPORT_PARAMETERS_MAP º REPORT_CLASS_LOADER ¼ REPORT_URL_HANDLER_FACTORY ¾ REPORT_DATA_SOURCE À IS_IGNORE_PAGINATION Â XML_DATE_PATTERN Ä XML_DATA_DOCUMENT Æ REPORT_MAX_COUNT È 
XML_LOCALE Ê REPORT_TEMPLATES Ì XML_NUMBER_PATTERN Î 
REPORT_LOCALE Ð REPORT_VIRTUALIZER Ò logoPadraoRelatorio Ô REPORT_SCRIPTLET Ö 
nomeCategoria Ø 
totalizadores Ú REPORT_CONNECTION Ü tipoVisualizacao Þ 
valorImpresso à 
SUBREPORT_DIR â REPORT_FORMAT_FACTORY ä 
statusProduto æ REPORT_RESOURCE_BUNDLE è 
XML_TIME_ZONE ê "produto.categoriaProduto.descricao ì ,net/sf/jasperreports/engine/fill/JRFillField î pontos ð 
valorUnitario ò valorTotalEstoque ô produto.codigo ö 
estoqueMinimo ø produto.descricao ú empresa.cidade.nome ü estoque þ empresa.endereco  empresa.nome PAGE_NUMBER /net/sf/jasperreports/engine/fill/JRFillVariable 
COLUMN_NUMBER REPORT_COUNT
 
PAGE_COUNT COLUMN_COUNT categoria_COUNT valorTotalGeral totalEstoque evaluate (I)Ljava/lang/Object; 
Exceptions java/lang/Throwable oC:\PactoJ\Desenvolvimento\Sistemas\ZillyonWeb\tronco-novo\src\main\resources\relatorio\designRelatorio\estoque\ java/lang/Integer (I)V 8
  getValue ()Ljava/lang/Object;"#
 ï$ java/lang/Double& java/lang/String( java/lang/StringBuffer* CATEGORIA: , (Ljava/lang/String;)V 8.
+/ append ,(Ljava/lang/String;)Ljava/lang/StringBuffer;12
+3 toString ()Ljava/lang/String;56
+7  9
$ ,(Ljava/lang/Object;)Ljava/lang/StringBuffer;1<
+= 	PÃ¡gina: ?  de A
 ³$ equals (Ljava/lang/Object;)ZDE
F Todos ProdutosH $Somente Produtos com estoque mÃ­nimoJ TodosL java/io/InputStreamN java/util/DateP
Q ; java/text/DecimalFormatS 0000U
T/ format &(Ljava/lang/Object;)Ljava/lang/String;XY
TZ (net/sf/jasperreports/engine/JRDataSource\ valueOf^Y
)_ -RelatorioEstoqueProduto_subTotalizador.jaspera evaluateOld getOldValued#
 ïe
e evaluateEstimated getEstimatedValuei#
j 
SourceFile !     0                 	     
               
                                                                                                     !     "     # $    % $    & $    ' $    ( $    ) $    * $    + $    , $    - $    . $    / 0    1 0    2 0    3 0    4 0    5 0    6 0    7 0     8 9  :  Ñ     õ*· <*µ >*µ @*µ B*µ D*µ F*µ H*µ J*µ L*µ N*µ P*µ R*µ T*µ V*µ X*µ Z*µ \*µ ^*µ `*µ b*µ d*µ f*µ h*µ j*µ l*µ n*µ p*µ r*µ t*µ v*µ x*µ z*µ |*µ ~*µ *µ *µ *µ *µ *µ *µ *µ *µ *µ *µ *µ *µ *µ *µ ±       Ê 2      	          "  '   , ! 1 " 6 # ; $ @ % E & J ' O ( T ) Y * ^ + c , h - m . r / w 0 | 1  2  3  4  5  6  7  8 ¤ 9 © : ® ; ³ < ¸ = ½ > Â ? Ç @ Ì A Ñ B Ö C Û D à E å F ê G ï H ô      :   4     *+· £*,· ¦*-· ©±           T  U 
 V  W    ¡  :      *+«¹ ± À ³À ³µ >*+µ¹ ± À ³À ³µ @*+·¹ ± À ³À ³µ B*+¹¹ ± À ³À ³µ D*+»¹ ± À ³À ³µ F*+½¹ ± À ³À ³µ H*+¿¹ ± À ³À ³µ J*+Á¹ ± À ³À ³µ L*+Ã¹ ± À ³À ³µ N*+Å¹ ± À ³À ³µ P*+Ç¹ ± À ³À ³µ R*+É¹ ± À ³À ³µ T*+Ë¹ ± À ³À ³µ V*+Í¹ ± À ³À ³µ X*+Ï¹ ± À ³À ³µ Z*+Ñ¹ ± À ³À ³µ \*+Ó¹ ± À ³À ³µ ^*+Õ¹ ± À ³À ³µ `*+×¹ ± À ³À ³µ b*+Ù¹ ± À ³À ³µ d*+Û¹ ± À ³À ³µ f*+Ý¹ ± À ³À ³µ h*+ß¹ ± À ³À ³µ j*+á¹ ± À ³À ³µ l*+ã¹ ± À ³À ³µ n*+å¹ ± À ³À ³µ p*+ç¹ ± À ³À ³µ r*+é¹ ± À ³À ³µ t*+ë¹ ± À ³À ³µ v±       z    _  ` $ a 6 b H c Z d l e ~ f  g ¢ h ´ i Æ j Ø k ê l ü m n  o2 pD qV rh sz t u v° wÂ xÔ yæ zø {
 |  ¤ ¡  :  
     É*+í¹ ± À ïÀ ïµ x*+ñ¹ ± À ïÀ ïµ z*+ó¹ ± À ïÀ ïµ |*+õ¹ ± À ïÀ ïµ ~*+÷¹ ± À ïÀ ïµ *+ù¹ ± À ïÀ ïµ *+û¹ ± À ïÀ ïµ *+ý¹ ± À ïÀ ïµ *+ÿ¹ ± À ïÀ ïµ *+¹ ± À ïÀ ïµ *+¹ ± À ïÀ ïµ ±       2       $  6  H  Z  l  ~    ¢  µ  È   § ¡  :   Ñ     *+¹ ± ÀÀµ *+	¹ ± ÀÀµ *+¹ ± ÀÀµ *+
¹ ± ÀÀµ *+¹ ± ÀÀµ *+¹ ± ÀÀµ *+¹ ± ÀÀµ *+¹ ± ÀÀµ ±       & 	      &  9  L  _  r            :  |    0Mª  +       %   ¥   ¬   ¸   Ä   Ð   Ü   è   ô         $  2  @  N  l  z      ´  Ø  ý    ?  M  [  f  t        ¬  º  Õ  ã  ñ  ÿ  
M§»Y·!M§v»Y·!M§j»Y·!M§^»Y·!M§R»Y·!M§F»Y·!M§:»Y·!M§.»Y·!M§"»Y·!M§»Y·!M§
*´ ~¶%À'M§ü*´ ¶%ÀM§î*´ x¶%À)M§à»+Y-·0*´ x¶%À)¶4¶8M§Â*´ ¶%À)M§´*´ ¶%À)M§¦*´ ¶%À)M§»+Y:·0*´ ¶;À¶>¶8M§z»+Y@·0*´ ¶;À¶>B¶4¶8M§V*´ j¶CÀ»Y·!¶G 	I§ KM§1*´ d¶CÀ)Ç 	M§ 
*´ d¶CÀ)M§*´ r¶CÀ)Ç 	M§ 
*´ r¶CÀ)M§ ï*´ @¶CÀ)M§ á*´ `¶CÀOM§ Ó»QY·RM§ È*´ l¶CÀ)M§ º*´ ¶%ÀM§ ¬*´ ¶%À)M§ *´ ¶%ÀM§ *´ |¶%À'M§ *´ ~¶%À'M§ t»TYV·W*´ ¶%À¶[M§ Y*´ z¶%ÀM§ K*´ ¶;ÀM§ =*´ ¶;À'M§ /*´ f¶CÀ]M§ !»+Y*´ n¶CÀ)¸`·0b¶4¶8M,°      : N   §  © ¨ ­ ¬ ® ¯ ² ¸ ³ » · Ä ¸ Ç ¼ Ð ½ Ó Á Ü Â ß Æ è Ç ë Ë ô Ì ÷ Ð  Ñ Õ Ö Ú Û ß$ à' ä2 å5 é@ êC îN ïQ ól ôo øz ù} ý þ´·Ø
Ûý !?B M!P%[&^*f+i/t0w459:>?¡C¬D¯HºI½MÕNØRãSæWñXô\ÿ]a
bf.n c      :  |    0Mª  +       %   ¥   ¬   ¸   Ä   Ð   Ü   è   ô         $  2  @  N  l  z      ´  Ø  ý    ?  M  [  f  t        ¬  º  Õ  ã  ñ  ÿ  
M§»Y·!M§v»Y·!M§j»Y·!M§^»Y·!M§R»Y·!M§F»Y·!M§:»Y·!M§.»Y·!M§"»Y·!M§»Y·!M§
*´ ~¶fÀ'M§ü*´ ¶fÀM§î*´ x¶fÀ)M§à»+Y-·0*´ x¶fÀ)¶4¶8M§Â*´ ¶fÀ)M§´*´ ¶fÀ)M§¦*´ ¶fÀ)M§»+Y:·0*´ ¶gÀ¶>¶8M§z»+Y@·0*´ ¶gÀ¶>B¶4¶8M§V*´ j¶CÀ»Y·!¶G 	I§ KM§1*´ d¶CÀ)Ç 	M§ 
*´ d¶CÀ)M§*´ r¶CÀ)Ç 	M§ 
*´ r¶CÀ)M§ ï*´ @¶CÀ)M§ á*´ `¶CÀOM§ Ó»QY·RM§ È*´ l¶CÀ)M§ º*´ ¶fÀM§ ¬*´ ¶fÀ)M§ *´ ¶fÀM§ *´ |¶fÀ'M§ *´ ~¶fÀ'M§ t»TYV·W*´ ¶fÀ¶[M§ Y*´ z¶fÀM§ K*´ ¶gÀM§ =*´ ¶gÀ'M§ /*´ f¶CÀ]M§ !»+Y*´ n¶CÀ)¸`·0b¶4¶8M,°      : N  w y ¨} ¬~ ¯ ¸ » Ä Ç Ð Ó Ü ß è ë ô ÷  ¡¥¦ª«¯$°'´2µ5¹@ºC¾N¿QÃlÄoÈzÉ}ÍÎÒÓ×´Ø·ÜØÝÛáýâ æç!ë?ìBðMñPõ[ö^úfûiÿt w	
¡¬¯º½ÕØ"ã#æ'ñ(ô,ÿ-1
26.> h      :  |    0Mª  +       %   ¥   ¬   ¸   Ä   Ð   Ü   è   ô         $  2  @  N  l  z      ´  Ø  ý    ?  M  [  f  t        ¬  º  Õ  ã  ñ  ÿ  
M§»Y·!M§v»Y·!M§j»Y·!M§^»Y·!M§R»Y·!M§F»Y·!M§:»Y·!M§.»Y·!M§"»Y·!M§»Y·!M§
*´ ~¶%À'M§ü*´ ¶%ÀM§î*´ x¶%À)M§à»+Y-·0*´ x¶%À)¶4¶8M§Â*´ ¶%À)M§´*´ ¶%À)M§¦*´ ¶%À)M§»+Y:·0*´ ¶kÀ¶>¶8M§z»+Y@·0*´ ¶kÀ¶>B¶4¶8M§V*´ j¶CÀ»Y·!¶G 	I§ KM§1*´ d¶CÀ)Ç 	M§ 
*´ d¶CÀ)M§*´ r¶CÀ)Ç 	M§ 
*´ r¶CÀ)M§ ï*´ @¶CÀ)M§ á*´ `¶CÀOM§ Ó»QY·RM§ È*´ l¶CÀ)M§ º*´ ¶%ÀM§ ¬*´ ¶%À)M§ *´ ¶%ÀM§ *´ |¶%À'M§ *´ ~¶%À'M§ t»TYV·W*´ ¶%À¶[M§ Y*´ z¶%ÀM§ K*´ ¶kÀM§ =*´ ¶kÀ'M§ /*´ f¶CÀ]M§ !»+Y*´ n¶CÀ)¸`·0b¶4¶8M,°      : N  G I ¨M ¬N ¯R ¸S »W ÄX Ç\ Ð] Óa Üb ßf èg ëk ôl ÷p quvz{$'25@CNQloz}¢£§´¨·¬Ø­Û±ý² ¶·!»?¼BÀMÁPÅ[Æ^ÊfËiÏtÐwÔÕÙÚÞß¡ã¬ä¯èºé½íÕîØòãóæ÷ñøôüÿý
. l    t _1580836178142_30999t 2net.sf.jasperreports.engine.design.JRJavacCompiler