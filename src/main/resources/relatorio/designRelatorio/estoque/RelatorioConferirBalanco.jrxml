<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="RelatorioConferirBalanco" pageWidth="595" pageHeight="842" columnWidth="555" leftMargin="20" rightMargin="20" topMargin="20" bottomMargin="20" isIgnorePagination="true">
	<property name="ireport.zoom" value="2.0"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="240"/>
	<parameter name="logoPadraoRelatorio" class="java.io.InputStream" isForPrompting="false"/>
	<field name="empresa.nome" class="java.lang.String"/>
	<field name="empresa.cidade.nome" class="java.lang.String"/>
	<field name="empresa.endereco" class="java.lang.String"/>
	<field name="descricao" class="java.lang.String"/>
	<field name="estoque" class="java.lang.Integer"/>
	<background>
		<band splitType="Stretch"/>
	</background>
	<title>
		<band height="180" splitType="Stretch">
			<image isUsingCache="true" onErrorType="Blank" evaluationTime="Page">
				<reportElement key="image-1" positionType="Float" stretchType="RelativeToBandHeight" x="0" y="0" width="121" height="48" isPrintWhenDetailOverflows="true" forecolor="#FFFFFF"/>
				<graphicElement>
					<pen lineColor="#FFFFFF"/>
				</graphicElement>
				<imageExpression class="java.io.InputStream"><![CDATA[$P{logoPadraoRelatorio}]]></imageExpression>
			</image>
			<staticText>
				<reportElement x="1" y="60" width="553" height="31"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="16" isBold="true"/>
				</textElement>
				<text><![CDATA[Formulário para Conferência de Estoque]]></text>
			</staticText>
			<staticText>
				<reportElement x="1" y="116" width="117" height="20"/>
				<textElement verticalAlignment="Bottom">
					<font isBold="true"/>
				</textElement>
				<text><![CDATA[Data da Conferência:]]></text>
			</staticText>
			<staticText>
				<reportElement x="1" y="137" width="117" height="20"/>
				<textElement verticalAlignment="Bottom">
					<font isBold="true"/>
				</textElement>
				<text><![CDATA[Responsável:]]></text>
			</staticText>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-208" x="123" y="2" width="113" height="14"/>
				<textElement>
					<font isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{empresa.nome}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-209" x="123" y="18" width="113" height="14"/>
				<textElement>
					<font isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{empresa.endereco}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-210" x="123" y="34" width="113" height="14"/>
				<textElement>
					<font isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{empresa.cidade.nome}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement key="staticText-14" mode="Opaque" x="292" y="1" width="262" height="23"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Top" lineSpacing="Single">
					<font fontName="Microsoft Sans Serif" size="9" isBold="true" isItalic="true" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica-BoldOblique"/>
				</textElement>
				<text><![CDATA[Zillyon - Sistema Administrativo para Academias Desenvolvido por PACTO Soluções Tecnológicas Ltda.]]></text>
			</staticText>
			<textField evaluationTime="Report" isBlankWhenNull="false">
				<reportElement key="textField-212" x="544" y="43" width="11" height="17"/>
				<box bottomPadding="2">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
				</box>
				<textElement>
					<font fontName="Arial" size="6" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[" " + $V{PAGE_NUMBER} + ""]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement key="staticText-15" mode="Opaque" x="458" y="32" width="96" height="12"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Top" lineSpacing="Single">
					<font fontName="Microsoft Sans Serif" size="6" isBold="true" isItalic="true" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica-BoldOblique"/>
				</textElement>
				<text><![CDATA[(0xx62) 3251-5820]]></text>
			</staticText>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-211" x="468" y="43" width="75" height="17"/>
				<box bottomPadding="2">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right">
					<font fontName="Arial" size="6" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA["Página: " + $V{PAGE_NUMBER} + " de "]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="117" y="116" width="151" height="20"/>
				<textElement verticalAlignment="Bottom"/>
				<text><![CDATA[______/______/_________]]></text>
			</staticText>
			<staticText>
				<reportElement x="117" y="137" width="215" height="20"/>
				<textElement verticalAlignment="Bottom"/>
				<text><![CDATA[__________________________________________]]></text>
			</staticText>
		</band>
	</title>
	<pageHeader>
		<band height="22">
			<staticText>
				<reportElement x="1" y="0" width="100" height="20"/>
				<textElement verticalAlignment="Bottom">
					<font isBold="true"/>
				</textElement>
				<text><![CDATA[Produto]]></text>
			</staticText>
			<staticText>
				<reportElement x="286" y="0" width="136" height="20"/>
				<textElement textAlignment="Right" verticalAlignment="Bottom">
					<font isBold="true"/>
				</textElement>
				<text><![CDATA[Qtde. Estoque Anterior]]></text>
			</staticText>
			<staticText>
				<reportElement x="447" y="0" width="108" height="20"/>
				<textElement textAlignment="Right" verticalAlignment="Bottom">
					<font isBold="true"/>
				</textElement>
				<text><![CDATA[Qtde. Balanço]]></text>
			</staticText>
			<line>
				<reportElement x="1" y="20" width="555" height="1"/>
			</line>
		</band>
	</pageHeader>
	<detail>
		<band height="21" splitType="Stretch">
			<textField>
				<reportElement x="0" y="0" width="215" height="20"/>
				<textElement verticalAlignment="Bottom"/>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{descricao}]]></textFieldExpression>
			</textField>
			<textField pattern="">
				<reportElement x="285" y="0" width="136" height="20"/>
				<textElement textAlignment="Right" verticalAlignment="Bottom"/>
				<textFieldExpression class="java.lang.Integer"><![CDATA[$F{estoque}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="447" y="0" width="107" height="20"/>
				<textElement textAlignment="Right" verticalAlignment="Bottom"/>
				<text><![CDATA[------------------------]]></text>
			</staticText>
		</band>
	</detail>
	<summary>
		<band height="22" splitType="Stretch"/>
	</summary>
</jasperReport>
