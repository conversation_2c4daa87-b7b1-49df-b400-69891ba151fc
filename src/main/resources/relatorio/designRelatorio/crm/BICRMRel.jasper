¬í sr (net.sf.jasperreports.engine.JasperReport      'Ø L compileDatat Ljava/io/Serializable;L compileNameSuffixt Ljava/lang/String;L 
compilerClassq ~ xr -net.sf.jasperreports.engine.base.JRBaseReport      'Ø ,I PSEUDO_SERIAL_VERSION_UIDI bottomMarginI columnCountI 
columnSpacingI columnWidthZ ignorePaginationZ isFloatColumnFooterZ isSummaryNewPageZ  isSummaryWithPageHeaderAndFooterZ isTitleNewPageI 
leftMarginB orientationI 
pageHeightI 	pageWidthB 
printOrderI rightMarginI 	topMarginB whenNoDataTypeL 
backgroundt $Lnet/sf/jasperreports/engine/JRBand;L columnDirectiont 3Lnet/sf/jasperreports/engine/type/RunDirectionEnum;L columnFooterq ~ L columnHeaderq ~ [ datasetst ([Lnet/sf/jasperreports/engine/JRDataset;L defaultFontt *Lnet/sf/jasperreports/engine/JRReportFont;L defaultStylet %Lnet/sf/jasperreports/engine/JRStyle;L detailq ~ L 
detailSectiont 'Lnet/sf/jasperreports/engine/JRSection;[ fontst +[Lnet/sf/jasperreports/engine/JRReportFont;L formatFactoryClassq ~ L 
importsSett Ljava/util/Set;L languageq ~ L lastPageFooterq ~ L mainDatasett 'Lnet/sf/jasperreports/engine/JRDataset;L nameq ~ L noDataq ~ L orientationValuet 2Lnet/sf/jasperreports/engine/type/OrientationEnum;L 
pageFooterq ~ L 
pageHeaderq ~ L printOrderValuet 1Lnet/sf/jasperreports/engine/type/PrintOrderEnum;[ stylest &[Lnet/sf/jasperreports/engine/JRStyle;L summaryq ~ [ 	templatest /[Lnet/sf/jasperreports/engine/JRReportTemplate;L titleq ~ L whenNoDataTypeValuet 5Lnet/sf/jasperreports/engine/type/WhenNoDataTypeEnum;xp  wñ            7           J  S        sr +net.sf.jasperreports.engine.base.JRBaseBand      'Ø I PSEUDO_SERIAL_VERSION_UIDI heightZ isSplitAllowedL printWhenExpressiont *Lnet/sf/jasperreports/engine/JRExpression;L 	splitTypet Ljava/lang/Byte;L splitTypeValuet 0Lnet/sf/jasperreports/engine/type/SplitTypeEnum;xr 3net.sf.jasperreports.engine.base.JRBaseElementGroup      'Ø L childrent Ljava/util/List;L elementGroupt ,Lnet/sf/jasperreports/engine/JRElementGroup;xpsr java.util.ArrayListxÒÇa I sizexp    w    xp  wñ    pp~r .net.sf.jasperreports.engine.type.SplitTypeEnum          xr java.lang.Enum          xpt STRETCH~r 1net.sf.jasperreports.engine.type.RunDirectionEnum          xq ~ t LTRpsq ~ sq ~     w    xp  wñ    ppq ~ ur ([Lnet.sf.jasperreports.engine.JRDataset;L6Í¬D  xp   sr .net.sf.jasperreports.engine.base.JRBaseDataset      'Ø I PSEUDO_SERIAL_VERSION_UIDZ isMainB whenResourceMissingType[ fieldst &[Lnet/sf/jasperreports/engine/JRField;L filterExpressionq ~ [ groupst &[Lnet/sf/jasperreports/engine/JRGroup;L nameq ~ [ 
parameterst *[Lnet/sf/jasperreports/engine/JRParameter;L 
propertiesMapt -Lnet/sf/jasperreports/engine/JRPropertiesMap;L queryt %Lnet/sf/jasperreports/engine/JRQuery;L resourceBundleq ~ L scriptletClassq ~ [ 
scriptletst *[Lnet/sf/jasperreports/engine/JRScriptlet;[ 
sortFieldst *[Lnet/sf/jasperreports/engine/JRSortField;[ 	variablest )[Lnet/sf/jasperreports/engine/JRVariable;L whenResourceMissingTypeValuet >Lnet/sf/jasperreports/engine/type/WhenResourceMissingTypeEnum;xp  wñ  pppt Testeur *[Lnet.sf.jasperreports.engine.JRParameter;" *Ã`!  xp   sr 0net.sf.jasperreports.engine.base.JRBaseParameter      'Ø 	Z isForPromptingZ isSystemDefinedL defaultValueExpressionq ~ L descriptionq ~ L nameq ~ L nestedTypeNameq ~ L 
propertiesMapq ~ ,L valueClassNameq ~ L valueClassRealNameq ~ xpppt REPORT_PARAMETERS_MAPpsr +net.sf.jasperreports.engine.JRPropertiesMap      'Ø L baseq ~ ,L propertiesListq ~ L 
propertiesMapt Ljava/util/Map;xppppt 
java.util.Mappsq ~ 6ppt 
JASPER_REPORTpsq ~ 9pppt (net.sf.jasperreports.engine.JasperReportpsq ~ 6ppt REPORT_CONNECTIONpsq ~ 9pppt java.sql.Connectionpsq ~ 6ppt REPORT_MAX_COUNTpsq ~ 9pppt java.lang.Integerpsq ~ 6ppt REPORT_DATA_SOURCEpsq ~ 9pppt (net.sf.jasperreports.engine.JRDataSourcepsq ~ 6ppt REPORT_SCRIPTLETpsq ~ 9pppt /net.sf.jasperreports.engine.JRAbstractScriptletpsq ~ 6ppt 
REPORT_LOCALEpsq ~ 9pppt java.util.Localepsq ~ 6ppt REPORT_RESOURCE_BUNDLEpsq ~ 9pppt java.util.ResourceBundlepsq ~ 6ppt REPORT_TIME_ZONEpsq ~ 9pppt java.util.TimeZonepsq ~ 6ppt REPORT_FORMAT_FACTORYpsq ~ 9pppt .net.sf.jasperreports.engine.util.FormatFactorypsq ~ 6ppt REPORT_CLASS_LOADERpsq ~ 9pppt java.lang.ClassLoaderpsq ~ 6ppt REPORT_URL_HANDLER_FACTORYpsq ~ 9pppt  java.net.URLStreamHandlerFactorypsq ~ 6ppt REPORT_FILE_RESOLVERpsq ~ 9pppt -net.sf.jasperreports.engine.util.FileResolverpsq ~ 6ppt REPORT_TEMPLATESpsq ~ 9pppt java.util.Collectionpsq ~ 6ppt SORT_FIELDSpsq ~ 9pppt java.util.Listpsq ~ 9ppppppppur )[Lnet.sf.jasperreports.engine.JRVariable;bæ|,·D  xp   sr /net.sf.jasperreports.engine.base.JRBaseVariable      'Ø I PSEUDO_SERIAL_VERSION_UIDB calculationB 
incrementTypeZ isSystemDefinedB 	resetTypeL calculationValuet 2Lnet/sf/jasperreports/engine/type/CalculationEnum;L 
expressionq ~ L incrementGroupt %Lnet/sf/jasperreports/engine/JRGroup;L incrementTypeValuet 4Lnet/sf/jasperreports/engine/type/IncrementTypeEnum;L incrementerFactoryClassNameq ~ L incrementerFactoryClassRealNameq ~ L initialValueExpressionq ~ L nameq ~ L 
resetGroupq ~ zL resetTypeValuet 0Lnet/sf/jasperreports/engine/type/ResetTypeEnum;L valueClassNameq ~ L valueClassRealNameq ~ xp  wî   ~r 0net.sf.jasperreports.engine.type.CalculationEnum          xq ~ t SYSTEMpp~r 2net.sf.jasperreports.engine.type.IncrementTypeEnum          xq ~ t NONEppsr 1net.sf.jasperreports.engine.base.JRBaseExpression      'Ø I id[ chunkst 0[Lnet/sf/jasperreports/engine/JRExpressionChunk;L valueClassNameq ~ L valueClassRealNameq ~ xp    ur 0[Lnet.sf.jasperreports.engine.JRExpressionChunk;mYÏÞiK£U  xp   sr 6net.sf.jasperreports.engine.base.JRBaseExpressionChunk      'Ø B typeL textq ~ xpt new java.lang.Integer(1)q ~ Hpt PAGE_NUMBERp~r .net.sf.jasperreports.engine.type.ResetTypeEnum          xq ~ t REPORTq ~ Hpsq ~ x  wî   q ~ ppq ~ ppsq ~    uq ~    sq ~ t new java.lang.Integer(1)q ~ Hpt 
COLUMN_NUMBERp~q ~ t PAGEq ~ Hpsq ~ x  wî   ~q ~ ~t COUNTsq ~    uq ~    sq ~ t new java.lang.Integer(1)q ~ Hppq ~ ppsq ~    uq ~    sq ~ t new java.lang.Integer(0)q ~ Hpt REPORT_COUNTpq ~ q ~ Hpsq ~ x  wî   q ~ sq ~    uq ~    sq ~ t new java.lang.Integer(1)q ~ Hppq ~ ppsq ~    uq ~    sq ~ t new java.lang.Integer(0)q ~ Hpt 
PAGE_COUNTpq ~ q ~ Hpsq ~ x  wî   q ~ sq ~    uq ~    sq ~ t new java.lang.Integer(1)q ~ Hppq ~ ppsq ~    uq ~    sq ~ t new java.lang.Integer(0)q ~ Hpt COLUMN_COUNTp~q ~ t COLUMNq ~ Hp~r <net.sf.jasperreports.engine.type.WhenResourceMissingTypeEnum          xq ~ t NULLpppsr .net.sf.jasperreports.engine.base.JRBaseSection      'Ø [ bandst %[Lnet/sf/jasperreports/engine/JRBand;xpur %[Lnet.sf.jasperreports.engine.JRBand;Ý~ìÊ5  xp   sq ~ sq ~    w   sr 0net.sf.jasperreports.engine.base.JRBaseRectangle      'Ø L radiust Ljava/lang/Integer;xr 5net.sf.jasperreports.engine.base.JRBaseGraphicElement      'Ø I PSEUDO_SERIAL_VERSION_UIDL fillq ~ L 	fillValuet +Lnet/sf/jasperreports/engine/type/FillEnum;L linePent #Lnet/sf/jasperreports/engine/JRPen;L penq ~ xr .net.sf.jasperreports.engine.base.JRBaseElement      'Ø I PSEUDO_SERIAL_VERSION_UIDI heightZ isPrintInFirstWholeBandZ isPrintRepeatedValuesZ isPrintWhenDetailOverflowsZ isRemoveLineWhenBlankB positionTypeB stretchTypeI widthI xI yL 	backcolort Ljava/awt/Color;L defaultStyleProvidert 4Lnet/sf/jasperreports/engine/JRDefaultStyleProvider;L elementGroupq ~ L 	forecolorq ~ ÊL keyq ~ L modeq ~ L 	modeValuet +Lnet/sf/jasperreports/engine/type/ModeEnum;L parentStyleq ~ L parentStyleNameReferenceq ~ L positionTypeValuet 3Lnet/sf/jasperreports/engine/type/PositionTypeEnum;L printWhenExpressionq ~ L printWhenGroupChangesq ~ zL 
propertiesMapq ~ ,[ propertyExpressionst 3[Lnet/sf/jasperreports/engine/JRPropertyExpression;L stretchTypeValuet 2Lnet/sf/jasperreports/engine/type/StretchTypeEnum;xp  wñ          7        sr java.awt.Color¥3u F falphaI valueL cst Ljava/awt/color/ColorSpace;[ 	frgbvaluet [F[ fvalueq ~ Óxp    ÿ´ÍÍpppq ~ q ~ Âpppppp~r 1net.sf.jasperreports.engine.type.PositionTypeEnum          xq ~ t FIX_RELATIVE_TO_TOPsq ~    uq ~    sq ~ t 
new Boolean((sq ~ t COLUMN_COUNTsq ~ t .intValue()%2)==0)t java.lang.Booleanpppp~r 0net.sf.jasperreports.engine.type.StretchTypeEnum          xq ~ t 
NO_STRETCH  wîppsr *net.sf.jasperreports.engine.base.JRBasePen      'Ø I PSEUDO_SERIAL_VERSION_UIDL 	lineColorq ~ ÊL 	lineStyleq ~ L lineStyleValuet 0Lnet/sf/jasperreports/engine/type/LineStyleEnum;L 	lineWidtht Ljava/lang/Float;L penContainert ,Lnet/sf/jasperreports/engine/JRPenContainer;xp  wñsq ~ Ñ    ÿÿÿÿpppppsr java.lang.FloatÚíÉ¢Û<ðì F valuexr java.lang.Number¬à  xp    q ~ Ðppsr 0net.sf.jasperreports.engine.base.JRBaseTextField      'Ø I PSEUDO_SERIAL_VERSION_UIDI 
bookmarkLevelB evaluationTimeB hyperlinkTargetB 
hyperlinkTypeZ isStretchWithOverflowL anchorNameExpressionq ~ L evaluationGroupq ~ zL evaluationTimeValuet 5Lnet/sf/jasperreports/engine/type/EvaluationTimeEnum;L 
expressionq ~ L hyperlinkAnchorExpressionq ~ L hyperlinkPageExpressionq ~ [ hyperlinkParameterst 3[Lnet/sf/jasperreports/engine/JRHyperlinkParameter;L hyperlinkReferenceExpressionq ~ L hyperlinkTooltipExpressionq ~ L isBlankWhenNullt Ljava/lang/Boolean;L 
linkTargetq ~ L linkTypeq ~ L patternq ~ xr 2net.sf.jasperreports.engine.base.JRBaseTextElement      'Ø %I PSEUDO_SERIAL_VERSION_UIDL borderq ~ L borderColorq ~ ÊL bottomBorderq ~ L bottomBorderColorq ~ ÊL 
bottomPaddingq ~ ÅL fontNameq ~ L fontSizeq ~ ÅL horizontalAlignmentq ~ L horizontalAlignmentValuet 6Lnet/sf/jasperreports/engine/type/HorizontalAlignEnum;L isBoldq ~ ðL isItalicq ~ ðL 
isPdfEmbeddedq ~ ðL isStrikeThroughq ~ ðL isStyledTextq ~ ðL isUnderlineq ~ ðL 
leftBorderq ~ L leftBorderColorq ~ ÊL leftPaddingq ~ ÅL lineBoxt 'Lnet/sf/jasperreports/engine/JRLineBox;L lineSpacingq ~ L lineSpacingValuet 2Lnet/sf/jasperreports/engine/type/LineSpacingEnum;L markupq ~ L paddingq ~ ÅL pdfEncodingq ~ L pdfFontNameq ~ L 
reportFontq ~ L rightBorderq ~ L rightBorderColorq ~ ÊL rightPaddingq ~ ÅL rotationq ~ L 
rotationValuet /Lnet/sf/jasperreports/engine/type/RotationEnum;L 	topBorderq ~ L topBorderColorq ~ ÊL 
topPaddingq ~ ÅL verticalAlignmentq ~ L verticalAlignmentValuet 4Lnet/sf/jasperreports/engine/type/VerticalAlignEnum;xq ~ É  wñ   
      \  Ô   pq ~ q ~ Âppppppq ~ Öppppq ~ â  wñpppppt Arialsr java.lang.Integerâ ¤÷8 I valuexq ~ ë   p~r 4net.sf.jasperreports.engine.type.HorizontalAlignEnum          xq ~ t RIGHTpppppppppsr .net.sf.jasperreports.engine.base.JRBaseLineBox      'Ø L 
bottomPaddingq ~ ÅL 	bottomPent +Lnet/sf/jasperreports/engine/base/JRBoxPen;L boxContainert ,Lnet/sf/jasperreports/engine/JRBoxContainer;L leftPaddingq ~ ÅL leftPenq ~ ÿL paddingq ~ ÅL penq ~ ÿL rightPaddingq ~ ÅL rightPenq ~ ÿL 
topPaddingq ~ ÅL topPenq ~ ÿxppsr 3net.sf.jasperreports.engine.base.JRBaseBoxBottomPen      'Ø  xr -net.sf.jasperreports.engine.base.JRBaseBoxPen      'Ø L lineBoxq ~ óxq ~ ä  wñppppq ~q ~q ~ ÷psr 1net.sf.jasperreports.engine.base.JRBaseBoxLeftPen      'Ø  xq ~  wñppppq ~q ~psq ~  wñppppq ~q ~psr 2net.sf.jasperreports.engine.base.JRBaseBoxRightPen      'Ø  xq ~  wñppppq ~q ~psr 0net.sf.jasperreports.engine.base.JRBaseBoxTopPen      'Ø  xq ~  wñppppq ~q ~pppppppppppppppp~r 2net.sf.jasperreports.engine.type.VerticalAlignEnum          xq ~ t MIDDLE  wñ        pp~r 3net.sf.jasperreports.engine.type.EvaluationTimeEnum          xq ~ t NOWsq ~    uq ~    sq ~ t perct java.lang.Stringppppppppppsq ~ í  wñ   
      d  p   pq ~ q ~ Âpppppp~q ~ Õt FLOATppppq ~ â  wñpppppq ~ øq ~ úpq ~ üpppppppppsq ~ þpsq ~  wñppppq ~q ~q ~psq ~  wñppppq ~q ~psq ~  wñppppq ~q ~psq ~  wñppppq ~q ~psq ~
  wñppppq ~q ~ppppppppppppppppq ~
  wñ        ppq ~sq ~    uq ~    sq ~ t repescagem_Apresentart java.lang.Stringpppppppppt ###0.00sq ~ í  wñ   
      d     pq ~ q ~ Âppppppq ~ppppq ~ â  wñpppppq ~ øq ~ úpq ~ üpppppppppsq ~ þpsq ~  wñppppq ~'q ~'q ~&psq ~  wñppppq ~'q ~'psq ~  wñppppq ~'q ~'psq ~  wñppppq ~'q ~'psq ~
  wñppppq ~'q ~'ppppppppppppppppq ~
  wñ        ppq ~sq ~    uq ~    sq ~ t metaAtingida_Apresentart java.lang.Stringpppppppppt ###0.00sq ~ í  wñ   
      d   §   pq ~ q ~ Âppppppq ~ppppq ~ â  wñpppppq ~ øq ~ úpq ~ üpppppppppsq ~ þpsq ~  wñppppq ~4q ~4q ~3psq ~  wñppppq ~4q ~4psq ~  wñppppq ~4q ~4psq ~  wñppppq ~4q ~4psq ~
  wñppppq ~4q ~4ppppppppppppppppq ~
  wñ        ppq ~sq ~    uq ~    sq ~ t meta_Apresentart java.lang.Stringpppppppppt ###0.00sq ~ í  wñ   
             pq ~ q ~ Âppppppq ~ppppq ~ â  wñpppppq ~ øq ~ úpppppppppppsq ~ þpsq ~  wñppppq ~Aq ~Aq ~@psq ~  wñppppq ~Aq ~Apsq ~  wñppppq ~Aq ~Apsq ~  wñppppq ~Aq ~Apsq ~
  wñppppq ~Aq ~Appppppppppppppppq ~
  wñ        ppq ~sq ~    uq ~    sq ~ t 
nomeImpressaot java.lang.Stringppppppppppxp  wñ   ppq ~ ppsr java.util.HashSetºD¸·4  xpw   ?@     t "net.sf.jasperreports.engine.data.*t net.sf.jasperreports.engine.*t java.util.*xt javasq ~ sq ~    w   sq ~ í  wñ   
       7        pq ~ q ~Rpt 
textField-207ppppq ~ Öppppq ~ â  wñpppppt Arialq ~ úpppsr java.lang.BooleanÍ rÕúî Z valuexppppppppsq ~ þpsq ~  wñsq ~ Ñ    ÿfffpppp~r .net.sf.jasperreports.engine.type.LineStyleEnum          xq ~ t SOLIDsq ~ ê?   q ~Yq ~Yq ~Tpsq ~  wñsq ~ Ñ    ÿfffppppq ~]sq ~ ê?   q ~Yq ~Ypsq ~  wñppppq ~Yq ~Ypsq ~  wñsq ~ Ñ    ÿfffppppq ~]sq ~ ê?   q ~Yq ~Ypsq ~
  wñsq ~ Ñ    ÿfffppppq ~]sq ~ ê?   q ~Yq ~Ypppppt Helvetica-Obliqueppppppppppq ~
  wñ        ppq ~sq ~    uq ~    sq ~ t " "+" UsuÃ¡rio: " + sq ~ t usuariot java.lang.Stringppppppsq ~W ppt  xp  wñ   ppq ~ sq ~ (  wñ ur &[Lnet.sf.jasperreports.engine.JRField;<ßÇN*òp  xp   sr ,net.sf.jasperreports.engine.base.JRBaseField      'Ø L descriptionq ~ L nameq ~ L 
propertiesMapq ~ ,L valueClassNameq ~ L valueClassRealNameq ~ xppt 
nomeImpressaosq ~ 9pppt java.lang.Stringpsq ~wpt meta_Apresentarsq ~ 9pppt java.lang.Stringpsq ~wpt metaAtingida_Apresentarsq ~ 9pppt java.lang.Stringpsq ~wpt porcentagemsq ~ 9pppt java.lang.Doublepsq ~wpt repescagem_Apresentarsq ~ 9pppt java.lang.Stringpsq ~wpt percsq ~ 9pppt java.lang.Stringppur &[Lnet.sf.jasperreports.engine.JRGroup;@£_zLýxê  xp   sr ,net.sf.jasperreports.engine.base.JRBaseGroup      'Ø I PSEUDO_SERIAL_VERSION_UIDB footerPositionZ isReprintHeaderOnEachPageZ isResetPageNumberZ isStartNewColumnZ isStartNewPageZ keepTogetherI minHeightToStartNewPageL 
countVariablet (Lnet/sf/jasperreports/engine/JRVariable;L 
expressionq ~ L footerPositionValuet 5Lnet/sf/jasperreports/engine/type/FooterPositionEnum;L groupFooterq ~ L groupFooterSectionq ~ 	L groupHeaderq ~ L groupHeaderSectionq ~ 	L nameq ~ xp  wñ          sq ~ x  wî   q ~ sq ~    
uq ~    sq ~ t new java.lang.Integer(1)q ~ Hppq ~ ppsq ~    uq ~    sq ~ t new java.lang.Integer(0)q ~ Hpt Metas_COUNTq ~~q ~ t GROUPq ~ Hpsq ~    uq ~    sq ~ t PAGE_NUMBERt java.lang.Objectp~r 3net.sf.jasperreports.engine.type.FooterPositionEnum          xq ~ t NORMALpsq ~ ½uq ~ À   sq ~ sq ~    w   sr 0net.sf.jasperreports.engine.base.JRBaseSubreport      'Ø L connectionExpressionq ~ L dataSourceExpressionq ~ L 
expressionq ~ L isUsingCacheq ~ ð[ 
parameterst 3[Lnet/sf/jasperreports/engine/JRSubreportParameter;L parametersMapExpressionq ~ [ returnValuest 5[Lnet/sf/jasperreports/engine/JRSubreportReturnValue;L runToBottomq ~ ðxq ~ É  wñ   H          2   pq ~ q ~¬ppppppq ~ Öppppq ~ âpsq ~    
uq ~    sq ~ t listaTotaisq ~ Lpsq ~    uq ~    sq ~ t 
SUBREPORT_DIRsq ~ t  + "BICRMRel_subreport1.jasper"t java.lang.Stringppppppsq ~ Ä  wñ           J   ä   sq ~ Ñ    ÿÀÀÀpppq ~ q ~¬sq ~ Ñ    ÿÀÀÀppppppppq ~ Öppppq ~ â  wîppsq ~ ä  wñppppq ~½ppsq ~ Ä  wñ           J      sq ~ Ñ    ÿÀÀÀpppq ~ q ~¬sq ~ Ñ    ÿÀÀÀppppppppq ~ Öppppq ~ â  wîppsq ~ ä  wñppppq ~Áppsq ~ Ä  wñ           J  =   sq ~ Ñ    ÿÀÀÀpppq ~ q ~¬sq ~ Ñ    ÿÀÀÀppppppppq ~ Öppppq ~ â  wîppsq ~ ä  wñppppq ~Åppsq ~ Ä  wñ           J   2   sq ~ Ñ    ÿÀÀÀpppq ~ q ~¬sq ~ Ñ    ÿÀÀÀppppppppq ~ Öppppq ~ â  wîppsq ~ ä  wñppppq ~Éppsq ~ Ä  wñ   0        J   2   pq ~ q ~¬sq ~ Ñ    ÿÀÀÀppppppppq ~ Öppppq ~ â  wîppsq ~ ä  wñppppq ~Íppsq ~ Ä  wñ   0        J      pq ~ q ~¬sq ~ Ñ    ÿÀÀÀppppppppq ~ Öppppq ~ â  wîppsq ~ ä  wñppppq ~Ðppsq ~ Ä  wñ   0        J   ä   pq ~ q ~¬sq ~ Ñ    ÿÀÀÀppppppppq ~ Öppppq ~ â  wîppsq ~ ä  wñppppq ~Óppsq ~ Ä  wñ   0        J  =   pq ~ q ~¬sq ~ Ñ    ÿÀÀÀppppppppq ~ Öppppq ~ â  wîppsq ~ ä  wñppppq ~Öppsq ~ í  wñ   0      K   2   pq ~ q ~¬ppppppq ~ppppq ~ â  wñpppppq ~ øsq ~ ù   p~q ~ ût CENTERq ~Xppppppppsq ~ þpsq ~  wñppppq ~Ýq ~Ýq ~Ùpsq ~  wñppppq ~Ýq ~Ýpsq ~  wñppppq ~Ýq ~Ýpsq ~  wñppppq ~Ýq ~Ýpsq ~
  wñppppq ~Ýq ~Ýppppppppppppppppq ~
  wñ        ppq ~sq ~    uq ~    sq ~ t agendLigt java.lang.Stringpppppppppt ###0.00sq ~ í  wñ   0      J      pq ~ q ~¬ppppppq ~ppppq ~ â  wñpppppq ~ øq ~Úpq ~Ûq ~Xppppppppsq ~ þpsq ~  wñppppq ~êq ~êq ~épsq ~  wñppppq ~êq ~êpsq ~  wñppppq ~êq ~êpsq ~  wñppppq ~êq ~êpsq ~
  wñppppq ~êq ~êppppppppppppppppq ~
  wñ        ppq ~sq ~    uq ~    sq ~ t 
indSemContt java.lang.Stringpppppppppt ###0.00sq ~ í  wñ   0      J   ä   pq ~ q ~¬ppppppq ~ppppq ~ â  wñpppppq ~ øq ~Úpq ~Ûq ~Xppppppppsq ~ þpsq ~  wñppppq ~÷q ~÷q ~öpsq ~  wñppppq ~÷q ~÷psq ~  wñppppq ~÷q ~÷psq ~  wñppppq ~÷q ~÷psq ~
  wñppppq ~÷q ~÷ppppppppppppppppq ~
  wñ        ppq ~sq ~    uq ~    sq ~ t 	contRecept java.lang.Stringpppppppppt ###0.00sq ~ í  wñ   0      J  =   pq ~ q ~¬ppppppq ~ppppq ~ â  wñpppppq ~ øq ~Úpq ~Ûq ~Xppppppppsq ~ þpsq ~  wñppppq ~q ~q ~psq ~  wñppppq ~q ~psq ~  wñppppq ~q ~psq ~  wñppppq ~q ~psq ~
  wñppppq ~q ~ppppppppppppppppq ~
  wñ        ppq ~sq ~    uq ~    sq ~ t objecoest java.lang.Stringpppppppppt ###0.00sr 1net.sf.jasperreports.engine.base.JRBaseStaticText      'Ø L textq ~ xq ~ ñ  wñ           d       pq ~ q ~¬ppppppq ~ Öppppq ~ â  wñpppppq ~ øppppppppppppsq ~ þpsq ~  wñppppq ~q ~q ~psq ~  wñppppq ~q ~psq ~  wñppppq ~q ~psq ~  wñppppq ~q ~psq ~
  wñppppq ~q ~pppppppppppppppppt 
Resultado:sq ~  wñ           d      rpq ~ q ~¬ppppppq ~ Öppppq ~ â  wñpppppq ~ øppppppppppppsq ~ þpsq ~  wñppppq ~q ~q ~psq ~  wñppppq ~q ~psq ~  wñppppq ~q ~psq ~  wñppppq ~q ~psq ~
  wñppppq ~q ~pppppppppppppppppt Indicadores:sq ~  wñ           J   2   pq ~ q ~¬ppppppq ~ Öppppq ~ â  wñpppppq ~ øsq ~ ù   pq ~Ûpppppppppsq ~ þpsq ~  wñppppq ~#q ~#q ~!psq ~  wñppppq ~#q ~#psq ~  wñppppq ~#q ~#psq ~  wñppppq ~#q ~#psq ~
  wñppppq ~#q ~#ppppppppppppppppq ~
t !Agendamento de
LigaÃ§Ã£o Pendentesq ~  wñ           J      pq ~ q ~¬ppppppq ~ Öppppq ~ â  wñpppppq ~ øq ~"pq ~Ûpppppppppsq ~ þpsq ~  wñppppq ~+q ~+q ~*psq ~  wñppppq ~+q ~+psq ~  wñppppq ~+q ~+psq ~  wñppppq ~+q ~+psq ~
  wñppppq ~+q ~+ppppppppppppppppq ~
t IndicaÃ§Ãµes
Sem Contatosq ~  wñ           J   ä   pq ~ q ~¬ppppppq ~ Öppppq ~ â  wñpppppq ~ øq ~"pq ~Ûpppppppppsq ~ þpsq ~  wñppppq ~3q ~3q ~2psq ~  wñppppq ~3q ~3psq ~  wñppppq ~3q ~3psq ~  wñppppq ~3q ~3psq ~
  wñppppq ~3q ~3ppppppppppppppppq ~
t Contato
Receptivosq ~  wñ           J  =   pq ~ q ~¬ppppppq ~ Öppppq ~ â  wñpppppq ~ øq ~"pq ~Ûpppppppppsq ~ þpsq ~  wñppppq ~;q ~;q ~:psq ~  wñppppq ~;q ~;psq ~  wñppppq ~;q ~;psq ~  wñppppq ~;q ~;psq ~
  wñppppq ~;q ~;ppppppppppppppppq ~
t ObjeÃ§Ãµes
por Fasexp  wñ   Ùppppsq ~ ½uq ~ À   sq ~ sq ~    w   sq ~  wñ           d  p    pq ~ q ~Dpt 
staticText-19ppppq ~ Öppppq ~ â  wñpppppq ~ øsq ~ ù   
pq ~ üq ~Xppppppppsq ~ þpsq ~  wñppppq ~Iq ~Iq ~Fpsq ~  wñppppq ~Iq ~Ipsq ~  wñppppq ~Iq ~Ipsq ~  wñppppq ~Iq ~Ipsq ~
  wñppppq ~Iq ~Ipppppt Helvetica-Boldppppppppppq ~
t 
Repescagemsq ~  wñ           d   ¨    pq ~ q ~Dpt 
staticText-19ppppq ~ Öppppq ~ â  wñpppppq ~ øq ~Hpq ~ üq ~Xppppppppsq ~ þpsq ~  wñppppq ~Sq ~Sq ~Qpsq ~  wñppppq ~Sq ~Spsq ~  wñppppq ~Sq ~Spsq ~  wñppppq ~Sq ~Spsq ~
  wñppppq ~Sq ~Spppppt Helvetica-Boldppppppppppq ~
t Metasq ~  wñ           \  Ô    pq ~ q ~Dpt 
staticText-19ppppq ~ Öppppq ~ â  wñpppppq ~ øq ~Hpq ~ üq ~Xppppppppsq ~ þpsq ~  wñppppq ~]q ~]q ~[psq ~  wñppppq ~]q ~]psq ~  wñppppq ~]q ~]psq ~  wñppppq ~]q ~]psq ~
  wñppppq ~]q ~]pppppt Helvetica-Boldppppppppppq ~
t Percentual (%)sq ~  wñ           d      pq ~ q ~Dpt 
staticText-19ppppq ~ Öppppq ~ â  wñpppppq ~ øq ~Hpq ~ üq ~Xppppppppsq ~ þpsq ~  wñppppq ~gq ~gq ~epsq ~  wñppppq ~gq ~gpsq ~  wñppppq ~gq ~gpsq ~  wñppppq ~gq ~gpsq ~
  wñppppq ~gq ~gpppppt Helvetica-Boldppppppppppq ~
t 
Meta Atingidasq ~  wñ                   pq ~ q ~Dpq ~Rppppq ~ Öppppq ~ â  wñpppppq ~ øq ~Hppq ~Xppppppppsq ~ þpsq ~  wñppppq ~pq ~pq ~opsq ~  wñppppq ~pq ~ppsq ~  wñppppq ~pq ~ppsq ~  wñppppq ~pq ~ppsq ~
  wñppppq ~pq ~ppppppq ~Yppppppppppq ~
t Identificador Metaxp  wñ   pppt Metast ParcelaEmAbertoReluq ~ 4   1sq ~ 6ppq ~ 8psq ~ 9pppq ~ <psq ~ 6ppq ~ >psq ~ 9pppq ~ @psq ~ 6ppq ~ Bpsq ~ 9pppq ~ Dpsq ~ 6ppq ~ Fpsq ~ 9pppq ~ Hpsq ~ 6ppq ~ Jpsq ~ 9pppq ~ Lpsq ~ 6ppq ~ Npsq ~ 9pppq ~ Ppsq ~ 6ppq ~ Rpsq ~ 9pppq ~ Tpsq ~ 6ppq ~ Vpsq ~ 9pppq ~ Xpsq ~ 6ppq ~ Zpsq ~ 9pppq ~ \psq ~ 6ppq ~ ^psq ~ 9pppq ~ `psq ~ 6ppq ~ bpsq ~ 9pppq ~ dpsq ~ 6ppq ~ fpsq ~ 9pppq ~ hpsq ~ 6ppq ~ jpsq ~ 9pppq ~ lpsq ~ 6ppq ~ npsq ~ 9pppq ~ ppsq ~ 6ppq ~ rpsq ~ 9pppq ~ tpsq ~ 6ppt REPORT_VIRTUALIZERpsq ~ 9pppt )net.sf.jasperreports.engine.JRVirtualizerpsq ~ 6ppt IS_IGNORE_PAGINATIONpsq ~ 9pppq ~ àpsq ~ 6  ppt logoPadraoRelatoriopsq ~ 9pppt java.io.InputStreampsq ~ 6  ppt tituloRelatoriopsq ~ 9pppt java.lang.Stringpsq ~ 6  ppt nomeEmpresapsq ~ 9pppt java.lang.Stringpsq ~ 6  ppt versaoSoftwarepsq ~ 9pppt java.lang.Stringpsq ~ 6  ppt usuariopsq ~ 9pppt java.lang.Stringpsq ~ 6  ppt filtrospsq ~ 9pppt java.lang.Stringpsq ~ 6 sq ~     uq ~    sq ~ t p"E:\\desenvolvimentosv\\Projeto Pacto Solucoes\\ZillyonWeb\\src\\java\\relatorio\\designRelatorio\\financeiro\\"t java.lang.Stringppt 
SUBREPORT_DIRpsq ~ 9pppq ~¼psq ~ 6 sq ~    uq ~    sq ~ t p"E:\\desenvolvimentosv\\Projeto Pacto Solucoes\\ZillyonWeb\\src\\java\\relatorio\\designRelatorio\\financeiro\\"t java.lang.Stringppt SUBREPORT_DIR1psq ~ 9pppq ~Äpsq ~ 6  ppt dataInipsq ~ 9pppt java.lang.Stringpsq ~ 6  ppt dataFimpsq ~ 9pppt java.lang.Stringpsq ~ 6  ppt qtdAVpsq ~ 9pppt java.lang.Stringpsq ~ 6  ppt qtdCApsq ~ 9pppt java.lang.Stringpsq ~ 6  ppt qtdChequeAVpsq ~ 9pppt java.lang.Stringpsq ~ 6  ppt qtdChequePRpsq ~ 9pppt java.lang.Stringpsq ~ 6  ppt qtdOutropsq ~ 9pppt java.lang.Stringpsq ~ 6  ppt valorAVpsq ~ 9pppt java.lang.Doublepsq ~ 6  ppt valorCApsq ~ 9pppt java.lang.Doublepsq ~ 6  ppt 
valorChequeAVpsq ~ 9pppt java.lang.Doublepsq ~ 6  ppt 
valorChequePRpsq ~ 9pppt java.lang.Doublepsq ~ 6  ppt 
valorOutropsq ~ 9pppt java.lang.Doublepsq ~ 6  ppt 
parametro1psq ~ 9pppt java.lang.Stringpsq ~ 6  ppt 
parametro2psq ~ 9pppt java.lang.Stringpsq ~ 6  ppt 
parametro3psq ~ 9pppt java.lang.Stringpsq ~ 6  ppt 
parametro4psq ~ 9pppt java.lang.Stringpsq ~ 6  ppt 
parametro5psq ~ 9pppt java.lang.Stringpsq ~ 6  ppt 
parametro6psq ~ 9pppt java.lang.Stringpsq ~ 6 ppt totalPessoaspsq ~ 9pppt java.lang.Doublepsq ~ 6  ppt listaTotaispsq ~ 9pppt java.lang.Objectpsq ~ 6 ppt agendLigpsq ~ 9pppt java.lang.Stringpsq ~ 6 ppt 
indSemContpsq ~ 9pppt java.lang.Stringpsq ~ 6 ppt 	contReceppsq ~ 9pppt java.lang.Stringpsq ~ 6 ppt objecoespsq ~ 9pppt java.lang.Stringpsq ~ 9psq ~    w   t ireport.scriptlethandlingt ireport.encodingt ireport.zoomt 	ireport.xt 	ireport.yxsr java.util.HashMapÚÁÃ`Ñ F 
loadFactorI 	thresholdxp?@     w      q ~+t 2.5937424601000063q ~*t 
ISO-8859-1q ~,t 0q ~-t 2q ~)t 0xpppppuq ~ v   sq ~ x  wî   q ~ ppq ~ ppsq ~    uq ~    sq ~ t new java.lang.Integer(1)q ~ Hpq ~ pq ~ q ~ Hpsq ~ x  wî   q ~ ppq ~ ppsq ~    uq ~    sq ~ t new java.lang.Integer(1)q ~ Hpq ~ pq ~ q ~ Hpsq ~ x  wî   q ~ sq ~    uq ~    sq ~ t new java.lang.Integer(1)q ~ Hppq ~ ppsq ~    uq ~    sq ~ t new java.lang.Integer(0)q ~ Hpq ~ £pq ~ q ~ Hpsq ~ x  wî   q ~ sq ~    uq ~    sq ~ t new java.lang.Integer(1)q ~ Hppq ~ ppsq ~    uq ~    sq ~ t new java.lang.Integer(0)q ~ Hpq ~ ­pq ~ q ~ Hpsq ~ x  wî   q ~ sq ~    uq ~    sq ~ t new java.lang.Integer(1)q ~ Hppq ~ ppsq ~    	uq ~    sq ~ t new java.lang.Integer(0)q ~ Hpq ~ ·pq ~ ¸q ~ Hpq ~~q ~ ºt EMPTYq ~xp~r 0net.sf.jasperreports.engine.type.OrientationEnum          xq ~ t PORTRAITpsq ~ sq ~    w   sq ~ Ä  wñ          7       3pq ~ q ~`ppppppq ~ Öppppq ~ â  wîppsq ~ ä  wñpppsq ~ ê>  q ~bppsq ~ í  wñ           |  »   sq ~ Ñ    ÿÿÿÿpppq ~ q ~`pt 	dataRel-1p~r )net.sf.jasperreports.engine.type.ModeEnum          xq ~ t OPAQUEppq ~ Öppppq ~ â  wñpppppt Verdanaq ~ úpq ~Ûpq ~rpppppppsq ~ þsq ~ ù   sq ~  wñsq ~ Ñ    ÿfffppppq ~]sq ~ ê?   q ~lq ~lq ~epsq ~  wñsq ~ Ñ    ÿfffppppq ~]sq ~ ê?   q ~lq ~lpsq ~  wñppppq ~lq ~lpsq ~  wñsq ~ Ñ    ÿfffppppq ~]sq ~ ê?   q ~lq ~lpsq ~
  wñsq ~ Ñ    ÿfffppppq ~]sq ~ ê?   q ~lq ~lpppppt 	Helveticappppppppppq ~
  wñ        ppq ~sq ~    uq ~    sq ~ t 
new Date()t java.util.Dateppppppq ~rppt dd/MM/yyyy HH.mm.sssq ~ í  wñ   $       h   S   pq ~ q ~`pt textField-2ppppq ~ Öppppq ~ â  wñpppppt Arialsq ~ ù   pq ~Ûq ~Xppppppppsq ~ þpsq ~  wñsq ~ Ñ    ÿfffppppq ~]sq ~ ê?   q ~q ~q ~psq ~  wñppq ~]sq ~ ê    q ~q ~psq ~  wñppq ~]sq ~ ê?   q ~q ~psq ~  wñppq ~]sq ~ ê    q ~q ~psq ~
  wñsq ~ Ñ    ÿfffppppq ~]sq ~ ê?   q ~q ~pppppt Helvetica-Boldppppppppppq ~
  wñ        ppq ~sq ~    uq ~    sq ~ t tituloRelatoriot java.lang.Stringppppppq ~rpppsq ~ í  wñ           |  »   pq ~ q ~`pt textField-25ppppq ~ Öppppq ~ â  wñpppppt Arialppq ~Ûpppppppppsq ~ þpsq ~  wñsq ~ Ñ    ÿfffppppq ~]sq ~ ê?   q ~q ~q ~psq ~  wñsq ~ Ñ    ÿfffppppq ~]sq ~ ê?   q ~q ~psq ~  wñppppq ~q ~psq ~  wñsq ~ Ñ    ÿ   ppppq ~]sq ~ ê    q ~q ~psq ~
  wñsq ~ Ñ    ÿ   ppppq ~]sq ~ ê    q ~q ~ppppppppppppppppq ~
  wñ        ppq ~sq ~    uq ~    sq ~ t "PÃ¡g: " + sq ~ t PAGE_NUMBERsq ~ t  + " de " + sq ~ t PAGE_NUMBERt java.lang.Stringppppppq ~rpppsq ~ í  wñ              7   pq ~ q ~`pt textField-26ppppq ~ Öppppq ~ â  wñpppppt Arialppppppppppppsq ~ þq ~msq ~  wñsq ~ Ñ    ÿfffppppq ~]sq ~ ê?   q ~¸q ~¸q ~µpsq ~  wñsq ~ Ñ    ÿfffppppq ~]sq ~ ê    q ~¸q ~¸psq ~  wñppppq ~¸q ~¸psq ~  wñsq ~ Ñ    ÿfffppppq ~]sq ~ ê?   q ~¸q ~¸psq ~
  wñsq ~ Ñ    ÿ   ppppq ~]sq ~ ê    q ~¸q ~¸ppppppppppppppppq ~
  wñ        ppq ~ppppppq ~rpppsq ~ í  wñ          6      3pq ~ q ~`ppppppq ~ Öppppq ~ â  wñpppppq ~ øq ~Hpq ~Ûpppppppppsq ~ þpsq ~  wñppppq ~Çq ~Çq ~Æpsq ~  wñppppq ~Çq ~Çpsq ~  wñppppq ~Çq ~Çpsq ~  wñppppq ~Çq ~Çpsq ~
  wñppppq ~Çq ~Çpppppppppppppppp~q ~t TOP  wñ        ppq ~sq ~    uq ~    sq ~ t filtrost java.lang.Stringppppppppppsr ,net.sf.jasperreports.engine.base.JRBaseImage      'Ø *I PSEUDO_SERIAL_VERSION_UIDI 
bookmarkLevelB evaluationTimeB hyperlinkTargetB 
hyperlinkTypeZ isLazyB onErrorTypeL anchorNameExpressionq ~ L borderq ~ L borderColorq ~ ÊL bottomBorderq ~ L bottomBorderColorq ~ ÊL 
bottomPaddingq ~ ÅL evaluationGroupq ~ zL evaluationTimeValueq ~ îL 
expressionq ~ L horizontalAlignmentq ~ L horizontalAlignmentValueq ~ òL hyperlinkAnchorExpressionq ~ L hyperlinkPageExpressionq ~ [ hyperlinkParametersq ~ ïL hyperlinkReferenceExpressionq ~ L hyperlinkTooltipExpressionq ~ L isUsingCacheq ~ ðL 
leftBorderq ~ L leftBorderColorq ~ ÊL leftPaddingq ~ ÅL lineBoxq ~ óL 
linkTargetq ~ L linkTypeq ~ L onErrorTypeValuet 2Lnet/sf/jasperreports/engine/type/OnErrorTypeEnum;L paddingq ~ ÅL rightBorderq ~ L rightBorderColorq ~ ÊL rightPaddingq ~ ÅL 
scaleImageq ~ L scaleImageValuet 1Lnet/sf/jasperreports/engine/type/ScaleImageEnum;L 	topBorderq ~ L topBorderColorq ~ ÊL 
topPaddingq ~ ÅL verticalAlignmentq ~ L verticalAlignmentValueq ~ öxq ~ Æ  wñ   $       R      pq ~ q ~`pt image-1ppppq ~ Öppppq ~ â  wîppsq ~ ä  wñppppq ~×p  wñ         ppppppp~q ~t PAGEsq ~    uq ~    sq ~ t logoPadraoRelatoriot java.io.InputStreamppppppppq ~Xpppsq ~ þpsq ~  wñsq ~ Ñ    ÿfffppppq ~]sq ~ ê?   q ~áq ~áq ~×psq ~  wñsq ~ Ñ    ÿfffppppq ~]sq ~ ê?   q ~áq ~ápsq ~  wñppppq ~áq ~ápsq ~  wñsq ~ Ñ    ÿfffppppq ~]sq ~ ê?   q ~áq ~ápsq ~
  wñsq ~ Ñ    ÿfffppppq ~]sq ~ ê?   q ~áq ~ápp~r 0net.sf.jasperreports.engine.type.OnErrorTypeEnum          xq ~ t BLANKpppppppppppxp  wñ   `ppq ~ ~r /net.sf.jasperreports.engine.type.PrintOrderEnum          xq ~ t VERTICALpppsq ~ sq ~     w    xp  wñ    ppq ~ ~r 3net.sf.jasperreports.engine.type.WhenNoDataTypeEnum          xq ~ t NO_PAGESsr 6net.sf.jasperreports.engine.design.JRReportCompileData      'Ø L crosstabCompileDataq ~ :L datasetCompileDataq ~ :L mainDatasetCompileDataq ~ xpsq ~.?@     w       xsq ~.?@     w      q ~ 3ur [B¬óøTà  xp  ÓÊþº¾   .  -ParcelaEmAbertoRel_Teste_1453998052406_226684  ,net/sf/jasperreports/engine/fill/JREvaluator  parameter_REPORT_LOCALE 2Lnet/sf/jasperreports/engine/fill/JRFillParameter; parameter_JASPER_REPORT parameter_REPORT_TIME_ZONE parameter_SORT_FIELDS parameter_REPORT_FILE_RESOLVER parameter_REPORT_SCRIPTLET parameter_REPORT_PARAMETERS_MAP parameter_REPORT_CONNECTION parameter_REPORT_CLASS_LOADER parameter_REPORT_DATA_SOURCE $parameter_REPORT_URL_HANDLER_FACTORY parameter_REPORT_FORMAT_FACTORY parameter_REPORT_MAX_COUNT parameter_REPORT_TEMPLATES  parameter_REPORT_RESOURCE_BUNDLE variable_PAGE_NUMBER 1Lnet/sf/jasperreports/engine/fill/JRFillVariable; variable_COLUMN_NUMBER variable_REPORT_COUNT variable_PAGE_COUNT variable_COLUMN_COUNT <init> ()V Code  
    	     	  "  	  $ 	 	  & 
 	  (  	  *  	  , 
 	  .  	  0  	  2  	  4  	  6  	  8  	  :  	  <  	  >  	  @  	  B  	  D  	  F LineNumberTable customizedInit 0(Ljava/util/Map;Ljava/util/Map;Ljava/util/Map;)V 
initParams (Ljava/util/Map;)V K L
  M 
initFields O L
  P initVars R L
  S 
REPORT_LOCALE U 
java/util/Map W get &(Ljava/lang/Object;)Ljava/lang/Object; Y Z X [ 0net/sf/jasperreports/engine/fill/JRFillParameter ] 
JASPER_REPORT _ REPORT_TIME_ZONE a SORT_FIELDS c REPORT_FILE_RESOLVER e REPORT_SCRIPTLET g REPORT_PARAMETERS_MAP i REPORT_CONNECTION k REPORT_CLASS_LOADER m REPORT_DATA_SOURCE o REPORT_URL_HANDLER_FACTORY q REPORT_FORMAT_FACTORY s REPORT_MAX_COUNT u REPORT_TEMPLATES w REPORT_RESOURCE_BUNDLE y PAGE_NUMBER { /net/sf/jasperreports/engine/fill/JRFillVariable } 
COLUMN_NUMBER  REPORT_COUNT  
PAGE_COUNT  COLUMN_COUNT  evaluate (I)Ljava/lang/Object; 
Exceptions java/lang/Throwable  java/lang/Integer  (I)V  
   evaluateOld evaluateEstimated 
SourceFile !                      	     
               
                                                                        Õ     i*· *µ !*µ #*µ %*µ '*µ )*µ +*µ -*µ /*µ 1*µ 3*µ 5*µ 7*µ 9*µ ;*µ =*µ ?*µ A*µ C*µ E*µ G±    H   Z       	          ! " " ' # , $ 1 % 6 & ; ' @ ( E ) J * O + T , Y - ^ . c / h   I J     4     *+· N*,· Q*-· T±    H       ;  < 
 =  >  K L    c    *+V¹ \ À ^À ^µ !*+`¹ \ À ^À ^µ #*+b¹ \ À ^À ^µ %*+d¹ \ À ^À ^µ '*+f¹ \ À ^À ^µ )*+h¹ \ À ^À ^µ +*+j¹ \ À ^À ^µ -*+l¹ \ À ^À ^µ /*+n¹ \ À ^À ^µ 1*+p¹ \ À ^À ^µ 3*+r¹ \ À ^À ^µ 5*+t¹ \ À ^À ^µ 7*+v¹ \ À ^À ^µ 9*+x¹ \ À ^À ^µ ;*+z¹ \ À ^À ^µ =±    H   B    F  G $ H 6 I H J Z K l L ~ M  N ¢ O ´ P Æ Q Ø R ê S ü T U  O L           ±    H       ]  R L          [*+|¹ \ À ~À ~µ ?*+¹ \ À ~À ~µ A*+¹ \ À ~À ~µ C*+¹ \ À ~À ~µ E*+¹ \ À ~À ~µ G±    H       e  f $ g 6 h H i Z j              ë     Mª             -   9   E   Q   ]   i   u   » Y· M§ T» Y· M§ H» Y· M§ <» Y· M§ 0» Y· M§ $» Y· M§ » Y· M§ » Y· M,°    H   J    r  t 0 x 9 y < } E ~ H  Q  T  ]  `  i  l  u  x       £              ë     Mª             -   9   E   Q   ]   i   u   » Y· M§ T» Y· M§ H» Y· M§ <» Y· M§ 0» Y· M§ $» Y· M§ » Y· M§ » Y· M,°    H   J    ¬  ® 0 ² 9 ³ < · E ¸ H ¼ Q ½ T Á ] Â ` Æ i Ç l Ë u Ì x Ð  Ñ  Õ  Ý              ë     Mª             -   9   E   Q   ]   i   u   » Y· M§ T» Y· M§ H» Y· M§ <» Y· M§ 0» Y· M§ $» Y· M§ » Y· M§ » Y· M,°    H   J    æ  è 0 ì 9 í < ñ E ò H ö Q ÷ T û ] ü `  i l u x
         xuq ~þ  (Êþº¾   .¡ 'ParcelaEmAbertoRel_1453998052406_226684  ,net/sf/jasperreports/engine/fill/JREvaluator  parameter_JASPER_REPORT 2Lnet/sf/jasperreports/engine/fill/JRFillParameter; parameter_REPORT_TIME_ZONE parameter_valorCA parameter_usuario parameter_REPORT_FILE_RESOLVER parameter_contRecep parameter_REPORT_PARAMETERS_MAP parameter_qtdCA parameter_SUBREPORT_DIR1 parameter_REPORT_CLASS_LOADER $parameter_REPORT_URL_HANDLER_FACTORY parameter_REPORT_DATA_SOURCE parameter_IS_IGNORE_PAGINATION parameter_indSemCont parameter_agendLig parameter_valorChequeAV parameter_qtdChequePR parameter_valorChequePR parameter_REPORT_MAX_COUNT parameter_REPORT_TEMPLATES parameter_valorOutro parameter_qtdAV parameter_dataIni parameter_REPORT_LOCALE parameter_qtdOutro parameter_REPORT_VIRTUALIZER parameter_SORT_FIELDS parameter_logoPadraoRelatorio parameter_REPORT_SCRIPTLET parameter_REPORT_CONNECTION parameter_parametro3 parameter_SUBREPORT_DIR parameter_parametro4 parameter_dataFim parameter_parametro1 parameter_parametro2 parameter_REPORT_FORMAT_FACTORY parameter_totalPessoas parameter_tituloRelatorio parameter_parametro5 parameter_nomeEmpresa parameter_parametro6 parameter_qtdChequeAV parameter_objecoes parameter_valorAV parameter_listaTotais  parameter_REPORT_RESOURCE_BUNDLE parameter_versaoSoftware parameter_filtros field_porcentagem .Lnet/sf/jasperreports/engine/fill/JRFillField; field_meta_Apresentar field_metaAtingida_Apresentar field_nomeImpressao field_repescagem_Apresentar 
field_perc variable_PAGE_NUMBER 1Lnet/sf/jasperreports/engine/fill/JRFillVariable; variable_COLUMN_NUMBER variable_REPORT_COUNT variable_PAGE_COUNT variable_COLUMN_COUNT variable_Metas_COUNT <init> ()V Code E F
  H  	  J  	  L  	  N 	 	  P 
 	  R  	  T  	  V 
 	  X  	  Z  	  \  	  ^  	  `  	  b  	  d  	  f  	  h  	  j  	  l  	  n  	  p  	  r  	  t  	  v  	  x  	  z  	  |   	  ~ ! 	   " 	   # 	   $ 	   % 	   & 	   ' 	   ( 	   ) 	   * 	   + 	   , 	   - 	   . 	   / 	   0 	   1 	    2 	  ¢ 3 	  ¤ 4 	  ¦ 5 	  ¨ 6 	  ª 7 8	  ¬ 9 8	  ® : 8	  ° ; 8	  ² < 8	  ´ = 8	  ¶ > ?	  ¸ @ ?	  º A ?	  ¼ B ?	  ¾ C ?	  À D ?	  Â LineNumberTable customizedInit 0(Ljava/util/Map;Ljava/util/Map;Ljava/util/Map;)V 
initParams (Ljava/util/Map;)V Ç È
  É 
initFields Ë È
  Ì initVars Î È
  Ï 
JASPER_REPORT Ñ 
java/util/Map Ó get &(Ljava/lang/Object;)Ljava/lang/Object; Õ Ö Ô × 0net/sf/jasperreports/engine/fill/JRFillParameter Ù REPORT_TIME_ZONE Û valorCA Ý usuario ß REPORT_FILE_RESOLVER á 	contRecep ã REPORT_PARAMETERS_MAP å qtdCA ç SUBREPORT_DIR1 é REPORT_CLASS_LOADER ë REPORT_URL_HANDLER_FACTORY í REPORT_DATA_SOURCE ï IS_IGNORE_PAGINATION ñ 
indSemCont ó agendLig õ 
valorChequeAV ÷ qtdChequePR ù 
valorChequePR û REPORT_MAX_COUNT ý REPORT_TEMPLATES ÿ 
valorOutro qtdAV dataIni 
REPORT_LOCALE qtdOutro	 REPORT_VIRTUALIZER SORT_FIELDS
 logoPadraoRelatorio REPORT_SCRIPTLET REPORT_CONNECTION 
parametro3 
SUBREPORT_DIR 
parametro4 dataFim 
parametro1 
parametro2 REPORT_FORMAT_FACTORY! totalPessoas# tituloRelatorio% 
parametro5' nomeEmpresa) 
parametro6+ qtdChequeAV- objecoes/ valorAV1 listaTotais3 REPORT_RESOURCE_BUNDLE5 versaoSoftware7 filtros9 porcentagem; ,net/sf/jasperreports/engine/fill/JRFillField= meta_Apresentar? metaAtingida_ApresentarA 
nomeImpressaoC repescagem_ApresentarE percG PAGE_NUMBERI /net/sf/jasperreports/engine/fill/JRFillVariableK 
COLUMN_NUMBERM REPORT_COUNTO 
PAGE_COUNTQ COLUMN_COUNTS Metas_COUNTU evaluate (I)Ljava/lang/Object; 
Exceptions java/lang/ThrowableZ eE:\desenvolvimentosv\Projeto Pacto Solucoes\ZillyonWeb\src\java\relatorio\designRelatorio\financeiro\\ java/lang/Integer^ (I)V E`
_a getValue ()Ljava/lang/Object;cd
Le
 Úe (net/sf/jasperreports/engine/JRDataSourceh java/lang/StringBufferj java/lang/Stringl valueOf &(Ljava/lang/Object;)Ljava/lang/String;no
mp (Ljava/lang/String;)V Er
ks BICRMRel_subreport1.jasperu append ,(Ljava/lang/String;)Ljava/lang/StringBuffer;wx
ky toString ()Ljava/lang/String;{|
k} java/util/Date
 H PÃ¡g:  ,(Ljava/lang/Object;)Ljava/lang/StringBuffer;w
k  de  java/io/InputStream java/lang/Boolean intValue ()I
_ (Z)V E

>e   UsuÃ¡rio:  evaluateOld getOldValued
L
> evaluateEstimated getEstimatedValued
L 
SourceFile !     =                 	     
               
                                                                                                     !     "     #     $     %     &     '     (     )     *     +     ,     -     .     /     0     1     2     3     4     5     6     7 8    9 8    : 8    ; 8    < 8    = 8    > ?    @ ?    A ?    B ?    C ?    D ?     E F  G  F    6*· I*µ K*µ M*µ O*µ Q*µ S*µ U*µ W*µ Y*µ [*µ ]*µ _*µ a*µ c*µ e*µ g*µ i*µ k*µ m*µ o*µ q*µ s*µ u*µ w*µ y*µ {*µ }*µ *µ *µ *µ *µ *µ *µ *µ *µ *µ *µ *µ *µ *µ *µ *µ *µ *µ ¡*µ £*µ ¥*µ §*µ ©*µ «*µ ­*µ ¯*µ ±*µ ³*µ µ*µ ·*µ ¹*µ »*µ ½*µ ¿*µ Á*µ Ã±    Ä   þ ?      	          ! " " ' # , $ 1 % 6 & ; ' @ ( E ) J * O + T , Y - ^ . c / h 0 m 1 r 2 w 3 | 4  5  6  7  8  9  :  ; ¤ < © = ® > ³ ? ¸ @ ½ A Â B Ç C Ì D Ñ E Ö F Û G à H å I ê J ï K ô L ù M þ N O P
 Q R S T! U& V+ W0 X5   Å Æ  G   4     *+· Ê*,· Í*-· Ð±    Ä       d  e 
 f  g  Ç È  G  m    *+Ò¹ Ø À ÚÀ Úµ K*+Ü¹ Ø À ÚÀ Úµ M*+Þ¹ Ø À ÚÀ Úµ O*+à¹ Ø À ÚÀ Úµ Q*+â¹ Ø À ÚÀ Úµ S*+ä¹ Ø À ÚÀ Úµ U*+æ¹ Ø À ÚÀ Úµ W*+è¹ Ø À ÚÀ Úµ Y*+ê¹ Ø À ÚÀ Úµ [*+ì¹ Ø À ÚÀ Úµ ]*+î¹ Ø À ÚÀ Úµ _*+ð¹ Ø À ÚÀ Úµ a*+ò¹ Ø À ÚÀ Úµ c*+ô¹ Ø À ÚÀ Úµ e*+ö¹ Ø À ÚÀ Úµ g*+ø¹ Ø À ÚÀ Úµ i*+ú¹ Ø À ÚÀ Úµ k*+ü¹ Ø À ÚÀ Úµ m*+þ¹ Ø À ÚÀ Úµ o*+ ¹ Ø À ÚÀ Úµ q*+¹ Ø À ÚÀ Úµ s*+¹ Ø À ÚÀ Úµ u*+¹ Ø À ÚÀ Úµ w*+¹ Ø À ÚÀ Úµ y*+
¹ Ø À ÚÀ Úµ {*+¹ Ø À ÚÀ Úµ }*+¹ Ø À ÚÀ Úµ *+¹ Ø À ÚÀ Úµ *+¹ Ø À ÚÀ Úµ *+¹ Ø À ÚÀ Úµ *+¹ Ø À ÚÀ Úµ *+¹ Ø À ÚÀ Úµ *+¹ Ø À ÚÀ Úµ *+¹ Ø À ÚÀ Úµ *+¹ Ø À ÚÀ Úµ *+ ¹ Ø À ÚÀ Úµ *+"¹ Ø À ÚÀ Úµ *+$¹ Ø À ÚÀ Úµ *+&¹ Ø À ÚÀ Úµ *+(¹ Ø À ÚÀ Úµ *+*¹ Ø À ÚÀ Úµ *+,¹ Ø À ÚÀ Úµ *+.¹ Ø À ÚÀ Úµ *+0¹ Ø À ÚÀ Úµ ¡*+2¹ Ø À ÚÀ Úµ £*+4¹ Ø À ÚÀ Úµ ¥*+6¹ Ø À ÚÀ Úµ §*+8¹ Ø À ÚÀ Úµ ©*+:¹ Ø À ÚÀ Úµ «±    Ä   Ê 2   o  p $ q 6 r H s Z t l u ~ v  w ¢ x ´ y Æ z Ø { ê | ü } ~  2 D V i |  ¢ µ È Û î   ' : M ` s   ¬ ¿ Ò å ø   1 D W j }     Ë È  G   £     s*+<¹ Ø À>À>µ ­*+@¹ Ø À>À>µ ¯*+B¹ Ø À>À>µ ±*+D¹ Ø À>À>µ ³*+F¹ Ø À>À>µ µ*+H¹ Ø À>À>µ ·±    Ä       ¨  © & ª 9 « L ¬ _ ­ r ®  Î È  G   £     s*+J¹ Ø ÀLÀLµ ¹*+N¹ Ø ÀLÀLµ »*+P¹ Ø ÀLÀLµ ½*+R¹ Ø ÀLÀLµ ¿*+T¹ Ø ÀLÀLµ Á*+V¹ Ø ÀLÀLµ Ã±    Ä       ¶  · & ¸ 9 ¹ L º _ » r ¼ WX Y    [ G      rMª  m                   £   ¯   »   Ç   Ó   ß   ë   ÷        +  L  Z  h  v        Î  Ü  ê      (  6  D  R]M§à]M§Ù»_Y·bM§Í»_Y·bM§Á»_Y·bM§µ»_Y·bM§©»_Y·bM§»_Y·bM§»_Y·bM§»_Y·bM§y»_Y·bM§m»_Y·bM§a*´ ¹¶fÀ_M§S*´ ¥¶gÀiM§E»kY*´ ¶gÀm¸q·tv¶z¶~M§$*´ g¶gÀmM§*´ e¶gÀmM§*´ U¶gÀmM§ ú*´ ¡¶gÀmM§ ì»Y·M§ á*´ ¶gÀmM§ Ó»kY·t*´ ¹¶fÀ_¶¶z*´ ¹¶fÀ_¶¶~M§ ¢*´ «¶gÀmM§ *´ ¶gÀM§ »Y*´ Á¶fÀ_¶p § ·M§ d*´ ·¶ÀmM§ V*´ µ¶ÀmM§ H*´ ±¶ÀmM§ :*´ ¯¶ÀmM§ ,*´ ³¶ÀmM§ »kY·t*´ Q¶gÀm¶z¶~M,°    Ä   @   Ä  Æ  Ê  Ë  Ï  Ð  Ô £ Õ ¦ Ù ¯ Ú ² Þ » ß ¾ ã Ç ä Ê è Ó é Ö í ß î â ò ë ó î ÷ ÷ ø ú ü ý +.LOZ]hkv y$%)*./ 3Î4Ñ8Ü9ß=ê>íBCGHL(M+Q6R9VDWG[R\U`ph X Y    [ G      rMª  m                   £   ¯   »   Ç   Ó   ß   ë   ÷        +  L  Z  h  v        Î  Ü  ê      (  6  D  R]M§à]M§Ù»_Y·bM§Í»_Y·bM§Á»_Y·bM§µ»_Y·bM§©»_Y·bM§»_Y·bM§»_Y·bM§»_Y·bM§y»_Y·bM§m»_Y·bM§a*´ ¹¶À_M§S*´ ¥¶gÀiM§E»kY*´ ¶gÀm¸q·tv¶z¶~M§$*´ g¶gÀmM§*´ e¶gÀmM§*´ U¶gÀmM§ ú*´ ¡¶gÀmM§ ì»Y·M§ á*´ ¶gÀmM§ Ó»kY·t*´ ¹¶À_¶¶z*´ ¹¶À_¶¶~M§ ¢*´ «¶gÀmM§ *´ ¶gÀM§ »Y*´ Á¶À_¶p § ·M§ d*´ ·¶ÀmM§ V*´ µ¶ÀmM§ H*´ ±¶ÀmM§ :*´ ¯¶ÀmM§ ,*´ ³¶ÀmM§ »kY·t*´ Q¶gÀm¶z¶~M,°    Ä   @  q s w x | }  £ ¦ ¯ ² » ¾ Ç Ê Ó Ö ß â ë  î¤ ÷¥ ú©ª®¯³´ ¸+¹.½L¾OÂZÃ]ÇhÈkÌvÍyÑÒÖ×ÛÜ àÎáÑåÜæßêêëíïðôõù(ú+þ6ÿ9DGR	U
p X Y    [ G      rMª  m                   £   ¯   »   Ç   Ó   ß   ë   ÷        +  L  Z  h  v        Î  Ü  ê      (  6  D  R]M§à]M§Ù»_Y·bM§Í»_Y·bM§Á»_Y·bM§µ»_Y·bM§©»_Y·bM§»_Y·bM§»_Y·bM§»_Y·bM§y»_Y·bM§m»_Y·bM§a*´ ¹¶À_M§S*´ ¥¶gÀiM§E»kY*´ ¶gÀm¸q·tv¶z¶~M§$*´ g¶gÀmM§*´ e¶gÀmM§*´ U¶gÀmM§ ú*´ ¡¶gÀmM§ ì»Y·M§ á*´ ¶gÀmM§ Ó»kY·t*´ ¹¶À_¶¶z*´ ¹¶À_¶¶~M§ ¢*´ «¶gÀmM§ *´ ¶gÀM§ »Y*´ Á¶À_¶p § ·M§ d*´ ·¶ÀmM§ V*´ µ¶ÀmM§ H*´ ±¶ÀmM§ :*´ ¯¶ÀmM§ ,*´ ³¶ÀmM§ »kY·t*´ Q¶gÀm¶z¶~M,°    Ä   @     $ % ) * . £/ ¦3 ¯4 ²8 »9 ¾= Ç> ÊB ÓC ÖG ßH âL ëM îQ ÷R úVW[\`a e+f.jLkOoZp]thukyvzy~ ÎÑÜßêí¡¢¦(§+«6¬9°D±GµR¶UºpÂ      t _1453998052406_226684t 2net.sf.jasperreports.engine.design.JRJavacCompiler