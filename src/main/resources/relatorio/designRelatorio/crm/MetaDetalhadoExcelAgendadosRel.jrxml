<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="ParcelaEmAbertoRel" pageWidth="1275" pageHeight="842" columnWidth="1247" leftMargin="14" rightMargin="14" topMargin="14" bottomMargin="14" whenResourceMissingType="Empty">
	<property name="ireport.scriptlethandling" value="0"/>
	<property name="ireport.encoding" value="ISO-8859-1"/>
	<property name="ireport.zoom" value="1.7715610000000026"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="0"/>
	<import value="net.sf.jasperreports.engine.*"/>
	<import value="java.util.*"/>
	<import value="net.sf.jasperreports.engine.data.*"/>
	<subDataset name="Teste"/>
	<parameter name="logoPadraoRelatorio" class="java.io.InputStream" isForPrompting="false"/>
	<parameter name="tituloRelatorio" class="java.lang.String" isForPrompting="false"/>
	<parameter name="nomeEmpresa" class="java.lang.String" isForPrompting="false"/>
	<parameter name="versaoSoftware" class="java.lang.String" isForPrompting="false"/>
	<parameter name="usuario" class="java.lang.String" isForPrompting="false"/>
	<parameter name="filtros" class="java.lang.String" isForPrompting="false"/>
	<parameter name="SUBREPORT_DIR" class="java.lang.String">
		<defaultValueExpression><![CDATA["E:\\desenvolvimentosv\\Projeto Pacto Solucoes\\ZillyonWeb\\src\\java\\relatorio\\designRelatorio\\financeiro\\"]]></defaultValueExpression>
	</parameter>
	<parameter name="SUBREPORT_DIR1" class="java.lang.String">
		<defaultValueExpression><![CDATA["E:\\desenvolvimentosv\\Projeto Pacto Solucoes\\ZillyonWeb\\src\\java\\relatorio\\designRelatorio\\financeiro\\"]]></defaultValueExpression>
	</parameter>
	<parameter name="dataIni" class="java.lang.String" isForPrompting="false"/>
	<parameter name="dataFim" class="java.lang.String" isForPrompting="false"/>
	<parameter name="qtdAV" class="java.lang.String" isForPrompting="false"/>
	<parameter name="qtdCA" class="java.lang.String" isForPrompting="false"/>
	<parameter name="qtdChequeAV" class="java.lang.String" isForPrompting="false"/>
	<parameter name="qtdChequePR" class="java.lang.String" isForPrompting="false"/>
	<parameter name="qtdOutro" class="java.lang.String" isForPrompting="false"/>
	<parameter name="valorAV" class="java.lang.Double" isForPrompting="false"/>
	<parameter name="valorCA" class="java.lang.Double" isForPrompting="false"/>
	<parameter name="valorChequeAV" class="java.lang.Double" isForPrompting="false"/>
	<parameter name="valorChequePR" class="java.lang.Double" isForPrompting="false"/>
	<parameter name="valorOutro" class="java.lang.Double" isForPrompting="false"/>
	<parameter name="parametro1" class="java.lang.String" isForPrompting="false"/>
	<parameter name="parametro2" class="java.lang.String" isForPrompting="false"/>
	<parameter name="parametro3" class="java.lang.String" isForPrompting="false"/>
	<parameter name="parametro4" class="java.lang.String" isForPrompting="false"/>
	<parameter name="parametro5" class="java.lang.String" isForPrompting="false"/>
	<parameter name="parametro6" class="java.lang.String" isForPrompting="false"/>
	<field name="nomePessoaRel" class="java.lang.String"/>
	<field name="agenda.tipoAgendamento_Apresentar" class="java.lang.String"/>
	<field name="agenda.dataLancamento_Apresentar" class="java.lang.String"/>
	<field name="agenda.dataAgendamentoComparecimento_Apresentar" class="java.lang.String"/>
	<field name="agenda.dataComparecimento_Apresentar" class="java.lang.String"/>
	<field name="agenda.responsavelComparecimento.nomeAbreviado" class="java.lang.String"/>
	<field name="agenda.colaboradorResponsavel.primeiroNomeConcatenado" class="java.lang.String"/>
	<field name="cliente.matricula" class="java.lang.String"/>
	<field name="historicoContatoVO.resultado" class="java.lang.String"/>
	<field name="dataUltimoContato_Apresentar" class="java.lang.String"/>
	<field name="cliente.clienteSituacaoVO_Apresentar.situacaoAtualCliente_Apresentar" class="java.lang.String"/>
	<group name="nomeCliente">
		<groupHeader>
			<band height="15" splitType="Stretch">
				<staticText>
					<reportElement key="staticText-19" x="73" y="0" width="183" height="15"/>
					<textElement>
						<font size="10" isBold="true" pdfFontName="Helvetica-Bold"/>
					</textElement>
					<text><![CDATA[Nome]]></text>
				</staticText>
				<staticText>
					<reportElement key="staticText-19" x="311" y="0" width="106" height="15"/>
					<textElement>
						<font size="10" isBold="true" pdfFontName="Helvetica-Bold"/>
					</textElement>
					<text><![CDATA[Tipo Ag.]]></text>
				</staticText>
				<staticText>
					<reportElement key="staticText-19" x="629" y="0" width="94" height="15"/>
					<textElement>
						<font size="10" isBold="true" pdfFontName="Helvetica-Bold"/>
					</textElement>
					<text><![CDATA[Agendamento]]></text>
				</staticText>
				<staticText>
					<reportElement key="staticText-19" x="827" y="0" width="145" height="15"/>
					<textElement>
						<font size="10" isBold="true" pdfFontName="Helvetica-Bold"/>
					</textElement>
					<text><![CDATA[Resp. Comparecimento]]></text>
				</staticText>
				<staticText>
					<reportElement key="staticText-19" x="1119" y="0" width="126" height="15"/>
					<textElement>
						<font size="10" isBold="true" pdfFontName="Helvetica-Bold"/>
					</textElement>
					<text><![CDATA[Resultado]]></text>
				</staticText>
				<staticText>
					<reportElement key="staticText-19" x="972" y="0" width="147" height="15"/>
					<textElement>
						<font size="10" isBold="true" pdfFontName="Helvetica-Bold"/>
					</textElement>
					<text><![CDATA[Colaborador Responsável]]></text>
				</staticText>
				<staticText>
					<reportElement key="staticText-19" x="723" y="0" width="104" height="15"/>
					<textElement>
						<font size="10" isBold="true" pdfFontName="Helvetica-Bold"/>
					</textElement>
					<text><![CDATA[Comparecimento]]></text>
				</staticText>
				<staticText>
					<reportElement key="staticText-19" x="417" y="0" width="100" height="15"/>
					<textElement>
						<font size="10" isBold="true" pdfFontName="Helvetica-Bold"/>
					</textElement>
					<text><![CDATA[Lançamento]]></text>
				</staticText>
				<staticText>
					<reportElement key="staticText-19" x="0" y="0" width="73" height="15"/>
					<textElement>
						<font size="10" isBold="true" pdfFontName="Helvetica-Bold"/>
					</textElement>
					<text><![CDATA[Matrícula]]></text>
				</staticText>
				<staticText>
					<reportElement key="staticText-19" x="517" y="0" width="112" height="15"/>
					<textElement>
						<font size="10" isBold="true" pdfFontName="Helvetica-Bold"/>
					</textElement>
					<text><![CDATA[Último Contato]]></text>
				</staticText>
				<staticText>
					<reportElement key="staticText-19" x="256" y="0" width="55" height="15"/>
					<textElement>
						<font size="10" isBold="true" pdfFontName="Helvetica-Bold"/>
					</textElement>
					<text><![CDATA[Situação]]></text>
				</staticText>
			</band>
		</groupHeader>
		<groupFooter>
			<band splitType="Stretch"/>
		</groupFooter>
	</group>
	<background>
		<band splitType="Stretch"/>
	</background>
	<title>
		<band splitType="Stretch"/>
	</title>
	<pageHeader>
		<band height="3" splitType="Stretch"/>
	</pageHeader>
	<columnHeader>
		<band splitType="Stretch"/>
	</columnHeader>
	<detail>
		<band height="13" splitType="Stretch">
			<textField>
				<reportElement x="311" y="0" width="106" height="13"/>
				<textElement>
					<font size="8"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{agenda.tipoAgendamento_Apresentar}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="723" y="0" width="104" height="13"/>
				<textElement>
					<font size="8"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{agenda.dataComparecimento_Apresentar}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="972" y="0" width="147" height="13"/>
				<textElement>
					<font size="8"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{agenda.colaboradorResponsavel.primeiroNomeConcatenado}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="0" y="0" width="73" height="13"/>
				<textElement>
					<font size="8"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{cliente.matricula}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="1119" y="0" width="126" height="13"/>
				<textElement>
					<font size="8"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{historicoContatoVO.resultado}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="73" y="0" width="183" height="13"/>
				<textElement>
					<font size="8"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{nomePessoaRel}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="827" y="0" width="145" height="13"/>
				<textElement>
					<font size="8"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{agenda.responsavelComparecimento.nomeAbreviado}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="629" y="0" width="94" height="13"/>
				<textElement>
					<font size="8"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{agenda.dataAgendamentoComparecimento_Apresentar}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="417" y="0" width="100" height="13"/>
				<textElement>
					<font size="8"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{agenda.dataLancamento_Apresentar}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="517" y="0" width="112" height="13"/>
				<textElement>
					<font size="8"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{dataUltimoContato_Apresentar}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="256" y="0" width="55" height="13"/>
				<textElement>
					<font size="8"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{cliente.clienteSituacaoVO_Apresentar.situacaoAtualCliente_Apresentar}]]></textFieldExpression>
			</textField>
		</band>
	</detail>
	<columnFooter>
		<band splitType="Stretch"/>
	</columnFooter>
	<pageFooter>
		<band splitType="Stretch"/>
	</pageFooter>
	<lastPageFooter>
		<band height="19" splitType="Stretch"/>
	</lastPageFooter>
	<summary>
		<band splitType="Stretch"/>
	</summary>
</jasperReport>
