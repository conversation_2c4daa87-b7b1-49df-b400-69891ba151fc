¬í sr (net.sf.jasperreports.engine.JasperReport      'Ø L compileDatat Ljava/io/Serializable;L compileNameSuffixt Ljava/lang/String;L 
compilerClassq ~ xr -net.sf.jasperreports.engine.base.JRBaseReport      'Ø ,I PSEUDO_SERIAL_VERSION_UIDI bottomMarginI columnCountI 
columnSpacingI columnWidthZ ignorePaginationZ isFloatColumnFooterZ isSummaryNewPageZ  isSummaryWithPageHeaderAndFooterZ isTitleNewPageI 
leftMarginB orientationI 
pageHeightI 	pageWidthB 
printOrderI rightMarginI 	topMarginB whenNoDataTypeL 
backgroundt $Lnet/sf/jasperreports/engine/JRBand;L columnDirectiont 3Lnet/sf/jasperreports/engine/type/RunDirectionEnum;L columnFooterq ~ L columnHeaderq ~ [ datasetst ([Lnet/sf/jasperreports/engine/JRDataset;L defaultFontt *Lnet/sf/jasperreports/engine/JRReportFont;L defaultStylet %Lnet/sf/jasperreports/engine/JRStyle;L detailq ~ L 
detailSectiont 'Lnet/sf/jasperreports/engine/JRSection;[ fontst +[Lnet/sf/jasperreports/engine/JRReportFont;L formatFactoryClassq ~ L 
importsSett Ljava/util/Set;L languageq ~ L lastPageFooterq ~ L mainDatasett 'Lnet/sf/jasperreports/engine/JRDataset;L nameq ~ L noDataq ~ L orientationValuet 2Lnet/sf/jasperreports/engine/type/OrientationEnum;L 
pageFooterq ~ L 
pageHeaderq ~ L printOrderValuet 1Lnet/sf/jasperreports/engine/type/PrintOrderEnum;[ stylest &[Lnet/sf/jasperreports/engine/JRStyle;L summaryq ~ [ 	templatest /[Lnet/sf/jasperreports/engine/JRReportTemplate;L titleq ~ L whenNoDataTypeValuet 5Lnet/sf/jasperreports/engine/type/WhenNoDataTypeEnum;xp  wñ            O           p  S        p~r 1net.sf.jasperreports.engine.type.RunDirectionEnum          xr java.lang.Enum          xpt LTRppppppsr .net.sf.jasperreports.engine.base.JRBaseSection      'Ø [ bandst %[Lnet/sf/jasperreports/engine/JRBand;xpur %[Lnet.sf.jasperreports.engine.JRBand;Ý~ìÊ5  xp   sr +net.sf.jasperreports.engine.base.JRBaseBand      'Ø I PSEUDO_SERIAL_VERSION_UIDI heightZ isSplitAllowedL printWhenExpressiont *Lnet/sf/jasperreports/engine/JRExpression;L 	splitTypet Ljava/lang/Byte;L splitTypeValuet 0Lnet/sf/jasperreports/engine/type/SplitTypeEnum;xr 3net.sf.jasperreports.engine.base.JRBaseElementGroup      'Ø L childrent Ljava/util/List;L elementGroupt ,Lnet/sf/jasperreports/engine/JRElementGroup;xpsr java.util.ArrayListxÒÇa I sizexp   Êw   Êsr 0net.sf.jasperreports.engine.base.JRBaseRectangle      'Ø L radiust Ljava/lang/Integer;xr 5net.sf.jasperreports.engine.base.JRBaseGraphicElement      'Ø I PSEUDO_SERIAL_VERSION_UIDL fillq ~ L 	fillValuet +Lnet/sf/jasperreports/engine/type/FillEnum;L linePent #Lnet/sf/jasperreports/engine/JRPen;L penq ~ xr .net.sf.jasperreports.engine.base.JRBaseElement      'Ø I PSEUDO_SERIAL_VERSION_UIDI heightZ isPrintInFirstWholeBandZ isPrintRepeatedValuesZ isPrintWhenDetailOverflowsZ isRemoveLineWhenBlankB positionTypeB stretchTypeI widthI xI yL 	backcolort Ljava/awt/Color;L defaultStyleProvidert 4Lnet/sf/jasperreports/engine/JRDefaultStyleProvider;L elementGroupq ~ "L 	forecolorq ~ ,L keyq ~ L modeq ~ L 	modeValuet +Lnet/sf/jasperreports/engine/type/ModeEnum;L parentStyleq ~ L parentStyleNameReferenceq ~ L positionTypeValuet 3Lnet/sf/jasperreports/engine/type/PositionTypeEnum;L printWhenExpressionq ~ L printWhenGroupChangest %Lnet/sf/jasperreports/engine/JRGroup;L 
propertiesMapt -Lnet/sf/jasperreports/engine/JRPropertiesMap;[ propertyExpressionst 3[Lnet/sf/jasperreports/engine/JRPropertyExpression;L stretchTypeValuet 2Lnet/sf/jasperreports/engine/type/StretchTypeEnum;xp  wñ          ×      ¹pq ~ q ~ #pppppp~r 1net.sf.jasperreports.engine.type.PositionTypeEnum          xq ~ t FIX_RELATIVE_TO_TOPpppp~r 0net.sf.jasperreports.engine.type.StretchTypeEnum          xq ~ t 
NO_STRETCH  wîppsr *net.sf.jasperreports.engine.base.JRBasePen      'Ø I PSEUDO_SERIAL_VERSION_UIDL 	lineColorq ~ ,L 	lineStyleq ~ L lineStyleValuet 0Lnet/sf/jasperreports/engine/type/LineStyleEnum;L 	lineWidtht Ljava/lang/Float;L penContainert ,Lnet/sf/jasperreports/engine/JRPenContainer;xp  wñpppsr java.lang.FloatÚíÉ¢Û<ðì F valuexr java.lang.Number¬à  xp?   q ~ 4ppsq ~ &  wñ             U   ¹pq ~ q ~ #ppppppq ~ 6ppppq ~ 9  wîppsq ~ ;  wñpppsq ~ @?   q ~ Cppsq ~ &  wñ           k  ß   õpq ~ q ~ #ppppppq ~ 6ppppq ~ 9  wîppsq ~ ;  wñpppsq ~ @?   q ~ Fppsq ~ &  wñ          ×      õpq ~ q ~ #ppppppq ~ 6ppppq ~ 9  wîppsq ~ ;  wñpppsq ~ @?   q ~ Ippsq ~ &  wñ              °   õpq ~ q ~ #ppppppq ~ 6ppppq ~ 9  wîppsq ~ ;  wñpppsq ~ @?   q ~ Lppsq ~ &  wñ           %  F   õpq ~ q ~ #ppppppq ~ 6ppppq ~ 9  wîppsq ~ ;  wñpppsq ~ @?   q ~ Oppsq ~ &  wñ   l        ÿ      pq ~ q ~ #ppppppq ~ 6ppppq ~ 9  wîppsq ~ ;  wñpppsq ~ @?   q ~ Rppsq ~ &  wñ   l        Ô  v   pq ~ q ~ #ppppppq ~ 6ppppq ~ 9  wîppsq ~ ;  wñpppsq ~ @?   q ~ Uppsq ~ &  wñ   '        Ô  v   pq ~ q ~ #ppppppq ~ 6ppppq ~ 9  wîppsq ~ ;  wñpppsq ~ @?   q ~ Xppsq ~ &  wñ   #        Ô  v   Npq ~ q ~ #ppppppq ~ 6ppppq ~ 9  wîppsq ~ ;  wñpppsq ~ @?   q ~ [ppsq ~ &  wñ          E      qpq ~ q ~ #ppppppq ~ 6ppppq ~ 9  wîppsq ~ ;  wñpppsq ~ @?   q ~ ^ppsq ~ &  wñ   #        õ  U   qpq ~ q ~ #ppppppq ~ 6ppppq ~ 9  wîppsq ~ ;  wñpppsq ~ @?   q ~ appsq ~ &  wñ          E      pq ~ q ~ #ppppppq ~ 6ppppq ~ 9  wîppsq ~ ;  wñpppsq ~ @?   q ~ dppsq ~ &  wñ          ×      ×pq ~ q ~ #ppppppq ~ 6ppppq ~ 9  wîppsq ~ ;  wñpppsq ~ @?   q ~ gppsq ~ &  wñ             ^   .pq ~ q ~ #ppppppq ~ 6ppppq ~ 9  wîppsq ~ ;  wñpppsq ~ @?   q ~ jppsq ~ &  wñ           ¬   ©   pq ~ q ~ #ppppppq ~ 6ppppq ~ 9  wîppsq ~ ;  wñpppsq ~ @?   q ~ mppsq ~ &  wñ           õ  U   pq ~ q ~ #ppppppq ~ 6ppppq ~ 9  wîppsq ~ ;  wñpppsq ~ @?   q ~ pppsq ~ &  wñ           k  ß   ¹pq ~ q ~ #ppppppq ~ 6ppppq ~ 9  wîppsq ~ ;  wñpppsq ~ @?   q ~ sppsq ~ &  wñ           k  ß   ×pq ~ q ~ #ppppppq ~ 6ppppq ~ 9  wîppsq ~ ;  wñpppsq ~ @?   q ~ vppsq ~ &  wñ          E     Ipq ~ q ~ #ppppppq ~ 6ppppq ~ 9  wîppsq ~ ;  wñpppsq ~ @?   q ~ yppsq ~ &  wñ           \     Ipq ~ q ~ #ppppppq ~ 6ppppq ~ 9  wîppsq ~ ;  wñpppsq ~ @?   q ~ |ppsq ~ &  wñ           8   a  Ipq ~ q ~ #ppppppq ~ 6ppppq ~ 9  wîppsq ~ ;  wñpppsq ~ @?   q ~ ppsq ~ &  wñ           \     Ipq ~ q ~ #ppppppq ~ 6ppppq ~ 9  wîppsq ~ ;  wñpppsq ~ @?   q ~ ppsr 1net.sf.jasperreports.engine.base.JRBaseStaticText      'Ø L textq ~ xr 2net.sf.jasperreports.engine.base.JRBaseTextElement      'Ø %I PSEUDO_SERIAL_VERSION_UIDL borderq ~ L borderColorq ~ ,L bottomBorderq ~ L bottomBorderColorq ~ ,L 
bottomPaddingq ~ 'L fontNameq ~ L fontSizeq ~ 'L horizontalAlignmentq ~ L horizontalAlignmentValuet 6Lnet/sf/jasperreports/engine/type/HorizontalAlignEnum;L isBoldt Ljava/lang/Boolean;L isItalicq ~ L 
isPdfEmbeddedq ~ L isStrikeThroughq ~ L isStyledTextq ~ L isUnderlineq ~ L 
leftBorderq ~ L leftBorderColorq ~ ,L leftPaddingq ~ 'L lineBoxt 'Lnet/sf/jasperreports/engine/JRLineBox;L lineSpacingq ~ L lineSpacingValuet 2Lnet/sf/jasperreports/engine/type/LineSpacingEnum;L markupq ~ L paddingq ~ 'L pdfEncodingq ~ L pdfFontNameq ~ L 
reportFontq ~ L rightBorderq ~ L rightBorderColorq ~ ,L rightPaddingq ~ 'L rotationq ~ L 
rotationValuet /Lnet/sf/jasperreports/engine/type/RotationEnum;L 	topBorderq ~ L topBorderColorq ~ ,L 
topPaddingq ~ 'L verticalAlignmentq ~ L verticalAlignmentValuet 4Lnet/sf/jasperreports/engine/type/VerticalAlignEnum;xq ~ +  wñ   
        i  z   .pq ~ q ~ #pt 
staticText-16ppppq ~ 6ppppq ~ 9  wñppppppsr java.lang.Integerâ ¤÷8 I valuexq ~ A   ppsr java.lang.BooleanÍ rÕúî Z valuexp ppppppppsr .net.sf.jasperreports.engine.base.JRBaseLineBox      'Ø L 
bottomPaddingq ~ 'L 	bottomPent +Lnet/sf/jasperreports/engine/base/JRBoxPen;L boxContainert ,Lnet/sf/jasperreports/engine/JRBoxContainer;L leftPaddingq ~ 'L leftPenq ~ L paddingq ~ 'L penq ~ L rightPaddingq ~ 'L rightPenq ~ L 
topPaddingq ~ 'L topPenq ~ xppsr 3net.sf.jasperreports.engine.base.JRBaseBoxBottomPen      'Ø  xr -net.sf.jasperreports.engine.base.JRBaseBoxPen      'Ø L lineBoxq ~ xq ~ ;  wñppppq ~ q ~ q ~ psr 1net.sf.jasperreports.engine.base.JRBaseBoxLeftPen      'Ø  xq ~   wñppppq ~ q ~ psq ~   wñppppq ~ q ~ psr 2net.sf.jasperreports.engine.base.JRBaseBoxRightPen      'Ø  xq ~   wñppppq ~ q ~ psr 0net.sf.jasperreports.engine.base.JRBaseBoxTopPen      'Ø  xq ~   wñppppq ~ q ~ pppppt 	Helveticapppppppppp~r 2net.sf.jasperreports.engine.type.VerticalAlignEnum          xq ~ t TOPt CHAVE DE ACESSOsq ~   wñ   
        Í  z   Spq ~ q ~ #pt 
staticText-16ppppq ~ 6ppppq ~ 9  wñppppppsq ~    p~r 4net.sf.jasperreports.engine.type.HorizontalAlignEnum          xq ~ t CENTERq ~ ppppppppsq ~ psq ~   wñppppq ~ ¬q ~ ¬q ~ ¦psq ~   wñppppq ~ ¬q ~ ¬psq ~   wñppppq ~ ¬q ~ ¬psq ~   wñppppq ~ ¬q ~ ¬psq ~   wñppppq ~ ¬q ~ ¬pppppt 	Helveticappppppppppq ~ £t 4Consulta de autenticidade no portal nacional da NF-esq ~   wñ           m      rpq ~ q ~ #pt 
staticText-16ppppq ~ 6ppppq ~ 9  wñppppppq ~ ppq ~ ppppppppsq ~ psq ~   wñppppq ~ ¶q ~ ¶q ~ ´psq ~   wñppppq ~ ¶q ~ ¶psq ~   wñppppq ~ ¶q ~ ¶psq ~   wñppppq ~ ¶q ~ ¶psq ~   wñppppq ~ ¶q ~ ¶pppppt 	Helveticappppppppppq ~ £t NATUREZA DA OPERAÃÃOsq ~   wñ           î  Y   rpq ~ q ~ #pt 
staticText-16ppppq ~ 6ppppq ~ 9  wñppppppq ~ ppq ~ ppppppppsq ~ psq ~   wñppppq ~ Àq ~ Àq ~ ¾psq ~   wñppppq ~ Àq ~ Àpsq ~   wñppppq ~ Àq ~ Àpsq ~   wñppppq ~ Àq ~ Àpsq ~   wñppppq ~ Àq ~ Àpppppt 	Helveticappppppppppq ~ £t !PROTOCOLO DE AUTORIZAÃÃO DE USOsq ~   wñ   
       E      ­pq ~ q ~ #pt 
staticText-16ppppq ~ 6ppppq ~ 9  wñppppppq ~ ppsq ~ ppppppppsq ~ psq ~   wñppppq ~ Ëq ~ Ëq ~ Èpsq ~   wñppppq ~ Ëq ~ Ëpsq ~   wñppppq ~ Ëq ~ Ëpsq ~   wñppppq ~ Ëq ~ Ëpsq ~   wñppppq ~ Ëq ~ Ëpppppt 	Helveticapppppppppp~q ~ ¢t MIDDLEt DESTINATÃRIO / REMETENTEsq ~   wñ           r     pq ~ q ~ #pt 
staticText-16ppppq ~ 6ppppq ~ 9  wñppppppsq ~    pq ~ ªq ~ Êppppppppsq ~ psq ~   wñppppq ~ Øq ~ Øq ~ Õpsq ~   wñppppq ~ Øq ~ Øpsq ~   wñppppq ~ Øq ~ Øpsq ~   wñppppq ~ Øq ~ Øpsq ~   wñppppq ~ Øq ~ Øpppppt 	Helveticappppppppppq ~ Òt DANFEsr 0net.sf.jasperreports.engine.base.JRBaseTextField      'Ø I PSEUDO_SERIAL_VERSION_UIDI 
bookmarkLevelB evaluationTimeB hyperlinkTargetB 
hyperlinkTypeZ isStretchWithOverflowL anchorNameExpressionq ~ L evaluationGroupq ~ 0L evaluationTimeValuet 5Lnet/sf/jasperreports/engine/type/EvaluationTimeEnum;L 
expressionq ~ L hyperlinkAnchorExpressionq ~ L hyperlinkPageExpressionq ~ [ hyperlinkParameterst 3[Lnet/sf/jasperreports/engine/JRHyperlinkParameter;L hyperlinkReferenceExpressionq ~ L hyperlinkTooltipExpressionq ~ L isBlankWhenNullq ~ L 
linkTargetq ~ L linkTypeq ~ L patternq ~ xq ~   wñ          Ô  v   <pq ~ q ~ #pt 
textField-212ppppq ~ 6ppppq ~ 9  wñppppppsq ~    pq ~ ªq ~ Êppppppppsq ~ psq ~   wñppppq ~ æq ~ æq ~ ãpsq ~   wñppppq ~ æq ~ æpsq ~   wñppppq ~ æq ~ æpsq ~   wñppppq ~ æq ~ æpsq ~   wñppppq ~ æq ~ æpppppt Helvetica-Boldppppppppppq ~ Ò  wñ        pp~r 3net.sf.jasperreports.engine.type.EvaluationTimeEnum          xq ~ t NOWsr 1net.sf.jasperreports.engine.base.JRBaseExpression      'Ø I id[ chunkst 0[Lnet/sf/jasperreports/engine/JRExpressionChunk;L valueClassNameq ~ L valueClassRealNameq ~ xp   	ur 0[Lnet.sf.jasperreports.engine.JRExpressionChunk;mYÏÞiK£U  xp   sr 6net.sf.jasperreports.engine.base.JRBaseExpressionChunk      'Ø B typeL textq ~ xpt chaveAcessot java.lang.Stringppppppq ~ pppsq ~ à  wñ           î  Y   pq ~ q ~ #pt 
textField-212ppppq ~ 6ppppq ~ 9  wñppppppq ~ ¨pq ~ ªq ~ Êppppppppsq ~ psq ~   wñppppq ~ ûq ~ ûq ~ ùpsq ~   wñppppq ~ ûq ~ ûpsq ~   wñppppq ~ ûq ~ ûpsq ~   wñppppq ~ ûq ~ ûpsq ~   wñppppq ~ ûq ~ ûpppppt Helvetica-Boldppppppppppq ~ Ò  wñ        ppq ~ îsq ~ ð   
uq ~ ó   sq ~ õt protocoloAutorizacaot java.lang.Stringppppppq ~ pppsq ~ à  wñ           ÿ      pq ~ q ~ #pt 
textField-212ppppq ~ 6ppppq ~ 9  wñppppppq ~ pq ~ ªq ~ ppppppppsq ~ psq ~   wñppppq ~	q ~	q ~psq ~   wñppppq ~	q ~	psq ~   wñppppq ~	q ~	psq ~   wñppppq ~	q ~	psq ~   wñppppq ~	q ~	pppppt Helvetica-Boldppppppppppq ~ Ò  wñ        ppq ~ îsq ~ ð   uq ~ ó   sq ~ õt razaoSocialPrestadort java.lang.Stringppppppq ~ pppsq ~ à  wñ           ï  X   pq ~ q ~ #pt 
textField-212ppppq ~ 6ppppq ~ 9  wñppppppq ~ ¨ppq ~ ppppppppsq ~ psq ~   wñppppq ~q ~q ~psq ~   wñppppq ~q ~psq ~   wñppppq ~q ~psq ~   wñppppq ~q ~psq ~   wñppppq ~q ~pppppt Helvetica-Boldppppppppppq ~ Ò  wñ        ppq ~ îsq ~ ð   uq ~ ó   sq ~ õt 
cnpjPrestadort java.lang.Stringppppppq ~ pppsq ~ à  wñ           §   Y   pq ~ q ~ #pt 
textField-212ppppq ~ 6ppppq ~ 9  wñppppppq ~ ppq ~ ppppppppsq ~ psq ~   wñppppq ~%q ~%q ~#psq ~   wñppppq ~%q ~%psq ~   wñppppq ~%q ~%psq ~   wñppppq ~%q ~%psq ~   wñppppq ~%q ~%pppppt Helvetica-Boldppppppppppq ~ Ò  wñ        ppq ~ îsq ~ ð   
uq ~ ó   sq ~ õt enderecoPrestadort java.lang.Stringppppppq ~ pppsq ~ à  wñ          J      Çpq ~ q ~ #pt 
textField-212ppppq ~ 6ppppq ~ 9  wñppppppq ~ ¨ppq ~ ppppppppsq ~ psq ~   wñppppq ~3q ~3q ~1psq ~   wñppppq ~3q ~3psq ~   wñppppq ~3q ~3psq ~   wñppppq ~3q ~3psq ~   wñppppq ~3q ~3pppppt Helvetica-Boldppppppppppq ~ Ò  wñ        ppq ~ îsq ~ ð   uq ~ ó   sq ~ õt razaoSocialTomadort java.lang.Stringppppppq ~ pppsq ~ à  wñ             X   Çpq ~ q ~ #pt 
textField-212ppppq ~ 6ppppq ~ 9  wñppppppq ~ ¨ppq ~ ppppppppsq ~ psq ~   wñppppq ~Aq ~Aq ~?psq ~   wñppppq ~Aq ~Apsq ~   wñppppq ~Aq ~Apsq ~   wñppppq ~Aq ~Apsq ~   wñppppq ~Aq ~Apppppt Helvetica-Boldppppppppppq ~ Ò  wñ        ppq ~ îsq ~ ð   uq ~ ó   sq ~ õt cnpjTomadort java.lang.Stringppppppq ~ pppsq ~ &  wñ          E     pq ~ q ~ #ppppppq ~ 6sq ~ ð   uq ~ ó   sq ~ õt apresentarFaturassq ~ õt .equals(false)t java.lang.Booleanppppq ~ 9  wîppsq ~ ;  wñpppsq ~ @?   q ~Mppsq ~ à  wñ           e  â   åpq ~ q ~ #pt 
textField-212ppppq ~ 6ppppq ~ 9  wñppppppq ~ ¨pq ~ ªq ~ ppppppppsq ~ psq ~   wñppppq ~Yq ~Yq ~Wpsq ~   wñppppq ~Yq ~Ypsq ~   wñppppq ~Yq ~Ypsq ~   wñppppq ~Yq ~Ypsq ~   wñppppq ~Yq ~Ypppppt Helvetica-Boldppppppppppq ~ Ò  wñ        ppq ~ îsq ~ ð   uq ~ ó   sq ~ õt 	dataSaidat java.lang.Stringppppppq ~ pppsq ~ &  wñ           s   õ  pq ~ q ~ #ppppppq ~ 6sq ~ ð   uq ~ ó   sq ~ õt apresentarFaturassq ~ õt .equals(false)q ~Tppppq ~ 9  wîppsq ~ ;  wñpppsq ~ @?   q ~eppsq ~ à  wñ           Ë      åpq ~ q ~ #pt 
textField-212ppppq ~ 6ppppq ~ 9  wñppppppq ~ ¨ppq ~ ppppppppsq ~ psq ~   wñppppq ~pq ~pq ~npsq ~   wñppppq ~pq ~ppsq ~   wñppppq ~pq ~ppsq ~   wñppppq ~pq ~ppsq ~   wñppppq ~pq ~ppppppt Helvetica-Boldppppppppppq ~ Ò  wñ        ppq ~ îsq ~ ð   uq ~ ó   sq ~ õt enderecoTomadort java.lang.Stringppppppq ~ pppsq ~ &  wñ           n  Ü  pq ~ q ~ #ppppppq ~ 6sq ~ ð   uq ~ ó   sq ~ õt apresentarFaturassq ~ õt .equals(false)q ~Tppppq ~ 9  wîppsq ~ ;  wñpppsq ~ @?   q ~|ppsq ~ &  wñ          E     gpq ~ q ~ #ppppppq ~ 6ppppq ~ 9  wîppsq ~ ;  wñpppsq ~ @?   q ~ppsq ~ à  wñ           ¯   Ù   åpq ~ q ~ #pt 
textField-212ppppq ~ 6ppppq ~ 9  wñppppppq ~ ¨ppq ~ ppppppppsq ~ psq ~   wñppppq ~q ~q ~psq ~   wñppppq ~q ~psq ~   wñppppq ~q ~psq ~   wñppppq ~q ~psq ~   wñppppq ~q ~pppppt Helvetica-Boldppppppppppq ~ Ò  wñ        ppq ~ îsq ~ ð   uq ~ ó   sq ~ õt 
bairroTomadort java.lang.Stringppppppq ~ pppsq ~ à  wñ           L     åpq ~ q ~ #pt 
textField-212ppppq ~ 6ppppq ~ 9  wñppppppq ~ ¨ppq ~ ppppppppsq ~ psq ~   wñppppq ~q ~q ~psq ~   wñppppq ~q ~psq ~   wñppppq ~q ~psq ~   wñppppq ~q ~psq ~   wñppppq ~q ~pppppt Helvetica-Boldppppppppppq ~ Ò  wñ        ppq ~ îsq ~ ð   uq ~ ó   sq ~ õt 
cepTomadort java.lang.Stringppppppq ~ pppsq ~ à  wñ              ²  pq ~ q ~ #pt 
textField-212ppppq ~ 6ppppq ~ 9  wñppppppq ~ ¨ppq ~ ppppppppsq ~ psq ~   wñppppq ~¦q ~¦q ~¤psq ~   wñppppq ~¦q ~¦psq ~   wñppppq ~¦q ~¦psq ~   wñppppq ~¦q ~¦psq ~   wñppppq ~¦q ~¦pppppt Helvetica-Boldppppppppppq ~ Ò  wñ        ppq ~ îsq ~ ð   uq ~ ó   sq ~ õt telefoneTomadort java.lang.Stringppppppq ~ pppsr ,net.sf.jasperreports.engine.base.JRBaseImage      'Ø *I PSEUDO_SERIAL_VERSION_UIDI 
bookmarkLevelB evaluationTimeB hyperlinkTargetB 
hyperlinkTypeZ isLazyB onErrorTypeL anchorNameExpressionq ~ L borderq ~ L borderColorq ~ ,L bottomBorderq ~ L bottomBorderColorq ~ ,L 
bottomPaddingq ~ 'L evaluationGroupq ~ 0L evaluationTimeValueq ~ áL 
expressionq ~ L horizontalAlignmentq ~ L horizontalAlignmentValueq ~ L hyperlinkAnchorExpressionq ~ L hyperlinkPageExpressionq ~ [ hyperlinkParametersq ~ âL hyperlinkReferenceExpressionq ~ L hyperlinkTooltipExpressionq ~ L isUsingCacheq ~ L 
leftBorderq ~ L leftBorderColorq ~ ,L leftPaddingq ~ 'L lineBoxq ~ L 
linkTargetq ~ L linkTypeq ~ L onErrorTypeValuet 2Lnet/sf/jasperreports/engine/type/OnErrorTypeEnum;L paddingq ~ 'L rightBorderq ~ L rightBorderColorq ~ ,L rightPaddingq ~ 'L 
scaleImageq ~ L scaleImageValuet 1Lnet/sf/jasperreports/engine/type/ScaleImageEnum;L 	topBorderq ~ L topBorderColorq ~ ,L 
topPaddingq ~ 'L verticalAlignmentq ~ L verticalAlignmentValueq ~ xq ~ (  wñ   P         M   	   pq ~ q ~ #pt image-1ppppq ~ 6ppppq ~ 9  wîppsq ~ ;  wñppppq ~µp  wñ         ppppppp~q ~ ít REPORTsq ~ ð   uq ~ ó   sq ~ õt logomarcaPrestadort java.io.InputStreampp~q ~ ©t LEFTpppppq ~ Êpppsq ~ psq ~   wñsr java.awt.Color¥3u F falphaI valueL cst Ljava/awt/color/ColorSpace;[ 	frgbvaluet [F[ fvalueq ~Åxp    ÿfffpppp~r .net.sf.jasperreports.engine.type.LineStyleEnum          xq ~ t SOLIDsq ~ @?   q ~Áq ~Áq ~µpsq ~   wñsq ~Ã    ÿfffppppq ~Èsq ~ @?   q ~Áq ~Ápsq ~   wñppppq ~Áq ~Ápsq ~   wñsq ~Ã    ÿfffppppq ~Èsq ~ @?   q ~Áq ~Ápsq ~   wñsq ~Ã    ÿfffppppq ~Èsq ~ @?   q ~Áq ~Ápp~r 0net.sf.jasperreports.engine.type.OnErrorTypeEnum          xq ~ t BLANKppppp~r /net.sf.jasperreports.engine.type.ScaleImageEnum          xq ~ t REAL_HEIGHTpppppsq ~ à  wñ           §   Y   %pq ~ q ~ #pt 
textField-212ppppq ~ 6ppppq ~ 9  wñppppppq ~ ppq ~ ppppppppsq ~ psq ~   wñppppq ~Ýq ~Ýq ~Ûpsq ~   wñppppq ~Ýq ~Ýpsq ~   wñppppq ~Ýq ~Ýpsq ~   wñppppq ~Ýq ~Ýpsq ~   wñppppq ~Ýq ~Ýpppppt Helvetica-Boldppppppppppq ~ Ò  wñ        ppq ~ îsq ~ ð   uq ~ ó   sq ~ õt complementoPrestadort java.lang.Stringppppppq ~ pppsq ~   wñ           p     pq ~ q ~ #pt 
staticText-16ppppq ~ 6ppppq ~ 9  wñppppppq ~ ¨pq ~ ªq ~ Êppppppppsq ~ psq ~   wñppppq ~ëq ~ëq ~épsq ~   wñppppq ~ëq ~ëpsq ~   wñppppq ~ëq ~ëpsq ~   wñppppq ~ëq ~ëpsq ~   wñppppq ~ëq ~ëpppppt 	Helveticappppppppppq ~ Òt -Documento Auxiliar da Nota Fiscal EletrÃ´nicasq ~ à  wñ           §   Y   4pq ~ q ~ #pt 
textField-212ppppq ~ 6ppppq ~ 9  wñppppppq ~ ppq ~ ppppppppsq ~ psq ~   wñppppq ~õq ~õq ~ópsq ~   wñppppq ~õq ~õpsq ~   wñppppq ~õq ~õpsq ~   wñppppq ~õq ~õpsq ~   wñppppq ~õq ~õpppppt Helvetica-Boldppppppppppq ~ Ò  wñ        ppq ~ îsq ~ ð   uq ~ ó   sq ~ õt bairroPrestadort java.lang.Stringppppppq ~ pppsq ~ à  wñ           §   Y   Cpq ~ q ~ #pt 
textField-212ppppq ~ 6ppppq ~ 9  wñppppppq ~ ppq ~ ppppppppsq ~ psq ~   wñppppq ~q ~q ~psq ~   wñppppq ~q ~psq ~   wñppppq ~q ~psq ~   wñppppq ~q ~psq ~   wñppppq ~q ~pppppt Helvetica-Boldppppppppppq ~ Ò  wñ        ppq ~ îsq ~ ð   uq ~ ó   sq ~ õt municipioPrestadort java.lang.Stringppppppq ~ pppsq ~ à  wñ           §   Y   Rpq ~ q ~ #pt 
textField-212ppppq ~ 6ppppq ~ 9  wñppppppq ~ ppq ~ ppppppppsq ~ psq ~   wñppppq ~q ~q ~psq ~   wñppppq ~q ~psq ~   wñppppq ~q ~psq ~   wñppppq ~q ~psq ~   wñppppq ~q ~pppppt Helvetica-Boldppppppppppq ~ Ò  wñ        ppq ~ îsq ~ ð   uq ~ ó   sq ~ õt cepPrestadort java.lang.Stringppppppq ~ pppsq ~   wñ   
        L     -pq ~ q ~ #pt 
staticText-16ppppq ~ 6ppppq ~ 9  wñppppppq ~ ppq ~ ppppppppsq ~ psq ~   wñppppq ~q ~q ~psq ~   wñppppq ~q ~psq ~   wñppppq ~q ~psq ~   wñppppq ~q ~psq ~   wñppppq ~q ~pppppt 	Helveticappppppppppq ~ Òt 0 - ENTRADAsq ~   wñ   
        L     9pq ~ q ~ #pt 
staticText-16ppppq ~ 6ppppq ~ 9  wñppppppq ~ ppq ~ ppppppppsq ~ psq ~   wñppppq ~)q ~)q ~'psq ~   wñppppq ~)q ~)psq ~   wñppppq ~)q ~)psq ~   wñppppq ~)q ~)psq ~   wñppppq ~)q ~)pppppt 	Helveticappppppppppq ~ Òt 
1 - SAÃDAsq ~   wñ             _   0pq ~ q ~ #pt 
staticText-16ppppq ~ 6ppppq ~ 9  wñppppppsq ~    pq ~ ªq ~ Êppppppppsq ~ psq ~   wñppppq ~4q ~4q ~1psq ~   wñppppq ~4q ~4psq ~   wñppppq ~4q ~4psq ~   wñppppq ~4q ~4psq ~   wñppppq ~4q ~4pppppt 	Helveticappppppppppq ~ Òt 1sq ~ à  wñ           p     Epq ~ q ~ #pt 
textField-212ppppq ~ 6ppppq ~ 9  wñppppppq ~ ¨ppq ~ Êppppppppsq ~ psq ~   wñppppq ~>q ~>q ~<psq ~   wñppppq ~>q ~>psq ~   wñppppq ~>q ~>psq ~   wñppppq ~>q ~>psq ~   wñppppq ~>q ~>pppppt Helvetica-Boldppppppppppq ~ Ò  wñ        ppq ~ îsq ~ ð   uq ~ ó   sq ~ õt 	"NÂº " + sq ~ õt 
numeroNotat java.lang.Stringppppppq ~ pppsq ~ à  wñ           p     Rpq ~ q ~ #pt 
textField-212ppppq ~ 6ppppq ~ 9  wñppppppq ~ ¨ppq ~ Êppppppppsq ~ psq ~   wñppppq ~Nq ~Nq ~Lpsq ~   wñppppq ~Nq ~Npsq ~   wñppppq ~Nq ~Npsq ~   wñppppq ~Nq ~Npsq ~   wñppppq ~Nq ~Npppppt Helvetica-Boldppppppppppq ~ Ò  wñ        ppq ~ îsq ~ ð   uq ~ ó   sq ~ õt "SÃRIE " + sq ~ õt 	serieNotat java.lang.Stringppppppq ~ pppsq ~   wñ   
        Í  z   apq ~ q ~ #pt 
staticText-16ppppq ~ 6ppppq ~ 9  wñppppppq ~ ¨pq ~ ªq ~ ppppppppsq ~ psq ~   wñppppq ~^q ~^q ~\psq ~   wñppppq ~^q ~^psq ~   wñppppq ~^q ~^psq ~   wñppppq ~^q ~^psq ~   wñppppq ~^q ~^pppppt 	Helveticappppppppppq ~ £t >www.nfe.fazenda.gov.br/portal ou no site da Sefaz Autorizadorasq ~ à  wñ          I      pq ~ q ~ #pt 
textField-212ppppq ~ 6ppppq ~ 9  wñppppppq ~ ¨ppq ~ ppppppppsq ~ psq ~   wñppppq ~hq ~hq ~fpsq ~   wñppppq ~hq ~hpsq ~   wñppppq ~hq ~hpsq ~   wñppppq ~hq ~hpsq ~   wñppppq ~hq ~hpppppt Helvetica-Boldppppppppppq ~ Ò  wñ        ppq ~ îsq ~ ð   uq ~ ó   sq ~ õt naturezaOperacaot java.lang.Stringppppppq ~ pppsq ~   wñ           m      pq ~ q ~ #pt 
staticText-16ppppq ~ 6ppppq ~ 9  wñppppppq ~ ppq ~ ppppppppsq ~ psq ~   wñppppq ~vq ~vq ~tpsq ~   wñppppq ~vq ~vpsq ~   wñppppq ~vq ~vpsq ~   wñppppq ~vq ~vpsq ~   wñppppq ~vq ~vpppppt 	Helveticappppppppppq ~ £t INSCRIÃÃO ESTADUALsq ~   wñ           ª   «   pq ~ q ~ #pt 
staticText-16ppppq ~ 6ppppq ~ 9  wñppppppq ~ ppq ~ ppppppppsq ~ psq ~   wñppppq ~q ~q ~~psq ~   wñppppq ~q ~psq ~   wñppppq ~q ~psq ~   wñppppq ~q ~psq ~   wñppppq ~q ~pppppt 	Helveticappppppppppq ~ £t $INSC. ESTADUAL DO SUBST. TRIBUTÃRIOsq ~   wñ           ª  X   pq ~ q ~ #pt 
staticText-16ppppq ~ 6ppppq ~ 9  wñppppppq ~ ppq ~ ppppppppsq ~ psq ~   wñppppq ~q ~q ~psq ~   wñppppq ~q ~psq ~   wñppppq ~q ~psq ~   wñppppq ~q ~psq ~   wñppppq ~q ~pppppt 	Helveticappppppppppq ~ £t CNPJsq ~ à  wñ                  pq ~ q ~ #pt 
textField-212ppppq ~ 6ppppq ~ 9  wñppppppq ~ ¨ppq ~ ppppppppsq ~ psq ~   wñppppq ~q ~q ~psq ~   wñppppq ~q ~psq ~   wñppppq ~q ~psq ~   wñppppq ~q ~psq ~   wñppppq ~q ~pppppt Helvetica-Boldppppppppppq ~ Ò  wñ        ppq ~ îsq ~ ð    uq ~ ó   sq ~ õt inscricaoEstadualPrestadort java.lang.Stringppppppq ~ pppsq ~   wñ           m      ºpq ~ q ~ #pt 
staticText-16ppppq ~ 6ppppq ~ 9  wñppppppq ~ ppq ~ ppppppppsq ~ psq ~   wñppppq ~¢q ~¢q ~ psq ~   wñppppq ~¢q ~¢psq ~   wñppppq ~¢q ~¢psq ~   wñppppq ~¢q ~¢psq ~   wñppppq ~¢q ~¢pppppt 	Helveticappppppppppq ~ £t NOME / RAZÃO SOCIALsq ~   wñ           m  X   ºpq ~ q ~ #pt 
staticText-16ppppq ~ 6ppppq ~ 9  wñppppppq ~ ppq ~ ppppppppsq ~ psq ~   wñppppq ~¬q ~¬q ~ªpsq ~   wñppppq ~¬q ~¬psq ~   wñppppq ~¬q ~¬psq ~   wñppppq ~¬q ~¬psq ~   wñppppq ~¬q ~¬pppppt 	Helveticappppppppppq ~ £t 
CNPJ / CPFsq ~ &  wñ           n   õ  Ipq ~ q ~ #ppppppq ~ 6ppppq ~ 9  wîppsq ~ ;  wñpppsq ~ @?   q ~´ppsq ~   wñ           ]  â   ºpq ~ q ~ #pt 
staticText-16ppppq ~ 6ppppq ~ 9  wñppppppq ~ ppq ~ ppppppppsq ~ psq ~   wñppppq ~¹q ~¹q ~·psq ~   wñppppq ~¹q ~¹psq ~   wñppppq ~¹q ~¹psq ~   wñppppq ~¹q ~¹psq ~   wñppppq ~¹q ~¹pppppt 	Helveticappppppppppq ~ £t DATA DA EMISSÃOsq ~ à  wñ           e  â   Çpq ~ q ~ #pt 
textField-212ppppq ~ 6ppppq ~ 9  wñppppppq ~ ¨pq ~ ªq ~ ppppppppsq ~ psq ~   wñppppq ~Ãq ~Ãq ~Ápsq ~   wñppppq ~Ãq ~Ãpsq ~   wñppppq ~Ãq ~Ãpsq ~   wñppppq ~Ãq ~Ãpsq ~   wñppppq ~Ãq ~Ãpppppt Helvetica-Boldppppppppppq ~ Ò  wñ        ppq ~ îsq ~ ð   !uq ~ ó   sq ~ õt dataEmissaot java.lang.Stringppppppq ~ pppsq ~   wñ           e  â   Øpq ~ q ~ #pt 
staticText-16ppppq ~ 6ppppq ~ 9  wñppppppq ~ ppq ~ ppppppppsq ~ psq ~   wñppppq ~Ñq ~Ñq ~Ïpsq ~   wñppppq ~Ñq ~Ñpsq ~   wñppppq ~Ñq ~Ñpsq ~   wñppppq ~Ñq ~Ñpsq ~   wñppppq ~Ñq ~Ñpppppt 	Helveticappppppppppq ~ £t DATA DA SAIDA / ENTRADAsq ~   wñ           m      Øpq ~ q ~ #pt 
staticText-16ppppq ~ 6ppppq ~ 9  wñppppppq ~ ppq ~ ppppppppsq ~ psq ~   wñppppq ~Ûq ~Ûq ~Ùpsq ~   wñppppq ~Ûq ~Ûpsq ~   wñppppq ~Ûq ~Ûpsq ~   wñppppq ~Ûq ~Ûpsq ~   wñppppq ~Ûq ~Ûpppppt 	Helveticappppppppppq ~ £t 	ENDEREÃOsq ~   wñ           m   Ù   Øpq ~ q ~ #pt 
staticText-16ppppq ~ 6ppppq ~ 9  wñppppppq ~ ppq ~ ppppppppsq ~ psq ~   wñppppq ~åq ~åq ~ãpsq ~   wñppppq ~åq ~åpsq ~   wñppppq ~åq ~åpsq ~   wñppppq ~åq ~åpsq ~   wñppppq ~åq ~åpppppt 	Helveticappppppppppq ~ £t BAIRRO / DISTRITOsq ~   wñ           :     Øpq ~ q ~ #pt 
staticText-16ppppq ~ 6ppppq ~ 9  wñppppppq ~ ppq ~ ppppppppsq ~ psq ~   wñppppq ~ïq ~ïq ~ípsq ~   wñppppq ~ïq ~ïpsq ~   wñppppq ~ïq ~ïpsq ~   wñppppq ~ïq ~ïpsq ~   wñppppq ~ïq ~ïpppppt 	Helveticappppppppppq ~ £t CEPsq ~   wñ           m      õpq ~ q ~ #pt 
staticText-16ppppq ~ 6ppppq ~ 9  wñppppppq ~ ppq ~ ppppppppsq ~ psq ~   wñppppq ~ùq ~ùq ~÷psq ~   wñppppq ~ùq ~ùpsq ~   wñppppq ~ùq ~ùpsq ~   wñppppq ~ùq ~ùpsq ~   wñppppq ~ùq ~ùpppppt 	Helveticappppppppppq ~ £t 
MUNICÃPIOsq ~   wñ           m   ²   õpq ~ q ~ #pt 
staticText-16ppppq ~ 6ppppq ~ 9  wñppppppq ~ ppq ~ ppppppppsq ~ psq ~   wñppppq ~q ~q ~psq ~   wñppppq ~q ~psq ~   wñppppq ~q ~psq ~   wñppppq ~q ~psq ~   wñppppq ~q ~pppppt 	Helveticappppppppppq ~ £t 
FONE / FAXsq ~   wñ             H   õpq ~ q ~ #pt 
staticText-16ppppq ~ 6ppppq ~ 9  wñppppppq ~ ppq ~ ppppppppsq ~ psq ~   wñppppq ~
q ~
q ~psq ~   wñppppq ~
q ~
psq ~   wñppppq ~
q ~
psq ~   wñppppq ~
q ~
psq ~   wñppppq ~
q ~
pppppt 	Helveticappppppppppq ~ £t UFsq ~   wñ           m  m   õpq ~ q ~ #pt 
staticText-16ppppq ~ 6ppppq ~ 9  wñppppppq ~ ppq ~ ppppppppsq ~ psq ~   wñppppq ~q ~q ~psq ~   wñppppq ~q ~psq ~   wñppppq ~q ~psq ~   wñppppq ~q ~psq ~   wñppppq ~q ~pppppt 	Helveticappppppppppq ~ £t INSCRIÃÃO ESTADUALsq ~ à  wñ           ¦     pq ~ q ~ #pt 
textField-212ppppq ~ 6ppppq ~ 9  wñppppppq ~ ¨ppq ~ ppppppppsq ~ psq ~   wñppppq ~!q ~!q ~psq ~   wñppppq ~!q ~!psq ~   wñppppq ~!q ~!psq ~   wñppppq ~!q ~!psq ~   wñppppq ~!q ~!pppppt Helvetica-Boldppppppppppq ~ Ò  wñ        ppq ~ îsq ~ ð   "uq ~ ó   sq ~ õt municipioTomadort java.lang.Stringppppppq ~ pppsq ~ à  wñ           !  H  pq ~ q ~ #pt 
textField-212ppppq ~ 6ppppq ~ 9  wñppppppq ~ ¨pq ~ ªq ~ ppppppppsq ~ psq ~   wñppppq ~/q ~/q ~-psq ~   wñppppq ~/q ~/psq ~   wñppppq ~/q ~/psq ~   wñppppq ~/q ~/psq ~   wñppppq ~/q ~/pppppt Helvetica-Boldppppppppppq ~ Ò  wñ        ppq ~ îsq ~ ð   #uq ~ ó   sq ~ õt 	ufTomadort java.lang.Stringppppppq ~ pppsq ~ à  wñ           n  l  pq ~ q ~ #pt 
textField-212ppppq ~ 6ppppq ~ 9  wñppppppq ~ ¨ppq ~ ppppppppsq ~ psq ~   wñppppq ~=q ~=q ~;psq ~   wñppppq ~=q ~=psq ~   wñppppq ~=q ~=psq ~   wñppppq ~=q ~=psq ~   wñppppq ~=q ~=pppppt Helvetica-Boldppppppppppq ~ Ò  wñ        ppq ~ îsq ~ ð   $uq ~ ó   sq ~ õt inscricaoEstadualTomadort java.lang.Stringppppppq ~ pppsq ~   wñ           e  â   õpq ~ q ~ #pt 
staticText-16ppppq ~ 6ppppq ~ 9  wñppppppq ~ ppq ~ ppppppppsq ~ psq ~   wñppppq ~Kq ~Kq ~Ipsq ~   wñppppq ~Kq ~Kpsq ~   wñppppq ~Kq ~Kpsq ~   wñppppq ~Kq ~Kpsq ~   wñppppq ~Kq ~Kpppppt 	Helveticappppppppppq ~ £t HORA DE SAÃDAsq ~ &  wñ           :   §  gpq ~ q ~ #ppppppq ~ 6ppppq ~ 9  wîppsq ~ ;  wñpppsq ~ @?   q ~Sppsq ~ &  wñ           y  c  Ipq ~ q ~ #ppppppq ~ 6ppppq ~ 9  wîppsq ~ ;  wñpppsq ~ @?   q ~Vppsq ~ &  wñ           F   a  gpq ~ q ~ #ppppppq ~ 6ppppq ~ 9  wîppsq ~ ;  wñpppsq ~ @?   q ~Yppsq ~ &  wñ           r   á  gpq ~ q ~ #ppppppq ~ 6ppppq ~ 9  wîppsq ~ ;  wñpppsq ~ @?   q ~\ppsq ~ &  wñ           H  S  gpq ~ q ~ #ppppppq ~ 6ppppq ~ 9  wîppsq ~ ;  wñpppsq ~ @?   q ~_ppsq ~ &  wñ           Q    gpq ~ q ~ #ppppppq ~ 6ppppq ~ 9  wîppsq ~ ;  wñpppsq ~ @?   q ~bppsq ~ à  wñ           e  â  pq ~ q ~ #pt 
textField-212ppppq ~ 6ppppq ~ 9  wñppppppq ~ ¨pq ~ ªq ~ ppppppppsq ~ psq ~   wñppppq ~gq ~gq ~epsq ~   wñppppq ~gq ~gpsq ~   wñppppq ~gq ~gpsq ~   wñppppq ~gq ~gpsq ~   wñppppq ~gq ~gpppppt Helvetica-Boldppppppppppq ~ Ò  wñ        ppq ~ îsq ~ ð   %uq ~ ó   sq ~ õt 	horaSaidat java.lang.Stringppppppq ~ pppsq ~   wñ   
       E     pq ~ q ~ #pt 
staticText-16ppppq ~ 6ppppq ~ 9  wñppppppq ~ ppq ~ Êppppppppsq ~ psq ~   wñppppq ~uq ~uq ~spsq ~   wñppppq ~uq ~upsq ~   wñppppq ~uq ~upsq ~   wñppppq ~uq ~upsq ~   wñppppq ~uq ~upppppt 	Helveticappppppppppq ~ Òt FATURA / DUPLICATASsq ~   wñ   
       E     =pq ~ q ~ #pt 
staticText-16ppppq ~ 6ppppq ~ 9  wñppppppq ~ ppq ~ Êppppppppsq ~ psq ~   wñppppq ~q ~q ~}psq ~   wñppppq ~q ~psq ~   wñppppq ~q ~psq ~   wñppppq ~q ~psq ~   wñppppq ~q ~pppppt 	Helveticappppppppppq ~ Òt CÃLCULO DO IMPOSTOsq ~   wñ           Z     Jpq ~ q ~ #pt 
staticText-16ppppq ~ 6ppppq ~ 9  wñppppppq ~ åppq ~ ppppppppsq ~ psq ~   wñppppq ~q ~q ~psq ~   wñppppq ~q ~psq ~   wñppppq ~q ~psq ~   wñppppq ~q ~psq ~   wñppppq ~q ~pppppt 	Helveticappppppppppq ~ £t BASE DE CÃLCULO DO ICMSsq ~   wñ           6   c  Jpq ~ q ~ #pt 
staticText-16ppppq ~ 6ppppq ~ 9  wñppppppq ~ åppq ~ ppppppppsq ~ psq ~   wñppppq ~q ~q ~psq ~   wñppppq ~q ~psq ~   wñppppq ~q ~psq ~   wñppppq ~q ~psq ~   wñppppq ~q ~pppppt 	Helveticappppppppppq ~ £t 
VALOR DO ICMSsq ~   wñ           Z     Jpq ~ q ~ #pt 
staticText-16ppppq ~ 6ppppq ~ 9  wñppppppq ~ åppq ~ ppppppppsq ~ psq ~   wñppppq ~q ~q ~psq ~   wñppppq ~q ~psq ~   wñppppq ~q ~psq ~   wñppppq ~q ~psq ~   wñppppq ~q ~pppppt 	Helveticappppppppppq ~ £t BASE CÃLC. DE ICMS SUBS.sq ~   wñ           j   ù  Jpq ~ q ~ #pt 
staticText-16ppppq ~ 6ppppq ~ 9  wñppppppq ~ åppq ~ ppppppppsq ~ psq ~   wñppppq ~§q ~§q ~¥psq ~   wñppppq ~§q ~§psq ~   wñppppq ~§q ~§psq ~   wñppppq ~§q ~§psq ~   wñppppq ~§q ~§pppppt 	Helveticappppppppppq ~ £t VALOR DO ICMS SUBSTITUIÃÃO sq ~   wñ           t  f  Jpq ~ q ~ #pt 
staticText-16ppppq ~ 6ppppq ~ 9  wñppppppq ~ åppq ~ ppppppppsq ~ psq ~   wñppppq ~±q ~±q ~¯psq ~   wñppppq ~±q ~±psq ~   wñppppq ~±q ~±psq ~   wñppppq ~±q ~±psq ~   wñppppq ~±q ~±pppppt 	Helveticappppppppppq ~ £t VALOR APROXIMADO DOS TRIBUTOSsq ~   wñ           l  Þ  Jpq ~ q ~ #pt 
staticText-16ppppq ~ 6ppppq ~ 9  wñppppppq ~ åppq ~ ppppppppsq ~ psq ~   wñppppq ~»q ~»q ~¹psq ~   wñppppq ~»q ~»psq ~   wñppppq ~»q ~»psq ~   wñppppq ~»q ~»psq ~   wñppppq ~»q ~»pppppt 	Helveticappppppppppq ~ £t VALOR TOTAL DOS PRODUTOSsq ~   wñ           R     hpq ~ q ~ #pt 
staticText-16ppppq ~ 6ppppq ~ 9  wñppppppq ~ åppq ~ ppppppppsq ~ psq ~   wñppppq ~Åq ~Åq ~Ãpsq ~   wñppppq ~Åq ~Åpsq ~   wñppppq ~Åq ~Åpsq ~   wñppppq ~Åq ~Åpsq ~   wñppppq ~Åq ~Åpppppt 	Helveticappppppppppq ~ £t VALOR DO FRETEsq ~   wñ           D   c  hpq ~ q ~ #pt 
staticText-16ppppq ~ 6ppppq ~ 9  wñppppppq ~ åppq ~ ppppppppsq ~ psq ~   wñppppq ~Ïq ~Ïq ~Ípsq ~   wñppppq ~Ïq ~Ïpsq ~   wñppppq ~Ïq ~Ïpsq ~   wñppppq ~Ïq ~Ïpsq ~   wñppppq ~Ïq ~Ïpppppt 	Helveticappppppppppq ~ £t VALOR DO SEGURO sq ~   wñ           0   ©  hpq ~ q ~ #pt 
staticText-16ppppq ~ 6ppppq ~ 9  wñppppppq ~ åppq ~ ppppppppsq ~ psq ~   wñppppq ~Ùq ~Ùq ~×psq ~   wñppppq ~Ùq ~Ùpsq ~   wñppppq ~Ùq ~Ùpsq ~   wñppppq ~Ùq ~Ùpsq ~   wñppppq ~Ùq ~Ùpppppt 	Helveticappppppppppq ~ £t DESCONTOsq ~   wñ           o   ä  hpq ~ q ~ #pt 
staticText-16ppppq ~ 6ppppq ~ 9  wñppppppq ~ åppq ~ ppppppppsq ~ psq ~   wñppppq ~ãq ~ãq ~ápsq ~   wñppppq ~ãq ~ãpsq ~   wñppppq ~ãq ~ãpsq ~   wñppppq ~ãq ~ãpsq ~   wñppppq ~ãq ~ãpppppt 	Helveticappppppppppq ~ £t OUTRAS DESPESAS ACESSÃRIASsq ~   wñ           D  U  hpq ~ q ~ #pt 
staticText-16ppppq ~ 6ppppq ~ 9  wñppppppq ~ åppq ~ ppppppppsq ~ psq ~   wñppppq ~íq ~íq ~ëpsq ~   wñppppq ~íq ~ípsq ~   wñppppq ~íq ~ípsq ~   wñppppq ~íq ~ípsq ~   wñppppq ~íq ~ípppppt 	Helveticappppppppppq ~ £t VALOR DO IPIsq ~   wñ           D    hpq ~ q ~ #pt 
staticText-16ppppq ~ 6ppppq ~ 9  wñppppppq ~ åppq ~ ppppppppsq ~ psq ~   wñppppq ~÷q ~÷q ~õpsq ~   wñppppq ~÷q ~÷psq ~   wñppppq ~÷q ~÷psq ~   wñppppq ~÷q ~÷psq ~   wñppppq ~÷q ~÷pppppt 	Helveticappppppppppq ~ £t 
VALOR DO IRRFsq ~   wñ           X  ï  hpq ~ q ~ #pt 
staticText-16ppppq ~ 6ppppq ~ 9  wñppppppq ~ åppq ~ ppppppppsq ~ psq ~   wñppppq ~q ~q ~ÿpsq ~   wñppppq ~q ~psq ~   wñppppq ~q ~psq ~   wñppppq ~q ~psq ~   wñppppq ~q ~pppppt 	Helveticappppppppppq ~ £t VALOR TOTAL DA NOTAsq ~   wñ   
       E     pq ~ q ~ #pt 
staticText-16ppppq ~ 6ppppq ~ 9  wñppppppq ~ ppq ~ Êppppppppsq ~ psq ~   wñppppq ~q ~q ~	psq ~   wñppppq ~q ~psq ~   wñppppq ~q ~psq ~   wñppppq ~q ~psq ~   wñppppq ~q ~pppppt 	Helveticappppppppppq ~ Òt %TRANSPORTADOR / VOLUMES TRANSPORTADOSsq ~ &  wñ          E     pq ~ q ~ #ppppppq ~ 6ppppq ~ 9  wîppsq ~ ;  wñpppsq ~ @?   q ~ppsq ~ &  wñ          E     «pq ~ q ~ #ppppppq ~ 6ppppq ~ 9  wîppsq ~ ;  wñpppsq ~ @?   q ~ppsq ~ &  wñ          E     Äpq ~ q ~ #ppppppq ~ 6ppppq ~ 9  wîppsq ~ ;  wñpppsq ~ @?   q ~ppsq ~   wñ   
       E     Ýpq ~ q ~ #pt 
staticText-16ppppq ~ 6ppppq ~ 9  wñppppppq ~ ppq ~ Êppppppppsq ~ psq ~   wñppppq ~q ~q ~psq ~   wñppppq ~q ~psq ~   wñppppq ~q ~psq ~   wñppppq ~q ~psq ~   wñppppq ~q ~pppppt 	Helveticappppppppppq ~ Òt LOCAL DE ENTREGAsq ~ &  wñ          E     épq ~ q ~ #ppppppq ~ 6ppppq ~ 9  wîppsq ~ ;  wñpppsq ~ @?   q ~&ppsq ~   wñ   
       E     pq ~ q ~ #pt 
staticText-16ppppq ~ 6ppppq ~ 9  wñppppppq ~ ppq ~ Êppppppppsq ~ psq ~   wñppppq ~+q ~+q ~)psq ~   wñppppq ~+q ~+psq ~   wñppppq ~+q ~+psq ~   wñppppq ~+q ~+psq ~   wñppppq ~+q ~+pppppt 	Helveticappppppppppq ~ Òt DADOS DO PRODUTO / SERVIÃOSsq ~ &  wñ          E     pq ~ q ~ #ppppppq ~ 6ppppq ~ 9  wîppsq ~ ;  wñpppsq ~ @?   q ~3ppsq ~ &  wñ           V  ô  pq ~ q ~ #ppppppq ~ 6ppppq ~ 9  wîppsq ~ ;  wñpppsq ~ @?   q ~6ppsq ~ &  wñ           ,  ô  !pq ~ q ~ #ppppppq ~ 6ppppq ~ 9  wîppsq ~ ;  wñpppsq ~ @?   q ~9ppsq ~ &  wñ           *     !pq ~ q ~ #ppppppq ~ 6ppppq ~ 9  wîppsq ~ ;  wñpppsq ~ @?   q ~<ppsq ~   wñ           V  ô  pq ~ q ~ #pt 
staticText-16ppppq ~ 6ppppq ~ 9  wñppppppq ~ åpq ~ ªq ~ ppppppppsq ~ psq ~   wñppppq ~Aq ~Aq ~?psq ~   wñppppq ~Aq ~Apsq ~   wñppppq ~Aq ~Apsq ~   wñppppq ~Aq ~Apsq ~   wñppppq ~Aq ~Apppppt 	Helveticappppppppppq ~ Òt 
ALÃ­QUOTASsq ~   wñ           ,  ô  #pq ~ q ~ #pt 
staticText-16ppppq ~ 6ppppq ~ 9  wñppppppq ~ åpq ~ ªq ~ ppppppppsq ~ psq ~   wñppppq ~Kq ~Kq ~Ipsq ~   wñppppq ~Kq ~Kpsq ~   wñppppq ~Kq ~Kpsq ~   wñppppq ~Kq ~Kpsq ~   wñppppq ~Kq ~Kpppppt 	Helveticappppppppppq ~ Òt ICMSsq ~   wñ           *     #pq ~ q ~ #pt 
staticText-16ppppq ~ 6ppppq ~ 9  wñppppppq ~ åpq ~ ªq ~ ppppppppsq ~ psq ~   wñppppq ~Uq ~Uq ~Spsq ~   wñppppq ~Uq ~Upsq ~   wñppppq ~Uq ~Upsq ~   wñppppq ~Uq ~Upsq ~   wñppppq ~Uq ~Upppppt 	Helveticappppppppppq ~ Òt IPIsq ~ &  wñ                pq ~ q ~ #ppppppq ~ 6ppppq ~ 9  wîppsq ~ ;  wñpppsq ~ @?   q ~]ppsq ~ &  wñ                pq ~ q ~ #ppppppq ~ 6ppppq ~ 9  wîppsq ~ ;  wñpppsq ~ @?   q ~`ppsq ~   wñ                pq ~ q ~ #pt 
staticText-16ppppq ~ 6ppppq ~ 9  wñppppppq ~ åpq ~ ªq ~ ppppppppsq ~ psq ~   wñppppq ~eq ~eq ~cpsq ~   wñppppq ~eq ~epsq ~   wñppppq ~eq ~epsq ~   wñppppq ~eq ~epsq ~   wñppppq ~eq ~epppppt 	Helveticappppppppppq ~ Òt CÃD. PROD.sq ~   wñ                pq ~ q ~ #pt 
staticText-16ppppq ~ 6ppppq ~ 9  wñppppppq ~ åpq ~ ªq ~ ppppppppsq ~ psq ~   wñppppq ~oq ~oq ~mpsq ~   wñppppq ~oq ~opsq ~   wñppppq ~oq ~opsq ~   wñppppq ~oq ~opsq ~   wñppppq ~oq ~opppppt 	Helveticappppppppppq ~ Òt #DESCRIÃÃO DO PRODUTO / SERVIÃOS sq ~ &  wñ              È  pq ~ q ~ #ppppppq ~ 6ppppq ~ 9  wîppsq ~ ;  wñpppsq ~ @?   q ~wppsq ~   wñ               §  pq ~ q ~ #pt 
staticText-16ppppq ~ 6ppppq ~ 9  wñppppppq ~ åpq ~ ªq ~ ppppppppsq ~ psq ~   wñppppq ~|q ~|q ~zpsq ~   wñppppq ~|q ~|psq ~   wñppppq ~|q ~|psq ~   wñppppq ~|q ~|psq ~   wñppppq ~|q ~|pppppt 	Helveticappppppppppq ~ Òt NCM / SHsq ~   wñ              È  pq ~ q ~ #pt 
staticText-16ppppq ~ 6ppppq ~ 9  wñppppppq ~ åpq ~ ªq ~ ppppppppsq ~ psq ~   wñppppq ~q ~q ~psq ~   wñppppq ~q ~psq ~   wñppppq ~q ~psq ~   wñppppq ~q ~psq ~   wñppppq ~q ~pppppt 	Helveticappppppppppq ~ Òt CSTsq ~ &  wñ              à  pq ~ q ~ #ppppppq ~ 6ppppq ~ 9  wîppsq ~ ;  wñpppsq ~ @?   q ~ppsq ~   wñ              à  pq ~ q ~ #pt 
staticText-16ppppq ~ 6ppppq ~ 9  wñppppppq ~ åpq ~ ªq ~ ppppppppsq ~ psq ~   wñppppq ~q ~q ~psq ~   wñppppq ~q ~psq ~   wñppppq ~q ~psq ~   wñppppq ~q ~psq ~   wñppppq ~q ~pppppt 	Helveticappppppppppq ~ Òt CFOPsq ~ &  wñ              ù  pq ~ q ~ #ppppppq ~ 6ppppq ~ 9  wîppsq ~ ;  wñpppsq ~ @?   q ~ppsq ~   wñ              ù  pq ~ q ~ #pt 
staticText-16ppppq ~ 6ppppq ~ 9  wñppppppq ~ åpq ~ ªq ~ ppppppppsq ~ psq ~   wñppppq ~ q ~ q ~psq ~   wñppppq ~ q ~ psq ~   wñppppq ~ q ~ psq ~   wñppppq ~ q ~ psq ~   wñppppq ~ q ~ pppppt 	Helveticappppppppppq ~ Òt UNID.sq ~ &  wñ               pq ~ q ~ #ppppppq ~ 6ppppq ~ 9  wîppsq ~ ;  wñpppsq ~ @?   q ~¨ppsq ~   wñ               pq ~ q ~ #pt 
staticText-16ppppq ~ 6ppppq ~ 9  wñppppppq ~ åpq ~ ªq ~ ppppppppsq ~ psq ~   wñppppq ~­q ~­q ~«psq ~   wñppppq ~­q ~­psq ~   wñppppq ~­q ~­psq ~   wñppppq ~­q ~­psq ~   wñppppq ~­q ~­pppppt 	Helveticappppppppppq ~ Òt QTD.sq ~ &  wñ           -  1  pq ~ q ~ #ppppppq ~ 6ppppq ~ 9  wîppsq ~ ;  wñpppsq ~ @?   q ~µppsq ~   wñ           -  2  pq ~ q ~ #pt 
staticText-16ppppq ~ 6ppppq ~ 9  wñppppppq ~ åpq ~ ªq ~ ppppppppsq ~ psq ~   wñppppq ~ºq ~ºq ~¸psq ~   wñppppq ~ºq ~ºpsq ~   wñppppq ~ºq ~ºpsq ~   wñppppq ~ºq ~ºpsq ~   wñppppq ~ºq ~ºpppppt 	Helveticappppppppppq ~ Òt V. UNITÃRIOsq ~ &  wñ           -  ]  pq ~ q ~ #ppppppq ~ 6ppppq ~ 9  wîppsq ~ ;  wñpppsq ~ @?   q ~Âppsq ~   wñ           -  ]  pq ~ q ~ #pt 
staticText-16ppppq ~ 6ppppq ~ 9  wñppppppq ~ åpq ~ ªq ~ ppppppppsq ~ psq ~   wñppppq ~Çq ~Çq ~Åpsq ~   wñppppq ~Çq ~Çpsq ~   wñppppq ~Çq ~Çpsq ~   wñppppq ~Çq ~Çpsq ~   wñppppq ~Çq ~Çpppppt 	Helveticappppppppppq ~ Òt V. TOTALsq ~ &  wñ           $    pq ~ q ~ #ppppppq ~ 6ppppq ~ 9  wîppsq ~ ;  wñpppsq ~ @?   q ~Ïppsq ~   wñ           #    pq ~ q ~ #pt 
staticText-16ppppq ~ 6ppppq ~ 9  wñppppppq ~ åpq ~ ªq ~ ppppppppsq ~ psq ~   wñppppq ~Ôq ~Ôq ~Òpsq ~   wñppppq ~Ôq ~Ôpsq ~   wñppppq ~Ôq ~Ôpsq ~   wñppppq ~Ôq ~Ôpsq ~   wñppppq ~Ôq ~Ôpppppt 	Helveticappppppppppq ~ Òt BC ICMSsq ~ &  wñ           $  ®  pq ~ q ~ #ppppppq ~ 6ppppq ~ 9  wîppsq ~ ;  wñpppsq ~ @?   q ~Üppsq ~   wñ           #  ®  pq ~ q ~ #pt 
staticText-16ppppq ~ 6ppppq ~ 9  wñppppppq ~ åpq ~ ªq ~ ppppppppsq ~ psq ~   wñppppq ~áq ~áq ~ßpsq ~   wñppppq ~áq ~ápsq ~   wñppppq ~áq ~ápsq ~   wñppppq ~áq ~ápsq ~   wñppppq ~áq ~ápppppt 	Helveticappppppppppq ~ Òt V. ICMSsq ~   wñ           #  Ò  pq ~ q ~ #pt 
staticText-16ppppq ~ 6ppppq ~ 9  wñppppppq ~ åpq ~ ªq ~ ppppppppsq ~ psq ~   wñppppq ~ëq ~ëq ~épsq ~   wñppppq ~ëq ~ëpsq ~   wñppppq ~ëq ~ëpsq ~   wñppppq ~ëq ~ëpsq ~   wñppppq ~ëq ~ëpppppt 	Helveticappppppppppq ~ Òt V. IPIsq ~   wñ           m     pq ~ q ~ #pt 
staticText-16ppppq ~ 6ppppq ~ 9  wñppppppq ~ ppq ~ ppppppppsq ~ psq ~   wñppppq ~õq ~õq ~ópsq ~   wñppppq ~õq ~õpsq ~   wñppppq ~õq ~õpsq ~   wñppppq ~õq ~õpsq ~   wñppppq ~õq ~õpppppt 	Helveticappppppppppq ~ £t NOME / RAZÃO SOCIALsq ~ &  wñ           I   ø  pq ~ q ~ #ppppppq ~ 6ppppq ~ 9  wîppsq ~ ;  wñpppsq ~ @?   q ~ýppsq ~ &  wñ           ¼   c  épq ~ q ~ #ppppppq ~ 6ppppq ~ 9  wîppsq ~ ;  wñpppsq ~ @?   q ~ ppsq ~ &  wñ           ¯    épq ~ q ~ #ppppppq ~ 6ppppq ~ 9  wîppsq ~ ;  wñpppsq ~ @?   q ~ppsq ~   wñ           Z     êpq ~ q ~ #pt 
staticText-16ppppq ~ 6ppppq ~ 9  wñppppppq ~ ppq ~ ppppppppsq ~ psq ~   wñppppq ~q ~q ~psq ~   wñppppq ~q ~psq ~   wñppppq ~q ~psq ~   wñppppq ~q ~psq ~   wñppppq ~q ~pppppt 	Helveticappppppppppq ~ £t 
CNPJ / CPFsq ~   wñ           Z   f  êpq ~ q ~ #pt 
staticText-16ppppq ~ 6ppppq ~ 9  wñppppppq ~ ppq ~ ppppppppsq ~ psq ~   wñppppq ~q ~q ~psq ~   wñppppq ~q ~psq ~   wñppppq ~q ~psq ~   wñppppq ~q ~psq ~   wñppppq ~q ~pppppt 	Helveticappppppppppq ~ £t 	ENDEREÃOsq ~   wñ           Z  "  êpq ~ q ~ #pt 
staticText-16ppppq ~ 6ppppq ~ 9  wñppppppq ~ ppq ~ ppppppppsq ~ psq ~   wñppppq ~q ~q ~psq ~   wñppppq ~q ~psq ~   wñppppq ~q ~psq ~   wñppppq ~q ~psq ~   wñppppq ~q ~pppppt 	Helveticappppppppppq ~ £t BAIRRO / DISTRITOsq ~   wñ           Z    êpq ~ q ~ #pt 
staticText-16ppppq ~ 6ppppq ~ 9  wñppppppq ~ ppq ~ ppppppppsq ~ psq ~   wñppppq ~&q ~&q ~$psq ~   wñppppq ~&q ~&psq ~   wñppppq ~&q ~&psq ~   wñppppq ~&q ~&psq ~   wñppppq ~&q ~&pppppt 	Helveticappppppppppq ~ £t 
MUNICÃPIOsq ~   wñ           G   ú  pq ~ q ~ #pt 
staticText-16ppppq ~ 6ppppq ~ 9  wñppppppq ~ ppq ~ ppppppppsq ~ psq ~   wñppppq ~0q ~0q ~.psq ~   wñppppq ~0q ~0psq ~   wñppppq ~0q ~0psq ~   wñppppq ~0q ~0psq ~   wñppppq ~0q ~0pppppt 	Helveticappppppppppq ~ £t FRETE POR CONTAsq ~ &  wñ           M  ý  pq ~ q ~ #ppppppq ~ 6ppppq ~ 9  wîppsq ~ ;  wñpppsq ~ @?   q ~8ppsq ~   wñ           G  ÿ  pq ~ q ~ #pt 
staticText-16ppppq ~ 6ppppq ~ 9  wñppppppq ~ ppq ~ ppppppppsq ~ psq ~   wñppppq ~=q ~=q ~;psq ~   wñppppq ~=q ~=psq ~   wñppppq ~=q ~=psq ~   wñppppq ~=q ~=psq ~   wñppppq ~=q ~=pppppt 	Helveticappppppppppq ~ £t 
CNPJ / CPFsq ~ &  wñ             â  pq ~ q ~ #ppppppq ~ 6ppppq ~ 9  wîppsq ~ ;  wñpppsq ~ @?   q ~Eppsq ~   wñ             å  pq ~ q ~ #pt 
staticText-16ppppq ~ 6ppppq ~ 9  wñppppppq ~ ppq ~ ppppppppsq ~ psq ~   wñppppq ~Jq ~Jq ~Hpsq ~   wñppppq ~Jq ~Jpsq ~   wñppppq ~Jq ~Jpsq ~   wñppppq ~Jq ~Jpsq ~   wñppppq ~Jq ~Jpppppt 	Helveticappppppppppq ~ £t UFsq ~ &  wñ           Z    pq ~ q ~ #ppppppq ~ 6ppppq ~ 9  wîppsq ~ ;  wñpppsq ~ @?   q ~Rppsq ~   wñ           G    pq ~ q ~ #pt 
staticText-16ppppq ~ 6ppppq ~ 9  wñppppppq ~ ppq ~ ppppppppsq ~ psq ~   wñppppq ~Wq ~Wq ~Upsq ~   wñppppq ~Wq ~Wpsq ~   wñppppq ~Wq ~Wpsq ~   wñppppq ~Wq ~Wpsq ~   wñppppq ~Wq ~Wpppppt 	Helveticappppppppppq ~ £t PLACA DO VEÃCULOsq ~   wñ           D  D  pq ~ q ~ #pt 
staticText-16ppppq ~ 6ppppq ~ 9  wñppppppq ~ ppq ~ ppppppppsq ~ psq ~   wñppppq ~aq ~aq ~_psq ~   wñppppq ~aq ~apsq ~   wñppppq ~aq ~apsq ~   wñppppq ~aq ~apsq ~   wñppppq ~aq ~apppppt 	Helveticappppppppppq ~ £t CÃDIGO ANTTsq ~   wñ           m     ¬pq ~ q ~ #pt 
staticText-16ppppq ~ 6ppppq ~ 9  wñppppppq ~ ppq ~ ppppppppsq ~ psq ~   wñppppq ~kq ~kq ~ipsq ~   wñppppq ~kq ~kpsq ~   wñppppq ~kq ~kpsq ~   wñppppq ~kq ~kpsq ~   wñppppq ~kq ~kpppppt 	Helveticappppppppppq ~ £t 	ENDEREÃOsq ~ &  wñ           ¡   ø  «pq ~ q ~ #ppppppq ~ 6ppppq ~ 9  wîppsq ~ ;  wñpppsq ~ @?   q ~sppsq ~ &  wñ               «pq ~ q ~ #ppppppq ~ 6ppppq ~ 9  wîppsq ~ ;  wñpppsq ~ @?   q ~vppsq ~ &  wñ           \     Äpq ~ q ~ #ppppppq ~ 6ppppq ~ 9  wîppsq ~ ;  wñpppsq ~ @?   q ~yppsq ~ &  wñ           \   a  Äpq ~ q ~ #ppppppq ~ 6ppppq ~ 9  wîppsq ~ ;  wñpppsq ~ @?   q ~|ppsq ~ &  wñ           \   ¼  Äpq ~ q ~ #ppppppq ~ 6ppppq ~ 9  wîppsq ~ ;  wñpppsq ~ @?   q ~ppsq ~ &  wñ           \    Äpq ~ q ~ #ppppppq ~ 6ppppq ~ 9  wîppsq ~ ;  wñpppsq ~ @?   q ~ppsq ~ &  wñ           \  t  Äpq ~ q ~ #ppppppq ~ 6ppppq ~ 9  wîppsq ~ ;  wñpppsq ~ @?   q ~ppsq ~ &  wñ           µ   Ö   ×pq ~ q ~ #ppppppq ~ 6ppppq ~ 9  wîppsq ~ ;  wñpppsq ~ @?   q ~ppsq ~ &  wñ           s     pq ~ q ~ #ppppppq ~ 6sq ~ ð   &uq ~ ó   sq ~ õt apresentarFaturassq ~ õt .equals(false)q ~Tppppq ~ 9  wîppsq ~ ;  wñpppsq ~ @?   q ~ppsq ~   wñ           m   ú  ¬pq ~ q ~ #pt 
staticText-16ppppq ~ 6ppppq ~ 9  wñppppppq ~ ppq ~ ppppppppsq ~ psq ~   wñppppq ~q ~q ~psq ~   wñppppq ~q ~psq ~   wñppppq ~q ~psq ~   wñppppq ~q ~psq ~   wñppppq ~q ~pppppt 	Helveticappppppppppq ~ £t 
MUNICÃPIOsq ~   wñ               ¬pq ~ q ~ #pt 
staticText-16ppppq ~ 6ppppq ~ 9  wñppppppq ~ ppq ~ ppppppppsq ~ psq ~   wñppppq ~ q ~ q ~psq ~   wñppppq ~ q ~ psq ~   wñppppq ~ q ~ psq ~   wñppppq ~ q ~ psq ~   wñppppq ~ q ~ pppppt 	Helveticappppppppppq ~ £t UFsq ~   wñ             »  ¬pq ~ q ~ #pt 
staticText-16ppppq ~ 6ppppq ~ 9  wñppppppq ~ ppq ~ ppppppppsq ~ psq ~   wñppppq ~ªq ~ªq ~¨psq ~   wñppppq ~ªq ~ªpsq ~   wñppppq ~ªq ~ªpsq ~   wñppppq ~ªq ~ªpsq ~   wñppppq ~ªq ~ªpppppt 	Helveticappppppppppq ~ £t INSCRIÃÃO ESTADUALsq ~   wñ           R     Åpq ~ q ~ #pt 
staticText-16ppppq ~ 6ppppq ~ 9  wñppppppq ~ ppq ~ ppppppppsq ~ psq ~   wñppppq ~´q ~´q ~²psq ~   wñppppq ~´q ~´psq ~   wñppppq ~´q ~´psq ~   wñppppq ~´q ~´psq ~   wñppppq ~´q ~´pppppt 	Helveticappppppppppq ~ £t 
QUANTIDADEsq ~   wñ           R   d  Åpq ~ q ~ #pt 
staticText-16ppppq ~ 6ppppq ~ 9  wñppppppq ~ ppq ~ ppppppppsq ~ psq ~   wñppppq ~¾q ~¾q ~¼psq ~   wñppppq ~¾q ~¾psq ~   wñppppq ~¾q ~¾psq ~   wñppppq ~¾q ~¾psq ~   wñppppq ~¾q ~¾pppppt 	Helveticappppppppppq ~ £t ESPÃCIEsq ~   wñ           R   ¿  Åpq ~ q ~ #pt 
staticText-16ppppq ~ 6ppppq ~ 9  wñppppppq ~ ppq ~ ppppppppsq ~ psq ~   wñppppq ~Èq ~Èq ~Æpsq ~   wñppppq ~Èq ~Èpsq ~   wñppppq ~Èq ~Èpsq ~   wñppppq ~Èq ~Èpsq ~   wñppppq ~Èq ~Èpppppt 	Helveticappppppppppq ~ £t MARCAsq ~   wñ           R    Åpq ~ q ~ #pt 
staticText-16ppppq ~ 6ppppq ~ 9  wñppppppq ~ ppq ~ ppppppppsq ~ psq ~   wñppppq ~Òq ~Òq ~Ðpsq ~   wñppppq ~Òq ~Òpsq ~   wñppppq ~Òq ~Òpsq ~   wñppppq ~Òq ~Òpsq ~   wñppppq ~Òq ~Òpppppt 	Helveticappppppppppq ~ £t NUMERAÃÃOsq ~   wñ           R  v  Åpq ~ q ~ #pt 
staticText-16ppppq ~ 6ppppq ~ 9  wñppppppq ~ ppq ~ ppppppppsq ~ psq ~   wñppppq ~Üq ~Üq ~Úpsq ~   wñppppq ~Üq ~Üpsq ~   wñppppq ~Üq ~Üpsq ~   wñppppq ~Üq ~Üpsq ~   wñppppq ~Üq ~Üpppppt 	Helveticappppppppppq ~ £t 
PESO BRUTOsq ~   wñ           t  Ó  Åpq ~ q ~ #pt 
staticText-16ppppq ~ 6ppppq ~ 9  wñppppppq ~ ppq ~ ppppppppsq ~ psq ~   wñppppq ~æq ~æq ~äpsq ~   wñppppq ~æq ~æpsq ~   wñppppq ~æq ~æpsq ~   wñppppq ~æq ~æpsq ~   wñppppq ~æq ~æpppppt 	Helveticappppppppppq ~ £t 
PESO LÃQUIDOsq ~ à  wñ           X     Wpq ~ q ~ #pt 
textField-212ppppq ~ 6ppppq ~ 9  wñppppppq ~ ¨p~q ~ ©t RIGHTq ~ ppppppppsq ~ psq ~   wñppppq ~òq ~òq ~îpsq ~   wñppppq ~òq ~òpsq ~   wñppppq ~òq ~òpsq ~   wñppppq ~òq ~òpsq ~   wñppppq ~òq ~òpppppt Helvetica-Boldppppppppppq ~ Ò  wñ        ppq ~ îsq ~ ð   'uq ~ ó   sq ~ õt baseCalculoICMSt java.lang.Stringppppppq ~ pppsq ~ à  wñ           4   c  Wpq ~ q ~ #pt 
textField-212ppppq ~ 6ppppq ~ 9  wñppppppq ~ ¨pq ~ðq ~ ppppppppsq ~ psq ~   wñppppq ~ q ~ q ~þpsq ~   wñppppq ~ q ~ psq ~   wñppppq ~ q ~ psq ~   wñppppq ~ q ~ psq ~   wñppppq ~ q ~ pppppt Helvetica-Boldppppppppppq ~ Ò  wñ        ppq ~ îsq ~ ð   (uq ~ ó   sq ~ õt 	valorICMSt java.lang.Stringppppppq ~ pppsq ~ à  wñ           W     Wpq ~ q ~ #pt 
textField-212ppppq ~ 6ppppq ~ 9  wñppppppq ~ ¨pq ~ðq ~ ppppppppsq ~ psq ~   wñppppq ~q ~q ~psq ~   wñppppq ~q ~psq ~   wñppppq ~q ~psq ~   wñppppq ~q ~psq ~   wñppppq ~q ~pppppt Helvetica-Boldppppppppppq ~ Ò  wñ        ppq ~ îsq ~ ð   )uq ~ ó   sq ~ õt baseCalculoICMSSubst java.lang.Stringppppppq ~ pppsq ~ à  wñ           h   ù  Wpq ~ q ~ #pt 
textField-212ppppq ~ 6ppppq ~ 9  wñppppppq ~ ¨pq ~ðq ~ ppppppppsq ~ psq ~   wñppppq ~q ~q ~psq ~   wñppppq ~q ~psq ~   wñppppq ~q ~psq ~   wñppppq ~q ~psq ~   wñppppq ~q ~pppppt Helvetica-Boldppppppppppq ~ Ò  wñ        ppq ~ îsq ~ ð   *uq ~ ó   sq ~ õt 
valorICMSSubst java.lang.Stringppppppq ~ pppsq ~ à  wñ           t  f  Wpq ~ q ~ #pt 
textField-212ppppq ~ 6ppppq ~ 9  wñppppppq ~ ¨pq ~ðq ~ ppppppppsq ~ psq ~   wñppppq ~*q ~*q ~(psq ~   wñppppq ~*q ~*psq ~   wñppppq ~*q ~*psq ~   wñppppq ~*q ~*psq ~   wñppppq ~*q ~*pppppt Helvetica-Boldppppppppppq ~ Ò  wñ        ppq ~ îsq ~ ð   +uq ~ ó   sq ~ õt 
valorTributost java.lang.Stringppppppq ~ pppsq ~ à  wñ           i  ß  Wpq ~ q ~ #pt 
textField-212ppppq ~ 6ppppq ~ 9  wñppppppq ~ ¨pq ~ðq ~ Êppppppppsq ~ psq ~   wñppppq ~8q ~8q ~6psq ~   wñppppq ~8q ~8psq ~   wñppppq ~8q ~8psq ~   wñppppq ~8q ~8psq ~   wñppppq ~8q ~8pppppt Helvetica-Boldppppppppppq ~ Ò  wñ        ppq ~ îsq ~ ð   ,uq ~ ó   sq ~ õt valorTotalProdutost java.lang.Stringppppppq ~ pppsq ~   wñ           G   ú  pq ~ q ~ #pt 
staticText-16ppppq ~ 6ppppq ~ 9  wñppppppq ~ ppq ~ ppppppppsq ~ psq ~   wñppppq ~Fq ~Fq ~Dpsq ~   wñppppq ~Fq ~Fpsq ~   wñppppq ~Fq ~Fpsq ~   wñppppq ~Fq ~Fpsq ~   wñppppq ~Fq ~Fpppppt 	Helveticappppppppppq ~ £t 
0-Emitentesq ~ à  wñ           W     vpq ~ q ~ #pt 
textField-212ppppq ~ 6ppppq ~ 9  wñppppppq ~ ¨pq ~ðq ~ ppppppppsq ~ psq ~   wñppppq ~Pq ~Pq ~Npsq ~   wñppppq ~Pq ~Ppsq ~   wñppppq ~Pq ~Ppsq ~   wñppppq ~Pq ~Ppsq ~   wñppppq ~Pq ~Ppppppt Helvetica-Boldppppppppppq ~ Ò  wñ        ppq ~ îsq ~ ð   -uq ~ ó   sq ~ õt 
valorFretet java.lang.Stringppppppq ~ pppsq ~ à  wñ           A   c  vpq ~ q ~ #pt 
textField-212ppppq ~ 6ppppq ~ 9  wñppppppq ~ ¨pq ~ðq ~ ppppppppsq ~ psq ~   wñppppq ~^q ~^q ~\psq ~   wñppppq ~^q ~^psq ~   wñppppq ~^q ~^psq ~   wñppppq ~^q ~^psq ~   wñppppq ~^q ~^pppppt Helvetica-Boldppppppppppq ~ Ò  wñ        ppq ~ îsq ~ ð   .uq ~ ó   sq ~ õt valorSegurot java.lang.Stringppppppq ~ pppsq ~ à  wñ           6   ©  vpq ~ q ~ #pt 
textField-212ppppq ~ 6ppppq ~ 9  wñppppppq ~ ¨pq ~ðq ~ ppppppppsq ~ psq ~   wñppppq ~lq ~lq ~jpsq ~   wñppppq ~lq ~lpsq ~   wñppppq ~lq ~lpsq ~   wñppppq ~lq ~lpsq ~   wñppppq ~lq ~lpppppt Helvetica-Boldppppppppppq ~ Ò  wñ        ppq ~ îsq ~ ð   /uq ~ ó   sq ~ õt 
valorDescontot java.lang.Stringppppppq ~ pppsq ~ à  wñ           m   ä  vpq ~ q ~ #pt 
textField-212ppppq ~ 6ppppq ~ 9  wñppppppq ~ ¨pq ~ðq ~ ppppppppsq ~ psq ~   wñppppq ~zq ~zq ~xpsq ~   wñppppq ~zq ~zpsq ~   wñppppq ~zq ~zpsq ~   wñppppq ~zq ~zpsq ~   wñppppq ~zq ~zpppppt Helvetica-Boldppppppppppq ~ Ò  wñ        ppq ~ îsq ~ ð   0uq ~ ó   sq ~ õt valorOutrasDespesast java.lang.Stringppppppq ~ pppsq ~ à  wñ           D  U  vpq ~ q ~ #pt 
textField-212ppppq ~ 6ppppq ~ 9  wñppppppq ~ ¨pq ~ðq ~ ppppppppsq ~ psq ~   wñppppq ~q ~q ~psq ~   wñppppq ~q ~psq ~   wñppppq ~q ~psq ~   wñppppq ~q ~psq ~   wñppppq ~q ~pppppt Helvetica-Boldppppppppppq ~ Ò  wñ        ppq ~ îsq ~ ð   1uq ~ ó   sq ~ õt valorIPIt java.lang.Stringppppppq ~ pppsq ~ à  wñ           K    vpq ~ q ~ #pt 
textField-212ppppq ~ 6ppppq ~ 9  wñppppppq ~ ¨pq ~ðq ~ ppppppppsq ~ psq ~   wñppppq ~q ~q ~psq ~   wñppppq ~q ~psq ~   wñppppq ~q ~psq ~   wñppppq ~q ~psq ~   wñppppq ~q ~pppppt Helvetica-Boldppppppppppq ~ Ò  wñ        ppq ~ îsq ~ ð   2uq ~ ó   sq ~ õt 	valorIRRFt java.lang.Stringppppppq ~ pppsq ~ à  wñ           X  ï  vpq ~ q ~ #pt 
textField-212ppppq ~ 6ppppq ~ 9  wñppppppq ~ ¨pq ~ðq ~ Êppppppppsq ~ psq ~   wñppppq ~¤q ~¤q ~¢psq ~   wñppppq ~¤q ~¤psq ~   wñppppq ~¤q ~¤psq ~   wñppppq ~¤q ~¤psq ~   wñppppq ~¤q ~¤pppppt Helvetica-Boldppppppppppq ~ Ò  wñ        ppq ~ îsq ~ ð   3uq ~ ó   sq ~ õt valorTotalNotat java.lang.Stringppppppq ~ pppsq ~   wñ           p     apq ~ q ~ #pt 
staticText-16ppppq ~ 6ppppq ~ 9  wñppppppq ~ ¨ppq ~ Êppppppppsq ~ psq ~   wñppppq ~²q ~²q ~°psq ~   wñppppq ~²q ~²psq ~   wñppppq ~²q ~²psq ~   wñppppq ~²q ~²psq ~   wñppppq ~²q ~²pppppt 	Helveticappppppppppq ~ Òt FOLHA 01/01sr 0net.sf.jasperreports.engine.base.JRBaseSubreport      'Ø L connectionExpressionq ~ L dataSourceExpressionq ~ L 
expressionq ~ L isUsingCacheq ~ [ 
parameterst 3[Lnet/sf/jasperreports/engine/JRSubreportParameter;L parametersMapExpressionq ~ [ returnValuest 5[Lnet/sf/jasperreports/engine/JRSubreportReturnValue;L runToBottomq ~ xq ~ +  wñ          E     2pq ~ q ~ #ppppppq ~ 6ppppq ~ 9psq ~ ð   4uq ~ ó   sq ~ õt 
produtosJrt (net.sf.jasperreports.engine.JRDataSourcepsq ~ ð   5uq ~ ó   sq ~ õt 
SUBREPORT_DIRsq ~ õt  + "NFe_Produtos.jasper"t java.lang.Stringpq ~ ppppsq ~ à  wñ           §   Y   apq ~ q ~ #pt 
textField-212ppppq ~ 6ppppq ~ 9  wñppppppq ~ ppq ~ ppppppppsq ~ psq ~   wñppppq ~Ìq ~Ìq ~Êpsq ~   wñppppq ~Ìq ~Ìpsq ~   wñppppq ~Ìq ~Ìpsq ~   wñppppq ~Ìq ~Ìpsq ~   wñppppq ~Ìq ~Ìpppppt Helvetica-Boldppppppppppq ~ Ò  wñ        ppq ~ îsq ~ ð   6uq ~ ó   sq ~ õt "FONE: " + sq ~ õt telefonePrestadort java.lang.Stringppppppq ~ pppsr 7net.sf.jasperreports.engine.base.JRBaseComponentElement      'Ø L 	componentt 1Lnet/sf/jasperreports/engine/component/Component;L componentKeyt 4Lnet/sf/jasperreports/engine/component/ComponentKey;xq ~ +  wñ           É  |   
pq ~ q ~ #ppppppq ~ 6ppppq ~ 9sr Bnet.sf.jasperreports.components.barbecue.StandardBarbecueComponent      'Ø I PSEUDO_SERIAL_VERSION_UIDZ checksumRequiredZ drawTextB evaluationTimeL applicationIdentifierExpressionq ~ L 	barHeightq ~ 'L barWidthq ~ 'L codeExpressionq ~ L evaluationGroupq ~ L evaluationTimeValueq ~ áL typeq ~ xp  wñ   psq ~     sq ~     sq ~ ð   7uq ~ ó   sq ~ õt chaveAcessot java.lang.Stringppq ~ ît 2of7sr 2net.sf.jasperreports.engine.component.ComponentKey      'Ø L nameq ~ L 	namespaceq ~ L namespacePrefixq ~ xpt barbecuet =http://jasperreports.sourceforge.net/jasperreports/componentst jrsr ,net.sf.jasperreports.engine.base.JRBaseFrame      'Ø L borderq ~ L borderColorq ~ ,L bottomBorderq ~ L bottomBorderColorq ~ ,L 
bottomPaddingq ~ 'L childrenq ~ !L 
leftBorderq ~ L leftBorderColorq ~ ,L leftPaddingq ~ 'L lineBoxq ~ L paddingq ~ 'L rightBorderq ~ L rightBorderColorq ~ ,L rightPaddingq ~ 'L 	topBorderq ~ L topBorderColorq ~ ,L 
topPaddingq ~ 'xq ~ +  wñ   +       E     @pq ~ q ~ #pppppp~q ~ 5t FLOATppppq ~ 9pppppsq ~ $   
w   
sq ~ &  wñ          E       
pq ~ q ~îppppppq ~ 6ppppq ~ 9  wîppsq ~ ;  wñpppsq ~ @?   q ~òppsq ~ &  wñ             ±   
pq ~ q ~îppppppq ~ 6ppppq ~ 9  wîppsq ~ ;  wñpppsq ~ @?   q ~õppsq ~ &  wñ                  
pq ~ q ~îppppppq ~ 6ppppq ~ 9  wîppsq ~ ;  wñpppsq ~ @?   q ~øppsq ~ &  wñ                
pq ~ q ~îppppppq ~ 6ppppq ~ 9  wîppsq ~ ;  wñpppsq ~ @?   q ~ûppsq ~ à  wñ                 pq ~ q ~îpt 
textField-212ppppq ~ 6ppppq ~ 9  wñppppppq ~ ¨pq ~ðq ~ ppppppppsq ~ psq ~   wñppppq ~ q ~ q ~þpsq ~   wñppppq ~ q ~ psq ~   wñppppq ~ q ~ psq ~   wñppppq ~ q ~ psq ~   wñppppq ~ q ~ pppppt Helvetica-Boldppppppppppq ~ Ò  wñ        ppq ~ îsq ~ ð   8uq ~ ó   sq ~ õt valorBaseCalculoISSQNt java.lang.Stringppppppq ~ pppsq ~ à  wñ             ´   pq ~ q ~îpt 
textField-212ppppq ~ 6ppppq ~ 9  wñppppppq ~ ¨pq ~ðq ~ ppppppppsq ~ psq ~   wñppppq ~q ~q ~psq ~   wñppppq ~q ~psq ~   wñppppq ~q ~psq ~   wñppppq ~q ~psq ~   wñppppq ~q ~pppppt Helvetica-Boldppppppppppq ~ Ò  wñ        ppq ~ îsq ~ ð   9uq ~ ó   sq ~ õt 
valorISSQNt java.lang.Stringppppppq ~ pppsq ~ à  wñ                 pq ~ q ~îpt 
textField-212ppppq ~ 6ppppq ~ 9  wñppppppq ~ ¨ppq ~ ppppppppsq ~ psq ~   wñppppq ~q ~q ~psq ~   wñppppq ~q ~psq ~   wñppppq ~q ~psq ~   wñppppq ~q ~psq ~   wñppppq ~q ~pppppt Helvetica-Boldppppppppppq ~ Ò  wñ        ppq ~ îsq ~ ð   :uq ~ ó   sq ~ õt inscricaoEstadualPrestadort java.lang.Stringppppppq ~ pppsq ~ à  wñ                 pq ~ q ~îpt 
textField-212ppppq ~ 6ppppq ~ 9  wñppppppq ~ ¨pq ~ðq ~ ppppppppsq ~ psq ~   wñppppq ~*q ~*q ~(psq ~   wñppppq ~*q ~*psq ~   wñppppq ~*q ~*psq ~   wñppppq ~*q ~*psq ~   wñppppq ~*q ~*pppppt Helvetica-Boldppppppppppq ~ Ò  wñ        ppq ~ îsq ~ ð   ;uq ~ ó   sq ~ õt valorTotalServicost java.lang.Stringppppppq ~ pppsq ~   wñ   
       E        pq ~ q ~îpt 
staticText-16ppppq ~ 6ppppq ~ 9  wñppppppq ~ ppq ~ Êppppppppsq ~ psq ~   wñppppq ~8q ~8q ~6psq ~   wñppppq ~8q ~8psq ~   wñppppq ~8q ~8psq ~   wñppppq ~8q ~8psq ~   wñppppq ~8q ~8pppppt 	Helveticappppppppppq ~ Òt CÃLCULO DO ISSQNsq ~   wñ                 pq ~ q ~îpt 
staticText-16ppppq ~ 6ppppq ~ 9  wñppppppq ~ ppq ~ ppppppppsq ~ psq ~   wñppppq ~Bq ~Bq ~@psq ~   wñppppq ~Bq ~Bpsq ~   wñppppq ~Bq ~Bpsq ~   wñppppq ~Bq ~Bpsq ~   wñppppq ~Bq ~Bpppppt 	Helveticappppppppppq ~ £t INSCRIÃÃO MUNICIPALsq ~   wñ           }      
pq ~ q ~îpt 
staticText-16ppppq ~ 6ppppq ~ 9  wñppppppq ~ ppq ~ ppppppppsq ~ psq ~   wñppppq ~Lq ~Lq ~Jpsq ~   wñppppq ~Lq ~Lpsq ~   wñppppq ~Lq ~Lpsq ~   wñppppq ~Lq ~Lpsq ~   wñppppq ~Lq ~Lpppppt 	Helveticappppppppppq ~ £t VALOR TOTAL DOS SERVIÃOSsq ~   wñ           }  !   pq ~ q ~îpt 
staticText-16ppppq ~ 6ppppq ~ 9  wñppppppq ~ ppq ~ ppppppppsq ~ psq ~   wñppppq ~Vq ~Vq ~Tpsq ~   wñppppq ~Vq ~Vpsq ~   wñppppq ~Vq ~Vpsq ~   wñppppq ~Vq ~Vpsq ~   wñppppq ~Vq ~Vpppppt 	Helveticappppppppppq ~ £t BASE DE CÃLCULO DO ISSQNsq ~   wñ           }  µ   pq ~ q ~îpt 
staticText-16ppppq ~ 6ppppq ~ 9  wñppppppq ~ ppq ~ ppppppppsq ~ psq ~   wñppppq ~`q ~`q ~^psq ~   wñppppq ~`q ~`psq ~   wñppppq ~`q ~`psq ~   wñppppq ~`q ~`psq ~   wñppppq ~`q ~`pppppt 	Helveticappppppppppq ~ £t VALOR DO ISSQNxpppsq ~ psq ~   wñppppq ~hq ~hq ~îpsq ~   wñppppq ~hq ~hpsq ~   wñppppq ~hq ~hpsq ~   wñppppq ~hq ~hpsq ~   wñppppq ~hq ~hpppppppsq ~   wñ   
      E     opq ~ q ~ #pt 
staticText-16ppppq ~ïppppq ~ 9  wñppppppq ~ ppq ~ Êppppppppsq ~ psq ~   wñppppq ~pq ~pq ~npsq ~   wñppppq ~pq ~ppsq ~   wñppppq ~pq ~ppsq ~   wñppppq ~pq ~ppsq ~   wñppppq ~pq ~ppppppt 	Helveticappppppppppq ~ Òt DADOS ADICIONAISsq ~ &  wñ   X      B     }pq ~ q ~ #ppppppq ~ïppppq ~ 9  wîppsq ~ ;  wñpppsq ~ @?   q ~xppsq ~ &  wñ   X        F  }pq ~ q ~ #ppppppq ~ïppppq ~ 9  wîppsq ~ ;  wñpppsq ~ @?   q ~{ppsq ~   wñ          «     }pq ~ q ~ #pt 
staticText-16ppppq ~ïppppq ~ 9  wñppppppq ~ ppq ~ ppppppppsq ~ psq ~   wñppppq ~q ~q ~~psq ~   wñppppq ~q ~psq ~   wñppppq ~q ~psq ~   wñppppq ~q ~psq ~   wñppppq ~q ~pppppt 	Helveticappppppppppq ~ Òt INFORMAÃÃES COMPLEMENTARES
sq ~   wñ          «  I  }pq ~ q ~ #pt 
staticText-16ppppq ~ïppppq ~ 9  wñppppppq ~ ppq ~ ppppppppsq ~ psq ~   wñppppq ~q ~q ~psq ~   wñppppq ~q ~psq ~   wñppppq ~q ~psq ~   wñppppq ~q ~psq ~   wñppppq ~q ~pppppt 	Helveticappppppppppq ~ Òt RESERVADO AO FISCOsq ~ à  wñ           ü  L  pq ~ q ~ #pt 
textField-212ppppq ~ 6ppppq ~ 9  wñppppppq ~ ¨ppq ~ ppppppppsq ~ psq ~   wñppppq ~q ~q ~psq ~   wñppppq ~q ~psq ~   wñppppq ~q ~psq ~   wñppppq ~q ~psq ~   wñppppq ~q ~pppppt Helvetica-Boldppppppppppq ~ Ò  wñ        ppq ~ îsq ~ ð   <uq ~ ó   sq ~ õt infAdicinfAdFiscot java.lang.Stringppppppq ~ pppsq ~º  wñ         ;   	  pq ~ q ~ #ppppppq ~ïppppq ~ 9psq ~ ð   =uq ~ ó   sq ~ õt 
observacoesJrq ~Âpsq ~ ð   >uq ~ ó   sq ~ õt 
SUBREPORT_DIRsq ~ õt  + "NFe_Observacoes.jasper"t java.lang.Stringpq ~ ppppsq ~   wñ   
       ~     Öpq ~ q ~ #pt 
staticText-16ppppq ~ïppppq ~ 9  wñppppppq ~ ¨ppq ~ ppppppppsq ~ psq ~   wñppppq ~®q ~®q ~¬psq ~   wñppppq ~®q ~®psq ~   wñppppq ~®q ~®psq ~   wñppppq ~®q ~®psq ~   wñppppq ~®q ~®pppppt 	Helveticappppppppppq ~ Òt DATA E HORA DA IMPRESSÃO:sq ~ à  wñ   
            Ösq ~Ã    ÿÿÿÿpppq ~ q ~ #pt 	dataRel-1p~r )net.sf.jasperreports.engine.type.ModeEnum          xq ~ t TRANSPARENTppq ~ïppppq ~ 9  wñpppppt Arialsq ~    	ppq ~ q ~ Êq ~ ppq ~ pppsq ~ sq ~    sq ~   wñsq ~Ã    ÿfffppppq ~Èsq ~ @    q ~¾q ~¾q ~¶psq ~   wñsq ~Ã    ÿfffppppq ~Èsq ~ @    q ~¾q ~¾psq ~   wñppppq ~¾q ~¾psq ~   wñsq ~Ã    ÿfffppppq ~Èsq ~ @    q ~¾q ~¾psq ~   wñsq ~Ã    ÿfffppppq ~Èsq ~ @    q ~¾q ~¾pppppt Helvetica-Obliquepppppppppp~q ~ ¢t BOTTOM  wñ        ppq ~ îsq ~ ð   ?uq ~ ó   sq ~ õt 
new Date()t java.util.Dateppppppq ~ Êppt dd/MM/yyyy HH:mm:sssq ~ à  wñ           m      pq ~ q ~ #ppppppq ~ 6sq ~ ð   @uq ~ ó   sq ~ õt apresentarFaturassq ~ õt .equals(false)q ~Tppppq ~ 9  wñppppppq ~ pppppppppppsq ~ psq ~   wñppppq ~Ýq ~Ýq ~Öpsq ~   wñppppq ~Ýq ~Ýpsq ~   wñppppq ~Ýq ~Ýpsq ~   wñppppq ~Ýq ~Ýpsq ~   wñppppq ~Ýq ~Ýppppppppppppppppp  wñ        ppq ~ îsq ~ ð   Auq ~ ó   sq ~ õt nomeFormaPagamentot java.lang.Stringppppppppppsq ~º  wñ          E     pq ~ q ~ #ppppppq ~ 6sq ~ ð   Buq ~ ó   sq ~ õt apresentarFaturassq ~ õt 
.equals(true)q ~Tppppq ~ 9psq ~ ð   Cuq ~ ó   sq ~ õt 	faturasJrq ~Âpsq ~ ð   Duq ~ ó   sq ~ õt 
SUBREPORT_DIRsq ~ õt  + "NFe_Faturas.jasper"q ~Épq ~ ppppxp  wñ  çpppppsr java.util.HashSetºD¸·4  xpw   ?@     t "net.sf.jasperreports.engine.data.*t net.sf.jasperreports.engine.*t java.util.*xt javapsr .net.sf.jasperreports.engine.base.JRBaseDataset      'Ø I PSEUDO_SERIAL_VERSION_UIDZ isMainB whenResourceMissingType[ fieldst &[Lnet/sf/jasperreports/engine/JRField;L filterExpressionq ~ [ groupst &[Lnet/sf/jasperreports/engine/JRGroup;L nameq ~ [ 
parameterst *[Lnet/sf/jasperreports/engine/JRParameter;L 
propertiesMapq ~ 1L queryt %Lnet/sf/jasperreports/engine/JRQuery;L resourceBundleq ~ L scriptletClassq ~ [ 
scriptletst *[Lnet/sf/jasperreports/engine/JRScriptlet;[ 
sortFieldst *[Lnet/sf/jasperreports/engine/JRSortField;[ 	variablest )[Lnet/sf/jasperreports/engine/JRVariable;L whenResourceMissingTypeValuet >Lnet/sf/jasperreports/engine/type/WhenResourceMissingTypeEnum;xp  wñ ur &[Lnet.sf.jasperreports.engine.JRField;<ßÇN*òp  xp   Wsr ,net.sf.jasperreports.engine.base.JRBaseField      'Ø L descriptionq ~ L nameq ~ L 
propertiesMapq ~ 1L valueClassNameq ~ L valueClassRealNameq ~ xppt razaoSocialPrestadorsr +net.sf.jasperreports.engine.JRPropertiesMap      'Ø L baseq ~ 1L propertiesListq ~ !L 
propertiesMapt Ljava/util/Map;xppppt java.lang.Stringpsq ~pt 
cnpjPrestadorsq ~pppt java.lang.Stringpsq ~pt inscricaoMunicipalPrestadorsq ~pppt java.lang.Stringpsq ~pt inscricaoEstadualPrestadorsq ~pppt java.lang.Stringpsq ~pt enderecoPrestadorsq ~pppt java.lang.Stringpsq ~pt complementoPrestadorsq ~pppt java.lang.Stringpsq ~pt municipioPrestadorsq ~pppt java.lang.Stringpsq ~pt ufPrestadorsq ~pppt java.lang.Stringpsq ~pt telefonePrestadorsq ~pppt java.lang.Stringpsq ~pt emailPrestadorsq ~pppt java.lang.Stringpsq ~pt logomarcaPrestadorsq ~pppt java.io.InputStreampsq ~pt razaoSocialTomadorsq ~pppt java.lang.Stringpsq ~pt cnpjTomadorsq ~pppt java.lang.Stringpsq ~pt inscricaoMunicipalTomadorsq ~pppt java.lang.Stringpsq ~pt inscricaoEstadualTomadorsq ~pppt java.lang.Stringpsq ~pt enderecoTomadorsq ~pppt java.lang.Stringpsq ~pt complementoTomadorsq ~pppt java.lang.Stringpsq ~pt municipioTomadorsq ~pppt java.lang.Stringpsq ~pt 	ufTomadorsq ~pppt java.lang.Stringpsq ~pt telefoneTomadorsq ~pppt java.lang.Stringpsq ~pt emailTomadorsq ~pppt java.lang.Stringpsq ~pt logomarcaPrefeiturasq ~pppt java.io.InputStreampsq ~pt cidadePrestacaosq ~pppt java.lang.Stringpsq ~pt 
numeroNotasq ~pppt java.lang.Stringpsq ~pt dataServicosq ~pppt java.lang.Stringpsq ~pt dataHoraEmissaosq ~pppt java.lang.Stringpsq ~pt codigoAutorizacaosq ~pppt java.lang.Stringpsq ~pt dataCompetenciasq ~pppt java.lang.Stringpsq ~pt 	numeroRPSsq ~pppt java.lang.Stringpsq ~pt municipioPrestacaosq ~pppt java.lang.Stringpsq ~pt servicosDescricaosq ~pppt java.lang.Stringpsq ~pt 
codigoServicosq ~pppt java.lang.Stringpsq ~pt valorPISsq ~pppt java.lang.Stringpsq ~pt valorCOFINSsq ~pppt java.lang.Stringpsq ~pt valorIRsq ~pppt java.lang.Stringpsq ~pt 	valorINSSsq ~pppt java.lang.Stringpsq ~pt 	valorCSLLsq ~pppt java.lang.Stringpsq ~pt 
valorServicossq ~pppt java.lang.Stringpsq ~pt descontoIncondicionadosq ~pppt java.lang.Stringpsq ~pt descontoCondicionadosq ~pppt java.lang.Stringpsq ~pt retencoesFederaissq ~pppt java.lang.Stringpsq ~pt outrasRetencoessq ~pppt java.lang.Stringpsq ~pt issRetidoValorsq ~pppt java.lang.Stringpsq ~pt valorLiquidosq ~pppt java.lang.Stringpsq ~pt naturezaOperacaosq ~pppt java.lang.Stringpsq ~pt regimeEspecialsq ~pppt java.lang.Stringpsq ~pt simplesNacionalsq ~pppt java.lang.Stringpsq ~pt incentivadorCulturalsq ~pppt java.lang.Stringpsq ~pt deducoesPermitidassq ~pppt java.lang.Stringpsq ~pt descontoIncondicionadoMunicipiosq ~pppt java.lang.Stringpsq ~pt baseCalculosq ~pppt java.lang.Stringpsq ~pt aliquotasq ~pppt java.lang.Stringpsq ~pt reterISSsq ~pppt java.lang.Stringpsq ~pt valorISSsq ~pppt java.lang.Stringpsq ~pt valorTotalNotasq ~pppt java.lang.Stringpsq ~pt outrasInformacoessq ~pppt java.lang.Stringpsq ~pt chaveAcessosq ~pppt java.lang.Stringpsq ~pt protocoloAutorizacaosq ~pppt java.lang.Stringpsq ~pt 	dataSaidasq ~pppt java.lang.Stringpsq ~pt 
bairroTomadorsq ~pppt java.lang.Stringpsq ~pt 
cepTomadorsq ~pppt java.lang.Stringpsq ~pt bairroPrestadorsq ~pppt java.lang.Stringpsq ~pt cepPrestadorsq ~pppt java.lang.Stringpsq ~pt 	serieNotasq ~pppt java.lang.Stringpsq ~pt dataEmissaosq ~pppt java.lang.Stringpsq ~pt 	horaSaidasq ~pppt java.lang.Stringpsq ~pt baseCalculoICMSsq ~pppt java.lang.Stringpsq ~pt 	valorICMSsq ~pppt java.lang.Stringpsq ~pt baseCalculoICMSSubssq ~pppt java.lang.Stringpsq ~pt 
valorICMSSubssq ~pppt java.lang.Stringpsq ~pt 
valorTributossq ~pppt java.lang.Stringpsq ~pt valorIPIsq ~pppt java.lang.Stringpsq ~pt 	valorIRRFsq ~pppt java.lang.Stringpsq ~pt 
valorFretesq ~pppt java.lang.Stringpsq ~pt valorSegurosq ~pppt java.lang.Stringpsq ~pt 
valorDescontosq ~pppt java.lang.Stringpsq ~pt 
valorISSQNsq ~pppt java.lang.Stringpsq ~pt valorTotalProdutossq ~pppt java.lang.Stringpsq ~pt valorOutrasDespesassq ~pppt java.lang.Stringpsq ~pt valorTotalServicossq ~pppt java.lang.Stringpsq ~pt 
produtosJrsq ~pppt java.lang.Objectpsq ~pt 
observacoesJrsq ~pppt java.lang.Objectpsq ~pt valorBaseCalculoISSQNsq ~pppt java.lang.Stringpsq ~pt infAdicinfAdFiscosq ~pppt java.lang.Stringpsq ~pt nomeFormaPagamentosq ~pppt java.lang.Stringpsq ~pt 	faturasJrsq ~pppt java.lang.Objectpsq ~pt apresentarFaturassq ~pppq ~Tpppt NFeur *[Lnet.sf.jasperreports.engine.JRParameter;" *Ã`!  xp   sr 0net.sf.jasperreports.engine.base.JRBaseParameter      'Ø 	Z isForPromptingZ isSystemDefinedL defaultValueExpressionq ~ L descriptionq ~ L nameq ~ L nestedTypeNameq ~ L 
propertiesMapq ~ 1L valueClassNameq ~ L valueClassRealNameq ~ xpppt REPORT_PARAMETERS_MAPpsq ~pppt 
java.util.Mappsq ~	lppt 
JASPER_REPORTpsq ~pppt (net.sf.jasperreports.engine.JasperReportpsq ~	lppt REPORT_CONNECTIONpsq ~pppt java.sql.Connectionpsq ~	lppt REPORT_MAX_COUNTpsq ~pppt java.lang.Integerpsq ~	lppt REPORT_DATA_SOURCEpsq ~pppq ~Âpsq ~	lppt REPORT_SCRIPTLETpsq ~pppt /net.sf.jasperreports.engine.JRAbstractScriptletpsq ~	lppt 
REPORT_LOCALEpsq ~pppt java.util.Localepsq ~	lppt REPORT_RESOURCE_BUNDLEpsq ~pppt java.util.ResourceBundlepsq ~	lppt REPORT_TIME_ZONEpsq ~pppt java.util.TimeZonepsq ~	lppt REPORT_FORMAT_FACTORYpsq ~pppt .net.sf.jasperreports.engine.util.FormatFactorypsq ~	lppt REPORT_CLASS_LOADERpsq ~pppt java.lang.ClassLoaderpsq ~	lppt REPORT_URL_HANDLER_FACTORYpsq ~pppt  java.net.URLStreamHandlerFactorypsq ~	lppt REPORT_FILE_RESOLVERpsq ~pppt -net.sf.jasperreports.engine.util.FileResolverpsq ~	lppt REPORT_TEMPLATESpsq ~pppt java.util.Collectionpsq ~	lppt SORT_FIELDSpsq ~pppt java.util.Listpsq ~	lppt REPORT_VIRTUALIZERpsq ~pppt )net.sf.jasperreports.engine.JRVirtualizerpsq ~	lppt IS_IGNORE_PAGINATIONpsq ~pppq ~Tpsq ~	l  sq ~ ð    uq ~ ó   sq ~ õt ""t java.lang.Stringppt 
SUBREPORT_DIRpsq ~pppq ~	´psq ~psq ~ $   w   t ireport.zoomt 	ireport.xt 	ireport.yt ireport.scriptlethandlingt ireport.encodingxsr java.util.HashMapÚÁÃ`Ñ F 
loadFactorI 	thresholdxp?@     w      q ~	¹t 1.5q ~	½t 
ISO-8859-1q ~	ºt 0q ~	»t 279q ~	¼t 0xsr ,net.sf.jasperreports.engine.base.JRBaseQuery      'Ø [ chunkst +[Lnet/sf/jasperreports/engine/JRQueryChunk;L languageq ~ xppt sqlppppur )[Lnet.sf.jasperreports.engine.JRVariable;bæ|,·D  xp   sr /net.sf.jasperreports.engine.base.JRBaseVariable      'Ø I PSEUDO_SERIAL_VERSION_UIDB calculationB 
incrementTypeZ isSystemDefinedB 	resetTypeL calculationValuet 2Lnet/sf/jasperreports/engine/type/CalculationEnum;L 
expressionq ~ L incrementGroupq ~ 0L incrementTypeValuet 4Lnet/sf/jasperreports/engine/type/IncrementTypeEnum;L incrementerFactoryClassNameq ~ L incrementerFactoryClassRealNameq ~ L initialValueExpressionq ~ L nameq ~ L 
resetGroupq ~ 0L resetTypeValuet 0Lnet/sf/jasperreports/engine/type/ResetTypeEnum;L valueClassNameq ~ L valueClassRealNameq ~ xp  wî   ~r 0net.sf.jasperreports.engine.type.CalculationEnum          xq ~ t SYSTEMpp~r 2net.sf.jasperreports.engine.type.IncrementTypeEnum          xq ~ t NONEppsq ~ ð   uq ~ ó   sq ~ õt new java.lang.Integer(1)q ~	|pt PAGE_NUMBERp~r .net.sf.jasperreports.engine.type.ResetTypeEnum          xq ~ t REPORTq ~	|psq ~	Ë  wî   q ~	Ñppq ~	Ôppsq ~ ð   uq ~ ó   sq ~ õt new java.lang.Integer(1)q ~	|pt 
COLUMN_NUMBERp~q ~	Ût PAGEq ~	|psq ~	Ë  wî   ~q ~	Ðt COUNTsq ~ ð   uq ~ ó   sq ~ õt new java.lang.Integer(1)q ~	|ppq ~	Ôppsq ~ ð   uq ~ ó   sq ~ õt new java.lang.Integer(0)q ~	|pt REPORT_COUNTpq ~	Üq ~	|psq ~	Ë  wî   q ~	çsq ~ ð   uq ~ ó   sq ~ õt new java.lang.Integer(1)q ~	|ppq ~	Ôppsq ~ ð   uq ~ ó   sq ~ õt new java.lang.Integer(0)q ~	|pt 
PAGE_COUNTpq ~	äq ~	|psq ~	Ë  wî   q ~	çsq ~ ð   uq ~ ó   sq ~ õt new java.lang.Integer(1)q ~	|ppq ~	Ôppsq ~ ð   uq ~ ó   sq ~ õt new java.lang.Integer(0)q ~	|pt COLUMN_COUNTp~q ~	Ût COLUMNq ~	|p~r <net.sf.jasperreports.engine.type.WhenResourceMissingTypeEnum          xq ~ t EMPTYq ~	ip~r 0net.sf.jasperreports.engine.type.OrientationEnum          xq ~ t PORTRAITpp~r /net.sf.jasperreports.engine.type.PrintOrderEnum          xq ~ t VERTICALpppp~r 3net.sf.jasperreports.engine.type.WhenNoDataTypeEnum          xq ~ t ALL_SECTIONS_NO_DETAILsr 6net.sf.jasperreports.engine.design.JRReportCompileData      'Ø L crosstabCompileDataq ~L datasetCompileDataq ~L mainDatasetCompileDataq ~ xpsq ~	¾?@     w       xsq ~	¾?@     w       xur [B¬óøTà  xp  GUÊþº¾   . NFe_1566476890979_828598  ,net/sf/jasperreports/engine/fill/JREvaluator  parameter_REPORT_LOCALE 2Lnet/sf/jasperreports/engine/fill/JRFillParameter; parameter_JASPER_REPORT parameter_REPORT_VIRTUALIZER parameter_REPORT_TIME_ZONE parameter_SORT_FIELDS parameter_REPORT_FILE_RESOLVER parameter_REPORT_SCRIPTLET parameter_REPORT_PARAMETERS_MAP parameter_REPORT_CONNECTION parameter_REPORT_CLASS_LOADER parameter_REPORT_DATA_SOURCE $parameter_REPORT_URL_HANDLER_FACTORY parameter_IS_IGNORE_PAGINATION parameter_SUBREPORT_DIR parameter_REPORT_FORMAT_FACTORY parameter_REPORT_MAX_COUNT parameter_REPORT_TEMPLATES  parameter_REPORT_RESOURCE_BUNDLE field_enderecoPrestador .Lnet/sf/jasperreports/engine/fill/JRFillField; field_valorFrete field_serieNota field_municipioPrestacao field_valorICMSSubs 
field_valorIR  field_inscricaoEstadualPrestador field_nomeFormaPagamento field_outrasInformacoes field_naturezaOperacao field_observacoesJr field_cnpjTomador field_valorTotalNota field_reterISS field_codigoServico field_valorCOFINS field_valorISSQN field_logomarcaPrefeitura field_valorDesconto field_cepPrestador field_numeroRPS field_dataHoraEmissao field_horaSaida field_dataSaida field_numeroNota field_dataServico field_valorCSLL field_bairroPrestador field_cnpjPrestador field_chaveAcesso field_enderecoTomador field_valorINSS field_produtosJr field_incentivadorCultural field_valorSeguro field_faturasJr field_valorICMS field_deducoesPermitidas field_municipioPrestador field_inscricaoEstadualTomador field_emailPrestador field_simplesNacional field_valorIRRF field_issRetidoValor field_cidadePrestacao field_ufPrestador %field_descontoIncondicionadoMunicipio field_ufTomador !field_inscricaoMunicipalPrestador field_razaoSocialPrestador field_baseCalculo field_dataCompetencia field_valorPIS field_valorOutrasDespesas field_baseCalculoICMSSubs field_telefoneTomador field_aliquota field_apresentarFaturas field_retencoesFederais field_bairroTomador field_municipioTomador field_valorTotalServicos field_inscricaoMunicipalTomador field_valorISS field_complementoPrestador field_valorIPI field_cepTomador field_descontoIncondicionado field_valorLiquido field_emailTomador field_codigoAutorizacao field_regimeEspecial field_valorTotalProdutos field_outrasRetencoes field_valorTributos field_telefonePrestador field_descontoCondicionado field_servicosDescricao field_razaoSocialTomador field_baseCalculoICMS field_logomarcaPrestador field_valorBaseCalculoISSQN field_dataEmissao field_protocoloAutorizacao field_complementoTomador field_infAdicinfAdFisco field_valorServicos variable_PAGE_NUMBER 1Lnet/sf/jasperreports/engine/fill/JRFillVariable; variable_COLUMN_NUMBER variable_REPORT_COUNT variable_PAGE_COUNT variable_COLUMN_COUNT <init> ()V Code v w
  y  	  {  	  }  	   	 	   
 	    	    	   
 	    	    	    	    	    	    	    	    	    	    	    	    	  ¡  	  £  	  ¥  	  §  	  ©  	  «   	  ­ ! 	  ¯ " 	  ± # 	  ³ $ 	  µ % 	  · & 	  ¹ ' 	  » ( 	  ½ ) 	  ¿ * 	  Á + 	  Ã , 	  Å - 	  Ç . 	  É / 	  Ë 0 	  Í 1 	  Ï 2 	  Ñ 3 	  Ó 4 	  Õ 5 	  × 6 	  Ù 7 	  Û 8 	  Ý 9 	  ß : 	  á ; 	  ã < 	  å = 	  ç > 	  é ? 	  ë @ 	  í A 	  ï B 	  ñ C 	  ó D 	  õ E 	  ÷ F 	  ù G 	  û H 	  ý I 	  ÿ J 	  K 	  L 	  M 	  N 	 	 O 	  P 	 
 Q 	  R 	  S 	  T 	  U 	  V 	  W 	  X 	  Y 	  Z 	 ! [ 	 # \ 	 % ] 	 ' ^ 	 ) _ 	 + ` 	 - a 	 / b 	 1 c 	 3 d 	 5 e 	 7 f 	 9 g 	 ; h 	 = i 	 ? j 	 A k 	 C l 	 E m 	 G n 	 I o 	 K p q	 M r q	 O s q	 Q t q	 S u q	 U LineNumberTable customizedInit 0(Ljava/util/Map;Ljava/util/Map;Ljava/util/Map;)V 
initParams (Ljava/util/Map;)VZ[
 \ 
initFields^[
 _ initVarsa[
 b 
REPORT_LOCALEd 
java/util/Mapf get &(Ljava/lang/Object;)Ljava/lang/Object;higj 0net/sf/jasperreports/engine/fill/JRFillParameterl 
JASPER_REPORTn REPORT_VIRTUALIZERp REPORT_TIME_ZONEr SORT_FIELDSt REPORT_FILE_RESOLVERv REPORT_SCRIPTLETx REPORT_PARAMETERS_MAPz REPORT_CONNECTION| REPORT_CLASS_LOADER~ REPORT_DATA_SOURCE REPORT_URL_HANDLER_FACTORY IS_IGNORE_PAGINATION 
SUBREPORT_DIR REPORT_FORMAT_FACTORY REPORT_MAX_COUNT REPORT_TEMPLATES REPORT_RESOURCE_BUNDLE enderecoPrestador ,net/sf/jasperreports/engine/fill/JRFillField 
valorFrete 	serieNota municipioPrestacao 
valorICMSSubs valorIR inscricaoEstadualPrestador nomeFormaPagamento  outrasInformacoes¢ naturezaOperacao¤ 
observacoesJr¦ cnpjTomador¨ valorTotalNotaª reterISS¬ 
codigoServico® valorCOFINS° 
valorISSQN² logomarcaPrefeitura´ 
valorDesconto¶ cepPrestador¸ 	numeroRPSº dataHoraEmissao¼ 	horaSaida¾ 	dataSaidaÀ 
numeroNotaÂ dataServicoÄ 	valorCSLLÆ bairroPrestadorÈ 
cnpjPrestadorÊ chaveAcessoÌ enderecoTomadorÎ 	valorINSSÐ 
produtosJrÒ incentivadorCulturalÔ valorSeguroÖ 	faturasJrØ 	valorICMSÚ deducoesPermitidasÜ municipioPrestadorÞ inscricaoEstadualTomadorà emailPrestadorâ simplesNacionalä 	valorIRRFæ issRetidoValorè cidadePrestacaoê ufPrestadorì descontoIncondicionadoMunicipioî 	ufTomadorð inscricaoMunicipalPrestadorò razaoSocialPrestadorô baseCalculoö dataCompetenciaø valorPISú valorOutrasDespesasü baseCalculoICMSSubsþ telefoneTomador  aliquota apresentarFaturas retencoesFederais 
bairroTomador municipioTomador
 valorTotalServicos inscricaoMunicipalTomador valorISS complementoPrestador valorIPI 
cepTomador descontoIncondicionado valorLiquido emailTomador codigoAutorizacao regimeEspecial  valorTotalProdutos" outrasRetencoes$ 
valorTributos& telefonePrestador( descontoCondicionado* servicosDescricao, razaoSocialTomador. baseCalculoICMS0 logomarcaPrestador2 valorBaseCalculoISSQN4 dataEmissao6 protocoloAutorizacao8 complementoTomador: infAdicinfAdFisco< 
valorServicos> PAGE_NUMBER@ /net/sf/jasperreports/engine/fill/JRFillVariableB 
COLUMN_NUMBERD REPORT_COUNTF 
PAGE_COUNTH COLUMN_COUNTJ evaluate (I)Ljava/lang/Object; 
Exceptions java/lang/ThrowableO  Q java/lang/IntegerS (I)V vU
TV getValue ()Ljava/lang/Object;XY
Z java/lang/String\ java/lang/Boolean^ valueOf (Z)Ljava/lang/Boolean;`a
_b equals (Ljava/lang/Object;)Zde
_f java/io/InputStreamh java/lang/StringBufferj NÂº l (Ljava/lang/String;)V vn
ko append ,(Ljava/lang/String;)Ljava/lang/StringBuffer;qr
ks toString ()Ljava/lang/String;uv
kw SÃRIE y (net/sf/jasperreports/engine/JRDataSource{
mZ &(Ljava/lang/Object;)Ljava/lang/String;`~
] NFe_Produtos.jasper FONE:  NFe_Observacoes.jasper java/util/Date
 y NFe_Faturas.jasper evaluateOld getOldValueY
 evaluateEstimated 
SourceFile !     n                 	     
               
                                                                                                !     "     #     $     %     &     '     (     )     *     +     ,     -     .     /     0     1     2     3     4     5     6     7     8     9     :     ;     <     =     >     ?     @     A     B     C     D     E     F     G     H     I     J     K     L     M     N     O     P     Q     R     S     T     U     V     W     X     Y     Z     [     \     ]     ^     _     `     a     b     c     d     e     f     g     h     i     j     k     l     m     n     o     p q    r q    s q    t q    u q     v w  x  ÿ    +*· z*µ |*µ ~*µ *µ *µ *µ *µ *µ *µ *µ *µ *µ *µ *µ *µ *µ *µ *µ *µ  *µ ¢*µ ¤*µ ¦*µ ¨*µ ª*µ ¬*µ ®*µ °*µ ²*µ ´*µ ¶*µ ¸*µ º*µ ¼*µ ¾*µ À*µ Â*µ Ä*µ Æ*µ È*µ Ê*µ Ì*µ Î*µ Ð*µ Ò*µ Ô*µ Ö*µ Ø*µ Ú*µ Ü*µ Þ*µ à*µ â*µ ä*µ æ*µ è*µ ê*µ ì*µ î*µ ð*µ ò*µ ô*µ ö*µ ø*µ ú*µ ü*µ þ*µ *µ*µ*µ*µ*µ
*µ*µ*µ*µ*µ*µ*µ*µ*µ*µ*µ *µ"*µ$*µ&*µ(*µ**µ,*µ.*µ0*µ2*µ4*µ6*µ8*µ:*µ<*µ>*µ@*µB*µD*µF*µH*µJ*µL*µN*µP*µR*µT*µV±   W  Â p      	          ! " " ' # , $ 1 % 6 & ; ' @ ( E ) J * O + T , Y - ^ . c / h 0 m 1 r 2 w 3 | 4  5  6  7  8  9  :  ; ¤ < © = ® > ³ ? ¸ @ ½ A Â B Ç C Ì D Ñ E Ö F Û G à H å I ê J ï K ô L ù M þ N O P
 Q R S T! U& V+ W0 X5 Y: Z? [D \I ]N ^S _X `] ab bg cl dq ev f{ g h i j k l m n£ o¨ p­ q² r· s¼ tÁ uÆ vË wÐ xÕ yÚ zß {ä |é }î ~ó ø ý         % *  XY  x   4     *+·]*,·`*-·c±   W          
    Z[  x  ·    W*+e¹k ÀmÀmµ |*+o¹k ÀmÀmµ ~*+q¹k ÀmÀmµ *+s¹k ÀmÀmµ *+u¹k ÀmÀmµ *+w¹k ÀmÀmµ *+y¹k ÀmÀmµ *+{¹k ÀmÀmµ *+}¹k ÀmÀmµ *+¹k ÀmÀmµ *+¹k ÀmÀmµ *+¹k ÀmÀmµ *+¹k ÀmÀmµ *+¹k ÀmÀmµ *+¹k ÀmÀmµ *+¹k ÀmÀmµ *+¹k ÀmÀmµ *+¹k ÀmÀmµ ±   W   N       ¡ & ¢ 9 £ L ¤ _ ¥ r ¦  §  ¨ « © ¾ ª Ñ « ä ¬ ÷ ­
 ® ¯0 °C ±V ² ^[  x  ê    v*+¹k ÀÀµ  *+¹k ÀÀµ ¢*+¹k ÀÀµ ¤*+¹k ÀÀµ ¦*+¹k ÀÀµ ¨*+¹k ÀÀµ ª*+¹k ÀÀµ ¬*+¡¹k ÀÀµ ®*+£¹k ÀÀµ °*+¥¹k ÀÀµ ²*+§¹k ÀÀµ ´*+©¹k ÀÀµ ¶*+«¹k ÀÀµ ¸*+­¹k ÀÀµ º*+¯¹k ÀÀµ ¼*+±¹k ÀÀµ ¾*+³¹k ÀÀµ À*+µ¹k ÀÀµ Â*+·¹k ÀÀµ Ä*+¹¹k ÀÀµ Æ*+»¹k ÀÀµ È*+½¹k ÀÀµ Ê*+¿¹k ÀÀµ Ì*+Á¹k ÀÀµ Î*+Ã¹k ÀÀµ Ð*+Å¹k ÀÀµ Ò*+Ç¹k ÀÀµ Ô*+É¹k ÀÀµ Ö*+Ë¹k ÀÀµ Ø*+Í¹k ÀÀµ Ú*+Ï¹k ÀÀµ Ü*+Ñ¹k ÀÀµ Þ*+Ó¹k ÀÀµ à*+Õ¹k ÀÀµ â*+×¹k ÀÀµ ä*+Ù¹k ÀÀµ æ*+Û¹k ÀÀµ è*+Ý¹k ÀÀµ ê*+ß¹k ÀÀµ ì*+á¹k ÀÀµ î*+ã¹k ÀÀµ ð*+å¹k ÀÀµ ò*+ç¹k ÀÀµ ô*+é¹k ÀÀµ ö*+ë¹k ÀÀµ ø*+í¹k ÀÀµ ú*+ï¹k ÀÀµ ü*+ñ¹k ÀÀµ þ*+ó¹k ÀÀµ *+õ¹k ÀÀµ*+÷¹k ÀÀµ*+ù¹k ÀÀµ*+û¹k ÀÀµ*+ý¹k ÀÀµ
*+ÿ¹k ÀÀµ*+¹k ÀÀµ*+¹k ÀÀµ*+¹k ÀÀµ*+¹k ÀÀµ*+	¹k ÀÀµ*+¹k ÀÀµ*+
¹k ÀÀµ*+¹k ÀÀµ*+¹k ÀÀµ*+¹k ÀÀµ *+¹k ÀÀµ"*+¹k ÀÀµ$*+¹k ÀÀµ&*+¹k ÀÀµ(*+¹k ÀÀµ**+¹k ÀÀµ,*+!¹k ÀÀµ.*+#¹k ÀÀµ0*+%¹k ÀÀµ2*+'¹k ÀÀµ4*+)¹k ÀÀµ6*++¹k ÀÀµ8*+-¹k ÀÀµ:*+/¹k ÀÀµ<*+1¹k ÀÀµ>*+3¹k ÀÀµ@*+5¹k ÀÀµB*+7¹k ÀÀµD*+9¹k ÀÀµF*+;¹k ÀÀµH*+=¹k ÀÀµJ*+?¹k ÀÀµL±   W  b X   º  » & ¼ 9 ½ L ¾ _ ¿ r À  Á  Â « Ã ¾ Ä Ñ Å ä Æ ÷ Ç
 È É0 ÊC ËV Ìi Í| Î Ï¢ Ðµ ÑÈ ÒÛ Óî Ô Õ Ö' ×: ØM Ù` Ús Û Ü Ý¬ Þ¿ ßÒ àå áø â ã ä1 åD æW çj è} é ê£ ë¶ ìÉ íÜ îï ï ð ñ( ò; óN ôa õt ö ÷ ø­ ùÀ úÓ ûæ üù ý þ ÿ2 EXk~¤·ÊÝ	ð
)
<Obu a[  x        `*+A¹k ÀCÀCµN*+E¹k ÀCÀCµP*+G¹k ÀCÀCµR*+I¹k ÀCÀCµT*+K¹k ÀCÀCµV±   W        & 9 L _ LM N    P x  ¸    tMª  o       D  !  (  4  @  L  X  d  p  |      ¤  ²  À  Î  Ü  ê      (  6  N  \  j  x      ¢  °  ¾  Ü  ú      $  2  @  N  \  t        ¬  º  È  Ö  ä  ò         *  8  Y  w      ¡  ¯  ½  Ë  Ù  ú      +  C  QRM§J»TY·WM§>»TY·WM§2»TY·WM§&»TY·WM§»TY·WM§»TY·WM§»TY·WM§ö»TY·WM§ê*´ Ú¶[À]M§Ü*´F¶[À]M§Î*´¶[À]M§À*´ Ø¶[À]M§²*´  ¶[À]M§¤*´<¶[À]M§*´ ¶¶[À]M§*´¶[À_¸c¶g¸cM§p*´ Î¶[À]M§b*´¶[À_¸c¶g¸cM§J*´ Ü¶[À]M§<*´¶[À_¸c¶g¸cM§$*´¶[À]M§*´$¶[À]M§*´¶[À]M§ú*´@¶[ÀiM§ì*´ ¶[À]M§Þ*´ Ö¶[À]M§Ð*´ ì¶[À]M§Â*´ Æ¶[À]M§´»kYm·p*´ Ð¶[À]¶t¶xM§»kYz·p*´ ¤¶[À]¶t¶xM§x*´ ²¶[À]M§j*´ ¬¶[À]M§\*´D¶[À]M§N*´¶[À]M§@*´ þ¶[À]M§2*´ î¶[À]M§$*´ Ì¶[À]M§*´¶[À_¸c¶g¸cM§þ*´>¶[À]M§ð*´ è¶[À]M§â*´¶[À]M§Ô*´ ¨¶[À]M§Æ*´4¶[À]M§¸*´0¶[À]M§ª*´ ¢¶[À]M§*´ ä¶[À]M§*´ Ä¶[À]M§*´
¶[À]M§r*´"¶[À]M§d*´ ô¶[À]M§V*´ ¸¶[À]M§H*´ à¶[À|M§:»kY*´ ¶}À]¸·p¶t¶xM§»kY·p*´6¶[À]¶t¶xM§ û*´ Ú¶[À]M§ í*´B¶[À]M§ ß*´ À¶[À]M§ Ñ*´ ¬¶[À]M§ Ã*´¶[À]M§ µ*´J¶[À]M§ §*´ ´¶[À|M§ »kY*´ ¶}À]¸·p¶t¶xM§ x»Y·M§ m*´¶[À_¸c¶g¸cM§ U*´ ®¶[À]M§ G*´¶[À_¸c¶g¸cM§ /*´ æ¶[À|M§ !»kY*´ ¶}À]¸·p¶t¶xM,°   W  2   & ($,(-+14276@7C;L<O@XA[EdFgJpKsO|PTUYZ^¤_§c²dµhÀiÃmÎnÑrÜsßwêxí|}(+69NQ\_jmx {¤¥©ª®¢¯¥³°´³¸¾¹Á½Ü¾ßÂúÃýÇÈÌÍÑ$Ò'Ö2×5Û@ÜCàNáQå\æ_êtëwïðôõùú¡þ¬ÿ¯º½È	Ë
ÖÙäçòõ !"&'+*,-081;5Y6\:w;z?@DEI¡J¤N¯O²S½TÀXËYÎ]Ù^Übúcýghlm q+r.vCwF{Q|Tr M N    P x  ¸    tMª  o       D  !  (  4  @  L  X  d  p  |      ¤  ²  À  Î  Ü  ê      (  6  N  \  j  x      ¢  °  ¾  Ü  ú      $  2  @  N  \  t        ¬  º  È  Ö  ä  ò         *  8  Y  w      ¡  ¯  ½  Ë  Ù  ú      +  C  QRM§J»TY·WM§>»TY·WM§2»TY·WM§&»TY·WM§»TY·WM§»TY·WM§»TY·WM§ö»TY·WM§ê*´ Ú¶À]M§Ü*´F¶À]M§Î*´¶À]M§À*´ Ø¶À]M§²*´  ¶À]M§¤*´<¶À]M§*´ ¶¶À]M§*´¶À_¸c¶g¸cM§p*´ Î¶À]M§b*´¶À_¸c¶g¸cM§J*´ Ü¶À]M§<*´¶À_¸c¶g¸cM§$*´¶À]M§*´$¶À]M§*´¶À]M§ú*´@¶ÀiM§ì*´ ¶À]M§Þ*´ Ö¶À]M§Ð*´ ì¶À]M§Â*´ Æ¶À]M§´»kYm·p*´ Ð¶À]¶t¶xM§»kYz·p*´ ¤¶À]¶t¶xM§x*´ ²¶À]M§j*´ ¬¶À]M§\*´D¶À]M§N*´¶À]M§@*´ þ¶À]M§2*´ î¶À]M§$*´ Ì¶À]M§*´¶À_¸c¶g¸cM§þ*´>¶À]M§ð*´ è¶À]M§â*´¶À]M§Ô*´ ¨¶À]M§Æ*´4¶À]M§¸*´0¶À]M§ª*´ ¢¶À]M§*´ ä¶À]M§*´ Ä¶À]M§*´
¶À]M§r*´"¶À]M§d*´ ô¶À]M§V*´ ¸¶À]M§H*´ à¶À|M§:»kY*´ ¶}À]¸·p¶t¶xM§»kY·p*´6¶À]¶t¶xM§ û*´ Ú¶À]M§ í*´B¶À]M§ ß*´ À¶À]M§ Ñ*´ ¬¶À]M§ Ã*´¶À]M§ µ*´J¶À]M§ §*´ ´¶À|M§ »kY*´ ¶}À]¸·p¶t¶xM§ x»Y·M§ m*´¶À_¸c¶g¸cM§ U*´ ®¶À]M§ G*´¶À_¸c¶g¸cM§ /*´ æ¶À|M§ !»kY*´ ¶}À]¸·p¶t¶xM,°   W  2    $(+47¡@¢C¦L§O«X¬[°d±gµp¶sº|»¿ÀÄÅÉ¤Ê§Î²ÏµÓÀÔÃØÎÙÑÝÜÞßâêãíçèìíñ(ò+ö6÷9ûNüQ \_jm
x{¢¥°³#¾$Á(Ü)ß-ú.ý2378<$='A2B5F@GCKNLQP\Q_UtVwZ[_`de¡i¬j¯nºo½sÈtËxÖyÙ}ä~çòõ *-8; Y¡\¥w¦zª«¯°´¡µ¤¹¯º²¾½¿ÀÃËÄÎÈÙÉÜÍúÎýÒÓ×Ø Ü+Ý.áCâFæQçTëró M N    P x  ¸    tMª  o       D  !  (  4  @  L  X  d  p  |      ¤  ²  À  Î  Ü  ê      (  6  N  \  j  x      ¢  °  ¾  Ü  ú      $  2  @  N  \  t        ¬  º  È  Ö  ä  ò         *  8  Y  w      ¡  ¯  ½  Ë  Ù  ú      +  C  QRM§J»TY·WM§>»TY·WM§2»TY·WM§&»TY·WM§»TY·WM§»TY·WM§»TY·WM§ö»TY·WM§ê*´ Ú¶[À]M§Ü*´F¶[À]M§Î*´¶[À]M§À*´ Ø¶[À]M§²*´  ¶[À]M§¤*´<¶[À]M§*´ ¶¶[À]M§*´¶[À_¸c¶g¸cM§p*´ Î¶[À]M§b*´¶[À_¸c¶g¸cM§J*´ Ü¶[À]M§<*´¶[À_¸c¶g¸cM§$*´¶[À]M§*´$¶[À]M§*´¶[À]M§ú*´@¶[ÀiM§ì*´ ¶[À]M§Þ*´ Ö¶[À]M§Ð*´ ì¶[À]M§Â*´ Æ¶[À]M§´»kYm·p*´ Ð¶[À]¶t¶xM§»kYz·p*´ ¤¶[À]¶t¶xM§x*´ ²¶[À]M§j*´ ¬¶[À]M§\*´D¶[À]M§N*´¶[À]M§@*´ þ¶[À]M§2*´ î¶[À]M§$*´ Ì¶[À]M§*´¶[À_¸c¶g¸cM§þ*´>¶[À]M§ð*´ è¶[À]M§â*´¶[À]M§Ô*´ ¨¶[À]M§Æ*´4¶[À]M§¸*´0¶[À]M§ª*´ ¢¶[À]M§*´ ä¶[À]M§*´ Ä¶[À]M§*´
¶[À]M§r*´"¶[À]M§d*´ ô¶[À]M§V*´ ¸¶[À]M§H*´ à¶[À|M§:»kY*´ ¶}À]¸·p¶t¶xM§»kY·p*´6¶[À]¶t¶xM§ û*´ Ú¶[À]M§ í*´B¶[À]M§ ß*´ À¶[À]M§ Ñ*´ ¬¶[À]M§ Ã*´¶[À]M§ µ*´J¶[À]M§ §*´ ´¶[À|M§ »kY*´ ¶}À]¸·p¶t¶xM§ x»Y·M§ m*´¶[À_¸c¶g¸cM§ U*´ ®¶[À]M§ G*´¶[À_¸c¶g¸cM§ /*´ æ¶[À|M§ !»kY*´ ¶}À]¸·p¶t¶xM,°   W  2   ü þ$(+47@
CLOX[dg p!s%|&*+/04¤5§9²:µ>À?ÃCÎDÑHÜIßMêNíRSWX\(]+a6b9fNgQk\l_pjqmuxv{z{¢¥°³¾ÁÜßúý¢£§$¨'¬2­5±@²C¶N·Q»\¼_ÀtÁwÅÆÊËÏÐ¡Ô¬Õ¯ÙºÚ½ÞÈßËãÖäÙèäéçíòîõò ó÷øüý*-8;Y\wz¡ ¤$¯%²)½*À.Ë/Î3Ù4Ü8ú9ý=>BC G+H.LCMFQQRTVr^     t _1566476890979_828598t 2net.sf.jasperreports.engine.design.JRJavacCompiler