¬í sr (net.sf.jasperreports.engine.JasperReport      'Ø L compileDatat Ljava/io/Serializable;L compileNameSuffixt Ljava/lang/String;L 
compilerClassq ~ xr -net.sf.jasperreports.engine.base.JRBaseReport      'Ø +I PSEUDO_SERIAL_VERSION_UIDI bottomMarginI columnCountI 
columnSpacingI columnWidthZ ignorePaginationZ isFloatColumnFooterZ isSummaryNewPageZ  isSummaryWithPageHeaderAndFooterZ isTitleNewPageI 
leftMarginB orientationI 
pageHeightI 	pageWidthB 
printOrderI rightMarginI 	topMarginB whenNoDataTypeL 
backgroundt $Lnet/sf/jasperreports/engine/JRBand;L columnFooterq ~ L columnHeaderq ~ [ datasetst ([Lnet/sf/jasperreports/engine/JRDataset;L defaultFontt *Lnet/sf/jasperreports/engine/JRReportFont;L defaultStylet %Lnet/sf/jasperreports/engine/JRStyle;L detailq ~ L 
detailSectiont 'Lnet/sf/jasperreports/engine/JRSection;[ fontst +[Lnet/sf/jasperreports/engine/JRReportFont;L formatFactoryClassq ~ L 
importsSett Ljava/util/Set;L languageq ~ L lastPageFooterq ~ L mainDatasett 'Lnet/sf/jasperreports/engine/JRDataset;L nameq ~ L noDataq ~ L orientationValuet 2Lnet/sf/jasperreports/engine/type/OrientationEnum;L 
pageFooterq ~ L 
pageHeaderq ~ L printOrderValuet 1Lnet/sf/jasperreports/engine/type/PrintOrderEnum;[ stylest &[Lnet/sf/jasperreports/engine/JRStyle;L summaryq ~ [ 	templatest /[Lnet/sf/jasperreports/engine/JRReportTemplate;L titleq ~ L whenNoDataTypeValuet 5Lnet/sf/jasperreports/engine/type/WhenNoDataTypeEnum;xp  wî            +           J  S        sr +net.sf.jasperreports.engine.base.JRBaseBand      'Ø I PSEUDO_SERIAL_VERSION_UIDI heightZ isSplitAllowedL printWhenExpressiont *Lnet/sf/jasperreports/engine/JRExpression;L 	splitTypet Ljava/lang/Byte;L splitTypeValuet 0Lnet/sf/jasperreports/engine/type/SplitTypeEnum;xr 3net.sf.jasperreports.engine.base.JRBaseElementGroup      'Ø L childrent Ljava/util/List;L elementGroupt ,Lnet/sf/jasperreports/engine/JRElementGroup;xpsr java.util.ArrayListxÒÇa I sizexp    w   
xp  wî    pp~r .net.sf.jasperreports.engine.type.SplitTypeEnum          xr java.lang.Enum          xpt STRETCHpsq ~ sq ~    
w   
sr 1net.sf.jasperreports.engine.base.JRBaseStaticText      'Ø L textq ~ xr 2net.sf.jasperreports.engine.base.JRBaseTextElement      'Ø %I PSEUDO_SERIAL_VERSION_UIDL borderq ~ L borderColort Ljava/awt/Color;L bottomBorderq ~ L bottomBorderColorq ~ $L 
bottomPaddingt Ljava/lang/Integer;L fontNameq ~ L fontSizeq ~ %L horizontalAlignmentq ~ L horizontalAlignmentValuet 6Lnet/sf/jasperreports/engine/type/HorizontalAlignEnum;L isBoldt Ljava/lang/Boolean;L isItalicq ~ 'L 
isPdfEmbeddedq ~ 'L isStrikeThroughq ~ 'L isStyledTextq ~ 'L isUnderlineq ~ 'L 
leftBorderq ~ L leftBorderColorq ~ $L leftPaddingq ~ %L lineBoxt 'Lnet/sf/jasperreports/engine/JRLineBox;L lineSpacingq ~ L lineSpacingValuet 2Lnet/sf/jasperreports/engine/type/LineSpacingEnum;L markupq ~ L paddingq ~ %L pdfEncodingq ~ L pdfFontNameq ~ L 
reportFontq ~ L rightBorderq ~ L rightBorderColorq ~ $L rightPaddingq ~ %L rotationq ~ L 
rotationValuet /Lnet/sf/jasperreports/engine/type/RotationEnum;L 	topBorderq ~ L topBorderColorq ~ $L 
topPaddingq ~ %L verticalAlignmentq ~ L verticalAlignmentValuet 4Lnet/sf/jasperreports/engine/type/VerticalAlignEnum;xr .net.sf.jasperreports.engine.base.JRBaseElement      'Ø I PSEUDO_SERIAL_VERSION_UIDI heightZ isPrintInFirstWholeBandZ isPrintRepeatedValuesZ isPrintWhenDetailOverflowsZ isRemoveLineWhenBlankB positionTypeB stretchTypeI widthI xI yL 	backcolorq ~ $L defaultStyleProvidert 4Lnet/sf/jasperreports/engine/JRDefaultStyleProvider;L elementGroupq ~ L 	forecolorq ~ $L keyq ~ L modeq ~ L 	modeValuet +Lnet/sf/jasperreports/engine/type/ModeEnum;L parentStyleq ~ L parentStyleNameReferenceq ~ L positionTypeValuet 3Lnet/sf/jasperreports/engine/type/PositionTypeEnum;L printWhenExpressionq ~ L printWhenGroupChangest %Lnet/sf/jasperreports/engine/JRGroup;L 
propertiesMapt -Lnet/sf/jasperreports/engine/JRPropertiesMap;[ propertyExpressionst 3[Lnet/sf/jasperreports/engine/JRPropertyExpression;L stretchTypeValuet 2Lnet/sf/jasperreports/engine/type/StretchTypeEnum;xp  wî           :        pq ~ q ~  pt staticText-1pppp~r 1net.sf.jasperreports.engine.type.PositionTypeEnum          xq ~ t FLOATpppp~r 0net.sf.jasperreports.engine.type.StretchTypeEnum          xq ~ t 
NO_STRETCH  wîppppppsr java.lang.Integerâ ¤÷8 I valuexr java.lang.Number¬à  xp   	ppsr java.lang.BooleanÍ rÕúî Z valuexpppppppppsr .net.sf.jasperreports.engine.base.JRBaseLineBox      'Ø L 
bottomPaddingq ~ %L 	bottomPent +Lnet/sf/jasperreports/engine/base/JRBoxPen;L boxContainert ,Lnet/sf/jasperreports/engine/JRBoxContainer;L leftPaddingq ~ %L leftPenq ~ BL paddingq ~ %L penq ~ BL rightPaddingq ~ %L rightPenq ~ BL 
topPaddingq ~ %L topPenq ~ Bxppsr 3net.sf.jasperreports.engine.base.JRBaseBoxBottomPen      'Ø  xr -net.sf.jasperreports.engine.base.JRBaseBoxPen      'Ø L lineBoxq ~ (xr *net.sf.jasperreports.engine.base.JRBasePen      'Ø I PSEUDO_SERIAL_VERSION_UIDL 	lineColorq ~ $L 	lineStyleq ~ L lineStyleValuet 0Lnet/sf/jasperreports/engine/type/LineStyleEnum;L 	lineWidtht Ljava/lang/Float;L penContainert ,Lnet/sf/jasperreports/engine/JRPenContainer;xp  wîppppq ~ Dq ~ Dq ~ 4psr 1net.sf.jasperreports.engine.base.JRBaseBoxLeftPen      'Ø  xq ~ F  wîppppq ~ Dq ~ Dpsq ~ F  wîppppq ~ Dq ~ Dpsr 2net.sf.jasperreports.engine.base.JRBaseBoxRightPen      'Ø  xq ~ F  wîppppq ~ Dq ~ Dpsr 0net.sf.jasperreports.engine.base.JRBaseBoxTopPen      'Ø  xq ~ F  wîppppq ~ Dq ~ Dpppppt Helvetica-Boldpppppppppppt TransaÃ§Ã£osq ~ "  wî           V   h    pq ~ q ~  pt staticText-1ppppq ~ 7ppppq ~ :  wîppppppq ~ >ppq ~ @ppppppppsq ~ Apsq ~ E  wîppppq ~ Wq ~ Wq ~ Upsq ~ L  wîppppq ~ Wq ~ Wpsq ~ F  wîppppq ~ Wq ~ Wpsq ~ O  wîppppq ~ Wq ~ Wpsq ~ Q  wîppppq ~ Wq ~ Wpppppt Helvetica-Boldpppppppppppt Titularsq ~ "  wî           -   ;    pq ~ q ~  pt staticText-1ppppq ~ 7ppppq ~ :  wîppppppq ~ >ppq ~ @ppppppppsq ~ Apsq ~ E  wîppppq ~ aq ~ aq ~ _psq ~ L  wîppppq ~ aq ~ apsq ~ F  wîppppq ~ aq ~ apsq ~ O  wîppppq ~ aq ~ apsq ~ Q  wîppppq ~ aq ~ apppppt Helvetica-Boldpppppppppppt Valorsq ~ "  wî           4   ¾    pq ~ q ~  pt staticText-1ppppq ~ 7ppppq ~ :  wîppppppq ~ >ppq ~ @ppppppppsq ~ Apsq ~ E  wîppppq ~ kq ~ kq ~ ipsq ~ L  wîppppq ~ kq ~ kpsq ~ F  wîppppq ~ kq ~ kpsq ~ O  wîppppq ~ kq ~ kpsq ~ Q  wîppppq ~ kq ~ kpppppt Helvetica-Boldpppppppppppt Sit.sq ~ "  wî           (   ü    pq ~ q ~  pt staticText-1ppppq ~ 7ppppq ~ :  wîppppppq ~ >ppq ~ @ppppppppsq ~ Apsq ~ E  wîppppq ~ uq ~ uq ~ spsq ~ L  wîppppq ~ uq ~ upsq ~ F  wîppppq ~ uq ~ upsq ~ O  wîppppq ~ uq ~ upsq ~ Q  wîppppq ~ uq ~ upppppt Helvetica-Boldpppppppppppt Aut.sq ~ "  wî           4  $    pq ~ q ~  pt staticText-1ppppq ~ 7ppppq ~ :  wîppppppq ~ >ppq ~ @ppppppppsq ~ Apsq ~ E  wîppppq ~ q ~ q ~ }psq ~ L  wîppppq ~ q ~ psq ~ F  wîppppq ~ q ~ psq ~ O  wîppppq ~ q ~ psq ~ Q  wîppppq ~ q ~ pppppt Helvetica-Boldpppppppppppt Band.sq ~ "  wî           H  X    pq ~ q ~  pt staticText-1ppppq ~ 7ppppq ~ :  wîppppppq ~ >ppq ~ @ppppppppsq ~ Apsq ~ E  wîppppq ~ q ~ q ~ psq ~ L  wîppppq ~ q ~ psq ~ F  wîppppq ~ q ~ psq ~ O  wîppppq ~ q ~ psq ~ Q  wîppppq ~ q ~ pppppt Helvetica-Boldpppppppppppt CartÃ£osq ~ "  wî           d       pq ~ q ~  pt staticText-1ppppq ~ 7ppppq ~ :  wîppppppq ~ >ppq ~ @ppppppppsq ~ Apsq ~ E  wîppppq ~ q ~ q ~ psq ~ L  wîppppq ~ q ~ psq ~ F  wîppppq ~ q ~ psq ~ O  wîppppq ~ q ~ psq ~ Q  wîppppq ~ q ~ pppppt Helvetica-Boldpppppppppppt 	Data/Horasq ~ "  wî           '      pq ~ q ~  pt staticText-1ppppq ~ 7ppppq ~ :  wîppppppq ~ >ppq ~ @ppppppppsq ~ Apsq ~ E  wîppppq ~ q ~ q ~ psq ~ L  wîppppq ~ q ~ psq ~ F  wîppppq ~ q ~ psq ~ O  wîppppq ~ q ~ psq ~ Q  wîppppq ~ q ~ pppppt Helvetica-Boldpppppppppppt UsuÃ¡riosr +net.sf.jasperreports.engine.base.JRBaseLine      'Ø I PSEUDO_SERIAL_VERSION_UIDB 	directionL directionValuet 4Lnet/sf/jasperreports/engine/type/LineDirectionEnum;xr 5net.sf.jasperreports.engine.base.JRBaseGraphicElement      'Ø I PSEUDO_SERIAL_VERSION_UIDL fillq ~ L 	fillValuet +Lnet/sf/jasperreports/engine/type/FillEnum;L linePent #Lnet/sf/jasperreports/engine/JRPen;L penq ~ xq ~ ,  wî          +       pq ~ q ~  pppppp~q ~ 6t FIX_RELATIVE_TO_TOPppppq ~ :  wîppsq ~ G  wîppppq ~ ªp  wî ~r 2net.sf.jasperreports.engine.type.LineDirectionEnum          xq ~ t TOP_DOWNxp  wî   ppq ~ ppppsr .net.sf.jasperreports.engine.base.JRBaseSection      'Ø [ bandst %[Lnet/sf/jasperreports/engine/JRBand;xpur %[Lnet.sf.jasperreports.engine.JRBand;Ý~ìÊ5  xp   sq ~ sq ~    	w   
sr 0net.sf.jasperreports.engine.base.JRBaseTextField      'Ø I PSEUDO_SERIAL_VERSION_UIDI 
bookmarkLevelB evaluationTimeB hyperlinkTargetB 
hyperlinkTypeZ isStretchWithOverflowL anchorNameExpressionq ~ L evaluationGroupq ~ 0L evaluationTimeValuet 5Lnet/sf/jasperreports/engine/type/EvaluationTimeEnum;L 
expressionq ~ L hyperlinkAnchorExpressionq ~ L hyperlinkPageExpressionq ~ [ hyperlinkParameterst 3[Lnet/sf/jasperreports/engine/JRHyperlinkParameter;L hyperlinkReferenceExpressionq ~ L hyperlinkTooltipExpressionq ~ L isBlankWhenNullq ~ 'L 
linkTargetq ~ L linkTypeq ~ L patternq ~ xq ~ #  wî           :       pq ~ q ~ ¶ppppppq ~ «ppppq ~ :  wîppppppsq ~ <   pppppppppppsq ~ Apsq ~ E  wîppppq ~ ½q ~ ½q ~ »psq ~ L  wîppppq ~ ½q ~ ½psq ~ F  wîppppq ~ ½q ~ ½psq ~ O  wîppppq ~ ½q ~ ½psq ~ Q  wîppppq ~ ½q ~ ½ppppppppppppppppp  wî        pp~r 3net.sf.jasperreports.engine.type.EvaluationTimeEnum          xq ~ t NOWsr 1net.sf.jasperreports.engine.base.JRBaseExpression      'Ø I id[ chunkst 0[Lnet/sf/jasperreports/engine/JRExpressionChunk;L valueClassNameq ~ L valueClassRealNameq ~ xp   ur 0[Lnet.sf.jasperreports.engine.JRExpressionChunk;mYÏÞiK£U  xp   sr 6net.sf.jasperreports.engine.base.JRBaseExpressionChunk      'Ø B typeL textq ~ xpt 
codigoExternot java.lang.Stringppppppppppsq ~ ¸  wî           -   ;    pq ~ q ~ ¶ppppppq ~ «ppppq ~ :  wîppppppq ~ ¼pppppppppppsq ~ Apsq ~ E  wîppppq ~ Ðq ~ Ðq ~ Ïpsq ~ L  wîppppq ~ Ðq ~ Ðpsq ~ F  wîppppq ~ Ðq ~ Ðpsq ~ O  wîppppq ~ Ðq ~ Ðpsq ~ Q  wîppppq ~ Ðq ~ Ðppppppppppppppppp  wî        ppq ~ Äsq ~ Æ   uq ~ É   sq ~ Ët valor_Apresentart java.lang.Stringppppppppppsq ~ ¸  wî           V   h    pq ~ q ~ ¶ppppppq ~ «ppppq ~ :  wîppppppq ~ ¼pppppppppppsq ~ Apsq ~ E  wîppppq ~ Üq ~ Üq ~ Ûpsq ~ L  wîppppq ~ Üq ~ Üpsq ~ F  wîppppq ~ Üq ~ Üpsq ~ O  wîppppq ~ Üq ~ Üpsq ~ Q  wîppppq ~ Üq ~ Üppppppppppppppppp  wî        ppq ~ Äsq ~ Æ   uq ~ É   sq ~ Ët 
nomePessoat java.lang.Stringppppppppppsq ~ ¸  wî           4   ¾    pq ~ q ~ ¶ppppppq ~ «ppppq ~ :  wîppppppq ~ ¼pppppppppppsq ~ Apsq ~ E  wîppppq ~ èq ~ èq ~ çpsq ~ L  wîppppq ~ èq ~ èpsq ~ F  wîppppq ~ èq ~ èpsq ~ O  wîppppq ~ èq ~ èpsq ~ Q  wîppppq ~ èq ~ èppppppppppppppppp  wî        ppq ~ Äsq ~ Æ   uq ~ É   sq ~ Ët situacao_Apresentart java.lang.Stringppppppppppsq ~ ¸  wî           (   ü    pq ~ q ~ ¶ppppppq ~ «ppppq ~ :  wîppppppq ~ ¼pppppppppppsq ~ Apsq ~ E  wîppppq ~ ôq ~ ôq ~ ópsq ~ L  wîppppq ~ ôq ~ ôpsq ~ F  wîppppq ~ ôq ~ ôpsq ~ O  wîppppq ~ ôq ~ ôpsq ~ Q  wîppppq ~ ôq ~ ôppppppppppppppppp  wî        ppq ~ Äsq ~ Æ   uq ~ É   sq ~ Ët autorizacaot java.lang.Stringppppppppppsq ~ ¸  wî           4  $    pq ~ q ~ ¶ppppppq ~ «ppppq ~ :  wîppppppq ~ ¼pppppppppppsq ~ Apsq ~ E  wîppppq ~ q ~ q ~ ÿpsq ~ L  wîppppq ~ q ~ psq ~ F  wîppppq ~ q ~ psq ~ O  wîppppq ~ q ~ psq ~ Q  wîppppq ~ q ~ ppppppppppppppppp  wî        ppq ~ Äsq ~ Æ   uq ~ É   sq ~ Ët bandeirat java.lang.Stringppppppppppsq ~ ¸  wî           H  X    pq ~ q ~ ¶ppppppq ~ «ppppq ~ :  wîppppppq ~ ¼pppppppppppsq ~ Apsq ~ E  wîppppq ~q ~q ~psq ~ L  wîppppq ~q ~psq ~ F  wîppppq ~q ~psq ~ O  wîppppq ~q ~psq ~ Q  wîppppq ~q ~ppppppppppppppppp  wî        ppq ~ Äsq ~ Æ   uq ~ É   sq ~ Ët cartaoMascaradot java.lang.Stringppppppppppsq ~ ¸  wî           d       pq ~ q ~ ¶ppppppq ~ «ppppq ~ :  wîppppppq ~ ¼pppppppppppsq ~ Apsq ~ E  wîppppq ~q ~q ~psq ~ L  wîppppq ~q ~psq ~ F  wîppppq ~q ~psq ~ O  wîppppq ~q ~psq ~ Q  wîppppq ~q ~ppppppppppppppppp  wî        ppq ~ Äsq ~ Æ   uq ~ É   sq ~ Ët dataProcessamento_Apresentart java.lang.Stringppppppppppsq ~ ¸  wî           '      pq ~ q ~ ¶ppppppq ~ «ppppq ~ :  wîppppppq ~ ¼pppppppppppsq ~ Apsq ~ E  wîppppq ~$q ~$q ~#psq ~ L  wîppppq ~$q ~$psq ~ F  wîppppq ~$q ~$psq ~ O  wîppppq ~$q ~$psq ~ Q  wîppppq ~$q ~$ppppppppppppppppp  wî        ppq ~ Äsq ~ Æ   uq ~ É   sq ~ Ët nomeUsuariot java.lang.Stringppppppppppxp  wî   ppq ~ pppt javapsr .net.sf.jasperreports.engine.base.JRBaseDataset      'Ø I PSEUDO_SERIAL_VERSION_UIDZ isMainB whenResourceMissingType[ fieldst &[Lnet/sf/jasperreports/engine/JRField;L filterExpressionq ~ [ groupst &[Lnet/sf/jasperreports/engine/JRGroup;L nameq ~ [ 
parameterst *[Lnet/sf/jasperreports/engine/JRParameter;L 
propertiesMapq ~ 1L queryt %Lnet/sf/jasperreports/engine/JRQuery;L resourceBundleq ~ L scriptletClassq ~ [ 
scriptletst *[Lnet/sf/jasperreports/engine/JRScriptlet;[ 
sortFieldst *[Lnet/sf/jasperreports/engine/JRSortField;[ 	variablest )[Lnet/sf/jasperreports/engine/JRVariable;L whenResourceMissingTypeValuet >Lnet/sf/jasperreports/engine/type/WhenResourceMissingTypeEnum;xp  wî ur &[Lnet.sf.jasperreports.engine.JRField;<ßÇN*òp  xp   
sr ,net.sf.jasperreports.engine.base.JRBaseField      'Ø L descriptionq ~ L nameq ~ L 
propertiesMapq ~ 1L valueClassNameq ~ L valueClassRealNameq ~ xppt codigosr +net.sf.jasperreports.engine.JRPropertiesMap      'Ø L baseq ~ 1L propertiesListq ~ L 
propertiesMapt Ljava/util/Map;xppppt java.lang.Integerpsq ~<pt 
codigoExternosq ~?pppt java.lang.Stringpsq ~<pt valor_Apresentarsq ~?pppt java.lang.Stringpsq ~<pt 
nomePessoasq ~?pppt java.lang.Stringpsq ~<pt situacao_Apresentarsq ~?pppt java.lang.Stringpsq ~<pt autorizacaosq ~?pppt java.lang.Stringpsq ~<pt bandeirasq ~?pppt java.lang.Stringpsq ~<pt cartaoMascaradosq ~?pppt java.lang.Stringpsq ~<pt dataProcessamento_Apresentarsq ~?pppt java.lang.Stringpsq ~<pt nomeUsuariosq ~?pppt java.lang.Stringpppt GestaoTransacoesur *[Lnet.sf.jasperreports.engine.JRParameter;" *Ã`!  xp    sr 0net.sf.jasperreports.engine.base.JRBaseParameter      'Ø 	Z isForPromptingZ isSystemDefinedL defaultValueExpressionq ~ L descriptionq ~ L nameq ~ L nestedTypeNameq ~ L 
propertiesMapq ~ 1L valueClassNameq ~ L valueClassRealNameq ~ xpppt REPORT_PARAMETERS_MAPpsq ~?pppt 
java.util.Mappsq ~jppt 
JASPER_REPORTpsq ~?pppt (net.sf.jasperreports.engine.JasperReportpsq ~jppt REPORT_CONNECTIONpsq ~?pppt java.sql.Connectionpsq ~jppt REPORT_MAX_COUNTpsq ~?pppt java.lang.Integerpsq ~jppt REPORT_DATA_SOURCEpsq ~?pppt (net.sf.jasperreports.engine.JRDataSourcepsq ~jppt REPORT_SCRIPTLETpsq ~?pppt /net.sf.jasperreports.engine.JRAbstractScriptletpsq ~jppt 
REPORT_LOCALEpsq ~?pppt java.util.Localepsq ~jppt REPORT_RESOURCE_BUNDLEpsq ~?pppt java.util.ResourceBundlepsq ~jppt REPORT_TIME_ZONEpsq ~?pppt java.util.TimeZonepsq ~jppt REPORT_FORMAT_FACTORYpsq ~?pppt .net.sf.jasperreports.engine.util.FormatFactorypsq ~jppt REPORT_CLASS_LOADERpsq ~?pppt java.lang.ClassLoaderpsq ~jppt REPORT_URL_HANDLER_FACTORYpsq ~?pppt  java.net.URLStreamHandlerFactorypsq ~jppt REPORT_FILE_RESOLVERpsq ~?pppt -net.sf.jasperreports.engine.util.FileResolverpsq ~jppt REPORT_TEMPLATESpsq ~?pppt java.util.Collectionpsq ~jppt REPORT_VIRTUALIZERpsq ~?pppt )net.sf.jasperreports.engine.JRVirtualizerpsq ~jppt IS_IGNORE_PAGINATIONpsq ~?pppt java.lang.Booleanpsq ~j  ppt logoPadraoRelatoriopsq ~?pppt java.io.InputStreampsq ~j  ppt tituloRelatoriopsq ~?pppt java.lang.Stringpsq ~j  ppt versaoSoftwarepsq ~?pppt java.lang.Stringpsq ~j  ppt usuariopsq ~?pppt java.lang.Stringpsq ~j  ppt filtrospsq ~?pppt java.lang.Stringpsq ~j sq ~ Æ    uq ~ É   sq ~ Ët q"D:\\PactoJ\\Desenvolvimento\\Sistemas\\ZillyonWeb\\tronco-novo\\src\\java\\relatorio\\designRelatorio\\outros\\"t java.lang.Stringppt 
SUBREPORT_DIRpsq ~?pppq ~Äpsq ~j ppt nomeEmpresapsq ~?pppt java.lang.Stringpsq ~j ppt enderecoEmpresapsq ~?pppt java.lang.Stringpsq ~j ppt 
cidadeEmpresapsq ~?pppt java.lang.Stringpsq ~j  ppt dataInipsq ~?pppt java.lang.Stringpsq ~j  ppt dataFimpsq ~?pppt java.lang.Stringpsq ~j ppt SUBREPORT_DIR1psq ~?pppt java.lang.Stringpsq ~j  ppt dadosTotalPorSituacaopsq ~?pppt java.lang.Objectpsq ~j  ppt dadosValoresPorSituacaopsq ~?pppt java.lang.Objectpsq ~j  ppt dadosValoresParcelaspsq ~?pppt java.lang.Objectpsq ~j ppt totalTransacoesCobradaspsq ~?pppt java.lang.Integerpsq ~?psq ~    w   
t ireport.zoomt 	ireport.xt 	ireport.yxsr java.util.HashMapÚÁÃ`Ñ F 
loadFactorI 	thresholdxp?@     w      q ~ñt 1.3310000000000004q ~òt 0q ~ót 5xpppppur )[Lnet.sf.jasperreports.engine.JRVariable;bæ|,·D  xp   sr /net.sf.jasperreports.engine.base.JRBaseVariable      'Ø I PSEUDO_SERIAL_VERSION_UIDB calculationB 
incrementTypeZ isSystemDefinedB 	resetTypeL calculationValuet 2Lnet/sf/jasperreports/engine/type/CalculationEnum;L 
expressionq ~ L incrementGroupq ~ 0L incrementTypeValuet 4Lnet/sf/jasperreports/engine/type/IncrementTypeEnum;L incrementerFactoryClassNameq ~ L incrementerFactoryClassRealNameq ~ L initialValueExpressionq ~ L nameq ~ L 
resetGroupq ~ 0L resetTypeValuet 0Lnet/sf/jasperreports/engine/type/ResetTypeEnum;L valueClassNameq ~ L valueClassRealNameq ~ xp  wî   ~r 0net.sf.jasperreports.engine.type.CalculationEnum          xq ~ t SYSTEMpp~r 2net.sf.jasperreports.engine.type.IncrementTypeEnum          xq ~ t NONEppsq ~ Æ   uq ~ É   sq ~ Ët new java.lang.Integer(1)q ~zpt PAGE_NUMBERp~r .net.sf.jasperreports.engine.type.ResetTypeEnum          xq ~ t REPORTq ~zpsq ~û  wî   q ~ppq ~ppsq ~ Æ   uq ~ É   sq ~ Ët new java.lang.Integer(1)q ~zpt 
COLUMN_NUMBERp~q ~t PAGEq ~zpsq ~û  wî   ~q ~ t COUNTsq ~ Æ   uq ~ É   sq ~ Ët new java.lang.Integer(1)q ~zppq ~ppsq ~ Æ   uq ~ É   sq ~ Ët new java.lang.Integer(0)q ~zpt REPORT_COUNTpq ~q ~zpsq ~û  wî   q ~sq ~ Æ   uq ~ É   sq ~ Ët new java.lang.Integer(1)q ~zppq ~ppsq ~ Æ   uq ~ É   sq ~ Ët new java.lang.Integer(0)q ~zpt 
PAGE_COUNTpq ~q ~zpsq ~û  wî   q ~sq ~ Æ   uq ~ É   sq ~ Ët new java.lang.Integer(1)q ~zppq ~ppsq ~ Æ   uq ~ É   sq ~ Ët new java.lang.Integer(0)q ~zpt COLUMN_COUNTp~q ~t COLUMNq ~zp~r <net.sf.jasperreports.engine.type.WhenResourceMissingTypeEnum          xq ~ t NULLq ~gp~r 0net.sf.jasperreports.engine.type.OrientationEnum          xq ~ t PORTRAITpp~r /net.sf.jasperreports.engine.type.PrintOrderEnum          xq ~ t VERTICALur &[Lnet.sf.jasperreports.engine.JRStyle;ÔÃÙr5  xp   sr ,net.sf.jasperreports.engine.base.JRBaseStyle      ' 9I PSEUDO_SERIAL_VERSION_UIDZ 	isDefaultL 	backcolorq ~ $L borderq ~ L borderColorq ~ $L bottomBorderq ~ L bottomBorderColorq ~ $L 
bottomPaddingq ~ %[ conditionalStylest 1[Lnet/sf/jasperreports/engine/JRConditionalStyle;L defaultStyleProviderq ~ -L fillq ~ L 	fillValueq ~ ¨L fontNameq ~ L fontSizeq ~ %L 	forecolorq ~ $L horizontalAlignmentq ~ L horizontalAlignmentValueq ~ &L isBlankWhenNullq ~ 'L isBoldq ~ 'L isItalicq ~ 'L 
isPdfEmbeddedq ~ 'L isStrikeThroughq ~ 'L isStyledTextq ~ 'L isUnderlineq ~ 'L 
leftBorderq ~ L leftBorderColorq ~ $L leftPaddingq ~ %L lineBoxq ~ (L linePenq ~ ©L lineSpacingq ~ L lineSpacingValueq ~ )L markupq ~ L modeq ~ L 	modeValueq ~ .L nameq ~ L paddingq ~ %L parentStyleq ~ L parentStyleNameReferenceq ~ L patternq ~ L pdfEncodingq ~ L pdfFontNameq ~ L penq ~ L positionTypeq ~ L radiusq ~ %L rightBorderq ~ L rightBorderColorq ~ $L rightPaddingq ~ %L rotationq ~ L 
rotationValueq ~ *L 
scaleImageq ~ L scaleImageValuet 1Lnet/sf/jasperreports/engine/type/ScaleImageEnum;L stretchTypeq ~ L 	topBorderq ~ L topBorderColorq ~ $L 
topPaddingq ~ %L verticalAlignmentq ~ L verticalAlignmentValueq ~ +xp  wî pppppppppppppppppppppppppsq ~ Apsq ~ E  wîppppq ~Gq ~Gq ~Fpsq ~ L  wîppppq ~Gq ~Gpsq ~ F  wîsr java.awt.Color¥3u F falphaI valueL cst Ljava/awt/color/ColorSpace;[ 	frgbvaluet [F[ fvalueq ~Mxp    ÿ   pppppsr java.lang.FloatÚíÉ¢Û<ðì F valuexq ~ =?  q ~Gq ~Gpsq ~ O  wîppppq ~Gq ~Gpsq ~ Q  wîppppq ~Gq ~Gsq ~ G  wîppppq ~Fpppppt tableppppppppppppppppppppppsq ~C  wî sq ~K    ÿðøÿpppppppppppppppppppppppppppsq ~ Apsq ~ E  wîppppq ~Wq ~Wq ~Upsq ~ L  wîppppq ~Wq ~Wpsq ~ F  wîsq ~K    ÿ   pppppsq ~O?   q ~Wq ~Wpsq ~ O  wîppppq ~Wq ~Wpsq ~ Q  wîppppq ~Wq ~Wsq ~ G  wîppppq ~Upppp~r )net.sf.jasperreports.engine.type.ModeEnum          xq ~ t OPAQUEt table_THppppppppppppppppppppppsq ~C  wî sq ~K    ÿ¿áÿpppppppppppppppppppppppppppsq ~ Apsq ~ E  wîppppq ~fq ~fq ~dpsq ~ L  wîppppq ~fq ~fpsq ~ F  wîsq ~K    ÿ   pppppsq ~O?   q ~fq ~fpsq ~ O  wîppppq ~fq ~fpsq ~ Q  wîppppq ~fq ~fsq ~ G  wîppppq ~dppppq ~at table_CHppppppppppppppppppppppsq ~C  wî sq ~K    ÿÿÿÿpppppppppppppppppppppppppppsq ~ Apsq ~ E  wîppppq ~rq ~rq ~ppsq ~ L  wîppppq ~rq ~rpsq ~ F  wîsq ~K    ÿ   pppppsq ~O?   q ~rq ~rpsq ~ O  wîppppq ~rq ~rpsq ~ Q  wîppppq ~rq ~rsq ~ G  wîppppq ~pppppq ~at table_TDppppppppppppppppppppppsq ~ sq ~    w   
sr 0net.sf.jasperreports.engine.base.JRBaseSubreport      'Ø L connectionExpressionq ~ L dataSourceExpressionq ~ L 
expressionq ~ L isUsingCacheq ~ '[ 
parameterst 3[Lnet/sf/jasperreports/engine/JRSubreportParameter;L parametersMapExpressionq ~ [ returnValuest 5[Lnet/sf/jasperreports/engine/JRSubreportReturnValue;L runToBottomq ~ 'xq ~ ,  wî              
   0pq ~ q ~|pt 
subreport1ppppq ~ «ppppq ~ :psq ~ Æ   uq ~ É   sq ~ Ët dadosTotalPorSituacaoq ~~psq ~ Æ   uq ~ É   sq ~ Ët 
SUBREPORT_DIRsq ~ Ët + + "GestaoTransacoes_subQtdSituacao.jasper"t java.lang.Stringpsq ~ ? ppppsq ~~  wî                0pq ~ q ~|pt 
subreport3ppppq ~ «ppppq ~ :psq ~ Æ   uq ~ É   sq ~ Ët dadosValoresParcelasq ~~psq ~ Æ   uq ~ É   sq ~ Ët 
SUBREPORT_DIRsq ~ Ët / + "GestaoTransacoes_subValoresParcelas.jasper"t java.lang.Stringpq ~ppppsq ~ ¸  wî   
       ¿       pq ~ q ~|ppppppq ~ «ppppq ~ :  wîppppppq ~ >ppq ~ @ppppppppsq ~ Apsq ~ E  wîppppq ~q ~q ~psq ~ L  wîppppq ~q ~psq ~ F  wîppppq ~q ~psq ~ O  wîppppq ~q ~psq ~ Q  wîppppq ~q ~ppppppppppppppppp  wî        ppq ~ Äsq ~ Æ   uq ~ É   sq ~ Ët "* "+ sq ~ Ët totalTransacoesCobradassq ~ Ët ?.toString() + " transaÃ§Ãµes estÃ£o passÃ­veis de tarifaÃ§Ã£o."t java.lang.Stringppppppppppsq ~~  wî              Ñ   0pq ~ q ~|pt 
subreport2ppppq ~ «ppppq ~ :psq ~ Æ   uq ~ É   sq ~ Ët dadosValoresPorSituacaoq ~~psq ~ Æ   uq ~ É   sq ~ Ët 
SUBREPORT_DIRsq ~ Ët 1 + "GestaoTransacoes_subValoresTransacoes.jasper"t java.lang.Stringpq ~ppppsq ~ "  wî          )      pq ~ q ~|ppppppq ~ «ppppq ~ :  wîppppppsq ~ <   p~r 4net.sf.jasperreports.engine.type.HorizontalAlignEnum          xq ~ t CENTERq ~ @ppppppppsq ~ Apsq ~ E  wîppppq ~¾q ~¾q ~¹psq ~ L  wîppppq ~¾q ~¾psq ~ F  wîppppq ~¾q ~¾psq ~ O  wîppppq ~¾q ~¾psq ~ Q  wîppppq ~¾q ~¾pppppppppppppppppt SumÃ¡riosq ~ ¥  wî          +       pq ~ q ~|ppppppq ~ «ppppq ~ :  wîppsq ~ G  wîppppq ~Åp  wî q ~ ¯sq ~ ¥  wî          +       pq ~ q ~|ppppppq ~ «ppppq ~ :  wîppsq ~ G  wîppppq ~Çp  wî q ~ ¯xp  wî   cppppsq ~ sq ~    
w   
sq ~ ¸  wî                (pq ~ q ~Épt 
textField-212ppppq ~ «ppppq ~ :  wîpppppt Arialsq ~ <   
ppq ~ @ppppppppsq ~ Asq ~ <   sq ~ E  wîsq ~K    ÿfffpppp~r .net.sf.jasperreports.engine.type.LineStyleEnum          xq ~ t SOLIDsq ~O    q ~Ïq ~Ïq ~Ëpsq ~ L  wîsq ~K    ÿfffppppq ~Ôsq ~O    q ~Ïq ~Ïpsq ~ F  wîppppq ~Ïq ~Ïpsq ~ O  wîsq ~K    ÿfffppppq ~Ôsq ~O    q ~Ïq ~Ïpsq ~ Q  wîsq ~K    ÿ   ppppq ~Ôsq ~O    q ~Ïq ~Ïpppppt Helvetica-Boldppppppppppp  wî        pp~q ~ Ãt REPORTsq ~ Æ   	uq ~ É   sq ~ Ët " " + sq ~ Ët PAGE_NUMBERsq ~ Ët  + ""t java.lang.Stringppppppq ~pppsq ~ ¸  wî           q   `   pq ~ q ~Épt 
textField-208ppppq ~ «ppppq ~ :  wîpppppppppq ~ @ppppppppsq ~ Apsq ~ E  wîppppq ~ïq ~ïq ~ípsq ~ L  wîppppq ~ïq ~ïpsq ~ F  wîppppq ~ïq ~ïpsq ~ O  wîppppq ~ïq ~ïpsq ~ Q  wîppppq ~ïq ~ïpppppt Helvetica-Boldppppppppppp  wî        ppq ~ Äsq ~ Æ   
uq ~ É   sq ~ Ët nomeEmpresat java.lang.Stringppppppq ~pppsq ~ ¸  wî           q   `   pq ~ q ~Épt 
textField-209ppppq ~ «ppppq ~ :  wîpppppppppq ~ @ppppppppsq ~ Apsq ~ E  wîppppq ~ýq ~ýq ~ûpsq ~ L  wîppppq ~ýq ~ýpsq ~ F  wîppppq ~ýq ~ýpsq ~ O  wîppppq ~ýq ~ýpsq ~ Q  wîppppq ~ýq ~ýpppppt Helvetica-Boldppppppppppp  wî        ppq ~ Äsq ~ Æ   uq ~ É   sq ~ Ët enderecoEmpresat java.lang.Stringppppppq ~pppsq ~ "  wî            '   pq ~ q ~Épt 
staticText-14pq ~appq ~ «ppppq ~ :  wîpppppt Microsoft Sans Serifq ~ >p~q ~»t RIGHTq ~ @q ~ @pq ~pq ~pppsq ~ Apsq ~ E  wîsq ~K    ÿfffppppq ~Ôsq ~O    q ~q ~q ~	psq ~ L  wîsq ~K    ÿfffppppq ~Ôsq ~O    q ~q ~psq ~ F  wîppppq ~q ~psq ~ O  wîsq ~K    ÿfffppppq ~Ôsq ~O    q ~q ~psq ~ Q  wîsq ~K    ÿfffppppq ~Ôsq ~O    q ~q ~pppppt Helvetica-BoldObliquepppppppppp~r 2net.sf.jasperreports.engine.type.VerticalAlignEnum          xq ~ t TOPt eZillyon - Sistema Administrativo para Academias Desenvolvido por PACTO SoluÃ§Ãµes TecnolÃ³gicas Ltda.sr ,net.sf.jasperreports.engine.base.JRBaseImage      'Ø *I PSEUDO_SERIAL_VERSION_UIDI 
bookmarkLevelB evaluationTimeB hyperlinkTargetB 
hyperlinkTypeZ isLazyB onErrorTypeL anchorNameExpressionq ~ L borderq ~ L borderColorq ~ $L bottomBorderq ~ L bottomBorderColorq ~ $L 
bottomPaddingq ~ %L evaluationGroupq ~ 0L evaluationTimeValueq ~ ¹L 
expressionq ~ L horizontalAlignmentq ~ L horizontalAlignmentValueq ~ &L hyperlinkAnchorExpressionq ~ L hyperlinkPageExpressionq ~ [ hyperlinkParametersq ~ ºL hyperlinkReferenceExpressionq ~ L hyperlinkTooltipExpressionq ~ L isUsingCacheq ~ 'L 
leftBorderq ~ L leftBorderColorq ~ $L leftPaddingq ~ %L lineBoxq ~ (L 
linkTargetq ~ L linkTypeq ~ L onErrorTypeValuet 2Lnet/sf/jasperreports/engine/type/OnErrorTypeEnum;L paddingq ~ %L rightBorderq ~ L rightBorderColorq ~ $L rightPaddingq ~ %L 
scaleImageq ~ L scaleImageValueq ~EL 	topBorderq ~ L topBorderColorq ~ $L 
topPaddingq ~ %L verticalAlignmentq ~ L verticalAlignmentValueq ~ +xq ~ §  wî   .       R   
   pq ~ q ~Épt image-1ppppq ~ «ppppq ~ :  wîppsq ~ G  wîppppq ~#p  wî         ppppppp~q ~ Ãt PAGEsq ~ Æ   uq ~ É   sq ~ Ët logoPadraoRelatoriot java.io.InputStreamppppppppq ~ @pppsq ~ Apsq ~ E  wîsq ~K    ÿfffppppq ~Ôsq ~O?   q ~-q ~-q ~#psq ~ L  wîsq ~K    ÿfffppppq ~Ôsq ~O?   q ~-q ~-psq ~ F  wîppppq ~-q ~-psq ~ O  wîsq ~K    ÿfffppppq ~Ôsq ~O?   q ~-q ~-psq ~ Q  wîsq ~K    ÿfffppppq ~Ôsq ~O?   q ~-q ~-pp~r 0net.sf.jasperreports.engine.type.OnErrorTypeEnum          xq ~ t BLANKpppppppppp~q ~t MIDDLEsq ~ ¸  wî           K  ¼   (pq ~ q ~Épt 
textField-211ppppq ~ «ppppq ~ :  wîpppppt Arialq ~Îpq ~q ~ @ppppppppsq ~ Aq ~Ðsq ~ E  wîsq ~K    ÿfffppppq ~Ôsq ~O    q ~Cq ~Cq ~@psq ~ L  wîsq ~K    ÿfffppppq ~Ôsq ~O    q ~Cq ~Cpsq ~ F  wîppppq ~Cq ~Cpsq ~ O  wîsq ~K    ÿ   ppppq ~Ôsq ~O    q ~Cq ~Cpsq ~ Q  wîsq ~K    ÿ   ppppq ~Ôsq ~O    q ~Cq ~Cpppppt Helvetica-Boldppppppppppp  wî        ppq ~ Äsq ~ Æ   
uq ~ É   sq ~ Ët "PÃ¡gina: " + sq ~ Ët PAGE_NUMBERsq ~ Ët 	 + " de "t java.lang.Stringppppppq ~pppsq ~ "  wî           o  ¼   pq ~ q ~Épt 
staticText-15pq ~appq ~ «ppppq ~ :  wîpppppt Microsoft Sans Serifq ~ >pq ~q ~ @q ~ @pq ~pq ~pppsq ~ Apsq ~ E  wîsq ~K    ÿfffppppq ~Ôsq ~O    q ~^q ~^q ~[psq ~ L  wîsq ~K    ÿfffppppq ~Ôsq ~O    q ~^q ~^psq ~ F  wîppppq ~^q ~^psq ~ O  wîsq ~K    ÿfffppppq ~Ôsq ~O    q ~^q ~^psq ~ Q  wîsq ~K    ÿfffppppq ~Ôsq ~O    q ~^q ~^pppppt Helvetica-BoldObliqueppppppppppq ~t (0xx62) 3251-5820sq ~ ¸  wî           q   `   %pq ~ q ~Épt 
textField-210ppppq ~ «ppppq ~ :  wîpppppppppq ~ @ppppppppsq ~ Apsq ~ E  wîppppq ~pq ~pq ~npsq ~ L  wîppppq ~pq ~ppsq ~ F  wîppppq ~pq ~ppsq ~ O  wîppppq ~pq ~ppsq ~ Q  wîppppq ~pq ~ppppppt Helvetica-Boldppppppppppp  wî        ppq ~ Äsq ~ Æ   uq ~ É   sq ~ Ët 
cidadeEmpresat java.lang.Stringppppppq ~pppsq ~ "  wî          +      9pq ~ q ~Épt 
staticText-13ppppq ~ «ppppq ~ :  wîppppppsq ~ <   pq ~¼q ~ @ppppppppsq ~ Apsq ~ E  wîppppq ~q ~q ~|psq ~ L  wîppppq ~q ~psq ~ F  wîppppq ~q ~psq ~ O  wîppppq ~q ~psq ~ Q  wîppppq ~q ~pppppt Helvetica-Boldppppppppppq ~>t GestÃ£o de TransaÃ§Ãµessq ~ ¸  wî          )      Ppq ~ q ~Épt 
textField-214ppppq ~ «ppppq ~ :  wîpppppt Arialsq ~ <   pq ~¼q ~ @q ~ @pppppppsq ~ Apsq ~ E  wîppppq ~q ~q ~psq ~ L  wîppppq ~q ~psq ~ F  wîppppq ~q ~psq ~ O  wîppppq ~q ~psq ~ Q  wîppppq ~q ~ppt htmlppt Helvetica-BoldObliqueppppppppppq ~>  wî       ppq ~ Äsq ~ Æ   uq ~ É   sq ~ Ët filtrost java.lang.Stringppppppq ~pppxp  wî   ippq ~ ~r 3net.sf.jasperreports.engine.type.WhenNoDataTypeEnum          xq ~ t NO_PAGESsr 6net.sf.jasperreports.engine.design.JRReportCompileData      'Ø L crosstabCompileDataq ~@L datasetCompileDataq ~@L mainDatasetCompileDataq ~ xpsq ~ô?@     w       xsq ~ô?@     w       xur [B¬óøTà  xp  %9Êþº¾   .X %GestaoTransacoes_1316208405452_187880  ,net/sf/jasperreports/engine/fill/JREvaluator  parameter_enderecoEmpresa 2Lnet/sf/jasperreports/engine/fill/JRFillParameter; parameter_JASPER_REPORT parameter_REPORT_TIME_ZONE parameter_usuario parameter_REPORT_FILE_RESOLVER !parameter_totalTransacoesCobradas parameter_REPORT_PARAMETERS_MAP parameter_SUBREPORT_DIR1 parameter_REPORT_CLASS_LOADER $parameter_REPORT_URL_HANDLER_FACTORY parameter_REPORT_DATA_SOURCE parameter_IS_IGNORE_PAGINATION parameter_dadosValoresParcelas parameter_REPORT_MAX_COUNT parameter_REPORT_TEMPLATES parameter_dataIni parameter_REPORT_LOCALE parameter_dadosTotalPorSituacao parameter_REPORT_VIRTUALIZER parameter_logoPadraoRelatorio parameter_REPORT_SCRIPTLET parameter_REPORT_CONNECTION parameter_SUBREPORT_DIR parameter_dataFim parameter_REPORT_FORMAT_FACTORY !parameter_dadosValoresPorSituacao parameter_tituloRelatorio parameter_nomeEmpresa parameter_cidadeEmpresa  parameter_REPORT_RESOURCE_BUNDLE parameter_versaoSoftware parameter_filtros field_nomePessoa .Lnet/sf/jasperreports/engine/fill/JRFillField; field_codigo field_cartaoMascarado field_autorizacao field_situacao_Apresentar field_valor_Apresentar field_codigoExterno field_nomeUsuario field_bandeira "field_dataProcessamento_Apresentar variable_PAGE_NUMBER 1Lnet/sf/jasperreports/engine/fill/JRFillVariable; variable_COLUMN_NUMBER variable_REPORT_COUNT variable_PAGE_COUNT variable_COLUMN_COUNT <init> ()V Code 7 8
  :  	  <  	  >  	  @ 	 	  B 
 	  D  	  F  	  H 
 	  J  	  L  	  N  	  P  	  R  	  T  	  V  	  X  	  Z  	  \  	  ^  	  `  	  b  	  d  	  f  	  h  	  j  	  l  	  n   	  p ! 	  r " 	  t # 	  v $ 	  x % 	  z & '	  | ( '	  ~ ) '	   * '	   + '	   , '	   - '	   . '	   / '	   0 '	   1 2	   3 2	   4 2	   5 2	   6 2	   LineNumberTable customizedInit 0(Ljava/util/Map;Ljava/util/Map;Ljava/util/Map;)V 
initParams (Ljava/util/Map;)V  
   
initFields ¡ 
  ¢ initVars ¤ 
  ¥ enderecoEmpresa § 
java/util/Map © get &(Ljava/lang/Object;)Ljava/lang/Object; « ¬ ª ­ 0net/sf/jasperreports/engine/fill/JRFillParameter ¯ 
JASPER_REPORT ± REPORT_TIME_ZONE ³ usuario µ REPORT_FILE_RESOLVER · totalTransacoesCobradas ¹ REPORT_PARAMETERS_MAP » SUBREPORT_DIR1 ½ REPORT_CLASS_LOADER ¿ REPORT_URL_HANDLER_FACTORY Á REPORT_DATA_SOURCE Ã IS_IGNORE_PAGINATION Å dadosValoresParcelas Ç REPORT_MAX_COUNT É REPORT_TEMPLATES Ë dataIni Í 
REPORT_LOCALE Ï dadosTotalPorSituacao Ñ REPORT_VIRTUALIZER Ó logoPadraoRelatorio Õ REPORT_SCRIPTLET × REPORT_CONNECTION Ù 
SUBREPORT_DIR Û dataFim Ý REPORT_FORMAT_FACTORY ß dadosValoresPorSituacao á tituloRelatorio ã nomeEmpresa å 
cidadeEmpresa ç REPORT_RESOURCE_BUNDLE é versaoSoftware ë filtros í 
nomePessoa ï ,net/sf/jasperreports/engine/fill/JRFillField ñ codigo ó cartaoMascarado õ autorizacao ÷ situacao_Apresentar ù valor_Apresentar û 
codigoExterno ý nomeUsuario ÿ bandeira dataProcessamento_Apresentar PAGE_NUMBER /net/sf/jasperreports/engine/fill/JRFillVariable 
COLUMN_NUMBER	 REPORT_COUNT 
PAGE_COUNT
 COLUMN_COUNT evaluate (I)Ljava/lang/Object; 
Exceptions java/lang/Throwable dD:\PactoJ\Desenvolvimento\Sistemas\ZillyonWeb\tronco-novo\src\java\relatorio\designRelatorio\outros\ java/lang/Integer (I)V 7
 java/lang/StringBuffer   (Ljava/lang/String;)V 7!
" getValue ()Ljava/lang/Object;$%
& append ,(Ljava/lang/Object;)Ljava/lang/StringBuffer;()
* toString ()Ljava/lang/String;,-
.
 °& java/lang/String1 java/io/InputStream3 	PÃ¡gina: 5  de 7 ,(Ljava/lang/String;)Ljava/lang/StringBuffer;(9
:
 ò& (net/sf/jasperreports/engine/JRDataSource= valueOf &(Ljava/lang/Object;)Ljava/lang/String;?@
2A &GestaoTransacoes_subQtdSituacao.jasperC *GestaoTransacoes_subValoresParcelas.jasperE * G
. / transaÃ§Ãµes estÃ£o passÃ­veis de tarifaÃ§Ã£o.J ,GestaoTransacoes_subValoresTransacoes.jasperL evaluateOld getOldValueO%
P
 òP evaluateEstimated getEstimatedValueT%
U 
SourceFile !     /                 	     
               
                                                                                                     !     "     #     $     %     & '    ( '    ) '    * '    + '    , '    - '    . '    / '    0 '    1 2    3 2    4 2    5 2    6 2     7 8  9  È     ð*· ;*µ =*µ ?*µ A*µ C*µ E*µ G*µ I*µ K*µ M*µ O*µ Q*µ S*µ U*µ W*µ Y*µ [*µ ]*µ _*µ a*µ c*µ e*µ g*µ i*µ k*µ m*µ o*µ q*µ s*µ u*µ w*µ y*µ {*µ }*µ *µ *µ *µ *µ *µ *µ *µ *µ *µ *µ *µ *µ *µ ±       Æ 1      	          "  '   , ! 1 " 6 # ; $ @ % E & J ' O ( T ) Y * ^ + c , h - m . r / w 0 | 1  2  3  4  5  6  7  8 ¤ 9 © : ® ; ³ < ¸ = ½ > Â ? Ç @ Ì A Ñ B Ö C Û D à E å F ê G ï      9   4     *+·  *,· £*-· ¦±           S  T 
 U  V     9  Ù    A*+¨¹ ® À °À °µ =*+²¹ ® À °À °µ ?*+´¹ ® À °À °µ A*+¶¹ ® À °À °µ C*+¸¹ ® À °À °µ E*+º¹ ® À °À °µ G*+¼¹ ® À °À °µ I*+¾¹ ® À °À °µ K*+À¹ ® À °À °µ M*+Â¹ ® À °À °µ O*+Ä¹ ® À °À °µ Q*+Æ¹ ® À °À °µ S*+È¹ ® À °À °µ U*+Ê¹ ® À °À °µ W*+Ì¹ ® À °À °µ Y*+Î¹ ® À °À °µ [*+Ð¹ ® À °À °µ ]*+Ò¹ ® À °À °µ _*+Ô¹ ® À °À °µ a*+Ö¹ ® À °À °µ c*+Ø¹ ® À °À °µ e*+Ú¹ ® À °À °µ g*+Ü¹ ® À °À °µ i*+Þ¹ ® À °À °µ k*+à¹ ® À °À °µ m*+â¹ ® À °À °µ o*+ä¹ ® À °À °µ q*+æ¹ ® À °À °µ s*+è¹ ® À °À °µ u*+ê¹ ® À °À °µ w*+ì¹ ® À °À °µ y*+î¹ ® À °À °µ {±        !   ^  _ $ ` 6 a H b Z c l d ~ e  f ¢ g ´ h Æ i Ø j ê k ü l m  n2 oD pV qh rz s t u° vÂ wÔ xæ yø z
 { |. }@ ~  ¡   9   ø     ¸*+ð¹ ® À òÀ òµ }*+ô¹ ® À òÀ òµ *+ö¹ ® À òÀ òµ *+ø¹ ® À òÀ òµ *+ú¹ ® À òÀ òµ *+ü¹ ® À òÀ òµ *+þ¹ ® À òÀ òµ *+ ¹ ® À òÀ òµ *+¹ ® À òÀ òµ *+¹ ® À òÀ òµ ±       .       $  6  H  Z  l  ~    ¤  ·   ¤   9        `*+¹ ® ÀÀµ *+
¹ ® ÀÀµ *+¹ ® ÀÀµ *+¹ ® ÀÀµ *+¹ ® ÀÀµ ±              &  9  L  _        9  Ì    °Mª  «                    ¬   ¸   Ä   Ð   Ü   è   ô       .  <  `  n  |      ¦  ´  Â  Ð  Þ  ì  ú    )  7  X    M§»Y·M§»Y·M§»Y·M§ö»Y·M§ê»Y·M§Þ»Y·M§Ò»Y·M§Æ»Y·M§º»Y ·#*´ ¶'À¶+¶/M§*´ s¶0À2M§*´ =¶0À2M§*´ c¶0À4M§r»Y6·#*´ ¶'À¶+8¶;¶/M§N*´ u¶0À2M§@*´ {¶0À2M§2*´ ¶<À2M§$*´ ¶<À2M§*´ }¶<À2M§*´ ¶<À2M§ ú*´ ¶<À2M§ ì*´ ¶<À2M§ Þ*´ ¶<À2M§ Ð*´ ¶<À2M§ Â*´ ¶<À2M§ ´*´ _¶0À>M§ ¦»Y*´ i¶0À2¸B·#D¶;¶/M§ *´ U¶0À>M§ w»Y*´ i¶0À2¸B·#F¶;¶/M§ V»YH·#*´ G¶0À¶I¶;K¶;¶/M§ /*´ o¶0À>M§ !»Y*´ i¶0À2¸B·#M¶;¶/M,°      
 B   ¥  §  «  ¬  °   ± £ µ ¬ ¶ ¯ º ¸ » » ¿ Ä À Ç Ä Ð Å Ó É Ü Ê ß Î è Ï ë Ó ô Ô ÷ Ø Ù Ý  Þ# â. ã1 ç< è? ì` íc ñn òq ö| ÷ û ü ¦©
´·ÂÅÐÓÞáìï#ú$ý()-).,273:7X8[<=ABF®N N      9  Ì    °Mª  «                    ¬   ¸   Ä   Ð   Ü   è   ô       .  <  `  n  |      ¦  ´  Â  Ð  Þ  ì  ú    )  7  X    M§»Y·M§»Y·M§»Y·M§ö»Y·M§ê»Y·M§Þ»Y·M§Ò»Y·M§Æ»Y·M§º»Y ·#*´ ¶QÀ¶+¶/M§*´ s¶0À2M§*´ =¶0À2M§*´ c¶0À4M§r»Y6·#*´ ¶QÀ¶+8¶;¶/M§N*´ u¶0À2M§@*´ {¶0À2M§2*´ ¶RÀ2M§$*´ ¶RÀ2M§*´ }¶RÀ2M§*´ ¶RÀ2M§ ú*´ ¶RÀ2M§ ì*´ ¶RÀ2M§ Þ*´ ¶RÀ2M§ Ð*´ ¶RÀ2M§ Â*´ ¶RÀ2M§ ´*´ _¶0À>M§ ¦»Y*´ i¶0À2¸B·#D¶;¶/M§ *´ U¶0À>M§ w»Y*´ i¶0À2¸B·#F¶;¶/M§ V»YH·#*´ G¶0À¶I¶;K¶;¶/M§ /*´ o¶0À>M§ !»Y*´ i¶0À2¸B·#M¶;¶/M,°      
 B  W Y ] ^ b  c £g ¬h ¯l ¸m »q Är Çv Ðw Ó{ Ü| ß è ë ô ÷ #.1<?`c£n¤q¨|©­®²³·¦¸©¼´½·ÁÂÂÅÆÐÇÓËÞÌáÐìÑïÕúÖýÚÛß)à,ä7å:éXê[îïóôø®  S      9  Ì    °Mª  «                    ¬   ¸   Ä   Ð   Ü   è   ô       .  <  `  n  |      ¦  ´  Â  Ð  Þ  ì  ú    )  7  X    M§»Y·M§»Y·M§»Y·M§ö»Y·M§ê»Y·M§Þ»Y·M§Ò»Y·M§Æ»Y·M§º»Y ·#*´ ¶VÀ¶+¶/M§*´ s¶0À2M§*´ =¶0À2M§*´ c¶0À4M§r»Y6·#*´ ¶VÀ¶+8¶;¶/M§N*´ u¶0À2M§@*´ {¶0À2M§2*´ ¶<À2M§$*´ ¶<À2M§*´ }¶<À2M§*´ ¶<À2M§ ú*´ ¶<À2M§ ì*´ ¶<À2M§ Þ*´ ¶<À2M§ Ð*´ ¶<À2M§ Â*´ ¶<À2M§ ´*´ _¶0À>M§ ¦»Y*´ i¶0À2¸B·#D¶;¶/M§ *´ U¶0À>M§ w»Y*´ i¶0À2¸B·#F¶;¶/M§ V»YH·#*´ G¶0À¶I¶;K¶;¶/M§ /*´ o¶0À>M§ !»Y*´ i¶0À2¸B·#M¶;¶/M,°      
 B  	       £ ¬ ¯ ¸ »# Ä$ Ç( Ð) Ó- Ü. ß2 è3 ë7 ô8 ÷<=A B#F.G1K<L?P`QcUnVqZ|[_`dei¦j©n´o·sÂtÅxÐyÓ}Þ~áìïúý),7:X[ ¡¥¦ª®² W    t _1316208405452_187880t 2net.sf.jasperreports.engine.design.JRJavacCompiler