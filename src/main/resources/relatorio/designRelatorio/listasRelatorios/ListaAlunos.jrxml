<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="RelatorioCliente" pageWidth="842" pageHeight="595" orientation="Landscape" columnWidth="786" leftMargin="28" rightMargin="28" topMargin="28" bottomMargin="28">
	<property name="ireport.zoom" value="1.5"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="0"/>
	<parameter name="logoPadraoRelatorio" class="java.io.InputStream" isForPrompting="false"/>
	<parameter name="tituloRelatorio" class="java.lang.String" isForPrompting="false"/>
	<parameter name="versaoSoftware" class="java.lang.String" isForPrompting="false"/>
	<parameter name="usuario" class="java.lang.String" isForPrompting="false"/>
	<parameter name="dadosImpressao" class="java.lang.String" isForPrompting="false"/>
	<parameter name="nomeEmpresa" class="java.lang.String"/>
	<parameter name="enderecoEmpresa" class="java.lang.String"/>
	<parameter name="cidadeEmpresa" class="java.lang.String"/>
	<parameter name="dataIni" class="java.lang.String" isForPrompting="false"/>
	<parameter name="dataFim" class="java.lang.String" isForPrompting="false"/>
	<parameter name="totalClientes" class="java.lang.Integer" isForPrompting="false"/>
	<parameter name="listaTotais" class="java.lang.Object" isForPrompting="false"/>
	<parameter name="apresentarLinha1" class="java.lang.Boolean">
		<defaultValueExpression><![CDATA[true]]></defaultValueExpression>
	</parameter>
	<parameter name="apresentarLinha2" class="java.lang.Boolean">
		<defaultValueExpression><![CDATA[true]]></defaultValueExpression>
	</parameter>
	<parameter name="apresentarLinha3" class="java.lang.Boolean">
		<defaultValueExpression><![CDATA[true]]></defaultValueExpression>
	</parameter>
	<parameter name="filtro" class="java.lang.String"/>
	<queryString>
		<![CDATA[]]>
	</queryString>
	<field name="matricula" class="java.lang.String">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="nome" class="java.lang.String"/>
	<field name="situacaoCliente" class="java.lang.String">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="telefone" class="java.lang.String">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="plano" class="java.lang.String">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="dataMatricula" class="java.util.Date">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="inicioPlano" class="java.util.Date"/>
	<field name="bairro" class="java.lang.String"/>
	<field name="cidade" class="java.lang.String"/>
	<field name="cep" class="java.lang.String"/>
	<field name="complemento" class="java.lang.String"/>
	<field name="logradouro" class="java.lang.String"/>
	<field name="numero" class="java.lang.String"/>
	<field name="professores" class="java.lang.String"/>
	<field name="email" class="java.lang.String"/>
	<field name="dataCadastro" class="java.util.Date"/>
	<field name="dataNascimento" class="java.util.Date"/>
	<field name="consultor" class="java.lang.String"/>
	<field name="dataUltimoAcesso" class="java.util.Date"/>
	<field name="vencimentoPlano" class="java.util.Date"/>
	<field name="sexo" class="java.lang.String"/>
	<field name="cpf" class="java.lang.String"/>
	<field name="justificativa" class="java.lang.String"/>
	<field name="observacao" class="java.lang.String"/>
	<field name="nomeRespContrato" class="java.lang.String"/>
	<field name="nomeConsultor" class="java.lang.String"/>
	<field name="nomeProfessores" class="java.lang.String"/>
	<field name="dataFimOperacao" class="java.util.Date"/>
	<field name="dataInicioOperacao" class="java.util.Date"/>
	<field name="estadoCivil" class="java.lang.String"/>
	<field name="profissao" class="java.lang.String"/>
	<field name="dataOperacao" class="java.util.Date"/>
	<field name="documento" class="java.lang.String"/>
	<field name="cnpj" class="java.lang.String"/>
	<background>
		<band splitType="Stretch"/>
	</background>
	<title>
		<band height="65" splitType="Prevent">
			<image isUsingCache="true" onErrorType="Blank" evaluationTime="Page">
				<reportElement key="image-1" positionType="Float" x="1" y="2" width="83" height="39" isPrintWhenDetailOverflows="true"/>
				<box>
					<topPen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
					<leftPen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
					<bottomPen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
				</box>
				<imageExpression class="java.io.InputStream"><![CDATA[$P{logoPadraoRelatorio}]]></imageExpression>
			</image>
			<textField pattern="dd/MM/yyyy HH.mm.ss" isBlankWhenNull="true">
				<reportElement key="dataRel-1" mode="Transparent" x="672" y="2" width="112" height="12" backcolor="#FFFFFF"/>
				<box bottomPadding="0">
					<topPen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
					<leftPen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
					<bottomPen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Verdana" size="8" isItalic="false" pdfFontName="Helvetica"/>
				</textElement>
				<textFieldExpression class="java.util.Date"><![CDATA[new Date()]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="false">
				<reportElement key="textField-207" x="672" y="14" width="112" height="14"/>
				<box>
					<topPen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
					<leftPen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
					<bottomPen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Verdana" size="8" isBold="false" isItalic="false" pdfFontName="Helvetica"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA["Usuário:"+ $P{usuario}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-2" x="84" y="2" width="588" height="19"/>
				<box>
					<pen lineWidth="0.5" lineStyle="Solid"/>
					<topPen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
					<leftPen lineWidth="0.0" lineStyle="Solid"/>
					<bottomPen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.0" lineStyle="Solid"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="14" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{tituloRelatorio}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="672" y="29" width="70" height="12"/>
				<box>
					<leftPen lineWidth="0.5" lineStyle="Dotted"/>
					<bottomPen lineWidth="0.5" lineStyle="Dotted"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Verdana" size="8"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA["Página: "+$V{PAGE_NUMBER}+" de"]]></textFieldExpression>
			</textField>
			<textField evaluationTime="Report">
				<reportElement x="742" y="29" width="42" height="12"/>
				<box leftPadding="4">
					<bottomPen lineWidth="0.5" lineStyle="Dotted"/>
					<rightPen lineWidth="0.5" lineStyle="Dotted"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Verdana" size="8"/>
				</textElement>
				<textFieldExpression class="java.lang.Integer"><![CDATA[$V{PAGE_NUMBER}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="false">
				<reportElement key="textField-215" x="84" y="21" width="588" height="20"/>
				<box>
					<bottomPen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="13" isBold="false" isItalic="false" pdfFontName="Helvetica-BoldOblique"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA["Dados da Impressão: "+ $P{dadosImpressao}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true">
				<reportElement stretchType="RelativeToTallestObject" x="2" y="42" width="782" height="15"/>
				<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
					<font fontName="Arial" size="12"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA["Filtros:"+ $P{filtro}]]></textFieldExpression>
			</textField>
		</band>
	</title>
	<columnHeader>
		<band height="66" splitType="Stretch">
			<staticText>
				<reportElement key="" positionType="Float" x="50" y="22" width="200" height="14" isRemoveLineWhenBlank="true">
					<printWhenExpression><![CDATA[$P{apresentarLinha1}.equals( true ) || $P{apresentarLinha2}.equals( true )]]></printWhenExpression>
				</reportElement>
				<textElement>
					<font isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Nome]]></text>
			</staticText>
			<staticText>
				<reportElement key="" positionType="Float" x="0" y="22" width="50" height="14" isRemoveLineWhenBlank="true">
					<printWhenExpression><![CDATA[$P{apresentarLinha1}.equals( true ) || $P{apresentarLinha2}.equals( true )]]></printWhenExpression>
				</reportElement>
				<textElement>
					<font isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Mat.]]></text>
			</staticText>
			<line>
				<reportElement key="line-4" positionType="Float" x="1" y="64" width="786" height="1"/>
			</line>
			<staticText>
				<reportElement key="" positionType="Float" x="350" y="22" width="100" height="14" isRemoveLineWhenBlank="true">
					<printWhenExpression><![CDATA[$P{apresentarLinha1}.equals( true )]]></printWhenExpression>
				</reportElement>
				<textElement>
					<font isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Telefone]]></text>
			</staticText>
			<staticText>
				<reportElement key="" positionType="Float" x="470" y="50" width="50" height="14" isRemoveLineWhenBlank="true">
					<printWhenExpression><![CDATA[$P{apresentarLinha1}.equals( true ) || $P{apresentarLinha2}.equals( true ) || $P{apresentarLinha3}.equals( true )]]></printWhenExpression>
				</reportElement>
				<textElement>
					<font size="8" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Cadastro]]></text>
			</staticText>
			<staticText>
				<reportElement key="" positionType="Float" x="670" y="22" width="60" height="14" isRemoveLineWhenBlank="true">
					<printWhenExpression><![CDATA[$P{apresentarLinha1}.equals( true )]]></printWhenExpression>
				</reportElement>
				<textElement>
					<font isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Situação]]></text>
			</staticText>
			<staticText>
				<reportElement key="" positionType="Float" x="0" y="36" width="180" height="14" isRemoveLineWhenBlank="true">
					<printWhenExpression><![CDATA[$P{apresentarLinha2}.equals( true )]]></printWhenExpression>
				</reportElement>
				<textElement>
					<font size="8" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Logradouro]]></text>
			</staticText>
			<staticText>
				<reportElement positionType="Float" x="450" y="22" width="150" height="14" isRemoveLineWhenBlank="true">
					<printWhenExpression><![CDATA[$P{apresentarLinha1}.equals( true )]]></printWhenExpression>
				</reportElement>
				<textElement>
					<font size="10" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Email]]></text>
			</staticText>
			<staticText>
				<reportElement x="180" y="36" width="50" height="14">
					<printWhenExpression><![CDATA[$P{apresentarLinha2}.equals( true )]]></printWhenExpression>
				</reportElement>
				<textElement>
					<font size="8" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Número]]></text>
			</staticText>
			<staticText>
				<reportElement x="230" y="36" width="140" height="14">
					<printWhenExpression><![CDATA[$P{apresentarLinha2}.equals( true )]]></printWhenExpression>
				</reportElement>
				<textElement>
					<font size="8" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Bairro]]></text>
			</staticText>
			<staticText>
				<reportElement x="370" y="36" width="120" height="14">
					<printWhenExpression><![CDATA[$P{apresentarLinha2}.equals( true )]]></printWhenExpression>
				</reportElement>
				<textElement>
					<font size="8" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Cidade]]></text>
			</staticText>
			<staticText>
				<reportElement x="490" y="36" width="56" height="14">
					<printWhenExpression><![CDATA[$P{apresentarLinha2}.equals( true )]]></printWhenExpression>
				</reportElement>
				<textElement>
					<font size="8" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[CEP]]></text>
			</staticText>
			<staticText>
				<reportElement x="546" y="36" width="91" height="14">
					<printWhenExpression><![CDATA[$P{apresentarLinha2}.equals( true )]]></printWhenExpression>
				</reportElement>
				<textElement>
					<font size="8" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Complemento]]></text>
			</staticText>
			<staticText>
				<reportElement x="0" y="50" width="57" height="14">
					<printWhenExpression><![CDATA[$P{apresentarLinha3}.equals( true )]]></printWhenExpression>
				</reportElement>
				<textElement>
					<font size="8" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Data Mat.]]></text>
			</staticText>
			<staticText>
				<reportElement x="57" y="50" width="56" height="14">
					<printWhenExpression><![CDATA[$P{apresentarLinha3}.equals( true )]]></printWhenExpression>
				</reportElement>
				<textElement>
					<font size="8" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Inc. Plano]]></text>
			</staticText>
			<staticText>
				<reportElement x="113" y="50" width="57" height="14">
					<printWhenExpression><![CDATA[$P{apresentarLinha3}.equals( true )]]></printWhenExpression>
				</reportElement>
				<textElement>
					<font size="8" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Venc. Plano]]></text>
			</staticText>
			<staticText>
				<reportElement x="300" y="50" width="50" height="14">
					<printWhenExpression><![CDATA[$P{apresentarLinha3}.equals( true )]]></printWhenExpression>
				</reportElement>
				<textElement>
					<font size="8" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Últ.Acesso]]></text>
			</staticText>
			<staticText>
				<reportElement x="350" y="50" width="120" height="14">
					<printWhenExpression><![CDATA[$P{apresentarLinha3}.equals( true )]]></printWhenExpression>
				</reportElement>
				<textElement>
					<font size="8" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Plano]]></text>
			</staticText>
			<staticText>
				<reportElement x="520" y="50" width="130" height="14">
					<printWhenExpression><![CDATA[$P{apresentarLinha3}.equals( true )]]></printWhenExpression>
				</reportElement>
				<textElement>
					<font size="8" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Consultor]]></text>
			</staticText>
			<staticText>
				<reportElement x="600" y="22" width="70" height="14" isRemoveLineWhenBlank="true">
					<printWhenExpression><![CDATA[$P{apresentarLinha1}.equals( true )]]></printWhenExpression>
				</reportElement>
				<textElement>
					<font isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Nascimento]]></text>
			</staticText>
			<staticText>
				<reportElement x="730" y="22" width="50" height="13" isRemoveLineWhenBlank="true">
					<printWhenExpression><![CDATA[$P{apresentarLinha1}.equals( true )]]></printWhenExpression>
				</reportElement>
				<textElement>
					<font isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Sexo]]></text>
			</staticText>
			<staticText>
				<reportElement x="248" y="22" width="100" height="14">
					<printWhenExpression><![CDATA[$P{apresentarLinha1}.equals( true )]]></printWhenExpression>
				</reportElement>
				<textElement>
					<font isBold="true" isUnderline="false" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Documento]]></text>
			</staticText>
			<staticText>
				<reportElement x="309" y="22" width="100" height="14">
					<printWhenExpression><![CDATA[$P{apresentarLinha1}.equals( true )]]></printWhenExpression>
				</reportElement>
				<textElement>
					<font isBold="true" isUnderline="false" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Cnpj]]></text>
			</staticText>
			<staticText>
				<reportElement x="170" y="50" width="128" height="14">
					<printWhenExpression><![CDATA[$P{apresentarLinha3}.equals( true )]]></printWhenExpression>
				</reportElement>
				<textElement>
					<font size="8" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Resp. Contrato]]></text>
			</staticText>
			<staticText>
				<reportElement x="650" y="50" width="130" height="14">
					<printWhenExpression><![CDATA[$P{apresentarLinha3}.equals( true )]]></printWhenExpression>
				</reportElement>
				<textElement>
					<font size="8" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Professores]]></text>
			</staticText>
			<staticText>
				<reportElement x="637" y="36" width="56" height="14">
					<printWhenExpression><![CDATA[$P{apresentarLinha2}.equals( true )]]></printWhenExpression>
				</reportElement>
				<textElement>
					<font size="8" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Estado Civil]]></text>
			</staticText>
			<staticText>
				<reportElement x="693" y="36" width="87" height="14">
					<printWhenExpression><![CDATA[$P{apresentarLinha2}.equals( true )]]></printWhenExpression>
				</reportElement>
				<textElement>
					<font size="8" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Profissão]]></text>
			</staticText>
		</band>
	</columnHeader>
	<detail>
		<band height="50" splitType="Prevent">
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement key="textField-224" stretchType="RelativeToTallestObject" x="0" y="0" width="50" height="16" isRemoveLineWhenBlank="true">
					<printWhenExpression><![CDATA[$P{apresentarLinha1}.equals( true ) || $P{apresentarLinha2}.equals( true )]]></printWhenExpression>
				</reportElement>
				<textElement>
					<font size="10" isBold="true" isItalic="true" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{matricula}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement key="textField-224" stretchType="RelativeToTallestObject" x="50" y="0" width="200" height="16" isRemoveLineWhenBlank="true">
					<printWhenExpression><![CDATA[$P{apresentarLinha1}.equals( true ) || $P{apresentarLinha2}.equals( true )]]></printWhenExpression>
				</reportElement>
				<textElement>
					<font size="10" isBold="true" isItalic="true" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{nome}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement key="textField-224" stretchType="RelativeToTallestObject" x="350" y="0" width="100" height="16" isRemoveLineWhenBlank="true">
					<printWhenExpression><![CDATA[$P{apresentarLinha1}.equals( true )]]></printWhenExpression>
				</reportElement>
				<textElement>
					<font size="10" isBold="true" isItalic="true" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{telefone}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="dd/MM/yyyy" isBlankWhenNull="true">
				<reportElement key="textField-224" stretchType="RelativeToTallestObject" x="600" y="0" width="70" height="16" isRemoveLineWhenBlank="true">
					<printWhenExpression><![CDATA[$P{apresentarLinha1}.equals( true )]]></printWhenExpression>
				</reportElement>
				<textElement>
					<font size="10" isBold="true" isItalic="true" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression class="java.util.Date"><![CDATA[$F{dataNascimento}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement key="textField-224" stretchType="RelativeToTallestObject" x="670" y="0" width="60" height="16" isRemoveLineWhenBlank="true">
					<printWhenExpression><![CDATA[$P{apresentarLinha1}.equals( true )]]></printWhenExpression>
				</reportElement>
				<textElement>
					<font size="10" isBold="true" isItalic="true" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{situacaoCliente}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement key="textField-224" stretchType="RelativeToTallestObject" x="730" y="0" width="50" height="16" isRemoveLineWhenBlank="true">
					<printWhenExpression><![CDATA[$P{apresentarLinha1}.equals( true )]]></printWhenExpression>
				</reportElement>
				<textElement>
					<font size="10" isBold="true" isItalic="true" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{sexo}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="dd/MM/yyyy" isBlankWhenNull="true">
				<reportElement key="textField-224" stretchType="RelativeToTallestObject" x="470" y="32" width="50" height="16" isRemoveLineWhenBlank="true">
					<printWhenExpression><![CDATA[$P{apresentarLinha1}.equals( true ) || $P{apresentarLinha2}.equals( true ) || $P{apresentarLinha3}.equals( true )]]></printWhenExpression>
				</reportElement>
				<textElement>
					<font size="8" isBold="false" isItalic="false" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression class="java.util.Date"><![CDATA[$F{dataCadastro}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement stretchType="RelativeToTallestObject" x="0" y="16" width="180" height="16" isRemoveLineWhenBlank="true">
					<printWhenExpression><![CDATA[$P{apresentarLinha2}.equals( true )]]></printWhenExpression>
				</reportElement>
				<textElement>
					<font size="8" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{logradouro}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement stretchType="RelativeToTallestObject" x="180" y="16" width="50" height="16" isRemoveLineWhenBlank="true">
					<printWhenExpression><![CDATA[$P{apresentarLinha2}.equals( true )]]></printWhenExpression>
				</reportElement>
				<textElement>
					<font size="8" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{numero}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement stretchType="RelativeToTallestObject" x="230" y="16" width="140" height="16" isRemoveLineWhenBlank="true">
					<printWhenExpression><![CDATA[$P{apresentarLinha2}.equals( true )]]></printWhenExpression>
				</reportElement>
				<textElement>
					<font size="8" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{bairro}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement stretchType="RelativeToTallestObject" x="370" y="16" width="120" height="16" isRemoveLineWhenBlank="true">
					<printWhenExpression><![CDATA[$P{apresentarLinha2}.equals( true )]]></printWhenExpression>
				</reportElement>
				<textElement>
					<font size="8" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{cidade}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement stretchType="RelativeToTallestObject" x="490" y="16" width="56" height="16" isRemoveLineWhenBlank="true">
					<printWhenExpression><![CDATA[$P{apresentarLinha2}.equals( true )]]></printWhenExpression>
				</reportElement>
				<textElement>
					<font size="8" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{cep}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement stretchType="RelativeToTallestObject" x="546" y="16" width="91" height="16" isRemoveLineWhenBlank="true">
					<printWhenExpression><![CDATA[$P{apresentarLinha2}.equals( true )]]></printWhenExpression>
				</reportElement>
				<textElement>
					<font size="8" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{complemento}]]></textFieldExpression>
			</textField>
			<textField pattern="dd/MM/yyyy" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToTallestObject" x="0" y="32" width="57" height="16" isRemoveLineWhenBlank="true">
					<printWhenExpression><![CDATA[$P{apresentarLinha3}.equals( true )]]></printWhenExpression>
				</reportElement>
				<textElement>
					<font size="8" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression class="java.util.Date"><![CDATA[$F{dataMatricula}]]></textFieldExpression>
			</textField>
			<textField pattern="dd/MM/yyyy" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToTallestObject" x="58" y="32" width="55" height="16" isRemoveLineWhenBlank="true">
					<printWhenExpression><![CDATA[$P{apresentarLinha3}.equals( true )]]></printWhenExpression>
				</reportElement>
				<textElement>
					<font size="8" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression class="java.util.Date"><![CDATA[$F{inicioPlano}]]></textFieldExpression>
			</textField>
			<textField pattern="dd/MM/yyyy" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToTallestObject" x="116" y="32" width="54" height="16" isRemoveLineWhenBlank="true">
					<printWhenExpression><![CDATA[$P{apresentarLinha3}.equals( true )]]></printWhenExpression>
				</reportElement>
				<textElement>
					<font size="8" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression class="java.util.Date"><![CDATA[$F{vencimentoPlano}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="dd/MM/yyyy HH.mm.ss" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToTallestObject" x="300" y="32" width="50" height="16" isRemoveLineWhenBlank="true">
					<printWhenExpression><![CDATA[$P{apresentarLinha3}.equals( true )]]></printWhenExpression>
				</reportElement>
				<textElement>
					<font size="8" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression class="java.util.Date"><![CDATA[$F{dataUltimoAcesso}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="350" y="32" width="120" height="16" isRemoveLineWhenBlank="true" isPrintWhenDetailOverflows="true">
					<printWhenExpression><![CDATA[$P{apresentarLinha3}.equals( true )]]></printWhenExpression>
				</reportElement>
				<textElement>
					<font size="8" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{plano}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="520" y="32" width="130" height="16" isRemoveLineWhenBlank="true" isPrintWhenDetailOverflows="true">
					<printWhenExpression><![CDATA[$P{apresentarLinha3}.equals( true )]]></printWhenExpression>
				</reportElement>
				<textElement>
					<font size="8" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{nomeConsultor}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement key="textField-224" stretchType="RelativeToTallestObject" x="450" y="0" width="150" height="16" isRemoveLineWhenBlank="true">
					<printWhenExpression><![CDATA[$P{apresentarLinha1}.equals( true )]]></printWhenExpression>
				</reportElement>
				<textElement>
					<font size="10" isBold="true" isItalic="true" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{email}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement key="textField-224" stretchType="RelativeToTallestObject" x="243" y="0" width="100" height="16" isRemoveLineWhenBlank="true">
					<printWhenExpression><![CDATA[$P{apresentarLinha1}.equals( true )]]></printWhenExpression>
				</reportElement>
				<textElement>
					<font isBold="true" isItalic="true" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{documento}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement key="textField-224" stretchType="RelativeToTallestObject" x="314" y="0" width="100" height="16" isRemoveLineWhenBlank="true">
					<printWhenExpression><![CDATA[$P{apresentarLinha1}.equals( true )]]></printWhenExpression>
				</reportElement>
				<textElement>
					<font isBold="true" isItalic="true" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{cnpj}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="170" y="32" width="130" height="16" isRemoveLineWhenBlank="true" isPrintWhenDetailOverflows="true">
					<printWhenExpression><![CDATA[$P{apresentarLinha3}.equals( true )]]></printWhenExpression>
				</reportElement>
				<textElement>
					<font size="8" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{nomeRespContrato}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="650" y="32" width="130" height="16" isRemoveLineWhenBlank="true" isPrintWhenDetailOverflows="true">
					<printWhenExpression><![CDATA[$P{apresentarLinha3}.equals( true )]]></printWhenExpression>
				</reportElement>
				<textElement>
					<font size="8" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{nomeProfessores}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement stretchType="RelativeToTallestObject" x="639" y="16" width="54" height="16" isRemoveLineWhenBlank="true">
					<printWhenExpression><![CDATA[$P{apresentarLinha2}.equals( true )]]></printWhenExpression>
				</reportElement>
				<textElement>
					<font size="8" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{estadoCivil}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement stretchType="RelativeToTallestObject" x="693" y="16" width="87" height="16" isRemoveLineWhenBlank="true">
					<printWhenExpression><![CDATA[$P{apresentarLinha2}.equals( true )]]></printWhenExpression>
				</reportElement>
				<textElement>
					<font size="8" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{profissao}]]></textFieldExpression>
			</textField>
		</band>
		<band height="37">
			<printWhenExpression><![CDATA[(!$F{justificativa}.trim().equals("") || !$F{observacao}.trim().equals("")) || !$F{dataOperacao}.equals(null)]]></printWhenExpression>
			<textField>
				<reportElement x="240" y="16" width="540" height="16" isRemoveLineWhenBlank="true">
					<printWhenExpression><![CDATA[$F{observacao}.length() > 0]]></printWhenExpression>
				</reportElement>
				<textElement verticalAlignment="Middle">
					<font size="8" isBold="false"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA["Observação: " + $F{observacao}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="0" y="16" width="240" height="16" isRemoveLineWhenBlank="true">
					<printWhenExpression><![CDATA[$F{justificativa}.length() > 0]]></printWhenExpression>
				</reportElement>
				<textElement verticalAlignment="Middle">
					<font size="8" isBold="false"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA["Justificativa: " + $F{justificativa}]]></textFieldExpression>
			</textField>
			<textField pattern="dd/MM/yyyy" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToTallestObject" x="276" y="0" width="82" height="16" isRemoveLineWhenBlank="true">
					<printWhenExpression><![CDATA[!$F{dataInicioOperacao}.equals(null)]]></printWhenExpression>
				</reportElement>
				<textElement>
					<font size="8" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression class="java.util.Date"><![CDATA[$F{dataInicioOperacao}]]></textFieldExpression>
			</textField>
			<textField pattern="dd/MM/yyyy" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToTallestObject" x="432" y="0" width="140" height="16" isRemoveLineWhenBlank="true">
					<printWhenExpression><![CDATA[!$F{dataFimOperacao}.equals(null)]]></printWhenExpression>
				</reportElement>
				<textElement>
					<font size="8" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression class="java.util.Date"><![CDATA[$F{dataFimOperacao}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="200" y="0" width="76" height="16">
					<printWhenExpression><![CDATA[!$F{dataInicioOperacao}.equals(null)]]></printWhenExpression>
				</reportElement>
				<textElement>
					<font size="8" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Inicio da Operação:]]></text>
			</staticText>
			<staticText>
				<reportElement x="362" y="0" width="70" height="16">
					<printWhenExpression><![CDATA[!$F{dataFimOperacao}.equals(null)]]></printWhenExpression>
				</reportElement>
				<textElement>
					<font size="8" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Fim da Operação:]]></text>
			</staticText>
			<staticText>
				<reportElement x="2" y="0" width="111" height="16">
					<printWhenExpression><![CDATA[!$F{dataOperacao}.equals(null)]]></printWhenExpression>
				</reportElement>
				<textElement>
					<font size="8" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Lançamento da  Operação:]]></text>
			</staticText>
			<textField pattern="dd/MM/yyyy" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToTallestObject" x="113" y="0" width="82" height="16" isRemoveLineWhenBlank="true">
					<printWhenExpression><![CDATA[!$F{dataOperacao}.equals(null)]]></printWhenExpression>
				</reportElement>
				<textElement>
					<font size="8" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression class="java.util.Date"><![CDATA[$F{dataOperacao}]]></textFieldExpression>
			</textField>
		</band>
		<band height="2">
			<line>
				<reportElement positionType="FixRelativeToBottom" x="0" y="0" width="786" height="1" forecolor="#CCCCCC"/>
			</line>
		</band>
	</detail>
	<summary>
		<band height="18" splitType="Prevent">
			<staticText>
				<reportElement key="staticText-2" positionType="Float" x="0" y="2" width="100" height="14"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Total de Clientes: ]]></text>
			</staticText>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement key="textField-224" positionType="Float" x="100" y="2" width="100" height="14"/>
				<textElement textAlignment="Center" verticalAlignment="Middle" markup="html">
					<font size="8" isBold="false" isStrikeThrough="false"/>
				</textElement>
				<textFieldExpression class="java.lang.Integer"><![CDATA[$P{totalClientes}]]></textFieldExpression>
			</textField>
		</band>
	</summary>
</jasperReport>
