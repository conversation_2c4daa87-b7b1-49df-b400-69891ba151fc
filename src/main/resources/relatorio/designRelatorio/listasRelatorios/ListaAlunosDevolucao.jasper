¬í sr (net.sf.jasperreports.engine.JasperReport      'Ø L compileDatat Ljava/io/Serializable;L compileNameSuffixt Ljava/lang/String;L 
compilerClassq ~ xr -net.sf.jasperreports.engine.base.JRBaseReport      'Ø +I PSEUDO_SERIAL_VERSION_UIDI bottomMarginI columnCountI 
columnSpacingI columnWidthZ ignorePaginationZ isFloatColumnFooterZ isSummaryNewPageZ  isSummaryWithPageHeaderAndFooterZ isTitleNewPageI 
leftMarginB orientationI 
pageHeightI 	pageWidthB 
printOrderI rightMarginI 	topMarginB whenNoDataTypeL 
backgroundt $Lnet/sf/jasperreports/engine/JRBand;L columnFooterq ~ L columnHeaderq ~ [ datasetst ([Lnet/sf/jasperreports/engine/JRDataset;L defaultFontt *Lnet/sf/jasperreports/engine/JRReportFont;L defaultStylet %Lnet/sf/jasperreports/engine/JRStyle;L detailq ~ L 
detailSectiont 'Lnet/sf/jasperreports/engine/JRSection;[ fontst +[Lnet/sf/jasperreports/engine/JRReportFont;L formatFactoryClassq ~ L 
importsSett Ljava/util/Set;L languageq ~ L lastPageFooterq ~ L mainDatasett 'Lnet/sf/jasperreports/engine/JRDataset;L nameq ~ L noDataq ~ L orientationValuet 2Lnet/sf/jasperreports/engine/type/OrientationEnum;L 
pageFooterq ~ L 
pageHeaderq ~ L printOrderValuet 1Lnet/sf/jasperreports/engine/type/PrintOrderEnum;[ stylest &[Lnet/sf/jasperreports/engine/JRStyle;L summaryq ~ [ 	templatest /[Lnet/sf/jasperreports/engine/JRReportTemplate;L titleq ~ L whenNoDataTypeValuet 5Lnet/sf/jasperreports/engine/type/WhenNoDataTypeEnum;xp  wî                       S  J        sr +net.sf.jasperreports.engine.base.JRBaseBand      'Ø I PSEUDO_SERIAL_VERSION_UIDI heightZ isSplitAllowedL printWhenExpressiont *Lnet/sf/jasperreports/engine/JRExpression;L 	splitTypet Ljava/lang/Byte;L splitTypeValuet 0Lnet/sf/jasperreports/engine/type/SplitTypeEnum;xr 3net.sf.jasperreports.engine.base.JRBaseElementGroup      'Ø L childrent Ljava/util/List;L elementGroupt ,Lnet/sf/jasperreports/engine/JRElementGroup;xpsr java.util.ArrayListxÒÇa I sizexp    w    xp  wî    pp~r .net.sf.jasperreports.engine.type.SplitTypeEnum          xr java.lang.Enum          xpt STRETCHpsq ~ sq ~    !w   !sr 1net.sf.jasperreports.engine.base.JRBaseStaticText      'Ø L textq ~ xr 2net.sf.jasperreports.engine.base.JRBaseTextElement      'Ø %I PSEUDO_SERIAL_VERSION_UIDL borderq ~ L borderColort Ljava/awt/Color;L bottomBorderq ~ L bottomBorderColorq ~ $L 
bottomPaddingt Ljava/lang/Integer;L fontNameq ~ L fontSizeq ~ %L horizontalAlignmentq ~ L horizontalAlignmentValuet 6Lnet/sf/jasperreports/engine/type/HorizontalAlignEnum;L isBoldt Ljava/lang/Boolean;L isItalicq ~ 'L 
isPdfEmbeddedq ~ 'L isStrikeThroughq ~ 'L isStyledTextq ~ 'L isUnderlineq ~ 'L 
leftBorderq ~ L leftBorderColorq ~ $L leftPaddingq ~ %L lineBoxt 'Lnet/sf/jasperreports/engine/JRLineBox;L lineSpacingq ~ L lineSpacingValuet 2Lnet/sf/jasperreports/engine/type/LineSpacingEnum;L markupq ~ L paddingq ~ %L pdfEncodingq ~ L pdfFontNameq ~ L 
reportFontq ~ L rightBorderq ~ L rightBorderColorq ~ $L rightPaddingq ~ %L rotationq ~ L 
rotationValuet /Lnet/sf/jasperreports/engine/type/RotationEnum;L 	topBorderq ~ L topBorderColorq ~ $L 
topPaddingq ~ %L verticalAlignmentq ~ L verticalAlignmentValuet 4Lnet/sf/jasperreports/engine/type/VerticalAlignEnum;xr .net.sf.jasperreports.engine.base.JRBaseElement      'Ø I PSEUDO_SERIAL_VERSION_UIDI heightZ isPrintInFirstWholeBandZ isPrintRepeatedValuesZ isPrintWhenDetailOverflowsZ isRemoveLineWhenBlankB positionTypeB stretchTypeI widthI xI yL 	backcolorq ~ $L defaultStyleProvidert 4Lnet/sf/jasperreports/engine/JRDefaultStyleProvider;L elementGroupq ~ L 	forecolorq ~ $L keyq ~ L modeq ~ L 	modeValuet +Lnet/sf/jasperreports/engine/type/ModeEnum;L parentStyleq ~ L parentStyleNameReferenceq ~ L positionTypeValuet 3Lnet/sf/jasperreports/engine/type/PositionTypeEnum;L printWhenExpressionq ~ L printWhenGroupChangest %Lnet/sf/jasperreports/engine/JRGroup;L 
propertiesMapt -Lnet/sf/jasperreports/engine/JRPropertiesMap;[ propertyExpressionst 3[Lnet/sf/jasperreports/engine/JRPropertyExpression;L stretchTypeValuet 2Lnet/sf/jasperreports/engine/type/StretchTypeEnum;xp  wî          È   2   pq ~ q ~  pt  pppp~r 1net.sf.jasperreports.engine.type.PositionTypeEnum          xq ~ t FLOATsr 1net.sf.jasperreports.engine.base.JRBaseExpression      'Ø I id[ chunkst 0[Lnet/sf/jasperreports/engine/JRExpressionChunk;L valueClassNameq ~ L valueClassRealNameq ~ xp   ur 0[Lnet.sf.jasperreports.engine.JRExpressionChunk;mYÏÞiK£U  xp   sr 6net.sf.jasperreports.engine.base.JRBaseExpressionChunk      'Ø B typeL textq ~ xpt apresentarLinha1sq ~ >t .equals( true ) || sq ~ >t apresentarLinha2sq ~ >t .equals( true )t java.lang.Booleanpppp~r 0net.sf.jasperreports.engine.type.StretchTypeEnum          xq ~ t 
NO_STRETCH  wîpppppppppsr java.lang.BooleanÍ rÕúî Z valuexpppppppppsr .net.sf.jasperreports.engine.base.JRBaseLineBox      'Ø L 
bottomPaddingq ~ %L 	bottomPent +Lnet/sf/jasperreports/engine/base/JRBoxPen;L boxContainert ,Lnet/sf/jasperreports/engine/JRBoxContainer;L leftPaddingq ~ %L leftPenq ~ NL paddingq ~ %L penq ~ NL rightPaddingq ~ %L rightPenq ~ NL 
topPaddingq ~ %L topPenq ~ Nxppsr 3net.sf.jasperreports.engine.base.JRBaseBoxBottomPen      'Ø  xr -net.sf.jasperreports.engine.base.JRBaseBoxPen      'Ø L lineBoxq ~ (xr *net.sf.jasperreports.engine.base.JRBasePen      'Ø I PSEUDO_SERIAL_VERSION_UIDL 	lineColorq ~ $L 	lineStyleq ~ L lineStyleValuet 0Lnet/sf/jasperreports/engine/type/LineStyleEnum;L 	lineWidtht Ljava/lang/Float;L penContainert ,Lnet/sf/jasperreports/engine/JRPenContainer;xp  wîppppq ~ Pq ~ Pq ~ 4psr 1net.sf.jasperreports.engine.base.JRBaseBoxLeftPen      'Ø  xq ~ R  wîppppq ~ Pq ~ Ppsq ~ R  wîppppq ~ Pq ~ Ppsr 2net.sf.jasperreports.engine.base.JRBaseBoxRightPen      'Ø  xq ~ R  wîppppq ~ Pq ~ Ppsr 0net.sf.jasperreports.engine.base.JRBaseBoxTopPen      'Ø  xq ~ R  wîppppq ~ Pq ~ Ppppppt Helvetica-Boldpppppppppppt Nomesq ~ "  wî          2       pq ~ q ~  pq ~ 5ppppq ~ 7sq ~ 9   uq ~ <   sq ~ >t apresentarLinha1sq ~ >t .equals( true ) || sq ~ >t apresentarLinha2sq ~ >t .equals( true )q ~ Gppppq ~ I  wîpppppppppq ~ Lppppppppsq ~ Mpsq ~ Q  wîppppq ~ lq ~ lq ~ apsq ~ X  wîppppq ~ lq ~ lpsq ~ R  wîppppq ~ lq ~ lpsq ~ [  wîppppq ~ lq ~ lpsq ~ ]  wîppppq ~ lq ~ lpppppt Helvetica-Boldpppppppppppt Mat.sr +net.sf.jasperreports.engine.base.JRBaseLine      'Ø I PSEUDO_SERIAL_VERSION_UIDB 	directionL directionValuet 4Lnet/sf/jasperreports/engine/type/LineDirectionEnum;xr 5net.sf.jasperreports.engine.base.JRBaseGraphicElement      'Ø I PSEUDO_SERIAL_VERSION_UIDL fillq ~ L 	fillValuet +Lnet/sf/jasperreports/engine/type/FillEnum;L linePent #Lnet/sf/jasperreports/engine/JRPen;L penq ~ xq ~ ,  wî                ;pq ~ q ~  pt line-4ppppq ~ 7ppppq ~ I  wîppsq ~ S  wîppppq ~ yp  wî ~r 2net.sf.jasperreports.engine.type.LineDirectionEnum          xq ~ t TOP_DOWNsq ~ "  wî          d  ^   pq ~ q ~  pq ~ 5ppppq ~ 7sq ~ 9   uq ~ <   sq ~ >t apresentarLinha1sq ~ >t .equals( true )q ~ Gppppq ~ I  wîpppppppppq ~ Lppppppppsq ~ Mpsq ~ Q  wîppppq ~ q ~ q ~ psq ~ X  wîppppq ~ q ~ psq ~ R  wîppppq ~ q ~ psq ~ [  wîppppq ~ q ~ psq ~ ]  wîppppq ~ q ~ pppppt Helvetica-Boldpppppppppppt Telefonesq ~ "  wî          2  Ö   pq ~ q ~  pq ~ 5ppppq ~ 7sq ~ 9   uq ~ <   sq ~ >t apresentarLinha1sq ~ >t .equals( true ) || sq ~ >t apresentarLinha2sq ~ >t .equals( true ) || sq ~ >t apresentarLinha3sq ~ >t .equals( true )q ~ Gppppq ~ I  wîppppppsr java.lang.Integerâ ¤÷8 I valuexr java.lang.Number¬à  xp   ppq ~ Lppppppppsq ~ Mpsq ~ Q  wîppppq ~  q ~  q ~ psq ~ X  wîppppq ~  q ~  psq ~ R  wîppppq ~  q ~  psq ~ [  wîppppq ~  q ~  psq ~ ]  wîppppq ~  q ~  pppppt Helvetica-Boldpppppppppppt Cadastrosq ~ "  wî          <     pq ~ q ~  pq ~ 5ppppq ~ 7sq ~ 9   uq ~ <   sq ~ >t apresentarLinha1sq ~ >t .equals( true )q ~ Gppppq ~ I  wîpppppppppq ~ Lppppppppsq ~ Mpsq ~ Q  wîppppq ~ ¯q ~ ¯q ~ ¨psq ~ X  wîppppq ~ ¯q ~ ¯psq ~ R  wîppppq ~ ¯q ~ ¯psq ~ [  wîppppq ~ ¯q ~ ¯psq ~ ]  wîppppq ~ ¯q ~ ¯pppppt Helvetica-Boldpppppppppppt 
SituaÃ§Ã£osq ~ "  wî          ´       pq ~ q ~  pq ~ 5ppppq ~ 7sq ~ 9   uq ~ <   sq ~ >t apresentarLinha2sq ~ >t .equals( true )q ~ Gppppq ~ I  wîppppppq ~ ppq ~ Lppppppppsq ~ Mpsq ~ Q  wîppppq ~ ¾q ~ ¾q ~ ·psq ~ X  wîppppq ~ ¾q ~ ¾psq ~ R  wîppppq ~ ¾q ~ ¾psq ~ [  wîppppq ~ ¾q ~ ¾psq ~ ]  wîppppq ~ ¾q ~ ¾pppppt Helvetica-Boldpppppppppppt 
Logradourosq ~ "  wî            Â   pq ~ q ~  ppppppq ~ 7sq ~ 9   uq ~ <   sq ~ >t apresentarLinha1sq ~ >t .equals( true )q ~ Gppppq ~ I  wîppppppsq ~    
ppq ~ Lppppppppsq ~ Mpsq ~ Q  wîppppq ~ Îq ~ Îq ~ Æpsq ~ X  wîppppq ~ Îq ~ Îpsq ~ R  wîppppq ~ Îq ~ Îpsq ~ [  wîppppq ~ Îq ~ Îpsq ~ ]  wîppppq ~ Îq ~ Îpppppt Helvetica-Boldpppppppppppt Emailsq ~ "  wî           2   ´   pq ~ q ~  pppppp~q ~ 6t FIX_RELATIVE_TO_TOPsq ~ 9   uq ~ <   sq ~ >t apresentarLinha2sq ~ >t .equals( true )q ~ Gppppq ~ I  wîppppppq ~ ppq ~ Lppppppppsq ~ Mpsq ~ Q  wîppppq ~ ßq ~ ßq ~ Öpsq ~ X  wîppppq ~ ßq ~ ßpsq ~ R  wîppppq ~ ßq ~ ßpsq ~ [  wîppppq ~ ßq ~ ßpsq ~ ]  wîppppq ~ ßq ~ ßpppppt Helvetica-Boldpppppppppppt NÃºmerosq ~ "  wî              æ   pq ~ q ~  ppppppq ~ ×sq ~ 9   uq ~ <   sq ~ >t apresentarLinha2sq ~ >t .equals( true )q ~ Gppppq ~ I  wîppppppq ~ ppq ~ Lppppppppsq ~ Mpsq ~ Q  wîppppq ~ îq ~ îq ~ çpsq ~ X  wîppppq ~ îq ~ îpsq ~ R  wîppppq ~ îq ~ îpsq ~ [  wîppppq ~ îq ~ îpsq ~ ]  wîppppq ~ îq ~ îpppppt Helvetica-Boldpppppppppppt Bairrosq ~ "  wî           x  r   pq ~ q ~  ppppppq ~ ×sq ~ 9   uq ~ <   sq ~ >t apresentarLinha2sq ~ >t .equals( true )q ~ Gppppq ~ I  wîppppppq ~ ppq ~ Lppppppppsq ~ Mpsq ~ Q  wîppppq ~ ýq ~ ýq ~ öpsq ~ X  wîppppq ~ ýq ~ ýpsq ~ R  wîppppq ~ ýq ~ ýpsq ~ [  wîppppq ~ ýq ~ ýpsq ~ ]  wîppppq ~ ýq ~ ýpppppt Helvetica-Boldpppppppppppt Cidadesq ~ "  wî           8  ê   pq ~ q ~  ppppppq ~ ×sq ~ 9   uq ~ <   sq ~ >t apresentarLinha2sq ~ >t .equals( true )q ~ Gppppq ~ I  wîppppppq ~ ppq ~ Lppppppppsq ~ Mpsq ~ Q  wîppppq ~q ~q ~psq ~ X  wîppppq ~q ~psq ~ R  wîppppq ~q ~psq ~ [  wîppppq ~q ~psq ~ ]  wîppppq ~q ~pppppt Helvetica-Boldpppppppppppt CEPsq ~ "  wî           [  "   pq ~ q ~  ppppppq ~ ×sq ~ 9   uq ~ <   sq ~ >t apresentarLinha2sq ~ >t .equals( true )q ~ Gppppq ~ I  wîppppppq ~ ppq ~ Lppppppppsq ~ Mpsq ~ Q  wîppppq ~q ~q ~psq ~ X  wîppppq ~q ~psq ~ R  wîppppq ~q ~psq ~ [  wîppppq ~q ~psq ~ ]  wîppppq ~q ~pppppt Helvetica-Boldpppppppppppt Complementosq ~ "  wî           9       pq ~ q ~  ppppppq ~ ×sq ~ 9   uq ~ <   sq ~ >t apresentarLinha3sq ~ >t .equals( true )q ~ Gppppq ~ I  wîppppppq ~ ppq ~ Lppppppppsq ~ Mpsq ~ Q  wîppppq ~*q ~*q ~#psq ~ X  wîppppq ~*q ~*psq ~ R  wîppppq ~*q ~*psq ~ [  wîppppq ~*q ~*psq ~ ]  wîppppq ~*q ~*pppppt Helvetica-Boldpppppppppppt 	Data Mat.sq ~ "  wî           8   9   pq ~ q ~  ppppppq ~ ×sq ~ 9    uq ~ <   sq ~ >t apresentarLinha3sq ~ >t .equals( true )q ~ Gppppq ~ I  wîppppppq ~ ppq ~ Lppppppppsq ~ Mpsq ~ Q  wîppppq ~9q ~9q ~2psq ~ X  wîppppq ~9q ~9psq ~ R  wîppppq ~9q ~9psq ~ [  wîppppq ~9q ~9psq ~ ]  wîppppq ~9q ~9pppppt Helvetica-Boldpppppppppppt 
Inc. Planosq ~ "  wî           9   q   pq ~ q ~  ppppppq ~ ×sq ~ 9   !uq ~ <   sq ~ >t apresentarLinha3sq ~ >t .equals( true )q ~ Gppppq ~ I  wîppppppq ~ ppq ~ Lppppppppsq ~ Mpsq ~ Q  wîppppq ~Hq ~Hq ~Apsq ~ X  wîppppq ~Hq ~Hpsq ~ R  wîppppq ~Hq ~Hpsq ~ [  wîppppq ~Hq ~Hpsq ~ ]  wîppppq ~Hq ~Hpppppt Helvetica-Boldpppppppppppt Venc. Planosq ~ "  wî           2  ,   pq ~ q ~  ppppppq ~ ×sq ~ 9   "uq ~ <   sq ~ >t apresentarLinha3sq ~ >t .equals( true )q ~ Gppppq ~ I  wîppppppq ~ ppq ~ Lppppppppsq ~ Mpsq ~ Q  wîppppq ~Wq ~Wq ~Ppsq ~ X  wîppppq ~Wq ~Wpsq ~ R  wîppppq ~Wq ~Wpsq ~ [  wîppppq ~Wq ~Wpsq ~ ]  wîppppq ~Wq ~Wpppppt Helvetica-Boldpppppppppppt Ãlt.Acessosq ~ "  wî           x  ^   pq ~ q ~  ppppppq ~ ×sq ~ 9   #uq ~ <   sq ~ >t apresentarLinha3sq ~ >t .equals( true )q ~ Gppppq ~ I  wîppppppq ~ ppq ~ Lppppppppsq ~ Mpsq ~ Q  wîppppq ~fq ~fq ~_psq ~ X  wîppppq ~fq ~fpsq ~ R  wîppppq ~fq ~fpsq ~ [  wîppppq ~fq ~fpsq ~ ]  wîppppq ~fq ~fpppppt Helvetica-Boldpppppppppppt Planosq ~ "  wî                pq ~ q ~  ppppppq ~ ×sq ~ 9   $uq ~ <   sq ~ >t apresentarLinha3sq ~ >t .equals( true )q ~ Gppppq ~ I  wîppppppq ~ ppq ~ Lppppppppsq ~ Mpsq ~ Q  wîppppq ~uq ~uq ~npsq ~ X  wîppppq ~uq ~upsq ~ R  wîppppq ~uq ~upsq ~ [  wîppppq ~uq ~upsq ~ ]  wîppppq ~uq ~upppppt Helvetica-Boldpppppppppppt 	Consultorsq ~ "  wî          F  X   pq ~ q ~  ppppppq ~ ×sq ~ 9   %uq ~ <   sq ~ >t apresentarLinha1sq ~ >t .equals( true )q ~ Gppppq ~ I  wîpppppppppq ~ Lppppppppsq ~ Mpsq ~ Q  wîppppq ~q ~q ~}psq ~ X  wîppppq ~q ~psq ~ R  wîppppq ~q ~psq ~ [  wîppppq ~q ~psq ~ ]  wîppppq ~q ~pppppt Helvetica-Boldpppppppppppt 
Nascimentosq ~ "  wî   
       2  Ú   pq ~ q ~  ppppppq ~ ×sq ~ 9   &uq ~ <   sq ~ >t apresentarLinha1sq ~ >t .equals( true )q ~ Gppppq ~ I  wîpppppppppq ~ Lppppppppsq ~ Mpsq ~ Q  wîppppq ~q ~q ~psq ~ X  wîppppq ~q ~psq ~ R  wîppppq ~q ~psq ~ [  wîppppq ~q ~psq ~ ]  wîppppq ~q ~pppppt Helvetica-Boldpppppppppppt Sexosq ~ "  wî           d   ø   pq ~ q ~  ppppppq ~ ×sq ~ 9   'uq ~ <   sq ~ >t apresentarLinha1sq ~ >t .equals( true )q ~ Gppppq ~ I  wîpppppppppq ~ Lppppsq ~ K pppsq ~ Mpsq ~ Q  wîppppq ~£q ~£q ~psq ~ X  wîppppq ~£q ~£psq ~ R  wîppppq ~£q ~£psq ~ [  wîppppq ~£q ~£psq ~ ]  wîppppq ~£q ~£pppppt Helvetica-Boldpppppppppppt 	Documentosq ~ "  wî           d  5   pq ~ q ~  ppppppq ~ ×sq ~ 9   (uq ~ <   sq ~ >t apresentarLinha1sq ~ >t .equals( true )q ~ Gppppq ~ I  wîpppppppppq ~ Lppppq ~¢pppsq ~ Mpsq ~ Q  wîppppq ~²q ~²q ~«psq ~ X  wîppppq ~²q ~²psq ~ R  wîppppq ~²q ~²psq ~ [  wîppppq ~²q ~²psq ~ ]  wîppppq ~²q ~²pppppt Helvetica-Boldpppppppppppt Cnpjsq ~ "  wî              ª   pq ~ q ~  ppppppq ~ ×sq ~ 9   )uq ~ <   sq ~ >t apresentarLinha3sq ~ >t .equals( true )q ~ Gppppq ~ I  wîppppppq ~ ppq ~ Lppppppppsq ~ Mpsq ~ Q  wîppppq ~Áq ~Áq ~ºpsq ~ X  wîppppq ~Áq ~Ápsq ~ R  wîppppq ~Áq ~Ápsq ~ [  wîppppq ~Áq ~Ápsq ~ ]  wîppppq ~Áq ~Ápppppt Helvetica-Boldpppppppppppt Resp. Contratosq ~ "  wî                pq ~ q ~  ppppppq ~ ×sq ~ 9   *uq ~ <   sq ~ >t apresentarLinha3sq ~ >t .equals( true )q ~ Gppppq ~ I  wîppppppq ~ ppq ~ Lppppppppsq ~ Mpsq ~ Q  wîppppq ~Ðq ~Ðq ~Épsq ~ X  wîppppq ~Ðq ~Ðpsq ~ R  wîppppq ~Ðq ~Ðpsq ~ [  wîppppq ~Ðq ~Ðpsq ~ ]  wîppppq ~Ðq ~Ðpppppt Helvetica-Boldpppppppppppt Professoressq ~ "  wî           8  }   pq ~ q ~  ppppppq ~ ×sq ~ 9   +uq ~ <   sq ~ >t apresentarLinha2sq ~ >t .equals( true )q ~ Gppppq ~ I  wîppppppq ~ ppq ~ Lppppppppsq ~ Mpsq ~ Q  wîppppq ~ßq ~ßq ~Øpsq ~ X  wîppppq ~ßq ~ßpsq ~ R  wîppppq ~ßq ~ßpsq ~ [  wîppppq ~ßq ~ßpsq ~ ]  wîppppq ~ßq ~ßpppppt Helvetica-Boldpppppppppppt Estado Civilsq ~ "  wî           W  µ   pq ~ q ~  ppppppq ~ ×sq ~ 9   ,uq ~ <   sq ~ >t apresentarLinha2sq ~ >t .equals( true )q ~ Gppppq ~ I  wîppppppq ~ ppq ~ Lppppppppsq ~ Mpsq ~ Q  wîppppq ~îq ~îq ~çpsq ~ X  wîppppq ~îq ~îpsq ~ R  wîppppq ~îq ~îpsq ~ [  wîppppq ~îq ~îpsq ~ ]  wîppppq ~îq ~îpppppt Helvetica-Boldpppppppppppt 
ProfissÃ£osq ~ "  wî           I     ,pq ~ q ~  ppppppq ~ ×sq ~ 9   -uq ~ <   sq ~ >t apresentarLinha3sq ~ >t .equals( true )q ~ Gppppq ~ I  wîppppppq ~ ppq ~ Lppppppppsq ~ Mpsq ~ Q  wîppppq ~ýq ~ýq ~öpsq ~ X  wîppppq ~ýq ~ýpsq ~ R  wîppppq ~ýq ~ýpsq ~ [  wîppppq ~ýq ~ýpsq ~ ]  wîppppq ~ýq ~ýpppppt Helvetica-Boldpppppppppppt Valor Devolvidosq ~ "  wî           9       ,pq ~ q ~  ppppppq ~ ×sq ~ 9   .uq ~ <   sq ~ >t apresentarLinha3sq ~ >t .equals( true )q ~ Gppppq ~ I  wîppppppq ~ ppq ~ Lppppppppsq ~ Mpsq ~ Q  wîppppq ~q ~q ~psq ~ X  wîppppq ~q ~psq ~ R  wîppppq ~q ~psq ~ [  wîppppq ~q ~psq ~ ]  wîppppq ~q ~pppppt Helvetica-Boldpppppppppppt Cpfsq ~ "  wî           x  ^   ,pq ~ q ~  ppppppq ~ ×sq ~ 9   /uq ~ <   sq ~ >t apresentarLinha3sq ~ >t .equals( true )q ~ Gppppq ~ I  wîppppppq ~ ppq ~ Lppppppppsq ~ Mpsq ~ Q  wîppppq ~q ~q ~psq ~ X  wîppppq ~q ~psq ~ R  wîppppq ~q ~psq ~ [  wîppppq ~q ~psq ~ ]  wîppppq ~q ~pppppt Helvetica-Boldpppppppppppt DescriÃ§Ã£o do Cancelamentosq ~ "  wî           8   9   ,pq ~ q ~  ppppppq ~ ×sq ~ 9   0uq ~ <   sq ~ >t apresentarLinha3sq ~ >t .equals( true )q ~ Gppppq ~ I  wîppppppq ~ ppq ~ Lppppppppsq ~ Mpsq ~ Q  wîppppq ~*q ~*q ~#psq ~ X  wîppppq ~*q ~*psq ~ R  wîppppq ~*q ~*psq ~ [  wîppppq ~*q ~*psq ~ ]  wîppppq ~*q ~*pppppt Helvetica-Boldpppppppppppt Valor Contratosq ~ "  wî           9   y   ,pq ~ q ~  ppppppq ~ ×sq ~ 9   1uq ~ <   sq ~ >t apresentarLinha3sq ~ >t .equals( true )q ~ Gppppq ~ I  wîppppppq ~ ppq ~ Lppppppppsq ~ Mpsq ~ Q  wîppppq ~9q ~9q ~2psq ~ X  wîppppq ~9q ~9psq ~ R  wîppppq ~9q ~9psq ~ [  wîppppq ~9q ~9psq ~ ]  wîppppq ~9q ~9pppppt Helvetica-Boldpppppppppppt 
Valor Pagosq ~ "  wî           B   ¹   ,pq ~ q ~  ppppppq ~ ×sq ~ 9   2uq ~ <   sq ~ >t apresentarLinha3sq ~ >t .equals( true )q ~ Gppppq ~ I  wîppppppq ~ ppq ~ Lppppppppsq ~ Mpsq ~ Q  wîppppq ~Hq ~Hq ~Apsq ~ X  wîppppq ~Hq ~Hpsq ~ R  wîppppq ~Hq ~Hpsq ~ [  wîppppq ~Hq ~Hpsq ~ ]  wîppppq ~Hq ~Hpppppt Helvetica-Boldpppppppppppt Valor Utilizadoxp  wî   =ppq ~ ppppsr .net.sf.jasperreports.engine.base.JRBaseSection      'Ø [ bandst %[Lnet/sf/jasperreports/engine/JRBand;xpur %[Lnet.sf.jasperreports.engine.JRBand;Ý~ìÊ5  xp   sq ~ sq ~     w    sr 0net.sf.jasperreports.engine.base.JRBaseTextField      'Ø I PSEUDO_SERIAL_VERSION_UIDI 
bookmarkLevelB evaluationTimeB hyperlinkTargetB 
hyperlinkTypeZ isStretchWithOverflowL anchorNameExpressionq ~ L evaluationGroupq ~ 0L evaluationTimeValuet 5Lnet/sf/jasperreports/engine/type/EvaluationTimeEnum;L 
expressionq ~ L hyperlinkAnchorExpressionq ~ L hyperlinkPageExpressionq ~ [ hyperlinkParameterst 3[Lnet/sf/jasperreports/engine/JRHyperlinkParameter;L hyperlinkReferenceExpressionq ~ L hyperlinkTooltipExpressionq ~ L isBlankWhenNullq ~ 'L 
linkTargetq ~ L linkTypeq ~ L patternq ~ xq ~ #  wî          2        pq ~ q ~Upt 
textField-224ppppq ~ ×sq ~ 9   3uq ~ <   sq ~ >t apresentarLinha1sq ~ >t .equals( true ) || sq ~ >t apresentarLinha2sq ~ >t .equals( true )q ~ Gpppp~q ~ Ht RELATIVE_TO_TALLEST_OBJECT  wîppppppq ~ Íppq ~ Lq ~ Lq ~ Lppppppsq ~ Mpsq ~ Q  wîppppq ~hq ~hq ~Zpsq ~ X  wîppppq ~hq ~hpsq ~ R  wîppppq ~hq ~hpsq ~ [  wîppppq ~hq ~hpsq ~ ]  wîppppq ~hq ~hppppppppppppppppp  wî       pp~r 3net.sf.jasperreports.engine.type.EvaluationTimeEnum          xq ~ t NOWsq ~ 9   4uq ~ <   sq ~ >t 	matriculat java.lang.Stringppppppq ~ Lpppsq ~W  wî          È   2    pq ~ q ~Upt 
textField-224ppppq ~ ×sq ~ 9   5uq ~ <   sq ~ >t apresentarLinha1sq ~ >t .equals( true ) || sq ~ >t apresentarLinha2sq ~ >t .equals( true )q ~ Gppppq ~f  wîppppppq ~ Íppq ~ Lq ~ Lq ~ Lppppppsq ~ Mpsq ~ Q  wîppppq ~q ~q ~vpsq ~ X  wîppppq ~q ~psq ~ R  wîppppq ~q ~psq ~ [  wîppppq ~q ~psq ~ ]  wîppppq ~q ~ppppppppppppppppp  wî       ppq ~osq ~ 9   6uq ~ <   sq ~ >t nomet java.lang.Stringppppppq ~ Lpppsq ~W  wî          d  ^    pq ~ q ~Upt 
textField-224ppppq ~ ×sq ~ 9   7uq ~ <   sq ~ >t apresentarLinha1sq ~ >t .equals( true )q ~ Gppppq ~f  wîppppppq ~ Íppq ~ Lq ~ Lq ~ Lppppppsq ~ Mpsq ~ Q  wîppppq ~q ~q ~psq ~ X  wîppppq ~q ~psq ~ R  wîppppq ~q ~psq ~ [  wîppppq ~q ~psq ~ ]  wîppppq ~q ~ppppppppppppppppp  wî       ppq ~osq ~ 9   8uq ~ <   sq ~ >t telefonet java.lang.Stringppppppq ~ Lpppsq ~W  wî          F  X    pq ~ q ~Upt 
textField-224ppppq ~ ×sq ~ 9   9uq ~ <   sq ~ >t apresentarLinha1sq ~ >t .equals( true )q ~ Gppppq ~f  wîppppppq ~ Íppq ~ Lq ~ Lq ~ Lppppppsq ~ Mpsq ~ Q  wîppppq ~¨q ~¨q ~ psq ~ X  wîppppq ~¨q ~¨psq ~ R  wîppppq ~¨q ~¨psq ~ [  wîppppq ~¨q ~¨psq ~ ]  wîppppq ~¨q ~¨ppppppppppppppppp  wî       ppq ~osq ~ 9   :uq ~ <   sq ~ >t dataNascimentot java.util.Dateppppppq ~ Lppt 
dd/MM/yyyysq ~W  wî          <      pq ~ q ~Upt 
textField-224ppppq ~ ×sq ~ 9   ;uq ~ <   sq ~ >t apresentarLinha1sq ~ >t .equals( true )q ~ Gppppq ~f  wîppppppq ~ Íppq ~ Lq ~ Lq ~ Lppppppsq ~ Mpsq ~ Q  wîppppq ~¼q ~¼q ~´psq ~ X  wîppppq ~¼q ~¼psq ~ R  wîppppq ~¼q ~¼psq ~ [  wîppppq ~¼q ~¼psq ~ ]  wîppppq ~¼q ~¼ppppppppppppppppp  wî       ppq ~osq ~ 9   <uq ~ <   sq ~ >t situacaoClientet java.lang.Stringppppppq ~ Lpppsq ~W  wî          2  Ú    pq ~ q ~Upt 
textField-224ppppq ~ ×sq ~ 9   =uq ~ <   sq ~ >t apresentarLinha1sq ~ >t .equals( true )q ~ Gppppq ~f  wîppppppq ~ Íppq ~ Lq ~ Lq ~ Lppppppsq ~ Mpsq ~ Q  wîppppq ~Ïq ~Ïq ~Çpsq ~ X  wîppppq ~Ïq ~Ïpsq ~ R  wîppppq ~Ïq ~Ïpsq ~ [  wîppppq ~Ïq ~Ïpsq ~ ]  wîppppq ~Ïq ~Ïppppppppppppppppp  wî       ppq ~osq ~ 9   >uq ~ <   sq ~ >t sexot java.lang.Stringppppppq ~ Lpppsq ~W  wî          2  Ö    pq ~ q ~Upt 
textField-224ppppq ~ ×sq ~ 9   ?uq ~ <   sq ~ >t apresentarLinha1sq ~ >t .equals( true ) || sq ~ >t apresentarLinha2sq ~ >t .equals( true ) || sq ~ >t apresentarLinha3sq ~ >t .equals( true )q ~ Gppppq ~f  wîppppppq ~ ppq ~¢q ~¢q ~ Lppppppsq ~ Mpsq ~ Q  wîppppq ~êq ~êq ~Úpsq ~ X  wîppppq ~êq ~êpsq ~ R  wîppppq ~êq ~êpsq ~ [  wîppppq ~êq ~êpsq ~ ]  wîppppq ~êq ~êppppppppppppppppp  wî       ppq ~osq ~ 9   @uq ~ <   sq ~ >t dataCadastrot java.util.Dateppppppq ~ Lppt 
dd/MM/yyyysq ~W  wî          ´       pq ~ q ~Uppppppq ~ ×sq ~ 9   Auq ~ <   sq ~ >t apresentarLinha2sq ~ >t .equals( true )q ~ Gppppq ~f  wîppppppq ~ ppppq ~ Lppppppsq ~ Mpsq ~ Q  wîppppq ~ýq ~ýq ~öpsq ~ X  wîppppq ~ýq ~ýpsq ~ R  wîppppq ~ýq ~ýpsq ~ [  wîppppq ~ýq ~ýpsq ~ ]  wîppppq ~ýq ~ýppppppppppppppppp  wî        ppq ~osq ~ 9   Buq ~ <   sq ~ >t 
logradourot java.lang.Stringppppppq ~ Lpppsq ~W  wî          2   ´   pq ~ q ~Uppppppq ~ ×sq ~ 9   Cuq ~ <   sq ~ >t apresentarLinha2sq ~ >t .equals( true )q ~ Gppppq ~f  wîppppppq ~ ppppq ~ Lppppppsq ~ Mpsq ~ Q  wîppppq ~q ~q ~psq ~ X  wîppppq ~q ~psq ~ R  wîppppq ~q ~psq ~ [  wîppppq ~q ~psq ~ ]  wîppppq ~q ~ppppppppppppppppp  wî        ppq ~osq ~ 9   Duq ~ <   sq ~ >t numerot java.lang.Stringppppppq ~ Lpppsq ~W  wî             æ   pq ~ q ~Uppppppq ~ ×sq ~ 9   Euq ~ <   sq ~ >t apresentarLinha2sq ~ >t .equals( true )q ~ Gppppq ~f  wîppppppq ~ ppppq ~ Lppppppsq ~ Mpsq ~ Q  wîppppq ~!q ~!q ~psq ~ X  wîppppq ~!q ~!psq ~ R  wîppppq ~!q ~!psq ~ [  wîppppq ~!q ~!psq ~ ]  wîppppq ~!q ~!ppppppppppppppppp  wî        ppq ~osq ~ 9   Fuq ~ <   sq ~ >t bairrot java.lang.Stringppppppq ~ Lpppsq ~W  wî          x  r   pq ~ q ~Uppppppq ~ ×sq ~ 9   Guq ~ <   sq ~ >t apresentarLinha2sq ~ >t .equals( true )q ~ Gppppq ~f  wîppppppq ~ ppppq ~ Lppppppsq ~ Mpsq ~ Q  wîppppq ~3q ~3q ~,psq ~ X  wîppppq ~3q ~3psq ~ R  wîppppq ~3q ~3psq ~ [  wîppppq ~3q ~3psq ~ ]  wîppppq ~3q ~3ppppppppppppppppp  wî        ppq ~osq ~ 9   Huq ~ <   sq ~ >t cidadet java.lang.Stringppppppq ~ Lpppsq ~W  wî          8  ê   pq ~ q ~Uppppppq ~ ×sq ~ 9   Iuq ~ <   sq ~ >t apresentarLinha2sq ~ >t .equals( true )q ~ Gppppq ~f  wîppppppq ~ ppppq ~ Lppppppsq ~ Mpsq ~ Q  wîppppq ~Eq ~Eq ~>psq ~ X  wîppppq ~Eq ~Epsq ~ R  wîppppq ~Eq ~Epsq ~ [  wîppppq ~Eq ~Epsq ~ ]  wîppppq ~Eq ~Eppppppppppppppppp  wî        ppq ~osq ~ 9   Juq ~ <   sq ~ >t cept java.lang.Stringppppppq ~ Lpppsq ~W  wî          [  "   pq ~ q ~Uppppppq ~ ×sq ~ 9   Kuq ~ <   sq ~ >t apresentarLinha2sq ~ >t .equals( true )q ~ Gppppq ~f  wîppppppq ~ ppppq ~ Lppppppsq ~ Mpsq ~ Q  wîppppq ~Wq ~Wq ~Ppsq ~ X  wîppppq ~Wq ~Wpsq ~ R  wîppppq ~Wq ~Wpsq ~ [  wîppppq ~Wq ~Wpsq ~ ]  wîppppq ~Wq ~Wppppppppppppppppp  wî        ppq ~osq ~ 9   Luq ~ <   sq ~ >t complementot java.lang.Stringppppppq ~ Lpppsq ~W  wî          9        pq ~ q ~Uppppppq ~ ×sq ~ 9   Muq ~ <   sq ~ >t apresentarLinha3sq ~ >t .equals( true )q ~ Gppppq ~f  wîppppppq ~ ppppq ~ Lppppppsq ~ Mpsq ~ Q  wîppppq ~iq ~iq ~bpsq ~ X  wîppppq ~iq ~ipsq ~ R  wîppppq ~iq ~ipsq ~ [  wîppppq ~iq ~ipsq ~ ]  wîppppq ~iq ~ippppppppppppppppp  wî        ppq ~osq ~ 9   Nuq ~ <   sq ~ >t 
dataMatriculat java.util.Dateppppppq ~ Lppt 
dd/MM/yyyysq ~W  wî          7   :    pq ~ q ~Uppppppq ~ ×sq ~ 9   Ouq ~ <   sq ~ >t apresentarLinha3sq ~ >t .equals( true )q ~ Gppppq ~f  wîppppppq ~ ppppq ~ Lppppppsq ~ Mpsq ~ Q  wîppppq ~|q ~|q ~upsq ~ X  wîppppq ~|q ~|psq ~ R  wîppppq ~|q ~|psq ~ [  wîppppq ~|q ~|psq ~ ]  wîppppq ~|q ~|ppppppppppppppppp  wî        ppq ~osq ~ 9   Puq ~ <   sq ~ >t inicioPlanot java.util.Dateppppppq ~ Lppt 
dd/MM/yyyysq ~W  wî          6   t    pq ~ q ~Uppppppq ~ ×sq ~ 9   Quq ~ <   sq ~ >t apresentarLinha3sq ~ >t .equals( true )q ~ Gppppq ~f  wîppppppq ~ ppppq ~ Lppppppsq ~ Mpsq ~ Q  wîppppq ~q ~q ~psq ~ X  wîppppq ~q ~psq ~ R  wîppppq ~q ~psq ~ [  wîppppq ~q ~psq ~ ]  wîppppq ~q ~ppppppppppppppppp  wî        ppq ~osq ~ 9   Ruq ~ <   sq ~ >t vencimentoPlanot java.util.Dateppppppq ~ Lppt 
dd/MM/yyyysq ~W  wî          2  ,    pq ~ q ~Uppppppq ~ ×sq ~ 9   Suq ~ <   sq ~ >t apresentarLinha3sq ~ >t .equals( true )q ~ Gppppq ~f  wîppppppq ~ ppppq ~ Lppppppsq ~ Mpsq ~ Q  wîppppq ~¢q ~¢q ~psq ~ X  wîppppq ~¢q ~¢psq ~ R  wîppppq ~¢q ~¢psq ~ [  wîppppq ~¢q ~¢psq ~ ]  wîppppq ~¢q ~¢ppppppppppppppppp  wî       ppq ~osq ~ 9   Tuq ~ <   sq ~ >t dataUltimoAcessot java.util.Dateppppppq ~ Lppt dd/MM/yyyy HH.mm.sssq ~W  wî         x  ^    pq ~ q ~Uppppppq ~ ×sq ~ 9   Uuq ~ <   sq ~ >t apresentarLinha3sq ~ >t .equals( true )q ~ Gpppp~q ~ Ht RELATIVE_TO_BAND_HEIGHT  wîppppppq ~ ppppq ~ Lppppppsq ~ Mpsq ~ Q  wîppppq ~·q ~·q ~®psq ~ X  wîppppq ~·q ~·psq ~ R  wîppppq ~·q ~·psq ~ [  wîppppq ~·q ~·psq ~ ]  wîppppq ~·q ~·ppppppppppppppppp  wî       ppq ~osq ~ 9   Vuq ~ <   sq ~ >t planot java.lang.Stringppppppq ~ Lpppsq ~W  wî               pq ~ q ~Uppppppq ~ ×sq ~ 9   Wuq ~ <   sq ~ >t apresentarLinha3sq ~ >t .equals( true )q ~ Gppppq ~µ  wîppppppq ~ ppppq ~ Lppppppsq ~ Mpsq ~ Q  wîppppq ~Éq ~Éq ~Âpsq ~ X  wîppppq ~Éq ~Épsq ~ R  wîppppq ~Éq ~Épsq ~ [  wîppppq ~Éq ~Épsq ~ ]  wîppppq ~Éq ~Éppppppppppppppppp  wî       ppq ~osq ~ 9   Xuq ~ <   sq ~ >t 
nomeConsultort java.lang.Stringppppppq ~ Lpppsq ~W  wî            Â    pq ~ q ~Upt 
textField-224ppppq ~ ×sq ~ 9   Yuq ~ <   sq ~ >t apresentarLinha1sq ~ >t .equals( true )q ~ Gppppq ~f  wîppppppq ~ Íppq ~ Lq ~ Lq ~ Lppppppsq ~ Mpsq ~ Q  wîppppq ~Üq ~Üq ~Ôpsq ~ X  wîppppq ~Üq ~Üpsq ~ R  wîppppq ~Üq ~Üpsq ~ [  wîppppq ~Üq ~Üpsq ~ ]  wîppppq ~Üq ~Üppppppppppppppppp  wî       ppq ~osq ~ 9   Zuq ~ <   sq ~ >t emailt java.lang.Stringppppppq ~ Lpppsq ~W  wî          d   ó    pq ~ q ~Upt 
textField-224ppppq ~ ×sq ~ 9   [uq ~ <   sq ~ >t apresentarLinha1sq ~ >t .equals( true )q ~ Gppppq ~f  wîpppppppppq ~ Lq ~ Lq ~ Lppppppsq ~ Mpsq ~ Q  wîppppq ~ïq ~ïq ~çpsq ~ X  wîppppq ~ïq ~ïpsq ~ R  wîppppq ~ïq ~ïpsq ~ [  wîppppq ~ïq ~ïpsq ~ ]  wîppppq ~ïq ~ïppppppppppppppppp  wî       ppq ~osq ~ 9   \uq ~ <   sq ~ >t 	documentot java.lang.Stringppppppq ~ Lpppsq ~W  wî          d  :    pq ~ q ~Upt 
textField-224ppppq ~ ×sq ~ 9   ]uq ~ <   sq ~ >t apresentarLinha1sq ~ >t .equals( true )q ~ Gppppq ~f  wîpppppppppq ~ Lq ~ Lq ~ Lppppppsq ~ Mpsq ~ Q  wîppppq ~q ~q ~úpsq ~ X  wîppppq ~q ~psq ~ R  wîppppq ~q ~psq ~ [  wîppppq ~q ~psq ~ ]  wîppppq ~q ~ppppppppppppppppp  wî       ppq ~osq ~ 9   ^uq ~ <   sq ~ >t cnpjt java.lang.Stringppppppq ~ Lpppsq ~W  wî            ª    pq ~ q ~Uppppppq ~ ×sq ~ 9   _uq ~ <   sq ~ >t apresentarLinha3sq ~ >t .equals( true )q ~ Gppppq ~µ  wîppppppq ~ ppppq ~ Lppppppsq ~ Mpsq ~ Q  wîppppq ~q ~q ~
psq ~ X  wîppppq ~q ~psq ~ R  wîppppq ~q ~psq ~ [  wîppppq ~q ~psq ~ ]  wîppppq ~q ~ppppppppppppppppp  wî       ppq ~osq ~ 9   `uq ~ <   sq ~ >t nomeRespContratot java.lang.Stringppppppq ~ Lpppsq ~W  wî               pq ~ q ~Uppppppq ~ ×sq ~ 9   auq ~ <   sq ~ >t apresentarLinha3sq ~ >t .equals( true )q ~ Gppppq ~µ  wîppppppq ~ ppppq ~ Lppppppsq ~ Mpsq ~ Q  wîppppq ~&q ~&q ~psq ~ X  wîppppq ~&q ~&psq ~ R  wîppppq ~&q ~&psq ~ [  wîppppq ~&q ~&psq ~ ]  wîppppq ~&q ~&ppppppppppppppppp  wî       ppq ~osq ~ 9   buq ~ <   sq ~ >t nomeProfessorest java.lang.Stringppppppq ~ Lpppsq ~W  wî          6     pq ~ q ~Uppppppq ~ ×sq ~ 9   cuq ~ <   sq ~ >t apresentarLinha2sq ~ >t .equals( true )q ~ Gppppq ~f  wîppppppq ~ ppppq ~ Lppppppsq ~ Mpsq ~ Q  wîppppq ~8q ~8q ~1psq ~ X  wîppppq ~8q ~8psq ~ R  wîppppq ~8q ~8psq ~ [  wîppppq ~8q ~8psq ~ ]  wîppppq ~8q ~8ppppppppppppppppp  wî        ppq ~osq ~ 9   duq ~ <   sq ~ >t estadoCivilt java.lang.Stringppppppq ~ Lpppsq ~W  wî          W  µ   pq ~ q ~Uppppppq ~ ×sq ~ 9   euq ~ <   sq ~ >t apresentarLinha2sq ~ >t .equals( true )q ~ Gppppq ~f  wîppppppq ~ ppppq ~ Lppppppsq ~ Mpsq ~ Q  wîppppq ~Jq ~Jq ~Cpsq ~ X  wîppppq ~Jq ~Jpsq ~ R  wîppppq ~Jq ~Jpsq ~ [  wîppppq ~Jq ~Jpsq ~ ]  wîppppq ~Jq ~Jppppppppppppppppp  wî        ppq ~osq ~ 9   fuq ~ <   sq ~ >t 	profissaot java.lang.Stringppppppq ~ Lpppsq ~W  wî          6   y   1pq ~ q ~Uppppppq ~ ×sq ~ 9   guq ~ <   sq ~ >t apresentarLinha3sq ~ >t .equals( true )q ~ Gppppq ~f  wîppppppq ~ ppppq ~ Lppppppsq ~ Mpsq ~ Q  wîppppq ~\q ~\q ~Upsq ~ X  wîppppq ~\q ~\psq ~ R  wîppppq ~\q ~\psq ~ [  wîppppq ~\q ~\psq ~ ]  wîppppq ~\q ~\ppppppppppppppppp  wî        ppq ~osq ~ 9   huq ~ <   sq ~ >t "R$ " + sq ~ >t 	valorPagot java.lang.Stringppppppq ~ Lppt 
dd/MM/yyyysq ~W  wî          I     1pq ~ q ~Uppppppq ~ ×ppppq ~f  wîppppppq ~ ppppq ~ Lppppppsq ~ Mpsq ~ Q  wîppppq ~kq ~kq ~jpsq ~ X  wîppppq ~kq ~kpsq ~ R  wîppppq ~kq ~kpsq ~ [  wîppppq ~kq ~kpsq ~ ]  wîppppq ~kq ~kppppppppppppppppp  wî       ppq ~osq ~ 9   iuq ~ <   sq ~ >t "R$ " + sq ~ >t valorDevolvidot java.lang.Stringppppppq ~ Lppq ~ 5sq ~W  wî   #     ®  ^   :pq ~ q ~Uppppppq ~ ×ppppq ~µ  wîppppppsq ~    ppppq ~ Lppppppsq ~ Mpsq ~ Q  wîppppq ~zq ~zq ~xpsq ~ X  wîppppq ~zq ~zpsq ~ R  wîppppq ~zq ~zpsq ~ [  wîppppq ~zq ~zpsq ~ ]  wîppppq ~zq ~zppppppppppppppppp  wî       ppq ~osq ~ 9   juq ~ <   sq ~ >t descricaoCancelamentot java.lang.Stringppppppq ~ Lpppsq ~W  wî          7   9   1pq ~ q ~Uppppppq ~ ×ppppq ~f  wîppppppq ~ ppppq ~ Lppppppsq ~ Mpsq ~ Q  wîppppq ~q ~q ~psq ~ X  wîppppq ~q ~psq ~ R  wîppppq ~q ~psq ~ [  wîppppq ~q ~psq ~ ]  wîppppq ~q ~ppppppppppppppppp  wî        ppq ~osq ~ 9   kuq ~ <   sq ~ >t "R$ " + sq ~ >t 
valorContratot java.lang.Stringppppppq ~ Lppq ~ 5sq ~W  wî          9      1pq ~ q ~Uppppppq ~ ×ppppq ~f  wîppppppq ~ ppppq ~ Lppppppsq ~ Mpsq ~ Q  wîppppq ~q ~q ~psq ~ X  wîppppq ~q ~psq ~ R  wîppppq ~q ~psq ~ [  wîppppq ~q ~psq ~ ]  wîppppq ~q ~ppppppppppppppppp  wî        ppq ~osq ~ 9   luq ~ <   sq ~ >t cpft java.lang.Stringppppppq ~ Lppq ~ 5sq ~W  wî         B   ¹   1pq ~ q ~Uppppppq ~ ×ppppq ~µ  wîppppppq ~ ppppq ~ Lppppppsq ~ Mpsq ~ Q  wîppppq ~ q ~ q ~psq ~ X  wîppppq ~ q ~ psq ~ R  wîppppq ~ q ~ psq ~ [  wîppppq ~ q ~ psq ~ ]  wîppppq ~ q ~ ppppppppppppppppp  wî       ppq ~osq ~ 9   muq ~ <   sq ~ >t "R$ " + sq ~ >t valorUtilizadot java.lang.Stringppppppq ~ Lpppxp  wî   ^pp~q ~ t PREVENTsq ~ sq ~    w   sq ~W  wî            ð   pq ~ q ~¯ppppppq ~ ×sq ~ 9   ouq ~ <   sq ~ >t 
observacaosq ~ >t 
.length() > 0q ~ Gppppq ~ I  wîppppppq ~ ppq ~¢ppppppppsq ~ Mpsq ~ Q  wîppppq ~¸q ~¸q ~±psq ~ X  wîppppq ~¸q ~¸psq ~ R  wîppppq ~¸q ~¸psq ~ [  wîppppq ~¸q ~¸psq ~ ]  wîppppq ~¸q ~¸pppppppppppppppp~r 2net.sf.jasperreports.engine.type.VerticalAlignEnum          xq ~ t MIDDLE  wî        ppq ~osq ~ 9   puq ~ <   sq ~ >t "ObservaÃ§Ã£o: " + sq ~ >t 
observacaot java.lang.Stringppppppppppsq ~W  wî          ð       pq ~ q ~¯ppppppq ~ ×sq ~ 9   quq ~ <   sq ~ >t 
justificativasq ~ >t 
.length() > 0q ~ Gppppq ~ I  wîppppppq ~ ppq ~¢ppppppppsq ~ Mpsq ~ Q  wîppppq ~Ïq ~Ïq ~Èpsq ~ X  wîppppq ~Ïq ~Ïpsq ~ R  wîppppq ~Ïq ~Ïpsq ~ [  wîppppq ~Ïq ~Ïpsq ~ ]  wîppppq ~Ïq ~Ïppppppppppppppppq ~¿  wî        ppq ~osq ~ 9   ruq ~ <   sq ~ >t "Justificativa: " + sq ~ >t 
justificativat java.lang.Stringppppppppppsq ~W  wî          R      pq ~ q ~¯ppppppq ~ ×sq ~ 9   suq ~ <   sq ~ >t !sq ~ >t dataInicioOperacaosq ~ >t 
.equals(null)q ~ Gppppq ~f  wîppppppq ~ ppppq ~ Lppppppsq ~ Mpsq ~ Q  wîppppq ~åq ~åq ~Üpsq ~ X  wîppppq ~åq ~åpsq ~ R  wîppppq ~åq ~åpsq ~ [  wîppppq ~åq ~åpsq ~ ]  wîppppq ~åq ~åppppppppppppppppp  wî        ppq ~osq ~ 9   tuq ~ <   sq ~ >t dataInicioOperacaot java.util.Dateppppppq ~ Lppt 
dd/MM/yyyysq ~W  wî            °    pq ~ q ~¯ppppppq ~ ×sq ~ 9   uuq ~ <   sq ~ >t !sq ~ >t dataFimOperacaosq ~ >t 
.equals(null)q ~ Gppppq ~f  wîppppppq ~ ppppq ~ Lppppppsq ~ Mpsq ~ Q  wîppppq ~úq ~úq ~ñpsq ~ X  wîppppq ~úq ~úpsq ~ R  wîppppq ~úq ~úpsq ~ [  wîppppq ~úq ~úpsq ~ ]  wîppppq ~úq ~úppppppppppppppppp  wî        ppq ~osq ~ 9   vuq ~ <   sq ~ >t dataFimOperacaot java.util.Dateppppppq ~ Lppt 
dd/MM/yyyysq ~ "  wî           L   È    pq ~ q ~¯ppppppq ~ ×sq ~ 9   wuq ~ <   sq ~ >t !sq ~ >t dataInicioOperacaosq ~ >t 
.equals(null)q ~ Gppppq ~ I  wîppppppq ~ ppq ~ Lppppppppsq ~ Mpsq ~ Q  wîppppq ~q ~q ~psq ~ X  wîppppq ~q ~psq ~ R  wîppppq ~q ~psq ~ [  wîppppq ~q ~psq ~ ]  wîppppq ~q ~pppppt Helvetica-Boldpppppppppppt Inicio da OperaÃ§Ã£o:sq ~ "  wî           F  j    pq ~ q ~¯ppppppq ~ ×sq ~ 9   xuq ~ <   sq ~ >t !sq ~ >t dataFimOperacaosq ~ >t 
.equals(null)q ~ Gppppq ~ I  wîppppppq ~ ppq ~ Lppppppppsq ~ Mpsq ~ Q  wîppppq ~ q ~ q ~psq ~ X  wîppppq ~ q ~ psq ~ R  wîppppq ~ q ~ psq ~ [  wîppppq ~ q ~ psq ~ ]  wîppppq ~ q ~ pppppt Helvetica-Boldpppppppppppt Fim da OperaÃ§Ã£o:sq ~ "  wî           o       pq ~ q ~¯ppppppq ~ ×sq ~ 9   yuq ~ <   sq ~ >t !sq ~ >t dataOperacaosq ~ >t 
.equals(null)q ~ Gppppq ~ I  wîppppppq ~ ppq ~ Lppppppppsq ~ Mpsq ~ Q  wîppppq ~1q ~1q ~(psq ~ X  wîppppq ~1q ~1psq ~ R  wîppppq ~1q ~1psq ~ [  wîppppq ~1q ~1psq ~ ]  wîppppq ~1q ~1pppppt Helvetica-Boldpppppppppppt LanÃ§amento da  OperaÃ§Ã£o:sq ~W  wî          R   q    pq ~ q ~¯ppppppq ~ ×sq ~ 9   zuq ~ <   sq ~ >t !sq ~ >t dataOperacaosq ~ >t 
.equals(null)q ~ Gppppq ~f  wîppppppq ~ ppppq ~ Lppppppsq ~ Mpsq ~ Q  wîppppq ~Bq ~Bq ~9psq ~ X  wîppppq ~Bq ~Bpsq ~ R  wîppppq ~Bq ~Bpsq ~ [  wîppppq ~Bq ~Bpsq ~ ]  wîppppq ~Bq ~Bppppppppppppppppp  wî        ppq ~osq ~ 9   {uq ~ <   sq ~ >t dataOperacaot java.util.Dateppppppq ~ Lppt 
dd/MM/yyyyxp  wî   %sq ~ 9   nuq ~ <   sq ~ >t (!sq ~ >t 
justificativasq ~ >t .trim().equals("") || !sq ~ >t 
observacaosq ~ >t .trim().equals("")) || !sq ~ >t dataOperacaosq ~ >t 
.equals(null)q ~ Gpppsq ~ sq ~    w   sq ~ t  wî                  pq ~ q ~^sr java.awt.Color¥3u F falphaI valueL cst Ljava/awt/color/ColorSpace;[ 	frgbvaluet [F[ fvalueq ~cxp    ÿÌÌÌpppppppp~q ~ 6t FIX_RELATIVE_TO_BOTTOMppppq ~ I  wîppsq ~ S  wîppppq ~`p  wî q ~ }xp  wî   ppppppt javapsr .net.sf.jasperreports.engine.base.JRBaseDataset      'Ø I PSEUDO_SERIAL_VERSION_UIDZ isMainB whenResourceMissingType[ fieldst &[Lnet/sf/jasperreports/engine/JRField;L filterExpressionq ~ [ groupst &[Lnet/sf/jasperreports/engine/JRGroup;L nameq ~ [ 
parameterst *[Lnet/sf/jasperreports/engine/JRParameter;L 
propertiesMapq ~ 1L queryt %Lnet/sf/jasperreports/engine/JRQuery;L resourceBundleq ~ L scriptletClassq ~ [ 
scriptletst *[Lnet/sf/jasperreports/engine/JRScriptlet;[ 
sortFieldst *[Lnet/sf/jasperreports/engine/JRSortField;[ 	variablest )[Lnet/sf/jasperreports/engine/JRVariable;L whenResourceMissingTypeValuet >Lnet/sf/jasperreports/engine/type/WhenResourceMissingTypeEnum;xp  wî ur &[Lnet.sf.jasperreports.engine.JRField;<ßÇN*òp  xp   'sr ,net.sf.jasperreports.engine.base.JRBaseField      'Ø L descriptionq ~ L nameq ~ L 
propertiesMapq ~ 1L valueClassNameq ~ L valueClassRealNameq ~ xpt  t 	matriculasr +net.sf.jasperreports.engine.JRPropertiesMap      'Ø L baseq ~ 1L propertiesListq ~ L 
propertiesMapt Ljava/util/Map;xppppt java.lang.Stringpsq ~upt nomesq ~ypppt java.lang.Stringpsq ~ut  t situacaoClientesq ~ypppt java.lang.Stringpsq ~ut  t telefonesq ~ypppt java.lang.Stringpsq ~ut  t planosq ~ypppt java.lang.Stringpsq ~ut  t 
dataMatriculasq ~ypppt java.util.Datepsq ~upt inicioPlanosq ~ypppt java.util.Datepsq ~upt bairrosq ~ypppt java.lang.Stringpsq ~upt cidadesq ~ypppt java.lang.Stringpsq ~upt cepsq ~ypppt java.lang.Stringpsq ~upt complementosq ~ypppt java.lang.Stringpsq ~upt 
logradourosq ~ypppt java.lang.Stringpsq ~upt numerosq ~ypppt java.lang.Stringpsq ~upt professoressq ~ypppt java.lang.Stringpsq ~upt emailsq ~ypppt java.lang.Stringpsq ~upt dataCadastrosq ~ypppt java.util.Datepsq ~upt dataNascimentosq ~ypppt java.util.Datepsq ~upt 	consultorsq ~ypppt java.lang.Stringpsq ~upt dataUltimoAcessosq ~ypppt java.util.Datepsq ~upt vencimentoPlanosq ~ypppt java.util.Datepsq ~upt sexosq ~ypppt java.lang.Stringpsq ~upt cpfsq ~ypppt java.lang.Stringpsq ~upt 
justificativasq ~ypppt java.lang.Stringpsq ~upt 
observacaosq ~ypppt java.lang.Stringpsq ~upt nomeRespContratosq ~ypppt java.lang.Stringpsq ~upt 
nomeConsultorsq ~ypppt java.lang.Stringpsq ~upt nomeProfessoressq ~ypppt java.lang.Stringpsq ~upt dataFimOperacaosq ~ypppt java.util.Datepsq ~upt dataInicioOperacaosq ~ypppt java.util.Datepsq ~upt estadoCivilsq ~ypppt java.lang.Stringpsq ~upt 	profissaosq ~ypppt java.lang.Stringpsq ~upt dataOperacaosq ~ypppt java.util.Datepsq ~upt 	documentosq ~ypppt java.lang.Stringpsq ~upt cnpjsq ~ypppt java.lang.Stringpsq ~upt 
valorContratosq ~ypppt java.lang.Stringpsq ~upt 	valorPagosq ~ypppt java.lang.Stringpsq ~upt valorUtilizadosq ~ypppt java.lang.Stringpsq ~upt valorDevolvidosq ~ypppt java.lang.Stringpsq ~upt descricaoCancelamentosq ~ypppt java.lang.Stringpppt RelatorioClienteur *[Lnet.sf.jasperreports.engine.JRParameter;" *Ã`!  xp    sr 0net.sf.jasperreports.engine.base.JRBaseParameter      'Ø 	Z isForPromptingZ isSystemDefinedL defaultValueExpressionq ~ L descriptionq ~ L nameq ~ L nestedTypeNameq ~ L 
propertiesMapq ~ 1L valueClassNameq ~ L valueClassRealNameq ~ xpppt REPORT_PARAMETERS_MAPpsq ~ypppt 
java.util.Mappsq ~ppt 
JASPER_REPORTpsq ~ypppt (net.sf.jasperreports.engine.JasperReportpsq ~ppt REPORT_CONNECTIONpsq ~ypppt java.sql.Connectionpsq ~ppt REPORT_MAX_COUNTpsq ~ypppt java.lang.Integerpsq ~ppt REPORT_DATA_SOURCEpsq ~ypppt (net.sf.jasperreports.engine.JRDataSourcepsq ~ppt REPORT_SCRIPTLETpsq ~ypppt /net.sf.jasperreports.engine.JRAbstractScriptletpsq ~ppt 
REPORT_LOCALEpsq ~ypppt java.util.Localepsq ~ppt REPORT_RESOURCE_BUNDLEpsq ~ypppt java.util.ResourceBundlepsq ~ppt REPORT_TIME_ZONEpsq ~ypppt java.util.TimeZonepsq ~ppt REPORT_FORMAT_FACTORYpsq ~ypppt .net.sf.jasperreports.engine.util.FormatFactorypsq ~ppt REPORT_CLASS_LOADERpsq ~ypppt java.lang.ClassLoaderpsq ~ppt REPORT_URL_HANDLER_FACTORYpsq ~ypppt  java.net.URLStreamHandlerFactorypsq ~ppt REPORT_FILE_RESOLVERpsq ~ypppt -net.sf.jasperreports.engine.util.FileResolverpsq ~ppt REPORT_TEMPLATESpsq ~ypppt java.util.Collectionpsq ~ppt REPORT_VIRTUALIZERpsq ~ypppt )net.sf.jasperreports.engine.JRVirtualizerpsq ~ppt IS_IGNORE_PAGINATIONpsq ~ypppq ~ Gpsq ~  ppt logoPadraoRelatoriopsq ~ypppt java.io.InputStreampsq ~  ppt tituloRelatoriopsq ~ypppt java.lang.Stringpsq ~  ppt versaoSoftwarepsq ~ypppt java.lang.Stringpsq ~  ppt usuariopsq ~ypppt java.lang.Stringpsq ~  ppt dadosImpressaopsq ~ypppt java.lang.Stringpsq ~ ppt nomeEmpresapsq ~ypppt java.lang.Stringpsq ~ ppt enderecoEmpresapsq ~ypppt java.lang.Stringpsq ~ ppt 
cidadeEmpresapsq ~ypppt java.lang.Stringpsq ~  ppt dataInipsq ~ypppt java.lang.Stringpsq ~  ppt dataFimpsq ~ypppt java.lang.Stringpsq ~  ppt 
totalClientespsq ~ypppt java.lang.Integerpsq ~  ppt listaTotaispsq ~ypppt java.lang.Objectpsq ~ sq ~ 9    uq ~ <   sq ~ >t truet java.lang.Booleanppt apresentarLinha1psq ~ypppq ~psq ~ sq ~ 9   uq ~ <   sq ~ >t truet java.lang.Booleanppt apresentarLinha2psq ~ypppq ~psq ~ sq ~ 9   uq ~ <   sq ~ >t truet java.lang.Booleanppt apresentarLinha3psq ~ypppq ~¡psq ~ ppt filtropsq ~ypppt java.lang.Stringpsq ~ypsq ~    w   t ireport.zoomt 	ireport.xt 	ireport.yxsr java.util.HashMapÚÁÃ`Ñ F 
loadFactorI 	thresholdxp?@     w      q ~ªt 1.8150000000000006q ~«t 0q ~¬t 0xsr ,net.sf.jasperreports.engine.base.JRBaseQuery      'Ø [ chunkst +[Lnet/sf/jasperreports/engine/JRQueryChunk;L languageq ~ xppt sqlppppur )[Lnet.sf.jasperreports.engine.JRVariable;bæ|,·D  xp   sr /net.sf.jasperreports.engine.base.JRBaseVariable      'Ø I PSEUDO_SERIAL_VERSION_UIDB calculationB 
incrementTypeZ isSystemDefinedB 	resetTypeL calculationValuet 2Lnet/sf/jasperreports/engine/type/CalculationEnum;L 
expressionq ~ L incrementGroupq ~ 0L incrementTypeValuet 4Lnet/sf/jasperreports/engine/type/IncrementTypeEnum;L incrementerFactoryClassNameq ~ L incrementerFactoryClassRealNameq ~ L initialValueExpressionq ~ L nameq ~ L 
resetGroupq ~ 0L resetTypeValuet 0Lnet/sf/jasperreports/engine/type/ResetTypeEnum;L valueClassNameq ~ L valueClassRealNameq ~ xp  wî   ~r 0net.sf.jasperreports.engine.type.CalculationEnum          xq ~ t SYSTEMpp~r 2net.sf.jasperreports.engine.type.IncrementTypeEnum          xq ~ t NONEppsq ~ 9   uq ~ <   sq ~ >t new java.lang.Integer(1)q ~,pt PAGE_NUMBERp~r .net.sf.jasperreports.engine.type.ResetTypeEnum          xq ~ t REPORTq ~,psq ~¸  wî   q ~¾ppq ~Áppsq ~ 9   uq ~ <   sq ~ >t new java.lang.Integer(1)q ~,pt 
COLUMN_NUMBERp~q ~Èt PAGEq ~,psq ~¸  wî   ~q ~½t COUNTsq ~ 9   uq ~ <   sq ~ >t new java.lang.Integer(1)q ~,ppq ~Áppsq ~ 9   uq ~ <   sq ~ >t new java.lang.Integer(0)q ~,pt REPORT_COUNTpq ~Éq ~,psq ~¸  wî   q ~Ôsq ~ 9   uq ~ <   sq ~ >t new java.lang.Integer(1)q ~,ppq ~Áppsq ~ 9   uq ~ <   sq ~ >t new java.lang.Integer(0)q ~,pt 
PAGE_COUNTpq ~Ñq ~,psq ~¸  wî   q ~Ôsq ~ 9   	uq ~ <   sq ~ >t new java.lang.Integer(1)q ~,ppq ~Áppsq ~ 9   
uq ~ <   sq ~ >t new java.lang.Integer(0)q ~,pt COLUMN_COUNTp~q ~Èt COLUMNq ~,p~r <net.sf.jasperreports.engine.type.WhenResourceMissingTypeEnum          xq ~ t NULLq ~p~r 0net.sf.jasperreports.engine.type.OrientationEnum          xq ~ t 	LANDSCAPEpp~r /net.sf.jasperreports.engine.type.PrintOrderEnum          xq ~ t VERTICALpsq ~ sq ~    w   sq ~ "  wî           d       pq ~ q ~þpt staticText-2ppppq ~ 7ppppq ~ I  wîpppppppp~r 4net.sf.jasperreports.engine.type.HorizontalAlignEnum          xq ~ t RIGHTq ~ Lppppppppsq ~ Mpsq ~ Q  wîppppq ~q ~q ~ psq ~ X  wîppppq ~q ~psq ~ R  wîppppq ~q ~psq ~ [  wîppppq ~q ~psq ~ ]  wîppppq ~q ~pppppt Helvetica-Boldppppppppppq ~¿t Total de Clientes: sq ~W  wî           d   d   pq ~ q ~þpt 
textField-224ppppq ~ 7ppppq ~ I  wîppppppq ~ p~q ~t CENTERq ~¢ppq ~¢pppppsq ~ Mpsq ~ Q  wîppppq ~q ~q ~
psq ~ X  wîppppq ~q ~psq ~ R  wîppppq ~q ~psq ~ [  wîppppq ~q ~psq ~ ]  wîppppq ~q ~ppt htmlpppppppppppppq ~¿  wî       ppq ~osq ~ 9   |uq ~ <   sq ~ >t 
totalClientest java.lang.Integerppppppq ~ Lpppxp  wî   ppq ~­psq ~ sq ~    w   sr ,net.sf.jasperreports.engine.base.JRBaseImage      'Ø *I PSEUDO_SERIAL_VERSION_UIDI 
bookmarkLevelB evaluationTimeB hyperlinkTargetB 
hyperlinkTypeZ isLazyB onErrorTypeL anchorNameExpressionq ~ L borderq ~ L borderColorq ~ $L bottomBorderq ~ L bottomBorderColorq ~ $L 
bottomPaddingq ~ %L evaluationGroupq ~ 0L evaluationTimeValueq ~XL 
expressionq ~ L horizontalAlignmentq ~ L horizontalAlignmentValueq ~ &L hyperlinkAnchorExpressionq ~ L hyperlinkPageExpressionq ~ [ hyperlinkParametersq ~YL hyperlinkReferenceExpressionq ~ L hyperlinkTooltipExpressionq ~ L isUsingCacheq ~ 'L 
leftBorderq ~ L leftBorderColorq ~ $L leftPaddingq ~ %L lineBoxq ~ (L 
linkTargetq ~ L linkTypeq ~ L onErrorTypeValuet 2Lnet/sf/jasperreports/engine/type/OnErrorTypeEnum;L paddingq ~ %L rightBorderq ~ L rightBorderColorq ~ $L rightPaddingq ~ %L 
scaleImageq ~ L scaleImageValuet 1Lnet/sf/jasperreports/engine/type/ScaleImageEnum;L 	topBorderq ~ L topBorderColorq ~ $L 
topPaddingq ~ %L verticalAlignmentq ~ L verticalAlignmentValueq ~ +xq ~ v  wî   '       S      pq ~ q ~pt image-1ppppq ~ 7ppppq ~ I  wîppsq ~ S  wîppppq ~"p  wî         ppppppp~q ~nt PAGEsq ~ 9   uq ~ <   sq ~ >t logoPadraoRelatoriot java.io.InputStreamppppppppq ~ Lpppsq ~ Mpsq ~ Q  wîsq ~a    ÿfffpppp~r .net.sf.jasperreports.engine.type.LineStyleEnum          xq ~ t SOLIDsr java.lang.FloatÚíÉ¢Û<ðì F valuexq ~ ?   q ~,q ~,q ~"psq ~ X  wîsq ~a    ÿfffppppq ~0sq ~2?   q ~,q ~,psq ~ R  wîppppq ~,q ~,psq ~ [  wîsq ~a    ÿfffppppq ~0sq ~2?   q ~,q ~,psq ~ ]  wîsq ~a    ÿfffppppq ~0sq ~2?   q ~,q ~,pp~r 0net.sf.jasperreports.engine.type.OnErrorTypeEnum          xq ~ t BLANKpppppppppppsq ~W  wî           p      sq ~a    ÿÿÿÿpppq ~ q ~pt 	dataRel-1p~r )net.sf.jasperreports.engine.type.ModeEnum          xq ~ t TRANSPARENTppq ~ ×ppppq ~ I  wîpppppt Verdanaq ~ pq ~pq ~¢pppppppsq ~ Msq ~     sq ~ Q  wîsq ~a    ÿfffppppq ~0sq ~2?   q ~Hq ~Hq ~Apsq ~ X  wîsq ~a    ÿfffppppq ~0sq ~2?   q ~Hq ~Hpsq ~ R  wîppppq ~Hq ~Hpsq ~ [  wîsq ~a    ÿfffppppq ~0sq ~2?   q ~Hq ~Hpsq ~ ]  wîsq ~a    ÿfffppppq ~0sq ~2?   q ~Hq ~Hpppppt 	Helveticappppppppppq ~¿  wî        ppq ~osq ~ 9   uq ~ <   sq ~ >t 
new Date()t java.util.Dateppppppq ~ Lppt dd/MM/yyyy HH.mm.sssq ~W  wî           p      pq ~ q ~pt 
textField-207ppppq ~ ×ppppq ~ I  wîpppppt Verdanaq ~ pq ~q ~¢q ~¢pppppppsq ~ Mpsq ~ Q  wîsq ~a    ÿfffppppq ~0sq ~2?   q ~aq ~aq ~^psq ~ X  wîsq ~a    ÿfffppppq ~0sq ~2?   q ~aq ~apsq ~ R  wîppppq ~aq ~apsq ~ [  wîsq ~a    ÿfffppppq ~0sq ~2?   q ~aq ~apsq ~ ]  wîsq ~a    ÿfffppppq ~0sq ~2?   q ~aq ~apppppt 	Helveticappppppppppq ~¿  wî        ppq ~osq ~ 9   
uq ~ <   sq ~ >t 
"UsuÃ¡rio:"+ sq ~ >t usuariot java.lang.Stringppppppq ~¢ppq ~ 5sq ~W  wî          L   T   pq ~ q ~pt textField-2ppppq ~ ×ppppq ~ I  wîpppppt Arialsq ~    pq ~q ~ Lppppppppsq ~ Mpsq ~ Q  wîsq ~a    ÿfffppppq ~0sq ~2?   q ~{q ~{q ~wpsq ~ X  wîppq ~0sq ~2    q ~{q ~{psq ~ R  wîppq ~0sq ~2?   q ~{q ~{psq ~ [  wîppq ~0sq ~2    q ~{q ~{psq ~ ]  wîsq ~a    ÿfffppppq ~0sq ~2?   q ~{q ~{pppppt Helvetica-Boldppppppppppq ~¿  wî        ppq ~osq ~ 9   uq ~ <   sq ~ >t tituloRelatoriot java.lang.Stringppppppq ~¢pppsq ~W  wî           F      pq ~ q ~ppppppq ~ ×ppppq ~ I  wîpppppt Verdanaq ~ pq ~pppppppppsq ~ Mpsq ~ Q  wîpp~q ~/t DOTTEDsq ~2?   q ~q ~q ~psq ~ X  wîppq ~sq ~2?   q ~q ~psq ~ R  wîppppq ~q ~psq ~ [  wîppppq ~q ~psq ~ ]  wîppppq ~q ~ppppppppppppppppq ~¿  wî        ppq ~osq ~ 9   uq ~ <   sq ~ >t "PÃ¡gina: "+sq ~ >t PAGE_NUMBERsq ~ >t +" de"t java.lang.Stringppppppppppsq ~W  wî           *  æ   pq ~ q ~ppppppq ~ ×ppppq ~ I  wîpppppt Verdanaq ~ pppppppppppsq ~ Mpsq ~ Q  wîppq ~sq ~2?   q ~¥q ~¥q ~£sq ~    sq ~ X  wîppppq ~¥q ~¥psq ~ R  wîppppq ~¥q ~¥psq ~ [  wîppq ~sq ~2?   q ~¥q ~¥psq ~ ]  wîppppq ~¥q ~¥ppppppppppppppppq ~¿  wî        pp~q ~nt REPORTsq ~ 9   uq ~ <   sq ~ >t PAGE_NUMBERt java.lang.Integerppppppppppsq ~W  wî          L   T   pq ~ q ~pt 
textField-215ppppq ~ ×ppppq ~ I  wîpppppt Arialsq ~    
pq ~q ~¢q ~¢pppppppsq ~ Mpsq ~ Q  wîsq ~a    ÿfffppppq ~0sq ~2?   q ~¹q ~¹q ~µpsq ~ X  wîppppq ~¹q ~¹psq ~ R  wîppppq ~¹q ~¹psq ~ [  wîppppq ~¹q ~¹psq ~ ]  wîppppq ~¹q ~¹pppppt Helvetica-BoldObliqueppppppppppq ~¿  wî       ppq ~osq ~ 9   uq ~ <   sq ~ >t "Dados da ImpressÃ£o: "+ sq ~ >t dadosImpressaot java.lang.Stringppppppq ~¢pppsq ~W  wî                *pq ~ q ~ppppppq ~ ×ppppq ~f  wîpppppt Arialsq ~    pq ~pppppppppsq ~ Mpsq ~ Q  wîppppq ~Ìq ~Ìq ~Épsq ~ X  wîppppq ~Ìq ~Ìpsq ~ R  wîppppq ~Ìq ~Ìpsq ~ [  wîppppq ~Ìq ~Ìpsq ~ ]  wîppppq ~Ìq ~Ìppt nonepppppppppppppq ~¿  wî       ppq ~osq ~ 9   uq ~ <   sq ~ >t "Filtros:"+ sq ~ >t filtrot java.lang.Stringppppppppppxp  wî   Appq ~­~r 3net.sf.jasperreports.engine.type.WhenNoDataTypeEnum          xq ~ t NO_PAGESsr 6net.sf.jasperreports.engine.design.JRReportCompileData      'Ø L crosstabCompileDataq ~zL datasetCompileDataq ~zL mainDatasetCompileDataq ~ xpsq ~­?@     w       xsq ~­?@     w       xur [B¬óøTà  xp  WÊþº¾   . %RelatorioCliente_1744627768762_969617  ,net/sf/jasperreports/engine/fill/JREvaluator  parameter_enderecoEmpresa 2Lnet/sf/jasperreports/engine/fill/JRFillParameter; parameter_JASPER_REPORT parameter_REPORT_TIME_ZONE parameter_usuario parameter_REPORT_FILE_RESOLVER parameter_REPORT_PARAMETERS_MAP parameter_REPORT_CLASS_LOADER $parameter_REPORT_URL_HANDLER_FACTORY parameter_REPORT_DATA_SOURCE parameter_IS_IGNORE_PAGINATION parameter_REPORT_MAX_COUNT parameter_REPORT_TEMPLATES parameter_apresentarLinha3 parameter_apresentarLinha1 parameter_apresentarLinha2 parameter_dataIni parameter_REPORT_LOCALE parameter_dadosImpressao parameter_REPORT_VIRTUALIZER parameter_filtro parameter_logoPadraoRelatorio parameter_REPORT_SCRIPTLET parameter_REPORT_CONNECTION parameter_totalClientes parameter_dataFim parameter_REPORT_FORMAT_FACTORY parameter_tituloRelatorio parameter_nomeEmpresa parameter_listaTotais parameter_cidadeEmpresa  parameter_REPORT_RESOURCE_BUNDLE parameter_versaoSoftware field_dataFimOperacao .Lnet/sf/jasperreports/engine/fill/JRFillField; field_vencimentoPlano field_professores field_estadoCivil 
field_sexo field_valorDevolvido field_consultor 
field_cnpj field_dataUltimoAcesso field_inicioPlano field_plano 	field_cep field_dataOperacao field_justificativa field_profissao field_dataNascimento 	field_cpf field_dataCadastro field_nomeProfessores field_complemento field_nomeRespContrato field_documento field_situacaoCliente field_dataInicioOperacao field_observacao field_matricula field_numero field_valorUtilizado field_valorContrato field_cidade field_bairro field_valorPago field_nomeConsultor field_email field_descricaoCancelamento field_telefone field_logradouro 
field_nome field_dataMatricula variable_PAGE_NUMBER 1Lnet/sf/jasperreports/engine/fill/JRFillVariable; variable_COLUMN_NUMBER variable_REPORT_COUNT variable_PAGE_COUNT variable_COLUMN_COUNT <init> ()V Code T U
  W  	  Y  	  [  	  ] 	 	  _ 
 	  a  	  c  	  e 
 	  g  	  i  	  k  	  m  	  o  	  q  	  s  	  u  	  w  	  y  	  {  	  }  	    	    	    	    	    	    	     	   ! 	   " 	   # 	   $ 	   % 	   & '	   ( '	   ) '	   * '	   + '	  ¡ , '	  £ - '	  ¥ . '	  § / '	  © 0 '	  « 1 '	  ­ 2 '	  ¯ 3 '	  ± 4 '	  ³ 5 '	  µ 6 '	  · 7 '	  ¹ 8 '	  » 9 '	  ½ : '	  ¿ ; '	  Á < '	  Ã = '	  Å > '	  Ç ? '	  É @ '	  Ë A '	  Í B '	  Ï C '	  Ñ D '	  Ó E '	  Õ F '	  × G '	  Ù H '	  Û I '	  Ý J '	  ß K '	  á L '	  ã M '	  å N O	  ç P O	  é Q O	  ë R O	  í S O	  ï LineNumberTable customizedInit 0(Ljava/util/Map;Ljava/util/Map;Ljava/util/Map;)V 
initParams (Ljava/util/Map;)V ô õ
  ö 
initFields ø õ
  ù initVars û õ
  ü enderecoEmpresa þ 
java/util/Map  get &(Ljava/lang/Object;)Ljava/lang/Object; 0net/sf/jasperreports/engine/fill/JRFillParameter 
JASPER_REPORT REPORT_TIME_ZONE
 usuario REPORT_FILE_RESOLVER REPORT_PARAMETERS_MAP REPORT_CLASS_LOADER REPORT_URL_HANDLER_FACTORY REPORT_DATA_SOURCE IS_IGNORE_PAGINATION REPORT_MAX_COUNT REPORT_TEMPLATES apresentarLinha3 apresentarLinha1  apresentarLinha2" dataIni$ 
REPORT_LOCALE& dadosImpressao( REPORT_VIRTUALIZER* filtro, logoPadraoRelatorio. REPORT_SCRIPTLET0 REPORT_CONNECTION2 
totalClientes4 dataFim6 REPORT_FORMAT_FACTORY8 tituloRelatorio: nomeEmpresa< listaTotais> 
cidadeEmpresa@ REPORT_RESOURCE_BUNDLEB versaoSoftwareD dataFimOperacaoF ,net/sf/jasperreports/engine/fill/JRFillFieldH vencimentoPlanoJ professoresL estadoCivilN sexoP valorDevolvidoR 	consultorT cnpjV dataUltimoAcessoX inicioPlanoZ plano\ cep^ dataOperacao` 
justificativab 	profissaod dataNascimentof cpfh dataCadastroj nomeProfessoresl complementon nomeRespContratop 	documentor situacaoClientet dataInicioOperacaov 
observacaox 	matriculaz numero| valorUtilizado~ 
valorContrato cidade bairro 	valorPago 
nomeConsultor email descricaoCancelamento telefone 
logradouro nome 
dataMatricula PAGE_NUMBER /net/sf/jasperreports/engine/fill/JRFillVariable 
COLUMN_NUMBER REPORT_COUNT 
PAGE_COUNT COLUMN_COUNT  evaluate (I)Ljava/lang/Object; 
Exceptions java/lang/Throwable¥ java/lang/Boolean§ valueOf (Z)Ljava/lang/Boolean;©ª
¨« java/lang/Integer­ (I)V T¯
®° getValue ()Ljava/lang/Object;²³
´ java/io/InputStream¶ java/util/Date¸
¹ W java/lang/StringBuffer» 	UsuÃ¡rio:½ (Ljava/lang/String;)V T¿
¼À java/lang/StringÂ append ,(Ljava/lang/String;)Ljava/lang/StringBuffer;ÄÅ
¼Æ toString ()Ljava/lang/String;ÈÉ
¼Ê 	PÃ¡gina: Ì
´ ,(Ljava/lang/Object;)Ljava/lang/StringBuffer;ÄÏ
¼Ð  deÒ Dados da ImpressÃ£o: Ô Filtros:Ö equals (Ljava/lang/Object;)ZØÙ
¨Ú
I´ 	evaluate1Ý£
 Þ R$ à trimâÉ
Ãã  å
ÃÚ
¹Ú length ()Iéê
Ãë ObservaÃ§Ã£o: í Justificativa: ï evaluateOld getOldValueò³
ó
Ió evaluateOld1ö£
 ÷ evaluateEstimated getEstimatedValueú³
û evaluateEstimated1ý£
 þ 
SourceFile !     L                 	     
               
                                                                                                     !     "     #     $     %     & '    ( '    ) '    * '    + '    , '    - '    . '    / '    0 '    1 '    2 '    3 '    4 '    5 '    6 '    7 '    8 '    9 '    : '    ; '    < '    = '    > '    ? '    @ '    A '    B '    C '    D '    E '    F '    G '    H '    I '    J '    K '    L '    M '    N O    P O    Q O    R O    S O     T U  V  Í    *· X*µ Z*µ \*µ ^*µ `*µ b*µ d*µ f*µ h*µ j*µ l*µ n*µ p*µ r*µ t*µ v*µ x*µ z*µ |*µ ~*µ *µ *µ *µ *µ *µ *µ *µ *µ *µ *µ *µ *µ *µ *µ *µ *µ  *µ ¢*µ ¤*µ ¦*µ ¨*µ ª*µ ¬*µ ®*µ °*µ ²*µ ´*µ ¶*µ ¸*µ º*µ ¼*µ ¾*µ À*µ Â*µ Ä*µ Æ*µ È*µ Ê*µ Ì*µ Î*µ Ð*µ Ò*µ Ô*µ Ö*µ Ø*µ Ú*µ Ü*µ Þ*µ à*µ â*µ ä*µ æ*µ è*µ ê*µ ì*µ î*µ ð±    ñ  : N      	          "  '   , ! 1 " 6 # ; $ @ % E & J ' O ( T ) Y * ^ + c , h - m . r / w 0 | 1  2  3  4  5  6  7  8 ¤ 9 © : ® ; ³ < ¸ = ½ > Â ? Ç @ Ì A Ñ B Ö C Û D à E å F ê G ï H ô I ù J þ K L M
 N O P Q! R& S+ T0 U5 V: W? XD YI ZN [S \X ]] ^b _g `l aq bv c{ d   ò ó  V   4     *+· ÷*,· ú*-· ý±    ñ       p  q 
 r  s  ô õ  V  ø    `*+ÿ¹ ÀÀµ Z*+	¹ ÀÀµ \*+¹ ÀÀµ ^*+
¹ ÀÀµ `*+¹ ÀÀµ b*+¹ ÀÀµ d*+¹ ÀÀµ f*+¹ ÀÀµ h*+¹ ÀÀµ j*+¹ ÀÀµ l*+¹ ÀÀµ n*+¹ ÀÀµ p*+¹ ÀÀµ r*+!¹ ÀÀµ t*+#¹ ÀÀµ v*+%¹ ÀÀµ x*+'¹ ÀÀµ z*+)¹ ÀÀµ |*++¹ ÀÀµ ~*+-¹ ÀÀµ *+/¹ ÀÀµ *+1¹ ÀÀµ *+3¹ ÀÀµ *+5¹ ÀÀµ *+7¹ ÀÀµ *+9¹ ÀÀµ *+;¹ ÀÀµ *+=¹ ÀÀµ *+?¹ ÀÀµ *+A¹ ÀÀµ *+C¹ ÀÀµ *+E¹ ÀÀµ ±    ñ    !   {  | % } 8 ~ K  ^  q      ª  ½  Ð  ã  ö 	  / B U h {  ¡ ´ Ç Ú í    & 9 L _   ø õ  V      æ*+G¹ ÀIÀIµ *+K¹ ÀIÀIµ *+M¹ ÀIÀIµ *+O¹ ÀIÀIµ  *+Q¹ ÀIÀIµ ¢*+S¹ ÀIÀIµ ¤*+U¹ ÀIÀIµ ¦*+W¹ ÀIÀIµ ¨*+Y¹ ÀIÀIµ ª*+[¹ ÀIÀIµ ¬*+]¹ ÀIÀIµ ®*+_¹ ÀIÀIµ °*+a¹ ÀIÀIµ ²*+c¹ ÀIÀIµ ´*+e¹ ÀIÀIµ ¶*+g¹ ÀIÀIµ ¸*+i¹ ÀIÀIµ º*+k¹ ÀIÀIµ ¼*+m¹ ÀIÀIµ ¾*+o¹ ÀIÀIµ À*+q¹ ÀIÀIµ Â*+s¹ ÀIÀIµ Ä*+u¹ ÀIÀIµ Æ*+w¹ ÀIÀIµ È*+y¹ ÀIÀIµ Ê*+{¹ ÀIÀIµ Ì*+}¹ ÀIÀIµ Î*+¹ ÀIÀIµ Ð*+¹ ÀIÀIµ Ò*+¹ ÀIÀIµ Ô*+¹ ÀIÀIµ Ö*+¹ ÀIÀIµ Ø*+¹ ÀIÀIµ Ú*+¹ ÀIÀIµ Ü*+¹ ÀIÀIµ Þ*+¹ ÀIÀIµ à*+¹ ÀIÀIµ â*+¹ ÀIÀIµ ä*+¹ ÀIÀIµ æ±    ñ   ¢ (   £  ¤ & ¥ 9 ¦ L § _ ¨ r ©  ª  « « ¬ ¾ ­ Ñ ® ä ¯ ÷ °
 ± ²0 ³C ´V µi ¶| · ¸¢ ¹µ ºÈ »Û ¼î ½ ¾ ¿' À: ÁM Â` Ãs Ä Å Æ¬ Ç¿ ÈÒ Éå Ê  û õ  V        `*+¹ ÀÀµ è*+¹ ÀÀµ ê*+¹ ÀÀµ ì*+¹ ÀÀµ î*+¡¹ ÀÀµ ð±    ñ       Ò  Ó & Ô 9 Õ L Ö _ × ¢£ ¤    ¦ V  
    
KMª  
@       c    ¥  ­  µ  Á  Í  Ù  å  ñ  ý  	    #  .  L  Z  ~    ª  È  ü  0  H    ¨  À  Ø  ð       8  P  h      °  È  à  ø    (  @  X  p       ¸  Ð  è       0  d  r  ¦  ´  Ì  Ú  ò       &  >  L    ¢  º  È  à  î      ,  :  R  `  x      ¬  Ä  Ò  ê  ø  	  	  	6  	D  	\  	j  	  	  	¨  	¶  	Î  	Ü  	ô  
  
  
(¸¬M§¤¸¬M§¸¬M§»®Y·±M§»®Y·±M§|»®Y·±M§p»®Y·±M§d»®Y·±M§X»®Y·±M§L»®Y·±M§@»®Y·±M§4*´ ¶µÀ·M§&»¹Y·ºM§»¼Y¾·Á*´ `¶µÀÃ¶Ç¶ËM§ý*´ ¶µÀÃM§ï»¼YÍ·Á*´ è¶ÎÀ®¶ÑÓ¶Ç¶ËM§Ë*´ è¶ÎÀ®M§½»¼YÕ·Á*´ |¶µÀÃ¶Ç¶ËM§»¼Y×·Á*´ ¶µÀÃ¶Ç¶ËM§*´ t¶µÀ¨¸¬¶Û *´ v¶µÀ¨¸¬¶Û § ¸¬M§M*´ t¶µÀ¨¸¬¶Û *´ v¶µÀ¨¸¬¶Û § ¸¬M§*´ t¶µÀ¨¸¬¶Û¸¬M§*´ t¶µÀ¨¸¬¶Û /*´ v¶µÀ¨¸¬¶Û *´ r¶µÀ¨¸¬¶Û § ¸¬M§¹*´ t¶µÀ¨¸¬¶Û¸¬M§¡*´ v¶µÀ¨¸¬¶Û¸¬M§*´ t¶µÀ¨¸¬¶Û¸¬M§q*´ v¶µÀ¨¸¬¶Û¸¬M§Y*´ v¶µÀ¨¸¬¶Û¸¬M§A*´ v¶µÀ¨¸¬¶Û¸¬M§)*´ v¶µÀ¨¸¬¶Û¸¬M§*´ v¶µÀ¨¸¬¶Û¸¬M§ù*´ r¶µÀ¨¸¬¶Û¸¬M§á*´ r¶µÀ¨¸¬¶Û¸¬M§É*´ r¶µÀ¨¸¬¶Û¸¬M§±*´ r¶µÀ¨¸¬¶Û¸¬M§*´ r¶µÀ¨¸¬¶Û¸¬M§*´ r¶µÀ¨¸¬¶Û¸¬M§i*´ t¶µÀ¨¸¬¶Û¸¬M§Q*´ t¶µÀ¨¸¬¶Û¸¬M§9*´ t¶µÀ¨¸¬¶Û¸¬M§!*´ t¶µÀ¨¸¬¶Û¸¬M§	*´ r¶µÀ¨¸¬¶Û¸¬M§ñ*´ r¶µÀ¨¸¬¶Û¸¬M§Ù*´ v¶µÀ¨¸¬¶Û¸¬M§Á*´ v¶µÀ¨¸¬¶Û¸¬M§©*´ r¶µÀ¨¸¬¶Û¸¬M§*´ r¶µÀ¨¸¬¶Û¸¬M§y*´ r¶µÀ¨¸¬¶Û¸¬M§a*´ r¶µÀ¨¸¬¶Û¸¬M§I*´ r¶µÀ¨¸¬¶Û¸¬M§1*´ r¶µÀ¨¸¬¶Û¸¬M§*´ t¶µÀ¨¸¬¶Û *´ v¶µÀ¨¸¬¶Û § ¸¬M§å*´ Ì¶ÜÀÃM§×*´ t¶µÀ¨¸¬¶Û *´ v¶µÀ¨¸¬¶Û § ¸¬M§£*´ ä¶ÜÀÃM§*´ t¶µÀ¨¸¬¶Û¸¬M§}*´ à¶ÜÀÃM§o*´ t¶µÀ¨¸¬¶Û¸¬M§W*´ ¸¶ÜÀ¹M§I*´ t¶µÀ¨¸¬¶Û¸¬M§1*´ Æ¶ÜÀÃM§#*´ t¶µÀ¨¸¬¶Û¸¬M§*´ ¢¶ÜÀÃM§ý*´ t¶µÀ¨¸¬¶Û /*´ v¶µÀ¨¸¬¶Û *´ r¶µÀ¨¸¬¶Û § ¸¬M§µ*´ ¼¶ÜÀ¹M§§*´ v¶µÀ¨¸¬¶Û¸¬M§*´ â¶ÜÀÃM§*´ v¶µÀ¨¸¬¶Û¸¬M§i*´ Î¶ÜÀÃM§[*´ v¶µÀ¨¸¬¶Û¸¬M§C*´ Ö¶ÜÀÃM§5*´ v¶µÀ¨¸¬¶Û¸¬M§*´ Ô¶ÜÀÃM§*´ v¶µÀ¨¸¬¶Û¸¬M§÷*´ °¶ÜÀÃM§é*´ v¶µÀ¨¸¬¶Û¸¬M§Ñ*´ À¶ÜÀÃM§Ã*´ r¶µÀ¨¸¬¶Û¸¬M§«*´ æ¶ÜÀ¹M§*´ r¶µÀ¨¸¬¶Û¸¬M§*´ ¬¶ÜÀ¹M§w*´ r¶µÀ¨¸¬¶Û¸¬M§_*´ ¶ÜÀ¹M§Q*´ r¶µÀ¨¸¬¶Û¸¬M§9*´ ª¶ÜÀ¹M§+*´ r¶µÀ¨¸¬¶Û¸¬M§*´ ®¶ÜÀÃM§*´ r¶µÀ¨¸¬¶Û¸¬M§ í*´ Ú¶ÜÀÃM§ ß*´ t¶µÀ¨¸¬¶Û¸¬M§ Ç*´ Ü¶ÜÀÃM§ ¹*´ t¶µÀ¨¸¬¶Û¸¬M§ ¡*´ Ä¶ÜÀÃM§ *´ t¶µÀ¨¸¬¶Û¸¬M§ {*´ ¨¶ÜÀÃM§ m*´ r¶µÀ¨¸¬¶Û¸¬M§ U*´ Â¶ÜÀÃM§ G*´ r¶µÀ¨¸¬¶Û¸¬M§ /*´ ¾¶ÜÀÃM§ !*´ v¶µÀ¨¸¬¶Û¸¬M§ 	*·ßM,°    ñ  2 Ì   ß  á  å¥ æ¨ ê­ ë° ïµ ð¸ ôÁ õÄ ùÍ úÐ þÙ ÿÜåèñ	ô
ý 	#&!."1&L'O+Z,]0~156:ª;­?È@ËDüEÿI0J3NHOKSTX¨Y«]À^ÃbØcÛgðhólmq r#v8w;{P|Shk°³ÈËàãøû£¤¨(©+­@®C²X³[·p¸s¼½Á Â£Æ¸Ç»ËÐÌÓÐèÑëÕ ÖÚÛß0à3ädågérêuî¦ï©ó´ô·øÌùÏýÚþÝòõ 
&)>ALO !%¢&¥*º+½/È0Ë4à5ã9î:ñ>?	CDH,I/M:N=RRSUW`Xc\x]{abfg¡k¬l¯pÄqÇuÒvÕzê{íøû				!	6	9	D	G	\	_	j	m¢	£	§	¨	¬	¨­	«±	¶²	¹¶	Î·	Ñ»	Ü¼	ßÀ	ôÁ	÷Å
Æ
Ê
Ë
Ï
(Ð
+Ô
@Õ
CÙ
IÝ Ý£ ¤    ¦ V  Ú    öMª  ñ   d   |   q         ¥   ½   Û   ù    %  3  Q    ¶  Ô  ð    +  9  V  d      »  Ø  æ*´  ¶ÜÀÃM§u*´ v¶µÀ¨¸¬¶Û¸¬M§]*´ ¶¶ÜÀÃM§O*´ r¶µÀ¨¸¬¶Û¸¬M§7»¼Yá·Á*´ Ø¶ÜÀÃ¶Ç¶ËM§»¼Yá·Á*´ ¤¶ÜÀÃ¶Ç¶ËM§û*´ Þ¶ÜÀÃM§í»¼Yá·Á*´ Ò¶ÜÀÃ¶Ç¶ËM§Ï*´ º¶ÜÀÃM§Á»¼Yá·Á*´ Ð¶ÜÀÃ¶Ç¶ËM§£*´ ´¶ÜÀÃ¶äæ¶ç .*´ Ê¶ÜÀÃ¶äæ¶ç *´ ²¶ÜÀ¹¶è § ¸¬M§Z*´ Ê¶ÜÀÃ¶ì § ¸¬M§>»¼Yî·Á*´ Ê¶ÜÀÃ¶Ç¶ËM§ *´ ´¶ÜÀÃ¶ì § ¸¬M§»¼Yð·Á*´ ´¶ÜÀÃ¶Ç¶ËM§ æ*´ È¶ÜÀ¹¶è § ¸¬M§ É*´ È¶ÜÀ¹M§ »*´ ¶ÜÀ¹¶è § ¸¬M§ *´ ¶ÜÀ¹M§ *´ È¶ÜÀ¹¶è § ¸¬M§ s*´ ¶ÜÀ¹¶è § ¸¬M§ V*´ ²¶ÜÀ¹¶è § ¸¬M§ 9*´ ²¶ÜÀ¹¶è § ¸¬M§ *´ ²¶ÜÀ¹M§ *´ ¶µÀ®M,°    ñ   Ò 4  æ è tì í ñ ò ö ¥÷ ¨û ½ü À  Û Þ ù ü

%(36QT#¶$¹(Ô)×-ð.ó237+8.<9=<AVBYFdGgKLPQ¡U»V¾ZØ[Û_æ`édôl ñ£ ¤    ¦ V  
    
KMª  
@       c    ¥  ­  µ  Á  Í  Ù  å  ñ  ý  	    #  .  L  Z  ~    ª  È  ü  0  H    ¨  À  Ø  ð       8  P  h      °  È  à  ø    (  @  X  p       ¸  Ð  è       0  d  r  ¦  ´  Ì  Ú  ò       &  >  L    ¢  º  È  à  î      ,  :  R  `  x      ¬  Ä  Ò  ê  ø  	  	  	6  	D  	\  	j  	  	  	¨  	¶  	Î  	Ü  	ô  
  
  
(¸¬M§¤¸¬M§¸¬M§»®Y·±M§»®Y·±M§|»®Y·±M§p»®Y·±M§d»®Y·±M§X»®Y·±M§L»®Y·±M§@»®Y·±M§4*´ ¶µÀ·M§&»¹Y·ºM§»¼Y¾·Á*´ `¶µÀÃ¶Ç¶ËM§ý*´ ¶µÀÃM§ï»¼YÍ·Á*´ è¶ôÀ®¶ÑÓ¶Ç¶ËM§Ë*´ è¶ôÀ®M§½»¼YÕ·Á*´ |¶µÀÃ¶Ç¶ËM§»¼Y×·Á*´ ¶µÀÃ¶Ç¶ËM§*´ t¶µÀ¨¸¬¶Û *´ v¶µÀ¨¸¬¶Û § ¸¬M§M*´ t¶µÀ¨¸¬¶Û *´ v¶µÀ¨¸¬¶Û § ¸¬M§*´ t¶µÀ¨¸¬¶Û¸¬M§*´ t¶µÀ¨¸¬¶Û /*´ v¶µÀ¨¸¬¶Û *´ r¶µÀ¨¸¬¶Û § ¸¬M§¹*´ t¶µÀ¨¸¬¶Û¸¬M§¡*´ v¶µÀ¨¸¬¶Û¸¬M§*´ t¶µÀ¨¸¬¶Û¸¬M§q*´ v¶µÀ¨¸¬¶Û¸¬M§Y*´ v¶µÀ¨¸¬¶Û¸¬M§A*´ v¶µÀ¨¸¬¶Û¸¬M§)*´ v¶µÀ¨¸¬¶Û¸¬M§*´ v¶µÀ¨¸¬¶Û¸¬M§ù*´ r¶µÀ¨¸¬¶Û¸¬M§á*´ r¶µÀ¨¸¬¶Û¸¬M§É*´ r¶µÀ¨¸¬¶Û¸¬M§±*´ r¶µÀ¨¸¬¶Û¸¬M§*´ r¶µÀ¨¸¬¶Û¸¬M§*´ r¶µÀ¨¸¬¶Û¸¬M§i*´ t¶µÀ¨¸¬¶Û¸¬M§Q*´ t¶µÀ¨¸¬¶Û¸¬M§9*´ t¶µÀ¨¸¬¶Û¸¬M§!*´ t¶µÀ¨¸¬¶Û¸¬M§	*´ r¶µÀ¨¸¬¶Û¸¬M§ñ*´ r¶µÀ¨¸¬¶Û¸¬M§Ù*´ v¶µÀ¨¸¬¶Û¸¬M§Á*´ v¶µÀ¨¸¬¶Û¸¬M§©*´ r¶µÀ¨¸¬¶Û¸¬M§*´ r¶µÀ¨¸¬¶Û¸¬M§y*´ r¶µÀ¨¸¬¶Û¸¬M§a*´ r¶µÀ¨¸¬¶Û¸¬M§I*´ r¶µÀ¨¸¬¶Û¸¬M§1*´ r¶µÀ¨¸¬¶Û¸¬M§*´ t¶µÀ¨¸¬¶Û *´ v¶µÀ¨¸¬¶Û § ¸¬M§å*´ Ì¶õÀÃM§×*´ t¶µÀ¨¸¬¶Û *´ v¶µÀ¨¸¬¶Û § ¸¬M§£*´ ä¶õÀÃM§*´ t¶µÀ¨¸¬¶Û¸¬M§}*´ à¶õÀÃM§o*´ t¶µÀ¨¸¬¶Û¸¬M§W*´ ¸¶õÀ¹M§I*´ t¶µÀ¨¸¬¶Û¸¬M§1*´ Æ¶õÀÃM§#*´ t¶µÀ¨¸¬¶Û¸¬M§*´ ¢¶õÀÃM§ý*´ t¶µÀ¨¸¬¶Û /*´ v¶µÀ¨¸¬¶Û *´ r¶µÀ¨¸¬¶Û § ¸¬M§µ*´ ¼¶õÀ¹M§§*´ v¶µÀ¨¸¬¶Û¸¬M§*´ â¶õÀÃM§*´ v¶µÀ¨¸¬¶Û¸¬M§i*´ Î¶õÀÃM§[*´ v¶µÀ¨¸¬¶Û¸¬M§C*´ Ö¶õÀÃM§5*´ v¶µÀ¨¸¬¶Û¸¬M§*´ Ô¶õÀÃM§*´ v¶µÀ¨¸¬¶Û¸¬M§÷*´ °¶õÀÃM§é*´ v¶µÀ¨¸¬¶Û¸¬M§Ñ*´ À¶õÀÃM§Ã*´ r¶µÀ¨¸¬¶Û¸¬M§«*´ æ¶õÀ¹M§*´ r¶µÀ¨¸¬¶Û¸¬M§*´ ¬¶õÀ¹M§w*´ r¶µÀ¨¸¬¶Û¸¬M§_*´ ¶õÀ¹M§Q*´ r¶µÀ¨¸¬¶Û¸¬M§9*´ ª¶õÀ¹M§+*´ r¶µÀ¨¸¬¶Û¸¬M§*´ ®¶õÀÃM§*´ r¶µÀ¨¸¬¶Û¸¬M§ í*´ Ú¶õÀÃM§ ß*´ t¶µÀ¨¸¬¶Û¸¬M§ Ç*´ Ü¶õÀÃM§ ¹*´ t¶µÀ¨¸¬¶Û¸¬M§ ¡*´ Ä¶õÀÃM§ *´ t¶µÀ¨¸¬¶Û¸¬M§ {*´ ¨¶õÀÃM§ m*´ r¶µÀ¨¸¬¶Û¸¬M§ U*´ Â¶õÀÃM§ G*´ r¶µÀ¨¸¬¶Û¸¬M§ /*´ ¾¶õÀÃM§ !*´ v¶µÀ¨¸¬¶Û¸¬M§ 	*·øM,°    ñ  2 Ì  u w {¥|¨­°µ¸ÁÄÍÐÙÜåèñô£ý¤ ¨	©­®²#³&·.¸1¼L½OÁZÂ]Æ~ÇËÌÐªÑ­ÕÈÖËÚüÛÿß0à3äHåKéêî¨ï«óÀôÃøØùÛýðþó #8
;PShk !%°&³*È+Ë/à0ã4ø5û9:>(?+C@DCHXI[MpNsRSW X£\¸]»aÐbÓfègëk lpqu0v3zd{gru¦©´·ÌÏÚÝòõ ¢£§&¨)¬>­A±L²O¶·»¢¼¥ÀºÁ½ÅÈÆËÊàËãÏîÐñÔÕ	ÙÚÞ,ß/ã:ä=èRéUí`îcòxó{÷øüý¡¬¯ÄÇÒÕêíøû			 	!$	6%	9)	D*	G.	\/	_3	j4	m8	9	=	>	B	¨C	«G	¶H	¹L	ÎM	ÑQ	ÜR	ßV	ôW	÷[
\
`
a
e
(f
+j
@k
Co
Is ö£ ¤    ¦ V  Ú    öMª  ñ   d   |   q         ¥   ½   Û   ù    %  3  Q    ¶  Ô  ð    +  9  V  d      »  Ø  æ*´  ¶õÀÃM§u*´ v¶µÀ¨¸¬¶Û¸¬M§]*´ ¶¶õÀÃM§O*´ r¶µÀ¨¸¬¶Û¸¬M§7»¼Yá·Á*´ Ø¶õÀÃ¶Ç¶ËM§»¼Yá·Á*´ ¤¶õÀÃ¶Ç¶ËM§û*´ Þ¶õÀÃM§í»¼Yá·Á*´ Ò¶õÀÃ¶Ç¶ËM§Ï*´ º¶õÀÃM§Á»¼Yá·Á*´ Ð¶õÀÃ¶Ç¶ËM§£*´ ´¶õÀÃ¶äæ¶ç .*´ Ê¶õÀÃ¶äæ¶ç *´ ²¶õÀ¹¶è § ¸¬M§Z*´ Ê¶õÀÃ¶ì § ¸¬M§>»¼Yî·Á*´ Ê¶õÀÃ¶Ç¶ËM§ *´ ´¶õÀÃ¶ì § ¸¬M§»¼Yð·Á*´ ´¶õÀÃ¶Ç¶ËM§ æ*´ È¶õÀ¹¶è § ¸¬M§ É*´ È¶õÀ¹M§ »*´ ¶õÀ¹¶è § ¸¬M§ *´ ¶õÀ¹M§ *´ È¶õÀ¹¶è § ¸¬M§ s*´ ¶õÀ¹¶è § ¸¬M§ V*´ ²¶õÀ¹¶è § ¸¬M§ 9*´ ²¶õÀ¹¶è § ¸¬M§ *´ ²¶õÀ¹M§ *´ ¶µÀ®M,°    ñ   Ò 4  | ~ t     ¥ ¨ ½ À Û Þ ù ü ¡
¥%¦(ª3«6¯Q°T´µ¹¶º¹¾Ô¿×ÃðÄóÈÉÍ+Î.Ò9Ó<×VØYÜdÝgáâæç¡ë»ì¾ðØñÛõæöéúô ù£ ¤    ¦ V  
    
KMª  
@       c    ¥  ­  µ  Á  Í  Ù  å  ñ  ý  	    #  .  L  Z  ~    ª  È  ü  0  H    ¨  À  Ø  ð       8  P  h      °  È  à  ø    (  @  X  p       ¸  Ð  è       0  d  r  ¦  ´  Ì  Ú  ò       &  >  L    ¢  º  È  à  î      ,  :  R  `  x      ¬  Ä  Ò  ê  ø  	  	  	6  	D  	\  	j  	  	  	¨  	¶  	Î  	Ü  	ô  
  
  
(¸¬M§¤¸¬M§¸¬M§»®Y·±M§»®Y·±M§|»®Y·±M§p»®Y·±M§d»®Y·±M§X»®Y·±M§L»®Y·±M§@»®Y·±M§4*´ ¶µÀ·M§&»¹Y·ºM§»¼Y¾·Á*´ `¶µÀÃ¶Ç¶ËM§ý*´ ¶µÀÃM§ï»¼YÍ·Á*´ è¶üÀ®¶ÑÓ¶Ç¶ËM§Ë*´ è¶üÀ®M§½»¼YÕ·Á*´ |¶µÀÃ¶Ç¶ËM§»¼Y×·Á*´ ¶µÀÃ¶Ç¶ËM§*´ t¶µÀ¨¸¬¶Û *´ v¶µÀ¨¸¬¶Û § ¸¬M§M*´ t¶µÀ¨¸¬¶Û *´ v¶µÀ¨¸¬¶Û § ¸¬M§*´ t¶µÀ¨¸¬¶Û¸¬M§*´ t¶µÀ¨¸¬¶Û /*´ v¶µÀ¨¸¬¶Û *´ r¶µÀ¨¸¬¶Û § ¸¬M§¹*´ t¶µÀ¨¸¬¶Û¸¬M§¡*´ v¶µÀ¨¸¬¶Û¸¬M§*´ t¶µÀ¨¸¬¶Û¸¬M§q*´ v¶µÀ¨¸¬¶Û¸¬M§Y*´ v¶µÀ¨¸¬¶Û¸¬M§A*´ v¶µÀ¨¸¬¶Û¸¬M§)*´ v¶µÀ¨¸¬¶Û¸¬M§*´ v¶µÀ¨¸¬¶Û¸¬M§ù*´ r¶µÀ¨¸¬¶Û¸¬M§á*´ r¶µÀ¨¸¬¶Û¸¬M§É*´ r¶µÀ¨¸¬¶Û¸¬M§±*´ r¶µÀ¨¸¬¶Û¸¬M§*´ r¶µÀ¨¸¬¶Û¸¬M§*´ r¶µÀ¨¸¬¶Û¸¬M§i*´ t¶µÀ¨¸¬¶Û¸¬M§Q*´ t¶µÀ¨¸¬¶Û¸¬M§9*´ t¶µÀ¨¸¬¶Û¸¬M§!*´ t¶µÀ¨¸¬¶Û¸¬M§	*´ r¶µÀ¨¸¬¶Û¸¬M§ñ*´ r¶µÀ¨¸¬¶Û¸¬M§Ù*´ v¶µÀ¨¸¬¶Û¸¬M§Á*´ v¶µÀ¨¸¬¶Û¸¬M§©*´ r¶µÀ¨¸¬¶Û¸¬M§*´ r¶µÀ¨¸¬¶Û¸¬M§y*´ r¶µÀ¨¸¬¶Û¸¬M§a*´ r¶µÀ¨¸¬¶Û¸¬M§I*´ r¶µÀ¨¸¬¶Û¸¬M§1*´ r¶µÀ¨¸¬¶Û¸¬M§*´ t¶µÀ¨¸¬¶Û *´ v¶µÀ¨¸¬¶Û § ¸¬M§å*´ Ì¶ÜÀÃM§×*´ t¶µÀ¨¸¬¶Û *´ v¶µÀ¨¸¬¶Û § ¸¬M§£*´ ä¶ÜÀÃM§*´ t¶µÀ¨¸¬¶Û¸¬M§}*´ à¶ÜÀÃM§o*´ t¶µÀ¨¸¬¶Û¸¬M§W*´ ¸¶ÜÀ¹M§I*´ t¶µÀ¨¸¬¶Û¸¬M§1*´ Æ¶ÜÀÃM§#*´ t¶µÀ¨¸¬¶Û¸¬M§*´ ¢¶ÜÀÃM§ý*´ t¶µÀ¨¸¬¶Û /*´ v¶µÀ¨¸¬¶Û *´ r¶µÀ¨¸¬¶Û § ¸¬M§µ*´ ¼¶ÜÀ¹M§§*´ v¶µÀ¨¸¬¶Û¸¬M§*´ â¶ÜÀÃM§*´ v¶µÀ¨¸¬¶Û¸¬M§i*´ Î¶ÜÀÃM§[*´ v¶µÀ¨¸¬¶Û¸¬M§C*´ Ö¶ÜÀÃM§5*´ v¶µÀ¨¸¬¶Û¸¬M§*´ Ô¶ÜÀÃM§*´ v¶µÀ¨¸¬¶Û¸¬M§÷*´ °¶ÜÀÃM§é*´ v¶µÀ¨¸¬¶Û¸¬M§Ñ*´ À¶ÜÀÃM§Ã*´ r¶µÀ¨¸¬¶Û¸¬M§«*´ æ¶ÜÀ¹M§*´ r¶µÀ¨¸¬¶Û¸¬M§*´ ¬¶ÜÀ¹M§w*´ r¶µÀ¨¸¬¶Û¸¬M§_*´ ¶ÜÀ¹M§Q*´ r¶µÀ¨¸¬¶Û¸¬M§9*´ ª¶ÜÀ¹M§+*´ r¶µÀ¨¸¬¶Û¸¬M§*´ ®¶ÜÀÃM§*´ r¶µÀ¨¸¬¶Û¸¬M§ í*´ Ú¶ÜÀÃM§ ß*´ t¶µÀ¨¸¬¶Û¸¬M§ Ç*´ Ü¶ÜÀÃM§ ¹*´ t¶µÀ¨¸¬¶Û¸¬M§ ¡*´ Ä¶ÜÀÃM§ *´ t¶µÀ¨¸¬¶Û¸¬M§ {*´ ¨¶ÜÀÃM§ m*´ r¶µÀ¨¸¬¶Û¸¬M§ U*´ Â¶ÜÀÃM§ G*´ r¶µÀ¨¸¬¶Û¸¬M§ /*´ ¾¶ÜÀÃM§ !*´ v¶µÀ¨¸¬¶Û¸¬M§ 	*·ÿM,°    ñ  2 Ì   
 ¥¨­°µ¸ Á!Ä%Í&Ð*Ù+Ü/å0è4ñ5ô9ý: >	?CDH#I&M.N1RLSOWZX]\~]abfªg­kÈlËpüqÿu0v3zH{K¨«ÀÃØÛðó #¢8£;§P¨S¬h­k±²¶·»°¼³ÀÈÁËÅàÆãÊøËûÏÐÔ(Õ+Ù@ÚCÞXß[ãpäsèéí î£ò¸ó»÷ÐøÓüèýë 03dgru¦©´ ·$Ì%Ï)Ú*Ý.ò/õ3 489=&>)B>CAGLHOLMQ¢R¥VºW½[È\Ë`àaãeîfñjk	opt,u/y:z=~RU`cx{¡¬¯ÄÇ¡Ò¢Õ¦ê§í«ø¬û°	±	µ	¶	!º	6»	9¿	DÀ	GÄ	\Å	_É	jÊ	mÎ	Ï	Ó	Ô	Ø	¨Ù	«Ý	¶Þ	¹â	Îã	Ñç	Üè	ßì	ôí	÷ñ
ò
ö
÷
û
(ü
+ 
@
C
I	 ý£ ¤    ¦ V  Ú    öMª  ñ   d   |   q         ¥   ½   Û   ù    %  3  Q    ¶  Ô  ð    +  9  V  d      »  Ø  æ*´  ¶ÜÀÃM§u*´ v¶µÀ¨¸¬¶Û¸¬M§]*´ ¶¶ÜÀÃM§O*´ r¶µÀ¨¸¬¶Û¸¬M§7»¼Yá·Á*´ Ø¶ÜÀÃ¶Ç¶ËM§»¼Yá·Á*´ ¤¶ÜÀÃ¶Ç¶ËM§û*´ Þ¶ÜÀÃM§í»¼Yá·Á*´ Ò¶ÜÀÃ¶Ç¶ËM§Ï*´ º¶ÜÀÃM§Á»¼Yá·Á*´ Ð¶ÜÀÃ¶Ç¶ËM§£*´ ´¶ÜÀÃ¶äæ¶ç .*´ Ê¶ÜÀÃ¶äæ¶ç *´ ²¶ÜÀ¹¶è § ¸¬M§Z*´ Ê¶ÜÀÃ¶ì § ¸¬M§>»¼Yî·Á*´ Ê¶ÜÀÃ¶Ç¶ËM§ *´ ´¶ÜÀÃ¶ì § ¸¬M§»¼Yð·Á*´ ´¶ÜÀÃ¶Ç¶ËM§ æ*´ È¶ÜÀ¹¶è § ¸¬M§ É*´ È¶ÜÀ¹M§ »*´ ¶ÜÀ¹¶è § ¸¬M§ *´ ¶ÜÀ¹M§ *´ È¶ÜÀ¹¶è § ¸¬M§ s*´ ¶ÜÀ¹¶è § ¸¬M§ V*´ ²¶ÜÀ¹¶è § ¸¬M§ 9*´ ²¶ÜÀ¹¶è § ¸¬M§ *´ ²¶ÜÀ¹M§ *´ ¶µÀ®M,°    ñ   Ò 4    t    " ¥# ¨' ½( À, Û- Þ1 ù2 ü67
;%<(@3A6EQFTJKO¶P¹TÔU×YðZó^_c+d.h9i<mVnYrdsgwx|}¡»¾ØÛæéô      t _1744627768762_969617t 2net.sf.jasperreports.engine.design.JRJavacCompiler