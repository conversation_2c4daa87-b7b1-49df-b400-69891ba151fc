# ==============================================================================
# Labels do Central de Eventos - Em ordem alfab\u00e9tica das chaves.
# ==============================================================================

entidade.agencia=Agencia
entidade.agendaVisita.acoesVisita=A\u00e7\u00f5es do Agendamento da Visita
entidade.agendaVisita.dadosVisita=Dados do Agendamento da Visita
entidade.agendaVisita.dataVisita=Data da Visita
entidade.agendaVisita.novaDataVisita=Nova Data da Visita
entidade.agendaVisita.horarioFinal=Hor\u00e1rio Final
entidade.agendaVisita.horarioInicial=Hor\u00e1rio Inicial
entidade.agendaVisita.observacoes=Observa\u00e7\u00f5es
entidade.agendaVisita.tipoVisita=Tipo de Visita
entidade.agrupar=Agrupar por
entidade.agruparAmbiente=Agrupar por Ambiente
entidade.agruparTipo=Agrupar por Tipo
entidade.ambiente=Ambiente
entidade.ambiente.desconto=Desconto em Ambiente
entidade.ambiente.valorComDesconto=Valor com Desconto
entidade.ambientes=Ambientes
entidade.arquivo=Arquivo
entidade.atendente=Atendente
entidade.data.encerramento=Data Encerramento
entidade.responsavel.encerramento=Responsavel Encerramento
entidade.obs.encerramento=Obs. Encerramento
 
entidade.bemConsumo=Bem de Consumo
entidade.bensConsumo.consulta=Consulta de Bens de Consumo
entidade.bensConsumo=Bens de Consumo
entidade.brinquedo=Brinquedo
entidade.brinquedos.consulta=Consulta de Brinquedos
entidade.brinquedos=Brinquedos

entidade.cadastroInicial.conversa=Clique aqui para inserir um novo Interessado na tela de Cadastro Inicial. 
entidade.cadastroInicial.hint.ambienteInteresse=Selecione um dos ambientes onde pode ser realizado o evento desejado. Obs: \u00c9 necess\u00e1rio selecionar uma data de interesse para verificar a sua disponibilidade.
entidade.cadastroInicial.hint.dataInteresse=Selecionar data de interesse para realiza\u00e7\u00e3o do Evento.Obs. \u00c9 necess\u00e1rio selecionar um ambiente para verificar a disponibilidade.
entidade.cadastroInicial.hint.nomeCliente=Nome do cliente respons\u00e1vel pelo evento.
entidade.cadastroInicial.hint.nomeEvento=Inserir um nome para o evento. Ex: Anivers\u00e1rio da Maria, Casamento de Maria e Jo\u00e3o.
entidade.cadastroInicial.hint.qeclienteEvento=Escrever o nome da pessoa do evento. Ex. Se no nome do evento colocou Anivers\u00e1rio escrever aqui o nome da aniversariante.
entidade.centralEventos.cadastro=Central de Eventos - Cadastro
entidade.centralEventos.funcoes=Central de Eventos - Fun\u00e7\u00f5es
entidade.conversa.hint.disponibilidade=\u00c9 preciso selecionar uma data e ambiente para verificar a disponibilidade
entidade.conversa.hint.evento=\u00c9 preciso selecionar um evento para ver sua informa\u00e7\u00f5es.
entidade.detalhamentoEvento.hint.fecharNegociacao=Para poder fechar a negocia\u00e7\u00e3o \u00e9 preciso cadastrar o cliente.
entidade.prospect.hint.cadastro=Lista em ordem crescente. Caso n\u00e3o exista lista os n\u00e3o agendados.
entidade.prospect.hint.conversa=Lista os nomes que tenham alguma conversa cadastrada.

entidade.campoObrigatorio=Campo Obrigat\u00f3rio
entidade.cartaoDebito=Cart\u00e3o de D\u00e9bito
entidade.categoriaAmbiente=Categoria de Ambiente
entidade.cel=Celular
entidade.checklist=Checklist
entidade.cheque=Cheque
entidade.cheque.tipoValorCaucao=Tipo Valor do Cheque Cau\u00e7\u00e3o
entidade.cliente.cadastrar=Cadastrar Cliente
entidade.cliente.nome=Nome do Cliente
entidade.cliente=Cliente
entidade.clienteInexistente=Cliente Inexistente
entidade.codigo=C\u00f3digo
entidade.compensacao=Compensa\u00e7\u00e3o
entidade.condicaoPagamento=Condi\u00e7\u00e3o de Pagamento
entidade.chequeCaucaoECredito=Cheque Cau\u00e7\u00e3o e Cr\u00e9dito
entidade.consultaEventos.agenda=Agenda
entidade.consultaEventos.dia.situacao.cheio=Dia Lotado
entidade.consultaEventos.dia.situacao.disp=Com reservas, mas com disponibilidade
entidade.consultaEventos.dia.situacao.vazio=Dia Vazio
entidade.consultaEventos.legenda=Legenda
entidade.consultaEventos.preReserva=Pr\u00e9-Reservado
entidade.consultaEventos.autorizadoE=Aguardando Encerramento
entidade.consultaEventos.encerrado=Encerrado
entidade.consultaEventos.visita=Visita
entidade.consultaEventos.aguardando=Aguardando autoriza\u00e7\u00e3o
entidade.consultaMes=Consultar M\u00eas
entidade.conta=Conta
entidade.contato=Contato
entidade.contaCorrenteCliente=Conta Corrente do Cliente
entidade.contrato.imagens.adicionar=Adicionar Imagens
entidade.contrato.modelo=Modelo de Contrato
entidade.contrato.modelos=Modelos de Contrato
entidade.contrato.tags.ambiente=#ambiente - Nome do Ambiente reservado para o Evento
entidade.contrato.tags.bairro=#bairro - Bairro
entidade.contrato.tags.bensConsumo=#bensdeconsumo - Bens de Consumo registrados
entidade.contrato.tags.bensConsumolistahorizontal=#bensdeconsumolistahorizontal - Bens de Consumo registrados Listados na horizontal
entidade.contrato.tags.bensConsumolistavertical=#bensdeconsumolistavertical - Bens de Consumo registrados Listados na vertical
entidade.contrato.tags.brinquedoslistahorizontal=#brinquedoslistahorizontal - Brinquedos registrados Listados na horizontal
entidade.contrato.tags.brinquedoslistavertical=#brinquedoslistavertical - Brinquedos registrados Listados na vertical
entidade.contrato.tags.cheque.cheque=#cheque - Informa\u00e7\u00f5es do Cheque
entidade.contrato.tags.cidade=#cidade - Cidade
entidade.contrato.tags.condPag=#condicaopagamento - Condi\u00e7\u00e3o de Pagamento
entidade.contrato.tags.cpf=#cpf - CPF do Cliente
entidade.contrato.tags.data=#data - Data do Evento
entidade.contrato.tags.dataCompensacao=#compensacaodata - Data de Compensa\u00e7\u00e3o
entidade.contrato.tags.datasParcelas=#vencimentoparcelas - Tabela com Data de Vencimento de todas as Parcelas
entidade.contrato.tags.duracaoEvento=#tempoduracaoevento - Tempo de dura\u00e7\u00e3o do Evento em horas
entidade.contrato.tags.endereco=#endereco - Endere\u00e7o
entidade.contrato.tags.eventohorainicial=#eventohorainicial - Hor\u00e1rio inicial do evento
entidade.contrato.tags.eventohorafinal=#eventohorafinal - Hor\u00e1rio final do evento
entidade.contrato.tags.formaPagamento=#formapagamento - Forma de Pagamento
entidade.contrato.tags.horario=#horario - Hor\u00e1rio do Evento
entidade.contrato.tags.layout=#layout - Tipo de Layout do Ambiente
entidade.contrato.tags.nomeCliente=#nomecliente - Nome do Cliente
entidade.contrato.tags.nomeEvento=#nomeevento - Nome do Evento
entidade.contrato.tags.observacao=#observacao - Observa\u00e7\u00e3o
entidade.contrato.tags.observacaoAmbiente=#obsambiente - Observa\u00e7\u00e3o do Ambiente
entidade.contrato.tags.parcelas=#parcelas - Tabela com todos os dados de Parcelas
entidade.contrato.tags.qtdConvidados=#qtdconvidados - Quantidade de Convidados
entidade.contrato.tags.rg=#rg - RG do Cliente
entidade.contrato.tags.servicoslistahorizontal=#servicoslistahorizontal - Servi\u00e7os contratados Listados na horizontal
entidade.contrato.tags.servicoslistavertical=#servicoslistavertical - Servi\u00e7os contratados Listados na vertical
entidade.contrato.tags.telefones=#telefones - Telefones
entidade.contrato.tags.texto=#texto - Texto Padr\u00e3o definido para o Evento
entidade.contrato.tags.total=#total - Valor Total do Evento
entidade.contrato.tags.totalDesc=#descontos - Valor Total de descontos na negocia\u00e7\u00e3o
entidade.contrato.tags.utensilioslistahorizontal=#utensilioslistahorizontal - Utensil\u00edos registrados Listados na horizontal
entidade.contrato.tags.utensilioslistavertical=#utensilioslistavertical - Utensil\u00edos registrados Listados na vertical
entidade.contrato.tags.valorFinal=#valorfinal - Valor Final do Evento
entidade.contrato.tags.valorParcela=#valorparcela - Tabela com Valor de cada Parcela
entidade.contrato.tags=Tags Dispon\u00edveis
entidade.contrato.visualizarModelos=Visualizar Modelos de Contrato
entidade.contratos=Contratos
entidade.convidados=Convidados
entidade.conversa.registroconversa=Registro da Conversa
entidade.cnpj=CNPJ
entidade.inscricaoEstadual=Inscri\u00e7\u00e3o Estadual
entidade.cfdf=CFDF
entidade.cpf=CPF
entidade.preenchaCadastroCliente=Informa\u00e7\u00f5es complementares

entidade.dadosauxiliares=Dados Auxiliares
entidade.data=Data
entidade.data.de=De
entidade.dataAte=at\u00e9
entidade.dataCadastro=Data do cadastro
entidade.dataContato=Data do Contato
entidade.dataConversa = Data da conversa
entidade.dataDe=Per\u00edodo: de
entidade.dataEnvio=Data de Envio
entidade.dataFim=Data fim
entidade.dataHorario=Data/Hor\u00e1rio e Perfil
entidade.dataInicio=Data in\u00edcio
entidade.dataInteresse=Data de Interesse Inicial
entidade.dataImpress\u00e3o Data de impress\u00e3o
entidade.dataProxContato=Data do Pr\u00f3ximo Contato
entidade.dataRealizacao=Data de Realiza\u00e7\u00e3o do Evento
entidade.dataRegistro=Data de registro
entidade.dataTermino=Data t\u00e9rmino
entidade.dataValidade=Data de Validade
entidade.desconto.aplicar=Aplicar Desconto
entidade.caucao.lancar=Lan\u00e7ar Cau\u00e7\u00e3o
entidade.credito.lancar=Lan\u00e7ar Cr\u00e9dito
entidade.desconto.tipo=Tipo de Desconto
entidade.desconto.total=Total de Descontos
entidade.desconto.valor=Valor do Desconto
entidade.caucao.valor=Valor do Cau\u00e7\u00e3o
entidade.credito.valor=Valor do Cr\u00e9dito
entidade.descricao=Descri\u00e7\u00e3o
entidade.descricaoConversa=Descri\u00e7\u00e3o da Conversa
entidade.descricaoContato=Descri\u00e7\u00e3o do Contato
entidade.diaSemana=Dia da semana
entidade.diferenca=Diferen\u00e7a
entidade.diferencaValores=Diferen\u00e7a restante
entidade.dispAmbiente.hint.ambiente=Realiza a consulta referente aos ambientes. Ex-Sal\u00e3o-1.
entidade.dispAmbiente.hint.buscar=Realiza todas as op\u00e7\u00f5es de busca referentes a guia consulta. Ex-Consultar a data do dia.
entidade.dispAmbiente.hint.categoriaAmbiente=Realiza a consulta referente a categoria de ambiente. Ex-Sal\u00e3o.
entidade.dispAmbiente.hint.mes=Consulta o m\u00eas corrente.
entidade.dispAmbiente.hint.filtroData=Essas consultas podem ser feitas pela-Data(Consulta o dia pelo calend\u00e1rio). Intervalo de Data(consulta uma faixa de dias entre o inicio e o fim). M\u00eas/Semana(M\u00eas-Consulta o m\u00eas, Semana-Consulta a semana).
entidade.dispAmbiente.hint.quadra=Consulta todas as quadras na semana.
entidade.dispAmbiente.hint.salao=Consulta todas os sal\u00f5es na semana.
entidade.dispAmbiente.hint.semana=Consulta a pr\u00f3xima semana.
entidade.dispAmbiente.hint.semanaAtual=Consulta a semana atual do m\u00eas corrente.
entidade.duracaoMinimaHrs=Dura\u00e7\u00e3o (m\u00ednima)

entidade.data.atual=Data Atual

entidade.endereco=Endere\u00e7o
entidade.evento.data=Data do Evento
entidade.evento.nome=Nome do Evento
entidade.evento.tipo=Tipo do Evento
entidade.evento.situacao=Situa\u00e7\u00e3o do Evento
entidade.evento=Evento
entidade.evento.exibir=Exibir dados do Evento
entidade.exigencias=Exig\u00eancias
entidade.expira=Expira em
entidade.evento.perfil=Perfil

entidade.filtroPesquisa=Filtro de Pesquisa
entidade.final=Final
entidade.formaCalculo=Forma de C\u00e1lculo
entidade.formaContato=Forma de Contato
entidade.formasContato=Formas de Contato
entidade.formasPagamento=Formas de Pagamento

entidade.fornecedor.adicionarTercerizado=Adicionar Servi\u00e7o Tercerizado
entidade.fornecedor=Fornecedor

entidade.hint.ambiente=Ambiente que o cliente vai escolher para visitar. Necess\u00e1rio informar este campo para verificar a disponibilidade.
entidade.hint.atendente=Usu\u00e1rio logado no sistema que est\u00e1 realizando o contato com o cliente.
entidade.hint.abrirDetalhamento=Abre o detalhamento do evento selecionado na combo de eventos.
entidade.hint.dadosEvento=Exibe os dados b\u00e1sicos do evento selecionado na combo de eventos.
entidade.hint.dataContato=Data que o cliente est\u00e1 realizando o contato. 
entidade.hint.editar=Cadastro inicial
entidade.hint.evento=Selecione o evento que est\u00e1 relacionado com a conversa. Indispon\u00edvel se o cliente ainda n\u00e3o possuir um evento vinculado.
entidade.hint.gravarConversa=Salva a conversa com o cliente. 
entidade.hint.horaFinal=Pode ser alterado se o tipo de visita for em aberto. Selecione um tipo de visita para preencher esse campo.
entidade.hint.horaInicial=Usado somente para leitura. Selecione um tipo de visita para preencher esse campo.
entidade.hint.nomeCliente=Usado somente para leitura. Para mudar o nome do cliente v\u00e1 em cadastro inicial.
entidade.hint.telefone=Necess\u00e1rio informar pelo menos um n\u00famero de telefone com DDD de contato para salvar o cadastro, podendo ser Celular, Comercial ou Fixo. 
entidade.hint.tipoVisita=Selecione um tipo de visita. Ex -(Visita r\u00e1pida 15 min). Ap\u00f3s isso clique no link VERIFICAR DISPONIBILIDADE e escolha uma disponibilidade. 
entidade.hint.verificarDisponibilidade=Verificar Disponibilidade - Para realizar essa consulta \u00e9 necess\u00e1rio informar um ambiente. Ex - Piscina.
entidade.hint.verificarDisponibilidadeData=Verificar Disponibilidade - Para realizar essa consulta \u00e9 necess\u00e1rio informar um ambiente e uma data.

entidade.hint.perfilEvento.dataInicio=Campo obrigat\u00f3rio que deve ser maior ou igual a data atual.
entidade.hint.perfilEvento.ambiente=
entidade.hint.perfilEvento.bensConsumo=
entidade.hint.perfilEvento.brinquedos=
entidade.hint.perfilEvento.dadosBasicos=Para cadastrar os dados b\u00e1sicos \u00e9 necess\u00e1rio cadastrar o nome do perfil, ambiente e modelo de contrato.
entidade.hint.perfilEvento.exigencias=
entidade.hint.perfilEvento.modeloContrato=
entidade.hint.perfilEvento.modeloOrcamento=
entidade.hint.perfilEvento.servicos=
entidade.hint.perfilEvento.utensilios=

entidade.historico=Hist\u00f3rico
entidade.horario.final=Hor\u00e1rio Final
entidade.horario.inicial=Hor\u00e1rio Inicial
entidade.horario=Hor\u00e1rio

entidade.imagens=Imagens
entidade.inicial=Inicial
entidade.interessado.codigo=C\u00f3digo Interessado
entidade.interessado.email=E-mail
entidade.interessado.nome=Nome do Cliente
entidade.interessado.status.evento= Status do evento
entidade.interessado.status=Status
entidade.interessado.telefoneCelular=Telefone Celular
entidade.interessado.telefoneComercial=Telefone Comercial
entidade.interessado.telefoneFixo=Telefone Fixo

entidade.layouts=Layouts
entidade.layouts.tipoArquivo=O arquivo de layout deve ser do tipo imagem ou planilha (xls ou xlsx).

entidade.nao=N\u00e3o
entidade.nCheque=N\u00b0 do Cheque
entidade.negociacao.resultado=Resultado da Negocia\u00e7\u00e3o
entidade.negociacao.selecioneConvidados=Selecionar n\u00famero de convidados para calcular o valor
entidade.negociacao.total=Total da Negocia\u00e7\u00e3o
entidade.negociacao.credito.total=Total de Cr\u00e9dito
entidade.nome=Nome
entidade.nomeCompleto=Nome Completo
entidade.nrConv=Nr. de Convidados
entidade.nrEventos=N\u00ba de Eventos
entidade.nrMaxConv=N\u00famero m\u00e1ximo de convidados

entidade.obrigatorio=Obrigatorio
entidade.observacao=Observa\u00e7\u00e3o
entidade.observacaoOrcamento=Observa\u00e7\u00e3o do Or\u00e7amento
entidade.operadoraCartao=Operadora de Cart\u00e3o
entidade.orcamento.modelos=Modelos de Or\u00e7amento
entidade.orcamento.visualizarModelos=Visualizar Modelos de Or\u00e7amento
entidade.orcamentos=Or\u00e7amentos

entidade.pago=Pago
entidade.pagamento.responsavalPagamento=Respons\u00e1vel pelo Pagamento
entidade.parcelas=Parcelas
entidade.parcelasPagas=PARCELAS PAGAS
entidade.perfilEvento.ambienteEsc=Ambiente escolhido
entidade.perfilEvento.chequeCaucao=Cheque Cau\u00e7\u00e3o
entidade.perfilEvento.dadosBasicos=Dados b\u00e1sicos
entidade.perfilEvento.detalharEvento= Detalhar Evento
entidade.perfilEvento.exigeChequeCaucao=Exige Cheque Cau\u00e7\u00e3o
entidade.perfilEvento.exigeUploadContratoAssinado=Exige upload do contrato assinado
entidade.perfilEvento.nome=Nome do perfil
entidade.perfilEvento.pagamentoPrevio=Pagamento pr\u00e9vio (%)
entidade.perfilEvento.permiteOutrosBens=Permite outros Bens de Consumo?
entidade.perfilEvento.permiteOutrosBrinq=Permite outros Brinquedos?
entidade.perfilEvento.permiteOutrosSer=Permite outros Servi\u00e7os?
entidade.perfilEvento.permiteOutrosUten=Permite outros Utens\u00edlios?
entidade.perfilEvento.produtoPadrao=Produto Padr\u00e3o
entidade.perfilEvento.textoPadrao=Texto padr\u00e3o
entidade.perfilEvento.valorChequeCaucao=Valor do cheque cau\u00e7\u00e3o
entidade.perfilEvento=Perfil do Evento
entidade.perfilEventoInteresse= Perfil de Interesse
entidade.perfilEventoAmbiente.addLayout=Adicionar Layouts
entidade.perfilEventoAmbiente.addSazon=Adicionar Sazonalidades
entidade.perfilEventoAmbiente.sazonalidades=Sazonalidades
entidade.perfilEventoAmbiente.visualizar=Visualizar Ambientes
entidade.peridoVencimentoParcela=Per\u00edodo de Vencimento da Parcela 
entidade.pessoa.codigo=C\u00f3digo Pessoa
entidade.pessoa.email=E-mail
entidade.pessoa.nomeCompleto=Nome Completo
entidade.produto.cadastrar=Cadastrar Produto
entidade.produto.estoque=Estoque
entidade.produto.locacao=Produtos
entidade.produto=Descri\u00e7\u00e3o do Produto
entidade.produto=Produto
entidade.produtoLocacao.minimoEstoque=M\u00ednimo no Estoque
entidade.produtoLocacao.descontoProduto=Aplicar desconto em produto
entidade.produtoLocacao.rastreado=Produto Rastreado
entidade.produtoLocacao.rastreadoAbv=Rastreado
entidade.produtoLocacaoPatrimonio=Patrim\u00f4nio
entidade.produtoLocacaoRastreamento.horaFinal=Hora Final
entidade.produtoLocacaoRastreamento.horaInicial=Hora Inicial
entidade.produtoLocacaoRastreamento.pessoa=Pessoa Respons\u00e1vel
entidade.produtoLocacaoRastreamento=Rastreamento
entidade.produtos.consulta=Consulta de Produtos
entidade.produtos=Produtos
entidade.prospect.urgente=Realizar Contato com Urg\u00eancia
entidade.prospect.breve=Realizar Contato em breve
entidade.prospect.dataExpirada=Data de contato expirada
entidade.prospect.semUrgencia=Sem urg\u00eancia
entidade.provaConceito.verdadeiro= Verdadeiro
entidade.provaConceito_Simonides= Prova de Conceito Simonides
entidade.provaConceito= Prova de Conceito

entidade.qtdMaximaReservasDia=Reservas/dia (M\u00e1ximo)
entidade.quantidade.maxima=Quantidade M\u00e1xima
entidade.quantidade.minima=Quantidade M\u00ednima
entidade.quantidade=Quantidade
entidade.quemE=Quem \u00e9 o cliente no evento

entidade.reserva.responsavel=Respons\u00e1vel
entidade.registradoPor=Registrado por

entidade.saldoDisponivel=Saldo Dispon\u00edvel de
entidade.semana=Semana
entidade.servico=Servi\u00e7o
entidade.servico=Servi\u00e7o
entidade.servicoTercerizado=Servi\u00e7o Terceirizado
entidade.servicosTercerizado.consulta=Consulta Servi\u00e7o Terceirizado
entidade.servicos.consulta=Consulta de Servi\u00e7o
entidade.servicos=Servi\u00e7os
entidade.sim=Sim
entidade.situacao=Situa\u00e7\u00e3o
entidade.subTotal=Subtotal


entidade.tel=Telefones
entidade.telComercial=Telefone Comercial
entidade.telefone=Telefone
entidade.telFixo=Telefone Resid\u00eancial
entidade.tempoAdicionalPosteriorMin=Adicional
entidade.textoLivre=Texto Livre
entidade.textoPredefinido=Texto Pr\u00e9-definido
entidade.tipo=Tipo
entidade.tipoLayout=Tipo de Layout
entidade.tipoOperacao=Tipo de Opera\u00e7\u00e3o
entidade.total=Total
entidade.totalLancado=Total Lan\u00e7ado

entidade.utensilio=Utens\u00edlio
entidade.utensilios.consulta=Consulta de Utens\u00edlios
entidade.utensilios=Utens\u00edlios

entidade.valor=Valor
entidade.valorMensal=Valor Mensal
entidade.vencimento=Vencimento
entidade.visualizar=Visualizar
entidade.valorAtualEvento=Valor Atual do Evento 
entidade.valorInicialEvento=Valor Inicial do Evento

mensagem.salvarParcelas=Deseja salvar as novas parcelas?

menu.cadastros.acessoSistema.controleLog=Controle de Log
menu.cadastros.acessoSistema.empresa=Empresa
menu.cadastros.acessoSistema.perfisAcesso=Perfis de Acesso
menu.cadastros.acessoSistema.usuario=Usu\u00e1rio
menu.cadastros.acessoSistema=Acesso ao Sistema
menu.cadastros.ambiente.tipo=Tipo de Ambiente
menu.cadastros.boletinsVisita.perguntas=Perguntas
menu.cadastros.boletinsVisita.questionarios=Question\u00e1rios
menu.cadastros.boletinsVisita=Boletins de Visita
menu.cadastros.cadastrosAuxiliares=Cadastros Auxiliares
menu.cadastros.categoriaCliente=Categoria de Cliente
menu.cadastros.cidade=Cidade
menu.cadastros.cliente=Cliente
menu.cadastros.colaborador=Colaborador
menu.cadastros.pessoa=Pessoa
menu.cadastros.confFinanceiras.banco=Banco
menu.cadastros.confFinanceiras.estornoPag=Estorno de Pagamento
menu.cadastros.confFinanceiras.formaPag=Forma de Pagamento
menu.cadastros.confFinanceiras.movContaCor=Movimento de Conta Corrente do Cliente
menu.cadastros.confFinanceiras.movPagamento=Movimento do Pagamento
menu.cadastros.confFinanceiras.movParcela=Movimento da Parcela
menu.cadastros.confFinanceiras.movProduto=Movimento de Produto
menu.cadastros.confFinanceiras.operCartaoCrdt=Operadora de Cart\u00e3o
menu.cadastros.confFinanceiras=Conf. Financeiras
menu.cadastros.configContrato.justOperacao=Justificativa de Opera\u00e7\u00e3o
menu.cadastros.configContrato.modelo=Modelo de Contrato
menu.cadastros.configContrato.tabelaFeriados=Tabela de Feriados
menu.cadastros.configContrato=Config. de Contrato
menu.cadastros.grauInstrucao=Grau de Instru\u00e7\u00e3o
menu.cadastros.pais=Pa\u00eds
menu.cadastros.perfisEventos.ambiente=Ambiente
menu.cadastros.perfisEventos.descontos=Desconto
menu.cadastros.perfisEventos.removerDescontos=Remover desconto
menu.cadastros.perfisEventos.disponibilidadeAmb=Disponibilidade de Ambiente
menu.cadastros.perfisEventos.fechamentoNegociacao=Fechamento de Negocia\u00e7\u00e3o
menu.cadastros.perfisEventos.tabelaPrecos=Tabela de Pre\u00e7os / Perfil Evento
menu.cadastros.perfisEventos.tipoAmbiente=Tipo Ambiente
menu.cadastros.perfisEventos=Perfis de Eventos
menu.cadastros.produto.finalVigencia=Data Final de Vig\u00eancia
menu.cadastros.produtos.fornecedor= Fornecedor
menu.cadastros.produto.inicioVigencia=Data In\u00edcio de Vig\u00eancia
menu.cadastros.produto.nrDiasVigencia=N\u00famero de dias Vig\u00eancia
menu.cadastros.produto.tipo=Tipo do Produto
menu.cadastros.produtoLocacao=Produtos
menu.cadastros.produtos.categoria=Categoria de Produto
menu.cadastros.produtos.produto=Produto
menu.cadastros.produtos.produtosLocacao=Produtos
menu.cadastros.produtos.servicos = Servi\u00e7os
menu.cadastros.produtos=Produtos
menu.cadastros.produtosPerfis=Produtos
menu.cadastros.profissao=Profiss\u00e3o
menu.cadastros.prospect=Prospect
menu.cadastros.provaConceito=Prova de Conceito
menu.cadastros.secao=Se\u00e7\u00e3o

menu.consulta.relatorios=Relat\u00f3rios
menu.consulta.relatorios.clientesAniversariantes=Clientes Aniversariante
menu.consulta.relatorios.fechamentoCaixa=Fechamento de Caixa
menu.consulta.relatorios.faturamentoPeriodo=Faturamento Per\u00edodo
menu.consulta.relatorios.receitaPeriodo=Receita por Per\u00edodo
menu.consulta.relatorios.parcelasAberto=Parcelas em Aberto
menu.consulta.relatorios.parcelasPagas=Parcelas quitadas
menu.consulta.relatorios.saldoContaCorrenteCliente=Saldo Conta Corrente Cliente

menu.consulta.financeiro=Financeiro
menu.consulta.financeiro.estornoRecibo=Estorno de Recibo
menu.consulta.financeiro.movimentoContaCorrenteCliente=Movimento Conta Corrente do Cliente
menu.consulta.financeiro.formasPagamento=Formas de Pagamento
menu.consulta.financeiro.operadorasCartao=Operadoras de Cart\u00e3o

menu.consulta.Log=Visualizar Log
menu.caixaAberto=Caixa em aberto

menu.operacoesCE.acoes=A\u00e7\u00f5es
menu.operacoesCE.agendaVisita=Agenda Visita
menu.operacoesCE.alterarNomeInteressado=Alterar Nome
menu.operacoesCE.cancelarVisita=Cancelar Visita
menu.operacoesCE.confirmarVisita=Confirmar Visita
menu.operacoesCE.remarcarVisita=Remarcar Visita
menu.operacoesCE.confirmacaocancelarVisita=Confirma\u00e7\u00e3o Cancelar Visita
menu.operacoesCE.definirNovaDataVisita=Definir Nova Data Visita
menu.operacoesCE.Alterar=Altera\u00e7\u00e3o da Negocia\u00e7\u00e3o
menu.operacoesCE.cadastrarEvento=Cadastrar Evento
menu.operacoesCE.cadastroConversa=Cadastro de Conversas 
menu.operacoesCE.cadastroInicial=Cadastro Inicial
menu.operacoesCE.cadastroInicialDetalhado=Detalhamento de Evento
menu.operacoesCE.confirmacaoEvento=Confirma\u00e7\u00e3o / Pagamento
menu.operacoesCE.confirmacaoPagamento.contrato=Confirma\u00e7\u00e3o do Fechamento do Contrato
menu.operacoesCE.confirmacaoPagamento.chequeCaucao=Confirma\u00e7\u00e3o do Pagamento de Cheque Cau\u00e7\u00e3o
menu.operacoesCE.dadosEvento=Dados do Evento
menu.operacoesCE.descartarOrcamento=Descartar Or\u00e7amento
menu.operacoesCE.descricaoContrato=Descri\u00e7\u00e3o do Contrato
menu.operacoesCE.descricaoOrcamento= Descri\u00e7\u00e3o do Or\u00e7amento
menu.operacoesCE.detalheCliente=Detalhe do Cliente
menu.operacoesCE.detalheEvento=Detalhe do Evento
menu.operacoesCE.disponibilidade=Agenda de Eventos
menu.operacoesCE.downloadLayout=Download Layout
menu.operacoesCE.emissaoDocumentos=Emiss\u00e3o de Documentos
menu.operacoesCE.exibicaoContrato=Exibi\u00e7\u00e3o de Contrato
menu.operacoesCE.exibicaoOrcamento=Exibi\u00e7\u00e3o de Or\u00e7amento
menu.operacoesCE.listaProspects=Lista de Prospects
menu.operacoesCE.mensagens=Mensagens
menu.operacoesCE.operacoes.agendarContato=Agendar Contato
menu.operacoesCE.operacoes.cancelamento=Cancelamento
menu.operacoesCE.operacoes.cancelamentoEvento=Cancelamento de Evento
menu.operacoesCE.operacoes.confirmacao=Confirma\u00e7\u00e3o / Pagamento
menu.operacoesCE.operacoes.encerramento=Encerramento
menu.operacoesCE.operacoes.orcamento=Or\u00e7amento
menu.operacoesCE.operacoes.preReserva=Pr\u00e9-reserva
menu.operacoesCE.operacoes.reservas=Reservas confirmadas
menu.operacoesCE.operacoes.salvar=Salvar
menu.operacoesCE.operacoes.simularOrcamento=Simular Or\u00e7amento
menu.operacoesCE.operacoes=Opera\u00e7\u00f5es
menu.operacoesCE.orcamentoDetalhado=Or\u00e7amento Detalhado
menu.operacoesCE.pagamentos=Pagamentos
menu.operacoesCE.pesquisa=Pesquisa Geral
menu.operacoesCE.provaConceito= Prova de Conceito
menu.operacoesCE.quantidadesValidas=As quantidades v\u00e1lidas s\u00e3o
menu.operacoesCE.valorAlterado=Alterado automaticamente por n\u00e3o estar inserido em nenhuma faixa de quantidade definida no perfil do evento.
menu.operacoesCE.verParcelas=Ver Parcelas  
menu.operacoesCE=Eventos
menu.outrosDados=Outros dados
menu.operacoesCE.relatorios=Relat\u00f3rios
menu.operacoesCE.financeiro=Financeiro

operacoes.adicionar.ambiente=Adicionar ambiente
operacoes.adicionar.bemConsumo=Adicionar Bem de Consumo
operacoes.adicionar.brinquedo=Adicionar Brinquedo
operacoes.adicionar.patrimonio=Adicionar Patrim\u00f4nio
operacoes.adicionar.proximoContato=Adicionar Pr\u00f3ximo Contato
operacoes.adicionar.utensilio=Adicionar Utens\u00edlio
operacoes.adicionar=Adicionar
operacoes.alterarNegociacao=AlterarNegociacao
operacoes.alterarNegociacao.alterar=Alterar parcelas existentes
operacoes.alterarNegociacao.dataPrimeira=Data da nova parcela (primeira)
operacoes.alterarNegociacao.gerar=Gerar novas parcelas
operacoes.alterarNegociacao.qtdAtualParcelas=Quantidade de novas parcelas
operacoes.alterarNegociacao.qtdInicialParcelas=Quantidade inicial parcelas

operacoes.quitarCaucaoECredito.caucaoDevolvido=Cau\u00e7\u00e3o devolvido.
operacoes.quitarCaucaoECredito.usadoParaQuitarParcelas=Usado para quitar parcelas.
operacoes.quitarCaucaoECredito.depositadoCreditoCliente=Depositado em Credito para o Cliente.

operacoes.atualizar=Atualizar
operacoes.atualizarPreReserva=Atualizar Pr\u00e9-Reserva
operacoes.atualizarProxContato=Agendar Contato
operacoes.buscar=Buscar


operacoes.cadastro.terminar=Terminar Cadastro
operacoes.cadastro.editar=Editar Cadastro
operacoes.cadastro.encerrarEvento=Encerrar Evento
operacoes.cancelar=Cancelar
operacoes.cancelar.texto=Encerrar Evento
operacoes.cacaoecredito.texto=Cau\u00e7\u00e3o e Cr\u00e9dito
operacoes.cacaoecredito.quitacao.caucao.texto=Quita\u00e7\u00e3o de Cau\u00e7\u00e3o
operacoes.cacaoecredito.quitacao.credito.texto=Quita\u00e7\u00e3o de Cr\u00e9dito
operacoes.titulo.autorizarEvento=Autorizar Evento
operacoes.texto.explicativo.autorizarEvento=Para autorizar o evento \u00e9 necess\u00e1rio contato com cliente e realizar as seguintes tarefas abaixo:<br>1. Confirmar a quantidade de convidados;<br>2. Mudar (definir) o hor\u00e1rio do evento;<br>3. Definir a quantidade de bebidas;<br>4. Definir os utens\u00edlios necess\u00e1rios;<br>5. Definir os servi\u00e7os necess\u00e1rios;<br>6. Confirma\u00e7\u00e3o do Cr\u00e9dito de bebidas.
operacoes.carregar=Carregar
operacoes.cartaoCredito=Cart\u00e3o de Cr\u00e9dito
operacoes.concluido=Conclu\u00eddo
operacoes.confirmar=Confirmar
operacoes.consulta.cadastrosEventos=Cadastros
operacoes.consulta.consultar=Consultar
operacoes.consulta.consultarAmbiente=Consultar Ambiente
operacoes.consulta.consultarDados=Consultar Dados
operacoes.consulta.consultarPor=Consultar por
operacoes.consulta.consultarTodasSemana=Consultar Todos da Semana
operacoes.consulta.consultarTodosHoje=Consultar Todos de Hoje
operacoes.consulta.filtros=Filtros
operacoes.consulta.filtrosPesquisa=Filtros da Pesquisa
operacoes.consulta.legenda=Legenda
operacoes.consulta.pagina=P\u00e1gina
operacoes.consulta=Consulta
operacoes.consultaCliente=Informe um filtro para a pesquisa
operacoes.contrato.imagens.remover=Remover Imagens
operacoes.contrato.modelo.adicionar=Adicionar Modelo de Contrato
operacoes.contrato.remover=Remover Contrato
operacoes.conversas=Conversas

operacoes.dinheiro=Dinheiro
operacoes.disponibilidade.verificar=Verificar Disponibilidade
operacoes.editar.confirmar=Confirmar Altera\u00e7\u00e3o
operacoes.editar.editarDados=Editar Dados
operacoes.editar.valor.bemConsumo=Alterar Valor do Bem de Consumo
operacoes.editar.valor.brinquedo=Alterar Valor do Brinquedo
operacoes.editar.valor.novo.aplicar=Aplicar Novo Valor
operacoes.editar.valor.servico=Alterar Valor do Servi\u00e7o
operacoes.editar.valor.utensilio=Alterar Valor do Utens\u00edlio
operacoes.editar=Editar
operacoes.estaContido= est\u00e1 contido nos perfis 
operacoes.eventoEspecifico=Evento Espec\u00edfico
operacoes.excluir.dados=Excluir Dados
operacoes.excluir=Excluir
operacoes.excluir.contato=Excluir contato
operacoes.exibirLayout=Exibir Layout

operacoes.formatoTelefone=Formato do telefone deve ser (XX), (XX)XXXX, (XX)XXXXXXXX ou (XX)XXXXXXXXX.

operacoes.gerarData=Gerar Data
operacoes.gravar.dados=Gravar Dados
operacoes.gravar=Gravar

operacoes.ajuda=Ajuda

operacoes.horario.selecione=Selecione algum Hor\u00e1rio

operacoes.imprimir=Imprimir
operacoes.incluir.incluirNovo=Novo
operacoes.incluir=Incluir Novos Dados

operacoes.layout.remover=Remover Layout
operacoes.limpar=Limpar
operacoes.limparBusca=Limpar Busca

operacoes.modelo.orcamento.remover=Remover Modelo de Or\u00e7amento

operacoes.naoAgendado=N\u00e3o agendado.
operacoes.naoEstaContido=n\u00e3o est\u00e1 contido em um Perfil de Evento.
operacoes.naoExistemParcelas=Nenhuma parcela est\u00e1 relacionada ao evento.
operacoes.negociacao.contrato=Exibir Contrato
operacoes.negociacao.escolherPerfil=Escolha o Perfil do Evento
operacoes.negociacao.exibir=Exibir Or\u00e7amento
operacoes.negociacao.fechar=Fechar Negocia\u00e7\u00e3o
operacoes.negociacao.perfil=Perfil do Evento
operacoes.negociacao.preReserva=Pr\u00e9-Reserva
operacoes.negociacao.salvar=Salvar Negocia\u00e7\u00e3o
operacoes.negociacao.salvarReserva=Salvar com Reserva
operacoes.negociacao.salvarSemReserva=Salvar sem Reserva
operacoes.novo=Os dados do cadastro ser\u00e3o perdidos. Confirmar? 
operacoes.negociacao.quitar.credito.caucao=quita\u00e7\u00e3o
operacoes.oAmbiente=O Ambiente
operacoes.obrigatorio.condicaoPagamento=Selecione uma condi\u00e7\u00e3o de pagamento. 
operacoes.opcoes=Op\u00e7\u00f5es


operacoes.parar=Parar
operacoes.parcelasRelacionadas=Parcelas relacionadas ao evento
operacoes.perfilEvento.adionarProduto=Os dados editados do perfil ser\u00e3o perdidos. Deseja continuar?

operacoes.receber=Receber
operacoes.receberChequeCaucao=Receber Cheque Cau\u00e7\u00e3o
operacoes.remover.todos=Remover Todos
operacoes.remover=Remover
operacoes.residuo=Res\u00edduo

operacoes.selecionar.selecionarDado=Selecionar Dado
operacoes.selecionar=Selecionar
operacoes.selecione=--Selecione--

operacoes.todos=--Todos--
operacoes.totalizacao=Totaliza\u00e7\u00e3o
operacoes.totalResultados=Total de Resultados

operacoes.voltar=Voltar
operacoes.voltarOrcamento=Editar Or\u00e7amento

entidade.credito=Cr\u00e9dito

entidade.valorPago=Valor que j\u00e1 foi pago
