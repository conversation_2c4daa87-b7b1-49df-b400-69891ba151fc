operacoes.adicao.sucesso=Dados Adicionados com Sucesso

operacoes.adicao.erro.ambienteJaRelacionado=O Ambiente selecionado já foi adicionado
operacoes.adicao.erro.servicoJaRelacionado=O Serviço escolhido já está cadastrado no Perfil de Evento
operacoes.adicao.erro.servicoObrigatorio=Algum Serviço deve ser informado
operacoes.adicao.erro.bemConsumoJaRelacionado=O Bem de Consumo escolhido já está cadastrado no Perfil de Evento
operacoes.adicao.erro.bemConsumoObrigatorio=Algum Bem de Consumo deve ser informado
operacoes.adicao.erro.utensilioJaRelacionado=O Utensílio escolhido já está cadastrado no Perfil de Evento
operacoes.adicao.erro.utensilioObrigatorio=Algum Utensílio deve ser informado
operacoes.adicao.erro.brinquedoJaRelacionado=O Brinquedo escolhido já está cadastrado no Perfil de Evento
operacoes.adicao.erro.brinquedoObrigatorio=Algum Brinquedo deve ser informado
operacoes.adicao.erro.sazonalidadeJaRelacionada=A Sazonalidade informada já foi adicionada ao Ambiente

operacoes.consulta.sucesso=Dados Consultados com Sucesso

operacoes.edicao.dadosProntos=Dados Prontos para Edição
operacoes.edicao.sucesso=Dados Alterados com Sucesso

operacoes.erro=Não Foi Possível Realizar esta Operação

operacoes.exclusao.confirmar=Confirma exclusão dos dados?
operacoes.exclusao.sucesso=Dados Excluídos com Sucesso

operacoes.salvar.sucesso=Dados salvos com Sucesso

dados.informar=Informe os Dados
dados.informar.parametros=Informe os Parâmetros para a Consulta