<html lang="pt"
      xmlns="http://www.w3.org/1999/xhtml"
      xmlns:th="http://www.thymeleaf.org">
<div style="width: 400px">

    <span th:utext="'Enviado às: ' + ${dataHoraRelatorio}">Enviado &agrave;s: dd/mm/YYYY hh:mm:yyyy</span>

    <div style="text-align:center;
            font-family: Arial;
            margin-top: 10px;
            color: #777777;
            font-size: 0.95em;">
        Isto &eacute; um alerta autom&aacute;tico, n&atilde;o &eacute; necess&aacute;rio respond&ecirc;-lo.
    </div>

    <table id="table_container" width="100%" border="0" cellpadding="0" cellspacing="0"
           style="font-family: Arial;
                  color: #333;">
        <tbody>

        <tr>
            <td style="color: #777777;
                   padding-top: 15px;">
                <div th:utext="'Unidade: <b>' + ${nomeFantasiaEmpresa}"
                     style="text-align: left; margin-left: 20px;">
                    Unidade: <b>Empresa XXXX - yyyyyyyy</b>
                </div>

            <td>
        </tr>
        <tr>
            <td style="color: #777777;
                   padding-top: 5px;">
                <div th:utext="'Período: <b>' + ${periodoLimiteInferiorString} + '</b> a <br> <b>' + ${periodoLimiteSuperiorString} + '</b>'"
                     style="text-align: left; margin-left: 20px;">
                    Per&iacute;odo: <b>dd/mm/YYYY hh:mm:yyyy</b> a <br> <b>dd/mm/YYYY hh:mm:yyyy</b>
                </div>

            <td>
        </tr>

        <tr>
            <td>
                <div id="div-content"
                     style="width: 97%;
                            padding-top: 5px;">
                    <ul th:if="${quantidadeNFCeCancelada} or ${quantidadeNFCeNaoAutorizada}">
                        <li><b>NFCe</b> - Nota Fiscal do Consumidor Eletr&ocirc;nica
                            <ul>
                                <li th:utext="'<b>' + (${quantidadeNFCeCancelada} ?: 0) + '</b> nota(s) com o status <b>Cancelado</b>.'">X1 nota(s) com o status <b>Cancelado</b></li>
                                <li th:utext="'<b>' + (${quantidadeNFCeNaoAutorizada} ?: 0) + '</b> nota(s) com o status <b>Não autorizado</b>.'">Y1 nota(s) com o status <b>Não autorizado</b></li>
                            </ul>
                        </li>
                    </ul>

                    <hr th:if="(${quantidadeNFCeCancelada} or ${quantidadeNFCeNaoAutorizada}) and (${quantidadeNFSeCancelada} or ${quantidadeNFSeNaoAutorizada})" style="width:60%;">

                    <ul th:if="${quantidadeNFSeCancelada} or ${quantidadeNFSeNaoAutorizada}">
                        <li><b>NFSe</b> - Nota Fiscal de Serviços Eletrônica
                            <ul>
                                <li th:utext="'<b>' + (${quantidadeNFSeCancelada} ?: 0) + '</b> nota(s) com o status <b>Cancelado</b>.'">X2 nota(s) com o status <b>Cancelado</b></li>
                                <li th:utext="'<b>' + (${quantidadeNFSeNaoAutorizada} ?: 0) + '</b> nota(s) com o status <b>Não autorizado</b>.'">Y2 nota(s) com o status <b>N&atilde;o autorizado</b></li>
                            </ul>
                        </li>
                    </ul>
                </div>
            </td>
        </tr>

        </tbody>
    </table>

    <div id="text-mais-informacoes"
         th:if="${!eNotas}"
         style="font-size: 0.9em;
            margin-top: 10px;
            text-align:center;">
        Para mais informa&ccedil;&otilde;es, acesse o <a th:href="'http://nfe2-web.pactosolucoes.com.br:8030/nfe/faces/inicio.jsp?key=' + ${chaveModuloNFe}" target="_blank"><b>M&oacute;dulo de notas</b></a>
    </div>
</div>
</html>
