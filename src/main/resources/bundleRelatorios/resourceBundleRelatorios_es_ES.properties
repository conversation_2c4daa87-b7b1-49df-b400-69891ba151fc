#*************Recibo impressora Termica
DT_Ent_Caixa = FC Ent Caja
Matricula = Registro
Nome_Responsavel = Nombre del responsable
Nome_Responsavel_Emissao = Responsable de emitir
Prod_do_Recibo = Productos del recibo
Pacote = Paquete
Unitario = Unitario
Qtd = Ctd
Desc = Desc
Valor_Pago = Valor Pago
Desconto = Descuento
Descontos = Descuentos
Pag_Recibos = Pg del recibo
Recebemos_de = *Recibimos de
A_quantia_de = , la cantidad de
Proveniente_dos = refiere a los itens arriba descritos.
Resp_Recebimento = Resp. Recibimiento:
Cons.Resp = Cons.Resp.:
Cliente = Cliente:
Dt_impressao = Fc. impressión:
Dt_pagamento = Fc del pago:
Dt_alteracao = Fc del cambio:
Responsavel = Responsable:
Inicio = Inicio:
Termino = Fin:
Parcelas_Recibo = Cuotas del Recibo
Obs = Obs.:
Autorizacao = Autorización:
#***************Fim Recibo Impressora Termica

#***************Recibo Caixa Por Operador Rel
Usuario = Usuario
Pagina = Pagina
Recibo = Recibo
Nome_Resp_Pag = Nombre del responsable por el pago
Contrato = Contracto
Nome_Aluno = Nombre del Alumno
Produto = Producto
Data_Pagamento = Fecha del Pago
Forma_Pg = Forma del Pg
Devolucao = Devolución
Resp_Receb = Resp Recebimiento:
Resp_Devolucao = Respo Devolución:
Consultor_Resp = Consultor responsable:
Calculo_Devolucao = Calculo Devolución:
Produtos_Recibo = Productos del Recibo
Desc_Extra_Convenio = Descuento Extra Convenio
Valor_Devolvido = Valor Devuelto
Cheques_Devolvidos = Cheque Devuelto
Agencia = Sucursal
Conta = Cuenta
Numero = Numero
DT_Devolucoes = Fecha Devolución
Dev_Cheques_Edicao_PG = Devolucion de cheque en la ediccion del pago
Calculo_Cancelamento = Cálculo del Cancelamiento
Cartoes_Estornados = Tarjetas Reembolsadas
Valor = Valor
Especie = Especie
Titulo = Cierre del Caja por Operador
Horario = Horario:
Ate = hasta
Operador = Operador:
Tipo_Comp_Todos = Tipo del Comprador: Todo
Tipo_Comp_Cliente = Tipo del Comprador: Cliente
Tipo_Comp_Colaborador = Tipo del Comprador: Colaborador
Tipo_Comp_Consumidor = Tipo del Comprador: Consumidor
MaiorQue = más grande que
MenorQue = menos que
FaturamentoProduto = Facturación de productos


#***************Fim Recibo Caixa Por Operador Rel
