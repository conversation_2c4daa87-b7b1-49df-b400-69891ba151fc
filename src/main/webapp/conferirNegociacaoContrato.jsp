<%--
  Created by IntelliJ IDEA.
  User: Rafael
  Date: 20/04/2016
  Time: 08:54
  To change this template use File | Settings | File Templates.
--%>
<%@page contentType="text/html"%>
<%@page pageEncoding="ISO-8859-1"%>
<head>
    <script type="text/javascript" language="javascript" src="hoverform.js"></script>
    <script type="text/javascript" language="javascript" src="script/negociacaoContrato_1.0.min.js"></script>
    <script type="text/javascript" language="javascript" src="script/gobackblock.js"></script>
    <link href="css/css_contrato.1.0.css" rel="stylesheet" type="text/css"/>
    <link href="./css/tooltipster/tooltipster.css" rel="stylesheet" type="text/css"/>
    <link href="./css/tooltipster/tooltipster-light.css" rel="stylesheet" type="text/css"/>
    <script type="text/javascript" language="javascript" src="bootstrap/jquery.js"></script>
    <script type="text/javascript" src="script/tooltipster/jquery.tooltipster.min.js"></script>
    <script type="text/javascript" src="script/jquery.maskedinput-1.7.6.js"></script>
</head>

<script>
    function getPlaceHolderVitio() {
        if(document.getElementById('formVitio:nomeCliente')) {
            document.getElementById('formVitio:nomeCliente').setAttribute("placeholder", "Informe o nome");
        }
        if(document.getElementById('formVitio:emailCliente')) {
            document.getElementById('formVitio:emailCliente').setAttribute("placeholder", "Informe o e-mail");
        }
        if(document.getElementById('formVitio:telefoneCliente')) {
            document.getElementById('formVitio:telefoneCliente').setAttribute("placeholder", "Informe o telefone");
        }
    }

    function copiar(copyText) {
        var el = document.createElement('textarea');
        el.value = copyText;
        document.body.appendChild(el);
        el.select();
        document.execCommand('copy');
        document.body.removeChild(el);
        Notifier.info('Link gerado com sucesso e copiado para a área de transferência! O link expira em 5 dias.');
    }
    jQuery.noConflict();
</script>
<%@taglib prefix="f" uri="http://java.sun.com/jsf/core"%>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html"%>
<%@taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions"%>

<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax"%>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich"%>

<f:loadBundle var="msg_bt" basename="propriedades.Botoes"/>
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao"/>
<f:loadBundle var="msg" basename="propriedades.Mensagens"/>

<body class="paginaFontResponsiva">
<f:view>
    <jsp:include page="includes/include_carregando_ripple.jsp" flush="true"/>
    <a4j:loadScript src="/script/jquery.maskedinput-1.2.2.js" />
    <jsp:include page="/includes/include_modal_usuarioSenhaTipoContrato.jsp" flush="true"/>
  <title>
    <h:outputText value="Fechamento da Negociação"/>
  </title>

  <head>
    <title><h:outputText value="#{msg_aplic.prt_Cliente_tituloForm}" /></title>
  </head>

  <h:form id="form">
  <html>
    <jsp:include page="include_head.jsp" flush="true" />
    <body>
    <h:panelGroup layout="block" style="display: inline-table;width: 100%;" styleClass="fundoCinza">
      <h:panelGroup layout="block" styleClass="bgtop topoZW" rendered="#{MenuControle.apresentarTopo}">
        <jsp:include page="include_topo_novo.jsp" flush="true"/>
        <jsp:include page="include_menu_zw_flat.jsp" flush="true"/>
      </h:panelGroup>

      <h:panelGroup layout="block" styleClass="caixaCorpo">
        <h:panelGroup layout="block" style="height: 80%;width: 100%">
          <h:panelGroup layout="block" styleClass="caixaMenuLatel">
            <h:panelGroup layout="block" styleClass="container-imagem container-conteudo-central" style="position:relative;">

              <h:panelGroup layout="block" styleClass="container-box zw_ui especial ">
                <h:panelGroup layout="block" styleClass="container-box-header">
                  <h:panelGroup layout="block" styleClass="margin-box">
                    <h:outputText styleClass="container-header-titulo"
                                  value="Fechamento Negociação"/>
                  </h:panelGroup>
                </h:panelGroup>
                <h:panelGroup layout="block" styleClass="container-box-header bg-cinza">
                      <h:panelGroup id="panelVigenciaContrato" layout="block" styleClass="margin-container">
                          <h:outputText
                                  styleClass="tituloLabel texto-font texto-bold texto-cor-cinza texto-size-14"
                                  value="PERÍODO CONTRATO"/>
                          <h:outputText
                                  styleClass="valorCampos texto-cor-cinza texto-font texto-size-16"
                                  value="#{ContratoControle.contratoVO.vigenciaDe_ApresentarTela7}"/>
                          <h:panelGroup layout="block" styleClass="timeLineSmall"/>
                          <h:outputText
                                  styleClass="valorCampos texto-cor-cinza texto-font texto-size-16"
                                  value="#{ContratoControle.contratoVO.vigenciaAteAjustada_Apresentar}"/>

                          <h:panelGroup>
                          <a4j:commandLink rendered="#{ContratoControle.apresentarHintCalculoMesSeguinte}" styleClass="linkPadrao">
                              <i title="Pode acontecer da data final não ser exatamente o 'dia anterior do mês seguinte'. Para mais informações consulte o Wiki ao lado." class="tooltipster fa-icon-question-sign texto-cor-azul texto-size-16"></i>
                          </a4j:commandLink>

                          <h:outputLink styleClass="linkWiki " rendered="#{ContratoControle.apresentarHintCalculoMesSeguinte}"
                                        value="#{SuperControle.urlBaseConhecimento}tela-do-cliente-modulo-adm/"
                                        title="Clique e saiba mais: Datas do contrato durante a negociação" target="_blank">
                              <i class="fa-icon-question-sign" style="font-size: 18px"></i>
                          </h:outputLink>
                          </h:panelGroup>
                      </h:panelGroup>
                </h:panelGroup>
                <h:panelGroup styleClass="tabelasSemHover" layout="block" >
                    <h:panelGroup layout="block" styleClass="containerConfNegociacao margin-container" style="display: inline-block">
                        <table  height="100%" width="100%" border="0" cellpadding="0" cellspacing="0" style="font-size: 1vw;">

                    <tr>
                      <td width="57%" align="left" valign="top" style="padding:0 10px 10px 0;">
                        <table width="100%" border="0" cellspacing="0" cellpadding="0" >
                          <tr>
                            <td align="left" valign="top">
                              <table width="100%" class="tabelaSimplesCustom" border="0" cellspacing="0" cellpadding="0" style="margin-bottom:25px;">
                               <tbody>
                                <tr>
                                  <td>
                                      <b><h:outputText styleClass="rotuloCampos"  value="INÍCIO CONTRATO " /></b>
                                  </td>
                                  <td height="35" align="left" valign="middle">
                                    <h:outputText styleClass="texto-size-14 texto-cor-cinza texto-font" value="#{ContratoControle.contratoVO.vigenciaDe_ApresentarTela7}" />
                                  </td>
                                </tr>
                                <tr>
                                  <td>
                                    <b><h:outputText styleClass="rotuloCampos"  value="DATA TÉRMINO " /></b>

                                  </td>
                                    <td height="27" align="left" valign="middle">
                                        <h:outputText id="vigencia" styleClass="texto-size-14 texto-cor-cinza texto-font" value="#{ContratoControle.contratoVO.vigenciaAteAjustada_Apresentar}" />
                                    </td>
                                </tr>
                                <tr>
                                    <td>
                                        <b><h:outputText styleClass="rotuloCampos"  value="EMPRESA  " /></b>
                                    </td>
                                  <td height="27" align="left" valign="middle">
                                    <h:outputText  value="#{ContratoControle.contratoVO.empresa.nome}" styleClass="texto-size-14 texto-cor-cinza texto-font"/>
                                  </td>
                                </tr>
                                <tr>
                                  <td>
                                    <b><h:outputText styleClass="rotuloCampos"  value="DURAÇÃO " /></b>

                                  </td>
                                  <td height="27" align="left" valign="middle">
                                    <h:outputText  value="#{ContratoControle.contratoVO.descricaoDuracaoTelaNegociacao}" styleClass="texto-size-14 texto-cor-cinza texto-font texto-upper"/>
                                  </td>
                                </tr>
                                <tr>
                                  <td>
                                    <b><h:outputText styleClass="rotuloCampos"  value="PLANO " /></b>
                                  </td>
                                  <td height="27" align="left" valign="middle">
                                    <h:outputText  value="#{ContratoControle.contratoVO.plano.descricao}" styleClass="texto-size-14 texto-cor-cinza texto-font"/>
                                  </td>
                                </tr>
                                <tr>
                                  <td>
                                    <b><h:outputText styleClass="rotuloCampos"  value="HORÁRIO " /></b>
                                  </td>
                                  <td height="27" align="left" valign="middle">
                                        <h:outputText  value="#{ContratoControle.contratoVO.planoHorario.horario.descricao}" styleClass="texto-size-14 texto-cor-cinza texto-font"/>
                                  </td>
                                </tr>
                                <tr>
                                  <td>
                                    <b><h:outputText styleClass="rotuloCampos" value="BOLSA " /></b>
                                  </td>
                                  <td height="27" align="left" valign="middle" colspan="2">
                                    <h:outputText  value="#{ContratoControle.contratoVO.plano.bolsa_Apresentar}" styleClass="texto-size-14 texto-cor-cinza texto-font texto-upper"/>
                                  </td>
                                </tr>
                               </tbody>
                              </table>
                            </td>
                          </tr>
                          <tr>
                            <td align="left" valign="top">

                              <table width="100%" border="0" cellspacing="0" cellpadding="0" style="margin-bottom:25px;font-size: 1vw">
                                <tr>
                                  <td align="left" valign="top"><div style="clear:both;">
                                    <p class="rotuloCampos">MODALIDADES</p>
                                  </div>
                                </tr>
                                <tr>
                                  <td height="27" align="left" valign="middle" style="padding-top: 10px;">
                                    <a4j:repeat id="planoModalidadeVO" value="#{ContratoControle.contratoModalidades}" var="cm">
                                      <h:panelGroup layout="block"
                                                    styleClass="containerModalidadeItem bordaCinza containerModalidadeItem" >
                                        <h:panelGroup layout="block" styleClass="modalidadeHeader">
                                          <h:outputText styleClass="texto-size-14 texto-font texto-cor-cinza textoImcompleto tooltipster"
                                                        title="#{cm.modalidade.nome}" style="max-width: 57%" value="#{cm.modalidade.nome}"/>
                                          <h:panelGroup layout="block" style="float: right;width: 40%">
                                              <h:outputText
                                                      styleClass="texto-size-14 texto-font texto-cor-cinza texto-bold"
                                                      value=" #{cm.nrVezesSemana}X"/>


                                            <h:outputText rendered="#{!ContratoControle.contratoVO.plano.regimeRecorrencia}"
                                                          styleClass="texto-size-14 texto-font texto-cor-cinza"
                                                          style="float: right;margin-right: 30px;min-width: 56px"
                                                          value="#{cm.modalidade.valorMensal}">
                                              <f:converter converterId="FormatadorNumerico"/>
                                            </h:outputText>
                                              <h:outputText rendered="#{!ContratoControle.contratoVO.plano.regimeRecorrencia}"
                                                            styleClass="texto-size-14 texto-font texto-cor-cinza"
                                                            style="float: right;margin-right: 5px" value="#{ContratoControle.empresaLogado.moeda}">
                                              </h:outputText>
                                          </h:panelGroup>

                                        </h:panelGroup>
                                        <h:panelGroup layout="block" style="width: 100%;display: inline-block;" styleClass="panelEditarModalidade">
                                          <h:panelGroup layout="block"
                                                        style="width: calc(100% - 30px);line-height:2.5;float: left;margin-left:2.5%;"
                                                        rendered="#{not empty cm.contratoModalidadeTurmaVOs}">
                                            <h:outputText
                                                          styleClass="texto-size-14 texto-font texto-cor-cinza texto-bold"
                                                          value="TURMAS "/>

                                            <a4j:repeat id="modalidadeTurma" value="#{cm.contratoModalidadeTurmaVOs}" var="turma">
                                              <h:panelGroup rendered="#{fn:length(turma.contratoModalidadeHorarioTurmaVOs) > 0}" layout="block" style="width: calc(100% - 30px);line-height:2.5;float: left;margin-left: 0.5%">
                                                <h:outputText styleClass="texto-size-14 texto-font texto-cor-cinza" value="#{turma.turma.identificador}"/>
                                              </h:panelGroup>
                                              <h:panelGroup layout="block" style="margin:0px 10px 10px 10px;width: calc(100% - 20px)">
                                                <a4j:repeat value="#{turma.contratoModalidadeHorarioTurmaVOs}" var="horario">

                                                  <h:panelGroup layout="block" style="width: 7%;height:40px;display: inline-block">
                                                    <h:outputText styleClass="texto-font texto-cor-cinza texto-size-14"
                                                                  value="#{horario.horarioTurma.diaSemanaAbreviado_Apresentar} "/>
                                                  </h:panelGroup>

                                                  <h:panelGroup layout="block"
                                                                style="width: 22%;height:40px;margin-left:1%;display: inline-block;text-align: center">
                                                    <h:outputText styleClass="texto-font texto-cor-cinza texto-size-14"
                                                                  value="#{horario.horarioTurma.horaInicial} às #{horario.horarioTurma.horaFinal} "/>
                                                  </h:panelGroup>

                                                  <h:panelGroup layout="block"
                                                                style="width: 38%;height:40px;margin-left:1%;display: inline-block">
                                                    <h:outputText
                                                            styleClass="texto-font texto-cor-cinza texto-size-14 texto-formato-nome"
                                                            value="#{horario.horarioTurma.professor.pessoa.nomeAbreviadoMinusculo}"/>
                                                  </h:panelGroup>

                                                  <h:panelGroup layout="block" styleClass="textoImcompleto col-text-align-right"
                                                                style="width: 24%;vertical-align:middle;height:40px;;margin-left:1%;display: inline-block;">
                                                    <h:outputText styleClass="texto-font texto-cor-cinza texto-size-14 tooltipster"
                                                                  title="#{horario.horarioTurma.ambiente.descricao}"
                                                                  value="#{horario.horarioTurma.ambiente.descricao}"/>
                                                  </h:panelGroup>
                                                  <h:panelGroup layout="block"
                                                  style="width: 2%;height:3m;margin-left: 1%;text-align: left;display: inline-block">
                                                    <a4j:commandLink  rendered="#{horario.horarioTurma.msgMatriculasFuturas != ''}"
                                                                     styleClass="linkPadrao tooltipster"
                                                                     title="#{horario.horarioTurma.msgMatriculasFuturas}">
                                                              <h:outputText
                                                                styleClass="fa-icon-exclamation-triangle-sign texto-cor-amarelo texto-size-18"/>
                                                        </a4j:commandLink>
                                                </h:panelGroup>
                                                </a4j:repeat>
                                              </h:panelGroup>
                                            </a4j:repeat>
                                          </h:panelGroup>
                                        </h:panelGroup>
                                      </h:panelGroup>
                                    </a4j:repeat>
                                  </td>
                                </tr>
                              </table>
                            </td>
                          </tr>
                            <tr>
                                <td>
                                    <table width="100%" border="0" cellspacing="0" cellpadding="0" style="margin-bottom: 25px;"
                                           >
                                        <tr>
                                            <td align="left" valign="top">
                                                <rich:dataTable id="contratoVOPlanoProdutoSugerido" width="100%"
                                                                value="#{ContratoControle.contratoVO.contratoPlanoProdutoSugeridoVOs}"
                                                                var="contratoPlanoProdutoSugerido"
                                                                styleClass="tabelaSimplesCustom">
                                                    <rich:column styleClass="col-text-align-left" headerClass="col-text-align-left" >
                                                        <f:facet name="header">
                                                            <h:outputText
                                                                    styleClass="texto-size-16 texto-cor-cinza texto-font"
                                                                    value="PRODUTOS" >
                                                            </h:outputText>
                                                        </f:facet>
                                                            <h:outputText id="descricao" style="display: inline-block;max-width: 120px"
                                                                    styleClass="texto-size-16 texto-cor-cinza texto-font textoImcompleto tooltipster"
                                                                    title="#{contratoPlanoProdutoSugerido.planoProdutoSugerido.produto.descricao}" value="#{contratoPlanoProdutoSugerido.planoProdutoSugerido.produto.descricao}"/>
                                                    </rich:column>
                                                    <rich:column styleClass="col-text-align-center" headerClass="col-text-align-center" >
                                                        <f:facet name="header">
                                                            <h:outputText
                                                                    styleClass="texto-size-16 texto-cor-cinza texto-font"
                                                                    value="QTD">
                                                            </h:outputText>
                                                        </f:facet>
                                                        <h:outputText
                                                                styleClass="texto-size-16 texto-cor-cinza texto-font"
                                                                value="#{contratoPlanoProdutoSugerido.planoProdutoSugerido.quantidade}">
                                                        </h:outputText>
                                                    </rich:column>
                                                    <rich:column styleClass="col-text-align-right" headerClass="col-text-align-right" >
                                                        <f:facet name="header">
                                                            <h:outputText
                                                                    styleClass="texto-size-16 texto-cor-cinza texto-font"
                                                                    value="VALOR">
                                                            </h:outputText>
                                                        </f:facet>
                                                        <h:outputText id="valor"
                                                                styleClass="texto-size-16 texto-cor-cinza texto-font"
                                                                value="#{contratoPlanoProdutoSugerido.planoProdutoSugerido.valorProduto}">
                                                            <f:converter converterId="FormatadorNumerico"/>
                                                        </h:outputText>
                                                    </rich:column>
                                                    <rich:column styleClass="col-text-align-right" headerClass="col-text-align-right">
                                                        <f:facet name="header">
                                                            <h:outputText
                                                                    styleClass="texto-size-16 texto-cor-cinza texto-font"
                                                                    value="DESC.">
                                                            </h:outputText>
                                                        </f:facet>
                                                        <h:outputText
                                                                styleClass="texto-size-16 texto-cor-cinza texto-font"
                                                                value="#{contratoPlanoProdutoSugerido.planoProdutoSugerido.produto.desconto.valor}">
                                                            <f:converter converterId="FormatadorNumerico"/>
                                                        </h:outputText>
                                                    </rich:column>
                                                    <rich:column styleClass="col-text-align-right" headerClass="col-text-align-right">
                                                        <f:facet name="header">
                                                            <h:outputText
                                                                    styleClass="texto-size-16 texto-cor-cinza texto-font"
                                                                    value="VALOR FINAL">
                                                            </h:outputText>
                                                        </f:facet>
                                                        <h:outputText
                                                                styleClass="texto-size-16 texto-cor-cinza texto-font"
                                                                value="#{contratoPlanoProdutoSugerido.planoProdutoSugerido.valorProdutoQtdDesconto}">
                                                            <f:converter converterId="FormatadorNumerico"/>
                                                        </h:outputText>
                                                    </rich:column>
                                                </rich:dataTable>
                                                <rich:dataTable id="contratoVOModalidadeProdutoSugerido" width="100%"
                                                                styleClass="tabelaSimplesCustom"
                                                                value="#{ContratoControle.listaProdutoApresentar}"
                                                                var="contratoProdutoSugerido">
                                                    <rich:column styleClass="semBorda">
                                                        <h:outputText
                                                                styleClass="texto-size-16 texto-cor-cinza texto-font"
                                                                value="#{contratoProdutoSugerido.produtoSugerido.produto.descricao}"/>
                                                    </rich:column>
                                                    <rich:column styleClass="colunaDireita">
                                                        <h:outputText
                                                                styleClass="texto-size-16 texto-cor-cinza texto-font"
                                                                value="#{contratoProdutoSugerido.valorFinalProduto}">
                                                            <f:converter converterId="FormatadorNumerico"/>
                                                        </h:outputText>
                                                    </rich:column>
                                                </rich:dataTable>
                                            </td>
                                        </tr>
                                    </table>
                                </td>
                            </tr>
                          <tr>
                            <td>
                              <table width="100%" border="0" cellspacing="0" cellpadding="0">
                                <tr>
                                  <td align="left" valign="top">

                                    <rich:dataTable id="movProduto" width="100%"
                                                    rendered="#{not empty ContratoControle.contratoVO.movProdutoVOs}"
                                                    value="#{ContratoControle.contratoVO.movProdutoVOs}" var="movProduto" styleClass="tabelaSimplesCustom">

                                      <rich:column headerClass="col-text-align-left"  styleClass="semBorda col-text-align-left">
                                          <f:facet name="header">
                                              <h:outputText value="MENSALIDADE" styleClass="rotuloCampos"/>
                                          </f:facet>
                                          <h:panelGroup>
                                              <h:outputText  id="descMensalidade" rendered="#{movProduto.apresentarMovProduto}" style="max-width: 27em;display: inline-block;" title="#{movProduto.descricao}" styleClass="textoImcompleto texto-size-16 texto-cor-cinza texto-font tooltipster" value="#{movProduto.descricao}"/>
                                              <h:outputLabel styleClass="texto-size-16 texto-cor-verde tooltipster" title="Cupom : #{ContratoControle.numeroCupom}" style="vertical-align: middle;" rendered="#{(not empty movProduto.numeroCupomDesconto) and (movProduto.apresentarMovProduto)}" >
                                                <i   class="fa-icon-tags texto-size-16 texto-cor-verde" ></i>
                                             </h:outputLabel>
                                          </h:panelGroup>
                                      </rich:column>
                                        
                                      <rich:column  headerClass="col-text-align-right" styleClass="col-text-align-right">
                                        <f:facet name="header">
                                              <h:outputText value="TOTAL" styleClass="rotuloCampos"/>
                                        </f:facet>
                                           <h:outputText  id="totalMensalidade" rendered="#{movProduto.apresentarMovProduto}" styleClass="texto-size-16 texto-cor-cinza texto-font" value="#{movProduto.totalFinalApresentarTelaNegociacao}">
                                          <f:converter converterId="FormatadorNumerico"/>
                                        </h:outputText>
                                      </rich:column>

                                    </rich:dataTable>
                                  </td>
                                </tr>
                              </table>
                            </td>
                          </tr>
                            <tr>
                                <td>
                                    <h:panelGroup layout="block" styleClass="container-botoes" style="text-align: left;margin: 20px 0px 20px 0px;">

                                        <a4j:commandLink styleClass="linkPadrao texto-font texto-size-14"
                                                         action="#{ContratoControle.voltar}"
                                                         rendered="#{ContratoControle.apresentarBotoesFecharEReceber}">
                                            <i class="fa-icon-arrow-left texto-size-16 texto-cor-azul"></i> Voltar para a tela de negociação
                                        </a4j:commandLink>
                                    </h:panelGroup>
                                </td>
                            </tr>
                        </table>
                      </td>
                    </tr>
                  </table> <!-- fim item -->
                    </h:panelGroup>
                  <h:panelGroup id="dados" layout="block"   styleClass="containerDetalhesNegociacao bg-cinza" style="text-align: center;">
                        <h:panelGroup id="detalhesNegociacao" layout="block"
                                      styleClass="detalhesNegociacao">
                            <h:panelGroup layout="block" >


                                <%--com familiares--%>
                                <h:panelGroup  layout="block" styleClass="containerFotoCliente" rendered="#{fn:length(NegociacaoFamiliaresControle.familiares) gt 0}">
                                    <%@include file="/includes/include_conferir_negociacao_familiares.jsp" %>
                                </h:panelGroup>


                                <%--sem familiares--%>
                                <h:panelGroup  layout="block" styleClass="containerFotoCliente" rendered="#{fn:length(NegociacaoFamiliaresControle.familiares) eq 0}">
                                    <a4j:mediaOutput element="img" id="imagemFoto" cacheable="false"
                                                     rendered="#{!SuperControle.fotosNaNuvem}"
                                                     createContent="#{ContratoControle.paintFoto}"
                                                     value="#{ImagemData}" mimeType="image/jpeg">
                                    </a4j:mediaOutput>
                                    <h:graphicImage rendered="#{SuperControle.fotosNaNuvem}"
                                                    url="#{ContratoControle.paintFotoDaNuvem}"/>
                                </h:panelGroup>

                                <h:panelGroup layout="block" rendered="#{fn:length(NegociacaoFamiliaresControle.familiares) eq 0}">
                                    <h:outputText id="pessoaNome"
                                                  styleClass="texto-size-20 texto-font texto-cor-cinza-2 texto-bold"
                                                  value="#{ContratoControle.contratoVO.pessoa.nome}"/>
                                </h:panelGroup>

                                <h:panelGroup layout="block" styleClass="gridValoresContrato">
                                    <h:panelGroup layout="block" styleClass="valorItem" >
                                        <h:outputText styleClass="texto-size-16 texto-font texto-cor-cinza-2 texto-bold"
                                                      value="TIPO CONTRATO"/>
                                        <h:panelGroup id="agendadoEspontaneo" layout="block" styleClass="pull-right" style="margin-right: 5%">
                                            <a4j:commandLink id="linkContratoEspontaneo" styleClass="linkPadrao texto-size-16 texto-font texto-cor-azul texto-bold"
                                                             rendered="#{!ContratoControle.contratoVO.contratoAgendado}"

                                                             actionListener="#{ContratoControle.prepararAlteracaoTipoContrato}"
                                                             oncomplete="Richfaces.showModalPanel('panelUsuarioSenhaTipoContrato');"
                                                             reRender="panelUsuarioSenhaTipoContrato" >
                                                <f:attribute value="#{ContratoControle.contratoVO}" name="contratoAlteracaoTipo" />
                                                <h:outputText styleClass="tooltipster"   value="#{msg_aplic.prt_Contrato_espontaneo}" title="#{msg_aplic.prt_Contrato_Tipodica}"/>
                                            </a4j:commandLink>
                                            <a4j:commandLink id="linkContratoAgendado" styleClass="linkPadrao texto-size-16 texto-font texto-cor-azul texto-bold"
                                                             rendered="#{ContratoControle.contratoVO.contratoAgendado}"
                                                             value="#{msg_aplic.prt_Contrato_agendado}"
                                                             actionListener="#{ContratoControle.prepararAlteracaoTipoContrato}"
                                                             oncomplete="Richfaces.showModalPanel('panelUsuarioSenhaTipoContrato');"
                                                             reRender="panelUsuarioSenhaTipoContrato">
                                                <f:attribute value="#{ContratoControle.contratoVO}" name="contratoAlteracaoTipo" />
                                                <h:outputText styleClass="tooltipster" title="#{msg_aplic.prt_Contrato_Tipodica}"/>
                                            </a4j:commandLink>
                                        </h:panelGroup>
                                    </h:panelGroup>
                                    <h:panelGroup layout="block" styleClass="valorItem">
                                        <h:outputText
                                                styleClass="texto-size-16 texto-font texto-cor-cinza-2 texto-bold"
                                                value="TOTAL DO CONTRATO"/>
                                        <%-- <h:outputText id="totalContrato" styleClass="texto-size-16 texto-font texto-cor-cinza-2 texto-bold" value="#{ContratoControle.contratoVO.valorFinalApresentarConferirNegociacao}"> --%>
                                        <h:outputText id="totalContrato" styleClass="texto-size-16 texto-font texto-cor-cinza-2 texto-bold" value="#{ContratoControle.contratoVO.valorFinal}">
                                            <f:converter converterId="FormatadorNumerico"/>
                                        </h:outputText>
                                        <h:outputText value="#{ContratoControle.empresaLogado.moeda} "
                                                      styleClass="texto-size-16 texto-font texto-cor-cinza-2 texto-bold"/>
                                    </h:panelGroup>
                                    <h:panelGroup layout="block" styleClass="valorItem">
                                        <h:outputText styleClass="texto-size-16 texto-font texto-cor-cinza-2 texto-bold"
                                                      value="TOTAL PLANO"/>
                                        <%-- <h:outputText id="totalMensalidade"  styleClass="texto-size-16 texto-font texto-cor-cinza-2 texto-bold" value="#{ContratoControle.contratoVO.valorBaseCalculoApresentarConferirNegociacao}"> --%>
                                        <h:outputText id="totalMensalidade"  styleClass="texto-size-16 texto-font texto-cor-cinza-2 texto-bold" value="#{ContratoControle.contratoVO.valorBaseCalculo}">
                                            <f:converter converterId="FormatadorNumerico"/>
                                        </h:outputText>

                                        <h:outputText value="#{ContratoControle.empresaLogado.moeda} "
                                                      styleClass="texto-size-16 texto-font texto-cor-cinza-2 texto-bold"/>
                                    </h:panelGroup>
                                    <h:panelGroup layout="block" styleClass="valorItem">
                                        <h:outputText styleClass="texto-size-16 texto-font texto-cor-cinza-2 texto-bold"
                                                      value="PRODUTOS"/>
                                        <h:outputText styleClass="texto-size-16 texto-font texto-cor-cinza-2 texto-bold"
                                                      value="#{ContratoControle.contratoVO.somaProduto}">
                                            <f:converter converterId="FormatadorNumerico"/>
                                        </h:outputText>
                                        <h:outputText value="#{ContratoControle.empresaLogado.moeda} "
                                                      styleClass="texto-size-16 texto-font texto-cor-cinza-2 texto-bold"/>
                                    </h:panelGroup>

                                    <h:panelGroup layout="block" styleClass="valorItem" rendered="#{ContratoControle.contratoVO.descontoParcelaUsandoCupomDesconto > 0}">
                                        <h:outputText
                                                styleClass="texto-size-16 texto-font texto-cor-cinza-2 texto-bold"
                                                value="CUPOM DE DESCONTO"/>
                                        <h:outputText id="totalDescontoCupomDesconto" styleClass="texto-size-16 texto-font texto-cor-cinza-2 texto-bold" value="#{ContratoControle.contratoVO.descontoParcelaUsandoCupomDesconto}">
                                            <f:converter converterId="FormatadorNumerico"/>
                                        </h:outputText>
                                        <h:outputText value="#{ContratoControle.empresaLogado.moeda} "
                                                      styleClass="texto-size-16 texto-font texto-cor-cinza-2 texto-bold"/>
                                    </h:panelGroup>

                                </h:panelGroup>
                            </h:panelGroup>


                            <h:panelGroup layout="block" styleClass="gridValoresContrato">
                                <h:panelGroup layout="block" styleClass="boxTotalPagar bg-cinza-3">
                                    <h:panelGroup layout="block" styleClass="pull-left" style="margin-left: 5%">
                                        <h:outputText
                                                styleClass="texto-size-20 texto-font texto-cor-branco texto-bold"
                                                value="TOTAL"/>
                                    </h:panelGroup>
                                    <h:panelGroup layout="block" styleClass="pull-right" style="margin-right: 5%">
                                        <%-- <h:outputText id="valorFinalContrato" styleClass="pull-right texto-bold texto-size-20 texto-font texto-font texto-cor-branco" value="#{ContratoControle.contratoVO.valorFinalApresentarConferirNegociacao}"> --%>
                                        <h:outputText id="valorFinalContrato" styleClass="pull-right texto-bold texto-size-20 texto-font texto-font texto-cor-branco" value="#{ContratoControle.contratoVO.valorFinalComDescontos}">
                                            <f:converter converterId="FormatadorNumerico"/>
                                        </h:outputText>
                                        <h:outputText
                                                styleClass="pull-right texto-bold texto-size-20 texto-font texto-font texto-cor-branco"
                                                value="#{ContratoControle.empresaLogado.moeda}" style="margin-right: 5px"/>
                                    </h:panelGroup>
                                </h:panelGroup>
                            </h:panelGroup>

                            <h:panelGroup id="tabelaInvPontos" layout="block" styleClass="tooltipElementoPontos"
                                          style="display: none;">
                                <h:panelGrid columns="3"
                                             columnClasses="colunaEsquerda,colunaDireita,colunaDireita"
                                             styleClass="tabelaSimplesCustom font-size-Em"
                                             width="100%"
                                             style="margin: 5px;display: inline-block;">

                                    <h:outputText
                                            styleClass="texto-font texto-size-16 texto-cor-cinza"
                                            value="#{ContratoControle.contratoVO.plano.descricao} "/>
                                    <h:outputText style="text-align:right;padding-left: 20px"
                                                  styleClass="texto-font texto-size-16 texto-cor-verde"
                                                  value="#{ContratoControle.contratoVO.plano.pontos}">
                                    </h:outputText>
                                    <h:outputText style="text-align:right;padding-left: 20px"
                                                  styleClass="texto-font texto-size-16 texto-cor-verde"
                                                  value="x #{ContratoControle.contratoVO.planoMaiorMultiplicadorCampanhaAtiva <= 0 ? 1 : ContratoControle.contratoVO.planoMaiorMultiplicadorCampanhaAtiva}">
                                    </h:outputText>

                                    <c:forEach items="#{ContratoControle.contratoVO.contratoPlanoProdutoSugeridoVOs}"
                                               var="produto">
                                        <h:outputText
                                                styleClass="texto-font texto-size-16 texto-cor-cinza"
                                                value="#{produto.planoProdutoSugerido.produto.descricao} "/>
                                        <h:outputText style="text-align:right;padding-left: 20px"
                                                      styleClass="texto-font texto-size-16 texto-cor-verde"
                                                      value="#{produto.planoProdutoSugerido.produto.pontos * (produto.planoProdutoSugerido.quantidade > 1 ? produto.planoProdutoSugerido.quantidade : 1)}"/>

                                        <h:outputText style="text-align:right;padding-left: 20px"
                                                      styleClass="texto-font texto-size-16 texto-cor-verde"
                                                      value="x #{produto.planoProdutoSugerido.obterMultiplicadorCampanhaProduto <= 0 ? 1 : produto.planoProdutoSugerido.obterMultiplicadorCampanhaProduto}">
                                        </h:outputText>
                                    </c:forEach>

                                    <h:outputText
                                            styleClass="texto-font texto-size-16 texto-cor-cinza"
                                            value="#{ContratoControle.contratoVO.planoDuracao.descricaoDuracao} "/>
                                    <h:outputText style="text-align:right;padding-left: 20px"
                                                  styleClass="texto-font texto-size-16 texto-cor-verde"
                                                  value="#{ContratoControle.contratoVO.planoDuracao.pontos}">
                                    </h:outputText>
                                    <h:outputText style="text-align:right;padding-left: 20px"
                                                  styleClass="texto-font texto-size-16 texto-cor-verde"
                                                  value="x #{ContratoControle.contratoVO.planoDuracaoMaiorMultiplicadorCampanhaAtiva <= 0 ? 1 : ContratoControle.contratoVO.planoDuracaoMaiorMultiplicadorCampanhaAtiva}">
                                    </h:outputText>
                                </h:panelGrid>
                            </h:panelGroup>
                            <br/>
                            <h:panelGroup layout="block" styleClass="gridValoresContrato"
                                          rendered="#{(!ContratoControle.contratoVO.plano.regimeRecorrencia) && (!ContratoControle.contratoVO.plano.vendaCreditoTreino)}">
                                <h:panelGroup layout="block" >
                                    <h:outputText styleClass="texto-size-14 texto-font texto-cor-cinza texto-bold pull-left"
                                                  value=" PRÓ-RATA"/>
                                    <h:outputLink styleClass="linkWiki "
                                                  style="vertical-align: bottom;display: table-caption;margin-left: 5px;"
                                                  value="#{SuperControle.urlBaseConhecimento}como-lancar-plano-normal-para-os-clientes/"
                                                  title="Clique e saiba mais: Pró-Rata" target="_blank">
                                        <i class="fa-icon-question-sign" style="font-size: 18px"></i>
                                    </h:outputLink>
                                </h:panelGroup>

                                <h:panelGroup layout="block" styleClass="cb-container "
                                              style="width:25%;display: block;margin-top: 5px;margin-right: 15px;">
                                    <h:selectOneMenu id="diasVencimento" onblur="blurinput(this);"
                                                     onfocus="focusinput(this);"
                                                     value="#{ContratoControle.indiceDiaVencimento}">
                                        <a4j:support event="onchange"
                                                     reRender="panelProdutoParcela, movProduto, vigencia, valorFinalContrato, totalMensalidade, groupPrimeiraParcela,containerParcelas,panelParcelaProdutoMatricula,panelVigenciaContrato"
                                                     action="#{ContratoControle.selecionarProrata}"/>
                                        <f:selectItems value="#{ContratoControle.listaDiasVencimento}"/>
                                    </h:selectOneMenu>
                                </h:panelGroup>
                            </h:panelGroup>
                            <h:panelGroup layout="block" styleClass="gridValoresContrato"
                                          rendered="#{!ContratoControle.contratoVO.plano.vendaCreditoTreino}">
                                <h:panelGroup id="groupPrimeiraParcela" layout="block" styleClass="col-text-align-left">
                                    <h:panelGroup layout="block" >
                                        <h:outputText styleClass="texto-size-14 texto-font texto-cor-cinza texto-bold pull-left" style="font-weight: bold"
                                                      value="PRIMEIRA PARCELA"/>
                                        <h:outputLink styleClass="linkWiki"
                                                      style="vertical-align: bottom;display: table-caption;margin-left: 5px;"
                                                      value="#{SuperControle.urlBaseConhecimento}como-lancar-plano-normal-para-os-clientes/"
                                                      title="Clique e saiba mais: Data Primeira Parcela" target="_blank">
                                            <i class="fa-icon-question-sign" style="font-size: 18px"></i>
                                        </h:outputLink>
                                    </h:panelGroup>
                                    <c:choose>
                                        <c:when test="${ContratoControle.contratoVO.plano.regimeRecorrencia}">
                                            <h:panelGroup layout="block"  style="margin-top: 5px;margin-right: 15px;">
                                                <h:outputText style="width: 90px;text-align: left;" styleClass="texto-font texto-size-16 texto-cor-cinza" value="#{ContratoControle.contratoVO.dataPrimeiraParcela}">
                                                    <f:convertDateTime pattern="dd/MM/yyyy"/>
                                                </h:outputText>
                                            </h:panelGroup>
                                        </c:when>
                                        <c:otherwise>
                                            <h:panelGroup layout="block" styleClass="dateTimeCustom alignToRight col-text-align-left" style="margin-top: 5px;display: table;position: relative">
                                                <rich:calendar id="dataPrimeiraParcela"
                                                               value="#{ContratoControle.contratoVO.dataPrimeiraParcela}"
                                                               inputSize="10"
                                                               inputClass="form"
                                                               oninputblur="blurinput(this);"
                                                               oninputfocus="focusinput(this);"
                                                               datePattern="dd/MM/yyyy"
                                                               buttonIcon="/imagens_flat/calendar-button.svg"
                                                               enableManualInput="true"
                                                               styleClass="inputTextClean"
                                                               zindex="2"
                                                               showWeeksBar="false">
                                                    <a4j:support event="oninputchange"
                                                                 action="#{ContratoControle.validarDataPrimeiraParcela}"
                                                                 reRender="dataPrimeiraParcela,groupPrimeiraParcela,panelAutorizacaoFuncionalidade"
                                                                 ajaxSingle="true"
                                                                 oncomplete="#{ContratoControle.mensagemNotificar}"/>
                                                    <a4j:support event="onchanged"
                                                                 action="#{ContratoControle.validarDataPrimeiraParcela}"
                                                                 reRender="dataPrimeiraParcela,groupPrimeiraParcela,panelAutorizacaoFuncionalidade"
                                                                 ajaxSingle="true"
                                                                 oncomplete="#{ContratoControle.mensagemNotificar}"/>
                                                </rich:calendar>
                                            </h:panelGroup>
                                        </c:otherwise>
                                    </c:choose>
                                    <h:message for="dataPrimeiraParcela" styleClass="mensagemDetalhada"/>
                                </h:panelGroup>
                            </h:panelGroup>
                            <h:panelGroup rendered="#{ContratoControle.mostrarDividirProdutosNasParcelas}" layout="block" style="margin: 0px 20px 0px 20px;" styleClass="gridValoresContrato col-text-align-left">
                                <h:panelGroup layout="block" styleClass="checkbox-fa">
                                    <a4j:commandLink styleClass="linkPadrao"
                                                     style="display: inline-block"
                                                     action="#{ContratoControle.marcarDivisaoProdutoParcelaTirandoArredondamento}"
                                                     reRender="panelProdutoParcela, valorFinalContrato,dados">

                                        <h:outputText  styleClass="#{ContratoControle.contratoVO.dividirProdutosNasParcelas ? 'fa-icon-check' : 'fa-icon-check-empty'} texto-size-16 texto-cor-cinza"/>
                                        <h:outputText  styleClass="rotuloCampos" value=" DIVIDIR PRODUTOS NAS PARCELAS" />

                                    </a4j:commandLink>
                                </h:panelGroup>
                            </h:panelGroup>
                            <h:panelGroup layout="block" styleClass="gridValoresContrato"
                                          rendered="#{(!ContratoControle.contratoVO.cobrarMatriculaSeparada) && (!ContratoControle.contratoVO.cobrarProdutoSeparado)}"
                                          id="containerParcelas">

                                <h:panelGroup layout="block" styleClass="valorItem col-text-align-right" style="height: 50px;" >
                                    <a4j:commandLink action="#{ContratoControle.apresentarArrendondamento}"
                                                     id="Arrendondamento"
                                                     rendered="#{ContratoControle.usarArredondamento}"
                                                     reRender="formArredondamento,panelProdutoParcela,valorFinalContrato"
                                                     styleClass="linkPadrao texto-font texto-size-14"
                                                     style="line-height:normal;margin-top: 10px;margin-right: 15px;display:inline-block;"
                                                     value="#{msg_aplic.prt_arredondamento_parcelas}"
                                                     oncomplete="Richfaces.showModalPanel('painelArredondamento');">
                                    </a4j:commandLink>
                                    <a4j:commandLink
                                                     rendered="#{LoginControle.permissaoAcessoMenuVO.permiteEditarValorParcelaNegociacao}"
                                                     reRender="formArredondamento,panelProdutoParcela,valorFinalContrato, mensagem, editarParcelas"
                                                     styleClass="linkPadrao texto-font texto-size-14"
                                                     style="line-height:normal;margin-top: 10px;display:inline-block;"
                                                     value="Editar"
                                                     action="#{ContratoControle.montarModalEdicaoParcelasNegociacao}"
                                                     oncomplete="Richfaces.showModalPanel('editarParcelas');">
                                    </a4j:commandLink>
                                </h:panelGroup>
                            </h:panelGroup>

                            <h:panelGroup layout="block" id="panelParcelaProdutoMatricula" styleClass="tudo">
                                <h:panelGroup layout="block" styleClass="container-botoes"
                                              rendered="#{((ContratoControle.contratoVO.cobrarProdutoSeparado) || (ContratoControle.contratoVO.cobrarMatriculaSeparada) || (ContratoControle.contratoVO.somaAdesao > 0))}"
                                              style="display:inline-table;min-height: 50px;line-height: 50px;margin: 0px 0px 10px 0px;width: calc(100% - 40px); border-top: 1px solid #777; border-bottom: 1px solid #777">

                                    <h:panelGrid columns="1">
                                        <h:outputText styleClass="texto-size-16 texto-font texto-cor-cinza-2 texto-bold pull-left"
                                                      value="PARCELA(s)"/>

                                    </h:panelGrid>

                                    <h:panelGrid columns="2"
                                                 cellspacing="10px"
                                                 columnClasses="colunaEsquerda, colunaDireita,colunaDireita,colunaDireita">

                                        <%-- INICIO PARCELAS MATRICULA --%>
                                        <h:outputText styleClass="texto-size-16 texto-font texto-bold texto-cor-cinza"
                                                      rendered="#{(ContratoControle.mostrarParcelamentoMatricula) && (ContratoControle.contratoVO.valorMatricula > 0)}"
                                                      value="Matrícula"/>
                                        <h:panelGroup rendered="#{(ContratoControle.mostrarParcelamentoMatricula) && (ContratoControle.contratoVO.valorMatricula > 0) }">

                                            <h:outputText styleClass="texto-size-16 texto-font texto-bold texto-cor-cinza"
                                                          style="margin-left: 10px"
                                                          value="#{ContratoControle.contratoVO.nrVezesParcelarMatricula}x "/>
                                            <h:outputLabel  value="#{ContratoControle.empresaLogado.moeda}" styleClass="texto-size-16 texto-font texto-cor-cinza-2 texto-bold " style="padding: 3px"/>
                                            <h:outputText
                                                    styleClass="texto-size-16 texto-font texto-cor-cinza texto-bold"
                                                    value="#{ContratoControle.contratoVO.valorParcelasMatricula}">
                                                <f:converter converterId="FormatadorNumerico"/>
                                            </h:outputText>
                                        </h:panelGroup>

                                        <%-- INICIO PARCELAS REMATRÍCULA --%>
                                        <h:outputText styleClass="texto-size-16 texto-font texto-bold texto-cor-cinza"
                                                      rendered="#{(ContratoControle.mostrarParcelamentoMatricula) && (ContratoControle.contratoVO.valorRematricula > 0) }"
                                                      value="Rematrícula"/>
                                        <h:panelGroup rendered="#{(ContratoControle.mostrarParcelamentoMatricula && (ContratoControle.contratoVO.valorRematricula > 0) )}">

                                            <h:outputText styleClass="texto-size-16 texto-font texto-bold texto-cor-cinza"
                                                          style="margin-left: 10px"
                                                          value="#{ContratoControle.contratoVO.nrVezesParcelarMatricula}x "/>
                                            <h:outputLabel  value="#{MovPagamentoControle.empresaLogado.moeda}" styleClass="texto-size-16 texto-font texto-cor-cinza-2 texto-bold " style="padding: 3px"/>
                                            <h:outputText
                                                    styleClass="texto-size-16 texto-font texto-cor-cinza texto-bold"
                                                    value="#{ContratoControle.contratoVO.valorParcelasRematricula}">
                                                <f:converter converterId="FormatadorNumerico"/>
                                            </h:outputText>
                                        </h:panelGroup>

                                        <%-- INICIO PARCELAS PRODUTOS --%>
                                        <h:outputText styleClass="texto-size-16 texto-font texto-bold texto-cor-cinza"
                                                      rendered="#{(ContratoControle.mostrarParcelamentoProdutos)}"
                                                      value="Produto(s)"/>
                                        <h:panelGroup rendered="#{(ContratoControle.mostrarParcelamentoProdutos)}">

                                            <h:outputText styleClass="texto-size-16 texto-font texto-bold texto-cor-cinza"
                                                          style="margin-left: 10px"
                                                          value="#{ContratoControle.contratoVO.nrVezesParcelarProduto}x "/>
                                            <h:outputLabel  value="#{ContratoControle.empresaLogado.moeda}" styleClass="texto-size-16 texto-font texto-cor-cinza-2 texto-bold " style="padding: 3px"/>
                                            <h:outputText
                                                    styleClass="texto-size-16 texto-font texto-cor-cinza texto-bold"
                                                    value="#{ContratoControle.contratoVO.valorParcelasProduto}">
                                                <f:converter converterId="FormatadorNumerico"/>
                                            </h:outputText>
                                        </h:panelGroup>

                                        <%-- INICIO PARCELAS ADESÃO --%>
                                        <h:outputText styleClass="texto-size-16 texto-font texto-bold texto-cor-cinza"
                                                      rendered="#{ContratoControle.contratoVO.gerarParcelaParaProdutos && ContratoControle.contratoVO.somaAdesao > 0 && ContratoControle.contratoVO.planoCondicaoPagamento.condicaoPagamento.condicaoPagamentoEscolhida}"
                                                      value="Adesão"/>

                                        <h:panelGroup  layout="block"  rendered="#{ContratoControle.contratoVO.gerarParcelaParaProdutos && ContratoControle.contratoVO.somaAdesao > 0 && ContratoControle.contratoVO.planoCondicaoPagamento.condicaoPagamento.condicaoPagamentoEscolhida}">
                                            <h:outputText id="nrCondicaoPagamento111"
                                                          styleClass="texto-size-16 texto-font texto-bold texto-cor-cinza"
                                                          value="#{ContratoControle.contratoVO.nrParcelasAdesao}x "/>
                                            <h:outputLabel  value="#{ContratoControle.empresaLogado.moeda}" styleClass="texto-size-16 texto-font texto-cor-cinza-2 texto-bold " style="padding: 3px"/>
                                            <h:outputText id="valorCondicaoPagamento11"
                                                          styleClass="texto-size-16 texto-font texto-bold texto-cor-cinza"
                                                          value="#{ContratoControle.contratoVO.valorParcelasAdesao}">
                                                <f:converter converterId="FormatadorNumerico"/>
                                            </h:outputText>

                                        </h:panelGroup>

                                        <c:if test="${ContratoControle.contratoVO.gerarParcelaAnuidadeSeparada && ContratoControle.contratoVO.nrParcelasAnuidades > 0}">
                                            <%-- INICIO PARCELAS ANUIDADE --%>
                                            <h:outputText
                                                    styleClass="texto-size-16 texto-font texto-bold texto-cor-cinza"
                                                    value="Anuidade"/>

                                            <h:panelGroup layout="block">
                                                <c:choose>
                                                    <c:when test="${ContratoControle.contratoVO.plano.planoRecorrencia.parcelarAnuidade and fn:length(ContratoControle.contratoVO.plano.planoRecorrencia.parcelasAnuidade) > 0}">
                                                        <h:outputText id="txtAnuidade11"
                                                                      styleClass="texto-size-16 texto-font texto-bold texto-cor-cinza"
                                                                      value="#{ContratoControle.anuidadeConfigurada} "/>
                                                    </c:when>
                                                    <c:otherwise>
                                                        <h:outputText id="nrAnuidades11"
                                                                      styleClass="texto-size-16 texto-font texto-bold texto-cor-cinza"
                                                                      value="#{ContratoControle.contratoVO.nrParcelasAnuidades}x "/>

                                                        <h:outputLabel value="#{ContratoControle.empresaLogado.moeda}"
                                                                       styleClass="texto-size-16 texto-font texto-cor-cinza-2 texto-bold "
                                                                       style="padding: 3px"/>

                                                        <h:outputText id="valorAnuidade11"
                                                                      styleClass="texto-size-16 texto-font texto-bold texto-cor-cinza"
                                                                      value="#{ContratoControle.contratoVO.valorPorAnuidade}">
                                                            <f:converter converterId="FormatadorNumerico"/>
                                                        </h:outputText>
                                                    </c:otherwise>
                                                </c:choose>
                                            </h:panelGroup>
                                        </c:if>


                                        <%-- INICIO PARCELAS PLANO --%>
                                        <h:outputText styleClass="texto-size-16 texto-font texto-bold texto-cor-cinza"
                                                      value="Plano"/>
                                        <c:if test="${fn:length(ContratoControle.contratoVO.listParcelasEditadas) == 0}">
                                        <h:panelGroup >

                                            <h:panelGroup  layout="block" rendered="#{ContratoControle.contratoVO.planoCondicaoPagamento.condicaoPagamento.condicaoPagamentoEscolhida}">
                                                <h:outputText id="nrCondicaoPagamento11"
                                                              styleClass="texto-size-16 texto-font texto-bold texto-cor-cinza"
                                                              value=" 1x "/>
                                                <h:outputLabel  value="#{ContratoControle.empresaLogado.moeda}" styleClass="texto-size-16 texto-font texto-cor-cinza-2 texto-bold " style="padding: 3px"/>
                                                <h:outputText id="valorCondicaoPagamento111"
                                                              styleClass="texto-size-16 texto-font texto-bold texto-cor-cinza"
                                                              value="#{ContratoControle.valorPrimeiraParcela} ">
                                                    <f:converter converterId="FormatadorNumerico"/>
                                                </h:outputText>
                                            </h:panelGroup>
                                            <h:panelGroup layout="block" rendered="#{!ContratoControle.parcelaAvistaContrato && ContratoControle.contratoVO.planoCondicaoPagamento.condicaoPagamento.condicaoPagamentoEscolhida}">
                                                <h:outputText
                                                        style="margin-left: 10px"
                                                        styleClass="texto-size-16 texto-font texto-bold texto-cor-cinza"
                                                        value="#{ContratoControle.quantidadeParcelasValorContrato}x "/>
                                                <h:outputLabel value="#{ContratoControle.empresaLogado.moeda}" styleClass="texto-size-16 texto-font texto-cor-cinza-2 texto-bold " style="padding: 3px"/>
                                                <h:outputText
                                                        styleClass="texto-size-16 texto-font texto-cor-cinza texto-bold"
                                                        value="#{ContratoControle.valorParcelaContrato}">
                                                    <f:converter converterId="FormatadorNumerico"/>
                                                </h:outputText>
                                                <h:outputText styleClass="texto-size-14 texto-font texto-cor-cinza-2 texto-bold " value="#{ContratoControle.parcelasValorDiferente}" />

                                            </h:panelGroup>
                                        </h:panelGroup>
                                    </c:if>
                                        <c:if test="${fn:length(ContratoControle.contratoVO.listParcelasEditadas) > 0}">
                                            <h:panelGroup styleClass="tooltipster">
                                                <span   class="texto-size-16 texto-font texto-cor-cinza texto-bold " style="margin-left: 10px"
                                                        onmouseover="exibirElementoTooltip(jQuery(this).parent(),jQuery('.tooltipElementoParcelasEditadas'))"><i class="fa-icon-info-sign" style="margin-right: 2px"></i> Parcelas editadas</span>
                                            </h:panelGroup>
                                            <h:panelGroup id="tabelaParcelasEditadas2" layout="block" styleClass="tooltipElementoParcelasEditadas"
                                                          style="display: none;">
                                                <h:panelGrid columns="4"
                                                             columnClasses="colunaEsquerda,colunaDireita"
                                                             styleClass="tabelaSimplesCustom font-size-Em"
                                                             width="100%"
                                                             style="margin: 5px;display: inline-block;">

                                                    <h:outputText
                                                            styleClass="texto-font texto-size-16 texto-cor-cinza texto-bold"
                                                            value="Entrada"/>
                                                    <h:outputLabel  value="#{ContratoControle.empresaLogado.moeda}" styleClass="texto-size-16 texto-font texto-cor-cinza-2 texto-bold " style="padding: 3px"/>
                                                    <h:outputText
                                                            styleClass="texto-font texto-size-16 texto-cor-verde"
                                                            value="#{ContratoControle.valorParcelaComProdutoContrato}">
                                                        <f:converter converterId="FormatadorNumerico"/>
                                                    </h:outputText>
                                                    <c:forEach items="#{ContratoControle.contratoVO.listParcelasEditadas}" var="parcelasEditadas">
                                                        <h:outputText
                                                                styleClass="texto-font texto-size-16 texto-cor-cinza texto-bold"
                                                                value="#{parcelasEditadas.descricao}"/>
                                                        <h:outputLabel  value="#{ContratoControle.empresaLogado.moeda}" styleClass="texto-size-16 texto-font texto-cor-cinza-2 texto-bold " style="padding: 3px"/>
                                                        <h:outputText
                                                                styleClass="texto-font texto-size-16 texto-cor-verde"
                                                                value="#{parcelasEditadas.valorParcela}">
                                                            <f:converter converterId="FormatadorNumerico"/>
                                                        </h:outputText>
                                                    </c:forEach>
                                                </h:panelGrid>
                                            </h:panelGroup>
                                        </c:if>

                                    </h:panelGrid>

                                </h:panelGroup>
                                <h:panelGroup layout="block" styleClass="valorItem col-text-align-right" style="height: 50px;"
                                              rendered="#{((ContratoControle.contratoVO.cobrarProdutoSeparado) || (ContratoControle.contratoVO.cobrarMatriculaSeparada))}">
                                    <a4j:commandLink action="#{ContratoControle.apresentarArrendondamento}"
                                                     rendered="#{ContratoControle.usarArredondamento}"
                                                     reRender="formArredondamento,panelProdutoParcela,valorFinalContrato"
                                                     styleClass="linkPadrao texto-font texto-size-14"
                                                     style="line-height:normal;margin-right: 15px;display:inline-block;"
                                                     value="#{msg_aplic.prt_arredondamento_parcelas}"
                                                     oncomplete="Richfaces.showModalPanel('painelArredondamento');">
                                    </a4j:commandLink>
                                    <a4j:commandLink
                                            rendered="#{LoginControle.permissaoAcessoMenuVO.permiteEditarValorParcelaNegociacao}"
                                            reRender="formArredondamento,panelProdutoParcela,valorFinalContrato, mensagem, editarParcelas"
                                            styleClass="linkPadrao texto-font texto-size-14"
                                            style="line-height:normal;margin-right: 15px; display:inline-block;"
                                            value="Editar"
                                            action="#{ContratoControle.montarModalEdicaoParcelasNegociacao}"
                                            oncomplete="Richfaces.showModalPanel('editarParcelas');">
                                    </a4j:commandLink>
                                </h:panelGroup>
                            </h:panelGroup>


                            <h:panelGroup layout="block" id="btnConcluirNegociacao" style="height: 83px;line-height: 83px;text-align: center;width:100%;display: inline-table">
                                <a4j:commandLink id="botaoConfirmarcao" action="#{ContratoControle.confirmarNegociacaoContrato}"
                                                 reRender="form:panelProdutoParcela, form:panelMesangem, form:planoCondicaoPagamentoVO,form:contratoVOPlanoProdutoSugerido ,form:contratoVO6 , form:movProduto , form:turma, form:modalidade, panelAutorizacaoFuncionalidade"
                                                 oncomplete="#{ContratoControle.onCompleteSemValidarBolsa};getPlaceHolderVitio();"
                                                 styleClass="botaoPrimarioMedio texto-font texto-size-16 #{ContratoControle.contratoVO.plano.bolsa ? '' : 'noBorderRight' }">
                                    Concluir <i class="fa-icon-ok texto-size-16 texto-cor-branco"></i
                                    <f:param name="tipo" value="FECHAR_NEGOCIACAO"/>
                                </a4j:commandLink>

                                <a4j:commandLink   id="botaoPagar"
                                                   styleClass="botaoPrimarioMedio texto-font texto-size-16 noBorderLeft borderBtnSeparetorLeft"
                                                   action="#{ContratoControle.confirmarNegociacaoContrato}"
                                                   reRender="dados, panelAutorizacaoFuncionalidade"
                                                   rendered="#{!ContratoControle.contratoVO.plano.bolsa}"
                                                   oncomplete="#{ContratoControle.onCompleteSemValidarBolsa};getPlaceHolderVitio();">
                                    Receber <i class="fa-icon-money texto-size-16 texto-cor-branco"></i>
                                    <f:param name="tipo" value="FECHAR_RECEBER_NEGOCIACAO"/>
                                </a4j:commandLink>
                            </h:panelGroup>
                            <h:panelGroup layout="block" id="btnCompartilharNegociacao" style="height: 83px;line-height: 83px;text-align: center;width:100%;display: inline-table">
                                <a4j:commandLink   id="botaoCompartilhar" style="background-color: #0090FF"
                                                   styleClass="botaoPrimarioMedio texto-font texto-size-16 borderBtnSeparetorLeft"
                                                   action="#{ContratoControle.confirmarNegociacaoContrato}"
                                                   reRender="dados, panelAutorizacaoFuncionalidade"
                                                   rendered="#{!ContratoControle.contratoVO.plano.bolsa}"
                                                   oncomplete="#{ContratoControle.onCompleteSemValidarBolsa};getPlaceHolderVitio();">
                                    Compartilhar com Cliente <i class="fa-icon-link texto-size-16 texto-cor-branco"></i>
                                    <f:param name="tipo" value="COMPARTILHAR"/>
                                </a4j:commandLink>
                            </h:panelGroup>
                            <h:panelGroup layout="block" style="width: 100%;margin: 0px 20px 20px 20px;" styleClass="gridValoresContrato col-text-align-left">

                                <a4j:commandLink  styleClass="linkPadrao texto-font texto-size-14"
                                                  action="#{ContratoControle.voltar}"
                                                  rendered="#{ContratoControle.apresentarBotoesFecharEReceber}">
                                    <i class="fa-icon-arrow-left texto-size-16 texto-cor-azul" ></i> Voltar
                                </a4j:commandLink>
                            </h:panelGroup>
                        </h:panelGroup>
                    </h:panelGroup>
                </h:panelGroup>
              </h:panelGroup>
            </h:panelGroup>
            <jsp:include page="include_box_menulateral.jsp" flush="true"/>
          </h:panelGroup>
        </h:panelGroup>
      </h:panelGroup>
      <jsp:include page="include_rodape_flat.jsp" flush="true"/>
    </h:panelGroup>
      <rich:jQuery id="mskData" selector=".rich-calendar-input" timing="onload" query="mask('99/99/9999');" />
        <script>
            carregarTooltipster();
        </script>
  </h:form>

    <rich:modalPanel id="panelOfertaVitio" autosized="true" onbeforeshow="getPlaceHolder()"  styleClass="balloon-modal modalVitio" minHeight="350" minWidth="1000" onresize="" moveable="true"
                      style="overflow: auto; max-height: 95vh" onmaskclick="Richfaces.hideModalPanel('panelOfertaVitio');">
        <h:form id="formVitio" style="overflow: hidden">
        <div style="">
            <h:graphicImage url="./imagens/logo-vitio.svg" width="89" height="23"/>
                    <a4j:commandButton id="botaoFechar"
                                       value="X"
                                       reRender="#{AutorizacaoFuncionalidadeControle.renderComponents}"
                                       style="cursor: pointer;float: right;font-family: 'Nunito Sans';background: 0;font-weight: 100;border-color: 0;font-size: 15px;border: 0;"
                                       oncomplete="#{AutorizacaoFuncionalidadeControle.onComplete};#{ContratoControle.mensagemNotificar};"
                                       action="#{ContratoControle.confirmarNegociacaoVitioSemCodigo}"/>
        </div>
        <div style="background-image: url('./imagens/imagem-modal-vitio.png');background-repeat: no-repeat;background-position: bottom right;background-size: 50% auto;">

        <h:panelGroup>
                <div class="textoModalVitusPopUp">
                    Chegou o momento <span style="color: #05E173"> de turbinar o Treino</span> do aluno!
                </div>
                <div style="margin-top: 32px;width: 563px;height: 44px;font-family: 'Nunito Sans';font-style: normal;font-weight: 100;font-size: 16px;line-height: 22px;color: #51555A;">
                    O apoio nutricional é essencial para todo o esforço no treino valer a pena.
                    <br>
                    Garanta <span style="font-weight: bold"> consultas online com nutricionistas de verdade</span> no Vitio
                    e muito mais!
                </div>

                <div style="margin-top:30px;width: 300px; height: 24px;font-family: 'Nunito Sans';height: 44px;font-style: normal; font-weight: 700; font-size: 16px; line-height: 24px;color: #878A92;">
                    Confira os dados do aluno:
                </div>

                    <div style="padding:5px;border-color: #80808087;border-width: 1px;border-radius: 4px;border-style: solid;width: 390px; height: 24px;">
                        <div style=" font-family: 'Nunito Sans';font-style: normal; font-weight: 700; font-size: 16px; line-height: 24px;color: #51555A;;">
                            <h:outputText value="#{ContratoControle.contratoVO.pessoa.nome}" rendered="#{ContratoControle.existeNomeCliente == true}"></h:outputText>
                            <h:inputText styleClass="inputModalVitus" rendered="#{ContratoControle.existeNomeCliente == false}" id="nomeCliente"></h:inputText>
                        </div>
                    </div>
                    <div style="margin-top: 10px;padding:5px;border-color: #80808087;border-width: 1px;border-radius: 4px;border-style: solid;width: 390px; height: 24px;">
                        <div style="font-family: 'Nunito Sans';font-style: normal; font-weight: 700; font-size: 16px; line-height: 24px;color: #51555A;">
                            <h:outputText value="#{ContratoControle.contratoVO.pessoa.email}" rendered="#{ContratoControle.existeEmailCliente == true}"></h:outputText>
                            <h:inputText styleClass="inputModalVitus" rendered="#{ContratoControle.existeEmailCliente == false}" id="emailCliente"></h:inputText>
                        </div>
                    </div>
                    <div style="margin-top: 10px;padding:5px;border-color: #80808087;border-width: 1px;border-radius: 4px;border-style: solid;width: 390px; height: 24px;">
                        <div style="font-family: 'Nunito Sans';font-style: normal; font-weight: 700; font-size: 16px; line-height: 24px;color: #51555A;">
                            <h:outputText value="#{ContratoControle.contratoVO.telefonesCliente}" rendered="#{ContratoControle.existeTelefoneCliente == true}"></h:outputText>
                            <h:inputText styleClass="inputModalVitus" rendered="#{ContratoControle.existeTelefoneCliente == false}" id="telefoneCliente"></h:inputText>
                        </div>
                    </div>

                    <div style="margin-top: 30px;width: 300px; height: 24px;font-family: 'Nunito Sans';font-style: normal; font-weight: 700; font-size: 16px; line-height: 24px;color: #878A92;">
                        Insira o seu código de afiliado:
                    </div>

                        <div style="margin-top: 16px;width: 400px;height: 150px; background-color: #F7F7F7;border-radius: 19px;text-align: center;margin-bottom: 30px">
                            <h:panelGroup rendered="#{ContratoControle.existeCodigoVitio}">
                                    <a4j:repeat var="caracter" value="#{ContratoControle.contratoVO.codigoVitio}">
                                        <div class="blocosVitio">
                                            <div style="margin-left: 6px;font-family: 'Nunito Sans';font-style: normal; font-weight: 400; font-size: 16px; line-height: 24px;color: #51555A;">
                                                <h:outputText value="#{caracter}"></h:outputText>
                                            </div>
                                        </div>
                                    </a4j:repeat>
                            </h:panelGroup>

                            <h:panelGroup rendered="#{!ContratoControle.existeCodigoVitio}">

                                    <h:inputText id="um" maxlength="1" onkeyup="if(this.value.length >= this.getAttribute('maxlength')) {#{rich:element('dois')}.focus()}" onchange="" styleClass="blocosVitio" style="text-align: center;height: 35px; width: 35px;margin-left: 6px;font-family: 'Nunito Sans';font-style: normal; font-weight: 400; font-size: 16px; line-height: 24px;color: #51555A;"></h:inputText>
                                    <h:inputText id="dois" maxlength="1" onkeyup="if(this.value.length >= this.getAttribute('maxlength')) {#{rich:element('tres')}.focus()}" styleClass="blocosVitio" style="text-align: center;height: 35px; width: 35px;margin-left: 6px;font-family: 'Nunito Sans';font-style: normal; font-weight: 400; font-size: 16px; line-height: 24px;color: #51555A;"></h:inputText>
                                    <h:inputText id="tres" maxlength="1" onkeyup="if(this.value.length >= this.getAttribute('maxlength')) {#{rich:element('quatro')}.focus()}" styleClass="blocosVitio" style="text-align: center;height: 35px; width: 35px;margin-left: 6px;font-family: 'Nunito Sans';font-style: normal; font-weight: 400; font-size: 16px; line-height: 24px;color: #51555A;"></h:inputText>
                                    <h:inputText id="quatro" maxlength="1" onkeyup="if(this.value.length >= this.getAttribute('maxlength')) {#{rich:element('cinco')}.focus()}" styleClass="blocosVitio" style="text-align: center;height: 35px; width: 35px;margin-left: 6px;font-family: 'Nunito Sans';font-style: normal; font-weight: 400; font-size: 16px; line-height: 24px;color: #51555A;"></h:inputText>
                                    <h:inputText id="cinco" maxlength="1" onkeyup="if(this.value.length >= this.getAttribute('maxlength')) {#{rich:element('seis')}.focus()}" styleClass="blocosVitio" style="text-align: center;height: 35px; width: 35px;margin-left: 6px;font-family: 'Nunito Sans';font-style: normal; font-weight: 400; font-size: 16px; line-height: 24px;color: #51555A;"></h:inputText>
                                    <h:inputText id="seis" maxlength="1" styleClass="blocosVitio" style="text-align: center;height: 35px; width: 35px;margin-left: 6px;font-family: 'Nunito Sans';font-style: normal; font-weight: 400; font-size: 16px; line-height: 24px;color: #51555A;"></h:inputText>

                            </h:panelGroup>

                            <div style="margin-left: 16px;width: 368px;border: 1px solid #DCDDDF;margin-top: 20px"></div>
                            <div style="margin-left: 80px;margin-top: 12px;font-family: 'Nunito Sans';font-style: normal;font-weight: 700;font-size: 16px;line-height: 24px;display: flex;align-items: center;color: #878A92;">
                                <img src="imagens/icone_whats.png" style="color: #878A92;">

                                <a4j:commandButton value=" Enviar eBook por Whatsapp" styleClass="botaoEnviarEbookPorWpp" style="margin-left: 8px;" action="#{ContratoControle.obtemLinkWPPVitioNaoAderiu}" oncomplete="#{ContratoControle.linkWPPVitioNaoAderiu};#{ContratoControle.mensagemNotificar};#{ContratoControle.onComplete}">
                                </a4j:commandButton>
                            </div>
                        </div>


                    <h:panelGroup layout="block" styleClass="container-botoes" style="text-align: initial; width: 116%">
                        <a4j:commandButton id="btnProsseguirComVitio"
                                           value="Prosseguir"
                                           reRender="#{AutorizacaoFuncionalidadeControle.renderComponents}"
                                           styleClass="botaoProsseguirVitio"
                                           oncomplete="#{ContratoControle.mensagemNotificar};#{AutorizacaoFuncionalidadeControle.onComplete};#{ContratoControle.linkWPPVitioAderiu}"
                                           action="#{ContratoControle.confirmarNegociacaoVitio}"/>
                    </h:panelGroup>
            </div>

            </h:panelGroup>
    </h:form>
    </rich:modalPanel>

  <rich:modalPanel id="painelArredondamento" autosized="true" styleClass="novaModal"
                   shadowOpacity="true" width="330" >
    <f:facet name="header">
      <h:panelGroup>
        <h:outputText value="Valores sugeridos"
                      style="font-weight: bold;"></h:outputText>
      </h:panelGroup>
    </f:facet>

    <f:facet name="controls">
      <h:panelGroup>
        <h:outputText styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign btnSelecionarModalidade" id="hidelinkArredondamento"/>
        <rich:componentControl for="painelArredondamento" attachTo="hidelinkArredondamento" operation="hide"  event="onclick"/>
      </h:panelGroup>
    </f:facet>

    <a4j:form id="formArredondamento" style="overflow: hidden;">

      <rich:dataGrid value="#{ContratoControle.valoresArredondados}"
                     var="parcela" width="100%" columns="1"
                     id="tableResults2" styleClass="tabelaSimplesCustom"
                     rendered="#{fn:length(ContratoControle.valoresArredondados) > 0}">
          <rich:column styleClass="col-texto-align-center" id="valor">
              <a4j:commandLink oncomplete="Richfaces.hideModalPanel('painelArredondamento');"
                               reRender="form"
                               id="valorArrendondar"
                               styleClass="linkPadrao"
                               action="#{ContratoControle.selecionarArredondamento}">
                  <h:panelGroup rendered="#{!ContratoControle.contratoVO.dividirProdutosNasParcelas && !ContratoControle.contratoVO.plano.vendaCreditoTreino && ContratoControle.numeroParcelaContrato > 0}">
                      <h:outputText styleClass="texto-font texto-size-16 texto-cor-azul" value="1x "/>
                      <h:outputText styleClass="texto-font texto-size-16 texto-cor-azul" value="#{parcela.valorEntrada}">
                          <f:converter converterId="FormatadorNumerico"/>
                      </h:outputText>

                      <rich:spacer width="12px"/>
                      <h:outputText styleClass="texto-font texto-size-16 texto-cor-azul" value=" #{ContratoControle.numeroParcelaContrato}x "/>
                      <h:outputText styleClass="texto-font texto-size-16 texto-cor-azul" value="#{parcela.valorParcelas}">
                          <f:converter converterId="FormatadorNumerico"/>
                      </h:outputText>
                  </h:panelGroup>

                  <h:panelGroup rendered="#{ContratoControle.contratoVO.dividirProdutosNasParcelas || ContratoControle.contratoVO.plano.vendaCreditoTreino || ContratoControle.numeroParcelaContrato < 1}">
                      <h:outputText styleClass="texto-font texto-size-16 texto-cor-azul" value="#{ContratoControle.contratoVO.planoCondicaoPagamento.condicaoPagamento.nrParcelas}x "/>
                      <h:outputText styleClass="texto-font texto-size-16 texto-cor-azul" value="#{parcela.valorParcelas}" >
                          <f:converter converterId="FormatadorNumerico"/>
                      </h:outputText>
                  </h:panelGroup>
              </a4j:commandLink>
          </rich:column>
      </rich:dataGrid>
      <h:panelGroup layout="block" styleClass="container-botoes">
        <a4j:commandLink reRender="form"
                         styleClass="botaoSecundario texto-font texto-size-16"
                         oncomplete="Richfaces.hideModalPanel('painelArredondamento');">
          Cancelar
        </a4j:commandLink>
      </h:panelGroup>
    </a4j:form>
  </rich:modalPanel>

    <rich:modalPanel id="modalCompartilharPagamentoContrato"  styleClass="novaModal noMargin" shadowOpacity="true"
                     width="650" autosized="true">
        <f:facet name="header">
            <h:panelGroup layout="block" id="panelSupeCompartilharPagamentoContrato">
                <h:outputText value="Compartilhando link de pagamento"/>
            </h:panelGroup>
        </f:facet>
        <f:facet name="controls">
<%--            <h:panelGroup>--%>
<%--                <h:outputText--%>
<%--                        styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign "--%>
<%--                        id="hdmodalCompartilharPagamentoContrato"/>--%>
<%--                <rich:componentControl for="modalCompartilharPagamentoContrato" attachTo="hdmodalCompartilharPagamentoContrato" operation="hide"--%>
<%--                                       event="onclick"/>--%>
<%--            </h:panelGroup>--%>
        </f:facet>
        <a4j:form ajaxSubmit="true">

            <style>
                .btncompartilhar a i{
                    font-size: 65px;
                    color: #0090FF;
                }
                .btncompartilhar:hover{
                    background-color: #dddddd;
                }
                .btncompartilhar{
                    display: inline-block;
                    width: 32%;
                    padding: 20px 0;
                    text-align: center;
                    vertical-align: top;
                }
                .btncompartilhar a:hover{
                    text-decoration: none;
                }
                .textolink{
                    font-size: 14px;
                    margin-top: 20px;
                    color: #474747;
                }

            </style>

            <h:panelGroup id="painelCompartilharContrato" layout="block" style="margin: 20px 10px; text-align: center">
                <h2>Como deseja compartilhar o link?</h2>

                <h:panelGroup layout="block" styleClass="btncompartilhar" rendered="#{not empty ContratoControle.contratoVO.cliente.pessoa.telefoneVOs}">
                    <a4j:commandLink styleClass="toolpister" title="Clique para enviar o link via Whatsapp"
                                     action="#{ContratoControle.linkCompartilharWhatsapp}"
                                     oncomplete="#{ContratoControle.msgAlert}">
                        <img src="imagens/whatsapp-logo.png"
                    </a4j:commandLink>

                    <div class="textolink">WhatsApp</div>
                </h:panelGroup>


                <div class="btncompartilhar">
                    <a4j:commandLink styleClass="toolpister" title="Clique para copiar para a área de trabalho"
                                     action="#{ContratoControle.linkCompartilhar}"
                                     oncomplete="#{ContratoControle.msgAlert}">
                        <i class="fa-icon-copy"></i>
                    </a4j:commandLink>
                    <div class="textolink">Copiar</div>
                </div>

                <h:panelGroup layout="block" style="margin-top: 20px" styleClass="container-botoes">
                    <a4j:commandLink status="false"
                                     action="#{ContratoControle.fecharModalCompartilharLink}"
                                     oncomplete="#{ContratoControle.onComplete}"
                                     value="Ok"
                                     styleClass="botaoPrimario texto-size-14-real"/>
                </h:panelGroup>

            </h:panelGroup>
        </a4j:form>
    </rich:modalPanel>

    <jsp:include page="includes/autorizacao/include_autorizacao_funcionalidade_nova.jsp" flush="true"/>
    <jsp:include page="includes/include_panelMensagem_goBackBlock.jsp" flush="true"/>
    <jsp:include page="include_edicaoParcelasNovo.jsp" flush="true"/>
</f:view>
</body>
