/*
 * FCKeditor - The text editor for Internet - http://www.fckeditor.net
 * Copyright (C) 2003-2008 <PERSON><PERSON>
 *
 * == BEGIN LICENSE ==
 *
 * Licensed under the terms of any of the following licenses at your
 * choice:
 *
 *  - GNU General Public License Version 2 or later (the "GPL")
 *    http://www.gnu.org/licenses/gpl.html
 *
 *  - GNU Lesser General Public License Version 2.1 or later (the "LGPL")
 *    http://www.gnu.org/licenses/lgpl.html
 *
 *  - Mozilla Public License Version 1.1 or later (the "MPL")
 *    http://www.mozilla.org/MPL/MPL-1.1.html
 *
 * == END LICENSE ==
 *
 * Styles used by the editor IFRAME and Toolbar.
 */

/*
	### Basic Editor IFRAME Styles.
*/

body
{
	padding: 1px;
	margin: 0;
	background-color: #ffffff;
}

#xEditingArea
{
	border: #696969 1px solid;
}

.SourceField
{
	padding: 5px;
	margin: 0px;
	font-family: Monospace;
}

/*
	Toolbar
*/

.TB_ToolbarSet, .TB_Expand, .TB_Collapse
{
    cursor: default;
	background-color: #f7f7f7;
}

.TB_ToolbarSet
{
	padding: 1px;
	border-top: #efefde 1px outset;
	border-bottom: #efefde 1px outset;
}

.TB_ToolbarSet TD
{
	font-size: 11px;
	font-family: 'Microsoft Sans Serif' , Tahoma, Arial, Verdana, Sans-Serif;
}

.TB_Toolbar
{
    display: inline-table;
}

.TB_Separator
{
    width: 1px;
    height: 21px;
    margin: 2px;
    background-color: #C6C3BD;
}

.TB_Start
{
    background-image: url(images/toolbar.start.gif);
    margin-left: 2px;
    margin-right: 2px;
    width: 3px;
    background-repeat: no-repeat;
    height: 27px;
    background-position: center center;
}

.TB_End
{
	display: none;
}

.TB_ExpandImg
{
	background-image: url(images/toolbar.expand.gif);
	background-repeat: no-repeat;
}

.TB_CollapseImg
{
	background-image: url(images/toolbar.collapse.gif);
	background-repeat: no-repeat;
}

.TB_SideBorder
{
	background-color: #696969;
}

.TB_Expand, .TB_Collapse
{
	padding: 2px 2px 2px 2px;
	border: #efefde 1px outset;
}

.TB_Collapse
{
	border: #efefde 1px outset;
	width: 5px;
}

.TB_Break
{
	height: 27px;
}

/*
	Toolbar Button
*/

.TB_Button_On, .TB_Button_Off, .TB_Button_On_Over, .TB_Button_Off_Over, .TB_Button_Disabled
{
	padding: 1px ;
	margin:1px;
	height: 21px;
}

.TB_Button_On, .TB_Button_Off, .TB_Button_On_Over, .TB_Button_Off_Over, .TB_Button_Disabled
{
	border: #cec6b5 1px solid;
}

.TB_Button_On
{
	border-color: #316ac5;
	background-color: #c1d2ee;
}

.TB_Button_On_Over, .TB_Button_Off_Over
{
    border: #316ac5 1px solid;
    background-color: #dff1ff;
}

.TB_Button_Off
{
	background: #efefef url(images/toolbar.buttonbg.gif) repeat-x;
}

.TB_Button_Off, .TB_Combo_Off
{
	opacity: 0.70; /* Safari, Opera and Mozilla */
	filter: alpha(opacity=70); /* IE */
	/* -moz-opacity: 0.70; Mozilla (Old) */
}

.TB_Button_Disabled
{
    opacity: 0.30; /* Safari, Opera and Mozilla */
    filter: gray() alpha(opacity=30); /* IE */
}

.TB_Button_Padding
{
    visibility: hidden;
    width: 3px;
    height: 21px;
}

.TB_Button_Image
{
    overflow: hidden;
    width: 16px;
    height: 16px;
    margin: 3px;
    margin-top: 4px;
    margin-bottom: 2px;
    background-repeat: no-repeat;
}

/* For composed button ( icon + text, icon + arrow ), we must compensate the table */
.TB_Button_On TABLE .TB_Button_Image,
.TB_Button_Off TABLE .TB_Button_Image,
.TB_Button_On_Over TABLE .TB_Button_Image,
.TB_Button_Off_Over TABLE .TB_Button_Image,
.TB_Button_Disabled TABLE .TB_Button_Image
{
    margin-top: 3px;
}

.TB_Button_Image img
{
    position: relative;
}

.TB_ConnectionLine
{
    background-color: #ffffff;
    height: 1px;
    margin-left: 1px;   /* ltr */
    margin-right: 1px;  /* rtl */
}

/*
	Menu
*/

.MN_Menu
{
    border: 1px solid #8f8f73;
    padding: 2px;
    background-color: #f7f7f7;
    cursor: default;
}

.MN_Menu, .MN_Menu .MN_Label
{
    font-size: 11px;
    font-family: 'Microsoft Sans Serif' , Tahoma, Arial, Verdana, Sans-Serif;
}

.MN_Item_Padding
{
    visibility: hidden;
    width: 3px;
    height: 20px;
}

.MN_Icon
{
    background-color: #dedede;
    text-align: center;
    height: 20px;
}

.MN_Label
{
    padding-left: 3px;
    padding-right: 3px;
}

.MN_Separator
{
    height: 3px;
}

.MN_Separator_Line
{
    border-top: #b9b99d 1px solid;
}

.MN_Item .MN_Icon IMG
{
    filter: alpha(opacity=70);
    opacity: 0.70;
}

.MN_Item_Over
{
    color: #ffffff;
    background-color: #8a857d;
}

.MN_Item_Over .MN_Icon
{
    background-color: #6c6761;
}

.MN_Item_Disabled IMG
{
    filter: gray() alpha(opacity=30); /* IE */
    opacity: 0.30; /* Safari, Opera and Mozilla */
}

.MN_Item_Disabled .MN_Label
{
    color: #b7b7b7;
}

.MN_Arrow
{
    padding-right: 3px;
    padding-left: 3px;
}

.MN_ConnectionLine
{
    background-color: #ffffff;
}

.Menu .TB_Button_On, .Menu .TB_Button_On_Over
{
    border: #8f8f73 1px solid;
    background-color: #ffffff;
}

/*
	### Panel Styles
*/

.FCK_Panel
{
    border: #8f8f73 1px solid;
    padding: 2px;
    background-color: #ffffff;
}

.FCK_Panel, .FCK_Panel TD
{
    font-family: 'Microsoft Sans Serif' , Tahoma, Arial, Verdana, Sans-Serif;
    font-size: 11px;
}

/*
	### Special Combos
*/

.SC_Panel
{
    overflow: auto;
    white-space: nowrap;
    cursor: default;
    border: 1px solid #8f8f73;
    padding-left: 2px;
    padding-right: 2px;
}

.SC_Panel, .SC_Panel TD
{
    font-size: 11px;
    font-family: 'Microsoft Sans Serif' , Tahoma, Arial, Verdana, Sans-Serif;
}

.SC_Item, .SC_ItemSelected
{
    margin-top: 2px;
    margin-bottom: 2px;
    background-position: left center;
    padding-left: 11px;
    padding-right: 3px;
    padding-top: 2px;
    padding-bottom: 2px;
    text-overflow: ellipsis;
    overflow: hidden;
    background-repeat: no-repeat;
    border: #dddddd 1px solid;
}

.SC_Item *, .SC_ItemSelected *
{
    margin-top: 0px;
    margin-bottom: 0px;
}

.SC_ItemSelected
{
    border: #9a9afb 1px solid;
    background-image: url(images/toolbar.arrowright.gif);
}

.SC_ItemOver
{
    border: #316ac5 1px solid;
}

.SC_Field
{
    margin-top:1px ;
    border: #b7b7a6 1px solid;
    cursor: default;
}

.SC_FieldCaption
{
    padding-top: 1px ;
    overflow: visible;
    padding-right: 5px;
    padding-left: 5px;
    opacity: 0.75; /* Safari, Opera and Mozilla */
    filter: alpha(opacity=70); /* IE */ /* -moz-opacity: 0.75; Mozilla (Old) */
    height: 23px;
    background-color: #f7f7f7;
}

.SC_FieldLabel
{
    white-space: nowrap;
    padding: 2px;
    width: 100%;
    cursor: default;
    background-color: #ffffff;
    text-overflow: ellipsis;
    overflow: hidden;
}

.SC_FieldButton
{
    background-position: center center;
    background-image: url(images/toolbar.buttonarrow.gif);
    border-left: #b7b7a6 1px solid;
    width: 14px;
    background-repeat: no-repeat;
}

.SC_FieldDisabled .SC_FieldButton, .SC_FieldDisabled .SC_FieldCaption, .SC_FieldDisabled .TB_ButtonType_Text
{
    opacity: 0.30; /* Safari, Opera and Mozilla */
    filter: gray() alpha(opacity=30); /* IE */ /* -moz-opacity: 0.30; Mozilla (Old) */
}

.SC_FieldOver
{
    border: #316ac5 1px solid;
}

.SC_FieldOver .SC_FieldButton
{
    border-left: #316ac5 1px solid;
}

/*
	### Color Selector Panel
*/

.ColorBoxBorder
{
    border: #808080 1px solid;
    position: static;
}

.ColorBox
{
    font-size: 1px;
    width: 10px;
    position: static;
    height: 10px;
}

.ColorDeselected, .ColorSelected
{
    cursor: default;
}

.ColorDeselected
{
    border: #ffffff 1px solid;
    padding: 2px;
    float: left;
}

.ColorSelected
{
    border: #316ac5 1px solid;
    padding: 2px;
    float: left;
    background-color: #c1d2ee;
}
