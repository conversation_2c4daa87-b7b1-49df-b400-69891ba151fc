<%@ page import="java.util.Calendar" %>
<%@page contentType="text/html" %>
<%@page pageEncoding="ISO-8859-1" %>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html" %>
<%@taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax" %>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich" %>
<%@taglib prefix="f" uri="http://java.sun.com/jsf/core" %>

<c:set scope="page" var="diffTime"
       value="<%=(session.getLastAccessedTime() + session.getMaxInactiveInterval() * 1000L) - Calendar.getInstance().getTimeInMillis()%>"/>
<c:set scope="page" var="urlAfterLogout" value="${LogoutControle.URL}"/>
<script type="text/javascript" src="${pageContext.request.contextPath}/script/tooltipster/jquery.tooltipster.min.js"></script>

<h:panelGroup id="panelGroupNovoMenu" rendered="#{SuperControle.menuZwUi}"
              styleClass="panel-group-novo-menu"
              style="position: fixed; display: flex;font-family: 'Nunito Sans',sans-serif;">
    <link href="${root}/css/menu_zw_ui_v1.20.css" rel="stylesheet" type="text/css">
    <link href="${root}/bootstrap/bootplus.min.css" rel="stylesheet">

    <script>
        var terceiroNivelSubMenus = [".ADM_PRODUTOS", ".ADM_PLANOS", ".ADM_TURMAS"];
        var executed = false;
        var contador = 0;

        const moduloAbertoSigla = '${LoginControle.moduloAbertoSigla}';
        var alturaTela = screen.availHeight;
        const modulosJson = JSON.parse('${MenuControle.modulosJson}');
        var tamanhoJson = modulosJson.modulos.length;
        var qtdePorPagina;
        const listaPagina=[];
        if(alturaTela<720){
            qtdePorPagina = 5;
        }else{
            qtdePorPagina = 6;
        }

        window.addEventListener('load', (event) =>{
            montaModulomenu();
        });

        window.isRenderedModuloMenu = function(){
            return jQuery("#modules-menu").length > 0;
        }

        function montaModulomenu() {

            var toalPagina = Math.ceil (tamanhoJson /qtdePorPagina);

            for (var i = 0; i < toalPagina; i++) {
                var start = i * qtdePorPagina;
                var end = Math.min(start + qtdePorPagina, tamanhoJson);
                listaPagina.push(modulosJson.modulos.slice(start,end));
            }

            var page = 1;
            listaPagina.forEach(
                (elemento) => {
                    jQuery("#modules-menu").append('<div class="modules-menu-page" id="modules-menu-page-'+page+'">');
                    elemento.forEach(
                        (elementoSecundario) => {
                            var m = elementoSecundario;
                            jQuery("#modules-menu-page-"+page).append('<a class="modules-menu-icon '+(moduloAbertoSigla == m.siglaModulo ? "modules-menu-icon-selected":"" )+ ' tituloModulos" onclick="abrirModulo(\''+m.siglaModulo+'\', \'LATERAL_MODULO\')" style="font-size: 24px;">' +
                                '<img src="${root}/'+m.iconeRelaiveUrl+'"' +
                                ' data-cy="modulo-sigla-'+m.siglaModulo+'"' +
                                ' alt="'+m.descricao+'"' +
                                ' style="float: left; margin-bottom: 14px;cursor: pointer;"' +
                                ' width="24" height="24"' +
                                ' title="'+m.descricao+'"' +
                                ' class="tooltipsterright"' +
                                ' /> ');
                            jQuery("#modules-menu-page-"+page).append('</a>');
                        }
                    );
                    jQuery("#modules-menu").append('</div>');
                    page++;
                }
            );
        }


        jQuery(window).on("load", function () {
            calcularRodape();
        });

        function abrirSubmenu(classe, aPartirDe) {
            if (aPartirDe) {
                voltarSubmenu(aPartirDe)
            }

            if (isClasseTerceiroNivelSubMenu(classe)) {
                removeClassesComTresNiveisSubMenu();
            }

            if (jQuery(classe)[0]) {
                jQuery(classe).addClass('opensubmenu');
                jQuery('.menuAcessoRapido').hide();
                jQuery('.titulomenu-zwui').hide();
            }
        }

        function isClasseTerceiroNivelSubMenu(classe) {
            return terceiroNivelSubMenus.indexOf(classe) !== -1;
        }

        function removeClassesComTresNiveisSubMenu() {
            jQuery('.ADM_CADASTROS').removeClass('opensubmenu');
        }

        function voltarSubmenu(classe, retornarPara) {
            if (classe == ".ADM_PRODUTOS_PLANOS_TURMAS") {
                retornarPara = ".ADM_CADASTROS";
            }
            if (classe == '.ADM_CONTROLE_ESTOQUE') {
                retornarPara = '.ADM_PRODUTOS';
            }
            jQuery(classe).removeClass('opensubmenu');
            jQuery('.menuAcessoRapido').show();
            jQuery('.titulomenu-zwui').show();

            if (retornarPara) {
                abrirSubmenu(retornarPara)
            }
        }

        function fecharMenu() {
            const event = arguments[0] || window.event;
            event.stopPropagation();
            // jQuery('.zw_ui_topo').css({"z-index": "1000"});
            jQuery('.zw_ui_info_empresa').addClass('fechado-zwui');
            jQuery('.zw_ui_opcoes_laterais').addClass('fechado-zwui');
            jQuery('.zw_ui_modulos_pesquisa').addClass('fechado-zwui');
            jQuery('.container-box.zw_ui').addClass('fechado-zwui');
            jQuery('.container-imagem').addClass('fechado-zwui');
            jQuery('.caixa-modulos-zw-ui').addClass('fechado-zwui');
            jQuery('.caixaCorpo.zw_ui').addClass('fechado-zwui');
            jQuery('.modal-ponto-interrogacao').addClass('fechado-zwui');
            jQuery('.menuLateralNota').css('padding-left', jQuery('#form\\:panelGroupNovoMenu').width());
        }

        function isTelaModuloPessoas(){
            return ['/faces/clientes.jsp', '/faces/preCadastro.jsp'].some((page) => window.location.pathname.includes(page));
        }

        function onCompleteBtnVoltar(){
            if(isTelaModuloPessoas()){
                history.back();
            }
        }

        function abrirMenu() {
            // jQuery('.zw_ui_topo').css({"z-index": "1100"});
            jQuery('.caixaCorpo.zw_ui').removeClass('fechado-zwui');
            jQuery('.container-imagem').removeClass('fechado-zwui');
            jQuery('.zw_ui_info_empresa').removeClass('fechado-zwui');
            jQuery('.zw_ui_opcoes_laterais').removeClass('fechado-zwui');
            jQuery('.zw_ui_modulos_pesquisa').removeClass('fechado-zwui');
            jQuery('.container-box.zw_ui').removeClass('fechado-zwui');
            jQuery('.caixa-modulos-zw-ui').removeClass('fechado-zwui');
            jQuery('#form\\:menuLateralNotaFiscal').css('padding-left', jQuery('#form\\:panelGroupNovoMenu').width());
            jQuery('.modal-ponto-interrogacao').removeClass('fechado-zwui');
        }

        function abrirModulos() {
            jQuery('.caixa-modulos-zw-ui').addClass('aberto');
            jQuery('.modulo-opener').addClass('aberto');
        }

        function fecharModulos() {
            jQuery('.caixa-modulos-zw-ui').removeClass('aberto');
            jQuery('.modulo-opener').removeClass('aberto');
        }

        function abrirUsuario(event) {
            jQuery('.blue-triangle').hide();
            jQuery('.blue-container').hide();
            jQuery('.blue-container-assinatura').hide();
            jQuery('.blue-container-usuario').show();
            jQuery('.container-user-box-zw-ui').show();
            jQuery('.container-assinatura-box-zw-ui').hide();
            jQuery('.container-favoritos-box-zw-ui').hide();
            jQuery('.balloon-modal').removeClass('assinatura');
            jQuery('.balloon-modal').addClass('menu-usuario');
            jQuery('.modal-usuario-zw-ui').addClass('modal-open');
            jQuery('.section-menu').addClass('aberto-usuario-zw-ui');
            jQuery('.modal-conteudo').addClass('menu-usuario');
            const target = event.target;
            const parentDropDown = target.closest('.background') || target.closest('.topbar-menu-item-usuario');
            console.log(parentDropDown);
            const dropdown = document.getElementById('dropDownUsuario');
            dropdown.classList.add('open');
            dropdown.style.left = parentDropDown.getBoundingClientRect().left - 250 + 'px';
        }

        function abrirPontoInterrogacao() {
            try {
                let elemento = document.getElementById('idpontointerrogacao');
                if (elemento) {
                    let left = getOffset(elemento).left;
                    document.getElementById("id-baloon-ponto-interrogacao").style.left = (left - 338) + 'px';
                    elemento.classList.add('topbar-item-selected');
                }
            } catch (e) {
                console.log(e);
            }

            clearConhecimento();
            initConhecimento();
            jQuery('.modal-ponto-interrogacao').addClass('modal-open');
        }

        function abrirNotificacoes(aba) {
            try {
                let left = getOffset(document.getElementById('idNotificacoesVersoes')).left;
                document.getElementById("id-notificacoes-versoes").style.left = (left - 410) + 'px';
                document.getElementById('idNotificacoesVersoes').classList.add('topbar-item-selected');
            } catch (e) {
                console.log(e);
            }
            initTotalNotificacoes(true);
            registrarAcaoNotificacao('CLICOU_MENU_NOTIFICACOES', '')
            selecionarAba(aba ? aba : 'aba-alertas');
            jQuery('.modal-notificacoes-versoes').removeClass('hidden');
            jQuery('.modal-notificacoes-versoes').addClass('modal-open');
        }

        function getOffset(el) {
            const rect = el.getBoundingClientRect();
            return {
                left: rect.left + window.scrollX,
                top: rect.top + window.scrollY
            };
        }

        function abrirAssinatura(event) {
            jQuery('.balloon-modal').addClass('assinatura');
            jQuery('.container-user-box-zw-ui').hide();
            jQuery('.blue-triangle').hide();
            jQuery('.blue-container').hide();
            jQuery('.blue-container-usuario').hide();
            jQuery('.blue-container-assinatura').show();
            jQuery('.container-favoritos-box-zw-ui').hide();
            jQuery('.container-assinatura-box-zw-ui').show();
            jQuery('.modal-conteudo').removeClass('menu-usuario');
            jQuery('.modal-usuario-zw-ui').addClass('modal-open');
            jQuery('#topbar-menu-item-qrcode').addClass('topbar-item-selected');

            const target = event.target;
            const parentDropDown = target.closest('.assinaturaTrigger');
            const dropdown = document.getElementById('dropDownUsuario');
            dropdown.classList.add('open');
            dropdown.style.left = parentDropDown.getBoundingClientRect().left - 120 + 'px';
        }

        function escondeIconeLembrete(codigoCliente) {
            if (document.getElementsByClassName('imagem-lembrete-nulo' + codigoCliente).length > 0) {
                document.getElementsByClassName('imagem-lembrete-nulo' + codigoCliente)[0].style.visibility = 'hidden';
            }
        }

        function mostraIconeLembrete(codigoCliente) {
            if (document.getElementsByClassName('imagem-lembrete-nulo' + codigoCliente).length > 0) {
                document.getElementsByClassName('imagem-lembrete-nulo' + codigoCliente)[0].style.visibility = '';
            }
        }

        function mostraToolTipLembrete(codigoCliente) {
            if (document.getElementsByClassName('tooltip-lembrete' + codigoCliente).length > 0) {
                document.getElementsByClassName('tooltip-lembrete' + codigoCliente)[0].style.display = '';
            }
        }

        function escondeToolTipLembrete(codigoCliente) {
            if (document.getElementsByClassName('tooltip-lembrete' + codigoCliente).length > 0) {
                document.getElementsByClassName('tooltip-lembrete' + codigoCliente)[0].style.display = 'none';
            }
        }

        function escondeInputLembrete(codigosFavoritos, codigoFavoritoClick) {
            if (!(codigosFavoritos === '')) {
                var listCodigoFavoritos = codigosFavoritos.replace("[", "").replace("]", "").split(", ");
                for (const elemento of listCodigoFavoritos) {
                    jQuery(document).find('.input.' + elemento).css('display', 'none');
                    if ((codigoFavoritoClick !== null && elemento == codigoFavoritoClick)) {
                        mostraToolTipLembrete(codigoFavoritoClick);
                    }
                }
            } else {
                jQuery(document).find('.input').css('display', 'none');
            }

        }

        function mostraInputLembrete(codigosFavoritos) {
            var listCodigoFavoritos = codigosFavoritos.replace("[", "").replace("]", "").split(", ");

            for (const elemento of listCodigoFavoritos) {
                var lembrete = jQuery(document).find('.input.'+elemento);

                if (lembrete.css('display') !== 'none') {
                    lembrete.css('display', 'none');
                } else {
                    lembrete.css('display', '');
                    lembrete.css('left', $('.tooltip-lembrete'+elemento).eq(0).position().left - 50 + 'px');
                }
            }
        }

        function escondeLembrete() {
            document.getElementsByClassName('cmarc')[0].style.display = 'none';
        }

        function abrirFavoritos(event) {
            jQuery('.balloon-modal').addClass('favoritos')
            jQuery('.container-user-box-zw-ui').hide();
            jQuery('.container-assinatura-box-zw-ui').hide();
            jQuery('.blue-container').hide();
            jQuery('.blue-triangle').hide();
            jQuery('.blue-container-usuario').hide();
            jQuery('.blue-container-assinatura').hide();
            jQuery('.container-favoritos-box-zw-ui').show();
            jQuery('.modal-conteudo').removeClass('menu-usuario');
            jQuery('#topbar-menu-item-fav').addClass('topbar-item-selected');

            jQuery('.modal-usuario-zw-ui').addClass('modal-open');
            jQuery('.section-menu').addClass('aberto-usuario-zw-ui');
            jQuery('.campo-favoritos').show();

            const target = event.target;
            const parentDropDown = target.closest('.favoritos');
            const dropdown = document.getElementById('dropDownUsuario');
            dropdown.classList.add('open');
            dropdown.style.left = parentDropDown.getBoundingClientRect().left - 200 + 'px';
            dropdown.style.width = parentDropDown.getBoundingClientRect().width + 260 + 'px';
            document.getElementById('modal-conteudo-menu-superior').style.width = dropdown.style.width;
            document.getElementById('container-zw-ui').style.borderRadius = '3em 3em 0.9em 0.9em';
            document.getElementById('modal-content-usuario-zw-ui').style.borderRadius = '2rem'
            document.getElementById('modal-content-usuario-zw-ui').style.border = '0';
            jQuery('.form-search-zw').show()
        }

        function esconderFavorito(mensagemRetorno, codigoClienteMarcado) {
            console.log(mensagemRetorno);
            if (mensagemRetorno === '') {
                document.getElementsByClassName('FAVORITO' + codigoClienteMarcado)[0].style.display = 'none';
            }
        }

        function esconderRecenteFavorito(mensagemRetorno, codigoClienteMarcado) {
            if (mensagemRetorno == '') {
                document.getElementsByClassName('RECENTE' + codigoClienteMarcado)[0].style.display = 'none';
            }
        }

        function fecharUsuario() {
            jQuery('.blue-container').show();
            jQuery('.blue-triangle').show();
            const dropdown = document.getElementById('dropDownUsuario');
            dropdown.style.width = 170 + 'px';
            jQuery('.balloon-modal').removeClass('assinatura');
            jQuery('.balloon-modal').removeClass('novidades');
            jQuery('.modal-usuario-zw-ui').removeClass('modal-open');
            jQuery('.section-menu').removeClass('aberto-usuario-zw-ui');
            jQuery('.topbar-menu-item').removeClass('topbar-item-selected');
            const modal = document.getElementById('modal-conteudo-menu-superior');
            document.getElementById('container-zw-ui').style.borderRadius = '';
            document.getElementById('modal-content-usuario-zw-ui').style.border = ''
            // const modalTopo = document.getElementeById('modal-content-usuario-zw-ui').style.borderRadius = '';
            modal.style.width = '';
        }
        function fecharMsgFavorito() {
            jQuery('.modal-favorito-zw-ui').removeClass('modal-open');
        }
        function fecharPonto() {
            jQuery('.modal-ponto-interrogacao').removeClass('modal-open');
            jQuery('.topbar-menu-item').removeClass('topbar-item-selected');
            clearConhecimento();
            jQuery('#conteudo-modal-mkt').html('');
        }
        function fecharNotificacoes() {
            jQuery('.modal-notificacoes-versoes').removeClass('modal-open');
            jQuery('.topbar-menu-item').removeClass('topbar-item-selected');
        }

        function fecharMenuExplorar(){
            if(jQuery('.caixa-modulos-zw-ui.fechado-zwui.aberto').length > 0){
                toggleModulos();
            }
        }

        function toggleModulos() {
            jQuery('.caixa-modulos-zw-ui').toggleClass('aberto');
            jQuery('.modulo-opener').toggleClass('aberto');
            redimensionarMenuExplorar();
        }

        function redimensionarMenuExplorar(){
            // Seta o tamanho do dropdown de acordo com o tamanho da topbar
            const topbarElement = document.getElementById('zw_ui_modulos_pesquisa').getBoundingClientRect();
            const explorarMenuElement = document.getElementById('form:caixa-modulos-zw-ui');
            explorarMenuElement.style.top = topbarElement.height + 'px';
            explorarMenuElement.style.left = topbarElement.left + 'px';
            explorarMenuElement.style.width = topbarElement.width + 'px';
            explorarMenuElement.style.zIndex = '1';
        }

        function marcaFuncionalidadeAtivada(funcionalidade) {
            jQuery('.titulo3.MENU-' + funcionalidade).parent().parent().css({
                "border-left": "solid 4px #bde5fe"
            })
        }

        document.addEventListener('click', (e) => {
            const clickElement = e.target;
            const explorarMenuElement = document.getElementById('form:caixa-modulos-zw-ui');
            const menuExplorarTrigger = document.getElementById('explorar-trigger');
            const menuExplorarTriggerall = document.getElementById('explorar-trigger-all');
            if (clickElement !== explorarMenuElement && !isDescendant(explorarMenuElement, clickElement)
                && clickElement !== menuExplorarTrigger && !isDescendant(menuExplorarTrigger, clickElement)
                && clickElement !== menuExplorarTriggerall && !isDescendant(menuExplorarTriggerall, clickElement)
            ) {
                fecharModulos();
            }
        });

        function isDescendant(parentElement, childElement) {
            let node = childElement.parentNode;
            if (parentElement === childElement) {
                return true;
            } else {
                while (node !== null) {
                    if (node === parentElement) {
                        return true;
                    }
                    node = node.parentNode;
                }
                return false;
            }
        }

        function abrirMenuAcessoRapido() {
            jQuery('.caixaCorpo.zw_ui').removeClass('fechado-zwui');
            jQuery('.container-imagem').removeClass('fechado-zwui');
            jQuery('.zw_ui_info_empresa').removeClass('fechado-zwui');
            jQuery('.zw_ui_opcoes_laterais').removeClass('fechado-zwui');
            jQuery('.zw_ui_modulos_pesquisa').removeClass('fechado-zwui');
            if(window.location.pathname.indexOf('/faces/notaFiscal.jsp') !== -1){
                jQuery('.caixaMenuLatel').css('display', 'block');
                jQuery('#form\\:menuLateralNotaFiscal').css('display', 'none');
            }
        }
        function fecharMenuAcessoRapido(){
            jQuery('.caixaCorpo.zw_ui').addClass('fechado-zwui');
            jQuery('.container-imagem').addClass('fechado-zwui');
            jQuery('.zw_ui_info_empresa').addClass('fechado-zwui');
            jQuery('.zw_ui_opcoes_laterais').addClass('fechado-zwui');
            jQuery('.zw_ui_modulos_pesquisa').addClass('fechado-zwui');
            jQuery('.container-imagem').removeClass('close');
            jQuery('.container-imagem-configuracao').removeClass('open');
            if(window.location.pathname.indexOf('/faces/notaFiscal.jsp') !== -1){
                jQuery('.caixaMenuLatel').css('display', 'none');
                jQuery('#form\\:menuLateralNotaFiscal').css('display', 'initial');
            }
        }
        function abrirMsgNotificacao() {
            jQuery('.modal-favorito-zw-ui').animate({left:'400'},0);
            jQuery('.green-container-favorito').show();
            jQuery('.modal-favorito-zw-ui').animate({left:'0'},350);
            jQuery('.modal-favorito-zw-ui').addClass('modal-open');
            jQuery('.top-container-favorito').addClass('green-top-container-favorito');
            jQuery('.container-favorito').addClass('green-container-favorito');
            jQuery('.icoMsg').addClass('pct pct-check');
            jQuery('.msg-container').addClass('msg-container-green');
            const dropdown = document.getElementById('modalNatificacao');
            jQuery('.msg-title').addClass('msg-title-green');
            dropdown.classList.add('open');
            setTimeout(function(){ fechaMesg () }, 5000);
        }

        function abrirMsgErro() {
            jQuery('.modal-favorito-zw-ui').animate({left:'400'},0);
            jQuery('.red-container-favorito-cheio').show();
            jQuery('.modal-favorito-zw-ui').animate({left:'0'},350);
            jQuery('.modal-favorito-zw-ui').addClass('modal-open');
            jQuery('.top-container-favorito').addClass('red-top-container-favorito');
            jQuery('.container-favorito').addClass('red-container-favorito-cheio');
            jQuery('.icoMsg').addClass('pct pct-alert-triangle');
            jQuery('.msg-container').addClass('msg-container-red');
            jQuery('.msg-title').addClass('msg-title-red');
            const dropdown = document.getElementById('modalNatificacao');
            dropdown.classList.add('open');
            setTimeout(function(){ fechaMesg () }, 5000);

        }
        function abrirMsgNotificacaoCanalCliente(){
            jQuery('.green-container-favorito').show();
            jQuery('.modal-notificacao-canal-do-cliente').addClass('modal-open');
            const dropdown = document.getElementById('modalNatificacao');
            dropdown.classList.add('open');
        }
        function fecharMsgNotificacao() {
            jQuery('.modal-notificacao-canal-do-cliente').removeClass('modal-open');
        }
        function abrirModalUsuario(event) {
            jQuery('.modal-notificacao-canal-do-cliente').removeClass('modal-open');
            jQuery("#divAbrirUsuarioADM").trigger('click');
            jQuery("#divAbrirUsuario").trigger('click');
        }

        function fechaMesg (){
            jQuery('.modal-favorito-zw-ui').animate({left:'400'},350);
        }
        function calcularRodape(){
            var usuarioPACTOBR = '${LoginControle.usuarioLogado.usuarioPACTOBR}';
            if(usuarioPACTOBR=='false'){
                jQuery('.container-mask').removeClass('menus');
                jQuery('.container-mask').addClass('menus-user-padrao');
            }
        }

        function exibirMenuVoltar(){
            if(${MenuControle.exibirBtnVoltar}){
                jQuery('.zw_ui_modulo_atual_descricao').hide();
                jQuery('.zw_ui_retornar').css("display", "flex");
                jQuery('.zw_ui_modulo_atual').hide();
                jQuery('.zw_ui_modulo_configuracao').show();
                jQuery('#zwUiModuloAtualIconeVoltar').addClass('color-retornar');
            }
        }

        function ocultarMenuVoltar()
        {
            jQuery('#zwUiRetornarDescricao').hide();
            jQuery('#zwUiModuloAtualDescricao').show();
            jQuery('#zwUiModuloAtualIconeVoltar').removeClass('color-retornar');
            // jQuery('.zw_ui_modulo_atual_descricao').show();
            // jQuery('.zw_ui_retornar').hide();
            // jQuery('.zw_ui_modulo_atual').show();
            // jQuery('.zw_ui_modulo_configuracao').hide();
        }
        function mostrarContainerImagem(){
            jQuery('.container-imagem').removeClass('close');
            jQuery('.container-imagem-configuracao').removeClass('open');
            jQuery('#td-pesquisa-clientes').show();
        }

        function mostrarContainerImagemConfiguracao(){
            let customLayout = false;

            const paginaFluxoCaixa = window.location.pathname.indexOf('faces/pages/finan/fluxoCaixa.jsp') !== -1;
            const paginaDre = window.location.pathname.indexOf('faces/pages/finan/dre.jsp') !== -1;
            const paginaDemonstrativo = window.location.pathname.indexOf('/faces/pages/finan/demonstrativoFinanceiro.jsp') !== -1;
            const paginaPlanoContas = window.location.pathname.indexOf('/faces/pages/finan/planoContaCons.jsp') !== -1;
            const paginaGestaoRemessas = window.location.pathname.indexOf('/faces/gestaoRemessas.jsp') !== -1;
            const paginaGestaoVendasOnline = window.location.pathname.indexOf('/faces/gestaoVendasOnline.jsp') !== -1;
            const paginaRateio = window.location.pathname.indexOf('/faces/pages/finan/rateioIntegracaoCons.jsp') !== -1;
            const paginaCliente = window.location.pathname.indexOf('/faces/cliente.jsp') !== -1;

            const htmlLayoutLegado = paginaDemonstrativo || paginaDre || paginaPlanoContas || paginaRateio || paginaGestaoRemessas || paginaGestaoVendasOnline || paginaCliente;

            if(jQuery('#formFC\\:panelFluxoCaixa').length > 0 && paginaFluxoCaixa){
                jQuery('#formFC\\:panelFluxoCaixa').css('display', 'none');
                customLayout = true;
            }
            if(jQuery('.container-box.zw_ui.especial').length > 0 && htmlLayoutLegado){
                jQuery('.container-box.zw_ui.especial').css('display', 'none');
                customLayout = true;
            }

            if(jQuery('.caixaCorpo.zw_ui').length > 0 && htmlLayoutLegado){
                jQuery('.caixaCorpo.zw_ui').css('display', 'none');
                customLayout = true;
            }

            if(customLayout){
                jQuery('.container-imagem-configuracao').css('position', 'absolute');
                let width = jQuery('.panel-group-novo-menu').width();
                jQuery('.container-imagem-configuracao').css('left', width + 10);
            }

            if(!customLayout){
                jQuery('.container-imagem').addClass('close');
            }

            jQuery('.container-imagem-configuracao').addClass('open');
            jQuery('#td-pesquisa-clientes').hide();
        }

        function clearLocalSession() {
            localStorage.clear();
        }

    </script>
    <div class="zw_ui_opcoes_modulos_laterais">

        <a4j:commandLink id="btn-link-modulo-atual-zwui" action="#{LoginControle.abrirModulo}" styleClass="link-modulo-atual-zwui"/>
        <a4j:commandLink id="btn-abrir-modulo-atual-zwui"
                         action="#{LoginControle.abrirModuloAtual}"
                         oncomplete="#{LoginControle.onCompleteModuloAtual}" styleClass="btn-abrir-modulo-atual-zwui"/>

        <div class="zw_ui_modulo_atual ${LoginControle.moduloAberto.sigla} ${MenuControle.exibirFavoritos ? 'FAVORITOS' : ''} "
             style="${MenuControle.exibirBtnVoltar ? 'display: none' : ''};"
             onclick="jQuery('.btn-abrir-modulo-atual-zwui').click();"
             onmouseenter="exibirMenuVoltar()">
            <span class="inner-circle" style="border: none !important;">
                <div>
                    <c:if test="${MenuControle.exibirFavoritos}">
                        <h:graphicImage alt="logo-favoritos"
                                        styleClass="icon-height"
                                        style="width: 18.35px; height: 17.51px;"
                                        url="/imagens_flat/pct-star-white.svg"/>
                    </c:if>
                    <c:if test="${not MenuControle.exibirFavoritos and not MenuControle.exibirConfiguracao and LoginControle.moduloAberto.sigla ne 'PES'}">
                        <h:graphicImage alt="logo-pacto" styleClass="icon-height" style="width: 32px"
                                        url="/imagens_flat/pct-transparente.svg"/>
                    </c:if>
                    <c:if test="${MenuControle.exibirConfiguracao}">
                        <i class="pct pct-settings"></i>
                    </c:if>

                    <c:if test="${not MenuControle.exibirFavoritos and not MenuControle.exibirConfiguracao and LoginControle.moduloAberto.sigla eq 'PES'}">
                        <i class="pct pct-users"></i>
                    </c:if>
                </div>
            </span>
        </div>
        <div id="zwUiModuloAtualIconeVoltar" class="zw_ui_modulo_configuracao zw_ui_modulo_atual ${LoginControle.moduloAberto.sigla} ${MenuControle.exibirFavoritos ? 'FAVORITOS' : ''} ${MenuControle.exibirConfiguracao ? 'CONFIG' : ''}"
             style="${not MenuControle.exibirBtnVoltar ? 'display: none' : ''};"
             onmouseleave="ocultarMenuVoltar()"
             onmouseover="exibirMenuVoltar()"
             onclick="document.getElementById('zwUiRetornarDescricao').click()">
            <span class="inner-circle" style="border: none !important;">
                <i class="pct pct-arrow-left-circle icon-voltar-configuracao"></i>
            </span>
        </div>
        <div class="upper-section">
            <a4j:jsFunction name="abrirModulo" action="#{MenuControle.abrirModulo}" oncomplete="#{MenuControle.oncompleteModulo}">
                <a4j:actionparam name="siglaModulo" assignTo="#{MenuControle.siglaModulo}"/>
                <a4j:actionparam name="idLocalizacaoMenu"/>
            </a4j:jsFunction>

            <div class="modules-wrapper" style="display: flex; flex-direction: column; justify-content: space-between;align-items: center;">
                <div class="modules-menu-nav-container" onclick="scrollModules('up')">
                    <a class="modules-menu-nav-icon modules-menu-mav-disable"
                       id="modules-menu-nav-up">
                        <img src="${root}/imagens/pct-chevron-up.png">
                    </a>
                </div>
                <div id="modules-menu"
                     current-page="${MenuControle.paginaAtualModulos}"
                     style="scroll-behavior: smooth;overflow-y: hidden;" class="margin-modules">

                </div>
                <div class="modules-menu-nav-container" onclick="scrollModules('down')">
                    <a class="modules-menu-mav-icon"
                       id="modules-menu-nav-down">
                        <img src="${root}/imagens/pct-chevron-down.png">
                    </a>
                </div>
            </div>

            <a4j:commandLink id="menuFavoritos"
                             styleClass="icon-module #{MenuControle.taskbarSelected eq 'linkFavorito' ? 'selected' : ''}"
                             action="#{MenuControle.processarFavoritos}"
                             onclick="abrirMenuAcessoRapido()"
                             actionListener="#{MenuControle.onSelectTaskbarItem}"
                             reRender="panelGroupNovoMenu"
                             oncomplete="montaModulomenu()"
                             title="Favoritos">
                <i class="pct pct-star"></i>
                <f:attribute name="taskbarSelected" value="linkFavorito"/>
            </a4j:commandLink>
            <a4j:commandLink id="linkaddClienteMenuSuperiorZWUI" rendered="#{LoginControle.apresentarLinkIncluirCliente}"
                             styleClass="tooltipsterright icon-module #{MenuControle.taskbarSelected eq 'linkaddClienteMenuSuperiorZWUI' ? 'selected' : ''}"
                             action="#{PreCadastroClienteControle.novo}"
                             oncomplete="#{PreCadastroClienteControle.msgAlert}"
                             actionListener="#{MenuControle.onSelectTaskbarItem}"
                             title="Incluir Cliente" accesskey="2">
                <i class="pct pct-user-plus"></i>
                <c:if test="${modulo eq '4bf2add2267962ea87f029fef8f75a2f'}">
                    <f:attribute name="modulo" value="4bf2add2267962ea87f029fef8f75a2f"/>
                </c:if>
                <f:attribute name="taskbarSelected" value="linkaddClienteMenuSuperiorZWUI"/>
            </a4j:commandLink>

            <a4j:commandLink id="linkClientesMenuSuperiorZWUI"
                             styleClass="tooltipsterright icon-module #{MenuControle.taskbarSelected eq 'linkClientesMenuSuperiorZWUI' ? 'selected' : ''}"
                             action="#{ConsultaClienteControle.todosClientes}"
                             actionListener="#{MenuControle.onSelectTaskbarItem}"
                             title="Pessoas">
                <i class="pct pct-users"></i>
                <f:attribute name="taskbarSelected" value="linkClientesMenuSuperiorZWUI"/>
            </a4j:commandLink>

            <c:if test="${LoginControle.apresentarMenuNovoBI}">
                <a4j:commandLink styleClass="tooltipsterright icon-module #{MenuControle.taskbarSelected eq 'linkMenuNovoBIMenuSuperiorZWUI' ? 'selected' : ''}"
                                 title="BI's"
                                 id="linkMenuNovoBIMenuSuperiorZWUI"
                                 actionListener="#{MenuControle.onSelectTaskbarItem}"
                                 action="#{BIControle.abrirNovoBI}"
                                 oncomplete="#{BIControle.msgAlert}">
                    <i class="pct pct-bi"></i>
                    <f:attribute name="taskbarSelected" value="linkMenuNovoBIMenuSuperiorZWUI"/>
                </a4j:commandLink>
            </c:if>

            <a4j:jsFunction name="selectTasbarItemAgenda" action="#{MenuControle.onSelectTaskbarAgendaItem}" status="false"></a4j:jsFunction>
            <c:if test="${LoginControle.apresentarModuloNovoTreino}">
                <h:outputLink id="linkAgendaMenuSuperiorZWUI" value="#{LoginControle.abrirTreinoNovo}&redirect=agenda/painel/bi"
                              title="Agenda"
                              styleClass="tooltipsterright icon-module">
                    <i class="pct pct-calendar"></i>
                </h:outputLink>
                <script>
                    document.addEventListener("load", function () {
                        document.getElementById('form:linkAgendaMenuSuperiorZWUI').onclick = function () {
                            selectTasbarItemAgenda();
                        }
                    });
                </script>
            </c:if>
            <div class="bottom-botton-settings">
                <a4j:commandLink styleClass="icon-module #{MenuControle.taskbarSelected eq 'btnConfiguracaoZWui' ? 'selected' : ''}"
                                 title="Configurações"
                                 id="btnConfiguracaoZWui"
                                 onclick="abrirMenuAcessoRapido()"
                                 reRender="form:panelGroupNovoMenu,formMenu:panelGroupNovoMenu "
                                 action="#{MenuControle.processarConfiguracao}"
                                 rendered="#{!ConfiguracaoSistemaControle.novaTelaConfiguracao}"
                                 actionListener="#{MenuControle.onSelectTaskbarItem}"
                                 oncomplete="montaModulomenu(); mostrarContainerImagemConfiguracao()">
                    <i class="pct pct-settings border-botton-settings"></i>
                    <f:attribute name="taskbarSelected" value="CONFIG"/>
                </a4j:commandLink>

                <a4j:commandLink styleClass="icon-module #{MenuControle.taskbarSelected eq 'btnConfiguracaoZWui' ? 'selected' : ''}"
                                 title="Configurações"
                                 id="btnConfiguracaoZWuiNovo"
                                 rendered="#{ConfiguracaoSistemaControle.novaTelaConfiguracao}"
                                 action="#{ConfiguracaoSistemaControle.abrirNovaConfiguracao}"
                                 oncomplete="#{ConfiguracaoSistemaControle.msgAlert}">
                    <i class="pct pct-settings border-botton-settings"></i>
                    <f:attribute name="taskbarSelected" value="CONFIG"/>
                </a4j:commandLink>

<%--                <a4j:commandLink styleClass="icon-module #{MenuControle.taskbarSelected eq 'btnConfiguracaoZWuiFin' ? 'selected' : ''}"--%>
<%--                                 title="Configurações"--%>
<%--                                 id="btnConfiguracaoZWuiFin"--%>
<%--                                 onclick="abrirMenuAcessoRapido()"--%>
<%--                                 reRender="form:panelGroupNovoMenu"--%>
<%--                                 rendered="#{LoginControle.moduloAtualFin}"--%>
<%--                                 action="#{MenuControle.processarConfiguracao}"--%>
<%--                                 oncomplete="montaModulomenu(); mostrarContainerImagemConfiguracao()">--%>
<%--                    <i class="pct pct-settings border-botton-settings"></i>--%>
<%--                </a4j:commandLink>--%>

<%--                <a4j:commandLink styleClass="icon-module #{MenuControle.taskbarSelected eq 'btnConfiguracaoZWuiCrm' ? 'selected' : ''}"--%>
<%--                                 title="Configurações"--%>
<%--                                 id="btnConfiguracaoZWuiCrm"--%>
<%--                                 onclick="abrirMenuAcessoRapido()"--%>
<%--                                 reRender="form:panelGroupNovoMenu"--%>
<%--                                 rendered="#{LoginControle.moduloAtualCrm}"--%>
<%--                                 action="#{MenuControle.processarConfiguracao}"--%>
<%--                                 oncomplete="montaModulomenu(); mostrarContainerImagemConfiguracao()">--%>
<%--                    <i class="pct pct-settings border-botton-settings"></i>--%>
<%--                    <f:attribute name="taskbarSelected" value="btnConfiguracaoZWuiCrm"/>--%>
<%--                </a4j:commandLink>--%>
            </div>
        </div>
    </div>
    <div class="zw_ui_opcoes_laterais mini ">
        <div class="zw_ui_menu-toggle menuOpen openmenu-zwui" onclick="fecharMenuExplorar();abrirMenu();">
            <i class="pct pct-chevron-right ng-star-inserted"></i>
        </div>
    </div>

    <div class="zw_ui_opcoes_laterais">

        <div id="zwUiModuloAtualDescricao" class="zw_ui_modulo_atual_descricao ${LoginControle.moduloAberto.sigla} ${MenuControle.exibirFavoritos ? 'FAVORITOS' : ''} ${MenuControle.exibirConfiguracao ? 'CONFIG' : ''}" onmouseover="exibirMenuVoltar()">
            <c:if test="${not MenuControle.exibirFavoritos && not MenuControle.exibirConfiguracao}">
                <div class="module-name"> ${LoginControle.modulo.descricao} </div>
                <div class="zw_ui_menu-toggle menuOpen" onclick="fecharMenu();redimensionarMenuExplorar();">
                    <i class="pct pct-chevron-left ng-star-inserted"></i>
                </div>
            </c:if>

            <c:if test="${MenuControle.exibirFavoritos}">
                <div class="module-name">${msg_menu.Menu_btn_favoritos}</div>
                <div class="zw_ui_menu-toggle menuOpen" onclick="fecharMenu();redimensionarMenuExplorar();">
                    <i class="pct pct-chevron-left ng-star-inserted"></i>
                </div>
            </c:if>

            <c:if test="${MenuControle.exibirConfiguracao}">
                <div class="module-name">${msg_menu.Menu_btn_configuracoes}</div>
                <div class="zw_ui_menu-toggle menuOpen" onclick="fecharMenu();redimensionarMenuExplorar();">
                    <i class="pct pct-chevron-left ng-star-inserted"></i>
                </div>
            </c:if>
        </div>

        <a4j:jsFunction name="processarRetornarModulo" action="#{MenuControle.processarRetornarModulo}"
                        reRender="form:panelGroupNovoMenu" oncomplete="#{MenuControle.onCompleteModuloAtual};abrirMenu();montaModulomenu();mostrarContainerImagem();" />
        <div onclick="processarRetornarModulo();" id="zwUiRetornarDescricao" class="zw_ui_retornar" onmouseleave="ocultarMenuVoltar()">
            <span>${MenuControle.labelBtnVoltar}</span>
        </div>
        <div class="zw_ui_menu-toggle menuOpen" onclick="fecharMenu()">
            <i class="pct pct-chevron-left ng-star-inserted"></i>
        </div>

        <h:panelGroup layout="block" id="container-mask-menus">

            <div class="container-mask menus">
                <h:panelGroup id="panelMenuAcessoRapido">
                    <jsp:include page="/includes/include_menulateral_acessorapido.jsp"/>
                </h:panelGroup>
                <h:panelGroup rendered="#{MenuControle.exibirConfiguracao}">
                    <a4j:repeat value="#{MenuControle.modulosConfiguracoes}" var="funcionalidade">
                        <a4j:repeat value="#{funcionalidade}" var="modulo">
                            <h:panelGroup
                                    layout="block"
                                    styleClass="grupoMenuItem grupoMenuItemContainer">

                                <h:panelGroup layout="block" styleClass="grupoMenuItemNomeContainer">
                                    <a4j:commandLink value="#{modulo.descricao}"
                                                     title="#{modulo.descricao}"
                                                     style="max-width: 201px;"
                                                     styleClass="titulo3 linkFuncionalidade linkFuncionalidadeAcessoRapido MENU-#{modulo.descricao}"
                                                     actionListener="#{MenuControle.actionConfiguracao}"
                                                     oncomplete="#{MenuControle.scriptAbrirPopUp}">
                                        <f:attribute name="funcionalidade" value="#{funcionalidade.siglaModulo}"/>
                                        <f:attribute name="idLocalizacaoMenu" value="LATERAL_RECURSO"/>
                                    </a4j:commandLink>
                                </h:panelGroup>
                            </h:panelGroup>
                        </a4j:repeat>
                    </a4j:repeat>
                </h:panelGroup>
                <h:panelGroup layout="block" rendered="#{not MenuControle.exibirFavoritos && not MenuControle.exibirConfiguracao}">
<%--                <c:if test="${not MenuControle.exibirFavoritos && not MenuControle.exibirConfiguracao}"> Foi substituido pelo <h:panelGroup rendered porque existe um bug no glassfish que gera ids duplicados quando existe um panel dentro de um if--%>
                <!-- NOTAS - GESTO DE NOTAS -->
                    <h:panelGroup layout="block" styleClass="grupoMenuItem grupoMenuItemContainer titulomenu-zwui GESTAO_NOTAS"
                                  rendered="#{LoginControle.permissaoAcessoMenuVO.gestaoNotas and LoginControle.moduloAberto.sigla eq 'NOTAS'}">
                        <div class="grupoMenuItemNomeContainer"
                             style="max-width: 201px;">

                            <a4j:commandLink styleClass="titulo3 linkFuncionalidade linkFuncionalidadeAcessoRapido"
                                             actionListener="#{FuncionalidadeControle.prepararFuncionalidade}"
                                             oncomplete="#{FuncionalidadeControle.abrirPopUp}"
                                             action="#{FuncionalidadeControle.abrirComCasoNavegacao}"
                                             style="text-decoration: none"
                                             id="menu-notas-gestao"
                                             value="Gestão de notas">
                                <f:attribute name="funcionalidade" value="GESTAO_NOTAS"/>
                            </a4j:commandLink>
                        </div>
                    </h:panelGroup>

                    <!-- NOTAS - GESTAO NFCE -->
                    <h:panelGroup layout="block" styleClass="grupoMenuItem grupoMenuItemContainer titulomenu-zwui GESTAO_NFCE"
                                  rendered="#{LoginControle.permissaoAcessoMenuVO.gestaoNotas and LoginControle.moduloAberto.sigla eq 'NOTAS'}">
                        <div class="grupoMenuItemNomeContainer"
                             style="max-width: 201px;">

                            <a4j:commandLink styleClass="titulo3 linkFuncionalidade linkFuncionalidadeAcessoRapido"
                                             actionListener="#{FuncionalidadeControle.prepararFuncionalidade}"
                                             oncomplete="#{FuncionalidadeControle.abrirPopUp}"
                                             action="#{FuncionalidadeControle.abrirComCasoNavegacao}"
                                             style="text-decoration: none"
                                             id="menu-notas-config-nfce"
                                             value="Gestão de NFC-e">
                                <f:attribute name="funcionalidade" value="GESTAO_NFCE"/>
                            </a4j:commandLink>
                        </div>
                    </h:panelGroup>

                    <!-- Gestão CRM-->
                    <h:panelGroup layout="block" styleClass="grupoMenuItem grupoMenuItemContainer titulomenu-zwui CRM_BI"
                                  rendered="#{LoginControle.permissaoAcessoMenuVO.businessIntelligenceCRM  and LoginControle.moduloAberto.sigla eq 'CRM' and LoginControle.biCrmAtivo}">
                        <div class="grupoMenuItemNomeContainer"
                             style="max-width: 201px;">

                            <a4j:commandLink rendered="#{LoginControle.permissaoAcessoMenuVO.visualizarBI}"
                                             id="linkBICRMZWUI"
                                             styleClass="titulo3 linkFuncionalidade linkFuncionalidadeAcessoRapido MENU-BusinessInteligence2 CRM_BI"
                                             action="#{BusinessIntelligenceCRMControle.carregarBusinessIntelligence}"
                                             reRender="superiorPrincipalCRM, colunaCRMEsquerda, colunaCRMCentro, colunaCRMDireita"
                                             style="text-decoration: none"
                                             value="BI CRM">
                                <f:param name="funcionalidadeAberta" value="BusinessInteligence2"/>
                            </a4j:commandLink>
                        </div>
                    </h:panelGroup>

                    <%-- CONTATO EM GRUPO --%>
                    <h:panelGroup layout="block" styleClass="grupoMenuItem grupoMenuItemContainer titulomenu-zwui MAILING"
                                  rendered="#{LoginControle.permissaoAcessoMenuVO.malaDireta and LoginControle.moduloAberto.sigla eq 'CRM' and LoginControle.mailingAtivo}">
                        <div class="grupoMenuItemNomeContainer"
                             style="max-width: 201px;">

                            <a4j:commandLink
                                    styleClass="titulo3 linkFuncionalidade linkFuncionalidadeAcessoRapido MENU-MAILING"
                                    style="text-decoration: none"
                                    id="btncontatoemgrupo"
                                    actionListener="#{FuncionalidadeControle.prepararFuncionalidade}"
                                    action="#{MensagemBuilderControle.init}"
                                    value="Contato em grupo">
                                <f:attribute name="funcionalidade" value="MAILING" />
                                <f:param name="funcionalidadeAberta" value="MAILING"/>
                            </a4j:commandLink>
                        </div>
                    </h:panelGroup>

                    <%-- CONTATO PESSOAL --%>
                    <h:panelGroup layout="block"
                                  styleClass="grupoMenuItem grupoMenuItemContainer titulomenu-zwui CONTATO_PESSOAL"
                                  rendered="#{LoginControle.permissaoAcessoMenuVO.malaDireta and LoginControle.moduloAberto.sigla eq 'CRM' and LoginControle.mailingAtivo}">
                        <div class="grupoMenuItemNomeContainer"
                             style="max-width: 201px;">

                            <a4j:commandLink
                                    styleClass="titulo3 linkFuncionalidade linkFuncionalidadeAcessoRapido MENU-CONTATO_PESSOAL"
                                    style="text-decoration: none"
                                    reRender="superiorPrincipalCRM, colunaCRMEsquerda, colunaCRMCentro, colunaCRMDireita"
                                    id="btncontatopessoal"
                                    ajaxSingle="true"
                                    actionListener="#{HistoricoContatoControle.consultarPaginadoListener}"
                                    value="Contato pessoal"
                                    oncomplete="abrirPopup('mailing.jsp', 'mailing', 1000, 650);">
                                <f:attribute name="funcionalidade" value="CONTATO_PESSOAL"/>
                                <f:param name="funcionalidadeAberta" value="CONTATO_PESSOAL"/>
                            </a4j:commandLink>
                        </div>
                    </h:panelGroup>

                    <%-- GESTAO DE CARTEIRAS --%>
                    <h:panelGroup layout="block" styleClass="grupoMenuItem grupoMenuItemContainer titulomenu-zwui CARTEIRAS"
                                  rendered="#{LoginControle.permissaoAcessoMenuVO.organizadorCarteira and LoginControle.moduloAberto.sigla eq 'CRM' and LoginControle.contatoPessoaAtivo}">
                        <div class="grupoMenuItemNomeContainer"
                             style="max-width: 201px;">

                            <a4j:commandLink
                                    styleClass="titulo3 linkFuncionalidade linkFuncionalidadeAcessoRapido MENU-CARTEIRAS"
                                    style="text-decoration: none"
                                    id="btngestaocarteiras"
                                    ajaxSingle="true"
                                    action="#{LoginControle.abrirCarteirasCRM}"
                                    value="Gestão de carteiras">
                                <f:param name="funcionalidadeAberta" value="CARTEIRAS"/>
                            </a4j:commandLink>
                        </div>
                    </h:panelGroup>

                    <%-- MARCAR COMPARECIMENTO --%>
                    <h:panelGroup layout="block"
                                  styleClass="grupoMenuItem grupoMenuItemContainer titulomenu-zwui MARCAR_COMPARECIMENTO"
                                  rendered="#{LoginControle.permissaoAcessoMenuVO.agenda and LoginControle.moduloAberto.sigla eq 'CRM' and LoginControle.marcarComparecimentoAtivo}">
                        <div class="grupoMenuItemNomeContainer"
                             style="max-width: 201px;">

                            <a4j:commandLink
                                    styleClass="titulo3 linkFuncionalidade linkFuncionalidadeAcessoRapido MENU-MARCAR_COMPARECIMENTO"
                                    style="text-decoration: none"
                                    id="btnmarcarcomparecimento"
                                    ajaxSingle="true"
                                    action="#{AgendaControle.novo}"
                                    oncomplete="abrirPopup('confirmarComparecimentoAgendadoForm.jsp', 'ConfirmarAgendados', 780, 595);"
                                    value="Marcar Comparecimento">
                                <f:param name="funcionalidadeAberta" value="MARCAR_COMPARECIMENTO"/>
                            </a4j:commandLink>
                        </div>
                    </h:panelGroup>

                <!-- META DIARIA-->
                    <h:panelGroup layout="block"
                                  styleClass="grupoMenuItem grupoMenuItemContainer titulomenu-zwui CRM_META_DIARIA"
                                  rendered="#{LoginControle.permissaoAcessoMenuVO.visualizarMeta and LoginControle.moduloAberto.sigla eq 'CRM' and LoginControle.metaDiariaAtivo}">
                        <div class="grupoMenuItemNomeContainer"
                             style="max-width: 201px;">

                            <a4j:commandLink
                                    styleClass="titulo3 linkFuncionalidade linkFuncionalidadeAcessoRapido MENU-MetaDiaria"
                                    action="#{MetaCRMControle.consultarMetas}"
                                    style="text-decoration: none"
                                    reRender="superiorPrincipalCRM, colunaCRMEsquerda, colunaCRMCentro, colunaCRMDireita"
                                    oncomplete="mostrarTodasTelas();adicionarPlaceHolderCRM();"
                                    value="Meta Diária">
                                <f:param name="funcionalidadeAberta" value="MetaDiaria"/>
                            </a4j:commandLink>
                        </div>
                    </h:panelGroup>

                    <%-- REGISTRO DE PARALIZAÇÃO --%>
                    <h:panelGroup layout="block"
                                  styleClass="grupoMenuItem grupoMenuItemContainer  titulomenu-zwui CRM_REGISTRO_PARALISACAO"
                                  rendered="#{LoginControle.permissaoAcessoMenuVO.crmQuarentena and LoginControle.moduloAberto.sigla eq 'CRM' and LoginControle.registroParalizacaoAtivo}">
                        <div class="grupoMenuItemNomeContainer"
                             style="max-width: 201px;">

                            <a4j:commandLink
                                    styleClass="titulo3 linkFuncionalidade linkFuncionalidadeAcessoRapido MENU-CRM_REGISTRO_PARALISACAO"
                                    style="text-decoration: none"
                                    id="btnregistroparalizacao"
                                    ajaxSingle="true"
                                    actionListener="#{FuncionalidadeControle.prepararFuncionalidade}"
                                    action="#{FuncionalidadeControle.abrirComCasoNavegacao}"
                                    oncomplete="#{FuncionalidadeControle.abrirPopUp};reRenderMenuLateral()"
                                    value="Registro de Paralisação">
                                <f:param name="funcionalidadeAberta" value="CRM_REGISTRO_PARALISACAO"/>
                                <f:attribute name="funcionalidade" value="CRM_REGISTRO_PARALISACAO"/>
                            </a4j:commandLink>
                        </div>
                    </h:panelGroup>

                    <!-- GESTÃO FINANCEIRO-->
                    <h:panelGroup layout="block" styleClass="grupoMenuItem grupoMenuItemContainer  titulomenu-zwui FIN_BI"
                                  rendered="#{LoginControle.permissaoAcessoMenuVO.biFinanceiro  and LoginControle.moduloAberto.sigla eq 'FIN' and LoginControle.biFinanceiroAtivo}">
                        <div class="grupoMenuItemNomeContainer"
                             style="max-width: 201px;">

                            <a4j:commandLink rendered="#{LoginControle.permissaoAcessoMenuVO.biFinanceiro}"
                                             id="linkBIMenuSuperiorZWUIFIN"
                                             styleClass="titulo3 linkFuncionalidade linkFuncionalidadeAcessoRapido  MENU-BusinessInteligence3"
                                             action="#{BIFinanceiroControle.inicializar}"
                                             style="text-decoration: none"
                                             value="BI Financeiro">
                                <f:param name="funcionalidadeAberta" value="BusinessInteligence3"/>
                            </a4j:commandLink>
                        </div>
                    </h:panelGroup>
                    <a4j:repeat value="#{MenuControle.gruposExibirAux}" var="grupo">

                        <h:panelGroup layout="block"
                                      rendered="#{grupo.grupoFuncionalidadeSistemaEnum != 'ADM_RELATORIOS' and grupo.grupoFuncionalidadeSistemaEnum != 'ADM_CADASTROS' and subMenu.grupoFuncionalidadeSistemaEnum.name != 'ADM_CONTROLE_ESTOQUE' and grupo.renderizarGrupo}">
                            <h:panelGroup rendered="#{!grupo.menuSolto}">
                                <h:panelGroup layout="block"
                                              styleClass="grupoMenuItem grupoMenuItemContainer titulomenu-zwui MENU_#{grupo.grupoFuncionalidadeSistemaEnum.name}"
                                              rendered="#{grupo.renderizarGrupo and grupo.grupoFuncionalidadeSistemaEnum !='ADM_PRODUTOS_PLANOS_TURMAS' and grupo.grupoFuncionalidadeSistemaEnum != 'ADM_CONTROLE_ESTOQUE'}">
                                    <div class="grupoMenuItemNomeContainer"
                                         style="max-width: 201px;">
                                        <a4j:commandLink styleClass="titulo3 linkFuncionalidade linkFuncionalidadeAcessoRapido"
                                                         status="false" action="#{MenuControle.abrirSubMenu}"
                                                         onclick="abrirSubmenu('.#{grupo.grupoFuncionalidadeSistemaEnum.name}')">
                                            <f:param name="menuAberto" value="#{grupo.grupoFuncionalidadeSistemaEnum.name}"/>
                                            <h:outputText value="#{grupo.grupoFuncionalidadeSistemaEnum.descricao}"/>
                                        </a4j:commandLink>
                                        <i class="submenu-indicator pct pct-arrow-right-circle ng-star-inserted"></i>
                                    </div>
                                </h:panelGroup>

                                <h:panelGroup layout="block" styleClass="#{grupo.grupoFuncionalidadeSistemaEnum.name}">
                                    <div class="voltar-zwui submenu-zwui"
                                         style="max-width: 201px;">
                                        <a4j:commandLink status="false" action="#{MenuControle.fecharSubMenu}"
                                                         onclick="voltarSubmenu('.#{grupo.grupoFuncionalidadeSistemaEnum.name}')">
                                            <i class="pct pct-arrow-left-circle"><h:outputText styleClass="class-submenu-retornar" value="#{grupo.grupoFuncionalidadeSistemaEnum.descricao}"/></i>
                                        </a4j:commandLink>
                                    </div>

                                    <a4j:repeat value="#{grupo.subMenu}" var="subMenu">
                                        <h:panelGroup rendered="#{grupo.subMenu != null and subMenu.renderizarGrupo}"
                                                      layout="block"
                                                      styleClass="grupoMenuItem grupoMenuItemContainer submenu-zwui">
                                            <h:panelGroup layout="block" styleClass="grupoMenuItemNomeContainer">
                                                <a4j:commandLink status="false" style="max-width: 201px;"
                                                                 styleClass="titulo3 linkFuncionalidade linkFuncionalidadeAcessoRapido"
                                                                 onclick="abrirSubmenu('.#{subMenu.grupoFuncionalidadeSistemaEnum}', '.#{grupo.grupoFuncionalidadeSistemaEnum.name}')">
                                                    <f:param name="menuAberto"
                                                             value="#{subMenu.grupoFuncionalidadeSistemaEnum.name}"/>
                                                    <h:outputText value="#{subMenu.descricao}"/>
                                                </a4j:commandLink>
                                                <i class="submenu-indicator pct pct-arrow-right-circle ng-star-inserted"></i>
                                            </h:panelGroup>
                                        </h:panelGroup>
                                    </a4j:repeat>

                                    <a4j:repeat value="#{grupo.funcionalidades}" var="funcionalidade">

                                        <h:panelGroup
                                                rendered="#{funcionalidade.tipo == 'menulink' and funcionalidade.renderizar}"
                                                layout="block"
                                                styleClass="grupoMenuItem grupoMenuItemContainer submenu-zwui #{funcionalidade.name}">


                                            <div class="grupoMenuItemNomeContainer"
                                                 style="max-width: 201px;">
                                                <a4j:commandLink
                                                        value="#{funcionalidade.descricaoMenulateral}"
                                                        title="#{funcionalidade.funcionalidadeSistemaEnum.descricao}"
                                                        style="max-width: 201px;"
                                                        styleClass="titulo3 linkFuncionalidade linkFuncionalidadeAcessoRapido MENU-#{funcionalidade.name}"
                                                        actionListener="#{FuncionalidadeControle.prepararFuncionalidade}"
                                                        oncomplete="#{FuncionalidadeControle.abrirPopUp};reRenderMenuLateral()"
                                                        action="#{FuncionalidadeControle.abrirComCasoNavegacao}"
                                                        reRender="#{funcionalidade.funcionalidadeSistemaEnum.reRenderElement}">
                                                    <f:attribute name="funcionalidade" value="#{funcionalidade.name}"/>
                                                    <f:param name="funcionalidadeAberta"
                                                             value="#{funcionalidade.funcionalidadeSistemaEnum.name}"/>
                                                    <f:attribute name="idLocalizacaoMenu" value="LATERAL_RECURSO"/>
                                                </a4j:commandLink>
                                            </div>


                                        </h:panelGroup>
                                    </a4j:repeat>
                                </h:panelGroup>
                                <a4j:repeat value="#{grupo.subMenu}" var="subMenu">

                                    <h:panelGroup layout="block" styleClass="#{subMenu.grupoFuncionalidadeSistemaEnum.name}"
                                                  rendered="#{grupo.subMenu != null and subMenu.renderizarGrupo and subMenu.grupoFuncionalidadeSistemaEnum.name != 'ADM_CONTROLE_ESTOQUE'}">
                                        <div class="voltar-zwui submenu-zwui"
                                             style="max-width: 201px;">
                                            <a4j:commandLink status="false" action="#{MenuControle.fecharSubMenu}"
                                                             onclick="voltarSubmenu('.#{subMenu.grupoFuncionalidadeSistemaEnum.name}', '.#{grupo.grupoFuncionalidadeSistemaEnum.name}')">
                                                <i class="pct pct-arrow-left-circle"><h:outputText styleClass="class-submenu-retornar" value="#{grupo.grupoFuncionalidadeSistemaEnum.descricao}"/></i>
                                            </a4j:commandLink>
                                        </div>

                                        <a4j:repeat value="#{subMenu.funcionalidades}" var="funcionalidade">

                                            <h:panelGroup
                                                    rendered="#{funcionalidade.tipo == 'menulink' and funcionalidade.renderizar}"
                                                    layout="block"
                                                    styleClass="grupoMenuItem grupoMenuItemContainer submenu-zwui #{funcionalidade.name}">


                                                <div class="grupoMenuItemNomeContainer"
                                                     style="max-width: 201px;">
                                                    <a4j:commandLink
                                                            value="#{funcionalidade.descricaoMenulateral}"
                                                            title="#{funcionalidade.funcionalidadeSistemaEnum.descricao}"
                                                            style="max-width: 201px;"
                                                            styleClass="titulo3 linkFuncionalidade linkFuncionalidadeAcessoRapido MENU-#{funcionalidade.name}"
                                                            actionListener="#{FuncionalidadeControle.prepararFuncionalidade}"
                                                            oncomplete="#{FuncionalidadeControle.abrirPopUp};reRenderMenuLateral()"
                                                            action="#{FuncionalidadeControle.abrirComCasoNavegacao}"
                                                            reRender="#{funcionalidade.funcionalidadeSistemaEnum.reRenderElement}">
                                                        <f:attribute name="funcionalidade" value="#{funcionalidade.name}"/>
                                                        <f:param name="funcionalidadeAberta"
                                                                 value="#{funcionalidade.funcionalidadeSistemaEnum.name}"/>
                                                        <f:attribute name="idLocalizacaoMenu" value="LATERAL_RECURSO"/>
                                                    </a4j:commandLink>
                                                </div>


                                            </h:panelGroup>
                                        </a4j:repeat>
                                        <a4j:repeat value="#{subMenu.subMenu}" var="subMenu2">
                                            <h:panelGroup rendered="#{subMenu2.grupoFuncionalidadeSistemaEnum != null}"
                                                          layout="block"
                                                          styleClass="grupoMenuItem grupoMenuItemContainer submenu-zwui">
                                                <h:panelGroup layout="block" styleClass="grupoMenuItemNomeContainer">
                                                    <a4j:commandLink status="false" style="max-width: 201px;"
                                                                     styleClass="titulo3 linkFuncionalidade linkFuncionalidadeAcessoRapido"
                                                                     onclick="abrirSubmenu('.#{subMenu2.grupoFuncionalidadeSistemaEnum}', '.#{subMenu.grupoFuncionalidadeSistemaEnum.name}')">
                                                        <f:param name="menuAberto"
                                                                 value="#{subMenu2.grupoFuncionalidadeSistemaEnum.name}"/>
                                                        <h:outputText
                                                                value="#{subMenu2.grupoFuncionalidadeSistemaEnum.descricao}"/>
                                                    </a4j:commandLink>
                                                    <i class="submenu-indicator pct pct-arrow-right-circle ng-star-inserted"></i>
                                                </h:panelGroup>
                                            </h:panelGroup>
                                        </a4j:repeat>
                                    </h:panelGroup>
                                </a4j:repeat>

                            </h:panelGroup>

                            <h:panelGroup rendered="#{grupo.menuSolto}">
                                <h:panelGroup layout="block"
                                              styleClass="grupoMenuItem grupoMenuItemContainer titulomenu-zwui MENU_#{grupo.grupoFuncionalidadeSistemaEnum.name}"
                                              rendered="#{grupo.renderizarGrupo and grupo.funcionalidade.tipo == 'menulink' and grupo.funcionalidade.renderizar}">
                                    <div class="grupoMenuItemNomeContainer"
                                         style="max-width: 201px;">
                                        <a4j:commandLink
                                                value="#{grupo.funcionalidade.descricaoMenulateral}"
                                                title="#{grupo.funcionalidade.funcionalidadeSistemaEnum.descricao}"
                                                style="max-width: 201px;"
                                                styleClass="titulo3 linkFuncionalidade linkFuncionalidadeAcessoRapido MENU-#{grupo.funcionalidade.name}"
                                                actionListener="#{FuncionalidadeControle.prepararFuncionalidade}"
                                                oncomplete="#{FuncionalidadeControle.abrirPopUp};reRenderMenuLateral()"
                                                action="#{FuncionalidadeControle.abrirComCasoNavegacao}"
                                                reRender="#{grupo.funcionalidade.funcionalidadeSistemaEnum.reRenderElement}">
                                            <f:attribute name="funcionalidade" value="#{grupo.funcionalidade.name}"/>
                                            <f:param name="funcionalidadeAberta"
                                                     value="#{grupo.funcionalidade.funcionalidadeSistemaEnum.name}"/>
                                            <f:attribute name="idLocalizacaoMenu" value="LATERAL_RECURSO"/>
                                        </a4j:commandLink>
                                    </div>
                                </h:panelGroup>
                            </h:panelGroup>
                        </h:panelGroup>
                    </a4j:repeat>
                    <a4j:repeat value="#{MenuControle.gruposExibirAux}" var="grupo">

                        <h:panelGroup layout="block"
                                      rendered="#{grupo.grupoFuncionalidadeSistemaEnum eq 'ADM_RELATORIOS' and subMenu.grupoFuncionalidadeSistemaEnum.name != 'ADM_CONTROLE_ESTOQUE'}">

                            <h:panelGroup layout="block"
                                          styleClass="grupoMenuItem grupoMenuItemContainer titulomenu-zwui MENU_#{grupo.grupoFuncionalidadeSistemaEnum.name}"
                                          rendered="#{grupo.renderizar}">
                                <div class="grupoMenuItemNomeContainer"
                                     style="max-width: 201px;">
                                    <a4j:commandLink styleClass="titulo3 linkFuncionalidade linkFuncionalidadeAcessoRapido"
                                                     status="false" action="#{MenuControle.abrirSubMenu}"
                                                     onclick="abrirSubmenu('.#{grupo.grupoFuncionalidadeSistemaEnum.name}')">
                                        <f:param name="menuAberto" value="#{grupo.grupoFuncionalidadeSistemaEnum.name}"/>
                                        <h:outputText value="#{grupo.grupoFuncionalidadeSistemaEnum.descricao}"/>
                                    </a4j:commandLink>
                                    <i class="submenu-indicator pct pct-arrow-right-circle ng-star-inserted"></i>
                                </div>
                            </h:panelGroup>

                            <h:panelGroup layout="block" styleClass="#{grupo.grupoFuncionalidadeSistemaEnum.name}">
                                <div class="voltar-zwui submenu-zwui"
                                     style="max-width: 201px;">
                                    <a4j:commandLink status="false" action="#{MenuControle.fecharSubMenu}"
                                                     onclick="voltarSubmenu('.#{grupo.grupoFuncionalidadeSistemaEnum.name}')">
                                        <i class="pct pct-arrow-left-circle"><h:outputText styleClass="class-submenu-retornar" value="#{grupo.grupoFuncionalidadeSistemaEnum.descricao}"/></i>
                                    </a4j:commandLink>
                                </div>

                                <a4j:repeat value="#{grupo.subMenu}" var="subMenu">
                                    <h:panelGroup rendered="#{grupo.subMenu != null}"
                                                  layout="block"
                                                  styleClass="grupoMenuItem grupoMenuItemContainer submenu-zwui">
                                        <h:panelGroup layout="block" styleClass="grupoMenuItemNomeContainer">
                                            <a4j:commandLink status="false" style="max-width: 201px;"
                                                             action="#{MenuControle.abrirSubMenu}"
                                                             styleClass="titulo3 linkFuncionalidade linkFuncionalidadeAcessoRapido"
                                                             onclick="abrirSubmenu('.#{subMenu.grupoFuncionalidadeSistemaEnum}', '.#{grupo.grupoFuncionalidadeSistemaEnum.name}')">
                                                <f:param name="menuAberto"
                                                         value="#{subMenu.grupoFuncionalidadeSistemaEnum.name}"/>
                                                <h:outputText value="#{subMenu.descricao}"/>
                                            </a4j:commandLink>
                                            <i class="submenu-indicator pct pct-arrow-right-circle ng-star-inserted"></i>
                                        </h:panelGroup>
                                    </h:panelGroup>
                                </a4j:repeat>

                                <a4j:repeat value="#{grupo.funcionalidades}" var="funcionalidade">

                                    <h:panelGroup rendered="#{funcionalidade.tipo == 'menulink' and funcionalidade.renderizar}"
                                                  layout="block"
                                                  styleClass="grupoMenuItem grupoMenuItemContainer submenu-zwui #{funcionalidade.name}">

                                        <div class="grupoMenuItemNomeContainer"
                                             style="max-width: 201px;">
                                            <a4j:commandLink
                                                    value="#{funcionalidade.descricaoMenulateral}"
                                                    title="#{funcionalidade.funcionalidadeSistemaEnum.descricao}"
                                                    style="max-width: 201px;"
                                                    styleClass="titulo3 linkFuncionalidade linkFuncionalidadeAcessoRapido MENU-#{funcionalidade.name}"
                                                    actionListener="#{FuncionalidadeControle.prepararFuncionalidade}"
                                                    oncomplete="#{FuncionalidadeControle.abrirPopUp};reRenderMenuLateral()"
                                                    action="#{FuncionalidadeControle.abrirComCasoNavegacao}"
                                                    reRender="#{funcionalidade.funcionalidadeSistemaEnum.reRenderElement}">
                                                <f:attribute name="funcionalidade" value="#{funcionalidade.name}"/>
                                                <f:param name="funcionalidadeAberta"
                                                         value="#{funcionalidade.funcionalidadeSistemaEnum.name}"/>
                                                <f:attribute name="idLocalizacaoMenu" value="LATERAL_RECURSO"/>
                                            </a4j:commandLink>
                                        </div>

                                    </h:panelGroup>
                                </a4j:repeat>
                            </h:panelGroup>

                            <a4j:repeat value="#{grupo.subMenu}" var="subMenu">

                                <h:panelGroup layout="block" styleClass="#{subMenu.grupoFuncionalidadeSistemaEnum.name}"
                                              rendered="#{grupo.subMenu != null}">
                                    <div class="voltar-zwui submenu-zwui"
                                         style="max-width: 201px;">
                                        <a4j:commandLink status="false" action="#{MenuControle.abrirSubMenu}"
                                                         onclick="voltarSubmenu('.#{subMenu.grupoFuncionalidadeSistemaEnum.name}', '.#{grupo.grupoFuncionalidadeSistemaEnum.name}')">
                                            <f:param name="menuAberto" value="#{grupo.grupoFuncionalidadeSistemaEnum.name}"/>
                                            <i class="pct pct-arrow-left-circle"><h:outputText styleClass="class-submenu-retornar" value="#{grupo.grupoFuncionalidadeSistemaEnum.descricao}"/></i>
                                        </a4j:commandLink>
                                    </div>

                                    <a4j:repeat value="#{subMenu.funcionalidades}" var="funcionalidade">

                                        <h:panelGroup
                                                rendered="#{funcionalidade.tipo == 'menulink' and funcionalidade.renderizar}"
                                                layout="block"
                                                styleClass="grupoMenuItem grupoMenuItemContainer submenu-zwui #{funcionalidade.name}">


                                            <div class="grupoMenuItemNomeContainer"
                                                 style="max-width: 201px;">
                                                <a4j:commandLink
                                                        value="#{funcionalidade.descricaoMenulateral}"
                                                        title="#{funcionalidade.funcionalidadeSistemaEnum.descricao}"
                                                        style="max-width: 201px;"
                                                        styleClass="titulo3 linkFuncionalidade linkFuncionalidadeAcessoRapido MENU-#{funcionalidade.name}"
                                                        actionListener="#{FuncionalidadeControle.prepararFuncionalidade}"
                                                        oncomplete="#{FuncionalidadeControle.abrirPopUp};reRenderMenuLateral()"
                                                        action="#{FuncionalidadeControle.abrirComCasoNavegacao}"
                                                        reRender="#{funcionalidade.funcionalidadeSistemaEnum.reRenderElement}">
                                                    <f:attribute name="funcionalidade" value="#{funcionalidade.name}"/>
                                                    <f:param name="funcionalidadeAberta"
                                                             value="#{funcionalidade.funcionalidadeSistemaEnum.name}"/>
                                                    <f:attribute name="idLocalizacaoMenu" value="LATERAL_RECURSO"/>
                                                </a4j:commandLink>
                                            </div>


                                        </h:panelGroup>
                                    </a4j:repeat>
                                </h:panelGroup>
                            </a4j:repeat>
                        </h:panelGroup>
                    </a4j:repeat>
                    <a4j:repeat value="#{MenuControle.gruposExibirAux}" var="grupo">

                        <h:panelGroup layout="block" rendered="#{grupo.grupoFuncionalidadeSistemaEnum eq 'ADM_CADASTROS'}">

                            <h:panelGroup layout="block"
                                          styleClass="grupoMenuItem grupoMenuItemContainer titulomenu-zwui MENU_#{grupo.grupoFuncionalidadeSistemaEnum.name}"
                                          rendered="#{grupo.renderizar}">
                                <div class="grupoMenuItemNomeContainer"
                                     style="max-width: 201px;">
                                    <a4j:commandLink styleClass="titulo3 linkFuncionalidade linkFuncionalidadeAcessoRapido"
                                                     status="false" action="#{MenuControle.abrirSubMenu}"
                                                     onclick="abrirSubmenu('.#{grupo.grupoFuncionalidadeSistemaEnum.name}')">
                                        <f:param name="menuAberto" value="#{grupo.grupoFuncionalidadeSistemaEnum.name}"/>
                                        <h:outputText value="#{grupo.grupoFuncionalidadeSistemaEnum.descricao}"/>
                                    </a4j:commandLink>
                                    <i class="submenu-indicator pct pct-arrow-right-circle ng-star-inserted"></i>
                                </div>
                            </h:panelGroup>

                            <h:panelGroup layout="block" styleClass="#{grupo.grupoFuncionalidadeSistemaEnum.name}">
                                <div class="voltar-zwui submenu-zwui"
                                     style="max-width: 201px;">
                                    <a4j:commandLink status="false" action="#{MenuControle.fecharSubMenu}"
                                                     onclick="voltarSubmenu('.#{grupo.grupoFuncionalidadeSistemaEnum.name}')">
                                        <i class="pct pct-arrow-left-circle"><h:outputText styleClass="class-submenu-retornar" value="#{grupo.grupoFuncionalidadeSistemaEnum.descricao}"/></i>
                                    </a4j:commandLink>
                                </div>

                                <a4j:repeat value="#{grupo.subMenu}" var="subMenu">
                                    <h:panelGroup
                                            layout="block"
                                            styleClass="grupoMenuItem grupoMenuItemContainer submenu-zwui">
                                        <h:panelGroup layout="block" styleClass="grupoMenuItemNomeContainer">
                                            <a4j:commandLink status="false"
                                                             style="max-width: 201px; white-space: normal !important;"
                                                             action="#{MenuControle.abrirSubMenu}"
                                                             styleClass="titulo3 linkFuncionalidade linkFuncionalidadeAcessoRapido"
                                                             onclick="abrirSubmenu('.#{subMenu.grupoFuncionalidadeSistemaEnum}', '.#{grupo.grupoFuncionalidadeSistemaEnum.name}')">
                                                <f:param name="menuAberto"
                                                         value="#{subMenu.grupoFuncionalidadeSistemaEnum.name}"/>
                                                <h:outputText value="#{subMenu.descricao}"/>
                                            </a4j:commandLink>
                                            <i class="submenu-indicator pct pct-arrow-right-circle ng-star-inserted"></i>
                                        </h:panelGroup>
                                    </h:panelGroup>
                                </a4j:repeat>

                                <a4j:repeat value="#{grupo.funcionalidades}" var="funcionalidade">

                                    <h:panelGroup rendered="#{funcionalidade.tipo == 'menulink' and funcionalidade.renderizar}"
                                                  layout="block"
                                                  styleClass="grupoMenuItem grupoMenuItemContainer submenu-zwui #{funcionalidade.name}">


                                        <div class="grupoMenuItemNomeContainer"
                                             style="max-width: 201px;">
                                            <a4j:commandLink
                                                    value="#{funcionalidade.descricaoMenulateral}"
                                                    title="#{funcionalidade.funcionalidadeSistemaEnum.descricao}"
                                                    style="max-width: 201px;"
                                                    styleClass="titulo3 linkFuncionalidade linkFuncionalidadeAcessoRapido MENU-#{funcionalidade.name}"
                                                    actionListener="#{FuncionalidadeControle.prepararFuncionalidade}"
                                                    oncomplete="#{FuncionalidadeControle.abrirPopUp};reRenderMenuLateral()"
                                                    action="#{FuncionalidadeControle.abrirComCasoNavegacao}"
                                                    reRender="#{funcionalidade.funcionalidadeSistemaEnum.reRenderElement}">
                                                <f:attribute name="funcionalidade" value="#{funcionalidade.name}"/>
                                                <f:param name="funcionalidadeAberta"
                                                         value="#{funcionalidade.funcionalidadeSistemaEnum.name}"/>
                                                <f:attribute name="idLocalizacaoMenu" value="LATERAL_RECURSO"/>
                                            </a4j:commandLink>
                                        </div>


                                    </h:panelGroup>
                                </a4j:repeat>
                            </h:panelGroup>

                            <a4j:repeat value="#{grupo.subMenu}" var="subMenu">

                                <h:panelGroup layout="block" styleClass="#{subMenu.grupoFuncionalidadeSistemaEnum.name}"
                                              rendered="#{grupo.subMenu != null and subMenu.grupoFuncionalidadeSistemaEnum.name != 'ADM_PRODUTOS_PLANOS_TURMAS'}">
                                    <div class="voltar-zwui submenu-zwui"
                                         style="max-width: 201px;">
                                        <a4j:commandLink status="false" action="#{MenuControle.abrirSubMenu}"
                                                         onclick="voltarSubmenu('.#{subMenu.grupoFuncionalidadeSistemaEnum.name}', '.#{grupo.grupoFuncionalidadeSistemaEnum.name}')">
                                            <f:param name="menuAberto" value="#{grupo.grupoFuncionalidadeSistemaEnum.name}"/>
                                            <i class="pct pct-arrow-left-circle"><h:outputText styleClass="class-submenu-retornar" value="#{grupo.grupoFuncionalidadeSistemaEnum.descricao}"/></i>
                                        </a4j:commandLink>
                                    </div>

                                    <a4j:repeat value="#{subMenu.funcionalidades}" var="funcionalidade">

                                        <h:panelGroup
                                                rendered="#{funcionalidade.tipo == 'menulink' and funcionalidade.renderizar}"
                                                layout="block"
                                                styleClass="grupoMenuItem grupoMenuItemContainer submenu-zwui #{funcionalidade.name}">


                                            <div class="grupoMenuItemNomeContainer"
                                                 style="max-width: 201px;">
                                                <a4j:commandLink
                                                        value="#{funcionalidade.descricaoMenulateral}"
                                                        title="#{funcionalidade.funcionalidadeSistemaEnum.descricao}"
                                                        style="max-width: 201px;"
                                                        styleClass="titulo3 linkFuncionalidade linkFuncionalidadeAcessoRapido MENU-#{funcionalidade.name}"
                                                        actionListener="#{FuncionalidadeControle.prepararFuncionalidade}"
                                                        oncomplete="#{FuncionalidadeControle.abrirPopUp};reRenderMenuLateral()"
                                                        action="#{FuncionalidadeControle.abrirComCasoNavegacao}"
                                                        reRender="#{funcionalidade.funcionalidadeSistemaEnum.reRenderElement}">
                                                    <f:attribute name="funcionalidade" value="#{funcionalidade.name}"/>
                                                    <f:param name="funcionalidadeAberta"
                                                             value="#{funcionalidade.funcionalidadeSistemaEnum.name}"/>
                                                    <f:attribute name="idLocalizacaoMenu" value="LATERAL_RECURSO"/>
                                                </a4j:commandLink>
                                            </div>


                                        </h:panelGroup>
                                    </a4j:repeat>
                                </h:panelGroup>
                            </a4j:repeat>
                        </h:panelGroup>
                    </a4j:repeat>
<%--                </c:if>--%>
                </h:panelGroup>
                <%--  not MenuControle.exibirFavoritos --%>
            </div>

            <c:if test="${not empty MenuControle.subMenu}">
                <script>
                    abrirSubmenu('.${MenuControle.subMenu}')
                </script>
            </c:if>

            <c:if test="${not empty FuncionalidadeControle.funcionalidadeAberta}">
            <script>
                marcaFuncionalidadeAtivada('${FuncionalidadeControle.funcionalidadeAberta}')
            </script>
        </c:if>


        </h:panelGroup>

        <div class="rodape"><!---->
            <div style="margin-top: 1em;padding-left: 20px;">
                <h:outputLink value="https://pactosolucoes.com.br/ajuda/kb/novidades" target="_blank" >
                    <div class="system-stat" style="margin-top: -1em;"><i class="pct pct-insight"></i>
                        <h:outputText value="Novidades"/>
                    </div>
                    <!---->
                </h:outputLink>
                <h:panelGroup rendered="#{!SuporteControle.desv}"
                              style=" font-size: 12px;color: #9E9E9E;vertical-align: middle;position: relative;top: -2px;margin-right: 3px;z-index: 1">
                    <c:if test="${LoginControle.usuarioLogado.usuarioPACTOBR}">
                        <h:graphicImage url="/imagens_flat/pct-calendar.png" style="color:grey;"></h:graphicImage>
                    </c:if>
                    <c:if test="${!LoginControle.usuarioLogado.usuarioPACTOBR}">
                        <i class="fa-icon-calendar" style="color:grey;"></i>
                    </c:if>
                </h:panelGroup>
                <h:commandLink rendered="#{SuporteControle.desv}"
                               onclick="abrirPopup('robocontrol.jsp', 'RoboControl', 654, 300);"
                               styleClass="relogios"
                               style=" font-size: 12px;color: #9E9E9E;vertical-align: middle;position: relative;top: -2px;margin-right: 3px;z-index: 1">
                    <i class="fa-icon-calendar" style="color:grey"></i>
                </h:commandLink>

                <c:if test="${SuperControle.menuZwUi and !LoginControle.usuarioLogado.usuarioPACTOBR}">
                    <h:outputText id="dataSistema" styleClass="relogios textSmallFlat2"
                                  value="#{SuporteControle.dataAtual}"/>
                    <h:outputText id="horaSistema" style="padding-left: 5px" styleClass="relogios textSmallFlat2"
                                  rendered="#{!SuporteControle.desv}" value="#{SuporteControle.agora}"/>
                </c:if>
                <c:if test="${SuperControle.menuZwUi and LoginControle.usuarioLogado.usuarioPACTOBR}">
                    <h:outputText style="font-size: 12px;" value="#{SuporteControle.dataNovoPadraoZWUI}">
                    </h:outputText>
                </c:if>
                <h:outputText style="padding-left: 5px" styleClass="relogios textSmallFlat2"
                              rendered="#{!SuporteControle.desv and SuperControle.enableCountDown}" value=" - "/>
                <h:outputText rendered="#{!SuporteControle.desv and SuperControle.enableCountDown}"
                              style="padding-left: 5px" styleClass="fa-icon-time areaLogout"/>
                <c:if test="${SuperControle.menuZwUi}">
                    <h:outputText id="tempo"
                                  styleClass="areaLogout "
                                  title="Tempo Restante de sua Sessão. Para sua segurança, depois deste tempo, você será desconectado automaticamente."/>
                </c:if>
                <div class="system-stat">
                    <c:if test="${LoginControle.usuarioLogado.usuarioPACTOBR}">
                        <h:graphicImage url="/imagens_flat/pct-map-pin.png" style="color:grey;"></h:graphicImage>
                    </c:if>
                    <c:if test="${!LoginControle.usuarioLogado.usuarioPACTOBR}">
                        <i class="pct pct-monitor"></i>
                    </c:if>
                    IP:
                        ${SuperControle.ipCliente}
                </div><!---->
                <div class="system-stat">
                    <h:commandLink action="#{SuperControle.mudarMenu}">
                        <c:if test="${LoginControle.usuarioLogado.usuarioPACTOBR}">
                            <h:graphicImage url="/imagens_flat/pct-monitor.svg"></h:graphicImage>
                        </c:if>
                        <c:if test="${!LoginControle.usuarioLogado.usuarioPACTOBR}">
                            <i class="pct pct-package"></i>
                        </c:if>
                    </h:commandLink>
                    Sistema:
                        ${SuperControle.versaoSistema_Apresentar} ${SuperControle.shortCommitHash}
                </div><!---->
                <c:if test="${!LoginControle.usuarioLogado.usuarioPACTOBR}">
                    <div class="system-stat"><i class="pct pct-globe"></i> Lang: <span
                            class="language"> pt </span></div>
                </c:if>
            </div>
        </div>
    </div>

</h:panelGroup>

<c:if test="${SuperControle.pontoInterrogacaoHabilitado}">
    <jsp:include page="/include_ponto_interrogacao.jsp"/>
    <a4j:jsFunction name="notificarClickChat"
                    action="#{SuperControle.notificarEmpresaClickChat}">
    </a4j:jsFunction>
</c:if>


<div class="modal-usuario-zw-ui  balloon-modal" style="overflow-y: scroll;" onclick="fecharUsuario()">
    <div role="document" class="modal-dialog" id="dropDownUsuario">
        <div id="modal-content-usuario-zw-ui" class="modal-content-usuario-zw-ui">
            <div>
                <div class="pacto-modal-wrapper">
                    <div id="modal-conteudo-menu-superior" class="modal-conteudo"><!---->
                        <div>
                            <div class="blue-container"></div>
                            <div class="blue-container-assinatura">

                                <div class="modal-title">QR Code</div>

                                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                    <span class="class-span" aria-hidden="true">&times;</span>
                                </button>

                            </div>
                            <div class="blue-container-usuario">

                                <div class="modal-title">Perfil</div>

                                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                    <span class="class-span" aria-hidden="true">&times;</span>
                                </button>

                            </div>
                            <div id="container-zw-ui" class="container-zw-ui">
                                <div class="container-assinatura-box-zw-ui row-qr-code-zw-ui">
                                    <div class="col-md-12">
                                        <div class="container" onclick="fecharUsuario()">
                                            <a4j:commandLink action="#{QRCodeControle.prepararQRCodeAssinaturaDigital}"
                                                             oncomplete="javascript:Richfaces.showModalPanel('modalAssinaturaDigital',{width:450, top:150}, {formid:'somevalue'})"
                                                             reRender="qrCodeAssinaturaDigital">
                                                <div class="option">
                                                    <div class="option-img">
                                                        <h:graphicImage url="/imagens_flat/pct-assinatura-digital.svg"/>
                                                    </div>
                                                    <span class="class-span">Assinatura Digital</span></div>
                                            </a4j:commandLink>
                                            <hr class="class-hr">
                                            <a4j:commandLink action="#{QRCodeControle.prepararQRCodeCartaoVacinacao}"
                                                             oncomplete="javascript:Richfaces.showModalPanel('modalCartaoVacina',{width:450, top:150}, {formid:'somevalue'})"
                                                             reRender="qrCodeCartaoVacina">
                                                <div class="option">
                                                    <div class="option-img">
                                                        <h:graphicImage url="/imagens_flat/pct-cartao-vacina.svg"/>
                                                    </div>
                                                    <span class="class-span">Cartão de Vacina</span></div>
                                            </a4j:commandLink>
                                            <hr class="class-hr">
                                            <a4j:commandLink action="#{QRCodeControle.prepararQRCodeFormularioParQ}"
                                                             oncomplete="javascript:Richfaces.showModalPanel('modalParQ',{width:450, top:150}, {formid:'somevalue'})"
                                                             reRender="qrCodeFormularioParQ">
                                                <div class="option">
                                                    <div class="option-img">
                                                        <h:graphicImage url="/imagens_flat/pct-parq.svg"/>
                                                    </div>
                                                    <span class="class-span">Formulário Par-Q +</span></div>
                                            </a4j:commandLink>
                                            <hr class="class-hr">
                                        </div>
                                    </div>
                                </div>

                                <div class="container-favoritos-box-zw-ui container-alunos-favoritos"
                                     style="margin-bottom: 1em;"
                                     onclick="event.stopPropagation()">
                                    <div style="border-radius: 8px 8px 8px 8px;margin-left: 16px;margin-right: 16px;">
                                        <div class="titulo-notificacoes">Alunos favoritos
                                            <div style="margin-top: -0.1em;cursor: pointer;float: right;font-family: 'Nunito Sans';background: 0;font-weight: 100;border-color: 0;font-size: 20px;border: 0;"
                                                 onclick="fecharUsuario()">x
                                            </div>
                                        </div>
                                        <div style="font-family: 'Nunito Sans';font-style: normal;font-weight: 400;font-size: 16px;line-height: 125%;margin-top: 12px;">
                                            <div style="margin-bottom: 12px">
                                                Favoritos
                                            </div>

                                            <h:panelGroup id="panelAlunosFavoritos">
                                                <h:panelGroup rendered="#{empty ClientesMarcadosControle.clientesFavoritos}">
                                                    <div class="nao-existe-aluno-favorito"
                                                         style="margin-bottom: 16px; margin-top: 16px;font-size: 14px">
                                                        Nenhum aluno adicionado na sua lista de favoritos. Você pode
                                                        adicionar até 3 (três) alunos aqui.
                                                    </div>
                                                    <div style="border-bottom: solid 1px #EFF2F7;margin-top: 16px;margin-left: -1em; width: 17.5em;"></div>

                                                </h:panelGroup>

                                                <a4j:repeat value="#{ClientesMarcadosControle.clientesFavoritos}"
                                                            var="clienteM">
                                                    <div style="padding-bottom: 1em;">
                                                        <a4j:commandLink action="#{ClientesMarcadosControle.abrirCliente}" styleClass="imagem-aluno-favoritos"
                                                                         onmouseover="mostraIconeLembrete('#{clienteM.codigo}')"
                                                                         onmouseout="escondeIconeLembrete('#{clienteM.codigo}')">
                                                            <h:graphicImage styleClass="imagemAluno pequena"
                                                                            url="#{(SuperControle.fotosNaNuvem ? ClientesMarcadosControle.fotoNuvem : clienteM.pessoa.urlFotoContexto )}">
                                                            </h:graphicImage>
                                                        </a4j:commandLink>

                                                        <div style="display: inline-block;margin-left: 1em; cursor: pointer;"
                                                             onclick="abrirClienteLink(this)"
                                                             onmouseover="abrirMouseOver(this)"
                                                             onmouseout="abrirMouseOut(this)">
                                                            <div style="font-family: 'Nunito Sans';font-style: normal;font-weight: 400;font-size: 16px;line-height: 125%;color: #51555A;">
                                                                <h:outputText
                                                                        value="#{clienteM.pessoa.nome}"></h:outputText>

                                                                <div class="cmarc" style="display: inline-table;"
                                                                     onclick="event.stopPropagation()">
                                                                    <a4j:commandLink
                                                                            id="botao-lembrete-aluno-favorito"
                                                                            styleClass=""
                                                                            onclick="escondeInputLembrete('#{ClientesMarcadosControle.codigosClientesMarcadosFavoritos}','#{clienteM.codigo}');mostraInputLembrete('#{clienteM.codigo}');"
                                                                            onmouseover="mostraIconeLembrete('#{clienteM.codigo}');mostraToolTipLembrete('#{clienteM.codigo}');"
                                                                            onmouseout="escondeIconeLembrete('#{clienteM.codigo}');escondeToolTipLembrete('#{clienteM.codigo}');"
                                                                            status="false">
                                                                        <h:graphicImage
                                                                                styleClass="imagem-lembrete-nulo#{clienteM.codigo}"
                                                                                rendered="#{clienteM.observacao == ''}"
                                                                                url="/imagens_flat/pct-message-square.svg"
                                                                                style="margin-left: 0.5em;visibility: hidden">
                                                                        </h:graphicImage>
                                                                        <h:graphicImage
                                                                                rendered="#{clienteM.observacao != ''}"
                                                                                url="/imagens_flat/pct-message-square-2.svg"
                                                                                style="margin-top: -1em;margin-left: 0.5em;">
                                                                        </h:graphicImage>
                                                                    </a4j:commandLink>
                                                                    <h:panelGroup
                                                                            rendered="#{clienteM.observacao != ''}"
                                                                            styleClass="tooltip-lembrete#{clienteM.codigo}"
                                                                            style="line-break: anywhere;padding: 8px;display:none;background: #FFFFFF;border: 1px solid #B4B7BB;border-radius: 4px;position: absolute;width: 176px;min-height:80px;margin-top: -9.3em;margin-left: -7.5em;">
                                                                        <h:outputText
                                                                                style="font-family: 'Nunito Sans';font-style: normal;font-weight: 400;font-size: 14px;line-height: 125%;display: flex;align-items: center;color: #90949A;"
                                                                                value="#{clienteM.observacao}">
                                                                        </h:outputText>
                                                                    </h:panelGroup>
                                                                    <h:panelGroup
                                                                            styleClass="input #{clienteM.codigo}">
                                                                        <div style="width: 172px !important;display: flex;flex-direction: column;align-items: flex-end;padding: 10px;gap: 4px;width: 180px;height: 120px;background: #FFFFFF;border: 1px solid #B4B7BB;border-radius: 4px;position: fixed;margin-left: -7.5em;margin-top: -10.1em;"
                                                                             onclick="event.stopPropagation()">
                                                                            <h:inputTextarea
                                                                                    id="input-text-lembrete"
                                                                                    style="border: none;outline: none;"
                                                                                    value="#{clienteM.observacao}"
                                                                                    styleClass="input-lembrete-aluno"></h:inputTextarea>
                                                                            <a4j:commandLink
                                                                                    id="botao-salvar-lembrete"
                                                                                    styleClass="botaoPrimarioSmall texto-size-14-real"
                                                                                    style="font-size:14px;font-color: white;display: flex;flex-direction: row;justify-content: center;align-items: center;padding: 8px 12px;width: 58px;height: 26px;background: #0380E3;border-radius: 1.99694px;"
                                                                                    onclick="escondeInputLembrete('#{ClientesMarcadosControle.codigosClientesMarcadosFavoritos}')"
                                                                                    action="#{ClientesMarcadosControle.gravarObservacao}"
                                                                                    oncomplete="#{ClientesMarcadosControle.mensagemNotificar};escondeInputLembrete('#{ClientesMarcadosControle.codigosClientesMarcadosFavoritos}')"
                                                                                    reRender="panelAlunosFavoritos">
                                                                                Salvar
                                                                            </a4j:commandLink>
                                                                        </div>
                                                                    </h:panelGroup>
                                                                </div>
                                                            </div>
                                                            <div style="font-family: 'Nunito Sans';font-style: normal;font-weight: 400;font-size: 14px;line-height: 125%;color:#6F747B;">
                                                                <h:outputText
                                                                        value="Mat: #{clienteM.matricula}"></h:outputText>
                                                                <div style="color: white;text-align-last: center;font-size: 8px;">
                                                                    <h:outputText
                                                                            rendered="#{clienteM.situacao == 'AT'}">
                                                                        <div class="matriculaAlunoFavorito"
                                                                             style="background-color: #28AB45;">
                                                                            AT
                                                                        </div>
                                                                    </h:outputText>
                                                                    <h:outputText
                                                                            rendered="#{clienteM.situacao == 'IN'}">
                                                                        <div class="matriculaAlunoFavorito"
                                                                             style="background-color: #db2c3d;">
                                                                            IN
                                                                        </div>
                                                                    </h:outputText>
                                                                    <h:outputText
                                                                            rendered="#{clienteM.situacao == 'VI'}">
                                                                        <div class="matriculaAlunoFavorito"
                                                                             style="background-color: #f0b924;">
                                                                            VI
                                                                        </div>
                                                                    </h:outputText>
                                                                    <h:outputText
                                                                            rendered="#{clienteM.situacao == 'TR'}">
                                                                        <div class="matriculaAlunoFavorito"
                                                                             style="background-color: #fbc02d;">
                                                                            TR
                                                                        </div>
                                                                    </h:outputText>
                                                                </div>
                                                            </div>
                                                        </div>

                                                        <div style="text-align-last: end;"
                                                             onclick="event.stopPropagation()">
                                                            <a4j:commandLink
                                                                    id="botao-desativa-favorito"
                                                                    reRender="panelAlunosFavoritos"
                                                                    action="#{ClientesMarcadosControle.desmarcaClienteFavorito}"
                                                                    oncomplete="#{ClientesMarcadosControle.mensagemNotificar};escondeInputLembrete('#{ClientesMarcadosControle.codigosClientesMarcadosFavoritos}')"
                                                                    style="margin-left: 7em;">
                                                                <h:graphicImage
                                                                        url="/imagens_flat/pct-star-blue.svg"
                                                                        style="margin-top: -4.4em;">
                                                                </h:graphicImage>
                                                            </a4j:commandLink>

                                                        </div>
                                                        <div style="border-bottom: solid 1px #EFF2F7;margin-top: 1.3em;margin-left: -1em;width: 17.5em;"></div>

                                                    </div>
                                                </a4j:repeat>
                                            </h:panelGroup>
                                        </div>

                                        <div style="font-family: 'Nunito Sans';font-style: normal;font-weight: 400;font-size: 16px;line-height: 125%;margin-top: 12px;">
                                            <div style="margin-bottom: 12px">
                                                Recentes
                                            </div>

                                            <h:panelGroup rendered="#{empty ClientesMarcadosControle.clientesRecentes}">
                                                <div style="margin-bottom: 16px; margin-top: 16px;font-size: 14px;">
                                                    Nâo existe nenhum aluno recente.
                                                </div>
                                                <div style="border-bottom: solid 1px #EFF2F7;margin-top: 16px; width: 16em;"></div>

                                            </h:panelGroup>

                                            <a4j:repeat value="#{ClientesMarcadosControle.clientesRecentes}"
                                                        var="clienteM">
                                                <div style="padding-bottom: 1em;">
                                                    <h:commandLink action="#{ClientesMarcadosControle.abrirCliente}">
                                                        <h:graphicImage styleClass="imagemAluno pequena"
                                                                        url="#{(SuperControle.fotosNaNuvem ? ClientesMarcadosControle.fotoNuvem : clienteM.pessoa.urlFotoContexto )}">
                                                        </h:graphicImage>
                                                        <div style="display: inline-block;margin-left: 1em;">
                                                            <div style="font-family: 'Nunito Sans';font-style: normal;font-weight: 400;font-size: 16px;line-height: 125%;color: #51555A;">
                                                                <h:outputText
                                                                        value="#{clienteM.pessoa.nome}"></h:outputText>
                                                            </div>
                                                            <div style="font-family: 'Nunito Sans';font-style: normal;font-weight: 400;font-size: 14px;line-height: 125%;color:#6F747B;">
                                                                <h:outputText
                                                                        value="Mat: #{clienteM.matricula}"></h:outputText>
                                                                <div style="color: white;text-align-last: center;font-size: 8px;">
                                                                    <h:outputText
                                                                            rendered="#{clienteM.situacao == 'AT'}">
                                                                        <div class="matriculaAlunoFavorito"
                                                                             style="background-color: #28AB45;">
                                                                            AT
                                                                        </div>
                                                                    </h:outputText>
                                                                    <h:outputText
                                                                            rendered="#{clienteM.situacao == 'IN'}">
                                                                        <div class="matriculaAlunoFavorito"
                                                                             style="background-color: #db2c3d;">
                                                                            IN
                                                                        </div>
                                                                    </h:outputText>
                                                                    <h:outputText
                                                                            rendered="#{clienteM.situacao == 'VI'}">
                                                                        <div class="matriculaAlunoFavorito"
                                                                             style="background-color: #f0b924;">
                                                                            VI
                                                                        </div>
                                                                    </h:outputText>
                                                                    <h:outputText
                                                                            rendered="#{clienteM.situacao == 'TR'}">
                                                                        <div class="matriculaAlunoFavorito"
                                                                             style="background-color: #fbc02d;">
                                                                            TR
                                                                        </div>
                                                                    </h:outputText>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </h:commandLink>

                                                    <div style="text-align-last: end;">
                                                        <a4j:commandLink reRender="panelAlunosFavoritos"
                                                                         id="botao-ativa-favorito"
                                                                         action="#{ClientesMarcadosControle.marcaClienteFavorito}"
                                                                         oncomplete="#{ClientesMarcadosControle.mensagemNotificar}; escondeInputLembrete('#{ClientesMarcadosControle.codigosClientesMarcadosFavoritos}')"
                                                                         style="margin-left: 7em;">
                                                            <h:graphicImage url="/imagens_flat/pct-star.svg"
                                                                            style="margin-top: -3em;">
                                                            </h:graphicImage>
                                                        </a4j:commandLink>
                                                    </div>
                                                    <div style="border-bottom: solid 1px #EFF2F7;margin-top: 1.3em;margin-left: -1em;width: 17.5em;"></div>

                                                </div>
                                            </a4j:repeat>
                                        </div>
                                    </div>
                                </div>

                                <div class="container-user-box-zw-ui row-user-zw-ui menu-usuario">
                                    <div class="col-md-2">
                                        <img class="menu-usuario-logo" src="${SuperControle.fotoKeyUsuarioLogado}">
                                    </div>
                                    <div class="col-md-8 menu-usuario-titulo">
                                        <span class="menu-usuario-nome" style="width: 14em;">${SuperControle.usuarioLogado.nomePrimeiraLetraMaiuscula}</span>
                                        <span class="menu-usuario-perfil" style="width: 15.9em;">${SuperControle.perfilUsuarioLogado.nomePrimeiraLetraMaiuscula}</span>
                                    </div>
                                </div>
                                <c:if test="${LoginControle.usuario.permiteAlterarPropriaSenha && !LoginControle.usuario.usuarioPactoSolucoes}">
                                    <div class="container-user-box-zw-ui row-user-zw-ui menu-usuario row-user-zw-ui row-action">
                                        <div tabindex="0">
                                            <div _nghost-rny-c3="">
                                                <a4j:commandLink
                                                        rendered="#{LoginControle.usuario.permiteAlterarPropriaSenha}"
                                                        actionListener="#{FuncionalidadeControle.prepararFuncionalidade}"
                                                        oncomplete="#{FuncionalidadeControle.abrirPopUp}"
                                                        action="#{FuncionalidadeControle.abrirComCasoNavegacao}"
                                                        styleClass="pacto-button pacto-primary"
                                                        id="pacto-btn-49">
                                                    <f:attribute name="funcionalidade" value="DADOS_USUSARIO"/>
                                                    <div class="menu-usuario menu-usuario-acao">
                                                        <span>Editar Perfil</span>
                                                        <i class="pct pct-chevron-right"></i>
                                                    </div>
                                                </a4j:commandLink>
                                            </div>
                                        </div>
                                    </div>
                                </c:if>
                                <div class="container-user-box-zw-ui row-user-zw-ui menu-usuario row-user-zw-ui row-action">
                                    <div tabindex="0">
                                        <div _nghost-rny-c3="">
                                            <a4j:commandLink
                                                    actionListener="#{FuncionalidadeControle.prepararFuncionalidade}"
                                                    oncomplete="#{FuncionalidadeControle.abrirPopUp}"
                                                    action="#{FuncionalidadeControle.abrirComCasoNavegacao}"
                                                    styleClass="pacto-button pacto-primary"
                                                    id="menu-usuario-btn-canal-cliente">
                                                <f:attribute name="funcionalidade" value="CANAL_CLIENTE"/>
                                                <div class="menu-usuario menu-usuario-acao">
                                                    <span>Canal do cliente</span>
                                                    <i class="pct pct-chevron-right"></i>
                                                </div>
                                            </a4j:commandLink>
                                        </div>
                                    </div>
                                </div>
                                <c:if test="${not empty SuporteControle.clubeDeBeneficios.link}">
                                    <div class="container-user-box-zw-ui row-user-zw-ui menu-usuario row-user-zw-ui row-action">
                                        <div tabindex="0">
                                            <div _nghost-rny-c3="">
                                                <a4j:commandLink
                                                        actionListener="#{FuncionalidadeControle.prepararFuncionalidade}"
                                                        oncomplete="#{FuncionalidadeControle.abrirPopUp}"
                                                        action="#{FuncionalidadeControle.abrirComCasoNavegacao}"
                                                        styleClass="pacto-button pacto-primary"
                                                        id="menu-clube-de-beneficio">
                                                    <f:attribute name="funcionalidade" value="CLUBE_DE_BENEFICIOS"/>
                                                    <div class="menu-usuario menu-usuario-acao">
                                                        <span>Clube de Beneficios</span>
                                                        <i class="pct pct-chevron-right"></i>
                                                    </div>
                                                </a4j:commandLink>
                                            </div>
                                        </div>
                                    </div>
                                </c:if>
                                <div class="container-user-box-zw-ui row-user-zw-ui menu-usuario row-user-zw-ui row-exit">
                                    <a id="pacto-btn-13"
                                       onclick="clearLocalSession(); document.location.href='${LogoutControle.redirectLogout}'"
                                       class="button-sair">Sair do Sistema
                                        <i class="pct pct-log-out"></i></a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<rich:modalPanel id="modalQRCodeAppGestor" style="border-radius: 4.8px; min-height: 300px;border-color: #000000;"
                 domElementAttachment="parent" zindex="1101"
                 styleClass="modal-app-gestor" shadowDepth="false"
                 shadowOpacity="false" width="488" height="400"
                 onmaskclick="Richfaces.hideModalPanel('modalQRCodeAppGestor');"
                 moveable="false" resizeable="false">
    <div class="close-modal">
        <a onclick="Richfaces.hideModalPanel('modalQRCodeAppGestor');" href="#">
            <h:graphicImage url="/imagens_flat/icon-close.png" styleClass="">
            </h:graphicImage>
        </a>
    </div>
    <h3 style="width: 100%; text-align: center; font-size: 1.8em; overflow: hidden; font-family: 'Nunito Sans',sans-serif">
        Expira em <span style="z-index: 1102;font-family: 'Nunito Sans',sans-serif" id="sessaoQRCodeZWUI">
    </span> segundos</h3>
    <div class="QR-code-zw-ui" style="margin-top: -25px">
        <h:panelGroup layout="block" rendered="#{QRCodeControle.apresentarQRCode}">
            <h:graphicImage url="#{QRCodeControle.urlQRCode}"/>
        </h:panelGroup>
    </div>
</rich:modalPanel>


<rich:modalPanel id="modalAppGestor" style="border-radius: 4.8px; min-height: 300px"
                 domElementAttachment="parent" zindex="1101"
                 styleClass="modal-app-gestor"
                 shadowOpacity="false" width="488" height="400"
                 onmaskclick="Richfaces.hideModalPanel('modalAppGestor');"
                 moveable="false" resizeable="false">

    <div>
        <div class="modal-titulo">
            <div class="titulo-modal-app-gestor">
                Obter Acesso ao App do Gestor
            </div>
        </div>
        <div class="close-modal">
            <a onclick="Richfaces.hideModalPanel('modalAppGestor');" href="#">
                <h:graphicImage url="/imagens_flat/icon-close.png" styleClass="">
                </h:graphicImage>
            </a>
        </div>
        <div class="modal-conteudo-app-gestor">
            <div class="container-acesso">
                <div class="text-app-gestor">
                    Faça login sem dificuldades: Entre com sua senha do ZW para gerar um QRCode que poderá ser lido pelo
                    "App do Gestor"
                </div>
                <h:outputText styleClass="rotulo-campos-app-gestor" value="E-mail:"
                              style="margin-right: 20px;"></h:outputText>
                <h:outputText value="#{LoginControle.usuarioEmailVO.email}" rendered="#{!LoginControle.novoEmailQR}"
                              style="font-weight:100;" styleClass="rotulo-campos-app-gestor"></h:outputText>
                <h:outputText value="Senha:" style="padding-top: 30"
                              styleClass="rotulo-campos-app-gestor"></h:outputText>
                <h:inputSecret styleClass="inputTextClean" style="color: #67757c;margin-bottom: 31px;" size="60"
                               maxlength="64"
                               onkeypress="validarEnterSenha(event,'form:btnAutorizaAppGestor');"
                               value="#{LoginControle.senhaQRCode}"/>
                <h:panelGroup layout="block" styleClass="container-botoes">
                    <a4j:commandButton id="btnAutorizaAppGestor"
                                       value="GERAR QRCODE"
                                       styleClass="botaoGeraQRCODE texto-size-5"
                                       oncomplete="#{LoginControle.msgAlert}"
                                       action="#{QRCodeControle.prepararQRCodeAppGestor}"
                                       reRender="modalQRCodeAppGestor"/>
                </h:panelGroup>
            </div>
        </div>
    </div>
</rich:modalPanel>

<rich:modalPanel id="modalAssinaturaDigital" style="border-radius: 4.8px; min-height: 300px"
                 domElementAttachment="parent" zindex="1101"
                 autosized="true" styleClass="modal-app-gestor"
                 shadowOpacity="false" width="488" height="400"
                 onmaskclick="Richfaces.hideModalPanel('modalAssinaturaDigital');"
                 moveable="false" resizeable="false">
    <div>
        <div class="modal-titulo">
            <div class="titulo-modal-app-gestor">
                Acessar Assinatura Digital
            </div>
        </div>
        <div class="close-modal">
            <a onclick="Richfaces.hideModalPanel('modalAssinaturaDigital');" href="#">
                <h:graphicImage url="/imagens_flat/icon-close.png" styleClass="">
                </h:graphicImage>
            </a>
        </div>
        <div class="modal-conteudo-app-gestor">
            <div class="container-acesso">
                <div class="text-app-gestor" style="text-align: center;">
                    <span>
                        Leia o QRCode e vá até o Assinatura Digital no seu dispositivo móvel, ou acesse o link:
                            <a4j:commandLink
                                    value="Assinatura Digital"
                                    style="font-size: 14px; margin-left: 5px;"
                                    action="#{LoginControle.notificarAssinaturaDigital}"
                                    oncomplete="window.open('#{LoginControle.urlAssinaturaDigitalDireta}', '_blank');">
                            </a4j:commandLink>
                    </span>
                </div>
            </div>
        </div>
        <h:panelGroup id="qrCodeAssinaturaDigital" layout="block" styleClass="QR-code-zw-ui">
            <h:panelGroup layout="block" rendered="#{QRCodeControle.apresentarQRCode}">
                <h:graphicImage url="#{QRCodeControle.urlQRCode}"/>
            </h:panelGroup>
        </h:panelGroup>
    </div>
</rich:modalPanel>

<rich:modalPanel id="modalCartaoVacina" style="border-radius: 4.8px; min-height: 300px"
                 domElementAttachment="parent" zindex="1101"
                 autosized="true" styleClass="modal-app-gestor"
                 shadowOpacity="false" width="488" height="400"
                 onmaskclick="Richfaces.hideModalPanel('modalCartaoVacina');"
                 moveable="false" resizeable="false">
    <div>
        <div class="modal-titulo">
            <div class="titulo-modal-app-gestor">
                Acessar Cartão de Vacina
            </div>
        </div>
        <div class="close-modal">
            <a onclick="Richfaces.hideModalPanel('modalCartaoVacina');" href="#">
                <h:graphicImage url="/imagens_flat/icon-close.png" styleClass="">
                </h:graphicImage>
            </a>
        </div>
        <div class="modal-conteudo-app-gestor">
            <div class="container-acesso">
                <div class="text-app-gestor" style="text-align: center;">
                    <span>
                        Leia o QRCode e vá até o Cartão de Vacina no seu dispositivo móvel, ou acesse o link:
                        <a4j:commandLink
                                value="Cartão de Vacina"
                                style="font-size: 14px; margin-left: 5px;"
                                action="#{LoginControle.notificarCartaoVacina}"
                                oncomplete="window.open('#{LoginControle.urlAssinaturaDigitalDireta}', '_blank');">
                        </a4j:commandLink>
                    </span>
                </div>
            </div>
        </div>
        <h:panelGroup id="qrCodeCartaoVacina" layout="block" styleClass="QR-code-zw-ui">
            <h:panelGroup layout="block" rendered="#{QRCodeControle.apresentarQRCode}">
                <h:graphicImage url="#{QRCodeControle.urlQRCode}"/>
            </h:panelGroup>
        </h:panelGroup>
    </div>
</rich:modalPanel>

<rich:modalPanel id="modalParQ" style="border-radius: 4.8px; min-height: 300px"
                 domElementAttachment="parent" zindex="1101"
                 autosized="true" styleClass="modal-app-gestor"
                 shadowOpacity="false" width="488" height="400" onmaskclick="Richfaces.hideModalPanel('modalParQ');"
                 moveable="false" resizeable="false">
    <div>
        <div class="modal-titulo">
            <div class="titulo-modal-app-gestor">
                Acessar Formulário Par-Q +
            </div>
        </div>
        <div class="close-modal">
            <a onclick="Richfaces.hideModalPanel('modalParQ');" href="#">
                <h:graphicImage url="/imagens_flat/icon-close.png" styleClass="">
                </h:graphicImage>
            </a>
        </div>
        <div class="modal-conteudo-app-gestor">
            <div class="container-acesso">
                <div class="text-app-gestor" style="text-align: center;">
                    <span>
                        Leia o QRCode e vá até o Formulário Par-Q + no seu dispositivo móvel, ou acesse o link:
                        <a4j:commandLink
                                value="Formulário Par-Q +"
                                style="font-size: 14px; margin-left: 5px;"
                                oncomplete="window.open('#{LoginControle.urlAssinaturaDigitalDireta}', '_blank');">
                        </a4j:commandLink>
                    </span>
                </div>
            </div>
        </div>
        <h:panelGroup id="qrCodeFormularioParQ" layout="block" styleClass="QR-code-zw-ui">
            <h:panelGroup layout="block" rendered="#{QRCodeControle.apresentarQRCode}">
                <h:graphicImage url="#{QRCodeControle.urlQRCode}"/>
            </h:panelGroup>
        </h:panelGroup>
    </div>
</rich:modalPanel>
<div class="modal-favorito-zw-ui" style="overflow-y: scroll;" id="modalNotificacaoFavorito" onclick="fecharMsgFavorito()">
    <div role="document" class="modal-msg-item-favorito" id="modalNatificacao">
        <h:panelGroup  id="panelMsg" >
            <div class="modal-content-favorito-zw-ui">
                <div class="top-container-favorito">
                    <i class="icoMsg"></i>
                    <div class="msg-title">${MenuAcessoFacilControle.topMsgResultAddFavorito}</div>

                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span class="class-span" aria-hidden="true">&times;</span>
                    </button>

                </div>
                <div class="container-favorito">
                    <div class="msg-container">${MenuAcessoFacilControle.msgResultAddFavorito}</div>
                </div>
            </div>
        </h:panelGroup>
    </div>
</div>
<a4j:jsFunction status="statusHora" name="enviarIp" action="#{SuperControle.enviar}">
    <a4j:actionparam name="tmpIP" assignTo="#{SuperControle.ip}"/>
</a4j:jsFunction>
<a4j:jsFunction status="statusHora" name="enviarBrowser" action="#{SuperControle.enviarBrowser}">
    <a4j:actionparam name="tmpBrowser" assignTo="#{SuperControle.browser}"/>
    <a4j:actionparam name="tmpW" assignTo="#{SuperControle.widthScreenClient}"/>
    <a4j:actionparam name="tmpH" assignTo="#{SuperControle.heightScreenClient}"/>
    <a4j:actionparam name="protocol" assignTo="#{SuperControle.protocol}"/>
    <a4j:actionparam name="tmpURLBrowser" assignTo="#{SuperControle.urlBrowser}"/>
</a4j:jsFunction>
<div class="modal-notificacao-canal-do-cliente" style="overflow-y: scroll;" id="modalNotificacaoCanalDoCliente" onclick="fecharMsgNotificacao()">
    <div role="document" class="modal-msg-canal-do-cliente" id="modalNatificacaoCanalDoCliente">
        <h:panelGroup  id="panelMsgCanalDocliente" >
            <div class="top-container-canal-do-cliente">
                <div class="msg-title-canal-do-cliente">Aviso</div>

                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span class="class-span" aria-hidden="true">&times;</span>
                </button>

            </div>
            <div class="container-canal-do-cliente">
                <div class="msg-container-canal-do-cliente">${msg.Mensagem_modal_canal_do_cliente}</div>


                <div class="div-btn">
                    <a4j:commandLink style="text-decoration: none" styleClass="btn-classe">
                    <div class="container-btn-fechar" onclick="fecharMsgNotificacao()">
                        Fechar
                    </div>
                    </a4j:commandLink>
                    <a4j:commandLink style="text-decoration: none" styleClass="btn-classe">
                    <div id="divModalAbrirUsuario" class="background container-btn-acesso" onclick="abrirModalUsuario(event)">
                        Acessar o perfil
                    </div>
                    </a4j:commandLink>
                </div>
            </div>

        </h:panelGroup>
    </div>
</div>
<h:panelGroup layout="block" styleClass="container-imagem-configuracao" style="position:relative;">
    <jsp:include page="include_box_empty_state_configuracao.jsp"/>
</h:panelGroup>

<script>
    document.addEventListener('DOMContentLoaded', function () {
        const currentPage = parseInt(jQuery('#modules-menu').attr("current-page"));
        if (currentPage > 1) {
            jQuery('#modules-menu').animate({
                'scrollTop': jQuery('#modules-menu-page-' + currentPage).offset().top
            }, 0);
        }
        toggleNavigatonModules();
    });

    function toggleNavigatonModules() {
        const currentPage = parseInt(jQuery('#modules-menu').attr("current-page"));
        const totalPages = jQuery('.modules-menu-page').size();

        if (currentPage == 1) {
            jQuery('#modules-menu-nav-up').addClass("modules-menu-mav-disable");
        }

        if (currentPage == totalPages) {
            jQuery('#modules-menu-nav-down').addClass("modules-menu-mav-disable");
        }

        if (currentPage !== 1) {
            jQuery('#modules-menu-nav-up').removeClass("modules-menu-mav-disable");
        }

        if (currentPage < totalPages) {
            jQuery('#modules-menu-nav-down').removeClass("modules-menu-mav-disable");
        }
    }
    function scrollModules(direction){
        const alturaResolucao = window.innerHeight;
        const currentPage = parseInt(jQuery('#modules-menu').attr("current-page"));
        const totalPages =  jQuery('.modules-menu-page').size();
        var parametroDeMovimentacao = 225;
        if(alturaResolucao < 720){
            parametroDeMovimentacao = 225;
        }

        if(direction === 'up' && currentPage != 1){
            const nextPage = currentPage-1;
            if(nextPage ==1){
                jQuery('#modules-menu').animate({
                    'scrollTop':0
                }, 0).attr("current-page", nextPage);
            }else{
                jQuery('#modules-menu').animate({
                    'scrollTop':parametroDeMovimentacao * (currentPage - nextPage)
                }, 0).attr("current-page", nextPage);
            }
        }

        if(direction === 'down' && currentPage < totalPages){
            const nextPage = currentPage+1;
            jQuery('#modules-menu').animate({
                'scrollTop':parametroDeMovimentacao * currentPage
            }, 0).attr("current-page", nextPage);
        }

        toggleNavigatonModules();
    }

    function showQRCodeZWUI() {
        Richfaces.showModalPanel('modalQRCodeAppGestor', {width: 450, top: 200}, {formid: 'somevalue'});
        Richfaces.hideModalPanel('modalAppGestor');
    }

    function hideModalQRCodeZWUI() {
        Richfaces.hideModalPanel('modalQRCodeAppGestor');
    }

    var diferencaQrCodeZWUI = 30 * 1000;
    var dataFinalQrCodeZWUI = new Date(new Date().getTime() + diferencaQrCodeZWUI);

    function startQRCodeZWUI() {
        diferencaQrCodeZWUI = 30 * 1000;
        dataFinalQrCodeZWUI = new Date(new Date().getTime() + diferencaQrCodeZWUI);
        startCountdownQRCodeZWUI();
    }

    function startCountdownQRCodeZWUI() {
        var agoraQrCode = new Date();
        var numberCountdown = document.getElementById('sessaoQRCodeZWUI');
        diferencaQrCodeZWUI = dataFinalQrCodeZWUI.getTime() - agoraQrCode.getTime();
        var texto = Math.floor(diferencaQrCodeZWUI / 1000);
        if (diferencaQrCodeZWUI > 0) {
            if (document.all) {
                numberCountdown.innerText = texto;
            } else {
                numberCountdown.textContent = texto;
            }
            setTimeout("startCountdownQRCodeZWUI()", 500);
        } else {
            hideModalQRCodeZWUI();
        }
    }

    var token = "";

    function wiki() {
        var url = "https://wiki.pactosolucoes.com.br/content/api.php";
        var rtype = "jsonp";

        jQuery.ajax({
            type: "POST",
            data: {
                action: "login",
                lgname: "zillyonweb",
                lgpassword: "xq7J4m3n",
                format: "json"
            },
            url: url,
            dataType: rtype,
            timeout: 1000,
            success: function (obj) {
                token = obj.login.token;
                jQuery.ajax({
                    type: "POST",
                    data: {
                        action: "login",
                        lgname: "zillyonweb",
                        lgpassword: "xq7J4m3n",
                        lgtoken: token,
                        format: "json"
                    },
                    url: url,
                    dataType: rtype,
                    timeout: 1000,
                    success: function (obj2) {
//                        alert(obj2);
                    }
                });
            }
        });
    }

    function submitIp() {
        urlGetIp = "https://app.pactosolucoes.com.br/ip/v2.php";
        rtype = "text";
        var protocolo = document.location.protocol.toString();
        if (protocolo.indexOf("https", 0) != -1) {
            urlGetIp = "https://app.pactosolucoes.com.br/ip/v2.php";
        }
        jQuery.ajax({
            type: "GET",
            url: urlGetIp,
            dataType: rtype,
            success: function (valor) {
                if (rtype == "jsonp") {
                    enviarIp(valor.ip);
                } else {
                    enviarIp(valor);
                }
            }
        });
    }

    function abrirClienteLink(element) {
        var linkObj = jQuery(element).parent().find('.imagem-aluno-favoritos');
        linkObj.trigger('click');
    }

    function abrirMouseOver(element) {
        var linkObj = jQuery(element).parent().find('.imagem-aluno-favoritos');
        linkObj.trigger('mouseover');
    }

    function abrirMouseOut(element) {
        var linkObj = jQuery(element).parent().find('.imagem-aluno-favoritos');
        linkObj.trigger('mouseout');
    }

    function load() {
        wiki();
        enviarBrowser(navigator.userAgent, screen.availWidth,
            screen.availHeight, document.location.protocol, document.location.origin);
        submitIp();
    }
</script>

<c:if test="${!SuperControle.desv and SuperControle.enableCountDown}">
    <script type="text/javascript" language=JavaScript>
        diferenca = ${diffTime};
        dataFinal = new Date(agora.getTime() + diferenca);
        urlLogoutRedirect = "${urlAfterLogout}";
        startCountdown();
    </script>
</c:if>

<style>

    .modules-menu-mov-icon {
        width: 20px;
        height: 20px;
    }

    .modules-menu-mav-disable {
        opacity: 0.5;
        cursor: initial;
    }

    .rich-mpnl-content {
        border: 1px solid #67757c;
        border-radius: 0.3rem;
    }

    .caixa-pesquisa-notificacoes {
        width: 100%;
        border-radius: 3px;
        border: 1px solid #bcbfc7;
        padding: 0px 30px 0px 10px;
        line-height: 30px;
        color: #92959b;
        outline: 0px !important;
        font-family: 'Nunito Sans', sans-serif;
        font-weight: 400;
        font-size: 14px;
        overflow: visible;
        background-color: #000a28;
    (rgb(255, 255, 255), rgb(59, 59, 59));
        margin-bottom: 12px;
        margin-left: 14px;
    }

    .leia-mais-novidade-panel {
        display: none;
    }

    .div-alunos-favoritos {
        font-family: 'Nunito Sans';
        font-style: normal;
        font-weight: 400;
        font-size: 16px;
        line-height: 125%;
        color: #51555A;
        margin-top: 1em;
        margin-bottom: 5px;
        margin-left: -1.5em;
    }

    .input-lembrete-aluno {
        margin-right: -0.6em;
        resize: none;
        height: 7em;
        border: 0;
        font-family: 'Nunito Sans';
        font-style: normal;
        font-weight: 400;
        font-size: 14px;
        line-height: 125%;
        display: flex;
        align-items: center;
        color: #90949A;
        background-image: none;
        border: none;
        overflow: auto;
        outline: auto;

        -webkit-box-shadow: none;
        -moz-box-shadow: none;
        box-shadow: none;

        resize: none;
    }


    .form-search-zw input {
        width: 100%;
        border: 0;
        color: #a1a5aa;
        border-bottom: 1px solid #c7c9cc;
        font-family: 'Nunito Sans', sans-serif;
        font-weight: 400;
        font-size: 14px;
        background-image: none;
    }

    .form-search-zw .pct-search {
        position: absolute;
        left: 0;
        top: 6px;
        color: #a1a5aa;
        font-size: 14px;
    }

    .titulo-notificacoes {
        background: #0380E3;
        padding: 1em;
        font-family: 'Nunito Sans';
        color: white;
        font-size: 16px;
        font-weight: 700;
        margin-left: -16px;
        margin-right: -16px;
        border-radius: 0.8em 0.8em 0 0;
    }

    .matriculaAlunoFavorito {
        border-radius: 50%;
        width: 2.5em;
        margin-left: 12.5em;
        margin-top: -2.2em;
    }

    .modules-menu-icon {
        opacity: 0.5;
    }

    .modules-menu-icon:hover, .modules-menu-icon-selected {
        opacity: 1;
    }

    .blue-container-assinatura, .blue-container-usuario {
        width: 100%;
        height: 40px;
        background-color: #0380E3;
        color: #FFFFFF;
        margin-top: -4px;
        display: -ms-flexbox;
        display: flex;
        -ms-flex-align: start;
        align-items: flex-start;
        -ms-flex-pack: justify;
        justify-content: space-between;
        border-bottom: 1px solid #0380E3;
        border-top-left-radius: 0.5rem;
        border-top-right-radius: 0.5rem;
    }

    .class-span {
        font-size: 22px;
        padding-right: 10px;
        font-weight: lighter;
        font-family: Times, Times New Roman, serif;
    }

    .close {
        font-weight: lighter;
        text-shadow: none;
        padding-top: 8px;
    }

    button.close {
        color: #FFF;
        opacity: 1;
        padding-top: 10px;
        font-weight: lighter;
    }

    .modal-title {
        font-size: 16px;
        font-weight: bold;
        padding-top: 0.8rem;
        padding-left: 1.0rem;
    }

    .modal-conteudo {
        width: 260px;
        height: 183px;
        left: 542px;
    }

    .class-hr {
        border: 0;
        height: 1px;
        background: #f3f3f3;
        width: 258px;
        margin-left: 16px;
        margin-top: 0px;
        margin-bottom: 12px;
    }

    .container .option .class-span {
        color: #51555A;
        font-family: 'Nunito Sans';
        font-style: normal;
        font-weight: 300;
        font-size: 16px;
        line-height: 125%;
        padding-left: 0px;
    }

    .container-alunos-favoritos {
        border-radius: 1em !important;
    }

    .icon-module i{
        font-size: 20px;
    }
    .bottom-botton-settings{

       padding-top: 5px;
    }


    i.pct.pct-chevron-left.ng-star-inserted{
        margin-right: 5px;
        width: 16px;
        height: 20px;
    }

    .zw_ui_menu-toggle{
        margin-top: 0px;
        width: 24px;
        height: 25.33px;
    }

    .inner-circle{
        width: 32px;
        height: 32px;
    }
    .modules-menu-page{
        height: 225px;
        padding-top: 4px;
    }
</style>
