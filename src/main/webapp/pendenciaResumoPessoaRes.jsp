<%--
    Document   : pendenciaResumoPessoaRes
    Author     : <PERSON><PERSON><PERSON>
--%>
<%@page contentType="text/html"%>
<%@page pageEncoding="ISO-8859-1"%>
<head><%@include file="/includes/include_import_minifiles.jsp" %></head>
<script type="text/javascript" language="javascript" src="../hoverform.js"></script>
<link href="./css/otimize.css" rel="stylesheet" type="text/css">
<link href="./beta/css/font-awesome_2.0.min.css" rel="stylesheet" type="text/css">
<link href="./beta/css/pure-min.css" type="text/css" rel="stylesheet"/>
<%@taglib prefix="f" uri="http://java.sun.com/jsf/core"%>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html"%>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions"%>

<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax"%>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich"%>

<f:loadBundle var="msg_bt" basename="propriedades.Botoes"/>
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao"/>
<f:loadBundle var="msg" basename="propriedades.Mensagens"/>
<style>
    th[id*='col_nomeheader'], th[id*='col_situacaoCliente']{
        text-align: left;
    }
    .rich-table-subheadercell >  div{
        text-align: center;
    }
</style>
<f:view>
    <jsp:include page="includes/include_carregando_ripple.jsp" flush="true"/>
    <title>
        <h:outputText value="Resumo de Cliente(s) \"#{PendenciaControleRel.pendenciaRelVO.tipo.descricao}\""/>
    </title>
    <h:panelGrid columns="1" width="100%" cellpadding="0" cellspacing="0">
        <html>
            <body onload="fireElement('form:botaoAtualizarPagina')"/>
            <h:form id="form" styleClass="pure-form pure-u-1">
                <c:set var="titulo" scope="session" value="${PendenciaControleRel.pendenciaRelVO.tipo.descricao}"/>
                <c:set var="urlWiki" scope="session" value="${SuperControle.urlBaseConhecimento}${PendenciaControleRel.pendenciaRelVO.tipo.urlLinkWiki}"/>
                <h:panelGroup layout="block" styleClass="pure-g-r">
                    <f:facet name="header">
                        <jsp:include page="topo_reduzido_popUp.jsp"/>
                    </f:facet>
                </h:panelGroup>
                <a4j:commandButton id="botaoAtualizarPagina" reRender="form" style="display:none"/>
                <h:panelGroup layout="block" style="display: inline-table;width: 100%;height: 100vh" styleClass="fundoBranco">

                    <h:panelGroup layout="block" styleClass="caixaCorpo">
                        <h:panelGroup layout="block" style="height: 80%;width: 100%">
                            <h:panelGroup layout="block" styleClass="caixaMenuLatel">
                                <h:panelGroup layout="block" >
                                    <h:panelGroup layout="block" styleClass="margin-box">
                                        <h:panelGrid width="100%" style="text-align: right">
                                            <h:panelGroup layout="block">
                                                <a4j:commandLink id="exportarExcel"
                                                                   style="margin-left: 8px;"
                                                                   actionListener="#{PendenciaControleRel.exportarPendencia}"
                                                                   rendered="#{not empty PendenciaControleRel.pendenciaRelVO.listaPendenciaResumoPessoaRelVOs}"
                                                                   oncomplete="#{PendenciaControleRel.mensagemNotificar}#{PendenciaControleRel.msgAlert}"
                                                                   accesskey="2" styleClass="linkPadrao">
                                                    <f:attribute name="tipo" value="xls"/>
                                                    <f:attribute name="atributos" value="#{PendenciaControleRel.atributosExportar}"/>
                                                    <f:attribute name="itemExportacao" value="#{PendenciaControleRel.itemExportar}"/>
                                                    <f:attribute name="prefixo" value="ClientesComDebito"/>
                                                    <h:outputText title="Exportar para o formato Excel" styleClass="btn-print-2 excel"/>
                                                </a4j:commandLink>
                                                <%--BOTÃO PDF--%>
                                                <a4j:commandLink id="exportarPdf"
                                                                   style="margin-left: 8px;"
                                                                   actionListener="#{PendenciaControleRel.exportarPendencia}"
                                                                   rendered="#{not empty PendenciaControleRel.pendenciaRelVO.listaPendenciaResumoPessoaRelVOs}"
                                                                   oncomplete="#{PendenciaControleRel.mensagemNotificar}#{PendenciaControleRel.msgAlert}"
                                                                   accesskey="2" styleClass="linkPadrao">
                                                    <f:attribute name="tipo" value="pdf"/>
                                                    <f:attribute name="atributos" value="#{PendenciaControleRel.atributosExportar}"/>
                                                    <f:attribute name="itemExportacao" value="#{PendenciaControleRel.itemExportar}"/>
                                                    <f:attribute name="prefixo" value="ClientesComDebito"/>
                                                    <h:outputText title="Exportar para o formato PDF" styleClass="btn-print-2 pdf"/>
                                                </a4j:commandLink>
                                            </h:panelGroup>
                                        </h:panelGrid>
                                        <h:panelGroup layout="block" style="margin-left: 5px;">
                                            <h:panelGroup layout="block" style="display: inline-block">
                                                <h:outputText styleClass="tituloCampos" rendered="#{PendenciaControleRel.pendenciaRelVO.tpClienteMesmoCartao}" value="Somente alunos ativos:" />
                                            </h:panelGroup>
                                            <h:panelGroup layout="block" style="display: inline-block;vertical-align: top;">
                                                <h:selectBooleanCheckbox rendered="#{PendenciaControleRel.pendenciaRelVO.tpClienteMesmoCartao}" value="#{PendenciaControleRel.somenteClientesAtivos}"  styleClass="tituloFormulario">
                                                    <a4j:support event="onchange" action="#{RelContratosRecorrenciaControle.consultarClientesMesmoCartaoSemLimparNome}" reRender="form"/>
                                                </h:selectBooleanCheckbox>
                                            </h:panelGroup>
                                        </h:panelGroup>
                                        <h:panelGroup layout="block" style="margin-left: 5px; margin-top: 3px;">
                                            <h:panelGroup layout="block" style="display: inline-block">
                                                <h:outputText styleClass="tituloCampos" rendered="#{PendenciaControleRel.BIClientesMesmoCartao}" value="Nome cliente:" />
                                            </h:panelGroup>
                                            <h:panelGroup layout="block" style="display: inline-block;vertical-align: bottom;">
                                                <h:inputText value="#{PendenciaControleRel.nomeCliente}" style="font-size: 13px;padding: .2em .6em;" styleClass="searchbox-input filtroDT" rendered="#{PendenciaControleRel.BIClientesMesmoCartao}">
                                                    <a4j:support event="onkeyup" requestDelay="1000" action="#{RelContratosRecorrenciaControle.consultarClientesMesmoCartaoSemLimparNome}" reRender="form"/>
                                                </h:inputText>
                                            </h:panelGroup>
                                        </h:panelGroup>
                                        <a4j:region ajaxListener="#{PendenciaControleRel.consultaPaginadoOrdenacao}">
                                            <rich:dataTable width="100%" styleClass="tabelaSimplesCustom" id="tabelaRes"
                                                            binding="#{PendenciaControleRel.dataTable}"
                                                            value="#{PendenciaControleRel.pendenciaRelVO.listaPendenciaResumoPessoaRelVOs}"  rows="50" var="resumoPessoa" >
                                                <c:if test="${PendenciaControleRel.BIClientesMesmoCartao}">
                                                    <rich:column sortBy="#{resumoPessoa.nomeEmpresa}" id="col_empresa" styleClass="col-text-align-center" headerClass="col-text-align-center">
                                                        <f:facet name="header">
                                                            <h:outputText styleClass="texto-upper texto-font texto-size-14-real texto-cor-cinza texto-bold" id="listEmpresa" value="Empresa" />
                                                        </f:facet>
                                                        <h:outputText styleClass="texto-font texto-size-14-real texto-cor-cinza" id="valorEmpresa" value="#{resumoPessoa.nomeEmpresa}" />
                                                    </rich:column>
                                                    <rich:column id="col_matriculacli_cmc" styleClass="col-text-align-left" headerClass="col-text-align-left">
                                                        <f:facet name="header">
                                                            <h:outputText styleClass="texto-upper texto-font texto-size-14-real texto-cor-cinza texto-bold"  value="Matrícula" />
                                                        </f:facet>
                                                        <h:outputText styleClass="texto-font texto-size-14-real texto-cor-cinza"  value="#{resumoPessoa.clienteVO.matricula}" />
                                                    </rich:column>
                                                    <rich:column id="col_nome_cmc"  styleClass="col-text-align-left" headerClass="col-text-align-left">
                                                        <f:facet name="header">
                                                            <h:outputText  styleClass="texto-upper texto-font texto-size-14-real texto-cor-cinza texto-bold"  id="listaNome" value="Nome" />
                                                        </f:facet>
                                                        <h:outputText styleClass="texto-font texto-size-14-real texto-cor-cinza"  value="#{resumoPessoa.clienteVO.pessoa.nome}" />
                                                    </rich:column>
                                                    <rich:column id="col_situacaoCliente__cmc" styleClass="col-text-align-left" headerClass="col-text-align-left">
                                                        <f:facet name="header">
                                                            <h:outputText styleClass="texto-upper texto-font texto-size-14-real texto-cor-cinza texto-bold"  value="Situação" />
                                                        </f:facet>
                                                        <h:outputText styleClass="texto-font texto-size-14-real texto-cor-cinza"  value="#{resumoPessoa.clienteVO.situacao_Apresentar}" />
                                                    </rich:column>
                                                    <rich:column id="col_nome_cpf"  styleClass="col-text-align-left" headerClass="col-text-align-left">
                                                        <f:facet name="header">
                                                            <h:outputText  styleClass="texto-upper texto-font texto-size-14-real texto-cor-cinza texto-bold"  id="listaCPF" value="CPF" />
                                                        </f:facet>
                                                        <h:outputText styleClass="texto-font texto-size-14-real texto-cor-cinza"  value="#{resumoPessoa.clienteVO.pessoa.cfp}" />
                                                    </rich:column>
                                                </c:if>
                                                <c:if test="${!PendenciaControleRel.BIClientesMesmoCartao}">
                                                    <rich:column sortBy="#{resumoPessoa.matricula}" id="col_matriculacli" styleClass="col-text-align-left" headerClass="col-text-align-left">
                                                        <f:facet name="header">
                                                            <h:outputText styleClass="texto-upper texto-font texto-size-14-real texto-cor-cinza texto-bold"  value="Matrícula" />
                                                        </f:facet>
                                                        <h:outputText styleClass="texto-font texto-size-14-real texto-cor-cinza"  value="#{resumoPessoa.clienteVO.matricula}" />
                                                    </rich:column>
                                                    <rich:column sortBy="#{resumoPessoa.clienteVO.pessoa.nome}" id="col_nome"  styleClass="col-text-align-left" headerClass="col-text-align-center">
                                                        <f:facet name="header">
                                                            <h:outputText  styleClass="texto-upper texto-font texto-size-14-real texto-cor-cinza texto-bold"  id="listaNome" value="Nome" />
                                                        </f:facet>
                                                        <h:outputText styleClass="texto-font texto-size-14-real texto-cor-cinza"  value="#{resumoPessoa.clienteVO.pessoa.nome}" />
                                                    </rich:column>
                                                    <rich:column sortBy="#{resumoPessoa.clienteVO.situacao_Apresentar}" id="col_situacaoCliente" styleClass="col-text-align-left" headerClass="col-text-align-left">
                                                        <f:facet name="header">
                                                            <h:outputText styleClass="texto-upper texto-font texto-size-14-real texto-cor-cinza texto-bold"  value="Situação" />
                                                        </f:facet>
                                                        <h:outputText styleClass="texto-font texto-size-14-real texto-cor-cinza"  value="#{resumoPessoa.clienteVO.situacao_Apresentar}" />
                                                    </rich:column>
                                                    <rich:column sortBy="#{resumoPessoa.clienteVO.pessoa.cfp}" id="col_cpf"  styleClass="col-text-align-center" headerClass="col-text-align-center">
                                                        <f:facet name="header">
                                                            <h:outputText  styleClass="texto-upper texto-font texto-size-14-real texto-cor-cinza texto-bold"  id="listaCPF" value="CPF" />
                                                        </f:facet>
                                                        <h:outputText styleClass="texto-font texto-size-14-real texto-cor-cinza"  value="#{resumoPessoa.clienteVO.pessoa.cfp}" />
                                                    </rich:column>
                                                </c:if>

                                                <c:if test="${PendenciaControleRel.pendenciaRelVO.exibirDtNascAniversariantes}">
                                                    <rich:column sortBy="#{resumoPessoa.contrato_Apresentar}" id="col_dtNasc"  styleClass="col-text-align-center" headerClass="col-text-align-center">
                                                        <f:facet name="header">
                                                            <h:outputText  styleClass="texto-upper texto-font texto-size-14-real texto-cor-cinza texto-bold"  id="listaDtaNasc" value="Dt.Nascimento" />
                                                        </f:facet>
                                                        <h:outputText styleClass="texto-font texto-size-14-real texto-cor-cinza"  value="#{resumoPessoa.dataNasc_Apresentar}" />
                                                    </rich:column>

                                                </c:if>
                                                <c:if test="${!PendenciaControleRel.pendenciaRelVO.exibirDtNascAniversariantes}">
                                                    <rich:column sortBy="#{resumoPessoa.contrato_Apresentar}" id="col_codContrato"  styleClass="col-text-align-center" headerClass="col-text-align-center">
                                                        <f:facet name="header">
                                                            <h:outputText  styleClass="texto-upper texto-font texto-size-14-real texto-cor-cinza texto-bold"  id="listaContrato" value="Nº CONTRATO" />
                                                        </f:facet>
                                                        <h:outputText styleClass="texto-font texto-size-14-real texto-cor-cinza"  value="#{resumoPessoa.contrato_Apresentar}" />
                                                    </rich:column>
                                                </c:if>

                                                <c:if test="${PendenciaControleRel.pendenciaRelVO.exibirDebitoCC}">
                                                    <rich:column sortBy="#{resumoPessoa.valorCC}" id="col_saldoAtual" styleClass="col-text-align-right">
                                                        <f:facet name="header">
                                                            <h:outputText styleClass="texto-upper texto-font texto-size-14-real texto-cor-cinza texto-bold"  value="Valor Débito" />
                                                        </f:facet>
                                                        <h:outputText styleClass="texto-font texto-size-14-real texto-cor-cinza"  value="#{resumoPessoa.valorCC_Apresentar}" />
                                                    </rich:column>

                                                    <rich:column
                                                                 id="col_dataRegistro"
                                                                 sortBy="#{resumoPessoa.dataRegistro}" styleClass="col-text-align-center">
                                                        <f:facet name="header">
                                                            <h:outputText styleClass="texto-upper texto-font texto-size-14-real texto-cor-cinza texto-bold"  value="Data de Lançamento" />
                                                        </f:facet>
                                                        <h:outputText styleClass="texto-font texto-size-14-real texto-cor-cinza"  value="#{resumoPessoa.dataRegistro_Apresentar}" />
                                                    </rich:column>
                                                </c:if>
                                                <rich:column sortBy="#{resumoPessoa.contratoVO.plano.descricao}" id="col_nomePlano"
                                                             rendered="#{!PendenciaControleRel.pendenciaRelVO.exibirAutorizacaoCobrancaCliente and PendenciaControleRel.pendenciaRelVO.exibirInformacaoContrato}">
                                                    <f:facet name="header">
                                                        <h:outputText styleClass="texto-upper texto-font texto-size-14-real texto-cor-cinza texto-bold"  value="Plano" />
                                                    </f:facet>
                                                    <h:outputText styleClass="texto-font texto-size-14-real texto-cor-cinza"  value="#{resumoPessoa.contratoVO.plano.descricao_Apresentar}" />
                                                </rich:column>
                                                <c:if test="${!PendenciaControleRel.pendenciaRelVO.exibirAutorizacaoCobrancaCliente
                                                    and PendenciaControleRel.pendenciaRelVO.exibirInformacaoContrato}">
                                                <rich:column sortBy="#{resumoPessoa.contratoVO.contratoDuracao.numeroMeses}" id="col_duracaoContrato" style="text-align: center;">
                                                    <f:facet name="header">
                                                        <h:outputText styleClass="texto-upper texto-font texto-size-14-real texto-cor-cinza texto-bold"  value="Duração" />
                                                    </f:facet>
                                                    <h:outputText styleClass="texto-font texto-size-14-real texto-cor-cinza"  value="#{resumoPessoa.contratoVO.contratoDuracao.numeroMeses}" />
                                                </rich:column>
                                                </c:if>
                                                <c:if test="${PendenciaControleRel.pendenciaRelVO.exibirMensagem}">
                                                <rich:column width="450"  id="col_mensagem"
                                                             sortBy="#{resumoPessoa.conteudoMensagem}">
                                                    <f:facet name="header">
                                                        <h:outputText styleClass="texto-upper texto-font texto-size-14-real texto-cor-cinza texto-bold"  value="Mensagem" />
                                                    </f:facet>
                                                    <h:outputText styleClass="texto-font texto-size-14-real texto-cor-cinza"  value="#{resumoPessoa.conteudoMensagem}" />
                                                </rich:column>
                                                </c:if>
                                                <c:if test="${PendenciaControleRel.pendenciaRelVO.exibirAutorizacaoCobrancaCliente}">
                                                <rich:column width="450" id="col_autorizacao"
                                                             sortBy="#{resumoPessoa.autorizacaoCobrancaCliente}" styleClass="col-text-align-center" headerClass="col-text-align-center">
                                                    <f:facet name="header">
                                                        <h:outputText styleClass="texto-upper texto-font texto-size-14-real texto-cor-cinza texto-bold"  value="Cartão de Crédito" />
                                                    </f:facet>
                                                    <h:outputText styleClass="texto-font texto-size-14-real texto-cor-cinza"  value="#{resumoPessoa.autorizacaoCobrancaCliente}" />
                                                </rich:column>
                                                </c:if>

                                                <c:if test="${PendenciaControleRel.pendenciaRelVO.exibirProblemaCartao}">
                                                    <rich:column width="450" id="col_situacaoCartao"
                                                                 sortBy="#{resumoPessoa.problemaCartao}" styleClass="col-text-align-center" headerClass="col-text-align-center">
                                                        <f:facet name="header">
                                                            <h:outputText styleClass="texto-upper texto-font texto-size-14-real texto-cor-cinza texto-bold"  value="Inf. Incompleta" />
                                                        </f:facet>
                                                        <h:outputText styleClass="texto-font texto-size-14-real texto-cor-cinza"  value="#{resumoPessoa.problemaCartao}" />
                                                    </rich:column>
                                                </c:if>

                                                <c:if test="${PendenciaControleRel.pendenciaRelVO.exibirQtdParcelasEmAtraso}">
                                                    <rich:column id="col_qtdParcelas" sortBy="#{resumoPessoa.qtdParcelaEmAtraso}">
                                                        <f:facet name="header">
                                                            <h:outputText styleClass="texto-upper texto-font texto-size-14-real texto-cor-cinza texto-bold"
                                                                          value="Qtd. Atrasadas"/>
                                                        </f:facet>
                                                        <h:outputText styleClass="texto-font texto-size-14-real texto-cor-cinza"  value="#{resumoPessoa.qtdParcelaEmAtraso}" />
                                                    </rich:column>
                                                </c:if>

                                                <c:if test="${PendenciaControleRel.pendenciaRelVO.exibirInformacoesFinanceiras}">
                                                    <rich:column  styleClass="col-text-align-right" headerClass="col-text-align-right"
                                                                 sortBy="#{resumoPessoa.valorEmAberto}">
                                                        <f:facet name="header">
                                                            <h:outputText styleClass="texto-upper texto-font texto-size-14-real texto-cor-cinza texto-bold"  value="Valor" />
                                                        </f:facet>
                                                        <h:outputText styleClass="texto-font texto-size-14-real texto-cor-cinza"  value="#{resumoPessoa.valorEmAberto_Apresentar}" />
                                                    </rich:column>
                                                </c:if>
                                                <c:if test="${PendenciaControleRel.pendenciaRelVO.exibirInformacoesPessoa}">
                                                <rich:column width="100" id="col_telefone" styleClass="col-text-align-left" headerClass="col-text-align-left">
                                                    <f:facet name="header">
                                                        <h:outputText styleClass="texto-upper texto-font texto-size-14-real texto-cor-cinza texto-bold"  value="Telefone" />
                                                    </f:facet>
                                                    <h:outputText styleClass="texto-font texto-size-14-real texto-cor-cinza"  value="#{resumoPessoa.telefone_Apresentar}" />
                                                </rich:column>
                                                </c:if>
                                                <rich:column   rendered="#{PendenciaControleRel.pendenciaRelVO.exibirInformacoesPessoa}" styleClass="col-text-align-center" headerClass="col-text-align-center">
                                                    <f:facet name="header">
                                                        <h:outputText styleClass="texto-upper texto-font texto-size-14-real texto-cor-cinza texto-bold"  value="Opções" />
                                                    </f:facet>
                                                    <a4j:commandLink id="visualizarCliente" styleClass="linkPadrao texto-cor-azul texto-size-16-real"
                                                                     action="#{PendenciaControleRel.irParaTelaCliente}"
                                                                     oncomplete="abrirPopup('clienteNav.jsp?page=viewCliente&teste=true&blalbala=false', 'Cliente', 1024, 700);">
                                                        <i class="fa-icon-search"></i>
                                                        <f:param name="state" value="AC"/>
                                                    </a4j:commandLink>
                                                </rich:column>

                                            </rich:dataTable>
                                        </a4j:region>
                                        <h:panelGrid columns="1" width="100%" columnClasses="colunaCentralizada" id="paginacao">
                                            <table width="100%" border="0" cellspacing="0" cellpadding="0">
                                                <tr>
                                                    <td align="center" valign="middle">
                                                        <h:panelGroup id="painelPaginacao" rendered="#{PendenciaControleRel.confPaginacao.existePaginacao}" styleClass="container-botoes">
                                                            <a4j:commandLink id="pagiInicial" styleClass="linkPadrao texto-cor-azul texto-size-20-real"  reRender="tabelaRes, paginaAtual, totalItens"
                                                                             action="#{PendenciaControleRel.primeiraPagina}">
                                                                <i class="fa-icon-double-angle-left"></i>
                                                            </a4j:commandLink>

                                                            <a4j:commandLink id="pagiAnterior" styleClass="linkPadrao texto-cor-azul texto-size-20-real" reRender="tabelaRes, paginaAtual, totalItens"
                                                                             action="#{PendenciaControleRel.paginaAnterior}">
                                                                <i class="fa-icon-angle-left"></i>
                                                            </a4j:commandLink>

                                                            <h:outputText id="paginaAtual" styleClass="linkPadrao texto-font texto-cor-cinza texto-size-16-real"
                                                                          value="#{msg_aplic.prt_msg_pagina} #{PendenciaControleRel.confPaginacao.paginaAtualDeTodas}" rendered="true"/>
                                                            <a4j:commandLink id="pagiPosterior" styleClass="linkPadrao texto-font texto-cor-azul texto-size-20-real" reRender="tabelaRes, paginaAtual, totalItens"
                                                                             action="#{PendenciaControleRel.proximaPagina}">
                                                                <i class="fa-icon-angle-right"></i>
                                                            </a4j:commandLink>

                                                            <a4j:commandLink id="pagiFinal" styleClass="linkPadrao texto-cor-azul texto-size-20-real" reRender="tabelaRes, paginaAtual, totalItens"
                                                                             action="#{PendenciaControleRel.ultimaPagina}">
                                                                <i class="fa-icon-double-angle-right"></i>
                                                            </a4j:commandLink>

                                                                <h:outputText id="totalItens" styleClass="texto-cor-azul texto-cor-cinza texto-font texto-size-16-real" style="display: block; text-align: left; margin-top: -53px;"
                                                                          value=" Total: #{PendenciaControleRel.confPaginacao.numeroTotalItens} itens" rendered="true"/>
                                                        </h:panelGroup>
                                                    </td>
                                                </tr>
                                            </table>
                                        </h:panelGrid>
                                    </h:panelGroup>
                                </h:panelGroup>
                            </h:panelGroup>

                        </h:panelGroup>
                    </h:panelGroup>
                </h:panelGroup>
            </h:form>
        </body>
    </html>
</h:panelGrid>
</f:view>

