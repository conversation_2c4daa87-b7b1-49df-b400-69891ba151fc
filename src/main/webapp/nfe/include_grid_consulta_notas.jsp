<%@page contentType="text/html" pageEncoding="ISO-8859-1" %>
<%@include file="../includes/imports.jsp" %>

<table width="100%" cellpadding="0" cellspacing="0">
    <tr>
        <td width="19" height="50" align="left" valign="top" background="../images/box_centro_top_left.gif"></td>
        <td align="left" valign="top" background="../images/box_centro_top.gif" class="tituloboxcentro"
            style="padding:11px 0 0 0;">
            Gerenciamento de Notas
            <%--<h:outputLink styleClass="linkWiki" value="#{SuperControle.urlWikiGST}Agendas:Individual"--%>
            <%--title="Gerenciamento de Notas" target="_blank">--%>
            <%--<i class="fa-icon-question-sign" style="font-size: 18px"></i>--%>
            <%--</h:outputLink>--%>
        </td>
        <td class="tituloboxcentro2" valign="middle">
        </td>
        <td width="19" align="left" valign="top" background="../images/box_centro_top_right.gif"></td>
    </tr>

    <tr>
        <td align="left" valign="top" background="../images/box_centro_left.gif"></td>
        <td colspan="2" align="left" valign="top" bgcolor="#ffffff" style="padding:5px 5px 5px 5px;">
            <div>
                <jsp:include page="include_consulta_notas.jsp"/>
            </div>

            <h:panelGrid columns="1" id="mensagemNFeErro">
                <h:outputText styleClass="mensagem" value="#{NotaFiscalDeServicoControle.mensagem}"/>
                <h:outputText styleClass="mensagemDetalhada" value="#{NotaFiscalDeServicoControle.mensagemDetalhada}"/>
            </h:panelGrid>

            <h:panelGroup layout="block" id="pnlAcoes" style="float: right">
                <%--rendered="#{NotaFiscalDeServicoControle.filtro.tipoNota == 0}"--%>

                <a4j:commandButton value="Alterar Sequência RPS" reRender="mensagemNFeErro,modalSequenciaRPS"
                                   styleClass="botoes nvoBt btSec"
                                   id="permiteAlterarNumeroRPS"
                                   rendered="#{(LoginControle.usuarioLogado.administrador || LoginControle.perfilNFe.permiteAlterarNumeroRPS) && NotaFiscalDeServicoControle.filtro.tipoNota == 0}"
                                   oncomplete="#{NotaFiscalDeServicoControle.onComplete}"
                                   action="#{NotaFiscalDeServicoControle.preparaAlterarSequenciaRPS}"/>

                <a4j:commandButton value="Cancelar Notas" reRender="mensagemNFeErro,modalCancelarVariasNotas"
                                   styleClass="botoes nvoBt"
                                   rendered="#{(LoginControle.usuarioLogado.administrador || LoginControle.perfilNFe.permiteCancelarNota) && NotaFiscalDeServicoControle.filtro.tipoNota == 0}"
                                   style="background: darkred;"
                                   oncomplete="#{NotaFiscalDeServicoControle.onComplete}"
                                   action="#{NotaFiscalDeServicoControle.preparaCancelarVariasNotas}"/>

                <a4j:commandButton value="Inutilizar NFC-e" reRender="mensagemNFeErro, modalInutilizarNFCE"
                                   styleClass="botoes nvoBt"
                                   id="preparaInutilizarNFCE"
                                   rendered="#{NotaFiscalDeServicoControle.filtro.tipoNota == 1}"
                                   style="background: darkred;"
                                   oncomplete="#{NotaFiscalDeServicoControle.onComplete}"
                                   action="#{NotaFiscalDeServicoControle.preparaInutilizarNFCE}"/>

                <a4j:commandButton value="Inutilizar NFe" reRender="mensagemNFeErro, modalInutilizarNFE"
                                   styleClass="botoes nvoBt"
                                   id="preparaInutilizarNFE"
                                   rendered="#{NotaFiscalDeServicoControle.filtro.tipoNota == 0 && NotaFiscalDeServicoControle.permiteInutilizarNFe}"
                                   style="background: darkred;"
                                   oncomplete="#{NotaFiscalDeServicoControle.onComplete}"
                                   action="#{NotaFiscalDeServicoControle.preparaInutilizarNFE}"/>

                <a4j:commandButton value="Excluir Notas" reRender="mensagemNFeErro, modalExcluirNotas"
                                   rendered="#{LoginControle.usuarioLogado.administrador && NotaFiscalDeServicoControle.filtro.tipoNota == 0}"
                                   styleClass="botoes nvoBt btSec"
                                   action="#{NotaFiscalDeServicoControle.preparaParaExcluirNotas}"
                                   oncomplete="#{rich:component('modalExcluirNotas')}.show();"/>

                <a4j:commandButton value="Desvincular Notas" reRender="mensagemNFeErro, modalDesvincularNotas"
                                   rendered="#{LoginControle.usuarioLogado.administrador && NotaFiscalDeServicoControle.filtro.tipoNota == 0}"
                                   styleClass="botoes nvoBt btSec"
                                   action="#{NotaFiscalDeServicoControle.preparaParaDesvincularNotas}"
                                   oncomplete="#{rich:component('modalDesvincularNotas')}.show();"/>

                <a4j:commandButton value="Gerar PDFs" reRender="mensagemNFeErro"
                                   id="GerarPDF"
                                   styleClass="botoes nvoBt"
                                   action="#{NotaFiscalDeServicoControle.downloadPDFArquivosMarcados}"
                                   oncomplete="#{NotaFiscalDeServicoControle.onComplete}"/>

                <a4j:commandButton value="Gerar XMLs" reRender="mensagemNFeErro"
                                   styleClass="botoes nvoBt"
                                   actionListener="#{NotaFiscalDeServicoControle.downloadXMLArquivosMarcados}"
                                   oncomplete="abrirPopup('UpdateServlet?op=downloadfile&file=#{NotaFiscalDeServicoControle.fileName}&mimetype=application/zip','Notas', 800,200);#{NotaFiscalDeServicoControle.msgAlert}"/>

                <a4j:commandButton value="Gerar XMLs - NFC-e Inutilizada"
                                   rendered="#{NotaFiscalDeServicoControle.filtro.tipoNota == 1}"
                                   reRender="mensagemNFeErro"
                                   styleClass="botoes nvoBt"
                                   action="#{NotaFiscalDeServicoControle.downloadXMLInutilizadaNFCeArquivosMarcados}"
                                   oncomplete="abrirPopup('UpdateServlet?op=downloadfile&file=#{NotaFiscalDeServicoControle.fileName}&mimetype=application/zip','Notas', 800,200);#{NotaFiscalDeServicoControle.msgAlert}"/>

                <a4j:commandButton id="exportarExcelNFSe"
                                   styleClass="botoes nvoBt"
                                   title="Exportar excel"
                                   actionListener="#{NotaFiscalDeServicoControle.exportarExcel}"
                                   oncomplete="abrirPopup('UpdateServlet?op=downloadfile&file=#{ExportadorListaControle.fileName}&mimetype=application/vnd.ms-excel','Transacoes', 800,200);#{ExportadorListaControle.msgAlert}"
                                   accesskey="3"
                                   value="Exportar excel">

                    <f:attribute name="tipo" value="xls"/>
                    <f:attribute name="atributos" value="#{NotaFiscalDeServicoControle.atributosExportar}"/>
                    <f:attribute name="prefixo" value="Notas"/>
                </a4j:commandButton>
                
            </h:panelGroup>
        </td>
        <td align="left" valign="top" background="../images/box_centro_right.gif">
            <img src="../images/shim.gif" alt="shim">
        </td>
    </tr>

    <tr>
        <td height="20" width="19" align="left" valign="top" background="../images/box_centro_bottom_left.gif"></td>
        <td colspan="2" align="left" valign="top" background="../images/box_centro_bottom.gif">
            <img src="../images/shim.gif" alt="shim">
        </td>
        <td align="left" width="19" height="20" valign="top" background="../images/box_centro_bottom_right.gif"></td>
    </tr>
</table>
