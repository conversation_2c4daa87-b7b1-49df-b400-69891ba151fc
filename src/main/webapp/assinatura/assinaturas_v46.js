var assinando = false;
var imageAssinatura;
var imageAssinatura2;
var imageDocs;
var imageEndereco;
var imageAtestado;
var imageAnexo1;
var imageAnexo2;
var codigoContrato;
var assinaturaCancelamento;
var codigoMatricula
var codigoAluno;
var codigoAcesso;
var nomeAluno;
var codigoModelo;
var texto;
var empresa = null;
var nomeAluno;
var fotoAlterada;
var imageFotoAluno;
var acao = 'contrato';
var contratoSelecionado_item;
var updateAssinatura;
var updateDoc;
var urlPdfParq;
var updateEnd;
var updateAte;
var updateAnexo1;
var updateAnexo2;
var updateCartao;
var urlSocket = 'https://wbskt.pactosolucoes.com.br/';
var isPlanoPersonal;
var imageAnexo1Cartao;
var updateAnexo1Cartao;
var tipoCartaoVacinaSelecionado;
var reenviarAssinatura = false;
var permitirRemoverAssinatura = false;
var permitirApresentarAssinatura2 = false;
var responsavelFinanceiroPreenchido = false;
// var urlSocket = 'http://localhost:8084/';
var perguntasParQ;
var respostasClienteParQ;
var matriculaAlunoSelecionado;
var codigoRespostaParqAlunoSelecionado;
var boolUtilizaTermoResponsabilidade;
var boolTermoResponsabilidadeEstaAtivo;
var boolConfigSesc;
var delay = (function() {
    var timer = 0;
    return function(callback, ms) {
        clearTimeout(timer);
        timer = setTimeout(callback, ms);
    };
})();
var ip;
jQuery.ajax({
    type: "GET",
    url: 'https://app.pactosolucoes.com.br/ip/v2.php',
    dataType: 'text',
    async: false,
    success: function (valor) {
        ip = valor;
    }
});
function params() {
    var params = {};

    if (location.search) {
        var parts = location.search.substring(1).split('&');

        for (var i = 0; i < parts.length; i++) {
            var nv = parts[i].split('=');
            if (!nv[0])
                continue;
            params[nv[0]] = nv[1] || true;
        }
    }
    return params;
}

var signaturePad,signaturePad2,
    editing;

function construirCanvas() {
    var wrapper = document.getElementById("signature-pad"),
        clearButton = wrapper.querySelector("[data-action=clear]"),
        savePNGButton = wrapper.querySelector("[data-action=save-png]"),
        saveSVGButton = wrapper.querySelector("[data-action=save-svg]"),
        canvas = wrapper.querySelector("canvas");

    function ajustarCanvas() {
        var ratio = Math.max(window.devicePixelRatio || 1, 1);
        var assinaturaAnterior = signaturePad.toDataURL();
        canvas.width = wrapper.clientWidth * ratio;
        canvas.height = wrapper.clientHeight * ratio;
        canvas.getContext("2d").scale(ratio, ratio);
        if (assinaturaAnterior) {
            signaturePad.fromDataURL(assinaturaAnterior);
        }
    }

    ajustarCanvas();

    signaturePad = new SignaturePad(canvas, {
        backgroundColor: 'rgba(255, 255, 255, 0)',
        penColor: 'rgb(0, 0, 0)'
    });

    clearButton.addEventListener("click", function(event) {
        signaturePad.clear();
    });

    savePNGButton.addEventListener("click", function(event) {
        if (signaturePad.isEmpty()) {
            alert("Please provide signature first.");
        } else {
            window.open(signaturePad.toDataURL());
        }
    });

    saveSVGButton.addEventListener("click", function(event) {
        if (signaturePad.isEmpty()) {
            alert("Please provide signature first.");
        } else {
            window.open(signaturePad.toDataURL('image/svg+xml'));
        }
    });
    window.addEventListener("resize", ajustarCanvas);
}

function construirCanvas2() {

    if (permitirApresentarAssinatura2 && responsavelFinanceiroPreenchido) {
        var wrapper = document.getElementById("signature-pad2"),
            clearButton = wrapper.querySelector("[data-action=clear]"),
            savePNGButton = wrapper.querySelector("[data-action=save-png]"),
            saveSVGButton = wrapper.querySelector("[data-action=save-svg]"),
            canvas = wrapper.querySelector("canvas");

        function ajustarCanvas() {
            var ratio = Math.max(window.devicePixelRatio || 1, 1);
            if (signaturePad2 == undefined) {
                signaturePad2 = new SignaturePad(canvas, {
                    backgroundColor: 'rgba(255, 255, 255, 0)',
                    penColor: 'rgb(0, 0, 0)'
                });
            }
            var assinaturaAnterior = signaturePad2.toDataURL();
            canvas.width = wrapper.clientWidth * ratio;  // Corrigido para usar clientWidth
            canvas.height = wrapper.clientHeight * ratio;
            canvas.getContext("2d").scale(ratio, ratio);
            if (assinaturaAnterior) {
                signaturePad2.fromDataURL(assinaturaAnterior);
            }
        }

        ajustarCanvas();

        if (signaturePad2 == undefined) {
            signaturePad2 = new SignaturePad(canvas, {
                backgroundColor: 'rgba(255, 255, 255, 0)',
                penColor: 'rgb(0, 0, 0)'
            });
        }

        clearButton.addEventListener("click", function(event) {
            signaturePad2.clear();
        });

        savePNGButton.addEventListener("click", function(event) {
            if (signaturePad2.isEmpty()) {
                alert("Please provide signature first.");
            } else {
                window.open(signaturePad2.toDataURL());
            }
        });

        saveSVGButton.addEventListener("click", function(event) {
            if (signaturePad2.isEmpty()) {
                alert("Please provide signature first.");
            } else {
                window.open(signaturePad2.toDataURL('image/svg+xml'));
            }
        });
        window.addEventListener("resize", ajustarCanvas);
    }
}

function construirCanvasTermoResponsabilidade() {

    var wrapper = document.getElementById("signature-pad-termoresponsabilidade"),
        clearButton = wrapper.querySelector("[data-action=clear]"),
        savePNGButton = wrapper.querySelector("[data-action=save-png]"),
        saveSVGButton = wrapper.querySelector("[data-action=save-svg]"),
        canvas = wrapper.querySelector("canvas");

    function ajustarCanvas() {
        var ratio = Math.max(window.devicePixelRatio || 1, 1);
        canvas.width = wrapper.clientWidth * ratio;  // Corrigido para usar clientWidth
        canvas.height = wrapper.clientHeight * ratio;
        canvas.getContext("2d").scale(ratio, ratio);
    }

    ajustarCanvas();

    signaturePad = new SignaturePad(canvas, {
        backgroundColor: 'rgba(255, 255, 255, 0)',
        penColor: 'rgb(0, 0, 0)'
    });

    clearButton.addEventListener("click", function(event) {
        signaturePad.clear();
    });

    savePNGButton.addEventListener("click", function(event) {
        if (signaturePad.isEmpty()) {
            alert("Please provide signature first.");
        } else {
            window.open(signaturePad.toDataURL());
        }
    });

    saveSVGButton.addEventListener("click", function(event) {
        if (signaturePad.isEmpty()) {
            alert("Please provide signature first.");
        } else {
            window.open(signaturePad.toDataURL('image/svg+xml'));
        }
    });
}

var signaturePad;

function ajustarCanvasAssinaturaParQ() {
    var wrapper = document.getElementById("signature-pad-parq"),
        canvas = wrapper.querySelector("canvas");

    function ajustarCanvas() {
        var ratio = Math.max(window.devicePixelRatio || 1, 1);
        canvas.width = wrapper.clientWidth * ratio;  // Corrigido para usar clientWidth
        canvas.height = wrapper.clientHeight * ratio;
        canvas.getContext("2d").scale(ratio, ratio);
    }

    ajustarCanvas();

    if (signaturePad) {
        var data = signaturePad.toDataURL();
        signaturePad.clear();
        signaturePad.fromDataURL(data);
    } else {
        signaturePad = new SignaturePad(canvas);
    }
    signaturePad.clear();
}

function construirCanvasParQ() {
    var wrapper = document.getElementById("signature-pad-parq"),
        clearButton = wrapper.querySelector("[data-action=clear]"),
        savePNGButton = wrapper.querySelector("[data-action=save-png]"),
        saveSVGButton = wrapper.querySelector("[data-action=save-svg]"),
        canvas = wrapper.querySelector("canvas");

    ajustarCanvasAssinaturaParQ();

    clearButton.addEventListener("click", function(event) {
        signaturePad.clear();
    });

    savePNGButton.addEventListener("click", function(event) {
        if (signaturePad.isEmpty()) {
            alert("Please provide signature first.");
        } else {
            window.open(signaturePad.toDataURL());
        }
    });

    saveSVGButton.addEventListener("click", function(event) {
        if (signaturePad.isEmpty()) {
            alert("Please provide signature first.");
        } else {
            window.open(signaturePad.toDataURL('image/svg+xml'));
        }
    });

    window.addEventListener('resize', ajustarCanvasAssinaturaParQ);
}

// Inicializar o canvas quando o documento estiver carregado
document.addEventListener("DOMContentLoaded", construirCanvasParQ);


var params = params();

function montarContratos() {
    $("#main").empty();
    if (isPlanoPersonal === true){
        var caixa = '<div onclick="retrair(this,  \'.caixanaoassinados \');" class="caixa retrair" style="line-height: 10vh;"><span class="lblTitulo"><i class="fa-icon-chevron-right"></i><i class="fa-icon-chevron-down"></i>Planos n�o assinados </span>' +
            '<span class="lblTitulo totalNaoAssinados" style="float: right"></span>' +
            '</div><div class="caixanaoassinados"></div>' +
            '<div class="caixa retrair" style="line-height: 10vh;" onclick="retrair(this, \'.caixaassinados\');">' +
            '<span class="lblTitulo"><i class="fa-icon-chevron-right"></i><i class="fa-icon-chevron-down"></i> Planos assinados </span>' +
            '<span class="lblTitulo totalAssinados" style="float: right"></span>' +
            '</div><div class="caixaassinados"></div>';
        $("#main").append(caixa);
    } else {
        var caixa = '<div onclick="retrair(this,  \'.caixanaoassinados \');" class="caixa retrair" style="line-height: 10vh;"><span class="lblTitulo"><i class="fa-icon-chevron-right"></i><i class="fa-icon-chevron-down"></i>Contratos n�o assinados </span>' +
            '<span class="lblTitulo totalNaoAssinados" style="float: right"></span>' +
            '</div><div class="caixanaoassinados"></div>' +
            '<div class="caixa retrair" style="line-height: 10vh;" onclick="retrair(this, \'.caixaassinados\');">' +
            '<span class="lblTitulo"><i class="fa-icon-chevron-right"></i><i class="fa-icon-chevron-down"></i> Contratos assinados </span>' +
            '<span class="lblTitulo totalAssinados" style="float: right"></span>' +
            '</div><div class="caixaassinados"></div>';
        $("#main").append(caixa);
    }
}

function montarContratoProdutos() {
    $("#main").empty();
    var caixa = '<div onclick="retrair(this,  \'.caixanaoassinados \');" class="caixa retrair" style="line-height: 10vh;"><span class="lblTitulo"><i class="fa-icon-chevron-right"></i><i class="fa-icon-chevron-down"></i>Contratos de produtos n�o assinados </span>' +
        '<span class="lblTitulo totalNaoAssinados" style="float: right"></span>' +
        '</div><div class="caixanaoassinados"></div>' +
        '<div class="caixa retrair" style="line-height: 10vh;" onclick="retrair(this, \'.caixaassinados\');">' +
        '<span class="lblTitulo"><i class="fa-icon-chevron-right"></i><i class="fa-icon-chevron-down"></i> Contratos de produtos assinados </span>' +
        '<span class="lblTitulo totalAssinados" style="float: right"></span>' +
        '</div><div class="caixaassinados"></div>';
    $("#main").append(caixa);
}

function montarHtmlParQ() {
    $("#main").empty();
    var caixa =
        '<div onclick="retrair(this,  \'.caixanaoassinados \');" class="caixa retrair" style="line-height: 10vh;">' +
        '<span class="lblTitulo">' +
        '<i class="fa-icon-chevron-right"></i>' +
        '<i class="fa-icon-chevron-down"></i>' +
        'Clientes com Par-q n�o assinado' +
        '</span>' +
        '<span class="lblTitulo totalNaoAssinados" style="float: right"></span>' +
        '</div>' +
        '<div class="caixanaoassinados"></div>' +

        '<div onclick="retrair(this,  \'.caixaparqpositivos \');" class="caixa retrair" style="line-height: 10vh;">' +
        '<span class="lblTitulo">' +
        '<i class="fa-icon-chevron-right"></i>' +
        '<i class="fa-icon-chevron-down"></i>' +
        'Clientes com Par-q Positivo ' +
        '</span>' +
        '<span class="lblTitulo totalParQPositivos" style="float: right"></span>' +
        '</div>' +
        '<div class="caixaparqpositivos"></div>' +

        '<div onclick="retrair(this, \'.caixaassinados\');" class="caixa retrair" style="line-height: 10vh;">' +
        '<span class="lblTitulo">' +
        '<i class="fa-icon-chevron-right"></i>' +
        '<i class="fa-icon-chevron-down"></i> ' +
        'Clientes com Par-q assinado ' +
        '</span>' +
        '<span class="lblTitulo totalAssinados" style="float: right"></span>' +
        '</div>' +
        '<div class="caixaassinados"></div>' +

        '<div  onclick="retrair(this, \'.caixaassinadosvencidos\');" class="caixa retrair" style="line-height: 10vh;display: none" id="assinadosvencidosparq">' +
        '<span class="lblTitulo">' +
        '<i class="fa-icon-chevron-right"></i>' +
        '<i class="fa-icon-chevron-down"></i> ' +
        'Clientes com Par-q vencido ' +
        '</span>' +
        '<span class="lblTitulo totalAssinadosVencidos" style="float: right"></span>' +
        '</div>' +
        '<div class="caixaassinadosvencidos"></div>';
    $("#main").append(caixa);
}

function montarTermoResponsabilidade() {
    $("#main").empty();
    var caixa = '<div onclick="retrair(this,  \'.caixanaoassinados \');" class="caixa retrair" style="line-height: 10vh;"><span class="lblTitulo"><i class="fa-icon-chevron-right"></i><i class="fa-icon-chevron-down"></i>Termo Responsabilidade n�o assinado </span>' +
        '<span class="lblTitulo totalNaoAssinados" style="float: right"></span>' +
        '</div><div class="caixanaoassinados"></div>' +
        '<div class="caixa retrair" style="line-height: 10vh;" onclick="retrair(this, \'.caixaassinados\');">' +
        '<span class="lblTitulo"><i class="fa-icon-chevron-right"></i><i class="fa-icon-chevron-down"></i> Termo de Responsabilidade assinado  </span>' +
        '<span class="lblTitulo totalAssinados" style="float: right"></span>' +
        '</div><div class="caixaassinados"></div>';
    $("#main").html(caixa);
}

function montarCartoesVacina() {
    $("#main").empty();
    var caixa = '<div onclick="retrair(this,  \'.caixanaocadastrados \');" class="caixa retrair" style="line-height: 10vh;"><span class="lblTitulo"><i class="fa-icon-chevron-right"></i><i class="fa-icon-chevron-down"></i>Cart�es n�o cadastrados </span>' +
        '<span class="lblTitulo totalNaoCadastrados" style="float: right"></span>' +
        '</div><div class="caixanaocadastrados"></div>' +
        '<div class="caixa retrair" style="line-height: 10vh;" onclick="retrair(this, \'.caixacadastrados\');">' +
        '<span class="lblTitulo"><i class="fa-icon-chevron-right"></i><i class="fa-icon-chevron-down"></i> Cart�es Cadastrados </span>' +
        '<span class="lblTitulo totalCadastrados" style="float: right"></span>' +
        '</div><div class="caixacadastrados"></div>';
    $("#main").append(caixa);
}

function retrair(caixa, child) {
    $(caixa).toggleClass('retraido');
    $(child).toggleClass('retraido');
}

function montarTipAtestados() {
    $("#main").empty();
    var caixa = '<div class="caixa atestado" style="line-height: 10vh;">PARA LAN�AR O ATESTADO, PESQUISE O ALUNO POR NOME OU MATR�CULA</div>';
    $("#main").append(caixa);
}

function assinarUmContrato() {
    if (editing === true) {
        endImageEditing();
    }

    acao = 'contrato';
    isPlanoPersonal = false;
    $('body').removeClass();
    montarContratos();
    desabilitarPesquisa();
    closeMenu();
}

function assinarUmContratoProduto() {
    if (editing === true) {
        endImageEditing();
    }

    acao = 'contratoProduto';
    $('body').removeClass();
    montarContratoProdutos();
    desabilitarPesquisa();
    closeMenu();
}

function assinarUmContratoCancelado() {
    if (editing === true) {
        endImageEditing();
    }

    acao = 'contratoCancelado';
    $('body').removeClass();
    montarContratosCancelados();
    desabilitarPesquisa();
    closeMenu();
}

function montarContratosCancelados() {
    $("#main").empty();
    var caixa = '<div onclick="retrair(this,  \'.caixanaoassinados \');" class="caixa retrair" style="line-height: 10vh;"><span class="lblTitulo"><i class="fa-icon-chevron-right"></i><i class="fa-icon-chevron-down"></i>Cancelamento de contratos n�o assinados </span>' +
        '<span class="lblTitulo totalNaoAssinados" style="float: right"></span>' +
        '</div><div class="caixanaoassinados"></div>' +
        '<div class="caixa retrair" style="line-height: 10vh;" onclick="retrair(this, \'.caixaassinados\');">' +
        '<span class="lblTitulo"><i class="fa-icon-chevron-right"></i><i class="fa-icon-chevron-down"></i> Cancelamento de contratos assinados </span>' +
        '<span class="lblTitulo totalAssinados" style="float: right"></span>' +
        '</div><div class="caixaassinados"></div>';
    $("#main").append(caixa);
}

function assinarTermoResponsabilidade() {

    if (boolTermoResponsabilidadeEstaAtivo){
        if (editing === true) {
            endImageEditing();
        }

        acao = 'termoresponsabilidade';
        isPlanoPersonal = false;
        $("#tituloHeader").empty();
        $("#tituloHeader").append('TERMO RESPONSABILIDADE');
        $('body').removeClass();
        desabilitarPesquisa();''
        montarTermoResponsabilidade();
        closeMenu();
    } else {
        alert("O termo de responsabilidade est� inativo ou ainda n�o foi criado!");
    }
}

function adicionarUmCartaoVacina() {
    if (editing === true) {
        endImageEditing();
    }

    acao = 'vacina';
    isPlanoPersonal = false;
    $("#tituloHeader").empty();
    $("#tituloHeader").append('CART�ES DE VACINA');
    $('body').removeClass();
    montarCartoesVacina()
    desabilitarPesquisaCartao();
    closeMenu();
}

function assinarUmPlano() {
    if (editing === true) {
        endImageEditing();
    }

    acao = 'planoPersonal';
    isPlanoPersonal = true;
    $("#tituloHeader").empty();
    $("#tituloHeader").append('CONTRATO PERSONAL');
    $('body').removeClass();
    montarContratos();
    desabilitarPesquisaPlanoPersonal();
    closeMenu();

}

function assinarParQ() {
    if (editing === true) {
        endImageEditing();
    }
    acao = 'par-q';
    $("#tituloHeader").empty();
    $("#tituloHeader").append('PAR-Q');
    $('body').removeClass();
    montarHtmlParQ();
    desabilitarPesquisaParQ();
    closeMenu();
}

function adicionarUmaFoto() {
    if (editing === true) {
        endImageEditing();
    }

    $('body').removeClass();
    $('#valorFiltro').val('');
    acao = 'foto';
    habilitarPesquisa();
    montarTipFoto();
    closeMenu();
}

function adicionarUmaFotoRecFacial() {
    if (editing === true) {
        endImageEditing();
    }

    $('body').removeClass();
    $('#valorFiltro').val('');
    acao = 'foto-facial';
    habilitarPesquisa();
    $("#main").empty();
    var caixa = '<div class="caixa atestado" style="line-height: 10vh;">PARA TIRAR UMA FOTO PARA O RECONHECIMENTO FACIAL, PESQUISE O ALUNO POR NOME OU MATR�CULA</div>';
    $("#main").append(caixa);
    closeMenu();
}

function montarTipFoto() {
    $("#main").empty();
    var caixa = '<div class="caixa atestado" style="line-height: 10vh;">PARA TIRAR UMA FOTO DE PERFIL, PESQUISE O ALUNO POR NOME OU MATR�CULA</div>';
    $("#main").append(caixa);
}

function obterCartoesVacina() {
    montarCartoesVacina();
    filtrarCartoesVacina('');
}

function obterContratos() {
    montarContratos();
    filtrarContratos('', false);
}


function changeEmpresa() {
    empresa = document.getElementById('idselectempresas').value;
    closeMenu();
    voltarInicio();
    obterContratos();
}

function filtrarPlanosPersonal(filtro) {
    var parametros = "?operacao=consultarPlanosPersonal&token="
        + params.token
        + "&remoteIP=" + ip
        + "&filtro=" + filtro;
    isPlanoPersonal = true;
    if (empresa !== null) {
        parametros = parametros + "&empresa=" + empresa;
    }
    $.post("../prest/contratoassinatura" + parametros).done(function(data) {
        if (empresa === null) {
            montarInfoAcesso(data);
        }
        montarTabelaNaoAssinadosPlanoPersonal(data.naoassinadosPlanoPersonal);
        montarTabelaAssinadosPlanoPersonal(data.assinadosPlanoPersonal);
        montarTotal(".totalNaoAssinados", data.nrnaoassinadosPlanoPersonal);
        montarTotal(".totalAssinados", data.nrAssinadosPlanoPersonal);
        if(filtro && filtro !== ''){
            $('#verTodosNaoAssinados').hide();
        }
    });
}

function filtrarContratos(filtro, cancelamento) {
    var parametros = "?operacao=consultarContratos&token=" + params.token
        + "&remoteIP=" + ip
        + "&filtro=" + filtro;
    isPlanoPersonal = false;
    assinaturaCancelamento = false;
    if (empresa !== null) {
        parametros = parametros + "&empresa=" + empresa;
    }
    if (cancelamento) {
        parametros = parametros + "&contratosCancelados=true";
        assinaturaCancelamento = true;
    }
    $.post("../prest/contratoassinatura" + parametros).done(function(data) {
        if (empresa === null) {
            montarInfoAcesso(data);
        }
        if (cancelamento) {
            montarTabelaCancelamentoNaoAssinados(data.naoassinados);
            montarTabelaAssinados(data.assinados);
        } else {
            montarTabelaNaoAssinados(data.naoassinados);
            montarTabelaAssinados(data.assinados);
        }
        permitirRemoverAssinatura = data.permissaoRemoverAssinatura;
        montarTotal(".totalNaoAssinados", data.nrnaoassinados);
        montarTotal(".totalAssinados", data.nrassinados);
        if(filtro && filtro !== ''){
            $('#verTodosNaoAssinados').hide();
        }
    });
}

function filtrarContratoProdtutos(filtro) {
    var parametros = "?operacao=consultarContratosProdutos&token=" + params.token + "&filtro=" + filtro;
    if (empresa !== null) {
        parametros = parametros + "&empresa=" + empresa;
    }
    $.post("../prest/contratoassinatura" + parametros).done(function(data) {
        if (empresa === null) {
            montarInfoAcesso(data);
        }
        montarTabelaProdutosNaoAssinados(data.naoassinados);
        montarTabelaProdutosAssinados(data.assinados);
        permitirRemoverAssinatura = data.permissaoRemoverAssinatura;
        montarTotal(".totalNaoAssinados", data.nrnaoassinados);
        montarTotal(".totalAssinados", data.nrassinados);
        if(filtro && filtro !== ''){
            $('#verTodosNaoAssinados').hide();
        }
    });
}

function filtrarCartoesVacina(filtro) {
    var parametros = "?operacao=consultarAlunoCartaoVacina&token=" + params.token
        + "&remoteIP=" + ip + "&filtro=" + filtro;
    if (empresa !== null) {
        parametros = parametros + "&empresa=" + empresa;
    }
    $.post("../prest/contratoassinatura" + parametros).done(function(data) {
        if (empresa === null) {
            montarInfoAcesso(data);
        }
        montarTabelaNaoCadastrados(data.naocadastrados);
        montarTabelaCadastrados(data.cadastrados);
        montarTotal(".totalNaoCadastrados", data.nrnaocadastrados);
        montarTotal(".totalCadastrados", data.nrcadastrados);
        if(filtro && filtro !== ''){
            $('#verTodosNaoCadastrados').hide();
        }
    });
}

function filtrarTermoResponsabilidade(filtro) {

    var parametros = "?operacao=consultarTermoResponsabilidade&token=" + params.token + "&remoteIP=" + ip + "&remoteIP=" + ip + "&filtro=" + filtro;
    isPlanoPersonal = false;
    if (empresa !== null) {
        parametros = parametros + "&empresa=" + empresa;
    }
    $.post("../prest/contratoassinatura" + parametros).done(function(data) {
        if (empresa === null) {
            montarInfoAcesso(data);
        }
        montarTabelaNaoAssinadosTermoResponsabilidade(data.naoassinados);
        montarTabelaAssinadosTermoResponsabilidade(data.assinados);
        montarTotal(".totalNaoAssinados", data.nrnaoassinados);
        montarTotal(".totalAssinados", data.nrassinados);
        if(filtro && filtro !== ''){
            $('#verTodosNaoAssinados').hide();
        }
    });
}

function filtrarClientesParQ(filtro, todos) {
    let parametros = "?operacao=consultarClientesParQ"
        + "&token=" + params.token
        + "&remoteIP=" + ip
        + "&filtro=" + encodeURIComponent(filtro);
    if (empresa !== null)  parametros += "&empresa=" + empresa;
    if (todos)             parametros += "&todos=naoassinadosParQ";

    $.post("../prest/contratoassinatura" + parametros)
        .done(function(data) {
            if (data.info) {
                montarInfoAcesso(data);
            }

            montarTabelaNaoAssinadosParQ(data.naoassinados);
            montarTabelaParQPositivo(data.parqpositivo);
            montarTabelaAssinadosParQ(data.assinados);

            montarTotal(".totalNaoAssinados", data.nrnaoassinados);
            montarTotal(".totalParQPositivos", data.nrparqpositivo);
            montarTotal(".totalAssinados", data.nrassinados);

            if (data.diasParaVencimentoParq != null && data.diasParaVencimentoParq !== 0) {
                document.getElementById('assinadosvencidosparq').style.display = '';
                montarTabelaAssinadosVencidosParQ(data.assinadosvencidos);
                montarTotal(".totalAssinadosVencidos", data.nrassinadosvencidos);
            }

            if (todos) {
                $('#verTodosParQNaoAssinados').hide();
            }
        });
}

function verTodosNaoAssinadosPlanoPersonal(){
    var parametros = "?operacao=consultarPlanosPersonal&token=" + params.token
        + "&remoteIP=" + ip
        + "&todos=naoassinadosPlanoPersonal";
    if (empresa !== null) {
        parametros = parametros + "&empresa=" + empresa;
    }
    $.post("../prest/contratoassinatura" + parametros).done(function(data) {
        if (empresa === null) {
            montarInfoAcesso(data);
        }
        montarTabelaNaoAssinadosPlanoPersonal(data.naoassinadosPlanoPersonal);
        $('#verTodosNaoAssinados').hide();
    });
}

function verTodosNaoAssinados(cancelamento){
    var parametros = "?operacao=consultarContratos&token=" + params.token + "&todos=naoassinados" + "&remoteIP=" + ip;
    if (empresa !== null) {
        parametros = parametros + "&empresa=" + empresa;
    }
    if (cancelamento) {
        parametros = parametros + "&contratosCancelados=true";
    }
    $.post("../prest/contratoassinatura" + parametros).done(function(data) {
        if (empresa === null) {
            montarInfoAcesso(data);
        }
        montarTabelaNaoAssinados(data.naoassinados);
        $('#verTodosNaoAssinados').hide();
    });
}

function verTodosProdutosNaoAssinados(){
    var parametros = "?operacao=consultarContratosProdutos&token=" + params.token + "&todos=naoassinados";
    if (empresa !== null) {
        parametros = parametros + "&empresa=" + empresa;
    }
    $.post("../prest/contratoassinatura" + parametros).done(function(data) {
        if (empresa === null) {
            montarInfoAcesso(data);
        }
        montarTabelaProdutosNaoAssinados(data.naoassinados);
        $('#verTodosNaoAssinados').hide();
    });
}

function verTodosTermoResponsabilidade() {

    var parametros = "?operacao=consultarTermoResponsabilidade&token=" + params.token + "&remoteIP=" + ip + "&todos=naoassinados";
    isPlanoPersonal = false;
    if (empresa !== null) {
        parametros = parametros + "&empresa=" + empresa;
    }
    $.post("../prest/contratoassinatura" + parametros).done(function(data) {
        if (empresa === null) {
            montarInfoAcesso(data);
        }
        montarTabelaNaoAssinadosTermoResponsabilidade(data.naoassinados);
        $('#verTodosNaoAssinados').hide();
    });
}

function verTodosNaoCadastrados(){
    var parametros = "?operacao=consultarAlunoCartaoVacina&token=" + params.token + "&remoteIP=" + ip  + "&todos=naocadastrados";
    if (empresa !== null) {
        parametros = parametros + "&empresa=" + empresa;
    }
    $.post("../prest/contratoassinatura" + parametros).done(function(data) {
        if (empresa === null) {
            montarInfoAcesso(data);
        }
        montarTabelaNaoCadastrados(data.naocadastrados);
        $('#verTodosNaoCadastrados').hide();
    });
}

function montarInfoAcesso(data) {
    $("#username").append(data.info.nomeusuario);
    if (data.info.empresas.length === 1) {
        $("#empresas").append(data.info.empresas[0]["nome"]);
        empresa = data.info.empresas[0]["codigo"];
    } else {
        empresa = data.info.empresa;
        var sel = $('<select id="idselectempresas" onchange="changeEmpresa()">').appendTo('#empresas');
        $(data.info.empresas).each(function() {
            sel.append($("<option>").attr('value', this.codigo).text(this.nome));
        });
        document.getElementById('idselectempresas').value = empresa;
    }
}


function filtrarAlunosAtestado(filtro) {
    var parametros = "?operacao=pesquisarParaAtestado&token=" + params.token + "&remoteIP=" + ip + "&filtro=" + filtro;
    if (empresa !== null) {
        parametros = parametros + "&empresa=" + empresa;
    }
    $.post("../prest/contratoassinatura" + parametros).done(function(data) {
        montarTabelaAtestados(data.alunos);
    });
}

function montarTotal(caixa, total) {
    $(caixa).empty();
    $(caixa).append(total);
}

function montarTabelaNaoAssinados(data) {
    $(".caixanaoassinados").empty();
    for (var i = 0; i < data.length; i++)
    {
        var caixa = `<a id="pos-${i}" onclick="selecionarContrato(this, false);"
        contrato="${data[i]["contrato"]}"
        urlFoto="${data[i]["urlFoto"]}"
        >
            <div class="caixa contrato">
                <img class="fotoAluno" alt="Foto Aluno" src="${data[i]["urlFoto"]}"></img>
                <div class="info">
                    <span class="nomeAluno"> ${data[i]["nome"]} 
                        <span style="color: #777777; float: right;">Contrato: ${data[i]["contrato"]}</span>
                    </span>
                    <span class="infoAssinatura">Contrato para assinar</span>
                </div>
            </div>
        </a>
        `;
        $(".caixanaoassinados").append(caixa);
    }
    var mais = '<a id="verTodosNaoAssinados" onclick="verTodosNaoAssinados(false)"><div class="caixa contrato" style="font-size: 3vh;line-height: 10vh; text-align: center; display: block;">Ver todos </div></a>';
    $(".caixanaoassinados").append(mais);

    if (data.length == 1){
        let link = document.getElementById("pos-0")

        if (link){
            link.click()
        }
    }
}

function montarTabelaProdutosNaoAssinados(data) {
    $(".caixanaoassinados").empty();
    for (var i = 0; i < data.length; i++)
    {
        var caixa = '<a onclick="selecionarContratoProduto(this);" contrato="' + data[i]["contrato"]
            + '" urlFoto="' + data[i]["urlFoto"] + '" ><div class="caixa contrato"><img class="fotoAluno" alt="Foto Aluno" src="'
            + data[i]["urlFoto"]
            + '"></img>'
            + '<div class="info"><span class="nomeAluno">' + data[i]["nome"] + '<span style="color: #777777; float: right;">Contrato: ' + data[i]["contrato"] + '</span></span>'
            + '<span class="infoAssinatura">Contrato para assinar</span></div></div></a>';
        $(".caixanaoassinados").append(caixa);
    }
    var mais = '<a id="verTodosNaoAssinados" onclick="verTodosProdutosNaoAssinados()"><div class="caixa contrato" style="font-size: 3vh;line-height: 10vh; text-align: center; display: block;">Ver todos </div></a>';
    $(".caixanaoassinados").append(mais);
}

function montarTabelaCancelamentoNaoAssinados(data) {
    $(".caixanaoassinados").empty();
    for (var i = 0; i < data.length; i++)
    {
        var caixa = '<a onclick="selecionarContrato(this, true);" contrato="' + data[i]["contrato"]
            + '" urlFoto="' + data[i]["urlFoto"] + '" msgCancelamento="' + data[i]["msgCancelamento"] + '" justificativaCancelamento="' + data[i]["justificativaCancelamento"] + '" ><div class="caixa contrato"><img class="fotoAluno" alt="Foto Aluno" src="'
            + data[i]["urlFoto"]
            + '"></img>'
            + '<div class="info"><span class="nomeAluno">' + data[i]["nome"] + '<span style="color: #777777; float: right;">Contrato: ' + data[i]["contrato"] + '</span></span>'
            + '<span class="infoAssinatura">Cancelamento para assinar</span></div></div></a>';
        $(".caixanaoassinados").append(caixa);
    }
    var mais = '<a id="verTodosNaoAssinados" onclick="verTodosNaoAssinados(true)"><div class="caixa contrato" style="font-size: 3vh;line-height: 10vh; text-align: center; display: block;">Ver todos </div></a>';
    $(".caixanaoassinados").append(mais);
}

function montarTabelaAssinadosTermoResponsabilidade(data) {
    $(".caixaassinados").empty();
    for (var i = 0; i < data.length; i++)
    {
        var caixa = '<a onclick="visualizarAssinaturaTermoResponsabilidade(this)" matricula="' + data[i]["matricula"]
            + '" urlFoto="' + data[i]["urlFoto"] + '" ><div class="caixa contrato"><img class="fotoAluno" alt="Foto Aluno" src="'
            + data[i]["urlFoto"]
            + '"></img>'
            + '<div class="info"><span class="nomeAluno">' + data[i]["nome"] + '<span style="color: #777777; float: right;">Matr�cula: ' + data[i]["matricula"] + '</span></span>'
            + '<span class="infoAssinatura">Termo Responsabilidade assinado</span></div></div></a>';
        $(".caixaassinados").append(caixa);
    }
}

function montarTabelaNaoAssinadosTermoResponsabilidade(data) {
    $(".caixanaoassinados").empty();
    for (var i = 0; i < data.length; i++)
    {
        var caixa = '<a onclick=" selecionarTermoResponsabilidade(this);" matricula ="' + data[i]["matricula"]
            + '" urlFoto="' + data[i]["urlFoto"] + '" ><div class="caixa contrato"><img class="fotoAluno" alt="Foto Aluno" src="'
            + data[i]["urlFoto"]
            + '"></img>'
            + '<div class="info"><span class="nomeAluno">' + data[i]["nome"] + '<span style="color: #777777; float: right;">Matr�cula: ' + data[i]["matricula"] + '</span></span>'
            + '<span class="infoAssinatura">Termo Responsabilidade para assinar</span></div></div></a>';
        $(".caixanaoassinados").append(caixa);
    }
    var mais = '<a id="verTodosNaoAssinados" onclick="verTodosTermoResponsabilidade(false)"><div class="caixa contrato" style="font-size: 3vh;line-height: 10vh; text-align: center; display: block;">Ver todos </div></a>';
    $(".caixanaoassinados").append(mais);
}

function montarTabelaNaoAssinadosParQ(data) {
    $(".caixanaoassinados").empty();
    for (let i = 0; i < data.length; i++) {
        const cli = data[i];
        const caixa =
            `<a onclick="selecionarPessoaParQ(this, null);"
           contrato="${cli.contrato}"
           urlFoto="${cli.urlFoto}"
           nomeAluno="${cli.nome}"
           matriculaAluno="${cli.matricula}">
         <div class="caixa contrato">
           <img class="fotoAluno" alt="Foto Aluno" src="${cli.urlFoto}">
           <div class="info">
             <span class="nomeAluno">${cli.nome}
               <span style="color:#777777;float:right;">Matr�cula: ${cli.matricula}</span>
             </span>
             <span class="infoAssinatura">Par-Q para assinar</span>
           </div>
         </div>
       </a>`;
        $(".caixanaoassinados").append(caixa);
    }

    var mais =
        '<div style="text-align: center; margin: 10px 0;">' +
        '<a id="verTodosParQNaoAssinados" onclick="verTodosParQ()">' +
        '<div class="caixa contrato" ' +
        'style="font-size: 3vh; line-height: 10vh; text-align: center; display: inline-block;">' +
        'Ver todos' +
        '</div>' +
        '</a>' +
        '</div>';
    $(".caixanaoassinados").append(mais);
}

function verTodosParQ() {
    $('#valorFiltro').val('');
    filtrarClientesParQ('', true);
}

function montarTabelaParQPositivo(data) {
    $(".caixaparqpositivos").empty();
    for (var i = 0; i < data.length; i++)
    {
        const date = new Date(data[i]["assinadoem"]);
        const dataFormatada = (date.getDate().toString().length === 1 ? '0' + date.getDate() : date.getDate() )
            + "/" + ((date.getMonth()+1).toString().length === 1 ? '0' + (date.getMonth()+1) : (date.getMonth()+1))
            + "/" + date.getFullYear()
            + " s " + (date.getHours().toString().length === 1 ? '0' + date.getHours() : date.getHours() )
            + ":" + (date.getMinutes().toString().length === 1 ? '0' + date.getMinutes() : date.getMinutes());
        var caixa = '';
        if (data[i].assinado) {
            caixa = '<a onclick="visualizarRespostasParQ(this);" matriculaAluno="' + data[i]["matricula"] + '" codigoRespostaParq="' + data[i]["codigorespostaclienteparq"] + '" nomeAluno="' + data[i]["nome"] + '"><div class="caixa contrato"><img class="fotoAluno" alt="Foto Aluno" src="'
                + data[i]["urlFoto"]
                + '"></img>'
                + '<div class="info"><span class="nomeAluno">' + data[i]["nome"] + '<span style="color: #777777; float: right;">Matr�cula: ' + data[i]["matricula"] + '</span></span>'
                + '<span class="infoAssinatura">Assinado em: ' + dataFormatada + '</span></div></div></a>';
        } else {
            caixa = '<a onclick="selecionarPessoaParQ(this, null);" contrato="' + data[i]["contrato"]
                + '" urlFoto="' + data[i]["urlFoto"] + '" nomeAluno="' + data[i]["nome"] + '" matriculaAluno="' + data[i]["matricula"] + '"><div class="caixa contrato"><img class="fotoAluno" alt="Foto Aluno" src="'
                + data[i]["urlFoto"]
                + '"></img>'
                + '<div class="info"><span class="nomeAluno">' + data[i]["nome"] + '<span style="color: #777777; float: right;">Matr�cula: ' + data[i]["matricula"] + '</span></span>'
                + '<span class="infoAssinatura">Par-q para assinar</span></div></div></a>';
        }
        $(".caixaparqpositivos").append(caixa);
    }
}

function montarTabelaAssinadosParQ(data) {
    $(".caixaassinados").empty();
    for (var i = 0; i < data.length; i++)
    {
        const date = new Date(data[i]["assinadoem"]);
        const dataFormatada = (date.getDate().toString().length === 1 ? '0' + date.getDate() : date.getDate() )
            + "/" + ((date.getMonth()+1).toString().length === 1 ? '0' + (date.getMonth()+1) : (date.getMonth()+1))
            + "/" + date.getFullYear()
            + " s " + (date.getHours().toString().length === 1 ? '0' + date.getHours() : date.getHours() )
            + ":" + (date.getMinutes().toString().length === 1 ? '0' + date.getMinutes() : date.getMinutes());
        var caixa = '<a onclick="visualizarRespostasParQ(this);" matriculaAluno="' + data[i]["matricula"] + '" codigoRespostaParq="' + data[i]["codigorespostaclienteparq"] + '" nomeAluno="' + data[i]["nome"] + '"><div class="caixa contrato"><img class="fotoAluno" alt="Foto Aluno" src="'
            + data[i]["urlFoto"]
            + '"></img>'
            + '<div class="info"><span class="nomeAluno">' + data[i]["nome"] + '<span style="color: #777777; float: right;">Matr�cula: ' + data[i]["matricula"] + '</span></span>'
            + '<span class="infoAssinatura">Assinado em: ' + dataFormatada + '</span></div></div></a>';
        $(".caixaassinados").append(caixa);
    }

}

function montarTabelaAssinadosVencidosParQ(data) {
    $(".caixaassinadosvencidos").empty();
    for (var i = 0; i < data.length; i++)
    {
        const date = new Date(data[i]["assinadoem"]);
        const dataFormatada = (date.getDate().toString().length === 1 ? '0' + date.getDate() : date.getDate() )
            + "/" + ((date.getMonth()+1).toString().length === 1 ? '0' + (date.getMonth()+1) : (date.getMonth()+1))
            + "/" + date.getFullYear()
            + " s " + (date.getHours().toString().length === 1 ? '0' + date.getHours() : date.getHours() )
            + ":" + (date.getMinutes().toString().length === 1 ? '0' + date.getMinutes() : date.getMinutes());
        const dateVenc = new Date(data[i]["vencidoem"]);
        const dataFormatadaVenc = (dateVenc.getDate().toString().length === 1 ? '0' + dateVenc.getDate() : dateVenc.getDate() )
            + "/" + ((dateVenc.getMonth()+1).toString().length === 1 ? '0' + (dateVenc.getMonth()+1) : (dateVenc.getMonth()+1))
            + "/" + dateVenc.getFullYear()
            + " s " + (dateVenc.getHours().toString().length === 1 ? '0' + dateVenc.getHours() : dateVenc.getHours() )
            + ":" + (dateVenc.getMinutes().toString().length === 1 ? '0' + dateVenc.getMinutes() : dateVenc.getMinutes());
        var caixa = '<a onclick="selecionarPessoaParQ(this, null);" matriculaAluno="' + data[i]["matricula"] + '" nomeAluno="' + data[i]["nome"] + '"><div class="caixa contrato"><img class="fotoAluno" alt="Foto Aluno" src="'
            + data[i]["urlFoto"]
            + '"></img>'
            + '<div class="info"><span class="nomeAluno">' + data[i]["nome"] + '<span style="color: #777777; float: right;">Matr�cula: ' + data[i]["matricula"] + '</span></span>'
            + '<span class="infoAssinatura">Assinado em: ' + dataFormatada + ' e Vencido em: ' + dataFormatadaVenc + '</span></div></div></a>';
        $(".caixaassinadosvencidos").append(caixa);
    }

}

function montarTabelaNaoCadastrados(data) {
    $(".caixanaocadastrados").empty();
    for (var i = 0; i < data.length; i++)
    {
        var caixa = '<a onclick="selecionarPessoa(this);" pessoa="' + data[i]["pessoa"]
            + '" urlFoto="' + data[i]["urlFoto"] + '" ><div class="caixa contrato"><img class="fotoAluno" alt="Foto Aluno" src="'
            + data[i]["urlFoto"]
            + '"></img>'
            + '<div class="info"><span class="nomeAluno">' + data[i]["nome"] + '<span style="color: #777777; float: right;">'+ data[i]["matricula"]+ ' </span></span>'
            + '<span class="infoAssinatura">Cart�o de Vacina</span></div></div></a>';
        $(".caixanaocadastrados").append(caixa);
    }
    var mais = '<a id="verTodosNaoCadastrados" onclick="verTodosNaoCadastrados()"><div class="caixa contrato" style="font-size: 3vh;line-height: 10vh; text-align: center; display: block;">Ver todos </div></a>';
    $(".caixanaocadastrados").append(mais);
}

function montarTabelaNaoAssinadosPlanoPersonal(data) {
    $(".caixanaoassinados").empty();
    for (var i = 0; i < data.length; i++)
    {
        var caixa = '<a onclick="selecionarPlano(this);" taxaPersonal="' + data[i]["taxaPersonal"]
            + '" urlFoto="' + data[i]["urlFotoPersonal"] + '" ><div class="caixa contrato"><img class="fotoAluno" alt="Foto Personal" src="'
            + data[i]["urlFotoPersonal"]
            + '"></img>'
            + '<div class="info"><span class="nomeAluno">' + data[i]["nomePersonal"] + '<span style="color: #777777; float: right;">Taxa personal: ' + data[i]["taxaPersonal"] + '</span></span>'
            + '<span class="infoAssinatura">Plano Personal para assinar</span></div></div></a>';
        $(".caixanaoassinados").append(caixa);
    }
    var mais = '<a id="verTodosNaoAssinados" onclick="verTodosNaoAssinadosPlanoPersonal()"><div class="caixa contrato" style="font-size: 3vh;line-height: 10vh; text-align: center; display: block;">Ver todos</div></a>';
    $(".caixanaoassinados").append(mais);
}

function montarTabelaAssinados(data) {
    $(".caixaassinados").empty();
    for (var i = 0; i < data.length; i++)
    {
        if(data[i]["ipassinaturacontrato"].length == 0){
            var caixa = '<a onclick="selecionarContratoVisualizar(this);" contrato="' + data[i]["contrato"] + '"><div class="caixa contrato"><img class="fotoAluno" alt="Foto Aluno" src="'
                + data[i]["urlFoto"]
                + '"></img>'
                + '<div class="info"><span class="nomeAluno">' + data[i]["nome"] + '<span style="color: #777777; float: right;">Contrato: ' + data[i]["contrato"] + '</span></span>'
                + '<span class="infoAssinatura">' + data[i]["assinadoem"] + '</span></div></div></a>';
            $(".caixaassinados").append(caixa);
        }else{
            var caixa = '<a onclick="abrirModalAssinaturaEletronica(\''+data[i]["dataAssinatura"] +'\' ,\''+data[i]["cpf"]+ '\', \'' + data[i]["ipassinaturacontrato"]+'\', \'' +data[i]["emailrecebimento"] + '\' , \'' + data[i]["contrato"]+'\' );" ><div class="caixa contrato"><img class="fotoAluno" alt="Foto Aluno" src="'
                + data[i]["urlFoto"]+ '">'
                + '<div class="info"><span class="nomeAluno">' + data[i]["nome"] + '<span style="color: #777777; float: right;">Contrato: ' + data[i]["contrato"] + '</span></span>'
                + '<span class="infoAssinatura">' + data[i]["assinadoem"] + '</span></div></div></a>';
            $(".caixaassinados").append(caixa);
        }
    }

}

function montarTabelaProdutosAssinados(data) {
    $(".caixaassinados").empty();
    for (var i = 0; i < data.length; i++)
    {
        if(data[i]["ipassinaturacontrato"].length == 0){
            var caixa = '<a onclick="selecionarContratoProdutoVisualizar(this);" contrato="' + data[i]["contrato"] + '"><div class="caixa contrato"><img class="fotoAluno" alt="Foto Aluno" src="'
                + data[i]["urlFoto"]
                + '"></img>'
                + '<div class="info"><span class="nomeAluno">' + data[i]["nome"] + '<span style="color: #777777; float: right;">Contrato: ' + data[i]["contrato"] + '</span></span>'
                + '<span class="infoAssinatura">' + data[i]["assinadoem"] + '</span></div></div></a>';
            $(".caixaassinados").append(caixa);
        }else{
            var caixa = '<a onclick="abrirModalAssinaturaEletronica(\''+data[i]["dataAssinatura"] +'\' ,\''+data[i]["cpf"]+ '\', \'' + data[i]["ipassinaturacontrato"]+'\', \'' +data[i]["emailrecebimento"] + '\' , \'' + data[i]["contrato"]+'\' );" ><div class="caixa contrato"><img class="fotoAluno" alt="Foto Aluno" src="'
                + data[i]["urlFoto"]+ '">'
                + '<div class="info"><span class="nomeAluno">' + data[i]["nome"] + '<span style="color: #777777; float: right;">Contrato: ' + data[i]["contrato"] + '</span></span>'
                + '<span class="infoAssinatura">' + data[i]["assinadoem"] + '</span></div></div></a>';
            $(".caixaassinados").append(caixa);
        }
    }

}

function montarTabelaCadastrados(data) {
    $(".caixacadastrados").empty();
    for (var i = 0; i < data.length; i++)
    {
        var caixa = '<a onclick="selecionarPessoaVisualizar(this);" pessoa="' + data[i]["pessoa"] + '"><div class="caixa contrato"><img class="fotoAluno" alt="Foto Aluno" src="'
            + data[i]["urlFoto"]
            + '"></img>'
            + '<div class="info"><span class="nomeAluno">' + data[i]["nome"] + '<span style="color: #777777; float: right;">'+ data[i]["matricula"]+ '</span></span>'
            + '<span class="infoAssinatura">' + data[i]["cadastradoem"] + '</span></div></div></a>';
        $(".caixacadastrados").append(caixa);
    }

}

function montarTabelaAssinadosPlanoPersonal(data) {
    $(".caixaassinados").empty();
    for (var i = 0; i < data.length; i++)
    {
        var caixa = '<a onclick="selecionarPlanoVisualizar(this);" taxaPersonal="' + data[i]["taxaPersonal"] + '"><div class="caixa contrato"><img class="fotoAluno" alt="Foto Aluno" src="'
            + data[i]["urlFotoPersonal"]
            + '"></img>'
            + '<div class="info"><span class="nomeAluno">' + data[i]["nomePersonal"] + '<span style="color: #777777; float: right;">' + data[i]["taxaPersonal"] + '</span></span>'
            + '<span class="infoAssinatura">' + data[i]["assinadoemPersonal"] + '</span></div></div></a>';
        $(".caixaassinados").append(caixa);
    }

}

function montarTabelaAtestados(data) {
    $("#main").empty();
    var d = new Date();
    var n = d.getTime();
    for (var i = 0; i < data.length; i++)
    {
        var caixa = '<a onclick="selecionarAlunoAtestado(this, \'' + data[i]["nome"] + '\');" codigo="'
            + data[i]["codigo"] + '" codacesso="' + data[i]["codacesso"]
            + '" urlFoto="' + data[i]["urlFoto"] + '" "><div class="caixa contrato"><img class="fotoAluno" alt="Foto Aluno" src="'
            + data[i]["urlFoto"]
            + '"></img>'
            + '<div class="info"><span class="nomeAluno">' + data[i]["nome"] + '</span>'
            + '</div></div></a>';
        $("#main").append(caixa);
    }
}

function voltarInicio() {
    if (acao === 'vacina') {
        $("#tituloHeader").empty();
        $("#tituloHeader").append('CART�ES DE VACINA');
        $("body").removeClass();
        montarCartoesVacina();
        filtrarCartoesVacina('')
    } else if (isPlanoPersonal === true){
        $("#tituloHeader").empty();
        $("#tituloHeader").append('CONTRATOS');
        $("body").removeClass();
        montarContratos();
        filtrarPlanosPersonal('');
    } else if (acao === 'par-q'){
        $("#tituloHeader").empty();
        $("#tituloHeader").append('PAR-Q');
        $("body").removeClass();
        montarHtmlParQ();
        filtrarClientesParQ('');
    } else if (acao === 'termoresponsabilidade'){
        $("#tituloHeader").empty();
        $("#tituloHeader").append('TERMO RESPONSABILIDADE');
        $("body").removeClass();
        montarTermoResponsabilidade();
        filtrarTermoResponsabilidade('');
    } else if (acao === 'contratoCancelado'){
        $("#tituloHeader").empty();
        $("#tituloHeader").append('CANCELAMENTO DE CONTRATOS');
        $("body").removeClass();
        montarContratosCancelados();
        filtrarContratos('', true);
    } else if (acao === 'contratoProduto'){
        $("#tituloHeader").empty();
        $("#tituloHeader").append('CONTRATOS DE PRODUTOS');
        $("body").removeClass();
        montarContratoProdutos();
        filtrarContratoProdtutos('');
    } else {
        $("#tituloHeader").empty();
        $("#tituloHeader").append('CONTRATOS');
        $("body").removeClass();
        montarContratos();
        filtrarContratos('', false);
    }
}

function voltar() {
    if(acao == 'vacina'){
        voltarInicio();
        return;
    }
    if(isPlanoPersonal === true){
        if ($("body").hasClass('assinar') || $("body").hasClass('jaAssinado')) {
            voltarInicio();
            return;
        }
        if ($("body").hasClass('documentos') && $("body").hasClass('reenvio')) {
            selecionarPlanoVisualizar(contratoSelecionado_item);
            return;
        } else if ($("body").hasClass('documentos') && !$("body").hasClass('reenvio')) {
            assinando = true;
            nomeAluno = '';
            $('header').removeClass('pesquisando');
            $("#main").empty();
            $("body").removeClass();
            $("#valorFiltro").val('');
            $("#tituloHeader").empty();

            fotoAlterada = false;
            imageFotoAluno = null;
            $("#tituloHeader").append('FOTO DO PERFIL');
            $("#idnomeparafoto").empty();
            $("#idnomeparafoto").append('');
            $("body").addClass('fotoPerfil');
            $("body").addClass('assinando');
            scrollTop();
            return;
        }
        if ($("body").hasClass('assinando')) {
            $("body").removeClass();
            $("#main").empty();
            $("#valorFiltro").val('');
            $("#tituloHeader").empty();
            var caixa = '<div class="caixacontrato">' + texto + '</div>';
            $("#main").append(caixa);
            $("#tituloHeader").append('VER CONTRATO');
            $("body").addClass('assinar');
            construirCanvas();
            construirCanvas2();
            scrollTop();
        }

        if ($("body").hasClass('validar')) {
            $("body").removeClass('validar');
            $("body").addClass('documentos');
        }
    } else {
        if ($("body").hasClass('assinar') || $("body").hasClass('jaAssinado')) {
            voltarInicio();
            return;
        }
        if ($("body").hasClass('documentos') && $("body").hasClass('reenvio')) {
            selecionarContratoVisualizar(contratoSelecionado_item);
            return;
        } else if ($("body").hasClass('documentos') && !$("body").hasClass('reenvio')) {
            assinando = true;
            nomeAluno = '';
            $('header').removeClass('pesquisando');
            $("#main").empty();
            $("body").removeClass();
            $("#valorFiltro").val('');
            $("#tituloHeader").empty();

            fotoAlterada = false;
            imageFotoAluno = null;
            $("#tituloHeader").append('FOTO DO PERFIL');
            $("#idnomeparafoto").empty();
            $("#idnomeparafoto").append('');
            $("body").addClass('fotoPerfil');
            $("body").addClass('assinando');
            scrollTop();
            return;
        }
        if ($("body").hasClass('assinando')) {
            $("body").removeClass();
            $("#main").empty();
            $("#valorFiltro").val('');
            $("#tituloHeader").empty();
            var caixa = '<div class="caixacontrato">' + texto + '</div>';
            $("#main").append(caixa);
            $("#tituloHeader").append('VER CONTRATO');
            $("body").addClass('assinar');
            construirCanvas();
            construirCanvas2();
            scrollTop();
        }

        if ($("body").hasClass('validar')) {
            $("body").removeClass('validar');
            $("body").addClass('documentos');
        }
    }
}

function concluir() {
    document.location.reload();
}

function concluido() {
    var body = $("body");
    body.addClass('concluido');
    body.removeClass('validar');

    var msgConcluir = $("#msgConcluir");
    msgConcluir.empty();
    var msgApresentar = (body.hasClass('reenvio')) ? 'DOCUMENTOS REENVIADOS COM SUCESSO!' : 'CONTRATO ASSINADO COM SUCESSO!';
    msgConcluir.append(msgApresentar);
    $("#main").empty();
}

function selecionarPlano(item) {
    fotoAlterada = false;
    $('header').removeClass('pesquisando');
    imageAssinatura = null;
    updateAssinatura = 'true';
    var d = new Date();
    var n = d.getTime();
    if (editing === true) {
        endImageEditing();
    }
    updateDoc = 'false';
    updateEnd = 'false';
    updateAte = 'false';
    updateAnexo1 = 'false';
    updateAnexo2 = 'false';
    document.getElementById('pseudoprofile').src = '../' + $(item).attr('urlFotoPersonal');
    var taxaPersonal = $(item).attr('taxaPersonal');
    $.post("../prest/contratoassinatura?operacao=selecionarPlanoPersonal&token=" + params.token + "&remoteIP=" + ip + "&taxaPersonal=" + taxaPersonal).done(function(data) {
        $("#main").empty();
        $("#valorFiltro").val('');
        $("#tituloHeader").empty();
        codigoContrato = data.dadosPersonal.taxaPersonal;
        codigoModelo = data.dadosPersonal.modeloPersonal;
        texto = data.dadosPersonal.textoPersonal;
        var caixa = '<div class="caixacontrato">' + data.dadosPersonal.textoPersonal + '</div>';
        $("#main").append(caixa);
        $("#tituloHeader").append('VER CONTRATO');
        $("body").addClass('assinar');
        construirCanvas();
        if (!isPlanoPersonal){
            var modalAssFinApresentar = document.getElementById("assFinApresentar");
            var modalAssFinAssinar = document.getElementById("assFinAssinar");
            modalAssFinApresentar.style.display = "block";
            modalAssFinAssinar.style.display = "block";
            construirCanvas2();
        }else{
            var modalAssFinApresentar = document.getElementById("assFinApresentar");
            var modalAssFinAssinar = document.getElementById("assFinAssinar");
            modalAssFinApresentar.style.display = "none";
            modalAssFinAssinar.style.display = "none";
        }
        scrollTop();
    });
}

function selecionarTermoResponsabilidade(item) {
    fotoAlterada = false;
    $('header').removeClass('pesquisando');
    imageAssinatura == null;
    updateAssinatura = 'true';
    var d = new Date();
    var n = d.getTime();
    if (editing === true) {
        endImageEditing();
    }
    updateDoc = 'false';
    updateEnd = 'false';
    updateAte = 'false';
    updateAnexo1 = 'false';
    updateAnexo2 = 'false';
    document.getElementById('pseudoprofile').src = '../' + $(item).attr('urlFoto');
    matriculaAlunoSelecionado = $(item).attr('matricula');
    $.post("../prest/contratoassinatura?operacao=selecionarClienteTermoResponsabilidade&token="
        + params.token + "&remoteIP=" + ip + "&path=" + window.location.href + "&matricula=" + matriculaAlunoSelecionado).done(function(data) {
        $("#main").empty();
        $("#valorFiltro").val('');
        $("#tituloHeader").empty();
        codigoMatricula = data.matricula;
        texto = data.texto;
        var caixa = '<div class="caixacontrato" style="text-align: center;margin-left: 15em;margin-right: 15em;FONT-WEIGHT: 100;font-size: 1.3em;">' + data.texto + '</div>';
        $("#main").append(caixa);
        $("#tituloHeader").append('TERMO RESPONSABILIDADE');
        $("body").addClass('assinarTermoResponsabilidade');
        construirCanvasTermoResponsabilidade();
        scrollTop();
    });

}

function selecionarContrato(item, cancelamento) {
    fotoAlterada = false;
    $('header').removeClass('pesquisando');
    imageAssinatura = null;
    imageAssinatura2 = null;
    updateAssinatura = 'true';
    var d = new Date();
    var n = d.getTime();
    if (editing === true) {
        endImageEditing();
    }
    updateDoc = 'false';
    updateEnd = 'false';
    updateAte = 'false';
    updateAnexo1 = 'false';
    updateAnexo2 = 'false';
    document.getElementById('pseudoprofile').src = '../' + $(item).attr('urlFoto');
    if (cancelamento) {
        $("#main").empty();
        $("#caixaValidarParq").empty();
        $("#valorFiltro").val('');
        $("#tituloHeader").empty();
        codigoContrato = $(item).attr('contrato');
        texto = $(item).attr('msgCancelamento');
        var caixa = '<div class="caixatitlecancel"><div style="font-weight: bold">JUSTIFICATIVA:</div><div style="margin-top: 1%">' + $(item).attr('justificativaCancelamento') + '</div></div>' +
            '<div class="caixacontrato">' +
            '<div style="text-align: center;font-weight: bold">DESCRI��O DO CANCELAMENTO:</div>' +
            '<div style="text-align: left;margin-top: 1%">' +
            '<div style="width: 50%;margin-left: 25%">' + texto + '</div>' +
            '</div>' +
            '</div>';
        $("#main").append(caixa);
        $("#tituloHeader").append('VER CANCELAMENTO');
        $("body").addClass('assinar');
        construirCanvas();
        construirCanvas2();
        scrollTop();
    } else {
        var contrato = $(item).attr('contrato');
        $.post("../prest/contratoassinatura?operacao=selecionarContrato&token=" + params.token + "&remoteIP=" + ip + "&contrato=" + contrato).done(function (data) {
            $("#main").empty();
            $("#caixaValidarParq").empty();
            $("#valorFiltro").val('');
            $("#tituloHeader").empty();
            codigoContrato = data.dados.contrato;
            codigoModelo = data.dados.modelo;
            texto = data.dados.texto;
            var caixa = '<div class="caixacontrato">' + data.dados.texto + '</div>';
            $("#main").append(caixa);
            $("#tituloHeader").append('VER CONTRATO');
            $("body").addClass('assinar');
            construirCanvas();
            permitirApresentarAssinatura2 = data.dados.permitirApresentarAssinatura2;
            responsavelFinanceiroPreenchido = data.dados.responsavelFinanceiroPreenchido;
            if (permitirApresentarAssinatura2 && responsavelFinanceiroPreenchido) {
                var modalAssFinApresentar = document.getElementById("assFinApresentar");
                var modalAssFinAssinar = document.getElementById("assFinAssinar");
                modalAssFinApresentar.style.display = "block";
                modalAssFinAssinar.style.display = "block";
                construirCanvas2();
            } else {
                var modalAssFinApresentar = document.getElementById("assFinApresentar");
                var modalAssFinAssinar = document.getElementById("assFinAssinar");
                modalAssFinApresentar.style.display = "none";
                modalAssFinAssinar.style.display = "none";
            }
            scrollTop();
        });
    }
}

function selecionarContratoProduto(item) {
    fotoAlterada = false;
    $('header').removeClass('pesquisando');
    imageAssinatura = null;
    imageAssinatura2 = null;
    updateAssinatura = 'true';
    var d = new Date();
    var n = d.getTime();
    if (editing === true) {
        endImageEditing();
    }
    updateDoc = 'false';
    updateEnd = 'false';
    updateAte = 'false';
    updateAnexo1 = 'false';
    updateAnexo2 = 'false';
    document.getElementById('pseudoprofile').src = '../' + $(item).attr('urlFoto');
    var contrato = $(item).attr('contrato');
    $.post("../prest/contratoassinatura?operacao=selecionarContratoProduto&token=" + params.token + "&contrato=" + contrato).done(function (data) {
        $("#main").empty();
        $("#caixaValidarParq").empty();
        $("#valorFiltro").val('');
        $("#tituloHeader").empty();
        codigoContrato = data.dados.contrato;
        codigoModelo = data.dados.modelo;
        texto = data.dados.texto;
        var caixa = '<div class="caixacontrato">' + data.dados.texto + '</div>';
        $("#main").append(caixa);
        $("#tituloHeader").append('VER CONTRATO DE PRODUTO');
        $("body").addClass('assinar');
        construirCanvas();
        var modalAssFinApresentar = document.getElementById("assFinApresentar");
        var modalAssFinAssinar = document.getElementById("assFinAssinar");
        modalAssFinApresentar.style.display = "none";
        modalAssFinAssinar.style.display = "none";
        scrollTop();
    });
}


function selecionarPessoa(item) {

    codigoPessoa = $(item).attr('pessoa');
    nomeAluno = $(item).attr('nome');
    $('header').removeClass('pesquisando');
    $("#main").empty();
    $("#valorFiltro").val('');
    $("#tituloHeader").empty();
    var d = new Date();
    var n = d.getTime();
    if (editing === true) {
        endImageEditing();
    }
    updateAnexo1Cartao = false;
    $("#tituloHeader").append('CART�O DE VACINA');
    limparAnexo1Cartao();
    limparTipoAnexo();
    $("#idnomeparacartao1").empty();
    $("#idnomeparacartao1").append($(item).attr('nome'));
    $("body").removeClass('cadastrado');
    $("body").addClass('cartaovacina');
    scrollTop();
}

function selecionarPessoaParQ(item, matricula) {
    codigoRespostaParqAlunoSelecionado = 0;
    if (item !== null) {
        matriculaAlunoSelecionado = $(item).attr('matriculaAluno');
    } else {
        matriculaAlunoSelecionado = matricula;
    }
    $('header').removeClass('pesquisando');
    $("#main").empty();
    $("#valorFiltro").val('');
    $("#tituloHeader").empty();
    var d = new Date();
    var n = d.getTime();

    $("#tituloHeader").append('PAR-Q');
    $("body").removeClass('cadastrado');
    $("body").addClass('parQ');

    $.post("../prest/contratoassinatura" + montarParametros(null)).done(function(data) {
        perguntasParQ = data.perguntasParQ;
        $("#perguntaParQ").remove();
        $("#perguntasParQ").append('<div id="perguntaParQ"></div>');
        for (var i = 0, numero = 1; i < data.perguntasParQ.length; i++, numero++) {
            $("#perguntaParQ").append(montarHtmlPerguntaParQ(data.perguntasParQ[i], numero, null));
        }
        $("body").addClass('assinarParQ');
        $("#checkTermoDeAceite").prop("checked", false);
        $("#texto_obrigatorio_termo").removeClass('texto_alert_termo');
        if (data.apresentarLeiParqRJ) {
            $('#leiParqRJ').show();
        } else {
            $('#leiParqRJ').hide();
        }

        if (data.apresentarLeiParqGO) {
            $('#leiParqGO').show();
        } else {
            $('#leiParqGO').hide();
        }
        construirCanvasParQ();
    });
    scrollTop();
}

function selecionarPessoaEditarParQ(matricula) {
    matriculaAlunoSelecionado = matricula;

    $('header').removeClass('pesquisando');
    $("#main").empty();
    $("#valorFiltro").val('');
    $("#tituloHeader").empty();
    var d = new Date();
    var n = d.getTime();

    $("#tituloHeader").append('PAR-Q');
    $("body").removeClass('cadastrado');
    $("body").addClass('parQ');

    $.post("../prest/contratoassinatura" + montarParametros(codigoRespostaParqAlunoSelecionado)).done(function(data) {
        perguntasParQ = data.perguntasParQ;
        respostasClienteParQ = data.respostasClienteParQ;
        $("#perguntaParQ").remove();
        $("#perguntasParQ").append('<div id="perguntaParQ"></div>');
        for (var i = 0, numero = 1; i < data.perguntasParQ.length; i++, numero++) {
            $("#perguntaParQ").append(montarHtmlPerguntaParQ(data.perguntasParQ[i], numero, respostasClienteParQ));
        }
        $("body").addClass('assinarParQ');
        $("#checkTermoDeAceite").prop("checked", false);
        $("#texto_obrigatorio_termo").removeClass('texto_alert_termo');
        if (data.apresentarLeiParqRJ) {
            $('#leiParqRJ').show();
        } else {
            $('#leiParqRJ').hide();
        }

        if (data.apresentarLeiParqGO) {
            $('#leiParqGO').show();
        } else {
            $('#leiParqGO').hide();
        }
        construirCanvasParQ();
    });
    scrollTop();
}

function montarParametros(codigoRespostaParq) {
    var parametros = "?operacao=consultarPerguntasParQ&token=" + params.token + "&remoteIP=" + ip + "&filtro=" + '';
    if (empresa !== null) {
        parametros = parametros + "&empresa=" + empresa;
    }
    parametros = codigoRespostaParq != null
        ? parametros + "&codigoRespostaParq=" + codigoRespostaParq
        : parametros + "&codigoRespostaParq=0";

    return parametros;
}

function montarHtmlPerguntaParQ(pergunta, n, respostas) {
    var checkedSim =  "";
    var checkedNao = "";
    var observacao = "";
    if (respostas != null) {
        for (var i = 0; i < respostas.length; i++) {
            if (pergunta.id == respostas[i].codigoPergunta) {
                if (respostas[i].resposta == "SIM") {
                    checkedSim = "checked";
                } else {
                    checkedNao = "checked"
                }
                observacao = respostas[i].obs || "";
                break;
            }
        }
    }

    var resposta =
        '<div class="div-pergunta-parq">' +
        '<div class="texto-pergunta">' + n + ' - ' + pergunta.pergunta + '</div>' +
        '<div class="div-resposta-parq">'+
        '<div class="radio-btn-parq">' +
        '<label class="radio-btn-sim">' +
        '<input type="radio" class="radio" value="1" name="respostapergunta'+pergunta.ordem+'" ' + checkedSim + '/> Sim' +
        '</label>' +
        '<label>' +
        '<input type="radio" class="radio" value="2" name="respostapergunta'+pergunta.ordem+'" ' + checkedNao + '/> N�o' +
        '</label>' +
        '</div>' +
        '<div class="textarea-parq">' +
        '<div class="text-observacao">Observa��o</div>' +
        '<textarea rows="3" id="observacao-parq' + n + '">' + observacao + '</textarea>' +
        '</div>' +
        '</div>' +
        '</div>';
    return resposta;
}

function prosseguirFotoPerfil() {
    if (signaturePad.isEmpty()) {
        alert("Favor, informe a assinatura do documento.");
    } else {

        if(isPlanoPersonal === true){
            $("#headerAluno").empty();
            $("#headerAluno").append('TIRE UMA FOTO PARA O PERFIL DO PERSONAL NO ZW');
        } else {
            $("#headerAluno").empty();
            $("#headerAluno").append('TIRE UMA FOTO PARA O PERFIL DO ALUNO NO ZW');
        }

        imageAssinatura = signaturePad.toDataURL();
        imageAssinatura2 = (signaturePad2 ? signaturePad2.toDataURL() : null);
        assinando = true;
        nomeAluno = '';
        $('header').removeClass('pesquisando');
        $("#main").empty();
        $("body").removeClass('assinar');
        $("#valorFiltro").val('');
        $("#tituloHeader").empty();

        fotoAlterada = false;
        imageFotoAluno = null;

        $(".imageBox > canvas").remove();
        $("#tituloHeader").append('FOTO DO PERFIL');
        $("#idnomeparafoto").empty();
        $("#idnomeparafoto").append('');
        $("body").addClass('fotoPerfil');
        $("body").addClass('assinando');
        scrollTop();
        CanvasCrop = $.CanvasCrop({
            cropBox : ".imageBox",
            imgSrc : getBase64Image(document.getElementById('pseudoprofile')),
            limitOver : 2
        });
    }
}

function getBase64Image(img) {
    var canvas = document.createElement("canvas");
    canvas.width = img.width;
    canvas.height = img.height;
    canvas.getContext('2d').drawImage(img, 0, 0);
    return canvas.toDataURL();
}

function selecionarAlunoAtestado(item, nome) {
    codigoAluno = $(item).attr('codigo');
    codigoAcesso = $(item).attr('codacesso');
    codigoContrato = $(item).attr('contrato');
    nomeAluno = nome;
    $('header').removeClass('pesquisando');
    $("#main").empty();
    $("#valorFiltro").val('');
    $("#tituloHeader").empty();
    var d = new Date();
    var n = d.getTime();
    if (acao === 'foto') {

        assinando = false;
        if (editing === true) {
            endImageEditing();
        }

        fotoAlterada = false;
        imageFotoAluno = null;
        $("#tituloHeader").append('FOTO DO PERFIL');

        $("#idnomeparafoto").empty();
        $("#idnomeparafoto").append(nome);
        $("body").addClass('fotoPerfil');
        toDataUrl($(item).attr('urlFoto'), function (myBase64) {
            imageExists(($(item).attr('urlFoto')), function (exists) {
                if (exists) {
                    CanvasCrop = $.CanvasCrop({
                        cropBox: ".imageBox",
                        imgSrc: $(item).attr('urlFoto'),
                        limitOver: 2
                    });
                } else {
                    CanvasCrop = $.CanvasCrop({
                        cropBox: ".imageBox",
                        imgSrc: ("../fotos/fotoPadrao.jpg"),
                        limitOver: 2
                    });
                }
            });
        });
    } else  if (acao === 'foto-facial') {
        imageAssinatura = null;
        $("#tituloHeader").append('FOTO PARA REC. FACIAL');
        $("#idnomeparafacial").empty();
        $("#idnomeparafacial").append(nome);
        $("body").addClass('facial');
        limparFotos();
        iniciarCaptura();

    } else {
        imageAssinatura = null;
        $("#tituloHeader").append('ATESTADO');
        $("#idnomeparaatestado").empty();
        $("#idnomeparaatestado").append(nome);
        $("body").addClass('atestado');
    }
    scrollTop();
}

function imageExists(url, callback) {
    var img = new Image();
    img.onload = function () {
        callback(true);
    };
    img.onerror = function () {
        callback(false);
    };
    img.src = url;
}

function toDataUrl(url, callback) {
    var xhr = new XMLHttpRequest();
    xhr.onload = function() {
        var reader = new FileReader();
        reader.onloadend = function() {
            callback(reader.result);
        };
        reader.readAsDataURL(xhr.response);
    };
    xhr.open('GET', url);
    xhr.responseType = 'blob';
    xhr.send();
}

function selecionarPessoaVisualizar(item) {
    codigoPessoa = $(item).attr('pessoa');
    nomeAluno = $(item).attr('nome');
    $('header').removeClass('pesquisando');
    $("#main").empty();
    $("#valorFiltro").val('');
    $("#tituloHeader").empty();
    $("#tituloHeader").append('VISUALIZAR CART�O');
    var d = new Date();
    var n = d.getTime();
    updateAssinatura = 'false';
    updateAnexo1Cartao = false;
    limparAnexo1Cartao();
    limparTipoAnexo()
    $.post("../prest/contratoassinatura?operacao=visualizarCartaoVacina&token=" + params.token + "&remoteIP=" + ip + "&pessoa=" + codigoPessoa).done(function (data) {
        $("body").addClass('cartaoVisualizar');
        $("body").addClass('cadastrado');
        $("#idnomeparacartao1").empty();
        $("#idnomeparacartao1").append($(item).attr('nome'));
        imageAnexo1Cartao = data.dados.anexo1;
        setarTipoAnexoVisualizar(data.dados.tipoanexo);

        updateAnexo1Cartao = false;

        document.getElementById("previewCartao").src = data.dados.anexo1;
        scrollTop();
    });
}

function selecionarContratoVisualizar(item) {
    contratoSelecionado_item = item;
    $('header').removeClass('pesquisando');
    updateAssinatura = 'false';
    var contrato = $(item).attr('contrato');
    var parametros = "?operacao=visualizarAnexos&token=" + params.token + "&remoteIP=" + ip + "&contrato=" + contrato + "&path=" + window.location.href
    if(empresa != null){
        parametros = parametros + "&empresa=" + empresa
    }
    if (assinaturaCancelamento) {
        parametros = parametros + "&contratosCancelados=true";
    }
    codigoContrato = contrato;
    $.post("../prest/contratoassinatura" + parametros).done(function(data) {
        $("#tituloHeader").empty();
        $("#main").empty();
        $("#caixaValidarParq").empty();
        $("#valorFiltro").val('');
        $("#tituloHeader").append('VISUALIZAR');
        $("body").removeClass('documentos');
        $("body").addClass('validar');
        $("body").addClass('jaAssinado');
        permitirRemoverAssinatura = data.permissaoRemoverAssinatura;
        imageDocs = data.dados.documentos;
        imageEndereco = data.dados.endereco;
        imageAssinatura = data.dados.assinatura;
        imageAssinatura2 = data.dados.assinatura2;
        imageAtestado = data.dados.atestado;
        imageAnexo1 = data.dados.anexo1;
        imageAnexo2 = data.dados.anexo2;

        updateDoc = 'false';
        updateEnd = 'false';
        updateAte = 'false';
        updateAnexo1 = 'false';
        updateAnexo2 = 'false';

        document.getElementById("previewDocs").src = data.dados.documentos;
        document.getElementById("previewEndereco").src = data.dados.endereco;
        document.getElementById("previewAssinatura").src = data.dados.assinatura;
        document.getElementById("previewAtestado").src = data.dados.atestado;
        document.getElementById("previewAnexo1").src = data.dados.anexo1;
        document.getElementById("previewAnexo2").src = data.dados.anexo2;

        permitirApresentarAssinatura2 = data.permitirApresentarAssinatura2;
        if (permitirApresentarAssinatura2) {
            var previewAssinatura2 = document.getElementById("previewAssinatura2");
            if (previewAssinatura2 != null && previewAssinatura2 != undefined) {
                document.getElementById("previewAssinatura2").src = data.dados.assinatura2;
            }
        } else {
            var modalAssFinApresentar = document.getElementById("assFinApresentar");
            var modalAssFinAssinar = document.getElementById("assFinAssinar");
            modalAssFinApresentar.style.display = "none";
            modalAssFinAssinar.style.display = "none";
        }
        scrollTop();
    });
}

function selecionarContratoProdutoVisualizar(item) {
    contratoSelecionado_item = item;
    $('header').removeClass('pesquisando');
    updateAssinatura = 'false';
    var contrato = $(item).attr('contrato');
    var parametros = "?operacao=visualizarAnexosProdutos&token=" + params.token + "&contrato=" + contrato + "&path=" + window.location.href
    if(empresa != null){
        parametros = parametros + "&empresa=" + empresa
    }
    codigoContrato = contrato;
    $.post("../prest/contratoassinatura" + parametros).done(function(data) {
        $("#tituloHeader").empty();
        $("#main").empty();
        $("#caixaValidarParq").empty();
        $("#valorFiltro").val('');
        $("#tituloHeader").append('VISUALIZAR');
        $("body").removeClass('documentos');
        $("body").addClass('validar');
        $("body").addClass('jaAssinado');
        permitirRemoverAssinatura = data.permissaoRemoverAssinatura;
        imageDocs = data.dados.documentos;
        imageEndereco = data.dados.endereco;
        imageAssinatura = data.dados.assinatura;
        imageAssinatura2 = data.dados.assinatura2;
        imageAtestado = data.dados.atestado;
        imageAnexo1 = data.dados.anexo1;
        imageAnexo2 = data.dados.anexo2;

        updateDoc = 'false';
        updateEnd = 'false';
        updateAte = 'false';
        updateAnexo1 = 'false';
        updateAnexo2 = 'false';

        document.getElementById("previewDocs").src = data.dados.documentos;
        document.getElementById("previewEndereco").src = data.dados.endereco;
        document.getElementById("previewAssinatura").src = data.dados.assinatura;
        document.getElementById("previewAtestado").src = data.dados.atestado;
        document.getElementById("previewAnexo1").src = data.dados.anexo1;
        document.getElementById("previewAnexo2").src = data.dados.anexo2;

        permitirApresentarAssinatura2 = data.permitirApresentarAssinatura2;
        if (permitirApresentarAssinatura2) {
            var previewAssinatura2 = document.getElementById("previewAssinatura2");
            if (previewAssinatura2 != null && previewAssinatura2 != undefined) {
                document.getElementById("previewAssinatura2").src = data.dados.assinatura2;
            }
        } else {
            var modalAssFinApresentar = document.getElementById("assFinApresentar");
            var modalAssFinAssinar = document.getElementById("assFinAssinar");
            modalAssFinApresentar.style.display = "none";
            modalAssFinAssinar.style.display = "none";
        }
        scrollTop();
    });
}

function selecionarPlanoVisualizar(item) {
    contratoSelecionado_item = item;
    $('header').removeClass('pesquisando');
    updateAssinatura = 'false';
    var planoPersonal = $(item).attr('taxaPersonal');
    codigoContrato = planoPersonal;
    $.post("../prest/contratoassinatura?operacao=visualizarAnexosPersonal&token=" + params.token + "&remoteIP=" + ip + "&taxaPersonal=" + planoPersonal + "&path=" + window.location.href).done(function(data) {
        $("#tituloHeader").empty();
        $("#main").empty();
        $("#caixaValidarParq").empty();
        $("#valorFiltro").val('');
        $("#tituloHeader").append('VISUALIZAR');
        $("body").removeClass('documentos');
        $("body").addClass('validar');
        $("body").addClass('jaAssinado');

        imageDocs = data.dadosPersonal.documentosPersonal;
        imageEndereco = data.dadosPersonal.enderecoPersonal;
        imageAssinatura = data.dadosPersonal.assinaturaPersonal;
        imageAtestado = data.dadosPersonal.atestadoPersonal;
        imageAnexo1 = data.dadosPersonal.anexo1Personal;
        imageAnexo2 = data.dadosPersonal.anexo2Personal;

        updateDoc = 'false';
        updateEnd = 'false';
        updateAte = 'false';
        updateAnexo1 = 'false';
        updateAnexo2 = 'false';

        document.getElementById("previewDocs").src = data.dadosPersonal.documentosPersonal;
        document.getElementById("previewEndereco").src = data.dadosPersonal.enderecoPersonal;
        document.getElementById("previewAssinatura").src = data.dadosPersonal.assinaturaPersonal;
        document.getElementById("previewAtestado").src = data.dadosPersonal.atestadoPersonal;
        document.getElementById("previewAnexo1").src = data.dadosPersonal.anexo1Personal;
        document.getElementById("previewAnexo2").src = data.dadosPersonal.anexo2Personal;
        scrollTop();
    });
}

function visualizarAssinaturaTermoResponsabilidade(item) {

    fotoAlterada = false;
    $('header').removeClass('pesquisando');
    var d = new Date();
    var n = d.getTime();
    if (editing === true) {
        endImageEditing();
    }
    updateDoc = 'false';
    updateEnd = 'false';
    updateAte = 'false';
    updateAnexo1 = 'false';
    updateAnexo2 = 'false';
    document.getElementById('pseudoprofile').src = '../' + $(item).attr('urlFoto');
    matriculaAlunoSelecionado = $(item).attr('matricula');

    contratoSelecionado_item = item;
    updateAssinatura = 'false';
    matriculaAlunoSelecionado = $(item).attr('matricula');
    $.post("../prest/contratoassinatura?operacao=visualizarAssinaturaTermoResponsabilidade&token=" + params.token + "&remoteIP=" + ip + "&path=" + window.location.href + "&matricula=" + matriculaAlunoSelecionado).done(function(data) {
        $("#tituloHeader").empty();
        $("#main").empty();
        $("#valorFiltro").val('');

        codigoMatricula = data.matricula;
        texto = data.texto;
        var caixa = '<div class="caixacontrato" style="text-align: center;margin-left: 15em;margin-right: 15em;FONT-WEIGHT: 100;font-size: 1.3em;">' + data.texto + '</div>';
        $("#main").append(caixa);

        $("#tituloHeader").append('VISUALIZAR');
        $("body").removeClass('documentos');

        $("body").addClass('validarTermoResponsabilidade');
        $("body").addClass('jaAssinado');

        document.getElementById("previewAssinaturaTermoResponsabilidade").src = data.assinatura;

        scrollTop();
    });
}

function visualizarRespostasParQ(item) {
    $('header').removeClass('pesquisando');
    updateAssinatura = 'false';
    matriculaAlunoSelecionado = $(item).attr('matriculaAluno');
    codigoRespostaParqAlunoSelecionado = $(item).attr('codigoRespostaParq') == null || $(item).attr('codigoRespostaParq') == "undefined" ? 0 : $(item).attr('codigoRespostaParq');
    $('header').removeClass('pesquisando');
    $.post("../prest/contratoassinatura",
        {
            token: params.token,
            remoteIP: ip,
            operacao: "visualizarRespostasParQ",
            matriculaAluno: matriculaAlunoSelecionado,
            codigoRespostaParq: codigoRespostaParqAlunoSelecionado
        }
    ).done(function (data) {
        $("#tituloHeader").empty();
        $("#main").empty();
        $("#valorFiltro").val('');
        $("#tituloHeader").append('VISUALIZAR');
        $("body").removeClass('documentos');
        $("body").addClass('validar');
        $("body").addClass('jaAssinado');
        $("#divAssinaturaDigital").empty();
        permitirRemoverAssinatura = data.permiteRemoverAss;
        imageAssinatura = data.urlAssinatura;
        urlPdfParq = data.urlPdfRespostas;
        updateDoc = 'false';
        updateEnd = 'false';
        updateAte = 'false';
        updateAnexo1 = 'false';
        updateAnexo2 = 'false';
        document.getElementById("previewAssinaturaParq").src = data.urlAssinatura;
        scrollTop();
    });
}

function testaImagemNula(imagem) {
    if (imagem == null) {
        return imageTemp = '../imagens/image_icon.jpg';
    } else {
        return imagem;
    }
}

function prosseguirValidar() {
    if(acao === 'foto-facial') {
        enviarFotoFacial()
    } else if(acao === 'vacina'){
        enviarFotoCartaoVacina();
    } else if (acao === 'par-q') {
        salvarRespostasParQAluno();
    } else if (acao === 'termoresponsabilidade') {
        enviarAssinaturaTermoResponsabilidade()
    }else{
        var body = $("body");
        $("#tituloHeader").empty();
        $("#main").empty();
        $("#tituloHeader").append('VALIDA��O');
        body.removeClass('documentos');
        body.addClass('validar');

        document.getElementById("previewDocs").src = testaImagemNula(imageDocs);
        document.getElementById("previewEndereco").src = testaImagemNula(imageEndereco);
        document.getElementById("previewAtestado").src = testaImagemNula(imageAtestado);
        document.getElementById("previewAnexo1").src = testaImagemNula(imageAnexo1);
        document.getElementById("previewAnexo2").src = testaImagemNula(imageAnexo2);
        document.getElementById("previewAssinatura").src = testaImagemNula(imageAssinatura);
        if (permitirApresentarAssinatura2 && responsavelFinanceiroPreenchido) {
            document.getElementById("previewAssinatura2").src = testaImagemNula(imageAssinatura2);
        }
        scrollTop();
    }
}

function enviarAssinaturaTermoResponsabilidade() {
    var assinatura = replaceImageData64(signaturePad.toDataURL());
    $.post("../prest/contratoassinatura?operacao=salvarAssinaturaTermoResponsabilidade",
        {
            token: params.token,
            remoteIP: ip,
            operacao: "incluirCartaoVacina",
            matricula: codigoMatricula,
            assinatura: assinatura,
        }).done(function (data) {

    });
    $("body").removeClass();
    $("body").addClass('concluido');
    $("#main").empty();
    $("#msgConcluir").empty();
    $("#msgConcluir").append('TERMO RESPONSABILIDADE ASSINADO COM SUCESSO');
}

function enviarFotoCartaoVacina() {
    var checkboxes = document.getElementsByName('tipoanexo')
    var marcado = false;
    checkboxes.forEach((item) => {
        if (item.checked) {
            marcado = true;
            tipoCartaoVacinaSelecionado = item.value;
        }
    })
    if(marcado === false){
        $('#msgcartao').show();
        var msgCartao = $("#msgcartaotxt");
        msgCartao.empty();
        var msgApresentar = 'SELECIONE QUAL DOSE O CART�O APRESENTA';
        msgCartao.append(msgApresentar);
        $("#main").empty();
        return;
    }
    if(updateAnexo1Cartao === false){
        $('#msgcartao').show();
        var msgCartao = $("#msgcartaotxt");
        msgCartao.empty();
        var msgApresentar = 'NENHUM ARQUIVO FOI ANEXADO';
        msgCartao.append(msgApresentar);
        $("#main").empty();
        return;
    }

    var anexo1Cartao64 = replaceImageData64(imageAnexo1Cartao);
    $.post("../prest/contratoassinatura",
        {
            token: params.token,
            remoteIP: ip,
            operacao: "incluirCartaoVacina",
            pessoa: codigoPessoa,
            tipoanexo: tipoCartaoVacinaSelecionado,
            anexo1: anexo1Cartao64,
        }).done(function (data) {

    });
    $("body").removeClass();
    $("body").addClass('concluido');
    $("#main").empty();
    $("#msgConcluir").empty();
    $("#msgConcluir").append('CART�O CADASTRADO COM SUCESSO!');
}

function salvarRespostasParQAluno() {
    if ($("#checkTermoDeAceite").is(":checked")) {
        if (signaturePad.isEmpty()) {
            alert("Favor, informe a assinatura do documento.");
        } else {
            var jsonRespostasParQ = new Object();
            var listaRespostas = [];
            var respondeuTodasPerguntas = true;
            for (var i = 0, n = 1; i < perguntasParQ.length; i++, n++) {
                var respostaPergunta = new Object();
                var resposta = $('input[name="respostapergunta' + n + '"]:checked').val();
                if (resposta != undefined) {
                    respostaPergunta.codigo = perguntasParQ[i].id;
                    respostaPergunta.ordem = perguntasParQ[i].ordem;
                    respostaPergunta.resposta = resposta === undefined ? '2' : resposta;
                    respostaPergunta.observacao = $('#observacao-parq' + n).val();
                    listaRespostas.push(respostaPergunta);
                } else {
                    respondeuTodasPerguntas = false;
                    break;
                }
            }
            if (!respondeuTodasPerguntas) {
                alert("Para prosseguir � necess�rio que todas as perguntas sejam respondidas!");
            } else {
                jsonRespostasParQ.respostas = listaRespostas;
                jsonRespostasParQ.assinatura64 = replaceImageData64(signaturePad.toDataURL());
                jsonRespostasParQ.matriculaAluno = matriculaAlunoSelecionado;

                let codigoRPAS = Number(codigoRespostaParqAlunoSelecionado);
                if (isNaN(codigoRPAS)) {
                    codigoRPAS = 0;
                }
                if (codigoRPAS != null && codigoRPAS > 0) {
                    $.post("../prest/contratoassinatura",
                        {
                            token: params.token,
                            remoteIP: ip,
                            operacao: "salvarEdicaoAssinaturaParQ",
                            matriculaAluno: matriculaAlunoSelecionado,
                            codigoRespostaParqAlunoSelecionado: codigoRPAS,
                            data: JSON.stringify(jsonRespostasParQ),
                        }
                    ).done(function (data) {
                        $("body").removeClass();
                        $("body").addClass('concluido');
                        $("#main").empty();
                        $("#msgConcluir").empty();
                        $("#msgConcluir").append('FORMUL�RIO PAR-Q EDITADO COM SUCESSO!');
                    });
                } else {
                    $.post("../prest/contratoassinatura",
                        {
                            token: params.token,
                            remoteIP: ip,
                            operacao: "salvarAssinaturaParQ",
                            matriculaAluno: matriculaAlunoSelecionado,
                            data: JSON.stringify(jsonRespostasParQ),
                        }
                    ).done(function (data) {
                        $("body").removeClass();
                        $("body").addClass('concluido');
                        $("#main").empty();
                        $("#msgConcluir").empty();
                        $("#msgConcluir").append('FORMUL�RIO PAR-Q RESPONDIDO COM SUCESSO!');
                    });
                }
            }
        }
    } else {
        alert('Para prosseguir � necess�rio que concorde com o termo de responsabilidade!');
        $("#texto_obrigatorio_termo").addClass('texto_alert_termo');
    }
}

function prosseguirDocumentos() {
    if (isPlanoPersonal === true) {
        $.post("../prest/contratoassinatura?operacao=visualizarAnexosPersonal&token=" + params.token + "&remoteIP=" + ip + "&planoPersonal=" + codigoContrato).done(function (data) {
            if (data.hasOwnProperty('dadosPersonal')) {
                imageDocs = data.dadosPersonal.documentosPersonal;
                imageEndereco = data.dadosPersonal.enderecoPersonal;
                imageAtestado = data.dadosPersonal.atestadoPersonal;
                imageAnexo1 = data.dadosPersonal.anexo1Personal;
                imageAnexo2 = data.dadosPersonal.anexo2Personal;

                document.getElementById("uploadPreview").src = imageDocs;
                document.getElementById("uploadPreviewEND").src = imageEndereco;
                document.getElementById("uploadPreviewATESTADO").src = imageAtestado;
                document.getElementById("uploadPreviewAnexo1").src = imageAnexo1;
                document.getElementById("uploadPreviewAnexo2").src = imageAnexo2;
            } else {
                limparDocumentos();
                limparEndereco();
                limparAtestado();
                limparAnexo1();
                limparAnexo2();
            }
        });
    } else {
        $.post("../prest/contratoassinatura?operacao=visualizarAnexos&token=" + params.token + "&remoteIP=" + ip + "&contrato=" + codigoContrato).done(function (data) {
            if (data.hasOwnProperty('dados')) {
                imageDocs = data.dados.documentos;
                imageEndereco = data.dados.endereco;
                imageAtestado = data.dados.atestado;
                imageAnexo1 = data.dados.anexo1;
                imageAnexo2 = data.dados.anexo2;

                document.getElementById("uploadPreview").src = imageDocs;
                document.getElementById("uploadPreviewEND").src = imageEndereco;
                document.getElementById("uploadPreviewATESTADO").src = imageAtestado;
                document.getElementById("uploadPreviewAnexo1").src = imageAnexo1;
                document.getElementById("uploadPreviewAnexo2").src = imageAnexo2;
            } else {
                limparDocumentos();
                limparEndereco();
                limparAtestado();
                limparAnexo1();
                limparAnexo2();
            }
        });
    }

    $("#tituloHeader").empty();
    $("#main").empty();
    $("#tituloHeader").append('DOCUMENTOS');
    $("body").removeClass();
    $("body").addClass('documentos');
    scrollTop();
}

// obterContratos();

function PreviewImage(idUpLoad, idPreview, tipo, size) {
    var oFReader = new FileReader();
    resizeImage({
        file: document.getElementById(idUpLoad).files[0],
        maxSize: size
    }).then(function(resizedImage) {
        oFReader.readAsDataURL(resizedImage);
        oFReader.onload = function(oFREvent) {
            document.getElementById(idPreview).src = oFREvent.target.result;
            if (tipo === 'docs') {
                imageDocs = oFREvent.target.result;
                updateDoc = 'true';
            } else if (tipo === 'foto') {
                imageFotoAluno = oFREvent.target.result;
                fotoAlterada = 'true';
            } else if (tipo === 'endereco') {
                imageEndereco = oFREvent.target.result;
                updateEnd = 'true';
            } else if (tipo === 'atestado') {
                imageAtestado = oFREvent.target.result;
                updateAte = 'true';
            } else if (tipo === 'anexo1') {
                imageAnexo1 = oFREvent.target.result;
                updateAnexo1 = 'true';
            } else if (tipo === 'anexo2') {
                imageAnexo2 = oFREvent.target.result;
                updateAnexo2 = 'true';
            } else if(tipo === 'cartaovacina'){
                updateAnexo1Cartao = true;
                imageAnexo1Cartao = oFREvent.target.result;
            }
        };
    }). catch (function(err) {
        console.error(err);
    });
}

function ajustarOrientacao(imageBox, arquivo, imageFoto) {
    //TODO necess�rio ajustar para os documentos
    var rot = 0;
    getOrientation(arquivo, function (orientation) {
        switch (orientation) {
            case 6:
                var CanvasCrop = $.CanvasCrop({
                    cropBox: imageBox,
                    imgSrc: imageFoto,
                    limitOver: 2
                });

                rot += 90;
                rot = rot > 360 ? 90 : rot;
                CanvasCrop.rotate(rot);
                break;

            case 8:
                var CanvasCrop = $.CanvasCrop({
                    cropBox: imageBox,
                    imgSrc: imageFoto,
                    limitOver: 2
                });

                rot -= 90;
                rot = rot < 0 ? 270 : rot;
                CanvasCrop.rotate(rot);
                break;
        }
    });
}

function getOrientation(file, callback) {
    var reader = new FileReader();
    reader.onload = function(e) {

        var view = new DataView(e.target.result);
        if (view.getUint16(0, false) != 0xFFD8) return callback(-2);
        var length = view.byteLength, offset = 2;
        while (offset < length) {
            var marker = view.getUint16(offset, false);
            offset += 2;
            if (marker == 0xFFE1) {
                if (view.getUint32(offset += 2, false) != 0x45786966) return callback(-1);
                var little = view.getUint16(offset += 6, false) == 0x4949;
                offset += view.getUint32(offset + 4, little);
                var tags = view.getUint16(offset, little);
                offset += 2;
                for (var i = 0; i < tags; i++)
                    if (view.getUint16(offset + (i * 12), little) == 0x0112)
                        return callback(view.getUint16(offset + (i * 12) + 8, little));
            }
            else if ((marker & 0xFF00) != 0xFF00) break;
            else offset += view.getUint16(offset, false);
        }
        return callback(-1);
    };
    reader.readAsArrayBuffer(file);
}

function scrollTop() {
    document.body.scrollTop = document.documentElement.scrollTop = 0;
}

function subirFoto() {
    if (editing === true) {
        endImageEditing();
    }
    if (isPlanoPersonal === true) {
        if (fotoAlterada) {
            var src = CanvasCrop.getDataURL("jpeg");
            var imageFotoAluno64 = src.replace(/^data:image\/(png|jpg|jpeg);base64,/, "");
            $.post("../prest/contratoassinatura",
                {
                    token: params.token,
                    remoteIP: ip,
                    operacao: "alterarFotoPersonal",
                    codigoPersonal: codigoAluno,
                    planoPersonal: codigoContrato,
                    fotoPersonal: imageFotoAluno64
                }
            ).done(function (data) {
                if (assinando) {
                    prosseguirDocumentos();
                } else {
                    $("body").removeClass();
                    $("body").addClass('concluido');
                    $("#main").empty();
                    $("#msgConcluir").empty();
                    $("#msgConcluir").append('FOTO ALTERADA COM SUCESSO!');
                    imageFotoAluno = null;
                }
            });
        }
        else {
            if (assinando) {
                prosseguirDocumentos();
            } else {
                $("body").removeClass();
                $("body").addClass('concluido');
                $("#main").empty();
                $("#msgConcluir").empty();
                $("#msgConcluir").append('FOTO ALTERADA COM SUCESSO!');
            }
        }
    } else {
        if (fotoAlterada) {
            var src = CanvasCrop.getDataURL("jpeg");
            var imageFotoAluno64 = src.replace(/^data:image\/(png|jpg|jpeg);base64,/, "");
            $.post("../prest/contratoassinatura",
                {
                    token: params.token,
                    remoteIP: ip,
                    operacao: "alterarFotoAluno",
                    codigoaluno: codigoAluno,
                    contrato: codigoContrato,
                    fotoAluno: imageFotoAluno64
                }
            ).done(function (data) {
                if (assinando) {
                    prosseguirDocumentos();
                } else {
                    $("body").removeClass();
                    $("body").addClass('concluido');
                    $("#main").empty();
                    $("#msgConcluir").empty();
                    $("#msgConcluir").append('FOTO ALTERADA COM SUCESSO!');
                    imageFotoAluno = null;
                }
            });
        }
        else {
            if (assinando) {
                prosseguirDocumentos();
            } else {
                $("body").removeClass();
                $("body").addClass('concluido');
                $("#main").empty();
                $("#msgConcluir").empty();
                $("#msgConcluir").append('FOTO ALTERADA COM SUCESSO!');
            }
        }
    }
}

function limparDocumentos() {
    imageDocs = null;
    updateDoc = 'true';
    document.getElementById('uploadImage').value = '';
    document.getElementById('uploadPreview').src = '../imagens/image_icon.jpg';
}

function limparEndereco() {
    imageEndereco = null;
    updateEnd = 'true';
    document.getElementById('uploadImageEND').value = '';
    document.getElementById('uploadPreviewEND').src = '../imagens/image_icon.jpg';
}

function limparAtestado() {
    imageAtestado = null;
    updateAte = 'true';
    document.getElementById('uploadImageATESTADO').value = '';
    document.getElementById('uploadPreviewATESTADO').src = '../imagens/image_icon.jpg';
}

function limparAnexo1() {
    imageAnexo1 = null;
    updateAnexo1 = 'true';
    document.getElementById('uploadImageAnexo1').value = '';
    document.getElementById('uploadPreviewAnexo1').src = '../imagens/image_icon.jpg';
}

function limparAnexo1Cartao() {
    imageAnexo1Cartao = null;
    updateAnexo1Cartao = false;
    document.getElementById('uploadImageAnexo1Cartao').value = '';
    document.getElementById('uploadPreviewAnexo1Cartao').src = '../imagens/image_icon.jpg';
}

function limparAnexo2() {
    imageAnexo2 = null;
    updateAnexo2 = 'true';
    document.getElementById('uploadImageAnexo2').value = '';
    document.getElementById('uploadPreviewAnexo2').src = '../imagens/image_icon.jpg';
}

function editar() {

    var tituloHeader = $("#tituloHeader");
    var body = $("body");
    $("#main").empty();
    tituloHeader.empty();
    tituloHeader.append('DOCUMENTOS');
    body.removeClass();

    if(acao === 'termoresponsabilidade') {
        reenviarAssinatura = true;
        selecionarTermoResponsabilidade(this);
        return;
    }
    imageFotoAluno = null;

    document.getElementById("uploadPreview").src = imageDocs;
    document.getElementById("uploadPreviewEND").src = imageEndereco;
    document.getElementById("uploadPreviewATESTADO").src = imageAtestado;
    document.getElementById("uploadPreviewAnexo1").src = imageAnexo1;
    document.getElementById("uploadPreviewAnexo2").src = imageAnexo2;

    $("#main").empty();
    tituloHeader.empty();
    tituloHeader.append('DOCUMENTOS');
    body.removeClass();
    body.addClass('documentos');
    body.addClass('reenvio');
    scrollTop();
}

function replaceImageData64(image){
    try {
        return image.replace(/^data:image\/(png|jpg|jpeg);base64,/, "");
    }catch(e){
        return '';
    }
}

function validar() {
    var assinatura64 = replaceImageData64(imageAssinatura);
    var assinatura264 = replaceImageData64(imageAssinatura2);
    var endereco64 = replaceImageData64(imageEndereco);
    var docs64 = replaceImageData64(imageDocs);
    var atestado64 = replaceImageData64(imageAtestado);
    var anexo164 = replaceImageData64(imageAnexo1);
    var anexo264 = replaceImageData64(imageAnexo2);

    if (isPlanoPersonal === true) {
        $.post("../prest/contratoassinatura",
            {
                token: params.token,
                remoteIP: ip,
                operacao: "incluirAssinaturaPlanoPersonal",
                planoPesonalTextoPadrao: codigoModelo,
                planoPersonal: codigoContrato,
                documentosPersonal: docs64,
                atestadoPersonal: atestado64,
                assinaturaPersonal: assinatura64,
                enderecoPersonal: endereco64,
                anexo1Personal: anexo164,
                anexo2Personal: anexo264,
                updateAssinaturaPersonal: updateAssinatura,
                updateDocsPersonal: updateDoc,
                updateEndPersonal: updateEnd,
                updateAtePersonal: updateAte,
                updateAnexo1Personal: updateAnexo1,
                updateAnexo2Personal: updateAnexo2
            }).done(function (data) {
            concluido();
        });
    } else if (acao === 'contratoProduto') {
        $.post("../prest/contratoassinatura",
            {
                token: params.token,
                operacao: "incluirAssinaturaContratoProduto",
                contratoTextoPadrao: codigoModelo,
                contrato: codigoContrato,
                ip: ip,
                documentos: docs64,
                atestado: atestado64,
                assinatura: assinatura64,
                endereco: endereco64,
                anexo1: anexo164,
                anexo2: anexo264,
                updateAssinatura: updateAssinatura,
                updateDocs: updateDoc,
                updateEnd: updateEnd,
                updateAte: updateAte,
                updateAnexo1: updateAnexo1,
                updateAnexo2: updateAnexo2
            }).done(function (data) {
            concluido();
        });
    } else {
        $.post("../prest/contratoassinatura",
            {
                token: params.token,
                remoteIP: ip,
                operacao: "incluirAssinatura",
                contratoTextoPadrao: codigoModelo,
                contrato: codigoContrato,
                assinaturaCancelamento: assinaturaCancelamento,
                ip: ip,
                documentos: docs64,
                atestado: atestado64,
                assinatura: assinatura64,
                assinatura2: assinatura264,
                endereco: endereco64,
                anexo1: anexo164,
                anexo2: anexo264,
                updateAssinatura: updateAssinatura,
                updateDocs: updateDoc,
                updateEnd: updateEnd,
                updateAte: updateAte,
                updateAnexo1: updateAnexo1,
                updateAnexo2: updateAnexo2
            }).done(function (data) {
            concluido();
        });
    }
}

function abrirPreviewGrande(item) {
    $('.previewGrande').addClass('show');
    document.getElementById("idpreviewGrande").src = item.src;
}

function fecharPreviewGrande() {
    $('.previewGrande').removeClass('show');

}

var resizeImage = function(settings) {
    var file = settings.file;
    var maxSize = settings.maxSize;
    var reader = new FileReader();
    var image = new Image();
    var canvas = document.createElement('canvas');
    var dataURItoBlob = function(dataURI) {
        var bytes = dataURI.split(',')[0].indexOf('base64') >= 0 ?
            atob(dataURI.split(',')[1]) :
            unescape(dataURI.split(',')[1]);
        var mime = dataURI.split(',')[0].split(':')[1].split(';')[0];
        var max = bytes.length;
        var ia = new Uint8Array(max);
        for (var i = 0; i < max; i++)
            ia[i] = bytes.charCodeAt(i);
        return new Blob([ia], {type: mime});
    };
    var resize = function() {
        var width = image.width;
        var height = image.height;
        if (width > height) {
            if (width > maxSize) {
                height *= maxSize / width;
                width = maxSize;
            }
        }
        else {
            if (height > maxSize) {
                width *= maxSize / height;
                height = maxSize;
            }
        }
        canvas.width = width;
        canvas.height = height;
        canvas.getContext('2d').drawImage(image, 0, 0, width, height);
        var dataUrl = canvas.toDataURL('image/jpeg');
        return dataURItoBlob(dataUrl);
    };
    return new Promise(function(ok, no) {

        reader.onload = function(readerEvent) {
            image.onload = function() {
                return ok(resize());
            };
            image.src = readerEvent.target.result;
        };
        reader.readAsDataURL(file);
    });
};

function habilitarPesquisa() {
    $('header').addClass('pesquisando');
    $('#valorFiltro').focus();
}

function desabilitarPesquisa() {
    $('header').removeClass('pesquisando');
    $('#valorFiltro').val('');
    voltarInicio();
}
function desabilitarPesquisaCartao() {
    $('header').removeClass('pesquisando');
    $('#valorFiltro').val('');
    filtrarCartoesVacina('');
}
function desabilitarPesquisaPlanoPersonal() {
    $('header').removeClass('pesquisando');
    $('#valorFiltro').val('');
    filtrarPlanosPersonal('');
}

function desabilitarPesquisaParQ() {
    $('header').removeClass('pesquisando');
    $('#valorFiltro').val('');
    var filtroPesquisa = '';
    if (params.matricula && (params.matricula != null || params.matricula.trim() !== "")) {
        filtroPesquisa = params.matricula;
    }
    filtrarClientesParQ(filtroPesquisa);
}

var $menu = $('#painelmenu').hide();
var $caixamenu = $('.caixaMenu');
$('#idbtnmenu').click(function() {
    $menu.show();
    $caixamenu.animate({
        marginLeft: '-8px'
    }, 700, function() {

    });
});
$($caixamenu).click(function(event) {
    event.stopPropagation();
});

function closeMenu() {
    $caixamenu.animate({
        marginLeft: '-75%'
    }, 1000, function() {
        $menu.hide();
    });
}
$($menu).click(function() {
    closeMenu();
});
$('#valorFiltro').keyup(function() {
    delay(function() {
        if (acao === 'vacina') {
            $("#tituloHeader").empty();
            $("#tituloHeader").append('CART�ES DE VACINA');
            $("body").removeClass();
            montarCartoesVacina();
            filtrarCartoesVacina($('#valorFiltro').val());
        } else if (acao === 'contrato') {
            $("#tituloHeader").empty();
            $("#tituloHeader").append('CONTRATOS');
            $("body").removeClass();
            montarContratos();
            filtrarContratos($('#valorFiltro').val(), false);
        } else if (acao === 'contratoCancelado') {
            $("#tituloHeader").empty();
            $("#tituloHeader").append('CANCELAMENTO DE CONTRATOS');
            $("body").removeClass();
            montarContratosCancelados();
            filtrarContratos($('#valorFiltro').val(), true);
        } else if (acao === 'contratoProduto') {
            $("#tituloHeader").empty();
            $("#tituloHeader").append('CONTRATOS DE PRODUTOS');
            $("body").removeClass();
            montarContratoProdutos();
            filtrarContratoProdtutos($('#valorFiltro').val());
        } else if (acao == 'planoPersonal' || isPlanoPersonal) {
            $("#tituloHeader").empty();
            $("#tituloHeader").append('CONTRATOS PLANO PERSONAL');
            $("body").removeClass();
            montarContratos();
            filtrarPlanosPersonal($('#valorFiltro').val());
        } else if (acao === 'par-q') {
            $("#tituloHeader").empty();
            $("#tituloHeader").append('PAR-Q');
            $("body").removeClass();
            montarHtmlParQ();
            filtrarClientesParQ($('#valorFiltro').val());
        } else if (acao === 'termoresponsabilidade') {
            $("#tituloHeader").empty();
            $("#tituloHeader").append('TERMO RESPONSABILIDADE');
            $("body").removeClass();
            montarTermoResponsabilidade();
            filtrarTermoResponsabilidade($('#valorFiltro').val());
        } else {
            filtrarAlunosAtestado($('#valorFiltro').val());
        }

    }, 850);
});

var $loading = $('#loadingDiv').hide();
$(document).ajaxStart(function () {
    $loading.show();
}).ajaxStop(function () {
    $loading.hide();
});

function imgtoDataURL(img) {
    var canvas = document.createElement('CANVAS');
    var ctx = canvas.getContext('2d');
    var dataURL;
    canvas.height = img.naturalHeight;
    canvas.width = img.naturalWidth;
    ctx.drawImage(img, 0, 0);
    dataURL = canvas.toDataURL('jpeg');
    return dataURL;
}

function enviarFotoFacial() {
    if(!document.getElementById("fotofacial1").src && !document.getElementById("fotofacial2").src){
        $('#msgfacial').show();
        $('#msgFacialtxt').empty();
        $('#msgFacialtxt').append('� necess�rio informar as duas fotos!');
        return;
    }

    var img1 = imgtoDataURL(document.getElementById("fotofacial1"));
    var img2 = imgtoDataURL(document.getElementById("fotofacial2"));
    var json = {
        chave: params.token,
        codigo: codigoAcesso,
        imagem1: img1,
        imagem2: img2
    };
    $.ajax({
        type: 'POST',
        url: urlSocket + 'facial/gravar',
        data: JSON.stringify(json),
        success: function(data) { resultadoFacial(data); },
        error: function(XMLHttpRequest, textStatus, errorThrown) { erroFacial(errorThrown); },
        contentType: "application/json",
        dataType: 'json'
    });
}



function erroFacial(errorThrown){
    $('#msgfacial').show();
    $('#msgFacialtxt').empty();
    $('#msgFacialtxt').append("N�o foi poss�vel conectar ao servi�o.");
}
function resultadoFacial(data) {
    scrollTop();
    if (data.status === 'ok') {
        $("body").removeClass();
        $("body").addClass('concluido');
        $("#main").empty();
        $("#msgConcluir").empty();
        $("#msgConcluir").append(data.msg);
    } else {
        $('#msgfacial').show();
        $('#msgFacialtxt').empty();
        $('#msgFacialtxt').append(data.msg);
    }
}

function selecionarTipoAnexo(checkbox) {
    var checkboxes = document.getElementsByName('tipoanexo')
    checkboxes.forEach((item) => {
        if (item !== checkbox) {
            item.checked = false;

        } else {
            tipoCartaoVacinaSelecionado = item.value;
        }

    })
}

function limparTipoAnexo() {
    var checkboxes = document.getElementsByName('tipoanexo')
    checkboxes.forEach((item) => {
        item.checked = false
    })
}
function setarTipoAnexo(tipoanexo)  {
    var checkboxes = document.getElementsByName('tipoanexo')
    checkboxes.forEach((item) => {
        if(item.value === tipoanexo){
            item.checked = true;
        } else {
            item.checked = false;
        }
    })
}

function setarTipoAnexoVisualizar(tipoanexo)  {
    var checkboxes = document.getElementsByName('tipoanexo1')
    checkboxes.forEach((item) => {
        if(item.value == tipoanexo){
            item.checked = true;
        } else {
            item.checked = false;
        }
    })
}

function editarCartao() {

    $('header').removeClass('pesquisando');
    $("#main").empty();
    $("#valorFiltro").val('');
    $("#tituloHeader").empty();
    var d = new Date();
    var n = d.getTime();
    if (editing === true) {
        endImageEditing();
    }
    updateAnexo1Cartao = false;
    $("#tituloHeader").append('CART�O DE VACINA');
    limparAnexo1Cartao();
    limparTipoAnexo()
    $("body").removeClass('cartaoVisualizar');
    $("body").removeClass('cadastrado');
    $("body").addClass('cartaovacina');
    scrollTop();
}

function utilizaTermoResponsabilidade () {
    var parametros = "?operacao=validarUtilizaTermoResponsabilidade&token=" + params.token + "&remoteIP=" + ip;
    if (empresa !== null) {
        parametros = parametros + "&empresa=" + empresa;
    }
    $.post("../prest/contratoassinatura" + parametros)
        .done(function (data) {
            if (data.urlRedirect) {
                window.location = data.urlRedirect;
                return;
            }
            boolUtilizaTermoResponsabilidade = data.utilizaTermoResponsabilidade.termoresponsabilidade === 'true';
            boolTermoResponsabilidadeEstaAtivo = data.utilizaTermoResponsabilidade.situacao == 'AT';
            validarinicio();
        });
}

function redirecionarParaAssinaturaDoContrato() {
    if (params.contrato != null){
        montarContratos()
        filtrarContratos(params.contrato, false)
        $("#tituloHeader").empty();
        $("#tituloHeader").append(params.contrato);
    }
}

function verificaConfigSescEAtualizaMensagemParq () {
    var parametros = "?operacao=verificaConfigSescEAtualizaMensagemParq&token=" + params.token + "&remoteIP=" + ip;
    if (empresa !== null) {
        parametros = parametros + "&empresa=" + empresa;
    }
    $.post("../prest/contratoassinatura" + parametros)
        .done(function (data) {
            boolConfigSesc = data.verificaConfigSescEAtualizaMensagemParq;
            if (boolConfigSesc) {
                const mensagemParqConfigSescHabilitada = "Voc� est� assumindo a responsabilidade e o compromisso de apresentar algum documento oficial liberando-o para a pr�tica de atividades f�sicas presencialmente na unidade,\n" +
                    "                        por ter respondido \"sim\" a uma ou mais perguntas do Question�rio de Prontid�o para Atividade F�sica (PAR-q).\n" +
                    "                        Assumo plena responsabilidade por qualquer atividade f�sica praticada sem o atendimento a essa recomenda��o."
                $("#texto_obrigatorio_termo").text(mensagemParqConfigSescHabilitada);
            } else {
                const mensagemParq = "Estou ciente de que � recomend�vel conversar com um m�dico antes de aumentar meu n�vel atual de atividade f�sica, por ter respondido \"sim\" a uma ou mais perguntas do Question�rio de Prontid�o para Atividade F�sica (PAR-Q)." +
                    " Assumo plena responsabilidade por qualquer atividade f�sica praticada sem o atendimento a essa recomenda��o."
                $("#texto_obrigatorio_termo").text(mensagemParq);
            }
        });
}

function validarinicio(){
    var divPrincipal = document.getElementById('termoResponsabilidadePrincipal');
    var divMenuLateral = document.getElementById('termoResponsabilidadeMenuLateral');
    if(!boolUtilizaTermoResponsabilidade) {

        divPrincipal.remove();
        divMenuLateral.remove();
    } else {
        divMenuLateral.append();
        divPrincipal.append();
    }
    if(params.tela === 'vacina'){
        adicionarUmCartaoVacina();
    } else if (params.tela === 'par-q') {
        assinarParQ();
    } else {
        params.tela = 'vazio';
    }

    redirecionarParaAssinaturaDoContrato();

}

function downloadPdfParq() {
    window.open(urlPdfParq, '_blank');
}

function abrirModal(operacao) {
    if (permitirRemoverAssinatura) {
        if (acao === 'contratoProduto') {
            const modal = document.getElementById("modalAssinaturaProduto");
            modal.style.display = "block";
        } else if (operacao === 'assinaturaEletronica') {
            const modal = document.getElementById("modalRemoverAssinaturaEletronica");
            modal.style.display = "block";
        } else if (operacao === 'assinaturaDigital') {
            const modal = document.getElementById("modalAssinatura");
            modal.style.display = "block";
        } else {
            const modal = document.getElementById("modalAssinatura2");
            modal.style.display = "block";
        }

    } else {
        const modal = document.getElementById("permissaoNegadaRemoverAssinatura");
        modal.style.display = "block";
    }

}

function abrirModalParq(){
    if(permitirRemoverAssinatura){
        const modal = document.getElementById("modalAssinaturaParq");
        modal.style.display = "block";
    } else {
        const modal = document.getElementById("permissaoNegadaRemoverAssinaturaParq");
        modal.style.display = "block";
    }

}

function fecharModalParq(){
    if(permitirRemoverAssinatura){
        const modal = document.getElementById("modalAssinaturaParq");
        modal.style.display = "none";
    } else {
        const modal = document.getElementById("permissaoNegadaRemoverAssinaturaParq");
        modal.style.display = "none";
    }
}

function editarAssinaturaParq(item) {
    fecharModalParq();
    $("body").removeClass();
    $("#main").empty();
    selecionarPessoaEditarParQ(matriculaAlunoSelecionado);
}

function abrirModalAssinaturaEletronica(assinadoem, cpf, ipassinaturacontrato, emailrecebimento, contrato){
    codigoContrato = contrato;
    document.getElementById("modalAssinaturaEletronica").style.display = "block";
    document.getElementById("labelAssinadoEm").innerHTML =assinadoem;
    document.getElementById("labelAssinadoIp").innerHTML =ipassinaturacontrato;
    document.getElementById("labelAssinadoCpf").innerHTML =cpf;
    document.getElementById("labelAssinadoEmail").innerHTML =emailrecebimento;
}

function fecharModalAssinaturaEletronica(){
    document.getElementById("modalAssinaturaEletronica").style.display = "none";
}

function fecharModal(){
    if(permitirRemoverAssinatura){
        const modal = document.getElementById("modalAssinatura");
        const modalAss2 = document.getElementById("modalAssinatura2");
        const modal2 = document.getElementById("modalRemoverAssinaturaEletronica");
        const modal3 = document.getElementById("modalAssinaturaProduto");
        modal.style.display = "none";
        modalAss2.style.display = "none";
        modal2.style.display = "none";
        modal3.style.display = "none";
    } else {
        const modal = document.getElementById("permissaoNegadaRemoverAssinatura");
        modal.style.display = "none";
    }
}

function removerAssinatura(operacao, assinatura2) {
    var urlOperacao;
    if(operacao === 'assinaturaEletronica'){
        urlOperacao = "../prest/contratoassinatura?operacao=removerAssinaturaEletronica&token=";
    }else{
        urlOperacao = "../prest/contratoassinatura?operacao=removerAssinaturaTermoResponsabilidade&token=";
    }
    var parametros = "&contrato=" + codigoContrato + "&remoteIP=" + ip ;

    if (assinaturaCancelamento) {
        parametros = parametros + "&contratoCancelado=true";
    }
    if (assinatura2) {
        parametros = parametros + "&assinatura2=true";
    }
    $.post(urlOperacao + params.token + parametros).done(function(data) {
        fecharModal();
        $("body").removeClass();
        $("body").addClass('concluido');
        $("#main").empty();
        $("#msgConcluir").empty();
        $("#msgConcluir").append('ASSINATURA REMOVIDA COM SUCESSO!');
    });
}

function removerAssinaturaProduto() {
    var urlOperacao = "../prest/contratoassinatura?operacao=removerAssinaturaProduto&token=";
    var parametros = "&contrato=" + codigoContrato;

    $.post(urlOperacao + params.token + parametros).done(function(data) {
        fecharModal();
        $("body").removeClass();
        $("body").addClass('concluido');
        $("#main").empty();
        $("#msgConcluir").empty();
        $("#msgConcluir").append('ASSINATURA REMOVIDA COM SUCESSO!');
    });
}

window.onclick = function (event) {
    if (event.target == "modalAssinatura") {
        modal.style.display = "none";
    }
};
