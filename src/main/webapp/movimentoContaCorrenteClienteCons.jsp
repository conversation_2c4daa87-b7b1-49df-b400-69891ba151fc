<%@page contentType="text/html;charset=UTF-8" %>
<head><%@include file="/includes/include_import_minifiles.jsp" %></head>
<link href="./css/otimize.css" rel="stylesheet" type="text/css">
<script type="text/javascript" language="javascript" src="hoverform.js"></script>

<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax"%>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich"%>
<%@taglib prefix="f" uri="http://java.sun.com/jsf/core"%>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html"%>
<%@taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>

<f:loadBundle var="msg_bt" basename="propriedades.Botoes"/>
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao"/>
<f:loadBundle var="msg" basename="propriedades.Mensagens"/>

<meta name="viewport" content="width=device-width, initial-scale=1.0">
<link href="beta/css/pure-min.css" type="text/css" rel="stylesheet"/>
<link href="beta/css/bootstrap-tabs.css" type="text/css" rel="stylesheet"/>
<link href="beta/css/font-awesome_2.0.min.css" type="text/css" rel="stylesheet"/>
<link href="beta/css/pure-ext.css" type="text/css" rel="stylesheet"/>
<script src="beta/js/jquery.js" type="text/javascript"></script>
<script src="beta/js/jquery.dataTables.min.js" type="text/javascript"></script>
<script src="beta/js/DT_bootstrap.js" type="text/javascript"></script>
<script src="beta/js/bootstrap-tab.js" type="text/javascript"></script>
<script>
    jQuery.noConflict();
    setTimeout(function () {
        setDocumentCookie('popupsImportante', '', 1);
    }, 500);
</script>
<style>
    .tabelaMovContaCorrenteCliente tbody tr td:nth-child(8),
    .tabelaMovContaCorrenteCliente tbody tr td:nth-child(7){
        text-align: right;
    }
</style>
<%@include file="/includes/verificaModulo.jsp" %>
<c:set var="contexto" value="${pageContext.request.contextPath}" scope="request" />

<f:view>
    <jsp:include page="includes/include_carregando_ripple.jsp" flush="true"/>
    <title>${msg_aplic.prt_MovimentoContaCorrenteCliente_tituloForm}</title>

    <%-- INICIO HEADER --%>
    <c:set var="titulo" scope="session" value="${msg_aplic.prt_MovimentoContaCorrenteCliente_tituloForm}"/>
    <c:set var="urlWiki" scope="session" value="${SuperControle.urlBaseConhecimento}como-lancar-credito-ou-debito-na-conta-corrente-do-aluno/"/>

    <f:facet name="header">
        <jsp:include page="topoReduzido_material.jsp"/>
    </f:facet>

    <%-- FIM HEADER --%>

    <h:panelGroup layout="block" styleClass="pure-g-r">
        <h:form id="form" styleClass="pure-form pure-u-1">
            <a4j:keepAlive beanName="ExportadorListaControle"/>
            <a4j:keepAlive beanName="AjusteSaldoContaCorrenteControle"/>
            <input type="hidden" value="${modulo}" name="modulo"/>
            <input id="chavePrimaria" type="hidden" value="" name="chavePrimaria"/>

            <%-- INICIO CONTENT --%>
            <h:panelGroup>

                <%-- INICIO COMANDOS --%>
                <h:panelGroup layout="block" styleClass="pure-g-r pure-u-12-12 margin-0-auto titulo-topo">
                    <h:panelGroup layout="block" styleClass="pure-form pure-form-aligned pure-u-2-3"></h:panelGroup>
                    <h:panelGroup layout="block" styleClass="pure-u-1-3 text-right">
                        <h:panelGroup layout="block" styleClass="controles">
                            <a4j:commandLink id="btnExcel"
                                             styleClass="exportadores"
                                             actionListener="#{MovimentoContaCorrenteClienteControle.exportar}"
                                             oncomplete="#{MovimentoContaCorrenteClienteControle.mensagemNotificar}#{MovimentoContaCorrenteClienteControle.oncomplete}"
                                             accesskey="3">
                                <f:attribute name="tipo" value="xls"/>
                                <f:attribute name="atributos"
                                             value="codigo=Código,pessoa_Apresentar=Cliente,descricao=Descrição,responsavel_Apresentar=Responsável,dataHoraRegistro=Data,tipoMovimentacao=Tipo,valor_Apresentar=Valor,saldoAtual_Apresentar=Saldo Atual"/>
                                <f:attribute name="prefixo" value="MovimentoContaCorrenteCliente"/>
                                <a4j:actionparam noEscape="true" name="paramsTableFiltrada" value="retornaDadosDT()"/>
                                <h:outputText title="Exportar para o formato Excel" styleClass="btn-print-2 excel"/>
                            </a4j:commandLink>

                            <a4j:commandLink id="btnPDF"
                                             styleClass="exportadores margin-h-10"
                                             actionListener="#{MovimentoContaCorrenteClienteControle.exportar}"
                                             oncomplete="#{MovimentoContaCorrenteClienteControle.mensagemNotificar}#{MovimentoContaCorrenteClienteControle.oncomplete}"
                                             accesskey="4">
                                <f:attribute name="tipo" value="pdf"/>
                                <f:attribute name="atributos"
                                             value="codigo=Código,pessoa_Apresentar=Cliente,descricao=Descrição,responsavel_Apresentar=Responsável,dataHoraRegistro=Data,tipoMovimentacao=Tipo,valor_Apresentar=Valor,saldoAtual_Apresentar=Saldo Atual"/>
                                <f:attribute name="prefixo" value="MovimentoContaCorrenteCliente"/>
                                <f:attribute name="titulo" value="Movimento de Conta Corrente do Cliente"/>
                                <a4j:actionparam noEscape="true" name="paramsTableFiltrada" value="retornaDadosDT()"/>
                                <h:outputText title="Exportar para o formato PDF" styleClass="btn-print-2 pdf"/>
                            </a4j:commandLink>

                            <c:if test="${modulo eq 'centralEventos'}">
                                <a4j:commandLink id="btnLog"
                                                 styleClass="pure-button pure-button-small margin-h-10"
                                                 reRender="formLog"
                                                 actionListener="#{LogControle.entidadeListener}"
                                                 oncomplete="Richfaces.showModalPanel('panelMasterLog');">
                                    <f:attribute name="nomeEntidade" value="MOVIMENTOCCC"/>
                                    <f:attribute name="funcao" value="118"/>

                                    <i class="fa-icon-list"></i>
                                </a4j:commandLink>
                            </c:if>

                            <a4j:commandLink id="btnNovo"
                                             styleClass="pure-button pure-button-primary"
                                             action="#{MovimentoContaCorrenteClienteControle.novo}"
                                             accesskey="1">
                                &nbsp ${msg_bt.btn_cadastrar_novo}
                            </a4j:commandLink>
                        </h:panelGroup>
                    </h:panelGroup>
                </h:panelGroup>
                <%-- FIM COMANDOS --%>

                <table id="tblMovContaCorrenteCliente" class="tabelaMovContaCorrenteCliente pure-g-r pure-u-11-12 margin-0-auto">
                    <thead>
                    <th>${msg_aplic.prt_Cadastro_label_codigo_maiusculo}</th>
                    <th style="width: 25%">${msg_aplic.prt_Cadastro_label_cliente_maiusculo}</th>
                    <th style="width: 20%">${msg_aplic.prt_Cadastro_label_descricao_maiusculo}</th>
                    <th>${msg_aplic.prt_Cadastro_label_responsavel_maiusculo}</th>
                    <th>${msg_aplic.prt_Cadastro_label_data_maiusculo}</th>
                    <th>${msg_aplic.prt_Cadastro_label_tipo_maiusculo}</th>
                    <th style="text-align: right">${msg_aplic.prt_Cadastro_label_valor_maiusculo}</th>
                    <th style="text-align: right">${msg_aplic.prt_Cadastro_label_saldoAtual_maiusculo}</th>
                </thead>
                <tbody></tbody>
            </table>

        </h:panelGroup>
        <%-- FIM CONTENT --%>
        <br/>
        <center>
            <a4j:commandLink reRender="modalHistoricoAjustes" action="#{AjusteSaldoContaCorrenteControle.consultarHistorico}"
                             oncomplete="#{AjusteSaldoContaCorrenteControle.msgAlert}">
                <h:outputText value="Recibos de devoluções em ajustes de saldo" style="font-size: 12;"/>
            </a4j:commandLink>
        </center>


        <%-- INICIO FOOTER --%>
        <h:panelGroup id="mensagem" layout="block" styleClass="pure-u-11-12 margin-0-auto">
            <h:graphicImage id="iconSucesso" rendered="#{MovimentoContaCorrenteClienteControle.sucesso}" value="./imagens/sucesso.png" />
            <h:graphicImage id="iconErro" rendered="#{MovimentoContaCorrenteClienteControle.erro}" value="./imagens/erro.png" />

            <h:outputText styleClass="mensagem" rendered="#{not empty MovimentoContaCorrenteClienteControle.mensagem}" value=" #{MovimentoContaCorrenteClienteControle.mensagem}"/>
            <h:outputText styleClass="mensagemDetalhada" rendered="#{not empty MovimentoContaCorrenteClienteControle.mensagemDetalhada}" value=" #{MovimentoContaCorrenteClienteControle.mensagemDetalhada}"/>
        </h:panelGroup>
        <%-- FIM FOOTER --%>
    </h:form>

    <rich:modalPanel id="modalHistoricoAjustes"  width="400" height="200"  shadowOpacity="true">
            <f:facet name="header">
                <h:panelGroup>
                    <h:outputText value="Recibos de devoluções em ajuste de saldo"/>
                </h:panelGroup>
            </f:facet>
            <f:facet name="controls">
                <h:panelGroup>
                    <h:graphicImage value="/imagens/close.png" style="cursor:pointer" id="hidelinkmodalConfirmaEstorno"/>
                    <rich:componentControl for="modalHistoricoAjustes" attachTo="hidelinkmodalConfirmaEstorno" operation="hide"  event="onclick" />
                </h:panelGroup>
            </f:facet>
        <a4j:form id="formHistoricoAjustes">
            <h:panelGroup>


            <rich:dataTable value="#{AjusteSaldoContaCorrenteControle.recibos}" var="recibo" width="100%" 
                            id="devolucoes" columnClasses="centralizado, direita" rows="5">
                <rich:column>
                    <f:facet name="header">
                        <h:outputText value="Data de devolução"/>
                    </f:facet>
                     <a4j:commandLink action="#{AjusteSaldoContaCorrenteControle.imprimirReciboDevolucaoLista}"
                             oncomplete="#{AjusteSaldoContaCorrenteControle.msgAlert}">
                        <h:outputText value="#{recibo.dataFormatada}"/>
                    </a4j:commandLink>
                </rich:column>
                <rich:column>
                    <f:facet name="header">
                        <h:outputText value="Valor"/>
                    </f:facet>
                    <a4j:commandLink action="#{AjusteSaldoContaCorrenteControle.imprimirReciboDevolucaoLista}"
                             oncomplete="#{AjusteSaldoContaCorrenteControle.msgAlert}">
                        <h:outputText value="#{recibo.valorMonetario}"/>
                    </a4j:commandLink>
                </rich:column>
                
            </rich:dataTable>
            <rich:datascroller for="devolucoes"/>
            </h:panelGroup>
        </a4j:form>

    </rich:modalPanel>


    <%@include file="/pages/ce/includes/include_modal_exibeLogEntidade.jsp" %>
</h:panelGroup>

<script src="beta/js/ext-funcs.js" type="text/javascript"></script>

    <script type="text/javascript">
        jQuery(window).on("load", function () {
            iniciarTabela("tabelaMovContaCorrenteCliente", "${contexto}/prest/financeiro/movimentoCCC?codPessoa=${MovimentoContaCorrenteClienteControle.movimentoContaCorrenteClienteVO.pessoa.codigo}&moeda=${MovimentoContaCorrenteClienteControle.empresaLogado.moeda}", 0, "desc", "", false);
        });
    </script>

</f:view>
