<%@ taglib prefix="h" uri="http://java.sun.com/jsf/html" %>
<%@include file="../includes/imports.jsp" %>

<style>
    .comboBandeirasCartao label {
        margin-left: 14px;
    }

    .comboBandeirasCartao input {
        position: absolute;
        margin-top: 10px;
    }

    .comboBandeirasCartao img {
        width: 50px;
    }

    .inputCartao input {
        margin-right: 10px;
    }

    .nomeAlunoClick {
        font-family: Arial;
        font-style: normal;
        font-weight: normal;
        font-size: 14px;
        line-height: 16px;
    }

    .nrParcelasCartao {
        font-family: Arial, Helvetica, sans-serif;
        font-size: 11px;
        text-decoration: none;
        line-height: normal;
        font-weight: bold;
        text-transform: none;
        color: #005bab;
    }

    .autorizacaoSelecionar {
        text-align: center;
    }

    .grid {
        background: #FAFAFA;
    }

    .header {
        font-size: 16px;
        line-height: 20px;
        font-family: Arial;
        color: #383B3E;
        padding: 24px 0px 0px 14px;
    }

    .cartao {
        border: 1px solid #D3D5D7;
        box-sizing: border-box;
        border-radius: 10px;
        width: 240px;
        margin: 24px 0px 0px 14px;
    }

    .cartao .titleWhite {
        font-family: Arial;
        font-style: normal;
        font-weight: bold;
        font-size: 12px;
        line-height: 18px;
        color: #E7E7E7;
    }

    .cartao .titleGrey {
        font-family: Arial;
        font-style: normal;
        font-weight: bold;
        font-size: 12px;
        line-height: 18px;
        color: #6F747B;
    }

    .white {
        background: #FFFFFF;
        border-radius: 10px;
    }

    .gradient {
        background: radial-gradient(98.33% 251.73% at 96.88% 64%, #4AB5E3 2.89%, #1B9FFD 45.13%, #0078D0 72.63%, #005A93 100%);
    }

    .cartaoRodaPe {
        background: #013E6F !important;
        border-radius: 0px 0px 10px 10px;
    }

    .group {
        display: flex;
    }

    .sub-group {
        float: left;
        margin: 11px;
    }

    .title {
        font-family: Arial;
        font-style: normal;
        font-weight: normal;
        font-size: 14px;
        line-height: 16px;
        color: #6F747B;
    }

    .circleQuestion {
        width: 21px;
        height: 21px;
        border-radius: 100%;
        background: #D3D5D7;
        border: 1px solid #D3D5D7;
        color: #0078D0;
        display: flex;
        margin: 8px;
    }

</style>

<rich:modalPanel domElementAttachment="parent" id="modalTrocarCartaoContratoCliente"
                 styleClass="novaModal"
                 autosized="true" width="640" height="200" shadowOpacity="true">
    <f:facet name="header">
        <h:panelGroup>
            <h:outputText value="Trocar cart�o recorr�ncia"/>
        </h:panelGroup>
    </f:facet>
    <f:facet name="controls">
        <h:panelGroup>
            <h:graphicImage styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign"
                            style="cursor:pointer" id="hidelinkTrocarCartao"/>
            <rich:componentControl for="modalTrocarCartaoContratoCliente" attachTo="hidelinkTrocarCartao"
                                   operation="hide" event="onclick"/>
        </h:panelGroup>
    </f:facet>

    <h:panelGrid columns="1" width="100%">

        <rich:panel>
            <h:panelGroup id="panelBotaoConfirmarTroca">

                <a4j:form id="formTrocaCartao">
                    <h:panelGroup id="topoConteudo" style="display: block">
                        <h:outputText value="Dados Atuais"
                                      style="font-weight: bold; font-size: 14px; line-height: 20px; color: #383B3E;"/>
                    </h:panelGroup>

                    <h:panelGroup style="padding-top: 7px; display: grid; grid-template-columns: 1fr 1.3fr 1fr">
                        <div>
                            <h:outputText style="display: block"
                                          styleClass="title"
                                          value="N�mero do cart�o:"/>
                            <h:outputText
                                    rendered="#{!empty TrocarCartaoRecorrenciaControle.contratoRecorrencia.numeroCartao}"
                                    styleClass="title"
                                    style="display: block; margin-top: 8px"
                                    value="#{TrocarCartaoRecorrenciaControle.contratoRecorrencia.numeroCartao}"/>
                            <h:outputText
                                    rendered="#{empty TrocarCartaoRecorrenciaControle.contratoRecorrencia.numeroCartao}"
                                    styleClass="title"
                                    style="display: block; margin-top: 8px"
                                    value="N�o foi poss�vel obter o cart�o do contrato"/>
                        </div>
                        <div>
                            <h:outputText style="display: block"
                                          styleClass="title"
                                          value="Plano:"/>
                            <h:outputText style="display: block; margin-top: 8px"
                                          styleClass="title"
                                          value="#{TrocarCartaoRecorrenciaControle.plano.descricao}"/>
                        </div>
                        <div>
                            <h:outputText style="display: block;"
                                          styleClass="title"
                                          value="Dia do vencimento:"/>
                            <h:outputText style="display: block; margin-top: 8px"
                                          styleClass="title"
                                          value="#{TrocarCartaoRecorrenciaControle.contratoRecorrencia.diaVencimentoCartao}"/>
                        </div>
                    </h:panelGroup>
                    <!-- ------------------------ INICIO - CONFIGURACOES DO PAGAMENTO -------------------------------- -->
                    <h:panelGroup id="panelConteudo">
                        <h:panelGroup>

                            <c:if test="${!PagamentoCartaoCreditoControle.dadosPagamento.usarIdVindiCobranca}">

                                <h:panelGroup style="display: block; padding-top: 20px">
                                    <h:outputText value="Dados do novo cart�o de cr�dito"
                                                  style="font-weight: bold; font-size: 14px; line-height: 20px; color: #383B3E;"/>
                                </h:panelGroup>
                                <div style="display: grid; margin-left: -10px; padding-top: 5px; grid-template-columns: 3fr 2.4fr">

                                    <div>
                                        <div class="group">

                                            <!-- CONV�NIO DE COBRAN�A-->
                                            <div class="sub-group">
                                                <h:outputText styleClass="title" value="Conv�nio de cobran�a"/>
                                                </br>
                                                <h:panelGroup layout="block" styleClass="block cb-container"
                                                              style="margin-top: 5px;">
                                                    <h:selectOneMenu id="comboTipoTransacao"
                                                                     disabled="#{PagamentoCartaoCreditoControle.dadosPagamento.usarIdVindiCobranca or PagamentoCartaoCreditoControle.dadosPagamento.usarTokenCieloCobranca}"
                                                                     value="#{PagamentoCartaoCreditoControle.convenioCobrancaSelecionado}"
                                                                     onblur="blurinput(this);"
                                                                     onfocus="focusinput(this);"
                                                                     styleClass="form" style="padding-top: 0;">
                                                        <f:selectItems
                                                                value="#{PagamentoCartaoCreditoControle.convenioCobrancaSelectItem}"/>
                                                        <a4j:support event="onchange" reRender="panelConteudo"
                                                                     action="#{PagamentoCartaoCreditoControle.acaoMudarConvenioCobranca}"
                                                                     oncomplete="adicionarPlaceHolderValidadeCartao()"/>
                                                    </h:selectOneMenu>
                                                </h:panelGroup>
                                            </div>
                                        </div>

                                        <!-- NOME DO TITULAR -->
                                        <div class="group">
                                            <div class="sub-group">
                                                <h:outputText styleClass="title" value="Nome titular do cart�o"/>
                                                </br>
                                                <h:inputText id="nomeTitular"
                                                             value="#{PagamentoCartaoCreditoControle.dadosPagamento.nomeTitular}"
                                                             size="50" styleClass="form"
                                                             style="text-transform: uppercase; margin-top: 10px; width: 88%"
                                                             onkeyup="tabAutom(this)"
                                                             onkeypress="return permiteSomenteLetra(this.form, this.id, event);"
                                                             tabindex="1" onfocus="focusinput(this);"
                                                             onblur="blurinput(this);"
                                                             maxlength="50">
                                                    <a4j:support event="onchange"
                                                                 reRender="nomeTitularCartao, cardCartao, panelConteudo"
                                                                 action="#{PagamentoCartaoCreditoControle.inicializarCard}"
                                                                 oncomplete="document.getElementById('formTrocaCartao:nrCartao').focus()"/>
                                                </h:inputText>
                                            </div>
                                        </div>

                                        <!-- N�MERO DO CART�O -->
                                        <div class="group">
                                            <div class="sub-group">
                                                <h:outputText styleClass="title" value="N�mero do cart�o"/>
                                                </br>
                                                <h:inputText id="nrCartao"
                                                             value="#{PagamentoCartaoCreditoControle.dadosPagamento.numero}"
                                                             styleClass="form"
                                                             style="margin-top: 5px; width: 88%"
                                                             maxlength="19"
                                                             tabindex="2"
                                                             onkeypress="mascaraCartaoCreditoTrocaDeCartao()">
                                                    <a4j:support event="onchange"
                                                                 reRender="numeroCartaoApresentar, panelConteudo, cardCartao, selectBandeira"
                                                                 action="#{PagamentoCartaoCreditoControle.buscaBandeiraCartaoOperadora}"
                                                                 oncomplete="document.getElementById('formTrocaCartao:validade').focus();mascaraCartaoCreditoCaixaEmAberto()"/>
                                                </h:inputText>
                                            </div>

                                                <%-- BANDEIRA --%>
                                            <div class="sub-group" style="margin-left: -5px;">
                                                <h:outputText styleClass="title"
                                                              value="Bandeira"/>
                                                </br>
                                                <h:panelGroup layout="block" styleClass="block cb-container"
                                                              style="margin-top: 5px; width: 104%;min-width: 104px;"
                                                              id="selectBandeira">
                                                    <h:selectOneMenu
                                                            value="#{PagamentoCartaoCreditoControle.operadoraCartao}"
                                                            tabindex="3"
                                                            onblur="blurinput(this);" onfocus="focusinput(this);"
                                                            styleClass="form" style="padding-top: 0;">
                                                        <f:selectItems
                                                                value="#{PagamentoCartaoCreditoControle.operadorasCartaoCredito}"/>
                                                        <a4j:support event="onchange"
                                                                     reRender="panelConteudo, cardCartao"
                                                                     action="#{PagamentoCartaoCreditoControle.selecionaOperadora}"/>
                                                    </h:selectOneMenu>
                                                </h:panelGroup>
                                            </div>
                                        </div>

                                        <!-- VENCIMENTO -->
                                        <div class="group">
                                            <div class="sub-group">
                                                <h:outputText styleClass="title" value="Data de vencimento"/>
                                                </br>

                                                <h:inputText id="validade"
                                                             value="#{PagamentoCartaoCreditoControle.dadosPagamento.validade}"
                                                             style="width: 80px; height: 32px !important; margin-right: 1rem; margin-top: 5px;"
                                                             onkeypress="return mascara(this.form, this.id, '99/99', event);"
                                                             styleClass="inputTextClean" onfocus="focusinput(this);"
                                                             tabindex="4"
                                                             onblur="blurinput(this);">
                                                    <a4j:support event="onchange" reRender="datesCartao"
                                                                 oncomplete="document.getElementById('formTrocaCartao:codSeguranca').focus()"/>
                                                </h:inputText>
                                            </div>

                                            <!-- CODIGO SEGURANCA -->
                                            <div class="sub-group" style="margin-left: 29px;">
                                                <h:outputText styleClass="title" value="CVV"/>
                                                </br>
                                                <h:panelGroup layout="block"
                                                              style="display: flex; align-items: center; margin-top: 5px;">
                                                    <h:inputText id="codSeguranca"
                                                                 value="#{PagamentoCartaoCreditoControle.dadosPagamento.codigoSeguranca}"
                                                                 onkeypress="return mascara(this.form, this.id, '9999', event);"
                                                                 tabindex="5"
                                                                 onkeyup="tabAutom(this)"
                                                                 size="4" styleClass="form"
                                                                 onfocus="focusinput(this);" onblur="blurinput(this);"
                                                                 maxlength="4">
                                                    </h:inputText>
                                                    <h:panelGroup>
                                                        <h:panelGroup id="botaoHelp"
                                                                      layout="block"
                                                                      styleClass="circleQuestion">
                                                            <h:outputText style="margin: auto;" value="?"/>
                                                            <rich:toolTip for="botaoHelp">
                                                                <h:panelGrid width="189" columns="2">
                                                                    <rich:panel
                                                                            rendered="#{PagamentoCartaoCreditoControle.dadosPagamento.band.id != 4}"
                                                                            header="#{PagamentoCartaoCreditoControle.dadosPagamento.band.descricao}">
                                                                        <h:graphicImage width="189"
                                                                                        value="images/cartao_visa_cvc2.gif"/>
                                                                        <h:outputText
                                                                                value="O c�digo de seguran�a est� localizado no verso do cart�o e corresponde aos tr�s �ltimos d�gitos da faixa num�rica."/>
                                                                    </rich:panel>
                                                                    <rich:panel
                                                                            rendered="#{PagamentoCartaoCreditoControle.dadosPagamento.band.id == 4}"
                                                                            header="American Express">
                                                                        <h:graphicImage width="189"
                                                                                        value="images/cartao_amex_cvc2.gif"/>
                                                                        <h:outputText
                                                                                value="O c�digo de seguran�a est� localizado na parte frontal do cart�o American Express e corresponde aos quatro d�gitos localizados do lado direito acima da faixa num�rica do cart�o."/>
                                                                    </rich:panel>
                                                                </h:panelGrid>
                                                            </rich:toolTip>
                                                        </h:panelGroup>
                                                    </h:panelGroup>
                                                </h:panelGroup>
                                            </div>
                                            </br>

                                        </div>

                                        <div class="group">

                                            <!-- N�MERO DE PARCELAS-->
                                            <div class="sub-group">
                                                <h:outputText styleClass="title" value="N� de parcelas"/>
                                                </br>
                                                <h:panelGroup layout="block" styleClass="block cb-container"
                                                              style="margin-top: 5px; width: 108%">
                                                    <h:selectOneMenu id="nrParcelasAPF"
                                                                     tabindex="6"
                                                                     valueChangeListener="#{MovPagamentoControle.atualizarOperadoraCartao}"
                                                                     value="#{PagamentoCartaoCreditoControle.dadosPagamento.parcelas}"
                                                                     onblur="blurinput(this);"
                                                                     onfocus="focusinput(this);"
                                                                     styleClass="form">
                                                        <f:selectItems
                                                                value="#{MovPagamentoControle.listaSelectItemNrParcelaCartao}"/>
                                                        <a4j:support event="onchange"
                                                                     reRender="panelPagamentoCartaoAPF">
                                                            <f:attribute name="movPagamento"
                                                                         value="#{PagamentoCartaoCreditoControle.movPagamentoSelecionado}"/>
                                                        </a4j:support>
                                                    </h:selectOneMenu>
                                                </h:panelGroup>
                                            </div>
                                                <%-- PARCELAMENTO STONE --%>
                                            <div class="sub-group" style="margin-left: 55px">
                                                <h:outputText
                                                        rendered="#{PagamentoCartaoCreditoControle.convenioStone && PagamentoCartaoCreditoControle.dadosPagamento.parcelas > 1}"
                                                        styleClass="title tooltipster"
                                                        title="Informe o tipo de parcelamento Stone"
                                                        value="Tipo de parcelamento"/>
                                                </br>
                                                <h:panelGroup
                                                        rendered="#{PagamentoCartaoCreditoControle.convenioStone && PagamentoCartaoCreditoControle.dadosPagamento.parcelas > 1}"
                                                        layout="block"
                                                        styleClass="block cb-container"
                                                        style="margin-top: 5px;">
                                                    <h:selectOneMenu id="tipoparcelamentostone"
                                                                     value="#{PagamentoCartaoCreditoControle.tipoParcelamentoStone}"
                                                                     valueChangeListener="#{PagamentoCartaoCreditoControle.atualizarTipoPagamentoStone}"
                                                                     onblur="blurinput(this);"
                                                                     onfocus="focusinput(this);"
                                                                     styleClass="form"
                                                                     style="padding-top: 0;">
                                                        <a4j:support event="onchange" focus="tipoparcelamentostone"
                                                                     reRender="panelConteudo"/>
                                                        <f:selectItem itemLabel=""/>
                                                        <f:selectItem
                                                                itemLabel="#{msg_aplic.prt_tipo_parcelamento_stone_lojista}"
                                                                itemValue="MCHT"/>
                                                        <f:selectItem
                                                                itemLabel="#{msg_aplic.prt_tipo_parcelamento_stone_emissor}"
                                                                itemValue="ISSR"/>
                                                    </h:selectOneMenu>
                                                </h:panelGroup>
                                            </div>
                                        </div>

                                    </div>

                                    <div>
                                        <h:panelGroup layout="block">
                                            <h:panelGroup layout="block" id="cardCartao"
                                                          styleClass="cartao #{PagamentoCartaoCreditoControle.styleClassCartao}">
                                                <div style="padding: 10px">
                                                    <h:panelGroup layout="block" id="chipcard"
                                                                  style="margin: 16px; display: flex">
                                                        <h:graphicImage value="imagens_flat/icon-chip.svg" width="25"
                                                                        height="18"/>
                                                    </h:panelGroup>

                                                    <h:panelGroup layout="block" id="nomeTitularCartao"
                                                                  style="text-transform: uppercase;  display: flex">
                                                        <h:outputText
                                                                styleClass="#{PagamentoCartaoCreditoControle.styleClassCartaoTitles}"
                                                                value="#{PagamentoCartaoCreditoControle.dadosPagamento.nomeTitular == '' ? 'Nome Titular do cart�o' : PagamentoCartaoCreditoControle.dadosPagamento.nomeTitular}"/>
                                                    </h:panelGroup>

                                                    <div style="display: flex; justify-content: space-between; margin: 3% 0 3% 0;">
                                                        <h:panelGroup id="numeroCartaoApresentar">
                                                            <h:outputText
                                                                    styleClass="#{PagamentoCartaoCreditoControle.styleClassCartaoTitles}"
                                                                    value="#{PagamentoCartaoCreditoControle.dadosPagamento.numero == '' || PagamentoCartaoCreditoControle.dadosPagamento.numero == null ? '**** **** **** ****' : PagamentoCartaoCreditoControle.dadosPagamento.numero}"/>
                                                        </h:panelGroup>
                                                        <h:panelGroup id="datesCartao">
                                                            <h:outputText
                                                                    styleClass="#{PagamentoCartaoCreditoControle.styleClassCartaoTitles}"
                                                                    value="#{PagamentoCartaoCreditoControle.dadosPagamento.validade == '' ? '00/00' : PagamentoCartaoCreditoControle.dadosPagamento.validade}"/>
                                                        </h:panelGroup>
                                                    </div>
                                                    <h:panelGroup layout="block"
                                                                  rendered="#{PagamentoCartaoCreditoControle.styleClassCartaoRodape == 'cartaoRodaPe' ? false : true}"
                                                                  style="border: 1px solid #D3D5D7; margin: auto;"/>
                                                </div>
                                                <h:panelGroup layout="block"
                                                              style="min-height: 37px"
                                                              styleClass="#{PagamentoCartaoCreditoControle.styleClassCartaoRodape}">
                                                    <h:panelGroup
                                                            style="display: flex; justify-content: space-between; padding: 3px;">
                                                        <div>
                                                            <h:panelGroup layout="block" id="imagemCartao"
                                                                          rendered="#{PagamentoCartaoCreditoControle.dadosPagamento.band != null && !PagamentoCartaoCreditoControle.dadosPagamento.usarIdVindiCobranca
                                                                          && !PagamentoCartaoCreditoControle.dadosPagamento.usarTokenCieloCobranca}">
                                                                <h:graphicImage style="margin-left: 624%;"
                                                                                url="/imagens_flat/#{PagamentoCartaoCreditoControle.dadosPagamento.band.imagem}-icon.svg"
                                                                                width="30" height="30"/>
                                                            </h:panelGroup>
                                                        </div>
                                                    </h:panelGroup>
                                                </h:panelGroup>
                                            </h:panelGroup>
                                            <h:panelGroup layout="block"
                                                          style="margin-left: 6%; margin-block-start: auto; margin-top: 10px;">
                                                <a4j:commandLink
                                                        action="#{PagamentoCartaoCreditoControle.limparDadosDoCartao}"
                                                        reRender="panelPagamentoCartaoAPF">
                                                    <i class="fa-icon-eraser texto-size-18"></i>
                                                    <h:outputText styleClass="title" value="Limpar dados"/>
                                                </a4j:commandLink>
                                            </h:panelGroup>
                                        </h:panelGroup>
                                    </div>
                                </div>

                            </c:if>

                            <!-- ------------------------ PAINEL DE MENSAGENS -------------------------------- -->

                            <table width="100%">
                                <tr>
                                    <td>
                                        <h:panelGrid id="mensagens">
                                            <h:outputText id="msgFormasPag" styleClass="mensagem"
                                                          value="#{PagamentoCartaoCreditoControle.mensagem}"/>
                                            <h:outputText id="msgFormasPagDet" styleClass="mensagemDetalhada"
                                                          value="#{PagamentoCartaoCreditoControle.mensagemDetalhada}"/>
                                        </h:panelGrid>
                                    </td>
                                </tr>
                            </table>
                        </h:panelGroup>
                    </h:panelGroup>

                    <%--------BOT�ES-------%>
                    <div style="margin-left: -3px;">
                        <a4j:commandButton id="botaoConfirmacaoAPF"
                                           value="Salvar"
                                           style="color: #FFFFFF; background: #22933B; height: 28px"
                                           styleClass="botoes nvoBt"
                                           status="statusTrocaCartao"
                                           reRender="panelBotaoConfirmarTroca,panelPagamentoCartaoAPF"
                                           oncomplete="#{PagamentoCartaoCreditoControle.mensagemNotificar}"
                                           actionListener="#{PagamentoCartaoCreditoControle.concretizarTrocaSemCobrar}">
                            <f:attribute name="codContrato" value="#{ClienteControle.contratoVO.codigo}"/>
                        </a4j:commandButton>
                        <a4j:commandButton id="btnFecharT"
                                           value="Cancelar"
                                           styleClass="botoes nvoBt"
                                           style="color: #6F747B; background: #E5E5E5; height: 28px"
                                           onclick="fireElement('hidelinkTrocarCartao');"/>
                    </div>

                </a4j:form>



            </h:panelGroup>

        </rich:panel>
    </h:panelGrid>
</rich:modalPanel>

<script>
    function mascaraCartaoCreditoTrocaDeCartao() {
        try {
            var v = document.getElementById('formTrocaCartao:nrCartao').value;
            v = v.replace(/^(\d{4})(\d)/g, "$1 $2");
            v = v.replace(/^(\d{4})\s(\d{4})(\d)/g, "$1 $2 $3");
            v = v.replace(/^(\d{4})\s(\d{4})\s(\d{4})(\d)/g, "$1 $2 $3 $4");
            document.getElementById('formTrocaCartao:nrCartao').value = v;
        } catch (e) {
            console.log("ERRO mascaraCartaoCredito: " + e);
        }
    }
</script>
