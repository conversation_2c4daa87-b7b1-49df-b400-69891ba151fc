<%--
  Created by IntelliJ IDEA.
  User: Rafael
  Date: 02/02/2016
  Time: 09:46
  To change this template use File | Settings | File Templates.
--%>
<%@page contentType="text/html" %>
<%@page pageEncoding="ISO-8859-1" %>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html" %>
<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax" %>
<%@taglib prefix="f" uri="http://java.sun.com/jsf/core" %>
<%@taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich" %>
<%@taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<%@ taglib prefix="H" uri="http://java.sun.com/jsf/html" %>
<%@ taglib prefix="ui" uri="http://richfaces.org/a4j" %>

<link href="${root}/css_pacto.css" rel="stylesheet" type="text/css"/>
<rich:modalPanel domElementAttachment="parent" id="panelRenovarSessao" styleClass="novaModal"
                 width="550"
                 onshow="setTotalProgressiveBar();"
                 autosized="true" shadowOpacity="true">
    <f:facet name="header">
        <h:panelGroup>
            <h:outputText value="Para sua segurança, sua sessão finalizará em.."/>
        </h:panelGroup>

    </f:facet>
    <h:panelGroup layout="block">
        <h:panelGroup id="regressiveBar" layout="block" styleClass="bg-vermelho progressiveBar"/>
        <h:panelGroup layout="block" id="tblRenovarSessao" style="text-align: center;margin-top: 15px;">

            <h:outputText id="regressiveTime" styleClass="texto-cor-vermelho texto-size-60-real texto-font" value=""/>
            <h:panelGroup layout="block" styleClass="container-botoes"
                          style="text-align: right;border-top: 1px solid #E5E5E5;margin-top: 25px;line-height: 40px;height: 29px;">
                <a4j:commandLink status="statusHora"
                                 title="Clique aqui apra renovar sua sessão"
                                 reRender="horaSistema"
                                 action="#{SuporteControle.poll}"
                                 styleClass="linkPadrao texto-size-16-real texto-cor-azul rotate-hover"
                                 oncomplete="resetTime(tempoEmMillis);#{SuperControle.enableSetLastActionTime ? 'setLastActionTime();' : ''}Richfaces.hideModalPanel('panelRenovarSessao');">
                    <i class="fa-icon-refresh"></i> Retornar minha sessão

                </a4j:commandLink>
            </h:panelGroup>
        </h:panelGroup>
    </h:panelGroup>
</rich:modalPanel>
<c:if test="${!SuperControle.menuZwUi}">
<h:panelGroup styleClass="container-navbar" layout="block">
    <h:panelGroup layout="block" styleClass="navbar navbar-default">
        <h:panelGroup layout="block" styleClass="container-fluid" id="navBarTopo">
            <h:panelGroup layout="block" styleClass="nav navbar-nav">
                <%--Inicial--%>
                <h:panelGroup id="item1" layout="block" styleClass="item1 menuItem">
                    <h:commandLink styleClass="titulo" id="menuCRM" action="crm"
                                   style="text-decoration: none">
                        <i style="font-size:21px;" class="fa-icon-home"></i>
                    </h:commandLink>
                </h:panelGroup>
                <%--&lt;%&ndash;Antigo Crm&ndash;%&gt;--%>
                <%--<h:panelGroup id="item2" layout="block" styleClass="item2 menuItem">--%>
                <%--<h:commandLink styleClass="titulo" action="telaAntigoCRM"--%>
                <%--style="text-decoration: none" value="Antigo CRM"/>--%>
                <%--</h:panelGroup>--%>
                <%--Meta Diária--%>
                <h:panelGroup id="item3" layout="block" styleClass="item3 menuItem">
                    <a4j:commandLink action="#{MetaCRMControle.consultarMetas}"
                                     rendered="#{LoginControle.permissaoAcessoMenuVO.visualizarMeta}"
                                     value="Meta Diária"
                                     id="metaDiaria"
                                     styleClass="titulo"
                                     reRender="superiorPrincipalCRM, colunaCRMEsquerda, colunaCRMCentro, colunaCRMDireita"
                                     oncomplete="mostrarTodasTelas();adicionarPlaceHolderCRM();"/>
                </h:panelGroup>
                <%--Cadastros--%>
                <h:panelGroup id="item4" layout="block" styleClass="item4 menuItem botao-hidden">
                    <h:commandLink styleClass="titulo">
                        Cadastros&nbsp<i class="titulo fa-icon-caret-down"
                        style="font-family: FontAwesome;padding-left: 6px;"></i>
                    </h:commandLink>
                    <h:panelGroup layout="block" styleClass="dropdown-content">

                        <h:panelGroup layout="block" styleClass="dropdown-item"
                                      rendered="#{LoginControle.permissaoAcessoMenuVO.evento}">
                            <h:panelGroup layout="block" styleClass="container-item">
                                <a4j:commandLink styleClass="textSmallFlat"
                                                 id="botaoEvento" ajaxSingle="true"
                                                 value="Evento"
                                                 oncomplete="abrirPopup('eventoCons.jsp', 'Evento', 800, 595);"/>
                            </h:panelGroup>
                        </h:panelGroup>

                        <h:panelGroup layout="block" styleClass="dropdown-item"
                                      rendered="#{LoginControle.permissaoAcessoMenuVO.feriado}">
                            <h:panelGroup layout="block" styleClass="container-item">
                                <a4j:commandLink styleClass="textSmallFlat"
                                                 id="botaoFeriado" ajaxSingle="true"
                                                 value="Feriado"
                                                 oncomplete="abrirPopup('feriadoCons.jsp', 'Feriado', 800, 595);"/>
                            </h:panelGroup>
                        </h:panelGroup>

                        <h:panelGroup layout="block" styleClass="dropdown-item"
                                      rendered="#{LoginControle.permissaoAcessoMenuVO.grupoColaborador}">
                            <h:panelGroup layout="block" styleClass="container-item">
                                <a4j:commandLink styleClass="textSmallFlat"
                                                 id="botaoGrupoColaborador" ajaxSingle="true"
                                                 value="Grupo Colaborador"
                                                 oncomplete="abrirPopup('grupoColaboradorCons.jsp', 'GrupoColaborador', 800, 595);"/>
                            </h:panelGroup>
                        </h:panelGroup>

                        <h:panelGroup layout="block" styleClass="dropdown-item"
                                      rendered="#{LoginControle.permissaoAcessoMenuVO.crmExtraCRM}">
                            <h:panelGroup layout="block" styleClass="container-item">
                                <a4j:commandLink
                                        id="botaoMetaExtra"
                                        styleClass="textSmallFlat"
                                        ajaxSingle="true"
                                        value="Meta Extra"
                                        oncomplete="abrirPopup('crmExtraCons.jsp', 'Meta Extra', 1000, 650);"/>
                            </h:panelGroup>
                        </h:panelGroup>

                        <h:panelGroup layout="block" styleClass="dropdown-item"
                                      rendered="#{LoginControle.permissaoAcessoMenuVO.modeloMensagem}">
                            <h:panelGroup layout="block" styleClass="container-item">
                                <h:commandLink styleClass="textSmallFlat"
                                               id="botaoModeloMensagem"
                                               action="#{MensagemBuilderControle.init}"
                                               value="Modelo de Mensagem"/>
                            </h:panelGroup>
                        </h:panelGroup>

                        <h:panelGroup layout="block" styleClass="dropdown-item"
                                      rendered="#{LoginControle.permissaoAcessoMenuVO.objecao}">
                            <h:panelGroup layout="block" styleClass="container-item">
                                <a4j:commandLink styleClass="textSmallFlat" id="botaoObjecao" ajaxSingle="true"
                                                 value="Objeção"
                                                 oncomplete="abrirPopup('objecaoCons.jsp', 'Objecao', 800, 595);"/>
                            </h:panelGroup>
                        </h:panelGroup>

                        <h:panelGroup layout="block" styleClass="dropdown-item"
                                      rendered="#{LoginControle.permissaoAcessoMenuVO.script}">
                            <h:panelGroup layout="block" styleClass="container-item">
                                <a4j:commandLink styleClass="textSmallFlat"
                                                 id="botaoTextoPadrao" ajaxSingle="true" value="Script"
                                                 oncomplete="abrirPopup('textoPadraoCons.jsp', 'Script', 800, 595);"/>
                            </h:panelGroup>
                        </h:panelGroup>

                    </h:panelGroup>
                </h:panelGroup>
                <%--Operações--%>
                <h:panelGroup layout="block" styleClass="item5 menuItem botao-hidden">
                    <h:commandLink styleClass="titulo">
                        Operações&nbsp<i class="titulo fa-icon-caret-down"
                        style="font-family: FontAwesome;padding-left: 6px;"></i>
                    </h:commandLink>
                    <h:panelGroup layout="block" styleClass="dropdown-content">

                        <h:panelGroup layout="block" styleClass="dropdown-item"
                                      rendered="#{LoginControle.permissaoAcessoMenuVO.organizadorCarteira}">
                            <h:panelGroup layout="block" styleClass="container-item">
                                <a4j:commandLink id="botaoCarteiras"
                                                 value="Gestão de Carteiras"
                                                 styleClass="textSmallFlat"
                                                 action="#{LoginControle.abrirCarteirasCRM}"/>
                            </h:panelGroup>
                        </h:panelGroup>

                        <h:panelGroup layout="block" styleClass="dropdown-item"
                                      rendered="#{LoginControle.permissaoAcessoMenuVO.agenda}">
                            <h:panelGroup layout="block" styleClass="container-item">
                                <a4j:commandLink
                                        id="botaoConfirmarAgendados"
                                        ajaxSingle="true" value="Marcar Comparecimento"
                                        styleClass="textSmallFlat"
                                        oncomplete="abrirPopup('confirmarComparecimentoAgendadoForm.jsp', 'ConfirmarAgendados', 780, 595);"
                                        action="#{AgendaControle.novo}"/>
                            </h:panelGroup>
                        </h:panelGroup>

                    </h:panelGroup>
                </h:panelGroup>
                <rich:spacer width="5px"/>
                <h:panelGroup id="item8" layout="block" styleClass="item4 menuItem botao-hidden" rendered="#{LoginControle.permissaoAcessoMenuVO.malaDireta}">
                    <h:commandLink styleClass="titulo">
                        Realizar contato&nbsp<i class="titulo fa-icon-caret-down"
                        style="font-family: FontAwesome;padding-left: 6px;"></i>
                    </h:commandLink>
                    <h:panelGroup layout="block" styleClass="dropdown-content">

                        <h:panelGroup layout="block" styleClass="dropdown-item"
                                      >
                            <h:panelGroup layout="block" styleClass="container-item">
                                <a4j:commandLink
                                        id="btncontatopessoa" ajaxSingle="true"
                                        actionListener="#{HistoricoContatoControle.consultarPaginadoListener}"
                                        value="Contato pessoal"
                                        styleClass="textSmallFlat"
                                        oncomplete="abrirPopup('mailing.jsp', 'mailing', 1000, 650);"/>
                            </h:panelGroup>
                        </h:panelGroup>

                        <h:panelGroup layout="block" styleClass="dropdown-item">
                            <h:panelGroup layout="block" styleClass="container-item">
                                <h:commandLink
                                        id="botaoMalaDireta"
                                        action="#{MensagemBuilderControle.init}"
                                        value="Contato em grupo"
                                        styleClass="textSmallFlat"/>
                            </h:panelGroup>
                        </h:panelGroup>
                    </h:panelGroup>
                </h:panelGroup>
                <rich:spacer width="5px"/>
                <%--Consultas--%>
                <h:panelGroup id="item6" layout="block" styleClass="item6 menuItem botao-hidden">
                    <h:commandLink styleClass="titulo">
                        Consultas&nbsp<i class="titulo fa-icon-caret-down" style="font-family: FontAwesome;padding-left: 6px;"></i>
                    </h:commandLink>
                    <h:panelGroup layout="block" styleClass="dropdown-content">

                        <h:panelGroup layout="block" styleClass="dropdown-item-consultas"
                                      rendered="#{LoginControle.permissaoAcessoMenuVO.relatorioAgendamentos}">
                            <h:panelGroup layout="block" styleClass="container-item">
                                <a4j:commandLink styleClass="textSmallFlat"
                                                 id="botaoAgendamentos"
                                                 ajaxSingle="true"
                                                 value="Agendamentos"
                                                 oncomplete="abrirPopup('agendamentos.jsp', 'agendamentos', 1200, 750);"/>

                            </h:panelGroup>
                        </h:panelGroup>

                        <h:panelGroup layout="block" styleClass="dropdown-item-consultas"
                                      rendered="#{LoginControle.permissaoAcessoMenuVO.relatorioContatosAPP}">
                            <h:panelGroup layout="block" styleClass="container-item">

                                <a4j:commandLink styleClass="textSmallFlat"
                                                 id="botaoContatosApp"
                                                 ajaxSingle="true"
                                                 value="Contatos App"
                                                 oncomplete="abrirPopup('contatosApp.jsp', 'contatosApp', 1000, 650);"/>

                            </h:panelGroup>
                        </h:panelGroup>

                        <h:panelGroup layout="block" styleClass="dropdown-item-consultas"
                                      rendered="#{LoginControle.permissaoAcessoMenuVO.historicoContato}">
                            <h:panelGroup layout="block" styleClass="container-item">
                                <a4j:commandLink
                                        id="botaoHistoricoContato"
                                        ajaxSingle="true" value="Histórico de Contatos "
                                        styleClass="textSmallFlat"
                                        action="#{HistoricoContatoControle.inicializarDadosRealizarContato}"
                                        oncomplete="abrirPopup('historicoContatoCons.jsp', 'historicoContatoCons', 800, 600);"/>
                            </h:panelGroup>
                        </h:panelGroup>
                        <%--<h:panelGroup layout="block" styleClass="dropdown-item-consultas" rendered="#{LoginControle.permissaoAcessoMenuVO.totalizadorMeta}">--%>
                        <%--<h:panelGroup layout="block" styleClass="container-item">--%>
                        <%--<a4j:commandLink styleClass="textSmallFlat"--%>
                        <%--id="botaoConsultarTotalizadorMetas"--%>
                        <%--value="Totalizador de Metas"--%>

                        <%--oncomplete="abrirPopupTopoPagina('totalizadorMeta.jsp', 'TotalizadorMeta', 816, 595);"/>--%>
                        <%--</h:panelGroup>--%>
                        <%--</h:panelGroup>--%>

                        <h:panelGroup layout="block" styleClass="dropdown-item-consultas"
                                      rendered="#{LoginControle.permissaoAcessoMenuVO.indicacao}">
                            <h:panelGroup layout="block" styleClass="container-item">
                                <a4j:commandLink styleClass="textSmallFlat"
                                                 id="botaoIndicacao"
                                                 action="#{IndicacaoControle.realizarlimpezaCamposMensagem}"
                                                 ajaxSingle="true"
                                                 value="Indicação"
                                                 oncomplete="abrirPopup('indicacaoCons.jsp', 'indicacao', 800, 595);"/>
                            </h:panelGroup>
                        </h:panelGroup>

                        <h:panelGroup layout="block" styleClass="dropdown-item-consultas"
                                      rendered="#{LoginControle.permissaoAcessoMenuVO.passivo}">
                            <h:panelGroup layout="block" styleClass="container-item">
                                <a4j:commandLink styleClass="textSmallFlat"
                                                 id="botaoPassivo" ajaxSingle="true"
                                                 action="#{PassivoControle.realizarlimpezaCamposMensagem}"
                                                 value="Receptivo"
                                                 oncomplete="abrirPopup('passivoCons.jsp', 'Passivo', 850, 595);"/>
                            </h:panelGroup>
                        </h:panelGroup>

                    </h:panelGroup>
                </h:panelGroup>
                <rich:spacer width="5px" rendered="#{LoginControle.permissaoAcessoMenuVO.businessIntelligenceCRM}"/>
                <%--Business Intelligence--%>
                <h:panelGroup id="item7" layout="block" styleClass="item7 menuItem"
                              rendered="#{LoginControle.permissaoAcessoMenuVO.businessIntelligenceCRM}">
                    <a4j:commandLink id="linkBICRM" styleClass="titulo"
                                     action="#{BusinessIntelligenceCRMControle.carregarBusinessIntelligence}"
                                     value="Business Intelligence"/>
                </h:panelGroup>

            </h:panelGroup>
            <script>
                jQuery(window).scroll(function () {
                    topAtual = jQuery(window).scrollTop();
                    if (topAtual > 82) {
                        jQuery('.navbar').addClass('barraFixa');
                    } else if (topAtual <= 82) {
                        jQuery('.navbar').removeClass('barraFixa');
                    }

                });
            </script>
            <jsp:include page="include_menu_campoBusca.jsp" flush="true"/>
        </h:panelGroup>
    </h:panelGroup>
</h:panelGroup>
</c:if>
