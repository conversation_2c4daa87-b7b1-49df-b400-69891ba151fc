<%@page contentType="text/html" %>
<%@page pageEncoding="ISO-8859-1" %>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html" %>
<%@taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax" %>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich" %>
<%@taglib prefix="f" uri="http://java.sun.com/jsf/core" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions"%>

<style>
    .item-superior-qtd-total-notificacoes {
        -webkit-background-clip: padding-box;
        -webkit-box-shadow: rgba(0, 0, 0, 0.701961) 0px 1px 1px 0px;
        -webkit-font-smoothing: subpixel-antialiased;
        background-color: #102D70 !important;
        box-shadow: rgba(0, 0, 0, 0.701961) 0 1px 1px 0;
        color: #fff !important;
        cursor: pointer;
        font-family: 'Helvetica Neue', Helvetica, sans-serif;
        font-size: 10px !important;
        text-align: center;
        text-shadow: rgba(0, 0, 0, 0.4) 0 -1px 0;
        border-radius: 10px;
        padding: 5px 5px 2px 5px;
        position: relative;
        top: -5px;
        min-width: 10px !important;
        margin-left: -7px;
    }
</style>

<div class="zw_ui_modulos_pesquisa" id="zw_ui_modulos_pesquisa">
    <a4j:commandLink id="atualizarMenuExplorar" reRender="menu-explorar-menus-container" style="display: none"/>
    <c:if test="${SuporteControle.apresentarMensagemExpiracao}">
        <div style="background-color: #BA202F;position: absolute; width: inherit; height: 24px;top: 0px;color: #ffffff;text-align: center;">
            <div class="content"><i style="font-size: 20px; padding-right: 5px;" class="pct pct-alert-triangle"></i>
                <h:outputText style="font-weight: 600; font-size: 14px; line-height: 100%;"
                              title="Entre em contato com a Pacto Soluções: (62) 3251-5820"
                              id="dataExpiracaoNovoMenu"
                              value="#{SuporteControle.mensagemExpiracaoTopo}"/>
                <a4j:commandLink rendered="#{LoginControle.usuarioLogado.possuiPerfilAcessoAdministrador}"
                                 style="color: #ffffff;font-size: 14px;"
                                 title="Clique para acessar os boletos disponíveis"
                                 value="  Clique aqui para ver suas pendências."
                                 oncomplete="#{CanalPactoControle.mensagemNotificar}"
                                 action="#{CanalPactoControle.abrirTelaMinhaContaFaturas}"/>

            </div>

        </div>
    </c:if>
    <div class="aux" style="margin-top: 16px">
        <div class="modulos-choose" id="explorar-trigger" onclick="toggleModulos()">
            <div class="modulo-opener">
                <i class="pct pct-grid"></i>
                <i class="pct pct-x"></i>
                <span> Explorar</span>
            </div>
        </div>
        <input type="password" style="visibility: hidden; width: 1px; margin: 0;"/>
        <div class="zw_pesquisa_global">
            <i class="pct pct-search"></i>
            <h:panelGroup id="pnlCampoBusca" layout="block" style="display: flex; width: 100%">
                <h:inputText id="campoBuscaZWUI" styleClass="ng-pristine ng-valid ng-touched campo-busca-zw-ui" readonly="true"
                             onfocus="this.removeAttribute('readonly');"
                             value="#{ConsultaClienteControle.valorConsultaParametrizada}" autocomplete="off"/>
            </h:panelGroup>
            <script>
                document.getElementById("form:campoBuscaZWUI").setAttribute("placeholder", "Buscar pelo aluno ou recurso");
                document.getElementById("form:campoBuscaZWUI").setAttribute("autocomplete", "new-password");
                document.getElementById("form:campoBuscaZWUI").setAttribute("spellcheck", "false");
                document.getElementById("form:campoBuscaZWUI").setAttribute("autocorrect", "off");
                document.getElementById("form:campoBuscaZWUI").setAttribute("role", "presentation");
                document.getElementById("form:campoBuscaZWUI").setAttribute("name", "campoBuscaUI");
            </script>

            <rich:suggestionbox  width="519"
                                 height="300"
                                 for="campoBuscaZWUI"
                                 frequency="0"
                                 popupStyle="margin-left: 12px;background-color: #000066;z-index: 1200"
                                 fetchValue="#{FuncionalidadeControle.rotulo}"
                                 suggestionAction="#{FuncionalidadeControle.executarAutocompleteFuncionalidade}"
                                 minChars="1" rowClasses="20"
                                 status="true"
                                 nothingLabel="Nenhuma funcionalidade Encontrada!"
                                 styleClass="suggestionCampoBusca"
                                 var="result" id="suggestionCampoBuscaZWUI">
                <a4j:support  event="onselect" onsubmit="setDocumentCookie('popupsImportante', 'close',1);setTimeout(function() { setDocumentCookie('popupsImportante', '',1); }, 1000);"
                              action="#{FuncionalidadeControle.abrirComCasoNavegacao}"
                              actionListener="#{FuncionalidadeControle.abrirComAcaoListener}"
                              oncomplete="#{FuncionalidadeControle.abrirPopUp}"
                              focus="buscaFuncionalidade" reRender="textfield2,suggestionFuncionalidade,menuLateral, modalAbrirCaixa">
                    <f:attribute name="paginaInicial" value="paginaInicial" />
                    <f:attribute name="tipoConsulta" value="parametrizado"/>
                    <f:attribute name="idLocalizacaoMenu" value="PESQUISA_RECURSO"/>
                </a4j:support>
                <h:column>
                    <h:graphicImage rendered="#{!result.cliente && result.funcionalidadeSistemaEnumAux.funcionalidadeSistemaEnum !=null}" style="width:28px;height:25px;" url="#{result.funcionalidadeSistemaEnumAux.funcionalidadeSistemaEnum.iconeModulo}"/>

                    <a4j:mediaOutput     element="img"
                                         rendered="#{!SuperControle.fotosNaNuvem && result.cliente}"
                                         align="left" style="left:0px;width:25px;height:25px; border:none;border-radius:5px; "
                                         cacheable="false" session="false"

                                         title="#{result.clienteVO.pessoa.nome}"
                                         createContent="#{FuncionalidadeControle.paintFoto}"
                                         styleClass="shadow"
                                         value="#{ImagemData}" mimeType="image/jpeg" >
                        <f:param value="#{SuperControle.timeStamp}" name="time"/>
                        <f:param name="largura" value="25"/>
                        <f:param name="altura" value="25"/>
                        <f:param name="pessoa" value="#{result.clienteVO.codigo}"></f:param>
                    </a4j:mediaOutput>
                    <h:graphicImage  rendered="#{SuperControle.fotosNaNuvem && result.cliente}"
                                     width="150" height="180"
                                     style="left:0px;width:25px;height:25px; border:none;border-radius:5px;"
                                     url="#{FuncionalidadeControle.paintFotoDaNuvem}">
                    </h:graphicImage>
                    <h:panelGroup layout="block" style="height: 25px" rendered="#{!result.cliente && result.funcionalidadeClienteEnumAux.funcionalidadeClienteEnum !=null}"/>
                </h:column>

                <h:column>

                    <h:panelGroup layout="block" rendered="#{!result.descricao and !result.subTitulo}" style="width: 250px;text-align: left;">
                        <h:outputText rendered="#{!result.cliente && result.funcionalidadeClienteEnumAux.funcionalidadeClienteEnum !=null}"
                                      style="font-size: 15px;text-align: right;" styleClass="fa-icon-arrow-right"/>
                        <h:panelGroup>
                            <h:outputText
                                    style="text-align: left"
                                    styleClass="textSmallFlat"
                                    value="#{result.rotulo}"/>
                            <div>
                                <h:outputText rendered="#{result.cliente}"
                                              style="text-align: left" value="#{result.clienteVO.empresa.nome}"/>
                            </div>

                            <h:panelGroup layout="block" rendered="#{result.cliente}">
                                <h:outputText style="font-weight: bold; text-align: left; color:#555"
                                              value="Mat:"/>
                                <h:outputText style="padding-left: 5px; text-align: left"
                                              value="#{result.clienteVO.matricula}"/>
                                <h:outputText rendered="#{LoginControle.permissaoAcessoMenuVO.visualizarCPFBusca && not empty result.clienteVO.pessoa.cfp}"
                                              style="padding-left: 20px; font-weight: bold; text-align: left; color:#555"
                                              value="CPF:"/>
                                <h:outputText rendered="#{LoginControle.permissaoAcessoMenuVO.visualizarCPFBusca && not empty result.clienteVO.pessoa.cfp}"
                                              style="padding-left: 5px; text-align: left"
                                              value="#{result.clienteVO.pessoa.cfp}"/>
                            </h:panelGroup>
                        </h:panelGroup>
                    </h:panelGroup>

                    <h:outputText style="text-align: left" rendered="#{result.autorizacaoCobranca}" value="#{result.itemDescricao }"/>

                    <h:panelGroup layout="block" rendered="#{result.descricao and !result.subTitulo}" style="width: 250px;text-align: left;height: 20px;vertical-align: baseline;line-height:19px">
                        <h:outputText style="text-align: left;color: #BBBDBF" styleClass="textSmallFlat" value="#{result.itemDescricao}"/>
                    </h:panelGroup>
                    <h:panelGroup layout="block" rendered="#{result.subTitulo}" style="width: 250px;text-align: left;height: 20px;vertical-align: baseline;line-height:19px">
                        <h:outputText style="text-align: left;color: #BBBDBF" styleClass="textSmallFlatSubTitle" value="#{result.itemDescricao}"/>
                    </h:panelGroup>
                </h:column>


            </rich:suggestionbox>
        </div>

        <div class="zw_ui_pacto-user">
            <div class="section-menu">
                <div class="topbar-actions-divider" style="margin-left: 6px"></div>

                <div class="topbar-menu-item" id="topbar-menu-item-fav">
                    <a4j:commandLink id="codigosClientesFavoritosLink" onclick="escondeInputLembrete('#{ClientesMarcadosControle.codigosClientesMarcadosFavoritos}')">
                        <div class="favoritos" style="place-content: center;"
                             onclick="abrirFavoritos(event);">
                            <div style="place-self: center; width: 100%;height: 100%;">
                                <i class="pct pct-user-fav"></i>
                            </div>
                        </div>
                    </a4j:commandLink>
                </div>

                <h:panelGroup layout="block" id="nrMensagensLidas" styleClass="topbar-menu-item">
                    <a4j:commandLink action="#{LoginControle.abrirSocialMailing}" reRender="nrMensagensLidas"
                                     id="nrMensagensLidasExplorarLink"
                                     oncomplete="#{LoginControle.msgAlert}" style="display: block;text-decoration: none;">
                        <i class="pct pct-message-circle"></i>
                    </a4j:commandLink>
                    <h:panelGroup layout="block"
                                  rendered="#{UsuarioControle.usuario.nrMensagensNaoLidas > 0}"
                                  style="height: 0;">
                        <a4j:commandLink styleClass="notificacaoSocialMailing" id="nrMensagensNaoLidas"
                                         action="#{LoginControle.abrirSocialMailing}"
                                         oncomplete="#{LoginControle.msgAlert}"
                                         reRender="nrMensagensLidas"
                                         value="#{UsuarioControle.usuario.nrMensagensNaoLidas}"/>
                    </h:panelGroup>
                </h:panelGroup>
                <div class="topbar-menu-item assinaturaTrigger" id="topbar-menu-item-qrcode" onclick="abrirAssinatura(event)">
                    <a>
                        <i class="pct pct-qr-code"></i>
                    </a>
                </div>
                <c:if test="${SuperControle.notificacoesVersoesHabilitado}">
                    <div id="idNotificacoesVersoes" class="topbar-menu-item" onclick="abrirNotificacoes()">
                        <a>
                            <div style="place-self: center;">
                                <i class="pct pct-bell"></i>
                            </div>
                        </a>
                        <div id="item-superior-qtd-total-notificacoes"
                             class="item-superior-qtd-total-notificacoes">0</div>
                    </div>
                </c:if>
                <c:if test="${not SuperControle.pontoInterrogacaoHabilitado}">
                    <div class="topbar-menu-item">
                        <h:outputLink value="https://pactosolucoes.com.br/ajuda/"
                                      title="Central de ajuda" target="_blank" styleClass="tooltipster">
                            <i class="pct pct-help-circle"></i>
                        </h:outputLink>
                    </div>
                </c:if>
                <c:if test="${SuperControle.pontoInterrogacaoHabilitado}">
                    <div id="idpontointerrogacao" class="topbar-menu-item" onclick="abrirPontoInterrogacao()" style="color:#1E60FA;">
                        <a style="color:#1E60FA;">
                            <i class="pct pct-help-circle"></i>
                        </a>
                    </div>
                </c:if>

                <c:if test="${SuperControle.maxGpt}">
                    <div id="iframe-container" class="iframe-container" style="display: none;">
                        <iframe src="https://pgpt.pactosolucoes.com.br/?chat_open=True"></iframe>
                    </div>
                </c:if>

                <style>
                    #gpt-iframe-button {
                        padding-right: 16px;
                    }
                    #gpt-iframe-button:hover {
                        color: #387edb;
                    }
                    .iframe-container {
                        position: fixed;
                        bottom: 40px;
                        right: 65px;
                        width: 397px;
                        height: 562px;
                        z-index: 1000;
                        border-radius: 25px;
                        box-shadow: 0 0 16px rgba(0, 0, 0, 0.5);
                    }
                    .iframe-container iframe {
                        position: absolute;
                        top: 0;
                        left: 0;
                        width: 100%;
                        height: 100%;
                        border: none;
                        border-radius: 25px;
                    }
                </style>

                <script>

                </script>

                <div class="topbar-actions-divider"></div>
                    <div id="divAbrirUsuarioADM" class="topbar-menu-item topbar-menu-item-usuario" onclick="abrirUsuario(event)">
                        <a class="user-menu-novo" title="${SuperControle.usuarioLogado.nome} (${SuperControle.perfilUsuarioLogado.nome})">
                            <h:graphicImage id="fotosNaNuvemTrueZwui"
                                            style="width: 24px; height: 24px"
                                            title="#{SuperControle.titleUsuario}"
                                            url="#{SuperControle.fotoKeyUsuarioLogado}">
                            </h:graphicImage>
                            <h:graphicImage id="fotoUsuarioPadrao"
                                            style="width: 24px; height: 24px; display: none"
                                            title="#{SuperControle.titleUsuario}"
                                            url="/imagens_flat/user-image-default.svg">
                            </h:graphicImage>
                        </a>
                    </div>
            </div>
        </div>
    </div>
</div>
<h:panelGroup layout="block" styleClass="caixa-modulos-zw-ui" id="caixa-modulos-zw-ui">
    <div class="aux grid grid-2c">
        <div class="menu-explorar-column">
            <div class="menu-explorar-column-name">
                Módulos
            </div>

            <script>
                const moduloAberto = '${LoginControle.moduloAberto.sigla}';
                document.addEventListener('DOMContentLoaded', function() {
                    const modulos = document.getElementsByClassName("menu-modulo");
                    for (let i = 0; i < modulos.length; i++) {
                        modulos[i].addEventListener('mouseenter', function(e){
                            const todosMenusModulo = document.getElementsByClassName('menus-modulo');
                            for (let i = 0; i < todosMenusModulo.length; i++) {
                                todosMenusModulo[i].style.visibility = 'hidden';
                                todosMenusModulo[i].style.display = 'none';
                            }

                            let moduloId = e.target.classList[1];
                            const menusModuloSelecionado = document.getElementsByClassName('menus-'+moduloId);
                            for (let i = 0; i < menusModuloSelecionado.length; i++) {
                                menusModuloSelecionado[i].style.visibility = 'visible';
                                menusModuloSelecionado[i].style.display = 'grid';
                            }
                        });
                    }
                }, false);
                function menuOnMouseEnter(e){
                }
            </script>

            <a4j:repeat value="#{MenuControle.gruposMenuExplorar}" var="menu">
                <h:panelGroup styleClass="menu-modulo modulo-#{menu.modulo.siglaModulo}" layout="block" >
                    <a4j:commandLink action="#{MenuControle.abrirModulo}"
                                     oncomplete="#{MenuControle.oncompleteModulo}"
                                     styleClass="menu-explorar-item #{LoginControle.moduloAberto.sigla == menu.modulo.siglaModulo ? 'selected' : ''}">
                        <div class="img-aux">
                            <h:graphicImage url="#{context}/#{menu.modulo.iconeRelaiveUrl}" />
                        </div>
                        <div class="nome"><h:outputText value="#{menu.modulo.descricao}"/></div>
                        <f:param name="siglaModulo" value="#{menu.modulo.siglaModulo}" />
                        <f:param name="idLocalizacaoMenu" value="EXPLORAR_MODULO" />
                    </a4j:commandLink>
                </h:panelGroup>
            </a4j:repeat>

        </div>
        <style>
            .modulo-aberto {
                background: #1B4166;
                border-radius: 4px 0px 0px 4px;
                width: 4px;
                height: 56px;
                position: absolute;
            }
            .menus-modulo {
                display: grid;
                grid-template-columns: 1fr 1fr 1fr 1fr;
                gap: 12px;
                color: #51555A;
                font-family: 'Nunito Sans';
                font-style: normal;
            }

            .menus-modulo-grupo > ul {
                padding-inline-start: 12px;
                min-width: 200px;
            }

            .menus-modulo-grupo > ul > lh {
                font-weight: 700;
                font-size: 16px;
                line-height: 100%;
                margin-top: 12px;
                display: flex;
                flex-direction: row;
                align-items: center;
                padding: 12px;
                max-width: 220px;
            }

            .menus-modulo-grupo > ul > li {
                background: #FAFAFA;
                border-radius: 4px;
                margin-top: 8px;
                display: flex;
                flex-direction: row;
                align-items: center;
                padding: 12px;
                max-width: 220px;
                cursor: pointer;
            }
            .menus-modulo-grupo > ul > li > a {
                text-decoration: none;
                font-family: 'Nunito Sans';
                font-style: normal;
                font-weight: 600;
                font-size: 14px;
                line-height: 125%;
                color: #51555A;
            }

            .menu-explorar-favorito {
                display: none;
            }
            .menu-explorar-favorito-icon {
                font-size: 14px;
                text-decoration: none;
                cursor: pointer;
                color: #1E60FA
            }

            .menu-explorar-favorito-icon:hover {
                text-decoration: none;
            }

            .menu-explorar-item:hover{
                background-color: #C7C9CC;
            }
            .menu-explorar-item.item-menu:hover {
                background-color: #dcdddf;
                .menu-explorar-favorito {
                    display: block;
                }
            }

            .item-menu-explorar {
                background: none !important;
            }

            .div-item-menu-explorar {
                display: grid !important;
                grid-template-columns: 10fr 1fr;
            }

            .div-item-menu-explorar:hover {
                background: #C7C9CC;
            }
        </style>
        <script>
            function menuItemOnClick(e){
                for (let i = 0; i < e.children.length; i++) {
                    if(e.children[i].tagName === "A"){
                        e.children[i].click();
                    }
                }
            }
        </script>
        <c:if test="${MenuControle.exibirMenuExplorar}">
            <h:panelGroup id="menu-explorar-menus-container"  layout="block" styleClass="grid grid-4c" style="overflow-y: auto; overflow-x: hidden; height: calc(90vh - (48px + 78px));">
                <a4j:repeat value="#{MenuControle.gruposMenuExplorar}" var="modulo">
                    <h:panelGroup
                            layout="block"
                            style="#{LoginControle.moduloAberto.sigla == modulo.modulo.siglaModulo ? 'visibility: visible; display: grid;' : 'visibility: hidden; display: none;'}"
                            styleClass="menus-modulo menus-modulo-#{modulo.modulo.siglaModulo}">
                        <a4j:repeat value="#{modulo.menuExplorarConfigs}" var="grupo">
                            <h:panelGroup layout="block" styleClass="menu-explorar-column">
                                <div class="menu-explorar-column-name"><h:outputText value="#{grupo.grupo.descricao}"/></div>
                                <a4j:repeat value="#{grupo.funcionalidadesFavorito}" var="funcionalidade">
                                    <div class="menu-${funcionalidade.funcionalidade.name} menu-explorar-item item-menu div-item-menu-explorar">
                                        <a4j:commandLink
                                                value="#{funcionalidade.descricaoMenulateral}"
                                                style="padding: 0; margin: 0"
                                                styleClass="menu-#{funcionalidade.funcionalidade.name} menu-explorar-item item-menu item-menu-explorar"
                                                title="#{empty funcionalidade.funcionalidade.descricaoMenulateral ? funcionalidade.funcionalidade.descricao : funcionalidade.funcionalidade.descricaoMenulateral}"
                                                actionListener="#{FuncionalidadeControle.prepararFuncionalidade}"
                                                oncomplete="#{FuncionalidadeControle.abrirPopUp};reRenderMenuLateral()"
                                                action="#{FuncionalidadeControle.abrirComCasoNavegacao}"
                                                reRender="#{funcionalidade.funcionalidade.reRenderElement}">
                                            <f:attribute name="funcionalidade" value="#{funcionalidade.funcionalidade.name}"/>
                                            <f:attribute name="idLocalizacaoMenu" value="EXPLORAR_RECURSO"/>
                                            <f:param name="funcionalidadeAberta" value="#{funcionalidade.funcionalidade.name}"/>
                                        </a4j:commandLink>
                                        <div class="menu-explorar-favorito">
                                            <a4j:commandLink  status="false"
                                                              styleClass="menu-explorar-favorito-icon"
                                                              reRender="form:menu-explorar-menus-container, form:fixados-itens"
                                                              rendered="#{funcionalidade.favorito}"
                                                              actionListener="#{MenuAcessoFacilControle.removerFavorito}"
                                                              oncomplete="#{MenuAcessoFacilControle.resultFavorito}"
                                                              title="Remover">
                                                <i class="pct pct-unpin"></i>
                                                <f:attribute name="funcionalidade" value="#{funcionalidade.funcionalidade}"/>
                                            </a4j:commandLink>
                                            <a4j:commandLink  status="false"
                                                              styleClass="menu-explorar-favorito-icon"
                                                              reRender="form:menu-explorar-menus-container, form:fixados-itens"
                                                              rendered="#{!funcionalidade.favorito}"
                                                              actionListener="#{MenuAcessoFacilControle.addFavoritoFuncionalidade}"
                                                              oncomplete="#{MenuAcessoFacilControle.resultFavorito}"
                                                              title="Fixar">
                                                <i class="pct pct-pin"></i>
                                                <f:attribute name="funcionalidade" value="#{funcionalidade.funcionalidade}"/>
                                            </a4j:commandLink>
                                        </div>
                                    </div>
                                </a4j:repeat>
                                <a4j:repeat value="#{grupo.subgrupos}" var="subgrupo">
                                    <h:panelGroup rendered="#{fn:length(subgrupo.funcionalidadesFavorito) > 0}" layout="block" styleClass="menu-explorar-column-submenu-name"><h:outputText value="#{subgrupo.subgrupoEnum.descricao}"/></h:panelGroup>
                                    <a4j:repeat value="#{subgrupo.funcionalidadesFavorito}" var="funcionalidadeSubgrupo">
                                        <div class="menu-${funcionalidadeSubgrupo.funcionalidade.name} menu-explorar-item item-menu div-item-menu-explorar">
                                            <a4j:commandLink
                                                    value="#{funcionalidadeSubgrupo.descricaoMenulateral}"
                                                    style="padding: 0; margin: 0"
                                                    styleClass="menu-#{funcionalidadeSubgrupo.funcionalidade.name} menu-explorar-item item-menu item-menu-explorar"
                                                    title="#{empty funcionalidadeSubgrupo.funcionalidade.descricaoMenulateral ? funcionalidadeSubgrupo.funcionalidade.descricao : funcionalidadeSubgrupo.funcionalidade.descricaoMenulateral}"
                                                    actionListener="#{FuncionalidadeControle.prepararFuncionalidade}"
                                                    oncomplete="#{FuncionalidadeControle.abrirPopUp};reRenderMenuLateral()"
                                                    action="#{FuncionalidadeControle.abrirComCasoNavegacao}"
                                                    reRender="#{funcionalidadeSubgrupo.funcionalidade.reRenderElement}">
                                                <f:attribute name="funcionalidade"
                                                             value="#{funcionalidadeSubgrupo.funcionalidade.name}"/>
                                                <f:attribute name="idLocalizacaoMenu" value="EXPLORAR_RECURSO"/>
                                                <f:param name="funcionalidadeAberta"
                                                         value="#{funcionalidadeSubgrupo.funcionalidade.name}"/>
                                            </a4j:commandLink>
                                            <div class="menu-explorar-favorito">
                                                <a4j:commandLink  reRender="form:menu-explorar-menus-container"
                                                                  styleClass="menu-explorar-favorito-icon"
                                                                  rendered="#{funcionalidadeSubgrupo.favorito}"
                                                                  actionListener="#{MenuAcessoFacilControle.removerFavorito}"
                                                                  oncomplete="#{MenuAcessoFacilControle.resultFavorito}"
                                                                  title="Remover">
                                                    <i class="pct pct-unpin"></i>
                                                    <f:attribute name="funcionalidade" value="#{funcionalidadeSubgrupo.funcionalidade}"/>
                                                </a4j:commandLink>
                                                <a4j:commandLink  reRender="form:menu-explorar-menus-container, form:fixados-itens"
                                                                  styleClass="menu-explorar-favorito-icon"
                                                                  rendered="#{!funcionalidadeSubgrupo.favorito}"
                                                                  actionListener="#{MenuAcessoFacilControle.addFavoritoFuncionalidade}"
                                                                  oncomplete="#{MenuAcessoFacilControle.resultFavorito}"
                                                                  title="Fixar">
                                                    <i class="pct pct-pin"></i>
                                                    <f:attribute name="funcionalidade" value="#{funcionalidadeSubgrupo.funcionalidade}"/>
                                                </a4j:commandLink>
                                            </div>
                                        </div>
                                    </a4j:repeat>
                                </a4j:repeat>
                            </h:panelGroup>
                        </a4j:repeat>
                    </h:panelGroup>
                </a4j:repeat>
            </h:panelGroup>
        </c:if>
    </div>
</h:panelGroup>
