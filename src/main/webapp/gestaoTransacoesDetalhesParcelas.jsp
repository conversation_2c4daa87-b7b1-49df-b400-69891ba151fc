<%@page contentType="text/html" %>
<%@page pageEncoding="ISO-8859-1" %>
<head>
    <%@include file="includes/include_import_minifiles.jsp" %>
</head>
<script type="text/javascript" language="javascript" src="../hoverform.js"></script>
<link href="./css/otimize.css" rel="stylesheet" type="text/css">
<link href="./beta/css/font-awesome_2.0.min.css" rel="stylesheet" type="text/css">
<script src="bootstrap/jquery.js" type="text/javascript"></script>
<script type="text/javascript" src="script/tooltipster/jquery.tooltipster.min.js"></script>
<%@taglib prefix="f" uri="http://java.sun.com/jsf/core" %>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>

<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax" %>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich" %>

<f:loadBundle var="msg_bt" basename="propriedades.Botoes"/>
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao"/>
<f:loadBundle var="msg" basename="propriedades.Mensagens"/>

<f:view>
    <jsp:include page="includes/include_carregando_ripple.jsp" flush="true"/>
    <title>
        <h:outputText value="Parcelas"/>
    </title>
    <h:panelGrid columns="1" width="100%" cellpadding="0" cellspacing="0">
        <html>
        <h:form id="form">
            <c:set var="titulo" scope="session" value="Parcelas"/>
            <c:set var="urlWiki" scope="session" value="semWiki"/>
            <h:panelGroup layout="block" styleClass="pure-g-r">
                <f:facet name="header">
                    <jsp:include page="topo_reduzido_popUp.jsp"/>
                </f:facet>
            </h:panelGroup>
            <h:panelGrid columns="1" width="100%">
                <h:panelGrid width="100%" style="text-align: right">
                    <h:panelGroup layout="block">
                        <a4j:commandLink id="exportarExcel"
                                         actionListener="#{ExportadorListaControle.exportar}"
                                         rendered="#{not empty GestaoTransacoesControle.listaParcelasTransacao}"
                                         oncomplete="abrirPopup('UpdateServlet?op=downloadfile&file=#{ExportadorListaControle.fileName}&mimetype=application/vnd.ms-excel','Transacoes', 800,200);#{ExportadorListaControle.msgAlert}"
                                         accesskey="2" styleClass="linkPadrao">
                            <f:attribute name="lista" value="#{GestaoTransacoesControle.listaParcelasTransacao}"/>
                            <f:attribute name="tipo" value="xls"/>
                            <f:attribute name="atributos" value="#{GestaoTransacoesControle.atributosExportar}"/>
                            <f:attribute name="prefixo" value="ListaParcelas"/>
                            <f:attribute name="titulo" value="Parcelas"/>
                            <h:outputText title="Exportar para o formato Excel" styleClass="btn-print-2 excel"/>
                        </a4j:commandLink>
                    </h:panelGroup>
                </h:panelGrid>
                <rich:dataTable width="100%" styleClass="tabelaSimplesCustom" id="tabelaRes"
                                value="#{GestaoTransacoesControle.listaParcelasTransacao}" rows="50" var="parcela"
                                rowKeyVar="status">
                    <rich:column sortBy="#{parcela.matricula}" styleClass="col-text-align-left"
                                 headerClass="col-text-align-left">
                        <f:facet name="header">
                            <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font" value="MATRÍCULA"/>
                        </f:facet>
                        <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font"
                                      value="#{parcela.matricula}"/>
                    </rich:column>
                    <rich:column sortBy="#{parcela.nomeCliente}" styleClass="col-text-align-left"
                                 headerClass="col-text-align-left">
                        <f:facet name="header">
                            <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font" value="CLIENTE"/>
                        </f:facet>
                        <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font"
                                      value="#{parcela.nomeCliente}"/>
                    </rich:column>
                    <rich:column sortBy="#{parcela.situacaoParcela_Apresentar}" styleClass="col-text-align-left"
                                 headerClass="col-text-align-left">
                        <f:facet name="header">
                            <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font" value="SITUAÇÃO"/>
                        </f:facet>
                        <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font tooltipster"
                                      value="#{parcela.situacaoParcela_Apresentar}"/>
                    </rich:column>
                    <rich:column sortBy="#{parcela.descricaoParcela}" styleClass="col-text-align-left"
                                 headerClass="col-text-align-left">
                        <f:facet name="header">
                            <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font" value="PARCELA"/>
                        </f:facet>
                        <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font"
                                      value="#{parcela.descricaoParcela}"/>
                    </rich:column>
                    <rich:column sortBy="#{parcela.valorParcela}" styleClass="col-text-align-left"
                                 headerClass="col-text-align-left">
                        <f:facet name="header">
                            <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font" value="VALOR"/>
                        </f:facet>
                        <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font"
                                      value="#{GestaoTransacoesControle.empresaLogado.moeda} #{parcela.valorParcela_Apresentar}"/>
                    </rich:column>
                    <rich:column styleClass="col-text-align-center" headerClass="col-text-align-center">
                        <a4j:commandLink styleClass="linkPadrao texto-size-16-real texto-cor-azul"
                                         action="#{GestaoTransacoesControle.irParaTelaCliente}"
                                         oncomplete="abrirPopup('clienteNav.jsp?page=viewCliente', 'Cliente', 1024, 700);">
                            <f:param name="state" value="AC"/>
                            <i class="fa-icon-search"></i>
                        </a4j:commandLink>
                    </rich:column>
                </rich:dataTable>
                <rich:datascroller styleClass="scrollPureCustom" renderIfSinglePage="false" align="center"
                                   for="form:tabelaRes" maxPages="10" id="sctabelaRes"/>
            </h:panelGrid>
        </h:form>
        </body>
        </html>
    </h:panelGrid>
</f:view>

