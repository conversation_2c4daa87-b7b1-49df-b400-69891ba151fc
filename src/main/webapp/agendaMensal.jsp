<%@page contentType="text/html" pageEncoding="ISO-8859-1"%>
<%@include file="/include_imports.jsp" %>
<jsp:include page="pages/estudio/includes/include_head.jsp" />
<c:set var="moduloSession" value="1" scope="session" />

<f:view>
    <jsp:include page="includes/include_carregando_ripple.jsp" flush="true"/>
    <html>

        <!-- Inclui o elemento HEAD da página -->
        <head>        	            
            <script type="text/javascript" language="javascript" src="${root}/script/telaInicial.js"></script>
            <link href="${root}/css_pacto.css" rel="stylesheet" type="text/css">
            <link href="${root}/css/otimize.css" rel="stylesheet" type="text/css">
            <link href="${root}/css/estudio.css" rel="stylesheet" type="text/css">
            <link href="${root}/css/otimizeCRM.css" rel="stylesheet" type="text/css">
            <link href="${root}/css/smartbox/smartbox.css" rel="stylesheet" type="text/css">
        </head>
        <body>

            <h:form id="formAgenda" prependId="false">


                <h:panelGroup layout="block" style="display: inline-table;width: 100%;" styleClass="fundoCinza">
                    <h:panelGroup layout="block" styleClass="bgtop topoZW" rendered="#{MenuControle.apresentarTopo}">
                        <c:set var="contexto" value="${pageContext.request.contextPath}" scope="page" />
                        <jsp:include page="include_topo_novo.jsp" flush="true"/>
                        <jsp:include page="include_menu_zw_flat.jsp" flush="true"/>
                        <rich:jQuery selector=".item6" query="addClass('menuItemAtual')"/>
                    </h:panelGroup>

                    <h:panelGroup layout="block" styleClass="caixaCorpo">
                        <h:panelGroup layout="block" style="height: 80%;width: 100%">
                            <h:panelGroup layout="block" style="display: block;">
                                    <div style="text-align:center;">
                                        <table width="100%" border="0" align="center" cellpadding="0" cellspacing="0" class="text" style="margin-bottom:20px;">
                                            <tr>
                                                    <%--CONTEUDO AGENDA MENSAL--%>
                                                <td id="lateralRetrair" align="left" valign="top">
                                                    <h:panelGroup id="expansores" layout="block">

                                                        <h:panelGroup id="expansor1" styleClass="menulateral_restaurado" rendered="#{SuperControle.exibirMenuLateralEstudio}">
                                                            <a4j:commandButton
                                                                    image="/css/smartbox/botao_minimizar.png"
                                                                    actionListener="#{SuperControle.toggleMenuLateralEstudio}"
                                                                    status="statusHora"
                                                                    reRender="tabelaConteudo,expansores"/>
                                                        </h:panelGroup>

                                                        <h:panelGroup id="expansor2" styleClass="menulateral_retraido" rendered="#{!SuperControle.exibirMenuLateralEstudio}">
                                                            <a4j:commandButton
                                                                    image="/css/smartbox/botao_maximizar.png"
                                                                    actionListener="#{SuperControle.toggleMenuLateralEstudio}"
                                                                    status="agendaGeral"
                                                                    reRender="tabelaConteudo,expansores"/>
                                                        </h:panelGroup>
                                                    </h:panelGroup>

                                                    <h:panelGrid width="100%" border="0" id="tabelaConteudo"
                                                                 columns="3"
                                                                 cellpadding="0" cellspacing="0">

                                                        <rich:column width="237" styleClass="#{SuperControle.exibirMenuLateralEstudio ? 'mostra' : 'esconde'}" style="padding: 0px 5px 0px 0px;vertical-align: top;">
                                                            <h:panelGroup layout="block" rendered="#{SuperControle.exibirMenuLateralEstudio}">
                                                                <jsp:include page="pages/estudio/includes/include_box_menulateral_sc.jsp" />
                                                            </h:panelGroup>

                                                        </rich:column>

                                                        <rich:column style="vertical-align: top;" width="100%">
                                                            <h:panelGroup layout="block" styleClass="container-box zw_ui especial ">
                                                                <h:panelGroup styleClass="container-box-header" layout="block">
                                                                    <h:panelGroup layout="block" styleClass="margin-box">
                                                                        <h:outputText value="Agenda Mensal" styleClass="container-header-titulo"/>
                                                                        <h:outputLink styleClass="linkWiki"
                                                                                      value="#{SuperControle.urlBaseConhecimento}como-ver-a-agenda-mensal-do-modulo-studio/"
                                                                                      title="Agenda Mensal"
                                                                                      target="_blank">
                                                                            <i class="fa-icon-question-sign" style="font-size: 18px"></i>
                                                                        </h:outputLink>
                                                                    </h:panelGroup>

                                                                </h:panelGroup>
                                                                <h:panelGroup layout="block" styleClass="margin-box">
                                                                    <rich:calendar value="#{agendaMensalController.calendarioDataModel.currentDate}"
                                                                                   id="myCalendar" popup="false"
                                                                                   style="width: 100%;min-height: 50%"
                                                                                   dataModel="#{agendaMensalController.calendarioDataModel}"
                                                                                   datePattern="dd/MM/yyyy HH:mm"
                                                                                   showWeeksBar="false"
                                                                                   boundaryDatesMode="none"
                                                                                   showApplyButton="false"
                                                                                   weekDayLabelsShort="#{agendaMensalController.week}">
                                                                        <a4j:support event="oncurrentdateselected" action="#{agendaMensalController.acaoMudarMes}"
                                                                                     reRender="myCalendar, cell, formAgenda" />
                                                                        <f:facet name="header">

                                                                            <h:panelGrid columns="2">
                                                                                <h:panelGrid columns="3" style="font-weight:bold; text-align:left">
                                                                                    <h:outputText value="{previousMonthControl}" style="font-weight:bold;"/>
                                                                                    <h:outputText value="{currentMonthControl}" style="font-weight:bold;"/>
                                                                                    <h:outputText value="{nextMonthControl}" style="font-weight:bold;" />
                                                                                </h:panelGrid>
                                                                            </h:panelGrid>
                                                                        </f:facet>

                                                                        <f:facet name="weekDay">

                                                                            <h:panelGroup style="overflow:hidden;" layout="block">

                                                                                <h:outputText value="{weekDayLabelShort}"/>

                                                                            </h:panelGroup>

                                                                        </f:facet>

                                                                        <a4j:outputPanel layout="block" id="cell" onclick="#{rich:component('myCalendar')}.resetSelectedDate()"
                                                                                         style="height: 80px; overflow-y: auto; width: 100%; overflow-x: hidden;" >
                                                                            <div>
                                                                                <h:outputText value="{day}" style="align:center"/>
                                                                            </div>
                                                                            <div style="text-align: left; padding-left: 5px;">
                                                                                <div>
                                                                                    <h:outputText value="{data.totalSessao}" style="font-weight: bold; text-decoration: underline; " />
                                                                                    <div style="visibility: hidden; float: right; " >

                                                                                        <h:commandButton value="{data.naoPaga}"
                                                                                                         style="border-color: transparent; background-color: transparent; background-image: url(../imagens/estudio/botaoFundo.png); background-size: 100%; font-weight: bold; font-size:11px; padding-right: 5px; cursor: pointer; visibility: {data.bool}"
                                                                                                         action="#{agendaMensalController.acaoBuscarDetalhes}" >
                                                                                            <a4j:support event="onclick" reRender="modalPanelDetalhes"/>
                                                                                        </h:commandButton>
                                                                                    </div>
                                                                                </div>
                                                                                <div>
                                                                                    <h:outputText value="{data.b}" />
                                                                                </div>
                                                                                <div>
                                                                                    <h:outputText style="color:green;" value="{data.c}" />
                                                                                </div>
                                                                                <div>
                                                                                    <h:outputText style="color:red;" value="{data.x}" />
                                                                                </div>
                                                                                <div>
                                                                                    <h:outputText style="color:red;" value="{data.f}" />
                                                                                </div>
                                                                                <div>
                                                                                    <h:outputText style="color:green;" value="{data.m}" />
                                                                                </div>
                                                                                <div>
                                                                                    <h:outputText style="color:orange;" value="{data.r}" />
                                                                                </div>
                                                                            </div>
                                                                        </a4j:outputPanel>




                                                                    </rich:calendar>
                                                                </h:panelGroup>
                                                            </h:panelGroup>
                                                        </rich:column>
                                                    </h:panelGrid>
                                                </td>
                                                    <%--CONTEUDO AGENDA MENSAL - FIM--%>
                                            </tr>
                                        </table>
                                    </div>
                            </h:panelGroup>
                        </h:panelGroup>
                    </h:panelGroup>

                    <jsp:include page="include_rodape_flat.jsp" flush="true"/>
                </h:panelGroup>

            </h:form>

            <rich:modalPanel id="modalPanelDetalhes" autosized="true" shadowOpacity="true" 
                             showWhenRendered="#{agendaMensalController.apresentarDetalhes}" width="525" 
                             height="155" >
                <f:facet name="header">
                    <h:panelGroup>
                        <h:outputText value="Detalhes"></h:outputText>
                    </h:panelGroup>
                </f:facet>
                <a4j:form id="formModalPanelDetalhes" prependId="false" ajaxSingle="true" ajaxSubmit="true">
                    <rich:scrollableDataTable value="#{agendaMensalController.listaDetalhes}" var="item" height="150" width="100%" style="border: 0;">
                        <rich:column title="Status" sortable="false" width="88" style="padding-left: 2px;">
                            <f:facet name="header" >
                                <h:outputText value="Status"/>
                            </f:facet>
                            <h:outputText value="#{item.status.descricao}" 
                                          style="margin-left: 5px; position:relative;" title="#{item.status.descricao}"/>
                        </rich:column>
                        <rich:column title="Nome do Cliente" sortable="false" width="180" style="padding-left: 2px;">
                            <f:facet name="header" >
                                <h:outputText value="Nome do Cliente"/>
                            </f:facet>
                            <h:outputText value="#{item.clienteVO.pessoa.nome}" 
                                          style="margin-left: 5px; position:relative;" title="#{item.clienteVO.pessoa.nome}"/>
                        </rich:column>
                        <rich:column title="Serviço" sortable="false" width="90" style="padding-left: 2px;">
                            <f:facet name="header" >
                                <h:outputText value="Serviço" />
                            </f:facet>
                            <h:outputText value="#{item.produtoVO.descricao}" 
                                          style="margin-left: 5px; position:relative;" title="#{item.produtoVO.descricao}"/>
                        </rich:column>
                        <rich:column title="Agendamento" sortable="false" width="72" style="padding-left: 2px;">
                            <f:facet name="header" >
                                <h:outputText value="Agendamento"/>
                            </f:facet>
                            <h:outputText value="#{item.tipoHorarioVO.descricao}" 
                                          style="margin-left: 5px; position:relative;" title="#{item.tipoHorarioVO.descricao}"/>
                        </rich:column>
                        <rich:column title="Preço" sortable="false" width="53" style="padding-left: 2px;">
                            <f:facet name="header" >
                                <h:outputText value="Preço"/>
                            </f:facet>
                            <h:outputText style="float:right; margin-right: 5px;" value="#{item.valor}" 
                                          title="#{item.valor}">
                                <f:convertNumber maxFractionDigits="2" groupingUsed="true" maxIntegerDigits="14" type="currency" currencySymbol="R$ " />
                            </h:outputText>
                        </rich:column>
                    </rich:scrollableDataTable>
                    <rich:spacer height="05px"/>
                    <h:panelGrid style="position: relative; float:right; ">
                        <a4j:commandButton
                            image="../imagens/estudio/fechar.png"
                            id="okButton"
                            value="Fechar"
                            title="Fechar"
                            status="statusHora"
                            action="#{agendaMensalController.acaoFecharDetalhes}" reRender="modalPanelDetalhes" />
                    </h:panelGrid>
                </a4j:form>
            </rich:modalPanel>
        </f:view>


    </body>
</html>

