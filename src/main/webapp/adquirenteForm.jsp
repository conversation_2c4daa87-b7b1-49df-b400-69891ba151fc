<%@page contentType="text/html;charset=UTF-8" %>
<head>
    <%@include file="/includes/include_import_minifiles.jsp" %>
</head>
<link href="./css/otimize.css" rel="stylesheet" type="text/css">
<link href="beta/css/font-awesome_2.0.min.css" type="text/css" rel="stylesheet"/>
<script type="text/javascript" language="javascript" src="hoverform.js"></script>
<%@taglib prefix="f" uri="http://java.sun.com/jsf/core" %>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html" %>
<%@taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax" %>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich" %>
<f:loadBundle var="msg_bt" basename="propriedades.Botoes"/>
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao"/>
<f:loadBundle var="msg" basename="propriedades.Mensagens"/>

<%@include file="/includes/verificaModulo.jsp" %>
<c:set var="contexto" value="${pageContext.request.contextPath}" scope="request"/>

<f:view locale="#{SuperControle.idioma}">

    <title>
        <h:outputText value="#{msg_aplic.prt_Adquirente_tituloForm}"/>
    </title>
    <c:set var="titulo" scope="session" value="${msg_aplic.prt_Adquirente_tituloForm}"/>
    <c:set var="urlWiki" scope="session" value="${SuperControle.urlWiki}Entidade:Pais"/>
    <h:panelGrid columns="1" width="100%" cellpadding="0" cellspacing="0">
        <f:facet name="header">
            <jsp:include page="topoReduzido_material.jsp"/>
        </f:facet>

        <h:form id="form">

            <input type="hidden" value="${modulo}" name="modulo"/>
            <hr style="border-color: #e6e6e6;"/>
            <h:commandLink action="#{AdquirenteControle.liberarBackingBeanMemoria}" id="idLiberarBackingBeanMemoria"
                           style="display: none"/>
            <h:panelGrid columns="1" width="100%">
                <h:panelGrid columns="2" rowClasses="linhaImpar, linhaPar" columnClasses="classEsquerda, classDireita"
                             width="100%">

                    <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_Pais_codigo}"/>
                    <h:panelGroup>
                        <h:inputText id="codigo" size="10" maxlength="10" readonly="true"
                                     styleClass="camposSomenteLeitura" onblur="blurinput(this);"
                                     onfocus="focusinput(this);" value="#{AdquirenteControle.adquirenteVO.codigo}"/>
                        <h:message for="codigo" styleClass="mensagemDetalhada"/>
                    </h:panelGroup>

                    <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_adquirente_ativa}" />
                    <h:selectBooleanCheckbox id="ativo" value="#{AdquirenteControle.adquirenteVO.situacao}"/>

                    <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_Pais_nome}"/>

                    <h:panelGroup>
<%--                        <c:if test="${!AdquirenteControle.isUruguay()}">--%>
                            <h:inputText id="nome" size="40" maxlength="40" styleClass="form" onblur="blurinput(this);"
                                         onfocus="focusinput(this);" value="#{AdquirenteControle.adquirenteVO.nome}"/>
<%--                        </c:if>--%>

<%--                        <c:if test="${AdquirenteControle.isUruguay() }">--%>
<%--                            <h:selectOneMenu id="nomeGeoitdAdquirente" styleClass="form" onblur="blurinput(this);"--%>
<%--                                             onfocus="focusinput(this);"--%>
<%--                                             value="#{AdquirenteControle.adquirenteVO.nome}">--%>
<%--                                <f:selectItems value="#{AdquirenteControle.listaAdquirenteGeoitd}"/>--%>
<%--                            </h:selectOneMenu>--%>

<%--                        </c:if>--%>

                    </h:panelGroup>

                    <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_ConvenioCobranca_tipoConvenio}"/>


                    <h:selectOneMenu id="tipoConvenio" styleClass="form" onblur="blurinput(this);"
                                     onfocus="focusinput(this);"
                                     value="#{AdquirenteControle.adquirenteVO.tipoConvenio}">
                        <f:selectItems value="#{AdquirenteControle.listaSelectItemTipoConvenio}"/>
                    </h:selectOneMenu>
                    <c:if test="${AdquirenteControle.configuracaoSistema.utilizarServicoSesiSC}">
                        <h:outputText  styleClass="tituloCampos" value="CNPJ:"/>

                        <h:inputText id="CNPJ" size="20" maxlength="18"
                                 onkeypress="return mascara(this.form, 'form:CNPJ', '99.999.999/9999-99', event);"
                                 onblur="blurinput(this);" onfocus="focusinput(this);" styleClass="form"
                                 value="#{AdquirenteControle.adquirenteVO.cnpj}"/>
                    </c:if>



                </h:panelGrid>

                <h:panelGrid columns="1" width="100%" styleClass="tabMensagens">
                    <h:panelGrid id="panelMensagemErro" columns="3" width="100%" styleClass="tabMensagens">
                        <h:panelGrid columns="1" width="100%">

                            <h:outputText value=" "/>

                        </h:panelGrid>
                        <h:commandButton rendered="#{AdquirenteControle.sucesso}" image="./imagens/sucesso.png"/>
                        <h:commandButton rendered="#{AdquirenteControle.erro}" image="./imagens/erro.png"/>
                        <h:panelGrid columns="1" width="100%">
                            <h:outputText styleClass="mensagem" value="#{AdquirenteControle.mensagem}"/>
                            <h:outputText styleClass="mensagemDetalhada"
                                          value="#{AdquirenteControle.mensagemDetalhada}"/>
                        </h:panelGrid>
                    </h:panelGrid>
                    <h:panelGrid columns="1" width="100%" columnClasses="colunaCentralizada">
                        <h:panelGroup>
                            <a4j:commandButton id="novo" immediate="true" action="#{AdquirenteControle.novo}"
                                               value="#{msg_bt.btn_novo}" alt="#{msg.msg_novo_dados}" accesskey="1"
                                               styleClass="botoes nvoBt btSec"/>

                            <h:outputText value="    "/>

                            <a4j:commandButton id="salvar" oncomplete="#{AdquirenteControle.msgAlert}" action="#{AdquirenteControle.gravar}"
                                               value="#{msg_bt.btn_gravar}" title="#{msg.msg_gravar_dados}" alt="#{msg.msg_gravar_dados}" accesskey="2"
                                               styleClass="botoes nvoBt" reRender="me" />

                            <h:outputText value="    "/>

                            <h:panelGroup id="grupoBtnExcluir">
                                <a4j:commandButton id="excluir"  reRender="mdlMensagemGenerica"
                                                 oncomplete="#{AdquirenteControle.msgAlert}" action="#{AdquirenteControle.confirmarExcluir}"
                                                 value="#{msg_bt.btn_excluir}" title="#{msg.msg_excluir_dados}" accesskey="3" styleClass="botoes nvoBt btSec btPerigo"/>
                            </h:panelGroup>

                            <h:outputText value="    "/>

                            <a4j:commandButton id="consultar" immediate="true"
                                               action="#{AdquirenteControle.inicializarConsultar}"
                                               value="#{msg_bt.btn_voltar_lista}" alt="#{msg.msg_consultar_dados}"
                                               accesskey="4" styleClass="botoes nvoBt btSec"/>
                            <rich:spacer width="15px"/>
                            <a4j:commandLink action="#{AdquirenteControle.realizarConsultaLogObjetoSelecionado}" reRender="form"
                                             oncomplete="abrirPopup('../faces/visualizadorLogForm.jsp', 'VisualizadorLog', 785, 595);"
                                             title="Visualizar Log"
                                             style="display: inline-block; padding: 8px 15px; margin-left: -6px;"
                                             styleClass="botoes nvoBt btSec">
                                <i style="text-decoration: none" class="fa-icon-list"/>
                            </a4j:commandLink>
                        </h:panelGroup>


                    </h:panelGrid>
                </h:panelGrid>
            </h:panelGrid>
        </h:form>
    </h:panelGrid>
    <jsp:include page="includes/include_carregando_ripple.jsp" flush="true"/>
    <%@include file="includes/include_modal_mensagem_generica.jsp"%>
</f:view>
<script>
    document.getElementById("form:nome").focus();
</script>
