<%@ taglib prefix="tags" tagdir="/WEB-INF/tags" %>
<%@page contentType="text/html" %>
<%@page pageEncoding="ISO-8859-1" %>
<%@include file="includes/imports.jsp" %>
<h:panelGroup styleClass="topoModulos" layout="block" style="">

    <jsp:include page="include_localize_traducao_linguagem.jsp"/>
<%--    <jsp:include page="include_topo_conhecimento_UCP.jsp"/>--%>

    <h:panelGroup layout="block" styleClass="blocoModulos col-md-4 pull-right">
        <h:panelGroup layout="block" styleClass="moduloAberto #{LoginControle.moduloAberto.sigla}"/>
        <h:panelGroup layout="block" styleClass="moduloFechados">
            <a4j:repeat value="#{LoginControle.modulosHabilitados}" var="modulo" id="modulos">
                <a4j:commandLink action="#{LoginControle.getAbrirModulo}"
                                 style="width: #{LoginControle.tamanhoModulosAbertos}%"
                                 rendered="#{not fn:contains(modulo.sigla,LoginControle.moduloAberto.sigla) && not fn:contains(modulo.sigla,'TR') && not fn:contains(modulo.sigla,'SLC') && not fn:contains(modulo.sigla,'NFSE') && not fn:contains(modulo.sigla,'UCP')}"
                                 styleClass="moduloFechado #{modulo.sigla}"
                                 id="abrirModulo">
                </a4j:commandLink>
                <h:outputLink  value="#{LoginControle.abrirModulo}" target="_blank"
                               style="width: #{LoginControle.tamanhoModulosAbertos}%"
                               rendered="#{not fn:contains(modulo.sigla,LoginControle.moduloAberto.sigla) && ((fn:contains(modulo.sigla,'TR') || fn:contains(modulo.sigla,'SLC') || fn:contains(modulo.sigla,'NFSE') || fn:contains(modulo.sigla,'UCP')))}"
                               styleClass="moduloFechado #{modulo.sigla} ">
                </h:outputLink>
            </a4j:repeat>
        </h:panelGroup>

    </h:panelGroup>
</h:panelGroup>
<h:panelGroup id="topoNovo" layout="block" styleClass="topoOpcoes">
    <h:panelGroup layout="block" style="line-height: 3.2;" styleClass="botoesOpcoes pull-right">
        <h:panelGroup layout="block" styleClass="configuracoes" style="height: 70px">
            <h:panelGroup layout="block" style="height: 100%;display: inline-block" styleClass="tudo">
                <h:panelGroup layout="block" styleClass="dropDownMenu"
                              style="float: right;position: relative">
                    <div class="itemsOpcoes menuModulos dropdown-toggle dropdown-border"  data-toggle="dropdown">
                        <h:graphicImage styleClass="iconePadrao" url="/imagens_flat/marcaGente.png"
                                        style="padding-top:39%;padding-left: 26%;display: none"
                                        width="25" height="26"/>
                        <h:graphicImage styleClass="iconeLogado pull-left"
                                        url="/imagens_flat/#{LoginControle.moduloAberto.iconeModulo}"
                                        style="padding-top:39%;padding-left: 26%;" width="25"
                                        height="26"/>
                    </div>
                    <ul class="dropMenuModulo dropdown-menu" role="menu" aria-labelledby="dropdownMenu">
                        <c:if test="${LoginControle.moduloAberto.sigla !=  'ZW'}">
                            <li>
                                <h:panelGroup layout="block" styleClass="item">
                                    <a4j:commandLink action="#{LoginControle.abrirZillyonWeb}"
                                                     value="adm" styleClass="tituloModulos">
                                        <h:graphicImage style="float: left;margin-top: 10px"
                                                        url="/imagens_flat/pct-icone-fundo-administrativo.svg" width="25"
                                                        height="26"/>
                                    </a4j:commandLink>
                                </h:panelGroup>
                            </li>
                        </c:if>
                        <c:if test="${LoginControle.apresentarLinkParaModuloCRM and LoginControle.moduloAberto.sigla !=  'CRM'}">
                            <li>
                                <h:panelGroup layout="block" styleClass="item">
                                    <a4j:commandLink value="crm" actionListener="#{SkinControle.definirSkinCrm}"
                                                     action="#{LoginControle.abrirModuloCRM}"
                                                     styleClass="tituloModulos">
                                        <h:graphicImage style="float: left;margin-top: 10px"
                                                        url="/imagens_flat/pct-icone-fundo-crm.svg" width="25"
                                                        height="26"/>
                                    </a4j:commandLink>
                                </h:panelGroup>
                            </li>
                        </c:if>
                        <c:if test="${LoginControle.apresentarLinkFinanceiro and LoginControle.moduloAberto.sigla !=  'FIN'}">
                            <li>
                                <h:panelGroup layout="block" styleClass="item">
                                    <a4j:commandLink action="#{LoginControle.abrirModuloFinanceiro}"
                                                     value="financeiro"
                                                     actionListener="#{SkinControle.definirSkinFinanceiro}"
                                                     styleClass="tituloModulos">
                                        <h:graphicImage style="float: left;margin-top: 10px"
                                                        url="/imagens_flat/pct-icone-fundo-financeiro.svg" width="25"
                                                        height="26"/>
                                    </a4j:commandLink>
                                </h:panelGroup>
                            </li>
                        </c:if>
                        <c:if test="${LoginControle.apresentarLinkCE and LoginControle.moduloAberto.sigla !=  'CE'}">
                            <li>
                                <h:panelGroup layout="block" styleClass="item">
                                    <a4j:commandLink value="central eventos"
                                                     oncomplete="#{LoginControle.msgAlert}"
                                                     action="#{LoginControle.abrirCE}" styleClass="tituloModulos">
                                        <h:graphicImage style="float: left;margin-top: 10px"
                                                        url="/imagens_flat/pct-icone-fundo-administrativo.svg" width="25"
                                                        height="26"/>
                                    </a4j:commandLink>
                                </h:panelGroup>
                            </li>
                        </c:if>
                        <c:if test="${LoginControle.validarTreinoLoginApp}">
                            <li>
                                <h:panelGroup layout="block" styleClass="item">
                                    <h:outputLink value="#{LoginControle.abrirTreino}" styleClass="tituloModulos">
                                        <h:graphicImage style="float: left;margin-top: 10px"
                                                        url="/imagens_flat/pct-icone-fundo-novo-treino.svg" width="25"
                                                        height="26"/>
                                        <h:outputText value="treino"/>
                                    </h:outputLink>
                                </h:panelGroup>
                            </li>
                        </c:if>
                        <c:if test="${LoginControle.apresentarModuloAulaCheia}">
                            <li>
                                <h:panelGroup layout="block" styleClass="item">
                                    <h:outputLink value="#{LoginControle.abrirAulaCheia}" styleClass="tituloModulos">
                                        <h:graphicImage style="float: left;margin-top: 10px"
                                                        url="/imagens_flat/genteAC.png" width="25"
                                                        height="26"/>
                                        <h:outputText value="aula cheia"/>
                                    </h:outputLink>
                                </h:panelGroup>
                            </li>
                        </c:if>
                        <li>
                            <h:panelGroup layout="block" styleClass="item">
                                <h:outputLink target="_blank"
                                              value="#{LoginControle.linkModuloUCP}" styleClass="tituloModulos">
                                    <h:graphicImage style="float: left;margin-top: 10px"
                                                    url="/imagens_flat/genteUCP.png" width="25"
                                                    height="26"/>
                                    <h:outputText value="ucp"/>
                                </h:outputLink>
                            </h:panelGroup>
                        </li>
                        
                        <c:if test="${LoginControle.permissaoAcessoMenuVO.gameOfResults}">
                            <li>
                                <h:panelGroup layout="block" styleClass="item">
                                    <h:outputLink target="_blank"
                                                  value="#{BIControle.urlGame}" styleClass="tituloModulos">
                                        <h:graphicImage style="float: left;margin-top: 7px"
                                                        url="/imagens_flat/game.png" width="25"
                                                        height="30"/>
                                        <h:outputText style="font-size: 20px;" value="game of results" />
                                    </h:outputLink>
                                </h:panelGroup>
                            </li>
                        </c:if>
                            
                    </ul>
                </h:panelGroup>
            </h:panelGroup>

            <h:panelGroup layout="block" styleClass="boxOpcoes transition-all pull-right"
                          style="display: table;margin-right: 20px;">
                <h:panelGroup layout="block"
                              styleClass="dropDownMenu col-celula finalStep tudo">
                    <h:panelGroup layout="block">
                        <h:outputLink value="https://pactosolucoes.com.br/ajuda/"
                                      title="Central de ajuda" target="_blank" styleClass="tooltipster">
                            <div class="itemsOpcoes dropdown-toggle dropdown-border">
                                <h:outputText styleClass="fa-icon-question-sign btnDropDown aberto"/>
                            </div>
                        </h:outputLink>

                    </h:panelGroup>
                </h:panelGroup>

                <h:panelGroup layout="block"
                              styleClass="dropDownMenu col-celula tudo" rendered="#{!SuperControle.usuarioLogado.administrador && SuperControle.empresaLogado.codigo != 0 && InicioControle.usuarioMultiEmpresa}">
                    <h:panelGroup layout="block">
                        <div style="position: relative;"
                             class="itemsOpcoes iconeTrocaEmpresa  dropdown-toggle  dropdown-border"  data-toggle="dropdown">
                            <h:outputText style="display:block;line-height: 59px;"
                                          styleClass="fa-icon-exchange iconeEmpresa aberto"/>
                            <h:outputText style="display: none;"
                                          styleClass="fa-icon-exchange iconeEmpresa fechado"/>
                        </div>
                        <ul class="dropMenuEmpresa dropdown-menu" role="menu" aria-labelledby="dropdownMenu">
                            <li>
                                <h:panelGroup layout="block"  style="margin-left: 20px;margin-right:20px;text-align: left;width: calc((9.8px * #{InicioControle.tamanhoMaiorNomeEmpresa}) );">
                                    <h:outputText styleClass="sink textSmallFlat3"
                                                  style="color:#BBBDBF; vertical-align: top;text-decoration: none;"
                                                  value="#{InicioControle.empresaLogado.nome}"></h:outputText>
                                </h:panelGroup>
                            </li>
                            <c:forEach items="#{InicioControle.usuarioLogado.usuarioPerfilAcessoVOs}"
                                       var="perfil">
                                <c:if test="${perfil.empresa.codigo != InicioControle.empresaLogado.codigo}">
                                    <li>

                                        <h:panelGroup layout="block" style="margin-left: 20px;margin-right:20px;text-align: left;width: calc((9.8px * #{InicioControle.tamanhoMaiorNomeEmpresa}) );">
                                            <a4j:commandLink
                                                    style="color:#BBBDBF; vertical-align: top;text-decoration: none;"
                                                    value="#{perfil.empresa.nome}"

                                                    action="#{InicioControle.confirmarTrocaEmpresa}"
                                                    styleClass="sink textSmallFlat">
                                                <f:setPropertyActionListener value="#{perfil.empresa.codigo}"
                                                                             target="#{InicioControle.codigoEmpresa}"/>
                                            </a4j:commandLink>
                                        </h:panelGroup>
                                    </li>
                                </c:if>
                            </c:forEach>
                        </ul>
                    </h:panelGroup>
                </h:panelGroup>

                <h:panelGroup layout="block" styleClass="col-celula tudo">
                    <h:panelGroup layout="block" styleClass="itemsOpcoes" id="nrMsgLidas" style="height: 1.35em;">
                        <a4j:commandLink action="#{LoginControle.abrirSocialMailing}" reRender="nrMsgLidas"
                                         oncomplete="#{LoginControle.msgAlert}" style="display: block;text-decoration: none;">
                            <i class="fa-icon-comment itemsOpcoes"></i>
                        </a4j:commandLink>
                        <h:panelGroup layout="block"
                                      rendered="#{UsuarioControle.usuario.nrMensagensNaoLidas > 0}"
                                      style="height: 0;">
                            <a4j:commandLink styleClass="notificacaoSocialMailing" id="nrMsgNaoLidas"
                                             action="#{LoginControle.abrirSocialMailing}"
                                             oncomplete="#{LoginControle.msgAlert}"
                                             reRender="nrMsgLidas"
                                             value="#{UsuarioControle.usuario.nrMensagensNaoLidas}"/>
                        </h:panelGroup>
                    </h:panelGroup>
                </h:panelGroup>

                <h:panelGroup layout="block" rendered="#{not LoginControle.moduloAtualCe && ((LoginControle.moduloAtualZw && LoginControle.permissaoAcessoMenuVO.configuracaoSistema) ||
                (LoginControle.moduloAtualCrm && LoginControle.permissaoAcessoMenuVO.configuracaoSistemaCRM) || (LoginControle.moduloAtualFin && LoginControle.permissaoAcessoMenuVO.configuracaoFinanceiro))}"
                              styleClass="dropDownMenu col-celula tudo">
                    <h:panelGroup layout="block">
                        <h:panelGroup layout="block" style="position: relative;"
                                      styleClass="itemsOpcoes iconeTrocaEmpresa" >
                            <a4j:commandLink styleClass="textSmallFlat"
                                             id="btnConfiguracaoZW"
                                             rendered="#{LoginControle.moduloAtualZw}"
                                             action="#{ConfiguracaoSistemaControle.novo}"
                                             oncomplete="abrirPopup('configuracaoSistemaForm.jsp', 'ConfiguracaoSistema', 1024, 768);">
                                <h:outputText style="display:block;"
                                              styleClass="fa-icon-cog iconeEmpresa itemsOpcoes aberto"/>
                            </a4j:commandLink>
                            <a4j:commandLink id="btnConfiguracaoCRM"
                                             rendered="#{LoginControle.moduloAtualCrm}"
                                             action="#{ConfiguracaoSistemaCRMControle.iniciar}"
                                             styleClass="textSmallFlat"
                                             oncomplete="abrirPopup('configuracaoSistemaCRMForm.jsp', 'configuracaoSistemaCRM', 900, 595);">
                                <h:outputText style="display:block;"
                                              styleClass="fa-icon-cog iconeEmpresa itemsOpcoes aberto"/>
                            </a4j:commandLink>
                            <a4j:commandLink action="#{ConfiguracaoFinanceiroControle.preparaEdicao}"
                                             id="btnConfiguracaoFIN"
                                             rendered="#{LoginControle.moduloAtualFin}"
                                             oncomplete="#{ConfiguracaoFinanceiroControle.msgAlert}"
                                             styleClass="textSmallFlat">
                                <h:outputText style="display:block;"
                                              styleClass="fa-icon-cog iconeEmpresa itemsOpcoes aberto"/>
                            </a4j:commandLink>
                        </h:panelGroup>
                    </h:panelGroup>
                </h:panelGroup>
                <h:panelGroup id="fotoUsuario" layout="block" styleClass="col-celula fotoUsuario fechado dropDownMenu tudo" style="width: 42px;height: 42px;border-radius:50%">
                    <div class="dropdown-toggle col-vert-align"   data-toggle="dropdown" style="width: 100%;height: 100%;position: relative;display: inline-block;width: 40px;border-radius: 50%;" >
                        <a4j:mediaOutput id="fotosNaNuvemFalse"
                                         element="img"
                                         rendered="#{!SuperControle.fotosNaNuvem}"
                                         align="left"
                                         style="#{SuperControle.styleUsuario}"
                                         cacheable="false" session="false"
                                         title="#{SuperControle.titleUsuario}"
                                         createContent="#{SuperControle.paintFotoUsuario}"
                                         value="#{ImagemData}" styleClass="dropdown-toggle" mimeType="image/jpeg">
                            <f:param value="#{SuperControle.timeStamp}" name="time"/>
                            <f:param name="largura" value="50"/>
                            <f:param name="altura" value="50"/>
                        </a4j:mediaOutput>
                        <h:outputText styleClass="icon-hidden fa-icon-caret-down"></h:outputText>
                        <h:graphicImage id="fotosNaNuvemTrue"
                                        rendered="#{SuperControle.fotosNaNuvem}"
                                        width="40" height="40"
                                        style="#{SuperControle.styleUsuario}"
                                        title="#{SuperControle.titleUsuario}"
                                        styleClass="dropdown-toggle"
                                        url="#{SuperControle.fotoKeyUsuarioLogado}">
                        </h:graphicImage>
                    </div>
                    <ul class="dropMenuUsuario dropdown-menu" style="display: none;" role="menu" aria-labelledby="dropdownMenu">
                        <li class="notHover">
                            <div style="width: 84%;float: right;text-align: center;height: 100%;margin-left: 7.5%;line-height: 50px;padding-top:30px">
                                <h:outputText style="line-height: 2px" styleClass="texto-size-14-real texto-cor-cinza texto-bold texto-font" value="#{SuperControle.usuarioLogado.nome}"/><br/>
                                <h:outputText style="line-height: 12px" styleClass="texto-size-14-real texto-cor-cinza texto-font" value="#{SuperControle.empresaLogado.nome}"/>
                            </div>
                        </li>
                        <li class="notHover" >
                            <h:panelGroup layout="block" styleClass="container-fluid img-responsive">
                                <h:graphicImage  id="foto" style="max-width:200px;max-height: 200px"
                                                 value="#{SuperControle.urlFotoEmpresa}" >
                                </h:graphicImage>
                            </h:panelGroup>
                        </li>

                        <div style="width: 100%;height: 1px;background-color: rgb(174, 187, 188);margin:10px 0px;"></div>

                        <c:if test="${LoginControle.permissaoAcessoMenuVO.boletosSistema}">
                        <li>
                            <h:panelGroup layout="block" styleClass="alinhaMentoItemMenu"
                                          style="width: 85%;float: right;text-align: left;height: 100%;margin-left: 7.5%;">
                                    <i class="fa-icon-dollar"></i>
                                    <a4j:commandLink styleClass="textSmallFlat" id="linkFinanceiro"
                                                     action="financeiroPacto">
                                        Boleto Pacto
                                    </a4j:commandLink>
                                </h:panelGroup>
                            </li>
                        </c:if>

                        <div style="width: 100%;height: 1px;background-color: rgb(174, 187, 188);margin:10px 0px;"></div>
                        <li>
                            <h:panelGroup layout="block" styleClass="alinhaMentoItemMenu"
                                          style="width: 85%;float: right;text-align: left;height: 100%;margin-left: 7.5%;">
                                <a4j:commandLink styleClass="textSmallFlat"
                                                 rendered="#{LoginControle.usuario.permiteAlterarPropriaSenha}"
                                                 actionListener="#{FuncionalidadeControle.prepararFuncionalidade}"
                                                 oncomplete="#{FuncionalidadeControle.abrirPopUp}"
                                                 action="#{FuncionalidadeControle.abrirComCasoNavegacao}">
                                    <f:attribute name="funcionalidade" value="DADOS_USUSARIO" />
                                    <h:outputText style="vertical-align: middle;" styleClass="fa-icon-user"/> Meus Dados
                                </a4j:commandLink>
                            </h:panelGroup>
                        </li>

                        <c:if test="${not LoginControle.moduloAtualCe}">
                            <div style="width: 100%;height: 1px;background-color: rgb(174, 187, 188);margin:10px 0px;"></div>
                            <li>
                                <h:panelGroup layout="block" styleClass="alinhaMentoItemMenu"
                                              style="width: 85%;float: right;text-align: left;height: 100%;margin-left: 7.5%;">
                                    <h:commandLink styleClass="textSmallFlat"
                                                   value="Ativar Novo Menu"
                                                   action="#{SuperControle.mudarMenu}"/>
                                </h:panelGroup>
                            </li>
                        </c:if>
                        <div style="width: 80%;height: 1px;margin-left: 10%;background-color: black"></div>
                        <li>
                            <h:panelGroup layout="block" styleClass="alinhaMentoItemMenu"
                                          style="width: 85%;float: right;text-align: left;height: 100%; margin-left: 7.5%;">
                                <a id="btnLogout" href="#" class="textSmallFlat"
                                   onclick="document.location.href = '${LogoutControle.redirectLogout}'">
                                    <h:outputText style="vertical-align: middle;" styleClass="fa-icon-remove"/> Sair
                                </a>
                            </h:panelGroup>
                        </li>
                    </ul>
                </h:panelGroup>
                <script>
                    function fecharTodos() {
                        jQuery('.dropDownMenu .fechado').css('display', 'none');
                        jQuery('.dropDownMenu .fechado').parent().find('.aberto').css('display', 'block');
                    }
                    jQuery('div.dropdown-toggle').click(function() {
                        var el = jQuery(this).parent().children('ul');
                        //  console.log(jQuery(this).attr('class'));
                        jQuery('.dropdown-menu').slideUp('fast');
                        if (el.css('display') === 'block') {
                            jQuery('.dropdown-menu').parent().removeClass('open');
                        } else {
                            jQuery('.dropdown-menu').parent().removeClass('open');
                            el.slideDown('fast', function() {
                                el.parent().addClass('open');
                            });
                        }
                    });
                    jQuery(document).click(function(e) {
                        try {
                            var classe = jQuery(e.target).parent().attr('class');

                            if (classe.indexOf('dropdown-toggle') < 0 && classe.indexOf('menuModulos') < 0
                                    && classe.indexOf('iconeTrocaEmpresa') < 0 && classe.indexOf('dropDownMenu') < 0
                                && classe.indexOf('naofecharmenufluxo') < 0) {
                                jQuery('.dropDownMenu ul').slideUp('fast', function() {
                                    jQuery('.dropdown-menu').parent().removeClass('open');

                                });

                            }
                        } catch (ignored) {
                        }
                    });
                    jQuery('.itemsOpcoes').click(function() {
                        if (jQuery(this).children('.aberto').css('display') === 'block') {
                            fecharTodos();
                            jQuery(this).children('.aberto').css('display', 'none');
                            jQuery(this).children('.aberto').parent().find('.fechado').css('display', 'block');
                        } else {
                            fecharTodos();
                        }
                    });
                </script>
            </h:panelGroup>
        </h:panelGroup>
    </h:panelGroup>
    <h:panelGroup styleClass="pull-right painelAlunosMarcados"  id="painelAlunosMarcados">
        <rich:dragIndicator id="indicator" >
            <f:facet name="single">
                <h:panelGroup>
                    {testDrop}
                </h:panelGroup>
            </f:facet>
        </rich:dragIndicator>
        <a4j:repeat value="#{ClientesMarcadosControle.clientesMarcados}" var="clienteM">
            <a4j:outputPanel>
                <rich:dragSupport  dragIndicator=":form:indicator"
                                   dragType="alunos" dragValue="#{clienteM}"
                                   ondragstart="subirPainelRemoverAluno()"
                                   ondragend="sumirPainelRemoverAluno()">
                    <rich:dndParam name="testDrop">
                        <h:graphicImage url="#{(SuperControle.fotosNaNuvem ? clienteM.pessoa.urlFoto : clienteM.pessoa.urlFotoContexto )}"
                                        styleClass="imagemAluno pequena"/>
                    </rich:dndParam>
                </rich:dragSupport>
                <div class="clienteMarcado" style="font-family: Arial; font-size: 14px;">
                    <a4j:commandLink style="margin-left: 3px;" action="#{ClientesMarcadosControle.abrirCliente}"
                                     styleClass="tooltipclientesMarcados">
                        <h:graphicImage styleClass="imagemAluno pequena"
                                        url="#{(SuperControle.fotosNaNuvem ? ClientesMarcadosControle.fotoNuvem : clienteM.pessoa.urlFotoContexto )}">
                        </h:graphicImage>
                    </a4j:commandLink>
                    <div class="mensagemCmarc">
                        <h:outputText value="#{clienteM.nomeLembrete}" escape="false"/>
                    </div>
                    <div class="cmarc">
                        <a4j:commandLink title="Adicionar um lembrete"
                                         styleClass="tooltipsterright"
                                         onclick="jQuery('.cliente#{clienteM.codigo}').toggle()"
                                         status="false">
                            <i class="fa-icon-comments" style="line-height: 24px; margin-left: 5px;"></i>
                        </a4j:commandLink>
                        <h:panelGroup styleClass="caixaLembrete cliente#{clienteM.codigo}" layout="block"
                        >
                            <h:inputTextarea styleClass="inputTextClean" value="#{clienteM.observacao}"
                                             style="width: calc(100% - 20px); margin-top:10px; margin-left:10px; height: 70px;"></h:inputTextarea>
                            <a4j:commandLink styleClass="botaoPrimarioSmall texto-size-14-real"
                                             action="#{ClientesMarcadosControle.gravarObservacao}"
                                             reRender="painelAlunosMarcados"
                                             style="float: right; margin-right: 10px;margin-top: 5px">
                                Gravar
                            </a4j:commandLink>
                        </h:panelGroup>
                    </div>
                </div>
            </a4j:outputPanel>
        </a4j:repeat>
        <script>
            carregarTooltipster();
        </script>
    </h:panelGroup>
    <h:panelGroup layout="block" styleClass="logoIcones col-celula pull-left tudo" style="max-width: 680px; text-align: left;">

        <h:panelGroup layout="block" style="margin-left: 10px;display: inline-block;" styleClass="logo-topo">
            <a4j:commandLink action="#{LoginControle.abrirModulo}">
                <h:graphicImage height="55px"
                        url="/imagens_flat/#{LoginControle.moduloAberto.logoModulo}" />
            </a4j:commandLink>
        </h:panelGroup>
        <rich:spacer width="15"/>

        <a4j:jsFunction name="verificarTemNova" reRender="painelFeed"
                        status="false"
                        action="#{FeedGestaoControle.verificarTemNova}">
        </a4j:jsFunction>
        <h:panelGroup id="painelFeed" layout="block" style="display: inline-block" rendered="true">
            <a4j:commandLink oncomplete="Richfaces.showModalPanel('feedGestao'); indiceAtual = 1;"
                             reRender="feedGestao, painelFeed"
                             status="false"
                             rendered="#{FeedGestaoControle.nrMsgsNaoLidas > 0}"
                             action="#{FeedGestaoControle.abrirFeed}">
                <h:graphicImage  url="/feed_gestao/assistente_sem_notif.png" width="60" height="60"
                                 title="Assistente de Gestão Pacto"/>
            </a4j:commandLink>
            <h:panelGroup id="nrlidaFeed" rendered="#{FeedGestaoControle.nrMsgsNaoLidas > 0}"
                          layout="block" style="float:right;display: inline;position: absolute;">
                <div class="notificacaoAtividades">
                    <h:outputText value="#{FeedGestaoControle.nrMsgsNaoLidas}"></h:outputText>
                </div>
            </h:panelGroup>
            <h:outputLink styleClass="linkWiki"
                          rendered="#{FeedGestaoControle.nrMsgsNaoLidas > 0}"
                          value="#{SuperControle.urlWiki}ZillyonWeb:Assistente_Gestao_Pacto"
                          title="Clique e saiba mais: Feed de gestão"
                          target="_blank">
                <i class="fa-icon-question-sign" style="font-size: 18px"></i>
            </h:outputLink>
        </h:panelGroup>

        <a4j:commandButton style="display: none;" reRender="topoNovo"
                           status="false"
                           id="btnAtualizaFotoUsuario">
        </a4j:commandButton>

        <h:panelGroup layout="block" style="width: 340px; display: inline-block;vertical-align: top;margin-left: 15px;margin-top: 15px;">
            <jsp:include page="includes/include_expiracao.jsp" flush="true"/>
        </h:panelGroup>
    </h:panelGroup>
</h:panelGroup>
<%--MENU OPÇÕES--%>
<c:if test="${FeedGestaoControle.nrMsgsNaoLidas > 0}">
    <jsp:include page="includes/include_modal_feedGestao.jsp"></jsp:include>
</c:if>
<script type="text/javascript">
    function blinker() {
        jQuery('.blink_me').fadeIn(500);
        jQuery('.blink_me').fadeOut(500);
    }
    setInterval(blinker, 500);

    function subirPainelRemoverAluno(){
        jQuery('.painelRemoverAluno').slideDown('slow');
    }
    function sumirPainelRemoverAluno(){
        jQuery('.painelRemoverAluno').slideUp();
    }
</script>

<c:if test="${LoginControle.apresentarHotjar}">
    <script>
        function hotjarParams(empresa, usuario, perfil) {
            hj('tagRecording', ['Empresa', empresa]);
            hj('tagRecording', ['Username', usuario]);
            hj('tagRecording', ['Perfil', perfil]);
        }

        (function(h,o,t,j,a,r){
            h.hj=h.hj||function(){(h.hj.q=h.hj.q||[]).push(arguments)};
            //hotjarParams('${LoginControle.empresa.nome}', '${LoginControle.usuario.username}', '${LoginControle.perfilAcesso.nome}');
            h._hjSettings={hjid:2500298,hjsv:6};
            a=o.getElementsByTagName('head')[0];
            r=o.createElement('script');r.async=1;
            r.src=t+h._hjSettings.hjid+j+h._hjSettings.hjsv;
            a.appendChild(r);
        })(window,document,'//static.hotjar.com/c/hotjar-','.js?sv=');

    </script>
</c:if>

<c:if test="${LoginControle.utilizaChatMovidesk}">
    <!-- Chat do Movidesk -->
    <script type="text/javascript">var mdChatClient="${LoginControle.grupoMoviDesk}";</script>
    <script src="https://chat.movidesk.com/Scripts/chat-widget.min.js"></script>
    <script type="text/javascript">
        // var TEMPO_MINIMIZAR_CHAT = 300;
        function corrigirTelefone(telefone) {
            if (telefone == undefined || telefone == '') {
                return telefone;
            }

            telefone = telefone.replace(/[^0-9.]/g, "");
            if (telefone.length < 9) {
                return telefone;
            }

            if (telefone.length == 9) {
                telefone = telefone.substring(0, 2) + "3" + telefone.substring(2);
            } else if (telefone.length == 10
                && !telefone.substring(2, 3) == "2"
                && !telefone.substring(2, 3) == "3"
                && !telefone.substring(2, 3) == "4"
                && !telefone.substring(2, 3) == "5") {
                telefone = telefone.substring(0, 2) + "9" + telefone.substring(2);
            }

            if (telefone.length == 10) {
                telefone = "(" + telefone.substring(0, 2) + ") " + telefone.substring(2, 6) + "-" + telefone.substring(6, 10);
            } else if (telefone.length == 11) {
                telefone = "(" + telefone.substring(0, 2) + ") " + telefone.substring(2, 7) + "-" + telefone.substring(7, 11);
            }

            return telefone;
        }

        function getTelefone() {
            var telefoneColaborador = '${LoginControle.usuarioLogado.colaboradorVO.primeiroTelefoneNaoNulo}';
            var telefoneEmpresa = '${LoginControle.empresa.primeiroTelefoneNaoNulo}';

            if (telefoneColaborador !== 'SEM TELEFONE') {
                return corrigirTelefone(telefoneColaborador);
            } else if (telefoneEmpresa !== 'SEM TELEFONE') {
                return corrigirTelefone(telefoneEmpresa);
            }
        }

        movideskLogin({
            name: "${LoginControle.usuarioLogado.nome}",
            CodeReference: "${LoginControle.codigoColaboradorMovidesk}",
            CodRefAdditional: "${LoginControle.codigoEmpresaFinMovidesk}",
            OrganizationCodeReference: "${LoginControle.codigoEmpresaMovidesk}",
            PhoneNumber: getTelefone(),
            email: "${LoginControle.emailUsuarioLogado}",
            stayConnected: false,
            emptySubject: false,
            startChat: false
        });

        // function clickOnMovChat() {
        //     waitForElm('.md-chat-widget-btn-wrapper').then(
        //         element => {
        //             element.addEventListener('click', function (e) {
        //                 const btn = document.querySelector('.md-chat-widget-btn-wrapper');
        //                 if (btn.classList.contains('md-chat-widget-btn-close')) {
        //                     try{
        //                         Notifier.info("Agora você deve clicar no ponto de interrogação para iniciar o chat para atendimento!", "O chat de atendimento mudou!");
        //                     } catch(e) {}
        //                     movideskChatWidgetChangeWindowState('minimized');
        //                     e.stopPropagation();
        //                 }
        //             });
        //         }
        //     )
        // }
        //
        // window.onload = clickOnMovChat();

        function waitForElm(selector) {
            return new Promise(resolve => {
                if (document.querySelector(selector)) {
                    return resolve(document.querySelector(selector));
                }

                const observer = new MutationObserver(mutations => {
                    if (document.querySelector(selector)) {
                        resolve(document.querySelector(selector));
                        observer.disconnect();
                    }
                });

                observer.observe(document.body, {
                    childList: true,
                    subtree: true
                });
            });
        }

        /*var interval = setInterval(function () {
            if (document.readyState == 'complete') {
                movideskChatWidgetChangeWindowState('minimized');
                clearInterval(interval);
            }
        }, TEMPO_MINIMIZAR_CHAT);*/
    </script>
    <!-- Chat do Movidesk fim -->
</c:if>

<c:if test="${LoginControle.utilizaOctadesk}">
    <tags:octadesk/>
</c:if>


<rich:panel styleClass="painelRemoverAluno">

    <rich:dropSupport acceptedTypes="alunos" dropValue="aluno"
                      reRender="painelAlunosMarcados"
                      dropListener="#{ClientesMarcadosControle.processDropDelete}">
    </rich:dropSupport>

    <div class="textoMarcarAluno">
        <i class="fa-icon-remove" style="margin-right: 10px;"></i>
        <h:outputText value="Desmarcar aluno"/>
    </div>
</rich:panel>
<style>
    .novaModal.mdlSimples{
        -webkit-box-shadow: 0 2px 10px 0 rgba(0,0,0,0.35);
        -moz-box-shadow: 0 2px 10px 0 rgba(0,0,0,0.35);
        box-shadow: 0 2px 10px 0 rgba(0,0,0,0.35);
        background-color: #ffffff;
    }
</style>

<jsp:include page="include_modal_acesso_app_gestor.jsp"/>
<jsp:include page="includes/include_modal_popUpBloqueado.jsp"/>
