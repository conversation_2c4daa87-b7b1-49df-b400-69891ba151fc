<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<%@page contentType="text/html" %>
<%@page pageEncoding="ISO-8859-1" %>

<link href="./css/otimize.css" rel="stylesheet" type="text/css">

<%@include file="/includes/imports.jsp" %>

<head>
    <jsp:include page="include_head.jsp" flush="true"/>
    <script type="text/javascript" language="javascript" src="hoverform.js"></script>
</head>
<link rel="shortcut icon" href="./favicon_zw_ui.png">
<link href="./css/otimize.css" rel="stylesheet" type="text/css">
<link href="./beta/css/pure-forms.css" rel="stylesheet" type="text/css">
<link href="./beta/css/pure-tables.css" rel="stylesheet" type="text/css">
<link href="./beta/css/pure-buttons.css" rel="stylesheet" type="text/css">
<link href="./beta/css/pure-ext.css" rel="stylesheet" type="text/css">
<link href="./beta/css/font-awesome_2.0.min.css" rel="stylesheet" type="text/css">
<link href="./beta/css/pure-min.css" rel="stylesheet" type="text/css">
<style type="text/css">
    .alinhar {
        vertical-align: top !important;
    }

    .iconeTrocarArmario {
        font-size: 19px !important;
        padding-left: 3px !important;
        color: #575757 !important;
    }

    #modalAlugarArmariosCDiv {
        top: 120px !important;
    }

    .btnLimparTamanhos {
        display: none;
    }

    .panelGrpTamanhos:hover .btnLimparTamanhos {
        display: inline-block;
        visibility: visible;
    }

    .btnExcluirFiltroTamanho {
        color: #023504;
        text-decoration: none !important;
    }

    .selectManyMenu {
        -webkit-background-clip: padding-box;
        -webkit-box-shadow: rgba(0, 0, 0, 0.701961) 0px 1px 1px 0px;
        -webkit-font-smoothing: subpixel-antialiased;
        -moz-background-clip: padding-box;
        -moz-box-shadow: rgba(0, 0, 0, 0.701961) 0px 1px 1px 0px;;
        background: #0f4c6b -moz-linear-gradient(top, rgba(15, 76, 107, 0.49),
        rgba(63, 81, 181, 0.56));
        background-clip: padding-box;
        background: #0f4c6b -webkit-linear-gradient(top, rgba(15, 76, 107, 0.49),
        rgba(63, 81, 181, 0.56));

        box-shadow: rgba(0, 0, 0, 0.701961) 0px 1px 1px 0px;
        color: rgb(255, 255, 255) !important;
        font-family: 'Helvetica Neue', Helvetica, sans-serif;
        font-size: 13px !important;
        height: 20px !important;
        line-height: normal;
        list-style-type: none;
        padding: 1px 3px !important;
        text-align: center;
        text-shadow: rgba(0, 0, 0, 0.4) 0 -1px 0;
        zoom: 1;
        border-radius: 33%;
    }

    .linhasArmario1 {
        background-color: rgba(0, 41, 80, 0.2);
    }

    .linhasArmario2 {
        background-color: rgba(204, 212, 220, 0.62);
    }

    .pesquiza-lente {
        text-decoration: none;
        position: absolute;
        top: 5px;
        right: 4px;
        z-index: 1;
        border: none;
        background: transparent;
        outline: none;
    }

    .pesquiza-line {
        position: relative;
        width: 200px;
        right: 12px;
    }

    .pesquiza-line input {
        width: 100%;
    }

    .boxFiltroArmario {
        display: inline-block;
    }

    .rich-table {
        background: none;
        border: none;
    }

    .rich-panel {
        padding: 0px;
        margin-bottom: 5px;
        border-color: #FFF;
        border-radius: 10px;
        -moz-border-radius: 10px;
        -webkit-border-radius: 10px;
        background-color: #ffffff;
        width: 100%;
    }

    .rich-paneltp {
        float: left;
        border-radius: 10px;
        -moz-border-radius: 10px;
        -webkit-border-radius: 10px;
        -moz-box-shadow:  0px 1px 6px 0px rgba(0,0,0,0.4);
        -webkit-box-shadow:  0px 1px 6px 0px rgba(0,0,0,0.4);
        box-shadow:  0px 1px 6px 0px rgba(0,0,0,0.4);
    }

    .rich-paneltp table {
        float: right;
    }

    .rich-panelsp {
        margin-left: 5px;
        padding: 0px !important;
        margin-bottom: 5px !important;
        margin-top: 5px !important;
        border-color: #FFF !important;
        border-radius: 10px !important;
        -moz-border-radius: 10px !important;
        -webkit-border-radius: 10px !important;
        background-color: #ffffff !important;
        float: left !important;
    }

    .rich-panel label {
        font-size: 11px;
        color: #666;
        margin-left: 5px;
    }

    .rich-panel input[type=checkbox] {
        margin-left: 25px;
    }

    .rich-table-cell {
        margin-bottom: 5px;
        border-color: #FFF;
        border-radius: 10px;
        padding: 5px;
        background: none;
        border: none;
        width: 25%;
    }

    .tableFiltros .rich-table-cell {
        border-radius: 5px !important;
    }

    .rich-table-cell span {
        font-family: "Trebuchet MS", Verdana, Arial, Helvetica, sans-serif;
        font-size: 9px;
        color: #808080;
    }

    .rich-panel-header {
        margin-bottom: 5px;
        border-color: #FFF;
        border-top-left-radius: 10px;
        border-right-right-radius: 10px;
        padding: 5px;
        background-color: #ffffff;
        background-image: none;
    }

    .panelDadosArmario {
        text-align: left;
        float: left;
        width: calc(100% - 47px);
        text-align: right;
        float: right;
    }

    .panelDadosArmario table {
        text-align: center;
        width: calc(100% - 50px);
    }

    .panelOperacaoArmario {
        width: 30px;
        text-align: right;
        float: right;
    }

    .panelDadosArmario div {
        border-radius: 90px;
        height: 67px;
        float: left;
        width: 50px;
    }

    .panelDadosArmario img {
        border-radius: 90px;
        float: left;
    }

    .panelDadosArmario a {
        font-family: "Trebuchet MS", Verdana, Arial, Helvetica, sans-serif;
        font-size: 9px;
        color: #808080;
    }

    .panelDadosArmario p {
        display: block;
        font-family: "Trebuchet MS", Verdana, Arial, Helvetica, sans-serif;
        font-size: 9px;
        color: #808080;
        width: auto;
        float: left;
    }

    .panelOperacaoArmario a {
        font-family: "Trebuchet MS", Verdana, Arial, Helvetica, sans-serif;
        font-size: 9px;
        color: #808080;
        width: 100%;
        text-align: right;
    }

    .panel-info td {
        width: auto;
        min-width: 100px;
        font-family: "Trebuchet MS", Verdana, Arial, Helvetica, sans-serif;
        font-size: 9px;
        color: #808080;

    }

    .panel-infosp td {
        width: auto;
        min-width: 100px;
        font-family: "Trebuchet MS", Verdana, Arial, Helvetica, sans-serif;
        font-size: 9px;
        color: #808080;
    }

    .header-top-zw {
        padding-top: 3px;
        border-top-color: #34b65b;
        border-top-style: solid;
        border-top-width: 1px;
        display: block;
        width: auto;
    }

    .header-top-zw-inativo {
        padding-top: 3px;
        border-top-color: #890008;
        border-top-style: solid;
        border-top-width: 2px;
        display: block;
        width: auto;
    }

    .header-top-zw-ativo {
        padding-top: 3px;
        border-top-color: #34b65b;
        border-top-style: solid;
        border-top-width: 2px;
        display: block;
        width: auto;
    }

    .header-top-zw-ativo.Masculino {
        border-top-color: royalblue !important;
    }

    .header-top-zw-ativo.Feminino {
        border-top-color: violet !important;
    }

    .rich-panel-body {
        padding: 10px 14px 10px 14px !important;
    }

    .tpGruposss td {
        font-size: 11px;
        color: #666;
        margin-left: 5px;
    }

    .tpGruposss td:nth-child(2) {
        padding-left: 5px;
        font-size: 11px;
        color: #666;
        margin-left: 5px;
    }

    .tituloCamposfiltro {
        font-size: 8px !important;
        color: #666 !important;
    }

    .tabela_modal {
        background: none;
        border: none;
        border-collapse: collapse;
        width: 100%;
    }

    .tabela_modal tr {
        width: 100%;
    }

    .tabela_modal tr:nth-child(odd) {
        background-color: #F0F0F0;
    }

    .tabela_modal td.direita {
        border-collapse: collapse;
        width: 35%;
        border: none;
        padding-top: 2px;
        padding-bottom: 2px;
    }

    .tabela_modal td.esquerda {
        border-collapse: collapse;
        width: 65%;
        border: none;
        padding-top: 2px;
        padding-bottom: 2px;
    }

    .rich-modalpanel:not(.novaModal)  .rich-mpnl-body {
        background: none !important;
    }

    .rich-mpnl-body img.xcloseModal {
        padding: 0 0 0 0;
        float: right !important;
    }

    .rich-mpnl-shadow {
        opacity: 0 !important;
    }

    .titulo_modal_form {
        background: none;
        padding: 5px 0 5px 0;
        margin: 5px 0 15px 0;
        width: 100%;
        display: block;
        border-bottom: dotted;
        border-bottom-width: 1px;
        border-bottom-color: #bfbfbf;
    }
    .linkPaginacao,.linkPaginacao:visited{
        font-size: 25px;
        padding: 5px;
        color: #9E9E9E;
    }
    .linkPaginacao:hover{
        color: #29ABE2;
    }
    .tituloCampos{
        line-height: 1.9 !important;
        padding: 5px;
    }
</style>
<f:view>
    <jsp:include page="includes/include_carregando_ripple.jsp" flush="true"/>
    <title>
        <h:outputText value="Gestão de Armários"/>
    </title>
    <h:form id="form">
        <a4j:keepAlive beanName="GestaoArmarioControle"/>
        <html>
        <body>
            <a4j:loadScript src="/script/jquery.maskedinput-1.2.2.js" />
        <h:panelGroup layout="block" style="display: inline-table;width: 100%;" styleClass="fundoCinza">
            <h:panelGroup layout="block" styleClass="bgtop topoZW" rendered="#{MenuControle.apresentarTopo}">
                <jsp:include page="include_topo_novo.jsp" flush="true"/>
                <jsp:include page="include_menu_zw_flat.jsp" flush="true"/>
            </h:panelGroup>

            <h:panelGroup layout="block" styleClass="caixaCorpo">
                <h:panelGroup layout="block" style="height: 80%;width: 100%">
                    <h:panelGroup layout="block" styleClass="caixaMenuLatel">
                        <h:panelGroup layout="block" styleClass="container-imagem container-conteudo-central">
                            <h:panelGroup id="panelConteudo">
                                <h:panelGroup layout="block" styleClass="container-box  zw_ui especial ">
                                    <h:panelGroup styleClass="container-box-header" layout="block">
                                        <h:panelGroup layout="block" styleClass="margin-box">
                                            <h:outputText value="Gestão de Armários"
                                                          styleClass="container-header-titulo"/>
                                            <h:outputLink styleClass="linkWiki"
                                                          value="#{SuperControle.urlBaseConhecimento}como-configurar-o-gestao-de-armarios/"
                                                          title="Clique e saiba mais: Gestão de Armários" target="_blank">
                                                <i class="fa-icon-question-sign" style="font-size: 18px"></i>
                                            </h:outputLink>

                                        </h:panelGroup>
                                    </h:panelGroup>
                                    <h:panelGroup layout="block" styleClass="margin-box">

                                        <h:panelGrid id="mensagem" columns="1" width="100%"
                                                     columnClasses="alinhar w15,alinhar w15">
                                            <h:panelGroup layout="block">
                                                <h:panelGroup layout="block" styleClass="rich-panelsp pure-form"
                                                              style="min-width: 72%">

                                                    <h:panelGroup styleClass="textsmall" layout="block"
                                                                  style="width: 100%;">


                                                        <h:panelGroup layout="block" styleClass="boxFiltroArmario">
                                                            <h:outputText
                                                                    value="#{msg_aplic.prt_situacaoArmarios} "/>
                                                            <h:selectOneMenu id="tpSituacoes"
                                                                             value="#{GestaoArmarioControle.situacoesEscolhidas}">
                                                                <f:selectItems
                                                                        value="#{GestaoArmarioControle.situacoes}"/>
                                                            </h:selectOneMenu>
                                                        </h:panelGroup>

                                                        <h:panelGroup layout="block" style="padding: 10px"
                                                                      styleClass="boxFiltroArmario">
                                                            <h:outputText value="#{msg_aplic.prt_grupos} "/>
                                                            <h:selectOneMenu id="tpGrupos"
                                                                             style="vertical-align:middle;"
                                                                             value="#{GestaoArmarioControle.grupoArmario}">
                                                                <f:selectItems
                                                                        value="#{GestaoArmarioControle.gruposFiltro}"/>
                                                            </h:selectOneMenu>
                                                        </h:panelGroup>


                                                        <h:panelGroup layout="block" id="grpTamanhos"
                                                                      styleClass="panelGrpTamanhos boxFiltroArmario"
                                                                      style="padding: 15px;">
                                                            <h:outputText value="#{msg_aplic.prt_tamanhos}  "
                                                                          styleClass="boxFiltroArmario"/>
                                                            <h:selectOneMenu id="tpTamanhos"
                                                                             styleClass="boxFiltroArmario"
                                                                             value="#{GestaoArmarioControle.tamanhosEscolhidos}">
                                                                <f:selectItems
                                                                        value="#{GestaoArmarioControle.tamanhos}"/>
                                                            </h:selectOneMenu>
                                                            <h:panelGroup id="qtdTamanhos" layout="block"
                                                                          styleClass="boxFiltroArmario"
                                                                          style="margin-left: 2px;width: 23px">
                                                                <h:panelGroup layout="block">
                                                                    <h:panelGroup layout="block"
                                                                                  styleClass="selectManyMenu">
                                                                        <a4j:commandLink
                                                                                value=" "
                                                                                style="text-decoration: none !important;color: white !important;font-size: 15px !important;"
                                                                                action="#{GestaoArmarioControle.adicionarItemFiltroNaLista}"
                                                                                reRender="panelConteudo">
                                                                            <h:outputText
                                                                                    styleClass="fa-icon-filter"></h:outputText>
                                                                        </a4j:commandLink>
                                                                    </h:panelGroup>
                                                                </h:panelGroup>
                                                            </h:panelGroup>
                                                            <rich:toolTip style="text-align: left"
                                                                          value="#{GestaoArmarioControle.tamanhoSelecionadasApresentar}"
                                                                          for="qtdTamanhos"
                                                                          followMouse="true"/>
                                                            <h:panelGroup layout="block"
                                                                          style="padding-left:2px;"
                                                                          styleClass="btnLimparTamanhos boxFiltroArmario">
                                                                <a4j:commandButton
                                                                        value=" "
                                                                        id="limparTamanhosSelecionadas"
                                                                        image="../imagens/limpar.gif"
                                                                        reRender="panelConteudo"
                                                                        styleClass="botoes"
                                                                        style="margin-left: 2px;margin-top: 3px;"
                                                                        action="#{GestaoArmarioControle.limparTamanhos}"/>
                                                            </h:panelGroup>
                                                        </h:panelGroup>

                                                        <h:panelGroup layout="block"
                                                                      styleClass="pesquiza-line boxFiltroArmario pull-right"
                                                                      style="float: right;margin-top: 2%">

                                                            <h:inputText id="campoBuscarArmario"
                                                                         value="#{GestaoArmarioControle.valorConsultaArmario}"
                                                                         onfocus="if(this.value=='Pesquisar')this.value=''"
                                                                         onblur="if(this.value=='')this.value='Pesquisar'">
                                                            </h:inputText>
                                                            <rich:hotKey selector="#campoBuscarArmario" key="return"
                                                                         handler="#{rich:element('btnBuscarArmario')}.onclick();return false;"/>

                                                            <h:panelGroup layout="block"
                                                                          styleClass="pesquiza-lente">
                                                                <a4j:commandLink id="btnBuscarArmario"
                                                                                 style="text-decoration: none !important;color:#012B01;"
                                                                                 action="#{GestaoArmarioControle.obterArmariosPaginaInicial}"
                                                                                 reRender="panelConteudo">
                                                                    <i class="fa-icon-search"></i>
                                                                </a4j:commandLink>
                                                            </h:panelGroup>

                                                        </h:panelGroup>
                                                    </h:panelGroup>


                                                    <h:panelGroup layout="block" styleClass="textsmall">
                                                        <h:panelGroup layout="block"
                                                                      style="float:left;padding: 10px"
                                                                      styleClass="textsmall">
                                                            <h:outputText value="Ordenação:"/>
                                                            <h:panelGroup layout="block"
                                                                          style="display: inline-block;">
                                                                <h:selectBooleanCheckbox
                                                                        value="#{GestaoArmarioControle.ordenarPorDataFinal}"
                                                                        style="margin-left: 10px !important;vertical-align: text-top">
                                                                    <a4j:support event="onclick"
                                                                                 action="#{GestaoArmarioControle.carregarArmarios}"
                                                                                 reRender="panelConteudo"/>

                                                                </h:selectBooleanCheckbox>
                                                            </h:panelGroup>
                                                            <h:outputText value="Data final da vigência"
                                                                          styleClass="tituloCamposfiltro"
                                                                          style="vertical-align:middle;font-size: 11px !important;"/>
                                                        </h:panelGroup>
                                                        <h:panelGroup layout="block"
                                                                      style="padding-right:10px;float: right">
                                                            <a4j:commandButton id="novoArmario"
                                                                               styleClass="botoes nvoBt"
                                                                               action="#{GestaoArmarioControle.novosArmarios}"
                                                                               value="#{msg_aplic.prt_cadastrarArmario}"
                                                                               reRender="formModalGerarArmarios"
                                                                               oncomplete="#{GestaoArmarioControle.msgAlert}"/>
                                                        </h:panelGroup>

                                                    </h:panelGroup>


                                                </h:panelGroup>
                                                <h:panelGroup layout="block" styleClass="rich-panelsp pull-right"
                                                              style="width: 264px !important">
                                                    <h:panelGroup layout="block"
                                                                  style="margin-top: 2px;margin-left: 12px">
                                                        <h:outputText styleClass="titulotexto"
                                                                      value="Filtros Selecionados"/>
                                                    </h:panelGroup>
                                                    <h:panelGroup layout="block"
                                                                  style="height:74px; overflow-y:auto;">
                                                        <rich:dataTable styleClass="tableFiltros"
                                                                        style="margin-left: 2.5%;margin-top: 2%"
                                                                        value="#{GestaoArmarioControle.filtroItem}"
                                                                        width="95%" var="filtroitem"
                                                                        rowClasses="linhasArmario1,linhasArmario2">
                                                            <rich:column>
                                                                <h:panelGroup layout="block"
                                                                              style="border-radius:12px;-webkit-border-radius: 12px;-moz-border-radius: 12px;background-color: rgba(64, 70, 63, 0.44)">
                                                                    <h:panelGroup layout="block"
                                                                                  style="float: left;">
                                                                        <h:outputText
                                                                                rendered="#{filtroitem.apresentarSit}"
                                                                                value="#{filtroitem.status.label},"/>
                                                                        <h:outputText
                                                                                rendered="#{filtroitem.apresentarGrp}"
                                                                                value="#{filtroitem.grupoSelecionado.label},"/>
                                                                        <h:outputText
                                                                                value="#{filtroitem.tamanhoArmario.descricao}"/>
                                                                    </h:panelGroup>
                                                                    <h:panelGroup layout="block"
                                                                                  style="float: right;">
                                                                        <a4j:commandLink
                                                                                styleClass="btnExcluirFiltroTamanho"
                                                                                action="#{GestaoArmarioControle.excluirTamanhoSelecionado}"
                                                                                reRender="panelConteudo">
                                                                            <i class="fa-icon-remove"></i>
                                                                        </a4j:commandLink>
                                                                    </h:panelGroup>
                                                                </h:panelGroup>
                                                            </rich:column>
                                                        </rich:dataTable>
                                                    </h:panelGroup>
                                                </h:panelGroup>
                                            </h:panelGroup>


                                        </h:panelGrid>
                                        <h:outputText styleClass="mensagem"
                                                      value="#{GestaoArmarioControle.mensagem}"/>
                                        <h:outputText styleClass="mensagemDetalhada"
                                                      value="#{GestaoArmarioControle.mensagemDetalhada}"/>
                                        <h:panelGrid columns="1" width="100%"
                                                     columnClasses="colunaCentralizada">
                                            <table width="100%" border="0" cellspacing="0" cellpadding="0">
                                                <tr>
                                                    <td align="center" valign="middle">
                                                        <h:panelGroup
                                                                rendered="#{GestaoArmarioControle.confPaginacao.existePaginacao}">
                                                            <a4j:commandLink styleClass="fa-icon-double-angle-left linkPaginacao"
                                                                             reRender="panelConteudo"
                                                                             action="#{GestaoArmarioControle.primeiraPaginacao}">
                                                            </a4j:commandLink>

                                                            <a4j:commandLink styleClass="fa-icon-angle-left linkPaginacao"
                                                                             reRender="panelConteudo"
                                                                             action="#{GestaoArmarioControle.anteriorPaginacao}">
                                                            </a4j:commandLink>

                                                            <h:outputText styleClass="tituloCampos"
                                                                          value="#{msg_aplic.prt_msg_pagina} #{GestaoArmarioControle.confPaginacao.paginaAtualDeTodas}"
                                                                          rendered="true"/>
                                                            <a4j:commandLink styleClass="fa-icon-angle-right linkPaginacao"
                                                                             reRender="panelConteudo"
                                                                             action="#{GestaoArmarioControle.proximaPaginacao}">
                                                            </a4j:commandLink>

                                                            <a4j:commandLink styleClass="fa-icon-double-angle-right linkPaginacao"
                                                                             reRender="panelConteudo"
                                                                             action="#{GestaoArmarioControle.ultimaPaginacao}">
                                                            </a4j:commandLink>

                                                            <h:outputText styleClass="tituloCampos"
                                                                          value=" [#{msg_aplic.prt_msg_itens} #{GestaoArmarioControle.confPaginacao.numeroTotalItens}]"
                                                                          rendered="true"/>
                                                        </h:panelGroup>
                                                    </td>
                                                </tr>
                                            </table>
                                        </h:panelGrid>
                                        <h:panelGroup layout="block" styleClass="container-box-blank">

                                            <rich:dataGrid style="width: 100%"
                                                           value="#{GestaoArmarioControle.armarios}"
                                                           var="armario"
                                                           columns="4">
                                                <rich:panel styleClass="rich-paneltp" style="height: 150px;">

                                                    <h:outputText
                                                            styleClass="#{armario.status.id == 0 ? 'header-top-zw-ativo' : 'header-top-zw-inativo'} #{armario.grupo.label}"
                                                            value="#{armario.descricao} - #{armario.tamanhoArmario.descricao} - #{armario.grupo.label}"/>
                                                    <h:outputText style="color:red"
                                                                  rendered="#{armario.status.id == 2}"
                                                                  value="(#{armario.status.label})"/>

                                                    <h:panelGroup style="width:100%" layout="block">
                                                        <h:panelGroup style="float:left;margin-top: 9%;"
                                                                      styleClass="panelDadosArmario" layout="block"
                                                                      rendered="#{armario.aluguelAtual != null
                                                                                                  && armario.aluguelAtual.codigo != null && armario.aluguelAtual.codigo != 0}">
                                                            <h:panelGroup layout="block" id="panelFotoArmario">
                                                                <a4j:mediaOutput element="img" id="imagem1"
                                                                                 style="width:50px;height:60px "
                                                                                 cacheable="false" session="true"
                                                                                 rendered="#{!SuperControle.fotosNaNuvem}"
                                                                                 createContent="#{GestaoArmarioControle.paintFotoArmario}"
                                                                                 value="#{ImagemData}"
                                                                                 mimeType="image/jpeg">
                                                                    <f:param value="#{SuperControle.timeStamp}"
                                                                             name="time"/>
                                                                    <f:param name="largura" value="50"/>
                                                                    <f:param name="altura" value="60"/>
                                                                    <f:param name="pessoa"
                                                                             value="#{armario.aluguelAtual.cliente.pessoa.codigo}"></f:param>
                                                                </a4j:mediaOutput>
                                                                <h:graphicImage
                                                                        rendered="#{SuperControle.fotosNaNuvem}"
                                                                        width="50" height="60"
                                                                        style="width:50px;height:60px"
                                                                        url="#{armario.aluguelAtual.cliente.pessoa.urlFoto}">
                                                                </h:graphicImage>

                                                            </h:panelGroup>

                                                            <h:panelGrid styleClass="panel-info" columns="1">
                                                                <h:outputText
                                                                        value="#{armario.aluguelAtual.cliente.pessoa.nomeAbreviado}"/>

                                                                <h:outputText
                                                                        value="#{msg_aplic.prt_fim} - #{GestaoArmarioControle.habilitarGestaoArmarios ? armario.aluguelAtual.dataFimOriginalApresentar : armario.aluguelAtual.finalVigenciaApresentar}"/>
                                                            </h:panelGrid>

                                                        </h:panelGroup>
                                                        <h:panelGroup layout="block"
                                                                      styleClass="panelOperacaoArmario"
                                                                      style="float: right">
                                                            <h:panelGrid>
                                                                <a4j:commandLink rendered="#{armario.aberto}"
                                                                                 reRender="formModalAlugarArmarios"
                                                                                 styleClass="tooltipster"
                                                                                 title="Alugar armário"
                                                                                 action="#{GestaoArmarioControle.prepararAluguelArmario}"
                                                                                 oncomplete="#{GestaoArmarioControle.msgAlert}">
                                                                    <h:graphicImage url="images/unlock_small.png"
                                                                                    alt="abrir armario"/>
                                                                </a4j:commandLink>
                                                                <a4j:commandLink rendered="#{armario.fechado}"
                                                                                 reRender="formModalAlugarArmarios"
                                                                                 styleClass="tooltipster"
                                                                                 title="Fechar armário"
                                                                                 action="#{GestaoArmarioControle.prepararAluguelArmario}"
                                                                                 oncomplete="#{GestaoArmarioControle.msgAlert}">
                                                                    <h:graphicImage url="images/lock_small.png"
                                                                                    alt="fechar armario"/>
                                                                </a4j:commandLink>
                                                                <a4j:commandLink
                                                                        styleClass="tooltipster"
                                                                        title="Histório armário"
                                                                        oncomplete="#{GestaoArmarioControle.msgAlert}"
                                                                        action="#{GestaoArmarioControle.carregarHistorico}">
                                                                    <h:graphicImage url="images/historico_small.png"
                                                                                    alt="historico"/>
                                                                </a4j:commandLink>
                                                                <a4j:commandLink
                                                                        styleClass="tooltipster"
                                                                        title="Editar armário"
                                                                        rendered="#{!armario.inativo}"
                                                                        action="#{GestaoArmarioControle.editarArmario}"
                                                                        reRender="formModalEditarArmarios"
                                                                        oncomplete="#{GestaoArmarioControle.msgAlert}">
                                                                    <h:graphicImage url="images/editar_small.png"/>
                                                                </a4j:commandLink>
                                                                <h:commandLink
                                                                        styleClass="tooltipster"
                                                                        title="Excluir armário"
                                                                        onclick="return confirm('Confirma exclusão do armário?');"
                                                                        action="#{GestaoArmarioControle.excluirArmario}"
                                                                        rendered="#{armario.aberto}">
                                                                    <h:graphicImage url="images/excluir_small.png"/>
                                                                </h:commandLink>
                                                                <a4j:commandLink
                                                                        styleClass="iconeTrocarArmario tooltipster"
                                                                        title="Trocar de armário"
                                                                        action="#{GestaoArmarioControle.abrirTrocaArmario}"
                                                                        rendered="#{armario.fechado}"
                                                                        reRender="formModalTrocarArmarios"
                                                                        oncomplete="#{GestaoArmarioControle.msgAlert}">
                                                                    <i class="fa-icon-exchange"></i>
                                                                </a4j:commandLink>
                                                            </h:panelGrid>
                                                        </h:panelGroup>
                                                    </h:panelGroup>
                                                </rich:panel>
                                            </rich:dataGrid>

                                        </h:panelGroup>
                                        <h:panelGrid style="margin-bottom: 34px" columns="1" width="100%" columnClasses="colunaCentralizada"
                                                     id="paginacao">
                                            <table width="100%" border="0" cellspacing="0" cellpadding="0">
                                                <tr>
                                                    <td align="center" valign="middle">
                                                        <h:panelGroup
                                                                rendered="#{GestaoArmarioControle.confPaginacao.existePaginacao}">
                                                            <a4j:commandLink styleClass="fa-icon-double-angle-left linkPaginacao"
                                                                             reRender="panelConteudo"
                                                                             action="#{GestaoArmarioControle.primeiraPaginacao}">
                                                            </a4j:commandLink>

                                                            <a4j:commandLink styleClass="fa-icon-angle-left linkPaginacao"
                                                                             reRender="panelConteudo"
                                                                             action="#{GestaoArmarioControle.anteriorPaginacao}">
                                                            </a4j:commandLink>

                                                            <h:outputText styleClass="tituloCampos"
                                                                          value="#{msg_aplic.prt_msg_pagina} #{GestaoArmarioControle.confPaginacao.paginaAtualDeTodas}"
                                                                          rendered="true"/>
                                                            <a4j:commandLink styleClass="fa-icon-angle-right linkPaginacao"
                                                                             reRender="panelConteudo"
                                                                             action="#{GestaoArmarioControle.proximaPaginacao}">
                                                            </a4j:commandLink>

                                                            <a4j:commandLink styleClass="fa-icon-double-angle-right linkPaginacao"
                                                                             reRender="panelConteudo"
                                                                             action="#{GestaoArmarioControle.ultimaPaginacao}">
                                                            </a4j:commandLink>

                                                            <h:outputText styleClass="tituloCampos"
                                                                          value=" [#{msg_aplic.prt_msg_itens} #{GestaoArmarioControle.confPaginacao.numeroTotalItens}]"
                                                                          rendered="true"/>
                                                        </h:panelGroup>
                                                    </td>
                                                </tr>
                                            </table>
                                        </h:panelGrid>
                                    </h:panelGroup>

                                </h:panelGroup>
                            </h:panelGroup>
                        </h:panelGroup>
                        <jsp:include page="include_box_menulateral.jsp" flush="true"/>
                    </h:panelGroup>
                </h:panelGroup>
            </h:panelGroup>
            <jsp:include page="include_rodape_flat.jsp" flush="true"/>
        </h:panelGroup>
        </body>
        </html>

    </h:form>
    <!-- ################# GERAR ARMARIOS ##################### -->
    <rich:modalPanel id="modalGerarArmarios" autosized="true" shadowOpacity="true" width="400" height="250"
                     style="border: none; border-radius: 15px; background: none; background-color: #ffffff;">

        <h:panelGroup>
            <h:graphicImage value="/imagens/close.png" style="cursor:pointer" id="hidelinkGerarArmarios"
                            styleClass="xcloseModal"/>
            <rich:componentControl for="modalGerarArmarios" attachTo="hidelinkGerarArmarios" operation="hide"
                                   event="onclick"/>
        </h:panelGroup>

        <h:panelGroup>
            <h:outputText styleClass="titulo_modal_form" value="#{msg_aplic.prt_admin_Armario}"/>
        </h:panelGroup>


        <h:form id="formModalGerarArmarios">
            <h:panelGrid styleClass="tabela_modal" columnClasses="direita, esquerda" cellpadding="3" columns="2">
                <h:outputText value="Incluir armários de" styleClass="tituloCampos"/>
                <h:panelGroup layout="block" style="width:100%;">
                    <h:panelGroup layout="block" style="width: 30%;display:inline-block">
                        <h:inputText id="id_de" size="6" onblur="blurinput(this);" maxlength="5" styleClass="form"
                                     value="#{GestaoArmarioControle.inicio}"
                                     onkeypress="return mascara(this.form, this.id, '99999', event);"/>
                    </h:panelGroup>
                    <h:panelGroup layout="block" style="width: 12%;display:inline-block;vertical-align: text-top">
                        <h:outputText value="#{msg_aplic.prt_CaixaPorOperador_ate}" styleClass="tituloCampos"/>
                    </h:panelGroup>
                    <h:panelGroup layout="block" style="width: 45%;display:inline-block">
                        <h:inputText id="id_ate" size="6" onblur="blurinput(this);" maxlength="5" styleClass="form"
                                     value="#{GestaoArmarioControle.fim}"
                                     onkeypress="return mascara(this.form, this.id, '99999', event);"/>
                    </h:panelGroup>
                </h:panelGroup>

                <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_tamanho}"/>
                <h:selectOneMenu id="tamanho" onblur="blurinput(this);" onfocus="focusinput(this);"
                                 styleClass="form" value="#{GestaoArmarioControle.tamanhoArmario.codigo}">
                    <f:selectItems value="#{GestaoArmarioControle.tamanhos}"/>
                </h:selectOneMenu>

                <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_grupo}"/>
                <h:selectOneMenu id="id_grupo" onblur="blurinput(this);" onfocus="focusinput(this);"
                                 styleClass="form" value="#{GestaoArmarioControle.grupoArmario}">
                    <f:selectItems value="#{GestaoArmarioControle.grupos}"/>
                </h:selectOneMenu>


            </h:panelGrid>

            <h:panelGroup layout="block" style="text-align:center;padding-top:16px;width: 100%;">
                <a4j:commandButton
                        style="background:none; background-color: rgb(0, 70, 113); border-radius:5px; padding: 6px 30px 6px 30px; color: #ffffff"
                        id="inserirArmarios"
                        action="#{GestaoArmarioControle.gerarArmarios}"
                        value="#{msg_aplic.prt_inserir_Armario}"
                        reRender="panelConteudo,panelMensagem2"
                        oncomplete="#{GestaoArmarioControle.mensagemNotificar};#{GestaoArmarioControle.msgAlert}"/>
            </h:panelGroup>

            <h:panelGrid columns="1" id="panelMensagem2" style="padding:15px;width: 100%;">
                <h:outputText styleClass="mensagem" value="#{GestaoArmarioControle.mensagem}"/>
                <h:outputText styleClass="mensagemDetalhada" value="#{GestaoArmarioControle.mensagemDetalhada}"/>
            </h:panelGrid>
        </h:form>
    </rich:modalPanel>
    <!-- ################# EDITAR ARMARIO ##################### -->
    <rich:modalPanel id="modalEditarArmarios" autosized="true" shadowOpacity="true" width="400" height="250"
                     style="border: none; border-radius: 15px; background: none; background-color: #ffffff;">

        <h:panelGroup>
            <h:graphicImage value="/imagens/close.png" style="cursor:pointer" id="hidelinkEditarArmarios"
                            styleClass="xcloseModal"/>
            <rich:componentControl for="modalEditarArmarios" attachTo="hidelinkEditarArmarios" operation="hide"
                                   event="onclick"/>
        </h:panelGroup>

        <h:panelGroup>
            <h:outputText styleClass="titulo_modal_form" value="#{msg_aplic.prt_editar_Armario}"/>
        </h:panelGroup>

        <h:form id="formModalEditarArmarios">
            <h:panelGrid styleClass="tabela_modal" columnClasses="direita, esquerda" cellpadding="3" columns="2">
                <h:outputText value="#{msg_aplic.prt_editar_Armario}" styleClass="tituloCampos"/>
                <h:inputText id="id_descricao_armario" size="20" onblur="blurinput(this);" styleClass="form"
                             value="#{GestaoArmarioControle.armario.descricao}"/>

                <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_tamanho}"/>
                <h:selectOneMenu id="tamanho_editar" onblur="blurinput(this);" onfocus="focusinput(this);"
                                 styleClass="form" value="#{GestaoArmarioControle.armario.tamanhoArmario.codigo}">
                    <f:selectItems value="#{GestaoArmarioControle.tamanhos}"/>
                </h:selectOneMenu>

                <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_grupo}"/>
                <h:selectOneMenu id="id_grupo_editar" onblur="blurinput(this);" onfocus="focusinput(this);"
                                 styleClass="form" value="#{GestaoArmarioControle.armario.grupo}">
                    <f:selectItems value="#{GestaoArmarioControle.grupos}"/>
                </h:selectOneMenu>

            </h:panelGrid>
            <h:panelGrid columns="1" width="100%" style="text-align:center;padding-top:15px;padding-bottom:15px;">
                <a4j:commandButton
                        style="background:none; background-color: rgb(0, 70, 113); border-radius:5px; padding: 5px 30px 5px 30px; color: #ffffff"
                        id="gravarEdicaoArmario"
                        action="#{GestaoArmarioControle.gravarArmario}"
                        value="#{msg_aplic.prt_gravar_Armario}"
                        reRender="panelConteudo,panelMensagem3"
                        oncomplete="#{GestaoArmarioControle.msgAlert}"/>
            </h:panelGrid>
            <h:panelGrid id="panelMensagem3" columns="1" width="100%" style="padding:15px;">
                <h:outputText styleClass="mensagem" value="#{GestaoArmarioControle.mensagem}"/>
                <h:outputText styleClass="mensagemDetalhada" value="#{GestaoArmarioControle.mensagemDetalhada}"/>
            </h:panelGrid>

        </h:form>
    </rich:modalPanel>

    <!-- ################# TROCAR DE ARMARIO ##################### -->
    <rich:modalPanel id="modalTrocarArmarios" autosized="true" shadowOpacity="true" width="500" height="250"
                     style="border: none; border-radius: 15px; background: none; background-color: #ffffff;">

        <h:panelGroup>
            <h:graphicImage value="/imagens/close.png" style="cursor:pointer" id="hidelinkModalTrocarArmarios"
                            styleClass="xcloseModal"/>
            <rich:componentControl for="modalTrocarArmarios" attachTo="hidelinkModalTrocarArmarios" operation="hide"
                                   event="onclick"/>
        </h:panelGroup>

        <h:panelGroup>
            <h:outputText styleClass="titulo_modal_form" value="#{msg_aplic.prt_trocar_Armario}"/>
        </h:panelGroup>

        <h:form id="formModalTrocarArmarios">
            <h:panelGroup layout="block" id="panelFoto"
                          style="position: absolute;">
                <a4j:mediaOutput rendered="#{!SuperControle.fotosNaNuvem}"
                                 element="img" id="imagem1" style="border-radius:100%; padding-left:10px;"
                                 cacheable="false" session="true"
                                 createContent="#{GestaoArmarioControle.paintFoto}"
                                 value="#{ImagemData}" mimeType="image/jpeg">
                    <f:param value="#{SuperControle.timeStamp}" name="time"/>
                    <f:param name="largura" value="50"/>
                    <f:param name="altura" value="50"/>
                </a4j:mediaOutput>
                <h:graphicImage rendered="#{SuperControle.fotosNaNuvem}"
                                width="80" height="96"
                                style="width:80px;height:96px;border-radius: 95%;"
                                url="#{GestaoArmarioControle.aluguelArmario.cliente.pessoa.urlFoto}">
                </h:graphicImage>
            </h:panelGroup>

            <h:panelGrid styleClass="tabela_modal" columnClasses="direita, esquerda" cellpadding="3" columns="2">
                <h:outputText value="#{msg_aplic.prt_Armario}" styleClass="tituloCampos"/>
                <h:outputText value="#{GestaoArmarioControle.armario.descricao}" styleClass="tituloCampos"/>


                <h:outputText value="#{msg_aplic.prt_Cliente_armario}" styleClass="tituloCampos"/>
                <h:outputText value="#{GestaoArmarioControle.armario.aluguelAtual.cliente.nome_Apresentar}"
                              styleClass="tituloCampos"/>
                <h:outputText value="#{msg_aplic.prt_Produto_armario}" styleClass="tituloCampos"/>
                <h:outputText value="#{GestaoArmarioControle.armario.aluguelAtual.produto.descricao}"
                              styleClass="tituloCampos"/>
                <h:outputText value="#{msg_aplic.prt_DataFim_armario}" styleClass="tituloCampos"/>
                <h:outputText value="#{GestaoArmarioControle.armario.aluguelAtual.fimApresentar}"
                              styleClass="tituloCampos"/>
                <h:outputText styleClass="tituloCampos" value="Para Armario"/>
                <h:selectOneMenu id="tamanho_editar" onblur="blurinput(this);" onfocus="focusinput(this);"
                                 styleClass="form" value="#{GestaoArmarioControle.armarioTrocaSelecionado}">
                    <f:selectItems value="#{GestaoArmarioControle.armariosTroca}"/>
                    <a4j:support event="onchange" action="#{GestaoArmarioControle.selecionarArmarioTrocar}"
                                 reRender="formModalTrocarArmarios"/>
                </h:selectOneMenu>
            </h:panelGrid>
            <h:panelGrid columns="1" width="100%" style="text-align:center;padding-top:15px;padding-bottom:15px;">
                <a4j:commandButton
                        style="background:none; background-color: rgb(0, 70, 113); border-radius:5px; padding: 5px 30px 5px 30px; color: #ffffff"
                        id="gravarAluguelArmario"
                        action="#{GestaoArmarioControle.trocarArmario}"
                        value="#{msg_aplic.prt_trocarArmario}"
                        reRender="panelConteudo,panelMensagem3,panelAutorizacaoFuncionalidade"
                        oncomplete="#{GestaoArmarioControle.msgAlert}"/>
            </h:panelGrid>
            <h:panelGrid id="panelMensagem4" columns="1" width="100%" style="padding:15;">
                <h:outputText styleClass="mensagem" value="#{GestaoArmarioControle.mensagem}"/>
                <h:outputText styleClass="mensagemDetalhada" value="#{GestaoArmarioControle.mensagemDetalhada}"/>
            </h:panelGrid>
        </h:form>
    </rich:modalPanel>
    <!-- ################# ALUGAR ou FECHAR ##################### -->
    <rich:modalPanel id="modalAlugarArmarios" autosized="true" shadowOpacity="true" width="500" height="250"
                     style="border: none; border-radius: 15px; background: none; background-color: #ffffff;">

        <h:panelGroup>
            <h:graphicImage value="/imagens/close.png" style="cursor:pointer" id="hidelinkModalAlugarArmarios"
                            styleClass="xcloseModal"/>
            <rich:componentControl for="modalAlugarArmarios" attachTo="hidelinkModalAlugarArmarios" operation="hide"
                                   event="onclick"/>
        </h:panelGroup>

        <h:panelGroup>
            <h:outputText styleClass="titulo_modal_form" value="#{msg_aplic.prt_alugar_Armario}"/>
        </h:panelGroup>

        <h:form id="formModalAlugarArmarios">
            <h:panelGroup layout="block" id="panelFoto"
                          style="position: absolute;"
                          rendered="#{GestaoArmarioControle.aluguelArmario.cliente.codigo != null && GestaoArmarioControle.aluguelArmario.cliente.codigo != 0}">
                <a4j:mediaOutput rendered="#{!SuperControle.fotosNaNuvem}"
                                 element="img" id="imagem1" style="border-radius:100%; padding-left:10px;"
                                 cacheable="false" session="true"
                                 createContent="#{GestaoArmarioControle.paintFoto}"
                                 value="#{ImagemData}" mimeType="image/jpeg">
                    <f:param value="#{SuperControle.timeStamp}" name="time"/>
                    <f:param name="largura" value="50"/>
                    <f:param name="altura" value="50"/>
                </a4j:mediaOutput>
                <h:graphicImage rendered="#{SuperControle.fotosNaNuvem}"
                                width="80" height="96"
                                style="width:80px;height:96px;border-radius: 95%;"
                                url="#{GestaoArmarioControle.aluguelArmario.cliente.pessoa.urlFoto}">
                </h:graphicImage>
            </h:panelGroup>

            <h:panelGrid id="gridInformacoes" styleClass="tabela_modal" columnClasses="direita, esquerda"
                         cellpadding="3" columns="2">
                <h:outputText value="#{msg_aplic.prt_Armario}" styleClass="tituloCampos"/>
                <h:outputText value="#{GestaoArmarioControle.armario.descricao}" styleClass="tituloCampos"/>

                <h:outputText value="#{msg_aplic.prt_inicio}" styleClass="tituloCampos"/>
                <h:outputText value="#{GestaoArmarioControle.aluguelArmario.inicioApresentar}"
                              rendered="#{GestaoArmarioControle.aluguelArmario.codigo != null && GestaoArmarioControle.aluguelArmario.codigo != 0}"
                              styleClass="tituloCampos"/>
                <h:panelGroup>
                <rich:calendar id="dataInicioAluguel"
                               rendered="#{GestaoArmarioControle.aluguelArmario.codigo == null || GestaoArmarioControle.aluguelArmario.codigo == 0}"
                               value="#{GestaoArmarioControle.inicioAluguel}"
                               inputSize="10"
                               inputClass="form"
                               oninputblur="blurinput(this);"
                               oninputfocus="focusinput(this);"
                               oninputchange="validar_Data(this.id);mudarData();"
                               datePattern="dd/MM/yyyy"
                               enableManualInput="true"
                               onchanged="mudarData();"
                               zindex="2"
                               showWeeksBar="false">
                </rich:calendar>
                <a4j:jsFunction name="mudarData" action="#{GestaoArmarioControle.mudarData}" reRender="formModalAlugarArmarios"/>
                </h:panelGroup>


                <h:outputText value="Cliente" styleClass="tituloCampos"/>
                <h:outputText value="#{GestaoArmarioControle.aluguelArmario.cliente.pessoa.nome}"
                              styleClass="tituloCampos"
                              rendered="#{GestaoArmarioControle.aluguelArmario.codigo != null && GestaoArmarioControle.aluguelArmario.codigo != 0}"/>

                <h:panelGroup
                        rendered="#{GestaoArmarioControle.aluguelArmario.codigo == null || GestaoArmarioControle.aluguelArmario.codigo == 0}">
                    <h:inputText id="nomeLocatario" size="30"
                                 maxlength="50" onblur="blurinput(this);" onfocus="focusinput(this);" styleClass="form"
                                 tabindex="1"
                                 value="#{GestaoArmarioControle.aluguelArmario.cliente.pessoa.nome}"
                            />
                    <rich:suggestionbox height="200" width="200"
                                        for="nomeLocatario"
                                        fetchValue="#{result.pessoa.nome}"
                                        suggestionAction="#{GestaoArmarioControle.executarAutocompleteConsultaCliente}"
                                        minChars="1" rowClasses="20"
                                        status="statusHora"
                                        nothingLabel="Nenhum Cliente encontrado !"
                                        var="result" id="suggestionNomeComprador" reRender="mensagem">
                        <a4j:support event="onselect" action="#{GestaoArmarioControle.selecionarClienteSuggestionBox}"
                                     reRender="formModalAlugarArmarios"/>
                        <h:column>
                            <h:outputText value="#{result.pessoa.nome}"/>
                        </h:column>
                    </rich:suggestionbox>
                </h:panelGroup>

                <h:outputText styleClass="tituloCampos" value="Produto"/>
                <h:selectOneMenu id="tamanho_editar" onblur="blurinput(this);" onfocus="focusinput(this);"
                                 styleClass="form" value="#{GestaoArmarioControle.aluguelArmario.produto.codigo}"
                                 rendered="#{GestaoArmarioControle.aluguelArmario.codigo == null || GestaoArmarioControle.aluguelArmario.codigo == 0}">
                    <f:selectItems value="#{GestaoArmarioControle.produtos}"/>
                    <a4j:support event="onchange" action="#{GestaoArmarioControle.selecionarProduto}"
                                 reRender="formModalAlugarArmarios"/>
                </h:selectOneMenu>
                <h:outputText styleClass="tituloCampos"
                              value="#{GestaoArmarioControle.aluguelArmario.produto.descricao}"
                              rendered="#{GestaoArmarioControle.aluguelArmario.codigo != null && GestaoArmarioControle.aluguelArmario.codigo != 0}"/>

                <h:outputText styleClass="tituloCampos" value="Valor"
                              rendered="#{GestaoArmarioControle.aluguelArmario.produto.codigo != null && GestaoArmarioControle.aluguelArmario.produto.codigo != 0}"/>
                <h:outputText styleClass="tituloCampos" value="#{GestaoArmarioControle.aluguelArmario.valorApresentar}"
                              rendered="#{GestaoArmarioControle.aluguelArmario.produto.codigo != null && GestaoArmarioControle.aluguelArmario.produto.codigo != 0}"/>

                <h:outputText value="#{msg_aplic.prt_fim}" styleClass="tituloCampos"
                              rendered="#{(GestaoArmarioControle.aluguelArmario.produto.codigo != null
                                          && GestaoArmarioControle.aluguelArmario.produto.codigo != 0)
                                          && !GestaoArmarioControle.podeAlterarVigencia && (!GestaoArmarioControle.habilitarGestaoArmarios || GestaoArmarioControle.fecharNegociacao )}"/>

                <h:panelGroup rendered="#{(GestaoArmarioControle.aluguelArmario.produto.codigo != null 
                                          && GestaoArmarioControle.aluguelArmario.produto.codigo != 0)
                                          && !GestaoArmarioControle.podeAlterarVigencia && (!GestaoArmarioControle.habilitarGestaoArmarios || GestaoArmarioControle.fecharNegociacao )}">
                    <c:if test="${GestaoArmarioControle.habilitarGestaoArmarios}">
                        <h:outputText styleClass="tituloCampos"
                                      value="#{msg_aplic.prt_fim} - #{armario.aluguelAtual.dataFimOriginalApresentar}"/>
                    </c:if>
                    <c:if test="${!GestaoArmarioControle.habilitarGestaoArmarios}">
                        <h:outputText styleClass="tituloCampos"
                                      value="#{msg_aplic.prt_fim} - #{armario.aluguelAtual.finalVigenciaApresentar}"/>
                    </c:if>
                    <a4j:commandLink rendered="#{!GestaoArmarioControle.habilitarGestaoArmarios}"
                                     styleClass="tituloCampos" value="Alterar Vigencia"
                                     action="#{GestaoArmarioControle.onMudarDataVigencia}"
                                     reRender="panelAutorizacaoFuncionalidade"/>
                </h:panelGroup>
                <h:panelGroup layout="block" rendered="#{(GestaoArmarioControle.aluguelArmario.produto.codigo != null
                                           && GestaoArmarioControle.aluguelArmario.produto.codigo != 0)
                                           && GestaoArmarioControle.podeAlterarVigencia}">
                    <h:outputText value="#{msg_aplic.prt_fim}" styleClass="tituloCampos"/>
                </h:panelGroup>
                <h:panelGroup layout="block" rendered="#{(GestaoArmarioControle.aluguelArmario.produto.codigo != null
                                           && GestaoArmarioControle.aluguelArmario.produto.codigo != 0)
                                           && GestaoArmarioControle.podeAlterarVigencia}">

                    <rich:calendar id="dataFimAluguel"
                                   value="#{GestaoArmarioControle.aluguelArmario.movProduto.dataFinalVigencia}"
                                   inputSize="10"
                                   inputClass="form"
                                   oninputblur="blurinput(this);"
                                   oninputfocus="focusinput(this);"
                                   oninputchange="return validar_Data(this.id);"
                                   datePattern="dd/MM/yyyy"
                                   enableManualInput="true"
                                   zindex="2"
                                   showWeeksBar="false">
                    </rich:calendar>
                    <rich:jQuery id="mskData" selector=".rich-calendar-input" timing="onload" query="mask('99/99/9999')" />
                </h:panelGroup>
                <h:outputText value="#{msg_aplic.prt_nrVezes_Parcelar}" styleClass="tituloCampos"
                              rendered="#{(GestaoArmarioControle.aluguelArmario.codigo == null || GestaoArmarioControle.aluguelArmario.codigo == 0) 
                                          && (GestaoArmarioControle.aluguelArmario.produto.codigo != null && GestaoArmarioControle.aluguelArmario.produto.codigo != 0)}"/>
                <rich:inputNumberSpinner minValue="1" maxValue="72"
                                         rendered="#{(GestaoArmarioControle.aluguelArmario.codigo == null || GestaoArmarioControle.aluguelArmario.codigo == 0) 
                                                     && (GestaoArmarioControle.aluguelArmario.produto.codigo != null && GestaoArmarioControle.aluguelArmario.produto.codigo != 0)}"
                                         value="#{GestaoArmarioControle.nrVezesParcelamento}">
                    <a4j:support event="onchange" action="#{GestaoArmarioControle.atualizarVenda}"
                                 reRender="formModalAlugarArmarios"/>
                </rich:inputNumberSpinner>

                <h:outputText value="#{msg_aplic.prt_primeira_parcela}" styleClass="tituloCampos"
                              rendered="#{(GestaoArmarioControle.aluguelArmario.codigo == null || GestaoArmarioControle.aluguelArmario.codigo == 0) 
                                          && (GestaoArmarioControle.aluguelArmario.produto.codigo != null && GestaoArmarioControle.aluguelArmario.produto.codigo != 0)}"/>
                <h:panelGroup id="pnl-dt-parcela" layout="block">
                <rich:calendar inputSize="10"
                               id="cal-parcela"
                               inputClass="form"
                               oninputblur="blurinput(this);"
                               oninputfocus="focusinput(this);"
                               oninputchange="return validar_Data(this.id);"
                               datePattern="dd/MM/yyyy"
                               enableManualInput="true"
                               rendered="#{(GestaoArmarioControle.aluguelArmario.codigo == null || GestaoArmarioControle.aluguelArmario.codigo == 0) 
                                           && (GestaoArmarioControle.aluguelArmario.produto.codigo != null && GestaoArmarioControle.aluguelArmario.produto.codigo != 0)}"
                               zindex="2" showWeeksBar="false"
                               value="#{GestaoArmarioControle.vencimentoPrimeiraParcela}">
                    <a4j:support process="cal-parcela" event="onchanged" action="#{GestaoArmarioControle.atualizarVenda}" reRender="formModalAlugarArmarios"/>
                </rich:calendar>
                <rich:jQuery selector=".rich-calendar-input" timing="onload" query="mask('99/99/9999')" />
                </h:panelGroup>
                <h:outputText
                        rendered="#{GestaoArmarioControle.habilitarGestaoArmarios && GestaoArmarioControle.fecharNegociacao}"
                        styleClass="tituloCampos" value="#{msg_aplic.prt_modeloContrato}"/>
                <h:selectOneMenu onblur="blurinput(this);" onfocus="focusinput(this);"
                                 styleClass="form"
                                 rendered="#{GestaoArmarioControle.habilitarGestaoArmarios && GestaoArmarioControle.fecharNegociacao}"
                                 value="#{GestaoArmarioControle.modeloContrato}">
                    <f:selectItems value="#{GestaoArmarioControle.modelosContrato}"/>
                </h:selectOneMenu>
                <h:outputText
                        rendered="#{GestaoArmarioControle.habilitarGestaoArmarios && GestaoArmarioControle.fecharNegociacao}"
                        styleClass="tituloCampos" value="#{msg_aplic.prt_contratoAssinado}"/>
                <h:selectBooleanCheckbox
                        rendered="#{GestaoArmarioControle.habilitarGestaoArmarios && GestaoArmarioControle.fecharNegociacao}"
                        value="#{GestaoArmarioControle.aluguelArmario.contratoAssinado}"/>

            </h:panelGrid>

            <h:panelGrid id="gridOperacoes" columns="1" width="100%"
                         style="text-align:center;padding-top:15px;padding-bottom:15px;">
                <h:panelGroup layout="block" style="height:120px;overflow-y: scroll;"
                              rendered="#{not empty GestaoArmarioControle.aluguelNegociar.vendaAvulsa.movParcelaVOs && GestaoArmarioControle.habilitarGestaoArmarios}">
                    <rich:dataTable id="tabelaParcelas"
                                    value="#{GestaoArmarioControle.aluguelNegociar.vendaAvulsa.movParcelaVOs}"
                                    rendered="#{not empty GestaoArmarioControle.aluguelNegociar.vendaAvulsa.movParcelaVOs && GestaoArmarioControle.habilitarGestaoArmarios}"
                                    width="100%" var="parcela">
                        <rich:column>
                            <f:facet name="header">
                                <h:outputText value="Parcela"/>
                            </f:facet>
                            <h:outputText value="#{parcela.descricao}"/>
                        </rich:column>
                        <rich:column>
                            <f:facet name="header">
                                <h:outputText value="Vencimento"/>
                            </f:facet>
                            <h:outputText value="#{parcela.dataVencimento_Apresentar}"/>
                        </rich:column>
                        <rich:column>
                            <f:facet name="header">
                                <h:outputText value="Valor"/>
                            </f:facet>
                            <h:outputText value="#{parcela.valorParcela_Apresentar}"/>
                        </rich:column>
                    </rich:dataTable>
                </h:panelGroup>
                <a4j:commandButton
                        style="margin-top:5px;background:none; background-color: rgb(0, 70, 113); border-radius:5px; padding: 5px 30px 5px 30px; color: #ffffff"
                        id="gravarAluguelArmario"
                        action="#{GestaoArmarioControle.pagarAluguel}"
                        value="#{msg_aplic.prt_finalizarEditarVenda}"
                        rendered="#{!GestaoArmarioControle.habilitarGestaoArmarios &&  GestaoArmarioControle.aluguelArmario.codigo != null && GestaoArmarioControle.aluguelArmario.codigo != 0}"
                        reRender="panelConteudo,panelMensagem4,panelAutorizacaoFuncionalidade,formModalAlugarArmarios"
                        oncomplete="#{GestaoArmarioControle.msgAlert}"/>
                <a4j:commandButton
                        style="margin-top:5px;background:none; background-color: rgb(0, 70, 113); border-radius:5px; padding: 5px 30px 5px 30px; color: #ffffff"
                        id="abrirArmario"
                        action="#{GestaoArmarioControle.abrirArmario}"
                        value="#{msg_aplic.prt_abrir_Armario}"
                        rendered="#{GestaoArmarioControle.aluguelArmario.codigo != null && GestaoArmarioControle.aluguelArmario.codigo != 0 and !GestaoArmarioControle.apresentarConfirmarDevolucao}"
                        reRender="panelConteudo,panelMensagem4,panelAutorizacaoFuncionalidade,gridOperacoes"
                        oncomplete="#{GestaoArmarioControle.msgAlert}"/>
                <a4j:commandLink
                        style="margin-top:5px;background:none; background-color: rgb(0, 70, 113);display: inline-block; border-radius:5px; padding: 5px 30px 5px 30px; color: #ffffff"
                        id="abrirTelaCaixa"
                        action="#{GestaoArmarioControle.abrirTelaCaixa}"
                        value="#{msg_aplic.prt_abrir_telaCaixa}"
                        rendered="#{GestaoArmarioControle.apresentarConfirmarDevolucao}"
                        reRender="panelConteudo,panelMensagem4,panelAutorizacaoFuncionalidade"
                        oncomplete="#{GestaoArmarioControle.msgAlert}">
                    <i class="fa-icon-money"></i>
                </a4j:commandLink>
                <a4j:commandButton
                        style="margin-top:5px;background:none; background-color: rgb(0, 70, 113); border-radius:5px; padding: 5px 30px 5px 30px; color: #ffffff"
                        value="Fechar"
                        rendered="#{GestaoArmarioControle.apresentarConfirmarDevolucao}"
                        reRender="panelConteudo,panelMensagem4,panelAutorizacaoFuncionalidade"
                        oncomplete="Richfaces.hideModalPanel('modalAlugarArmarios');">
                    <f:setPropertyActionListener value="#{false}"
                                                 target="#{GestaoArmarioControle.apresentarConfirmarDevolucao}"/>
                </a4j:commandButton>
                <a4j:commandButton
                        style="margin-top:5px;background:none; background-color: rgb(0, 70, 113); border-radius:5px; padding: 5px 30px 5px 30px; color: #ffffff"
                        id="gravarAlteracaoAluguelArmario"
                        rendered="#{GestaoArmarioControle.aluguelArmario.codigo == null || GestaoArmarioControle.aluguelArmario.codigo == 0}"
                        action="#{GestaoArmarioControle.pagarAluguel}"
                        value="#{GestaoArmarioControle.rotuloBotao}"
                        reRender="panelConteudo,panelMensagem4,panelAutorizacaoFuncionalidade,gridOperacoes,gridInformacoes,formModalAlugarArmarios"
                        oncomplete="#{GestaoArmarioControle.msgAlert}"/>
            </h:panelGrid>
            <h:panelGrid id="panelMensagem4" columns="1" width="100%" style="padding:15;">
                <h:outputText styleClass="mensagem" value="#{GestaoArmarioControle.mensagem}"/>
                <h:outputText styleClass="mensagemDetalhada" value="#{GestaoArmarioControle.mensagemDetalhada}"/>
            </h:panelGrid>
        </h:form>
    </rich:modalPanel>
    <!-- ################# FIM HISTORICO ARMARIO ##################### -->
    <rich:modalPanel id="modalHistoricoArmario" autosized="true" shadowOpacity="true"
                     minWidth="680">
        <f:facet name="header">
            <h:panelGroup>
                <h:outputText value="#{msg_aplic.prt_historicoArmario}"/>
            </h:panelGroup>
        </f:facet>

        <f:facet name="controls">
            <h:panelGroup>
                <h:graphicImage value="/imagens/close.png" style="cursor:pointer"
                                styleClass="xcloseModal" id="hidelinkHistoricoArmarios"/>
                <rich:componentControl for="modalHistoricoArmario" attachTo="hidelinkHistoricoArmarios" operation="hide"
                                       event="onclick"/>
            </h:panelGroup>
        </f:facet>

        <h:form id="formModalHistoricoArmario">
            <rich:dataTable id="idHistoricoAluguel" value="#{GestaoArmarioControle.armario.historicoAluguel}"
                            var="aluguelArmario"
                            rows="20" width="100%">
                <rich:column sortBy="#{aluguelArmario.cliente.pessoa.nome}">
                    <f:facet name="header">
                        <h:outputText value="#{msg_aplic.prt_Cliente_tituloForm}"/>
                    </f:facet>
                    <h:outputText value="#{aluguelArmario.cliente.pessoa.nome}"/>
                </rich:column>
                <rich:column sortBy="#{aluguelArmario.produto.descricao}">
                    <f:facet name="header">
                        <h:outputText value="#{msg_aplic.prt_Produto_tituloForm}"/>
                    </f:facet>
                    <h:outputText value="#{aluguelArmario.produto.descricao}"/>
                </rich:column>
                <rich:column sortBy="#{aluguelArmario.movProduto.dataInicioVigencia}">
                    <f:facet name="header">
                        <h:outputText value="#{msg_aplic.prt_inicio}"/>
                    </f:facet>
                    <h:outputText value="#{aluguelArmario.movProduto.dataInicioVigencia_Apresentar}"/>
                </rich:column>
                <rich:column sortBy="#{aluguelArmario.movProduto.dataFinalVigencia}">
                    <f:facet name="header">
                        <h:outputText value="#{msg_aplic.prt_fim}"/>
                    </f:facet>
                    <h:outputText value="#{aluguelArmario.movProduto.dataFinalVigencia_Apresentar}"/>
                </rich:column>
                <rich:column sortBy="#{aluguelArmario.responsavelCadastro.primeiroNomeConcatenado}">
                    <f:facet name="header">
                        <h:outputText value="#{msg_aplic.prt_MovProduto_label_responsavelLancamento}"/>
                    </f:facet>
                    <h:outputText value="#{aluguelArmario.responsavelCadastro.primeiroNomeConcatenado}"/>
                </rich:column>
                <rich:column sortBy="#{aluguelArmario.dataCadastro}">
                    <f:facet name="header">
                        <h:outputText value="#{msg_aplic.prt_Indicacao_dataCadastro}"/>
                    </f:facet>
                    <h:outputText value="#{aluguelArmario.dataCadastroApresentar}"/>
                </rich:column>
                <f:facet name="footer">
                    <h:panelGroup styleClass="pull-right">
                        <h:outputText value="#{fn:length(GestaoArmarioControle.armario.historicoAluguel)} Itens"/>
                    </h:panelGroup>
                </f:facet>
            </rich:dataTable>

            <rich:datascroller for="idHistoricoAluguel"/>
        </h:form>
    </rich:modalPanel>
    <%@include file="includes/autorizacao/include_autorizacao_funcionalidade.jsp" %>

</f:view>
