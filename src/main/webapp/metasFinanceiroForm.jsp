<%@page pageEncoding="ISO-8859-1" %>
<%@include file="pages/finan/includes/include_imports.jsp" %>
<%@include file="pages/finan/includes/include_head_finan.jsp" %>
<link href="beta/css/font-awesome_2.0.min.css" type="text/css" rel="stylesheet"/>
<f:view>
    <title>
        <h:outputText value="#{msg_aplic.prt_Finan_MetaFinanceiro_tituloForm}" />
        <c:set var="titulo" scope="session" value="Metas do Financeiro"/>
        <c:set var="urlWiki" scope="session"
               value="${SuperControle.urlBaseConhecimento}como-cadastrar-editar-meta-financeira-para-o-bi-velocimetro-do-modulo-financeiro/"/>
    </title>
    <jsp:include page="includes/include_carregando_ripple.jsp" flush="true"/>
    <body style="background-color:#FFFFFF;">
    <f:facet name="header">
        <jsp:include page="topoReduzido_material.jsp"/>
    </f:facet>
    <h:panelGrid columns="1" width="100%" cellpadding="0" cellspacing="0">
        <h:form id="form">
            <hr style="border-color: #e6e6e6;"/>
            <h:panelGrid columns="1" width="100%">
                <!-- FORMULARIO -->
                <h:panelGrid columns="2" rowClasses="linhaImpar, linhaPar" 
                             columnClasses="colunaDireita, colunaEsquerda, colunaEsquerda" width="100%">
                <h:panelGroup >
                    <!-- Empresa -->
                    <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_Finan_MetaFinanceiro_empresa}"
                    rendered="#{LoginControle.permissaoAcessoMenuVO.visualizarMetasFinanceirasTodasEmpresas}"/>
                    &nbsp;
                    </h:panelGroup >
                     <h:panelGroup >
                        <h:selectOneMenu rendered="#{LoginControle.permissaoAcessoMenuVO.visualizarMetasFinanceirasTodasEmpresas}" id="selectEmpresaMetaFinanceira"
                                         styleClass="form" value="#{MetaFinanceiroControle.meta.empresa.codigo}">
                            <f:selectItems value="#{MetaFinanceiroControle.listaSelectItemEmpresa}" />
                        </h:selectOneMenu>
                        <a4j:commandLink action="#{MetaFinanceiroControle.abrirTelaHistorico}"
                                         value="#{msg_aplic.prt_Finan_MetaFinanceiro_historico}"
                                         style="padding-left:20"
                                         reRender="panelHistorico"
                                         oncomplete="#{MetaFinanceiroControle.msgAlert}"/>
                    </h:panelGroup> 

                    <!-- mes e ano -->
                    <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_Finan_MetaFinanceiro_mes}" />
                    <h:panelGrid columns="3" columnClasses="colunaEsquerda, colunaDireita, colunaEsquerda" cellpadding="0" cellspacing="0">
                        <h:selectOneMenu id="mes" styleClass="form" value="#{MetaFinanceiroControle.meta.mes}">
                            <f:selectItems value="#{MetaFinanceiroControle.listaSelectItemMeses}" />
                        </h:selectOneMenu>

                        <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_Finan_MetaFinanceiro_ano}" />
                        <h:inputText id="ano" styleClass="form" size="5" value="#{MetaFinanceiroControle.meta.ano}"/>
                    </h:panelGrid>

                    <!-- descricao -->
                    <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_Finan_MetaFinanceiro_descricao}"/>
                    <h:inputText id="descricao" styleClass="form" value="#{MetaFinanceiroControle.meta.descricao}" size="90"/>

                    <!-- descricao -->
                    <h:outputText styleClass="tituloCampos" value="BI Velocímetro"/>

                   
                    <h:panelGrid columns="2" columnClasses="colunaEsquerda, colunaDireita, colunaEsquerda" cellpadding="0" cellspacing="2">
                        <h:outputText styleClass="tituloCampos" value="Receita:"/>
                        <h:inputText size="10" maxlength="40" onfocus="focusinput(this);" style="text-align: right;"
                                         onkeyup="return moeda(this);"
                                         styleClass="form" value="#{MetaFinanceiroControle.meta.receitaVeloc}">
                                <f:converter converterId="FormatadorNumerico" />
                            </h:inputText>
                        <h:outputText styleClass="tituloCampos" value="Faturamento:"/>
                        <h:inputText size="10" maxlength="40" onfocus="focusinput(this);" style="text-align: right;"
                                         onkeyup="return moeda(this);"
                                         styleClass="form" value="#{MetaFinanceiroControle.meta.faturamentoVeloc}">
                                <f:converter converterId="FormatadorNumerico" />
                            </h:inputText>
                        <h:outputText styleClass="tituloCampos" value="Despesa:"/>
                        <h:inputText size="10" maxlength="40" onfocus="focusinput(this);" style="text-align: right;"
                                         onkeyup="return moeda(this);"
                                         styleClass="form" value="#{MetaFinanceiroControle.meta.despesaVeloc}">
                                <f:converter converterId="FormatadorNumerico" />
                            </h:inputText>
                        

                    </h:panelGrid>
                    


                    
                </h:panelGrid>

                    <!-- meta atingida -->
                    <h:panelGrid columns="1" columnClasses="centralizado" width="100%">
                        <h:panelGroup id="metaAtingida"
                                      style="padding: 0 10 0 10; vertical-align: middle;">
                            <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_Finan_MetaFinanceiro_atingida}:" style="vertical-align: middle; margin-right: 3px;"/>
                            
                            <h:panelGroup  style="background-color: #{MetaFinanceiroControle.meta.cor}; padding: 2px; border-width: 2px; border-style: solid; border-color: #000; vertical-align: middle; margin-right: 5;" >
                          		<h:outputText styleClass="tituloCampos" value="#{MetaFinanceiroControle.empresaLogado.moeda}" style="color: #{MetaFinanceiroControle.meta.corTexto}; vertical-align: middle;"/>
	                            <h:outputText value="#{MetaFinanceiroControle.meta.metaAtingida}" styleClass="tituloCampos"
	                            			  style="color: #{MetaFinanceiroControle.meta.corTexto}; vertical-align: middle;">
	                                <f:converter converterId="FormatadorNumerico" />
	                            </h:outputText>
                            </h:panelGroup>
                            
                            <a4j:commandButton style="width:20px; text-align:right; vertical-align: middle;"
                        				   reRender="metaAtingida" image="imagens/atualizar.png"
                                           action="#{MetaFinanceiroControle.atualizarMetaAtingida}"/>
                        </h:panelGroup>
                    </h:panelGrid>

                <!-- LISTA DE METAS -->

                <h:panelGrid columns="2" columnClasses="colunaEsquerda2, colunaDireita2" width="100%">
                    <h:panelGrid id="valoresMetas" columns="5" 
                    			 width="100%" rowClasses="linhaImpar, linhaImpar, linhaImpar, linhaImpar, linhaImpar, linhaImpar, linhaImpar"
                                 columnClasses="colunaDireita, centralizado, centralizado, centralizado">
                        <!-- titulos -->
                        <rich:spacer width="20px"/>
                        <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_Finan_MetaFinanceiro_metas}" />
                        <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_Finan_MetaFinanceiro_observacoes}" />
                        <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_Finan_MetaFinanceiro_cor}" />
                        <rich:spacer width="20px"/>

                        <!-- meta 1 -->
                        <h:outputText styleClass="tituloCampos" value="1"/>
                        <h:panelGroup style="vertical-align: middle;">
                            <h:outputText styleClass="tituloCampos" value="#{MetaFinanceiroControle.empresaLogado.moeda}" style="vertical-align: middle;"/>
                            <h:inputText size="10" maxlength="40" onfocus="focusinput(this);" style="text-align: right;"
                                         onkeyup="return moeda(this);"
                                         styleClass="form" value="#{MetaFinanceiroControle.meta.listaValor0.valor}">
                                <f:converter converterId="FormatadorNumerico" />
                            </h:inputText>
                        </h:panelGroup>
                        <h:inputText value="#{MetaFinanceiroControle.meta.valores[0].observacao}" size="80"/>
                        <h:panelGroup style="vertical-align: middle;">
                            <rich:colorPicker colorMode="hex" value="#{MetaFinanceiroControle.meta.listaValor0.cor}">
                            </rich:colorPicker>
                        </h:panelGroup>
                        
                        <h:panelGroup>
                        			<a4j:commandButton actionListener="#{MetaFinanceiroControle.limparValorMeta}"
                                                       image="/images/limpar.gif" title="Limpar"
                                                       reRender="valoresMetas">
                                    	<f:attribute name="index" value="0"/>
                                    </a4j:commandButton>                   
                        </h:panelGroup>
                        
                        <!-- meta 2 -->
                        <h:outputText styleClass="tituloCampos" value="2"/>
                        <h:panelGroup style="vertical-align: middle;">
                            <h:outputText styleClass="tituloCampos" value="#{MetaFinanceiroControle.empresaLogado.moeda}" style="vertical-align: middle;"/>
                            <h:inputText size="10" maxlength="40" onfocus="focusinput(this);" style="text-align: right;"
                                         onkeyup="return moeda(this);"
                                         styleClass="form" value="#{MetaFinanceiroControle.meta.listaValor1.valor}">
                                <f:converter converterId="FormatadorNumerico" />
                            </h:inputText>
                        </h:panelGroup>
                        <h:inputText value="#{MetaFinanceiroControle.meta.valores[1].observacao}" size="80"/>
                        <h:panelGroup style="vertical-align: middle;">
                            <rich:colorPicker colorMode="hex" value="#{MetaFinanceiroControle.meta.listaValor1.cor}">
                            </rich:colorPicker>
                        </h:panelGroup>
                        
                        <h:panelGroup>
                        			<a4j:commandButton actionListener="#{MetaFinanceiroControle.limparValorMeta}"
                                                       image="/images/limpar.gif" title="Limpar"
                                                       reRender="valoresMetas">
                                    	<f:attribute name="index" value="1"/>
                                    </a4j:commandButton>                   
                        </h:panelGroup>

                        <!-- meta 3 -->
                        <h:outputText styleClass="tituloCampos" value="3"/>
                        <h:panelGroup style="vertical-align: middle;">
                            <h:outputText styleClass="tituloCampos" value="#{MetaFinanceiroControle.empresaLogado.moeda}" style="vertical-align: middle;"/>
                            <h:inputText size="10" maxlength="40" onfocus="focusinput(this);" style="text-align: right;"
                                         onkeyup="return moeda(this);"
                                         styleClass="form" value="#{MetaFinanceiroControle.meta.listaValor2.valor}">
                                <f:converter converterId="FormatadorNumerico" />
                            </h:inputText>
                        </h:panelGroup>
                        <h:inputText value="#{MetaFinanceiroControle.meta.valores[2].observacao}" size="80"/>
                        <h:panelGroup style="vertical-align: middle;">
                            <rich:colorPicker colorMode="hex" value="#{MetaFinanceiroControle.meta.listaValor2.cor}">
                            </rich:colorPicker>
                        </h:panelGroup>
                        
                        <h:panelGroup>
                        			<a4j:commandButton actionListener="#{MetaFinanceiroControle.limparValorMeta}"
                                                       image="/images/limpar.gif" title="Limpar"
                                                       reRender="valoresMetas">
                                    	<f:attribute name="index" value="2"/>
                                    </a4j:commandButton>                   
                        </h:panelGroup>

                        <!-- meta 4 -->
                        <h:outputText styleClass="tituloCampos" value="4"/>
                        <h:panelGroup style="vertical-align: middle;">
                            <h:outputText styleClass="tituloCampos" value="#{MetaFinanceiroControle.empresaLogado.moeda}" style="vertical-align: middle;"/>
                            <h:inputText size="10" maxlength="40" onfocus="focusinput(this);" style="text-align: right;"
                                         onkeyup="return moeda(this);"
                                         styleClass="form" value="#{MetaFinanceiroControle.meta.listaValor3.valor}">
                                <f:converter converterId="FormatadorNumerico" />
                            </h:inputText>
                        </h:panelGroup>
                        <h:inputText value="#{MetaFinanceiroControle.meta.valores[3].observacao}" size="80"/>
                        <h:panelGroup style="vertical-align: middle;">
                            <rich:colorPicker colorMode="hex" value="#{MetaFinanceiroControle.meta.listaValor3.cor}">
                            </rich:colorPicker>
                        </h:panelGroup>
                        <h:panelGroup>
                        			<a4j:commandButton actionListener="#{MetaFinanceiroControle.limparValorMeta}"
                                                       image="/images/limpar.gif" title="Limpar"
                                                       reRender="valoresMetas">
                                    	<f:attribute name="index" value="3"/>
                                    </a4j:commandButton>                   
                        </h:panelGroup>

                        <!-- meta 5 -->
                        <h:outputText styleClass="tituloCampos" value="5"/>
                        <h:panelGroup style="vertical-align: middle;">
                            <h:outputText styleClass="tituloCampos" value="#{MetaFinanceiroControle.empresaLogado.moeda}" style="vertical-align: middle;"/>
                            <h:inputText size="10" maxlength="40" onfocus="focusinput(this);" style="text-align: right;"
                                         onkeyup="return moeda(this);"
                                         styleClass="form" value="#{MetaFinanceiroControle.meta.listaValor4.valor}">
                                <f:converter converterId="FormatadorNumerico" />
                            </h:inputText>
                        </h:panelGroup>
                        <h:inputText value="#{MetaFinanceiroControle.meta.valores[4].observacao}" size="80"/>
                        <h:panelGroup style="vertical-align: middle;">
                            <rich:colorPicker colorMode="hex" value="#{MetaFinanceiroControle.meta.listaValor4.cor}">
                            </rich:colorPicker>
                        </h:panelGroup>
                        <h:panelGroup>
                        			<a4j:commandButton id="limparPeriodoCompensacao"
                                                       actionListener="#{MetaFinanceiroControle.limparValorMeta}"
                                                       image="/images/limpar.gif" title="Limpar"
                                                       reRender="valoresMetas">
                                    	<f:attribute name="index" value="4"/>
                                    </a4j:commandButton>                   
                        </h:panelGroup>
                    </h:panelGrid>
                </h:panelGrid>


                <!-- CONSULTORES -->
                <rich:spacer width="10px" height="10px"/>
                <h:panelGrid columns="1" width="100%" headerClass="subordinado" columnClasses="centralizado" >
                    <f:facet name="header">
                        <h:outputText value="Consultores"/>
                    </f:facet>

                    <h:panelGrid id="panelConsultor" columns="2" columnClasses="colunaDireita, colunaEsquerda"   width="100%" rowClasses="linhaImpar">
                        <!-- consultor -->
                        <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_Finan_MetaFinanceiro_consultor}" />
                        <h:panelGroup >
                            <h:inputText  id="consultor" styleClass="form" value="#{MetaFinanceiroControle.consultor.colaborador.pessoa.nome}" size="50" readonly="true"/>
                            <a4j:commandButton style="vertical-align: middle;" focus="consultor" reRender="consultor" oncomplete="Richfaces.showModalPanel('panelColaborador')" image="imagens/informacao.gif" />
                        <a4j:commandLink action="#{MetaFinanceiroControle.gerarMetasParaConsultores}" value="#{msg_aplic.prt_Finan_MetaFinanceiro_gerar}"
                                         style="padding-left:10"
                                         reRender="consultores"/>
                        </h:panelGroup>
                        
                        <!-- percentual -->
                        <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_Finan_MetaFinanceiro_percentual}"/>
                        <h:panelGroup>
                            <h:inputText size="6" maxlength="6" onfocus="focusinput(this);" style="text-align: right;"
                                         onkeypress="return(currencyFormat(this,'.',',',event));" id="percentagem"
                                         styleClass="form" value="#{MetaFinanceiroControle.consultor.percentagem}">
                                <f:converter converterId="FormatadorNumerico" />
                            </h:inputText>
                            <h:outputText styleClass="tituloCampos" value="%" style="vertical-align: middle;"/>
                        </h:panelGroup>
                    </h:panelGrid>
                      <a4j:commandButton styleClass="botoes" image= "imagens/botaoAdicionar.png" action="#{MetaFinanceiroControle.adicionarConsultor}"
                                         reRender="consultores, panelConsultor, panelMensagem"/>

                </h:panelGrid>

                 <h:panelGrid columns="1" width="100%" headerClass="subordinado" >
                    <h:dataTable id="consultores" value="#{MetaFinanceiroControle.meta.consultores}" var="consultor"
                                 width="100%" headerClass="subordinado" rowClasses="linhaImpar"
                                 columnClasses="colunaEsquerda, colunaDireita, colunaDireita, colunaDireita, colunaDireita, colunaDireita, centralizado">

                        <!-- consultor -->
                        <h:column>
                            <f:facet name="header">
                                <h:outputText value="#{msg_aplic.prt_Finan_MetaFinanceiro_consultor}"/>
                            </f:facet>
                            <h:outputText styleClass="tituloCampos" value="#{consultor.colaborador.pessoa.nome}" rendered="#{consultor.codigo >= 0}"/>
                            <h:outputText styleClass="tituloCampos" value="#{consultor.colaborador.pessoa.nome}" rendered="#{consultor.codigo < 0}" 
                                          style="font-weight: bold; color: #0000AA;"/>
                        </h:column>

                        <!-- meta 1 -->
                        <h:column>
                            <f:facet name="header">
                                <h:outputText value="#{msg_aplic.prt_Finan_MetaFinanceiro_meta1}"/>
                            </f:facet>
                            <h:outputText styleClass="tituloCampos" value="#{consultor.meta1}" rendered="#{consultor.codigo >= 0}">
                                <f:converter converterId="FormatadorNumerico" />
                            </h:outputText>
                            <h:outputText styleClass="tituloCampos" value="#{consultor.meta1}" rendered="#{consultor.codigo < 0}" 
                                          style="font-weight: bold; color: #0000AA;">
                                <f:converter converterId="FormatadorNumerico" />
                            </h:outputText>
                        </h:column>

                        <!-- meta 2 -->
                        <h:column>
                            <f:facet name="header">
                                <h:outputText value="#{msg_aplic.prt_Finan_MetaFinanceiro_meta2}"/>
                            </f:facet>
                            <h:outputText styleClass="tituloCampos" value="#{consultor.meta2}" rendered="#{consultor.codigo >= 0}">
                                <f:converter converterId="FormatadorNumerico" />
                            </h:outputText>
                            <h:outputText styleClass="tituloCampos" value="#{consultor.meta2}" rendered="#{consultor.codigo < 0}" 
                                          style="font-weight: bold; color: #0000AA;">
                                <f:converter converterId="FormatadorNumerico" />
                            </h:outputText>
                        </h:column>
                        
                        <!-- meta 3 -->
                        <h:column>
                            <f:facet name="header">
                                <h:outputText value="#{msg_aplic.prt_Finan_MetaFinanceiro_meta3}"/>
                            </f:facet>
                            <h:outputText styleClass="tituloCampos" value="#{consultor.meta3}" rendered="#{consultor.codigo >= 0}">
                                <f:converter converterId="FormatadorNumerico" />
                            </h:outputText>
                            <h:outputText styleClass="tituloCampos" value="#{consultor.meta3}" rendered="#{consultor.codigo < 0}" 
                                          style="font-weight: bold; color: #0000AA;">
                                <f:converter converterId="FormatadorNumerico" />
                            </h:outputText>
                        </h:column>

                        <!-- meta 4 -->
                        <h:column>
                            <f:facet name="header">
                                <h:outputText value="#{msg_aplic.prt_Finan_MetaFinanceiro_meta4}"/>
                            </f:facet>
                            <h:outputText styleClass="tituloCampos" value="#{consultor.meta4}" rendered="#{consultor.codigo >= 0}">
                                <f:converter converterId="FormatadorNumerico" />
                            </h:outputText>
                            <h:outputText styleClass="tituloCampos" value="#{consultor.meta4}" rendered="#{consultor.codigo < 0}" 
                                          style="font-weight: bold; color: #0000AA;">
                                <f:converter converterId="FormatadorNumerico" />
                            </h:outputText>
                        </h:column>

                        <!-- meta 5 -->
                        <h:column>
                            <f:facet name="header">
                                <h:outputText value="#{msg_aplic.prt_Finan_MetaFinanceiro_meta5}"/>
                            </f:facet>
                            <h:outputText styleClass="tituloCampos" value="#{consultor.meta5}" rendered="#{consultor.codigo >= 0}">
                                <f:converter converterId="FormatadorNumerico" />
                            </h:outputText>
                            <h:outputText styleClass="tituloCampos" value="#{consultor.meta5}" rendered="#{consultor.codigo < 0}" 
                                          style="font-weight: bold; color: #0000AA;">
                                <f:converter converterId="FormatadorNumerico" />
                            </h:outputText>
                        </h:column>

                        <!-- opcoes -->
                        <h:column>
                            <f:facet name="header">
                                <h:outputText value="#{msg_aplic.prt_Finan_MetaFinanceiro_opcoes}" />
                            </f:facet>
                            <a4j:commandButton styleClass="botoes" image="imagens/botaoEditar.png" rendered="#{consultor.codigo >= 0}"
                                               action="#{MetaFinanceiroControle.editarConsultor}" reRender="panelConsultor"/>
                            <a4j:commandButton styleClass="botoes" image="imagens/botaoRemover.png" rendered="#{consultor.codigo >= 0}"
                                               action="#{MetaFinanceiroControle.excluirConsultor}" reRender="consultores"/>
                        </h:column>
                    </h:dataTable>
                </h:panelGrid>
                
                <h:panelGrid id="panelMensagem" columns="1" width="100%" styleClass="tabMensagens">
                    <!-- mensagens -->
                    <h:panelGrid columns="3" width="100%" styleClass="tabMensagens">
                        <h:commandButton rendered="#{MetaFinanceiroControle.sucesso}"
                                         image="imagens/sucesso.png" />
                        <h:commandButton rendered="#{MetaFinanceiroControle.erro}"
                                         image="imagens/erro.png" />
                        <h:panelGrid columns="1" width="100%">
                            <h:outputText styleClass="mensagem"
                                          value="#{MetaFinanceiroControle.mensagem}" />
                            <h:outputText styleClass="mensagemDetalhada"
                                          value="#{MetaFinanceiroControle.mensagemDetalhada}" />
                        </h:panelGrid>
                    </h:panelGrid>
                </h:panelGrid>

                <!-- botões -->
                <h:panelGrid columns="1" width="100%" columnClasses="colunaCentralizada">
                    <h:panelGroup>
                        <a4j:commandButton id="salvar" action="#{MetaFinanceiroControle.gravar}"
                                           alt="#{msg.msg_gravar_dados}"
                                           value="#{msg_bt.btn_gravar}"
                                           styleClass="botoes nvoBt" reRender="form" />
                        <rich:spacer width="10px"/>
                        <a4j:commandButton rendered="#{MetaFinanceiroControle.meta.codigo > 0}" id="clonar" reRender="form"
                                         action="#{MetaFinanceiroControle.clonar}"
                                         value="Clonar"
                                         styleClass="botoes nvoBt btSec" />
                        <rich:spacer width="10px"/>
                        <h:commandButton rendered="#{MetaFinanceiroControle.meta.codigo > 0}" id="excluir" onclick="return confirm('Confirma exclusão desta meta?');"
                                         action="#{MetaFinanceiroControle.excluir}"
                                         value="#{msg_bt.btn_excluir}"
                                         alt="#{msg.msg_excluir_dados}"
                                         styleClass="botoes nvoBt btSec btPerigo" />
                        <rich:spacer width="10px"/>
                        <h:commandButton id="consultar" immediate="true"
                                         action="#{MetaFinanceiroControle.voltarTelaConsulta}"
                                         value="#{msg_bt.btn_voltar_lista}"
                                         alt="#{msg.msg_consultar_dados}"
                                         styleClass="botoes nvoBt btSec" />
                        <rich:spacer width="10px"/>
                        <a4j:commandLink action="#{MetaFinanceiroControle.realizarConsultaLogObjetoSelecionado}"
                                           oncomplete="abrirPopup('visualizadorLogForm.jsp', 'VisualizadorLog', 785, 595);"
                                           title="Visualizar Log"
                                           style="display: inline-block; padding: 8px 15px;"
                                           styleClass="botoes nvoBt btSec">
                            <i style="text-decoration: none" class="fa-icon-list"/>
                        </a4j:commandLink>
                    </h:panelGroup>
                </h:panelGrid>
            </h:panelGrid>
        </h:form>
    </h:panelGrid>
    </body>

    <rich:modalPanel id="panelColaborador" autosized="true" shadowOpacity="true" width="450" height="250">
        <f:facet name="controls">
            <h:panelGroup>
                <h:graphicImage value="imagens/close.png" style="cursor:pointer" id="hidelink1"/>
                <rich:componentControl for="panelColaborador" attachTo="hidelink1" operation="hide" event="onclick"/>
            </h:panelGroup>
        </f:facet>

        <a4j:form id="formColaborador" ajaxSubmit="true">
            <h:panelGrid columns="1" rowClasses="linhaImpar, linhaPar" columnClasses="colunaEsquerda" width="100%" >
                <f:facet name="header">
                    <h:panelGrid columns="1" style="height:25px; background: url('imagens/fundoBarraTopo.png') repeat-x;"
                                 columnClasses="colunaCentralizada" width="100%">
                        <h:outputText value="Consulta Colaborador" styleClass="tituloFormulario"/>
                    </h:panelGrid>
                </f:facet>
                <h:panelGroup>
                    <h:outputText styleClass="tituloCampos" value="#{msg.msg_consultar_por}" style="vertical-align: middle;"/>
                    <rich:spacer width="10px"/>
                    <h:inputText id="valorConsultaColaborador" size="20" value="#{MetaFinanceiroControle.valorConsultaColaborador}"/>
                    <rich:spacer width="10px"/>
                    <a4j:commandButton  id="btnConsultarColaborador" reRender="resultadoConsultaColaborador, mensagemConsultaColaborador, scResultadoColaborador"
                                        action="#{MetaFinanceiroControle.consultarColaborador}" style="vertical-align: middle;"
                                        styleClass="botoes" value="#{msg_bt.btn_consultar}" image="imagens/botaoConsultar.png"
                                        alt="#{msg.msg_consultar_dados}"/>
                </h:panelGroup>

                <rich:dataTable id="resultadoConsultaColaborador" width="100%" headerClass="consulta"
                                rowClasses="linhaImpar, linhaPar" columnClasses="colunaAlinhamento"
                                value="#{MetaFinanceiroControle.colaboradores}" rows="5" var="colaborador">
                    <rich:column>
                        <f:facet name="header">
                            <h:outputText value="#{msg_aplic.prt_Usuario_colaborador}"/>
                        </f:facet>
                        <h:panelGroup>
                            <a4j:commandLink action="#{MetaFinanceiroControle.selecionarColaborador}" focus="colaborador"
                                             reRender="form, formColaborador" oncomplete="Richfaces.hideModalPanel('panelColaborador')"
                                             value="#{colaborador.pessoa.nome}"/>
                        </h:panelGroup>
                    </rich:column>
                    <rich:column>
                        <f:facet name="header">
                            <h:outputText value="#{msg_bt.btn_opcoes}"/>
                        </f:facet>
                        <a4j:commandButton id="selecionarColaborador" action="#{MetaFinanceiroControle.selecionarColaborador}"
                                           focus="colaborador" reRender="form, formColaborador" oncomplete="Richfaces.hideModalPanel('panelColaborador')"
                                           value="#{msg_bt.btn_selecionar}" image="imagens/botaoEditar.png"
                                           alt="#{msg.msg_selecionar_dados}" styleClass="botoes"/>
                    </rich:column>
                </rich:dataTable>
                <rich:datascroller align="center" for="formColaborador:resultadoConsultaColaborador" maxPages="10"
                                   id="scResultadoColaborador" />
            </h:panelGrid>
        </a4j:form>
    </rich:modalPanel>

    <%@include file="pages/finan/includes/include_historicoMetasFinanceiro.jsp" %>

</f:view>