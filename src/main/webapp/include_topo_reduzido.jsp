<%--
  Created by IntelliJ IDEA.
  User: Rafael
  Date: 27/10/2016
  Time: 08:40
  To change this template use File | Settings | File Templates.
--%>
<%@page contentType="text/html" %>
<%@page pageEncoding="ISO-8859-1" %>
<%@include file="includes/imports.jsp" %>
<h:panelGroup styleClass="topoModulos" layout="block" style="">
    <h:panelGroup layout="block" styleClass="blocoModulos col-md-6 pull-right">
        <h:panelGroup layout="block" styleClass="moduloAberto #{LoginControle.moduloAberto.sigla}"/>
        <h:panelGroup layout="block" styleClass="moduloFechados">
            <a4j:repeat value="#{LoginControle.modulosHabilitados}" var="modulo">
                <a4j:commandLink action="#{LoginControle.getAbrirModulo}"
                                 style="width: #{LoginControle.tamanhoModulosAbertos}%"
                                 rendered="#{not fn:contains(modulo.sigla,LoginControle.moduloAberto.sigla) && not fn:contains(modulo.sigla,'TR') && not fn:contains(modulo.sigla,'SLC') && not fn:contains(modulo.sigla,'NFSE')}"
                                 styleClass="moduloFechado #{modulo.sigla}">
                </a4j:commandLink>
                <h:outputLink  value="#{LoginControle.abrirModulo}" target="_blank"
                               style="width: #{LoginControle.tamanhoModulosAbertos}%"
                               rendered="#{not fn:contains(modulo.sigla,LoginControle.moduloAberto.sigla) && ((fn:contains(modulo.sigla,'TR') || fn:contains(modulo.sigla,'SLC') || fn:contains(modulo.sigla,'NFSE')))}"
                               styleClass="moduloFechado #{modulo.sigla} ">
                </h:outputLink>
            </a4j:repeat>
        </h:panelGroup>

    </h:panelGroup>
</h:panelGroup>
<h:panelGroup id="topoNovo" layout="block" styleClass="topoOpcoes">
    <h:panelGroup styleClass="pull-right painelAlunosMarcados"  id="painelAlunosMarcados">
        <rich:dragIndicator id="indicator" >
            <f:facet name="single">
                <h:panelGroup>
                    {testDrop}
                </h:panelGroup>
            </f:facet>
        </rich:dragIndicator>
        <a4j:repeat value="#{ClientesMarcadosControle.clientesMarcados}" var="clienteM">
            <a4j:outputPanel>
                <rich:dragSupport  dragIndicator=":form:indicator"
                                   dragType="alunos" dragValue="#{clienteM}"
                                   ondragstart="subirPainelRemoverAluno()"
                                   ondragend="sumirPainelRemoverAluno()">
                    <rich:dndParam name="testDrop">
                        <h:graphicImage url="#{(SuperControle.fotosNaNuvem ? clienteM.pessoa.urlFoto : clienteM.pessoa.urlFotoContexto )}"
                                        styleClass="imagemAluno pequena"/>
                    </rich:dndParam>
                </rich:dragSupport>
                <div class="clienteMarcado" style="font-family: Arial; font-size: 14px;">
                    <a4j:commandLink style="margin-left: 10px;" action="#{ClientesMarcadosControle.abrirCliente}"
                                     styleClass="tooltipclientesMarcados">
                        <h:graphicImage styleClass="imagemAluno pequena"
                                        url="#{(SuperControle.fotosNaNuvem ? ClientesMarcadosControle.fotoNuvem : clienteM.pessoa.urlFotoContexto )}">
                        </h:graphicImage>
                    </a4j:commandLink>
                    <div class="mensagemCmarc" >
                        <h:outputText value="#{clienteM.nomeLembrete}" escape="false"/>
                    </div>
                    <div class="cmarc">
                        <a4j:commandLink title="Adicionar um lembrete"
                                         styleClass="tooltipsterright"
                                         onclick="jQuery('.cliente#{clienteM.codigo}').toggle()"
                                         status="false">
                            <i class="fa-icon-comments" style="line-height: 24px; margin-left: 5px;"></i>
                        </a4j:commandLink>
                        <h:panelGroup styleClass="caixaLembrete cliente#{clienteM.codigo}" layout="block"
                        >
                            <h:inputTextarea styleClass="inputTextClean" value="#{clienteM.observacao}"
                                             style="width: calc(100% - 20px); margin-top:10px; margin-left:10px; height: 70px;"></h:inputTextarea>
                            <a4j:commandLink styleClass="botaoPrimarioSmall texto-size-14-real"
                                             action="#{ClientesMarcadosControle.gravarObservacao}"
                                             reRender="painelAlunosMarcados"
                                             style="float: right; margin-right: 10px;margin-top: 5px">
                                Gravar
                            </a4j:commandLink>
                        </h:panelGroup>
                    </div>
                </div>
            </a4j:outputPanel>
        </a4j:repeat>
        <script>
            carregarTooltipster();
        </script>
    </h:panelGroup>
    <h:panelGroup layout="block" styleClass="logoIcones col-celula pull-left tudo">

        <h:panelGroup layout="block" style="margin-left: 20px;display: inline-block;" styleClass="logo-topo">
            <a4j:commandLink action="#{LoginControle.abrirModulo}">
                <h:graphicImage height="55px"
                        url="/imagens_flat/#{LoginControle.moduloAberto.logoModulo}" />
            </a4j:commandLink>
        </h:panelGroup>
        <rich:spacer width="15"/>

        <a4j:jsFunction name="verificarTemNova" reRender="painelFeed"
                        status="false"
                        action="#{FeedGestaoControle.verificarTemNova}">
        </a4j:jsFunction>
        <h:panelGroup id="painelFeed" layout="block" style="display: inline-block" rendered="true">
            <a4j:commandLink oncomplete="Richfaces.showModalPanel('feedGestao'); indiceAtual = 1;"
                             reRender="feedGestao, painelFeed"
                             status="false"
                             rendered="#{FeedGestaoControle.nrMsgsNaoLidas > 0}"
                             action="#{FeedGestaoControle.abrirFeed}">
                <h:graphicImage  url="/feed_gestao/assistente_sem_notif.png" width="60" height="60"
                                 title="Assistente de Gestão Pacto"/>
            </a4j:commandLink>
            <h:panelGroup id="nrlidaFeed" rendered="#{FeedGestaoControle.nrMsgsNaoLidas > 0}"
                          layout="block" style="float:right;display: inline;position: absolute;">
                <div class="notificacaoAtividades">
                    <h:outputText value="#{FeedGestaoControle.nrMsgsNaoLidas}"></h:outputText>
                </div>
            </h:panelGroup>
            <h:outputLink styleClass="linkWiki"
                          rendered="#{FeedGestaoControle.nrMsgsNaoLidas > 0}"
                          value="#{SuperControle.urlWiki}ZillyonWeb:Assistente_Gestao_Pacto"
                          title="Clique e saiba mais: Feed de gestão"
                          target="_blank">
                <i class="fa-icon-question-sign" style="font-size: 18px"></i>
            </h:outputLink>
        </h:panelGroup>

        <a4j:commandButton style="display: none;" reRender="topoNovo"
                           status="false"
                           id="btnAtualizaFotoUsuario">
        </a4j:commandButton>

        <h:panelGroup layout="block" style="width: 350px;display: inline-block;vertical-align: top;margin-left: 40px;margin-top: 15px;">
            <jsp:include page="includes/include_expiracao.jsp" flush="true"/>
        </h:panelGroup>
    </h:panelGroup>
</h:panelGroup>
