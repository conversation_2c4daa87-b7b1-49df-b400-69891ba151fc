<%@page pageEncoding="ISO-8859-1" %>
<%--
  Created by IntelliJ IDEA.
  User: hellison
  Date: 24/02/2016
  Time: 16:57
  To change this template use File | Settings | File Templates.
--%>
<%@include file="/includes/verificaModulo.jsp" %>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html" %>
<%@taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@include file="/includes/include_import_minifiles.jsp" %>
<link href="${root}/css/menu_zw_ui_v1.20.css" rel="stylesheet" type="text/css">
<link href="${root}/beta/css/pacto-icon-font4.0.min.css" type="text/css" rel="stylesheet"/>
<jsp:include page="include_localize_traducao_linguagem.jsp"/>
<link rel="icon" type="image/png" href=".${LoginControle.favIconModule}"/>
<c:set var="from" value="popup" scope="request" />
<c:if test="${SuperControle.ativarGoogleAnalytics}">
    <!-- Google Analytics -->
    <script>
        (function(i,s,o,g,r,a,m){i['GoogleAnalyticsObject']=r;i[r]=i[r]||function(){
            (i[r].q=i[r].q||[]).push(arguments)},i[r].l=1*new Date();a=s.createElement(o),
            m=s.getElementsByTagName(o)[0];a.async=1;a.src=g;m.parentNode.insertBefore(a,m)
        })(window,document,'script','https://www.google-analytics.com/analytics.js','ga');

        tela = window.location.pathname;
        ga('create', 'UA-113292205-2', 'auto');
        ga('set', '&uid', '${SuperControle.usuarioLogado.username}');
        <%--ga('send', 'pageview');--%>
        ga('send', 'event', 'popup', 'pageView',tela);


    </script>
    <style type="text/css">
        @media print {
            body {
                display: none;
            }
        }
        /*
        * Removido devido a solicitações de clientes.
        html {
            user-select: none;
        }*/
    </style>
    <!-- End Google Analytics -->
</c:if>
<style>
    body{
        min-width: inherit !important;
    }
    .container {
        height: 80px;
        text-align: center;
        font: 0/0 a;
        /*width: 170px;*/
    }

    /*centralizar a imagem verticalmente*/
    .container:before {
        content: ' ';
        display: inline-block;
        vertical-align: middle;
        height: 100%;
    }

    .element {
        display: inline-block;
        vertical-align: middle; /* vertical alignment of the inline element */
        font: 16px/1 Arial sans-serif; /* <-- reset the font property */
    }

    .fa-external-link-square:hover {
        color: #29abe2 !important;
    }
    .element{
        width: 100%;
    }
    .image-logo {
        float: left;
        margin-left: 20px;
        text-align: center;
        width: 54px;
        height: 54px;
    }

    .btnUCP {
        -webkit-background-clip: padding-box;
        -webkit-box-shadow: rgba(0, 0, 0, 0.701961) 0px 1px 1px 0px;
        -webkit-font-smoothing: subpixel-antialiased;
        background-color: #F06D29;
        box-shadow: rgba(0, 0, 0, 0.701961) 0px 1px 1px 0px;
        color: rgb(255, 255, 255) !important;
        cursor: pointer;
        font-family: 'Helvetica Neue', Helvetica, sans-serif;
        font-size: 10px !important;
        line-height: 0;
        text-align: center;
        text-shadow: rgba(0, 0, 0, 0.4) 0px -1px 0px;
        border-radius: 10px;
        padding: 10px 10px 10px 10px;
         position: relative;
        /* top: -14px; */
        float: right;
        margin-right: 15px;
        margin-top: 15px;
    }

    #gpt-iframe-button {
        padding-right: 16px;
    }
    #gpt-iframe-button:hover {
        color: #387edb;
    }
    .iframe-container {
        position: fixed;
        bottom: 40px;
        right: 65px;
        width: 397px;
        height: 562px;
        z-index: 1000;
        border-radius: 25px;
        box-shadow: 0 0 16px rgba(0, 0, 0, 0.5);
    }
    .iframe-container iframe {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        border: none;
        border-radius: 25px;
    }
    #id-baloon-ponto-interrogacao {
        left: calc(100vw - 495px)!important;
        top: -60px!important;
    }
    #idpontointerrogacao {
        padding-top: 20px;
        margin-right: 20px;
        margin-top: 0.83em;
    }
    .ponto-interrogacao {
        max-height: 505px!important;
    }

    .videos-conhecimento-title {
        color: #55585E;
    }

    .videos-conhecimento-title,
    .ponto-interrogacao .video div .title,
    .ponto-interrogacao .video div .subtitle,
    .chat-max-gpt-content .title,
    .chat-max-gpt-content .text span,
    .ponto-interrogacao .aprendizado .title,
    .ponto-interrogacao .aprendizado .link-ui a,
    .ponto-interrogacao .precisa-ajuda .link-ajuda a {
        font-family: system-ui !important;
    }

</style>
<c:choose>
    <c:when test="${modulo eq 'centralEventos'}">
       <div class="moduloCE" style="height: 4px;"></div>
    </c:when>
    <c:when test="${modulo eq 'financeiroWeb'}">
        <div class="moduloFIN" style="height: 4px;"></div>
    </c:when>

    <c:when test="${modulo == 'crm'}">
        <div class="moduloCRM" style="height: 4px;"></div>
    </c:when>
    <c:otherwise>
        <div class="moduloZW" style="height: 4px;"></div>
    </c:otherwise>
</c:choose>
<div style="position: relative; width: 100%;">
    <div style="position: absolute; width: 100%; display: flex; justify-content: center;">
        <h2 style="text-align: center; padding-top: 20px; font-family: arial, sans-serif; font-size: 21px;">
            <c:out value="${titulo}"/>
            <a title="Clique e saiba mais:" target="_blank" href="${urlWiki}">
                <i class="fa-icon-question-sign" style="color: #9e9e9e; font-size: 18px;"></i>
            </a>
        </h2>
        <div id="idpontointerrogacao" class="topbar-menu-item" onclick="abrirPontoInterrogacao()" style="color:#1E60FA; position: absolute; right: 0;">
            <a style="color:#1E60FA;">
                <i class="pct pct-help-circle"></i>
            </a>
        </div>
    </div>

    <c:if test="${SuperControle.maxGpt}">
        <div id="iframe-container" class="iframe-container" style="display: none;">
            <iframe src="https://pgpt.pactosolucoes.com.br/?chat_open=True"></iframe>
        </div>
    </c:if>
</div>

<jsp:include page="/include_ponto_interrogacao.jsp"/>
<a4j:jsFunction name="notificarClickChat"
                action="#{SuperControle.notificarEmpresaClickChat}">
</a4j:jsFunction>

<h:panelGroup layout="block" styleClass="wrapper" style="height: 80px;">

    <c:if test="${!param.semTopoUCP}">
        <jsp:include page="include_topo_conhecimento_UCP_reduzido.jsp"/>
    </c:if>

    <h:panelGroup layout="block" styleClass="container">
        <h:panelGroup layout="block" styleClass="element">
            <c:choose>
                    <c:when test="${modulo eq 'centralEventos'}">
                       <h:graphicImage styleClass="image-logo" url="/imagens_flat/shortLogoCE.png"/>
                    </c:when>
                    <c:when test="${modulo eq 'financeiroWeb'}">
                        <h:graphicImage styleClass="image-logo" url="/imagens_flat/shortLogoFin.png"/>
                    </c:when>

                    <c:when test="${modulo == 'crm'}">
                        <h:graphicImage styleClass="image-logo" url="/imagens_flat/shortLogoCRM.png"/>
                    </c:when>
                    <c:otherwise>
                        <h:graphicImage styleClass="image-logo" url="/imagens_flat/pct-icone-fundo-administrativo.svg"/>
                    </c:otherwise>
            </c:choose>

        </h:panelGroup>
    </h:panelGroup>
</h:panelGroup>

<script>

    function abrirPontoInterrogacao() {
        try {
            let left = getOffset(document.getElementById('idpontointerrogacao')).left;
            document.getElementById("id-baloon-ponto-interrogacao").style.left = (left - 338) + 'px';
            document.getElementById('idpontointerrogacao').classList.add('topbar-item-selected');
        } catch (e) {
            console.log(e);
        }

        clearConhecimento();
        initConhecimento();
        jQuery('.modal-ponto-interrogacao').addClass('modal-open');
    }

    function fecharPonto() {
        jQuery('.modal-ponto-interrogacao').removeClass('modal-open');
        jQuery('.topbar-menu-item').removeClass('topbar-item-selected');
        clearConhecimento();
        jQuery('#conteudo-modal-mkt').html('');
    }

    jQuery(document).ready(function(){
        setTimeout(function(){
            console.log('click');
            jQuery('.searchbox-icon').click();
            jQuery('.searchbox-input').focus();
        }, 1000);
    });
    carregarTooltipster();
</script>

<c:if test="${LoginControle.apresentarHotjar}">
    <script>
        function hotjarParams(empresa, usuario, perfil) {
            hj('tagRecording', ['Empresa', empresa]);
            hj('tagRecording', ['Username', usuario]);
            hj('tagRecording', ['Perfil', perfil]);
        }

        (function(h,o,t,j,a,r){
            h.hj=h.hj||function(){(h.hj.q=h.hj.q||[]).push(arguments)};
            //hotjarParams('${LoginControle.empresa.nome}', '${LoginControle.usuario.username}', '${LoginControle.perfilAcesso.nome}');
            h._hjSettings={hjid:2500298,hjsv:6};
            a=o.getElementsByTagName('head')[0];
            r=o.createElement('script');r.async=1;
            r.src=t+h._hjSettings.hjid+j+h._hjSettings.hjsv;
            a.appendChild(r);
        })(window,document,'//static.hotjar.com/c/hotjar-','.js?sv=');

    </script>
</c:if>
