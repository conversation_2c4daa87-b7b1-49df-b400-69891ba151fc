<%@page contentType="text/html;charset=UTF-8" %>
<head>
    
    <%@include file="/includes/include_import_minifiles.jsp" %>
    <script type="text/javascript" language="javascript" src="script/vanilla-masker.min.js"></script>
    <script type="text/javascript" language="javascript" src="bootstrap/jquery.js"></script>
    <script type="text/javascript" src="script/tooltipster/jquery.tooltipster.min.js"></script>
    <script type="text/javascript">
        jQuery.noConflict();
    </script>
</head>
<link href="./beta/css/font-awesome_2.0.min.css" rel="stylesheet" type="text/css">
<link href="./css/otimize.css" rel="stylesheet" type="text/css">
<link href="beta/css/font-awesome_2.0.min.css" type="text/css" rel="stylesheet"/>
<script type="text/javascript" language="javascript" src="hoverform.js"></script>
<%@taglib prefix="f" uri="http://java.sun.com/jsf/core" %>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html" %>
<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax" %>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich" %>
<%@taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<f:loadBundle var="msg_bt" basename="propriedades.Botoes"/>
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao"/>
<f:loadBundle var="msg" basename="propriedades.Mensagens"/>
<head>
    <script>
        function toggleBtnProcess(disable, component, label) {
            var botao = document.getElementById(component);
            botao.setAttribute("disabled", disable);
            if (disable) {
                botao.setAttribute("value", "Aguarde...");
                botao.setAttribute("disabled", disable);
            } else {
                botao.setAttribute("value", label);
                botao.removeAttribute("disabled");
            }
        }

        function hideProgressBar(){
            var progressBar = document.getElementById('form:pgrBar');
            progressBar.style.display = 'none';
        }

        function carregarTooltipsterPlano() {
            carregarTooltipPlano(jQuery('.tooltipster'));
        }

        function carregarTooltipPlano(el) {
            el.tooltipster({
                theme: 'tooltipster-light',
                position: 'bottom',
                animation: 'grow',
                contentAsHTML: true
            });
        }

        function diaMenorQueDataAtual(dia) {
            var data = new Date(dia.date);
            data.setHours(0, 0, 0, 0);
            var hoje = new Date();
            hoje.setHours(0, 0, 0, 0);
            if (data < hoje) {
                return true;
            } else {
                return false;
            }
        }

        function inicioMinimoContratoStyleClass(dia) {
            if (diaMenorQueDataAtual(dia)) {
                return 'disabled';
            } else {
                return 'enabled';
            }
        }

        function inicioMinimoContratoDiaPermitido(dia) {
            return !diaMenorQueDataAtual(dia);
        }

    </script>
    <style>
        .tabela-recorrencia tbody tr td {
            vertical-align: initial;
        }

        .form-plano {
            overflow: initial;
        }

        .rich-calendar-cell.disabled {
            background-color: lightgrey;
        }
    </style>
</head>

<f:view>
    <%@include file="includes/include_carregando_ripple.jsp" %>
    <%@include file="includes/include_modal_mensagem_generica.jsp" %>
    <title>
        <h:outputText value="#{msg_aplic.prt_Plano_tituloForm}"/>
    </title>

    <a4j:loadScript src="/script/jquery.maskedinput-1.2.2.js"/>

    <rich:modalPanel id="modalContratosCancelamentoAutoma" styleClass="novaModal" autosized="true" shadowOpacity="true"
                     width="450" height="250">
        <f:facet name="header">
            <h:panelGroup>
                <h:outputText value="Ajustar Dias Cancelamento Automático"/>
            </h:panelGroup>
        </f:facet>
        <f:facet name="controls">
            <h:panelGroup>
                <h:outputText
                        styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign btnSelecionarModalidade"
                        id="hidelinkContratossadsadsd"/>
                <rich:componentControl for="modalContratosCancelamentoAutoma" attachTo="hidelinkContratossadsadsd"
                                       operation="hide" event="onclick">
                </rich:componentControl>
            </h:panelGroup>
        </f:facet>

        <a4j:form id="formModalContratosCancelamentoAutoma">
            <h:panelGrid columns="1" width="100%" columnClasses="colunaCentralizada">

                <h:outputText styleClass="texto-font texto-size-14-real texto-cor-cinza texto-bold"
                              value="#{PlanoControle.msgContratosCancelamentoAutomatico}"/>

                <h:panelGroup layout="block">

                    
                    <a4j:commandButton value="Alterar"
                                       action="#{PlanoControle.ajustarConsultarContratosDiasVencimentoCancelamentoAutomatico}"
                                       reRender="formModalContratosCancelamentoAutoma"
                                       rendered="#{PlanoControle.apresentarBtnContratosCancelamentoAutomatico}"
                                       styleClass="botoes nvoBt"/>

                    <a4j:commandButton value="Fechar"
                                       onclick="Richfaces.hideModalPanel('modalContratosCancelamentoAutoma');"
                                       reRender="modalContratosCancelamentoAutoma"
                                       styleClass="botoes nvoBt btSec btPerigo"/>
                </h:panelGroup>
            </h:panelGrid>
        </a4j:form>
    </rich:modalPanel>

    <rich:modalPanel id="modalContratosIniciamAposInicioMinimo"
                     styleClass="novaModal"
                     autosized="true"
                     shadowOpacity="true"
                     width="450"
                     height="250">
        <f:facet name="header">
            <h:panelGroup>
                <h:outputText value="Alterar inicio dos contratos"/>
            </h:panelGroup>
        </f:facet>

        <a4j:form id="formContratosIniciamAposInicioMinimo">
            <h:panelGrid columns="1"
                         width="100%"
                         columnClasses="colunaCentralizada">
                <h:outputText styleClass="texto-font texto-size-14-real texto-cor-cinza texto-bold"
                              value="Serão alteradas as datas de inicio de #{PlanoControle.qtdeContratosComInicioAposInicioMinimo} contratos."/>
                <h:outputText styleClass="texto-font texto-size-14-real texto-cor-cinza texto-bold"
                              value="Ao confirmar, todas as datas dos contratos com inicio a partir de amanhã serão alteradas para o dia #{PlanoControle.planoVO.inicioMinimoContratoFomatado}"/>
                <h:panelGroup layout="block">

                    <a4j:commandButton value="Confirmar"
                                       action="#{PlanoControle.alterarInicioDeContratos}"
                                       oncomplete="#{PlanoControle.mensagemNotificar};Richfaces.hideModalPanel('modalContratosIniciamAposInicioMinimo');"
                                       styleClass="botoes nvoBt"/>

                    <a4j:commandButton value="Cancelar"
                                       onclick="Richfaces.hideModalPanel('modalContratosIniciamAposInicioMinimo');"
                                       styleClass="botoes nvoBt btSec btPerigo"/>
                </h:panelGroup>
            </h:panelGrid>
        </a4j:form>
    </rich:modalPanel>

    <rich:modalPanel id="modalContratosCancelamentoProporcional" styleClass="novaModal" autosized="true"
                     shadowOpacity="true" width="450" height="250">
        <f:facet name="header">
            <h:panelGroup>
                <h:outputText value="Ajustar Cancelamento"/>
            </h:panelGroup>
        </f:facet>
        <f:facet name="controls">
            <h:panelGroup>
                <h:outputText
                        styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign btnSelecionarModalidade"
                        id="hidelinkContratosProporcional"/>
                <rich:componentControl for="modalContratosCancelamentoProporcional"
                                       attachTo="hidelinkContratosProporcional" operation="hide" event="onclick">
                </rich:componentControl>
            </h:panelGroup>
        </f:facet>

        <a4j:form id="formModalContratosCancelamentoProporcional">
            <h:panelGrid columns="1" width="100%" columnClasses="colunaCentralizada">

                <h:outputText styleClass="texto-font texto-size-14-real texto-cor-cinza texto-bold"
                              value="#{PlanoControle.msgContratosCancelamentoProporcional}"/>

                <h:panelGroup layout="block">

                    <a4j:commandButton value="Alterar"
                                       action="#{PlanoControle.ajustarConsultarContratosCancelamentoProporcional}"
                                       reRender="formModalContratosCancelamentoProporcional"
                                       rendered="#{PlanoControle.apresentarBtnContratosCancelamentoProporcional}"
                                       styleClass="botoes nvoBt"/>

                    <a4j:commandButton value="Fechar"
                                       onclick="Richfaces.hideModalPanel('modalContratosCancelamentoProporcional');"
                                       reRender="modalContratosCancelamentoProporcional"
                                       styleClass="botoes nvoBt btSec btPerigo"/>
                </h:panelGroup>
            </h:panelGrid>
        </a4j:form>
    </rich:modalPanel>

    <rich:modalPanel id="modalContratosRenovacaoAutomatica" styleClass="novaModal" autosized="true" shadowOpacity="true"
                     width="450" height="250">
        <f:facet name="header">
            <h:panelGroup>
                <h:outputText value="Contratos para Renovação Automática"/>
            </h:panelGroup>
        </f:facet>
        <f:facet name="controls">
            <h:panelGroup>
                <h:outputText
                        styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign btnSelecionarModalidade"
                        id="hidelinkContratosRenovAut"/>
                <rich:componentControl for="modalContratosRenovacaoAutomatica" attachTo="hidelinkContratosRenovAut"
                                       operation="hide" event="onclick">
                </rich:componentControl>
            </h:panelGroup>
        </f:facet>

        <a4j:form id="formContratosRenovacaoAutomatica">

            <h:panelGrid columns="1" width="100%" columnClasses="colunaCentralizada">


                <h:panelGrid
                        style="font-size:12px; font-style: italic; font-family: Arial;  text-align: left; padding-top: 20px">
                    ${PlanoControle.totalContratosRenovarAuto}
                </h:panelGrid>


                <rich:dataTable id="dtClientes" styleClass="tabelaSimplesCustom" width="100%"
                                value="#{PlanoControle.listaContratosRenovacaoAutomatica}" rows="5" var="contrato">
                    <rich:column>
                        <f:facet name="header">
                            <h:outputText styleClass="texto-font texto-size-14-real texto-cor-cinza texto-bold"
                                          value="Clientes"/>
                        </f:facet>
                        <h:outputText styleClass="texto-cor-cinza texto-size-16 texto-font"
                                      value="#{contrato.pessoa.nome}"/>
                    </rich:column>
                    <rich:column>
                        <f:facet name="header">
                            <h:outputText styleClass="texto-font texto-size-14-real texto-cor-cinza texto-bold"
                                          value="Contrato"/>
                        </f:facet>
                        <h:outputText styleClass="texto-cor-cinza texto-size-16 texto-font" value="#{contrato.codigo}"/>
                    </rich:column>
                    <rich:column>
                        <f:facet name="header">
                            <h:outputText styleClass="texto-font texto-size-14-real texto-cor-cinza texto-bold"
                                          value="Renovação"/>
                        </f:facet>
                        <h:outputText styleClass="texto-cor-cinza texto-size-16 texto-font"
                                      value="#{contrato.dataPrevistaRenovar_Apresentar}"/>
                    </rich:column>
                </rich:dataTable>
                <rich:datascroller align="center" for="dtClientes" maxPages="10"/>
            </h:panelGrid>

        </a4j:form>
    </rich:modalPanel>


    <rich:modalPanel id="panelPlanoProduto" styleClass="novaModal" autosized="true" shadowOpacity="true" width="550"
                     height="250">
        <f:facet name="header">
            <h:panelGroup>
                <h:outputText value="Consulta do Produto"/>
            </h:panelGroup>
        </f:facet>
        <f:facet name="controls">
            <h:panelGroup>
                <h:outputText
                        styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign btnSelecionarModalidade"
                        id="hidelink1"/>
                <rich:componentControl for="panelPlanoProduto" attachTo="hidelink1" operation="hide" event="onclick"/>
            </h:panelGroup>
        </f:facet>

        <a4j:form id="formPlanoProduto" ajaxSubmit="true">
            <hr style="border-color: #e6e6e6;"/>
            <h:panelGrid columns="1" width="100%" columnClasses="colunaCentralizada">
                <h:panelGrid columns="4" footerClass="colunaCentralizada" width="100%" styleClass="font-size-Em-max">
                    <h:outputText styleClass="texto-font texto-size-14 texto-cor-cinza"
                                  value="#{msg.msg_consultar_por}"/>
                    <h:panelGroup layout="block" styleClass="cb-container">
                        <h:selectOneMenu onblur="blurinput(this);" onfocus="focusinput(this);" styleClass="form"
                                         id="consultaPlanoProduto" value="#{PlanoControle.campoConsultaPlanoProduto}">
                            <f:selectItems value="#{PlanoControle.tipoConsultaComboProduto}"/>
                        </h:selectOneMenu>
                    </h:panelGroup>
                    <h:inputText id="valorConsultaPlanoProduto" size="10" onblur="blurinput(this);"
                                 onfocus="focusinput(this);" styleClass="form"
                                 value="#{PlanoControle.valorConsultaPlanoProduto}"/>
                    <a4j:commandLink id="btnConsultar"
                                     reRender="formPlanoProduto"
                                     action="#{PlanoControle.consultarPlanoProduto}"
                                     styleClass="botaoPrimario texto-size-14-real" value="#{msg_bt.btn_consultar}"
                                     title="#{msg.msg_consultar_dados}"/>
                </h:panelGrid>
                <rich:dataTable id="resultadoConsultaPlanoProduto" width="100%" styleClass="tabelaSimplesCustom"
                                rendered="#{not empty PlanoControle.listaConsultaPlanoProduto}"
                                value="#{PlanoControle.listaConsultaPlanoProduto}" rows="5" var="produto">
                    <rich:column styleClass="col-text-align-left" headerClass="col-text-align-left">
                        <f:facet name="header">
                            <h:outputText styleClass="texto-font texto-size-14-real texto-cor-cinza texto-bold"
                                          value="#{msg_aplic.prt_Cadastro_label_descricao_maiusculo}"/>
                        </f:facet>
                        <h:panelGroup>
                            <a4j:commandLink action="#{PlanoControle.selecionarPlanoProduto}" focus="descricaoProduto"
                                             styleClass="texto-font texto-size-14-real texto-cor-azul linkPadrao"
                                             reRender="form" oncomplete="Richfaces.hideModalPanel('panelPlanoProduto')"
                                             value="#{produto.descricao}"/>
                        </h:panelGroup>
                    </rich:column>
                    <rich:column styleClass="col-text-align-right" headerClass="col-text-align-right">
                        <f:facet name="header">
                            <h:outputText styleClass="texto-font texto-size-14-real texto-cor-cinza texto-bold"
                                          value="#{msg_aplic.prt_Cadastro_label_valor_maiusculo}"/>
                        </f:facet>
                        <h:panelGroup>
                            <h:outputText styleClass="texto-font texto-size-14-real texto-cor-azul linkPadrao"
                                          value="#{produto.valorFinal}">
                                <f:converter converterId="FormatadorNumerico"/>
                            </h:outputText>
                        </h:panelGroup>
                    </rich:column>
                    <rich:column styleClass="col-text-align-center">
                        <a4j:commandLink id="selecionarPlanoProduto"
                                         styleClass="texto-font texto-size-14-real texto-cor-azul linkPadrao"
                                         action="#{PlanoControle.selecionarPlanoProduto}" focus="descricaoProduto"
                                         reRender="form"
                                         oncomplete="Richfaces.hideModalPanel('panelPlanoProduto')"
                                         title="#{msg.msg_selecionar_dados}">
                            <h:outputText styleClass="texto-font texto-size-16-real">Selecionar </h:outputText>
                            <h:outputText styleClass="fa-icon-arrow-right texto-size-16-real"></h:outputText>
                        </a4j:commandLink>
                    </rich:column>
                </rich:dataTable>
                <rich:datascroller align="center" styleClass="scrollPureCustom"
                                   for="formPlanoProduto:resultadoConsultaPlanoProduto" maxPages="10"
                                   renderIfSinglePage="false"
                                   id="scResultadoProduto"/>
                <h:panelGrid id="mensagemConsultaProduto" columns="1" width="100%" styleClass="tabMensagens">
                    <h:panelGrid columns="1" width="100%">
                        <h:outputText styleClass="mensagem" value="#{PlanoControle.mensagem}"/>
                        <h:outputText styleClass="mensagemDetalhada" value="#{PlanoControle.mensagemDetalhada}"/>
                    </h:panelGrid>
                </h:panelGrid>
            </h:panelGrid>
        </a4j:form>
    </rich:modalPanel>

    <rich:modalPanel id="panelDescontoAntecipado" autosized="true" styleClass="novaModal" shadowOpacity="true"
                     width="450" height="250">
        <f:facet name="header">
            <h:panelGroup>
                <h:outputText value="Consulta do Desconto"/>
            </h:panelGroup>
        </f:facet>
        <f:facet name="controls">
            <h:panelGroup>
                <h:outputText
                        styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign btnSelecionarModalidade"
                        id="hidelink2"/>
                <rich:componentControl for="panelDescontoAntecipado" attachTo="hidelink2" operation="hide"
                                       event="onclick"/>
            </h:panelGroup>
        </f:facet>

        <a4j:form id="formDescontoAntecipado" ajaxSubmit="true">
            <h:panelGrid columns="1" width="100%" columnClasses="colunaCentralizada">
                <rich:dataTable id="resultadoConsultaDescontoAntecipado" width="100%" styleClass="tabelaSimplesCustom"
                                value="#{PlanoControle.listaConsultaDescontoAntecipado}" rows="5" var="desconto">
                    <rich:column>
                        <f:facet name="header">
                            <h:outputText styleClass="texto-font texto-size-14-real texto-cor-cinza texto-bold"
                                          value="#{msg_aplic.prt_Desconto_codigo}"/>
                        </f:facet>
                        <h:panelGroup>
                            <a4j:commandLink action="#{PlanoControle.selecionarDescontoAntecipado}" reRender="form"
                                             styleClass="texto-font texto-size-14-real texto-cor-cinza"
                                             oncomplete="Richfaces.hideModalPanel('panelDescontoAntecipado')"
                                             value="#{desconto.codigo}"/>
                        </h:panelGroup>
                    </rich:column>
                    <rich:column>
                        <f:facet name="header">
                            <h:outputText styleClass="texto-font texto-size-14-real texto-cor-cinza texto-bold"
                                          value="#{msg_aplic.prt_Desconto_descricao}"/>
                        </f:facet>
                        <h:panelGroup>
                            <a4j:commandLink action="#{PlanoControle.selecionarDescontoAntecipado}" reRender="form"
                                             styleClass="texto-font texto-size-14-real texto-cor-cinza"
                                             oncomplete="Richfaces.hideModalPanel('panelDescontoAntecipado')"
                                             value="#{desconto.descricao}"/>
                        </h:panelGroup>
                    </rich:column>
                    <rich:column>
                        <f:facet name="header">
                            <h:outputText styleClass="texto-font texto-size-14-real texto-cor-cinza texto-bold"
                                          value="#{msg_bt.btn_opcoes}"/>
                        </f:facet>
                        <a4j:commandLink id="selecionarDescontoAntecipado"
                                         action="#{PlanoControle.selecionarDescontoAntecipado}" reRender="form"
                                         oncomplete="Richfaces.hideModalPanel('panelDescontoAntecipado')"
                                         style="display: inline-flex"
                                         title="#{msg.msg_selecionar_dados}"
                                         styleClass="linkPadrao texto-size-16-real texto-cor-azul">
                            <h:outputText styleClass="texto-font texto-size-16-real">Selecionar </h:outputText>
                            <h:outputText styleClass="fa-icon-arrow-right texto-size-16-real"></h:outputText>
                        </a4j:commandLink>
                    </rich:column>
                </rich:dataTable>
                <rich:datascroller align="center" styleClass="scrollPureCustom"
                                   for="formDescontoAntecipado:resultadoConsultaDescontoAntecipado" maxPages="10"
                                   renderIfSinglePage="false"
                                   id="scResultadoDesconto"/>
                <h:panelGrid id="mensagemConsultaDesconto" columns="1" width="100%" styleClass="tabMensagens">
                    <h:panelGrid columns="1" width="100%">
                        <h:outputText styleClass="mensagem" value="#{PlanoControle.mensagem}"/>
                        <h:outputText styleClass="mensagemDetalhada" value="#{PlanoControle.mensagemDetalhada}"/>
                    </h:panelGrid>
                </h:panelGrid>
            </h:panelGrid>
        </a4j:form>
    </rich:modalPanel>

    <c:set var="titulo" scope="session" value="${msg_aplic.prt_Plano_tituloForm}"/>
    <c:set var="urlWiki" scope="session" value="${SuperControle.urlWiki}Cadastros:PPT:Plano"/>
    <h:panelGrid columns="1" width="100%" cellpadding="0" cellspacing="0">
        <f:facet name="header">
            <jsp:include page="topoReduzido_material.jsp"/>
        </f:facet>

        <h:form id="form" styleClass="form-plano">
            <hr style="border-color: #e6e6e6;"/>
            <h:panelGrid columns="1" width="100%">
                <h:panelGrid columns="2" rowClasses="linhaImpar, linhaPar" columnClasses="classEsquerda, classDireita"
                             width="100%">
                    <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_Plano_codigo}"/>
                    <h:inputText id="codigo" size="10" maxlength="10" readonly="true" onblur="blurinput(this);"
                                 onfocus="focusinput(this);" styleClass="camposSomenteLeitura"
                                 value="#{PlanoControle.planoVO.codigo}"/>

                    <h:outputText rendered="#{PlanoControle.planoVO.usuarioVO.administrador}" styleClass="tituloCampos"
                                  value="#{msg_aplic.prt_Plano_empresa}"/>
                    <h:panelGroup rendered="#{PlanoControle.planoVO.usuarioVO.administrador}">
                        <h:selectOneMenu id="empresa" onblur="blurinput(this);" onfocus="focusinput(this);"
                                         styleClass="form" value="#{PlanoControle.planoVO.empresa.codigo}">
                            <a4j:support event="onchange" reRender="form"
                                         action="#{PlanoControle.adicionarObjDefaultEmpresa}" focus="empresa"/>
                            <f:selectItems value="#{PlanoControle.listaSelectItemEmpresa}"/>
                        </h:selectOneMenu>
                        <a4j:commandButton id="atualizar_empresa" action="#{PlanoControle.montarListaSelectItemEmpresa}"
                                           image="imagens/atualizar.png" immediate="true" ajaxSingle="true"
                                           reRender="form:empresa"/>
                    </h:panelGroup>
                    <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_Plano_descricao}"/>
                    <h:inputText id="descricao" size="50" maxlength="200" onblur="blurinput(this);"
                                 onfocus="focusinput(this);" styleClass="form"
                                 value="#{PlanoControle.planoVO.descricao}"/>

                </h:panelGrid>
                <rich:tabPanel id="painelPlanos" width="100%" switchType="ajax"
                               ontabchange="fireElementFromAnyParent('form:btnAtualizaTempo')"
                               selectedTab="#{PlanoControle.abaSelecionada}">
                    <rich:tab id="dadosPlano" label="Dados Básicos">
                        <h:panelGrid id="panelDadosBasicos" columns="1" width="100%" headerClass="subordinado"
                                     columnClasses="colunaCentralizada">
                            <f:facet name="header">
                                <h:outputText value="#{msg_aplic.prt_DadosBasicos_tituloForm}"/>
                            </f:facet>
                            <h:panelGrid columns="2" rowClasses="linhaImpar, linhaPar"
                                         columnClasses="classEsquerda, classDireita" width="100%">

                                <h:outputText rendered="#{PlanoControle.configuracaoSistema.utilizarTipoPlano}"
                                              styleClass="tituloCampos" value="Tipo:"/>
                                <h:panelGroup rendered="#{PlanoControle.configuracaoSistema.utilizarTipoPlano}">
                                    <h:selectOneMenu styleClass="campos"
                                                     id="selectTipoPlano"
                                                     value="#{PlanoControle.planoVO.planoTipo.codigo}">
                                        <f:selectItems value="#{PlanoControle.planoTipos}"/>
                                    </h:selectOneMenu>
                                    <a4j:commandButton action="#{PlanoControle.montarListaPlanoTipos}"
                                                       image="imagens/atualizar.png"
                                                       immediate="true"
                                                       ajaxSingle="true"
                                                       reRender="form:selectTipoPlano"/>
                                </h:panelGroup>

                                <h:outputText rendered="#{!PlanoControle.planoVO.planoPersonal}"
                                              styleClass="tituloCampos" value="#{msg_aplic.prt_Plano_label_site}"/>
                                <h:panelGroup rendered="#{!PlanoControle.planoVO.planoPersonal}">
                                    <h:selectBooleanCheckbox id="site" styleClass="campos"
                                                             value="#{PlanoControle.planoVO.site}">
                                        <a4j:support event="onclick" reRender="form"/>
                                    </h:selectBooleanCheckbox>
                                </h:panelGroup>
                                <h:outputText rendered="#{PlanoControle.planoVO.site}"
                                              styleClass="tituloCampos" value="#{msg_aplic.prt_Plano_label_permitirVendaPlanoSiteNoBalcao}"/>
                                <h:selectBooleanCheckbox id="permitirVendaPlanoSiteNoBalcao" styleClass="campos" rendered="#{PlanoControle.planoVO.site}"
                                                         value="#{PlanoControle.planoVO.permitirVendaPlanoSiteNoBalcao}"/>

                                <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_plano_label_restringeVendaPorCategoria}"/>
                                <h:selectBooleanCheckbox id="restringeVendaPorCategoria" styleClass="campos"
                                                         value="#{PlanoControle.planoVO.restringeVendaPorCategoria}">
                                    <a4j:support action="#{PlanoControle.atualizaListaPlanosCategoria}" event="onclick" reRender="form, dadosPlanoCategoria, planoCategoriaVO"/>
                                </h:selectBooleanCheckbox>

                                <h:outputText value="Plano personal:"
                                              title="Esta configuração habilita o plano para ser vendido em regime de recorrência para personal. O plano personal recorrente só pode ser vendido através do recurso de Venda Rápida. O usuário precisa ter a permissão \"4.40 - Venda Rápida\" para realizar a venda de Planoum Plano Personal."
                                              rendered="#{!PlanoControle.planoVO.site}"
                                              styleClass="tituloCampos tooltipster"/>
                                <h:panelGroup rendered="#{!PlanoControle.planoVO.site}">
                                    <h:selectBooleanCheckbox styleClass="campos"
                                                             disabled="#{!PlanoControle.planoVO.novoObj and PlanoControle.planoVO.planoPersonal}"
                                                             value="#{PlanoControle.planoVO.planoPersonal}">
                                        <a4j:support event="onchange"
                                                     reRender="form"
                                                     action="#{PlanoControle.alterarPlanoPersonal}"/>
                                    </h:selectBooleanCheckbox>
                                </h:panelGroup>

                                <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_Plano_vigenciaDe}"/>
                                <h:panelGroup>
                                    <rich:calendar id="vigenciaDe"
                                                   value="#{PlanoControle.planoVO.vigenciaDe}"
                                                   inputSize="10"
                                                   inputClass="form"
                                                   oninputblur="blurinput(this);"
                                                   oninputfocus="focusinput(this);"
                                                   oninputchange="return validar_Data(this.id);"
                                                   datePattern="dd/MM/yyyy"
                                                   enableManualInput="true"
                                                   zindex="2"
                                                   showWeeksBar="false"/>
                                    <h:message for="vigenciaDe" styleClass="mensagemDetalhada"/>
                                    <%--h:inputText  id="vigenciaDe" onchange="return mascara(this.form, 'form:vigenciaDe', '99/99/9999', event);" size="10" maxlength="10" onblur="blurinput(this);return validar_Data('form:vigenciaDe');"  onfocus="focusinput(this);" styleClass="form" value="#{PlanoControle.planoVO.vigenciaDe}" >
                                        <f:convertDateTime pattern="dd/MM/yyyy"/>
                                    </h:inputText--%>
                                </h:panelGroup>

                                <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_Plano_vigenciaAte}"/>
                                <h:panelGroup>
                                    <rich:calendar id="vigenciaAte"
                                                   value="#{PlanoControle.planoVO.vigenciaAte}"
                                                   inputSize="10"
                                                   inputClass="form"
                                                   oninputblur="blurinput(this);"
                                                   oninputfocus="focusinput(this);"
                                                   oninputchange="return validar_Data(this.id);"
                                                   datePattern="dd/MM/yyyy"
                                                   enableManualInput="true"
                                                   zindex="2"
                                                   showWeeksBar="false"/>
                                    <h:message for="vigenciaAte" styleClass="mensagemDetalhada"/>
                                    <%--h:inputText  id="vigenciaAte" onchange="return mascara(this.form, 'form:vigenciaAte', '99/99/9999', event);" size="10" maxlength="10" onblur="blurinput(this);return validar_Data('form:vigenciaAte');"  onfocus="focusinput(this);" styleClass="form" value="#{PlanoControle.planoVO.vigenciaAte}" >
                                        <f:convertDateTime pattern="dd/MM/yyyy"/>
                                    </h:inputText--%>
                                </h:panelGroup>

                                <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_Plano_ingressoAte}"/>
                                <h:panelGroup>
                                    <rich:calendar id="ingressoAte"
                                                   value="#{PlanoControle.planoVO.ingressoAte}"
                                                   inputSize="10"
                                                   inputClass="form"
                                                   oninputblur="blurinput(this);"
                                                   oninputfocus="focusinput(this);"
                                                   oninputchange="return validar_Data(this.id);"
                                                   datePattern="dd/MM/yyyy"
                                                   enableManualInput="true"
                                                   zindex="2"
                                                   showWeeksBar="false"/>
                                    <h:message for="ingressoAte" styleClass="mensagemDetalhada"/>
                                    <%--h:inputText  id="ingressoAte" onchange="return mascara(this.form, 'form:ingressoAte', '99/99/9999', event);" size="10" maxlength="10" onblur="blurinput(this);return validar_Data('form:ingressoAte');"  onfocus="focusinput(this);" styleClass="form" value="#{PlanoControle.planoVO.ingressoAte}" >
                                        <f:convertDateTime pattern="dd/MM/yyyy"/>
                                    </h:inputText--%>
                                </h:panelGroup>

                                <h:outputText rendered="#{!PlanoControle.planoVO.planoPersonal}"
                                              styleClass="tituloCampos tooltipster"
                                              title="#{PlanoControle.titlePlanoCreditoTreino}"
                                              value="Venda crédito de treino:"/>
                                <h:panelGroup rendered="#{!PlanoControle.planoVO.planoPersonal}">
                                    <h:selectBooleanCheckbox id="vendaCreditoTreino"
                                                             title="#{PlanoControle.titlePlanoCreditoTreino}"
                                                             styleClass="campos tooltipster"
                                                             rendered="#{!PlanoControle.desabilitarVendaCreditoTreino}"
                                                             value="#{PlanoControle.planoVO.vendaCreditoTreino}">
                                        <a4j:support event="onchange" reRender="form"
                                                     action="#{PlanoControle.selecionouCredito}"/>
                                    </h:selectBooleanCheckbox>
                                    <h:outputText styleClass="tituloCampos tooltipster"
                                                  title="#{PlanoControle.titlePlanoCreditoTreino}"
                                                  rendered="#{PlanoControle.desabilitarVendaCreditoTreino}"
                                                  value="#{PlanoControle.planoVO.vendaCreditoTreino ? 'Sim' : 'Não'}"/>
                                </h:panelGroup>

                                <h:outputText styleClass="tituloCampos tooltipster"
                                              rendered="#{PlanoControle.planoVO.vendaCreditoTreino}"
                                              value="Quantidade de semanas no ano:"/>
                                <h:selectOneRadio rendered="#{PlanoControle.planoVO.vendaCreditoTreino}"
                                                  value="#{PlanoControle.planoVO.qtdSemanasAno}">
                                    <f:selectItem itemValue="48" itemLabel="48"/>
                                    <f:selectItem itemValue="52" itemLabel="52"/>
                                </h:selectOneRadio>

                                <h:outputText rendered="#{PlanoControle.planoVO.vendaCreditoTreino}"
                                              styleClass="tituloCampos tooltipster"
                                              value="Crédito Treino não cumulativo:"/>
                                <h:selectBooleanCheckbox rendered="#{PlanoControle.planoVO.vendaCreditoTreino}"
                                                         id="creditoTreinoNaoCumulativo" styleClass="campos tooltipster"
                                                         value="#{PlanoControle.planoVO.creditoTreinoNaoCumulativo}">
                                    <a4j:support event="onchange" reRender="form"
                                                 action="#{PlanoControle.selecionouCreditoNaoCumulativo}"/>
                                </h:selectBooleanCheckbox>

                                <h:outputText
                                        rendered="#{PlanoControle.planoVO.vendaCreditoTreino and !PlanoControle.planoVO.site}"
                                        styleClass="tituloCampos tooltipster" value="Venda crédito por sessão:"
                                        title="Ao selecionar está opção será possível lançar planos por sessões, onde automaticamente os horários desses planos serão horários da turma."/>
                                <h:selectBooleanCheckbox
                                        rendered="#{PlanoControle.planoVO.vendaCreditoTreino and !PlanoControle.planoVO.site}"
                                        id="creditoSessao" styleClass="campos tooltipster"
                                        title="Ao selecionar está opção será possível lançar planos por sessões, onde automaticamente os horários desses planos serão horários da turma."
                                        value="#{PlanoControle.planoVO.creditoSessao}">
                                    <a4j:support event="onchange" reRender="form"
                                                 action="#{PlanoControle.selecionouCreditoSessao}"/>
                                </h:selectBooleanCheckbox>


                                <h:outputText
                                        rendered="#{!PlanoControle.planoVO.regimeRecorrencia && !PlanoControle.planoVO.site}"
                                        styleClass="tituloCampos" value="#{msg_aplic.prt_Plano_bolsa}"/>
                                <h:selectBooleanCheckbox
                                        rendered="#{!PlanoControle.planoVO.regimeRecorrencia && !PlanoControle.planoVO.site}"
                                        id="bolsa" styleClass="campos" value="#{PlanoControle.planoVO.bolsa}"/>

                                <h:outputText
                                        rendered="#{!PlanoControle.planoVO.regimeRecorrencia && !PlanoControle.planoVO.site}"
                                        styleClass="tituloCampos" value="#{msg_aplic.prt_Plano_permitePagarComBoleto}"/>
                                <h:selectBooleanCheckbox
                                        rendered="#{!PlanoControle.planoVO.regimeRecorrencia && !PlanoControle.planoVO.site}"
                                        id="permitePagarComBoleto" styleClass="campos"
                                        value="#{PlanoControle.planoVO.permitePagarComBoleto}"/>

                                <h:outputText
                                        rendered="#{!PlanoControle.planoVO.regimeRecorrencia && !PlanoControle.planoVO.site}"
                                        styleClass="tituloCampos"
                                        value="Dia do mês para pagamento antecipado para desconto do boleto:"/>
                                <rich:inputNumberSpinner
                                        rendered="#{!PlanoControle.planoVO.regimeRecorrencia && !PlanoControle.planoVO.site}"
                                        maxValue="31" minValue="0"
                                        value="#{PlanoControle.planoVO.diaDoMesDescontoBoletoPagAntecipado}"/>

                                <h:outputText
                                        rendered="#{!PlanoControle.planoVO.regimeRecorrencia && !PlanoControle.planoVO.site}"
                                        styleClass="tituloCampos"
                                        value="Valor do desconto em % para pagamento antecipado do boleto:"/>
                                <rich:inputNumberSpinner
                                        rendered="#{!PlanoControle.planoVO.regimeRecorrencia && !PlanoControle.planoVO.site}"
                                        maxValue="99" minValue="0" styleClass="form"
                                        value="#{PlanoControle.planoVO.porcentagemDescontoBoletoPagAntecipado}"/>

                                <h:outputText
                                        rendered="#{!PlanoControle.planoVO.regimeRecorrencia && !PlanoControle.planoVO.site}"
                                        styleClass="tituloCampos"
                                        value="Valor do desconto em R$ para pagamento antecipado do boleto:"/>
                                <rich:inputNumberSpinner
                                        rendered="#{!PlanoControle.planoVO.regimeRecorrencia && !PlanoControle.planoVO.site}"
                                        maxValue="500" minValue="0" styleClass="form"
                                        value="#{PlanoControle.planoVO.valorDescontoBoletoPagAntecipado}"/>

                                <h:outputText rendered="#{!PlanoControle.planoVO.planoPersonal}"
                                              styleClass="tituloCampos" value="#{msg_aplic.prt_Plano_comissao}"/>
                                <h:selectBooleanCheckbox rendered="#{!PlanoControle.planoVO.planoPersonal}"
                                                         id="comissao" styleClass="campos"
                                                         value="#{PlanoControle.planoVO.comissao}"/>

                                <h:outputText
                                        rendered="#{!PlanoControle.planoVO.regimeRecorrencia && !PlanoControle.planoVO.site}"
                                        styleClass="tituloCampos" value="#{msg_aplic.prt_Plano_descontoAntecipado}"/>
                                <h:panelGroup
                                        rendered="#{!PlanoControle.planoVO.regimeRecorrencia && !PlanoControle.planoVO.site}">
                                    <h:inputText readonly="true" id="desconto" size="40" maxlength="45"
                                                 onblur="blurinput(this);"
                                                 onfocus="focusinput(this);" styleClass="form"
                                                 value="#{PlanoControle.planoVO.descontoAntecipado.descricao}"/>
                                    <a4j:commandButton id="consultaDesconto" alt="Consulta Desconto"
                                                       reRender="formDescontoAntecipado"
                                                       action="#{PlanoControle.consultarDescontoAntecipado}"
                                                       oncomplete="Richfaces.showModalPanel('panelDescontoAntecipado'),
                                                       setFocus(formDescontoAntecipado,'formDescontoAntecipado:valorConsultaDescontoAntecipado');"
                                                       image="./imagens/informacao.gif"/>
                                    <rich:spacer width="5"/>
                                    <a4j:commandButton id="removeDesconto" alt="Remove Desconto"
                                                       action="#{PlanoControle.removerDescontoAntecipado}"
                                                       reRender="desconto"
                                                       image="./imagens/limpar.gif"/>
                                    <h:outputLink value="#{SuperControle.urlWiki}Cadastros:PPT:Plano#Plano"
                                                  title="Clique e saiba mais: Desconto de Renovação Antecipada" target="_blank">
                                        <h:graphicImage styleClass="linkWiki" style="margin-left:5px;"
                                                        url="imagens/wiki_link2.gif"/>
                                    </h:outputLink>
                                </h:panelGroup>

                                <h:outputText styleClass="tituloCampos"
                                              value="#{msg_aplic.prt_Plano_produtoPadraoGerarParcelasContrato}"/>
                                <h:panelGroup>
                                    <h:selectOneMenu id="produtoPadrao" onblur="blurinput(this);"
                                                     onfocus="focusinput(this);" styleClass="form"
                                                     value="#{PlanoControle.planoVO.produtoPadraoGerarParcelasContrato.codigo}">
                                        <f:selectItems value="#{PlanoControle.listaSelectItemProdutoPadrao}"/>
                                    </h:selectOneMenu>
                                    <a4j:commandButton id="atualizar_produtoPadrao"
                                                       action="#{PlanoControle.montarListaSelectItemProdutoPadrao}"
                                                       image="imagens/atualizar.png" immediate="true" ajaxSingle="true"
                                                       reRender="form:produtoPadrao"/>

                                    <%--<h:inputText  id="produtoPadraoGerarParcelasContrato" readonly="true" size="35" maxlength="50" onblur="blurinput(this);"  onfocus="focusinput(this);" styleClass="form" value="#{PlanoControle.planoVO.produtoPadraoGerarParcelasContrato.descricao}" />
                                    <a4j:commandButton id="consultaDadosProduto" focus="valorConsultaProduto" alt="Consulta Produto" reRender="formProduto" oncomplete="Richfaces.showModalPanel('panelProduto'), setFocus(formProduto,'formProduto:valorConsultaProduto');" image="./imagens/informacao.gif" />--%>
                                </h:panelGroup>
                                <h:outputText styleClass="tituloCampos"
                                              value="#{msg_aplic.prt_Plano_planoTextoPadrao}"/>
                                <h:panelGroup>
                                    <h:selectOneMenu id="planoTextoPadrao" onblur="blurinput(this);"
                                                     onfocus="focusinput(this);" styleClass="form"
                                                     value="#{PlanoControle.planoVO.planoTextoPadrao.codigo}">
                                        <f:selectItems value="#{PlanoControle.listaSelectItemPlanoTextoPadrao}"/>
                                    </h:selectOneMenu>
                                    <a4j:commandButton id="atualizar_planoTextoPadrao"
                                                       action="#{PlanoControle.montarListaSelectItemPlanoTextoPadrao}"
                                                       image="imagens/atualizar.png" immediate="true" ajaxSingle="true"
                                                       reRender="form:planoTextoPadrao"/>


                                    <a4j:commandLink
                                            id="textoPadraoContratosLancados"
                                            title="#{msg_aplic.prt_aplicar_texto_padrao_contratos_lancados_explica}"
                                            styleClass="tooltipster"
                                            rendered="#{PlanoControle.planoVO.codigo > 0}"
                                            reRender="mdlAviso, panelMensagem"
                                            style="margin-left: 10px;"
                                            action="#{PlanoControle.alterarTextoPadraoContratosLancados}">
                                        <h:outputText value="#{msg_aplic.prt_aplicar_contratos_lancados}"/>
                                    </a4j:commandLink>

                                </h:panelGroup>

                                <c:if test="${PlanoControle.planoVO.site}">
                                    <h:outputText styleClass="tituloCampos" value="Termo de Aceite"/>
                                    <h:panelGroup>
                                        <h:selectOneMenu id="termoAceite" onblur="blurinput(this);"
                                                         onfocus="focusinput(this);" styleClass="form"
                                                         value="#{PlanoControle.planoVO.termoAceite.codigo}">
                                            <f:selectItems value="#{PlanoControle.listaSelectItemTermoAceite}"/>
                                        </h:selectOneMenu>
                                    </h:panelGroup>
                                </c:if>

                                <h:outputText
                                        rendered="#{(PlanoControle.mostrarCampoRegimeRecorrencia) && (!PlanoControle.planoVO.vendaCreditoTreino)}"
                                        styleClass="tituloCampos"
                                        value="#{msg_aplic.prt_PlanoRecorrencia_regimeRecorrencia}"/>
                                <h:panelGroup
                                        rendered="#{(PlanoControle.mostrarCampoRegimeRecorrencia) && (!PlanoControle.planoVO.vendaCreditoTreino)}">
                                    <h:selectBooleanCheckbox id="regimeRecorrencia"
                                                             disabled="#{!PlanoControle.planoVO.novoObj or PlanoControle.planoVO.planoPersonal}"
                                                             styleClass="campos"
                                                             value="#{PlanoControle.planoVO.regimeRecorrencia}"
                                                             rendered="#{PlanoControle.mostrarCampoRegimeRecorrencia}">
                                        <a4j:support action="#{PlanoControle.verificaAbaASerAbertaDuracaoOuRecorrencia}"
                                                     event="onclick" reRender="form"/>
                                    </h:selectBooleanCheckbox>
                                    <h:outputLink rendered="#{PlanoControle.mostrarCampoRegimeRecorrencia}"
                                                  value="#{SuperControle.urlWiki}Operacional:Recorrência#Cadastro_de_Plano"
                                                  title="Clique e saiba mais: Plano Recorrente" target="_blank">
                                        <i class="fa-icon-question-sign" style="font-size: 18px"></i>
                                    </h:outputLink>
                                </h:panelGroup>

                                <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_Plano_label_totem}"
                                              rendered="#{(PlanoControle.planoVO.regimeRecorrencia or PlanoControle.planoVO.site) and !PlanoControle.planoVO.planoPersonal}"/>
                                <h:panelGroup
                                        rendered="#{(PlanoControle.planoVO.regimeRecorrencia or PlanoControle.planoVO.site) and !PlanoControle.planoVO.planoPersonal}">
                                    <h:selectBooleanCheckbox id="totem" styleClass="campos tooltipster"
                                                             title="#{msg_aplic.prt_Plano_label_totem_hint}"
                                                             value="#{PlanoControle.planoVO.totem}">
                                        <a4j:support event="onclick"
                                                     action="#{PlanoControle.limparDescricaoEncantamentoParaTotem}"
                                                     reRender="panelDadosBasicos"/>
                                    </h:selectBooleanCheckbox>
                                </h:panelGroup>

                                <h:outputText rendered="#{PlanoControle.planoVO.totem}"
                                              styleClass="tituloCampos" value="#{msg_aplic.prt_Plano_label_permitirVendaPlanoTotemNoBalcao}"/>
                                <h:selectBooleanCheckbox id="permitirVendaPlanoTotemNoBalcao" styleClass="campos" rendered="#{PlanoControle.planoVO.totem}"
                                                         value="#{PlanoControle.planoVO.permitirVendaPlanoTotemNoBalcao}"/>

                                <h:outputText styleClass="tituloCampos tooltipster"
                                              rendered="#{PlanoControle.planoVO.totem}"
                                              value="Permitir que o plano seja renovado pelo totem com desconto:"
                                              title="Ao selecionar esta opção, você permite que os planos sejam renovados no totem com os respectivos descontos (caso tenha desconto) que foram dados no ato da compra do plano"/>
                                <h:selectBooleanCheckbox
                                        rendered="#{PlanoControle.planoVO.totem}"
                                        id="renovarComDescontoTotem"
                                        value="#{PlanoControle.planoVO.renovarComDescontoTotem}"
                                        styleClass="tituloCampos tooltipster"
                                        title="Ao selecionar esta opção, você permite que os planos sejam renovados no totem com os respectivos descontos (caso tenha desconto) que foram dados no ato da compra do plano"/>

                                <h:outputText styleClass="tituloCampos" value="Convênio Cobrança Private Label:"
                                              rendered="#{PlanoControle.planoVO.regimeRecorrencia and !PlanoControle.planoVO.planoPersonal}"/>
                                <h:panelGroup
                                        rendered="#{PlanoControle.planoVO.regimeRecorrencia and !PlanoControle.planoVO.planoPersonal}">
                                    <h:selectOneMenu id="convenioCobrancaPrivateLabel" onblur="blurinput(this);"
                                                     onfocus="focusinput(this);"
                                                     styleClass="form"
                                                     value="#{PlanoControle.planoVO.convenioCobrancaPrivateLabel.codigo}">
                                        <f:selectItems
                                                value="#{PlanoControle.listaSelectConvenioCobrancaPrivateLabel}"/>
                                    </h:selectOneMenu>
                                    <a4j:commandButton id="atualizar_convenioCobrancaPrivateLabel"
                                                       action="#{PlanoControle.montarListaSelectItemConvenioPrivateLabel}"
                                                       image="imagens/atualizar.png" immediate="true" ajaxSingle="true"
                                                       reRender="form:convenioCobrancaPrivateLabel"/>
                                </h:panelGroup>

                                <h:outputText rendered="#{PlanoControle.planoVO.totem}" styleClass="tituloCampos"
                                              value="Descrição de Encantamento:"/>
                                <h:inputTextarea styleClass="tooltipster"
                                                 title="Informe uma descrição de encantamento que aparecerá junto à este plano no Autoatendimento (totem)"
                                                 rendered="#{PlanoControle.planoVO.totem}"
                                                 value="#{PlanoControle.planoVO.descricaoEncantamento}"/>

                                <h:outputText rendered="#{PlanoControle.usuarioLogado.administrador}"
                                              styleClass="tituloCampos" value="#{msg_aplic.prt_correspondenciaZD}"/>
                                <h:panelGroup rendered="#{PlanoControle.usuarioLogado.administrador}">

                                    <h:inputText value="#{PlanoControle.planoVO.correspondenciaZD}"/>
                                    <h:outputText rendered="#{PlanoControle.usuarioLogado.administrador}"
                                                  styleClass="tituloCampos"
                                                  value=" (#{msg_aplic.prt_correspondenciaZDInfo})"/>
                                </h:panelGroup>

                                <h:outputText styleClass="tituloCampos" value="Permitir acesso à academia somente:"/>
                                <h:panelGroup>
                                    <h:inputText id="quantidadeMaximaFrequencia"
                                                 size="3" styleClass="tooltipster"
                                                 title="#{PlanoControle.titleQuantidadeMaximaFrequencia}"
                                                 value="#{PlanoControle.planoVO.quantidadeMaximaFrequencia}"/>
                                    <h:outputText styleClass="tituloCampos" style="padding-left: 5px"
                                                  value="dias por semana."/>

                                    <a4j:commandLink
                                            id="frequenciaMaximaContratosLancados"
                                            title="#{msg_aplic.prt_aplicar_quantidade_maxima_frequencia_contratos_lancados_explica}"
                                            styleClass="tooltipster"
                                            rendered="#{PlanoControle.planoVO.codigo > 0}"
                                            reRender="mdlAviso, panelMensagem"
                                            style="margin-left: 10px;"
                                            action="#{PlanoControle.alterarFrequenciaMaximaContratosLancados}">
                                        <h:outputText value="#{msg_aplic.prt_aplicar_contratos_lancados}"/>
                                    </a4j:commandLink>
                                </h:panelGroup>

                                <h:outputText rendered="#{!PlanoControle.planoVO.planoPersonal}"
                                              styleClass="tituloCampos" value="Quantidade de convites por mês:"/>
                                <h:panelGroup rendered="#{!PlanoControle.planoVO.planoPersonal}">
                                    <h:inputText id="convidadosPorMes"
                                                 size="3" styleClass="tooltipster"
                                                 title="Informe a quantidade de convidados que o aluno terá direito a levar na academia. Desabilitado = 0"
                                                 value="#{PlanoControle.planoVO.convidadosPorMes}"/>
                                </h:panelGroup>

                                <c:if test="${!PlanoControle.planoVO.vendaCreditoTreino and !PlanoControle.planoVO.planoPersonal}">
                                    <h:outputText styleClass="tituloCampos"
                                                  value="#{msg_aplic.prt_Plano_label_restigir_marcacoes_aulacoletiva}"/>
                                    <h:panelGroup>
                                        <h:selectBooleanCheckbox id="restricaoMarcacaoAulasColetivas"
                                                                 styleClass="campos"
                                                                 value="#{PlanoControle.planoVO.restringirMarcacaoAulasColetivas}"/>
                                    </h:panelGroup>
                                    <h:outputText styleClass="tituloCampos"
                                                  value="#{msg_aplic.prt_Plano_label_restigir_um_marcacoes_pordia_aulacheia}"/>
                                    <rich:inputNumberSpinner
                                            id="MarcarApenasUmaAula"
                                            maxValue="99" minValue="0"
                                            value="#{PlanoControle.planoVO.restringirQtdMarcacaoPorDia}"/>

                                    <h:outputText styleClass="tituloCampos"
                                                  value="#{msg_aplic.prt_Plano_label_restigir_um_marcacoes_pordia}"/>
                                    <rich:inputNumberSpinner
                                            id="MarcarApenasUmaAulaDiaGeral"
                                            maxValue="99" minValue="0"
                                            value="#{PlanoControle.planoVO.restringirQtdMarcacaoPorDiaGeral}"/>

                                    <h:outputText styleClass="tituloCampos"
                                                  value="#{msg_aplic.prt_Plano_label_restigir_marcacoes_aulacoletiva_validando_mesmo_dia}"/>
                                    <h:panelGroup>
                                        <h:selectBooleanCheckbox id="restricaoMarcacaoAulasColetivasContabilizandoMesmoDia"
                                                                 styleClass="campos"
                                                                 value="#{PlanoControle.planoVO.restringirMarcacaoAulaPorNrVezesModalidade}"/>
                                    </h:panelGroup>
                                </c:if>

                                <h:outputText styleClass="tituloCampos"
                                              value="#{msg_aplic.prt_Plano_apresentaVendaRapida}"
                                              rendered="#{LoginControle.permissaoAcessoMenuVO.vendaRapida
                                              and (!PlanoControle.planoVO.site or PlanoControle.planoVO.permitirVendaPlanoSiteNoBalcao)
                                              and (!PlanoControle.planoVO.totem or PlanoControle.planoVO.permitirVendaPlanoTotemNoBalcao)
                                              and PlanoControle.mostrarAbaRecorrencia}"/>
                                <h:panelGroup rendered="#{LoginControle.permissaoAcessoMenuVO.vendaRapida
                                              and (!PlanoControle.planoVO.site or PlanoControle.planoVO.permitirVendaPlanoSiteNoBalcao)
                                              and (!PlanoControle.planoVO.totem or PlanoControle.planoVO.permitirVendaPlanoTotemNoBalcao)
                                              and PlanoControle.mostrarAbaRecorrencia}">
                                    <h:selectBooleanCheckbox id="apresentaVendaRapida" styleClass="campos"
                                                             value="#{PlanoControle.planoVO.apresentaVendaRapida}">
                                        <a4j:support action="#{PlanoControle.forcarInformarEmpresasDoPlano}"
                                                     oncomplete="#{PlanoControle.mensagemNotificar}"
                                                     event="onclick" reRender="painelPlanos"/>
                                    </h:selectBooleanCheckbox>
                                </h:panelGroup>

                                <h:outputText styleClass="tituloCampos"
                                              value="#{msg_aplic.prt_ConfiguracaoSistema_permiteSituacaoAtestadoContrato}"/>
                                <h:panelGroup>
                                    <h:selectBooleanCheckbox id="permiteSituacaoAtestadoContrato" styleClass="campos"
                                                             value="#{PlanoControle.planoVO.permiteSituacaoAtestadoContrato}">
                                        <a4j:support event="onclick" reRender="painelPlanos"/>
                                    </h:selectBooleanCheckbox>
                                </h:panelGroup>


                                <c:if test="${PlanoControle.integraProtheus}">
                                    <h:outputText rendered="#{PlanoControle.integraProtheus}"
                                                  styleClass="tituloCampos" value="Faturar"/>
                                    <h:panelGroup>
                                        <h:selectBooleanCheckbox rendered="#{PlanoControle.integraProtheus}"
                                                                 value="#{PlanoControle.planoVO.faturar}"/>
                                    </h:panelGroup>
                                </c:if>

                                <h:outputText rendered="#{PlanoControle.permiteAlterarInicioMinimoContrato}"
                                              styleClass="tituloCampos"
                                              value="#{msg_aplic.prt_Plano_inicioMinimoContrato}"
                                              title="Este campo só pode ser alterado caso a data informada aqui seja maior que a data atual.
                                                Ao alterar esta data, será possível aplicar a alteração para os contratos já lançados com a data anterior informada"/>
                                <h:panelGroup rendered="#{PlanoControle.permiteAlterarInicioMinimoContrato}">
                                    <rich:calendar id="inicioMinimoContrato"
                                                   value="#{PlanoControle.planoVO.inicioMinimoContrato}"
                                                   dayStyleClass="inicioMinimoContratoStyleClass"
                                                   isDayEnabled="inicioMinimoContratoDiaPermitido"
                                                   inputSize="10"
                                                   direction="top-left"
                                                   inputClass="form"
                                                   oninputblur="blurinput(this);"
                                                   oninputfocus="focusinput(this);"
                                                   oninputchange="return validar_Data(this.id);"
                                                   datePattern="dd/MM/yyyy"
                                                   enableManualInput="true"
                                                   zindex="2"
                                                   showWeeksBar="false"/>
                                    <a4j:commandButton id="btnAplicarTodosContratos"
                                                       style="margin-left: 5px;"
                                                       value="Alterar para todos os contratos"
                                                       action="#{PlanoControle.consultarContratosQueIniciamAposAmanha}"
                                                       reRender="formContratosIniciamAposInicioMinimo"
                                                       title="Ao aplicar esta ação, todos os contratos já vendidos e que iniciam com data futura, serão alterados.
                                                       O inicio dos contratos existentes e as datas de vencimento de suas parcelas serão alteradas."
                                                       oncomplete="Richfaces.showModalPanel('modalContratosIniciamAposInicioMinimo');#{PlanoControle.mensagemNotificar};"/>
                                    <h:message for="inicioMinimoContrato" styleClass="mensagemDetalhada"/>
                                </h:panelGroup>

                                <h:outputText styleClass="tituloCampos"
                                              value="#{msg_aplic.prt_Plano_parcelamentoOperadora}"/>
                                <h:panelGroup>
                                    <h:selectBooleanCheckbox id="parcelamentoOperadora"
                                                             styleClass="tooltipster"
                                                             title="Informe se o plano sera parcelado pela operadora. <strong>\"VENDAS ONLINE\"</strong>"
                                                             value="#{PlanoControle.planoVO.parcelamentoOperadora}">
                                        <a4j:support event="onclick" reRender="panelDadosBasicos"/>
                                    </h:selectBooleanCheckbox>
                                </h:panelGroup>

                                <h:outputText rendered="#{PlanoControle.planoVO.parcelamentoOperadora}"
                                              styleClass="tituloCampos" value="Número de vezes de acordo com a duração do contrato/plano:"/>
                                <h:panelGroup rendered="#{PlanoControle.planoVO.parcelamentoOperadora}">
                                    <h:selectBooleanCheckbox id="parcelamentoOperadoraDuracao"
                                                             styleClass="tooltipster"
                                                             title="A quantidade máxima de parcelamento será de acordo com a duração do contrato ou do plano"
                                                             value="#{PlanoControle.planoVO.parcelamentoOperadoraDuracao}">
                                        <a4j:support event="onclick" reRender="panelDadosBasicos"/>
                                    </h:selectBooleanCheckbox>
                                </h:panelGroup>


                                <h:outputText rendered="#{PlanoControle.planoVO.parcelamentoOperadora}"
                                              styleClass="tituloCampos" value="Máximo de vezes parcelar:"/>
                                <h:panelGroup rendered="#{PlanoControle.planoVO.parcelamentoOperadora}">
                                    <h:inputText id="maximoVezesParcelar"
                                                 size="3" styleClass="tooltipster"
                                                 title="Informe o máximo de vezes permitido ao realizar uma compra parcelada pela operadora. <br> Se estiver marcado a configuração de 'Número de vezes de acordo com a duração do contrato/plano' e o plano adquirido for menor que essa configuração, o valor limite será a Duração."
                                                 value="#{PlanoControle.planoVO.maximoVezesParcelar}"/>
                                </h:panelGroup>

                                <h:outputText rendered="#{PlanoControle.integranteRedeEmpresa}"
                                              styleClass="tituloCampos" value="Plano VIP:"/>
                                <h:selectBooleanCheckbox id="permitirAcessoRedeEmpresa"
                                                         rendered="#{PlanoControle.integranteRedeEmpresa}"
                                                         styleClass="tooltipster"
                                                         title="Ao negociar este plano, o cliente poderá acessa as demais empresas da rede."
                                                         value="#{PlanoControle.planoVO.permitirAcessoRedeEmpresa}"/>


                                <h:outputText rendered="#{PlanoControle.planoVO.recorrencia and PlanoControle.configuracaoSistema.usaPlanoRecorrenteCompartilhado}"
                                              styleClass="tituloCampos"
                                              value="Quantidade de compartilhamentos do plano:"/>
                                <h:panelGroup rendered="#{PlanoControle.planoVO.recorrencia and PlanoControle.configuracaoSistema.usaPlanoRecorrenteCompartilhado}">
                                    <h:inputText id="quantidadeCompartilhamentos"
                                                 size="3" styleClass="tooltipster"
                                                 title="Informe a quantidade de compartilhamentos que o aluno terá direito ao adquirir este plano. Desabilitado = 0"
                                                 value="#{PlanoControle.planoVO.quantidadeCompartilhamentos}"/>
                                </h:panelGroup>

                            </h:panelGrid>
                        </h:panelGrid>
                    </rich:tab>

                    <rich:tab
                            rendered="#{PlanoControle.planoVO.restringeVendaPorCategoria}"
                            id="dadosPlanoCategoria" label="Categoria">
                        <h:panelGrid id="panelPlanoCategoria" columns="1" width="100%" headerClass="subordinado"
                                     columnClasses="colunaCentralizada">
                            <f:facet name="header">
                                <h:outputText value="#{msg_aplic.prt_PlanoCategoria_tituloForm}"/>
                            </f:facet>
                            <h:panelGrid columns="2" width="100%" rowClasses="linhaImpar, linhaPar"
                                         columnClasses="classEsquerda, classDireita" footerClass="colunaCentralizada">
                                <h:outputText styleClass="tituloCampos"
                                              value="#{msg_aplic.prt_PlanoCategoria_categoria}"/>
                                <h:panelGroup>
                                    <h:selectOneMenu id="planoCategoria" onblur="blurinput(this);"
                                                     onfocus="focusinput(this);" styleClass="form"
                                                     value="#{PlanoControle.planoCategoriaVO.categoria.codigo}">
                                        <f:selectItems value="#{PlanoControle.listaSelectItemPlanoCategoria}"/>
                                    </h:selectOneMenu>
                                    <rich:spacer width="5px"/>
                                    <a4j:commandButton id="atualizarPlanoCategoria"
                                                       action="#{PlanoControle.montarListaSelectItemPlanoCategoria}"
                                                       image="imagens/atualizar.png" immediate="true" ajaxSingle="true"
                                                       reRender="panelPlanoCategoria"/>
                                    <rich:spacer width="5px"/>
                                    <a4j:commandButton id="cadastroNovaCategoria" alt="Cadastrar Categoria"
                                                       onclick="abrirPopup('categoriaCons.jsp', 'Categoria', 800, 595);"
                                                       action="#{CategoriaControle.novoSemRedirect}"
                                                       image="./images/icon_add.gif"/>
                                </h:panelGroup>
                            </h:panelGrid>
                            <a4j:commandButton id="addPlanoCategoria" action="#{PlanoControle.adicionarPlanoCategoria}"
                                               reRender="panelPlanoCategoria, panelMensagem"
                                               focus="form:planoCategoria" value="#{msg_bt.btn_adicionar}"
                                               image="./imagens/botaoAdicionar.png" styleClass="botoes"/>

                            <h:panelGrid columns="1" width="100%" styleClass="tabFormSubordinada">
                                <h:dataTable id="planoCategoriaVO" width="100%" headerClass="subordinado"
                                             styleClass="tabFormSubordinada"
                                             rowClasses="linhaImpar, linhaPar" columnClasses="colunaAlinhamento"
                                             value="#{PlanoControle.planoVO.planoCategoriaVOs}" var="planoCategoria">
                                    <h:column>
                                        <f:facet name="header">
                                            <h:outputText value="#{msg_aplic.prt_PlanoCategoria_categoria}"/>
                                        </f:facet>
                                        <h:outputText value="#{planoCategoria.categoria.codigo} - "/>
                                        <h:outputText value="#{planoCategoria.categoria.nome}"/>
                                    </h:column>
                                    <h:column>
                                        <f:facet name="header">
                                            <h:outputText value="#{msg_bt.btn_opcoes}"/>
                                        </f:facet>
                                        <h:panelGroup>
                                            <a4j:commandButton id="removerPlanoCategoria" reRender="form" ajaxSingle="true"
                                                               immediate="true"
                                                               oncomplete="#{PlanoControle.mensagemNotificar}"
                                                               action="#{PlanoControle.removerPlanoCategoria}"
                                                               value="#{msg_bt.btn_excluir}"
                                                               image="./imagens/botaoRemover.png" accesskey="7"
                                                               styleClass="botoes"/>
                                        </h:panelGroup>
                                    </h:column>
                                </h:dataTable>
                            </h:panelGrid>
                        </h:panelGrid>
                    </rich:tab>

                    <rich:tab
                            rendered="#{(!PlanoControle.planoVO.regimeRecorrencia && !PlanoControle.planoVO.site) &&(!PlanoControle.planoVO.vendaCreditoTreino)}"
                            id="dadosComposicao" label="Pacote">
                        <h:panelGrid id="panelPlanoComposicao" columns="1" width="100%" headerClass="subordinado"
                                     columnClasses="colunaCentralizada">
                            <f:facet name="header">
                                <h:outputText value="#{msg_aplic.prt_PlanoComposicao_tituloForm}"/>
                            </f:facet>
                            <h:panelGrid columns="2" width="100%" rowClasses="linhaImpar, linhaPar"
                                         columnClasses="classEsquerda, classDireita" footerClass="colunaCentralizada">
                                <h:outputText styleClass="tituloCampos"
                                              value="#{msg_aplic.prt_PlanoComposicao_composicao}"/>
                                <h:panelGroup>
                                    <h:selectOneMenu id="PlanoComposicao_composicao" onblur="blurinput(this);"
                                                     onfocus="focusinput(this);" styleClass="form"
                                                     value="#{PlanoControle.planoComposicaoVO.composicao.codigo}">
                                        <f:selectItems value="#{PlanoControle.listaSelectItemComposicao}"/>
                                    </h:selectOneMenu>
                                    <a4j:commandButton id="atualizar_PlanoComposicao_composicao"
                                                       action="#{PlanoControle.montarListaSelectItemComposicao}"
                                                       image="imagens/atualizar.png" immediate="true" ajaxSingle="true"
                                                       reRender="form:PlanoComposicao_composicao"/>
                                    <rich:spacer width="5px"/>
                                    <a4j:commandButton id="cadastroNovoPacote" alt="Cadastrar Pacote"
                                                       onclick="abrirPopup('composicaoCons.jsp', 'Pacote', 800, 595);"
                                                       action="#{ComposicaoControle.novoSemRedirect}"
                                                       image="./images/icon_add.gif"/>
                                </h:panelGroup>
                            </h:panelGrid>
                            <a4j:commandButton id="addPacote" action="#{PlanoControle.adicionarPlanoComposicao}"
                                               reRender="panelPlanoComposicao,panelPlanoModalidade,planoModalidadeVO, panelMensagem"
                                               focus="form:PlanoComposicao_composicao" value="#{msg_bt.btn_adicionar}"
                                               image="./imagens/botaoAdicionar.png" accesskey="7" styleClass="botoes"/>

                            <h:panelGrid columns="1" width="100%" styleClass="tabFormSubordinada">
                                <h:dataTable id="planoComposicaoVO" width="100%" headerClass="subordinado"
                                             styleClass="tabFormSubordinada"
                                             rowClasses="linhaImpar, linhaPar" columnClasses="colunaAlinhamento"
                                             value="#{PlanoControle.planoVO.planoComposicaoVOs}" var="planoComposicao">
                                    <h:column>
                                        <f:facet name="header">
                                            <h:outputText value="#{msg_aplic.prt_PlanoComposicao_composicao}"/>
                                        </f:facet>
                                        <h:outputText value="#{planoComposicao.composicao.codigo} - "/>
                                        <h:outputText value="#{planoComposicao.composicao.descricao}"/>
                                    </h:column>
                                    <h:column>
                                        <f:facet name="header">
                                            <h:outputText value="#{msg_aplic.prt_PlanoComposicao_precoComposicao}"/>
                                        </f:facet>
                                        <h:outputText value="#{planoComposicao.composicao.precoComposicao}">
                                            <f:converter converterId="FormatadorNumerico"/>
                                        </h:outputText>
                                    </h:column>
                                    <h:column>
                                        <f:facet name="header">
                                            <h:outputText value="#{msg_bt.btn_opcoes}"/>
                                        </f:facet>
                                        <h:panelGroup>
                                            <%--  <a4j:commandButton id="editarItemVenda" reRender="form" ajaxSingle="true" immediate="true" action="#{PlanoControle.editarPlanoComposicao}" value="#{msg_bt.btn_editar}" image="./imagens/botaoEditar.png" accesskey="6" styleClass="botoes"/>--%>

                                            <h:outputText value="    "/>

                                            <a4j:commandButton id="removerItemVenda" reRender="form" ajaxSingle="true"
                                                               immediate="true"
                                                               oncomplete="#{PlanoControle.mensagemNotificar}"
                                                               action="#{PlanoControle.removerPlanoComposicao}"
                                                               value="#{msg_bt.btn_excluir}"
                                                               image="./imagens/botaoRemover.png" accesskey="7"
                                                               styleClass="botoes"/>
                                        </h:panelGroup>
                                    </h:column>
                                </h:dataTable>
                            </h:panelGrid>
                        </h:panelGrid>
                    </rich:tab>

                    <rich:tab id="dadosModalidade" label="Modalidades"
                              rendered="#{!PlanoControle.planoVO.planoPersonal}">
                        <h:outputText
                                value="#{'Para os planos SITE, RECORRÊNCIA e CRÉDITO DE TREINO, não é permitido o aluno ou o consultor escolher a modalidade na hora da compra.'}"
                                rendered="#{PlanoControle.planoVO.site or PlanoControle.planoVO.regimeRecorrencia or PlanoControle.planoVO.vendaCreditoTreino}"/>
                        <br>
                        <h:outputText
                                value="#{'Se você adicionar mais de uma modalidade aqui, o aluno terá todas elas incluídas automaticamente caso adquira este plano.'}"
                                rendered="#{PlanoControle.planoVO.site or PlanoControle.planoVO.regimeRecorrencia or PlanoControle.planoVO.vendaCreditoTreino}"/>
                        <h:panelGrid id="panelModalidades" columns="1" width="100%" headerClass="subordinado"
                                     columnClasses="colunaCentralizada">
                            <f:facet name="header">
                                <h:outputText value="#{msg_aplic.prt_Modalidades_tituloForm}"/>
                            </f:facet>
                            <h:panelGrid id="panelPlanoModalidadeGeral" columns="1" width="100%"
                                         headerClass="subordinado" columnClasses="colunaCentralizada">
                                <h:panelGrid id="panelPlanoModalidade" columns="1" width="100%"
                                             headerClass="subordinado" columnClasses="colunaCentralizada">
                                    <h:panelGrid columns="2" width="100%" rowClasses="linhaImpar, linhaPar"
                                                 columnClasses="classEsquerda, classDireita"
                                                 footerClass="colunaCentralizada">
                                        <h:outputText styleClass="tituloCampos"
                                                      value="#{msg_aplic.prt_PlanoModalidade_modalidade}"/>
                                        <h:panelGroup>
                                            <h:selectOneMenu id="PlanoModalidade_modalidade" onblur="blurinput(this);"
                                                             onfocus="focusinput(this);" styleClass="form"
                                                             value="#{PlanoControle.planoModalidadeVO.modalidade.codigo}">
                                                <f:selectItems value="#{PlanoControle.listaSelectItemModalidade}"/>
                                            </h:selectOneMenu>
                                            <a4j:commandButton id="atualizar_PlanoModalidade_modalidade"
                                                               action="#{PlanoControle.montarListaSelectItemModalidade}"
                                                               image="imagens/atualizar.png" immediate="true"
                                                               ajaxSingle="true"
                                                               reRender="form:PlanoModalidade_modalidade"/>
                                            <rich:spacer width="5px;"/>
                                            <a4j:commandButton id="cadastroNovaModalidade" alt="Cadastrar Modalidade"
                                                               onclick="abrirPopup('modalidadeCons.jsp', 'Modalidade', 800, 595);"
                                                               action="#{ModalidadeControle.novoSemRedirect}"
                                                               image="./images/icon_add.gif"/>
                                        </h:panelGroup>
                                    </h:panelGrid>
                                    <a4j:commandButton id="addModalidade"
                                                       action="#{PlanoControle.adicionarPlanoModalidade}"
                                                       reRender="panelPlanoModalidade, panelMensagem"
                                                       focus="form:PlanoModalidade_modalidade"
                                                       value="#{msg_bt.btn_adicionar}"
                                                       image="./imagens/botaoAdicionar.png" accesskey="8"
                                                       styleClass="botoes"/>


                                    <h:panelGrid columns="1" width="100%" styleClass="tabFormSubordinada">
                                        <h:dataTable id="planoModalidadeVO" width="100%" headerClass="subordinado"
                                                     styleClass="tabFormSubordinada"
                                                     rowClasses="linhaImpar, linhaPar"
                                                     columnClasses="colunaAlinhamento, colunaEsquerda, colunaAlinhamento"
                                                     value="#{PlanoControle.planoVO.planoModalidadeVOs}"
                                                     var="planoModalidade">
                                            <h:column>
                                                <f:facet name="header">
                                                    <h:outputText value="#{msg_aplic.prt_PlanoModalidade_modalidade}"/>
                                                </f:facet>
                                                <h:outputText value="#{planoModalidade.modalidade.nome}"/>
                                                <h:outputText rendered="#{!planoModalidade.modalidade.ativo}"
                                                              title="Significa que não vai aparecer na Negociação"
                                                              value=" (inativa)"/>
                                            </h:column>
                                            <h:column
                                                    rendered="#{!PlanoControle.planoVO.vendaCreditoTreino || PlanoControle.planoVO.creditoSessao}">
                                                <f:facet name="header">
                                                    <h:outputText value="#{msg_aplic.prt_Modalidade_nrVezes}"/>
                                                </f:facet>
                                                <h:outputText value="#{planoModalidade.listaVezesSemana}"/>
                                            </h:column>
                                            <h:column>
                                                <f:facet name="header">
                                                    <h:outputText value="#{msg_bt.btn_opcoes}"/>
                                                </f:facet>
                                                <h:panelGroup>
                                                    <a4j:commandButton id="removerItemVenda" reRender="form"
                                                                       ajaxSingle="true" immediate="true"
                                                                       oncomplete="#{PlanoControle.mensagemNotificar}"
                                                                       action="#{PlanoControle.removerPlanoModalidade}"
                                                                       value="#{msg_bt.btn_excluir}"
                                                                       image="./imagens/botaoRemover.png" accesskey="7"
                                                                       styleClass="botoes"/>

                                                    <a4j:commandButton id="numVezesSemana"
                                                                       style="padding-left: 15px;"
                                                                       rendered="#{!PlanoControle.planoVO.vendaCreditoTreino || PlanoControle.planoVO.creditoSessao}"
                                                                       action="#{PlanoControle.selecionarPlanoModalidade}"
                                                                       focus="nrVezesSemana"
                                                                       reRender="panelPlanoModalidadeVezesSemana, planoModalidadeVezesSemanaVO,panelMensagem"
                                                                       alt="Adicionar Vezes Semana"
                                                                       value="#{msg_bt.btn_adicionar}" accesskey="9"
                                                                       image="./imagens/botaoVezesSemana.png"
                                                                       styleClass="botoes"/>
                                                </h:panelGroup>
                                            </h:column>
                                        </h:dataTable>
                                    </h:panelGrid>
                                </h:panelGrid>

                                <h:panelGrid id="panelPlanoModalidadeVezesSemana" width="100%" columns="1">
                                    <h:panelGrid columns="1" width="100%"
                                                 rendered="#{PlanoControle.apresentarPlamoModalidadeVesezSemana}"
                                                 columnClasses="colunaCentralizada">
                                        <h:panelGrid columns="1"
                                                     style="height:25px; background-image:url('./imagens/fundoBarraTopo.png');background-repeat: repeat-x;"
                                                     columnClasses="colunaCentralizada" width="100%">
                                            <h:outputText styleClass="tituloFormulario"
                                                          value="#{msg_aplic.prt_PlanoModalidadeVezesSemana_tituloForm}"/>
                                        </h:panelGrid>
                                        <h:panelGrid id="panelVezesSemana" columns="2" rowClasses="linhaImpar, linhaPar"
                                                     columnClasses="classEsquerda, classDireita" width="100%">
                                            <h:outputText styleClass="tituloCampos"
                                                          value="#{msg_aplic.prt_PlanoModalidade_modalidade}"/>
                                            <h:outputText id="planoModalidade" styleClass="form"
                                                          value="#{PlanoControle.planoModalidadeVOSelecionado.modalidade.nome}"/>
                                            <h:outputText
                                                    rendered="#{empty PlanoControle.planoModalidadeVOSelecionado.modalidade.valorOriginal}"
                                                    styleClass="tituloCampos"
                                                    value="#{msg_aplic.prt_PlanoModalidade_valor}"/>
                                            <h:outputText
                                                    rendered="#{not empty PlanoControle.planoModalidadeVOSelecionado.modalidade.valorOriginal}"
                                                    styleClass="tituloCampos"
                                                    value="#{msg_aplic.prt_PlanoModalidade_valorNoPlano}"/>
                                            <h:panelGroup>
                                                <h:outputText id="planoModalidadeValor"
                                                              styleClass="form"
                                                              value="#{PlanoControle.planoModalidadeVOSelecionado.modalidade.valorMensal_Apresentar}"/>
                                                <h:outputText
                                                        rendered="#{empty PlanoControle.planoModalidadeVOSelecionado.modalidade.valorOriginal}"
                                                        styleClass="form"
                                                        value=" para #{PlanoControle.planoModalidadeVOSelecionado.modalidade.nrVezes}X"/>
                                            </h:panelGroup>
                                            <h:outputText value="#{msg_aplic.prt_PlanoModalidadeVezesSemana_nrVezes}"/>
                                            <h:panelGroup>
                                                <h:inputText id="nrVezesSemana" styleClass="form"
                                                             disabled="#{!PlanoControle.habilitarCampoVezesPorSemana}"
                                                             title="#{PlanoControle.titleEdicaoVezesPorSemana}"
                                                             onkeypress="return Tecla(event);" onblur="blurinput(this);"
                                                             onfocus="focusinput(this);" size="10" maxlength="10"
                                                             value="#{PlanoControle.planoModalidadeVezesSemanaVO.nrVezes}"/>
                                            </h:panelGroup>

                                            <h:outputText
                                                    value="#{msg_aplic.prt_PlanoModalidadeVezesSemana_tipoOperacao}"/>
                                            <h:selectOneMenu id="tipoOperacaoVezesSemana" onblur="blurinput(this);"
                                                             onfocus="focusinput(this);" styleClass="form"
                                                             value="#{PlanoControle.planoModalidadeVezesSemanaVO.tipoOperacao}">
                                                <a4j:support event="onchange"
                                                             action="#{PlanoControle.tratarTipoOperacaoVezesSemana}"
                                                             reRender="panelVezesSemana"/>
                                                <f:selectItems
                                                        value="#{PlanoControle.listaSelectItemTipoOperacaoVezesSemana}"/>
                                            </h:selectOneMenu>

                                            <h:outputText
                                                    rendered="#{PlanoControle.planoModalidadeVezesSemanaVO.tipoOperacao == 'EX'}"
                                                    value="#{msg_aplic.prt_PlanoModalidadeVezesSemana_vezesSemanaDefault}"/>
                                            <h:panelGroup
                                                    rendered="#{PlanoControle.planoModalidadeVezesSemanaVO.tipoOperacao == 'EX'}">
                                                <h:selectBooleanCheckbox id="vezesSemanaDefault" styleClass="campos"
                                                                         value="#{PlanoControle.planoModalidadeVezesSemanaVO.referencia}">
                                                    <rich:toolTip followMouse="true" showDelay="500"
                                                                  styleClass="tooltip">
                                                        <h:outputText
                                                                value="#{msg_aplic.prt_PlanoModalidadeVezesSemana_vezesSemanaDefaultHint}"/>
                                                    </rich:toolTip>
                                                </h:selectBooleanCheckbox>
                                            </h:panelGroup>


                                            <%-- Configurar Valor de Modalidade X Vezes semana com Acrescimo ou Redução de valor ou porcentagem --%>
                                            <h:outputText
                                                    rendered="#{PlanoControle.planoModalidadeVezesSemanaVO.tipoOperacao != 'EX'}"
                                                    value="#{msg_aplic.prt_PlanoModalidadeVezesSemana_tipoValor}"/>
                                            <h:selectOneMenu id="valorTipoVezesSemana" onblur="blurinput(this);"
                                                             rendered="#{PlanoControle.planoModalidadeVezesSemanaVO.tipoOperacao != 'EX'}"
                                                             onfocus="focusinput(this);" styleClass="form"
                                                             value="#{PlanoControle.planoModalidadeVezesSemanaVO.tipoValor}">
                                                <a4j:support event="onchange" focus="valorTipoVezesSemana"
                                                             action="#{PlanoControle.planoModalidadeVezesSemanaVO.desenhaTipoValor}"
                                                             reRender="panelPlanoModalidadeVezesSemana"/>
                                                <f:selectItems value="#{PlanoControle.listaSelectItemTipoValor}"/>
                                            </h:selectOneMenu>

                                            <h:outputText rendered="#{PlanoControle.planoModalidadeVezesSemanaVO.apresentarValorDesconto
                                                                      and PlanoControle.planoModalidadeVezesSemanaVO.tipoOperacao != 'EX'}"
                                                          value="#{msg_aplic.prt_PlanoModalidadeVezesSemana_percentualDesconto}"/>
                                            <h:inputText rendered="#{PlanoControle.planoModalidadeVezesSemanaVO.apresentarValorDesconto
                                                                     and PlanoControle.planoModalidadeVezesSemanaVO.tipoOperacao != 'EX'}"
                                                         id="percentualDescontoVezesSemana" styleClass="form"
                                                         onblur="blurinput(this);" onfocus="focusinput(this);" size="20"
                                                         maxlength="20"
                                                         value="#{PlanoControle.planoModalidadeVezesSemanaVO.percentualDesconto}">
                                                <f:converter converterId="FormatadorNumerico7Casa"/>
                                            </h:inputText>


                                            <h:outputText
                                                    rendered="#{PlanoControle.planoModalidadeVezesSemanaVO.apresentarValorEspecifico}"
                                                    value="#{msg_aplic.prt_PlanoModalidadeVezesSemana_valorEspecifico}"/>
                                            <h:inputText
                                                    rendered="#{PlanoControle.planoModalidadeVezesSemanaVO.apresentarValorEspecifico}"
                                                    id="valorEspecificoVezesSemana" size="20" styleClass="form"
                                                    onblur="blurinput(this);" onfocus="focusinput(this);" maxlength="20"
                                                    value="#{PlanoControle.planoModalidadeVezesSemanaVO.valorEspecifico}">
                                                <f:converter converterId="FormatadorNumerico"/>
                                            </h:inputText>

                                            <script>
                                                VMasker(document.getElementById("form:valorEspecificoVezesSemana")).maskMoney({
                                                    precision: 2,
                                                    separator: ',',
                                                    delimiter: '.',
                                                    zeroCents: false
                                                });
                                            </script>


                                        </h:panelGrid>
                                        <h:panelGrid columns="1" columnClasses="colunaCentralizada" width="100%">
                                            <a4j:commandButton id="addVezesSemanaMod"
                                                               action="#{PlanoControle.adicionarPlanoModalidadeVezesSemana}"
                                                               oncomplete="#{PlanoControle.mensagemNotificar}"
                                                               reRender="panelPlanoModalidadeVezesSemana, panelMensagem"
                                                               focus="nrVezesSemana"
                                                               value="#{msg_bt.btn_adicionar}"
                                                               image="./imagens/botaoAdicionar.png"
                                                               accesskey="8" styleClass="botoes"/>
                                        </h:panelGrid>
                                        <h:panelGrid columns="1" width="100%" styleClass="tabFormSubordinada">
                                            <rich:dataTable id="planoModalidadeVezesSemanaVO" width="100%"
                                                            headerClass="subordinado" styleClass="tabFormSubordinada"
                                                            rowClasses="linhaImpar, linhaPar" rows="7"
                                                            columnClasses="colunaAlinhamento"
                                                            value="#{PlanoControle.planoModalidadeVOSelecionado.planoModalidadeVezesSemanaVOs}"
                                                            var="planoModalidadeVezesSemana">
                                                <rich:column>
                                                    <f:facet name="header">
                                                        <h:outputText
                                                                value="#{msg_aplic.prt_PlanoModalidadeVezesSemana_vezesSemana}"/>
                                                    </f:facet>
                                                    <h:outputText value="#{planoModalidadeVezesSemana.nrVezes}"/>
                                                </rich:column>
                                                <rich:column>
                                                    <f:facet name="header">
                                                        <h:outputText
                                                                value="#{msg_aplic.prt_PlanoModalidadeVezesSemana_vezesSemanaDefault}"/>
                                                    </f:facet>
                                                    <h:outputText
                                                            value="#{planoModalidadeVezesSemana.referenciaApresentar}"/>
                                                </rich:column>
                                                <rich:column>
                                                    <f:facet name="header">
                                                        <h:outputText
                                                                value="#{msg_aplic.prt_PlanoModalidadeVezesSemana_tipoOperacao}"/>
                                                    </f:facet>
                                                    <h:outputText
                                                            value="#{planoModalidadeVezesSemana.tipoOperacao_Apresentar}"/>
                                                </rich:column>

                                                <h:column>
                                                    <f:facet name="header">
                                                        <h:outputText
                                                                value="#{msg_aplic.prt_PlanoModalidadeVezesSemana_percentualDesconto}"/>
                                                    </f:facet>
                                                    <h:outputText
                                                            value="#{planoModalidadeVezesSemana.percentualDesconto}">
                                                        <f:converter converterId="FormatadorNumerico7Casa"/>
                                                    </h:outputText>
                                                </h:column>

                                                <h:column>
                                                    <f:facet name="header">
                                                        <h:outputText
                                                                value="#{msg_aplic.prt_PlanoModalidadeVezesSemana_valorEspecifico}"/>
                                                    </f:facet>
                                                    <h:outputText value="#{planoModalidadeVezesSemana.valorEspecifico}">
                                                        <f:converter converterId="FormatadorNumerico"/>
                                                    </h:outputText>
                                                </h:column>

                                                <rich:column>
                                                    <f:facet name="header">
                                                        <h:outputText value="#{msg_bt.btn_opcoes}"/>
                                                    </f:facet>
                                                    <h:panelGroup>
                                                        <a4j:commandButton id="editarItemVenda"
                                                                           reRender="panelPlanoModalidadeVezesSemana, panelMensagem"
                                                                           ajaxSingle="true"
                                                                           action="#{PlanoControle.editarPlanoModalidadeVezesSemana}"
                                                                           value="#{msg_bt.btn_editar}"
                                                                           image="./imagens/botaoEditar.png"
                                                                           accesskey="6" styleClass="botoes"/>

                                                        <h:outputText value="    "/>

                                                        <a4j:commandButton id="removerItemVenda"
                                                                           reRender="panelPlanoModalidadeVezesSemana, panelMensagem"
                                                                           oncomplete="#{PlanoControle.mensagemNotificar}"
                                                                           ajaxSingle="true"
                                                                           action="#{PlanoControle.removerPlanoModalidadeVezesSemana}"
                                                                           value="#{msg_bt.btn_excluir}"
                                                                           image="./imagens/botaoRemover.png"
                                                                           accesskey="7" styleClass="botoes"/>
                                                    </h:panelGroup>
                                                </rich:column>
                                            </rich:dataTable>
                                        </h:panelGrid>
                                        <h:panelGrid columns="1" width="100%" columnClasses="colunaCentralizada">
                                            <h:panelGroup>
                                                <a4j:commandButton id="salvarVezesSemana"
                                                                   reRender="panelPlanoModalidadeGeral, panelMensagem"
                                                                   action="#{PlanoControle.fechaJanelaPlanoModalidadeVezesSemana}"
                                                                   value="#{msg_bt.btn_gravar}"
                                                                   image="./imagens/botaoGravar.png"
                                                                   alt="#{msg.msg_gravar_dados}" accesskey="2"
                                                                   styleClass="botoes"/>
                                            </h:panelGroup>
                                        </h:panelGrid>
                                    </h:panelGrid>
                                </h:panelGrid>
                            </h:panelGrid>
                            <h:outputText id="avisoRecorrencia"
                                          styleClass="textsmall"
                                          style="color: red;"
                                          rendered="#{PlanoControle.planoVO.regimeRecorrencia}"
                                          value="* Obs.: Para planos do tipo Recorrência o valor de cada modalidade será o resultado do rateio da quantidade de modalidades negociadas pelo \"Valor da Mensalidade\" definidos na aba \"Recorrência\"."/>
                        </h:panelGrid>
                    </rich:tab>


                    <rich:tab id="dadosDuracao" label="Durações"
                              action="#{PlanoControle.montarListaSelectItemComposicaoAdicionada}"
                              rendered="#{PlanoControle.mostrarAbaDuracoes and !PlanoControle.planoVO.planoPersonal}">
                        <h:panelGrid id="panelDuracao" columns="1" width="100%" headerClass="subordinado"
                                     columnClasses="colunaCentralizada">
                            <f:facet name="header">
                                <h:outputText value="#{msg_aplic.prt_PlanoDuracao_tituloForm}"/>
                            </f:facet>
                            <h:panelGrid columns="2" width="100%" rowClasses="linhaImpar, linhaPar"
                                         columnClasses="classEsquerda, classDireita" footerClass="colunaCentralizada">

                                <h:outputText rendered="#{!PlanoControle.planoVO.vendaCreditoTreino}"
                                              styleClass="tituloCampos" value="Escolha um Pacote: "/>
                                <h:panelGroup rendered="#{!PlanoControle.planoVO.vendaCreditoTreino}">
                                    <h:selectOneMenu id="PlanoDuracoes_composicao" onblur="blurinput(this);"
                                                     onfocus="focusinput(this);"
                                                     styleClass="form"
                                                     style="width:200px;"
                                                     value="#{PlanoControle.codigoComposicaoVOValorReferencia}">
                                        <f:selectItems value="#{PlanoControle.listaSelectItemComposicaoAdicionada}"/>
                                        <a4j:support event="onchange" focus="numeroMeses"
                                                     action="#{PlanoControle.alterarValorReferenciaPelaComposicaoVO}"
                                                     reRender="form"/>
                                    </h:selectOneMenu>
                                    <h:panelGroup rendered="#{!PlanoControle.planoVO.vendaCreditoTreino}">
                                        <h:outputText value="ou Valor Mensal de Referência:"
                                                      style="position:relative; left:15px;"/>
                                        <h:inputText id="valorReferencia" size="10" maxlength="10"
                                                     onblur="blurinput(this);" onkeypress="return Tecla(event);"
                                                     onfocus="focusinput(this);" styleClass="form"
                                                     value="#{PlanoControle.valorMensal}"
                                                     style="position:relative; left:15px;">
                                            <f:converter converterId="FormatadorNumerico"/>
                                        </h:inputText>
                                    </h:panelGroup>
                                </h:panelGroup>
                                <h:outputText styleClass="tituloCampos"
                                              value="#{msg_aplic.prt_PlanoDuracao_numeroMeses}"/>
                                <h:panelGroup>
                                    <h:panelGroup>
                                        <h:inputText id="numeroMeses" disabled="#{!PlanoControle.habilitarCampoNrMeses}"
                                                     title="#{PlanoControle.titleEdicaoDuracao}" size="10"
                                                     maxlength="10" onblur="blurinput(this);"
                                                     onfocus="focusinput(this);" styleClass="form"
                                                     value="#{PlanoControle.planoDuracaoVO.numeroMeses}">
                                            <a4j:support event="onchange" reRender="outTotalDias"></a4j:support>
                                        </h:inputText>
                                    </h:panelGroup>

                                    <h:outputText styleClass="tituloCampos" value=" + "
                                                  rendered="#{!PlanoControle.planoVO.creditoSessao}"/>
                                    <h:inputText size="3" maxlength="3" onblur="blurinput(this);"
                                                 onfocus="focusinput(this);"
                                                 rendered="#{!PlanoControle.planoVO.creditoSessao}"
                                                 id="idDiasExtra"
                                                 styleClass="form"
                                                 value="#{PlanoControle.planoDuracaoVO.quantidadeDiasExtra}">
                                        <a4j:support event="onchange" reRender="outTotalDias"></a4j:support>
                                    </h:inputText>

                                    <h:outputText styleClass="tituloCampos" value=" dias extra ="
                                                  rendered="#{!PlanoControle.planoVO.creditoSessao}"/>

                                    <h:outputText id="outTotalDias" styleClass="tituloCampos"
                                                  value="#{PlanoControle.planoDuracaoVO.totalDias} dias "
                                                  style="padding-left: 5px"/>

                                </h:panelGroup>

                                <h:outputText styleClass="tituloCampos"
                                              value="#{msg_aplic.prt_PlanoDuracao_nrMaximoParcelasCondPagamento}"/>
                                <h:panelGroup>
                                    <h:inputText size="10" maxlength="10" onblur="blurinput(this);"
                                                 onfocus="focusinput(this);" styleClass="form"
                                                 value="#{PlanoControle.planoDuracaoVO.nrMaximoParcelasCondPagamento}"
                                                 id="numeroMaxParcelaasCondicPag"/>
                                    <h:panelGroup rendered="#{!PlanoControle.planoVO.vendaCreditoTreino}">
                                        <h:outputText value="#{msg_aplic.prt_PlanoDuracao_valorDesejadoMensal}"
                                                      style="position:relative; left:65px;"/>
                                        <h:inputText id="valorDesejadoMensal" size="10" maxlength="10"
                                                     onblur="blurinput(this);" onkeypress="return Tecla(event);"
                                                     onfocus="focusinput(this);" styleClass="form"
                                                     value="#{PlanoControle.planoDuracaoVO.valorDesejadoMensal}"
                                                     style="position:relative; left:106px;">
                                            <f:converter converterId="FormatadorNumerico"/>
                                        </h:inputText>
                                        <a4j:commandButton id="calcularValorMensalDuracao"
                                                           value="Calcular usando o Valor Desejado Mensal"
                                                           action="#{PlanoControle.calcularDuracaoMensal}"
                                                           reRender="panelDuracao,panelMensagem"
                                                           style="position:relative; left:126px;"
                                                           image="./images/icon_calculadora.png"/>
                                    </h:panelGroup>

                                </h:panelGroup>

                                <h:outputText styleClass="tituloCampos"
                                              rendered="#{!PlanoControle.planoVO.vendaCreditoTreino}"
                                              value="#{msg_aplic.prt_PlanoDuracao_formaDesconto}"/>
                                <h:panelGroup rendered="#{!PlanoControle.planoVO.vendaCreditoTreino}">
                                    <h:selectOneMenu id="tipoValorDuracao" onblur="blurinput(this);"
                                                     onfocus="focusinput(this);" styleClass="form"
                                                     value="#{PlanoControle.planoDuracaoVO.tipoValor}">
                                        <f:selectItems value="#{PlanoControle.listaSelectItemTipoValor}"/>
                                        <a4j:support event="onchange"
                                                     action="#{PlanoControle.planoDuracaoVO.desenhaTipoValor}"
                                                     reRender="form"/>
                                    </h:selectOneMenu>

                                    <h:panelGroup>
                                        <h:outputText value="#{msg_aplic.prt_PlanoDuracao_valorDesejadoParcela}"
                                                      style="position:relative; left:65px;"/>
                                        <h:inputText id="valorDesejadoParcela" size="10" maxlength="10"
                                                     onblur="blurinput(this);" onkeypress="return Tecla(event);"
                                                     onfocus="focusinput(this);" styleClass="form"
                                                     value="#{PlanoControle.planoDuracaoVO.valorDesejadoParcela}"
                                                     style="position:relative; left:87px;">
                                            <f:converter converterId="FormatadorNumerico"/>
                                        </h:inputText>
                                        <a4j:commandButton title="Calcular usando o Valor Desejado da Parcela"
                                                           action="#{PlanoControle.calcularDuracaoParcela}"
                                                           reRender="panelDuracao,panelMensagem"
                                                           style="position:relative; left:107px;"
                                                           image="./images/icon_calculadora.png"/>
                                    </h:panelGroup>

                                </h:panelGroup>

                                <h:outputText styleClass="tituloCampos"
                                              rendered="#{!PlanoControle.planoVO.vendaCreditoTreino}"
                                              value="#{msg_aplic.prt_PlanoDuracao_tipoOperacao}"/>
                                <h:panelGroup rendered="#{!PlanoControle.planoVO.vendaCreditoTreino}">
                                    <h:selectOneMenu id="tipoOperacaoDuracao" onblur="blurinput(this);"
                                                     onfocus="focusinput(this);" styleClass="form"
                                                     value="#{PlanoControle.planoDuracaoVO.tipoOperacao}">
                                        <f:selectItems value="#{PlanoControle.listaSelectItemTipoOperacao}"/>
                                    </h:selectOneMenu>
                                    <h:outputText value="#{msg_aplic.prt_PlanoDuracao_valorDesejadoAjuste}"
                                                  style="position:relative; left:65px;"/>
                                    <rich:spacer width="14px"/>
                                    <h:inputText id="valorDesejado" size="10" maxlength="10" onblur="blurinput(this);"
                                                 onkeypress="return Tecla(event);" onfocus="focusinput(this);"
                                                 styleClass="form" value="#{PlanoControle.planoDuracaoVO.valorDesejado}"
                                                 style="position:relative; left:80px;">
                                        <f:converter converterId="FormatadorNumerico7Casa"/>
                                    </h:inputText>
                                    <a4j:commandButton value="Calcular usando o Valor Desejado de Ajuste"
                                                       action="#{PlanoControle.calcularDuracao}"
                                                       reRender="panelDuracao,panelMensagem"
                                                       style="position:relative; left:100px;"
                                                       image="./images/icon_calculadora.png"/>

                                </h:panelGroup>
                                <h:outputText styleClass="tituloCampos" value="Ativa: "/>
                                <h:panelGroup>
                                    <h:selectBooleanCheckbox id="situacaoDuracao"
                                                             title="Ao inativar, a duração não vai mais estar disponível para novas negociações, exceto para renovações automáticas"
                                                             styleClass="campos"
                                                             value="#{PlanoControle.planoDuracaoVO.situacao}"/>
                                </h:panelGroup>
                            </h:panelGrid>
                            <a4j:commandButton id="addPlanoDuracao" action="#{PlanoControle.adicionarPlanoDuracao}"
                                               reRender="panelDuracao,panelGridParcelarMatriculaProdutoNormal, panelMensagem"
                                               focus="form:numeroMeses" value="#{msg_bt.btn_adicionar}"
                                               image="./imagens/botaoAdicionar.png" accesskey="5" styleClass="botoes"/>

                            <h:panelGrid columns="1" width="100%" styleClass="tabFormSubordinada">
                                <h:dataTable id="planoDuracaoVO" width="100%" headerClass="subordinado"
                                             styleClass="tabFormSubordinada"
                                             rowClasses="linhaImpar, linhaPar" columnClasses="colunaAlinhamento"
                                             value="#{PlanoControle.planoVO.planoDuracaoVOs}" var="planoDuracao">
                                    <h:column>
                                        <f:facet name="header">
                                            <h:outputText value="#{msg_aplic.prt_PlanoDuracao_numeroMeses_red}"/>
                                        </f:facet>
                                        <h:outputText value="#{planoDuracao.numeroMeses}"/>
                                    </h:column>
                                    <h:column rendered="#{PlanoControle.mostrarColunaTotalDias}">
                                        <f:facet name="header">
                                            <h:outputText value="Total Dias"/>
                                        </f:facet>
                                        <h:outputText value="#{planoDuracao.totalDias}"/>
                                    </h:column>
                                    <h:column>
                                        <f:facet name="header">
                                            <h:outputText value="#{msg_aplic.prt_PlanoDuracao_situacao_red}"/>
                                        </f:facet>
                                        <h:outputText value="#{planoDuracao.situacao_Apresentar}"/>
                                    </h:column>
                                    <h:column>
                                        <f:facet name="header">
                                            <h:outputText
                                                    value="#{msg_aplic.prt_PlanoDuracao_nrMaximoParcelasCondPagamento_red}"/>
                                        </f:facet>
                                        <h:outputText value="#{planoDuracao.nrMaximoParcelasCondPagamento}"/>
                                    </h:column>
                                    <h:column rendered="#{!PlanoControle.planoVO.vendaCreditoTreino}">
                                        <f:facet name="header">
                                            <h:outputText value="#{msg_aplic.prt_PlanoDuracao_tipoOperacao_red}"/>
                                        </f:facet>
                                        <h:outputText value="#{planoDuracao.tipoOperacao_Apresentar}"/>
                                    </h:column>
                                    <h:column rendered="#{!PlanoControle.planoVO.vendaCreditoTreino}">
                                        <f:facet name="header">
                                            <h:outputText value="#{msg_aplic.prt_PlanoDuracao_valorEspecifico_red}"/>
                                        </f:facet>
                                        <h:outputText value="#{planoDuracao.valorEspecifico}">
                                            <f:converter converterId="FormatadorNumerico"/>
                                        </h:outputText>
                                    </h:column>
                                    <h:column rendered="#{!PlanoControle.planoVO.vendaCreditoTreino}">
                                        <f:facet name="header">
                                            <h:outputText value="#{msg_aplic.prt_PlanoDuracao_percentualDesconto_red}"/>
                                        </f:facet>
                                        <h:outputText value="#{planoDuracao.percentualDesconto}">
                                            <f:converter converterId="FormatadorNumerico7Casa"/>
                                        </h:outputText>
                                    </h:column>
                                    <h:column rendered="#{!PlanoControle.planoVO.vendaCreditoTreino}">
                                        <f:facet name="header">
                                            <h:outputText value="#{msg_aplic.prt_PlanoDuracao_valorTotal}"/>
                                        </f:facet>
                                        <h:outputText value="#{planoDuracao.valorTotal}">
                                            <f:converter converterId="FormatadorNumerico"/>
                                        </h:outputText>
                                    </h:column>
                                    <h:column>
                                        <f:facet name="header">
                                            <h:outputText value="#{msg_bt.btn_opcoes}"/>
                                        </f:facet>
                                        <h:panelGroup>
                                            <a4j:commandButton id="editarItemVenda" reRender="form" ajaxSingle="true"
                                                               immediate="true"
                                                               action="#{PlanoControle.editarPlanoDuracao}"
                                                               value="#{msg_bt.btn_editar}"
                                                               image="./imagens/botaoEditar.png"
                                                               style="vertical-align: middle"
                                                               accesskey="6" styleClass="botoes"/>

                                            <h:outputText value="    "/>

                                            <a4j:commandButton id="removerItemVenda" reRender="form"
                                                               ajaxSingle="true" immediate="true"
                                                               action="#{PlanoControle.removerPlanoDuracao}"
                                                               oncomplete="#{PlanoControle.mensagemNotificar}"
                                                               style="vertical-align: middle"
                                                               value="#{msg_bt.btn_excluir}"
                                                               image="./imagens/botaoRemover.png" accesskey="7"
                                                               styleClass="botoes"/>

                                            <h:outputText value="    "/>

                                            <a4j:commandButton id="linkCondicaoPG"
                                                               action="#{PlanoControle.selecionarPlanoDuracao}"
                                                               value="Condição PG"
                                                               focus="PlanoCondicaoPagamento_condicaoPagamento"
                                                               reRender="panelPlanoCondicaoPagamentoGeral, planoCondicaoPagamentoVO,panelMensagem"
                                                               style="vertical-align: middle"
                                                               alt="Adicionar Condição Pagamento"
                                                               styleClass="botoes nvoBt btSec"
                                                               accesskey="9">
                                                <f:setPropertyActionListener value="#{planoDuracao}"
                                                                             target="#{PlanoControle.planoDuracaoVOSelecionado}"/>
                                            </a4j:commandButton>
                                            <h:panelGroup>
                                                <a4j:commandButton id="btnConfCreditoTreino"
                                                                   action="#{PlanoControle.configurarDuracaoCreditoTreino}"
                                                                   rendered="#{PlanoControle.planoVO.vendaCreditoTreino && !PlanoControle.planoVO.creditoSessao}"
                                                                   value="#{ClienteControle.configNomenclaturaVendaCredito}"
                                                                   focus="PlanoCondicaoPagamento_condicaoPagamento"
                                                                   reRender="panelConfiguracaoCreditoTreino, panelMensagem"
                                                                   alt="Adicionar nova configuração crédito de treino."
                                                                   styleClass="botoes nvoBt btSec"
                                                                   style="vertical-align: middle"
                                                                   accesskey="9">
                                                    <f:setPropertyActionListener value="#{planoDuracao}"
                                                                                 target="#{PlanoControle.planoDuracaoVOSelecionado}"/>
                                                </a4j:commandButton>

                                            </h:panelGroup>


                                        </h:panelGroup>
                                    </h:column>
                                </h:dataTable>
                            </h:panelGrid>
                        </h:panelGrid>

                        <h:panelGrid id="panelPlanoCondicaoPagamentoGeral" columns="1" width="100%"
                                     headerClass="subordinado" columnClasses="colunaCentralizada">
                            <h:panelGrid id="panelPlanoCondicaoPagamento"
                                         rendered="#{PlanoControle.apresentarPlamoCondicaoPagamento}" columns="1"
                                         width="100%" headerClass="subordinado" columnClasses="colunaCentralizada">
                                <h:panelGrid columns="1"
                                             style="height:25px; background-image:url('./imagens/fundoBarraTopo.png');background-repeat: repeat-x;"
                                             columnClasses="colunaCentralizada" width="100%">
                                    <h:outputText styleClass="tituloFormulario"
                                                  value="#{msg_aplic.prt_PlanoCondicaoPagamento_tituloForm}"/>
                                </h:panelGrid>
                                <h:panelGrid columns="2" width="100%" rowClasses="linhaImpar, linhaPar"
                                             columnClasses="classEsquerda, classDireita"
                                             footerClass="colunaCentralizada">

                                    <h:outputText value="#{msg_aplic.prt_PlanoDuracao_numeroMeses}"/>
                                    <h:inputText id="numeroMesesDuracao" size="10" maxlength="10" readonly="true"
                                                 onblur="blurinput(this);" onfocus="focusinput(this);" styleClass="form"
                                                 value="#{PlanoControle.planoDuracaoVOSelecionado.numeroMeses}"/>

                                    <h:outputText styleClass="tituloCampos"
                                                  value="#{msg_aplic.prt_PlanoCondicaoPagamento_condicaoPagamento}"/>
                                    <h:panelGroup>
                                        <h:selectOneMenu disabled="#{!PlanoControle.habilitarCampoCondicaoPagamento}"
                                                         title="#{PlanoControle.titleEdicaoCondicaoPagamento}"
                                                         id="PlanoCondicaoPagamento_condicaoPagamento"
                                                         onblur="blurinput(this);" onfocus="focusinput(this);"
                                                         styleClass="form"
                                                         value="#{PlanoControle.planoCondicaoPagamentoVO.condicaoPagamento.codigo}">
                                            <f:selectItems value="#{PlanoControle.listaSelectItemCondicaoPagamento}"/>
                                        </h:selectOneMenu>
                                        <a4j:commandButton id="atualizar_PlanoCondicaoPagamento_condicaoPagamento"
                                                           action="#{PlanoControle.montarListaSelectItemCondicaoPagamento}"
                                                           image="imagens/atualizar.png" immediate="true"
                                                           ajaxSingle="true"
                                                           reRender="form:PlanoCondicaoPagamento_condicaoPagamento"/>
                                    </h:panelGroup>

                                    <h:outputText styleClass="tituloCampos"
                                                  value="#{msg_aplic.prt_PlanoCondicaoPagamento_tipoOperacao}"/>
                                    <h:selectOneMenu id="tipoOperacaoCondicaoPagamento" onblur="blurinput(this);"
                                                     onfocus="focusinput(this);" styleClass="form"
                                                     value="#{PlanoControle.planoCondicaoPagamentoVO.tipoOperacao}">
                                        <f:selectItems value="#{PlanoControle.listaSelectItemTipoOperacao}"/>
                                    </h:selectOneMenu>

                                    <h:outputText styleClass="tituloCampos"
                                                  value="#{msg_aplic.prt_PlanoCondicaoPagamento_formaDesconto}"/>
                                    <h:selectOneMenu id="valorTipoCondicaoPagamento" onblur="blurinput(this);"
                                                     onfocus="focusinput(this);" styleClass="form"
                                                     value="#{PlanoControle.planoCondicaoPagamentoVO.tipoValor}">
                                        <a4j:support id="atualizar_PlanoCondicaoPagamento_formaDesconto"
                                                     event="onchange" focus="valorTipoCondicaoPagamento"
                                                     action="#{PlanoControle.planoCondicaoPagamentoVO.desenhaTipoValor}"
                                                     reRender="panelPlanoCondicaoPagamento"/>
                                        <f:selectItems value="#{PlanoControle.listaSelectItemTipoValor}"/>
                                    </h:selectOneMenu>

                                    <h:outputText styleClass="tituloCampos"
                                                  rendered="#{PlanoControle.planoCondicaoPagamentoVO.apresentarValorDesconto}"
                                                  value="#{msg_aplic.prt_PlanoCondicaoPagamento_percentualDesconto}"/>
                                    <h:inputText
                                            rendered="#{PlanoControle.planoCondicaoPagamentoVO.apresentarValorDesconto}"
                                            id="percentualDescontoPlanoCondicaoPagamento"
                                            onkeypress="return Tecla(event);" size="20" maxlength="20"
                                            onblur="blurinput(this);" onfocus="focusinput(this);" styleClass="form"
                                            value="#{PlanoControle.planoCondicaoPagamentoVO.percentualDesconto}">
                                        <f:converter converterId="FormatadorNumerico7Casa"/>
                                    </h:inputText>


                                    <h:outputText styleClass="tituloCampos"
                                                  rendered="#{PlanoControle.planoCondicaoPagamentoVO.apresentarValorEspecifico}"
                                                  value="#{msg_aplic.prt_PlanoCondicaoPagamento_valorEspecifico}"/>
                                    <h:inputText
                                            rendered="#{PlanoControle.planoCondicaoPagamentoVO.apresentarValorEspecifico}"
                                            id="valorEspecificoPlanoCondicaoPagamento" onkeypress="return Tecla(event);"
                                            size="20" maxlength="20" onblur="blurinput(this);"
                                            onfocus="focusinput(this);" styleClass="form"
                                            value="#{PlanoControle.planoCondicaoPagamentoVO.valorEspecifico}">
                                        <f:converter converterId="FormatadorNumerico"/>
                                    </h:inputText>


                                </h:panelGrid>
                                <a4j:commandButton id="addCondicaoPG"
                                                   action="#{PlanoControle.adicionarPlanoCondicaoPagamento}"
                                                   reRender="panelPlanoCondicaoPagamento,panelGridParcelarMatriculaProdutoNormal, panelMensagem"
                                                   focus="form:PlanoCondicaoPagamento_condicaoPagamento"
                                                   value="#{msg_bt.btn_adicionar}" image="./imagens/botaoAdicionar.png"
                                                   accesskey="6" styleClass="botoes"/>

                                <h:panelGrid columns="1" width="100%" styleClass="tabFormSubordinada">
                                    <h:dataTable id="planoCondicaoPagamentoVO" width="100%" headerClass="subordinado"
                                                 styleClass="tabFormSubordinada"
                                                 rowClasses="linhaImpar, linhaPar" columnClasses="colunaAlinhamento"
                                                 value="#{PlanoControle.planoDuracaoVOSelecionado.planoCondicaoPagamentoVOs}"
                                                 var="planoCondicaoPagamento"><%--rows="5" (com datascroll)--%>
                                        <h:column>
                                            <f:facet name="header">
                                                <h:outputText
                                                        value="#{msg_aplic.prt_PlanoCondicaoPagamento_condicaoPagamento}"/>
                                            </f:facet>
                                            <h:outputText
                                                    value="#{planoCondicaoPagamento.condicaoPagamento.descricao}"/>
                                        </h:column>
                                        <h:column>
                                            <f:facet name="header">
                                                <h:outputText
                                                        value="#{msg_aplic.prt_PlanoCondicaoPagamento_tipoOperacao}"/>
                                            </f:facet>
                                            <h:outputText value="#{planoCondicaoPagamento.tipoOperacao_Apresentar}"/>
                                        </h:column>
                                        <h:column>
                                            <f:facet name="header">
                                                <h:outputText
                                                        value="#{msg_aplic.prt_PlanoCondicaoPagamento_valorEspecifico}"/>
                                            </f:facet>
                                            <h:outputText value="#{planoCondicaoPagamento.valorEspecifico}">
                                                <f:converter converterId="FormatadorNumerico"/>
                                            </h:outputText>
                                        </h:column>
                                        <h:column>
                                            <f:facet name="header">
                                                <h:outputText
                                                        value="#{msg_aplic.prt_PlanoCondicaoPagamento_percentualDesconto}"/>
                                            </f:facet>
                                            <h:outputText value="#{planoCondicaoPagamento.percentualDesconto}">
                                                <f:converter converterId="FormatadorNumerico7Casa"/>
                                            </h:outputText>
                                        </h:column>
                                        <h:column>
                                            <f:facet name="header">
                                                <h:outputText value="#{msg_bt.btn_opcoes}"/>
                                            </f:facet>
                                            <h:panelGroup>
                                                <a4j:commandButton id="editarItemVenda"
                                                                   reRender="panelPlanoCondicaoPagamento, panelMensagem"
                                                                   ajaxSingle="true" immediate="true"
                                                                   action="#{PlanoControle.editarPlanoCondicaoPagamento}"
                                                                   value="#{msg_bt.btn_editar}"
                                                                   image="./imagens/botaoEditar.png" accesskey="6"
                                                                   styleClass="botoes"/>

                                                <h:outputText value="    "/>

                                                <a4j:commandButton id="removerItemVenda"
                                                                   reRender="panelPlanoCondicaoPagamento, panelMensagem,panelGridParcelarMatriculaProdutoNormal"
                                                                   ajaxSingle="true" immediate="true"
                                                                   oncomplete="#{PlanoControle.mensagemNotificar}"
                                                                   action="#{PlanoControle.removerPlanoCondicaoPagamento}"
                                                                   value="#{msg_bt.btn_excluir}"
                                                                   image="./imagens/botaoRemover.png" accesskey="7"
                                                                   styleClass="botoes"/>
                                            </h:panelGroup>
                                        </h:column>
                                    </h:dataTable>
                                    <%--<rich:datascroller align="center" for="form:planoCondicaoPagamentoVO" maxPages="10" id="scplanoCondicaoPagamentoVO" />--%>
                                </h:panelGrid>
                                <a4j:commandButton id="salvarCondicaoPagamento"
                                                   reRender="panelPlanoCondicaoPagamentoGeral, panelMensagem"
                                                   action="#{PlanoControle.fechaJanelaPlanoDuracao}"
                                                   value="#{msg_bt.btn_gravar}" image="./imagens/botaoGravar.png"
                                                   alt="#{msg.msg_gravar_dados}" accesskey="2" styleClass="botoes"/>
                            </h:panelGrid>
                        </h:panelGrid>

                        <h:panelGrid id="panelConfiguracaoCreditoTreino" columns="1" width="100%"
                                     headerClass="subordinado" columnClasses="colunaCentralizada">
                            <h:panelGrid id="panelConfCreditoTreino"
                                         rendered="#{PlanoControle.mostrarConfiguracaoDuracaoCreditoTreino}" columns="1"
                                         width="100%" headerClass="subordinado" columnClasses="colunaCentralizada">
                                <h:panelGrid columns="1"
                                             style="height:25px; background-image:url('./imagens/fundoBarraTopo.png');background-repeat: repeat-x;"
                                             columnClasses="colunaCentralizada" width="100%">
                                    <h:outputText styleClass="tituloFormulario"
                                                  value="Configuração créditos de treino"/>
                                </h:panelGrid>
                                <h:panelGrid columns="2" width="100%" rowClasses="linhaImpar, linhaPar"
                                             columnClasses="classEsquerda, classDireita"
                                             footerClass="colunaCentralizada">


                                    <h:outputText styleClass="tituloCampos" value="Tipo Horário:"/>
                                    <h:panelGroup>
                                        <h:selectOneMenu id="tipoHorCredTreino" onblur="blurinput(this);"
                                                         onfocus="focusinput(this);" styleClass="form"
                                                         value="#{PlanoControle.planoDuracaoCreditoTreinoVO.tipoHorarioCreditoTreino}">
                                            <a4j:support event="onchange"
                                                         action="#{PlanoControle.verificarHorarioCreditoTreinoSelecionado}"
                                                         reRender="form"></a4j:support>
                                            <f:selectItems value="#{PlanoControle.listaTipoHorarioCreditoTreino}"/>
                                        </h:selectOneMenu>
                                    </h:panelGroup>

                                    <c:if test="${PlanoControle.planoDuracaoCreditoTreinoVO.tipoHorarioCreditoTreinoEnum eq 'HORARIO_TURMA'}">
                                        <h:outputText styleClass="tituloCampos" value="Vezes semana:"/>
                                    </c:if>

                                    <c:if test="${PlanoControle.planoDuracaoCreditoTreinoVO.tipoHorarioCreditoTreinoEnum eq 'HORARIO_TURMA'}">
                                        <h:inputText size="10" maxlength="2" onblur="blurinput(this);"
                                                     id="idVezesSemanaConfCred"
                                                     onfocus="focusinput(this);" styleClass="form"
                                                     value="#{PlanoControle.planoDuracaoCreditoTreinoVO.numeroVezesSemana}">
                                            <a4j:support event="onkeyup"
                                                         action="#{PlanoControle.calcularQtdeCreditoTreinoHorarioTurma}"
                                                         reRender="qtdeCredito, qtdeCreditoMensal, qtdeCreditoTotal"></a4j:support>
                                        </h:inputText>
                                    </c:if>

                                    <h:outputText rendered="#{!PlanoControle.planoVO.creditoTreinoNaoCumulativo}"
                                                  styleClass="tituloCampos" value="Quantidade crédito:"/>
                                    <h:inputText id="qtdeCredito"
                                                 rendered="#{!PlanoControle.planoVO.creditoTreinoNaoCumulativo}"
                                                 size="10" maxlength="3" onblur="blurinput(this);"
                                                 readonly="#{PlanoControle.planoDuracaoCreditoTreinoVO.tipoHorarioCreditoTreinoEnum eq 'HORARIO_TURMA'}"
                                                 onfocus="focusinput(this);" styleClass="form"
                                                 value="#{PlanoControle.planoDuracaoCreditoTreinoVO.quantidadeCreditoCompra}"/>

                                    <h:outputText rendered="#{PlanoControle.planoVO.creditoTreinoNaoCumulativo}"
                                                  styleClass="tituloCampos" value="Quantidade crédito mensal:"/>
                                    <h:inputText id="qtdeCreditoMensal"
                                                 rendered="#{PlanoControle.planoVO.creditoTreinoNaoCumulativo}"
                                                 size="10" maxlength="3" onblur="blurinput(this);"
                                                 readonly="#{PlanoControle.planoDuracaoCreditoTreinoVO.tipoHorarioCreditoTreinoEnum eq 'HORARIO_TURMA'}"
                                                 onfocus="focusinput(this);" styleClass="form"
                                                 value="#{PlanoControle.planoDuracaoCreditoTreinoVO.quantidadeCreditoMensal}"/>

                                    <h:outputText
                                            rendered="#{PlanoControle.planoVO.creditoTreinoNaoCumulativo && PlanoControle.planoDuracaoCreditoTreinoVO.tipoHorarioCreditoTreinoEnum eq 'HORARIO_TURMA'}"
                                            styleClass="tituloCampos" value="Quantidade crédito total:"/>
                                    <h:inputText id="qtdeCreditoTotal"
                                                 rendered="#{PlanoControle.planoVO.creditoTreinoNaoCumulativo && PlanoControle.planoDuracaoCreditoTreinoVO.tipoHorarioCreditoTreinoEnum eq 'HORARIO_TURMA'}"
                                                 size="10" maxlength="3" onblur="blurinput(this);"
                                                 readonly="#{PlanoControle.planoDuracaoCreditoTreinoVO.tipoHorarioCreditoTreinoEnum eq 'HORARIO_TURMA'}"
                                                 onfocus="focusinput(this);" styleClass="form"
                                                 value="#{PlanoControle.planoDuracaoCreditoTreinoVO.quantidadeCreditoCompra}"/>


                                    <h:outputText styleClass="tituloCampos" value="Valor Unitário:"/>
                                    <h:inputText onkeypress="return Tecla(event);" size="20" maxlength="10"
                                                 onblur="blurinput(this);"
                                                 id="idValorUnitCredito"
                                                 onfocus="focusinput(this);" styleClass="form"
                                                 value="#{PlanoControle.planoDuracaoCreditoTreinoVO.valorUnitario}">
                                        <f:converter converterId="FormatadorNumerico3Casa"/>
                                    </h:inputText>
                                    <script>
                                        VMasker(document.getElementById("form:idValorUnitCredito")).maskMoney({
                                            precision: 3,
                                            separator: ',',
                                            delimiter: '.',
                                            zeroCents: false
                                        });
                                        VMasker(document.getElementById("form:qtdeCredito")).maskMoney({
                                            precision: 0,
                                            separator: ',',
                                            delimiter: '.',
                                            zeroCents: false
                                        });
                                        VMasker(document.getElementById("form:idVezesSemanaConfCred")).maskMoney({
                                            precision: 0,
                                            separator: ',',
                                            delimiter: '.',
                                            zeroCents: false
                                        });
                                        VMasker(document.getElementById("form:idDiasExtra")).maskMoney({
                                            precision: 0,
                                            separator: ',',
                                            delimiter: '.',
                                            zeroCents: false
                                        });

                                    </script>


                                </h:panelGrid>
                                <a4j:commandButton id="addConfCreditoTreino"
                                                   action="#{PlanoControle.adicionarPlanoDuracaoCreditoTreino}"
                                                   reRender="panelConfCreditoTreino, tableCreditoTreino, panelMensagem"
                                                   focus="form:tipoHorCredTreino" value="#{msg_bt.btn_adicionar}"
                                                   image="./imagens/botaoAdicionar.png" accesskey="6"
                                                   styleClass="botoes"/>

                                <h:panelGrid columns="1" width="100%" styleClass="tabFormSubordinada">
                                    <h:dataTable id="tableCreditoTreino" width="100%" headerClass="subordinado"
                                                 styleClass="tabFormSubordinada"
                                                 rowClasses="linhaImpar, linhaPar"
                                                 columnClasses="colunaCentralizada,colunaCentralizada,colunaCentralizada, colunaCentralizada, colunaCentralizada, colunaCentralizada, colunaDireita, colunaDireita,colunaCentralizada"
                                                 value="#{PlanoControle.planoDuracaoVOSelecionado.listaPlanoDuracaoCreditoTreino}"
                                                 var="planoDuracaoCreditoVO"><%--rows="5" (com datascroll)--%>

                                        <h:column>
                                            <f:facet name="header">
                                                <h:outputText value="Nr. Meses"/>
                                            </f:facet>
                                            <h:outputText value="#{planoDuracaoCreditoVO.planoDuracaoVO.numeroMeses}"/>
                                        </h:column>
                                        <h:column rendered="#{PlanoControle.mostrarColunaTotalDias}">
                                            <f:facet name="header">
                                                <h:outputText value="Total Dias"/>
                                            </f:facet>
                                            <h:outputText value="#{planoDuracaoCreditoVO.planoDuracaoVO.totalDias}"/>
                                        </h:column>

                                        <h:column>
                                            <f:facet name="header">
                                                <h:outputText value="Tipo Horário"/>
                                            </f:facet>
                                            <h:outputText
                                                    value="#{planoDuracaoCreditoVO.tipoHorarioCreditoTreinoEnum.descricao}"/>
                                        </h:column>
                                        <h:column>
                                            <f:facet name="header">
                                                <h:outputText value="Vezes semana"/>
                                            </f:facet>
                                            <h:outputText value="#{planoDuracaoCreditoVO.numeroVezesSemana}"/>
                                        </h:column>

                                        <h:column rendered="#{PlanoControle.planoVO.creditoTreinoNaoCumulativo}">
                                            <f:facet name="header">
                                                <h:outputText value="Quantidade crédito mensal"/>
                                            </f:facet>
                                            <h:outputText value="#{planoDuracaoCreditoVO.quantidadeCreditoMensal}"/>
                                        </h:column>

                                        <h:column>
                                            <f:facet name="header">
                                                <h:outputText value="Quantidade crédito"/>
                                            </f:facet>
                                            <h:outputText value="#{planoDuracaoCreditoVO.quantidadeCreditoCompra}"/>
                                        </h:column>
                                        <h:column>
                                            <f:facet name="header">
                                                <h:outputText value="Valor unitário"/>
                                            </f:facet>
                                            <h:outputText value="#{planoDuracaoCreditoVO.valorUnitario}">
                                                <f:converter converterId="FormatadorNumerico"/>
                                            </h:outputText>
                                        </h:column>
                                        <h:column>
                                            <f:facet name="header">
                                                <h:outputText value="Total"/>
                                            </f:facet>
                                            <h:outputText value="#{planoDuracaoCreditoVO.valorTotal}">
                                                <f:converter converterId="FormatadorNumerico"/>
                                            </h:outputText>
                                        </h:column>


                                        <h:column>
                                            <f:facet name="header">
                                                <h:outputText value="#{msg_bt.btn_opcoes}"/>
                                            </f:facet>
                                            <h:panelGroup>
                                                <a4j:commandButton id="editarConfCreditoTreino"
                                                                   reRender="panelConfiguracaoCreditoTreino, panelMensagem"
                                                                   ajaxSingle="true" immediate="true"
                                                                   action="#{PlanoControle.editarDuracaoCreditoTreino}"
                                                                   value="#{msg_bt.btn_editar}"
                                                                   image="./imagens/botaoEditar.png" accesskey="6"
                                                                   styleClass="botoes">
                                                    <f:setPropertyActionListener value="#{planoDuracaoCreditoVO}"
                                                                                 target="#{PlanoControle.planoDuracaoCreditoTreinoVO}"/>
                                                </a4j:commandButton>

                                                <h:outputText value="    "/>

                                                <a4j:commandButton id="removerConfCreditoTreino"
                                                                   reRender="tableCreditoTreino, panelMensagem"
                                                                   ajaxSingle="true" immediate="true"
                                                                   action="#{PlanoControle.removerDuracaoCreditoTreino}"
                                                                   value="#{msg_bt.btn_excluir}"
                                                                   image="./imagens/botaoRemover.png"
                                                                   accesskey="7" styleClass="botoes">
                                                    <f:setPropertyActionListener value="#{planoDuracaoCreditoVO}"
                                                                                 target="#{PlanoControle.planoDuracaoCreditoTreinoVO}"/>
                                                </a4j:commandButton>

                                            </h:panelGroup>
                                        </h:column>
                                    </h:dataTable>
                                </h:panelGrid>
                                <a4j:commandButton id="salvarConfigCreditoTreino"
                                                   reRender="panelConfCreditoTreino, panelConfiguracaoCreditoTreino,tableCreditoTreino, panelMensagem"
                                                   action="#{PlanoControle.fechaJanelaDuracaoCreditoTreino}"
                                                   value="#{msg_bt.btn_gravar}" image="./imagens/botaoGravar.png"
                                                   alt="#{msg.msg_gravar_dados}" accesskey="2" styleClass="botoes"/>
                            </h:panelGrid>
                        </h:panelGrid>

                    </rich:tab>
                    <rich:tab id="dadosHorario" label="Horários" rendered="#{!PlanoControle.planoVO.planoPersonal}">
                        <h:panelGrid id="panelPlanoHorario" columns="1" width="100%" headerClass="subordinado"
                                     columnClasses="colunaCentralizada">
                            <f:facet name="header">
                                <h:outputText value="#{msg_aplic.prt_PlanoHorario_tituloForm}"/>
                            </f:facet>
                            <h:panelGrid columns="2" width="100%" rowClasses="linhaImpar, linhaPar"
                                         columnClasses="classEsquerda, classDireita" footerClass="colunaCentralizada">
                                <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_PlanoHorario_horario}"/>
                                <h:panelGroup>
                                    <h:selectOneMenu disabled="#{!PlanoControle.habilitarCampoHorario}"
                                                     title="#{PlanoControle.titleEdicaoHorario}"
                                                     id="PlanoHorario_horario" onblur="blurinput(this);"
                                                     onfocus="focusinput(this);" styleClass="form"
                                                     value="#{PlanoControle.planoHorarioVO.horario.codigo}">
                                        <f:selectItems value="#{PlanoControle.listaSelectItemHorario}"/>
                                    </h:selectOneMenu>
                                    <a4j:commandButton id="atualizar_PlanoHorario_horario"
                                                       action="#{PlanoControle.montarListaSelectItemHorario}"
                                                       image="imagens/atualizar.png" immediate="true" ajaxSingle="true"
                                                       reRender="form:PlanoHorario_horario"/>
                                </h:panelGroup>

                                <h:outputText rendered="#{!PlanoControle.planoVO.vendaCreditoTreino}"
                                              styleClass="tituloCampos"
                                              value="#{msg_aplic.prt_PlanoHorario_tipoOperacao}"/>
                                <h:selectOneMenu rendered="#{!PlanoControle.planoVO.vendaCreditoTreino}"
                                                 id="tipoOperacaoHorario" onblur="blurinput(this);"
                                                 onfocus="focusinput(this);" styleClass="form"
                                                 value="#{PlanoControle.planoHorarioVO.tipoOperacao}">
                                    <f:selectItems value="#{PlanoControle.listaSelectItemTipoOperacao}"/>
                                </h:selectOneMenu>

                                <h:outputText rendered="#{!PlanoControle.planoVO.vendaCreditoTreino}"
                                              styleClass="tituloCampos"
                                              value="#{msg_aplic.prt_PlanoHorario_formaDesconto}"/>
                                <h:selectOneMenu rendered="#{!PlanoControle.planoVO.vendaCreditoTreino}"
                                                 id="valorTipoHorario" onblur="blurinput(this);"
                                                 onfocus="focusinput(this);" styleClass="form"
                                                 value="#{PlanoControle.planoHorarioVO.tipoValor}">
                                    <a4j:support event="onchange" focus="valorTipoHorario"
                                                 action="#{PlanoControle.planoHorarioVO.desenhaTipoValor}"
                                                 reRender="form"/>
                                    <f:selectItems value="#{PlanoControle.listaSelectItemTipoValor}"/>
                                </h:selectOneMenu>

                                <h:outputText rendered="#{PlanoControle.planoHorarioVO.desenharValorDescontoHorario}"
                                              value="#{msg_aplic.prt_PlanoHorario_percentualDesconto}"/>
                                <h:inputText rendered="#{PlanoControle.planoHorarioVO.desenharValorDescontoHorario}"
                                             id="percentualDescontoHorario" onkeypress="return Tecla(event);" size="20"
                                             maxlength="20" onblur="blurinput(this);" onfocus="focusinput(this);"
                                             styleClass="form"
                                             value="#{PlanoControle.planoHorarioVO.percentualDesconto}">
                                    <f:converter converterId="FormatadorNumerico7Casa"/>
                                </h:inputText>


                                <h:outputText rendered="#{PlanoControle.planoHorarioVO.desenharValorEspecificoHorario}"
                                              value="#{msg_aplic.prt_PlanoHorario_valorEspecifico}"/>
                                <h:inputText rendered="#{PlanoControle.planoHorarioVO.desenharValorEspecificoHorario}"
                                             id="valorEspecificoHorario" onkeypress="return Tecla(event);" size="20"
                                             maxlength="20" onblur="blurinput(this);" onfocus="focusinput(this);"
                                             styleClass="form" value="#{PlanoControle.planoHorarioVO.valorEspecifico}">
                                    <f:converter converterId="FormatadorNumerico"/>
                                </h:inputText>

                            </h:panelGrid>
                            <a4j:commandButton id="addHorario" action="#{PlanoControle.adicionarPlanoHorario}"
                                               reRender="panelPlanoHorario, panelMensagem"
                                               focus="form:PlanoHorario_horario" value="#{msg_bt.btn_adicionar}"
                                               image="./imagens/botaoAdicionar.png" accesskey="9" styleClass="botoes"/>

                            <h:panelGrid columns="1" width="100%" styleClass="tabFormSubordinada">
                                <h:dataTable id="planoHorarioVO" width="100%" headerClass="subordinado"
                                             styleClass="tabFormSubordinada"
                                             rowClasses="linhaImpar, linhaPar" columnClasses="colunaAlinhamento"
                                             value="#{PlanoControle.planoVO.planoHorarioVOs}" var="planoHorario">
                                    <h:column>
                                        <f:facet name="header">
                                            <h:outputText value="#{msg_aplic.prt_PlanoHorario_horario}"/>
                                        </f:facet>
                                        <h:outputText value="#{planoHorario.horario.descricao}"/>
                                    </h:column>
                                    <h:column rendered="#{!PlanoControle.planoVO.vendaCreditoTreino}">
                                        <f:facet name="header">
                                            <h:outputText value="#{msg_aplic.prt_PlanoHorario_tipoOperacao}"/>
                                        </f:facet>
                                        <h:outputText value="#{planoHorario.tipoOperacao_Apresentar}"/>
                                    </h:column>
                                    <h:column rendered="#{!PlanoControle.planoVO.vendaCreditoTreino}">
                                        <f:facet name="header">
                                            <h:outputText value="#{msg_aplic.prt_PlanoHorario_valorEspecifico}"/>
                                        </f:facet>
                                        <h:outputText value="#{planoHorario.valorEspecifico}">
                                            <f:converter converterId="FormatadorNumerico"/>
                                        </h:outputText>
                                    </h:column>
                                    <h:column rendered="#{!PlanoControle.planoVO.vendaCreditoTreino}">
                                        <f:facet name="header">
                                            <h:outputText value="#{msg_aplic.prt_PlanoHorario_percentualDesconto}"/>
                                        </f:facet>
                                        <h:outputText value="#{planoHorario.percentualDesconto}">
                                            <f:converter converterId="FormatadorNumerico7Casa"/>
                                        </h:outputText>
                                    </h:column>
                                    <h:column>
                                        <f:facet name="header">
                                            <h:outputText value="#{msg_bt.btn_opcoes}"/>
                                        </f:facet>
                                        <h:panelGroup>
                                            <a4j:commandButton id="editarItemVenda" reRender="form" ajaxSingle="true"
                                                               immediate="true"
                                                               action="#{PlanoControle.editarPlanoHorario}"
                                                               value="#{msg_bt.btn_editar}"
                                                               image="./imagens/botaoEditar.png" accesskey="6"
                                                               styleClass="botoes"/>

                                            <h:outputText value="    "/>

                                            <a4j:commandButton id="removerItemVenda" reRender="form" ajaxSingle="true"
                                                               immediate="true"
                                                               oncomplete="#{PlanoControle.mensagemNotificar}"
                                                               action="#{PlanoControle.removerPlanoHorario}"
                                                               value="#{msg_bt.btn_excluir}"
                                                               image="./imagens/botaoRemover.png" accesskey="7"
                                                               styleClass="botoes"/>
                                        </h:panelGroup>
                                    </h:column>
                                </h:dataTable>
                            </h:panelGrid>
                        </h:panelGrid>
                    </rich:tab>

                    <rich:tab id="dadosProdutoSugerido" label="Produtos Sugeridos"
                              rendered="#{!PlanoControle.planoVO.planoPersonal}">
                        <h:panelGrid id="panelPlanoProdutoSugerido" columns="1" width="100%" headerClass="subordinado"
                                     columnClasses="colunaCentralizada">
                            <f:facet name="header">
                                <h:outputText value="#{msg_aplic.prt_PlanoProdutoSugerido_tituloForm}"/>
                            </f:facet>
                            <h:panelGrid columns="2" width="100%" rowClasses="linhaImpar, linhaPar"
                                         columnClasses="classEsquerda, classDireita">
                                <h:outputText styleClass="tituloCampos"
                                              value="#{msg_aplic.prt_PlanoProdutoSugerido_produto}"/>
                                <h:panelGroup>
                                    <h:inputText readonly="true" id="ProdutoSugerido_produto" size="40" maxlength="45"
                                                 onblur="blurinput(this);" onfocus="focusinput(this);" styleClass="form"
                                                 value="#{PlanoControle.planoProdutoSugeridoVO.produto.descricao}"/>
                                    <a4j:commandButton id="consultaDadosPlanoProduto" focus="valorConsultaPlanoProduto"
                                                       alt="Consulta Produto" reRender="formPlanoProduto"
                                                       oncomplete="Richfaces.showModalPanel('panelPlanoProduto'), setFocus(formPlanoProduto,'formPlanoProduto:valorConsultaPlanoProduto');"
                                                       image="./imagens/informacao.gif"/>
                                </h:panelGroup>
                                <h:outputText styleClass="tituloCampos"
                                              value="#{msg_aplic.prt_PlanoProdutoSugerido_valorProduto}"/>
                                <h:panelGroup>
                                    <h:inputText id="ProdutoSugerido_valorProduto" size="10" maxlength="10"
                                                 onblur="blurinput(this);" onfocus="focusinput(this);" styleClass="form"
                                                 value="#{PlanoControle.planoProdutoSugeridoVO.valorProduto}">
                                        <f:converter converterId="FormatadorNumerico"/>
                                    </h:inputText>
                                </h:panelGroup>
                                <h:outputText styleClass="tituloCampos"
                                              value="#{msg_aplic.prt_PlanoProdutoSugerido_obrigatorio}"/>
                                <h:selectBooleanCheckbox id="produtoSugerido_obrigatorio" styleClass="campos"
                                                         disabled="#{PlanoControle.planoProdutoSugeridoVO.desabilitarObrigatoriedade}"
                                                         value="#{PlanoControle.planoProdutoSugeridoVO.obrigatorio}"/>
                            </h:panelGrid>
                            <a4j:commandButton id="addProdutoSugerido"
                                               action="#{PlanoControle.adicionarPlanoProdutoSugerido}"
                                               reRender="panelPlanoProdutoSugerido, panelMensagem"
                                               focus="form:ProdutoSugerido_produto" value="#{msg_bt.btn_adicionar}"
                                               image="./imagens/botaoAdicionar.png" accesskey="5" styleClass="botoes"/>

                            <h:panelGrid columns="1" width="100%" styleClass="tabFormSubordinada">
                                <h:dataTable id="planoProdutoSugeridoVO" width="100%" headerClass="subordinado"
                                             styleClass="tabFormSubordinada"
                                             rowClasses="linhaImpar, linhaPar" columnClasses="colunaAlinhamento"
                                             value="#{PlanoControle.planoVO.planoProdutoSugeridoVOs}"
                                             var="planoProdutoSugerido">
                                    <h:column>
                                        <f:facet name="header">
                                            <h:outputText value="#{msg_aplic.prt_PlanoProdutoSugerido_produto}"/>
                                        </f:facet>
                                        <h:outputText value="#{planoProdutoSugerido.produto.descricao}"/>
                                    </h:column>
                                    <h:column>
                                        <f:facet name="header">
                                            <h:outputText value="#{msg_aplic.prt_PlanoProdutoSugerido_valorProduto}"/>
                                        </f:facet>
                                        <h:outputText value="#{planoProdutoSugerido.valorProduto}">
                                            <f:converter converterId="FormatadorNumerico"/>
                                        </h:outputText>
                                    </h:column>
                                    <h:column>
                                        <f:facet name="header">
                                            <h:outputText
                                                    value="#{msg_aplic.prt_PlanoProdutoSugerido_situacaoProduto}"/>
                                        </f:facet>
                                        <h:outputText value="#{planoProdutoSugerido.produto.situacao}"/>
                                    </h:column>
                                    <h:column>
                                        <f:facet name="header">
                                            <h:outputText value="#{msg_aplic.prt_PlanoProdutoSugerido_obrigatorio}"/>
                                        </f:facet>
                                        <h:outputText value="#{planoProdutoSugerido.obrigatorio_Apresentar}"/>
                                    </h:column>
                                    <h:column>
                                        <f:facet name="header">
                                            <h:outputText value="#{msg_bt.btn_opcoes}"/>
                                        </f:facet>
                                        <h:panelGroup>
                                            <a4j:commandButton id="editarItemVenda" reRender="form" ajaxSingle="true"
                                                               immediate="true"
                                                               action="#{PlanoControle.editarPlanoProdutoSugerido}"
                                                               value="#{msg_bt.btn_editar}"
                                                               image="./imagens/botaoEditar.png" accesskey="6"
                                                               styleClass="botoes"/>

                                            <h:outputText value="    "/>

                                            <a4j:commandButton id="removerItemVenda" reRender="form" ajaxSingle="true"
                                                               immediate="true"
                                                               action="#{PlanoControle.removerPlanoProdutoSugerido}"
                                                               value="#{msg_bt.btn_excluir}"
                                                               image="./imagens/botaoRemover.png" accesskey="7"
                                                               styleClass="botoes"/>
                                        </h:panelGroup>
                                    </h:column>
                                </h:dataTable>
                            </h:panelGrid>
                        </h:panelGrid>
                    </rich:tab>
                    <rich:tab id="dadosConfiguracoesGerais" label="Configurações Gerais">
                        <h:panelGrid id="panelPlanoConfiguracoesGerais" columns="1" width="100%"
                                     headerClass="subordinado"
                                     columnClasses="colunaCentralizada">
                            <f:facet name="header">
                                <h:outputText value="#{msg_aplic.prt_PlanoConfiguracoesCancelamento_tituloForm}"/>
                            </f:facet>
                            <h:panelGrid id="panelConfiguracoesGerais" columns="1" width="100%"
                                         headerClass="subordinado">
                                <h:panelGrid columns="2" width="100%" rowClasses="linhaImpar, linhaPar"
                                             columnClasses="classEsquerda, classDireita">
                                    <h:outputText styleClass="tituloCampos"
                                                  value="#{msg_aplic.prt_Plano_produtoTaxaCancelamento}"/>
                                    <h:panelGroup>
                                        <h:selectOneMenu id="produtoTaxaCancelamento" onblur="blurinput(this);"
                                                         onfocus="focusinput(this);" styleClass="form"
                                                         value="#{PlanoControle.planoVO.produtoTaxaCancelamento.codigo}">
                                            <f:selectItems
                                                    value="#{PlanoControle.listaSelectItemProdutoTaxaCancelamento}"/>
                                        </h:selectOneMenu>
                                        <a4j:commandButton id="atualizar_produtoTaxaCancelamento"
                                                           action="#{PlanoControle.montarListaSelectItemProdutoTaxaCancelamento}"
                                                           image="imagens/atualizar.png" ajaxSingle="true"
                                                           reRender="form:produtoTaxaCancelamento"/>
                                    </h:panelGroup>
                                    <h:outputText styleClass="tituloCampos"
                                                  value="#{msg_aplic.prt_Plano_percentualMultaCancelamento}"/>
                                    <h:panelGroup>
                                        <h:inputText id="taxaCancelamento" onblur="blurinput(this);"
                                                     onfocus="focusinput(this);"
                                                     styleClass="form"
                                                     value="#{PlanoControle.planoVO.percentualMultaCancelamento}">
                                            <f:converter converterId="FormatadorNumerico7Casa"/>
                                        </h:inputText>
                                        <rich:spacer width="10"/>
                                        <h:outputText value="%"/>
                                    </h:panelGroup>
                                </h:panelGrid>
                            </h:panelGrid>
                            <h:panelGrid rendered="#{((!PlanoControle.planoVO.regimeRecorrencia) or (PlanoControle.configuracaoSistema.permiteLancarFeriasPlanoRecorrente))}"
                                         id="panelGridEdicaoCarencia" columns="1" width="100%"
                                         styleClass="tabFormSubordinada"
                                         headerClass="subordinado" columnClasses="colunaCentralizada">
                                <f:facet name="header">
                                    <h:outputText value="#{msg_aplic.CARENCIA}"/>
                                </f:facet>
                                <h:dataTable id="planoDuracaoVO2" width="100%" headerClass="subordinado"
                                             styleClass="tabFormSubordinada"
                                             rowClasses="linhaImpar, linhaPar" columnClasses="colunaAlinhamento"
                                             value="#{PlanoControle.planoVO.planoDuracaoVOs}" var="planoDuracao">
                                    <h:column>
                                        <f:facet name="header">
                                            <h:outputText value="#{msg_aplic.prt_PlanoDuracao_numeroMeses_red}"/>
                                        </f:facet>
                                        <h:outputText value="#{planoDuracao.numeroMeses}"/>
                                    </h:column>
                                    <h:column>
                                        <f:facet name="header">
                                            <h:outputText value="#{msg_aplic.prt_ConfiguracoesGeraisFerias_nrFerias}"/>
                                        </f:facet>
                                        <h:outputText id="outputTextCarencia" value="#{planoDuracao.carencia}"/>
                                    </h:column>
                                    <h:column>
                                        <f:facet name="header">
                                            <h:outputText value="#{msg_bt.btn_opcoes}"/>
                                        </f:facet>
                                        <h:panelGroup>
                                            <a4j:commandButton id="editarItemVenda2"
                                                               reRender="panelConfiguracoesGeraisCarencia,panelMensagem"
                                                               action="#{PlanoControle.selecionarCarenciaPlanoDuracao}"
                                                               value="#{msg_bt.btn_editar}"
                                                               image="./imagens/botaoEditar.png" accesskey="6"
                                                               styleClass="botoes"/>
                                        </h:panelGroup>
                                    </h:column>
                                </h:dataTable>
                            </h:panelGrid>
                            <h:panelGrid id="panelConfiguracoesGeraisCarencia" columns="1" width="100%"
                                         headerClass="subordinado"
                                         columnClasses="colunaCentralizada">
                                <h:panelGrid id="panelCarencia" rendered="#{PlanoControle.apresentarPanelCarencia}"
                                             columns="1"
                                             width="100%" headerClass="subordinado" columnClasses="colunaCentralizada">
                                    <h:panelGrid columns="2" width="100%" rowClasses="linhaImpar, linhaPar"
                                                 columnClasses="classEsquerda, classDireita"
                                                 footerClass="colunaCentralizada">
                                        <h:outputText value="#{msg_aplic.prt_PlanoDuracao_numeroMeses}"/>
                                        <h:inputText id="numeroMesesDuracao3" size="10" maxlength="10" readonly="true"
                                                     onblur="blurinput(this);" onfocus="focusinput(this);"
                                                     styleClass="form"
                                                     value="#{PlanoControle.planoDuracaoVO.numeroMeses}"/>
                                        <h:outputText value="#{msg_aplic.prt_ConfiguracoesGeraisFerias_ferias}"/>
                                        <h:inputText id="valorCarencia" size="10" maxlength="10"
                                                     onblur="blurinput(this);"
                                                     onfocus="focusinput(this);" styleClass="form"
                                                     value="#{PlanoControle.planoDuracaoVO.carencia}"/>
                                    </h:panelGrid>
                                    <a4j:commandButton id="addDiasCarencia" action="#{PlanoControle.adicionarCarencia}"
                                                       reRender="form, panelGridEdicaoCarencia, panelMensagem"
                                                       value="#{msg_bt.btn_adicionar}"
                                                       image="./imagens/botaoAdicionar.png" accesskey="61"
                                                       styleClass="botoes"/>
                                </h:panelGrid>
                            </h:panelGrid>

                            <h:panelGrid id="panelGridProrata" columns="1" width="100%" styleClass="tabFormSubordinada"
                                         headerClass="subordinado" columnClasses="colunaCentralizada">
                                <f:facet name="header">
                                    <h:outputText value="#{msg_aplic.prt_PlanoConfiguracoesProrata_tituloForm}"/>
                                </f:facet>
                                <h:panelGrid columns="2" width="100%" rowClasses="linhaImpar, linhaPar"
                                             columnClasses="classEsquerda, classDireita">
                                    <h:outputText styleClass="tituloCampos"
                                                  value="#{msg_aplic.prt_PlanoConfiguracoesProrata_obrigatorio}"/>
                                    <h:selectBooleanCheckbox id="prorataObrigatorio" styleClass="campos"
                                                             title="Se marcado todos os contratos feitos com este plano terão um dia de vencimento fixo da lista abaixo."
                                                             value="#{PlanoControle.planoVO.prorataObrigatorio}"/>
                                    <h:outputText styleClass="tituloCampos"
                                                  value="#{msg_aplic.prt_PlanoConfiguracoesProrata_prorata}"/>
                                    <h:inputText id="diaVencimento" size="10" maxlength="10" onblur="blurinput(this);"
                                                 onfocus="focusinput(this);" styleClass="form"
                                                 value="#{PlanoControle.diaVencimento}"/>
                                </h:panelGrid>
                                <a4j:commandLink id="adicionarVencimentos"
                                                 action="#{PlanoControle.adicionarTodosOsDiasVencimento}"
                                                 reRender="panelMensagem, planoProrata"
                                                 value="Adicionar Todos"/>
                                <a4j:commandButton id="addVencimento" action="#{PlanoControle.adicionarVecimento}"
                                                   reRender="panelMensagem, planoProrata"
                                                   value="#{msg_bt.btn_adicionar}" image="./imagens/botaoAdicionar.png"
                                                   accesskey="62" styleClass="botoes"/>
                                <h:dataTable id="planoProrata" width="100%" headerClass="subordinado"
                                             styleClass="tabFormSubordinada"
                                             rowClasses="linhaImpar, linhaPar" columnClasses="colunaAlinhamento"
                                             style="text-align: center;"
                                             value="#{PlanoControle.planoVO.listaDiasVencimento}" var="dias">
                                    <h:column>
                                        <f:facet name="header">
                                            <h:outputText
                                                    value="#{msg_aplic.prt_PlanoConfiguracoesProrata_vencimentos}"/>
                                        </f:facet>
                                        <h:outputText value="#{dias}"/>
                                    </h:column>
                                    <h:column>
                                        <f:facet name="header">
                                            <h:outputText value="#{msg_bt.btn_opcoes}"/>
                                        </f:facet>
                                        <h:panelGroup>
                                            <a4j:commandButton id="editarDiaVencimento" reRender="panelGridProrata"
                                                               ajaxSingle="true" immediate="true"
                                                               action="#{PlanoControle.editarDiaVencimento}"
                                                               value="#{msg_bt.btn_editar}"
                                                               image="./imagens/botaoEditar.png" accesskey="6"
                                                               styleClass="botoes"/>

                                            <h:outputText value="    "/>

                                            <a4j:commandButton id="removerDiaVencimento" reRender="panelGridProrata"
                                                               ajaxSingle="true" immediate="true"
                                                               action="#{PlanoControle.removerDiaVencimento}"
                                                               value="#{msg_bt.btn_excluir}"
                                                               image="./imagens/botaoRemover.png" accesskey="7"
                                                               styleClass="botoes"/>
                                        </h:panelGroup>
                                    </h:column>
                                </h:dataTable>
                                <a4j:commandLink id="removerVencimentos" ajaxSingle="true" immediate="true"
                                                 action="#{PlanoControle.removerTodosOsDiasVencimentos}"
                                                 reRender="panelGridProrata"
                                                 value="Remover Todos"/>
                            </h:panelGrid>

                            <h:panelGrid rendered="#{!PlanoControle.planoVO.regimeRecorrencia}"
                                         id="panelGridRenovarAutomaticoNormal" columns="1" width="100%"
                                         styleClass="tabFormSubordinada"
                                         headerClass="subordinado" columnClasses="colunaCentralizada">
                                <f:facet name="header">
                                    <h:outputText value="Renovação Automática"/>
                                </f:facet>
                                <h:panelGrid id="gridRenovacaoAuto" columns="2" width="100%"
                                             rowClasses="linhaImpar, linhaPar"
                                             columnClasses="classEsquerda, classDireita">
                                    <h:outputText styleClass="tituloCampos"
                                                  value="#{msg_aplic.prt_PlanoRecorrencia_renovavelAutomaticamente}"/>
                                    <h:panelGroup>
                                        <h:selectBooleanCheckbox styleClass="campos"
                                                                 title="Se marcado todos os contratos feitos com este plano serão renovados automaticamente."
                                                                 value="#{PlanoControle.planoVO.renovavelAutomaticamente}">

                                            <a4j:support event="onchange"
                                                         action="#{PlanoControle.consultarContratosRenovacaoAutomatica}"
                                                         reRender="gridRenovacaoAuto, modalContratosRenovacaoAutomatica"/>
                                        </h:selectBooleanCheckbox>

                                        <a4j:commandLink style="color: red" id="idLinkContratosRenovacaoAut"
                                                         onclick="Richfaces.showModalPanel('modalContratosRenovacaoAutomatica');"
                                                         rendered="#{PlanoControle.textoLinkRenovacaoAutomatica != null}"
                                                         value="#{PlanoControle.textoLinkRenovacaoAutomatica}"></a4j:commandLink>
                                    </h:panelGroup>

                                    <h:outputText styleClass="tituloCampos tooltipster"
                                                  rendered="#{PlanoControle.planoVO.renovavelAutomaticamente}"
                                                  value="Permitir que a renovação automática renove o plano com desconto"
                                                  title="Ao selecionar esta opção, a renovação automática vai renovar os planos com os respectivos descontos (caso tenha desconto) que foram dados no ato da compra do plano"/>
                                    <h:selectBooleanCheckbox
                                            rendered="#{PlanoControle.planoVO.renovavelAutomaticamente}"
                                            id="renovarComDesconto"
                                            value="#{PlanoControle.planoVO.renovarAutomaticamenteComDesconto}"
                                            styleClass="tituloCampos tooltipster"
                                            title="Ao selecionar esta opção, a renovação automática vai renovar os planos com os respectivos descontos (caso tenha desconto) que foram dados no ato da compra do plano"/>

                                    <h:outputText rendered="#{PlanoControle.planoVO.renovavelAutomaticamente}"
                                                  styleClass="tituloCampos"
                                                  value="#{msg_aplic.prt_PlanoRecorrencia_renovarProdutoObrigatorio}"/>
                                    <h:selectBooleanCheckbox
                                            rendered="#{PlanoControle.planoVO.renovavelAutomaticamente}"
                                            styleClass="campos"
                                            title="Se marcado todos produtos sugeridos marcados como obrigatório, serão adicionados ao contrato renovado, exceto produtos do tipo (MA,RN,RE,TA,TD)."
                                            value="#{PlanoControle.planoVO.renovarProdutoObrigatorio}">

                                    </h:selectBooleanCheckbox>

                                    <h:outputText rendered="#{PlanoControle.planoVO.renovavelAutomaticamente}"
                                                  styleClass="tituloCampos"
                                                  value="#{msg_aplic.prt_PlanoRecorrencia_renovarAutomaticamenteUtilizandoValorBaseContrato}"/>
                                    <h:selectBooleanCheckbox
                                            rendered="#{PlanoControle.planoVO.renovavelAutomaticamente}"
                                            styleClass="campos"
                                            title="Se marcado todos os contratos serão renovados com o valor que foi negociado."
                                            value="#{PlanoControle.planoVO.renovarAutomaticamenteUtilizandoValorBaseContrato}">
                                    </h:selectBooleanCheckbox>


                                    <h:outputText styleClass="tituloCampos tooltipster"
                                                  rendered="#{PlanoControle.planoVO.renovavelAutomaticamente}"
                                                  value="Renovar apenas planos com condição de pagamento recorrência"
                                                  title="Ao selecionar esta opção, a renovação automática vai renovar apenas os contratos que possuem condição de pagamento recorrência"/>
                                    <h:selectBooleanCheckbox
                                            rendered="#{PlanoControle.planoVO.renovavelAutomaticamente}"
                                            id="renovarApenasCondicaoDCC"
                                            value="#{PlanoControle.planoVO.renovarAutomaticamenteApenasCondicaoPagamentoRecorrencia}"
                                            styleClass="tituloCampos tooltipster"
                                            title="Ao selecionar esta opção, a renovação automática vai renovar apenas os contratos que possuem condição de pagamento recorrência"/>

                                    <h:outputText styleClass="tituloCampos tooltipster"
                                                  rendered="#{PlanoControle.planoVO.renovavelAutomaticamente}"
                                                  value="Não Renovar Contrato com Parcela Vencida em Aberto"
                                                  title="Ao selecionar esta opção, a renovação automática não irá renovar contratos que possuam parcelas vencidas em aberto."/>
                                    <h:selectBooleanCheckbox
                                            rendered="#{PlanoControle.planoVO.renovavelAutomaticamente}"
                                            id="naoRenovarComParcelaVencidaAberto"
                                            value="#{PlanoControle.planoVO.naoRenovarContratoParcelaVencidaAberto}"
                                            styleClass="tituloCampos tooltipster"
                                            title="Ao selecionar esta opção, a renovação automática não irá renovar contratos que possuam parcelas vencidas em aberto."/>

                                </h:panelGrid>

                            </h:panelGrid>
                            <h:panelGrid
                                    id="panelGridManutencao" columns="1" width="100%" styleClass="tabFormSubordinada"
                                    headerClass="subordinado" columnClasses="colunaCentralizada">
                                <f:facet name="header">
                                    <h:outputText value="Manutenção de Modalidade"/>
                                </f:facet>
                                <h:panelGrid id="panelConfiguracoesManutencao" columns="1" width="100%"
                                             headerClass="subordinado">
                                    <h:panelGrid columns="2" width="100%" rowClasses="linhaImpar, linhaPar"
                                                 columnClasses="classEsquerda, classDireita">
                                        <h:outputText styleClass="tituloCampos"
                                                      value="Dividir valor nas parcelas em abertos ainda não vencidas"/>
                                        <h:selectBooleanCheckbox id="dividirValorParcelas"
                                                                 styleClass="campos"
                                                                 title="Se marcado, o valor das manutenções será divido entre as parcelas em aberto ainda não vencidas. Caso não exista parcelas nessa condição, será gerado apenas uma parcela com o valor"
                                                                 value="#{PlanoControle.planoVO.dividirManutencaoParcelasEA}"/>
                                    </h:panelGrid>
                                </h:panelGrid>
                            </h:panelGrid>

                            <h:panelGrid rendered="#{!PlanoControle.planoVO.regimeRecorrencia}"
                                         id="panelGridParcelarMatriculaProdutoNormal" columns="1" width="100%"
                                         styleClass="tabFormSubordinada"
                                         headerClass="subordinado" columnClasses="colunaCentralizada">
                                <f:facet name="header">
                                    <h:outputText value="Parcelar Matrícula e Produto"/>
                                </f:facet>
                                <h:panelGrid id="gridParcMatProd" columns="2" width="100%"
                                             rowClasses="linhaImpar, linhaPar"
                                             columnClasses="classEsquerda, classDireita">
                                    <h:outputText styleClass="tituloCampos"
                                                  value="#{msg_aplic.prt_Plano_permiteCobrarMatriculaSeparada}"/>

                                    <h:selectBooleanCheckbox id="cobrarMatriculaSeparada" styleClass="campos"
                                                             value="#{PlanoControle.planoVO.cobrarAdesaoSeparada}">
                                        <a4j:support event="onclick" reRender="gridParcMatProd"/>
                                    </h:selectBooleanCheckbox>

                                    <h:outputText styleClass="tituloCampos"
                                                  value="#{msg_aplic.prt_Plano_NrVezesParcelarMatricula}"
                                                  rendered="#{PlanoControle.planoVO.cobrarAdesaoSeparada and not PlanoControle.planoVO.site}"/>
                                    <h:selectOneMenu id="cobrarMatriculaSeparadaNrVezes"
                                                     value="#{PlanoControle.planoVO.nrVezesParcelarAdesao}"
                                                     rendered="#{PlanoControle.planoVO.cobrarAdesaoSeparada and not PlanoControle.planoVO.site}">
                                        <f:selectItems value="#{PlanoControle.listaVezesParcelarMatricula}"/>
                                    </h:selectOneMenu>


                                    <h:outputText rendered="#{!PlanoControle.planoVO.planoPersonal}"
                                                  styleClass="tituloCampos"
                                                  value="#{msg_aplic.prt_Plano_permitecobrarProdutoSeparado}"/>
                                    <h:selectBooleanCheckbox rendered="#{!PlanoControle.planoVO.planoPersonal}"
                                                             id="cobrarProdutoSeparadoRecorrencia"
                                                             styleClass="campos"
                                                             value="#{PlanoControle.planoVO.cobrarProdutoSeparado}">
                                        <a4j:support event="onclick" reRender="gridParcMatProd"/>
                                    </h:selectBooleanCheckbox>

                                    <h:outputText styleClass="tituloCampos"
                                                  value="#{msg_aplic.prt_Plano_NrVezesParcelarProduto}"
                                                  rendered="#{PlanoControle.planoVO.cobrarProdutoSeparado and not PlanoControle.planoVO.site}"/>
                                    <h:selectOneMenu value="#{PlanoControle.planoVO.nrVezesParcelarProduto}"
                                                     rendered="#{PlanoControle.planoVO.cobrarProdutoSeparado and not PlanoControle.planoVO.site}">
                                        <f:selectItems value="#{PlanoControle.listaVezesParcelarMatricula}"/>
                                    </h:selectOneMenu>
                                </h:panelGrid>

                            </h:panelGrid>

                        </h:panelGrid>
                    </rich:tab>
                    <c:if test="${PlanoControle.exibirReplicarRedeEmpresa}">
                        <rich:tab id="tabReplicarEmpresas" label="Replicar Empresas"
                                  rendered="#{PlanoControle.exibirReplicarRedeEmpresa}">
                            <h:panelGrid columns="1" width="100%" headerClass="subordinado"
                                         columnClasses="colunaCentralizada">
                                <f:facet name="header">
                                    <h:outputText value="#{msg_aplic.prt_PlanoReplicarEmpresa_tituloForm}"/>
                                </f:facet>
                                <h:panelGrid columns="3" style="border-style: solid;" id="contadorReplicaPlano"
                                             columnClasses="colunaCentralizada, colunaCentralizada, colunaCentralizada"
                                             width="100%">
                                    <h:outputText value="Unidades" styleClass="botoes nvoBt"/>
                                    <h:outputText value="Replicadas" styleClass="botoes nvoBt"/>
                                    <h:outputText value="Não Replicadas" styleClass="botoes nvoBt"/>
                                    <h:outputText value="#{PlanoControle.listaPlanoRedeEmpresaSize}"
                                                  style="font-size: 20pt; font-weight: bold;"/>
                                    <h:outputText value="#{PlanoControle.listaPlanoRedeEmpresaSincronizado}"
                                                  style="color: #0f4c36; font-size: 20pt; font-weight: bold;"/>
                                    <h:outputText
                                            value="#{PlanoControle.listaPlanoRedeEmpresaSize - PlanoControle.listaPlanoRedeEmpresaSincronizado}"
                                            style="color: #8b0000; font-size: 20pt; font-weight: bold;"/>
                                </h:panelGrid>
                                <h:panelGrid columns="1" id="contadorReplicaPlano2"
                                             columnClasses="colunaDireita"
                                             width="100%"
                                             style="margin-top: 20px; margin-bottom: 1px">
                                    <h:panelGroup layout="block">
                                        <a4j:commandButton value="Replicar Todas" styleClass="botoes nvoBt"
                                                           action="#{PlanoControle.replicarTodas}"
                                                           reRender="listaEmpresasReplicar, contadorReplicaPlano"
                                                           ajaxSingle="true" immediate="true"/>
                                        <a4j:commandButton value="Replicar Selecionadas" styleClass="botoes nvoBt btSec"
                                                           action="#{PlanoControle.replicarSelecionadas}"
                                                           reRender="listaEmpresasReplicar, contadorReplicaPlano"
                                                           ajaxSingle="true" immediate="true"/>
                                        <a4j:commandButton value="Limpar Selecionadas" styleClass="botoes nvoBt btSec"
                                                           action="#{PlanoControle.limparReplicar}"
                                                           reRender="listaEmpresasReplicar, contadorReplicaPlano"
                                                           ajaxSingle="true" immediate="true"/>
                                    </h:panelGroup>
                                </h:panelGrid>
                                <h:panelGrid columns="1" rowClasses="linhaImpar, linhaPar"
                                             columnClasses="colunaCentralizada" width="100%">

                                    <h:dataTable id="listaEmpresasReplicar" width="100%" headerClass="subordinado"
                                                 styleClass="tabFormSubordinada"
                                                 rowClasses="linhaImpar, linhaPar"
                                                 columnClasses="colunaEsquerda, colunaEsquerda, colunaCentralizada, colunaEsquerda"
                                                 style="text-align: center;"
                                                 value="#{PlanoControle.listaPlanoRedeEmpresa}"
                                                 var="planoRedeEmpresaReplicacao">
                                        <h:column>
                                            <f:facet name="header">
                                                <h:outputText value=""/>
                                            </f:facet>
                                            <h:selectBooleanCheckbox id="check" styleClass="form"
                                                                     rendered="#{!planoRedeEmpresaReplicacao.dataAtualizacaoInformada}"
                                                                     value="#{planoRedeEmpresaReplicacao.selecionado}">
                                                <a4j:support event="onchange" reRender="listaEmpresasReplicar"/>
                                            </h:selectBooleanCheckbox>
                                        </h:column>
                                        <h:column>
                                            <f:facet name="header">
                                                <h:outputText value="#{msg_aplic.prt_PlanoRedeEmpresa_nomeUnidade}"/>
                                            </f:facet>
                                            <h:outputText value="#{planoRedeEmpresaReplicacao.nomeUnidade}"/>
                                        </h:column>
                                        <h:column>
                                            <f:facet name="header">
                                                <h:outputText value="#{msg_aplic.prt_PlanoRedeEmpresa_chave}"/>
                                            </f:facet>
                                            <h:outputText value="#{planoRedeEmpresaReplicacao.chave}"/>
                                        </h:column>
                                        <h:column>
                                            <f:facet name="header">
                                                <h:outputText value=""/>
                                            </f:facet>
                                            <h:panelGroup>
                                                <a4j:commandButton id="replicarPlano"
                                                                   reRender="listaEmpresasReplicar, contadorReplicaPlano"
                                                                   ajaxSingle="true" immediate="true"
                                                                   rendered="#{!planoRedeEmpresaReplicacao.dataAtualizacaoInformada}"
                                                                   action="#{PlanoControle.replicarPlanoRedeEmpresaGeral}"
                                                                   value="Replicar"/>
                                                <h:graphicImage url="./images/check.png"
                                                                rendered="#{planoRedeEmpresaReplicacao.dataAtualizacaoInformada}"/>
                                            </h:panelGroup>
                                        </h:column>
                                        <h:column>
                                            <f:facet name="header">
                                                <h:outputText
                                                        value="#{msg_aplic.prt_PlanoRedeEmpresa_mensagemSituacao}"/>
                                            </f:facet>
                                            <h:outputText value="#{planoRedeEmpresaReplicacao.mensagemSituacao}"/>
                                        </h:column>
                                        <h:column>
                                            <f:facet name="header">
                                                <h:outputText
                                                        value="Vínculo"/>
                                            </f:facet>
                                            <h:panelGroup>
                                                <a4j:commandButton
                                                        rendered="#{planoRedeEmpresaReplicacao.dataAtualizacaoInformada}"
                                                        reRender="listaEmpresasReplicar, contadorReplicaPlano"
                                                        ajaxSingle="true" immediate="true"
                                                        action="#{PlanoControle.retirarVinculoReplicacao}"
                                                        value="Retirar"/>
                                            </h:panelGroup>
                                        </h:column>

                                    </h:dataTable>
                                </h:panelGrid>
                            </h:panelGrid>
                        </rich:tab>
                    </c:if>
                    <rich:tab id="recorrencia" styleClass="tabela-recorrencia" label="Recorrência"
                              rendered="#{PlanoControle.mostrarAbaRecorrencia}">
                        <h:panelGrid id="panelRecorrencia" columns="1" width="100%" headerClass="subordinado"
                                     columnClasses="colunaCentralizada">
                            <f:facet name="header">
                                <h:outputText value="#{msg_aplic.prt_PlanoRecorrencia_tituloForm}"/>
                            </f:facet>
                            <h:panelGrid columns="2" rowClasses="linhaImpar, linhaPar"
                                         columnClasses="classEsquerda, classDireita" width="100%"
                                         id="dadosRecorrencia">
                                <h:outputText styleClass="tituloCampos"
                                              value="#{msg_aplic.prt_PlanoRecorrencia_valorMensal}"/>
                                <h:panelGroup>
                                    <h:inputText id="valorMensal" size="10" maxlength="10" onblur="blurinput(this);"
                                                 onkeypress="return formatar_moeda(this,'.',',',event);"
                                                 onfocus="focusinput(this);"
                                                 styleClass="form"
                                                 value="#{PlanoControle.planoVO.planoRecorrencia.valorMensal}">
                                        <f:converter converterId="FormatadorNumerico"/>
                                    </h:inputText>
                                </h:panelGroup>

                                <h:outputText styleClass="tituloCampos"
                                              value="#{msg_aplic.prt_PlanoRecorrencia_taxaAdesao}"/>
                                <h:panelGroup>
                                    <h:inputText id="valorTaxaAdesao" size="10" maxlength="10" onblur="blurinput(this);"
                                                 onkeypress="return formatar_moeda(this,'.',',',event);"
                                                 onfocus="focusinput(this);"
                                                 styleClass="form"
                                                 value="#{PlanoControle.planoVO.planoRecorrencia.taxaAdesao}">
                                        <f:converter converterId="FormatadorNumerico"/>
                                    </h:inputText>
                                </h:panelGroup>

                                <h:outputText styleClass="tituloCampos tooltipster"
                                              value="#{msg_aplic.prt_PlanoRecorrencia_valorAnuidade}"
                                              title="Defina o valor da anuidade que poderá ser cobrada por dia e mês fixo do ano,
                                e também poderá ser cobrada no momento da venda sendo gerada com o mesmo vencimento da parcela desejada.<br/>
                                Obs: Para cobrar na data específica, use os dois campos \"dia\" e \"mês\" localizados ao lado do campo \"valor\".<br/>
                                Para cobrar na mesma data de vencimento que alguma parcela específica, utilize a configuração localizada mais abaixo \"Gerar anuidade com mesmo vencimento da parcela\"."/>

                                <h:panelGroup>
                                    <h:inputText id="valorAnuidade" size="10" maxlength="10" onblur="blurinput(this);"
                                                 disabled="#{PlanoControle.planoVO.planoRecorrencia.parcelarAnuidade and fn:length(PlanoControle.planoVO.planoRecorrencia.parcelasAnuidade) > 0}"
                                                 onkeypress="return formatar_moeda(this,'.',',',event);"
                                                 onfocus="focusinput(this);"
                                                 styleClass="form"
                                                 value="#{PlanoControle.planoVO.planoRecorrencia.valorAnuidade}">
                                        <f:converter converterId="FormatadorNumerico"/>
                                    </h:inputText>
                                    <rich:spacer width="5px"/>
                                    <h:outputText
                                            rendered="#{!PlanoControle.planoVO.planoRecorrencia.anuidadeNaParcela}"
                                            styleClass="tituloCampos"
                                            value="#{msg_aplic.prt_PlanoRecorrencia_diaAnuidade}"/>
                                    <h:panelGroup
                                            rendered="#{!PlanoControle.planoVO.planoRecorrencia.anuidadeNaParcela}">
                                        <h:selectOneMenu id="diaAnuidade"
                                                         value="#{PlanoControle.planoVO.planoRecorrencia.diaAnuidade}">
                                            <f:selectItems value="#{PlanoControle.diasAnuidade}"/>
                                        </h:selectOneMenu>
                                    </h:panelGroup>
                                    <rich:spacer width="5px"/>
                                    <h:outputText
                                            rendered="#{!PlanoControle.planoVO.planoRecorrencia.anuidadeNaParcela}"
                                            styleClass="tituloCampos"
                                            value="#{msg_aplic.prt_PlanoRecorrencia_mesAnuidade}"/>
                                    <h:panelGroup
                                            rendered="#{!PlanoControle.planoVO.planoRecorrencia.anuidadeNaParcela}">
                                        <h:selectOneMenu id="mesAnuidade"
                                                         value="#{PlanoControle.planoVO.planoRecorrencia.mesAnuidade}">
                                            <f:selectItems value="#{PlanoControle.mesesAnuidade}"/>
                                        </h:selectOneMenu>
                                    </h:panelGroup>
                                </h:panelGroup>

                                <h:outputText styleClass="tituloCampos"
                                              value="#{msg_aplic.prt_PlanoRecorrencia_fidelidadePlano}"/>
                                <h:panelGroup>
                                    <h:inputText id="duracaoPlano" size="10" maxlength="10"
                                                 onfocus="focusinput(this);" styleClass="form"
                                                 value="#{PlanoControle.planoVO.planoRecorrencia.duracaoPlano}">
                                        <a4j:support event="onblur"
                                                     reRender="panelRecorrencia, panelParcelaAnuidade, panelParcelasValorDiferente"
                                                     status="false"
                                                     action="#{PlanoControle.montarListaParcelasAdesao}"/>
                                    </h:inputText>
                                </h:panelGroup>

                                <h:outputText styleClass="tituloCampos tooltipster"
                                              value="Gerar anuidade com mesmo vencimento da parcela"
                                              title="Ao utilizar esta opção, no ato do lançamento do plano, já será gerado a parcela de anuidade com a mesma data de vencimento que a parcela definida ao lado direto.<br/>
                                                    Ex: Se for definido \"PARCELA 12\", após o lançamento do plano, será gerado uma parcela automaticamente,
                                                    referente à anuidade com a mesma data de vencimento da parcela 12 do contrato lançado."/>

                                <h:panelGroup>
                                    <h:selectBooleanCheckbox id="anuidadeNaParcela"
                                                             value="#{PlanoControle.planoVO.planoRecorrencia.anuidadeNaParcela}">
                                        <a4j:support event="onchange"
                                                     actionListener="#{PlanoControle.marcarAnuidadeNaParcela}"
                                                     reRender="panelRecorrencia, modalContratosRenovacaoAutomatica, panelParcelasValorDiferente"/>
                                    </h:selectBooleanCheckbox>

                                    <h:panelGroup
                                            rendered="#{PlanoControle.planoVO.planoRecorrencia.anuidadeNaParcela and not PlanoControle.planoVO.planoRecorrencia.parcelarAnuidade}">
                                        <h:selectOneMenu id="parcelaAnuidade"
                                                         value="#{PlanoControle.planoVO.planoRecorrencia.parcelaAnuidade}">
                                            <f:selectItems value="#{PlanoControle.listaParcelaAnuidadeRecorrencia}"/>
                                        </h:selectOneMenu>
                                    </h:panelGroup>

                                    <br/>

                                    <h:outputText
                                            rendered="#{PlanoControle.planoVO.regimeRecorrencia and PlanoControle.planoVO.planoRecorrencia.anuidadeNaParcela}"
                                            style="margin-left: 5px; line-height: 21px"
                                            styleClass="tituloCampos tooltipster" value="Parcelar anuidade"/>
                                    <h:panelGroup
                                            rendered="#{PlanoControle.planoVO.regimeRecorrencia and PlanoControle.planoVO.planoRecorrencia.anuidadeNaParcela}">
                                        <h:selectBooleanCheckbox id="parcelarAnuidade"
                                                                 value="#{PlanoControle.planoVO.planoRecorrencia.parcelarAnuidade}">
                                            <a4j:support event="onchange"
                                                         action="#{PlanoControle.selecionouParcelarAnuidade}"
                                                         reRender="panelRecorrencia, modalContratosRenovacaoAutomatica, panelParcelasValorDiferente"/>
                                        </h:selectBooleanCheckbox>

                                        <c:if test="${PlanoControle.planoVO.planoRecorrencia.parcelarAnuidade}">

                                            <br/>
                                            <br/>

                                            <h:panelGroup layout="block">

                                                <h:outputText style="vertical-align: initial;"
                                                              styleClass="tituloCampos" value="Valor da parcela:"/>

                                                <h:inputText id="planoAnuidadeParcelaValor" size="6" maxlength="10"
                                                             onblur="blurinput(this);"
                                                             style="margin-left: 5px; vertical-align: initial;"
                                                             onkeypress="return formatar_moeda(this,'.',',',event);"
                                                             onfocus="focusinput(this);"
                                                             styleClass="form"
                                                             value="#{PlanoControle.planoAnuidadeParcelaVO.valor}">
                                                    <f:converter converterId="FormatadorNumerico"/>
                                                </h:inputText>

                                                <h:outputText
                                                        style="margin-left: 10px; vertical-align: initial;"
                                                        styleClass="tituloCampos" value="Cobrar no mesmo dia da"/>
                                                <h:selectOneMenu id="listaParcelaAnuidade"
                                                                 value="#{PlanoControle.planoAnuidadeParcelaVO.parcela}"
                                                                 style="margin-left: 5px; vertical-align: initial;">
                                                    <f:selectItems
                                                            value="#{PlanoControle.listaParcelasAnuidadeParcela}"/>
                                                </h:selectOneMenu>

                                                <a4j:commandButton id="btnAdicionarParcelaAnuidade"
                                                                   action="#{PlanoControle.adicionarParcelaAnuidade}"
                                                                   oncomplete="#{PlanoControle.mensagemNotificar}"
                                                                   reRender="valorAnuidade, parcelasValorAlterado, panelRecorrencia, panelMensagem"
                                                                   value="Adicionar parcela anuidade"
                                                                   image="./imagens/botaoAdicionar.png"
                                                                   style="vertical-align: middle;"/>
                                            </h:panelGroup>

                                        </c:if>

                                        <h:dataTable
                                                rendered="#{PlanoControle.planoVO.planoRecorrencia.parcelarAnuidade and fn:length(PlanoControle.planoVO.planoRecorrencia.parcelasAnuidade) > 0}"
                                                id="parcelasAnuidade" style="width: 100%; padding-top: 10px;"
                                                columnClasses="colunaCentralizada,colunaDireita,colunaDireita,colunaCentralizada"
                                                value="#{PlanoControle.planoVO.planoRecorrencia.parcelasAnuidade}"
                                                var="parcela">

                                            <h:column>
                                                <f:facet name="header">
                                                    <h:outputText value="Parcela"/>
                                                </f:facet>
                                                <h:outputText value="#{parcela.numero}"/>
                                            </h:column>

                                            <h:column>
                                                <f:facet name="header">
                                                    <h:outputText value="Valor"/>
                                                </f:facet>
                                                <h:outputText value="#{parcela.valorApresentar}"/>
                                            </h:column>

                                            <h:column>
                                                <f:facet name="header">
                                                    <h:outputText value="Data Cobrança"/>
                                                </f:facet>
                                                <h:outputText value="#{parcela.parcelaApresentar}"/>
                                            </h:column>

                                            <h:column>
                                                <f:facet name="header">
                                                    <h:outputText value="Excluir"/>
                                                </f:facet>
                                                <a4j:commandButton
                                                        reRender="valorAnuidade, parcelasValorAlterado, panelRecorrencia, panelMensagem, panelParcelasValorDiferente"
                                                        ajaxSingle="true"
                                                        immediate="true"
                                                        action="#{PlanoControle.removerParcelaAnuidade}"
                                                        value="Excluir"
                                                        image="./imagens/botaoRemover.png"
                                                        accesskey="7"
                                                        styleClass="botoes"/>
                                            </h:column>
                                        </h:dataTable>
                                    </h:panelGroup>
                                </h:panelGroup>

                                <h:outputText styleClass="tituloCampos" value="Parcelas com valor diferente"/>
                                <h:panelGroup>
                                    <h:panelGroup>
                                        <h:selectBooleanCheckbox id="valordiferente"
                                                                 value="#{PlanoControle.planoVO.planoRecorrencia.gerarParcelasValorDiferente}">
                                            <a4j:support event="onchange"
                                                         reRender="panelRecorrencia, modalContratosRenovacaoAutomatica, panelParcelasValorDiferente"/>
                                        </h:selectBooleanCheckbox>
                                        <h:outputText styleClass="classInfCadUsuario" value="Definir parcelas com valor diferente (matrícula ou rematrícula)"/>
                                    </h:panelGroup>
                                    <h:panelGroup rendered="#{PlanoControle.planoVO.planoRecorrencia.gerarParcelasValorDiferente}">
                                        <h:selectBooleanCheckbox id="valordiferenteRenovacao"
                                                                 value="#{PlanoControle.planoVO.planoRecorrencia.gerarParcelasValorDiferenteRenovacao}">
                                            <a4j:support event="onchange"
                                                         reRender="panelRecorrencia, modalContratosRenovacaoAutomatica, panelParcelasValorDiferente"/>
                                        </h:selectBooleanCheckbox>
                                        <h:outputText styleClass="classInfCadUsuario" value="Utilizar também na renovação"/>
                                    </h:panelGroup>

                                    <br/>
                                    <br/>

                                    <h:panelGroup layout="block"
                                                  rendered="#{PlanoControle.planoVO.planoRecorrencia.gerarParcelasValorDiferente}">
                                        <h:selectOneMenu id="listaParcelaValorDiferente"
                                                         value="#{PlanoControle.parcelaValorAlterado.numero}">
                                            <f:selectItems value="#{PlanoControle.listaParcelasParaAlteracaoValor}"/>
                                        </h:selectOneMenu>
                                        <h:outputText
                                                style="margin-left: 15px; margin-right: 5px; vertical-align: initial;"
                                                styleClass="tituloCampos" value="Valor"/>
                                        <h:inputText id="valorparcela"
                                                     value="#{PlanoControle.parcelaValorAlterado.valor}"
                                                     onkeypress="return formatar_moeda(this,'.',',',event); "
                                                     onblur="blurinput(this);"
                                                     onfocus="focusinput(this);">
                                            <f:converter converterId="FormatadorNumerico"/>
                                        </h:inputText>

                                        <a4j:commandButton action="#{PlanoControle.adicionarParcelaValorAlterado}"
                                                           reRender="parcelasValorAlterado, panelRecorrencia, panelMensagem"
                                                           value="Adicionar parcela com valor diferente"
                                                           id="btnvalordiferente"
                                                           image="./imagens/botaoAdicionar.png"
                                                           style="vertical-align: middle;"/>
                                    </h:panelGroup>

                                    <h:dataTable
                                            rendered="#{(PlanoControle.planoVO.planoRecorrencia.gerarParcelasValorDiferente or PlanoControle.planoVO.planoRecorrencia.gerarParcelasValorDiferenteRenovacao) and fn:length(PlanoControle.planoVO.planoRecorrencia.parcelas) > 0}"
                                            id="parcelasValorAlterado"
                                            value="#{PlanoControle.planoVO.planoRecorrencia.parcelas}"
                                            var="parcela">
                                        <h:column>
                                            <f:facet name="header">
                                                <h:outputText value="Parcela"/>
                                            </f:facet>
                                            <h:outputText value="#{parcela.numero}"/>
                                        </h:column>
                                        <h:column>
                                            <f:facet name="header">
                                                <h:outputText value="Valor"/>
                                            </f:facet>
                                            <h:outputText value="#{parcela.valorFormatado}"/>
                                        </h:column>
                                        <h:column>
                                            <a4j:commandButton
                                                    reRender="parcelasValorAlterado, panelRecorrencia, panelMensagem, panelParcelasValorDiferente"
                                                    ajaxSingle="true"
                                                    immediate="true"
                                                    action="#{PlanoControle.removerParcelaValorAlterado}"
                                                    value="Excluir"
                                                    image="./imagens/botaoRemover.png"
                                                    accesskey="7"
                                                    styleClass="botoes"/>
                                        </h:column>
                                    </h:dataTable>
                                </h:panelGroup>

                                <h:outputText styleClass="tituloCampos"
                                              value="#{msg_aplic.prt_PlanoRecorrencia_qtdDiasAposVencimentoCancelamentoAutomatico}"
                                              rendered="#{!PlanoControle.planoVO.planoPersonal}"/>
                                <h:outputText styleClass="tituloCampos"
                                              value="Qtde Dias após vencimento da parcela para Cancelamento Automático das parcelas do personal"
                                              rendered="#{PlanoControle.planoVO.planoPersonal}"/>
                                <h:panelGroup>
                                    <h:inputText id="qtdDiasAposVencCancelAutomatico" onblur="blurinput(this);" size="7"
                                                 maxlength="6" onfocus="focusinput(this);"
                                                 styleClass="form"
                                                 value="#{PlanoControle.planoVO.planoRecorrencia.qtdDiasAposVencimentoCancelamentoAutomatico}">
                                        <rich:toolTip followMouse="true" direction="top-right"
                                                      style="width:200px; height:120px; " showDelay="500">
                                            <h:outputText styleClass="tituloCampos"
                                                          value="#{msg_aplic.prt_PlanoRecorrencia_qtdDiasAposVencimenstoCancelamentoAutomaticoToolTip}"/>
                                        </rich:toolTip>
                                    </h:inputText>
                                    <a4j:commandButton id="ajustarDiasCancelamentoAutom"
                                                       style="margin-left: 5px;"
                                                       value="Ajustar Todos Contratos Ativos"
                                                       action="#{PlanoControle.consultarContratosDiasVencimentoCancelamentoAutomatico}"
                                                       reRender="form,modalContratosCancelamentoAutoma"
                                                       title="Serão alterados todos os contratos ativos desse plano com data final ajustada maior ou igual a data atual"
                                                       oncomplete="#{PlanoControle.onCompleteGravar}"/>
                                </h:panelGroup>

                                <h:outputText styleClass="tituloCampos"
                                              value="#{msg_aplic.prt_PlanoRecorrencia_renovavelAutomaticamente}"
                                              rendered="#{!PlanoControle.planoVO.planoPersonal}"/>
                                <h:panelGroup rendered="#{!PlanoControle.planoVO.planoPersonal}">
                                    <h:selectBooleanCheckbox id="renovavelAutomaticamente"
                                                             styleClass="campos"
                                                             value="#{PlanoControle.planoVO.planoRecorrencia.renovavelAutomaticamente}">
                                        <a4j:support event="onchange"
                                                     action="#{PlanoControle.consultarContratosRenovacaoAutomatica}"
                                                     reRender="panelRecorrencia, modalContratosRenovacaoAutomatica, panelParcelasValorDiferente"/>
                                    </h:selectBooleanCheckbox>
                                    <a4j:commandLink style="color: red" id="idLinkContratosRenovacaoAutomatic"
                                                     onclick="Richfaces.showModalPanel('modalContratosRenovacaoAutomatica');"
                                                     rendered="#{PlanoControle.textoLinkRenovacaoAutomatica != null}"
                                                     value="#{PlanoControle.textoLinkRenovacaoAutomatica}"/>
                                </h:panelGroup>

                                <h:outputText styleClass="tituloCampos tooltipster"
                                              rendered="#{PlanoControle.planoVO.planoRecorrencia.renovavelAutomaticamente}"
                                              value="Permitir que a renovação automática renove o plano com desconto"
                                              title="Ao selecionar esta opção, a renovação automática vai renovar os planos com os respectivos descontos (caso tenha desconto) que foram dados no ato da compra do plano"/>
                                <h:selectBooleanCheckbox
                                        rendered="#{PlanoControle.planoVO.planoRecorrencia.renovavelAutomaticamente}"
                                        id="renovarComDescontoRecorrencia"
                                        value="#{PlanoControle.planoVO.renovarAutomaticamenteComDesconto}"
                                        styleClass="tituloCampos tooltipster"
                                        title="Ao selecionar esta opção, a renovação automática vai renovar os planos com os respectivos descontos (caso tenha desconto) que foram dados no ato da compra do plano"/>


                                <h:outputText
                                        rendered="#{PlanoControle.planoVO.planoRecorrencia.renovavelAutomaticamente}"
                                        styleClass="tituloCampos tooltipster"
                                        value="#{msg_aplic.prt_PlanoRecorrencia_renovarProdutoObrigatorio}"
                                        title="Se marcado, todos os produtos sugeridos que estão definidos como obrigatório, serão adicionados ao contrato renovado, <b>exceto</b> produtos do tipo (MA,RN,RE,TA,TD)."/>
                                <h:selectBooleanCheckbox
                                        rendered="#{PlanoControle.planoVO.planoRecorrencia.renovavelAutomaticamente}"
                                        styleClass="campos tooltipster"
                                        title="Se marcado, todos os produtos sugeridos que estão definidos como obrigatório, serão adicionados ao contrato renovado, <b>exceto</b> produtos do tipo (MA,RN,RE,TA,TD)."
                                        value="#{PlanoControle.planoVO.renovarProdutoObrigatorio}">
                                </h:selectBooleanCheckbox>

                                <h:outputText rendered="#{PlanoControle.planoVO.planoRecorrencia.renovavelAutomaticamente}"
                                              styleClass="tituloCampos"
                                              value="#{msg_aplic.prt_PlanoRecorrencia_renovarAutomaticamenteUtilizandoValorBaseContrato}"/>
                                <h:selectBooleanCheckbox
                                        rendered="#{PlanoControle.planoVO.planoRecorrencia.renovavelAutomaticamente}"
                                        styleClass="campos"
                                        title="Se marcado todos os contratos serão renovados com o valor que foi negociado."
                                        value="#{PlanoControle.planoVO.renovarAutomaticamenteUtilizandoValorBaseContrato}">
                                </h:selectBooleanCheckbox>

                                <h:outputText
                                        rendered="#{PlanoControle.planoVO.planoRecorrencia.renovavelAutomaticamente && PlanoControle.planoVO.planoRecorrencia.duracaoPlano > 11}"
                                        styleClass="tituloCampos tooltipster"
                                        value="Renovar Anuidade Automaticamente"
                                        title="Se marcado esta opção e a anuidade definida no plano estiver como obrigatório, então será adicionada ao contrato renovado de acordo com o dia e mês de vencimento configurado. Caso está configuração não esteja habilitada, o sistema irá lançar a anuidade configurada neste plano no dia de seu vencimento."/>
                                <h:selectBooleanCheckbox
                                        rendered="#{PlanoControle.planoVO.planoRecorrencia.renovavelAutomaticamente && PlanoControle.planoVO.planoRecorrencia.duracaoPlano > 11}"
                                        styleClass="campos tooltipster"
                                        title="Se marcado esta opção e a anuidade definida no plano estiver como obrigatório, então será adicionada ao contrato renovado de acordo com o dia e mês de vencimento configurado. Caso está configuração não esteja habilitada, o sistema irá lançar a anuidade configurada neste plano no dia de seu vencimento."
                                        value="#{PlanoControle.planoVO.renovarAnuidadeAutomaticamente}"/>


                                <h:outputText styleClass="tituloCampos"
                                              value="#{msg_aplic.prt_PlanoRecorrencia_naorenovarparcelaaberta}"
                                              rendered="#{!PlanoControle.planoVO.planoPersonal}"/>
                                <h:selectBooleanCheckbox id="naoRenovarParcelaAberta"
                                                         styleClass="campos"
                                                         value="#{PlanoControle.planoVO.planoRecorrencia.naoRenovarParcelaVencida}"
                                                         rendered="#{!PlanoControle.planoVO.planoPersonal}"/>

                                <h:outputText styleClass="tituloCampos"
                                              value="#{msg_aplic.prt_PlanoRecorrencia_permitecobrarAdesaoSeparada}"/>
                                <h:selectBooleanCheckbox id="cobrarAdesaoSeparada" styleClass="campos"
                                                         value="#{PlanoControle.planoVO.cobrarAdesaoSeparada}">
                                    <a4j:support event="onclick" reRender="panelRecorrencia"/>
                                </h:selectBooleanCheckbox>

                                <h:outputText styleClass="tituloCampos"
                                              value="#{msg_aplic.prt_PlanoRecorrencia_Nrvezesparcelar}"
                                              rendered="#{PlanoControle.planoVO.cobrarAdesaoSeparada and not PlanoControle.planoVO.site}"/>
                                <h:selectOneMenu value="#{PlanoControle.planoVO.nrVezesParcelarAdesao}"
                                                 rendered="#{PlanoControle.planoVO.cobrarAdesaoSeparada  and not PlanoControle.planoVO.site}">
                                    <f:selectItems value="#{PlanoControle.listaVezesParcelarAdesao}"/>
                                </h:selectOneMenu>


                                <h:outputText rendered="#{!PlanoControle.planoVO.planoPersonal}"
                                              styleClass="tituloCampos"
                                              value="#{msg_aplic.prt_Plano_permitecobrarProdutoSeparado}"/>
                                <h:selectBooleanCheckbox id="cobrarProdutoSeparado"
                                                         rendered="#{!PlanoControle.planoVO.planoPersonal}"
                                                         styleClass="campos"
                                                         value="#{PlanoControle.planoVO.cobrarProdutoSeparado}">
                                    <a4j:support event="onclick" reRender="panelRecorrencia"/>
                                </h:selectBooleanCheckbox>

                                <h:outputText styleClass="tituloCampos"
                                              value="#{msg_aplic.prt_Plano_NrVezesParcelarProduto}"
                                              rendered="#{PlanoControle.planoVO.cobrarProdutoSeparado and not PlanoControle.planoVO.site}"/>
                                <h:selectOneMenu value="#{PlanoControle.planoVO.nrVezesParcelarProduto}"
                                                 rendered="#{PlanoControle.planoVO.cobrarProdutoSeparado and not PlanoControle.planoVO.site}">
                                    <f:selectItems value="#{PlanoControle.listaVezesParcelarAdesao}"/>
                                </h:selectOneMenu>

                                <h:outputText styleClass="tituloCampos"
                                              rendered="#{!PlanoControle.planoVO.planoPersonal}"
                                              value="#{msg_aplic.prt_PlanoRecorrencia_naoCobrarAnuidadeProporcional}"/>
                                <h:selectBooleanCheckbox id="naoCobrarAnuidadeProporcional"
                                                         rendered="#{!PlanoControle.planoVO.planoPersonal}"
                                                         disabled="#{PlanoControle.planoVO.planoRecorrencia.anuidadeNaParcela}"
                                                         styleClass="campos"
                                                         value="#{PlanoControle.planoVO.planoRecorrencia.naoCobrarAnuidadeProporcional}">
                                    <a4j:support event="onchange" reRender="panelRecorrencia"/>
                                </h:selectBooleanCheckbox>

                                <h:outputText styleClass="tituloCampos tooltipster"
                                              value="Habilitar método de cancelamento verificando próxima parcela em aberto"
                                              title="Habilite a configuração para trabalhar com um método de cancelamento específico com a possibilidade de cobrar a próxima parcela em aberto e a anuidade no ato do cancelamento."/>
                                <h:panelGroup layout="block">

                                    <h:selectBooleanCheckbox id="habilitaCancelamento" styleClass="campos tooltipster"
                                                             value="#{PlanoControle.planoVO.planoRecorrencia.cancelamentoProporcional}">
                                        <a4j:support event="onclick" reRender="panelRecorrencia"/>
                                    </h:selectBooleanCheckbox>

                                    <a4j:commandButton id="consultarContratosCancelamentoPropocional"
                                                       style="margin-left: 5px;"
                                                       value="#{PlanoControle.labelAjustarContratosHabilitandoMetodo}"
                                                       action="#{PlanoControle.consultarContratosCancelamentoPropocional}"
                                                       reRender="form,modalContratosCancelamentoProporcional"
                                                       title="Serão alterados todos os contratos ativos desse plano com data final ajustada maior ou igual a data atual"
                                                       oncomplete="#{PlanoControle.onCompleteGravar}"/>
                                </h:panelGroup>

                                <c:if test="${PlanoControle.planoVO.planoRecorrencia.cancelamentoProporcional eq true}">
                                    <h:outputText styleClass="tituloCampos tooltipster"
                                                  value="Aplicar o método de cancelamento apenas para contratos Renovados"
                                                  title="Com essa configuração marcada, apenas os contratos renovados que serão passíveis desta configuração."/>
                                    <h:selectBooleanCheckbox id="habilitaCancelamentoRenovacao"
                                                             styleClass="campos tooltipster"
                                                             value="#{PlanoControle.planoVO.planoRecorrencia.cancelamentoProporcionalSomenteRenovacao}">
                                        <a4j:support event="onclick" reRender="panelRecorrencia"/>
                                    </h:selectBooleanCheckbox>
                                </c:if>


                                <h:outputText styleClass="tituloCampos tooltipster"
                                              value="Quantidade de dias avisar com antecedência para não cobrar próxima parcela"
                                              rendered="#{PlanoControle.planoVO.planoRecorrencia.cancelamentoProporcional}"
                                              title="Informe a quantidade de dias que o sistema irá considerar para cobrar a próxima parcela em aberto do cliente no cancelamento.<br/>
                                        Ex: Se informado 20, o sistema faz um cálculo de dias entre a data de cancelamento e o vencimento da próxima parcela do cliente. <br/>
                                        Se o resultado for <= 20 então ele terá que pagar a próxima parcela em aberto, se for >=20 ele não terá que <br/>pagar a próxima parcela por ter avisado com a antecedência necessária."/>
                                <h:panelGroup
                                        rendered="#{PlanoControle.planoVO.planoRecorrencia.cancelamentoProporcional}">
                                    <h:inputText id="qtdDiasCobrarProximaParcela" size="5" maxlength="5"
                                                 onfocus="focusinput(this);" styleClass="form"
                                                 value="#{PlanoControle.planoVO.planoRecorrencia.qtdDiasCobrarProximaParcela}"/>
                                </h:panelGroup>

                                <h:outputText styleClass="tituloCampos tooltipster"
                                              value="Quantidade de dias até a data de vencimento do contrato para cobrar valor total da anuidade"
                                              title="Informe a quantidade de dias que o sistema irá considerar para cobrar a anuidade integral do cliente no cancelamento.<br/>
                                                Ex: Se informado 20, o sistema faz um cálculo de dias entre a data de cancelamento e o vencimento do contrato do aluno. Se o resultado for <= 20 então ele terá que <br/>
                                                pagar a anuidade no valor integral, se for >=20 ele terá que pagar a anuidade proporcional aos dias utilizados."
                                              rendered="#{PlanoControle.planoVO.planoRecorrencia.cancelamentoProporcional}"/>
                                <h:panelGroup
                                        rendered="#{PlanoControle.planoVO.planoRecorrencia.cancelamentoProporcional}">
                                    <h:inputText id="qtdDiasCobrarAnuidadeTotal" size="5" maxlength="5"
                                                 onfocus="focusinput(this);" styleClass="form"
                                                 value="#{PlanoControle.planoVO.planoRecorrencia.qtdDiasCobrarAnuidadeTotal}"/>
                                </h:panelGroup>
                            </h:panelGrid>
                        </h:panelGrid>
                    </rich:tab>

                    <rich:tab id="abaExcecaoModalidade" label="Exceções"
                              rendered="#{!PlanoControle.planoVO.regimeRecorrencia && !PlanoControle.planoVO.vendaCreditoTreino}"
                              action="#{PlanoControle.iniciarPlanoExcecao}">
                        <h:panelGrid id="panelExcecaoModalidade" columns="1" width="100%" headerClass="subordinado"
                                     columnClasses="colunaCentralizada">
                            <f:facet name="header">
                                <h:outputText value="#{msg_aplic.prt_PlanoExcecao_tituloForm}"/>
                            </f:facet>
                            <h:panelGrid columns="2" rowClasses="linhaImpar, linhaPar"
                                         columnClasses="classEsquerda, classDireita" width="100%"
                                         id="dadosExcecaoModalidade">
                                <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_PlanoExcecao_pacote}"/>
                                <h:panelGroup>
                                    <h:selectOneMenu id="pacoteExcecao"
                                                     value="#{PlanoControle.planoExcecaoVOSelecionado.pacote.codigo}">
                                        <f:selectItems value="#{PlanoControle.listaSelectItemComposicao}"/>
                                    </h:selectOneMenu>
                                </h:panelGroup>
                                <h:outputText styleClass="tituloCampos"
                                              value="#{msg_aplic.prt_PlanoExcecao_modalidade}"/>
                                <h:panelGroup>
                                    <h:selectOneMenu id="modalidadeExcecao"
                                                     value="#{PlanoControle.planoExcecaoVOSelecionado.modalidade.codigo}">
                                        <f:selectItems value="#{PlanoControle.listaSelectItemModalidadeExcecao}"/>
                                    </h:selectOneMenu>
                                </h:panelGroup>
                                <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_PlanoExcecao_horario}"/>
                                <h:panelGroup>
                                    <h:selectOneMenu id="horarioExcecao"
                                                     value="#{PlanoControle.planoExcecaoVOSelecionado.horario.codigo}">
                                        <f:selectItems value="#{PlanoControle.listaSelectItemHorarioExcecao}"/>
                                    </h:selectOneMenu>
                                </h:panelGroup>
                                <h:outputText styleClass="tituloCampos"
                                              value="#{msg_aplic.prt_PlanoExcecao_vezessemana}"/>
                                <h:inputText id="vezesSemanaModExcecao" size="4" styleClass="form"
                                             onblur="blurinput(this);" onfocus="focusinput(this);" maxlength="2"
                                             value="#{PlanoControle.planoExcecaoVOSelecionado.vezesSemana}">
                                </h:inputText>
                                <h:outputText styleClass="tituloCampos" value="*#{msg_aplic.prt_PlanoExcecao_duracao}"/>
                                <h:inputText id="duracaoModExcecao" size="4" styleClass="form"
                                             onblur="blurinput(this);" onfocus="focusinput(this);" maxlength="2"
                                             value="#{PlanoControle.planoExcecaoVOSelecionado.duracao}">
                                </h:inputText>

                                <h:outputText styleClass="tituloCampos"
                                              value="*Valor Mensal #{MovPagamentoControle.empresaLogado.moeda}"/>
                                <h:inputText id="valorModExcecao" size="8" styleClass="form"
                                             maxlength="8"
                                             value="#{PlanoControle.planoExcecaoVOSelecionado.valor}">
                                    <f:converter converterId="FormatadorNumerico"/>
                                </h:inputText>

                                <script>
                                    VMasker(document.getElementById("form:duracaoModExcecao")).maskMoney({
                                        precision: 0,
                                        separator: ',',
                                        delimiter: '.',
                                        zeroCents: false
                                    });
                                    VMasker(document.getElementById("form:vezesSemanaModExcecao")).maskMoney({
                                        precision: 0,
                                        separator: ',',
                                        delimiter: '.',
                                        zeroCents: false
                                    });
                                    VMasker(document.getElementById("form:valorModExcecao")).maskMoney({
                                        precision: 2,
                                        separator: ',',
                                        delimiter: '.',
                                        zeroCents: false
                                    });
                                </script>

                            </h:panelGrid>

                            <a4j:commandButton id="addPlanoExcecao" action="#{PlanoControle.adicionarPlanoExcecao}"
                                               reRender="panelExcecaoModalidade, panelMensagem"
                                               focus="form:modalidadeExcecao"
                                               value="#{msg_bt.btn_adicionar}" image="./imagens/botaoAdicionar.png"
                                               accesskey="8" styleClass="botoes"/>

                            <h:panelGrid columns="1" width="100%" styleClass="tabFormSubordinada">
                                <rich:dataTable id="listaPlanoExcecao" width="100%" headerClass="subordinado"
                                                styleClass="tabFormSubordinada"
                                                var="planoExcecao"
                                                rowClasses="linhaImpar, linhaPar" columnClasses="colunaAlinhamento"
                                                value="#{PlanoControle.planoVO.planoExcecaoVOs}">
                                    <rich:column>
                                        <f:facet name="header">
                                            <h:outputText value="#{msg_aplic.prt_PlanoExcecao_pacote}"/>
                                        </f:facet>
                                        <h:outputText rendered="#{planoExcecao.pacote.codigo > 0}"
                                                      value="#{planoExcecao.pacote.codigo} - "/>
                                        <h:outputText value="#{planoExcecao.pacote.descricao}"/>
                                    </rich:column>
                                    <rich:column>
                                        <f:facet name="header">
                                            <h:outputText value="#{msg_aplic.prt_PlanoExcecao_modalidade}"/>
                                        </f:facet>
                                        <h:outputText value="#{planoExcecao.modalidade.nome}"/>
                                    </rich:column>
                                    <rich:column>
                                        <f:facet name="header">
                                            <h:outputText value="#{msg_aplic.prt_PlanoExcecao_horario}"/>
                                        </f:facet>
                                        <h:outputText value="#{planoExcecao.horario.descricao}"/>
                                    </rich:column>
                                    <rich:column>
                                        <f:facet name="header">
                                            <h:outputText value="#{msg_aplic.prt_PlanoExcecao_vezessemana}"/>
                                        </f:facet>
                                        <h:outputText value="#{planoExcecao.vezesSemana}"/>
                                    </rich:column>
                                    <rich:column>
                                        <f:facet name="header">
                                            <h:outputText value="#{msg_aplic.prt_PlanoExcecao_duracao}"/>
                                        </f:facet>
                                        <h:outputText value="#{planoExcecao.duracao}"/>
                                    </rich:column>
                                    <rich:column>
                                        <f:facet name="header">
                                            <h:outputText
                                                    value="Valor Mensal #{MovPagamentoControle.empresaLogado.moeda}"/>
                                        </f:facet>
                                        <h:outputText value="#{planoExcecao.valor}">
                                            <f:converter converterId="FormatadorNumerico"/>
                                        </h:outputText>
                                    </rich:column>

                                    <rich:column>
                                        <f:facet name="header">
                                            <h:outputText value="#{msg_bt.btn_opcoes}"/>
                                        </f:facet>
                                        <h:panelGroup>
                                            <a4j:commandButton id="editarPlanoExcecao"
                                                               reRender="panelExcecaoModalidade, panelMensagem"
                                                               ajaxSingle="true"
                                                               action="#{PlanoControle.editarPlanoExcecao}"
                                                               value="#{msg_bt.btn_editar}"
                                                               image="./imagens/botaoEditar.png" accesskey="6"
                                                               styleClass="botoes"/>
                                            <h:outputText value="    "/>
                                            <a4j:commandButton id="removerPlanoExcecao"
                                                               reRender="panelExcecaoModalidade, panelMensagem"
                                                               ajaxSingle="true"
                                                               action="#{PlanoControle.removerPlanoExcecao}"
                                                               value="#{msg_bt.btn_excluir}"
                                                               image="./imagens/botaoRemover.png" accesskey="7"
                                                               styleClass="botoes"/>
                                        </h:panelGroup>
                                    </rich:column>
                                </rich:dataTable>
                            </h:panelGrid>
                        </h:panelGrid>
                    </rich:tab>

                    <rich:tab id="tabEmpresasPlano"
                              rendered="#{PlanoControle.planoVO.apresentarListaPlanoEmpresas && ((PlanoControle.planoVO.apresentaVendaRapida && LoginControle.permissaoAcessoMenuVO.vendaRapida) || PlanoControle.configuracaoSistema.controleAcessoMultiplasEmpresasPorPlano)}"
                              label="Empresas">

                        <div style="margin-bottom: 15px;">
                            <div class="container_texto_dicas_bi_titulo_font_topo">
                                <span class="texto-upper texto-size-14 texto-bold texto-cor-cinza">Observações:</span>
                            </div>
                            <div style="width: auto; color: #777777;"
                                 class="container_texto_dicas_bi_titulo_font_baixo texto-cor-cinza texto-normal">
                                Este cadastro é referente ao controle de acesso dos alunos por empresa e quais empresas
                                o plano pode ser vendido.<br/>
                                Os alunos que tiverem esse plano terão acesso a todas as unidades selecionadas. <br/>
                                Se "Permitir Acesso somente na empresa que vender o contrato" estiver desmarcado e
                                nenhuma empresa estiver marcada na coluna "Permitir acesso",
                                é considerado que o aluno terá acesso a todas as unidades. <br/>
                                Este plano poderá ser vendido em qualquer unidade onde "Permitir venda" esteja marcado.
                                Se nenhuma unidade estiver marcada com "Permitir venda", é considerado que todas as
                                unidades poderão vender este plano.
                            </div>
                        </div>

                        <div style="margin-bottom: 15px;">
                            <a4j:jsFunction reRender="tabelaEmpresas" name="rerenderTabelaEmpresas"
                                            oncomplete="processarCheckTodasEmpresas()"/>
                            <h:outputText value="Permitir Acesso somente na empresa que vender o contrato: "
                                          styleClass="texto-font texto-size-14-real texto-cor-cinza texto-bold"/>
                            <h:selectBooleanCheckbox
                                    value="#{PlanoControle.planoVO.permitirAcessoSomenteNaEmpresaVendeuContrato}"
                                    id="permitirAcessoSomenteNaEmpresaVendeuContrato"
                                    onclick="rerenderTabelaEmpresas()"/>
                        </div>

                        <h:inputHidden id="atualizarEmpresas" value="#{PlanoControle.planoVO.atualizarEmpresas}"/>
                        <rich:dataTable value="#{PlanoControle.planoVO.empresas}"
                                        var="planoEmpresa"
                                        id="tabelaEmpresas">
                            <rich:column
                                    visible="#{!PlanoControle.planoVO.permitirAcessoSomenteNaEmpresaVendeuContrato}"
                                    width="5%"
                                    style="text-align: center;"
                                    sortBy="#{planoEmpresa.acesso}">
                                <f:facet name="header">
                                    <h:panelGroup>
                                        <h:outputText value="Permitir acesso"
                                                      styleClass="texto-font texto-size-14-real texto-cor-cinza texto-bold"/>
                                        <br/>
                                        <h:selectBooleanCheckbox id="checkAcessoTodasEmpresas"
                                                                 value="#{PlanoControle.checkTodasAcessoEmpresas}"
                                                                 onclick="checkAcessoTodasEmpresas();atualizarEmpresas()"/>
                                    </h:panelGroup>
                                </f:facet>
                                <h:selectBooleanCheckbox id="checkAcessoEmpresa"
                                                         value="#{planoEmpresa.acesso}"
                                                         onclick="atualizarEmpresas()"/>
                            </rich:column>
                            <rich:column width="5%" style="text-align: center;" sortBy="#{planoEmpresa.venda}">
                                <f:facet name="header">
                                    <h:panelGroup>
                                        <h:outputText value="Permitir venda"
                                                      styleClass="texto-font texto-size-14-real texto-cor-cinza texto-bold"/>
                                        <br/>
                                        <h:selectBooleanCheckbox id="checkVendaTodasEmpresas"
                                                                 value="#{PlanoControle.checkTodasVendaEmpresas}"
                                                                 onclick="checkVendaTodasEmpresas();atualizarEmpresas()"/>
                                    </h:panelGroup>
                                </f:facet>
                                <h:selectBooleanCheckbox id="checkVendaEmpresa"
                                                         value="#{planoEmpresa.venda}"
                                                         onclick="atualizarEmpresas()"/>
                            </rich:column>
                            <rich:column width="40%" sortBy="#{planoEmpresa.empresa.nome}"
                                         filterBy="#{planoEmpresa.empresa.nome}"
                                         style="text-align: center">
                                <f:facet name="header">
                                    <h:outputText styleClass="texto-font texto-size-14-real texto-cor-cinza texto-bold"
                                                  value="Empresa"/>
                                </f:facet>
                                <h:outputText styleClass="texto-cor-cinza texto-size-16 texto-font"
                                              value="#{planoEmpresa.empresa.nome}"/>
                            </rich:column>
                            <c:if test="${PlanoControle.planoVO.regimeRecorrencia}">
                                <rich:column sortBy="#{planoEmpresa.valorMensal}" style="text-align: center">
                                    <f:facet name="header">
                                        <h:outputText
                                                styleClass="texto-font texto-size-14-real texto-cor-cinza texto-bold"
                                                value="Mensalidade"/>
                                    </f:facet>
                                    <h:inputText id="vlMensalEmpresa" size="6" maxlength="10"
                                                 onblur="blurinput(this);"
                                                 onkeypress="return formatar_moeda(this,'.',',',event);"
                                                 onfocus="focusinput(this);" styleClass="form"
                                                 value="#{planoEmpresa.valorMensal}">
                                        <f:converter converterId="FormatadorNumerico"/>
                                    </h:inputText>
                                </rich:column>
                                <rich:column sortBy="#{planoEmpresa.taxaAdesao}" style="text-align: center">
                                    <f:facet name="header">
                                        <h:outputText
                                                styleClass="texto-font texto-size-14-real texto-cor-cinza texto-bold"
                                                value="Adesão"/>
                                    </f:facet>
                                    <h:inputText id="vlAdesaoEmpresa" size="6" maxlength="10"
                                                 onblur="blurinput(this);"
                                                 onkeypress="return formatar_moeda(this,'.',',',event);"
                                                 onfocus="focusinput(this);" styleClass="form"
                                                 value="#{planoEmpresa.taxaAdesao}">
                                        <f:converter converterId="FormatadorNumerico"/>
                                    </h:inputText>
                                </rich:column>
                                <rich:column sortBy="#{planoEmpresa.valorAnuidade}" style="text-align: center">
                                    <f:facet name="header">
                                        <h:outputText
                                                styleClass="texto-font texto-size-14-real texto-cor-cinza texto-bold"
                                                value="Anuidade"/>
                                    </f:facet>
                                    <h:inputText id="vlAnuidadeEmpresa" size="6" maxlength="10"
                                                 onblur="blurinput(this);"
                                                 onkeypress="return formatar_moeda(this,'.',',',event);"
                                                 onfocus="focusinput(this);" styleClass="form"
                                                 value="#{planoEmpresa.valorAnuidade}">
                                        <f:converter converterId="FormatadorNumerico"/>
                                    </h:inputText>
                                </rich:column>
                                <rich:column sortBy="#{planoEmpresa.percentualMultaCancelamento}"
                                             style="text-align: center">
                                    <f:facet name="header">
                                        <h:outputText
                                                styleClass="texto-font texto-size-14-real texto-cor-cinza texto-bold"
                                                value="% Multa"/>
                                    </f:facet>
                                    <h:inputText id="vlPercMultaEmpresa" size="6" maxlength="10"
                                                 onblur="blurinput(this);"
                                                 onkeypress="return formatar_moeda(this,'.',',',event);"
                                                 onfocus="focusinput(this);" styleClass="form"
                                                 value="#{planoEmpresa.percentualMultaCancelamento}">
                                        <f:converter converterId="FormatadorNumerico"/>
                                    </h:inputText>
                                </rich:column>
                                <rich:column sortBy="#{planoEmpresa.modeloContrato}" style="text-align: center">
                                    <f:facet name="header">
                                        <h:outputText
                                                styleClass="texto-font texto-size-14-real texto-cor-cinza texto-bold"
                                                value="Modelo de contrato"/>
                                    </f:facet>
                                    <h:selectOneMenu id="planoTextoPadrao" onblur="blurinput(this);"
                                                     onfocus="focusinput(this);" styleClass="form"
                                                     value="#{planoEmpresa.modeloContrato.codigo}">
                                        <f:selectItems value="#{PlanoControle.listaSelectItemPlanoTextoPadrao}"/>
                                    </h:selectOneMenu>
                                </rich:column>

                            </c:if>
                            <c:if test="${!PlanoControle.planoVO.regimeRecorrencia}">
                                <rich:column width="5%" sortBy="#{planoEmpresa.empresa.estadoSigla}"
                                             filterBy="#{planoEmpresa.empresa.estadoSigla}">
                                    <f:facet name="header">
                                        <h:outputText
                                                styleClass="texto-font texto-size-14-real texto-cor-cinza texto-bold"
                                                value="Estado"/>
                                    </f:facet>
                                    <h:outputText styleClass="texto-cor-cinza texto-size-16 texto-font"
                                                  value="#{planoEmpresa.empresa.estadoSigla}"/>
                                </rich:column>
                                <rich:column width="10%" sortBy="#{planoEmpresa.empresa.cidadeNome}"
                                             filterBy="#{planoEmpresa.empresa.cidadeNome}">
                                    <f:facet name="header">
                                        <h:outputText
                                                styleClass="texto-font texto-size-14-real texto-cor-cinza texto-bold"
                                                value="Cidade"/>
                                    </f:facet>
                                    <h:outputText styleClass="texto-cor-cinza texto-size-16 texto-font"
                                                  value="#{planoEmpresa.empresa.cidadeNome}"/>
                                </rich:column>
                                <rich:column width="20%" sortBy="#{planoEmpresa.empresa.setor}"
                                             filterBy="#{planoEmpresa.empresa.setor}">
                                    <f:facet name="header">
                                        <h:outputText
                                                styleClass="texto-font texto-size-14-real texto-cor-cinza texto-bold"
                                                value="Bairro"/>
                                    </f:facet>
                                    <h:outputText styleClass="texto-cor-cinza texto-size-16 texto-font"
                                                  value="#{planoEmpresa.empresa.setor}"/>
                                </rich:column>
                            </c:if>
                        </rich:dataTable>
                    </rich:tab>

                    <rich:tab id="reajusteMonetario" label="Reajuste Monetário"
                              rendered="#{PlanoControle.usuarioLogado.administrador}">
                        <h:panelGrid id="panelReajusteMonetario" columns="1" width="100%" headerClass="subordinado"
                                     columnClasses="colunaCentralizada">
                            <h:panelGrid columns="2" rowClasses="linhaImpar, linhaPar"
                                         columnClasses="classEsquerda, classDireita" width="100%" id="panelDescricaoProgresso">
                                <f:facet name="header">
                                    <h:panelGrid columns="1">

                                        <h:outputText escape="false" style="text-align:left;color:red;"
                                                      styleClass="titulo7"
                                                      value="*Atenção! Esse procedimento não altera o valor do Plano. Esta alteração deve ser feita manualmente, como uma alteração de preços convencional."/>
                                        <h:outputText escape="false" style="font-weight:bold;" styleClass="tituloCampos"
                                                      value="#{msg_aplic.prt_Plano_descricaoReajuste}"/>

                                    </h:panelGrid>
                                </f:facet>
                                <f:facet name="footer">
                                    <h:outputText styleClass="titulo3" escape="false"
                                                  value="#{PlanoControle.planoVO.descricaoProgresso}"/>
                                </f:facet>

                                <h:outputText styleClass="tituloCampos" value="Tipo alteração:"/>
                                <h:panelGroup>
                                    <h:selectOneMenu value="#{PlanoControle.tipoAlteracaoSelecionado}">
                                        <f:selectItems value="#{PlanoControle.listaTipoAlteracao}"/>
                                        <a4j:support event="onchange"
                                                     reRender="pgAlteracaoDetalhado,pgAlteracaoSimples"></a4j:support>
                                    </h:selectOneMenu>
                                </h:panelGroup>
                            </h:panelGrid>
                            <h:panelGroup id="pgAlteracaoDetalhado" layout="block">
                                <h:panelGrid rendered="#{(PlanoControle.tipoAlteracaoSelecionado eq 'DETALHADO')}"
                                             columns="2"
                                             rowClasses="linhaImpar, linhaPar"
                                             columnClasses="classEsquerda, classDireita" width="100%">

                                    <h:outputText styleClass="tituloCampos" value="Código do contrato:"/>
                                    <h:inputText size="5" maxlength="7" onblur="blurinput(this);"
                                                 onfocus="focusinput(this);"
                                                 id="idCodigoContrato2"
                                                 styleClass="form" value="#{PlanoControle.codigoContratoSelecionado}"/>

                                    <h:outputText styleClass="tituloCampos"
                                                  value="#{msg_aplic.prt_Plano_tipoReajuste}"/>
                                    <h:panelGroup>
                                        <h:selectOneMenu value="#{PlanoControle.planoVO.tipoReajuste}">
                                            <f:selectItems value="#{PlanoControle.tiposReajuste}"/>
                                        </h:selectOneMenu>
                                    </h:panelGroup>

                                    <h:outputText styleClass="tituloCampos"
                                                  value="#{msg_aplic.prt_Plano_valorAcrescentar}"/>
                                    <h:panelGroup>
                                        <h:inputText id="valorAcrescentar" size="10" maxlength="10"
                                                     onblur="blurinput(this);"
                                                     onkeypress="return formatar_moeda(this,'.',',',event);"
                                                     onfocus="focusinput(this);"
                                                     styleClass="form"
                                                     value="#{PlanoControle.planoVO.valorAcrescentar}">
                                            <f:converter converterId="FormatadorNumerico"/>
                                        </h:inputText>
                                    </h:panelGroup>

                                    <h:outputText styleClass="tituloCampos"
                                                  value="#{msg_aplic.prt_Plano_vencimentoParcelas}"/>
                                    <h:panelGroup>
                                        <rich:calendar id="vencimentoParcelas"
                                                       value="#{PlanoControle.planoVO.dataVencimentoParcela}"
                                                       inputSize="10"
                                                       inputClass="form"
                                                       oninputblur="blurinput(this);"
                                                       oninputfocus="focusinput(this);"
                                                       oninputchange="return validar_Data(this.id);"
                                                       datePattern="dd/MM/yyyy"
                                                       enableManualInput="true"
                                                       zindex="2"
                                                       showWeeksBar="false"/>

                                    </h:panelGroup>
                                    <h:outputText styleClass="tituloCampos"
                                                  value="#{msg_aplic.prt_Plano_dataLancamentoContrato}"/>
                                    <h:panelGroup>
                                        <h:panelGroup>
                                            <rich:calendar id="lancamentoContratoinicio"
                                                           value="#{PlanoControle.planoVO.dataLancamentoContratoInicio}"
                                                           inputSize="10"
                                                           inputClass="form"
                                                           oninputblur="blurinput(this);"
                                                           oninputfocus="focusinput(this);"
                                                           oninputchange="return validar_Data(this.id);"
                                                           datePattern="dd/MM/yyyy"
                                                           enableManualInput="true"
                                                           zindex="2"
                                                           showWeeksBar="false"/>

                                        </h:panelGroup>
                                        <h:outputText styleClass="tituloCampos" value=" até "/>
                                        <h:panelGroup>
                                            <rich:calendar id="lancamentoContratofinal"
                                                           value="#{PlanoControle.planoVO.dataLancamentoContratoFim}"
                                                           inputSize="10"
                                                           inputClass="form"
                                                           oninputblur="blurinput(this);"
                                                           oninputfocus="focusinput(this);"
                                                           oninputchange="return validar_Data(this.id);"
                                                           datePattern="dd/MM/yyyy"
                                                           enableManualInput="true"
                                                           zindex="2"
                                                           showWeeksBar="false"/>

                                        </h:panelGroup>
                                    </h:panelGroup>
                                    <h:outputText styleClass="tituloCampos"
                                                  value="#{msg_aplic.prt_Plano_tiposContrato}"/>
                                    <h:panelGroup>
                                        <h:selectBooleanCheckbox styleClass="tituloCampos"
                                                                 value="#{PlanoControle.planoVO.contratosMatricula}"/>
                                        <h:outputText styleClass="tituloCampos" value="Matrícula"/>
                                        <rich:spacer width="50px"/>
                                        <h:selectBooleanCheckbox styleClass="tituloCampos"
                                                                 value="#{PlanoControle.planoVO.contratosRematricula}"/>
                                        <h:outputText styleClass="tituloCampos" value="Rematrícula"/>
                                        <rich:spacer width="50px"/>
                                        <h:selectBooleanCheckbox styleClass="tituloCampos"
                                                                 value="#{PlanoControle.planoVO.contratosRenovacao}"/>
                                        <h:outputText styleClass="tituloCampos" value="Renovação"/>

                                    </h:panelGroup>

                                </h:panelGrid>
                            </h:panelGroup>

                            <h:panelGroup id="pgAlteracaoSimples" layout="block">
                                <h:panelGrid rendered="#{(PlanoControle.tipoAlteracaoSelecionado eq 'SIMPLES')}"
                                             columns="2"
                                             rowClasses="linhaImpar, linhaPar"
                                             columnClasses="classEsquerda, classDireita" width="100%">

                                    <h:outputText styleClass="tituloCampos" value="Código do contrato:"/>
                                    <h:inputText size="5" maxlength="7" onblur="blurinput(this);"
                                                 onfocus="focusinput(this);"
                                                 id="idCodigoContrato"
                                                 styleClass="form" value="#{PlanoControle.codigoContratoSelecionado}">
                                    </h:inputText>

                                    <h:outputText styleClass="tituloCampos" value="Duração do contrato:"/>
                                    <h:inputText size="5" maxlength="7" onblur="blurinput(this);"
                                                 onfocus="focusinput(this);"
                                                 id="idDuracaoContrato"
                                                 styleClass="form" value="#{PlanoControle.duracaoSelecionado}">
                                    </h:inputText>

                                    <h:outputText styleClass="tituloCampos" value="Valor da mensalidade atual:"/>
                                    <h:panelGroup>
                                        <h:inputText size="10" maxlength="10" onblur="blurinput(this);"
                                                     title="Informe o valor das mensalidades que serão alteradas."
                                                     onkeypress="return formatar_moeda(this,'.',',',event);"
                                                     onfocus="focusinput(this);"
                                                     styleClass="form" value="#{PlanoControle.valorMensalAntigo}">
                                            <f:converter converterId="FormatadorNumerico"/>
                                        </h:inputText>
                                    </h:panelGroup>

                                    <h:outputText styleClass="tituloCampos" value="Novo valor da mensalidade:"/>
                                    <h:panelGroup>
                                        <h:inputText size="10" maxlength="10" onblur="blurinput(this);"
                                                     title="Defina um novo valor para as mensalidades."
                                                     onkeypress="return formatar_moeda(this,'.',',',event);"
                                                     onfocus="focusinput(this);"
                                                     styleClass="form" value="#{PlanoControle.valorMensalNovo}">
                                            <f:converter converterId="FormatadorNumerico"/>
                                        </h:inputText>
                                    </h:panelGroup>

                                    <h:outputText styleClass="tituloCampos"
                                                  value="#{msg_aplic.prt_Plano_tiposContrato}"/>
                                    <h:panelGroup>
                                        <h:selectBooleanCheckbox styleClass="tituloCampos"
                                                                 value="#{PlanoControle.planoVO.contratosMatricula}"/>
                                        <h:outputText styleClass="tituloCampos" value="Matrícula"/>
                                        <rich:spacer width="50px"/>
                                        <h:selectBooleanCheckbox styleClass="tituloCampos"
                                                                 value="#{PlanoControle.planoVO.contratosRematricula}"/>
                                        <h:outputText styleClass="tituloCampos" value="Rematrícula"/>
                                        <rich:spacer width="50px"/>
                                        <h:selectBooleanCheckbox styleClass="tituloCampos"
                                                                 value="#{PlanoControle.planoVO.contratosRenovacao}"/>
                                        <h:outputText styleClass="tituloCampos" value="Renovação"/>

                                    </h:panelGroup>

                                </h:panelGrid>
                            </h:panelGroup>

                            <h:panelGrid columns="2">
                                <a4j:commandButton value="Simular"
                                                   id="btnSimularReajuste"
                                                   style="vertical-align:middle;"
                                                   onclick="toggleBtnProcess(true,this.id,'Simular');#{rich:component('pgrBar')}.enable()"
                                                   oncomplete="toggleBtnProcess(false,this.id,'Simular');#{rich:component('pgrBar')}.disable();hideProgressBar()"
                                                   actionListener="#{PlanoControle.processarReajuste}"
                                                   reRender="panelDescricaoProgresso,panelMensagem">
                                    <f:attribute name="simular" value="true"/>
                                </a4j:commandButton>

                                <a4j:commandButton value="Processar"
                                                   id="btnProcessarReajuste"
                                                   style="vertical-align:middle;"
                                                   disabled="#{empty PlanoControle.planoVO.descricaoProgresso}"
                                                   onclick="if (!confirm('Tem certeza que deseja alterar os valores das parcelas em aberto do Plano \"#{PlanoControle.planoVO.descricao}\" definitivamente?')){return false;}toggleBtnProcess(true,this.id,'Processar');#{rich:component('pgrBar')}.enable()"
                                                   oncomplete="toggleBtnProcess(false,this.id,'Processar');#{rich:component('pgrBar')}.disable();hideProgressBar()"
                                                   actionListener="#{PlanoControle.processarReajuste}"
                                                   reRender="panelDescricaoProgresso,panelMensagem">
                                    <f:attribute name="simular" value="false"/>
                                </a4j:commandButton>
                            </h:panelGrid>

                            <rich:progressBar interval="900" value="#{PlanoControle.pgrAtual}"
                                              id="pgrBar"
                                              enabled="#{PlanoControle.processandoReajuste}"
                                              style="width:100%;"
                                              minValue="1" maxValue="#{PlanoControle.pgrTotal}"
                                              progressVar="progress">

                                <h:outputText value="#{(PlanoControle.pgrAtual * 100) / PlanoControle.pgrTotal}">
                                    <f:converter converterId="FormatadorNumerico"/>
                                </h:outputText>
                                <h:outputText value="%"/>
                            </rich:progressBar>

                        </h:panelGrid>


                    </rich:tab>

                </rich:tabPanel>

                <h:panelGrid id="panelMensagem" columns="1" width="100%" styleClass="tabMensagens">
                    <h:panelGrid columns="3" width="100%" styleClass="tabMensagens">
                        <h:panelGrid columns="1" width="100%">
                            <h:outputText value=" "/>
                        </h:panelGrid>
                        <h:commandButton id="imgAtencao" rendered="#{PlanoControle.atencao}"
                                         image="./imagens/atencao.png"/>
                        <h:commandButton id="imgSucesso" rendered="#{PlanoControle.sucesso}"
                                         image="./imagens/sucesso.png"/>
                        <h:commandButton id="imgErro" rendered="#{PlanoControle.erro}" image="./imagens/erro.png"/>
                        <h:panelGrid columns="1" width="100%">
                            <h:outputText id="msgPlano" styleClass="mensagem" value="#{PlanoControle.mensagem}"/>
                            <h:outputText id="msgPlanoDet" styleClass="mensagemDetalhada"
                                          value="#{PlanoControle.mensagemDetalhada}"/>
                        </h:panelGrid>
                    </h:panelGrid>
                    <h:panelGrid columns="1" width="100%" columnClasses="colunaCentralizada">
                        <h:panelGroup>
                            <a4j:commandButton id="salvar" action="#{PlanoControle.validarDadosParaGravar}"
                                               value="#{msg_bt.btn_gravar}"
                                               alt="#{msg.msg_gravar_dados}"
                                               oncomplete="#{PlanoControle.mensagemNotificar};#{PlanoControle.onCompleteGravar};fireElementFromAnyParent('form:btnAtualizaTempo')"
                                               accesskey="2" styleClass="botoes nvoBt"
                                               reRender="dadosModalidade, mdlAviso, panelMensagem,painelPlanos"/>
                            <h:outputText value="    "/>

                            <h:panelGroup id="grupoBtnExcluir">
                                <a4j:commandButton id="excluir" reRender="mdlMensagemGenerica"
                                                   oncomplete="#{PlanoControle.msgAlert}"
                                                   action="#{PlanoControle.confirmarExcluir}"
                                                   value="#{msg_bt.btn_excluir}" title="#{msg.msg_excluir_dados}"
                                                   accesskey="3" styleClass="botoes nvoBt btSec btPerigo"/>
                            </h:panelGroup>
                            <h:outputText value="    "/>

                            <a4j:commandButton id="clonar" rendered="#{PlanoControle.planoVO.codigo > 0 && !LoginControle.apresentarAcessoNovoPlano}"
                                               action="#{PlanoControle.clonar}" reRender="form"
                                               value="#{msg_bt.btn_Clonar}"
                                               oncomplete="fireElementFromAnyParent('form:btnAtualizaTempo')"
                                               alt="#{msg.msg_clonar_dados}" accesskey="4"
                                               styleClass="botoes nvoBt btSec"/>
                            <h:outputText value="    "/>

                            <h:commandButton id="consultar" immediate="true"
                                             action="#{PlanoControle.inicializarConsultar}"
                                             value="#{msg_bt.btn_voltar_lista}"
                                             onclick="fireElementFromAnyParent('form:btnAtualizaTempo')"
                                             alt="#{msg.msg_consultar_dados}" accesskey="5"
                                             styleClass="botoes nvoBt btSec"/>
                            <h:outputText value="    "/>

                            <a4j:commandLink action="#{PlanoControle.realizarConsultaLogObjetoSelecionado}"
                                             reRender="form"
                                             oncomplete="abrirPopup('visualizadorLogForm.jsp', 'VisualizadorLog', 785, 595);fireElementFromAnyParent('form:btnAtualizaTempo')"
                                             title="Visualizar Log" styleClass="botoes nvoBt btSec"
                                             style="display: inline-block;padding-bottom: 10px;">
                                <i class="fa-icon-list"></i>
                            </a4j:commandLink>
                        </h:panelGroup>
                    </h:panelGrid>
                </h:panelGrid>
            </h:panelGrid>
            <SCRIPT>
                carregarTooltipsterPlano();
            </SCRIPT>
        </h:form>
    </h:panelGrid>

    <rich:modalPanel id="mdlAviso" width="400" autosized="true" shadowOpacity="true">
        <f:facet name="header">
            <h:panelGroup>
                <h:outputText value="Aviso"/>
            </h:panelGroup>
        </f:facet>
        <a4j:form id="formAviso">
            <h:panelGrid columns="1" columnClasses="colunaCentralizada" width="100%">
                <h:outputText styleClass="titulo3" value="#{PlanoControle.msgModal}"/>

                <h:panelGroup layout="block">
                    <a4j:commandButton value="Sim" action="#{PlanoControle.gravar}" reRender="mdlAviso, panelMensagem, maximoVezesParcelar"
                                       id="confirmacaoOpercaoSim"
                                       oncomplete="Richfaces.hideModalPanel('mdlAviso');"/>
                    <a4j:commandButton value="Não" onclick="Richfaces.hideModalPanel('mdlAviso');" reRender="mdlAviso"
                                       id="confirmacaoOpercaoNao"/>
                </h:panelGroup>
            </h:panelGrid>
        </a4j:form>
    </rich:modalPanel>
    <rich:jQuery id="mskData" selector=".rich-calendar-input" timing="onload" query="mask('99/99/9999')"/>

</f:view>
<script>
    window.onload = function () {
        processarCheckTodas('tabelaEmpresas', 'checkAcessoTodasEmpresas', 'checkVendaEmpresa');
    }
    carregarTooltipsterPlano();
    document.getElementById("form:descricao").focus();

    function totalEmpresasAcessoSelecionadas() {
        var total = 0;
        jQuery("input[id$='checkAcessoEmpresa']").each(function (i, el) {
            if (el.checked === true)
                total++;
        });
        return total;
    }

    function totalEmpresasVendaSelecionadas() {
        var total = 0;
        jQuery("input[id$='checkVendaEmpresa']").each(function (i, el) {
            if (el.checked === true)
                total++;
        });
        return total;
    }

    function processarCheckTodasEmpresas() {
        console.log(totalEmpresasAcessoSelecionadas());
        if (totalEmpresas() === totalEmpresasAcessoSelecionadas()) {
            jQuery("input[id$='checkAcessoTodasEmpresas']")[0].checked = true;
        } else {
            jQuery("input[id$='checkAcessoTodasEmpresas']")[0].checked = false;
        }

        if (totalEmpresas() === totalEmpresasVendaSelecionadas()) {
            jQuery("input[id$='checkVendaTodasEmpresas']")[0].checked = true;
        } else {
            jQuery("input[id$='checkVendaTodasEmpresas']")[0].checked = false;
        }
    }

    function atualizarEmpresas() {
        document.getElementById('form:atualizarEmpresas').value = true;
        processarCheckTodasEmpresas();
    }

    function totalEmpresas() {
        return jQuery("[id$='tabelaEmpresas'] tr").length - 1;
    }

    function checkVendaTodasEmpresas() {
        var checkedTodas = jQuery("input[id$='checkVendaTodasEmpresas']")[0].checked;
        jQuery("input[id$='checkVendaEmpresa']").each(function (i, el) {
            el.checked = checkedTodas;
        });
    }

    function checkAcessoTodasEmpresas() {
        var checkedTodas = jQuery("input[id$='checkAcessoTodasEmpresas']")[0].checked;
        jQuery("input[id$='checkAcessoEmpresa']").each(function (i, el) {
            el.checked = checkedTodas;
        });
    }
</script>
