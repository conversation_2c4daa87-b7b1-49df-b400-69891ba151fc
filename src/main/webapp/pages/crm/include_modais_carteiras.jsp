<%@taglib prefix="f" uri="http://java.sun.com/jsf/core" %>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html" %>
<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax" %>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich" %>
<%@taglib prefix="jsfChart" uri="http://sourceforge.net/projects/jsf-comp" %>
<%@taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@page pageEncoding="ISO-8859-1"%>

<c:set var="contexto" value="${pageContext.request.contextPath}" scope="request"/>

<style>
    .dropFgma {
        background: #FFFFFF;
        border: 1px solid #A1A5AA;
        box-sizing: border-box;
        border-radius: 3px;
        width: 229px;
        height: 33px;
    }

    .textFigma {
        font-family: Arial;
        font-style: normal;
        font-weight: normal;
        font-size: 16px;
        line-height: 21px;
        color: #51555A;
        margin-right: 10px;
    }

    .titleFigma {
        width: 114px;
        height: 18px;

        font-family: Arial;
        font-style: normal;
        font-weight: bold;
        font-size: 16px;
        line-height: 18px;

        /* Preto / Pri */

        color: #51555A;
    }

    .title2Figma {
        width: 67px;
        height: 18px;

        font-family: Arial;
        font-style: normal;
        font-weight: bold;
        font-size: 16px;
        line-height: 18px;

        /* Preto / Pri */

        color: #51555A;
    }

    .relative {
        position: relative;
    }

    .textOrigiFigma {
        font-family: Arial;
        font-style: normal;
        font-weight: normal;
        font-size: 16px;
        color: #51555A;
    }

    .myRadioCtrl td {
        padding-left: 137px;
        padding-right: 14px;
    }
</style>

<rich:modalPanel id="pnlVerColaboradores" styleClass="novaModal noMargin" shadowOpacity="true"
                 width="700">
    <f:facet name="header">
        <h:panelGroup>
            <h:outputText value="Colaboradores"/>
        </h:panelGroup>
    </f:facet>
    <f:facet name="controls">
        <h:panelGroup>
            <h:outputLink onclick="#{rich:component('pnlVerColaboradores')}.hide()" value="#">
                <i class='fa-icon-remove'></i>
            </h:outputLink>
            <rich:componentControl for="pnlVerColaboradores" attachTo="hidelink" operation="hide" event="onclick"/>
        </h:panelGroup>
    </f:facet>
    <h:form id="formMdlVerColaboradores">
        <h:panelGroup id="mdlVerColaboradores" layout="block" style="height: 433px; overflow-y: auto;"
                      styleClass="pure-u-1 blocoCarteirasInterno pure-form pure-form-aligned">
            <fieldset>
                <h:panelGroup id="selGrupoColaborador" layout="block" styleClass="pure-control-group">
                    <label>Filtrar por grupo</label>
                    <h:selectOneMenu styleClass="pure-input-1-4"
                                     value="#{CarteirasControle.filtroCarteira.grupoColaboradorVO.codigo}">
                        <f:selectItems value="#{CarteirasControle.filtroCarteira.listaGrupoColaborador}"/>
                        <a4j:support event="onchange" action="#{CarteirasControle.alterarListaVerColaboradores}"
                                     reRender="mdlVerColaboradores"/>
                    </h:selectOneMenu>
                </h:panelGroup>
            </fieldset>
            <rich:dataGrid
                    styleClass="pure-u-1 pure-table pure-table-striped pure-table-bordered cell-based tblClientes"
                    value="#{CarteirasControle.listaVerColaboradores}" var="infoCarteira" columns="2">
                <a4j:commandLink styleClass="cellColaborador" action="#{CarteirasControle.selecionarColaborador}"
                                 reRender="form:selColaborador"
                                 oncomplete="Richfaces.hideModalPanel('pnlVerColaboradores')">
                    <h:outputText value="#{infoCarteira.nomeColaborador}"/>
                    <h:outputText styleClass="idOculta" value="#{infoCarteira.colaborador.codigo}"/>
                    <h:outputText styleClass="pull-right" value="#{infoCarteira.totalVinculos_Apresentar}"/>
                </a4j:commandLink>
            </rich:dataGrid>
        </h:panelGroup>
    </h:form>
</rich:modalPanel>

<%-- MODAL ORGANIZAR --%>
<rich:modalPanel id="filtrar" styleClass="novaModal noMargin" shadowOpacity="true"
                 width="919" height="554">
    <f:facet name="header">
        <h:panelGroup>
            <h:outputText value="Organizar"/>
        </h:panelGroup>
    </f:facet>
    <f:facet name="controls">
        <h:panelGroup>
            <h:outputText
                    styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign btnSelecionarModalidade"
                    id="org"/>
            <rich:componentControl for="filtrar" attachTo="org" operation="hide"
                                   event="onclick"/>
        </h:panelGroup>
    </f:facet>
    <h:form id="formMdlOrganizar">
        <h:panelGroup layout="block" styleClass="pure-u-1 pure-g blocoCarteirasInterno pure-form pure-form-aligned">
            <h:panelGroup layout="block" styleClass="blocoCarteirasInterno pure-form pure-form-aligned"
                          style="margin: 5px;">

                <fieldset>

                    <h:panelGroup id="selTipoColaboradorOrg" layout="block" styleClass="pure-control-group pure-u-2-5"
                                  style="margin-left: 25px">
                        <h:outputText styleClass="textFigma" value="Tipo"/>
                        <h:selectOneMenu styleClass="pure-input-3-4 dropFgma"
                                         disabled="#{CarteirasControle.casosImportantesAvaliar}"
                                         value="#{CarteirasControle.filtroOrganizarCarteira.tipoVinculo}"
                                         id="filtrarColaborador">
                            <f:selectItems value="#{CarteirasControle.listaTiposColaboradores}"/>
                            <a4j:support event="onchange"
                                         actionListener="#{CarteirasControle.carregarListaGrupoColaborador}"
                                         reRender="formMdlOrganizar">
                                <f:attribute name="filtro" value="#{CarteirasControle.filtroOrganizarCarteira}"/>
                            </a4j:support>
                        </h:selectOneMenu>
                    </h:panelGroup>
                    <h:panelGroup id="selGrupoColaboradorOrg" layout="block" styleClass="pure-control-group pure-u-3-5">
                        <h:outputText styleClass="textFigma" value="Grupo"/>
                        <h:selectOneMenu id="filtrarGrupoColaborador"
                                         styleClass="pure-input-3-4 dropFgma"
                                         value="#{CarteirasControle.filtroOrganizarCarteira.grupoColaboradorVO.codigo}">
                            <f:selectItems value="#{CarteirasControle.filtroOrganizarCarteira.listaGrupoColaborador}"/>
                            <a4j:support event="onchange"
                                         actionListener="#{CarteirasControle.carregarListaGrupoColaborador}"
                                         reRender="formMdlOrganizar">
                                <f:attribute name="filtro" value="#{CarteirasControle.filtroOrganizarCarteira}"/>
                            </a4j:support>
                        </h:selectOneMenu>
                        <a4j:commandLink rendered="#{CarteirasControle.mostrarBotaoTransferir}"
                                         action="#{CarteirasControle.transferir}"
                                         id="btnTransferir"
                                         style="margin-left: 23px"
                                         styleClass="pure-button pure-button-primary" value="Transferir"
                                         reRender="formConfirmar, formModalAlertaValidacao"
                                         oncomplete="Richfaces.hideModalPanel('filtrar');Richfaces.showModalPanel('#{CarteirasControle.idModalTransferencia}')">
                            <a4j:actionparam noEscape="true" name="clientesSelecionados"
                                             value="retornaArrayCheckados()"/>
                        </a4j:commandLink>
                        <a4j:commandLink rendered="#{CarteirasControle.mostrarBotaoRemover}"
                                         action="#{CarteirasControle.remover}"
                                         id="btnRemover"
                                         style="margin-left: 5%"
                                         styleClass="pure-button pure-button-primary" value="Remover"
                                         reRender="formConfirmar, formModalAlertaValidacao"
                                         oncomplete="Richfaces.hideModalPanel('filtrar');Richfaces.showModalPanel('#{CarteirasControle.idModalTransferencia}')">
                            <a4j:actionparam noEscape="true" name="clientesSelecionados"
                                             value="retornaArrayCheckados()"/>
                        </a4j:commandLink>
                    </h:panelGroup>
                </fieldset>
            </h:panelGroup>


            <h:panelGroup layout="block" style="width: 100%;margin-top: 25px;">
                <h:outputText value="Colaborador" styleClass="titleFigma" style="margin-left: 5%"/>
                <h:outputText value="Vínculo" styleClass="title2Figma" style="margin-left: 50%"/>
            </h:panelGroup>
            <h:panelGroup layout="block"
                          style="border: 1px solid #DCDDDF;margin-top: 10px;max-height: 250px; overflow-y: scroll; margin-bottom: 39px; width: 100% ">
                <rich:dataTable value="#{CarteirasControle.filtroOrganizarCarteira.infoCarteiraTOs}"
                                styleClass="tabelaSimplesCustom showCellEmpty "
                                id="infoCarteiras"
                                var="infoCarteira" width="100%" columns="1" cellpadding="0">
                    <rich:column id="carteira" styleClass="relative">
                        <rich:simpleTogglePanel switchType="client" width="100%"
                                                style="border: none !important;margin-left: 0px;"
                                                bodyClass="bodyToogle" headerClass="headerToogle"
                                                opened="#{infoCarteira.aberto}">
                            <f:facet name="header">
                                <h:panelGroup>

                                    <h:panelGroup>
                                        <a4j:mediaOutput element="img"
                                                         style="width:32px;height:32px;border-radius: 50%;vertical-align: middle;"
                                                         cacheable="false"
                                                         rendered="#{!SuperControle.fotosNaNuvem}"
                                                         createContent="#{infoCarteira.paintFoto}"
                                                         value="#{ImagemData}"
                                                         mimeType="image/jpeg">
                                            <f:param name="largura" value="43"/>
                                            <f:param name="altura" value="43"/>
                                        </a4j:mediaOutput>
                                        <h:graphicImage rendered="#{SuperControle.fotosNaNuvem}"
                                                        style="width:32px;height:32px;border-radius: 50%;vertical-align: middle;"
                                                        url="#{infoCarteira.paintFotoDaNuvem}"/>

                                        <h:outputText styleClass="nomeCol texto"
                                                      style="color: #51555A;font-size: 14px; vertical-align: middle; margin-left:10px;"
                                                      value="#{infoCarteira.nomeColaborador}"
                                                      id="nome"/>
                                    </h:panelGroup>

                                    <h:panelGroup>
                                        <h:outputText styleClass="vinc texto"
                                                      style="color: #51555A;font-size: 14px; font-weight: normal; vertical-align: middle;margin-right:25%"
                                                      value="#{infoCarteira.totalVinculos} vínculo(s)"/>
                                    </h:panelGroup>
                                </h:panelGroup>
                            </f:facet>
                            <f:facet name="openMarker">
                                <h:panelGroup style="position: absolute;right: 20px;font-size: 20px;top: 25px">
                                    <i class="fa-icon-chevron-down"></i>
                                </h:panelGroup>
                            </f:facet>
                            <f:facet name="closeMarker">
                                <h:panelGroup style="position: absolute;right: 20px;font-size: 20px;top: 25px">
                                    <i class="fa-icon-chevron-up"></i>
                                </h:panelGroup>
                            </f:facet>

                            <h:panelGroup layout="block" id="tableToogleTable" style="padding: 0px !important;">
                                <rich:dataTable columns="2" value="#{infoCarteira.qtdVinculosColaborador}"
                                                styleClass="tabelaSimplesCustom showCellEmpty"
                                                style="box-sizing: border-box; border-collapse: collapse; border-bottom: 1px solid #DCDDDF;"
                                                var="tipoVinculoCarteira" width="100%"
                                                id="tipoVinculoCarteira"
                                                columnsWidth="25%" rowClasses="linhaClicka">
                                    <rich:column id="colunaTipoVinculo"
                                                 style="background: #{tipoVinculoCarteira.classCSS};color: #80858C;">
                                        <a4j:commandLink value="#{tipoVinculoCarteira.tipoVinculo_Apresentar}"
                                                         styleClass="texto"
                                                         style="margin-left: 50px;color: #80858C; font-size: 14px;font-weight: normal;"
                                                         action="#{CarteirasControle.setarDestino}"
                                                         reRender="formMdlOrganizar">
                                            <f:attribute name="infoCarteiraDestino" value="#{infoCarteira}"/>
                                            <f:attribute name="tipoVinculoDestino" value="#{tipoVinculoCarteira}"/>
                                        </a4j:commandLink>
                                    </rich:column>
                                    <rich:column id="colunaQtdVinculo"
                                                 style="width: 6%;background: #{tipoVinculoCarteira.classCSS};">
                                        <a4j:commandLink value="#{tipoVinculoCarteira.qtdVinculos} vínculo(s)"
                                                         styleClass="texto"
                                                         style="color: #80858C; font-size: 14px;font-weight: normal;"
                                                         action="#{CarteirasControle.setarDestino}"
                                                         reRender="formMdlOrganizar">
                                            <f:attribute name="infoCarteira" value="#{infoCarteira}"/>
                                            <f:attribute name="tipoVinculoCarteira" value="#{tipoVinculoCarteira}"/>
                                        </a4j:commandLink>
                                    </rich:column>
                                </rich:dataTable>
                            </h:panelGroup>
                        </rich:simpleTogglePanel>
                    </rich:column>
                </rich:dataTable>
            </h:panelGroup>


            <h:panelGroup layout="block" style="margin-left: 10px;">
                <h:panelGroup>
                    <h:outputText styleClass="textOrigiFigma" value="Quantidade:"/>
                    <h:inputText id="inputTransferirPorQuantidade" size="2"
                                 maxlength="5"
                                 onblur="blurinput(this);" onfocus="focusinput(this);"
                                 disabled="#{!CarteirasControle.transferirPorQuantidade}"
                                 value="#{CarteirasControle.quantidadeAlunosParaTransferencia}">
                        <a4j:support event="onchange" rendered="btnTransferir"
                                     action="#{CarteirasControle.validarAlgumaCoisa}"/>
                    </h:inputText>
                </h:panelGroup>
                <h:panelGroup styleClass="textOrigiFigma">
                    <h:selectOneRadio id="opcaoTransferenciaSelecaoCarteira" styleClass="myRadioCtrl"
                                      style="margin-left: 10%;font-size: 14px !important;display: inline;font-family: Arial;font-style: normal;
                                      font-weight: normal;color: #51555A;"
                                      value="#{CarteirasControle.transferirPorQuantidade}">
                        <a4j:support event="onclick" reRender="inputTransferirPorQuantidade"/>
                        <f:selectItem itemValue="true" itemLabel="Transferir aleatório(s) por quantidade"
                                      itemDisabled="#{CarteirasControle.quantidadeAlunosSelecionadosParaTransferencia > 0}"
                        />
                        <f:selectItem itemValue="false"
                                      itemLabel="Transferir"
                                      itemDisabled="#{CarteirasControle.quantidadeAlunosSelecionadosParaTransferencia == 0}"
                        />
                    </h:selectOneRadio>
                </h:panelGroup>
            </h:panelGroup>

            <h:panelGroup layout="block" styleClass="pure-u-1 text-right margin-v-10">
                <a4j:commandLink id="btnAdicionar"
                                 rendered="#{CarteirasControle.mostrarBotaoAdicionar}"
                                 action="#{CarteirasControle.adicionar}"
                                 style="margin-right: 27px;"
                                 styleClass="pure-button margin-h-10" value="Adicionar" reRender="formConfirmar"
                                 oncomplete="#{CarteirasControle.onCompleteDetalhes}">
                    <a4j:actionparam noEscape="true" name="clientesSelecionados" value="retornaArrayCheckados()"/>
                </a4j:commandLink>

                <h:outputText styleClass="margin-h-10" style="font-weight: bold; font-size: 12px"
                              value="É necessário escolher o tipo de vínculo da carteira."
                              rendered="#{CarteirasControle.mostrarMensagemExplicativa  and !CarteirasControle.destino.tipoVinculo eq 'CR'}"/>

                <h:outputText styleClass="margin-h-10" style="font-weight: bold; font-size: 12px"
                              value="Este vínculo não pode ser escolhido. Selecione outro tipo de vínculo."
                              rendered="#{CarteirasControle.mostrarMensagemExplicativa and CarteirasControle.destino.tipoVinculo eq 'CR'}"/>
            </h:panelGroup>

        </h:panelGroup>
    </h:form>
</rich:modalPanel>

<rich:modalPanel id="modalAlertaValidacao" styleClass="novaModal noMargin" shadowOpacity="true" width="700">
    <f:facet name="header">
        <h:panelGroup>
            <h:outputText value="Alerta"/>
        </h:panelGroup>
    </f:facet>
    <f:facet name="controls">
        <h:panelGroup>
            <h:outputText
                    styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign btnSelecionarModalidade"
                    id="alert"/>
            <rich:componentControl for="modalAlertaValidacao" attachTo="alert" operation="hide"
                                   event="onclick"/>
        </h:panelGroup>
    </f:facet>
    <h:form id="formModalAlertaValidacao">
        <h:panelGroup layout="block" styleClass="pure-u-1 pure-g blocoCarteirasInterno pure-form pure-form-aligned">
            <i class="fa-icon-warning-sign fa-icon-3x" style="float: left; margin-right: 20px;"></i>
            <h:panelGroup layout="block" style="display: inline-block; font-size: 14px; margin-top: 8px;">
                <h:outputText value="#{CarteirasControle.mensagemAlertaValidacaoTransferencia} "/>
            </h:panelGroup>
        </h:panelGroup>
    </h:form>
</rich:modalPanel>

<rich:modalPanel id="filtrarConfirma" styleClass="novaModal noMargin" shadowOpacity="true"
                 width="700">
    <f:facet name="header">
        <h:panelGroup>
            <h:outputText value="Organizar"/>
        </h:panelGroup>
    </f:facet>
    <f:facet name="controls">
        <h:panelGroup>
            <h:outputText
                    styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign btnSelecionarModalidade"
                    id="org2"/>
            <rich:componentControl for="filtrarConfirma" attachTo="org2" operation="hide"
                                   event="onclick"/>
        </h:panelGroup>
    </f:facet>
    <h:form id="formConfirmar">
        <h:panelGroup layout="block" style="margin: 5px;"
                      styleClass="pure-u-1 pure-g blocoCarteirasInterno pure-form pure-form-aligned">
            <i class="fa-icon-warning-sign fa-icon-3x" style="float: left; margin-right: 20px;"></i>

            <h:panelGroup rendered="#{CarteirasControle.operacaoAdicionar}" layout="block"
                          style="display: inline-block; font-size: 14px; margin-top: 8px;">
                <h:outputText value="#{CarteirasControle.acao} "/>
                <h:outputText style="font-weight: bold" value="#{CarteirasControle.qtdVinculosAlterados} "/>
                <h:outputText value="para o "/>
                <h:outputText style="font-weight: bold" value="#{CarteirasControle.destino.tipoVinculo_Apresentar} "/>
                <h:outputText style="font-weight: bold" value="#{CarteirasControle.destino.nomeColaborador}"/>
            </h:panelGroup>
            <h:panelGroup rendered="#{CarteirasControle.operacaoRemover}" layout="block"
                          style="display: inline-block; font-size: 14px">
                <h:outputText value="#{CarteirasControle.acao} "/>
                <h:outputText style="font-weight: bold" value="#{CarteirasControle.qtdVinculosAlterados} "/>
                <h:outputText value="do "/>
                <h:outputText style="font-weight: bold"
                              value="#{CarteirasControle.filtroBuscarCarteira.tipoVinculo_Apresentar} "/>
                <h:outputText style="font-weight: bold"
                              value="#{CarteirasControle.filtroBuscarCarteira.nomeColaborador}"/>
            </h:panelGroup>
            <h:panelGroup rendered="#{CarteirasControle.operacaoTransferir}" layout="block"
                          style="display: inline-block; font-size: 14px">
                <h:outputText value="#{CarteirasControle.acao} "/>
                <h:outputText style="font-weight: bold" value="#{CarteirasControle.qtdVinculosAlterados} "/>
                <h:outputText rendered="#{!CarteirasControle.operacaoAdicionar}" value="do "/>
                <h:outputText style="font-weight: bold"
                              value="#{CarteirasControle.filtroBuscarCarteira.tipoVinculo_Apresentar} "/>
                <h:outputText style="font-weight: bold"
                              value="#{CarteirasControle.filtroBuscarCarteira.nomeColaborador}"/>
                <h:outputText value=" para o "/>
                <h:outputText style="font-weight: bold" value="#{CarteirasControle.destino.tipoVinculo_Apresentar} "/>
                <h:outputText style="font-weight: bold" value="#{CarteirasControle.destino.nomeColaborador}"/>
            </h:panelGroup>
            <br/>

            <br/>
            <h:panelGroup layout="block" style="display: inline-block;font-size: 14px"
                          rendered="#{CarteirasControle.codigosAgendamentosTransferir != null && not empty CarteirasControle.codigosAgendamentosTransferir}">
                <h:selectBooleanCheckbox value="#{CarteirasControle.transferirAgendamentos}">
                    <a4j:support event="onchange" reRender="filtrarConfirma"
                                 oncomplete="Richfaces.showModalPanel('filtrarConfirma')"
                                 actionListener="#{CarteirasControle.verificarPossibilidadeTransferirVinculosFuturos}"/>
                </h:selectBooleanCheckbox>
                <h:outputText value="Transferir também agendamentos futuros dos alunos selecionados"/>
            </h:panelGroup>
            <br/>

            <br/>
            <h:panelGroup id="informativoNaoTransferirAgendamentos"
                          rendered="#{!CarteirasControle.habilitarBotaoConfirmarTransferencia}" layout="block"
                          style="display: inline-block; font-size: 14px">
                <h:outputText style="color: red;"
                              value="Agendamentos futuros não poderão ser transferidos pois o colaborador que receberá a transferência não tem usuário cadastrado. Para transferir apenas os vínculos desmarque a opção acima."/>
            </h:panelGroup>
            <h:panelGroup layout="block" styleClass="pure-u-1 text-right margin-v-10">
                <a4j:commandLink id="cancOrg" styleClass="pure-button margin-h-10" value="Cancelar"
                                 reRender="form:carteirasColaborador"
                                 oncomplete="Richfaces.hideModalPanel('filtrarConfirma')">
                </a4j:commandLink>

                <a4j:commandLink id="confOrg" styleClass="pure-button pure-button-primary margin-h-10"
                                 action="#{CarteirasControle.processar}" value="Confirmar"
                                 reRender="form:carteirasColaborador, pnlCarteiras"
                                 oncomplete="#{CarteirasControle.dispararDataTables}"
                                 rendered="#{CarteirasControle.habilitarBotaoConfirmarTransferencia}">
                    <a4j:actionparam noEscape="true" name="clientesSelecionados" value="retornaArrayCheckados()"/>
                    <rich:componentControl for="filtrarConfirma" attachTo="confOrg" operation="hide" event="onclick"/>
                </a4j:commandLink>
            </h:panelGroup>
        </h:panelGroup>
    </h:form>
</rich:modalPanel>

<%--
    Document   : include_smartbox_conteudo
    Created on : 20/01/2012, 08:30:11
    Author     : Joao Alcides
--%>
<rich:modalPanel id="modalListaClientes" autosized="true"
                 shadowOpacity="true" width="850" minHeight="350">

    <f:facet name="controls">
        <h:panelGroup>
            <h:outputLink onclick="#{rich:component('modalListaClientes')}.hide()" value="#">
                <i class='fa-icon-remove'></i>
            </h:outputLink>
            <rich:componentControl for="filtrarConfirma" attachTo="hidelink" operation="hide" event="onclick"/>
        </h:panelGroup>
    </f:facet>

    <a4j:form id="formListaClientes" ajaxSubmit="true">
        <a4j:outputPanel id="panelLista">

            <script type="text/javascript">
                tabelaSmartBox('formListaClientes:listaClientes');
                var modal = document.getElementById('modalListaClientesCDiv');
                modal.style.top = '100px';
                modal.style.left = (screen.availWidth - 670) / 2;
            </script>

            <!-- LISTA -->
            <br/>
            <rich:dataTable id="listaClientes" var="cliente"
                            value="#{CarteirasControle.listaClientes}" rows="10"
                            onRowMouseOver="this.style.backgroundColor='#F1F1F1'"
                            onRowMouseOut="this.style.backgroundColor='#{a4jSkin.tableBackgroundColor}'"
                            width="830">
                <rich:column>
                    <f:facet name="header">
                        <h:outputText value="#{msg_aplic.prt_ListaClientes_matricula}"/>
                    </f:facet>
                    <a4j:commandLink action="#{CarteirasControle.irParaTelaCliente}"
                                     oncomplete="abrirPopup('clienteNav.jsp?page=viewCliente', 'Cliente', 1024, 700);">
                        <h:outputText styleClass="linkListaClientes" value="#{cliente.matricula}"/>
                    </a4j:commandLink>
                </rich:column>

                <rich:column sortBy="#{cliente.pessoa.nome}" sortable="true">
                    <f:facet name="header">
                        <h:outputText value="#{msg_aplic.prt_ListaClientes_nome}"/>
                    </f:facet>
                    <a4j:commandLink action="#{CarteirasControle.irParaTelaCliente}"
                                     oncomplete="abrirPopup('clienteNav.jsp?page=viewCliente', 'Cliente', 1024, 700);">
                        <h:outputText styleClass="linkListaClientes" value="#{cliente.pessoa.nome}"/>
                    </a4j:commandLink>
                </rich:column>

                <rich:column>
                    <f:facet name="header">
                        <h:outputText value="#{msg_aplic.prt_Plano_tituloForm}"/>
                    </f:facet>
                    <a4j:commandLink action="#{CarteirasControle.irParaTelaCliente}"
                                     oncomplete="abrirPopup('clienteNav.jsp?page=viewCliente', 'Cliente', 1024, 700);">
                        <h:outputText styleClass="linkListaClientes"
                                      value="#{cliente.situacaoClienteSinteticoVO.nomePlano}"/>
                    </a4j:commandLink>
                </rich:column>

                <rich:column>
                    <f:facet name="header">
                        <h:outputText value="#{msg_aplic.prt_ListaClientes_situacao}"/>
                    </f:facet>
                    <a4j:commandLink action="#{CarteirasControle.irParaTelaCliente}"
                                     oncomplete="abrirPopup('clienteNav.jsp?page=viewCliente', 'Cliente', 1024, 700);">
                        <h:outputText styleClass="linkListaClientes"
                                      value="#{cliente.situacaoClienteSinteticoVO.situacao}"/>
                    </a4j:commandLink>
                </rich:column>

                <rich:column sortBy="#{cliente.situacaoClienteSinteticoVO.duracaoContratoMeses}" sortable="true">
                    <f:facet name="header">
                        <h:outputText value="#{msg_aplic.prt_Duracao_tituloForm} "/>
                    </f:facet>
                    <a4j:commandLink action="#{CarteirasControle.irParaTelaCliente}"
                                     oncomplete="abrirPopup('clienteNav.jsp?page=viewCliente', 'Cliente', 1024, 700);">
                        <h:outputText value="#{cliente.situacaoClienteSinteticoVO.duracaoContratoMeses}"/>
                    </a4j:commandLink>
                </rich:column>

                <rich:column sortBy="#{cliente.situacaoClienteSinteticoVO.dataVigenciaAteAjustada}">
                    <f:facet name="header">
                        <h:outputText value="#{msg_aplic.prt_Agenda_vencimentoContrato}"/>
                    </f:facet>
                    <a4j:commandLink action="#{CarteirasControle.irParaTelaCliente}"
                                     oncomplete="abrirPopup('clienteNav.jsp?page=viewCliente', 'Cliente', 1024, 700);">
                        <h:outputText styleClass="linkListaClientes"
                                      value="#{cliente.situacaoClienteSinteticoVO.dataVigenciaAteAjustada}">
                            <f:convertDateTime type="date" dateStyle="short" locale="pt" timeZone="America/Sao_Paulo"
                                               pattern="dd/MM/yy"/>
                        </h:outputText>
                    </a4j:commandLink>
                </rich:column>
            </rich:dataTable>
        </a4j:outputPanel>
        <!-- ---------------DETALHAMENTO DE CLIENTE ------------------------------- -->
        <a4j:outputPanel id="panelDetalhamento">
            <script type="text/javascript">
                tabelaSmartBox('formListaClientes:detalhaCliente');
                var modal = document.getElementById('modalListaClientesCDiv');
                modal.style.top = '5px';
                modal.style.left = (screen.availWidth - 870) / 2;
            </script>
            <br/>

            <table width="100%">
                <tr>
                    <!-- DADOS DO ALUNO -->
                    <td width="30%" valign="top">
                        <table width="100%">
                            <tr>
                                <td width="35%" valign="top">
                                    <a4j:outputPanel id="panelFoto">
                                        <a4j:mediaOutput element="img" id="imagemFoto" style="width:120px;height:150px "
                                                         cacheable="false"
                                                         rendered="#{!SuperControle.fotosNaNuvem}"
                                                         createContent="#{ClienteControle.paintFoto}"
                                                         value="#{ImagemData}" mimeType="image/jpeg">
                                            <f:param name="largura" value="120"/>
                                            <f:param name="altura" value="150"/>
                                        </a4j:mediaOutput>
                                        <h:graphicImage rendered="#{SuperControle.fotosNaNuvem}"
                                                        width="120" height="150"
                                                        style="left:0px;width:120px;height:150px "
                                                        url="#{ClienteControle.paintFotoDaNuvem}"/>
                                    </a4j:outputPanel>
                                </td>
                                <td width="70%" valign="top">
                                    <img alt="" src="images/arrow2.gif" width="16" height="16"
                                         style="vertical-align:middle;margin-right:6px;">
                                    <h:outputText styleClass="tituloDetalhamentoCliente"
                                                  value="#{msg_aplic.prt_DadosPessoais_tituloForm} "/>
                                    <br/><rich:spacer height="20px"/>
                                    <h:outputText styleClass="labelDetalhamentoCliente"
                                                  value="#{msg_aplic.prt_ListaClientes_dataCadastramento} "/>
                                    <h:outputText styleClass="valoresDetalhamentoCliente"
                                                  value="#{ClienteControle.clienteVO.pessoa.dataCadastro}">
                                        <f:convertDateTime type="date" dateStyle="short" locale="pt"
                                                           timeZone="America/Sao_Paulo" pattern="dd/MM/yy"/>
                                    </h:outputText>
                                    <br/>
                                    <h:outputText styleClass="labelDetalhamentoCliente"
                                                  value="#{msg_aplic.prt_Cliente_categoria} "/>
                                    <h:outputText styleClass="valoresDetalhamentoCliente"
                                                  value="#{ClienteControle.clienteVO.categoria.nome}"/>
                                    <br/>
                                    <h:outputText styleClass="labelDetalhamentoCliente"
                                                  value="#{msg_aplic.prt_ListaClientes_dataMatricula} "/>
                                    <h:outputText styleClass="valoresDetalhamentoCliente"
                                                  value="#{ClienteControle.clienteVO.situacaoClienteSinteticoVO.dataMatricula}">
                                        <f:convertDateTime type="date" dateStyle="short" locale="pt"
                                                           timeZone="America/Sao_Paulo" pattern="dd/MM/yy"/>
                                    </h:outputText>
                                    <br/>
                                    <h:outputText styleClass="labelDetalhamentoCliente"
                                                  value="#{msg_aplic.prt_ListaClientes_dataRematricula} "/>
                                    <h:outputText
                                            rendered="#{ClienteControle.clienteVO.situacaoClienteSinteticoVO.dataUltimaRematricula != null}"
                                            styleClass="valoresDetalhamentoCliente"
                                            value="#{ClienteControle.clienteVO.situacaoClienteSinteticoVO.dataUltimaRematricula}">
                                        <f:convertDateTime type="date" dateStyle="short" locale="pt"
                                                           timeZone="America/Sao_Paulo" pattern="dd/MM/yy"/>
                                    </h:outputText>
                                    <h:outputText
                                            rendered="#{ClienteControle.clienteVO.situacaoClienteSinteticoVO.dataUltimaRematricula == null}"
                                            styleClass="valoresDetalhamentoCliente" value="..."/>
                                    <br/>
                                    <h:outputText styleClass="labelDetalhamentoCliente"
                                                  value="#{msg_aplic.prt_Cliente_situacao} "/>
                                    <h:outputText styleClass="valoresDetalhamentoCliente"
                                                  value="#{ClienteControle.clienteVO.situacao_Apresentar}"/>
                                    <br/>
                                    <h:outputText styleClass="labelDetalhamentoCliente"
                                                  value="#{msg_aplic.prt_HistoricoContato_aniversario}: "/>
                                    <h:outputText rendered="#{ClienteControle.clienteVO.pessoa.dataNasc != null}"
                                                  styleClass="valoresDetalhamentoCliente"
                                                  value="#{ClienteControle.clienteVO.pessoa.dataNasc}">
                                        <f:convertDateTime type="date" dateStyle="short" locale="pt"
                                                           timeZone="America/Sao_Paulo" pattern="dd/MM/yy"/>
                                    </h:outputText>
                                    <h:outputText rendered="#{ClienteControle.clienteVO.pessoa.dataNasc == null}"
                                                  styleClass="valoresDetalhamentoCliente" value="..."/>
                                </td>
                            </tr>
                        </table>
                        <table>
                            <tr>
                                <td>
                                    <img alt="" src="images/arrow2.gif" width="16" height="16"
                                         style="vertical-align:middle;margin-right:6px;">
                                    <h:outputText styleClass="tituloDetalhamentoCliente"
                                                  value="#{msg_aplic.prt_ListaClientes_dadosAluno}"/>
                                    <br/><rich:spacer height="20px"/>
                                    <h:outputText styleClass="labelDetalhamentoCliente"
                                                  value="#{msg_aplic.prt_ListaClientes_contaAcademia} "/>
                                    <h:outputText
                                            styleClass="#{ClienteControle.corVermelhaSaldoNegativoCorVerdeSaldoPositivo}"
                                            value="#{ClienteControle.clienteVO.saldoContaCorrente}">
                                        <f:converter converterId="FormatadorNumerico"/>
                                    </h:outputText>
                                    <br/>
                                    <h:outputText styleClass="labelDetalhamentoCliente"
                                                  value="#{msg_aplic.prt_ListaClientes_Idade} "/>
                                    <h:outputText styleClass="valoresDetalhamentoCliente"
                                                  value="#{ClienteControle.idadeCliente}"/>
                                    <br/>
                                    <h:outputText styleClass="labelDetalhamentoCliente"
                                                  value="#{msg_aplic.prt_Pessoa_sexo} "/>
                                    <h:outputText styleClass="valoresDetalhamentoCliente"
                                                  value="#{ClienteControle.clienteVO.pessoa.sexo_Apresentar}"/>
                                    <br/>
                                    <a4j:repeat value="#{ClienteControle.clienteVO.vinculoVOs}" var="vinculo">
                                        <h:outputText styleClass="labelDetalhamentoCliente"
                                                      value="#{vinculo.tipoVinculo_Apresentar}: "/>
                                        <h:outputText styleClass="valoresDetalhamentoCliente"
                                                      value="#{vinculo.colaborador.pessoa.nome}"/>
                                        <br/>
                                    </a4j:repeat>
                                </td>
                            </tr>

                            <tr>
                                <td width="100%">
                                    <img alt="" src="images/arrow2.gif" width="16" height="16"
                                         style="vertical-align:middle;margin-right:6px;">
                                    <h:outputText styleClass="tituloDetalhamentoCliente"
                                                  value="#{msg_aplic.prt_ListaClientes_mensagensAvisos}"/>

                                    <h:panelGroup layout="block" style="height:100px;overflow:auto;">
                                        <h:dataTable id="avisosCliente" width="100%" columnClasses="textsmall"
                                                     value="#{ClienteControle.clienteVO.listaMensagemAvisoCliente}"
                                                     styleClass="tabFormSubordinada" var="avisoCliente">
                                            <h:column id="teste1">
                                                <img alt="" style="vertical-align: middle; margin-right: 4px;"
                                                     src="imagens/ico_ani_telefone.gif">
                                                <h:panelGroup id="teste2" rendered="#{avisoCliente.navegacaoFrame}">
                                                    <a4j:commandLink id="linkMsgCliente" styleClass="link_red"
                                                                     action="#{ClienteControle.abreTela}"
                                                                     actionListener="#{ClienteControle.selecionarClienteMensagemListener}">
                                                        <f:attribute name="objClienteMensagem" value="#{avisoCliente}"/>
                                                        <h:outputText id="textoMsgCliente"
                                                                      value="#{avisoCliente.mensagem}"/>
                                                    </a4j:commandLink>
                                                </h:panelGroup>
                                                <h:panelGroup rendered="#{avisoCliente.navegacaoPopUp}">
                                                    <a4j:commandLink id="linkMsgCliente1" styleClass="link_red"
                                                                     action="#{ClienteControle.abreTela}"
                                                                     actionListener="#{ClienteControle.selecionarClienteMensagemListener}"
                                                                     oncomplete="abrirPopup('#{avisoCliente.tipomensagem.navegacao}', '#{avisoCliente.tipomensagem}', 780, 595);">
                                                        <f:attribute name="objClienteMensagem" value="#{avisoCliente}"/>
                                                        <h:outputText id="textoMsgCliente1"
                                                                      value="#{avisoCliente.mensagem}"/>
                                                    </a4j:commandLink>
                                                </h:panelGroup>
                                            </h:column>
                                        </h:dataTable>
                                    </h:panelGroup>
                                </td>
                            </tr>
                            <tr>
                                <td>
                                    <img alt="" src="images/smile_smartbox.png" width="24" height="24"
                                         style="vertical-align:middle;margin-right:3px;">
                                    <h:outputText styleClass="valoresDetalhamentoCliente"
                                                  value="#{msg_aplic.prt_ListaClientes_grauSatisfacao} "/>
                                    <h:outputText styleClass="valoresDetalhamentoCliente"
                                                  value="#{ClienteControle.grauSatisfacao}"/>
                                    <br/>
                                    <img alt="" src="imagensCRM/grupo_de_risco.png" width="21" height="12"
                                         style="vertical-align:middle;margin-right:6px;">
                                    <h:outputText styleClass="valoresDetalhamentoCliente"
                                                  value="#{msg_aplic.prt_HistoricoContato_risco}: "/>
                                    <h:outputText rendered="#{ClienteControle.risco.peso != null}"
                                                  styleClass="valoresDetalhamentoCliente"
                                                  value="#{ClienteControle.risco.peso}"/>
                                    <h:outputText rendered="#{ClienteControle.risco.peso == null}"
                                                  styleClass="valoresDetalhamentoCliente" value="..."/>
                                    <br/>
                                    <img alt="" src="imagensCRM/vencidos.png" width="24" height="24"
                                         style="vertical-align:middle;margin-right:6px;">
                                    <h:outputText styleClass="valoresDetalhamentoCliente"
                                                  value="#{msg_aplic.prt_ListaClientes_nrDiasUltimoContato} "/>
                                    <h:outputText rendered="#{!(ClienteControle.diasUltimoContato == '0')}"
                                                  styleClass="valoresDetalhamentoCliente"
                                                  value="#{ClienteControle.diasUltimoContato}"/>
                                    <h:outputText rendered="#{ClienteControle.diasUltimoContato == '0'}"
                                                  styleClass="valoresDetalhamentoCliente"
                                                  value="#{msg_aplic.prt_hoje}"/>
                                    <br/>
                                    <br/>
                                </td>
                            </tr>
                        </table>
                    </td>
                    <!-- ESTATÍSTICAS -->
                    <td width="60%" valign="top">
                        <!-- -------------CONTRATOS---------------------- -->
                        <table width="100%">
                            <tr>
                                <td align="left">
                                    <img alt="" src="images/arrow2.gif" width="16" height="16"
                                         style="vertical-align:middle;margin-right:6px;">
                                    <h:outputText styleClass="tituloDetalhamentoCliente" value="Contratos"/>
                                </td>
                                <td align="right">
                                </td>
                            </tr>
                        </table>
                        <rich:dataTable id="listaContrato" width="100%" border="0" rows="3" cellspacing="0"
                                        cellpadding="10" styleClass="textsmall"
                                        rendered="#{!empty ClienteControle.listaContratos}"
                                        value="#{ClienteControle.listaContratos}" var="contrato">
                            <rich:column style="border: 1px solid silver; padding: 2px;"
                                         headerClass="tabelaFichaCliente" styleClass="tabelaFichaCliente">
                                <f:facet name="header">
                                    <h:outputText style="font-weight: bold" value="Contrato"/>
                                </f:facet>
                                <h:outputText style="font-weight: bold"
                                              styleClass="#{contrato.styleContratoVigenteOuNaoVigente}"
                                              value="#{contrato.codigo}"/>
                            </rich:column>

                            <rich:column style="border: 1px solid silver;padding: 2px;" headerClass="tabelaFichaCliente"
                                         styleClass="tabelaFichaCliente">
                                <f:facet name="header">
                                    <h:outputText style="font-weight: bold" value="Lançamento"/>
                                </f:facet>
                                <h:outputText style="font-weight: bold"
                                              styleClass="#{contrato.styleContratoVigenteOuNaoVigente}"
                                              value="#{contrato.dataLancamento_Apresentar2}"/>
                            </rich:column>

                            <rich:column style="border: 1px solid silver;padding: 2px;" headerClass="tabelaFichaCliente"
                                         styleClass="tabelaFichaCliente">
                                <f:facet name="header">
                                    <h:outputText style="font-weight: bold" value="Início"/>
                                </f:facet>
                                <h:outputText style="font-weight: bold"
                                              styleClass="#{contrato.styleContratoVigenteOuNaoVigente}"
                                              value="#{contrato.vigenciaDe}">
                                    <f:convertDateTime pattern="dd/MM/yy"/>
                                </h:outputText>
                            </rich:column>

                            <rich:column style="border: 1px solid silver;padding: 2px;" headerClass="tabelaFichaCliente"
                                         styleClass="tabelaFichaCliente">
                                <f:facet name="header">
                                    <h:outputText style="font-weight: bold" value="Valor"/>
                                </f:facet>
                                <h:outputText style="font-weight: bold" value="#{contrato.valorFinal}"
                                              styleClass="#{contrato.styleContratoVigenteOuNaoVigente}">
                                    <f:converter converterId="FormatadorNumerico"/>
                                </h:outputText>
                            </rich:column>

                            <rich:column style="border: 1px solid silver;padding: 2px;"
                                         headerClass="tabelaFichaCliente">
                                <f:facet name="header">
                                    <h:outputText style="font-weight: bold" value="Término"/>
                                </f:facet>
                                <h:outputText style="font-weight: bold"
                                              styleClass="#{contrato.styleContratoVigenteOuNaoVigenteDataTermino}"
                                              value="#{contrato.vigenciaAteAjustada}">
                                    <f:convertDateTime pattern="dd/MM/yy"/>
                                </h:outputText>
                            </rich:column>

                            <rich:column style="border: 1px solid silver;padding: 2px;"
                                         headerClass="tabelaFichaCliente">
                                <f:facet name="header">
                                    <h:outputText style="font-weight: bold" value="Plano"/>
                                </f:facet>
                                <h:outputText style="font-weight: bold"
                                              styleClass="#{contrato.styleContratoVigenteOuNaoVigente}"
                                              value="#{contrato.plano.descricao}">
                                </h:outputText>
                            </rich:column>

                            <rich:column style="border: 1px solid silver;padding: 2px;"
                                         headerClass="tabelaFichaCliente">
                                <f:facet name="header">
                                    <h:outputText style="font-weight: bold" value="Dur."/>
                                </f:facet>
                                <h:outputText style="font-weight: bold"
                                              styleClass="#{contrato.styleContratoVigenteOuNaoVigente}"
                                              value="#{contrato.contratoDuracao.numeroMeses}"/>
                            </rich:column>

                            <rich:column style="border: 1px solid silver;padding: 2px;"
                                         headerClass="tabelaFichaCliente">
                                <f:facet name="header">
                                    <h:outputText style="font-weight:bold" value=" S."/>
                                </f:facet>
                                <h:graphicImage id="contAtivo" value="./imagens/botaoAtivo.png"
                                                rendered="#{contrato.contratoAtivo}" width="15" height="15"/>
                                <h:graphicImage id="contCancelado" value="./imagens/botaoCancelamento.png"
                                                rendered="#{contrato.contratoCancelado}" width="15" height="15"/>
                                <h:graphicImage id="contTrancado" value="./imagens/botaoTrancamento.png"
                                                rendered="#{contrato.contratoTrancado}" width="15" height="15"/>
                                <h:graphicImage id="contInativo" value="./imagens/botaoInativo.png"
                                                rendered="#{contrato.contratoInativo}" width="15" height="15"/>
                                <h:graphicImage id="contRenovado" value="./imagens/botaoRenovado.png"
                                                rendered="#{contrato.apresentarBotaoRenovarContrato}" width="15"
                                                height="15"/>
                            </rich:column>
                        </rich:dataTable>

                        <!-- -------------produtos com validade---------------------- -->

                        <h:panelGroup rendered="#{not empty ClienteControle.clienteVO.listaProdutosComValidade}">
                            <rich:spacer height="10px"/>
                            <table width="100%">
                                <tr>
                                    <td align="left">
                                        <img alt="" src="images/arrow2.gif" width="16" height="16"
                                             style="vertical-align:middle;margin-right:6px;">
                                        <h:outputText styleClass="tituloDetalhamentoCliente"
                                                      value="Produtos com Validade"/>
                                    </td>
                                    <td align="right">
                                    </td>
                                </tr>
                            </table>
                            <rich:dataTable id="listaProdutoComValidade" width="100%" border="0" rows="3"
                                            cellspacing="0" cellpadding="10" styleClass="textsmall"
                                            columnClasses="centralizado, centralizado, centralizado "
                                            value="#{ClienteControle.clienteVO.listaProdutosComValidade}"
                                            var="movProduto">

                                <rich:column style="border: 1px solid silver;padding: 2px;"
                                             headerClass="tabelaFichaCliente">
                                    <f:facet name="header">
                                        <h:outputText style="font-weight: bold" value="Produto"/>
                                    </f:facet>
                                    <h:outputText value="#{movProduto.produto.descricao}"/>
                                </rich:column>

                                <rich:column style="border: 1px solid silver;padding: 2px;"
                                             headerClass="tabelaFichaCliente">
                                    <f:facet name="header">
                                        <h:outputText style="font-weight: bold" value="Data Compra"/>
                                    </f:facet>
                                    <h:outputText value="#{movProduto.dataLancamento_Apresentar}"/>
                                </rich:column>

                                <rich:column style="border: 1px solid silver;padding: 2px;"
                                             headerClass="tabelaFichaCliente">
                                    <f:facet name="header">
                                        <h:outputText style="font-weight: bold" value="Data Final Vigência"/>
                                    </f:facet>
                                    <h:outputText style="font-weight: bold" styleClass="red"
                                                  value="#{movProduto.dataFinalVigencia_Apresentar}"/>
                                </rich:column>

                            </rich:dataTable>
                        </h:panelGroup>
                        <!-- -------------acessos---------------------- -->
                        <rich:spacer height="10px"/>

                        <jsp:include page="includes/cliente/include_panelgroup_dados_acesso_cliente_branco.jsp"
                                     flush="true"/>

                        <!-- -------------contatos---------------------- -->
                        <rich:spacer height="10px"/>
                        <table width="100%">
                            <tr>
                                <td align="left">
                                    <img alt="" src="images/btn_historico_ligacoes.png" width="32" height="32"
                                         style="vertical-align:middle;margin-right:6px;">
                                    <h:outputText styleClass="tituloDetalhamentoCliente" value="Histórico de ligações"/>
                                </td>
                                <td align="right">
                                    <a4j:commandButton image="images/btn_fazer_ligacao.png"
                                                       action="#{MetaCRMControle.inicializarContatoAvulso}"
                                                       oncomplete="abrirPopup('newRealizarContatoForm.jsp', 'Realizar Contato Avulso', 850, 700);"/>
                                </td>
                            </tr>
                        </table>

                        <rich:dataTable id="dadosLigacoes" width="100%" border="0" cellspacing="0" cellpadding="10"
                                        styleClass="textsmall" value="#{ClienteControle.contatos}" rows="3"
                                        var="historicoContato">
                            <rich:column style="border: 1px solid silver;padding: 2px;"
                                         headerClass="tabelaFichaCliente">
                                <f:facet name="header">
                                    <h:outputText style="font-weight: bold" value="Data"/>
                                </f:facet>
                                <f:verbatim>
                                    <h:outputText value="#{historicoContato.dia}">
                                        <f:convertDateTime pattern="dd/MM/yy HH:mm"/>
                                    </h:outputText>
                                </f:verbatim>
                            </rich:column>

                            <rich:column style="border: 1px solid silver;padding: 2px;"
                                         headerClass="tabelaFichaCliente">
                                <f:facet name="header">
                                    <h:outputText style="font-weight: bold" value="Consultor"/>
                                </f:facet>
                                <f:verbatim>
                                    <h:outputText value="#{historicoContato.responsavelApresentar}"/>
                                </f:verbatim>
                            </rich:column>

                            <rich:column style="border: 1px solid silver;padding: 2px;"
                                         headerClass="tabelaFichaCliente">
                                <f:facet name="header">
                                    <h:outputText style="font-weight: bold" value="Motivo"/>
                                </f:facet>
                                <f:verbatim>
                                    <h:outputText value="#{historicoContato.fase_Apresentar}"/>
                                </f:verbatim>
                            </rich:column>

                            <rich:column style="border: 1px solid silver;padding: 2px;"
                                         headerClass="tabelaFichaCliente">
                                <f:facet name="header">
                                    <h:outputText style="font-weight: bold" value="Descrição"/>
                                </f:facet>
                                <f:verbatim>
                                    <h:outputText escape="false" value="#{historicoContato.resumoObservacao}"/>
                                    <a4j:commandLink value="[...]"
                                                     action="#{HistoricoContatoControle.abrirPopUpHistorico}"
                                                     oncomplete="#{HistoricoContatoControle.abrirPopUpAdequada}"/>
                                </f:verbatim>
                            </rich:column>

                        </rich:dataTable>

                        <table width="100%">
                            <tr>
                                <td align="right">
                                    <a4j:commandLink value="Ver histórico completo"
                                                     action="#{HistoricoContatoControle.abrirPopUpHistorico}"
                                                     oncomplete="#{HistoricoContatoControle.abrirPopUpAdequada}"/>
                                </td>
                            </tr>
                        </table>
                    </td>
                </tr>
            </table>
        </a4j:outputPanel>
    </a4j:form>
</rich:modalPanel>

<rich:modalPanel id="modalFiltroGestaoCarteiras" autosized="true" styleClass="novaModal noMargin"
                 shadowOpacity="true" width="966" height="500">
    <f:facet name="header">
        <h:panelGroup>
            <h:outputText value="Filtros Avançados"
                          style="font-family: Arial;font-style: normal;font-weight: bold;font-size: 16px;line-height: 28px;"/>
        </h:panelGroup>
    </f:facet>

    <a4j:form id="formFiltrosAvan">
        <h:panelGroup>
            <h:panelGrid id="pnlDatas" columns="2" style="margin-top: 25px;margin-left: 56px;">

                <h:panelGroup layout="block">
                    <a4j:commandLink id="mdlTodos" status="none"
                                     styleClass="texto-cor-azul botao-checkbox texto-size-14"
                                     reRender="mdlTodos, pnlDatas">
                        <h:outputText
                                styleClass="icon #{CarteirasControle.filtroCarteira.todosClientes ? 'fa-icon-check' : 'fa-icon-check-empty'}"/>
                        <h:outputText
                                style="font-family: Arial;font-style: normal;font-weight: normal;font-size: 14px;line-height: 21px;color: #51555A;"
                                value="Todos os clientes cadastrados"/>
                        <f:setPropertyActionListener value="#{!CarteirasControle.filtroCarteira.todosClientes}"
                                                     target="#{CarteirasControle.filtroCarteira.todosClientes}"/>
                    </a4j:commandLink>
                </h:panelGroup>

                <h:panelGroup styleClass="flex" style="margin-left: 120px;align-items: baseline;"
                              rendered="#{!CarteirasControle.filtroCarteira.todosClientes}" layout="block">
                    <h:outputText
                            style="font-family: Arial; margin-right: 20px;font-style: normal;font-weight: normal;font-size: 14px;line-height: 21px;color: #51555A;"
                            value="Período de Cadastro"/>
                    <h:panelGroup styleClass="dateTimeCustom" style="font-size: 11px !important;">
                        <rich:calendar value="#{CarteirasControle.filtroCarteira.inicioCadastro}"
                                       inputSize="10"
                                       inputClass="form"
                                       buttonIcon="/imagens_flat/calendar-button.svg"
                                       oninputblur="blurinput(this);"
                                       oninputfocus="focusinput(this);"
                                       oninputchange="return validar_Data(this.id);"
                                       datePattern="dd/MM/yyyy"
                                       enableManualInput="true"
                                       zindex="2"
                                       showWeeksBar="false"/>
                    </h:panelGroup>
                    <h:outputText value="a"
                                  style="font-family: Arial;font-style: normal;font-weight: normal;font-size: 18px;line-height: 21px;color: #51555A;margin: 0 10px 0 10px;"/>
                    <h:panelGroup styleClass="dateTimeCustom" style="font-size: 11px !important;">
                        <rich:calendar value="#{CarteirasControle.filtroCarteira.fimCadastro}"
                                       inputSize="10"
                                       inputClass="form"
                                       buttonIcon="/imagens_flat/calendar-button.svg"
                                       oninputblur="blurinput(this);"
                                       oninputfocus="focusinput(this);"
                                       oninputchange="return validar_Data(this.id);"
                                       datePattern="dd/MM/yyyy"
                                       enableManualInput="true"
                                       zindex="2"
                                       showWeeksBar="false"/>
                    </h:panelGroup>
                </h:panelGroup>
            </h:panelGrid>

            <h:panelGroup id="containerFiltrosAvancados">
                <h:panelGroup rendered="#{CarteirasControle.apresentarFiltrosAvancados}">
                    <h:panelGroup id="filtrosAvancado"
                                  layout="block">
                        <rich:dataTable id="filtrosAvancadosCarteiras"
                                        styleClass="tabelaSimplesCustom showCellEmpty "
                                        style="height: 260px;margin-top: 25px;
                                        text-align-last: start; box-sizing: border-box; border-collapse: collapse;
                                        border-bottom: 1px solid #DCDDDF;" rows="5"
                                        value="#{CarteirasControle.filtrosAvancados}" var="filtro">
                            <rich:column
                                    style="border-left: 1px solid #DCDDDF; border-bottom: none;vertical-align: baseline;">
                                <f:facet name="header">
                                    <h:outputText value="FILTRAR POR" styleClass="texto"
                                                  style="font-size: 12px; line-height: 14px;"/>
                                </f:facet>

                                <h:panelGroup layout="block" styleClass="pure-control-group">
                                    <h:selectOneMenu onblur="blurinput(this);"
                                                     styleClass="inputTextClean texto-cor-cinza texto-font"
                                                     style="font-size: 12px !important; width: 197px; height: 33px;"
                                                     onfocus="focusinput(this);"
                                                     id="infoPreenchida"
                                                     value="#{filtro.campoGR.coluna}">
                                        <f:selectItems value="#{CarteirasControle.listaCamposFiltroAvancado}"/>
                                        <a4j:support action="#{CarteirasControle.atualizarCondicoes}"
                                                     event="onchange" reRender="condPreenchida, entradas"/>
                                    </h:selectOneMenu>
                                </h:panelGroup>
                            </rich:column>

                            <rich:column style="border-bottom: none;vertical-align: baseline;">
                                <f:facet name="header">
                                    <h:outputText value="CONDIÇÃO" styleClass="texto"
                                                  style="font-size: 12px; line-height: 14px;"/>

                                </f:facet>
                                <h:selectOneMenu onblur="blurinput(this);"
                                                 styleClass="inputTextClean texto-cor-cinza texto-font"
                                                 style="font-size: 12px !important; width: 197px; height: 33px;"
                                                 onfocus="focusinput(this);"
                                                 id="condPreenchida"
                                                 value="#{filtro.condicao}">
                                    <f:selectItem itemValue="" itemLabel="-"/>
                                    <f:selectItems value="#{filtro.condicoesPermitidas}"/>
                                    <a4j:support action="#{CarteirasControle.atualizarEntradas}"
                                                 event="onchange" reRender="entradas"/>
                                </h:selectOneMenu>
                            </rich:column>

                            <rich:column style="border-bottom: none;vertical-align: baseline;">
                                <f:facet name="header">
                                    <h:outputText value="PERÍODO/VALOR" styleClass="texto"
                                                  style="font-size: 12px; line-height: 14px;"/>
                                </f:facet>
                                <h:panelGroup id="entradas" layout="block" styleClass="pure-u-1">
                                    <h:panelGroup style="text-align-last: center;" layout="block" styleClass="flex">
                                        <h:panelGroup styleClass="dateTimeCustom" rendered="#{filtro.apresentarValor1}"
                                                      id="valor1"
                                                      style="font-size: 11px !important;">
                                            <rich:calendar value="#{filtro.valor1}"
                                                           id="calendario1"
                                                           inputSize="10"
                                                           inputClass="form"
                                                           buttonIcon="/imagens_flat/calendar-button.svg"
                                                           oninputblur="blurinput(this);"
                                                           oninputfocus="focusinput(this);"
                                                           oninputchange="return validar_Data(this.id);"
                                                           datePattern="dd/MM/yyyy"
                                                           enableManualInput="true"
                                                           zindex="2"
                                                           showWeeksBar="false"/>
                                        </h:panelGroup>

                                        <h:panelGroup layout="block" styleClass="flex"
                                                      id="valor2"
                                                      rendered="#{filtro.apresentarValor2}">
                                            <h:outputText value="a" styleClass="texto"
                                                          style="margin: 5px 10px 0 10px; font-size: 16px;"/>

                                            <h:panelGroup styleClass="dateTimeCustom"
                                                          style="font-size: 11px !important;">
                                                <rich:calendar value="#{filtro.valor2}"
                                                               id="calendario2"
                                                               inputSize="10"
                                                               inputClass="form"
                                                               buttonIcon="/imagens_flat/calendar-button.svg"
                                                               oninputblur="blurinput(this);"
                                                               oninputfocus="focusinput(this);"
                                                               oninputchange="return validar_Data(this.id);"
                                                               datePattern="dd/MM/yyyy"
                                                               enableManualInput="true"
                                                               zindex="2"
                                                               showWeeksBar="false"/>
                                            </h:panelGroup>
                                        </h:panelGroup>

                                        <h:panelGroup rendered="#{filtro.apresentarListaChaveEstrangeira}">
                                            <h:selectOneMenu style="width:180px;" value="#{filtro.valor1}"
                                                             onblur="blurinput(this);"
                                                             id="apresentarChaveEstrangeira"
                                                             onfocus="focusinput(this);" styleClass="form">
                                                <f:selectItem itemValue="" itemLabel="-"/>
                                                <f:selectItems
                                                        value="#{filtro.campoGR.valoresChaveEstrangeira}"/>
                                            </h:selectOneMenu>
                                        </h:panelGroup>
                                    </h:panelGroup>
                                </h:panelGroup>
                            </rich:column>

                            <rich:column style="border-bottom: none;vertical-align: baseline;">
                                <f:facet name="header">
                                    <h:outputText value="AGRUPADOR" styleClass="texto"
                                                  style="font-size: 12px; line-height: 14px;"/>
                                </f:facet>
                                <h:selectOneMenu onblur="blurinput(this);"
                                                 styleClass="inputTextClean texto-cor-cinza texto-font"
                                                 style="font-size: 12px !important; width: 68px; height: 33px;"
                                                 onfocus="focusinput(this);"
                                                 id="agrupador" value="#{filtro.agrupador}"
                                                 rendered="#{filtro.permiteAgrupador}">
                                    <f:selectItem itemValue="AND" itemLabel="E"/>
                                    <f:selectItem itemValue="OR" itemLabel="OU"/>
                                </h:selectOneMenu>

                            </rich:column>

                            <rich:column
                                    style="border-right: 1px solid #DCDDDF; border-bottom: none;vertical-align: baseline;">
                                <f:facet name="header">
                                    <h:outputText value="AÇÃO" styleClass="texto"
                                                  style="font-size: 12px; line-height: 14px;"/>
                                </f:facet>
                                <a4j:commandLink id="btnAdicionarItem"
                                                 styleClass="linkPadrao"
                                                 action="#{CarteirasControle.adicionarItemFiltroAvancado}"
                                                 reRender="filtrosAvancado"
                                                 title="Adicionar Item">
                                    <i class="fa-icon-plus-sign" style="color: #2EC750;"></i>
                                </a4j:commandLink>
                                <a4j:commandLink id="btnRemoverCheque"
                                                 styleClass="linkPadrao"
                                                 rendered="#{CarteirasControle.desenharBotao}"
                                                 action="#{CarteirasControle.removerItemFiltroAvancado}"
                                                 reRender="filtrosAvancado"
                                                 title="Excluir Item">
                                    <i class="fa-icon-minus-sign" style="color: #DB2C3D;"></i>
                                </a4j:commandLink>
                            </rich:column>
                        </rich:dataTable>
                    </h:panelGroup>

                    <h:panelGroup id="btsMdlFiltrosGestaoCar" layout="block"
                                  style="width: 100%; margin-top: 30px; text-align: end">
                        <a4j:commandLink id="fecharFiltroGestaoCart" value="Fechar"
                                         style="border-radius: 3px !important;"
                                         styleClass="pure-button margin-h-10"
                                         onclick="Richfaces.hideModalPanel('modalFiltroGestaoCarteiras');"/>

                        <a4j:commandLink id="limparFiltroGestaoCart" value="Limpar"
                                         style="border-radius: 3px !important;"
                                         styleClass="pure-button margin-h-10"
                                         reRender="filtrosAvancado"
                                         oncomplete="#{CarteirasControle.mensagemNotificar}"
                                         action="#{CarteirasControle.limparFiltrosAvancados}"/>

                        <a4j:commandLink action="#{CarteirasControle.aplicarFiltrosAvancados}"
                                         oncomplete="#{CarteirasControle.mensagemNotificar};#{CarteirasControle.msgAlert}"
                                         style="border-radius: 3px !important;"
                                         id="aplicarFiltroGestaoCart"
                                         value="Aplicar"
                                         styleClass="pure-button pure-button-primary margin-h-10">
                        </a4j:commandLink>
                    </h:panelGroup>
                </h:panelGroup>
            </h:panelGroup>
        </h:panelGroup>
    </a4j:form>
</rich:modalPanel>

<rich:modalPanel id="modalReverterCarteira" autosized="true" styleClass="novaModal noMargin"
                 shadowOpacity="true" width="919" height="300">

    <f:facet name="header">
        <h:panelGroup>
            <h:outputText value="Reverter Transferência"
                          style="font-family: Arial;font-style: normal;font-weight: bold;font-size: 16px;line-height: 28px;"/>
        </h:panelGroup>
    </f:facet>

    <a4j:form id="formReverTerCarteira">

        <h:panelGroup
                style="margin: 1vh;font-family: Arial;font-style: normal;font-weight: normal;font-size: 14px;line-height: 21px;color: #51555A; margin-left: 30px;"
                layout="block">
                               <span class="tooltipster" style="margin-top: 2vh" title="Ao realizar esta operação, todas ações de transferência realizada pelo usuário que você informar e no dia que você informar, será revertida.</br>
                             Ou seja, o vínculo de consultor do aluno será ajustado voltando para o último consultor SAÍDA do histórico de vínculos do aluno.</br>
                              EX. Se no dia 01/01/2020 dia que você informou no processo aqui em baixo. O consultor SAÍDA lá no histórico de vínculo do aluno é o FULANO. E hoje ele está com o CICLANO. Então, ele voltará para o FULANO.">Reverter transferência de carteira realizada.</span>
        </h:panelGroup>
        <h:panelGroup layout="block" style="margin-top: 3vh; margin-left: 30px" id="containerProcessosAvancados">
            <h:panelGroup style="margin: 10px;">
                <h:outputText style="margin: 10px;font-family: Arial;font-style: normal;font-weight: normal;font-size: 14px;line-height: 21px;color: #51555A;"
                              value="Dia em que foi realizada "/>
                <h:panelGroup styleClass="dateTimeCustom"
                              style="font-size: 12px">
                    <rich:calendar value="#{CarteirasControle.dataOperacaoTransarencia}"
                                   id="dataInicial"
                                   inputSize="10"
                                   inputClass="form"
                                   oninputblur="blurinput(this);"
                                   oninputfocus="focusinput(this);"
                                   oninputchange="return validar_Data(this.id);"
                                   datePattern="dd/MM/yyyy"
                                   buttonIcon="/imagens_flat/calendar-button.svg"
                                   enableManualInput="true"
                                   zindex="2"
                                   showWeeksBar="false"/>
                </h:panelGroup>
            </h:panelGroup>


            <h:panelGroup layout="block" styleClass="pure-control-group" style="margin: 10px;">
                <h:outputText
                        style="margin: 10px;font-family: Arial;font-style: normal;font-weight: normal;font-size: 14px;line-height: 21px;color: #51555A;"
                        value="Por quem foi realizada "/>
                <h:selectOneMenu onblur="blurinput(this);"
                                 styleClass="inputTextClean texto-cor-cinza texto-font"
                                 style="width: 361px; height: 33px;margin-left: 14px;"
                                 onfocus="focusinput(this);"
                                 value="#{CarteirasControle.codUsuarioResponsavelTransf}">
                    <f:selectItem itemValue="" itemLabel="-"/>
                    <f:selectItems value="#{CarteirasControle.selectUsuariosOperacaoTransf}"/>
                </h:selectOneMenu>
            </h:panelGroup>


            <h:panelGroup id="btsMdlReverterCarteira" layout="block" style="width: 100%; text-align: end; margin-top: 25px;">
                <a4j:commandLink id="fecharFiltroGestaoCart" value="Fechar"
                                 style="border-radius: 3px !important;"
                                 styleClass="pure-button margin-h-10"
                                 onclick="Richfaces.hideModalPanel('modalReverterCarteira')"/>

                <a4j:commandLink style="margin-left: 2vh" id="btnRevert" value="Reverter"
                                 styleClass="pure-button pure-button-primary margin-h-10"
                                 action="#{CarteirasControle.reverterTransferencia}"
                                 oncomplete="#{CarteirasControle.mensagemNotificar}"
                                 reRender="mensagens"/>
            </h:panelGroup>
        </h:panelGroup>
    </a4j:form>

</rich:modalPanel>
