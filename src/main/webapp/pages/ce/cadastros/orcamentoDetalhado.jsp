<%@page contentType="text/html" pageEncoding="ISO-8859-1" %>
<%@include file="../includes/include_imports.jsp" %>

<f:view>
    <jsp:include page="/includes/include_carregando_ripple.jsp" flush="true"/>

    <script>
        function noEnter(e) {
            alert('as');
            var event = e || window.event;
            if (event.keyCode == 13) {
                return false;
            } else {
                return mascara(objForm, '99999', e);
            }

        }
    </script>
    <html>
        <!-- Inclui o elemento HEAD da página -->
        <%@include file="/pages/ce/includes/include_head.jsp" %>


        <script type="text/javascript" language="javascript" src="../../../script/alterarValores.js"></script>
        <script type="text/javascript" language="javascript" src="../../../script/orcamentoDetalhado.js"></script>

        <body>

            <h:form id="form">
                <a4j:keepAlive beanName="OrcamentoDetalhadoControle" />
                <h:panelGroup layout="block" style="display: inline-table;width: 100%;" styleClass="fundoCinza">
                    <h:panelGroup layout="block" styleClass="bgtop topoZW" rendered="#{MenuControle.apresentarTopo}">
                        <jsp:include page="../../../include_topo_2.0.jsp" flush="true"/>
                        <jsp:include page="../../../include_menu_ce_flat.jsp" flush="true" />
                    </h:panelGroup>

                    <h:panelGroup layout="block" styleClass="caixaCorpo">
                        <h:panelGroup layout="block" style="height: 80%;width: 100%">
                            <h:panelGroup layout="block" styleClass="caixaMenuLatel">
                                <h:panelGroup layout="block" styleClass="container-imagem container-conteudo-central">
                                    <h:panelGroup layout="block" styleClass="container-box zw_ui especial ">
                                        <h:panelGroup styleClass="container-box-header" layout="block">
                                            <h:panelGroup layout="block" styleClass="margin-box">
                                                <h:outputText styleClass="container-header-titulo" value="#{CElabels['menu.operacoesCE.orcamentoDetalhado']}"/>
                                                <h:outputLink styleClass="linkWiki"
                                                              value="#{SuperControle.urlWikiCE}Operações:Orçamento_Detalhado"
                                                              title="Wiki"
                                                              target="_blank" >
                                                    <i class="fa-icon-question-sign" style="font-size: 18px"></i>
                                                </h:outputLink>
                                            </h:panelGroup>
                                        </h:panelGroup>
                                        <h:panelGroup layout="block" styleClass="margin-box">
                                            <h:panelGrid id="conteudo" columns="2" width="100%" border="0">

                                                <h:inputHidden id="ambienteSelec" value="#{OrcamentoDetalhadoControle.ambienteSelecionado}" />
                                                <h:inputHidden id="condicaoPagamentoSelec" value="#{OrcamentoDetalhadoControle.condicaoPagamentoSelecionada}" />

                                                <rich:column width="60%" style="border: 0px; vertical-align: top;">
                                                    <h:panelGrid columns="1" styleClass="tablepadding2 textsmall" width="100%" border="0"
                                                                 style="margin-bottom:5px; text-align: left; vertical-align: top;" cellpadding="0" cellspacing="0">
                                                        <h:panelGroup>

                                                            <a4j:commandButton id="dadosEvento" value="#{CElabels['entidade.evento.exibir']}"
                                                                               image="/imagens/botoesCE/dados_do_evento.png"
                                                                               reRender="panelDetalheEvento" action="#{OrcamentoDetalhadoControle.exibirDetalhesEvento}"
                                                                               rendered="#{!OrcamentoDetalhadoControle.orcamentoFicticio}"
                                                                               oncomplete="Richfaces.showModalPanel('panelDetalheEvento');" /> &nbsp;
                                                            <a4j:commandButton id="voltar" action="cadastroInicial"
                                                                               image="/imagens/botoesCE/voltar_sem_fundo.png"
                                                                               value="#{CElabels['operacoes.voltar']}"
                                                                               rendered="#{OrcamentoDetalhadoControle.orcamentoFicticio && OrcamentoDetalhadoControle.origemDetalhamentoEvento}"
                                                                               styleClass="botoes" />
                                                        </h:panelGroup>

                                                        <!-- EMPRESA -->

                                                        <h:panelGroup rendered="#{OrcamentoDetalhadoControle.usuarioLogado.administrador}">
                                                            <img src="${contexto}/images/arrow2.gif" width="16" height="16" style="vertical-align:middle;margin-right:6px;">
                                                            <h:outputText value="#{CElabels['menu.cadastros.acessoSistema.empresa']}: "/>
                                                            &nbsp;<%@include file="../includes/include_obrigatorio.jsp" %>
                                                            <div class="sep" style="margin:4px 0 5px 0;"><img src="${contexto}/images/shim.gif"></div>
                                                        </h:panelGroup>
                                                        <h:panelGrid id="panelEmpresa" columns="1" border="0" width="100%"
                                                                     rendered="#{OrcamentoDetalhadoControle.usuarioLogado.administrador}">
                                                            <h:panelGroup>
                                                                <h:outputText styleClass="text" style="font-weight: bold" value="#{CElabels['menu.cadastros.acessoSistema.empresa']}: "/>
                                                                <h:selectOneMenu value="#{OrcamentoDetalhadoControle.negociacaoEvento.empresa.codigo}">
                                                                    <f:selectItems value="#{OrcamentoDetalhadoControle.listaSelectItemEmpresa}"/>
                                                                </h:selectOneMenu>
                                                            </h:panelGroup>
                                                        </h:panelGrid>

                                                        <!-- INICIO - SELECAO DE DATA/HORARIO E PERFIL-->
                                                        <h:panelGroup>
                                                            <img src="${contexto}/images/arrow2.gif" width="16" height="16" style="vertical-align:middle;margin-right:6px;">
                                                            <h:outputText value="#{CElabels['entidade.dataHorario']}: "/>
                                                            &nbsp;<%@include file="../includes/include_obrigatorio.jsp" %>
                                                            <div class="sep" style="margin:4px 0 5px 0;"><img src="${contexto}/images/shim.gif"></div>
                                                        </h:panelGroup>
                                                        <h:panelGrid id="panelHorasData" columns="1" border="0" width="100%" columnClasses="colunaLeft, colunaRight">
                                                            <h:column>
                                                                <h:panelGroup>
                                                                    <h:outputText styleClass="text" style="font-weight: bold" value="#{CElabels['entidade.evento.data']}: "/>
                                                                    <rich:calendar id="dataEvento"
                                                                                   styleClass="form"
                                                                                   value="#{OrcamentoDetalhadoControle.negociacaoEvento.dataEvento}"
                                                                                   datePattern="dd/MM/yyyy" inputSize="10" inputClass="form"
                                                                                   oninputblur="document.getElementById('form:comboPerfilEvento').focus();blurinput(this); limparMsgObrig('form:dataValidadeInputDate', ['dataValidade','dataValidade-msgs']); VerificaData(this.value);"
                                                                                   oninputfocus="focusinput(this); "
                                                                                   oninputkeypress="return mascara(this.form, this.id, '99/99/9999', event);"
                                                                                   enableManualInput="true" zindex="2" showWeeksBar="false">
                                                                        <a4j:support event="oninputchange"
                                                                                     action="#{OrcamentoDetalhadoControle.selecionarDataEvento}"
                                                                                     reRender="conteudo" focus="comboPerfilEvento"></a4j:support>
                                                                        <a4j:support event="onchanged"
                                                                                     action="#{OrcamentoDetalhadoControle.selecionarDataEvento}"
                                                                                     reRender="conteudo" focus="comboPerfilEvento"></a4j:support>
                                                                    </rich:calendar>
                                                                    <div id="divMsg-bensConsumo" class="mensagemObrigatorio">
                                                                        <h:outputText styleClass="mensagemObrigatorio" id="msgDataMenorAtual" rendered="#{OrcamentoDetalhadoControle.dataEventoMenorAtual}" value="#{Mensagens['operacoes.data.evento.menor']}"></h:outputText>
                                                                    </div>

                                                                </h:panelGroup>
                                                            </h:column>

                                                            <h:column>
                                                                <h:outputText rendered="#{OrcamentoDetalhadoControle.negociacaoEvento.horarioInicialString != null}"
                                                                              styleClass="text"
                                                                              style="font-weight: bold"
                                                                              value="#{CElabels['entidade.horario.inicial']}: #{OrcamentoDetalhadoControle.negociacaoEvento.horarioInicialString}"/>
                                                                <rich:spacer  width="15px"
                                                                              rendered="#{OrcamentoDetalhadoControle.negociacaoEvento.horarioFinalExibicaoString != null}"></rich:spacer>
                                                                <h:outputText styleClass="text" style="font-weight: bold"
                                                                              rendered="#{OrcamentoDetalhadoControle.negociacaoEvento.horarioFinalExibicaoString != null}"
                                                                              value="#{CElabels['entidade.horario.final']}: #{OrcamentoDetalhadoControle.negociacaoEvento.horarioFinalExibicaoString}"/>
                                                            </h:column>

                                                            <h:column>
                                                                <h:panelGroup>
                                                                    <h:outputText styleClass="text" style="font-weight: bold" value="#{CElabels['entidade.perfilEvento']}: "/>
                                                                    <h:selectOneMenu  style="width: 130px;" id="comboPerfilEvento" onblur="blurinput(this);" onfocus="focusinput(this);"
                                                                                      styleClass="form" value="#{OrcamentoDetalhadoControle.negociacaoEvento.perfilEventoTO.codigo}">
                                                                        <a4j:support event="onchange" action="#{OrcamentoDetalhadoControle.carregarPerfilEvento}"
                                                                                     reRender="conteudo,detalhamentoNegociacao"
                                                                                     actionListener="#{OrcamentoDetalhadoControle.selPerfilEvento}"
                                                                                     oncomplete="document.getElementById('form:ambientes:0:comboBoxAmbientes').focus();limparMsgObrig('form:comboPerfilEvento', ['perfilEvento','perfilEvento-msgs']);"/>
                                                                        <f:selectItem itemValue="" itemLabel="#{CElabels['operacoes.selecione']}"/>
                                                                        <f:selectItems value="#{OrcamentoDetalhadoControle.itensPerfisEvento}" />
                                                                    </h:selectOneMenu>
                                                                </h:panelGroup>
                                                                <c:if test="${OrcamentoDetalhadoControle.novoOrcamento}">
                                                                    <div  id="perfisComAmbiente">


                                                                        <c:if test="${not empty OrcamentoDetalhadoControle.perfisComAmbienteFormatado}">
                                                                            <h:outputText styleClass="mensagemAzul"
                                                                                          value="#{CElabels['operacoes.oAmbiente']} : #{OrcamentoDetalhadoControle.ambienteInteresse.descricao} #{CElabels['operacoes.estaContido']}: #{OrcamentoDetalhadoControle.perfisComAmbienteFormatado}"/>
                                                                        </c:if>
                                                                        <c:if test="${OrcamentoDetalhadoControle.perfisComAmbienteFormatado eq null}">
                                                                            <h:outputText styleClass="mensagemAzul"
                                                                                          value="#{CElabels['operacoes.oAmbiente']} : #{OrcamentoDetalhadoControle.ambienteInteresse.descricao} #{CElabels['operacoes.naoEstaContido']}"/>
                                                                        </c:if>



                                                                    </div>
                                                                </c:if>
                                                            </h:column>


                                                        </h:panelGrid>
                                                        <!-- FIM - SELECAO DE DATA/HORARIO E PERFIL-->

                                                        <h:panelGroup id="painelPerfilSelecionado" rendered="#{OrcamentoDetalhadoControle.perfilSelecionado}">



                                                            <!-- INICIO  - SELECAO DE AMBIENTE-->


                                                            <div id="divObg-perfilEvento-msgs" class="mensagemObrigatorio"></div>

                                                            <h:panelGroup>
                                                                <img src="${contexto}/images/arrow2.gif" width="16" height="16" style="vertical-align:middle;margin-right:6px;">
                                                                <h:outputText value="#{CElabels['entidade.ambientes']}:" />
                                                                &nbsp;<%@include file="../includes/include_obrigatorio.jsp" %>
                                                                <div class="sep" style="margin:4px 0 5px 0;"><img src="${contexto}/images/shim.gif"></div>
                                                            </h:panelGroup>
                                                            <h:panelGrid styleClass="tablepadding2" columns="1" width="100%">

                                                                <a4j:commandButton reRender="panelMesangem, detalhamentoNegociacao,  panelHorasData, ambientes"
                                                                                   action="#{OrcamentoDetalhadoControle.validarAmbientes}"
                                                                                   id="atualizarAmbientes"
                                                                                   status="false"
                                                                                   style="display: none;"></a4j:commandButton>

                                                                <rich:dataTable id="ambientes" width="100%" rowClasses="linhaImpar" columnClasses="colunaEsquerda"
                                                                                value="#{OrcamentoDetalhadoControle.ambientes}" styleClass="spinner" var="ambiente">
                                                                    <rich:column>
                                                                        <table width="100%">
                                                                            <tr>
                                                                                <!-- PRIMEIRA COLUNA -->
                                                                                <td valign="top" width="50%">

                                                                                    <h:panelGrid columns="2">
                                                                                        <h:outputText styleClass="text" style="font-weight: bold" value="#{CElabels['entidade.ambiente']}: "/>

                                                                                        <h:selectOneMenu onblur="blurinput(this);" onfocus="focusinput(this);"
                                                                                                         styleClass="form" value="#{ambiente.codigoAmbiente}"
                                                                                                         id="comboBoxAmbientes">
                                                                                            <a4j:support event="onchange" action="#{OrcamentoDetalhadoControle.selecionarAmbiente}"
                                                                                                         reRender="ambientes, ambienteSelec, detalhamentoNegociacao"
                                                                                                         focus="disponibilidadeEventos">
                                                                                                <f:attribute name="codAmbienteSelecionado" value="#{ambiente.codigoAmbiente}" />
                                                                                            </a4j:support>
                                                                                            <f:selectItem itemValue="" itemLabel="#{CElabels['operacoes.selecione']}"/>
                                                                                            <f:selectItems value="#{OrcamentoDetalhadoControle.itensAmbientes}" />
                                                                                        </h:selectOneMenu>
                                                                                    </h:panelGrid>


                                                                                    &nbsp;<a4j:commandLink id="disponibilidadeEventos"
                                                                                                           reRender="panelDisponibilidade, ambientes" action="#{ConsultaEventosControle.consultarDispAmbOrcamentoDetalhado}"
                                                                                                           actionListener="#{OrcamentoDetalhadoControle.verificarDisponibilidadeAmbienteOrcDet}"
                                                                                                           oncomplete="Richfaces.showModalPanel('panelDisponibilidade');"
                                                                                                           focus="horaInicial">
                                                                                    <h:graphicImage value="/imagens/botoesCE/Verificar_disponibilidade.png" style="border: 0px;" alt="#{CElabels['operacoes.disponibilidade.verificar']}"/>
                                                                                </a4j:commandLink><br/>



                                                                                    <h:panelGroup rendered="#{not empty ambiente.tpAmbienteTO.duracaoMinimaHrs}">

                                                                                        &nbsp;<h:outputText styleClass="mensagemAzul" value="O horário mínimo para ambiente é #{ambiente.tpAmbienteTO.duracaoMinimaHrs} hrs." />

                                                                                    </h:panelGroup>



                                                                                    <h:panelGroup rendered="#{ambiente.vlrSazonalidade > 0}">
                                                                                        &nbsp;<h:outputText styleClass="mensagemAzul" value="Acréscimo de valor #{ambiente.vlrSazonalidadeString} referente a sazonalidade do ambiente." />
                                                                                        <br/>
                                                                                    </h:panelGroup>
                                                                                    <h:panelGroup rendered="#{ambiente.vlrSazonalidade < 0}">
                                                                                        &nbsp;<h:outputText styleClass="mensagemAzul" value="Decréscimo de valor #{ambiente.vlrSazonalidadeString} referente a sazonalidade do ambiente." />
                                                                                        <br/>
                                                                                    </h:panelGroup>

                                                                                    <h:panelGrid columns="2">
                                                                                        <h:outputText styleClass="text" style="font-weight: bold" value="#{CElabels['entidade.horario.inicial']}: "/>
                                                                                        <h:panelGroup>
                                                                                            <h:inputText  id="horaInicial" onfocus="focusinput(this);"
                                                                                                          styleClass="form" maxlength="7" value="#{ambiente.horarioInicialString}"
                                                                                                          size="7" onkeypress="return mascarahora(this, '99:99', event);"
                                                                                                          onblur="document.getElementById('form:atualizarAmbientes').click();">
                                                                                            </h:inputText>
                                                                                        </h:panelGroup>



                                                                                        <h:outputText styleClass="text" style="font-weight: bold" value="#{CElabels['entidade.horario.final']}: "/>
                                                                                        <h:panelGroup>
                                                                                            <h:inputText id="horaFinal" onfocus="focusinput(this);"
                                                                                                         styleClass="form" maxlength="7" value="#{ambiente.horarioFinalString}"
                                                                                                         size="7" onkeypress="return mascarahora(this, '99:99', event);"
                                                                                                         onblur="document.getElementById('form:atualizarAmbientes').click();">
                                                                                            </h:inputText>
                                                                                        </h:panelGroup>


                                                                                    </h:panelGrid>
                                                                                </td>

                                                                                <!-- SEGUNDA COLUNA -->
                                                                                <td valign="top" width="50%">
                                                                                    <h:panelGrid columns="1">
                                                                                        <h:panelGroup>
                                                                                            <h:outputText styleClass="text" style="font-weight: bold" value="#{ambiente.valorMonetario}" />
                                                                                            <br/>
                                                                                            <!-- INICIO - DESCONTO EM AMBIENTE -->

                                                                                            <h:outputText styleClass="text" style="font-weight: bold" value="#{CElabels['menu.cadastros.perfisEventos.descontos']}: "></h:outputText>
                                                                                            <h:outputText styleClass="text" style="font-weight: bold" value="#{ambiente.descontoApresentar}  " />
                                                                                            &nbsp;
                                                                                            <a4j:commandButton  image="/images/icon_calculadora.png"
                                                                                                                title="#{CElabels['menu.cadastros.perfisEventos.descontos']}"
                                                                                                                action="#{OrcamentoDetalhadoControle.informarDescontoAmbiente}"
                                                                                                                reRender="panelSenhaDescontoAmbiente"
                                                                                                                oncomplete="Richfaces.showModalPanel('panelSenhaDescontoAmbiente');"/>
                                                                                            <rich:spacer width="8px"></rich:spacer>
                                                                                            <a4j:commandButton  image="/images/bt_remove.png"
                                                                                                                rendered="#{ambiente.desconto > 0}"
                                                                                                                title="#{CElabels['menu.cadastros.perfisEventos.removerDescontos']}"
                                                                                                                action="#{OrcamentoDetalhadoControle.removerDescontoAmbiente}"
                                                                                                                reRender="detalhamentoNegociacao,ambientes"/>
                                                                                            <br/>
                                                                                            <h:outputText rendered="#{ambiente.nrMaximoConvidado == null || ambiente.nrMaximoConvidado == 0}" styleClass="mensagemAzul" value="#{CElabels['entidade.negociacao.selecioneConvidados']}" />

                                                                                            <!-- FIM - DESCONTO EM AMBIENTE -->
                                                                                        </h:panelGroup>
                                                                                        <h:panelGroup rendered="#{ambiente.tipoDesconto != null}">

                                                                                            <h:outputText styleClass="mensagemAzul" value="#{CElabels['entidade.ambiente.valorComDesconto']}: #{ambiente.valorDescontado}" />
                                                                                        </h:panelGroup>

                                                                                    </h:panelGrid>
                                                                                    <h:panelGrid columns="2">

                                                                                        <h:outputText styleClass="text" style="font-weight: bold" value="Layout: "/>
                                                                                        <h:selectOneMenu  id="comboLayouts" onblur="blurinput(this);" onfocus="focusinput(this);"
                                                                                                          styleClass="form" value="#{ambiente.tipoLayout.codigo}">
                                                                                            <f:selectItem itemValue="0" itemLabel="#{CElabels['operacoes.selecione']}"/>
                                                                                            <f:selectItems value="#{ambiente.layouts}" />
                                                                                        </h:selectOneMenu>



                                                                                        <h:outputText styleClass="text"  style="font-weight: bold" value="#{CElabels['entidade.nrConv']}: "/>


                                                                                        <h:inputText onblur="blurinput(this);"
                                                                                                     id= "nrconvidados"
                                                                                                     onfocus="focusinput(this);" size="7"
                                                                                                     styleClass="form" maxlength="7"
                                                                                                     value="#{ambiente.nrMaximoConvidado}"
                                                                                                     onkeypress="return mascara(this.form, this.id, '99999', event);">
                                                                                            <a4j:support event="onchange"
                                                                                                         action="#{OrcamentoDetalhadoControle.escolheNrConvidados}"
                                                                                                         reRender="ambientes, ambienteSelec, detalhamentoNegociacao"/>
                                                                                        </h:inputText>


                                                                                    </h:panelGrid>
                                                                                    <h:panelGroup rendered="#{ambiente.codigoAmbiente != null && ambiente.codigoAmbiente != 0 }">

                                                                                        <h:outputText styleClass="mensagemAzul" value="Permitido até #{ambiente.nrMaximoConvidadoExibicao} convidados." />

                                                                                    </h:panelGroup>

                                                                                </td>

                                                                            </tr>
                                                                            <!-- LINHA DOS BOTOES  -->

                                                                            <tr>
                                                                                <td>


                                                                                    <h:panelGrid columns="2">
                                                                                        <a4j:commandButton value="#{CElabels['operacoes.adicionar.ambiente']}"
                                                                                                           action="#{OrcamentoDetalhadoControle.novoAmbiente}"
                                                                                                           image="/imagens/botoesCE/adicionar_ambiente.png"
                                                                                                           reRender="panelMesangem, ambientes, detalhamentoNegociacao,  panelHorasData"/>

                                                                                        <a4j:commandButton value="#{CElabels['operacoes.remover']}"
                                                                                                           image="/imagens/botoesCE/remover.png"
                                                                                                           reRender="ambientes,detalhamentoNegociacao, panelHorasData" action="#{OrcamentoDetalhadoControle.removerAmbiente}"/>
                                                                                    </h:panelGrid>

                                                                                </td>
                                                                            </tr>


                                                                        </table>
                                                                        <h:panelGrid columns="1">
                                                                            <h:outputText styleClass="mensagemDetalhada" value="#{ambiente.msg}" />
                                                                        </h:panelGrid>




                                                                        <rich:separator width="515px"/>
                                                                    </rich:column>

                                                                </rich:dataTable>

                                                            </h:panelGrid>

                                                            <div id="divObg-ambiente-msgs" class="mensagemObrigatorio"></div>

                                                            <!-- FIM - SELECAO DE AMBIENTE-->

                                                            <br />

                                                            <!-- INICIO - SELECAO DE SERVICOS-->
                                                            <h:panelGroup>
                                                                <img src="${contexto}/images/arrow2.gif" width="16" height="16" style="vertical-align:middle;margin-right:6px;">
                                                                <h:outputText value="#{CElabels['entidade.servicos']}:" />
                                                                <div class="sep" style="margin:4px 0 5px 0;"><img src="${contexto}/images/shim.gif"></div>
                                                            </h:panelGroup>

                                                            <h:panelGrid id="panelServicos" styleClass="tablepadding2" columns="1" width="100%" style="text-align: center;">
                                                                <a4j:commandButton
                                                                        rendered="#{OrcamentoDetalhadoControle.negociacaoEvento.perfilEventoTO.permiteOutrosServicos}"
                                                                        type="button" reRender="formConsultaServico"
                                                                        onclick="Richfaces.showModalPanel('panelConsultaServico')"
                                                                        title="#{CElabels['operacoes.adicionar.bemConsumo']}"
                                                                        image="/images/icon_add.gif" />

                                                                <rich:dataTable id="servicos" width="100%" rowClasses="linhaImpar"
                                                                                columnClasses="colunaEsquerda, colunaCentralizada"
                                                                                value="#{OrcamentoDetalhadoControle.servicos}"
                                                                                var="servico"
                                                                >

                                                                    <rich:column>
                                                                        <f:facet name="header">
                                                                            <h:outputText
                                                                                    value="#{CElabels['entidade.descricao']}"/>
                                                                        </f:facet>
                                                                        <h:selectBooleanCheckbox value="#{servico.selecionado}"
                                                                                                 disabled="#{servico.obrigatorio}">
                                                                            <a4j:support event="onchange"
                                                                                         action="#{OrcamentoDetalhadoControle.selecionarServico}"
                                                                                         reRender="detalhamentoNegociacao, servicos"/>
                                                                        </h:selectBooleanCheckbox>
                                                                        <h:outputText value="#{servico.descricaoServico}"/>
                                                                    </rich:column>

                                                                    <rich:column>
                                                                        <f:facet name="header">
                                                                            <h:outputText
                                                                                    value="#{CElabels['entidade.fornecedor']}"/>
                                                                        </f:facet>
                                                                        <h:selectOneMenu
                                                                                disabled="#{!servico.selecionado}"
                                                                                onblur="blurinput(this); limparMsgObrig('form:fornecedor', 'fornecedor');"
                                                                                onfocus="focusinput(this);" styleClass="form"
                                                                                id="fornecedor"
                                                                                valueChangeListener="#{OrcamentoDetalhadoControle.fornecedorListener}"
                                                                                style="width: 200px;"
                                                                                value="#{servico.codigoFornecedor}">
                                                                            <f:selectItem itemValue="0"
                                                                                          itemLabel="--Selecione--"/>
                                                                            <f:selectItems
                                                                                    value="#{OrcamentoDetalhadoControle.listaFornecedor}"/>
                                                                        </h:selectOneMenu>
                                                                    </rich:column>

                                                                    <rich:column>
                                                                        <a4j:commandLink
                                                                                action="#{OrcamentoDetalhadoControle.exibirTextoLivreServico}"
                                                                                oncomplete="Richfaces.showModalPanel('panelTextoLivreItem')"
                                                                                reRender="panelTextoLivreItem"
                                                                                onmouseover="toolTip('#{CElabels['entidade.textoLivre']}' , 120 , 'gray')"
                                                                                onmouseout="hideToolTip();">
                                                                            <h:graphicImage
                                                                                    value="/imagens/botoesCE/texto_livre.png"
                                                                                    style="border: 0;"
                                                                                    alt="#{CElabels['entidade.textoLivre']}"/>
                                                                        </a4j:commandLink>
                                                                    </rich:column>

                                                                    <rich:column>
                                                                        <f:facet name="header">
                                                                            <h:outputText
                                                                                    value="#{CElabels['entidade.quantidade']}"/>
                                                                        </f:facet>
                                                                        <h:inputText onblur="blurinput(this);"
                                                                                     id="qtdservicos"
                                                                                     onfocus="focusinput(this);" size="7"
                                                                                     styleClass="form" maxlength="7"
                                                                                     value="#{servico.quantidade}"
                                                                                     onkeypress="return mascara(this.form, this.id, '99999', event);"
                                                                                     disabled="#{!servico.selecionado}">
                                                                            <a4j:support event="onchange"
                                                                                         action="#{OrcamentoDetalhadoControle.calcularValorServico}"
                                                                                         reRender="detalhamentoNegociacao, panelServicos"/>
                                                                        </h:inputText>
                                                                    </rich:column>

                                                                    <rich:column>
                                                                        <a4j:commandButton image="/images/icon_chave.png"
                                                                                           title="#{CElabels['operacoes.editar.valor.servico']}"
                                                                                           actionListener="#{OrcamentoDetalhadoControle.selAlteraServico}"
                                                                                           oncomplete="Richfaces.showModalPanel('panelSenhaValorServico');"
                                                                                           reRender="panelSenhaValorServico, servicos">
                                                                        </a4j:commandButton>
                                                                    </rich:column>

                                                                    <rich:column>
                                                                        <f:facet name="header">
                                                                            <h:outputText value="#{CElabels['entidade.valor']}"/>
                                                                        </f:facet>
                                                                        <h:outputText rendered="#{!servico.alteracaoValor}"
                                                                                      value="#{servico.valorUnitarioMonetario}"/>
                                                                        <h:inputText onfocus="focusinput(this);" size="7"
                                                                                     onkeypress="return(currencyFormat(this,'.',',',event));"
                                                                                     rendered="#{servico.alteracaoValor}"
                                                                                     styleClass="form" maxlength="14"
                                                                                     value="#{servico.valorUnitarioFormatado}"
                                                                                     disabled="#{!servico.selecionado}">
                                                                        </h:inputText>
                                                                        <a4j:commandButton rendered="#{servico.alteracaoValor}"
                                                                                           image="/images/tick.png"
                                                                                           title="#{CElabels['operacoes.editar.valor.novo.aplicar']}"
                                                                                           action="#{OrcamentoDetalhadoControle.confirmarAlteracaoValorServico}"
                                                                                           reRender="panelServicos, detalhamentoNegociacao"/>
                                                                    </rich:column>

                                                                    <rich:column>
                                                                        <f:facet name="header">
                                                                            <h:outputText value="#{CElabels['entidade.total']}"/>
                                                                        </f:facet>
                                                                        <h:outputText value="#{servico.valorMonetario}"/>
                                                                    </rich:column>

                                                                </rich:dataTable>
                                                                <div id="divMsg-servico" class="mensagemAviso">
                                                                    <h:outputText styleClass="mensagemAviso" id="msgServico" rendered="#{OrcamentoDetalhadoControle.qtdAlteradaServico}"
                                                                                  value="#{CElabels['menu.operacoesCE.valorAlterado']} #{CElabels['menu.operacoesCE.quantidadesValidas']}: #{OrcamentoDetalhadoControle.faixasServico}"></h:outputText></div>
                                                            </h:panelGrid>
                                                            <!-- FIM - SELECAO DE SERVICOS-->

                                                            <br />

                                                            <!-- INICIO - SELECAO DE SERVICOS TERCEIRIZADO-->
                                                            <h:panelGroup>
                                                                <img src="${contexto}/images/arrow2.gif" width="16" height="16" style="vertical-align:middle;margin-right:6px;">
                                                                <h:outputText value="#{CElabels['entidade.servicoTercerizado']}:" />
                                                                <div class="sep" style="margin:4px 0 5px 0;"><img src="${contexto}/images/shim.gif"></div>
                                                            </h:panelGroup>

                                                            <h:panelGrid id="panelServicosTercerizado" styleClass="tablepadding2" columns="1" width="100%" style="text-align: center;">
                                                                <a4j:commandButton
                                                                        type="button" reRender="formConsultaServicoTercerizado" onclick="Richfaces.showModalPanel('panelConsultaServicoTercerizado')"
                                                                        title="#{CElabels['operacoes.adicionar.bemConsumo']}" image="/images/icon_add.gif"/>

                                                                <rich:dataTable id="servicosTercerizado" width="100%" rowClasses="linhaImpar" columnClasses="colunaEsquerda,,colunaCentralizada,colunaCentralizada"
                                                                                value="#{OrcamentoDetalhadoControle.negociacaoEvento.servicosTerceirizados}" var="servicoTerceirizado">

                                                                    <rich:column>
                                                                        <f:facet name="header">
                                                                            <h:outputText value="#{CElabels['entidade.descricao']}" />
                                                                        </f:facet>
                                                                        <h:selectBooleanCheckbox value="#{servicoTerceirizado.selecionado}">
                                                                        </h:selectBooleanCheckbox>
                                                                        <h:outputText value="#{servicoTerceirizado.descricao}" />
                                                                    </rich:column>

                                                                    <rich:column>
                                                                        <a4j:commandLink action="#{OrcamentoDetalhadoControle.exibirTextoLivreServTerceirizado}"
                                                                                         oncomplete="Richfaces.showModalPanel('panelTextoLivreItem')" reRender="panelTextoLivreItem"
                                                                                         onmouseover="toolTip('#{CElabels['entidade.textoLivre']}' , 120 , 'gray')"
                                                                                         onmouseout="hideToolTip();"
                                                                        >
                                                                            <h:graphicImage value="/imagens/botoesCE/texto_livre.png" style="border: 0px;" alt="#{CElabels['entidade.textoLivre']}"/>
                                                                        </a4j:commandLink>
                                                                    </rich:column>

                                                                    <rich:column>
                                                                        <f:facet name="header">
                                                                            <h:outputText value="#{CElabels['entidade.fornecedor']}: " />
                                                                        </f:facet>

                                                                        <h:selectOneMenu onblur="blurinput(this);" onfocus="focusinput(this);"
                                                                                         value="#{servicoTerceirizado.fornecedorServico.codigo}" >
                                                                            <f:selectItem itemValue="" itemLabel="#{CElabels['operacoes.todos']}"/>
                                                                            <f:selectItems value="#{servicoTerceirizado.fornecedores}" />
                                                                        </h:selectOneMenu>
                                                                    </rich:column>

                                                                    <rich:column>
                                                                        <f:facet name="header">
                                                                            <h:outputText value="#{CElabels['entidade.valor']}:" />
                                                                        </f:facet>
                                                                        <h:inputText onfocus="focusinput(this);" styleClass="form" size="14" maxlength="14" style="border: 1px solid #8eb3c3"
                                                                                     onkeypress="return(currencyFormat(this,'.',',',event));" value="#{servicoTerceirizado.valorNumerico}" id="valor">
                                                                        </h:inputText>
                                                                    </rich:column>


                                                                </rich:dataTable>
                                                                <div id="divMsg-servicoTercerizado" class="mensagemAviso">
                                                                    <h:outputText styleClass="mensagemAviso" id="msgServicoTercerizado" rendered="#{OrcamentoDetalhadoControle.qtdAlteradaServico}"
                                                                                  value="#{CElabels['menu.operacoesCE.valorAlterado']} #{CElabels['menu.operacoesCE.quantidadesValidas']}: #{OrcamentoDetalhadoControle.faixasServico}"></h:outputText></div>
                                                            </h:panelGrid>
                                                            <!-- FIM - SELECAO DE SERVICOS TERCEIRIZADO-->

                                                            <br />

                                                            <!-- INICIO - SELECAO DE BENS DE CONSUMO-->
                                                            <h:panelGroup>
                                                                <img src="${contexto}/images/arrow2.gif" width="16" height="16" style="vertical-align:middle;margin-right:6px;">
                                                                <h:outputText value="#{CElabels['entidade.bensConsumo']}:" />
                                                                <div class="sep" style="margin:4px 0 5px 0;"><img src="${contexto}/images/shim.gif"></div>
                                                            </h:panelGroup>

                                                            <h:panelGrid id="panelBensConsumo" styleClass="tablepadding2" columns="1" width="100%" style="text-align: center;">

                                                                <a4j:commandButton rendered="#{OrcamentoDetalhadoControle.negociacaoEvento.perfilEventoTO.permiteOutrosBensConsumo}"
                                                                                   type="button" reRender="formConsultaBemConsumo" onclick="Richfaces.showModalPanel('panelConsultaBemConsumo')"
                                                                                   title="#{CElabels['operacoes.adicionar.bemConsumo']}" image="/images/icon_add.gif"/>

                                                                <rich:dataTable id="bensConsumo" width="100%" rowClasses="linhaImpar" columnClasses="colunaEsquerda"
                                                                                value="#{OrcamentoDetalhadoControle.bensConsumo}" var="bemConsumo">

                                                                    <rich:column>
                                                                        <f:facet name="header">
                                                                            <h:outputText value="#{CElabels['entidade.descricao']}" />
                                                                        </f:facet>
                                                                        <h:selectBooleanCheckbox  value="#{bemConsumo.selecionado}" disabled="#{bemConsumo.obrigatorio}">
                                                                            <a4j:support event="onchange" action="#{OrcamentoDetalhadoControle.selecionarBemConsumo}" reRender="panelBensConsumo, detalhamentoNegociacao"/>
                                                                        </h:selectBooleanCheckbox>
                                                                        <h:outputText value="#{bemConsumo.descricaoProdutoLocacao}" />
                                                                    </rich:column>

                                                                    <rich:column>
                                                                        <a4j:commandLink action="#{OrcamentoDetalhadoControle.exibirTextoLivreBemConsumo}"
                                                                                         oncomplete="Richfaces.showModalPanel('panelTextoLivreItem')" reRender="panelTextoLivreItem"
                                                                                         onmouseover="toolTip('#{CElabels['entidade.textoLivre']}' , 120 , 'gray')"
                                                                                         onmouseout="hideToolTip();">
                                                                            <h:graphicImage value="/imagens/botoesCE/texto_livre.png" style="border: 0px;" alt="#{CElabels['entidade.textoLivre']}"/>
                                                                        </a4j:commandLink>
                                                                    </rich:column>

                                                                    <rich:column>
                                                                        <f:facet name="header">
                                                                            <h:outputText value="#{CElabels['entidade.quantidade']}" />
                                                                        </f:facet>
                                                                        <h:inputText onblur="blurinput(this);" onfocus="focusinput(this);" size="7" id="qtdbens"
                                                                                     styleClass="form" maxlength="7" disabled="#{!bemConsumo.selecionado}"
                                                                                     value="#{bemConsumo.quantidade}" onkeypress="return mascara(this.form, this.id, '99999', event);">
                                                                            <a4j:support event="onchange" action="#{OrcamentoDetalhadoControle.calcularValorBemConsumo}" reRender="panelBensConsumo, detalhamentoNegociacao"/>
                                                                        </h:inputText>
                                                                    </rich:column>

                                                                    <rich:column>
                                                                        <a4j:commandButton image="/images/icon_chave.png" title="#{CElabels['operacoes.editar.valor.bemConsumo']}"
                                                                                           actionListener="#{OrcamentoDetalhadoControle.selTipoProdutoLocacao}"
                                                                                           oncomplete="Richfaces.showModalPanel('panelSenhaValorProdLocacao');"
                                                                                           reRender="panelSenhaValorProdLocacao, bensConsumo">
                                                                            <f:attribute name="tipoProdutoLocacao" value="1" />
                                                                        </a4j:commandButton>
                                                                    </rich:column>

                                                                    <rich:column>
                                                                        <f:facet name="header">
                                                                            <h:outputText value="#{CElabels['entidade.valor']}" />
                                                                        </f:facet>
                                                                        <h:outputText rendered="#{!bemConsumo.alteracaoValor}"
                                                                                      value="#{bemConsumo.valorUnitarioMonetario}" />
                                                                        <h:inputText onfocus="focusinput(this);" size="7" onkeypress="return(currencyFormat(this,'.',',',event));"
                                                                                     rendered="#{bemConsumo.alteracaoValor}" styleClass="form" maxlength="14"
                                                                                     value="#{bemConsumo.valorUnitarioFormatado}"
                                                                                     disabled="#{!bemConsumo.selecionado}">
                                                                        </h:inputText>
                                                                        <a4j:commandButton rendered="#{bemConsumo.alteracaoValor}" image="/images/tick.png" title="#{CElabels['operacoes.editar.valor.novo.aplicar']}"
                                                                                           action="#{OrcamentoDetalhadoControle.confirmarAlteracaoValorProdutoLocacao}" reRender="panelBensConsumo, detalhamentoNegociacao"/>
                                                                    </rich:column>

                                                                    <!-- INICIO - DESCONTO EM PRODUTO -->
                                                                    <rich:column>
                                                                        <f:facet name="header">
                                                                            <h:outputText value="#{CElabels['menu.cadastros.perfisEventos.descontos']}" />
                                                                        </f:facet>
                                                                        <h:outputText value="#{bemConsumo.descontoApresentar}  " />
                                                                        <a4j:commandButton image="/images/icon_calculadora.png"
                                                                                           title="#{CElabels['menu.cadastros.perfisEventos.descontos']}"
                                                                                           action="#{OrcamentoDetalhadoControle.informarDescontoBemConsumo}"
                                                                                           reRender="panelSenhaDesconto"
                                                                                           oncomplete="Richfaces.showModalPanel('panelSenhaDesconto');"/>

                                                                        <rich:spacer width="8px"></rich:spacer>

                                                                        <a4j:commandButton  image="/images/bt_remove.png"
                                                                                            rendered="#{bemConsumo.desconto > 0}"
                                                                                            title="#{CElabels['menu.cadastros.perfisEventos.removerDescontos']}"
                                                                                            action="#{OrcamentoDetalhadoControle.removerDescontoBemConsumo}"
                                                                                            reRender="detalhamentoNegociacao, bensConsumo"/>
                                                                    </rich:column>
                                                                    <!-- FIM - DESCONTO EM PRODUTO -->

                                                                    <rich:column>
                                                                        <f:facet name="header">
                                                                            <h:outputText value="#{CElabels['entidade.total']}" />
                                                                        </f:facet>
                                                                        <h:outputText value="#{bemConsumo.valorMonetario}" />
                                                                    </rich:column>

                                                                </rich:dataTable>

                                                                <div id="divMsg-bensConsumo" class="mensagemAviso">
                                                                    <h:outputText styleClass="mensagemAviso" id="msgBensConsumo" rendered="#{OrcamentoDetalhadoControle.qtdAlteradaBensConsumo}"
                                                                                  value="#{CElabels['menu.operacoesCE.valorAlterado']} #{CElabels['menu.operacoesCE.quantidadesValidas']}: #{OrcamentoDetalhadoControle.faixasProdutos}"></h:outputText></div>

                                                            </h:panelGrid>
                                                            <!-- FIM - SELECAO DE BENS DE CONSUMO-->

                                                            <br />

                                                            <!-- INICIO - SELECAO DE UTENSILIOS-->
                                                            <h:panelGroup>
                                                                <img src="${contexto}/images/arrow2.gif" width="16" height="16" style="vertical-align:middle;margin-right:6px;">
                                                                <h:outputText value="#{CElabels['entidade.utensilios']}:" />
                                                                <div class="sep" style="margin:4px 0 5px 0;"><img src="${contexto}/images/shim.gif"></div>
                                                            </h:panelGroup>

                                                            <h:panelGrid id="panelUtensilios" styleClass="tablepadding2" columns="1" width="100%" style="text-align: center;">

                                                                <a4j:commandButton rendered="#{OrcamentoDetalhadoControle.negociacaoEvento.perfilEventoTO.permiteOutrosUtensilios}"
                                                                                   type="button" reRender="formConsultaUtensilio" onclick="Richfaces.showModalPanel('panelConsultaUtensilio')"
                                                                                   title="#{CElabels['operacoes.adicionar.utensilio']}" image="/images/icon_add.gif"/>

                                                                <rich:dataTable id="utensilios" width="100%" rowClasses="linhaImpar" columnClasses="colunaEsquerda"
                                                                                value="#{OrcamentoDetalhadoControle.utensilios}" var="utensilio">

                                                                    <rich:column>
                                                                        <f:facet name="header">
                                                                            <h:outputText value="#{CElabels['entidade.descricao']}" />
                                                                        </f:facet>
                                                                        <h:selectBooleanCheckbox value="#{utensilio.selecionado}" disabled="#{utensilio.obrigatorio}">
                                                                            <a4j:support event="onchange" action="#{OrcamentoDetalhadoControle.selecionarUtensilio}" reRender="panelUtensilios, detalhamentoNegociacao"/>
                                                                        </h:selectBooleanCheckbox>
                                                                        <h:outputText value="#{utensilio.descricaoProdutoLocacao}" />
                                                                    </rich:column>

                                                                    <rich:column>
                                                                        <a4j:commandLink action="#{OrcamentoDetalhadoControle.exibirTextoLivreUtensilio}"
                                                                                         oncomplete="Richfaces.showModalPanel('panelTextoLivreItem')" reRender="panelTextoLivreItem"
                                                                                         onmouseover="toolTip('#{CElabels['entidade.textoLivre']}' , 120 , 'gray')"
                                                                                         onmouseout="hideToolTip();">
                                                                            <h:graphicImage value="/imagens/botoesCE/texto_livre.png" style="border: 0px;" alt="#{CElabels['entidade.textoLivre']}"/>
                                                                        </a4j:commandLink>

                                                                    </rich:column>

                                                                    <rich:column>
                                                                        <f:facet name="header">
                                                                            <h:outputText value="#{CElabels['entidade.quantidade']}" />
                                                                        </f:facet>
                                                                        <h:inputText  onblur="blurinput(this);" onfocus="focusinput(this);" size="7"
                                                                                      styleClass="form" maxlength="7" disabled="#{!utensilio.selecionado}"
                                                                                      value="#{utensilio.quantidade}" id="qtdutens"
                                                                                      onkeypress="return mascara(this.form, this.id, '99999', event);">
                                                                            <a4j:support event="onchange" action="#{OrcamentoDetalhadoControle.calcularValorUtensilio}" reRender="panelUtensilios, detalhamentoNegociacao"/>
                                                                        </h:inputText>
                                                                    </rich:column>

                                                                    <rich:column>
                                                                        <a4j:commandButton image="/images/icon_chave.png" title="#{CElabels['operacoes.editar.valor.utensilio']}"
                                                                                           actionListener="#{OrcamentoDetalhadoControle.selTipoProdutoLocacao}"
                                                                                           oncomplete="Richfaces.showModalPanel('panelSenhaValorProdLocacao');" reRender="panelSenhaValorProdLocacao, utensilios">
                                                                            <f:attribute name="tipoProdutoLocacao" value="2" />
                                                                        </a4j:commandButton>
                                                                    </rich:column>

                                                                    <rich:column>
                                                                        <f:facet name="header">
                                                                            <h:outputText value="#{CElabels['entidade.valor']}" />
                                                                        </f:facet>
                                                                        <h:outputText rendered="#{!utensilio.alteracaoValor}"
                                                                                      value="#{utensilio.valorUnitarioMonetario}" />
                                                                        <h:inputText onfocus="focusinput(this);" onkeypress="return(currencyFormat(this,'.',',',event));"
                                                                                     size="7" rendered="#{utensilio.alteracaoValor}" styleClass="form" maxlength="14"
                                                                                     value="#{utensilio.valorUnitarioFormatado}" disabled="#{!utensilio.selecionado}"/>
                                                                        <a4j:commandButton rendered="#{utensilio.alteracaoValor}" image="/images/tick.png" title="#{CElabels['operacoes.editar.valor.novo.aplicar']}"
                                                                                           action="#{OrcamentoDetalhadoControle.confirmarAlteracaoValorProdutoLocacao}" reRender="panelUtensilios, detalhamentoNegociacao"/>
                                                                    </rich:column>
                                                                    <!-- DESCONTO EM PRODUTO -->
                                                                    <rich:column>
                                                                        <f:facet name="header">
                                                                            <h:outputText value="#{CElabels['menu.cadastros.perfisEventos.descontos']}" />
                                                                        </f:facet>
                                                                        <h:outputText value="#{utensilio.descontoApresentar}  " />
                                                                        <a4j:commandButton  image="/images/icon_calculadora.png" title="#{CElabels['menu.cadastros.perfisEventos.descontos']}"
                                                                                            action="#{OrcamentoDetalhadoControle.informarDescontoUtensilio}"
                                                                                            reRender="panelSenhaDesconto"
                                                                                            oncomplete="Richfaces.showModalPanel('panelSenhaDesconto');"/>

                                                                        <rich:spacer width="8px"></rich:spacer>

                                                                        <a4j:commandButton  image="/images/bt_remove.png"
                                                                                            rendered="#{utensilio.desconto > 0}"
                                                                                            title="#{CElabels['menu.cadastros.perfisEventos.removerDescontos']}"
                                                                                            action="#{OrcamentoDetalhadoControle.removerDescontoUtensilio}"
                                                                                            reRender="detalhamentoNegociacao, utensilios"/>
                                                                    </rich:column>
                                                                    <rich:column>
                                                                        <f:facet name="header">
                                                                            <h:outputText value="#{CElabels['entidade.total']}" />
                                                                        </f:facet>
                                                                        <h:outputText value="#{utensilio.valorMonetario}" />
                                                                    </rich:column>

                                                                </rich:dataTable>
                                                                <div id="divMsg-utensilios" class="mensagemAviso">
                                                                    <h:outputText styleClass="mensagemAviso" id="msgUtensilios" rendered="#{OrcamentoDetalhadoControle.qtdAlteradaUtensilio}"
                                                                                  value="#{CElabels['menu.operacoesCE.valorAlterado']} #{CElabels['menu.operacoesCE.quantidadesValidas']}: #{OrcamentoDetalhadoControle.faixasProdutos}"></h:outputText></div>

                                                            </h:panelGrid>
                                                            <!-- FIM - SELECAO DE UTENSILIOS-->

                                                            <br />

                                                            <!-- INICIO - SELECAO DE BRINQUEDOS-->
                                                            <h:panelGroup>
                                                                <img src="${contexto}/images/arrow2.gif" width="16" height="16" style="vertical-align:middle;margin-right:6px;">
                                                                <h:outputText value="#{CElabels['entidade.brinquedos']}:" />
                                                                <div class="sep" style="margin:4px 0 5px 0;"><img src="${contexto}/images/shim.gif"></div>
                                                            </h:panelGroup>

                                                            <h:panelGrid id="panelBrinquedos" styleClass="tablepadding2" columns="1" width="100%" style="text-align: center;">

                                                                <a4j:commandButton rendered="#{OrcamentoDetalhadoControle.negociacaoEvento.perfilEventoTO.permiteOutrosBrinquedos}"
                                                                                   type="button" reRender="formConsultaBrinquedo" onclick="Richfaces.showModalPanel('panelConsultaBrinquedo')"
                                                                                   title="#{CElabels['operacoes.adicionar.brinquedo']}" image="/images/icon_add.gif"/>

                                                                <rich:dataTable id="brinquedos" width="100%" rowClasses="linhaImpar" columnClasses="colunaEsquerda"
                                                                                value="#{OrcamentoDetalhadoControle.brinquedos}" var="brinquedo">

                                                                    <rich:column>
                                                                        <f:facet name="header">
                                                                            <h:outputText value="#{CElabels['entidade.descricao']}" />
                                                                        </f:facet>
                                                                        <h:selectBooleanCheckbox value="#{brinquedo.selecionado}" disabled="#{brinquedo.obrigatorio}">
                                                                            <a4j:support event="onchange" action="#{OrcamentoDetalhadoControle.selecionarBrinquedo}" reRender="panelBrinquedos, detalhamentoNegociacao"/>
                                                                        </h:selectBooleanCheckbox>
                                                                        <h:outputText value="#{brinquedo.descricaoProdutoLocacao}" />
                                                                    </rich:column>

                                                                    <rich:column>
                                                                        <a4j:commandLink  action="#{OrcamentoDetalhadoControle.exibirTextoLivreBrinquedo}"
                                                                                          oncomplete="Richfaces.showModalPanel('panelTextoLivreItem')" reRender="panelTextoLivreItem"
                                                                                          onmouseover="toolTip('#{CElabels['entidade.textoLivre']}' , 120 , 'gray')"
                                                                                          onmouseout="hideToolTip();">
                                                                            <h:graphicImage value="/imagens/botoesCE/texto_livre.png" style="border: 0px;" alt="#{CElabels['entidade.textoLivre']}"/>
                                                                        </a4j:commandLink>
                                                                    </rich:column>

                                                                    <rich:column>
                                                                        <f:facet name="header">
                                                                            <h:outputText value="#{CElabels['entidade.quantidade']}" />
                                                                        </f:facet>
                                                                        <h:inputText onblur="blurinput(this);" onfocus="focusinput(this);" size="7" id="qtdbrinqs"
                                                                                     styleClass="form" maxlength="7" disabled="#{!brinquedo.selecionado}"
                                                                                     value="#{brinquedo.quantidade}" onkeypress="return mascara(this.form, this.id, '99999', event);">
                                                                            <a4j:support event="onchange" action="#{OrcamentoDetalhadoControle.calcularValorBrinquedo}" reRender="panelBrinquedos, detalhamentoNegociacao"/>
                                                                        </h:inputText>
                                                                    </rich:column>

                                                                    <rich:column>
                                                                        <a4j:commandButton  image="/images/icon_chave.png" title="#{CElabels['operacoes.editar.valor.brinquedo']}"
                                                                                            actionListener="#{OrcamentoDetalhadoControle.selTipoProdutoLocacao}"
                                                                                            oncomplete="Richfaces.showModalPanel('panelSenhaValorProdLocacao');" reRender="panelSenhaValorProdLocacao, brinquedos">
                                                                            <f:attribute name="tipoProdutoLocacao" value="3" />
                                                                        </a4j:commandButton>
                                                                    </rich:column>

                                                                    <rich:column>
                                                                        <f:facet name="header">
                                                                            <h:outputText value="#{CElabels['entidade.valor']}" />
                                                                        </f:facet>
                                                                        <h:outputText rendered="#{!brinquedo.alteracaoValor}"
                                                                                      value="#{brinquedo.valorUnitarioMonetario}" />
                                                                        <h:inputText onfocus="focusinput(this);" onkeypress="return(currencyFormat(this,'.',',',event));"
                                                                                     size="7" rendered="#{brinquedo.alteracaoValor}" styleClass="form" maxlength="14"
                                                                                     value="#{brinquedo.valorUnitarioFormatado}" disabled="#{!brinquedo.selecionado}"/>
                                                                        <a4j:commandButton rendered="#{brinquedo.alteracaoValor}" image="/images/tick.png" title="#{CElabels['operacoes.editar.valor.novo.aplicar']}"
                                                                                           action="#{OrcamentoDetalhadoControle.confirmarAlteracaoValorProdutoLocacao}" reRender="panelBrinquedos, detalhamentoNegociacao"/>
                                                                    </rich:column>
                                                                    <!-- DESCONTO EM PRODUTO -->
                                                                    <rich:column>
                                                                        <f:facet name="header">
                                                                            <h:outputText value="#{CElabels['menu.cadastros.perfisEventos.descontos']}" />
                                                                        </f:facet>
                                                                        <h:outputText value="#{brinquedo.descontoApresentar}  " />
                                                                        <a4j:commandButton image="/images/icon_calculadora.png" title="#{CElabels['menu.cadastros.perfisEventos.descontos']}"
                                                                                           action="#{OrcamentoDetalhadoControle.informarDescontoBrinquedo}"
                                                                                           reRender="panelSenhaDesconto"
                                                                                           oncomplete="Richfaces.showModalPanel('panelSenhaDesconto');"/>
                                                                        <rich:spacer width="8px"></rich:spacer>

                                                                        <a4j:commandButton  image="/images/bt_remove.png"
                                                                                            rendered="#{brinquedo.desconto > 0}"
                                                                                            title="#{CElabels['menu.cadastros.perfisEventos.removerDescontos']}"
                                                                                            action="#{OrcamentoDetalhadoControle.removerDescontoBrinquedo}"
                                                                                            reRender="detalhamentoNegociacao, brinquedos"/>
                                                                    </rich:column>
                                                                    <rich:column>
                                                                        <f:facet name="header">
                                                                            <h:outputText value="#{CElabels['entidade.total']}" />
                                                                        </f:facet>
                                                                        <h:outputText value="#{brinquedo.valorMonetario}" />
                                                                    </rich:column>

                                                                </rich:dataTable>
                                                                <div id="divMsg-brinquedos" class="mensagemAviso">
                                                                    <h:outputText styleClass="mensagemAviso" id="msgBrinquedos" rendered="#{OrcamentoDetalhadoControle.qtdAlteradaBrinquedo}"
                                                                                  value="#{CElabels['menu.operacoesCE.valorAlterado']} #{CElabels['menu.operacoesCE.quantidadesValidas']}: #{OrcamentoDetalhadoControle.faixasProdutos}"></h:outputText></div>

                                                            </h:panelGrid>
                                                            <!-- FIM - SELECAO DE BRINQUEDOS-->

                                                            <br />

                                                            <div class="sep" style="margin:4px 0 5px 0;"><img src="${contexto}/images/shim.gif"></div>

                                                            <!-- INICIO - TIPO DE DESCONTO E VALOR -->
                                                            <h:panelGrid columns="2" id="gridDesconto">

                                                                <h:outputText styleClass="text" style="font-weight: bold" value="#{CElabels['entidade.desconto.tipo']}:"/>
                                                                <h:selectOneMenu onblur="blurinput(this);" onfocus="focusinput(this);" styleClass="form"
                                                                                 id="tiposDesconto" value="#{OrcamentoDetalhadoControle.negociacaoEvento.tipoDesconto}">
                                                                    <f:selectItem itemValue="" itemLabel="#{CElabels['operacoes.selecione']}"/>
                                                                    <f:selectItems value="#{OrcamentoDetalhadoControle.tiposDesconto}" />
                                                                    <a4j:support event="onchange" action="#{OrcamentoDetalhadoControle.calcular}" reRender="detalhamentoNegociacao"/>
                                                                </h:selectOneMenu>

                                                                <h:outputText styleClass="text" style="font-weight: bold" value="#{CElabels['entidade.desconto.valor']}:"/>
                                                                <h:panelGroup>
                                                                    <table><tr><td valign="middle">
                                                                        <h:inputText id="valorDesconto" onblur="blurinput(this);" onfocus="focusinput(this);"
                                                                                     onkeypress="return(currencyFormat(this,'.',',',event));"
                                                                                     styleClass="form" maxlength="14" value="#{OrcamentoDetalhadoControle.negociacaoEvento.descontoFormatado}" />
                                                                    </td><td valign="middle">
                                                                        <a4j:commandButton value="#{CElabels['entidade.desconto.aplicar']}"
                                                                                           image="/imagens/botoesCE/aplicar_desconto.png"
                                                                                           action="#{OrcamentoDetalhadoControle.aplicarDesconto}"
                                                                                           reRender="detalhamentoNegociacao"/>
                                                                    </td><td valign="middle">

                                                                        <a4j:commandButton image="/imagens/botoesCE/remover.png"
                                                                                           action="#{OrcamentoDetalhadoControle.removerDesconto}"
                                                                                           reRender="detalhamentoNegociacao, gridDesconto"/>

                                                                    </td></tr></table>
                                                                </h:panelGroup>

                                                            </h:panelGrid>
                                                            <!-- FIM - TIPO DE DESCONTO E VALOR -->

                                                            <br/>

                                                            <!-- INICIO - CHEQUE CAUCAO E CREDITO -->
                                                            <h:panelGroup>
                                                                <img src="${contexto}/images/arrow2.gif" width="16" height="16" style="vertical-align:middle;margin-right:6px;">
                                                                <h:outputText value="#{CElabels['entidade.chequeCaucaoECredito']}:" />
                                                                <div class="sep" style="margin:4px 0 5px 0;"><img src="${contexto}/images/shim.gif"></div>
                                                            </h:panelGroup>

                                                            <!-- Inicio - Botoes Lancar Caucao e Lancar Credito -->
                                                            <h:panelGrid columns="2">
                                                                <h:panelGroup>
                                                                    <a4j:commandButton value="#{CElabels['entidade.caucao.lancar']}"
                                                                                       image="/imagens/botoesCE/lancar_caucao.png"
                                                                                       action="#{OrcamentoDetalhadoControle.setarTipoCaucao}"
                                                                                       oncomplete="Richfaces.showModalPanel('panelCacaoECredito')"
                                                                                       reRender="panelCacaoECredito">
                                                                    </a4j:commandButton>
                                                                    <rich:spacer width="8"/>
                                                                    <a4j:commandButton value="#{CElabels['entidade.credito.lancar']}"
                                                                                       image="/imagens/botoesCE/lancar_credito.png"
                                                                                       action="#{OrcamentoDetalhadoControle.setarTipoCredito}"
                                                                                       oncomplete="Richfaces.showModalPanel('panelCacaoECredito')"
                                                                                       reRender="panelCacaoECredito">
                                                                    </a4j:commandButton>&nbsp;
                                                                </h:panelGroup>
                                                            </h:panelGrid>
                                                            <!-- Fim - Botoes Lancar Caucao e Lancar Credito -->
                                                            <h:panelGrid styleClass="tablepadding2" columns="1" width="100%">

                                                                <rich:dataTable id="dttaCaucaoCredito" width="100%" rowClasses="linhaImpar"
                                                                                columnClasses="colunaLeft, colunaLeft, colunaLeft, colunaRight"
                                                                                value="#{OrcamentoDetalhadoControle.negociacaoEvento.listaNegEvCaucaoECreditoTO}"
                                                                                styleClass="tablepreviewtotal"
                                                                                var="caucaoCredito">

                                                                    <rich:column>
                                                                        <h:outputText styleClass="text" value="Tipo: " />
                                                                        <h:outputText value="#{caucaoCredito.tipoCaucaoECredito.descricao}" />
                                                                        <rich:spacer width="8"/>
                                                                        <h:outputText styleClass="text" value="Valor: " />
                                                                        <h:outputText value="#{caucaoCredito.strValorFormatado}" />
                                                                    </rich:column>
                                                                    <rich:column>
                                                                        <h:outputText escape="false" value="#{caucaoCredito.observacao}" />
                                                                    </rich:column>
                                                                    <rich:column>
                                                                        <a4j:commandLink action="#{OrcamentoDetalhadoControle.exibirTextoLivreCaucao}"
                                                                                         oncomplete="Richfaces.showModalPanel('panelTextoLivreItem')" reRender="panelTextoLivreItem"
                                                                                         onmouseover="toolTip('#{CElabels['entidade.textoLivre']}' , 120 , 'gray')"
                                                                                         onmouseout="hideToolTip();">
                                                                            <h:graphicImage value="/imagens/botoesCE/texto_livre.png" style="border: 0px;" alt="#{CElabels['entidade.textoLivre']}"/>
                                                                        </a4j:commandLink>																	</rich:column>
                                                                    <rich:column>
                                                                        <a4j:commandButton value="Quitar" rendered="#{caucaoCredito.podeQuitar}"
                                                                                           image="/imagens/botoesCE/quitar.png">
                                                                            <a4j:support event="onclick" actionListener="#{OrcamentoDetalhadoControle.selCaucaoECredito}" oncomplete="Richfaces.showModalPanel('panelCacaoECreditoQuitacao')" reRender="panelCacaoECreditoQuitacao"/>
                                                                        </a4j:commandButton>
                                                                        <rich:spacer width="8"/>

                                                                        <a4j:commandButton image="/imagens/botoesCE/remover.png" rendered="#{OrcamentoDetalhadoControle.btnRemoverCredito}">
                                                                            <a4j:support event="onclick" actionListener="#{OrcamentoDetalhadoControle.removerCaucaoECredito}"
                                                                                         reRender="detalhamentoNegociacao, dttaCaucaoCredito"
                                                                            />
                                                                        </a4j:commandButton>
                                                                    </rich:column>
                                                                </rich:dataTable>

                                                            </h:panelGrid>
                                                            <!-- FIM - CHEQUE CAUCAO E CREDITO -->

                                                            <br />

                                                            <!-- INICIO - SELECAO DA CONDICAO DE PAGAMENTO -->
                                                            <h:panelGroup>
                                                                <img src="${contexto}/images/arrow2.gif" width="16" height="16" style="vertical-align:middle;margin-right:6px;">
                                                                <h:outputText value="#{CElabels['entidade.condicaoPagamento']}:" />
                                                                &nbsp;<%@include file="../includes/include_obrigatorio.jsp" %>
                                                                <div class="sep" style="margin:4px 0 5px 0;"><img src="${contexto}/images/shim.gif"></div>
                                                            </h:panelGroup>

                                                            <rich:dataGrid columns="3" width="100%" value="#{OrcamentoDetalhadoControle.condicoesPagamento}" var="condicao"
                                                                           id="condicoesPagamento">
                                                                <h:column>
                                                                    <h:selectBooleanCheckbox value="#{condicao.selecionado}"
                                                                                             disabled="#{OrcamentoDetalhadoControle.parcelasExistentes}"
                                                                                             onclick="preencherHiddenChamarBotao('form:botaoCondicao','form:codigoCondicao',
                                                                                                         '#{condicao.codigoCondicaoPagamento}');return false;">
                                                                    </h:selectBooleanCheckbox>
                                                                    <h:outputText value="#{condicao.descricaoCondicaoPagamento}"/>
                                                                </h:column>

                                                            </rich:dataGrid>

                                                            <a4j:commandButton id="botaoCondicao" action="#{OrcamentoDetalhadoControle.selecionarCondicaoPagamento}" style="visibility: hidden;"
                                                                               oncomplete="limparMsgObrigCondPag();" reRender="condicoesPagamento, detalhamentoNegociacao, condicaoPagamentoSelec"/>
                                                            <h:inputHidden id="codigoCondicao" value="#{OrcamentoDetalhadoControle.codigoCondicao}" />
                                                            <div id="divObg-condicaoPagamento" class="mensagemObrigatorio"></div>

                                                            <!-- FIM - SELECAO DA CONDICAO DE PAGAMENTO -->

                                                            <br />

                                                            <!-- INICIO - TEXTO PRE-DEFINIDO -->
                                                            <h:panelGroup>
                                                                <img src="${contexto}/images/arrow2.gif" width="16" height="16" style="vertical-align:middle;margin-right:6px;">
                                                                <h:outputText value="#{CElabels['entidade.textoPredefinido']}:" />
                                                                <div class="sep" style="margin:4px 0 5px 0;"><img src="${contexto}/images/shim.gif"></div>
                                                            </h:panelGroup>
                                                            <h:panelGrid styleClass="tablelistras" columns="1" width="100%">
                                                                <rich:editor id="textoPadrao" useSeamText="false" viewMode="visual"
                                                                             width="400" height="200" value="#{OrcamentoDetalhadoControle.negociacaoEvento.textoPredefinido}"
                                                                             onchange="limparMsgObrigTextoPadrao();" />
                                                            </h:panelGrid>
                                                            <!-- FIM - TEXTO PRE-DEFINIDO -->

                                                            <br />

                                                            <!-- INICIO - OBSERVACAO -->
                                                            <h:panelGroup>
                                                                <img src="${contexto}/images/arrow2.gif" width="16" height="16" style="vertical-align:middle;margin-right:6px;">
                                                                <h:outputText value="#{CElabels['entidade.observacao']}:" />
                                                                <div class="sep" style="margin:4px 0 5px 0;"><img src="${contexto}/images/shim.gif"></div>
                                                            </h:panelGroup>
                                                            <h:panelGrid styleClass="tablelistras" columns="1" width="100%">
                                                                <rich:editor id="textoPadrao1" useSeamText="false" viewMode="visual"
                                                                             width="400" height="200" value="#{OrcamentoDetalhadoControle.negociacaoEvento.textoLivre}"
                                                                             onchange="limparMsgObrigTextoPadrao();" />
                                                            </h:panelGrid>
                                                            <!-- FIM - OBSERVACAO -->

                                                        </h:panelGroup>
                                                    </h:panelGrid>
                                                </rich:column>

                                                <rich:column styleClass="sombrapreview" width="40%" style="border: 0px; text-align: left; vertical-align: top; padding:10px;">
                                                    <h:panelGrid id="detalhamentoNegociacao" columns="1" width="100%" border="0" cellpadding="0" cellspacing="0">

                                                        <rich:column style="border: 0px; vertical-align: top; text-align: center; margin-bottom:10px;">
                                                            <h:outputText styleClass="tituloAzul" value="#{CElabels['entidade.negociacao.resultado']}"/>
                                                        </rich:column>
                                                        <rich:column style="border: 0px;">
                                                            <c:if test="${not empty OrcamentoDetalhadoControle.mensagem or not empty OrcamentoDetalhadoControle.mensagemDetalhada }">
                                                                <h:panelGrid styleClass="mensagemOrcamento" style="text-align: right;" columns="1" width="100%" border="0" cellpadding="0" cellspacing="0">
                                                                    <h:panelGroup>
                                                                        <h:outputText styleClass="mensagemDetalhadaGrande" value="#{OrcamentoDetalhadoControle.mensagem}" />&nbsp;
                                                                        <h:outputText styleClass="mensagemDetalhadaGrande" value="#{OrcamentoDetalhadoControle.mensagemDetalhada}" /><br /><br />


                                                                    </h:panelGroup>
                                                                </h:panelGrid>
                                                            </c:if>
                                                            <h:panelGrid styleClass="mensagemOrcamento2" style="text-align: right;" columns="1" width="100%" border="0" cellpadding="0" cellspacing="0">
                                                                <h:panelGroup>
                                                                    <div id="divObg-condicaoPagamento-msgs" class="mensagemObrigatorio"></div>
                                                                </h:panelGroup>
                                                            </h:panelGrid>
                                                        </rich:column>
                                                        <rich:column style="border: 0px;">
                                                            <h:panelGrid styleClass="tablepreviewtotal" style="text-align: right; vertical-align: middle;" columns="1" width="100%" border="0" cellpadding="0" cellspacing="0">
                                                                <h:panelGroup>
                                                                    <h:outputText  styleClass="totalNegrito" value="#{CElabels['entidade.total']} = "/>  <h:outputText styleClass="verde" value="#{OrcamentoDetalhadoControle.negociacaoEvento.valorFinalFormatado}"/>
                                                                </h:panelGroup>
                                                            </h:panelGrid>
                                                        </rich:column>

                                                        <rich:column style="border: 0px;">
                                                            <h:panelGrid styleClass="tablepreview" style="vertical-align: middle;" columns="1" width="100%" border="0" cellpadding="0" cellspacing="0">
                                                                <h:panelGroup>
                                                                    <h:outputText style="font-weight: bold;" value="#{CElabels['entidade.perfilEvento']}:"/>&nbsp;<h:outputText value="#{OrcamentoDetalhadoControle.negociacaoEvento.perfilEventoTO.descricao}"/>
                                                                </h:panelGroup>

                                                                <h:panelGroup>
                                                                    <h:outputText style="font-weight: bold;" value="#{CElabels['entidade.ambiente']}:"/>
                                                                    <h:dataTable id="ambientesSelecionados" rowClasses="linhaImpar" columnClasses="colunaEsquerda"
                                                                                 rendered="#{!empty OrcamentoDetalhadoControle.negociacaoEvento.ambientes}"
                                                                                 value="#{OrcamentoDetalhadoControle.negociacaoEvento.ambientes}" var="amb">
                                                                        <h:column>
                                                                            <h:outputText value="#{amb.descricaoAmbiente} "/>
                                                                        </h:column>
                                                                        <h:column>
                                                                            <h:outputText value="#{amb.valorDescontadoComSazonalidade} "/>
                                                                        </h:column>
                                                                        <h:column>
                                                                        </h:column>
                                                                    </h:dataTable>
                                                                </h:panelGroup>


                                                                <h:panelGroup>
                                                                    <h:outputText style="font-weight: bold;" value="#{CElabels['entidade.servicos']}:"/>
                                                                    <h:dataTable id="servicosSelecionados" rowClasses="linhaImpar" columnClasses="colunaEsquerda"
                                                                                 rendered="#{!empty OrcamentoDetalhadoControle.negociacaoEvento.servicos}"
                                                                                 value="#{OrcamentoDetalhadoControle.negociacaoEvento.servicos}" var="servico">
                                                                        <h:column>
                                                                            <h:outputText value="#{servico.descricaoServico} "/>
                                                                        </h:column>
                                                                        <h:column>
                                                                            <h:outputText value="#{servico.quantidade} "/>
                                                                        </h:column>
                                                                        <h:column>
                                                                            <h:outputText value="#{servico.valorMonetario} "/>
                                                                        </h:column>
                                                                    </h:dataTable>
                                                                </h:panelGroup>

                                                                <h:panelGroup>
                                                                    <h:outputText style="font-weight: bold;" value="#{CElabels['entidade.bensConsumo']}:"/>
                                                                    <h:dataTable id="bensConsumoSelecionados" rowClasses="linhaImpar" columnClasses="colunaEsquerda"
                                                                                 rendered="#{!empty OrcamentoDetalhadoControle.negociacaoEvento.bensConsumo}"
                                                                                 value="#{OrcamentoDetalhadoControle.negociacaoEvento.bensConsumo}" var="bemConsumo">
                                                                        <h:column>
                                                                            <h:outputText value="#{bemConsumo.descricaoProdutoLocacao} "/>
                                                                        </h:column>
                                                                        <h:column>
                                                                            <h:outputText value="#{bemConsumo.quantidade} "/>
                                                                        </h:column>
                                                                        <h:column>
                                                                            <h:outputText value="#{bemConsumo.valorMonetario} "/>
                                                                        </h:column>
                                                                    </h:dataTable>
                                                                </h:panelGroup>

                                                                <h:panelGroup>
                                                                    <h:outputText style="font-weight: bold;" value="#{CElabels['entidade.utensilios']}:"/>
                                                                    <h:dataTable id="utensiliosSelecionados" rowClasses="linhaImpar" columnClasses="colunaEsquerda"
                                                                                 rendered="#{!empty OrcamentoDetalhadoControle.negociacaoEvento.utensilios}"
                                                                                 value="#{OrcamentoDetalhadoControle.negociacaoEvento.utensilios}" var="utensilio">
                                                                        <h:column>
                                                                            <h:outputText value="#{utensilio.descricaoProdutoLocacao} "/>
                                                                        </h:column>
                                                                        <h:column>
                                                                            <h:outputText value="#{utensilio.quantidade} "/>
                                                                        </h:column>
                                                                        <h:column>
                                                                            <h:outputText value="#{utensilio.valorMonetario} "/>
                                                                        </h:column>
                                                                    </h:dataTable>
                                                                </h:panelGroup>

                                                                <h:panelGroup>
                                                                    <h:outputText style="font-weight: bold;" value="#{CElabels['entidade.brinquedos']}:"/>
                                                                    <h:dataTable id="brinquedosSelecionados" rowClasses="linhaImpar" columnClasses="colunaEsquerda"
                                                                                 rendered="#{!empty OrcamentoDetalhadoControle.negociacaoEvento.brinquedos}"
                                                                                 value="#{OrcamentoDetalhadoControle.negociacaoEvento.brinquedos}" var="brinquedo">
                                                                        <h:column>
                                                                            <h:outputText value="#{brinquedo.descricaoProdutoLocacao} "/>
                                                                        </h:column>
                                                                        <h:column>
                                                                            <h:outputText value="#{brinquedo.quantidade} "/>
                                                                        </h:column>
                                                                        <h:column>
                                                                            <h:outputText value="#{brinquedo.valorMonetario} "/>
                                                                        </h:column>
                                                                    </h:dataTable>
                                                                </h:panelGroup>

                                                                <h:panelGrid columns="2" columnClasses="tituloCamposNegrito, tituloCampos, tituloCampos, tituloCampos">
                                                                    <h:outputText value="#{CElabels['entidade.negociacao.credito.total']}:"/>
                                                                    <h:outputText value="#{OrcamentoDetalhadoControle.negociacaoEvento.creditoMonetario}"/>

                                                                    <h:outputText value="#{CElabels['entidade.negociacao.total']}:"/>
                                                                    <h:outputText value="#{OrcamentoDetalhadoControle.negociacaoEvento.valorTotalMonetario}"/>

                                                                    <h:outputText value="#{CElabels['entidade.desconto.total']}:"/>
                                                                    <h:outputText value="#{OrcamentoDetalhadoControle.negociacaoEvento.descontoMonetario}"/>

                                                                    <h:outputText value="#{CElabels['entidade.subTotal']}:"/>
                                                                    <h:outputText styleClass="blue" value="#{OrcamentoDetalhadoControle.negociacaoEvento.valorFinalMonetario}"/>

                                                                    <h:outputText value="#{CElabels['entidade.valorMensal']}:"/>
                                                                    <h:outputText value="#{OrcamentoDetalhadoControle.negociacaoEvento.valorMensalMonetario}"/>

                                                                    <h:outputText value="#{CElabels['entidade.desconto.total']}:"/>
                                                                    <h:outputText value="#{OrcamentoDetalhadoControle.negociacaoEvento.descontoPercentual}"/>
                                                                </h:panelGrid>
                                                            </h:panelGrid>
                                                        </rich:column>

                                                        <rich:column style="border: 0px;">
                                                            <h:panelGrid styleClass="tablepreviewtotal" style="text-align: right;" columns="1" width="100%" border="0" cellpadding="0" cellspacing="0">
                                                                <h:panelGroup>
                                                                    <h:outputText styleClass="totalNegrito" value="#{CElabels['entidade.total']} = "/> <h:outputText styleClass="verde" value="#{OrcamentoDetalhadoControle.negociacaoEvento.valorFinalFormatado}"/>
                                                                </h:panelGroup>
                                                            </h:panelGrid>
                                                            <c:if test="${not OrcamentoDetalhadoControle.parcelasExistentes}">
                                                                <h:panelGrid id="parcelas" columns="1" columnClasses="colunaDireita" width="100%">
                                                                    <h:outputText value="Parcela(s)" styleClass="tituloAzul" style="clear:both;text-align:right;margin-bottom:5px;" />
                                                                    <h:panelGroup rendered="#{OrcamentoDetalhadoControle.negociacaoEvento.valorFinal > 0}">
                                                                        <h:outputText style="font-weight: bold" value=" 1 x " />
                                                                        <h:outputText id="valorCondicaoPagamento" style="font-weight: bold" value="#{OrcamentoDetalhadoControle.negociacaoEvento.valorMensalMonetario}"/>
                                                                    </h:panelGroup>
                                                                    <h:panelGroup rendered="#{OrcamentoDetalhadoControle.negociacaoEvento.condicaoPagamento.nrParcelas > 1}">
                                                                        <h:outputText style="font-weight: bold" value="#{OrcamentoDetalhadoControle.negociacaoEvento.condicaoPagamento.nrParcelas - 1} x " />
                                                                        <h:outputText style="font-weight: bold" value="#{OrcamentoDetalhadoControle.negociacaoEvento.valorMensalMonetario}"/>
                                                                    </h:panelGroup>
                                                                </h:panelGrid>
                                                            </c:if>

                                                            <div style="clear: both; text-align: right;">
                                                                <table width="100%" border="0" cellspacing="0" cellpadding="0">
                                                                    <tr>
                                                                        <td>
                                                                            <c:if test="${not empty OrcamentoDetalhadoControle.mensagem or not empty OrcamentoDetalhadoControle.mensagemDetalhada }">
                                                                                <h:panelGrid styleClass="mensagemOrcamento" style="text-align: right;" columns="1" width="100%" border="0" cellpadding="0" cellspacing="0">
                                                                                    <h:panelGroup>
                                                                                        <h:outputText styleClass="mensagemDetalhadaGrande" value="#{OrcamentoDetalhadoControle.mensagem}" />&nbsp;
                                                                                        <h:outputText styleClass="mensagemDetalhadaGrande" value="#{OrcamentoDetalhadoControle.mensagemDetalhada}" /><br /><br />

                                                                                    </h:panelGroup>
                                                                                </h:panelGrid>
                                                                            </c:if>
                                                                            <h:panelGrid styleClass="mensagemOrcamento2" style="text-align: right;" columns="1" width="100%" border="0" cellpadding="0" cellspacing="0">
                                                                                <h:panelGroup>
                                                                                    <div id="divObg-condicaoPagamento-msgs-2" class="mensagemObrigatorio"></div>
                                                                                </h:panelGroup>
                                                                            </h:panelGrid>
                                                                        </td>
                                                                    </tr>
                                                                </table>
                                                            </div>
                                                            <h:panelGrid  columns="8">

                                                                <h:commandButton id="fechaNegociacao"
                                                                                 onclick="if(!validar()){return false;};"
                                                                                 image="/imagens/botoesCE/exibir_orcamento.png"
                                                                                 action="#{OrcamentoDetalhadoControle.exibirOrcamento}" rendered="#{!OrcamentoDetalhadoControle.orcamentoFicticio and !OrcamentoDetalhadoControle.eventoAutorizado }"
                                                                                 value="#{CElabels['operacoes.negociacao.exibir']}"
                                                                                 actionListener="#{OrcamentoDetalhadoControle.autorizacao}">
                                                                    <!-- funcao.negociacao evento -->
                                                                    <f:attribute name="funcao" value="110" />
                                                                </h:commandButton>

                                                                <h:commandButton value="#{CElabels['menu.operacoesCE.cadastrarEvento']}"
                                                                                 image="/imagens/botoesCE/cadastrar_evento.png"
                                                                                 onclick="if(!validar()){return false;};"
                                                                                 action="#{OrcamentoDetalhadoControle.abrirCadastroEventoOrcado}"
                                                                                 actionListener="#{OrcamentoDetalhadoControle.selCadastrarEvento}"
                                                                                 rendered="#{OrcamentoDetalhadoControle.orcamentoFicticio || OrcamentoDetalhadoControle.origemDetalhamentoEvento}"/>

                                                                <h:commandButton value="#{CElabels['menu.operacoesCE.descartarOrcamento']}"
                                                                                 image="/imagens/botoesCE/descartar_orcamento.png"
                                                                                 action="#{CadastroInicialControle.terminarCadastro}"
                                                                                 rendered="#{OrcamentoDetalhadoControle.orcamentoFicticio || OrcamentoDetalhadoControle.origemDetalhamentoEvento}"/>

                                                                <a4j:commandButton value="Alterar"
                                                                                   action="#{OrcamentoDetalhadoControle.autorizaAlteracao}"
                                                                                   rendered="#{OrcamentoDetalhadoControle.eventoAutorizado}"
                                                                                   reRender="detalhamentoNegociacao, panelAlterarValores, panelMsgAlterar"
                                                                                   actionListener="#{OrcamentoDetalhadoControle.autorizacao}"
                                                                                   image="/imagens/botoesCE/alterar.png" >
                                                                    <!-- funcao.alterar valores -->
                                                                    <f:attribute name="funcao" value="111" />
                                                                </a4j:commandButton>

                                                                <h:commandButton id="cancela"
                                                                                 actionListener="#{CancelamentoEventoControle.selEventoListener}"
                                                                                 action="#{NavegacaoControle.abrirTelaCancelamentoEvento}"
                                                                                 image="/imagens/botoesCE/cancelar.png">
                                                                    <f:attribute name="codigoEvento"
                                                                                 value="#{OrcamentoDetalhadoControle.negociacaoEvento.codigoEventoInteresse}" />
                                                                </h:commandButton>

                                                                <h:commandButton id="voltar2"  action="#{CadastroInicialControle.abrirDetalhamento}"
                                                                                 image="/imagens/botoesCE/voltar_sem_fundo.png"
                                                                                 value="#{CElabels['operacoes.voltar']}"
                                                                                 rendered="#{!OrcamentoDetalhadoControle.orcamentoFicticio}"
                                                                                 actionListener="#{CadastroInicialControle.selCadastroInicialListener}">
                                                                    <f:attribute name="codigoEventoInteresse" value="#{OrcamentoDetalhadoControle.negociacaoEvento.codigoEventoInteresse}"/>
                                                                </h:commandButton>
                                                            </h:panelGrid>


                                                        </rich:column>

                                                    </h:panelGrid>
                                                </rich:column>

                                            </h:panelGrid>
                                        </h:panelGroup>
                                    </h:panelGroup>
                                </h:panelGroup>
                                <jsp:include page="../includes/include_box_menulateral.jsp" flush="true"/>
                            </h:panelGroup>
                        </h:panelGroup>
                    </h:panelGroup>

                    <jsp:include page="../../../include_rodape_flat.jsp" flush="true" />
                    <rich:jQuery id="mskData" selector=".rich-calendar-input" timing="onload" query="mask('99/99/9999')" />
                </h:panelGroup>
            </h:form>

            <rich:modalPanel id="panelConsultaBemConsumo" autosized="true" shadowOpacity="true" width="450" height="250">
                <f:facet name="header">

                    <jsp:include page="../includes/topoReduzido.jsp"/>

                </f:facet>
                <f:facet name="header">
                    <h:panelGroup>
                        <h:outputText value="#{CElabels['entidade.bensConsumo.consulta']}"></h:outputText>
                    </h:panelGroup>
                </f:facet>
                <f:facet name="controls">
                    <h:panelGroup>
                        <h:graphicImage value="/imagens/close.png" style="cursor:pointer" id="hidelinkBemConsumo"/>
                        <rich:componentControl for="panelConsultaBemConsumo" attachTo="hidelinkBemConsumo" operation="hide" event="onclick"/>
                    </h:panelGroup>
                </f:facet>

                <a4j:form id="formConsultaBemConsumo" ajaxSubmit="true">
                    <h:panelGrid columns="1" width="100%" columnClasses="colunaCentralizada">
                        <h:panelGrid columns="3" footerClass="colunaCentralizada" width="100%">
                            <h:outputText value="#{CElabels['entidade.nome']}:"/>
                            <h:inputText id="valorConsultaBemConsumo" size="50" onblur="blurinput(this);" onfocus="focusinput(this);"
                                         styleClass="form" value="#{OrcamentoDetalhadoControle.filtroBensConsumo.descricao}"/>
                            <a4j:commandButton id="btnConsultarBemConsumo" reRender="formConsultaBemConsumo"
                                               action="#{OrcamentoDetalhadoControle.consultarBensConsumo}" styleClass="botoes"
                                               value="#{CElabels['operacoes.consulta.consultar']}" image="/imagens/botoesCE/buscar.png"
                                               title="#{CElabels['operacoes.consulta.consultarDados']}"/>
                        </h:panelGrid>
                        <rich:dataTable id="resultadoConsultaBemConsumo" width="100%" headerClass="consulta" rowClasses="linhaImpar, linhaPar"
                                        columnClasses="colunaAlinhamento" value="#{OrcamentoDetalhadoControle.bensConsumoConsulta}" rows="5" var="bemConsumoConsulta">
                            <rich:column>
                                <f:facet name="header">
                                    <h:outputText value="#{CElabels['entidade.nome']}"/>
                                </f:facet>
                                <h:panelGroup>
                                    <h:outputText value="#{bemConsumoConsulta.descricao}" />
                                </h:panelGroup>
                            </rich:column>
                            <rich:column>
                                <f:facet name="header">
                                    <h:outputText value="#{CElabels['operacoes.opcoes']}"/>
                                </f:facet>
                                <a4j:commandButton action="#{OrcamentoDetalhadoControle.selecionarBemConsumoConsulta}"
                                                   reRender="bensConsumo, bensConsumoSelecionados, panelMesangem" oncomplete="Richfaces.hideModalPanel('panelConsultaBemConsumo')" value="#{CElabels['operacoes.selecionar']}"
                                                   image="/imagens/botaoEditar.png" title="#{CElabels['operacoes.selecionar.selecionarDado']}" styleClass="botoes"/>
                            </rich:column>
                        </rich:dataTable>
                        <rich:datascroller align="center" for="formConsultaBemConsumo:resultadoConsultaBemConsumo" maxPages="10" id="scResultadoConsultaBemConsumo" />
                        <h:panelGrid id="mensagemConsultaBemConsumo" columns="1" width="100%" styleClass="tabMensagens">
                            <h:panelGrid columns="1" width="100%">
                                <h:outputText styleClass="mensagem" value="#{OrcamentoDetalhadoControle.mensagem}"/>
                                <h:outputText styleClass="mensagemDetalhada" value="#{OrcamentoDetalhadoControle.mensagemDetalhada}"/>
                            </h:panelGrid>
                        </h:panelGrid>
                    </h:panelGrid>
                </a4j:form>
            </rich:modalPanel>

            <rich:modalPanel id="panelConsultaUtensilio" autosized="true" shadowOpacity="true" width="450" height="250">
                <f:facet name="header">

                    <jsp:include page="../includes/topoReduzido.jsp"/>

                </f:facet>
                <f:facet name="header">
                    <h:panelGroup>
                        <h:outputText value="#{CElabels['entidade.utensilios.consulta']}"></h:outputText>
                    </h:panelGroup>
                </f:facet>
                <f:facet name="controls">
                    <h:panelGroup>
                        <h:graphicImage value="/imagens/close.png" style="cursor:pointer" id="hidelinkUtensilio"/>
                        <rich:componentControl for="panelConsultaUtensilio" attachTo="hidelinkUtensilio" operation="hide" event="onclick"/>
                    </h:panelGroup>
                </f:facet>

                <a4j:form id="formConsultaUtensilio" ajaxSubmit="true">
                    <h:panelGrid columns="1" width="100%" columnClasses="colunaCentralizada">
                        <h:panelGrid columns="3" footerClass="colunaCentralizada" width="100%">
                            <h:outputText value="#{CElabels['entidade.nome']}:"/>
                            <h:inputText id="valorConsultaUtensilio" size="50" onblur="blurinput(this);" onfocus="focusinput(this);"
                                         styleClass="form" value="#{OrcamentoDetalhadoControle.filtroUtensilios.descricao}"/>
                            <a4j:commandButton id="btnConsultarUtensilio" reRender="formConsultaUtensilio"
                                               action="#{OrcamentoDetalhadoControle.consultarUtensilios}" styleClass="botoes"
                                               value="#{CElabels['operacoes.consulta.consultar']}" image="/imagens/botoesCE/buscar.png"
                                               title="#{CElabels['operacoes.consulta.consultarDados']}"/>
                        </h:panelGrid>
                        <rich:dataTable id="resultadoConsultaUtensilio" width="100%" headerClass="consulta" rowClasses="linhaImpar, linhaPar"
                                        columnClasses="colunaAlinhamento" value="#{OrcamentoDetalhadoControle.utensiliosConsulta}" rows="5" var="utensilioConsulta">
                            <rich:column>
                                <f:facet name="header">
                                    <h:outputText value="#{CElabels['entidade.nome']}"/>
                                </f:facet>
                                <h:panelGroup>
                                    <h:outputText value="#{utensilioConsulta.descricao}" />
                                </h:panelGroup>
                            </rich:column>
                            <rich:column>
                                <f:facet name="header">
                                    <h:outputText value="#{CElabels['operacoes.opcoes']}"/>
                                </f:facet>
                                <a4j:commandButton action="#{OrcamentoDetalhadoControle.selecionarUtensilioConsulta}"
                                                   reRender="utensilios, utensiliosSelecionados, panelMesangem" oncomplete="Richfaces.hideModalPanel('panelConsultaUtensilio')" value="#{CElabels['operacoes.selecionar']}"
                                                   image="/imagens/botaoEditar.png" title="#{CElabels['operacoes.selecionar.selecionarDado']}" styleClass="botoes"/>
                            </rich:column>
                        </rich:dataTable>
                        <rich:datascroller align="center" for="formConsultaUtensilio:resultadoConsultaUtensilio" maxPages="10" id="scResultadoConsultaUtensilio" />
                        <h:panelGrid id="mensagemConsultaUtensilio" columns="1" width="100%" styleClass="tabMensagens">
                            <h:panelGrid columns="1" width="100%">
                                <h:outputText styleClass="mensagem" value="#{OrcamentoDetalhadoControle.mensagem}"/>
                                <h:outputText styleClass="mensagemDetalhada" value="#{OrcamentoDetalhadoControle.mensagemDetalhada}"/>
                            </h:panelGrid>
                        </h:panelGrid>
                    </h:panelGrid>
                </a4j:form>
            </rich:modalPanel>

            <rich:modalPanel id="panelConsultaBrinquedo" autosized="true" shadowOpacity="true" width="450" height="250">
                <f:facet name="header">

                    <jsp:include page="../includes/topoReduzido.jsp"/>

                </f:facet>
                <f:facet name="header">
                    <h:panelGroup>
                        <h:outputText value="#{CElabels['entidade.brinquedos.consulta']}"></h:outputText>
                    </h:panelGroup>
                </f:facet>
                <f:facet name="controls">
                    <h:panelGroup>
                        <h:graphicImage value="/imagens/close.png" style="cursor:pointer" id="hidelinkBrinquedo"/>
                        <rich:componentControl for="panelConsultaBrinquedo" attachTo="hidelinkBrinquedo" operation="hide" event="onclick"/>
                    </h:panelGroup>
                </f:facet>

                <a4j:form id="formConsultaBrinquedo" ajaxSubmit="true">
                    <h:panelGrid columns="1" width="100%" columnClasses="colunaCentralizada">
                        <h:panelGrid columns="3" footerClass="colunaCentralizada" width="100%">
                            <h:outputText value="#{CElabels['entidade.nome']}:"/>
                            <h:inputText id="valorConsultaBrinquedo" size="50" onblur="blurinput(this);" onfocus="focusinput(this);"
                                         styleClass="form" value="#{OrcamentoDetalhadoControle.filtroBrinquedos.descricao}"/>
                            <a4j:commandButton id="btnConsultarBrinquedo" reRender="formConsultaBrinquedo"
                                               action="#{OrcamentoDetalhadoControle.consultarBrinquedos}" styleClass="botoes"
                                               value="#{CElabels['operacoes.consulta.consultar']}" image="/imagens/botoesCE/buscar.png"
                                               title="#{CElabels['operacoes.consulta.consultarDados']}"/>
                        </h:panelGrid>
                        <rich:dataTable id="resultadoConsultaBrinquedo" width="100%" headerClass="consulta" rowClasses="linhaImpar, linhaPar"
                                        columnClasses="colunaAlinhamento" value="#{OrcamentoDetalhadoControle.brinquedosConsulta}" rows="5" var="brinquedoConsulta">
                            <rich:column>
                                <f:facet name="header">
                                    <h:outputText value="#{CElabels['entidade.nome']}"/>
                                </f:facet>
                                <h:panelGroup>
                                    <h:outputText value="#{brinquedoConsulta.descricao}" />
                                </h:panelGroup>
                            </rich:column>
                            <rich:column>
                                <f:facet name="header">
                                    <h:outputText value="#{CElabels['operacoes.opcoes']}"/>
                                </f:facet>
                                <a4j:commandButton action="#{OrcamentoDetalhadoControle.selecionarBrinquedoConsulta}"
                                                   reRender="brinquedos, brinquedosSelecionados, panelMesangem" oncomplete="Richfaces.hideModalPanel('panelConsultaBrinquedo')" value="#{CElabels['operacoes.selecionar']}"
                                                   image="/imagens/botaoEditar.png" title="#{CElabels['operacoes.selecionar.selecionarDado']}" styleClass="botoes"/>
                            </rich:column>
                        </rich:dataTable>
                        <rich:datascroller align="center" for="formConsultaBrinquedo:resultadoConsultaBrinquedo" maxPages="10" id="scResultadoConsultaBrinquedo" />
                        <h:panelGrid id="mensagemConsultaBrinquedo" columns="1" width="100%" styleClass="tabMensagens">
                            <h:panelGrid columns="1" width="100%">
                                <h:outputText styleClass="mensagem" value="#{OrcamentoDetalhadoControle.mensagem}"/>
                                <h:outputText styleClass="mensagemDetalhada" value="#{OrcamentoDetalhadoControle.mensagemDetalhada}"/>
                            </h:panelGrid>
                        </h:panelGrid>
                    </h:panelGrid>
                </a4j:form>
            </rich:modalPanel>
            <rich:modalPanel id="panelConsultaServico" autosized="true" shadowOpacity="true" width="450" height="250">
                <f:facet name="header">

                    <jsp:include page="../includes/topoReduzido.jsp"/>

                </f:facet>
                <f:facet name="header">
                    <h:panelGroup>
                        <h:outputText value="#{CElabels['entidade.servicos.consulta']}"></h:outputText>
                    </h:panelGroup>
                </f:facet>
                <f:facet name="controls">
                    <h:panelGroup>
                        <h:graphicImage value="/imagens/close.png" style="cursor:pointer" id="hidelinkServico"/>
                        <rich:componentControl for="panelConsultaServico" attachTo="hidelinkServico" operation="hide" event="onclick"/>
                    </h:panelGroup>
                </f:facet>

                <a4j:form id="formConsultaServico" ajaxSubmit="true">
                    <h:panelGrid columns="1" width="100%" columnClasses="colunaCentralizada">
                        <h:panelGrid columns="3" footerClass="colunaCentralizada" width="100%">
                            <h:outputText value="#{CElabels['entidade.nome']}:"/>
                            <h:inputText id="valorConsultaServico" size="50" onblur="blurinput(this);" onfocus="focusinput(this);"
                                         styleClass="form" value="#{OrcamentoDetalhadoControle.filtroServicos.descricao}"/>
                            <a4j:commandButton id="btnConsultarServico" reRender="formConsultaServico"
                                               action="#{OrcamentoDetalhadoControle.consultarServico}" styleClass="botoes"
                                               value="#{CElabels['operacoes.consulta.consultar']}" image="/imagens/botoesCE/buscar.png"
                                               title="#{CElabels['operacoes.consulta.consultarDados']}"/>
                        </h:panelGrid>
                        <rich:dataTable id="resultadoConsultaServico" width="100%" headerClass="consulta" rowClasses="linhaImpar, linhaPar"
                                        columnClasses="colunaAlinhamento" value="#{OrcamentoDetalhadoControle.servicosConsulta}" rows="5" var="servico">
                            <rich:column>
                                <f:facet name="header">
                                    <h:outputText value="#{CElabels['entidade.nome']}"/>
                                </f:facet>
                                <h:panelGroup>
                                    <h:outputText value="#{servico.descricao}" />
                                </h:panelGroup>
                            </rich:column>
                            <rich:column>
                                <f:facet name="header">
                                    <h:outputText value="#{CElabels['operacoes.opcoes']}"/>
                                </f:facet>
                                <a4j:commandButton action="#{OrcamentoDetalhadoControle.selecionarServicoConsulta}"
                                                   reRender="servicos, servicosSelecionados, panelMesangem" oncomplete="Richfaces.hideModalPanel('panelConsultaServico')" value="#{CElabels['operacoes.selecionar']}"
                                                   image="/imagens/botaoEditar.png" title="#{CElabels['operacoes.selecionar.selecionarDado']}" styleClass="botoes"/>
                            </rich:column>
                        </rich:dataTable>
                        <rich:datascroller align="center" for="formConsultaServico:resultadoConsultaServico" maxPages="10" id="scResultadoConsultaServico" />
                        <h:panelGrid id="mensagemConsultaServico" columns="1" width="100%" styleClass="tabMensagens">
                            <h:panelGrid columns="1" width="100%">
                                <h:outputText styleClass="mensagem" value="#{OrcamentoDetalhadoControle.mensagem}"/>
                                <h:outputText styleClass="mensagemDetalhada" value="#{OrcamentoDetalhadoControle.mensagemDetalhada}"/>
                            </h:panelGrid>
                        </h:panelGrid>
                    </h:panelGrid>
                </a4j:form>
            </rich:modalPanel>
            <rich:modalPanel id="panelConsultaServicoTercerizado" autosized="true" shadowOpacity="true" width="450" height="250">
                <f:facet name="header">

                    <jsp:include page="../includes/topoReduzido.jsp"/>

                </f:facet>
                <f:facet name="header">
                    <h:panelGroup>
                        <h:outputText value="#{CElabels['entidade.servicoTercerizado']}"></h:outputText>
                    </h:panelGroup>
                </f:facet>
                <f:facet name="controls">
                    <h:panelGroup>
                        <h:graphicImage value="/imagens/close.png" style="cursor:pointer" id="hidelinkServicoTercerizado"/>
                        <rich:componentControl for="panelConsultaServicoTercerizado" attachTo="hidelinkServicoTercerizado" operation="hide" event="onclick"/>
                    </h:panelGroup>
                </f:facet>

                <a4j:form id="formConsultaServicoTercerizado" ajaxSubmit="true">
                    <h:panelGrid columns="1" width="100%" columnClasses="colunaCentralizada">
                        <h:panelGrid columns="3" footerClass="colunaCentralizada" width="100%">
                            <h:outputText value="#{CElabels['entidade.nome']}:"/>
                            <h:inputText id="valorConsultaServicoTercerizado" size="50" onblur="blurinput(this);" onfocus="focusinput(this);"
                                         styleClass="form" value="#{OrcamentoDetalhadoControle.filtroServicosTercerizados.descServico}"/>
                            <a4j:commandButton id="btnConsultarServicoTercerizado" reRender="formConsultaServicoTercerizado"
                                               action="#{OrcamentoDetalhadoControle.consultarServicoTercerizado}" styleClass="botoes"
                                               value="#{CElabels['operacoes.consulta.consultar']}" image="/imagens/botoesCE/buscar.png"
                                               title="#{CElabels['operacoes.consulta.consultarDados']}"/>
                        </h:panelGrid>
                        <rich:dataTable id="resultadoConsultaServicoTercerizado" width="100%" headerClass="consulta" rowClasses="linhaImpar, linhaPar"
                                        columnClasses="colunaAlinhamento" value="#{OrcamentoDetalhadoControle.servicoTercerizadoConsulta}" rows="5" var="servicoTercerizado">
                            <rich:column>
                                <f:facet name="header">
                                    <h:outputText value="#{CElabels['entidade.nome']}"/>
                                </f:facet>
                                <h:panelGroup>
                                    <h:outputText value="#{servicoTercerizado.descricao}" />
                                </h:panelGroup>
                            </rich:column>
                            <rich:column>
                                <f:facet name="header">
                                    <h:outputText value="#{CElabels['operacoes.opcoes']}"/>
                                </f:facet>
                                <a4j:commandButton action="#{OrcamentoDetalhadoControle.selecionarServTerceConsulta}"
                                                   reRender="servicosTercerizado, servicosSelecionadosTercerizado, panelMesangem" oncomplete="Richfaces.hideModalPanel('panelConsultaServicoTercerizado')" value="#{CElabels['operacoes.selecionar']}"
                                                   image="/imagens/botaoEditar.png" title="#{CElabels['operacoes.selecionar.selecionarDado']}" styleClass="botoes"/>
                            </rich:column>
                        </rich:dataTable>
                        <rich:datascroller align="center" for="formConsultaServicoTercerizado:resultadoConsultaServicoTercerizado" maxPages="10" id="scResultadoConsultaServicoTercerizado" />
                        <h:panelGrid id="mensagemConsultaServicoTercerizado" columns="1" width="100%" styleClass="tabMensagens">
                            <h:panelGrid columns="1" width="100%">
                                <h:outputText styleClass="mensagem" value="#{OrcamentoDetalhadoControle.mensagem}"/>
                                <h:outputText styleClass="mensagemDetalhada" value="#{OrcamentoDetalhadoControle.mensagemDetalhada}"/>
                            </h:panelGrid>
                        </h:panelGrid>
                    </h:panelGrid>
                </a4j:form>
            </rich:modalPanel>
            <rich:modalPanel id="panelTextoLivreItem" autosized="true" shadowOpacity="true" width="420" height="220"
                             onshow="document.getElementById('formTextoLivreItem:textoLivreItem').focus();" domElementAttachment="parent">
                <f:facet name="header">
                    <h:panelGroup>
                        <h:outputText value="#{CElabels['entidade.textoLivre']}"></h:outputText>
                    </h:panelGroup>
                </f:facet>
                <f:facet name="controls">
                    <h:panelGroup>
                        <h:graphicImage value="/imagens/close.png" style="cursor:pointer" id="hidelinkTextoLivreItem"
                                        onclick="document.getElementById('formTextoLivreItem:fecharEdicaoTextoLivre').click();"/>
                        <rich:componentControl for="panelTextoLivreItem" attachTo="hidelinkTextoLivreItem" operation="hide" event="onclick"/>
                    </h:panelGroup>
                </f:facet>
                <a4j:form id="formTextoLivreItem" ajaxSubmit="true">
                    <rich:editor id="textoLivreItem" viewMode="visual" width="400" height="200"
                                 value="#{OrcamentoDetalhadoControle.textoLivre}" />
                    <a4j:commandButton id="fecharEdicaoTextoLivre" 
                                       action="#{OrcamentoDetalhadoControle.fecharEdicaoTextoLivre}"
                                       reRender="form:dttaCaucaoCredito"
                                       style="display: none;"/>
                </a4j:form>
            </rich:modalPanel>
            <rich:modalPanel showWhenRendered="#{OrcamentoDetalhadoControle.alteracaoPermitida}" id="panelMsgAlterar" autosized="true" shadowOpacity="true" width="150" height="70">
                <f:facet name="controls">
                    <h:panelGroup>
                        <h:graphicImage value="/imagens/close.png" style="cursor:pointer" id="hidelinkMsgSucesso"/>
                        <rich:componentControl for="panelMsgSucesso" attachTo="hidelinkMsgSucesso" operation="hide" event="onclick"/>
                    </h:panelGroup>
                </f:facet>
                <h:panelGrid columns="1" width="100%" style="text-align: center;">
                    <h:outputText styleClass="titulo3" value="#{Mensagens['operacoes.salvar.sucesso']}"/>
                    <br />
                    <h:form><h:commandButton action="#{OrcamentoDetalhadoControle.confirmarAlteracoes}" value="OK"
                                             actionListener="#{CadastroInicialControle.selCadastroInicialListener}">
                            <f:attribute name="codigoEventoInteresse" value="#{OrcamentoDetalhadoControle.negociacaoEvento.codigoEventoInteresse}"/>
                        </h:commandButton>
                    </h:form>
                </h:panelGrid>
            </rich:modalPanel>
            <%@include file="/pages/ce/includes/include_modal_alterarValores.jsp" %>
            <%@include file="/pages/ce/includes/include_modal_cacaoecredito.jsp" %>
            <%@include file="/pages/ce/includes/include_modal_cacaoecredito_quitacao.jsp" %>
            <%@include file="/pages/ce/includes/include_modal_disponibilidades_orcamentoDetalhado.jsp" %>
            <%@include file="/pages/ce/includes/include_modal_detalhamentoEventoOrcamentoDetalhado.jsp" %>
            <%@include file="/pages/ce/includes/include_modal_senhaValorProdLocacao.jsp" %>
            <%@include file="/pages/ce/includes/include_modal_senhaValorServico.jsp" %>
            <%@include file="/pages/ce/includes/include_descontoProdutoLocacao.jsp" %>
            <%@include file="/pages/ce/includes/include_descontoAmbiente.jsp" %>
            <%@include file="/pages/ce/includes/include_focus.jsp" %>

            <jsp:include page="../../../includes/include_modal_mensagem_generica.jsp" flush="true"/>
        </body>
    </html>
</f:view>
