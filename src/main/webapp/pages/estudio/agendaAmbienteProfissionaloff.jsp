<%-- 
    Document   : agendaAmbienteProfissional
    Created on : Feb 28, 2012, 3:24:58 PM
    Author     : <PERSON>
--%>


<%@include file="includes/include_imports.jsp" %>
<c:set var="moduloSession" value="1" scope="session" />


<html>
    <head>
        <script type="text/javascript" language="javascript" src="script/basico.js"></script>
        <script type="text/javascript" language="javascript" src="${contexto}/script/script.js"></script>
        <script type="text/javascript" language="javascript" src="${contexto}/hoverform.js"></script>
        <script type="text/javascript" language="javascript" src="${contexto}/script/telaInicial.js"></script>
        <link href="${contexto}/css/otimize.css" rel="stylesheet" type="text/css">
        <jsp:include page="includes/include_head.jsp" />
    </head>
    <body>
        <f:view>
            <jsp:include page="/includes/include_carregando_ripple.jsp" flush="true"/>
            <h:form id="agendaGeral" prependId="false">
                <table width="100%" height="100%" border="0" cellpadding="0" cellspacing="0">
                    <!-- Topo e menu superior -->
                    <c:if test="${MenuControle.apresentarTopo}">
                        <tr>
                            <td height="77" align="left" valign="top" class="bgtop"><jsp:include page="../../include_top.jsp" flush="true" />	</td>
                        </tr>

                        <tr>
                            <td height="48" align="left" valign="top" class="bgmenu"><jsp:include page="includes/include_menu_sem_modal.jsp" flush="true" /></td>
                        </tr> 
                    </c:if>
                    <tr>
                        <td align="left" valign="top" class="bglateral">
                            <table width="100%" height="100%" align="center" border="0" cellpadding="0" cellspacing="0">
                                <tr>
                                    <td width="206" align="center" valign="top" class="bglateraltop" style="padding-top:1px;">

                                        <jsp:include page="includes/include_box_menulateral.jsp" />
                                        <jsp:include page="includes/include_box_descricao.jsp" />
                                    </td>
                                    <td align="top" valign="top" height="100%" style="padding:7px 15px 0 20px;">
                                        <div style="text-align:center;">
                                            <table width="100%" border="0" align="center" cellpadding="0" cellspacing="0" class="text" style="margin-bottom:20px;">
                                                <tr>
                                                    <td width="19" height="50" align="left" valign="top"><img src="${contexto}/images/box_centro_top_left.gif" width="19" height="50"></td>
                                                    <td align="left" valign="top" background="${contexto}/images/box_centro_top.gif" class="tituloboxcentro" style="padding:1px 0 0 0;"></td>
                                                    <td width="19" align="left" valign="top"><img src="${contexto}/images/box_centro_top_right.gif" width="19" height="50"></td>
                                                </tr>
                                                <tr>
                                                    <td align="left" valign="top" background="${contexto}/images/box_centro_left.gif"><img src="${contexto}/images/shim.gif"></td>
                                                    <td align="left" valign="top" bgcolor="#ffffff" style="padding:5px 5px 5px 5px;">
                                                        <table width="100%" border="0" cellpadding="0" cellspacing="0">
                                                            <tr>
                                                                <td>
                                                                    <h:panelGrid columns="1" style="padding-bottom: 05px;">
                                                                        <a4j:commandButton 
                                                                            value="FILTRO"
                                                                            reRender="tabPanel"
                                                                            onclick="#{rich:component('panelFiltro')}.show()"/>
                                                                    </h:panelGrid>
                                                                    <div style="overflow-y: scroll; overflow-x: hidden; height: 500px; padding-top: 05px;" >
                                                                        <table border="1" cellpadding="0" cellspacing="0" style="border-style: solid;" width="100%" >
                                                                            <tr style="height:30px;">
                                                                                <td style="width:30px; height:30px;">

                                                                                </td> 
                                                                                <rich:column colspan="#{agendaAmbienteProfissionalControle.tamanhoColunas}" style="text-align: center;" >
                                                                                    <h:outputText style="font-weight:bold;" value="#{agendaAmbienteProfissionalControle.hojePorExtenso}"/>
                                                                                </rich:column>
                                                                            </tr>

                                                                            <tr>
                                                                                <td style="height:30px; text-align: center;">
                                                                                    <h:outputText value="Hora" style="text-align: center;"/>
                                                                                </td>
                                                                                <a4j:repeat value="#{empty agendaAmbienteProfissionalControle.listaAmbiente ? 
                                                                                                     empty agendaAmbienteProfissionalControle.listaPessoa ? 1 :
                                                                                                     agendaAmbienteProfissionalControle.listaPessoa : 
                                                                                                     agendaAmbienteProfissionalControle.listaAmbiente}" var="item" rowKeyVar="idx">
                                                                                            <td style="height:30px;text-align: center;">
                                                                                                <h:outputText  value="#{empty agendaAmbienteProfissionalControle.listaAmbiente ? 
                                                                                                                        empty agendaAmbienteProfissionalControle.listaPessoa ? '' : 
                                                                                                                        item.nome : item.descricao}" />
                                                                                            </td>
                                                                                </a4j:repeat>
                                                                                <td style="height:30px; text-align: center;">
                                                                                    <h:outputText value="Hora" />
                                                                                </td>
                                                                            </tr>

                                                                            <a4j:repeat value="#{agendaAmbienteProfissionalControle.horas}" var="hour" rowKeyVar="idh">
                                                                                <tr>
                                                                                    <td style="width: 50px; height:30px; text-align: center;" >
                                                                                        <h:outputText value="#{hour}:00" />
                                                                                    </td>
                                                                                    <a4j:repeat value="#{empty agendaAmbienteProfissionalControle.listaAmbiente and 
                                                                                                         empty agendaAmbienteProfissionalControle.listaPessoa ? 1 : agendaAmbienteProfissionalControle.listaAgendaVO}" var="item" rowKeyVar="idx">
                                                                                                <td style="height:30px; text-align: center;">

                                                                                                    <a4j:commandButton 
                                                                                                        value="#{empty agendaAmbienteProfissionalControle.listaAmbiente and 
                                                                                                                 empty agendaAmbienteProfissionalControle.listaPessoa ? '' :
                                                                                                                 item.hora == hour ? empty agendaAmbienteProfissionalControle.listaAmbiente ?
                                                                                                                 item.ambienteVO.descricao : item.pessoaVO.nome  : ''}"
                                                                                                        reRender="area"
                                                                                                        style="width:100%;"
                                                                                                        ondblclick="#{rich:component('panelAgendaAluno')}.show()"/>
                                                                                                </td>
                                                                                    </a4j:repeat>

                                                                                    <td style="width: 50px; height:30px; text-align: center;">
                                                                                        <h:outputText value="#{hour}:00" />
                                                                                    </td>
                                                                                </tr> 
                                                                            </a4j:repeat>
                                                                        </table>
                                                                    </div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                        <rich:spacer height="5" />
                                                        <rich:panel style="height:150px; width: 580px;" >
                                                            <h:outputText value="Dados do hor�rio" style="font-size:15px;font-weight:bold"/>
                                                            <h:panelGrid columns="3">
                                                                <h:graphicImage value="../../../images/user.jpg" style="width:90px" />
                                                                <h:panelGrid columns="1">
                                                                 <h:outputText value="Aluno: Bruna Andrade Silva" style="font-size:13px;font-weight:bold;width:150px;"/>
                                                              </h:panelGrid>
                                                                 <h:panelGrid columns="1">
                                                                 <h:outputText value="Tipo de Servi�o: Personal 3X" style="font-size:13px;font-weight:bold;width:150px;"/>
                                                              </h:panelGrid>
                                                            </h:panelGrid>
                                                        </rich:panel>
                                                    </td>
                                                    <td align="left" valign="top" background="${contexto}/images/box_centro_right.gif"><img src="${contexto}/images/shim.gif"></td>
                                                </tr>
                                                <tr>
                                                    <td height="20" align="left" valign="top"><img src="${contexto}/images/box_centro_bottom_left.gif" width="19" height="20"></td>
                                                    <td align="left" valign="top" background="${contexto}/images/box_centro_bottom.gif"><img src="${contexto}/images/shim.gif"></td>
                                                    <td align="left" valign="top"><img src="${contexto}/images/box_centro_bottom_right.gif" width="19" height="20"></td>
                                                </tr>
                                            </table>
                                        </div>
                                        <a4j:commandButton style="visibility: hidden;" reRender="panelExpiracaoSenha"
                                                           id="btnAtualizaPagina">
                                        </a4j:commandButton>
                                    </td>
                                </tr>
                            </table>
                        </td>
                    </tr>
                    <tr>
                        <td height="93" align="left" valign="top" class="bgrodape"><jsp:include page="/include_rodape.jsp" flush="true" /></td>
                    </tr>
                </table>
            </h:form>

            <rich:modalPanel id="panelFiltro" autosized="true" shadowOpacity="true" 
                             showWhenRendered="false" width="550"
                             height="300">
                <f:facet name="header">
                    <h:panelGroup>
                        <h:outputText value="Filtro Ambiente/Profissional"/>
                    </h:panelGroup>
                </f:facet>
                <f:facet name="controls">
                    <h:panelGroup>
                        <h:graphicImage value="../../imagens/close.png" style="cursor:pointer" id="hidelinkPanelFiltro" />
                        <rich:componentControl for="panelFiltro" attachTo="hidelinkPanelFiltro"
                                               operation="hide" event="onclick" />
                    </h:panelGroup>
                </f:facet>

                <a4j:form id="formPanelFiltro" ajaxSubmit="true">

                    <rich:tabPanel
                        switchType="ajax" id="tabPanel" height="250">
                        <rich:tab
                            id="abaAmbiente" focus="modalAmbienteCodg-ambiente-codigo"
                            name="Ambiente" label="Ambiente">
                            <h:panelGrid columns="3" id="dadosDependente">
                                <h:outputLabel
                                    value="N� do Ambiente" />
                                <rich:spacer width="20px"/>
                                <h:outputLabel
                                    value="Nome" />

                                <h:inputText
                                    maxlength="4"
                                    autocomplete="off"
                                    size="3"
                                    value="#{agendaAmbienteProfissionalControle.ambienteVO.codigo}"
                                    id="modalAmbienteCodg-ambiente-codigo">
                                </h:inputText>
                                <rich:spacer width="20px"/>
                                <h:inputText
                                    maxlength="20"
                                    autocomplete="off"
                                    size="19"
                                    value="#{agendaAmbienteProfissionalControle.ambienteVO.descricao}"
                                    id="modalAmbienteCodg-ambiente-descricao">
                                </h:inputText>
                            </h:panelGrid>

                            <rich:spacer height="5" />
                            <h:panelGrid columns="2"> 
                                <rich:scrollableDataTable
                                    id="agendaAmbienteProfissionalControle-listaAmbiente"
                                    onRowClick="getById('agendaAmbienteProfissionalControle-listaAmbiente:'+ this.rowIndex +':filtro').onclick();"
                                    var="item"
                                    height="140px"
                                    width="450px"
                                    rows="5"
                                    frozenColCount="1"
                                    value="#{agendaAmbienteProfissionalControle.listaAmbiente}">

                                    <rich:column sortable="false" width="25">
                                        <f:facet name="header">
                                            <h:selectBooleanCheckbox
                                                id="selecionarTodosAmbiente"
                                                value="#{agendaAmbienteProfissionalControle.selecionarTodosItemAmbiente}">
                                                <a4j:support
                                                    action="#{agendaAmbienteProfissionalControle.acaoSelecionarTodosItemAmbiente}"
                                                    event="onclick"
                                                    reRender="agendaAmbienteProfissionalControle-listaAmbiente">
                                                </a4j:support>
                                            </h:selectBooleanCheckbox>
                                        </f:facet>
                                        <h:selectBooleanCheckbox
                                            id="itemSolicitacao-selecionado-ambiente"
                                            value="#{item.selecionado}">
                                            <a4j:support
                                                action="#{agendaAmbienteProfissionalControle.acaoSelecionarUmItemAmbiente}"
                                                event="onclick"
                                                reRender="selecionarTodosAmbiente,
                                                modalAmbienteCodg-ambiente-codigo,
                                                modalAmbienteCodg-ambiente-descricao">
                                                <f:setPropertyActionListener
                                                    value="#{item}"
                                                    target="#{agendaAmbienteProfissionalControle.ambienteVO}" />
                                            </a4j:support>
                                        </h:selectBooleanCheckbox> 
                                    </rich:column>

                                    <rich:column width="70px">
                                        <f:facet name="header">
                                            <h:outputText value="C�digo" />
                                        </f:facet>
                                        <h:outputText
                                            value="#{item.codigo}"
                                            style="float:right; margin-right: 5px;"/>
                                    </rich:column>
                                    <rich:column
                                        width="330px"
                                        sortBy="#{item.descricao}"
                                        filterBy="#{item.descricao}"
                                        filterEvent="onchange">
                                        <f:facet name="header">
                                            <h:outputText value="Descri��o" />
                                        </f:facet>
                                        <h:outputText
                                            value="#{item.descricao}"
                                            style="margin-left: 5px; position:relative;"/>
                                    </rich:column>
                                </rich:scrollableDataTable>

                                <h:panelGrid columns="1">
                                    <a4j:commandButton
                                        value="Pesquisar"
                                        title="Pesquisar" style="width:75px"/>
                                    <a4j:commandButton
                                        value="Adicionar"
                                        title="Adicionar" style="width:75px"/>
                                    <a4j:commandButton
                                        value="Limpar"
                                        title="Limpar" style="width:75px"/>
                                    <a4j:commandButton
                                        value="Remover"
                                        title="Remover" style="width:75px"/>
                                </h:panelGrid>
                            </h:panelGrid>
                        </rich:tab>

                        <rich:tab
                            id="abaProfissional" focus="modalAmbienteCodg-profissional-codigo"
                            name="Profissional"
                            label="Profissional">
                            <h:panelGrid columns="3">
                                <h:outputLabel
                                    value="N� do Profissional" />
                                <rich:spacer width="20px"/>
                                <h:outputLabel
                                    value="Nome" />

                                <h:inputText
                                    maxlength="4"
                                    autocomplete="off"
                                    size="3"
                                    value="#{agendaAmbienteProfissionalControle.pessoaVO.codigo}"
                                    id="modalAmbienteCodg-profissional-codigo">
                                </h:inputText>
                                <rich:spacer width="20px"/>
                                <h:inputText
                                    maxlength="20"
                                    autocomplete="off"
                                    size="19"
                                    value="#{agendaAmbienteProfissionalControle.pessoaVO.nome}"
                                    id="modalAmbienteCodg-profissional-descricao">
                                </h:inputText>
                            </h:panelGrid>

                            <rich:spacer height="5" />
                            <h:panelGrid columns="2"> 
                                <rich:scrollableDataTable
                                    id="agendaAmbienteProfissionalControle-listaPessoa"
                                    onRowClick="getById('agendaAmbienteProfissionalControle-listaPessoa:'+ this.rowIndex +':filtro').onclick();"
                                    var="item"
                                    rows="5"
                                    frozenColCount="1"
                                    height="140px"
                                    width="450px"
                                    value="#{agendaAmbienteProfissionalControle.listaPessoa}">

                                    <rich:column sortable="false" width="25">
                                        <f:facet name="header">
                                            <h:selectBooleanCheckbox
                                                id="selecionarTodosPessoa"
                                                value="#{agendaAmbienteProfissionalControle.selecionarTodosItemPessoa}">
                                                <a4j:support
                                                    action="#{agendaAmbienteProfissionalControle.acaoSelecionarTodosItemPessoa}"
                                                    event="onclick"
                                                    reRender="agendaAmbienteProfissionalControle-listaPessoa">
                                                </a4j:support>
                                            </h:selectBooleanCheckbox>
                                        </f:facet>
                                        <h:selectBooleanCheckbox
                                            id="itemSolicitacao-selecionado-pessoa"
                                            value="#{item.selecionado}">
                                            <a4j:support
                                                action="#{agendaAmbienteProfissionalControle.acaoSelecionarUmItemPessoa}"
                                                event="onclick"
                                                reRender="selecionarTodosPessoa,
                                                modalAmbienteCodg-profissional-codigo,
                                                modalAmbienteCodg-profissional-descricao">
                                                <f:setPropertyActionListener
                                                    value="#{item}"
                                                    target="#{agendaAmbienteProfissionalControle.pessoaVO}" />
                                            </a4j:support>
                                        </h:selectBooleanCheckbox> 
                                    </rich:column>

                                    <rich:column width="70px">
                                        <f:facet name="header">
                                            <h:outputText value="C�digo" />
                                        </f:facet>
                                        <h:outputText
                                            value="#{item.codigo}"
                                            style="float:right; margin-right: 5px;"/>
                                    </rich:column>
                                    <rich:column
                                        width="330px"
                                        sortBy="#{item.nome}"
                                        filterBy="#{item.nome}"
                                        filterEvent="onchange">
                                        <f:facet name="header">
                                            <h:outputText value="Nome" />
                                        </f:facet>
                                        <h:outputText
                                            value="#{item.nome}"
                                            style="margin-left: 5px; position:relative;"/>
                                    </rich:column>
                                </rich:scrollableDataTable>

                                <h:panelGrid columns="1">
                                    <a4j:commandButton
                                        value="Pesquisar"
                                        title="Pesquisar" style="width:75px"/>
                                    <a4j:commandButton
                                        value="Adicionar"
                                        title="Adicionar" style="width:75px"/>
                                    <a4j:commandButton
                                        value="Limpar"
                                        title="Limpar" style="width:75px"/>
                                    <a4j:commandButton
                                        value="Remover"
                                        title="Remover" style="width:75px"/>
                                </h:panelGrid>
                            </h:panelGrid>
                        </rich:tab>
                    </rich:tabPanel>
                </a4j:form>

            </rich:modalPanel>

            <rich:modalPanel id="panelAgendaAluno" autosized="true" shadowOpacity="true" 
                             showWhenRendered="false" width="450"
                             height="290">
                <f:facet name="header">
                    <h:panelGroup>
                        <h:outputText value="Agenda Aluno"></h:outputText>

                    </h:panelGroup>
                </f:facet>
                <f:facet name="controls">
                    <h:panelGroup>
                        <h:graphicImage value="../../imagens/close.png" style="cursor:pointer" id="hidelinkPanelAgenda" />
                        <rich:componentControl for="panelAgendaAluno" attachTo="hidelinkPanelAgenda"
                                               operation="hide" event="onclick" />
                    </h:panelGroup>
                </f:facet>

                <a4j:form id="formPanelAgendaAluno" ajaxSubmit="true">

                    <h:panelGrid columns="3" >
                        <h:outputText value="Matr�cula"/>
                        <h:outputText value="Aluno"/>
                        <rich:spacer />
                        <h:panelGrid columns="2">
                            <h:inputText value="112" size="10"/>
                        </h:panelGrid>
                        <h:panelGrid columns="2">
                            <h:inputText value="Bruna Andrade Silva de Oliveira" size="30"/>
                        </h:panelGrid>
                        <a4j:commandButton 
                            id="link2"
                            action="#{testeController.acaoSelecionar}"
                            value="Novo Cliente"
                            reRender="panelAgendaAluno"
                            oncomplete="#{rich:component('panelAgendaAluno')}.hide(); #{rich:component('panelCadastroSimplificadoCliente')}.show();">
                        </a4j:commandButton>
                    </h:panelGrid>

                    <h:panelGrid columns="3" style="width:400px">
                        <h:graphicImage value="../../../images/user.jpg" style="width:90px" />
                        
                        <h:panelGrid columns="1" >
                            <h:outputLabel
                                id="lb-turma-diasSemana"
                                value="Dias da Semana:"/>
                            <h:selectOneRadio id="turma-diasSemana"
                                              label="Dias da Semana"
                                              value="#{testeController.diaSemana}">
                                <f:selectItem itemValue="1" itemLabel="D"/>
                                <f:selectItem itemValue="2" itemLabel="S"/>
                                <f:selectItem itemValue="3" itemLabel="T"/>
                                <f:selectItem itemValue="4" itemLabel="Q"/>
                                <f:selectItem itemValue="5" itemLabel="Q"/>
                                <f:selectItem itemValue="6" itemLabel="S"/>
                                <f:selectItem itemValue="7" itemLabel="S"/>
                            </h:selectOneRadio>

                            <h:outputLabel
                                id="lb-servico"
                                style="width:200px"
                                styleClass="classLabel"
                                value="Servi�o"/>
                            <h:selectOneMenu
                                id="listaServico"
                                style="width:200px"
                                value="#{testeController.modalidade}"
                                title="Servi�o">
                                <f:selectItem itemLabel="" />
                                <f:selectItems value="#{testeController.listaModalidade}" />
                            </h:selectOneMenu>

                            <h:outputLabel
                                id="lb-tipoHorario"
                                style="width:200px"
                                styleClass="classLabel"
                                value="Tipo Hor�rio:"/>
                            <h:selectOneMenu
                                id="tipoHorario"
                                style="width:200px"
                                value="#{testeController.tipoHorario}"
                                title="Modalidade">
                                <f:selectItem itemLabel="" />
                                <f:selectItems value="#{testeController.listaTipoHorario}" />
                            </h:selectOneMenu>
                        </h:panelGrid>

                        <h:panelGrid columns="1">

                            <h:outputLabel
                                id="lb-hora"
                                styleClass="classLabel"
                                value="Hora:"/>
                            <h:inputText value="#{testeController.hora} : 00" size="5"/>
                            <h:outputLabel
                                id="lb-hora-desc"
                                styleClass="classLabel"
                                value="60min"/>

                        </h:panelGrid>
                    </h:panelGrid>

                </a4j:form>
            </rich:modalPanel>


        </f:view>
    </body>
</html>