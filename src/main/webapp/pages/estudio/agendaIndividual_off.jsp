<%-- 
    Document   : agendaTabela
    Created on : Feb 14, 2012, 3:24:58 PM
    Author     : <PERSON>
--%>


<%@include file="includes/include_imports.jsp" %>
<c:set var="moduloSession" value="1" scope="session" />


<html>
    <head>
        <script type="text/javascript" language="javascript" src="${contexto}/script/script.js"></script>
        <script type="text/javascript" language="javascript" src="${contexto}/hoverform.js"></script>
        <link href="${contexto}/css/otimize.css" rel="stylesheet" type="text/css">

        <style type="text/css">
            .teste {
                background-color: white;
            }

            .teste1 {
                background-color: black;
            }
            .teste2 {
                background-color: red;
            }
            .teste3 {
                background-color: green;
            }
        </style>

    </head>
    <body>
        <f:view>
            <jsp:include page="/includes/include_carregando_ripple.jsp" flush="true"/>
            <h:form id="agendaGeral" prependId="false">
                <table width="100%" border="1" cellpadding="0" cellspacing="0">
                    <tr>
                        <td>
                            <h:panelGrid columns="3">
                                <h:outputText value="Professor"/>
                                <h:selectOneMenu
                                    style="width:300px;"
                                    id="listaProfessor"
                                    value="#{testeController.professor}"
                                    title="Professor">
                                    <f:selectItem itemLabel="" />
                                    <f:selectItems value="#{testeController.listaProfessor}" />
                                </h:selectOneMenu>
                            </h:panelGrid>
                            <table border="1">
                                <tr style="width:30px; height:30px;">
                                    <td style="width:30px; height:30px;">

                                    </td>                        
                                    <a4j:repeat value="#{testeController.listaSemana}" var="item" rowKeyVar="idx">
                                        <td style="width:30px; text-align: center;">
                                            <h:outputText value="#{item.descricao}" /></td>
                                        </a4j:repeat>
                                </tr>
                                <tr>
                                    <td style="width:30px; height:30px;">
                                        <h:outputText value="Hora" style="text-align: center;"/>
                                    </td>
                                    <a4j:repeat value="#{testeController.listaSemana}" var="item" rowKeyVar="idx">
                                        <td style="width:30px; text-align: center;"><h:outputText  value="#{idx + 1}" /></td>
                                    </a4j:repeat>
                                    <td>
                                        <h:outputText value="Hora" />
                                    </td>
                                </tr>

                                <a4j:repeat value="#{testeController.horas}" var="hour" rowKeyVar="idh">
                                    <tr style="width:30px; height:30px;">
                                        <td style="width:30px; text-align: center;" >
                                            <h:outputText value="#{idh}:00" />
                                        </td>
                                        <a4j:repeat value="#{testeController.listaSemana}" var="item">
                                            <rich:column  style="width:30px; height:30px; text-align: center;" 
                                                          styleClass="#{item.hora == idh and item.status == 1 ? 'teste1' :
                                                                        item.hora == idh and item.status == 2 ? 'teste2' :
                                                                        item.hora == idh and item.status == 3 ? 'teste3' :'teste'}">

                                                          <a4j:commandButton styleClass="#{item.hora == idh and item.status == 1 ? 'teste1' :
                                                                        item.hora == idh and item.status == 2 ? 'teste2' :
                                                                        item.hora == idh and item.status == 3 ? 'teste3' :'teste'}"
                                                                        style="background-repeat: no-repeat;
                                                                             background-color: transparent;
                                                                             background-image:none;
                                                                             border-color: transparent;" 
                                                                             reRender="area"  
                                                                             action="#{testeController.acaoArea(idh)}" 
                                                                             ondblclick="#{rich:component('panelAgendaAluno')}.show()"/>
                                            </rich:column>
                                        </a4j:repeat>
                                        <td style="text-align: center;">
                                            <h:outputText value="#{idh}:00" />
                                        </td>
                                    </tr> 
                                </a4j:repeat>
                            </table>
                        </td>
                    </tr>
                </table>
                <h:panelGrid columns="1">
                    <h:inputTextarea value="#{testeController.area}" id="area" rows="2"/>
                </h:panelGrid>
            </h:form>

            <rich:modalPanel id="panelAgendaAluno" autosized="true" shadowOpacity="true" 
                             showWhenRendered="false" width="550"
                             height="300">
                <f:facet name="header">
                    <h:panelGroup>
                        <h:outputText value="Agenda Aluno"></h:outputText>

                    </h:panelGroup>
                </f:facet>
                <f:facet name="controls">
                    <h:panelGroup>
                        <h:graphicImage value="../../imagens/close.png" style="cursor:pointer" id="hidelinkPanelAgenda" />
                        <rich:componentControl for="panelAgendaAluno" attachTo="hidelinkPanelAgenda"
                                               operation="hide" event="onclick" />
                    </h:panelGroup>
                </f:facet>

                <a4j:form id="formPanelAgendaAluno" ajaxSubmit="true">

                    <h:panelGrid columns="3">
                        <h:outputText value="Matr�cula"/>
                        <h:outputText value="Aluno"/>
                        <rich:spacer />
                        <h:panelGrid columns="2">
                            <h:inputText value="123456" size="10"/>
                        </h:panelGrid>
                        <h:panelGrid columns="2">
                            <h:inputText value="Pedro Eug�nio" size="20"/>
                        </h:panelGrid>
                        <a4j:commandButton 
                            id="link2"
                            action="#{testeController.acaoSelecionar}"
                            value="Novo Cliente"
                            reRender="panelAgendaAluno"
                            oncomplete="#{rich:component('panelAgendaAluno')}.hide(); #{rich:component('panelCadastroSimplificadoCliente')}.show();">
                        </a4j:commandButton>
                    </h:panelGrid>

                    <h:panelGrid columns="3">
                        <h:graphicImage value="../../images/user.jpg" />

                        <h:panelGrid columns="1">
                            <h:outputLabel
                                id="lb-turma-diasSemana"
                                value="Dias da Semana:"/>
                            <h:selectOneRadio id="turma-diasSemana"
                                              label="Dias da Semana"
                                              value="#{testeController.diaSemana}">
                                <f:selectItem itemValue="1" itemLabel="D"/>
                                <f:selectItem itemValue="2" itemLabel="S"/>
                                <f:selectItem itemValue="3" itemLabel="T"/>
                                <f:selectItem itemValue="4" itemLabel="Q"/>
                                <f:selectItem itemValue="5" itemLabel="Q"/>
                                <f:selectItem itemValue="6" itemLabel="S"/>
                                <f:selectItem itemValue="7" itemLabel="S"/>
                            </h:selectOneRadio>

                            <h:outputLabel
                                id="lb-modalidade"
                                style="100px"
                                styleClass="classLabel"
                                value="Modalidade:"/>
                            <h:selectOneMenu
                                id="listaModalidade"
                                style="100px"
                                value="#{testeController.modalidade}"
                                title="Modalidade">
                                <f:selectItem itemLabel="" />
                                <f:selectItems value="#{testeController.listaModalidade}" />
                            </h:selectOneMenu>

                            <h:outputLabel
                                id="lb-tipoHorario"
                                styleClass="classLabel"
                                value="Tipo Hor�rio:"/>
                            <h:selectOneMenu
                                id="tipoHorario"
                                value="#{testeController.tipoHorario}"
                                title="Modalidade">
                                <f:selectItem itemLabel="" />
                                <f:selectItems value="#{testeController.listaTipoHorario}" />
                            </h:selectOneMenu>
                        </h:panelGrid>

                        <h:panelGrid columns="1">

                            <h:outputLabel
                                id="lb-hora"
                                styleClass="classLabel"
                                value="Hora:"/>
                            <h:inputText value="#{testeController.hora} : 00" size="5"/>

                        </h:panelGrid>
                    </h:panelGrid>

                </a4j:form>
            </rich:modalPanel>


            <rich:modalPanel id="panelCadastroSimplificadoCliente" autosized="true" shadowOpacity="true" 
                             showWhenRendered="false" width="550"
                             height="300" onshow="document.getElementById('formCadastroSimplificadoCliente:nomeCadastroSimplificadoCliente').focus();">
                <f:facet name="header">                    
					<jsp:include page="topoReduzido.jsp" />                    
                </f:facet>
                <f:facet name="header">
                    <h:panelGroup>
                        <h:outputText value="Cadastrar Cliente"></h:outputText>
                    </h:panelGroup>
                </f:facet>
                <f:facet name="controls">
                    <h:panelGroup>
                        <h:graphicImage value="../../imagens/close.png" style="cursor:pointer" id="hidelinkCadastroSimplificadoCliente" />
                        <rich:componentControl for="panelCadastroSimplificadoCliente" attachTo="hidelinkCadastroSimplificadoCliente"
                                               operation="hide" event="onclick" />
                    </h:panelGroup>
                </f:facet>

                <a4j:form id="formCadastroSimplificadoCliente" ajaxSubmit="true">
                    <h:panelGrid columns="1" width="100%">
                        <h:panelGrid columns="1" style="height:25px; background-image:url('./imagens/fundoBarraTopo.png');background-repeat: repeat-x;"
                                     columnClasses="colunaCentralizada" width="100%">
                            <h:outputText styleClass="tituloFormulario" value="Cadastro de Cliente" />
                        </h:panelGrid>

                        <h:panelGrid columns="2" id="panelCadastroSimples" rowClasses="linhaImpar, linhaPar"
                                     columnClasses="classEsquerda, classDireita" width="100%">
                            <h:panelGroup>
                                <h:outputText value="#{msg_aplic.prt_Asterisco}" rendered="#{ClienteControle.configuracaoSistema.nomeOb}"/>
                                <h:outputText value="Nome Completo " />
                            </h:panelGroup>
                            <h:panelGroup>
                                <h:inputText id="nomeCadastroSimplificadoCliente" size="50" maxlength="50" onblur="blurinput(this);"
                                             onfocus="focusinput(this);" styleClass="form" value="#{ClienteControle.pessoaVO.nome}" />                       

                                <rich:suggestionbox   height="200" width="400"
                                                      for="nomeCadastroSimplificadoCliente"
                                                      status="statusHora"
                                                      immediate="true"
                                                      suggestionAction="#{ClienteControle.executarAutocompleteClientePotencialVO}"
                                                      minChars="3"
                                                      rowClasses="linhaImpar, linhaPar"                                              
                                                      var="result"  id="suggestionResponsavel">
                                    <a4j:support event="onselect" reRender="panelCadastroSimples" focus="nomeCadastroSimplificadoCliente"  
                                                 action="#{ClienteControle.selecionarClientePotencial}">
                                    </a4j:support>
                                    <h:column>
                                        <f:facet name="header">
                                            <h:outputText value="Nome"  styleClass="textverysmall"/>
                                        </f:facet>
                                        <h:outputText styleClass="textverysmall" value="#{result.passivoVO.nome}" rendered="#{result.isApresentarPassivo}" />
                                        <h:outputText styleClass="textverysmall" value="#{result.indicadoVO.nomeIndicado}" rendered="#{result.isApresentarIndicado}"/>
                                        <h:outputText styleClass="textverysmall" value="#{result.clienteVO.pessoa.nome}"  rendered="#{result.isApresentarCliente}"/>
                                    </h:column>
                                    <h:column >  
                                        <f:facet name="header">
                                            <h:outputText value="Tipo" styleClass="textverysmall"/>
                                        </f:facet>
                                        <h:outputText styleClass="textverysmall" value="#{result.tipoPessoa}" />
                                    </h:column>
                                    <h:column >  
                                        <f:facet name="header">
                                            <h:outputText value="Telefone" styleClass="textverysmall"/>
                                        </f:facet>
                                        <h:outputText  styleClass="textverysmall" value="#{result.telefone}" />
                                    </h:column>
                                </rich:suggestionbox>
                            </h:panelGroup>
                            <h:panelGroup>
                                <h:outputText value="#{msg_aplic.prt_Asterisco}" rendered="#{ClienteControle.configuracaoSistema.dataNascOb}"/>
                                <h:outputText value="Data Nascimento" />
                            </h:panelGroup>
                            <h:panelGroup>
                                <%--
                                <rich:calendar id="dataNascCadastroSimplificadoCliente" value="#{ClienteControle.pessoaVO.dataNasc}" inputSize="10"
                                               inputClass="form" oninputblur="blurinput(this);" oninputfocus="focusinput(this);"
                                               oninputchange="return validar_Data(this.id);" oninputkeypress="return mascara(this.form, this.id, '99/99/9999', event);"
                                               datePattern="dd/MM/yyyy" enableManualInput="true" zindex="2" showWeeksBar="false" />

                        <h:inputText id="dataNascCadastroSimplificadoCliente" value="#{ClienteControle.pessoaVO.dataNasc}" size="10"
                                     styleClass="form" onblur="blurinput(this);" onfocus="focusinput(this);"
                                     onkeypress="return mascara(this.form, this.id, '99/99/9999', event);">
                            <f:convertDateTime pattern="dd/MM/yyyy" locale="#{SuperControle.localeDefault}" timeZone="#{SuperControle.timeZoneDefault}"/>
                            <a4j:support event="onchange" action="#{ClienteControle.validarIdade}" reRender="form,nomeMaeApresentar" />
                        </h:inputText>
                        <h:message for="dataNascCadastroSimplificadoCliente" styleClass="mensagemDetalhada" />--%>
                                <h:inputText id="dataNascCadastroSimplificadoCliente" value="#{ClienteControle.pessoaVO.dataNasc}" size="10"
                                             styleClass="form" onblur="blurinput(this);" onfocus="focusinput(this);"
                                             onchange="return validar_Data(this.id); return validarIdade(#{rich:component('dataNascCadastroSimplificadoCliente')}.getCurrentDate());"
                                             onkeypress="return mascara(this.form, this.id, '99/99/9999', event);">
                                    <f:convertDateTime pattern="dd/MM/yyyy" locale="#{SuperControle.localeDefault}" timeZone="#{SuperControle.timeZoneDefault}"/>
                                    <a4j:support focus="cpfCadastroSimplificadoCliente" event="onchange" action="#{ClienteControle.validarIdade}" reRender="form" />
                                </h:inputText>
                                <a4j:jsFunction name="validarIdade" action="#{ClienteControle.validarIdade}"
                                                focus="cpfCadastroSimplificadoCliente"
                                                reRender="form">
                                    <a4j:actionparam name="dtnasc" assignTo="#{ClienteControle.pessoaVO.dataNasc}"/>
                                </a4j:jsFunction>

                            </h:panelGroup>
                            <h:panelGroup>
                                <h:outputText value="#{msg_aplic.prt_Asterisco}" rendered="#{ClienteControle.configuracaoSistema.cfpOb}"/>
                                <h:outputText value="CPF" />
                            </h:panelGroup>
                            <h:panelGroup>
                                <rich:jQuery id="mskCPF" selector="#cpfCadastroSimplificadoCliente" timing="onload" query="mask('999.999.999-99')" />
                                <h:inputText id="cpfCadastroSimplificadoCliente" size="14" maxlength="14" onblur="blurinput(this);"
                                             onfocus="focusinput(this);" styleClass="form" value="#{ClienteControle.pessoaVO.cfp}">
                                    <a4j:support event="onchange" action="#{ClienteControle.validarClienteSimples}"
                                                 reRender="formCadastroSimplificadoCliente,panelMensagem" />
                                </h:inputText>
                                <h:inputHidden id="inputInvisivel" />
                            </h:panelGroup>
                        </h:panelGrid>
                        <h:panelGrid id="panelMensagem" columns="1" width="100%" styleClass="tabMensagens">
                            <h:panelGrid columns="1" width="100%">
                                <h:outputText id="msgPanelCadas" styleClass="mensagem" value="#{ClienteControle.mensagem}" />
                                <h:outputText id="msgPanelCadasDet"  styleClass="mensagemDetalhada" value="#{ClienteControle.mensagemDetalhada}" />
                            </h:panelGrid>
                            <h:panelGrid columns="1" width="100%" columnClasses="colunaCentralizada">
                                <h:panelGroup>
                                    <a4j:commandButton onmousemove="document.getElementById('formCadastroSimplificadoCliente:salvarCadastroSimplificadoCliente').focus();" 
                                                       id="salvarCadastroSimplificadoCliente" action="#{ClienteControle.validarClienteSimples}" focus="nomeCliente"
                                                       value="#{msg_bt.btn_gravar}" image="../imagens/botaoGravar.png" alt="#{msg.msg_gravar_dados}" accesskey="2" styleClass="botoes"
                                                       reRender="codigo,empresa,panelFoto,nomeCliente,dataNascCliente,categoria,nomeMae,nomePai,sexo,profissao,
                                                       grauIntrucao,estadoCivil,cpf,rg,rgOrgao,rgUf,dataCadastro,residencial,comercial,celular,email,
                                                       webPage,CEP,endereco,complento,bairro,numero,pais,estado,cidade,estado,panelExistePessoa,
                                                       panelExisteCliente,panelCadastroSimplificadoCliente" />
                                </h:panelGroup>
                            </h:panelGrid>
                        </h:panelGrid>
                    </h:panelGrid>
                </a4j:form>
            </rich:modalPanel>
        </f:view>
    </body>
</html>