<%@page pageEncoding="ISO-8859-1"%>
<!-- código -->
<h:outputText styleClass="tituloCampos" id="codMovContaLanc"  value="Código:"/>
<h:outputText styleClass="tituloCampos" value="#{MovContaControle.movContaVO.codigo}"/>

<!-- Lote de pagamento -->
<h:outputText rendered="#{MovContaControle.movContaVO.temLoteDePagamento}"
              styleClass="tituloCampos" id="codLotePgtoLanc"  value="Lote de Pagamento:"/>
<h:outputText rendered="#{MovContaControle.movContaVO.temLoteDePagamento}"
              styleClass="tituloCampos" value="#{MovContaControle.movContaVO.loteDePagamento}"/>

<!-- pago via webhook -->
<h:outputText rendered="#{MovContaControle.movContaVO.contaQuitada and MovContaControle.movContaVO.temLoteDePagamento}"
              styleClass="tituloCampos" id="infoPgViaWebhookLanc"  value="Pago via webhook:"/>
<h:outputText rendered="#{MovContaControle.movContaVO.contaQuitada and MovContaControle.movContaVO.temLoteDePagamento and !MovContaControle.movContaVO.pagoOrigemWebhook}"
              id="naoPgViaWebhookLanc"
              title="Essa conta não foi quitada automaticamente via webhook enviado pelo banco. <br> Provavelmente foi a sincronização manual do lote na tela de gestão de lotes de pagamento."
              styleClass="fa-icon-ok-sign tooltipster" style="color: #d93434; margin-left: 4px; margin-top: 2px; display: block;"/>
<h:outputText rendered="#{MovContaControle.movContaVO.contaQuitada and MovContaControle.movContaVO.temLoteDePagamento and MovContaControle.movContaVO.pagoOrigemWebhook}"
              id="pgViaWebhookLanc"
              title="Essa conta foi quitada automaticamente via webhook enviado pelo banco"
              styleClass="fa-icon-ok-sign tooltipster" style="color: #3cdb5c; margin-left: 4px; margin-top: 2px; display: block;"/>

<!-- responsável -->
<h:outputText id="respLancConta" rendered="#{MovContaControle.movContaVO.usuarioVO != null && MovContaControle.movContaVO.usuarioVO.codigo > 0 && MovContaControle.movContaVO.codigo > 0}"
              styleClass="tituloCampos" value="Responsável Lançamento:"/>
<h:outputText id="respLancContaValue" rendered="#{MovContaControle.movContaVO.usuarioVO != null && MovContaControle.movContaVO.usuarioVO.codigo > 0 && MovContaControle.movContaVO.codigo > 0}"
              styleClass="tituloCampos" value="#{MovContaControle.movContaVO.usuarioVO.nome}"/>
<!-- empresa -->
<h:outputText rendered="#{MovContaControle.mostrarCampoEmpresa}" styleClass="tituloCampos"
              id="empresaConta"
              value="*Empresa:"/>
<h:selectOneMenu rendered="#{MovContaControle.mostrarCampoEmpresa}"
                 id="empresa" onblur="blurinput(this);"
                 onfocus="focusinput(this);"
                 style="color:black;"
                 styleClass="form"
                 value="#{MovContaControle.movContaVO.empresaVO.codigo}">
    <f:selectItems value="#{MovContaControle.listaSelectItemsEmpresa}"/>
    <a4j:support event="onchange" action="#{MovContaControle.verificarPreenchimentoCampoEmpresa}"
                 reRender="pessoa, suggestionPessoa, nomePessoaLabel, contaSelectitem"/>
</h:selectOneMenu>

<!-- pessoa -->
<h:outputText styleClass="tituloCampos" id="nomeCampoPessoaFinan" rendered="#{MovContaControle.botoesCamposEnabled}" value="#{MovContaControle.nomeCampoPessoa}"/>
<h:panelGroup rendered="#{MovContaControle.botoesCamposEnabled}" id="nomeCampoPessoaFinanPanel">

    <h:inputText id="pessoa" size="50" maxlength="80" rendered="#{MovContaControle.botoesCamposEnabled}"
                 onkeypress="if (event.keyCode == 13) { document.getElementById('formLanc:descricao').focus();return false;};"
                 onfocus="focusinput(this);" styleClass="form" value="#{MovContaControle.movContaVO.pessoaVO.nome}">
        <a4j:support event="onblur" reRender="modalPanelCadastrarPessoaSimplificada"
                     oncomplete="#{MovContaControle.msgAlert} document.getElementById('formLanc:descricao').focus();"
                     action="#{MovContaControle.cadastrarNovaPessoa}" requestDelay="500"/>
    </h:inputText>

    <h:graphicImage id="imageLoading" style="visibility: hidden;vertical-align: middle;" value="/images/loading.gif"/>

    <rich:suggestionbox height="200" width="650"
                        for="pessoa"
                        rendered="#{MovContaControle.botoesCamposEnabled}"
                        fetchValue="#{result}"
                        suggestionAction="#{MovContaControle.executarAutocompleteConsultaPessoa}"
                        minChars="1" rowClasses="20"
                        status="true"
                        nothingLabel="Nenhuma pessoa encontrada!"
                        var="result" id="suggestionPessoa">
        <a4j:support event="onselect" ignoreDupResponses="true" action="#{MovContaControle.selecionarPessoaSuggestionBox}" focus="descricao"
                     reRender="formLanc, panelCampos, modalPanelCadastrarPessoaSimplificada"/>

        <h:column>
            <f:facet name="header">
                <h:outputText styleClass="textverysmall" value="Nome"/>
            </f:facet>
            <h:outputText value="#{result.nome}"/>
        </h:column>
        <h:column>
            <f:facet name="header">
                <h:outputText styleClass="textverysmall" value="Tipo"/>
            </f:facet>
            <h:outputText value="#{result.tipoPessoa}"/>
        </h:column>
        <h:column>
            <f:facet name="header">
                <h:outputText styleClass="textverysmall" value="CPF/CNPJ"/>
            </f:facet>
            <h:outputText value="#{result.cfp}"/>
        </h:column>
        <h:column>
            <f:facet name="header">
                <h:outputText styleClass="textverysmall" value="Empresa"/>
            </f:facet>
            <h:outputText value="#{result.nomeEmpresa}"/>
        </h:column>
    </rich:suggestionbox>
    <rich:spacer width="15px"/>
</h:panelGroup>

<!-- descricao -->
<h:panelGroup id="descricaoContaFinan" rendered="#{MovContaControle.botoesCamposEnabled || !OperacaoContaControle.apresentarLote}">
    <h:outputText styleClass="tituloCampos" value="*Descrição:"/>
</h:panelGroup>
<h:panelGroup id="descricaoContaFinanInput" rendered="#{MovContaControle.botoesCamposEnabled || !OperacaoContaControle.apresentarLote}">
    <h:inputText id="descricao" size="50" maxlength="100" onblur="blurinput(this);" onfocus="focusinput(this);"
                 styleClass="form" value="#{MovContaControle.movContaVO.descricao}"/>
</h:panelGroup>

<h:panelGroup id="valorContaFinanPanel">
    <h:outputText styleClass="tituloCampos" value="*Valor:"/>
</h:panelGroup>
<h:panelGroup>
    <h:inputText id="valor" style="width: 90px;" maxlength="40" onfocus="focusinput(this);"
                 onkeyup="return moeda(this);"
                 rendered="#{!MovContaControle.valorReadOnly}"
                 styleClass="form" value="#{MovContaControle.movContaVO.valor}">
        <a4j:support event="onblur" action="#{MovContaControle.removerRateiosSugeridos}"
                     rendered="#{MovContaControle.limparRateioSugerido}"
                     reRender="rateioGrid, panelCampos" oncomplete="#{MovContaControle.msgAlert}"
                     ajaxSingle="false"
                     status="false"/>
        <f:converter converterId="FormatadorNumerico"/>
    </h:inputText>

    <h:inputText id="valorRO" style="width: 90px;" maxlength="40" onfocus="focusinput(this);"
                 rendered="#{MovContaControle.valorReadOnly}"
                 readonly="true"
                 styleClass="form" value="#{MovContaControle.movContaVO.valor}"
                 title="Este lançamento não pode ter o valor alterado.">
        <f:converter converterId="FormatadorNumerico"/>
    </h:inputText>

    <script>
        VMasker(document.getElementById("formLanc:valor")).maskMoney({
            precision: 2,
            separator: ',',
            delimiter: '.',
            zeroCents: false
        });
    </script>
</h:panelGroup>

<c:if test="${MovContaControle.botoesCamposEnabled}">
    <h:panelGroup>
        <h:outputText id="dataLancamentoConta" styleClass="tituloCampos" value="*Data de Lançamento:"/>
    </h:panelGroup>

    <!-- data lançamento -->
    <h:panelGroup id="dataLancamentoContaPanel">
        <rich:calendar id="dtLancamento" rendered="#{MovContaControle.botoesCamposEnabled}"
                       value="#{MovContaControle.movContaVO.dataLancamento}"
                       inputSize="8"
                       inputClass="form"
                       oninputchange="return validar_DataFatura(this.id);"
                       oninputblur="blurinput(this);"
                       oninputfocus="focusinput(this);"
                       datePattern="dd/MM/yyyy"
                       enableManualInput="true"
                       showWeeksBar="false"/>
    </h:panelGroup>

    <!-- data vencimento -->
    <h:panelGroup>
        <h:outputText id="dataVencConta" styleClass="tituloCampos" value="*Data de Vencimento:"/>
    </h:panelGroup>
    <h:panelGroup id="dataVencContaPanel">
        <rich:calendar id="dataVencimento" rendered="#{MovContaControle.botoesCamposEnabled}"
                       value="#{MovContaControle.movContaVO.dataVencimento}"
                       inputSize="8"
                       onchanged="atualizarDataCompetenciaJS()"
                       oninputchange="if(validar_DataFatura(this.id)){atualizarDataCompetenciaJS()}"
                       inputClass="form"
                       oninputblur="blurinput(this);"
                       oninputfocus="focusinput(this);"
                       datePattern="dd/MM/yyyy"
                       enableManualInput="true"
                       showWeeksBar="false"/>
        <!-- Função JavaScript para atualização -->
        <a4j:jsFunction name="atualizarDataCompetenciaJS"
                        action="#{MovContaControle.isObrigarPreenchimentoManualDtCompetencia}"
                        reRender="dataCompetencia"
                        oncomplete="if(#{!MovContaControle.obrigarPreenchimentoManualDtCompetencia}) {document.getElementById('formLanc:dataCompetenciaInputDate').value=document.getElementById('formLanc:dataVencimentoInputDate').value;}"/>
        <!-- data vencimento -->
    </h:panelGroup>

    <!-- data competencia -->
    <h:panelGroup>
        <h:outputText id="dataCompConta" styleClass="tituloCampos" value="*Data de Competência:"/>
    </h:panelGroup>
    <h:panelGroup id="dataCompContaPanel">
        <rich:calendar id="dataCompetencia"
                       value="#{MovContaControle.movContaVO.dataCompetencia}"
                       inputSize="8"
                       inputClass="form" rendered="#{MovContaControle.botoesCamposEnabled}"
                       oninputblur="blurinput(this);"
                       oninputfocus="focusinput(this);"
                       oninputchange="return validar_DataFatura(this.id);"
                       datePattern="dd/MM/yyyy"
                       enableManualInput="true"
                       zindex="2"
                       showWeeksBar="false"/>
    </h:panelGroup>

</c:if>
<h:panelGroup>
    <h:outputText id="observacoesConta" rendered="#{MovContaControle.botoesCamposEnabled || MovContaControle.conciliarSaldo}"
                  styleClass="tituloCampos" value="Observações:"/><br/>
</h:panelGroup>
<h:panelGroup id="observacoesContaPanel">
    <h:inputTextarea style="height: 63px; resize: none;"
                     rendered="#{MovContaControle.botoesCamposEnabled || MovContaControle.conciliarSaldo}"
                     id="observacaoRichEditor"
                     value="#{MovContaControle.movContaVO.observacoes}" rows="3" cols="31"/>
</h:panelGroup>

<!-- forma de pagamento -->
<h:panelGroup id="formaPagamentoConta"
        rendered="#{!MovContaControle.existeMaisDeUmMovContaRateio
							&& !MovContaControle.conciliarSaldo
							&& !MovContaControle.devolucaoCheques
							&& (ConfiguracaoFinanceiroControle.confFinanceiro.usarMovimentacaoContas
							    || MovContaControle.botoesCamposEnabled)}">
    <h:outputText styleClass="tituloCampos"
                  value="Forma de Pagamento:"/>
</h:panelGroup>
<h:panelGroup id="formaPagamentoContaCombo"
        rendered="#{!MovContaControle.existeMaisDeUmMovContaRateio
							&& !MovContaControle.conciliarSaldo
							&& !MovContaControle.devolucaoCheques
							&& (ConfiguracaoFinanceiroControle.confFinanceiro.usarMovimentacaoContas
							    || MovContaControle.botoesCamposEnabled)}">
    <h:selectOneMenu
            id="formaPagamento" onblur="blurinput(this);"
            onfocus="focusinput(this);"
            styleClass="form"
            value="#{MovContaControle.movContaRateioVO.formaPagamentoVO.codigo}">
        <f:selectItems value="#{MovContaControle.listaSelectItemFormaPagamento}"/>
        <a4j:support event="onchange" action="#{MovContaControle.setarTipoFormaPgtoEscolhida}" reRender="formLanc"/>
    </h:selectOneMenu>
</h:panelGroup>

<!-- N. codigo barras -->
<h:outputText styleClass="tituloCampos"
              id="cpfOuCnpjBenBoletoFinan"
              rendered="#{MovContaControle.movContaVO.tipoOperacaoLancamento.codigo == 1 and MovContaControle.tipoFormaPagamentoBoletoBancario}"
              value="CPF/CNPJ beneficiário:"/>
<h:inputText id="cpfOuCnpjBenBoletoFinanInput" size="30" maxlength="18" onblur="blurinput(this);" onfocus="focusinput(this);"
             rendered="#{MovContaControle.movContaVO.tipoOperacaoLancamento.codigo == 1 and MovContaControle.tipoFormaPagamentoBoletoBancario}"
             disabled="#{!MovContaControle.movContaVO.pagar_Apresentar}"
             styleClass="form" value="#{MovContaControle.movContaVO.cpfOuCnpjBeneficiario}">
    <a4j:support event="onchange"
                 oncomplete="#{MovContaControle.mensagemNotificar}" reRender="mensagens, formLanc"/>
</h:inputText>

<!-- Conta de consumo-->
<h:outputText styleClass="tituloCampos tooltipster"
              id="consumoContaFinan"
              title="#{MovContaControle.titleContasConsumoExplicacao}"
              rendered="#{MovContaControle.movContaVO.tipoOperacaoLancamento.codigo == 1 and MovContaControle.tipoFormaPagamentoBoletoBancario}"
              value="Conta de consumo:"/>
<h:selectBooleanCheckbox value="#{MovContaControle.movContaVO.contaDeConsumo}"
                         styleClass="tooltipster"
                         title="#{MovContaControle.titleContasConsumoExplicacao}"
                         rendered="#{MovContaControle.movContaVO.tipoOperacaoLancamento.codigo == 1 and MovContaControle.tipoFormaPagamentoBoletoBancario}">
</h:selectBooleanCheckbox>

<!-- N. codigo barras -->
<h:outputText styleClass="tituloCampos"
              id="codBarrasContaFinan"
              rendered="#{MovContaControle.movContaVO.tipoOperacaoLancamento.codigo == 1 and MovContaControle.tipoFormaPagamentoBoletoBancario}"
              value="Código de barras:"/>
<h:inputText id="barCodeContaFinan" size="50" maxlength="100" onblur="blurinput(this);" onfocus="focusinput(this);"
             rendered="#{MovContaControle.movContaVO.tipoOperacaoLancamento.codigo == 1 and MovContaControle.tipoFormaPagamentoBoletoBancario}"
             disabled="#{!MovContaControle.movContaVO.pagar_Apresentar}"
             styleClass="form" value="#{MovContaControle.movContaVO.codigoBarras}">
    <a4j:support event="onchange"
                 oncomplete="#{MovContaControle.mensagemNotificar}" reRender="mensagens, formLanc"/>
</h:inputText>

<!-- Payload Pix -->
<h:outputText styleClass="tituloCampos tooltipster"
              id="payLoadPixContaFinan"
              rendered="#{MovContaControle.movContaVO.tipoOperacaoLancamento.codigo == 1 and MovContaControle.tipoFormaPagamentoPix}"
              title="Payload do pix a ser pago, conhecido também como 'Pix Copia e Cola'"
              value="Payload do Pix:"/>
<h:inputTextarea id="inputPayLoadPixContaFinan" style="width: 360px;height: 80px" onblur="blurinput(this);" onfocus="focusinput(this);"
             rendered="#{MovContaControle.movContaVO.tipoOperacaoLancamento.codigo == 1 and MovContaControle.tipoFormaPagamentoPix}"
             disabled="#{!MovContaControle.movContaVO.pagar_Apresentar}"
             styleClass="form" value="#{MovContaControle.movContaVO.payloadPix}">
    <a4j:support event="onchange"
                 oncomplete="#{MovContaControle.mensagemNotificar}" reRender="mensagens, formLanc"/>
</h:inputTextarea>

<!--CONTA BANCÁRIA EXISTENTE-->
    <h:outputText styleClass="tituloCampos tooltipster"
                  rendered="#{MovContaControle.movContaVO.tipoOperacaoLancamento.codigo == 1 and MovContaControle.tipoFormaPagamentoTransferencia
                        and MovContaControle.movContaVO.fornecedorPossuiDadosBancarios}"
                  id="dadosContaFornecedorExistente"
                  title="Informações da conta bancária do fornecedor selecionado, utilizadas para pagamento em lote"
                  value="Dados Bancários:"/>

<h:panelGroup rendered="#{MovContaControle.movContaVO.tipoOperacaoLancamento.codigo == 1 and MovContaControle.tipoFormaPagamentoTransferencia
                        and MovContaControle.movContaVO.fornecedorPossuiDadosBancarios}" id="panelInfoContaForn">
    <table cellpadding="0" cellspacing="0">
        <tr valign="top">
            <td>
                <h:outputText styleClass="tituloCampos tooltipster"
                              title="#{MovContaControle.titleContaExistenteFornecedorExibir}"
                              rendered="#{MovContaControle.movContaVO.tipoOperacaoLancamento.codigo == 1 and MovContaControle.tipoFormaPagamentoTransferencia
                              and MovContaControle.movContaVO.fornecedorPossuiDadosBancarios}"
                              value="#{MovContaControle.infoContaExistenteFornecedorExibir}"/>
            </td>
            <td>
                <a4j:commandLink id="btnEditarContaForn"
                                 style="text-decoration: none"
                                 action="#{MovContaControle.prepararAbrirModalContaBancariaFornecedor}"
                                 rendered="#{MovContaControle.movContaVO.tipoOperacaoLancamento.codigo == 1 and MovContaControle.tipoFormaPagamentoTransferencia
                                 and MovContaControle.movContaVO.fornecedorPossuiDadosBancarios}"
                                 styleClass="linkAzul texto-size-14"
                                 reRender="modalContaBancariaFornecedor"
                                 oncomplete="#{MovContaControle.abrirFecharModalDadosContaBancariaFornecedor}#{MovContaControle.mensagemNotificar}">
                    <i class="fa-icon-edit tooltipster" title="Editar Conta Bancária" style="margin-left: 5px;"></i>
                </a4j:commandLink>
            </td>
            <td>
                <a4j:commandLink id="btnExcluirContaForn"
                                 style="text-decoration: none"
                                 action="#{MovContaControle.prepararAbrirModalExcluirContaFornecedor}"
                                 rendered="#{MovContaControle.movContaVO.tipoOperacaoLancamento.codigo == 1 and MovContaControle.tipoFormaPagamentoTransferencia
                                 and MovContaControle.movContaVO.fornecedorPossuiDadosBancarios}"
                                 styleClass="linkAzul texto-size-14"
                                 reRender="formLanc:modalExcluirContaFornecedor"
                                 oncomplete="#{MovContaControle.mensagemNotificar}#{MovContaControle.abrirFecharModalExcluirContaFornecedor}">
                    <i class="fa-icon-trash tooltipster" title="Excluir Conta Bancária" style="margin-left: 5px;"></i>
                </a4j:commandLink>
            </td>
        </tr>
    </table>
</h:panelGroup>

<!--CONTA BANCÁRIA NÃO EXISTENTE-->
<h:outputText styleClass="tituloCampos tooltipster"
              id="informarNovaContaFornecedor"
              rendered="#{MovContaControle.movContaVO.tipoOperacaoLancamento.codigo == 1 and MovContaControle.tipoFormaPagamentoTransferencia
                        and !MovContaControle.movContaVO.fornecedorPossuiDadosBancarios}"
              title="Informações da conta bancária do fornecedor selecionado, utilizadas para pagamento em lote"
              value="Dados Bancários:"/>
<a4j:commandLink styleClass="tituloCampos tooltipster"
                 style="color: #4d90fe; text-decoration: none;"
                 rendered="#{MovContaControle.movContaVO.tipoOperacaoLancamento.codigo == 1 and MovContaControle.tipoFormaPagamentoTransferencia
                        and !MovContaControle.movContaVO.fornecedorPossuiDadosBancarios}"
                 id="btnInformarNovaContaFornecedor"
                 reRender="modalContaBancariaFornecedor, formContaBancariaFornecedor"
                 value="Informar Conta Bancária"
                 action="#{MovContaControle.prepararAbrirModalContaBancariaFornecedor}"
                 oncomplete="#{MovContaControle.abrirFecharModalDadosContaBancariaFornecedor}#{MovContaControle.mensagemNotificar}"/>
<h:panelGroup>
    <h:outputText id="anexarContaFinan" styleClass="tituloCampos tooltipster" title="O tamanho do arquivo deve ser menor ou igual a 512KB"
                  value="Anexar conta: "></h:outputText>
</h:panelGroup>

<h:panelGroup>
    <rich:fileUpload id="uploadConta"
                     listHeight="50"
                     listWidth="350"
                     noDuplicate="false"
                     fileUploadListener="#{MovContaControle.uploadConta}"
                     maxFilesQuantity="1"
                     allowFlash="false"
                     immediateUpload="false"
                     acceptedTypes="jpg, jpeg, gif, png, bmp, pdf, JPG, JPEG, GIF, PNG, BMP, PDF"
                     addControlLabel="Adicionar"
                     cancelEntryControlLabel="Cancelar"
                     doneLabel="Pronto"
                     sizeErrorLabel="Arquivo não Enviado. Excedeu o tamanho permitido. 512KB"
                     progressLabel="Enviando"
                     clearControlLabel="Limpar"
                     clearAllControlLabel="Limpar todos"
                     stopControlLabel="Parar"
                     uploadControlLabel="Enviar"
                     transferErrorLabel="Falha de Transmissão"
                     stopEntryControlLabel="Parar">
        <a4j:support event="onadd" reRender="panelMensagem"/>
        <a4j:support event="onerror" oncomplete="#{MovContaControle.mensagemNotificar}" reRender="uploadConta, panelMensagem"/>
        <a4j:support event="onupload" reRender="panelMensagem"/>
        <a4j:support event="onuploadcomplete" reRender="panelMensagem"/>
        <a4j:support event="onclear" action="#{MovContaControle.limparArquivoConta}" oncomplete="#{MovContaControle.mensagemNotificar}" reRender="uploadConta, panelMensagem"/>
    </rich:fileUpload>
</h:panelGroup>
<h:panelGroup rendered="#{MovContaControle.existeArquivoConta}">
    <h:outputText value="Anexo conta: "
                  id="anexarContaFinanText"
                  title="O tamanho do arquivo deve ser menor ou igual a 512KB"
                  rendered="#{MovContaControle.existeArquivoConta}"
                  styleClass="tooltipster tituloCampos"/>
</h:panelGroup>

<h:panelGroup rendered="#{MovContaControle.existeArquivoConta}" id="existeArquivoContaFinan">
    <h:commandLink id="arqMovConta" style="margin-left: 12px"
                   actionListener="#{MovContaControle.downloadContaListener}"
                   value="DOWNLOAD"/>
    <h:commandLink id="excluirArqConta" style="margin-left: 12px"
                   actionListener="#{MovContaControle.excluirArqContaListener}"
                   value="EXCLUIR"/>
</h:panelGroup>

<h:panelGroup>
    <h:outputText styleClass="tituloCampos tooltipster" title="O tamanho do arquivo deve ser menor ou igual a 512KB"
                  id="anexarComprovanteFinan"
                  value="Anexar comprovante: "></h:outputText>
</h:panelGroup>

<h:panelGroup>
    <rich:fileUpload id="uploadComprovante"
                     listHeight="50"
                     listWidth="350"
                     noDuplicate="false"
                     fileUploadListener="#{MovContaControle.uploadComprovante}"
                     maxFilesQuantity="1"
                     allowFlash="false"
                     immediateUpload="false"
                     acceptedTypes="jpg, jpeg, gif, png, bmp, pdf, JPG, JPEG, GIF, PNG, BMP, PDF"
                     addControlLabel="Adicionar"
                     cancelEntryControlLabel="Cancelar"
                     doneLabel="Pronto"
                     sizeErrorLabel="Arquivo não Enviado. Excedeu o tamanho permitido. 512KB"
                     progressLabel="Enviando"
                     clearControlLabel="Limpar"
                     clearAllControlLabel="Limpar todos"
                     stopControlLabel="Parar"
                     uploadControlLabel="Enviar"
                     transferErrorLabel="Falha de Transmissão"
                     stopEntryControlLabel="Parar">
        <a4j:support event="onadd" reRender="panelMensagem"/>
        <a4j:support event="onerror" oncomplete="#{MovContaControle.mensagemNotificar}" reRender="uploadComprovante, panelMensagem"/>
        <a4j:support event="onupload" reRender="panelMensagem"/>
        <a4j:support event="onuploadcomplete" reRender="panelMensagem"/>
        <a4j:support event="onclear" action="#{MovContaControle.limparArquivoComprovante}" oncomplete="#{MovContaControle.mensagemNotificar}" reRender="uploadComprovante, panelMensagem"/>
    </rich:fileUpload>
</h:panelGroup>

<h:panelGroup rendered="#{MovContaControle.existeArquivoComprovante}" id="existeArquivoComprovanteFinan">
    <h:outputText value="Anexo comprovante: "
                  title="O tamanho do arquivo deve ser menor ou igual a 512KB"
                  styleClass="tooltipster tituloCampos"/>
</h:panelGroup>
<h:panelGroup rendered="#{MovContaControle.existeArquivoComprovante}">
    <h:commandLink id="arqMovComprovante" style="margin-left: 12px"
                   actionListener="#{MovContaControle.downloadComprovanteListener}"
                   value="DOWNLOAD"/>
    <h:commandLink id="excluirArqMovComprovante" style="margin-left: 12px"
                   actionListener="#{MovContaControle.excluirComprovanteListener}"
                   value="EXCLUIR"/>
</h:panelGroup>

<h:panelGroup rendered="#{MovContaControle.existeAnexoCompraEstoque}" id="existeArquivoCompraContaFinanLabel">
    <h:outputText value="Anexo(s) da compra: "
                  styleClass="tooltipster tituloCampos"/>
</h:panelGroup>
<h:panelGroup rendered="#{MovContaControle.existeAnexoCompraEstoque}"
              id="existeArquivoCompraContaFinan">
    <h:commandLink id="arqCompraContaFinan" style="margin-left: 12px"
                   actionListener="#{MovContaControle.downloadDocumentoCompraListener}"
                   value="DOWNLOAD"/>
</h:panelGroup>

<%--Temporario, até todos anexar arquivo atraves do novo modelo--%>
<h:panelGroup rendered="#{fn:length(MovContaControle.anexos) ge 1}">
    <h:outputText styleClass="tituloCampos" id="imagenAnexo" value="Demais Anexos:"  rendered="#{fn:length(MovContaControle.anexos) ge 1}"/>
</h:panelGroup>
<h:panelGroup  id="imagem" rendered="#{fn:length(MovContaControle.anexos) ge 1}">
    <c:forEach varStatus="stts" items="#{MovContaControle.anexos}" var="an">
        <c:if test="${not stts.first}">
            /
        </c:if>
        <a target="_blank" href="${an.urlFoto}" style="font-size: 14px;">
            Imagem ${stts.index+1}
        </a>

    </c:forEach>
</h:panelGroup>
<%--fim temporario--%>

<!-- data quitacao -->
<h:panelGroup rendered="#{!MovContaControle.movContaVO.pagar_Apresentar && MovContaControle.botoesCamposEnabled}">
    <h:outputText id="dtQuitacaoContaFinan" styleClass="tituloCampos" value="Data de Quitação:"/>
</h:panelGroup>
<h:panelGroup id="quitacao" rendered="#{!MovContaControle.movContaVO.pagar_Apresentar && MovContaControle.botoesCamposEnabled}">
    <h:inputText id="dataQuitacaoContaPagarReceber"
                 style="width: 130px;"
                 styleClass="form" value="#{MovContaControle.movContaVO.dataQuitacao_Apresentar}"
                 rendered="#{!MovContaControle.movContaVO.pagar_Apresentar}"
                 readonly="true"/>

    <rich:spacer width="10px;"/>
    <a4j:commandLink rendered="#{!MovContaControle.movContaVO.pagar_Apresentar
    								&& MovContaControle.botoesCamposEnabled
    								&& not empty MovContaControle.movContaVO.conjuntoPagamento}"
                     value="Lançamentos pagos em conjunto "
                     reRender="modalVerPagamentoConjunto"
                     oncomplete="#{MovContaControle.msgAlert}"
                     action="#{MovContaControle.verConjuntoPagamentos}">
    </a4j:commandLink>
    <h:outputText rendered="#{!MovContaControle.movContaVO.pagar_Apresentar
    								&& MovContaControle.botoesCamposEnabled
    								&& not empty MovContaControle.movContaVO.conjuntoPagamento}" value="/ "/>


    <h:outputText rendered="#{!MovContaControle.movContaVO.pagar_Apresentar && MovContaControle.botoesCamposEnabled &&
                                    (MovContaControle.moduloOpenBankAtivado and (not empty MovContaControle.movContaVO.codigoBarras) and
                                     !fn:contains(MovContaControle.movContaVO.statusRetornoStoneOpenBanking, 'Pagamento Recusado'))}"
                  styleClass="texto-upper texto-cor-verde texto-size-16"
                  value="#{MovContaControle.movContaVO.statusRetornoStoneOpenBanking}"/>

    <h:outputText rendered="#{!MovContaControle.movContaVO.pagar_Apresentar && MovContaControle.botoesCamposEnabled &&
                                    (MovContaControle.moduloOpenBankAtivado and (not empty MovContaControle.movContaVO.codigoBarras) and
                                     fn:contains(MovContaControle.movContaVO.statusRetornoStoneOpenBanking, 'Pagamento Recusado'))}"
                  styleClass="texto-upper texto-cor-vermelho texto-size-16"
                  value="#{MovContaControle.movContaVO.statusRetornoStoneOpenBanking}"/>

    <a4j:commandLink rendered="#{!MovContaControle.movContaVO.pagar_Apresentar && MovContaControle.botoesCamposEnabled &&
                                    !(MovContaControle.moduloOpenBankAtivado and (not empty MovContaControle.movContaVO.codigoBarras) and
                                     !(fn:contains(MovContaControle.movContaVO.statusRetornoStoneOpenBanking, 'Pagamento Recusado') or
                                     fn:contains(MovContaControle.movContaVO.statusRetornoStoneOpenBanking, 'Pagamento Agendado') or
                                     fn:contains(MovContaControle.movContaVO.statusRetornoStoneOpenBanking, ' - ')))}"
                     value=" - #{MovContaControle.nomeEstornar}"
                     status="false"
                     reRender="formLanc"
                     action="#{MovContaControle.verificarPermissaoEstorno}"
                     oncomplete="#{MovContaControle.onCompleteEstornarPagamento}">
    </a4j:commandLink>

</h:panelGroup>

<!-- tipo de documento -->
<h:outputText  id="tipoDocContaFinan" rendered="#{!MovContaControle.existeMaisDeUmMovContaRateio && MovContaControle.botoesCamposEnabled && !MovContaControle.devolucaoCheques}" styleClass="tituloCampos"
               value="Tipo de Documento:"/>
<h:selectOneMenu rendered="#{!MovContaControle.existeMaisDeUmMovContaRateio && MovContaControle.botoesCamposEnabled && !MovContaControle.devolucaoCheques}"
                 style="width: 130px;"
                 id="tipoDocumento" onblur="blurinput(this);"
                 onfocus="focusinput(this);"
                 styleClass="form"
                 value="#{MovContaControle.movContaRateioVO.tipoDocumentoVO.codigo}">
    <f:selectItems value="#{MovContaControle.listaSelectItemTipoDocumento}"/>
    <a4j:support event="onchange" action="#{MovContaControle.acaoNumeroDocumento}" reRender="formLanc"/>
</h:selectOneMenu>
<%--numero Documento--%>
<h:outputText styleClass="tituloCampos"
              id="numeroDocContaFinan"
              rendered="#{MovContaControle.exibirNumeroDocumeto or (MovContaControle.movContaVO.numeroDocumento != null and (not empty MovContaControle.movContaVO.numeroDocumento))}"
              value="N° Documento:"/>
<h:inputText id="numeroDocumento" size="18" maxlength="100" onblur="blurinput(this);" onfocus="focusinput(this);"
             rendered="#{MovContaControle.exibirNumeroDocumeto or (MovContaControle.movContaVO.numeroDocumento != null and (not empty MovContaControle.movContaVO.numeroDocumento))}"
             styleClass="form" value="#{MovContaControle.movContaVO.numeroDocumento}">
</h:inputText>

<!-- plano de contas -->
<h:outputText rendered="#{!MovContaControle.existeMaisDeUmMovContaRateio && MovContaControle.botoesCamposEnabled}" styleClass="tituloCampos"
              id="planoContasContaFinan"
              value="Plano de Contas:"/>
<h:panelGroup rendered="#{!MovContaControle.existeMaisDeUmMovContaRateio && MovContaControle.botoesCamposEnabled}" id="planoContasContaFinanPanel">
    <table cellpadding="0" cellspacing="0">
        <tr valign="top">
            <td>
                <%@include file="include_SuggestionPlanoConta.jsp" %>
            </td>
            <td> &nbsp;<a4j:commandLink action="#{PlanoContasControle.verificarConsultaLancamento}"
                                        reRender="modalPlanos"
                                        id="btAddPlano" value="Consultar"
                                        oncomplete="Richfaces.showModalPanel('modalPlanos')"/></td>
        </tr>
    </table>
</h:panelGroup>

<!-- centro de custos -->

<h:outputText rendered="#{!MovContaControle.existeMaisDeUmMovContaRateio && MovContaControle.botoesCamposEnabled}" styleClass="tituloCampos"
              id="centroCustoContaFinan"
              value="Centro de Custo:"/>
<h:panelGroup rendered="#{!MovContaControle.existeMaisDeUmMovContaRateio && MovContaControle.botoesCamposEnabled}" id="centroCustoContaFinanPanel">
    <table cellpadding="0" cellspacing="0">
        <tr valign="top">
            <td>
                <%@include file="include_SuggestionCentroCusto.jsp" %>
            </td>
            <td> &nbsp;<a4j:commandLink action="#{CentroCustosControle.loadTree}"
                                        reRender="modalCentros"
                                        id="btAddCentro" value="Consultar"
                                        oncomplete="Richfaces.showModalPanel('modalCentros')"/></td>
        </tr>
    </table>
</h:panelGroup>

<c:if test="${MovContaControle.botoesCamposEnabled}">
    <%@include file="include_SuggestionContaContabilCreditoValor.jsp" %>
    <%@include file="include_SuggestionContaContabilDebitoValor.jsp" %>

    <h:outputText rendered="#{MovContaControle.integracaoContabilAlterData}"
                  value="Histórico contábil:"
                  style="vertical-align: middle"
                  styleClass="tituloCampos"></h:outputText>
    <h:selectOneMenu  value="#{MovContaControle.movContaContabilVO.historicoContabil}"
                      rendered="#{MovContaControle.integracaoContabilAlterData}"
                      onblur="blurinput(this);"
                      onfocus="focusinput(this);"
                      style="color:black;"
                      styleClass="form">
        <f:selectItems value="#{MovContaControle.listaHistoricoContabil}"/>
    </h:selectOneMenu>

    <h:outputText rendered="#{MovContaControle.integracaoContabilAlterData}"
                  value="Complemento histórico contábil:"
                  style="vertical-align: middle"
                  styleClass="tituloCampos"></h:outputText>
    <h:inputText size="50" maxlength="100" onblur="blurinput(this);" onfocus="focusinput(this);"
                 rendered="#{MovContaControle.integracaoContabilAlterData}"
                 styleClass="form" value="#{MovContaControle.movContaContabilVO.complementoHistoricoContabil}"/>
</c:if>

<h:outputText id="contaLancConta"
        rendered="#{MovContaControle.usarMovimentacao}"
        styleClass="tituloCampos" value="Conta:"/>
<h:panelGroup rendered="#{MovContaControle.usarMovimentacao}" id="contaLancContaPanel">
    <h:selectOneMenu  value="#{MovContaControle.movContaVO.contaVO.codigo}"
                      onblur="blurinput(this);"
                      onfocus="focusinput(this);"
                      style="color:black;"
                      id="contaSelectitem"
                      styleClass="form">
        <f:selectItems value="#{MovContaControle.listaComboConta}"/>
        <a4j:support action="#{MovContaControle.selecionarContaCustodia(true)}" reRender="formDeposito" event="onchange"/>
    </h:selectOneMenu>
</h:panelGroup>
<h:outputText styleClass="tituloCampos" rendered="#{MovContaControle.apresentarValorLiquido}" value="Taxa do Cartão:"/>
<h:panelGroup rendered="#{MovContaControle.apresentarValorLiquido}">
    <h:inputText id="taxaCartao" disabled="true" style="width: 90px;" maxlength="40" onfocus="focusinput(this);"
                 onkeypress="return(currencyFormat(this,'.',',',event));"
                 styleClass="form" value="#{MovContaControle.formapagamentoVO.taxaCartao}">
        <f:converter converterId="FormatadorNumerico"/>
    </h:inputText>
    <h:outputText styleClass="tituloCampos" value="%"/>
</h:panelGroup>
<h:outputText styleClass="tituloCampos" rendered="#{MovContaControle.apresentarValorLiquido}" value="Taxa de Antecipação:"/>
<h:panelGroup rendered="#{MovContaControle.apresentarValorLiquido}">
    <h:inputText id="taxaAntecipacao" disabled="true" style="width: 90px;" maxlength="40" onfocus="focusinput(this);"
                 onkeypress="return(currencyFormat(this,'.',',',event));"
                 styleClass="form" value="#{MovContaControle.taxaAntecipacaoCartao}">
        <f:converter converterId="FormatadorNumerico"/>
    </h:inputText>
    <h:outputText styleClass="tituloCampos" value="%"/>
</h:panelGroup>
<h:outputText styleClass="tituloCampos" rendered="#{MovContaControle.apresentarValorLiquido}"
              value="#{msg_aplic.prt_Finan_Lancamento_valorLiquido}"/>
<h:panelGroup rendered="#{MovContaControle.apresentarValorLiquido}">
    <h:inputText id="valorLiquido" style="width: 90px;" maxlength="40" onfocus="focusinput(this);"
                 onkeypress="return(currencyFormat(this,'.',',',event));"
                 styleClass="form" value="#{MovContaControle.movContaVO.valorLiquido}">
        <f:converter converterId="FormatadorNumerico"/>
    </h:inputText>
</h:panelGroup>

<h:outputText
        rendered="#{MovContaControle.movContaVO.apresentarRecebido && ConfiguracaoFinanceiroControle.confFinanceiro.usarMovimentacaoContas}"
        styleClass="tituloCampos" value="Recebido do ADM:"/>
<h:selectBooleanCheckbox value="#{MovContaControle.movContaVO.apresentarNoCaixa}"
                         rendered="#{MovContaControle.movContaVO.apresentarRecebido
                                    && ConfiguracaoFinanceiroControle.confFinanceiro.usarMovimentacaoContas}">
</h:selectBooleanCheckbox>

<h:outputText
        rendered="#{MovContaControle.loteUsado != null && MovContaControle.loteUsado.codigo > 0 && ConfiguracaoFinanceiroControle.confFinanceiro.usarMovimentacaoContas}"
        styleClass="tituloCampos" value="#{msg_aplic.prt_Finan_Lancamentos_loteusadoparapagar}"/>
<h:commandLink action="#{GestaoLotesControle.abrirEdicaoLote}"
               actionListener="#{GestaoLotesControle.listenerEdicaoLote}"
               rendered="#{MovContaControle.loteUsado != null && MovContaControle.loteUsado.codigo > 0 && ConfiguracaoFinanceiroControle.confFinanceiro.usarMovimentacaoContas}">
    <h:outputText styleClass="tituloCampos" style="text-decoration: underline;"
                  value="#{MovContaControle.loteUsado.descLoteUsadoParaPagar_apresentar}"/>
    <f:attribute name="codigoLote" value="#{MovContaControle.loteUsado.codigo}"/>
    <f:attribute name="origem" value="lancamentosCadastro"/>
</h:commandLink>

<h:outputText
        rendered="#{MovContaControle.movContaVO.lote != null && MovContaControle.movContaVO.lote.codigo > 0 && ConfiguracaoFinanceiroControle.confFinanceiro.usarMovimentacaoContas}"
        styleClass="tituloCampos" value="Lote:"/>

<h:commandLink action="#{GestaoLotesControle.abrirEdicaoLote}"
               actionListener="#{GestaoLotesControle.listenerEdicaoLote}"
               rendered="#{MovContaControle.movContaVO.lote != null && MovContaControle.movContaVO.lote.codigo > 0 && ConfiguracaoFinanceiroControle.confFinanceiro.usarMovimentacaoContas}">
    <h:outputText styleClass="tituloCampos" style="text-decoration: underline;"
                  value="#{MovContaControle.movContaVO.lote.codigo}"/>
    <f:attribute name="codigoLote" value="#{MovContaControle.movContaVO.lote.codigo}"/>
    <f:attribute name="origem" value="lancamentosCadastro"/>
</h:commandLink>

<h:outputText rendered="#{MovContaControle.movContaVO.caixa != null && MovContaControle.movContaVO.caixa > 0}"
              styleClass="tituloCampos" value="Pertence ao caixa:"/>
<h:commandLink action="#{MovContaControle.visualizarCaixa}"
               rendered="#{MovContaControle.movContaVO.caixa != null && MovContaControle.movContaVO.caixa > 0}">
    <h:outputText styleClass="tituloCampos" style="text-decoration: underline;"
                  value="#{MovContaControle.movContaVO.caixa}"/>
</h:commandLink>
