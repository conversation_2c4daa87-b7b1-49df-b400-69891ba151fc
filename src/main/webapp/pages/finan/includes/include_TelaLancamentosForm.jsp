<%@page contentType="text/html; charset=ISO-8859-1" pageEncoding="ISO-8859-1" %>
<script type="text/javascript" language="javascript" src="../../../bootstrap/jquery.js"></script>
<script type="text/javascript" src="../../../script/tooltipster/jquery.tooltipster.min.js"></script>
<%--
    Document   : include_lancamentos
    Created on : 22/08/2011, 17:51:16
    Author     : carla
--%>
<style type="text/css">
    .tituloBold {
        font-family: Arial, Helvetica, sans-serif;
        font-size: 12px;
        text-decoration: none;
        line-height: normal;
        text-transform: none;
        font-weight: bold;
        color: #000000;
    }

    .tituloDemonstrativo {
        font-family:"Trebuchet MS", Verdana, Arial, Helvetica, sans-serif;
        font-size: 13px;
        text-decoration: none;
        line-height: normal;
        text-transform: none;
        color: #000000;
    }

    .pseudo-disabled {
        cursor: not-allowed;          /* Cursor de proibido */
        opacity: 0.6;                 /* Aparência mais clara */
        pointer-events: none;         /* Impede interações */
    }
</style>
<!-- Inicio Tela de Lançamentos-->

<h:panelGrid id="tabelaGeral" width="100%" columns="3"
             columnClasses="direitaLancamento,centroLancamento,esquerdaLancamento">

<h:panelGroup style="width :100%; vertical-align: top;">
<c:if test="${MovContaControle.botoesCamposEnabled}">
<table width="100%">
    <tr>
        <td align="left">
            <img src="${root}/images/arrow2.gif" width="16" height="16"
                 style="vertical-align: middle; margin-right: 6px;">
            <h:outputText styleClass="tituloBold" value="#{msg_aplic.prt_Finan_Lancamentos_dados}"/>
        </td>
        <td align="right">
            <h:outputText styleClass="fa-icon-mobile-phone" rendered="#{MovContaControle.movContaVO.app}"
            style="font-size: 16px;"></h:outputText>
            <h:outputText rendered="#{MovContaControle.agendamento}" styleClass="tituloDemonstrativo"
                          value="#{msg_aplic.prt_Finan_Lancamentos_parcela}: #{MovContaControle.movContaVO.nrParcela}"/>
        </td>
    </tr>
</table>

<div class="sep" style="margin:4px 0 5px 0;"><img src="${root}/images/shim.gif"></div>
</c:if>
<h:panelGrid id="panelCampos" columns="2" rowClasses="linhaImpar, linhaPar" columnClasses="alinhamentoTop, classDireita"
             width="100%">

<%@include file="/pages/finan/includes/include_dadosLancamento.jsp" %>
<h:outputText styleClass="tituloCampos" value="Cheque: " rendered="#{not empty MovContaControle.movContaVO.cheques}"></h:outputText>
		<h:panelGrid id="chequeslista" width="600px" rendered="#{not empty MovContaControle.movContaVO.cheques}"
		             columnClasses="centralizado, centralizado, centralizado, centralizado, centralizado, direita"
		             columns="6">
				 <h:outputText value="Banco" styleClass="textsmall" />
		 		 <h:outputText value="Agência" styleClass="textsmall" />
		 		 <h:outputText value="Conta" styleClass="textsmall" />
		 		 <h:outputText value="Número" styleClass="textsmall" />
		 		 <h:outputText value="Compensação" styleClass="textsmall" />
		 		 <h:outputText value="Valor" styleClass="textsmall" />

		 		<h:outputText styleClass="tituloCampos" value="#{MovContaControle.chequeUsado.banco.nome}"></h:outputText>
		 		<h:outputText styleClass="tituloCampos" value="#{MovContaControle.chequeUsado.agencia}"></h:outputText>
		 		<h:outputText styleClass="tituloCampos" value="#{MovContaControle.chequeUsado.conta}"></h:outputText>
		 		<h:outputText styleClass="tituloCampos" value="#{MovContaControle.chequeUsado.numero}"></h:outputText>
		 		<h:outputText styleClass="tituloCampos" value="#{MovContaControle.chequeUsado.dataCompensacao_Apresentar}"></h:outputText>
		 		<h:outputText styleClass="tituloCampos" value="#{MovContaControle.chequeUsado.valor}"><f:converter converterId="FormatadorNumerico" /></h:outputText>
		</h:panelGrid>

</h:panelGrid>

<h:panelGrid width="100%" id="panelAgendRat" columnClasses="colunaCentralizada" style="padding-bottom:20px;padding-top:20px;" rendered="#{MovContaControle.botoesCamposEnabled}">
    <h:panelGroup>
        <a4j:commandButton id="agendarPagamento" rendered="#{MovContaControle.contasPagar && MovContaControle.botoesCamposEnabled}"
                           action="#{MovContaControle.prepararAgendamento}"
                           value="#{MovContaControle.movContaVO.agendamentoFinanceiro gt 0 ? 'Alterar agendamento' : 'Agendar pagamento'}"
                           oncomplete="#{MovContaControle.abrirAgendamento}"
                           styleClass="botoes nvoBt btSec" reRender="agendamentoFinanceiroPanel, panelMensagem,rateioGrid"/>
        <a4j:commandButton id="agendarRecebimento" rendered="#{MovContaControle.contasReceber && MovContaControle.botoesCamposEnabled}"
                           value="#{MovContaControle.movContaVO.agendamentoFinanceiro gt 0 ? 'Alterar agendamento' : 'Agendar recebimento'}"
                           action="#{MovContaControle.prepararAgendamento}"
                           oncomplete="#{MovContaControle.abrirAgendamento}"
                           styleClass="botoes nvoBt btSec" reRender="agendamentoFinanceiroPanel, panelMensagem,rateioGrid"/>
        <rich:spacer width="10"/>
        <a4j:commandButton id="ratearValor" action="#{MovContaControle.ratearValor}"
        				   rendered="#{MovContaControle.botoesCamposEnabled}"
                           value="Ratear valor"
                           styleClass="botoes nvoBt btSec"
                           reRender="tabelaGeral,rateioGrid">
        </a4j:commandButton>
    </h:panelGroup>
</h:panelGrid>

</h:panelGroup>
<h:panelGroup rendered="#{MovContaControle.agendamento}" style="width: 100%;">
    <rich:spacer width="35px;"/>
</h:panelGroup>

<h:panelGroup rendered="#{MovContaControle.agendamento}" style="width: 35%; vertical-align: top;">
    <!-- -------------------------- INICIO - PARCELAS DE AGENDAMENTO ------------------------------------------- -->

    <rich:panel id="panelAgendamentos" style="background-color: #EEEEEE;">
        <table width="100%">
            <tr>
                <td align="left">
                    <img src="${root}/images/arrow2.gif" width="16" height="16"
                         style="vertical-align: middle; margin-right: 6px;">
                    <h:outputText styleClass="tituloBold" value="#{msg_aplic.prt_Finan_Lancamentos_parcelas}"/>
                </td>
                <td align="right">
                    <h:outputText styleClass="tituloDemonstrativo"
                                  value="#{msg_aplic.prt_Finan_Lancamentos_totalRegistros} #{MovContaControle.nrParcelas}"/><br/>
                    <h:outputText styleClass="tituloDemonstrativo" rendered="#{MovContaControle.agendamentoVO.dataLancamento != null}"
                                  value=" Lançamentos do agendamento: #{MovContaControle.agendamentoVO.dataLancamento_Apresentar}"/>
                </td>
            </tr>
        </table>
        <div class="sep" style="margin:4px 0 5px 0;"><img src="${root}/images/shim.gif"></div>

        <!-- -------------------------- FILTROS DA CONSULTA DE PARCELAS ------------------------------------------- -->
        <table width="100%">
            <tr>
                <td valign="top" align="right" width="90%">
                    <h:outputText styleClass="tituloDemonstrativo"
                                  value="#{msg_aplic.prt_Finan_Lancamentos_vencimento}:"/>
                    <rich:spacer width="3"/>
                    <rich:calendar id="dataInicioLancamento"
                                   value="#{MovContaControle.dataInicioParcelas}"
                                   inputSize="6"
                                   inputClass="form"
                                   oninputblur="blurinput(this);"
                                   oninputfocus="focusinput(this);"
                                   oninputchange="return validar_Data(this.id);"
                                   datePattern="dd/MM/yyyy"
                                   enableManualInput="true"
                                   zindex="2"
                                   showWeeksBar="false"/>
                    <rich:spacer width="3"/>
                    <h:outputText styleClass="tituloDemonstrativo" value="#{msg_aplic.prt_Finan_Lancamentos_a}"/>
                    <rich:spacer width="3"/>
                    <rich:calendar id="dataTerminoLancamento"
                                   value="#{MovContaControle.dataFimParcelas}"
                                   inputSize="6"
                                   inputClass="form"
                                   oninputblur="blurinput(this);"
                                   oninputfocus="focusinput(this);"
                                   oninputchange="return validar_Data(this.id);"
                                   datePattern="dd/MM/yyyy"
                                   enableManualInput="true"
                                   zindex="2"
                                   showWeeksBar="false"/>
                </td>
                <td valign="top" align="right">
                    <a4j:commandButton image="../../../imagens/btn_Pesquisar.png"
                                       action="#{MovContaControle.consultaParcelas}"
                                       reRender="panelAgendamentos"></a4j:commandButton>
                </td>
            </tr>
        </table>


        <rich:dataTable id="items" width="100%" headerClass="consulta" rowClasses="linhaPar"
                        reRender="paginaAtual, paginaAtualTop,painelPaginacaoTop,painelPaginacao"
                        columnClasses="centralizado, centralizado, centralizado, centralizado, direita, centralizado"
                        value="#{MovContaControle.parcelasRelacionadas}" rows="5" var="movConta">

            <!-- parcela -->
            <rich:column style="#{movConta.styleLinhaSelecionada}" sortBy="#{movConta.nrParcela}" filterEvent="onkeyup">
                <f:facet name="header">
                    <h:outputText styleClass="topoPequeno" value="#{msg_aplic.prt_Finan_Lancamentos_parcela}"/>
                </f:facet>
                <h:panelGroup>
                    <a4j:commandLink action="#{MovContaControle.preparaEdicaoSemMudarPagina}" id="nrParcela"
                                     reRender="formLanc, form">
                        <h:outputText value="#{movConta.nrParcela}"/>
                    </a4j:commandLink>
                </h:panelGroup>
            </rich:column>

            <!-- descricao -->
            <rich:column style="#{movConta.styleLinhaSelecionada}" sortBy="#{movConta.descricao}" filterEvent="onkeyup">
                <f:facet name="header">
                    <h:outputText styleClass="topoPequeno" value="#{msg_aplic.prt_Finan_Lancamentos_descricao}"/>
                </f:facet>
                <h:panelGroup>
                    <a4j:commandLink action="#{MovContaControle.preparaEdicaoSemMudarPagina}" id="descricao"
                                     reRender="formLanc, form">
                        <h:outputText value="#{movConta.descricao}"/>
                    </a4j:commandLink>
                </h:panelGroup>
            </rich:column>
            <!-- vencimento -->
            <rich:column style="#{movConta.styleLinhaSelecionada}" sortBy="#{movConta.dataVencimento}"
                         filterEvent="onkeyup">
                <f:facet name="header">
                    <h:outputText styleClass="topoPequeno" value="#{msg_aplic.prt_Finan_Lancamentos_vencimento}"/>
                </f:facet>
                <a4j:commandLink reRender="formLanc, form" action="#{MovContaControle.preparaEdicaoSemMudarPagina}"
                                 id="dataVencimento">
                    <h:outputText value="#{movConta.dataVencimento_Apresentar}">
                    </h:outputText>
                </a4j:commandLink>
            </rich:column>

            <!-- quitacao -->
            <rich:column style="#{movConta.styleLinhaSelecionada}" sortBy="#{movConta.dataQuitacao}" id="DtQuitacaoContaPagar"
                         filterEvent="onkeyup">
                <f:facet name="header">
                    <h:outputText styleClass="topoPequeno" value="#{msg_aplic.prt_Finan_Lancamentos_quitacao}"/>
                </f:facet>
                <a4j:commandLink reRender="formLanc, form" action="#{MovContaControle.preparaEdicaoSemMudarPagina}"
                                 id="dataQuitacao">
                    <h:outputText value="#{movConta.dataQuitacao_Apresentar}">
                    </h:outputText>
                </a4j:commandLink>
            </rich:column>

            <!-- valor -->
            <rich:column style="#{movConta.styleLinhaSelecionada}" sortBy="#{movConta.valor}" filterEvent="onkeyup">
                <f:facet name="header">
                    <h:outputText styleClass="topoPequeno" value="#{msg_aplic.prt_Finan_Lancamentos_valor}"/>
                </f:facet>
                <a4j:commandLink reRender="formLanc, form" action="#{MovContaControle.preparaEdicaoSemMudarPagina}" id="valor">
                    <h:outputText value="#{movConta.valor_Apresentar}" styleClass="#{movConta.mudarCorLinkValor}">
                    </h:outputText>
                </a4j:commandLink>
            </rich:column>
            <!-- opções -->
            <rich:column style="#{movConta.styleLinhaSelecionada}">
                <f:facet name="header">
                    <h:outputText styleClass="topoPequeno" value="#{msg_aplic.prt_Finan_Lancamentos_operacoes}"/>
                </f:facet>
                <h:panelGrid columns="3">
                    <a4j:commandButton action="#{MovContaControle.preparaEdicaoSemMudarPagina}"
                                       value="#{msg_bt.btn_editar}"
                                       image="/imagens/botaoEditar.png"
                                       reRender="formLanc"
                                       alt="#{msg.msg_editar_dados}" styleClass="botoes"/>

                    <a4j:commandButton id="excluir"
                                       value="#{msg_bt.btn_excluir}"
                                       image="/imagens/botaoRemover.png" alt="#{msg.msg_excluir_dados}"
                                       styleClass="botaoExcluir"
                                       reRender="formConfirmaExclusao"
                                       oncomplete="Richfaces.showModalPanel('modalConfirmaExclusao');"
                                       action="#{MovContaControle.prepararExclusaoVindoDasParcelas}"
                                       status="false">
                    </a4j:commandButton>

                </h:panelGrid>
            </rich:column>
        </rich:dataTable>
        <rich:datascroller id="scrollItems" status="false" for="items"></rich:datascroller>
        <a4j:commandLink id="consultarLogAgendamentoFinanceiro" action="#{MovContaControle.realizarConsultaLogAgendamentoFinanceiro}"
                         reRender="formLanc"
                         rendered="#{MovContaControle.mostrarBotoesTelaLancamento}"
                         oncomplete="#{MovContaControle.msgAlert}"
                         style="display: inline-block; padding: 8px 15px;"
                         title="Visualizar Log" accesskey="4" styleClass="botoes nvoBt btSec">
            <i class="fa-icon-list"></i>
        </a4j:commandLink>
    </rich:panel>
    <!-- -------------------------- FIM - PARCELAS DE AGENDAMENTO ------------------------------------------- -->
</h:panelGroup>

</h:panelGrid>


<!--INICIO Rateio-->
<h:panelGrid width="100%" id="rateioGrid">
<h:panelGroup>
<h:panelGrid rendered="#{MovContaControle.existeMaisDeUmMovContaRateio}" id="tituloDivisaoRateioTelaLancamentosForm"
             styleClass="tablepreviewtotal" width="100%" columnClasses="colunaCentralizada">
    <h:outputText
            style="font-family:Arial, Helvetica, sans-serif;font-size:14px;font-weight:bold;text-decoration:none;color:#0f4c6b;"
            value="#{msg_aplic.prt_Finan_Lancamentos_rateioValor}"/>
</h:panelGrid>

<h:panelGrid rendered="#{MovContaControle.existeMaisDeUmMovContaRateio}" id="painelRateio" columns="2"
             rowClasses="linhaImpar, linhaPar" columnClasses="classEsquerda, classDireita" width="100%">
    <%--<img src="${contextoFinan}/images/arrow2.gif" width="16" height="16" style="vertical-align:middle;margin-right:6px;">--%>

    <!-- empresa -->
    <h:outputText rendered="#{MovContaControle.existeMaisDeUmMovContaRateio}" styleClass="tituloCampos"
                  id="empresaContaRatear"
                  value="Empresa:"/>
    <h:panelGroup>
    <h:selectOneMenu rendered="#{MovContaControle.existeMaisDeUmMovContaRateio}"
                     id="empresaRatear" onblur="blurinput(this);"
                     onfocus="focusinput(this);"
                     style="color:black;"
                     styleClass="form"
                     value="#{MovContaControle.empresaParaRateamento.codigo}">
        <f:selectItems value="#{MovContaControle.listaSelectItemsEmpresa}"/>
    </h:selectOneMenu>
    </h:panelGroup>
    <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_Finan_Lancamento_planoContas}"/>
    <h:panelGroup>

        <table cellpadding="0" cellspacing="0">
            <tr valign="top">
                <td><h:inputText id="nomePlanoSelecionadoRateio" size="50" maxlength="50"
                                 onfocus="focusinput(this);" styleClass="form"
                                 value="#{PlanoContasControle.planoNome}">
                    <a4j:support event="onchange"
                                 action="#{PlanoContasControle.setarPlanoPaiVazio}" reRender="formLanc"/>
                </h:inputText>

                    <rich:suggestionbox height="200" width="400"
                                        for="nomePlanoSelecionadoRateio" status="statusInComponent"
                                        immediate="true"
                                        nothingLabel="Nenhum Plano de Contas encontrado"
                                        suggestionAction="#{PlanoContasControle.executarAutocompletePesqPlanoContas}"
                                        minChars="1" rowClasses="linhaImpar, linhaPar" var="result"
                                        id="suggestionResponsavelRateio">
                        <a4j:support event="onselect" reRender="formLanc"
                                     focus="nomeCentroSelecionadoRateio"
                                     action="#{PlanoContasControle.selecionarPlanoContas}">
                        </a4j:support>
                        <h:column>
                            <f:facet name="header">
                                <h:outputText value="Nome" styleClass="textverysmall"/>
                            </f:facet>
                            <h:outputText styleClass="textverysmall"
                                          value="#{result.descricaoCurta}"/>
                        </h:column>
                        <h:column>
                            <f:facet name="header">
                                <h:outputText value="Tipo" styleClass="textverysmall"/>
                            </f:facet>
                            <h:outputText styleClass="textverysmall"
                                          value="#{result.tipoPadrao.descricao}"/>
                        </h:column>
                    </rich:suggestionbox></td>
                <td>&nbsp;<a4j:commandLink action="#{PlanoContasControle.verificarConsultaCorretaRateio}"
                                           reRender="modalPlanos"
                                           id="btAddPlanoRateio" value="Consultar"
                                           oncomplete="Richfaces.showModalPanel('modalPlanos')"/>
            </tr>
        </table>
    </h:panelGroup>
    <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_Finan_Lancamento_centroCusto}"/>
    <h:panelGroup>

        <table cellpadding="0" cellspacing="0">
            <tr valign="top">
                <td>
                    <h:inputText id="nomeCentroSelecionadoRateio" size="50" maxlength="50"
                                 onblur="blurinput(this);" onfocus="focusinput(this);"
                                 styleClass="form" value="#{CentroCustosControle.centroNome}">
                        <a4j:support event="onchange"
                                     action="#{CentroCustosControle.setarCentroVazio}" reRender="formLanc"/>
                    </h:inputText>

                    <rich:suggestionbox height="200" width="400"
                                        for="nomeCentroSelecionadoRateio" status="statusInComponent"
                                        immediate="true"
                                        nothingLabel="Nenhum Centro de Custos encontrado"
                                        suggestionAction="#{CentroCustosControle.executarAutocompletePesqCentroCusto}"
                                        minChars="1" rowClasses="linhaImpar, linhaPar" var="result"
                                        id="suggestionCentroCustoRateio">
                        <a4j:support event="onselect" reRender="formLanc" focus="rateioFormaPagto"
                                     action="#{CentroCustosControle.selecionarCentroCusto}">
                        </a4j:support>
                        <h:column>
                            <f:facet name="header">
                                <h:outputText value="Nome" styleClass="textverysmall"/>
                            </f:facet>
                            <h:outputText styleClass="textverysmall"
                                          value="#{result.descricaoCurta}"/>
                        </h:column>
                    </rich:suggestionbox>
                </td>
                <td>&nbsp;<a4j:commandLink action="#{CentroCustosControle.loadTree}"
                                           reRender="modalCentros"
                                           id="btAddCentroRateio" value="Consultar"
                                           oncomplete="Richfaces.showModalPanel('modalCentros')"/>
                </td>
            </tr>
        </table>
    </h:panelGroup>
    <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_Finan_Lancamento_formaPagamento}"/>
    <h:panelGroup>
        <h:selectOneMenu id="rateioFormaPagto" onblur="blurinput(this);"
                         onfocus="focusinput(this);"
                         style="color:black;"
                         styleClass="form"
                         value="#{MovContaControle.movContaRateioVO.formaPagamentoVO.codigo}">
            <f:selectItems value="#{MovContaControle.listaSelectItemFormaPagamento}"/>
        </h:selectOneMenu>
    </h:panelGroup>
    <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_Finan_Lancamento_tipoDocumento}"/>
    <h:panelGroup>
        <h:selectOneMenu id="rateioTipoDocumento" onblur="blurinput(this);"
                         onfocus="focusinput(this);"
                         style="color:black;"
                         styleClass="form"
                         value="#{MovContaControle.movContaRateioVO.tipoDocumentoVO.codigo}">
            <f:selectItems value="#{MovContaControle.listaSelectItemTipoDocumento}"/>
        </h:selectOneMenu>
    </h:panelGroup>

    <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_Finan_Lancamento_numDocumento}"/>
    <h:panelGroup>
        <h:inputText id="rateioNumeroDocumento" size="40" maxlength="40" onblur="blurinput(this);" onfocus="focusinput(this);"
                     styleClass="form" value="#{MovContaControle.movContaRateioVO.numeroDocumento}"/>
    </h:panelGroup>

    <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_Finan_Lancamento_descricao}"/>
    <h:panelGroup>
        <h:inputText id="rateioDescricao" size="40" maxlength="40" onblur="blurinput(this);" onfocus="focusinput(this);"
                     styleClass="form" value="#{MovContaControle.movContaRateioVO.descricao}"/>

    </h:panelGroup>
    <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_Finan_Lancamento_valor}"/>
    <h:panelGroup>
        <h:inputText id="rateioValor" size="40" maxlength="40"
                     onblur="blurinput(this);"
                     onfocus="focusinput(this);"
                     onkeypress="if(event.keyCode === 13) { document.getElementById('#{rich:clientId('botaoAdicionar')}').click(); return false; } else { return currencyFormat(this,'.',',',event); }"
                     styleClass="form" value="#{MovContaControle.movContaRateioVO.valor}">
            <f:converter converterId="FormatadorNumerico"/>
        </h:inputText>
    </h:panelGroup>

</h:panelGrid>

<h:panelGrid rendered="#{MovContaControle.existeMaisDeUmMovContaRateio}" id="panelBotaoAdicionar" columns="1"
             width="50%" columnClasses="classEsquerda, classDireita"
             style="padding-bottom:20px;padding-top:20px;padding-left:150px;">
    <a4j:commandButton id="botaoAdicionar"
                       action="#{MovContaControle.adicionarMovContaRateioLista}"
                       value="#{msg_bt.btn_adicionar}"
                       image="/imagens/Adicionar.png"
                       styleClass="botoes"
                       reRender="rateioGrid, Quitar">
    </a4j:commandButton>
</h:panelGrid>

<h:panelGrid id="tabelaMovRateio" rendered="#{MovContaControle.existeMaisDeUmMovContaRateio}" columns="1" width="100%"
             columnClasses="colunaCentralizada">
    <h:dataTable id="itemMovContaRateio" headerClass="subordinado" rows="5"
                 rowClasses="linhaImpar, linhaPar" columnClasses="colunaCentralizada" width="80%"
                 value="#{MovContaControle.movContaVO.movContaRateios}" var="itemMovContaRateio"
                 style="padding-left:200px;">
        <h:column>
            <f:facet name="header">
                <h:outputText value="Empresa"/>
            </f:facet>
            <h:outputText
                    value="#{itemMovContaRateio.empresaVO.nome}"/>
        </h:column>
        <h:column>
            <f:facet name="header">
                <h:outputText value="#{msg_aplic.prt_Finan_Lancamento_planoContas}"/>
            </f:facet>
            <h:outputText
                    value="#{itemMovContaRateio.planoContaVO.codigoPlano} - #{itemMovContaRateio.planoContaVO.descricao}"/>
        </h:column>
        <h:column>
            <f:facet name="header">
                <h:outputText value="#{msg_aplic.prt_Finan_Lancamento_centroCusto}"/>
            </f:facet>
            <h:outputText
                    value="#{itemMovContaRateio.centroCustoVO.codigoCentro} - #{itemMovContaRateio.centroCustoVO.descricao}"/>
        </h:column>
        <h:column>
            <f:facet name="header">
                <h:outputText value="#{msg_aplic.prt_Finan_Lancamento_descricao}"/>
            </f:facet>
            <h:outputText value="#{itemMovContaRateio.descricao}"/>
        </h:column>
        <h:column>
            <f:facet name="header">
                <h:outputText value="#{msg_aplic.prt_Finan_Lancamento_formaPagamento}"/>
            </f:facet>
            <h:outputText value="#{itemMovContaRateio.formaPagamentoVO.descricao}">
            </h:outputText>
        </h:column>
        <h:column>
            <f:facet name="header">
                <h:outputText value="#{msg_aplic.prt_Finan_Lancamento_numDocumento}"/>
            </f:facet>
            <h:outputText value="#{itemMovContaRateio.numeroDocumento}">
            </h:outputText>
        </h:column>
        <h:column>
            <f:facet name="header">
                <h:outputText value="#{msg_aplic.prt_Finan_Lancamento_tipoDocumento}"/>
            </f:facet>
            <h:outputText value="#{itemMovContaRateio.tipoDocumentoVO.descricao}">
            </h:outputText>
        </h:column>
        <h:column>
            <f:facet name="header">
                <h:outputText value="#{msg_aplic.prt_Finan_Lancamento_valor}"/>
            </f:facet>
            <h:outputText value="#{itemMovContaRateio.valor}">
                <f:converter converterId="FormatadorNumerico"/>
            </h:outputText>
        </h:column>
        <h:column>
            <f:facet name="header">
                <h:outputText value="#{msg_bt.btn_opcoes}"/>
            </f:facet>
            <h:panelGroup>
                <a4j:commandButton id="editarMovRateio" reRender="rateioGrid"
                                   action="#{MovContaControle.editarMovRateio}" value="#{msg_bt.btn_editar}"
                                   image="/imagens/botaoEditar.png" styleClass="botoes"/>

                <h:outputText value="    "/>

                <a4j:commandButton id="removerMovRateio"
                                   action="#{MovContaControle.preparaExclusaoRateio}" value="#{msg_bt.btn_excluir}"
                                   image="/imagens/botaoRemover.png" alt="#{msg.msg_excluir_dados}"
                                   oncomplete="Richfaces.showModalPanel('modalConfirmaExclusaoRateio');"
                                   styleClass="botaoExcluir"/>
            </h:panelGroup>
        </h:column>
    </h:dataTable>
    <rich:datascroller align="center" for="itemMovContaRateio" maxPages="10"
                       id="scResultadoItemVenda"/>


</h:panelGrid>

<h:panelGrid id="excluirTodosRateio" rendered="#{MovContaControle.existeMaisDeUmMovContaRateio}" columns="2"
             width="100%" columnClasses="colunaCentralizada">
    <h:panelGrid width="65%" columnClasses="colunaDireita">
        <a4j:commandButton id="excluirRateios" value="#{msg_bt.btn_excluir}"
                         image="/imagens/btn_ExcluirTodososRateios.png" alt="#{msg.msg_excluir}"
                           reRender="formLanc"
                         action="#{MovContaControle.excluirTodosRateios}">
        </a4j:commandButton>
    </h:panelGrid>
    <h:panelGrid columns="2" width="85%" columnClasses="colunaDireita">
        <h:panelGrid width="100%" columnClasses="colunaDireita">
            <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_Finan_Lancamentos_divisaoSoma} "/>
            <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_Finan_Lancamentos_diferenca} "/>
            <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_Finan_Lancamentos_valorLancamento} "/>
        </h:panelGrid>
        <h:panelGrid width="100%" columnClasses="colunaEsquerda">
            <h:outputText id="valorDivisao" value="#{MovContaControle.somaDivisao}" styleClass="tituloCamposVerde">
                <f:converter converterId="FormatadorNumerico"/>
            </h:outputText>
            <h:outputText id="diferenca" value="#{MovContaControle.diferenca}" styleClass="tituloCamposVerde">
                <f:converter converterId="FormatadorNumerico"/>
            </h:outputText>
            <h:outputText id="valorLancamento" value="#{MovContaControle.valorLancamento}"
                          styleClass="tituloCamposVerde">
                <f:converter converterId="FormatadorNumerico"/>
            </h:outputText>
        </h:panelGrid>
    </h:panelGrid>
</h:panelGrid>
<!--Fim Rateio-->


<h:panelGrid id="panelMensagem" columns="1" width="100%" styleClass="tabMensagens">
    <h:panelGrid columns="3" width="100%" styleClass="tabMensagens">
        <h:panelGrid columns="1" width="100%">

            <h:outputText value=" "/>

        </h:panelGrid>
        <h:commandButton rendered="#{MovContaControle.sucesso}" image="/imagens/sucesso.png"/>
        <h:commandButton rendered="#{MovContaControle.erro}" image="/imagens/erro.png"/>
        <h:panelGrid columns="1" width="100%">
            <h:outputText id="msgContaPagar" styleClass="mensagem" value="#{MovContaControle.mensagem}"/>
            <h:outputText styleClass="mensagemDetalhada" value="#{MovContaControle.mensagemDetalhada}"/>
        </h:panelGrid>
    </h:panelGrid>
</h:panelGrid>
    <h:panelGrid width="100%" id="gridBotoesLancamento" columnClasses="colunaCentralizada" columns="1">
        <h:panelGroup>
            <a4j:commandButton id="voltar" rendered="#{!MovContaControle.lancamentoDemonstrativo}"
                             action="#{MovContaControle.voltar}"
                             value="Voltar"
                             reRender="form:pagarEmConjunto, items, painelPaginacao,containerFuncMask, painelPaginacaoTop, totalValor,totalRegistros,valorTotalReceb,valorTotalPag, panelMensagem, totalReg, valorTotal,panelTipoLancamento, filtros, checado, panelTotalizadoresTop, panelTotalizadoresButton, panelTotalizadoresTopNovo"
                             accesskey="1"
                             styleClass="botoes nvoBt btSec"/>
            <h:outputText value="    "/>
            <a4j:commandButton id="clonar" rendered="#{MovContaControle.movContaVO.codigo > 0 && MovContaControle.botoesCamposEnabled}"
                               action="#{MovContaControle.clonar}" value="Clonar"
                               reRender="items,tabelaGeral,gridBotoesLancamento"
                               accesskey="2" styleClass="botoes nvoBt btSec btPerigo"/>
            <h:outputText value="    "/>
            <%--Botão Gravar normal--%>
            <a4j:commandButton id="btnGravarLanc"
                               rendered="#{!MovContaControle.movContaVO.presaEmLoteDePagamento}"
                               reRender="modalAlteracoesAgendamento,panelAutorizacaoFuncionalidade, panelMensagem, panelCampos,gridBotoesLancamento"
                               oncomplete="#{MovContaControle.onCompleteGravar}"
                               value="Gravar"
                               action="#{MovContaControle.verificarConfiguracaoSolicitaSenha}"
                               styleClass="botoes nvoBt btSec btPerigo">
            </a4j:commandButton>
            <%--Botão Gravar Fake, pois conta está presa em lote de pagamento--%>
            <a4j:commandButton id="btnGravarLancFake"
                               title="#{MovContaControle.movContaVO.msgPresaLoteDePagamento_Apresentar}"
                               rendered="#{MovContaControle.movContaVO.presaEmLoteDePagamento}"
                               value="Gravar"
                               accesskey="3"
                               style="cursor: not-allowed; opacity: 50%;"
                               styleClass="botoes nvoBt btSec tooltipster"
                               onclick="return false;">
            </a4j:commandButton>

            <a4j:commandButton id="excluirLanc"
                               rendered="#{MovContaControle.apresentarBotaoExcluirLanc}"
                               value="Excluir"
                               reRender="panelAutorizacaoFuncionalidade, formLanc"
                               action="#{MovContaControle.preparaExclusaoTelaLancamentos}"
                               styleClass="botoes nvoBt btSec btPerigo"
                               oncomplete="#{MovContaControle.onCompleteExcluirLanc}">
                <f:setPropertyActionListener value="#{MovContaControle.movContaVO}" target="#{MovContaControle.movContaExcluir}"/>
            </a4j:commandButton>

            <h:outputText value="    "/>
            <%--Botão Quitar normal--%>
            <a4j:commandButton id="Quitar"
                               rendered="#{!MovContaControle.movContaVO.presaEmLoteDePagamento && MovContaControle.movContaVO.botaoQuitar && MovContaControle.botoesCamposEnabled && MovContaControle.apresentarBotaoQuitar}"
                               reRender="formLanc,modalAlteracoesAgendamento,modalPanelLancarPagamento,panelAutorizacaoFuncionalidade, gridBotoesLancamento"
                               oncomplete="#{MovContaControle.onCompleteGravar}"
                               action="#{MovContaControle.autorizacaoLancamentoContasPagarQuitar}"
                               value="Quitar" accesskey="3" styleClass="botoes nvoBt"/>
            <%--Botão Quitar Fake, pois conta está presa em lote de pagamento--%>
            <a4j:commandButton
                    id="QuitarFake"
                    title="#{MovContaControle.movContaVO.msgPresaLoteDePagamento_Apresentar}"
                    rendered="#{MovContaControle.movContaVO.presaEmLoteDePagamento && MovContaControle.movContaVO.botaoQuitar && MovContaControle.botoesCamposEnabled && MovContaControle.apresentarBotaoQuitar}"
                    value="Quitar"
                    accesskey="3"
                    style="cursor: not-allowed; opacity: 60%;"
                    styleClass="botoes nvoBt tooltipster"
                    onclick="return false;"/>
            <a4j:commandButton id="quitarOnline"
                               reRender="formLanc,modalAlteracoesAgendamento,modalPanelLancarPagamento,panelAutorizacaoFuncionalidade, gridBotoesLancamento"
                               oncomplete="#{MovContaControle.onCompleteGravar}"
                               rendered="#{MovContaControle.movContaVO.botaoQuitar && MovContaControle.botoesCamposEnabled &&
                                MovContaControle.apresentarBotaoQuitar && MovContaControle.moduloOpenBankAtivado && MovContaControle.movContaVO.tipoOperacaoLancamento.codigo == 1 }"
                               action="#{MovContaControle.autorizacaoLancamentoContasPagarQuitarOnline}"
                               value="Quitar Online" accesskey="3" styleClass="botoes nvoBt btSec"/>
            <h:outputText value="    "/>
            <a4j:commandLink id="consultar" action="#{MovContaControle.realizarConsultaLogObjetoSelecionado}"
                               reRender="formLanc"
                               rendered="#{MovContaControle.mostrarBotoesTelaLancamento}"
                               oncomplete="#{MovContaControle.oncompleteLog}"
                               style="display: inline-block; padding: 8px 15px;"
                               title="Visualizar Log" accesskey="4" styleClass="botoes nvoBt btSec">
                <i class="fa-icon-list"></i>
            </a4j:commandLink>
        </h:panelGroup>
    </h:panelGrid>
</h:panelGroup>
</h:panelGrid>


<!--Fim - Tela de Lançamento-->

