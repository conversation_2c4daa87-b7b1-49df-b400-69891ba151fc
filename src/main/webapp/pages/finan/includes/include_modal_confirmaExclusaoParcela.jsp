<%@page contentType="text/html" pageEncoding="ISO-8859-1" %>
<%@include file="include_imports.jsp" %>

    <a4j:outputPanel>
        <rich:modalPanel id="modalConfirmaExclusao" autosized="true" width="400" height="130"  shadowOpacity="true" styleClass="novaModal">
            <f:facet name="header">
                <h:panelGroup>
                    <h:outputText value="Confirmação"></h:outputText>
                </h:panelGroup>
            </f:facet>
            <f:facet name="controls">
                <h:panelGroup>
                    <h:outputText
                            styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign"
                            id="hidelinkmodalConfirmaExclusao"/>
                    <rich:componentControl for="modalConfirmaExclusao" attachTo="hidelinkmodalConfirmaExclusao" operation="hide"  event="onclick" />
                </h:panelGroup>
            </f:facet>
            <h:form id="formConfirmaExclusao">
                <rich:panel>
                    <h:outputText id="msgExclusao" styleClass="tituloDemonstrativo" value="#{msg_aplic.prt_Finan_Lancamentos_confirmaExclusao} #{MovContaControle.contaExclusao.descricao} ?"/>
                    
                </rich:panel>
                <center style="margin: 15px;">
                    <a4j:commandLink id="ModalExlusaoParcelaSim"
                                       action="#{MovContaControle.excluirVindoDasParcelas}"
                                       oncomplete="#{MovContaControle.msgAlert}"
                                       reRender="formLanc, formConfirmaExclusaoPararAgendamento" value="#{msg_aplic.sim}"
                                       styleClass="botoes nvoBt">

                    </a4j:commandLink>

                    <a4j:commandLink id="ModalExlusaoParcelaNao"
                                     style="margin-left: 15px;"
                                     styleClass="botoes nvoBt btSec"
                    				   status="statusInComponent" value="#{msg_aplic.nao}"
                                       oncomplete="Richfaces.hideModalPanel('modalConfirmaExclusao');">
					</a4j:commandLink>
              	</center>
            </h:form>
        </rich:modalPanel>
    </a4j:outputPanel>
