<%@page contentType="text/html" pageEncoding="ISO-8859-1" %>
<%@include file="imports.jsp" %>
<a4j:outputPanel>
    <rich:modalPanel id="modalNovoLotePagamento" autosized="true" styleClass="novaModal"
                     minWidth="1000" width="1000" minHeight="580" height="580" top="30" shadowOpacity="true">
        <f:facet name="header">
            <h:panelGroup>
                <h:outputText value="Novo Lote de Pagamento"></h:outputText>
            </h:panelGroup>
        </f:facet>
        <f:facet name="controls">
            <h:panelGroup>
                <h:outputText
                        styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign "
                        id="hideModalNovoLotePagamento"/>
                <rich:componentControl for="modalNovoLotePagamento" attachTo="hideModalNovoLotePagamento"
                                       operation="hide" event="onclick"/>
            </h:panelGroup>
        </f:facet>
        <h:form id="formNovoLotePagamento" style="display: grid; grid-template-rows: 0fr 0fr 0fr;">

            <h:panelGroup id="panelAcoesPorTipoModalNovoLote">
                <table id="tableAcoesPorTipoModalNovoLote">
                    <tr style="display: block; border-radius: 10px; border: 1px solid #ede4e4;">
                        <a4j:repeat value="#{MovContaControle.tiposContaPagarLoteExibirVO}" var="tipo">
                            <td class="botaoConta">
                                <a4j:commandLink actionListener="#{MovContaControle.filtrarTipoContaPagarLote}"
                                                 style="display: flex; text-decoration: none; padding-right: 40px;"
                                                 styleClass="tooltipster"
                                                 oncomplete="#{MovContaControle.mensagemNotificar}"
                                                 title="#{tipo.titleTipoApresentar}"
                                                 reRender="formNovoLotePagamento">
                                    <h:panelGroup style="display: flex; align-items: center; position: relative;">
                                        <!-- Aplica a opacidade apenas na imagem -->
                                        <h:graphicImage value="#{tipo.uriImagem}"
                                                        style="#{!tipo.selecionadoParaFiltrar ? 'opacity: 0.2;' : ''}">
                                        </h:graphicImage>
                                    <h:panelGroup id="panelContadoresFiltro" styleClass="div-geral-contadores">
                                        <!-- Texto de total de registros, formatado como círculo -->
                                        <h:outputText id="totalRegistFiltroElegiveis"
                                                      value="#{tipo.totalRegistrosElegiveis}"
                                                      rendered="#{tipo.totalRegistrosElegiveis > 0}"
                                                      title="<b>Quantidade Total de registros elegíveis para a forma de pagamento #{tipo.descricao}</b>"
                                                      styleClass="contador-circulo-elegiveis tooltipster">
                                        </h:outputText>
                                        <!-- Texto de total de registros, formatado como círculo -->
                                        <h:outputText id="totalRegistFiltroNaoElegiveis"
                                                      value="#{tipo.totalRegistrosNaoElegiveis}"
                                                      rendered="#{tipo.totalRegistrosNaoElegiveis > 0}"
                                                      title="<b>Quantidade Total de registros não elegíveis para a forma de pagamento #{tipo.descricao}</b>"
                                                      styleClass="contador-circulo-nao-elegiveis tooltipster">
                                        </h:outputText>
                                    </h:panelGroup>
                                    </h:panelGroup>
                                    <f:attribute name="tipo" value="#{tipo}"/>
                                </a4j:commandLink>
                            </td>
                        </a4j:repeat>
                    </tr>
                </table>
            </h:panelGroup>

            <%--PIX--%>
            <rich:dataTable id="listaPix" width="100%" headerClass="consulta" rowClasses="linhaPar"
                            style="margin-top: 10px;"
                            rendered="#{MovContaControle.exibirListaParaNovoLotePix}"
                            reRender="paginaAtual, paginaAtualTop,painelPaginacaoTop,painelPaginacao"
                            columnClasses="centralizado, centralizado, centralizado, centralizado, direita, centralizado"
                            value="#{MovContaControle.listaMovContasLotePixPaginada}" rows="10" var="movConta">

                <!-- descricao -->
                <rich:column style="#{movConta.styleLinhaSelecionada}"
                             filterEvent="onkeyup">
                    <f:facet name="header">
                        <h:outputText styleClass="topoPequeno" value="Descrição"/>
                    </f:facet>
                    <h:panelGroup>
                        <h:outputText value="#{movConta.descricao}"/>
                    </h:panelGroup>
                </rich:column>

                <!-- favorecido -->
                <rich:column style="#{movConta.styleLinhaSelecionada}"
                             filterEvent="onkeyup">
                    <f:facet name="header">
                        <h:outputText styleClass="topoPequeno" value="Favorecido"/>
                    </f:facet>
                    <h:panelGroup>
                        <h:outputText value="#{movConta.pessoaVO.nome}"/>
                    </h:panelGroup>
                </rich:column>

                <!-- vencimento -->
                <rich:column style="#{movConta.styleLinhaSelecionada}"
                             filterEvent="onkeyup">
                    <f:facet name="header">
                        <h:outputText styleClass="topoPequeno" value="Vencimento"/>
                    </f:facet>
                    <h:outputText value="#{movConta.dataVencimento_Apresentar}">
                    </h:outputText>
                </rich:column>

                <!-- valor -->
                <rich:column style="#{movConta.styleLinhaSelecionada}">
                    <f:facet name="header">
                        <h:outputText styleClass="topoPequeno" value="Valor"/>
                    </f:facet>
                    <h:outputText value="#{movConta.valor_Apresentar}" styleClass="#{movConta.mudarCorLinkValor}">
                    </h:outputText>
                </rich:column>

                <!-- elegível -->
                <rich:column style="#{movConta.styleLinhaSelecionada}">
                    <f:facet name="header">
                        <h:outputText styleClass="topoPequeno" value="Elegível"/>
                    </f:facet>
                    <h:panelGroup id="panelElegibilidadeIcon" style="display: block; text-align-last: center;">
                        <h:outputText rendered="#{movConta.elegivelParaLoteDePagamento}"
                                      id="movContaElegivel"
                                      title="Esta conta será incluída no lote de pagamento."
                                      styleClass="fa-icon-ok-sign tooltipster"
                                      style="color: #3cdb5c; font-size: 15px;"/>
                        <h:outputText rendered="#{!movConta.elegivelParaLoteDePagamento}"
                                      id="movContaNaoElegivel"
                                      title="#{movConta.getMovContaNaoElegivelLotePagamentoApresentar()}"
                                      styleClass="fa-icon-remove-sign tooltipster"
                                      style="color: #BC2525; font-size: 15px;"/>
                    </h:panelGroup>
                </rich:column>

                <%--AÇÕES--%>
                <rich:column style="#{movConta.styleLinhaSelecionada}; display: flex">
                    <f:facet name="header">
                        <h:outputText value="Ações"/>
                    </f:facet>
                    <a4j:commandLink id="editarLancamentoLotePix"
                                     actionListener="#{MovContaControle.abrirModalNovoLotePagamentoEdicaoLancamento}"
                                     title="Editar informações de pagamento"
                                     style="text-decoration: none"
                                     styleClass="linkAzul texto-size-14 tooltipster"
                                     reRender="modalNovoLotePagamentoEdicaoLancamento, formNovoLotePagamentoEdicaoLancamento"
                                     oncomplete="#{MovContaControle.mensagemNotificar}#{MovContaControle.abrirFecharModalNovoLotePagamentoEdicaoLancamento}">
                        <i class="fa-icon-edit" style="font-size: 16px; margin-top: 2px; margin-left: 17px;"></i>
                        <f:attribute name="movconta" value="#{movConta}"/>
                    </a4j:commandLink>
                    <a4j:commandLink id="removerLancamentoLotePix"
                                     rendered="#{movConta.elegivelParaLoteDePagamento}"
                                     actionListener="#{MovContaControle.removerItemDoLote}"
                                     title="Remover este lançamento do lote"
                                     style="text-decoration: none"
                                     styleClass="linkAzul texto-size-14 tooltipster"
                                     reRender="formNovoLotePagamento, listaPix, listaBoleto, listaOutros"
                                     oncomplete="#{MovContaControle.mensagemNotificar}">
                        <i class="fa-icon-trash" style="font-size: 16px; margin-left: 5px; margin-top: 1px"></i>
                        <f:attribute name="movconta" value="#{movConta}"/>
                    </a4j:commandLink>
                </rich:column>
            </rich:dataTable>

            <%--BOLETO--%>
            <rich:dataTable id="listaBoleto" width="100%" headerClass="consulta" rowClasses="linhaPar"
                            style="margin-top: 10px;"
                            rendered="#{MovContaControle.exibirListaParaNovoLoteBoleto}"
                            reRender="paginaAtual, paginaAtualTop,painelPaginacaoTop,painelPaginacao"
                            columnClasses="centralizado, centralizado, centralizado, centralizado, direita, centralizado"
                            value="#{MovContaControle.listaMovContasLoteBoletoPaginada}" rows="10" var="movConta">

                <!-- descricao -->
                <rich:column style="#{movConta.styleLinhaSelecionada}"
                             filterEvent="onkeyup">
                    <f:facet name="header">
                        <h:outputText styleClass="topoPequeno" value="Descrição"/>
                    </f:facet>
                    <h:panelGroup>
                        <h:outputText value="#{movConta.descricao}"/>
                    </h:panelGroup>
                </rich:column>

                <!-- favorecido -->
                <rich:column style="#{movConta.styleLinhaSelecionada}"
                             filterEvent="onkeyup">
                    <f:facet name="header">
                        <h:outputText styleClass="topoPequeno" value="Favorecido"/>
                    </f:facet>
                    <h:panelGroup>
                        <h:outputText value="#{movConta.pessoaVO.nome}"/>
                    </h:panelGroup>
                </rich:column>

                <!-- vencimento -->
                <rich:column style="#{movConta.styleLinhaSelecionada}"
                             filterEvent="onkeyup">
                    <f:facet name="header">
                        <h:outputText styleClass="topoPequeno" value="Vencimento"/>
                    </f:facet>
                    <h:outputText value="#{movConta.dataVencimento_Apresentar}">
                    </h:outputText>
                </rich:column>

                <!-- valor -->
                <rich:column style="#{movConta.styleLinhaSelecionada}">
                    <f:facet name="header">
                        <h:outputText styleClass="topoPequeno" value="Valor"/>
                    </f:facet>
                    <h:outputText value="#{movConta.valor_Apresentar}" styleClass="#{movConta.mudarCorLinkValor}">
                    </h:outputText>
                </rich:column>

                <!-- elegível -->
                <rich:column style="#{movConta.styleLinhaSelecionada}">
                    <f:facet name="header">
                        <h:outputText styleClass="topoPequeno" value="Elegível"/>
                    </f:facet>
                    <h:panelGroup id="panelElegibilidadeIcon" style="display: block; text-align-last: center;">
                        <h:outputText rendered="#{movConta.elegivelParaLoteDePagamento}"
                                      id="movContaElegivel"
                                      title="Esta conta será incluída no lote de pagamento."
                                      styleClass="fa-icon-ok-sign tooltipster"
                                      style="color: #3cdb5c; font-size: 15px;"/>
                        <h:outputText rendered="#{!movConta.elegivelParaLoteDePagamento}"
                                      id="movContaNaoElegivel"
                                      title="#{movConta.getMovContaNaoElegivelLotePagamentoApresentar()}"
                                      styleClass="fa-icon-remove-sign tooltipster"
                                      style="color: #BC2525; font-size: 15px;"/>
                    </h:panelGroup>
                </rich:column>

                <%--AÇÕES--%>
                <rich:column style="#{movConta.styleLinhaSelecionada}; display: flex">
                    <f:facet name="header">
                        <h:outputText value="Ações"/>
                    </f:facet>
                    <a4j:commandLink id="editarLancamentoLoteBoleto"
                                     actionListener="#{MovContaControle.abrirModalNovoLotePagamentoEdicaoLancamento}"
                                     title="Editar informações de pagamento"
                                     style="text-decoration: none"
                                     styleClass="linkAzul texto-size-14 tooltipster"
                                     reRender="modalNovoLotePagamentoEdicaoLancamento, formNovoLotePagamentoEdicaoLancamento"
                                     oncomplete="#{MovContaControle.mensagemNotificar}#{MovContaControle.abrirFecharModalNovoLotePagamentoEdicaoLancamento}">
                        <i class="fa-icon-edit" style="font-size: 16px; margin-top: 2px; margin-left: 17px;"></i>
                        <f:attribute name="movconta" value="#{movConta}"/>
                    </a4j:commandLink>
                    <a4j:commandLink id="removerLancamentoLoteBoleto"
                                     rendered="#{movConta.elegivelParaLoteDePagamento}"
                                     actionListener="#{MovContaControle.removerItemDoLote}"
                                     title="Remover este lançamento do lote"
                                     style="text-decoration: none"
                                     styleClass="linkAzul texto-size-14 tooltipster"
                                     reRender="formNovoLotePagamento, listaPix, listaBoleto, listaOutros"
                                     oncomplete="#{MovContaControle.mensagemNotificar}">
                        <i class="fa-icon-trash" style="font-size: 16px; margin-left: 5px; margin-top: 1px"></i>
                        <f:attribute name="movconta" value="#{movConta}"/>
                    </a4j:commandLink>
                </rich:column>
            </rich:dataTable>

            <%--OUTROS--%>
            <rich:dataTable id="listaOutros" width="100%" headerClass="consulta" rowClasses="linhaPar"
                            style="margin-top: 10px;"
                            rendered="#{MovContaControle.exibirListaParaNovoLoteOutros}"
                            reRender="paginaAtual, paginaAtualTop,painelPaginacaoTop,painelPaginacao"
                            columnClasses="centralizado, centralizado, centralizado, centralizado, direita, centralizado"
                            value="#{MovContaControle.listaMovContasLoteOutrosPaginada}" rows="10" var="movConta">

                <!-- descricao -->
                <rich:column style="#{movConta.styleLinhaSelecionada}"
                             filterEvent="onkeyup">
                    <f:facet name="header">
                        <h:outputText styleClass="topoPequeno" value="Descrição"/>
                    </f:facet>
                    <h:panelGroup>
                        <h:outputText value="#{movConta.descricao}"/>
                    </h:panelGroup>
                </rich:column>

                <!-- favorecido -->
                <rich:column style="#{movConta.styleLinhaSelecionada}"
                             filterEvent="onkeyup">
                    <f:facet name="header">
                        <h:outputText styleClass="topoPequeno" value="Favorecido"/>
                    </f:facet>
                    <h:panelGroup>
                        <h:outputText value="#{movConta.pessoaVO.nome}"/>
                    </h:panelGroup>
                </rich:column>

                <!-- vencimento -->
                <rich:column style="#{movConta.styleLinhaSelecionada}"
                             filterEvent="onkeyup">
                    <f:facet name="header">
                        <h:outputText styleClass="topoPequeno" value="Vencimento"/>
                    </f:facet>
                    <h:outputText value="#{movConta.dataVencimento_Apresentar}">
                    </h:outputText>
                </rich:column>

                <!-- valor -->
                <rich:column style="#{movConta.styleLinhaSelecionada}">
                    <f:facet name="header">
                        <h:outputText styleClass="topoPequeno" value="Valor"/>
                    </f:facet>
                    <h:outputText value="#{movConta.valor_Apresentar}" styleClass="#{movConta.mudarCorLinkValor}">
                    </h:outputText>
                </rich:column>

                <!-- elegível -->
                <rich:column style="#{movConta.styleLinhaSelecionada}">
                    <f:facet name="header">
                        <h:outputText styleClass="topoPequeno" value="Elegível"/>
                    </f:facet>
                    <h:panelGroup id="panelElegibilidadeIcon" style="display: block; text-align-last: center;">
                        <h:outputText rendered="#{movConta.elegivelParaLoteDePagamento}"
                                      id="movContaElegivel"
                                      title="Esta conta será incluída no lote de pagamento."
                                      styleClass="fa-icon-ok-sign tooltipster"
                                      style="color: #3cdb5c; font-size: 15px;"/>
                        <h:outputText rendered="#{!movConta.elegivelParaLoteDePagamento}"
                                      id="movContaNaoElegivel"
                                      title="#{movConta.getMovContaNaoElegivelLotePagamentoApresentar()}"
                                      styleClass="fa-icon-remove-sign tooltipster"
                                      style="color: #BC2525; font-size: 15px;"/>
                    </h:panelGroup>
                </rich:column>

                <%--AÇÕES--%>
                <rich:column style="#{movConta.styleLinhaSelecionada}; display: flex">
                    <f:facet name="header">
                        <h:outputText value="Ações"/>
                    </f:facet>
                    <a4j:commandLink id="editarLancamentoLoteOutros"
                                     actionListener="#{MovContaControle.abrirModalNovoLotePagamentoEdicaoLancamento}"
                                     title="Editar informações de pagamento"
                                     style="text-decoration: none"
                                     styleClass="linkAzul texto-size-14 tooltipster"
                                     reRender="modalNovoLotePagamentoEdicaoLancamento, formNovoLotePagamentoEdicaoLancamento"
                                     oncomplete="#{MovContaControle.mensagemNotificar}#{MovContaControle.abrirFecharModalNovoLotePagamentoEdicaoLancamento}">
                        <i class="fa-icon-edit" style="font-size: 16px; margin-top: 2px; margin-left: 17px;"></i>
                        <f:attribute name="movconta" value="#{movConta}"/>
                    </a4j:commandLink>
                    <a4j:commandLink id="removerLancamentoLoteOutros"
                                     rendered="#{movConta.elegivelParaLoteDePagamento}"
                                     actionListener="#{MovContaControle.removerItemDoLote}"
                                     title="Remover este lançamento do lote"
                                     style="text-decoration: none"
                                     styleClass="linkAzul texto-size-14 tooltipster"
                                     reRender="formNovoLotePagamento, listaPix, listaBoleto, listaOutros"
                                     oncomplete="#{MovContaControle.mensagemNotificar}">
                        <i class="fa-icon-trash" style="font-size: 16px; margin-left: 5px; margin-top: 1px"></i>
                        <f:attribute name="movconta" value="#{movConta}"/>
                    </a4j:commandLink>
                </rich:column>
            </rich:dataTable>

            <%--TRANSFERÊNCIA--%>
            <rich:dataTable id="listaTransferencia" width="100%" headerClass="consulta" rowClasses="linhaPar"
                            style="margin-top: 10px;"
                            rendered="#{MovContaControle.exibirListaParaNovoLoteTransferencia}"
                            reRender="paginaAtual, paginaAtualTop,painelPaginacaoTop,painelPaginacao"
                            columnClasses="centralizado, centralizado, centralizado, centralizado, direita, centralizado"
                            value="#{MovContaControle.listaMovContasLoteTransferenciaPaginada}" rows="10" var="movConta">

                <!-- descricao -->
                <rich:column style="#{movConta.styleLinhaSelecionada}"
                             filterEvent="onkeyup">
                    <f:facet name="header">
                        <h:outputText styleClass="topoPequeno" value="Descrição"/>
                    </f:facet>
                    <h:panelGroup>
                        <h:outputText value="#{movConta.descricao}"/>
                    </h:panelGroup>
                </rich:column>

                <!-- favorecido -->
                <rich:column style="#{movConta.styleLinhaSelecionada}"
                             filterEvent="onkeyup">
                    <f:facet name="header">
                        <h:outputText styleClass="topoPequeno" value="Favorecido"/>
                    </f:facet>
                    <h:panelGroup>
                        <h:outputText value="#{movConta.pessoaVO.nome}"/>
                    </h:panelGroup>
                </rich:column>

                <!-- vencimento -->
                <rich:column style="#{movConta.styleLinhaSelecionada}"
                             filterEvent="onkeyup">
                    <f:facet name="header">
                        <h:outputText styleClass="topoPequeno" value="Vencimento"/>
                    </f:facet>
                    <h:outputText value="#{movConta.dataVencimento_Apresentar}">
                    </h:outputText>
                </rich:column>

                <!-- valor -->
                <rich:column style="#{movConta.styleLinhaSelecionada}">
                    <f:facet name="header">
                        <h:outputText styleClass="topoPequeno" value="Valor"/>
                    </f:facet>
                    <h:outputText value="#{movConta.valor_Apresentar}" styleClass="#{movConta.mudarCorLinkValor}">
                    </h:outputText>
                </rich:column>

                <!-- elegível -->
                <rich:column style="#{movConta.styleLinhaSelecionada}">
                    <f:facet name="header">
                        <h:outputText styleClass="topoPequeno" value="Elegível"/>
                    </f:facet>
                    <h:panelGroup id="panelElegibilidadeIcon" style="display: block; text-align-last: center;">
                        <h:outputText rendered="#{movConta.elegivelParaLoteDePagamento}"
                                      id="movContaElegivel"
                                      title="Esta conta será incluída no lote de pagamento."
                                      styleClass="fa-icon-ok-sign tooltipster"
                                      style="color: #3cdb5c; font-size: 15px;"/>
                        <h:outputText rendered="#{!movConta.elegivelParaLoteDePagamento}"
                                      id="movContaNaoElegivel"
                                      title="#{movConta.getMovContaNaoElegivelLotePagamentoApresentar()}"
                                      styleClass="fa-icon-remove-sign tooltipster"
                                      style="color: #BC2525; font-size: 15px;"/>
                    </h:panelGroup>
                </rich:column>

                <%--AÇÕES--%>
                <rich:column style="#{movConta.styleLinhaSelecionada}; display: flex">
                    <f:facet name="header">
                        <h:outputText value="Ações"/>
                    </f:facet>
                    <a4j:commandLink id="editarLancamentoLoteTransferencia"
                                     actionListener="#{MovContaControle.abrirModalNovoLotePagamentoEdicaoLancamento}"
                                     title="Editar informações de pagamento"
                                     style="text-decoration: none"
                                     styleClass="linkAzul texto-size-14 tooltipster"
                                     reRender="modalNovoLotePagamentoEdicaoLancamento, formNovoLotePagamentoEdicaoLancamento"
                                     oncomplete="#{MovContaControle.mensagemNotificar}#{MovContaControle.abrirFecharModalNovoLotePagamentoEdicaoLancamento}">
                        <i class="fa-icon-edit" style="font-size: 16px; margin-top: 2px; margin-left: 17px;"></i>
                        <f:attribute name="movconta" value="#{movConta}"/>
                    </a4j:commandLink>
                    <a4j:commandLink id="removerLancamentoLoteTransferencia"
                                     rendered="#{movConta.elegivelParaLoteDePagamento}"
                                     actionListener="#{MovContaControle.removerItemDoLote}"
                                     title="Remover este lançamento do lote"
                                     style="text-decoration: none"
                                     styleClass="linkAzul texto-size-14 tooltipster"
                                     reRender="formNovoLotePagamento, listaPix, listaBoleto, listaOutros"
                                     oncomplete="#{MovContaControle.mensagemNotificar}">
                        <i class="fa-icon-trash" style="font-size: 16px; margin-left: 5px; margin-top: 1px"></i>
                        <f:attribute name="movconta" value="#{movConta}"/>
                    </a4j:commandLink>
                </rich:column>
            </rich:dataTable>

            <%--BOLETO DE CONSUMO--%>
            <rich:dataTable id="listaBoletoConsumo" width="100%" headerClass="consulta" rowClasses="linhaPar"
                            style="margin-top: 10px;"
                            rendered="#{MovContaControle.exibirListaParaNovoLoteBoletoConsumo}"
                            reRender="paginaAtual, paginaAtualTop,painelPaginacaoTop,painelPaginacao"
                            columnClasses="centralizado, centralizado, centralizado, centralizado, direita, centralizado"
                            value="#{MovContaControle.listaMovContasLoteBoletoConsumoPaginada}" rows="10" var="movConta">

                <!-- descricao -->
                <rich:column style="#{movConta.styleLinhaSelecionada}"
                             filterEvent="onkeyup">
                    <f:facet name="header">
                        <h:outputText styleClass="topoPequeno" value="Descrição"/>
                    </f:facet>
                    <h:panelGroup>
                        <h:outputText value="#{movConta.descricao}"/>
                    </h:panelGroup>
                </rich:column>

                <!-- favorecido -->
                <rich:column style="#{movConta.styleLinhaSelecionada}"
                             filterEvent="onkeyup">
                    <f:facet name="header">
                        <h:outputText styleClass="topoPequeno" value="Favorecido"/>
                    </f:facet>
                    <h:panelGroup>
                        <h:outputText value="#{movConta.pessoaVO.nome}"/>
                    </h:panelGroup>
                </rich:column>

                <!-- vencimento -->
                <rich:column style="#{movConta.styleLinhaSelecionada}"
                             filterEvent="onkeyup">
                    <f:facet name="header">
                        <h:outputText styleClass="topoPequeno" value="Vencimento"/>
                    </f:facet>
                    <h:outputText value="#{movConta.dataVencimento_Apresentar}">
                    </h:outputText>
                </rich:column>

                <!-- valor -->
                <rich:column style="#{movConta.styleLinhaSelecionada}">
                    <f:facet name="header">
                        <h:outputText styleClass="topoPequeno" value="Valor"/>
                    </f:facet>
                    <h:outputText value="#{movConta.valor_Apresentar}" styleClass="#{movConta.mudarCorLinkValor}">
                    </h:outputText>
                </rich:column>

                <!-- elegível -->
                <rich:column style="#{movConta.styleLinhaSelecionada}">
                    <f:facet name="header">
                        <h:outputText styleClass="topoPequeno" value="Elegível"/>
                    </f:facet>
                    <h:panelGroup id="panelElegibilidadeIcon" style="display: block; text-align-last: center;">
                        <h:outputText rendered="#{movConta.elegivelParaLoteDePagamento}"
                                      id="movContaElegivel"
                                      title="Esta conta será incluída no lote de pagamento."
                                      styleClass="fa-icon-ok-sign tooltipster"
                                      style="color: #3cdb5c; font-size: 15px;"/>
                        <h:outputText rendered="#{!movConta.elegivelParaLoteDePagamento}"
                                      id="movContaNaoElegivel"
                                      title="#{movConta.getMovContaNaoElegivelLotePagamentoApresentar()}"
                                      styleClass="fa-icon-remove-sign tooltipster"
                                      style="color: #BC2525; font-size: 15px;"/>
                    </h:panelGroup>
                </rich:column>

                <%--AÇÕES--%>
                <rich:column style="#{movConta.styleLinhaSelecionada}; display: flex">
                    <f:facet name="header">
                        <h:outputText value="Ações"/>
                    </f:facet>
                    <a4j:commandLink id="editarLancamentoLoteBoletoConsumo"
                                     actionListener="#{MovContaControle.abrirModalNovoLotePagamentoEdicaoLancamento}"
                                     title="Editar informações de pagamento"
                                     style="text-decoration: none"
                                     styleClass="linkAzul texto-size-14 tooltipster"
                                     reRender="modalNovoLotePagamentoEdicaoLancamento, formNovoLotePagamentoEdicaoLancamento"
                                     oncomplete="#{MovContaControle.mensagemNotificar}#{MovContaControle.abrirFecharModalNovoLotePagamentoEdicaoLancamento}">
                        <i class="fa-icon-edit" style="font-size: 16px; margin-top: 2px; margin-left: 17px;"></i>
                        <f:attribute name="movconta" value="#{movConta}"/>
                    </a4j:commandLink>
                    <a4j:commandLink id="removerLancamentoLoteBoletoConsumo"
                                     rendered="#{movConta.elegivelParaLoteDePagamento}"
                                     actionListener="#{MovContaControle.removerItemDoLote}"
                                     title="Remover este lançamento do lote"
                                     style="text-decoration: none"
                                     styleClass="linkAzul texto-size-14 tooltipster"
                                     reRender="formNovoLotePagamento, listaPix, listaBoleto, listaOutros"
                                     oncomplete="#{MovContaControle.mensagemNotificar}">
                        <i class="fa-icon-trash" style="font-size: 16px; margin-left: 5px; margin-top: 1px"></i>
                        <f:attribute name="movconta" value="#{movConta}"/>
                    </a4j:commandLink>
                </rich:column>
            </rich:dataTable>


            <%--PAGINAÇÃO--%>
            <h:panelGrid columns="1"
                         style="margin-top: 12px;"
                         width="100%"
                         columnClasses="colunaCentralizada">
                <table width="100%" border="0" cellspacing="0" cellpadding="0">
                    <tr>
                        <td align="center" valign="middle">
                            <h:panelGroup layout="block"
                                          styleClass="paginador-container">
                                <h:panelGroup styleClass="pull-left"
                                              layout="block">
                                    <h:outputText
                                            styleClass="texto-size-14 cinza"
                                            value="Total #{MovContaControle.labelTotalItensPorFormaNovoLote} itens"></h:outputText>
                                </h:panelGroup>
                                <h:panelGroup layout="block"
                                              style="align-items: center;position: absolute;margin-left: 34%;">
                                    <a4j:commandLink styleClass="linkPadrao texto-cor-azul texto-size-20-real"
                                                     reRender="formNovoLotePagamento"
                                                     action="#{MovContaControle.primeiraPagina}">
                                        <i class="fa-icon-double-angle-left"></i>
                                    </a4j:commandLink>

                                    <a4j:commandLink styleClass="linkPadrao texto-cor-azul texto-size-20-real"
                                                     reRender="formNovoLotePagamento"
                                                     action="#{MovContaControle.paginaAnterior}">
                                        <i class="fa-icon-angle-left"></i>
                                    </a4j:commandLink>

                                    <h:outputText styleClass="texto-font texto-cor-cinza texto-size-16-real"
                                                  value="Página #{MovContaControle.labelPaginaAtualExibir} / #{MovContaControle.totalPaginas}"
                                                  rendered="true"/>
                                    <a4j:commandLink
                                            styleClass="linkPadrao texto-font texto-cor-azul texto-size-20-real"
                                            reRender="formNovoLotePagamento"
                                            action="#{MovContaControle.proximaPagina}">
                                        <i class="fa-icon-angle-right"></i>
                                    </a4j:commandLink>

                                    <a4j:commandLink styleClass="linkPadrao texto-cor-azul texto-size-20-real"
                                                     reRender="formNovoLotePagamento"
                                                     action="#{MovContaControle.ultimaPagina}">
                                        <i class="fa-icon-double-angle-right"></i>
                                    </a4j:commandLink>
                                </h:panelGroup>
                            </h:panelGroup>
                        </td>
                    </tr>
                </table>
            </h:panelGrid>

            <h:panelGroup id="btnProsseguirNovoLote" style="display: block">
                <a4j:commandButton
                        title="Prosseguir para criação do lote"
                        reRender="modalNovoLotePagamentoDetalhes, formNovoLotePagamentoDetalhes, panelTablesTotalizadores"
                        id="btnProsseguirDetalhesNovoLote"
                        action="#{MovContaControle.abrirModalNovoLotePagamentoDetalhes}"
                        value="Prosseguir"
                        style="margin: 0; vertical-align: middle; float: right"
                        styleClass="botoes nvoBt tooltipster"
                        oncomplete="#{MovContaControle.mensagemNotificar}#{MovContaControle.abrirFecharModalNovoLotePagamento}#{MovContaControle.abrirFecharModalNovoLotePagamentoDetalhes}"/>
            </h:panelGroup>

        </h:form>
    </rich:modalPanel>
</a4j:outputPanel>

<style>
    .botaoConta:hover {
        /*background: white;*/
        background-color: #bdd6ff;
        color: #094771 !important;
        text-decoration: none;
    }

    .svg-color {
        fill: red;
    }

    .contador-circulo-nao-elegiveis {
        top: -5px;
        right: -5px;
        background-color: #da2128;
        color: white;
        width: 15px;
        height: 14px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 11px;
        font-weight: bold;
        box-shadow: 0px 0px 5px rgba(0, 0, 0, 0.3); /* Sombra para destaque */
        z-index: 10; /* Garante que o círculo fique acima da imagem */
    }

    .contador-circulo-elegiveis {
        top: -5px;
        right: -5px;
        background-color: #3cdb5c;
        color: white;
        width: 15px;
        height: 14px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 11px;
        font-weight: bold;
        box-shadow: 0px 0px 5px rgba(0, 0, 0, 0.3); /* Sombra para destaque */
        z-index: 10; /* Garante que o círculo fique acima da imagem */
    }

    .div-geral-contadores {
        display: flex;
        padding-left: 3px;
        gap: 2px;
        top: -12px;
        position: relative;
    }
</style>
