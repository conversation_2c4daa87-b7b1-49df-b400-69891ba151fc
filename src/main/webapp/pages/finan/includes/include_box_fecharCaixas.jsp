<%@include file="imports.jsp" %>
<rich:modalPanel id="modalFecharCaixas" autosized="true" shadowOpacity="true" width="450" height="380"
	showWhenRendered="#{FuncionalidadeControle.funcionalidadeNome eq 'FECHAR_CAIXA'}"
>
    <f:facet name="header">
        <h:panelGroup>
            <h:outputText  value="Fechamento de Caixa"/>
        </h:panelGroup>
    </f:facet>

    <f:facet name="controls">
        <h:panelGroup>
            <h:graphicImage value="/imagens/close.png" style="cursor:pointer" id="hidelinkFecharCaxixa" />
            <rich:componentControl for="modalFecharCaixas" attachTo="hidelinkFecharCaxixa" operation="hide" event="onclick" />
        </h:panelGroup>
    </f:facet>

    <a4j:form id="formModalFecharCaixa" style="height: 100%;">


			<h:outputText value="#{msg_aplic.prt_finan_maisdeumcaixa}" styleClass="tituloCampos" ></h:outputText>
			<br/>
			<br/>

			<rich:dataTable value="#{CaixaControle.caixasEmAberto}" var="caixa"
							rows="10"
							id="listaCxFechar"
							width="100%" columnClasses="centralizado">

				<rich:column>
					<f:facet name="header">
						<h:outputText value="C�digo"></h:outputText>
					</f:facet>
					<h:outputText value="#{caixa.codigo}"></h:outputText>
				</rich:column>
				<rich:column>
					<f:facet name="header">
						<h:outputText value="Data de Abertura"></h:outputText>
					</f:facet>
					<h:outputText value="#{caixa.dataAbertura}">
						<f:convertDateTime pattern="dd/MM/yyyy HH:mm"/>
					</h:outputText>
				</rich:column>
				<rich:column>
					<f:facet name="header">
					</f:facet>
					<a4j:commandLink id="btnfechar" value="Fechar" action="#{CaixaControle.selecionarCaixaParaFechamento}" oncomplete="#{CaixaControle.msgAlert}">
					</a4j:commandLink>

				</rich:column>
			</rich:dataTable>
		<h:panelGrid columns="1" columnClasses="colunaCentralizada" width="100%" >
			<h:panelGroup layout="block">
			<rich:datascroller align="center"
							   for="listaCxFechar" maxPages="100"
							   id="sclistaCxFechar" />
			</h:panelGroup>
		</h:panelGrid>


	</a4j:form>
</rich:modalPanel>
