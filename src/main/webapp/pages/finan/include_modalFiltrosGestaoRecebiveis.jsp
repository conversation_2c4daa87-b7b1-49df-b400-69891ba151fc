<%@page contentType="text/html"%>
<%@page pageEncoding="ISO-8859-1"%>
<%@ taglib prefix="r" uri="http://mojarra.dev.java.net/mojarra_ext" %>
<%@ taglib prefix="rick" uri="http://java.sun.com/jsf/core" %>
<%@include file="includes/imports.jsp" %>
<rich:modalPanel id="modalPanelFiltrosGestaoRecebiveis" trimOverlayedElements="false" autosized="false"
                 shadowOpacity="true" width="974" height="620"
                 styleClass="novaModal">
    <f:facet name="header">
        <h:panelGroup>
            <h:outputText value="Filtros Consulta de #{GestaoRecebiveisControle.visaoConciliacao ? 'Conciliação' : 'Gestão de Recebíveis'}"/>
        </h:panelGroup>
    </f:facet>
    <f:facet name="controls">
        <h:panelGroup>
            <h:outputText
                    styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign btnSelecionarModalidade"
                    id="hidelink1"/>
            <rich:componentControl for="modalPanelFiltrosGestaoRecebiveis"
                                   attachTo="hidelink1" operation="hide" event="onclick"/>
        </h:panelGroup>
    </f:facet>
    <a4j:form id="formFiltrosGestaoRecebiveis" ajaxSubmit="true">
        <h:panelGroup id="botoesTL"
                      style="display: inline-flex; padding-bottom: 10px; border-bottom:#E5E5E5 1px solid; width: 100%;"
                      layout="block">
            <a4j:commandLink id="filtrosPrincipais"
                             status="false" styleClass="botaoModoTimeLine ativo filtrosPrincipais"
                             onclick="trocarBloco('.filtrosPrincipais', '.painelFiltros', '.botaoModoTimeLine');">
                <h:outputText value="Filtros principais"/>
            </a4j:commandLink>
            <a4j:commandLink id="abaEmpresa"
                             status="false" styleClass="botaoModoTimeLine empresapainelfiltro"
                             rendered="#{!GestaoRecebiveisControle.visaoConciliacao}"
                             onclick="trocarBloco('.empresapainelfiltro', '.painelFiltros', '.botaoModoTimeLine');">
                <h:outputText value="Empresas"/>
            </a4j:commandLink>
            <a4j:commandLink id="filtrosLote"
                             status="false" styleClass="botaoModoTimeLine filtrosLote"
                             rendered="#{!GestaoRecebiveisControle.visaoConciliacao}"
                             onclick="trocarBloco('.filtrosLote', '.painelFiltros', '.botaoModoTimeLine');">
                <h:outputText value="Movimentação"/>
            </a4j:commandLink>
            <a4j:commandLink id="cpfNomeMatricula"
                             status="false" styleClass="botaoModoTimeLine cpfnomematricula"
                             rendered="#{!GestaoRecebiveisControle.visaoConciliacao}"
                             onclick="trocarBloco('.cpfnomematricula', '.painelFiltros', '.botaoModoTimeLine');">
                <h:outputText value="#{GestaoRecebiveisControle.displayIdentificadorFront[0]}, Nome e Matrícula"/>
            </a4j:commandLink>
            <a4j:commandLink id="cheque"
                             status="false" styleClass="botaoModoTimeLine chequepainelfiltro"
                             rendered="#{!GestaoRecebiveisControle.visaoConciliacao}"
                             onclick="trocarBloco('.chequepainelfiltro', '.painelFiltros', '.botaoModoTimeLine');">
                <h:outputText value="Cheque"/>
            </a4j:commandLink>
            <a4j:commandLink id="cartao"
                             status="false" styleClass="botaoModoTimeLine cartaopainelfiltro"
                             onclick="trocarBloco('.cartaopainelfiltro', '.painelFiltros', '.botaoModoTimeLine');">
                <h:outputText value="Cartão"/>
            </a4j:commandLink>
            <a4j:commandLink id="boleto"
                             rendered="#{!GestaoRecebiveisControle.visaoConciliacao}"
                             status="false" styleClass="botaoModoTimeLine boletopainelfiltro"
                             onclick="trocarBloco('.boletopainelfiltro', '.painelFiltros', '.botaoModoTimeLine');">
                <h:outputText value="Boleto"/>
            </a4j:commandLink>
            <a4j:commandLink id="centroCustos"
                             status="false" styleClass="botaoModoTimeLine rateiopainelfiltro"
                             rendered="#{!GestaoRecebiveisControle.visaoConciliacao}"
                             onclick="trocarBloco('.rateiopainelfiltro', '.painelFiltros', '.botaoModoTimeLine');">
                <h:outputText value="Centro de custos"/>
            </a4j:commandLink>
        </h:panelGroup>

<%--        Corpo filtro Empresa--%>
        <h:panelGroup styleClass="painelFiltros empresapainelfiltro" layout="block" style="width: 100%; display:none;">
            <div style="width: 100%; padding: 0 15px;">
                <h:panelGroup id="containerTituloEmpresas">
                    <div class="block" style="border-bottom: #E5E5E5 1px solid; margin-right: 15px; padding-bottom: 5px;">
                        <h:outputText styleClass="tituloCampos upper flex" value="Empresa" style="padding-bottom: 5px;"/>
                        <h:selectBooleanCheckbox rendered="#{GestaoRecebiveisControle.possuiPermissaoConsultarTodasEmpresas}" id="checkboxTodasEmpresas" value="#{GestaoRecebiveisControle.checkboxTodasEmpresasSelecionado}">
                            <a4j:support action="#{GestaoRecebiveisControle.selecionarTodasEmpresas}"
                                         event="onchange" reRender="containerListaEmpresas"/>
                        </h:selectBooleanCheckbox>
                        <h:outputText rendered="#{GestaoRecebiveisControle.possuiPermissaoConsultarTodasEmpresas}"
                                      styleClass="tituloCampos upper flex inline"
                                      style="font-weight: normal; margin-left: 3px;"
                                      value="Todas as Empresas"/>
                    </div>
                </h:panelGroup>
                <h:panelGroup id="containerListaEmpresas">
                    <div class="block" style="margin-top: 5px;">
                        <h:selectManyCheckbox styleClass="tituloCampos" layout="pageDirection" style="text-align: left; font-weight: normal;"
                                              value="#{GestaoRecebiveisControle.empresasSelecionadas}">
                            <f:selectItems value="#{GestaoRecebiveisControle.listaSelectItemsEmpresa}"/>
                            <a4j:support action="#{GestaoRecebiveisControle.selecionarCheckboxEmpresa}" event="onchange" reRender="containerTituloEmpresas"/>
                        </h:selectManyCheckbox>
                    </div>
                </h:panelGroup>
                <h:outputText styleClass="tituloCampos flex"
                              rendered="#{GestaoRecebiveisControle.possuiPermissaoConsultarTodasEmpresas}"
                              value="Obs: Seleção de várias Empresas apenas para visualização. As opções de Movimentação serão desabilitadas ao selecionar mais de uma Empresa."
                              style="text-align: left; font-weight: normal; margin-top: 20px;"/>
            </div>
        </h:panelGroup>

        <h:panelGroup styleClass="painelFiltros rateiopainelfiltro" layout="block" style="width: 100%; display:none;">
            <div style="width: 100%; padding: 0 15px;">
                <div class="block">
                    <h:outputText styleClass="tituloCampos upper flex" value="CENTRO DE CUSTOS"/>
                    <div class="block cb-container">
                        <h:selectOneMenu id="centroCusto"
                                         value="#{GestaoRecebiveisControle.centroCustoTO.codigo}" onblur="blurinput(this);"
                                         onfocus="focusinput(this);" styleClass="form">
                            <f:selectItem itemLabel="" itemValue="0"/>
                            <f:selectItems value="#{GestaoRecebiveisControle.centrosCusto}"/>
                        </h:selectOneMenu>
                    </div>
                </div>
            </div>
        </h:panelGroup>
        <h:panelGroup styleClass="painelFiltros cartaopainelfiltro" layout="block" style="width: 100%; display:none;">
            <div style="width: 100%; padding: 0 15px;">
                <div class="block">
                    <h:outputText styleClass="tituloCampos upper flex"
                                  value="#{msg_aplic.prt_Adquirente_tituloForm}"/>
                    <div class="block cb-container">
                        <h:selectOneMenu id="adquirentes"
                                         value="#{GestaoRecebiveisControle.adquirente}" onblur="blurinput(this);"
                                         onfocus="focusinput(this);" styleClass="form">
                            <f:selectItem itemLabel="" itemValue="0" />
                            <f:selectItem itemLabel="SEM ADQUIRENTE" itemValue="9999" />
                            <f:selectItems value="#{GestaoRecebiveisControle.adquirentes}"/>
                        </h:selectOneMenu>
                    </div>
                </div>
                <div class="block">
                    <h:outputText styleClass="tituloCampos upper flex"
                                  value="#{msg_aplic.prt_Finan_GestaoRecebiveis_operadora}"/>
                    <div class="block cb-container">
                        <h:selectOneMenu id="operadora"
                                         value="#{GestaoRecebiveisControle.operadoraCartao}" onblur="blurinput(this);"
                                         onfocus="focusinput(this);" styleClass="form">
                            <f:selectItem itemLabel="" itemValue="0"/>
                            <f:selectItems value="#{GestaoRecebiveisControle.operadorasCartao}"/>
                        </h:selectOneMenu>
                    </div>
                </div>
                <div class="block">
                    <h:outputText id="codigoAutorizacaoExtratolbl" styleClass="tituloCampos upper flex"
                                  value="#{msg_aplic.prt_Finan_GestaoRecebiveis_codigoAutorizacao}"/>

                    <h:inputText id="codigoAutorizacaoExtrato" value="#{GestaoRecebiveisControle.codAutorizacaoedi}" rendered="#{GestaoRecebiveisControle.visaoConciliacao}"
                                 onblur="blurinput(this);" size="20" maxlength="20"
                                 onfocus="focusinput(this);" styleClass="form"/>
                    <h:inputText id="codigoAutorizacaoRecebiveis" value="#{GestaoRecebiveisControle.codAutorizacao}" rendered="#{!GestaoRecebiveisControle.visaoConciliacao}"
                                 onblur="blurinput(this);" size="20" maxlength="20"
                                 onfocus="focusinput(this);" styleClass="form"/>
                </div>

                <div class="block">
                    <h:outputText styleClass="tituloCampos upper flex"
                                  value="#{msg_aplic.prt_Finan_GestaoRecebiveis_nsu}"/>
                    <h:inputText id="nsu" value="#{GestaoRecebiveisControle.nsu}"
                                 onblur="blurinput(this);" size="20" maxlength="20"
                                 onfocus="focusinput(this);" styleClass="form"/>
                </div>
            </div>
        </h:panelGroup>
        <h:panelGroup styleClass="painelFiltros boletopainelfiltro" layout="block" style="width: 100%; display:none;">
            <div style="width: 100%; padding: 0 15px;">
                <div class="block">
                    <h:outputText styleClass="tituloCampos upper flex"
                                  value="#{msg_aplic.prt_Finan_GestaoRecebiveis_numeroDocumento}"/>
                    <h:inputText id="numeroDocumento" value="#{GestaoRecebiveisControle.numeroDocumento}"
                                 onblur="blurinput(this);" size="20" maxlength="50"
                                 onfocus="focusinput(this);" styleClass="form"/>
                </div>
            </div>
        </h:panelGroup>
        <h:panelGroup styleClass="painelFiltros chequepainelfiltro" layout="block" style="width: 100%; display:none;">
            <div style="width: 100%; padding: 0 15px;">
                <div class="block">
                    <h:outputText styleClass="tituloCampos upper flex"
                                  value="#{msg_aplic.prt_Finan_GestaoRecebiveis_banco}"/>
                    <div class="block cb-container">
                        <h:selectOneMenu id="bancoPreenchido"
                                         value="#{GestaoRecebiveisControle.chequeVO.banco.codigo}"
                                         onblur="blurinput(this);"
                                         onfocus="focusinput(this);" styleClass="form">
                            <f:selectItems
                                    value="#{GestaoRecebiveisControle.listaSelectItemBanco}"/>
                            <a4j:support event="onchange" focus="bancoPreenchido"
                                         action="#{GestaoRecebiveisControle.consultarBancoPorCodigo}"
                                         reRender="panelChequePreenchido"/>
                        </h:selectOneMenu>
                    </div>
                </div>
                <div class="block">
                    <div class="inlineBlock" style="margin-right: 10px;">
                        <h:outputText styleClass="tituloCampos upper flex"
                                      value="#{msg_aplic.prt_Finan_GestaoRecebiveis_agencia}"/>
                        <h:inputText id="agencia" value="#{GestaoRecebiveisControle.chequeVO.agencia}"
                                     onblur="blurinput(this);" size="7" maxlength="5"
                                     onfocus="focusinput(this);" styleClass="form"/>
                    </div>
                    <div class="inlineBlock">
                        <h:outputText styleClass="tituloCampos upper flex"
                                      value="#{msg_aplic.prt_Finan_GestaoRecebiveis_conta}"/>
                        <h:inputText id="conta" value="#{GestaoRecebiveisControle.chequeVO.conta}"
                                     onblur="blurinput(this);" size="30" maxlength="20"
                                     onfocus="focusinput(this);" styleClass="form"/>
                    </div>
                </div>
                <div class="block">
                    <h:outputText styleClass="tituloCampos upper flex"
                                  value="#{msg_aplic.prt_Finan_GestaoRecebiveis_nCheque}"/>
                    <h:inputText id="nDoc" value="#{GestaoRecebiveisControle.chequeVO.numero}"
                                 onblur="blurinput(this);" size="15" maxlength="15"
                                 onfocus="focusinput(this);" styleClass="form"/>
                </div>
                <div class="block">
                    <h:outputText styleClass="tituloCampos upper flex"
                                  value="#{msg_aplic.prt_Finan_GestaoRecebiveis_nomeTerceiro}:"/>
                    <h:inputText id="nomeTerceiro" size="40" maxlength="255" onblur="blurinput(this);"
                                 onfocus="focusinput(this);" styleClass="form"
                                 value="#{GestaoRecebiveisControle.nomeTerceiro}"/>

                </div>
                <div class="block" style="margin-top: 10px;">
                    <div class="chk-fa-container inline ">
                        <h:selectBooleanCheckbox id="aVista" value="#{GestaoRecebiveisControle.chequeAvista}"/>
                        <span/>
                    </div>
                    <h:outputText styleClass="tituloCampos upper flex inline"
                                  style="font-weight: normal;"
                                  value="à vista"/>

                    <div class="chk-fa-container inline " style="margin-left: 10px;">
                        <h:selectBooleanCheckbox id="aPrazo" value="#{GestaoRecebiveisControle.chequeAprazo}"/>
                        <span/>
                    </div>
                    <h:outputText styleClass="tituloCampos upper flex inline"
                                  style="font-weight: normal;"
                                  value="A prazo"/>
                </div>
                <h:panelGroup layout="block"
                              rendered="#{!GestaoRecebiveisControle.visaoConciliacao}"
                              styleClass="block" style="border-bottom:#E5E5E5 1px solid; width: 100%;">
                    <div class="inlineBlock" style="padding: 10px 10px 10px 0px;">
                        <div class="chk-fa-container inline ">
                             <input id="mostrarCanceladosCheque" type="checkbox" name="mostrarCancelados" onclick="setarMostrarCancelados(this);"/>
                            <span/>
                        </div>
                        <h:outputText styleClass="tituloCampos upper" value="Mostrar cancelados/devolvidos"
                                      style="font-weight: normal;"/>
                    </div>
                    
                </h:panelGroup>
            </div>
        </h:panelGroup>
        <h:panelGroup styleClass="painelFiltros cpfnomematricula" layout="block" style="width: 100%; display:none;">
            <div style="width: 100%; padding: 0 15px;">
                <div class="block">
                    <c:if test="${!GestaoRecebiveisControle.configuracaoSistema.usarSistemaInternacional}">
                        <h:outputText styleClass="tituloCampos upper flex"
                                      value="#{GestaoRecebiveisControle.displayIdentificadorFront[0]}"/>
                        <h:inputText id="cpf" size="30" maxlength="14" onblur="blurinput(this);"
                                     onkeypress="return mascara(this.form, this.id, '999.999.999-99', event);"
                                     onfocus="focusinput(this);" styleClass="form"
                                     value="#{GestaoRecebiveisControle.cpf}"/>
                    </c:if>

                    <c:if test="${GestaoRecebiveisControle.configuracaoSistema.usarSistemaInternacional}">
                        <h:outputText styleClass="tituloCampos upper flex"
                                      value="#{GestaoRecebiveisControle.displayIdentificadorFront[0]}"/>
                        <h:inputText id="cpf" size="30" maxlength="14" onblur="blurinput(this);"
                                     onfocus="focusinput(this);" styleClass="form"
                                     value="#{GestaoRecebiveisControle.cpf}"/>
                    </c:if>
                </div>
                <div class="block">
                    <h:outputText styleClass="tituloCampos upper flex"
                                  value="#{msg_aplic.prt_Finan_GestaoRecebiveis_nomeClienteContrato}"/>
                    <h:inputText id="nomeCliente" size="90" maxlength="255" onblur="blurinput(this);"
                                 onfocus="focusinput(this);" styleClass="form"
                                 value="#{GestaoRecebiveisControle.nomeClienteContrato}"/>
                </div>
                <div class="block">
                    <h:outputText styleClass="tituloCampos upper flex"
                                  value="#{msg_aplic.prt_RelatorioCliente_Matricula}"/>
                    <h:inputText id="matricula" size="30" maxlength="14" onblur="blurinput(this);"
                                 onkeypress="return mascara(this.form, this.id, '999999999999', event);"
                                 onfocus="focusinput(this);" styleClass="form"
                                 value="#{GestaoRecebiveisControle.matricula}"/>
                </div>
                 <div style="padding-top: 5px;">
                    <h:outputText
                        value="* Estes filtros se referem ao dono dos produtos, tem o intuito de apresentar todos os pagamentos de produtos de uma pessoa, podendo ser um pagamento de terceiro."
                        styleClass="classInfCadUsuario"/>
                </div>
            </div>
        </h:panelGroup>
        <h:panelGroup styleClass="painelFiltros filtrosLote" layout="block" style="width: 100%;display:none;"
                      id="painelfiltroslotes">
            <div style="width: 100%; padding: 0 15px;">
                <div class="block">
                    <h:outputText styleClass="tituloCampos upper flex" value="STATUS DE MOVIMENTAÇÃO"/>
                    <div class="block cb-container">
                        <h:selectOneMenu id="statusMov" value="#{GestaoRecebiveisControle.statusMovimentacao}"
                                         title="Recebíveis movimentados ou não movimentados">
                            <f:selectItems value="#{GestaoRecebiveisControle.listaStatusItens}"/>
                            <a4j:support event="onchange" reRender="formFiltrosGestaoRecebiveis" oncomplete="trocarBloco('.filtrosLote', '.painelFiltros', '.botaoModoTimeLine');"/>
                        </h:selectOneMenu>
                    </div>
                </div>
                <c:if test="${GestaoRecebiveisControle.statusMovimentacao eq 'MOVIMENTADOS'}">
                    <div class="block">
                        <h:outputText styleClass="tituloCampos upper flex"
                                      value="Data Movimentação"/>
                        <h:panelGroup styleClass="flex" layout="block">
                            <h:panelGroup styleClass="dateTimeCustom" style="font-size: 11px;" >
                                <rich:calendar id="dataInicioCa"
                                               value="#{GestaoRecebiveisControle.dataInicioMovimentacaoInicio}"
                                               inputSize="10"
                                               buttonIcon="/imagens_flat/calendar-button.svg"
                                               inputClass="form"
                                               oninputblur="blurinput(this);"
                                               oninputfocus="focusinput(this);"
                                               datePattern="dd/MM/yyyy"
                                               oninputchange="return validar_Data(this.id);"
                                               oninputkeypress="return mascara2(this.form, this.id, '99/99/9999', event);"
                                               enableManualInput="true"
                                               zindex="2"
                                               showWeeksBar="false"/>
                            </h:panelGroup>

                            <h:inputText id="horaInicialCa" size="8" maxlength="8" style="margin: 0 10px;font-size: 11px;"
                                         onkeypress="return mascaraTodos(this.form, 'formFiltrosGestaoRecebiveis:horaInicialCa', '99:99:99', event);"
                                         onblur="blurinput(this);" onfocus="focusinput(this);"
                                         styleClass="form"
                                         value="#{GestaoRecebiveisControle.horaInicioMovimentacaoInicio}"/>


                            <h:outputText styleClass="tituloCampos"
                                          style="margin: 7px 9px 0 0px; font-size: 11px;"
                                          value="#{msg_aplic.prt_ate}"/>


                            <h:panelGroup styleClass="dateTimeCustom"  style="font-size: 11px;">
                                <rich:calendar id="dataTerminoCa"
                                               value="#{GestaoRecebiveisControle.dataInicioMovimentacaoFim}"
                                               inputSize="10"
                                               inputClass="form"
                                               oninputblur="blurinput(this);"
                                               buttonIcon="/imagens_flat/calendar-button.svg"
                                               oninputfocus="focusinput(this);"
                                               oninputchange="return validar_Data(this.id);"
                                               oninputkeypress="return mascara2(this.form, this.id, '99/99/9999', event);"
                                               datePattern="dd/MM/yyyy"
                                               enableManualInput="true" showWeeksBar="false"
                                               zindex="2"/>
                            </h:panelGroup>

                            <h:inputText id="horaFinalCa" size="8" maxlength="8" style="margin: 0 10px; font-size: 11px;"
                                         onkeypress="return mascaraTodos(this.form, 'formFiltrosGestaoRecebiveis:horaFinalCa', '99:99:99', event);"
                                         onblur="blurinput(this);" onfocus="focusinput(this);" styleClass="form"
                                         value="#{GestaoRecebiveisControle.horaInicioMovimentacaoFim}"/>

                            <a4j:commandLink id="limparPeriodoMovimentacao"
                                             style="position:relative; top:7px; left:5px; font-size: 11px;"
                                             onclick="document.getElementById('formFiltrosGestaoRecebiveis:dataInicioCaInputDate').value='';
                                         document.getElementById('formFiltrosGestaoRecebiveis:dataTerminoCaInputDate').value='';
                                         document.getElementById('formFiltrosGestaoRecebiveis:horaInicialCa').value='';
                                         document.getElementById('formFiltrosGestaoRecebiveis:horaFinalCa').value='';"
                                             title="Limpar período de movimentação.">
                                <i class="fa-icon-eraser"></i>
                            </a4j:commandLink>

                        </h:panelGroup>

                    </div>
                </c:if>
                <div class="block">
                    <h:outputText styleClass="tituloCampos upper flex" value="Nome da conta"/>
                    <h:inputText id="nomeConta" size="90" maxlength="255" onblur="blurinput(this);"
                                 onfocus="focusinput(this);" styleClass="form"
                                 value="#{GestaoRecebiveisControle.nomeConta}"
                    />
                </div>
                <div class="block" style="margin-top: 10px;">
                    <div class="chk-fa-container inline ">
                        <h:selectBooleanCheckbox id="recebiveisComLote"
                                                 value="#{GestaoRecebiveisControle.pesquisarRecebiveisComLote}">
                            <a4j:support action="#{GestaoRecebiveisControle.validarRecebiveisMarcadoComLote}"
                                         event="onchange" reRender="recebiveisComLote,recebiveisSemLote"/>
                        </h:selectBooleanCheckbox>
                        <span/>
                    </div>

                    <h:outputText styleClass="tituloCampos upper" style="font-weight: normal; margin-right: 10px;"
                                  value="#{msg_aplic.prt_Finan_Lancamentos_incluirRecebiveisComLote}"/>
                    <div class="chk-fa-container inline ">
                        <h:selectBooleanCheckbox id="recebiveisSemLote"
                                                 value="#{GestaoRecebiveisControle.pesquisarRecebiveisSemLote}">
                            <a4j:support action="#{GestaoRecebiveisControle.validarRecebiveisMarcadoSemLote}"
                                         event="onchange" reRender="recebiveisComLote,recebiveisSemLote"/>
                        </h:selectBooleanCheckbox>
                        <span/>
                    </div>
                    <h:outputText styleClass="tituloCampos upper"
                                  value="#{msg_aplic.prt_Finan_Lancamentos_incluirRecebiveisSemLote}"
                                  style="font-weight: normal;"/>
                </div>
                <div class="block" style="margin-top: 10px;">
                    <h:outputText styleClass="tituloCampos upper flex" value="Lote"/>
                    <h:inputText id="codigoLote" size="8" maxlength="255" title="Entre com os números de lote que deseja consultar Ex. 50,51,52"
                                 onblur="blurinput(this);" onfocus="focusinput(this);" styleClass="tituloCampos tooltipster upper"
                                 value="#{GestaoRecebiveisControle.codigoLoteFiltro}">
                    </h:inputText>
                </div>

            </div>
        </h:panelGroup>
        <h:panelGroup styleClass="painelFiltros filtrosPrincipais visivel" id="painelfiltrosinsidemodal" style="width: 100%; min-height: 50vh; ">
            <div style="width: 100%; padding: 0 15px;  min-height: 50vh;">
<%--                Esse select da Empresa, foi substituido pela Aba Empresa, onde o cliente pode selecionar mais de uma unidade--%>
<%--                <h:outputText styleClass="tituloCampos  upper flex" value="#{msg_aplic.prt_Finan_Lancamentos_empresa}"--%>
<%--                              rendered="#{GestaoRecebiveisControle.mostrarCampoEmpresa}"/>--%>
<%--                <div class="cb-container">--%>
<%--                    <h:selectOneMenu id="empresa"--%>
<%--                                     onfocus="focusinput(this);"--%>
<%--                                     rendered="#{GestaoRecebiveisControle.mostrarCampoEmpresa}"--%>
<%--                                     value="#{GestaoRecebiveisControle.empresaRel.codigo}">--%>
<%--                        <f:selectItems value="#{GestaoRecebiveisControle.listaSelectItemEmpresa}"/>--%>
<%--                        <a4j:support action="#{GestaoRecebiveisControle.obterEmpresaEscolhida}" event="onchange"/>--%>
<%--                    </h:selectOneMenu>--%>
<%--                </div>--%>

                <h:outputText styleClass="tituloCampos  upper flex" value="Tipo de conciliação"
                              rendered="#{GestaoRecebiveisControle.visaoConciliacao}"/>
                <h:panelGroup styleClass="cb-container" rendered="#{GestaoRecebiveisControle.visaoConciliacao}"
                              layout="block">
                    <h:selectOneMenu id="tipoconciliacao"
                                     value="#{GestaoRecebiveisControle.tipoConciliacao}">
                        <f:selectItems value="#{GestaoRecebiveisControle.tiposConcilacao}"/>
                    </h:selectOneMenu>
                </h:panelGroup>

                <h:panelGroup layout="block" styleClass="flex" rendered="#{GestaoRecebiveisControle.visaoConciliacao}">
                    <div class="inline" style="margin-right: 10px;">
                        <h:outputText styleClass="tituloCampos  upper flex" value="Formas de pagamento"
                                      />
                        <h:panelGroup styleClass="cb-container"
                                      layout="block">
                            <h:selectOneMenu id="tipofp"
                                             value="#{GestaoRecebiveisControle.fpSelecionada}">
                                <f:selectItems value="#{GestaoRecebiveisControle.formasPagamentoSI}"/>
                                <a4j:support reRender="painelfiltrosinsidemodal" event="onchange"/>
                            </h:selectOneMenu>
                        </h:panelGroup>
                    </div>

                    <div  class="inline">
                        <h:outputText styleClass="tituloCampos  upper flex" value="Convênio de cobrança"/>
                        <h:panelGroup styleClass="cb-container"
                                      layout="block">
                            <h:selectOneMenu id="tipoconvenio"
                                             value="#{GestaoRecebiveisControle.codigoConvenio}">
                                <f:selectItems value="#{GestaoRecebiveisControle.tiposConvenio}"/>
                                <a4j:support reRender="painelfiltrosinsidemodal" event="onchange"/>
                            </h:selectOneMenu>
                        </h:panelGroup>
                    </div>
                </h:panelGroup>


                <div style="padding-bottom: 10px; border-bottom:#E5E5E5 1px solid; width: 100%;">
                    <h:outputText styleClass="tituloCampos upper flex"
                                  rendered="#{!GestaoRecebiveisControle.visaoConciliacao}"
                                  value="#{msg_aplic.prt_Finan_GestaoRecebiveis_faturamentoRecebido}"/>
                        <h:panelGroup styleClass="flex" layout="block"
                             rendered="#{!GestaoRecebiveisControle.visaoConciliacao}">
                                <h:panelGroup styleClass="dateTimeCustom"  style="font-size: 11px !important;">
                                    <rich:calendar id="dataInicioL"
                                                   value="#{GestaoRecebiveisControle.dataInicialLancamento}"
                                                   inputSize="10"
                                                   inputClass="form"
                                                   buttonIcon="/imagens_flat/calendar-button.svg"
                                                   oninputblur="blurinput(this);"
                                                   oninputfocus="focusinput(this);"
                                                   oninputchange="return validar_Data(this.id);"
                                                   datePattern="dd/MM/yyyy"
                                                   enableManualInput="true"
                                                   zindex="2"
                                                   showWeeksBar="false"/>
                                </h:panelGroup>
                                <h:inputText id="horaInicialL" size="8" maxlength="8" style="margin: 0 10px; font-size: 12px !important;"
                                             onkeypress="return mascaraTodos(this.form, 'formFiltrosGestaoRecebiveis:horaInicialL', '99:99:99', event);"
                                             onblur="blurinput(this);" onfocus="focusinput(this);" styleClass="form"
                                             value="#{GestaoRecebiveisControle.horaInicialLancamento}"/>

                        <h:outputText styleClass="tituloCampos"
                                              style="margin: 7px 9px 0 0px;  font-size: 11px !important;"
                                              value="#{msg_aplic.prt_ate}"/>

                        <h:panelGroup styleClass="dateTimeCustom"  style="font-size: 11px;">
                                <rich:calendar id="dataTerminoL"
                                               value="#{GestaoRecebiveisControle.dataFinalLancamento}"
                                               inputSize="10"
                                               buttonIcon="/imagens_flat/calendar-button.svg"
                                               inputClass="form"
                                               oninputblur="blurinput(this);"
                                               oninputfocus="focusinput(this);"
                                               oninputkeypress="return mascara2(this.form, this.id, '99/99/9999', event);"
                                               oninputchange="return validar_Data(this.id);"
                                               datePattern="dd/MM/yyyy"
                                               enableManualInput="true"
                                               zindex="2"
                                               showWeeksBar="false"/>
                                </h:panelGroup>

                        <h:inputText id="horaFinalL" size="8" maxlength="8" style="margin: 0 10px; font-size: 12px !important;"
                                             onkeypress="return mascaraTodos(this.form, 'formFiltrosGestaoRecebiveis:horaFinalL', '99:99:99', event);"
                                             onblur="blurinput(this);" onfocus="focusinput(this);" styleClass="form"
                                             value="#{GestaoRecebiveisControle.horaFinalLancamento}"/>

                                <div class="cb-container" style=" font-size: 11px !important;">
                                    <h:selectOneMenu id="periodoLancamentos"
                                                     onfocus="focusinput(this);"
                                                     value="#{GestaoRecebiveisControle.periodoLancamento}">
                                        <f:selectItems value="#{GestaoRecebiveisControle.listaPeriodos}"/>
                                        <a4j:support action="#{GestaoRecebiveisControle.alterarPeriodoLancamento}"
                                                     event="onchange"
                                                     reRender="dataInicioL,dataTerminoL,horaInicialL,horaFinalL,containerFuncMask"/>
                                    </h:selectOneMenu>
                                </div>
                                <a4j:commandLink id="limparPeriodoLancamento"
                                                 style="position:relative; top:7px; left:5px; font-size: 11px;"
                                                 onclick="document.getElementById('formFiltrosGestaoRecebiveis:dataInicioLInputDate').value='';
                                         document.getElementById('formFiltrosGestaoRecebiveis:dataTerminoLInputDate').value='';
                                         document.getElementById('formFiltrosGestaoRecebiveis:horaInicialL').value='';
                                         document.getElementById('formFiltrosGestaoRecebiveis:horaFinalL').value='';"
                                                 title="Limpar período de faturamento." status="false">
                                    <i class="fa-icon-eraser"></i>
                                </a4j:commandLink>

                        </h:panelGroup>

                    <h:outputText styleClass="tituloCampos upper flex"
                                  rendered="#{!GestaoRecebiveisControle.visaoConciliacao}"
                                  value="#{msg_aplic.prt_Finan_GestaoRecebiveis_periodoCompensacao}"/>
                    <h:panelGroup styleClass="flex" layout="block"
                                  rendered="#{!GestaoRecebiveisControle.visaoConciliacao}">
                        <h:panelGroup styleClass="dateTimeCustom" style="font-size: 11px;" >
                                <rich:calendar id="dataInicioC"
                                           value="#{GestaoRecebiveisControle.dataInicialCompensacao}"
                                           inputSize="10"
                                           buttonIcon="/imagens_flat/calendar-button.svg"
                                           inputClass="form"
                                           oninputblur="blurinput(this);"
                                           oninputfocus="focusinput(this);"
                                           datePattern="dd/MM/yyyy"
                                           oninputchange="return validar_Data(this.id);"
                                           oninputkeypress="return mascara2(this.form, this.id, '99/99/9999', event);"
                                           enableManualInput="true"
                                           zindex="2"
                                           showWeeksBar="false"/>
                                </h:panelGroup>

                            <h:inputText id="horaInicialC" size="8" maxlength="8" style="margin: 0 10px;font-size: 11px;"
                                             onkeypress="return mascaraTodos(this.form, 'formFiltrosGestaoRecebiveis:horaInicialC', '99:99:99', event);"
                                             onblur="blurinput(this);" onfocus="focusinput(this);"
                                             styleClass="form"
                                             value="#{GestaoRecebiveisControle.horaInicialCompensacao}"/>


                                    <h:outputText styleClass="tituloCampos"
                                              style="margin: 7px 9px 0 0px; font-size: 11px;"
                                              value="#{msg_aplic.prt_ate}"/>


                                <h:panelGroup styleClass="dateTimeCustom"  style="font-size: 11px;">
                                <rich:calendar id="dataTerminoC"
                                               value="#{GestaoRecebiveisControle.dataFinalCompensacao}"
                                               inputSize="10"
                                               inputClass="form"
                                               oninputblur="blurinput(this);"
                                               buttonIcon="/imagens_flat/calendar-button.svg"
                                               oninputfocus="focusinput(this);"
                                               oninputchange="return validar_Data(this.id);"
                                               oninputkeypress="return mascara2(this.form, this.id, '99/99/9999', event);"
                                               datePattern="dd/MM/yyyy"
                                               enableManualInput="true" showWeeksBar="false"
                                               zindex="2"/>
                                </h:panelGroup>

                            <h:inputText id="horaFinalC" size="8" maxlength="8" style="margin: 0 10px; font-size: 11px;"
                                             onkeypress="return mascaraTodos(this.form, 'formFiltrosGestaoRecebiveis:horaFinalC', '99:99:99', event);"
                                             onblur="blurinput(this);" onfocus="focusinput(this);" styleClass="form"
                                             value="#{GestaoRecebiveisControle.horaFinalCompensacao}"/>

                            <div class="cb-container" style="font-size: 11px;">
                                <h:selectOneMenu id="periodoPesquisa"
                                                 onfocus="focusinput(this);"
                                                 value="#{GestaoRecebiveisControle.periodoCompensacao}">
                                    <f:selectItems value="#{GestaoRecebiveisControle.listaPeriodos}"/>
                                    <a4j:support action="#{GestaoRecebiveisControle.alterarPeriodoCompensacao}"
                                                 event="onchange" reRender="dataTerminoC,dataInicioC,horaInicialC,horaFinalC,containerFuncMask"/>
                                </h:selectOneMenu>
                            </div>

                                <a4j:commandLink id="limparPeriodoCompensacao"
                                                 style="position:relative; top:7px; left:5px; font-size: 11px;"
                                                 onclick="document.getElementById('formFiltrosGestaoRecebiveis:dataInicioCInputDate').value='';
                                         document.getElementById('formFiltrosGestaoRecebiveis:dataTerminoCInputDate').value='';
                                         document.getElementById('formFiltrosGestaoRecebiveis:horaInicialC').value='';
                                         document.getElementById('formFiltrosGestaoRecebiveis:horaFinalC').value='';"
                                                 title="Limpar período de compensação.">
                                    <i class="fa-icon-eraser"></i>
                                </a4j:commandLink>

                    </h:panelGroup>

                    <!-- filtros de conciliação -->
                    <h:outputText styleClass="tituloCampos upper flex"
                                  rendered="#{GestaoRecebiveisControle.visaoConciliacao}"
                                  value="#{msg_aplic.prt_Finan_GestaoRecebiveis_periodoConciliacao}"/>
                    <h:panelGroup styleClass="flex"
                                  layout="block"
                                  rendered="#{GestaoRecebiveisControle.visaoConciliacao}">
                        <h:panelGroup styleClass="dateTimeCustom" style="font-size: 11px;" >
                            <rich:calendar id="dataInicioConciliacao"
                                           value="#{GestaoRecebiveisControle.dataInicialConciliacao}"
                                           inputSize="10"
                                           buttonIcon="/imagens_flat/calendar-button.svg"
                                           inputClass="form"
                                           oninputblur="blurinput(this);"
                                           oninputfocus="focusinput(this);"
                                           datePattern="dd/MM/yyyy"
                                           oninputchange="return validar_Data(this.id);"
                                           oninputkeypress="return mascara2(this.form, this.id, '99/99/9999', event);"
                                           enableManualInput="true"
                                           zindex="2"
                                           showWeeksBar="false"/>
                        </h:panelGroup>

                        <h:outputText styleClass="tituloCampos"
                                      style="margin: 7px 9px 0 0px; font-size: 11px; margin-left: 7px;"
                                      value="#{msg_aplic.prt_ate}"/>

                        <h:panelGroup styleClass="dateTimeCustom"  style="font-size: 11px;">
                            <rich:calendar id="dataTerminoConciliacao"
                                           value="#{GestaoRecebiveisControle.dataFinalConciliacao}"
                                           inputSize="10"
                                           inputClass="form"
                                           oninputblur="blurinput(this);"
                                           buttonIcon="/imagens_flat/calendar-button.svg"
                                           oninputfocus="focusinput(this);"
                                           oninputchange="return validar_Data(this.id);"
                                           oninputkeypress="return mascara2(this.form, this.id, '99/99/9999', event);"
                                           datePattern="dd/MM/yyyy"
                                           enableManualInput="true" showWeeksBar="false"
                                           zindex="2"/>
                        </h:panelGroup>

                        <a4j:commandLink id="limparPeriodoConciliacao"
                                         style="position:relative; top:7px; left:5px; font-size: 11px;"
                                         onclick="document.getElementById('formFiltrosGestaoRecebiveis:dataInicioConciliacaoInputDate').value='';
                                         document.getElementById('formFiltrosGestaoRecebiveis:dataTerminoConciliacaoInputDate').value='';"
                                         title="Limpar período de conciliação.">
                            <i class="fa-icon-eraser"></i>
                        </a4j:commandLink>

                    </h:panelGroup>

                </div>

                <h:panelGroup layout="block" rendered="#{GestaoRecebiveisControle.visaoConciliacao}"
                              styleClass="block">
                    <h:outputText styleClass="tituloCampos upper flex"
                                  value="REMESSA / RESUMO DE OPERAÇÕES"/>
                    <h:inputText id="codigoresumooperacoes" value="#{GestaoRecebiveisControle.ro}"
                                 onblur="blurinput(this);" size="20" maxlength="20"
                                 onfocus="focusinput(this);" styleClass="form"/>

                </h:panelGroup>

                <h:panelGroup layout="block" rendered="#{GestaoRecebiveisControle.visaoConciliacao}"
                              styleClass="block">
                    <div class="inlineBlock" style="padding: 10px 10px 10px 0;">
                        <div class="chk-fa-container inline ">
                            <input type="checkbox" name="apresentarPagamentosCancelados"
                                   onclick="setarApresentarPagamentosCancelados(this);"/>
                            <span/>
                        </div>
                        <h:outputText styleClass="tituloCampos upper"
                                      value="Mostrar cancelados/devolvidos no Sistema"
                                      style="font-weight: normal;"/>
                    </div>
                </h:panelGroup>


                <h:panelGroup layout="block" rendered="#{!GestaoRecebiveisControle.visaoConciliacao}"
                              style="padding: 0 0 10px 0px; border-bottom:#E5E5E5 1px solid; width: 100%;">
                    <div class="inlineBlock" style="padding-right: 10px;">
                        <h:outputText styleClass="tituloCampos upper flex"
                                      value="#{msg_aplic.prt_Finan_GestaoRecebiveis_operador}"/>
                        <h:panelGroup id="panelOperadorCaixa">
                            <h:inputText id="nomeOperador" size="40"
                                         maxlength="255" onblur="blurinput(this);"
                                         onfocus="focusinput(this);" styleClass="form"
                                         value="#{GestaoRecebiveisControle.operadorCaixa.nome}"/>

                            <rich:suggestionbox height="200" width="200"
                                                for="nomeOperador"
                                                fetchValue="#{result.nome}"
                                                suggestionAction="#{GestaoRecebiveisControle.executarAutocompleteConsultaUsuario}"
                                                minChars="1" rowClasses="20"
                                                status="statusHora"
                                                nothingLabel="Nenhum Operador encontrado !"
                                                var="result" id="suggestionUsuario">
                                <a4j:support event="onselect"
                                             action="#{GestaoRecebiveisControle.selecionarUsuarioSuggestionBox}"/>
                                <a4j:support event="onkeydown"
                                             action="#{GestaoRecebiveisControle.selecionarUsuarioSuggestionBox}"/>
                                <h:column>
                                    <h:outputText value="#{result.nome}"/>
                                </h:column>
                            </rich:suggestionbox>
                        </h:panelGroup>

                        <a4j:commandLink id="limparOperador"
                                         style="position:relative; left:5px; font-size: 11px;"
                                         action="#{GestaoRecebiveisControle.limparOperador}"
                                         status="false"
                                         reRender="formFiltrosGestaoRecebiveis:panelOperadorCaixa"
                                         title="Limpar operador de caixa.">
                            <i class="fa-icon-eraser"></i>
                        </a4j:commandLink>
                    </div>
                    <div class="inlineBlock">
                        <h:outputText styleClass="tituloCampos upper flex"
                                      value="#{msg_aplic.prt_Finan_GestaoRecebiveis_nome}"/>

                        <h:inputText id="nome" size="40" maxlength="255" onblur="blurinput(this);"
                                     onfocus="focusinput(this);" styleClass="form"
                                     value="#{GestaoRecebiveisControle.nome}"/>

                        <a4j:commandLink id="limparNomePagador"
                                         style="position:relative; left:5px; font-size: 11px;"
                                         onclick="document.getElementById('formFiltrosGestaoRecebiveis:nome').value='';"
                                         title="Limpar nome pagador." status="false">
                            <i class="fa-icon-eraser"></i>
                        </a4j:commandLink>
                    </div>
                </h:panelGroup>
                <h:panelGroup layout="block"
                              rendered="#{!GestaoRecebiveisControle.visaoConciliacao}"
                              styleClass="block" style="border-bottom:#E5E5E5 1px solid; width: 100%;">
                    <div class="inlineBlock" style="padding: 10px 10px 10px 0px;">
                        <div class="chk-fa-container inline ">
                            <input id="mostrarCanceladosDevolvidos" type="checkbox" name="mostrarCancelados" onclick="setarMostrarCancelados(this);"/>
                            <span/>
                        </div>
                        <h:outputText styleClass="tituloCampos upper" value="Mostrar cancelados/devolvidos"
                                      style="font-weight: normal;"/>
                    </div>
                    <div class="inlineBlock">
                        <div class="chk-fa-container inline ">
                            <h:selectBooleanCheckbox
                                    value="#{GestaoRecebiveisControle.considerarDataCompensacaoOriginal}"
                                    id="considerarDataOriginal"/>
                            <span/>
                        </div>
                        <h:outputText styleClass="tituloCampos tooltipster upper" value="Considerar a compensação original"
                                      title="Ao marcar este check-box, será considerado na consulta a data de compensação original dos recebíveis das formas de pagamento 'cheques' e 'cartões de crédito'.<br/>Obs: Não será considerado a forma de pagamento 'cartão de débito'"
                                      style="font-weight: normal;"/>
                    </div>
                </h:panelGroup>

                <h:panelGroup layout="block"
                              rendered="#{!GestaoRecebiveisControle.visaoConciliacao}"
                              styleClass="block" style="border-bottom:#E5E5E5 1px solid; width: 100%;">

                        <div class="inlineBlock" style="padding: 10px 10px 10px 0px;">
                            <div class="chk-fa-container inline ">
                                <h:selectBooleanCheckbox
                                        value="#{GestaoRecebiveisControle.antecipados}"
                                        id="antecipados"/>
                                <span/>
                            </div>
                            <h:outputText styleClass="tituloCampos tooltipster upper" value="Cheques e cartões antecipados"
                                          title="Ao marcar este check-box serão apresentados somente os recebíveis que tiveram antecipação realizada"
                                          style="font-weight: normal;"/>
                        </div>
                </h:panelGroup>

                <h:panelGroup rendered="#{!GestaoRecebiveisControle.visaoConciliacao}"
                              styleClass="block" style="padding: 10px 0 0px 0px">
                    <div class="chk-fa-container inline ">
                        <h:selectBooleanCheckbox value="#{GestaoRecebiveisControle.recebidosZW}"
                                                 rendered="#{ConfiguracaoFinanceiroControle.confFinanceiro.usarMovimentacaoContas}">
                            <a4j:support event="onclick" reRender="filtros" status="false"/>
                        </h:selectBooleanCheckbox>
                        <span/>
                    </div>
                    <h:outputText styleClass="tituloCampos tooltipster upper" style="font-weight: normal;"
                                  rendered="#{ConfiguracaoFinanceiroControle.confFinanceiro.usarMovimentacaoContas}"
                                  title="#{msg_aplic.gestaoRecebiveis_recebidosZW}"
                                  value="Vou movimentar recebíveis do ADM">
                    </h:outputText>
                </h:panelGroup>
            </div>
        </h:panelGroup>
        <div class="rodapeBotoes" style="padding-bottom: 20px;">
            <a4j:commandLink
                    id="btnConsultarConciliacao"
                    onclick="iniciarStatus(2.5);"
                    oncomplete="fecharStatus();#{GestaoRecebiveisControle.msgAlert};#{GestaoRecebiveisControle.mensagemNotificar}"
                    reRender="idCaixaCorpo"
                    rendered="#{GestaoRecebiveisControle.visaoConciliacao}"
                    action="#{GestaoRecebiveisControle.consultarConciliacao}"
                    styleClass="pure-button pure-button-primary" style="margin-right:10px; margin-left: 15px;"
                    title="#{msg.msg_consultar_dados}">
                <i style="font-size: 14px" class="fa-icon-search"></i> &nbsp <h:outputText style="font-size: 14px"
                                                                                           value="Pesquisar"/>
            </a4j:commandLink>
            <a4j:commandLink rendered="#{!GestaoRecebiveisControle.visaoConciliacao}"
                    onclick="Richfaces.hideModalPanel('modalPanelFiltrosGestaoRecebiveis');iniciarStatus(2.5);jQuery('.growl-container').empty();"
                    oncomplete="fecharStatus();reRenderMenuLateral();#{GestaoRecebiveisControle.mensagemNotificar}"
                    reRender="idCaixaCorpo"
                    id="pesquisar"
                    action="#{GestaoRecebiveisControle.consultarRecebiveis}"
                    styleClass="pure-button pure-button-primary" style="margin-right:10px; margin-left: 15px;"
                    title="#{msg.msg_consultar_dados}">
                <i style="font-size: 14px" class="fa-icon-search"></i> &nbsp <h:outputText style="font-size: 14px"
                                                                                           value="Pesquisar"/>
            </a4j:commandLink>
            <a4j:commandLink id="limparFiltros"
                             reRender="formFiltrosGestaoRecebiveis"
                             rendered="#{!GestaoRecebiveisControle.visaoConciliacao}"
                             action="#{GestaoRecebiveisControle.limparFiltros}"
                             styleClass="pure-button"
                             oncomplete="trocarUltimoBloco()"
                             title="#{msg.msg_consultar_dados}">
                <h:outputText style="font-size: 14px" value="Limpar filtros"/>
            </a4j:commandLink>
            <h:selectBooleanCheckbox value="#{GestaoRecebiveisControle.mostrarCancelados}"
                                     style="display: none;"
                                     id="idmostrarcancelados"/>

            <h:selectBooleanCheckbox value="#{GestaoRecebiveisControle.apresentarPagamentosCancelados}"
                                     style="display: none;"
                                     id="idapresentarPagamentosCancelados"/>
        </div>
        <script type="text/javascript" language="javascript">
            inicializarMostrarCancelados();
            inicializarApresentarPagamentosCancelados();
            carregarTooltipster();
        </script>
        <h:panelGroup layout="block" id="containerFuncMask">
            <script>
                carregarMaskInput();
            </script>
        </h:panelGroup>
    </a4j:form>
</rich:modalPanel>


