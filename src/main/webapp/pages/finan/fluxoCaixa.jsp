<%@page contentType="text/html" pageEncoding="ISO-8859-1" %>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<%@include file="includes/include_imports.jsp" %>
<%@taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<link href="../../css/telaCliente.css" rel="stylesheet" type="text/css"/>
<link href="../../beta/css/pure-min.css" type="text/css" rel="stylesheet"/>
<link href="../../beta/css/bootstrap-tabs.css" type="text/css" rel="stylesheet"/>
<link href="../../beta/css/pure-ext.css" type="text/css" rel="stylesheet"/>
<link href="../../css_pacto.css" rel="stylesheet" type="text/css">
<link href="../../css/financeiro.css" rel="stylesheet" type="text/css">
<link href="../../css/fluxoCaixa.css" rel="stylesheet" type="text/css">
<link href="../../css/otimize.css" rel="stylesheet" type="text/css">
<script src="../../script/packJQueryPlugins.min.js" type="text/javascript"></script>


<script>
    var ctxForJS = "${pageContext.request.contextPath}"
</script>

<style>
    .cabecalhoimpressao, .diaAno, .modoimpressao .diaSimples, .modoimpressao .comboempresa, .modoimpressao a .saldoInicial, .saldoInicial.nolink{
        display: none !important;
    }
    .modoimpressao .fcrealizado, .modoimpressao .fcprevisto{
        margin: 0 50px !important;
    }

    .modoimpressao .container-box-header.fc{
        border-bottom: none;
    }
    .modoimpressao .diaAno{
        display: inline-block !important;
    }
    .modoimpressao .cabecalhoimpressao{
        display: block !important;
    }
    .container-imagem{
        width: 100% !important;
    }

    .container-imagem.zw_ui.fechado-zwui {
        width: calc(100% - 84px) !important;
        margin-left: 0px !important;
        overflow: auto;
    }

    .modoimpressao.fcprevisto .tabelaModoTable.fcprevisto,
    .modoimpressao.fcrealizado .tabelaModoTable.fcrealizado{
        display: inline-block !important;
        width: calc(100% - 68px) !important;
    }

    .modoimpressao .saldoInicial.nolink{
        display: inline-block !important;

    }

    .modoimpressao i, .modoimpressao a:not(.mostrarimpressao),
    .modoimpressao .sliderOpcoes,
    .modoimpressao .tabelaModoTable,
    .modoimpressao .caixaRodape,
    .modoimpressao .topoZW,
    .tooltipster-content table tr td:last-child{
        text-align: right;
    }
    .contentTipo {
        display: block;
        width: 100%;
    }

    .tituloCampos, .alinhar, table {
        color: #777777 !important;
    }

    .colunaConta {
        display: flex;
    }

    td.w70 {
        width: 70%;
    }

    td.w30right {
        width: 30%;
        height: 35px;
        text-align: right;
    }

    .margin-box.fc {
        margin: 10px !important;
        width: calc(100% - 20px);
    }

    .fc .tituloCampos {
        display: inline-block;
        vertical-align: baseline;
        margin: 0 10px;
    }

    .fc .btns .tituloCampos:first-child {
        margin-left: 0;
    }

    .fc .btns {
        font-size: 11px;
    }

    .btns .minibtn, .margin-box .minibtn {
        margin: 0 10px;
        float: right;
        font-size: 14px;
        margin-top: 5px;
    }

    .container-header-titulo {
        margin-left: 15px;
    }

    .dataTable.tableCliente {
        margin-top: 20px !important;
    }

    .container-box-header.fc {
        padding: 10px;
        width: calc(100% - 20px);
    }

    .gridConta .rich-table-cell {
        font-size: 12px;
        width: 33%;
        border: none !important;
        padding: 5px;
    }

    .tabelaSimplesCustom tr.semBg,
    .tabelaSimplesCustom > tbody tr:nth-child(odd),
    .tabelaSimplesCustom > tbody tr:nth-child(even) {
        background-color: transparent !important;
    }

    .linhaFluxo.separador, .linhaFluxo.separador:hover {
        background-color: #52bfff !important;
        color: #ffffff;
        font-weight: bold;
        height: 43px;
    }

    .hoje {
        border-bottom: 2px solid #777777;

    }
    .diaSimples, .diaAno{
        -webkit-user-select: none; /* webkit (safari, chrome) browsers */
        -moz-user-select: none; /* mozilla browsers */
        -khtml-user-select: none; /* webkit (konqueror) browsers */
        -ms-user-select: none; /* IE10+ */
    }

    .linhaFluxo {
        line-height: 40px;
        color: #777777;
        font-family: Arial;
        font-size: 12px;
        cursor: pointer;
    }

    .containerInterior {
        display: none;
    }

    .linhaFluxo.cabecalho, .linhaFluxo.rodape {
        text-transform: uppercase;
        color: #777777;
        font-size: 12px !important;
        font-weight: bold;
        cursor: text;
    }

    .linhaFluxo.rodape {
        border-top: 2px #777777 solid;
    }

    .linhaFluxo.planoContas.pai {
        display: block;
    }

    .linhaFluxo.planoContas {
        font-weight: bold;
        text-transform: uppercase;
        font-size: 11px;
        line-height: 30px;
        display: none;
    }

    .linhaFluxo.lancamentos {
        font-weight: normal;
        font-size: 11px;
        line-height: 20px;
    }

    .linhaFluxo.dia, .linhaFluxo.cabecalho {
        display: block;
    }

    .linhaFluxo.dia, .linhaFluxo.planoContas, .linhaFluxo.lancamentos {
        border-right: 2px solid #00c350;
    }

    .linhaFluxo.negativo {
        border-right-color: red;
    }

    .linhaFluxo.planoContas .colunaFluxo.descricao {
        width: calc(36% - 25px);
        padding-left: 10px;
    }

    .linhaFluxo.lancamentos .colunaFluxo.descricao {
        width: calc(36% - 30px);
        padding-left: 15px;
        white-space: nowrap;
        overflow-y: hidden;
        overflow-x: hidden;
        display: inline-grid;
    }

    .linhaFluxo .colunaFluxo:last-child {
        width: calc(18% - 10px);
        padding-right: 10px;
    }

    .colunaFluxo.entradadiferente {
        font-weight: bold;
        color: green;
    }

    .colunaFluxo.saidadiferente {
        font-weight: bold;
        color: red;
    }

    .colunaFluxo {
        width: 15%;
        display: inline-block;
        text-align: right;
    }

    .colunaFluxo.descricao {
        width: calc(36% - 25px);
        padding-left: 10px;
        text-align: left;
    }

    .modificado.itemPar .linhaFluxo {
        background-color: #dbe8e8;
    }

    .modificado.itemImpar .linhaFluxo {
        background-color: #ecefed;
    }

    .itemPar .linhaFluxo {
        background-color: #eeeeee;
    }

    .itemImpar .linhaFluxo {
        background-color: #ffffff;
    }

    .linhaFluxo:hover, .linhaImpar:hover, .linhaPar:hover {
        background-color: rgba(0, 0, 0, .2) !important;
        color: #777777;
    }

    .linhaFluxo.aberto .fa-icon-plus, .linhaFluxo.fechado .fa-icon-minus {
        display: none;
    }

    .linhaFluxo.fechado .fa-icon-plus, .linhaFluxo.aberto .fa-icon-minus {
        display: inline;
    }

    .saldoInicial {
        text-align: right;
        line-height: 30px;
        font-size: 12px;
        text-transform: uppercase;
        color: #777777;
    }

    .fa-icon-unlink:before,
    .fa-icon-chain-broken:before {
        content: "\f127";
    }

    a.minibtn.btnModo {
        display: inline-block;
        background-color: #eee;
        padding: 10px;
        border-radius: 50%;
        margin-top: 0;
        color: #777777 !important;
        text-align: center;
        right: 5px;
        position: absolute;
    }

    a.minibtn.btnModo.ativo {
        background-color: #00c350;
        color: #ffffff !important;
    }

</style>

<f:view>
    <jsp:include page="/includes/include_carregando_ripple.jsp" flush="true"/>
    <html>

    <!-- Inclui o elemento HEAD da página -->
    <head>
        <%@include file="includes/include_head_finan.jsp" %>
        <script type="text/javascript" language="javascript" src="${contextoFinan}/script/telaInicial.js"></script>

    </head>

    <h:panelGroup layout="block" style="display: inline-table;width: 100%;" styleClass="fundoCinza" >
        <h:form id="formTopo" style="overflow-x: visible !important;">
            <h:panelGroup layout="block" styleClass="bgtop topoZW" rendered="#{MenuControle.apresentarTopo}">
                <jsp:include page="../../include_topo_novo.jsp" flush="true"/>
                <jsp:include page="../../include_menu_fin_flat.jsp" flush="true"/>
                <rich:jQuery selector=".item3" query="addClass('menuItemAtual')"/>
            </h:panelGroup>
        </h:form>

        <h:panelGroup layout="block" styleClass="caixaCorpo">
            <h:panelGroup layout="block" style="height: 80%;width: 100%">
                <h:panelGroup layout="block" styleClass="caixaMenuLatel">

                    <h:panelGroup layout="block" styleClass="container-imagem zw_ui">
                        <h:form id="formFC">

                            <a4j:keepAlive beanName="ExportadorListaControle"/>

                            <script>
                                function sumirTabelas() {
                                    jQuery('.tabelaModoTable').slideUp();
                                    jQuery('.tabelaModoTable').hide();
                                }
                                function abrir(v) {
                                    jQuery(v).toggle();
                                }
                                function abrirTudo(v, p) {
                                    jQuery(v).toggle();
                                    jQuery(v).click();
                                    jQuery(p).show();
                                    jQuery(p).addClass('aberto');
                                    jQuery(p).removeClass('fechado');
                                }
                                function abrirPlano(v, p, f) {
                                    if (jQuery(p).hasClass('aberto')) {
                                        jQuery("[agrup^='" + f + "']").hide();
                                        jQuery("[agrup^='" + f + "']").removeClass('aberto');
                                        jQuery("[agrup^='" + f + "']").addClass('fechado');
                                    } else {
                                        jQuery(f).show();
                                    }

                                    jQuery(v).toggle();
                                    jQuery(p).toggleClass('aberto');
                                    jQuery(p).toggleClass('fechado');
                                }
                                function preencherValorChamarBotao(idBotao, idHidden, valorHidden) {
                                    var hidden = document.getElementById(idHidden);
                                    var botao = document.getElementById(idBotao);
                                    hidden.value = valorHidden;
                                    botao.click();
                                    event.stopPropagation();
                                }

                                function preencherValorChamarBotaoLog(idBotao) {
                                    var botao = document.getElementById(idBotao);
                                    botao.click();
                                    event.stopPropagation();
                                }


                                function marcarTodos(ck, marcador){
                                    if(jQuery(ck).is(':checked')){
                                        jQuery(marcador).prop('checked',true);
                                    }else{
                                        jQuery(marcador).prop('checked',false);
                                    }
                                }

                                function checarSeTodos(marcador){
                                    var todos = true;
                                    jQuery('.'+marcador).each(function( index ) {
                                        if(!jQuery(this).prop('checked')){
                                            todos = false;
                                        }
                                    });
                                    jQuery('.princ'+marcador).prop('checked', todos);
                                }


                            </script>

                            <table  class="cabecalhoimpressao" width="100%" style="margin: 20px;">
                                <tr>
                                    <td>Data: <h:outputText value="#{FluxoCaixaControle.hoje}">
                                        <f:convertDateTime pattern="dd/MM/yyyy HH:mm"/></h:outputText>
                                    </td>
                                <tr>
                                <tr>
                                    <td>Usuário: <h:outputText
                                            value="#{FluxoCaixaControle.nomeUsuarioLogado}"/></td>
                                </tr>

                            </table>

                            <h:panelGroup style="display: flex" id="panelFluxoCaixa">
                                <!-- grafico -->

                                <h:panelGroup layout="block" styleClass="container-box tabelaModoGrafico"
                                              style="display: inline-block; margin-left: 20px !important; margin-top: 10px !important"
                                              rendered="#{FluxoCaixaControle.modoGrafico}">
                                    <h:panelGroup styleClass="container-box-header" layout="block">
                                        <h:panelGroup layout="block" styleClass="margin-box"
                                                      id="headergeralgrafico"
                                                      style="display:flex; justify-content:space-between; align-items: center; height: 59px;margin: 0; width: 100%;">

                                            <h:panelGroup styleClass="text" layout="block">
                                                <h:outputText styleClass="container-header-titulo"
                                                              value="#{msg_aplic.prt_lbl_grafico_fluxo_caixa}"/>

                                                <h:outputLink styleClass="linkWiki"
                                                              value="#{SuperControle.urlBaseConhecimento}como-ver-meu-fluxo-de-caixa/"
                                                              title="Clique e saiba mais: #{msg_aplic.prt_fluxo_caixa_previsto}"
                                                              target="_blank">
                                                    <i class="fa-icon-question-sign" style="font-size: 18px"></i>
                                                </h:outputLink>
                                            </h:panelGroup>

                                            <a4j:commandLink styleClass="tooltipster icon linkAzul minibtn btnModo "
                                                             reRender="panelFluxoCaixa"
                                                             action="#{FluxoCaixaControle.sairGrafico}"
                                                             title="#{msg_aplic.prt_tabela_fluxo_caixa}"
                                                             accesskey="2">
                                                <i class="fa-icon-table font16"></i>
                                            </a4j:commandLink>


                                        </h:panelGroup>

                                    </h:panelGroup>
                                    <h:panelGroup styleClass="container-box-header fc" layout="block"
                                                  style="height: 35px;">
                                        <h:panelGrid styleClass="btns" width="100%"
                                                     style="width: 100%;"
                                                     columns="1">

                                                <h:panelGroup>

                                            <%--<h:outputText value="#{msg_aplic.prt_de}" styleClass="tituloCampos"/>

                                            <h:panelGroup styleClass="dateTimeCustom" style="display: inline-block">
                                                <rich:calendar value="#{FluxoCaixaControle.dataInicialPrevisto}"
                                                               inputSize="10"
                                                               inputClass="form"
                                                               oninputblur="blurinput(this);"
                                                               oninputfocus="focusinput(this);"
                                                               oninputchange="return validar_Data(this.id);"
                                                               datePattern="dd/MM/yyyy"
                                                               buttonIcon="/imagens_flat/calendar-button.svg"
                                                               enableManualInput="true"
                                                               zindex="2"
                                                               showWeeksBar="false"/>
                                            </h:panelGroup>
                                            <h:outputText value="#{msg_aplic.prt_ate}" styleClass="tituloCampos"/>
                                            <h:panelGroup styleClass="dateTimeCustom" style="display: inline-block">
                                                <rich:calendar value="#{FluxoCaixaControle.dataFinalPrevisto}"
                                                               inputSize="10"
                                                               inputClass="form"
                                                               oninputblur="blurinput(this);"
                                                               oninputfocus="focusinput(this);"
                                                               oninputchange="return validar_Data(this.id);"
                                                               datePattern="dd/MM/yyyy"
                                                               buttonIcon="/imagens_flat/calendar-button.svg"
                                                               enableManualInput="true"
                                                               zindex="2"
                                                               showWeeksBar="false"/>
                                            </h:panelGroup>--%>

                                        </h:panelGroup>
                                            <h:panelGroup>


                                            </h:panelGroup>


                                        </h:panelGrid>
                                    </h:panelGroup>
                                    <h:panelGroup layout="block" styleClass="margin-box fc" id="graficoFC">
                                        <script>
                                            function montarGrafico() {
                                                var chart = AmCharts.makeChart("chartdiv", {
                                                    "type": "serial",
                                                    "theme": "light",

                                                    "dataProvider": ${FluxoCaixaControle.dadosGrafico},
                                                    "valueAxes": [{
                                                        "axisAlpha": 0,
                                                        "position": "left",
                                                        "labelFunction": function (value, valueText, valueAxis) {
                                                            return converteFloatMoeda(value);
                                                        }
                                                    }],
                                                    "legend": {
                                                        "valueFunction": function (graphDataItem, valueText) {
                                                            return "";
                                                        },
                                                        "useGraphSettings": true
                                                    },
                                                    "startDuration": 1,
                                                    "graphs": [{
                                                        "balloonText": "${msg_aplic.prt_previsto}: <b>[[value]]</b>",
                                                        "balloonFunction": function (item) {
                                                            return item.description + ' - ${msg_aplic.prt_previsto}: ' + converteFloatMoeda(item.values.value);
                                                        },
                                                        "bullet": "square",
                                                        "bulletSize": 8,
                                                        "lineColor": "#3659d1",
                                                        "lineThickness": 2,
                                                        "descriptionField": "dia",
                                                        "type": "smoothedLine",
                                                        "title": "${msg_aplic.prt_previsto}",
                                                        "valueField": "previsto"
                                                    }, {
                                                        "balloonText": "${msg_aplic.prt_simulado_realizado}: <b>[[value]]</b>",
                                                        "balloonFunction": function (item) {
                                                            return item.description + ' - ${msg_aplic.prt_simulado_realizado}: ' + converteFloatMoeda(item.values.value);
                                                        },
                                                        "bullet": "diamond",
                                                        "bulletSize": 8,
                                                        "title": "${msg_aplic.prt_simulado_realizado}",
                                                        "lineColor": "#d43489",
                                                        "lineThickness": 2,
                                                        "descriptionField": "dia",
                                                        "type": "smoothedLine",
                                                        "valueField": "simulado"
                                                    }, {
                                                        "balloonText": "${msg_aplic.prt_realizado}: <b>[[value]]</b>",
                                                        "balloonFunction": function (item) {
                                                            return item.description + ' - ${msg_aplic.prt_realizado}: ' + converteFloatMoeda(item.values.value);
                                                        },
                                                        "bullet": "round",
                                                        "bulletSize": 8,
                                                        "lineColor": "#d1810f",
                                                        "lineThickness": 2,
                                                        "title": "${msg_aplic.prt_realizado}",
                                                        "descriptionField": "dia",
                                                        "type": "smoothedLine",
                                                        "valueField": "realizado"
                                                    }],
                                                    "chartCursor": {
                                                        "categoryBalloonEnabled": false,
                                                        "cursorAlpha": 0,
                                                        "zoomable": false
                                                    },
                                                    "categoryField": "dia",
                                                    "categoryAxis": {
                                                        "gridPosition": "start",
                                                        "labelRotation": 0
                                                    },
                                                    "export": {
                                                        "enabled": false
                                                    }

                                                });
                                            }

                                            montarGrafico();

                                        </script>

                                        <div id="chartdiv" style="width: 100%; height: 50vh;" class="step2 tudo"></div>

                                    </h:panelGroup>
                                </h:panelGroup>


                                <!-- previsto -->
                                <h:panelGroup layout="block" styleClass="container-box tabelaModoTable fcprevisto"
                                              id="groupPrevisto"
                                              style="display: inline-block; margin-left: 20px !important; margin-top: 10px !important"
                                              rendered="#{!FluxoCaixaControle.modoGrafico}">
                                    <h:panelGroup styleClass="container-box-header" layout="block">
                                        <h:panelGroup layout="block" styleClass="margin-box"
                                                      id="headergeral"
                                                      style="display:flex; justify-content:space-between; align-items: center; height: 59px;margin: 0; width: 100%;">

                                            <h:panelGroup styleClass="text" layout="block">
                                                <h:outputText styleClass="container-header-titulo"
                                                              value="#{msg_aplic.prt_fluxo_caixa_previsto}"/>

                                                <h:outputLink styleClass="linkWiki"
                                                              value="#{SuperControle.urlBaseConhecimento}como-ver-meu-fluxo-de-caixa/"
                                                              title="Clique e saiba mais: #{msg_aplic.prt_fluxo_caixa_previsto}"
                                                              target="_blank">
                                                    <i class="fa-icon-question-sign" style="font-size: 18px"></i>
                                                </h:outputLink>
                                            </h:panelGroup>


                                            <h:panelGroup styleClass="cb-container comboempresa" layout="block"
                                                          style="position: absolute; right: 112px;"
                                                          rendered="#{FluxoCaixaControle.mostrarCampoEmpresa}">
                                                <h:selectOneMenu id="empresa"
                                                                 style="max-width: 170px;"
                                                                 onfocus="focusinput(this);"
                                                                 value="#{FluxoCaixaControle.empresa.codigo}">
                                                    <f:selectItems value="#{FluxoCaixaControle.listaSelectItemEmpresa}"/>
                                                    <a4j:support action="#{FluxoCaixaControle.obterEmpresaEscolhida}" event="onchange"
                                                                 reRender="panelFluxoCaixa"/>
                                                </h:selectOneMenu>
                                            </h:panelGroup>

                                            <a4j:commandLink styleClass="tooltipster icon linkAzul minibtn btnModo "
                                                             reRender="panelFluxoCaixa"
                                                             style="right: 50px;"
                                                             action="#{FluxoCaixaControle.abrirGrafico}"
                                                             title="#{msg_aplic.prt_grafico_fluxo_caixa}"
                                                             accesskey="2">
                                                <i class="fa-icon-bar-chart font16"></i>
                                            </a4j:commandLink>

                                            <h:panelGroup rendered="#{!FluxoCaixaControle.saldoFixado}">
                                                <a4j:commandLink
                                                        styleClass="tooltipster icon linkAzul minibtn btnModo ativo"
                                                        reRender="panelFluxoCaixa"
                                                        rendered="#{FluxoCaixaControle.datasVinculadas}"
                                                        action="#{FluxoCaixaControle.desvincularDatas}"
                                                        oncomplete="carregarTooltipster();"
                                                        title="#{msg_aplic.prt_desvincular_datas_fluxo_caixa}"
                                                        accesskey="2">
                                                    <i class="fa-icon-link font16"></i>
                                                </a4j:commandLink>
                                            </h:panelGroup>


                                            <h:panelGroup rendered="#{FluxoCaixaControle.saldoFixado}">
                                                <a4j:commandLink
                                                        styleClass="tooltipster icon linkAzul minibtn btnModo ativo"
                                                        reRender="panelFluxoCaixa"
                                                        disabled="#{FluxoCaixaControle.saldoFixado}"
                                                        rendered="#{FluxoCaixaControle.datasVinculadas && FluxoCaixaControle.saldoFixado}"
                                                        action="#{FluxoCaixaControle.desvincularDatas}"
                                                        oncomplete="carregarTooltipster();"
                                                        title="Impossível desvincular datas com simulação fixada."
                                                        accesskey="2">
                                                    <i class="fa-icon-link font16"></i>
                                                </a4j:commandLink>
                                            </h:panelGroup>

                                            <a4j:commandLink id="linkVincularDatasFluxoCaixa"
                                                             styleClass="tooltipster icon linkAzul minibtn btnModo "
                                                             oncomplete="carregarTooltipster();"
                                                             rendered="#{!FluxoCaixaControle.datasVinculadas}"
                                                             reRender="panelFluxoCaixa"
                                                             action="#{FluxoCaixaControle.vincularDatas}"
                                                             title="#{msg_aplic.prt_vincular_datas_fluxo_caixa}"
                                                             accesskey="2">
                                                <i class="fa-icon-unlink font16"></i>
                                            </a4j:commandLink>

                                        </h:panelGroup>
                                    </h:panelGroup>
                                    <h:panelGroup layout="block" styleClass="sliderOpcoes" id="sliderOpcoesPrevisto">
                                        <a4j:repeat var="mp" value="#{FluxoCaixaControle.mesesPrevisto}">

                                            <h:panelGroup layout="block"
                                                          styleClass="opcao #{mp.codigoString eq FluxoCaixaControle.mesPrevisto ? 'selecionado' : ''}">
                                                <a4j:commandLink reRender="panelFluxoCaixa"
                                                                 action="#{FluxoCaixaControle.consultarPrevistoMes}" oncomplete="#{FluxoCaixaControle.mensagemNotificar}">
                                                    <h:outputText styleClass="label" value="#{mp.label}"></h:outputText>
                                                </a4j:commandLink>
                                                    <h:outputText styleClass="fa-icon-caret-down"></h:outputText>
                                                <a4j:commandLink reRender="panelFluxoCaixa"
                                                                 styleClass="indicador"
                                                                 action="#{FluxoCaixaControle.consultarPrevistoMes}" oncomplete="#{FluxoCaixaControle.mensagemNotificar}">

                                                </a4j:commandLink>
                                                    <h:outputText styleClass="fa-icon-caret-up"></h:outputText>

                                            </h:panelGroup>


                                        </a4j:repeat>
                                    </h:panelGroup>
                                    <h:panelGroup styleClass="container-box-header fc" layout="block">


                                        <h:panelGrid styleClass="btns" width="100%" columnClasses="fecharmenusaldofluxo"
                                                     style="width: 100%;">

                                            <h:panelGroup layout="block"
                                                          style="display: flex; align-items: center">

                                                <h:panelGroup layout="block" style="width: 70%">
                                                    <div class="cb-container"
                                                         style="font-size: 11px !important;">
                                                        <h:selectOneMenu id="filtroParcelasEmAberto"
                                                                         styleClass="form"
                                                                         value="#{FluxoCaixaControle.filtroParcelasEmAberto}">
                                                            <f:selectItems
                                                                    value="#{FluxoCaixaControle.listaFiltroParcelasEmAberto}"/>
                                                            <a4j:support
                                                                    event="onchange"
                                                                    action="#{FluxoCaixaControle.consultarPrevisto}"
                                                                    reRender="groupPrevisto"/>
                                                        </h:selectOneMenu>
                                                    </div>
                                                </h:panelGroup>

                                                <h:panelGroup layout="block" style="width: 30%">

                                                    <a4j:commandLink id="exportarPdfPrevisto"
                                                                     style="display: inline-block; margin: 0 10px;"
                                                                     styleClass="tooltipster icon linkAzul minibtn"
                                                                     title="Imprimir PDF"
                                                                     oncomplete="#{FluxoCaixaControle.msgAlert}"
                                                                     actionListener="#{FluxoCaixaControle.exportarPrevisto}">
                                                        <f:attribute name="tipo" value="pdf"/>
                                                        <i class="fa-icon-print font16"></i>
                                                    </a4j:commandLink>

                                                    <a4j:commandLink id="exportarExcelPrevisto"
                                                                     style="display: inline-block; margin: 0 10px;"
                                                                     styleClass="tooltipster icon linkAzul minibtn"
                                                                     title="Imprimir Excel"
                                                                     oncomplete="#{FluxoCaixaControle.msgAlert}"
                                                                     actionListener="#{FluxoCaixaControle.exportarPrevisto}">
                                                        <f:attribute name="tipo" value="xls"/>
                                                        <i class="fa-icon-file-excel-o font16"></i>
                                                    </a4j:commandLink>

                                                    <a4j:commandLink
                                                            rendered="#{FluxoCaixaControle.incluirParcelasRecorrenciaListaPrevisto}"
                                                            style="display: inline-block; margin:0 10px;"
                                                            id="linkExcelComParcelaRecorrencia"
                                                            styleClass="tooltipster icon linkAzul minibtn"
                                                            title="#{msg_aplic.prt_exportar_excel}"
                                                            actionListener="#{ExportadorListaControle.exportar}"
                                                            oncomplete="abrirPopup('../../UpdateServlet?op=downloadfile&file=#{ExportadorListaControle.fileName}&mimetype=application/vnd.ms-excel','Transacoes', 800,200);#{ExportadorListaControle.msgAlert}">
                                                        <f:attribute name="lista"
                                                                     value="#{FluxoCaixaControle.listaPrevisto}"/>
                                                        <f:attribute name="tipo" value="xls"/>
                                                        <f:attribute name="prefixo" value="FluxoCaixaPrevisto"/>
                                                        <f:attribute name="atributos"
                                                                     value="diaAnoApresentar=Dia,entradas=Entradas,entradasTipoAutorizacaoCartaoCredito=Cartão de crédito,entradasTipoAutorizacaoDebitoConta=Debito em conta,entradasTipoAutorizacaoBoletoBancario=Boleto bancário,entradasTipoAutorizacaoNenhum=Sem autorização de cobrança,saidas=Saídas,total=Total,saldo=Saldo"/>
                                                        <i class="fa-icon-file-excel-o font16"></i>
                                                    </a4j:commandLink>

                                                    <a4j:commandLink id="filtroDadosListaPelasContas"
                                                                     style="display: inline-block; margin:0 10px;"
                                                                     styleClass="tooltipster icon linkAzul minibtn"
                                                                     action="#{FluxoCaixaControle.abrirFiltroPrevisto}"
                                                                     reRender="modalPanelFiltrosFC"
                                                                     rendered="#{ConfiguracaoFinanceiroControle.confFinanceiro.usarMovimentacaoContas}"
                                                                     oncomplete="Richfaces.showModalPanel('modalPanelFiltrosFC');"
                                                                     title="Fitra os dados da lista, retornando todos os lançamentos da conta selecionada.">
                                                        <h:outputText styleClass="fa-icon-filter font16"
                                                                      style="#{empty FluxoCaixaControle.contasPrevisto ? '' : 'color: red;'}"/>
                                                    </a4j:commandLink>
                                                </h:panelGroup>
                                            </h:panelGroup>

                                            <h:panelGroup layout="block" styleClass="saldoInicial"
                                                          id="saldopr"
                                                          style="text-align: right;">

                                                <h:panelGroup layout="block"
                                                              rendered="#{FluxoCaixaControle.alterarSaldoInicial}" style="text-transform: none">
                                                    <div class="linhaFluxo dia separador tooltipster "
                                                         style="text-align: left;">

                                                        <span style="margin-left: 24vh;"
                                                              title="visualizar log geral da entidade"
                                                              class="tooltipster"
                                                              onclick="preencherValorChamarBotaoLog('formFC:btnLog')">
                                                                 Log
                                                            <i class="fa-icon-list"
                                                               style="padding-left: 7px; vertical-align: middle"
                                                               width="14px"
                                                               height="14px"></i>
                                                         </span>
                                                        <span style="margin-left: 25px;"
                                                              title="Clique para <b>fixar o saldo simulado</b> deste dia em diante"
                                                              class="tooltipster"
                                                              id="btnFixar"
                                                              onclick="fixar()">
                                                                 Fixar
                                                            <h:graphicImage width="14px"
                                                                            height="14px"
                                                                            style="padding-left: 7px;  vertical-align: middle;"
                                                                            alt="Fixar" value="/images/pct-paperclip.svg"/>
                                                         </span>

                                                        <script>
                                                            var interromperAtualizacao = false;
                                                            function fixar(){
                                                                interromperAtualizacao = true;
                                                                preencherValorChamarBotao('formFC:botaoFixarInicalSaldoAlterado', 'formFC:saldoInicialAlterado',
                                                                    document.getElementById('inputsaldoinicialalterado').value);
                                                            }


                                                            function atualizarSaldo(input){
                                                                interromperAtualizacao = false;
                                                                setTimeout(function(){
                                                                    atualizarOnChange(input);
                                                                    }, 800);

                                                            }

                                                            function atualizarOnChange(input){
                                                                if(!interromperAtualizacao){
                                                                    preencherValorChamarBotao('formFC:aplicarSaldoInicialAlterado', 'formFC:saldoInicialAlterado', input.value);
                                                                }

                                                            }
                                                        </script>

                                                        <span style="margin-left: 25px;"
                                                              title="${msg_aplic.prt_voltar_saldo_previsto}"
                                                              class="tooltipster"
                                                              id="botaoDesfazer"
                                                              onclick="preencherValorChamarBotao('formFC:desfazerSaldoRealizado', 'formFC:diaAplicar', this.value)">
                                                                 ${msg_aplic.prt_desfazer}
                                                            <i class="fa-icon-undo" style="padding-left: 7px; vertical-align: middle" width="14px"
                                                               height="14px"></i>
                                                         </span>

                                                        <span style="margin-left: 14px; border-left: 2px #ffffff solid; border-right-width: 10px">
                                                        </span>

                                                        <span style="margin-left: 14px;">
                                                                 SALDO INICIAL:
                                                         </span>

                                                        <span style="margin-right: 15px; float: right;">
                                                            <input type="text" class="inputTextClean tooltipster"
                                                                   onkeyup="mascara_com_sinal_negativo(this);"
                                                                   onchange="atualizarSaldo(this)"
                                                                   id="inputsaldoinicialalterado"
                                                                   style="margin-top: 6px;text-align: right;width: 130px;"
                                                                   title="${msg_aplic.prt_informe_saldo_desejado}"
                                                                   value="${FluxoCaixaControle.saldoInicialAlterado}"/>
                                                        </span>

                                                    </div>
                                                </h:panelGroup>

                                                <h:panelGroup layout="block"
                                                              rendered="#{!FluxoCaixaControle.alterarSaldoInicial}">
                                                    <div class="saldoInicial fluxomenu fechado dropDownMenu tudo">
                                                        <h:outputText value="#{msg_aplic.prt_saldoinicial}: "
                                                                      style="font-weight: bold"></h:outputText>
                                                        <div class="dropdown-fluxo col-vert-align naofecharmenufluxo tooltipster"
                                                             data-toggle="dropdown" title="Opções saldo inicial"
                                                             style="width: 100%;height: 100%;position: relative;display: inline-block;width: 70px;padding-left: 50px;
                                                             color: #29ABE2 !important;">

                                                            <h:outputText styleClass="saldoInicial"
                                                                          style="color: #29ABE2 !important; margin-right: 25px; margin-left: -40px;"
                                                                          id="saldoInicialBntPrev"
                                                                          value="#{FluxoCaixaControle.saldoInicialPrevistoApresentar}"></h:outputText>
                                                            <h:outputText style="margin-left: 10px;"
                                                                          styleClass="fa-icon-caret-down"></h:outputText>

                                                        </div>

                                                        <ul class="dropMenuUsuario dropdown-menu fluxo-menu naofecharmenufluxo"
                                                            id="dropdown-menu"
                                                            style="display: none; position: relative; margin-left: auto!important; max-width: 335px;text-transform: none"
                                                            role="menu" aria-labelledby="dropdownMenu">
                                                            <li>
                                                                <h:panelGroup layout="block"
                                                                              styleClass="alinhaMentoItemMenu"
                                                                              style="width: 85%;float: right;text-align: left;height: 100%;margin-left: 7.5%;">
                                                                    <a4j:commandLink style="color: #000;font-size: 14px;"
                                                                                     id="usarPrevMesAnteriorPrev"
                                                                                     action="#{FluxoCaixaControle.usarSaldoPrevistoOlhandoProSaldoPrevisto}"
                                                                                     reRender="formFC">
                                                                        <i class="fa-icon-calendar"></i>
                                                                        Usar saldo previsto do mês anterior
                                                                    </a4j:commandLink>
                                                                </h:panelGroup>
                                                            </li>

                                                            <div style="width: 100%;height: 1px;background-color: rgb(174, 187, 188);margin:10px 0px;"></div>
                                                            <li>
                                                                <h:panelGroup layout="block"
                                                                              styleClass="alinhaMentoItemMenu"
                                                                              style="width: 85%;float: right;text-align: left;height: 100%;margin-left: 7.5%;">
                                                                    <a4j:commandLink style="color: #000; font-size: 14px;"
                                                                                     id="usarRelMesAnteriorPrev"
                                                                                     action="#{FluxoCaixaControle.usarSaldoRealizadoOlhandoProSaldoPrevisto}"
                                                                                     reRender="formFC">
                                                                        <i class="fa-icon-calendar"></i>
                                                                        Usar saldo realizado do mês anterior
                                                                    </a4j:commandLink>
                                                                </h:panelGroup>
                                                            </li>

                                                            <div style="width: 100%;height: 1px;background-color: rgb(174, 187, 188);margin:10px 0px;"></div>
                                                            <li>
                                                                <h:panelGroup layout="block"
                                                                              styleClass="alinhaMentoItemMenu"
                                                                              style="width: 85%;float: right;text-align: left;height: 100%;margin-left: 7.5%;">

                                                                    <a4j:commandLink
                                                                            title="#{msg_aplic.prt_saldo_inicial_contas}"
                                                                            style="color: #000; font-size: 14px;"
                                                                            styleClass="tooltipster"
                                                                            action="#{FluxoCaixaControle.abrirSaldoPrevisto}"
                                                                            reRender="modalPanelFiltrosFC"
                                                                            id="usarSaldoContasPrev"
                                                                            oncomplete="Richfaces.showModalPanel('modalPanelFiltrosFC');">
                                                                        <h:outputText
                                                                                style="vertical-align: middle; color: #000;font-weight: bold;"
                                                                                styleClass="fa-icon-credit-card"/> Usar saldo de contas específicas
                                                                    </a4j:commandLink>


                                                                </h:panelGroup>
                                                            </li>
                                                            <div style="width: 100%;height: 1px;background-color: rgb(174, 187, 188);margin:10px 0px;"></div>
                                                            <li>

                                                                <h:panelGroup layout="block"
                                                                              styleClass="alinhaMentoItemMenu"
                                                                              style="width: 85%;float: right;text-align: left;height: 100%;margin-left: 7.5%;color: #000;">
                                                                    <a4j:commandLink style="color: #000; font-size: 14px;"
                                                                                     action="#{FluxoCaixaControle.alterarRenderizarSaldoInicial}"
                                                                                     id="usarOutroSaldoPrev"
                                                                                     reRender="formFC">
                                                                        <h:outputText
                                                                                style="vertical-align: middle;color: #000; font-weight: bold;"
                                                                                styleClass="fa-icon-edit"/> Usar outro saldo
                                                                    </a4j:commandLink>
                                                                </h:panelGroup>
                                                            </li>
                                                        </ul>
                                                    </div>
                                                </h:panelGroup>
                                                <script>
                                                    function fecharTodosFluxo() {
                                                        jQuery('.fluxomenu .dropDownMenu .fechado').css('display', 'none');
                                                        jQuery('.fluxomenu  .dropDownMenu .fechado').parent().find('.aberto').css('display', 'block');
                                                    }

                                                    jQuery('div.dropdown-fluxo').click(function () {
                                                        var el = jQuery(this).parent().children('ul');
                                                        //  console.log(jQuery(this).attr('class'));
                                                        jQuery('.dropdown-menu.fluxo-menu').slideUp('fast');
                                                        if (el.css('display') === 'block') {
                                                            jQuery('.dropdown-menu.fluxo-menu').parent().removeClass('open');
                                                        } else {
                                                            jQuery('.dropdown-menu.fluxo-menu').parent().removeClass('open');
                                                            el.slideDown('fast', function () {
                                                                el.parent().addClass('open');
                                                            });

                                                        }
                                                    });
                                                </script>
                                            </h:panelGroup>

                                        </h:panelGrid>
                                    </h:panelGroup>


                                    <a4j:commandLink id="abrirLancamento"
                                                     action="#{FluxoCaixaControle.editarLancamentoPopUp}"
                                                     oncomplete="abrirPopup('includes/include_LancamentosDF.jsp', 'TelaLançamento', 1200, 700);"
                                                     style="display: none">
                                    </a4j:commandLink>
                                    <h:inputHidden id="codigomc" value="#{FluxoCaixaControle.codigoMovConta}"/>

                                    <a4j:commandLink id="desfazerSaldoRealizado" reRender="panelFluxoCaixa"
                                                     action="#{FluxoCaixaControle.desfazerSaldoRealizado}"
                                                     oncomplete="#{FluxoCaixaControle.msgAlert}"
                                                     style="display: none">
                                    </a4j:commandLink>

                                    <a4j:commandLink id="btnLog"
                                                     action="#{FluxoCaixaControle.realizarConsultaLogObjetoSelecionado}"
                                                     oncomplete="abrirPopup('../../visualizadorLogForm.jsp', 'VisualizadorLog', 785, 595);">
                                    </a4j:commandLink>

                                    <a4j:commandLink id="aplicarSaldoAlterado" reRender="formFC"
                                                     action="#{FluxoCaixaControle.aplicarSaldoAlterado}"
                                                     oncomplete="#{FluxoCaixaControle.msgAlert}"
                                                     style="display: none">
                                    </a4j:commandLink>

                                    <a4j:commandLink id="aplicarSaldoInicialAlterado" reRender="formFC"
                                                     action="#{FluxoCaixaControle.aplicarSaldoInicialAlterado}"
                                                     oncomplete="#{FluxoCaixaControle.msgAlert}"
                                                     style="display: none">
                                    </a4j:commandLink>

                                    <a4j:commandLink id="botaoFixarSaldoAlterado" reRender="formFC"
                                                     action="#{FluxoCaixaControle.fixarSaldoAlterado}"
                                                     oncomplete="#{FluxoCaixaControle.mensagemNotificar}"
                                                     style="display: none">
                                    </a4j:commandLink>

                                    <a4j:commandLink id="botaoFixarInicalSaldoAlterado" reRender="formFC"
                                                     action="#{FluxoCaixaControle.fixarSaldoInicialAlterado}"
                                                     oncomplete="#{FluxoCaixaControle.mensagemNotificar}"
                                                     style="display: none">
                                    </a4j:commandLink>

                                    <a4j:commandLink id="aplicarSaldoRealizado" reRender="panelFluxoCaixa"
                                                     action="#{FluxoCaixaControle.aplicarSaldoRealizado}"
                                                     style="display: none">
                                    </a4j:commandLink>

                                    <a4j:commandLink id="irCliente"
                                                     style="display: none"
                                                     action="#{FluxoCaixaControle.irParaTelaCliente}"
                                                     oncomplete="abrirPopup('../../clienteNav.jsp?page=viewCliente', 'Cliente', 1024, 700);">
                                    </a4j:commandLink>

                                    <a4j:commandLink id="abrirSaldoContasRealizado"
                                            action="#{FluxoCaixaControle.abrirSaldoContasRealizado}"
                                                     reRender="modalPanelFiltrosFC"
                                                     oncomplete="Richfaces.showModalPanel('modalPanelFiltrosFC');">
                                    </a4j:commandLink>

                                    <h:inputHidden id="codigoPessoa" value="#{FluxoCaixaControle.codigoPessoa}"/>
                                    <h:inputHidden id="diaAplicar" value="#{FluxoCaixaControle.diaAplicar}"/>
                                    <h:inputHidden id="alterarSaldoInicial" value="#{FluxoCaixaControle.alterarSaldoInicial}"/>
                                    <h:inputHidden id="saldoInicialAlterado" value="#{FluxoCaixaControle.saldoInicialAlterado}"/>
                                    <h:inputHidden id="diaMostrarSaldo" value="#{FluxoCaixaControle.diaMostrarSaldo}"/>
                                    <h:inputHidden id="saldoAlterado" value="#{FluxoCaixaControle.saldoAlterado}"/>
                                    <h:inputHidden id="fixarSaldoAlterado" value="#{FluxoCaixaControle.fixarSaldoAlterado}"/>


                                    <h:panelGroup layout="block" styleClass="margin-box fc" id="listaPrevisto">
                                        <h:panelGroup rendered="#{not empty FluxoCaixaControle.listaPrevisto}">

                                            <div class="linhaFluxo cabecalho">
                                                <span class="colunaFluxo descricao">
                                                    <h:outputText value="#{msg_aplic.prt_msg_data}"/>
                                                </span>
                                                <span class="colunaFluxo">
                                                    <h:outputText value="#{msg_aplic.prt_entradas}"/>
                                                </span>
                                                <span class="colunaFluxo">
                                                    <h:outputText value="#{msg_aplic.prt_saidas}"/>
                                                </span>
                                                <span class="colunaFluxo">
                                                    <h:outputText value="#{msg_aplic.prt_total}"/>
                                                </span>
                                                <span class="colunaFluxo indicadorsaldo">
                                                    <h:outputText value="#{msg_aplic.prt_saldo}"/>
                                                </span>
                                            </div>


                                            <c:forEach var="prev" items="#{FluxoCaixaControle.listaPrevisto}"
                                                       varStatus="rk">
                                                <c:if test="${!FluxoCaixaControle.alterarSaldoInicial}">
                                                    <c:if test="${prev.diaApresentar eq FluxoCaixaControle.diaAplicar}">
                                                        <div class="linhaFluxo dia separador tooltipster ">
                                                        <span style="margin-left: 10px">
                                                            Simulação a partir daqui
                                                            <i class="fa-icon-chevron-down"></i>
                                                        </span>

                                                            <span style="margin-left: 16vh;"
                                                                  title="visualizar log geral da entidade"
                                                                  class="tooltipster"
                                                                  onclick="preencherValorChamarBotaoLog('formFC:btnLog')">
                                                                 Log
                                                            <i class="fa-icon-list"
                                                               style="padding-left: 7px; vertical-align: middle"
                                                               width="14px"
                                                               height="14px"></i>
                                                         </span>
                                                            <span style="margin-left: 25px;"
                                                                  title="Clique para <b>fixar o saldo simulado</b> deste dia em diante"
                                                                  class="tooltipster"
                                                                  onclick="preencherValorChamarBotao('formFC:botaoFixarSaldoAlterado', 'formFC:fixarSaldoAlterado', this.value)">
                                                                 Fixar
                                                                <h:graphicImage width="14px"
                                                                                height="14px"
                                                                                style="padding-left: 7px;  vertical-align: middle;"
                                                                                alt="Fixar" value="/images/pct-paperclip.svg"/>

                                                         </span>

                                                            <span style="margin-left: 25px;"
                                                                  title="${msg_aplic.prt_voltar_saldo_previsto}"
                                                                  class="tooltipster"
                                                                  onclick="preencherValorChamarBotao('formFC:desfazerSaldoRealizado', 'formFC:diaAplicar', '')">
                                                                 ${msg_aplic.prt_desfazer}
                                                             <i class="fa-icon-undo" style="padding-left: 7px; vertical-align: middle" width="14px"
                                                                height="14px"></i>
                                                         </span>
                                                            <span style="margin-right: 15px; float: right;">
                                                            <input type="text" class="inputTextClean tooltipster"
                                                                   onkeyup="mascara_com_sinal_negativo(this);"
                                                                   onchange="preencherValorChamarBotao('formFC:aplicarSaldoAlterado', 'formFC:saldoAlterado', this.value)"
                                                                   style="margin-top: 3px;text-align: right;width: 130px;"
                                                                   title="${msg_aplic.prt_informe_saldo_desejado}"
                                                                   value="${FluxoCaixaControle.saldoAlterado}"/>

                                                        </span>

                                                        </div>

                                                    </c:if>
                                                </c:if>

                                                <div class="${prev.dia eq FluxoCaixaControle.hojeZ ? 'hoje' : ''} ${prev.saldoRealizado eq null ? '' : 'modificado'} ${rk.index % 2 == 0 ? 'itemPar' : 'itemImpar'}">
                                                    <div class="${(prev.saldoRealizado eq null and prev.saldo < 0.0) or (prev.saldoRealizado ne null and prev.saldoRealizado < 0.0) ? 'negativo': ''} linhaFluxo dia"
                                                         onclick="abrir('.planoprev${prev.id}')"
                                                         ondblclick="abrirTudo('.planoprev${prev.id}', '.tudoprev${prev.id}')">
                                                        <span class="colunaFluxo descricao tooltipster diaSimples"
                                                              title="${msg_aplic.prt_clique_expandir}">
                                                                ${prev.diaApresentar}
                                                        </span>

                                                        <span class="colunaFluxo descricao tooltipster diaAno"
                                                              title="${msg_aplic.prt_clique_expandir}">
                                                                ${prev.diaAnoApresentar}
                                                        </span>
                                                        <span class="colunaFluxo tooltipster ${prev.entradaDiferente ? 'entradadiferente' : ''}"
                                                              title="${prev.tableFormasEntrada}">
                                                                ${prev.entrada_Apresentar}
                                                        </span>
                                                        <span class="colunaFluxo tooltipster ${prev.saidaDiferente ? 'saidadiferente' : ''}" 
                                                              title="${prev.tableFormasSaida}">
                                                                ${prev.saida_Apresentar}
                                                        </span>
                                                        <span class="colunaFluxo ">
                                                                ${prev.total_Apresentar}
                                                        </span>
                                                        <c:if test="${prev.saldoRealizado ne null}">
                                                            <span class="colunaFluxo tooltipster"
                                                                  style="font-weight: bold;">
                                                                    ${prev.saldoRealizado_Apresentar}
                                                            </span>
                                                        </c:if>
                                                        <c:if test="${prev.saldoRealizado eq null}">
                                                            <span class="colunaFluxo tooltipster"
                                                                  onclick="preencherValorChamarBotao('formFC:aplicarSaldoRealizado', 'formFC:diaAplicar', '${prev.diaApresentar}')"
                                                                  title="${msg_aplic.prt_aplicar_saldo_realizado}">
                                                                    ${prev.saldo_Apresentar}
                                                            </span>
                                                        </c:if>

                                                    </div>

                                                    <span class="containerInterior planoprev${prev.id}">
                                                    <c:forEach var="planoPrev" items="#{prev.listaFluxoCaixa}">
                                                        <div class="filhoPrev${prev.id}_${planoPrev.nivelPai} linhaFluxo planoContas ${planoPrev.index < 1 ? 'aberto':'fechado'} tudoprev${prev.id} pai ${planoPrev.saldo < 0.0 ? 'negativo':''}"
                                                             agrup=".filhoPrev${prev.id}_${planoPrev.nivelPai}"
                                                             onclick="abrirPlano('.lancprev${prev.id}_${planoPrev.id}', this, '.filhoPrev${prev.id}_${planoPrev.id}')">
                                                            <span class="colunaFluxo descricao">
                                                                <div style="display: inline-block; vertical-align:top;margin: 9px 0;">
                                                                    <i style="margin-left: ${planoPrev.index * 5}px"
                                                                       class="fa-icon-plus"></i>
                                                                    <i style="margin-left: ${planoPrev.index * 5}px"
                                                                       class="fa-icon-minus"></i>
                                                                </div>
                                                                <div style="display: inline-block; width: 80%;">
                                                                    <c:if test="${planoPrev.agrupador eq 'NAOINFORMADO'}">
                                                                        ${msg_aplic.prt_nao_informado_plano_contas}
                                                                    </c:if>
                                                                    <c:if test="${planoPrev.agrupador ne 'NAOINFORMADO'}">
                                                                        ${planoPrev.planoConta.descricao}
                                                                    </c:if>
                                                                </div>

                                                            </span>
                                                            <span class="colunaFluxo">
                                                                    ${planoPrev.entrada_Apresentar}
                                                            </span>
                                                            <span class="colunaFluxo">
                                                                    ${planoPrev.saida_Apresentar}
                                                            </span>
                                                            <span class="colunaFluxo">
                                                                    ${planoPrev.total_Apresentar}
                                                            </span>
                                                            <span class="colunaFluxo">
                                                                    ${planoPrev.saldo_Apresentar}
                                                            </span>
                                                        </div>
                                                        <span agrup=".filhoPrev${prev.id}_${planoPrev.nivelPai}"
                                                              class="containerInterior lancprev${prev.id}_${planoPrev.id} tudoprev${prev.id}">
                                                            <c:forEach var="lancPrev"
                                                                       items="${planoPrev.listaFluxoCaixa}">
                                                                <c:if test="${lancPrev.codigoMovConta > 0}">
                                                                    <div class="linhaFluxo lancamentos ${lancPrev.saldo < 0.0 ? 'negativo':''}"
                                                                         onclick="preencherValorChamarBotao('formFC:abrirLancamento', 'formFC:codigomc', ${lancPrev.codigoMovConta})">
                                                                        <span class="colunaFluxo descricao">
                                                                            <span style="margin-left: ${planoPrev.index * 5}px">${lancPrev.descricao}</span>
                                                                        </span>
                                                                    <span class="colunaFluxo">
                                                                            ${lancPrev.entrada_Apresentar}
                                                                    </span>
                                                                    <span class="colunaFluxo">
                                                                            ${lancPrev.saida_Apresentar}
                                                                    </span>
                                                                    <span class="colunaFluxo">
                                                                            ${lancPrev.total_Apresentar}
                                                                    </span>
                                                                    <span class="colunaFluxo">
                                                                            ${lancPrev.saldo_Apresentar}
                                                                    </span>
                                                                </div>
                                                                </c:if>
                                                                <c:if test="${lancPrev.codigoPessoa > 0}">
                                                                    <div class="linhaFluxo lancamentos ${lancPrev.saldo < 0.0 ? 'negativo':''}"
                                                                         onclick="preencherValorChamarBotao('formFC:irCliente', 'formFC:codigoPessoa', ${lancPrev.codigoPessoa})">
                                                                        <span class="colunaFluxo descricao">
                                                                            <span style="margin-left: ${planoPrev.index * 5}px">${lancPrev.descricao}</span>
                                                                        </span>
                                                                    <span class="colunaFluxo">
                                                                            ${lancPrev.entrada_Apresentar}
                                                                    </span>
                                                                    <span class="colunaFluxo">
                                                                            ${lancPrev.saida_Apresentar}
                                                                    </span>
                                                                    <span class="colunaFluxo">
                                                                            ${lancPrev.total_Apresentar}
                                                                    </span>
                                                                    <span class="colunaFluxo">
                                                                            ${lancPrev.saldo_Apresentar}
                                                                    </span>
                                                                </div>
                                                                </c:if>
                                                                <%--VENDA CONSUMIDOR--%>
                                                                <c:if test="${lancPrev.codigoPessoa == 0}">
                                                                    <div title="Venda para Consumidor"
                                                                         style="cursor:default !important;"
                                                                         class="linhaFluxo tooltipster lancamentos ${lancPrev.saldo < 0.0 ? 'negativo':''}">
                                                                        <span class="colunaFluxo descricao">
                                                                            <span style="margin-left: ${planoPrev.index * 5}px">${lancPrev.descricao}</span>
                                                                        </span>
                                                                    <span class="colunaFluxo">
                                                                            ${lancPrev.entrada_Apresentar}
                                                                    </span>
                                                                    <span class="colunaFluxo">
                                                                            ${lancPrev.saida_Apresentar}
                                                                    </span>
                                                                    <span class="colunaFluxo">
                                                                            ${lancPrev.total_Apresentar}
                                                                    </span>
                                                                    <span class="colunaFluxo">
                                                                            ${lancPrev.saldo_Apresentar}
                                                                    </span>
                                                                </div>
                                                                </c:if>

                                                            </c:forEach>
                                                        </span>
                                                    </c:forEach>
                                                        </span>
                                                </div>
                                            </c:forEach>

                                            <div class="linhaFluxo rodape" style="position: static; width: 100%">
                                                <span class="colunaFluxo descricao">
                                                    <h:outputText value="#{msg_aplic.prt_total}"/>
                                                </span>
                                                <span class="colunaFluxo">
                                                    <h:outputText
                                                            value="#{FluxoCaixaControle.totalPrevisto.entrada_Apresentar}"/>
                                                </span>
                                                <span class="colunaFluxo">
                                                    <h:outputText
                                                            value="#{FluxoCaixaControle.totalPrevisto.saida_Apresentar}"/>
                                                </span>
                                                <span class="colunaFluxo">
                                                    <h:outputText
                                                            value="#{FluxoCaixaControle.totalPrevisto.total_Apresentar}"/>
                                                </span>
                                                <span class="colunaFluxo">
                                                    <h:outputText
                                                            id="saldoFinal"
                                                            value="#{FluxoCaixaControle.totalPrevisto.saldo_Apresentar}"/>
                                                </span>
                                            </div>

                                        </h:panelGroup>
                                    </h:panelGroup>
                                </h:panelGroup>


                                <!-- realizado -->
                                <h:panelGroup layout="block" styleClass="container-box tabelaModoTable fcrealizado"
                                              rendered="#{!FluxoCaixaControle.modoGrafico}"
                                              style="width: calc(50% - 68px); display: inline-block; margin-left: 20px !important; margin-top: 10px !important">
                                    <h:panelGroup styleClass="container-box-header" layout="block">
                                        <h:panelGroup layout="block" styleClass="margin-box"
                                                      style="display:flex; justify-content:space-between; align-items: center; height: 59px; margin: 0;">

                                            <h:panelGroup styleClass="text" layout="block">
                                                <h:outputText styleClass="container-header-titulo"
                                                              value="#{msg_aplic.prt_fluxo_caixa_realizado}"/>

                                            </h:panelGroup>

                                        </h:panelGroup>


                                    </h:panelGroup>

                                    <h:panelGroup layout="block" styleClass="sliderOpcoes" id="sliderOpcoesRealizado">
                                        <a4j:repeat var="mr" value="#{FluxoCaixaControle.mesesRealizado}"
                                                    rendered="#{!FluxoCaixaControle.datasVinculadas}">

                                            <h:panelGroup layout="block"
                                                          styleClass="opcao #{mr.codigoString eq FluxoCaixaControle.mesRealizado ? 'selecionado' : ''}">
                                                <a4j:commandLink reRender="panelFluxoCaixa"
                                                                 action="#{FluxoCaixaControle.consultarRealizadoMes}">
                                                    <h:outputText styleClass="label" value="#{mr.label}"></h:outputText>
                                                </a4j:commandLink>
                                                <h:outputText styleClass="fa-icon-caret-down"></h:outputText>
                                                <a4j:commandLink reRender="panelFluxoCaixa"
                                                                 styleClass="indicador"
                                                                 action="#{FluxoCaixaControle.consultarRealizadoMes}">

                                                </a4j:commandLink>

                                                <h:outputText styleClass="fa-icon-caret-up"></h:outputText>

                                            </h:panelGroup>


                                        </a4j:repeat>
                                    </h:panelGroup>

                                    <h:panelGroup styleClass="container-box-header fc" layout="block"
                                                  id="painelbtnsrealizados">
                                        <h:panelGrid styleClass="btns" width="100%" columnClasses="fecharmenucadastromenufluxo"
                                                     style="width: 100%;">
                                            <h:panelGroup>
                                                <%--<h:outputText rendered="#{!FluxoCaixaControle.datasVinculadas}"
                                                              value="#{msg_aplic.prt_de}" styleClass="tituloCampos"/>

                                                <h:panelGroup styleClass="dateTimeCustom" style="display: inline-block"
                                                              rendered="#{!FluxoCaixaControle.datasVinculadas}">
                                                    <rich:calendar value="#{FluxoCaixaControle.dataInicialRealizado}"
                                                                   inputSize="10"
                                                                   inputClass="form"
                                                                   oninputblur="blurinput(this);"
                                                                   oninputfocus="focusinput(this);"
                                                                   oninputchange="return validar_Data(this.id);"
                                                                   datePattern="dd/MM/yyyy"
                                                                   buttonIcon="/imagens_flat/calendar-button.svg"
                                                                   enableManualInput="true"
                                                                   zindex="2"
                                                                   showWeeksBar="false"/>
                                                </h:panelGroup>
                                                <h:outputText rendered="#{!FluxoCaixaControle.datasVinculadas}"
                                                              value="#{msg_aplic.prt_ate}" styleClass="tituloCampos"/>
                                                <h:panelGroup rendered="#{!FluxoCaixaControle.datasVinculadas}"
                                                              styleClass="dateTimeCustom" style="display: inline-block">
                                                    <rich:calendar value="#{FluxoCaixaControle.dataFinalRealizado}"
                                                                   inputSize="10"
                                                                   inputClass="form"
                                                                   oninputblur="blurinput(this);"
                                                                   oninputfocus="focusinput(this);"
                                                                   oninputchange="return validar_Data(this.id);"
                                                                   datePattern="dd/MM/yyyy"
                                                                   buttonIcon="/imagens_flat/calendar-button.svg"
                                                                   enableManualInput="true"
                                                                   zindex="2"
                                                                   showWeeksBar="false"/>
                                                </h:panelGroup>--%>

                                                <a4j:commandLink id="exportarPdfRealizado"
                                                                 style="display: inline-block; margin: 0 10px;"
                                                                 styleClass="tooltipster icon linkAzul minibtn"
                                                                 title="Imprimir PDF"
                                                                 oncomplete="#{FluxoCaixaControle.msgAlert}"
                                                                 actionListener="#{FluxoCaixaControle.exportarRealizado}"
                                                >
                                                    <f:attribute name="tipo" value="pdf"/>
                                                    <i class="fa-icon-print font16"></i>
                                                </a4j:commandLink>

                                                <a4j:commandLink id="exportarExcelRealizado"
                                                                 style="display: inline-block; margin: 0 10px;"
                                                                 styleClass="tooltipster icon linkAzul minibtn"
                                                                 title="Imprimir Excel"
                                                                 oncomplete="#{FluxoCaixaControle.msgAlert}"
                                                                 actionListener="#{FluxoCaixaControle.exportarRealizado}"
                                                >
                                                    <f:attribute name="tipo" value="xls"/>
                                                    <i class="fa-icon-file-excel-o font16"></i>
                                                </a4j:commandLink>

                                                <a4j:commandLink style="display: inline-block; margin: 0 10px;"
                                                                 styleClass="tooltipster icon linkAzul minibtn"
                                                                 action="#{FluxoCaixaControle.abrirFiltroRealizado}"
                                                                 reRender="modalPanelFiltrosFC"
                                                                 oncomplete="Richfaces.showModalPanel('modalPanelFiltrosFC');"
                                                                 rendered="#{!FluxoCaixaControle.datasVinculadas and ConfiguracaoFinanceiroControle.confFinanceiro.usarMovimentacaoContas}"
                                                                 title="#{msg_aplic.FILTRAR}" accesskey="2">
                                                    <h:outputText styleClass="fa-icon-filter font16"
                                                                  style="#{empty FluxoCaixaControle.contasRealizado ? '' : 'color: red;'}"/>
                                                </a4j:commandLink>

                                            </h:panelGroup>

                                            <h:panelGroup layout="block" styleClass="saldoInicial" id="saldore"
                                                          style="text-align: right">


                                                <h:panelGroup layout="block"
                                                              rendered="#{FluxoCaixaControle.alterarSaldoInicial}"
                                                              style="text-transform: none">
                                                    <div class="linhaFluxo dia separador tooltipster "
                                                         style="text-align: left;">

                                                        <span style="margin-left: 25px;"
                                                              title="${msg_aplic.prt_voltar_saldo_previsto}"
                                                              class="tooltipster"
                                                              onclick="preencherValorChamarBotao('formFC:desfazerSaldoRealizado', 'formFC:diaAplicar', '')">
                                                                 ${msg_aplic.prt_desfazer}
                                                             <i class="fa-icon-undo" style="padding-left: 7px; vertical-align: middle" width="14px"
                                                                height="14px"></i>
                                                         </span>

                                                        <span style="margin-right: 15px; float: right;">
                                                            <input type="text" class="inputTextClean tooltipster"
                                                                   onkeyup="mascara_com_sinal_negativo(this);"
                                                                   onchange="preencherValorChamarBotao('formFC:aplicarSaldoInicialAlterado', 'formFC:saldoInicialAlterado', this.value)"
                                                                   style="margin-top: 6px;text-align: right;width: 130px;"
                                                                   title="${msg_aplic.prt_informe_saldo_desejado}"
                                                                   value="${FluxoCaixaControle.saldoInicialAlterado}"/>
                                                        </span>

                                                    </div>
                                                </h:panelGroup>

                                                <h:panelGroup layout="block" style="margin-top: 13px;"
                                                              rendered="#{!FluxoCaixaControle.alterarSaldoInicial}">
                                                    <div class="saldoInicial fluxomenu2 fechado dropDownMenu tudo">
                                                        <h:outputText value="#{msg_aplic.prt_saldoinicial}: "
                                                                      style="font-weight: bold; font-size: 14px"></h:outputText>
                                                        <div class="dropdown-fluxo col-vert-align naofecharmenufluxo tooltipster"
                                                             data-toggle="dropdown" title="Opções saldo inicial"
                                                             style="width: 100%;height: 100%;position: relative;display: inline-block;width: 70px;padding-left: 50px;
                                                             color: #29ABE2 !important;">

                                                            <h:outputText styleClass="saldoInicial"
                                                                          style="color: #29ABE2 !important; margin-right: 25px; margin-left: -40px;"
                                                                          id="saldoInicialBntRel"
                                                                          value="#{FluxoCaixaControle.saldoInicialRealizadoApresentar}"></h:outputText>
                                                            <h:outputText style="margin-left: 10px;"
                                                                          styleClass="fa-icon-caret-down"></h:outputText>

                                                        </div>
                                                        <ul class="dropMenuUsuario dropdown-menu fluxo-menu2 naofecharmenufluxo"
                                                            id="dropdown-menu2"
                                                            style="display: none; position: relative; margin-left: auto!important; max-width: 335px;text-transform: none"
                                                            role="menu" aria-labelledby="dropdownMenu">
                                                            <li>
                                                                <h:panelGroup layout="block"
                                                                              styleClass="alinhaMentoItemMenu"
                                                                              style="width: 85%;float: right;text-align: left;height: 100%;margin-left: 7.5%;">
                                                                    <a4j:commandLink style="color: #000; font-size: 14px;"
                                                                                     id="usarPrevMesAnteriorRel"
                                                                                     action="#{FluxoCaixaControle.usarSaldoPrevistoOlhandoProSaldoRealizado}"
                                                                                     reRender="formFC">
                                                                        <i class="fa-icon-calendar"></i>
                                                                        Usar saldo previsto do mês anterior
                                                                    </a4j:commandLink>
                                                                </h:panelGroup>
                                                            </li>

                                                            <div style="width: 100%;height: 1px;background-color: rgb(174, 187, 188);margin:10px 0px;"></div>
                                                            <li>
                                                                <h:panelGroup layout="block"
                                                                              styleClass="alinhaMentoItemMenu"
                                                                              style="width: 85%;float: right;text-align: left;height: 100%;margin-left: 7.5%;">
                                                                    <a4j:commandLink style="color: #000; font-size: 14px;"
                                                                                     id="usarRelMesAnteriorRel"
                                                                                     action="#{FluxoCaixaControle.usarSaldoRealizadoOlhandoProSaldoRealizado}"
                                                                                     reRender="formFC">
                                                                        <i class="fa-icon-calendar"></i>
                                                                        Usar saldo realizado do mês anterior
                                                                    </a4j:commandLink>
                                                                </h:panelGroup>
                                                            </li>

                                                            <div style="width: 100%;height: 1px;background-color: rgb(174, 187, 188);margin:10px 0px;"></div>
                                                            <li>
                                                                <h:panelGroup layout="block"
                                                                              styleClass="alinhaMentoItemMenu"
                                                                              style="width: 85%;float: right;text-align: left;height: 100%;margin-left: 7.5%;">

                                                                    <a4j:commandLink
                                                                            title="#{msg_aplic.prt_saldo_inicial_contas}"
                                                                            styleClass="tooltipster"
                                                                            style="color: #000; font-size: 14px;"
                                                                            action="#{FluxoCaixaControle.abrirSaldoRealizado}"
                                                                            reRender="modalPanelFiltrosFC"
                                                                            id="usarSaldoContasRel"
                                                                            oncomplete="Richfaces.showModalPanel('modalPanelFiltrosFC');">
                                                                        <h:outputText
                                                                                style="vertical-align: middle; color: #000;"
                                                                                styleClass="fa-icon-credit-card"/> Usar saldo de contas específicas
                                                                    </a4j:commandLink>


                                                                </h:panelGroup>
                                                            </li>
                                                            <div style="width: 100%;height: 1px;background-color: rgb(174, 187, 188);margin:10px 0px;"></div>
                                                            <li>
                                                                <h:panelGroup layout="block"
                                                                              styleClass="alinhaMentoItemMenu"
                                                                              style="width: 85%;float: right;text-align: left;height: 100%;margin-left: 7.5%;color: #000;">
                                                                    <a4j:commandLink style="color: #000;font-size: 14px;"
                                                                                     action="#{FluxoCaixaControle.alterarRenderizarSaldoInicial}"
                                                                                     id="usarOutroSaldoRel"
                                                                                     reRender="formFC">
                                                                        <h:outputText
                                                                                style="vertical-align: middle;color: #000; font-weight: bold;"
                                                                                styleClass="fa-icon-edit"/> Usar outro saldo
                                                                    </a4j:commandLink>
                                                                </h:panelGroup>
                                                            </li>
                                                        </ul>
                                                    </div>
                                                </h:panelGroup>
                                                <script>
                                                    function fecharTodosFluxo2() {
                                                        jQuery('.fluxomenu2 .dropDownMenu .fechado').css('display', 'none');
                                                        jQuery('.fluxomenu2  .dropDownMenu .fechado').parent().find('.aberto').css('display', 'block');
                                                    }

                                                    jQuery('div.dropdown-fluxo').click(function () {
                                                        var el = jQuery(this).parent().children('ul');
                                                        //  console.log(jQuery(this).attr('class'));
                                                        jQuery('.dropdown-menu2.fluxo-menu2').slideUp('fast');
                                                        if (el.css('display') === 'block') {
                                                            jQuery('.dropdown-menu2.fluxo-menu2').parent().removeClass('open');
                                                        } else {
                                                            jQuery('.dropdown-menu2.fluxo-menu2').parent().removeClass('open');
                                                            el.slideDown('fast', function () {
                                                                el.parent().addClass('open');
                                                            });

                                                        }
                                                    });
                                                </script>
                                            </h:panelGroup>
                                        </h:panelGrid>
                                    </h:panelGroup>
                                    <h:panelGroup layout="block" styleClass="margin-box fc" id="listaRealizado">
                                        <h:panelGroup rendered="#{not empty FluxoCaixaControle.listaRealizado}">

                                            <div class="linhaFluxo cabecalho">
                                                <span class="colunaFluxo descricao">
                                                    <h:outputText value="#{msg_aplic.prt_msg_data}"/>
                                                </span>
                                                <span class="colunaFluxo">
                                                    <h:outputText value="#{msg_aplic.prt_entradas}"/>
                                                </span>
                                                <span class="colunaFluxo">
                                                    <h:outputText value="#{msg_aplic.prt_saidas}"/>
                                                </span>
                                                <span class="colunaFluxo">
                                                    <h:outputText value="#{msg_aplic.prt_total}"/>
                                                </span>
                                                <span class="colunaFluxo">
                                                    <h:outputText value="#{msg_aplic.prt_saldo}"/>
                                                </span>
                                            </div>


                                            <c:forEach var="real" items="#{FluxoCaixaControle.listaRealizado}"
                                                       varStatus="rkr">
                                                <c:if test="${!FluxoCaixaControle.alterarSaldoInicial}">
                                                    <c:if test="${real.diaApresentar eq FluxoCaixaControle.diaAplicar}">
                                                        <div class="linhaFluxo dia separador tooltipster ">
                                                         <span style="margin-left: 10px;"
                                                               title="${msg_aplic.prt_voltar_saldo_previsto}"
                                                               class="tooltipster"
                                                               onclick="preencherValorChamarBotao('formFC:desfazerSaldoRealizado', 'formFC:diaAplicar', '')">
                                                                 ${msg_aplic.prt_desfazer}
                                                             <i class="fa-icon-undo"></i>
                                                         </span>

                                                            <span style="margin-right: 10px; float: right;">
                                                                    ${FluxoCaixaControle.saldoAlterado}
                                                            </span>

                                                        </div>

                                                    </c:if>
                                                </c:if>

                                                <div class="${real.dia eq FluxoCaixaControle.hojeZ ? 'hoje' : ''} ${rkr.index % 2 == 0 ? 'itemPar' : 'itemImpar'}">
                                                    <div class="linhaFluxo dia ${(real.saldoRealizado eq null and real.saldo < 0.0) or (real.saldoRealizado ne null and real.saldoRealizado < 0.0) ? 'negativo':''}"
                                                         onclick="abrir('.planoreal${real.id}')"
                                                         ondblclick="abrirTudo('.planoreal${real.id}', '.tudoreal${real.id}')">
                                                        <span class="colunaFluxo descricao tooltipster diaSimples"
                                                              title="${msg_aplic.prt_clique_expandir}">
                                                                ${real.diaApresentar}
                                                        </span>
                                                        <span class="colunaFluxo descricao tooltipster diaAno"
                                                              title="${msg_aplic.prt_clique_expandir}">
                                                                ${real.diaAnoApresentar}
                                                        </span>
                                                        <span class="colunaFluxo tooltipster ${real.entradaDiferente ? 'entradadiferente' : ''}"
                                                              title="${real.tableFormasEntrada}">
                                                                ${real.entrada_Apresentar}
                                                        </span>
                                                        <span class="colunaFluxo tooltipster ${real.saidaDiferente ? 'saidadiferente' : ''}"  
                                                              title="${real.tableFormasSaida}">
                                                                ${real.saida_Apresentar}
                                                        </span>
                                                        <span class="colunaFluxo" >
                                                                ${real.total_Apresentar}
                                                        </span>
                                                        <c:if test="${real.saldoRealizado ne null}">
                                                            <span class="colunaFluxo tooltipster"
                                                                  style="font-weight: bold;">
                                                                    ${real.saldoRealizado_Apresentar}
                                                            </span>
                                                        </c:if>
                                                        <c:if test="${real.saldoRealizado eq null}">
                                                        <span class="colunaFluxo tooltipster"
                                                              title="${msg_aplic.prt_mostrar_saldo_realizado_contas}"
                                                                onclick="preencherValorChamarBotao('formFC:abrirSaldoContasRealizado', 'formFC:diaMostrarSaldo', '${real.diaApresentar}')">
                                                                    ${real.saldo_Apresentar}
                                                        </span>
                                                        </c:if>
                                                    </div>

                                                    <span class="containerInterior planoreal${real.id}">
                                                    <c:forEach var="planoReal" items="#{real.listaFluxoCaixa}">
                                                        <div class="${planoReal.saldo < 0.0 ? 'negativo':''} filhoReal${real.id}_${planoReal.nivelPai} linhaFluxo planoContas ${planoReal.index < 1 ? 'aberto':'fechado'} tudoreal${real.id} pai ${planoReal.index < 2 ? 'pai':''}"
                                                             agrup=".filhoReal${real.id}_${planoReal.nivelPai}"
                                                             onclick="abrirPlano('.lancreal${real.id}_${planoReal.id}', this, '.filhoReal${real.id}_${planoReal.id}')">
                                                            <span class="colunaFluxo descricao">
                                                                <div style="display: inline-block; vertical-align:top;margin: 9px 0;">
                                                                    <i style="margin-left: ${planoReal.index * 5}px"
                                                                       class="fa-icon-plus"></i>
                                                                    <i style="margin-left: ${planoReal.index * 5}px"
                                                                       class="fa-icon-minus"></i>
                                                                </div>
                                                                <div style="display: inline-block; width: 80%;">
                                                                    <c:if test="${planoPrev.agrupador eq 'NAOINFORMADO'}">
                                                                        ${msg_aplic.prt_nao_informado_plano_contas}
                                                                    </c:if>
                                                                    <c:if test="${planoPrev.agrupador ne 'NAOINFORMADO'}">
                                                                        ${planoReal.planoConta.descricao}
                                                                    </c:if>
                                                                </div>
                                                            </span>
                                                            <span class="colunaFluxo">
                                                                    ${planoReal.entrada_Apresentar}
                                                            </span>
                                                            <span class="colunaFluxo">
                                                                    ${planoReal.saida_Apresentar}
                                                            </span>
                                                            <span class="colunaFluxo">
                                                                    ${planoReal.total_Apresentar}
                                                            </span>
                                                            <span class="colunaFluxo">
                                                                    ${planoReal.saldo_Apresentar}
                                                            </span>
                                                        </div>
                                                        <span class="containerInterior lancreal${real.id}_${planoReal.id} tudoreal${real.id}"
                                                              agrup=".filhoReal${real.id}_${planoReal.nivelPai}">
                                                            <c:forEach var="lancReal"
                                                                       items="${planoReal.listaFluxoCaixa}">
                                                                <c:if test="${lancReal.codigoMovConta > 0}">
                                                                    <div class="linhaFluxo lancamentos ${lancReal.saldo < 0.0 ? 'negativo':''}"
                                                                         onclick="preencherValorChamarBotao('formFC:abrirLancamento', 'formFC:codigomc', ${lancReal.codigoMovConta})">
                                                                        <span class="colunaFluxo descricao">
                                                                            <span style="margin-left: ${lancReal.index * 5}px">${lancReal.descricao}</span>
                                                                        </span>
                                                                    <span class="colunaFluxo">
                                                                            ${lancReal.entrada_Apresentar}
                                                                    </span>
                                                                    <span class="colunaFluxo">
                                                                            ${lancReal.saida_Apresentar}
                                                                    </span>
                                                                    <span class="colunaFluxo">
                                                                            ${lancReal.total_Apresentar}
                                                                    </span>
                                                                    <span class="colunaFluxo">
                                                                            ${lancReal.saldo_Apresentar}
                                                                    </span>
                                                                </div>
                                                                </c:if>
                                                                <c:if test="${lancReal.codigoPessoa > 0}">
                                                                    <div class="linhaFluxo lancamentos ${lancReal.saldo < 0.0 ? 'negativo':''}"
                                                                         onclick="preencherValorChamarBotao('formFC:irCliente', 'formFC:codigoPessoa', ${lancReal.codigoPessoa})">
                                                                        <span class="colunaFluxo descricao">
                                                                            <span style="margin-left: ${lancReal.index * 5}px">${lancReal.descricao}</span>
                                                                        </span>
                                                                    <span class="colunaFluxo">
                                                                            ${lancReal.entrada_Apresentar}
                                                                    </span>
                                                                    <span class="colunaFluxo">
                                                                            ${lancReal.saida_Apresentar}
                                                                    </span>
                                                                    <span class="colunaFluxo">
                                                                            ${lancReal.total_Apresentar}
                                                                    </span>
                                                                    <span class="colunaFluxo">
                                                                            ${lancReal.saldo_Apresentar}
                                                                    </span>
                                                                </div>
                                                                </c:if>
                                                                <%--VENDA CONSUMIDOR--%>
                                                                <c:if test="${lancReal.codigoPessoa == 0}">
                                                                <div title="Venda para Consumidor"
                                                                     style="cursor:default !important;"
                                                                     class="linhaFluxo tooltipster lancamentos ${lancReal.saldo < 0.0 ? 'negativo':''}">
                                                                        <span class="colunaFluxo descricao">
                                                                            <span style="margin-left: ${lancReal.index * 5}px">${lancReal.descricao}</span>
                                                                        </span>
                                                                    <span class="colunaFluxo">
                                                                            ${lancReal.entrada_Apresentar}
                                                                    </span>
                                                                    <span class="colunaFluxo">
                                                                            ${lancReal.saida_Apresentar}
                                                                    </span>
                                                                    <span class="colunaFluxo">
                                                                            ${lancReal.total_Apresentar}
                                                                    </span>
                                                                    <span class="colunaFluxo">
                                                                            ${lancReal.saldo_Apresentar}
                                                                    </span>
                                                                </div>
                                                                </c:if>

                                                            </c:forEach>
                                                        </span>
                                                    </c:forEach>
                                                        </span>
                                                </div>
                                            </c:forEach>
                                            <div class="linhaFluxo rodape" style="position: static; width: 100%" >
                                                <span class="colunaFluxo descricao">
                                                    <h:outputText value="#{msg_aplic.prt_total}"/>
                                                </span>
                                                <span class="colunaFluxo">
                                                    <h:outputText
                                                            value="#{FluxoCaixaControle.totalRealizado.entrada_Apresentar}"/>
                                                </span>
                                                <span class="colunaFluxo">
                                                    <h:outputText
                                                            value="#{FluxoCaixaControle.totalRealizado.saida_Apresentar}"/>
                                                </span>
                                                <span class="colunaFluxo">
                                                    <h:outputText
                                                            value="#{FluxoCaixaControle.totalRealizado.total_Apresentar}"/>
                                                </span>
                                                <span class="colunaFluxo">
                                                    <h:outputText
                                                            value="#{FluxoCaixaControle.totalRealizado.saldo_Apresentar}"/>
                                                </span>
                                            </div>
                                        </h:panelGroup>


                                    </h:panelGroup>

                                </h:panelGroup>
                                <script type="text/javascript">
                                    carregarTooltipster();
                                </script>
                            </h:panelGroup>
                        </h:form>
                    </h:panelGroup>
                    <h:form id="formMenu" style="display: flex;align-items: stretch;flex-direction: row-reverse;">
                        <jsp:include page="includes/include_menu_relatorios.jsp" flush="true"/>
                    </h:form>
                </h:panelGroup>
            </h:panelGroup>
        </h:panelGroup>

        <jsp:include page="../../include_rodape_flat.jsp" flush="true"/>
    </h:panelGroup>
    <script type="text/javascript">
        carregarTooltipster();
    </script>
    </body>

    </html>



    <rich:modalPanel id="modalPanelFiltrosFC" trimOverlayedElements="false" autosized="false"
                     shadowOpacity="true" width="600" height="540"
                     styleClass="novaModal"
                     onshow="carregarTooltipster()">

        <f:facet name="header">
            <h:panelGroup>
                <h:outputText
                        value="#{FluxoCaixaControle.visualizarSaldo ? msg_aplic.prt_visualizar_saldo_contas : msg_aplic.prt_filtros_fc_contas}"/>
            </h:panelGroup>
        </f:facet>
        <f:facet name="controls">
            <h:panelGroup>
                <h:outputText
                        styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign btnSelecionarModalidade"
                        id="hidelinkModalFiltrosFC"/>
                <rich:componentControl for="modalPanelFiltrosFC"
                                       attachTo="hidelinkModalFiltrosFC" operation="hide" event="onclick"/>
            </h:panelGroup>
        </f:facet>
        <a4j:form id="formFiltrosFC" ajaxSubmit="true">
            <div>
                <a4j:commandLink rendered="#{!FluxoCaixaControle.visualizarSaldo}" styleClass="linkAzul"
                                 value="#{msg_aplic.prt_PerfilAcesso_marcarTodos}" reRender="formFiltrosFC"
                                 action="#{FluxoCaixaControle.marcarTodas}"></a4j:commandLink>
                <a4j:commandLink rendered="#{!FluxoCaixaControle.visualizarSaldo}" styleClass="linkAzul"
                                 style="margin-left: 10px" value="#{msg_aplic.prt_contasDesmarcarTodos}"
                                 reRender="formFiltrosFC"
                                 action="#{FluxoCaixaControle.desmarcarTodas}"></a4j:commandLink>
            </div>

            <h:panelGroup rendered="#{FluxoCaixaControle.visualizarSaldo}"
                          layout="block"
                          style="text-align: right; padding: 10px">
                <h:outputText value="Saldo até #{FluxoCaixaControle.dtInicioApresentar}"/>
            </h:panelGroup>

            <h:panelGroup layout="block" styleClass="contasFiltro" id="panelGeralContas" style="overflow: auto; max-height: 388px;">
                <a4j:repeat value="#{FluxoCaixaControle.tipos}" id="repeatPanelContas" var="t" rowKeyVar="idx">
                    <h:panelGroup layout="block" styleClass="contentTipo" id="panelContas"
                                  rendered="#{not empty t.label or !FluxoCaixaControle.visualizarSaldo}">
                        <h:panelGroup layout="block" style="margin: 0 5px; height: 30px; line-height: 30px">
                            <h:panelGroup styleClass="chk-fa-container inline " style="margin: 0 5px"

                                            layout="block">
                                <h:selectBooleanCheckbox value="#{t.selecionado}" styleClass="princ#{t.codigoString}"
                                onclick="marcarTodos(this, '.#{t.codigoString}');"></h:selectBooleanCheckbox>
                                <span style="top: -1px;"/>
                            </h:panelGroup>
                            <h:outputText value="#{empty t.label ? msg_aplic.prt_nao_movimentados : t.label}"
                                          style="color:#333;font-size:9pt;font-weight:bold;"/>


                        </h:panelGroup>
                        <rich:separator width="100%" height="1px"/>
                        <rich:dataTable value="#{FluxoCaixaControle.contasFiltro[t.codigoString]}" width="100%"
                                        id="tableContas"
                                        var="conta"
                                        styleClass="gridConta" style="border: none;" rowClasses="linhaPar, linhaImpar">
                            <h:column id="columnId">

                                <div class="chk-fa-container inline " style="margin: 0 5px">
                                    <h:selectBooleanCheckbox value="#{conta.contaEscolhida}" styleClass="#{t.codigoString}"
                                                             id="checkteste"
                                                             onclick="checarSeTodos('#{t.codigoString}')"></h:selectBooleanCheckbox>
                                    <span style="top: 1px;"/>
                                </div>
                                <h:outputText value="#{conta.descricao}"
                                              rendered="#{conta.codigo > 0}"
                                              style="color:#777;font-size:9pt;"
                                              styleClass="text"/>
                                <h:outputText styleClass="tooltipster text" value="#{msg_aplic.prt_nao_movimentados}"
                                              style="color:#777;font-size:9pt;"
                                              title="#{msg_aplic.prt_nao_movimentados_tip}"
                                              rendered="#{conta.codigo < 0 and !FluxoCaixaControle.visualizarSaldo}"/>

                                <h:outputText value="#{conta.saldoAtualApresentar}"
                                              id="saldoCheckBox"
                                              rendered="#{FluxoCaixaControle.visualizarSaldo}"
                                              style="color:#0f4c6b;font-size:9pt; float: right; color: #{conta.saldoAtual < 0.0 ? 'red' : 'green'}"
                                              styleClass="text"/>
                            </h:column>
                        </rich:dataTable>
                    </h:panelGroup>
                </a4j:repeat>
            </h:panelGroup>
            <a4j:commandLink reRender="panelFluxoCaixa"
                             oncomplete="Richfaces.hideModalPanel('modalPanelFiltrosFC');"
                             action="#{FluxoCaixaControle.fecharFiltroContas}"
                             id="salvarSaldoContas"
                             styleClass="pure-button pure-button-primary" style="margin-top: 15px; margin-left:248px">
                <h:outputText style="font-size: 14px"
                              value="Salvar"/>
            </a4j:commandLink>

        </a4j:form>
    </rich:modalPanel>

    <%@include file="includes/include_modal_consultarCaixa.jsp" %>

</f:view>

