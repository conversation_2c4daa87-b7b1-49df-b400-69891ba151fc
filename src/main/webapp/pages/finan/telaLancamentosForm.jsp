<%@page contentType="text/html" pageEncoding="ISO-8859-1" %>
<%@include file="includes/include_imports.jsp" %>
<script type="text/javascript" src="../../script/demonstrativoFinan.js"></script>
<script type="text/javascript" src="../../script/script.js"></script>
<script type="text/javascript" language="javascript" src="../../script/gobackblock.js"></script>
<link href="../../css/financeiro.css" rel="stylesheet" type="text/css">
<link href="../../beta/css/font-awesome_2.0.min.css" type="text/css" rel="stylesheet"/>
<link href="../../beta/css/pure-ext.css" type="text/css" rel="stylesheet"/>
<script type="text/javascript" language="javascript" src="../../bootstrap/jquery.js"></script>
<script type="text/javascript" src="../../script/tooltipster/jquery.tooltipster.min.js"></script>

<c:set var="moduloSession" value="1" scope="session"/>
<c:set var="titulo" scope="session" value="FINANCEIRO"/>
<f:view>
    <jsp:include page="../../includes/include_carregando_ripple.jsp"/>
    <%@include file="includes/include_modalSelecaoPlanoConta.jsp" %>
    <%@include file="includes/include_modalSelecaoCentroCusto.jsp" %>
    <%@include file="includes/include_agendamentoFinanceiro.jsp" %>
    <%@include file="includes/include_modal_lancarPagamento.jsp" %>
    <%@include file="includes/include_modal_lancarPagamentoOnline.jsp" %>
    <%@include file="includes/include_modal_alteracoesAgendamento.jsp" %>
    <%@include file="includes/include_modal_confirmaExclusaoParcela.jsp" %>
    <%@include file="includes/include_modal_confirmaExclusaoParcelaPararAgendamento.jsp" %>
    <%@include file="includes/include_modal_pergunta_excluirContaFornecedor.jsp" %>

    <a4j:loadScript src="/script/jquery.maskedinput-1.2.2.js"/>
        <h:panelGroup layout="block" style="display: inline-table;width: 100%;" styleClass="fundoCinza">
            <h:form id="formTopo" style="overflow-x: visible !important;">
                <h:panelGroup layout="block" styleClass="bgtop topoZW" rendered="#{MenuControle.apresentarTopo}">
                    <jsp:include page="../../include_topo_novo.jsp" flush="true"/>
                    <jsp:include page="../../include_menu_fin_flat.jsp" flush="true"/>
                </h:panelGroup>
            </h:form>
            <h:panelGroup layout="block" styleClass="caixaCorpo">
                <h:panelGroup layout="block" style="height: 80%;width: 100%">
                    <h:panelGroup layout="block" styleClass="caixaMenuLatel">
                        <h:panelGroup layout="block" styleClass="container-imagem container-conteudo-central">
                            <h:panelGroup layout="block" styleClass="container-box zw_ui especial ">
                                <h:panelGroup styleClass="container-box-header" layout="block">
                                    <h:panelGroup layout="block" styleClass="margin-box">
                                        <h:outputText styleClass="container-header-titulo" value="#{MovContaControle.tituloTelaLancamento} "/>
                                        <h:outputLink styleClass="linkWiki"
                                                      value="#{SuperControle.urlBaseConhecimento}relatorio-ver-lancamentos/"
                                                      title="Clique e saiba mais: Lançamentos Financeiros"
                                                      target="_blank">
                                            <i class="fa-icon-question-sign" style="font-size: 18px"></i>
                                        </h:outputLink>
                                    </h:panelGroup>
                                </h:panelGroup>

                                <h:panelGroup layout="block" styleClass="margin-box">
                                    <a4j:form id="formLanc">
                                        <!-- Inclui o elemento HEAD da página -->
                                        <head>
                                            <%@include file="includes/include_head_finan.jsp" %>
                                            <script type="text/javascript" language="javascript"
                                                    src="${contextoFinan}/script/telaInicial.js"></script>
                                            <script type="text/javascript" language="javascript"
                                                    src="${contextoFinan}/script/vanilla-masker.min.js"></script>
                                        </head>
                                        <body>
                                        <c:if test="${MovContaControle.somenteVisualizacao}">
                                            <%@include file="includes/include_visualizacaoLancamento.jsp" %>
                                        </c:if>
                                        <c:if test="${!MovContaControle.somenteVisualizacao}">
                                            <%@include file="includes/include_TelaLancamentosForm.jsp" %>
                                        </c:if>
                                        </body>
                                    </a4j:form>
                                </h:panelGroup>
                            </h:panelGroup>
                        </h:panelGroup>
                        <h:form id="form">
                            <jsp:include page="includes/include_box_menulateral.jsp" flush="true"/>
                        </h:form>
                    </h:panelGroup>
                </h:panelGroup>
            </h:panelGroup>

            <jsp:include page="../../include_rodape_flat.jsp" flush="true" />
            <rich:jQuery id="mskData" selector=".rich-calendar-input" timing="immediate" query="mask('99/99/9999')" />
        </h:panelGroup>

    <%@include file="/includes/autorizacao/include_autorizacao_funcionalidade.jsp" %>
    <%@include file="includes/include_modal_pagamentoConjunto.jsp" %>
    <%@include file="includes/include_modal_consultarCaixa.jsp" %>
    <%@include file="includes/include_box_fecharCaixas.jsp" %>
    <%@include file="includes/include_modal_abrirCaixa.jsp" %>
    <%@include file="includes/include_modal_ContaBancariaFornecedor.jsp"%>
    <%@include file="includes/include_modal_excessoValorRateio.jsp" %>
    <%@include file="includes/include_modal_confirmaExclusaoRateio.jsp" %>
    <%@include file="includes/include_modal_estornoPagamento.jsp" %>
    <%@include file="pessoaSimplificado.jsp" %>
    <%@include file="cidade.jsp" %>
    <jsp:include page="../../includes/include_panelMensagem_goBackBlock.jsp"/> 
</f:view>

