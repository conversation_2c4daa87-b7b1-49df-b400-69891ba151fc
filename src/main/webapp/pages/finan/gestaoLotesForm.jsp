<%@page pageEncoding="ISO-8859-1"%>
<%--
    Document   : gestaoLotesForm
    Created on : 29/09/2011, 09:22:25
    Author     : <PERSON><PERSON><PERSON>
--%>

<%@include file="includes/include_imports.jsp" %>
<script type="text/javascript" src="../../script/demonstrativoFinan.js"></script>
<script type="text/javascript" language="javascript" src="../../script/gobackblock.js"></script>

<script type="text/javascript">
    function atualizar() {
        document.getElementById('form:fechar').click();
    }
</script>

<f:view>
    <jsp:include page="/includes/include_carregando_ripple.jsp" flush="true"/>
    <title>
        <h:outputText value="#{msg_aplic.prt_Finan_GestaoLotes_tituloForm}"/>
    </title>

    <%@include file="includes/include_operacaoConta.jsp" %>
    <a4j:loadScript src="/script/jquery.maskedinput-1.2.2.js" />
    <h:form id="form">
        <html>
            <head>
                <%@include file="includes/include_head_finan.jsp" %>
                <link href="../../css/telaCliente.css" rel="stylesheet" type="text/css">
                <link href="../../css/financeiro.css" rel="stylesheet" type="text/css">
                <link href="../../css/jquery.treeTable.css" rel="stylesheet" type="text/css">
                <link href="../../beta/css/pure-min.css" type="text/css" rel="stylesheet"/>
                <link href="../../beta/css/bootstrap-tabs.css" type="text/css" rel="stylesheet"/>
                <link href="../../beta/css/pure-ext.css" type="text/css" rel="stylesheet"/>
                <link href="../../beta/css/font-awesome_2.0.min.css" type="text/css" rel="stylesheet"/>
                <script type="text/javascript" language="javascript" src="${contextoFinan}/script/telaInicial.js"></script>
            </head>
            <body>
                <h:panelGroup layout="block" style="display: inline-table;width: 100%;" styleClass="fundoCinza">
                    <h:panelGroup layout="block" styleClass="bgtop topoZW" rendered="#{MenuControle.apresentarTopo}">
                        <jsp:include page="../../include_topo_novo.jsp" flush="true"/>
                        <jsp:include page="../../include_menu_fin_flat.jsp" flush="true"/>
                    </h:panelGroup>
                    <h:panelGroup layout="block" styleClass="caixaCorpo">
                        <h:panelGroup layout="block" style="height: 80%;width: 100%">
                            <h:panelGroup layout="block" styleClass="caixaMenuLatel">
                                <h:panelGroup layout="block" styleClass="container-imagem container-conteudo-central">
                                    <h:panelGroup layout="block" styleClass="container-box zw_ui especial ">
                                        <h:panelGroup styleClass="container-box-header" layout="block">
                                            <h:panelGroup layout="block" styleClass="margin-box">
                                                <h:outputText value="#{msg_aplic.prt_Finan_GestaoLotes_tituloForm} " styleClass="container-header-titulo"/>
                                                <h:outputLink styleClass="linkWiki"
                                                              value="#{SuperControle.urlBaseConhecimento}gestao-de-lotes-do-modulo-financeiro/"
                                                              title="Clique e saiba mais: Gestão de Lotes"
                                                              target="_blank">
                                                    <i class="fa-icon-question-sign" style="font-size: 18px"></i>
                                                </h:outputLink>
                                            </h:panelGroup>
                                        </h:panelGroup>
                                        <h:panelGroup layout="block" styleClass="margin-box">
                                            <h:panelGrid columns="2" rowClasses="linhaPar,linhaImpar"
                                                         columnClasses="classEsquerda, classDireita" width="100%"
                                                         id="dadosLotePainel">
                                                <h:outputText styleClass="tituloCampos upper" value="#{msg_aplic.prt_Finan_Lancamentos_empresa}"
                                                              rendered="#{GestaoLotesControle.lote.novoObj}"/>
                                                <h:panelGroup layout="block" styleClass="block cb-container" rendered="#{GestaoLotesControle.lote.novoObj}">
                                                    <h:selectOneMenu id="empresa" 
                                                                 value="#{GestaoLotesControle.lote.empresa.codigo}" >
                                                        <f:selectItems value="#{GestaoLotesControle.listaSelectItemEmpresa}" />
                                                    </h:selectOneMenu>
                                                </h:panelGroup>
                                                
                                                <h:outputText styleClass="tituloCampos upper " value="#{msg_aplic.prt_Finan_GestaoLotes_codigo}" />
                                                <h:inputText id="codigo" size="10" maxlength="10" onblur="blurinput(this);" readonly="true"
                                                             styleClass="inputTextClean"
                                                             onfocus="focusinput(this);" value="#{GestaoLotesControle.lote.codigo}" />
                                                <h:outputText styleClass="tituloCampos upper" value="#{msg_aplic.prt_Finan_GestaoLotes_descricao}" />
                                                <h:panelGroup>
                                                    <h:inputText id="descricao" size="40" maxlength="50" onblur="blurinput(this);"
                                                                 onfocus="focusinput(this);" styleClass="inputTextClean" value="#{GestaoLotesControle.lote.descricao}" />
                                                </h:panelGroup>

                                                <h:outputText styleClass="tituloCampos upper" rendered="#{GestaoLotesControle.lote.codigo > 0}"
                                                              value="Data Lançamento:" />
                                                <h:panelGrid rendered="#{GestaoLotesControle.lote.codigo > 0 && !GestaoLotesControle.permitirAlteracaoDataLancamento}" columns="2">
                                                    <h:outputText styleClass="tituloCampos" value="#{GestaoLotesControle.lote.dataLancamento_Apresentar}" />
                                                    <a4j:commandLink value="Alterar" style="padding: 10px;"
                                                                     action="#{GestaoLotesControle.confirmaAlterarDataLancamento}"
                                                                     oncomplete="#{GestaoLotesControle.msgAlert}"
                                                                     reRender="panelAutorizacaoFuncionalidade"/>
                                                </h:panelGrid>
                                                <h:panelGroup layout="block" styleClass="dateTimeCustom" style="height: auto;"
                                                              rendered="#{GestaoLotesControle.lote.codigo > 0 && GestaoLotesControle.permitirAlteracaoDataLancamento}">
                                                    <rich:calendar id="dataLancamento"
                                                               value="#{GestaoLotesControle.lote.dataLancamento}"
                                                               
                                                               inputSize="10"
                                                               inputClass="inputTextClean"
                                                               oninputblur="blurinput(this);"
                                                               oninputfocus="focusinput(this);"
                                                               datePattern="dd/MM/yyyy"
                                                               buttonIcon="/imagens_flat/calendar-button.svg"
                                                               oninputchange="return validar_Data(this.id);"
                                                               enableManualInput="true"
                                                               zindex="2"
                                                               showWeeksBar="false"/>
                                                </h:panelGroup>
                                                


                                                <h:outputText styleClass="tituloCampos upper" value="#{msg_aplic.prt_Finan_GestaoRecebiveis_dataDeposito}"
                                                              />
                                                <h:panelGroup layout="block" styleClass="dateTimeCustom" style="height: auto;">
                                                    <rich:calendar id="dataInicioD"
                                                                   value="#{GestaoLotesControle.lote.dataDeposito}"
                                                                   inputSize="10"
                                                                   inputClass="inputTextClean"
                                                                   oninputblur="blurinput(this);"
                                                                   oninputfocus="focusinput(this);"
                                                                   datePattern="dd/MM/yyyy"
                                                                   oninputchange="return validar_Data(this.id);"
                                                                   enableManualInput="true"
                                                                   zindex="2"
                                                                   buttonIcon="/imagens_flat/calendar-button.svg"
                                                                   showWeeksBar="false"/>
                                                </h:panelGroup>
                                                <h:outputText styleClass="tituloCampos upper"
                                                              rendered="#{not empty GestaoLotesControle.lote.conta && ConfiguracaoFinanceiroControle.confFinanceiro.usarMovimentacaoContas}"
                                                              value="Conta:" />
                                                <h:outputText styleClass="tituloCampos upper"
                                                              rendered="#{not empty GestaoLotesControle.lote.conta && ConfiguracaoFinanceiroControle.confFinanceiro.usarMovimentacaoContas}"
                                                              value="#{GestaoLotesControle.lote.conta}" />

                                                <h:outputText styleClass="tituloCampos upper" rendered="#{GestaoLotesControle.lote.codigo > 0}"
                                                              value="Responsável Lançamento:" />
                                                <h:panelGroup rendered="#{GestaoLotesControle.lote.codigo > 0}">
                                                    <h:outputText styleClass="tituloCampos upper"
                                                                  value="#{GestaoLotesControle.lote.usuarioResponsavel.nome}" />

                                                    <h:outputText styleClass="tituloCampos upper"
                                                                  rendered="#{GestaoLotesControle.lancamento.caixa > 0 && ConfiguracaoFinanceiro.confFinanceiro.usarMovimentacaoContas}"
                                                                  value=" - Caixa: " />

                                                    <h:commandLink action="#{GestaoLotesControle.visualizarCaixa}"
                                                                   rendered="#{GestaoLotesControle.lancamento.caixa > 0 && ConfiguracaoFinanceiro.confFinanceiro.usarMovimentacaoContas}">
                                                        <h:outputText styleClass="tituloCampos" style="text-decoration: underline;"
                                                                      value="#{GestaoLotesControle.lancamento.caixa}" />
                                                    </h:commandLink>



                                                    <h:outputText styleClass="tituloCampos upper" rendered="#{GestaoLotesControle.lancamento.caixa > 0 && ConfiguracaoFinanceiro.confFinanceiro.usarMovimentacaoContas}"
                                                                  value=" - Lanç.: " />
                                                    <h:commandLink action="#{GestaoLotesControle.editarLancamentoLote}"
                                                                   rendered="#{GestaoLotesControle.lancamento.caixa > 0 && ConfiguracaoFinanceiro.confFinanceiro.usarMovimentacaoContas}">
                                                        <h:outputText styleClass="tituloCampos" style="text-decoration: underline;"
                                                                      value="#{GestaoLotesControle.lancamento.codigo}" />
                                                    </h:commandLink>
                                                </h:panelGroup>

                                                <h:outputText styleClass="tituloCampos upper" rendered="#{GestaoLotesControle.lotePagaMovConta}"
                                                              value="#{GestaoLotesControle.labelContasQueLotePaga}" />
                                                <h:commandLink action="#{GestaoLotesControle.editarLancamento}" rendered="#{(GestaoLotesControle.lotePagaMovConta) && (GestaoLotesControle.listaMovContaQueLotePaga == null)}">
                                                    <h:outputText styleClass="tituloCampos" style="text-decoration: underline;"
                                                                  value="#{GestaoLotesControle.contasQueLotePaga}" />
                                                </h:commandLink>
                                                <a4j:commandLink oncomplete="Richfaces.showModalPanel('modalContasLotePagou');"  rendered="#{(GestaoLotesControle.lotePagaMovConta) && (GestaoLotesControle.listaMovContaQueLotePaga != null)}">
                                                    <h:outputText styleClass="tituloCampos" style="text-decoration: underline;"
                                                                  value="#{GestaoLotesControle.contasQueLotePaga}" />
                                                </a4j:commandLink>


                                            </h:panelGrid>

                                            <%-- FIM DADOS LOTE --%>

                                            <div class="sep" style="margin:10px 0 10px 0;"></div>

                                            <rich:togglePanel id="listas" switchType="ajax" stateOrder="closed, cheque, cartao"
                                                              value="#{GestaoLotesControle.escolha}">

                                                <f:facet name="closed">
                                                    <h:panelGrid columns="1" width="100%" columnClasses="centralizado">
                                                        <h:panelGroup>
                                                            <a4j:commandButton id="voltar1" image="/imagens/Voltar_finan.png"
                                                                               action="#{GestaoLotesControle.voltarGestao}" title="Voltar para consulta de lotes"/>
                                                            <rich:spacer width="10"/>
                                                            <a4j:commandButton reRender="listas, panelMensagem, formConsultaCheque" styleClass="botoes"
                                                                               action="#{GestaoLotesControle.incluirLote}"
                                                                               oncomplete="#{GestaoLotesControle.abrirModalCheque}"
                                                                               image="/imagens/Adicionar_Cheque.png" title="Adicionar Cheques"/>
                                                            <rich:spacer width="10"/>
                                                            <a4j:commandButton reRender="listas, panelMensagem, formConsultaCartao" styleClass="botoes"
                                                                               action="#{GestaoLotesControle.incluirLote}"
                                                                               oncomplete="#{GestaoLotesControle.abrirModalCartao}"
                                                                               image="/imagens/Adicionar_Cartao.png" title="Adicionar Cartões de Crédito"/>


                                                        </h:panelGroup>
                                                    </h:panelGrid>
                                                </f:facet>

                                                <%-- INICIO LISTA DE CHEQUE --%>

                                                <f:facet name="cheque">
                                                    <h:panelGroup>
                                                        <table width="100%" style="margin-bottom: 20px;">
                                                            <tr><td width="20%"></td><td align="center" width="60%" >
                                                                    <h:panelGrid columns="6"  columnClasses="centralizado"
                                                                                 rendered="#{GestaoLotesControle.exibirBotoesAcimaCheque}">
                                                                        <a4j:commandLink styleClass="pure-button texto-size-12 inlineBlock mLeft10 "
                                                                                           action="#{GestaoLotesControle.voltarGestao}" 
                                                                                           title="Voltar para consulta de lotes">
                                                                            <i class="fa-icon-chevron-left"></i> &nbsp; Voltar
                                                                            </a4j:commandLink>  
                                                                        <a4j:commandLink styleClass="pure-button texto-size-12 inlineBlock mLeft10 pure-button-primary"
                                                                                           action="#{GestaoLotesControle.alterar}"
                                                                                           value="Gravar"
                                                                                           reRender="form">
                                                                        </a4j:commandLink>
                                                                        <a4j:commandLink reRender="listas, panelMensagem, formConsultaCheque" 
                                                                                         styleClass="pure-button texto-size-12 inlineBlock mLeft10 "
                                                                                           action="#{GestaoLotesControle.incluirLote}"
                                                                                           oncomplete="#{GestaoLotesControle.abrirModalCheque}"
                                                                                           rendered="#{GestaoLotesControle.lote.avulso && !GestaoLotesControle.lotePagaMovConta}"
                                                                                           value="Adicionar Cheques">
                                                                        </a4j:commandLink>
                                                                        <a4j:commandLink  reRender="listas, formConsultaCheque"
                                                                                            rendered="#{!GestaoLotesControle.depositado}"
                                                                                            styleClass="pure-button texto-size-12 inlineBlock mLeft10 "
                                                                                            action="#{GestaoLotesControle.limparDadosConsulta}"
                                                                                            oncomplete="Richfaces.showModalPanel('panelConsultaCheque');"
                                                                                            value="Adicionar Cheques">
                                                                        </a4j:commandLink>
                                                                        <a4j:commandLink	value="Depositar Lote"
                                                                                           rendered="#{!GestaoLotesControle.depositado}"
                                                                                           styleClass="pure-button texto-size-12 inlineBlock mLeft10 "
                                                                                           action="#{GestaoLotesControle.depositarLoteCheques}"
                                                                                           reRender="panelDeposito"
                                                                                           oncomplete="#{GestaoLotesControle.msgAlert}">
                                                                        </a4j:commandLink>
                                                                        <a4j:commandLink oncomplete="abrirPopup('impressaoLote.jsp', 'ImpressaoLote', 780, 595);"
                                                                                         styleClass="pure-button texto-size-12 inlineBlock mLeft10 "
                                                                                         value="Visualizar Impressão">
                                                                        </a4j:commandLink>
                                                                        <a4j:commandLink immediate="true"
                                                                                           action="#{GestaoLotesControle.realizarConsultaLogObjetoLoteSelecionado}"
                                                                                           styleClass="pure-button texto-size-12 inlineBlock mLeft10 "
                                                                                           title="Visualizar log"
                                                                                           accesskey="5"
                                                                                           onclick="abrirPopup('../../visualizadorLogForm.jsp', 'VisualizadorLog', 785, 595);">
                                                                            <i class="fa-icon-search"></i>
                                                                        </a4j:commandLink>

                                                                    </h:panelGrid>
                                                                </td>
                                                                <td align="right" width="20%">
                                                                    <a4j:commandLink id="retirar" value="Retirar do lote" action="#{GestaoLotesControle.retirarGrupo}"
                                                                                       reRender="listas, panelMensagem, modalRetirarChequeLote, comboAcoes"
                                                                                       styleClass="pure-button texto-size-12 inlineBlock mLeft10 "
                                                                                       oncomplete="#{GestaoLotesControle.msgAlert}"
                                                                                       rendered="#{!GestaoLotesControle.lotePagaMovConta
                                                                                                   && GestaoLotesControle.exibirBotoesAcimaCheque
                                                                                                   && ConfiguracaoFinanceiroControle.confFinanceiro.usarMovimentacaoContas}">
                                                                    </a4j:commandLink>
                                                                </td></tr></table>

                                                        <h:dataTable id="listaChequesLote" width="100%" styleClass="tabelaDados"
                                                                        value="#{GestaoLotesControle.listaChequesLote}" var="cheque">
                                                            <h:column >
                                                                <f:facet name="header">
                                                                    <h:outputText value="#{msg_aplic.prt_Finan_GestaoRecebiveis_nome}" />
                                                                </f:facet>
                                                                <h:outputText value="#{cheque.nomePagador}" />
                                                            </h:column>
                                                            <h:column>
                                                                <f:facet name="header">
                                                                    <h:outputText value="#{msg_aplic.prt_Cheque_banco}" />
                                                                </f:facet>
                                                                <h:outputText value="#{cheque.numeroBanco}"
                                                                              style="#{cheque.removido ? 'opacity: 0.4' : ''}" />
                                                            </h:column>
                                                            <h:column>
                                                                <f:facet name="header">
                                                                    <h:outputText value="#{msg_aplic.prt_Cheque_agencia}" />
                                                                </f:facet>
                                                                <h:outputText value="#{cheque.agencia}" style="#{cheque.removido ? 'opacity: 0.4' : ''}" />
                                                            </h:column>
                                                            <h:column>
                                                                <f:facet name="header">
                                                                    <h:outputText value="#{msg_aplic.prt_Cheque_conta}" />
                                                                </f:facet>
                                                                <h:outputText value="#{cheque.conta}" style="#{cheque.removido ? 'opacity: 0.4' : ''}" />
                                                            </h:column>
                                                            <h:column>
                                                                <f:facet name="header">
                                                                    <h:outputText value="#{msg_aplic.prt_Finan_GestaoRecebiveis_numeroCheque}" />
                                                                </f:facet>
                                                                <h:outputText value="#{cheque.numero}" style="#{cheque.removido ? 'opacity: 0.4' : ''}" />
                                                            </h:column>
                                                            <h:column>
                                                                <f:facet name="header">
                                                                    <h:outputText value="#{msg_aplic.prt_Finan_GestaoRecebiveis_dataLancamento}" />
                                                                </f:facet>
                                                                <h:outputText value="#{cheque.dataLancamento}">
                                                                    <f:convertDateTime pattern="dd/MM/yyyy" />
                                                                </h:outputText>
                                                            </h:column>
                                                            <h:column>
                                                                <f:facet name="header">
                                                                    <h:outputText value="#{msg_aplic.prt_Finan_GestaoRecebiveis_dataCompensacao}" />
                                                                </f:facet>
                                                                <h:outputText value="#{cheque.dataCompensacao}">
                                                                    <f:convertDateTime pattern="dd/MM/yyyy" />
                                                                </h:outputText>
                                                            </h:column>
                                                            <h:column >
                                                                <f:facet name="header">
                                                                    <h:outputText value="#{msg_aplic.prt_Cheque_valor}" />
                                                                </f:facet>

                                                                <h:outputText style="#{cheque.removido ? 'opacity: 0.4' : ''}" value="#{cheque.valor}">
                                                                    <f:converter converterId="FormatadorNumerico" />
                                                                </h:outputText>
                                                            </h:column>
                                                            <h:column>
                                                                <f:facet name="header">
                                                                    <h:outputText value="Opções" />
                                                                </f:facet>


                                                                <a4j:commandLink action="#{GestaoLotesControle.excluirCheque}"
                                                                                   
                                                                                   oncomplete="#{GestaoLotesControle.msgAlert}"
                                                                                   title="Retirar cheque do lote"
                                                                                   rendered="#{!GestaoLotesControle.lotePagaMovConta && !cheque.removido
                                                                                               && ConfiguracaoFinanceiroControle.confFinanceiro.usarMovimentacaoContas}"
                                                                                   onclick="#{GestaoLotesControle.onclickExcluir}"
                                                                                   reRender="listas, panelMensagem, modalRetirarChequeLote"
                                                                                   styleClass="texto-cor-azul linkPadrao inline mLeft10">
                                                                    <i class="fa-icon-remove"></i>
                                                                 </a4j:commandLink>
                                                                    
                                                                    <a4j:commandLink action="#{GestaoLotesControle.excluirCheque}"
                                                                                   title="Retirar cheque do lote"
                                                                                   rendered="#{!GestaoLotesControle.lotePagaMovConta && !cheque.removido
                                                                                               && !ConfiguracaoFinanceiroControle.confFinanceiro.usarMovimentacaoContas}"
                                                                                   onclick="if(!confirm('Confirma retirada do cheque do lote?')){return false;}"
                                                                                   reRender="listas, panelMensagem, modalRetirarChequeLote"
                                                                                   styleClass="texto-cor-azul linkPadrao inline mLeft10">
                                                                    <i class="fa-icon-remove"></i>
                                                                    </a4j:commandLink>
                                                                
                                                                <a4j:commandLink title="Historico do Cheque" reRender="modalHistoricoCheque"
                                                                                   actionListener="#{ChequeControle.escolherCheque}"
                                                                                   oncomplete="Richfaces.showModalPanel('modalHistoricoCheque')"
                                                                                   styleClass="texto-cor-azul linkPadrao inline mLeft10">
                                                                    <f:attribute name="chequeTO" value="#{cheque}" />
                                                                    <i class="fa-icon-hourglass"></i>
                                                                </a4j:commandLink>
                                                                    <div class="chk-fa-container inline mLeft10">
                                                                    <h:selectBooleanCheckbox  value="#{cheque.chequeEscolhido}"
                                                                                          rendered="#{!GestaoLotesControle.lote.avulso
                                                                                                      && !cheque.removido
                                                                                                      && ConfiguracaoFinanceiroControle.confFinanceiro.usarMovimentacaoContas}">
                                                                </h:selectBooleanCheckbox>
                                                                    <span/>
                                                                </div>

                                                                
                                                                
                                                                
                                                            </h:column>
                                                        </h:dataTable>



                                                        <table width="100%" >
                                                            <tr>
                                                                <td align="center" width="20%"></td>
                                                                <td align="center" width="60%" >

                                                                     <h:panelGrid columns="6"  columnClasses="centralizado">
                                                                        <a4j:commandLink styleClass="pure-button texto-size-12 inlineBlock mLeft10 "
                                                                                           action="#{GestaoLotesControle.voltarGestao}" 
                                                                                           title="Voltar para consulta de lotes">
                                                                            <i class="fa-icon-chevron-left"></i> &nbsp; Voltar
                                                                            </a4j:commandLink>  
                                                                        <a4j:commandLink styleClass="pure-button texto-size-12 inlineBlock mLeft10 pure-button-primary"
                                                                                           action="#{GestaoLotesControle.alterar}"
                                                                                           value="Gravar"
                                                                                           reRender="form">
                                                                        </a4j:commandLink>
                                                                        <a4j:commandLink reRender="listas, panelMensagem, formConsultaCheque" 
                                                                                         styleClass="pure-button texto-size-12 inlineBlock mLeft10 "
                                                                                           action="#{GestaoLotesControle.incluirLote}"
                                                                                           oncomplete="#{GestaoLotesControle.abrirModalCheque}"
                                                                                           rendered="#{GestaoLotesControle.lote.avulso && !GestaoLotesControle.lotePagaMovConta}"
                                                                                           value="Adicionar Cheques">
                                                                        </a4j:commandLink>
                                                                        <a4j:commandLink  reRender="listas, formConsultaCheque"
                                                                                            rendered="#{!GestaoLotesControle.depositado}"
                                                                                            styleClass="pure-button texto-size-12 inlineBlock mLeft10 "
                                                                                            action="#{GestaoLotesControle.limparDadosConsulta}"
                                                                                            oncomplete="Richfaces.showModalPanel('panelConsultaCheque');"
                                                                                            value="Adicionar Cheques">
                                                                        </a4j:commandLink>
                                                                        <a4j:commandLink	value="Depositar Lote"
                                                                                           rendered="#{!GestaoLotesControle.depositado}"
                                                                                           styleClass="pure-button texto-size-12 inlineBlock mLeft10 "
                                                                                           action="#{GestaoLotesControle.depositarLoteCheques}"
                                                                                           reRender="panelDeposito"
                                                                                           oncomplete="#{GestaoLotesControle.msgAlert}">
                                                                        </a4j:commandLink>
                                                                        <a4j:commandLink oncomplete="abrirPopup('impressaoLote.jsp', 'ImpressaoLote', 780, 595);"
                                                                                         styleClass="pure-button texto-size-12 inlineBlock mLeft10 "
                                                                                         value="Visualizar Impressão">
                                                                        </a4j:commandLink>
                                                                        <a4j:commandLink immediate="true"
                                                                                           action="#{GestaoLotesControle.realizarConsultaLogObjetoLoteSelecionado}"
                                                                                           styleClass="pure-button texto-size-12 inlineBlock mLeft10 "
                                                                                           title="Visualizar log"
                                                                                           accesskey="5"
                                                                                           onclick="abrirPopup('../../visualizadorLogForm.jsp', 'VisualizadorLog', 785, 595);">
                                                                            <i class="fa-icon-search"></i>
                                                                        </a4j:commandLink>

                                                                    </h:panelGrid>
                                                                </td>
                                                                <td align="right" width="20%">
                                                                    <a4j:commandLink id="retirar2" value="Retirar do lote" action="#{GestaoLotesControle.retirarGrupo}"
                                                                                       reRender="listas, panelMensagem, modalRetirarChequeLote, comboAcoes"
                                                                                       styleClass="pure-button texto-size-12 inlineBlock mLeft10 "
                                                                                       oncomplete="#{GestaoLotesControle.msgAlert}"
                                                                                       rendered="#{!GestaoLotesControle.lotePagaMovConta
                                                                                                   && ConfiguracaoFinanceiroControle.confFinanceiro.usarMovimentacaoContas}">
                                                                    </a4j:commandLink>
                                                                    
                                                                </td>
                                                            </tr>
                                                        </table>
                                                        <h:panelGrid id="totalDepositadoCh" columns="2" width="100%" columnClasses="colunaEsquerda, colunaDireita" style="margin-top:20px;">
                                                            <h:panelGroup>
                                                                <h:outputText styleClass="label texto-size-14" value="Total de registros: "/>
                                                                <h:outputText  styleClass="label texto-size-14" value="#{fn:length(GestaoLotesControle.listaChequesLote)}" />
                                                            </h:panelGroup>
                                                            <h:panelGroup>
                                                                <h:outputText styleClass="label texto-size-14" value="#{msg_aplic.prt_Finan_GestaoLotes_total} = "/>
                                                                <h:outputText  styleClass="label texto-size-14" value="R$ " />
                                                                <h:outputText styleClass="label texto-size-14" value="#{GestaoLotesControle.totalChequesLote}">
                                                                    <f:converter converterId="FormatadorNumerico"/>
                                                                </h:outputText>
                                                            </h:panelGroup>
                                                        </h:panelGrid>
                                                    </h:panelGroup>
                                                </f:facet>

                                                <%-- FIM LISTA DE CHEQUE --%>
                                                <%-- INICIO LISTA DE CARTAO --%>

                                                <f:facet name="cartao">
                                                    <h:panelGroup>

                                                        <table width="100%">
                                                            <tr>
                                                                <td width="20%"></td>
                                                                <td align="center" width="60%" >
                                                                    <h:panelGrid columns="6" rendered="#{GestaoLotesControle.exibirBotoesAcimaCartao}">

                                                                        <a4j:commandLink styleClass="pure-button texto-size-12 inlineBlock mLeft10"
                                                                                         action="#{GestaoLotesControle.voltarGestao}">
                                                                            <i class="fa-icon-chevron-left"></i> &nbsp; Voltar
                                                                        </a4j:commandLink>

                                                                        <a4j:commandLink styleClass="pure-button texto-size-12 inlineBlock mLeft10 pure-button-primary"
                                                                                         action="#{GestaoLotesControle.alterar}"
                                                                                         reRender="form">
                                                                            <i class="fa-icon-save"></i> &nbsp; Gravar
                                                                        </a4j:commandLink>
                                                                        <a4j:commandLink reRender="listas, formConsultaCartao" 
                                                                                         styleClass="pure-button texto-size-12 inlineBlock mLeft10 "
                                                                                         action="#{GestaoLotesControle.limparDadosConsulta}"
                                                                                         oncomplete="Richfaces.showModalPanel('panelConsultaCartao');" 
                                                                                         rendered="#{!GestaoLotesControle.depositado}"
                                                                                         value="Adicionar Cartões de Crédito">
                                                                        </a4j:commandLink>
                                                                        <a4j:commandLink    value="Depositar Lote"
                                                                                            styleClass="pure-button texto-size-12 inlineBlock mLeft10 "
                                                                                            rendered="#{!GestaoLotesControle.depositado}"
                                                                                            action="#{GestaoLotesControle.depositarLoteCartoes}"
                                                                                            reRender="panelDeposito"
                                                                                            oncomplete="#{GestaoLotesControle.msgAlert}">
                                                                        </a4j:commandLink>

                                                                        <a4j:commandLink oncomplete="abrirPopup('impressaoLote.jsp', 'ImpressaoLote', 780, 595);"
                                                                                         value="Visualizar Impressão"
                                                                                         styleClass="pure-button texto-size-12 inlineBlock mLeft10 ">
                                                                        </a4j:commandLink>
                                                                        <a4j:commandLink  immediate="true"
                                                                                          action="#{GestaoLotesControle.realizarConsultaLogObjetoLoteSelecionado}"
                                                                                          title="Visualizar log"
                                                                                          accesskey="5"
                                                                                          styleClass="pure-button texto-size-12 inlineBlock mLeft10 "
                                                                                          onclick="abrirPopup('../../visualizadorLogForm.jsp', 'VisualizadorLog', 785, 595);">
                                                                            <i class="fa-icon-search"></i>
                                                                        </a4j:commandLink>


                                                                    </h:panelGrid>
                                                                </td>
                                                                <td align="right" width="20%">
                                                                    <a4j:commandLink id="retirar4" value="Retirar do lote" action="#{GestaoLotesControle.retirarGrupo}"
                                                                                     styleClass="pure-button texto-size-12 inlineBlock mLeft10 "
                                                                                     reRender="listas, panelMensagem, modalRetirarChequeLote, comboAcoes"
                                                                                     oncomplete="#{GestaoLotesControle.msgAlert}"
                                                                                     rendered="#{!GestaoLotesControle.lotePagaMovConta && GestaoLotesControle.exibirBotoesAcimaCartao
                                                                                                 && ConfiguracaoFinanceiroControle.confFinanceiro.usarMovimentacaoContas}">
                                                                    </a4j:commandLink>
                                                                </td>
                                                            </tr>
                                                        </table>

                                                        <h:dataTable id="listaCartoesLote" width="100%" styleClass="tabelaDados "
                                                                     value="#{GestaoLotesControle.listaCartoesLote}" var="cartao">
                                                            <h:column>
                                                                <f:facet name="header">
                                                                    <h:outputText value="#{msg_aplic.prt_Finan_GestaoRecebiveis_nome}" />
                                                                </f:facet>
                                                                <h:outputText value="#{cartao.nomePagadorMin}" />
                                                            </h:column>
                                                            <h:column>
                                                                <f:facet name="header">
                                                                    <h:outputText value="#{msg_aplic.prt_OperadoraCartao_tituloForm}" />
                                                                </f:facet>
                                                                <h:outputText value="#{cartao.operadoraMin}" />
                                                            </h:column>
                                                            <h:column>
                                                                <f:facet name="header">
                                                                    <h:outputText value="#{msg_aplic.prt_Finan_GestaoRecebiveis_dataLancamento}" />
                                                                </f:facet>
                                                                <h:outputText value="#{cartao.dataLancamento}">
                                                                    <f:convertDateTime pattern="dd/MM/yyyy" />
                                                                </h:outputText>
                                                            </h:column>
                                                            <h:column>
                                                                <f:facet name="header">
                                                                    <h:outputText value="#{msg_aplic.prt_Finan_GestaoRecebiveis_dataCompensacao}" />
                                                                </f:facet>
                                                                <h:outputText value="#{cartao.dataCompensacao}">
                                                                    <f:convertDateTime pattern="dd/MM/yyyy" />
                                                                </h:outputText>
                                                            </h:column>

                                                            <h:column>
                                                                <f:facet name="header">
                                                                    <h:outputText value="#{msg_aplic.prt_Finan_GestaoRecebiveis_Autorizacao}" />
                                                                </f:facet>
                                                                <h:outputText  value="#{cartao.autorizacao}">
                                                                </h:outputText>
                                                            </h:column>

                                                            <h:column>
                                                                <f:facet name="header">
                                                                    <h:outputText value="#{msg_aplic.prt_Finan_GestaoRecebiveis_NSU}" />
                                                                </f:facet>
                                                                <h:outputText value="#{cartao.nsu}"/>
                                                            </h:column>

                                                            <h:column>
                                                                <f:facet name="header">
                                                                    <h:outputText value="#{msg_aplic.prt_Cheque_valor}" />
                                                                </f:facet>
                                                                <h:outputText style="float:right; margin-right: 5px;"  value="#{cartao.valor}">
                                                                    <f:converter converterId="FormatadorNumerico" />
                                                                </h:outputText>
                                                            </h:column>
                                                            <h:column>
                                                                <f:facet name="header">
                                                                </f:facet>
                                                                <a4j:commandLink action="#{GestaoLotesControle.excluirCartao}" title="Retirar cartao do lote"
                                                                                 styleClass="texto-cor-azul linkPadrao inline mLeft10"
                                                                                 rendered="#{!cartao.removido && ConfiguracaoFinanceiroControle.confFinanceiro.usarMovimentacaoContas}"
                                                                                 onclick="#{GestaoLotesControle.onclickExcluir}"
                                                                                 reRender="listas, panelMensagem, modalRetirarChequeLote"
                                                                                 oncomplete="#{GestaoLotesControle.msgAlert}">
                                                                    <i class="fa-icon-remove"></i>
                                                                </a4j:commandLink>

                                                                <a4j:commandLink action="#{GestaoLotesControle.excluirCartao}" title="Retirar cartao do lote"
                                                                                 styleClass="texto-cor-azul linkPadrao inline mLeft10"
                                                                                 rendered="#{!cartao.removido && !ConfiguracaoFinanceiroControle.confFinanceiro.usarMovimentacaoContas}"
                                                                                 onclick="if(!confirm('Confirma a exclusão do cartão deste lote?')){return false;}"
                                                                                 reRender="listas, panelMensagem, modalRetirarChequeLote">
                                                                    <i class="fa-icon-remove"></i>
                                                                </a4j:commandLink>

                                                                <a4j:commandLink title="Historico do Cartão" reRender="modalHistoricoCartao"
                                                                                 actionListener="#{GestaoRecebiveisControle.selecionarCartaoCreditoHistorico}"
                                                                                 oncomplete="Richfaces.showModalPanel('modalHistoricoCartao')"
                                                                                 styleClass="texto-cor-azul linkPadrao inline mLeft10">
                                                                    <f:attribute name="cart" value="#{cartao}" />
                                                                    <i class="fa-icon-hourglass"></i>
                                                                </a4j:commandLink>
                                                                <div class="chk-fa-container inline mLeft10">
                                                                    <h:selectBooleanCheckbox value="#{cartao.cartaoEscolhido}"
                                                                                             rendered="#{!cartao.removido
                                                                                                         && ConfiguracaoFinanceiroControle.confFinanceiro.usarMovimentacaoContas
                                                                                                         && !GestaoLotesControle.lote.avulso}">
                                                                    </h:selectBooleanCheckbox>
                                                                    <span/>
                                                                </div>


                                                            </h:column>
                                                        </h:dataTable>

                                                        <table width="100%">
                                                            <tr>
                                                                <td width="20%" >
                                                                </td>
                                                                <td align="center" width="60%" >
                                                                    <h:panelGrid columns="6">

                                                                        <a4j:commandLink styleClass="pure-button texto-size-12 inlineBlock mLeft10"
                                                                                         action="#{GestaoLotesControle.voltarGestao}">
                                                                            <i class="fa-icon-chevron-left"></i> &nbsp; Voltar
                                                                        </a4j:commandLink>

                                                                        <a4j:commandLink styleClass="pure-button texto-size-12 inlineBlock mLeft10 pure-button-primary"
                                                                                         action="#{GestaoLotesControle.alterar}"
                                                                                         oncomplete="window.opener.location.reload();"
                                                                                         reRender="form">
                                                                                         <!--oncomplete="window.close()"-->
                                                                            <i class="fa-icon-save"></i> &nbsp; Gravar
                                                                        </a4j:commandLink>
                                                                        <a4j:commandLink reRender="listas, formConsultaCartao" 
                                                                                         styleClass="pure-button texto-size-12 inlineBlock mLeft10 "
                                                                                         action="#{GestaoLotesControle.limparDadosConsulta}"
                                                                                         oncomplete="Richfaces.showModalPanel('panelConsultaCartao');"
                                                                                         rendered="#{!GestaoLotesControle.depositado}"
                                                                                         value="Adicionar Cartões de Crédito">
                                                                        </a4j:commandLink>
                                                                        <a4j:commandLink    value="Depositar Lote"
                                                                                            styleClass="pure-button texto-size-12 inlineBlock mLeft10 "
                                                                                            rendered="#{!GestaoLotesControle.depositado}"
                                                                                            action="#{GestaoLotesControle.depositarLoteCartoes}"
                                                                                            reRender="panelDeposito"
                                                                                            oncomplete="#{GestaoLotesControle.msgAlert}">
                                                                        </a4j:commandLink>

                                                                        <a4j:commandLink oncomplete="abrirPopup('impressaoLote.jsp', 'ImpressaoLote', 780, 595);"
                                                                                         value="Visualizar Impressão"
                                                                                         styleClass="pure-button texto-size-12 inlineBlock mLeft10 ">
                                                                        </a4j:commandLink>
                                                                        <a4j:commandLink  immediate="true"
                                                                                          action="#{GestaoLotesControle.realizarConsultaLogObjetoLoteSelecionado}"
                                                                                          title="Visualizar log"
                                                                                          accesskey="5"
                                                                                          styleClass="pure-button texto-size-12 inlineBlock mLeft10 "
                                                                                          onclick="abrirPopup('../../visualizadorLogForm.jsp', 'VisualizadorLog', 785, 595);">
                                                                            <i class="fa-icon-search"></i>
                                                                        </a4j:commandLink>


                                                                    </h:panelGrid>

                                                                </td>
                                                                <td align="right" width="20%">
                                                                    <a4j:commandLink id="retirar3" value="Retirar do lote" action="#{GestaoLotesControle.retirarGrupo}"
                                                                                     styleClass="pure-button texto-size-12 inlineBlock mLeft10 "
                                                                                     reRender="listas, panelMensagem, modalRetirarChequeLote, comboAcoes"
                                                                                     oncomplete="#{GestaoLotesControle.msgAlert}"
                                                                                     rendered="#{!GestaoLotesControle.lotePagaMovConta 
                                                                                                 && ConfiguracaoFinanceiroControle.confFinanceiro.usarMovimentacaoContas}">
                                                                    </a4j:commandLink>
                                                                </td></tr></table>

                                                        <h:panelGrid id="totalDepositadoCa" style="margin-top:20px;" width="100%" columns="2" columnClasses="colunaEsquerda, colunaDireita">
                                                            <h:panelGroup>
                                                                <h:outputText styleClass="label texto-size-14" value="Total de registros: "/>
                                                                <h:outputText  styleClass="label texto-size-14" value="#{fn:length(GestaoLotesControle.listaCartoesLote)}" />
                                                            </h:panelGroup>

                                                            <h:panelGroup>
                                                                <h:outputText styleClass="label texto-size-14 " value="#{msg_aplic.prt_Finan_GestaoLotes_total} = "/>
                                                                <h:outputText value="R$ " styleClass="label texto-size-14 "/>
                                                                <h:outputText styleClass="label texto-size-14 "  value="#{GestaoLotesControle.totalCartoesLote}">
                                                                    <f:converter converterId="FormatadorNumerico"/>
                                                                </h:outputText>
                                                            </h:panelGroup>
                                                        </h:panelGrid>
                                                    </h:panelGroup>
                                                </f:facet>

                                                <%-- FIM LISTA DE CARTAO --%>

                                            </rich:togglePanel>
                                            <h:commandButton id="fechar" style="display: none;"
                                                             action="#{GestaoLotesControle.atualizarTelaLote}">

                                            </h:commandButton>

                                            <div class="sep" style="margin:10px 0 10px 0;"></div>

                                            <h:panelGrid id="panelMensagem" columns="1" width="100%" style="padding:15;">
                                                <h:outputText styleClass="mensagem"  value="#{GestaoLotesControle.mensagem}"/>
                                                <h:outputText styleClass="mensagemDetalhada" value="#{GestaoLotesControle.mensagemDetalhadaNovaLinha}" escape="false"/>
                                            </h:panelGrid>
                                        </h:panelGroup>
                                    </h:panelGroup>

                                </h:panelGroup>
                                <jsp:include page="includes/include_box_menulateral.jsp" flush="true"/>
                            </h:panelGroup>
                        </h:panelGroup>
                    </h:panelGroup>
                    <rich:jQuery id="mskData" selector=".rich-calendar-input" timing="onload" query="mask('99/99/9999')" />
                    <jsp:include page="../../include_rodape_flat.jsp" flush="true" />
                </h:panelGroup>

            </body>
        </html>
    </h:form>

    <rich:modalPanel id="panelConsultaCheque" autosized="true" shadowOpacity="true" width="800" height="600"
                     onshow="document.getElementById('formConsultaCheque:nomeCh').focus();">
        <f:facet name="header">
            <h:outputText value="#{msg_aplic.prt_Finan_GestaoLotes_tituloConsultaCheques}"/>
        </f:facet>
        <f:facet name="controls">
            <h:panelGroup>
                <h:graphicImage value="/imagens/close.png" style="cursor:pointer" id="hidelink" />
                <rich:componentControl for="panelConsultaCheque"
                                       attachTo="hidelink" operation="hide" event="onclick" />
            </h:panelGroup>
        </f:facet>
        <h:form id="formConsultaCheque">
            <h:panelGrid columns="2" rowClasses="linhaImpar, linhaPar"
                         columnClasses="classEsquerda, classDireita" width="100%">
                <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_Finan_GestaoRecebiveis_nome}" />
                <h:inputText id="nomeCh" size="40" maxlength="255" onblur="blurinput(this);"
                             onfocus="focusinput(this);" styleClass="form" value="#{GestaoLotesControle.nome}" />
                <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_Finan_GestaoRecebiveis_periodoCompensacao}" />
                <h:panelGroup>
                    <rich:calendar id="dataInicioCCh"
                                   value="#{GestaoLotesControle.dataInicialCompensacao}"
                                   inputSize="10"
                                   inputClass="form"
                                   oninputblur="blurinput(this);"
                                   oninputfocus="focusinput(this);"
                                   datePattern="dd/MM/yyyy"
                                   oninputchange="return validar_Data(this.id);"
                                   enableManualInput="true"
                                   zindex="2"
                                   showWeeksBar="false"/>
                    <h:outputText styleClass="tituloCampos" style="position:relative; top:0px; left:10px;" value="#{msg_aplic.prt_ate}" />
                    <rich:spacer width="12px"/>
                    <rich:calendar id="dataTerminoCCh"
                                   value="#{GestaoLotesControle.dataFinalCompensacao}"
                                   inputSize="10"
                                   inputClass="form"
                                   oninputblur="blurinput(this);"
                                   oninputfocus="focusinput(this);"
                                   oninputchange="return validar_Data(this.id);"
                                   datePattern="dd/MM/yyyy"
                                   enableManualInput="true"
                                   zindex="2"
                                   showWeeksBar="true" />
                    <rich:spacer width="5" />
                    <a4j:commandButton id="limparPeriodoCompensacao"
                                       action="#{GestaoLotesControle.limparPeriodoC}"
                                       image="/images/limpar.gif" title="Limpar período de compensaçao."
                                       reRender="dataInicioCCh, dataTerminoCCh"/>
                </h:panelGroup>
                <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_Finan_GestaoRecebiveis_periodoLancamento}" />
                <h:panelGroup>
                    <rich:calendar id="dataInicioLCh"
                                   value="#{GestaoLotesControle.dataInicialLancamentoC}"
                                   inputSize="10"
                                   inputClass="form"
                                   oninputblur="blurinput(this);"
                                   oninputfocus="focusinput(this);"
                                   oninputchange="return validar_Data(this.id);"
                                   datePattern="dd/MM/yyyy"
                                   enableManualInput="true"
                                   zindex="2"
                                   showWeeksBar="false"/>
                    <h:outputText styleClass="tituloCampos" style="position:relative; top:0px; left:10px;" value="#{msg_aplic.prt_ate}" />
                    <rich:spacer width="12px"/>
                    <rich:calendar id="dataTerminoLCh"
                                   value="#{GestaoLotesControle.dataFinalLancamentoC}"
                                   inputSize="10"
                                   inputClass="form"
                                   oninputblur="blurinput(this);"
                                   oninputfocus="focusinput(this);"
                                   datePattern="dd/MM/yyyy"
                                   oninputchange="return validar_Data(this.id);"
                                   enableManualInput="true"
                                   zindex="2"
                                   showWeeksBar="true" />
                    <rich:spacer width="5" />
                    <a4j:commandButton id="limparPeriodoLancamento"
                                       action="#{GestaoLotesControle.limparPeriodoLC}"
                                       image="/images/limpar.gif" title="Limpar período de lançamento."
                                       reRender="dataInicioLCh, dataTerminoLCh"/>
                </h:panelGroup>
            </h:panelGrid>
            <h:panelGrid columns="1" width="100%" columnClasses="centralizado" style="margin-top:5px;">
                <a4j:commandButton id="consultar" title="#{msg.msg_consultar_dados}" action="#{GestaoLotesControle.consultarInclusaoCheques}"
                                   image="/imagens/botaoConsultar.png" reRender="listaCheques, scResultado, panelMensagem, formConsultaCheque"/>
            </h:panelGrid>
            <h:panelGrid id="panelMensagem" rendered="#{!empty GestaoLotesControle.mensagemDetalhada}" columns="1" width="100%" styleClass="tabMensagens">
                <h:outputText styleClass="mensagemDetalhada" value="#{GestaoLotesControle.mensagemDetalhada}"/>
            </h:panelGrid>
            <div class="sep" style="margin:5px 0 10px 0;"></div>
            <h:panelGroup>
                <rich:dataTable id="listaCheques" width="100%" rows="10" styleClass="textverysmall" style="border-color:#FFF"
                                columnClasses="colunaEsquerda, centralizado, centralizado, centralizado, centralizado,
                                centralizado, centralizado, colunaDireita, centralizado, centralizado"
                                headerClass="subordinado" rowClasses="linhaImpar, linhaPar"
                                value="#{GestaoLotesControle.listaCheques}" var="cheq">
                    <h:column>
                        <f:facet name="header">
                            <h:outputText style="font-weight: bold; font-size:9px;"
                                          value="#{msg_aplic.prt_Finan_GestaoRecebiveis_nome}" />
                        </f:facet>
                        <h:outputText style="font-weight: bold; font-size:9px;"
                                      styleClass="blue"
                                      value="#{cheq.nomePagador}" />
                    </h:column>
                    <h:column>
                        <f:facet name="header">
                            <h:outputText style="font-weight: bold; font-size:9px;"
                                          value="#{msg_aplic.prt_Cheque_banco}" />
                        </f:facet>
                        <h:outputText style="font-weight: bold; font-size:9px;"
                                      styleClass="blue"
                                      value="#{cheq.numeroBanco}" />
                    </h:column>
                    <h:column>
                        <f:facet name="header">
                            <h:outputText style="font-weight: bold; font-size:9px;"
                                          value="#{msg_aplic.prt_Cheque_agencia}" />
                        </f:facet>
                        <h:outputText style="font-weight: bold; font-size:9px;"
                                      styleClass="blue" value="#{cheq.agencia}" />
                    </h:column>
                    <h:column>
                        <f:facet name="header">
                            <h:outputText style="font-weight: bold; font-size:9px;"
                                          value="#{msg_aplic.prt_Cheque_conta}" />
                        </f:facet>
                        <h:outputText style="font-weight: bold; font-size:9px;"
                                      styleClass="blue" value="#{cheq.conta}" />
                    </h:column>
                    <h:column>
                        <f:facet name="header">
                            <h:outputText style="font-weight: bold; font-size:9px;"
                                          value="#{msg_aplic.prt_Finan_GestaoRecebiveis_numeroCheque}" />
                        </f:facet>
                        <h:outputText style="font-weight: bold; font-size:9px;"
                                      styleClass="blue" value="#{cheq.numero}" />
                    </h:column>
                    <h:column>
                        <f:facet name="header">
                            <h:outputText style="font-weight: bold; font-size:9px;"
                                          value="#{msg_aplic.prt_Finan_GestaoRecebiveis_dataLancamento}" />
                        </f:facet>
                        <h:outputText style="font-weight: bold; font-size:9px;"
                                      styleClass="blue"
                                      value="#{cheq.dataLancamento}">
                            <f:convertDateTime pattern="dd/MM/yyyy" />
                        </h:outputText>
                    </h:column>
                    <h:column>
                        <f:facet name="header">
                            <h:outputText style="font-weight: bold; font-size:9px;"
                                          value="#{msg_aplic.prt_Finan_GestaoRecebiveis_dataCompensacao}" />
                        </f:facet>
                        <h:outputText style="font-weight: bold; font-size:9px;"
                                      styleClass="blue"
                                      value="#{cheq.dataCompensacao}">
                            <f:convertDateTime pattern="dd/MM/yyyy" />
                        </h:outputText>
                    </h:column>
                    <h:column>
                        <f:facet name="header">
                            <h:outputText style="font-weight: bold; font-size:9px;"
                                          value="#{msg_aplic.prt_Cheque_valor}" />
                        </f:facet>
                        <h:outputText style="font-weight: bold; font-size:9px;"
                                      styleClass="blue" value="#{cheq.valor}">
                            <f:converter converterId="FormatadorNumerico" />
                        </h:outputText>
                    </h:column>
                    <h:column>
                        <f:facet name="header">
                            <h:outputText style="font-weight: bold; font-size:9px;"
                                          value="Conta Financeiro" />
                        </f:facet>
                        <h:outputText style="font-weight: bold; font-size:9px;"
                                      styleClass="blue" value="#{cheq.contaContido}"/>
                    </h:column>
                    <h:column>
                        <f:facet name="header">
                            <h:selectBooleanCheckbox value="#{GestaoLotesControle.todosChequesMarcados}"
                                                     title="Adicionar todos os cheques ao Lote">
                                <a4j:support event="onclick"
                                             action="#{GestaoLotesControle.marcaDesmarcaCheques}"
                                             reRender="panelMensagem, totalSelecionado, listaCheques"/>
                            </h:selectBooleanCheckbox>
                        </f:facet>
                        <h:selectBooleanCheckbox id="chequeEscolhido" value="#{cheq.chequeEscolhido}">
                            <a4j:support event="onclick"
                                         focus="chequeEscolhido"
                                         action="#{GestaoLotesControle.calcularTotalCheques}"
                                         reRender="panelMensagem, totalSelecionado"/>
                        </h:selectBooleanCheckbox>
                    </h:column>
                </rich:dataTable>
                <center>
                    <rich:datascroller for="listaCheques" maxPages="5" id="scResultado" />
                </center>
                <h:panelGrid id="totalSelecionado" styleClass="tablepreviewtotal" width="100%" columnClasses="colunaDireita">
                    <h:panelGroup>
                        <h:outputText value="#{msg_aplic.prt_Finan_GestaoRecebiveis_total} = "/>
                        <h:outputText value="R$ " styleClass="verde"/>
                        <h:outputText styleClass="verde" value="#{GestaoLotesControle.totalCheques}">
                            <f:converter converterId="FormatadorNumerico"/>
                        </h:outputText>
                    </h:panelGroup>
                </h:panelGrid>
                <h:panelGrid width="100%" columnClasses="colunaDireita" columns="1" style="margin-top:5px;">
                    <a4j:commandButton  action="#{GestaoLotesControle.depositarCheques}" 
                                        oncomplete="Richfaces.hideModalPanel('panelConsultaCheque'); atualizar();"
                                        styleClass="botoes" reRender="codigo, form"
                                        image="/imagens/Adicionar_ao_lote.png">
                    </a4j:commandButton>
                </h:panelGrid>
            </h:panelGroup>
        </h:form>
    </rich:modalPanel>

    <rich:modalPanel id="panelConsultaCartao" autosized="true" shadowOpacity="true" width="800" height="600"
                     onshow="document.getElementById('formConsultaCartao:nomeCa').focus();">
        <f:facet name="header">
            <h:outputText value="#{msg_aplic.prt_Finan_GestaoLotes_tituloConsultaCartoes}"/>
        </f:facet>
        <f:facet name="controls">
            <h:panelGroup>
                <h:graphicImage value="/imagens/close.png" style="cursor:pointer" id="hidelink1" />
                <rich:componentControl for="panelConsultaCartao"
                                       attachTo="hidelink1" operation="hide" event="onclick" />
            </h:panelGroup>
        </f:facet>
        <h:form id="formConsultaCartao">
            <h:panelGrid columns="2" rowClasses="linhaImpar, linhaPar"
                         columnClasses="classEsquerda, classDireita" width="100%">
                <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_Finan_GestaoRecebiveis_nome}" />
                <h:inputText id="nomeCa" size="40" maxlength="255" onblur="blurinput(this);"
                             onfocus="focusinput(this);" styleClass="form" value="#{GestaoLotesControle.nome}" />
                <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_Finan_GestaoRecebiveis_periodoCompensacao}" />
                <h:panelGroup>
                    <rich:calendar id="dataInicioCCa"
                                   value="#{GestaoLotesControle.dataInicialCompensacao}"
                                   inputSize="10"
                                   inputClass="form"
                                   oninputblur="blurinput(this);"
                                   oninputfocus="focusinput(this);"
                                   datePattern="dd/MM/yyyy"
                                   oninputchange="return validar_Data(this.id);"
                                   enableManualInput="true"
                                   zindex="2"
                                   showWeeksBar="false"/>
                    <h:outputText styleClass="tituloCampos" style="position:relative; top:0px; left:10px;" value="#{msg_aplic.prt_ate}" />
                    <rich:spacer width="12px"/>
                    <rich:calendar id="dataTerminoCCa"
                                   value="#{GestaoLotesControle.dataFinalCompensacao}"
                                   inputSize="10"
                                   inputClass="form"
                                   oninputblur="blurinput(this);"
                                   oninputfocus="focusinput(this);"
                                   oninputchange="return validar_Data(this.id);"
                                   datePattern="dd/MM/yyyy"
                                   enableManualInput="true"
                                   zindex="2"
                                   showWeeksBar="true" />
                    <rich:spacer width="5" />
                    <a4j:commandButton id="limparPeriodoCompensacao"
                                       action="#{GestaoLotesControle.limparPeriodoC}"
                                       image="/images/limpar.gif" title="Limpar período de compensação."
                                       reRender="dataInicioCCa, dataTerminoCCa"/>
                </h:panelGroup>
                <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_Finan_GestaoRecebiveis_periodoLancamento}" />
                <h:panelGroup>
                    <rich:calendar id="dataInicioLCa"
                                   value="#{GestaoLotesControle.dataInicialLancamentoC}"
                                   inputSize="10"
                                   inputClass="form"
                                   oninputblur="blurinput(this);"
                                   oninputfocus="focusinput(this);"
                                   oninputchange="return validar_Data(this.id);"
                                   datePattern="dd/MM/yyyy"
                                   enableManualInput="true"
                                   zindex="2"
                                   showWeeksBar="false"/>
                    <h:outputText styleClass="tituloCampos" style="position:relative; top:0px; left:10px;" value="#{msg_aplic.prt_ate}" />
                    <rich:spacer width="12px"/>
                    <rich:calendar id="dataTerminoLCa"
                                   value="#{GestaoLotesControle.dataFinalLancamentoC}"
                                   inputSize="10"
                                   inputClass="form"
                                   oninputblur="blurinput(this);"
                                   oninputfocus="focusinput(this);"
                                   oninputchange="return validar_Data(this.id);"
                                   datePattern="dd/MM/yyyy"
                                   enableManualInput="true"
                                   zindex="2"
                                   showWeeksBar="true" />
                    <rich:spacer width="5" />
                    <a4j:commandButton id="limparPeriodoLancamento"
                                       action="#{GestaoLotesControle.limparPeriodoLC}"
                                       image="/images/limpar.gif" title="Limpar período de lançamento."
                                       reRender="dataInicioLCa, dataTerminoLCa"/>
                </h:panelGroup>
            </h:panelGrid>
            <h:panelGrid columns="1" width="100%" columnClasses="centralizado" style="margin-top:5px;">
                <a4j:commandButton id="consultar" title="#{msg.msg_consultar_dados}" action="#{GestaoLotesControle.consultarInclusaoCartoes}"
                                   image="/imagens/botaoConsultar.png" reRender="listaCartoes, scResultado2, panelMensagem, formConsultaCartao"/>
            </h:panelGrid>
            <h:panelGrid id="panelMensagem" rendered="#{!empty GestaoLotesControle.mensagemDetalhada}" columns="1" width="100%" styleClass="tabMensagens">
                <h:outputText styleClass="mensagemDetalhada" value="#{GestaoLotesControle.mensagemDetalhada}"/>
            </h:panelGrid>
            <div class="sep" style="margin:5px 0 10px 0;"></div>
            <h:panelGroup>
                <rich:dataTable id="listaCartoes" width="100%" styleClass="textsmall" style="border-color:#FFF"
                                columnClasses="colunaEsquerda, colunaEsquerda, centralizado, centralizado,
                                colunaDireita, centralizado, centralizado" rows="10"
                                headerClass="subordinado" rowClasses="linhaImpar, linhaPar"
                                value="#{GestaoLotesControle.listaCartoes}" var="cart">
                    <rich:column style="border-color:#FFF">
                        <f:facet name="header">
                            <h:outputText style="font-weight: bold; font-size:9px;"
                                          value="#{msg_aplic.prt_Finan_GestaoRecebiveis_nome}" />
                        </f:facet>
                        <h:outputText style="font-weight: bold; font-size:9px;"
                                      styleClass="blue"
                                      value="#{cart.nomePagador}" />
                    </rich:column>
                    <rich:column style="border-color:#FFF">
                        <f:facet name="header">
                            <h:outputText style="font-weight: bold; font-size:9px;"
                                          value="#{msg_aplic.prt_OperadoraCartao_tituloForm}" />
                        </f:facet>
                        <h:outputText style="font-weight: bold; font-size:9px;"
                                      styleClass="blue" value="#{cart.operadora}" />
                    </rich:column>
                    <rich:column style="border-color:#FFF">
                        <f:facet name="header">
                            <h:outputText style="font-weight: bold; font-size:9px;"
                                          value="#{msg_aplic.prt_Finan_GestaoRecebiveis_dataLancamento}" />
                        </f:facet>
                        <h:outputText style="font-weight: bold; font-size:9px;"
                                      styleClass="blue"
                                      value="#{cart.dataLancamento}">
                            <f:convertDateTime pattern="dd/MM/yyyy" />
                        </h:outputText>
                    </rich:column>
                    <rich:column style="border-color:#FFF">
                        <f:facet name="header">
                            <h:outputText style="font-weight: bold; font-size:9px;"
                                          value="#{msg_aplic.prt_Finan_GestaoRecebiveis_dataCompensacao}" />
                        </f:facet>
                        <h:outputText style="font-weight: bold; font-size:9px;"
                                      styleClass="blue"
                                      value="#{cart.dataCompensacao}">
                            <f:convertDateTime pattern="dd/MM/yyyy" />
                        </h:outputText>
                    </rich:column>
                    <rich:column style="border-color:#FFF">
                        <f:facet name="header">
                            <h:outputText style="font-weight: bold; font-size:9px;"
                                          value="#{msg_aplic.prt_Cheque_valor}" />
                        </f:facet>
                        <h:outputText style="font-weight: bold; font-size:9px;"
                                      styleClass="blue" value="#{cart.valor}">
                            <f:converter converterId="FormatadorNumerico" />
                        </h:outputText>
                    </rich:column>
                    <rich:column style="border-color:#FFF">
                        <f:facet name="header">
                            <h:selectBooleanCheckbox value="#{GestaoLotesControle.todosCartoesMarcados}"
                                                     title="Adicionar todos os cartoes ao Lote">
                                <a4j:support event="onclick"
                                             action="#{GestaoLotesControle.marcaDesmarcaCartoes}"
                                             reRender="panelMensagem, totalSelecionado2, listaCartoes"/>
                            </h:selectBooleanCheckbox>
                        </f:facet>
                        <h:selectBooleanCheckbox id="cartaoEscolhido" value="#{cart.cartaoEscolhido}">
                            <a4j:support event="onclick" focus="cartaoEscolhido"
                                         action="#{GestaoLotesControle.calcularTotalCartoes}"
                                         reRender="panelMensagem, totalSelecionado2"
                                         rendered="#{ConfiguracaoFinanceiroControle.confFinanceiro.usarMovimentacaoContas}"/>
                        </h:selectBooleanCheckbox>
                    </rich:column>
                </rich:dataTable>
                <center>
                    <rich:datascroller for="listaCartoes" maxPages="5" id="scResultado2" />
                </center>
                <h:panelGrid id="totalSelecionado2" styleClass="tablepreviewtotal" width="100%" columnClasses="colunaDireita">
                    <h:panelGroup>
                        <h:outputText value="#{msg_aplic.prt_Finan_GestaoRecebiveis_total} = "/>
                        <h:outputText value="R$ " styleClass="verde"/>
                        <h:outputText styleClass="verde" value="#{GestaoLotesControle.totalCartoes}">
                            <f:converter converterId="FormatadorNumerico"/>
                        </h:outputText>
                    </h:panelGroup>
                </h:panelGrid>
                <h:panelGrid width="100%" columnClasses="colunaDireita" columns="1" style="margin-top:5px;">
                    <a4j:commandButton  action="#{GestaoLotesControle.depositarCartoes}" oncomplete="Richfaces.hideModalPanel('panelConsultaCartao');atualizar();"
                                        styleClass="botoes" reRender="form"
                                        image="/imagens/Adicionar_ao_lote.png">
                    </a4j:commandButton>
                </h:panelGrid>
            </h:panelGroup>
        </h:form>
    </rich:modalPanel>

    <rich:modalPanel id="modalContasLotePagou" autosized="true" shadowOpacity="true" width="600" height="220">
        <f:facet name="header">
            <h:panelGroup>
                <h:outputText  value="Lançamentos pagos pelo lote"/>
            </h:panelGroup>
        </f:facet>

        <f:facet name="controls">
            <h:panelGroup>
                <h:graphicImage value="/imagens/close.png" style="cursor:pointer" id="hidelinkmodalContasLotePagou" />
                <rich:componentControl for="modalContasLotePagou" attachTo="hidelinkmodalContasLotePagou" operation="hide" event="onclick" />
            </h:panelGroup>
        </f:facet>

        <a4j:form id="formContasLotePagou" style="height: 100%;" >
            <h:panelGroup layout="block"  style="height: 170px; overflow: auto;">
                <rich:dataTable value="#{GestaoLotesControle.listaMovContaQueLotePaga}" var="movConta" width="100%"
                                columnClasses="esquerda, direita">
                    <rich:column>
                        <f:facet name="header">
                            <h:outputText value="Descrição"  ></h:outputText>
                        </f:facet>


                        <a4j:commandLink  rendered="#{(!(movConta.descricao eq 'TOTAL'))}" action="#{GestaoLotesControle.editarLancamentoPagamentoConjunto}">
                            <h:outputText value="#{movConta.descricao}" ></h:outputText>
                            <f:setPropertyActionListener value="#{movConta}" target="#{GestaoLotesControle.movContaPagaEmConjunto}"></f:setPropertyActionListener>
                        </a4j:commandLink>
                        <h:outputText rendered="#{(movConta.descricao eq 'TOTAL')}" value="TOTAL" ></h:outputText>

                    </rich:column>
                    <rich:column>
                        <f:facet name="header">
                            <h:outputText value="Valor"  ></h:outputText>
                        </f:facet>
                        <h:outputText value="#{movConta.valor_Apresentar}" ></h:outputText>
                    </rich:column>
                </rich:dataTable>
            </h:panelGroup>
        </a4j:form>

    </rich:modalPanel>

    <%@include file="includes/include_historicoCheque.jsp" %>
    <%@include file="includes/include_historicoCartao.jsp" %>
    <%@include file="includes/include_modalSelecaoPlanoConta.jsp" %>
    <%@include file="includes/include_modalSelecaoCentroCusto.jsp" %>
    <%@include file="include_modal_retirarChequeLote.jsp" %>
    <%@include file="../../includes/autorizacao/include_autorizacao_funcionalidade.jsp" %>
    <jsp:include page="../../includes/include_panelMensagem_goBackBlock.jsp"/> 
</f:view>

