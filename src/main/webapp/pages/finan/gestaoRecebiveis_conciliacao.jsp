<%@page contentType="text/html"%>
<%@page pageEncoding="ISO-8859-1"%>
<%@include file="includes/imports.jsp" %>
<h:panelGroup layout="block" styleClass="margin-box" rendered="#{GestaoRecebiveisControle.visaoConciliacao}" id="idconciliacao">
    <script>
        msgs = ['Consultando banco de dados...', 'Processando resultado da consulta', 'Verificando extratos obtidos da operadora...', 'Listando parcelas de cartão de crédito...', 'Conciliando lançamentos...'];
        var ccZW = 0;
        var ccEX = 0;
        function addCCZW(zw, check) {
            var checado = check.checked;
            jQuery('.ccZW').prop('checked', false);
            ccZW = 0;
            check.checked = checado;
            if (checado) {
                ccZW = zw;
            }
            if (ccZW > 0 && ccEX > 0) {
                jQuery('.barraFixaClean').show();
            } else {
                jQuery('.barraFixaClean').hide();
            }
        }
        function addCCEX(ex, check) {
            var checado = check.checked;
            jQuery('.ccED').prop('checked', false);
            ccEX = 0;
            check.checked = checado;
            if (checado) {
                ccEX = ex;
            }
            if (ccZW > 0 && ccEX > 0) {
                jQuery('.barraFixaClean').show();
            } else {
                jQuery('.barraFixaClean').hide();
            }
        }
    </script>
    <style>
        .table-striped {
            width: 100%;
            font-size: 14px;
            color: #777;
            margin-bottom: 20px;
        }

        .table-striped th {
            border: 1px solid #e5e5e5;
            border-right: none;
            border-left: none;
            padding: 5px 0;
            text-transform: uppercase;
        }

        .table-striped td {
            text-align: center;
            padding: 2px;
        }

        .table-striped tr:nth-child(even) {
            background: #e5e5e5
        }

        .table-striped tr:hover {
            background: #d3d3d3;
        }

        .table-striped thead tr:hover {
            background: #fff;
        }

        .table-striped thead tr th {
            padding: 10px 5px;
        }

        .table-striped tbody tr td a i {
            color: #777;
        }

        .table-striped-separator {
            width: 5px;
            background: #fff;
        }

        .table-striped-separator + .table-striped-separator {
            border-left: 1px solid black;
        }

        .table-striped-options {
            display: flex;
            justify-content: center;
        }

        .table-striped-options a {
            margin: 2px;
        }

        .table-info tr td {
            padding: 10px;
            text-transform: uppercase;
        }

        .conciliacao-container {
            display: flex;
            flex-direction: column;
            box-sizing: border-box;
        }

        .conciliacao-info {
            display: flex;
            justify-content: space-between;
            margin-bottom: 20px;
        }

        .conciliacao-info div {
            flex: 1;
            text-align: center;
        }

        .conciliacao-info div > span {
            display: block;
            color: #666;
            font-size: 20px;
            margin-bottom: 20px;
        }

        .conciliacao-status {
            width: 600px;
            margin-bottom: 20px;
        }

        .conciliacao-info-zw,
        .conciliacao-info-extrato {
            padding: 0 10px;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
        }

        .alert {
            color: red !important;
        }

        .linkalunoextrato span {
            text-transform: capitalize;
            font-size: 14.5px !important;
            font-weight: normal;
        }
        .linhacancelamento td, .linhacancelamento td .linkalunoextrato span {
            color: red !important;
        }

    </style>
    <div class="conciliacao-container">
        <table class="conciliacao-status">
            <tr>
                <td>
                    <a4j:commandLink action="#{GestaoRecebiveisControle.toggleOk}"
                                     reRender="idconciliacao">
                        <h:panelGroup layout="block" style="width: 5px;height:30px;background-color: #077113;#{GestaoRecebiveisControle.ok ? '' : 'opacity: 0.3;'}">
                        </h:panelGroup>
                    </a4j:commandLink>
                </td>
                <td>
                    <a4j:commandLink reRender="idconciliacao"
                                     style="#{GestaoRecebiveisControle.ok ? '' : 'opacity: 0.3;'}padding: 0 10px 0 5px"
                                     action="#{GestaoRecebiveisControle.toggleOk}" styleClass="linkLegenda">
                        <h:outputText value="Conciliado"/>
                    </a4j:commandLink>
                </td>
                <td>
                    <a4j:commandLink action="#{GestaoRecebiveisControle.togglePendencias}"
                                     reRender="idconciliacao">
                        <h:panelGroup layout="block" style="width: 5px;height:30px;background-color: #FFFF00;#{GestaoRecebiveisControle.pendencia ? '' : 'opacity: 0.3;'}">
                        </h:panelGroup>
                    </a4j:commandLink>
                </td>
                <td>
                    <a4j:commandLink action="#{GestaoRecebiveisControle.togglePendencias}"
                                     reRender="idconciliacao"
                                     style="padding: 0 10px 0 5px;#{GestaoRecebiveisControle.pendencia ? '' : 'opacity: 0.3;'}"
                                     styleClass="linkLegenda">

                        <h:outputText value="Alerta"/>
                    </a4j:commandLink>
                </td>
                <td>
                    <a4j:commandLink action="#{GestaoRecebiveisControle.toggleNE}"
                                     reRender="idconciliacao">
                        <h:panelGroup layout="block" style="width: 5px;height:30px;background-color: #A90102;#{GestaoRecebiveisControle.nao_encontrado ? '' : 'opacity: 0.3;'}">
                        </h:panelGroup>
                    </a4j:commandLink>
                </td>
                <td>
                    <a4j:commandLink  action="#{GestaoRecebiveisControle.toggleNE}"
                                      reRender="idconciliacao"
                                      style="padding: 0 10px 0 5px; #{GestaoRecebiveisControle.nao_encontrado ? '' : 'opacity: 0.3;'}"
                                      styleClass="linkLegenda ">
                        <h:outputText value="Não conciliado"/>
                    </a4j:commandLink>
                </td>
                <td>
                    <a4j:commandLink action="#{GestaoRecebiveisControle.toggleEstornado}"
                                     reRender="idconciliacao">
                        <h:panelGroup layout="block" style="width: 5px;height:30px;background-color: #0f0000;#{GestaoRecebiveisControle.estornado ? '' : 'opacity: 0.3;'}">
                        </h:panelGroup>
                    </a4j:commandLink>
                </td>
                <td>
                    <a4j:commandLink  action="#{GestaoRecebiveisControle.toggleEstornado}"
                                      reRender="idconciliacao"
                                      style="padding: 0 10px 0 5px; #{GestaoRecebiveisControle.estornado ? '' : 'opacity: 0.3;'}"
                                      styleClass="linkLegenda ">
                        <h:outputText value="Estornado Sistema Pacto"/>
                    </a4j:commandLink>
                </td>

                <td>
                    <a4j:commandLink action="#{GestaoRecebiveisControle.toggleEstornadoOperadora}"
                                     reRender="idconciliacao">
                        <h:panelGroup layout="block" style="width: 5px;height:30px;background-color: #ff6e1e;#{GestaoRecebiveisControle.estornadoOperadora ? '' : 'opacity: 0.3;'}">
                        </h:panelGroup>
                    </a4j:commandLink>
                </td>
                <td>
                    <a4j:commandLink  action="#{GestaoRecebiveisControle.toggleEstornadoOperadora}"
                                      reRender="idconciliacao"
                                      style="padding: 0 10px 0 5px; #{GestaoRecebiveisControle.estornadoOperadora ? '' : 'opacity: 0.3;'}"
                                      styleClass="linkLegenda ">
                        <h:outputText value="Cancelamento/Chargeback"/>
                    </a4j:commandLink>
                </td>

            </tr>
        </table>
        <div class="conciliacao-info">
            <div class="conciliacao-info-zw">
                <span>SISTEMA PACTO</span>
                <table class="table-striped table-info">
                    <tr>
                        <td>Crédito:</td>
                        <td>
                            <h:outputText value="#{GestaoRecebiveisControle.totalizadorConciliacaoDTO.totalCreditoZW}">
                                <f:convertNumber type="number" groupingUsed="true" minFractionDigits="2" pattern="#{MovPagamentoControle.empresaLogado.moeda} #0.00"/>
                            </h:outputText>
                        </td>
                    </tr>
                    <tr>
                        <td>Débito:</td>
                        <td>
                            <h:outputText value="#{GestaoRecebiveisControle.totalizadorConciliacaoDTO.totalDebitoZW}">
                                <f:convertNumber type="number" groupingUsed="true" minFractionDigits="2" pattern="#{MovPagamentoControle.empresaLogado.moeda} #0.00"/>
                            </h:outputText>
                        </td>
                    </tr>
                    <tr class="negrito">
                        <td>Total:</td>
                        <td>
                            <h:outputText value="#{GestaoRecebiveisControle.totalizadorConciliacaoDTO.totalZW}">
                                <f:convertNumber type="number" groupingUsed="true" minFractionDigits="2" pattern="#{MovPagamentoControle.empresaLogado.moeda} #0.00"/>
                            </h:outputText>
                        </td>
                    </tr>
                </table>

            </div>
            <div class="conciliacao-info-extrato">
                <span>EXTRATO DA OPERADORA</span>
                <table class="table-striped table-info">
                    <tr>
                        <td>Crédito:</td>
                        <td><h:outputText value="#{GestaoRecebiveisControle.totalizadorConciliacaoDTO.qtdCredito}"/></td>
                        <td>
                            <h:outputText value="#{GestaoRecebiveisControle.totalizadorConciliacaoDTO.creditoBruto}"
                                          title="Valor Bruto: #{GestaoRecebiveisControle.totalizadorConciliacaoDTO.creditoLiquidoApresentar}">
                                <f:convertNumber type="number" groupingUsed="true" minFractionDigits="2" pattern="#{MovPagamentoControle.empresaLogado.moeda} #0.00"/>
                            </h:outputText>
                        </td>
                    </tr>
                    <tr>
                        <td>Débito:</td>
                        <td><h:outputText value="#{GestaoRecebiveisControle.totalizadorConciliacaoDTO.qtdDebito}"/></td>
                        <td>
                            <h:outputText value="#{GestaoRecebiveisControle.totalizadorConciliacaoDTO.debitoBruto}"
                                          title="Valor Bruto: #{GestaoRecebiveisControle.totalizadorConciliacaoDTO.debitoLiquidoApresentar}">
                                <f:convertNumber type="number" groupingUsed="true" minFractionDigits="2" pattern="#{MovPagamentoControle.empresaLogado.moeda} #0.00"/>
                            </h:outputText>
                        </td>
                    </tr>

                    <tr class="negrito">
                        <td>Total:</td>
                        <td><h:outputText value="#{GestaoRecebiveisControle.totalizadorConciliacaoDTO.qtdTotal}"/></td>
                        <td>
                            <h:outputText value="#{GestaoRecebiveisControle.totalizadorConciliacaoDTO.totalBruto}"
                                          title="Valor Bruto: #{GestaoRecebiveisControle.totalizadorConciliacaoDTO.totalLiquidoApresentar}">
                                <f:convertNumber type="number" groupingUsed="true" minFractionDigits="2" pattern="#{MovPagamentoControle.empresaLogado.moeda} #0.00"/>
                            </h:outputText>
                        </td>
                    </tr>

                    <tr class="alert">
                        <td>Cancelamentos / </br>Chargebacks:</td>
                        <td><h:outputText value="#{GestaoRecebiveisControle.totalizadorConciliacaoDTO.qtdCancelamentos}"/></td>
                        <td>
                            <h:outputText value="#{GestaoRecebiveisControle.totalizadorConciliacaoDTO.cancelamentos}"
                                          title="Valor Bruto: #{GestaoRecebiveisControle.totalizadorConciliacaoDTO.cancelamentoLiquidoApresentar}">
                                <f:convertNumber type="number" groupingUsed="true" minFractionDigits="2" pattern="#{MovPagamentoControle.empresaLogado.moeda} #0.00"/>
                            </h:outputText>
                        </td>
                    </tr>
                </table>
                <table>

                </table>
            </div>
        </div>
        <div id="divDetalhesConciliacao" style="overflow-x: auto">
            <table class="table-striped">
                <thead>
                <tr>
                    <th></th>
                    <th></th>
                    <th style="text-align: left;">Pagador</th>
                    <th style="text-align: left;" >Conta</th>
                    <th><h:outputText value="AUT/NSU" title="Autorização ou NSU"/></th>
                    <th>
                         Parc.
                    </th>
                    <th>
                        <c:if test="${GestaoRecebiveisControle.tipoConciliacao == 3}">
                            Lançamento
                        </c:if>
                        <c:if test="${GestaoRecebiveisControle.tipoConciliacao != 3}">
                            Compensação
                        </c:if>
                    </th>
                    <th style="text-align: right; padding-right: 5px;">Vl. Bruto</th>
                    <th class="table-striped-separator"></th>
                    <th class="table-striped-separator"></th>
                    <th></th>
                    <th><h:outputText value="AUT/NSU" title="Autorização ou NSU"/></th>
                    <th>
                            Parc.
                    </th>
                    <th>
                        <c:if test="${GestaoRecebiveisControle.tipoConciliacao == 3}">
                            Lançamento
                        </c:if>
                        <c:if test="${GestaoRecebiveisControle.tipoConciliacao != 3}">
                            Compensação
                        </c:if>
                    </th>
                    <th style="text-align: right; padding-right: 5px;">Vl. Bruto</th>
                    <th></th>
                </tr>
                </thead>
                <tbody>
                <a4j:repeat value="#{GestaoRecebiveisControle.extratoDiario}" var="item">
                    <tr>
                        <td>
                            <h:panelGroup layout="block" style="width: 5px;height:35px;background-color: #{item.situacao.corLinha};">
                            </h:panelGroup>
                        </td>
                        <td>
                            <h:panelGroup layout="block" styleClass="chk-fa-container inline"
                                          rendered="#{item.situacao == 'AUTORIZACAO_NAO_EXISTE' and ((item.movPagamento != null && item.movPagamento.codigo > 0) || item.cartao != null)}">
                                <h:selectBooleanCheckbox styleClass="tooltipster ccZW" onclick="addCCZW(1, this);" value="#{item.cartaoEscolhido}"
                                                         title="Marque este checkbox e o checkbox do item no extrato da operadora que você deseja conciliar. Depois clique no botão 'Mesclar' que aparecerá no canto inferior da página."
                                />
                                <span/>
                            </h:panelGroup>
                        </td>
                        <td style="text-align: left">
                            <a4j:commandLink action="#{GestaoRecebiveisControle.irParaTelaClienteEx}"
                                             style="font-weight: bold; font-size:9px;" styleClass="linkalunoextrato"
                                             oncomplete="abrirPopup('../../clienteNav.jsp?page=viewCliente', 'Cliente', 1024, 700);">
                                <h:outputText value="#{item.nomePagadorCortado}"
                                              styleClass="tooltipster"
                                              title="#{item.informacoes} #{item.movPagamento.adquirenteVO.nome} "/>
                            </a4j:commandLink>
                        </td>
                        <td style="text-align: left; max-width: 10%; text-overflow: ellipsis" width="150px">
                            <h:outputText value="#{item.contaMovimento}" />
                        </td>
                        <td>
                            <a4j:commandLink style="font-size: 14px !important;color: #f70e37;"  title="NSU diferente do Extrato"
                                             styleClass="tooltipster inline"
                                             rendered="#{item.stone and (item.tipoConciliacao != 3) and item.tipoFormaPagamento eq 'CA'
                                                        and not empty item.movPagamento.autorizacaoCartao and (item.autorizacao == item.movPagamento.autorizacaoCartao) and (item.nsu != item.movPagamento.nsu)
                                             }">
                                <i class="fa-icon-warning-sign"></i>
                            </a4j:commandLink>
                            <a4j:commandLink style="font-size: 14px !important;color: #f70e37;"  title="Autorização diferente do Extrato"
                                             styleClass="tooltipster inline"
                                             rendered="#{item.stone and (item.tipoConciliacao != 3) and item.tipoFormaPagamento eq 'CD'
                                                        and not empty item.movPagamento.autorizacaoCartao and (item.autorizacao != item.movPagamento.autorizacaoCartao)
                                             }">
                                <i class="fa-icon-warning-sign"></i>
                            </a4j:commandLink>
                            <a4j:commandLink style="font-size: 14px !important;color: #f70e37;"  title="Autorização diferente do Extrato"
                                             styleClass="tooltipster inline"
                                             rendered="#{item.stone and (item.tipoConciliacao != 3) and item.tipoFormaPagamento eq 'CA'
                                                        and not empty item.movPagamento.autorizacaoCartao and (item.autorizacao != item.movPagamento.autorizacaoCartao)
                                             }">
                                <i class="fa-icon-warning-sign"></i>
                            </a4j:commandLink>
                            <h:outputText value="#{item.movPagamento.autorizacaoCartao}" rendered="#{item.tipoConciliacao == 3 or (item.tipoFormaPagamento eq 'CD' and item.tipoConciliacao != 3)}"/>
                            <h:outputText value="#{item.cartao.autorizacao}" rendered="#{item.tipoConciliacao != 3 and not (item.tipoFormaPagamento eq 'CD')}"/>
                            <h:outputText value=" / #{item.movPagamento.nsu}" rendered="#{item.stone and not empty item.movPagamento.nsu and not empty item.movPagamento.autorizacaoCartao and not empty item.nomePagador}"/>
                            <h:outputText value="#{item.nsu}" rendered="#{empty item.cartao.autorizacao and empty item.movPagamento.autorizacaoCartao and not empty item.nsu and not empty item.nomePagador}"/>
                        </td>
                        <td>
                            <a4j:commandLink style="font-size: 14px !important;color: #f70e37;"  title="Forma de Pagamento Diferente do Extrato"
                                             styleClass="tooltipster inline"
                                             rendered="#{item.formaPagamentoDiferente}">
                                <i class="fa-icon-warning-sign"></i>
                            </a4j:commandLink>
                            <h:outputText value="#{item.movPagamento.nrParcelaCartaoCredito}" rendered="#{item.tipoConciliacao == 3 or (item.tipoFormaPagamento eq 'CD' and item.tipoConciliacao != 3)}"/>
                            <h:outputText value="#{item.cartao.nrParcelas_apresentar}" rendered="#{item.tipoConciliacao != 3 and not (item.tipoFormaPagamento eq 'CD')}"/>
                        </td>
                        <c:if test="${GestaoRecebiveisControle.tipoConciliacao == 3}">
                            <td style="text-align: center;">
                                <h:outputText value="#{item.dataLancamentoMP}"/>
                            </td>
                        </c:if>
                        <c:if test="${GestaoRecebiveisControle.tipoConciliacao != 3}">
                            <td>
                                <a4j:commandLink style="font-size: 14px !important; position: absolute; right: 56%; margin-top: 1px;"
                                                 styleClass="inline tooltipster"
                                                 reRender="painelPendenciaItemExtratoEstorno"
                                                 id="testeLuiz"
                                                 rendered="#{item.situacao == 'ESTORNADO_OPERADORA' and item.tipoConciliacao != 6}"
                                                 title="Operadora informou que este recebimento foi estornado, clique para estornar no ZW também"
                                                 oncomplete="Richfaces.showModalPanel('modalEstornoExtrato')"
                                                 action="#{GestaoRecebiveisControle.abrirEstornoRecibo}">
                                    <i class="fa-icon-warning-sign"></i>
                                </a4j:commandLink>
                                <a4j:commandLink style="font-size: 14px !important; position: absolute; right: 56%; margin-top: 1px;"
                                                 styleClass="inline tooltipster"
                                                 title="Pagamento estornado pela Adquirente, favor verificar com o Aluno e/ou Adquirente sobre o motivo!"
                                                 reRender="painelPendenciaItemExtratoEstorno"
                                                 id="modalChargeback"
                                                 rendered="#{item.situacao == 'ESTORNADO_OPERADORA' and item.tipoConciliacao == 6}"
                                                 oncomplete="Richfaces.showModalPanel('modalEstornoChargeback')">
                                    <i class="fa-icon-warning-sign"></i>
                                </a4j:commandLink>
                                <a4j:commandLink style="font-size: 14px !important; position: absolute; right: 56%; margin-top: 1px;"
                                                 styleClass="inline tooltipster"
                                                 title="Data de pagamento diferente da data informada pela adquirente no Extrato. Clique para conciliar."
                                                 reRender="painelPendenciaItemExtrato"
                                                 rendered="#{item.situacao == 'PENDENCIAS' and (item.dataDiferente)}"
                                                 oncomplete="Richfaces.showModalPanel('modalPendenciaItemExtrato')"
                                                 action="#{GestaoRecebiveisControle.abrirAvisoData}">
                                    <i class="fa-icon-warning-sign"></i>
                                </a4j:commandLink>

                                <div style="display: inline-flex; margin-left: 36px; display: grid; grid-template-columns: 1fr 1fr;" >
                                <h:outputText rendered="#{item.tipoConciliacao == 3 or (item.tipoFormaPagamento eq 'CD' and item.tipoConciliacao != 3)}" value="#{item.dataMP}"/>
                                <h:outputText rendered="#{item.tipoConciliacao != 3 and not (item.tipoFormaPagamento eq 'CD')}" value="#{item.dataCC}"/>
                                    <h:graphicImage value="/imagens/antecipacao.svg"
                                                    rendered="#{item.antecipacao && item.situacao == 'OK' && !item.alterouDataRecebimentoZWAutomaticamente}"
                                                    style="width:15px;height:15px; margin-left: 6px"
                                                    styleClass="tooltipster"
                                                    title="Este recebimento foi antecipado na adquirente e teve sua data de compensação alterada automaticamente.</br>
                                                    Data de compensação original Sistema Pacto: #{item.dataPgtoOriginalAntesDaAntecipacaoApresentar}">
                                    </h:graphicImage>
                                    <h:graphicImage value="/imagens/calendar-check-black.svg"
                                                    rendered="#{item.alterouDataRecebimentoZWAutomaticamente && !item.antecipacao && item.situacao == 'OK'}"
                                                    style="width:15px;height:15px; margin-left: 6px"
                                                    styleClass="tooltipster"
                                                    title="Este recebimento teve sua data de compensação alterada automaticamente pois estava diferente da que veio no extrato.</br>
                                                        Data de compensação original Sistema Pacto: #{item.dataPgtoOriginalZWAntesDaAlteracaoAutomaticaApresentar}">
                                    </h:graphicImage>
                                </div>
                            </td>
                        </c:if>
                        <td style="text-align: right; padding-right: 5px;">
                            <a4j:commandLink style="font-size: 14px !important;" styleClass="inline"
                                             reRender="painelPendenciaItemExtrato"
                                             rendered="#{item.situacao == 'PENDENCIAS' and (item.valorDiferente)}"
                                             oncomplete="Richfaces.showModalPanel('modalPendenciaItemExtrato')"
                                             action="#{GestaoRecebiveisControle.abrirAlterarPagamento}">
                                <i class="fa-icon-warning-sign"></i>
                            </a4j:commandLink>
                            <h:outputText value="#{item.valorCC}" rendered="#{((not empty item.valorCC and item.tipoConciliacao != 3) and not (item.tipoFormaPagamento eq 'CD'))}"/>
                            <h:outputText value="#{item.valorMP}" rendered="#{((not empty item.valorMP and item.tipoConciliacao == 3) or (item.tipoFormaPagamento eq 'CD' and item.tipoConciliacao != 3))}"/>
                        </td>
                        <td class="table-striped-separator"></td>
                        <td class="table-striped-separator"></td>
                            <%-- DADOS OPERADORA--%>
                        <td>
                            <h:panelGroup layout="block" styleClass="chk-fa-container inline"
                                          rendered="#{item.situacao == 'AUTORIZACAO_NAO_EXISTE' and (not empty item.autorizacao or not empty item.nsu)}">
                                <h:selectBooleanCheckbox styleClass="tooltipster ccED" onclick="addCCEX(1, this);" value="#{item.itemEscolhido}"
                                                         title="Marque este checkbox e o checkbox do lançamento do ZW que você deseja conciliar. Depois clique no botão 'Mesclar' que aparecerá no canto inferior da página. "

                                />
                                <span/>
                            </h:panelGroup>
                        </td>
                        <td>
                            <h:outputText value="#{item.autorizacao}" rendered="#{not empty item.autorizacao}"/>
                            <h:outputText value=" / #{item.nsu}" rendered="#{item.stone and not empty item.nsu and not empty item.autorizacao}"/>
                            <h:outputText value="#{item.nsu}" rendered="#{empty item.autorizacao and not empty item.nsu}"
                                          title="Este número é o NSU, a operadora não enviou no extrato o código da autorização"
                                          styleClass="tooltipster"
                            />
                        </td>
                        <td>
                            <h:outputText value="#{item.nrParcelas_apresentar}" rendered="#{(not empty item.autorizacao or not empty item.nsu)}"/>
                        </td>
                        <td>
                            <h:outputText value="#{item.dataLancamentoApresentar}" rendered="#{(not empty item.autorizacao or not empty item.nsu) && GestaoRecebiveisControle.tipoConciliacao == 3}"/>
                            <h:outputText value="#{item.dataPrevistaPagamentoApresentar}" rendered="#{(not empty item.autorizacao or not empty item.nsu) && GestaoRecebiveisControle.tipoConciliacao != 3}"/>
                        </td>
                        <td style="text-align: right;">
                            <h:outputText value="#{item.valorBrutoApresentar}" rendered="#{(not empty item.autorizacao or not empty item.nsu)}"/>
                        </td>
                        <td>
                            <div class="table-striped-options">
                                <a4j:commandLink style="font-size: 14px !important;"
                                                 reRender="painelInfoItemExtrato"
                                                 title="#{item.pessoa.nomeApresentarExtrato} Clique para ver mais informações"
                                                 styleClass="tooltipster inline"
                                                 rendered="#{(not empty item.autorizacao or not empty item.nsu)}"
                                                 oncomplete="Richfaces.showModalPanel('modalInfoItemExtrato')"
                                                 action="#{GestaoRecebiveisControle.abrirAlterarPagamento}">
                                    <i class="fa-icon-info-sign"></i>
                                </a4j:commandLink>
                                <a4j:commandLink style="font-size: 14px !important;"
                                                 title="Lançar cartão avulso"
                                                 styleClass="tooltipster inline"
                                                 rendered="#{item.situacao == 'AUTORIZACAO_NAO_EXISTE' and (not empty item.autorizacao or not empty item.nsu)}"
                                                 oncomplete="#{GestaoRecebiveisControle.msgAlert}"
                                                 action="#{GestaoRecebiveisControle.novoCartaoAvulso}">
                                    <i class="fa-icon-plus"></i>
                                </a4j:commandLink>
                                <a4j:commandLink style="font-size: 14px !important;"
                                                 title="Reprocessar item"
                                                 reRender="idconciliacao"
                                                 styleClass="tooltipster inline"
                                                 rendered="#{(item.situacao == 'AUTORIZACAO_NAO_EXISTE' or item.situacao =='ESTORNADO_SISTEMA' or item.situacao == 'PENDENCIAS') and (not empty item.autorizacao or not empty item.nsu)}"
                                                 oncomplete="#{GestaoRecebiveisControle.mensagemNotificar}"
                                                 action="#{GestaoRecebiveisControle.reprocessarItemConciliacao}">
                                    <i class="fa-icon-refresh"></i>
                                </a4j:commandLink>
                            </div>
                        </td>
                    </tr>
                </a4j:repeat>
                <a4j:repeat value="#{GestaoRecebiveisControle.extratoDiarioCancelamentos}" var="item">
                    <tr>
                        <td>
                            <h:panelGroup layout="block" style="width: 5px;height:35px;background-color: #{item.situacao.corLinha};">
                            </h:panelGroup>
                        </td>
                        <td>
                            <h:panelGroup layout="block" styleClass="chk-fa-container inline"
                                          rendered="#{item.situacao == 'AUTORIZACAO_NAO_EXISTE' and ((item.movPagamento != null && item.movPagamento.codigo > 0) || item.cartao != null)}">
                                <h:selectBooleanCheckbox styleClass="tooltipster ccZW" onclick="addCCZW(1, this);" value="#{item.cartaoEscolhido}"
                                                         title="Marque este checkbox e o checkbox do item no extrato da operadora que você deseja conciliar. Depois clique no botão 'Mesclar' que aparecerá no canto inferior da página."
                                />
                                <span/>
                            </h:panelGroup>
                        </td>
                        <td style="text-align: left">
                            <a4j:commandLink action="#{GestaoRecebiveisControle.irParaTelaClienteEx}"
                                             style="font-weight: bold; font-size:9px;" styleClass="linkalunoextrato"
                                             oncomplete="abrirPopup('../../clienteNav.jsp?page=viewCliente', 'Cliente', 1024, 700);">
                                <h:outputText value="#{item.nomePagadorCortado}"
                                              styleClass="tooltipster"
                                              title="#{item.informacoes} #{item.movPagamento.adquirenteVO.nome} "/>
                            </a4j:commandLink>
                        </td>
                        <td style="text-align: left; max-width: 10%; text-overflow: ellipsis" width="150px">
                            <h:outputText value="#{item.contaMovimento}" />
                        </td>
                        <td>
                            <h:outputText value="#{item.movPagamento.autorizacaoCartao}" rendered="#{item.tipoConciliacao == 3 or (item.tipoFormaPagamento eq 'CD' and item.tipoConciliacao != 3)}"/>
                            <h:outputText value="#{item.cartao.autorizacao}" rendered="#{item.tipoConciliacao != 3 and not (item.tipoFormaPagamento eq 'CD')}"/>
                            <h:outputText value=" / #{item.movPagamento.nsu}" rendered="#{item.stone and not empty item.movPagamento.nsu and not empty item.movPagamento.autorizacaoCartao and not empty item.nomePagador}"/>
                            <h:outputText value="#{item.nsu}" rendered="#{empty item.cartao.autorizacao and empty item.movPagamento.autorizacaoCartao and not empty item.nsu and not empty item.nomePagador}"/>
                        </td>
                        <td>
                            <a4j:commandLink style="font-size: 14px !important;color: #f70e37;"  title="Forma de Pagamento Diferente do Extrato"
                                             styleClass="tooltipster inline"
                                             rendered="#{item.formaPagamentoDiferente}">
                                <i class="fa-icon-warning-sign"></i>
                            </a4j:commandLink>
                            <h:outputText value="#{GestaoRecebiveisControle.estornadoOperadora ? '' : item.movPagamento.nrParcelaCartaoCredito}" rendered="#{item.tipoConciliacao == 3 or (item.tipoFormaPagamento eq 'CD' and item.tipoConciliacao != 3)}"/>
                            <h:outputText value="#{GestaoRecebiveisControle.estornadoOperadora ? '' : item.cartao.nrParcelas_apresentar}" rendered="#{item.tipoConciliacao != 3 and not (item.tipoFormaPagamento eq 'CD')}"/>
                        </td>
                        <c:if test="${GestaoRecebiveisControle.tipoConciliacao == 3}">
                            <td style="text-align: center;">
                                <h:outputText value="#{item.dataLancamentoMP}"/>
                            </td>
                        </c:if>
                        <c:if test="${GestaoRecebiveisControle.tipoConciliacao != 3}">
                            <td>
                                <a4j:commandLink style="font-size: 14px !important;" styleClass="inline"
                                                 reRender="painelPendenciaItemExtratoEstorno tooltipster"
                                                 id="testeLuiz"
                                                 title="Operadora informou que este recebimento foi estornado, clique para estornar no ZW também"
                                                 rendered="#{not GestaoRecebiveisControle.estornadoOperadora and item.situacao == 'ESTORNADO_OPERADORA' and item.tipoConciliacao != 6}"
                                                 oncomplete="Richfaces.showModalPanel('modalEstornoExtrato')"
                                                 action="#{GestaoRecebiveisControle.abrirEstornoRecibo}">
                                    <i class="fa-icon-warning-sign"></i>
                                </a4j:commandLink>
                                <a4j:commandLink style="font-size: 14px !important;" styleClass="inline"
                                                 reRender="painelPendenciaItemExtratoEstorno tooltipster"
                                                 title="Pagamento estornado pela Adquirente, favor verificar com o Aluno e/ou Adquirente sobre o motivo!"
                                                 id="modalChargeback"
                                                 rendered="#{not GestaoRecebiveisControle.estornadoOperadora and item.situacao == 'ESTORNADO_OPERADORA' and item.tipoConciliacao == 6}"
                                                 oncomplete="Richfaces.showModalPanel('modalEstornoChargeback')">
                                    <i class="fa-icon-warning-sign"></i>
                                </a4j:commandLink>
                                <a4j:commandLink style="font-size: 14px !important;"  styleClass="inline"
                                                 reRender="painelPendenciaItemExtrato tooltipster"
                                                 rendered="#{not GestaoRecebiveisControle.estornadoOperadora and item.situacao == 'PENDENCIAS' and (item.dataDiferente)}"
                                                 oncomplete="Richfaces.showModalPanel('modalPendenciaItemExtrato')"
                                                 title="A data do pagamento encontrado no sistema com esse código de autorização, não bate com o item do Extrato diário. Clique para alterar."
                                                 action="#{GestaoRecebiveisControle.abrirAvisoData}">
                                    <i class="fa-icon-warning-sign"></i>
                                </a4j:commandLink>

                                <h:outputText rendered="#{item.tipoConciliacao == 3 or (item.tipoFormaPagamento eq 'CD' and item.tipoConciliacao != 3)}" value="#{GestaoRecebiveisControle.estornadoOperadora ? '' : item.dataMP}"/>
                                <h:outputText rendered="#{item.tipoConciliacao != 3 and not (item.tipoFormaPagamento eq 'CD')}" value="#{GestaoRecebiveisControle.estornadoOperadora ? '' : item.dataCC}"/>
                            </td>
                        </c:if>
                        <td style="text-align: right; padding-right: 5px;">
                            <a4j:commandLink style="font-size: 14px !important;" styleClass="inline"
                                             reRender="painelPendenciaItemExtrato"
                                             rendered="#{item.situacao == 'PENDENCIAS' and (item.valorDiferente)}"
                                             oncomplete="Richfaces.showModalPanel('modalPendenciaItemExtrato')"
                                             action="#{GestaoRecebiveisControle.abrirAlterarPagamento}">
                                <i class="fa-icon-warning-sign"></i>
                            </a4j:commandLink>
                            <h:outputText value="#{GestaoRecebiveisControle.estornadoOperadora ? '' : item.valorCC}" rendered="#{((not empty item.valorCC and item.tipoConciliacao != 3) and not (item.tipoFormaPagamento eq 'CD'))}"/>
                            <h:outputText value="#{GestaoRecebiveisControle.estornadoOperadora ? '' : item.valorMP}" rendered="#{((not empty item.valorMP and item.tipoConciliacao == 3) or (item.tipoFormaPagamento eq 'CD' and item.tipoConciliacao != 3))}"/>
                        </td>
                        <td class="table-striped-separator"></td>
                        <td class="table-striped-separator"></td>



                            <%-- DADOS OPERADORA--%>
                        <td>
                            <h:panelGroup layout="block" styleClass="chk-fa-container inline"
                                          rendered="#{item.situacao == 'AUTORIZACAO_NAO_EXISTE' and (not empty item.autorizacao or not empty item.nsu)}">
                                <h:selectBooleanCheckbox styleClass="tooltipster ccED" onclick="addCCEX(1, this);" value="#{item.itemEscolhido}"
                                                         title="Marque este checkbox e o checkbox do lançamento do ZW que você deseja conciliar. Depois clique no botão 'Mesclar' que aparecerá no canto inferior da página. "

                                />
                                <span/>
                            </h:panelGroup>
                        </td>
                        <td>
                            <h:outputText value="#{item.autorizacao}" rendered="#{not empty item.autorizacao}"/>
                            <h:outputText value=" / #{item.nsu}" rendered="#{item.stone and not empty item.nsu and not empty item.autorizacao}"/>
                            <h:outputText value="#{item.nsu}" rendered="#{empty item.autorizacao and not empty item.nsu}"
                                          title="Este número é o NSU, a operadora não enviou no extrato o código da autorização"
                                          styleClass="tooltipster"
                            />
                        </td>
                        <td>
                            <h:outputText value="#{item.nrParcelas_apresentar}" rendered="#{(not empty item.autorizacao or not empty item.nsu)}"/>
                        </td>
                        <td>
                            <h:outputText value="#{item.dataLancamentoApresentar}" rendered="#{(not empty item.autorizacao or not empty item.nsu) && GestaoRecebiveisControle.tipoConciliacao == 3}"/>
                            <h:outputText value="#{item.dataPrevistaPagamentoApresentar}" rendered="#{(not empty item.autorizacao or not empty item.nsu) && GestaoRecebiveisControle.tipoConciliacao != 3}"/>
                        </td>
                        <td style="text-align: right;">
                            <h:outputText value="#{item.valorBrutoApresentar}" rendered="#{(not empty item.autorizacao or not empty item.nsu)}"/>
                        </td>
                        <td>
                            <div class="table-striped-options">
                                <a4j:commandLink style="font-size: 14px !important;"
                                                 reRender="painelInfoItemExtrato"
                                                 title="Ver mais informações"
                                                 styleClass="tooltipster inline"
                                                 rendered="#{(not empty item.autorizacao or not empty item.nsu)}"
                                                 oncomplete="Richfaces.showModalPanel('modalInfoItemExtrato')"
                                                 action="#{GestaoRecebiveisControle.abrirAlterarPagamento}">
                                    <i class="fa-icon-info-sign"></i>
                                </a4j:commandLink>
                                <a4j:commandLink style="font-size: 14px !important;"
                                                 title="Lançar cartão avulso"
                                                 styleClass="tooltipster inline"
                                                 rendered="#{item.situacao == 'AUTORIZACAO_NAO_EXISTE' and (not empty item.autorizacao or not empty item.nsu)}"
                                                 oncomplete="#{GestaoRecebiveisControle.msgAlert}"
                                                 action="#{GestaoRecebiveisControle.novoCartaoAvulso}">
                                    <i class="fa-icon-plus"></i>
                                </a4j:commandLink>
                                <a4j:commandLink style="font-size: 14px !important;"
                                                 title="Reprocessar item"
                                                 reRender="idconciliacao"
                                                 styleClass="tooltipster inline"
                                                 rendered="#{(item.situacao == 'AUTORIZACAO_NAO_EXISTE' or item.situacao =='ESTORNADO_SISTEMA' or item.situacao == 'PENDENCIAS') and (not empty item.autorizacao or not empty item.nsu)}"
                                                 oncomplete="#{GestaoRecebiveisControle.mensagemNotificar}"
                                                 action="#{GestaoRecebiveisControle.reprocessarItemConciliacao}">
                                    <i class="fa-icon-refresh"></i>
                                </a4j:commandLink>
                            </div>
                        </td>
                    </tr>
                </a4j:repeat>
                </tbody>
            </table>
        </div>
    </div>

    <h:panelGroup layout="block" style="width: 100%; text-align: right;">

        <a4j:commandLink id="alertaTodos"  style="margin: 0 10px"
                         rendered="#{GestaoRecebiveisControle.tipoConciliacao != 3}"
                         oncomplete="Richfaces.showModalPanel('modalAlertaTodos')"
                         reRender="panelDeposito,painelNaoConciliados, painelModalAlertaTodos"
                         styleClass="pure-button pure-button-small texto-size-14 inlineBlock">
            <i class="fa-icon-warning-sign" ></i>&nbsp Processar Todos
        </a4j:commandLink>

        <a4j:commandLink id="estornarTodos"  style="margin: 0 10px"
                         oncomplete="Richfaces.showModalPanel('modalEstornoExtratoTodos')"
                         reRender="panelDeposito,painelNaoConciliados, painelPendenciaItemExtratoEstornoTodos"
                         styleClass="pure-button pure-button-small texto-size-14 inlineBlock">
            <i class="fa-icon-usd" ></i>&nbsp Estornar
        </a4j:commandLink>

        <%--div pontilhada com botões de movimentação--%>
        <h:panelGroup rendered="#{GestaoRecebiveisControle.fpSelecionada == null || GestaoRecebiveisControle.fpSelecionada == 0}"
                id="panelBotMovConciliacao" style="display: inline-block; border: 2px dotted #c5c5c5; border-radius: 6px;">
        <h:selectOneMenu styleClass="newComboBox noBorderLeft"
                         value="#{GestaoRecebiveisControle.tipoMovimentar}">
            <f:selectItems value="#{GestaoRecebiveisControle.itensMovimentar}"/>
        </h:selectOneMenu>
        <a4j:commandLink id="depositarconciliacaodiv"  style="margin: 0 10px"
                         oncomplete="#{GestaoRecebiveisControle.onCompleteDepositar}"
                         action="#{GestaoRecebiveisControle.decidirMovimentacaoConciliacao}"
                         reRender="panelDeposito,painelNaoConciliados"
                         styleClass="pure-button pure-button-small pure-button-primary texto-size-14 inlineBlock">
            <i class="fa-icon-usd" ></i>&nbsp Movimentar
        </a4j:commandLink>
        </h:panelGroup>

        <%--somente botão de movimentação--%>
        <a4j:commandLink id="depositarconciliacao"  style="margin: 0 10px"
                         rendered="#{GestaoRecebiveisControle.fpSelecionada != null && GestaoRecebiveisControle.fpSelecionada != 0}"
                         oncomplete="#{GestaoRecebiveisControle.onCompleteDepositar}"
                         action="#{GestaoRecebiveisControle.decidirMovimentacaoConciliacao}"
                         reRender="panelDeposito,painelNaoConciliados"
                         styleClass="pure-button pure-button-small pure-button-primary texto-size-14 inlineBlock">
            <i class="fa-icon-usd" ></i>&nbsp Movimentar
        </a4j:commandLink>

    </h:panelGroup>
    <div style="margin-bottom: 0px;" class="barraFixaClean">
        <a4j:commandLink style="cursor: pointer;float: right; margin: 10px 55px;font-size: 16px; "
                         action="#{GestaoRecebiveisControle.mesclarCartoes}"
                         oncomplete="#{GestaoRecebiveisControle.msgAlert}"
                         styleClass="pure-button pure-button-primary botaoMesclar">
            <i class="fa-icon-signin"></i> &nbsp Mesclar
        </a4j:commandLink>

    </div>
    <script>
        carregarTooltipster();
    </script>
</h:panelGroup>

<rich:modalPanel id="modalDecidir"  styleClass="novaModal noMargin" shadowOpacity="true"
                 width="500" height="200">
    <f:facet name="header">
        <h:panelGroup>
            <h:outputText value="Conciliação"/>
        </h:panelGroup>
    </f:facet>
    <f:facet name="controls">
        <h:panelGroup>
            <h:outputText
                    styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign btnSelecionarModalidade"
                    id="hidelink7"/>
            <rich:componentControl for="modalDecidir" attachTo="hidelink7" operation="hide"
                                   event="onclick"/>
        </h:panelGroup>
    </f:facet>
    <h:panelGroup layout="block" style="margin: 10 20 10 20;"  id="painelNaoConciliados">

        <h:outputText styleClass="texto-size-16 texto-cor-cinza texto-font"
                      value="Existem #{GestaoRecebiveisControle.nrNaoConciliados} itens não conciliados na lista para movimentação. Deseja movimentar mesmo assim?"/>

        <div style="width: 100%; display: block; text-align: right; margin-top: 2vw;">
            <a onclick="Richfaces.hideModalPanel('modalDecidir');"
               class="pure-button pure-button-small texto-size-14 inlineBlock">
                Não, vou conciliar esses itens
            </a>

            <a4j:commandLink oncomplete="Richfaces.hideModalPanel('modalDecidir');#{GestaoRecebiveisControle.onCompleteDepositar}"
                             action="#{GestaoRecebiveisControle.salvaEscolha}"
                             reRender="panelDeposito"
                             styleClass="pure-button pure-button-small pure-button-primary texto-size-14 inlineBlock">
                Sim
            </a4j:commandLink>


        </div>


    </h:panelGroup>
</rich:modalPanel>
