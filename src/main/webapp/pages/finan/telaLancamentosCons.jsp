<%@page contentType="text/html" pageEncoding="ISO-8859-1" %>
<%@include file="includes/include_imports.jsp" %>
<c:set var="moduloSession" value="1" scope="session"/>

<link href="../../css_pacto.css" rel="stylesheet" type="text/css">
<link href="../../css/financeiro.css" rel="stylesheet" type="text/css">
<link href="../../css/jquery.treeTable.css" rel="stylesheet" type="text/css">


<script type="text/javascript" src="../../script/jquery.treeTable.js"></script>
<script type="text/javascript" src="../../script/demonstrativoFinan.js"></script>
<script type="text/javascript" src="../../script/jquery.maskedinput-1.2.2.js"></script>
<script type="text/javascript" language="javascript" src="../../script/Notifier.js"></script>
<script type="text/javascript" language="javascript" src="../../script/gobackblock.js"></script>
<head>
    <%@include file="includes/include_head_finan.jsp" %>
    <link href="../../css/gestaoRecebiveis1.0.min.css" rel="stylesheet" type="text/css">
    <link href="../../css/packcss1.0.min.css" rel="stylesheet" type="text/css">
    <link href="../../css/gestaoRecebiveis1.0.min.css" rel="stylesheet" type="text/css">
    <link href="../../css/pacto_flat_2.16.min.css" rel="stylesheet" type="text/css"/>
    <script type="text/javascript" language="javascript" src="../../script/script.js"></script>
    <script src="../../script/packJQueryPlugins.min.js" type="text/javascript"></script>
    <script type="text/javascript" language="javascript" src="${contextoFinan}/script/telaInicial.js"></script>
    <script type="text/javascript">
        jQuery.noConflict();
    </script>
    <style type="text/css">
        a {
            text-decoration: none !important;
        }
    </style>
</head>


<f:view>
    <jsp:include page="../../includes/include_carregando_ripple.jsp"/>
    <h:form id="form" style="overflow: initial;">
        <a4j:keepAlive beanName="ExportadorListaControle"/>
        <a4j:keepAlive beanName="PlanoContasControle" />
        <a4j:keepAlive beanName="CentroCustosControle" />
        <h:panelGroup layout="block" style="display: inline-table;width: 100%;" styleClass="fundoCinza">
            <h:panelGroup layout="block" styleClass="bgtop topoZW" rendered="#{MenuControle.apresentarTopo}">
                <jsp:include page="../../include_topo_novo.jsp" flush="true"/>
                <jsp:include page="../../include_menu_fin_flat.jsp" flush="true"/>
            </h:panelGroup>

            <h:panelGroup layout="block" styleClass="caixaCorpo">
                <h:panelGroup layout="block" style="height: 80%;width: 100%">
                    <h:panelGroup layout="block" styleClass="caixaMenuLatel">

                        <h:panelGroup id="panelGroupAtualizarLancarPagamento" layout="block"
                                      styleClass="container-imagem container-conteudo-central">
                            <h:panelGroup layout="block" styleClass="container-box zw_ui especial">
                                <h:panelGroup style="padding-bottom: 15px;" styleClass="container-box-header" layout="block">
                                    <h:panelGroup layout="block" styleClass="margin-box"
                                                  style="display:inline-block; justify-content: space-between; align-items: center">

                                        <h:panelGroup layout="block" styleClass="text">
                                            <a4j:commandLink
                                                    action="#{MovContaControle.encerrarModos}"
                                                    id="linkLancamentosFin"
                                                    reRender="panelGroupAtualizarLancarPagamento, formFiltrosGestaoRecebiveis"
                                                    styleClass="tudo"
                                                    style="padding: 15px; display: inline-block; #{MovContaControle.visaoConciliacao or MovContaControle.visaoLotes ? 'opacity: 0.3;' : 'border-bottom: solid #094771;'}">
                                                <h:outputText styleClass="container-header-titulo"
                                                              rendered="#{MovContaControle.verLancamentos and not MovContaControle.contasPagar and not MovContaControle.contasReceber}"
                                                              value="Lançamentos Financeiros"/>
                                                <h:outputText styleClass="container-header-titulo"
                                                              rendered="#{MovContaControle.contasReceber and not MovContaControle.contasPagar and not MovContaControle.verLancamentos}"
                                                              value="Contas a Receber"/>
                                                <h:outputText id="tituloContasAPagar"
                                                              styleClass="container-header-titulo"
                                                              rendered="#{MovContaControle.contasPagar and not MovContaControle.contasReceber and not MovContaControle.verLancamentos}"
                                                              value="Contas a Pagar"/>

                                                <h:outputLink styleClass="linkWiki"
                                                              rendered="#{MovContaControle.verLancamentos and not MovContaControle.contasPagar and not MovContaControle.contasReceber}"
                                                              value="#{SuperControle.urlBaseConhecimento}relatorio-ver-lancamentos/"
                                                              target="_blank">
                                                    <i class="fa-icon-question-sign tooltipster"
                                                       title="Clique e saiba mais: Lançamentos Financeiros"
                                                       style="font-size: 18px"></i>
                                                </h:outputLink>
                                                <h:outputLink styleClass="linkWiki"
                                                              rendered="#{MovContaControle.contasReceber and not MovContaControle.contasPagar and not MovContaControle.verLancamentos}"
                                                              value="#{SuperControle.urlBaseConhecimento}como-ver-todas-as-contas-a-receber-agendadas-do-mes/"
                                                              target="_blank">
                                                    <i class="fa-icon-question-sign tooltipster"
                                                       title="Clique e saiba mais: Lançamentos Financeiros"
                                                       style="font-size: 18px"></i>
                                                </h:outputLink>
                                                <h:outputLink styleClass="linkWiki"
                                                              rendered="#{MovContaControle.contasPagar and not MovContaControle.contasReceber and not MovContaControle.verLancamentos}"
                                                              value="#{SuperControle.urlBaseConhecimento}como-vejo-todas-as-despesas-a-pagar-do-mes/"
                                                              target="_blank">
                                                    <i class="fa-icon-question-sign tooltipster"
                                                       title="Clique e saiba mais: Lançamentos Financeiros"
                                                       style="font-size: 18px"></i>
                                                </h:outputLink>

                                            </a4j:commandLink>

                                            <a4j:commandLink
                                                    rendered="#{MovContaControle.contasPagar and not MovContaControle.contasReceber and not MovContaControle.verLancamentos}"
                                                    action="#{MovContaControle.entrarModoConciliacao}"
                                                    oncomplete="#{MovContaControle.msgAlert};#{MovContaControle.mensagemNotificar}"
                                                    id="linkLConciliacaoContasFin"
                                                    reRender="panelGroupAtualizarLancarPagamento, formFiltrosGestaoRecebiveis, modalPanelFiltrosConciliacaoContas, panelGroupAtualizarLancarPagamento"
                                                    styleClass="tudo step6"
                                                    style="padding: 15px; display: inline-block; #{MovContaControle.visaoConciliacao ? 'border-bottom: solid #094771;' : 'opacity: 0.3;'}">
                                                <h:outputText styleClass="container-header-titulo"
                                                              value="Conciliação"/>
                                                <h:outputLink styleClass="linkWiki"
                                                              value="#{SuperControle.urlBaseConhecimento}conciliacao-bancaria-de-contas-a-pagar/"
                                                              target="_blank">
                                                    <i class="fa-icon-question-sign tooltipster"
                                                       title="Clique e saiba mais: Conciliação bancária de contas a pagar"
                                                       style="font-size: 18px"></i>
                                                </h:outputLink>
                                            </a4j:commandLink>
                                            <a4j:commandLink
                                                    rendered="#{MovContaControle.contasReceber and not MovContaControle.contasPagar and not MovContaControle.verLancamentos}"
                                                    action="#{MovContaControle.entrarModoConciliacao}"
                                                    oncomplete="#{MovContaControle.msgAlert};#{MovContaControle.mensagemNotificar}"
                                                    id="linkLConciliacaoContasRecFin"
                                                    reRender="panelGroupAtualizarLancarPagamento, formFiltrosGestaoRecebiveis, modalPanelFiltrosConciliacaoContas, panelGroupAtualizarLancarPagamento"
                                                    styleClass="tudo step6"
                                                    style="padding: 15px; display: inline-block; #{MovContaControle.visaoConciliacao ? 'border-bottom: solid #094771;' : 'opacity: 0.3;'}">
                                                <h:outputText styleClass="container-header-titulo"
                                                              value="Conciliação"/>
                                                <h:outputLink styleClass="linkWiki"
                                                              value="#{SuperControle.urlBaseConhecimento}conciliacao-bancaria-facilitepay-contas-a-receber/"
                                                              target="_blank">
                                                    <i class="fa-icon-question-sign tooltipster"
                                                       title="Clique e saiba mais: Conciliação bancária de contas a receber"
                                                       style="font-size: 18px"></i>
                                                </h:outputLink>
                                            </a4j:commandLink>
                                            <a4j:commandLink
                                                    rendered="#{MovContaControle.contasPagar and !MovContaControle.verLancamentos and !MovContaControle.contasReceber}"
                                                    action="#{MovContaControle.entrarModoLotes}"
                                                    oncomplete="#{MovContaControle.msgAlert};#{MovContaControle.mensagemNotificar}"
                                                    id="linkLotePagamentos"
                                                    reRender="panelGroupAtualizarLancarPagamento"
                                                    styleClass="tudo step6"
                                                    style="padding: 15px; display: inline-block; #{MovContaControle.visaoLotes ? 'border-bottom: solid #094771;' : 'opacity: 0.3;'}">
                                                <h:outputText styleClass="container-header-titulo"
                                                              value="Lotes"/>
                                                <h:outputLink styleClass="linkWiki"
                                                              value="#{SuperControle.urlBaseConhecimento}lotes-de-pagamento-financeiro/"
                                                              target="_blank">
                                                    <i class="fa-icon-question-sign tooltipster"
                                                       title="Clique e saiba mais: Lotes de pagamentos"
                                                       style="font-size: 18px"></i>
                                                </h:outputLink>
                                            </a4j:commandLink>
                                            <h:outputText value="new!" style="color: #00c350;" styleClass="negrito"
                                                          rendered="#{MovContaControle.contasPagar or MovContaControle.contasReceber and not MovContaControle.verLancamentos}"
                                                          id="newConcContaReceber"/>
                                        </h:panelGroup>
                                    </h:panelGroup>
                                </h:panelGroup>
                                <h:panelGroup layout="block" styleClass="margin-box"
                                              rendered="#{!MovContaControle.visaoConciliacao and !MovContaControle.visaoLotes}">

                                    <a4j:outputPanel id="filtros">
                                        <rich:panel rendered="#{MovContaControle.apresentarFiltros}">
                                            <h:outputText styleClass="tituloCamposNegrito"
                                                          value="Filtros:"></h:outputText>
                                            <h:outputText styleClass="tituloDemonstrativo"
                                                          value="#{MovContaControle.filtros}"></h:outputText>
                                        </rich:panel>
                                    </a4j:outputPanel>
                                    <h:panelGroup id="panelTotalizadoresTopNovo" layout="block"
                                                  style="margin-top: 20px;margin-bottom: 20px;">
                                        <a4j:commandLink value="Refazer Consulta"
                                                         id="btnRefazerConnsulta"
                                                         oncomplete="Richfaces.showModalPanel('modalPanelFiltrosLancamento');"
                                                         style="vertical-align: middle"
                                                         styleClass="pure-button pure-button-primary step5 tudo">
                                        </a4j:commandLink>

                                        <a4j:commandLink style="margin-left: 10px;"
                                                         id="btnContasHoje"
                                                         reRender="form:pagarEmConjunto,btnContasZWMobile, items, painelPaginacao,containerFuncMask, painelPaginacaoTop, totalValor,totalRegistros,valorTotalReceb,valorTotalPag, panelMensagem, totalReg, valorTotal,panelTipoLancamento, filtros, checado, panelTotalizadoresTop, panelTotalizadoresButton, panelTotalizadoresTopNovo, modalPanelFiltrosLancamento"
                                                         styleClass="step1 tudo botaoModoTimeLine btnfaturadosOntem #{MovContaControle.filtrosLancamentos.somenteContasHoje ? 'ativo' : ''}"
                                                         actionListener="#{MovContaControle.consultarPaginadoListenerContasHoje}">
                                            <i class="fa-icon-money"></i> &nbsp Contas de Hoje
                                        </a4j:commandLink>

                                        <a4j:commandLink style="margin-left: 10px;"
                                                         id="btnContasZWMobile"
                                                         reRender="form:pagarEmConjunto, items, painelPaginacao,containerFuncMask, painelPaginacaoTop, totalValor,totalRegistros,valorTotalReceb,valorTotalPag, panelMensagem, totalReg, valorTotal,panelTipoLancamento, filtros, checado, panelTotalizadoresTop, panelTotalizadoresButton, panelTotalizadoresTopNovo, modalPanelFiltrosLancamento"
                                                         styleClass="step1 tudo botaoModoTimeLine btnfaturadosOntem #{MovContaControle.filtrosLancamentos.somenteContasMobile ? 'ativo' : ''}"
                                                         actionListener="#{MovContaControle.consultarPaginadoListenerSomenteApp}">
                                            <i class="fa-icon-mobile-phone"></i> &nbsp Somente contas do Pacto App
                                        </a4j:commandLink>

                                        <a4j:commandLink id="imprimir"
                                                         title="Imprimir Lançamentos"
                                                         style="padding-left:10px; vertical-align: middle; float: right; font-size: 15px;"
                                                         action="#{MovContaControle.imprimir}"
                                                         reRender="form:panelMensagem,form:panelMensagem"
                                                         oncomplete="abrirPopup('../../impressaoLancamentos.jsp', 'ImpressaoLancamentos', 800, 620);">
                                            <i class="fa-icon-print"></i>
                                        </a4j:commandLink>

                                        <%--BOTãO EXCEL--%>
                                        <a4j:commandLink id="exportarExcelTelaLancamentosCons"
                                                         style="margin-left: 8px; vertical-align: middle; float: right; font-size: 15px;"
                                                         actionListener="#{MovContaControle.imprimirRelatorioExcel}"
                                                         rendered="#{not empty MovContaControle.listaConsulta and not MovContaControle.filtrosLancamentos.agruparPorPlanoDeContasCentroCusto}"
                                                         oncomplete="abrirPopup('../../../UpdateServlet?op=downloadfile&file=#{ExportadorListaControle.fileName}&mimetype=application/vnd.ms-excel','Lancamentos', 800,200);#{ExportadorListaControle.msgAlert}"
                                                         accesskey="2" styleClass="botoes">
                                            <f:attribute name="tipo" value="xls"/>
                                            <f:attribute name="atributos"
                                                         value="#{MovContaControle.atributos}"/>
                                            <f:attribute name="prefixo" value="lancamentos-financeiros"/>
                                            <i class="fa-icon-file-excel-o"></i>
                                        </a4j:commandLink>

                                        <%--BOTÃO EXCEL COM PLANO DE CONTAS E CENTRO DE CUSTOS--%>
                                        <a4j:commandLink id="exportarExcelComPlanoCentro"
                                                         style="margin-left: 8px; vertical-align: middle; float: right; font-size: 15px;"
                                                         actionListener="#{MovContaControle.imprimirRelatorioExcel}"
                                                         rendered="#{not empty MovContaControle.listaConsulta and MovContaControle.filtrosLancamentos.agruparPorPlanoDeContasCentroCusto}"
                                                         oncomplete="abrirPopup('../../../UpdateServlet?op=downloadfile&file=#{ExportadorListaControle.fileName}&mimetype=application/vnd.ms-excel','Lancamentos', 800,200);#{ExportadorListaControle.msgAlert}"
                                                         accesskey="2" styleClass="botoes">
                                            <f:attribute name="tipo" value="xls"/>
                                            <f:attribute name="atributos"
                                                         value="#{MovContaControle.atributosComPlanoContas}"/>
                                            <f:attribute name="prefixo" value="lancamentos-financeiros"/>
                                            <i class="fa-icon-file-excel-o"></i>
                                        </a4j:commandLink>

                                        <a4j:commandLink id="btnExportarAlterData"
                                                         action="#{MovContaControle.exportarLancamentosAlterData}"
                                                         oncomplete="location.href='#{MovContaControle.urlUploadArquivo}'"
                                                         rendered="#{MovContaControle.integracaoContabilAlterData && (not empty MovContaControle.listaConsulta)}"
                                                         style="vertical-align: middle; float: right; font-size: 15px;"
                                                         title="Exportar lançamentos quitados para o layout do sistema contábil AlterData."
                                                         reRender="form"
                                                         accesskey="3"
                                                         styleClass="botoes">
                                            <i class="fa-icon-upload"></i>
                                        </a4j:commandLink>
                                    </h:panelGroup>
                                    <h:panelGroup id="panelTotalizadoresTop" layout="block"
                                                  style="width: 100%;text-align: center;">
                                        <h:panelGroup layout="block">
                                            <h:outputText style="font-weight:bold"
                                                          value="Total Registros: "></h:outputText> <h:outputText
                                                id="totalRegistro" styleClass="tituloCamposVerdeGrande"
                                                value="#{MovContaControle.confPaginacao.numeroTotalItens}"/>
                                            <c:if test="${!MovContaControle.filtrosLancamentos.movimentacoesExcluidas}">
                                                <h:outputText style="margin-left:5%; font-weight:bold"
                                                              value="Tot. Recebimento: "></h:outputText><h:outputText
                                                    id="totalRecebimento"
                                                    value="#{MovPagamentoControle.empresaLogado.moeda} #{MovContaControle.valorTotalReceb_Apresentar}"
                                                    styleClass="#{MovContaControle.mudarCorTotalReceb}"/>
                                                <h:outputText style="margin-left:5%; font-weight:bold"
                                                              value="Tot. Pagamento: "></h:outputText><h:outputText
                                                    id="totalPagamento"
                                                    value="#{MovPagamentoControle.empresaLogado.moeda} #{MovContaControle.valorTotalPag_Apresentar}"
                                                    styleClass="#{MovContaControle.mudarCorTotalPag}"/>
                                                <h:outputText style="margin-left:5%; font-weight:bold;"
                                                              value="Saldo: "></h:outputText> <h:outputText
                                                    id="totalSaldo"
                                                    value="#{MovPagamentoControle.empresaLogado.moeda} #{MovContaControle.valorTotal_Apresentar}"
                                                    styleClass="#{MovContaControle.mudarCorTotal}"
                                                    style=" font: 13pt 'Trebuchet MS', verdana, arial, helvetica, sans-serif;"/>
                                            </c:if>
                                        </h:panelGroup>
                                    </h:panelGroup>
                                    <br/>

                                    <h:panelGrid width="100%" columns="2"
                                                 columnClasses="colunaEsquerda w33,w33 centralizado, colunaDireita w33"
                                                 style="margin-top: 20px">
                                        <h:panelGroup layout="block" id="acoesEmMassaFinanPanel" style="display: flex;">
                                            <a4j:commandButton value="Novo Lote de Pagamento"
                                                               id="nvLotePagamentoFinan"
                                                               style="margin: 0; vertical-align: middle;"
                                                               styleClass="botoes nvoBt"
                                                               action="#{MovContaControle.prepararAbrirModalNovoLotePagamento}"
                                                               reRender="modalNovoLotePagamento, formNovoLotePagamento"
                                                               rendered="#{MovContaControle.mostrarPgtoConjunto}"
                                                               oncomplete="#{MovContaControle.mensagemNotificar}#{MovContaControle.abrirFecharModalNovoLotePagamento}"></a4j:commandButton>
                                            <a4j:commandButton value="Pagar em conjunto"
                                                               id="nvPagamentoConjuntoFinan"
                                                               style="margin: 0; vertical-align: middle; margin-left: 10px;"
                                                               styleClass="botoes nvoBt btSec"
                                                               action="#{MovContaControle.selecionarParaPagamentoConjunto}"
                                                               reRender="modalPanelLancarPagamento"
                                                               rendered="#{MovContaControle.mostrarPgtoConjunto}"
                                                               oncomplete="#{MovContaControle.msgAlert} #{MovContaControle.mensagemNotificar}"></a4j:commandButton>

                                            <a4j:commandButton id="btnEstornarEmConjunto"
                                                               value="Estornar em conjunto"
                                                               style="margin: 0 0 0 10px; vertical-align: middle"
                                                               styleClass="botoes nvoBt btSec"
                                                               action="#{MovContaControle.abrirModalConfirmarEstornoConjunto}"
                                                               reRender="modalConfirmaEstornoQuitacaoEmMassa"
                                                               rendered="#{MovContaControle.mostrarPgtoConjunto}"
                                                               oncomplete="#{MovContaControle.msgAlert} #{MovContaControle.mensagemNotificar}"></a4j:commandButton>

                                            <a4j:commandLink
                                                    id="linkModalSelecionados"
                                                    style="padding: 0 10px; color: #29abe2; margin-left: 10px; font-size: 1em; margin-top: 5px;"
                                                    oncomplete="Richfaces.showModalPanel('modalSelecionados')"
                                                    reRender="modalSelecionados"
                                                    action="#{MovContaControle.registrarEventoContasSelecionadas}"
                                                    rendered="#{!MovContaControle.marcouTodasMovContas}">
                                                <h:outputText
                                                        rendered="#{fn:length(MovContaControle.movContasSelecionadas) > 1}"
                                                        value="#{fn:length(MovContaControle.movContasSelecionadas)} selecionados"
                                                        style="vertical-align: middle"/>
                                                <h:outputText
                                                        rendered="#{fn:length(MovContaControle.movContasSelecionadas) == 1}"
                                                        value="1 selecionado" style="vertical-align: middle"/>
                                                <h:outputText
                                                        rendered="#{fn:length(MovContaControle.movContasSelecionadas) > 0}"
                                                        value=" (#{MovContaControle.valorSelecionadoApresentar})"
                                                        style="vertical-align: middle"/>
                                            </a4j:commandLink>

                                            <h:outputText
                                                    style="padding: 0 10px; color: #29abe2; margin-left: 10px; font-size: 1em; vertical-align: middle; margin-top: 5px;"
                                                    rendered="#{MovContaControle.marcouTodasMovContas}"
                                                    value="Todos selecionados"/>
                                        </h:panelGroup>

                                        <h:panelGroup>
                                            <h:outputText value=" "/>
                                        </h:panelGroup>

                                    </h:panelGrid>
                                    <h:panelGrid width="100%" columns="1"
                                                 columnClasses="colunaEsquerda w33,w33 centralizado, colunaDireita w33"
                                                 style="margin-top: 20px">
                                        <h:panelGroup layout="block" style="text-align: center">
                                            <a4j:outputPanel id="painelPaginacaoTop">
                                                <h:panelGroup id="painelPaginacaoTopManual"
                                                              rendered="#{MovContaControle.confPaginacao.paginarBanco}">

                                                    <a4j:commandLink id="pagiInicialTop" styleClass="tituloCampos"
                                                                     value="  <<  "
                                                                     reRender="items, paginaAtualTop,paginaAtual, painelPaginacaoTop, painelPaginacao"
                                                                     rendered="#{MovContaControle.confPaginacao.apresentarPrimeiro}"
                                                                     actionListener="#{MovContaControle.consultarPaginadoListenerSemLimpar}">
                                                        <f:attribute name="pagNavegacao" value="pagInicial"/>
                                                    </a4j:commandLink>
                                                    <a4j:commandLink id="pagiAnteriorTop" styleClass="tituloCampos"
                                                                     value="  <  "
                                                                     reRender="items, paginaAtualTop,paginaAtual, painelPaginacaoTop, painelPaginacao"
                                                                     rendered="#{MovContaControle.confPaginacao.apresentarAnterior}"
                                                                     actionListener="#{MovContaControle.consultarPaginadoListenerSemLimpar}">
                                                        <f:attribute name="pagNavegacao" value="pagAnterior"/>
                                                    </a4j:commandLink>
                                                    <h:outputText id="paginaAtualTop" styleClass="tituloCampos"
                                                                  value="#{msg_aplic.prt_msg_pagina} #{MovContaControle.confPaginacao.paginaAtualDeTodas}"
                                                                  rendered="true"/>
                                                    <a4j:commandLink id="pagiPosteriorTop" styleClass="tituloCampos"
                                                                     value="  >  "
                                                                     reRender="items, paginaAtualTop,paginaAtual, painelPaginacaoTop, painelPaginacao"
                                                                     rendered="#{MovContaControle.confPaginacao.apresentarPosterior}"
                                                                     actionListener="#{MovContaControle.consultarPaginadoListenerSemLimpar}">
                                                        <f:attribute name="pagNavegacao" value="pagPosterior"/>
                                                    </a4j:commandLink>
                                                    <a4j:commandLink id="pagiFinalTop" styleClass="tituloCampos"
                                                                     value="  >>  "
                                                                     reRender="items, paginaAtualTop,paginaAtual, painelPaginacaoTop, painelPaginacao"
                                                                     rendered="#{MovContaControle.confPaginacao.apresentarUltimo}"
                                                                     actionListener="#{MovContaControle.consultarPaginadoListenerSemLimpar}">
                                                        <f:attribute name="pagNavegacao" value="pagFinal"/>
                                                    </a4j:commandLink>

                                                    <h:outputText id="totalItensTop" styleClass="tituloCampos"
                                                                  value=" [#{msg_aplic.prt_msg_itens} #{MovContaControle.confPaginacao.numeroTotalItens}]"
                                                                  rendered="true"/>

                                                </h:panelGroup>

                                            </a4j:outputPanel>
                                        </h:panelGroup>
                                    </h:panelGrid>
                                    <div style="padding-top: 15px"></div>

                                    <rich:dataTable id="items" width="100%" headerClass="consulta"
                                                    rowClasses="linhaPar,linhaImpar "
                                                    reRender="paginaAtual, paginaAtualTop,painelPaginacaoTop,painelPaginacao"
                                                    columnClasses="#{MovContaControle.columnClasses}"
                                                    value="#{MovContaControle.listaConsulta}" rows="100" var="movConta"
                                                    cellpadding="0"
                                                    cellspacing="0">


                                        <rich:column style="border-right-width: 0px;"
                                                     rendered="#{MovContaControle.mostrarPgtoConjunto}">
                                            <f:facet name="header">

                                                <h:selectBooleanCheckbox id="selecionarPgtoConjuntotodos"
                                                                         styleClass="tooltipster"
                                                                         value="#{MovContaControle.marcouTodasMovContas}"
                                                                         title="Selecionar todos pagamentos para criação de lote ou pagamento em conjunto ou estorno em conjunto">
                                                    <a4j:support event="onclick"
                                                                 action="#{MovContaControle.marcarTodosPagamentosConjunto}"
                                                                 reRender="items, pagarEmConjunto"/>
                                                </h:selectBooleanCheckbox>

                                            </f:facet>
                                            <h:selectBooleanCheckbox
                                                    rendered="#{(movConta.contaAPagarNaoQuitada && !movConta.excluido) || (movConta.contaAPagar && movConta.dataQuitacao != null)}"
                                                    disabled="#{movConta.presaEmLoteDePagamento}"
                                                    styleClass="tooltipster"
                                                    id="selecionarPgtoConjunto"
                                                    value="#{movConta.lancamentoSelecionado}"
                                                    title="#{movConta.presaEmLoteDePagamento ? movConta.msgPresaLoteDePagamento_Apresentar : 'Selecionar pagamento para criação de lote ou pagamento em conjunto ou estorno em conjunto'}"
                                                    onmouseover="applyProhibitedCursor(this)"
                                                    onmouseout="resetCursor(this)">
                                                <a4j:support event="onclick" action="#{MovContaControle.checkConta}"
                                                             reRender="pagarEmConjunto, selecionarPgtoConjuntotodos, form:linkModalSelecionados"/>
                                            </h:selectBooleanCheckbox>

                                        </rich:column>

                                        <rich:column id="dataInterna"
                                                     rendered="#{(MovContaControle.filtrosLancamentos.dataInicioUltimaAlteracao != null) && (MovContaControle.filtrosLancamentos.dataFimUltimaAlteracao != null)}">
                                            <f:facet name="header">
                                                <a4j:commandLink actionListener="#{MovContaControle.sortDataList}"
                                                                 reRender="items, paginaAtualTop,paginaAtual, painelPaginacaoTop, painelPaginacao">
                                                    <f:attribute name="sortField" value="movConta.dataUltimaAlteracao"/>
                                                    <h:outputText value="Dt. Interna(IAE)"
                                                                  title="Últimas inclusões, alterações e exclusões."/>
                                                </a4j:commandLink>
                                            </f:facet>
                                            <a4j:commandLink
                                                    rendered="#{(!movConta.tipoRecebivel) && (!MovContaControle.filtrosLancamentos.agruparPorPlanoDeContasCentroCusto) && (!movConta.excluido)}"
                                                    action="#{MovContaControle.preparaEdicao}">
                                                <h:outputText value="#{movConta.dataUltimaAlteracao_Apresentar}">
                                                </h:outputText>
                                            </a4j:commandLink>
                                            <a4j:commandLink
                                                    rendered="#{(movConta.tipoRecebivel) && (!MovContaControle.filtrosLancamentos.agruparPorPlanoDeContasCentroCusto) && (!movConta.excluido)}">
                                                <h:outputText value="#{movConta.dataUltimaAlteracao_Apresentar}">
                                                </h:outputText>
                                            </a4j:commandLink>
                                            <h:outputText
                                                    rendered="#{(MovContaControle.filtrosLancamentos.agruparPorPlanoDeContasCentroCusto) && (!movConta.excluido)}"
                                                    value="#{movConta.dataUltimaAlteracao_Apresentar}"/>
                                            <h:outputText rendered="#{(movConta.excluido)}"
                                                          style="text-decoration: line-through;font-size: 11px;font-family: Arial,Verdana,sans-serif;"
                                                          title="Registro excluído"
                                                          value="#{movConta.dataUltimaAlteracao_Apresentar}"/>
                                        </rich:column>


                                        <rich:column id="codigoMovConta">
                                            <f:facet name="header">
                                                <a4j:commandLink actionListener="#{MovContaControle.sortDataList}"
                                                                 reRender="items, paginaAtualTop,paginaAtual, painelPaginacaoTop, painelPaginacao">
                                                    <f:attribute name="sortField" value="movConta.datavencimento"/>
                                                    <h:outputText
                                                            value="Código"/>
                                                </a4j:commandLink>
                                            </f:facet>
                                            <a4j:commandLink
                                                    rendered="#{(!movConta.tipoRecebivel) && (!MovContaControle.filtrosLancamentos.agruparPorPlanoDeContasCentroCusto) && (!movConta.excluido)}"
                                                    action="#{MovContaControle.preparaEdicao}">
                                                <h:outputText value="#{movConta.codigo}">
                                                </h:outputText>
                                            </a4j:commandLink>
                                            <a4j:commandLink
                                                    rendered="#{(movConta.tipoRecebivel) && (!MovContaControle.filtrosLancamentos.agruparPorPlanoDeContasCentroCusto) && (!movConta.excluido)}">
                                                <h:outputText value="#{movConta.codigo}">
                                                </h:outputText>
                                            </a4j:commandLink>
                                            <h:outputText
                                                    rendered="#{(MovContaControle.filtrosLancamentos.agruparPorPlanoDeContasCentroCusto) && (!movConta.excluido)}"
                                                    value="#{movConta.codigo}"/>
                                            <h:outputText rendered="#{(movConta.excluido)}"
                                                          style="text-decoration: line-through;font-size: 11px;font-family: Arial,Verdana,sans-serif;"
                                                          title="Registro excluído"
                                                          value="#{movConta.codigo}"/>
                                        </rich:column>


                                        <rich:column id="dataVencimento">
                                            <f:facet name="header">
                                                <a4j:commandLink actionListener="#{MovContaControle.sortDataList}"
                                                                 reRender="items, paginaAtualTop,paginaAtual, painelPaginacaoTop, painelPaginacao">
                                                    <f:attribute name="sortField" value="movConta.datavencimento"/>
                                                    <h:outputText
                                                            value="#{msg_aplic.prt_Finan_Lancamentos_dataVencimento}"/>
                                                </a4j:commandLink>
                                            </f:facet>
                                            <a4j:commandLink
                                                    rendered="#{(!movConta.tipoRecebivel) && (!MovContaControle.filtrosLancamentos.agruparPorPlanoDeContasCentroCusto) && (!movConta.excluido)}"
                                                    action="#{MovContaControle.preparaEdicao}">
                                                <h:outputText value="#{movConta.dataVencimento_Apresentar}">
                                                </h:outputText>
                                            </a4j:commandLink>
                                            <a4j:commandLink
                                                    rendered="#{(movConta.tipoRecebivel) && (!MovContaControle.filtrosLancamentos.agruparPorPlanoDeContasCentroCusto) && (!movConta.excluido)}">
                                                <h:outputText value="#{movConta.dataVencimento_Apresentar}">
                                                </h:outputText>
                                            </a4j:commandLink>
                                            <h:outputText
                                                    rendered="#{(MovContaControle.filtrosLancamentos.agruparPorPlanoDeContasCentroCusto) && (!movConta.excluido)}"
                                                    value="#{movConta.dataVencimento_Apresentar}"/>
                                            <h:outputText rendered="#{(movConta.excluido)}"
                                                          style="text-decoration: line-through;font-size: 11px;font-family: Arial,Verdana,sans-serif;"
                                                          title="Registro excluído"
                                                          value="#{movConta.dataVencimento_Apresentar}"/>
                                        </rich:column>

                                        <c:if test="${MovContaControle.filtrosLancamentos.qtdEmpresasSelecionadas > 1}">
                                            <rich:column id="empresa-origem" style="text-align: left;">
                                                <f:facet name="header">
                                                    <a4j:commandLink actionListener="#{MovContaControle.sortDataList}"
                                                                     reRender="items, paginaAtualTop,paginaAtual, painelPaginacaoTop, painelPaginacao">
                                                        <f:attribute name="sortField" value="empresa.nome"/>
                                                        <h:outputText value="#{msg_aplic.prt_Finan_Lancamentos_empresaOrigem}"/>
                                                    </a4j:commandLink>
                                                </f:facet>
                                                <h:panelGroup style="text-align: left;">
                                                    <a4j:commandLink
                                                            rendered="#{(!movConta.tipoRecebivel) && (!MovContaControle.filtrosLancamentos.agruparPorPlanoDeContasCentroCusto) && (!movConta.excluido)}"
                                                            action="#{MovContaControle.preparaEdicao}" id="empresaOrigemInf">
                                                        <h:outputText value="#{movConta.empresaVO.nome}"/>
                                                    </a4j:commandLink>
                                                    <a4j:commandLink
                                                            rendered="#{(movConta.tipoRecebivel) && (!MovContaControle.filtrosLancamentos.agruparPorPlanoDeContasCentroCusto) && (!movConta.excluido)}"
                                                            id="empresaOrigemR">
                                                        <h:outputText style="text-align: left;" value="#{movConta.empresaVO.nome}"/>
                                                    </a4j:commandLink>
                                                </h:panelGroup>
                                                <h:outputText
                                                        rendered="#{(MovContaControle.filtrosLancamentos.agruparPorPlanoDeContasCentroCusto) && (!movConta.excluido)}"
                                                        value="#{movConta.empresaVO.nome}"/>
                                                <h:outputText rendered="#{(movConta.excluido)}"
                                                              style="text-decoration: line-through;font-size: 11px;font-family: Arial,Verdana,sans-serif;"
                                                              title="Registro excluído" value="#{movConta.empresaVO.nome}"/>
                                            </rich:column>
                                        </c:if>

                                        <rich:column id="favorecido">
                                            <f:facet name="header">
                                                <a4j:commandLink actionListener="#{MovContaControle.sortDataList}"
                                                                 reRender="items, paginaAtualTop,paginaAtual, painelPaginacaoTop, painelPaginacao">
                                                    <f:attribute name="sortField" value="pessoa.nome"/>
                                                    <h:outputText
                                                            value="#{msg_aplic.prt_Finan_Lancamentos_favorecido}"/>
                                                </a4j:commandLink>
                                            </f:facet>
                                            <h:panelGroup>
                                                <a4j:commandLink
                                                        rendered="#{(!movConta.tipoRecebivel) && (!MovContaControle.filtrosLancamentos.agruparPorPlanoDeContasCentroCusto) && (!movConta.excluido)}"
                                                        action="#{MovContaControle.preparaEdicao}" id="pessoa">
                                                    <h:outputText value="#{movConta.pessoaVO.nome}"/>
                                                </a4j:commandLink>
                                                <a4j:commandLink
                                                        rendered="#{(movConta.tipoRecebivel) && (!MovContaControle.filtrosLancamentos.agruparPorPlanoDeContasCentroCusto) && (!movConta.excluido)}"
                                                        id="pessoaR">
                                                    <h:outputText value="#{movConta.pessoaVO.nome}"/>
                                                </a4j:commandLink>
                                            </h:panelGroup>
                                            <h:outputText
                                                    rendered="#{(MovContaControle.filtrosLancamentos.agruparPorPlanoDeContasCentroCusto) && (!movConta.excluido)}"
                                                    value="#{movConta.pessoaVO.nome}"/>
                                            <h:outputText rendered="#{(movConta.excluido)}"
                                                          style="text-decoration: line-through;font-size: 11px;font-family: Arial,Verdana,sans-serif;"
                                                          title="Registro excluído" value="#{movConta.pessoaVO.nome}"/>
                                        </rich:column>

                                        <rich:column id="valorPago" rendered="#{MovContaControle.apresentarValorPago}">
                                            <f:facet name="header">
                                                <a4j:commandLink actionListener="#{MovContaControle.sortDataList}"
                                                                 reRender="items, paginaAtualTop,paginaAtual, painelPaginacaoTop, painelPaginacao">
                                                    <f:attribute name="sortField" value="movConta.valorPago"/>
                                                    <h:outputText
                                                            value="#{MovPagamentoControle.empresaLogado.moeda} Valor pago"/>
                                                </a4j:commandLink>
                                            </f:facet>
                                            <a4j:commandLink>
                                                <h:outputText id="textValorpago"
                                                              styleClass="#{movConta.mudarCorLinkValor}"
                                                              value="#{movConta.valorPago_Apresentar}"
                                                              title="Valor informado na quitação da conta"/>
                                            </a4j:commandLink>
                                        </rich:column>

                                        <rich:column id="valor">
                                            <f:facet name="header">
                                                <a4j:commandLink actionListener="#{MovContaControle.sortDataList}"
                                                                 reRender="items, paginaAtualTop,paginaAtual, painelPaginacaoTop, painelPaginacao">
                                                    <f:attribute name="sortField" value="movConta.valor"/>
                                                    <h:outputText
                                                            value="#{MovPagamentoControle.empresaLogado.moeda} Valor previsto"
                                                            rendered="#{MovContaControle.apresentarValorPago}"/>
                                                    <h:outputText value="#{MovPagamentoControle.empresaLogado.moeda}"
                                                                  rendered="#{!MovContaControle.apresentarValorPago}"/>
                                                </a4j:commandLink>
                                            </f:facet>
                                            <a4j:commandLink
                                                    rendered="#{(!movConta.tipoRecebivel)  && (!MovContaControle.filtrosLancamentos.agruparPorPlanoDeContasCentroCusto) && (!movConta.excluido)}"
                                                    action="#{MovContaControle.preparaEdicao}">
                                                <h:outputText value="#{movConta.valor_Apresentar}"
                                                              styleClass="#{movConta.mudarCorLinkValor}"
                                                              rendered="#{!MovContaControle.apresentarValorPago}"/>
                                                <h:outputText value="#{movConta.valor_ApresentarOriginalAlterado}"
                                                              styleClass="#{movConta.mudarCorLinkValor}"
                                                              rendered="#{MovContaControle.apresentarValorPago}"/>
                                            </a4j:commandLink>
                                            <a4j:commandLink
                                                    rendered="#{(movConta.tipoRecebivel)  && (!MovContaControle.filtrosLancamentos.agruparPorPlanoDeContasCentroCusto) && (!movConta.excluido)}"
                                                    id="valorR">
                                                <h:outputText value="#{movConta.valor_Apresentar}"
                                                              styleClass="#{movConta.mudarCorLinkValor}"/>
                                            </a4j:commandLink>

                                            <h:panelGroup rendered="#{(MovContaControle.filtrosLancamentos.agruparPorPlanoDeContasCentroCusto) && (!movConta.excluido)}">
                                                <h:outputText
                                                        value="#{movConta.valor_Apresentar}"
                                                        rendered="#{(!MovContaControle.apresentarValorPago)}"/>
                                                <h:outputText
                                                        value="#{movConta.valor_ApresentarOriginalAlterado}"
                                                        rendered="#{(MovContaControle.apresentarValorPago)}"/>
                                            </h:panelGroup>

                                            <h:outputText rendered="#{(movConta.excluido)}"
                                                          style="text-decoration: line-through;font-size: 11px;font-family: Arial,Verdana,sans-serif;"
                                                          title="Registro excluído"
                                                          value="#{movConta.valor_Apresentar}"/>
                                        </rich:column>

                                        <rich:column id="descricao">
                                            <f:facet name="header">
                                                <a4j:commandLink actionListener="#{MovContaControle.sortDataList}"
                                                                 reRender="items, paginaAtualTop,paginaAtual, painelPaginacaoTop, painelPaginacao">
                                                    <f:attribute name="sortField" value="movConta.descricao"/>
                                                    <h:outputText value="#{msg_aplic.prt_Finan_Lancamentos_descricao}"/>
                                                </a4j:commandLink>
                                            </f:facet>
                                            <h:panelGroup>
                                                <a4j:commandLink
                                                        rendered="#{(!movConta.tipoRecebivel) && (!MovContaControle.filtrosLancamentos.agruparPorPlanoDeContasCentroCusto) && (!movConta.excluido)}"
                                                        action="#{MovContaControle.preparaEdicao}" id="descricaoInf">
                                                    <h:outputText value="#{movConta.descricao}"/>
                                                </a4j:commandLink>
                                                <a4j:commandLink
                                                        rendered="#{(movConta.tipoRecebivel) && (!MovContaControle.filtrosLancamentos.agruparPorPlanoDeContasCentroCusto) && (!movConta.excluido)}"
                                                        id="descricaoR">
                                                    <h:outputText value="#{movConta.descricao}"/>
                                                </a4j:commandLink>
                                            </h:panelGroup>
                                            <h:outputText
                                                    rendered="#{(MovContaControle.filtrosLancamentos.agruparPorPlanoDeContasCentroCusto) && (!movConta.excluido)}"
                                                    value="#{movConta.descricao}"/>
                                            <h:outputText rendered="#{(movConta.excluido)}"
                                                          style="text-decoration: line-through;font-size: 11px;font-family: Arial,Verdana,sans-serif;"
                                                          title="Registro excluído" value="#{movConta.descricao}"/>
                                        </rich:column>

                                        <rich:column id="dataQuitacao">
                                            <f:facet name="header">
                                                <a4j:commandLink actionListener="#{MovContaControle.sortDataList}"
                                                                 reRender="items, paginaAtualTop,paginaAtual, painelPaginacaoTop, painelPaginacao">
                                                    <f:attribute name="sortField" value="movConta.dataquitacao"/>
                                                    <h:outputText
                                                            value="#{msg_aplic.prt_Finan_Lancamentos_dataQuitacao}"/>
                                                </a4j:commandLink>
                                            </f:facet>
                                            <a4j:commandLink
                                                    rendered="#{(!movConta.tipoRecebivel) && (!MovContaControle.filtrosLancamentos.agruparPorPlanoDeContasCentroCusto) && (!movConta.excluido)}"
                                                    action="#{MovContaControle.preparaEdicao}">
                                                <h:outputText value="#{movConta.dataQuitacao_Apresentar}">
                                                </h:outputText>
                                            </a4j:commandLink>
                                            <a4j:commandLink
                                                    rendered="#{(movConta.tipoRecebivel) && (!MovContaControle.filtrosLancamentos.agruparPorPlanoDeContasCentroCusto) && (!movConta.excluido)}"
                                                    id="dataQuitacaoR">
                                                <h:outputText value="#{movConta.dataQuitacao_Apresentar}">
                                                </h:outputText>
                                            </a4j:commandLink>
                                            <h:outputText
                                                    rendered="#{(MovContaControle.filtrosLancamentos.agruparPorPlanoDeContasCentroCusto) && (!movConta.excluido)}"
                                                    value="#{movConta.dataQuitacao_Apresentar}"/>
                                            <h:outputText rendered="#{(movConta.excluido)}"
                                                          style="text-decoration: line-through;font-size: 11px;font-family: Arial,Verdana,sans-serif;"
                                                          title="Registro excluído"
                                                          value="#{movConta.dataQuitacao_Apresentar}"/>
                                        </rich:column>
                                        <rich:column id="descricaoConta"
                                                     rendered="#{MovContaControle.usarMovimentacao}">
                                            <f:facet name="header">
                                                <a4j:commandLink actionListener="#{MovContaControle.sortDataList}"
                                                                 reRender="items, paginaAtualTop,paginaAtual, painelPaginacaoTop, painelPaginacao">
                                                    <f:attribute name="sortField" value="conta.descricao"/>
                                                    <h:outputText
                                                            value="#{msg_aplic.prt_Finan_Lancamento_contaSemPonto}"/>
                                                </a4j:commandLink>
                                            </f:facet>
                                            <h:panelGroup>
                                                <a4j:commandLink
                                                        rendered="#{(!movConta.tipoRecebivel) && (!MovContaControle.filtrosLancamentos.agruparPorPlanoDeContasCentroCusto) && (!movConta.excluido)}"
                                                        action="#{MovContaControle.preparaEdicao}" id="conta">
                                                    <h:outputText value="#{movConta.contaVO.descricao}"/>
                                                </a4j:commandLink>
                                                <a4j:commandLink
                                                        rendered="#{(movConta.tipoRecebivel) && (!MovContaControle.filtrosLancamentos.agruparPorPlanoDeContasCentroCusto) && (!movConta.excluido)}"
                                                        id="contaR">
                                                    <h:outputText value="#{movConta.contaVO.descricao}"/>
                                                </a4j:commandLink>
                                            </h:panelGroup>
                                            <h:outputText
                                                    rendered="#{(MovContaControle.filtrosLancamentos.agruparPorPlanoDeContasCentroCusto) && (!movConta.excluido)}"
                                                    value="#{movConta.contaVO.descricao}"/>
                                            <h:outputText rendered="#{(movConta.excluido)}"
                                                          style="text-decoration: line-through;font-size: 11px;font-family: Arial,Verdana,sans-serif;"
                                                          title="Registro excluído"
                                                          value="#{movConta.contaVO.descricao}"/>
                                        </rich:column>
                                        <rich:column id="codigoNotaEmitida">
                                            <f:facet name="header">
                                                <h:outputText styleClass="tooltipster" value="ID Lote"
                                                              title="Codigo do lote da nota fiscal já emitida"/>
                                            </f:facet>
                                            <h:panelGroup>
                                                <h:outputText value="#{movConta.codigoNotaEmitidaMostrar}"/>
                                            </h:panelGroup>
                                        </rich:column>

                                        <rich:column id="agruparPlanoConta"
                                                     rendered="#{MovContaControle.filtrosLancamentos.agruparPorPlanoDeContasCentroCusto}">
                                            <f:facet name="header">
                                                <h:outputText value="Plano de Contas"/>
                                            </f:facet>
                                            <h:outputText rendered="#{(!movConta.excluido)}"
                                                          value="#{movConta.nomePlanoConta}"/>
                                            <h:outputText rendered="#{(movConta.excluido)}"
                                                          style="text-decoration: line-through;font-size: 11px;font-family: Arial,Verdana,sans-serif;"
                                                          title="Registro excluído" value="#{movConta.nomePlanoConta}"/>
                                        </rich:column>
                                        <rich:column id="centroCusto"
                                                     rendered="#{MovContaControle.filtrosLancamentos.agruparPorPlanoDeContasCentroCusto}">
                                            <f:facet name="header">
                                                <a4j:commandLink actionListener="#{MovContaControle.sortDataList}"
                                                                 reRender="items, paginaAtualTop,paginaAtual, painelPaginacaoTop, painelPaginacao">
                                                    <f:attribute name="sortField" value="nomeCentroCusto"/>
                                                    <h:outputText value="Centro de Custos"/>
                                                </a4j:commandLink>
                                            </f:facet>
                                            <h:outputText rendered="#{(!movConta.excluido)}"
                                                          value="#{movConta.nomeCentroCusto}"/>
                                            <h:outputText rendered="#{(movConta.excluido)}"
                                                          style="text-decoration: line-through;font-size: 11px;font-family: Arial,Verdana,sans-serif;"
                                                          title="Registro excluído"
                                                          value="#{movConta.nomeCentroCusto}"/>
                                        </rich:column>

                                        <rich:column id="tipoOperacaoLancamento_ApresentarResumido">
                                            <f:facet name="header">
                                                <h:outputText value="#{msg_aplic.prt_Finan_Lancamentos_tipo}"/>
                                            </f:facet>


                                            <a4j:commandLink
                                                    rendered="#{(!movConta.tipoRecebivel) && (!MovContaControle.filtrosLancamentos.agruparPorPlanoDeContasCentroCusto) && (!movConta.excluido)}"
                                                    action="#{MovContaControle.preparaEdicao}" id="tipoOperacao">
                                                <h:outputText styleClass="tooltipster"
                                                              value="#{movConta.tipoOperacaoLancamento_ApresentarResumido}"
                                                              title="#{movConta.tipoOperacaoLancamento_Apresentar}">
                                                </h:outputText>
                                            </a4j:commandLink>
                                            <a4j:commandLink
                                                    rendered="#{(movConta.tipoRecebivel) && (!MovContaControle.filtrosLancamentos.agruparPorPlanoDeContasCentroCusto) && (!movConta.excluido)}"
                                                    id="tipoOperacaoR">
                                                <h:outputText
                                                        value="#{movConta.tipoOperacaoLancamento_ApresentarResumido}"></h:outputText>
                                            </a4j:commandLink>
                                            <h:outputText value="#{movConta.tipoOperacaoLancamento_ApresentarResumido}"
                                                          rendered="#{(MovContaControle.filtrosLancamentos.agruparPorPlanoDeContasCentroCusto) && (!movConta.excluido)}"></h:outputText>
                                            <h:outputText value="#{movConta.tipoOperacaoLancamento_ApresentarResumido}"
                                                          style="text-decoration: line-through;font-size: 11px;font-family: Arial,Verdana,sans-serif;"
                                                          title="Registro excluído"
                                                          rendered="#{(movConta.excluido)}"></h:outputText>
                                            <a4j:commandLink rendered="#{(movConta.app) && (!movConta.excluido)}"
                                                             action="#{MovContaControle.preparaEdicao}" id="appid"
                                                             style="margin-left: 5px;">
                                                <i class="fa-icon-mobile-phone"></i>
                                            </a4j:commandLink>

                                        </rich:column>


                                        <rich:column
                                                rendered="#{(!MovContaControle.filtrosLancamentos.agruparPorPlanoDeContasCentroCusto)}">
                                            <f:facet name="header">
                                                <h:outputText value="#{msg_aplic.prt_Finan_Lancamentos_operacoes}"/>
                                            </f:facet>
                                            <a4j:commandButton
                                                    rendered="#{(!movConta.tipoRecebivel) && (!movConta.excluido)}"
                                                    id="editarPagamento"
                                                    action="#{MovContaControle.preparaEdicao}"
                                                    value="#{msg_bt.btn_editar}"
                                                    title="#{msg_bt.btn_editar}"
                                                    image="/imagens/botaoEditar.png"
                                                    alt="#{msg.msg_editar_dados}" styleClass="botoes tooltipster"/>
                                            <a4j:commandButton
                                                    rendered="#{(movConta.tipoRecebivel) && (!movConta.excluido)}"
                                                    id="edicao" value="#{msg_bt.btn_editar}"
                                                    image="/imagens/botaoEditar.png"
                                                    alt="#{msg.msg_editar_dados}" styleClass="botoes tooltipster"/>
                                            <a4j:commandButton id="excluirPagamento"
                                                               rendered="#{movConta.excluir_Apresentar}"
                                                               action="#{MovContaControle.preparaExclusaoTelaLancamentos}"
                                                               value="#{msg_bt.btn_excluir}"
                                                               title="#{msg.msg_excluir_dados}"
                                                               image="/imagens/botaoRemover.png"
                                                               alt="#{msg.msg_excluir_dados}"
                                                               reRender="panelAutorizacaoFuncionalidade"
                                                               styleClass="botaoExcluir tooltipster">
                                                <f:setPropertyActionListener value="#{movConta}"
                                                                             target="#{MovContaControle.movContaVO}"></f:setPropertyActionListener>
                                                <a4j:support event="oncomplete"
                                                             action="#{AutorizacaoFuncionalidadeControle.invoke}"
                                                             reRender="#{AutorizacaoFuncionalidadeControle.renderComponents}, modalPanelLancarPagamento,panelAutorizacaoFuncionalidade"
                                                             oncomplete="#{AutorizacaoFuncionalidadeControle.onComplete}"/>
                                            </a4j:commandButton>

                                            <%--   <a4j:commandLink value="#{movConta.tituloLinkQuitar}"
                                                                rendered="#{movConta.pagar_Apresentar && !movConta.tipoRecebivel}"
                                                                reRender="modalPanelLancarPagamento"
                                                                action="#{MovContaControle.selecionarLancamentoQuitacao}"
                                                                oncomplete="#{MovContaControle.abrirFecharModalPagto}"/>   --%>

                                            <a4j:commandLink id="contaPagarTitulo"
                                                             value="#{movConta.tituloLinkQuitar}"
                                                             title="#{movConta.tituloLinkQuitar}"
                                                             rendered="#{(movConta.pagar_Apresentar) && (!movConta.tipoRecebivel)
                                                             && (!MovContaControle.filtrosLancamentos.agruparPorPlanoDeContasCentroCusto) && (!movConta.excluido) &&
                                                                              !MovContaControle.moduloOpenBankAtivado}"
                                                             styleClass="tooltipster"
                                                             reRender="modalPanelLancarPagamento,panelAutorizacaoFuncionalidade"
                                                             action="#{MovContaControle.selecionarLancamentoQuitacao}"
                                                             oncomplete="#{MovContaControle.mensagemNotificar}#{MovContaControle.onCompleteGravar}"/>


                                            <a4j:commandButton id="gerarNotaFiscalTelaLancamentosCons"
                                                               reRender="mdlDataEmissao, mdlMensagemGenerica"
                                                               value="NFSe" title="Enviar Nota Fiscal"
                                                               image="../../images/bot-nfse-20.png"
                                                               action="#{MovContaControle.confirmarEmitirNFSE}"
                                                               rendered="#{(MovContaControle.apresentarBotoesNFSe && !movConta.nfseEmitida) && (!MovContaControle.filtrosLancamentos.agruparPorPlanoDeContasCentroCusto) && (!movConta.excluido)}"
                                                               oncomplete="#{MovContaControle.msgAlert} ">
                                                <f:setPropertyActionListener value="#{movConta}"
                                                                             target="#{MovContaControle.movContaVO}"></f:setPropertyActionListener>
                                            </a4j:commandButton>
                                            <a4j:commandButton id="gerarNotaFiscalEnviada"
                                                               reRender="panelAutorizacaoFuncionalidade, mdlMensagemGenerica"
                                                               value="NFSe" title="Reenviar Nota Fiscal"
                                                               image="../../images/btn_cupomEmitido.png"
                                                               action="#{MovContaControle.confirmarEmitirNFSE}"
                                                               rendered="#{(MovContaControle.apresentarBotoesNFSe && movConta.nfseEmitida) && (!MovContaControle.filtrosLancamentos.agruparPorPlanoDeContasCentroCusto) && (!movConta.excluido)}"
                                                               oncomplete="#{MovContaControle.msgAlert}">

                                                <f:setPropertyActionListener value="#{movConta}"
                                                                             target="#{MovContaControle.movContaVO}"></f:setPropertyActionListener>
                                            </a4j:commandButton>


                                            <a4j:commandButton id="gerarNFCe"
                                                               reRender="panelAutorizacaoFuncionalidade, mdlMensagemGenerica"
                                                               value="NFC-e" title="Enviar NFC-e"
                                                               action="#{MovContaControle.confirmarEmitirNFCE}"
                                                               rendered="#{(MovContaControle.apresentarBotoesNFCe && !movConta.nfceEmitida) && (!MovContaControle.filtrosLancamentos.agruparPorPlanoDeContasCentroCusto) && (!movConta.excluido)}"
                                                               oncomplete="#{MovContaControle.msgAlert} ">
                                                <f:setPropertyActionListener value="#{movConta}"
                                                                             target="#{MovContaControle.movContaVO}"></f:setPropertyActionListener>
                                            </a4j:commandButton>
                                            <a4j:commandButton id="gerarNFCeEnviada"
                                                               reRender="panelAutorizacaoFuncionalidade, mdlMensagemGenerica"
                                                               value="NFC-e" title="Reenviar NFC-e"
                                                               image="../../images/btn_cupomEmitido.png"
                                                               action="#{MovContaControle.confirmarEmitirNFCE}"
                                                               rendered="#{(MovContaControle.apresentarBotoesNFCe && movConta.nfceEmitida) && (!MovContaControle.filtrosLancamentos.agruparPorPlanoDeContasCentroCusto) && (!movConta.excluido)}"
                                                               oncomplete="#{MovContaControle.msgAlert}">

                                                <f:setPropertyActionListener value="#{movConta}"
                                                                             target="#{MovContaControle.movContaVO}"></f:setPropertyActionListener>
                                            </a4j:commandButton>
                                            <a4j:commandButton action="#{MovContaControle.consultarLogExclusaoMovConta}"
                                                               reRender="form"
                                                               rendered="#{movConta.excluido}"
                                                               oncomplete="#{MovContaControle.oncompleteLog}"
                                                               style="margin-left:5%;"
                                                               image="/imagens/botalVisualizarLog.png"
                                                               title="Visualizar Log"
                                                               styleClass="botoes"/>

                                        </rich:column>
                                    </rich:dataTable>
                                    <table width="100%" border="0" cellspacing="0" cellpadding="0">
                                        <tr>
                                            <td align="center" valign="middle">

                                                <a4j:outputPanel id="painelPaginacao">
                                                    <h:panelGroup id="painelPaginacaoManual"
                                                                  rendered="#{MovContaControle.confPaginacao.paginarBanco}">

                                                        <a4j:commandLink id="pagiInicial" styleClass="tituloCampos"
                                                                         value="  <<  "
                                                                         reRender="items, paginaAtual,paginaAtualTop,painelPaginacaoTop, painelPaginacao"
                                                                         rendered="#{MovContaControle.confPaginacao.apresentarPrimeiro}"
                                                                         actionListener="#{MovContaControle.consultarPaginadoListenerSemLimpar}">
                                                            <f:attribute name="pagNavegacao" value="pagInicial"/>
                                                        </a4j:commandLink>
                                                        <a4j:commandLink id="pagiAnterior" styleClass="tituloCampos"
                                                                         value="  <  "
                                                                         reRender="items, paginaAtual,paginaAtualTop,painelPaginacaoTop, painelPaginacao"
                                                                         rendered="#{MovContaControle.confPaginacao.apresentarAnterior}"
                                                                         actionListener="#{MovContaControle.consultarPaginadoListenerSemLimpar}">
                                                            <f:attribute name="pagNavegacao" value="pagAnterior"/>
                                                        </a4j:commandLink>
                                                        <h:outputText id="paginaAtual" styleClass="tituloCampos"
                                                                      value="#{msg_aplic.prt_msg_pagina} #{MovContaControle.confPaginacao.paginaAtualDeTodas}"
                                                                      rendered="true"/>
                                                        <a4j:commandLink id="pagiPosterior" styleClass="tituloCampos"
                                                                         value="  >  "
                                                                         reRender="items, paginaAtual,paginaAtualTop,painelPaginacaoTop, painelPaginacao"
                                                                         rendered="#{MovContaControle.confPaginacao.apresentarPosterior}"
                                                                         actionListener="#{MovContaControle.consultarPaginadoListenerSemLimpar}">
                                                            <f:attribute name="pagNavegacao" value="pagPosterior"/>
                                                        </a4j:commandLink>
                                                        <a4j:commandLink id="pagiFinal" styleClass="tituloCampos"
                                                                         value="  >>  "
                                                                         reRender="items, paginaAtual,paginaAtualTop,painelPaginacaoTop, painelPaginacao"
                                                                         rendered="#{MovContaControle.confPaginacao.apresentarUltimo}"
                                                                         actionListener="#{MovContaControle.consultarPaginadoListenerSemLimpar}">
                                                            <f:attribute name="pagNavegacao" value="pagFinal"/>
                                                        </a4j:commandLink>

                                                        <h:outputText id="totalItens" styleClass="tituloCampos"
                                                                      value=" [#{msg_aplic.prt_msg_itens} #{MovContaControle.confPaginacao.numeroTotalItens}]"
                                                                      rendered="true"/>

                                                    </h:panelGroup>

                                                    <h:panelGroup id="painelPaginacaoScroller"
                                                                  rendered="#{!MovContaControle.confPaginacao.paginarBanco}">
                                                        <rich:datascroller align="center" for="form:items"
                                                                           maxPages="100" id="scitems"
                                                                           reRender="items, paginaAtual,paginaAtualTop,painelPaginacaoTop, painelPaginacao"/>
                                                    </h:panelGroup>
                                                </a4j:outputPanel>

                                            </td>
                                        </tr>
                                    </table>
                                    <h:panelGroup id="panelTotalizadoresBottom" layout="block"
                                                  style="width: 100%;text-align: center;margin-top: 20px;">
                                        <h:panelGroup id="panelTotalizadoresButton">
                                            <h:outputText style="font-weight:bold" value="Total Registros: "/>
                                            <h:outputText styleClass="tituloCamposVerdeGrande"
                                                          value="#{MovContaControle.confPaginacao.numeroTotalItens}"/>
                                            <c:if test="${!MovContaControle.filtrosLancamentos.movimentacoesExcluidas}">
                                                <h:outputText style="margin-left:5%; font-weight:bold"
                                                              value="Tot. Recebimento: "/><h:outputText
                                                    value="#{MovContaControle.valorTotalReceb_Apresentar}"
                                                    styleClass="#{MovContaControle.mudarCorTotalReceb}"/>
                                                <h:outputText style="margin-left:5%; font-weight:bold"
                                                              value="Tot. Pagamento: "/><h:outputText
                                                    value="#{MovContaControle.valorTotalPag_Apresentar}"
                                                    styleClass="#{MovContaControle.mudarCorTotalPag}"/>
                                                <h:outputText style="margin-left:5%; font-weight:bold" value="Saldo: "/>
                                                <h:outputText value="#{MovContaControle.valorTotal_Apresentar}"
                                                              styleClass="#{MovContaControle.mudarCorTotal}"
                                                              style=" font: 13pt 'Trebuchet MS', verdana, arial, helvetica, sans-serif;"/>
                                                <a4j:commandButton
                                                        action="#{MovContaControle.realizarConsultaLogContasPagar}"
                                                        reRender="form"
                                                        oncomplete="#{MovContaControle.oncompleteLog}"
                                                        style="margin-left:5%;"
                                                        image="/imagens/botalVisualizarLog.png" alt="Visualizar LOG"
                                                        title="Visualizar Log"
                                                        styleClass="botoes"/>
                                            </c:if>
                                        </h:panelGroup>
                                    </h:panelGroup>
                                    <h:panelGrid id="panelMensagem" columns="1" width="100%" styleClass="tabMensagens"
                                                 style="margin-top: 20px;">
                                        <h:panelGrid columns="3" width="100%" styleClass="tabMensagens">
                                            <h:panelGrid columns="1" width="100%">

                                                <h:outputText value=" "/>

                                            </h:panelGrid>
                                            <h:commandButton rendered="#{MovContaControle.sucesso}"
                                                             image="/imagens/sucesso.png"/>
                                            <h:commandButton rendered="#{MovContaControle.erro}"
                                                             image="/imagens/erro.png"/>
                                            <h:panelGrid columns="1" width="100%">
                                                <h:outputText styleClass="mensagem"
                                                              value="#{MovContaControle.mensagem}"/>
                                                <h:outputText styleClass="mensagemDetalhada"
                                                              value="#{MovContaControle.mensagemDetalhada}"/>
                                            </h:panelGrid>
                                        </h:panelGrid>
                                    </h:panelGrid>
                                </h:panelGroup>

                                <jsp:include page="telaLancamentosCons_Conciliacao.jsp" flush="true"/>
                                <jsp:include page="telaLancamentosCons_Lotes.jsp" flush="true"/>

                            </h:panelGroup>

                        </h:panelGroup>
                        <jsp:include page="includes/include_box_menulateral.jsp" flush="true"/>
                    </h:panelGroup>
                </h:panelGroup>
            </h:panelGroup>

            <jsp:include page="../../include_rodape_flat.jsp" flush="true"/>
        </h:panelGroup>
    </h:form>

    <rich:modalPanel id="modalSelecionados" autosized="true" shadowOpacity="true" width="600"
                     top="100"
                     styleClass="novaModal">
        <f:facet name="header">
            <h:panelGroup>
                <h:outputText value="Lançamentos selecionados"/>
            </h:panelGroup>
        </f:facet>

        <f:facet name="controls">
            <h:panelGroup>
                <h:outputText
                        styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign btnSelecionarModalidade"
                        id="hidelinkSelecionados"/>
                <rich:componentControl for="modalSelecionados" attachTo="hidelinkSelecionados" operation="hide"
                                       event="onclick"/>
            </h:panelGroup>
        </f:facet>

        <h:form id="formModalLanSelecionados" style="min-height: 200px">
            <div style="overflow: auto; max-height: 600px">
                <rich:dataTable id="itemsselecionados" width="100%" styleClass="tabelaDados dataTable"
                                rowClasses="linhaPar,linhaImpar "
                                columnClasses="esquerda, direita, centralizado"
                                value="#{MovContaControle.movContasSelecionadas}" rows="100" var="mcselecionado"
                                cellpadding="0"
                                cellspacing="0">

                    <rich:column>
                        <f:facet name="header">
                            <h:outputText value="#{msg_aplic.prt_Finan_Lancamentos_descricao}"/>
                        </f:facet>
                        <h:outputText value="#{mcselecionado.descricao}"/>
                    </rich:column>

                    <rich:column id="valor">
                        <f:facet name="header">
                            <h:outputText value="#{MovPagamentoControle.empresaLogado.moeda}"/>
                        </f:facet>
                        <h:outputText value="#{mcselecionado.valor_Apresentar}">
                        </h:outputText>
                    </rich:column>

                    <rich:column>
                        <f:facet name="header">
                            <h:outputText value="#{msg_aplic.prt_Finan_Lancamentos_operacoes}"/>
                        </f:facet>

                        <a4j:commandLink reRender="form:items,form:pagarEmConjunto, itemsselecionados"
                                         action="#{MovContaControle.removerSelecionado}">
                            <i class="fa-icon-remove"></i>
                        </a4j:commandLink>

                    </rich:column>
                </rich:dataTable>
            </div>

            <div style="text-align: center">
                <a4j:commandButton value="Limpar selecionados" style="margin: 0; vertical-align: middle"
                                   styleClass="botoes nvoBt btSec"
                                   action="#{MovContaControle.limparSelecionados}"
                                   reRender="form:items,form:pagarEmConjunto, itemsselecionados"
                                   oncomplete="Richfaces.hideModalPanel('modalSelecionados')"></a4j:commandButton>
            </div>
        </h:form>
    </rich:modalPanel>

    <rich:modalPanel id="mdlDataEmissao" styleClass="novaModal" width="450" autosized="true" shadowOpacity="true"
                     showWhenRendered="#{MovContaControle.abrirFecharModalDataEmissao}"
                     onhide="Richfaces.hideModalPanel('mdlDataEmissao');">
        <f:facet name="header">
            <h:outputText value="Emissão NFSe - Atribuir Data de Emissão"/>
        </f:facet>

        <f:facet name="controls">
            <h:panelGroup>
                <h:outputText
                        styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign"
                        id="hidelinkDataEmissao"/>
                <rich:componentControl for="mdlDataEmissao" attachTo="hidelinkDataEmissao"
                                       operation="hide" event="onclick"/>
            </h:panelGroup>
        </f:facet>

        <h:form id="formMdlDataEmissao">
            <h:panelGroup styleClass="margin-box">
                <h:panelGroup layout="block">
                    <h:outputText value="Data de Emissão: "/>
                    <rich:calendar id="dataEmissao"
                                   inputSize="10"
                                   showWeekDaysBar="false"
                                   inputClass="formMdlDataEmissao"
                                   showWeeksBar="false"
                                   oninputfocus="focusinput(this);"
                                   oninputblur="blurinput(this);"
                                   enableManualInput="true"
                                   oninputchange="return validar_Data(this.id);"
                                   oncomplete="return validar_Data(this.id);"
                                   datePattern="dd/MM/yyyy"
                                   value="#{MovContaControle.dataEmissaoNota}" />
                </h:panelGroup>
                <h:panelGroup layout="block" styleClass="container-botoes">
                    <a4j:commandLink oncomplete="Richfaces.hideModalPanel('mdlDataEmissao');"
                                     id="confirmarDataEmissao"
                                     styleClass="botaoPrimario texto-size-16-real"
                                     action="#{MovContaControle.atribuirDataEmissao}"
                                     value="Confirmar"
                                     reRender="mdlDataEmissao, panelAutorizacaoFuncionalidade, mdlMensagemGenerica" />
                    <rich:spacer width="30px;"/>
                    <a4j:commandLink oncomplete="Richfaces.hideModalPanel('mdlDataEmissao');"
                                     id="fecharDataEmissao"
                                     styleClass="botaoSecundario texto-size-16-real"
                                     action="#{MovContaControle.fecharModalDataEmissao}"
                                     value="Fechar"
                                     reRender="mdlDataEmissao" />
                </h:panelGroup>
            </h:panelGroup>
        </h:form>
    </rich:modalPanel>

    <%@include file="../../includes/autorizacao/include_autorizacao_funcionalidade.jsp" %>
    <%@include file="include_modalFiltrosLancamentos.jsp" %>
    <%@include file="includes/include_modal_lancarPagamento.jsp" %>
    <%@include file="includes/include_modal_lancarPagamentoConciliacao.jsp" %>
    <%@include file="includes/include_modal_lancarPagamentoConciliacaoEmMassa.jsp" %>
    <%@include file="includes/include_modal_criarNovaContaConciliacao.jsp" %>
    <%@include file="include_modalFiltrosConciliacaoContas.jsp" %>
    <%@include file="pessoaSimplificado.jsp" %>
    <%@include file="includes/include_modal_abrirCaixa.jsp" %>
    <%@include file="includes/include_modal_consultarCaixa.jsp" %>
    <%@include file="includes/include_box_fecharCaixas.jsp" %>
    <%@include file="../../includes/include_modal_mensagem_generica.jsp" %>
    <jsp:include page="../../includes/include_panelMensagem_goBackBlock.jsp"/>
    <%@include file="includes/include_modalSelecaoPlanoConta.jsp"%>
    <%@include file="includes/include_modalSelecaoCentroCusto.jsp"%>
    <%@include file="includes/include_modal_adicionarMultiplasContasConciliacao.jsp" %>
    <%@include file="includes/include_modal_informarPlanoContasCentroCusto.jsp"%>
    <%@include file="includes/include_modal_informarFormaPagamentoQuitarMultiplasContas.jsp"%>
    <%@include file="includes/include_modal_estornoQuitacaoEmMassa.jsp"%>
    <%@include file="includes/include_modal_resultadoEstornoQuitacaoEmMassa.jsp"%>
    <%@include file="includes/include_modal_novoLotePagamento.jsp"%>
    <%@include file="includes/include_modal_novoLotePagamento_detalhes.jsp"%>
    <%@include file="includes/include_modal_novoLotePagamento_editar_lancamento.jsp"%>
    <%@include file="includes/include_modal_detalhes_lote_existente.jsp"%>
    <%@include file="include_modalFiltrosLotes.jsp" %>
</f:view>

<script>
    carregarTooltipster();
    window.addEventListener('load', (e) =>{
        atualizarTreeViewFiltrosLancamento();
    });

    function applyProhibitedCursor(checkbox) {
        // Se o checkbox estiver desabilitado, altere o cursor para "not-allowed"
        if (checkbox.disabled) {
            checkbox.style.cursor = 'not-allowed';
        }
    }

    function resetCursor(checkbox) {
        // Reseta o cursor para o padrão quando o mouse sair do checkbox
        checkbox.style.cursor = '';
    }
</script>
