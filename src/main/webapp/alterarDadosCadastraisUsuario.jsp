<%--
  Created by IntelliJ IDEA.
  User: luiz
  Date: 01/03/2016
  Time: 10:01
  To change this template use File | Settings | File Templates.
--%>
<%@page contentType="text/html" %>
<%@page pageEncoding="ISO-8859-1" %>
<head>
    <%@include file="/includes/include_import_minifiles.jsp" %>
</head>
<link href="./css/otimize.css" rel="stylesheet" type="text/css">
<script type="text/javascript" language="javascript" src="hoverform.js"></script>
<script type="text/javascript" language="javascript" src="script/time_1.3.js"></script>
<script src="${root}/script/packJQueryPlugins.min.js" type="text/javascript"></script>
<script type="text/javascript" src="${root}/script/tooltipster/jquery.tooltipster.min.js"></script>
<link href="./beta/css/font-awesome_2.0.min.css" type="text/css" rel="stylesheet"/>
<link href="./beta/css/pure-buttons.css" type="text/css" rel="stylesheet"/>
<link href="./beta/css/pure-ext.css" type="text/css" rel="stylesheet"/>
<link href="./beta/css/pure-forms.css" type="text/css" rel="stylesheet"/>
<%@taglib prefix="f" uri="http://java.sun.com/jsf/core" %>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html" %>
<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax" %>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich" %>
<%@taglib prefix="jsfChart" uri="http://sourceforge.net/projects/jsf-comp" %>

<%@taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<f:loadBundle var="msg_bt" basename="propriedades.Botoes"/>
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao"/>
<f:loadBundle var="msg" basename="propriedades.Mensagens"/>

<style type="text/css">
    .colunaEsquerda {
        vertical-align: middle;
        text-align: left;
    }

    .colunaDireita {
        vertical-align: middle;
        text-align: right;
    }

    .colunaCentralizada {
        vertical-align: middle;
        text-align: center;
        padding-top: 5px;
    }

    .colunaCentralizadaTop {
        vertical-align: top;
        text-align: center;
        padding-top: 5px;
        width: 10%;
    }

    .botaoItem {
        background: #094771;
        cursor: pointer;
        margin-left: 20px;
        color: white !important;
        font-size: 14px
    }

    .botaoItem:hover {
        /*background: white;*/
        background-color: rgba(0, 0, 0, 0.18);
        color: #094771 !important;
        text-decoration: none;
    }

    .btnMenu {
        color: white !important;
    }

    .btnMenu:hover {
        background: white;
        color: #094771 !important;
        text-decoration: none;
    }

    .selecionado {
        background: white !important;
        text-decoration: none;
    }

    .menuLateral {
        background: #094771;
        /*margin-right: 10px;*/
        /*padding-bottom: 10%;*/
        /*padding-top: 10%;*/
    }

    .botoesMeusDados {
        font-family: Arial;
        font-size: 14px;
        color: white;
        text-decoration: none !important;
        font-weight: normal;
        padding-left: 20px;
    }

    .w10 {
        background: #094771;
    }

    .w90 {
        vertical-align: top;
    }

    .tituloMeusDados {
        background: #FFF;
        font-size: 10pt;
        font-family: 'Trebuchet MS', verdana;
    }

    .headerMeusDados {
        border-color: #FFF;
        background-color: #CCCCCC;
        font-family: Arial, Helvetica, sans-serif;
        font-size: 10pt;
        font-weight: bold;
    }

    .subHeaderMeusDados {
        border-color: #FFF;
        background-color: #CCCCCC;
        font-family: Arial, Helvetica, sans-serif;
        font-size: 8pt;
        font-weight: bold;
    }
</style>

<script>
    jQuery.noConflict();

    function carregarTooltipsterAlterarDados() {
        carregarTooltipsterAltDados(jQuery('.tooltipster'));
    }

    function carregarTooltipsterAltDados(el) {
        el.tooltipster({
            theme: 'tooltipster-light',
            position: 'bottom',
            animation: 'grow',
            contentAsHTML: true
        });
    }
</script>

<script type="text/javascript" language="javascript">
    setTimeout(function () {
        setDocumentCookie('popupsImportante', '', 1);
    }, 500);
    function validar() {

        if (document.getElementById('formConsultarCEP:estadoCEP').value == ""
            && document.getElementById('formConsultarCEP:cidadeCEP').value == ""
            && document.getElementById('formConsultarCEP:bairroCEP').value == ""
            && document.getElementById('formConsultarCEP:logradouroCEP').value == "") {
            alert("Ao menos um parâmetro deve ser informado!");
            return false;
        }
        return true;
    }
</script>

<%@include file="includes/verificaModulo.jsp" %>
<c:set var="contexto" value="${pageContext.request.contextPath}" scope="request"/>

<f:view>
    <jsp:include page="includes/include_carregando_ripple.jsp" flush="true"/>
    <title>${msg_aplic.prt_MeusDados_tituloForm}</title>

    <%-- INICIO HEADER --%>
    <c:set var="titulo" scope="session" value="${msg_aplic.prt_MeusDados_tituloForm}"/>
    <c:set var="urlWiki" scope="session" value="${SuperControle.urlBaseConhecimento}como-o-proprio-usuario-faz-para-alterar-os-dados-do-seu-perfil/"/>
    <a4j:loadScript src="/script/jquery.maskedinput-1.2.2.js"/>
    <f:facet name="header">
        <c:if test="${modulo eq 'zillyonWeb'}">
            <jsp:include page="topoReduzido_material.jsp"/>
        </c:if>
        <c:if test="${modulo eq 'centralEventos'}">
            <jsp:include page="pages/ce/includes/topoReduzido.jsp"/>
        </c:if>
    </f:facet>

    <%-- FIM HEADER --%>

    <h:form id="form" styleClass="pure-form" style="background: #FFF">

        <hr style="border-color: #e6e6e6;"/>
        <input type="hidden" value="${modulo}" name="modulo"/>

        <h:panelGroup layout="block" id="conteudo">

            <%--MEUS DADOS--%>
            <rich:simpleTogglePanel opened="true" switchType="client" headerClass="colunacentralizada">

                <f:facet name="header">
                    <h:outputText value="Dados Cadastrais"/>
                </f:facet>

                <h:panelGrid columns="2" width="100%" headerClass="headerMeusDados" styleClass="tituloMeusDados"
                             style="background: #FFF;"
                             columnClasses="colunaCentralizadaTop, colunaCentralizada">

                    <h:panelGrid columns="1" headerClass="headerMeusDados">

                        <f:facet name="header">
                            <h:outputText value="Foto"/>
                        </f:facet>

                        <a4j:jsFunction name="updateFoto"
                                        action="#{UsuarioControle.recarregarFotoMeusDados}"
                                        oncomplete="fireElementFromParent('form:btnAtualizaFotoUsuario')"
                                        reRender="panelFotoColaborador"/>

                        <h:panelGroup layout="block" id="panelFotoColaborador" style="padding-top: 5%">
                            <a4j:mediaOutput
                                    id="mediaOutput"
                                    element="img"
                                    style="left:0px;width:150px;height:150px;border-radius: 100%" cacheable="false"
                                    session="true"
                                    rendered="#{!SuperControle.fotosNaNuvem}"
                                    createContent="#{UsuarioControle.paintFotoMeusDados}"
                                    value="#{ImagemData}"
                                    mimeType="image/jpeg">
                                <f:param value="#{SuperControle.timeStamp}" name="time"/>
                                <f:param name="largura" value="120"/>
                                <f:param name="altura" value="150"/>
                            </a4j:mediaOutput>
                            <h:graphicImage
                                    id="graphicImage"
                                    rendered="#{SuperControle.fotosNaNuvem}"
                                    width="150" height="150"
                                    style="width:150px;height:150px;border-radius: 100%"
                                    url="#{UsuarioControle.paintFotoDaNuvemMeusDados}?time=#{SuperControle.timeStamp}">
                            </h:graphicImage>
                        </h:panelGroup>

                        <h:panelGrid id="botoesFoto" columns="2" width="85%" columnClasses="colunaDireita, colunaEsquerda">
                            <a4j:commandButton actionListener="#{CapturaFotoControle.selecionarPessoa}"
                                               action="#{UsuarioControle.verificaColaborador}"
                                               id="btnAlterarFoto"
                                               image="./imagens/webcam.png"
                                               oncomplete="#{UsuarioControle.onComplete}"
                                               reRender="form"
                                               title="#{msg_bt.btn_capturarfoto}" styleClass="botoes">
                                <f:attribute name="pessoa"
                                             value="#{UsuarioControle.usuarioVO.colaboradorVO.pessoa.codigo}"/>
                            </a4j:commandButton>
                            <a4j:commandButton id="btnRemoverFoto" value="#{msg_bt.btn_removerfoto}"
                                               image="./images/icon_delete.png"
                                               onclick="if (!confirm('Confirma exclusão da foto ?')){return false;}"
                                               action="#{UsuarioControle.removerFotoMeusDados}"
                                               oncomplete="fireElementFromParent('form:btnAtualizaFotoUsuario')"
                                               reRender="panelFotoColaborador"
                                               title="#{msg_bt.btn_removerfoto}"
                                               styleClass="botoes"/>
                        </h:panelGrid>
                    </h:panelGrid>


                    <h:panelGroup layout="block">

                        <h:panelGrid columns="2" width="100%" headerClass="headerMeusDados"
                                     styleClass="tituloMeusDados"
                                     style="background: #FFF" columnClasses="colunaDireita, colunaEsquerda">


                            <f:facet name="header">
                                <h:outputText value="Dados Cadastrais"/>
                            </f:facet>

                            <h:outputText value="#{msg_aplic.prt_Colaborador_codigo}"/>
                            <h:inputText id="codigoUsuario" size="5"
                                         styleClass="form"
                                         disabled="true"
                                         value="#{UsuarioControle.usuarioVO.codigo}"/>


                            <h:outputText value="#{msg_aplic.prt_Colaborador_nome}"/>
                            <h:inputText id="nome" size="50" maxlength="50" onblur="blurinput(this);"
                                         onfocus="focusinput(this);" styleClass="form"
                                         value="#{UsuarioControle.pessoaVO.nome}"/>


                            <h:outputText value="#{msg_aplic.prt_Colaborador_dataNasc}"/>
                            <rich:calendar id="dataNasc"
                                           value="#{UsuarioControle.pessoaVO.dataNasc}"
                                           inputClass="form"
                                           zindex="2"
                                           showWeeksBar="false"
                                           inputSize="8"
                                           enableManualInput="true"
                                           oninputblur="blurinput(this);"
                                           oninputfocus="focusinput(this);"
                                           oninputchange="return validar_Data(this.id);"
                                           oninputkeypress="return mascara(this.form, this.id, '99/99/9999', event);"
                                           datePattern="dd/MM/yyyy"/>


                            <h:outputText value="#{msg_aplic.prt_Pessoa_cfp}:"/>
                            <h:inputText id="cfp" size="14" maxlength="14" onblur="blurinput(this);"
                                         onfocus="focusinput(this);" styleClass="form"
                                         onkeypress="return mascara(this.form, this.id, '999.999.999-99', event);"
                                         value="#{UsuarioControle.pessoaVO.cfp}"/>


                            <h:outputText value="* E-mail:"/>
                            <h:panelGroup layout="block"
                                          style="display: flex; align-items: center;">
                                <h:inputText id="emailColaborador"
                                             disabled="true"
                                             size="40" maxlength="50"
                                             onblur="blurinput(this);" onfocus="focusinput(this);"
                                             styleClass="form tooltipster"
                                             title="Ao alterar o e-mail será enviado um e-mail para confirmação da alteração."
                                             value="#{UsuarioControle.usuarioEmailVO.email}"/>
                                <h:panelGroup layout="block" id="panelEmailVerificado"
                                              style="display: flex; align-items: center;">
                                    <h:outputText id="emailVerificado"
                                                  rendered="#{UsuarioControle.usuarioEmailVO.verificado && not empty UsuarioControle.usuarioEmailVO.email}"
                                                  style="padding-left: 2px"
                                                  styleClass="outUsuarioDisponivel userdisponivel fa-icon-ok"
                                                  value="Verificado"/>
                                    <h:outputText id="emailNaoVerificado"
                                                  rendered="#{!UsuarioControle.usuarioEmailVO.verificado && not empty UsuarioControle.usuarioEmailVO.email}"
                                                  style="padding-left: 2px"
                                                  styleClass="outUsuarioDisponivel userindisponivel fa-icon-remove"
                                                  value="Não verificado"/>

                                    <a4j:commandLink id="btnVerificarEmail"
                                                       style="margin-left: 10px;"
                                                       rendered="#{!UsuarioControle.usuarioEmailVO.verificado && not empty UsuarioControle.usuarioEmailVO.email}"
                                                       action="#{UsuarioControle.enviarCodigoVerificarEmail}"
                                                       value="Verificar"
                                                       reRender="formValidarToken"
                                                       oncomplete="#{UsuarioControle.onComplete}#{UsuarioControle.mensagemNotificar}"
                                                       styleClass="btSec pure-button pure-button-small"/>

                                    <a4j:commandLink id="btnAbrirNovoEmail"
                                                       style="margin-left: 10px;"
                                                       action="#{UsuarioControle.abrirNovoEmail}"
                                                       value="Alterar"
                                                       reRender="formEmailLogin"
                                                       oncomplete="#{UsuarioControle.onComplete}#{UsuarioControle.mensagemNotificar}"
                                                       styleClass="btSec pure-button pure-button-small"/>
                                </h:panelGroup>
                            </h:panelGroup>

                            <h:outputText value="Celular:"/>
                            <h:panelGroup layout="block"
                                          style="display: flex; align-items: center;">
                                <h:inputText id="telefoneUsuarioAlt"
                                             size="13"
                                             maxlength="13"
                                             disabled="true"
                                             onblur="blurinput(this);"
                                             onkeypress="return mascara(this.form, this.id , '(99)999999999', event);"
                                             onfocus="focusinput(this);"
                                             styleClass="form"
                                             value="#{UsuarioControle.usuarioTelefoneVO.numero}"/>

                                <h:panelGroup layout="block" id="panelTelefoneVerificado"
                                              style="display: flex; align-items: center;">
                                    <h:outputText id="telefoneVerificado"
                                                  rendered="#{UsuarioControle.usuarioTelefoneVO.verificado && not empty UsuarioControle.usuarioTelefoneVO.numero}"
                                                  style="padding-left: 2px"
                                                  styleClass="outUsuarioDisponivel userdisponivel fa-icon-ok"
                                                  value="Verificado"/>
                                    <h:outputText id="telefoneNaoVerificado"
                                                  rendered="#{!UsuarioControle.usuarioTelefoneVO.verificado && not empty UsuarioControle.usuarioTelefoneVO.numero}"
                                                  style="padding-left: 2px"
                                                  styleClass="outUsuarioDisponivel userindisponivel fa-icon-remove"
                                                  value="Não verificado"/>

                                    <a4j:commandLink id="btnAbrirNovoTelefone"
                                                       style="margin-left: 10px;"
                                                       action="#{UsuarioControle.abrirNovoTelefone}"
                                                       value="Alterar"
                                                       reRender="formTelefoneLogin"
                                                       oncomplete="#{UsuarioControle.onComplete}#{UsuarioControle.mensagemNotificar}"
                                                       styleClass="btSec pure-button pure-button-small"/>
                                </h:panelGroup>
                            </h:panelGroup>

                            <h:outputText value="Idioma:"/>
                            <h:selectOneMenu id="linguagem" onblur="blurinput(this);"
                                             onfocus="focusinput(this);" styleClass="form"
                                             value="#{UsuarioControle.usuarioVO.linguagem}">
                                <f:selectItems value="#{UsuarioControle.locales}" />
                            </h:selectOneMenu>


                            <h:outputText value="#{msg_aplic.prt_Usuario_senha}"/>
                            <h:panelGrid columns="1"
                                         cellpadding="0"
                                         cellspacing="0">

                                <h:panelGroup layout="block">
                                    <a4j:commandLink id="solicitarNovaSenhaNewLogin"
                                                     action="#{UsuarioControle.solicitarNovaSenha}"
                                                     oncomplete="#{UsuarioControle.mensagemNotificar}"
                                                     style="font-size: 12px;font-weight: 400;"
                                                     title="Será enviado um e-mail com o link para criar uma nova senha"
                                                     value="Alterar senha"
                                                     styleClass="btSec pure-button pure-button-small tooltipster">
                                    </a4j:commandLink>
                                </h:panelGroup>
                            </h:panelGrid>

                            <%--INFORMAÇÕES DE USUÁRIO--%>
                            <h:outputText value="#{msg_aplic.prt_Usuario_pin}"/>

                            <h:inputSecret size="14" maxlength="4"
                                           value="#{UsuarioControle.pin}"/>

                        </h:panelGrid>
                    </h:panelGroup>
                </h:panelGrid>
            </rich:simpleTogglePanel>

            <%--ENDEREÇO--%>
            <rich:simpleTogglePanel opened="false" switchType="client" headerClass="colunacentralizada">
                <f:facet name="header">
                    <h:outputText value="Endereço"/>
                </f:facet>

                <h:panelGrid id="panelEnderecoUsuario1" columns="1" width="100%"
                             headerClass="headerMeusDados"
                             columnClasses="colunaCentralizada">

                    <f:facet name="header">
                        <h:outputText value="Endereço"/>
                    </f:facet>

                    <h:panelGrid columns="2" width="100%" headerClass="headerMeusDados"
                                 styleClass="tituloMeusDados"
                                 style="background: #FFF" columnClasses="colunaDireita, colunaEsquerda">

                        <c:if test="${fn:contains(UsuarioControle.listaCamposMostrarColaboradorDinamico, 'CEP')}">
                            <h:panelGroup>
                                <c:if test="${fn:contains(UsuarioControle.listaCamposObrigatorioColaboradorDinamico, 'CEP')}">
                                    <h:outputLabel value="* "/>
                                </c:if>
                                <h:outputText value="#{msg_aplic.prt_Endereco_cep}"/>
                            </h:panelGroup>
                        </c:if>
                        <c:if test="${fn:contains(UsuarioControle.listaCamposMostrarColaboradorDinamico, 'CEP')}">
                            <h:panelGroup>
                                <h:inputText  id="cep" size="10" maxlength="10" onkeypress="return mascara(this.form, 'form:cep', '99.999-999', event);" onblur="blurinput(this);"  onfocus="focusinput(this);" styleClass="form" value="#{UsuarioControle.enderecoVO.cep}" />
                                <rich:spacer width="10"/>
                                <a4j:commandLink id="linkConsultaCEP"  reRender="form:panelEnderecoUsuario1" focus="form:enderecoCorresponencia" action="#{UsuarioControle.consultarCEPCadastroCompleto}">Consulte o CEP</a4j:commandLink>
                                <rich:spacer width="10"/>
                                <a4j:commandButton id="consultaDadosCep" alt="Consultar CEP" reRender="formConsultarCEP"
                                                   oncomplete="Richfaces.showModalPanel('panelCEP') , setFocus(formConsultarCEP,'formConsultarCEP:estadoCEP');"
                                                   image="./imagens/informacao.gif" />
                            </h:panelGroup>
                        </c:if>
                        <c:if test="${fn:contains(UsuarioControle.listaCamposMostrarColaboradorDinamico, 'ENDERECO')}">
                            <h:panelGroup>
                                <c:if test="${fn:contains(UsuarioControle.listaCamposObrigatorioColaboradorDinamico, 'ENDERECO')}">
                                    <h:outputLabel value="* "/>
                                </c:if>
                                <h:outputText value="#{msg_aplic.prt_Endereco_endereco}"/>
                            </h:panelGroup>
                        </c:if>
                        <c:if test="${fn:contains(UsuarioControle.listaCamposMostrarColaboradorDinamico, 'ENDERECO')}">
                            <h:inputText id="enderecoColaborador" size="40" maxlength="40"
                                         onblur="blurinput(this);"
                                         onfocus="focusinput(this);" styleClass="form"
                                         value="#{UsuarioControle.enderecoVO.endereco}"/>
                        </c:if>
                        <c:if test="${fn:contains(UsuarioControle.listaCamposMostrarColaboradorDinamico, 'COMPLEMENTO')}">
                            <h:panelGroup>
                                <c:if test="${fn:contains(UsuarioControle.listaCamposObrigatorioColaboradorDinamico, 'COMPLEMENTO')}">
                                    <h:outputLabel value="* "/>
                                </c:if>
                                <h:outputText value="#{msg_aplic.prt_Endereco_complemento}"/>
                            </h:panelGroup>
                        </c:if>
                        <c:if test="${fn:contains(UsuarioControle.listaCamposMostrarColaboradorDinamico, 'COMPLEMENTO')}">
                            <h:inputText size="40" maxlength="40" onblur="blurinput(this);"
                                         onfocus="focusinput(this);" styleClass="form"
                                         value="#{UsuarioControle.enderecoVO.complemento}"/>
                        </c:if>
                        <c:if test="${fn:contains(UsuarioControle.listaCamposMostrarColaboradorDinamico, 'NUMERO')}">
                            <h:panelGroup>
                                <c:if test="${fn:contains(UsuarioControle.listaCamposObrigatorioColaboradorDinamico, 'NUMERO')}">
                                    <h:outputLabel value="* "/>
                                </c:if>
                                <h:outputText value="#{msg_aplic.prt_Endereco_numero}"/>
                            </h:panelGroup>
                        </c:if>
                        <c:if test="${fn:contains(UsuarioControle.listaCamposMostrarColaboradorDinamico, 'NUMERO')}">
                            <h:inputText size="10" maxlength="10" onblur="blurinput(this);"
                                         onfocus="focusinput(this);" styleClass="form"
                                         value="#{UsuarioControle.enderecoVO.numero}"/>
                        </c:if>
                        <c:if test="${fn:contains(UsuarioControle.listaCamposMostrarColaboradorDinamico, 'BAIRRO')}">
                            <h:panelGroup>
                                <c:if test="${fn:contains(UsuarioControle.listaCamposObrigatorioColaboradorDinamico, 'BAIRRO')}">
                                    <h:outputLabel value="* "/>
                                </c:if>
                                <h:outputText value="#{msg_aplic.prt_Endereco_bairro}"/>
                            </h:panelGroup>
                        </c:if>
                        <c:if test="${fn:contains(UsuarioControle.listaCamposMostrarColaboradorDinamico, 'BAIRRO')}">
                            <h:inputText size="35" maxlength="35" onblur="blurinput(this);"
                                         onfocus="focusinput(this);" styleClass="form"
                                         value="#{UsuarioControle.enderecoVO.bairro}"/>
                        </c:if>
                        <c:if test="${fn:contains(UsuarioControle.listaCamposMostrarColaboradorDinamico, 'ENDERECOCORRESPONDENCIA')}">
                            <h:panelGroup>
                                <c:if test="${fn:contains(UsuarioControle.listaCamposObrigatorioColaboradorDinamico, 'ENDERECOCORRESPONDENCIA')}">
                                    <h:outputLabel value="* "/>
                                </c:if>
                                <h:outputText value="#{msg_aplic.prt_Endereco_enderecoCorrespondencia}"/>
                            </h:panelGroup>
                        </c:if>
                        <c:if test="${fn:contains(UsuarioControle.listaCamposMostrarColaboradorDinamico, 'ENDERECOCORRESPONDENCIA')}">
                            <h:selectBooleanCheckbox id="enderecoCorresponencia"
                                                     value="#{UsuarioControle.enderecoVO.enderecoCorrespondencia}"/>
                        </c:if>
                        <c:if test="${fn:contains(UsuarioControle.listaCamposMostrarColaboradorDinamico, 'TIPOENDERECO')}">
                            <h:panelGroup>
                                <c:if test="${fn:contains(UsuarioControle.listaCamposObrigatorioColaboradorDinamico, 'TIPOENDERECO')}">
                                    <h:outputLabel value="* "/>
                                </c:if>
                                <h:outputText value="#{msg_aplic.prt_Endereco_tipoEndereco}"/>
                            </h:panelGroup>
                        </c:if>
                        <c:if test="${fn:contains(UsuarioControle.listaCamposMostrarColaboradorDinamico, 'TIPOENDERECO')}">
                            <h:selectOneMenu id="Endereco_tipoEndereco" onblur="blurinput(this);"
                                             onfocus="focusinput(this);" styleClass="form"
                                             value="#{UsuarioControle.enderecoVO.tipoEndereco}">
                                <f:selectItems
                                        value="#{UsuarioControle.listaSelectItemTipoEnderecoEndereco}"/>
                            </h:selectOneMenu>
                        </c:if>
                    </h:panelGrid>

                    <a4j:commandButton id="addEndereco"
                                       action="#{UsuarioControle.adicionarEndereco}"
                                       reRender="panelEnderecoUsuario1, panelMensagemErro"
                                       focus="enderecoColaborador" value="#{msg_bt.btn_adicionar}"
                                       image="./imagens/botaoAdicionar.png" accesskey="5" styleClass="botoes"/>

                    <h:panelGrid columns="1" width="100%" styleClass="tabFormSubordinada">
                        <h:dataTable id="enderecoVO" width="100%"
                                     headerClass="subHeaderMeusDados"
                                     rowClasses="linhaImpar"
                                     columnClasses="colunaAlinhamento, colunaAlinhamento, colunaAlinhamento, colunaAlinhamento, colunaAlinhamento, colunaAlinhamento, colunaAlinhamento"
                                     value="#{UsuarioControle.pessoaVO.enderecoVOs}"
                                     var="endereco">
                            <h:column>
                                <f:facet name="header">
                                    <h:outputText value="#{msg_aplic.prt_Endereco_endereco}"/>
                                </f:facet>
                                <h:outputText value="#{endereco.endereco}"/>
                            </h:column>
                            <h:column>
                                <f:facet name="header">
                                    <h:outputText value="#{msg_aplic.prt_Endereco_complemento}"/>
                                </f:facet>
                                <h:outputText value="#{endereco.complemento}"/>
                            </h:column>
                            <h:column>
                                <f:facet name="header">
                                    <h:outputText value="#{msg_aplic.prt_Endereco_numero}"/>
                                </f:facet>
                                <h:outputText value="#{endereco.numero}"/>
                            </h:column>
                            <h:column>
                                <f:facet name="header">
                                    <h:outputText value="#{msg_aplic.prt_Endereco_bairro}"/>
                                </f:facet>
                                <h:outputText value="#{endereco.bairro}"/>
                            </h:column>
                            <h:column>
                                <f:facet name="header">
                                    <h:outputText value="#{msg_aplic.prt_Endereco_cep}"/>
                                </f:facet>
                                <h:outputText value="#{endereco.cep}"/>
                            </h:column>
                            <h:column>
                                <f:facet name="header">
                                    <h:outputText value="#{msg_aplic.prt_Endereco_tipoEndereco}"/>
                                </f:facet>
                                <h:outputText value="#{endereco.tipoEndereco_Apresentar}"/>
                            </h:column>
                            <h:column>
                                <f:facet name="header">
                                    <h:outputText value="#{msg_bt.btn_opcoes}"/>
                                </f:facet>
                                <h:panelGroup>
                                    <h:commandButton id="editarItemVenda"
                                                     action="#{UsuarioControle.editarEndereco}"
                                                     value="#{msg_bt.btn_editar}"
                                                     image="./imagens/botaoEditar.png"
                                                     accesskey="6" styleClass="botoes"/>

                                    <h:outputText value="    "/>

                                    <h:commandButton id="removerItemVenda" immediate="true"
                                                     action="#{UsuarioControle.removerEndereco}"
                                                     value="#{msg_bt.btn_excluir}"
                                                     image="./imagens/botaoRemover.png" accesskey="7"
                                                     styleClass="botoes"/>
                                </h:panelGroup>
                            </h:column>
                        </h:dataTable>
                    </h:panelGrid>
                </h:panelGrid>
            </rich:simpleTogglePanel>


            <h:panelGrid columns="1" width="100%" columnClasses="colunaCentralizada"
                         style="background: #eee;">
                <h:panelGroup layout="block" style="padding: 10px">

                    <a4j:commandLink value="Cancelar"
                                     styleClass="botoes nvoBt btSec"
                                     onclick="window.close()"/>

                    <h:outputText value="    "/>

                    <a4j:commandLink value="Gravar"
                                     styleClass="botoes nvoBt"
                                     action="#{UsuarioControle.gravarMeusDados}"
                                     oncomplete="#{UsuarioControle.modalMensagemGenerica}"
                                     reRender="mdlMensagemGenerica"/>

                </h:panelGroup>
            </h:panelGrid>


        </h:panelGroup>
        <rich:jQuery id="mskData" selector=".rich-calendar-input" timing="onload" query="mask('99/99/9999')" />
    </h:form>
    <%--CONSULTA ENDEREÇO--%>
    <rich:modalPanel id="panelCEP" autosized="true" styleClass="novaModal" shadowOpacity="true" width="500" height="400">
        <f:facet name="header">
            <h:panelGroup>
                <h:outputText value="#{msg_aplic.prt_CEP_tituloConsulta}"></h:outputText>
            </h:panelGroup>
        </f:facet>
        <f:facet name="controls">
            <h:panelGroup>
                <h:outputText
                        styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign btnSelecionarModalidade"
                        id="hidelinkCEP"/>
                <rich:componentControl for="panelCEP" attachTo="hidelinkCEP" operation="hide" event="onclick"/>
            </h:panelGroup>
        </f:facet>


        <a4j:form id="formConsultarCEP" ajaxSubmit="true" styleClass="paginaFontResponsiva">
            <h:panelGrid columns="1" width="100%" columnClasses="colunaCentralizada">
                <h:panelGrid columns="2" columnClasses="classEsquerda" width="100%">
                    <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_CEP_estadoC_maiusculo}" />
                    <h:panelGroup layout="block" styleClass="cb-container" style="height: 40px;">
                        <h:selectOneMenu id="estadoCEP" styleClass="campos" value="#{UsuarioControle.cepControle.cepVO.ufSigla}">
                            <f:selectItems value="#{UsuarioControle.listaSelectItemRgUfPessoa}" />
                        </h:selectOneMenu>
                    </h:panelGroup>
                    <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_CEP_cidadeC_maiusculo}" />
                    <h:inputText id="cidadeCEP" size="20" styleClass="campos" value="#{UsuarioControle.cepControle.cepVO.cidadeDescricao}" />
                    <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_CEP_bairro_maiusculo}" />
                    <h:inputText id="bairroCEP" size="20" styleClass="campos" value="#{UsuarioControle.cepControle.cepVO.bairroDescricao}" />
                    <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_CEP_logradouro_maiusculo}" />
                    <h:inputText id="logradouroCEP" size="20" styleClass="campos" value="#{UsuarioControle.cepControle.cepVO.enderecoLogradouro}" />
                </h:panelGrid>
                <h:panelGrid columns="1">

                    <h:panelGroup>
                        <h:outputText styleClass="textsmall" value="Informe o nome ou parte do seu logradouro, rua ou avenida. Não Inclua o tipo da via nem o número da sua casa." />
                    </h:panelGroup>
                </h:panelGrid>
                <h:panelGroup>
                    <c:if test="${modulo eq 'zillyonWeb'}">
                        <a4j:commandLink id="btnConsultarCEP"
                                         onclick="if(!validar()){return false;};"
                                         reRender=":formConsultarCEP"
                                         action="#{UsuarioControle.cepControle.consultarCEPDetalhe}"
                                         styleClass="botaoPrimario texto-size-14" value="#{msg_bt.btn_consultar}"

                                         title="#{msg.msg_consultar_dados}"></a4j:commandLink>
                    </c:if>
                    <c:if test="${modulo eq 'centralEventos'}">
                        <a4j:commandLink id="btnConsultarCEP"
                                         onclick="if(!validar()){return false;};"
                                         reRender="formConsultarCEP:mensagemConsultaCEP, formConsultarCEP:resultadoConsultaCEP, formConsultarCEP:scResultadoCEP"
                                         action="#{UsuarioControle.cepControle.consultarCEPDetalhe}"
                                         styleClass="botaoPrimario texto-size-14" value="#{msg_bt.btn_consultar}"
                                         title="#{msg.msg_consultar_dados}"/>
                    </c:if>
                </h:panelGroup>
                <h:panelGroup layout="block" >
                    <rich:dataTable id="resultadoConsultaCEP" width="100%" styleClass="tabelaSimplesCustom"
                                    rendered="#{not empty UsuarioControle.cepControle.listaConsultaCep}" value="#{UsuarioControle.cepControle.listaConsultaCep}" rows="4" var="cep">
                        <rich:column>
                            <f:facet name="header">
                                <h:outputText style="float: left;" styleClass="texto-font texto-bold texto-cor-cinza texto-size-14" value="#{msg_aplic.prt_CEP_titulo}" />
                            </f:facet>
                            <h:panelGroup>
                                <a4j:commandLink action="#{UsuarioControle.selecionarCep}" focus="CEP"
                                                 styleClass="texto-font texto-cor-cinza texto-size-14"
                                                 reRender="form:panelEnderecoUsuario1"
                                                 oncomplete="Richfaces.hideModalPanel('panelCEP')" value="#{cep.enderecoCep}" />
                            </h:panelGroup>
                        </rich:column>
                        <rich:column>
                            <f:facet name="header">
                                <h:outputText style="float: left;" styleClass="texto-font texto-bold texto-cor-cinza texto-size-14"  value="#{msg_aplic.prt_CEP_cidadeC}" />
                            </f:facet>
                            <h:panelGroup>
                                <a4j:commandLink action="#{UsuarioControle.selecionarCep}" focus="CEP"
                                                 styleClass="texto-font texto-cor-cinza texto-size-14"
                                                 reRender="form:panelEnderecoUsuario1"
                                                 oncomplete="Richfaces.hideModalPanel('panelCEP')" value="#{cep.cidadeDescricao}" />
                            </h:panelGroup>
                        </rich:column>
                        <rich:column>
                            <f:facet name="header">
                                <h:outputText style="float: left;" styleClass="texto-font texto-bold texto-cor-cinza texto-size-14"  value="#{msg_aplic.prt_CEP_bairroC}" />
                            </f:facet>
                            <a4j:commandLink action="#{UsuarioControle.selecionarCep}" focus="CEP"
                                             styleClass="texto-font texto-cor-cinza texto-size-14"
                                             reRender="form:panelEnderecoUsuario1"
                                             oncomplete="Richfaces.hideModalPanel('panelCEP')" value="#{cep.bairroDescricao}" />
                        </rich:column>
                        <rich:column>
                            <f:facet name="header">
                                <h:outputText styleClass="texto-font texto-bold texto-cor-cinza texto-size-14"  value="#{msg_aplic.prt_CEP_logradouroC}" />
                            </f:facet>
                            <a4j:commandLink action="#{UsuarioControle.selecionarCep}" focus="CEP"
                                             styleClass="texto-font texto-cor-cinza texto-size-14"
                                             reRender="form:panelEnderecoUsuario1"
                                             oncomplete="Richfaces.hideModalPanel('panelCEP')" value="#{cep.enderecoLogradouro}" />
                        </rich:column>
                    </rich:dataTable>
                    <rich:datascroller id="scResultadoCEP" align="center" style="margin-top: 10px" styleClass="scrollPureCustom" renderIfSinglePage="false" for="formConsultarCEP:resultadoConsultaCEP" maxPages="10" />
                </h:panelGroup>
                <h:panelGrid id="mensagemConsultaCEP" columns="1" width="100%" styleClass="tabMensagens">
                    <h:panelGrid columns="1" width="100%">
                        <h:outputText styleClass="mensagem" value="#{UsuarioControle.cepControle.mensagem}" />
                        <h:outputText styleClass="mensagemDetalhada" value="#{UsuarioControle.cepControle.mensagemDetalhada}" />
                    </h:panelGrid>
                </h:panelGrid>
            </h:panelGrid>
        </a4j:form>
    </rich:modalPanel>

    <jsp:include page="includes/cliente/include_modal_capfoto_html5.jsp" flush="true"/>

    <%@include file="includes/include_modal_mensagem_generica.jsp" %>
    <%@include file="includes/include_modais_usuario_geral.jsp" %>
</f:view>

<script type="text/javascript">
    function removerSelecionado(id) {
        jQuery('.btnMenu').removeClass('selecionado');
        jQuery('.' + id + '').addClass('selecionado');
    }

    function validarSenha() {
        senha = document.getElementById("form:novasenha").value;
        senhaRepetida = document.getElementById("form:confirmarnovasenha").value;
        if (senhaRepetida == senha) {
            document.getElementById("form:senhaErrada").hide();
        } else {
            document.getElementById("form:senhaErrada").show();
            document.getElementById("form:confirmarnovasenha").focus();
        }
    }

    function clearAccentuation() {
        const username = document.getElementById('form:username');
        if (username) {
            username.addEventListener('keyup', e => {
                const start = this.selectionStart,
                    end = this.selectionEnd;

                username.value = username.value.normalize("NFD").replace(/[\u0300-\u036f]/g, "");

                this.setSelectionRange(start, end);
            });
        }
    }
    clearAccentuation();

    carregarTooltipsterAlterarDados();
</script>
