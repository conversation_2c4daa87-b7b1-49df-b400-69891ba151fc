<%@page pageEncoding="ISO-8859-1" %>
<%--
    Document   : include_paramspanel_transacao
    Created on : 03/08/2011, 16:05:03
    Author     : waller
--%>

<%@include file="/includes/imports.jsp" %>

<a4j:outputPanel>


    <%-- PARÂMETROS UTILIZADOS NA TRANSMISSÃO DA TRANSAÇÃO--%>
    <rich:modalPanel  id="panelDadosParametros" showWhenRendered="#{GestaoTransacoesControle.exibirModalParametros}" width="500"
                      autosized="true" styleClass="novaModal"
                      shadowOpacity="true">

        <h:panelGrid id="pnlGridMensagem1" columns="2" width="100%" rendered="#{not empty GestaoTransacoesControle.mensagemDetalhada}" title="#{GestaoTransacoesControle.mensagemDetalhada}">
            <h:graphicImage value="imagens/icon-error.png" width="45"/>
            <h:panelGrid columns="1" width="100%">
                <h:outputText styleClass="mensagemDetalhada"
                              value="#{GestaoTransacoesControle.mensagem}"/>
            </h:panelGrid>
        </h:panelGrid>

        <f:facet name="header">
            <h:panelGroup>
                <c:choose>
                    <c:when test="${!empty GestaoTransacoesControle.transacaoVO.codigoExterno}">
                        <h:outputText value="Transação: #{GestaoTransacoesControle.transacaoVO.codigoExterno} -
                                      #{GestaoTransacoesControle.transacaoVO.dataProcessamento_Apresentar}" />
                    </c:when>
                    <c:otherwise>
                        <h:outputText value="Transação: #{GestaoTransacoesControle.transacaoVO.dataProcessamento_Apresentar}"/>
                    </c:otherwise>
                </c:choose>
            </h:panelGroup>
        </f:facet>

        <f:facet name="controls">
            <h:panelGroup>
                <a4j:form id="formFE">
                    <a4j:commandButton id="btnFecharEnvio" image="imagens/close.png" style="cursor:pointer"
                                       action="#{GestaoTransacoesControle.fecharPanelDadosParametros}"
                                       oncomplete="#{rich:component('panelDadosParametros')}.hide();"/>
                </a4j:form>
            </h:panelGroup>
        </f:facet>
        <h:panelGroup>
            <c:if test="${empty GestaoTransacoesControle.listaParametrosSelecionado}">
                <h:panelGrid columns="2">
                    <h:graphicImage value="images/warning.png"/>
                    <h:outputText styleClass="mensagemDetalhada" value="Não há parâmetros para a situação atual desta transação."/>
                </h:panelGrid>
            </c:if>
            <h:panelGroup layout="block" style="height: 400px; overflow-y: scroll; overflow-x: hidden;">
                <rich:dataTable  width="100%"  value="#{GestaoTransacoesControle.listaParametrosSelecionado}" var="obj">
                    <f:facet name="header">
                        <c:if test="${!empty GestaoTransacoesControle.listaParametrosSelecionado}">
                            <h:outputText value="Parâmetros utilizados"/>
                        </c:if>
                    </f:facet>
                    <rich:column >
                        <f:facet name="header">
                            <h:outputText value="Atributo"/>
                        </f:facet>
                        <h:outputText value="#{obj.atributo}"/>
                    </rich:column>
                    <rich:column>
                        <f:facet name="header">
                            <h:outputText value="Valor"/>
                        </f:facet>
                        <h:outputText style="font-weight:bold;" value="#{obj.valor}"/>
                    </rich:column>
                </rich:dataTable>
            </h:panelGroup>

        </h:panelGroup>
    </rich:modalPanel>

    <%-- DADOS DETALHADOS DA TRANSAÇÃO--%>

    <rich:modalPanel  id="panelDadosTransacao" showWhenRendered="#{GestaoTransacoesControle.exibirTransacao}"
                      width="#{SuperControle.widthScreenClient - (SuperControle.widthScreenClient*30/100)}"
                      autosized="true" styleClass="novaModal" shadowOpacity="true">

        <f:facet name="header">
            <h:panelGroup>
                <c:choose>
                    <c:when test="${!empty GestaoTransacoesControle.transacaoVO.codigoExterno}">
                        <h:outputText value="Transação: #{GestaoTransacoesControle.transacaoVO.codigoExterno} -
                                      #{GestaoTransacoesControle.transacaoVO.dataProcessamento_Apresentar}" />
                    </c:when>
                    <c:otherwise>
                        <h:outputText value="Transação: #{GestaoTransacoesControle.transacaoVO.dataProcessamento_Apresentar}"/>
                    </c:otherwise>
                </c:choose>
            </h:panelGroup>
        </f:facet>

        <f:facet name="controls">
            <h:panelGroup>
                <a4j:form id="formF">
                    <a4j:commandButton  id="btnFechar" image="imagens/close.png" style="cursor:pointer"
                                       action="#{GestaoTransacoesControle.fecharPanelDadosParametros}"
                                       oncomplete="#{rich:component('panelDadosTransacao')}.hide();"/>
                </a4j:form>
            </h:panelGroup>
        </f:facet>
        <h:panelGroup id="panelBasico">
            <rich:panel header="Básico" styleClass="text">
                <h:panelGrid columnClasses="textsmall,textsmall,textsmall,textsmall" columns="4">

                    <h:outputText style="font-weight:bold;" value="Id: "/>
                    <h:outputText id="idTransacao" value="#{GestaoTransacoesControle.transacaoVO.codigo}"/>

                    <h:outputText style="font-weight:bold;" value="Situação: "/>
                    <h:panelGroup>
                        <h:panelGrid columnClasses="textsmall, textsmall" columns="2">
                            <h:panelGroup layout="block" style="border:none; width: 8px; height: 8px; background-color:#{GestaoTransacoesControle.transacaoVO.situacao.imagem}"/>
                            <h:outputText id="situacaoDescricao" value="#{GestaoTransacoesControle.transacaoVO.situacao.descricao}"/>
                        </h:panelGrid>
                    </h:panelGroup>

                    <h:outputText style="font-weight:bold;" value="Titular Cartão: "/>
                    <h:outputText style="text-transform: uppercase" id="nomeTitular" value="#{GestaoTransacoesControle.transacaoVO.nomePessoa}"/>

                    <h:outputText style="font-weight:bold;" value="Cartão: "/>
                    <h:outputText id="cartaoMascaradoTransacao" value="#{GestaoTransacoesControle.transacaoVO.cartaoMascarado}"/>

                    <h:outputText rendered="#{!empty GestaoTransacoesControle.transacaoVO.dataHoraCancelamento_Apresentar}" style="font-weight:bold;" value="Data Cancelamento: "/>
                    <h:outputText id="dataCancelamento" rendered="#{!empty GestaoTransacoesControle.transacaoVO.dataHoraCancelamento_Apresentar}" value="#{GestaoTransacoesControle.transacaoVO.dataHoraCancelamento_Apresentar}"/>

                    <h:outputText style="font-weight:bold;" value="Data Processamento: "/>
                    <h:outputText id="dataProcessamento" value="#{GestaoTransacoesControle.transacaoVO.dataProcessamento_Apresentar}"/>                    

                    <h:outputText style="font-weight:bold;" value="Usuário: "/>
                    <h:outputText id="nomeUsuario" value="#{GestaoTransacoesControle.transacaoVO.usuarioResponsavel.nomeAbreviado}"/>

                    <h:outputText style="font-weight:bold;" value="Valor: "/>
                    <h:outputText id="valorTransacao" value="#{GestaoTransacoesControle.transacaoVO.valor_Apresentar}"/>

                    <h:outputText style="font-weight:bold;" value="Nº Vezes: "/>
                    <h:outputText id="nrVezesTransacao"
                                  value="#{GestaoTransacoesControle.transacaoVO.nrVezesCobranca}"/>

                    <c:if test="${GestaoTransacoesControle.transacaoVO.convenioCobrancaVO.codigo > 0}">
                        <h:outputText style="font-weight:bold;" value="Convênio de Cobrança: "/>
                        <h:outputText id="descricaoConvenio"
                                      styleClass="tooltipster"
                                      title="Código do convênio: #{GestaoTransacoesControle.transacaoVO.convenioCobrancaVO.codigo}"
                                      value="#{GestaoTransacoesControle.transacaoVO.convenioCobrancaVO.descricao}"/>
                    </c:if>

                    <c:if test="${GestaoTransacoesControle.transacaoVO.origem.codigo > 0}">
                        <h:outputText style="font-weight:bold;" value="Origem: "/>
                        <h:outputText id="origemTransacao"
                                      value="#{GestaoTransacoesControle.transacaoVO.origem.descricao}"/>
                    </c:if>

                    <h:panelGroup rendered="#{!empty GestaoTransacoesControle.transacaoVO.logEstorno}">
                        <h:graphicImage style="cursor:pointer;" value="images/logEstorno.png" id="logEstorno"/>
                        <rich:toolTip value="Log do Estorno/Cancelamento: #{GestaoTransacoesControle.transacaoVO.logEstorno}"
                                      for="logEstorno"/>
                    </h:panelGroup>
                </h:panelGrid>

            </rich:panel>

            <rich:panel id="panelContratoRecorrencia" header="Contrato Recorrência" rendered="#{!empty GestaoTransacoesControle.contratoRecorrencia}" styleClass="text">
                <h:panelGrid style="font-size: 11px;vertical-align: middle;" columns="2">

                    <h:panelGroup>
                        <h:outputText style="font-weight:bold;" value="Matrícula: "/>
                        <h:outputText id="clienteMatricula" value="#{GestaoTransacoesControle.cliente.matricula}"/>
                    </h:panelGroup>

                    <h:panelGroup>
                        <h:outputText style="font-weight:bold;margin-botton: 5px; margin-top: 5px;" value="Nome Cliente: "/>
                        <h:outputText id="clienteNome" value="#{GestaoTransacoesControle.cliente.pessoa.nome}"/>
                        <h:panelGroup style="margin-botton: 5px; margin-top: 5px;">
                            <a4j:form id="formClienteVisualizar" style="margin: 0 0 0 0; padding: 0 0 0 0; height:11px;"
                                      rendered="#{GestaoTransacoesControle.transacaoVO.aprovaFacil}">
                                <rich:spacer width="10px"/>
                                <a4j:commandButton style="vertical-align:middle;" id="btnAbrirCliente" image="images/search_button.png" title="Abrir cadastro de cliente"
                                                   value="Abrir Cliente" oncomplete="abrirPopup('faces/clienteNav.jsp?page=viewCliente', 'Cliente', 1024, 700);"
                                                   actionListener="#{ClienteControle.atualizarCliente}" action="#{ClienteControle.acaoAjax}">
                                    <f:attribute name="cliente" value="#{GestaoTransacoesControle.cliente}"/>
                                </a4j:commandButton>
                            </a4j:form>
                    </h:panelGroup>

                    </h:panelGroup>                    

                    <h:panelGroup>
                        <h:outputText style="font-weight:bold;" value="Contrato: "/>
                        <h:outputText id="clienteContrato" value="#{GestaoTransacoesControle.contratoRecorrencia.contrato.codigo }"/>
                    </h:panelGroup>

                    <h:panelGroup>
                        <h:outputText style="font-weight:bold;" value="Vigência: "/>
                        <h:outputText id="clienteContratoInicio" value="#{GestaoTransacoesControle.contratoRecorrencia.contrato.vigenciaDe_Apresentar} Até "/>
                        <h:outputText id="clienteContratoFim" value="#{GestaoTransacoesControle.contratoRecorrencia.contrato.vigenciaAteAjustada_Apresentar}"/>
                    </h:panelGroup>

                    <h:panelGroup>
                        <h:outputText style="font-weight:bold;" value="Plano: "/>
                        <h:outputText id="clientePlano" value="#{GestaoTransacoesControle.contratoRecorrencia.contrato.plano.descricao}"/>
                    </h:panelGroup>
                    <h:panelGroup>
                        <h:outputText style="font-weight:bold;" value="Fidelidade: "/>
                        <h:outputText id="clienteFidelidade" value="#{GestaoTransacoesControle.contratoRecorrencia.fidelidade} Meses"/>
                    </h:panelGroup>


                    <c:if test="${GestaoTransacoesControle.transacaoVO.aprovaFacil}">
                        <h:panelGroup>
                            <h:outputText style="font-weight:bold;" value="Nº Cartão Crédito: "/>
                            <h:outputText id="clienteCartao"
                                          value="#{GestaoTransacoesControle.contratoRecorrencia.numeroCartao}"/>
                        </h:panelGroup>
                        <h:panelGroup>
                            <a4j:form id="formTrocaCartao" style="margin: 0 0 0 0; padding: 0 0 0 0; height:11px;">
                                <a4j:commandLink style="font-weight:bold;"
                                                 reRender="mensagemModal,panelBasico,botoes,modalPanelTrocarCartao"
                                                 id="btnTrocarCartaoRecorrencia"
                                                 title="Trocar o cartão de crédito associado a este Contrato de Recorrência"
                                                 value="Trocar Cartão"
                                                 oncomplete="#{rich:component('modalPanelTrocarCartao')}.show();"
                                                 actionListener="#{TrocarCartaoRecorrenciaControle.listenerContratoRecorrencia}">
                                    <f:attribute name="contratoRecorrencia"
                                                 value="#{GestaoTransacoesControle.contratoRecorrencia}"/>
                                </a4j:commandLink>

                            </a4j:form>
                        </h:panelGroup>

                        <h:outputText style="font-weight:bold;" value="Vencimento: "/>
                        <h:panelGroup>
                            <a4j:form id="formTrocaVencimento" style="margin: 0 0 0 0; padding: 0 0 0 0; height:11px;">
                                <a4j:commandLink style="font-weight:bold;"
                                                 reRender="modalAlterarVencimento"
                                                 id="btnAbrirModalAlterarVencimento"
                                                 title="Alterar os vencimentos de parcelas deste contrato"
                                                 value="Todo dia #{GestaoTransacoesControle.contratoRecorrencia.diaVencimentoCartao}"
                                                 oncomplete="#{rich:component('modalAlterarVencimento')}.show();"
                                                 actionListener="#{AlterarVencimentoParcelasControle.preparar}">
                                    <f:attribute name="ehRecorrencia" value="#{true}"/>
                                    <f:attribute name="contrato"
                                                 value="#{GestaoTransacoesControle.contratoRecorrencia.contrato}"/>
                                    <f:attribute name="contratoRecorrencia"
                                                 value="#{GestaoTransacoesControle.contratoRecorrencia}"/>
                                </a4j:commandLink>

                            </a4j:form>
                        </h:panelGroup>

                        <h:outputText style="font-weight:bold;" value="Ult. Transação Aprovada: "/>
                        <h:outputText id="clienteUltimaTrans"
                                      value="#{GestaoTransacoesControle.contratoRecorrencia.ultimaTransacaoAprovada}"/>

                    </c:if>

                    <h:panelGroup style="margin-botton: 5px; margin-top: 5px;">
                        <h:outputText style="font-weight:bold;" value="Anuidade: "/>
                        <h:outputText id="clienteAnuidade" value="#{GestaoTransacoesControle.contratoRecorrencia.valorAnuidade_Apresentar}"/>
                    </h:panelGroup>

                    <h:panelGroup style="margin-botton: 5px; margin-top: 5px;">
                        <h:outputText style="font-weight:bold;" value="Venc.: "/>
                        <h:outputText id="clienteAnuidadeVenc" value="#{GestaoTransacoesControle.contratoRecorrencia.diaVencimentoAnuidade}/#{GestaoTransacoesControle.contratoRecorrencia.mesVencimentoAnuidade}"/>
                    </h:panelGroup>

                    <h:panelGroup style="margin-botton: 5px; margin-top: 5px;">
                        <h:outputText style="font-weight:bold;" value="Valor Mensal: "/>
                        <h:outputText id="clienteValorMensal" value="#{GestaoTransacoesControle.contratoRecorrencia.valorMensal_Apresentar}"/>
                    </h:panelGroup>

                    <h:panelGroup style="margin-botton: 5px; margin-top: 5px;">
                        <h:outputText style="font-weight:bold;" value="Cancelamento automático: "/>
                        <h:outputText id="clienteCancelamentoAuto" value="Após #{GestaoTransacoesControle.contratoRecorrencia.diasCancelamentoAutomatico} dias"/>
                    </h:panelGroup>

                    <h:panelGroup style="margin-botton: 5px; margin-top: 5px;">
                        <h:outputText style="font-weight:bold;" value="Renovável automaticamente: "/>
                        <h:outputText id="clienteRenovacaoAuto" value="#{GestaoTransacoesControle.contratoRecorrencia.renovavelAutomaticamente_Apresentar}"/>
                    </h:panelGroup>

                </h:panelGrid>
            </rich:panel>

            <rich:panel rendered="#{!empty GestaoTransacoesControle.transacaoVO.listaParcelas}"                        
                        header="Parcelas da Transação" styleClass="text">

                <rich:dataTable id="tabParcelas"
                                columnClasses="text" rowClasses="linhaImpar,linhaPar"
                                style="width: 100%; max-height: 100px; padding: 0;"
                                value="#{GestaoTransacoesControle.transacaoVO.listaParcelas}" var="parcela">

                    <rich:column>
                        <f:facet name="header">
                            <h:outputText value="Aluno"/>
                        </f:facet>
                        <h:outputText value="#{parcela.pessoa.nome}"/>
                    </rich:column>

                    <rich:column>
                        <f:facet name="header">
                            <h:outputText value="Parcela"/>
                        </f:facet>
                        <h:outputText value="#{parcela.codigo}"/>
                    </rich:column>

                    <rich:column>
                        <f:facet name="header">
                            <h:outputText value="Descrição Parcela"/>
                        </f:facet>
                        <h:outputText value="#{parcela.descricao}"/>
                    </rich:column>

                    <rich:column>
                        <f:facet name="header">
                            <h:outputText value="Contrato"/>
                        </f:facet>
                         <h:outputText value="#{parcela.contrato.codigo}"/>
                    </rich:column>

                    <rich:column>
                        <f:facet name="header">
                            <h:outputText value="Venda Avulsa"/>
                        </f:facet>
                        <h:outputText value="#{parcela.vendaAvulsaVO.codigo}"/>
                    </rich:column>

                    <rich:column>
                        <f:facet name="header">
                            <h:outputText value="Venc. Parcela"/>
                        </f:facet>
                        <h:outputText value="#{parcela.dataVencimento_Apresentar}"/>
                    </rich:column>

                    <rich:column>
                        <f:facet name="header">
                            <h:outputText value="Valor"/>
                        </f:facet>
                        <h:outputText value="R$ #{parcela.valorParcelaNumerico}"/>
                    </rich:column>

                    <rich:column>
                        <f:facet name="header">
                            <h:outputText value="Situação"/>
                        </f:facet>
                        <h:outputText value="#{parcela.situacao_Apresentar}"/>
                    </rich:column>

                     <rich:column>
                        <f:facet name="header">
                            <h:outputText value="Em Remessa"/>
                        </f:facet>
                         <h:outputText rendered="#{parcela.emRemessaAindaNaoProcessada}" value="Sim"/>
                         <h:outputText rendered="#{!parcela.emRemessaAindaNaoProcessada}" value="Não"/>
                    </rich:column>
                </rich:dataTable>
            </rich:panel>
        </h:panelGroup>

        <a4j:form id="formOperacoes">
            <h:panelGroup id="botoes">
                <a4j:commandButton rendered="#{GestaoTransacoesControle.transacaoVO.permiteRetransmitir}"
                                   id="btnRepetirTransacao" title="Repetir a transação para cobrança"
                                   value="Repetir Transação"
                                   onclick="if (!confirm('Se você continuar uma nova transação será criada nesta data e, se esta for aprovada a transação anterior será sinalizada como \"Descartada\". Deseja continuar?')){return false;}"
                                   reRender="panelDadosTransacao"
                                   oncomplete="#{GestaoTransacoesControle.mensagemNotificar}"
                                   action="#{GestaoTransacoesControle.repetirTransacao}"/>

                <a4j:commandButton rendered="#{GestaoTransacoesControle.transacaoVO.permiteConfirmar}"
                                   reRender="mensagemModal,panelBasico,botoes"
                                   id="btnConfirmarTransacao" title="Confirmar a transação para cobrança do Cartão"
                                   value="Confirmar Transação"
                                   oncomplete="#{GestaoTransacoesControle.mensagemNotificar}"
                                   action="#{GestaoTransacoesControle.confirmarTransacao}"/>

                <a4j:commandButton rendered="#{GestaoTransacoesControle.transacaoVO.permiteDescartar}"
                                   reRender="mensagemModal,panelBasico,botoes"
                                   id="btnDescartarTransacao" title="Descartar a transação pendente"
                                   value="Descartar Transação"
                                   oncomplete="#{GestaoTransacoesControle.mensagemNotificar}"
                                   action="#{GestaoTransacoesControle.descartarTransacao}"/>
            </h:panelGroup>
        </a4j:form>


        <h:panelGrid id="mensagemModal"
                     rendered="#{not empty GestaoTransacoesControle.mensagem || not empty GestaoTransacoesControle.mensagemDetalhada}"
                     columns="1" width="100%" styleClass="tabMensagens" style="margin:0 0 0 0;">
            <h:outputText id="msgOperacoes" styleClass="mensagem" value="#{GestaoTransacoesControle.mensagem}"/>
            <h:outputText id="msgOperacoesDet" styleClass="mensagemDetalhada" value="#{GestaoTransacoesControle.mensagemDetalhada}"/>
        </h:panelGrid>
    </rich:modalPanel>


<%--  MODAL DE CANCELAR TRANSACAO  --%>
    <rich:modalPanel id="modalCancelarTransacao" domElementAttachment="parent"
                     autosized="true" shadowOpacity="false" width="500"
                     styleClass="novaModal">

        <f:facet name="header">
            <h:panelGroup>
                <h:outputText value="Cancelar Transação"/>
            </h:panelGroup>
        </f:facet>
        <f:facet name="controls">
            <h:panelGroup>
                <h:outputText
                        styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign "
                        id="hidelinkModalCancelarTransacao"/>
                <rich:componentControl for="modalCancelarTransacao" attachTo="hidelinkModalCancelarTransacao"
                                       operation="hide" event="onclick">
                </rich:componentControl>
            </h:panelGroup>
        </f:facet>

        <a4j:form id="formModalCancelarTransacao" ajaxSubmit="true">
            <h:panelGroup id="panelModalCancelarTransacao"
                    layout="block" style="padding: 20px">

                <h:panelGroup layout="block" id="panelTitleBloqueio"
                              style="padding-top: 10px;padding-bottom: 10px;">
                    <h:outputText value="Ao cancelar essa transação, o recibo também será estornado e a parcela do aluno ficará em aberto.
                    Caso haja nota fiscal emitida, você deve realizar o cancelamento manualmente da nota fiscal."
                                  styleClass="texto-size-20 cinza"/>
                </h:panelGroup>

                <h:panelGroup layout="block" id="panelBtnModalCancelarTransacao"
                              style="text-align: center; padding: 25px 0 20px 0;">
                    <a4j:commandLink id="confirmarCancelarTransacao"
                                     reRender="form:tabelaTransacoes, form:tblTransacoes"
                                     value="Confirmar"
                                     action="#{GestaoTransacoesControle.cancelarTransacao}"
                                     oncomplete="#{GestaoTransacoesControle.mensagemNotificar};#{GestaoTransacoesControle.onComplete}"
                                     styleClass="botaoPrimario texto-size-16"/>
                </h:panelGroup>
            </h:panelGroup>
        </a4j:form>
    </rich:modalPanel>

    <rich:modalPanel domElementAttachment="parent" id="modalEnviarEmailTransacao" styleClass="novaModal" width="500"
                     autosized="true"
                     shadowOpacity="true">
        <f:facet name="header">
            <h:panelGroup>
                <h:outputText value="Enviar comprovante cancelamento"/>
            </h:panelGroup>
        </f:facet>
        <f:facet name="controls">
            <h:panelGroup>
                <h:outputText
                        styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign btnSelecionarModalidade"
                        id="hideLinkModalEnviarEmailTransacao"/>
                <rich:componentControl for="modalEnviarEmailTransacao" attachTo="hideLinkModalEnviarEmailTransacao"
                                       operation="hide"
                                       event="onclick"/>
            </h:panelGroup>
        </f:facet>
        <a4j:form id="formEmail">
            <h:panelGrid columns="1" width="100%" columnClasses="colunaCentralizada">
                <h:panelGroup id="panelEnviarEmailTransacao">
                    <div>
                        <h:inputText id="emailEnviar" maxlength="50"
                                     value="#{GestaoTransacoesControle.emailEnviar}"
                                     style="width: 100%"/>
                    </div>

                    <h:panelGroup layout="block" style="text-align: center;" styleClass="margin-box">
                        <a4j:commandLink id="btnEnviarEmail"
                                         value="Enviar"
                                         styleClass="botoes nvoBt"
                                         action="#{GestaoTransacoesControle.enviarEmailComprovanteCancelamento}"
                                         oncomplete="#{GestaoTransacoesControle.mensagemNotificar};#{GestaoTransacoesControle.onComplete}"
                                         reRender="form:panelENotas"/>
                    </h:panelGroup>
                </h:panelGroup>
            </h:panelGrid>
        </a4j:form>
    </rich:modalPanel>

    <%--  MODAL DE ATUALIZAR TRANSACAO  --%>
    <rich:modalPanel id="modalSincronizarTransacao" domElementAttachment="parent"
                     autosized="true" shadowOpacity="false" width="500"
                     styleClass="novaModal">

        <f:facet name="header">
            <h:panelGroup>
                <h:outputText value="Sincronizar Transação"/>
            </h:panelGroup>
        </f:facet>
        <f:facet name="controls">
            <h:panelGroup>
                <h:outputText
                        styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign "
                        id="hidelinkModalSincronizarTransacao"/>
                <rich:componentControl for="modalSincronizarTransacao" attachTo="hidelinkModalSincronizarTransacao"
                                       operation="hide" event="onclick">
                </rich:componentControl>
            </h:panelGroup>
        </f:facet>

        <a4j:form id="formModalSincronizarTransacao" ajaxSubmit="true">
            <h:panelGroup id="panelModalSincronizarTransacao"
                          layout="block" style="padding: 20px">

                <h:panelGroup layout="block" id="panelTitleBloqueioSincronizar"
                              style="padding-top: 10px;padding-bottom: 10px;">
                    <h:outputText value="Ao sincronizar a transação, o sistema irá verificar a situação da transação na #{GestaoTransacoesControle.transacaoVO.tipo.descricao}, caso ela
                                         tenha sofrido alguma alteração será atualizado no ZillyonWeb."
                                  styleClass="texto-size-20 cinza"/>
                </h:panelGroup>

                <h:panelGroup layout="block" id="panelBtnModalSincronizarTransacao"
                              style="text-align: center; padding: 25px 0 20px 0;">
                    <a4j:commandLink id="confirmarSincronizarTransacao"
                                     reRender="form:tabelaTransacoes, form:tblTransacoes"
                                     value="Confirmar"
                                     action="#{GestaoTransacoesControle.sincronizarTransacao}"
                                     oncomplete="#{GestaoTransacoesControle.mensagemNotificar};#{GestaoTransacoesControle.onComplete}"
                                     styleClass="botaoPrimario texto-size-16"/>
                </h:panelGroup>
            </h:panelGroup>
        </a4j:form>
    </rich:modalPanel>

    <%--  MODAL DE REALIZAR RETENTATIVA TRANSACAO  --%>
    <rich:modalPanel id="modalRetentativaTransacao" domElementAttachment="parent"
                     autosized="true" shadowOpacity="false" width="500"
                     styleClass="novaModal">

        <f:facet name="header">
            <h:panelGroup>
                <h:outputText value="Retentativa Transação"/>
            </h:panelGroup>
        </f:facet>
        <f:facet name="controls">
            <h:panelGroup>
                <h:outputText
                        styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign "
                        id="hidelinkModalRetentativaTransacao"/>
                <rich:componentControl for="modalRetentativaTransacao" attachTo="hidelinkModalRetentativaTransacao"
                                       operation="hide" event="onclick">
                </rich:componentControl>
            </h:panelGroup>
        </f:facet>

        <a4j:form id="formModalRetentativaTransacao" ajaxSubmit="true">
            <h:panelGroup id="panelModalRetentativaTransacao"
                          layout="block" style="padding: 20px">

                <h:panelGroup layout="block" id="panelTitleRetentativaTransacao"
                              style="padding-top: 10px;padding-bottom: 10px;">
                    <h:outputText value="Ao realizar a retentativa da transação o sistema irá realizar uma nova tentativa imediata de cobrança das parcelas em aberto da transação selecionada."
                                  styleClass="texto-size-20 cinza"/>
                </h:panelGroup>

                <h:panelGroup layout="block"
                              style="padding-top: 10px;padding-bottom: 10px;">
<%--                    <h:selectOneMenu styleClass="form"--%>
<%--                                     value="#{GestaoTransacoesControle.convenioCobrancaRetentativa}">--%>
<%--                        <f:selectItems--%>
<%--                                value="#{GestaoTransacoesControle.selectItemListaConvenioCobrancaRetentativa}"/>--%>
<%--                    </h:selectOneMenu>--%>

                    <div>
                        <h:outputText styleClass="tituloCampos"
                                      style="vertical-align:middle;"
                                      value="Selecione o convênio de cobrança que será realizado a nova tentativa:"/>
                    </div>
                    <div class="cb-container"
                         style="font-size: 12px !important; margin-top: 10px; display: grid;">
                        <h:selectOneMenu styleClass="form"
                                         value="#{GestaoTransacoesControle.convenioCobrancaRetentativa}">
                            <f:selectItems
                                    value="#{GestaoTransacoesControle.selectItemListaConvenioCobrancaRetentativa}"/>
                        </h:selectOneMenu>
                    </div>
                </h:panelGroup>

                <h:panelGroup layout="block" id="panelBtnModalRetentativaTransacao"
                              style="text-align: center; padding: 25px 0 20px 0;">
                    <a4j:commandLink id="confirmarRetentativaTransacao"
                                     reRender="form:tabelaTransacoes, form:tblTransacoes"
                                     value="Confirmar"
                                     action="#{GestaoTransacoesControle.retentativaTransacao}"
                                     oncomplete="#{GestaoTransacoesControle.mensagemNotificar};#{GestaoTransacoesControle.onComplete}"
                                     styleClass="botaoPrimario texto-size-16"/>
                </h:panelGroup>
            </h:panelGroup>
        </a4j:form>
    </rich:modalPanel>

    <%--  MODAL INFORMACAO  PACTOPAY--%>
    <rich:modalPanel id="modalInformacaoPactoPay" domElementAttachment="parent"
                     showWhenRendered="#{LoginControle.apresentarModuloPactoPay && LoginControle.permissaoAcessoMenuVO.moduloPactoPay
                     && GestaoTransacoesControle.exibirModalInformativo}"
                     autosized="true" shadowOpacity="false" width="650"
                     styleClass="novaModal">

        <f:facet name="header">
            <h:panelGroup>
                <h:outputText value="Informativo"/>
            </h:panelGroup>
        </f:facet>
        <f:facet name="controls">
            <h:panelGroup>
                <h:outputText
                        styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign "
                        id="hidelinkModal"/>
                <rich:componentControl for="modalInformacaoPactoPay"
                                       attachTo="hidelinkModal"
                                       operation="hide" event="onclick">
                </rich:componentControl>
            </h:panelGroup>
        </f:facet>

        <a4j:form id="formModalInformacaoPactoPay" ajaxSubmit="true">
            <h:panelGroup id="panelModalInformacaoPactoPay"
                          layout="block" style="padding: 20px">

                <h:panelGroup layout="block"
                              style="text-align: center; font-weight: bold; font-size: 18px;">
                    <h:outputText value="Atenção usuário: A partir de 06/01/2023 esta tela será descontinuada!"/>
                </h:panelGroup>

                <h:panelGroup layout="block"
                              styleClass="texto-size-14-real texto-cor-cinza texto-font"
                              style="margin-top: 20px;">
                    <h:outputText value="Um novo relatório ainda mais completo para acompanhar suas transações de cartão de crédito está disponível no PactoPay, nossa ferramenta exclusiva para a gestão de suas cobranças."/>
                </h:panelGroup>

                <h:panelGroup layout="block"
                              style="text-align: center; padding: 25px 0 20px 0;">
                    <a4j:commandLink action="#{GestaoTransacoesControle.abrirPactoPayTelaCartao}"
                                  styleClass="tituloModulos" id="abrirPactoPay" oncomplete="#{GestaoTransacoesControle.msgAlert}">
                        <h:graphicImage url="/imagens_flat/acessar_pactopay.png" width="27%"/>
                    </a4j:commandLink>
                </h:panelGroup>
            </h:panelGroup>
        </a4j:form>
    </rich:modalPanel>

</a4j:outputPanel>
