<%-- 
    Document   : include_itens_remessa
    Created on : 13/12/2012, 19:11:15
    Author     : waller
--%>
<%@include file="/includes/imports.jsp" %>
<rich:dataTable rowClasses="linhaImpar,linhaPar" width="100%"
                id="tblItensRemessaNovo"
                rows="#{fn:contains(pagina, 'impressao') ? 5000 : 10}"
                rowKeyVar="status"
                value="#{GestaoRemessasControle.remessaVO.listaItens}"
                var="item">
    <f:facet name="header">
        <h:outputText value="Itens de Remessa de Cobran�a"/>
    </f:facet>
    <%@include file="../../pages/ce/includes/include_contador_richtable.jsp" %>

    <rich:column sortBy="#{item.codigo}">
        <f:facet name="header">
            <h:outputText value="Cod."/>
        </f:facet>
        <h:outputText title="C�digo Item Remessa" value="#{item.codigo}"/>
    </rich:column>

    <rich:column rendered="#{fn:contains(pagina, 'estorno') || fn:contains(pagina, 'tela6')}"
                 sortBy="#{item.remessa.identificador}">
        <f:facet name="header">
            <h:outputText value="Remessa"/>
        </f:facet>
        <h:outputText title="Remessa" value="#{item.remessa.identificador}"/>
    </rich:column>

    <rich:column rendered="#{!(fn:contains(pagina, 'estorno') || fn:contains(pagina, 'tela6'))}"
                 sortBy="#{item.pessoa.nome}">
        <f:facet name="header">
            <h:outputText value="Pessoa Pagador"/>
        </f:facet>
        <div title="Clique para abrir o Cadastro de Cliente" style="overflow:hidden;width:250px">
            <% if (request.getRequestURI().contains("impressao")) {%>
            <a4j:commandLink value="#{item.pessoa.nome}"
                             oncomplete="#{GestaoRemessasControle.onCompleteDetalhes}"
                             actionListener="#{GestaoRemessasControle.abrirTelaClienteColaborador}">
                <f:attribute name="pessoa" value="#{item.pessoa}"/>
                <f:attribute name="impressao" value="true"/>
            </a4j:commandLink>
            <%} else {%>
            <a4j:commandLink value="#{item.pessoa.nome}"
                             oncomplete="#{GestaoRemessasControle.onCompleteDetalhes}"
                             actionListener="#{GestaoRemessasControle.abrirTelaClienteColaborador}">
                <f:attribute name="pessoa" value="#{item.pessoa}"/>
                <f:attribute name="impressao" value="false"/>
            </a4j:commandLink>
            <%}%>
        </div>
    </rich:column>

    <rich:column sortBy="#{item.valorCartaoMascaradoOuAgenciaConta}">
        <f:facet name="header">
            <h:outputText value="Nr� Cart�o"/>
        </f:facet>
        <h:outputText style="cursor:pointer;" value="#{item.valorCartaoMascaradoOuAgenciaConta}"/>
    </rich:column>

    <rich:column sortBy="#{item.codigoStatus}" styleClass="col-text-align-center"
                 headerClass="col-text-align-center">
        <f:facet name="header">
            <h:outputText value="Status"/>
        </f:facet>
        <h:outputText style="cursor:help;"
                      styleClass="tooltipster"
                      value="#{item.codigoStatus}" title="#{item.descricaoStatus}"/>
    </rich:column>

    <rich:column sortBy="#{item.autorizacao}">
        <f:facet name="header">
            <h:outputText title="Autoriza��o" value="Aut."/>
        </f:facet>
        <h:outputText title="C�digo Autoriza��o Administradora" value="#{item.autorizacao}"/>
    </rich:column>

    <rich:column sortBy="#{item.movPagamento.codigo}">
        <f:facet name="header">
            <h:outputText title="MovPagamento" value="MPg"/>
        </f:facet>
        <h:outputText
                style="color:blue;font-weight:bold;"
                title="C�digo do Movimento de Pagamento"
                rendered="#{item.movPagamento.codigo != 0}"
                value="Pgt. #{item.movPagamento.codigo}"/>

        <h:outputText
                style="color:red;font-weight:bold;"
                title="Outra Remessa ou Outra Forma de Pagamento pagou as parcelas"
                rendered="#{(item.movPagamento.codigo == 0) && (item.todasParcelasPagas)}"
                value="Pgt. OUTRO"/>

        <h:outputText
                style="color:green;font-weight:bold;"
                title="Outra Remessa ou Outra Forma de Pagamento pagou uma das parcelas"
                rendered="#{(item.movPagamento.codigo == 0) && (!item.todasParcelasPagas && item.temParcelasPagas)}"
                value="Pgt. OUTRO PARCIAL (Pelo menos uma das parcelas foi paga)"/>
    </rich:column>

    <rich:column sortBy="#{item.valorItemRemessa}">
        <f:facet name="header">
            <h:outputText value="Valor"/>
        </f:facet>
        <h:outputText title="Valor total da cobran�a"
                      styleClass="tooltipster"
                      value="R$ #{item.valorItemRemessaNumerico}"/>
    </rich:column>

    <rich:column>
        <f:facet name="header">
            <h:outputText value="Parcelas"/>
        </f:facet>

        <rich:dataTable rowClasses="linhaImpar,linhaPar" width="100%" rowKeyVar="statusParc"
                        value="#{item.movParcelas}" var="parcela">

            <rich:column sortBy="#{parcela.movParcelaVO.contrato.codigo}">
                <f:facet name="header">
                    <h:outputText value="Parcela"/>
                </f:facet>
                <h:outputText styleClass="tooltipster"
                              title="<b>Contrato:</b> #{parcela.movParcelaVO.contrato_Apresentar}<br/><b>Pessoa:</b> #{parcela.movParcelaVO.pessoa.nome}"
                              value="#{parcela.movParcelaVO.codigo}"/>
            </rich:column>

            <rich:column sortBy="#{parcela.movParcelaVO.descricao}">
                <f:facet name="header">
                    <h:outputText value="Descri��o"/>
                </f:facet>
                <h:outputText rendered="#{!empty parcela.movParcelaVO.descricao}"
                              styleClass="tooltipster"
                              style="vertical-align:middle;"
                              title="<b>Pessoa:</b> #{parcela.movParcelaVO.pessoa.nome}"
                              value="#{!empty parcela.movParcelaVO.descricao ? parcela.movParcelaVO.descricao : '***Estornada***'}"/>
                <a4j:commandLink rendered="#{empty parcela.movParcelaVO.descricao}"
                                 value="Log Estorno"
                                 styleClass="tooltipster"
                                 onclick="alert('#{!empty parcela.movParcelaVO.descricao ? parcela.movParcelaVO.descricao : item.logEstorno}');"/>
            </rich:column>

            <rich:column sortBy="#{parcela.movParcelaVO.situacao_Apresentar}">
                <f:facet name="header">
                    <h:outputText title="Situa��o Atual da Parcela"
                                  styleClass="tooltipster"
                                  value="Situa��o"/>
                </f:facet>
                <h:outputText value="#{parcela.movParcelaVO.situacao_Apresentar}"/>
            </rich:column>

            <rich:column sortBy="#{parcela.valorParcela}">
                <f:facet name="header">
                    <h:outputText value="Valor"
                                  styleClass="tooltipster"
                                  title="Valor da Parcela"/>
                </f:facet>
                <h:outputText value="#{parcela.valorOriginal_Apresentar}"/>
            </rich:column>

            <rich:column sortBy="#{parcela.valorMulta}">
                <f:facet name="header">
                    <h:outputText value="Multa"
                                  styleClass="tooltipster"
                                  title="Valor da Multa"/>
                </f:facet>
                <h:outputText value="#{parcela.valorMulta_Apresentar}"/>
            </rich:column>

            <rich:column sortBy="#{parcela.valorJuros}">
                <f:facet name="header">
                    <h:outputText value="Juros"
                                  styleClass="tooltipster"
                                  title="Valor dos Juros"/>
                </f:facet>
                <h:outputText value="#{parcela.valorJuros_Apresentar}"/>
            </rich:column>
        </rich:dataTable>
    </rich:column>
</rich:dataTable>
<rich:datascroller reRender="tblItensRemessaNovo"
                   maxPages="30" for="tblItensRemessaNovo"/>
