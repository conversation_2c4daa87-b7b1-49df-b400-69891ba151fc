<%@include file="/includes/imports.jsp" %>
<rich:modalPanel id="panelGerarRecibo" width="480" minHeight="200" styleClass="novaModal" autosized="true">
    <f:facet name="header">
        <h:panelGroup>
            <h:outputText value="Gerar Recibo"/>
        </h:panelGroup>
    </f:facet>
    <f:facet name="controls">
        <h:panelGroup>
            <span class="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign btnSelecionarModalidade"
                  id="hidelinkGerarRecibo"
                  onclick="Richfaces.hideModalPanel('panelGerarRecibo');"/>
        </h:panelGroup>
    </f:facet>

    <h:form id="formPanelGerarRecibo" style="height: 100%">
        <h:panelGroup layout="block" styleClass="paginaFontResponsiva">
            <h:panelGroup layout="block"
                          style="display: inline-block;padding-top: 15px; padding-left: 15px;">
                <h:panelGrid columns="2" width="114%">
                    <h:outputText value="C�d. de Autoriza��o: "/>
                    <h:inputText id="codAutorizacao" size="15" maxlength="15" onblur="blurinput(this);"
                                 onfocus="focusinput(this);" value="#{GestaoRemessasControle.codAutorizacao}"
                                 style="height: 25px;"/>
                </h:panelGrid>
            </h:panelGroup>
            <h:panelGroup layout="block"
                          style="display: inline-block;padding-top: 15px; padding-left: 15px;">
                <h:panelGrid columns="2" width="105%">
                    <h:outputText value="Data de compensa��o: "/>
                    <h:panelGroup styleClass="dateTimeCustom" style="font-size: 11px !important;">
                        <rich:calendar id="dataInicio"
                                       value="#{GestaoRemessasControle.dataCompensacao}"
                                       locale="#{SuperControle.localeDefault}"
                                       inputSize="10"
                                       inputClass="form"
                                       oninputblur="blurinput(this);"
                                       buttonIcon="/imagens_flat/calendar-button.svg"
                                       oninputfocus="focusinput(this);"
                                       oninputchange="return validar_Data(this.id);"
                                       datePattern="dd/MM/yyyy"
                                       enableManualInput="true"
                                       zindex="2"
                                       showWeeksBar="false">
                            <f:convertDateTime pattern="dd/MM/yyyy"
                                               locale="#{SuperControle.localeDefault}"
                                               timeZone="#{SuperControle.timeZoneDefault}"/>
                        </rich:calendar>
                    </h:panelGroup>
                </h:panelGrid>
            </h:panelGroup>
        </h:panelGroup>
        <h:outputText id="mensagemErro" style="display: inline-block;padding-top: 15px; padding-left: 15px;color: red" value="#{GestaoRemessasControle.msgErro}"/>
        <br>
        <h:panelGroup layout="block" styleClass="container-botoes" id="blocoBotaoAtualizar">
            <%--<a4j:commandLink reRender="panelRecarregar, panelGerarRecibo, containerListaRemessaEstorno"--%>
            <a4j:commandLink reRender="mensagemErro, modalSucessoRecibo"
                             action="#{GestaoRemessasControle.gerarRecibo}"
                             styleClass="botaoPrimario texto-size-16"
                             oncomplete="#{GestaoRemessasControle.mensagemNotificar}; #{GestaoRemessasControle.onCompleteDetalhes}">
                <span class="texto-size-16 texto-font  texto-cor-branco">Salvar</span>
                <f:attribute name="codigoItem" value="#{item.codigo}"/>
            </a4j:commandLink>
        </h:panelGroup>
    </h:form>
</rich:modalPanel>


<rich:modalPanel id="modalSucessoRecibo" width="480" minHeight="200" styleClass="novaModal" autosized="true">
    <f:facet name="header">
        <h:panelGroup>
            <h:outputText value="Sucesso!"/>
        </h:panelGroup>
    </f:facet>

    <h:form id="formPanelSucessoGerarRecibo" style="height: 100%">

        <h:panelGroup layout="block"
                      rendered="#{GestaoRemessasControle.sucessoReciboItemRemessa}"
                      id="sucesso"
                      style="display: inline-block;padding-top: 15px; padding-left: 15px;">
            <h:panelGrid columns="2" width="114%">
                <h:outputText value="Recibo gerado com Sucesso!"/>
            </h:panelGrid>
        </h:panelGroup>
        <h:panelGroup layout="block" styleClass="container-botoes" id="bloco">
            <a4j:commandLink action="#{GestaoRemessasControle.redirecionarTelaAluno}"
                             styleClass="botaoPrimario texto-size-16">
                <span class="texto-size-16 texto-font  texto-cor-branco">Fechar</span>
            </a4j:commandLink>
        </h:panelGroup>
    </h:form>
</rich:modalPanel>

