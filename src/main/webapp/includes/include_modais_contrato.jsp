<%--
  Created by IntelliJ IDEA.
  User: Rafael
  Date: 09/06/2016
  Time: 13:51
  To change this template use File | Settings | File Templates.
--%>
<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@include file="imports.jsp" %>
<script>
    function adicionarPlaceHolderAutorizacao() {
        if (document.getElementById("formCadastroAutorizacaoCobranca:validade") != null) {
            document.getElementById("formCadastroAutorizacaoCobranca:validade").setAttribute("placeholder", "MM/AA");
        }
        if (document.getElementById("formCadastroAutorizacaoCobranca:cvv") != null) {
            document.getElementById("formCadastroAutorizacaoCobranca:cvv").setAttribute("placeholder", "3 ou 4 digitos");
        }

    }

    function mascaraCartaoCreditoNovoContrato() {
        try {
            var v = document.getElementById('formCadastroAutorizacaoCobranca:nrCartao').value;
            v = v.replace(/^(\d{4})(\d)/g, "$1 $2");
            v = v.replace(/^(\d{4})\s(\d{4})(\d)/g, "$1 $2 $3");
            v = v.replace(/^(\d{4})\s(\d{4})\s(\d{4})(\d)/g, "$1 $2 $3 $4");
            document.getElementById('formCadastroAutorizacaoCobranca:nrCartao').value = v;
        } catch (e) {
            console.log("ERRO mascaraCartaoCredito: " + e);
        }
    }

</script>
<style>
    .formaPagamento {
        border: 1px solid #E5E5E5;
        border-radius: 3px;
        margin-left: 8px;
        margin-right: 8px;
        margin-top: 17px;
        margin-bottom: 14px;
        padding: 14px;
        text-decoration-color: #9d9d9d;
    }

    .formaPagamentoSelecionada {
        border: 1px solid #29AAE2;
        border-radius: 3px;
        margin-left: 8px;
        margin-right: 8px;
        margin-top: 17px;
        margin-bottom: 14px;
        padding: 14px;
    }

    .circleQuestion {
        width: 21px;
        height: 21px;
        border-radius: 100%;
        background: #D3D5D7;
        border: 1px solid #D3D5D7;
        color: #0078D0;
        display: flex;
        margin: 8px;
    }

    .group {
        display: flex;
    }

    .sub-group {
        display: -webkit-inline-box;
        margin: 11px;
    }

    .label {
        display: inline-table;
    }

    .grid {
        background: #FAFAFA;
    }

    .header {
        font-size: 16px;
        line-height: 20px;
        font-family: Arial;
        color: #383B3E;
        margin-left: 10px;
    }

    .cartao {
        border: 1px solid #D3D5D7;
        box-sizing: border-box;
        border-radius: 10px;
        width: 240px;
        height: 160px;
        margin: 24px 0px 0px 14px;
    }

    .cartao .titleWhite {
        font-family: Arial;
        font-style: normal;
        font-weight: bold;
        font-size: 12px;
        line-height: 18px;
        color: #E7E7E7;
    }

    .cartao .titleGrey {
        font-family: Arial;
        font-style: normal;
        font-weight: bold;
        font-size: 12px;
        line-height: 18px;
        color: #6F747B;
    }

    .white {
        background: #FFFFFF;
        border-radius: 10px;
    }

    .white .rodape {
        background: #FFFFFF; border-radius: 0px 0px 10px 10px; width: 100%; height: 8px; margin-top: 31px;
    }

    .gradient {
        background: radial-gradient(98.33% 251.73% at 96.88% 64%, #4AB5E3 2.89%, #1B9FFD 45.13%, #0078D0 72.63%, #005A93 100%);
    }

    .gradient .rodape {
        background: #013E6F; border-radius: 0px 0px 10px 10px; width: 100%; height: 8px; margin-top: 31px;
    }

    .cartaoRodaPe {
        background: #013E6F !important;
        border-radius: 0px 0px 10px 10px;
    }

    .title {
        font-family: Arial;
        font-style: normal;
        font-weight: normal;
        font-size: 14px;
        line-height: 16px;
        color: #6F747B;
    }

    .circleQuestion {
        width: 21px;
        height: 21px;
        border-radius: 100%;
        background: #D3D5D7;
        border: 1px solid #D3D5D7;
        color: #0078D0;
        display: flex;
        margin: 8px;
    }

    .button-produtos {
        position: absolute;
        height: 32px;
        width: 89%;
        border: 1px solid #E5E5E5;
        background-color: #FFFFFF;
        box-sizing: border-box;
        border-radius: 3px;
        padding-top: 6px;
        padding-left: 10px;
        color: #29abe2;
        font-size: 14px;
        text-decoration: none;
    }

    .button-produtos:hover {
        color: #29abe2;
        text-decoration: none;
    }

    .button-produtos::after {
        font-family: 'FontAwesome';
        font-size: calc((100vw / 100) * 1.1);
        position: absolute;
        content: "\f0d7";
        right: 0;
        top: 0;
        line-height: 2.0em;
        z-index: 0;
        padding-right: 10px;
        color: #777;
    }
</style>
<rich:modalPanel id="panelProdutoSugerido" autosized="true" styleClass="novaModal" shadowOpacity="true" width="560"
                 height="250">
    <f:facet name="header">
        <h:panelGroup>
            <h:outputText value="Escolher Produto Sugerido"></h:outputText>
        </h:panelGroup>
    </f:facet>
    <f:facet name="controls">
        <h:panelGroup>
            <h:outputText
                    styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign btnSelecionarModalidade"
                    id="hidelink2"/>
            <rich:componentControl for="panelProdutoSugerido" attachTo="hidelink2" operation="hide"
                                   event="onclick"/>
        </h:panelGroup>
    </f:facet>
    <a4j:form id="formProdutoSugerido" ajaxSubmit="true">
        <h:panelGroup layout="block" style="width:100%;text-align: center;">
            <h:panelGrid columns="1" width="100%" styleClass="tabFormSubordinada">
                <rich:dataTable id="produtoSugeridoVO" width="100%" rows="10" styleClass="tabelaSimplesCustom"
                                value="#{ContratoControle.contratoModalidadeVO.modalidade.produtoSugeridoVOs}"
                                var="produtoSugerido">
                    <rich:column styleClass="col-text-align-left" headerClass="col-text-align-left">
                        <f:facet name="header">
                            <h:outputText styleClass="texto-cor-cinza texto-size-14 texto-font texto-bold"
                                          value="DESCRIÇÃO"/>
                        </f:facet>
                        <h:panelGroup layout="block" styleClass="checkbox-fa">
                            <a4j:commandLink styleClass="linkPadrao"
                                             action="#{ContratoControle.marcarProdutoSugerido}"
                                             rendered="#{!produtoSugerido.obrigatorio}"
                                             style="margin-left: 0px;"
                                             reRender="dados,msgPanelProdutoSugerido,produtoSugeridoVO">
                                <h:outputText
                                        styleClass="#{produtoSugerido.produtoSugeridoEscolhida ? 'fa-icon-check' : 'fa-icon-check-empty'} texto-cor-cinza texto-size-16 texto-font"
                                        value="#{produtoSugerido.produto.descricao}"/>
                            </a4j:commandLink>

                            <h:outputText rendered="#{produtoSugerido.obrigatorio}"
                                          styleClass="#{produtoSugerido.produtoSugeridoEscolhida ? 'fa-icon-check' : 'fa-icon-check-empty'} texto-cor-cinza texto-size-16 texto-font"
                                          value="#{produtoSugerido.produto.descricao}"/>

                        </h:panelGroup>
                    </rich:column>
                    <rich:column width="30px" styleClass="col-text-align-right" headerClass="col-text-align-right">
                        <f:facet name="header">
                            <h:outputText style="float:right;"
                                          styleClass="texto-cor-cinza texto-size-14 texto-font texto-bold"
                                          value="VALOR"/>
                        </f:facet>
                        <h:outputText styleClass="texto-cor-cinza texto-size-16 texto-font"
                                      value="#{produtoSugerido.produto.valorFinal}">
                            <f:converter converterId="FormatadorNumerico"/>
                        </h:outputText>
                    </rich:column>
                    <rich:column style="text-align: right;">
                        <f:facet name="header">
                            <h:outputText style="float: right;"
                                          styleClass="texto-cor-cinza texto-size-14 texto-font texto-bold"
                                          value="VALOR FINAL"/>
                        </f:facet>
                        <h:outputText id="valorComDescontoProdutoSugerido"
                                      styleClass="texto-size-16 texto-font texto-cor-cinza"
                                      value="#{produtoSugerido.produto.desconto.codigo > 0 ? produtoSugerido.produto.desconto.valorProdudoComDesconto : produtoSugerido.produto.valorFinal}">
                            <f:converter converterId="FormatadorNumerico"/>
                        </h:outputText>
                    </rich:column>
                    <rich:column style="width: 40px;padding: 12px;" styleClass="col-text-align-right"
                                 headerClass="col-text-align-right">
                        <f:facet name="header">
                            <h:outputText styleClass="texto-cor-cinza texto-size-14 texto-font texto-bold"
                                          value="DESC"/>
                        </f:facet>
                        <a4j:commandLink action="#{ContratoControle.consultarDescontoModalidadeProdutoSugerido}"
                                         reRender="formDescontoProdutoSugerido"
                                         styleClass="linkPadrao"
                                         rendered="#{produtoSugerido.produto.desconto.valor <= 0 }"
                                         oncomplete="Richfaces.showModalPanel('panelDescontoProdutoSugerido')">
                            <i title="Consutar Desconto"
                               class="tooltipster fa-icon-plus-sign texto-cor-azul texto-size-16"/>
                        </a4j:commandLink>

                        <a4j:commandLink action="#{ContratoControle.limparDescontoModalidadeProdutoSugerido}"
                                         rendered="#{produtoSugerido.produto.desconto.valor > 0 }"
                                         styleClass="linkPadrao"
                                         reRender="produtoSugeridoVO,#{ContratoControle.updateComponente.valoresContrato}">
                            <i title="Limpar Desconto"
                               class="tooltipster fa-icon-minus-sign texto-cor-vermelho texto-size-16"/>
                        </a4j:commandLink>
                    </rich:column>
                </rich:dataTable>
                <rich:datascroller align="center" for="formProdutoSugerido:produtoSugeridoVO"
                                   renderIfSinglePage="false" styleClass="scrollPureCustom"
                                   id="scRProdutoSugeridoVO"/>
            </h:panelGrid>
            <h:panelGroup layout="block" style="width: 100%;height: 80px;line-height: 100px;text-align: center">
                <h:outputText id="msgPanelProdutoSugerido" styleClass="mensagemDetalhada"
                              value="#{ContratoControle.mensagemDetalhada}"/>
                <a4j:commandLink id="salvar" action="#{ContratoControle.selecionarCondicaoPagamento}"
                                 oncomplete="Richfaces.hideModalPanel('panelProdutoSugerido')"
                                 value="#{msg_bt.btn_gravar}" title="#{msg.msg_gravar_dados}" accesskey="2"
                                 styleClass="botaoPrimario texto-font texto-size-16"
                                 reRender="plano,planoDescricao,planoDuracaoVO,planoModalidadeVO,planoModalidadeVezesSemanaVO,planoHorarioVO,planoComposicaoVO,composicaoMarcada ,
                                   modalidadeMarcada,produtoMarcada,planoProdutoMarcada,planoProdutoVO,botaoAdicionar,total, total1, subtotal,convenioDescontoMarcada ,valorMensalModalidade, duracao, horario, panelMesangem, panelProdutoParcela"/>
            </h:panelGroup>
        </h:panelGroup>
    </a4j:form>
</rich:modalPanel>
<rich:modalPanel id="panelDescontoProdutoSugerido" styleClass="novaModal" autosized="true" shadowOpacity="true"
                 width="560" height="250">
    <f:facet name="header">
        <h:panelGroup>
            <h:outputText value="Consulta de Desconto Para Produto"></h:outputText>
        </h:panelGroup>
    </f:facet>
    <f:facet name="controls">
        <h:panelGroup>
            <h:outputText
                    styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign btnSelecionarModalidade"
                    id="hidelink5"/>
            <rich:componentControl for="panelDescontoProdutoSugerido" attachTo="hidelink5" operation="hide"
                                   event="onclick"/>
        </h:panelGroup>
    </f:facet>

    <a4j:form id="formDescontoProdutoSugerido" ajaxSubmit="true">
        <h:panelGroup layout="block" style="width:100%;margin: 20px 0px 20px 0px">
            <rich:dataTable id="resultadoConsultaDescontoProdutoSugerido" width="100%"
                            styleClass="tabelaSimplesCustom" columnClasses="colunaAlinhamento"
                            value="#{ContratoControle.listaConsultaDesconto}" rows="5" var="desconto">
                <rich:column style="text-align: left">
                    <f:facet name="header">
                        <h:outputText styleClass="texto-font texto-size-14 texto-bold texto-cor-cinza"
                                      value="DESCRIÇÃO"/>
                    </f:facet>
                    <h:panelGroup>
                        <a4j:commandLink action="#{ContratoControle.selecionarDescontoModalidadeProdutoSugerido}"
                                         styleClass="texto-font texto-size-16 texto-cor-cinza linkPadrao"
                                         focus="descontoProdutoSugerido"
                                         reRender="formProdutoSugerido,produtoSugeridoVO,#{ContratoControle.updateComponente.valoresContrato}"
                                         oncomplete="Richfaces.hideModalPanel('panelDescontoProdutoSugerido')"
                                         value="#{desconto.descricaoMinusculo}"/>
                    </h:panelGroup>
                </rich:column>
                <rich:column style="text-align: cennter;">
                    <f:facet name="header">
                        <h:outputText styleClass="texto-font texto-size-14 texto-cor-cinza texto-bold"
                                      value="TIPO DESCONTO"/>
                    </f:facet>
                    <h:panelGroup>
                        <a4j:commandLink action="#{ContratoControle.selecionarDescontoModalidadeProdutoSugerido}"
                                         styleClass="texto-font texto-size-16 texto-cor-cinza linkPadrao"
                                         focus="descontoProdutoSugerido"
                                         reRender="formProdutoSugerido ,produtoSugeridoVO,#{ContratoControle.updateComponente.valoresContrato}"
                                         oncomplete="Richfaces.hideModalPanel('panelDescontoProdutoSugerido')"
                                         value="#{desconto.tipoProduto_Apresentar}"/>
                    </h:panelGroup>
                </rich:column>
                <rich:column style="text-align: right">
                    <f:facet name="header">
                        <h:outputText style="float: right;margin-right: 15px;"
                                      styleClass="texto-font texto-size-14 texto-cor-cinza texto-bold"
                                      value="VALOR"/>
                    </f:facet>
                    <h:panelGroup rendered="#{desconto.apresentarDescontoValor}">
                        <a4j:commandLink id="btnSelecionarDes"
                                         styleClass="linkPadrao"
                                         action="#{ContratoControle.selecionarDescontoModalidadeProdutoSugerido}"
                                         focus="descontoProdutoSugerido"
                                         reRender="formProdutoSugerido,produtoSugeridoVO,#{ContratoControle.updateComponente.valoresContrato}"
                                         oncomplete="Richfaces.hideModalPanel('panelDescontoProdutoSugerido')">
                            <h:panelGroup>
                                <h:outputText value="R$ "/>
                                <h:outputText styleClass="texto-cor-azul texto-font texto-size-16"
                                              value="#{desconto.valor}">
                                    <f:converter converterId="FormatadorNumerico"/>
                                </h:outputText>
                            </h:panelGroup>
                        </a4j:commandLink>
                    </h:panelGroup>
                    <h:panelGroup rendered="#{desconto.apresentarDescontoPorcentagem}">
                        <a4j:commandLink action="#{ContratoControle.selecionarDescontoModalidadeProdutoSugerido}"
                                         styleClass="linkPadrao"
                                         focus="descontoProdutoSugerido"
                                         reRender="formProdutoSugerido,produtoSugeridoVO,#{ContratoControle.updateComponente.valoresContrato}"
                                         oncomplete="Richfaces.hideModalPanel('panelDescontoProdutoSugerido')">
                            <h:panelGroup>
                                <h:outputText styleClass="texto-cor-azul texto-font texto-size-16"
                                              value="#{desconto.valor}">
                                    <f:converter converterId="FormatadorNumerico7Casa"/>
                                </h:outputText>
                                <h:outputText value=" %"/>
                            </h:panelGroup>
                        </a4j:commandLink>
                    </h:panelGroup>
                </rich:column>
                <rich:column style="text-align: center">
                    <a4j:commandLink action="#{ContratoControle.selecionarDescontoModalidadeProdutoSugerido}"
                                     focus="descontoProdutoSugerido"
                                     reRender="formProdutoSugerido, produtoSugeridoVO,#{ContratoControle.updateComponente.valoresContrato}"
                                     oncomplete="Richfaces.hideModalPanel('panelDescontoProdutoSugerido')"
                                     styleClass="linkPadrao">
                        <h:outputText title="#{msg.msg_selecionar_dados}"
                                      styleClass="tooltipster fa-icon-ok-sign texto-size-16 texto-cor-azul"/>
                    </a4j:commandLink>
                </rich:column>
            </rich:dataTable>
            <rich:datascroller align="center"
                               for="formDescontoProdutoSugerido:resultadoConsultaDescontoProdutoSugerido"
                               styleClass="scrollPureCustom" renderIfSinglePage="false" maxPages="10"
                               id="scResultadoDescontoProdutoSugerido"/>
        </h:panelGroup>
    </a4j:form>
</rich:modalPanel>
<rich:modalPanel id="panelSenhaDescontoConvenio" autosized="true" shadowOpacity="true" styleClass="novaModal"
                 width="450" height="350">
    <f:facet name="header">
        <h:panelGroup>
            <h:outputText value="Autorizar Desconto Convênio do Plano"></h:outputText>
        </h:panelGroup>
    </f:facet>
    <f:facet name="controls">
        <h:panelGroup>
            <a4j:form>
                <a4j:commandLink style="color: white;"
                                 styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign btnSelecionarModalidade"
                                 id="hidelinkSenhaDescontoConvenio"
                                 reRender="selectConvenioDesconto"
                                 action="#{ContratoControle.getHidePanelSenhaDescontoConvenio}"
                                 oncomplete="Richfaces.hideModalPanel('panelSenhaDescontoConvenio');">
                </a4j:commandLink>
            </a4j:form>
        </h:panelGroup>
    </f:facet>
    <a4j:form id="formSenhaDescontoConvenio" styleClass="paginaFontResponsiva">
        <h:panelGroup id="panelConfirmacaoSenhaDescontoConvenio" style="text-align: left;" styleClass="tabForm">
            <h:panelGroup layout="block" style="width: 20%;display: inline-block">
                <h:inputText style="opacity:0;height: 0;" size="5" maxlength="7"/>
                <h:inputSecret size="14" maxlength="64" style="opacity:0;height: 0;"/>
            </h:panelGroup>
            <h:panelGroup layout="block" style="width: 100%;margin: 10px;height: 41px;line-height: 2.5;">
                <h:panelGroup layout="block" style="width: 20%;display: inline-block">
                    <h:outputText styleClass="texto-size-16 texto-cor-cinza texto-font" value="USUÁRIO"/>
                </h:panelGroup>
                <h:panelGroup layout="block" style="width: 80%;display: inline-block">
                    <h:inputText autocomplete="off" id="username" size="14" maxlength="64"
                                 styleClass="inputTextClean"
                                 style="margin-left:8px"
                                 value="#{ContratoControle.contratoVO.responsavelAutorizacaoDescontoConvenio.username}"/>
                </h:panelGroup>
            </h:panelGroup>
            <h:panelGroup layout="block" style="width: 100%;margin: 10px;">
                <h:panelGroup layout="block" style="width: 20%;display: inline-block">
                    <h:outputText styleClass="texto-size-16 texto-cor-cinza texto-font" value="SENHA"/>
                </h:panelGroup>
                <h:panelGroup layout="block" style="width: 80%;display: inline-block">
                    <h:inputSecret autocomplete="off" id="senha" size="14" maxlength="64"
                                   style="margin-left:8px"
                                   styleClass="inputTextClean"
                                   onkeydown="validarEnter(event, 'formSenhaDesconto:autorizarDescConv');"
                                   value="#{ContratoControle.contratoVO.responsavelAutorizacaoDescontoConvenio.senha}"/>
                </h:panelGroup>
            </h:panelGroup>
            <h:panelGroup layout="block" style="width: 100%;margin: 10px;line-height: 1.5;height: 20px;">
                <h:outputText id="obs" styleClass="texto-size-14 texto-cor-cinza texto-font"
                              value="OBS: Autorização \"Desconto Convênio Plano\" necessária."/>
            </h:panelGroup>
        </h:panelGroup>
        <h:panelGroup layout="block" style="width: 100%;margin: 10px;line-height: 1.5;height: 20px;"
                      rendered="#{not empty ContratoControle.mensagemDetalhada}">
            <h:outputText id="mensagem" styleClass="mensagemDetalhada texto-size-14 texto-cor-cinza texto-font"
                          value="#{ContratoControle.mensagemDetalhada}"/>
        </h:panelGroup>
        <h:panelGroup layout="block"
                      style="margin-top: 40px; width: 100%;text-align: center;line-height: 4.2;height: 59px;">
            <a4j:commandLink id="autorizarDescConv" value="#{msg_bt.btn_gravar}"
                             styleClass="botaoPrimarioGrande texto-size-14 texto-cor-branco tooltipster"
                             reRender="selectConvenioDesconto, mensagem, panelMesangem,confNegociacao,dados"
                             oncomplete="#{ContratoControle.msgAlertAuxiliar}"
                             title="#{msg.msg_gravar_dados}"
                             action="#{ContratoControle.validarPermissaoDescontoConvenio}"/>
        </h:panelGroup>
    </a4j:form>
</rich:modalPanel>
<rich:modalPanel id="panelDescontoPlanoProdutoSugerido" autosized="true" styleClass="novaModal grande"
                 shadowOpacity="true" width="580" height="250">
    <f:facet name="header">
        <h:panelGroup>
            <h:outputText value="Desconto de Produto"></h:outputText>
        </h:panelGroup>
    </f:facet>

    <f:facet name="controls">
        <h:panelGroup>
            <h:outputText
                    styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign btnSelecionarModalidade"
                    id="hidelink4"/>
            <rich:componentControl for="panelDescontoPlanoProdutoSugerido" attachTo="hidelink4" operation="hide"
                                   event="onclick"/>
        </h:panelGroup>
    </f:facet>

    <h:form id="formDescontoPlanoProdutoSugerido" onkeypress="return event.keyCode != 13; ">
        <h:panelGroup layout="block" style="width: 100%">
            <rich:dataTable id="resultadoConsultaDescontoPlanoProdutoSugerido"
                            rendered="#{not empty ContratoControle.listaConsultaDesconto}"
                            styleClass="tabelaSimplesCustom" width="100%"
                            value="#{ContratoControle.listaConsultaDesconto}" rows="5" var="desconto">
                <rich:column style="text-align: left">
                    <f:facet name="header">
                        <h:outputText value="DESCONTO"
                                      styleClass="texto-font texto-cor-cinza texto-size-14 texto-bold"/>
                    </f:facet>
                    <h:panelGroup layout="block" style="width: 100%;text-align: left">
                        <a4j:commandLink action="#{ContratoControle.selecionarDescontoPlanoProdutoSugerido}"
                                         focus="descontoPlanoProdutoSugerido"
                                         styleClass="linkPadrao texto-size-16 texto-cor-cinza texto-font"
                                         reRender="planoProdutoVO, planoProdutoMarcada,#{ContratoControle.updateComponente.valoresContrato}, panelMesangem, mensagem, panelProdutoParcela,containerCondicaoPagamento"
                                         oncomplete="Richfaces.hideModalPanel('panelDescontoPlanoProdutoSugerido')"
                                         value="#{desconto.descricao}"/>
                    </h:panelGroup>
                </rich:column>
                <rich:column style="text-align: center;width:80px">
                    <f:facet name="header">
                        <h:outputText value="TIPO"
                                      styleClass="texto-font texto-cor-cinza texto-size-14 texto-bold"/>
                    </f:facet>
                    <h:panelGroup>
                        <a4j:commandLink action="#{ContratoControle.selecionarDescontoPlanoProdutoSugerido}"
                                         focus="descontoPlanoProdutoSugerido"
                                         styleClass="linkPadrao texto-size-16 texto-cor-cinza texto-font"
                                         reRender="planoProdutoVO, planoProdutoMarcada, #{ContratoControle.updateComponente.valoresContrato}, panelMesangem, mensagem, panelProdutoParcela,containerCondicaoPagamento"
                                         oncomplete="Richfaces.hideModalPanel('panelDescontoPlanoProdutoSugerido')"
                                         value="#{desconto.tipoDesconto.descricao}"/>
                    </h:panelGroup>
                </rich:column>
                <rich:column style="text-align: right;width: 80px">
                    <f:facet name="header">
                        <h:outputText value="VALOR"
                                      styleClass="texto-font texto-cor-cinza texto-size-14 texto-bold pull-right"/>
                    </f:facet>
                    <a4j:commandLink rendered="#{desconto.apresentarDescontoValor}"
                                     action="#{ContratoControle.selecionarDescontoPlanoProdutoSugerido}"
                                     focus="descontoPlanoProdutoSugerido"
                                     styleClass="linkPadrao texto-size-16 texto-cor-cinza texto-font"
                                     reRender="pnlProduto, planoProdutoMarcada,#{ContratoControle.updateComponente.valoresContrato}, panelMesangem, mensagem, panelProdutoParcela, containerCondicaoPagamento"
                                     oncomplete="Richfaces.hideModalPanel('panelDescontoPlanoProdutoSugerido')"
                                     value="#{desconto.valor_Apresentar}">
                    </a4j:commandLink>
                    <a4j:commandLink rendered="#{desconto.apresentarDescontoPorcentagem}"
                                     action="#{ContratoControle.selecionarDescontoPlanoProdutoSugerido}"
                                     focus="descontoPlanoProdutoSugerido"
                                     styleClass="linkPadrao texto-size-16 texto-cor-cinza texto-font"
                                     reRender="pnlProduto, planoProdutoMarcada, #{ContratoControle.updateComponente.valoresContrato}, panelMesangem, mensagem, panelProdutoParcela, containerCondicaoPagamento"
                                     oncomplete="Richfaces.hideModalPanel('panelDescontoPlanoProdutoSugerido')"
                                     value="#{desconto.valor}%">
                    </a4j:commandLink>
                </rich:column>
                <rich:column style="text-align: center;width: 150px;">
                    <a4j:commandLink id="btnAddDesconto"
                                     action="#{ContratoControle.selecionarDescontoPlanoProdutoSugerido}"
                                     focus="descontoPlanoProdutoSugerido"
                                     reRender="planoDuracaoVO,pnlProduto, planoProdutoMarcada,#{ContratoControle.updateComponente.valoresContrato}, panelMesangem, mensagem, panelProdutoParcela, containerCondicaoPagamento"
                                     oncomplete="Richfaces.hideModalPanel('panelDescontoPlanoProdutoSugerido')"
                                     styleClass="texto-size-14 texto-cor-azul linkPadrao"
                                     title="#{msg.msg_selecionar_dados}">
                        Aplicar Desconto <i class="fa-icon-arrow-right"></i>
                    </a4j:commandLink>
                </rich:column>
            </rich:dataTable>
            <rich:datascroller align="center" renderIfSinglePage="false" style="margin-top: 20px"
                               styleClass="scrollPureCustom"
                               for="formDescontoPlanoProdutoSugerido:resultadoConsultaDescontoPlanoProdutoSugerido"
                               maxPages="10"
                               id="scResultadoConsultaDescontoPlanoProdutoSugerido"/>
            <h:panelGroup layout="block" styleClass="containerDescontoManual">
                <h:panelGroup layout="block" style="text-align: center;margin: 14px;">

                    <h:inputText styleClass="inputTextClean inputValorDescontoManual"
                                 id="inputValorDescontoManual"
                                 style="width: calc(85% - 250px);margin-right: 10px"
                                 value="#{ContratoControle.descontoManualProduto}"
                                 onfocus="focusinput(this);if(this.value=='Adicione um desconto manual'){this.value=''}"
                                 onblur="if(this.value == ''){this.value='Adicione um desconto manual'}"
                                 onkeyup="return moeda(this);" maxlength="40">
                        <f:converter converterId="FormatadorNumerico"/>
                    </h:inputText>
                    <script>
                        verificarPlaceHolder(jQuery('.inputValorDescontoManual'), 'Adicione um desconto manual');
                    </script>
                    <a4j:commandLink value="Aplicar desconto manual" styleClass="botaoPrimario texto-size-16"
                                     action="#{ContratoControle.validaPermisaoDescontoManual}"
                                     oncomplete="#{ContratoControle.mensagemNotificar};#{ContratoControle.msgAlertAuxiliar}"
                                     reRender="formDescontoPlanoProdutoSugerido, pnlProduto, planoProdutoMarcada,#{ContratoControle.updateComponente.valoresContrato},panelAutorizacaoFuncionalidade,formDescontoPlanoProdutoSugerido:inputValorDescontoManual, panelMesangem, mensagem, panelProdutoParcela"/>
                </h:panelGroup>
            </h:panelGroup>
            <h:panelGrid id="mensagemDetalhadaDescontoManual" columns="1" width="100%">
                <h:panelGrid columns="1" width="100%">
                    <h:outputText styleClass="mensagemDetalhada" value="#{ContratoControle.mensagemDetalhada}"/>
                </h:panelGrid>
            </h:panelGrid>
        </h:panelGroup>
    </h:form>
</rich:modalPanel>
<jsp:include flush="true" page="/includes/include_consultarTurma.jsp"/>
<jsp:include flush="true" page="/includes/include_modal_cadastro_cliente.jsp"/>
<rich:modalPanel id="panelAutorizacaoCobranca" autosized="true" onshow="adicionarPlaceHolderAutorizacao()"
                 styleClass="novaModal noMargin" shadowOpacity="true" width="770" height="599">
    <f:facet name="header">
        <h:panelGroup>
            <h:outputText value="Autorização Cobrança"></h:outputText>
        </h:panelGroup>
    </f:facet>
    <f:facet name="controls">
        <h:panelGroup>
            <h:outputText
                    styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign btnSelecionarModalidade"
                    id="hidelinkAutorizacaoCobranca"/>
            <rich:componentControl for="panelAutorizacaoCobranca" attachTo="hidelinkAutorizacaoCobranca"
                                   operation="hide" event="onclick"/>
        </h:panelGroup>
    </f:facet>

    <a4j:form id="formCadastroAutorizacaoCobranca" ajaxSubmit="true" styleClass="paginaFontResponsiva"
              style="width: 100%;">
        <h:panelGroup id="panelAutorizacaoCobrancaCliente" layout="block" style="padding: 10px;">

            <h3 class="header">Dados da Cobrança</h3>
            <h:panelGroup layout="block" styleClass="group" style="margin-top: -20px; display: grid; grid-template-columns: 4fr 3.8fr 4fr">

                <%--TIPO DE COBRANÇA--%>
                <h:panelGroup layout="block" styleClass="sub-group">
                    <h:outputText styleClass="texto-font texto-size-16 texto-cor-cinza title"
                                  style="display: inline-table; margin-top: 10px; margin-bottom: 10px;"
                                  value="Tipo de cobrança"/>
                    </br>
                    <h:panelGroup layout="block" styleClass="cb-container">
                        <h:selectOneMenu onblur="blurinput(this);" onfocus="focusinput(this);"
                                         value="#{AutorizacaoCobrancaControle.autorizacao.tipoAutorizacao}">
                            <f:selectItems value="#{AutorizacaoCobrancaControle.tiposAutorizacao}"/>
                            <a4j:support event="onchange"
                                         action="#{AutorizacaoCobrancaControle.carregarConveniosTela}"
                                         oncomplete="#{AutorizacaoCobrancaControle.mensagemNotificar}"
                                         reRender="tabelaTipoAutorizacao, dadosCartao, panelAutorizacaoCobrancaCliente"/>
                        </h:selectOneMenu>
                    </h:panelGroup>
                </h:panelGroup>

                <%--CONVÊNIO DE COBRANÇA--%>
                <h:panelGroup layout="block" styleClass="sub-group"
                              rendered="#{AutorizacaoCobrancaControle.autorizacao.edit || AutorizacaoCobrancaControle.autorizacao.novoObj}">
                    <h:outputText styleClass="texto-font texto-size-16 texto-cor-cinza"
                                  style="display: inline-table; margin-top: 10px; margin-bottom: 10px;"
                                  value="Convênio de cobrança"/>
                    <br/>
                    <h:panelGroup layout="block" styleClass="cb-container">
                        <h:selectOneMenu onblur="blurinput(this);" onfocus="focusinput(this);"
                                         id="convAutorizacao"
                                         value="#{AutorizacaoCobrancaControle.autorizacao.convenio.codigo}">
                            <f:selectItems value="#{AutorizacaoCobrancaControle.convenios}"/>
                            <a4j:support event="onchange" action="#{AutorizacaoCobrancaControle.setarConvenio}"
                                         reRender="panelAutorizacaoCobrancaCliente,dadosContaCorrente"/>
                        </h:selectOneMenu>
                    </h:panelGroup>

                </h:panelGroup>

                <%--PARCELAS A COBRAR--%>
                <h:panelGroup layout="block" styleClass="sub-group"
                              rendered="#{(AutorizacaoCobrancaControle.autorizacao.edit || AutorizacaoCobrancaControle.autorizacao.novoObj) && AutorizacaoCobrancaControle.autorizacao.tipoAutorizacao.id != 3}">
                    <h:outputText styleClass="texto-font texto-size-16 texto-cor-cinza"
                                  style="display: inline-table; margin-top: 10px; margin-bottom: 10px;"
                                  value="Parcelas a cobrar"/>
                    <h:panelGroup layout="block" id="panelParACobrar" styleClass="cb-container">
                        <h:selectOneMenu onblur="blurinput(this);" onfocus="focusinput(this);"
                                         id="tpParcelasCobrar"
                                         value="#{AutorizacaoCobrancaControle.autorizacao.tipoACobrar}"
                                         valueChangeListener="#{AutorizacaoCobrancaControle.tipoACobrarListener}">
                            <f:selectItems value="#{AutorizacaoCobrancaControle.tiposACobrar}"/>
                            <a4j:support event="onchange" reRender="panelAutorizacaoCobrancaCliente"/>
                        </h:selectOneMenu>
                    </h:panelGroup>
                </h:panelGroup>
            </h:panelGroup>

            <h:panelGroup id="button-adicionar-produtos"
                          styleClass="group"
                          style="margin-top: -20px"
                          rendered="#{AutorizacaoCobrancaControle.autorizacao.tipoACobrar.id == 3}">
                <h:panelGroup styleClass="sub-group" style="width: 100%; display: block" id="prod">
                    <h:outputText styleClass="texto-font texto-size-16 texto-cor-cinza label"
                                  value="Tipos de produtos: "/>
                    </br>

                    <%-- NÃO PERMITE EDITAR TIPOS DE PRODUTO --%>
                    <h:outputText rendered="#{!AutorizacaoCobrancaControle.permiteEditarProdutos}"
                                  styleClass="texto-font texto-cor-cinza tooltipster"
                                  title="Não é possível editar os tipos de produtos para o tipo de convênio de cobrança selecionado"
                                  style="font-size: 12px !important"
                                  value="#{AutorizacaoCobrancaControle.autorizacao.listaObjetosACobrar}"/>

                    <%-- PERMITE EDITAR TIPOS DE PRODUTO --%>
                    <a4j:commandLink rendered="#{AutorizacaoCobrancaControle.permiteEditarProdutos}"
                                     id="labelTiposProdutos"
                                     value="#{empty AutorizacaoCobrancaControle.autorizacao.listaObjetosACobrar ? 'CLIQUE PARA ADICIONAR' : AutorizacaoCobrancaControle.autorizacao.listaObjetosACobrar}"
                                     action="#{AutorizacaoCobrancaControle.abrirModalTiposProdutosNovo}"
                                     oncomplete="#{AutorizacaoCobrancaControle.mensagemNotificar};#{AutorizacaoCobrancaControle.onComplete}"
                                     reRender="painelConfigTipoProdutoAuto"
                                     title="#{AutorizacaoCobrancaControle.autorizacao.listaObjetosACobrar_Title}"
                                     styleClass="tooltipster"/>
                </h:panelGroup>
            </h:panelGroup>

            <div class="sep" style="margin:4px 0 5px 0;"/>
            <h:panelGroup id="dadosCartao" layout="block"
                          rendered="#{AutorizacaoCobrancaControle.autorizacao.edit || AutorizacaoCobrancaControle.autorizacao.novoObj}">

                <h:panelGroup style="display: flex;" layout="block"
                              rendered="#{AutorizacaoCobrancaControle.autorizacao.tipoAutorizacao.id == 1 && AutorizacaoCobrancaControle.autorizacao.convenio.codigo > 0}">
                    <h:panelGroup layout="block" style="margin-right: 45px;">
                        <div class="group">
                            <div class="sub-group">
                                <%--COBRANÇA VINDI--%>
                            <h:panelGroup layout="block" id="panelBtnIncluirAutorizacaoIdVindiPlano" styleClass="sub-group"
                                          style="margin-left: -6px"
                                          rendered="#{AutorizacaoCobrancaControle.permiteUsarIdVindiCliente}">
                                <a4j:commandLink id="incluirAutorizacaoIdVindi" value="Usar IdVindi do Cliente"
                                                 title="A transação será enviada utilizando somente o IdVindi do cliente.<br/>
                                A Vindi irá realizar a tentativa de cobrança utilizando o cartão que o cliente tiver cadastrado lá na Vindi."
                                                 action="#{AutorizacaoCobrancaControle.incluirAutorizacaoIdVindi}"
                                                 styleClass="tooltipster botoes nvoBt btSec"
                                                 oncomplete="#{AutorizacaoCobrancaControle.mensagemNotificar};#{AutorizacaoCobrancaControle.msgAlert}"
                                                 reRender="panelAutorizacaoCobrancaCliente, dados"/>
                            </h:panelGroup>
                        </div>
                        </div>
                        <div class="group">
                            <!-- NOME DO TITULAR -->
                            <div class="sub-group">
                                <h:outputText styleClass="title" value="Nome titular do cartão"
                                              rendered="#{!empty AutorizacaoCobrancaControle.bandeirasCartao}"/>
                                </br>
                                <h:inputText id="nomeTitular"
                                             value="#{AutorizacaoCobrancaControle.autorizacao.nomeTitularCartao}"
                                             styleClass="inputTextClean"
                                             style="margin-top: 10px;text-transform: uppercase"
                                             onkeypress="return permiteSomenteLetra(this.form, this.id, event);"
                                             tabindex="1"
                                             onfocus="focusinput(this);"
                                             onblur="blurinput(this);"
                                             maxlength="50">
                                    <a4j:support event="onchange" reRender="nomeTitularCartao, cardCartao"
                                                 action="#{AutorizacaoCobrancaControle.inicializarCard}"
                                                 oncomplete="document.getElementById('formCadastroAutorizacaoCobranca:cpfTitulaCartao').focus()"/>
                                </h:inputText>
                            </div>

                            <!-- CPF DO TITULAR -->
                            <div class="sub-group">
                                <h:panelGroup
                                        rendered="#{AutorizacaoCobrancaControle.autorizacao.tipoAutorizacao.id == 1 && AutorizacaoCobrancaControle.autorizacao.convenio.codigo > 0}">
                                    <h:outputText styleClass="title" value="CPF do titular"/>
                                    </br>
                                    <h:inputText id="cpfTitulaCartao"
                                                 tabindex="2"
                                                 value="#{AutorizacaoCobrancaControle.autorizacao.cpfTitular}"
                                                 styleClass="inputTextClean"
                                                 style="margin-top: 10px;width: 125px;"
                                                 onfocus="focusinput(this);"
                                                 onblur="blurinput(this);"
                                                 onkeypress="return mascara(this.form, this.id, '999.999.999-99', event);"
                                                 maxlength="14">
                                    </h:inputText>
                                </h:panelGroup>
                            </div>
                        </div>
                        <div class="group">

                            <!-- NÚMERO DO CARTÃO -->
                            <div class="sub-group">
                                <h:panelGroup rendered="#{!empty AutorizacaoCobrancaControle.bandeirasCartao}">
                                    <h:outputText styleClass="title" value="Número do cartão"/>
                                    </br>
                                    <h:inputText id="nrCartao"
                                                 tabindex="3"
                                                 value="#{AutorizacaoCobrancaControle.autorizacao.numeroCartao}"
                                                 styleClass="inputTextClean"
                                                 style="margin-top: 10px;"
                                                 onfocus="focusinput(this);"
                                                 onblur="blurinput(this);"
                                                 maxlength="19"
                                                 onkeypress="mascaraCartaoCreditoNovoContrato()">
                                        <a4j:support event="onchange"
                                                     action="#{AutorizacaoCobrancaControle.buscaBandeiraCartaoOperadora}"
                                                     reRender="cardCartao, selectBandeira"
                                                     oncomplete="document.getElementById('formCadastroAutorizacaoCobranca:validade').focus()"/>
                                    </h:inputText>
                                </h:panelGroup>
                            </div>

                            <!-- BANDEIRA -->
                            <div class="sub-group">
                                <h:outputText styleClass="title" value="Bandeira"/>
                                </br>
                                <h:panelGroup layout="block" styleClass="block cb-container" style="margin-top: 10px;width: 126px;" id="selectBandeira">
                                    <h:selectOneMenu
                                            value="#{AutorizacaoCobrancaControle.bandeiraCartao}"
                                            onblur="blurinput(this);" onfocus="focusinput(this);"
                                            tabindex="4"
                                            styleClass="form">
                                        <f:selectItems value="#{AutorizacaoCobrancaControle.bandeirasAprovaFacil}"/>
                                        <a4j:support event="onchange" reRender="panelConteudo, cardCartao"
                                                     action="#{AutorizacaoCobrancaControle.setBandeiraCartaoOperadora}"/>
                                    </h:selectOneMenu>
                                </h:panelGroup>
                            </div>
                        </div>
                        <div class="group">

                            <!-- VALIDADE -->
                            <div class="sub-group">
                                <h:panelGroup
                                        rendered="#{AutorizacaoCobrancaControle.autorizacao.tipoAutorizacao.id == 1 && AutorizacaoCobrancaControle.autorizacao.convenio.codigo > 0}">
                                    <h:outputText styleClass="title" value="Data de vencimento"/>
                                    </br>
                                    <h:inputText id="validade" value="#{AutorizacaoCobrancaControle.autorizacao.validadeCartao}"
                                                 tabindex="5"
                                                 style="width: 90px; height: 32px !important; margin-right: 1rem; margin-top: 10px;"
                                                 onkeypress="return mascara(this.form, this.id, '99/99', event);"
                                                 styleClass="inputCobranca"
                                                 onkeyup="tabAutom(this)"
                                                 onfocus="focusinput(this);"
                                                 onblur="blurinput(this);">
                                        <a4j:support event="onchange" reRender="datesCartao"
                                                     oncomplete="document.getElementById('formCadastroAutorizacaoCobranca:codSeguranca').focus()"/>
                                    </h:inputText>
                                </h:panelGroup>
                            </div>

                            <!-- CODIGO SEGURANCA -->
                            <div class="sub-group" style="margin-left: 93px;">
                                <h:outputText styleClass="title" value="CVV" style="margin-right: 11px;"/>
                                </br>
                                <h:panelGroup layout="block"
                                              style="display: flex; align-items: center; display: flex; margin-left: -40px; margin-top: 15px;">
                                    <h:inputText id="codSeguranca"
                                                 style="margin-top: 10px;"
                                                 value="#{AutorizacaoCobrancaControle.autorizacao.cvv}"
                                                 onkeypress="return mascara(this.form, this.id, '9999', event);"
                                                 tabindex="6"
                                                 onkeyup="tabAutom(this)"
                                                 size="6" styleClass="form"
                                                 onfocus="focusinput(this);" onblur="blurinput(this);"
                                                 maxlength="4">
                                    </h:inputText>
                                    <h:panelGroup id="botaoHelp"
                                                  layout="block"
                                                  style="margin-top: 15px;"
                                                  styleClass="circleQuestion">
                                        <h:outputText id="hintCVV" style="margin: auto;"
                                                      styleClass="tooltipParcelas tooltipster"
                                                      title="CVV é um código de segurança de 3 dígitos impresso no verso de cartões de crédito"
                                                      value="?"/>
                                    </h:panelGroup>
                                </h:panelGroup>
                            </div>
                        </div>
                    </h:panelGroup>
                    <h:panelGroup layout="block">
                        <h:panelGroup layout="block" id="cardCartao" style="margin-top: 35px;"
                                      styleClass="cartao #{AutorizacaoCobrancaControle.styleClassCartao}">
                            <div style="padding: 10px">
                                <h:panelGroup layout="block" id="chipcard" style="margin: 16px;">
                                    <h:graphicImage value="imagens_flat/icon-chip.svg" width="25" height="18"/>
                                </h:panelGroup>

                                <h:panelGroup layout="block" id="nomeTitularCartao" style="text-transform: uppercase">
                                    <h:outputText styleClass="#{AutorizacaoCobrancaControle.styleClassCartaoTitles}"
                                                  value="#{AutorizacaoCobrancaControle.autorizacao.nomeTitularCartao == '' ? 'Nome Titular do cartão' : AutorizacaoCobrancaControle.autorizacao.nomeTitularCartao}"/>
                                </h:panelGroup>

                                <div style="display: flex; justify-content: space-between; margin: 3% 0 3% 0;">
                                    <h:panelGroup id="numeroCartaoApresentar">
                                        <h:outputText
                                                styleClass="#{AutorizacaoCobrancaControle.styleClassCartaoTitles}"
                                                value="#{AutorizacaoCobrancaControle.autorizacao.numeroCartao == '' || AutorizacaoCobrancaControle.autorizacao.numeroCartao == null ? '**** **** **** ****' : AutorizacaoCobrancaControle.autorizacao.numeroCartao}"/>
                                    </h:panelGroup>
                                    <h:panelGroup id="datesCartao">
                                        <h:outputText
                                                styleClass="#{AutorizacaoCobrancaControle.styleClassCartaoTitles}"
                                                value="#{AutorizacaoCobrancaControle.autorizacao.validadeCartao == '' ? '00/00' : AutorizacaoCobrancaControle.autorizacao.validadeCartao}"/>
                                    </h:panelGroup>
                                </div>
                            </div>
                            <h:panelGroup layout="block">
                                <h:panelGroup layout="block" id="imagemCartao"
                                              style="float: right; margin-right: 10px;">
                                    <h:graphicImage
                                            rendered="#{AutorizacaoCobrancaControle.imagemCartao != 'semCartao'}"
                                            url="/imagens_flat/#{AutorizacaoCobrancaControle.imagemCartao}-icon.svg"
                                            width="30" height="30"/>
                                </h:panelGroup>
                                <h:panelGroup layout="block"/>
                                <div style="border-top: 1px solid #2e5d7c; padding: 50px 20px 26px; color: #b4b7bb;">
                                    <h:panelGroup>
                                        <a4j:commandLink action="#{AutorizacaoCobrancaControle.limparDadosDoCartao}"
                                                         reRender="dadosCartao">
                                            <i class="fa-icon-eraser texto-size-18"></i>
                                            <h:outputText styleClass="title" value="Limpar dados"/>
                                        </a4j:commandLink>
                                    </h:panelGroup>
                                </div>
                            </h:panelGroup>
                        </h:panelGroup>
                    </h:panelGroup>

                </h:panelGroup>


                <%--<h:panelGroup id="dadosCartaoCreditoCPFNumInp" style="width:100%;display: flex"
                              rendered="#{AutorizacaoCobrancaControle.autorizacao.tipoAutorizacao.id == 1 && AutorizacaoCobrancaControle.autorizacao.convenio.codigo > 0}">
                    <h:inputText id="cpfTitulaCartao"
                                 value="#{AutorizacaoCobrancaControle.autorizacao.cpfTitular}"
                                 tabindex="5"
                                 style="background-position: right;background-size: contain;width: 268px;margin-top: 10px"
                                 styleClass="inputTextClean cpftit"
                                 onfocus="focusinput(this);"
                                 onblur="blurinput(this);"
                                 onkeypress="return mascara(this.form, this.id, '999.999.999-99', event);"
                                 maxlength="80">
                    </h:inputText>
                    <rich:spacer width="45px"/>
                    <h:inputText id="nrCartao" value="#{AutorizacaoCobrancaControle.autorizacao.numeroCartao}"
                                 tabindex="7"
                                 styleClass="inputTextClean inputCartao"
                                 onfocus="focusinput(this);"
                                 onblur="blurinput(this);"
                                 style="background-position: right;background-size: contain; width: 268px;margin-top: 10px"
                                 onkeypress="return mascara(this.form, this.id, '9999999999999999999', event);"
                                 maxlength="16">
                        <a4j:support event="onchange"
                                     action="#{AutorizacaoCobrancaControle.buscaBandeiraCartaoOperadora}"
                                     reRender="numeroCartao1"/>
                    </h:inputText>
                    <h:graphicImage title="#{AutorizacaoCobrancaControle.imagemCartao}" id="numeroCartao1"
                                    style="width: 48px; height: 26px;border:none; margin-top: 15px; margin-left: -54px"
                                    url="imagens/bandeiras/#{AutorizacaoCobrancaControle.imagemCartao}.png">
                    </h:graphicImage>
                </h:panelGroup>--%>

                <%--<h:panelGroup id="dadosCartaoCreditoVAlCVVout" style="width:100%;display: flex"
                              rendered="#{AutorizacaoCobrancaControle.autorizacao.tipoAutorizacao.id == 1 && AutorizacaoCobrancaControle.autorizacao.convenio.codigo > 0}">
                    <h:outputText styleClass="texto-cor-cinza texto-size-14" value="Validade"
                                  style="display: inline-block; margin-right:95px;margin-top: 20px"/>
                    <rich:spacer width="168px"/>
                    <h:panelGroup>
                        <h:outputText rendered="#{!empty AutorizacaoCobrancaControle.bandeirasCartao}"
                                      style=" ;margin-top: 20px "
                                      styleClass="texto-cor-cinza texto-size-14" value="CVV"/>
                        <h:outputText id="hintCVV" styleClass="tooltipParcelas tooltipster circleQuestion"
                                      title="CVV é um código de segurança de 3 dígitos impresso no verso de cartões de crédito"
                                      value="?">
                        </h:outputText>
                    </h:panelGroup>
                </h:panelGroup>--%>

                <%--<h:panelGroup id="cvvCartaoAtu" style="margin-bottom: 15px; width: 204px"
                              rendered="#{AutorizacaoCobrancaControle.autorizacao.tipoAutorizacao.id == 1 && AutorizacaoCobrancaControle.autorizacao.convenio.codigo > 0}">
                    <h:inputText id="validadeCartaoAut"
                                 value="#{AutorizacaoCobrancaControle.autorizacao.validadeCartao}"
                                 tabindex="8"
                                 style="background-position: right;background-size: contain; width: 268px;margin-top: 10px"
                                 styleClass="inputTextClean validadeCartao"
                                 onfocus="focusinput(this);"
                                 onblur="blurinput(this);"
                                 onkeypress="return mascara(this.form, this.id, '99/9999', event);"
                                 maxlength="80">
                    </h:inputText>
                    <rich:spacer width="45px"/>
                    <h:inputText id="cvv" value="#{AutorizacaoCobrancaControle.autorizacao.cvv}"
                                 tabindex="7"
                                 style="background-position: right;background-size: contain; width: 268px;margin-top: 10px"
                                 styleClass="inputTextClean cvvCartao"
                                 onfocus="focusinput(this);"
                                 onblur="blurinput(this);"
                                 maxlength="4">
                    </h:inputText>
                </h:panelGroup>--%>

                <h:panelGroup id="dadosCartaoCredito1" layout="block" style="width:100%"
                              rendered="#{AutorizacaoCobrancaControle.autorizacao.tipoAutorizacao.id == 1 && AutorizacaoCobrancaControle.autorizacao.convenio.codigo > 0}">
                    <h:panelGrid columns="5" width="50%">
                        <h:panelGroup layout="block" style="display: inline-block;margin-left: -187px;height: 28px">
                        </h:panelGroup>
                    </h:panelGrid>
                </h:panelGroup>

                <h:panelGrid id="dadosContaCorrente" columns="2" width="100%" styleClass="colsL colsR"
                             rendered="#{AutorizacaoCobrancaControle.autorizacao.tipoAutorizacao.id == 2 and not AutorizacaoCobrancaControle.autorizacao.convenio.boleto}">

                    <h:panelGroup layout="block" style="width: 100%; margin-bottom: 20px">
                        <h:inputHidden id="bancoCodigo"
                                       value="#{AutorizacaoCobrancaControle.autorizacao.banco.codigoBanco}"/>
                        <h:inputHidden id="contaValida" value="#{AutorizacaoCobrancaControle.contaValida}"/>
                        <h:outputText styleClass="texto-size-14 texto-font texto-cor-cinza texto-bold"
                                      value="#{AutorizacaoCobrancaControle.autorizacao.banco.codigoBanco}"
                                      rendered="#{AutorizacaoCobrancaControle.autorizacao.banco.codigoBanco != 0}">
                            <f:convertNumber pattern="000"/>
                        </h:outputText>
                        <h:outputText styleClass="texto-size-14 texto-font texto-cor-cinza texto-bold"
                                      value=" - #{AutorizacaoCobrancaControle.autorizacao.banco.nome}"
                                      rendered="#{AutorizacaoCobrancaControle.autorizacao.banco.codigoBanco != 0}"/>
                        <h:panelGroup id="erroValidacaoDcoConta" layout="block" style="color: orangered;"/>
                    </h:panelGroup>
                    <rich:spacer width="0px"/>

                    <h:panelGroup style="display: block">
                        <h:outputText styleClass="texto-size-14 texto-font texto-cor-cinza"
                                      value="Nome do titular" title="#{msg.title_cpf_titular}"/>
                    </h:panelGroup>
                    <rich:spacer width="0px"/>

                    <h:panelGroup style="width:100%;display: flex">
                        <h:inputText style="background-position: right;background-size: contain; width: 578px;margin-top: 10px;display: inline-block;margin-bottom: 20px"
                                     styleClass="inputTextClean nomeContaDB"
                                     onfocus="focusinput(this);if(this.value=='Nome do titular da conta'){this.value=''}"
                                     onblur="blurinput(this);if(this.value == ''){this.value='Nome do titular da conta'}"
                                     value="#{AutorizacaoCobrancaControle.autorizacao.nomeTitularCartao}">
                            <script>
                                verificarPlaceHolder(jQuery('.nomeContaDB'), 'Nome do titular da conta');
                            </script>
                        </h:inputText>
                    </h:panelGroup>
                    <rich:spacer width="0px"/>

                    <h:panelGroup style="margin-top: 20px">
                        <h:outputText styleClass="texto-size-14 texto-font texto-cor-cinza"
                                      style="margin-top: 20px"
                                      value="CPF"
                                      title="#{msg.title_cpf_titular}"/>
                        <rich:spacer width="250px"/>
                    </h:panelGroup>
                    <rich:spacer width="0px"/>

                    <h:panelGroup>
                        <h:inputText id="cpfTitular" maxlength="14"
                                     style=" width: 268px"
                                     onkeypress="return mascara(this.form, this.id, '999.999.999-99', event);"
                                     styleClass="inputTextClean CPFContaDB"
                                     onfocus="focusinput(this);if(this.value=='somente números'){this.value=''}"
                                     onblur="blurinput(this);if(this.value == ''){this.value='somente números'}"
                                     value="#{AutorizacaoCobrancaControle.autorizacao.cpfTitular}">
                            <script>
                                verificarPlaceHolder(jQuery('.CPFContaDB'), 'somente números');
                            </script>
                        </h:inputText>
                        <rich:spacer width="37px"/>

                    </h:panelGroup>
                    <rich:spacer width="0px"/>
                    <h:panelGroup style="display: inline-block;margin-top: 20px">
                        <h:outputText styleClass="texto-size-14 texto-font texto-cor-cinza "
                                      style="margin-top: 20px"
                                      value="Agência"/>
                        <rich:spacer width="165px"/>
                        <h:outputText styleClass="texto-size-14 texto-font texto-cor-cinza "
                                      style="margin-top: 20px"
                                      value="Dígito"/>
                        <rich:spacer width="57px"/>
                        <h:outputText styleClass="texto-size-14 texto-font texto-cor-cinza "
                                      value="Conta Corrente"/>
                        <rich:spacer width="117px"/>
                        <h:outputText styleClass="texto-size-14 texto-font texto-cor-cinza "
                                      value="Dígito"/>
                        <h:panelGroup style="display: block;width: 160%">
                            <h:inputText id="agenciaCobranca"
                                         value="#{AutorizacaoCobrancaControle.autorizacao.agencia}"
                                         onchange="validarConta()"
                                         title="Agência"
                                         onkeypress="return mascara(this.form, this.id, '9999999999999999999', event);"
                                         styleClass="inputTextClean" onfocus="focusinput(this);"
                                         style="width: 200px"
                                         onblur="blurinput(this);" maxlength="12"/>
                            <h:outputText styleClass="texto-font texto-size-16 texto-cor-cinza" value=" - "/>
                            <h:inputText id="agenciaCobrancaDV"
                                         value="#{AutorizacaoCobrancaControle.autorizacao.agenciaDV}"
                                         onchange="validarConta()"
                                         style="width: 60px"
                                         styleClass="inputTextClean"
                                         title="Dígito Agência" onfocus="focusinput(this);"
                                         onblur="blurinput(this);" maxlength="2"/>
                            <rich:spacer width="30px"/>
                            <h:inputText id="contaCorrenteCobranca"
                                         value="#{AutorizacaoCobrancaControle.autorizacao.contaCorrente}"
                                         onchange="validarConta()"
                                         title="Conta Corrente"
                                         style="width: 200px; margin-top: 10px"
                                         onkeypress="return mascara(this.form, this.id, '9999999999999999999', event);"
                                         onfocus="focusinput(this);"
                                         onblur="blurinput(this);"
                                         maxlength="12"/>

                            <h:outputText styleClass="texto-font texto-size-16 texto-cor-cinza" value=" - "/>
                            <h:inputText id="contaCorrenteCobrancaDV"
                                         value="#{AutorizacaoCobrancaControle.autorizacao.contaCorrenteDV}"
                                         onchange="validarConta()"
                                         style="width: 56px; margin-top: 10px"
                                         title="Dígito Conta Corrente"
                                         onfocus="focusinput(this);"
                                         onblur="blurinput(this);" maxlength="2"/>
                        </h:panelGroup>

                        <h:panelGroup styleClass="texto-size-14 texto-font texto-cor-cinza"
                                      rendered="#{AutorizacaoCobrancaControle.autorizacao.apresentarCodigoOperacao}">
                            <h:outputText style="display: block; margin-top: 10px"
                                          styleClass="texto-size-14 texto-font texto-cor-cinza"
                                          value="Operação"/>
                            <h:inputText id="codOperacao"
                                         value="#{AutorizacaoCobrancaControle.autorizacao.codigoOperacao}"
                                         size="3"
                                         title="Código Operação"
                                         styleClass="inputTextClean" onfocus="focusinput(this);"
                                         onblur="blurinput(this);" maxlength="3"/>
                        </h:panelGroup>

                    </h:panelGroup>
                </h:panelGrid>
            </h:panelGroup>
            <h:panelGroup layout="block"
                          style="line-height: 50px;height: 50px; margin-left: 11px;"
                          rendered="#{AutorizacaoCobrancaControle.autorizacao.edit || AutorizacaoCobrancaControle.autorizacao.novoObj}">
                <a4j:commandLink id="confirmarAutorizacao"
                                 action="#{AutorizacaoCobrancaControle.confirmarOrigemVendaDePlano}"
                                 style="color: #FFFFFF; background: #22933B !important; height: 36px"
                                 styleClass="texto-font texto-size-14 texto-cor-branco linkPadrao botaoPrimario"
                                 oncomplete="#{AutorizacaoCobrancaControle.msgAlert}"
                                 reRender="dados, formCadastroAutorizacaoCobranca:mensagemAutorizacaoCobranca, tabAutorizacao, panelModalVerificacaoCartao">
                    Salvar
                </a4j:commandLink>
                <a4j:commandLink id="cancelarAutorizacao" value="Cancelar"
                                 styleClass="texto-font texto-size-14 texto-cor-branco linkPadrao botaoPrimario"
                                 oncomplete="Richfaces.hideModalPanel('panelAutorizacaoCobranca');"
                                 style="color: #6F747B !important; background: #E5E5E5 !important; height: 36px; margin-left: 10px;"
                                 reRender="panelAutorizacaoCobranca, formCadastroAutorizacaoCobranca:mensagemAutorizacaoCobranca, tabAutorizacao"/>
            </h:panelGroup>
        </h:panelGroup>
    </a4j:form>
</rich:modalPanel>
<rich:modalPanel id="panelProduto" autosized="true" shadowOpacity="true" styleClass="novaModal grande " width="50"
                 height="250">
    <f:facet name="header">
        <h:panelGroup>
            <h:outputText value="Consulta do Produto"></h:outputText>
        </h:panelGroup>
    </f:facet>
    <f:facet name="controls">
        <h:panelGroup>
            <h:outputText
                    styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign btnSelecionarModalidade"
                    id="hidelink"/>
            <rich:componentControl for="panelProduto" attachTo="hidelink" operation="hide" event="onclick"/>
        </h:panelGroup>
    </f:facet>

    <h:form id="formProduto" onkeypress="return event.keyCode != 13; ">
        <h:panelGrid columns="1" width="100%" columnClasses="colunaCentralizada">
            <h:panelGroup styleClass="colunaEsquerda" layout="block">
                <h:inputText id="valorConsultaProduto" size="10"
                             style="margin-left:15px;margin-right: 10px;min-width: 60%"
                             onfocus="focusinput(this);if(this.value=='Consultar por'){this.value=''}"
                             onblur="blurinput(this);if(this.value == ''){this.value='Consultar por'}"
                             styleClass="inputTextClean valorConsultaProduto"
                             onkeypress="validarEnter(event,'formProduto:formProduto');"
                             value="#{ContratoControle.valorConsultaProduto}"/>
                <script>
                    verificarPlaceHolder(jQuery('.valorConsultaProduto'), 'Consultar por');
                </script>
                <h:panelGroup layout="block" styleClass="cb-container" style="height: 41px;margin-right: 10px">
                    <h:selectOneMenu onblur="blurinput(this);" onfocus="focusinput(this);" styleClass="form"
                                     id="consultaProduto" value="#{ContratoControle.campoConsultaProduto}">
                        <f:selectItems value="#{ContratoControle.tipoConsultaComboProduto}"/>
                    </h:selectOneMenu>
                </h:panelGroup>
                <a4j:commandLink id="formProduto"
                                 reRender="formProduto"
                                 action="#{ContratoControle.consultarProduto}"
                                 styleClass="botaoprimariogrande texto-cor-branco texto-size-14 tooltipster"
                                 value="#{msg_bt.btn_consultar}" title="#{msg.msg_consultar_dados}"/>
            </h:panelGroup>
            <rich:dataTable id="resultadoConsultaProduto" width="100%"
                            styleClass="tabelaSimplesCustom"
                            rendered="#{not empty ContratoControle.listaConsultaProduto}"
                            value="#{ContratoControle.listaConsultaProduto}" rows="5" var="produto">
                <rich:column style="width: 60%;text-align: left">
                    <f:facet name="header">
                        <h:outputText value="DESCRIÇÃO"
                                      styleClass="texto-font texto-cor-cinza texto-size-14 texto-bold"/>
                    </f:facet>
                    <h:panelGroup>
                        <h:panelGroup layout="block" style="width: 100%;text-align: left">
                            <a4j:commandLink action="#{ContratoControle.selecionarProduto}"
                                             styleClass="linkPadrao texto-size-16 texto-cor-azul texto-font"
                                             reRender="pnlProduto,plano,planoDescricao,planoDuracaoVO,planoModalidadeVO,planoModalidadeVezesSemanaVO,planoHorarioVO,planoComposicaoVO,composicaoMarcada ,
                                             modalidadeMarcada,produtoMarcada,planoProdutoMarcada,planoProdutoVO,botaoAdicionar,total, total1, subtotal,convenioDescontoMarcada ,valorMensalModalidade, duracao, horario, panelMesangem,mensagemConsultaProduto,detalhesNegociacao"
                                             oncomplete="#{ContratoControle.msgAlertAuxiliar}"
                                             value="#{produto.descricao}"/>
                        </h:panelGroup>
                    </h:panelGroup>
                </rich:column>
                <rich:column styleClass="col-text-align-left" headerClass="col-text-align-left">
                    <f:facet name="header">
                        <h:outputText value="TIPO VIGÊNCIA"
                                      styleClass="texto-font texto-cor-cinza texto-size-14 texto-bold"/>
                    </f:facet>
                    <h:panelGroup>
                        <a4j:commandLink action="#{ContratoControle.selecionarProduto}" focus=""
                                         reRender="plano,planoDescricao,planoDuracaoVO,planoModalidadeVO,planoModalidadeVezesSemanaVO,
                                             planoHorarioVO,planoComposicaoVO,composicaoMarcada ,modalidadeMarcada,produtoMarcada,
                                             planoProdutoMarcada,planoProdutoVO,botaoAdicionar,total, total1, subtotal,
                                             convenioDescontoMarcada ,valorMensalModalidade, duracao, horario, panelMesangem,
                                             mensagemConsultaProduto,pnlProduto,detalhesNegociacao"
                                         styleClass="linkPadrao texto-size-16 texto-cor-azul texto-font"
                                         oncomplete="#{ContratoControle.msgAlertAuxiliar}"
                                         value="#{produto.tipoVigencia_Apresentar}"/>
                    </h:panelGroup>
                </rich:column>
                <rich:column styleClass="col-text-align-right" headerClass="col-text-align-right">
                    <f:facet name="header">
                        <h:outputText value="VALOR"
                                      styleClass="texto-font texto-cor-cinza texto-size-14 texto-bold"/>
                    </f:facet>
                    <h:outputText styleClass="linkPadrao texto-size-16 texto-cor-cinza texto-font valorMonetario"
                                  value="#{produto.valorFinal}">
                        <f:converter converterId="FormatadorNumerico"/>
                    </h:outputText>
                </rich:column>
                <rich:column styleClass="col-text-align-center" headerClass="col-text-align-center">
                    <a4j:commandLink action="#{ContratoControle.selecionarProduto}" focus=""
                                     reRender="plano,planoDescricao,planoDuracaoVO,planoModalidadeVO,planoModalidadeVezesSemanaVO,
                                           planoHorarioVO,planoComposicaoVO,composicaoMarcada ,modalidadeMarcada,produtoMarcada,
                                           planoProdutoMarcada,planoProdutoVO,botaoAdicionar,total, total1, subtotal,
                                           convenioDescontoMarcada ,valorMensalModalidade, duracao, horario, panelMesangem,
                                           mensagemConsultaProduto,panelProdutoParcela,pnlProduto,detalhesNegociacao"
                                     oncomplete="#{ContratoControle.msgAlertAuxiliar}"
                                     title="#{msg.msg_selecionar_dados}"
                                     styleClass="linkPadrao texto-size-20 texto-cor-azul texto-font tooltipster">
                        <i class="fa-icon-plus-sign"></i>
                    </a4j:commandLink>
                </rich:column>
            </rich:dataTable>
            <rich:datascroller align="center" renderIfSinglePage="false" style="margin-top: 20px"
                               styleClass="scrollPureCustom" for="formProduto:resultadoConsultaProduto"
                               maxPages="10"
                               id="scResultadoProduto"/>
        </h:panelGrid>
    </h:form>
</rich:modalPanel>
<rich:modalPanel id="panelConfirmarAlteracao" autosized="true" styleClass="novaModal noMargin" shadowOpacity="true"
                 width="500" height="200">
    <f:facet name="header">
        <h:panelGroup>
            <h:outputText value="Confirmar Alteração de Data Base"/>
        </h:panelGroup>
    </f:facet>
    <f:facet name="controls">
        <h:panelGroup>
            <h:outputText
                    styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign btnSelecionarModalidade"
                    id="hidelink7"/>
            <rich:componentControl for="panelConfirmarAlteracao" attachTo="hidelink7" operation="hide"
                                   event="onclick"/>
        </h:panelGroup>
    </f:facet>
    <a4j:form id="formConfirmarAlteracao" ajaxSubmit="true">
        <h:panelGroup layout="block" style="margin: 10 20 10 20;">
            <rich:spacer width="10"/>
            <h:panelGroup layout="block" styleClass="margin-box" style="padding-bottom: 0;">
                <h:outputText styleClass="texto-size-14 texto-bold texto-font texto-cor-cinza"
                              value="DATA LANÇAMENTO"/>
                <rich:spacer width="5"/>
                <h:panelGroup styleClass="dateTimeCustom">
                    <rich:calendar id="dataBase"
                                   value="#{ContratoControle.dataAuxiliar}"
                                   inputSize="10"
                                   inputClass="form"
                                   oninputblur="blurinput(this);"
                                   oninputfocus="focusinput(this);"
                                   buttonIcon="/imagens_flat/calendar-button.svg"
                                   oninputchange="return validar_Data(this.id);"
                                   datePattern="dd/MM/yyyy"
                                   enableManualInput="true"
                                   jointPoint="bottom-left"
                                   zindex="2"
                                   styleClass="inputTextClean"
                                   showWeeksBar="false">
                    </rich:calendar>
                </h:panelGroup>
                <h:message for="dataBase" styleClass="mensagemDetalhada"/>
            </h:panelGroup>
            <h:panelGroup layout="block" styleClass="margin-box" style="padding-bottom: 0;">
                <h:outputText styleClass="texto-size-12 texto-font texto-cor-cinza" value="Alterar a data base do contrato alterará todos os relatórios e
                                  estatísticas do sistema referentes a data escolhida. Tem certeza que deseja continuar? "/>
            </h:panelGroup>
            <h:panelGrid id="mensagemDataBase" columns="1" width="100%">
                <h:panelGrid columns="1" width="100%">
                    <h:outputText styleClass="mensagemDetalhada" value="#{ContratoControle.mensagemDetalhada}"/>
                </h:panelGrid>
            </h:panelGrid>
            <h:panelGrid width="100%" columns="1" style="text-align:right; padding:20px 0 0 0;margin-right: 40px;">
                <h:outputLink styleClass="tooltipster"
                              value="#{SuperControle.urlBaseConhecimento}tela-do-cliente-modulo-adm/"
                              title="Clique e saiba mais: Alteração da data de lançamento do Contrato"
                              target="_blank">
                    <h:outputText value="Para saber mais detalhes veja o wiki"/>
                </h:outputLink>
                <h:panelGroup layout="block" style="width:50%;float: right;height: 50px;line-height: 4;">
                    <a4j:commandLink id="confirmar" reRender="panelAutorizacaoFuncionalidade, dados, confNegociacao"
                                     action="#{ContratoControle.validarDataBaseInformada}"
                                     oncomplete="#{ContratoControle.mensagemNotificar}"
                                     value="#{msg_bt.btn_confirmar}" accesskey="2"
                                     styleClass="botaoprimario linkPadrao texto-size-14"/>
                    <rich:spacer width="10"/>
                    <a4j:commandLink id="cancelar" oncomplete="Richfaces.hideModalPanel('panelConfirmarAlteracao');"
                                     value="#{msg_bt.btn_fechar}" accesskey="2"
                                     styleClass="botaosecundario texto-size-14"/>
                </h:panelGroup>
            </h:panelGrid>
        </h:panelGroup>
    </a4j:form>
</rich:modalPanel>
<rich:modalPanel id="panelAlteracaoInicioContrato" autosized="true" styleClass="novaModal noMargin" shadowOpacity="true"
                 width="500" height="200">
    <f:facet name="header">
        <h:panelGroup>
            <h:outputText value="Confirmar alteração de ínicio Contrato"/>
        </h:panelGroup>
    </f:facet>
    <f:facet name="controls">
        <h:panelGroup>
            <h:outputText
                    styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign btnSelecionarModalidade"
                    id="hidelink8"/>
            <rich:componentControl for="panelAlteracaoInicioContrato" attachTo="hidelink8" operation="hide"
                                   event="onclick"/>
        </h:panelGroup>
    </f:facet>
    <a4j:form id="formConfirmarAlteracaoInicioContrato" ajaxSubmit="true">
        <h:panelGroup layout="block" style="margin: 10 20 10 20;">
            <rich:spacer width="10"/>
            <h:panelGroup layout="block" styleClass="margin-box" style="padding-bottom: 0;">
                <h:outputText styleClass="texto-size-14 texto-bold texto-font texto-cor-cinza"
                              value="DATA ÍNICIO "/>
                <rich:spacer width="5"/>
                <h:panelGroup styleClass="dateTimeCustom">
                    <rich:calendar id="dataInicio"
                                   value="#{ContratoControle.dataInicioContrato}"
                                   inputSize="10"
                                   buttonIcon="/imagens_flat/calendar-button.svg"
                                   inputClass="form"
                                   oninputblur="blurinput(this);"
                                   oninputfocus="focusinput(this);"
                                   oninputchange="return validar_Data(this.id);"
                                   datePattern="dd/MM/yyyy"
                                   enableManualInput="true"
                                   styleClass="inputTextClean "
                                   zindex="2"
                                   showWeeksBar="false">
                    </rich:calendar>
                </h:panelGroup>
                <h:message for="dataInicio" styleClass="mensagemDetalhada"/>
            </h:panelGroup>
            <h:panelGrid id="mensagemDataInicio" columns="1" width="100%">
                <h:panelGrid columns="1" width="100%">
                    <h:outputText styleClass="mensagemDetalhada" value="#{ContratoControle.msgDataInicio}"/>
                </h:panelGrid>
            </h:panelGrid>
            <h:panelGrid width="100%" columns="1" style="text-align:right; padding:20px 0 0 0;margin-right: 40px;">
                <h:panelGroup layout="block" style="width:50%;float: right;height: 50px;line-height: 4;">
                    <a4j:commandLink id="confirmar"
                                     reRender="formConfirmarAlteracaoInicioContrato,datasContrato,confNegociacao,dados"
                                     action="#{ContratoControle.validarDataInicio}"
                                     oncomplete="#{ContratoControle.msgAlert}"
                                     value="#{msg_bt.btn_confirmar}" accesskey="2"
                                     styleClass="botaoprimario linkPadrao texto-size-14"/>
                    <rich:spacer width="10"/>
                    <a4j:commandLink id="cancelar"
                                     oncomplete="Richfaces.hideModalPanel('panelAlteracaoInicioContrato');"
                                     value="#{msg_bt.btn_fechar}" accesskey="2"
                                     styleClass="botaosecundario texto-size-14"/>
                </h:panelGroup>
            </h:panelGrid>
        </h:panelGroup>
    </a4j:form>
</rich:modalPanel>
<rich:modalPanel id="panelConfirmarAlteracao2" autosized="true" styleClass="novaModal noMargin" shadowOpacity="true"
                 width="450" height="200">
    <f:facet name="header">
        <h:panelGroup>
            <h:outputText value="Confirmar Alteração de Data Base"/>
        </h:panelGroup>
    </f:facet>
    <a4j:form id="formConfirmarAlteracao2" ajaxSubmit="true">
        <h:panelGroup layout="block" styleClass="margin-box">
            <rich:spacer style="display:block" width="10"/>
            <h:outputText styleClass="texto-font texto-size-16 texto-cor-cinza"
                          value="A Data Base do Contrato escolhida é #{ContratoControle.dataBase_Apresentar}
                          com Início em #{ContratoControle.dataInicioContrato_Apresentar}. Confirmar estes dados e continuar?"/>
            <h:panelGrid width="100%" columns="1" style="text-align:right; padding:20px 0 0 0;">
                <h:panelGroup>
                    <a4j:commandLink action="#{ContratoControle.concluirNegociacaoDataBaseAlterada}"
                                     styleClass="botaoPrimario texto-size-16"
                                     oncomplete="#{ContratoControle.abrirTelaConferir ? (ContratoControle.contratoSemAutorizacaoCobranca ? 'abrirModalSemAutorizacaoCobranca();' : 'abrirTelaConferir();' ) : ContratoControle.abrirModaisFecharNegociacaoDataBaseAlterada}">
                        <h:outputText id="confirmar" value="#{msg_bt.btn_confirmar}"/>
                    </a4j:commandLink>
                    <rich:spacer width="10"/>
                    <a4j:commandLink id="cancelar"
                                     oncomplete="Richfaces.hideModalPanel('panelConfirmarAlteracao2');"
                                     value="#{msg_bt.btn_fechar}"
                                     accesskey="2" styleClass="botaoSecundario texto-size-16"/>
                </h:panelGroup>
            </h:panelGrid>
        </h:panelGroup>
    </a4j:form>
</rich:modalPanel>
<rich:modalPanel id="panelAutorizarDataBase" autosized="true" shadowOpacity="true" styleClass="novaModal" width="450"
                 height="250" onshow="document.getElementById('formAutorizarDataBase:senha').focus()">
    <f:facet name="header">
        <h:panelGroup>
            <h:outputText value="Autorizar Nova Data Base"></h:outputText>
        </h:panelGroup>
    </f:facet>
    <f:facet name="controls">
        <h:panelGroup>
            <h:outputText
                    styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign btnSelecionarModalidade"
                    id="hideLinkPanelAutorizarDataBase"/>
            <rich:componentControl for="panelAutorizarDataBase" attachTo="hideLinkPanelAutorizarDataBase"
                                   operation="hide" event="onclick"/>
        </h:panelGroup>
    </f:facet>
    <a4j:form id="formAutorizarDataBase">
        <h:panelGroup layout="block" styleClass="margin-box">
            <h:panelGrid id="panelAutorizarDB" cellpadding="5" columns="2" width="100%" columnClasses="colunaEsquerda">
                <h:inputText style="opacity:0;height: 0;" size="5" maxlength="7"/>
                <h:inputSecret size="14" maxlength="64" style="opacity:0;height: 0;"/>
                <h:outputText styleClass="texto-font texto-size-16 texto-cor-cinza" value="CÓDIGO"/>
                <h:panelGroup>

                    <h:inputText id="codigoUsuario" style="margin-left:5px" size="5" maxlength="7"
                                 styleClass="inputTextClean" value="#{ContratoControle.responsavelDataBase.codigo}">
                        <a4j:support event="onchange" focus="formAutorizarDataBase:senha"
                                     action="#{ContratoControle.consultarResponsavelDataBase}"
                                     reRender="formAutorizarDataBase"/>
                    </h:inputText>
                    <h:inputText id="autoCompleteHidden" size="3" maxlength="10" style="margin-left:6px;opacity:0;"/>
                </h:panelGroup>
                <h:outputText styleClass="texto-font texto-size-16 texto-cor-cinza" value="USÚARIO"/>
                <h:panelGroup>

                    <h:outputText style="margin-left:5px" styleClass="texto-font texto-size-16 texto-cor-cinza"
                                  value="#{ContratoControle.responsavelDataBase.username}"/>
                </h:panelGroup>
                <h:outputText styleClass="texto-font texto-size-16 texto-cor-cinza" value="SENHA"/>
                <h:panelGroup>

                    <h:inputSecret style="margin-left:5px" autocomplete="off" id="senha" size="14" maxlength="64"
                                   styleClass="inputTextClean"
                                   value="#{ContratoControle.responsavelDataBase.senha}"
                                   onkeypress="validarEnter(event,'formAutorizarDataBase:autorizarDB')"/>
                </h:panelGroup>

            </h:panelGrid>
            <h:panelGroup layout="block" styleClass="container-botoes">
                <a4j:commandLink id="autorizarDB" value="#{msg_bt.btn_confirmar}"
                                 styleClass="botaoPrimario texto-size-16"
                                 reRender="form"
                                 oncomplete="#{ContratoControle.apresentarModalAutorizacao2}"
                                 title="#{msg.msg_gravar_dados}" action="#{ContratoControle.autorizarDataBase}"/>
            </h:panelGroup>
        </h:panelGroup>
    </a4j:form>
</rich:modalPanel>
<rich:modalPanel id="panelPlanoModalidadeVezesSemana" styleClass="novaModal" autosized="true" shadowOpacity="true"
                 width="550">
    <f:facet name="header">
        <h:panelGroup>
            <h:outputText value="Consultar Vezes por Semana"></h:outputText>
        </h:panelGroup>
    </f:facet>
    <f:facet name="controls">
        <h:panelGroup>
            <h:outputText
                    styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign btnSelecionarModalidade"
                    id="hidelinkVezesSemana"/>
            <rich:componentControl for="panelPlanoModalidadeVezesSemana" attachTo="hidelinkVezesSemana" operation="hide"
                                   event="onclick"/>
        </h:panelGroup>
    </f:facet>
    <a4j:form id="formPlanoModalidadeVezesSemana">
        <h:panelGrid columns="1" width="100%" columnClasses="colunaCentralizada">
            <h:panelGrid id="panelGridPlanoModalidadeVezesSemana" columns="1" width="100%"
                         styleClass="tabFormSubordinada">
                <rich:dataTable id="planoModalidadeVezesSemanaVO" styleClass="tabelaSimplesCustom" width="100%"
                                value="#{ContratoControle.planoModalidadeVO.planoModalidadeVezesSemanaVOs}"
                                var="vezesSemana">
                    <rich:column>
                        <a4j:commandLink action="#{ContratoControle.selecionarVezesSemana}"
                                         title="selecionar vezes por semana"
                                         oncomplete="Richfaces.hideModalPanel('panelPlanoModalidadeVezesSemana')"
                                         reRender="planoComposicaoVO,resumoContrato,pgModalidade,convenioDescontoVO,composicaoMarcada,modalidadeMarcada,
                                             produtoMarcada,total,total1,valorMensalModalidade,panelMesangem,
                                             panelProdutoParcela,panelEdicaoModalidade, mensagem, planoModalidadeVO, panelGeral, subtotal, selectModalidade,
                                             totalContrato, totalDescontos, totalDescontosP, planoDuracaoVO, pgDuracaoPlano, tabelaModalidadeItem"
                                         styleClass="texto-size-16 texto-font linkPadrao"
                                         value="#{vezesSemana.nrVezes} vezes por semana"
                                         rendered="#{!ContratoControle.planoModalidadeVO.modalidade.utilizarTurma}"/>

                        <h:outputText title="selecionar vezes por semana"
                                      styleClass="texto-size-16 texto-font linkPadrao"
                                      value="#{vezesSemana.nrVezes} vezes por semana"
                                      rendered="#{ContratoControle.planoModalidadeVO.modalidade.utilizarTurma}"/>
                    </rich:column>
                    <h:column>
                        <h:outputText value="#{vezesSemana.tipoOperacao_Apresentar} "/>
                    </h:column>
                    <h:column>
                        <h:panelGroup rendered="#{vezesSemana.apresentarValorEspecifico}">
                            <h:outputText styleClass="texto-size-16 texto-font linkPadrao" value="R$ "/>
                            <h:outputText styleClass="texto-size-16 texto-font linkPadrao"
                                          value="#{vezesSemana.valorEspecifico}">
                                <f:converter converterId="FormatadorNumerico"/>
                            </h:outputText>
                            <h:outputText styleClass="texto-size-16 texto-font linkPadrao" value=" - Ref.: "
                                          rendered="#{vezesSemana.referencia}"/>
                            <h:outputText styleClass="texto-size-16 texto-font linkPadrao"
                                          rendered="#{vezesSemana.referencia}"
                                          value=" (#{vezesSemana.origem.descricao})"/>
                        </h:panelGroup>
                    </h:column>
                    <h:column>
                        <h:panelGroup rendered="#{!vezesSemana.apresentarValorEspecifico}">
                            <h:outputText styleClass="texto-size-16 texto-font linkPadrao"
                                          value="#{vezesSemana.percentualDesconto} ">
                                <f:converter converterId="FormatadorNumerico7Casa"/>
                            </h:outputText>
                            <h:outputText styleClass="texto-size-16 texto-font linkPadrao" value=" %"/>
                        </h:panelGroup>
                    </h:column>
                </rich:dataTable>
                <h:panelGrid id="planoModalidadeVezesSemanaVOMensagem"
                             rendered="#{ContratoControle.planoModalidadeVO.modalidade.utilizarTurma}" width="100%">
                    <h:outputText id="mensagem" styleClass="mensagemDetalhada"
                                  value="#{ContratoControle.mensagemDetalhada}"/>
                </h:panelGrid>
                <h:panelGroup layout="block" styleClass="container-botoes">
                    <a4j:commandLink styleClass="botaoSecundario texto-size-16" value="Cancelar"
                                     oncomplete="Richfaces.hideModalPanel('panelPlanoModalidadeVezesSemana')"
                                     title="cancelar"/>
                </h:panelGroup>
            </h:panelGrid>
        </h:panelGrid>
    </a4j:form>
</rich:modalPanel>
<rich:modalPanel id="panelCupomDesconto" autosized="true" styleClass="novaModal" shadowOpacity="true" width="450"
                 height="200">
    <f:facet name="header">
        <h:panelGroup>
            <h:outputText value="Cupom de Desconto"></h:outputText>
        </h:panelGroup>
    </f:facet>
    <f:facet name="controls">
        <h:panelGroup>
            <h:outputText
                    styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign btnSelecionarModalidade"
                    id="hidelinkCupomDesc"/>
            <rich:componentControl for="panelCupomDesconto" attachTo="hidelinkCupomDesc" operation="hide"
                                   event="onclick"/>
        </h:panelGroup>
    </f:facet>

    <a4j:form id="formCupomDesc" ajaxSubmit="true" styleClass="paginaFontResponsiva">

        <h:panelGrid columns="1" columnClasses="col-text-align-center" width="100%" cellpadding="5">
            <h:outputText styleClass="tituloCampos" value="Número do Cupom "/>
            <h:panelGroup>
                <h:inputText id="nrCupom" style="padding-left: 0px;padding-right: 0px" size="15" maxlength="25"
                             onblur="blurinput(this);" onfocus="focusinput(this);" styleClass="col-text-align-center"
                             value="#{ContratoControle.numeroCupom}"/>
                <h:message for="nrCupom" styleClass="mensagemDetalhada"/>
            </h:panelGroup>
        </h:panelGrid>
        <h:panelGroup layout="block" styleClass="container-botoes">
            <a4j:commandLink
                    id="botaoConfirmarCupomDesconto"
                    reRender="#{ContratoControle.updateComponente.condicaoPagamento},confNegociacao, mensagemCupomDesc"
                    action="#{ContratoControle.validarCupomCupom}"
                    oncomplete="#{ContratoControle.msgAlert}"
                    styleClass="botaoPrimario texto-size-16">
                Confirmar
            </a4j:commandLink>
        </h:panelGroup>

    </a4j:form>
</rich:modalPanel>
<rich:modalPanel id="panelConfirmaContratoSemAutorizacaoCobranca" autosized="true" styleClass="novaModal"
                 shadowOpacity="true" width="450" height="200">
    <f:facet name="header">
        <h:panelGroup>
            <h:outputText value="Atenção"></h:outputText>
        </h:panelGroup>
    </f:facet>
    <f:facet name="controls">
        <h:panelGroup>
            <h:outputText
                    styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign btnSelecionarModalidade"
                    id="hidelinkSemAC"/>
            <rich:componentControl for="panelConfirmaContratoSemAutorizacaoCobranca" attachTo="hidelinkSemAC"
                                   operation="hide" event="onclick"/>
        </h:panelGroup>
    </f:facet>

    <a4j:form id="formSemAutorizacaoCobranca" ajaxSubmit="true" styleClass="paginaFontResponsiva">

        <h:panelGroup id="teste16" layout="block" styleClass="col-text-align-center"
                      rendered="#{ContratoControle.empresaPermiteFecharNegSemCartao}"
                      style="margin-top: 10px">
            <h:outputText styleClass="texto-size-16-real texto-cor-cinza texto-font" id="teste136"
                          value="Você não informou os dados para autorização de cobrança.Deseja continuar assim mesmo?"/>
        </h:panelGroup>
        <h:panelGroup layout="block" styleClass="col-text-align-center"
                      rendered="#{ContratoControle.apresentarMsgCartaoPerm}">
            <h:outputText styleClass="texto-size-16-real texto-cor-cinza texto-font"
                          value="Você tem a permissão 2.74 selecionada que permite finalizar negociação sem informar a Autorização."/>
        </h:panelGroup>
        <h:panelGroup layout="block" id="testeauto" styleClass="col-text-align-center"
                      rendered="#{ContratoControle.apresentarMsgCartaoPerm}"
                      style="margin-top: 10px; margin-bottom: 10px">
            <h:outputText value="Deseja continuar assim mesmo?" style="color: #F37021;margin-top: 10px"
                          styleClass="texto-size-16-real texto-cor-cinza texto-font texto-bold"/>
        </h:panelGroup>
        <h:panelGroup layout="block" styleClass="container-botoes">
            <a4j:commandLink
                    id="confirmarSemAC"
                    oncomplete="abrirTelaConferir();"
                    styleClass="botaoSecundario texto-size-16">
                Sim
            </a4j:commandLink>
            <a4j:commandLink action="#{ContratoControle.removerValoresProRata}"
                             id="fecharSemAC"
                             reRender="dados,confNegociacao, mensagemCupomDesc"
                             oncomplete="Richfaces.hideModalPanel('panelConfirmaContratoSemAutorizacaoCobranca');"
                             styleClass="botaoPrimario texto-size-16">
                Não
            </a4j:commandLink>
        </h:panelGroup>

    </a4j:form>
</rich:modalPanel>
<rich:modalPanel id="panelPeriodoAcessoExistente"
                 showWhenRendered="#{ContratoControle.dataPeriodoAcessoVigente != null && ContratoControle.contratoVO.apresentarDataInicio && !ContratoControle.confirmarModalPeriodoAcessoExistente}"
                 autosized="true" styleClass="novaModal" shadowOpacity="true" width="450" height="200">
    <f:facet name="header">
        <h:panelGroup>
            <h:outputText value="Atenção"></h:outputText>
        </h:panelGroup>
    </f:facet>
    <f:facet name="controls">
        <h:panelGroup>
            <h:outputText
                    styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign btnSelecionarModalidade"
                    id="hidelinkComPA"/>
            <rich:componentControl for="panelPeriodoAcessoExistente" attachTo="hidelinkComPA" operation="hide"
                                   event="onclick"/>
        </h:panelGroup>
    </f:facet>

    <a4j:form id="formPeriodoAcessoExistente" ajaxSubmit="true" styleClass="paginaFontResponsiva">

        <h:panelGroup layout="block" styleClass="col-text-align-center">
            <h:outputText styleClass="texto-size-16-real texto-cor-cinza texto-font"
                          value="O aluno possui acesso até o dia #{ContratoControle.dataPeriodoAcessoVigenteApresentar}.Deseja continuar?"/>
        </h:panelGroup>
        <h:panelGroup layout="block" styleClass="container-botoes">
            <a4j:commandLink
                    id="confirmarComPA"
                    oncomplete="Richfaces.hideModalPanel('panelPeriodoAcessoExistente');"
                    styleClass="botaoPrimario texto-size-16">
                <f:setPropertyActionListener value="#{true}"
                                             target="#{ContratoControle.confirmarModalPeriodoAcessoExistente}"/>
                Sim
            </a4j:commandLink>
            <a4j:commandLink
                    id="fecharComPA"
                    action="#{ContratoControle.abrirTelaCliente}"
                    style="margin-left: 8px;"
                    styleClass="botaoSecundario texto-size-16">
                Não
            </a4j:commandLink>
        </h:panelGroup>

    </a4j:form>
</rich:modalPanel>
<rich:modalPanel id="panelAviso" styleClass="novaModal" showWhenRendered="#{ContratoControle.apresentarAviso}"
                 autosized="true" shadowOpacity="true" width="450" height="150">
    <f:facet name="header">
        <h:panelGroup>
            <h:outputText value="Atenção!"></h:outputText>
        </h:panelGroup>
    </f:facet>
    <a4j:form id="formPanelAviso">
        <h:panelGrid columns="1" width="100%" columnClasses="colunaCentralizada">
            <h:panelGrid id="panelGridAviso" columns="1" width="100%" styleClass="tabFormSubordinada">
                <h:outputText id="mensagem" styleClass="texto-size-14-real texto-cor-vermelho texto-font"
                              value="#{ContratoControle.mensagemDetalhada}"/>
            </h:panelGrid>
            <h:panelGrid columns="1" width="100%" columnClasses="colunaCentralizada">
                <div style="margin:10px 0 0 0; text-align: center;">
                    <a4j:commandLink styleClass="botaoSecundario texto-size-14-real" value="Cancelar"
                                     action="#{ContratoControle.cancelarNegociacao}"
                                     title="cancelar"/>
                </div>
            </h:panelGrid>
        </h:panelGrid>
    </a4j:form>
</rich:modalPanel>
<script>
    function validarConta() {
        var selectorInputCodigoBanco = "formCadastroAutorizacaoCobranca:bancoCodigo";
        var selectorInputAgencia = "formCadastroAutorizacaoCobranca:agenciaCobranca";
        var selectorInputAgenciaDV = "formCadastroAutorizacaoCobranca:agenciaCobrancaDV";
        var selectorInputConta = "formCadastroAutorizacaoCobranca:contaCorrenteCobranca";
        var selectorInputContaDV = "formCadastroAutorizacaoCobranca:contaCorrenteCobrancaDV";
        var selectorInputContaValida = "formCadastroAutorizacaoCobranca:contaValida";
        var selectorOutputMensagemErro = "formCadastroAutorizacaoCobranca:erroValidacaoDcoConta";

        validarContaBancaria(selectorInputCodigoBanco,
            selectorInputAgencia,
            selectorInputAgenciaDV,
            selectorInputConta,
            selectorInputContaDV,
            selectorInputContaValida,
            selectorOutputMensagemErro
        );
    }
</script>
