<%--
    Document   : include_bi_contratosRecorrencia
    Created on : 14/07/2011
    Author     : <PERSON><PERSON>
--%>
<%@include file="../imports.jsp" %>
<h:panelGroup layout="block" id="panelCR" styleClass="container-bi"
              rendered="#{LoginControle.permissaoAcessoMenuVO.biCobrancaConvenio}">
    <c:if test="${(LoginControle.permissaoAcessoMenuVO.contratoRecorrencia || LoginControle.usuarioLogado.administrador) && (!BIControle.configuracaoBI.contratoR.naLixeira)}">
        <a4j:commandLink action="#{BIControle.carregarContratoRecorrencia}"
                         reRender="containerRecorrencia, blocoBotaoAtualizar"
                         onclick="abrirCarregandoPoBI(this)" oncomplete="hideCarregandoPorBI(this)"
                         status="nenhumStatus"
                         styleClass="btn-atualizar-bi"/>


        <h:panelGroup layout="block" id="containerRecorrencia">
            <h:panelGroup layout="block"
                          rendered="#{(BIControle.configuracaoBI.contratoR.apresentarBoxCarregar) && !BIControle.biCarregado}"
                          styleClass="bi-unloaded">
                <h:panelGroup styleClass="bi-header" layout="block">
                    <h:panelGroup layout="block" styleClass="bi-panel">
                        <h:outputText value="Cobran�as por Conv�nio" styleClass="bi-titulo pull-left"/>
                    </h:panelGroup>
                </h:panelGroup>

                <div class="ghost">
                    <table>
                        <tr>
                            <td style="width: 20%"><h3 class="card-title loading" style="height: 60px"></h3></td>
                            <td><h3 class="card-title loading" style="height: 60px"></h3></td>
                        </tr>
                        <tr>
                            <td style="width: 20%;"><h3 class="card-title loading" style="height: 60px"></h3></td>
                            <td><h3 class="card-title loading" style="height: 60px"></h3></td>
                        </tr>
                        <tr>
                            <td style="width: 20%;"><h3 class="card-title loading" style="height: 60px"></h3></td>
                            <td><h3 class="card-title loading" style="height: 60px"></h3></td>
                        </tr>
                    </table>
                </div>

            </h:panelGroup>
            <h:panelGroup layout="block"
                          rendered="#{!BIControle.configuracaoBI.contratoR.apresentarBoxCarregar || BIControle.biCarregado}">
                <h:panelGroup styleClass="bi-header" layout="block">
                    <h:panelGroup layout="block" styleClass="bi-panel">
                        <h:outputText value="Cobran�as por Conv�nio" styleClass="bi-titulo pull-left"/>

                        <h:outputLink styleClass="linkWiki bi-cor-cinza bi-left-align tooltipster"
                                      style="float: left;margin-top: 4.4%;margin-left: 1%"
                                      value="#{SuperControle.urlBaseConhecimento}bi-cobrancas-por-convenio-adm/"
                                      title="Clique e saiba mais: BI - DCC (D�bito em Cart�o de Cr�dito)" target="_blank">
                            <i class="fa-icon-question-sign" style="font-size: 18px; margin-top: -0.4em !important"></i>
                        </h:outputLink>

                        <a4j:commandLink id="consultarCR"
                                         style="margin-left: 7px"
                                         reRender="containerRecorrencia"
                                         oncomplete="montarTips();"
                                         action="#{RelContratosRecorrenciaControle.atualizarAgora}">
                            <i title="Atualizar cobran�a"
                               class="tooltipster lineHeight-3em fa-icon-refresh bi-btn-refresh bi-link pull-right"></i>
                        </a4j:commandLink>

                        <a4j:commandLink action="#{RelContratosRecorrenciaControle.abrirFiltros}"
                                         reRender="formPanelFiltroConvenioCobranca"
                                         oncomplete="Richfaces.showModalPanel('panelFiltroConvenioCobranca')">
                            <i title="Filtro"
                               class="tooltipster fa-icon-filter bi-btn-refresh bi-link pull-right lineHeight-3em"></i>
                        </a4j:commandLink>

                        <h:panelGroup layout="block" styleClass="pull-right calendarSemInputBI col-text-align-right">
                            <div title="${RelContratosRecorrenciaControle.dataBase_ApresentarMesDia}"
                                 style="display: inline-flex;margin-top: 1em;vertical-align: top;"
                                 class=" tooltipster dateTimeCustom alignToRight">
                                <rich:calendar id="dataInicioDCC"
                                               value="#{RelContratosRecorrenciaControle.dataBaseFiltro}"
                                               inputSize="8"
                                               showInput="false"
                                               inputClass="forcarSemBorda"
                                               buttonIcon="#{RelContratosRecorrenciaControle.dataAlterada ? '/imagens_flat/icon-calendar-red.png' : '/imagens_flat/icon-calendar-check.png'}"
                                               oninputblur="blurinput(this);"
                                               oninputfocus="focusinput(this);"
                                               datePattern="dd/MM/yyyy"
                                               enableManualInput="true"
                                               zindex="2"
                                               showWeeksBar="false">
                                    <a4j:support event="onchanged"
                                                 action="#{RelContratosRecorrenciaControle.atualizarAgora}"
                                                 oncomplete="montarTips();" reRender="containerRecorrencia"/>
                                </rich:calendar>
                            </div>
                        </h:panelGroup>
                        <%--                    <h:panelGroup layout="block"--%>
                        <%--                                  styleClass="pull-right calendarSemInputBI" style="margin-right: 10px;">--%>
                        <%--                        Este ainda n�o filtra por colaborador--%>

                        <%--                        <a4j:commandLink oncomplete="Richfaces.showModalPanel('filtroConversaoColaborador')" action="#{BIControle.biAtualizarParam}"  reRender="colCheck, grupoMarcadoCss, tituloFiltro">--%>
                        <%--                            <i title="Filtrar Por Colaborador" class="tooltipster fa-icon-user bi-btn-refresh bi-link pull-right lineHeight-3em">--%>
                        <%--                                <span class="badgeItem3Icon" data-bagde="${BIControle.qtdColRotContrato}"></span>--%>
                        <%--                            </i>--%>
                        <%--                            <a4j:actionparam name="biAtualizar" value="ROTATIVIDADE_CONTRATO"></a4j:actionparam>--%>
                        <%--                            <a4j:actionparam name="biAtualizarAbrirConsulta" value="biAtualizarAbrirConsulta"></a4j:actionparam>--%>
                        <%--                            <a4j:actionparam name="reRenderBi" value="containerRecorrencia"></a4j:actionparam>--%>
                        <%--                        </a4j:commandLink>--%>
                        <%--                    </h:panelGroup>--%>
                    </h:panelGroup>
                </h:panelGroup>
                <rich:dataTable id="listaRecorrencia" width="100%"
                                headerClass="consulta"
                                columnClasses="semBorda" styleClass="tabelaSimplesCustom showCellEmpty"
                                rows="100"
                                value="#{RelContratosRecorrenciaControle.listaBIDCC}" var="obj">
                    <rich:column width="60%" rendered="#{obj.qtd > 0 || RelContratosRecorrenciaControle.exibirTodos}">
                        <h:panelGroup>
                            <h:outputText styleClass="texto-size-16 linkPadrao texto-cor-cinza tooltipster"
                                          title="#{obj.tipo.hint}" value="#{obj.tipo.descricao}"/>
                        </h:panelGroup>
                    </rich:column>
                    <rich:column rendered="#{obj.qtd > 0 || RelContratosRecorrenciaControle.exibirTodos}"
                                 styleClass="col-text-align-right">
                        <a4j:commandLink id="abrirobj" styleClass="texto-size-16 linkPadrao texto-cor-azul tooltipster"
                                         reRender="bi-container-obj"
                                         title="#{obj.tipo.hint}"
                                         actionListener="#{RelContratosRecorrenciaControle.prepararLista}"
                                         oncomplete="#{obj.abrirPopUp}"
                                         value="#{obj.qtd}">
                            <f:attribute name="obj" value="#{obj}"/>
                        </a4j:commandLink>
                    </rich:column>
                    <rich:column style="text-align: right;margin-right: 4%;"
                                 rendered="#{obj.qtd > 0 || RelContratosRecorrenciaControle.exibirTodos}">
                        <h:panelGroup rendered="#{obj.valor != 0.0}">
                            <h:outputText styleClass="texto-size-16 linkPadrao texto-cor-cinza"
                                          style="font-weight:bold;"
                                          value=" #{MovPagamentoControle.empresaLogado.moeda} "/>
                            <h:outputText styleClass="texto-size-16 linkPadrao texto-cor-cinza"
                                          style="font-weight:bold;margin-right: 13%;"
                                          value="#{obj.valor}">
                                <f:converter converterId="FormatadorNumerico"/>
                            </h:outputText>
                        </h:panelGroup>
                    </rich:column>
                </rich:dataTable>

                <h:panelGroup layout="block" styleClass="bi-panel" style="text-align: right">
                    <a4j:commandLink value="#{!RelContratosRecorrenciaControle.exibirTodos ? 'Ver mais' : 'Ver menos'}"
                                     oncomplete="montarTips();"
                                     styleClass="linkPadrao texto-font texto-size-16 texto-cor-azul tooltipster"
                                     id="verMaisVerMenos"
                                     title="Mostrar/Esconder indicadores sem resultado."
                                     reRender="containerRecorrencia">
                        <h:outputText style="margin-left: 5px;vertical-align: middle;"
                                      styleClass="texto-font texto-cor-azul texto-size-16 fa-icon-minus-sign"
                                      rendered="#{RelContratosRecorrenciaControle.exibirTodos}"/>
                        <h:outputText style="margin-left: 5px;vertical-align: middle;"
                                      styleClass="texto-font texto-cor-azul texto-size-16 fa-icon-plus-sign"
                                      rendered="#{!RelContratosRecorrenciaControle.exibirTodos}"/>
                        <f:setPropertyActionListener value="#{!RelContratosRecorrenciaControle.exibirTodos}"
                                                     target="#{RelContratosRecorrenciaControle.exibirTodos}"/>
                    </a4j:commandLink>
                </h:panelGroup>

                <!-- ------------------- tentativas de cobran�a sem sucesso --------------------------- -->
                <h:panelGroup layout="block" style="width: 100%;">

                    <h:panelGroup layout="block" id="filtro" styleClass="bi-totalizador-header">
                        <h:panelGroup layout="block" style="width: 39.5%;margin-left:4.5%;display: inline-block;"
                                      styleClass="col-text-align-left">
                            <h:outputText styleClass="texto-size-16 bi-font-family bi-cor-cinza"
                                          value="Parcelas em vencimento"/>
                        </h:panelGroup>

                        <h:panelGroup layout="block" style="width:28%;display: inline-block;"
                                      styleClass="col-text-align-center">
                            <a4j:commandLink action="#{RelContratosRecorrenciaControle.toggleParcelasSomenteEsseMes}"
                                             id="noProprioMes"
                                             styleClass="texto-size-14"
                                             oncomplete="montarTips();"
                                             reRender="containerRecorrencia">
                                <h:outputText style="vertical-align:middle;"
                                              rendered="#{RelContratosRecorrenciaControle.somenteParcelasMes}"
                                              styleClass="fa-icon-check texto-size-14 bi-cor-azul"/>
                                <h:outputText style="vertical-align:middle;"
                                              rendered="#{!RelContratosRecorrenciaControle.somenteParcelasMes}"
                                              styleClass="fa-icon-check-empty texto-size-14 bi-cor-azul"/>
                            </a4j:commandLink>
                            <h:outputText styleClass="texto-size-14 bi-font-family bi-cor-cinza tooltipster"
                                          title="Parcelas com vencimento no m�s" value=" no pr�prio m�s"/>
                        </h:panelGroup>

                        <h:panelGroup layout="block" style="width:28%;display: inline-block;"
                                      styleClass="col-text-align-center">
                            <a4j:commandLink action="#{RelContratosRecorrenciaControle.toggleParcelasSomenteForaMes}"
                                             id="emOutrosMeses"
                                             styleClass="texto-size-14"
                                             oncomplete="montarTips();"
                                             reRender="containerRecorrencia">
                                <h:outputText style="vertical-align:middle;"
                                              rendered="#{RelContratosRecorrenciaControle.somenteParcelasForaMes}"
                                              styleClass="fa-icon-check texto-size-14 bi-cor-azul"/>
                                <h:outputText style="vertical-align:middle;"
                                              rendered="#{!RelContratosRecorrenciaControle.somenteParcelasForaMes}"
                                              styleClass="fa-icon-check-empty texto-size-14 bi-cor-azul"/>
                            </a4j:commandLink>
                            <h:outputText styleClass="texto-size-14 bi-font-family bi-cor-cinza tooltipster"
                                          title="Parcelas com vencimento em outros meses." value=" em outros meses"/>
                        </h:panelGroup>

                    </h:panelGroup>
                    <h:panelGroup layout="block" id="filtro2" styleClass="bi-totalizador-header">
                        <h:panelGroup layout="block" style="width:38%;display: inline-block;"
                                      styleClass="col-text-align-center">
                            <a4j:commandLink
                                    action="#{RelContratosRecorrenciaControle.toggleIncluirContratosCancelados}"
                                    oncomplete="montarTips();"
                                    id="IncluirContCancelados"
                                    styleClass="texto-size-14"
                                    reRender="containerRecorrencia">
                                <h:outputText style="vertical-align:middle;"
                                              rendered="#{RelContratosRecorrenciaControle.incluirContratosCancelados}"
                                              styleClass="fa-icon-check texto-size-14 bi-cor-azul"/>
                                <h:outputText style="vertical-align:middle;"
                                              rendered="#{!RelContratosRecorrenciaControle.incluirContratosCancelados}"
                                              styleClass="fa-icon-check-empty texto-size-14 bi-cor-azul"/>
                            </a4j:commandLink>
                            <h:outputText styleClass="texto-size-14 bi-font-family bi-cor-cinza tooltipster"
                                          value=" Incluir contratos cancelados"
                                          title="Incluir parcelas de contratos cancelados"/>
                        </h:panelGroup>

                    </h:panelGroup>

                    <h:panelGroup layout="block" styleClass="container-row" style="padding-bottom: 20px">
                        <rich:dataTable width="100%" value="#{RelContratosRecorrenciaControle.totalizador}"
                                        id="tabelaConvenio"
                                        var="totalizador"
                                        headerClass="font-size-Em"
                                        columnClasses="col-text-align-left,col-text-align-left,col-text-align-center,col-text-align-right"
                                        styleClass="bi-totalizador-table">
                            <rich:column styleClass="bi-table-text tooltipster" title="#{totalizador.title}">
                                <f:facet name="header">
                                    <h:outputText
                                            styleClass="tooltipster texto-size-14 bi-font-bold bi-font-family bi-cor-cinza"
                                            title="Totalizador de Parcelas considera o m�s at� a data de refer�ncia"
                                            value="TOTALIZADOR DE PARCELAS"/>
                                </f:facet>
                                <h:outputText styleClass="bi-table-text texto-size-16" id="label"
                                              value="#{totalizador.label}"/>
                            </rich:column>

                            <rich:column styleClass="bi-table-text col-text-align-right tooltipster"
                                         headerClass="col-text-align-right" title="#{totalizador.title}">
                                <f:facet name="header">
                                    <h:outputText
                                            styleClass="pull-right tooltipster texto-size-14 bi-font-bold bi-font-family bi-cor-cinza"
                                            title="Quantidade" value="QTD"/>
                                </f:facet>

                                <a4j:commandLink styleClass="texto-size-16 linkPadrao texto-cor-azul tooltipster"
                                                 reRender="bi-container-obj" style="float: right"
                                                 actionListener="#{RelContratosRecorrenciaControle.prepararListaTotalizador}"
                                                 oncomplete="#{totalizador.abrirPopUp}"
                                                 value="#{totalizador.quantidade}">
                                    <f:attribute name="totalizador" value="#{totalizador}"/>
                                </a4j:commandLink>

                            </rich:column>

                            <rich:column styleClass="bi-table-text col-text-align-right tooltipster"
                                         headerClass="col-text-align-right" title="#{totalizador.title}">
                                <f:facet name="header">
                                    <h:outputText
                                            styleClass="pull-right tooltipster texto-size-14 bi-font-bold bi-font-family bi-cor-cinza"
                                            title="Valor"
                                            value="VALOR"/>
                                </f:facet>
                                <h:outputText styleClass="texto-size-16 linkPadrao texto-cor-cinza"
                                              style="font-weight:bold;"
                                              value=" #{MovPagamentoControle.empresaLogado.moeda} "/>
                                <h:outputText styleClass="bi-table-text texto-size-16" id="valorApresentar"
                                              value="#{totalizador.valorApresentar}"/>
                            </rich:column>

                            <rich:column styleClass="bi-table-text tooltipster" title="#{totalizador.title}">
                                <f:facet name="header">
                                    <h:outputText
                                            styleClass="tooltipster texto-size-14 bi-font-bold bi-font-family bi-cor-cinza"
                                            title="Porcentagem"
                                            value="%"/>
                                </f:facet>
                                <h:outputText rendered="#{totalizador.porcentagem_Apresentar == ''}"
                                              id="porcentagem_Apresentar1"
                                              styleClass="texto-size-16 bi-font-family bi-cor-cinza" value=" -  "/>
                                <h:outputText rendered="#{totalizador.porcentagem_Apresentar != ''}"
                                              id="porcentagem_Apresentar2"
                                              styleClass="texto-size-16 bi-font-family bi-cor-cinza"
                                              value="#{totalizador.porcentagem_Apresentar}"/>
                            </rich:column>

                        </rich:dataTable>
                    </h:panelGroup>

                    <h:panelGroup layout="block"
                                  style="margin-bottom: 20px; border-top: 2px #E5E5E5 solid; border-bottom: 2px #E5E5E5 solid; padding-left: 4.5%; padding-right: 4.5%;">
                        <rich:dataTable width="100%"
                                        value="#{RelContratosRecorrenciaControle.totalizadorAguardandoRetorno}"
                                        id="tabelaConvenioAguardando"
                                        var="totalizador"
                                        style="margin-top: 3px;margin-bottom: 3px;"
                                        headerClass="font-size-Em"
                                        columnClasses="col-text-align-left,col-text-align-left,col-text-align-center,col-text-align-right"
                                        styleClass="bi-totalizador-table">

                            <rich:column styleClass="bi-table-text tooltipster" title="#{totalizador.title}"
                                         width="74%">
                                <h:outputText styleClass="bi-table-text texto-size-16" id="labelAguardando"
                                              value="#{totalizador.label}"/>
                            </rich:column>

                            <rich:column styleClass="bi-table-text col-text-align-right tooltipster"
                                         headerClass="col-text-align-right" title="#{totalizador.title}">
                                <h:outputText styleClass="bi-table-text texto-size-16" id="quantidadeAguardando"
                                              value="#{totalizador.quantidade}"/>
                            </rich:column>

                            <rich:column styleClass="bi-table-text col-text-align-center tooltipster"
                                         headerClass="col-text-align-right" title="#{totalizador.title}">
                                <h:outputText styleClass="texto-size-16 texto-cor-cinza" style="font-weight:bold;"
                                              value=" #{MovPagamentoControle.empresaLogado.moeda} "/>
                                <h:outputText styleClass="bi-table-text texto-size-16" id="valorApresentarAguardando"
                                              value="#{totalizador.valorApresentar}"/>
                            </rich:column>

                            <rich:column styleClass="bi-table-text col-text-align-right tooltipster"
                                         headerClass="col-text-align-right">
                            </rich:column>
                        </rich:dataTable>
                    </h:panelGroup>

                    <h:panelGroup layout="block" id="infoGradico" styleClass="bi-totalizador-header">
                        <h:panelGroup layout="block" style="margin-left: 4.5%;display: inline-block;"
                                      styleClass="col-text-align-center">
                            <h:outputText styleClass="icv-periodo-text bi-left-align tooltipster"
                                          value="Resultado do DCC"
                                          title="O resultado do DCC n�o leva em considera��o o filtro \"em outros meses\""/>
                        </h:panelGroup>
                    </h:panelGroup>

                    <h:panelGroup layout="block" styleClass="container-row tooltipster" style="padding-bottom: 20px">
                        <script type="text/javascript">
                            function carregarGraficoInadimplencia() {
                                chartInadimplencia = AmCharts.makeChart("chartinadimplenciadiv", {
                                    "type": "serial",
                                    "theme": "light",
                                    "dataProvider": ${RelContratosRecorrenciaControle.dadosInadimplenciaJSON},
                                    "valueAxes": [{
                                        "id": "inadimplenciaAxis",
                                        "axisAlpha": 0,
                                        "gridAlpha": 0,
                                        "position": "left",
                                        "maximum": 100,
                                        "title": "Efici�ncia / Inadimpl�ncia %"
                                    }, {
                                        "id": "valorTotalAxis",
                                        "axisAlpha": 0,
                                        "gridAlpha": 0,
                                        "position": "right",
                                        "title": "Valor (${MovPagamentoControle.empresaLogado.moeda})"
                                    }],
                                    "startDuration": 1,
                                    "legend": {
                                        "autoMargins": false,
                                        "maxColumns": 3,
                                        "align": "center",
                                        "equalWidths": true,
                                        "useGraphSettings": false,
                                        "valueAlign": "center",
                                        "valueWidth": 0
                                    },
                                    "numberFormatter": {
                                        "precision": 2,
                                        "decimalSeparator": ",",
                                        "thousandsSeparator": "."
                                    },
                                    "graphs": [{
                                        "balloonText": "[[title]]:<br/><b> ${MovPagamentoControle.empresaLogado.moeda} [[value]]</b>",
                                        "lineAlpha": 1,
                                        "lineThickness": 2,
                                        "bullet": "round",
                                        "dashLengthField": "dashLength",
                                        "type": "line",
                                        "lineColor": "#333",
                                        "valueField": "valorTotal",
                                        "title": "Enviadas com retorno",
                                        "valueAxis": "valorTotalAxis"
                                    }, {
                                        "balloonText": "[[title]]: <b> ${MovPagamentoControle.empresaLogado.moeda} [[value]]</b>",
                                        "lineAlpha": 1,
                                        "lineThickness": 1,
                                        "bullet": "round",
                                        "dashLengthField": "dashLength",
                                        "type": "line",
                                        "lineColor": "#008000",
                                        "valueField": "valorPago",
                                        "title": "${msg_aplic.prt_Bi_cobrancaRecorrencia_recebido}",
                                        "valueAxis": "valorTotalAxis"
                                    }, {
                                        "balloonText": "[[title]]: <b> ${MovPagamentoControle.empresaLogado.moeda} [[value]]</b>",
                                        "lineAlpha": 1,
                                        "lineThickness": 2,
                                        "dashLengthField": "dashLength",
                                        "bullet": "round",
                                        "type": "line",
                                        "lineColor": "#E23904",
                                        "valueField": "valorEmAberto",
                                        "title": "${msg_aplic.prt_Bi_cobrancaRecorrencia_aReceber}",
                                        "valueAxis": "valorTotalAxis"
                                    }, {
                                        "alphaField": "alpha",
                                        "clustered": false,
                                        "balloonText": "[[eficiencia]]",
                                        "dashLengthField": "dashLength",
                                        "fillAlphas": 0.9,
                                        "lineAlpha": 0.2,
                                        "columnWidth": 0.9,
                                        "title": "Efici�ncia(%)",
                                        "type": "column",
                                        "fillColors": "#E5E5E5",
                                        "lineColor": "#E5E5E5",
                                        "valueField": "eficienciaDouble",
                                        "valueAxis": "inadimplenciaAxis"
                                    }, {
                                        "alphaField": "alpha",
                                        "clustered": false,
                                        "balloonText": "[[value]]% ${msg_aplic.prt_Bi_cobrancaRecorrencia_deInadimplencia}",
                                        "dashLengthField": "dashLength",
                                        "fillAlphas": 0.9,
                                        "lineAlpha": 0.2,
                                        "columnWidth": 0.9,
                                        "title": "${msg_aplic.prt_Bi_cobrancaRecorrencia_inadimplencia}",
                                        "type": "column",
                                        "fillColors": "#B4B4B4",
                                        "lineColor": "#B4B4B4",
                                        "valueField": "inadimplencia",
                                        "valueAxis": "inadimplenciaAxis"
                                    }],
                                    "chartCursor": {
                                        "categoryBalloonEnabled": false,
                                        "cursorAlpha": 0,
                                        "zoomable": false
                                    },
                                    "categoryField": "mes",
                                    "categoryAxis": {
                                        "gridAlpha": 0,
                                        "axisAlpha": 0,
                                        "tickLength": 0,
                                        "labelRotation": 45
                                    },
                                    "export": {
                                        "enabled": true
                                    }

                                });
                            }
                        </script>

                        <div id="chartinadimplenciadiv" style="height: 300px; margin-top: 15px"></div>
                        <script>
                            carregarGraficoInadimplencia();
                        </script>

                    </h:panelGroup>
                </h:panelGroup>
            </h:panelGroup>
        </h:panelGroup>
    </c:if>
</h:panelGroup>
