<%--
  Created by IntelliJ IDEA.
  User: <PERSON>mberto
  Date: 09/05/2019
  Time: 11:59

--%>
<%@include file="/includes/imports.jsp" %>
<rich:dataTable id="tblProdutosSP" width="100%"
                styleClass="tabelaSimplesCustom"
                rendered="#{!ItemCampanhaControle.mostrarProdutosComPonto}"
                value="#{ItemCampanhaControle.listaProdutosSemPonto}"
                var="pesquisaProduto">
    <rich:column styleClass="col-text-align-left" headerClass="col-text-align-left" sortBy="#{pesquisaProduto.descricao}"
                 filterEvent="onkeyup">
        <f:facet name="header">
            <h:outputText styleClass="texto-font texto-size-14-real texto-cor-cinza texto-bold" value="#{msg_aplic.prt_descricao}"/>
        </f:facet>
        <h:outputText styleClass="texto-size-14 texto-cor-cinza texto-font" value="#{pesquisaProduto.descricao}"/>
        <a4j:support event="onclick"
                     actionListener="#{ItemCampanhaControle.selecionarProdutoSuggestionBox}"
                     oncomplete="#{ItemCampanhaControle.onComplete}"
                     reRender="panelBotoesControle, mensagem, panelProdutoRelatorio, painelBuscaProd">
            <f:attribute name="pesquisaProduto" value="#{pesquisaProduto}"/>
        </a4j:support>
    </rich:column>
</rich:dataTable>
<h:panelGrid id="gridPaginadorProdutoSP" columns="1" width="100%" columnClasses="colunaCentralizada"
             rendered="#{!ItemCampanhaControle.mostrarProdutosComPonto && ItemCampanhaControle.paginadorListaProdutosSP.count>0}">
    <table width="100%" border="0" cellspacing="0" cellpadding="0"  style="font-size: small">
        <tr>
            <td align="center" valign="middle">
                <h:panelGroup layout="block"
                              styleClass="paginador-container">
                    <h:panelGroup styleClass="pull-left"
                                  layout="block">
                        <h:outputText
                                styleClass="texto-size-14 texto-cor-cinza"
                                value="#{msg_aplic.prt_total_de}#{ItemCampanhaControle.paginadorListaProdutosSP.count} #{msg_aplic.ITENS}"></h:outputText>
                    </h:panelGroup>
                    <h:panelGroup layout="block"
                                  style="align-items: center">
                        <a4j:commandLink
                                disabled="#{ItemCampanhaControle.paginadorListaProdutosSP.desabilitarAcoes}"
                                styleClass="linkPadrao texto-cor-azul texto-size-20-real"
                                reRender="tblProdutosSP, gridPaginadorProdutoSP"
                                actionListener="#{ItemCampanhaControle.primeiraPagina}">
                            <i class="fa-icon-double-angle-left" id="primPaginaHistorico"></i>
                            <f:attribute name="tipo"
                                         value="ProdutosSP"/>
                        </a4j:commandLink>

                        <a4j:commandLink
                                disabled="#{ItemCampanhaControle.paginadorListaProdutosSP.desabilitarAcoes}"
                                styleClass="linkPadrao texto-cor-azul texto-size-20-real"
                                reRender="tblProdutosSP, gridPaginadorProdutoSP"
                                actionListener="#{ItemCampanhaControle.paginaAnterior}">
                            <i class="fa-icon-angle-left" id="pagAntHistorico"></i>
                            <f:attribute name="tipo"
                                         value="ProdutosSP"/>
                        </a4j:commandLink>
                        <h:outputText
                                styleClass="texto-font texto-cor-cinza texto-size-16-real"
                                value="#{msg_aplic.prt_msg_pagina} #{ItemCampanhaControle.paginadorListaProdutosSP.paginaAtualApresentar}"
                                rendered="true"/>
                        <a4j:commandLink
                                disabled="#{ItemCampanhaControle.paginadorListaProdutosSP.desabilitarAcoes}"
                                styleClass="linkPadrao texto-font texto-cor-azul texto-size-20-real"
                                reRender="tblProdutosSP, gridPaginadorProdutoSP"
                                actionListener="#{ItemCampanhaControle.proximaPagina}">
                            <i class="fa-icon-angle-right" id="proxPagHistorico"></i>
                            <f:attribute name="tipo"
                                         value="ProdutosSP"/>
                        </a4j:commandLink>

                        <a4j:commandLink
                                disabled="#{ItemCampanhaControle.paginadorListaProdutosSP.desabilitarAcoes}"
                                styleClass="linkPadrao texto-cor-azul texto-size-20-real"
                                reRender="tblProdutosSP, gridPaginadorProdutoSP"
                                actionListener="#{ItemCampanhaControle.ultimaPagina}">
                            <i class="fa-icon-double-angle-right" id="ultimaPaginaHistorico"></i>
                            <f:attribute name="tipo"
                                         value="ProdutosSP"/>
                        </a4j:commandLink>
                    </h:panelGroup>


                    <h:panelGroup layout="block" styleClass="paginador-itens-pagina">
                        <h:panelGroup styleClass="pull-right" layout="block">
                            <h:outputText styleClass="texto-size-14 texto-cor-cinza"
                                          value="#{msg_aplic.prt_itens_por_pagina}"/>
                        </h:panelGroup>

                        <h:panelGroup styleClass="cb-container pl20" layout="block">
                            <h:selectOneMenu
                                    value="#{ItemCampanhaControle.paginadorListaProdutosSP.limit}"
                                    id="qtdeItensPaginaProdutoSP">
                                <f:selectItem itemValue="#{10}"></f:selectItem>
                                <f:selectItem itemValue="#{30}"></f:selectItem>
                                <f:selectItem itemValue="#{50}"></f:selectItem>
                                <f:selectItem itemValue="#{100}"></f:selectItem>
                                <a4j:support event="onchange"
                                             actionListener="#{ItemCampanhaControle.atualizarNumeroItensPagina}"
                                             reRender="tblProdutosSP, gridPaginadorProdutoSP">
                                    <f:attribute name="tipo" value="ProdutosSP"/>
                                </a4j:support>
                            </h:selectOneMenu>

                        </h:panelGroup>
                    </h:panelGroup>
                </h:panelGroup>
            </td>
        </tr>
    </table>
</h:panelGrid>