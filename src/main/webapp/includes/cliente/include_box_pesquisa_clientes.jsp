<%--
    Document   : include_box_pesquisa_clientes
    Created on : 02/05/2012, 10:21:23
    Author     : Waller
--%>
<%@include file="../../include_imports.jsp" %>
<style>
    .btn-experimente {
        box-shadow: 0 2px 2px 0 rgba(169,169,169,.14), 0 3px 1px -2px rgba(169,169,169,.2), 0 1px 5px 0 rgba(169,169,169,.12);
        transition: .2s ease-in;
        background-color: #fff !important;
        color: #67757c !important;
        border-color: #b1b8bb !important;
        display: inline-block;
        font-weight: 400;
        font-size: 14px !important;
        text-align: center;
        white-space: nowrap;
        vertical-align: middle;
        -webkit-user-select: none;
        -moz-user-select: none;
        -ms-user-select: none;
        user-select: none;
        border: 1px solid transparent;
        transition: color .15s ease-in-out,background-color .15s ease-in-out,border-color .15s ease-in-out,box-shadow .15s ease-in-out;
        background: #fff !important;
        padding: 7px 12px !important;
        /*font-size: 15px !important;*/
        line-height: 1.5;
        border-radius: 5px;
        font-family: "Nunito Sans",sans-serif !important;
    }

    .btn-experimente:hover {
        box-shadow: 0 14px 26px -12px rgba(169,169,169,.42), 0 4px 23px 0 rgba(0,0,0,.12), 0 8px 10px -5px rgba(169,169,169,.2);
        color: #212529 !important;
        background-color: #b3b2b2 !important;
        border-color: #acacac !important;
    }

    .div-geral-experimente {
        display: grid;
        padding: 20px 0;
        grid-template-columns: 2fr 0fr;
    }

    .div-experimente2 {
        justify-content: end;
        display: flex;
        padding-left: 10px;
    }

    .div-experimente {
        background-color: #fff !important;
        border-radius: 5px;
        font-family: "Nunito Sans",sans-serif !important;
        padding: 10px;
        align-items: center;
        display: flex;
    }

    .switch {
        position: relative;
        display: inline-block;
        width: 30px;
        height: 17px;
    }

    /* Hide default HTML checkbox */
    .switch input {
        opacity: 0;
        width: 0;
        height: 0;
    }

    /* The slider */
    .slider {
        position: absolute;
        cursor: pointer;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-color: #ccc;
        -webkit-transition: .4s;
        transition: .4s;
    }

    .slider:before {
        position: absolute;
        content: "";
        height: 13px;
        width: 13px;
        left: 2px;
        bottom: 2px;
        background-color: white;
        -webkit-transition: .4s;
        transition: .4s;
    }

    input:checked + .slider {
        background-color: #2196F3;
    }

    input:focus + .slider {
        box-shadow: 0 0 1px #2196F3;
    }

    input:checked + .slider:before {
        -webkit-transform: translateX(13px);
        -ms-transform: translateX(13px);
        transform: translateX(13px);
    }

    /* Rounded sliders */
    .slider.round {
        border-radius: 17px;
    }

    .slider.round:before {
        border-radius: 50%;
    }

    .padrao {
        cursor: pointer;
        display: flex;
        color: #797D86;
        font-size: 16px;
        font-weight: 400;
        line-height: 16px;
        padding: 30px 0px 0 40px;
        width: calc(100% - 80px);
        justify-content: flex-end;
    }
    .padrao label{
        margin-left: 8px;
    }
</style>
<div class="container-box zw_ui especial"
     style="background: none; box-shadow: none; margin-top: 100px !important;">

    <h:panelGroup layout="block" styleClass="div-geral-experimente"
                  rendered="#{!ConsultaClienteControle.habilitarRecursoPadraoTelaCliente && LoginControle.apresentarModuloNovoTreino}">
        <h:panelGroup layout="block"
                      styleClass="div-experimente">
            <h:graphicImage value="images/pct-circle-exclamation-red.svg"
                            style="color: #FA1E1E; width: 16px; padding-right: 5px; padding-bottom: 2px;"/>
            <h:outputText
                    value="<b>Aten��o:</b> No dia 01/03/2024 essa tela ser� descontinuada. Experimente agora a nova vers�o e obtenha o m�ximo de desempenho."
                    escape="false" style="color: #FA1E1E; font-size: 14px !important; text-align: left;"/>
        </h:panelGroup>
        <h:panelGroup layout="block" style="width: 100%;"
                      styleClass="div-experimente2">
            <a4j:commandButton id="abrirNovaTelaClientes"
                               action="#{ConsultaClienteControle.abrirNovaListaPessoasExperimente}"
                               value="Experimentar nova vers�o"
                               alt="Experimentar nova vers�o"
                               styleClass="btn-experimente"
                               oncomplete="#{ConsultaClienteControle.msgAlert}"/>
        </h:panelGroup>
    </h:panelGroup>

    <h:panelGroup layout="block" style="padding: 10px"
                  rendered="#{!LoginControle.apresentarModuloNovoTreino}">
    </h:panelGroup>

    <div style="background-color: white; padding: 20px; position: relative; margin-top: 20px !important; box-shadow: 0 2px 10px 0 rgba(0,0,0,0.35);">
        <script>
            function abrirDropDownFiltros(componente, nomePainel) {
                jQuery('.paineldropdownfiltros').slideUp();
                var abrir = !jQuery(componente).hasClass('painelToogleAberto');
                jQuery('.painelToogleFiltros').removeClass('painelToogleAberto');
                jQuery('.painelToogleFiltros').addClass('painelToogleFechado');
                if (abrir) {
                    jQuery(componente).addClass('painelToogleAberto');
                    jQuery(componente).removeClass('painelToogleFechado');
                    jQuery(nomePainel).slideDown();
                }
            }


            function carregaMais() {
                document.getElementById('form:pagiPosteriorTopHide').click();
            }

            function ordernar(campo) {
                if (document.getElementById('form:idorderby').value === campo) {
                    document.getElementById('form:idalterarAD').click();
                } else {
                    document.getElementById('form:idorderby').value = campo;
                    document.getElementById('form:consultar').click();
                }
            }

            function consultarInput(event) {
                if (event.keyCode === 13) {
                    document.getElementById('form:consultar').click();
                }
            }

            function selectAllValor() {
                document.getElementById('form:valorConsulta').setSelectionRange(0, document.getElementById('form:valorConsulta').value.length);
            }
        </script>
        <h:inputHidden id="idorderby" value="#{ConsultaClienteControle.orderBy}"/>
        <a4j:commandLink actionListener="#{ConsultaClienteControle.trocarAD}"
                         style="display: none;" id="idalterarAD"
                         reRender="idpainelClientes, ultimossete, totalItens, pnlPagiPosterior">
            <f:attribute name="paginaInicial" value="paginaInicial"/>
            <f:attribute name="tipoConsulta" value="detalhada"/>
        </a4j:commandLink>

        <div class="tituloPainelPesquisa notop" style="font-size: 16px; z-index: 10; width: 24%; float: left;">
            Clientes
        </div>
        <div style="float: left; text-align: center; width: 49%;  z-index: 0;">
            <h:outputText id="totalItens" styleClass="tituloTotalizador"
                          style="font-weight: normal; padding:0 !important; float: left; height: 40px"
                          rendered="#{LoginControle.permissaoAcessoMenuVO.totalClienteConsulta}"
                          value="Total #{ConsultaClienteControle.confPaginacao.numeroTotalItens} #{msg_aplic.prt_clientes}"/>
        </div>
        <div style="float: right; z-index: 3; width: 24%; text-align: right; padding-right: 30px;">

                <a4j:commandLink id="exportarExcel"
                                 title="Exportar Excel" styleClass="tooltipster icon linkAzul"
                                 actionListener="#{ConsultaClienteControle.exportar}"
                                 oncomplete="#{ConsultaClienteControle.mensagemNotificar}#{ConsultaClienteControle.msgAlert}">
                    <f:attribute name="lista" value="#{ConsultaClienteControle.listaConsulta}"/>
                    <f:attribute name="tipo" value="xls"/>
                    <f:attribute name="atributos"
                                 value="matricula=Matr�cula,nome=Nome,categoria=Categoria,rg=Rg,inicioContrato=In�cio Contrato,terminoContrato=T�rmino Contrato,situacao_Apresentar=Situa��o,profissao=Profiss�o"/>
                    <f:attribute name="prefixo" value="Clientes"/>
                    <i class="fa-icon-file-excel-o font16"></i>
                </a4j:commandLink>
                <a4j:commandLink id="exportarPdf" style="margin-left: 5px;"
                                 title="Exportar PDF" styleClass="tooltipster icon font16 linkAzul"
                                 actionListener="#{ConsultaClienteControle.exportar}"
                                 oncomplete="#{ConsultaClienteControle.mensagemNotificar}#{ConsultaClienteControle.msgAlert}">
                    <f:attribute name="lista" value="#{ConsultaClienteControle.listaConsulta}"/>
                    <f:attribute name="tipo" value="pdf"/>
                    <f:attribute name="atributos"
                                 value="matricula=Matr�cula,nome=Nome,categoria=Categoria,rg=RG,inicioContrato=In�cio Contrato,terminoContrato=T�rmino Contrato,situacao_Apresentar=Situa��o,profissao=Profiss�o"/>
                    <f:attribute name="prefixo" value="Clientes"/>
                    <i class="fa-icon-pdf-o font16"></i>
                </a4j:commandLink>
        </div>

    <table border="0" class="tituloPainelPesquisa cinza nobottom" style="min-width: 1000px; max-height: 40px !important;" width="100%" cellpadding="0" cellspacing="0">
        <tr style="max-height: 40px !important;">
            <td>
        <h:outputText value="Buscar:" style="margin-left: 5px; font-size: 14px;"/>
            </td>
            <td style="border-right: 1px solid #9E9E9E;">
        <h:selectOneMenu styleClass="newComboBox noBorder" id="consulta" required="true"
                         style="max-width: 100px;"
                         value="#{ConsultaClienteControle.controleConsulta.campoConsulta}">
            <f:selectItems value="#{ConsultaClienteControle.listaTipoConsultaComboCliente}"/>
        </h:selectOneMenu>
            </td>
            <td style="max-width: 300px;">
        <h:inputText id="valorConsulta" styleClass="tituloAzul inputBusca inputBuscaAlunoSimples"
                     onkeypress="consultarInput(event);"
                     style="color: #777777  !important;  min-width: 180px !important;"
                     value="#{ConsultaClienteControle.controleConsulta.valorConsulta}"/>
        <script>
            selectAllValor();
        </script>
            </td>
            <td style="border-right: 1px solid #9E9E9E; min-width: 26px;">
        <a4j:commandLink id="consultar" style="font-size: 20px;"
                         oncomplete="selectAllValor();"
                         actionListener="#{ConsultaClienteControle.consultarPaginadoListenerReset}"
                         reRender="idpainelClientes, ultimossete, totalItens, pnlPagiPosterior"
                         title="#{msg.msg_consultar_dados}" accesskey="2">
            <f:attribute name="paginaInicial" value="paginaInicial"/>
            <f:attribute name="tipoConsulta" value="detalhada"/>
            <i class="fa-icon-search" style="color: #29ABE2;"></i>
        </a4j:commandLink>
            </td>
            <td style="border-right: 1px solid #9E9E9E;">
        <h:selectOneMenu rendered="#{ConsultaClienteControle.permissaoConsultaInfoTodasEmpresas}"
                         styleClass="newComboBox noBorder"
                         style="vertical-align: middle; max-width: 210px;"
                         value="#{ConsultaClienteControle.consultaEmpresa}">
            <f:selectItems value="#{ConsultaClienteControle.listaEmpresas}"/>
            <a4j:support event="onchange" actionListener="#{ConsultaClienteControle.consultarPaginadoListenerReset}"
                         reRender="idpainelClientes, ultimossete, totalItens, pnlPagiPosterior">
                <f:attribute name="paginaInicial" value="paginaInicial"/>
                <f:attribute name="tipoConsulta" value="empresa"/>
            </a4j:support>
        </h:selectOneMenu>
            </td>
            <td style="border-right: 1px solid #9E9E9E;">
        <h:selectOneMenu styleClass="newComboBox noBorder" style="vertical-align: middle; max-width: 110px;" id="categoria"
                         value="#{ConsultaClienteControle.consultaCategoria}">
            <f:selectItems value="#{ConsultaClienteControle.listaSelectItemCategoria}"/>
            <a4j:support event="onchange" actionListener="#{ConsultaClienteControle.consultarPaginadoListenerReset}"
                         reRender="idpainelClientes, ultimossete, totalItens, pnlPagiPosterior">
                <f:attribute name="paginaInicial" value="paginaInicial"/>
                <f:attribute name="tipoConsulta" value="detalhada"/>
            </a4j:support>
        </h:selectOneMenu>
            </td>
            <td style="border-right: 1px solid #9E9E9E;">
        <h:selectOneMenu id="situacao" styleClass="newComboBox noBorder" style="vertical-align:middle; max-width: 100px;"
                         value="#{ConsultaClienteControle.consultaSituacao}">
            <f:selectItem itemValue="" itemLabel="Situa��o"/>
            <f:selectItems value="#{ConsultaClienteControle.listaSelectItemSituacaoCliente}"/>
            <a4j:support event="onchange" actionListener="#{ConsultaClienteControle.consultarPaginadoListenerReset}"
                         reRender="idpainelClientes, ultimossete, totalItens, pnlPagiPosterior">
                <f:attribute name="paginaInicial" value="paginaInicial"/>
                <f:attribute name="tipoConsulta" value="detalhada"/>
            </a4j:support>
        </h:selectOneMenu>
            </td>
            <td>
        <h:selectOneMenu  styleClass="newComboBox noBorder" style="vertical-align:middle;"
                          value="#{ConsultaClienteControle.valorUltimosCadastros}"
                          id="ultimossete">
            <f:selectItem itemValue="0" itemLabel="Consultar �ltimos 7 dias por"/>
            <f:selectItem itemValue="1" itemLabel="Contratos"/>
            <f:selectItem itemValue="2" itemLabel="BVs"/>
            <a4j:support event="onchange" actionListener="#{ConsultaClienteControle.consultarPaginadoListenerUltimos7Dias}"
                         reRender="idpainelClientes, totalItens"/>
        </h:selectOneMenu>
            </td>
        </tr>
    </table>

    <h:panelGroup id="pnlPagiPosterior" layout="block">
        <a4j:commandLink id="pagiPosteriorTop" styleClass="tituloCampos" status="false"
                         reRender="idpainelClientes" oncomplete="carregarTooltipster();"
                         rendered="#{ConsultaClienteControle.confPaginacao.apresentarPosterior}"
                         actionListener="#{ConsultaClienteControle.consultarPaginadoListener}">
            <f:attribute name="pagNavegacao" value="pagPosterior"/>
        </a4j:commandLink>

        <a4j:commandLink id="pagiPosteriorTopHide" styleClass="tituloCampos" status="false"
                         style="display:none;"
                         reRender="idpainelClientes" oncomplete="carregarTooltipster();"
                         rendered="#{ConsultaClienteControle.confPaginacao.apresentarPosterior}"
                         actionListener="#{ConsultaClienteControle.consultarPaginadoListener}">
            <f:attribute name="pagNavegacao" value="pagPosterior"/>
        </a4j:commandLink>
    </h:panelGroup>

        <div class="scrollInfinito">
            <h:panelGroup id="idpainelClientes">
                <table id="items" class="pure-g-r pure-u-11-12 margin-0-auto dataTable tableCliente"
                       notranslate="notranslate">
                    <thead>
                    <th></th>
                    <th>
                        <a onclick="ordernar('cli.codigomatricula');">
                            <h:outputText value="#{msg_aplic.prt_Cliente_label_mat}"/>
                            <c:if test="${ConsultaClienteControle.orderBy eq 'cli.codigomatricula' and ConsultaClienteControle.orderByAD eq 'ASC'}">
                                <i class="fa-icon-caret-up"></i>
                            </c:if>
                            <c:if test="${ConsultaClienteControle.orderBy eq 'cli.codigomatricula' and ConsultaClienteControle.orderByAD eq 'DESC'}">
                                <i class="fa-icon-caret-down"></i>
                            </c:if>
                        </a>
                    </th>
                    <th>
                        <a onclick="ordernar('pes.nome');">
                            <h:outputText value="#{msg_aplic.prt_Cliente_label_nome}"/>
                            <c:if test="${ConsultaClienteControle.orderBy eq 'pes.nome' and ConsultaClienteControle.orderByAD eq 'ASC'}">
                                <i class="fa-icon-caret-up"></i>
                            </c:if>
                            <c:if test="${ConsultaClienteControle.orderBy eq 'pes.nome' and ConsultaClienteControle.orderByAD eq 'DESC'}">
                                <i class="fa-icon-caret-down"></i>
                            </c:if>
                        </a>
                    </th>
                    <h:panelGroup rendered="#{ConsultaClienteControle.consultaEmpresa == 0}">
                        <th>
                            <a onclick="ordernar('cli.empresaNome');">
                                <h:outputText value="Empresa"/>
                                <c:if test="${ConsultaClienteControle.orderBy eq 'cli.empresaNome' and ConsultaClienteControle.orderByAD eq 'ASC'}">
                                    <i class="fa-icon-caret-up"></i>
                                </c:if>
                                <c:if test="${ConsultaClienteControle.orderBy eq 'cli.empresaNome' and ConsultaClienteControle.orderByAD eq 'DESC'}">
                                    <i class="fa-icon-caret-down"></i>
                                </c:if>
                            </a>
                        </th>
                    </h:panelGroup>
                    <th>
                        <a onclick="ordernar('cat.nome');">
                            <h:outputText value="#{msg_aplic.prt_Cliente_label_categoria}"/>
                            <c:if test="${ConsultaClienteControle.orderBy eq 'cat.nome' and ConsultaClienteControle.orderByAD eq 'ASC'}">
                                <i class="fa-icon-caret-up"></i>
                            </c:if>
                            <c:if test="${ConsultaClienteControle.orderBy eq 'cat.nome' and ConsultaClienteControle.orderByAD eq 'DESC'}">
                                <i class="fa-icon-caret-down"></i>
                            </c:if>
                        </a>
                    </th>
                    <th class="centro">
                        <a onclick="ordernar('scsDW.datavigenciade');">
                            <h:outputText value="#{msg_aplic.prt_Cliente_inicioContrato}"/>
                            <c:if test="${ConsultaClienteControle.orderBy eq 'scsDW.datavigenciade' and ConsultaClienteControle.orderByAD eq 'ASC'}">
                                <i class="fa-icon-caret-up"></i>
                            </c:if>
                            <c:if test="${ConsultaClienteControle.orderBy eq 'scsDW.datavigenciade' and ConsultaClienteControle.orderByAD eq 'DESC'}">
                                <i class="fa-icon-caret-down"></i>
                            </c:if>
                        </a>
                    </th>
                    <th class="centro">
                        <a onclick="ordernar('scsDW.datavigenciaateajustada');">
                            <h:outputText value="#{msg_aplic.prt_Cliente_fim}"/>
                            <c:if test="${ConsultaClienteControle.orderBy eq 'scsDW.datavigenciaateajustada' and ConsultaClienteControle.orderByAD eq 'ASC'}">
                                <i class="fa-icon-caret-up"></i>
                            </c:if>
                            <c:if test="${ConsultaClienteControle.orderBy eq 'scsDW.datavigenciaateajustada' and ConsultaClienteControle.orderByAD eq 'DESC'}">
                                <i class="fa-icon-caret-down"></i>
                            </c:if>
                        </a>
                    </th>
                    <th>
                        <a onclick="ordernar('scsDW.situacao');">
                            <h:outputText value="#{msg_aplic.prt_Cliente_label_situacao}"/>
                            <c:if test="${ConsultaClienteControle.orderBy eq 'scsDW.situacao' and ConsultaClienteControle.orderByAD eq 'ASC'}">
                                <i class="fa-icon-caret-up"></i>
                            </c:if>
                            <c:if test="${ConsultaClienteControle.orderBy eq 'scsDW.situacao' and ConsultaClienteControle.orderByAD eq 'DESC'}">
                                <i class="fa-icon-caret-down"></i>
                            </c:if>
                        </a>
                    </th>
                    <th></th>
                    </thead>
                    <tbody>
                    <a4j:repeat value="#{ConsultaClienteControle.listaConsulta}" var="cliente" id="tabelaCliente">
                        <tr>
                            <td style="padding: 2px !important;">
                                <a4j:outputPanel style="width: 100%; text-align: center;" styleClass="containerfoto">
                                    <h:graphicImage style="width:43px;height:43px; border-radius: 50%;"
                                                    styleClass="tooltipsterright"
                                                    url="#{cliente.urlFoto}">
                                    </h:graphicImage>
                                </a4j:outputPanel>


                            </td>
                            <td class="noPadding">
                                <h:commandLink action="#{ClientesMarcadosControle.abrirClienteMarcandoComoRecente}"
                                        onclick="Richfaces.showModalPanel('panelStatus1');resetTime(tempoEmMillis);"
                                        title="Matricula do aluno"
                                        target="_self">
                                    <h:outputText value="#{cliente.matricula}"/>
                                </h:commandLink>

                            </td>
                            <td class="noPadding">
                                <h:commandLink action="#{ClientesMarcadosControle.abrirClienteMarcandoComoRecente}"
                                        onclick="Richfaces.showModalPanel('panelStatus1');resetTime(tempoEmMillis);"
                                        title="Nome do aluno"
                                        target="_self">
                                    <h:outputText id="nomeCliente" value="#{cliente.nomeMin}"/>
                                </h:commandLink>
                            </td>
                            <h:panelGroup rendered="#{ConsultaClienteControle.consultaEmpresa == 0}">
                                <td class="noPadding">
                                    <h:commandLink action="#{ClientesMarcadosControle.abrirClienteMarcandoComoRecente}"
                                            onclick="Richfaces.showModalPanel('panelStatus1');resetTime(tempoEmMillis);"
                                            title="Empresa do cliente"
                                            target="_self">
                                        <h:outputText value="#{cliente.empresaNome}"/>
                                    </h:commandLink>
                                </td>
                            </h:panelGroup>
                            <td class="noPadding">
                                <h:commandLink action="#{ClientesMarcadosControle.abrirClienteMarcandoComoRecente}"
                                        onclick="Richfaces.showModalPanel('panelStatus1');resetTime(tempoEmMillis);"
                                        title="Categoria do aluno"
                                        target="_self">
                                    <h:outputText value="#{cliente.categoria}"/>
                                </h:commandLink>
                            </td>
                            <td class="noPadding centro">
                                <h:commandLink action="#{ClientesMarcadosControle.abrirClienteMarcandoComoRecente}"
                                        onclick="Richfaces.showModalPanel('panelStatus1');resetTime(tempoEmMillis);"
                                        title="Nova tela de Cliente"
                                        target="_self">
                                    <h:outputText value="#{cliente.inicioContrato}">
                                        <f:convertDateTime pattern="dd/MM/yyyy" locale="pt"
                                                           timeZone="America/Sao_Paulo"/>
                                    </h:outputText>
                                </h:commandLink>

                            </td>
                            <td class="noPadding centro">
                                <h:commandLink action="#{ClientesMarcadosControle.abrirClienteMarcandoComoRecente}"
                                        onclick="Richfaces.showModalPanel('panelStatus1');resetTime(tempoEmMillis);"
                                        title="Nova tela de Cliente"
                                        target="_self">
                                    <h:outputText value="#{cliente.terminoContrato}">
                                        <f:convertDateTime pattern="dd/MM/yyyy" locale="pt"
                                                           timeZone="America/Sao_Paulo"/>
                                    </h:outputText>
                                </h:commandLink>
                            </td>
                            <td class="noPadding" align="center">

                                <h:panelGroup id="pnlSituacoes" layout="block" style="display: flex">
                                    <h:panelGroup layout="block" id="sitDependente"
                                                  style="width: 18px;height: 18px;display: flex;float: left;background-color: #bbb;border-radius: 50%;justify-content: center;align-items: center;margin-top: 2px"
                                                  rendered="#{cliente.dependentePlanoCompartilhado}">
                                        <h:outputText styleClass="tooltipster" style="font-weight: bold; font-size: 8px" value="DP"/>
                                    </h:panelGroup>

                                    <h:graphicImage id="alunoFreePass" style="width: 20px; height: 20px;"
                                                    value="/images/botaoFreePass.png" rendered="#{cliente.visitanteFreePass}"/>
                                    <h:graphicImage id="alunoAtivo" style="width: 20px; height: 20px;"
                                                    value="/images/botaoAtivo.png" rendered="#{cliente.ativo}"/>
                                    <h:graphicImage id="alunoInativo" style="width: 20px; height: 20px;"
                                                    value="/images/botaoInativo.png" rendered="#{cliente.inativo}"/>
                                    <h:graphicImage id="alunoVisitante" style="width: 20px; height: 20px;"
                                                    value="/images/botaoVisitante.png" rendered="#{cliente.visitante}"/>
                                    <h:graphicImage id="alunoTrancado" style="width: 20px; height: 20px;"
                                                    value="/images/botaoTrancamento.png" rendered="#{cliente.trancado}"/>
                                    <h:graphicImage id="alunoNormal" style="width: 20px; height: 20px;"
                                                    value="/images/botaoNormal.png" rendered="#{cliente.ativoNormal}"/>
                                    <h:graphicImage id="alunoTrancadoVencido" style="width: 20px; height: 20px;"
                                                    value="/images/botaoTrancadoVencido.png" rendered="#{cliente.trancadoVencido}"/>
                                    <h:graphicImage id="alunoAulaAvulsa" style="width: 20px; height: 20px;"
                                                    value="/images/botaoAulaAvulsa.png" rendered="#{cliente.visitanteAulaAvulsa}"/>
                                    <h:graphicImage id="alunoDiaria" style="width: 20px; height: 20px;"
                                                    value="/images/botaoDiaria.png" rendered="#{cliente.visitanteDiaria}"/>
                                    <h:graphicImage id="alunoCancelado" style="width: 20px; height: 20px;"
                                                    value="/images/botaoCancelamento.png" rendered="#{cliente.inativoCancelamento}"/>
                                    <h:graphicImage id="alunoDesistente" style="width: 20px; height: 20px;"
                                                    value="/images/botaoDesistente.png" rendered="#{cliente.inativoDesistente}"/>
                                    <h:graphicImage id="alunoAVencer" style="width: 20px; height: 20px;"
                                                    value="/images/botaoAvencer.png" rendered="#{cliente.ativoAvencer}"/>
                                    <h:graphicImage id="alunoVencido" style="width: 20px; height: 20px;"
                                                    value="/images/botaoVencido.png" rendered="#{cliente.inativoVencido}"/>
                                    <h:graphicImage id="alunoCarencia" style="width: 18px; height: 18px;"
                                                    value="./imagens/botaoCarencia.png" rendered="#{cliente.ativoCarencia}"/>
                                    <h:graphicImage id="alunoAtestado" style="width: 20px; height: 20px;"
                                                    value="/images/botaoAtestado.png" rendered="#{cliente.ativoAtestado}"/>
                                </h:panelGroup>
                            </td>
                        </tr>
                    </a4j:repeat>

                    </tbody>
                </table>

                <c:if test="${ConsultaClienteControle.mostrarCarregando}">
                    <div id="divfundo">
                        <div class="spinnerCarregando">
                            <div class="bounce1"></div>
                            <div class="bounce2"></div>
                            <div class="bounce3"></div>
                        </div>
                    </div>
                </c:if>
            </h:panelGroup>


        </div>

    </div>
    </div>
