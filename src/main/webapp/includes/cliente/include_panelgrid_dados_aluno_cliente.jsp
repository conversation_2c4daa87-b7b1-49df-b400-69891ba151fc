<%-- 
    Document   : include_panelgrid_dados_aluno_cliente
    Created on : 30/04/2012, 15:43:37
    Author     : Waller
--%>
<%@include file="../imports.jsp" %>
<h:panelGroup>
    <table width="100%" border="0" align="left" cellpadding="0"
           cellspacing="0" bgcolor="#e6e6e6"
           style="margin-right: 10px; margin-bottom: 10px; padding: 10px;">
        <tr>
            <td align="left" valign="top" style="padding-bottom: 5px;">
                <div style="clear: both;" class="text">
                    <p style="margin-bottom: 6px;"><img
                            src="images/arrow2.gif" width="16" height="16"
                            style="vertical-align: middle; margin-right: 6px;"><h:outputText
                            style="font-weight: bold" value="Dados do Aluno" /></p>
                    <div class="sep" style="margin: 4px 0 5px 0;"><img
                            src="images/shim.gif"></div>
                </div>
            </td>
        </tr>
        <tr>
            <td align="left" valign="top"><h:panelGrid
                    id="dadosAluno" columns="1" columnClasses="left, right"
                    width="100%" border="0" cellspacing="0" cellpadding="0"
                    styleClass="textsmall">
                    <h:panelGroup>
                        <h:outputText style="font-weight: bold" value="Empresa: " />

                        <h:outputText id="cliEmpresa" style="font-weight: bold"
                                      styleClass="blue"
                                      value="#{ClienteControle.clienteVO.empresa.nome}" />
                    </h:panelGroup>
                    <h:panelGroup id="saldo">
                        <h:outputText style="font-weight: bold"
                                      value="Saldo Conta Academia: " />
                        <h:outputText style="font-weight: bold" id="simb" styleClass="#{ClienteControle.corVermelhaSaldoNegativoCorVerdeSaldoPositivo}"
                                      value="R$ " />
                        <a4j:commandLink style="font-weight: bold"
                                         styleClass="#{ClienteControle.corVermelhaSaldoNegativoCorVerdeSaldoPositivo}"
                                         action="#{MovimentoContaCorrenteClienteControle.novo}"
                                         oncomplete="abrirPopup('movimentoContaCorrenteClienteCons.jsp', 'MovimentoContaCorrenteCliente', 800, 595);">
                            <h:outputText id="valorClienteCC" styleClass="#{ClienteControle.corVermelhaSaldoNegativoCorVerdeSaldoPositivo}"
                                          value="#{ClienteControle.clienteVO.saldoContaCorrente}">
                                <f:converter converterId="FormatadorNumerico" />
                            </h:outputText>
                        </a4j:commandLink>
                        <rich:spacer width="12px"/>
                        <a4j:commandButton id="debitoContaCorrente"
                                           rendered="#{ClienteControle.debitoContaCorrente}"
                                           alt="Receber D�bito Conta Corrente"
                                           image="./imagens/dinheiro1.png"
                                           oncomplete="abrirPopup('gerarProdutoContaCorrenteForm.jsp', 'gerarProdutoContaCorrenteCliente', 780, 595);"
                                           action="#{MovimentoContaCorrenteClienteControle.novoGerarParcelaDebito}"
                                           reRender="form:panelMensagemInferior,form:panelMensagemSuperior, form:saldo"
                                           title="Receber D�bito da Conta Corrente"/>
                        <rich:spacer width="5px"/>
                        <h:outputLink value="#{SuperControle.urlWiki}Recebimentos:D�bito_Conta_Corrente"
                                      rendered="#{ClienteControle.debitoContaCorrente}"
                                      title="Clique e saiba mais: Recebimento de D�bito de Conta Corrente" target="_blank">
                            <h:graphicImage styleClass="linkWiki" style="padding-bottom:10px;" url="imagens/wiki_link2.gif"/>
                        </h:outputLink>
                        <rich:spacer width="5px"/>
                        <a4j:commandButton id="devolucaoContaCorrente1"
                                           rendered="#{ClienteControle.debitoContaCorrente}"
                                           alt="Zerar/Alterar d�bitos da conta corrente"
                                           image="./imagens/devolver.png"
                                           oncomplete="abrirPopup('ajustesSaldoContaCorrente.jsp', 'AjusteSaldoContaCorrenteCliente', 780, 595);"
                                           title="Cancelar d�bitos da conta corrente"/>


                        <a4j:commandButton id="transferenciaContaCorrente"
                                           rendered="#{ClienteControle.permitirTransferenciaCredito}"
                                           alt="Transferir Cr�dito Conta Corrente"
                                           image="./imagens/transferencia.png"
                                           oncomplete="abrirPopup('transferenciaContaClienteForm.jsp', 'TranfereciaContaCorrenteCliente', 780, 595);"
                                           action="#{MovimentoContaCorrenteClienteControle.novoTransferencia}"
                                           reRender="form:panelMensagemInferior,form:panelMensagemSuperior, form:saldo"
                                           title="Transferir Cr�dito da Conta Corrente"/>
                        <rich:spacer width="5px"/>
                        <h:outputLink value="#{SuperControle.urlWiki}Recebimentos:Transferencia_Conta_Corrente"
                                      rendered="#{ClienteControle.permitirTransferenciaCredito}"
                                      title="Clique e saiba mais: Transfer�ncia de Cr�dito de Conta Corrente" target="_blank">
                            <h:graphicImage styleClass="linkWiki" style="padding-bottom:10px;" url="imagens/wiki_link2.gif"/>
                        </h:outputLink>

                         <a4j:commandButton id="devolucaoContaCorrente"
                                           rendered="#{ClienteControle.permitirTransferenciaCredito}"
                                           alt="Devolver Cr�dito Conta Corrente"
                                           image="./imagens/devolver.png"
                                           oncomplete="abrirPopup('ajustesSaldoContaCorrente.jsp', 'AjusteSaldoContaCorrenteCliente', 780, 595);"
                                           title="Devolver cr�dito da conta corrente"/>

                        <rich:spacer width="5px"/>
                        
                    </h:panelGroup>
                    <h:panelGroup>
                        <h:outputText style="font-weight: bold" value="Idade: " />
                        <h:outputText id="cliIdade" style="font-weight: bold"
                                      styleClass="blue"
                                      value="#{ClienteControle.idadeCliente}" />
                    </h:panelGroup>
                    <h:panelGroup>
                        <h:outputText style="font-weight: bold" value="Sexo biol�gico: " />
                        <h:outputText id="cliSexo" style="font-weight: bold"
                                      styleClass="blue"
                                      value="#{ClienteControle.clienteVO.pessoa.sexo_Apresentar}" />
                    </h:panelGroup>
                    <h:panelGroup>
                        <h:dataTable id="vinculos" width="100%"
                                     rowClasses="textsmall" columnClasses="colunaEsquerda"
                                     value="#{ClienteControle.clienteVO.vinculoVOs}"
                                     var="vinculo">
                            <h:column>
                                <h:outputText style="font-weight: bold"
                                              value="#{vinculo.tipoVinculo_Apresentar}" />
                            </h:column>
                            <h:column>
                                <h:outputText style="font-weight: bold"
                                              styleClass="blue"
                                              value="#{vinculo.colaborador.pessoa.nome}" />
                            </h:column>
                        </h:dataTable>
                    </h:panelGroup>
                    <h:panelGrid columns="2">
                        <a4j:commandLink rendered="#{LoginControle.apresentarLinkTREINO}" 
                                      style="margin: 0 0 0 0"
                                      styleClass="botoes nvoBt"
                                      action="#{ClienteControle.addAlunoTreino}"
                                      oncomplete="#{ClienteControle.msgAlert}">Add aluno treino</a4j:commandLink>
                                      
                                      <a4j:commandButton image="/imagens/history.png"
                                                         title="Ver Hist�rico V�nculo"
                                                         styleClass="botoes"
                                                         value="Hist�rico de V�nculos"
                                                         reRender="formHistoricoVinculo,panelListaHistoricoVinculo"
                                                         action="#{HistoricoVinculoControle.historicoCliente}"
                                                         oncomplete="Richfaces.showModalPanel('modalPanelHistoricoVinculo');"/>
                    </h:panelGrid>
                </h:panelGrid></td>
        </tr>
    </table>
</h:panelGroup>
