<%@page contentType="text/html" %>
<%@page pageEncoding="UTF-8" %>
<%@include file="../imports.jsp" %>

<h:panelGroup rendered="#{not empty TelaClienteControle.listaContratosDependentes}">
    <table class="googleAnalytics tblHeaderLeft semZebra">
        <thead>
        <tr>
            <th><h:outputText styleClass="texto-size-14 cinza negrito" value="CONTRATO"/></th>
            <th><h:outputText styleClass="texto-size-14 cinza negrito" value="INÍCIO"/></th>
            <th><h:outputText styleClass="texto-size-14 cinza negrito" value=""/></th>
            <th><h:outputText styleClass="texto-size-14 cinza negrito" value="FIM"/></th>
            <th><h:outputText styleClass="texto-size-14 cinza negrito" value="TITULAR"/></th>
            <th><h:outputText styleClass="texto-size-14 cinza negrito" value="DEPENDÊNCIA"/></th>
            <th><h:outputText styleClass="texto-size-14 cinza negrito" value="AÇÕES"/></th>
        </tr>
        </thead>
        <tbody>
        <a4j:repeat id="listaContratosDependente"
                    value="#{TelaClienteControle.listaContratosDependentes}"
                    rowKeyVar="index"
                    var="contratoDependente">
            <tr>
                <td>
                    <h:outputText styleClass="texto-size-16" value="#{contratoDependente.contrato.codigo}"/>
                </td>

                <td style="width: 8%;">
                    <h:outputText style="float: left;" styleClass="texto-size-16 cinza" value="#{contratoDependente.dataInicio}">
                        <f:convertDateTime pattern="dd/MM/yyyy"/>
                    </h:outputText>
                </td>

                <td style="width: 15%;">
                    <div>
                        <h:panelGroup layout="block"
                                      style="height: 10px; width: 10px; position: absolute; background-color: #{TelaClienteControle.hoje > contratoDependente.dataInicio ? 'black' : '#29ABE2'}; border-radius: 50px; margin-top: -4px;"/>
                        <h:panelGroup layout="block"
                                      style="height: 2px; width: #{contratoDependente.percentualAndamento < 0.0  ? 0.0: contratoDependente.percentualAndamento}%; float: left; background-color: black;"/>
                        <h:panelGroup
                                style="float: right;height: 2px; width: #{contratoDependente.percentualAndamento < 0.0 ? 100.0 : 100 - contratoDependente.percentualAndamento}%; float: left; background-color: #29ABE2;"/>
                        <h:panelGroup layout="block"
                                      style="float: right; height: 10px; width: 10px; background-color: #{TelaClienteControle.hoje > contratoDependente.dataFinalAjustada ? 'black' : '#29ABE2'}; border-radius: 50px; margin-top: -6px;"/>
                    </div>
                </td>

                <td>
                    <h:outputText styleClass="texto-size-16 cinza" value="#{contratoDependente.dataFinalAjustada}">
                        <f:convertDateTime pattern="dd/MM/yyyy"/>
                    </h:outputText>
                </td>

                <td>
                    <h:outputText styleClass="texto-size-14 cinza"
                                  rendered="#{contratoDependente.contrato.pessoaOriginal == null || contratoDependente.contrato.pessoaOriginal.codigo == 0}"
                                  value="#{contratoDependente.contrato.nome_Apresentar}"/>
                    <h:outputText styleClass="texto-size-14 cinza"
                                  rendered="#{contratoDependente.contrato.pessoaOriginal != null && contratoDependente.contrato.pessoaOriginal.codigo != 0}"
                                  value="#{contratoDependente.contrato.pessoaOriginal.nome}"/>
                </td>

                <td>
                    <h:outputText styleClass="texto-size-14 cinza" value="#{contratoDependente.posicaoDependente}"/>
                </td>

                <td style="text-align: right;">
                    <c:if test="${not param.readOnly}">
                        <h:panelGroup layout="block" style="display: flex; justify-content: flex-end;">
                            <a4j:commandLink reRender="pnlAfastamentoDependente"
                                             style="margin-left: 10px; width: 20px"
                                             id="afastamentoContratoDependente"
                                             oncomplete="#{AfastamentoContratoDependenteControle.mensagemNotificar}#{AfastamentoContratoDependenteControle.msgAlert}"
                                             styleClass="texto-cor-azul texto-size-14 linkPadrao tooltipster"
                                             action="#{AfastamentoContratoDependenteControle.novo}"
                                             title="Lançar afastamento">
                                <i id="imprimirContrato" class="fa-icon-pause"></i>
                                <f:setPropertyActionListener value="#{contratoDependente}" target="#{AfastamentoContratoDependenteControle.contratoDependenteVO}"/>
                            </a4j:commandLink>

                            <a4j:commandLink reRender="pnlListaAfastamentos"
                                             style="margin-left: 10px; width: 20px"
                                             id="listaAfastamentosContratoDependente"
                                             oncomplete="#{AfastamentoContratoDependenteControle.mensagemNotificar}#{AfastamentoContratoDependenteControle.msgAlert}"
                                             styleClass="texto-cor-azul texto-size-14 linkPadrao tooltipster"
                                             action="#{AfastamentoContratoDependenteControle.listarAfastamentos}"
                                             title="Afastamentos lançados">
                                <i id="imprimirContrato" class="fa-icon-list"></i>
                                <f:setPropertyActionListener value="#{contratoDependente}" target="#{AfastamentoContratoDependenteControle.contratoDependenteVO}"/>
                            </a4j:commandLink>
                        </h:panelGroup>
                    </c:if>
                </td>
            </tr>
        </a4j:repeat>
        </tbody>
    </table>
</h:panelGroup>


