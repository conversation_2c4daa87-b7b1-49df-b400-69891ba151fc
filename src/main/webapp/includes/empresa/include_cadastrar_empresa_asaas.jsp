<%@page contentType="text/html" pageEncoding="ISO-8859-1" %>
<%@include file="../imports.jsp" %>

<div style="margin-left: 44%;">
    <h:graphicImage id="iconAsaas"
                    styleClass="tooltipster"
                    width="100px"
                    value="../imagens/logo-asaas.png"
                    title="Asaas é um gateway de pagamentos ao qual o zw possui integração. O Asaas possui soluções de Boleto e Pix."/>
</div>

<h:panelGrid width="100%" columns="1" id="panelCadastroAsaas">

    <%--CADASTRAR NOVO--%>
    <h:panelGrid id="panelCadastrarAsaas"
                 rendered="#{!EmpresaControle.possuiCadastroAsaas}"
                 columns="2" rowClasses="linhaImpar, linhaPar"
                 columnClasses="classEsquerda, classDireita" width="100%"
                 headerClass="subordinado">

        <f:facet name="header">
            <h:outputText value="Realizar Integração com o Asaas"/>
        </f:facet>

        <%--Desativado--%>
        <h:outputText styleClass="tituloCampos tooltipster"
                      value="Status da Integração:"/>
        <h:panelGroup id="desativado" style="display: block; margin-top: 10px">
        <h:panelGroup style="display: block; display: inline-table; margin-top: -9px;">
            <a4j:commandLink style="background-color: #777777 !important; border-radius: 20px!important; padding: 1px 9px 1px 10px!important"
                             value="Desativada"
                             oncomplete="#{ConvenioCobrancaControle.mensagemNotificar};#{ConvenioCobrancaControle.onComplete}"
                             title="Você ainda não realizou a integração com o Asaas. Para realizar a integração, preencha os campos abaixo e depois clique em \"Cadastrar\"."
                             reRender="form"
                             styleClass="botaoPrimario texto-size-14-real tooltipster"/>
        </h:panelGroup>
        </h:panelGroup>

        <h:outputText styleClass="tituloCampos tooltipster"
                      value="Ambiente:"
                      title="Informe o ambiente para o cadastro da conta Asaas"/>
        <h:outputText
                rendered="#{!LoginControle.usuarioLogado.usuarioPactoSolucoes}"
                value="#{EmpresaControle.ambienteAsaas.descricao}"/>
        <h:selectOneMenu id="listaSelectItemAmbienteAsaas" styleClass="form"
                         rendered="#{LoginControle.usuarioLogado.usuarioPactoSolucoes}"
                         value="#{EmpresaControle.asaasEmpresaVO.ambienteEnum}">
            <f:selectItems value="#{EmpresaControle.listaSelectItemAmbiente}"/>
        </h:selectOneMenu>

        <h:outputText styleClass="tituloCampos tooltipster"
                      title="Informe o nome da empresa para o cadastro no portal Asaas"
                      value="Nome da empresa:"/>
        <h:inputText value="#{EmpresaControle.asaasEmpresaVO.name}"
                     title="O nome da empresa informado aqui aparecerá em todos os boletos/pix gerados para os alunos.</br> Recomendamos deixar igual ao que está no cadastro da empresa dentro do nosso sistema"
                     styleClass="tooltipster"
                     size="40"/>

        <h:outputText styleClass="tituloCampos tooltipster"
                      title="O CNPJ deve ser o mesmo do cadastro da empresa dentro do nosso sistema"
                      value="CNPJ:"/>
        <h:inputText size="20" maxlength="18" id="cnpjasaas"
                     onkeypress="return mascara(this.form, 'form:cnpjasaas', '99.999.999/9999-99', event);"
                     disabled="true"
                     title="O CNPJ deve ser o mesmo do cadastro da empresa dentro do nosso sistema"
                     onblur="blurinput(this);" onfocus="focusinput(this);" styleClass="form tooltipster"
                     value="#{EmpresaControle.asaasEmpresaVO.cpfCnpj}"/>

        <h:outputText styleClass="tituloCampos tooltipster"
                      title="Informe o tipo da sua empresa."
                      value="Tipo da Empresa:"/>
        <h:selectOneMenu id="tipoEmpresaAsaas" styleClass="form tooltipster" onblur="blurinput(this);"
                         title="Informe o tipo da sua empresa."
                         value="#{EmpresaControle.asaasEmpresaVO.companyType}"
                         onfocus="focusinput(this);">
            <f:selectItems value="#{EmpresaControle.listaSelectItemTipoEmpresaAsaas}"/>
        </h:selectOneMenu>

        <h:outputText styleClass="tituloCampos tooltipster"
                      title="Informe o email da empresa para o cadastro da Conta Asaas. Este email será usado mais tarde para login no portal Asaas."
                      value="Email:"/>
        <h:inputText value="#{EmpresaControle.asaasEmpresaVO.email}"
                     title="<b>Atenção: </b>Informe um email que você tenha acesso, pois o Asaas enviará confirmações para este email.</br> O seu login Asaas também será o email informado aqui."
                     styleClass="tooltipster"
                     size="50"/>

        <h:outputText styleClass="tituloCampos tooltipster"
                      title="Informe o telefone da empresa para o cadastro da Conta Asaa"
                      value="Telefone Fixo:"/>
        <h:panelGroup layout="block">
            <h:inputText value="#{EmpresaControle.asaasEmpresaVO.phone}" size="12" maxlength="13"
                         id="inputphone"
                         styleClass="form"
                         onkeypress="return mascara(this.form, this.id , '(99)9999-9999', event);"/>
        </h:panelGroup>

        <h:outputText styleClass="tituloCampos tooltipster"
                      title="Informe o telefone celular ao qual a conta Asaas será vinculada"
                      value="Telefone Celular:"/>
        <h:panelGroup layout="block">
            <h:inputText value="#{EmpresaControle.asaasEmpresaVO.mobilePhone}" size="12" maxlength="14"
                         id="inputmobphone"
                         title="<b>Atenção: </b>Informe um número de celular que você tenha acesso, pois o Asaas enviará confirmações SMS para este número.</br> O seu login Asaas também ficará vinculado com o número informado aqui."
                         styleClass="form tooltipster"
                         onkeypress="return mascara(this.form, this.id , '(99)99999-9999', event);"/>
        </h:panelGroup>


        <h:outputText styleClass="tituloCampos tooltipster"
                      title="Informe o endereço da sua empresa."
                      value="Endereço:"/>
        <h:panelGroup layout="block">
            <h:inputText value="#{EmpresaControle.asaasEmpresaVO.address}" size="50"/>
            <h:outputText styleClass="tituloCampos tooltipster"
                          style="margin-left: 6px;"
                          title="Informe o número do endereço da sua empresa. Caso não tenha, informar '0'."
                          value="Nº:"/>
            <h:inputText value="#{EmpresaControle.asaasEmpresaVO.addressNumber}" size="7"
                         styleClass="tooltipster"
                         id="numEndereco"
                         title="Informe o número do endereço da sua empresa. Caso não tenha, informar '0'."
                         onkeypress="return mascara(this.form, this.id, '9999999', event);"/>
        </h:panelGroup>


        <h:outputText styleClass="tituloCampos tooltipster"
                      title="Informe o bairro da sua Empresa"
                      value="Bairro:"/>
        <h:inputText value="#{EmpresaControle.asaasEmpresaVO.province}" size="50"/>

        <h:outputText styleClass="tituloCampos tooltipster"
                      title="Informe o CEP da sua Empresa"
                      value="CEP:"/>
        <h:inputText value="#{EmpresaControle.asaasEmpresaVO.postalCode}" id="cep"
                     onkeypress="return mascara(this.form, 'form:cep', '99999-999', event);"
                     size="8" maxlength="9"/>


        <h:outputText value=""/>
        <h:panelGroup layout="block" style="padding: 10px 0 10px">
            <a4j:commandLink value="Cadastrar"
                             style="margin: 0"
                             id="integrarAsaas"
                             styleClass="botoes nvoBt btSec tooltipster"
                             title="Realizar Integração com o Asaas"
                             oncomplete="#{EmpresaControle.mensagemNotificar}"
                             action="#{EmpresaControle.cadastrarEmpresaAsaas}"
                             reRender="panelCadastroAsaas"/>
        </h:panelGroup>
    </h:panelGrid>


    <%--EXIBIR CAMPOS QUANDO POSSUI INTEGRAÇÃO--%>
    <h:panelGrid id="panelAsaasCadastrado"
                 rendered="#{EmpresaControle.possuiCadastroAsaas}"
                 columns="2" rowClasses="linhaImpar, linhaPar"
                 columnClasses="classEsquerda, classDireita" width="100%"
                 headerClass="subordinado">

        <f:facet name="header">
            <h:outputText value="Dados da Integração Asaas"/>
        </f:facet>

        <%--Ativado--%>
        <h:outputText styleClass="tituloCampos tooltipster"
                      value="Status da Integração:"/>
        <h:panelGroup id="ativado" style="display: block;">
            <a4j:commandLink style="background-color: #00c350 !important; border-radius: 20px!important; padding: 1px 9px 1px 10px!important"
                             value="Ativado"
                             title="Foi identificado uma integração Ativa para esta empresa."
                             styleClass="botaoPrimario texto-size-14-real tooltipster"/>
        </h:panelGroup>


        <h:outputText styleClass="tituloCampos tooltipster"
                      value="Ambiente:"/>
        <h:outputText
                styleClass="tituloCampos tooltipster"
                value="#{EmpresaControle.asaasEmpresaVO.ambienteEnum.descricao}"/>

        <h:outputText styleClass="tituloCampos tooltipster"
                      title="Id obtido anteriormente no ato da integração com o Asaas"
                      value="ID ASAAS:"/>
        <h:panelGroup style="display: block">
            <h:outputText value="#{EmpresaControle.asaasEmpresaVO.id}"
                          title="Id obtido anteriormente no ato da integração com o Asaas"
                          styleClass="tooltipster"
                          style="font-size: 14px; padding: 5px;"/>
            <a4j:commandLink style="font-size: 15px;"
                             onclick="copiar('#{EmpresaControle.asaasEmpresaVO.id}')">
                <i class="fa-icon-copy tooltipster"
                   title="Clique para copiar para a área de transferência"></i>
            </a4j:commandLink>
        </h:panelGroup>

        <h:outputText styleClass="tituloCampos tooltipster"
                      title="Nome da empresa informado anteriormente no ato da integração com o Asaas"
                      value="Nome da empresa:"/>
        <h:outputText value="#{EmpresaControle.asaasEmpresaVO.name}"
                      title="Nome da empresa informado anteriormente no ato da integração com o Asaas"
                      styleClass="tooltipster"
                      style="font-size: 14px; padding: 5px;"/>

        <h:outputText styleClass="tituloCampos tooltipster"
                      title="CNPJ da empresa informado anteriormente no ato da integração com o Asaas"
                      value="CNPJ:"/>
        <h:outputText value="#{EmpresaControle.asaasEmpresaVO.cnpjApresentar}"
                      title="CNPJ da empresa informado anteriormente no ato da integração com o Asaas"
                      styleClass="tooltipster"
                      style="font-size: 14px; padding: 5px;"/>

        <h:outputText styleClass="tituloCampos tooltipster"
                      title="Tipo da empresa informado anteriormente no ato da integração com o Asaas"
                      value="Tipo da Empresa:"/>
        <h:outputText value="#{EmpresaControle.asaasEmpresaVO.companyTypeApresentar}"
                      title="Tipo da empresa informado anteriormente no ato da integração com o Asaas"
                      styleClass="tooltipster"
                      style="font-size: 14px; padding: 5px;"/>

        <h:outputText styleClass="tituloCampos tooltipster"
                      title="Email da empresa informado anteriormente no ato da integração com o Asaas"
                      value="Email:"/>
        <h:outputText value="#{EmpresaControle.asaasEmpresaVO.email}"
                      title="Email da empresa informado anteriormente no ato da integração com o Asaas"
                      styleClass="tooltipster"
                      style="font-size: 14px; padding: 5px;"/>

        <h:outputText styleClass="tituloCampos tooltipster"
                      title="Telefone Fixo da empresa informado anteriormente no ato da integração com o Asaas"
                      value="Telefone Fixo:"/>
        <h:outputText value="#{EmpresaControle.asaasEmpresaVO.phoneApresentar}"
                      title="Telefone Fixo da empresa informado anteriormente no ato da integração com o Asaas"
                      styleClass="tooltipster"
                      style="font-size: 14px; padding: 5px;"/>

        <h:outputText styleClass="tituloCampos tooltipster"
                      title="Telefone Celular da empresa informado anteriormente no ato da integração com o Asaas"
                      value="Telefone Celular:"/>
        <h:outputText value="#{EmpresaControle.asaasEmpresaVO.mobilePhoneApresentar}"
                      title="Telefone Celular da empresa informado anteriormente no ato da integração com o Asaas"
                      styleClass="tooltipster"
                      style="font-size: 14px; padding: 5px;"/>

        <h:outputText styleClass="tituloCampos tooltipster"
                      title="Endereço da empresa informado anteriormente no ato da integração com o Asaas"
                      value="Endereço:"/>
        <h:panelGroup layout="block">
            <h:outputText value="#{EmpresaControle.asaasEmpresaVO.address}"
                          title="Endereço da empresa informado anteriormente no ato da integração com o Asaas"
                          styleClass="tooltipster"
                          style="font-size: 14px; padding: 5px;"/>
            <h:outputText styleClass="tituloCampos tooltipster"
                          title="Nº do endereço informado anteriormente no ato da integração com o Asaas"
                          value="Nº:"/>
            <h:outputText value="#{EmpresaControle.asaasEmpresaVO.addressNumber}"
                          title="Nº do endereço informado anteriormente no ato da integração com o Asaas"
                          styleClass="tooltipster"
                          style="font-size: 14px; padding: 5px;"/>
        </h:panelGroup>

        <h:outputText styleClass="tituloCampos tooltipster"
                      title="Bairro da empresa informado anteriormente no ato da integração com o Asaas"
                      value="Bairro:"/>
        <h:outputText value="#{EmpresaControle.asaasEmpresaVO.province}"
                      title="Bairro da empresa informado anteriormente no ato da integração com o Asaas"
                      styleClass="tooltipster"
                      style="font-size: 14px; padding: 5px;"/>

        <h:outputText styleClass="tituloCampos tooltipster"
                      title="CEP do endereço da empresa informado anteriormente no ato da integração com o Asaas"
                      value="CEP:"/>
        <h:outputText value="#{EmpresaControle.asaasEmpresaVO.cepApresentar}"
                      title="CEP do endereço da empresa informado anteriormente no ato da integração com o Asaas"
                      styleClass="tooltipster"
                      style="font-size: 14px; padding: 5px;"/>

        <h:outputText styleClass="tituloCampos tooltipster"
                      title="Wallet ID obtido anteriormente no ato da integração com o Asaas"
                      value="Wallet ID:"/>
        <h:panelGroup style="display: block">
            <h:outputText value="#{EmpresaControle.asaasEmpresaVO.walletId}"
                          title="Wallet ID obtido no ato da integração com o Asaas"
                          styleClass="tooltipster"
                          style="font-size: 14px; padding: 5px;"/>
            <a4j:commandLink style="font-size: 15px;"
                             onclick="copiar('#{EmpresaControle.asaasEmpresaVO.walletId}')">
                <i class="fa-icon-copy tooltipster"
                   title="Clique para copiar para a área de transferência"></i>
            </a4j:commandLink>
        </h:panelGroup>

        <h:outputText styleClass="tituloCampos tooltipster"
                      title="API KEY resumida gerada no ato da integração com o Asaas. Clique no ícone ao lado para copiar completa."
                      value="Api Key:"/>
        <h:panelGroup style="display: block">
            <h:outputText value="#{EmpresaControle.asaasEmpresaVO.apiKeyApresentarResumida}"
                          title="API KEY resumida gerada no ato da integração com o Asaas. Clique no ícone ao lado para copiar completa."
                          styleClass="tooltipster"
                          style="font-size: 14px; padding: 5px;"/>
            <a4j:commandLink style="font-size: 15px;"
                             onclick="copiar('#{EmpresaControle.asaasEmpresaVO.apiKey}')">
                <i class="fa-icon-copy tooltipster"
                   title="Clique para copiar para a área de transferência"></i>
            </a4j:commandLink>
        </h:panelGroup>

        <%--Botões de consulta info Asaas--%>
        <h:outputText styleClass="tituloCampos tooltipster"
                      value="Consultar informações da conta Asaas:"/>

        <h:panelGrid id="gridAcoesContaAsaasEmpresa" style="display: grid; grid-template-columns: 1fr 1fr 1fr;" columns="3" rowClasses="linhaImpar, linhaPar"
                     columnClasses="classEsquerda, classDireita" width="100%">
            <a4j:commandButton styleClass="botoes nvoBt btSec tooltipster"
                               title="Ver taxas das cobranças da sua Conta Asaas."
                               id="consultarTaxasContaAsaasEmpresa"
                               value="Taxas"
                               reRender="modalTaxasContaAsaasEmpresa"
                               oncomplete="#{EmpresaControle.mensagemNotificar};#{EmpresaControle.onComplete}"
                               action="#{EmpresaControle.consultarTaxasContaAsaas}"/>
            <a4j:commandButton styleClass="botoes nvoBt btSec tooltipster"
                               title="Ver os dados comerciais da sua Conta Asaas."
                               id="consultarDadosContaAsaasEmpresa"
                               value="Dados comerciais"
                               reRender="modalDadosComerciaisContaAsaasEmpresa"
                               oncomplete="#{EmpresaControle.mensagemNotificar};#{EmpresaControle.onComplete}"
                               action="#{EmpresaControle.consultarDadosComerciaisContaAsaas}"/>
            <a4j:commandButton styleClass="botoes nvoBt btSec tooltipster"
                               title="Ver a situação cadastral da sua Conta Asaas."
                               id="consultarStatusContaAsaasEmpresa"
                               value="Situação cadastral"
                               reRender="modalStatusContaAsaasEmpresa"
                               oncomplete="#{EmpresaControle.mensagemNotificar};#{EmpresaControle.onComplete}"
                               action="#{EmpresaControle.consultarSituacaoCadastralContaAsaas}"/>
        </h:panelGrid>

        <%--EXCLUIR--%>
        <h:outputText styleClass="tituloCampos tooltipster"
                      value="Excluir do banco local:"
                      rendered="#{LoginControle.usuarioLogado.usuarioPactoSolucoes && EmpresaControle.asaasEmpresaVO.ambienteSandbox}"
                      title="Disponível apenas para contas criadas homologação/sandbox. Excluirá somente do banco de dados do nosso lado possibilitando cadastrar uma nova."/>
        <a4j:commandButton styleClass="botoes nvoBt btSec tooltipster"
                           rendered="#{LoginControle.usuarioLogado.usuarioPactoSolucoes && EmpresaControle.asaasEmpresaVO.ambienteSandbox}"
                           id="excluirAsaas"
                           value="Excluir"
                           title="Disponível apenas para contas criadas homologação/sandbox. Excluirá somente do banco de dados do nosso lado possibilitando cadastrar uma nova."
                           reRender="form"
                           oncomplete="#{EmpresaControle.mensagemNotificar}"
                           action="#{EmpresaControle.excluirContaAsaas}"/>

    </h:panelGrid>


    <%--Modal Taxas Conta Asaas--%>
    <rich:modalPanel id="modalTaxasContaAsaasEmpresa" styleClass="novaModal"
                     autosized="true" shadowOpacity="true" width="600" height="220">
        <f:facet name="header">
            <h:panelGroup>
                <h:outputText value="Taxas de cobranças Asaas"/>
            </h:panelGroup>
        </f:facet>
        <f:facet name="controls">
            <h:panelGroup>
                <h:outputText
                        styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign btnSelecionarModalidade"
                        id="hidelinkModalTaxasAsaas"/>
                <rich:componentControl for="modalTaxasContaAsaasEmpresa" attachTo="hidelinkModalTaxasAsaas"
                                       operation="hide"
                                       event="onclick">
                </rich:componentControl>
            </h:panelGroup>
        </f:facet>

        <a4j:form id="formModalTaxasContaAsaasEmpresa">
            <h:panelGrid id="panelGeralModalTaxasContaAsaas" columns="2" rowClasses="linhaImpar, linhaPar"
                         columnClasses="classEsquerda, classDireita" width="100%">

                <h:outputText styleClass="tituloCampos" value="Boleto Pago:"/>
                <h:outputText styleClass="tituloCampos"
                              value="#{EmpresaControle.taxasContaAsaasDTO.payment.taxasBoletoApresentar}"/>
                <h:outputText styleClass="tituloCampos" value="Pix Pago:"/>
                <h:outputText styleClass="tituloCampos"
                              value="#{EmpresaControle.taxasContaAsaasDTO.payment.taxasPixApresentar}"/>
                <h:outputText styleClass="tituloCampos tooltipster"
                              title="#{EmpresaControle.titleTaxaNotificacoesAsaas}"
                              value="Notificações:"/>
                <h:outputText styleClass="tituloCampos tooltipster"
                              title="#{EmpresaControle.titleTaxaNotificacoesAsaas}"
                              value="#{EmpresaControle.taxasContaAsaasDTO.taxasNotificacoesApresentar}"/>

            </h:panelGrid>
        </a4j:form>
    </rich:modalPanel>

    <%--Modal Dados Comerciais Conta Asaas--%>
    <rich:modalPanel id="modalDadosComerciaisContaAsaasEmpresa" styleClass="novaModal"
                     autosized="true" shadowOpacity="true" width="600" height="220">
        <f:facet name="header">
            <h:panelGroup>
                <h:outputText value="Dados Comerciais da Conta Asaas"/>
            </h:panelGroup>
        </f:facet>
        <f:facet name="controls">
            <h:panelGroup>
                <h:outputText
                        styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign btnSelecionarModalidade"
                        id="hidelinkModalDadosAsaas"/>
                <rich:componentControl for="modalDadosComerciaisContaAsaasEmpresa" attachTo="hidelinkModalDadosAsaas"
                                       operation="hide"
                                       event="onclick">
                </rich:componentControl>
            </h:panelGroup>
        </f:facet>

        <a4j:form id="formModalDadosContaAsaasEmpresa">
            <h:panelGrid id="panelGeralModalDadosContaAsaas" columns="2" rowClasses="linhaImpar, linhaPar"
                         columnClasses="classEsquerda, classDireita" width="100%">

                <h:outputText styleClass="tituloCampos" value="Status:"/>
                <h:outputText styleClass="tituloCampos"
                              value="#{EmpresaControle.dadosComerciaisContaAsaasDTO.status}"/>

                <h:outputText styleClass="tituloCampos" value="CNPJ:"/>
                <h:outputText styleClass="tituloCampos"
                              value="#{EmpresaControle.dadosComerciaisContaAsaasDTO.cpfCnpj}"/>

                <h:outputText styleClass="tituloCampos" value="Nome da Empresa:"/>
                <h:outputText styleClass="tituloCampos"
                              value="#{EmpresaControle.dadosComerciaisContaAsaasDTO.name}"/>

                <h:outputText styleClass="tituloCampos" value="Tipo da Empresa:"/>
                <h:outputText styleClass="tituloCampos"
                              value="#{EmpresaControle.dadosComerciaisContaAsaasDTO.companyType}"/>

                <h:outputText styleClass="tituloCampos" value="Tipo Pessoa:"/>
                <h:outputText styleClass="tituloCampos"
                              value="#{EmpresaControle.dadosComerciaisContaAsaasDTO.personType}"/>

                <h:outputText styleClass="tituloCampos" value="Email:"/>
                <h:outputText styleClass="tituloCampos"
                              value="#{EmpresaControle.dadosComerciaisContaAsaasDTO.email}"/>

                <h:outputText styleClass="tituloCampos" value="Telefone:"/>
                <h:outputText styleClass="tituloCampos"
                              value="#{EmpresaControle.dadosComerciaisContaAsaasDTO.phone}"/>

                <h:outputText styleClass="tituloCampos" value="Celular:"/>
                <h:outputText styleClass="tituloCampos"
                              value="#{EmpresaControle.dadosComerciaisContaAsaasDTO.mobilePhone}"/>

                <h:outputText styleClass="tituloCampos" value="Endereço:"/>
                <h:outputText styleClass="tituloCampos"
                              value="#{EmpresaControle.dadosComerciaisContaAsaasDTO.address}"/>

                <h:outputText styleClass="tituloCampos" value="Bairro:"/>
                <h:outputText styleClass="tituloCampos"
                              value="#{EmpresaControle.dadosComerciaisContaAsaasDTO.province}"/>

                <h:outputText styleClass="tituloCampos" value="CEP:"/>
                <h:outputText styleClass="tituloCampos"
                              value="#{EmpresaControle.dadosComerciaisContaAsaasDTO.postalCode}"/>

                <h:outputText styleClass="tituloCampos" value="Cidade:"/>
                <h:outputText styleClass="tituloCampos"
                              value="#{EmpresaControle.dadosComerciaisContaAsaasDTO.city}"/>
            </h:panelGrid>
        </a4j:form>
    </rich:modalPanel>


    <%--Modal Situação Cadastral Conta Asaas--%>
    <rich:modalPanel id="modalStatusContaAsaasEmpresa" styleClass="novaModal"
                     autosized="true" shadowOpacity="true" width="600" height="220">
        <f:facet name="header">
            <h:panelGroup>
                <h:outputText value="Situação cadastral da Conta Asaas"/>
            </h:panelGroup>
        </f:facet>
        <f:facet name="controls">
            <h:panelGroup>
                <h:outputText
                        styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign btnSelecionarModalidade"
                        id="hidelinkModalStatusAsaas"/>
                <rich:componentControl for="modalStatusContaAsaasEmpresa" attachTo="hidelinkModalStatusAsaas"
                                       operation="hide"
                                       event="onclick">
                </rich:componentControl>
            </h:panelGroup>
        </f:facet>

        <a4j:form id="formModalStatusContaAsaasEmpresa">
            <h:panelGrid id="panelGeralModalStatusContaAsaas" columns="2" rowClasses="linhaImpar, linhaPar"
                         columnClasses="classEsquerda, classDireita" width="100%">

                <h:outputText styleClass="tituloCampos" value="ID:"/>
                <h:outputText styleClass="tituloCampos"
                              value="#{EmpresaControle.situacaoCadastralContaAsaasDTO.id}"/>

                <h:outputText styleClass="tituloCampos" value="Informações Comerciais:"/>
                <h:outputText styleClass="tituloCampos"
                              value="#{EmpresaControle.situacaoCadastralContaAsaasDTO.commercialInfo}"/>

                <h:outputText styleClass="tituloCampos" value="Informações Conta Bancária:"/>
                <h:outputText styleClass="tituloCampos"
                              value="#{EmpresaControle.situacaoCadastralContaAsaasDTO.bankAccountInfo}"/>

                <h:outputText styleClass="tituloCampos" value="Documentação:"/>
                <h:outputText styleClass="tituloCampos"
                              value="#{EmpresaControle.situacaoCadastralContaAsaasDTO.documentation}"/>

                <h:outputText styleClass="tituloCampos" value="Geral:"/>
                <h:outputText styleClass="tituloCampos"
                              value="#{EmpresaControle.situacaoCadastralContaAsaasDTO.general}"/>

            </h:panelGrid>
        </a4j:form>
    </rich:modalPanel>


</h:panelGrid>
