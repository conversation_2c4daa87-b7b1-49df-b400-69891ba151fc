 <%@include file="imports.jsp" %>
<rich:modalPanel id="panelFiltroSessao" width="280" autosized="true" styleClass="novaModal">
    <f:facet name="header">
        <h:panelGroup>
            <h:outputText value="Filtro"/>
        </h:panelGroup>
    </f:facet>
    <f:facet name="controls">
        <h:panelGroup>
            <h:outputText styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign btnSelecionarModalidade"
                    id="hidelinkSessao"/>
            <rich:componentControl for="panelFiltroSessao" attachTo="hidelinkSessao" operation="hide" event="onclick"/>
        </h:panelGroup>
    </f:facet>
    <h:form id="formPanelFiltroSessao">
        <h:panelGrid id="listaFiltrosSessao" width="80%" border="0"
                     cellspacing="0" styleClass="margin-container" columns="2">
            <h:panelGroup layout="block" styleClass="chk-fa-container">
                <h:selectBooleanCheckbox id="primeiraCompra" value="#{ICVSessaoRelControle.filtroSessaoPrimeiraCompra}"/>
                <span/>
            </h:panelGroup>

            <h:outputText styleClass="texto-size-16 texto-cor-cinza" value="Primeira compra"/>
            <h:panelGroup layout="block" styleClass="chk-fa-container">
                <h:selectBooleanCheckbox id="rematriculas" value="#{ICVSessaoRelControle.filtroSessaoRetornoCompra}"/>
                <span/>
            </h:panelGroup>
            <h:outputText styleClass="texto-size-16 texto-cor-cinza" value="Retorno compra"/>
        </h:panelGrid>
        <br>
        <h:panelGroup styleClass="container-botoes" layout="block">
            <a4j:commandLink styleClass="linkPadrao botaoPrimario texto-size-16"
                               reRender="containerConversaoSessao"
                               title="Consultar Dados Indice de Convers�o de Vendas"
                               action="#{ICVSessaoRelControle.consultarIndiceConversaoVendaPorColaborador}"
                               oncomplete="RichFaces.hideModalPanel('panelFiltroSessao');"
                               style="vertical-align:middle;">
                <span class="texto-size-16 texto-font">Atualizar </span>
                <i style="vertical-align: inherit" class="fa-icon-refresh texto-size-20 texto-cor-branco">
            </a4j:commandLink>
        </h:panelGroup>
    </h:form>
</rich:modalPanel>
