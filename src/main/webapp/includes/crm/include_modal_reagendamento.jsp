<%-- 
    Document   : include_modal_reagendamento
    Created on : 13/06/2014, 14:45:44
    Author     : marcosandre
--%>

<%@include file="../../include_imports.jsp" %>
<rich:jQuery id="mskData" selector=".rich-calendar-input" timing="onload" query="mask('99/99/9999')" />
    <rich:modalPanel id="panelAgenda" autosized="true" shadowOpacity="true" width="450" height="250" styleClass="tabForm">
        <f:facet name="header">
            <h:panelGroup>
                <h:outputText value="#{msg_aplic.prt_Agenda_reagendamento}" />
            </h:panelGroup>
        </f:facet>
        <f:facet name="controls">
            <%--<h:panelGroup>
				<h:graphicImage value="/imagensCRM/close.png" style="cursor:pointer" id="hiperlinkAgenda" />
				<rich:componentControl for="panelAgenda" attachTo="hiperlinkAgenda"	operation="hide" event="onclick" >
					<a4j:support reRender="form:panelGridMensagens" action="#{HistoricoContatoControle.adicionarMensagemErro}" />
				</rich:componentControl>
			</h:panelGroup>--%>
        </f:facet>

        <a4j:form id="formReagendar">
            <%-- DADOS PASSIVO (HISTORICO) --%>
            <h:panelGrid columns="1" rowClasses="linhaImpar" columnClasses="colunaAlinhamento" width="100%" style="border:1px solid black;">
                <h:panelGrid id="dadosPassivo" columns="1" width="100%" style="border:1px solid black" columnClasses="colunaEsquerda" rendered="#{HistoricoContatoControle.apresentarDadosCabecalhoPassivo}">
                    <h:panelGrid columns="1" columnClasses="colunaEsquerda" cellpadding="0" cellspacing="0" style="text-align: top;" width="100%">
                        <h:panelGrid columns="2" columnClasses="colunaEsquerda" width="100%">
                            <h:panelGrid columns="1" columnClasses="colunaEsquerda" width="100%">
                                <h:panelGroup>
                                    <h:outputText styleClass="tituloCamposAgenda" value="#{msg_aplic.prt_Agenda_aluno}" />
                                </h:panelGroup>
                                <h:panelGroup>
                                    <h:outputText styleClass="camposAgenda" value="#{HistoricoContatoControle.historicoContatoVO.passivoVO.nome}" />
                                </h:panelGroup>
                            </h:panelGrid>
                            <h:panelGrid columns="1" columnClasses="colunaCentralizada" width="100%">
                                <h:outputText styleClass="tituloCamposAgenda" value="#{msg_aplic.prt_Agenda_dataCadastro}" />
                                <h:outputText styleClass="camposAgenda" value="#{HistoricoContatoControle.historicoContatoVO.passivoVO.dia_Apresentar}" />
                            </h:panelGrid>
                        </h:panelGrid>
                    </h:panelGrid>
                </h:panelGrid>

                <%-- DADOS INDICADO (HISTORICO) --%>

                <h:panelGrid id="dadosIndicado" columns="1" width="100%" style="border:1px solid black" columnClasses="colunaEsquerda" rendered="#{HistoricoContatoControle.apresentarDadosCabecalhoIndicado}">
                    <h:panelGrid columns="1" columnClasses="colunaEsquerda" cellpadding="0" cellspacing="0" style="text-align: top;" width="100%">
                        <h:panelGrid columns="2" columnClasses="colunaEsquerda" width="100%">
                            <h:panelGrid columns="1" columnClasses="colunaEsquerda" width="100%">
                                <h:panelGroup>
                                    <h:outputText styleClass="tituloCamposAgenda" value="#{msg_aplic.prt_Agenda_aluno}" />
                                </h:panelGroup>
                                <h:panelGroup>
                                    <h:outputText styleClass="camposAgenda" value="#{HistoricoContatoControle.historicoContatoVO.indicadoVO.nomeIndicado}" />
                                </h:panelGroup>
                            </h:panelGrid>
                            <h:panelGrid columns="1" columnClasses="colunaCentralizada" width="100%">
                                <h:outputText styleClass="tituloCamposAgenda" value="#{msg_aplic.prt_Agenda_dataCadastro}" />
                                <h:outputText styleClass="camposAgenda" value="#{HistoricoContatoControle.historicoContatoVO.indicadoVO.indicacaoVO.dia_Apresentar}" />
                            </h:panelGrid>
                        </h:panelGrid>
                    </h:panelGrid>
                </h:panelGrid>

                 <%-- DADOS CLIENTE (HISTORICO) --%>

                    <h:panelGrid id="dadosCliente" columns="2" width="100%" rendered="#{HistoricoContatoControle.apresentarDadosCabecalhoCliente}" style="border:1px solid black" columnClasses="colunaEsquerda">
                        <h:panelGrid columns="1" width="50px" cellpadding="0" cellspacing="0" columnClasses="colunaEsquerda">
                            <a4j:outputPanel id="panelFoto">
                                <a4j:mediaOutput element="img" id="imagem1" style="width:60px;height:80px " 
                                                 cacheable="false" session="true" 
                                                 rendered="#{!SuperControle.fotosNaNuvem}" 
                                                 createContent="#{HistoricoContatoControle.paintFoto}" 
                                                 value="#{ImagemData}" mimeType="image/jpeg">
                                    <f:param value="#{SuperControle.timeStamp}" name="time" />
                                    <f:param name="largura" value="60"/>
                                    <f:param name="altura" value="80"/>
                                </a4j:mediaOutput>
                                <h:graphicImage rendered="#{SuperControle.fotosNaNuvem}" 
                                                width="60" height="80"                                        
                                                style="width:60px;height:80px"
                                                url="#{HistoricoContatoControle.paintFotoDaNuvem}"/>
                            </a4j:outputPanel>
                        </h:panelGrid>
                        <h:panelGrid columns="1" columnClasses="colunaEsquerda" cellpadding="0" cellspacing="0" style="text-align: top;" width="100%">
                            <h:panelGrid columns="4" columnClasses="colunaEsquerda" width="100%">
                                <h:panelGrid columns="1" columnClasses="colunaEsquerda" width="100%">
                                    <h:panelGroup>
                                        <h:outputText styleClass="tituloCamposAgenda" value="#{msg_aplic.prt_Agenda_aluno}" />
                                    </h:panelGroup>
                                    <h:panelGroup>
                                        <h:outputText id="cmpNomeCliente" styleClass="camposAgenda" value="#{HistoricoContatoControle.historicoContatoVO.clienteVO.pessoa.nome}" />
                                    </h:panelGroup>
                                </h:panelGrid>
                                <h:panelGrid columns="1" width="100%" columnClasses="colunaCentralizada">
                                    <h:outputText styleClass="tituloCamposAgenda" value="#{msg_aplic.prt_Agenda_idade}" />
                                    <h:outputText id="cpmIdade" styleClass="camposAgenda" value="#{HistoricoContatoControle.historicoContatoVO.idade}" />
                                </h:panelGrid>
                                <h:panelGrid columns="1" width="100%" columnClasses="colunaCentralizada">
                                    <h:outputText styleClass="tituloCamposAgenda" value="#{msg_aplic.prt_Agenda_estadoCivil}" />
                                    <h:outputText id="cpmEstadoCivil" styleClass="camposAgenda" rendered="#{!HistoricoContatoControle.historicoContatoVO.alinharOutputEstadoCivil}" value="#{HistoricoContatoControle.historicoContatoVO.clienteVO.pessoa.estadoCivil_Apresentar}" />
                                    <rich:spacer height="22px" rendered="#{HistoricoContatoControle.historicoContatoVO.alinharOutputEstadoCivil}" />
                                </h:panelGrid>
                                <h:panelGrid columns="1" columnClasses="colunaCentralizada" width="100%">
                                    <h:outputText styleClass="tituloCamposAgenda" value="#{msg_aplic.prt_Agenda_dataCadastro}" />
                                    <h:outputText id="cpmDataCadastro" styleClass="camposAgenda" value="#{HistoricoContatoControle.historicoContatoVO.clienteVO.pessoa.dataCadastro_Apresentar}" />
                                </h:panelGrid>
                            </h:panelGrid>
                        </h:panelGrid>
                    </h:panelGrid>
           </h:panelGrid>


                    <h:panelGrid columns="2" width="100%" style="border:1px dotted black">
                        <h:panelGrid columns="1" width="100%" rendered="#{HistoricoContatoControle.apresentarDadosCabecalhoCliente}" columnClasses="colunaCentralizada">
                            <h:panelGroup>
                                <h:outputText styleClass="tituloCamposAgenda" value="#{msg_aplic.prt_Agenda_faseAtual}: " />
                               <h:outputText  styleClass="tituloCamposAgenda" value="#{HistoricoContatoControle.historicoContatoVO.fase_Apresentar}" />
                            </h:panelGroup>
                        </h:panelGrid>

                        <h:panelGrid columns="1" width="100%" columnClasses="colunaCentralizada">
                            <h:panelGroup>
                                <h:outputText styleClass="tituloCamposAgenda" value="#{msg_aplic.prt_Agenda_tipoContato}:" />
                                <rich:spacer width="5px" />
                                <h:selectOneMenu id="tipoOperacao" onblur="blurinput(this);" onfocus="focusinput(this);"
                                                 styleClass="form" 
                                                 value="#{HistoricoContatoControle.historicoContatoVO.tipoContato}">
                                    <f:selectItems value="#{HistoricoContatoControle.listaSelectItemTipoContatoReagendamento}" />
                                    <a4j:support event="onchange" action="#{HistoricoContatoControle.alterarMeioEnvio}"
                                                 reRender="formReagendar" />
                                </h:selectOneMenu>
                            </h:panelGroup>
                        </h:panelGrid>

                    </h:panelGrid>

                     <h:panelGrid columns="1" width="100%" columnClasses="colunaCentralizada">

                        <%--DADOS DO TELEFONE PASSIVO --%>

                        <h:panelGrid id="dadosTelefonarPassivo" columns="1" style="height:10px; background-image:url('./imagens/fundoBarraTopo.png');background-repeat: repeat-x;" columnClasses="colunaCentralizada" width="100%" rendered="#{HistoricoContatoControle.apresentarTelefonePassivo}">
                            <h:outputText styleClass="tituloFormulario" value="#{msg_aplic.prt_Agenda_telefones}" />

                            <rich:spacer height="10px" />
                            <h:dataTable id="resTelefonePassivo" width="90%" headerClass="subordinado"
                                         rowClasses="linhaImpar, linhaPar" columnClasses="colunaCentralizada"
                                         value="#{HistoricoContatoControle.telefonesPassivo}"
                                         rendered="#{!empty HistoricoContatoControle.telefonesPassivo}" var="telefonePassivo">
                                <h:column rendered="#{HistoricoContatoControle.historicoContatoVO.tipoContato == 'CS'}">
                                    <h:selectBooleanCheckbox onclick="marcarTelefone(this);" id="telSelecionado" value="#{telefonePassivo.selecionado}" />
                                </h:column>
                                <h:column>
                                    <f:facet name="header">
                                        <h:outputText value="#{msg_aplic.prt_Agenda_numero}" />
                                    </f:facet>
                                    <h:outputText value="#{telefonePassivo.numero}" />
                                </h:column>
                                <h:column>
                                    <f:facet name="header">
                                        <h:outputText value="#{msg_aplic.prt_Agenda_tipoTelefone}" />
                                    </f:facet>
                                    <h:outputText value="#{telefonePassivo.tipoTelefone_Apresentar}" />
                                </h:column>
                                <h:column>
                                    <f:facet name="header">
                                        <h:outputText value="#{msg_aplic.prt_Agenda_descricao}" />
                                    </f:facet>
                                    <h:outputText value="#{telefonePassivo.descricao}" />
                                </h:column>
                            </h:dataTable>

                            <rich:spacer width="20px" />

                            <h:panelGrid id="comentarioPassivo" columns="1" columnClasses="colunaCentralizada" width="100%" rendered="#{HistoricoContatoControle.apresentarComentarioTelefonePassivo}">
                                <h:outputText value="#{msg_aplic.prt_Agenda_telefoneNaoEncontrado}" styleClass="mensagemTelefoneNaoEncontrado" />
                            </h:panelGrid>
                        </h:panelGrid>

                        <%--DADOS DO TELEFONE INDICADO --%>

                        <h:panelGrid id="dadosTelefonarIndicado" columns="1" style="height:10px; background-image:url('./imagens/fundoBarraTopo.png');background-repeat: repeat-x;" columnClasses="colunaCentralizada" width="100%" rendered="#{HistoricoContatoControle.apresentarTelefoneIndicado}">
                            <h:outputText styleClass="tituloFormulario" value="#{msg_aplic.prt_Agenda_telefones}" />

                            <rich:spacer height="10px" />
                            <h:dataTable id="resTelefoneIndicado" width="90%" headerClass="subordinado" rowClasses="linhaImpar, linhaPar" columnClasses="colunaCentralizada" value="#{HistoricoContatoControle.listaTelefoneIndicado}" rendered="#{!empty HistoricoContatoControle.listaTelefoneIndicado}" var="telefoneIndicado">
                                <h:column rendered="#{HistoricoContatoControle.historicoContatoVO.tipoContato == 'CS'}">
                                    <h:selectBooleanCheckbox onclick="marcarTelefone(this);" id="telSelecionado" value="#{telefoneIndicado.selecionado}" />
                                </h:column>
                                <h:column>
                                    <f:facet name="header">
                                        <h:outputText value="#{msg_aplic.prt_Agenda_numero}" />
                                    </f:facet>
                                    <h:outputText value="#{telefoneIndicado.numero}" />
                                </h:column>
                                <h:column>
                                    <f:facet name="header">
                                        <h:outputText value="#{msg_aplic.prt_Agenda_tipoTelefone}" />
                                    </f:facet>
                                    <h:outputText value="#{telefoneIndicado.tipoTelefone_Apresentar}" />
                                </h:column>
                                <h:column>
                                    <f:facet name="header">
                                        <h:outputText value="#{msg_aplic.prt_Agenda_descricao}" />
                                    </f:facet>
                                    <h:outputText value="#{telefoneIndicado.descricao}" />
                                </h:column>
                            </h:dataTable>

                            <rich:spacer width="20px" />

                            <h:panelGrid id="comentarioIndicado" columns="1" columnClasses="colunaCentralizada" width="100%" rendered="#{HistoricoContatoControle.apresentarComentarioTelefoneIndicado}">
                                <h:outputText value="#{msg_aplic.prt_Agenda_telefoneNaoEncontrado}" styleClass="mensagemTelefoneNaoEncontrado" />
                            </h:panelGrid>
                        </h:panelGrid>

                        <%--DADOS DO TELEFONE CLIENTE --%>

                        <h:panelGrid columns="1" id="tabelaCliente" style="height:10px; background-image:url('./imagens/fundoBarraTopo.png');background-repeat: repeat-x;" columnClasses="colunaCentralizada" width="100%" rendered="#{HistoricoContatoControle.apresentarTelefoneCliente}">
                            <h:outputText styleClass="tituloFormulario" value="#{msg_aplic.prt_Agenda_telefones}" />

                            <rich:spacer height="10px" />
                            <h:dataTable id="resTelefone" width="90%" headerClass="subordinado" rowClasses="linhaImpar, linhaPar" columnClasses="colunaCentralizada"
                                         value="#{HistoricoContatoControle.historicoContatoVO.listaTelefoneClientePorTipoContato}"
                                         rendered="#{HistoricoContatoControle.apresentarTelefoneCliente}" var="telefone">
                                <h:column rendered="#{HistoricoContatoControle.historicoContatoVO.tipoContato == 'CS'}">
                                    <h:selectBooleanCheckbox onclick="marcarTelefone(this);" id="telSelecionado" value="#{telefone.selecionado}" />
                                </h:column>
                                <h:column>
                                    <f:facet name="header">
                                        <h:outputText value="#{msg_aplic.prt_Agenda_numero}" />
                                    </f:facet>
                                    <h:outputText value="#{telefone.numero}" />
                                </h:column>
                                <h:column>
                                    <f:facet name="header">
                                        <h:outputText value="#{msg_aplic.prt_Agenda_tipoTelefone}" />
                                    </f:facet>
                                    <h:outputText value="#{telefone.tipoTelefone_Apresentar}" />
                                </h:column>
                                <h:column>
                                    <f:facet name="header">
                                        <h:outputText value="#{msg_aplic.prt_Agenda_descricao}" />
                                    </f:facet>
                                    <h:outputText value="#{telefone.descricao}" />
                                </h:column>
                            </h:dataTable>

                            <rich:spacer width="20px" />

                            <h:panelGrid columns="1" columnClasses="colunaCentralizada" width="100%" rendered="#{HistoricoContatoControle.apresentarComentarioTelefoneCliente}">
                                <h:outputText value="#{msg_aplic.prt_Agenda_telefoneNaoEncontrado}" styleClass="mensagemTelefoneNaoEncontrado" />
                            </h:panelGrid>
                        </h:panelGrid>
                  </h:panelGrid>

                  <h:panelGrid columns="1" id="textComentario" style="height:25px; background-image:url('./imagens/fundoBarraTopo.png');background-repeat: repeat-x;" columnClasses="colunaCentralizada" width="100%" rendered="#{HistoricoContatoControle.historicoContatoVO.apresentarComentario}">
                            <h:outputText styleClass="tituloFormulario" value="#{msg_aplic.prt_Agenda_comentario}" />
                            <rich:spacer height="5"/>
                            


                            <h:inputTextarea 
                                             
                                             style="align:center;" id="comentarioTextArea" cols="100" rows="2"
                                             value="#{HistoricoContatoControle.historicoContatoVO.observacao}"/>

								

                        </h:panelGrid>


                 <h:panelGrid columns="1" id="dadosAgendamento" style="height:25px; background-image:url('./imagens/fundoBarraTopo.png');background-repeat: repeat-x;" columnClasses="colunaCentralizada" width="100%" rendered="#{HistoricoContatoControle.historicoContatoVO.apresentarComentario}">
                            <h:outputText styleClass="tituloFormulario" value="#{msg_aplic.prt_Agenda_tituloForm}" />
                            <rich:spacer height="5"/>
                 </h:panelGrid>
                 <h:panelGrid rendered="#{HistoricoContatoControle.agendamentoVisita}" columnClasses="colunaCentralizada" id="gridTipoLigacao" columns="1" width="100%" style="border:1px dotted black">
                     <h:panelGroup>
                    <h:outputText styleClass="tituloCampos"  value="#{msg_aplic.prt_Agenda_tipoAgendamento}:" />
                    <rich:spacer width="5"/>
                    <h:outputText styleClass="tituloCampos" value="Liga��o" />
                    </h:panelGroup>
                </h:panelGrid>
                <h:panelGrid rendered="#{!HistoricoContatoControle.agendamentoVisita}" columnClasses="colunaCentralizada" id="gridTipoAgendamento" columns="2" width="100%" style="border:1px dotted black">
                    <h:outputText styleClass="tituloCampos"  value="#{msg_aplic.prt_Agenda_tipoAgendamento}:" />
                    <h:selectOneRadio rendered="#{!HistoricoContatoControle.agendamentoVisita}" id="opcoesTipoAgendamento" onblur="blurinput(this);" onfocus="focusinput(this);" styleClass="form" style="border: none;"
                                      value="#{HistoricoContatoControle.agendaVO.tipoAgendamento}">
                        <f:selectItems value="#{HistoricoContatoControle.listaSelectItemTipoAgendamentoReagendamento}" />
                        <a4j:support event="onclick" reRender="panelTipoAgendamento" />
                    </h:selectOneRadio>
                </h:panelGrid>
                <rich:spacer height="10px" />
                <h:panelGrid columns="1" rowClasses="linhaImpar" columnClasses="colunaAlinhamento" width="100%" style="border:1px solid black;">
                <h:panelGroup id="panelTipoAgendamento">
                    
                    <h:panelGroup id="panelModalidade" rendered="#{HistoricoContatoControle.apresentarInputTextAulaExperimental}">
                        <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_Agenda_modalidade}" />
                        <rich:spacer width="5px" />
                        <h:inputText id="textModalidade" size="50"
                                     onblur="blurinput(this);"
                                     onfocus="focusinput(this);"
                                     styleClass="form"
                                     value="#{HistoricoContatoControle.agendaVO.modalidade.nome}" />

                        <rich:suggestionbox   height="200" width="200"
                                              for="textModalidade"
                                              status="statusInComponent"
                                              immediate="true"
                                              suggestionAction="#{HistoricoContatoControle.autocompleteModalidade}"
                                              nothingLabel="Nenhuma Modalidade encontrada !" var="result" id="suggestionResponsavel">
                            <h:column>
                                <h:outputText value="#{result.nome}" />
                            </h:column>
                        </rich:suggestionbox>

                    </h:panelGroup>
                    <h:panelGroup>
                        <a4j:outputPanel layout="block" >
                            <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_Agenda_data}" />
                           

                            <rich:calendar id="modaldataAgendamento"
                                           value="#{HistoricoContatoControle.agendaVO.dataAgendamento}"
                                           inputSize="8"
                                           inputClass="form"
                                           oninputblur="blurinput(this);document.getElementById('formF:atualizaCompetencia').click();"
                                           oninputfocus="focusinput(this);"
                                           oninputchange="return validar_Data(this.id);"
                                           datePattern="dd/MM/yyyy"
                                           enableManualInput="true"
                                           showWeeksBar="false" />
                            <rich:jQuery id="mskData" selector=".rich-calendar-input" timing="onload" query="mask('99/99/9999')" />
                            <rich:spacer width="20" />
                            <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_Agenda_hora}" />
                            <rich:spacer width="5" />
                            <h:selectOneMenu id="selectHora" value="#{HistoricoContatoControle.agendaVO.hora}" onblur="blurinput(this);" onfocus="focusinput(this);" styleClass="form">
                                <f:selectItems value="#{HistoricoContatoControle.listaSelectItemHoras}" />
                            </h:selectOneMenu>
                            <rich:spacer width="20" />
                            <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_Agenda_minuto}" />
                            <rich:spacer width="5" />
                            <h:selectOneMenu id="selectMinutos" value="#{HistoricoContatoControle.agendaVO.minuto}" onblur="blurinput(this);" onfocus="focusinput(this);" styleClass="form">
                                <f:selectItems value="#{HistoricoContatoControle.listaSelectItemMinutos}" />
                            </h:selectOneMenu>
                        </a4j:outputPanel>
                    </h:panelGroup>
                </h:panelGroup>
                </h:panelGrid>
            

            <h:panelGrid columns="1" columnClasses="colunaCentralizada" width="100%">
                <h:panelGroup>
                    <a4j:commandButton id="btnGrvar"
                                       reRender="formReagendar,panelGridMensagensAgendamento"
                                       action="#{HistoricoContatoControle.gravarAgendaHistoricoContato}"
                                       oncomplete="#{HistoricoContatoControle.manterAbertoRichModalPanelAgenda}" styleClass="botoes" image="./imagensCRM/botaoGravar.png" />
                    <rich:spacer width="10" />
                    <a4j:commandButton id="btnCancelar" reRender="formF" action="#{HistoricoContatoControle.cancelarAgendamentoPanelAgenda}" oncomplete="Richfaces.hideModalPanel('panelAgenda');" styleClass="botoes" image="./imagensCRM/botaoCancelar.png" />
                </h:panelGroup>
            </h:panelGrid>

            <h:panelGrid id="panelGridMensagensAgendamento" columns="1" width="100%">
                <h:outputText id="msgPanelAgendamento"	 styleClass="mensagem" value="#{HistoricoContatoControle.mensagem}" />
                <h:outputText id="msgPanelAgendamentoDet" styleClass="mensagemDetalhada" value="#{HistoricoContatoControle.mensagemDetalhada}" />
            </h:panelGrid>
        </a4j:form>
    </rich:modalPanel>