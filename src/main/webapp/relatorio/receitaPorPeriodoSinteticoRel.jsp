<%@page contentType="text/html" %>
<%@page pageEncoding="ISO-8859-1" %>
<head>
    <%@include file="/includes/include_import_minifiles.jsp" %>
    <script type="text/javascript" language="javascript" src="../script/script.js"></script>
</head>
<script type="text/javascript" language="javascript" src="../hoverform.js"></script>
<link href="../css/otimize.css" rel="stylesheet" type="text/css">
<link href="../beta/css/font-awesome_2.0.min.css" type="text/css" rel="stylesheet"/>
<title>Relatório de Receita Por Período Sintético</title>

<%@taglib prefix="f" uri="http://java.sun.com/jsf/core" %>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html" %>

<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax" %>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich" %>
<%@taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>

<f:loadBundle var="msg_bt" basename="propriedades.Botoes"/>
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao"/>
<f:loadBundle var="msg" basename="propriedades.Mensagens"/>

<%@include file="/includes/verificaModulo.jsp" %>
<c:set var="contexto" value="${pageContext.request.contextPath}" scope="request"/>
<c:set var="iconeWikiEquivalentes" scope="request" value="true"/>

<f:view>
    <jsp:include page="/includes/include_carregando_ripple.jsp" flush="true"/>
    <a4j:loadScript src="/script/jquery.maskedinput-1.2.2.js"/>

    <h:panelGrid columns="1" width="100%" cellpadding="0" cellspacing="0">
        <f:facet name="header">

            <c:if test="${modulo eq 'zillyonWeb'}">
                <c:set var="titulo" scope="session" value="${msg_aplic.prt_ReceitaPorPeriodoSintetico_tituloRel}"/>
                <c:set var="urlWiki" scope="session" value="${SuperControle.urlBaseConhecimento}como-verificar-a-receita-por-periodo-da-empresa/"/>
                <jsp:include page="../topoReduzido_material.jsp"/>
            </c:if>
            <c:if test="${modulo eq 'centralEventos'}">
                <jsp:include page="../pages/ce/includes/topoReduzido.jsp"/>
            </c:if>

        </f:facet>

        <h:form id="form" target="_blank">

            <input type="hidden" value="${modulo}" name="modulo"/>

            <h:commandLink action="#{ReceitaPorPeriodoSinteticoRelControleRel.liberarBackingBeanMemoria}"
                           id="idLiberarBackingBeanMemoria" style="display: none"/>
            <h:commandLink action="#{ReceitaPorPeriodoSinteticoRelControleRel.imprimirRelatorio}" id="imprimirRelatorio"
                           style="display: none"/>
            <h:inputHidden id="relatorio" value="#{ReceitaPorPeriodoSinteticoRelControleRel.relatorio}"/>
            <h:panelGrid columns="1" width="100%">
                <h:panelGrid columns="2" rowClasses="linhaImpar, linhaPar" columnClasses="classEsquerda, classDireita"
                             width="100%">
                    <h:outputText styleClass="tituloCampos"
                                  rendered="#{ReceitaPorPeriodoSinteticoRelControleRel.permissaoConsultaTodasEmpresas}"
                                  value="#{msg_aplic.prt_FaturamentoSintetico_empresa}"/>
                    <h:panelGroup rendered="#{ReceitaPorPeriodoSinteticoRelControleRel.permissaoConsultaTodasEmpresas}">
                        <h:selectOneMenu id="empresa" onblur="blurinput(this);" onfocus="focusinput(this);"
                                         styleClass="form"
                                         value="#{ReceitaPorPeriodoSinteticoRelControleRel.filtroEmpresa}">
                            <f:selectItems value="#{ReceitaPorPeriodoSinteticoRelControleRel.listaEmpresas}"/>
                        </h:selectOneMenu>
                    </h:panelGroup>
                    <h:panelGroup id="periodo">
                        <h:outputText styleClass="tituloCampos"
                                      value="#{msg_aplic.prt_ReceitaPorPeriodoSintetico_periodoPesquisa}:"
                                      rendered="#{ReceitaPorPeriodoSinteticoRelControleRel.receitaPorPeriodoSinteticoRel.formaPagamento < 3}"/>
                        <h:outputText styleClass="tituloCampos"
                                      value="#{msg_aplic.prt_ReceitaPorPeriodoSintetico_faturamentoRecebido}"
                                      rendered="#{ReceitaPorPeriodoSinteticoRelControleRel.receitaPorPeriodoSinteticoRel.formaPagamento == 3}"/>
                    </h:panelGroup>
                    <h:panelGroup>
                        <h:panelGroup>
                            <rich:calendar id="dataInicio"
                                           value="#{ReceitaPorPeriodoSinteticoRelControleRel.receitaPorPeriodoSinteticoRel.dataInicio}"
                                           inputSize="10"
                                           inputClass="form"
                                           oninputblur="blurinput(this);"
                                           oninputfocus="focusinput(this);"
                                           oninputchange="return validar_Data(this.id);"
                                           datePattern="dd/MM/yyyy"
                                           enableManualInput="true"
                                           zindex="2"
                                           showWeeksBar="false"/>
                            <h:message for="dataInicio" styleClass="mensagemDetalhada"/>
                        </h:panelGroup>

                        <h:outputText styleClass="tituloCampos" style="position:relative; top:0px; left:10px;"
                                      value="#{msg_aplic.prt_CaixaPorOperador_ate}"/>
                        <rich:spacer width="12px"/>
                        <h:panelGroup>
                            <rich:calendar id="dataTermino"
                                           value="#{ReceitaPorPeriodoSinteticoRelControleRel.receitaPorPeriodoSinteticoRel.dataTermino}"
                                           inputSize="10"
                                           inputClass="form"
                                           oninputblur="blurinput(this);"
                                           oninputfocus="focusinput(this);"
                                           oninputchange="return validar_Data(this.id);"
                                           datePattern="dd/MM/yyyy"
                                           enableManualInput="true"
                                           zindex="2"
                                           showWeeksBar="true"/>
                            <h:message for="dataTermino" styleClass="mensagemDetalhada"/>
                            <rich:jQuery id="mskData" selector=".rich-calendar-input" timing="onload" query="mask('99/99/9999')" />
                        </h:panelGroup>
                    </h:panelGroup>

                    <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_ReceitaPorPeriodoSintetico_visao}:"/>
                    <h:panelGroup>
                        <h:selectOneRadio id="formaPagamento" styleClass="tituloCampos"
                                          value="#{ReceitaPorPeriodoSinteticoRelControleRel.receitaPorPeriodoSinteticoRel.formaPagamento}">
                            <f:selectItems
                                    value="#{ReceitaPorPeriodoSinteticoRelControleRel.listaSelectItemTipoFormaPagamento}"/>
                            <a4j:support event="onchange" reRender="periodo"
                                         action="#{ReceitaPorPeriodoSinteticoRelControleRel.vazio}"/>
                        </h:selectOneRadio>
                    </h:panelGroup>

                    <h:outputText styleClass="tituloCampos"
                                  value="#{msg_aplic.prt_ReceitaPorPeriodoSintetico_formaPagamento}:"/>
                    <h:panelGroup>
                        <rich:spacer width="3px"/>
                        <h:selectBooleanCheckbox styleClass="tituloCampos"
                                                 value="#{ReceitaPorPeriodoSinteticoRelControleRel.receitaPorPeriodoSinteticoRel.visaoDinheiro}"/>
                        <h:outputText value="Dinheiro" styleClass="tituloCampos"/>

                        <rich:spacer width="10px"/>
                        <h:selectBooleanCheckbox styleClass="tituloCampos"
                                                 value="#{ReceitaPorPeriodoSinteticoRelControleRel.receitaPorPeriodoSinteticoRel.visaoChequeVista}"/>
                        <h:outputText value="Ch. a Vista" styleClass="tituloCampos"/>

                        <rich:spacer width="10px"/>
                        <h:selectBooleanCheckbox styleClass="tituloCampos"
                                                 value="#{ReceitaPorPeriodoSinteticoRelControleRel.receitaPorPeriodoSinteticoRel.visaoChequePrazo}"/>
                        <h:outputText value="Ch. a Prazo" styleClass="tituloCampos"/>

                        <rich:spacer width="10px"/>
                        <h:selectBooleanCheckbox styleClass="tituloCampos"
                                                 value="#{ReceitaPorPeriodoSinteticoRelControleRel.receitaPorPeriodoSinteticoRel.visaoCartaoCredito}"/>
                        <h:outputText value="Cartão de Crédito" styleClass="tituloCampos"/>

                        <rich:spacer width="10px"/>
                        <h:selectBooleanCheckbox styleClass="tituloCampos"
                                                 value="#{ReceitaPorPeriodoSinteticoRelControleRel.receitaPorPeriodoSinteticoRel.visaoCartaoDebito}"/>
                        <h:outputText value="Cartão de Débito" styleClass="tituloCampos"/>

                        <rich:spacer width="10px"/>
                        <h:selectBooleanCheckbox styleClass="tituloCampos"
                                                 value="#{ReceitaPorPeriodoSinteticoRelControleRel.receitaPorPeriodoSinteticoRel.visaoBoleto}"/>
                        <h:outputText value="Boleto" styleClass="tituloCampos"/>

                        <rich:spacer width="10px"/>
                        <h:selectBooleanCheckbox styleClass="tituloCampos"
                                                 value="#{ReceitaPorPeriodoSinteticoRelControleRel.receitaPorPeriodoSinteticoRel.visaoPix}"/>
                        <h:outputText value="Pix" styleClass="tituloCampos"/>

                        <rich:spacer width="10px"/>
                        <h:selectBooleanCheckbox styleClass="tituloCampos"
                                                 value="#{ReceitaPorPeriodoSinteticoRelControleRel.receitaPorPeriodoSinteticoRel.visaoOutros}"/>
                        <h:outputText value="Outros" styleClass="tituloCampos"/>

                        <rich:spacer width="10px"/>
                        <h:selectBooleanCheckbox styleClass="tituloCampos"
                                                 value="#{ReceitaPorPeriodoSinteticoRelControleRel.receitaPorPeriodoSinteticoRel.visaoDevolucao}"/>
                        <h:outputText value="Devolução de Dinheiro - Cancelamento" styleClass="tituloCampos"/>

                    </h:panelGroup>

                </h:panelGrid>

                <h:panelGrid columns="1" width="100%" styleClass="tabMensagens">
                    <h:panelGrid id="mensagem" columns="1" width="100%" columnClasses="colunaEsquerda">
                        <h:outputText styleClass="mensagem"
                                      value="#{ReceitaPorPeriodoSinteticoRelControleRel.mensagem}"/>
                        <h:outputText styleClass="mensagemDetalhada"
                                      value="#{ReceitaPorPeriodoSinteticoRelControleRel.mensagemDetalhada}"/>
                    </h:panelGrid>
                    <h:panelGrid columns="1" width="100%" styleClass="tabBotoes" columnClasses="colunaCentralizada">
                        <h:panelGroup>
                            <rich:spacer width="20px"/>
                            <c:if test="${modulo eq 'zillyonWeb'}">
                                <a4j:commandLink id="imprimirPDF"
                                                 action="#{ReceitaPorPeriodoSinteticoRelControleRel.imprimirPDF}"
                                                 oncomplete="#{ReceitaPorPeriodoSinteticoRelControleRel.nomeRefRelatorioGeradoAgora}"
                                                 accesskey="2" styleClass="botoes nvoBt"
                                                 reRender="form">
                                    <i class="fa-icon-print"></i> Gerar Relatório (PDF)
                                </a4j:commandLink>
                            </c:if>
                            <c:if test="${modulo eq 'centralEventos'}">
                                <a4j:commandLink id="imprimirPDF"
                                                 action="#{ReceitaPorPeriodoSinteticoRelControleRel.imprimirPDF}"
                                                 oncomplete="#{ReceitaPorPeriodoSinteticoRelControleRel.nomeRefRelatorioGeradoAgora}"
                                                 value="Gerar Relatório (PDF)"
                                                 accesskey="2" styleClass="botoes nvoBt" reRender="form">
                                    <i class="fa-icon-print"></i> Gerar Relatório (PDF)
                                </a4j:commandLink>
                            </c:if>

                        </h:panelGroup>
                    </h:panelGrid>
                </h:panelGrid>
            </h:panelGrid>
        </h:form>
    </h:panelGrid>
</f:view>
<script>
    document.getElementById("form:dataInicio").focus();
</script>
