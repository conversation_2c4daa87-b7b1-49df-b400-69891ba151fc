<%@page contentType="text/html"%>
<%@page pageEncoding="ISO-8859-1" %>
<head>
    <%@include file="/includes/include_import_minifiles.jsp" %>
    <script type="text/javascript" language="javascript" src="../script/script.js"></script>
</head>
<script type="text/javascript" language="javascript" src="../hoverform.js"></script>
<link href="../css/otimize.css" rel="stylesheet" type="text/css">
<link href="../beta/css/font-awesome_2.0.min.css" type="text/css" rel="stylesheet"/>
<%@taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<%@taglib prefix="f" uri="http://java.sun.com/jsf/core"%>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html"%>
<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax"%>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich"%>

<f:loadBundle var="msg_bt" basename="propriedades.Botoes"/>
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao"/>
<f:loadBundle var="msg" basename="propriedades.Mensagens"/>

<f:view>
    <jsp:include page="/includes/include_carregando_ripple.jsp" flush="true"/>
    <title>
        <h:outputText value="#{msg_aplic.prt_FaturamentoSintetico_tituloForm}" rendered="#{!FaturamentoSinteticoControleRel.somenteFaturamentoRecebido}"/>
        <h:outputText value="#{msg_aplic.prt_FaturamentoRecebidoSintetico_tituloForm}" rendered="#{FaturamentoSinteticoControleRel.somenteFaturamentoRecebido}"/>
    </title>
    <c:if test="${!FaturamentoSinteticoControleRel.somenteFaturamentoRecebido}">
        <c:set var="titulo" scope="session"  value="${msg_aplic.prt_FaturamentoSintetico_tituloForm}"/>
        <c:set var="urlWiki" scope="session" value="${SuperControle.urlBaseConhecimento}onde-vejo-minhas-vendas-do-mes-faturamento-por-periodo/"/>
    </c:if>
    <c:if test="${FaturamentoSinteticoControleRel.somenteFaturamentoRecebido}">
        <c:set var="titulo" scope="session"  value="${msg_aplic.prt_FaturamentoRecebidoSintetico_tituloForm}"/>
        <c:set var="urlWiki" scope="session" value="${SuperControle.urlBaseConhecimento}onde-vejo-o-faturamento-recebido-por-periodo-da-minha-empresa/"/>
    </c:if>

    <c:set var="iconeWikiEquivalentes" scope="request" value="true"/>

    <a4j:loadScript src="/script/jquery.maskedinput-1.2.2.js" />
    <h:panelGrid columns="1" width="100%" cellpadding="0" cellspacing="0">
        <f:facet name="header">
            <jsp:include page="../topoReduzido_material.jsp"/>
        </f:facet>
        <h:form id="form">
            <h:panelGrid columns="1" width="100%" >
                <h:panelGrid columns="2" rowClasses="linhaImpar, linhaPar" columnClasses="classEsquerda, classDireita" width="100%">
                    <h:outputText  styleClass="tituloCampos" rendered="#{FaturamentoSinteticoControleRel.permissaoConsultaTodasEmpresas}" value="#{msg_aplic.prt_FaturamentoSintetico_empresa}" />
                    <h:panelGroup rendered="#{FaturamentoSinteticoControleRel.permissaoConsultaTodasEmpresas}">
                        <h:selectOneMenu  id="empresa" onblur="blurinput(this);"  onfocus="focusinput(this);" styleClass="form"
                                          value="#{FaturamentoSinteticoControleRel.filtroEmpresa}" >
                            <f:selectItems value="#{FaturamentoSinteticoControleRel.listaEmpresas}" />
                            <a4j:support event="onchange" action="#{FaturamentoSinteticoControleRel.montarModalidadeSelectItem}"
                                         reRender="modalidade"/>
                        </h:selectOneMenu>
                    </h:panelGroup>
                    <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_FaturamentoSintetico_faturamento} de" />
                    <h:panelGroup>
                        <h:panelGroup>
                            <rich:calendar id="dataInicio"
                                           value="#{FaturamentoSinteticoControleRel.faturamentoSinteticoRel.dataInicio}"
                                           inputSize="10"
                                           inputClass="form"
                                           oninputblur="blurinput(this);"
                                           oninputfocus="focusinput(this);"
                                           oninputchange="return validar_Data(this.id);"
                                           datePattern="dd/MM/yyyy"
                                           enableManualInput="true"
                                           zindex="2"
                                           showWeeksBar="false" />
                            <h:message for="dataInicio"  styleClass="mensagemDetalhada"/>
                        </h:panelGroup>
                        <h:outputText  styleClass="tituloCampos" style="position:relative; top:0px; left:10px;" value="#{msg_aplic.prt_ate}" />
                        <rich:spacer width="12px"/>
                        <h:panelGroup>
                            <rich:calendar id="dataTermino"
                                           value="#{FaturamentoSinteticoControleRel.faturamentoSinteticoRel.dataTermino}"
                                           inputSize="10"
                                           inputClass="form"
                                           oninputblur="blurinput(this);"
                                           oninputfocus="focusinput(this);"
                                           oninputchange="return validar_Data(this.id);"
                                           datePattern="dd/MM/yyyy"
                                           enableManualInput="true"
                                           zindex="2"/>
                            <h:message for="dataTermino"  styleClass="mensagemDetalhada"/>
                        </h:panelGroup>
                    </h:panelGroup>
                    <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_Contrato_responsavelPeloContrato_extenso}" rendered="#{!FaturamentoSinteticoControleRel.somenteFaturamentoRecebido}"/>
                    <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_responsavel_recebimento}" rendered="#{FaturamentoSinteticoControleRel.somenteFaturamentoRecebido}"/>
                    <h:panelGroup>
                        <h:inputText id="nomeOperador" readonly="true" size="40" maxlength="40" onblur="blurinput(this);"
                                     onfocus="focusinput(this);" styleClass="form"
                                     value="#{FaturamentoSinteticoControleRel.operador.pessoa.nome}" />
                        <a4j:commandButton id="consultarOperador"
                                           oncomplete="Richfaces.showModalPanel('panelOperador'), setFocus(formOperador,'formOperador:valorConsultarOperador')"
                                           alt="Consultar Operador" image="../imagens/informacao.gif" />
                        <rich:spacer width="5px" />
                        <a4j:commandButton id="LimparOperador"
                                           image="../imagens/limpar.gif"
                                           reRender="form:nomeOperador"
                                           action="#{FaturamentoSinteticoControleRel.limparCampoOperador}"/>
                    </h:panelGroup>
                    <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_CaixaPorOperador_consultor}" />
                    <h:panelGroup>
                        <h:inputText id="nomeColaborador" readonly="true" size="40" maxlength="40" onblur="blurinput(this);"  onfocus="focusinput(this);" styleClass="form" value="#{FaturamentoSinteticoControleRel.colaborador.pessoa.nome}" />
                        <a4j:commandButton id="consultarColaborador"
                                           oncomplete="Richfaces.showModalPanel('panelColaborador'), setFocus(formColaborador,'formColaborador:valorConsultarColaborador')"
                                           alt="Consultar Colaborador" image="../imagens/informacao.gif"/>
                        <rich:spacer width="5px" />
                        <a4j:commandButton id="LimparColaborador"  image="../imagens/limpar.gif"  reRender="form:nomeColaborador" action="#{FaturamentoSinteticoControleRel.limparCampoColaborador}"/>
                    </h:panelGroup>

                    <h:outputText styleClass="tituloCampos"
                                  title="As informações serão apresentadas no relatório quando exportar para o formato PDF"
                                  value="Apresentar totalizador por formas de pagamento: "
                                  rendered="#{FaturamentoSinteticoControleRel.somenteFaturamentoRecebido}"/>
                    <h:panelGroup rendered="#{FaturamentoSinteticoControleRel.somenteFaturamentoRecebido}">
                        <h:selectBooleanCheckbox
                                title="As informações serão apresentadas no relatório quando exportar para o formato PDF"
                                styleClass="tituloCampos"
                                value="#{FaturamentoSinteticoControleRel.totalizador}"/>
                    </h:panelGroup>

                    <h:outputText styleClass="tituloCampos"
                                  title="As informações serão apresentadas no relatório quando exportar para o formato PDF"
                                  value="Apresentar competência de meses futuros: "/>
                    <h:panelGroup>
                        <h:selectBooleanCheckbox
                                title="As informações serão apresentadas no relatório quando exportar para o formato PDF"
                                styleClass="tituloCampos"
                                value="#{FaturamentoSinteticoControleRel.competencia}"/>
                    </h:panelGroup>

                    <h:outputText styleClass="tituloCampos"
                                  title="Não será considerado produto que está cancelado"
                                  value="Desconsiderar produtos cancelados:"/>
                    <h:panelGroup>
                        <h:selectBooleanCheckbox
                                title="Não será considerado produto que está cancelado"
                                styleClass="tituloCampos"
                                value="#{FaturamentoSinteticoControleRel.desconsiderarProdutoCancelado}"/>
                    </h:panelGroup>

                    <h:outputText styleClass="tituloCampos"
                                  value="Produtos com a mesma data do contrato ou venda avulsa:"/>
                    <h:panelGroup>
                        <h:selectBooleanCheckbox
                                styleClass="tituloCampos"
                                value="#{FaturamentoSinteticoControleRel.somenteContratoVendaAvulsaMesmoMesReferencia}"/>
                    </h:panelGroup>

                    <h:outputText  styleClass="tituloCampos" value="Incluir pagto. Conta Corrente:" rendered="#{FaturamentoSinteticoControleRel.somenteFaturamentoRecebido}"/>
                    <h:selectBooleanCheckbox styleClass="tituloCampos" value="#{FaturamentoSinteticoControleRel.contaCorrente}" rendered="#{FaturamentoSinteticoControleRel.somenteFaturamentoRecebido}"/>

                    <h:outputText  styleClass="tituloCampos" value="Somente Bolsista: " rendered="#{!FaturamentoSinteticoControleRel.somenteFaturamentoRecebido}"/>
                    <h:panelGroup rendered="#{!FaturamentoSinteticoControleRel.somenteFaturamentoRecebido}">
                        <h:selectBooleanCheckbox styleClass="tituloCampos" value="#{FaturamentoSinteticoControleRel.bolsa}" />
                    </h:panelGroup>

                    <h:outputText  styleClass="tituloCampos" value="Ignorar pagto. Edição de Pagamento" rendered="#{FaturamentoSinteticoControleRel.somenteFaturamentoRecebido}"/>
                    <h:panelGroup id="painelDevolucaoCheque" rendered="#{FaturamentoSinteticoControleRel.somenteFaturamentoRecebido}">
                        <h:selectBooleanCheckbox styleClass="tituloCampos" value="#{FaturamentoSinteticoControleRel.ignorarEdicaoPagamento}"/>
                    </h:panelGroup>
                    <h:outputText  styleClass="tituloCampos" value="Incluir pagto. Cheque Devolvido" rendered="#{FaturamentoSinteticoControleRel.somenteFaturamentoRecebido}" />
                    <h:panelGroup id="painelEdicaoPagamento" rendered="#{FaturamentoSinteticoControleRel.somenteFaturamentoRecebido}">
                        <h:selectBooleanCheckbox styleClass="tituloCampos" value="#{FaturamentoSinteticoControleRel.incluirDevolucaoCheque}"/>
                    </h:panelGroup>

                    <h:outputText  styleClass="tituloCampos" value="Tipo de Produtos:" />
                    <h:outputText  styleClass="tituloCampos" value="" />
                    <h:outputText  styleClass="tituloCampos" value="Matrícula, Rematrícula, Renovação" />
                    <h:panelGroup>
                        <h:selectBooleanCheckbox styleClass="tituloCampos" value="#{FaturamentoSinteticoControleRel.matriculaRenovacaoRematricula}"/>
                        <rich:spacer width="111px"/>
                        <h:outputText styleClass="tituloCampos" value="Desafio"/>
                        <h:selectBooleanCheckbox styleClass="tituloCampos"
                                                 value="#{FaturamentoSinteticoControleRel.desafio}"/>
                    </h:panelGroup>
                    <h:outputText  styleClass="tituloCampos" value="Manutenção Modalidade" />
                    <h:panelGroup>
                        <h:selectBooleanCheckbox styleClass="tituloCampos" value="#{FaturamentoSinteticoControleRel.manutencaoModalidade}"/>
                        <rich:spacer width="111px"/>
                        <h:outputText  styleClass="tituloCampos" value="Serviço" />
                        <h:selectBooleanCheckbox styleClass="tituloCampos" value="#{FaturamentoSinteticoControleRel.servico}"/>
                    </h:panelGroup>
                    <h:outputText styleClass="tituloCampos" value="Trancamento" />
                    <h:panelGroup>

                        <div style="width:194px;">
                            <h:selectBooleanCheckbox styleClass="tituloCampos" style="float: left" value="#{FaturamentoSinteticoControleRel.trancamento}"/>
                            <div style="float:right">
                                <h:outputText styleClass="tituloCampos" value="Crédito Personal" />
                                <h:selectBooleanCheckbox styleClass="tituloCampos" value="#{FaturamentoSinteticoControleRel.creditoPersonal}"/>
                            </div>
                        </div>
                    </h:panelGroup>

                    <h:outputText  styleClass="tituloCampos" value="Adesão Recorrência" />
                    <h:panelGroup>
                        <h:selectBooleanCheckbox styleClass="tituloCampos" value="#{FaturamentoSinteticoControleRel.taxaAdesao}"/>
                        <rich:spacer width="25px"/>
                        <h:outputText  styleClass="tituloCampos" value="Anuidade Recorrência" />
                        <h:selectBooleanCheckbox styleClass="tituloCampos" value="#{FaturamentoSinteticoControleRel.taxaAnuidade}"/>
                    </h:panelGroup>

                    <h:outputText  styleClass="tituloCampos" value="Aula Avulsa" />
                    <h:panelGroup>
                        <h:selectBooleanCheckbox styleClass="tituloCampos" value="#{FaturamentoSinteticoControleRel.aulaAvulsa}"/>
                        <rich:spacer width="119px"/>
                        <h:outputText  styleClass="tituloCampos" value="Diária" />
                        <h:selectBooleanCheckbox styleClass="tituloCampos" value="#{FaturamentoSinteticoControleRel.diaria}"/>
                    </h:panelGroup>


                    <h:outputText styleClass="tituloCampos" value="Produto do Tipo Sessão" />
                    <h:panelGroup >
                        <div style="width: 194px;">
                            <h:selectBooleanCheckbox styleClass="tituloCampos" value="#{FaturamentoSinteticoControleRel.produtoSessao}"/>
                            <div style="float:right">
                                <h:outputText style="float: left" styleClass="tituloCampos" value="Taxa de Personal" />
                                <h:selectBooleanCheckbox style="float: right" styleClass="tituloCampos" value="#{FaturamentoSinteticoControleRel.taxaPersonal}"/>
                            </div>
                        </div>
                    </h:panelGroup>


                    <h:outputText styleClass="tituloCampos" value="Acerto C/C Aluno" />
                    <h:panelGroup >
                        <h:selectBooleanCheckbox styleClass="tituloCampos" value="#{FaturamentoSinteticoControleRel.acertoCCAluno}"/>
                        <rich:spacer width="39px"/>
                        <h:outputText styleClass="tituloCampos" value="Pgto Saldo Devedor" />
                        <h:selectBooleanCheckbox styleClass="tituloCampos" value="#{FaturamentoSinteticoControleRel.pgtoSaldoDevedor}"/>
                    </h:panelGroup>
                    <h:outputText styleClass="tituloCampos" value="Quitação - Cancelamento" />
                    <h:panelGroup id="mesReferencia">
                        <h:selectBooleanCheckbox styleClass="tituloCampos" value="#{FaturamentoSinteticoControleRel.quitacaoCancelamento}"/>
                        <rich:spacer width="41px"/>
                        <h:outputText styleClass="tituloCampos" value="Atestado Ap. Física"/>
                        <h:selectBooleanCheckbox styleClass="tituloCampos"
                                                 value="#{FaturamentoSinteticoControleRel.atestado}"/>
                    </h:panelGroup>

                    <h:outputText  styleClass="tituloCampos" value="Alterar - Horário" />
                    <h:panelGroup>
                        <h:selectBooleanCheckbox styleClass="tituloCampos" value="#{FaturamentoSinteticoControleRel.alterarHorario}"/>
                        <rich:spacer width="107px"/>
                        <h:outputText styleClass="tituloCampos" value="Armário"/>
                        <h:selectBooleanCheckbox styleClass="tituloCampos"
                                                 value="#{FaturamentoSinteticoControleRel.armario}"/>
                    </h:panelGroup>

                    <h:outputText styleClass="tituloCampos" value="Bio Totem"/>
                    <h:panelGroup>
                        <h:selectBooleanCheckbox styleClass="tituloCampos"
                                                 value="#{FaturamentoSinteticoControleRel.bioTotem}"/>
                        <rich:spacer width="33px"/>
                        <h:outputText styleClass="tituloCampos" value="Consulta Nutricional"/>
                        <h:selectBooleanCheckbox styleClass="tituloCampos"
                                                 value="#{FaturamentoSinteticoControleRel.consultaNutricional}"/>
                    </h:panelGroup>

                    <h:outputText styleClass="tituloCampos" value="Taxa de Renegociação"/>
                    <h:panelGroup>
                        <div style="width: 194px;">
                            <h:selectBooleanCheckbox styleClass="tituloCampos"
                                                     value="#{FaturamentoSinteticoControleRel.taxaRenegociacao}"/>
                            <div style="float:right">
                                <h:outputText style="float: left" styleClass="tituloCampos" value="Locação" />
                                <h:selectBooleanCheckbox style="float: right" styleClass="tituloCampos" value="#{FaturamentoSinteticoControleRel.locacao}"/>
                            </div>
                        </div>
                    </h:panelGroup>


                    <h:outputText  styleClass="tituloCampos" value="Produto Estoque" />
                    <h:panelGroup id="painelProdutoEstoque">
                        <h:selectBooleanCheckbox styleClass="tituloCampos" value="#{FaturamentoSinteticoControleRel.produtoEstoque}">
                            <a4j:support reRender="painelProdutoEstoque" event="onclick"/>
                        </h:selectBooleanCheckbox>
                        <rich:spacer width="114px"/>
                        <h:outputText styleClass="tituloCampos" value="Evento" rendered="#{FaturamentoSinteticoControleRel.produtoEstoque}"/>
                        <h:selectBooleanCheckbox styleClass="tituloCampos" rendered="#{FaturamentoSinteticoControleRel.produtoEstoque}"
                                                 value="#{FaturamentoSinteticoControleRel.evento}"/>
                    </h:panelGroup>

                    <h:outputText  styleClass="tituloCampos" value="Mês de Referência Plano" />

                    <h:panelGroup id="painelMesReferencia">
                        <h:selectBooleanCheckbox styleClass="tituloCampos" value="#{FaturamentoSinteticoControleRel.mesReferenciaPlano}" id="checkMesRefPlano">
                            <a4j:support reRender="painelMesReferencia,mesReferencia" event="onclick"/>
                        </h:selectBooleanCheckbox>
                        <rich:spacer width="54px"/>
                        <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_FaturamentoSintetico_agrupamento}"
                                      rendered="#{FaturamentoSinteticoControleRel.mesReferenciaPlano}"/>
                        <a4j:region id="filtroAgrupamentoPlano">
                            <h:selectOneMenu  id="agrupar" value="#{FaturamentoSinteticoControleRel.agrupamento}"
                                              rendered="#{FaturamentoSinteticoControleRel.mesReferenciaPlano}">
                                <f:selectItems value="#{FaturamentoSinteticoControleRel.tipoAgrupamento}" />
                            </h:selectOneMenu>
                        </a4j:region>
                    </h:panelGroup>

                    <h:outputText styleClass="tituloCampos" value="Dep. Cta Corrente Aluno"
                                  rendered="#{FaturamentoSinteticoControleRel.somenteFaturamentoRecebido}"/>
                    <h:panelGroup id="painelCC" rendered="#{FaturamentoSinteticoControleRel.somenteFaturamentoRecebido}">
                        <h:selectBooleanCheckbox styleClass="tituloCampos"
                                                 value="#{FaturamentoSinteticoControleRel.creditoContaCorrente}"
                                                 rendered="#{FaturamentoSinteticoControleRel.somenteFaturamentoRecebido}"/>
                    </h:panelGroup>

                    <c:if test="${FaturamentoSinteticoControleRel.somenteFaturamentoRecebido}">
                        <h:outputText styleClass="tituloCampos" value="Outros:"/>
                        <h:outputText styleClass="tituloCampos" value=""/>
                        <h:outputText styleClass="tituloCampos" value="Modalidade: "/>
                        <h:selectOneMenu id="modalidade" onblur="blurinput(this);" onfocus="focusinput(this);"
                                         styleClass="form"
                                         value="#{FaturamentoSinteticoControleRel.modalidadeSelecionada}">
                            <f:selectItems value="#{FaturamentoSinteticoControleRel.listaModalidade}"/>
                            <a4j:support event="onchange" action="#{FaturamentoSinteticoControleRel.montarTurmas}"
                                         reRender="turma"/>
                        </h:selectOneMenu>

                        <h:outputText styleClass="tituloCampos" value="Turma: "/>
                        <h:selectOneMenu id="turma" onblur="blurinput(this);" onfocus="focusinput(this);"
                                         styleClass="form"
                                         value="#{FaturamentoSinteticoControleRel.turmaSelecionada}">
                            <f:selectItems value="#{FaturamentoSinteticoControleRel.listaTurma}"/>
                        </h:selectOneMenu>
                        <h:outputText styleClass="tituloCampos" value="Categoria: "/>
                        <h:selectOneMenu id="categoria" onblur="blurinput(this);" onfocus="focusinput(this);"
                                         styleClass="form"
                                         value="#{FaturamentoSinteticoControleRel.categoriaSelecionada}">
                            <f:selectItems value="#{FaturamentoSinteticoControleRel.listaCategorias}"/>
                        </h:selectOneMenu>
                        <h:outputText styleClass="tituloCampos" value="Forma de Pagamento: "/>
                        <h:selectOneMenu id="formaPagamento" onblur="blurinput(this);" onfocus="focusinput(this);"
                                         styleClass="form"
                                         value="#{FaturamentoSinteticoControleRel.formaPagamentoSelecionada}">
                            <f:selectItems value="#{FaturamentoSinteticoControleRel.listaFormaPagamento}"/>
                        </h:selectOneMenu>
                    </c:if>

                </h:panelGrid>
                <h:panelGrid columns="2" width="100%" styleClass="tabMensagens">
                    <h:panelGrid id="mensagem" columns="1" width="100%" columnClasses="colunaEsquerda">
                        <h:outputText styleClass="mensagem" value="#{FaturamentoSinteticoControleRel.mensagem}"/>
                        <h:outputText styleClass="mensagemDetalhada"
                                      value="#{FaturamentoSinteticoControleRel.mensagemDetalhada}"/>
                    </h:panelGrid>
                    <h:panelGrid id="mensagema" columns="1" width="100%" columnClasses="colunaCentralizada">

                    </h:panelGrid>
                </h:panelGrid>
                <h:panelGroup layout="block" styleClass="container-botoes">
                    <a4j:commandLink id="imprimirPDF" styleClass="botoes nvoBt"
                                     action="#{FaturamentoSinteticoControleRel.imprimir}" accesskey="2">
                        <i class="fa-icon-print"></i> Gerar Relatório
                    </a4j:commandLink>
                </h:panelGroup>
            </h:panelGrid>
            <rich:jQuery id="mskData" selector=".rich-calendar-input" timing="onload" query="mask('99/99/9999')" />
        </h:form>
    </h:panelGrid>

    <rich:modalPanel id="panelOperador" styleClass="novaModal" autosized="true" shadowOpacity="true" width="500" height="250">
        <f:facet name="header">
            <h:panelGroup>
                <h:outputText value="Consulta Operador"></h:outputText>
            </h:panelGroup>
        </f:facet>
        <f:facet name="controls">
            <h:panelGroup>
                <h:outputText
                        styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign btnSelecionarModalidade"
                        id="hidelink1"/>
                <rich:componentControl for="panelOperador" attachTo="hidelink1" operation="hide" event="onclick"/>
            </h:panelGroup>
        </f:facet>
        <a4j:form id="formOperador" ajaxSubmit="true" styleClass="font-size-Em-max">
            <h:panelGrid columns="1" width="100%" >
                <h:panelGrid columns="4" footerClass="colunaCentralizada" width="100%">
                    <h:outputText styleClass="texto-font texto-size-14-real texto-cor-cinza" value="#{msg.msg_consultar_por}"/>
                    <h:panelGroup layout="block" styleClass="cb-container">
                        <h:selectOneMenu id="consultaoperador"
                                         value="#{FaturamentoSinteticoControleRel.campoConsultarOperador}">
                            <f:selectItems value="#{FaturamentoSinteticoControleRel.tipoConsultaComboOperador}"/>
                        </h:selectOneMenu>
                    </h:panelGroup>
                    <h:inputText id="valorConsultaOperador" size="10"  value="#{FaturamentoSinteticoControleRel.valorConsultarOperador}"/>
                    <a4j:commandLink  id="btnConsultarOperador" reRender="formOperador"
                                      action="#{FaturamentoSinteticoControleRel.consultarOperador}"
                                      styleClass="botaoPrimario texto-size-14-real" value="#{msg_bt.btn_consultar}"
                                      title="#{msg.msg_consultar_dados}"/>
                </h:panelGrid>
                <rich:dataTable id="resultadoConsultaOperador" width="100%" styleClass="tabelaSimplesCustom"
                                rendered="#{not empty FaturamentoSinteticoControleRel.listaConsultarOperador}"
                                value="#{FaturamentoSinteticoControleRel.listaConsultarOperador}" rows="5" var="operador">
                    <rich:column  styleClass="col-text-align-left" headerClass="col-text-align-left">
                        <f:facet name="header">
                            <h:outputText  styleClass="texto-upper texto-font texto-size-14-real texto-cor-cinza texto-bold" value="#{msg_aplic.prt_Usuario_operador}"/>
                        </f:facet>
                        <h:panelGroup>
                            <a4j:commandLink styleClass="linkPadrao texto-size-14-real texto-cor-azul" action="#{FaturamentoSinteticoControleRel.selecionarOperador}" focus="operador" reRender="form" oncomplete="Richfaces.hideModalPanel('panelOperador')" value="#{operador.pessoa.nome}" />
                        </h:panelGroup>
                    </rich:column>
                    <rich:column  styleClass="col-text-align-right" headerClass="col-text-align-right">
                        <a4j:commandLink  styleClass="linkPadrao texto-size-14-real texto-cor-azul" action="#{FaturamentoSinteticoControleRel.selecionarOperador}"
                                          focus="operador" reRender="form" oncomplete="Richfaces.hideModalPanel('panelOperador')"
                                          title="#{msg.msg_selecionar_dados}">
                            <span>Selecionar </span>
                            <i class="fa-icon-arrow-right"></i>
                        </a4j:commandLink>
                    </rich:column>
                </rich:dataTable>
                <rich:datascroller align="center" for="formOperador:resultadoConsultaOperador" maxPages="10"
                                   styleClass="scrollPureCustom" renderIfSinglePage="false"
                                   id="scResultadoOperador" />
                <h:panelGrid id="mensagemConsultaOperador" columns="1" width="100%" styleClass="tabMensagens">
                    <h:panelGrid columns="3" width="100%" styleClass="tabMensagens">
                        <h:panelGrid columns="1" width="100%">
                            <h:outputText styleClass="mensagem"  value="#{FaturamentoSinteticoControleRel.mensagem}"/>
                            <h:outputText styleClass="mensagemDetalhada" value="#{FaturamentoSinteticoControleRel.mensagemDetalhada}"/>
                        </h:panelGrid>
                    </h:panelGrid>
                </h:panelGrid>
            </h:panelGrid>
        </a4j:form>
    </rich:modalPanel>
    <rich:modalPanel id="panelColaborador" styleClass="novaModal" autosized="true" shadowOpacity="true" width="500" height="250">
        <f:facet name="header">
            <h:panelGroup>
                <h:outputText value="Consulta Colaborador"></h:outputText>
            </h:panelGroup>
        </f:facet>
        <f:facet name="controls">
            <h:panelGroup>
                <h:outputText
                        styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign btnSelecionarModalidade"
                        id="hidelink2"/>
                <rich:componentControl for="panelColaborador" attachTo="hidelink2" operation="hide" event="onclick"/>
            </h:panelGroup>
        </f:facet>
        <a4j:form id="formColaborador" ajaxSubmit="true" styleClass="font-size-Em-max">
            <h:panelGrid columns="1" width="100%" >
                <h:panelGrid columns="4" footerClass="colunaCentralizada" width="100%">
                    <h:outputText styleClass="texto-font texto-size-14-real texto-cor-cinza"  value="#{msg.msg_consultar_por}"/>
                    <h:panelGroup layout="block" styleClass="cb-container">
                        <h:selectOneMenu id="consultacolaborador"
                                         value="#{FaturamentoSinteticoControleRel.campoConsultarColaborador}">
                            <f:selectItems value="#{FaturamentoSinteticoControleRel.tipoConsultaComboColaborador}"/>
                        </h:selectOneMenu>
                    </h:panelGroup>
                    <h:inputText id="valorConsultaColaborador" size="10"  value="#{FaturamentoSinteticoControleRel.valorConsultarColaborador}"/>
                    <a4j:commandLink  id="btnConsultarColaborador"
                                      reRender="formColaborador"
                                      action="#{FaturamentoSinteticoControleRel.consultarColaborador}"
                                      styleClass="botaoPrimario texto-size-16-real texto-cor-azul"
                                      value="#{msg_bt.btn_consultar}"
                                      title="#{msg.msg_consultar_dados}"/>
                </h:panelGrid>
                <rich:dataTable id="resultadoConsultaColaborador" width="100%" styleClass="tabelaSimplesCustom"
                                rendered="#{not empty FaturamentoSinteticoControleRel.listaConsultarColaborador}"
                                value="#{FaturamentoSinteticoControleRel.listaConsultarColaborador}" rows="5" var="colaborador">
                    <rich:column styleClass="col-text-align-left" headerClass="col-text-align-left">
                        <f:facet name="header">
                            <h:outputText styleClass="texto-upper texto-font texto-size-14-real texto-cor-cinza texto-bold"  value="#{msg_aplic.prt_Usuario_colaborador}"/>
                        </f:facet>
                        <h:panelGroup>
                            <a4j:commandLink styleClass="linkPadrao texto-size-14-real texto-cor-azul"
                                             action="#{FaturamentoSinteticoControleRel.selecionarColaborador}"
                                             focus="colaborador" reRender="form"
                                             oncomplete="Richfaces.hideModalPanel('panelColaborador')"
                                             value="#{colaborador.pessoa.nome}" />
                        </h:panelGroup>
                    </rich:column>
                    <rich:column styleClass="col-text-align-right" headerClass="col-text-align-right">
                        <a4j:commandLink action="#{FaturamentoSinteticoControleRel.selecionarColaborador}"
                                         focus="colaborador" reRender="form"
                                         oncomplete="Richfaces.hideModalPanel('panelColaborador')"
                                         title="#{msg.msg_selecionar_dados}" styleClass="linkPadrao texto-size-14-real texto-cor-azul">
                            <span>Selecionar </span>
                            <i class="fa-icon-arrow-right"></i>
                        </a4j:commandLink>
                    </rich:column>
                </rich:dataTable>
                <rich:datascroller align="center" for="formColaborador:resultadoConsultaColaborador" maxPages="10"   styleClass="scrollPureCustom"
                                   renderIfSinglePage="false"
                                   id="scResultadoColaborador" />
                <h:panelGrid id="mensagemConsultaColaborador" columns="1" width="100%" styleClass="tabMensagens">
                    <h:panelGrid columns="3" width="100%" styleClass="tabMensagens">
                        <h:panelGrid columns="1" width="100%">
                            <h:outputText styleClass="mensagem"  value="#{FaturamentoSinteticoControleRel.mensagem}"/>
                            <h:outputText styleClass="mensagemDetalhada" value="#{FaturamentoSinteticoControleRel.mensagemDetalhada}"/>
                        </h:panelGrid>
                    </h:panelGrid>
                </h:panelGrid>
            </h:panelGrid>
        </a4j:form>
    </rich:modalPanel>
</f:view>
<script>
    document.getElementById("form:dataInicio").focus();
</script>
