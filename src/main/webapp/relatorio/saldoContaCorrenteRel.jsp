<%@page contentType="text/html"%>
<%@page pageEncoding="ISO-8859-1"%>
<head>
    <%@include file="/includes/include_import_minifiles.jsp" %>
    <script type="text/javascript" language="javascript" src="../script/script.js"></script>
</head>
<script type="text/javascript" language="javascript" src="../hoverform.js"></script>
<link href="../css/otimize.css" rel="stylesheet" type="text/css">
<link href="../beta/css/font-awesome_2.0.min.css" type="text/css" rel="stylesheet"/>
<title>Relatório de Saldo de Conta Corrente do Cliente</title>

<%@taglib prefix="f" uri="http://java.sun.com/jsf/core"%>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html"%>

<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax"%>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich"%>
<%@taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>

<f:loadBundle var="msg_bt" basename="propriedades.Botoes"/>
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao"/>
<f:loadBundle var="msg" basename="propriedades.Mensagens"/>

<%@include file="/includes/verificaModulo.jsp" %>
<c:set var="contexto" value="${pageContext.request.contextPath}" scope="request" />

<f:view>
    <jsp:include page="/includes/include_carregando_ripple.jsp" flush="true"/>
    <c:set var="titulo" scope="session" value="${msg_aplic.prt_SaldoContaCorrente_tituloRel}"/>
    <c:set var="urlWiki" scope="session" value="${SuperControle.urlBaseConhecimento}como-tirar-um-relatorio-dos-alunos-que-possuem-saldo-na-conta-corrente/"/>
    <rich:modalPanel id="panelCliente" styleClass="novaModal" autosized="true" shadowOpacity="true" width="500" height="250">
        <f:facet name="header">
            <h:panelGroup>
                <h:outputText value="Consulta Cliente"></h:outputText>
            </h:panelGroup>
        </f:facet>
        <f:facet name="controls">
            <h:panelGroup>
                <h:outputText
                        styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign btnSelecionarModalidade"
                        id="hidelink2"/>
                <rich:componentControl for="panelCliente" attachTo="hidelink2" operation="hide" event="onclick"/>
            </h:panelGroup>
        </f:facet>

        <a4j:form id="formCliente" ajaxSubmit="true" styleClass="font-size-Em-max">
            <h:panelGrid columns="1" width="100%" >
                <h:panelGrid columns="4" footerClass="colunaCentralizada" width="100%">
                    <h:outputText   styleClass="texto-font texto-size-14-real texto-cor-cinza " value="#{msg.msg_consultar_por}"/>
                    <h:panelGroup layout="block" styleClass="cb-container">
                        <h:selectOneMenu id="consultacliente"
                                         value="#{SaldoContaCorrenteControleRel.campoConsultarCliente}">
                            <f:selectItems value="#{SaldoContaCorrenteControleRel.tipoConsultaComboCliente}"/>
                        </h:selectOneMenu>
                    </h:panelGroup>
                    <h:inputText id="valorConsultaCliente" size="10"  value="#{SaldoContaCorrenteControleRel.valorConsultarCliente}"/>

                    <c:if test="${modulo eq 'zillyonWeb'}">
                        <a4j:commandLink  id="btnConsultarCliente"
                                          reRender="formCliente"
                                          action="#{SaldoContaCorrenteControleRel.consultarCliente}"
                                          styleClass="botaoPrimario texto-size-16-real texto-cor-azul" value="#{msg_bt.btn_consultar}" title="#{msg.msg_consultar_dados}"/>
                    </c:if>
                    <c:if test="${modulo eq 'centralEventos'}">
                        <a4j:commandLink  id="btnConsultarCliente" reRender="formCliente"
                                          action="#{SaldoContaCorrenteControleRel.consultarCliente}"
                                          styleClass="botaoPrimario texto-size-16-real texto-cor-azul" value="#{msg_bt.btn_consultar}"
                                          title="#{msg.msg_consultar_dados}"/>
                    </c:if>
                </h:panelGrid>

                <rich:dataTable id="resultadoConsultaCliente" width="100%"  styleClass="tabelaSimplesCustom"
                                rendered="#{not empty SaldoContaCorrenteControleRel.listaConsultarCliente}"
                                value="#{SaldoContaCorrenteControleRel.listaConsultarCliente}" rows="5" var="cliente">
                    <rich:column styleClass="col-text-align-left" headerClass="col-text-align-left">
                        <f:facet name="header">
                            <h:outputText styleClass="texto-upper texto-font texto-size-14-real texto-cor-cinza texto-bold"   value="#{msg_aplic.prt_Usuario_nomePessoa}"/>
                        </f:facet>
                        <h:panelGroup>
                            <a4j:commandLink styleClass="linkPadrao texto-size-14-real texto-cor-azul"  action="#{SaldoContaCorrenteControleRel.selecionarCliente}"
                                             focus="cliente" reRender="form" oncomplete="Richfaces.hideModalPanel('panelCliente')" value="#{cliente.pessoa.nome}" />
                        </h:panelGroup>
                    </rich:column>
                    <rich:column styleClass="col-text-align-right" headerClass="col-text-align-right">
                        <a4j:commandLink styleClass="linkPadrao texto-size-14-real texto-cor-azul"
                                           action="#{SaldoContaCorrenteControleRel.selecionarCliente}"
                                           focus="cliente" reRender="form" oncomplete="Richfaces.hideModalPanel('panelCliente')"
                                           title="#{msg.msg_selecionar_dados}">
                            <span>Selecionar </span>
                            <i class="fa-icon-arrow-right"></i>
                        </a4j:commandLink>
                    </rich:column>
                </rich:dataTable>
                <rich:datascroller align="center" for="formCliente:resultadoConsultaCliente" maxPages="10"
                                   styleClass="scrollPureCustom" renderIfSinglePage="false"
                                   id="scResultadoCliente" />
                <h:panelGrid id="mensagemConsultaCliente" columns="1" width="100%" styleClass="tabMensagens">
                    <h:panelGrid columns="3" width="100%" styleClass="tabMensagens">
                        <h:panelGrid columns="1" width="100%">
                            <h:outputText styleClass="mensagem"  value="#{SaldoContaCorrenteControleRel.mensagem}"/>
                            <h:outputText styleClass="mensagemDetalhada" value="#{SaldoContaCorrenteControleRel.mensagemDetalhada}"/>
                        </h:panelGrid>
                    </h:panelGrid>
                </h:panelGrid>
            </h:panelGrid>
        </a4j:form>
    </rich:modalPanel>



    <h:panelGrid columns="1" width="100%" cellpadding="0" cellspacing="0">
        <f:facet name="header">
            
                <c:if test="${modulo eq 'zillyonWeb'}">
                    <jsp:include page="../topoReduzido_material.jsp"/>
                </c:if>
                <c:if test="${modulo eq 'centralEventos'}">
                    <jsp:include page="../pages/ce/includes/topoReduzido.jsp"/></c:if>
            
        </f:facet>

        <h:form id="form" target="_blank" >
        
        <input type="hidden" value="${modulo}" name="modulo"/>
        
            <h:commandLink action="#{SaldoContaCorrenteControleRel.liberarBackingBeanMemoria}" id="idLiberarBackingBeanMemoria" style="display: none" />
            <h:commandLink action="#{SaldoContaCorrenteControleRel.imprimirRelatorio}" id="imprimirRelatorio" style="display: none" />
            <h:inputHidden id="relatorio" value="#{SaldoContaCorrenteControleRel.relatorio}" />
            <h:panelGrid columns="1" width="100%" >
                <h:panelGrid columns="2" rowClasses="linhaImpar, linhaPar" columnClasses="classEsquerda, classDireita" width="100%">


                    <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_SaldoContaCorrente_Empresa}" rendered="#{SaldoContaCorrenteControleRel.temEmpresa}" />
                    <h:panelGrid rendered="#{SaldoContaCorrenteControleRel.temEmpresa}" >
                        <h:selectOneMenu  id="empresas" onblur="blurinput(this);"  onfocus="focusinput(this);" styleClass="form" value="#{SaldoContaCorrenteControleRel.saldoContaCorrenteRel.filtroEmpresa}" >
                            <f:selectItems  value="#{SaldoContaCorrenteControleRel.listaDeEmpresa}"/>
                        </h:selectOneMenu>
                    </h:panelGrid>


                    <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_SaldoContaCorrente_Clientenome}" />
                    <h:panelGroup>
                        <h:inputText id="nomeCliente" size="40" maxlength="40" onblur="blurinput(this);"  onfocus="focusinput(this);" styleClass="form" value="#{SaldoContaCorrenteControleRel.saldoContaCorrenteRel.clienteVO.pessoa.nome}" />
                        <a4j:commandButton id="consultarCliente" oncomplete="Richfaces.showModalPanel('panelCliente'), setFocus(formCliente,'formCliente:valorConsultarCliente')" alt="Consultar Operador" image="../imagens/informacao.gif" />
                        <rich:spacer width="5px" />
                        <a4j:commandButton id="LimparCliente"  image="../imagens/limpar.gif"  reRender="form:nomeCliente" action="#{SaldoContaCorrenteControleRel.limparCampoCliente}"/>
                    </h:panelGroup>

                    <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_SaldoContaCorrente_SaldoEntre}" />
                    <h:panelGroup>
                        <h:selectOneMenu  id="saldoMenor" styleClass="form" value="#{SaldoContaCorrenteControleRel.saldoContaCorrenteRel.saldoMenor}" >
                            <f:selectItems  value="#{SaldoContaCorrenteControleRel.listaSelectItemTipoPositivoNegativo}"/>
                        </h:selectOneMenu>
                        <rich:spacer width="15px"/>
                        <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_SaldoContaCorrente_ate}" />
                        <rich:spacer width="15px"/>
                        <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_SaldoContaCorrente_$}" />
                        <rich:spacer width="5px"/>
                        <h:selectOneMenu  id="saldoMaior" styleClass="form" value="#{SaldoContaCorrenteControleRel.saldoContaCorrenteRel.saldoMaior}" >
                            <f:selectItems  value="#{SaldoContaCorrenteControleRel.listaSelectItemTipoPositivoNegativo}"/>
                        </h:selectOneMenu>
                        <rich:spacer width="298px" />
                        <%--<h:panelGroup>
                            <h:selectOneRadio id="positivo" styleClass="tituloCampos" value="#{SaldoContaCorrenteControleRel.saldoContaCorrenteRel.positivoNegativo}" >
                                <f:selectItems  value="#{SaldoContaCorrenteControleRel.listaSelectItemTipoPositivoNegativo}"/>
                            </h:selectOneRadio>
                        </h:panelGroup>--%>
                    </h:panelGroup>
                </h:panelGrid>

                <h:panelGrid columns="1" width="100%" styleClass="tabMensagens">
                    <h:panelGrid id="mensagem" columns="1" width="100%" columnClasses="colunaEsquerda">
                        <h:outputText styleClass="mensagem"  value="#{SaldoContaCorrenteControleRel.mensagem}"/>
                        <h:outputText styleClass="mensagemDetalhada" value="#{SaldoContaCorrenteControleRel.mensagemDetalhada}"/>
                    </h:panelGrid>
                    <h:panelGrid columns="1" width="100%" styleClass="tabBotoes" columnClasses="colunaCentralizada">
                        <h:panelGroup>
                            <rich:spacer width="20px"/>
                            <c:if test="${modulo eq 'zillyonWeb'}">
                                <a4j:commandLink   id="imprimirPDF" action="#{SaldoContaCorrenteControleRel.imprimirPDF}"
                                                   oncomplete="#{SaldoContaCorrenteControleRel.nomeRefRelatorioGeradoAgora}"
                                                   accesskey="2"
                                                   styleClass="botoes nvoBt"
                                                   reRender="form">
                                    <i class="fa-icon-print"></i> Gerar Relatório (PDF)
                                </a4j:commandLink>
                            </c:if>
                            <c:if test="${modulo eq 'centralEventos'}">
                                <a4j:commandLink id="imprimirPDF" action="#{SaldoContaCorrenteControleRel.imprimirPDF}"
                                                   oncomplete="#{SaldoContaCorrenteControleRel.nomeRefRelatorioGeradoAgora}"
                                                   accesskey="2"
                                                   styleClass="botoes nvoBt" reRender="form">
                                        <i class="fa-icon-print"></i> Gerar Relatório (PDF)
                                </a4j:commandLink>
                            </c:if>

                        </h:panelGroup>
                    </h:panelGrid>
                </h:panelGrid>
            </h:panelGrid>
        </h:form>
    </h:panelGrid>

</f:view>
<script>
    document.getElementById("form:dataInicio").focus();
</script>
