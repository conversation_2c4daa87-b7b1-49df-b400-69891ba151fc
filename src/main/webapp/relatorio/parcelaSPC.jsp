<%@page contentType="text/html" %>
<%@page pageEncoding="ISO-8859-1" %>
<head>
    <%@include file="../includes/include_import_minifiles.jsp" %>
</head>
<script type="text/javascript" language="javascript" src="../hoverform.js"></script>
<script type="text/javascript" language="javascript" src="../script/ce_script.js"></script>
<link href="../css/otimize.css" rel="stylesheet" type="text/css">
<link href="../beta/css/font-awesome_2.0.min.css" type="text/css" rel="stylesheet"/>
<link href="../beta/css/pure-ext.css" type="text/css" rel="stylesheet"/>
<script src="../bootstrap/jquery.js" type="text/javascript"></script>
<script type="text/javascript" src="../script/tooltipster/jquery.tooltipster.min.js"></script>
<title>Gestão de Negativações</title>

<%@taglib prefix="f" uri="http://java.sun.com/jsf/core" %>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html" %>

<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax" %>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich" %>
<%@taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>

<f:loadBundle var="msg_bt" basename="propriedades.Botoes"/>
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao"/>
<f:loadBundle var="msg" basename="propriedades.Mensagens"/>

<%@include file="/includes/verificaModulo.jsp" %>

<c:set var="contexto" value="${pageContext.request.contextPath}" scope="request"/>

<style type="text/css">
    .notificacaoAtividades {
        -webkit-background-clip: padding-box;
        -webkit-box-shadow: rgba(0, 0, 0, 0.701961) 0 1px 1px 0;
        -webkit-font-smoothing: subpixel-antialiased;
        background-clip: padding-box;
        background: #819aa5 -webkit-linear-gradient(top, #96acb6,
        #5d7b89);
        box-shadow: rgba(0, 0, 0, 0.701961) 0 1px 1px 0;
        color: rgb(255, 255, 255) !important;
        font-family: 'Helvetica Neue', Helvetica, sans-serif;
        font-size: 10px !important;
        height: 13px !important;
        line-height: normal;
        list-style-type: none;
        padding: 1px 3px !important;
        text-align: center;
        text-shadow: rgba(0, 0, 0, 0.4) 0 -1px 0;
        zoom: 1;
        border-radius: 40%;
    }

    .iconCalendar {
        width: 17px;
        height: 17px;
        padding-top: 6px;
        padding-left: 5px;
    }

    input.inputs, select.inputs {
        padding: 5px;
        background-image: none !important;
        font-size: 12px !important;
        border-radius: 4px;
        color: #A1A5AA;
        border: 1px solid #DCDDDF;
        width: 80%;
    }

    .separador-horizontal {
        margin: 10px 0 10px 0;
        width: 100%;
        height: 1px;
        border-bottom: 1px solid #E5E5E5;
    }

    .tituloCampos {
        color: #51555A !important;
        text-align: right;
    }

    .iconeDentro {
        margin-left: -23px;
    }

    .width100 {
        width: 100% !important;
    }
</style>

<f:view>
    <jsp:include page="/includes/include_carregando_ripple.jsp" flush="true"/>
    <a4j:loadScript src="/script/jquery.maskedinput-1.2.2.js"/>
    <c:set var="titulo" scope="session" value="Gestão de Negativações"/>
    <c:set var="urlWiki" scope="session" value="${SuperControle.urlBaseConhecimento}como-negativar-aluno-pelo-sistema-spc/"/>

    <rich:modalPanel id="v20_panelCliente" styleClass="novaModal" autosized="true" shadowOpacity="true" width="500"
                     height="250">
        <f:facet name="header">
            <h:panelGroup>
                <h:outputText value="Consulta Cliente"/>
            </h:panelGroup>
        </f:facet>
        <f:facet name="controls">
            <h:panelGroup>
                <h:outputText
                        styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign btnSelecionarModalidade"
                        id="v20_hidelink2"/>
                <rich:componentControl for="v20_panelCliente" attachTo="v20_hidelink2" operation="hide"
                                       event="onclick"/>
            </h:panelGroup>
        </f:facet>

        <a4j:form id="v20_formCliente" ajaxSubmit="true" styleClass="font-size-Em-max">
            <h:panelGrid columns="1" width="100%">
                <h:panelGrid columns="4" footerClass="colunaCentralizada" width="100%">
                    <h:outputText styleClass="texto-font texto-size-14-real texto-cor-cinza "
                                  value="#{msg.msg_consultar_por}"/>
                    <h:panelGroup layout="block" styleClass="cb-container">
                        <h:selectOneMenu id="v20_consultacliente"
                                         value="#{ParcelaEmAbertoSPCControleRel.campoConsultarCliente}">
                            <f:selectItems value="#{ParcelaEmAbertoSPCControleRel.tipoConsultaComboCliente}"/>
                        </h:selectOneMenu>
                    </h:panelGroup>
                    <h:inputText id="v20_valorConsultaCliente" size="10"
                                 value="#{ParcelaEmAbertoSPCControleRel.valorConsultarCliente}"/>

                    <c:if test="${modulo eq 'zillyonWeb'}">
                        <a4j:commandLink id="v20_btnConsultarCliente" reRender="v20_formCliente"
                                         action="#{ParcelaEmAbertoSPCControleRel.consultarCliente}"
                                         styleClass="botaoPrimario texto-size-16-real texto-cor-azul"
                                         value="#{msg_bt.btn_consultar}"
                                         title="#{msg.msg_consultar_dados}"/>
                    </c:if>
                    <c:if test="${modulo eq 'centralEventos'}">
                        <a4j:commandLink id="v20_btnConsultarCliente" reRender="v20_formCliente"
                                         action="#{ParcelaEmAbertoSPCControleRel.consultarCliente}"
                                         styleClass="botaoPrimario texto-size-16-real texto-cor-azul"
                                         value="#{msg_bt.btn_consultar}"
                                         title="#{msg.msg_consultar_dados}"/>
                    </c:if>

                </h:panelGrid>

                <rich:dataTable id="v20_resultadoConsultaCliente" width="100%" styleClass="tabelaSimplesCustom"
                                rendered="#{not empty ParcelaEmAbertoSPCControleRel.listaConsultarCliente}"
                                value="#{ParcelaEmAbertoSPCControleRel.listaConsultarCliente}" rows="5" var="cliente">
                    <rich:column styleClass="col-text-align-left" headerClass="col-text-align-left">
                        <f:facet name="header">
                            <h:outputText
                                    styleClass="texto-upper texto-font texto-size-14-real texto-cor-cinza texto-bold"
                                    value="#{msg_aplic.prt_Usuario_nomePessoa}"/>
                        </f:facet>
                        <h:panelGroup>
                            <a4j:commandLink styleClass="linkPadrao texto-size-14-real texto-cor-azul"
                                             action="#{ParcelaEmAbertoSPCControleRel.selecionarCliente}" focus="cliente"
                                             reRender="v20_form"
                                             oncomplete="Richfaces.hideModalPanel('v20_panelCliente')"
                                             value="#{cliente.pessoa.nome}"/>
                        </h:panelGroup>
                    </rich:column>
                    <rich:column styleClass="col-text-align-right" headerClass="col-text-align-right">
                        <a4j:commandLink styleClass="linkPadrao texto-size-14-real texto-cor-azul"
                                         action="#{ParcelaEmAbertoSPCControleRel.selecionarCliente}"
                                         focus="cliente" reRender="v20_form"
                                         oncomplete="Richfaces.hideModalPanel('v20_panelCliente')"
                                         title="#{msg.msg_selecionar_dados}">
                            <span>Selecionar </span>
                            <i class="fa-icon-arrow-right"></i>
                        </a4j:commandLink>
                    </rich:column>
                </rich:dataTable>
                <rich:datascroller align="center" for="v20_formCliente:v20_resultadoConsultaCliente" maxPages="10"
                                   styleClass="scrollPureCustom"
                                   id="v20_scResultadoCliente" renderIfSinglePage="false"/>
                <h:panelGrid id="v20_mensagemConsultaCliente" columns="1" width="100%" styleClass="tabMensagens">
                    <h:panelGrid columns="3" width="100%" styleClass="tabMensagens">
                        <h:panelGrid columns="1" width="100%">
                            <h:outputText styleClass="mensagem" value="#{ParcelaEmAbertoSPCControleRel.mensagem}"/>
                            <h:outputText styleClass="mensagemDetalhada"
                                          value="#{ParcelaEmAbertoSPCControleRel.mensagemDetalhada}"/>
                        </h:panelGrid>
                    </h:panelGrid>
                </h:panelGrid>
            </h:panelGrid>
        </a4j:form>
    </rich:modalPanel>

    <h:panelGrid id="panelFiltros" columns="1" width="100%" cellpadding="0" cellspacing="0">
        <f:facet name="header">

            <c:if test="${modulo eq 'zillyonWeb'}">
                <jsp:include page="../topoReduzido_material.jsp"/>
            </c:if>
            <c:if test="${modulo eq 'centralEventos'}">
                <jsp:include page="../pages/ce/includes/topoReduzido.jsp"/>
            </c:if>

        </f:facet>

        <h:form id="v20_form" target="_blank">

            <input type="hidden" value="${modulo}" name="modulo"/>

            <h:commandLink action="#{ParcelaEmAbertoSPCControleRel.liberarBackingBeanMemoria}"
                           id="v20_idLiberarBackingBeanMemoria" style="display: none"/>
            <h:commandLink action="#{ParcelaEmAbertoSPCControleRel.imprimirRelatorio}" id="v20_imprimirRelatorio"
                           style="display: none"/>
            <h:inputHidden id="v20_relatorio" value="#{ParcelaEmAbertoSPCControleRel.relatorio}"/>

            <h:panelGroup styleClass="separador-horizontal" layout="block"/>
            <h:panelGroup id="fgroup_filtros" layout="block" style="display: flex; flex-direction: column;">
                <h:panelGrid columns="2"
                             style="margin: 10px; display: inline-flex">
                    <h:panelGrid columns="1">
                        <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_ParcelaEmAberto_Empresa}"/>
                        <h:panelGroup layout="block">
                            <h:selectOneMenu id="v20_empresas"
                                             style="width: 200px"
                                             disabled="#{!ParcelaEmAbertoSPCControleRel.permissaoConsultaTodasEmpresas}"
                                             styleClass="inputs" value="#{ParcelaEmAbertoSPCControleRel.filtroEmpresa}">
                                <f:selectItems value="#{ParcelaEmAbertoSPCControleRel.listaEmpresas}"/>
                                <a4j:support action="#{ParcelaEmAbertoSPCControleRel.carregarPlanoseConvenios}"
                                             reRender="v20_form"
                                             event="onchange"/>
                            </h:selectOneMenu>
                        </h:panelGroup>
                    </h:panelGrid>

                    <h:panelGrid columns="1" cellspacing="0" cellpadding="0">
                        <h:panelGrid columns="1">
                            <h:outputText styleClass="tituloCampos"
                                          value="#{msg_aplic.prt_ParcelaEmAberto_periodoNegativacao}"/>
                        </h:panelGrid>
                        <h:panelGrid columns="3">
                            <h:panelGroup style="font-size: 11px !important;" layout="block">
                                <rich:calendar id="v20_dataInicioSpc"
                                               value="#{ParcelaEmAbertoSPCControleRel.parcelaEmAbertoRel.dataInicioSpc}"
                                               buttonClass="iconeDentro"
                                               inputClass="form inputs width100"
                                               buttonIcon="/imagens_flat/icon-calendar-check.png"
                                               oninputchange="return validar_Data(this.id);"
                                               datePattern="dd/MM/yyyy"
                                               enableManualInput="true"
                                               zindex="2"
                                               showWeeksBar="false"/>
                                <h:message for="v20_dataInicioSpc" styleClass="mensagemDetalhada"/>
                            </h:panelGroup>
                            <h:panelGroup style="font-size: 11px !important;" layout="block">
                                <rich:calendar id="v20_dataTerminoSpc"
                                               value="#{ParcelaEmAbertoSPCControleRel.parcelaEmAbertoRel.dataTerminoSpc}"
                                               buttonClass="iconeDentro"
                                               inputClass="form inputs width100"
                                               buttonIcon="/imagens_flat/icon-calendar-check.png"
                                               oninputchange="return validar_Data(this.id);"
                                               datePattern="dd/MM/yyyy"
                                               enableManualInput="true"
                                               zindex="2"
                                               showWeeksBar="false"/>
                                <h:message for="v20_dataTerminoSpc" styleClass="mensagemDetalhada"/>
                            </h:panelGroup>
                            <h:panelGroup style="font-size: 11px !important; margin-left: 5px !important;" layout="block">
                                <h:outputText styleClass="tituloCampos"
                                              value="Considerar somente parcelas de contratos assinados:"/>
                                <h:selectBooleanCheckbox id="somenteContratoAssinados" styleClass="campos" style="margin-left: 3px !important;"
                                                         value="#{ParcelaEmAbertoSPCControleRel.parcelaEmAbertoRel.somenteParcelasContratosAssinados}"/>
                            </h:panelGroup>
                        </h:panelGrid>
                    </h:panelGrid>
                </h:panelGrid>

                <h:panelGrid id="v20_groupForm" columns="6" style="display: inline-flex; margin: 10px;">

                    <h:panelGrid columns="1" cellspacing="0" cellpadding="0">
                        <h:panelGrid columns="1">
                            <h:outputText styleClass="tituloCampos"
                                          value="#{msg_aplic.prt_ParcelaEmAberto_periodoVencimento}"/>
                        </h:panelGrid>
                        <h:panelGrid columns="2">
                            <h:panelGroup style="font-size: 11px !important;" layout="block">
                                <rich:calendar id="v20_dataInicioVencimento"
                                               value="#{ParcelaEmAbertoSPCControleRel.parcelaEmAbertoRel.dataInicioVencimento}"
                                               buttonClass="iconeDentro"
                                               inputClass="form inputs width100"
                                               buttonIcon="/imagens_flat/icon-calendar-check.png"
                                               oninputchange="return validar_Data(this.id);"
                                               datePattern="dd/MM/yyyy"
                                               enableManualInput="true"
                                               zindex="2"
                                               showWeeksBar="false"/>
                                <h:message for="v20_dataInicioVencimento" styleClass="mensagemDetalhada"/>
                            </h:panelGroup>
                            <h:panelGroup style="font-size: 11px !important;" layout="block">
                                <rich:calendar id="v20_dataTerminoVencimento"
                                               value="#{ParcelaEmAbertoSPCControleRel.parcelaEmAbertoRel.dataTerminoVencimento}"
                                               buttonClass="iconeDentro"
                                               inputClass="form inputs width100"
                                               buttonIcon="/imagens_flat/icon-calendar-check.png"
                                               oninputchange="return validar_Data(this.id);"
                                               datePattern="dd/MM/yyyy"
                                               enableManualInput="true"
                                               zindex="2"
                                               showWeeksBar="false"/>
                                <h:message for="v20_dataTerminoVencimento" styleClass="mensagemDetalhada"/>
                            </h:panelGroup>
                        </h:panelGrid>
                    </h:panelGrid>

                    <h:panelGrid columns="1" cellspacing="0" cellpadding="0">
                        <h:panelGrid columns="1">
                            <h:outputText styleClass="tituloCampos"
                                          value="#{msg_aplic.prt_ParcelaEmAberto_periodoPagamento}"/>
                        </h:panelGrid>
                        <h:panelGrid columns="2">
                            <h:panelGroup style="font-size: 11px !important;" layout="block">
                                <rich:calendar id="v20_dataInicioPagamento"
                                               value="#{ParcelaEmAbertoSPCControleRel.parcelaEmAbertoRel.dataInicioPagamento}"
                                               buttonClass="iconeDentro"
                                               inputClass="form inputs width100"
                                               buttonIcon="/imagens_flat/icon-calendar-check.png"
                                               oninputchange="return validar_Data(this.id);"
                                               datePattern="dd/MM/yyyy"
                                               enableManualInput="true"
                                               zindex="2"
                                               showWeeksBar="false"/>
                                <h:message for="v20_dataInicioPagamento" styleClass="mensagemDetalhada"/>
                            </h:panelGroup>
                            <h:panelGroup style="font-size: 11px !important;" layout="block">
                                <rich:calendar id="v20_dataTerminoPagamento"
                                               value="#{ParcelaEmAbertoSPCControleRel.parcelaEmAbertoRel.dataTerminoPagamento}"
                                               buttonClass="iconeDentro"
                                               inputClass="form inputs width100"
                                               buttonIcon="/imagens_flat/icon-calendar-check.png"
                                               oninputchange="return validar_Data(this.id);"
                                               datePattern="dd/MM/yyyy"
                                               enableManualInput="true"
                                               zindex="2"
                                               showWeeksBar="false"/>
                                <h:message for="v20_dataTerminoPagamento" styleClass="mensagemDetalhada"/>
                            </h:panelGroup>
                        </h:panelGrid>
                    </h:panelGrid>

                    <h:panelGrid id="v20_grpSituacoes" columns="2" cellpadding="0">
                        <h:panelGrid columns="1">
                            <h:outputText styleClass="tituloCampos"
                                          value="#{msg_aplic.prt_ParcelaEmAberto_Situacao}"/>
                            <h:panelGroup layout="block">
                                <h:selectOneMenu id="v20_situacao" styleClass="inputs"
                                                 value="#{ParcelaEmAbertoSPCControleRel.parcelaEmAbertoRel.situacao}"
                                                 style="float: left; font-size: 12px !important; width: 100%;">
                                    <f:selectItems
                                            value="#{ParcelaEmAbertoSPCControleRel.listaSelectItemTipoSituacao}"/>
                                </h:selectOneMenu>
                            </h:panelGroup>
                        </h:panelGrid>
                        <h:panelGrid columns="3" style="padding-top: 15px;">
                            <a4j:commandButton id="v20_adicionarSituacaoSelecionado"
                                               action="#{ParcelaEmAbertoSPCControleRel.adicionarSituacaoNaLista}"
                                               reRender="v20_groupForm, v20_situacao, v20_mensagemConsultaResponsavel, v20_grpSituacoes"
                                               image="../imagens/botaoAdicionar.png" styleClass="botoes"/>

                            <h:panelGrid id="v20_qtdSituacoes" cellpadding="0" cellspacing="0" columns="2">
                                <h:panelGroup layout="block">
                                    <h:panelGroup layout="block" styleClass="notificacaoAtividades">
                                        <h:outputText
                                                value="#{ParcelaEmAbertoSPCControleRel.parcelaEmAbertoRel.qtdSituacoesSelecionadas}"/>
                                    </h:panelGroup>
                                </h:panelGroup>
                                <rich:toolTip
                                        value="#{ParcelaEmAbertoSPCControleRel.parcelaEmAbertoRel.situacoesSelecionadasApresentar}"
                                        for="v20_qtdSituacoes" followMouse="true"/>
                            </h:panelGrid>

                            <a4j:commandButton id="v20_limparSituacoesSelecionadas" image="../imagens/limpar.gif"
                                               reRender="v20_groupForm, v20_situacao, v20_mensagemConsultaResponsavel, v20_grpSituacoes"
                                               styleClass="botoes"
                                               action="#{ParcelaEmAbertoSPCControleRel.limparSituacoes}"/>

                        </h:panelGrid>
                    </h:panelGrid>

                    <h:panelGrid columns="1" style="width: 100%">
                        <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_ParcelaEmAberto_Clientenome}"/>
                        <h:panelGroup>
                            <h:inputText id="v20_nomeCliente" size="40" maxlength="40" styleClass="inputs"
                                         value="#{ParcelaEmAbertoSPCControleRel.parcelaEmAbertoRel.clienteVO.pessoa.nome}"/>
                            <a4j:commandButton id="v20_consultarCliente"
                                               oncomplete="Richfaces.showModalPanel('v20_panelCliente'), setFocus(formCliente,'v20_formCliente:v20_valorConsultarCliente')"
                                               alt="Consultar Operador" image="../imagens/informacao.gif"/>
                            <rich:spacer width="5px"/>
                            <a4j:commandButton id="v20_LimparCliente" image="../imagens/limpar.gif"
                                               reRender="v20_form:v20_nomeCliente"
                                               action="#{ParcelaEmAbertoSPCControleRel.limparCampoCliente}"/>
                        </h:panelGroup>
                    </h:panelGrid>

                    <h:panelGrid columns="1">
                        <h:outputText styleClass="tituloCampos"
                                      value="Situação SPC"/>
                        <h:panelGroup layout="block">
                            <h:selectOneMenu id="v20_situacao_spc" styleClass="inputs"
                                             value="#{ParcelaEmAbertoSPCControleRel.parcelaEmAbertoRel.situacaoSPC}"
                                             style="float: left; font-size: 12px !important; width: 100%;">
                                <f:selectItems
                                        value="#{ParcelaEmAbertoSPCControleRel.listaSelectItemTipoSituacaoSPC}"/>
                            </h:selectOneMenu>
                        </h:panelGroup>
                    </h:panelGrid>
                    <h:panelGrid columns="1" style="width: 100%">
                        <h:outputText styleClass="tituloCampos" value="Planos"/>
                        <h:panelGroup id="grpPlanoSPC">
                            <h:selectOneMenu id="v20_planoSPC" styleClass="inputs"
                                             value="#{ParcelaEmAbertoSPCControleRel.parcelaEmAbertoRel.codigoPlano}"
                                             style="float: left;">
                                <f:selectItems value="#{ParcelaEmAbertoSPCControleRel.listaSelectPlanos}"/>
                            </h:selectOneMenu>

                            <a4j:commandButton id="adicionarPlanoSelecionado"
                                               action="#{ParcelaEmAbertoSPCControleRel.adicionarPlanoNaLista}"
                                               style="float: left" title="Adicionar plano na lista de filtros"
                                               reRender="fgroup_filtros, v20_planoSPC, grpPlanoSPC,v20_qtd, modalGerenciarFiltroPlano"
                                               image="../imagens/botaoAdicionar.png" styleClass="botoes"/>

                            <h:panelGrid cellpadding="0" cellspacing="0" columns="2" id="v20_qtd"
                                         style="float: left; margin-top: 3px; margin-left: 5px">
                                <h:panelGroup layout="block">
                                    <h:panelGroup  style="cursor: pointer" layout="block" styleClass="notificacaoAtividades" id="btnGerenciarFiltrosPlano">
                                        <h:outputText
                                                value="#{ParcelaEmAbertoSPCControleRel.parcelaEmAbertoRel.qtdPlanosSelecionados}"/>
                                    </h:panelGroup>
                                    <rich:componentControl for="modalGerenciarFiltroPlano"
                                                           attachTo="btnGerenciarFiltrosPlano"
                                                           operation="show"
                                                           event="onclick"/>
                                </h:panelGroup>
                            </h:panelGrid>
                            <rich:toolTip
                                    value="#{ParcelaEmAbertoSPCControleRel.parcelaEmAbertoRel.planosSelecionadosApresentar}"
                                    for="v20_qtd"
                                    followMouse="true"/>

                            <a4j:commandButton id="v20_limparPlanosSelecionados" image="../imagens/limpar.gif"
                                               reRender="grpPlanoSPC, v20_planoSPC, v20_qtd, modalGerenciarFiltroPlano"
                                               title="Remover todos os planos do filtro"
                                               styleClass="botoes"
                                               style="vertical-align: top; margin-top: 6px; margin-left: 6px; float: left"
                                               action="#{ParcelaEmAbertoSPCControleRel.limparPlanos}"/>
                        </h:panelGroup>
                    </h:panelGrid>
                </h:panelGrid>

                <h:panelGrid id="v20_groupForm_2" columns="6" style="display: inline-flex; margin: 10px">
                    <h:panelGroup id="v20_groupForm_consultar" layout="block" style="display: flex; align-items: center;">
                        <a4j:commandLink id="consultar"
                                         ajaxSingle="false"
                                         action="#{ParcelaEmAbertoSPCControleRel.consultarParcelas}"
                                         styleClass="botaoPrimario texto-size-14"
                                         style="margin-right: 8px"
                                         reRender="v20_form, v20_GridListagemParcelas"
                                         oncomplete="#{ParcelaEmAbertoSPCControleRel.mensagemNotificar}"
                                         value="Consultar"/>

                        <a4j:commandLink id="enviarNegativar"
                                         ajaxSingle="false"
                                         action="#{ParcelaEmAbertoSPCControleRel.negativarParcelas}"
                                         styleClass="botaoPrimario texto-size-14"
                                         style="margin-right: 8px"
                                         reRender="v20_form,v20_GridListagemParcelas,panelAutorizacaoFuncionalidade"
                                         oncomplete="#{ParcelaEmAbertoSPCControleRel.mensagemNotificar}"
                                         value="Negativar"/>

                        <a4j:commandLink id="enviarLiberar"
                                         ajaxSingle="false"
                                         action="#{ParcelaEmAbertoSPCControleRel.liberarParcelas}"
                                         styleClass="botaoPrimario texto-size-14"
                                         style="margin-right: 8px"
                                         reRender="v20_form,v20_GridListagemParcelas,panelAutorizacaoFuncionalidade"
                                         oncomplete="#{ParcelaEmAbertoSPCControleRel.mensagemNotificar}"
                                         value="Retirar Negativação"/>
                    </h:panelGroup>
                </h:panelGrid>
            </h:panelGroup>

            <h:panelGrid columns="5" cellpadding="0" cellspacing="0">
                <h:panelGrid columns="2"
                             style="font-family: sans-serif; margin: 30px 10px 10px 18px; padding: 10px; width: 250px; box-shadow: 0px 2px 4px #E4E5E6;"
                             cellspacing="0" cellpadding="0">

                    <h:outputText styleClass="tituloCampos" value="TOTAL DE ALUNOS"
                                  style="color: black; float: left"/>
                    <h:outputText styleClass="tituloCampos" value="#{ParcelaEmAbertoSPCControleRel.totalAlunos}"
                                  style="float: right"/>

                    <h:panelGroup styleClass="separador-horizontal" layout="block"/>
                    <h:panelGroup styleClass="separador-horizontal" layout="block"/>

                    <h:outputText styleClass="tituloCampos" value="TOTAL DE PARCELAS"
                                  style="color: black; float: left"/>
                    <h:outputText styleClass="tituloCampos" value="#{ParcelaEmAbertoSPCControleRel.qtdTotalParcelas}"
                                  style="color: black; float: right"/>

                    <h:panelGroup styleClass="separador-horizontal" layout="block"/>
                    <h:panelGroup styleClass="separador-horizontal" layout="block"/>

                    <h:outputText styleClass="tituloCampos" value="TOTAL"
                                  style="color: black; float: left; font-weight: bold"/>
                    <h:outputText styleClass="tituloCampos"
                                  value="#{EmpresaControle.empresaLogado.moeda} #{ParcelaEmAbertoSPCControleRel.valorTotalParcelas_Apresentar}"
                                  style="color: black; float: right; font-weight: bold"/>

                </h:panelGrid>
                <h:panelGrid columns="2"
                             style="display: inline-flex;
    float: left; font-family: sans-serif; margin: 30px 10px 10px 18px; padding: 21px; width: 290px; border: 1px solid #DB2C3D; box-sizing: border-box;"
                             cellspacing="0" cellpadding="0">
                    <h:graphicImage url="/images/pct-repeat.jpg"
                                    style="padding-left: 40px; padding-right: 35px;"/>
                    <h:panelGrid columns="1" cellpadding="0" cellspacing="0">
                        <h:outputText value="Aberto" style="font-size: 16px; color: #BDC3C7; "/>
                        <h:outputText value="#{ParcelaEmAbertoSPCControleRel.qtdeParcelasEmAberto}"
                                      style="font-weight: bold; font-size: 32px; color: #51555A;"/>
                        <h:outputText
                                value="#{EmpresaControle.empresaLogado.moeda} #{ParcelaEmAbertoSPCControleRel.valorTotalEmAberto_Apresentar}"
                                style="background-color: #EB5757; font-weight: bold; font-size: 12px; color: #FFFFFF; padding: 1px 7px 1px 7px;"/>
                    </h:panelGrid>
                </h:panelGrid>
                <h:panelGrid columns="2"
                             style="display: inline-flex;
    float: left; font-family: sans-serif; margin: 30px 10px 10px 18px; padding: 21px; width: 290px; border: 1px solid #81D742; box-sizing: border-box;"
                             cellspacing="0" cellpadding="0">
                    <h:graphicImage url="/images/pct-check-circle.jpg"
                                    style="padding-left: 40px; padding-right: 35px;"/>
                    <h:panelGrid columns="1" cellpadding="0" cellspacing="0">
                        <h:outputText value="Pago" style="font-size: 16px; color: #BDC3C7; "/>
                        <h:outputText value="#{ParcelaEmAbertoSPCControleRel.qtdeParcelasPago}"
                                      style="font-weight: bold; font-size: 32px; color: #51555A;"/>
                        <h:outputText
                                value="#{EmpresaControle.empresaLogado.moeda} #{ParcelaEmAbertoSPCControleRel.valorTotalPago_Apresentar}"
                                style="background-color: #81D742; font-weight: bold; font-size: 12px; color: #FFFFFF; padding: 1px 7px 1px 7px;"/>
                    </h:panelGrid>
                </h:panelGrid>

                <h:panelGrid columns="2"
                             style="display: inline-flex;
    float: left; font-family: sans-serif; margin: 30px 10px 10px 18px; padding: 21px; width: 290px; border: 1px solid #80858C; box-sizing: border-box;"
                             cellspacing="0" cellpadding="0">
                    <h:graphicImage url="/images/pct-flag.jpg"
                                    style="padding-left: 40px; padding-right: 35px;"/>
                    <h:panelGrid columns="1" cellpadding="0" cellspacing="0">
                        <h:outputText value="Cancelado" style="font-size: 16px; color: #BDC3C7; "/>
                        <h:outputText value="#{ParcelaEmAbertoSPCControleRel.qtdeParcelasCancelado}"
                                      style="font-weight: bold; font-size: 32px; color: #51555A;"/>
                        <h:outputText
                                value="#{EmpresaControle.empresaLogado.moeda} #{ParcelaEmAbertoSPCControleRel.valorTotalCancelado_Apresentar}"
                                style="background-color: #80858C; font-weight: bold; font-size: 12px; color: #FFFFFF; padding: 1px 7px 1px 7px;"/>
                    </h:panelGrid>
                </h:panelGrid>

                <h:panelGrid columns="2" style="display: inline-flex; float: left; font-family: sans-serif; margin: 30px 10px 10px 18px; padding: 21px; width: 290px; border: 1px solid #F0B924; box-sizing: border-box;"
                             cellspacing="0" cellpadding="0">
                    <h:graphicImage url="/images/pct-alert-circle.svg" style="padding-left: 40px; padding-right: 35px;width: 63px"/>
                    <h:panelGrid columns="1" cellpadding="0" cellspacing="0">
                        <h:outputText value="Negativados" style="font-size: 16px; color: #BDC3C7; "/>
                        <h:outputText value="#{ParcelaEmAbertoSPCControleRel.qtdeParcelasNegativadas}"
                                      style="font-weight: bold; font-size: 32px; color: #F0B924;"/>
                        <h:outputText
                                value="#{EmpresaControle.empresaLogado.moeda} #{ParcelaEmAbertoSPCControleRel.valorTotalNegativadasApresentar}"
                                style="background-color: #F0B924; font-weight: bold; font-size: 12px; color: #FFFFFF; padding: 1px 7px 1px 7px;"/>
                    </h:panelGrid>
                </h:panelGrid>

            </h:panelGrid>
            <h:panelGroup id="v20_GridListagemParcelas">
                <jsp:include page="/relatorio/include_listagem_parcelas_spc.jsp"/>
            </h:panelGroup>
        </h:form>
    </h:panelGrid>

    <rich:modalPanel id="modalGerenciarFiltroPlano" styleClass="novaModal" autosized="true" shadowOpacity="true" width="800"
                     height="350">
        <f:facet name="header">
            <h:panelGroup>
                <h:outputText value="Filtro avançado para planos"></h:outputText>
            </h:panelGroup>
        </f:facet>

        <f:facet name="controls">
            <h:panelGroup>
                <h:outputText styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign btnSelecionarModalidade"
                              id="fecharModalGerenciamentoFiltroPlano"/>
                <rich:componentControl for="modalGerenciarFiltroPlano"
                                       attachTo="fecharModalGerenciamentoFiltroPlano"
                                       operation="hide"
                                       event="onclick"/>
            </h:panelGroup>
        </f:facet>

        <h:form id="formModalGerenciarFiltroPlano">
            <h:panelGroup style="margin-bottom: 15px">
                <h:outputText style="font-size: 16px"
                              value="Plano: "></h:outputText>
                <h:selectOneMenu id="selectPlanoModal" styleClass="inputs"
                                 value="#{ParcelaEmAbertoSPCControleRel.codigoPlanoSelecionadoModal}"
                                 style="margin-bottom: 10px; width: 395px" >
                    <f:selectItems value="#{ParcelaEmAbertoSPCControleRel.listaSelectPlanos}"/>

                    <f:selectItems value="#{ParcelaEmAbertoSPCControleRel.listaSelectPlanos}" />
                </h:selectOneMenu>

                <!-- BTN ADD 1 PLANO -->
                <a4j:commandButton id="adicionarPlanoSelecionadoBtnModal"
                                   action="#{ParcelaEmAbertoSPCControleRel.adicionarPlanoNaListaModal}"
                                   style="margin-left: 5px"
                                   oncomplete="Richfaces.showModalPanel('modalGerenciarFiltroPlano');"
                                   reRender="fgroup_filtros, v20_planoSPC, grpPlanoSPC, v20_qtd, tableListaPlanosSelecionados, modalGerenciarFiltroPlano"
                                   value="Adicionar" styleClass="botaoPrimario texto-size-14"/>

                <!-- BTN ADD TODOS -->
                <a4j:commandButton id="adicionarTodosPlanosBtn"
                                   action="#{ParcelaEmAbertoSPCControleRel.adicionarTodosPlanos}"
                                   style="margin: 0 5px"
                                   ajaxSingle="true"
                                   oncomplete="Richfaces.showModalPanel('modalGerenciarFiltroPlano');"
                                   reRender="fgroup_filtros, v20_planoSPC, grpPlanoSPC, v20_qtd, tableListaPlanosSelecionados, modalGerenciarFiltroPlano"
                                   value="Adicionar Todos" styleClass="botaoPrimario texto-size-14"/>

                <!--  BTN LIMPA LISTA -->
                <a4j:commandButton id="removerPlanoDaListaTabela"
                                   action="#{ParcelaEmAbertoSPCControleRel.limparPlanos}"
                                   ajaxSingle="true"
                                   oncomplete="Richfaces.showModalPanel('modalGerenciarFiltroPlano');"
                                   reRender="grpPlanoSPC, v20_planoSPC, v20_qtd, tableListaPlanosSelecionados, modalGerenciarFiltroPlano"
                                   value="Limpar lista" styleClass="botaoPrimario texto-size-14"/>
            </h:panelGroup>
            <h:outputText style="text-align: center" styleClass="texto-font texto-size-14-real texto-cor-cinza texto-bold"
                          rendered="#{empty ParcelaEmAbertoSPCControleRel.parcelaEmAbertoRel.planosSelecionados}"
                          value="Não há nenhum plano adicionado no filtro."></h:outputText>
            <h:outputText styleClass="tituloCampos"
                          rendered="#{not empty ParcelaEmAbertoSPCControleRel.parcelaEmAbertoRel.planosSelecionados}"
                          value="#{ParcelaEmAbertoSPCControleRel.parcelaEmAbertoRel.qtdPlanosSelecionados} #{ParcelaEmAbertoSPCControleRel.parcelaEmAbertoRel.qtdPlanosSelecionados > 1 ? 'planos adicionados ao filtro' : 'plano adicionado ao filtro'}"/>
            <div style="margin-bottom: 10px; margin-top: 4px; max-height: 300px; overflow-y: auto; padding: 10px;">
                <rich:dataTable rendered="#{not empty ParcelaEmAbertoSPCControleRel.parcelaEmAbertoRel.planosSelecionados}" id="tableListaPlanosSelecionados" headerClass="subordinado"
                                styleClass="tabelaSimplesCustom" rowClasses="linhaImpar, linhaPar"
                                columnClasses="colunaAlinhamento"
                                value="#{ParcelaEmAbertoSPCControleRel.parcelaEmAbertoRel.planosSelecionados}"
                                var="planoSelecionado">
                    <rich:column width="10%" >
                        <f:facet name="header">
                            <h:outputText value="Código" />
                        </f:facet>
                        <h:outputText value="#{planoSelecionado.value}" />
                    </rich:column>
                    <rich:column width="20%">
                        <f:facet name="header">
                            <h:outputText value="Descrição" />
                        </f:facet>
                        <h:outputText value="#{planoSelecionado.label}" />
                    </rich:column>
                    <rich:column width="10%">
                        <f:facet name="header">
                            <h:outputText value="#{msg_bt.btn_opcoes}" />
                        </f:facet>
                        <a4j:commandButton id="removerPlanoDaListaTabela" image="../imagens/limpar.gif"
                                           title="Remover plano do filtro"
                                           reRender="grpPlanoSPC, v20_planoSPC, v20_qtd, tableListaPlanosSelecionados, modalGerenciarFiltroPlano"
                                           styleClass="botoes"
                                           oncomplete="Richfaces.showModalPanel('modalGerenciarFiltroPlano');"
                                           style="vertical-align: top; margin-top: 6px; margin-left: 6px;"
                                           action="#{ParcelaEmAbertoSPCControleRel.removerPlanoDaListaTabela}">
                            <f:setPropertyActionListener target="#{ParcelaEmAbertoSPCControleRel.planoParaRemoverDaLista}"
                                                         value="#{planoSelecionado}" />
                        </a4j:commandButton>
                    </rich:column>
                </rich:dataTable>
            </div>
        </h:form>

    </rich:modalPanel>




    <jsp:include page="/includes/autorizacao/include_autorizacao_funcionalidade.jsp" flush="true"/>
</f:view>
<script>
    document.getElementById("v20_form:v20_dataInicioVencimento").focus();
</script>
