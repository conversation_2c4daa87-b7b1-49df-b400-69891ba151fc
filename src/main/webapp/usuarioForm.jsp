<%@page contentType="text/html;charset=UTF-8" %>
<head>
    <%@include file="/includes/include_import_minifiles.jsp" %>
    <script src="${root}/script/packJQueryPlugins.min.js" type="text/javascript" ></script>
</head>
<link href="./css/otimize.css" rel="stylesheet" type="text/css">
<link href="beta/css/font-awesome_2.0.min.css" type="text/css" rel="stylesheet"/>
<script type="text/javascript" language="javascript" src="hoverform.js"></script>
<script type="text/javascript" language="javascript" src="script/time_1.3.js"></script>
<%@taglib prefix="f" uri="http://java.sun.com/jsf/core" %>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html" %>
<link href="./beta/css/font-awesome_2.0.min.css" type="text/css" rel="stylesheet"/>
<link href="./beta/css/pure-forms.css" type="text/css" rel="stylesheet"/>
<%@taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax" %>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich" %>
<%@taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<f:loadBundle var="msg_bt" basename="propriedades.Botoes"/>
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao"/>
<f:loadBundle var="msg" basename="propriedades.Mensagens"/>
<%@include file="/includes/verificaModulo.jsp" %>
<c:set var="contexto" value="${pageContext.request.contextPath}" scope="request"/>
<style>
    .inputCarregando:before {
        content: "\f110";
        /*position: absolute;*/
        top: 6px;
        right: 12px;
        font-family: FontAwesome;
        font-size: 21px;
        color: #333;
        -webkit-animation-name: spin;
        -webkit-animation-duration: 1000ms;
        -webkit-animation-iteration-count: infinite;
        -webkit-animation-timing-function: linear;
        -moz-animation-name: spin;
        -moz-animation-duration: 1000ms;
        -moz-animation-iteration-count: infinite;
        -moz-animation-timing-function: linear;
        -ms-animation-name: spin;
        -ms-animation-duration: 1000ms;
        -ms-animation-iteration-count: infinite;
        -ms-animation-timing-function: linear;

        animation-name: spin;
        animation-duration: 1000ms;
        animation-iteration-count: infinite;
        animation-timing-function: linear;
    }

</style>

<script>

    function carregarTooltipsterUsuarioForm() {
        carregarTooltipsterUsuario(jQuery('.tooltipster'));
    }

    function carregarTooltipsterUsuario(el) {
        el.tooltipster({
            theme: 'tooltipster-light',
            position: 'bottom',
            animation: 'grow',
            contentAsHTML: true
        });
    }
</script>

<f:view>

    <title>
        <h:outputText value="#{msg_aplic.prt_Usuario_tituloForm}"/>
    </title>
    <c:set var="titulo" scope="session" value="${msg_aplic.prt_Usuario_tituloForm}"/>
    <a4j:loadScript src="/script/jquery.maskedinput-1.2.2.js"/>
    <rich:modalPanel id="panelCliente" autosized="true" shadowOpacity="true" width="550" height="250">
        <f:facet name="header">
            <jsp:include page="topoReduzido_material_semUCP.jsp"/>
        </f:facet>
        <f:facet name="header">
            <h:panelGroup>
                <h:outputText value="Consulta Cliente"></h:outputText>
            </h:panelGroup>
        </f:facet>
        <f:facet name="controls">
            <h:panelGroup>
                <h:graphicImage value="/imagens/close.png" style="cursor:pointer" id="hidelink"/>
                <rich:componentControl for="panelCliente" attachTo="hidelink" operation="hide" event="onclick"/>
            </h:panelGroup>
        </f:facet>

        <a4j:form id="formCliente" ajaxSubmit="true">
            <hr style="border-color: #e6e6e6;"/>
            <h:panelGrid columns="1" rowClasses="linhaImpar, linhaPar" columnClasses="classEsquerda, classDireita"
                         width="100%">
                <h:panelGrid columns="4" footerClass="colunaCentralizada" width="100%">
                    <h:outputText style="font-size:12px;" value="#{msg.msg_consultar_por}"/>
                    <h:selectOneMenu id="consultaPessoa" value="#{UsuarioControle.campoConsultaUsuario}">
                        <f:selectItems value="#{UsuarioControle.tipoConsultaComboUsuario}"/>
                    </h:selectOneMenu>
                    <h:inputText id="valorConsultaPessoa" size="10" value="#{UsuarioControle.valorConsultaUsuario}"/>
                    <a4j:commandButton id="btnConsultar"
                                       reRender="formCliente:mensagemConsultaPessoa, formCliente:resultadoConsultaPessoa, formCliente:scResultadoPessoa"
                                       action="#{UsuarioControle.consultarCliente}" styleClass="botoes"
                                       value="#{msg_bt.btn_consultar}" image="./imagens/botaoConsultar.png"
                                       alt="#{msg.msg_consultar_dados}"/>
                </h:panelGrid>

                <rich:dataTable id="resultadoConsultaPessoa" width="100%" headerClass="consulta"
                                rowClasses="linhaImpar, linhaPar" columnClasses="colunaAlinhamento"
                                value="#{UsuarioControle.listaConsultaUsuario}" rows="5" var="cliente">
                    <rich:column>
                        <f:facet name="header">
                            <h:outputText value="#{msg_aplic.prt_Usuario_nomePessoa}"/>
                        </f:facet>
                        <h:panelGroup>
                            <a4j:commandLink action="#{UsuarioControle.selecionarCliente}" focus="cliente"
                                             reRender="form" oncomplete="Richfaces.hideModalPanel('panelCliente')"
                                             value="#{cliente.pessoa.nome}"/>
                        </h:panelGroup>
                    </rich:column>

                    <rich:column >
                        <f:facet name="header">
                            <h:outputText value="#{msg_aplic.prt_Usuario_cpf}"/>
                        </f:facet>
                        <h:panelGroup>
                            <a4j:commandLink action="#{UsuarioControle.selecionarCliente}" focus="cliente"
                                             reRender="form" oncomplete="Richfaces.hideModalPanel('panelCliente')"
                                             value="#{cliente.pessoa.cfp}"/>
                        </h:panelGroup>
                    </rich:column>


                    <rich:column>
                        <f:facet name="header">
                            <h:outputText value="#{msg_aplic.prt_Usuario_dataNasc}"/>
                        </f:facet>
                        <h:panelGroup>
                            <a4j:commandLink action="#{UsuarioControle.selecionarCliente}" focus="cliente"
                                             reRender="form" oncomplete="Richfaces.hideModalPanel('panelCliente')"
                                             value="#{cliente.pessoa.dataNasc_Apresentar}"/>
                        </h:panelGroup>
                    </rich:column>
                    <rich:column>
                        <f:facet name="header">
                            <h:outputText value="#{msg_bt.btn_opcoes}"/>
                        </f:facet>
                        <a4j:commandButton id="selecionarCliente" action="#{UsuarioControle.selecionarCliente}"
                                           focus="cliente" reRender="form"
                                           oncomplete="Richfaces.hideModalPanel('panelCliente')"
                                           value="#{msg_bt.btn_selecionar}" image="./imagens/botaoEditar.png"
                                           alt="#{msg.msg_selecionar_dados}" styleClass="botoes"/>
                    </rich:column>
                </rich:dataTable>
                <rich:datascroller align="center" for="formCliente:resultadoConsultaPessoa" maxPages="10"
                                   id="scResultadoPessoa"/>
                <h:panelGrid id="mensagemConsultaPessoa" columns="1" width="100%" styleClass="tabMensagens">
                    <h:panelGrid columns="3" width="100%" styleClass="tabMensagens">
                        <h:panelGrid columns="1" width="100%">

                            <h:outputText value=" "/>

                        </h:panelGrid>
                        <h:commandButton rendered="#{UsuarioControle.sucesso}" image="./imagens/sucesso.png"/>
                        <h:commandButton rendered="#{UsuarioControle.erro}" image="./imagens/erro.png"/>
                        <h:panelGrid columns="1" width="100%">
                            <h:outputText styleClass="mensagem" value="#{UsuarioControle.mensagem}"/>
                            <h:outputText styleClass="mensagemDetalhada" value="#{UsuarioControle.mensagemDetalhada}"/>
                        </h:panelGrid>
                    </h:panelGrid>
                </h:panelGrid>
            </h:panelGrid>
        </a4j:form>
    </rich:modalPanel>

    <rich:modalPanel id="panelColaborador" styleClass="novaModal" autosized="true" shadowOpacity="true" width="580" height="250">
        <f:facet name="header">
            <h:panelGroup>
                <h:outputText value="Consulta Colaborador"></h:outputText>
            </h:panelGroup>
        </f:facet>
        <f:facet name="controls">
            <h:panelGroup>
                <h:outputText
                    styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign btnSelecionarModalidade"
                    id="hidelink1"/>
                <rich:componentControl for="panelColaborador" attachTo="hidelink1" operation="hide" event="onclick"/>
            </h:panelGroup>
        </f:facet>

        <a4j:form id="formColaborador" ajaxSubmit="true">
            <h:panelGrid columns="1"  columnClasses="classEsquerda, classDireita"
                         width="100%">
                <h:panelGrid columns="4"  width="100%" styleClass="font-size-Em-max">
                    <h:outputText styleClass="texto-size-16-real texto-cor-cinza texto-font" value="#{msg.msg_consultar_por}"/>
                    <h:panelGroup layout="block" styleClass="cb-container">
                        <h:selectOneMenu id="consultacolaborador" value="#{UsuarioControle.campoConsultaUsuario}">
                            <f:selectItems value="#{UsuarioControle.tipoConsultaComboUsuario}"/>
                        </h:selectOneMenu>
                    </h:panelGroup>
                    <h:inputText id="valorConsultaColaborador" size="10"
                                 value="#{UsuarioControle.valorConsultaUsuario}"/>
                    <a4j:commandLink id="btnConsultarColaborador"
                                     reRender="formColaborador"
                                     action="#{UsuarioControle.consultarColaborador}" styleClass="botaoPrimario texto-size-16"
                                     value="#{msg_bt.btn_consultar}"
                                     title="#{msg.msg_consultar_dados}"/>
                </h:panelGrid>

                <rich:dataTable id="resultadoConsultaColaborador" width="100%"
                                styleClass="tabelaSimplesCustom" rendered="#{not empty UsuarioControle.listaConsultaUsuario}"
                                value="#{UsuarioControle.listaConsultaUsuario}" rows="5" var="colaborador">
                    <rich:column>
                        <f:facet name="header">
                            <h:outputText  styleClass="texto-font texto-size-14-real texto-cor-cinza texto-bold" value="#{msg_aplic.prt_Usuario_colaborador}"/>
                        </f:facet>
                        <h:panelGroup>
                            <a4j:commandLink action="#{UsuarioControle.selecionarColaborador}" focus="colaborador"
                                             styleClass="linkPadrao texto-cor-azul texto-size-14-real"
                                             reRender="form" oncomplete="Richfaces.hideModalPanel('panelColaborador')"
                                             value="#{colaborador.pessoa.nome}"/>
                        </h:panelGroup>
                    </rich:column>
                    <rich:column>
                        <f:facet name="header">
                            <h:outputText styleClass="texto-font texto-size-14-real texto-cor-cinza texto-bold" value="#{msg_aplic.prt_Usuario_cpf}"/>
                        </f:facet>
                        <h:panelGroup>
                            <a4j:commandLink action="#{UsuarioControle.selecionarColaborador}" focus="colaborador"
                                             styleClass="linkPadrao texto-cor-azul texto-size-14-real"
                                             reRender="form" oncomplete="Richfaces.hideModalPanel('panelColaborador')"
                                             value="#{colaborador.pessoa.cfp}"/>
                        </h:panelGroup>
                    </rich:column>
                    <rich:column>
                        <f:facet name="header">
                            <h:outputText styleClass="texto-font texto-size-14-real texto-cor-cinza texto-bold" value="#{msg_aplic.prt_Usuario_dataNasc}"/>
                        </f:facet>
                        <h:panelGroup>
                            <a4j:commandLink action="#{UsuarioControle.selecionarColaborador}" focus="colaborador"
                                             styleClass="linkPadrao texto-cor-azul texto-size-14-real"
                                             reRender="form" oncomplete="Richfaces.hideModalPanel('panelColaborador')"
                                             value="#{colaborador.pessoa.dataNasc_Apresentar}"/>
                        </h:panelGroup>
                    </rich:column>
                    <rich:column width="15%">
                        <a4j:commandLink id="selecionarColaborador" action="#{UsuarioControle.selecionarColaborador}"
                                         focus="colaborador" reRender="form"
                                         style="display: inline-flex;"
                                         oncomplete="Richfaces.hideModalPanel('panelColaborador')"
                                         title="#{msg.msg_selecionar_dados}" styleClass="linkPadrao texto-cor-azul">
                            <span class="texto-font texto-size-16-real"> Selecionar </span> <i class="fa-icon-arrow-right texto-size-16-real"></i>
                        </a4j:commandLink>
                    </rich:column>
                </rich:dataTable>
                <rich:datascroller align="center" styleClass="scrollPureCustom" renderIfSinglePage="false"
                                   for="formColaborador:resultadoConsultaColaborador" maxPages="10" id="scResultadoColaborador"/>
                <h:panelGrid id="mensagemConsultaColaborador" columns="1" width="100%" styleClass="tabMensagens">
                    <h:panelGrid columns="3" width="100%" styleClass="tabMensagens">
                        <h:panelGrid columns="1" width="100%">

                            <h:outputText value=" "/>

                        </h:panelGrid>
                        <h:commandButton rendered="#{UsuarioControle.sucesso}" image="./imagens/sucesso.png"/>
                        <h:commandButton rendered="#{UsuarioControle.erro}" image="./imagens/erro.png"/>
                        <h:panelGrid columns="1" width="100%">
                            <h:outputText styleClass="mensagem" value="#{UsuarioControle.mensagem}"/>
                            <h:outputText styleClass="mensagemDetalhada" value="#{UsuarioControle.mensagemDetalhada}"/>
                        </h:panelGrid>
                    </h:panelGrid>
                </h:panelGrid>
            </h:panelGrid>
        </a4j:form>
    </rich:modalPanel>

    <rich:modalPanel id="panelMsgSincronizadorUsuarioMovel" autosized="true" shadowOpacity="true" width="300"
                     height="100">
        <f:facet name="header">
            <h:panelGroup>
                <h:outputText value="Mensagem Aviso"></h:outputText>
            </h:panelGroup>
        </f:facet>

        <f:facet name="controls">
            <h:panelGroup>
                <h:graphicImage value="/imagens/close.png" style="cursor:pointer"
                                id="hidelinkpanelMsgSincronizadorUsuarioMovel"/>
                <rich:componentControl for="panelMsgSincronizadorUsuarioMovel"
                                       attachTo="hidelinkpanelMsgSincronizadorUsuarioMovel" operation="hide"
                                       event="onclick"/>
            </h:panelGroup>
        </f:facet>

        <h:form id="formMsgSincronizadorUsuarioMovel">
            <h:panelGrid id="panelMsgSincronizadorUsuarioMovel" columns="1" width="100%"
                         rowClasses="linhaImpar, linhaPar" columnClasses="colunaCentralizada">
                <h:outputText value="#{UsuarioControle.mensagemSincronizarUsuarioMovel}"/>
                <a4j:commandButton oncomplete="Richfaces.hideModalPanel('panelMsgSincronizadorUsuarioMovel')"
                                   image="./imagens/botaoFechar.png"/>
            </h:panelGrid>
        </h:form>
    </rich:modalPanel>

    <h:panelGrid columns="1" width="100%" cellpadding="0" cellspacing="0">
        <f:facet name="header">
            <jsp:include page="topoReduzido_material.jsp"/>

        </f:facet>

        <h:form id="form" styleClass="pure-form">

            <input type="hidden" value="${modulo}" name="modulo"/>

            <h:panelGrid columns="1" width="100%">

                <h:panelGrid id="mensagemDetalhadaSuperior" rendered="#{( UsuarioControle.sucesso || UsuarioControle.erro)  }" columns="1" width="100%" styleClass="tabMensagens">
                    <h:panelGrid columns="3" width="100%" styleClass="tabMensagens">
                        <h:panelGrid columns="1" width="100%">
                            <h:outputText value=" "/>
                        </h:panelGrid>
                        <h:commandButton rendered="#{UsuarioControle.sucesso}" image="./imagens/sucesso.png"/>
                        <h:commandButton rendered="#{UsuarioControle.erro}" image="./imagens/erro.png"/>
                        <h:panelGrid columns="1" width="100%">
                            <h:outputText styleClass="mensagem" value="#{UsuarioControle.mensagem}"/>
                            <h:outputText styleClass="mensagemDetalhada" value="#{UsuarioControle.mensagemDetalhada}"/>
                        </h:panelGrid>
                    </h:panelGrid>
                </h:panelGrid>


                <rich:tabPanel width="100%" activeTabClass="true" headerAlignment="rigth" switchType="ajax">
                    <rich:tab id="abaDadosUsuario" label="Dados Usuário" switchType="client">
                        <h:panelGrid columns="1" width="100%" headerClass="subordinado"
                                     rowClasses="linhaImpar, linhaPar" columnClasses="colunaCentralizada">
                            <rich:simpleTogglePanel opened="true" switchType="client" width="100%">
                                <f:facet name="header">
                                    <h:outputText value="Dados Usuário"/>
                                </f:facet>

                                <h:panelGrid columns="2" rowClasses="linhaImpar, linhaPar" headerClass="subordinado"
                                             columnClasses="classEsquerda, classDireita" width="100%">

                                    <h:outputText value="#{msg_aplic.prt_Usuario_codigo}"/>
                                    <h:panelGroup layout="block">
                                        <h:outputText id="codigo" value="#{UsuarioControle.usuarioVO.codigo}"
                                                      styleClass="form"/>

                                        <h:outputText id="idInfoUsuarioGeral"
                                                      style="float: right; padding-left: 10px;"
                                                      rendered="#{UsuarioControle.exibirCodigoUsuarioGeral}"
                                                      value="UsuarioGeral: #{UsuarioControle.usuarioVO.usuarioGeral}"
                                                      title="Código apenas para controle interno Pacto, só aparece para usuários da Pacto..."
                                                      styleClass="form tooltipster"/>

                                        <h:outputText id="idInfoNovoLogin"
                                                      style="float: right; color: #EEEEEE;"
                                                      rendered="#{SuperControle.integracaoNovoLogin}"
                                                      value="Novo Login"
                                                      styleClass="form"/>
                                    </h:panelGroup>

                                    <h:outputText rendered="#{UsuarioControle.mostraTipoUsuario}"
                                                  value="#{msg_aplic.prt_Usuario_tipoUsuario}"/>
                                    <h:panelGroup rendered="#{UsuarioControle.mostraTipoUsuario}">
                                        <h:selectOneMenu id="tipoUsuario" onblur="blurinput(this);"
                                                         onfocus="focusinput(this);"
                                                         styleClass="form"
                                                         value="#{UsuarioControle.usuarioVO.tipoUsuario}">
                                            <a4j:support event="onchange"
                                                         action="#{UsuarioControle.verificarTipoPessoa}"
                                                         reRender="form" focus="tipoUsuario"/>
                                            <f:selectItems value="#{UsuarioControle.listaSelectItemTipoUsuario}"/>
                                        </h:selectOneMenu>
                                    </h:panelGroup>

                                    <h:outputText rendered="#{UsuarioControle.cliente}"
                                                  value="#{msg_aplic.prt_Usuario_nomePessoa}"/>
                                    <h:panelGroup rendered="#{UsuarioControle.cliente}">
                                        <h:inputText id="cliente" size="40" maxlength="50" onblur="blurinput(this);"
                                                     readonly="true" onfocus="focusinput(this);" styleClass="form"
                                                     value="#{UsuarioControle.usuarioVO.clienteVO.pessoa.nome}"/>
                                        <a4j:commandButton id="consultaDadosCliente" focus="nomeCliente"
                                                           alt="Consultar Cliente"
                                                           reRender="formCliente"
                                                           oncomplete="Richfaces.showModalPanel('panelCliente')"
                                                           image="./imagens/informacao.gif"/>
                                    </h:panelGroup>

                                    <h:outputText rendered="#{UsuarioControle.colaborador}"
                                                  value="#{msg_aplic.prt_Usuario_colaborador}"/>
                                    <h:panelGroup rendered="#{UsuarioControle.colaborador}">
                                        <h:inputText id="colaborador" size="40" maxlength="50"
                                                     onblur="blurinput(this);" readonly="true"
                                                     onfocus="focusinput(this);"
                                                     styleClass="form"
                                                     value="#{UsuarioControle.usuarioVO.colaboradorVO.pessoa.nome}"/>
                                        <a4j:commandButton id="consultaDadosColaborador"
                                                           focus="nomeCliente" alt="Consultar Colaborador"
                                                           reRender="formColaborador"
                                                           oncomplete="Richfaces.showModalPanel('panelColaborador')"
                                                           image="./imagens/informacao.gif"/>
                                    </h:panelGroup>

                                    <h:outputText rendered="#{UsuarioControle.novoColaborador}"
                                                  value="* #{msg_aplic.prt_Usuario_colaborador}"/>
                                    <h:panelGroup rendered="#{UsuarioControle.novoColaborador}">
                                        <h:inputText id="nomecolaborador" size="40" maxlength="50"
                                                     onblur="blurinput(this);"
                                                     onfocus="focusinput(this);" styleClass="form"
                                                     value="#{UsuarioControle.usuarioVO.colaboradorVO.pessoa.nome}"/>
                                    </h:panelGroup>

                                    <h:outputText rendered="#{!UsuarioControle.usuarioVO.administrador && !UsuarioControle.configuracaoSistema.utilizarFormatoMMDDYYYDtNascimento}"
                                                  value="#{msg_aplic.prt_Colaborador_dataNasc}"/>
                                    <h:panelGroup rendered="#{!UsuarioControle.usuarioVO.administrador && !UsuarioControle.configuracaoSistema.utilizarFormatoMMDDYYYDtNascimento}">
                                        <rich:calendar id="dataNasc"
                                                       value="#{UsuarioControle.usuarioVO.colaboradorVO.pessoa.dataNasc}"
                                                       inputSize="10"
                                                       inputClass="form"
                                                       oninputblur="blurinput(this);"
                                                       oninputfocus="focusinput(this);"
                                                       oninputchange="return validar_Data(this.id);"
                                                       datePattern="dd/MM/yyyy"
                                                       enableManualInput="true"
                                                       zindex="2"
                                                       showWeeksBar="false">
                                        </rich:calendar>
                                        <h:message for="dataNasc" styleClass="mensagemDetalhada"/>
                                        <rich:jQuery id="mskData" selector=".rich-calendar-input" timing="onload" query="mask('99/99/9999')" />
                                    </h:panelGroup>

                                    <h:outputText rendered="#{!UsuarioControle.usuarioVO.administrador && UsuarioControle.configuracaoSistema.utilizarFormatoMMDDYYYDtNascimento}"
                                                  value="#{msg_aplic.prt_Colaborador_dataNasc}"/>
                                    <h:panelGroup rendered="#{!UsuarioControle.usuarioVO.administrador && UsuarioControle.configuracaoSistema.utilizarFormatoMMDDYYYDtNascimento}">
                                        <rich:calendar id="dataNascMMDDYYYY"
                                                       value="#{UsuarioControle.usuarioVO.colaboradorVO.pessoa.dataNasc}"
                                                       inputSize="10"
                                                       inputClass="form"
                                                       oninputblur="blurinput(this);"
                                                       oninputfocus="focusinput(this);"
                                                       datePattern="MM/dd/yyyy"
                                                       enableManualInput="true"
                                                       zindex="2"
                                                       showWeeksBar="false">
                                        </rich:calendar>
                                        <h:message for="dataNascMMDDYYYY" styleClass="mensagemDetalhada"/>
                                        <rich:jQuery id="mskDataMMDDYYYY" selector=".rich-calendar-input" timing="onload" query="mask('99/99/9999')" />
                                    </h:panelGroup>

                                        <h:outputText
                                                rendered="#{!UsuarioControle.configuracaoSistema.usarSistemaInternacional}"
                                                value="* #{msg_aplic.prt_Pessoa_cfp}:"/>
                                        <h:panelGroup
                                                rendered="#{!UsuarioControle.configuracaoSistema.usarSistemaInternacional}">
                                            <rich:jQuery id="mskCPF" selector="#cfp" timing="onload"
                                                         query="mask('999.999.999-99')"/>
                                            <h:inputText id="cfp" size="14" maxlength="14" onblur="blurinput(this);"
                                                         onfocus="focusinput(this);" styleClass="form"
                                                         value="#{UsuarioControle.usuarioVO.colaboradorVO.pessoa.cfp}"/>
                                        </h:panelGroup>

                                        <h:outputText
                                            rendered="#{UsuarioControle.configuracaoSistema.usarSistemaInternacional && UsuarioControle.configuracaoSistema.cfpOb}"
                                            value="* #{UsuarioControle.displayIdentificadorFront[0]}:"/>
                                    <h:panelGroup
                                            rendered="#{UsuarioControle.configuracaoSistema.usarSistemaInternacional && UsuarioControle.configuracaoSistema.cfpOb}">
                                        <h:inputText id="identificadorInt" size="14" maxlength="14" onblur="blurinput(this);"
                                                     onfocus="focusinput(this);" styleClass="form"
                                                     value="#{UsuarioControle.usuarioVO.colaboradorVO.pessoa.cfp}"/>
                                    </h:panelGroup>

                                    <h:outputText
                                        rendered="#{UsuarioControle.novoColaborador && UsuarioControle.usuarioVO.usuarioVO.administrador }"
                                        value="#{msg_aplic.prt_Colaborador_empresa}"/>
                                    <h:panelGroup
                                        rendered="#{UsuarioControle.novoColaborador && UsuarioControle.usuarioVO.usuarioVO.administrador}">
                                        <h:selectOneMenu id="empresaColaborador" styleClass="form"
                                                         onblur="blurinput(this);"
                                                         onfocus="focusinput(this);"
                                                         value="#{UsuarioControle.usuarioVO.colaboradorVO.empresa.codigo}">
                                            <f:selectItems value="#{UsuarioControle.listaSelectItemEmpresa}"/>
                                        </h:selectOneMenu>
                                        <a4j:commandButton id="atualizar_empresaColaborador"
                                                           action="#{UsuarioControle.montarListaSelectItemEmpresa}"
                                                           image="imagens/atualizar.png" ajaxSingle="true"
                                                           reRender="form:empresaColaborador"/>
                                    </h:panelGroup>

                                    <%-- EMAIL --%>
                                    <h:outputText value="#{msg_aplic.prt_Email_email_usuario}"
                                                  style="vertical-align: top"/>
                                    <h:panelGrid columns="1" cellspacing="0" cellpadding="0">
                                        <h:panelGroup layout="block" style="padding: 5px 0 0;"
                                                      rendered="#{UsuarioControle.usuarioVO.novoObj}">
                                            <h:outputText value="Ao concluir o cadastro será enviado para esse e-mail o link para definir a senha de acesso ao sistema."
                                                          styleClass="classInfCadUsuario"/>
                                        </h:panelGroup>

                                        <h:panelGroup layout="block" style="padding: 5px 0 5px; display: flex; align-items: center;">
                                            <h:inputText id="emailColaborador"
                                                         size="40" maxlength="50"
                                                         onblur="blurinput(this);" onfocus="focusinput(this);"
                                                         styleClass="form"
                                                         disabled="#{!UsuarioControle.usuarioVO.novoObj}"
                                                         value="#{UsuarioControle.usuarioEmailVO.email}"/>

                                            <h:panelGroup layout="block" id="panelEmailVerificado"
                                                          style="display: flex; align-items: center;"
                                                          rendered="#{!UsuarioControle.usuarioVO.novoObj}">
                                                <h:outputText id="emailVerificado"
                                                              rendered="#{UsuarioControle.usuarioEmailVO.verificado && not empty UsuarioControle.usuarioEmailVO.email}"
                                                              style="padding-left: 2px"
                                                              styleClass="outUsuarioDisponivel userdisponivel fa-icon-ok"
                                                              value="Verificado"/>
                                                <h:outputText id="emailNaoVerificado"
                                                              rendered="#{!UsuarioControle.usuarioEmailVO.verificado && not empty UsuarioControle.usuarioEmailVO.email}"
                                                              style="padding-left: 2px"
                                                              styleClass="outUsuarioDisponivel userindisponivel fa-icon-remove"
                                                              value="Não verificado"/>

                                                <a4j:commandLink id="btnVerificarEmail"
                                                                   style="margin-left: 10px;"
                                                                   rendered="#{!UsuarioControle.usuarioEmailVO.verificado && not empty UsuarioControle.usuarioEmailVO.email}"
                                                                   action="#{UsuarioControle.enviarCodigoVerificarEmail}"
                                                                   value="Verificar"
                                                                   reRender="formValidarToken"
                                                                   oncomplete="#{UsuarioControle.onComplete}#{UsuarioControle.mensagemNotificar}"
                                                                   styleClass="btSec pure-button pure-button-small"/>

                                                <a4j:commandLink id="btnAbrirNovoEmail"
                                                                   style="margin-left: 10px;"
                                                                   action="#{UsuarioControle.abrirNovoEmail}"
                                                                   value="Alterar"
                                                                   reRender="formEmailLogin"
                                                                   oncomplete="#{UsuarioControle.onComplete}#{UsuarioControle.mensagemNotificar}"
                                                                   styleClass="btSec pure-button pure-button-small"/>
                                            </h:panelGroup>
                                        </h:panelGroup>
                                    </h:panelGrid>

                                    <%-- TELEFONE --%>
                                    <h:outputText value="#{msg_aplic.prt_Usuario_telefone}"
                                                  rendered="#{!UsuarioControle.usuarioVO.novoObj && !UsuarioControle.configuracaoSistema.usarSistemaInternacional}"/>
                                    <h:panelGrid columns="1" cellspacing="0" cellpadding="0"
                                                 rendered="#{!UsuarioControle.usuarioVO.novoObj && !UsuarioControle.configuracaoSistema.usarSistemaInternacional}">
                                        <h:panelGroup layout="block"
                                                      style="display: flex; align-items: center;">
                                            <h:inputText id="telefoneUsuario"
                                                         size="13"
                                                         maxlength="13"
                                                         disabled="true"
                                                         onblur="blurinput(this);"
                                                         onkeypress="return mascara(this.form, this.id , '(99)999999999', event);"
                                                         onfocus="focusinput(this);"
                                                         styleClass="form"
                                                         value="#{UsuarioControle.usuarioTelefoneVO.numero}"/>

                                            <h:panelGroup layout="block" id="panelTelefoneVerificado"
                                                          style="display: flex; align-items: center;">
                                                <h:outputText id="telefoneVerificado"
                                                              rendered="#{UsuarioControle.usuarioTelefoneVO.verificado && not empty UsuarioControle.usuarioTelefoneVO.numero}"
                                                              style="padding-left: 2px"
                                                              styleClass="outUsuarioDisponivel userdisponivel fa-icon-ok"
                                                              value="Verificado"/>
                                                <h:outputText id="telefoneNaoVerificado"
                                                              rendered="#{!UsuarioControle.usuarioTelefoneVO.verificado && not empty UsuarioControle.usuarioTelefoneVO.numero}"
                                                              style="padding-left: 2px"
                                                              styleClass="outUsuarioDisponivel userindisponivel fa-icon-remove"
                                                              value="Não verificado"/>

                                                <a4j:commandLink id="btnAbrirNovoTelefone"
                                                                   style="margin-left: 10px;"
                                                                   action="#{UsuarioControle.abrirNovoTelefone}"
                                                                   value="Alterar"
                                                                   reRender="formTelefoneLogin"
                                                                   oncomplete="#{UsuarioControle.onComplete}#{UsuarioControle.mensagemNotificar}"
                                                                   styleClass="btSec pure-button pure-button-small"/>
                                            </h:panelGroup>
                                        </h:panelGroup>
                                    </h:panelGrid>

                                    <%--SENHA--%>
                                    <h:outputText value="#{msg_aplic.prt_Usuario_senha}"/>
                                    <h:panelGrid columns="1"
                                                 cellpadding="0"
                                                 cellspacing="0">

                                        <h:panelGroup layout="block">
                                            <h:outputText rendered="#{UsuarioControle.usuarioVO.novoObj}"
                                                          value="Ao concluir será enviado um e-mail com o link para definir a senha de acesso ao sistema"
                                                          styleClass="classInfCadUsuario"/>

                                            <a4j:commandLink id="solicitarNovaSenhaNewLogin"
                                                             rendered="#{!UsuarioControle.usuarioVO.novoObj}"
                                                             action="#{UsuarioControle.solicitarNovaSenha}"
                                                             oncomplete="#{UsuarioControle.mensagemNotificar}"
                                                             style="font-size: 12px;font-weight: 400;"
                                                             title="Será enviado um e-mail com o link para criar uma nova senha"
                                                             value="Alterar senha"
                                                             styleClass="btSec pure-button pure-button-small tooltipster">
                                            </a4j:commandLink>
                                        </h:panelGroup>
                                    </h:panelGrid>

                                    <%-- REMOVENDO OPÇÃO DO USUÁRIO PODER ALTERAR SENHA USANDO ADMIN PACTO. SENHA SERÁ ALTERADA SOMENTE VIA LINK ENCAMINHADO PARA O EMAIL DO USUÁRIO --%>
                                    <%--
                                    <h:panelGrid rendered="#{LoginControle.usuarioLogado.usuarioAdminPACTO}"
                                                 columns="1"
                                                 cellpadding="0"
                                                 cellspacing="0">


                                        <h:panelGroup layout="block" style="padding: 5px 0 0;"
                                                      rendered="#{UsuarioControle.usuarioVO.novoObj}">
                                            <h:outputText
                                                    value="Caso não seja informada nenhuma senha o sistema irá gerar uma senha aleatória que será enviada junto ao e-mail ao concluir o cadastro."
                                                    styleClass="classInfCadUsuario"/>
                                        </h:panelGroup>

                                        <h:panelGroup layout="block" style="padding: 5px 0 5px;">
                                            <h:inputSecret redisplay="true" id="senha" size="24" maxlength="24"
                                                           onblur="blurinput(this);"
                                                           onfocus="focusinput(this);" styleClass="form"
                                                           value="#{UsuarioControle.usuarioVO.senha}">
                                            </h:inputSecret>
                                        </h:panelGroup>

                                    </h:panelGrid>

                                    <h:outputText rendered="#{LoginControle.usuarioLogado.usuarioAdminPACTO}"
                                                  value="#{msg_aplic.prt_Usuario_confirmar_senha}"/>
                                    <h:panelGrid rendered="#{LoginControle.usuarioLogado.usuarioAdminPACTO}"
                                                 columns="1"
                                                 cellpadding="0"
                                                 cellspacing="0">
                                        <h:panelGroup>

                                            <h:inputSecret redisplay="true" id="senhaConfirmar" size="24" maxlength="24"
                                                           onblur="blurinput(this);"
                                                           onchange="validarSenha()"
                                                           onfocus="focusinput(this);" styleClass="form"
                                                           value="#{UsuarioControle.usuarioVO.senhaConfirmar}">
                                            </h:inputSecret>

                                            <h:outputText id="senhaErrada"
                                                          style="display: none; color: red; margin-left: 5px;"
                                                          value="As senhas não conferem."/>
                                        </h:panelGroup>
                                    </h:panelGrid>
                                    --%>
                                    <%--USERNAME--%>
                                    <h:outputText value="#{msg_aplic.prt_Usuario_username}"/>
                                    <h:panelGrid columns="1"
                                                 cellpadding="0"
                                                 cellspacing="0">
                                        <h:panelGroup layout="block" style="display: flex; align-items: center">
                                            <h:inputText id="username"
                                                         size="50" maxlength="250"
                                                         onblur="blurinput(this);"
                                                         onfocus="focusinput(this);" styleClass="form"
                                                         value="#{UsuarioControle.usuarioVO.username}"
                                                         autocomplete="off">

                                                <a4j:queue name="queueFiltro" ignoreDupResponses="true"
                                                           requestDelay="400"
                                                           onsubmit="iniciaCarregandoUsername()"
                                                           timeout="60000"/>

                                                <a4j:support status="statusInComponent" eventsQueue="queueFiltro"
                                                             event="onkeyup"
                                                             oncomplete="finalizaCarregandoUsername()"
                                                             action="#{UsuarioControle.validarUsernameExistente}"
                                                             reRender="panelUsuarioDispo"/>
                                            </h:inputText>

                                            <h:panelGroup layout="block" id="panelUsuarioDispo">
                                                <h:outputText id="outUsuarioDisponivelIcon"
                                                              style="padding-left: 2px"
                                                              styleClass="outUsuarioDisponivel #{UsuarioControle.usernameDisponivelClass} #{UsuarioControle.usernameDisponivelIcon}"/>
                                                <h:outputText id="outUsuarioDisponivel"
                                                              style="padding-left: 1px"
                                                              styleClass="outUsuarioDisponivel #{UsuarioControle.usernameDisponivelClass}"
                                                              value=" #{UsuarioControle.usernameDisponivelMsg}"/>
                                            </h:panelGroup>
                                        </h:panelGroup>
                                    </h:panelGrid>

                                    <%-- PIN --%>
                                    <h:outputText rendered="#{UsuarioControle.apresentarCampoSenhaUsuario}"
                                                  value="#{msg_aplic.prt_Usuario_pin}"/>
                                    <h:panelGrid rendered="#{UsuarioControle.apresentarCampoSenhaUsuario}"
                                                 columns="1"
                                                 cellpadding="0"
                                                 cellspacing="0">
                                        <h:panelGroup rendered="#{UsuarioControle.apresentarCampoSenhaUsuario}">
                                            <h:inputSecret size="10" maxlength="4" styleClass="form" redisplay="true"
                                                           value="#{UsuarioControle.usuarioVO.pin}"/>
                                        </h:panelGroup>
                                    </h:panelGrid>


                                    <h:outputText value="#{msg_aplic.prt_Usuario_situacao}"
                                                  rendered="#{!empty UsuarioControle.usuarioVO.tipoUsuario || !empty UsuarioControle.usuarioVO.colaboradorVO.situacao}"/>
                                    <h:panelGroup
                                            rendered="#{!empty UsuarioControle.usuarioVO.tipoUsuario || !empty UsuarioControle.usuarioVO.colaboradorVO.situacao}">
                                        <h:selectOneMenu id="situacao" onblur="blurinput(this);"
                                                         onfocus="focusinput(this);" styleClass="form"
                                                         value="#{UsuarioControle.usuarioVO.colaboradorVO.situacao}">
                                            <a4j:support event="onchange"
                                                         action="#{UsuarioControle.verificarSituacaoColaborador}"
                                                         reRender="form,mdlVinculoAgenda" focus="situacao" oncomplete="#{UsuarioControle.onComplete}"/>
                                            <f:selectItems
                                                    value="#{ColaboradorControle.listaSelectItemSituacaoColaborador}"/>
                                        </h:selectOneMenu>
                                        <h:message for="situacao" styleClass="mensagemDetalhada" id="msgSituacao"/>
                                    </h:panelGroup>

                                    <%-- INICIO TELEFONE--%>
                                    <h:outputText rendered="#{UsuarioControle.usuarioVO.tipoUsuario != '' and false}"
                                                  value="Telefone:"/>

                                    <h:panelGroup rendered="#{UsuarioControle.usuarioVO.tipoUsuario != '' and false}">

                                        <h:selectOneMenu id="tipoTelefone" onblur="blurinput(this);"
                                                         onfocus="focusinput(this);" styleClass="form"
                                                         value="#{UsuarioControle.telefoneVO.tipoTelefone}">
                                            <f:selectItems value="#{UsuarioControle.selectItemTipoTelefone}"/>
                                        </h:selectOneMenu>

                                        <h:outputText value="" style="padding-left: 5px"/>

                                        <c:if test="${!UsuarioControle.configuracaoSistema.usarSistemaInternacional}">
                                            <h:inputText id="numeroTelefone"
                                                         size="13"
                                                         maxlength="13"
                                                         onchange="return validar_Telefone(this.id);"
                                                         onblur="blurinput(this);"
                                                         onkeypress="return mascara(this.form, this.id , '(99)999999999', event);"
                                                         onfocus="focusinput(this);"
                                                         styleClass="form"
                                                         value="#{UsuarioControle.telefoneVO.numero}"/>
                                        </c:if>

                                        <c:if test="${UsuarioControle.configuracaoSistema.usarSistemaInternacional}">
                                            <h:inputText id="ddiTelefoneUser"
                                                         size="4" title="DDI"
                                                         maxlength="4"
                                                         onblur="blurinput(this);"
                                                         onkeypress="return mascara(this.form, this.id , '+999', event);"
                                                         onfocus="focusinput(this);"
                                                         styleClass="form"
                                                         value="#{UsuarioControle.telefoneVO.ddi}"
                                                         style="margin: 0 3px"/>

                                            <h:inputText id="numeroTelefoneIntern"
                                                         size="13"
                                                         maxlength="11"
                                                         onblur="blurinput(this);"
                                                         onfocus="focusinput(this);"
                                                         styleClass="form"
                                                         value="#{UsuarioControle.telefoneVO.numero}"/>
                                        </c:if>
                                        <a4j:commandButton id="addTelefone"
                                                           action="#{UsuarioControle.adicionarTelefoneUsuario}"
                                                           reRender="form"
                                                           value="#{msg_bt.btn_adicionar}"
                                                           image="./imagens/botaoAdicionar.png"
                                                           style="position:relative; top:7px; "
                                                           styleClass="botoes"/>
                                    </h:panelGroup>


                                    <h:outputText value=""
                                                  rendered="#{false and !empty UsuarioControle.usuarioVO.colaboradorVO.pessoa.telefoneVOs}"/>
                                    <h:panelGrid id="panelTelefone" columns="1" width="100%"
                                                 rendered="#{false and !empty UsuarioControle.usuarioVO.colaboradorVO.pessoa.telefoneVOs}"
                                                 styleClass="tabFormSubordinada">
                                        <h:dataTable id="dataTableTelefone"
                                                     width="100%" headerClass="subordinado"
                                                     styleClass="tabFormSubordinada"
                                                     rowClasses="linhaImpar, linhaPar"
                                                     columnClasses="colunaAlinhamento, colunaAlinhamento, colunaAlinhamento"
                                                     value="#{UsuarioControle.usuarioVO.colaboradorVO.pessoa.telefoneVOs}"
                                                     var="tel">

                                            <h:column>
                                                <f:facet name="header">
                                                    <h:outputText value="Número"/>
                                                </f:facet>
                                                <h:outputText value="#{tel.ddi} #{tel.numero}"/>
                                            </h:column>

                                            <h:column>
                                                <f:facet name="header">
                                                    <h:outputText value="Tipo do Telefone"/>
                                                </f:facet>
                                                <h:outputText value="#{tel.tipoTelefone_Apresentar}"/>
                                            </h:column>
                                            <h:column>
                                                <f:facet name="header">
                                                    <h:outputText value="#{msg_bt.btn_opcoes}"/>
                                                </f:facet>

                                                <h:panelGroup id="grupoBtnRemoverTelefone">
                                                    <a4j:commandButton id="removerTelefone"  reRender="mdlMensagemGenerica" image="./imagens/botaoRemover.png"
                                                                       oncomplete="#{UsuarioControle.msgAlert}" action="#{UsuarioControle.confirmarExcluir}"
                                                                       value="#{msg_bt.btn_excluir}" title="#{msg.msg_excluir_dados}" accesskey="7" styleClass="botoes">
                                                        <f:param name="metodochamar" value="removerTelefoneUsuario"/>
                                                    </a4j:commandButton>
                                                </h:panelGroup>
                                            </h:column>
                                        </h:dataTable>
                                    </h:panelGrid>
                                    <%-- FIM TELEFONE--%>

                                    <%-- INICIO TIPO COLABORADOR --%>
                                    <h:outputText rendered="#{UsuarioControle.usuarioVO.tipoUsuario != ''}"
                                                  value="#{msg_aplic.prt_Colaborador_tipoColaborador_usuario}"/>
                                    <h:panelGroup rendered="#{UsuarioControle.usuarioVO.tipoUsuario != ''}">
                                        <h:selectOneMenu id="tipoColaborador" onblur="blurinput(this);"
                                                         onfocus="focusinput(this);" styleClass="form"
                                                         value="#{UsuarioControle.tipoColaboradorVO.descricao}">
                                            <f:selectItems value="#{UsuarioControle.listaSelectItemTipoColaborador}"/>
                                        </h:selectOneMenu>
                                        <a4j:commandButton id="addTipoColaborador"
                                                           action="#{UsuarioControle.adicionarTipoColaborador}"
                                                           reRender="form"
                                                           value="#{msg_bt.btn_adicionar}"
                                                           image="./imagens/botaoAdicionar.png"
                                                           style="position:relative; top:7px; "
                                                           styleClass="botoes"/>
                                    </h:panelGroup>

                                    <h:outputText value=""
                                                  rendered="#{!empty UsuarioControle.usuarioVO.colaboradorVO.listaTipoColaboradorVOs}"/>
                                    <h:panelGrid id="panelTipoColaborador" columns="1" width="100%"
                                                 rendered="#{!empty UsuarioControle.usuarioVO.colaboradorVO.listaTipoColaboradorVOs}"
                                                 styleClass="tabFormSubordinada">
                                        <h:dataTable id="dataTabletipoColaboradorVO"
                                                     width="100%" headerClass="subordinado"
                                                     styleClass="tabFormSubordinada"
                                                     rowClasses="linhaImpar, linhaPar"
                                                     columnClasses="colunaAlinhamento, colunaAlinhamento"
                                                     value="#{UsuarioControle.usuarioVO.colaboradorVO.listaTipoColaboradorVOs}"
                                                     var="tipoColaboradorVO">
                                            <h:column>
                                                <f:facet name="header">
                                                    <h:outputText value="#{msg_aplic.prt_Colaborador_tipoColaborador}"/>
                                                </f:facet>
                                                <h:outputText id="desTipoColaborador"
                                                              value="#{tipoColaboradorVO.descricao_Apresentar}"/>
                                            </h:column>
                                            <h:column>
                                                <f:facet name="header">
                                                    <h:outputText value="#{msg_bt.btn_opcoes}"/>
                                                </f:facet>

                                                <h:panelGroup id="grupoBtnRemoverTipoColaborador">
                                                    <a4j:commandButton id="removerTipoColaborador"  reRender="mdlMensagemGenerica" image="./imagens/botaoRemover.png"
                                                                       oncomplete="#{UsuarioControle.msgAlert}" action="#{UsuarioControle.confirmarExcluir}"
                                                                       value="#{msg_bt.btn_excluir}" title="#{msg.msg_excluir_dados}" accesskey="3" styleClass="botoes">
                                                        <f:param name="metodochamar" value="removerTipoColaborador"/>
                                                    </a4j:commandButton>
                                                </h:panelGroup>

                                            </h:column>
                                        </h:dataTable>
                                    </h:panelGrid>
                                    <%-- FIM TIPO COLABORADOR --%>

                                </h:panelGrid>
                            </rich:simpleTogglePanel>
                        </h:panelGrid>

                        <%--PERFIL DE ACESSO - USUÁRIO--%>
                        <h:panelGrid rendered="#{UsuarioControle.mostraLista}" columns="1" width="100%"
                                     headerClass="subordinado" rowClasses="linhaImpar, linhaPar"
                                     columnClasses="colunaCentralizada">
                            <rich:simpleTogglePanel opened="true" switchType="client" width="100%">
                                <f:facet name="header">
                                    <h:outputText value="#{msg_aplic.prt_UsuarioPerfilAcesso_tituloForm}"/>
                                </f:facet>
                                <h:panelGrid id="painelGridDados" columns="2" width="100%"
                                             rowClasses="linhaImpar, linhaPar"
                                             columnClasses="classEsquerda, classDireita"
                                             footerClass="colunaCentralizada">
                                    <h:outputText value="#{msg_aplic.prt_UsuarioPerfilAcesso_empresa}"/>
                                    <h:panelGroup>
                                        <h:selectOneMenu id="empresa" onblur="blurinput(this);"
                                                         onfocus="focusinput(this);" styleClass="form"
                                                         value="#{UsuarioControle.usuarioPerfilAcessoVO.empresa.codigo}">
                                            <f:selectItems value="#{UsuarioControle.listaSelectItemEmpresa}"/>
                                        </h:selectOneMenu>


                                        <a4j:commandLink style="display: inline-block; margin-right: 10px; margin-left: 10px"
                                                         id="atualizar_empresa"
                                                         action="#{UsuarioControle.montarListaSelectItemEmpresa}"
                                                         immediate="true"
                                                         ajaxSingle="true" reRender="form:empresa">
                                            <i class="fa-icon-refresh"></i>
                                            Atualizar
                                        </a4j:commandLink>

                                        <a4j:commandLink style="display: inline-block" action="#{UsuarioControle.adicionarTodasEmpresasPerfilAcesso}"
                                                         id="AdicionarTodas"
                                                         reRender="form:usuarioPerfilAcessoVO,form:painelGridDados,form:mensagens, form">
                                            <i class="fa-icon-plus"></i>
                                            Adicionar todas
                                        </a4j:commandLink>

                                    </h:panelGroup>
                                    <h:outputText value="#{msg_aplic.prt_UsuarioPerfilAcesso_codPerfilAcesso}"/>
                                    <h:panelGroup>
                                        <h:selectOneMenu id="codPerfilAcesso" onblur="blurinput(this);"
                                                         onfocus="focusinput(this);" styleClass="form"
                                                         value="#{UsuarioControle.usuarioPerfilAcessoVO.perfilAcesso.codigo}">
                                            <f:selectItems value="#{UsuarioControle.listaSelectItemCodPerfilAcesso}"/>
                                        </h:selectOneMenu>
                                        <a4j:commandButton id="atualizar_codPerfilAcesso"
                                                           action="#{UsuarioControle.montarListaSelectItemCodPerfilAcesso}"
                                                           image="imagens/atualizar.png" immediate="true"
                                                           ajaxSingle="true" reRender="form:codPerfilAcesso"/>

                                    </h:panelGroup>
                                </h:panelGrid>
                                <h:panelGrid columns="1" width="100%" headerClass="subordinado"
                                             rowClasses="linhaImpar, linhaPar" columnClasses="colunaCentralizada">



                                    <a4j:commandButton id="addPerfilAcesso"
                                                       action="#{UsuarioControle.adicionarUsuarioPerfilAcesso}"
                                                       reRender="form:usuarioPerfilAcessoVO,form:painelGridDados,form:mensagens, form"
                                                       focus="empresa" value="#{msg_bt.btn_adicionar}" accesskey="5"
                                                       styleClass="botoes" image="./imagens/botaoAdicionar.png"/>
                                </h:panelGrid>

                                <h:panelGrid id="gridUsuarioPerfilAcessoVO" columns="1" width="100%"
                                             styleClass="tabFormSubordinada"
                                             rendered="#{UsuarioControle.usuarioVO.usuarioPerfilAcessoVOsSize > 0}">
                                    <h:dataTable id="usuarioPerfilAcessoVO" width="100%" headerClass="subordinado"
                                                 rowClasses="linhaImpar, linhaPar"
                                                 columnClasses="colunaCentralizada, colunaAlinhamento, colunaAlinhamento"
                                                 value="#{UsuarioControle.usuarioVO.usuarioPerfilAcessoVOs}"
                                                 var="usuarioPerfilAcesso">
                                        <h:column>
                                            <f:facet name="header">
                                                <h:outputText value="#{msg_aplic.prt_UsuarioPerfilAcesso_empresa}"/>
                                            </f:facet>
                                            <h:outputText value="#{usuarioPerfilAcesso.empresa.nome}"/>
                                        </h:column>

                                        <h:column>
                                            <f:facet name="header">
                                                <h:outputText
                                                    value="#{msg_aplic.prt_UsuarioPerfilAcesso_codPerfilAcesso}"/>
                                            </f:facet>
                                            <a4j:commandLink id="abrirPerfilAcesso"
                                                             rendered="#{LoginControle.permissaoAcessoMenuVO.perfilAcesso}"
                                                             action="#{UsuarioControle.abrirPerfilAcesso}"
                                                             oncomplete="#{UsuarioControle.onComplete}"
                                                             accesskey="7"
                                                             styleClass="pure-button"
                                                             title="Abrir cadastro do perfil de acesso">
                                                <f:param name="chavePrimaria"
                                                         value="#{usuarioPerfilAcesso.perfilAcesso.codigo}"/>
                                                <h:outputText value="#{usuarioPerfilAcesso.perfilAcesso.nome}"/>
                                            </a4j:commandLink>
                                            <h:outputText
                                                rendered="#{!LoginControle.permissaoAcessoMenuVO.perfilAcesso}"
                                                value="#{usuarioPerfilAcesso.perfilAcesso.nome}"/>
                                        </h:column>

                                        <h:column>
                                            <f:facet name="header">
                                                <a4j:commandLink id="removerUsuarioPerfilAcessotodas" style="margin: 5px; display: block"
                                                                   action="#{UsuarioControle.removerTodosPerfisAcesso}"
                                                                   reRender="usuarioPerfilAcessoVO, modalVinculoCliente, mensagemVinculoConsultor, form"
                                                                   oncomplete="#{UsuarioControle.msgAlert}">
                                                    <i class="fa-icon-remove"></i>
                                                   Remover todas
                                                </a4j:commandLink>
                                            </f:facet>
                                            <h:panelGroup>

                                                <a4j:commandButton id="removerUsuarioPerfilAcesso"
                                                                   action="#{UsuarioControle.removerUsuarioPerfilAcesso}"
                                                                   reRender="usuarioPerfilAcessoVO, modalVinculoCliente, mensagemVinculoConsultor, form"
                                                                   oncomplete="#{UsuarioControle.msgAlert}"
                                                                   value="#{msg_bt.btn_excluir}" accesskey="7"
                                                                   styleClass="botoes"
                                                                   image="./imagens/botaoRemover.png"/>
                                            </h:panelGroup>
                                        </h:column>
                                    </h:dataTable>
                                </h:panelGrid>
                            </rich:simpleTogglePanel>
                        </h:panelGrid>

                        <%--MODULO TREINO - PERFIL ACESSO--%>
                        <h:panelGrid rendered="#{UsuarioControle.mostraLista and UsuarioControle.moduloTreinoHabilitado}" columns="1" width="100%"
                                     headerClass="subordinado" rowClasses="linhaImpar, linhaPar"
                                     columnClasses="colunaCentralizada">
                            <rich:simpleTogglePanel opened="true" switchType="client" width="100%">
                                <f:facet name="header">
                                    <h:outputText value="Usuário Perfil Acesso Treino"/>
                                </f:facet>
                                <h:panelGrid id="painelGridDadosTreino" columns="2" width="100%"
                                             rowClasses="linhaImpar, linhaPar"
                                             columnClasses="classEsquerda, classDireita"
                                             footerClass="colunaCentralizada">
                                    <h:outputText value="* Nome do Perfil de Acesso Treino"/>
                                    <h:panelGroup>
                                        <h:selectOneMenu id="codPerfilAcessoTreino" onblur="blurinput(this);"
                                                         onfocus="focusinput(this);" styleClass="form"
                                                         value="#{UsuarioControle.codPerfilTw}">
                                            <f:selectItems value="#{UsuarioControle.listaSelectItemCodPerfilAcessoTreino}"/>
                                        </h:selectOneMenu>
                                        <a4j:commandButton id="atualizar_codPerfilAcessoTreino"
                                                           action="#{UsuarioControle.montarListaSelectItemCodPerfilAcessoTreino}"
                                                           image="imagens/atualizar.png" immediate="true"
                                                           ajaxSingle="true" reRender="form:codPerfilAcessoTreino"/>

                                    </h:panelGroup>
                                </h:panelGrid>
                            </rich:simpleTogglePanel>
                        </h:panelGrid>

                        <%--MODULO DE NOTAS - USUÁRIO--%>
                        <h:panelGrid rendered="#{UsuarioControle.mostrarGrupoModuloNotas}" columns="1" width="100%"
                                     headerClass="subordinado" rowClasses="linhaImpar, linhaPar"
                                     columnClasses="colunaCentralizada" >
                            <rich:simpleTogglePanel opened="true" switchType="client" width="100%">
                                <f:facet name="header">
                                    <h:outputText value="#{msg_aplic.prt_ModuloNotas_tituloForm}"/>
                                </f:facet>

                                <h:panelGrid columns="2" rowClasses="linhaImpar, linhaPar" headerClass="subordinado"
                                             columnClasses="classEsquerda, classDireita" width="100%">

                                    <h:outputText value="#{msg_aplic.prt_ModuloNotas_permissao_alterar_rps}"/>
                                    <h:panelGroup>
                                        <h:selectBooleanCheckbox id="permissaoAlterarRPS"
                                                                 value="#{UsuarioControle.usuarioVO.permissaoAlterarRPS}" />
                                    </h:panelGroup>

                                </h:panelGrid>

                            </rich:simpleTogglePanel>
                        </h:panelGrid>

                        <!-- Início - Horários de Acessos ao Sistema-->
                        <h:panelGrid rendered="#{!UsuarioControle.usuarioVO.administrador}" id="horariosAcessosSistema"
                                     columns="1" width="100%" headerClass="subordinado"
                                     rowClasses="linhaPar, linhaImpar" columnClasses="colunaCentralizada">
                            <rich:simpleTogglePanel opened="false" switchType="client" width="100%">
                                <f:facet name="header">
                                    <h:panelGroup>
                                        <h:outputText value="Horários de Acessos ao Sistema"/>
                                    </h:panelGroup>
                                </f:facet>


                                <h:panelGrid id="camposHorarioAcesso" columns="2" rowClasses="linhaImpar, linhaPar"
                                             columnClasses="classEsquerda, classDireita" width="100%"
                                             footerClass="colunaCentralizada">

                                    <h:outputText value="#{msg_aplic.prt_HorarioTurma_codigo}"/>
                                    <h:panelGroup>
                                        <h:inputText id="codigoHorario" required="true" size="10" maxlength="10"
                                                     readonly="true"
                                                     styleClass="camposSomenteLeitura"
                                                     value="#{UsuarioControle.horarioAcessoSistemaVO.codigo}"/>
                                        <h:message for="codigo" styleClass="mensagemDetalhada"/>
                                    </h:panelGroup>
                                    <h:outputText value="#{msg_aplic.prt_HorarioTurma_diaSemana}"/>

                                    <h:panelGroup>
                                        <h:selectOneMenu id="diaSemana"
                                                         onblur="blurinput(this);"
                                                         onfocus="focusinput(this);" styleClass="form"
                                                         value="#{UsuarioControle.codigoDiaSemana}">
                                            <f:selectItems
                                                value="#{UsuarioControle.listaDiaSemana}"/>
                                        </h:selectOneMenu>
                                    </h:panelGroup>

                                    <h:outputText value="#{msg_aplic.prt_HorarioTurma_horaInicial}"/>
                                    <h:panelGroup>
                                        <h:inputText id="horaInicial"
                                                     onkeypress="return mascaraTodos(this.form, 'form:horaInicial', '99:99', event);"
                                                     size="5" maxlength="5" onblur="blurinput(this);"
                                                     onfocus="focusinput(this);"
                                                     styleClass="form"
                                                     value="#{UsuarioControle.horarioAcessoSistemaVO.horaInicial}"/>
                                        <h:message for="horaInicial" styleClass="mensagemDetalhada"/>
                                    </h:panelGroup>
                                    <h:outputText value="#{msg_aplic.prt_HorarioTurma_horaFinal}"/>
                                    <h:panelGroup>
                                        <h:inputText id="horaFinal"
                                                     onkeypress="return mascaraTodos(this.form, 'form:horaFinal', '99:99', event);"
                                                     size="5" maxlength="5" onblur="blurinput(this);"
                                                     onfocus="focusinput(this);"
                                                     styleClass="form"
                                                     value="#{UsuarioControle.horarioAcessoSistemaVO.horaFinal}"/>
                                    </h:panelGroup>
                                </h:panelGrid>
                                <h:panelGrid id="panelHorarios" columns="2" width="100%"
                                             columnClasses="colunaCentralizada" rowClasses="">
                                    <h:panelGroup>
                                        <a4j:commandButton
                                            id="addHorarioAcesso"
                                            action="#{UsuarioControle.adicionarHorarioAcesso}"
                                            reRender="listaHorarioAcesso,camposHorarioAcesso,mensagens"
                                            focus="diaSemana" value="#{msg_bt.btn_adicionar}"
                                            accesskey="5" styleClass="botoes"
                                            image="./imagens/botaoAdicionar.png"/>
                                        <a4j:commandButton
                                            id="acesso24Horas"
                                            title="Permitir acesso 24 horas em todos os dias.
                                            <br><span style='color:red'>Atenção: Sobrescreverá dados não salvos!</span>"
                                            action="#{UsuarioControle.preencherHorariosAcessosSistema24Horas}"
                                            reRender="listaHorarioAcesso,camposHorarioAcesso,mensagens"
                                            focus="diaSemana" value="#{msg_bt.btn_adicionar}"
                                            accesskey="5" alt="Permitir acesso 24h." styleClass="botoes tooltipster"
                                            style="margin-left: 10px" image="./imagens/24h.png"/>
                                    </h:panelGroup>
                                </h:panelGrid>
                                <h:panelGrid columns="1" width="100%" styleClass="tabFormSubordinada">
                                    <h:dataTable id="listaHorarioAcesso" width="100%" headerClass="subordinado"
                                                 rowClasses="linhaImpar, linhaPar" columnClasses="colunaCentralizada"
                                                 value="#{UsuarioControle.listaDiasSemanaEscolhidos}" var="diasSemana">
                                        <h:column>
                                            <f:facet name="header">
                                                <h:outputText value="Dia da Semana"/>
                                            </f:facet>
                                            <h:outputText styleClass="tituloCampos" value="#{diasSemana}"/>
                                        </h:column>
                                        <h:column>
                                            <f:facet name="header">
                                                <h:outputText value="Horários / Opções"/>
                                            </f:facet>
                                            <h:dataTable id="tableHorarioDom" rendered="#{diasSemana == 'Domingo'}"
                                                         value="#{UsuarioControle.listaDomingo}" var="horariosDomingo"
                                                         columnClasses="colunaCentralizada" width="100%">
                                                <h:column>
                                                    <h:outputText styleClass="tituloCampos"
                                                                  value="#{horariosDomingo.horaInicial} - #{horariosDomingo.horaFinal}"
                                                                  style="padding-right:20px;"/>
                                                    <a4j:commandButton id="editarHorarioAcesso"
                                                                       action="#{UsuarioControle.editarHorarioAcesso}"
                                                                       value="#{msg_bt.btn_editar}" accesskey="6"
                                                                       styleClass="botoes"
                                                                       image="./imagens/botaoEditar.png"
                                                                       reRender="camposHorarioAcesso"/>
                                                    <h:commandButton id="removerHorarioAcesso"
                                                                     action="#{UsuarioControle.excluirHorarioAcesso}"
                                                                     value="#{msg_bt.btn_excluir}"
                                                                     image="./imagens/botaoRemover.png" accesskey="7"
                                                                     styleClass="botoes"/>
                                                </h:column>
                                            </h:dataTable>
                                            <h:dataTable id="tableHorarioSeg"
                                                         rendered="#{diasSemana == 'Segunda-feira'}"
                                                         value="#{UsuarioControle.listaSegunda}" var="horariosSegunda"
                                                         columnClasses="colunaCentralizada" width="100%">
                                                <h:column>
                                                    <h:outputText styleClass="tituloCampos"
                                                                  value="#{horariosSegunda.horaInicial} - #{horariosSegunda.horaFinal}"
                                                                  style="padding-right:20px;"/>
                                                    <a4j:commandButton id="editarHorarioAcesso"
                                                                       action="#{UsuarioControle.editarHorarioAcesso}"
                                                                       value="#{msg_bt.btn_editar}" accesskey="6"
                                                                       styleClass="botoes"
                                                                       image="./imagens/botaoEditar.png"
                                                                       reRender="camposHorarioAcesso"/>
                                                    <h:commandButton id="removerHorarioAcesso"
                                                                     action="#{UsuarioControle.excluirHorarioAcesso}"
                                                                     value="#{msg_bt.btn_excluir}"
                                                                     image="./imagens/botaoRemover.png" accesskey="7"
                                                                     styleClass="botoes"/>
                                                </h:column>
                                            </h:dataTable>
                                            <h:dataTable id="tableHorarioTer" rendered="#{diasSemana == 'Terça-feira'}"
                                                         value="#{UsuarioControle.listaTerca}" var="horariosTerca"
                                                         columnClasses="colunaCentralizada" width="100%">
                                                <h:column>
                                                    <h:outputText styleClass="tituloCampos"
                                                                  value="#{horariosTerca.horaInicial} - #{horariosTerca.horaFinal}"
                                                                  style="padding-right:20px;"/>
                                                    <a4j:commandButton id="editarHorarioAcesso"
                                                                       action="#{UsuarioControle.editarHorarioAcesso}"
                                                                       value="#{msg_bt.btn_editar}" accesskey="6"
                                                                       styleClass="botoes"
                                                                       image="./imagens/botaoEditar.png"
                                                                       reRender="camposHorarioAcesso"/>
                                                    <h:commandButton id="removerHorarioAcesso"
                                                                     action="#{UsuarioControle.excluirHorarioAcesso}"
                                                                     value="#{msg_bt.btn_excluir}"
                                                                     image="./imagens/botaoRemover.png" accesskey="7"
                                                                     styleClass="botoes"/>
                                                </h:column>
                                            </h:dataTable>
                                            <h:dataTable id="tableHorarioQua" rendered="#{diasSemana == 'Quarta-feira'}"
                                                         value="#{UsuarioControle.listaQuarta}" var="horariosQuarta"
                                                         columnClasses="colunaCentralizada" width="100%">
                                                <h:column>
                                                    <h:outputText styleClass="tituloCampos"
                                                                  value="#{horariosQuarta.horaInicial} - #{horariosQuarta.horaFinal}"
                                                                  style="padding-right:20px;"/>
                                                    <a4j:commandButton id="editarHorarioAcesso"
                                                                       action="#{UsuarioControle.editarHorarioAcesso}"
                                                                       value="#{msg_bt.btn_editar}" accesskey="6"
                                                                       styleClass="botoes"
                                                                       image="./imagens/botaoEditar.png"
                                                                       reRender="camposHorarioAcesso"/>
                                                    <h:commandButton id="removerHorarioAcesso"
                                                                     action="#{UsuarioControle.excluirHorarioAcesso}"
                                                                     value="#{msg_bt.btn_excluir}"
                                                                     image="./imagens/botaoRemover.png" accesskey="7"
                                                                     styleClass="botoes"/>
                                                </h:column>
                                            </h:dataTable>
                                            <h:dataTable id="tableHorarioQui" rendered="#{diasSemana == 'Quinta-feira'}"
                                                         value="#{UsuarioControle.listaQuinta}" var="horariosQuinta"
                                                         columnClasses="colunaCentralizada" width="100%">
                                                <h:column>
                                                    <h:outputText styleClass="tituloCampos"
                                                                  value="#{horariosQuinta.horaInicial} - #{horariosQuinta.horaFinal}"
                                                                  style="padding-right:20px;"/>
                                                    <a4j:commandButton id="editarHorarioAcesso"
                                                                       action="#{UsuarioControle.editarHorarioAcesso}"
                                                                       value="#{msg_bt.btn_editar}" accesskey="6"
                                                                       styleClass="botoes"
                                                                       image="./imagens/botaoEditar.png"
                                                                       reRender="camposHorarioAcesso"/>
                                                    <h:commandButton id="removerHorarioAcesso"
                                                                     action="#{UsuarioControle.excluirHorarioAcesso}"
                                                                     value="#{msg_bt.btn_excluir}"
                                                                     image="./imagens/botaoRemover.png" accesskey="7"
                                                                     styleClass="botoes"/>
                                                </h:column>
                                            </h:dataTable>
                                            <h:dataTable id="tableHorarioSex" rendered="#{diasSemana == 'Sexta-feira'}"
                                                         value="#{UsuarioControle.listaSexta}" var="horariosSexta"
                                                         columnClasses="colunaCentralizada" width="100%">
                                                <h:column>
                                                    <h:outputText styleClass="tituloCampos"
                                                                  value="#{horariosSexta.horaInicial} - #{horariosSexta.horaFinal}"
                                                                  style="padding-right:20px;"/>
                                                    <a4j:commandButton id="editarHorarioAcesso"
                                                                       action="#{UsuarioControle.editarHorarioAcesso}"
                                                                       value="#{msg_bt.btn_editar}" accesskey="6"
                                                                       styleClass="botoes"
                                                                       image="./imagens/botaoEditar.png"
                                                                       reRender="camposHorarioAcesso"/>
                                                    <h:commandButton id="removerHorarioAcesso"
                                                                     action="#{UsuarioControle.excluirHorarioAcesso}"
                                                                     value="#{msg_bt.btn_excluir}"
                                                                     image="./imagens/botaoRemover.png" accesskey="7"
                                                                     styleClass="botoes"/>
                                                </h:column>
                                            </h:dataTable>
                                            <h:dataTable id="tableHorarioSab" rendered="#{diasSemana == 'Sábado'}"
                                                         value="#{UsuarioControle.listaSabado}" var="horariosSabado"
                                                         columnClasses="colunaCentralizada" width="100%">
                                                <h:column>
                                                    <h:outputText styleClass="tituloCampos"
                                                                  value="#{horariosSabado.horaInicial} - #{horariosSabado.horaFinal}"
                                                                  style="padding-right:20px;"/>
                                                    <a4j:commandButton id="editarHorarioAcesso"
                                                                       action="#{UsuarioControle.editarHorarioAcesso}"
                                                                       value="#{msg_bt.btn_editar}" accesskey="6"
                                                                       styleClass="botoes"
                                                                       image="./imagens/botaoEditar.png"
                                                                       reRender="camposHorarioAcesso"/>
                                                    <h:commandButton id="removerHorarioAcesso"
                                                                     action="#{UsuarioControle.excluirHorarioAcesso}"
                                                                     value="#{msg_bt.btn_excluir}"
                                                                     image="./imagens/botaoRemover.png" accesskey="7"
                                                                     styleClass="botoes"/>
                                                </h:column>
                                            </h:dataTable>
                                        </h:column>
                                    </h:dataTable>
                                </h:panelGrid>
                            </rich:simpleTogglePanel>
                        </h:panelGrid>
                        <!-- Fim - Horários Acesso ao Sistema-->


                        <%--AÇÕES--%>
                        <h:panelGrid rendered="#{UsuarioControle.mostraLista && !UsuarioControle.usuarioVO.novoObj}"
                                     columns="1" width="100%"
                                     headerClass="subordinado" rowClasses="linhaImpar, linhaPar"
                                     columnClasses="colunaCentralizada">
                            <rich:simpleTogglePanel opened="false" switchType="client" width="100%">
                                <f:facet name="header">
                                    <h:outputText value="Ações"/>
                                </f:facet>
                                <h:panelGrid id="painelGridAcoes" columns="2" width="100%"
                                             rowClasses="linhaImpar, linhaPar"
                                             columnClasses="classEsquerdaUsuario, classDireita">

                                    <h:outputText value="Novo Login:"
                                                  rendered="#{LoginControle.usuarioLogado.usuarioPactoSolucoes}"/>

                                    <h:panelGrid rendered="#{LoginControle.usuarioLogado.usuarioPactoSolucoes}"
                                                 columns="1"
                                                 cellpadding="0"
                                                 cellspacing="0">

                                        <h:panelGroup layout="block" style="padding: 5px 0 0;">
                                            <h:outputText rendered="false"
                                                          value="Garantir que as informações do usuário do ZillyonWeb e TreinoWeb estejam idênticas."
                                                          styleClass="classInfCadUsuario"/>
                                        </h:panelGroup>

                                        <h:panelGroup layout="block" style="padding: 5px 0 5px;">
                                            <a4j:commandButton id="sincronizarNovoLogin"
                                                               action="#{UsuarioControle.sincronizarNovoLogin}"
                                                               value="Sincronizar"
                                                               reRender="form"
                                                               oncomplete="#{UsuarioControle.mensagemNotificar}"
                                                               styleClass="btSec pure-button  pure-button-small margin-h-10 "/>
                                        </h:panelGroup>
                                    </h:panelGrid>
                                    <h:outputText value="Desvincular usuário da academia:"
                                                  rendered="#{(LoginControle.usuarioLogado.usuarioPactoSolucoes and not empty UsuarioControle.usuarioVO.usuarioGeral) or (not empty UsuarioControle.usuarioVO.usuarioGeral and LoginControle.permissaoAcessoMenuVO.desvincularUsuarioAcademia)}"/>

                                    <h:panelGrid rendered="#{(LoginControle.usuarioLogado.usuarioPactoSolucoes and not empty UsuarioControle.usuarioVO.usuarioGeral) or (not empty UsuarioControle.usuarioVO.usuarioGeral and LoginControle.permissaoAcessoMenuVO.desvincularUsuarioAcademia)}"
                                                 columns="1"
                                                 cellpadding="0"
                                                 cellspacing="0">

                                        <h:panelGroup layout="block" style="padding: 5px 0 0;">
                                            <h:outputText value="Remove o acesso deste usuário à academia."
                                                          styleClass="classInfCadUsuario"/>
                                        </h:panelGroup>

                                        <h:panelGroup layout="block" style="padding: 5px 0 5px;">
                                            <a4j:commandButton id="desvincularUsuarioNovoLogin"
                                                               action="#{UsuarioControle.confirmarDesvinculoUsuario}"
                                                               value="Desvincular"
                                                               reRender="form"
                                                               oncomplete="#{UsuarioControle.onComplete}#{UsuarioControle.mensagemNotificar}"
                                                               styleClass="btSec pure-button  pure-button-small margin-h-10 "/>
                                        </h:panelGroup>
                                    </h:panelGrid>
                                </h:panelGrid>
                            </rich:simpleTogglePanel>
                        </h:panelGrid>


                        <%--DADOS OPCIONAIS--%>
                        <h:panelGrid rendered="#{UsuarioControle.mostraLista}" columns="1" width="100%"
                                     headerClass="subordinado" rowClasses="linhaImpar, linhaPar"
                                     columnClasses="colunaCentralizada">
                            <rich:simpleTogglePanel opened="false" switchType="client" width="100%">
                                <f:facet name="header">
                                    <h:outputText value="Dados Opcionais"/>
                                </f:facet>
                                <h:panelGrid id="painelGridDadosOpcionais" columns="2" width="100%"
                                             rowClasses="linhaImpar, linhaPar"
                                             columnClasses="classEsquerda, classDireita"
                                             footerClass="colunaCentralizada">

                                    <h:outputText
                                            rendered="#{LoginControle.usuario.administrador}"
                                            value="#{msg_aplic.prt_Usuario_serviceUsuario}"/>
                                    <h:panelGroup
                                            rendered="#{LoginControle.usuario.administrador}">
                                        <h:inputText id="serviceUsuario" size="50" maxlength="100"
                                                     onblur="blurinput(this);"
                                                     onfocus="focusinput(this);"
                                                     styleClass="form"
                                                     value="#{UsuarioControle.usuarioVO.serviceUsuario}"/>
                                    </h:panelGroup>

                                    <h:outputText
                                            rendered="#{LoginControle.usuario.administrador}"
                                            value="#{msg_aplic.prt_Usuario_serviceSenha}"/>
                                    <h:panelGroup
                                            rendered="#{LoginControle.usuario.administrador}">
                                        <h:inputSecret redisplay="true" id="serviceSenha" size="14"
                                                       maxlength="14"
                                                       onblur="blurinput(this);"
                                                       onfocus="focusinput(this);" styleClass="form"
                                                       value="#{UsuarioControle.usuarioVO.serviceSenha}">
                                        </h:inputSecret>
                                    </h:panelGroup>

                                    <h:outputText value="Pedir senhas em Funcionalidades"/>
                                    <h:panelGroup >
                                        <h:selectBooleanCheckbox id="pedirSenhaFuncionalidade" value="#{UsuarioControle.usuarioVO.pedirSenhaFuncionalidade}"/>
                                    </h:panelGroup>

                                    <h:outputText value="Acesso ao Pacto App"/>
                                    <h:panelGroup >
                                        <h:selectBooleanCheckbox id="acessoPactoApp" value="#{UsuarioControle.usuarioVO.acessoPactoApp}"/>
                                    </h:panelGroup>

                                    <h:outputText value="Permite forçar execução de processos automáticos"
                                                  rendered="#{LoginControle.usuario.administrador}"/>
                                    <h:panelGroup rendered="#{LoginControle.usuario.administrador}">
                                        <h:selectBooleanCheckbox id="permiteExecutarProcessos" value="#{UsuarioControle.usuarioVO.permiteExecutarProcessos}"/>
                                    </h:panelGroup>

                                    <h:outputText rendered="#{!UsuarioControle.usuarioVO.administrador}"
                                                  value="#{msg_aplic.prt_Usuario_permiteAlteracaoPropriaSenha}:"/>
                                    <h:panelGroup rendered="#{!UsuarioControle.usuarioVO.administrador}">
                                        <h:selectBooleanCheckbox id="permiteAlteracaoPropriaSenha"
                                                                 value="#{UsuarioControle.usuarioVO.permiteAlterarPropriaSenha}">
                                        </h:selectBooleanCheckbox>
                                    </h:panelGroup>

                                    <h:outputText value="Permite excluir clientes com vínculos no banco de dados"
                                                  rendered="#{UsuarioControle.apresentarCampoExcluirDados}"/>
                                    <h:panelGroup rendered="#{UsuarioControle.apresentarCampoExcluirDados}">
                                        <h:selectBooleanCheckbox id="permiteExcluirCliente" value="#{UsuarioControle.usuarioVO.permiteExclusaoCliente}"/>
                                    </h:panelGroup>

                                    <h:outputText value="Usuário pode acionar o botão de pânico"
                                                  rendered="#{UsuarioControle.usuarioOAMDpossuiPermissaoConfigurarEmergencia}"/>
                                    <h:panelGroup rendered="#{UsuarioControle.usuarioOAMDpossuiPermissaoConfigurarEmergencia}">
                                        <h:selectBooleanCheckbox id="checkboxPermissaoBotaoPanico" value="#{UsuarioControle.usuarioVO.permiteAcionarBotaoPanico}"/>
                                    </h:panelGroup>
                                </h:panelGrid>
                            </rich:simpleTogglePanel>
                        </h:panelGrid>

                    </rich:tab>

                    <rich:tab id="abaDadosColaborador" label="Foto" switchType="client">
                        <a4j:jsFunction name="updateFoto" action="#{UsuarioControle.recarregarFoto}"
                                        reRender="panelFotoColaborador"/>
                        <h:panelGrid rendered="#{UsuarioControle.mostrarPanelFoto}" columns="1" width="100%"
                                     headerClass="subordinado"
                                     columnClasses="colunaCentralizada">

                            <f:facet name="header">
                                <h:outputText value="Foto"/>
                            </f:facet>

                            <h:panelGrid id="panelFotoColaborador" columns="1">
                                <a4j:mediaOutput element="img" id="imagem1" align="left"
                                                 style="left:0px;width:150px;height:150px " cacheable="false"
                                                 session="true"
                                                 rendered="#{!SuperControle.fotosNaNuvem}"
                                                 createContent="#{UsuarioControle.paintFoto}"
                                                 value="#{ImagemData}"
                                                 mimeType="image/jpeg">
                                    <f:param value="#{SuperControle.timeStamp}" name="time"/>
                                    <f:param name="largura" value="150"/>
                                    <f:param name="altura" value="150"/>
                                </a4j:mediaOutput>
                                <h:graphicImage rendered="#{SuperControle.fotosNaNuvem}"
                                                width="150" height="150"
                                                style="width:150px;height:150px"
                                                url="#{UsuarioControle.paintFotoDaNuvem}?time=#{SuperControle.timeStamp}">
                                </h:graphicImage>
                            </h:panelGrid>

                            <h:panelGrid columns="2">
                                <a4j:commandButton actionListener="#{CapturaFotoControle.selecionarPessoa}"
                                                   action="#{UsuarioControle.verificaColaborador}"
                                                   id="btnAlterarFoto" value="#{msg_bt.btn_capturarfoto}"
                                                   image="./imagens/webcam.png"
                                                   oncomplete="#{UsuarioControle.onComplete}"
                                                   reRender="form"
                                                   alt="#{msg_bt.btn_capturarfoto}"
                                                   title="#{msg_bt.btn_capturarfoto}" styleClass="botoes">
                                    <f:attribute name="pessoa"
                                                 value="#{UsuarioControle.usuarioVO.colaboradorVO.pessoa.codigo}"/>
                                </a4j:commandButton>
                                <a4j:commandButton id="btnRemoverFoto" value="#{msg_bt.btn_removerfoto}"
                                                   image="./images/icon_delete.png"
                                                   onclick="if (!confirm('Confirma exclusão do registro?')){return false;}"
                                                   action="#{UsuarioControle.removerFoto}"
                                                   reRender="panelFoto"
                                                   alt="#{msg_bt.btn_removerfoto}"
                                                   title="#{msg_bt.btn_removerfoto}"
                                                   styleClass="botoes"/>
                            </h:panelGrid>
                        </h:panelGrid>

                    </rich:tab>
                    <rich:tab label="Assinatura digital">
                        <script>
                            function sleep(milliseconds) {
                                var start = new Date().getTime();
                                for (var i = 0; i < 1e7; i++) {
                                    if ((new Date().getTime() - start) > milliseconds) {
                                        break;
                                    }
                                }
                            }
                        </script>
                        <h:panelGrid id="panelAssinaturaDigital" columns="1" width="100%" headerClass="subordinado"
                                     columnClasses="colunaCentralizada">
                            <f:facet name="header">
                                <h:outputText value="Assinatura da academia para contrato digital"/>
                            </f:facet>
                            <h:panelGrid columns="1" width="100%" rowClasses="linhaImpar, linhaPar"
                                         columnClasses="colunaCentralizada" id="painelAssDig">
                                <h:panelGroup rendered="#{not empty UsuarioControle.urlImagemAssinatura}">
                                    <img style="width: 40%;" src="${UsuarioControle.urlImagemAssinatura}"/>
                                </h:panelGroup>

                                <rich:fileUpload listHeight="0"
                                                 listWidth="150"
                                                 noDuplicate="false"
                                                 fileUploadListener="#{UsuarioControle.uploadImgAssinatura}"
                                                 maxFilesQuantity="1"
                                                 addControlLabel="Adicionar"
                                                 cancelEntryControlLabel="Cancelar"
                                                 doneLabel="Pronto"
                                                 sizeErrorLabel="Arquivo não Enviado. Tamanho Máximo Permitido 100 MB"
                                                 progressLabel="Enviando"
                                                 stopControlLabel="Parar"
                                                 uploadControlLabel="Enviar"
                                                 transferErrorLabel="Falha de Transmissão"
                                                 stopEntryControlLabel="Parar"
                                                 id="uploadAssinaturaDigital"
                                                 immediateUpload="true"
                                                 autoclear="true"
                                                 acceptedTypes="png,gif,jpg,jpeg,ico,bmp,PNG,GIF,JPG,JPEG,ICO,BMP">

                                    <a4j:support event="onuploadcomplete" reRender="panelErroMensagem,painelAssDig"
                                                 oncomplete="sleep(6000);"/>
                                </rich:fileUpload>
                            </h:panelGrid>
                        </h:panelGrid>
                    </rich:tab>

                    <rich:tab rendered="#{LoginControle.usuarioLogado.usuarioPactoSolucoes}"
                              action="#{UsuarioControle.carregarListaLogSincronizacao}"
                              label="Log Sincronização">

                        <h:panelGrid id="panelLogSincronizacaoLoginGeral" columns="1" width="100%" headerClass="subordinado"
                                     columnClasses="colunaCentralizada">

                            <h:panelGroup layout="block" style="overflow:auto; max-height: 300px;">
                                <rich:dataTable width="100%" headerClass="subordinado" rowClasses="linhaImpar, linhaPar"
                                                value="#{UsuarioControle.logSincronizacao}" var="log">
                                    <rich:column style="text-align: center">
                                        <f:facet name="header">
                                            <h:outputText value="Data"/>
                                        </f:facet>
                                        <h:outputText value="#{log.dataRegistroApresentar}"/>
                                    </rich:column>
                                    <rich:column style="text-align: center">
                                        <f:facet name="header">
                                            <h:outputText value="Operação"/>
                                        </f:facet>
                                        <h:outputText value="#{log.operacao}"/>
                                    </rich:column>
                                    <rich:column style="text-align: center">
                                        <f:facet name="header">
                                            <h:outputText value="Sucesso"/>
                                        </f:facet>
                                        <h:outputText value="#{log.sucesso ? 'SIM' : 'NÂO'}"/>
                                    </rich:column>
                                    <rich:column>
                                        <f:facet name="header">
                                            <h:outputText value="Log"/>
                                        </f:facet>
                                        <h:outputText value="#{log.resposta}"/>
                                    </rich:column>
                                </rich:dataTable>
                            </h:panelGroup>
                        </h:panelGrid>
                    </rich:tab>
                    <c:if test="${UsuarioControle.exibirReplicarRedeEmpresa}">
                        <rich:tab label="Replicar Empresa">

                            <h:panelGrid columns="1" width="100%" headerClass="subordinado"
                                         columnClasses="colunaCentralizada">
                                <f:facet name="header">
                                    <h:outputText value="#{msg_aplic.prt_PlanoReplicarEmpresa_tituloForm}"/>
                                </f:facet>
                                <h:panelGrid columns="3" style="border-style: solid;" id="contadorReplicaPlano"
                                             columnClasses="colunaCentralizada, colunaCentralizada, colunaCentralizada"
                                             width="100%">
                                    <h:outputText value="Unidades" styleClass="botoes nvoBt"/>
                                    <h:outputText value="Replicadas" styleClass="botoes nvoBt"/>
                                    <h:outputText value="Não Replicadas" styleClass="botoes nvoBt"/>
                                    <h:outputText value="#{UsuarioControle.listaUsuarioRedeEmpresaSize}"
                                                  style="font-size: 20pt; font-weight: bold;"/>
                                    <h:outputText value="#{UsuarioControle.listaUsuarioRedeEmpresaSincronizado}"
                                                  style="color: #0f4c36; font-size: 20pt; font-weight: bold;"/>
                                    <h:outputText
                                            value="#{UsuarioControle.listaUsuarioRedeEmpresaSize - UsuarioControle.listaUsuarioRedeEmpresaSincronizado}"
                                            style="color: #8b0000; font-size: 20pt; font-weight: bold;"/>
                                </h:panelGrid>
                                <h:panelGrid columns="1" id="contadorReplicaPlano2"
                                             columnClasses="colunaDireita"
                                             width="100%"
                                             style="margin-top: 20px; margin-bottom: 1px">
                                    <h:panelGroup layout="block">
                                        <a4j:commandButton value="Replicar Todas" styleClass="botoes nvoBt"
                                                           action="#{UsuarioControle.replicarTodas}"
                                                           reRender="listaEmpresasReplicar, contadorReplicaPlano"
                                                           ajaxSingle="true" immediate="true"/>
                                        <a4j:commandButton value="Replicar Selecionadas" styleClass="botoes nvoBt btSec"
                                                           action="#{UsuarioControle.replicarSelecionadas}"
                                                           reRender="listaEmpresasReplicar, contadorReplicaPlano"
                                                           ajaxSingle="true" immediate="true"/>
                                        <a4j:commandButton value="Limpar Selecionadas" styleClass="botoes nvoBt btSec"
                                                           action="#{UsuarioControle.limparReplicar}"
                                                           reRender="listaEmpresasReplicar, contadorReplicaPlano"
                                                           ajaxSingle="true" immediate="true"/>
                                    </h:panelGroup>
                                </h:panelGrid>
                                <h:panelGrid columns="1" rowClasses="linhaImpar, linhaPar"
                                             columnClasses="colunaCentralizada" width="100%">

                                    <h:dataTable id="listaEmpresasReplicar" width="100%" headerClass="subordinado"
                                                 styleClass="tabFormSubordinada"
                                                 rowClasses="linhaImpar, linhaPar"
                                                 columnClasses="colunaEsquerda, colunaEsquerda, colunaCentralizada, colunaEsquerda"
                                                 style="text-align: center;"
                                                 value="#{UsuarioControle.listaUsuarioRedeEmpresa}"
                                                 var="usuarioRedeEmpresaReplicacao">
                                        <h:column>
                                            <f:facet name="header">
                                                <h:outputText value=""/>
                                            </f:facet>
                                            <h:selectBooleanCheckbox id="check" styleClass="form"
                                                                     rendered="#{!usuarioRedeEmpresaReplicacao.dataAtualizacaoInformada}"
                                                                     value="#{usuarioRedeEmpresaReplicacao.selecionado}">
                                                <a4j:support event="onchange" reRender="listaEmpresasReplicar"/>
                                            </h:selectBooleanCheckbox>
                                        </h:column>
                                        <h:column>
                                            <f:facet name="header">
                                                <h:outputText value="#{msg_aplic.prt_PlanoRedeEmpresa_nomeUnidade}"/>
                                            </f:facet>
                                            <h:outputText value="#{usuarioRedeEmpresaReplicacao.nomeUnidade}"/>
                                        </h:column>
                                        <h:column>
                                            <f:facet name="header">
                                                <h:outputText value="#{msg_aplic.prt_PlanoRedeEmpresa_chave}"/>
                                            </f:facet>
                                            <h:outputText value="#{usuarioRedeEmpresaReplicacao.chaveDestino}"/>
                                        </h:column>
                                        <h:column>
                                            <f:facet name="header">
                                                <h:outputText value=""/>
                                            </f:facet>
                                            <h:panelGroup>
                                                <a4j:commandButton id="replicarPlano"
                                                                   reRender="listaEmpresasReplicar, contadorReplicaPlano"
                                                                   ajaxSingle="true" immediate="true"
                                                                   rendered="#{!usuarioRedeEmpresaReplicacao.dataAtualizacaoInformada}"
                                                                   action="#{UsuarioControle.replicarUsuarioRedeEmpresaGeral}"
                                                                   value="Replicar"/>
                                                <h:graphicImage url="./images/check.png"
                                                                rendered="#{usuarioRedeEmpresaReplicacao.dataAtualizacaoInformada}"/>
                                            </h:panelGroup>
                                        </h:column>
                                        <h:column>
                                            <f:facet name="header">
                                                <h:outputText
                                                        value="#{msg_aplic.prt_PlanoRedeEmpresa_mensagemSituacao}"/>
                                            </f:facet>
                                            <h:outputText value="#{usuarioRedeEmpresaReplicacao.mensagemSituacao}"/>
                                        </h:column>
                                        <h:column>
                                            <f:facet name="header">
                                                <h:outputText
                                                        value="Vínculo"/>
                                            </f:facet>
                                            <h:panelGroup>
                                            <a4j:commandButton
                                                               rendered="#{usuarioRedeEmpresaReplicacao.exibirBotaoRetirarVinculo}"
                                                               reRender="listaEmpresasReplicar, contadorReplicaPlano"
                                                               ajaxSingle="true" immediate="true"
                                                               action="#{UsuarioControle.retirarVinculoReplicacao}"
                                                               value="Retirar"/>
                                            </h:panelGroup>
                                        </h:column>
                                    </h:dataTable>
                                </h:panelGrid>
                            </h:panelGrid>
                        </rich:tab>
                    </c:if>
                </rich:tabPanel>

                <h:panelGrid columns="1" width="100%" styleClass="tabMensagens">
                    <h:panelGrid id="mensagens" columns="3" width="100%" styleClass="tabMensagens">
                        <h:panelGrid columns="1" width="100%">

                            <h:outputText value=" "/>

                        </h:panelGrid>
                        <h:commandButton rendered="#{UsuarioControle.sucesso}" image="./imagens/sucesso.png"/>
                        <h:commandButton rendered="#{UsuarioControle.erro}" image="./imagens/erro.png"/>
                        <h:panelGrid columns="1" width="100%">
                            <h:outputText id="msgUsuario" styleClass="mensagem" value="#{UsuarioControle.mensagem}"/>
                            <h:outputText id="msgUsuarioDet" styleClass="mensagemDetalhada"
                                          value="#{UsuarioControle.mensagemDetalhada}"/>
                        </h:panelGrid>
                    </h:panelGrid>
                    <h:panelGrid columns="1" width="100%" columnClasses="colunaCentralizada"
                                 style="margin-top: 10px; margin-bottom: 10px;">
                        <h:panelGroup>

                            <c:if test="${modulo eq 'zillyonWeb'}">
                                <a4j:commandLink id="novo" immediate="true" action="#{UsuarioControle.novo}"
                                                 value="#{msg_bt.btn_novo}"
                                                 title="#{msg.msg_novo_dados}" accesskey="1"
                                                 styleClass="botoes nvoBt btSec"/>

                                <h:outputText value="    "/>

                                <a4j:commandLink id="salvar" action="#{UsuarioControle.gravar}"
                                                 value="#{msg_bt.btn_gravar}"
                                                 oncomplete="#{UsuarioControle.msgAlert}"
                                                 reRender="formUsuarioEmail, modalVinculoCliente"
                                                 title="#{msg.msg_gravar_dados}" accesskey="2"
                                                 styleClass="botoes nvoBt"/>

                                <h:outputText value="    "/>

                                <h:panelGroup id="grupoBtnExcluir">
                                    <a4j:commandLink id="excluir"  reRender="mdlMensagemGenerica"
                                                       oncomplete="#{UsuarioControle.msgAlert}" action="#{UsuarioControle.confirmarExcluir}"
                                                       value="#{msg_bt.btn_excluir}" title="#{msg.msg_excluir_dados}" accesskey="3" styleClass="botoes nvoBt btSec btPerigo">
                                        <f:param name="metodochamar" value="excluir"/>
                                    </a4j:commandLink>
                                </h:panelGroup>

                                <h:outputText value="    "/>

                                <a4j:commandLink id="consultar" immediate="true"
                                                 action="#{UsuarioControle.inicializarConsultar}"
                                                 value="#{msg_bt.btn_voltar_lista}"
                                                 title="#{msg.msg_consultar_dados}" accesskey="4"
                                                 styleClass="botoes nvoBt btSec"/>

                                <h:outputText value="    "/>

                                <a4j:commandLink action="#{UsuarioControle.realizarConsultaLog}"
                                                 rendered="#{UsuarioControle.pesquisar}"
                                                 oncomplete="abrirPopup('./visualizadorLogForm.jsp', 'VisualizadorLog', 785, 595);"
                                                 title="Visualizar Log"
                                                 styleClass="botoes nvoBt btSec">
                                    <i style="text-decoration: none" class="fa-icon-search"/>
                                </a4j:commandLink>
                            </c:if>

                            <c:if test="${modulo eq 'centralEventos'}">
                                <a4j:commandButton id="novo" immediate="true" action="#{UsuarioControle.novo}"
                                                   value="#{msg_bt.btn_novo}" image="./imagens/botoesCE/btnNovo.png"
                                                   alt="#{msg.msg_novo_dados}"
                                                   accesskey="1" styleClass="botoes"/>

                                <h:outputText value="    "/>

                                <a4j:commandButton id="salvar"
                                                   action="#{UsuarioControle.gravarCE}"
                                                   value="#{msg_bt.btn_gravar}"
                                                   image="/imagens/botoesCE/gravar.png"
                                                   alt="#{msg.msg_gravar_dados}"
                                                   accesskey="2"
                                                   styleClass="botoes"
                                                   actionListener="#{UsuarioControle.autorizacao}">
                                    <!-- Entidade.usuario -->
                                    <f:attribute name="entidade" value="121"/>
                                    <!-- operacao.gravar -->
                                    <f:attribute name="operacao" value="G"/>
                                </a4j:commandButton>

                                <h:outputText value="    "/>

                                <a4j:commandButton id="excluir" onclick="if(!confirm('Confirma exclusão dos dados?')){return true;}"
                                                   action="#{UsuarioControle.excluirCE}" value="#{msg_bt.btn_excluir}"
                                                   image="/imagens/botoesCE/excluir.png" alt="#{msg.msg_excluir_dados}"
                                                   accesskey="3" styleClass="botaoExcluir"
                                                   actionListener="#{UsuarioControle.autorizacao}">
                                    <!-- Entidade.usuario -->
                                    <f:attribute name="entidade" value="121"/>
                                    <!-- operacao.excluir -->
                                    <f:attribute name="operacao" value="E"/>
                                </a4j:commandButton>


                                <h:outputText value="    "/>

                                <a4j:commandButton id="consultar" immediate="true"
                                                   action="#{UsuarioControle.inicializarConsultar}"
                                                   value="#{msg_bt.btn_consultar}"
                                                   image="/imagens/botoesCE/buscar.png"
                                                   alt="#{msg.msg_consultar_dados}"
                                                   accesskey="4" styleClass="botoes"
                                                   actionListener="#{UsuarioControle.autorizacao}">
                                    <!-- Entidade.usuario -->
                                    <f:attribute name="entidade" value="121"/>
                                    <!-- operacao.consultar -->
                                    <f:attribute name="operacao" value="C"/>
                                </a4j:commandButton>


                            </c:if>
                        </h:panelGroup>
                    </h:panelGrid>
                </h:panelGrid>

            </h:panelGrid>
        </h:form>
    </h:panelGrid>



    <rich:modalPanel id="modalVinculoCliente" autosized="true" shadowOpacity="true" width="800" height="350" styleClass="novaModal">

        <f:facet name="header">
            <h:panelGroup>
                <h:outputText value="Vínculos de Clientes"/>
            </h:panelGroup>
        </f:facet>
        <f:facet name="controls">
            <h:panelGroup>
                <h:graphicImage value="/imagens/close.png" style="cursor:pointer" id="hidelinkVincCliente"/>
                <rich:componentControl for="modalVinculoCliente" attachTo="hidelinkVincCliente"
                                       operation="hide" event="onclick">
                </rich:componentControl>
            </h:panelGroup>
        </f:facet>

        <a4j:form id="formVinculClientes">


            <div style="overflow: auto; >
                <h:panelGroup id="panelBasico">
                    <%@include file="include_vinculos_colaborador.jsp" %>
                </h:panelGroup>
            </div>

            <h:panelGrid id="pgBotoes" columns="1" width="100%" columnClasses="colunaCentralizada">
                <h:panelGroup>
                    <a4j:commandButton id="excluirPerfilAcessoTransferirVinculo"  rendered="#{ColaboradorControle.listaColaboradoresTrocarVinculosSize > 1}"
                                       action="#{UsuarioControle.transferirVinculosColaborador}" value="Transferir Vínculos"
                                       reRender="modalVinculoCliente"
                                       oncomplete="#{UsuarioControle.msgAlert}#{UsuarioControle.mensagemNotificar}"
                                       alt="#{msg.msg_gravar_dados}" accesskey="2"
                                       styleClass="botoes nvoBt"/>
                    <a4j:commandButton rendered="#{ColaboradorControle.tipoVinculoListaColaborador.sigla != 'CO' &&  ColaboradorControle.tipoVinculoListaColaborador.sigla != 'TW'}" id="excluirPerfilAcessoRemoverVinculo"
                                       action="#{UsuarioControle.removerVinculoColaborador}" value="Remover Vínculos"
                                       reRender="modalVinculoCliente"
                                       oncomplete="#{UsuarioControle.msgAlert}#{UsuarioControle.mensagemNotificar}"
                                       alt="#{msg.msg_gravar_dados}" accesskey="2"
                                       styleClass="botoes nvoBt"/>
                </h:panelGroup>
            </h:panelGrid>


        </a4j:form>
    </rich:modalPanel>
    <rich:modalPanel id="modalUsuarioEmail" autosized="true" styleClass="novaModal" shadowOpacity="true" >
        <f:facet name="header">
            <h:panelGroup>
                <h:outputText value="Selecione o email para login através do aplicativo"/>
            </h:panelGroup>
        </f:facet>
        <f:facet name="controls">
            <h:panelGroup>
                <h:outputText
                    styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign"
                    id="hidelinkUsuarioEmail"/>
                <rich:componentControl for="modalUsuarioEmail" attachTo="hidelinkUsuarioEmail"
                                       operation="hide" event="onclick">
                </rich:componentControl>
            </h:panelGroup>
        </f:facet>
        <a4j:form id="formUsuarioEmail">
            <h:panelGrid columns="1" width="100%" columnClasses="colunaCentralizada">
                <rich:dataTable id="dtUsuarioEmail" width="100%" styleClass="tabelaSimplesCustom"
                                value="#{UsuarioControle.usuarioVO.colaboradorVO.pessoa.emailVOs}" rows="5" var="colaboradorEmail">
                    <rich:column>
                        <a4j:commandLink action="#{UsuarioControle.gravarSimplesComUsuarioEmail}" styleClass="linkPadrao">
                            <h:outputText styleClass="tituloCampos" value="#{colaboradorEmail.email}"/>
                        </a4j:commandLink>
                    </rich:column>
                </rich:dataTable>
                <rich:datascroller align="center" renderIfSinglePage="false" for="dtUsuarioEmail" maxPages="10"/>
            </h:panelGrid>
        </a4j:form>
    </rich:modalPanel>
    <jsp:include page="includes/cliente/include_modal_capfoto_html5.jsp" flush="true"/>
    <jsp:include page="includes/include_carregando_ripple.jsp" flush="true"/>
    <jsp:include page="includes/include_modal_vinculo_agenda.jsp" flush="true"/>
    <%@include file="includes/include_modal_mensagem_generica.jsp"%>
    <%@include file="includes/include_modais_usuario_geral.jsp" %>
</f:view>
<script>
    document.getElementById("form:nome").focus();

    function validarSenha() {
        senha = document.getElementById("form:senha").value;
        senhaRepetida = document.getElementById("form:senhaConfirmar").value;
        if (senhaRepetida == senha) {
            document.getElementById("form:senhaErrada").hide();
        } else {
            document.getElementById("form:senhaErrada").show();
            document.getElementById("form:senhaConfirmar").focus();
        }
    }
</script>

<script>
    function clearAccentuation() {
        const username = document.getElementById('form:username');
        if (username) {
            username.addEventListener('keyup', e => {
                const start = this.selectionStart,
                    end = this.selectionEnd;

                username.value = username.value.normalize("NFD").replace(/[\u0300-\u036f]/g, "");

                this.setSelectionRange(start, end);
            });
        }
    }
    clearAccentuation();

    function iniciaCarregandoUsername() {
        jQuery('.outUsuarioDisponivel').addClass("inputCarregando");
    }

    function finalizaCarregandoUsername() {
        jQuery('.outUsuarioDisponivel').removeClass( "inputCarregando" );
    }

    carregarTooltipsterUsuarioForm();
</script>
