<%@page contentType="text/html" %>
<%@page pageEncoding="ISO-8859-1" %>
<%@taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@taglib prefix="f" uri="http://java.sun.com/jsf/core" %>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html" %>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich" %>
<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax" %>
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao"/>
<f:loadBundle var="msg" basename="propriedades.Mensagens"/>

<script src="script/packJQueryPlugins.min.js" type="text/javascript"></script>
<link href="./css/otimize.css" rel="stylesheet" type="text/css">
<link href="beta/css/pure-min.css" type="text/css" rel="stylesheet"/>
<link href="beta/css/font-awesome_2.0.min.css" type="text/css" rel="stylesheet"/>
<link href="beta/css/pure-ext.css" type="text/css" rel="stylesheet"/>
<script type="text/javascript" src="script/tooltipster/jquery.tooltipster.min.js"></script>
<style>
    .color-white {
        color: white;
    }
</style>

<script>
    function carregarTooltipsterGestaoNotas() {
        carregarTooltipNota(jQuery('.tooltipster'));
    }

    function carregarTooltipNota(el) {
        el.tooltipster({
            theme: 'tooltipster-light',
            position: 'bottom',
            animation: 'grow',
            contentAsHTML: true
        });
    }
</script>

<head>
    <%@include file="/includes/include_import_minifiles.jsp" %>
</head>

<f:view>

    <a4j:loadScript src="/script/jquery.maskedinput-1.2.2.js"/>
    <title>${msg_aplic.prt_gestao_notas}</title>

    <c:set var="titulo" scope="session" value=" ${msg_aplic.prt_gestao_notas}"></c:set>
    <c:set var="urlWiki" scope="session"
           value="${SuperControle.urlBaseConhecimento}como-emitir-nota-fiscal-pelo-gestao-de-notas-ou-pelo-gestao-de-nfc-e/"></c:set>

    <f:facet name="header">
        <jsp:include page="topoReduzido_material.jsp"/>
    </f:facet>
    <style type="text/css">
        .divComBorda {
            border: solid 1px #C0C0C0;
        }
    </style>

    <h:form id="form" styleClass="pure-form pure-u-1" style="height: 100%;">
        <hr style="border-color: #e6e6e6;"/>
        <rich:tabPanel width="100%" selectedTab="dadosPessoais" switchType="ajax">
            <rich:tab id="abaIndividual" label="#{msg_aplic.prt_nota_individual}">
                <h:panelGrid columns="2" rowClasses="linhaImpar, linhaPar" style="margin-top: 10px"
                             columnClasses="classEsquerda, classDireita" width="100%">

                    <h:panelGroup rendered="#{LoginControle.usuario.administrador}">
                        <h:outputText styleClass="titulo3" value="#{msg_aplic.EMPRESA}:"/>
                    </h:panelGroup>

                    <h:panelGroup rendered="#{LoginControle.usuario.administrador}">
                        <h:selectOneMenu id="comboEmpresa" styleClass="form" onblur="blurinput(this);"
                                         onfocus="focusinput(this);" value="#{GestaoNotasControle.empresaVO.codigo}">
                            <f:selectItems value="#{GestaoNotasControle.listaEmpresas}"/>

                            <a4j:support event="onchange" reRender="form"
                                         action="#{GestaoNotasControle.consultarDadosEmpresa}"></a4j:support>
                        </h:selectOneMenu>
                    </h:panelGroup>

                    <h:outputText styleClass="tituloCampos"
                                  rendered="#{(GestaoNotasControle.emissaoPorCompetencia or GestaoNotasControle.emissaoPorCompetenciaIndependenteQuitacao)
                                  and GestaoNotasControle.empresaLogado.mostrarNotaPorDiaCompetencia}"
                                  value="Data Inicial"/>

                    <h:outputText styleClass="tituloCampos"
                                  rendered="#{GestaoNotasControle.emissaoPorCompetencia and !GestaoNotasControle.empresaLogado.mostrarNotaPorDiaCompetencia}"
                                  value="#{msg_aplic.prt_mes_competencia}"/>
                    <h:outputText styleClass="tituloCampos"
                                  rendered="#{GestaoNotasControle.emissaoPorCompetenciaIndependenteQuitacao and !GestaoNotasControle.empresaLogado.mostrarNotaPorDiaCompetencia}"
                                  value="#{msg_aplic.prt_mes_competencia_independente}"/>

                    <h:outputText styleClass="tituloCampos" rendered="#{!GestaoNotasControle.emissaoPorCompetencia and !GestaoNotasControle.emissaoPorCompetenciaIndependenteQuitacao}"
                                  value="#{GestaoNotasControle.labelPeriodo}"/>
                    <h:panelGroup>

                        <h:panelGroup>
                            <h:panelGroup id="pgDataInicio" rendered="#{!GestaoNotasControle.emissaoPorCompetencia and !GestaoNotasControle.emissaoPorCompetenciaIndependenteQuitacao}">
                                <rich:calendar id="dataInicio"
                                               value="#{GestaoNotasControle.dataInicio}"
                                               inputSize="10"
                                               inputClass="form"
                                               oninputblur="blurinput(this);"
                                               oninputfocus="focusinput(this);"
                                               oninputchange="return validar_Data(this.id)"
                                               datePattern="dd/MM/yyyy"
                                               enableManualInput="true"
                                               zindex="2"
                                               showWeeksBar="false">
                                    <a4j:support event="onchanged"
                                                 action="#{GestaoNotasControle.limparListas}"
                                                 reRender="dtableGestaoNotas, totalizadores,mensagem"/>
                                </rich:calendar>
                                <h:message for="dataInicio" styleClass="mensagemDetalhada"/>
                                <rich:jQuery id="mskDataInicio" selector=".rich-calendar-input" timing="onload"
                                             query="mask('99/99/9999')"/>
                            </h:panelGroup>

                            <h:panelGroup id="pgMesCompetencia"
                                          rendered="#{(GestaoNotasControle.emissaoPorCompetencia or GestaoNotasControle.emissaoPorCompetenciaIndependenteQuitacao)
                                          and !GestaoNotasControle.empresaLogado.mostrarNotaPorDiaCompetencia}">
                                <rich:calendar id="dataCompetencia"
                                               value="#{GestaoNotasControle.dataInicio}"
                                               inputSize="8"
                                               inputClass="form MMyyyy"
                                               oninputblur="blurinput(this);"
                                               oninputfocus="focusinput(this);"
                                               datePattern="MM/yyyy"
                                               enableManualInput="true"
                                               zindex="2"
                                               showWeeksBar="true">
                                    <a4j:support event="onchanged"
                                                 action="#{GestaoNotasControle.limparListas}"
                                                 reRender="dtableGestaoNotas, totalizadores,mensagem"/>
                                </rich:calendar>
                                <h:message for="dataCompetencia" styleClass="mensagemDetalhada"/>
                            </h:panelGroup>

                            <h:panelGroup id="pgMesCompetenciaDia"
                                          rendered="#{(GestaoNotasControle.emissaoPorCompetencia or GestaoNotasControle.emissaoPorCompetenciaIndependenteQuitacao)
                                          and GestaoNotasControle.empresaLogado.mostrarNotaPorDiaCompetencia}">
                                <rich:calendar id="dataCompetenciaDia"
                                               value="#{GestaoNotasControle.dataInicio}"
                                               inputSize="10"
                                               oninputblur="blurinput(this);"
                                               oninputfocus="focusinput(this);"
                                               datePattern="dd/MM/yyyy"
                                               enableManualInput="true"
                                               zindex="2"
                                               showWeeksBar="false">
                                    <a4j:support event="onchanged"
                                                 action="#{GestaoNotasControle.limparListas}"
                                                 reRender="dtableGestaoNotas, totalizadores,mensagem"/>
                                </rich:calendar>
                                <h:message for="dataCompetenciaDia" styleClass="mensagemDetalhada"/>
                            </h:panelGroup>

                            <h:panelGroup id="pgMesCompetenciaDiaFinal"
                                          rendered="#{(GestaoNotasControle.emissaoPorCompetencia or GestaoNotasControle.emissaoPorCompetenciaIndependenteQuitacao)
                                          and GestaoNotasControle.empresaLogado.mostrarNotaPorDiaCompetencia}">
                                <h:outputText styleClass="tituloCampos"
                                              value="#{msg_aplic.prt_HistoricoContrato_dataFinalSituacao}"/>
                                <rich:calendar id="dataCompetenciaFinalMes"
                                               value="#{GestaoNotasControle.dataFim}"
                                               inputSize="10"
                                               oninputblur="blurinput(this);"
                                               oninputfocus="focusinput(this);"
                                               datePattern="dd/MM/yyyy"
                                               enableManualInput="true"
                                               zindex="2"
                                               showWeeksBar="false">
                                    <a4j:support event="onchanged"
                                                 action="#{GestaoNotasControle.limparListas}"
                                                 reRender="dtableGestaoNotas, totalizadores,mensagem"/>
                                </rich:calendar>
                                <h:message for="dataCompetenciaFinalMes" styleClass="mensagemDetalhada"/>
                            </h:panelGroup>

                        </h:panelGroup>

                        <h:outputText styleClass="tituloCampos" rendered="#{!GestaoNotasControle.emissaoPorCompetencia and !GestaoNotasControle.emissaoPorCompetenciaIndependenteQuitacao}"
                                      style="vertical-align: middle" value=" #{msg_aplic.prt_ate} "/>

                        <h:panelGroup rendered="#{!GestaoNotasControle.emissaoPorCompetencia and !GestaoNotasControle.emissaoPorCompetenciaIndependenteQuitacao}">
                            <rich:calendar id="dataFim"
                                           value="#{GestaoNotasControle.dataFim}"
                                           inputSize="10"
                                           inputClass="form"
                                           oninputblur="blurinput(this);"
                                           oninputfocus="focusinput(this);"
                                           oninputchange="return validar_Data(this.id)"
                                           datePattern="dd/MM/yyyy"
                                           enableManualInput="true"
                                           zindex="2"
                                           showWeeksBar="false">
                                <a4j:support event="onchanged"
                                             action="#{GestaoNotasControle.limparListas}"
                                             reRender="dtableGestaoNotas, totalizadores,mensagem"/>
                            </rich:calendar>
                            <h:message for="dataFim" styleClass="mensagemDetalhada"/>
                        </h:panelGroup>

                    </h:panelGroup>

                    <h:outputText styleClass="tituloCampos"
                                  value="#{msg_aplic.prt_forma_pagamento} "/>
                    <h:panelGroup id="formaspagamentoselect">
                        <h:selectOneMenu value="#{GestaoNotasControle.formaPagamentoSelecionado}">
                            <f:selectItems value="#{GestaoNotasControle.selectItemsFormaDePagamento}"/>
                            <a4j:support event="onchange"
                                         action="#{GestaoNotasControle.limparListas}"
                                         reRender="dtableGestaoNotas, totalizadores,mensagem"/>
                        </h:selectOneMenu>
                        <a4j:commandLink style="margin-left: 10px" action="#{GestaoNotasControle.mudarIncluirInativas}"
                                         reRender="formaspagamentoselect">
                            <h:outputText rendered="#{!GestaoNotasControle.formasInativas}" value="Incluir as inativas"
                                          title="Adicione à lista as formas de pagamento que já foram inativadas"/>
                            <h:outputText rendered="#{GestaoNotasControle.formasInativas}" value="Remover as inativas"
                                          title="Remova da lista as formas de pagamento que já foram inativadas"/>
                        </a4j:commandLink>
                    </h:panelGroup>

                    <h:outputText styleClass="tituloCampos" value="#{msg_aplic.NOME}"/>
                    <h:panelGroup>
                        <h:inputText id="nomeCliente" size="50"
                                     maxlength="50" onblur="blurinput(this);" onfocus="focusinput(this);"/>

                        <rich:suggestionbox height="200" width="200"
                                            for="nomeCliente"
                                            fetchValue="#{result.nome}"
                                            suggestionAction="#{GestaoNotasControle.executarAutocompleteConsultaPessoa}"
                                            minChars="1" rowClasses="20"
                                            status="statusHora"
                                            nothingLabel="#{msg_aplic.prt_nenhuma_pessoa_encontrada}"
                                            var="result" id="suggestionNomeCliente" reRender="mensagem">
                            <a4j:support event="onselect"
                                         reRender="dtableGestaoNotas, totalizadores,mensagem"
                                         action="#{GestaoNotasControle.selecionarPessoaSuggestionBox}"/>
                            <h:column>
                                <h:outputText value="#{result.nome}"/>
                            </h:column>
                        </rich:suggestionbox>
                        <a4j:commandButton id="limparAluno"
                                           onclick="document.getElementById('form:nomeCliente').value = null;"
                                           image="/images/limpar.gif" title="#{msg_aplic.prt_limpar_aluno}."
                                           status="false"
                                           reRender="dtableGestaoNotas, totalizadores,mensagem"
                                           action="#{GestaoNotasControle.limparAluno}"/>
                    </h:panelGroup>

                    <h:outputText styleClass="tituloCampos" value="Plano(s)"/>
                    <h:panelGroup layout="block" id="filtroPlanos" style="padding: 5px 0px 5px 0px;">
                        <a4j:commandLink reRender="panelGeralPlanos"
                                         onclick="Richfaces.showModalPanel('modalPlanos');"
                                         oncomplete="#{GestaoNotasControle.onComplete};#{GestaoNotasControle.mensagemNotificar}">
                            Selecionar planos ${GestaoNotasControle.qtdPlanosSelecionados}
                        </a4j:commandLink>
                    </h:panelGroup>

                    <h:outputText styleClass="tituloCampos" value="Valor desejado para emissão de notas" title="As notas só serão selecionadas caso o CPF do aluno esteja cadastrado ou a empresa emita nota manual."/>
                    <h:panelGroup>
                        <%--<h:inputText value="#{GestaoNotasControle.valorParaSelecionar}"/>--%>
                        <h:inputText id="valor" onfocus="focusinput(this);"
                                     onkeypress="return formatar_moeda(this,'.',',',event);"
                                     styleClass="form" value="#{GestaoNotasControle.valorParaSelecionar}">
                            <f:converter converterId="FormatadorNumerico"/>
                        </h:inputText>

                        <a4j:commandLink action="#{GestaoNotasControle.selecionarValor}"
                                         style="margin-left: 5px"
                                         reRender="dtableGestaoNotas, totalizadores,mensagem"
                                         styleClass="pure-button pure-button-small "
                                         title="As notas só serão selecionadas caso o CPF do aluno esteja cadastrado ou a empresa emita nota manual.">
                            <i class="fa-icon-check"></i> &nbsp ${msg_aplic.SELECIONAR}
                        </a4j:commandLink>
                    </h:panelGroup>

                    <h:outputText styleClass="tituloCampos" value="Visualizar "/>
                    <h:panelGroup>
                        <h:selectOneMenu value="#{GestaoNotasControle.tipoVisualizacao}">
                            <f:selectItems value="#{GestaoNotasControle.selectItemsTipoVisualizacao}"/>
                            <a4j:support event="onchange" reRender="dtableGestaoNotas, totalizadores,mensagem"
                                         action="#{GestaoNotasControle.processarTipoVisualizacao}"/>
                        </h:selectOneMenu>
                    </h:panelGroup>

                    <h:outputText rendered="#{GestaoNotasControle.permiteAlterarDataEmissaoNFSe}"
                                  styleClass="tituloCampos" value="#{msg_aplic.prt_data_emissao} "/>
                    <h:panelGroup rendered="#{GestaoNotasControle.permiteAlterarDataEmissaoNFSe}">
                        <rich:calendar
                                id="dataEmissao"
                                style="margin-left: 6px"
                                inputSize="10"
                                inputClass="form"
                                oninputblur="blurinput(this);"
                                oninputfocus="focusinput(this);"
                                oninputchange="return validar_Data(this.id)"
                                datePattern="dd/MM/yyyy"
                                enableManualInput="true"
                                showWeeksBar="false"
                                value="#{GestaoNotasControle.dataEmissao}"/>
                    </h:panelGroup>

                    <h:outputText rendered="#{GestaoNotasControle.permiteGerarLoteRPSSaoPaulo}"
                                  styleClass="tituloCampos" value="#{msg_aplic.prt_sequencia_rps}"/>
                    <h:inputText rendered="#{GestaoNotasControle.permiteGerarLoteRPSSaoPaulo}"
                                 value="#{GestaoNotasControle.empresaVO.sequencialLoteRPS}"/>

                    <h:outputText rendered="#{!GestaoNotasControle.loadEnviandoNotas}"/>

                    <h:panelGroup>
                        <h:panelGrid columns="1" width="100%">
                            <h:panelGroup rendered="#{!GestaoNotasControle.loadEnviandoNotas}">
                                <a4j:commandLink action="#{GestaoNotasControle.consultarItens}" id="ConsultarItens"
                                                 reRender="dtableGestaoNotas, totalizadores,mensagem"
                                                 styleClass="pure-button pure-button-small pure-button-primary "
                                                 onclick="fireElementFromAnyParent('form:btnAtualizaTempo')"
                                                 oncomplete="#{GestaoNotasControle.mensagemNotificar}">
                                    <i class="fa-icon-check"></i> &nbsp ${msg_aplic.CONSULTAR}
                                </a4j:commandLink>

                                <a4j:commandLink rendered="#{!GestaoNotasControle.permiteGerarLoteRPS}"
                                                 style="margin-left: 12px"
                                                 onclick="fireElementFromAnyParent('form:btnAtualizaTempo');if (!confirm('Confirma envio de todas as notas?')){return false;}"
                                                 action="#{GestaoNotasControle.enviarTodasNotas}"
                                                 styleClass="pure-button pure-button-small"
                                                 reRender="dtableGestaoNotas, totalizadores,mensagem">
                                    <i class="fa-icon-cloud-upload"></i> &nbsp ${msg_aplic.prt_enviar_todas}
                                </a4j:commandLink>
                            </h:panelGroup>
                        </h:panelGrid>
                    </h:panelGroup>
                </h:panelGrid>
                <h:panelGroup
                        rendered="#{GestaoNotasControle.loadEnviandoNotas}">
                    <h:panelGrid  columns="1">
                        <h:outputText id="msgProcessando2" styleClass="mensagemDetalhada"  value="Uma solicitação desse serviço está sendo processada. Dentro de alguns instantes, atualize a página para verificar se operação já foi concluída"/>
                        <rich:spacer height="7"/>
                        <a4j:commandLink id="atualizar2" title="Atualizar" onclick="window.location.reload();" styleClass="pure-button pure-button-primary">
                            <i class="fa-icon-refresh"></i>&nbsp;Atualizar
                        </a4j:commandLink>
                    </h:panelGrid>
                </h:panelGroup>


                <h:panelGrid id="totalizadores" columns="3" width="100%" columnClasses="w30,w30,w40"
                             style="margin-top: 12px">
                    <h:panelGroup>
                        <rich:dataTable value="#{GestaoNotasControle.totalizadores}" width="100%" var="totalizador"
                                        rowClasses="linhaImpar, linhaPar">
                            <rich:column>
                                <f:facet name="header">
                                    <h:outputText value="#{msg_aplic.TOTALIZADOR}"/>
                                </f:facet>
                                <h:outputText rendered="#{!totalizador.click}"
                                              value="#{totalizador.nome}"/>
                                <a4j:commandLink rendered="#{totalizador.click}"
                                                 style="font-size: 14px; color: red"
                                                 reRender="panelGeralModalNotasExcluidas"
                                                 oncomplete="Richfaces.showModalPanel('modalNotasExcluidas');"
                                                 value="#{totalizador.nome}"/>
                            </rich:column>
                            <rich:column style="text-align: right">
                                <f:facet name="header">
                                    <h:outputText value="Qtd"/>
                                </f:facet>
                                <h:outputText rendered="#{!totalizador.click}"
                                              value="#{totalizador.quantidade}"/>
                                <a4j:commandLink rendered="#{totalizador.click}"
                                                 reRender="panelGeralModalNotasExcluidas"
                                                 style="font-size: 14px; color: red"
                                                 oncomplete="Richfaces.showModalPanel('modalNotasExcluidas');"
                                                 value="#{totalizador.quantidade}"/>
                            </rich:column>
                            <rich:column style="text-align: right">
                                <f:facet name="header">
                                    <h:outputText value="#{msg_aplic.prt_sifrao_dinheiro}"/>
                                </f:facet>
                                <h:outputText rendered="#{!totalizador.click}"
                                              value="#{totalizador.valor_apresentar}"/>
                                <a4j:commandLink rendered="#{totalizador.click}"
                                                 style="font-size: 14px; color: red"
                                                 reRender="panelGeralModalNotasExcluidas"
                                                 oncomplete="Richfaces.showModalPanel('modalNotasExcluidas');"
                                                 value="#{totalizador.valor_apresentar}"/>
                            </rich:column>
                        </rich:dataTable>

                        <h:panelGroup layout="block" rendered="#{GestaoNotasControle.empresaVO.permiteGerarNotaManual}">
                            <br/>
                            <a4j:commandLink
                                    id="btnNotaManual"
                                    style="margin-left: 5px"
                                    reRender="modalNotaManual"
                                    action="#{GestaoNotasControle.preparaNotaManual}"
                                    status="false"
                                    oncomplete="#{GestaoNotasControle.onComplete}">
                                <i class="fa-icon-cloud-upload"></i> &nbsp ${msg_aplic.prt_notas_manuais_selecionadas}
                            </a4j:commandLink>

                            <br/>
                            <br/>
                            <a4j:commandLink
                                    id="btnNotaManualTodas"
                                    style="margin-left: 5px"
                                    reRender="modalNotaManual, dtableGestaoNotas, totalizadores, mensagem"
                                    action="#{GestaoNotasControle.preparaNotaManualTodas}"
                                    status="false"
                                    oncomplete="#{GestaoNotasControle.onComplete}">
                                <i class="fa-icon-cloud-upload"></i> &nbsp ${msg_aplic.prt_nota_manual_todas}
                            </a4j:commandLink>

                            <br/>
                            <br/>
                            <a4j:commandLink
                                    id="btnNotaManualRelatorio"
                                    style="margin-left: 5px"
                                    reRender="modalNotaManualTitulo, dtableGestaoNotas, totalizadores, mensagem"
                                    action="#{GestaoNotasControle.preparaImprimirRelatorioEmitidas}"
                                    oncomplete="#{GestaoNotasControle.onComplete}">
                                <i class="fa-icon-print"></i> &nbsp ${msg_aplic.prt_relatorio_notas_manuais_emitidas}
                            </a4j:commandLink>

                            <br/>
                            <br/>
                            <a4j:commandLink
                                    id="btnNotaManualExcluir"
                                    style="margin-left: 5px"
                                    reRender="modalNotaManualExcluir, dtableGestaoNotas, totalizadores, mensagem"
                                    action="#{GestaoNotasControle.preparaExcluirNotaManual}"
                                    status="false"
                                    oncomplete="#{GestaoNotasControle.onComplete}">
                                <i class="fa-icon-trash"></i> &nbsp ${msg_aplic.prt_excluir_nota_manual}
                            </a4j:commandLink>
                        </h:panelGroup>
                    </h:panelGroup>

                    <h:panelGroup layout="block" style="padding-left: 10px">
                        <rich:dataTable value="#{GestaoNotasControle.totalTipoFormaPg}" width="100%" var="tot"
                                        rowClasses="linhaImpar, linhaPar">
                            <rich:column>
                                <f:facet name="header">
                                    <h:outputText value="Tipo Pagamento"/>
                                </f:facet>
                                <h:outputText value="#{tot.nome}"/>
                            </rich:column>
                            <rich:column style="text-align: right">
                                <f:facet name="header">
                                    <h:outputText value="Emitido"/>
                                </f:facet>
                                <h:outputText value="#{tot.valor_apresentar}"/>
                            </rich:column>
                            <rich:column style="text-align: right">
                                <f:facet name="header">
                                    <h:outputText value="Não Emitido"/>
                                </f:facet>
                                <h:outputText value="#{tot.valorNaoEmitido_apresentar}"/>
                            </rich:column>
                            <rich:column style="text-align: right">
                                <f:facet name="header">
                                    <h:outputText value="Total"/>
                                </f:facet>
                                <h:outputText value="#{tot.valorTotal_apresentar}"/>
                            </rich:column>
                        </rich:dataTable>
                    </h:panelGroup>

                    <h:panelGroup layout="block" style="padding-left: 10px">
                        <rich:dataTable value="#{GestaoNotasControle.totalFormaPg}" width="100%" var="tot"
                                        rowClasses="linhaImpar, linhaPar">
                            <rich:column>
                                <f:facet name="header">
                                    <h:outputText value="#{msg_aplic.prt_forma_pagamento}"/>
                                </f:facet>
                                <h:outputText value="#{tot.nome}"/>
                            </rich:column>
                            <rich:column style="text-align: right">
                                <f:facet name="header">
                                    <h:outputText value="Emitido"/>
                                </f:facet>
                                <h:outputText value="#{tot.valor_apresentar}"/>
                            </rich:column>
                            <rich:column style="text-align: right">
                                <f:facet name="header">
                                    <h:outputText value="Não Emitido"/>
                                </f:facet>
                                <h:outputText value="#{tot.valorNaoEmitido_apresentar}"/>
                            </rich:column>
                            <rich:column style="text-align: right">
                                <f:facet name="header">
                                    <h:outputText value="Total"/>
                                </f:facet>
                                <h:outputText value="#{tot.valorTotal_apresentar}"/>
                            </rich:column>
                        </rich:dataTable>
                    </h:panelGroup>
                </h:panelGrid>

                <h:panelGrid id="dtableGestaoNotas" columns="1" width="100%" style="text-align: center" cellpadding="0"
                             cellspacing="0"
                             styleClass="centralizado">
                    <h:panelGrid width="100%" style="text-align: right">
                        <h:panelGroup layout="block" style="margin-top: 10px">
                            <a4j:commandButton id="exportarExcel"
                                               image="/imagens/btn_excel.png"
                                               style="margin-left: 8px;"
                                               actionListener="#{ExportadorListaControle.exportar}"
                                               rendered="#{not empty GestaoNotasControle.listaItensApresentar}"
                                               value="Excel"
                                               oncomplete="abrirPopup('UpdateServlet?op=downloadfile&file=#{ExportadorListaControle.fileName}&mimetype=application/vnd.ms-excel','GestaoNotas', 800,200);#{ExportadorListaControle.msgAlert}"
                                               accesskey="2" styleClass="botoes">
                                <f:attribute name="lista" value="#{GestaoNotasControle.listaItensApresentar}"/>
                                <f:attribute name="tipo" value="xls"/>
                                <f:attribute name="atributos"
                                             value="nome=Nome,nomeResponsavel=Responsável,matricula=Matrícula,cpf=CPF#{GestaoNotasControle.emissaoPorFaturamentoRecebido ? ',descricaoProdutosPagos=Produto' : (GestaoNotasControle.emissaoPorFaturamento or GestaoNotasControle.emissaoPorCompetencia or GestaoNotasControle.emissaoPorCompetenciaIndependenteQuitacao ? ',movProdutoVO.descricao=Produto':'') },valor_apresentar=Valor a Emitir,notaNFSEEmitida=Item Enviado,rps=IdLote"/>
                                <f:attribute name="prefixo" value="GestaoNotas"/>
                            </a4j:commandButton>
                            <%--BOTÃO PDF--%>
                            <a4j:commandButton id="exportarPdf"
                                               style="margin-left: 8px;"
                                               image="/imagens/imprimir.png"
                                               actionListener="#{ExportadorListaControle.exportar}"
                                               rendered="#{not empty GestaoNotasControle.listaItensApresentar}"
                                               value="PDF"
                                               oncomplete="abrirPopup('UpdateServlet?op=downloadfile&file=#{ExportadorListaControle.fileName}&mimetype=application/pdf','GestaoNotas', 800,200);#{ExportadorListaControle.msgAlert}"
                                               accesskey="2" styleClass="botoes">
                                <f:attribute name="lista" value="#{GestaoNotasControle.listaItensApresentar}"/>
                                <f:attribute name="tipo" value="pdf"/>
                                <f:attribute name="atributos"
                                             value="nome=Nome,nomeResponsavel=Responsável,matricula=Matrícula,cpf=CPF#{GestaoNotasControle.emissaoPorFaturamentoRecebido ? ',descricaoProdutosPagos=Produto' : (GestaoNotasControle.emissaoPorFaturamento or GestaoNotasControle.emissaoPorCompetencia or GestaoNotasControle.emissaoPorCompetenciaIndependenteQuitacao ? ',movProdutoVO.descricao=Produto':'')},valor_apresentar=Valor a Emitir,notaNFSEEmitida=Item Enviado"/>
                                <f:attribute name="prefixo" value="GestaoNotas"/>
                            </a4j:commandButton>
                        </h:panelGroup>
                    </h:panelGrid>

                    <h:panelGroup layout="block" style="margin-top: 5px">
                        <rich:dataTable id="tblGestaNotas" width="100%"
                                        columnClasses="colunaEsquerda,colunaEsquerda,colunaEsquerda,colunaEsquerda,colunaEsquerda,colunaDireita,colunaDireita,colunaDireita,colunaCentralizada,colunaCentralizada,colunaCentralizada,colunaCentralizada,colunaCentralizada"
                                        value="#{GestaoNotasControle.listaItensApresentar}"
                                        var="item" rowKeyVar="status" rows="30">
                            <%@include file="/pages/ce/includes/include_contador_richtable.jsp" %>

                            <rich:column sortBy="#{item.matricula}" style="text-align: center;#{item.style}">
                                <f:facet name="header">
                                    <h:outputText value="#{msg_aplic.MATRICULA}"/>
                                </f:facet>
                                <a4j:commandLink style="#{item.textStyle}" value="#{item.matricula}"
                                                 action="#{GestaoNotasControle.irParaTelaClienteColaborador}"
                                                 oncomplete="#{GestaoNotasControle.onComplete}">
                                    <f:param name="state" value="AC"/>
                                </a4j:commandLink>
                            </rich:column>

                            <rich:column sortBy="#{item.nome}" style="#{item.style}">
                                <f:facet name="header">
                                    <h:outputText value="#{msg_aplic.NOME}"/>
                                </f:facet>
                                <a4j:commandLink style="#{item.textStyle}" value="#{item.nome}"
                                                 rendered="#{!item.responsavelReciboDiferenteDoTitularContrato}"
                                                 action="#{GestaoNotasControle.irParaTelaClienteColaborador}"
                                                 oncomplete="#{GestaoNotasControle.onComplete}">
                                    <f:param name="state" value="AC"/>
                                </a4j:commandLink>
                                <a4j:commandLink style="#{item.textStyle}" value="#{item.nome}"
                                                 rendered="#{item.responsavelReciboDiferenteDoTitularContrato}"
                                                 action="#{GestaoNotasControle.irParaTelaClienteColaborador}"
                                                 oncomplete="#{GestaoNotasControle.onComplete}"
                                                 styleClass="tooltipster">
                                    <f:param name="state" value="AC"/>
                                    <i class="fa-icon-info-circle"  style="font-size: 14px">
                                        <f:attribute name="title" value="#{item.mensagemResponsavelReciboDiferenteDoTitularContrato}" />
                                    </i>
                                </a4j:commandLink>
                            </rich:column>

                            <rich:column sortBy="#{item.nomeResponsavel}" style="#{item.style}">
                                <f:facet name="header">
                                    <h:outputText value="#{msg_aplic.NOME_RESPONSAVEL}"/>
                                </f:facet>
                                <a4j:commandLink style="#{item.textStyle}" value="#{item.nomeResponsavel}"
                                                 action="#{GestaoNotasControle.irParaTelaClienteColaborador}"
                                                 oncomplete="#{GestaoNotasControle.onComplete}">
                                    <f:param name="state" value="AC"/>
                                </a4j:commandLink>
                            </rich:column>

                            <rich:column sortBy="#{item.nomeResponsavelEmissaoNota}" style="#{item.style}">
                                <f:facet name="header">
                                    <h:outputText value="#{msg_aplic.Nome_Responsavel_Emissao}"/>
                                </f:facet>
                                <h:outputText id="nomeResponsavelEmisaoNota" value="#{item.nomeResponsavelEmissaoNota}"/>
                            </rich:column>

                            <rich:column sortBy="#{item.cpf}" style="text-align: center;#{item.style}">
                                <f:facet name="header">
                                    <h:outputText value="CPF"/>
                                </f:facet>
                                <a4j:commandLink style="#{item.textStyle}" value="#{item.cpf}"
                                                 action="#{GestaoNotasControle.irParaTelaClienteColaborador}"
                                                 oncomplete="#{GestaoNotasControle.onComplete}">
                                    <f:param name="state" value="AC"/>
                                </a4j:commandLink>
                            </rich:column>

                            <rich:column sortBy="#{item.diaCompetencia}" style="#{item.style}"
                                         rendered="#{GestaoNotasControle.empresaLogado.mostrarNotaPorDiaCompetencia
                                         and (GestaoNotasControle.emissaoPorCompetencia or GestaoNotasControle.emissaoPorCompetenciaIndependenteQuitacao)}">
                                <f:facet name="header">
                                    <h:outputText value="#{msg_aplic.prt_PlanoRecorrencia_diaAnuidade}"
                                                  id="diaCompetenciaTitle"/>
                                </f:facet>
                                <h:outputText id="diaCompetencia" value="#{item.diaCompetencia}"/>
                                <rich:toolTip value="Dia da Competencia" for="diaCompetencia"/>
                            </rich:column>

                            <rich:column sortBy="#{item.movProdutoVO.descricao}" style="#{item.style}"
                                         rendered="#{GestaoNotasControle.emissaoPorFaturamento or GestaoNotasControle.emissaoPorCompetencia or GestaoNotasControle.emissaoPorCompetenciaIndependenteQuitacao}">
                                <f:facet name="header">
                                    <h:outputText value="#{msg_aplic.PRODUTO}"/>
                                </f:facet>
                                <h:outputText value="#{item.movProdutoVO.descricao}"/>
                            </rich:column>

                            <rich:column sortBy="#{item.descricaoProdutosPagos}" style="#{item.style}"
                                         rendered="#{GestaoNotasControle.emissaoPorFaturamentoRecebido}">
                                <f:facet name="header">
                                    <h:outputText value="#{msg_aplic.PRODUTO}"/>
                                </f:facet>
                                <h:outputText escape="false" value="#{item.descricaoProdutosPagos}"/>
                            </rich:column>

                            <rich:column sortBy="#{item.valor}" style="#{item.style}" styleClass="col-text-align-right"
                                         headerClass="col-text-align-right">
                                <f:facet name="header">
                                    <h:outputText value="#{msg_aplic.prt_valor_emitir}"/>
                                </f:facet>
                                <h:outputText value="#{item.valor_apresentar}"/>
                            </rich:column>

                            <rich:column sortBy="#{item.nrNotaManual}" style="#{item.style}"
                                         rendered="#{GestaoNotasControle.empresaVO.permiteGerarNotaManual}">
                                <f:facet name="header">
                                    <h:outputText value="#{msg_aplic.prt_nota_manual}"/>
                                </f:facet>
                                <h:outputText value="#{item.nrNotaManual}"/>
                            </rich:column>

                            <rich:column sortBy="#{item.dataReferenciaItem}" style="#{item.style}"
                                         rendered="#{!GestaoNotasControle.emissaoPorCompetencia and !GestaoNotasControle.emissaoPorCompetenciaIndependenteQuitacao}">
                                <f:facet name="header">
                                    <h:outputText value="Dt. Referência"/>
                                </f:facet>
                                <h:outputText value="#{item.dataReferenciaItem_Apresentar}"/>
                            </rich:column>

                            <rich:column sortBy="#{item.dataReferenciaItem}" style="#{item.style}"
                                         rendered="#{GestaoNotasControle.emissaoPorCompetencia or GestaoNotasControle.emissaoPorCompetenciaIndependenteQuitacao}">
                                <f:facet name="header">
                                    <h:outputText value="Mês Referência"/>
                                </f:facet>
                                <h:outputText value="#{item.dataMesReferenciaItem_Apresentar}"/>
                            </rich:column>

                            <rich:column sortBy="#{item.dataEmissao}" style="#{item.style}">
                                <f:facet name="header">
                                    <h:outputText value="Dt. Emissão"/>
                                </f:facet>
                                <h:outputText value="#{item.dataEmissao_Apresentar}"/>
                            </rich:column>

                            <rich:column sortBy="#{item.nfseemitida}" style="#{item.style}">
                                <f:facet name="header">
                                    <h:outputText value="#{msg_aplic.prt_nfse_emitida}"/>
                                </f:facet>
                                <h:panelGroup layout="block" style="display: inline-flex">
                                    <h:panelGroup layout="block"
                                                  rendered="#{item.emitidoDeOutraForma}"
                                                  style="margin-top: 1px;margin-right: 10px; width: 10px; height: 10px; background-color: orange"/>
                                    <h:panelGroup layout="block"
                                                  style="margin-top: 1px;margin-right: 10px; width: 10px; height: 10px; background-color:#{item.cor}"
                                                  id="teste"/>
                                    <h:outputText value="#{item.notaNFSEEmitida}"/>
                                </h:panelGroup>
                            </rich:column>

                            <rich:column sortBy="#{item.rps}" style="text-align: center;#{item.style}"
                                         rendered="#{GestaoNotasControle.existeNotaDelphi}">
                                <f:facet name="header">
                                    <h:outputText value="Lote"/>
                                </f:facet>
                                <h:outputText value="#{item.rps}"/>
                            </rich:column>

                            <rich:column sortBy="#{item.notaFiscalVO.statusNotaApresentar}"
                                         style="#{item.style}"
                                         rendered="#{GestaoNotasControle.empresaVO.usaEnotas}">
                                <f:facet name="header">
                                    <h:outputText value="Status"/>
                                </f:facet>
                                <h:outputText value="#{item.notaFiscalVO.statusNotaApresentar}"
                                              styleClass="tooltipster"
                                              title="#{item.notaFiscalVO.statusNotaHint}"/>
                            </rich:column>

                            <rich:column style="text-align: center;#{item.style}">
                                <f:facet name="header">
                                    <h:panelGroup layout="block" style="display: inline-flex;">
                                        <h:outputText value="#{msg_aplic.ENVIAR}"
                                                      styleClass="tooltipster"
                                                      title="Marcar todos os itens de todas as páginas."/>

                                        <h:selectBooleanCheckbox value="#{GestaoNotasControle.marcarTodos}"
                                                                 style="margin-left: 5px"
                                                                 styleClass="tooltipster"
                                                                 title="Marcar todos os itens de todas as páginas."
                                                                 id="marcarTodos">
                                            <a4j:support event="onchange"
                                                         action="#{GestaoNotasControle.acaoMarcarTodos}"
                                                         reRender="totalizadores,mensagem,tblGestaNotas"/>
                                        </h:selectBooleanCheckbox>
                                    </h:panelGroup>
                                </f:facet>
                                <h:selectBooleanCheckbox rendered="#{item.nfseemitida}" disabled="true" id="notaEnviada"
                                                         value="#{item.selecionado}"/>
                                <h:selectBooleanCheckbox rendered="#{!item.nfseemitida}" value="#{item.selecionado}"
                                                         id="notaNaoEnviada">
                                    <a4j:support event="onchange"
                                                 action="#{GestaoNotasControle.calcularValorSelecionado}"
                                                 reRender="totalizadores,mensagem"/>
                                </h:selectBooleanCheckbox>
                            </rich:column>

                            <rich:column style="#{item.style}">
                                <f:facet name="header">
                                    <h:outputText value="#{msg_aplic.RETORNO}"/>
                                </f:facet>
                                <h:outputText id="retorno" value="#{item.retorno}"/>
                            </rich:column>

                            <rich:column style="text-align: center;#{item.style}">

                                <f:facet name="header">
                                    <h:panelGroup layout="block" style="display: inline-flex;">
                                        <h:outputText value="Desvincular"
                                                      styleClass="tooltipster"
                                                      title="Marcar todos os itens que podem ser excluidos."/>

                                        <h:selectBooleanCheckbox value="#{GestaoNotasControle.marcarTodosExcluir}"
                                                                 rendered="#{LoginControle.permissaoAcessoMenuVO.permiteExcluirNotaFiscal}"
                                                                 style="margin-left: 5px"
                                                                 styleClass="tooltipster"
                                                                 title="Marcar todos os itens que podem ser excluidos.">
                                            <a4j:support event="onchange"
                                                         action="#{GestaoNotasControle.acaoMarcarTodosExcluir}"
                                                         reRender="tblGestaNotas"/>
                                        </h:selectBooleanCheckbox>

                                    </h:panelGroup>
                                </f:facet>

                                <a4j:commandLink action="#{GestaoNotasControle.desvincularNota}"
                                                 oncomplete="#{GestaoNotasControle.mensagemNotificar}"
                                                 rendered="#{item.apresentarDesvincular}"
                                                 value="Desvincular"
                                                 reRender="totalizadores,mensagem,tblGestaNotas"/>

                                <h:selectBooleanCheckbox styleClass="tooltipster"
                                                         title="Selecionar para excluir"
                                                         rendered="#{item.apresentarExcluirNotaFiscalNFSe && LoginControle.permissaoAcessoMenuVO.permiteExcluirNotaFiscal}"
                                                         value="#{item.selecionadoExcluir}">
                                </h:selectBooleanCheckbox>
                            </rich:column>
                        </rich:dataTable>
                        <rich:datascroller for="tblGestaNotas" maxPages="20" page="#{GestaoNotasControle.scrollerPage}"
                                           id="sc2"
                                           oncomplete="carregarTooltipsterGestaoNotas();"/>
                        <script>
                            carregarTooltipsterGestaoNotas();
                        </script>
                    </h:panelGroup>
                </h:panelGrid>

                <h:panelGroup layout="block" style="margin-top: 20px; text-align: center; margin-bottom: 10px;"
                              id="operacoes"
                              rendered="#{!GestaoNotasControle.permiteGerarLoteRPS}">
                    <h:panelGroup
                            rendered="#{!GestaoNotasControle.loadEnviandoNotas}">

                        <a4j:commandLink style="padding-right: 12px" action="#{GestaoNotasControle.enviarNotas}"
                                         styleClass="pure-button pure-button-primary tooltipster"
                                         title="Enviar notas selecionadas"
                                         status="false"
                                         reRender="dtableGestaoNotas,totalizadores,mensagem,formMdlEnviandoNotas"
                                         onclick="fireElementFromAnyParent('form:btnAtualizaTempo');Richfaces.showModalPanel('modalLoadEnviandoNotas');"
                                         oncomplete="#{GestaoNotasControle.mensagemNotificar};Richfaces.hideModalPanel('modalLoadEnviandoNotas')">
                            <i class="fa-icon-cloud-upload"></i> &nbsp ${msg_aplic.prt_enviar_selecionados}
                        </a4j:commandLink>
                        <a4j:commandLink action="#{GestaoNotasControle.excluirNotasFiscais}"
                                         rendered="#{LoginControle.permissaoAcessoMenuVO.permiteExcluirNotaFiscal}"
                                         reRender="form" style="margin-left: 10px;"
                                         styleClass="pure-button pure-button-primary tooltipster"
                                         oncomplete="#{GestaoNotasControle.mensagemNotificar}"
                                         title="Desvincular notas selecionadas">
                            <i class="fa-icon-ban-circle"></i> &nbsp ${msg_aplic.prt_desvincular_selecionados}
                        </a4j:commandLink>
                        <a4j:commandLink action="#{GestaoNotasControle.realizarConsultaLogObjetoSelecionado}"
                                         reRender="form" style="margin-left: 12px"
                                         styleClass="pure-button pure-button-secundary tooltipster"
                                         oncomplete="abrirPopup('visualizadorLogForm.jsp', 'VisualizadorLog', 785, 595);"
                                         title="#{msg_aplic.prt_visualizar_log}">
                            <i class="fa-icon-list"></i> &nbsp ${msg_aplic.LOG}
                        </a4j:commandLink>
                    </h:panelGroup>
                <h:panelGroup
                        rendered="#{GestaoNotasControle.loadEnviandoNotas}">
                    <h:panelGrid  columns="1">
                        <h:outputText id="msgProcessando" styleClass="mensagemDetalhada"  value="Uma solicitação desse serviço está sendo processada. Dentro de alguns instantes, atualize a página para verificar se operação já foi concluída"/>
                        <rich:spacer height="7"/>
                        <a4j:commandLink id="atualizar" title="Atualizar" onclick="window.location.reload();" styleClass="pure-button pure-button-primary">
                            <i class="fa-icon-refresh"></i>&nbsp;Atualizar
                        </a4j:commandLink>
                    </h:panelGrid>
                </h:panelGroup>

                    <h:panelGrid id="tabelaLegenda" columns="2" width="100%" columnClasses="w50, w50"
                                 style="margin-top: 15px">

                        <rich:dataTable value="#{GestaoNotasControle.situacaoNFSe}" var="situacao" width="100%">
                            <f:facet name="header">
                                <h:outputText value="Legenda"/>
                            </f:facet>

                            <rich:column style="background-color:#{situacao.classe}">
                                <h:panelGroup layout="block"
                                              style="border:none; width: 10px; height: 10px; background-color:#{situacao.cor}"/>
                            </rich:column>

                            <rich:column>
                                <h:outputText value="#{situacao.descricao}"/>
                            </rich:column>

                            <rich:column>
                                <h:outputText value="#{situacao.hint}"/>
                            </rich:column>
                        </rich:dataTable>

                        <h:panelGroup layout="block" style="padding-left: 10px" id="panelTotalizadoresModuloNFSe">

                            <rich:dataTable value="#{GestaoNotasControle.totalizadoresModuloNFSe}" width="100%"
                                            var="totalizador">

                                <f:facet name="header">
                                    <h:panelGroup layout="block">
                                        <h:outputText value="Resumo Módulo NFSe" style="padding-right: 5px"/>

                                        <rich:calendar id="dataConsultaTotalizadorNFSe"
                                                       value="#{GestaoNotasControle.dataConsultaTotalizadorNFSe}"
                                                       inputSize="8"
                                                       inputClass="form MMyyyy"
                                                       oninputblur="blurinput(this);"
                                                       oninputfocus="focusinput(this);"
                                                       datePattern="MM/yyyy"
                                                       zindex="2"
                                                       showWeeksBar="true">
                                            <a4j:support event="onchanged"
                                                         action="#{GestaoNotasControle.consultarTotalizadoresNFSe}"
                                                         reRender="panelTotalizadoresModuloNFSe, mensagem"/>
                                        </rich:calendar>
                                    </h:panelGroup>
                                </f:facet>

                                <rich:column>
                                    <f:facet name="header">
                                        <h:outputText value="Status"/>
                                    </f:facet>
                                    <h:outputText value="#{totalizador.nome}"/>
                                </rich:column>
                                <rich:column style="text-align: right">
                                    <f:facet name="header">
                                        <h:outputText value="Qtd Notas"/>
                                    </f:facet>
                                    <h:outputText value="#{totalizador.quantidade}"/>
                                </rich:column>
                                <rich:column style="text-align: right">
                                    <f:facet name="header">
                                        <h:outputText value="#{msg_aplic.prt_sifrao_dinheiro}"/>
                                    </f:facet>
                                    <h:outputText value="#{totalizador.valor_apresentar}"/>
                                </rich:column>
                            </rich:dataTable>
                        </h:panelGroup>

                    </h:panelGrid>
                </h:panelGroup>

                <h:panelGroup layout="block" id="btnGerarArquivo" style="padding-top: 10px"
                              rendered="#{GestaoNotasControle.permiteGerarLoteRPSSaoPaulo}">

                    <a4j:commandLink action="#{GestaoNotasControle.gerarArquivoLoteRPS}"
                                     styleClass="pure-button pure-button-small pure-button-primary"
                                     oncomplete="#{GestaoNotasControle.onComplete}"
                                     reRender="form"
                                     value="#{msg_aplic.prt_gerar_xml_selecionadas}">
                    </a4j:commandLink>

                    <a4j:commandLink style="margin-left: 12px;"
                                     action="#{GestaoNotasControle.gerarArquivoLoteRPSTodas}"
                                     styleClass="pure-button pure-button-small"
                                     oncomplete="#{GestaoNotasControle.onComplete}"
                                     reRender="form"
                                     value="#{msg_aplic.prt_gerar_xml_todas}">
                    </a4j:commandLink>

                    <a4j:commandLink action="#{GestaoNotasControle.realizarConsultaLogObjetoSelecionado}"
                                     reRender="form" style="margin-left: 12px; margin-top: 12px"
                                     styleClass="pure-button pure-button-small"
                                     oncomplete="abrirPopup('visualizadorLogForm.jsp', 'VisualizadorLog', 785, 595);"
                                     title="Visualizar Log">
                        <i class="fa-icon-list"></i> &nbsp ${msg_aplic.LOG}
                    </a4j:commandLink>
                </h:panelGroup>
                <h:panelGroup layout="block" id="btnGerarArquivoMaraba" style="padding-top: 10px"
                              rendered="#{GestaoNotasControle.permiteGerarLoteRPSMaraba}">

                    <a4j:commandLink action="#{GestaoNotasControle.gerarArquivoLoteRPSMaraba}"
                                     styleClass="pure-button pure-button-small pure-button-primary"
                                     oncomplete="#{GestaoNotasControle.onComplete}"
                                     reRender="form"
                                     value="#{msg_aplic.prt_gerar_xml_selecionadas}">
                    </a4j:commandLink>

                    <a4j:commandLink style="margin-left: 12px;"
                                     action="#{GestaoNotasControle.gerarArquivoLoteRPSTodasMaraba}"
                                     styleClass="pure-button pure-button-small"
                                     oncomplete="#{GestaoNotasControle.onComplete}"
                                     reRender="form"
                                     value="#{msg_aplic.prt_gerar_xml_todas}">
                    </a4j:commandLink>

                    <a4j:commandLink action="#{GestaoNotasControle.realizarConsultaLogObjetoSelecionado}"
                                     reRender="form" style="margin-left: 12px; margin-top: 12px"
                                     styleClass="pure-button pure-button-small"
                                     oncomplete="abrirPopup('visualizadorLogForm.jsp', 'VisualizadorLog', 785, 595);"
                                     title="Visualizar Log">
                        <i class="fa-icon-list"></i> &nbsp ${msg_aplic.LOG}
                    </a4j:commandLink>
                </h:panelGroup>
            </rich:tab>

            <rich:tab id="abaFamilia" label="Nota em Grupo"
                      rendered="#{!GestaoNotasControle.permiteGerarLoteRPS}">
                <h:panelGrid columns="2" rowClasses="linhaImpar, linhaPar" style="margin-top: 10px"
                             columnClasses="classEsquerda, classDireita" width="100%">

                    <h:panelGroup rendered="#{LoginControle.usuario.administrador}">
                        <h:outputText styleClass="titulo3" value="#{msg_aplic.EMPRESA}:"/>
                    </h:panelGroup>

                    <h:panelGroup rendered="#{LoginControle.usuario.administrador}">
                        <h:selectOneMenu id="comboEmpresaFamilia" styleClass="form" onblur="blurinput(this);"
                                         onfocus="focusinput(this);" value="#{GestaoNotasControle.empresaVO.codigo}">
                            <f:selectItems value="#{GestaoNotasControle.listaEmpresas}"/>

                            <a4j:support event="onchange" reRender="form"
                                         action="#{GestaoNotasControle.consultarDadosEmpresa}"></a4j:support>
                        </h:selectOneMenu>
                    </h:panelGroup>

                    <h:outputText styleClass="tituloCampos" value="Configuração de Emissão:"/>
                    <h:selectOneMenu id="configNotaFiscalFamilia" styleClass="form" onblur="blurinput(this);"
                                     onfocus="focusinput(this);"
                                     value="#{GestaoNotasControle.configNotaFiscalFamilia.codigo}">
                        <f:selectItems value="#{GestaoNotasControle.listaConfiguracaoNotaFamilia}"/>
                    </h:selectOneMenu>

                    <h:outputText styleClass="tituloCampos" rendered="#{GestaoNotasControle.emissaoPorCompetencia or GestaoNotasControle.emissaoPorCompetenciaIndependenteQuitacao}"
                                  value="#{msg_aplic.prt_mes_competencia}"/>
                    <h:outputText styleClass="tituloCampos" rendered="#{!GestaoNotasControle.emissaoPorCompetencia and !GestaoNotasControle.emissaoPorCompetenciaIndependenteQuitacao}"
                                  value="#{GestaoNotasControle.labelPeriodo}"/>
                    <h:panelGroup>
                        <h:panelGroup>
                            <h:panelGroup id="pgdataIniFamilia"
                                          rendered="#{!GestaoNotasControle.emissaoPorCompetencia and !GestaoNotasControle.emissaoPorCompetenciaIndependenteQuitacao}">
                                <rich:calendar id="dataIniFamilia"
                                               value="#{GestaoNotasControle.dataInicio}"
                                               inputSize="10"
                                               inputClass="form"
                                               oninputblur="blurinput(this);"
                                               oninputfocus="focusinput(this);"
                                               oninputchange="return validar_Data(this.id)"
                                               datePattern="dd/MM/yyyy"
                                               enableManualInput="true"
                                               zindex="5"
                                               showWeeksBar="false">
                                </rich:calendar>
                                <rich:jQuery id="mskDataInicioFamilia" selector=".rich-calendar-input" timing="onload"
                                             query="mask('99/99/9999')"/>
                                <h:message for="dataIniFamilia" styleClass="mensagemDetalhada"/>
                            </h:panelGroup>

                            <h:panelGroup id="pgMesCompetenciaFamilia"
                                          rendered="#{GestaoNotasControle.emissaoPorCompetencia or GestaoNotasControle.emissaoPorCompetenciaIndependenteQuitacao}">
                                <rich:calendar id="dataCompetenciaFamilia"
                                               value="#{GestaoNotasControle.dataInicio}"
                                               inputSize="8"
                                               inputClass="form MMyyyy"
                                               oninputblur="blurinput(this);"
                                               oninputfocus="focusinput(this);"
                                               datePattern="MM/yyyy"
                                               enableManualInput="true"
                                               zindex="5"
                                               showWeeksBar="true"/>
                                <h:message for="dataCompetenciaFamilia" styleClass="mensagemDetalhada"/>
                            </h:panelGroup>


                        </h:panelGroup>


                        <h:outputText styleClass="tituloCampos" rendered="#{!GestaoNotasControle.emissaoPorCompetencia and !GestaoNotasControle.emissaoPorCompetenciaIndependenteQuitacao}"
                                      style="vertical-align: middle" value=" #{msg_aplic.prt_ate} "/>
                        <h:panelGroup rendered="#{!GestaoNotasControle.emissaoPorCompetencia and !GestaoNotasControle.emissaoPorCompetenciaIndependenteQuitacao}">
                            <rich:calendar id="dataFimFamilia"
                                           value="#{GestaoNotasControle.dataFim}"
                                           inputSize="10"
                                           inputClass="form"
                                           oninputblur="blurinput(this);"
                                           oninputfocus="focusinput(this);"
                                           oninputchange="return validar_Data(this.id)"
                                           datePattern="dd/MM/yyyy"
                                           enableManualInput="true"
                                           zindex="5"
                                           showWeeksBar="false">
                            </rich:calendar>
                            <h:message for="dataFimFamilia" styleClass="mensagemDetalhada"/>
                        </h:panelGroup>

                    </h:panelGroup>

                    <h:outputText styleClass="tituloCampos"
                                  value="#{msg_aplic.prt_forma_pagamento} "/>
                    <h:panelGroup>
                        <h:selectOneMenu value="#{GestaoNotasControle.formaPagamentoSelecionadoFamilia}">
                            <f:selectItems value="#{GestaoNotasControle.selectItemsFormaDePagamento}"/>
                        </h:selectOneMenu>
                    </h:panelGroup>

                    <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_aluno_familia}"/>
                    <h:panelGroup>
                        <h:inputText id="nomeClienteFamilia" size="50"
                                     maxlength="50" onblur="blurinput(this);" onfocus="focusinput(this);"/>

                        <rich:suggestionbox height="200" width="400"
                                            for="nomeClienteFamilia"
                                            fetchValue="#{result.pessoa.nome}"
                                            suggestionAction="#{GestaoNotasControle.executarAutocompleteConsultaClienteFamilia}"
                                            minChars="1" rowClasses="20"
                                            title="#{msg_aplic.msg_inf_nome_alun_titu_ou_depend}"
                                            status="statusHora"
                                            nothingLabel="#{msg_aplic.msg_nao_poss_encontrar_cliente}"
                                            var="result" id="suggestionNomeClienteFamilia" reRender="mensagem">
                            <a4j:support event="onselect"
                                         action="#{GestaoNotasControle.selecionarClienteSuggestionBoxFamilia}"/>
                            <h:column>
                                <h:outputText value="#{result.pessoa.nome}"/>
                            </h:column>
                        </rich:suggestionbox>
                        <a4j:commandButton id="limparAlunoFamilia"
                                           onclick="document.getElementById('form:nomeClienteFamilia').value = null;"
                                           image="/images/limpar.gif" title="Limpar aluno."
                                           status="false"
                                           action="#{GestaoNotasControle.limparAlunoFamilia}"/>
                    </h:panelGroup>
                    <h:outputText/>
                    <h:panelGroup>
                        <h:panelGrid columns="1" width="100%">
                            <h:panelGroup rendered="#{!GestaoNotasControle.loadEnviandoNotas}">
                                <a4j:commandLink action="#{GestaoNotasControle.consultarItensFamilia}"
                                                 reRender="form"
                                                 onclick="fireElementFromAnyParent('form:btnAtualizaTempo')"
                                                 oncomplete="#{GestaoNotasControle.mensagemNotificar};carregarTooltipsterGestaoNotas();"
                                                 styleClass="pure-button pure-button-small pure-button-primary ">
                                    <i class="fa fa-check"></i> &nbsp ${msg_aplic.CONSULTAR}
                                </a4j:commandLink>

                                <a4j:commandLink style="margin-left: 12px"
                                                 onclick="fireElementFromAnyParent('form:btnAtualizaTempo');if (!confirm('Confirma envio de todas as notas?')){return false;}"
                                                 action="#{GestaoNotasControle.enviarTodasNotas}"
                                                 title="Enviar todas as notas"
                                                 oncomplete="#{GestaoNotasControle.mensagemNotificar};carregarTooltipsterGestaoNotas();"
                                                 styleClass="pure-button pure-button-small tooltipster"
                                                 reRender="form">
                                    <i class="fa fa-cloud-upload"></i> &nbsp ${msg_aplic.prt_enviar_todas}
                                </a4j:commandLink>
                            </h:panelGroup>
                            <h:panelGroup
                                    rendered="#{GestaoNotasControle.loadEnviandoNotas}">
                                <h:panelGrid  columns="1">
                                    <h:outputText id="msgProcessando1" styleClass="mensagemDetalhada"  value="Uma solicitação desse serviço está sendo processada. Dentro de alguns instantes, atualize a página para verificar se operação já foi concluída"/>
                                    <rich:spacer height="7"/>
                                    <a4j:commandLink id="atualizar1" title="Atualizar" onclick="window.location.reload();" styleClass="pure-button pure-button-primary">
                                        <i class="fa-icon-refresh"></i>&nbsp;Atualizar
                                    </a4j:commandLink>
                                </h:panelGrid>
                            </h:panelGroup>

                        </h:panelGrid>
                    </h:panelGroup>
                </h:panelGrid>
                <h:panelGrid id="dtableGestaoNotasFamilia" columns="1" width="100%" style="text-align: center"
                             cellpadding="0"
                             cellspacing="0"
                             styleClass="centralizado">
                    <h:panelGrid width="100%" style="text-align: right">
                        <h:panelGroup layout="block" style="margin-top: 10px">
                            <a4j:commandButton id="exportarExcelFam"
                                               image="/imagens/btn_excel.png"
                                               style="margin-left: 8px;"
                                               actionListener="#{ExportadorListaControle.exportar}"
                                               rendered="#{not empty GestaoNotasControle.listaItensApresentar}"
                                               value="Excel"
                                               oncomplete="abrirPopup('UpdateServlet?op=downloadfile&file=#{ExportadorListaControle.fileName}&mimetype=application/vnd.ms-excel','GestaoNotas', 800,200);#{ExportadorListaControle.msgAlert}"
                                               accesskey="2" styleClass="botoes">
                                <f:attribute name="lista" value="#{GestaoNotasControle.listaFamilia}"/>
                                <f:attribute name="tipo" value="xls"/>
                                <f:attribute name="atributos"
                                             value="nome=Nome,matricula=Matrícula,cpf=CPF,valor_apresentar=Valor a Emitir,nfseemitida=Item Emitido"/>
                                <f:attribute name="prefixo" value="GestaoNotas"/>
                            </a4j:commandButton>
                            <%--BOTÃO PDF--%>
                            <a4j:commandButton id="exportarPdfFm"
                                               style="margin-left: 8px;"
                                               image="/imagens/imprimir.png"
                                               actionListener="#{ExportadorListaControle.exportar}"
                                               rendered="#{not empty GestaoNotasControle.listaItensApresentar}"
                                               value="PDF"
                                               oncomplete="abrirPopup('UpdateServlet?op=downloadfile&file=#{ExportadorListaControle.fileName}&mimetype=application/pdf','GestaoNotas', 800,200);#{ExportadorListaControle.msgAlert}"
                                               accesskey="2" styleClass="botoes">
                                <f:attribute name="lista" value="#{GestaoNotasControle.listaFamilia}"/>
                                <f:attribute name="tipo" value="pdf"/>
                                <f:attribute name="atributos"
                                             value="nome=Nome,matricula=Matrícula,cpf=CPF,valor_apresentar=Valor a Emitir,nfseemitida=Item Emitido"/>
                                <f:attribute name="prefixo" value="GestaoNotas"/>
                            </a4j:commandButton>
                        </h:panelGroup>
                    </h:panelGrid>

                    <h:panelGroup layout="block" style="margin-top: 12px; padding-left: 12px; padding-right: 12px">
                        <script type="text/javascript">
                            jQuery('.accordionPanels .accordionPanel:even > .accordionHeader').css({
                                background: '#eee',
                                color: '#fff'
                            });
                            var allPanels = jQuery('.accordionPanels  .accordionPanel  .accordionBodyMark').hide();
                            var allButtonsExpand = jQuery('.accordionPanels  .accordionPanel  .accordionHeader  .btnExpand');

                            // openFirstPanel();
                            jQuery('.accordionPanels > .accordionPanel > .accordionHeader  .btnExpand').click(function () {
                                $this = jQuery(this);
                                var indicie = $this.attr('lang');
                                target = jQuery('.acBody' + indicie);
                                //   alert(target);
                                if (target.hasClass('active')) {
                                    target.removeClass('active').slideUp('fast');
                                    $this.removeClass('fa-caret-up');
                                    $this.addClass('fa-caret-down');
                                    jQuery('.accordionPanel').removeClass('divComBorda');
                                } else {
                                    allPanels.removeClass('active').slideUp('fast');
                                    target.addClass('active').slideDown('fast');
                                    allButtonsExpand.removeClass('fa-caret-up');
                                    allButtonsExpand.addClass('fa-caret-down');
                                    $this.removeClass('fa-caret-down');
                                    $this.addClass('fa-caret-up');
                                    jQuery('.accordionPanel').removeClass('divComBorda');
                                    jQuery('.accordion' + indicie).addClass('divComBorda');
                                }

                                return false;
                            });
                            // jQuery('.accordionPanels > .accordionPanel > .accordionHeader  .checkFamilia').click(function () {
                            //     $this = jQuery(this);
                            //     var indicie = $this.attr('lang');
                            //     //  alert( $this.attr('checked')) ;
                            //     var valor = $this.attr('checked');
                            //     jQuery(".accordionPanels > .accordion" + indicie + " > .accordionBodyMark  .checkDependente").attr("checked", valor);
                            // });

                            carregarTooltipsterGestaoNotas();
                        </script>

                        <h:panelGroup layout="block" styleClass="accordionPanels pure-g-r pure-u-11-12 margin-0-auto">
                            <h:panelGroup rendered="#{not empty GestaoNotasControle.listaFamilia}" layout="block"
                                          style="width: 100%">

                                <h:panelGroup layout="block" styleClass="accordionHeader"
                                              style="background-color: #fff;height: 20px">
                                    <h:panelGroup layout="block"
                                                  style="vertical-align:vertical-align: -webkit-baseline-middle;height: 60%;">

                                        <h:panelGroup layout="block"
                                                      style="width:10%;display: inline-block;vertical-align: -webkit-baseline-middle;">

                                        </h:panelGroup>
                                        <h:panelGroup layout="block" styleClass="textsmall"
                                                      style="width:80%;display: inline-block;vertical-align: -webkit-baseline-middle;">
                                            <h:panelGroup layout="block" styleClass="textsmall" style="width: 100%">
                                                <h:panelGroup layout="block" style="width: 10%;display: inline-block">
                                                    <h:outputText styleClass="textsmall"
                                                                  value="#{msg_aplic.MATRICULA}"/>
                                                </h:panelGroup>
                                                <h:panelGroup layout="block" style="width: 20%;display: inline-block">
                                                    <h:outputText styleClass="textsmall" value="#{msg_aplic.NOME}"/>
                                                </h:panelGroup>
                                                <h:panelGroup layout="block" style="width: 10%;display: inline-block">
                                                    <h:outputText styleClass="textsmall" value="CNPJ"/>
                                                </h:panelGroup>
                                                <h:panelGroup layout="block" style="width: 15%;display: inline-block">
                                                    <h:outputText styleClass="textsmall" value="#{msg_aplic.VALOR}"/>
                                                </h:panelGroup>
                                                <h:panelGroup layout="block" style="width: 5%;display: inline-block">
                                                    <h:outputText styleClass="textsmall" value="#{msg_aplic.EMITIDA}"/>
                                                </h:panelGroup>
                                                <h:panelGroup layout="block" style="width: 20%;display: inline-block">
                                                    <h:outputText styleClass="textsmall" value="#{msg_aplic.RETORNO}"/>
                                                </h:panelGroup>
                                                <h:panelGroup layout="block" style="width: 10%;display: inline-block"
                                                              rendered="#{GestaoNotasControle.empresaVO.usaEnotas}">
                                                    <h:outputText styleClass="textsmall" value="#{msg_aplic.STATUS}"/>
                                                </h:panelGroup>
                                                <h:panelGroup layout="block" style="width: 10%;display: inline-block"
                                                              rendered="#{GestaoNotasControle.empresaVO.usaEnotas}">
                                                    <h:outputText styleClass="textsmall" value="Ações"/>
                                                </h:panelGroup>
                                            </h:panelGroup>
                                        </h:panelGroup>
                                        <h:panelGroup layout="block" styleClass="textsmall"
                                                      style="width:10%;display: inline-block;vertical-align: sub;">

                                        </h:panelGroup>
                                    </h:panelGroup>
                                </h:panelGroup>

                                <h:panelGroup layout="block" style="width:100%; padding: 0"
                                              styleClass="accordionBody acBody#{index}">
                                    <h:panelGroup layout="block" style="width: 80%;margin:0 auto;">
                                        <rich:dataTable styleClass="colapseTable"
                                                        value="#{reciboFamilia.reciboPagamentoVOs}"
                                                        columnClasses="colunaCentralizada,classDireita,classEsquerda"
                                                        width="100%"
                                                        var="totalizador">
                                            <rich:column width="1%">
                                                <h:selectBooleanCheckbox id="chkSelFamilia"
                                                                         value="#{totalizador.selecionado}">
                                                </h:selectBooleanCheckbox>
                                            </rich:column>
                                            <rich:column>
                                                <h:outputText value="#{totalizador.nome}"/>
                                            </rich:column>
                                            <rich:column>
                                                <h:outputText value="#{totalizador.movProdutoVO.produto.descricao}"/>
                                            </rich:column>
                                            <rich:column width="10%">
                                                <h:outputText value="#{totalizador.valor_apresentar}"/>
                                            </rich:column>
                                        </rich:dataTable>
                                    </h:panelGroup>
                                </h:panelGroup>

                            </h:panelGroup>
                            <a4j:repeat rowKeyVar="index" var="reciboFamilia"
                                        value="#{GestaoNotasControle.listaFamilia}">
                                <h:panelGroup layout="block" style="width: 100%"
                                              styleClass="accordionPanel accordion#{index}">

                                    <h:panelGroup layout="block" styleClass="accordionHeader"
                                                  style="#{reciboFamilia.style}height: 30px;padding-bottom: 4px">
                                        <h:panelGroup layout="block"
                                                      style="vertical-align:vertical-align: -webkit-baseline-middle;height: 60%;">

                                            <h:panelGroup layout="block"
                                                          style="width:10%;display: inline-block;vertical-align: -webkit-baseline-middle;">
                                                <h:selectBooleanCheckbox disabled="#{reciboFamilia.nfseemitida}"
                                                                         styleClass="checkFamilia"
                                                                         value="#{reciboFamilia.selecionado}"
                                                                         lang="#{index}">
                                                    <a4j:support event="onchange"
                                                                 actionListener="#{GestaoNotasControle.selecionarItensNotaFiscalFamilia}"
                                                                 action="#{GestaoNotasControle.calcularValorSelecionadoFamilia}"
                                                                 reRender="totalizadores,mensagem,form:dtableGestaoNotasFamilia"/>
                                                </h:selectBooleanCheckbox>
                                            </h:panelGroup>
                                            <h:panelGroup layout="block" styleClass="textsmall"
                                                          style="width:80%;display: inline-block;vertical-align: -webkit-baseline-middle;">
                                                <h:panelGroup layout="block" styleClass="textsmall" style="width: 100%">
                                                    <h:panelGroup layout="block"
                                                                  style="width: 10%;display: inline-block">
                                                        <h:outputText
                                                                value="#{reciboFamilia.clientePagador.matricula}"/>
                                                    </h:panelGroup>
                                                    <h:panelGroup layout="block"
                                                                  style="width: 20%;display: inline-block">
                                                        <h:outputText
                                                                value="#{reciboFamilia.clientePagador.nome_Apresentar}"/>
                                                    </h:panelGroup>
                                                    <h:panelGroup layout="block"
                                                                  style="width: 10%;display: inline-block">
                                                        <h:outputText
                                                                value="#{reciboFamilia.clientePagador.pessoa.cnpj}"/>
                                                    </h:panelGroup>
                                                    <h:panelGroup layout="block"
                                                                  style="width: 15%;display: inline-block;">
                                                        <h:outputText value="#{reciboFamilia.valor_apresentar}"/>
                                                    </h:panelGroup>
                                                    <h:panelGroup layout="block"
                                                                  style="width: 5%;display: inline-block">
                                                        <h:outputText value="#{reciboFamilia.notaNFSEEmitida}"/>
                                                    </h:panelGroup>
                                                    <h:panelGroup layout="block"
                                                                  style="width: 20%;display: inline-block;font-size: 10px;vertical-align: middle">
                                                        <h:outputText value="#{reciboFamilia.retorno}"/>
                                                    </h:panelGroup>

                                                    <h:panelGroup layout="block"
                                                                  rendered="#{GestaoNotasControle.empresaVO.usaEnotas}"
                                                                  style="width: 10%;display: inline-block;vertical-align: middle">
                                                        <h:outputText
                                                                value="#{reciboFamilia.notaFiscalVO.statusNotaApresentar}"
                                                                styleClass="tooltipster"
                                                                title="#{reciboFamilia.notaFiscalVO.statusNotaHint}"/>
                                                    </h:panelGroup>

                                                    <h:panelGroup layout="block"
                                                                  rendered="#{GestaoNotasControle.empresaVO.usaEnotas}"
                                                                  style="width: 10%;display: inline-block;font-size: 10px;vertical-align: middle">
                                                        <a4j:commandLink action="#{GestaoNotasControle.excluirNotaFiscalFamilia}"
                                                                         oncomplete="#{GestaoNotasControle.mensagemNotificar}"
                                                                         rendered="#{reciboFamilia.apresentarExcluirNotaFiscalNFSeFamilia && LoginControle.permissaoAcessoMenuVO.permiteExcluirNotaFiscal}"
                                                                         value="Excluir"
                                                                         reRender="form:dtableGestaoNotasFamilia"/>
                                                    </h:panelGroup>
                                                </h:panelGroup>

                                            </h:panelGroup>
                                            <h:panelGroup layout="block" styleClass="textsmall"
                                                          style="width:10%;display: inline-block;vertical-align: sub;">
                                                <h:panelGroup layout="block">
                                                    <h:outputText styleClass="btnExpand fa-icon-caret-down"
                                                                  style="margin-top: 5px;font-size:19px;display: inline-block;cursor:pointer;"
                                                                  lang="#{index}">
                                                    </h:outputText></h:panelGroup>
                                            </h:panelGroup>
                                        </h:panelGroup>
                                    </h:panelGroup>

                                    <h:panelGroup layout="block" style="width:100%;padding-top: 5px"
                                                  styleClass="accordionBodyMark acBody#{index}">
                                        <h:panelGroup layout="block" style="width: 50%;margin:0 auto;">
                                            <rich:dataTable styleClass="colapseTable"
                                                            value="#{reciboFamilia.reciboPagamentoVOs}" width="100%"
                                                            var="totalizador">
                                                <rich:column style="text-align: left">
                                                    <h:selectBooleanCheckbox disabled="#{totalizador.nfseemitida}"
                                                                             styleClass="checkDependente"
                                                                             value="#{totalizador.selecionado}">
                                                        <a4j:support event="onchange"
                                                                     action="#{GestaoNotasControle.calcularValorSelecionadoFamilia}"
                                                                     reRender="totalizadores"/>
                                                    </h:selectBooleanCheckbox>
                                                </rich:column>
                                                <rich:column style="text-align: left" width="70%">
                                                    <h:outputText value="#{totalizador.nome}"/>
                                                </rich:column>
                                                <rich:column style="text-align: right">
                                                    <h:outputText value="#{totalizador.valor_apresentar}"/>
                                                </rich:column>
                                            </rich:dataTable>
                                        </h:panelGroup>
                                    </h:panelGroup>

                                </h:panelGroup>
                            </a4j:repeat>
                        </h:panelGroup>
                        <h:panelGrid columns="2" style="margin-top: 10px"
                                     columnClasses="classEsquerda, classDireita" width="100%">
                            <h:outputText/>
                            <h:panelGroup layout="block">

                                <a4j:commandLink style="padding-right: 12px"
                                                 action="#{GestaoNotasControle.enviarNotasFamilia}"
                                                 onclick="fireElementFromAnyParent('form:btnAtualizaTempo')"
                                                 oncomplete="#{GestaoNotasControle.mensagemNotificar};carregarTooltipsterGestaoNotas();"
                                                 styleClass="pure-button pure-button-small pure-button-primary"
                                                 reRender="form">
                                    <i class="fa fa-cloud-upload"></i> &nbsp ${msg_aplic.prt_enviar_selecionados}
                                </a4j:commandLink>
                                <a4j:commandLink action="#{GestaoNotasControle.realizarConsultaLogObjetoSelecionado}"
                                                 reRender="form" style="margin-left: 12px; margin-top: 12px"
                                                 styleClass="pure-button pure-button-small"
                                                 oncomplete="abrirPopup('visualizadorLogForm.jsp', 'VisualizadorLog', 785, 595);"
                                                 title="#{msg_aplic.prt_visualizar_log}">
                                    <i class="fa-icon-list"></i> &nbsp ${msg_aplic.LOG}
                                </a4j:commandLink>

                            </h:panelGroup>
                        </h:panelGrid>

                    </h:panelGroup>
                </h:panelGrid>
            </rich:tab>

        </rich:tabPanel>

        <h:panelGrid id="mensagem" columns="1" width="100%" styleClass="tabMensagens">
            <h:panelGrid columns="1" width="100%">
                <h:outputText styleClass="mensagem" value="#{GestaoNotasControle.mensagem}"/>
                <h:outputText styleClass="mensagemDetalhada" value="#{GestaoNotasControle.mensagemDetalhada}"/>
            </h:panelGrid>
        </h:panelGrid>
        <rich:jQuery selector=".rich-calendar-input.MMyyyy" timing="onload" query="mask('99/9999')"/>
    </h:form>

    <rich:modalPanel id="modalNotaManual" styleClass="novaModal" width="450" autosized="true" shadowOpacity="true">
        <f:facet name="header">
            <h:panelGroup>
                <h:outputText value="#{msg_aplic.prt_nota_manual}"/>
            </h:panelGroup>
        </f:facet>
        <f:facet name="controls">
            <h:panelGroup>
                <h:outputText
                        id="hidelinkManual"
                        styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign btnSelecionarModalidade"/>
                <rich:componentControl for="modalNotaManual" attachTo="hidelinkManual" operation="hide"
                                       event="onclick"/>
            </h:panelGroup>
        </f:facet>

        <a4j:form prependId="true" id="formModalNotaManual">
            <h:panelGroup>
                <h:panelGroup styleClass="margin-box">
                    <h:panelGroup layout="block" styleClass="texto-size-16-real texto-font texto-cor-cinza">
                        <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_informe_num_nota_manual}:"/>
                        <h:inputText id="inputNotaManual" size="50" onblur="blurinput(this);"
                                     onfocus="focusinput(this);" styleClass="form"
                                     value="#{GestaoNotasControle.numeroNotaManual}"/>
                        <br/>
                        <br/>
                        <h:outputText id="msgDetalhadaNotaManual"
                                      value="#{GestaoNotasControle.mensagemDetalhada}"/>
                    </h:panelGroup>
                </h:panelGroup>
                <h:panelGroup layout="block" styleClass="container-botoes">
                    <h:panelGroup styleClass="margin-box">
                        <a4j:commandLink
                                id="botaoGerarNotaManual"
                                action="#{GestaoNotasControle.gerarNotaManual}"
                                styleClass="botaoPrimario texto-size-16-real"
                                oncomplete="#{GestaoNotasControle.onComplete}"
                                reRender="formModalNotaManual, dtableGestaoNotas, totalizadores, mensagem"
                                value="#{msg_aplic.prt_gerar_nota_manual}"/>

                        <a4j:commandLink
                                id="btnFecharNotaManual"
                                style="margin-left: 12px"
                                status="false"
                                onclick="fireElement('hidelinkManual');"
                                value="#{msg_aplic.FECHAR}"
                                styleClass="botaoSecundario texto-size-16-real"/>
                    </h:panelGroup>
                </h:panelGroup>
                <br/>
                <br/>
            </h:panelGroup>
        </a4j:form>
    </rich:modalPanel>

    <rich:modalPanel id="modalNotaManualExcluir" styleClass="novaModal" width="450" autosized="true"
                     shadowOpacity="true">
        <f:facet name="header">
            <h:panelGroup>
                <h:outputText value="#{msg_aplic.prt_excluir_nota_manual}"/>
            </h:panelGroup>
        </f:facet>
        <f:facet name="controls">
            <h:panelGroup>
                <h:outputText
                        id="hidelinkManualExcluir"
                        styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign btnSelecionarModalidade"/>
                <rich:componentControl for="modalNotaManualExcluir" attachTo="hidelinkManualExcluir" operation="hide"
                                       event="onclick"/>
            </h:panelGroup>
        </f:facet>

        <a4j:form prependId="true" id="formModalNotaManualExcluir">
            <h:panelGroup>
                <h:panelGroup styleClass="margin-box">
                    <h:panelGroup layout="block" styleClass="texto-size-16-real texto-font texto-cor-cinza">
                        <h:outputText styleClass="tituloCampos"
                                      value="#{msg_aplic.prt_informe_num_nota_manual_sera_exc}:"/>

                        <h:inputText id="inputNotaManualExcluir" size="50" onblur="blurinput(this);"
                                     onfocus="focusinput(this);" styleClass="form"
                                     value="#{GestaoNotasControle.numeroNotaManual}"/>
                        <br/>
                        <br/>
                        <h:outputText id="msgDetalhadaNotaManualExcluir"
                                      style="color: red"
                                      value="#{GestaoNotasControle.mensagemDetalhada}"/>
                        <h:outputText id="msgNotaManualExcluir"
                                      style="color: red"
                                      value="#{GestaoNotasControle.msgExcluirNotas}"/>
                    </h:panelGroup>
                </h:panelGroup>
                <h:panelGroup layout="block" styleClass="container-botoes">
                    <h:panelGroup styleClass="margin-box">

                        <a4j:commandLink
                                id="botaoGerarNotaManualConsultar"
                                action="#{GestaoNotasControle.consultarNotasExcluir}"
                                styleClass="botaoPrimario texto-size-16-real"
                                oncomplete="#{GestaoNotasControle.onComplete}"
                                reRender="formModalNotaManualExcluir, dtableGestaoNotas, totalizadores, mensagem"
                                value="#{msg_aplic.CONSULTAR}"/>

                        <a4j:commandLink
                                id="botaoGerarNotaManualExcluir"
                                rendered="#{GestaoNotasControle.apresentarBtnExcluirNotas}"
                                action="#{GestaoNotasControle.excluirNotaManual}"
                                style="margin-left: 12px"
                                styleClass="botaoPrimario texto-size-16-real"
                                oncomplete="#{GestaoNotasControle.onComplete}"
                                reRender="formModalNotaManualExcluir, dtableGestaoNotas, totalizadores, mensagem"
                                value="#{msg_aplic.prt_excluir_nota_manual}"/>

                        <a4j:commandLink
                                id="btnFecharNotaManualExcluir"
                                style="margin-left: 12px"
                                status="false"
                                onclick="fireElement('hidelinkManualExcluir');"
                                value="Fechar"
                                styleClass="botaoSecundario texto-size-16-real"/>
                    </h:panelGroup>
                </h:panelGroup>
                <br/>
                <br/>
            </h:panelGroup>
        </a4j:form>
    </rich:modalPanel>

    <rich:modalPanel id="modalNotaManualTitulo" styleClass="novaModal" width="450" autosized="true"
                     shadowOpacity="true">
        <f:facet name="header">
            <h:panelGroup>
                <h:outputText value="#{msg_aplic.prt_imp_notas_manuais_emit}"/>
            </h:panelGroup>
        </f:facet>
        <f:facet name="controls">
            <h:panelGroup>
                <h:outputText
                        id="hidelinkManualTitulo"
                        styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign btnSelecionarModalidade"/>
                <rich:componentControl for="modalNotaManualTitulo" attachTo="hidelinkManualTitulo" operation="hide"
                                       event="onclick"/>
            </h:panelGroup>
        </f:facet>

        <a4j:form prependId="true" id="formModalNotaManualTitulo">
            <h:panelGroup>
                <h:panelGroup styleClass="margin-box">
                    <h:panelGroup layout="block" styleClass="texto-size-16-real texto-font texto-cor-cinza">
                        <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_titulo_relatorio}:"/>

                        <h:inputText id="inputNotaManualTitulo" size="50" onblur="blurinput(this);"
                                     onfocus="focusinput(this);" styleClass="form"
                                     value="#{GestaoNotasControle.tituloRelatorio}"/>
                        <br/>
                        <br/>
                        <h:outputText id="msgDetalhadaNotaManualTitulo"
                                      style="color: red"
                                      value="#{GestaoNotasControle.mensagemDetalhada}"/>
                    </h:panelGroup>
                </h:panelGroup>
                <h:panelGroup layout="block" styleClass="container-botoes">
                    <h:panelGroup styleClass="margin-box">

                        <a4j:commandLink
                                id="btnNotaManualRelatorioTitulo"
                                styleClass="botaoPrimario texto-size-16-real"
                                reRender="modalNotaManualTitulo, dtableGestaoNotas, totalizadores, mensagem"
                                action="#{GestaoNotasControle.imprimirRelatorioNotasManuais}"
                                oncomplete="#{GestaoNotasControle.nomeArquivoRelatorioNotaManual}">
                            <i class="fa-icon-print"></i> &nbsp ${msg_aplic.IMPRIMIR}
                        </a4j:commandLink>

                        <a4j:commandLink
                                id="btnFecharNotaManualTitulo"
                                style="margin-left: 12px"
                                status="false"
                                onclick="fireElement('hidelinkManualTitulo');"
                                value="#{msg_aplic.FECHAR}"
                                styleClass="botaoSecundario texto-size-16-real"/>
                    </h:panelGroup>
                </h:panelGroup>
                <br/>
                <br/>
            </h:panelGroup>
        </a4j:form>
    </rich:modalPanel>

    <rich:modalPanel id="modalLoadEnviandoNotas" styleClass="novaModal" width="450" autosized="true"
                     shadowOpacity="true">
        <h:form id="formMdlEnviandoNotas">
            <h:panelGroup id="pnlMdlEnviandoNotas" layout="block"
                          style="display:flex; flex-direction: column; align-items: center;">
                <h:outputText id="msgEnviandoNotas" value="#{GestaoNotasControle.msgEnviandoNotas}"
                              style="margin-top: 10px;" styleClass="texto-size-20-real"/>
            </h:panelGroup>
        </h:form>
    </rich:modalPanel>

    <rich:modalPanel domElementAttachment="parent" id="modalNotasExcluidas"
                     styleClass="novaModal"
                     autosized="true" width="650"
                     height="150" shadowOpacity="true">

        <f:facet name="header">
            <h:panelGroup>
                <h:outputText value="Notas Excluídas"/>
            </h:panelGroup>
        </f:facet>
        <f:facet name="controls">
            <h:panelGroup>
                <h:outputText
                        styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign"
                        id="hidelinkNotasExcluidas"/>
                <rich:componentControl for="modalNotasExcluidas" attachTo="hidelinkNotasExcluidas" operation="hide"
                                       event="onclick"/>
            </h:panelGroup>
        </f:facet>

        <a4j:form prependId="true" id="formModalNotasExcluidas">

            <h:panelGroup id="panelGeralModalNotasExcluidas" style="text-align: center">

                <rich:dataTable id="tblNotasExcluidas" value="#{GestaoNotasControle.listaItensExcluidos}" width="100%"
                                var="excluido" rows="10">
                    <rich:column>
                        <f:facet name="header">
                            <h:outputText value="Matrícula"/>
                        </f:facet>
                        <h:outputText value="#{excluido.matricula}"/>
                    </rich:column>
                    <rich:column>
                        <f:facet name="header">
                            <h:outputText value="Nome"/>
                        </f:facet>
                        <h:outputText value="#{excluido.nome}"/>
                    </rich:column>
                    <rich:column style="text-align: right">
                        <f:facet name="header">
                            <h:outputText value="Valor"/>
                        </f:facet>
                        <h:outputText value="#{excluido.valor_apresentar}"/>
                    </rich:column>
                    <rich:column style="text-align: right">
                        <f:facet name="header">
                            <h:outputText value="RPS"/>
                        </f:facet>
                        <h:outputText value="#{excluido.rps}"/>
                    </rich:column>
                    <rich:column style="text-align: right">
                        <f:facet name="header">
                            <h:outputText value="Data Emissão"/>
                        </f:facet>
                        <h:outputText value="#{excluido.dataEmissao_Apresentar}"/>
                    </rich:column>
                </rich:dataTable>
                <rich:datascroller align="center" for="formModalNotasExcluidas:tblNotasExcluidas"
                                   id="scNotasExcluidas"/>
            </h:panelGroup>
        </a4j:form>
    </rich:modalPanel>

    <rich:modalPanel domElementAttachment="parent" id="modalPlanos"
                     styleClass="novaModal"
                     autosized="true" width="600"
                     height="150" shadowOpacity="true">

        <f:facet name="header">
            <h:panelGroup>
                <h:outputText value="Filtrar Planos"/>
            </h:panelGroup>
        </f:facet>
        <f:facet name="controls">
            <h:panelGroup>
                <h:outputText
                        styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign"
                        id="hideLinkModalPlanos"/>
                <rich:componentControl for="modalPlanos" attachTo="hideLinkModalPlanos" operation="hide"
                                       event="onclick"/>
            </h:panelGroup>
        </f:facet>

        <a4j:form prependId="true" id="formPlanos">

            <h:outputText style="font-size: 14px;font-weight: bold;"
                          value="#{GestaoNotasControle.qtdPlanosSelecionados}"/>

            <h:panelGroup id="panelGeralPlanos"
                          layout="block"
                          style="display: inherit;max-width: 700px; padding-top: 10px">

                <rich:dataTable id="tblPlanos" value="#{GestaoNotasControle.planos}" width="100%" var="plano" rows="20">
                    <rich:column style="text-align: center">
                        <f:facet name="header">
                            <h:selectBooleanCheckbox value="#{GestaoNotasControle.selecionarTodosPlanos}">
                                <a4j:support action="#{GestaoNotasControle.marcarTodosPlanos}"
                                             status="false"
                                             reRender="formPlanos, form:filtroPlanos" event="onclick"/>
                            </h:selectBooleanCheckbox>
                        </f:facet>
                        <h:selectBooleanCheckbox value="#{plano.selecionado}">
                            <a4j:support action="#{GestaoNotasControle.calcularPlanosSelecionados}"
                                         status="false"
                                         reRender="formPlanos, form:filtroPlanos" event="onclick"/>
                        </h:selectBooleanCheckbox>
                    </rich:column>
                    <rich:column style="text-align: left">
                        <f:facet name="header">
                            <h:outputText value="Descrição"/>
                        </f:facet>
                        <h:outputText value="#{plano.descricao}"/>
                    </rich:column>
                </rich:dataTable>
                <rich:datascroller align="center" for="formPlanos:tblPlanos" id="scPlanos"/>
            </h:panelGroup>

            <h:panelGroup layout="block"
                          style="text-align: center; width: 100%; padding: 10px">

                <a4j:commandLink reRender="panelGeralPlanos"
                                 onclick="Richfaces.hideModalPanel('modalPlanos');"
                                 value="Fechar"
                                 styleClass="pure-button pure-button-secundary"/>
            </h:panelGroup>

        </a4j:form>
    </rich:modalPanel>

    <jsp:include page="includes/include_carregando.jsp"/>
    <jsp:include page="/includes/include_expiracao_alerta_popup.jsp" flush="true">
        <jsp:param name="apresentarModal" value="${SuporteControle.apresentarMensagemNFe}"/>
    </jsp:include>
</f:view>

<script>
    carregarTooltipsterGestaoNotas();
</script>
