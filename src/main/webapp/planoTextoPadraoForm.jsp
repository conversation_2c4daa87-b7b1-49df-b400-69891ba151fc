<head><%@include file="/includes/include_import_minifiles.jsp" %></head>
<link href="beta/css/font-awesome_2.0.min.css" type="text/css" rel="stylesheet"/>
<link href="./css/otimize.css" rel="stylesheet" type="text/css">
<link href="./beta/css/pure-min.css" rel="stylesheet" type="text/css">
<link href="./beta/css/pure-ext.css" rel="stylesheet" type="text/css">
<link href="../sample.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" language="javascript" src="hoverform.js"></script>
<%@taglib prefix="f" uri="http://java.sun.com/jsf/core"%>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html"%>

<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax"%>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich"%>

<f:loadBundle var="msg_bt" basename="propriedades.Botoes"/>
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao"/>
<f:loadBundle var="msg" basename="propriedades.Mensagens"/>
<f:loadBundle var="editorModelopropriedades" basename="propriedades.editorModelopropriedades"/>

<f:view>
    <jsp:include page="includes/include_carregando_ripple.jsp" flush="true"/>
    <title>
        <h:outputText value="#{msg_aplic.prt_PlanoTextoPadrao_tituloForm}"/>
    </title>

    <script type="text/javascript">
        function FCKeditor_OnComplete(editorInstance) {
            window.status = editorInstance.Description;
        }
    </script>

    <meta name="robots" content="noindex, nofollow" />
    <a4j:loadScript src="/script/jquery.maskedinput-1.2.2.js" />
    <rich:modalPanel id="panelEmpresa" styleClass="novaModal" autosized="true" shadowOpacity="true" width="380" height="150">
        <f:facet name="header">
            <h:panelGroup>
                <h:outputText value="Marcadores Empresa" />
            </h:panelGroup>
        </f:facet>

        <f:facet name="controls">
            <h:panelGroup>
                <h:outputText
                        styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign btnSelecionarModalidade"
                        id="hidelinkEmpresa"/>
                <rich:componentControl for="panelEmpresa" attachTo="hidelinkEmpresa" operation="hide" event="onclick" />
            </h:panelGroup>
        </f:facet>

        <a4j:form  id="formMarcadorEmpresa" styleClass="paginaFontResponsiva">
            <hr style="border-color: #e6e6e6;"/>
            <div style="overflow-x: auto; overflow-y: auto;  width: 550px; height: 200px;">
                <table>
                    <tr>
                        <td>
                            <rich:dataTable id="MarcadoEmpresa"   styleClass="tabelaSimplesCustom"
                                            var="marcadorEmpresa" rows="40" value="#{PlanoTextoPadraoControle.listaSelectItemMarcadoEmpresa}">
                                <rich:column width="170px">
                                    <h:outputText styleClass="mensagem"  value="#{marcadorEmpresa.nome}"/>
                                </rich:column>

                                <rich:column >
                                    <h:inputText styleClass="campos" size="30"  value="#{marcadorEmpresa.tag}"/>
                                    <a4j:commandLink action="#{PlanoTextoPadraoControle.selecionarMarcadorEmpresa}"
                                                       onclick="fireElementFromAnyParent('form:btnAtualizaTempo')"
                                                       reRender="form"  styleClass="linkPadrao texto-cor-azul texto-size-16"
                                                       oncomplete="Richfaces.hideModalPanel('panelMarcado');">
                                        <i class="fa-icon-plus"></i>
                                    </a4j:commandLink>
                                </rich:column>
                            </rich:dataTable>
                        </td>
                    </tr>
                </table>
            </div>

            <h:panelGrid columns="1" width="95%" columnClasses="colunaCentralizada">
                <h:panelGrid id="mensagemMarcadoresEmpresa" columns="1" width="100%" styleClass="tabMensagens">
                    <h:panelGrid columns="1" width="100%">
                        <h:outputText styleClass="mensagem"  value="#{PlanoTextoPadraoControle.mensagem}"/>
                        <h:outputText styleClass="mensagemDetalhada" value="#{PlanoTextoPadraoControle.mensagemDetalhada}"/>
                    </h:panelGrid>
                </h:panelGrid>
            </h:panelGrid>
        </a4j:form>
    </rich:modalPanel>

    <rich:modalPanel id="panelCliente" styleClass="novaModal"  autosized="true" shadowOpacity="true" width="600" height="150">
        <f:facet name="header">
            <h:panelGroup>
                <h:outputText value="Marcadores Cliente" />
            </h:panelGroup>
        </f:facet>

        <f:facet name="controls">
            <h:panelGroup>
                <h:outputText
                        styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign btnSelecionarModalidade"
                        id="hidelink1"/>
                <rich:componentControl for="panelCliente" attachTo="hidelink1" operation="hide" event="onclick" />
            </h:panelGroup>
        </f:facet>

        <a4j:form id="formMarcadorCliente" styleClass="paginaFontResponsiva">
            <div style="overflow-x: auto; overflow-y: auto;  width: 580px; height: 200px;">
                <table>
                    <tr>
                        <td>
                            <rich:dataTable id="MarcadoCliente"  styleClass="tabelaSimplesCustom"
                                            var="marcadorCliente" rows="95" value="#{PlanoTextoPadraoControle.listaSelectItemMarcadoCliente}">
                                <rich:column width="270px">
                                    <h:outputText styleClass="mensagem"  value="#{marcadorCliente.nome}"/>
                                </rich:column>

                                <rich:column >
                                    <h:inputText styleClass="campos" size="30"  value="#{marcadorCliente.tag}"/>
                                </rich:column>
                                <rich:column >
                                    <a4j:commandLink action="#{PlanoTextoPadraoControle.selecionarMarcadorCliente}"
                                                     id="addTagContratoCliente"
                                                       onclick="fireElementFromAnyParent('form:btnAtualizaTempo')"
                                                       reRender="form" styleClass="linkPadrao texto-cor-azul texto-size-16"
                                                       oncomplete="Richfaces.hideModalPanel('panelMarcado');">
                                        <i class="fa-icon-plus"></i>
                                    </a4j:commandLink>
                                </rich:column>
                            </rich:dataTable>
                        </td>
                    </tr>
                </table>
            </div>

            <h:panelGrid columns="1" width="95%" columnClasses="colunaCentralizada">
                <h:panelGrid id="mensagemMarcadoresCliente" columns="1" width="100%" styleClass="tabMensagens">
                    <h:panelGrid columns="1" width="100%">
                        <h:outputText styleClass="mensagem"  value="#{PlanoTextoPadraoControle.mensagem}"/>
                        <h:outputText styleClass="mensagemDetalhada" value="#{PlanoTextoPadraoControle.mensagemDetalhada}"/>
                    </h:panelGrid>
                </h:panelGrid>
            </h:panelGrid>
        </a4j:form>
    </rich:modalPanel>

    <rich:modalPanel id="panelVenda" styleClass="novaModal"  autosized="true" shadowOpacity="true" width="380" height="150">
        <f:facet name="header">
            <h:panelGroup>
                <h:outputText value="Marcadores Venda Avulsa" />
            </h:panelGroup>
        </f:facet>

        <f:facet name="controls">
            <h:panelGroup>
                <h:outputText
                        styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign btnSelecionarModalidade"
                        id="hidelinkVenda"/>
                <rich:componentControl for="panelVenda" attachTo="hidelinkVenda" operation="hide" event="onclick" />
            </h:panelGroup>
        </f:facet>

        <a4j:form id="formMarcadorVenda" styleClass="paginaFontResponsiva">
            <div style="overflow-x: auto; overflow-y: auto; height: 200px;">
                <table>
                    <tr>
                        <td>
                            <rich:dataTable id="MarcadoVenda"  styleClass="tabelaSimplesCustom"
                                            var="marcadorVenda" rows="40" value="#{PlanoTextoPadraoControle.listaSelectItemMarcadoVenda}">
                                <rich:column width="170px">
                                    <h:outputText styleClass="mensagem"  value="#{marcadorVenda.nome}"/>
                                </rich:column>

                                <rich:column >
                                    <h:inputText styleClass="campos" size="30"  value="#{marcadorVenda.tag}"/>
                                    <a4j:commandLink action="#{PlanoTextoPadraoControle.selecionarMarcadorVenda}"
                                                       onclick="fireElementFromAnyParent('form:btnAtualizaTempo')"
                                                       reRender="form" styleClass="linkPadrao texto-cor-azul texto-size-16"
                                                       oncomplete="Richfaces.hideModalPanel('panelMarcado');">
                                        <i class="fa-icon-plus"></i>
                                    </a4j:commandLink>
                                </rich:column>
                            </rich:dataTable>
                        </td>
                    </tr>
                </table>
            </div>

            <h:panelGrid columns="1" width="95%" columnClasses="colunaCentralizada">
                <h:panelGrid id="mensagemMarcadoresVenda" columns="1" width="100%" styleClass="tabMensagens">
                    <h:panelGrid columns="1" width="100%">
                        <h:outputText styleClass="mensagem"  value="#{PlanoTextoPadraoControle.mensagem}"/>
                        <h:outputText styleClass="mensagemDetalhada" value="#{PlanoTextoPadraoControle.mensagemDetalhada}"/>
                    </h:panelGrid>
                </h:panelGrid>
            </h:panelGrid>
        </a4j:form>
    </rich:modalPanel>

    <rich:modalPanel id="panelItensVenda" styleClass="novaModal"  autosized="true" shadowOpacity="true" width="380" height="150">
        <f:facet name="header">
            <h:panelGroup>
                <h:outputText value="Marcadores Itens Venda Avulsa" />
            </h:panelGroup>
        </f:facet>

        <f:facet name="controls">
            <h:panelGroup>
                <h:outputText
                        styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign btnSelecionarModalidade"
                        id="hidelinkItens"/>
                <rich:componentControl for="panelItensVenda" attachTo="hidelinkItens" operation="hide" event="onclick" />
            </h:panelGroup>
        </f:facet>

        <a4j:form id="formMarcadorItensVenda" styleClass="paginaFontResponsiva">
            <div style="overflow-x: auto; overflow-y: auto;  width: 550px; height: 200px;">
                <table>
                    <tr>
                        <td>
                            <rich:dataTable id="MarcadoItensVenda"  styleClass="tabelaSimplesCustom"
                                            var="marcadorItensVenda" rows="40" value="#{PlanoTextoPadraoControle.listaSelectItemMarcadoItensVenda}">
                                <rich:column width="170px">
                                    <h:outputText styleClass="mensagem"  value="#{marcadorItensVenda.nome}"/>
                                </rich:column>

                                <rich:column >
                                    <h:inputText styleClass="campos" size="30"  value="#{marcadorItensVenda.tag}"/>
                                    <a4j:commandLink action="#{PlanoTextoPadraoControle.selecionarMarcadorItensVenda}"
                                                       reRender="form" styleClass="linkPadrao texto-cor-azul texto-size-16"
                                                       oncomplete="Richfaces.hideModalPanel('panelMarcado');">

                                    </a4j:commandLink>
                                </rich:column>
                            </rich:dataTable>
                        </td>
                    </tr>
                </table>
            </div>

            <h:panelGrid columns="1" width="95%" columnClasses="colunaCentralizada">
                <h:panelGrid id="mensagemMarcadoresItensVenda" columns="1" width="100%" styleClass="tabMensagens">
                    <h:panelGrid columns="1" width="100%">
                        <h:outputText styleClass="mensagem"  value="#{PlanoTextoPadraoControle.mensagem}"/>
                        <h:outputText styleClass="mensagemDetalhada" value="#{PlanoTextoPadraoControle.mensagemDetalhada}"/>
                    </h:panelGrid>
                </h:panelGrid>
            </h:panelGrid>
        </a4j:form>
    </rich:modalPanel>

    <rich:modalPanel id="panelPacoteVenda" styleClass="novaModal"  autosized="true" shadowOpacity="true" width="380" height="150">
        <f:facet name="header">
            <h:panelGroup>
                <h:outputText value="Marcadores Pacote Venda Avulsa" />
            </h:panelGroup>
        </f:facet>

        <f:facet name="controls">
            <h:panelGroup>
                <h:outputText
                        styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign btnSelecionarModalidade"
                        id="hidelinkPacote"/>
                <rich:componentControl for="panelPacoteVenda" attachTo="hidelinkPacote" operation="hide" event="onclick" />
            </h:panelGroup>
        </f:facet>

        <a4j:form id="formMarcadorPacoteVenda" styleClass="paginaFontResponsiva">
            <div style="overflow-x: auto; overflow-y: auto;  width: 550px; height: 200px;">
                <table>
                    <tr>
                        <td>
                            <rich:dataTable id="MarcadoPacoteVenda"  styleClass="tabelaSimplesCustom"
                                            var="marcadorPacoteVenda" rows="40" value="#{PlanoTextoPadraoControle.listaSelectItemMarcadoPacoteVenda}">
                                <rich:column width="170px">
                                    <h:outputText styleClass="mensagem"  value="#{marcadorPacoteVenda.nome}"/>
                                </rich:column>

                                <rich:column >
                                    <h:inputText styleClass="campos" size="30"  value="#{marcadorPacoteVenda.tag}"/>
                                    <a4j:commandLink action="#{PlanoTextoPadraoControle.selecionarMarcadorPacoteVenda}"
                                                       onclick="fireElementFromAnyParent('form:btnAtualizaTempo')"
                                                       reRender="form" styleClass="linkPadrao texto-cor-azul texto-size-16"
                                                       oncomplete="Richfaces.hideModalPanel('panelMarcado');">
                                        <i class="fa-icon-plus"></i>
                                    </a4j:commandLink>
                                </rich:column>
                            </rich:dataTable>
                        </td>
                    </tr>
                </table>
            </div>

            <h:panelGrid columns="1" width="95%" columnClasses="colunaCentralizada">
                <h:panelGrid id="mensagemMarcadoresPacoteVenda" columns="1" width="100%" styleClass="tabMensagens">
                    <h:panelGrid columns="1" width="100%">
                        <h:outputText styleClass="mensagem"  value="#{PlanoTextoPadraoControle.mensagem}"/>
                        <h:outputText styleClass="mensagemDetalhada" value="#{PlanoTextoPadraoControle.mensagemDetalhada}"/>
                    </h:panelGrid>
                </h:panelGrid>
            </h:panelGrid>
        </a4j:form>
    </rich:modalPanel>

    <rich:modalPanel id="panelPlano" styleClass="novaModal"  autosized="true" shadowOpacity="true" width="380" height="150">
        <f:facet name="header">
            <h:panelGroup>
                <h:outputText value="Marcadores do Plano" />
            </h:panelGroup>
        </f:facet>

        <f:facet name="controls">
            <h:panelGroup>
                <h:outputText
                        styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign btnSelecionarModalidade"
                        id="hidelink2"/>
                <rich:componentControl for="panelPlano" attachTo="hidelink2" operation="hide" event="onclick"/>
            </h:panelGroup>
        </f:facet>

        <a4j:form id="formMarcadorPlano" styleClass="paginaFontResponsiva">
            <div style="overflow-x: auto; overflow-y: auto;  width: 550px; height: 200px;">
                <table>
                    <tr>
                        <td>
                            <rich:dataTable id="MarcadoPlano"  styleClass="tabelaSimplesCustom"
                                            var="marcadorPlano" rows="40" value="#{PlanoTextoPadraoControle.listaSelectItemMarcadoPlano}">
                                <rich:column width="170px">
                                    <h:outputText styleClass="mensagem"  value="#{marcadorPlano.nome}"/>
                                </rich:column>

                                <rich:column >
                                    <h:inputText styleClass="campos" size="30" value="#{marcadorPlano.tag}"/>
                                    <a4j:commandLink action="#{PlanoTextoPadraoControle.selecionarMarcadorPlano}"
                                                       onclick="fireElementFromAnyParent('form:btnAtualizaTempo')"
                                                       reRender="form" styleClass="linkPadrao texto-cor-azul texto-size-16"
                                                       oncomplete="Richfaces.hideModalPanel('panelMarcado');">
                                        <i class="fa-icon-plus"></i>
                                    </a4j:commandLink>
                                </rich:column>
                            </rich:dataTable>
                        </td>
                    </tr>
                </table>
            </div>

            <h:panelGrid columns="1" width="95%" columnClasses="colunaCentralizada">
                <h:panelGrid id="mensagemMarcadorPlano" columns="1" width="100%" styleClass="tabMensagens">
                    <h:panelGrid columns="1" width="100%">
                        <h:outputText styleClass="mensagem"  value="#{PlanoTextoPadraoControle.mensagem}"/>
                        <h:outputText styleClass="mensagemDetalhada" value="#{PlanoTextoPadraoControle.mensagemDetalhada}"/>
                    </h:panelGrid>
                </h:panelGrid>
            </h:panelGrid>
        </a4j:form>
    </rich:modalPanel>

    <rich:modalPanel id="panelContrato" styleClass="novaModal"  autosized="true" shadowOpacity="true" width="380" height="150">
        <f:facet name="header">
            <h:panelGroup>
                <h:outputText value="Marcadores do Contrato" />
            </h:panelGroup>
        </f:facet>

        <f:facet name="controls">
            <h:panelGroup>
                <h:outputText
                        styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign btnSelecionarModalidade"
                        id="hidelink3"/>
                <rich:componentControl for="panelContrato" attachTo="hidelink3" operation="hide" event="onclick"/>
            </h:panelGroup>
        </f:facet>

        <a4j:form id="formMarcadorContrato" styleClass="paginaFontResponsiva">
            <div style="overflow-x: auto; overflow-y: auto;  width: 680px; height: 200px;">
                <table>
                    <tr>
                        <td>
                            <rich:dataTable id="MarcadoContrato"  styleClass="tabelaSimplesCustom"
                                            var="marcadorContrato" value="#{PlanoTextoPadraoControle.listaSelectItemMarcadoContrato}">
                                <rich:column width="120px">
                                    <h:outputText styleClass="mensagem"  value="#{marcadorContrato.nome}"/>
                                </rich:column>

                                <rich:column >
                                    <h:inputText styleClass="campos" size="30" value="#{marcadorContrato.tag}"/>
                                    <a4j:commandLink action="#{PlanoTextoPadraoControle.selecionarMarcadorContrato}"
                                                       onclick="fireElementFromAnyParent('form:btnAtualizaTempo')"
                                                       reRender="form" styleClass="linkPadrao texto-cor-azul texto-size-16"
                                                       oncomplete="Richfaces.hideModalPanel('panelMarcado');">
                                        <i class="fa-icon-plus"></i>
                                    </a4j:commandLink>
                                    <%--h:commandLink  action="#{PlanoTextoPadraoControle.selecionarMarcadorCliente}" value="#{marcadorPlano.nome}"/--%>
                                </rich:column>
                            </rich:dataTable>
                        </td>
                    </tr>
                </table>
            </div>

            <h:panelGrid columns="1" width="95%" columnClasses="colunaCentralizada">
                <h:panelGrid id="mensagemMarcadorContrato" columns="1" width="100%" styleClass="tabMensagens">
                    <h:panelGrid columns="1" width="100%">
                        <h:outputText styleClass="mensagem"  value="#{PlanoTextoPadraoControle.mensagem}"/>
                        <h:outputText styleClass="mensagemDetalhada" value="#{PlanoTextoPadraoControle.mensagemDetalhada}"/>
                    </h:panelGrid>
                </h:panelGrid>
            </h:panelGrid>
        </a4j:form>
    </rich:modalPanel>

    <rich:modalPanel id="panelUsuario" styleClass="novaModal"  autosized="true" shadowOpacity="true" width="550" height="150">
        <f:facet name="header">
            <h:panelGroup>
                <h:outputText value="Marcadores de Usu�rio" />
            </h:panelGroup>
        </f:facet>

        <f:facet name="controls">
            <h:panelGroup>
                <h:outputText
                        styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign btnSelecionarModalidade"
                        id="hidelink4"/>
                <rich:componentControl for="panelUsuario" attachTo="hidelink4" operation="hide" event="onclick"/>
            </h:panelGroup>
        </f:facet>

        <a4j:form id="formMarcadorUsuario" styleClass="paginaFontResponsiva">
            <div style="overflow-x: auto; overflow-y: auto;">
                <table>
                    <tr>
                        <td>
                            <rich:dataTable id="MarcadoUsuario"   styleClass="tabelaSimplesCustom"
                                            var="marcadorUsuario" rows="40" value="#{PlanoTextoPadraoControle.listaSelectItemMarcadoUsuario}">
                                <rich:column width="170px">
                                    <h:outputText styleClass="mensagem"  value="#{marcadorUsuario.nome}"/>
                                </rich:column>

                                <rich:column >
                                    <h:inputText styleClass="campos" size="30" value="#{marcadorUsuario.tag}"/>
                                    <a4j:commandLink action="#{PlanoTextoPadraoControle.selecionarMarcadorUsuario}"
                                                       onclick="fireElementFromAnyParent('form:btnAtualizaTempo')"
                                                       reRender="form" styleClass="linkPadrao texto-cor-azul texto-size-16"
                                                       oncomplete="Richfaces.hideModalPanel('panelMarcado');">
                                        <i class="fa-icon-plus"></i>
                                    </a4j:commandLink>
                                </rich:column>
                            </rich:dataTable>
                        </td>
                    </tr>
                </table>
            </div>

            <h:panelGrid columns="1" width="95%" columnClasses="colunaCentralizada">
                <h:panelGrid id="mensagemMarcadorUsuario" columns="1" width="100%" styleClass="tabMensagens">
                    <h:panelGrid columns="1" width="100%">
                        <h:outputText styleClass="mensagem" value="#{PlanoTextoPadraoControle.mensagem}"/>
                        <h:outputText styleClass="mensagemDetalhada" value="#{PlanoTextoPadraoControle.mensagemDetalhada}"/>
                    </h:panelGrid>
                </h:panelGrid>
            </h:panelGrid>
        </a4j:form>
    </rich:modalPanel>

    <rich:modalPanel id="panelModalidade" styleClass="novaModal"  autosized="true" shadowOpacity="true" width="550" height="250">
        <f:facet name="header">
            <h:panelGroup>
                <h:outputText value="Marcadores da Modalidade" />
            </h:panelGroup>
        </f:facet>

        <f:facet name="controls">
            <h:panelGroup>
                <h:outputText
                        styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign btnSelecionarModalidade"
                        id="hidelink5"/>
                <rich:componentControl for="panelModalidade" attachTo="hidelink5" operation="hide" event="onclick"/>
            </h:panelGroup>
        </f:facet>

        <a4j:form id="formMarcadorModalidade" styleClass="paginaFontResponsiva">
            <div style="overflow-x: auto; overflow-y: auto;">
                <table>
                    <tr>
                        <td>
                            <rich:dataTable id="MarcadoModalidade" width="430px" styleClass="tabelaSimplesCustom"
                                            var="marcadorModalidade" rows="40" value="#{PlanoTextoPadraoControle.listaSelectItemMarcadoModalidade}">
                                <rich:column width="180px">
                                    <h:outputText styleClass="mensagem"  value="#{marcadorModalidade.nome}"/>
                                </rich:column>

                                <rich:column width="200px">
                                    <h:inputText styleClass="campos" size="30"  value="#{marcadorModalidade.tag}"/>
                                </rich:column>

                                <rich:column width="30px">
                                    <h:selectBooleanCheckbox id="selecionar" value="#{marcadorModalidade.selecionado}"/>
                                </rich:column>
                            </rich:dataTable>
                        </td>
                    </tr>
                </table>
            </div>

            <h:panelGrid columns="1" width="100%" columnClasses="colunaCentralizada">
                <h:panelGrid columns="1" columnClasses="colunaAlinhamento" width="100%">
                    <h:panelGrid columns="1" width="100%" columnClasses="colunaCentralizada">
                        <a4j:commandLink action="#{PlanoTextoPadraoControle.setarTagVariavel}"
                                         onclick="fireElementFromAnyParent('form:btnAtualizaTempo')" oncomplete="#{PlanoTextoPadraoControle.mensagemNotificar}"
                                         styleClass="linkPadrao texto-cor-azul texto-size-16" reRender="formMarcadorModalidade" value="Visualizar TAG">
                            <i class="fa-icon-plus"></i>
                        </a4j:commandLink>
                    </h:panelGrid>

                    <h:inputText styleClass="campos" id="variavelTela" size="80" value="#{PlanoTextoPadraoControle.variavelTela}"/>
                </h:panelGrid>
            </h:panelGrid>
            <h:panelGroup layout="block" styleClass="container-botoes">
                <a4j:commandLink action="#{PlanoTextoPadraoControle.selecionarMarcadorModalidade}"
                                 onclick="fireElementFromAnyParent('form:btnAtualizaTempo')"
                                 reRender="form"  styleClass="botaoPrimario texto-size-14"
                                 oncomplete="Richfaces.hideModalPanel('panelModalidade');" value="Adicionar Marcador Texto Padr�o"/>
            </h:panelGroup>
        </a4j:form>
    </rich:modalPanel>

    <rich:modalPanel id="panelComposicao" styleClass="novaModal"  autosized="true" shadowOpacity="true" width="550" height="250">
        <f:facet name="header">
            <h:panelGroup>
                <h:outputText value="Marcadores do Pacote" />
            </h:panelGroup>
        </f:facet>

        <f:facet name="controls">
            <h:panelGroup>
                <h:outputText
                        styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign btnSelecionarModalidade"
                        id="hidelink6"/>
                <rich:componentControl for="panelComposicao" attachTo="hidelink6" operation="hide" event="onclick"/>
            </h:panelGroup>
        </f:facet>

        <a4j:form id="formMarcadorComposicao" styleClass="paginaFontResponsiva" >
            <h:panelGrid columns="1" width="95%" columnClasses="colunaCentralizada">
                <h:panelGrid columns="1" columnClasses="colunaAlinhamento" width="100%">
                    <h:panelGrid columns="1" width="100%" columnClasses="colunaCentralizada">
                        <rich:dataTable id="MarcadoComposicao" width="430px"  styleClass="tabelaSimplesCustom"
                                        var="marcadorComposicao" rows="40" value="#{PlanoTextoPadraoControle.listaSelectItemMarcadoComposicao}">
                            <rich:column width="180px">
                                <h:outputText styleClass="mensagem"  value="#{marcadorComposicao.nome}"/>
                            </rich:column>

                            <rich:column width="200px">
                                <h:inputText styleClass="campos" size="30" value="#{marcadorComposicao.tag}"/>
                            </rich:column>

                            <rich:column width="30px">
                                <h:selectBooleanCheckbox id="selecionar" value="#{marcadorComposicao.selecionado}"/>
                            </rich:column>
                        </rich:dataTable>

                        <a4j:commandLink action="#{PlanoTextoPadraoControle.setarTagVariavelComposicao}"
                                           onclick="fireElementFromAnyParent('form:btnAtualizaTempo')" oncomplete="#{PlanoTextoPadraoControle.mensagemNotificar}"
                                           styleClass="linkPadrao texto-cor-azul texto-size-16" reRender="formMarcadorComposicao" value="Visualizar TAG">
                            <i class="fa-icon-plus"></i>
                        </a4j:commandLink>
                    </h:panelGrid>

                    <h:inputText styleClass="campos" id="variavelTelaComposicao" size="80" value="#{PlanoTextoPadraoControle.variavelTelaComposicao}"/>
                    <h:panelGroup layout="block" styleClass="container-botoes">
                         <a4j:commandLink action="#{PlanoTextoPadraoControle.selecionarMarcadorComposicao}"
                                       onclick="fireElementFromAnyParent('form:btnAtualizaTempo')"
                                       reRender="form" styleClass="botaoPrimario texto-size-14" value="Adicionar Marcador"
                                       oncomplete="Richfaces.hideModalPanel('panelComposicao');"/>
                    </h:panelGroup>
                </h:panelGrid>
            </h:panelGrid>

            <h:panelGrid id="mensagemMarcadorComposicao" columns="1" width="100%" styleClass="tabMensagens">
                <h:panelGrid columns="1" width="100%">
                    <h:outputText styleClass="mensagem"  value="#{PlanoTextoPadraoControle.mensagem}"/>
                    <h:outputText styleClass="mensagemDetalhada" value="#{PlanoTextoPadraoControle.mensagemDetalhada}"/>
                </h:panelGrid>
            </h:panelGrid>
        </a4j:form>
    </rich:modalPanel>

    <rich:modalPanel id="panelMovParcela" styleClass="novaModal"  autosized="true" shadowOpacity="true" width="550" height="250">
        <f:facet name="header">
            <h:panelGroup>
                <h:outputText value="Marcadores da Mov. Parcela."></h:outputText>
            </h:panelGroup>
        </f:facet>

        <f:facet name="controls">
            <h:panelGroup>
                <h:outputText
                        styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign btnSelecionarModalidade"
                        id="hidelink7"/>
                <rich:componentControl for="panelMovParcela" attachTo="hidelink7" operation="hide" event="onclick"/>
            </h:panelGroup>
        </f:facet>

        <a4j:form id="formMarcadorMovParcela" >
            <h:panelGrid columns="1" width="95%" columnClasses="colunaCentralizada">
                <h:panelGrid columns="1" columnClasses="colunaAlinhamento" width="100%">
                    <h:panelGrid columns="1" width="100%" columnClasses="colunaCentralizada">
                        <rich:dataTable id="MarcadoMovParcela" width="430" styleClass="tabelaSimplesCustom"
                                        var="marcadorMovParcela" rows="40" value="#{PlanoTextoPadraoControle.listaSelectItemMarcadoMovParcela}">
                            <rich:column width="180px">
                                <h:outputText styleClass="mensagem"  value="#{marcadorMovParcela.nome}"/>
                            </rich:column>

                            <rich:column width="200px">
                                <h:inputText styleClass="campos" size="30" value="#{marcadorMovParcela.tag}"/>
                            </rich:column>

                            <rich:column width="30px">
                                <h:selectBooleanCheckbox id="selecionar" value="#{marcadorMovParcela.selecionado}"/>
                            </rich:column>
                        </rich:dataTable>

                        <a4j:commandLink action="#{PlanoTextoPadraoControle.setarTagVariavelMovParcela}"
                                         onclick="fireElementFromAnyParent('form:btnAtualizaTempo')" oncomplete="#{PlanoTextoPadraoControle.mensagemNotificar}"
                                           reRender="formMarcadorMovParcela" styleClass="linkPadrao texto-cor-azul texto-size-16" value="Visualizar TAG">
                            <i class="fa-icon-plus"></i>
                        </a4j:commandLink>
                    </h:panelGrid>

                    <h:inputText styleClass="campos" size="80" id="variavelTelaMovParcela" value="#{PlanoTextoPadraoControle.variavelTelaMovParcela}"/>


                </h:panelGrid>
                <h:panelGroup layout="block" styleClass="container-botoes">
                    <a4j:commandLink action="#{PlanoTextoPadraoControle.selecionarMarcadorMovParcela}"
                                       onclick="fireElementFromAnyParent('form:btnAtualizaTempo')"
                                       reRender="form" styleClass="botaoPrimario texto-size-16"
                                       oncomplete="Richfaces.hideModalPanel('panelMovParcela');" value="Adicionar Marcador"/>
                </h:panelGroup>
                <h:panelGrid id="mensagemMarcadorMovParcela" columns="1" width="100%" styleClass="tabMensagens">
                    <h:panelGrid columns="1" width="100%">
                        <h:outputText styleClass="mensagem"  value="#{PlanoTextoPadraoControle.mensagem}"/>
                        <h:outputText styleClass="mensagemDetalhada" value="#{PlanoTextoPadraoControle.mensagemDetalhada}"/>
                    </h:panelGrid>
                </h:panelGrid>
            </h:panelGrid>
        </a4j:form>
    </rich:modalPanel>

    <%-- <rich:modalPanel id="panelMovPagamento" styleClass="novaModal"  autosized="true" shadowOpacity="true" width="550" height="250">
        <f:facet name="header">
            <h:panelGroup>
                <h:outputText value="Marcadores da Mov. Pagamento" />
            </h:panelGroup>
        </f:facet>

        <f:facet name="controls">
            <h:panelGroup>
                <h:outputText
                        styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign btnSelecionarModalidade"
                        id="hidelinkMovPagamento"/>
                <rich:componentControl for="panelMovPagamento" attachTo="hidelinkMovPagamento" operation="hide" event="onclick"/>
            </h:panelGroup>
        </f:facet>

        <a4j:form id="formMarcadorMovPagamento" >
            <h:panelGrid columns="1" width="95%" columnClasses="colunaCentralizada">
                <h:panelGrid columns="1" columnClasses="colunaAlinhamento" width="100%">
                    <h:panelGrid columns="1" width="100%" columnClasses="colunaCentralizada">
                        <rich:dataTable id="MarcadorMovPagamento" width="430"    styleClass="tabelaSimplesCustom"
                                        var="marcadorMovPagamento" rows="40" value="#{PlanoTextoPadraoControle.listaSelectItemMarcadorMovPagamento}">
                            <rich:column width="180px">
                                <h:outputText styleClass="mensagem"  value="#{marcadorMovPagamento.nome}"/>
                            </rich:column>

                            <rich:column width="200px">
                                <h:inputText styleClass="campos" size="30" value="#{marcadorMovPagamento.tag}"/>
                            </rich:column>

                            <rich:column width="30px">
                                <h:selectBooleanCheckbox id="selecionar" value="#{marcadorMovPagamento.selecionado}"/>
                            </rich:column>
                        </rich:dataTable>

                        <a4j:commandLink action="#{PlanoTextoPadraoControle.setarTagVariavelMovPagamento}"
                                           onclick="fireElementFromAnyParent('form:btnAtualizaTempo')" oncomplete="#{PlanoTextoPadraoControle.mensagemNotificar}"
                                           reRender="formMarcadorMovPagamento" styleClass="linkPadrao texto-cor-azul texto-size-16" value="Visualizar TAG">
                            <i class="fa-icon-plus"></i>
                        </a4j:commandLink>
                    </h:panelGrid>

                    <h:inputText styleClass="campos" size="80" id="variavelTelaMovPagamento" value="#{PlanoTextoPadraoControle.variavelTelaMovPagamento}"/>


                </h:panelGrid>
                <h:panelGroup layout="block" styleClass="container-botoes">
                    <a4j:commandLink action="#{PlanoTextoPadraoControle.selecionarMarcadorMovPagamento}"
                                       onclick="fireElementFromAnyParent('form:btnAtualizaTempo')"
                                       reRender="form" styleClass="botaoPrimario texto-size-14"
                                       oncomplete="Richfaces.hideModalPanel('panelMovPagamento');" value="Adicionar Marcador">

                    </a4j:commandLink>
                </h:panelGroup>
                <h:panelGrid id="mensagemMarcadorMovPagamento" columns="1" width="100%" styleClass="tabMensagens">
                    <h:panelGrid columns="1" width="100%">
                        <h:outputText styleClass="mensagem"  value="#{PlanoTextoPadraoControle.mensagem}"/>
                        <h:outputText styleClass="mensagemDetalhada" value="#{PlanoTextoPadraoControle.mensagemDetalhada}"/>
                    </h:panelGrid>
                </h:panelGrid>
            </h:panelGrid>
        </a4j:form>
    </rich:modalPanel>--%>

    <%-- <rich:modalPanel id="panelReciboPagamento" styleClass="novaModal"  autosized="true" shadowOpacity="true" width="550" height="250">
        <f:facet name="header">
            <h:panelGroup>
                <h:outputText value="Marcadores Recibo Pagamento" />
            </h:panelGroup>
        </f:facet>

        <f:facet name="controls">
            <h:panelGroup>
                <h:outputText
                        styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign btnSelecionarModalidade"
                        id="hidelinkReciboPagamento"/>
                <rich:componentControl for="panelReciboPagamento" attachTo="hidelinkReciboPagamento" operation="hide" event="onclick"/>
            </h:panelGroup>
        </f:facet>

        <a4j:form ajaxSubmit="true"  id="formMarcadorReciboPagamento" >
            <div style="overflow-x: auto; overflow-y: auto;  width: 100%; height: 200px;">
                <table>
                    <tr>
                        <td>
                            <rich:dataTable id="MarcadorReciboPagamento" width="430" styleClass="tabelaSimplesCustom"
                                            var="marcadorReciboPagamento" rows="40" value="#{PlanoTextoPadraoControle.listaSelectItemMarcadorReciboPagamento}">
                                <rich:column width="180px">
                                    <h:outputText styleClass="mensagem"  value="#{marcadorReciboPagamento.nome}"/>
                                </rich:column>

                                <rich:column >
                                    <h:inputText styleClass="campos" size="30" value="#{marcadorReciboPagamento.tag}"/>
                                </rich:column>

                                <rich:column width="30px">
                                    <h:selectBooleanCheckbox id="selecionar" value="#{marcadorReciboPagamento.selecionado}"/>
                                </rich:column>
                            </rich:dataTable>
                        </td>
                    </tr>
                </table>
            </div>

            <h:panelGrid columns="1" width="95%" columnClasses="colunaCentralizada">
                <h:panelGrid columns="1" columnClasses="colunaAlinhamento" width="100%">
                    <h:panelGrid columns="1" width="100%" columnClasses="colunaCentralizada">
                        <a4j:commandLink action="#{PlanoTextoPadraoControle.setarTagVariavelReciboPagamento}"
                                           onclick="fireElementFromAnyParent('form:btnAtualizaTempo')" oncomplete="#{PlanoTextoPadraoControle.mensagemNotificar}"
                                           reRender="formMarcadorReciboPagamento" styleClass="linkPadrao texto-cor-azul texto-size-16">
                            <i class="fa-icon-plus"></i>
                        </a4j:commandLink>
                    </h:panelGrid>

                    <h:inputText styleClass="campos" size="80" id="variavelTelaMovPagamento" value="#{PlanoTextoPadraoControle.variavelTelaReciboPagamento}"/>


                </h:panelGrid>
                <h:panelGroup layout="block" styleClass="container-botoes">
                    <a4j:commandLink action="#{PlanoTextoPadraoControle.selecionarMarcadorReciboPagamento}"
                                       onclick="fireElementFromAnyParent('form:btnAtualizaTempo')"
                                       reRender="form" styleClass="botaoPrimario texto-size-14"
                                       oncomplete="Richfaces.hideModalPanel('panelReciboPagamento');" value="Adicionar Marcador Texto Padr�o"/>
                </h:panelGroup>
                <h:panelGrid id="mensagemMarcadorReciboPagamento" columns="1" width="100%" styleClass="tabMensagens">
                    <h:panelGrid columns="1" width="100%">
                        <h:outputText styleClass="mensagem"  value="#{PlanoTextoPadraoControle.mensagem}"/>
                        <h:outputText styleClass="mensagemDetalhada" value="#{PlanoTextoPadraoControle.mensagemDetalhada}"/>
                    </h:panelGrid>
                </h:panelGrid>
            </h:panelGrid>
        </a4j:form>
    </rich:modalPanel>--%>

    <rich:modalPanel id="panelTurma" styleClass="novaModal"  autosized="true" shadowOpacity="true" width="550" height="250">
        <f:facet name="header">
            <h:panelGroup>
                <h:outputText value="Marcadores da Turma"></h:outputText>
            </h:panelGroup>
        </f:facet>

        <f:facet name="controls">
            <h:panelGroup>
                <h:outputText
                        styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign btnSelecionarModalidade"
                        id="hidelink8"/>
                <rich:componentControl for="panelTurma" attachTo="hidelink8" operation="hide" event="onclick"/>
            </h:panelGroup>
        </f:facet>

        <a4j:form id="formMarcadorTurma" >
            <h:panelGrid columns="1" width="95%" columnClasses="colunaCentralizada">
                <h:panelGrid columns="1" columnClasses="colunaAlinhamento" width="100%">
                    <h:panelGrid columns="1" width="100%" columnClasses="colunaCentralizada">
                        <rich:dataTable id="MarcadoTurma" width="430px" styleClass="tabelaSimplesCustom"
                                        var="marcadorTurma" rows="40" value="#{PlanoTextoPadraoControle.listaSelectItemMarcadoTurma}">
                            <rich:column width="180px">
                                <h:outputText styleClass="mensagem"  value="#{marcadorTurma.nome}"/>
                            </rich:column>

                            <rich:column width="200px">
                                <h:inputText styleClass="campos" size="30" value="#{marcadorTurma.tag}"/>
                            </rich:column>

                            <rich:column width="30px">
                                <h:selectBooleanCheckbox id="selecionar" value="#{marcadorTurma.selecionado}"/>
                            </rich:column>
                        </rich:dataTable>

                        <a4j:commandLink action="#{PlanoTextoPadraoControle.setarTagVariavelTurma}"
                                           onclick="fireElementFromAnyParent('form:btnAtualizaTempo')" value="Visualizar TAG" oncomplete="#{PlanoTextoPadraoControle.mensagemNotificar}"
                                           reRender="formMarcadorTurma" styleClass="linkPadrao texto-cor-azul texto-size-16" >
                            <i class="fa-icon-plus"></i>
                        </a4j:commandLink>
                    </h:panelGrid>

                    <h:inputText styleClass="campos" size="80" id="variavelTelaTurma" value="#{PlanoTextoPadraoControle.variavelTelaTurma}"/>
                </h:panelGrid>
                <h:panelGroup layout="block" styleClass="container-botoes">
                    <a4j:commandLink action="#{PlanoTextoPadraoControle.selecionarMarcadorTurma}"
                                       onclick="fireElementFromAnyParent('form:btnAtualizaTempo')"
                                       reRender="form"  styleClass="botaoPrimario texto-size-14" value="Adicionar Marcador"
                                       oncomplete="Richfaces.hideModalPanel('panelTurma');"/>
                </h:panelGroup>
                <h:panelGrid id="mensagemMarcadorTurma" columns="1" width="100%" styleClass="tabMensagens">
                    <h:panelGrid columns="1" width="100%">
                        <h:outputText styleClass="mensagem"  value="#{PlanoTextoPadraoControle.mensagem}"/>
                        <h:outputText styleClass="mensagemDetalhada" value="#{PlanoTextoPadraoControle.mensagemDetalhada}"/>
                    </h:panelGrid>
                </h:panelGrid>
            </h:panelGrid>
        </a4j:form>
    </rich:modalPanel>

    <h:panelGrid columns="1" width="100%" cellpadding="0" cellspacing="0">
        <%-- INICIO HEADER --%>
        <c:set var="titulo" scope="session" value="${msg_aplic.prt_PlanoTextoPadrao_tituloForm}"/>
        <c:set var="urlWiki" scope="session" value="${SuperControle.urlWiki}Cadastros:Config._de_Contrato:Modelo_de_Contrato_e_Recibo"/>
        <f:facet name="header">
            <jsp:include page="topoReduzido_material.jsp"/>
        </f:facet>

        <a4j:form id="form">
            <hr style="border-color: #e6e6e6;"/>
            <h:commandLink action="#{PlanoTextoPadraoControle.liberarBackingBeanMemoria}"
                           onclick="fireElementFromAnyParent('form:btnAtualizaTempo')"
                           id="idLiberarBackingBeanMemoria" style="display: none" />

            <h:panelGrid columns="1" width="100%">
                <h:panelGrid columns="2" rowClasses="linhaImpar, linhaPar" columnClasses="classEsquerda, classDireita" width="100%">
                    <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_PlanoTextoPadrao_codigo}" />

                    <h:panelGroup>
                        <h:inputText  id="codigo"
                                      size="10"
                                      maxlength="10"
                                      onblur="blurinput(this);"
                                      onfocus="focusinput(this);"
                                      styleClass="camposSomenteLeitura"
                                      readonly="true"
                                      value="#{PlanoTextoPadraoControle.planoTextoPadraoVO.codigo}" />
                        <h:message for="codigo" styleClass="mensagemDetalhada"/>
                    </h:panelGroup>

                    <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_PlanoTextoPadrao_descricao}" />

                    <h:inputText  id="descricao"  size="45" maxlength="45" onblur="blurinput(this);"  onfocus="focusinput(this);" styleClass="form" value="#{PlanoTextoPadraoControle.planoTextoPadraoVO.descricao}" />

                    <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_PlanoTextoPadrao_Tipo}" />

                    <h:selectOneMenu  id="tipo" onblur="blurinput(this);"  onfocus="focusinput(this);" styleClass="form" value="#{PlanoTextoPadraoControle.planoTextoPadraoVO.tipoContrato}" >
                        <f:selectItems  value="#{PlanoTextoPadraoControle.listaSelectItemTipo}" />
                        <a4j:support event="onchange" reRender="form" />
                    </h:selectOneMenu>

                    <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_PlanoTextoPadrao_situacao}" />

                    <h:selectOneMenu  id="situacao" onblur="blurinput(this);"  onfocus="focusinput(this);" styleClass="form" value="#{PlanoTextoPadraoControle.planoTextoPadraoVO.situacao}" >
                        <f:selectItems  value="#{PlanoTextoPadraoControle.listaSelectItemSituacao}" />
                    </h:selectOneMenu>

                    <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_PlanoTextoPadrao_dataDefinicao}" />

                    <h:panelGroup>
                        <rich:calendar id="dataDefinicao"
                                       value="#{PlanoTextoPadraoControle.planoTextoPadraoVO.dataDefinicao}"
                                       inputSize="10"
                                       inputClass="form"
                                       oninputblur="blurinput(this);"
                                       oninputfocus="focusinput(this);"
                                       oninputchange="return validar_Data(this.id);"
                                       datePattern="dd/MM/yyyy"
                                       enableManualInput="true"
                                       zindex="2"
                                       showWeeksBar="false" />
                        <h:message for="dataDefinicao" styleClass="mensagemDetalhada"/>
                    </h:panelGroup>

                    <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_PlanoTextoPadrao_responsavelDefinicao}" />

                    <h:panelGroup>
                        <h:inputText id="responsavelDefinicao" readonly="true" onblur="blurinput(this);"  onfocus="focusinput(this);" styleClass="camposSomenteLeitura"   size="5" maxlength="10" value="#{PlanoTextoPadraoControle.planoTextoPadraoVO.responsavelDefinicao.codigo}"/>
                        <h:outputText  styleClass="tituloCampos" value="#{PlanoTextoPadraoControle.planoTextoPadraoVO.responsavelDefinicao.nome}" />
                    </h:panelGroup>
                </h:panelGrid>

                <h:panelGrid id="gridMarcadores" rendered="#{PlanoTextoPadraoControle.planoTextoPadraoVO.apresentarMarcadores}" columns="1" columnClasses="colunaCentralizada" headerClass="subordinado" width="100%">
                    <f:facet name="header">
                        <h:outputText value="Marcadores"/>
                    </f:facet>

                    <h:panelGrid  columns="9" columnClasses="colunaCentralizada" headerClass="subordinado" width="100%">
                        <a4j:commandLink id="marcCliente" styleClass="pure-button pure-button-small"
                                         onclick="fireElementFromAnyParent('form:btnAtualizaTempo')"
                                         reRender="formMarcado"
                                         rendered="#{(PlanoTextoPadraoControle.planoTextoPadraoVO.tipoContrato != 'AM')}"
                                         action="#{PlanoTextoPadraoControle.disponibilizarTextoParaEdicaoEditorFCK}"
                                         oncomplete="Richfaces.showModalPanel('panelCliente');"
                                         accesskey="1">Cliente</a4j:commandLink>
                        <a4j:commandLink id="marcContrato" styleClass="pure-button pure-button-small"
                                         onclick="fireElementFromAnyParent('form:btnAtualizaTempo')"
                                         rendered="#{!(PlanoTextoPadraoControle.planoTextoPadraoVO.tipoContrato == 'SE') && (PlanoTextoPadraoControle.planoTextoPadraoVO.tipoContrato != 'AM') && PlanoTextoPadraoControle.planoTextoPadraoVO.tipoContrato != 'CC'}" reRender="formMarcado" action="#{PlanoTextoPadraoControle.disponibilizarTextoParaEdicaoEditorFCK}"
                                         oncomplete="Richfaces.showModalPanel('panelContrato');"
                                         accesskey="1">Contrato</a4j:commandLink>
                        <a4j:commandLink id="marcVenda" styleClass="pure-button pure-button-small"
                                         onclick="fireElementFromAnyParent('form:btnAtualizaTempo')"
                                         rendered="#{((PlanoTextoPadraoControle.planoTextoPadraoVO.tipoContrato == 'SE') && (PlanoTextoPadraoControle.planoTextoPadraoVO.tipoContrato != 'AM')) || PlanoTextoPadraoControle.planoTextoPadraoVO.tipoContrato == 'CC'}" reRender="formMarcado" action="#{PlanoTextoPadraoControle.disponibilizarTextoParaEdicaoEditorFCK}"
                                         oncomplete="Richfaces.showModalPanel('panelVenda');"
                                         accesskey="1">Venda</a4j:commandLink>
                        <a4j:commandLink id="marcItensVenda" styleClass="pure-button pure-button-small"
                                         onclick="fireElementFromAnyParent('form:btnAtualizaTempo')"
                                         rendered="#{((PlanoTextoPadraoControle.planoTextoPadraoVO.tipoContrato == 'SE') && (PlanoTextoPadraoControle.planoTextoPadraoVO.tipoContrato != 'AM')) || (PlanoTextoPadraoControle.planoTextoPadraoVO.tipoContrato == 'CC')}" reRender="formMarcado" action="#{PlanoTextoPadraoControle.disponibilizarTextoParaEdicaoEditorFCK}"
                                         oncomplete="Richfaces.showModalPanel('panelItensVenda');"
                                         accesskey="1">Itens Venda</a4j:commandLink>
                        <a4j:commandLink id="marcPlano" styleClass="pure-button pure-button-small"
                                         onclick="fireElementFromAnyParent('form:btnAtualizaTempo')"
                                         rendered="#{!(PlanoTextoPadraoControle.planoTextoPadraoVO.tipoContrato == 'SE') && (PlanoTextoPadraoControle.planoTextoPadraoVO.tipoContrato != 'AM') && PlanoTextoPadraoControle.planoTextoPadraoVO.tipoContrato != 'CC'}" reRender="formMarcado" action="#{PlanoTextoPadraoControle.disponibilizarTextoParaEdicaoEditorFCK}"
                                         oncomplete="Richfaces.showModalPanel('panelPlano');"
                                         accesskey="1">Plano</a4j:commandLink>
                        <a4j:commandLink id="marcModalidade" styleClass="pure-button pure-button-small"
                                         onclick="fireElementFromAnyParent('form:btnAtualizaTempo')"
                                         rendered="#{!(PlanoTextoPadraoControle.planoTextoPadraoVO.tipoContrato == 'SE') && (PlanoTextoPadraoControle.planoTextoPadraoVO.tipoContrato != 'AM') && (PlanoTextoPadraoControle.planoTextoPadraoVO.tipoContrato != 'CC')}" reRender="formMarcado" action="#{PlanoTextoPadraoControle.disponibilizarTextoParaEdicaoEditorFCK}"
                                         oncomplete="Richfaces.showModalPanel('panelModalidade');"
                                         accesskey="1">Modalidade</a4j:commandLink>
                        <a4j:commandLink id="marcTurma" styleClass="pure-button pure-button-small"
                                         onclick="fireElementFromAnyParent('form:btnAtualizaTempo')"
                                         rendered="#{!(PlanoTextoPadraoControle.planoTextoPadraoVO.tipoContrato == 'SE') && (PlanoTextoPadraoControle.planoTextoPadraoVO.tipoContrato != 'AM') && (PlanoTextoPadraoControle.planoTextoPadraoVO.tipoContrato != 'CC')}" reRender="formMarcado" action="#{PlanoTextoPadraoControle.disponibilizarTextoParaEdicaoEditorFCK}"
                                         oncomplete="Richfaces.showModalPanel('panelTurma');"
                                         accesskey="1">Turma</a4j:commandLink>
                        <a4j:commandLink id="marcMovParcela" styleClass="pure-button pure-button-small"
                                         onclick="fireElementFromAnyParent('form:btnAtualizaTempo')"
                                         rendered="#{(PlanoTextoPadraoControle.planoTextoPadraoVO.tipoContrato != 'AM')}"
                                         reRender="formMarcado" action="#{PlanoTextoPadraoControle.disponibilizarTextoParaEdicaoEditorFCK}"
                                         oncomplete="Richfaces.showModalPanel('panelMovParcela');"
                                         accesskey="1">Mov. Parcela</a4j:commandLink>
                        <%--<a4j:commandLink id="marcRecibo" styleClass="pure-button pure-button-small"
                                         onclick="fireElementFromAnyParent('form:btnAtualizaTempo')"
                                         rendered="#{(PlanoTextoPadraoControle.planoTextoPadraoVO.tipoContrato != 'AM')}"
                                         reRender="formMarcado" action="#{PlanoTextoPadraoControle.disponibilizarTextoParaEdicaoEditorFCK}"
                                         oncomplete="Richfaces.showModalPanel('panelReciboPagamento');"
                                         accesskey="1">Recibo</a4j:commandLink>
                        <a4j:commandLink id="marcMovPagamento" styleClass="pure-button pure-button-small"
                                         onclick="fireElementFromAnyParent('form:btnAtualizaTempo')"
                                         rendered="#{(PlanoTextoPadraoControle.planoTextoPadraoVO.tipoContrato != 'AM')}"
                                         reRender="formMarcado" action="#{PlanoTextoPadraoControle.disponibilizarTextoParaEdicaoEditorFCK}"
                                         oncomplete="Richfaces.showModalPanel('panelMovPagamento');"
                                         accesskey="1">Mov. Pagamento</a4j:commandLink>--%>

                        <a4j:commandLink id="marcPacote" styleClass="pure-button pure-button-small"
                                         onclick="fireElementFromAnyParent('form:btnAtualizaTempo')"
                                         rendered="#{!(PlanoTextoPadraoControle.planoTextoPadraoVO.tipoContrato == 'SE') && (PlanoTextoPadraoControle.planoTextoPadraoVO.tipoContrato != 'AM') && (PlanoTextoPadraoControle.planoTextoPadraoVO.tipoContrato != 'CC')}" reRender="formMarcado" action="#{PlanoTextoPadraoControle.disponibilizarTextoParaEdicaoEditorFCK}"
                                         oncomplete="Richfaces.showModalPanel('panelComposicao');"
                                         accesskey="1">Pacote</a4j:commandLink>

                        <a4j:commandLink id="marcPacoteVenda" styleClass="pure-button pure-button-small"
                                         rendered="#{(PlanoTextoPadraoControle.planoTextoPadraoVO.tipoContrato == 'SE') && (PlanoTextoPadraoControle.planoTextoPadraoVO.tipoContrato != 'AM') && (PlanoTextoPadraoControle.planoTextoPadraoVO.tipoContrato != 'CC')}" reRender="formMarcado" action="#{PlanoTextoPadraoControle.disponibilizarTextoParaEdicaoEditorFCK}"
                                         oncomplete="Richfaces.showModalPanel('panelPacoteVenda');"
                                         accesskey="1">Pacote Venda</a4j:commandLink>

                        <a4j:commandLink id="marcUsuario" styleClass="pure-button pure-button-small"
                                         onclick="fireElementFromAnyParent('form:btnAtualizaTempo')"
                                         rendered="#{(PlanoTextoPadraoControle.planoTextoPadraoVO.tipoContrato != 'AM')}"
                                         reRender="formMarcado" action="#{PlanoTextoPadraoControle.disponibilizarTextoParaEdicaoEditorFCK}"
                                         oncomplete="Richfaces.showModalPanel('panelUsuario');"
                                         accesskey="1">Usu�rio</a4j:commandLink>

                        <a4j:commandLink id="marcEmpresa" styleClass="pure-button pure-button-small"
                                         onclick="fireElementFromAnyParent('form:btnAtualizaTempo')"
                                         rendered="#{(PlanoTextoPadraoControle.planoTextoPadraoVO.tipoContrato != 'AM')}"
                                         reRender="formMarcado" action="#{PlanoTextoPadraoControle.disponibilizarTextoParaEdicaoEditorFCK}"
                                         oncomplete="Richfaces.showModalPanel('panelEmpresa');"
                                         accesskey="1">Empresa</a4j:commandLink>
                    </h:panelGrid>
                </h:panelGrid>

                <h:panelGrid id="panelGridEditor" columns="1" rowClasses="linhaImpar, linhaPar" width="100%">
                    <f:facet name="footer">
                        <rich:editor id="richEditor" viewMode="visual" theme="advanced" configuration="editorModelopropriedades"
                                     height="250" width="735"  value="#{PlanoTextoPadraoControle.planoTextoPadraoVO.texto}"  />
                    </f:facet>
                </h:panelGrid>

                <h:panelGrid id="panelMensagem" columns="1" width="100%" styleClass="tabMensagens">
                    <h:outputText value="Tag = [exemplo_exemplo]"/>

                    <h:outputText value="Valor Num�rico = (??)"/>

                    <h:outputText value="Texto = {Exemplo}"/>

                    <h:outputText value="Exemplo: [(15){C�digo: }codigo_cliente]"/>

                    <h:panelGrid columns="3" width="100%" styleClass="tabMensagens">
                        <h:panelGrid columns="1" width="100%">
                            <h:outputText value=" "/>
                        </h:panelGrid>

                        <h:commandButton rendered="#{PlanoTextoPadraoControle.sucesso}" image="./imagens/sucesso.png"/>

                        <h:commandButton rendered="#{PlanoTextoPadraoControle.erro}" image="./imagens/erro.png"/>

                        <h:panelGrid columns="1" width="100%">
                            <h:outputText id="msgModelo" styleClass="mensagem"  value="#{PlanoTextoPadraoControle.mensagem}"/>
                            <h:outputText id="msgModeloDet" styleClass="mensagemDetalhada" value="#{PlanoTextoPadraoControle.mensagemDetalhada}"/>
                        </h:panelGrid>
                    </h:panelGrid>

                    <h:panelGrid columns="1" width="100%" columnClasses="colunaCentralizada">
                        <h:panelGroup>
                            <h:commandButton id="novo" immediate="true" action="#{PlanoTextoPadraoControle.novo}" value="#{msg_bt.btn_novo}"
                                             onclick="fireElementFromAnyParent('form:btnAtualizaTempo')"
                                             alt="#{msg.msg_novo_dados}" accesskey="1" styleClass="botoes nvoBt btSec"/>

                            <h:outputText value="    "/>

                            <h:commandButton id="salvar" action="#{PlanoTextoPadraoControle.gravar}" value="#{msg_bt.btn_gravar}"
                                             onclick="fireElementFromAnyParent('form:btnAtualizaTempo')"
                                             alt="#{msg.msg_gravar_dados}" accesskey="2" styleClass="botoes nvoBt"/>

                            <h:outputText value="    "/>


                            <h:panelGroup id="grupoMensagem">
                                <a4j:commandButton id="excluir" reRender="mdlMensagemGenerica"
                                                   oncomplete="#{PlanoTextoPadraoControle.msgAlert}" action="#{PlanoTextoPadraoControle.confirmarExcluir}" value="#{msg_bt.btn_excluir}"
                                                   alt="#{msg.msg_excluir_dados}" accesskey="3" styleClass="botoes nvoBt btSec btPerigo" style="border: 0px" />
                            </h:panelGroup>

                            <h:outputText value="    "/>
                            <h:panelGroup>
                            <a4j:commandLink id="imprimir" value="Imprimir Contrato"
                                              onclick="fireElementFromAnyParent('form:btnAtualizaTempo')"
                                               action="#{PlanoTextoPadraoControle.imprimirContrato}" reRender="panelMensagem"
                                               styleClass="botoes nvoBt btSec"
                                               style="display: inline-block; padding: 6px 15px;"
                                               oncomplete="#{PlanoTextoPadraoControle.abrirPDF}">
                            </a4j:commandLink>
                            </h:panelGroup>
                            <h:outputText value="    "/>

                            <h:commandButton id="consultar" immediate="true" action="#{PlanoTextoPadraoControle.inicializarConsultar}"
                                             onclick="fireElementFromAnyParent('form:btnAtualizaTempo')"
                                             value="#{msg_bt.btn_voltar_lista}" alt="#{msg.msg_consultar_dados}"
                                             accesskey="4" styleClass="botoes nvoBt btSec"/>
                            <h:outputText value="    "/>
                            <a4j:commandLink action="#{PlanoTextoPadraoControle.realizarConsultaLogObjetoSelecionado}"
                                               reRender="form"
                                               oncomplete="abrirPopup('visualizadorLogForm.jsp', 'VisualizadorLog', 785, 595);fireElementFromAnyParent('form:btnAtualizaTempo')"
                                               title="Visualizar Log" styleClass="botoes nvoBt btSec" style="display: inline-block;padding-bottom: 10px;">
                                <i class="fa-icon-list"></i>
                            </a4j:commandLink>
                        </h:panelGroup>
                    </h:panelGrid>
                </h:panelGrid>
            </h:panelGrid>
        </a4j:form>
    </h:panelGrid>
    <rich:jQuery id="mskData" selector=".rich-calendar-input" timing="onload" query="mask('99/99/9999')" />
    <%@include file="includes/include_modal_mensagem_generica.jsp" %>
</f:view>

<script>
    document.getElementById("form:descricao").focus();
</script>
