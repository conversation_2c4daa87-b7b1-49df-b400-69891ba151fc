<%@ page import="java.util.Map" %>
<%--
Document   : relatorioClientesForm
Created on : 03/05/2011
Author     : <PERSON><PERSON>
--%>

<%@page contentType="text/html"%>
<%@page pageEncoding="ISO-8859-1"%>
<script type="text/javascript" language="javascript" src="hoverform.js"></script>
<%@taglib prefix="f" uri="http://java.sun.com/jsf/core"%>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html"%>
<%@taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax"%>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich"%>
<%@taglib prefix="jsfChart" uri="http://sourceforge.net/projects/jsf-comp" %>

<link href="./css/otimize.css" rel="stylesheet" type="text/css">
<link href="beta/css/font-awesome_2.0.min.css" type="text/css" rel="stylesheet"/>
<link href="css_pacto.css" rel="stylesheet" type="text/css">
<link href="css/ce.css" rel="stylesheet" type="text/css">

<f:loadBundle var="msg_bt" basename="propriedades.Botoes"/>
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao"/>
<f:loadBundle var="msg" basename="propriedades.Mensagens"/>
<f:loadBundle var="msg_menu" basename="propriedades.Menu"/>
<body>
    <head><%@include file="/includes/include_import_minifiles.jsp" %></head>
    <%@include file="includes/imports.jsp" %>
    <f:view>
        <jsp:include page="includes/include_carregando_ripple.jsp" flush="true"/>
    <title>
        <h:outputText value="Relatório de Clientes"/>
    </title>

    <h:form id="form">
        <a4j:loadScript src="/script/jquery.maskedinput-1.2.2.js" />
        <table width="100%" align="center" height="100%" border="0" cellpadding="0" cellspacing="0">
            <tr>
                <td height="77" align="left" valign="top" class="bgtop">
                    <c:set var="titulo" scope="session" value="Relatório de Clientes"/>
                    <c:set var="urlWiki" scope="session"
                           value="${SuperControle.urlBaseConhecimento}como-utilizar-os-filtros-do-relatorio-de-clientes/"/>


                        <jsp:include page="topoReduzido_material.jsp"/>

                    <jsp:include page="include_head.jsp" flush="true"/>
                    <script type="text/javascript" >
                        function imprimirRelatorio(form) {
                            if (window.document.getElementById("form:relatorio").value == "sim") {
                                var hiddenCommandLink = window.document.getElementById("form:imprimirRelatorio");
                                if (hiddenCommandLink) {
		        
                                    //hiddenCommandLink.fireEvent("onclick");
                                    hiddenCommandLink.onclick();
                                }
                            } else {
                                form.submit();
                            }
                        }

                        function alternarDataLancamento(marcado){
                            var itens = document.getElementsByName('form:tipoFiltroPeriodo');
                            var ativosCheck = document.getElementById('ativosC');
                            var inativosCheck = document.getElementById('inativosC');
                            var renovadosCheck = document.getElementById('renovados');
                            var trancadosCheck = document.getElementById('trancadosC');
			
                            if(marcado.checked == 0){
                                itens[0].disabled=false;
                            }else{
                                if (itens[0].checked) {
                                    itens[1].click();
                                }
                                itens[0].disabled=true;
                                if(ativosCheck.checked == 0){
                                    ativosCheck.click();
                                }
                                if(inativosCheck.checked == 0){
                                    inativosCheck.click();
                                }
                                if(renovadosCheck.checked == 0){
                                    renovadosCheck.click();
                                }
                                if(trancadosCheck.checked == 0){
                                    trancadosCheck.click();
                                }
                            }
                        }

                        function alternarFiltro() {
                            var itens = document.getElementsByName('form:tipoFiltroPeriodo');
                            for (var i = 0; i < itens.length; i++) {
                                if (itens[i].checked) {
                                    var divFiltroData = document.getElementById('filtroData');
                                    var divFiltroIntervalo = document.getElementById('filtroIntervalo');
                                    var divFiltroMesSemana = document.getElementById('filtroMesSemana');
                                    switch (itens[i].value) {
                                        case '1':
                                            divFiltroData.style.display="block";
                                            divFiltroIntervalo.style.display="none";
                                            divFiltroMesSemana.style.display="none";
                                            break;
                                        case '2':
                                            divFiltroData.style.display="none";
                                            divFiltroIntervalo.style.display="block";
                                            divFiltroMesSemana.style.display="none";
                                            break;
                                        case '3':
                                            divFiltroData.style.display="none";
                                            divFiltroIntervalo.style.display="none";
                                            divFiltroMesSemana.style.display="block";
                                            break;
                                    }
                                }
                            }
                        }

                        function desmarcarVisitante(marcado){
                            var visitantes = document.getElementById('visitantesC');
                            var situacao = document.getElementById('form:fsituacao');
                            if(marcado.checked == 0){
                                if(visitantes.checked == 0){
                                    situacao.value = situacao.value.replace('VI;', '');
                                }
                            }
                        }

                        function atualizarAreaFiltros(){
                            var plano = document.getElementById('form:fplanodesc');
                            var situacao = document.getElementById('form:fsituacao');
                            var modalidade = document.getElementById('form:fmodalidadedesc');
                            var horario = document.getElementById('form:fhorariodesc');
                            var duracao = document.getElementById('form:fduracao');
                            var colaborador = document.getElementById('form:fcolaborador');
                            var situacaoContrato = document.getElementById('form:fsituacaocontrato');
                            var areaFiltros = document.getElementById('areaFiltros');
                            var filtros = '';
                            if(situacao.value != null && situacao.value != ''){
                                filtros += '<b>Situação: </b>'+ situacao.value;
                            }
                            if(plano.value != null && plano.value != ''){
                                if(filtros != '')
                                    filtros += '<br/>';
                                filtros += '<b>Plano: </b>'+ plano.value;
                            }
                            if(duracao.value != null && duracao.value != ''){
                                if(filtros != '')
                                    filtros += '<br/>';
                                filtros += '<b>Duração: </b>'+ duracao.value;
                            }
                            if(modalidade.value != null && modalidade.value != ''){
                                if(filtros != '')
                                    filtros += '<br/>';
                                filtros += '<b>Modalidade: </b>'+ modalidade.value;
                            }
                            if(horario.value != null && horario.value != ''){
                                if(filtros != '')
                                    filtros += '<br/>';
                                filtros += '<b>Horário: </b>'+ horario.value;
                            }
                            if(colaborador.value != null && colaborador.value != ''){
                                if(filtros != '')
                                    filtros += '<br/>';
                                filtros += '<b>Colaborador: </b>'+'<div style="width:1000px;padding:0px;text-align:justify;word-wrap: break-word;height: auto;">'+ colaborador.value+'</div>';
                            }
                            if(situacaoContrato.value != null && situacaoContrato.value != '') {
                                if (filtros != '') {
                                    filtros += '<br/>';
                                }
                                filtros += '<b>Situação dos Contratos: </b>' + situacaoContrato.value;
                            }
                            if(filtros != ''){
                                areaFiltros.innerHTML = '<h:outputText styleClass="text" escape="false" value="'+filtros+'"/>';
                            }else{
                                areaFiltros.innerHTML = '';
                            }
                        }

                        function limparFiltrosPlano(){
                            var plano = document.getElementById('form:fplano');
                            var planodesc = document.getElementById('form:fplanodesc');
                            var modalidade = document.getElementById('form:fmodalidade');
                            var modalidadedesc = document.getElementById('form:fmodalidadedesc');
                            var horario = document.getElementById('form:fhorario');
                            var horariodesc = document.getElementById('form:fhorariodesc');
                            var duracao = document.getElementById('form:fduracao');
                            plano.value = '';
                            planodesc.value = '';
                            modalidade.value = '';
                            modalidadedesc.value = '';
                            horario.value = '';
                            horariodesc.value = '';
                            duracao.value = '';
                        }

                        function limpar(){
                            limparFiltrosPlano();
                            alternarFiltro();
                            var colaborador = document.getElementById('form:fcolaborador');
                            var situacao = document.getElementById('form:fsituacao');
                            situacao.value = '';
                            colaborador.value = '';
                            atualizarAreaFiltros();
                        }

                        function limparFiltrosColaborador(){
                            var colaborador = document.getElementById('form:fcolaborador');
                            colaborador.value = '';
                        }

                        function adicionarOperacao(valorFiltro, tipoFiltro, marcado){
                            var recipienteFiltro = document.getElementById(tipoFiltro);
                            var filtro = valorFiltro+';';
                            // verificar se ainda não foi selecionada
                            if(marcado.checked == 1)
                            {
                                recipienteFiltro.value = recipienteFiltro.value + filtro;
                            }else{
                                if(tipoFiltro == 'form:fcolaborador'){
                                    var splitCol = recipienteFiltro.value.split(';');
                                    recipienteFiltro.value = '';
                                    for (var i = 0; i < splitCol.length; i++) {
                                        if(valorFiltro != splitCol[i] && splitCol[i] != ''){
                                          recipienteFiltro.value = recipienteFiltro.value + splitCol[i]+';';  
                                        } 
                                    }
                                }else {
                                    recipienteFiltro.value = recipienteFiltro.value.replace(filtro,'');
                                }
                            }
                        }

                        function marcarTodos(nomeInputs, marcado){
                            var controle = true;
                            var cont = 0;
                            while(controle){
                                var check = document.getElementById(nomeInputs+cont);
                              //  alert(check);
                                if (check != null){
                                    if(marcado.checked == 0){
                                        if(check.checked == 1){
                                            check.click();
                                        }
                                    }else{
                                        if(check.checked == 0){
                                            check.click();
                                        }
                                    }
                                }else{
                                    controle = false;
                                }
                                cont = cont+1;
                            }
                        }

                        function desmarcarTopo(submisso, topo){
                            var check = document.getElementById(topo);
                            if(submisso.checked == 0){
                                if(check.checked == 1){
                                    check.checked = 0;
                                }
                            }
                        }

                        function inicializar(){
                            var empresa = document.getElementById('form:empresa');
                            if (empresa.value != null && empresa.value != "") {
                                var ativosCheck = document.getElementById('ativosC');
                                ativosCheck.click();

                                var matricula = document.getElementById('form:matriculaC');
                                matricula.click();
                                var rematricula = document.getElementById('form:rematriculaC');
                                rematricula.click();
                                var renovacao = document.getElementById('form:renovacaoC');
                                renovacao.click();
                                var renovacao = document.getElementById('form:transferidoC');
                                renovacao.click();
                                atualizarAreaFiltros();
                            }
                        }
                    </script>
                    <style>
                        .titulo3SemDecoracao{
                            font-family:Arial, Helvetica, sans-serif;
                            font-size: 11px;
                            text-decoration: none;
                            line-height: normal;
                            font-weight: normal;
                            text-transform: none;
                            color: #0f4c6b;
                        }

                    </style>
                </td>
            </tr>
            <tr>
                <td align="center" valign="top"  >
                    <table width="100%" height="100%" border="0" cellpadding="0" cellspacing="0" >
                        <tr>
                            <td align="left" valign="top" style="padding-top:6px; padding-left:15px;">
                                <!-- ----------------INICIO - FILTROS ------------------------------------>
                                <table width="100%" border="0" cellspacing="0" cellpadding="0" style="padding-bottom:10px;">
                                    <tr>
                                        <td align="left" valign="top">
                                            <rich:panel>

                                                <div id="areaFiltros"></div>
                                            </rich:panel>
                                            <rich:tabPanel switchType="client" id="tabPanelFiltros" width="100%">
                                                <!-- ------------------- INICIO - FILTRO DE DATAS ---------------------  -->
                                                <rich:tab id="abaData"  styleClass="titulo3SemDecoracao" label="Datas" >
                                                    <!-- --------------- Combo de empresa -------------------------- -->
                                                    <h:outputText styleClass="text"	value="Empresa: " />&nbsp;
                                                    <h:selectOneMenu id="empresa" value="#{RelatorioClientesControle.filtrosConsulta.codigoEmpresa}"
                                                                     onchange="document.getElementById('form:botaoEdicao').click()">
                                                        <f:selectItem itemValue="0" itemLabel="TODAS"/>
                                                        <f:selectItems value="#{RelatorioClientesControle.listaEmpresa}" />

                                                    </h:selectOneMenu><br/><br/>
                                                    <a4j:commandButton id="botaoEdicao" action="#{RelatorioClientesControle.inicializarEmpresa}" style="visibility: hidden;"
                                                                       reRender="tabPanelFiltros" oncomplete="limpar();"  />
                                                    <h:selectBooleanCheckbox value="#{RelatorioClientesControle.filtrosConsulta.filtrarDataLancamento}" onclick="alternarDataLancamento(this);"/>
                                                    <h:outputText styleClass="text" value="Filtrar por data de lançamento"></h:outputText>
                                                    <h:selectOneRadio style="align: left;" id="tipoFiltroPeriodo" onclick="alternarFiltro();"
                                                                      layout="pageDirection" styleClass="text"
                                                                      value="#{RelatorioClientesControle.tipoFiltroData}">
                                                        <f:selectItems value="#{RelatorioClientesControle.tiposFiltroData}" />
                                                    </h:selectOneRadio>

                                                    <div id="filtroData" style="clear:both; display: block;"
                                                         ><rich:calendar
                                                            id="dataExata" styleClass="form"
                                                            value="#{RelatorioClientesControle.filtrosConsulta.data}" verticalOffset="-60"
                                                            datePattern="dd/MM/yyyy"
                                                            inputSize="10" inputClass="form" 
                                                            oninputchange="return validar_Data(this.id);"
                                                            enableManualInput="true" zindex="2" showWeeksBar="false" /></div>



                                                    <div id="filtroMesSemana" style="display: none; clear:both;"><h:panelGrid
                                                            columns="1">
                                                            <h:panelGroup>
                                                                <h:outputText styleClass="tituloCampos"
                                                                              value="#{CElabels['entidade.consultaMes']}: " />
                                                                <br />
                                                                <h:selectOneMenu onblur="blurinput(this);" onfocus="focusinput(this);" id="meses"
                                                                                 value="#{RelatorioClientesControle.filtrosConsulta.codigoMes}"
                                                                                 style="width:175px">
                                                                    <f:selectItems value="#{RelatorioClientesControle.meses}" />
                                                                    <a4j:support event="onchange" action="#{RelatorioClientesControle.atualizarSemanas}" reRender="semanas"></a4j:support>
                                                                </h:selectOneMenu>
                                                                <br />
                                                            </h:panelGroup>

                                                            <h:panelGroup>
                                                                <h:outputText styleClass="tituloCampos"
                                                                              value="#{CElabels['entidade.semana']}: " />
                                                                <br />
                                                                <h:selectOneMenu
                                                                    onblur="blurinput(this);" onfocus="focusinput(this);" id="semanas"
                                                                    value="#{RelatorioClientesControle.filtrosConsulta.codigoSemana}"
                                                                    style="width:175px">
                                                                    <f:selectItem itemValue=""
                                                                                  itemLabel="#{CElabels['operacoes.selecione']}" />
                                                                    <f:selectItems value="#{RelatorioClientesControle.semanas}" />
                                                                </h:selectOneMenu>
                                                            </h:panelGroup>
                                                        </h:panelGrid></div>
                                                    <div id="filtroIntervalo" style="display: none; clear:both;">
                                                        <h:outputText		styleClass="tituloCampos" value="#{CElabels['entidade.data']}: " /><br />
                                                        <h:outputText styleClass="tituloCampos"
                                                                      value="#{CElabels['entidade.data.de']} " /> <rich:calendar
                                                                      id="dataInicio" styleClass="form"
                                                                      value="#{RelatorioClientesControle.filtrosConsulta.dataInicial}" verticalOffset="-60"
                                                                      datePattern="dd/MM/yyyy"
                                                                      inputSize="10" inputClass="form" 
                                                                      oninputchange="return validar_Data(this.id);"
                                                                      enableManualInput="true" zindex="2" showWeeksBar="false" /><br />

                                                        <br />
                                                        <h:outputText styleClass="tituloCampos"
                                                                      value="#{CElabels['entidade.dataAte']} " /> <rich:calendar
                                                                      id="dataFim" styleClass="form"
                                                                      value="#{RelatorioClientesControle.filtrosConsulta.dataFinal}" verticalOffset="-70"
                                                                      horizontalOffset="-50" datePattern="dd/MM/yyyy" inputSize="10"
                                                                      inputClass="form" 
                                                                      oninputchange="return validar_Data(this.id);"
                                                                      enableManualInput="true" zindex="2" showWeeksBar="false" /></div>
                                                    <rich:jQuery id="mskData" selector=".rich-calendar-input" timing="onload" query="mask('99/99/9999')" />

                                                </rich:tab>
                                                <!-- ------------------- INICIO - FILTRO DE SITUACOES ---------------------  -->
                                                <rich:tab id="abaSituacoes"  styleClass="titulo3SemDecoracao" label="Situações" >
                                                    <table id="tableVisitantes" width="100%">
                                                        <tr>
                                                            <!-- VISITANTES -->
                                                            <td valign="top" width="20%"><fieldset><legend>
                                                                        <input id="visitantesC" onclick="marcarTodos('visitante', this); atualizarAreaFiltros();" type="checkbox" /> <h:outputText styleClass="text"
                                                                                      style="font-weight: bold;" value="Visitantes" /> </legend>
                                                                    <table class="text" width="100%">
                                                                        <c:forEach varStatus="index" items="${RelatorioClientesControle.situacoesVisitante}" var="sitVisitante">

                                                                            <c:set var="indexVis" scope="request"
                                                                            value='<%= "visitante" + ((javax.servlet.jsp.jstl.core.LoopTagStatus) pageContext.getAttribute("index")).getIndex()%>' />
                                                                            <tr>
                                                                                <td valign="top" >
                                                                                    <input id="${indexVis}" type="checkbox"  onclick="adicionarOperacao('${sitVisitante.codigo}', 'form:fsituacao', this); desmarcarTopo(this, 'visitantesC'); desmarcarVisitante(this);atualizarAreaFiltros();" />
                                                                                    <c:out value="${sitVisitante.descricao}"/>
                                                                                </td>
                                                                            </tr>
                                                                        </c:forEach>

                                                                    </table>
                                                                </fieldset>



                                                            </td><td valign="top" width="20%">
                                                                <!-- TRANCADOS -->
                                                                <fieldset><legend><h:outputText styleClass="text" style="font-weight: bold;" value="Renovados(Inativos)" /></legend>
                                                                    <input id="renovados" type="checkbox"  onclick="adicionarOperacao('${RelatorioClientesControle.situacaoRenovado.codigo}', 'form:fsituacao', this);atualizarAreaFiltros();" />
                                                                    <h:outputText styleClass="text" value="#{RelatorioClientesControle.situacaoRenovado.descricao}"/>
                                                                </fieldset>
                                                                <fieldset><legend><input id="trancadosC" onclick="marcarTodos('trancado', this);" type="checkbox" /> <h:outputText styleClass="text"
                                                                              style="font-weight: bold;" value="Trancados" /> </legend>
                                                                    <table class="text" width="100%">
                                                                        <c:forEach varStatus="index" items="${RelatorioClientesControle.situacoesTrancado}" var="sitTrancado">
                                                                            <c:set var="indexTr" scope="request"
                                                                            value='<%= "trancado" + ((javax.servlet.jsp.jstl.core.LoopTagStatus) pageContext.getAttribute("index")).getIndex()%>'/>

                                                                            <tr>
                                                                                <td valign="top" >
                                                                                    <input id="${indexTr}" type="checkbox" onclick="adicionarOperacao('${sitTrancado.codigo}', 'form:fsituacao', this); desmarcarTopo(this,'trancadosC');atualizarAreaFiltros();" />
                                                                                    <c:out value="${sitTrancado.descricao}"/>
                                                                                </td>
                                                                            </tr>
                                                                        </c:forEach>
                                                                    </table>
                                                                </fieldset>

                                                            </td>

                                                            <!-- ATIVOS -->
                                                            <td valign="top" width="20%">
                                                                <fieldset><legend>
                                                                        <input id="ativosC" onclick="marcarTodos('ativo', this);atualizarAreaFiltros();" type="checkbox" />
                                                                        <h:outputText styleClass="text" style="font-weight: bold;"
                                                                                      value="Ativos" /></legend>
                                                                    <table class="text" width="100%">
                                                                        <c:forEach varStatus="index" items="${RelatorioClientesControle.situacoesAtivo}" var="sitAtivo">

                                                                            <c:set var="indexAt" scope="request"
                                                                            value='<%= "ativo" + ((javax.servlet.jsp.jstl.core.LoopTagStatus) pageContext.getAttribute("index")).getIndex()%>'/>
                                                                            <tr>
                                                                                <td valign="top" >
                                                                                    <input id="${indexAt}" type="checkbox" onclick="adicionarOperacao('${sitAtivo.codigo}', 'form:fsituacao', this); desmarcarTopo(this, 'ativosC');atualizarAreaFiltros();" />
                                                                                    <c:out value="${sitAtivo.descricao}"/>
                                                                                </td>
                                                                            </tr>
                                                                        </c:forEach>
                                                                    </table></fieldset>

                                                            </td><td valign="top" width="20%">
                                                                <!-- INATIVOS -->

                                                                <fieldset><legend>
                                                                        <input id="inativosC" type="checkbox" onclick="marcarTodos('inativo', this);atualizarAreaFiltros();" />
                                                                        <h:outputText styleClass="text" style="font-weight: bold;"
                                                                                      value="Inativos" /></legend>
                                                                    <table width="100%" class="text">
                                                                        <c:forEach varStatus="index" items="${RelatorioClientesControle.situacoesInativo}" var="sitInativo">
                                                                            <c:set var="indexIn" scope="request"
                                                                            value='<%= "inativo" + ((javax.servlet.jsp.jstl.core.LoopTagStatus) pageContext.getAttribute("index")).getIndex()%>'/>
                                                                            <tr>
                                                                                <td valign="top" >
                                                                                    <input id="${indexIn}" type="checkbox" onclick="adicionarOperacao('${sitInativo.codigo}', 'form:fsituacao', this); desmarcarTopo(this,'inativosC');atualizarAreaFiltros();" />
                                                                                    <c:out value="${sitInativo.descricao}"/>
                                                                                </td>
                                                                            </tr>
                                                                        </c:forEach>
                                                                    </table></fieldset>

                                                            </td>

                                                            <td valign="top" width="20%">
                                                                <!-- PARCELAS -->
                                                                <fieldset><legend><h:outputText styleClass="text" style="font-weight: bold;" value="Parcelas" /></legend>

                                                                    <h:selectBooleanCheckbox style="align: left;"
                                                                                             id="semParcelasVencidas"
                                                                                             styleClass="text"
                                                                                             value="#{RelatorioClientesControle.filtrosConsulta.semParcelasVencidas}"/>
                                                                    <h:outputText styleClass="text" value="Sem parcelas vencidas"/>
                                                                </fieldset>
                                                            </td>

                                                        </tr>
                                                    </table>

                                                </rich:tab>
                                                <rich:tab id="abaSituacaoContrato" styleClass="titulo3SemDecoracao" label="Situação Contrato">
                                                    <fieldset>
                                                        <legend>
                                                            <h:outputText value="Consultar por Situação do Contrato:" style="font-weight: bold" styleClass="text"/>
                                                        </legend>
                                                        <h:selectBooleanCheckbox id="matriculaC" onclick="adicionarOperacao('MA', 'form:fsituacaocontrato', this); atualizarAreaFiltros();" value="#{RelatorioClientesControle.contratoMatricula}"/>
                                                        <h:outputText styleClass="text" value="Matrícula"/> &nbsp;
                                                        <h:selectBooleanCheckbox id="rematriculaC" onclick="adicionarOperacao('RE', 'form:fsituacaocontrato', this); atualizarAreaFiltros();" value="#{RelatorioClientesControle.contratoRematricula}"/>
                                                        <h:outputText styleClass="text" value="Rematrícula"/> &nbsp;
                                                        <h:selectBooleanCheckbox id="renovacaoC" onclick="adicionarOperacao('RN', 'form:fsituacaocontrato', this); atualizarAreaFiltros();" value="#{RelatorioClientesControle.contratoRenovacao}"/>
                                                        <h:outputText styleClass="text" value="Renovação"/>
                                                        <h:selectBooleanCheckbox id="transferidoC" onclick="adicionarOperacao('TF', 'form:fsituacaocontrato', this); atualizarAreaFiltros();" value="#{RelatorioClientesControle.contratoTransferido}"/>
                                                        <h:outputText styleClass="text" value="Transferido"/>
                                                    </fieldset>
                                                </rich:tab>
                                                <!-- ------------------- INICIO - FILTRO DE PLANOS ---------------------  -->
                                                <rich:tab id="abaPlanos"  styleClass="titulo3SemDecoracao" label="Planos" >

                                                    <fieldset><legend><h:outputText value="Consultar por Planos:" style="font-weight: bold"  styleClass="text" /></legend>
                                                        <h:selectBooleanCheckbox
                                                                onclick="limparFiltrosPlano();atualizarAreaFiltros();"
                                                                value="#{RelatorioClientesControle.planoAtivo}">
                                                            <a4j:support event="onchange"
                                                                         action="#{RelatorioClientesControle.inicializarFiltroPlano}"
                                                                         reRender="plano"/>
                                                        </h:selectBooleanCheckbox>
                                                        <h:outputText styleClass="text" value="Ativos"/> &nbsp;

                                                        <h:selectBooleanCheckbox
                                                                onclick="limparFiltrosPlano(); atualizarAreaFiltros();"
                                                                value="#{RelatorioClientesControle.planoInativo}">
                                                            <a4j:support event="onchange"
                                                                         action="#{RelatorioClientesControle.inicializarFiltroPlano}"
                                                                         reRender="plano"/>
                                                        </h:selectBooleanCheckbox>
                                                        <h:outputText styleClass="text" value="Inativos"/>

                                                        <h:selectBooleanCheckbox
                                                                onclick="limparFiltrosPlano(); atualizarAreaFiltros();"
                                                                value="#{RelatorioClientesControle.planoComPermissaoVenda}">
                                                            <a4j:support event="onchange"
                                                                         action="#{RelatorioClientesControle.inicializarFiltroPlano}"
                                                                         reRender="plano"/>
                                                        </h:selectBooleanCheckbox>
                                                        <h:outputText styleClass="text" value="Planos com permissão de venda"/>
                                                        </fieldset>
                                                        <br/>
                                                    <h:panelGroup id="plano">
                                                        <table  class="text" width="100%">
                                                            <tr>
                                                                <c:forEach var="plano"
                                                                           items="${RelatorioClientesControle.filtrosConsulta.planos}"
                                                                           varStatus="index">
                                                                    <c:set var="cont"
                                                                    value='<%= ((javax.servlet.jsp.jstl.core.LoopTagStatus) pageContext.getAttribute("index")).getIndex() + ""%>' />
                                                                    <c:if test="${cont % 4 == 0}">
                                                                    </tr>
                                                                    <tr>
                                                                    </c:if>
                                                                    <td width="25%" valign="top">
                                                                        <fieldset><legend>
                                                                                <!-- NOME DO PLANO -->
                                                                                <h:outputText styleClass="text" style="font-weight: bold;" value="Plano: "/>
                                                                                <input type="checkbox" onclick="adicionarOperacao('F${plano.codigo}F', 'form:fplano', this);adicionarOperacao('${plano.descricao}', 'form:fplanodesc', this); atualizarAreaFiltros();"/>
                                                                                <c:out value="${plano.descricao}"/> </legend>
                                                                            <!-- DURACOES -->
                                                                            <br/>
                                                                            <h:outputText styleClass="text" value="Durações :"/>
                                                                            <table width="100%" class="text">
                                                                                <c:forEach varStatus="index" var="duracao"  items="${plano.planoDuracaoVOs}">
                                                                                    <c:set var="indexDu" scope="request"
                                                                                    value='<%= "duracao" + ((javax.servlet.jsp.jstl.core.LoopTagStatus) pageContext.getAttribute("index")).getIndex()%>'/>

                                                                                    <tr>
                                                                                        <td>
                                                                                            <input id="${indexDu}${plano.descricao}" type="checkbox" onclick="adicionarOperacao('${duracao.numeroMeses}', 'form:fduracao', this);atualizarAreaFiltros();" />
                                                                                            <c:out value="${duracao.numeroMeses} meses"/>
                                                                                        </td>
                                                                                    </tr>
                                                                                </c:forEach>
                                                                            </table>
                                                                            <!-- MODALIDADES -->
                                                                            <h:outputText styleClass="text" value="Modalidades :"/>
                                                                            <table  width="100%" class="text">
                                                                                <c:forEach varStatus="index" var="modalidade"  items="${plano.planoModalidadeVOs}">
                                                                                    <c:set var="indexMo" scope="request"
                                                                                    value='<%= "modalidade" + ((javax.servlet.jsp.jstl.core.LoopTagStatus) pageContext.getAttribute("index")).getIndex()%>'/>

                                                                                    <tr>
                                                                                        <td>
                                                                                            <input id="${indexMo}${plano.descricao}" type="checkbox" onclick="adicionarOperacao('F${modalidade.modalidade.codigo}F', 'form:fmodalidade', this);adicionarOperacao('${modalidade.modalidade.nome}', 'form:fmodalidadedesc', this);atualizarAreaFiltros();" />
                                                                                            <c:out value="${modalidade.modalidade.nome} "/>
                                                                                        </td>
                                                                                    </tr>
                                                                                </c:forEach>
                                                                            </table>
                                                                            <!-- 	HORARIOS  -->
                                                                            <c:if test="${not empty plano.planoHorarioVOs}">
                                                                                <h:outputText styleClass="text" value="Horários :"/>
                                                                                <table width="100%" class="text">
                                                                                    <c:forEach varStatus="index" var="horario"  items="${plano.planoHorarioVOs}">
                                                                                        <c:set var="indexHo" scope="request"
                                                                                        value='<%= "horario" + ((javax.servlet.jsp.jstl.core.LoopTagStatus) pageContext.getAttribute("index")).getIndex()%>'/>
                                                                                        <tr>
                                                                                            <td>
                                                                                                <input id="${indexHo}${plano.descricao}" type="checkbox" onclick="adicionarOperacao('F${horario.horario.codigo}F', 'form:fhorario', this);adicionarOperacao('${horario.horario.descricao}', 'form:fhorariodesc', this);atualizarAreaFiltros();" />
                                                                                                <c:out value="${horario.horario.descricao} "/>
                                                                                            </td>
                                                                                        </tr>
                                                                                    </c:forEach>
                                                                                </table>

                                                                            </c:if>
                                                                        </fieldset>
                                                                    </td>
                                                                </c:forEach>
                                                            </tr>
                                                        </table>
                                                    </h:panelGroup>


                                                </rich:tab>
                                                <!-- ---------------- INICIO  - FILTROS COLABORADORES ------------------------------------>
                                                <rich:tab id="abaColaboradores"  styleClass="titulo3SemDecoracao" label="Colaboradores" >

                                                    <fieldset><legend><h:outputText value="Consultar por Colaboradores:" style="font-weight: bold"  styleClass="text" /></legend>
                                                        <h:selectBooleanCheckbox value="#{RelatorioClientesControle.colaboradorAtivo}" onclick="limparFiltrosColaborador();atualizarAreaFiltros();">
                                                            <a4j:support event="onchange"
                                                                         action="#{RelatorioClientesControle.inicializarFiltroConsultor}"
                                                                         reRender="colaboradores"/>
                                                        </h:selectBooleanCheckbox> <h:outputText styleClass="text" value="Ativos"/> &nbsp;
                                                        <h:selectBooleanCheckbox value="#{RelatorioClientesControle.colaboradorInativo}" onclick="limparFiltrosColaborador();atualizarAreaFiltros();">
                                                            <a4j:support event="onchange"
                                                                         action="#{RelatorioClientesControle.inicializarFiltroConsultor}"
                                                                         reRender="colaboradores"/>
                                                        </h:selectBooleanCheckbox> <h:outputText styleClass="text" value="Inativos"/>
                                                    </fieldset>

                                                    <br/>
                                                    <h:panelGroup id="colaboradores">
                                                        <table class="text" width="100%">
                                                            <tr>
                                                                <c:forEach var="tiposColaborador"
                                                                           items="${RelatorioClientesControle.filtrosConsulta.colaboradores}"
                                                                           varStatus="index">
                                                                    <c:set var="cont"
                                                                    value='<%=  ((javax.servlet.jsp.jstl.core.LoopTagStatus) pageContext.getAttribute("index")).getIndex() + ""%>' />
                                                                    <c:if test="${cont % 4 == 0}">
                                                                    </tr>
                                                                    <tr>
                                                                    </c:if>



                                                                    <td width="25%" valign="top" style="font-weight: bold"  class="text">
                                                                        <fieldset> <legend><input id="colaboradorC${tiposColaborador.tipo}" onclick="marcarTodos('${tiposColaborador.tipo}', this); atualizarAreaFiltros();" type="checkbox" /><span class="text" style="font-weight: bold;">${tiposColaborador.tipo}</span></legend>
                                                                            <table width="100%"  class="text">

                                                                                <c:forEach var="colaborador" varStatus="index" items="${tiposColaborador.lista}">
                                                                                    <c:set var="indexCo" scope="request"
                                                                                           value='<%= ((Map<String, Object>)pageContext.getAttribute("tiposColaborador")).get("tipo").toString()+ ((javax.servlet.jsp.jstl.core.LoopTagStatus) pageContext.getAttribute("index")).getIndex()%>' />
                                                                                    <c:if test="${((index.index+1)%2)!=0}">
                                                                                    <tr>
                                                                                        <td valign="top">
                                                                                            <input id="${indexCo}" type="checkbox" onclick="adicionarOperacao('${colaborador.codigo}|${tiposColaborador.tipo}', 'form:fcolaborador', this);atualizarAreaFiltros(); desmarcarTopo(this,'colaboradorC${tiposColaborador.tipo}');atualizarAreaFiltros();" />
                                                                                            <c:out value="${colaborador.pessoa.nome} "/>
                                                                                    <c:if test="${index.last}">
                                                                                        </tr>
                                                                                    </c:if>

                                                                                    </c:if>
                                                                                    <c:if test="${((index.index+1)%2)==0}">

                                                                                            <td valign="top">
                                                                                                <input id="${indexCo}"  type="checkbox" onclick="adicionarOperacao('${colaborador.codigo}|${tiposColaborador.tipo}', 'form:fcolaborador', this);atualizarAreaFiltros(); desmarcarTopo(this,'colaboradorC');atualizarAreaFiltros();" />
                                                                                                <c:out value="${colaborador.pessoa.nome} "/>
                                                                                            </td>
                                                                                        </tr>
                                                                                    </c:if>
                                                                                </c:forEach>
                                                                            </table>

                                                                        </fieldset></td>

                                                                </c:forEach>
                                                            </tr>
                                                        </table>
                                                    </h:panelGroup>

                                                </rich:tab>
                                                <!-- ---------------- INICIO  - ESCOLHER COLUNAS / AGRUPAMENTO ------------------------------------>
                                                <rich:tab id="escolherColunas"  styleClass="titulo3SemDecoracao" label="Colunas" >
                                                    <h:panelGroup id="colunasAgrupamento" >
                                                        <h:panelGroup  rendered="#{!RelatorioClientesControle.consultaPaginada}" >
                                                            <h:outputText styleClass="text" style="font-weight: bold;" value="Agrupar por :" /> &nbsp;

                                                            <h:selectOneMenu  id="agruparPor" value="#{RelatorioClientesControle.colunaAgrupada}">
                                                                <f:selectItem itemValue="" itemLabel="#{CElabels['operacoes.selecione']}"/>
                                                                <f:selectItem itemValue="situacaocol" itemLabel="#{msg_aplic.prt_RelatorioCliente_Situacao}"/>
                                                                <f:selectItem itemValue="empresacol" itemLabel="#{msg_aplic.prt_RelatorioCliente_empresa}"/>
                                                                <f:selectItem itemValue="vinculocol" itemLabel="#{msg_aplic.prt_RelatorioCliente_vinculo}"/>
                                                                <f:selectItem itemValue="planocol" itemLabel="#{msg_aplic.prt_RelatorioCliente_plano}"/>
                                                                <f:selectItem itemValue="contratocol" itemLabel="#{msg_aplic.prt_RelatorioCliente_contrato}"/>
                                                                <f:selectItem itemValue="modalidadecol" itemLabel="#{msg_aplic.prt_RelatorioCliente_modalidade}"/>
                                                                <f:selectItem itemValue="duracaocol" itemLabel="#{msg_aplic.prt_RelatorioCliente_duracao}"/>
                                                                <f:selectItem itemValue="horariocol" itemLabel="#{msg_aplic.prt_RelatorioCliente_horario}"/>
                                                                <f:selectItem itemValue="inciocol" itemLabel="#{msg_aplic.prt_RelatorioCliente_inicio}"/>
                                                                <f:selectItem itemValue="vencecol" itemLabel="#{msg_aplic.prt_RelatorioCliente_vence}"/>
                                                                <f:selectItem itemValue="valorcol" itemLabel="#{msg_aplic.prt_RelatorioCliente_valor}"/>
                                                            </h:selectOneMenu>
                                                            <br/>
                                                            &nbsp;
                                                            <br/>
                                                        </h:panelGroup>

                                                        <h:outputText styleClass="text" style="font-weight: bold;" value="Colunas :" /> &nbsp;

                                                        <table width="100%">

                                                            <tr>
                                                                <td valign="top" width="25%">
                                                                    <h:selectBooleanCheckbox value="#{RelatorioClientesControle.colMatricula}"/>
                                                                    <h:outputText styleClass="text" value="#{msg_aplic.prt_RelatorioCliente_Matricula}" />
                                                                    <br/>
                                                                    <h:selectBooleanCheckbox value="#{RelatorioClientesControle.colNome}"/>
                                                                    <h:outputText styleClass="text" value="#{msg_aplic.prt_RelatorioCliente_Nome}" />
                                                                    <br/>
                                                                    <h:selectBooleanCheckbox value="#{RelatorioClientesControle.colEmpresa}"/>
                                                                    <h:outputText styleClass="text" value="#{msg_aplic.prt_RelatorioCliente_Empresa}" />
                                                                    <br/>
                                                                    <h:selectBooleanCheckbox value="#{RelatorioClientesControle.colSituacao}"/>
                                                                    <h:outputText styleClass="text" value="#{msg_aplic.prt_RelatorioCliente_Situacao}" />
                                                                    <br/>
                                                                    <h:selectBooleanCheckbox value="#{RelatorioClientesControle.colVinculo}"/>
                                                                    <h:outputText styleClass="text" value="#{msg_aplic.prt_RelatorioCliente_vinculo}" />
                                                                    <br/>
                                                                </td>

                                                                <td valign="top" width="25%">
                                                                    <h:selectBooleanCheckbox value="#{RelatorioClientesControle.colDuracao}"/>
                                                                    <h:outputText styleClass="text" value="#{msg_aplic.prt_RelatorioCliente_duracao}" />
                                                                    <br/>
                                                                    <h:selectBooleanCheckbox value="#{RelatorioClientesControle.colContrato}"/>
                                                                    <h:outputText styleClass="text" value="#{msg_aplic.prt_RelatorioCliente_contrato}" />
                                                                    <br/>
                                                                    <h:selectBooleanCheckbox value="#{RelatorioClientesControle.colModalidade}"/>
                                                                    <h:outputText styleClass="text" value="#{msg_aplic.prt_RelatorioCliente_modalidade}" />
                                                                    <br/>
                                                                    <h:selectBooleanCheckbox value="#{RelatorioClientesControle.colInicio}"/>
                                                                    <h:outputText styleClass="text" value="#{msg_aplic.prt_RelatorioCliente_inicio}" />
                                                                    <br/>
                                                                </td>

                                                                <td valign="top" width="25%">

                                                                    <h:selectBooleanCheckbox value="#{RelatorioClientesControle.colVence}"/>
                                                                    <h:outputText styleClass="text" value="#{msg_aplic.prt_RelatorioCliente_vence}" />
                                                                    <br/>
                                                                    <h:selectBooleanCheckbox value="#{RelatorioClientesControle.colValor}"/>
                                                                    <h:outputText styleClass="text" value="#{msg_aplic.prt_RelatorioCliente_valor}" />
                                                                    <br/>
                                                                    <h:selectBooleanCheckbox value="#{RelatorioClientesControle.colPlano}"/>
                                                                    <h:outputText styleClass="text" value="#{msg_aplic.prt_RelatorioCliente_plano}" />
                                                                    <br/>
                                                                    <h:selectBooleanCheckbox value="#{RelatorioClientesControle.colHorario}"/>
                                                                    <h:outputText styleClass="text" value="#{msg_aplic.prt_RelatorioCliente_horario}" />
                                                                    <br/>
                                                                </td>
                                                                <td valign="top" width="25%">
                                                                    <h:selectBooleanCheckbox value="#{RelatorioClientesControle.colValorModalidade}"/>
                                                                    <h:outputText styleClass="text" value="#{msg_aplic.prt_RelatorioCliente_valorModalidade}" />
                                                                    <br/>
                                                                    <h:selectBooleanCheckbox value="#{RelatorioClientesControle.colDataLancamento}"/>
                                                                    <h:outputText styleClass="text" value="#{msg_aplic.prt_RelatorioCliente_dtLancamento}" />
                                                                    <br/>
                                                                    <h:selectBooleanCheckbox value="#{RelatorioClientesControle.colCategoriaCliente}"/>
                                                                    <h:outputText styleClass="text" value="#{msg_aplic.prt_RelatorioCliente_categoriaCliente}" />
                                                                    <br/>
                                                                    <h:selectBooleanCheckbox value="#{RelatorioClientesControle.colNivelTurma}"/>
                                                                    <h:outputText styleClass="text" value="#{msg_aplic.prt_RelatorioCliente_nivelTurma}" />

                                                                </td>
                                                            </tr>
                                                        </table>
                                                        <h:panelGroup layout="block" styleClass="container-botoes ">
                                                            <a4j:commandLink
                                                                    rendered="#{!RelatorioClientesControle.consultaPaginada}"
                                                                    title="Atualizar Tabela" reRender="resultadoRel"
                                                                    styleClass="linkPadrao texto-cor-azul texto-size-14-real">
                                                                <span>Atualizar </span>
                                                                <i class="fa-icon-refresh"></i>
                                                            </a4j:commandLink>
                                                            <a4j:commandLink
                                                                    rendered="#{RelatorioClientesControle.consultaPaginada}"
                                                                    title="Atualizar Tabela" reRender="resultadoRelPag"
                                                                    styleClass="linkPadrao texto-cor-azul texto-size-14-real">
                                                                <span>Atualizar </span>
                                                                <i class="fa-icon-refresh"></i>
                                                            </a4j:commandLink>
                                                        </h:panelGroup>

                                                    </h:panelGroup>
                                                </rich:tab>
                                            </rich:tabPanel>
                                            <!-- ----------------FIM  - FILTROS ------------------------------------>
                                        </td>
                                    </tr>
                                    <tr>
                                        <!-- ----------------BOTOES DE BUSCA E IMPRESSAO ------------------------------------>
                                        <td align="left" valign="top" >

                                            <rich:spacer style="display:block;" height="10"/>

                                            <h:panelGrid id="botoes" columns="2">
                                                <h:panelGroup>
                                                    <h:panelGroup>
                                                        <a4j:commandLink id="consultaRelatorio" value="Buscar"
                                                                           action="#{RelatorioClientesControle.consultarComFiltros}"
                                                                           styleClass="botaoPrimario texto-size-16-real"
                                                                           reRender="botoes, areaResultados, totais,mensagens, dataInicio, dataFim"/> &nbsp;

                                                        <a4j:commandButton id="exportarExcel"
                                                                           actionListener="#{RelatorioClientesControle.exportarConsulta}"
                                                                           rendered="#{not empty RelatorioClientesControle.listaRelatorioClientes}"
                                                                           value="Excel"
                                                                           image="/imagens/btn_excel.png"
                                                                           oncomplete="#{ExportadorListaControle.mensagemNotificar}#{ExportadorListaControle.operacaoOnComplete}#{ExportadorListaControle.msgAlert}"
                                                                           accesskey="2" styleClass="botoes">
                                                            <f:attribute name="lista" value="#{RelatorioClientesControle.listaRelatorioClientes}"/>
                                                            <f:attribute name="tipo" value="xls"/>
                                                            <f:attribute name="atributos" value="matricula=Matrícula,nome=Nome,situacao=Situação,empresa=Empresa,vinculo=Vínculo,plano=Plano,contrato=Contrato,categoriaCliente=Categoria,modalidade=Modalidade,valorModalidade=Valor Modalidade,duracao=Duração,horario=Horário,nivelTurma=Nível de Turma,dataLancamento=Data Lançamento,inicio=Início,vence=Vence,faturamento=Faturamento,email=Email"/>
                                                            <f:attribute name="prefixo" value="RelatorioClientes"/>
                                                            <f:attribute name="itemExportacao" value="relClientes"/>
                                                        </a4j:commandButton>

                                                        <c:if test="${RelatorioClientesControle.mostrarConteudo}">

                                                            <a4j:commandButton id="imprimirPDF" ajaxSingle="false"
                                                                               action="#{RelatorioClientesControle.imprimirRelatorio}"
                                                                               value="Imprimir" style="margin-left: 8px;"
                                                                               reRender="mensagens" image="/imagens/imprimir.png"
                                                                               oncomplete="#{RelatorioClientesControle.mensagemNotificar}#{RelatorioClientesControle.msgAlert}"
                                                                               accesskey="2" styleClass="botoes"/>
                                                        </c:if>
                                                    </h:panelGroup>
                                                </h:panelGroup>
                                            </h:panelGrid>
                                            <br/>
                                            <br/>
                                            <!-- ----------------TOTALIZADORES ------------------------------------>
                                            <table width="100%">
                                                <tr>
                                                    <td>
                                                        <rich:spacer width="5px"/></td>
                                                    <td>
                                                        <h:panelGroup id="totais" >
                                                            <c:if test="${RelatorioClientesControle.mostrarConteudo}">
                                                                <rich:simpleTogglePanel width="700px" switchType="client" opened="false" id="painelTotalizadores">
                                                                    <f:facet name="header">
                                                                        <h:panelGroup >
                                                                            <h:outputText styleClass="text" style="font-weight: bold" value="Clientes : " />
                                                                            <h:outputText styleClass="text" value="#{RelatorioClientesControle.totalCliente}" />&nbsp;
                                                                            <h:outputText styleClass="text" style="font-weight: bold" value="Contratos : " />
                                                                            <h:outputText styleClass="text" value="#{RelatorioClientesControle.totalContrato}" />&nbsp;
                                                                            <h:outputText styleClass="text" style="font-weight: bold" value="Valor por mês : " />
                                                                            <h:outputText styleClass="text" value="#{RelatorioClientesControle.totalCompetenciaFormatado}" />&nbsp;
                                                                            <h:outputText styleClass="text" style="font-weight: bold" value="Valor total : " />
                                                                            <h:outputText styleClass="text" value="#{RelatorioClientesControle.totalValorFormatado}" />
                                                                        </h:panelGroup>
                                                                    </f:facet>
                                                                            <center>
                                                                                 <h:outputText style="font-weight: bold"  styleClass="text" value="Totais por modalidade"/>
                                                                    </center>
                                                                        
                                                                    <!-- totais das modalidades -->
                                                                    <rich:dataTable var="total" value="#{RelatorioClientesControle.totalModalidade}" width="100%">
                                                                        <rich:column>
                                                                            <f:facet name="header">
                                                                                <h:outputText style="font-weight: bold"  styleClass="text" value="Modalidade"/>
                                                                            </f:facet>
                                                                            <h:outputText styleClass="text" value="#{total.modalidade}"/>
                                                                        </rich:column>

                                                                        <rich:column>
                                                                            <f:facet name="header">
                                                                                <h:outputText style="font-weight: bold"  styleClass="text" value="Clientes"/>
                                                                            </f:facet>
                                                                            <h:outputText styleClass="text" value="#{total.clientes}"/>
                                                                        </rich:column>

                                                                        <rich:column>
                                                                            <f:facet name="header">
                                                                                <h:outputText style="font-weight: bold"  styleClass="text" value="Contratos"/>
                                                                            </f:facet>
                                                                            <h:outputText styleClass="text" value="#{total.contratos}"/>
                                                                        </rich:column>

                                                                        <rich:column>
                                                                            <f:facet name="header">
                                                                                <h:outputText style="font-weight: bold"  styleClass="text" value="Valor por mês"/>
                                                                            </f:facet>
                                                                            <h:outputText styleClass="text" value="#{total.total}"/>
                                                                        </rich:column>

                                                                        <rich:column>
                                                                            <f:facet name="header">
                                                                                <h:outputText style="font-weight: bold"  styleClass="text" value="Valor total"/>
                                                                            </f:facet>
                                                                            <h:outputText styleClass="text" value="#{total.faturamento}"/>
                                                                        </rich:column>


                                                                    </rich:dataTable>
                                                                </rich:simpleTogglePanel>
                                                                    <rich:toolTip for="painelTotalizadores">
                                                                        <h:outputText escape="false"  value="Os valores aqui apresentados são referentes aos contratos listados nos resultados da consulta.<br/> Onde 'Valor por mês' é a soma dos valores mensais e 'Valor total' é a soma do valor final dos contratos."/>
                                                                    </rich:toolTip>
                                                            </c:if>
                                                        </h:panelGroup>
                                                    </td>
                                                </tr>
                                            </table>

                                        </td>
                                    </tr>

                                    <tr>
                                        <td align="left" valign="top" width="100%"  style="padding:7px 20px 0 20px;">
                                            <!-- ----------------RESULTADOS ------------------------------------>
                                            <h:panelGroup id="panelRelatorio">
                                                <table  width="100%" height="100%" border="0" align="left" cellpadding="0" cellspacing="0" class="text">

                                                    <tr>
                                                        <td width="19" height="50" align="left" valign="top"><img src="images/box_centro_top_left.gif" width="19" height="50"></td>
                                                        <td align="left" valign="top" background="images/box_centro_top.gif" class="tituloboxcentro" style="padding:11px 0 0 0;">Resultados</td>
                                                        <td width="19" align="left" valign="top"><img src="images/box_centro_top_right.gif" width="19" height="50"></td>
                                                    </tr>
                                                    <tr>
                                                        <td align="left" valign="top" background="images/box_centro_left.gif"></td>
                                                        <td align="left" valign="top" bgcolor="#ffffff" style="padding:5px 15px 5px 15px;">
                                                            <h:panelGrid id="areaResultados" width="100%">
                                                                <h:panelGroup id="mensagens">
                                                                    <h:outputText styleClass="mensagemDetalhadaGrande" value="#{RelatorioClientesControle.mensagem}" />&nbsp;
                                                                </h:panelGroup>
                                                                <c:if test="${RelatorioClientesControle.mostrarConteudo}">
                                                                    <table width="100%">
                                                                        <tr>
                                                                            <td>
                                                                                <table width="100%">
                                                                                    <tr>
                                                                                        <td width="50%">
                                                                                            <h:panelGroup>
                                                                                                <h:selectBooleanCheckbox value="#{RelatorioClientesControle.consultaPaginada}">
                                                                                                    <a4j:support event="onclick" action="#{RelatorioClientesControle.mudarPaginacao}" reRender="colunasAgrupamento, resultadoRel, resultadoRelPag"></a4j:support>
                                                                                                </h:selectBooleanCheckbox>
                                                                                                <h:outputText value="Exibir com paginação" styleClass="text"></h:outputText>
                                                                                            </h:panelGroup>
                                                                                        </td>

                                                                                    </tr>
                                                                                </table>
                                                                            </td>

                                                                        </tr>
                                                                    </table>

                                                                    <h:panelGrid width="100%" columns="1">
                                                                        <h:panelGroup  id="resultadoRelPag">
                                                                            <rich:dataTable
                                                                                id="itens"
                                                                                rendered="#{RelatorioClientesControle.consultaPaginada}" 
                                                                                value="#{RelatorioClientesControle.listaRelatorioClientes}"
                                                                                var="clienteRel" width="100%" rows="#{RelatorioClientesControle.nrPagina}">

                                                                                <!-- COLUNA - MATRICULA -->
                                                                                <rich:column id="matricula" width="60px" sortable="true" sortBy="#{clienteRel.matriculaOrdem}"
                                                                                             filterBy="#{clienteRel.matricula}" filterEvent="onblur"
                                                                                             label="#{msg_aplic.prt_RelatorioCliente_Matricula}"
                                                                                             rendered="#{RelatorioClientesControle.colMatricula}">
                                                                                    <f:facet name="header">
                                                                                        <h:outputText value="#{msg_aplic.prt_RelatorioCliente_Matricula}" />
                                                                                    </f:facet>
                                                                                    <a4j:outputPanel>
                                                                                        <h:outputText escape="false" value="#{clienteRel.matricula}" />
                                                                                    </a4j:outputPanel>
                                                                                </rich:column>

                                                                                <!-- COLUNA - NOME -->

                                                                                <rich:column id="nome"  sortable="true" sortBy="#{clienteRel.nome}"
                                                                                             filterBy="#{clienteRel.nome}" filterEvent="onblur"
                                                                                             label="#{msg_aplic.prt_RelatorioCliente_Nome}"
                                                                                             rendered="#{RelatorioClientesControle.colNome}">
                                                                                    <f:facet name="header">
                                                                                        <h:outputText value="#{msg_aplic.prt_RelatorioCliente_Nome}" />
                                                                                    </f:facet>
                                                                                    <h:outputText escape="false" value="#{clienteRel.nome}" />
                                                                                </rich:column>

                                                                                <!-- COLUNA - SITUACAO -->
                                                                                <rich:column id="situacao"  width="60px" sortable="true" sortBy="#{clienteRel.situacao}"
                                                                                             filterBy="#{clienteRel.situacao}" filterEvent="onblur"
                                                                                             label="#{msg_aplic.prt_RelatorioCliente_Situacao}"
                                                                                             rendered="#{RelatorioClientesControle.colSituacao}">
                                                                                    <f:facet name="header">
                                                                                        <h:outputText value="#{msg_aplic.prt_RelatorioCliente_Situacao}" />
                                                                                    </f:facet>
                                                                                    <h:outputText escape="false" value="#{clienteRel.situacao}" />
                                                                                </rich:column>

                                                                                <!-- COLUNA - EMPRESA -->
                                                                                <rich:column id="empresa"  sortable="true" sortBy="#{clienteRel.empresa}"
                                                                                             filterBy="#{clienteRel.empresa}" filterEvent="onblur"
                                                                                             label="#{msg_aplic.prt_RelatorioCliente_empresa}"
                                                                                             rendered="#{RelatorioClientesControle.colEmpresa}">
                                                                                    <f:facet name="header">
                                                                                        <h:outputText value="#{msg_aplic.prt_RelatorioCliente_Empresa}" />
                                                                                    </f:facet>
                                                                                    <h:outputText escape="false" value="#{clienteRel.empresa}" />
                                                                                </rich:column>

                                                                                <!-- COLUNA - VINCULO -->
                                                                                <rich:column id="vinculo"  sortable="true" sortBy="#{clienteRel.vinculo}"
                                                                                             filterBy="#{clienteRel.vinculo}" filterEvent="onblur"
                                                                                             label="#{msg_aplic.prt_RelatorioCliente_vinculo}"
                                                                                             rendered="#{RelatorioClientesControle.colVinculo}">
                                                                                    <f:facet name="header">
                                                                                        <h:outputText value="#{msg_aplic.prt_RelatorioCliente_vinculo}" />
                                                                                    </f:facet>
                                                                                    <h:outputText escape="false" value="#{clienteRel.vinculo}" />
                                                                                </rich:column>

                                                                                <!-- COLUNA - PLANO -->
                                                                                <rich:column id="plano"  width="120px" sortable="true" sortBy="#{clienteRel.plano}"
                                                                                             filterBy="#{clienteRel.plano}" filterEvent="onblur"
                                                                                             label="#{msg_aplic.prt_RelatorioCliente_plano}"
                                                                                             rendered="#{RelatorioClientesControle.colPlano}">
                                                                                    <f:facet name="header">
                                                                                        <h:outputText value="#{msg_aplic.prt_RelatorioCliente_plano}" />
                                                                                    </f:facet>
                                                                                    <h:outputText escape="false" value="#{clienteRel.plano}" />
                                                                                </rich:column>

                                                                                <!-- COLUNA - CONTRATO -->
                                                                                <rich:column id="contrato"  width="80px" sortable="true" sortBy="#{clienteRel.contrato}"
                                                                                             filterBy="#{clienteRel.contrato}" filterEvent="onblur"
                                                                                             label="#{msg_aplic.prt_RelatorioCliente_contrato}"
                                                                                             rendered="#{RelatorioClientesControle.colContrato}">
                                                                                    <f:facet name="header">
                                                                                        <h:outputText value="#{msg_aplic.prt_RelatorioCliente_contrato}" />
                                                                                    </f:facet>
                                                                                    <h:outputText escape="false"  value="#{clienteRel.contrato}" />
                                                                                </rich:column>
                                                                                <%-----------COLUNA NOME CATEGORIA CLIENTE-------%>
                                                                                <rich:column id="categoria"  width="80px" sortable="true" sortBy="#{clienteRel.categoriaCliente}"
                                                                                             filterBy="#{clienteRel.categoriaCliente}" filterEvent="onblur"
                                                                                             label="#{msg_aplic.prt_RelatorioCliente_categoriaCliente}"
                                                                                             rendered="#{RelatorioClientesControle.colCategoriaCliente}">
                                                                                    <f:facet name="header">
                                                                                        <h:outputText value="#{msg_aplic.prt_RelatorioCliente_categoriaCliente}" />
                                                                                    </f:facet>
                                                                                    <h:outputText escape="false"  value="#{clienteRel.categoriaCliente}" />
                                                                                </rich:column>
                                                                                <!-- COLUNA - MODALIDADE -->

                                                                                <rich:column id="modalidade"  sortable="true" sortBy="#{clienteRel.modalidade}"
                                                                                             filterBy="#{clienteRel.modalidade}" filterEvent="onblur"
                                                                                             label="#{msg_aplic.prt_RelatorioCliente_modalidade}"
                                                                                             rendered="#{RelatorioClientesControle.colModalidade}">
                                                                                    <f:facet name="header">
                                                                                        <h:outputText value="#{msg_aplic.prt_RelatorioCliente_modalidade}" />
                                                                                    </f:facet>
                                                                                    <h:outputText escape="false" value="#{clienteRel.modalidade}" />
                                                                                </rich:column>

                                                                                <!-- COLUNA - VALOR MODALIDADE -->

                                                                                <rich:column id="valorModalidadeOrdem"  sortable="true" sortBy="#{clienteRel.valorModalidadeOrdem}"
                                                                                             filterBy="#{clienteRel.valorModalidade}" filterEvent="onblur"
                                                                                             label="#{msg_aplic.prt_RelatorioCliente_valorModalidade}"
                                                                                             rendered="#{RelatorioClientesControle.colValorModalidade}">
                                                                                    <f:facet name="header">
                                                                                        <h:outputText value="#{msg_aplic.prt_RelatorioCliente_valorModalidade}" />
                                                                                    </f:facet>
                                                                                    <h:outputText escape="false" value="#{clienteRel.valorModalidade}" />
                                                                                </rich:column>

                                                                                <!-- COLUNA - DURACAO -->

                                                                                <rich:column id="duracaoOrdem"  sortable="true" sortBy="#{clienteRel.duracao}"
                                                                                             filterBy="#{clienteRel.duracao}" filterEvent="onblur"
                                                                                             label="#{msg_aplic.prt_RelatorioCliente_duracao}"
                                                                                             rendered="#{RelatorioClientesControle.colDuracao}">
                                                                                    <f:facet name="header">
                                                                                        <h:outputText value="#{msg_aplic.prt_RelatorioCliente_duracao}" />
                                                                                    </f:facet>
                                                                                    <h:outputText escape="false" value="#{clienteRel.duracao}" />
                                                                                </rich:column>

                                                                                <!-- COLUNA - HORARIO -->
                                                                                <rich:column id="horario"  sortable="true" sortBy="#{clienteRel.horario}"
                                                                                             filterBy="#{clienteRel.horario}" filterEvent="onblur"
                                                                                             label="#{msg_aplic.prt_RelatorioCliente_horario}"
                                                                                             rendered="#{RelatorioClientesControle.colHorario}">
                                                                                    <f:facet name="header">
                                                                                        <h:outputText value="#{msg_aplic.prt_RelatorioCliente_horario}" />
                                                                                    </f:facet>
                                                                                    <h:outputText escape="false" value="#{clienteRel.horario}" />
                                                                                </rich:column>
                                                                                <!-- COLUNA - NIVEL TURMA -->
                                                                                <rich:column id="nivelturma"  sortable="true" sortBy="#{clienteRel.nivelTurma}"
                                                                                             filterBy="#{clienteRel.nivelTurma}" filterEvent="onblur"
                                                                                             label="#{msg_aplic.prt_RelatorioCliente_nivelTurma}"
                                                                                             rendered="#{RelatorioClientesControle.colNivelTurma}">
                                                                                    <f:facet name="header">
                                                                                        <h:outputText value="#{msg_aplic.prt_RelatorioCliente_nivelTurma}" />
                                                                                    </f:facet>
                                                                                    <h:outputText escape="false" value="#{clienteRel.nivelTurma}" />
                                                                                </rich:column>
                                                                                <!-- COLUNA - DATA LANCAMENTO -->
                                                                                <rich:column id="dataLancamentoOrdem"  sortable="true" sortBy="#{clienteRel.dataLancamentoOrdem}"
                                                                                             filterBy="#{clienteRel.dataLancamento}" filterEvent="onblur"
                                                                                             label="#{msg_aplic.prt_RelatorioCliente_dtLancamento}"
                                                                                             rendered="#{RelatorioClientesControle.colDataLancamento}">
                                                                                    <f:facet name="header">
                                                                                        <h:outputText value="#{msg_aplic.prt_RelatorioCliente_dtLancamento}" />
                                                                                    </f:facet>
                                                                                    <h:outputText escape="false" value="#{clienteRel.dataLancamento}" />
                                                                                </rich:column>

                                                                                <!-- COLUNA - INICIO -->
                                                                                <rich:column id="inicioOrdem"  sortable="true" sortBy="#{clienteRel.inicioOrdem}"
                                                                                             filterBy="#{clienteRel.inicio}" filterEvent="onblur"
                                                                                             label="#{msg_aplic.prt_RelatorioCliente_inicio}"
                                                                                             rendered="#{RelatorioClientesControle.colInicio}">
                                                                                    <f:facet name="header">
                                                                                        <h:outputText value="#{msg_aplic.prt_RelatorioCliente_inicio}" />
                                                                                    </f:facet>
                                                                                    <h:outputText escape="false" value="#{clienteRel.inicio}" />
                                                                                </rich:column>

                                                                                <!-- COLUNA - VENCE -->
                                                                                <rich:column id="maiorData"  sortable="true" sortBy="#{clienteRel.maiorData}"
                                                                                             filterBy="#{clienteRel.vence}" filterEvent="onblur"
                                                                                             label="#{msg_aplic.prt_RelatorioCliente_vence}"
                                                                                             rendered="#{RelatorioClientesControle.colVence}">
                                                                                    <f:facet name="header">
                                                                                        <h:outputText value="#{msg_aplic.prt_RelatorioCliente_vence}" />
                                                                                    </f:facet>
                                                                                    <h:outputText escape="false" value="#{clienteRel.vence}" />
                                                                                </rich:column>
                                                                                <!-- COLUNA - VALOR -->
                                                                                <rich:column id="faturamentoOrdem"  sortable="true" sortBy="#{clienteRel.faturamentoOrdem}"
                                                                                             filterBy="#{clienteRel.faturamento}" filterEvent="onblur"
                                                                                             label="#{msg_aplic.prt_RelatorioCliente_valor}"
                                                                                             rendered="#{RelatorioClientesControle.colValor}">
                                                                                    <f:facet name="header">
                                                                                        <h:outputText value="#{msg_aplic.prt_RelatorioCliente_valor}" />
                                                                                    </f:facet>
                                                                                    <h:outputText escape="false" value="#{clienteRel.faturamento}" />
                                                                                </rich:column>
                                                                            </rich:dataTable>

                                                                            <h:panelGrid rendered="#{RelatorioClientesControle.consultaPaginada}" columns="1"
                                                                                         columnClasses="colunaCentralizada" width="100%">

                                                                                <h:panelGrid id="paginacao" columns="3">
                                                                                    <rich:datascroller align="center"
                                                                                                       for="form:itens" maxPages="10"
                                                                                                       id="scResultado" />
                                                                                    <rich:inputNumberSpinner inputSize="4"
                                                                                                             styleClass="form" enableManualInput="true"
                                                                                                             minValue="1" maxValue="100"
                                                                                                             value="#{RelatorioClientesControle.nrPagina}">
                                                                                        <a4j:support event="onchange"
                                                                                                     focus="scResultado"
                                                                                                     reRender="itens,scResultado" />
                                                                                    </rich:inputNumberSpinner>

                                                                                </h:panelGrid>

                                                                            </h:panelGrid>
                                                                        </h:panelGroup>

                                                                        <h:panelGroup id="resultadoRel">
                                                                            <h:panelGroup rendered="#{!RelatorioClientesControle.consultaPaginada}">
                                                                                <div style="overflow-x: auto;overflow-y: auto; width: 100%; height:700px;">
                                                                                    <!-- TABELA - RESULTADOS -->
                                                                                    <rich:extendedDataTable value="#{RelatorioClientesControle.listaRelatorioClientes}" sortMode=""
                                                                                                            var="clienteRel" width="1100" height="10000px"
                                                                                                            groupingColumn="#{RelatorioClientesControle.colunaAgrupada}">

                                                                                        <!-- COLUNA - MATRICULA -->
                                                                                        <rich:column id="matriculacol" width="60px" sortable="true" sortBy="#{clienteRel.matricula}"
                                                                                                     filterBy="#{clienteRel.matricula}" filterEvent="onblur"
                                                                                                     label="#{msg_aplic.prt_RelatorioCliente_Matricula}"
                                                                                                     rendered="#{RelatorioClientesControle.colMatricula}">
                                                                                            <f:facet name="header">
                                                                                                <h:outputText value="#{msg_aplic.prt_RelatorioCliente_Matricula}" />
                                                                                            </f:facet>
                                                                                            <h:outputText escape="false" value="#{clienteRel.matricula}" />
                                                                                        </rich:column>

                                                                                        <!-- COLUNA - NOME -->

                                                                                        <rich:column id="nomecol"  sortable="true" sortBy="#{clienteRel.nome}"
                                                                                                     filterBy="#{clienteRel.nome}" filterEvent="onblur"
                                                                                                     label="#{msg_aplic.prt_RelatorioCliente_Nome}"
                                                                                                     rendered="#{RelatorioClientesControle.colNome}">
                                                                                            <f:facet name="header">
                                                                                                <h:outputText value="#{msg_aplic.prt_RelatorioCliente_Nome}" />
                                                                                            </f:facet>
                                                                                            <h:outputText escape="false" value="#{clienteRel.nome}" />
                                                                                        </rich:column>

                                                                                        <!-- COLUNA - SITUACAO -->
                                                                                        <rich:column id="situacaocol"  width="60px" sortable="true" sortBy="#{clienteRel.situacao}"
                                                                                                     filterBy="#{clienteRel.situacao}" filterEvent="onblur"
                                                                                                     label="#{msg_aplic.prt_RelatorioCliente_Situacao}"
                                                                                                     rendered="#{RelatorioClientesControle.colSituacao}">
                                                                                            <f:facet name="header">
                                                                                                <h:outputText value="#{msg_aplic.prt_RelatorioCliente_Situacao}" />
                                                                                            </f:facet>
                                                                                            <h:outputText escape="false" value="#{clienteRel.situacao}" />
                                                                                        </rich:column>

                                                                                        <!-- COLUNA - VINCULO -->
                                                                                        <rich:column id="vinculocol"  sortable="true" sortBy="#{clienteRel.vinculo}"
                                                                                                     filterBy="#{clienteRel.vinculo}" filterEvent="onblur"
                                                                                                     label="#{msg_aplic.prt_RelatorioCliente_vinculo}"
                                                                                                     rendered="#{RelatorioClientesControle.colVinculo}">
                                                                                            <f:facet name="header">
                                                                                                <h:outputText value="#{msg_aplic.prt_RelatorioCliente_vinculo}" />
                                                                                            </f:facet>
                                                                                            <h:outputText escape="false" value="#{clienteRel.vinculo}" />
                                                                                        </rich:column>

                                                                                        <!-- COLUNA - EMPRESA -->
                                                                                        <rich:column id="empresacol"  sortable="true" sortBy="#{clienteRel.empresa}"
                                                                                                     filterBy="#{clienteRel.empresa}" filterEvent="onblur"
                                                                                                     label="#{msg_aplic.prt_RelatorioCliente_empresa}"
                                                                                                     rendered="#{RelatorioClientesControle.colEmpresa}">
                                                                                            <f:facet name="header">
                                                                                                <h:outputText value="#{msg_aplic.prt_RelatorioCliente_empresa}" />
                                                                                            </f:facet>
                                                                                            <h:outputText escape="false" value="#{clienteRel.empresa}" />
                                                                                        </rich:column>

                                                                                        <!-- COLUNA - PLANO -->
                                                                                        <rich:column id="planocol"  width="120px" sortable="true" sortBy="#{clienteRel.plano}"
                                                                                                     filterBy="#{clienteRel.plano}" filterEvent="onblur"
                                                                                                     label="#{msg_aplic.prt_RelatorioCliente_plano}"
                                                                                                     rendered="#{RelatorioClientesControle.colPlano}">
                                                                                            <f:facet name="header">
                                                                                                <h:outputText value="#{msg_aplic.prt_RelatorioCliente_plano}" />
                                                                                            </f:facet>
                                                                                            <h:outputText escape="false" value="#{clienteRel.plano}" />
                                                                                        </rich:column>

                                                                                        <!-- COLUNA - CONTRATO -->
                                                                                        <rich:column id="contratocol"  width="80px" sortable="true" sortBy="#{clienteRel.contrato}"
                                                                                                     filterBy="#{clienteRel.contrato}" filterEvent="onblur"
                                                                                                     label="#{msg_aplic.prt_RelatorioCliente_contrato}"
                                                                                                     rendered="#{RelatorioClientesControle.colContrato}">
                                                                                            <f:facet name="header">
                                                                                                <h:outputText value="#{msg_aplic.prt_RelatorioCliente_contrato}" />
                                                                                            </f:facet>
                                                                                            <h:outputText escape="false"  value="#{clienteRel.contrato}" />
                                                                                        </rich:column>

                                                                                        <!-- COLUNA - MODALIDADE -->

                                                                                        <rich:column id="modalidadecol"  sortable="true" sortBy="#{clienteRel.modalidade}"
                                                                                                     filterBy="#{clienteRel.modalidade}" filterEvent="onblur"
                                                                                                     label="#{msg_aplic.prt_RelatorioCliente_modalidade}"
                                                                                                     rendered="#{RelatorioClientesControle.colModalidade}">
                                                                                            <f:facet name="header">
                                                                                                <h:outputText value="#{msg_aplic.prt_RelatorioCliente_modalidade}" />
                                                                                            </f:facet>
                                                                                            <h:outputText escape="false" value="#{clienteRel.modalidade}" />
                                                                                        </rich:column>

                                                                                        <!-- COLUNA - VALOR MODALIDADE -->

                                                                                        <rich:column id="valormodalidadecol"  sortable="true" sortBy="#{clienteRel.valorModalidade}"
                                                                                                     filterBy="#{clienteRel.valorModalidade}" filterEvent="onblur"
                                                                                                     label="#{msg_aplic.prt_RelatorioCliente_valorModalidade}"
                                                                                                     rendered="#{RelatorioClientesControle.colValorModalidade}">
                                                                                            <f:facet name="header">
                                                                                                <h:outputText value="#{msg_aplic.prt_RelatorioCliente_valorModalidade}" />
                                                                                            </f:facet>
                                                                                            <h:outputText escape="false" value="#{clienteRel.valorModalidade}" />
                                                                                        </rich:column>

                                                                                        <!-- COLUNA - DURACAO -->

                                                                                        <rich:column id="duracaocol"  sortable="true" sortBy="#{clienteRel.duracao}"
                                                                                                     filterBy="#{clienteRel.duracao}" filterEvent="onblur"
                                                                                                     label="#{msg_aplic.prt_RelatorioCliente_duracao}"
                                                                                                     rendered="#{RelatorioClientesControle.colDuracao}">
                                                                                            <f:facet name="header">
                                                                                                <h:outputText value="#{msg_aplic.prt_RelatorioCliente_duracao}" />
                                                                                            </f:facet>
                                                                                            <h:outputText escape="false" value="#{clienteRel.duracao}" />
                                                                                        </rich:column>

                                                                                        <!-- COLUNA - HORARIO -->
                                                                                        <rich:column id="horariocol"  sortable="true" sortBy="#{clienteRel.horario}"
                                                                                                     filterBy="#{clienteRel.horario}" filterEvent="onblur"
                                                                                                     label="#{msg_aplic.prt_RelatorioCliente_horario}"
                                                                                                     rendered="#{RelatorioClientesControle.colHorario}">
                                                                                            <f:facet name="header">
                                                                                                <h:outputText value="#{msg_aplic.prt_RelatorioCliente_horario}" />
                                                                                            </f:facet>
                                                                                            <h:outputText escape="false" value="#{clienteRel.horario}" />
                                                                                        </rich:column>
                                                                                        <!-- COLUNA - DATA LANCAMENTO -->
                                                                                        <rich:column id="lancamentocol"  sortable="true" sortBy="#{clienteRel.dataLancamento}"
                                                                                                     filterBy="#{clienteRel.dataLancamento}" filterEvent="onblur"
                                                                                                     label="#{msg_aplic.prt_RelatorioCliente_dtLancamento}"
                                                                                                     rendered="#{RelatorioClientesControle.colDataLancamento}">
                                                                                            <f:facet name="header">
                                                                                                <h:outputText value="#{msg_aplic.prt_RelatorioCliente_dtLancamento}" />
                                                                                            </f:facet>
                                                                                            <h:outputText escape="false" value="#{clienteRel.dataLancamento}" />
                                                                                        </rich:column>
                                                                                        <!-- COLUNA - INICIO -->
                                                                                        <rich:column id="iniciocol"  sortable="true" sortBy="#{clienteRel.inicio}"
                                                                                                     filterBy="#{clienteRel.inicio}" filterEvent="onblur"
                                                                                                     label="#{msg_aplic.prt_RelatorioCliente_inicio}"
                                                                                                     rendered="#{RelatorioClientesControle.colInicio}">
                                                                                            <f:facet name="header">
                                                                                                <h:outputText value="#{msg_aplic.prt_RelatorioCliente_inicio}" />
                                                                                            </f:facet>
                                                                                            <h:outputText escape="false" value="#{clienteRel.inicio}" />
                                                                                        </rich:column>

                                                                                        <!-- COLUNA - VENCE -->
                                                                                        <rich:column id="vencecol"  sortable="true" sortBy="#{clienteRel.vence}"
                                                                                                     filterBy="#{clienteRel.vence}" filterEvent="onblur"
                                                                                                     label="#{msg_aplic.prt_RelatorioCliente_vence}"
                                                                                                     rendered="#{RelatorioClientesControle.colVence}">
                                                                                            <f:facet name="header">
                                                                                                <h:outputText value="#{msg_aplic.prt_RelatorioCliente_vence}" />
                                                                                            </f:facet>
                                                                                            <h:outputText escape="false" value="#{clienteRel.vence}" />
                                                                                        </rich:column>
                                                                                        <!-- COLUNA - VALOR -->
                                                                                        <rich:column id="valorcol"  sortable="true" sortBy="#{clienteRel.faturamento}"
                                                                                                     filterBy="#{clienteRel.faturamento}" filterEvent="onblur"
                                                                                                     label="#{msg_aplic.prt_RelatorioCliente_valor}"
                                                                                                     rendered="#{RelatorioClientesControle.colValor}">
                                                                                            <f:facet name="header">
                                                                                                <h:outputText value="#{msg_aplic.prt_RelatorioCliente_valor}" />
                                                                                            </f:facet>
                                                                                            <h:outputText escape="false" value="#{clienteRel.faturamento}" />
                                                                                        </rich:column>

                                                                                    </rich:extendedDataTable>
                                                                                </div>
                                                                            </h:panelGroup>
                                                                        </h:panelGroup>
                                                                    </h:panelGrid>


                                                                </c:if>
                                                            </h:panelGrid>

                                                        </td>
                                                        <td align="left" valign="top" background="images/box_centro_right.gif"><img src="images/shim.gif"></td>

                                                    </tr>
                                                    <tr>
                                                        <td height="20" align="left" valign="top"><img src="images/box_centro_bottom_left.gif" width="19" height="20"></td>
                                                        <td align="left" valign="top" background="images/box_centro_bottom.gif"><img src="images/shim.gif"></td>
                                                        <td align="left" valign="top"><img src="images/box_centro_bottom_right.gif" width="19" height="20"></td>
                                                    </tr>
                                                </table>
                                            </h:panelGroup>
                                        </td>
                                    </tr>
                                </table>
                            </td>
                        </tr>

                    </table>
                </td>
            </tr>
            <tr>
                <td  height="93" align="left" valign="top" class="bgrodape">
                    <jsp:include page="include_rodape_1.jsp" flush="true" />
                </td>
            </tr>
        </table>


        <h:inputHidden id="relatorio" value="#{RelatorioClientesControle.relatorio}" />
        <h:inputHidden id="fsituacao" value="#{RelatorioClientesControle.filtrosSituacao}" />
        <h:inputHidden id="fmodalidade" value="#{RelatorioClientesControle.filtrosModalidade}" />
        <h:inputHidden id="fmodalidadedesc" value="#{RelatorioClientesControle.filtrosModalidadeDescricao}" />
        <h:inputHidden id="fplano" value="#{RelatorioClientesControle.filtrosPlano}" />
        <h:inputHidden id="fplanodesc" value="#{RelatorioClientesControle.filtrosPlanoDescricao}" />
        <h:inputHidden id="fcolaborador" value="#{RelatorioClientesControle.filtrosColaborador}" />
        <h:inputHidden id="fhorario" value="#{RelatorioClientesControle.filtrosHorario}" />
        <h:inputHidden id="fhorariodesc" value="#{RelatorioClientesControle.filtrosHorarioDescricao}" />
        <h:inputHidden id="fduracao" value="#{RelatorioClientesControle.filtrosDuracao}" />
        <h:inputHidden id="fsituacaocontrato" value="#{RelatorioClientesControle.filtrosSituacaoContrato}" />
    </h:form>
    <script>
        inicializar();
    </script>
</f:view>
</body>
