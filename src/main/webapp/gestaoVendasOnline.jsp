<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<%@page contentType="text/html" %>
<%@page pageEncoding="ISO-8859-1" %>

<link href="./css/otimize.css" rel="stylesheet" type="text/css">
<link href="./css/css_bi_1.4.css" rel="stylesheet" type="text/css"/>

<%@include file="/includes/imports.jsp" %>
<head>

    <jsp:include page="include_head.jsp" flush="true"/>
    <script type="text/javascript" language="javascript" src="../script/script.js"></script>
    <link href="../beta/css/font-awesome_2.0.min.css" type="text/css" rel="stylesheet"/>
    <link href="../css/telaCliente.css" rel="stylesheet" type="text/css"/>
    <link href="../css/pacto_flat_2.16.min.css" rel="stylesheet" type="text/css"/>
    <link href="../beta/css/pure-min.css" type="text/css" rel="stylesheet"/>
    <link href="../beta/css/bootstrap-tabs.css" type="text/css" rel="stylesheet"/>
    <link href="../beta/css/pure-ext.css" type="text/css" rel="stylesheet"/>

    <script src="script/packJQueryPlugins.min.js" type="text/javascript"></script>
    <link href="css/gestaoRecebiveis1.0.min.css" rel="stylesheet" type="text/css">
</head>

<script type="text/javascript">
    function verificarTamanhoMaximo(campo, tamanho) {
        if (campo.value.length > tamanho) {
            campo.value = campo.value.toString().substring(0,tamanho-1);
        }
    }

    function marcarTodos(tipo){

        var hiddenQuantRegistroCampo = document.getElementById('form:hiddenQuantRegistroCampo');

        var todos = document.getElementById('form:listaCamposObrigatoriosCadastroVendas:0:mostrarTodos'+tipo+'');

        var checkDoLoop = false;

        if(todos.checked) {
            checkDoLoop = true;
        }

        for (var i = 0; i < hiddenQuantRegistroCampo.value; i++){
            var idCheckPlano = 'form:listaConfiguracaoCamposObrigatoriosCadastroVendas:'+ i +':check'+tipo+'';
            var elemento = document.getElementById(idCheckPlano);
            if (elemento) {
                document.getElementById(idCheckPlano).checked = checkDoLoop;
            }
        }
    }
</script>
<style type="text/css">
    .dadosvendas {
        margin: 20px;
    }

    .dadosvendas h4.cinza {
        font-weight: normal;
    }

    .dadosvendas .row {
        margin: 0;
        margin-top: 20px;
    }

    .linkvendas a {
        font-size: 17px;

    }

    .linkvendas {
        position: relative;
        padding: 10px;
        border: 1px solid #ddd !important;
        background-color: #fff !important;
        border-radius: 3px;
        color: #b4b4b4 !important;
        margin-bottom: 20px;
    }

    .caixactrlc a {
        margin-top: 10px;
        display: block;
        margin-left: 13px;
    }

    .caixactrlc {
        background-color: #ddd;
        width: 40px;
        height: 40px;
        position: absolute;
        top: 0;
        font-size: 16px;
        right: 0;
    }
    .textoCarregando {
        margin-left: 0px !important;
    }

    .gr-totalizador {
        width: calc(100% / 2 - 1px);
        height: 280px;
        font-size: 23px;
        display: inline-block;
        text-align: center;
        margin-top: 10px;
    }

    .gr-totalizador-plano {
        height: 280px;
        font-size: 23px;
    }

    .configs {
        position: absolute;
        top: 15px;
        font-size: 25px;
        right: 15px;
    }

    .rich-color-picker-icon {
        width: 40px !important;
        height: 40px !important;
    }

    .novaModal .rich-color-picker-colors-input {
        font-size: 12px !important;
    }

    .gr-totalizador-text, .gr-totalizador-text:hover, .gr-totalizador-text:visited {
        display: block;
        margin-top: 41px;
        font-size: 4em;
    }

    .alinhamentoCheckLabel {
        margin-top: 3.9%;
        display: inline-block;
        width: 100%;
    }

    .alinhamentoCheckLabelQuebrada {
        margin-top: 3.4%;
    }

    .alinhamentoLabels {
        margin-top: 3.9%;
        display: inline-block;
        width: 100%;
    }


    .alinhamentoLabelsQuebrada {
        margin-top: 1%;
    }

    .btnVoltarConfig {
        font-size: 16px;
        color: silver !important;
    }

    .divAbaConvenio {
        margin-top: 20px;
        margin-bottom: 20px;
        display: grid;
        grid-template-columns: 0.8fr 1.2fr;
        gap: 30px
    }

    .divNomeConfigPlanoProduto {
        font-weight: bold;
        align-items: center;
        display: flex;
        justify-content: center;
        border-bottom: 1px solid #E5E5E5;
        padding-bottom: 20px;
        margin-bottom: 20px;
    }

    .divNomeConfigPlanoProdutoConfig {
        font-weight: bold;
        align-items: center;
        display: flex;
        justify-content: center;
        border-top: 1px solid #E5E5E5;
        padding-top: 15px;
        margin-top: 30px;
    }

    .nomeEmpresaConfig {
        font-family: Arial;
        color: #29abe2;
    }

    .divConvenioPlanoProduto {
        display: grid;
    }

    .nomeConvenioConfig {
        font-family: Arial;
        color: #29abe2;
    }

    .divConfigProdutoPlano {
        border-bottom: 1px solid #E5E5E5;
        padding-top: 10px;
        margin-bottom: 20px;
    }

    .inputVendas, .inputTextVendas {
        padding: 10px;
        border: 1px solid #ddd !important;
        background: none !important;
        border-radius: 3px;
        color: #b4b4b4 !important;
        font-size: 14px !important;
    }

    .inputTextVendas {
        height: 38px;
    }

    .inputTextVendas:focus {
        border: 1px solid #29abe2 !important;
        outline: 0;
        color: #777 !important;
        -webkit-box-shadow: 0 0 8px 0 rgba(41, 171, 226, 0.7);
        -moz-box-shadow: 0 0 8px 0 rgba(41, 171, 226, 0.7);
        box-shadow: 0 0 8px 0 rgba(41, 171, 226, 0.7);
    }

    .divFiltrosVendas {
        display: flex;
        text-align: right;
        align-items: center;
        justify-content: flex-end;
        margin-bottom: 5px;
    }

    .btnConfigsVendas {
        font-size: 25px;
    }

    .rich-picklist-list-content {
        width: 200px !important;
        height: auto !important;
        overflow: hidden !important;
        min-height: 150px !important;
    }

    .container-meta-diaria div.cb-container select {
        z-index: 0;
    }

    .container-meta-diaria {
        /* width: 100%; */
        margin: 15px;
        padding: 10px;
        height: auto !important;
        position: relative;
        -webkit-box-shadow: 0px 2px 10px 0px rgba(0, 0, 0, 0.35);
        -moz-box-shadow: 0px 2px 10px 0px rgba(0, 0, 0, 0.35);
        box-shadow: 0px 2px 10px 0px rgba(0, 0, 0, 0.35);
        background-color: #ffffff;
        transition: transform .1s ease-out;
        -moz-transition: -moz-transform .1s ease-out;
        -o-transition: -o-transform .1s ease-out;
        -webkit-transition: -webkit-transform .1s ease-out;
    }

    table.tabelaSimplesCustom:not(.dataTable) {
        height: auto !important;
    }

    table,
    tr,
    td {
        vertical-align: middle !important;
        border-collapse: collapse;
    }

    td {
        vertical-align: top !important;
    }

    .tags b {
        cursor: copy;
    }
    .neww {
        margin-left: 5px;
        top: -4px;
        color: #ffffff !important;
        font-size: 12px;
    }
    .divNovidadee{
        background: #ff6e1e;
        font-weight: 600;
        border-top-left-radius: 15px;
        border-bottom-left-radius: 15px;
        border-bottom-right-radius: 15px;
        border-top-right-radius: 15px;
        height: 20px;
        padding-left: 14px;
        padding-right: 14px;
        position: relative;
    }

    th.consulta {
        padding: 7px;
    }

    .tableAgendaItens {
        font-size: 14px;
    }

    .tableAgendaItens td {
        padding: 10px;
    }

    .tableAgendaItens span.acoes {
        padding: 10px;
    }

    .tableAgendaItens td i {
        font-size: 14px !important;
        margin: 1px !important;
    }

    .pbodyCustomizado{
        width: 95% !important;
        padding-right: 10px !important;
    }

    .agendaEscolhaProduto label {
        margin-right: 10px;
    }
</style>

<f:view>
    <jsp:include page="includes/include_carregando_ripple.jsp" flush="true"/>
    <title>
        <h:outputText value="Gestão de Vendas Online"/>
    </title>
    <c:set var="titulo" scope="session" value="${GestaoVendasOnlineControle.titulo}"/>
    <h:form id="form">
        <a4j:keepAlive beanName="GestaoVendasOnlineControle"/>
        <html>
        <body>
        <a4j:loadScript src="/script/jquery.maskedinput-1.2.2.js"/>
        <h:panelGroup layout="block" style="display: inline-table;width: 100%;" styleClass="fundoCinza">
            <h:panelGroup layout="block" styleClass="bgtop topoZW" rendered="#{MenuControle.apresentarTopo}">
                <jsp:include page="include_topo_novo.jsp" flush="true"/>
                <jsp:include page="include_menu_zw_flat.jsp" flush="true"/>
            </h:panelGroup>

        <h:panelGroup layout="block" styleClass="caixaCorpo">
        <h:panelGroup layout="block" style="height: 80%;width: 100%">

            <h:panelGroup layout="block" styleClass="#{GestaoVendasOnlineControle.classesPeloMenuAtivo}">
            <h:panelGroup layout="block" styleClass="container-meta-diaria" id="vendasOnlinePanel"
                          style="margin-left: auto; margin-right: auto; display: block; min-height: 75vh; padding:15px; padding-bottom: 120px !important; overflow: hidden">

                <%--Topo--%>
                <h:panelGroup layout="block" styleClass="pure-form pure-form-aligned"
                              style="padding: 15px; position: relative">
                    <div class="tituloPainelPesquisa notop" style="font-weight: bold;font-size: 16px; z-index: 10; width: 100%; float: left;">
                        <h:panelGroup layout="block"
                                      style="display: grid; border-bottom:#E5E5E5 1px solid; width: 100%; grid-template-columns: 1fr 2fr">
                            <h:panelGroup layout="block" style="text-align: left;display: flex;">
                                <a4j:commandLink
                                        id="abaGestao"
                                        action="#{GestaoVendasOnlineControle.selecionarGestao}"
                                        reRender="vendasOnlinePanel"
                                        styleClass="top-aba-builder"
                                        style="padding: 0 15px; display: inline-block; padding-bottom: 10px; #{GestaoVendasOnlineControle.gestaoSelecionado ? 'border-bottom: solid #094771;' : 'opacity: 0.3;' }">
                                    <h:outputText styleClass="container-header-titulo"
                                                  value="Gestão"/>
                                </a4j:commandLink>

                                <a4j:commandLink id="abaLinks"
                                                 action="#{GestaoVendasOnlineControle.selecionarLinks}"
                                                 reRender="vendasOnlinePanel"
                                                 styleClass="top-aba-builder"
                                                 style="padding: 0 15px; display: inline-block; padding-bottom: 10px; #{GestaoVendasOnlineControle.linksSelecionado ? 'border-bottom: solid #094771;' : 'opacity: 0.3;'}">
                                    <h:outputText styleClass="container-header-titulo" value="Links"/>
                                </a4j:commandLink>

                                <h:panelGroup id="abaLinksCampanha">
                                    <a4j:commandLink
                                                     action="#{GestaoVendasOnlineControle.selecionarLinksCampanha}"
                                                     rendered="#{GestaoVendasOnlineControle.config.habilitarCampanha}"
                                                     reRender="vendasOnlinePanel"
                                                     styleClass="top-aba-builder"
                                                     style="padding: 0 15px; display: inline-block; padding-bottom: 10px; #{GestaoVendasOnlineControle.linksCampanhaSelecionado ? 'border-bottom: solid #094771;' : 'opacity: 0.3;'}">
                                        <h:outputText styleClass="container-header-titulo" value="Campanhas"/>
                                    </a4j:commandLink>
                                </h:panelGroup>

                                <a4j:commandLink 
                                                 id="abaSite"
                                                 action="#{GestaoVendasOnlineControle.selecionarSite}"
                                                 reRender="vendasOnlinePanel"
                                                 styleClass="top-aba-builder"
                                                 style="padding: 0 15px; display: inline-block; padding-bottom: 10px; #{GestaoVendasOnlineControle.siteSelecionado ? 'border-bottom: solid #094771;' : 'opacity: 0.3;'}">
                                    <h:outputText styleClass="testSite container-header-titulo" value="Site"/>
                                </a4j:commandLink>
                            </h:panelGroup>
                            <h:panelGroup layout="block" styleClass="divFiltrosVendas">
                                <h:panelGroup rendered="#{!GestaoVendasOnlineControle.linksSelecionado and !GestaoVendasOnlineControle.siteSelecionado}">
                                    <h:outputText styleClass="texto-cor-cinza texto-font"
                                                  style="font-size: 14px;"
                                                  value="Período:"/>
                                    <h:panelGroup styleClass="dateTimeCustom"
                                                  style="font-size: 11px !important;">
                                        <rich:calendar
                                                value="#{GestaoVendasOnlineControle.dataInicial}"
                                                id="dataInicialVendasCfg"
                                                inputStyle="width: 80px;"
                                                inputSize="10"
                                                inputClass="form"
                                                oninputblur="blurinput(this);"
                                                oninputfocus="focusinput(this);"
                                                oninputchange="return validar_Data(this.id);"
                                                datePattern="dd/MM/yyyy"
                                                buttonIcon="/imagens_flat/calendar-button.svg"
                                                enableManualInput="true"
                                                zindex="2"
                                                showWeeksBar="false"/>
                                    </h:panelGroup>
                                    <h:outputText styleClass="texto-cor-cinza texto-font"
                                                  value=" até "
                                                  style="font-size: 14px !important; padding: 0 5px 0 5px"/>
                                    <h:panelGroup styleClass="dateTimeCustom"
                                                  style="font-size: 11px !important;">
                                        <rich:calendar
                                                value="#{GestaoVendasOnlineControle.dataFinal}"
                                                id="dataFinalVendasCfg"
                                                inputSize="10"
                                                inputStyle="width: 80px;"
                                                buttonIcon="/imagens_flat/calendar-button.svg"
                                                inputClass="form"
                                                oninputblur="blurinput(this);"
                                                oninputfocus="focusinput(this);"
                                                oninputchange="return validar_Data(this.id);"
                                                datePattern="dd/MM/yyyy"
                                                enableManualInput="true"
                                                zindex="2"
                                                showWeeksBar="false"/>

                                    </h:panelGroup>
                                </h:panelGroup>
                                <h:outputText styleClass="texto-cor-cinza texto-font"
                                              style="font-size: 14px; padding-left: 10px;"
                                              rendered="#{!GestaoVendasOnlineControle.multiEmpresaConfig}"
                                              value="#{msg_aplic.prt_Brinde_empresa}"/>
                                <h:selectOneMenu id="empresaSelecionadaCVVen" tabindex="8"
                                                 onblur="blurinput(this);"
                                                 styleClass="inputTextClean texto-cor-cinza texto-font"
                                                 style="font-size: 12px !important;"
                                                 onfocus="focusinput(this);"
                                                 rendered="#{!GestaoVendasOnlineControle.multiEmpresaConfig}"
                                                 value="#{GestaoVendasOnlineControle.empresaVO.codigo}">
                                    <f:selectItems
                                            value="#{GestaoVendasOnlineControle.selectItemListaEmpresas}"/>
                                </h:selectOneMenu>

                                <a4j:commandLink id="btnAtualizarDados"
                                                 styleClass="btnConfigsVendas tooltipster"
                                                 rendered="#{!GestaoVendasOnlineControle.multiEmpresaConfig}"
                                                 title="Atualizar os dados"
                                                 action="#{GestaoVendasOnlineControle.init}"
                                                 style="margin-left: 10px;font-size: 28px;"
                                                 reRender="vendasOnlinePanel, painelfotos, mdlMensagemGenerica"
                                                 oncomplete="#{GestaoVendasOnlineControle.msgAlert}">
                                    <a4j:actionparam name="recarregarVendasControle"
                                                     value=""></a4j:actionparam>
                                    <i class="fa-icon-refresh"></i>
                                </a4j:commandLink>

                                <a4j:commandLink styleClass="btnConfigsVendas tooltipster"
                                                 rendered="#{GestaoVendasOnlineControle.gestaoSelecionado}"
                                                 title="Adicionar imagens da academia"
                                                 oncomplete="Richfaces.showModalPanel('modalFotos')"
                                                 reRender="modalFotos"
                                                 action="#{GestaoVendasOnlineControle.verImagens}"
                                                 style="margin-left: 10px;font-size: 28px;">
                                    <i style="color: silver" class="fa-icon-picture"></i>
                                </a4j:commandLink>
                                <a4j:commandLink id="btnConfigVendasOnline"
                                                 styleClass="btnConfigsVendas tooltipster"
                                                 rendered="#{GestaoVendasOnlineControle.gestaoSelecionado}"
                                                 title="Configurações das vendas online"
                                                 reRender="vendasOnlinePanel"
                                                 style="margin-left: 10px"
                                                 oncomplete="#{GestaoVendasOnlineControle.mensagemNotificar}"
                                                 action="#{GestaoVendasOnlineControle.abrirConfiguracoes}">
                                    <i style="color: silver" class="fa-icon-cogs"></i>
                                </a4j:commandLink>
                                <a4j:commandLink styleClass="btnConfigsVendas tooltipster"
                                                 id="btnAbrirConfigacaoMultiEmpresa"
                                                 rendered="#{GestaoVendasOnlineControle.siteSelecionado}"
                                                 title="Configurações pagina inicial multiempresas"
                                                 reRender="vendasOnlinePanel"
                                                 style="margin-left: 10px"
                                                 oncomplete="#{GestaoVendasOnlineControle.mensagemNotificar}"
                                                 action="#{GestaoVendasOnlineControle.abrirConfiguracoesMultiEmpresa}">
                                    <i style="color: silver" class="fa-icon-cogs"></i>
                                </a4j:commandLink>
                            </h:panelGroup>
                        </h:panelGroup>
                    </div>
                </h:panelGroup>
                <h:panelGroup rendered="#{GestaoVendasOnlineControle.gestaoSelecionado}" layout="block" styleClass="gr-container-totalizador"
                              style="margin: 20px; width: calc(100% - 40px); border: none;">
                    <h:panelGroup layout="block" styleClass="gr-totalizador">

                                            <span style="display: block;"
                                                  class="bi-font-family bi-table-text">
                                                Vendas não finalizadas
                                                <i class="fa-icon-question-sign tooltipster"
                                                   id="questionVendasNaoFinalizadas"
                                                   title="Contabiliza todas as tentativas onde o cliente preencheu o cadastro, informou dados de cartão de crédito, porém a operadora não aprovou a cobrança."></i>
                                            </span>

                        <a4j:commandLink styleClass="gr-totalizador-text bi-font-family"
                                         style="text-decoration: none"
                                         id="btnvendasnaorealizadas"
                                         action="#{GestaoVendasOnlineControle.naorealizadas}"
                                         oncomplete="abrirPopup('listaVendas.jsp', 'Gestão de vendas', 1024, 595);"
                                         value="#{GestaoVendasOnlineControle.vendasNaoFinalizadas}"/>
                    </h:panelGroup>

                    <h:panelGroup layout="block" styleClass="gr-totalizador">

                                            <span style="display: block;"
                                                  class="bi-font-family bi-table-text">
                                                Vendas realizadas
                                                <i class="fa-icon-question-sign tooltipster"
                                                   title="Todas as vendas via site realizadas com sucesso no período."></i>
                                            </span>
                        <a4j:commandLink styleClass="gr-totalizador-text bi-font-family"
                                         style="text-decoration: none"
                                         id="btnvendas"
                                         action="#{GestaoVendasOnlineControle.realizadas}"
                                         oncomplete="abrirPopup('listaVendas.jsp', 'Gestão de vendas', 1024, 595);"
                                         value="#{GestaoVendasOnlineControle.vendas}"/>
                    </h:panelGroup>

                    <script>
                        var chart = AmCharts.makeChart("chartdiv", {
                            "type": "serial",
                            "theme": "light",
                            "marginRight": 40,
                            "dataProvider": ${GestaoVendasOnlineControle.grafico},
                            "valueAxes": [{
                                "axisAlpha": 0,
                                "position": "left"
                            }],
                            "startDuration": 1,
                            "graphs": [{
                                "balloonText": "<b>[[description]]: [[value]]</b>",
                                "fillAlphas": 0.9,
                                "lineAlpha": 0.2,
                                "colorField": "cor",
                                "descriptionField": "plano",
                                "type": "column",
                                "valueField": "valor"
                            }],
                            "chartCursor": {
                                "categoryBalloonEnabled": false,
                                "cursorAlpha": 0,
                                "zoomable": false
                            },
                            "categoryField": "sigla",
                            "categoryAxis": {
                                "gridPosition": "start",
                                "labelRotation": 0
                            },
                            "export": {
                                "enabled": true
                            }

                        });

                    </script>


                </h:panelGroup>

                <h:panelGroup rendered="#{GestaoVendasOnlineControle.gestaoSelecionado}" layout="block" styleClass="gr-container-totalizador"
                              style="margin: 20px; width: calc(100% - 40px)">

                    <h:panelGroup layout="block" styleClass="gr-totalizador-plano">

                                            <span style="display: block; text-align: center;"
                                                  class="bi-font-family bi-table-text">
                                                Vendas por plano
                                                <i class="fa-icon-question-sign tooltipster"
                                                   title="Todas as vendas via site realizadas com sucesso no período separadas por plano"></i>
                                            </span>

                        <div id="chartdiv" style="width: 100%; top: 0;left: 0;height: 250px;"
                             class="step2 tudo"></div>

                    </h:panelGroup>

                    <script>
                        var chart = AmCharts.makeChart("chartdiv", {
                            "type": "serial",
                            "theme": "light",
                            "marginRight": 40,
                            "dataProvider": ${GestaoVendasOnlineControle.grafico},
                            "valueAxes": [{
                                "axisAlpha": 0,
                                "position": "left"
                            }],
                            "startDuration": 1,
                            "graphs": [{
                                "balloonText": "<b>[[description]]: [[value]]</b>",
                                "fillAlphas": 0.9,
                                "lineAlpha": 0.2,
                                "colorField": "cor",
                                "descriptionField": "plano",
                                "type": "column",
                                "valueField": "valor"
                            }],
                            "chartCursor": {
                                "categoryBalloonEnabled": false,
                                "cursorAlpha": 0,
                                "zoomable": false
                            },
                            "categoryField": "sigla",
                            "categoryAxis": {
                                "gridPosition": "start",
                                "labelRotation": 0
                            },
                            "export": {
                                "enabled": true
                            }

                        });

                    </script>


                </h:panelGroup>

                <h:panelGroup rendered="#{GestaoVendasOnlineControle.linksSelecionado}" layout="block" styleClass="dadosvendas" id="divLinks">

                    <div class="row">
                        <h:outputText styleClass="texto-size-18 negrito"
                                      style="display: block; margin-top: 20px"
                                      value="Gerador de Links do Vendas Online"/>
                        <div class="column col-md-12">
                          <span style="display: ruby;" class="bi-font-family bi-table-text">
                            <h4 style="display: block; margin-top: 30px;" class="texto-size-14 cinza">Você pode definir um usuário responsável pelos links no campo abaixo caso necessário.</h4>
                                <i class="fa-icon-question-sign tooltipster"
                                title="${GestaoVendasOnlineControle.titleLinkUserResponsavel}"></i>
                          </span>
                        </div>
                        <div style="margin-top: 1vh" class="column col-md-8">
                            <span class="texto-size-14 cinza">Usuário responsável:
                            </span>
                            <h:panelGroup layout="block" styleClass="cb-container"
                                          style="margin-left: 9vh;">
                                <h:selectOneMenu
                                        value="#{GestaoVendasOnlineControle.paramUsuario}">
                                    <a4j:support event="onchange"
                                                 reRender="form:vendasOnlinePanel"/>
                                    <f:selectItems
                                            value="#{GestaoVendasOnlineControle.selectItemUsuariosLink}"/>
                                </h:selectOneMenu>
                            </h:panelGroup>
                        </div>
                    </div>

                    <rich:separator style="margin-top: 25px;" width="100%" height="1"/>

                    <%--LINK PARA LOJA--%>
                    <div class="row" style="margin-top: 0px;">
                        <div class="column col-md-12">
                            <h3 class="texto-size-14 negrito cinzaEscuro">Link para a loja</h3>
                            <h4 class="texto-size-14 cinza">Este link leva o usuário para a tela
                                de escolha de planos e produtos.</h4>
                        </div>
                        <div class="column col-md-7">
                            <div class="linkvendas">
                                <h:outputLink value="#{GestaoVendasOnlineControle.linkLoja}"
                                              target="_blank">
                                    <h:outputText
                                            value="#{GestaoVendasOnlineControle.linkLoja}"/>
                                </h:outputLink>

                                <div class="caixactrlc">
                                    <a4j:commandLink
                                            onclick="copiar('#{GestaoVendasOnlineControle.linkLoja}')">
                                        <i class="fa-icon-copy tooltipster"
                                           title="Clique para copiar para a área de transferência"></i>
                                    </a4j:commandLink>
                                </div>
                            </div>
                        </div>
                    </div>

                    <rich:separator style="margin-top: 5px;" width="100%" height="1"/>

                    <%--LINK PARA PLANOS--%>
                    <div class="row">
                        <div class="column col-md-12" style="margin-top: -20px;">
                            <h3 class="texto-size-14 negrito cinzaEscuro">Link para os
                                planos</h3>
                        </div>
                        <div style="margin-top: 1vh" class="column col-md-8">
                            <span class="texto-size-14 cinza">Tipo do Link: </span>
                            <h:panelGroup layout="block" styleClass="cb-container"
                                          style="margin-left: 9vh;">
                                <h:selectOneMenu
                                        value="#{GestaoVendasOnlineControle.tipoLinkPlano}">
                                    <a4j:support event="onchange" reRender="panelLinksTipoPlano"/>
                                    <f:selectItems
                                            value="#{GestaoVendasOnlineControle.listaTipoLinkPlano}"/>
                                </h:selectOneMenu>
                            </h:panelGroup>
                        </div>

                        <h:panelGroup id="panelLinksTipoPlano">
                        <c:if test="${GestaoVendasOnlineControle.tipoLinkPlano == 1}">
                            <div class="column col-md-12">
                                <h4 class="texto-size-14 cinza">Este link leva o usuário para a
                                    tela
                                    de escolha de planos, com opção de todos os planos marcados
                                    como
                                    <b>site</b> desta unidade. </h4>
                            </div>
                            <div class="column col-md-7">
                                <div class="linkvendas">
                                    <h:outputLink
                                            value="#{GestaoVendasOnlineControle.linkPlanos}"
                                            target="_blank">
                                        <h:outputText
                                                value="#{GestaoVendasOnlineControle.linkPlanos}"/>
                                    </h:outputLink>

                                    <div class="caixactrlc">
                                        <a4j:commandLink
                                                onclick="copiar('#{GestaoVendasOnlineControle.linkPlanos}')">
                                            <i class="fa-icon-copy tooltipster"
                                               title="Clique para copiar para a área de transferência"></i>
                                        </a4j:commandLink>
                                    </div>

                                </div>
                            </div>
                        </c:if>


                        <c:if test="${GestaoVendasOnlineControle.tipoLinkPlano == 2}">
                            <div class="column col-md-12">
                                <h4 class="texto-size-14 cinza">Este link leva o usuário direto
                                    para
                                    a tela de compra, com o plano já escolhido, selecionado por
                                    você
                                    na lista abaixo.</br>
                                    Obs. Caso queira seguir diretamente para tela de compra com
                                    o
                                    plano preenchido e o cupom tambem já preenchido, basta
                                    preenche-lo e atualizar o link.</h4>
                            </div>

                            <div style="margin-top: 1vh" class="column col-md-8">
                                <span class="texto-size-14 cinza">Plano: </span>
                                <h:panelGroup layout="block" styleClass="cb-container"
                                              style="margin-left: 9vh;">
                                    <h:selectOneMenu
                                            value="#{GestaoVendasOnlineControle.plano}">
                                        <a4j:support event="onchange"
                                                     reRender="planoespecifico"/>
                                        <f:selectItems
                                                value="#{GestaoVendasOnlineControle.planos}"/>
                                    </h:selectOneMenu>
                                </h:panelGroup>
                            </div>
                            <h:panelGroup id="cupomDesconto" styleClass="column col-md-8"
                                          layout="block" style="margin-top: 1vh;">
                                <h:outputText styleClass="texto-size-14 cinza"
                                              value="Cupom Desconto: "/>
                                <h:inputText styleClass="form"
                                             style="height: 3.6vh; font-size: 14px; border-radius: 3px; margin-left: 1.3vh;"
                                             onblur="blurinput(this);"
                                             onfocus="focusinput(this);"
                                             value="#{GestaoVendasOnlineControle.paramCupom}"/>
                                <a4j:commandLink styleClass="botoes nvoBt tooltipster"
                                                 value="Gerar Link"
                                                 title="Atualizar link do plano"
                                                 action="#{GestaoVendasOnlineControle.atualizarlinkPlanoCupom}"
                                                 oncomplete="#{GestaoVendasOnlineControle.msgAlert}"
                                                 reRender="planoespecifico, cupomDesconto, mdlMensagemGenerica">
                                </a4j:commandLink>
                                <a4j:commandLink style="margin-left: 5px"
                                                 styleClass="botoes nvoBt btSec"
                                                 value="Limpar Cupom"
                                                 action="#{GestaoVendasOnlineControle.limparAtualizarlinkPlanoCupom}"
                                                 reRender="planoespecifico, cupomDesconto">
                                </a4j:commandLink>
                            </h:panelGroup>
                            <h:panelGroup layout="block" id="planoespecifico"
                                          styleClass="column col-md-7" style="margin-top: 20px">
                                <div class="linkvendas">
                                    <h:outputLink
                                            value="#{GestaoVendasOnlineControle.linkPlanoEspecifico}"
                                            target="_blank">
                                        <h:outputText
                                                value="#{GestaoVendasOnlineControle.linkPlanoEspecifico}"/>
                                    </h:outputLink>

                                    <div class="caixactrlc">
                                        <a4j:commandLink
                                                onclick="copiar('#{GestaoVendasOnlineControle.linkPlanoEspecifico}')">
                                            <i class="fa-icon-copy tooltipster"
                                               title="Clique para copiar para a área de transferência"></i>
                                        </a4j:commandLink>
                                    </div>

                                </div>

                                <h:outputText styleClass="texto-size-14 cinza" style="margin-bottom: 10px; display: block"
                                              rendered="#{GestaoVendasOnlineControle.montarLinkCredito}"
                                              value="Link para a agenda de aulas (somente plano de crédito): "/>

                                <h:panelGroup layout="block" styleClass="linkvendas"
                                              rendered="#{GestaoVendasOnlineControle.montarLinkCredito}">
                                    <h:outputLink
                                            value="#{GestaoVendasOnlineControle.linkPlanoEspecificoAgenda}"
                                            target="_blank">
                                        <h:outputText
                                                value="#{GestaoVendasOnlineControle.linkPlanoEspecificoAgenda}"/>
                                    </h:outputLink>

                                    <div class="caixactrlc">
                                        <a4j:commandLink
                                                onclick="copiar('#{GestaoVendasOnlineControle.linkPlanoEspecificoAgenda}')">
                                            <i class="fa-icon-copy tooltipster"
                                               title="Clique para copiar para a área de transferência"></i>
                                        </a4j:commandLink>
                                    </div>

                                </h:panelGroup>
                            </h:panelGroup>
                        </c:if>

                        <c:if test="${GestaoVendasOnlineControle.tipoLinkPlano == 3}">
                            <div class="column col-md-12">
                                <h4 class="texto-size-14 cinza">Este link leva o usuário direto
                                    para
                                    a tela de compra, com uma categoria de planos selecionada por
                                    você
                                    na lista abaixo.</br>
                                    Obs. Somente os planos da categoria selecionada
                                    irão aparecer.</h4>
                            </div>

                            <div style="margin-top: 1vh" class="column col-md-8">
                                <span class="texto-size-14 cinza">Categoria: </span>
                                <h:panelGroup layout="block" styleClass="cb-container"
                                              style="margin-left: 9vh;">
                                    <h:selectOneMenu
                                            value="#{GestaoVendasOnlineControle.categoria}">
                                        <a4j:support event="onchange"
                                                     reRender="categoriaEspecifica"/>
                                        <f:selectItems
                                                value="#{GestaoVendasOnlineControle.categorias}"/>
                                    </h:selectOneMenu>
                                </h:panelGroup>
                            </div>
                            <h:panelGroup layout="block" id="categoriaEspecifica"
                                          styleClass="column col-md-7" style="margin-top: 20px">
                                <div class="linkvendas">
                                    <h:outputLink
                                            value="#{GestaoVendasOnlineControle.linkPlanoCategoriaEspecifico}"
                                            target="_blank">
                                        <h:outputText
                                                value="#{GestaoVendasOnlineControle.linkPlanoCategoriaEspecifico}"/>
                                    </h:outputLink>

                                    <div class="caixactrlc">
                                        <a4j:commandLink
                                                onclick="copiar('#{GestaoVendasOnlineControle.linkPlanoCategoriaEspecifico}')">
                                            <i class="fa-icon-copy tooltipster"
                                               title="Clique para copiar para a área de transferência"></i>
                                        </a4j:commandLink>
                                    </div>

                                </div>
                            </h:panelGroup>
                        </c:if>
                        </h:panelGroup>

                        <rich:separator id="sepProd" style="margin-top: 7px; display: inline-block;" width="100%" height="1"/>

                        <%--LINK PARA OS PRODUTOS--%>
                        <div class="column col-md-12">
                            <h3 class="texto-size-14 negrito cinzaEscuro">Link para os produtos</h3>
                        </div>
                        <div style="margin-top: 1vh" class="column col-md-8">
                            <span class="texto-size-14 cinza">Tipo do Link: </span>
                            <h:panelGroup layout="block" styleClass="cb-container"
                                          style="margin-left: 9vh;">
                                <h:selectOneMenu
                                        value="#{GestaoVendasOnlineControle.tipoLinkProduto}">
                                    <a4j:support event="onchange" reRender="divLinks"/>
                                    <f:selectItems
                                            value="#{GestaoVendasOnlineControle.listaTipoLinkProduto}"/>
                                </h:selectOneMenu>
                            </h:panelGroup>
                        </div>


                        <c:if test="${GestaoVendasOnlineControle.tipoLinkProduto == 1}">
                            <div class="column col-md-12">
                                <h4 class="texto-size-14 cinza">Este link leva o usuário para a
                                    tela
                                    de escolha de produtos, com opção de todos os produtos
                                    marcados
                                    como <b>Apresentar no Vendas Online</b>.</h4>
                            </div>
                            <div class="column col-md-7">
                                <div class="linkvendas">
                                    <h:outputLink
                                            value="#{GestaoVendasOnlineControle.linkProdutos}"
                                            target="_blank">
                                        <h:outputText
                                                value="#{GestaoVendasOnlineControle.linkProdutos}"/>
                                    </h:outputLink>

                                    <div class="caixactrlc">
                                        <a4j:commandLink
                                                onclick="copiar('#{GestaoVendasOnlineControle.linkProdutos}')">
                                            <i class="fa-icon-copy tooltipster"
                                               title="Clique para copiar para a área de transferência"></i>
                                        </a4j:commandLink>
                                    </div>

                                </div>
                            </div>
                        </c:if>

                        <c:if test="${GestaoVendasOnlineControle.tipoLinkProduto == 2}">

                            <div class="column col-md-12">
                                <h4 class="texto-size-14 cinza">Este link leva o usuário direto
                                    para
                                    a tela de compra, com o produto já escolhido, selecionado
                                    por
                                    você na lista abaixo.</h4>
                            </div>

                            <div style="margin-top: 1vh" class="column col-md-8">
                                <span class="texto-size-14 cinza">Produto: </span>
                                <h:panelGroup layout="block" styleClass="cb-container"
                                              style="margin-left: 4vh;">
                                    <h:selectOneMenu
                                            id="selectProdutosGestaoVendasOnline"
                                            value="#{GestaoVendasOnlineControle.produto}">
                                        <a4j:support event="onchange"
                                                     reRender="produtoespecifico"/>
                                        <f:selectItems
                                                value="#{GestaoVendasOnlineControle.produtos}"/>
                                    </h:selectOneMenu>
                                </h:panelGroup>
                            </div>
                            <h:panelGroup styleClass="column col-md-8" layout="block"
                                          style="margin-top: 1vh;">
                                <h:outputText styleClass="texto-size-14 cinza"
                                              value="Quantidade: "/>
                                <h:inputText styleClass="form"
                                             size="5"
                                             style="height: 3.6vh; font-size: 14px; border-radius: 3px; margin-left: 1.3vh;"
                                             onblur="blurinput(this);"
                                             onfocus="focusinput(this);"
                                             onkeypress="return mascara(this.form, this.id, '99999999', event);"
                                             onkeyup="somenteNumeros(this);"
                                             value="#{GestaoVendasOnlineControle.qtdProduto}">
                                    <a4j:support event="onchange"
                                                 action="#{GestaoVendasOnlineControle.atualizarLinkProdutoQtd}"
                                                 oncomplete="#{GestaoVendasOnlineControle.msgAlert}"
                                                 reRender="produtoespecifico"/>
                                </h:inputText>
                                <a4j:commandLink styleClass="botoes nvoBt tooltipster"
                                                 value="Gerar Link"
                                                 title="Atualizar link do produto"
                                                 action="#{GestaoVendasOnlineControle.atualizarLinkProdutoQtd}"
                                                 oncomplete="#{GestaoVendasOnlineControle.msgAlert}"
                                                 reRender="produtoespecifico">
                                </a4j:commandLink>
                            </h:panelGroup>

                            <h:panelGroup layout="block" id="produtoespecifico"
                                          styleClass="column col-md-7" style="margin-top: 20px">
                                <div class="linkvendas">
                                    <h:outputLink
                                            value="#{GestaoVendasOnlineControle.linkProdutoEspecifico}"
                                            target="_blank">
                                        <h:outputText
                                                value="#{GestaoVendasOnlineControle.linkProdutoEspecifico}"/>
                                    </h:outputLink>

                                    <div class="caixactrlc">
                                        <a4j:commandLink
                                                onclick="copiar('#{GestaoVendasOnlineControle.linkProdutoEspecifico}')">
                                            <i class="fa-icon-copy tooltipster"
                                               title="Clique para copiar para a área de transferência"></i>
                                        </a4j:commandLink>
                                    </div>
                                </div>

                                <h:outputText styleClass="texto-size-14 cinza" style="margin-bottom: 10px; display: block"
                                              rendered="#{GestaoVendasOnlineControle.montarLinkDiaria}"
                                              value="Link para agenda de diárias: "/>

                                <h:panelGroup layout="block" styleClass="linkvendas" rendered="#{GestaoVendasOnlineControle.montarLinkDiaria}">
                                    <h:outputLink
                                            value="#{GestaoVendasOnlineControle.linkProdutoEspecificoAgenda}"
                                            target="_blank">
                                        <h:outputText
                                                value="#{GestaoVendasOnlineControle.linkProdutoEspecificoAgenda}"/>
                                    </h:outputLink>

                                    <div class="caixactrlc">
                                        <a4j:commandLink
                                                onclick="copiar('#{GestaoVendasOnlineControle.linkProdutoEspecificoAgenda}')">
                                            <i class="fa-icon-copy tooltipster"
                                               title="Clique para copiar para a área de transferência"></i>
                                        </a4j:commandLink>
                                    </div>

                                </h:panelGroup>
                            </h:panelGroup>
                        </c:if>
                    </div>


                    <rich:separator style="margin-top: 5px;" width="100%" height="1"/>

                    <%--LINK PARA CADASTROS DE VISITANTES--%>
                    <div class="row">
                        <div class="column col-md-12" style="margin-top: -20px;">
                            <h3 class="texto-size-14 negrito cinzaEscuro">Link para Cadastros de Visitantes Site
                                <i class="fa-icon-question-sign tooltipster" title="${GestaoVendasOnlineControle.titleLinkCadastroVisitante}"></i></h3>
                            <h4 class="texto-size-14 cinza">Este link leva o usuário para a tela de cadastros de visitantes.</h4>
                        </div>
                        <div style="margin-top: 1vh; margin-bottom: 1vh" class="column col-md-8">
                            <span class="texto-size-14 cinza">FreePass: </span>
                            <h:panelGroup layout="block" styleClass="cb-container"
                                          style="margin-left: 5vh;">
                                <h:selectOneMenu
                                        value="#{GestaoVendasOnlineControle.freepass}">
                                    <a4j:support event="onchange"  reRender="divLinks"/>
                                    <f:selectItems
                                            value="#{GestaoVendasOnlineControle.listaFreepass}"/>
                                </h:selectOneMenu>
                            </h:panelGroup>
                        </div>
                        <div class="column col-md-7">
                            <div class="linkvendas cadastro">
                                <h:outputLink value="#{GestaoVendasOnlineControle.linkCadastro}"
                                              target="_blank">
                                    <h:outputText
                                            value="#{GestaoVendasOnlineControle.linkCadastro}"/>
                                </h:outputLink>

                                <div class="caixactrlc">
                                    <a4j:commandLink
                                            onclick="copiar('#{GestaoVendasOnlineControle.linkCadastro}')">
                                        <i class="fa-icon-copy tooltipster"
                                           title="Clique para copiar para a área de transferência"></i>
                                    </a4j:commandLink>
                                </div>
                            </div>
                        </div>
                    </div>

                    <rich:separator style="margin-top: 5px;" width="100%" height="1"/>

                    <%--LINK PARA ESPELHO DE AULAS--%>
                    <div class="row" style="margin-bottom: 30px; display: grid">

                    <span style="display: ruby;"
                          class="bi-font-family bi-table-text">
                            <h3 style="margin-top: -20px;" class="texto-size-14 negrito cinzaEscuro">Link para o espelho de aulas</h3>
                           <i class="fa-icon-question-sign tooltipster"
                              title="${GestaoVendasOnlineControle.titleEspelhoAulas}"></i>
                          </span>
                        <div class="column col-md-7">
                            <div class="linkvendas">
                                <h:outputLink value="#{GestaoVendasOnlineControle.linkEspelhoDeAulas}"
                                              target="_blank">
                                    <h:outputText
                                            value="#{GestaoVendasOnlineControle.linkEspelhoDeAulas}"/>
                                </h:outputLink>

                                <div class="caixactrlc">
                                    <a4j:commandLink
                                            onclick="copiar('#{GestaoVendasOnlineControle.linkEspelhoDeAulas}')">
                                        <i class="fa-icon-copy tooltipster"
                                           title="Clique para copiar para a área de transferência"></i>
                                    </a4j:commandLink>
                                </div>
                            </div>
                        </div>
                    </div>

                    <rich:separator style="margin-top: -20px;" width="100%" height="1"/>

                    <%--DOCUMENTAÇÃO DA API--%>
                    <div class="row" style="margin-bottom: 30px;">
                        <div class="column col-md-12" STYLE="margin-top: -20px">
                            <h:panelGrid columns="2" style="display: inline">
                                <h3 class="texto-size-14 negrito cinzaEscuro">Documentação da </h3>
                                <h:graphicImage value="/imagens/api-rest-logo.png"
                                                style="width: 40px; height: 36px; margin-top: 10px;"
                                                styleClass="tooltipster"/>
                            </h:panelGrid>
                        </div>
                        <h4 class="texto-size-14 cinza">Disponibilizamos para o seu negócio, uma API segura para que você possa realizar vendas
                            e muito mais no Sistema Pacto através do seu site próprio, customizando a aparência e usabilidade conforme desejado.</h4>
                        <h4 class="texto-size-14 cinza">Para o uso da API do Sistema Pacto é necessário gerar uma credencial em: Configurações -> Integrações -> ADM -> API Sistema Pacto.</h4>
                        <div class="column col-md-7">
                            <div class="linkvendas">
                                <h:outputLink value="#{GestaoVendasOnlineControle.linkApi}"
                                              target="_blank">
                                    <h:outputText
                                            value="#{GestaoVendasOnlineControle.linkApi}"/>
                                </h:outputLink>

                                <div class="caixactrlc">
                                    <a4j:commandLink
                                            onclick="copiar('#{GestaoVendasOnlineControle.linkApi}')">
                                        <i class="fa-icon-copy tooltipster"
                                           title="Clique para copiar para a área de transferência"></i>
                                    </a4j:commandLink>
                                </div>
                            </div>
                        </div>
                    </div>
                </h:panelGroup>

                <jsp:include page="include_link_campanha_vendadireta.jsp" flush="true"/>

                <h:panelGroup rendered="#{GestaoVendasOnlineControle.siteSelecionado and not GestaoVendasOnlineControle.multiEmpresaConfig}" layout="block" styleClass="gr-container-totalizador"
                              style="margin: 10px 20px 20px 20px; width: calc(100% - 40px); border: none;">

                    <h:panelGroup layout="block" styleClass="text"
                                  style="display: inline-flex; border-bottom:#E5E5E5 1px solid; width: 100%; position: relative; height: 40px;">
                        <a4j:commandLink id="abrirCores"
                                         action="#{GestaoVendasOnlineControle.abrirCores}"
                                         onclick="addCores();"
                                         reRender="vendasOnlinePanel"
                                         styleClass="botaoModoTimeLine filtrosPrincipais #{GestaoVendasOnlineControle.abaCores ? 'ativo' : ''}">
                            <h:outputText value="Cores"/>
                        </a4j:commandLink>

                        <a4j:commandLink id="abrirMenu"
                                         action="#{GestaoVendasOnlineControle.abrirMenu}"
                                         onclick="addCores();"
                                         reRender="vendasOnlinePanel"
                                         styleClass="botaoModoTimeLine filtrosPrincipais #{GestaoVendasOnlineControle.abaMenu ? 'ativo' : ''}">
                            <h:outputText value="Menu"/>
                        </a4j:commandLink>

                        <a4j:commandLink id="abrirPagInicial"
                                         action="#{GestaoVendasOnlineControle.abrirPagInicial}"
                                         onclick="addCores();"
                                         reRender="vendasOnlinePanel"
                                         styleClass="botaoModoTimeLine filtrosPrincipais #{GestaoVendasOnlineControle.abaPagInicial ? 'ativo' : ''}">
                            <h:outputText value="Página Inicial"/>
                        </a4j:commandLink>

                        <a4j:commandLink id="abrirFotoFachada"
                                         action="#{GestaoVendasOnlineControle.abrirFotoFachada}"
                                         onclick="addCores();"
                                         reRender="vendasOnlinePanel"
                                         styleClass="botaoModoTimeLine filtrosPrincipais #{GestaoVendasOnlineControle.abaFotoFachada ? 'ativo' : ''}">
                            <h:outputText value="Foto da fachada"/>
                        </a4j:commandLink>

                        <a4j:commandLink id="abrirModaCarrosel"
                                         action="#{GestaoVendasOnlineControle.abrirModaCarrosel}"
                                         onclick="addCores();"
                                         reRender="vendasOnlinePanel"
                                         styleClass="botaoModoTimeLine filtrosPrincipais #{GestaoVendasOnlineControle.abaModaCarrosel ? 'ativo' : ''}">
                            <h:outputText value="Modalidades - Carrossel"/>
                        </a4j:commandLink>

                        <a4j:commandLink id="abrirModaBanner"
                                         action="#{GestaoVendasOnlineControle.abrirModaBanner}"
                                         onclick="addCores();"
                                         reRender="vendasOnlinePanel"
                                         styleClass="botaoModoTimeLine filtrosPrincipais #{GestaoVendasOnlineControle.abaModaBanner ? 'ativo' : ''}">
                            <h:outputText value="Modalidades - Banner"/>
                        </a4j:commandLink>

                        <a4j:commandLink id="abrirPlanos"
                                         action="#{GestaoVendasOnlineControle.abrirPlanos}"
                                         onclick="addCores();"
                                         reRender="vendasOnlinePanel"
                                         styleClass="botaoModoTimeLine filtrosPrincipais #{GestaoVendasOnlineControle.abaPlanos ? 'ativo' : ''}">
                            <h:outputText value="Planos"/>
                        </a4j:commandLink>

                        <a4j:commandLink id="abrirCapLeads"
                                         action="#{GestaoVendasOnlineControle.abrirCapLeads}"
                                         onclick="addCores();"
                                         reRender="vendasOnlinePanel"
                                         styleClass="botaoModoTimeLine filtrosPrincipais #{GestaoVendasOnlineControle.abaCapLeads ? 'ativo' : ''}">
                            <h:outputText value="Captação de leads"/>
                        </a4j:commandLink>

                        <a4j:commandLink id="abrirAgenda"
                                         action="#{GestaoVendasOnlineControle.abrirAgenda}"
                                         onclick="addCores();"
                                         reRender="vendasOnlinePanel"
                                         styleClass="botaoModoTimeLine filtrosPrincipais #{GestaoVendasOnlineControle.abaAgenda ? 'ativo' : ''}">
                            <h:outputText value="Agenda"/>
                        </a4j:commandLink>

                        <a4j:commandLink id="contato"
                                         action="#{GestaoVendasOnlineControle.abrirContato}"
                                         onclick="addCores();"
                                         reRender="vendasOnlinePanel"
                                         styleClass="botaoModoTimeLine filtrosPrincipais #{GestaoVendasOnlineControle.abaContato ? 'ativo' : ''}">
                            <h:outputText value="Contato"/>
                        </a4j:commandLink>
                        <a4j:commandLink id="integracoesHotsite"
                                         action="#{GestaoVendasOnlineControle.abrirIntegracoesHotsite}"
                                         onclick="addCores();"
                                         reRender="vendasOnlinePanel"
                                         styleClass="botaoModoTimeLine filtrosPrincipais #{GestaoVendasOnlineControle.abaIntegracoesHotsite ? 'ativo' : ''}">
                            <h:outputText value="Integrações"/>
                        </a4j:commandLink>
                        <a4j:commandLink id="publicar"
                                         action="#{GestaoVendasOnlineControle.abrirPublicar}"
                                         onclick="addCores();"
                                         oncomplete="#{GestaoVendasOnlineControle.mensagemNotificar}"
                                         reRender="vendasOnlinePanel"
                                         styleClass="botaoModoTimeLine filtrosPrincipais #{GestaoVendasOnlineControle.abaPublicar ? 'ativo' : ''}">
                            <h:outputText value="Publicar"/>
                        </a4j:commandLink>
                    </h:panelGroup>

                    <h:panelGroup layout="block" id="divAbaCores" style="width: 50%;"
                                  rendered="#{GestaoVendasOnlineControle.abaCores}">

                        <h:panelGrid width="100%" columns="2"
                                     columnClasses="w40, w60" cellpadding="10"
                                     style="margin-top: 20px">

                            <h:panelGroup
                                    styleClass="alinhamentoLabels alinhamentoLabelsQuebrada">
                                <h:outputText styleClass="texto-size-14 cinza"
                                              value="Cor dos detalhes: "></h:outputText>
                                <i class="fa-icon-question-sign tooltipster"
                                   style="color: #777"
                                   title="Escolha a opção que mais combina com a identidade visual da sua empresa."></i>
                            </h:panelGroup>
                            <h:panelGroup style="vertical-align: middle;">
                                <input type="color" id="cordetalhesAbaCores" name="body"
                                       value="${GestaoVendasOnlineControle.config.cor}">
                                <h:inputHidden
                                        value="#{GestaoVendasOnlineControle.config.cor}"
                                        id="cores"/>
                            </h:panelGroup>

                            <h:panelGroup>
                                <h:outputText styleClass="texto-size-14 cinza"
                                              value="Tema do site:"> </h:outputText>
                                <i class="fa-icon-question-sign tooltipster"
                                   style="color: #777"
                                   title="Escolha uma das opções para aplicar o fundo em seu site."></i>
                            </h:panelGroup>
                            <h:panelGrid columns="2" style="margin-top:6px;">
                                <h:panelGroup >
                                    <h:selectBooleanCheckbox styleClass="testClaro" value="#{GestaoVendasOnlineControle.config.temaClaro}">
                                        <a4j:support event="onclick"
                                                     action="#{GestaoVendasOnlineControle.temaClaro}"
                                                     reRender="vendasOnlinePanel"/>
                                    </h:selectBooleanCheckbox>
                                    <h:outputText styleClass="texto-size-14 cinza" style="margin-left: 3px" value="Claro"/>
                                    <rich:spacer width="8px"/>
                                    <h:selectBooleanCheckbox styleClass="testEscuro" value="#{GestaoVendasOnlineControle.config.temaEscuro}">
                                        <a4j:support event="onclick"
                                                     action="#{GestaoVendasOnlineControle.temaEscuro}"
                                                     reRender="vendasOnlinePanel"/>
                                    </h:selectBooleanCheckbox>
                                    <h:outputText styleClass="texto-size-14 cinza" style="margin-left: 3px" value="Escuro"/>
                                </h:panelGroup>
                            </h:panelGrid>


                        </h:panelGrid>
                    </h:panelGroup>


                    <h:panelGroup layout="block" id="divAbaPagInicial" style="width: 100%;"
                                  rendered="#{GestaoVendasOnlineControle.abaPagInicial}">

                        <h:panelGrid width="100%"
                                     columns="3"
                                     columnClasses="w33, w33, w33"
                                     cellpadding="10"
                                     style="margin-top: 20px">

                            <h:panelGrid width="95%" columns="1" cellpadding="5" style="margin-top: 5px; padding-right: 15px; border-right: 1px dotted lightgrey;">

                                <h:panelGroup styleClass="alinhamentoLabels" style="margin-top: 0px">
                                    <h:outputText styleClass="texto-size-14 texto-cor-azul"
                                                  style="font-weight: bold"
                                                  value="1ª Configuração:"> </h:outputText>
                                </h:panelGroup>

                                <h:panelGroup styleClass="alinhamentoLabels">
                                    <h:outputText styleClass="texto-size-14 cinza"
                                                  value="Adicione uma imagem de fundo para o banner: "> </h:outputText>
                                    <i class="fa-icon-question-sign tooltipster"
                                       style="color: #777"
                                       title="Lembre-se que o texto principal ficará em cima da imagem, por isso busque por ângulos que não cortem o motivo principal. "></i>
                                </h:panelGroup>

                                <h:panelGroup id="painelfotosPagInicial" layout="block" style="margin: 20px 10px;">
                                    <h:graphicImage url="#{GestaoVendasOnlineControle.paginaInicialVendasOnlineVO.urlFoto}" style="padding-left: 6px; width:100px; height:100px;"/>
                                    <span style="text-align: center;margin-top: 15px">
                                                    <a4j:commandLink style="font-size: 20px;" action="#{GestaoVendasOnlineControle.removerImagemPagInicial}" reRender="divAbaPagInicial">
                                                        <i class="fa-icon-trash"></i>
                                                    </a4j:commandLink>
                                            </span>
                                    <rich:fileUpload listHeight="0" listWidth="129" noDuplicate="false"
                                                     fileUploadListener="#{GestaoVendasOnlineControle.uploadImagemPaginaInicial1}" maxFilesQuantity="1"
                                                     addControlLabel="Procurar Arquivo" cancelEntryControlLabel="Cancelar" doneLabel="Pronto"
                                                     sizeErrorLabel="Arquivo não Enviado. Tamanho Máximo Permitido 1 MB"
                                                     progressLabel="Enviando" stopControlLabel="Parar" uploadControlLabel="Enviar"
                                                     transferErrorLabel="Falha de Transmissão" stopEntryControlLabel="Parar"
                                                     id="uploadImagemPagInicial" immediateUpload="true" autoclear="true"
                                                     acceptedTypes="png,jpg,jpeg,PNG,JPG,JPEG">

                                        <a4j:support event="onuploadcomplete" oncomplete="#{GestaoVendasOnlineControle.mensagemNotificar}" reRender="panelMensagem, divAbaPagInicial"/>
                                    </rich:fileUpload>
                                    <h:outputText styleClass="texto-size-14 cinza"
                                                  value="Tamanho obrigatório: 1920 x 1080 px - Formato recomendado: PNG com fundo transparente - Peso máximo: 1MB."> </h:outputText>
                                </h:panelGroup>

                                <h:panelGroup styleClass="alinhamentoLabels">
                                    <h:outputText styleClass="texto-size-14 cinza"
                                                  value="Texto do box arredondado: "> </h:outputText>
                                    <i class="fa-icon-question-sign tooltipster"
                                       style="color: #777"
                                       title="Aqui vocÊ pode colocar um texto simples sobre a modalidade/serviço divulgado no banner. "></i>
                                </h:panelGroup>

                                <h:panelGroup layout="block" style="font-size: 14px">
                                    <h:inputText
                                            value="#{GestaoVendasOnlineControle.paginaInicialVendasOnlineVO.textoBoxArredondado}"
                                            onblur="blurinput(this);" style="width: 90%"
                                            onfocus="focusinput(this);"
                                            size="30" maxlength="30"
                                            styleClass="testboxArredondado form inputTextVendas"/>
                                </h:panelGroup>

                                <h:panelGroup styleClass="alinhamentoLabels">
                                    <h:outputText styleClass="texto-size-14 cinza"
                                                  value="Titulo principal: "> </h:outputText>
                                    <i class="fa-icon-question-sign tooltipster"
                                       style="color: #777"
                                       title="Adicione neste campo um texto que fale mais sobre a modalidade/serviço divulgado no Texto Box Arredondado. "></i>
                                </h:panelGroup>

                                <h:panelGroup layout="block" style="font-size: 14px">
                                    <h:inputText
                                            value="#{GestaoVendasOnlineControle.paginaInicialVendasOnlineVO.tituloPrincipal}"
                                            onblur="blurinput(this);" style="width: 90%"
                                            onfocus="focusinput(this);"
                                            size="50" maxlength="50"
                                            styleClass="testTituloPrincipal form inputTextVendas"/>
                                </h:panelGroup>
                            </h:panelGrid>
                            <h:panelGrid width="95%" columns="1" cellpadding="5" style="margin-top: 5px; padding-right: 15px; border-right: 1px dotted lightgrey;">

                                <h:panelGroup styleClass="alinhamentoLabels" style="margin-top: 0px">
                                    <h:outputText styleClass="texto-size-14 texto-cor-azul"
                                                  style="font-weight: bold"
                                                  value="2ª Configuração:"> </h:outputText>
                                </h:panelGroup>

                                <h:panelGroup styleClass="alinhamentoLabels">
                                    <h:outputText styleClass="texto-size-14 cinza"
                                                  value="Adicione uma imagem de fundo para o banner: "> </h:outputText>
                                    <i class="fa-icon-question-sign tooltipster"
                                       style="color: #777"
                                       title="Lembre-se que o texto principal ficará em cima da imagem, por isso busque por ângulos que não cortem o motivo principal. "></i>
                                </h:panelGroup>

                                <h:panelGroup id="painelfotosPagInicial2" layout="block" style="margin: 20px 10px;">
                                    <h:graphicImage url="#{GestaoVendasOnlineControle.paginaInicialVendasOnlineVO2.urlFoto}" style="padding-left: 6px; width:100px; height:100px;"/>
                                    <span style="text-align: center;margin-top: 15px">
                                                    <a4j:commandLink style="font-size: 20px;" action="#{GestaoVendasOnlineControle.removerImagemPagInicial2}" reRender="divAbaPagInicial">
                                                        <i class="fa-icon-trash"></i>
                                                    </a4j:commandLink>
                                            </span>

                                    <rich:fileUpload listHeight="0" listWidth="129" noDuplicate="false"
                                                     fileUploadListener="#{GestaoVendasOnlineControle.uploadImagemPaginaInicial2}" maxFilesQuantity="1"
                                                     addControlLabel="Procurar Arquivo" cancelEntryControlLabel="Cancelar" doneLabel="Pronto"
                                                     sizeErrorLabel="Arquivo não Enviado. Tamanho Máximo Permitido 1 MB"
                                                     progressLabel="Enviando" stopControlLabel="Parar" uploadControlLabel="Enviar"
                                                     transferErrorLabel="Falha de Transmissão" stopEntryControlLabel="Parar"
                                                     id="uploadImagemPagInicial2" immediateUpload="true" autoclear="true"
                                                     acceptedTypes="png,jpg,jpeg,PNG,JPG,JPEG">

                                        <a4j:support event="onuploadcomplete" oncomplete="#{GestaoVendasOnlineControle.mensagemNotificar}" reRender="panelMensagem, divAbaPagInicial"/>
                                    </rich:fileUpload>
                                    <h:outputText styleClass="texto-size-14 cinza"
                                                  value="Tamanho obrigatório: 1920 x 1080 px - Formato recomendado: PNG com fundo transparente - Peso máximo: 1MB."> </h:outputText>
                                </h:panelGroup>

                                <h:panelGroup styleClass="alinhamentoLabels">
                                    <h:outputText styleClass="texto-size-14 cinza"
                                                  value="Texto do box arredondado: "> </h:outputText>
                                    <i class="fa-icon-question-sign tooltipster"
                                       style="color: #777"
                                       title="Aqui vocÊ pode colocar um texto simples sobre a modalidade/serviço divulgado no banner. "></i>
                                </h:panelGroup>

                                <h:panelGroup layout="block" style="font-size: 14px">
                                    <h:inputText
                                            value="#{GestaoVendasOnlineControle.paginaInicialVendasOnlineVO2.textoBoxArredondado}"
                                            onblur="blurinput(this);" style="width: 90%"
                                            onfocus="focusinput(this);"
                                            size="30" maxlength="30"
                                            styleClass="testboxArredondado form inputTextVendas"/>
                                </h:panelGroup>

                                <h:panelGroup styleClass="alinhamentoLabels">
                                    <h:outputText styleClass="texto-size-14 cinza"
                                                  value="Titulo principal: "> </h:outputText>
                                    <i class="fa-icon-question-sign tooltipster"
                                       style="color: #777"
                                       title="Adicione neste campo um texto que fale mais sobre a modalidade/serviço divulgado no Texto Box Arredondado. "></i>
                                </h:panelGroup>

                                <h:panelGroup layout="block" style="font-size: 14px">
                                    <h:inputText
                                            value="#{GestaoVendasOnlineControle.paginaInicialVendasOnlineVO2.tituloPrincipal}"
                                            onblur="blurinput(this);" style="width: 90%"
                                            onfocus="focusinput(this);"
                                            size="50" maxlength="50"
                                            styleClass="testTituloPrincipal form inputTextVendas"/>
                                </h:panelGroup>
                            </h:panelGrid>

                            <h:panelGrid width="95%" columns="1" cellpadding="5" style="margin-top: 5px">

                                <h:panelGroup styleClass="alinhamentoLabels" style="margin-top: 0px">
                                    <h:outputText styleClass="texto-size-14 texto-cor-azul"
                                                  style="font-weight: bold"
                                                  value="3ª Configuração:"> </h:outputText>
                                </h:panelGroup>

                                <h:panelGroup styleClass="alinhamentoLabels">
                                    <h:outputText styleClass="texto-size-14 cinza"
                                                  value="Adicione uma imagem de fundo para o banner: "> </h:outputText>
                                    <i class="fa-icon-question-sign tooltipster"
                                       style="color: #777"
                                       title="Lembre-se que o texto principal ficará em cima da imagem, por isso busque por ângulos que não cortem o motivo principal. "></i>
                                </h:panelGroup>

                                <h:panelGroup id="painelfotosPagInicial3" layout="block" style="margin: 20px 10px;">
                                    <h:graphicImage url="#{GestaoVendasOnlineControle.paginaInicialVendasOnlineVO3.urlFoto}" style="padding-left: 6px; width:100px; height:100px;"/>
                                    <span style="text-align: center;margin-top: 15px">
                                                    <a4j:commandLink style="font-size: 20px;" action="#{GestaoVendasOnlineControle.removerImagemPagInicial3}" reRender="divAbaPagInicial">
                                                        <i class="fa-icon-trash"></i>
                                                    </a4j:commandLink>
                                            </span>
                                    <rich:fileUpload listHeight="0" listWidth="129" noDuplicate="false"
                                                     fileUploadListener="#{GestaoVendasOnlineControle.uploadImagemPaginaInicial3}" maxFilesQuantity="1"
                                                     addControlLabel="Procurar Arquivo" cancelEntryControlLabel="Cancelar" doneLabel="Pronto"
                                                     sizeErrorLabel="Arquivo não Enviado. Tamanho Máximo Permitido 1 MB"
                                                     progressLabel="Enviando" stopControlLabel="Parar" uploadControlLabel="Enviar"
                                                     transferErrorLabel="Falha de Transmissão" stopEntryControlLabel="Parar"
                                                     id="uploadImagemPagInicial3" immediateUpload="true" autoclear="true"
                                                     acceptedTypes="png,jpg,jpeg,PNG,JPG,JPEG">

                                        <a4j:support event="onuploadcomplete" oncomplete="#{GestaoVendasOnlineControle.mensagemNotificar}" reRender="panelMensagem, divAbaPagInicial"/>
                                    </rich:fileUpload>
                                    <h:outputText styleClass="texto-size-14 cinza"
                                                  value="Tamanho obrigatório: 1920 x 1080 px - Formato recomendado: PNG com fundo transparente - Peso máximo: 1MB."> </h:outputText>
                                </h:panelGroup>

                                <h:panelGroup styleClass="alinhamentoLabels">
                                    <h:outputText styleClass="texto-size-14 cinza"
                                                  value="Texto do box arredondado: "> </h:outputText>
                                    <i class="fa-icon-question-sign tooltipster"
                                       style="color: #777"
                                       title="Aqui vocÊ pode colocar um texto simples sobre a modalidade/serviço divulgado no banner. "></i>
                                </h:panelGroup>

                                <h:panelGroup layout="block" style="font-size: 14px">
                                    <h:inputText
                                            value="#{GestaoVendasOnlineControle.paginaInicialVendasOnlineVO3.textoBoxArredondado}"
                                            onblur="blurinput(this);" style="width: 90%"
                                            onfocus="focusinput(this);"
                                            size="30" maxlength="30"
                                            styleClass="testboxArredondado form inputTextVendas"/>
                                </h:panelGroup>

                                <h:panelGroup styleClass="alinhamentoLabels">
                                    <h:outputText styleClass="texto-size-14 cinza"
                                                  value="Titulo principal: "> </h:outputText>
                                    <i class="fa-icon-question-sign tooltipster"
                                       style="color: #777"
                                       title="Adicione neste campo um texto que fale mais sobre a modalidade/serviço divulgado no Texto Box Arredondado. "></i>
                                </h:panelGroup>

                                <h:panelGroup layout="block" style="font-size: 14px">
                                    <h:inputText
                                            value="#{GestaoVendasOnlineControle.paginaInicialVendasOnlineVO3.tituloPrincipal}"
                                            onblur="blurinput(this);" style="width: 90%"
                                            onfocus="focusinput(this);"
                                            size="50" maxlength="50"
                                            styleClass="testTituloPrincipal form inputTextVendas"/>
                                </h:panelGroup>
                            </h:panelGrid>

                        </h:panelGrid>

                    </h:panelGroup>

                    <h:panelGroup layout="block" id="divAbaFotoFachada" style="width: 50%;"
                                  rendered="#{GestaoVendasOnlineControle.abaFotoFachada}">

                        <h:panelGrid width="100%" columns="2"
                                     columnClasses="w40, w60" cellpadding="10"
                                     style="margin-top: 20px">

                            <h:panelGroup styleClass="alinhamentoLabels">
                                <h:outputText styleClass="texto-size-14 cinza"
                                              value="Adicione a foto da fachada da sua academia: "> </h:outputText>
                                <i class="fa-icon-question-sign tooltipster"
                                   style="color: #777"
                                   title="Adicione uma foto da fachada da sua academia para aparecer em seu site."></i>
                            </h:panelGroup>

                            <h:panelGroup id="painelfotosFachada" layout="block" style="margin: 20px 10px;">
                                <h:graphicImage url="#{GestaoVendasOnlineControle.fotoFachadaVendasOnlineVO.urlFoto}" style="padding-left: 6px; width:100px; height:100px;"/>
                                <span style="text-align: center;margin-top: 15px">
                                                    <a4j:commandLink style="font-size: 20px;" action="#{GestaoVendasOnlineControle.removerFotoFachada()}" reRender="divAbaFotoFachada">
                                                        <i class="fa-icon-trash"></i>
                                                    </a4j:commandLink>
                                            </span>

                                <rich:fileUpload listHeight="0" listWidth="129" noDuplicate="false"
                                                 fileUploadListener="#{GestaoVendasOnlineControle.uploadImagemFotoFachada}" maxFilesQuantity="1"
                                                 addControlLabel="Procurar Arquivo" cancelEntryControlLabel="Cancelar" doneLabel="Pronto"
                                                 sizeErrorLabel="Arquivo no Enviado. Tamanho Mximo Permitido 1 MB"
                                                 progressLabel="Enviando" stopControlLabel="Parar" uploadControlLabel="Enviar"
                                                 transferErrorLabel="Falha de Transmisso" stopEntryControlLabel="Parar"
                                                 id="uploadImagemFachada" immediateUpload="true" autoclear="true"
                                                 acceptedTypes="png,jpg,jpeg,PNG,JPG,JPEG">

                                    <a4j:support event="onuploadcomplete" oncomplete="#{GestaoVendasOnlineControle.mensagemNotificar}" reRender="panelMensagem, divAbaFotoFachada"/>

                                </rich:fileUpload>

                                <h:outputText styleClass="texto-size-14 cinza"
                                              value="Tamanho obrigatório: 1920 x 1080 px - Formato recomendado: PNG com fundo transparente - Peso máximo: 1MB."> </h:outputText>

                            </h:panelGroup>
                        </h:panelGrid>
                    </h:panelGroup>

                    <h:panelGroup layout="block" id="divAbaMenu" style="width: 70%;"
                                  rendered="#{GestaoVendasOnlineControle.abaMenu}">

                        <h:panelGrid width="100%" columns="4"
                                     columnClasses="w20, w30" cellpadding="10"
                                     style="margin-top: 20px">

                            <h:panelGroup styleClass="alinhamentoLabels">
                                <h:outputText styleClass="texto-size-14 cinza"
                                              value="Adicione a logo da sua empresa: "> </h:outputText>
                                <i class="fa-icon-question-sign tooltipster"
                                   style="color: #777"
                                   title="Esta será a marca principal que aparecerá em seu site."></i>
                            </h:panelGroup>

                            <h:panelGroup id="painelfotosMenu" layout="block" style="margin: 20px 10px;">
                                <h:graphicImage url="#{GestaoVendasOnlineControle.menuVendasOnlineVO.urlFoto}" style="padding-left: 6px; width:100px; height:100px;"/>
                                <span style="text-align: center;margin-top: 15px">
                                                    <a4j:commandLink style="font-size: 20px;" action="#{GestaoVendasOnlineControle.removerImagemMenu}" reRender="divAbaMenu">
                                                        <i class="fa-icon-trash"></i>
                                                    </a4j:commandLink>
                                            </span>

                                <rich:fileUpload listHeight="0" listWidth="129" noDuplicate="false"
                                                 fileUploadListener="#{GestaoVendasOnlineControle.uploadImagemMenu}" maxFilesQuantity="1"
                                                 addControlLabel="Procurar Arquivo" cancelEntryControlLabel="Cancelar" doneLabel="Pronto"
                                                 sizeErrorLabel="Arquivo não Enviado. Tamanho Máximo Permitido 1 MB."
                                                 progressLabel="Enviando" stopControlLabel="Parar" uploadControlLabel="Enviar"
                                                 transferErrorLabel="Falha de Transmissão" stopEntryControlLabel="Parar"
                                                 id="uploadImagemMenul" immediateUpload="true" autoclear="true"
                                                 acceptedTypes="png,jpg,jpeg,PNG,JPG,JPEG">

                                    <a4j:support event="onuploadcomplete" reRender="divAbaMenu"/>
                                </rich:fileUpload>
                                <h:outputText styleClass="texto-size-14 cinza"
                                              value="Tamanho obrigatório: 150 x 71 px - Formato recomendado: PNG/SVG com fundo transparente - Peso máximo: 1MB."> </h:outputText>

                            </h:panelGroup>

                            <h:panelGroup styleClass="alinhamentoLabels">
                            </h:panelGroup>

                            <h:panelGroup layout="block">
                            </h:panelGroup>

                            <h:panelGroup styleClass="alinhamentoLabels">
                                <h:outputText styleClass="texto-size-14 cinza"
                                              value="Menu personalizado 01: "> </h:outputText>
                                <i class="fa-icon-question-sign tooltipster"
                                   style="color: #777"
                                   title="Informe o um nome para este menu. "></i>
                            </h:panelGroup>

                            <h:panelGroup layout="block" style="font-size: 14px">
                                <h:inputText title="Max. 30 caracteres."
                                             value="#{GestaoVendasOnlineControle.menuVendasOnlineVO.menuPersonalizado01}"
                                             onblur="blurinput(this);" style="width: 90%"
                                             onfocus="focusinput(this);"
                                             size="12" maxlength="12"
                                             styleClass="testmenu01 form inputTextVendas"/>
                            </h:panelGroup>

                            <h:panelGroup styleClass="alinhamentoLabels">
                                <h:outputText styleClass="texto-size-14 cinza"
                                              value="Link - Menu personalizado 01: "> </h:outputText>
                                <i class="fa-icon-question-sign tooltipster"
                                   style="color: #777"
                                   title="Informe qual link deverá abrir neste menu."></i>
                            </h:panelGroup>

                            <h:panelGroup layout="block" style="font-size: 14px">
                                <h:inputText title="Max. 50 caracteres."
                                             value="#{GestaoVendasOnlineControle.menuVendasOnlineVO.linkMenuPersonalizado01}"
                                             onblur="blurinput(this);" style="width: 90%"
                                             onfocus="focusinput(this);"
                                             size="50" maxlength="50"
                                             styleClass="testmenuLink01 form inputTextVendas"/>
                            </h:panelGroup>

                            <h:panelGroup styleClass="alinhamentoLabels">
                                <h:outputText styleClass="texto-size-14 cinza"
                                              value="Menu personalizado 02: "> </h:outputText>
                                <i class="fa-icon-question-sign tooltipster"
                                   style="color: #777"
                                   title="Informe o um nome para este menu. "></i>
                            </h:panelGroup>

                            <h:panelGroup layout="block" style="font-size: 14px">
                                <h:inputText title="Max. 30 caracteres."
                                             value="#{GestaoVendasOnlineControle.menuVendasOnlineVO.menuPersonalizado02}"
                                             onblur="blurinput(this);" style="width: 90%"
                                             onfocus="focusinput(this);"
                                             size="12" maxlength="12"
                                             styleClass="testmenu02 form inputTextVendas"/>
                            </h:panelGroup>

                            <h:panelGroup styleClass="alinhamentoLabels">
                                <h:outputText styleClass="texto-size-14 cinza"
                                              value="Link - Menu personalizado 02: "> </h:outputText>
                                <i class="fa-icon-question-sign tooltipster"
                                   style="color: #777"
                                   title="Informe qual link deverá abrir neste menu."></i>
                            </h:panelGroup>

                            <h:panelGroup layout="block" style="font-size: 14px">
                                <h:inputText title="Max. 50 caracteres."
                                             value="#{GestaoVendasOnlineControle.menuVendasOnlineVO.linkMenuPersonalizado02}"
                                             onblur="blurinput(this);" style="width: 90%"
                                             onfocus="focusinput(this);"
                                             size="50" maxlength="50"
                                             styleClass="testmenuLink02 form inputTextVendas"/>
                            </h:panelGroup>

                            <h:panelGroup styleClass="alinhamentoLabels">
                                <h:outputText styleClass="texto-size-14 cinza"
                                              value="Menu personalizado 03: "> </h:outputText>
                                <i class="fa-icon-question-sign tooltipster"
                                   style="color: #777"
                                   title="Informe o um nome para este menu. "></i>
                            </h:panelGroup>

                            <h:panelGroup layout="block" style="font-size: 14px">
                                <h:inputText title="Max. 30 caracteres."
                                             value="#{GestaoVendasOnlineControle.menuVendasOnlineVO.menuPersonalizado03}"
                                             onblur="blurinput(this);" style="width: 90%"
                                             onfocus="focusinput(this);"
                                             size="12" maxlength="12"
                                             styleClass="testmenu03 form inputTextVendas"/>
                            </h:panelGroup>

                            <h:panelGroup styleClass="alinhamentoLabels">
                                <h:outputText styleClass="texto-size-14 cinza"
                                              value="Link - Menu personalizado 03: "> </h:outputText>
                                <i class="fa-icon-question-sign tooltipster"
                                   style="color: #777"
                                   title="Informe qual link deverá abrir neste menu."></i>
                            </h:panelGroup>

                            <h:panelGroup layout="block" style="font-size: 14px">
                                <h:inputText title="Max. 50 caracteres."
                                             value="#{GestaoVendasOnlineControle.menuVendasOnlineVO.linkMenuPersonalizado03}"
                                             onblur="blurinput(this);" style="width: 90%"
                                             onfocus="focusinput(this);"
                                             size="50" maxlength="50"
                                             styleClass="testmenuLink03 form inputTextVendas"/>
                            </h:panelGroup>
                        </h:panelGrid>
                    </h:panelGroup>

                    <h:panelGroup layout="block" id="divAbaModaCarrousel" style="width: 100%;" rendered="#{GestaoVendasOnlineControle.abaModaCarrosel}">

                        <!--INICIO SEÇÃO DE MODALIDADE OU AMBIENTE-->
                        <h:panelGrid width="50%" columns="2"
                                         columnClasses="w40, w60" cellpadding="10"
                                         style="margin-top: 20px">

                            <h:panelGroup styleClass="alinhamentoLabels">
                                <h:outputText styleClass="texto-size-14 cinza" value="Titulo da seção:"/>
                                <i class="fa-icon-question-sign tooltipster"
                                   style="color: #777"
                                   title="Adicione aqui o titulo seção de modalidade ou ambiente de acordo como ela deve ser exibida no site.
                                          Caso não seja preenchido nenhum valor, será definido um automaticamente no site."></i>
                            </h:panelGroup>

                            <h:panelGroup layout="block" style="font-size: 14px">
                                <h:inputText title="Max. 26 caracteres."
                                             id="tituloDaSecaoModalidadeCarrossel"
                                             value="#{GestaoVendasOnlineControle.configModalidadeCarrosselVendasOnlineVO.tituloModalidadeVendas}"
                                             onblur="blurinput(this);" style="width: 90%"
                                             onfocus="focusinput(this);"
                                             size="26" maxlength="26"
                                             styleClass="testDescricaoCabecalho form inputTextVendas"/>
                            </h:panelGroup>

                            <h:panelGroup styleClass="alinhamentoLabels">
                                <h:outputText styleClass="texto-size-14 cinza" value="Descrição da seção:"/>
                                <i class="fa-icon-question-sign tooltipster"
                                   style="color: #777"
                                   title="Adicione aqui a descrição da seção de modalidade ou ambiente acordo como ela deve ser exibida no site.
                                          Caso não seja preenchido nenhum valor, será definido um automaticamente no site."></i>
                            </h:panelGroup>

                            <h:panelGroup layout="block" style="font-size: 14px">
                                <h:inputTextarea
                                        id="descricaoDaSecaoModalidadeCarrossel"
                                        value="#{GestaoVendasOnlineControle.configModalidadeCarrosselVendasOnlineVO.descricaoModalidadeVendas}"
                                        onblur="blurinput(this); verificarTamanhoMaximo(this,270);" onkeypress="verificarTamanhoMaximo(this,270);"
                                        onkeydown="verificarTamanhoMaximo(this,270);" onkeyup="verificarTamanhoMaximo(this,270);"
                                        style="width: 98%;height: 100px;" onfocus="focusinput(this);"
                                        styleClass="testDescricaoModalidade01 form inputTextVendas"/>
                            </h:panelGroup>

                        </h:panelGrid>

                        <!-- Aplicar Alterações Seção de Modalidade/Ambiente-->
                        <h:panelGroup layout="block" styleClass="container-botoes"
                                      style="width: 100%; padding-bottom: 10px; border-bottom: 1px dotted lightgray;">
                            <a4j:commandLink id="gravarConfigModalidade"
                                             action="#{GestaoVendasOnlineControle.gravarConfigModalidade}"
                                             onclick="addCores();"
                                             reRender="vendasOnlinePanel"
                                             oncomplete="#{GestaoVendasOnlineControle.mensagemNotificar}"
                                             value="Aplicar alterações"
                                             styleClass="testAplicarAlteracao botaoPrimario texto-size-14-real"/>
                        </h:panelGroup>

                        <!-- FIM MODALIDADE GERAL -->

                        <h:panelGrid width="100%" columns="3" columnClasses="w30,w30,w30" cellpadding="5" style="margin-top: 5px">
                            <h:panelGrid width="95%" columns="1" cellpadding="5" style="margin-top: 5px">
                                <!-- INICIO 1 MODAL CARROSEL-->
                                <h:panelGroup styleClass="alinhamentoLabels">
                                    <h:outputText styleClass="texto-size-14 cinza" value="Imagem da modalidade/ambiente 1: "> </h:outputText>
                                    <i class="fa-icon-question-sign tooltipster" style="color: #777"
                                       title="Escolha uma imagem que ilustre a modalidade/ambiente que será adicionado(a) "></i>
                                </h:panelGroup>

                                <h:panelGroup id="painelfotosModaCarrousel" layout="block" style="margin: 20px 10px;">
                                    <c:if test="${not empty GestaoVendasOnlineControle.modalidadeCarrouselVendasOnlineVO.nomeArquivo}">
                                        <h:outputText value="#{GestaoVendasOnlineControle.modalidadeCarrouselVendasOnlineVO.nomeArquivo}"/>
                                    </c:if>
                                    <c:if test="${not empty GestaoVendasOnlineControle.modalidadeCarrouselVendasOnlineVO.urlFoto}">
                                        <h:graphicImage url="#{GestaoVendasOnlineControle.modalidadeCarrouselVendasOnlineVO.urlFoto}"
                                                        style="padding-left: 6px; width:100px; height:100px;"/>
                                    </c:if>
                                    <span style="text-align: center;margin-top: 15px">
                                            <a4j:commandLink style="font-size: 20px;" action="#{GestaoVendasOnlineControle.removerImagemModaCarrousel}"
                                                             reRender="painelfotosModaCarrousel, divAbaModaCarrousel">
                                                <i class="fa-icon-trash"></i>
                                            </a4j:commandLink>
                                    </span>

                                    <rich:fileUpload listHeight="0" listWidth="129" noDuplicate="false"
                                                     fileUploadListener="#{GestaoVendasOnlineControle.uploadImagemModalidadeCarrousel}" maxFilesQuantity="1"
                                                     addControlLabel="Procurar Arquivo" cancelEntryControlLabel="Cancelar" doneLabel="Pronto"
                                                     sizeErrorLabel="Arquivo não Enviado. Tamanho Máximo Permitido 1 MB"
                                                     progressLabel="Enviando" stopControlLabel="Parar" uploadControlLabel="Enviar"
                                                     transferErrorLabel="Falha de Transmissão" stopEntryControlLabel="Parar"
                                                     id="uploadImagemModalidadeCarrousel" immediateUpload="true" autoclear="true"
                                                     acceptedTypes="png,jpg,jpeg,PNG,JPG,JPEG">


                                        <a4j:support event="onuploadcomplete" oncomplete="#{GestaoVendasOnlineControle.mensagemNotificar}" reRender="panelMensagem, divAbaModaCarrousel"/>

                                    </rich:fileUpload>
                                    <h:outputText styleClass="texto-size-14 cinza"
                                                  value="Tamanho obrigatório: 378 x 236 px - Formato recomendado: PNG/SVG com fundo transparente - Peso máximo: 1MB."> </h:outputText>
                                </h:panelGroup>

                                <h:panelGroup styleClass="alinhamentoLabels">
                                    <h:outputText styleClass="texto-size-14 cinza"
                                                  value="Nome da modalidade/ambiente 1: "> </h:outputText>
                                    <i class="fa-icon-question-sign tooltipster"
                                       style="color: #777"
                                       title="Adicione aqui o nome da modalidade/ambiente de acordo como ela deve ser exibida no site."></i>
                                </h:panelGroup>

                                <h:panelGroup layout="block" style="font-size: 14px">
                                    <h:inputText
                                            value="#{GestaoVendasOnlineControle.modalidadeCarrouselVendasOnlineVO.tituloModalidadeVendas}"
                                            onblur="blurinput(this);" style="width: 98%"
                                            onfocus="focusinput(this);"
                                            maxlength="100"
                                            styleClass="testModadlidade01 form inputTextVendas"/>
                                </h:panelGroup>

                                <h:panelGroup styleClass="alinhamentoLabels">
                                    <h:outputText styleClass="texto-size-14 cinza"
                                                  value="Descrição da modalidade/ambiente 1: "> </h:outputText>
                                    <i class="fa-icon-question-sign tooltipster"
                                       style="color: #777"
                                       title="Aqui você pode colocar um texto simples sobre a modalidade/serviço divulgado no banner"></i>
                                </h:panelGroup>

                                <h:panelGroup layout="block" style="font-size: 14px">
                                    <h:inputTextarea
                                            value="#{GestaoVendasOnlineControle.modalidadeCarrouselVendasOnlineVO.descricaoModalidadeVendas}"
                                            onblur="blurinput(this); verificarTamanhoMaximo(this,270);" onkeypress="verificarTamanhoMaximo(this,270);"
                                            onkeydown="verificarTamanhoMaximo(this,270);" onkeyup="verificarTamanhoMaximo(this,270);"
                                            style="width: 98%;height: 100px;" onfocus="focusinput(this);"

                                            styleClass="testDescricaoModalidade01 form inputTextVendas"/>
                                </h:panelGroup>
                                <!-- FIM CARROSEL 1 -->
                            </h:panelGrid>
                            <h:panelGrid width="98%" columns="1" columnClasses="w100" cellpadding="5" style="margin-top: 5px">
                                <!-- INICIO 2 MODAL CARROSEL-->
                                <h:panelGroup styleClass="alinhamentoLabels">
                                    <h:outputText styleClass="texto-size-14 cinza" value="Imagem da modalidade/ambiente 2: "> </h:outputText>
                                    <i class="fa-icon-question-sign tooltipster" style="color: #777"
                                       title="Escolha uma imagem que ilustre a modalidade/ambiente que será adicionado(a)"></i>
                                </h:panelGroup>

                                <h:panelGroup id="painelfotosModaCarrousel2" layout="block" style="margin: 20px 10px;">
                                    <c:if test="${not empty GestaoVendasOnlineControle.modalidadeCarrouselVendasOnlineVO2.nomeArquivo}">
                                        <h:outputText value="#{GestaoVendasOnlineControle.modalidadeCarrouselVendasOnlineVO2.nomeArquivo}"/>
                                    </c:if>
                                    <c:if test="${not empty GestaoVendasOnlineControle.modalidadeCarrouselVendasOnlineVO2.urlFoto}">
                                        <h:graphicImage url="#{GestaoVendasOnlineControle.modalidadeCarrouselVendasOnlineVO2.urlFoto}"
                                                        style="padding-left: 6px; width:100px; height:100px;"/>
                                    </c:if>
                                    <span style="text-align: center;margin-top: 15px">
                                            <a4j:commandLink style="font-size: 20px;" action="#{GestaoVendasOnlineControle.removerImagemModaCarrousel2}"
                                                             reRender="painelfotosModaCarrousel2, divAbaModaCarrousel">
                                                <i class="fa-icon-trash"></i>
                                            </a4j:commandLink>
                                    </span>

                                    <rich:fileUpload listHeight="0" listWidth="129" noDuplicate="false"
                                                     fileUploadListener="#{GestaoVendasOnlineControle.uploadImagemModalidadeCarrousel2}" maxFilesQuantity="1"
                                                     addControlLabel="Procurar Arquivo" cancelEntryControlLabel="Cancelar" doneLabel="Pronto"
                                                     sizeErrorLabel="Arquivo não Enviado. Tamanho Máximo Permitido 1 MB"
                                                     progressLabel="Enviando" stopControlLabel="Parar" uploadControlLabel="Enviar"
                                                     transferErrorLabel="Falha de Transmissão" stopEntryControlLabel="Parar"
                                                     id="uploadImagemModalidadeCarrousel2" immediateUpload="true" autoclear="true"
                                                     acceptedTypes="png,jpg,jpeg,PNG,JPG,JPEG">

                                        <a4j:support event="onuploadcomplete" oncomplete="#{GestaoVendasOnlineControle.mensagemNotificar}" reRender="panelMensagem, divAbaModaCarrousel"/>
                                    </rich:fileUpload>
                                    <h:outputText styleClass="texto-size-14 cinza"
                                                  value="Tamanho obrigatório: 378 x 236 px - Formato recomendado: PNG/SVG com fundo transparente - Peso máximo: 1MB."> </h:outputText>
                                </h:panelGroup>

                                <h:panelGroup styleClass="alinhamentoLabels">
                                    <h:outputText styleClass="texto-size-14 cinza"
                                                  value="Nome da modalidade/ambiente 2: "> </h:outputText>
                                    <i class="fa-icon-question-sign tooltipster"
                                       style="color: #777"
                                       title="Adicione aqui o nome da modalidade/ambiente de acordo como ela deve ser exibida no site."></i>
                                </h:panelGroup>

                                <h:panelGroup layout="block" style="font-size: 14px">
                                    <h:inputText
                                            value="#{GestaoVendasOnlineControle.modalidadeCarrouselVendasOnlineVO2.tituloModalidadeVendas}"
                                            onblur="blurinput(this);" style="width: 98%"
                                            onfocus="focusinput(this);"
                                            size="26" maxlength="26"
                                            styleClass="testModalidade02 form inputTextVendas"/>
                                </h:panelGroup>

                                <h:panelGroup styleClass="alinhamentoLabels">
                                    <h:outputText styleClass="texto-size-14 cinza"
                                                  value="Descrição da modalidade/ambiente 2: "> </h:outputText>
                                    <i class="fa-icon-question-sign tooltipster"
                                       style="color: #777"
                                       title="Aqui você pode colocar um texto simples sobre a modalidade/serviço divulgado no banner"></i>
                                </h:panelGroup>

                                <h:panelGroup layout="block" style="font-size: 14px">
                                    <h:inputTextarea
                                            value="#{GestaoVendasOnlineControle.modalidadeCarrouselVendasOnlineVO2.descricaoModalidadeVendas}"
                                            onblur="blurinput(this); verificarTamanhoMaximo(this,270);" onkeypress="verificarTamanhoMaximo(this,270);"
                                            onkeydown="verificarTamanhoMaximo(this,270);" onkeyup="verificarTamanhoMaximo(this,270);"
                                            style="width: 98%;height: 100px;" onfocus="focusinput(this);" rows="5"
                                            styleClass="testDescricaoModalidade02 form inputTextVendas"/>
                                </h:panelGroup>
                                <!-- FIM CARROSEL 2 -->
                            </h:panelGrid>
                            <h:panelGrid width="98%" columns="1" columnClasses="w100" cellpadding="5" style="margin-top: 5px">
                                <!-- INICIO 3 MODAL CARROSEL-->
                                <h:panelGroup styleClass="alinhamentoLabels">
                                    <h:outputText styleClass="texto-size-14 cinza" value="Imagem da modalidade/ambiente 3: "> </h:outputText>
                                    <i class="fa-icon-question-sign tooltipster" style="color: #777"
                                       title="Escolha uma imagem que ilustre a modalidade/ambiente que será adicionado(a)"></i>
                                </h:panelGroup>

                                <h:panelGroup id="painelfotosModaCarrousel3" layout="block" style="margin: 20px 10px;">
                                    <c:if test="${not empty GestaoVendasOnlineControle.modalidadeCarrouselVendasOnlineVO3.nomeArquivo}">
                                        <h:outputText value="#{GestaoVendasOnlineControle.modalidadeCarrouselVendasOnlineVO3.nomeArquivo}"/>
                                    </c:if>
                                    <c:if test="${not empty GestaoVendasOnlineControle.modalidadeCarrouselVendasOnlineVO3.urlFoto}">
                                        <h:graphicImage url="#{GestaoVendasOnlineControle.modalidadeCarrouselVendasOnlineVO3.urlFoto}"
                                                        style="padding-left: 6px; width:100px; height:100px;"/>
                                    </c:if>
                                    <span style="text-align: center;margin-top: 15px">
                                            <a4j:commandLink style="font-size: 20px;" action="#{GestaoVendasOnlineControle.removerImagemModaCarrousel3}"
                                                             reRender="painelfotosModaCarrousel3, divAbaModaCarrousel">
                                                <i class="fa-icon-trash"></i>
                                            </a4j:commandLink>
                                    </span>

                                    <rich:fileUpload listHeight="0" listWidth="129" noDuplicate="false"
                                                     fileUploadListener="#{GestaoVendasOnlineControle.uploadImagemModalidadeCarrousel3}" maxFilesQuantity="1"
                                                     addControlLabel="Procurar Arquivo" cancelEntryControlLabel="Cancelar" doneLabel="Pronto"
                                                     sizeErrorLabel="Arquivo não Enviado. Tamanho Máximo Permitido 1 MB"
                                                     progressLabel="Enviando" stopControlLabel="Parar" uploadControlLabel="Enviar"
                                                     transferErrorLabel="Falha de Transmissão" stopEntryControlLabel="Parar"
                                                     id="uploadImagemModalidadeCarrousel3" immediateUpload="true" autoclear="true"
                                                     acceptedTypes="png,jpg,jpeg,PNG,JPG,JPEG">

                                        <a4j:support event="onuploadcomplete" oncomplete="#{GestaoVendasOnlineControle.mensagemNotificar}" reRender="panelMensagem, divAbaModaCarrousel"/>
                                    </rich:fileUpload>
                                    <h:outputText styleClass="texto-size-14 cinza"
                                                  value="Tamanho obrigatório: 378 x 236 px - Formato recomendado: PNG/SVG com fundo transparente - Peso máximo: 1MB."> </h:outputText>
                                </h:panelGroup>

                                <h:panelGroup styleClass="alinhamentoLabels">
                                    <h:outputText styleClass="texto-size-14 cinza"
                                                  value="Nome da modalidade/ambiente 3: "> </h:outputText>
                                    <i class="fa-icon-question-sign tooltipster"
                                       style="color: #777"
                                       title="Adicione aqui o nome da modalidade/ambiente de acordo como ela deve ser exibida no site. "></i>
                                </h:panelGroup>

                                <h:panelGroup layout="block" style="font-size: 14px">
                                    <h:inputText
                                            value="#{GestaoVendasOnlineControle.modalidadeCarrouselVendasOnlineVO3.tituloModalidadeVendas}"
                                            onblur="blurinput(this);" style="width: 98%"
                                            onfocus="focusinput(this);"
                                            size="26" maxlength="26"
                                            styleClass="testModalidade03 form inputTextVendas"/>
                                </h:panelGroup>

                                <h:panelGroup styleClass="alinhamentoLabels">
                                    <h:outputText styleClass="texto-size-14 cinza"
                                                  value="Descrição da modalidade/ambiente 3: "> </h:outputText>
                                    <i class="fa-icon-question-sign tooltipster"
                                       style="color: #777"
                                       title="Aqui você pode colocar um texto simples sobre a modalidade/serviço divulgado no banner"></i>
                                </h:panelGroup>

                                <h:panelGroup layout="block" style="font-size: 14px">
                                    <h:inputTextarea
                                            value="#{GestaoVendasOnlineControle.modalidadeCarrouselVendasOnlineVO3.descricaoModalidadeVendas}"
                                            onblur="blurinput(this); verificarTamanhoMaximo(this,270);" onkeypress="verificarTamanhoMaximo(this,270);"
                                            onkeydown="verificarTamanhoMaximo(this,270);" onkeyup="verificarTamanhoMaximo(this,270);"
                                            style="width: 98%;height: 100px;" onfocus="focusinput(this);" rows="3"
                                            styleClass="testDescricaoModalidade03 form inputTextVendas"/>
                                </h:panelGroup>
                                <!-- FIM CARROSEL 3 -->
                            </h:panelGrid>
                        </h:panelGrid>
                    </h:panelGroup>

                    <h:panelGroup layout="block" id="divAbaModaBanner" style="width: 100%;" rendered="#{GestaoVendasOnlineControle.abaModaBanner}">
                        <h:panelGrid width="100%" columns="3" columnClasses="w30,w30,w30" cellpadding="5" style="margin-top: 5px">
                            <h:panelGrid width="95%" columns="1" cellpadding="5" style="margin-top: 5px">
                                <!-- INICIO 1 MODAL BANNER-->
                                <h:panelGroup styleClass="alinhamentoLabels">
                                    <h:outputText styleClass="texto-size-14 cinza" value="Banner da modalidade 1: "> </h:outputText>
                                    <i class="fa-icon-question-sign tooltipster" style="color: #777"
                                       title="Escolha uma imagem que ilustre a modalidade que será adicionada "></i>
                                </h:panelGroup>

                                <h:panelGroup id="painelfotosModaBanner" layout="block" style="margin: 20px 10px;">
                                    <c:if test="${not empty GestaoVendasOnlineControle.modalidadeCarrouselVendasOnlineVO.nomeArquivo}">
                                        <h:outputText value="#{GestaoVendasOnlineControle.modalidadeCarrouselVendasOnlineVO.nomeArquivo}"/>
                                    </c:if>
                                    <c:if test="${not empty GestaoVendasOnlineControle.modalidadeCarrouselVendasOnlineVO.urlFoto}">
                                        <h:graphicImage url="#{GestaoVendasOnlineControle.modalidadeCarrouselVendasOnlineVO.urlFoto}"
                                                        style="padding-left: 6px; width:100px; height:100px;"/>
                                    </c:if>
                                    <span style="text-align: center;margin-top: 15px">
                                            <a4j:commandLink style="font-size: 20px;" action="#{GestaoVendasOnlineControle.removerImagemModaCarrousel}"
                                                             reRender="divAbaModaBanner">
                                                <i class="fa-icon-trash"></i>
                                            </a4j:commandLink>
                                    </span>

                                    <rich:fileUpload listHeight="0" listWidth="129" noDuplicate="false"
                                                     fileUploadListener="#{GestaoVendasOnlineControle.uploadImagemModalidadeCarrousel}" maxFilesQuantity="1"
                                                     addControlLabel="Procurar Arquivo" cancelEntryControlLabel="Cancelar" doneLabel="Pronto"
                                                     sizeErrorLabel="Arquivo não Enviado. Tamanho Máximo Permitido 1 MB"
                                                     progressLabel="Enviando" stopControlLabel="Parar" uploadControlLabel="Enviar"
                                                     transferErrorLabel="Falha de Transmissão" stopEntryControlLabel="Parar"
                                                     id="uploadImagemModalidadeBanner" immediateUpload="true" autoclear="true"
                                                     acceptedTypes="png,jpg,jpeg,PNG,JPG,JPEG">

                                        <a4j:support event="onuploadcomplete" oncomplete="#{GestaoVendasOnlineControle.mensagemNotificar}" reRender="panelMensagem, divAbaModaBanner"/>
                                    </rich:fileUpload>
                                    <h:outputText styleClass="texto-size-14 cinza"
                                                  value="Tamanho obrigatório: 960 x 600 px - Formato recomendado: PNG/SVG com fundo transparente - Peso máximo: 1MB."> </h:outputText>
                                </h:panelGroup>

                                <h:panelGroup styleClass="alinhamentoLabels">
                                    <h:outputText styleClass="texto-size-14 cinza"
                                                  value="Titulo da Modalidade 1: "> </h:outputText>
                                    <i class="fa-icon-question-sign tooltipster"
                                       style="color: #777"
                                       title="Adicione aqui o nome da modalidade de acordo como ela deve ser exibida no site. "></i>
                                </h:panelGroup>

                                <h:panelGroup layout="block" style="font-size: 14px">
                                    <h:inputText
                                            value="#{GestaoVendasOnlineControle.modalidadeCarrouselVendasOnlineVO.tituloModalidadeVendas}"
                                            onblur="blurinput(this);" style="width: 98%"
                                            onfocus="focusinput(this);"
                                            maxlength="100"
                                            styleClass="testTituloModalidadebanner01 form inputTextVendas"/>
                                </h:panelGroup>

                                <h:panelGroup styleClass="alinhamentoLabels">
                                    <h:outputText styleClass="texto-size-14 cinza"
                                                  value="Descrição da Modalidade 1: "> </h:outputText>
                                    <i class="fa-icon-question-sign tooltipster"
                                       style="color: #777"
                                       title="Adicione aqui um pequeno resumo da modalidade que você está cadatrando. "></i>
                                </h:panelGroup>

                                <h:panelGroup layout="block" style="font-size: 14px">
                                    <h:inputTextarea
                                            value="#{GestaoVendasOnlineControle.modalidadeCarrouselVendasOnlineVO.descricaoModalidadeVendas}"
                                            onblur="blurinput(this); verificarTamanhoMaximo(this,270);" onkeypress="verificarTamanhoMaximo(this,270);"
                                            onkeydown="verificarTamanhoMaximo(this,270);" onkeyup="verificarTamanhoMaximo(this,270);"
                                            style="width: 98%;height: 100px;" onfocus="focusinput(this);" styleClass=" testModalidadeDescricaoBanner01  form inputTextVendas"/>
                                </h:panelGroup>
                                <!-- FIM BANNER 1 -->
                            </h:panelGrid>
                            <h:panelGrid width="98%" columns="1" columnClasses="w100" cellpadding="5" style="margin-top: 5px">
                                <!-- INICIO 2 MODAL BANNER-->
                                <h:panelGroup styleClass="alinhamentoLabels">
                                    <h:outputText styleClass="texto-size-14 cinza" value="Banner da modalidade 2: "> </h:outputText>
                                    <i class="fa-icon-question-sign tooltipster" style="color: #777"
                                       title="Escolha uma imagem que ilustre a modalidade que será adicionada "></i>
                                </h:panelGroup>

                                <h:panelGroup id="painelfotosModaBanner2" layout="block" style="margin: 20px 10px;">
                                    <c:if test="${not empty GestaoVendasOnlineControle.modalidadeCarrouselVendasOnlineVO2.nomeArquivo}">
                                        <h:outputText value="#{GestaoVendasOnlineControle.modalidadeCarrouselVendasOnlineVO2.nomeArquivo}"/>
                                    </c:if>
                                    <c:if test="${not empty GestaoVendasOnlineControle.modalidadeCarrouselVendasOnlineVO2.urlFoto}">
                                        <h:graphicImage url="#{GestaoVendasOnlineControle.modalidadeCarrouselVendasOnlineVO2.urlFoto}"
                                                        style="padding-left: 6px; width:100px; height:100px;"/>
                                    </c:if>
                                    <span style="text-align: center;margin-top: 15px">
                                            <a4j:commandLink style="font-size: 20px;" action="#{GestaoVendasOnlineControle.removerImagemModaCarrousel2}"
                                                             reRender="divAbaModaBanner">
                                                <i class="fa-icon-trash"></i>
                                            </a4j:commandLink>
                                    </span>

                                    <rich:fileUpload listHeight="0" listWidth="129" noDuplicate="false"
                                                     fileUploadListener="#{GestaoVendasOnlineControle.uploadImagemModalidadeCarrousel2}" maxFilesQuantity="1"
                                                     addControlLabel="Procurar Arquivo" cancelEntryControlLabel="Cancelar" doneLabel="Pronto"
                                                     sizeErrorLabel="Arquivo não Enviado. Tamanho Máximo Permitido 1 MB"
                                                     progressLabel="Enviando" stopControlLabel="Parar" uploadControlLabel="Enviar"
                                                     transferErrorLabel="Falha de Transmissão" stopEntryControlLabel="Parar"
                                                     id="uploadImagemModalidadeBanner2" immediateUpload="true" autoclear="true"
                                                     acceptedTypes="png,jpg,jpeg,PNG,JPG,JPEG">

                                        <a4j:support event="onuploadcomplete" oncomplete="#{GestaoVendasOnlineControle.mensagemNotificar}" reRender="panelMensagem, divAbaModaBanner"/>
                                    </rich:fileUpload>
                                    <h:outputText styleClass="texto-size-14 cinza"
                                                  value="Tamanho obrigatório: 960 x 600 px - Formato recomendado: PNG/SVG com fundo transparente - Peso máximo: 1MB."> </h:outputText>
                                </h:panelGroup>

                                <h:panelGroup styleClass="alinhamentoLabels">
                                    <h:outputText styleClass="texto-size-14 cinza"
                                                  value="Titulo da Modalidade 2: "> </h:outputText>
                                    <i class="fa-icon-question-sign tooltipster"
                                       style="color: #777"
                                       title="Adicione aqui o nome da modalidade de acordo como ela deve ser exibida no site. "></i>
                                </h:panelGroup>

                                <h:panelGroup layout="block" style="font-size: 14px">
                                    <h:inputText
                                            value="#{GestaoVendasOnlineControle.modalidadeCarrouselVendasOnlineVO2.tituloModalidadeVendas}"
                                            onblur="blurinput(this);" style="width: 98%"
                                            onfocus="focusinput(this);"
                                            size="26" maxlength="26"
                                            styleClass="testModalidadeBanner02 form inputTextVendas"/>
                                </h:panelGroup>

                                <h:panelGroup styleClass="alinhamentoLabels">
                                    <h:outputText styleClass="texto-size-14 cinza"
                                                  value="Descrição da Modalidade 2: "> </h:outputText>
                                    <i class="fa-icon-question-sign tooltipster"
                                       style="color: #777"
                                       title="Adicione aqui um pequeno resumo da modalidade que você está cadatrando. "></i>
                                </h:panelGroup>

                                <h:panelGroup layout="block" style="font-size: 14px">
                                    <h:inputTextarea
                                            value="#{GestaoVendasOnlineControle.modalidadeCarrouselVendasOnlineVO2.descricaoModalidadeVendas}"
                                            onblur="blurinput(this);verificarTamanhoMaximo(this,270);" onkeypress="verificarTamanhoMaximo(this,270);"
                                            onkeydown="verificarTamanhoMaximo(this,270);" onkeyup="verificarTamanhoMaximo(this,270);" style="width: 98%;height: 100px;"
                                            onfocus="focusinput(this);" rows="5" styleClass="testDescricaoModalidadeBanner02 form inputTextVendas">

                                    </h:inputTextarea>
                                </h:panelGroup>
                                <!-- FIM BANNER 2 -->
                            </h:panelGrid>
                            <h:panelGrid width="98%" columns="1" columnClasses="w100" cellpadding="5" style="margin-top: 5px">
                                <!-- INICIO 3 MODAL BANNER-->
                                <h:panelGroup styleClass="alinhamentoLabels">
                                    <h:outputText styleClass="texto-size-14 cinza" value="Banner da modalidade 3: "> </h:outputText>
                                    <i class="fa-icon-question-sign tooltipster" style="color: #777"
                                       title="Escolha uma imagem que ilustre a modalidade que será adicionada "></i>
                                </h:panelGroup>

                                <h:panelGroup id="painelfotosModaBanner3" layout="block" style="margin: 20px 10px;">
                                    <c:if test="${not empty GestaoVendasOnlineControle.modalidadeCarrouselVendasOnlineVO3.nomeArquivo}">
                                        <h:outputText value="#{GestaoVendasOnlineControle.modalidadeCarrouselVendasOnlineVO3.nomeArquivo}"/>
                                    </c:if>
                                    <c:if test="${not empty GestaoVendasOnlineControle.modalidadeCarrouselVendasOnlineVO3.urlFoto}">
                                        <h:graphicImage url="#{GestaoVendasOnlineControle.modalidadeCarrouselVendasOnlineVO3.urlFoto}"
                                                        style="padding-left: 6px; width:100px; height:100px;"/>
                                    </c:if>
                                    <span style="text-align: center;margin-top: 15px">
                                            <a4j:commandLink style="font-size: 20px;" action="#{GestaoVendasOnlineControle.removerImagemModaCarrousel3}"
                                                             reRender="divAbaModaBanner">
                                                <i class="fa-icon-trash"></i>
                                            </a4j:commandLink>
                                    </span>

                                    <rich:fileUpload listHeight="0" listWidth="129" noDuplicate="false"
                                                     fileUploadListener="#{GestaoVendasOnlineControle.uploadImagemModalidadeCarrousel3}" maxFilesQuantity="1"
                                                     addControlLabel="Procurar Arquivo" cancelEntryControlLabel="Cancelar" doneLabel="Pronto"
                                                     sizeErrorLabel="Arquivo não Enviado. Tamanho Máximo Permitido 1 MB"
                                                     progressLabel="Enviando" stopControlLabel="Parar" uploadControlLabel="Enviar"
                                                     transferErrorLabel="Falha de Transmissão" stopEntryControlLabel="Parar"
                                                     id="uploadImagemModalidadeBanner3" immediateUpload="true" autoclear="true"
                                                     acceptedTypes="png,jpg,jpeg,PNG,JPG,JPEG">

                                        <a4j:support event="onuploadcomplete" oncomplete="#{GestaoVendasOnlineControle.mensagemNotificar}" reRender="panelMensagem, divAbaModaBanner"/>
                                    </rich:fileUpload>
                                    <h:outputText styleClass="texto-size-14 cinza"
                                                  value="Tamanho obrigatório: 960 x 600 px - Formato recomendado: PNG/SVG com fundo transparente - Peso máximo: 1MB."> </h:outputText>
                                </h:panelGroup>

                                <h:panelGroup styleClass="alinhamentoLabels">
                                    <h:outputText styleClass="texto-size-14 cinza"
                                                  value="Título da Modalidade 3: "> </h:outputText>
                                    <i class="fa-icon-question-sign tooltipster"
                                       style="color: #777"
                                       title="Adicione aqui o nome da modalidade de acordo como ela deve ser exibida no site. "></i>
                                </h:panelGroup>

                                <h:panelGroup layout="block" style="font-size: 14px">
                                    <h:inputText
                                            value="#{GestaoVendasOnlineControle.modalidadeCarrouselVendasOnlineVO3.tituloModalidadeVendas}"
                                            onblur="blurinput(this);" style="width: 98%"
                                            onfocus="focusinput(this);"
                                            size="26" maxlength="26"
                                            styleClass="testModalidadeBanner03 form inputTextVendas"/>
                                </h:panelGroup>

                                <h:panelGroup styleClass="alinhamentoLabels">
                                    <h:outputText styleClass="texto-size-14 cinza"
                                                  value="Descrição da Modalidade 3: "> </h:outputText>
                                    <i class="fa-icon-question-sign tooltipster"
                                       style="color: #777"
                                       title="Adicione aqui um pequeno resumo da modalidade que você está cadatrando. "></i>
                                </h:panelGroup>

                                <h:panelGroup layout="block" style="font-size: 14px">
                                    <h:inputTextarea
                                            value="#{GestaoVendasOnlineControle.modalidadeCarrouselVendasOnlineVO3.descricaoModalidadeVendas}"
                                            onblur="blurinput(this);verificarTamanhoMaximo(this,270);" style="width: 98%;height: 100px;"
                                            onfocus="focusinput(this);" onkeypress="verificarTamanhoMaximo(this,270);"
                                            onkeydown="verificarTamanhoMaximo(this,270);" rows="3"
                                            styleClass="testDescricaoModalidadeBanner03 form inputTextVendas">
                                    </h:inputTextarea>
                                </h:panelGroup>
                                <!-- FIM BANNER 3 -->
                            </h:panelGrid>
                        </h:panelGrid>
                    </h:panelGroup>

                    <h:panelGroup layout="block" id="divAbaPlanoSite" style="width: 50%;"
                                  rendered="#{GestaoVendasOnlineControle.abaPlanos}">

                        <h:panelGrid width="100%" columns="2"
                                     columnClasses="w40, w60" cellpadding="10"
                                     style="margin-top: 20px">

                            <h:panelGroup styleClass="alinhamentoLabels">
                                <h:outputText styleClass="texto-size-14 cinza" value="Plano:"/>
                                <i class="fa-icon-question-sign tooltipster"
                                   style="color: #777"
                                   title="Selecione o plano. "></i>
                            </h:panelGroup>

                            <h:panelGroup layout="block" style="font-size: 14px" styleClass="cb-container">
                                <h:selectOneMenu styleClass="testDropPlano  form"
                                                 value="#{GestaoVendasOnlineControle.planosSiteVendasOnlineVO.codPlano}">
                                    <f:selectItems value="#{GestaoVendasOnlineControle.planosSite}" />

                                    <a4j:support event="onchange"
                                                 actionListener="#{GestaoVendasOnlineControle.verificarPlanoSelecionado}"
                                                 reRender="form:divAbaPlanoSite"/>
                                </h:selectOneMenu>
                            </h:panelGroup>

                            <h:panelGroup styleClass="alinhamentoLabels">
                                <h:outputText styleClass="texto-size-14 cinza"
                                              value="Destaque no cabeçalho: "> </h:outputText>
                                <i class="fa-icon-question-sign tooltipster"
                                   style="color: #777"
                                   title="Adicione aqui um atributo comercial que diferencie esse plano. Por exemplo: Melhor custo benefício / Melhor preço / Melhor horário. "></i>
                            </h:panelGroup>

                            <h:panelGroup layout="block" style="font-size: 14px">
                                <h:inputText title="Max. 26 caracteres."
                                             value="#{GestaoVendasOnlineControle.planosSiteVendasOnlineVO.destaqueCabecalho}"
                                             onblur="blurinput(this);" style="width: 90%"
                                             onfocus="focusinput(this);"
                                             size="26" maxlength="22"
                                             styleClass="testDescricaoCabecalho form inputTextVendas"/>
                            </h:panelGroup>

                            <h:panelGroup styleClass="alinhamentoLabels">
                                <h:outputText styleClass="texto-size-14 cinza"
                                              value="Descrição do plano: "> </h:outputText>
                                <i class="fa-icon-question-sign tooltipster"
                                   style="color: #777"
                                   title="Adicione aqui informações importantes para a venda deste plano. Por exemplo: Treine quando quiser em sua unidade, sem taxa de cancelamento. "></i>
                            </h:panelGroup>

                            <h:panelGroup layout="block" style="font-size: 14px">
                                <h:inputTextarea title="Max. 255 caracteres."
                                                 value="#{GestaoVendasOnlineControle.planosSiteVendasOnlineVO.descricaoPlano}"
                                                 onblur="blurinput(this);"
                                                 style="height: 38px; width: 90%; max-width: 263px;"
                                                 onfocus="focusinput(this);"
                                                 onkeypress="limitarCaracteres(this,255);"
                                                 onchange="limitarCaracteres(this,255);"
                                                 onclick="limitarCaracteres(this,255);"
                                                 styleClass="form tooltipster inputTextVendas"/>
                            </h:panelGroup>

                            <h:panelGroup styleClass="alinhamentoLabels">
                                <h:outputText styleClass="texto-size-14 cinza"
                                              value="Benefícios:"> </h:outputText>
                                <i class="fa-icon-question-sign tooltipster"
                                   style="color: #777"
                                   title="Adicione aqui os benefícios importantes para a sua estratégia comercial. "></i>
                            </h:panelGroup>

                            <h:panelGroup>
                                <h:inputTextarea id="inputBeneficio"
                                                 title="Max. 255 caracteres."
                                                 value="#{GestaoVendasOnlineControle.beneficioParaAdicionar}"
                                                 onblur="blurinput(this);"
                                                 style="height: 38px; width: 90%; max-width: 263px;"
                                                 onfocus="focusinput(this);"
                                                 onkeypress="limitarCaracteres(this,255);"
                                                 onchange="limitarCaracteres(this,255);"
                                                 onclick="limitarCaracteres(this,255);"
                                                 styleClass="form tooltipster inputTextVendas"/>

                                <a4j:commandLink id="btnAdicionarBeneficioNaLista"
                                                 action="#{GestaoVendasOnlineControle.adicionarBeneficioNaLista}"
                                                 oncomplete="#{GestaoVendasOnlineControle.mensagemNotificar}"
                                                 reRender="tabelaBeneficiosDoPlano, inputBeneficio, form:sctablebeneficios"
                                                 title="Adicionar Benefício na Lista"
                                                 style="font-size: 18px; margin: 0 0 0 10px;"
                                                 styleClass="fa-icon-plus tooltipster"/>
                            </h:panelGroup>

                            <h:panelGroup></h:panelGroup>

                            <h:panelGroup>
                                <h:dataTable styleClass="tabelaSimplesCustom"
                                             id="tabelaBeneficiosDoPlano"
                                             width="500px"
                                             headerClass="consulta"
                                             rowClasses="linhaImpar, linhaPar"
                                             columnClasses="colunaAlinhamento"
                                             value="#{GestaoVendasOnlineControle.lstBeneficiosDoPlano}"
                                             rows="5" var="beneficio" >

                                    <h:column>
                                        <f:facet name="header">
                                            <h:outputText value="Descrição Benefício"/>
                                        </f:facet>
                                        <h:outputText value="#{beneficio}"/>
                                    </h:column>

                                    <h:column>
                                        <f:facet name="header">
                                            <h:outputText value="Ações" styleClass="acoes"/>
                                        </f:facet>

                                        <a4j:commandLink style="font-size: 20px;" styleClass="tooltipster"
                                                         action="#{GestaoVendasOnlineControle.removerBeneficioDaLista(beneficio)}"
                                                         oncomplete="#{GestaoVendasOnlineControle.mensagemNotificar}"
                                                         reRender="divAbaPlanoSite"
                                                         title="Remover Benefício">
                                            <i class="fa-icon-remove"></i>
                                        </a4j:commandLink>
                                    </h:column>
                                </h:dataTable>

                                <rich:datascroller align="center" for="form:tabelaBeneficiosDoPlano" maxPages="10" id="sctablebeneficios"/>
                            </h:panelGroup>

                            <%-- Hibrael 20/01/2025
                                Comentado para implementar nova abordagem possibilitando informar múltiplos planos

                            <h:panelGroup styleClass="alinhamentoLabels">
                                <h:outputText styleClass="texto-size-14 cinza"
                                              value="Benefício 01: "> </h:outputText>
                                <i class="fa-icon-question-sign tooltipster"
                                   style="color: #777"
                                   title="Adicione aqui o benefício importante para a sua estratégia comercial. "></i>
                            </h:panelGroup>

                            <h:panelGroup layout="block" style="font-size: 14px">
                                <h:inputTextarea title="Max. 255 caracteres."
                                             value="#{GestaoVendasOnlineControle.planosSiteVendasOnlineVO.beneficio01}"
                                             onblur="blurinput(this);"
                                             style="height: 38px; width: 90%; max-width: 263px;"
                                             onfocus="focusinput(this);"
                                             onkeypress="limitarCaracteres(this,255);"
                                             onchange="limitarCaracteres(this,255);"
                                             onclick="limitarCaracteres(this,255);"
                                             styleClass="form tooltipster inputTextVendas"/>
                            </h:panelGroup>

                            <h:panelGroup styleClass="alinhamentoLabels">
                                <h:outputText styleClass="texto-size-14 cinza"
                                              value="Benefício 02: "> </h:outputText>
                                <i class="fa-icon-question-sign tooltipster"
                                   style="color: #777"
                                   title="Adicione aqui o benefício importante para a sua estratégia comercial. "></i>
                            </h:panelGroup>

                            <h:panelGroup layout="block" style="font-size: 14px">
                                <h:inputTextarea title="Max. 255 caracteres."
                                                 value="#{GestaoVendasOnlineControle.planosSiteVendasOnlineVO.beneficio02}"
                                                 onblur="blurinput(this);"
                                                 style="height: 38px; width: 90%; max-width: 263px;"
                                                 onfocus="focusinput(this);"
                                                 onkeypress="limitarCaracteres(this,255);"
                                                 onchange="limitarCaracteres(this,255);"
                                                 onclick="limitarCaracteres(this,255);"
                                                 styleClass="form tooltipster inputTextVendas"/>
                            </h:panelGroup>

                            <h:panelGroup styleClass="alinhamentoLabels">
                                <h:outputText styleClass="texto-size-14 cinza"
                                              value="Benefício 03: "> </h:outputText>
                                <i class="fa-icon-question-sign tooltipster"
                                   style="color: #777"
                                   title="Adicione aqui o benefício importante para a sua estratégia comercial. "></i>
                            </h:panelGroup>

                            <h:panelGroup layout="block" style="font-size: 14px">
                                <h:inputTextarea title="Max. 255 caracteres."
                                                 value="#{GestaoVendasOnlineControle.planosSiteVendasOnlineVO.beneficio03}"
                                                 onblur="blurinput(this);"
                                                 style="height: 38px; width: 90%; max-width: 263px;"
                                                 onfocus="focusinput(this);"
                                                 onkeypress="limitarCaracteres(this,255);"
                                                 onchange="limitarCaracteres(this,255);"
                                                 onclick="limitarCaracteres(this,255);"
                                                 styleClass="form tooltipster inputTextVendas"/>
                            </h:panelGroup>

                            --%>

                            <h:panelGroup styleClass="alinhamentoLabels">
                                <h:outputText styleClass="texto-size-14 cinza"
                                              value="Preço original: "> </h:outputText>
                                <i class="fa-icon-question-sign tooltipster"
                                   style="color: #777"
                                   title="Adicione aqui o preço original, como se você fosse vender sem descontos. "></i>
                            </h:panelGroup>

                            <h:panelGroup layout="block" style="font-size: 14px">
                                <h:inputText
                                        value="#{GestaoVendasOnlineControle.planosSiteVendasOnlineVO.precoOriginal}"
                                        onkeypress="return formatar_moeda(this,'.',',',event);"
                                        onfocus="focusinput(this);"
                                        size="10" maxlength="5"
                                        styleClass="testPrecoOriginal form inputTextVendas">
                                    <f:converter converterId="FormatadorNumerico"/>
                                </h:inputText>
                            </h:panelGroup>

                            <h:panelGroup styleClass="alinhamentoLabels" rendered="#{GestaoVendasOnlineControle.informarValorDesejadoMensal}">
                                <h:outputText styleClass="texto-size-14 cinza"
                                              value="Valor Desejado Mensal: "> </h:outputText>
                                <i class="fa-icon-question-sign tooltipster"
                                   style="color: #777"
                                   title="Para planos do tipo crédito, é necessário informar este campo, para que o valor do plano seja exibido no hotsite."></i>
                            </h:panelGroup>

                            <h:panelGroup layout="block" style="font-size: 14px">
                                <h:inputText
                                        rendered="#{GestaoVendasOnlineControle.informarValorDesejadoMensal}"
                                        value="#{GestaoVendasOnlineControle.valorDesejadoMensal}"
                                        onkeypress="return formatar_moeda(this,'.',',',event);"
                                        onfocus="focusinput(this);"
                                        size="10" maxlength="5"
                                        styleClass="testValorDesejadoMensal form inputTextVendas">
                                    <f:converter converterId="FormatadorNumerico"/>
                                </h:inputText>
                            </h:panelGroup>
                        </h:panelGrid>
                    </h:panelGroup>

                    <h:panelGroup layout="block" id="divAbaCapLeads" style="width: 50%;"
                                  rendered="#{GestaoVendasOnlineControle.abaCapLeads}">

                        <h:panelGrid width="100%" columns="2"
                                     columnClasses="w40, w60" cellpadding="10"
                                     style="margin-top: 20px">

                            <h:panelGroup styleClass="alinhamentoLabels">
                                <h:outputText styleClass="texto-size-14 cinza"
                                              value="Titulo do Cabeçalho: "> </h:outputText>
                                <i class="fa-icon-question-sign tooltipster"
                                   style="color: #777"
                                   title="Adicione aqui o texto que será utilizado no seu formulário para captação de contatos/leads. "></i>
                            </h:panelGroup>

                            <h:panelGroup layout="block" style="font-size: 14px">
                                <h:inputText title="Max. 26 caracteres"
                                             value="#{GestaoVendasOnlineControle.captacaoLeadsVendasOnline.tituloCabecalho}"
                                             onblur="blurinput(this);" style="width: 90%"
                                             onfocus="focusinput(this);"
                                             size="26" maxlength="26"
                                             styleClass="testCabecalhoLead form inputTextVendas"/>
                            </h:panelGroup>

                            <h:panelGroup styleClass="alinhamentoLabels">
                                <h:outputText styleClass="texto-size-14 cinza"
                                              value="Adicione uma imagem de fundo para o banner: "> </h:outputText>
                                <i class="fa-icon-question-sign tooltipster"
                                   style="color: #777"
                                   title="Lembre-se que o texto principal ficará em cima da imagem, por isso busque por ângulos que não cortem o motivo principal. "></i>
                            </h:panelGroup>

                            <h:panelGroup id="painelfotosCapLeads" layout="block" style="margin: 20px 10px;">
                                <h:graphicImage url="#{GestaoVendasOnlineControle.captacaoLeadsVendasOnline.urlFoto}" style="padding-left: 6px; width:100px; height:100px;"/>
                                <span style="text-align: center;margin-top: 15px">
                                                    <a4j:commandLink style="font-size: 20px;" action="#{GestaoVendasOnlineControle.removerImagemCapLeads}" reRender="divAbaCapLeads">
                                                        <i class="fa-icon-trash"></i>
                                                    </a4j:commandLink>
                                            </span>

                                <rich:fileUpload listHeight="0" listWidth="129" noDuplicate="false"
                                                 fileUploadListener="#{GestaoVendasOnlineControle.uploadImagemCaptacaoLeads}" maxFilesQuantity="1"
                                                 addControlLabel="Procurar Arquivo" cancelEntryControlLabel="Cancelar" doneLabel="Pronto"
                                                 sizeErrorLabel="Arquivo não Enviado. Tamanho Máximo Permitido 1 MB"
                                                 progressLabel="Enviando" stopControlLabel="Parar" uploadControlLabel="Enviar"
                                                 transferErrorLabel="Falha de Transmissão" stopEntryControlLabel="Parar"
                                                 id="uploadImagemCapLeads" immediateUpload="true" autoclear="true"
                                                 acceptedTypes="png,jpg,jpeg,PNG,JPG,JPEG">

                                    <a4j:support event="onuploadcomplete" oncomplete="#{GestaoVendasOnlineControle.mensagemNotificar}" reRender="panelMensagem, divAbaCapLeads"/>
                                </rich:fileUpload>
                                <h:outputText styleClass="texto-size-14 cinza"
                                              value="Tamanho obrigatório: 1920 x 1080 px - Formato recomendado: PNG com fundo transparente - Peso máximo: 1MB."> </h:outputText>

                            </h:panelGroup>
                        </h:panelGrid>

                        <h:panelGrid width="100%" columns="1"
                                     columnClasses="w100" cellpadding="10"
                                     style="margin-top: 20px">

                            <h:outputText styleClass="texto-size-14"
                                          value="Importante: Necessário ir nas Configurações Gerais > Integração > CRM > Leads (Genérica) e informar qual o Responsável Padrão por esses Leads.
                                          Se no futuro essa pessoa for Inativada, precisa configurar um novo.">
                            </h:outputText>

                        </h:panelGrid>

                    </h:panelGroup>

                    <h:panelGroup layout="block" id="divAbaAgenda" style="width: 100%;"
                                  rendered="#{GestaoVendasOnlineControle.abaAgenda}">

                        <h:panelGrid width="50%" columns="1"
                                     columnClasses="w50, w50" cellpadding="10"
                                     style="margin-top: 20px">


                            <h:panelGroup layout="block" rendered="#{!GestaoVendasOnlineControle.agendaVendasOnlineVO.estaEditando}">
                                <h:column>
                                    <div>
                                        <h:outputText styleClass="texto-size-14 cinza"
                                                      value="Aula: "> </h:outputText>
                                    </div>

                                    <h:selectOneMenu
                                            value="#{GestaoVendasOnlineControle.agendaVendasOnlineVO.aulaEscolhida}"
                                            styleClass="testDropAulaAgenda form inputTextVendas"
                                            style="width: 90%"
                                            valueChangeListener="#{GestaoVendasOnlineControle.carregarProdutosDeVendasOnline}"
                                            id="select-aulas"
                                    >
                                        <f:selectItem itemLabel="Selecione uma aula!" />
                                        <f:selectItems  value="#{GestaoVendasOnlineControle.agendaVendasOnlineVO.aulas}" />
                                        <a4j:support event="onchange" reRender="panelAgendaProdutos" />
                                    </h:selectOneMenu>
                                </h:column>
                            </h:panelGroup>

                            <h:panelGroup styleClass="alinhamentoLabels" style="clear: both;" rendered="#{!GestaoVendasOnlineControle.agendaVendasOnlineVO.estaEditando}">
                                <h:column>
                                    <h:outputText styleClass="texto-size-14 cinza">Escolha como você quer criar um produto para a aula:</h:outputText>
                                    <h:selectOneRadio styleClass="texto-size-14 cinza agendaEscolhaProduto"
                                                      value="#{GestaoVendasOnlineControle.agendaVendasOnlineVO.tipoProduto}"
                                                      valueChangeListener="#{GestaoVendasOnlineControle.agendaVendasOnlineVO.trocarCategoria}"
                                                      id="select-escolha-tipo-produto"
                                    >
                                        <f:selectItem itemValue="0" itemLabel="Criar um novo produto" />
                                        <f:selectItem itemValue="1" itemLabel="Escolher um produto existente"/>
                                        <a4j:support event="onchange" reRender="panelAgendaProdutos" />
                                    </h:selectOneRadio>
                                </h:column>
                            </h:panelGroup>

                            <h:panelGroup styleClass="alinhamentoLabels" style="clear: both;" id="panelAgendaProdutos">
                                <h:column rendered="#{GestaoVendasOnlineControle.agendaVendasOnlineVO.tipoProduto == 0
                                                || GestaoVendasOnlineControle.agendaVendasOnlineVO.estaEditando}">
                                    <h:outputText styleClass="texto-size-14 cinza"
                                                  value="Descrição do Produto: "> </h:outputText>

                                    <h:inputText title="Max. 50 caracteres"
                                                 value="#{GestaoVendasOnlineControle.agendaVendasOnlineVO.descricaoProduto}"
                                                 onblur="blurinput(this);" style="width: 90%"
                                                 onfocus="focusinput(this);"
                                                 size="50" maxlength="50"
                                                 styleClass="testDescricaoProduto form inputTextVendas"
                                                 id="input-produto-descricao" />

                                    <h:panelGroup styleClass="alinhamentoLabels">
                                        <h:column>
                                            <h:outputText styleClass="texto-size-14 cinza"
                                                          value="Valor do Produto: "> </h:outputText>

                                            <h:inputText title="Max. 50 caracteres"
                                                         value="#{GestaoVendasOnlineControle.agendaVendasOnlineVO.valorProduto}"
                                                         style="width: 90%"
                                                         size="50" maxlength="50"
                                                         id="inputValorProduto"
                                                         onfocus="adicionarMascaraMonetaria('form:inputValorProduto');"
                                                         styleClass="testValorProduto form inputTextVendas"/>
                                        </h:column>
                                    </h:panelGroup>
                                </h:column>
                                <h:column rendered="#{GestaoVendasOnlineControle.agendaVendasOnlineVO.tipoProduto == 1
                                                        && !GestaoVendasOnlineControle.agendaVendasOnlineVO.estaEditando}">

                                    <h:outputText styleClass="texto-size-14 cinza"
                                                  value="Escolha uma diária: "> </h:outputText>

                                    <h:selectOneMenu
                                            value="#{GestaoVendasOnlineControle.agendaVendasOnlineVO.produtoEscolhido}"
                                            styleClass="testDropAulaAgenda form inputTextVendas"
                                            style="width: 90%"
                                            id="select-produto-escolhido"

                                    >
                                        <f:selectItem itemLabel="Selecione uma diária!" />
                                        <f:selectItems  value="#{GestaoVendasOnlineControle.agendaVendasOnlineVO.produtosExistentes}" />
                                    </h:selectOneMenu>
                                </h:column>
                            </h:panelGroup>

                            <a4j:commandLink id="btnIncluirListaAulasVendasOnline"
                                             action="#{GestaoVendasOnlineControle.incluirItemListaAulasVendasOnline()}"
                                             onclick="addCores();"
                                             rendered="#{GestaoVendasOnlineControle.abaPublicar eq false}"
                                             reRender="vendasOnlinePanel"
                                             oncomplete="#{GestaoVendasOnlineControle.mensagemNotificar}"
                                             value="Incluir lista"
                                             styleClass="testAplicarAlteracao botaoPrimario texto-size-14-real"/>

                            <h:panelGroup>
                                <h:dataTable styleClass="tableAgendaItens"
                                             id="agendaVendasOnlineitems" width="100%" headerClass="consulta" rowClasses="linhaImpar, linhaPar" columnClasses="colunaAlinhamento"
                                             value="#{GestaoVendasOnlineControle.agendaVendasOnlineVO.aulasVendasOnline}"  rows="10" var="aulaVendasOnline">

                                    <h:column>
                                        <f:facet name="header">
                                            <h:outputText value="Descrição"/>
                                        </f:facet>
                                        <h:outputText id="tableDescProduto" value="#{aulaVendasOnline.descricaoProduto}"/>
                                    </h:column>
                                    <h:column>
                                        <f:facet name="header">
                                            <h:outputText value="Valor"/>
                                        </f:facet>
                                        <h:outputText id="tableValorProduto" value="#{aulaVendasOnline.valor}"/>
                                    </h:column>
                                    <h:column>
                                        <f:facet name="header">
                                            <h:outputText value="Turma"/>
                                        </f:facet>
                                        <h:outputText id="tableTurma" value="#{aulaVendasOnline.turma}"/>
                                    </h:column>
                                    <h:column>
                                        <f:facet name="header">
                                            <h:outputText value="Modalidade"/>
                                        </f:facet>
                                        <h:outputText id="tableModalidade" value="#{aulaVendasOnline.modalidade}"/>
                                    </h:column>

                                    <h:column>
                                        <f:facet name="header">
                                            <h:outputText value="#{msg_bt.btn_opcoes}" styleClass="acoes"/>
                                        </f:facet>
                                        <a4j:commandLink rendered="#{aulaVendasOnline.ativo}"
                                                         id="comandoDesativar"
                                                         reRender="agendaVendasOnlineitems"
                                                         title="Pausar Aula"
                                                         action="#{GestaoVendasOnlineControle.alterarStatusProduto}" styleClass="testAlterarStatusAula botoes">
                                            <i class="fa-icon-pause"></i>
                                        </a4j:commandLink>
                                        <a4j:commandLink rendered="#{!aulaVendasOnline.ativo}"
                                                         id="comandoAtivar"
                                                         reRender="agendaVendasOnlineitems"
                                                         title="Ativar Aula"
                                                         action="#{GestaoVendasOnlineControle.alterarStatusProduto}" styleClass="testAtivarAula botoes">
                                            <i class="fa-icon-play"></i>
                                        </a4j:commandLink>
                                        <a4j:commandLink id="comandoAtualizar"
                                                         reRender="vendasOnlinePanel"
                                                         title="Editar Aula"
                                                         action="#{GestaoVendasOnlineControle.editarProduto}" styleClass="testEditarAula botoes">
                                            <i class="fa-icon-edit"></i>
                                        </a4j:commandLink>
                                        <a4j:commandLink id="comandoExcluir"
                                                         reRender="vendasOnlinePanel"
                                                         title="Excluir Aula"
                                                         action="#{GestaoVendasOnlineControle.excluirProduto}" styleClass="testeExcluirAula botoes">
                                            <i class="fa-icon-trash"></i>
                                        </a4j:commandLink>
                                    </h:column>
                                </h:dataTable>

                                <rich:datascroller align="center" for="form:agendaVendasOnlineitems" maxPages="10" id="scResultadoLog"/>
                            </h:panelGroup>

                            <h:panelGroup styleClass="alinhamentoLabels">
                                <h:outputText styleClass="texto-size-14 cinza"
                                              value="Exibir botão Agende Agora sobre Banner:"
                                              style="color: #777; margin-right: 10px;"
                                              title="Ao marcar essa opção, sobre o banner será exibido o botão Agende Agora.">
                                </h:outputText>
                                <h:selectBooleanCheckbox styleClass="testClaro"
                                                         value="#{GestaoVendasOnlineControle.config.exibirBotaoAgendaSobreBanner}"
                                                         title="Ao marcar essa opção, sobre o banner será exibido o botão Agende Agora.">
                                </h:selectBooleanCheckbox>
                            </h:panelGroup>

                        </h:panelGrid>
                    </h:panelGroup>

                    <h:panelGroup layout="block" id="divAbaContatoRodape" style="width: 50%;"
                                  rendered="#{GestaoVendasOnlineControle.abaContato}">

                        <h:panelGrid width="100%" columns="2"
                                     columnClasses="w40, w60" cellpadding="10"
                                     style="margin-top: 20px">

                            <h:panelGroup styleClass="alinhamentoLabels">
                                <h:outputText styleClass="texto-size-14 cinza"
                                              value="Horário de funcionamento de Segunda a Sexta-feira: "> </h:outputText>
                                <i class="fa-icon-question-sign tooltipster"
                                   style="color: #777"
                                   title="Escreva aqui os horários de funcionamento da sua empresa. Utilize o padrão 24h neste campo. Por exemplo: Segunda a Sexta-feira das 10h às 17h.. "></i>
                            </h:panelGroup>

                            <h:panelGroup layout="block" style="font-size: 14px">
                                <h:inputText title="Max. 50 caracteres"
                                             value="#{GestaoVendasOnlineControle.contatoRodapeVendasOnlineVO.horarioSegundaSexta}"
                                             onblur="blurinput(this);" style="width: 90%"
                                             onfocus="focusinput(this);"
                                             size="50" maxlength="50"
                                             styleClass="testHorarioContato form inputTextVendas"/>
                            </h:panelGroup>

                            <h:panelGroup styleClass="alinhamentoLabels">
                                <h:outputText styleClass="texto-size-14 cinza"
                                              value="Horário de funcionamento Sábado: "> </h:outputText>
                                <i class="fa-icon-question-sign tooltipster"
                                   style="color: #777"
                                   title="Escreva aqui os horários de funcionamento da sua empresa. Utilize o padrão 24h neste campo. Por exemplo: Sábado das 10h às 17h.. "></i>
                            </h:panelGroup>

                            <h:panelGroup layout="block" style="font-size: 14px">
                                <h:inputText title="Max. 50 caracteres"
                                             value="#{GestaoVendasOnlineControle.contatoRodapeVendasOnlineVO.horarioSabado}"
                                             onblur="blurinput(this);" style="width: 90%"
                                             onfocus="focusinput(this);"
                                             size="50" maxlength="50"
                                             styleClass="testHorarioSabado form inputTextVendas"/>
                            </h:panelGroup>

                            <h:panelGroup styleClass="alinhamentoLabels">
                                <h:outputText styleClass="texto-size-14 cinza"
                                              value="Horário de funcionamento Domingo e Feriados: "> </h:outputText>
                                <i class="fa-icon-question-sign tooltipster"
                                   style="color: #777"
                                   title="Escreva aqui os horários de funcionamento da sua empresa. Utilize o padrão 24h neste campo. Por exemplo: Domingo das 10h às 17h.. "></i>
                            </h:panelGroup>

                            <h:panelGroup layout="block" style="font-size: 14px">
                                <h:inputText title="Max. 50 caracteres"
                                             value="#{GestaoVendasOnlineControle.contatoRodapeVendasOnlineVO.horarioDomingoFeriado}"
                                             onblur="blurinput(this);" style="width: 90%"
                                             onfocus="focusinput(this);"
                                             size="50" maxlength="50"
                                             styleClass="testHorarioFeriado form inputTextVendas"/>
                            </h:panelGroup>

                            <h:panelGroup styleClass="alinhamentoLabels">
                                <h:outputText styleClass="texto-size-14 cinza"
                                              value="Link Whatsapp: "> </h:outputText>
                                <i class="fa-icon-question-sign tooltipster"
                                   style="color: #777"
                                   title="Cole neste espaço o link que leva para o WhatsApp da sua empresa. O padrão é https://api.whatsapp.com/send?phone=(numero_do_telefone). "></i>
                            </h:panelGroup>

                            <h:panelGroup layout="block" style="font-size: 14px">
                                <h:inputText title="Max. 50 caracteres"
                                             value="#{GestaoVendasOnlineControle.contatoRodapeVendasOnlineVO.linkWhatsapp}"
                                             onblur="blurinput(this);" style="width: 90%"
                                             onfocus="focusinput(this);"
                                             size="50" maxlength="50"
                                             styleClass="testLinkWhatsapp form inputTextVendas"/>
                            </h:panelGroup>

                            <h:panelGroup styleClass="alinhamentoLabels">
                                <h:outputText styleClass="texto-size-14 cinza"
                                              value="Link Instagram: "> </h:outputText>
                                <i class="fa-icon-question-sign tooltipster"
                                   style="color: #777"
                                   title="Cole neste espaço o link que leva para o Instagram da sua empresa. O padrão é https://instagram.com/nome_do_usuario. "></i>
                            </h:panelGroup>

                            <h:panelGroup layout="block" style="font-size: 14px">
                                <h:inputText title="Max. 50 caracteres"
                                             value="#{GestaoVendasOnlineControle.contatoRodapeVendasOnlineVO.linkInstagram}"
                                             onblur="blurinput(this);" style="width: 90%"
                                             onfocus="focusinput(this);"
                                             size="50" maxlength="50"
                                             styleClass="testLinkInstagram form inputTextVendas"/>
                            </h:panelGroup>

                            <h:panelGroup styleClass="alinhamentoLabels">
                                <h:outputText styleClass="texto-size-14 cinza"
                                              value="Link Facebook: "> </h:outputText>
                                <i class="fa-icon-question-sign tooltipster"
                                   style="color: #777"
                                   title="Cole neste espaço o link que leva para o Facebook da sua empresa. O padrão é https://facebook.com/nome_do_usuario. "></i>
                            </h:panelGroup>

                            <h:panelGroup layout="block" style="font-size: 14px">
                                <h:inputText title="Max. 50 caracteres"
                                             value="#{GestaoVendasOnlineControle.contatoRodapeVendasOnlineVO.linkFacebook}"
                                             onblur="blurinput(this);" style="width: 90%"
                                             onfocus="focusinput(this);"
                                             size="50" maxlength="50"
                                             styleClass="testLinkFacebook form inputTextVendas"/>
                            </h:panelGroup>

                            <h:panelGroup styleClass="alinhamentoLabels">
                                <h:outputText styleClass="texto-size-14 cinza"
                                              value="Link Twitter: "> </h:outputText>
                                <i class="fa-icon-question-sign tooltipster"
                                   style="color: #777"
                                   title="Cole neste espaço o link que leva para o Twitter da sua empresa. O padrão é  https://twitter.com/nome_do_usuário "></i>
                            </h:panelGroup>

                            <h:panelGroup layout="block" style="font-size: 14px">
                                <h:inputText title="Max. 50 caracteres"
                                             value="#{GestaoVendasOnlineControle.contatoRodapeVendasOnlineVO.linkTwitter}"
                                             onblur="blurinput(this);" style="width: 90%"
                                             onfocus="focusinput(this);"
                                             size="50" maxlength="50"
                                             styleClass="testLinkTwitter form inputTextVendas"/>
                            </h:panelGroup>
                        </h:panelGrid>
                    </h:panelGroup>


                    <%--INTEGRAÇÕES--%>
                    <h:panelGroup layout="block" id="divAbaIntegracoesHotsite" style="width: 50%;"
                                  rendered="#{GestaoVendasOnlineControle.abaIntegracoesHotsite}">

                        <h:panelGrid width="100%" columns="2"
                                     columnClasses="w40, w60" cellpadding="10"
                                     style="margin-top: 20px">

                            <h:panelGroup styleClass="alinhamentoLabels">
                                <h:outputText styleClass="texto-size-14 cinza tooltipster"
                                              title="#{GestaoVendasOnlineControle.titleGoogleTagManagerHotsite}"
                                              value="Código Google Tag Manager: "> </h:outputText>
                            </h:panelGroup>

                            <h:panelGroup layout="block" style="font-size: 14px">
                                <h:inputText id="googleTagManagerHotsite"
                                             title="#{GestaoVendasOnlineControle.titleGoogleTagManagerHotsite}"
                                             onblur="blurinput(this);" style="width: 90%"
                                             onfocus="focusinput(this);" styleClass="form inputTextVendas tooltipster"/>
                            </h:panelGroup>

                        </h:panelGrid>
                    </h:panelGroup>


                    <%--STATUS--%>
                    <h:panelGroup id="painelStatus" style="display: inline-flex;">
                    <h:panelGrid width="100%" columns="2" cellpadding="10"
                                 style="margin-top: 20px" rendered="#{GestaoVendasOnlineControle.abaPublicar}">
                        <h:outputText styleClass="texto-size-18 cinza"
                                      value="Status atual: "> </h:outputText>

                        <%--STATUS PUBLICADO--%>
                        <h:panelGroup id="publicado" style="margin-top: 3px; display: block;"
                                      rendered="#{GestaoVendasOnlineControle.publicado and !GestaoVendasOnlineControle.erroConsultarStatus}">
                            <a4j:commandLink style="background-color: #00c350 !important; border-radius: 20px!important; cursor: default"
                                             value="Publicado"
                                             styleClass="botaoPrimario texto-size-14-real"/>
                        </h:panelGroup>

                        <%--STATUS ERRO--%>
                        <h:panelGroup id="erroStatus" style="margin-top: 3px; display: block;"
                                      rendered="#{GestaoVendasOnlineControle.erroConsultarStatus}">
                            <a4j:commandLink style="background-color: #BC2525 !important; border-radius: 20px!important; cursor: default"
                                             value="Erro"
                                             styleClass="botaoPrimario texto-size-14-real"/>
                        </h:panelGroup>

                        <h:outputText styleClass="texto-size-18 cinza"
                                      value="  "> </h:outputText>
                        <%--DESABILITAR PUBLICAÇÃO--%>
                        <h:panelGroup id="removerpublicacao" style="margin-top: 3px; display: block;"
                                      rendered="#{GestaoVendasOnlineControle.publicado and !GestaoVendasOnlineControle.erroConsultarStatus}">
                            <a4j:commandLink style="background-color: #BC2525 !important; border-radius: 20px!important;"
                                             value="Remover Publicação"
                                             action="#{GestaoVendasOnlineControle.desaabilitarHotsite}"
                                             reRender="vendasOnlinePanel"
                                             oncomplete="#{GestaoVendasOnlineControle.mensagemNotificar}"
                                             onclick="if(!confirm('Ao remover essa publicação o site não ficará mais disponível para acesso, deseja realmente remover esta publicação?')) {return false;}"
                                             styleClass="botaoPrimario texto-size-14-real"/>
                        </h:panelGroup>

                        <%--STATUS NÃO PUBLICADO--%>
                        <h:panelGroup id="naopublicado" style="margin-top: 3px; display: block;"
                                      rendered="#{!GestaoVendasOnlineControle.publicado and !GestaoVendasOnlineControle.erroConsultarStatus}">
                            <a4j:commandLink style="background-color: #777777 !important; border-radius: 20px!important; cursor: default"
                                             value="Não Publicado"
                                             styleClass="botaoPrimario texto-size-14-real"/>
                        </h:panelGroup>



                    </h:panelGrid>
                    </h:panelGroup>

                        <h:panelGroup  styleClass="alinhamentoLabels" rendered="#{GestaoVendasOnlineControle.abaPublicar}">
                            <h:outputText styleClass="texto-size-18 cinza tooltipster"
                                          style="margin-left: 10px;"
                                          title="#{GestaoVendasOnlineControle.titleExpDominioProprio}"
                                          value="Usar domínio próprio: "> </h:outputText>
                            <h:selectBooleanCheckbox
                                    styleClass="tooltipster"
                                    title="#{GestaoVendasOnlineControle.titleExpDominioProprio}"
                                    id="usarDominioProprio"
                                    style="margin-top: -3px;"
                                    value="#{GestaoVendasOnlineControle.dominioProprioHotsite}">
                                <a4j:support event="onclick"
                                             action="#{GestaoVendasOnlineControle.limparCamposHotsite}"
                                             reRender="painelDominio"/>
                            </h:selectBooleanCheckbox>


                        </h:panelGroup>

                    <h:panelGroup  styleClass="alinhamentoLabels" rendered="#{GestaoVendasOnlineControle.abaPublicar}">
                        <h:outputText styleClass="texto-size-18 cinza tooltipster"
                                      style="margin-left: 10px; margin-top: 25px"
                                      title="#{GestaoVendasOnlineControle.titleHabilitarEmpresa}"
                                      value="Mostrar na lista de empresas da página principal: "> </h:outputText>
                        <h:selectBooleanCheckbox styleClass="testClaro tooltipster"
                                                 value="#{GestaoVendasOnlineControle.habilitarempresahotsite}"
                                                 title="#{GestaoVendasOnlineControle.titleHabilitarEmpresa}">
                            <a4j:support event="onclick"
                                         action="#{GestaoVendasOnlineControle.ativaOuInativaEmpresaHotsite}"
                                         oncomplete="#{GestaoVendasOnlineControle.mensagemNotificar}"
                                         reRender="vendasOnlinePanel"/>
                        </h:selectBooleanCheckbox>
                    </h:panelGroup>



                    <h:panelGroup id="painelDominio">
                    <%--Domínio nosso hotsite.in--%>
                    <h:panelGroup layout="block" id="divAbaPublicar" style="width: 70%;"
                                  rendered="#{GestaoVendasOnlineControle.abaPublicar and !GestaoVendasOnlineControle.dominioProprioHotsite}">

                        <h:panelGrid width="100%" columns="3"  cellpadding="10"
                                     style="margin-top: 20px">

                            <h:panelGroup styleClass="alinhamentoLabels">
                                <h:outputText styleClass="texto-size-14 cinza"
                                              value="Defina o domínio do seu site: "> </h:outputText>
                                <i class="fa-icon-question-sign tooltipster"
                                   style="color: #777"
                                   title="Adicione aqui a url que será utilizado no seu hotsite. "></i>
                            </h:panelGroup>

                            <h:panelGroup layout="block" style="font-size: 14px">
                                <h:outputText  styleClass="texto-size-14 cinza" value="#{GestaoVendasOnlineControle.protocoloHttps}"/>
                                <h:inputText title="Máx. 30 caracteres"
                                             disabled="#{GestaoVendasOnlineControle.exibirBtnPublicar}"
                                             value="#{GestaoVendasOnlineControle.urlHotsite}"
                                             onblur="blurinput(this);mascaraLetraNumero(this);" onkeypress="mascaraLetraNumero(this);"
                                             onkeyup="mascaraLetraNumero(this);" onkeydown="mascaraLetraNumero(this);"
                                             onfocus="focusinput(this);mascaraLetraNumero(this);" maxlength="30"
                                             styleClass="testdominio  form inputTextVendas"/>
                                <h:outputText styleClass="texto-size-14 cinza" value="#{GestaoVendasOnlineControle.dominioHotsite}"/>
                            </h:panelGroup>

                            <h:panelGroup id="btnValidar" style="display: grid;">
                            <a4j:commandLink id="validarUrlSite"
                                             action="#{GestaoVendasOnlineControle.validarUrlAPI}"
                                             onclick="addCores();"
                                             reRender="divAbaPublicar" process="divAbaPublicar"
                                             oncomplete="#{GestaoVendasOnlineControle.mensagemNotificar}"
                                             value="Validar URL" style="width: fit-content;"
                                             styleClass="botaoPrimario texto-size-14-real"/>
                            </h:panelGroup>



                        </h:panelGrid>

                        <h:panelGroup rendered="#{GestaoVendasOnlineControle.exibirBtnPublicar}">
                            <h:panelGrid width="100%" columns="2" columnClasses="w40, w60" cellpadding="5" style="margin-top: 20px">
                                <h:panelGroup styleClass="alinhamentoLabels">
                                    <h:outputText styleClass="texto-size-14 cinza"
                                                  value="Clique em Publicar para confirmar o site: "> </h:outputText>
                                    <i class="fa-icon-question-sign tooltipster"
                                       style="color: #777"
                                       title="O Site somente será publicado após clicar em publicar "></i>
                                </h:panelGroup>

                                <h:panelGroup style="margin-top: 15px; display: block; margin-left: -47px;">
                                <a4j:commandLink id="btnPublicar"
                                                 action="#{GestaoVendasOnlineControle.publicarUrlAPI}"
                                                 onclick="addCores();" style="background-color: #00c350 !important; margin-left: 25px;"
                                                 reRender="form:vendasOnlinePanel"
                                                 oncomplete="#{GestaoVendasOnlineControle.mensagemNotificar}"
                                                 value="Publicar"
                                                 styleClass="botaoPrimario texto-size-14-real"/>
                                </h:panelGroup>
                            </h:panelGrid>

                        </h:panelGroup>
                        <h:panelGroup rendered="#{GestaoVendasOnlineControle.msgPublicado}">
                            <h:panelGroup styleClass="alinhamentoLabels">
                                <h:outputText styleClass="texto-size-14 blueGrande"
                                              value="O Site foi publicado com sucesso!!!"> </h:outputText>
                            </h:panelGroup>
                        </h:panelGroup>
                    </h:panelGroup>



                    <%--Domínio próprio--%>
                    <h:panelGroup layout="block" id="divAbaPublicarDominioProprio" style="width: 70%;"
                                  rendered="#{GestaoVendasOnlineControle.abaPublicar and GestaoVendasOnlineControle.dominioProprioHotsite}">

                        <h:panelGrid width="100%" columns="3"  cellpadding="10"
                                     style="margin-top: 20px">

                            <h:panelGroup styleClass="alinhamentoLabels" style="margin-top: 10px;">
                                <h:outputText styleClass="texto-size-14 cinza"
                                              value="Informe o domínio do seu site: "> </h:outputText>
                                <i class="fa-icon-question-sign tooltipster"
                                   style="color: #777"
                                   title="Deverá informar ao lado, a url do seu site domínio próprio. Lembrando que para o funcionamento correto é necessário que você tenha adquirido o certificado SSL/TLS 1.2 e nos enviado"></i>
                            </h:panelGroup>

                            <h:panelGroup layout="block" style="font-size: 14px;margin-left: -15%;">
                                <h:outputText  styleClass="texto-size-14 cinza" value="#{GestaoVendasOnlineControle.protocoloHttps}"/>
                                <h:inputText title="Máx. 50 caracteres"
                                             value="#{GestaoVendasOnlineControle.urlHotsite}"
                                             disabled="#{GestaoVendasOnlineControle.exibirBtnPublicar}"
                                             onblur="blurinput(this);"
                                             onfocus="focusinput(this);" maxlength="50"
                                             styleClass="testdominio  form inputTextVendas" style="width: 85%;"/>
                            </h:panelGroup>

                            <h:panelGroup id="btnValidarProprio" style="display: grid;margin-top: 2px;">
                            <a4j:commandLink id="validarUrlDominioProprio"
                                             action="#{GestaoVendasOnlineControle.validarUrlAPIDominioProprio}"
                                             onclick="addCores();"
                                             reRender="divAbaPublicarDominioProprio" process="divAbaPublicarDominioProprio"
                                             oncomplete="#{GestaoVendasOnlineControle.mensagemNotificar}"
                                             value="Validar URL" style="width: fit-content;"
                                             styleClass="botaoPrimario texto-size-14-real"/>
                            </h:panelGroup>

                        </h:panelGrid>

                        <h:panelGroup rendered="#{GestaoVendasOnlineControle.exibirBtnPublicar}">
                            <h:panelGrid width="100%" columns="2" columnClasses="w40, w60" cellpadding="5" style="margin-top: 20px">
                                <h:panelGroup styleClass="alinhamentoLabels">
                                    <h:outputText styleClass="texto-size-14 cinza"
                                                  value="Clique em Publicar para confirmar o site: "> </h:outputText>
                                    <i class="fa-icon-question-sign tooltipster"
                                       style="color: #777"
                                       title="O Site somente será publicado após clicar em publicar "></i>
                                </h:panelGroup>

                                <h:panelGroup style="margin-top: 15px; display: block; margin-left: -11px;">
                                <a4j:commandLink id="btnPublicarSiteProprio"
                                                 action="#{GestaoVendasOnlineControle.publicarUrlAPIDominioProprio}"
                                                 onclick="addCores();" style="background-color: #00c350 !important;"
                                                 reRender="form:vendasOnlinePanel"
                                                 oncomplete="#{GestaoVendasOnlineControle.mensagemNotificar}"
                                                 value="Publicar"
                                                 styleClass="botaoPrimario texto-size-14-real"/>
                                </h:panelGroup>
                            </h:panelGrid>
                        </h:panelGroup>
                        <h:panelGroup rendered="#{GestaoVendasOnlineControle.msgPublicado}">
                            <h:panelGroup styleClass="alinhamentoLabels">
                                <h:outputText styleClass="texto-size-14 blueGrande"
                                              value="O Site foi publicado com sucesso!!!"> </h:outputText>
                            </h:panelGroup>
                        </h:panelGroup>
                    </h:panelGroup>
                    </h:panelGroup>

                    <%--Aplicar Alterações--%>
                    <h:panelGroup layout="block" styleClass="container-botoes"
                                  style="padding-top: 30px; width: 82%">
                        <a4j:commandLink id="gravarSiteConfig"
                                         action="#{GestaoVendasOnlineControle.gravar}"
                                         onclick="addCores();"
                                         rendered="#{GestaoVendasOnlineControle.abaPublicar eq false}"
                                         reRender="vendasOnlinePanel"
                                         oncomplete="#{GestaoVendasOnlineControle.mensagemNotificar}"
                                         value="Aplicar alterações"
                                         styleClass="testAplicarAlteracao botaoPrimario texto-size-14-real"/>
                        <a4j:commandLink id="visualizarLogSiteConfig"
                                         immediate="true"
                                         action="#{GestaoVendasOnlineControle.realizarConsultaLogObjetoSelecionadoVendasOnlineConfigs}"
                                         accesskey="5"
                                         rendered="#{GestaoVendasOnlineControle.abaPublicar eq false}"
                                         style="font-size: 15.7px; margin-left: 10px;"
                                         styleClass="botaoSecundario texto-size-14-real fa-icon-list "
                                         onclick="abrirPopup('visualizadorLogForm.jsp', 'VisualizadorLog', 785, 595);">
                        </a4j:commandLink>
                    </h:panelGroup>

                </h:panelGroup>


                <c:if test="${GestaoVendasOnlineControle.abaModaCarrosel or GestaoVendasOnlineControle.abaModaBanner}">
                    <style>
                        .pbody {
                            width: 100%;
                            text-align: center;
                        }
                        .rich-table-cell{
                            max-width: 0;
                        }
                        .rich-panel{
                            height: calc(65vh - 17vw);
                            min-height: 275px;
                            max-height: 395px;
                        }
                    </style>
                    <rich:dataGrid value="#{GestaoVendasOnlineControle.listaModalidadeCarrouselVendasOnline}" var="item" columns="3" elements="15"
                                   style="width: 100%;">
                        <rich:panel bodyClass="pbody">
                            <f:facet name="header">
                                <h:outputText value="#{item.tipoAgrupamento.descricao}" />
                            </f:facet>
                            <h:panelGrid columns="1" columnClasses="pbodyCustomizado">
                                <h:outputText value="Título - #{item.tituloModalidadeVendas}" />
                                <h:graphicImage styleClass="tooltipster"
                                                title="Imagem Selecionada"
                                                url="#{item.urlFoto}"
                                                style="padding-left: 6px; width:100px; height:100px;"/>
                                <h:outputText value="Descrição: #{item.descricaoModalidadeVendas}"></h:outputText>
                            </h:panelGrid>
                        </rich:panel>
                        <h:panelGrid columns="2" style="text-align: center; padding: 5px;">
                            <a4j:commandLink style="font-size: 20px;"
                                             styleClass="testEditarCarrocel"
                                             action="#{GestaoVendasOnlineControle.editarModaCarrouselVendasOnline}"
                                             reRender="divAbaModaBanner, divAbaModaCarrousel">
                                <i class="fa-icon-edit"></i>
                            </a4j:commandLink>
                            <a4j:commandLink style="font-size: 20px;"
                                             styleClass="testExcluirCarrocel"
                                             action="#{GestaoVendasOnlineControle.abrirModalExclusao}"
                                             reRender="panelGroupModalExclusao"
                                             oncomplete="Richfaces.showModalPanel('panelExclusaoModalidade');">
                                <i class="fa-icon-remove red"></i>
                            </a4j:commandLink>
                        </h:panelGrid>
                    </rich:dataGrid>
                </c:if>

                <c:if test="${GestaoVendasOnlineControle.abaPlanos}">
                    <a4j:repeat id="forPlanoSite" var="planoSite" value="#{GestaoVendasOnlineControle.listaPlanosSiteVendasOnline}">
                        <h:panelGrid columns="2" width="50%" style="margin-left:20px; border-bottom: 1px solid #ccc;">
                            <h:panelGroup layout="block" styleClass="colunaEsquerda" style="border-bottom: #5d7b89">
                                <h:outputText styleClass="texto-upper texto-size-14 cinza"
                                              value="#{planoSite.destaqueCabecalho}"/>
                            </h:panelGroup>
                            <h:panelGroup layout="block" styleClass="colunaDireita">
                                <a4j:commandLink  styleClass="testEditarPlano" style="font-size: 20px;"
                                                 action="#{GestaoVendasOnlineControle.editarPlanoSiteVendasOnline}"
                                                 reRender="divAbaPlanoSite">
                                    <i class="fa-icon-edit"></i>
                                </a4j:commandLink>
                                <a4j:commandLink styleClass="testExclirPlano" style="font-size: 20px;"
                                                 action="#{GestaoVendasOnlineControle.removerPlanoSite}"
                                                 reRender="vendasOnlinePanel"
                                                 oncomplete="#{GestaoVendasOnlineControle.mensagemNotificar}">
                                    <i class="fa-icon-remove red"></i>
                                </a4j:commandLink>
                            </h:panelGroup>
                        </h:panelGrid>
                    </a4j:repeat>
                </c:if>
                <%-- CONFIGURAÇÕES --%>
                <h:panelGroup rendered="#{GestaoVendasOnlineControle.configuracoes}" layout="block" styleClass="gr-container-totalizador"
                              style="margin: 10px 20px 20px 20px; width: calc(100% - 40px); border: none;" id="configsV">
                    <h:panelGroup layout="block" styleClass="text"
                                  style="display: inline-flex; border-bottom:#E5E5E5 1px solid; width: 100%; position: relative; height: 40px;">
                        <a4j:commandLink id="abrirBasico"
                                         action="#{GestaoVendasOnlineControle.abrirBasico}"
                                         onclick="addCor();"
                                         reRender="vendasOnlinePanel"
                                         styleClass="botaoModoTimeLine filtrosPrincipais #{GestaoVendasOnlineControle.abaBasico ? 'ativo' : ''}">
                            <h:outputText value="Básico"/>
                        </a4j:commandLink>

                        <a4j:commandLink id="abrirAvancado"
                                         action="#{GestaoVendasOnlineControle.abrirAvancado}"
                                         onclick="addCor();"
                                         reRender="vendasOnlinePanel"
                                         styleClass="botaoModoTimeLine filtrosPrincipais #{GestaoVendasOnlineControle.abaAvancado ? 'ativo' : ''}">
                            <h:outputText value="Avançado"/>
                        </a4j:commandLink>

                        <a4j:commandLink id="abrirConvenioCobranca"
                                         action="#{GestaoVendasOnlineControle.abrirConvenioCobranca}"
                                         onclick="addCor();"
                                         reRender="vendasOnlinePanel"
                                         styleClass="botaoModoTimeLine filtrosPrincipais #{GestaoVendasOnlineControle.abaConvenioCobranca ? 'ativo' : ''}">
                            <h:outputText value="Convênio de Cobrança"/>
                        </a4j:commandLink>

                        <a4j:commandLink id="abrirFormaPagamento"
                                         action="#{GestaoVendasOnlineControle.abrirFormaPagamento}"
                                         onclick="addCor();"
                                         reRender="vendasOnlinePanel"
                                         styleClass="botaoModoTimeLine filtrosPrincipais #{GestaoVendasOnlineControle.abaFormaPagamento ? 'ativo' : ''}">
                            <h:outputText value="Forma de Pagamento"/>
                        </a4j:commandLink>

                        <a4j:commandLink id="integracoesVendasOnline"
                                         action="#{GestaoVendasOnlineControle.abrirIntegracoes}"
                                         onclick="addCor();"
                                         reRender="vendasOnlinePanel"
                                         styleClass="botaoModoTimeLine filtrosPrincipais #{GestaoVendasOnlineControle.abaIntegracoes ? 'ativo' : ''}">
                            <h:outputText value="Integrações"/>
                        </a4j:commandLink>

                        <a4j:commandLink id="abrirCampo"
                                         action="#{GestaoVendasOnlineControle.abrirCamposAdicionais()}"
                                         onclick="addCor();"
                                         reRender="vendasOnlinePanel"
                                         styleClass="botaoModoTimeLine filtrosPrincipais #{GestaoVendasOnlineControle.abaCamposAdicionais ? 'ativo' : ''}">
                            <h:outputText value="Campos a exibir"/>
                        </a4j:commandLink>
                    </h:panelGroup>

                    <h:panelGroup layout="block" id="divAbaBasico" style="width: 50%;"
                                  rendered="#{GestaoVendasOnlineControle.abaBasico}">

                        <h:panelGrid width="100%" columns="2"
                                     columnClasses="w40, w60" cellpadding="10"
                                     style="margin-top: 20px">

                            <h:panelGroup
                                    styleClass="alinhamentoLabels alinhamentoLabelsQuebrada">
                                <h:outputText styleClass="texto-size-14 cinza"
                                              value="Cor dos detalhes: "></h:outputText>
                                <i class="fa-icon-question-sign tooltipster"
                                   style="color: #777"
                                   title="Cor de botões e alguns detalhes do site"></i>
                            </h:panelGroup>
                            <h:panelGroup style="vertical-align: middle;">
                                <input type="color" id="cordetalhes" name="body"
                                       value="${GestaoVendasOnlineControle.config.cor}">
                                <h:inputHidden
                                        value="#{GestaoVendasOnlineControle.config.cor}"
                                        id="cor"/>
                            </h:panelGroup>


                            <h:panelGroup>
                                <h:outputText styleClass="texto-size-14 cinza"
                                              value="Utilizar tela de confirmação de compra padrão:"> </h:outputText>
                                <i class="fa-icon-question-sign tooltipster"
                                   style="color: #777"
                                   title="Se desejar personalizar a página destino, desmarque esta opção"></i>
                            </h:panelGroup>
                            <h:panelGroup layout="block"
                                          styleClass="chk-fa-container alinhamentoCheckLabel alinhamentoCheckLabelQuebrada">
                                <h:selectBooleanCheckbox
                                        id="cbComfirmaCompraPadrao"
                                        value="#{GestaoVendasOnlineControle.config.redirecionarApp}">
                                    <a4j:support event="onclick"
                                                 reRender="vendasOnlinePanel"/>
                                </h:selectBooleanCheckbox>
                                <span/>
                            </h:panelGroup>

                            <h:panelGroup
                                    rendered="#{!GestaoVendasOnlineControle.config.redirecionarApp}"
                                    styleClass="alinhamentoLabels alinhamentoLabelsQuebrada">
                                <h:outputText styleClass="texto-size-14 cinza"
                                              value="Endereço para direcionar o aluno após a venda: "> </h:outputText>
                                <i class="fa-icon-question-sign tooltipster"
                                   style="color: #777"
                                   title="Informe o endereço da página para qual o cliente será direcionado após a conclusão da venda"></i>
                            </h:panelGroup>
                            <h:panelGroup
                                    rendered="#{!GestaoVendasOnlineControle.config.redirecionarApp}"
                                    layout="block"
                                    style="font-size: 14px">
                                <h:inputText
                                        id="paginaRedirecionar"
                                        value="#{GestaoVendasOnlineControle.config.paginaRedirecionar}"
                                        onblur="blurinput(this);" style="width: 90%"
                                        onfocus="focusinput(this);"
                                        styleClass="form inputTextVendas"/>
                            </h:panelGroup>

                            <h:panelGroup>
                                <h:outputText styleClass="texto-size-14 cinza"
                                              value="Permitir renovação de contrato:"> </h:outputText>
                                <i class="fa-icon-question-sign tooltipster"
                                   style="color: #777"
                                   title="Quando o cliente comprar um novo plano no Vendas Online e tiver um contrato anterior, permite que ele seja renovado com os dados do novo plano"></i>
                            </h:panelGroup>
                            <h:panelGroup layout="block"
                                          styleClass="chk-fa-container alinhamentoCheckLabel alinhamentoCheckLabelQuebrada">
                                <h:selectBooleanCheckbox
                                        id="cbRenovarContrato"
                                        value="#{GestaoVendasOnlineControle.config.renovarContratoAntigo}">
                                    <a4j:support event="onclick"
                                                 reRender="vendasOnlinePanel"/>
                                </h:selectBooleanCheckbox>
                                <span/>
                            </h:panelGroup>
                            <!--- INICIO CHECKBOX PRE CADASTRO --->
                            <h:panelGroup>
                                <h:outputText styleClass="texto-size-14 cinza"
                                              value="Solicitar informações de pré-cadastro (geração de leads):"> </h:outputText>
                                <i class="fa-icon-question-sign tooltipster"
                                   style="color: #777"
                                   title="Solicitar um pré-cadastro do cliente no momento do checkout garante que, mesmo que a compra não seja finalizada, os dados do cliente sejam capturados como leads."></i>
                            </h:panelGroup>
                            <h:panelGroup layout="block"
                                          styleClass="chk-fa-container alinhamentoCheckLabel alinhamentoCheckLabelQuebrada">
                                <h:selectBooleanCheckbox
                                        id="cbHabilitarPreCadastro"
                                        value="#{GestaoVendasOnlineControle.config.habilitarPreCadastro}">
                                    <a4j:support event="onclick"
                                                 reRender="vendasOnlinePanel"/>
                                </h:selectBooleanCheckbox>
                                <span/>
                            </h:panelGroup>
                            <!--- FIM CHECKBOX PRE CADASTRO ---->

                            <h:panelGroup styleClass="alinhamentoLabels">
                                <h:outputText styleClass="texto-size-14 cinza"
                                              value="E-mail para alerta de compra: "> </h:outputText>
                                <i class="fa-icon-question-sign tooltipster"
                                   style="color: #777"
                                   title="Informe um endereço de e-mail para que o sistema possa enviar um alerta de compra concluída. Se quiser informar mais de um endereço, separe por ';'"></i>
                            </h:panelGroup>

                            <h:panelGroup layout="block" style="font-size: 14px">
                                <h:inputText
                                        id="emailAvisar"
                                        value="#{GestaoVendasOnlineControle.config.emailAvisar}"
                                        onblur="blurinput(this);" style="width: 90%"
                                        onfocus="focusinput(this);"
                                        styleClass="form inputTextVendas"/>
                            </h:panelGroup>
                        </h:panelGrid>
                    </h:panelGroup>


                    <h:panelGroup layout="block" id="divAbaAvancado" style="width: 50%;"
                                  rendered="#{GestaoVendasOnlineControle.abaAvancado}">

                        <h:panelGrid width="100%" columns="2"
                                     columnClasses="w40, w60" cellpadding="10"
                                     style="margin-top: 20px">

                            <h:panelGroup styleClass="alinhamentoLabels">
                                <h:outputText styleClass="texto-size-14 cinza"
                                              value="Título na tela de escolha de planos: "></h:outputText>
                                <i class="fa-icon-question-sign tooltipster"
                                   style="color: #777;"
                                   title="Este título irá aparecer na tela de escolha de planos, onde o aluno escolhe o plano. </br>Aconselhamos títulos inspiradores. Ex: <strong>'Bora Treinar?'</strong>."></i>
                            </h:panelGroup>

                            <h:panelGroup layout="block"
                                          style="font-size: 14px">
                                <h:inputText
                                        id="tituloCheckout"
                                        value="#{GestaoVendasOnlineControle.config.tituloCheckout}"
                                        onblur="blurinput(this);" style="width: 90%;"
                                        onfocus="focusinput(this);"
                                        styleClass="form inputTextVendas"/>
                            </h:panelGroup>

                            <h:panelGroup>
                                <h:outputText styleClass="texto-size-14 cinza"
                                              value="Cobrar 'PARCELA 1' sempre no ato da compra:"> </h:outputText>
                                <i class="fa-icon-question-sign tooltipster"
                                   style="color: #777"
                                   title="Por padrão o sistema sempre cobra as parcelas com vencimento para a data da compra.
                                   Se tiver planos configurados para gerar primeira parcela com vencimento posterior a compra e deseja cobrar, </br>
                                   marque esta opção para que a primeira parcela sempre seja cobrada no ato da compra, junto com a adesão/matricula e produtos,
                                   inclusive em contratos com início futuro. </br>
                                    <b>Obs:</b> Além desta configuração, existe uma configuração <b>'Entrada'</b> na Condição de Pagamento que também tem o efeito de cobrar parcelas futuras. </br>
                                    Se a sua intenção é não cobrar parcelas com vencimento futuro no ato da venda, certifique-se de que ela também não esteja marcada."></i>
                            </h:panelGroup>
                            <h:panelGroup layout="block"
                                          styleClass="chk-fa-container alinhamentoCheckLabel alinhamentoCheckLabelQuebrada">
                                <h:selectBooleanCheckbox
                                        id="cbCobrarPrimeiraParcelaCompra"
                                        value="#{GestaoVendasOnlineControle.config.cobrarPrimeiraParcelaCompra}">
                                    <a4j:support event="onclick"
                                                 action="#{GestaoVendasOnlineControle.desmarcarCobrarProdutosAtoCompra}"
                                                 reRender="vendasOnlinePanel"/>
                                </h:selectBooleanCheckbox>
                                <span/>
                            </h:panelGroup>

                            <c:if test="${!GestaoVendasOnlineControle.config.cobrarPrimeiraParcelaCompra}">
                                <h:panelGroup>
                                    <h:outputText styleClass="texto-size-14 cinza"
                                                  value="Cobrar produto(s) junto com adesão/matricula:"> </h:outputText>
                                    <i class="fa-icon-question-sign tooltipster"
                                       style="color: #777"
                                       title="Marque esta opção para que os produtos obrigatórios configurados no plano, sejam cobrados junto com adesão/matricula.
                               </br> Será lançado uma nova parcela no valor do produto com a mesma data de vencimento que a parcela de adesão/matricula.
                               </br><strong>Obs: Essa configuração serve apenas para casos em que o plano do aluno está com a configuração 'Permite cobrar produtos separado' desmarcada
                               </br> e a configuração 'Cobrar parcela 1 sempre no ato da compra' tambem esteja desmarcada, e o dia de vencimento da primeira parcela será diferente de hoje,
                                </br>o gestor mesmo assim deseja cobrar os produtos junto à adesão/matricula e não somados à parcela 1, que pode ter o dia de vencimento futuro.</strong>"></i>
                                </h:panelGroup>

                                <h:panelGroup layout="block"
                                              styleClass="chk-fa-container alinhamentoCheckLabel alinhamentoCheckLabelQuebrada">
                                    <h:selectBooleanCheckbox
                                            id="cbCobrarJuntoAdesao"
                                            value="#{GestaoVendasOnlineControle.config.cobrarProdutosJuntoAdesao}">
                                    </h:selectBooleanCheckbox>
                                    <span/>
                                </h:panelGroup>
                            </c:if>

                            <c:if test="${GestaoVendasOnlineControle.config.renovarContratoAntigo}">
                                <h:panelGroup styleClass="alinhamentoLabels">
                                    <h:outputText styleClass="texto-size-14 cinza"
                                                  value="Cobrar 'PARCELA 1' sempre no ato da compra (Renovação):"/>
                                    <i class="fa-icon-question-sign tooltipster"
                                       style="color: #777"
                                       title="Marque esta opção para que a primeira parcela sempre seja cobrada no ato da compra, junto com a adesão/renovação e produtos, inclusive em contratos com início futuro."></i>
                                </h:panelGroup>
                                <h:panelGroup layout="block"
                                              styleClass="chk-fa-container alinhamentoCheckLabel alinhamentoCheckLabelQuebrada">
                                    <h:selectBooleanCheckbox
                                            id="cbCobrarPrimeiraParcelaRenovacao"
                                            value="#{GestaoVendasOnlineControle.config.cobrarPrimeiraParcelaCompraRenovacao}">
                                        <a4j:support event="onclick"
                                                     action="#{GestaoVendasOnlineControle.desmarcarCobrarPrimeiraParcelaCompraRenovacao}"
                                                     reRender="vendasOnlinePanel"/>
                                    </h:selectBooleanCheckbox>
                                    <span/>
                                </h:panelGroup>
                            </c:if>

                            <c:if test="${GestaoVendasOnlineControle.config.cobrarPrimeiraParcelaCompraRenovacao}">
                                <h:panelGroup styleClass="alinhamentoLabels">
                                    <h:outputText styleClass="texto-size-14 cinza"
                                                  value="Cobrar parcelas da renovação no mês seguinte da 'PARCELA 1'"/>
                                    <i class="fa-icon-question-sign tooltipster"
                                       style="color: #777"
                                       title="Marque esta opção para que as demais parcelas sejam cobradas no mês seguinte da 'PARCELA 1'"></i>
                                </h:panelGroup>
                                <h:panelGroup layout="block"
                                              styleClass="chk-fa-container alinhamentoCheckLabel alinhamentoCheckLabelQuebrada">
                                    <h:selectBooleanCheckbox styleClass="form" id="cbCobrarPrimeiraParcelasMesSeguinteRenovacao" value="#{GestaoVendasOnlineControle.config.cobrarParcelasMesSeguinteRenovacao}"/>
                                    <span/>
                                </h:panelGroup>
                            </c:if>


                            <h:panelGroup>
                                <h:outputText styleClass="texto-size-14 cinza"
                                              value="Detalhar primeira parcela no checkout:"> </h:outputText>
                                <i class="fa-icon-question-sign tooltipster"
                                   style="color: #777"
                                   title="Marque esta opção para que a primeira parcela seja detalhada na tela de checkout. O detalhamento da parcela, mostra o valor de cada produto</br> que está sendo cobrado do aluno na primeira parcela."></i>
                            </h:panelGroup>
                            <h:panelGroup layout="block"
                                          styleClass="chk-fa-container alinhamentoCheckLabel alinhamentoCheckLabelQuebrada">
                                <h:selectBooleanCheckbox
                                        id="cbDetalharParcelaCheckout"
                                        value="#{GestaoVendasOnlineControle.config.detalharParcelaTelaCheckout}">
                                    <a4j:support event="onclick"
                                                 action="#{GestaoVendasOnlineControle.tratarApresentarValorAnuidade}"
                                                 reRender="vendasOnlinePanel"/>
                                </h:selectBooleanCheckbox>
                                <span/>
                            </h:panelGroup>

                            <c:if test="${GestaoVendasOnlineControle.config.detalharParcelaTelaCheckout}">
                                <h:panelGroup styleClass="alinhamentoLabels">
                                    <h:outputText styleClass="texto-size-14 cinza"
                                                  value="Exibir valor da anuidade:"> </h:outputText>
                                    <i class="fa-icon-question-sign tooltipster"
                                       style="color: #777"
                                       title="Ao marcar esta configuração, na tela de checkout o valor da anuidade será exibido, juntamente com o detalhamento de quando será cobrada. Com essa opção desmarcada, será exibido somente quando a anuidade será cobrada, sem exibir o valor."></i>
                                </h:panelGroup>

                                <h:panelGroup layout="block"
                                              styleClass="chk-fa-container alinhamentoCheckLabel">
                                    <h:selectBooleanCheckbox
                                            id="cbValorAnuidade"
                                            value="#{GestaoVendasOnlineControle.config.apresentarValorAnuidade}">
                                    </h:selectBooleanCheckbox>
                                    <span/>
                                </h:panelGroup>
                            </c:if>

                            <h:panelGroup styleClass="alinhamentoLabels">
                                <h:outputText styleClass="texto-size-14 cinza"
                                              value="Exibir produto sem estoque:"> </h:outputText>
                                <i class="fa-icon-question-sign tooltipster"
                                   style="color: #777"
                                   title="Ao marcar esta configuração, na tela de produtos será exibido os produtos que estão sem estoque.<br/> O produto será exibido com a mensagem <b>PRODUTO SEM ESTOQUE</b> e não será possível comprar."></i>
                            </h:panelGroup>

                            <h:panelGroup layout="block"
                                          styleClass="chk-fa-container alinhamentoCheckLabel">
                                <h:selectBooleanCheckbox
                                        id="cbProdutoSemEstoque"
                                        value="#{GestaoVendasOnlineControle.config.apresentarProdutoSemEstoque}">
                                </h:selectBooleanCheckbox>
                                <span/>
                            </h:panelGroup>

                            <h:panelGroup styleClass="alinhamentoLabels">
                                <h:outputText styleClass="texto-size-14 cinza"
                                              value="Consultor Site padrão:"/>
                                <i class="fa-icon-question-sign tooltipster"
                                   style="color: #777"
                                   title="Informe o consultor responsável por todas as vendas realizadas através do Vendas Online.
                                   </br>Caso você deixar esse campo em branco, o sistema irá lançar como 'COLABORADOR SITE' por padrão.
                                   </br>Se você gerar links lá na aba 'Links' para um usuário específico do sistema, essa configuração aqui será sobreposta pelo o usuário informado lá."></i>
                            </h:panelGroup>

                            <h:panelGroup layout="block" styleClass="cb-container"
                                          style="width: 90%; font-size: 14px">
                                <h:selectOneMenu
                                        id="cbConsultorSite"
                                        value="#{GestaoVendasOnlineControle.config.consultorSite.codigo}">
                                    <f:selectItems
                                            value="#{GestaoVendasOnlineControle.listaSelectItemConsultor}"/>
                                </h:selectOneMenu>
                            </h:panelGroup>

                            <h:panelGroup>
                                <h:outputText styleClass="texto-size-14 cinza"
                                              value="Apresentar CPF na tela de Link de Pagamento:"> </h:outputText>
                                <i class="fa-icon-question-sign tooltipster"
                                   style="color: #777"
                                   title="Marque esta opção para que apresentar CPF na tela de link de pagamento"></i>
                            </h:panelGroup>
                            <h:panelGroup layout="block"
                                          styleClass="chk-fa-container alinhamentoCheckLabel alinhamentoCheckLabelQuebrada">
                                <h:selectBooleanCheckbox
                                        id="cbCPFLinkPag"
                                        value="#{GestaoVendasOnlineControle.config.apresentarCPFLinkPag}">
                                </h:selectBooleanCheckbox>
                                <span/>
                            </h:panelGroup>

<%--                            <h:panelGroup>--%>
<%--                                <h:outputText styleClass="texto-size-14 cinza"--%>
<%--                                              value="Apresentar Venc. Fatura na tela de Link de Pagamento:"> </h:outputText>--%>
<%--                                <i class="fa-icon-question-sign tooltipster"--%>
<%--                                   style="color: #777"--%>
<%--                                   title="Marque esta opção para que apresentar o campo de vencimento da fatura na tela de link de pagamento"></i>--%>
<%--                            </h:panelGroup>--%>
<%--                            <h:panelGroup layout="block"--%>
<%--                                          styleClass="chk-fa-container alinhamentoCheckLabel alinhamentoCheckLabelQuebrada">--%>
<%--                                <h:selectBooleanCheckbox--%>
<%--                                        id="cbVencFaturaLinkPag"--%>
<%--                                        value="#{GestaoVendasOnlineControle.config.apresentarDtFaturaLinkPag}">--%>

<%--                                </h:selectBooleanCheckbox>--%>
<%--                                <span/>--%>
<%--                            </h:panelGroup>--%>

                            <h:panelGroup id="termoAceiteLinkPag">
                                <h:outputText styleClass="texto-size-14 cinza"
                                              value="Apresentar termo aceite na tela de Link de Pagamento:"> </h:outputText>
                                <i class="fa-icon-question-sign tooltipster"
                                   style="color: #777"
                                   title="Marque esta opção para que apresentar o termo de aceite na tela de link de pagamento"></i>
                            </h:panelGroup>
                            <h:panelGroup layout="block" id="checkBoxTermoAceite"
                                          styleClass="chk-fa-container alinhamentoCheckLabel alinhamentoCheckLabelQuebrada">
                                <h:selectBooleanCheckbox
                                        id="cbTermoAceiteLinkPag"
                                        value="#{GestaoVendasOnlineControle.config.apresentarTermoAceiteLinkPag}">
                                </h:selectBooleanCheckbox>
                                <span/>
                            </h:panelGroup>

                            <%--Recurso comentado, porquê fez o back e não fez o front e o projeto foi abandonado em 2022--%>
                            <%--Se no futuro resolver criar algo, definir a regra de funcionamento, descomentar essa parte e criar todo o front de uso do recurso--%>
<%--                            <h:panelGroup id="textPagarUsandoSaldoCliente">--%>
<%--                                <h:outputText styleClass="texto-size-14 cinza"--%>
<%--                                              value="Usar saldo do cliente no Link de Pagamento:"> </h:outputText>--%>
<%--                                <i class="fa-icon-question-sign tooltipster"--%>
<%--                                   style="color: #777"--%>
<%--                                   title="Marque esta opo para descontar o valor de pagamento usando o saldo do cliente, caso ele possua o valor completo no deve inserir outros meios de pagamento"></i>--%>
<%--                            </h:panelGroup>--%>
<%--                            <h:panelGroup layout="block" id="checkBoxPagarUsandoSaldoCliente"--%>
<%--                                          styleClass="chk-fa-container alinhamentoCheckLabel alinhamentoCheckLabelQuebrada">--%>
<%--                                <h:selectBooleanCheckbox--%>
<%--                                        value="#{GestaoVendasOnlineControle.config.pagarUsandoSaldoCliente}">--%>

<%--                                </h:selectBooleanCheckbox>--%>
<%--                                <span/>--%>
<%--                            </h:panelGroup>--%>

                            <h:panelGroup>
                                <h:outputText styleClass="texto-size-14 cinza"
                                              value="Estornar vendas Pix expirado:"> </h:outputText>
                                <i class="fa-icon-question-sign tooltipster"
                                   style="color: #777"
                                   title="Quando o cliente realizar uma compra com PIX,<br/>caso o pix não seja pago e expire será estornado a venda/contrato automaticamente"></i>
                            </h:panelGroup>
                            <h:panelGroup layout="block"
                                          styleClass="chk-fa-container alinhamentoCheckLabel alinhamentoCheckLabelQuebrada">
                                <h:selectBooleanCheckbox
                                        id="estornarVendaPix"
                                        value="#{GestaoVendasOnlineControle.config.estornarVendaPix}">
                                </h:selectBooleanCheckbox>
                                <span/>
                            </h:panelGroup>

                            <h:panelGroup id="textLinkCampanha">
                                <h:outputText styleClass="texto-size-14 cinza"
                                              value="Habilitar utilização de Campanha:"> </h:outputText>
                                <i class="fa-icon-question-sign tooltipster"
                                   style="color: #777"
                                   title="Marque esta opção para habilitar a aba de geração de link utilizando Campanha."></i>
                            </h:panelGroup>
                            <h:panelGroup layout="block" id="checkBoxLinkCampanha"
                                          styleClass="chk-fa-container alinhamentoCheckLabel alinhamentoCheckLabelQuebrada">
                                <h:selectBooleanCheckbox
                                        value="#{GestaoVendasOnlineControle.config.habilitarCampanha}">

                                </h:selectBooleanCheckbox>
                                <span/>
                            </h:panelGroup>

                            <h:panelGroup id="textPermitirMudarTipoParcelamento">
                                <h:outputText styleClass="texto-size-14 cinza"
                                              value="Permitir mudar o tipo de parcelamento na venda:"> </h:outputText>
                                <i class="fa-icon-question-sign tooltipster"
                                   style="color: #777"
                                   title="Marque esta opção caso o cliente possa escolher entre comprar recorrente ou parcelar pela operadora."></i>
                            </h:panelGroup>
                            <h:panelGroup layout="block" id="checkboxPermitirMudarTipoParcelamento"
                                          styleClass="chk-fa-container alinhamentoCheckLabel alinhamentoCheckLabelQuebrada">
                                <h:selectBooleanCheckbox
                                        value="#{GestaoVendasOnlineControle.config.permitirMudarTipoParcelamento}"/>
                                <span/>
                            </h:panelGroup>

                            <h:panelGroup id="textApresentarValorTotalPlano">
                                <h:outputText styleClass="texto-size-14 cinza"
                                              value="Apresentar valor total do Plano:"> </h:outputText>
                                <i class="fa-icon-question-sign tooltipster"
                                   style="color: #777"
                                   title="Marque esta opção para exibir o Valor Total do Plano na tela de seleção de plano, incluindo mensalidades, anuidade, adesão e etc."></i>
                            </h:panelGroup>
                            <h:panelGroup layout="block" id="checkboxApresentarValorTotalPlano"
                                          styleClass="chk-fa-container alinhamentoCheckLabel alinhamentoCheckLabelQuebrada">
                                <h:selectBooleanCheckbox
                                        value="#{GestaoVendasOnlineControle.config.apresentarValorTotalDoPlanoNaTelaDeSelecaoDoPlano}"/>
                                <span/>
                            </h:panelGroup>

                            <h:panelGroup id="textPermiteVendaContratoParcelaEmAberto" rendered="#{GestaoVendasOnlineControle.exibirConfigContratoConcomitanteComParcelaEmAberto}">
                                <h:outputText styleClass="texto-size-14 cinza"
                                              value="Permitir vender contrato concomitante mesmo com parcela em aberto:"> </h:outputText>
                                <i class="fa-icon-question-sign tooltipster"
                                   style="color: #777"
                                   title="${GestaoVendasOnlineControle.titleContratoConcomitanteParcelaEmAberto}"></i>
                            </h:panelGroup>
                            <h:panelGroup layout="block" id="checkboxPermiteVendaContratoParcelaEmAberto"
                                          rendered="#{GestaoVendasOnlineControle.exibirConfigContratoConcomitanteComParcelaEmAberto}"
                                          styleClass="chk-fa-container alinhamentoCheckLabel alinhamentoCheckLabelQuebrada">
                                <h:selectBooleanCheckbox
                                        value="#{GestaoVendasOnlineControle.config.permiteContratoConcomitanteComParcelaEmAberto}"/>
                                <span/>
                            </h:panelGroup>

                            <h:panelGroup styleClass="alinhamentoLabels">
                                <h:outputText styleClass="texto-size-14 cinza"
                                              value="Campanha padrão gerar cupons para alunos indicados:"/>
                                <i class="fa-icon-question-sign tooltipster"
                                   style="color: #777"
                                   title="Informe a campanha de cupom de desconto que será utilizada para gerar os cupons para os alunos indicados que se cadastraram pelo link de convite."></i>
                            </h:panelGroup>

                            <h:panelGroup layout="block" styleClass="cb-container"
                                          style="width: 90%; font-size: 14px">
                                <h:selectOneMenu
                                        id="campanhaCupomDescontoPadraoIndicacoes"
                                        value="#{GestaoVendasOnlineControle.config.campanhaCupomDescontoIndicacoes.id}">
                                    <f:selectItems
                                            value="#{GestaoVendasOnlineControle.listaSelectItemCampanhaCupomDesconto}"/>
                                </h:selectOneMenu>
                            </h:panelGroup>

                            <h:panelGroup id="textPermiteVendaProdutoUnidDif">
                                <h:outputText styleClass="texto-size-14 cinza"
                                              value="Permitir vender produto(s) para aluno(s) de outra(s) unidade(s):"> </h:outputText>
                                <i class="fa-icon-question-sign tooltipster"
                                   style="color: #777"
                                   title="${GestaoVendasOnlineControle.titleVendaProdutoAlunoOutraUnidade}"></i>
                            </h:panelGroup>
                            <h:panelGroup layout="block" id="checkboxPermiteVendaProdutoUnidDif"
                                          styleClass="chk-fa-container alinhamentoCheckLabel alinhamentoCheckLabelQuebrada">
                                <h:selectBooleanCheckbox
                                        value="#{GestaoVendasOnlineControle.config.permiteVendaProdutoAlunoOutraUnidade}"/>
                                <span/>
                            </h:panelGroup>

                            <h:panelGroup id="textExibirTipoDocumentoTelaVendasOnline">
                                <h:outputText styleClass="texto-size-14 cinza"
                                              value="Exibir Tipo de Documento tela Vendas Online:"> </h:outputText>
                                <i class="fa-icon-question-sign tooltipster"
                                   style="color: #777"
                                   title="${GestaoVendasOnlineControle.titleExibirTipoDocumentoTelaVendasOnline}"></i>
                            </h:panelGroup>
                            <h:panelGroup layout="block" id="checkboxExibirTipoDocumentoTelaVendasOnline"
                                          styleClass="chk-fa-container alinhamentoCheckLabel alinhamentoCheckLabelQuebrada">
                                <h:selectBooleanCheckbox
                                        value="#{GestaoVendasOnlineControle.config.exibirTipoDocumentoTelaVendasOnline}"/>
                                <span/>
                            </h:panelGroup>

                            <h:panelGroup id="enviarEmailUsuarioMovelAut">
                                <h:outputText styleClass="texto-size-14 cinza"
                                              value="Enviar email com usuário do APP Treino automaticamente após a venda com sucesso:"> </h:outputText>
                                <i class="fa-icon-question-sign tooltipster"
                                   style="color: #777"
                                   title="${GestaoVendasOnlineControle.titleExibirEnvioEmailUsuarioMovel}"></i>
                            </h:panelGroup>
                            <h:panelGroup layout="block" id="checkEnviarEmailUsuarioMovelAut"
                                          styleClass="chk-fa-container alinhamentoCheckLabel alinhamentoCheckLabelQuebrada">
                                <h:selectBooleanCheckbox
                                        value="#{GestaoVendasOnlineControle.config.enviarEmailUsuarioMovelAutomaticamente}"/>
                                <span/>
                            </h:panelGroup>

                            <h:panelGroup>
                                <h:outputText id="outputTextModalidadesIniciarSelecionadasContratoTurma"
                                              styleClass="texto-size-14 cinza"
                                              value="Modalidades devem iniciar selecionadas para contratos com Turma:">
                                </h:outputText>
                                <i class="fa-icon-question-sign tooltipster"
                                   style="color: #777"
                                   title="${GestaoVendasOnlineControle.titleModalidadesIniciarSelecionadasContratoTurma}"></i>
                            </h:panelGroup>
                            <h:panelGroup id="checkBoxModalidadesIniciarSelecionadasContratoTurma"
                                          layout="block"
                                          styleClass="chk-fa-container alinhamentoCheckLabel alinhamentoCheckLabelQuebrada">
                                <h:selectBooleanCheckbox
                                        value="#{GestaoVendasOnlineControle.config.modalidadesIniciarSelecionadasContratoTurma}">
                                </h:selectBooleanCheckbox>
                                <span/>
                            </h:panelGroup>

                            <h:panelGroup>
                                <h:outputText id="outputTextAtivarLinksGooglePlayEAppleStore"
                                              styleClass="texto-size-14 cinza"
                                              value="Ativar exibir Links Google Play e Apple Store:">
                                </h:outputText>
                                <i class="fa-icon-question-sign tooltipster"
                                   style="color: #777"
                                   title="${GestaoVendasOnlineControle.titleAtivarLinksGooglePlayEAppleStore}"></i>
                            </h:panelGroup>
                            <h:panelGroup id="checkBoxAtivarLinksGooglePlayEAppleStore"
                                          layout="block"
                                          styleClass="chk-fa-container alinhamentoCheckLabel alinhamentoCheckLabelQuebrada">
                                <h:selectBooleanCheckbox
                                        value="#{GestaoVendasOnlineControle.config.ativarLinksGooglePlayEAppleStore}">
                                        <a4j:support event="onchange" actionListener="#{GestaoVendasOnlineControle.limparCamposUrlLinksGooglePlayEAppleStore()}" reRender="configsV"/>
                                </h:selectBooleanCheckbox>
                                <span/>
                            </h:panelGroup>

                            <h:panelGroup styleClass="alinhamentoLabels" rendered="#{GestaoVendasOnlineControle.config.ativarLinksGooglePlayEAppleStore}">
                                <h:outputText styleClass="texto-size-14 cinza"
                                              value="Url tela Google Play: "></h:outputText>
                                <i class="fa-icon-question-sign tooltipster"
                                   style="color: #777;"
                                   title="Informe a url para o link dá página do Google Play, onde o aluno pode encontrar o App Treino."></i>
                            </h:panelGroup>
                            <h:panelGroup layout="block"
                                          style="font-size: 14px"
                                          rendered="#{GestaoVendasOnlineControle.config.ativarLinksGooglePlayEAppleStore}">
                                <h:inputText
                                        id="inputTextUrlLinkGooglePlay"
                                        value="#{GestaoVendasOnlineControle.config.urlLinkGooglePlay}"
                                        onblur="blurinput(this);" style="width: 90%;"
                                        onfocus="focusinput(this);"
                                        styleClass="form inputTextVendas"/>
                            </h:panelGroup>

                            <h:panelGroup styleClass="alinhamentoLabels" rendered="#{GestaoVendasOnlineControle.config.ativarLinksGooglePlayEAppleStore}">
                                <h:outputText styleClass="texto-size-14 cinza"
                                              value="Url tela Apple Store: "></h:outputText>
                                <i class="fa-icon-question-sign tooltipster"
                                   style="color: #777;"
                                   title="Informe a url para o link dá página da Apple Store, onde o aluno pode encontrar o App Treino."></i>
                            </h:panelGroup>
                            <h:panelGroup layout="block"
                                          style="font-size: 14px"
                                          rendered="#{GestaoVendasOnlineControle.config.ativarLinksGooglePlayEAppleStore}">
                                <h:inputText
                                        id="inputTextUrlLinkAppleStore"
                                        value="#{GestaoVendasOnlineControle.config.urlLinkAppleStore}"
                                        onblur="blurinput(this);" style="width: 90%;"
                                        onfocus="focusinput(this);"
                                        styleClass="form inputTextVendas"/>
                            </h:panelGroup>

                            <h:panelGroup id="habilitarAgendamentoAulaExperimentalLinkVisitante">
                                <h:outputText styleClass="texto-size-14 cinza"
                                              value="Habilitar Agendamento de Aula Experimental pelo Link de Visitante:"> </h:outputText>
                                <i class="fa-icon-question-sign tooltipster"
                                   style="color: #777"
                                   title="${GestaoVendasOnlineControle.titleHabilitarAgendamentoAulaExperimentalLinkVisitanteVendasOnline}"></i>
                            </h:panelGroup>
                            <h:panelGroup layout="block" id="checkBoxAgendamentoAulaExperimentalLinkVisitante"
                                          styleClass="chk-fa-container alinhamentoCheckLabel alinhamentoCheckLabelQuebrada">
                                <h:selectBooleanCheckbox
                                        value="#{GestaoVendasOnlineControle.config.habilitarAgendamentoAulaExperimentalLinkVisitante}">
                                    <a4j:support event="onchange" reRender="configsV"/>
                                </h:selectBooleanCheckbox>
                                <span/>
                            </h:panelGroup>

                            <h:panelGroup id="naoPermitirProsseguirCasoCpfJaExista">
                                <h:outputText styleClass="texto-size-14 cinza"
                                              value="Permite prosseguir com o cadastro de visitante caso o CPF já esteja cadastrado no sistema"> </h:outputText>
                                <i class="fa-icon-question-sign tooltipster"
                                   style="color: #777"
                                   title="${GestaoVendasOnlineControle.titleNaoPodeProsseguirVisitanteMesmoCPF}"></i>
                            </h:panelGroup>
                            <h:panelGroup layout="block" id="checkNaoPermitirProsseguirCasoCpfJaExista"
                                          styleClass="chk-fa-container alinhamentoCheckLabel alinhamentoCheckLabelQuebrada">
                                <h:selectBooleanCheckbox
                                        value="#{GestaoVendasOnlineControle.config.permiteProsseguirMesmoCpfCadastroVisitante}"/>
                                <span/>
                            </h:panelGroup>

                            <h:panelGroup>
                                <h:outputText styleClass="texto-size-14 cinza"
                                              value="Permitir Selecionar a Data de Utilização Diária:"> </h:outputText>
                                <i class="fa-icon-question-sign tooltipster"
                                   style="color: #777"
                                   title="Permite que o aluno selecione a data de utilização da diária para produtos do tipo diária"></i>
                            </h:panelGroup>
                            <h:panelGroup layout="block"
                                          styleClass="chk-fa-container alinhamentoCheckLabel alinhamentoCheckLabelQuebrada">
                                <h:selectBooleanCheckbox
                                        id="exibeDataUtilizacao"
                                        value="#{GestaoVendasOnlineControle.config.exibeDataUtilizacaoDiaria}">
                                </h:selectBooleanCheckbox>
                                <span/>
                            </h:panelGroup>
                        </h:panelGrid>
                    </h:panelGroup>

                    <h:panelGrid width="50%" columns="2"
                                 columnClasses="w50, w50" cellpadding="10"
                                 style="margin-left: 6px" rendered="#{GestaoVendasOnlineControle.abaAvancado and
                                  GestaoVendasOnlineControle.config.habilitarAgendamentoAulaExperimentalLinkVisitante}" id="gridtest">
                    <h:panelGroup layout="block">
                        <h:column>

                            <h:column>
                                <h:selectOneRadio styleClass="texto-size-14 cinza agendaEscolhaProduto"
                                                  value="#{GestaoVendasOnlineControle.agendaLinkVisitanteVendasOnlineVO.tipoAulasLinkVisitante}"
                                                  valueChangeListener="#{GestaoVendasOnlineControle.agendaLinkVisitanteVendasOnlineVO.trocarTipoAulaLinkVisitante}"
                                                  id="select-escolha-tipo-aulas"
                                >
                                    <f:selectItem itemValue="0" itemLabel="Todas as aulas"/>
                                    <f:selectItem itemValue="1" itemLabel="Aulas específicas"/>
                                    <a4j:support event="onchange" reRender="configsV"/>
                                </h:selectOneRadio>
                            </h:column>
                            <c:if test="${GestaoVendasOnlineControle.agendaLinkVisitanteVendasOnlineVO.tipoAulasLinkVisitante == 1}">

                                <h:selectOneMenu
                                        value="#{GestaoVendasOnlineControle.agendaLinkVisitanteVendasOnlineVO.aulaLinkVisitanteEscolhida}"
                                        styleClass="testDropAulaAgenda form inputTextVendas"
                                        style="width: 100%;margin-top: 3px"
                                        id="select-aulas-link"
                                >
                                    <f:selectItem itemLabel="Selecione uma aula!"/>
                                    <f:selectItems
                                            value="#{GestaoVendasOnlineControle.agendaLinkVisitanteVendasOnlineVO.aulasLinkVisitante}"/>
                                </h:selectOneMenu>
                                <h:panelGroup layout="block" style="width: 100%; text-align: center">
                                <a4j:commandButton value="Adicionar"
                                                   styleClass="botoes nvoBt btSec"
                                                   reRender="configsV"
                                                   action="#{GestaoVendasOnlineControle.adicionarAulaLinkVisitante}"
                                                   oncomplete="#{GestaoVendasOnlineControle.mensagemNotificar}">
                                </a4j:commandButton>
                                </h:panelGroup>
                            </c:if>
                        </h:column>

                        <c:if test="${GestaoVendasOnlineControle.agendaLinkVisitanteVendasOnlineVO.tipoAulasLinkVisitante == 1}">
                            <h:dataTable styleClass="tableAgendaItensLinkVisitante"
                                         style="text-align: center"
                                         id="agendaVendasOnlineitemsLink" width="100%" headerClass="consulta"
                                         rowClasses="linhaImpar, linhaPar" columnClasses="colunaAlinhamento"
                                         value="#{GestaoVendasOnlineControle.agendaLinkVisitanteVendasOnlineVO.aulasVendasOnlineLinkVisitante}"
                                         rows="10" var="aulaVendasOnlineLinkVisitante">

                                <h:column>
                                    <f:facet name="header">
                                        <h:outputText value="Aula"/>
                                    </f:facet>
                                    <h:outputText id="tableTurma" value="#{aulaVendasOnlineLinkVisitante.turma}"/>
                                </h:column>
                                <h:column>
                                    <f:facet name="header">
                                        <h:outputText value="Modalidade"/>
                                    </f:facet>
                                    <h:outputText id="tableModalidade"
                                                  value="#{aulaVendasOnlineLinkVisitante.modalidade}"/>
                                </h:column>

                                <h:column>
                                    <f:facet name="header">
                                        <h:outputText value="#{msg_bt.btn_opcoes}" styleClass="acoes"/>
                                    </f:facet>
                                    <a4j:commandLink id="comandoExcluirAulaLink"
                                                     reRender="select-aulas-link, configsV"
                                                     title="Excluir Aula"
                                                     action="#{GestaoVendasOnlineControle.excluirAulaLinkVisitante}"
                                                     oncomplete="#{GestaoVendasOnlineControle.mensagemNotificar}"
                                                     styleClass="testeExcluirAula botoes">
                                        <i class="fa-icon-trash"></i>
                                    </a4j:commandLink>
                                </h:column>
                            </h:dataTable>

                            <rich:datascroller align="center" for="form:agendaVendasOnlineitemsLink" maxPages="10"
                                               id="scResultadoLogLink"/>

                        </c:if>
                        </h:panelGroup>
                    </h:panelGrid>

                    <h:panelGroup layout="block" id="divAbaConvenioCobranca"
                                  styleClass="divAbaConvenio"
                                  rendered="#{GestaoVendasOnlineControle.abaConvenioCobranca}">

                        <h:panelGroup layout="block" id="configConvLeft"
                                      style="display: block">

                            <h:panelGrid width="100%" columns="2" columnClasses="w40, w60"
                                         cellpadding="10">

                                <h:panelGroup styleClass="alinhamentoLabels">
                                    <h:outputText styleClass="texto-size-14 cinza"
                                                  value="Empresa: "/>
                                </h:panelGroup>

                                <h:panelGroup layout="block" styleClass="alinhamentoLabels">
                                    <h:outputText
                                            id="nomeEmpresaConfig"
                                            styleClass="texto-size-14 nomeEmpresaConfig"
                                            value="#{GestaoVendasOnlineControle.empresaVO.nome}"/>
                                </h:panelGroup>

                                <h:panelGroup styleClass="alinhamentoLabels">
                                    <h:outputText styleClass="texto-size-14 cinza"
                                                  value="PIX: "/>
                                    <i class="fa-icon-question-sign tooltipster"
                                       style="color: #777"
                                       title="Convênio de cobrança de PIX que será usado nas cobranças das vendas via site"></i>
                                </h:panelGroup>

                                <h:panelGroup layout="block" styleClass="cb-container"
                                              style="width: 90%; font-size: 14px">
                                    <h:selectOneMenu
                                            id="cbConvenioCobrancaPixSite"
                                            value="#{GestaoVendasOnlineControle.config.convenioCobrancaPixVO.codigo}">
                                        <f:selectItems
                                                value="#{GestaoVendasOnlineControle.conveniosPix}"/>
                                        <a4j:support event="onchange" reRender="configConvLeft"/>
                                    </h:selectOneMenu>
                                </h:panelGroup>

                                <c:if test="${GestaoVendasOnlineControle.config.convenioCobrancaPixVO.codigo > 0}">
                                    <h:panelGroup>
                                        <h:outputText styleClass="texto-size-14 cinza"
                                                      value="Incluir categoria pagamento PIX:"></h:outputText>
                                        <i class="fa-icon-question-sign tooltipster"
                                           style="color: #777"
                                           title="Ao marcar esta configuração, quando o cliente realizar uma compra pelo vendas online
                                           pagando com PIX será incluido/alterado a categoria do cliente para (VENDAS ONLINE - PIX)."></i>
                                    </h:panelGroup>
                                    <h:panelGroup layout="block"
                                                  styleClass="chk-fa-container alinhamentoCheckLabel alinhamentoCheckLabelQuebrada">
                                        <h:selectBooleanCheckbox id="incluirCategoriaPix"
                                                                 value="#{GestaoVendasOnlineControle.config.incluirCategoriaPix}"/>
                                        <span/>
                                    </h:panelGroup>
                                </c:if>

                                <h:panelGroup styleClass="alinhamentoLabels">
                                    <h:outputText styleClass="texto-size-14 cinza"
                                                  value="Boleto: "/>
                                    <i class="fa-icon-question-sign tooltipster"
                                       style="color: #777"
                                       title="Convênio de cobrança de boleto que será usado nas cobranças das vendas via site"></i>
                                </h:panelGroup>

                                <h:panelGroup layout="block" styleClass="cb-container"
                                              style="width: 90%; font-size: 14px">
                                    <h:selectOneMenu
                                            id="cbConvenioCobrancaBoletoSite"
                                            value="#{GestaoVendasOnlineControle.config.convenioCobrancaBoletoVO.codigo}">
                                        <f:selectItems value="#{GestaoVendasOnlineControle.conveniosBoleto}"/>
                                        <a4j:support event="onchange" reRender="configConvLeft"/>
                                    </h:selectOneMenu>
                                </h:panelGroup>

                                <c:if test="${GestaoVendasOnlineControle.config.convenioCobrancaBoletoVO.codigo > 0}">
                                    <h:panelGroup>
                                        <h:outputText styleClass="texto-size-14 cinza"
                                                      value="Gerar boleto de todas as parcelas:"></h:outputText>
                                        <i class="fa-icon-question-sign tooltipster"
                                           style="color: #777"
                                           title="Ao marcar esta configuração, será gerado o boleto de todas as parcelas que foram geradas na venda.</br>
                                            Caso esteja desmarcada, será gerado o boleto somente da primeira parcela."></i>
                                    </h:panelGroup>
                                    <h:panelGroup layout="block"
                                                  styleClass="chk-fa-container alinhamentoCheckLabel alinhamentoCheckLabelQuebrada">
                                        <h:selectBooleanCheckbox id="gerarBoletoTodasParcelas"
                                                                 value="#{GestaoVendasOnlineControle.config.gerarBoletoTodasParcelas}"/>
                                        <span/>
                                    </h:panelGroup>

                                    <h:panelGroup>
                                        <h:outputText styleClass="texto-size-14 cinza"
                                                      value="Incluir autorização de cobrança de boleto:"></h:outputText>
                                        <i class="fa-icon-question-sign tooltipster"
                                           style="color: #777"
                                           title="Ao marcar esta configuração, ao realizar a compra utilizando boleto será incluido uma autorização de cobrança de boleto para o cliente."></i>
                                    </h:panelGroup>
                                    <h:panelGroup layout="block"
                                                  styleClass="chk-fa-container alinhamentoCheckLabel alinhamentoCheckLabelQuebrada">
                                        <h:selectBooleanCheckbox id="criarAutorizacaoCobrancaBoleto"
                                                                 value="#{GestaoVendasOnlineControle.config.criarAutorizacaoCobrancaBoleto}"/>
                                        <span/>
                                    </h:panelGroup>

                                    <h:panelGroup>
                                        <h:outputText styleClass="texto-size-14 cinza"
                                                      value="Dias para pagamento do primeiro boleto:"></h:outputText>
                                        <i class="fa-icon-question-sign tooltipster"
                                           style="color: #777"
                                           title="Informe a quantidade de dias que será acrescentado no primeiro boleto (apenas o vencimento do primeiro boleto terá acréscimo desses dias para poder realizar o pagamento."></i>
                                    </h:panelGroup>
                                    <h:panelGroup layout="block"
                                                  styleClass="chk-fa-container alinhamentoCheckLabel alinhamentoCheckLabelQuebrada">
                                        <h:inputText  id="diasVencimentoBoleto"
                                                      size="2"
                                                      maxlength="2"
                                                      styleClass="form inputTextVendas"
                                                      value="#{GestaoVendasOnlineControle.config.diasVencimentoBoleto}"
                                                      onblur="blurinput(this);"
                                                      onfocus="focusinput(this);"
                                                      onkeypress="return mascara(this.form, this.id, '99999999', event);"
                                                      onkeyup="somenteNumeros(this);"/>
                                        <span/>
                                    </h:panelGroup>
                                </c:if>

                                <c:if test="${fn:length(GestaoVendasOnlineControle.listaConvenioCobrancaVO) > 1}">
                                    <h:panelGroup>
                                        <h:outputText styleClass="texto-size-14 cinza"
                                                      value="Utilizar retentativa automática imediata de cobrança:"> </h:outputText>
                                        <i class="fa-icon-question-sign tooltipster"
                                           style="color: #777"
                                           title="Ao marcar esta configuração, será possível definir mais de um convênio para que, caso o sistema, ao tentar efetuar a cobrança da compra do aluno no primeiro
                                                                        convênio e este não for efetivado com sucesso, então automaticaticamente e imediatamente em seguida tentará nos demais convênios configurados na lista, de acordo
                                                                        com a posição/ordem definida. Após marcar, selecione o convênio que quer adicionar logo no campo abaixo e depois clique no botão 'Adicionar Convênio'.</br>
                                                                        <b>Obs:</b> Esta configuração só é exibida aqui se houver mais de um convênio de cobrança ativo."></i>
                                    </h:panelGroup>
                                    <h:panelGroup layout="block"
                                                  styleClass="chk-fa-container alinhamentoCheckLabel alinhamentoCheckLabelQuebrada">
                                        <h:selectBooleanCheckbox
                                                id="cbUtilizarRetentativa"
                                                value="#{GestaoVendasOnlineControle.utilizarRetentativaConvenio}">
                                            <a4j:support event="onchange"
                                                         action="#{GestaoVendasOnlineControle.acaoUtilizarRetentativaConvenio}"
                                                         reRender="vendasOnlinePanel"/>
                                        </h:selectBooleanCheckbox>
                                        <span/>
                                    </h:panelGroup>
                                </c:if>


                                <h:panelGroup styleClass="alinhamentoLabels">
                                    <h:outputText styleClass="texto-size-14 cinza"
                                                  value="Cartão de Crédito: "/>
                                    <i class="fa-icon-question-sign tooltipster"
                                       style="color: #777"
                                       title="Convênio de cobrança online que será usado nas cobranças das vendas via site"></i>
                                </h:panelGroup>

                                <h:panelGroup layout="block" styleClass="cb-container"
                                              style="width: 90%; font-size: 14px">
                                    <h:selectOneMenu
                                            id="cbConvenioCobrancaSite"
                                            value="#{GestaoVendasOnlineControle.vendasOnlineConvenioTentativaVO.convenioCobrancaVO.codigo}">
                                        <a4j:support event="onchange"
                                                     reRender="vendasOnlinePanel"
                                                     action="#{GestaoVendasOnlineControle.validarApresentacaoTipoParcelamentoStone}"/>
                                        <f:selectItems
                                                value="#{GestaoVendasOnlineControle.listaSelecItemConvenioTentativa}"/>
                                    </h:selectOneMenu>
                                </h:panelGroup>

                                <c:if test="${GestaoVendasOnlineControle.apresentarTipoParcelamentoStone}">
                                    <h:panelGroup styleClass="alinhamentoLabels">
                                        <h:outputText styleClass="texto-size-14 cinza"
                                                      value="#{msg_aplic.prt_tipoParcelamento}: "></h:outputText>
                                        <i class="fa-icon-question-sign tooltipster"
                                           style="color: #777"
                                           title="${GestaoVendasOnlineControle.titleTipoParcelamentoStone}"></i>
                                    </h:panelGroup>

                                    <h:panelGroup layout="block" styleClass="cb-container"
                                                  style="width: 90%; font-size: 14px">
                                        <h:selectOneMenu
                                                value="#{GestaoVendasOnlineControle.vendasOnlineConvenioTentativaVO.tipoParcelamentoStone}">
                                            <f:selectItem itemLabel=""/>
                                            <f:selectItem itemLabel="Lojista - Sem juros | MCHT | (lojista paga os juros)"
                                                          itemValue="MCHT"/>
                                            <f:selectItem itemLabel="Emissor - Com juros | ISSR | (portador do cartão paga os juros)"
                                                          itemValue="ISSR"/>
                                        </h:selectOneMenu>
                                    </h:panelGroup>
                                </c:if>

                                <c:if test="${GestaoVendasOnlineControle.utilizarRetentativaConvenio}">
                                    <h:panelGroup styleClass="alinhamentoLabels">
                                        <h:outputText styleClass="texto-size-14 cinza"
                                                      value=""/>
                                    </h:panelGroup>

                                    <h:panelGroup layout="block"
                                                  style="width: 90%; font-size: 14px">
                                        <a4j:commandButton value="Adicionar Convênio"
                                                           styleClass="botoes nvoBt btSec"
                                                           reRender="form:divAbaConvenioCobranca"
                                                           action="#{GestaoVendasOnlineControle.adicionarConvenio}"
                                                           oncomplete="#{GestaoVendasOnlineControle.mensagemNotificar}">
                                        </a4j:commandButton>
                                    </h:panelGroup>
                                </c:if>

                                <c:if test="${GestaoVendasOnlineControle.config.convenioCobrancaPixVO.codigo > 0 && GestaoVendasOnlineControle.vendasOnlineConvenioTentativaVO.convenioCobrancaVO.codigo > 0}">
                                    <h:panelGroup>
                                        <h:outputText styleClass="texto-size-14 cinza"
                                                      value="Cadastrar cartão previamente para pagamento da primeira parcela no PIX:"></h:outputText>
                                        <i class="fa-icon-question-sign tooltipster"
                                           style="color: #777"
                                           title="Ao marcar esta configuração, quando o cliente realizar uma compra pelo Vendas Online,
                                           o sistema irá guardar os dados do cartão para as próximas cobranças na Recorrência e efetuar a geração do Pix para a primeira cobrança.</br>
                                           <b>Obs:</b> Não foi desenvolvido para funcionar com Convênio especifico de Plano ou Produto, apenas para o Geral.</br>
                                           Se o mesmo for preenchido, ao clicar no Gravar, as configurações de Convênio especifico de Plano ou Produto serão apagadas."></i>
                                    </h:panelGroup>
                                    <h:panelGroup layout="block"
                                                  styleClass="chk-fa-container alinhamentoCheckLabel alinhamentoCheckLabelQuebrada">
                                        <h:selectBooleanCheckbox id="selectPrimeiraCobrancaPixEGuardarCartao"
                                                                 value="#{GestaoVendasOnlineControle.config.primeiraCobrancaPixEGuardarCartao}"/>
                                        <span/>
                                    </h:panelGroup>
                                </c:if>

                            </h:panelGrid>

                            <h:panelGroup layout="block" id="divConveniosVendas"
                                          rendered="#{GestaoVendasOnlineControle.utilizarRetentativaConvenio}"
                                          styleClass="divConvenioPlanos">
                                <rich:dataTable
                                        rendered="#{fn:length(GestaoVendasOnlineControle.listaVendasOnlineConvenioTentativa) > 0}"
                                        styleClass="tabelaDados semZebra"
                                        style="margin: 20px 0 20px 0; width: 100%"
                                        value="#{GestaoVendasOnlineControle.listaVendasOnlineConvenioTentativa}"
                                        var="conv" rowKeyVar="status">

                                    <rich:column
                                            sortBy="#{conv.ordem}">
                                        <f:facet name="header">
                                            <h:outputText value="Posição"/>
                                        </f:facet>
                                        <h:outputText
                                                style="margin-left: 20px"
                                                styleClass="fontListaNotaFiscal"
                                                value="#{conv.ordem}"/>
                                    </rich:column>

                                    <rich:column
                                            sortBy="#{conv.convenioCobrancaVO.descricao}">
                                        <f:facet name="header">
                                            <h:outputText value="Convênio Cobrança"/>
                                        </f:facet>
                                        <h:outputText
                                                styleClass="fontListaNotaFiscal"
                                                value="#{conv.convenioCobrancaApresentar}"/>
                                    </rich:column>

                                    <rich:column
                                            headerClass="col-text-align-center">
                                        <f:facet name="header">
                                            <h:outputText value="Opções"/>
                                        </f:facet>
                                        <h:panelGroup layout="block"
                                                      style="text-align: center; display: flex">

                                            <a4j:commandLink
                                                    action="#{GestaoVendasOnlineControle.moverParaCimaConvenio}"
                                                    oncomplete="#{GestaoVendasOnlineControle.mensagemNotificar}"
                                                    reRender="form:divAbaConvenioCobranca"
                                                    title="Mover para cima"
                                                    styleClass="tooltipster">
                                                <i class="fa fa-icon-circle-arrow-up"
                                                   style="font-size: 17px"></i>
                                            </a4j:commandLink>

                                            <a4j:commandLink
                                                    action="#{GestaoVendasOnlineControle.moverParaBaixoConvenio}"
                                                    oncomplete="#{GestaoVendasOnlineControle.mensagemNotificar}"
                                                    reRender="form:divAbaConvenioCobranca"
                                                    title="Mover para baixo"
                                                    styleClass="tooltipster">
                                                <i class="fa fa-icon-circle-arrow-down"
                                                   style="font-size: 17px"></i>
                                            </a4j:commandLink>

                                            <a4j:commandLink
                                                    action="#{GestaoVendasOnlineControle.removerConvenio}"
                                                    reRender="form:divAbaConvenioCobranca"
                                                    oncomplete="#{GestaoVendasOnlineControle.mensagemNotificar}"
                                                    title="Excluir"
                                                    styleClass="tooltipster">
                                                <i class="fa fa-icon-minus-sign"
                                                   style="font-size: 17px"></i>
                                            </a4j:commandLink>
                                        </h:panelGroup>
                                    </rich:column>
                                </rich:dataTable>
                            </h:panelGroup>
                        </h:panelGroup>


                        <h:panelGroup layout="block" id="panelConvenioPlanoProduto"
                                      style="display: block; border: 1px solid #E5E5E5; padding-left: 30px; padding-right: 30px">


                            <h:panelGroup layout="block"
                                          style="display: flex; align-items: center">

                                <h:panelGroup styleClass="alinhamentoLabels">
                                    <h:outputText styleClass="texto-size-14 cinza"
                                                  style="font-size: 16px !important;"
                                                  value="Convênio de cobrança por plano ou produto:"> </h:outputText>
                                    <i class="fa-icon-question-sign tooltipster"
                                       style="color: #777; font-size: 16px !important;"
                                       title="Ao marcar esta configuração, é possível determinar qual convênio de cobrança que será utilizado para cada plano ou produto específico.<br/>
                            Caso a venda tenha um plano e um produto de convênios distintos será utilizado o convênio padrão."></i>
                                </h:panelGroup>

                                <h:panelGroup layout="block"
                                              styleClass="chk-fa-container alinhamentoCheckLabel">
                                    <h:selectBooleanCheckbox
                                            id="cbConvenioPlanoProduto"
                                            value="#{GestaoVendasOnlineControle.config.usarConvenioPlanoProduto}">
                                        <a4j:support event="onclick"
                                                     reRender="vendasOnlinePanel"/>
                                    </h:selectBooleanCheckbox>
                                    <span/>
                                </h:panelGroup>
                            </h:panelGroup>

                            <c:if test="${GestaoVendasOnlineControle.config.usarConvenioPlanoProduto}">

                                <h:panelGrid width="100%"
                                             style="margin-top: 15px; border-top: 1px solid #E5E5E5;"
                                             columns="2" columnClasses="w40, w60"
                                             cellpadding="10">

                                    <h:panelGroup styleClass="alinhamentoLabels">
                                        <h:outputText styleClass="texto-size-14 cinza"
                                                      value="Convênio de Cobrança:"/>
                                    </h:panelGroup>
                                    <h:panelGroup layout="block" styleClass="cb-container"
                                                  style="width: 90%; font-size: 14px">
                                        <h:selectOneMenu
                                                id="cbConvenioCobrancaPlanoProduto"
                                                value="#{GestaoVendasOnlineControle.vendasOnlineConvenioVO.convenioCobrancaVO.codigo}">
                                            <f:selectItems
                                                    value="#{GestaoVendasOnlineControle.convenios}"/>
                                            <a4j:support event="onchange"
                                                         reRender="configConvLeft,panelConvenioPlanoProduto"/>
                                        </h:selectOneMenu>
                                    </h:panelGroup>

                                    <h:panelGroup styleClass="alinhamentoLabels"
                                                  rendered="#{GestaoVendasOnlineControle.apresentacaoTipoParcelamentoStoneVendasOnlineConvenio}">
                                        <h:outputText styleClass="texto-size-14 cinza"
                                                      value="#{msg_aplic.prt_tipoParcelamento}:"/>
                                        <i class="fa-icon-question-sign tooltipster"
                                           style="color: #777"
                                           title="Para vendas parceladas, quem irá pagar os juros?"></i>
                                    </h:panelGroup>
                                    <h:panelGroup layout="block" styleClass="cb-container"
                                                  rendered="#{GestaoVendasOnlineControle.apresentacaoTipoParcelamentoStoneVendasOnlineConvenio}"
                                                  style="width: 90%; font-size: 14px">
                                        <h:selectOneMenu
                                                value="#{GestaoVendasOnlineControle.vendasOnlineConvenioVO.tipoParcelamentoStone}">
                                            <f:selectItem itemValue="" itemLabel=""/>
                                            <f:selectItem
                                                    itemLabel="#{msg_aplic.prt_tipo_parcelamento_stone_lojista}"
                                                    itemValue="MCHT"/>
                                            <f:selectItem
                                                    itemLabel="#{msg_aplic.prt_tipo_parcelamento_stone_emissor}"
                                                    itemValue="ISSR"/>
                                        </h:selectOneMenu>
                                    </h:panelGroup>

                                    <h:panelGroup styleClass="alinhamentoLabels">
                                        <h:outputText styleClass="texto-size-14 cinza"
                                                      value="Plano:"/>
                                    </h:panelGroup>
                                    <h:panelGroup layout="block" styleClass="cb-container"
                                                  style="width: 90%; font-size: 14px">
                                        <h:selectOneMenu
                                                id="cbPlano"
                                                value="#{GestaoVendasOnlineControle.vendasOnlineConvenioVO.planoVO.codigo}">
                                            <f:selectItems
                                                    value="#{GestaoVendasOnlineControle.listaSelectItemPlano}"/>
                                            <a4j:support event="onchange"
                                                         reRender="configConvLeft"
                                                         action="#{GestaoVendasOnlineControle.selecionouPlano}"/>
                                        </h:selectOneMenu>
                                    </h:panelGroup>

                                    <h:panelGroup styleClass="alinhamentoLabels">
                                        <h:outputText styleClass="texto-size-14 cinza"
                                                      value="Produto:"/>
                                    </h:panelGroup>
                                    <h:panelGroup layout="block" styleClass="cb-container"
                                                  style="width: 90%; font-size: 14px">
                                        <h:selectOneMenu
                                                id="cbProduto"
                                                value="#{GestaoVendasOnlineControle.vendasOnlineConvenioVO.produtoVO.codigo}">
                                            <f:selectItems
                                                    value="#{GestaoVendasOnlineControle.listaSelectItemProduto}"/>
                                            <a4j:support event="onchange"
                                                         reRender="configConvLeft"
                                                         action="#{GestaoVendasOnlineControle.selecionouProduto}"/>
                                        </h:selectOneMenu>
                                    </h:panelGroup>


                                    <h:panelGroup styleClass="alinhamentoLabels">
                                        <h:outputText styleClass="texto-size-14 cinza"
                                                      value=""/>
                                    </h:panelGroup>
                                    <h:panelGroup layout="block">
                                        <a4j:commandButton id="btnAddConvenioPlanoProduto"
                                                           reRender="divAbaConvenioCobranca"
                                                           oncomplete="#{GestaoVendasOnlineControle.mensagemNotificar};#{GestaoVendasOnlineControle.onComplete}"
                                                           action="#{GestaoVendasOnlineControle.adicionarVendasOnlineConvenio}"
                                                           value="Adicionar"
                                                           styleClass="botoes nvoBt btSec">
                                        </a4j:commandButton>
                                    </h:panelGroup>
                                </h:panelGrid>
                            </c:if>

                            <c:if test="${GestaoVendasOnlineControle.config.usarConvenioPlanoProduto}">

                                <h:panelGroup layout="block"
                                              styleClass="divConfigProdutoPlano"
                                              rendered="#{not empty GestaoVendasOnlineControle.listaConfigPlanos}">

                                    <h:panelGroup layout="block" style="display: flex;">
                                        <h:outputText
                                                styleClass="texto-size-14 nomeConvenioConfig"
                                                value="Planos:"/>
                                    </h:panelGroup>

                                    <h:panelGroup layout="block"
                                                  styleClass="divConvenioPlanoProduto">
                                        <h:panelGroup layout="block"
                                                      styleClass="divConvenioPlanos">
                                            <rich:dataTable
                                                    styleClass="tabelaDados semZebra"
                                                    style="margin: 20px 0 20px 0; width: 100%"
                                                    value="#{GestaoVendasOnlineControle.listaConfigPlanos}"
                                                    var="itemPlano" rowKeyVar="status">

                                                <rich:column
                                                        sortBy="#{itemPlano.planoVO.descricao}">
                                                    <f:facet name="header">
                                                        <h:outputText value="Plano"/>
                                                    </f:facet>
                                                    <h:outputText
                                                            styleClass="fontListaNotaFiscal"
                                                            value="#{itemPlano.planoVO.codigo} - #{itemPlano.planoVO.descricao}"/>
                                                </rich:column>

                                                <rich:column
                                                        sortBy="#{itemPlano.convenioCobrancaVO.descricao}">
                                                    <f:facet name="header">
                                                        <h:outputText
                                                                value="Convênio de Cobrança"/>
                                                    </f:facet>
                                                    <h:outputText
                                                            styleClass="fontListaNotaFiscal"
                                                            value="#{itemPlano.convenioCobrancaVO.descricao}"/>
                                                </rich:column>

                                                <rich:column
                                                        headerClass="col-text-align-center">
                                                    <f:facet name="header">
                                                        <h:outputText value="Excluir"/>
                                                    </f:facet>
                                                    <h:panelGroup layout="block"
                                                                  style="text-align: center">
                                                        <a4j:commandLink
                                                                reRender="divAbaConvenioCobranca"
                                                                oncomplete="#{GestaoVendasOnlineControle.mensagemNotificar};#{GestaoVendasOnlineControle.onComplete}"
                                                                action="#{GestaoVendasOnlineControle.excluirConvenioCobrancaPlano}"
                                                                title="Excluir"
                                                                styleClass="tooltipster">
                                                            <i class="fa fa-icon-minus-sign"
                                                               style="font-size: 17px"></i>
                                                        </a4j:commandLink>
                                                    </h:panelGroup>
                                                </rich:column>
                                            </rich:dataTable>
                                        </h:panelGroup>
                                    </h:panelGroup>
                                </h:panelGroup>


                                <h:panelGroup layout="block"
                                              styleClass="divConfigProdutoPlano"
                                              rendered="#{not empty GestaoVendasOnlineControle.listaConfigProdutos}">

                                    <h:panelGroup layout="block" style="display: flex;">
                                        <h:outputText
                                                styleClass="texto-size-14 nomeConvenioConfig"
                                                value="Produtos:"/>
                                    </h:panelGroup>

                                    <h:panelGroup layout="block"
                                                  styleClass="divConvenioPlanoProduto">
                                        <h:panelGroup layout="block"
                                                      styleClass="divConvenioProdutos">
                                            <rich:dataTable
                                                    styleClass="tabelaDados semZebra"
                                                    style="margin: 20px 0 20px 0; width: 100%"
                                                    value="#{GestaoVendasOnlineControle.listaConfigProdutos}"
                                                    var="itemProduto"
                                                    rowKeyVar="status">

                                                <rich:column
                                                        sortBy="#{itemProduto.produtoVO.descricao}">
                                                    <f:facet name="header">
                                                        <h:outputText value="Produto"/>
                                                    </f:facet>
                                                    <h:outputText
                                                            styleClass="fontListaNotaFiscal"
                                                            value="#{itemProduto.produtoVO.codigo} - #{itemProduto.produtoVO.descricao}"/>
                                                </rich:column>

                                                <rich:column
                                                        sortBy="#{itemProduto.convenioCobrancaVO.descricao}">
                                                    <f:facet name="header">
                                                        <h:outputText
                                                                value="Convênio de Cobrança"/>
                                                    </f:facet>
                                                    <h:outputText
                                                            styleClass="fontListaNotaFiscal"
                                                            value="#{itemProduto.convenioCobrancaVO.descricao}"/>
                                                </rich:column>

                                                <rich:column
                                                        headerClass="col-text-align-center">
                                                    <f:facet name="header">
                                                        <h:outputText value="Excluir"/>
                                                    </f:facet>
                                                    <h:panelGroup layout="block"
                                                                  style="text-align: center">
                                                        <a4j:commandLink
                                                                reRender="divAbaConvenioCobranca"
                                                                oncomplete="#{GestaoVendasOnlineControle.mensagemNotificar};#{GestaoVendasOnlineControle.onComplete}"
                                                                action="#{GestaoVendasOnlineControle.excluirConvenioCobrancaProduto}"
                                                                title="Excluir"
                                                                styleClass="tooltipster">
                                                            <i class="fa fa-icon-minus-sign"
                                                               style="font-size: 17px"></i>
                                                        </a4j:commandLink>
                                                    </h:panelGroup>
                                                </rich:column>
                                            </rich:dataTable>
                                        </h:panelGroup>
                                    </h:panelGroup>
                                </h:panelGroup>
                            </c:if>
                        </h:panelGroup>

                        <h:panelGroup layout="block" id="configConvLeft2"
                                      style="display: block">

                            <h:panelGrid width="100%" columns="2" columnClasses="w40, w60"
                                         cellpadding="10">
                            </h:panelGrid>
                        </h:panelGroup>
                    </h:panelGroup>
                    <h:panelGroup layout="block" id="divAbaFormaPagamento"
                                  styleClass="divAbaConvenio"
                                  rendered="#{GestaoVendasOnlineControle.abaFormaPagamento}">
                        <h:panelGroup layout="block" id="panelFormaPagamentoPlanoProduto"
                                      style="min-height: 225px; display: block; border: 1px solid #E5E5E5; padding-left: 30px; padding-right: 30px">


                            <h:panelGroup layout="block"
                                          style="display: flex; align-items: center">

                                <h:panelGroup styleClass="alinhamentoLabels">
                                    <h:outputText styleClass="texto-size-14 cinza"
                                                  style="font-size: 16px !important;"
                                                  value="Forma de Pagamento por plano ou produto:"> </h:outputText>
                                    <i class="fa-icon-question-sign tooltipster"
                                       style="color: #777; font-size: 16px !important;"
                                       title="Ao marcar esta configuração, é possível determinar qual forma de pagamento que será utilizado para cada plano ou produto específico.<br/>
                            Caso a venda tenha um plano e um produto de convênios distintos será retornado um erro informando para selecionar apenas um dos itens."></i>
                                </h:panelGroup>

                                <h:panelGroup layout="block"
                                              styleClass="chk-fa-container alinhamentoCheckLabel">
                                    <h:selectBooleanCheckbox
                                            id="cbFormaPagamentoPlanoProduto"
                                            value="#{GestaoVendasOnlineControle.config.usarFormaPagamentoPlanoProduto}">
                                        <a4j:support event="onclick"
                                                     reRender="vendasOnlinePanel"/>
                                    </h:selectBooleanCheckbox>
                                    <span/>
                                </h:panelGroup>
                            </h:panelGroup>

                            <c:if test="${GestaoVendasOnlineControle.config.usarFormaPagamentoPlanoProduto}">

                                <h:panelGrid width="100%"
                                             style="margin-top: 15px; border-top: 1px solid #E5E5E5;"
                                             columns="2" columnClasses="w40, w60"
                                             cellpadding="10">

                                    <h:panelGroup styleClass="alinhamentoLabels">
                                        <h:outputText styleClass="texto-size-14 cinza"
                                                      value="Forma de pagamento:"/>
                                    </h:panelGroup>
                                    <h:panelGroup layout="block" styleClass="cb-container"
                                                  style="width: 90%; font-size: 14px">
                                        <h:selectOneMenu
                                                id="cbFormaPagamentoPlanoProduto2"
                                                value="#{GestaoVendasOnlineControle.vendasOnlineFormPagVO.formaPagamento}">
                                            <f:selectItem itemValue="0" itemLabel=""/>
                                            <f:selectItem itemValue="1" itemLabel="Pix"/>
                                            <f:selectItem itemValue="2" itemLabel="Boleto"/>
                                            <f:selectItem itemValue="3" itemLabel="Cartão de crédito"/>
                                            <a4j:support event="onchange"
                                                         reRender="configConvLeft,panelFormaPagamentoPlanoProduto"/>
                                        </h:selectOneMenu>
                                    </h:panelGroup>

                                    <h:panelGroup styleClass="alinhamentoLabels">
                                        <h:outputText styleClass="texto-size-14 cinza"
                                                      value="Plano:"/>
                                    </h:panelGroup>
                                    <h:panelGroup layout="block" styleClass="cb-container"
                                                  style="width: 90%; font-size: 14px">
                                        <h:selectOneMenu
                                                id="cbfPlano"
                                                value="#{GestaoVendasOnlineControle.vendasOnlineFormPagVO.planoVO.codigo}">
                                            <f:selectItems
                                                    value="#{GestaoVendasOnlineControle.listaSelectItemPlanoFormPag}"/>
                                            <a4j:support event="onchange"
                                                         reRender="configConvLeft"
                                                         action="#{GestaoVendasOnlineControle.selecionouPlanoFormpag}"/>
                                        </h:selectOneMenu>
                                    </h:panelGroup>

                                    <h:panelGroup styleClass="alinhamentoLabels">
                                        <h:outputText styleClass="texto-size-14 cinza"
                                                      value="Produto:"/>
                                    </h:panelGroup>
                                    <h:panelGroup layout="block" styleClass="cb-container"
                                                  style="width: 90%; font-size: 14px">
                                        <h:selectOneMenu
                                                id="cbfProduto"
                                                value="#{GestaoVendasOnlineControle.vendasOnlineFormPagVO.produtoVO.codigo}">
                                            <f:selectItems
                                                    value="#{GestaoVendasOnlineControle.listaSelectItemProdutoFormPag}"/>
                                            <a4j:support event="onchange"
                                                         reRender="configConvLeft"
                                                         action="#{GestaoVendasOnlineControle.selecionouProdutoFormPag}"/>
                                        </h:selectOneMenu>
                                    </h:panelGroup>


                                    <h:panelGroup styleClass="alinhamentoLabels">
                                        <h:outputText styleClass="texto-size-14 cinza"
                                                      value=""/>
                                    </h:panelGroup>
                                    <h:panelGroup layout="block">
                                        <a4j:commandButton id="btnAddFormaPagamentoPlanoProduto"
                                                           reRender="divAbaFormaPagamento"
                                                           oncomplete="#{GestaoVendasOnlineControle.mensagemNotificar};#{GestaoVendasOnlineControle.onComplete}"
                                                           action="#{GestaoVendasOnlineControle.adicionarVendasOnlineFormaPagamento}"
                                                           value="Adicionar"
                                                           styleClass="botoes nvoBt btSec">
                                        </a4j:commandButton>
                                    </h:panelGroup>
                                </h:panelGrid>
                            </c:if>

                            <c:if test="${GestaoVendasOnlineControle.config.usarFormaPagamentoPlanoProduto}">

                                <h:panelGroup layout="block"
                                              styleClass="divConfigProdutoPlano"
                                              rendered="#{not empty GestaoVendasOnlineControle.listaConfigPlanosFormaPag}">

                                    <h:panelGroup layout="block" style="display: flex;">
                                        <h:outputText
                                                styleClass="texto-size-14 nomeConvenioConfig"
                                                value="Planos:"/>
                                    </h:panelGroup>

                                    <h:panelGroup layout="block"
                                                  styleClass="divConvenioPlanoProduto">
                                        <h:panelGroup layout="block"
                                                      styleClass="divConvenioPlanos">
                                            <rich:dataTable
                                                    styleClass="tabelaDados semZebra"
                                                    style="margin: 20px 0 20px 0; width: 100%"
                                                    value="#{GestaoVendasOnlineControle.listaConfigPlanosFormaPag}"
                                                    var="itemPlano2" rowKeyVar="status">

                                                <rich:column
                                                        sortBy="#{itemPlano2.planoVO.descricao}">
                                                    <f:facet name="header">
                                                        <h:outputText value="Plano"/>
                                                    </f:facet>
                                                    <h:outputText
                                                            styleClass="fontListaNotaFiscal"
                                                            value="#{itemPlano2.planoVO.codigo} - #{itemPlano2.planoVO.descricao}"/>
                                                </rich:column>

                                                <rich:column
                                                        sortBy="#{itemPlano2.formaPagamentoDescricao}">
                                                    <f:facet name="header">
                                                        <h:outputText
                                                                value="Forma de Pagamento"/>
                                                    </f:facet>
                                                    <h:outputText
                                                            styleClass="fontListaNotaFiscal"
                                                            value="#{itemPlano2.formaPagamentoDescricao}"/>
                                                </rich:column>

                                                <rich:column
                                                        headerClass="col-text-align-center">
                                                    <f:facet name="header">
                                                        <h:outputText value="Excluir"/>
                                                    </f:facet>
                                                    <h:panelGroup layout="block"
                                                                  style="text-align: center">
                                                        <a4j:commandLink
                                                                reRender="divAbaFormaPagamento"
                                                                oncomplete="#{GestaoVendasOnlineControle.mensagemNotificar};#{GestaoVendasOnlineControle.onComplete}"
                                                                action="#{GestaoVendasOnlineControle.excluirFormaPagamentoPlano}"
                                                                title="Excluir"
                                                                styleClass="tooltipster">
                                                            <i class="fa fa-icon-minus-sign"
                                                               style="font-size: 17px"></i>
                                                        </a4j:commandLink>
                                                    </h:panelGroup>
                                                </rich:column>
                                            </rich:dataTable>
                                        </h:panelGroup>
                                    </h:panelGroup>
                                </h:panelGroup>


                                <h:panelGroup layout="block"
                                              styleClass="divConfigProdutoPlano"
                                              rendered="#{not empty GestaoVendasOnlineControle.listaConfigProdutosFormaPag}">

                                    <h:panelGroup layout="block" style="display: flex;">
                                        <h:outputText
                                                styleClass="texto-size-14 nomeConvenioConfig"
                                                value="Produtos:"/>
                                    </h:panelGroup>

                                    <h:panelGroup layout="block"
                                                  styleClass="divConvenioPlanoProduto">
                                        <h:panelGroup layout="block"
                                                      styleClass="divConvenioProdutos">
                                            <rich:dataTable
                                                    styleClass="tabelaDados semZebra"
                                                    style="margin: 20px 0 20px 0; width: 100%"
                                                    value="#{GestaoVendasOnlineControle.listaConfigProdutosFormaPag}"
                                                    var="itemProduto2"
                                                    rowKeyVar="status">

                                                <rich:column
                                                        sortBy="#{itemProduto2.produtoVO.descricao}">
                                                    <f:facet name="header">
                                                        <h:outputText value="Produto"/>
                                                    </f:facet>
                                                    <h:outputText
                                                            styleClass="fontListaNotaFiscal"
                                                            value="#{itemProduto2.produtoVO.codigo} - #{itemProduto2.produtoVO.descricao}"/>
                                                </rich:column>

                                                <rich:column
                                                        sortBy="#{itemProduto2.formaPagamentoDescricao}">
                                                    <f:facet name="header">
                                                        <h:outputText
                                                                value="Forma de Pagamento"/>
                                                    </f:facet>
                                                    <h:outputText
                                                            styleClass="fontListaNotaFiscal"
                                                            value="#{itemProduto2.formaPagamentoDescricao}"/>
                                                </rich:column>

                                                <rich:column
                                                        headerClass="col-text-align-center">
                                                    <f:facet name="header">
                                                        <h:outputText value="Excluir"/>
                                                    </f:facet>
                                                    <h:panelGroup layout="block"
                                                                  style="text-align: center">
                                                        <a4j:commandLink
                                                                reRender="divAbaFormaPagamento"
                                                                oncomplete="#{GestaoVendasOnlineControle.mensagemNotificar};#{GestaoVendasOnlineControle.onComplete}"
                                                                action="#{GestaoVendasOnlineControle.excluirFormaPagamentoProduto}"
                                                                title="Excluir"
                                                                styleClass="tooltipster">
                                                            <i class="fa fa-icon-minus-sign"
                                                               style="font-size: 17px"></i>
                                                        </a4j:commandLink>
                                                    </h:panelGroup>
                                                </rich:column>
                                            </rich:dataTable>
                                        </h:panelGroup>
                                    </h:panelGroup>
                                </h:panelGroup>
                            </c:if>
                        </h:panelGroup>
                    </h:panelGroup>

                    <h:panelGroup layout="block" id="divAbaIntegracoes" style="width: 50%;"
                                  rendered="#{GestaoVendasOnlineControle.abaIntegracoes}">

                        <h:panelGrid width="100%" columns="2"
                                     columnClasses="w40, w60" cellpadding="10"
                                     style="margin-top: 20px">

                            <h:panelGroup styleClass="alinhamentoLabels">
                                <h:outputText styleClass="texto-size-14 cinza" value="Chave Google Analytics:"/>
                            </h:panelGroup>

                            <h:panelGroup layout="block" style="font-size: 14px">
                                <h:inputText  id="chaveAnalytics" value="#{GestaoVendasOnlineControle.config.analyticsId}" onblur="blurinput(this);" style="width: 90%"
                                             onfocus="focusinput(this);" styleClass="form inputTextVendas"/>
                            </h:panelGroup>

                            <h:panelGroup>
                                <h:outputText styleClass="texto-size-14 cinza" value="Pixel Meta:"/>
                            </h:panelGroup>

                            <h:panelGroup layout="block" style="font-size: 14px">
                                <h:inputText id="pixelFacebook" value="#{GestaoVendasOnlineControle.config.pixelId}" title="Informe o pixel id gerado aqui" onblur="blurinput(this);" style="width: 90%"
                                             onfocus="focusinput(this);" styleClass="form inputTextVendas tooltipster"/>
                            </h:panelGroup>

                            <h:panelGroup>
                                <h:outputText styleClass="texto-size-14 cinza" value="API de Conversões Meta:"/>
                            </h:panelGroup>

                            <h:panelGroup layout="block" style="font-size: 14px">
                                <h:inputText id="apiConversaoFacebook" value="#{GestaoVendasOnlineControle.config.tokenApiConversao}" title="Informe o token gerado aqui" onblur="blurinput(this);" style="width: 90%"
                                             onfocus="focusinput(this);" styleClass="form inputTextVendas tooltipster"/>
                            </h:panelGroup>

                            <h:panelGroup>
                                <h:outputText styleClass="texto-size-14 cinza tooltipster"
                                              title="#{GestaoVendasOnlineControle.titleGoogleTagManagerVendasOnline}"
                                              value="Código Google Tag Manager:"/>
                            </h:panelGroup>

                            <h:panelGroup layout="block" style="font-size: 14px">
                                <h:inputText id="googleTagManager"
                                             value="#{GestaoVendasOnlineControle.config.googleTagId}"
                                             title="#{GestaoVendasOnlineControle.titleGoogleTagManagerVendasOnline}"
                                             onblur="blurinput(this);" style="width: 90%"
                                             onfocus="focusinput(this);" styleClass="form inputTextVendas tooltipster"/>
                            </h:panelGroup>

                            <%--Integração BotConversa Acad. Otto Personal--%>
                            <h:panelGroup rendered="#{GestaoVendasOnlineControle.usuarioLogado.usuarioPactoSolucoes}">
                                <h:outputText styleClass="texto-size-14 cinza" value="Integração GymBot:"/>
                            </h:panelGroup>

                            <h:selectBooleanCheckbox
                                    rendered="#{GestaoVendasOnlineControle.usuarioLogado.usuarioPactoSolucoes}"
                                    id="habIntBotConversa"
                                    value="#{GestaoVendasOnlineControle.config.integracaoBotConversa}">
                                <a4j:support event="onclick"
                                             reRender="vendasOnlinePanel"/>
                            </h:selectBooleanCheckbox>

                            <h:panelGroup
                                    rendered="#{GestaoVendasOnlineControle.config.integracaoBotConversa && GestaoVendasOnlineControle.usuarioLogado.usuarioPactoSolucoes}">
                                <h:outputText styleClass="texto-size-14 cinza" value="Endereço da API:"/>
                            </h:panelGroup>

                            <h:panelGroup layout="block" style="font-size: 14px"
                                          rendered="#{GestaoVendasOnlineControle.config.integracaoBotConversa && GestaoVendasOnlineControle.usuarioLogado.usuarioPactoSolucoes}">
                                <h:inputText id="endpointAPI"
                                             value="#{GestaoVendasOnlineControle.config.enderecoEnviarAcoesBotConversa}"
                                             onblur="blurinput(this);" style="width: 90%; margin-top: -15px;"
                                             onfocus="focusinput(this);" styleClass="form inputTextVendas"/>
                            </h:panelGroup>
                        </h:panelGrid>
                    </h:panelGroup>

                    <h:panelGroup layout="block" id="divAbaCamposAdicionais" style="width: 80%;" rendered="#{GestaoVendasOnlineControle.abaCamposAdicionais}">

                        <h:inputHidden id="hiddenQuantRegistroCampo" value="#{GestaoVendasOnlineControle.quantidadeCamposAdicionaisItensPlanoProduto}"/>

                        <span style="display: ruby;" class="bi-font-family bi-table-text">
                            <h4 style="display: block; margin-top: 30px;"
                                class="texto-size-14 cinza">Defina os campos a serem exibidos nos links gerados.</h4>
                                <i class="fa-icon-question-sign tooltipster"
                                   title="${GestaoVendasOnlineControle.titleCamposExibirVendasOnline}"></i>
                          </span>

                        <h:panelGrid id="panelCamposObrigatoriosCadastroVendas" columns="4" width="80%,100%">
                            <h:panelGrid
                                    columns="1" rowClasses="linhaImpar, linhaPar"
                                    columnClasses="classEsquerdaConfiguracaoSistema, classDireitaConfiguracaoSistema" >

                                <h:panelGrid id="panelTituloCamposObrigatoriosCadastroVendas" columns="4" width="100%"
                                             headerClass="subordinado" columnClasses="colunaCentralizada"
                                             style="margin-right:15px;background-color:#B5B5B5;">
                                </h:panelGrid>

                                <rich:dataTable
                                        id="listaCamposObrigatoriosCadastroVendas" width="100%" border="0" rows="50"
                                        cellspacing="0" cellpadding="2" styleClass="textsmall"
                                        columnClasses="colunaEsquerda, colunaCentralizada, colunaCentralizada, colunaCentralizada, colunaCentralizada"
                                        value="" var="">

                                    <rich:column width="200">
                                        <f:facet name="header">
                                            <h:outputText style="font-weight: bold" value="Nome"/>
                                        </f:facet>
                                        <h:outputText style="font-weight: bold" value="Marcar/Desmarcar"/>
                                    </rich:column>

                                    <rich:column width="80">
                                        <f:facet name="header">
                                            <h:outputText style="font-weight: bold" value="Plano"/>
                                        </f:facet>
                                        <h:selectBooleanCheckbox id="mostrarTodosPlano" value="#{marcaApresentar}" onclick="marcarTodos('Plano')"/>
                                    </rich:column>

                                    <rich:column width="110">
                                        <f:facet name="header">
                                            <h:outputText style="font-weight: bold; " value="Produtos / Visitante"/>
                                        </f:facet>
                                        <h:selectBooleanCheckbox id="mostrarTodosProduto" value="#{marcaPendente}"  onclick="marcarTodos('Produto')"/>
                                    </rich:column>

                                    <rich:column width="80">
                                        <f:facet name="header">
                                            <h:outputText style="font-weight: bold; " value="Plano Flow"/>
                                        </f:facet>
                                        <h:selectBooleanCheckbox id="mostrarTodosPlanoFlow" value="#{marcaPendente}"  onclick="marcarTodos('PlanoFlow')"/>
                                    </rich:column>

                                    <rich:column width="80">
                                        <f:facet name="header">
                                            <h:outputText style="font-weight: bold; " value="Produto Flow"/>
                                        </f:facet>
                                        <h:selectBooleanCheckbox id="mostrarTodosProdutoFlow" value="#{marcaPendente}"  onclick="marcarTodos('ProdutoFlow')"/>
                                    </rich:column>
                                </rich:dataTable>

                                <rich:dataTable id="listaConfiguracaoCamposObrigatoriosCadastroVendas" width="100%" border="0" rows="50"
                                cellspacing="0"
                                cellpadding="2" styleClass="textsmall"
                                columnClasses="colunaEsquerda, colunaCentralizada, colunaCentralizada, colunaCentralizada, colunaCentralizada"
                                value="#{GestaoVendasOnlineControle.camposAdicionaisItensPlanoProduto}"
                                var="camposAdicionais">

                                    <rich:column width="200">
                                        <h:outputText id="nome" styleClass="texto-size-14 cinza" style="font-size: 14px" title="#{camposAdicionais.hint}"  value="#{camposAdicionais.descricao}"/>
                                    </rich:column>

                                    <rich:column width="80">
                                        <h:selectBooleanCheckbox id="checkPlano"
                                                                 style="#{camposAdicionais.styleCheckBoxWithIcon}"
                                                                 value="#{camposAdicionais.obrigatorioPlano}"
                                                                 title="#{camposAdicionais.hint}"
                                                                 rendered="#{camposAdicionais.habilitaCampoPlano}"/>
                                        <h:panelGroup styleClass="tooltipster" id="panelinfocampos1"
                                                      rendered="#{camposAdicionais.exibirIconeInfoAdicional}">
                                            <i class="fa-icon-question-sign tooltipster"
                                               title="Este campo é obrigatório para clientes menores de 18 anos de qualquer forma. </br>
                                            Isso quer dizer que no momento do cadastro, mesmo que este campo aqui esteja <b>desmarcado</b>, caso o cliente seja menor de idade </br>
                                            este campo será mostrado automaticamente mesmo assim e será obrigatório. </br>
                                            Se você deixar esse campo <b>marcado</b> ele será obrigatório para todo e qualquer cadastro, independente se é menor de idade ou não."></i>
                                        </h:panelGroup>
                                    </rich:column>

                                    <rich:column width="110">
                                        <h:selectBooleanCheckbox style="#{camposAdicionais.styleCheckBoxWithIcon}" id="checkProduto"
                                                                 value="#{camposAdicionais.obrigatorioProduto}"  title="#{camposAdicionais.hint}"
                                                                 rendered="#{camposAdicionais.habilitaCampoProduto}"/>
                                        <h:panelGroup styleClass="tooltipster" id="panelinfocampos2"
                                                      rendered="#{camposAdicionais.exibirIconeInfoAdicional}">
                                        <i class="fa-icon-question-sign tooltipster"
                                            title="Este campo é obrigatório para clientes menores de 18 anos de qualquer forma. </br>
                                            Isso quer dizer que no momento do cadastro, mesmo que este campo aqui esteja <b>desmarcado</b>, caso o cliente seja menor de idade </br>
                                            este campo será mostrado automaticamente mesmo assim e será obrigatório. </br>
                                            Se você deixar esse campo <b>marcado</b> ele será obrigatório para todo e qualquer cadastro, independente se é menor de idade ou não."></i>
                                        </h:panelGroup>
                                    </rich:column>

                                    <rich:column width="80">
                                        <h:selectBooleanCheckbox style="#{camposAdicionais.styleCheckBoxWithIcon}" id="checkPlanoFlow"
                                                                 value="#{camposAdicionais.obrigatorioPlanoFlow}"  title="#{camposAdicionais.hint}"
                                                                 rendered="#{camposAdicionais.habilitaCampoPlanoFlow}"/>
                                        <h:panelGroup styleClass="tooltipster" id="panelinfocampos3"
                                                      rendered="#{camposAdicionais.exibirIconeInfoAdicional}">
                                            <i class="fa-icon-question-sign tooltipster"
                                               title="Este campo é obrigatório para clientes menores de 18 anos de qualquer forma. </br>
                                            Isso quer dizer que no momento do cadastro, mesmo que este campo aqui esteja <b>desmarcado</b>, caso o cliente seja menor de idade </br>
                                            este campo será mostrado automaticamente mesmo assim e será obrigatório. </br>
                                            Se você deixar esse campo <b>marcado</b> ele será obrigatório para todo e qualquer cadastro, independente se é menor de idade ou não."></i>
                                        </h:panelGroup>
                                    </rich:column>

                                    <rich:column width="80">
                                        <h:selectBooleanCheckbox style="#{camposAdicionais.styleCheckBoxWithIcon}" id="checkProdutoFlow"
                                                                 value="#{camposAdicionais.obrigatorioProdutoFlow}"  title="#{camposAdicionais.hint}"
                                                                 rendered="#{camposAdicionais.habilitaCampoProdutoFlow}"/>
                                        <h:panelGroup styleClass="tooltipster" id="panelinfocampos4"
                                                      rendered="#{camposAdicionais.exibirIconeInfoAdicional}">
                                            <i class="fa-icon-question-sign tooltipster"
                                               title="Este campo é obrigatório para clientes menores de 18 anos de qualquer forma. </br>
                                            Isso quer dizer que no momento do cadastro, mesmo que este campo aqui esteja <b>desmarcado</b>, caso o cliente seja menor de idade </br>
                                            este campo será mostrado automaticamente mesmo assim e será obrigatório. </br>
                                            Se você deixar esse campo <b>marcado</b> ele será obrigatório para todo e qualquer cadastro, independente se é menor de idade ou não."></i>
                                        </h:panelGroup>
                                    </rich:column>

                                </rich:dataTable>
                            </h:panelGrid>
                        </h:panelGrid>
                    </h:panelGroup>

                    <h:panelGroup layout="block" styleClass="container-botoes"
                                  style="padding-top: 30px; width: 82%">
                        <a4j:commandLink id="gravarVendasConfig"
                                         action="#{GestaoVendasOnlineControle.gravar}"
                                         onclick="addCor();"
                                         reRender="form:panelConteudo, form:abaLinksCampanha, form:panelConvenioPlanoProduto"
                                         oncomplete="#{GestaoVendasOnlineControle.mensagemNotificar}"
                                         value="Gravar configurações"
                                         styleClass="botaoPrimario texto-size-14-real"/>
                        <a4j:commandLink id="visualizarLogVendasOnlineConfig"
                                         immediate="true"
                                         action="#{GestaoVendasOnlineControle.realizarConsultaLogObjetoSelecionadoVendasOnlineConfigs}"
                                         accesskey="5"
                                         style="font-size: 15.7px; margin-left: 10px;"
                                         styleClass="botaoSecundario texto-size-14-real fa-icon-list "
                                         onclick="abrirPopup('visualizadorLogForm.jsp', 'VisualizadorLog', 785, 595);">
                        </a4j:commandLink>
                    </h:panelGroup>
                </h:panelGroup>


                <h:panelGroup rendered="#{GestaoVendasOnlineControle.multiEmpresaConfig}" layout="block"
                              styleClass="gr-container-totalizador"
                              id="divAbaConfigsMulti"
                              style="margin: 10px 20px 20px 20px; width: calc(100% - 40px); border: none;">

                    <h:panelGrid width="60%" columns="2"
                                 columnClasses="w20, w50" cellpadding="10"
                                 style="margin-top: 10px">

                        <h:panelGroup styleClass="alinhamentoLabels">
                            <h:outputText styleClass="texto-size-14 cinza"
                                          value="Upload da Logo: "> </h:outputText>
                            <i class="fa-icon-question-sign tooltipster"
                               style="color: #777"
                               title="Logo para apresentar na pagina de multiempresas. "></i>
                        </h:panelGroup>

                        <h:panelGroup id="painelBannerMulti" layout="block" style="margin: 20px 10px;">
                            <h:graphicImage url="#{GestaoVendasOnlineControle.multiEmpresaConfigsVendasOnlineVO.urlFoto}"
                                            style="padding-left: 6px; width:100px; height:100px;"/>
                            <span style="text-align: center;margin-top: 15px">
                                                    <a4j:commandLink  styleClass="testremoveImagemMultunidade" style="font-size: 20px;"
                                                                     action="#{GestaoVendasOnlineControle.removerImagemMultiConfigs}"
                                                                     reRender="divAbaConfigsMulti">
                                                        <i class="fa-icon-trash"></i>
                                                    </a4j:commandLink>
                                            </span>

                            <rich:fileUpload listHeight="0" listWidth="129" noDuplicate="false"
                                             fileUploadListener="#{GestaoVendasOnlineControle.uploadImagemMultiEmpresaConfig}"
                                             maxFilesQuantity="1"
                                             addControlLabel="Procurar Arquivo" cancelEntryControlLabel="Cancelar"
                                             doneLabel="Pronto"
                                             sizeErrorLabel="Arquivo não Enviado. Tamanho Máximo Permitido 1 MB."
                                             progressLabel="Enviando" stopControlLabel="Parar"
                                             uploadControlLabel="Enviar"
                                             transferErrorLabel="Falha de Transmissão" stopEntryControlLabel="Parar"
                                             id="uploadImagemBannerMulti" immediateUpload="true" autoclear="true"
                                             acceptedTypes="png,jpg,jpeg,PNG,JPG,JPEG">

                                <a4j:support event="onuploadcomplete" oncomplete="#{GestaoVendasOnlineControle.mensagemNotificar}" reRender="panelMensagem, divAbaConfigsMulti"/>
                            </rich:fileUpload>
                            <h:outputText styleClass="texto-size-14 cinza"
                                          value="Tamanho obrigatório: 100X100 px - Formato recomendado: PNG/SVG com fundo transparente - Peso máximo: 1MB."> </h:outputText>

                        </h:panelGroup>

                        <h:panelGroup
                                styleClass="alinhamentoLabels alinhamentoLabelsQuebrada">
                            <h:outputText styleClass="texto-size-14 cinza"
                                          value="Cor dos detalhes: "></h:outputText>
                            <i class="fa-icon-question-sign tooltipster"
                               style="color: #777"
                               title="Escolha a opção que mais combina com a identidade visual para sua pagina de multiempresas."></i>
                        </h:panelGroup>
                        <h:panelGroup style="vertical-align: middle;">
                            <input type="color" id="cordetalhesAbaCoresMulti" name="body"
                                   value="${GestaoVendasOnlineControle.multiEmpresaConfigsVendasOnlineVO.cor}">
                            <h:inputHidden
                                    value="#{GestaoVendasOnlineControle.multiEmpresaConfigsVendasOnlineVO.cor}"
                                    id="coresMulti"/>
                        </h:panelGroup>

                        <h:panelGroup styleClass="alinhamentoLabels">
                            <h:outputText styleClass="texto-size-14 cinza"
                                          value="Link Whatsapp: "> </h:outputText>
                            <i class="fa-icon-question-sign tooltipster"
                               style="color: #777"
                               title="Cole neste espaço o link que leva para o WhatsApp da sua empresa. O padrão é https://api.whatsapp.com/send?phone=(numero_do_telefone). "></i>
                        </h:panelGroup>

                        <h:panelGroup layout="block" style="font-size: 14px">
                            <h:inputText title="Max. 50 caracteres"
                                         value="#{GestaoVendasOnlineControle.multiEmpresaConfigsVendasOnlineVO.linkWhatsapp}"
                                         onblur="blurinput(this);" style="width: 90%"
                                         onfocus="focusinput(this);"
                                         size="50" maxlength="50"
                                         styleClass="form inputTextVendas"/>
                        </h:panelGroup>

                        <h:panelGroup styleClass="alinhamentoLabels">
                            <h:outputText styleClass="texto-size-14 cinza"
                                          value="Link Instagram: "> </h:outputText>
                            <i class="fa-icon-question-sign tooltipster"
                               style="color: #777"
                               title="Cole neste espaço o link que leva para o Instagram da sua empresa. O padrão é https://instagram.com/nome_do_usuario. "></i>
                        </h:panelGroup>

                        <h:panelGroup layout="block" style="font-size: 14px">
                            <h:inputText title="Max. 50 caracteres"
                                         value="#{GestaoVendasOnlineControle.multiEmpresaConfigsVendasOnlineVO.linkInstagram}"
                                         onblur="blurinput(this);" style="width: 90%"
                                         onfocus="focusinput(this);"
                                         size="50" maxlength="50"
                                         styleClass="form inputTextVendas"/>
                        </h:panelGroup>

                        <h:panelGroup styleClass="alinhamentoLabels">
                            <h:outputText styleClass="texto-size-14 cinza"
                                          value="Link Facebook: "> </h:outputText>
                            <i class="fa-icon-question-sign tooltipster"
                               style="color: #777"
                               title="Cole neste espaço o link que leva para o Facebook da sua empresa. O padrão é https://facebook.com/nome_do_usuario. "></i>
                        </h:panelGroup>

                        <h:panelGroup layout="block" style="font-size: 14px">
                            <h:inputText title="Max. 50 caracteres"
                                         value="#{GestaoVendasOnlineControle.multiEmpresaConfigsVendasOnlineVO.linkFacebook}"
                                         onblur="blurinput(this);" style="width: 90%"
                                         onfocus="focusinput(this);"
                                         size="50" maxlength="50"
                                         styleClass="form inputTextVendas"/>
                        </h:panelGroup>

                        <h:panelGroup styleClass="alinhamentoLabels">
                            <h:outputText styleClass="texto-size-14 cinza"
                                          value="Link Twitter: "> </h:outputText>
                            <i class="fa-icon-question-sign tooltipster"
                               style="color: #777"
                               title="Cole neste espaço o link que leva para o Twitter da sua empresa. "></i>
                        </h:panelGroup>

                        <h:panelGroup layout="block" style="font-size: 14px">
                            <h:inputText title="Max. 50 caracteres"
                                         value="#{GestaoVendasOnlineControle.multiEmpresaConfigsVendasOnlineVO.linkTwitter}"
                                         onblur="blurinput(this);" style="width: 90%"
                                         onfocus="focusinput(this);"
                                         size="50" maxlength="50"
                                         styleClass="form inputTextVendas"/>
                        </h:panelGroup>
                    </h:panelGrid>


                    <h:panelGroup layout="block" styleClass="container-botoes"
                                  style="padding-top: 30px; width: 82%">
                        <a4j:commandLink id="gravarVendasConfigMult"
                                         action="#{GestaoVendasOnlineControle.gravar}"
                                         onclick="addCorMulti();"
                                         reRender="form:panelConteudo"
                                         oncomplete="#{GestaoVendasOnlineControle.mensagemNotificar}"
                                         value="Gravar configurações"
                                         styleClass="botaoPrimario texto-size-14-real"/>
                        <a4j:commandLink id="visualizarLogVendasOnlineConfigMulti"
                                         immediate="true"
                                         action="#{GestaoVendasOnlineControle.realizarConsultaLogObjetoSelecionadoVendasOnlineConfigs}"
                                         accesskey="5"
                                         style="font-size: 15.7px; margin-left: 10px;"
                                         styleClass="botaoSecundario texto-size-14-real fa-icon-list "
                                         onclick="abrirPopup('visualizadorLogForm.jsp', 'VisualizadorLog', 785, 595);">
                        </a4j:commandLink>
                    </h:panelGroup>
                </h:panelGroup>
            </h:panelGroup>
        </h:panelGroup>

            <jsp:include page="include_rodape_flat.jsp" flush="true"/>
        </h:panelGroup>
        <h:panelGroup id="panelGroupModalExclusao">
            <rich:modalPanel id="panelExclusaoModalidade" styleClass="novaModal" autosized="true"
                             showWhenRendered="#{GestaoVendasOnlineControle.mostrarConfirmacaoExclusao}"
                             shadowOpacity="true" width="220" height="90">
                <f:facet name="header">
                    <h:panelGroup >
                        <h:outputText value="Excluir um dos agrupamentos pode atrapalhar a exibição das modalidades em seu site.
                                                <br/>Tem certeza que deseja realizar a operação?" escape="false"></h:outputText>
                    </h:panelGroup>
                </f:facet>


                <h:panelGrid columns="2" width="100%">
                    <h:panelGroup layout="block" styleClass="container-botoes">
                        <a4j:commandLink value="Sim"
                                         action="#{GestaoVendasOnlineControle.removerModaCarrousel}"
                                         oncomplete="Richfaces.hideModalPanel('panelExclusaoModalidade');"
                                         reRender="panelGroupModalExclusao,msg,panelMensagem,form"
                                         styleClass="testSimModalidade botaoPrimario texto-size-14-real"/>
                        <a4j:commandLink value="Não"
                                         style="margin-left: 50px"
                                         oncomplete="Richfaces.hideModalPanel('panelExclusaoModalidade');"
                                         reRender="panelGroupModalExclusao,msg,panelMensagem"
                                         styleClass="testNaoModalidade botaoSecundario texto-size-14-real"/>
                    </h:panelGroup>
                </h:panelGrid>


            </rich:modalPanel>
        </h:panelGroup>

            <c:if test="${SuperControle.menuZwUi}">
                <jsp:include page="include_box_menulateral.jsp">
                    <jsp:param name="menu" value="ADM-INICIO" />
                </jsp:include>
            </c:if>

        </h:panelGroup>
        </h:panelGroup>
        </body>
        </html>
    </h:form>

    <jsp:include page="gestaoVendasOnline_modalFotosAcademia.jsp" flush="true"/>
</f:view>

<script src="script/vanilla-masker.min.js" type="text/javascript"></script>
<script src="hoverform.js" type="text/javascript"></script>

<script>
    function addCores() {
        if (document.getElementById('form:cores') != null) {
            document.getElementById('form:cores').value = document.getElementById('cordetalhesAbaCores').value;
        }
    }
    function addCor() {
        if (document.getElementById('form:cor') != null) {
            document.getElementById('form:cor').value = document.getElementById('cordetalhes').value;
        }
    }
    function addCorMulti() {
        if (document.getElementById('form:coresMulti') != null) {
            document.getElementById('form:coresMulti').value = document.getElementById('cordetalhesAbaCoresMulti').value;
        }
    }

    carregarTooltipster();

    function copiar(copyText) {
        var el = document.createElement('textarea');
        el.value = copyText;
        document.body.appendChild(el);
        el.select();
        document.execCommand('copy');
        document.body.removeChild(el);
        Notifier.info('Link copiado para a área de transferência.');
    }

    function adicionarMascaraMonetaria(el) {
        try {
            VMasker(document.getElementById(el)).maskMoney({
                precision: 2,
                separator: ',',
                delimiter: '.',
                zeroCents: false
            });
        } catch (e) {
            console.log("O elemento " + el + " ainda não está na tela.")
        }
    }

    function mascaraLetraNumero(campo){
        var digits="0123456789ABCDEFGHIJKLMNOPQRSTWUVXZYabcdefghijklmnopqrstwuvxzy";
        var campo_temp;
        var campo_result = '';
        for (var i=0;i < campo.value.length;i++){
            campo_temp=campo.value.substring(i,i+1);
            if (digits.indexOf(campo_temp)!=-1 && campo_temp != '.'){
                campo_result +=  campo_temp;

            }
        }
        campo.value = campo_result;
    }

    function limitarCaracteres(textarea, maxLength) {
        if (textarea.value.length > maxLength) {
            textarea.value = textarea.value.substring(0, maxLength);
        }
    }
</script>
