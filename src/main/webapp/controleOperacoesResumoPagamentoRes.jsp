<%--
    Document   : controleOperacoesResumoLogsRes
    Author     : Carla
--%>
<%@page contentType="text/html"%>
<%@page pageEncoding="ISO-8859-1"%>
<%@include file="include_imports.jsp"%>
<head><script type="text/javascript" language="javascript" src="./script/script.js"></script></head>
<script type="text/javascript" language="javascript" src="../hoverform.js"></script>
<link href="./css/otimize.css" rel="stylesheet" type="text/css">
<link href="./beta/css/font-awesome_2.0.min.css" rel="stylesheet" type="text/css">

<%@taglib prefix="f" uri="http://java.sun.com/jsf/core"%>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html"%>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions"%>

<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax"%>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich"%>

<f:loadBundle var="msg_bt" basename="propriedades.Botoes"/>
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao"/>
<f:loadBundle var="msg" basename="propriedades.Mensagens"/>

<f:view>
    <jsp:include page="includes/include_carregando_ripple.jsp" flush="true"/>
    <jsp:include page="include_log.jsp"/>
    <title>
        <h:outputText value="Resumo dos Pagamentos"/>
    </title>
    <h:panelGrid columns="1" width="100%" cellpadding="0" cellspacing="0">
        <html>
            <body onload="fireElement('form:botaoAtualizarPagina')"/>
            <h:form id="form" >
                <c:set var="titulo" scope="session" value="Resumo dos Pagamentos Total : ${fn:length(RelControleOperacoesControle.controleOperacoesRelVO.listaPendenciaResumoPessoaRelVOs)} "/>
                <c:set var="urlWiki" scope="session" value="${SuperControle.urlBaseConhecimento}bi-controle-de-operacoes-de-excecoes-adm/"/>
                <h:panelGroup layout="block" styleClass="pure-g-r">
                    <f:facet name="header">
                        <jsp:include page="topo_reduzido_popUp.jsp"/>
                    </f:facet>
                </h:panelGroup>
                <a4j:commandButton id="botaoAtualizarPagina" reRender="form" style="display:none"/>

                <h:panelGroup layout="block" style="display: inline-table;width: 100%;height: 100vh" styleClass="fundoBranco">

                    <h:panelGroup layout="block" styleClass="caixaCorpo">
                        <h:panelGroup layout="block" style="height: 80%;width: 100%">
                            <h:panelGroup layout="block" styleClass="caixaMenuLatel">
                                <h:panelGroup layout="block" >
                                    <h:panelGroup layout="block" styleClass="margin-box">
                                        <h:panelGrid columns="1" width="100%" >
                                            <h:panelGrid width="100%" style="text-align: right">
                                                <h:panelGroup layout="block">
                                                    <a4j:commandLink id="exportarExcel"
                                                                     style="margin-left: 8px;"
                                                                     actionListener="#{ExportadorListaControle.exportar}"
                                                                     rendered="#{not empty RelControleOperacoesControle.controleOperacoesRelVO.listaPendenciaResumoPessoaRelVOs}"
                                                                     oncomplete="#{ExportadorListaControle.mensagemNotificar}#{ExportadorListaControle.operacaoOnComplete}#{ExportadorListaControle.msgAlert}"
                                                                     accesskey="2" styleClass="linkPadrao">
                                                        <f:attribute name="lista" value="#{RelControleOperacoesControle.controleOperacoesRelVO.listaPendenciaResumoPessoaRelVOs}"/>
                                                        <f:attribute name="itemExportacao" value="#{RelControleOperacoesControle.controleOperacoesRelVO.itemExportar}"/>
                                                        <f:attribute name="tipo" value="xls"/>
                                                        <f:attribute name="atributos" value="nomePagador=Nome do Cliente,dataLancamento=Data de Lançamento,dataPagamento=Data do Pagamento,dataAlteracao=Data da Alteração,valor=Valor (R$),formaPagamento=Forma de Pagamento,responsavelPagamento=Responsável Pagamento,dataQuitacao=Data da Quitação"/>
                                                        <f:attribute name="prefixo" value="ControleOpExcecao"/>
                                                        <h:outputText title="Exportar para o formato Excel" styleClass="btn-print-2 excel"/>
                                                    </a4j:commandLink>
                                                    <%--BOTÃO PDF--%>
                                                    <a4j:commandLink id="exportarPdf"
                                                                     style="margin-left: 8px;"
                                                                     actionListener="#{ExportadorListaControle.exportar}"
                                                                     rendered="#{not empty RelControleOperacoesControle.controleOperacoesRelVO.listaPendenciaResumoPessoaRelVOs}"
                                                                     oncomplete="#{ExportadorListaControle.mensagemNotificar}#{ExportadorListaControle.operacaoOnComplete}#{ExportadorListaControle.msgAlert}"
                                                                     accesskey="2" styleClass="linkPadrao">
                                                        <f:attribute name="lista" value="#{RelControleOperacoesControle.controleOperacoesRelVO.listaPendenciaResumoPessoaRelVOs}"/>
                                                        <f:attribute name="tipo" value="pdf"/>
                                                        <f:attribute name="itemExportacao" value="#{RelControleOperacoesControle.controleOperacoesRelVO.itemExportar}"/>
                                                        <f:attribute name="atributos" value="nomePagador=Nome do Cliente,dataLancamento=Data de Lançamento,dataPagamento=Data do Pagamento,dataAlteracao=Data da Alteração,valor=Valor (R$),formaPagamento=Forma de Pagamento,responsavelPagamento=Responsável Pagamento,dataQuitacao=Data da Quitação"/>
                                                        <f:attribute name="prefixo" value="ControleOpExcecao"/>
                                                        <h:outputText title="Exportar para o formato PDF" styleClass="btn-print-2 pdf"/>
                                                    </a4j:commandLink>
                                                </h:panelGroup>
                                            </h:panelGrid>
                                            <rich:dataTable width="100%" styleClass="tabelaSimplesCustom" id="tabelaRes"
                                                            value="#{RelControleOperacoesControle.controleOperacoesRelVO.listaPendenciaResumoPessoaRelVOs}" rows="50" var="resumoPessoa" rowKeyVar="status">
                                                <%@include file="/pages/ce/includes/include_contador_richtable.jsp" %>
                                                <rich:column>
                                                    <f:facet name="header">
                                                        <h:outputText styleClass="texto-font texto-size-14-real texto-cor-cinza texto-bold" value="NOME DO CLIENTE" />
                                                    </f:facet>
                                                    <h:outputText styleClass="texto-font texto-size-14-real texto-cor-cinza" value="#{resumoPessoa.movPagamentoVO.nomePagador}" />
                                                </rich:column>
                                                <rich:column>
                                                    <f:facet name="header">
                                                        <h:outputText styleClass="texto-font texto-size-14-real texto-cor-cinza texto-bold" value="DATA DE LANÇAMENTO" />
                                                    </f:facet>
                                                    <h:outputText styleClass="texto-font texto-size-14-real texto-cor-cinza" value="#{resumoPessoa.movPagamentoVO.dataLancamentoSemHora_Apresentar}" />
                                                </rich:column>
                                                <rich:column>
                                                    <f:facet name="header">
                                                        <h:outputText styleClass="texto-font texto-size-14-real texto-cor-cinza texto-bold" value="DATA DO PAGAMENTO" />
                                                    </f:facet>
                                                    <h:outputText styleClass="texto-font texto-size-14-real texto-cor-cinza" value="#{resumoPessoa.movPagamentoVO.dataPagamentoSemHora_Apresentar}" />
                                                </rich:column>
                                                <rich:column>
                                                    <f:facet name="header">
                                                        <h:outputText styleClass="texto-font texto-size-14-real texto-cor-cinza texto-bold" value="DATA DA ALTERAÇÃO" />
                                                    </f:facet>
                                                    <h:outputText styleClass="texto-font texto-size-14-real texto-cor-cinza" value="#{resumoPessoa.movPagamentoVO.dataAlteracaoManualSemHora_Apresentar}" />
                                                </rich:column>
                                                <rich:column>
                                                    <f:facet name="header">
                                                        <h:outputText styleClass="texto-font texto-size-14-real texto-cor-cinza texto-bold" value="VALOR (R$)" />
                                                    </f:facet>
                                                    <h:outputText styleClass="texto-font texto-size-14-real texto-cor-cinza" value="#{resumoPessoa.movPagamentoVO.valor}" >
                                                        <f:converter converterId="FormatadorNumerico" />
                                                    </h:outputText>
                                                </rich:column>
                                                <rich:column>
                                                    <f:facet name="header">
                                                        <h:outputText styleClass="texto-font texto-size-14-real texto-cor-cinza texto-bold" value="FORMA DE PAGAMENTO" />
                                                    </f:facet>
                                                    <h:outputText styleClass="texto-font texto-size-14-real texto-cor-cinza" value="#{resumoPessoa.movPagamentoVO.formaPagamento.descricao}" />
                                                </rich:column>
                                                <rich:column>
                                                    <f:facet name="header">
                                                        <h:outputText styleClass="texto-font texto-size-14-real texto-cor-cinza texto-bold" value="RESPONSÁVEL PAGAMENTO" />
                                                    </f:facet>
                                                    <h:outputText styleClass="texto-font texto-size-14-real texto-cor-cinza" value="#{resumoPessoa.movPagamentoVO.responsavelPagamento}" />
                                                </rich:column>
                                                <rich:column>
                                                    <f:facet name="header">
                                                        <h:outputText styleClass="texto-font texto-size-14-real texto-cor-cinza texto-bold" value="DATA DA QUITAÇÃO" />
                                                    </f:facet>
                                                    <h:outputText styleClass="texto-font texto-size-14-real texto-cor-cinza" value="#{resumoPessoa.movPagamentoVO.dataQuitacao_Apresentar}" />
                                                </rich:column>
                                                <rich:column >
                                                    <a4j:commandLink action="#{RelControleOperacoesControle.irParaTelaCliente}"
                                                                     reRender="form, formLog"
                                                                     styleClass="linkPadrao"
                                                                     oncomplete="abrirPopup('clienteNav.jsp?page=viewCliente', 'Cliente', 1024, 700);">
                                                        <f:param name="state" value="AC"/>
                                                        <i class="fa-icon-seach"></i>
                                                    </a4j:commandLink>
                                                </rich:column>
                                            </rich:dataTable>
                                            <rich:datascroller styleClass="scrollPureCustom" renderIfSinglePage="false" align="center" for="form:tabelaRes" maxPages="10" id="sctabelaRes" />
                                        </h:panelGrid>
                                    </h:panelGroup>
                                </h:panelGroup>
                            </h:panelGroup>

                        </h:panelGroup>
                    </h:panelGroup>
                </h:panelGroup>

            </h:form>
        </body>
    </html>
</h:panelGrid>
</f:view>

