<%@page contentType="text/html" %>
<%@page pageEncoding="ISO-8859-1" %>

<%@taglib prefix="f" uri="http://java.sun.com/jsf/core" %>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html" %>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich" %>
<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax" %>
<f:loadBundle var="msg_bt" basename="propriedades.Botoes"/>
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao"/>
<f:loadBundle var="msg" basename="propriedades.Mensagens"/>
<link href="./css/otimize.css" rel="stylesheet" type="text/css">

<head>
    <%@include file="/includes/include_import_minifiles.jsp" %>
</head>

<f:view>
    <jsp:include page="includes/include_carregando_ripple.jsp" flush="true"/>
    <h:form id="form">
        <html>
        <jsp:include page="include_head.jsp" flush="true"/>
        <body>
        <h:panelGroup layout="block" style="display: inline-table;width: 100%;" styleClass="fundoCinza">
            <h:panelGroup layout="block" styleClass="bgtop topoZW">
                <jsp:include page="include_topo_novo.jsp" flush="true"/>
                <jsp:include page="include_menu_zw_flat.jsp" flush="true"/>
                <rich:jQuery query="addClass('menuItemAtual')" selector=".item2"/>
            </h:panelGroup>
            <h:panelGroup layout="block" styleClass="caixaCorpo">
                <h:panelGroup layout="block" style="height: 80%;width: 100%">
                    <h:panelGroup layout="block" styleClass="caixaMenuLatel">
                        <h:panelGroup layout="block" styleClass="container-imagem container-conteudo-central">
                            <h:panelGroup layout="block" styleClass="container-box form-flat">
                                <h:panelGroup styleClass="container-box-header" layout="block">
                                    <h:panelGroup layout="block" styleClass="margin-box">
                                        <h:outputText value="Config. Financeiras" styleClass="container-header-titulo"/>
                                    </h:panelGroup>
                                </h:panelGroup>


                                <h:panelGroup layout="block" styleClass="margin-box">
                                    <h:panelGrid columns="1" width="100%" cellpadding="0" cellspacing="0">
                                        <h:panelGrid columnClasses="w33,w33,w33" columns="3" width="100%"
                                                     cellpadding="0" cellspacing="0">
                                            <h:panelGrid columns="1" style="height:100%" width="100%"
                                                         cellpadding="5" cellspacing="5">
                                                <h:panelGroup>
                                                    <div>
                                                        <h:outputText styleClass="fa-icon-pushpin textoCorAzul"/>
                                                        <rich:spacer width="5px"/>
                                                        <a4j:commandLink id="bancoDescritivo" styleClass="tituloCampos"
                                                                         rendered="#{LoginControle.permissaoAcessoMenuVO.banco}"
                                                                         oncomplete="#{FuncionalidadeControle.abrirPopUp}"
                                                                         actionListener="#{MenuAcessoFacilControle.addHistoricoPreparaFuncionalidade}"
                                                                         value="Banco">
                                                            <f:attribute name="funcionalidade" value="BANCO"/>
                                                        </a4j:commandLink>
                                                        <h:outputText styleClass="tituloCampos" id="bancoDescritivoText"
                                                                      rendered="#{!LoginControle.permissaoAcessoMenuVO.banco}"
                                                                      value="Banco"/>
                                                    </div>
                                                    <rich:spacer height="10px"/>
                                                    <h:outputText styleClass="text" value="Aqui serão cadastrados os Bancos utilizados para os pagamentos efetuados com cheque. Lembre-se que cada banco possui seu próprio código identificador e é extremamente importante que essa codificação seja seguida no momento de cadastro.
                                                                                  "/>
                                                </h:panelGroup>
                                                <h:panelGroup>
                                                    <h:outputText styleClass="text quote-text"
                                                                  value="Ao efetuar o cadastro do Banco do Brasil definirei o código como 01. Ao cadastrar a Caixa Econômica Federal irei lançar o código como 104."/>
                                                </h:panelGroup>
                                            </h:panelGrid>
                                            <h:panelGrid style="height:100%" columns="1" width="100%"
                                                         cellpadding="5" cellspacing="5">
                                                <h:panelGroup>
                                                    <div>
                                                        <h:outputText styleClass="fa-icon-pushpin textoCorAzul"/>
                                                        <rich:spacer width="5px"/>
                                                        <a4j:commandLink id="formaPagamentoDescritivo"
                                                                         styleClass="tituloCampos"
                                                                         rendered="#{LoginControle.permissaoAcessoMenuVO.formaPagamento}"
                                                                         oncomplete="reRenderMenuLateral();"
                                                                         actionListener="#{MenuAcessoFacilControle.addHistorinoFuncionalidade}"
                                                                         onclick="abrirPopup('formaPagamentoCons.jsp', 'FormaPagamento', 800, 595);"
                                                                         value="Formas de Pagamento">
                                                            <f:attribute name="funcionalidade" value="FORMA_PAGAMENTO"/>
                                                        </a4j:commandLink>
                                                    </div>
                                                    <h:outputText styleClass="tituloCampos"
                                                                  id="formaPagamentoDescritivoText"
                                                                  rendered="#{!LoginControle.permissaoAcessoMenuVO.formaPagamento}"
                                                                  value="Formas de Pagamento"/>
                                                    <rich:spacer height="10px"/>
                                                    <h:outputText styleClass="text"
                                                                  value="Quais as formas de pagamento você disponibiliza para seus Clientes?
                                                                                      Esta tela de lançamento permite que você cadastre todas estas formas, mantendo assim,
                                                                                      o controle de todos os pagamentos efetuados pelos seus Alunos, de maneira rápida e organizada."/>
                                                </h:panelGroup>
                                            </h:panelGrid>
                                            <h:panelGrid style="height:100%" columns="1" width="100%"
                                                         cellpadding="5" cellspacing="5">
                                                <h:panelGroup>
                                                    <div>
                                                        <h:outputText styleClass="fa-icon-pushpin textoCorAzul"/>
                                                        <rich:spacer width="5px"/>
                                                        <a4j:commandLink id="cuponsDescritivo"
                                                                         styleClass="tituloCampos"
                                                                         rendered="#{LoginControle.permissaoAcessoMenuVO.cupomFiscal}"
                                                                         onclick="abrirPopup('cuponsCons.jsp', 'ConsultaCupom', 1100, 650);"
                                                                         oncomplete="reRenderMenuLateral();"
                                                                         actionListener="#{MenuAcessoFacilControle.addHistorinoFuncionalidade}"
                                                                         value="Consulta de Cupons Fiscais">
                                                            <f:attribute name="funcionalidade" value="CONSULTA_DE_CUPONS_FISCAIS"/>
                                                        </a4j:commandLink>
                                                        <h:outputText styleClass="tituloCampos" id="cuponsDescritivoText"
                                                                      rendered="#{!LoginControle.permissaoAcessoMenuVO.cupomFiscal}"
                                                                      value="Consulta de Cupons Fiscais"/>
                                                    </div>
                                                    <rich:spacer height="10px"/>
                                                    <h:outputText styleClass="text"
                                                                  value="O ZillyonWeb conta com a funcionalidade de impressão de cupons fiscais de acordo com os
                                                                                     recebimentos realizados. Para uma gestão mais efetiva você poderá utilizar a ferramenta
                                                                                     para identificar todos os cupons que foram enviados para impressão."/>
                                                </h:panelGroup>

                                            </h:panelGrid>

                                            <h:panelGrid style="height:100%" columns="1" width="100%"
                                                         cellpadding="5" cellspacing="5">
                                                <h:panelGroup>
                                                    <div>
                                                        <h:outputText styleClass="fa-icon-pushpin textoCorAzul"/>
                                                        <rich:spacer width="5px"/>
                                                        <a4j:commandLink id="gestaoNotasDescritivo"
                                                                         styleClass="tituloCampos"
                                                                         rendered="#{LoginControle.permissaoAcessoMenuVO.gestaoNotas}"
                                                                         oncomplete="reRenderMenuLateral();"
                                                                         actionListener="#{MenuAcessoFacilControle.addHistorinoFuncionalidade}"
                                                                         onclick="abrirPopup('indexGestaoNotas.jsp', 'GestaodeNotas', 1000, 650);"
                                                                         value="Gestão de Notas">
                                                            <f:attribute name="funcionalidade" value="GESTAO_NOTAS"/>
                                                        </a4j:commandLink>
                                                        <h:outputText styleClass="tituloCampos"
                                                                      id="gestaoNotasDescritivoText"
                                                                      rendered="#{!LoginControle.permissaoAcessoMenuVO.gestaoNotas}"
                                                                      value="Gestão de Notas"/>
                                                    </div>
                                                    <rich:spacer height="10px"/>
                                                    <h:outputText styleClass="text"
                                                                  value="Através do Gestão de Notas você poderá visualizar de forma fácil a
                                                                                         quantidade de notas emitidas em um determinado período ou até mesmo
                                                                                         envia-las manualmente. Lembrando que a forma de visualização dos
                                                                                         dados dependerá do tipo de envio definido nas configurações da
                                                                                         empresa, ou seja, por faturamento, faturamento recebido,
                                                                                          competência ou compensação. "/>
                                                </h:panelGroup>

                                            </h:panelGrid>

                                            <h:panelGrid style="height:100%" columns="1" width="100%"
                                                         cellpadding="5" cellspacing="5"
                                                         rendered="#{LoginControle.permissaoAcessoMenuVO.vendaConsumidor}">
                                                <h:panelGroup>
                                                    <div>
                                                        <h:outputText styleClass="fa-icon-pushpin textoCorAzul"/>
                                                        <rich:spacer width="5px"/>
                                                        <a4j:commandLink id="vendaConsumidorDescritivo"
                                                                         styleClass="tituloCampos"
                                                                         rendered="#{LoginControle.permissaoAcessoMenuVO.vendaConsumidor}"
                                                                         oncomplete="reRenderMenuLateral();"
                                                                         actionListener="#{MenuAcessoFacilControle.addHistorinoFuncionalidade}"
                                                                         onclick="abrirPopup('vendaConsumidorCons.jsp', 'VendaConsumidor', 1000, 650);"
                                                                         value="#{msg_menu.Menu_vendaConsumidor}">
                                                            <f:attribute name="funcionalidade" value="VENDA_CONSUMIDOR"/>
                                                        </a4j:commandLink>
                                                        <h:outputText styleClass="tituloCampos"
                                                                      id="vendaConsumidorDescritivoText"
                                                                      rendered="#{!LoginControle.permissaoAcessoMenuVO.vendaConsumidor}"
                                                                      value="#{msg_menu.Menu_vendaConsumidor}"/>
                                                    </div>
                                                    <rich:spacer height="10px"/>
                                                    <h:outputText styleClass="text"
                                                                  value="Aqui você poderá consultar todas as vendas já realizadas para consumidores
                                                                                  , ou seja, clientes que não possuem cadastros no ZillyonWeb,
                                                                                   podendo ainda estornar determinada venda ou imprimir o seu recibo. "/>
                                                </h:panelGroup>

                                            </h:panelGrid>
                                            <h:panelGrid style="height:100%" columns="1" width="100%"
                                                         cellpadding="5" cellspacing="5">
                                                <h:panelGroup>
                                                    <div>
                                                        <h:outputText styleClass="fa-icon-pushpin textoCorAzul"/>
                                                        <rich:spacer width="5px"/>
                                                        <a4j:commandLink
                                                                styleClass="tituloCampos"
                                                                action="#{MetaFinanceiroControle.abrirTelaMetas}"
                                                                actionListener="#{MenuAcessoFacilControle.addHistorinoFuncionalidade}"
                                                                value="#{msg_menu.Menu_metasFinanceiro}"
                                                                oncomplete="#{MetaFinanceiroControle.msgAlert};reRenderMenuLateral();">
                                                            <f:attribute name="funcionalidade" value="METAS_FINANCEIRO_VENDA"/>
                                                        </a4j:commandLink>
                                                    </div>
                                                    <rich:spacer height="10px"/>
                                                    <h:outputText styleClass="text"
                                                                  value="Nessa tela você poderá cadastrar as taxas
                                                                                   que serão utilizadas como base para o calculo
                                                                                    da comissão dos seus consultores, lembrando que o calculo será
                                                                                     feito ou por percentual ou valor fixo. Essas taxas serão fundamentais
                                                                                      para a consulta do relatório de comissão para consultor."/>
                                                </h:panelGroup>

                                            </h:panelGrid>
                                            <h:panelGrid style="height:100%" columns="1" width="100%"
                                                         cellpadding="5" cellspacing="5"
                                                         rendered="#{LoginControle.permissaoAcessoMenuVO.taxasComissao}">
                                                <h:panelGroup>
                                                    <div>
                                                        <h:outputText styleClass="fa-icon-pushpin textoCorAzul"/>
                                                        <rich:spacer width="5px"/>
                                                        <a4j:commandLink
                                                                styleClass="tituloCampos"
                                                                onclick="abrirPopup('comissaoGeralConfiguracaoCons.jsp', 'ComissaoGeralConfiguracao', 1200, 650);"
                                                                oncomplete="reRenderMenuLateral();"
                                                                actionListener="#{MenuAcessoFacilControle.addHistorinoFuncionalidade}"
                                                                value="Taxas de Comissão">
                                                            <f:attribute name="funcionalidade" value="TAXA_COMISSAO"/>
                                                        </a4j:commandLink></div>
                                                    <rich:spacer height="10px"/>
                                                    <h:outputText styleClass="text"
                                                                  value="Nessa tela você poderá cadastrar as taxas que serão utilizadas
                                                                                   como base para o calculo da comissão dos seus consultores, lembrando
                                                                                   que o calculo será feito ou por percentual ou valor fixo.
                                                                                    Essas taxas serão fundamentais para a consulta do relatório
                                                                                     de comissão para consultor."/>
                                                </h:panelGroup>

                                            </h:panelGrid>


                                        </h:panelGrid>


                                        <h:panelGrid>

                                        </h:panelGrid>
                                    </h:panelGrid>

                                </h:panelGroup>

                            </h:panelGroup>

                        </h:panelGroup>

                        <jsp:include page="include_box_menulateral.jsp">
                            <jsp:param name="menu" value="ADM-CADASTROS_CONFIG_FINANCEIRAS" />
                        </jsp:include>

                    </h:panelGroup>
                </h:panelGroup>
            </h:panelGroup>

            <jsp:include page="include_rodape_flat.jsp" flush="true"/>
        </h:panelGroup>
    </h:form>
</f:view>
