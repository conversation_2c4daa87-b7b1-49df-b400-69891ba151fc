<%--
    Document   : renovacaoAnaliticoForm
    Created on : 26/09/2009, 11:20:08
    Author     : <PERSON><PERSON><PERSON><PERSON>
--%>

<%@page contentType="text/html"%>
<%@page pageEncoding="ISO-8859-1"%>
<link href="./css/otimize.css" rel="stylesheet" type="text/css">
<link href="./beta/css/font-awesome_2.0.min.css" rel="stylesheet" type="text/css">
<script type="text/javascript" language="javascript" src="hoverform.js"></script>
<%@taglib prefix="f" uri="http://java.sun.com/jsf/core"%>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html"%>
<%@taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>

<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax"%>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich"%>
<%@taglib prefix="jsfChart" uri="http://sourceforge.net/projects/jsf-comp" %>

<f:loadBundle var="msg_bt" basename="propriedades.Botoes"/>
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao"/>
<f:loadBundle var="msg" basename="propriedades.Mensagens"/>

<head><%@include file="/includes/include_import_minifiles.jsp" %></head>

<f:view>
    <jsp:include page="includes/include_carregando_ripple.jsp" flush="true"/>
    <title>
        <h:outputText value="Relatório de Previsão de Renovação por Contrato"/>
    </title>
    <a4j:loadScript src="/script/jquery.maskedinput-1.2.2.js" />
    <h:form id="form" style="overflow: visible;">
        <table width="100%" align="center" height="100%" border="0" cellpadding="0" cellspacing="0">
            <tr>
                <td height="77" align="left" valign="top" class="bgtop">
                    <c:set var="titulo" scope="session" value="${msg_aplic.prt_RenovacaoAnalitico_tituloLista}"/>
                    <c:set var="urlWiki" scope="session" value="${SuperControle.urlBaseConhecimento}bi-indice-renovacao-mes-adm/"/>
                    <h:panelGroup layout="block" styleClass="pure-g-r">
                        <f:facet name="header">
                            <jsp:include page="topoReduzido_material.jsp"/>
                        </f:facet>
                    </h:panelGroup>
                    <jsp:include page="include_head.jsp" flush="true"/>
                </td>
            </tr>
            <tr>
                <td align="center" valign="top"  >
                    <table width="100%" height="100%" border="0" cellpadding="0" cellspacing="0" >
                        <tr>
                            <td align="left" valign="top" style="padding-top:6px; padding-left:15px;">
                                <!-- inicio box -->
                                <table width="100%" border="0" cellspacing="0" cellpadding="0" style="padding-bottom:10px;">
                                    <tr>
                                        <td align="left" valign="top">
                                            <rich:tabPanel switchType="client" id="tabPanelFiltros" width="100%" rendered="#{LoginControle.permissaoAcessoMenuVO.relatorioDePrevisaoRenovacao}">
                                                <rich:tab id="abaData"  styleClass="tituloPre" label="Data" >
                                                    <h:panelGroup>
                                                        <h:panelGrid width="100%">
                                                            <h:panelGroup style="border:0;" id="panelFiltrosUtilizadosData">
                                                                <h:outputText
                                                                    styleClass="tituloCamposAzul"
                                                                    value="#{msg_aplic.prt_SituacaoContratoAnaliticoDW_labelFiltros}"/>

                                                                <rich:spacer width="10px" />
                                                                <rich:toolTip
                                                                    onclick="true"
                                                                    followMouse="true"
                                                                    direction="top-right"
                                                                    style=" background-color:#cedfff; border-color:#000000;text-align: left;"
                                                                    showDelay="500" >
                                                                    ${RenovacaoSinteticoControle.filtros}
                                                                </rich:toolTip>
                                                            </h:panelGroup>
                                                        </h:panelGrid>

                                                    </h:panelGroup>
                                                    <h:panelGrid columns="1">
                                                        <h:panelGroup rendered="#{RenovacaoSinteticoControle.usuario.administrador or RenovacaoSinteticoControle.consultarTodasEmpresas}">
                                                            <h:outputText styleClass="tituloPre" value="#{msg_aplic.prt_MatriculaAlunoHorarioTurma_empresa}" />
                                                            <rich:spacer width="10px" />
                                                            <h:selectOneMenu  id="empresa" styleClass="form"  onblur="blurinput(this);"  onfocus="focusinput(this);" value="#{RenovacaoSinteticoControle.empresaVO.codigo}" >
                                                                <f:selectItems  value="#{RenovacaoSinteticoControle.listaEmpresas}" />
                                                            </h:selectOneMenu>
                                                        </h:panelGroup>

                                                        <h:panelGroup layout="block">
                                                            <h:outputText styleClass="tituloPre" value="Período de :"/>
                                                            <rich:spacer width="5px"/>
                                                            <h:panelGroup>
                                                                <rich:calendar id="dataInicio"
                                                                               value="#{RenovacaoSinteticoControle.dataInicio}"
                                                                               inputSize="10"
                                                                               inputClass="form"
                                                                               oninputblur="blurinput(this);"
                                                                               oninputfocus="focusinput(this);"
                                                                               oninputchange="return validar_Data(this.id);"
                                                                               datePattern="dd/MM/yyyy"
                                                                               enableManualInput="true"
                                                                               zindex="2"
                                                                               showWeeksBar="false" />
                                                                <h:message for="dataInicio" styleClass="mensagemDetalhada"/>
                                                            </h:panelGroup>
                                                            <rich:spacer width="5px"/>
                                                            <h:outputText styleClass="tituloPre" value="Até:"/>
                                                            <rich:spacer width="5px"/>
                                                            <h:panelGroup>
                                                                <rich:calendar id="dataFinal"
                                                                               value="#{RenovacaoSinteticoControle.dataFim}"
                                                                               inputSize="10"
                                                                               inputClass="form"
                                                                               oninputblur="blurinput(this);"
                                                                               oninputfocus="focusinput(this);"
                                                                               oninputchange="return validar_Data(this.id);"
                                                                               datePattern="dd/MM/yyyy"
                                                                               enableManualInput="true"
                                                                               zindex="2"
                                                                               showWeeksBar="false" />
                                                                <h:message for="dataFinal" styleClass="mensagemDetalhada"/>
                                                            </h:panelGroup>
                                                            <rich:jQuery id="mskData" selector=".rich-calendar-input" timing="onload" query="mask('99/99/9999')" />
                                                            <rich:spacer width="10px" />

                                                            <a4j:commandLink id="consultar"
                                                                               reRender="form:panelRelatorioRenovacao, form:mensagem,
                                                                               form:tabPanelFiltros, form:tabPanelGeral, totalGeral, tabPanelResumo,
                                                                               form:tabPanelFiltrosAnalitico, form:tabPanelFiltrosSintetico,form:panelMensagem"
                                                                               styleClass=" botoes nvoBt "
                                                                               value="#{msg_aplic.prt_SituacaoRenovacaoAnaliticoDW_consulta_Consultor_titulo}"
                                                                               title="#{msg_aplic.prt_SituacaoRenovacaoAnaliticoDW_consulta_Consultor_titulo}"
                                                                               action="#{RenovacaoSinteticoControle.consultarClientesPorData}">
                                                                <i class="fa-icon-search"></i>
                                                         </a4j:commandLink>

                                                        </h:panelGroup>

                                                        <h:panelGroup>
                                                            <h:selectBooleanCheckbox value="#{RenovacaoSinteticoControle.bolsa}"/>
                                                            <rich:spacer width="5px"/>
                                                            <h:outputText styleClass="tituloPre" value="Pesquisar Também Contratos com Bolsa"/>

                                                        </h:panelGroup>
                                                        <h:panelGroup>
                                                            <h:selectBooleanCheckbox value="#{RenovacaoSinteticoControle.cancelado}"/>
                                                            <rich:spacer width="5px"/>
                                                            <h:outputText styleClass="tituloPre" value="Pesquisar Também Contratos Cancelados"/>

                                                        </h:panelGroup>
                                                        <h:panelGroup>
                                                            <h:selectBooleanCheckbox value="#{RenovacaoSinteticoControle.trancado}"/>
                                                            <rich:spacer width="5px"/>
                                                            <h:outputText styleClass="tituloPre" value="Pesquisar Também Contratos Trancados"/>

                                                        </h:panelGroup>
                                                    </h:panelGrid>
                                                </rich:tab>
                                                <rich:tab id="abaSituacaoRenovacao" styleClass="tituloPre" label="Situação Renovação">
                                                    <h:panelGroup>
                                                        <h:panelGrid width="100%">
                                                            <h:panelGroup style="border:0;" id="panelFiltrosUtilizadosSituacaoRenovacao">
                                                                <h:outputText
                                                                    styleClass="tituloCamposAzul"
                                                                    value="#{msg_aplic.prt_SituacaoContratoAnaliticoDW_labelFiltros}"/>

                                                                <rich:spacer width="10px" />
                                                                <rich:toolTip
                                                                    onclick="true"
                                                                    followMouse="true"
                                                                    direction="top-right"
                                                                    style=" background-color:#cedfff; border-color:#000000;text-align: left;"
                                                                    showDelay="500" >
                                                                    ${RenovacaoSinteticoControle.filtros}
                                                                </rich:toolTip>
                                                            </h:panelGroup>
                                                        </h:panelGrid>

                                                    </h:panelGroup>
                                                    <rich:dataGrid  width="100%" id="resultadoConsultaSituacaoRenovacao" columnClasses="semBorda" styleClass="semBorda"
                                                                    value="#{RenovacaoSinteticoControle.listaSituacaoRenovacao}"
                                                                    columns="#{RenovacaoSinteticoControle.nrColunaSituacaoRenovacao}"
                                                                    elements="#{RenovacaoSinteticoControle.tamanhoListaSituacaoRenovacao}" var="situacaoRenovacao">
                                                        <f:facet name="header">
                                                            <h:outputText styleClass="tituloPre" value="Filtrar Por: "/>
                                                        </f:facet>
                                                        <h:panelGrid columns="2">
                                                            <h:selectBooleanCheckbox id="checkSituacaoRenovacao" value="#{situacaoRenovacao.marcado}"/>
                                                            <h:outputText styleClass="tituloPre" value="#{situacaoRenovacao.nome_Apresentar}"/>
                                                        </h:panelGrid>
                                                    </rich:dataGrid>
                                                    <h:outputText escape="false" value= "<br/>" />
                                                    <a4j:commandLink id="botaoAtualizarRenovacao"
                                                                 reRender="form:panelRelatorioRenovacao,form:mensagem, form:tabPanelFiltros, form:tabPanelGeral,tabPanelResumo,
                                                                       form:tabPanelFiltrosAnalitico, form:tabPanelFiltrosSintetico, form:panelGrafico, totalGeral,form:panelMensagem"
                                                                 style="text-align:left;margin-top:10px;" action="#{RenovacaoSinteticoControle.atualizarPrevisaoRenovacaoComFiltros}"
                                                                 styleClass=" botoes nvoBt "
                                                                 value="Atualizar">
                                                    <i class="fa-icon-refresh"></i>
                                                    </a4j:commandLink>

                                                </rich:tab>
                                                <rich:tab id="abaPlano" styleClass="tituloPre" label="Planos e durações">
                                                    <h:panelGroup>
                                                        <h:panelGrid width="100%">
                                                            <h:panelGroup style="border:0;" id="panelFiltrosUtilizadosPlano">
                                                                <h:outputText
                                                                    styleClass="tituloCamposAzul"
                                                                    value="#{msg_aplic.prt_SituacaoContratoAnaliticoDW_labelFiltros}"/>

                                                                <rich:spacer width="10px" />
                                                                <rich:toolTip
                                                                    onclick="true"
                                                                    followMouse="true"
                                                                    direction="top-right"
                                                                    style=" background-color:#cedfff; border-color:#000000;text-align: left;"
                                                                    showDelay="500" >
                                                                    ${RenovacaoSinteticoControle.filtros}
                                                                </rich:toolTip>
                                                            </h:panelGroup>
                                                        </h:panelGrid>

                                                    </h:panelGroup>

                                                    <table class="rich-table semBorda"  border="0" cellpadding="0" cellspacing="0" width="100%">
                                                    <colgroup span="3"></colgroup>
                                                    <thead>
                                                    <tr class="rich-table-header  "><th class="rich-table-headercell  " colspan="3" scope="colgroup"><span class="tituloPre">Planos: </span></th></tr>
                                                    </thead>
                                                    </table>
                                                    <rich:dataGrid  width="600" id="resultadoConsultaPlano" columnClasses="semBorda" styleClass="semBorda"
                                                                    value="#{RenovacaoSinteticoControle.listaPlano}"
                                                                    columns="3"
                                                                    cellpadding="0"
                                                                    cellspacing="0"
                                                                    captionStyle="width:100px"
                                                                    elements="#{RenovacaoSinteticoControle.tamanhoListaPlano}" var="plano">

                                                        <h:panelGrid columns="2">
                                                            <h:selectBooleanCheckbox id="checkAbaPlano" value="#{plano.marcado}"/>
                                                            <h:outputText styleClass="tituloPre" value="#{plano.nome}"/>
                                                        </h:panelGrid>
                                                    </rich:dataGrid>


                                                <table class="rich-table semBorda" border="0" cellpadding="0"
                                                       cellspacing="0" width="100%">
                                                    <colgroup span="3"></colgroup>
                                                    <thead>
                                                    <tr class="rich-table-header  ">
                                                        <th class="rich-table-headercell  " colspan="3"
                                                            scope="colgroup"><span class="tituloPre">Durações: </span>
                                                        </th>
                                                    </tr>
                                                    </thead>
                                                </table>
                                                <rich:dataGrid  width="600" id="resultadoConsultaDuracoes" columnClasses="semBorda" styleClass="semBorda"
                                                                value="#{RenovacaoSinteticoControle.listaDuracoes}"
                                                                columns="3"
                                                                cellpadding="0"
                                                                cellspacing="0"
                                                                captionStyle="width:100px"
                                                                elements="#{RenovacaoSinteticoControle.tamanhoListaDuracoes}" var="duracao">

                                                <h:panelGrid columns="2">
                                                    <h:selectBooleanCheckbox id="checkAbaDuracoes" value="#{duracao.marcado}"/>
                                                    <h:outputText styleClass="tituloPre" value="#{duracao.nome}"/>
                                                </h:panelGrid>
                                                </rich:dataGrid>

                                                    <h:outputText escape="false" value= "<br/>" />
                                                    <a4j:commandLink id="botaoAtualizar" title="Atualizar Lista de Cliente" action="#{RenovacaoSinteticoControle.atualizarPrevisaoRenovacaoComFiltros}"
                                                                 reRender="form:panelRelatorioRenovacao,form:mensagem, form:tabPanelFiltros, form:tabPanelGeral,tabPanelResumo,
                                                                       form:tabPanelFiltrosAnalitico, form:tabPanelFiltrosSintetico, form:panelGrafico, totalGeral,form:panelMensagem"
                                                                 style="text-align:left;margin-top:10px;"
                                                                 styleClass=" botoes nvoBt "
                                                                 value="Atualizar">
                                                    <i class="fa-icon-refresh"></i>
                                                    </a4j:commandLink>
                                                </rich:tab>
                                                <rich:tab  id="abaColaboradores" styleClass="tituloPre" label="Colaborador">
                                                    <h:panelGroup>
                                                        <h:panelGrid width="100%">
                                                            <h:panelGroup style="border:0;" id="panelFiltrosUtilizadosConsultor">
                                                                <h:outputText
                                                                    styleClass="tituloCamposAzul"
                                                                    value="#{msg_aplic.prt_SituacaoContratoAnaliticoDW_labelFiltros}"/>

                                                                <rich:spacer width="10px" />
                                                                <rich:toolTip
                                                                    onclick="true"
                                                                    followMouse="true"
                                                                    direction="top-right"
                                                                    style=" background-color:#cedfff; border-color:#000000;text-align: left;"
                                                                    showDelay="500" >
                                                                    ${RenovacaoSinteticoControle.filtros}
                                                                </rich:toolTip>
                                                            </h:panelGroup>
                                                        </h:panelGrid>

                                                    </h:panelGroup>
                                                    <%--<rich:dataGrid  width="100%" id="resultadoConsultaConsultorColaborador" columnClasses="semBorda,semBorda,semBorda" styleClass="semBorda"--%>
                                                                    <%--value="#{RenovacaoSinteticoControle.listaConsultorColaborador}"--%>
                                                                    <%--columns="3"--%>

                                                                    <%--elements="#{RenovacaoSinteticoControle.tamanhoListaConsultor}" var="consultor">--%>
                                                       <%--<f:facet name="header">--%>
                                                            <%--<h:outputText styleClass="tituloPre" value="Filtrar por Colaboradores: "/>--%>
                                                        <%--</f:facet>--%>
                                                        <%--<h:panelGrid columns="2">--%>
                                                            <%--<h:selectBooleanCheckbox id="checkAbaConsultorColaborador" value="#{consultor.marcado}"/>--%>
                                                            <%--<h:outputText styleClass="tituloPre" value="#{consultor.nome}"/>--%>
                                                        <%--</h:panelGrid>--%>

                                                    <%--</rich:dataGrid>--%>

                                                <table width="100%">
                                                     <thead><tr class="rich-table-header"><th class="rich-table-headercell" colspan="3" scope="colgroup"><span class="tituloPre">Filtrar por Consultores:</span></th></tr></thead>
                                                    </table>
                                                    <h:panelGrid columns="3" width="100%">
                                                     <c:forEach varStatus="contadorC" items="#{RenovacaoSinteticoControle.listaVinculosConsultor}" var="consultor">
                                                         <h:panelGrid columns="2">
                                                             <h:selectBooleanCheckbox id="checkAbaConsultorColaborador" value="#{consultor.marcado}"/>
                                                             <h:outputText styleClass="tituloPre" value="#{consultor.nome}"/>
                                                         </h:panelGrid>
                                                <c:if test="${contadorC.last}">
                                                    <c:if test="${(contadorC.count%3)==1}">
                                                     <h:panelGroup/>

                                                      </c:if>

                                                <c:if test="${(contadorC.count%3)==0}">
                                                    <h:panelGroup/>
                                                    <h:panelGroup/>
                                                </c:if>
                                                  </c:if>

                                                 </c:forEach>
                                                     <c:if test="${not empty RenovacaoSinteticoControle.listaVinculosProfessor}">
                                                  <tr class="rich-table-header">
                                                  <th class="rich-table-headercell" colspan="3" scope="colgroup"><span
                                                class="tituloPre">Filtrar por Professor:</span></th>
                                                  </tr>
                                                </c:if>
                                                     <c:forEach varStatus="contadorP" items="#{RenovacaoSinteticoControle.listaVinculosProfessor}" var="consultor">


                                                     <h:panelGrid columns="2">
                                                         <h:selectBooleanCheckbox id="checkAbaConsultorProfessor" value="#{consultor.marcado}"/>
                                                         <h:outputText styleClass="tituloPre" value="#{consultor.nome}"/>
                                                         </h:panelGrid>
                                                     <c:if test="${contadorP.last || empty RenovacaoSinteticoControle.listaVinculosProfessor}">
                                                    <c:if test="${(contadorP.count%3)==1}">
                                                     <h:panelGroup/>

                                                      </c:if>

                                                <c:if test="${(contadorP.count%3)==0}">
                                                    <h:panelGroup/>
                                                    <h:panelGroup/>
                                                </c:if>


                                                  </c:if>

                                                     </c:forEach>
                                                  <c:if test="${not empty RenovacaoSinteticoControle.listaVinculosOutros}">
                                                  <tr class="rich-table-header">
                                                  <th class="rich-table-headercell" colspan="3" scope="colgroup"><span
                                                class="tituloPre">Filtrar por Outros:</span></th>
                                                  </tr>
                                                </c:if>
                                                    <c:forEach varStatus="contadorO" items="#{RenovacaoSinteticoControle.listaVinculosOutros}" var="consultor">


                                                     <h:panelGrid columns="2">
                                                         <h:selectBooleanCheckbox id="checkAbaConsultorOutros" value="#{consultor.marcado}"/>
                                                         <h:outputText styleClass="tituloPre" value="#{consultor.nome}"/>
                                                         </h:panelGrid>
                                                        </c:forEach>
                                                    </h:panelGrid>

                                                    <%--<rich:dataGrid  width="100%" id="resultadoConsultaConsultorProfessor" columnClasses="semBorda,semBorda,semBorda" styleClass="semBorda"--%>
                                                                    <%--value="#{RenovacaoSinteticoControle.listaConsultorProfessor}"--%>
                                                                    <%--columns="3"--%>
                                                                    <%--elements="#{RenovacaoSinteticoControle.tamanhoListaConsultor}" var="consultor">--%>
                                                        <%--<f:facet name="header">--%>
                                                            <%--<h:outputText styleClass="tituloPre" value="Filtrar por Professores: "/>--%>
                                                        <%--</f:facet>--%>
                                                        <%--<h:panelGrid columns="2">--%>
                                                            <%--<h:selectBooleanCheckbox id="checkAbaConsultorProfessor" value="#{consultor.marcado}"/>--%>
                                                            <%--<h:outputText styleClass="tituloPre" value="#{consultor.nome}"/>--%>
                                                        <%--</h:panelGrid>--%>
                                                    <%--</rich:dataGrid>--%>
                                                    <h:outputText escape="false" value= "<br/>" />
                                                    <a4j:commandLink id="botaoAtualizarConsultor" title="Atualizar Lista de Cliente" action="#{RenovacaoSinteticoControle.atualizarPrevisaoRenovacaoComFiltros}"
                                                        reRender="form:panelRelatorioRenovacao,form:mensagem, form:tabPanelFiltros, form:tabPanelGeral,tabPanelResumo,
                                                                       form:tabPanelFiltrosAnalitico, form:tabPanelFiltrosSintetico, form:panelGrafico, totalGeral,form:panelMensagem"
                                                        style="text-align:left;margin-top:10px;"
                                                        styleClass=" botoes nvoBt "
                                                        value="Atualizar">
                                                        <i class="fa-icon-refresh"></i>
                                                    </a4j:commandLink>

                                                </rich:tab>
                                            </rich:tabPanel>

                                        </td>
                                    </tr>
                                      <tr>
                                        <td align="left" valign="top" >
                                             <h:panelGrid id="panelMensagem" columns="3" width="100%" styleClass="tabMensagens">
                                                        <h:panelGrid columns="1" width="100%">
                                                            <h:outputText styleClass="mensagem"  value="#{RenovacaoSinteticoControle.mensagem}"/>
                                                            <h:outputText styleClass="mensagemDetalhada" value="#{RenovacaoSinteticoControle.mensagemDetalhada}"/>
                                                        </h:panelGrid>
                                            </h:panelGrid>

                                        </td>
                                    </tr>
                                    <tr>
                                        <td align="left" valign="top" >
                                            <rich:spacer style="display:block;" height="10"/>
                                            <rich:spacer width="100"/>


                                            <rich:spacer width="50"/>
                                            <h:outputText styleClass="tituloCamposAzul" value="Total de Contratos Filtrados e Previstos no Período: "/>
                                            <h:outputText id="totalGeral" value="#{RenovacaoSinteticoControle.qtdeTotal}"/>
                                            <rich:spacer style="display:block;" height="5"/>
                                        </td>
                                    </tr>

                                    <tr>
                                        <td align="left" valign="top" width="100%"  style="padding:7px 20px 0 20px;">

                                            <h:panelGroup id="panelRelatorioRenovacao" layout="block" styleClass="container-box zw_ui especial " style="margin: 0px !important;width: 100% !important;">
                                                            <h:panelGrid width="100">
                                                                <rich:tabPanel id="tabPanelGeral" width="100%">
                                                                    <rich:tab switchType="client" styleClass="tituloPre" label="Analítico" >
                                                                                <h:panelGrid style="text-align:right;">
                                                                <h:panelGroup layout="block" rendered="#{RenovacaoSinteticoControle.apresentarExportacao}">
                                                                                        <rich:spacer width="5px"/>

                                                                    <a4j:commandLink id="exportarExcelRenAnalitico"
                                                                                     actionListener="#{RenovacaoSinteticoControle.exportar}"
                                                                                     style="margin-bottom:5px;"
                                                                                     oncomplete="#{RenovacaoSinteticoControle.mensagemNotificar}#{RenovacaoSinteticoControle.msgAlert}"
                                                                                     accesskey="2" styleClass="botoes">
                                                                        <f:attribute name="tipo" value="xls"/>
                                                                        <f:attribute name="itemExportacao" value="relRenovacao"/>
                                                                        <f:attribute name="atributos" value="nomeApresentar=Nome,situacaoRenovacao_Apresentar=Situação,planoApresentar=Plano,previsao=Previsão,duracao=Duração,modalidadesConcatenado=Modalidades,valorBaseCalculoApresentar=Valor Contrato,planoRenovadoDate=Data Renovação,planoRenovadoDuracao=Duração,planoRenovadoModalidades=Modalidade,planoRenovadoResponsavelCadastro=Reponsavel Cadastro,foneCliente=Telefone,emailCliente=Email,ultimoContatoCRM_Apresentar=Último CRM,diasSemAcesso=Dias Sem Acesso,mediaAcessos4semanas_Apresentar=MédiaAcessos4semanas,checkin4semanas=AulasColetivas4semanas,professores=Professores"/>
                                                                        <f:attribute name="prefixo" value="RenovacaoPorContrato"/>
                                                                        <h:outputText title="Exportar para o formato Excel" styleClass="btn-print-2 excel"/>
                                                                    </a4j:commandLink>
                                                                                        <rich:spacer width="5px"/>
                                                                    <a4j:commandLink id="imprimir" value="#{msg_aplic.prt_SituacaoContratoAnaliticoDW_imprimir}"
                                                                                     title="#{msg_aplic.prt_SituacaoContratoAnaliticoDW_imprimir}"
                                                                                     action="#{RenovacaoSinteticoControle.imprimirRelatorio}"
                                                                                     oncomplete="#{RenovacaoSinteticoControle.mensagemNotificar}#{RenovacaoSinteticoControle.msgAlert}"
                                                                                     styleClass="botoes nvoBt btSec">
                                                                        <i class="fa-icon-print"></i>
                                                                    </a4j:commandLink>

                                                                                        <rich:spacer width="5px"/>
                                                                    <h:outputText value="A ordenação para exportação deverá ser na primeira Aba"/>

                                                                                    </h:panelGroup>
                                                                                </h:panelGrid>
                                                            <rich:tabPanel id="tabPanelFiltrosAnalitico" width="100%">
                                                                <rich:tab switchType="client" rendered="#{RenovacaoSinteticoControle.aprensetarLista1}" styleClass="tituloPre" label="#{RenovacaoSinteticoControle.mes1} / #{RenovacaoSinteticoControle.ano1} " >
                                                                                <rich:dataTable id="tabelaRelatorioAnaliticoExterna" width="1000" rowClasses="linhaImpar, linhaPar" styleClass="semBorda" columnClasses="colunaCentralizada"
                                                                                                  value="#{RenovacaoSinteticoControle.listaPrevisaoRenovacao1}" var="listaPrevisao">
                                                                                    <rich:column >
                                                                                        <rich:dataTable id="tabelaRelatorioAnalitico" width="1000" rowClasses="linhaImpar, linhaPar"  columnClasses="colunaEsquerda,colunaCentralizada,colunaCentralizada,colunaCentralizada,colunaCentralizada,colunaCentralizada,colunaCentralizada,colunaCentralizada"
                                                                                                        binding="#{RenovacaoSinteticoControle.dataTableRenovacao}"  value="#{listaPrevisao.listaRenovacaoSinteticoVOs}" var="renovacao" rowKeyVar="status">

                                                                                            <%@include file="pages/ce/includes/include_contador_richtable.jsp" %>

                                                                                            <rich:column sortBy="#{renovacao.contrato.pessoa.nome}" id="nomeClienteApresentar" filterEvent="onkeyup" >
                                                                                                <f:facet name="header">
                                                                                                    <h:outputText value="Nome"/>
                                                                                                </f:facet>
                                                                                                <a4j:commandLink  action="#{RenovacaoSinteticoControle.irParaTelaCliente}" title="Ir para tela de Edição do Cliente"  value="#{renovacao.contrato.pessoa.nome}" oncomplete="abrirPopup('clienteNav.jsp?page=viewCliente', 'Cliente', 1024, 700);" >
                                                                                                    <f:param name="state" value="AC"/>
                                                                                                </a4j:commandLink>
                                                                                            </rich:column>

                                                                                            <rich:column sortBy="#{renovacao.situacaoRenovacao_Apresentar}"  id="situacaoRenovacao_Apresentar" filterEvent="onkeyup">
                                                                                                <f:facet name="header">
                                                                                                    <h:outputText value="Situação"/>
                                                                                                </f:facet>
                                                                                                <a4j:commandLink action="#{RenovacaoSinteticoControle.irParaTelaCliente}" title="Ir para tela de Edição do Cliente"  value="#{renovacao.situacaoRenovacao_Apresentar}" oncomplete="abrirPopup('clienteNav.jsp?page=viewCliente', 'Cliente', 1024, 700);">
                                                                                                    <f:param name="state" value="AC"/>
                                                                                                </a4j:commandLink>
                                                                                            </rich:column>

                                                                                            <rich:column sortBy="#{renovacao.planoApresentar}" id="planoApresentar" filterEvent="onkeyup" >
                                                                                                <f:facet name="header">
                                                                                                    <h:outputText value="Plano"/>
                                                                                                </f:facet>
                                                                                                <a4j:commandLink
                                                                                                        action="#{RenovacaoSinteticoControle.irParaTelaCliente}"
                                                                                                        title="Ir para tela de Edição do Cliente"
                                                                                                        value="#{renovacao.planoApresentar}"
                                                                                                        oncomplete="abrirPopup('clienteNav.jsp?page=viewCliente', 'Cliente', 1024, 700);">
                                                                                                    <f:param name="state" value="AC"/>
                                                                                                </a4j:commandLink>
                                                                                            </rich:column>

                                                                                            <rich:column sortBy="#{renovacao.contrato.dataPrevistaRenovar}" id="dataPrevistaRenovarApresentar" filterEvent="onkeyup">
                                                                                                <f:facet name="header">
                                                                                                    <h:outputText value="Previsão"/>
                                                                                                </f:facet>
                                                                                                <a4j:commandLink action="#{RenovacaoSinteticoControle.irParaTelaCliente}" title="Ir para tela de Edição do Cliente"  value="#{renovacao.contrato.dataPrevistaRenovar_Apresentar}" oncomplete="abrirPopup('clienteNav.jsp?page=viewCliente', 'Cliente', 1024, 700);" >
                                                                                                    <f:param name="state" value="AC"/>
                                                                                                </a4j:commandLink>
                                                                                            </rich:column>

                                                                                            <rich:column sortBy="#{renovacao.contrato.contratoDuracao.numeroMeses}" id="duracaoMesesApresentar" filterEvent="onkeyup">
                                                                                                <f:facet name="header">
                                                                                                    <h:outputText value="Duração"/>
                                                                                                </f:facet>
                                                                                                <a4j:commandLink  action="#{RenovacaoSinteticoControle.irParaTelaCliente}" title="Ir para tela de Edição do Cliente" value="#{renovacao.contrato.contratoDuracao.numeroMeses}" oncomplete="abrirPopup('clienteNav.jsp?page=viewCliente', 'Cliente', 1024, 700);" >
                                                                                                    <f:param name="state" value="AC"/>
                                                                                                </a4j:commandLink>
                                                                                            </rich:column>

                                                                                            <rich:column sortBy="#{renovacao.contrato.nomeModalidadesConcatenado}" id="nomeModalidadesApresentar" filterEvent="onkeyup">
                                                                                                <f:facet name="header">
                                                                                                    <h:outputText value="Modalidades"/>
                                                                                                </f:facet>
                                                                                                <a4j:commandLink  action="#{RenovacaoSinteticoControle.irParaTelaCliente}" title="Ir para tela de Edição do Cliente" value="#{renovacao.contrato.nomeModalidadesConcatenado}" oncomplete="abrirPopup('clienteNav.jsp?page=viewCliente', 'Cliente', 1024, 700);">
                                                                                                    <f:param name="state" value="AC"/>
                                                                                                </a4j:commandLink>
                                                                                            </rich:column>

                                                                                            <rich:column sortBy="#{renovacao.contrato.valorBaseCalculo}" id="valorBaseContrato" filterEvent="onkeyup">
                                                                                                <f:facet name="header">
                                                                                                    <h:outputText value="Valor Contrato"/>
                                                                                                </f:facet>
                                                                                                <a4j:commandLink  action="#{RenovacaoSinteticoControle.irParaTelaCliente}" title="Ir para tela de Edição do Cliente" value="#{renovacao.contrato.valorBaseCalculoApresentar}" oncomplete="abrirPopup('clienteNav.jsp?page=viewCliente', 'Cliente', 1024, 700);">
                                                                                                    <f:param name="state" value="AC"/>
                                                                                                </a4j:commandLink>
                                                                                            </rich:column>
                                                                                            <rich:column sortBy="#{renovacao.diasSemAcesso}" id="diasSemAcesso" filterEvent="onkeyup">
                                                                                                <f:facet name="header">
                                                                                                    <h:outputText title="Quantidade de dias que o cliente está sem acessar a academia" value="Dias Sem Acesso"/>
                                                                                                </f:facet>
                                                                                                <a4j:commandLink  action="#{RenovacaoSinteticoControle.irParaTelaCliente}" title="Ir para tela de Edição do Cliente" value="#{renovacao.diasSemAcesso}" oncomplete="abrirPopup('clienteNav.jsp?page=viewCliente', 'Cliente', 1024, 700);">
                                                                                                    <f:param name="state" value="AC"/>
                                                                                                </a4j:commandLink>
                                                                                            </rich:column>
                                                                                            <rich:column sortBy="#{renovacao.mediaAcessos4semanas}" id="mediaAcessos4semanas" filterEvent="onkeyup">
                                                                                                <f:facet name="header">
                                                                                                    <h:outputText title="Média de acessos nas últimas 4 semanas" value="Média Acessos 4 Semanas"/>
                                                                                                </f:facet>
                                                                                                <a4j:commandLink  action="#{RenovacaoSinteticoControle.irParaTelaCliente}" title="Ir para tela de Edição do Cliente" value="#{renovacao.mediaAcessos4semanas}" oncomplete="abrirPopup('clienteNav.jsp?page=viewCliente', 'Cliente', 1024, 700);">
                                                                                                    <f:param name="state" value="AC"/>
                                                                                                </a4j:commandLink>
                                                                                            </rich:column>
                                                                                            <rich:column sortBy="#{renovacao.checkin4semanas}" id="checkin4semanas" filterEvent="onkeyup">
                                                                                                <f:facet name="header">
                                                                                                    <h:outputText title="Quantidade de check-in em aulas  nas últimas 4 semanas"  value="Aulas Coletivas 4 semanas"/>
                                                                                                </f:facet>
                                                                                                <a4j:commandLink  action="#{RenovacaoSinteticoControle.irParaTelaCliente}" title="Ir para tela de Edição do Cliente" value="#{renovacao.checkin4semanas}" oncomplete="abrirPopup('clienteNav.jsp?page=viewCliente', 'Cliente', 1024, 700);">
                                                                                                    <f:param name="state" value="AC"/>
                                                                                                </a4j:commandLink>
                                                                                            </rich:column>
                                                                                            <rich:column sortBy="#{renovacao.ultimoContatoCRM}" id="ultimoContatoCRM" filterEvent="onkeyup">
                                                                                                <f:facet name="header">
                                                                                                    <h:outputText title="Último registro de contato no CRM"  value="Último CRM"/>
                                                                                                </f:facet>
                                                                                                <a4j:commandLink  action="#{RenovacaoSinteticoControle.irParaTelaCliente}" title="Ir para tela de Edição do Cliente" value="#{renovacao.ultimoContatoCRM_Apresentar}" oncomplete="abrirPopup('clienteNav.jsp?page=viewCliente', 'Cliente', 1024, 700);">
                                                                                                    <f:param name="state" value="AC"/>
                                                                                                </a4j:commandLink>
                                                                                            </rich:column>

                                                                                            <rich:column sortBy="#{renovacao.planoRenovadoDate}" id="planoRenovadoDateApresentar" filterEvent="onkeyup">
                                                                                                <f:facet name="header">
                                                                                                    <h:outputText value="Data Renovação"/>
                                                                                                </f:facet>
                                                                                                <a4j:commandLink  action="#{RenovacaoSinteticoControle.irParaTelaCliente}" title="Ir para tela de Edição do Cliente" value="#{renovacao.planoRenovado}" oncomplete="abrirPopup('clienteNav.jsp?page=viewCliente', 'Cliente', 1024, 700);">
                                                                                                    <f:param name="state" value="AC"/>
                                                                                                </a4j:commandLink>
                                                                                            </rich:column>

                                                                                            <rich:column sortBy="#{renovacao.planoRenovadoDuracao}" id="planoRenovadoDuracao" filterEvent="onkeyup">
                                                                                                <f:facet name="header">
                                                                                                    <h:outputText value="Duração"/>
                                                                                                </f:facet>
                                                                                                <a4j:commandLink action="#{RenovacaoSinteticoControle.irParaTelaCliente}" title="Ir para tela de Edição do Cliente" value="#{renovacao.planoRenovadoDuracao}" oncomplete="abrirPopup('clienteNav.jsp?page=viewCliente', 'Cliente', 1024, 700);">
                                                                                                    <f:param name="state" value="AC"/>
                                                                                                </a4j:commandLink>
                                                                                            </rich:column>

                                                                                            <rich:column sortBy="#{renovacao.planoRenovadoModalidades}" id="planoRenovadoModalidades" filterEvent="onkeyup">
                                                                                                <f:facet name="header">
                                                                                                    <h:outputText value="Modalidade"/>
                                                                                                </f:facet>
                                                                                                <a4j:commandLink action="#{RenovacaoSinteticoControle.irParaTelaCliente}" title="Ir para tela de Edição do Cliente" value="#{renovacao.planoRenovadoModalidades}" oncomplete="abrirPopup('clienteNav.jsp?page=viewCliente', 'Cliente', 1024, 700);">
                                                                                                    <f:param name="state" value="AC"/>
                                                                                                </a4j:commandLink>
                                                                                            </rich:column>

                                                                                            <rich:column sortBy="#{renovacao.planoRenovadoResponsavelCadastro}" id="planoRenovadoResponsavelCadastro" filterEvent="onkeyup">
                                                                                                <f:facet name="header">
                                                                                                    <h:outputText value="Responsavel Contrato"/>
                                                                                                </f:facet>
                                                                                                <a4j:commandLink action="#{RenovacaoSinteticoControle.irParaTelaCliente}" title="Ir para tela de Edição do Cliente" value="#{renovacao.planoRenovadoResponsavelCadastro}" oncomplete="abrirPopup('clienteNav.jsp?page=viewCliente', 'Cliente', 1024, 700);">
                                                                                                    <f:param name="state" value="AC"/>
                                                                                                </a4j:commandLink>
                                                                                            </rich:column>
                                                                                        </rich:dataTable>
                                                                                    </rich:column>
                                                                                </rich:dataTable>
                                                                            </rich:tab>
                                                                            <rich:tab switchType="client" rendered="#{RenovacaoSinteticoControle.aprensetarLista2}" styleClass="tituloPre" label="#{RenovacaoSinteticoControle.mes2} / #{RenovacaoSinteticoControle.ano2} ">
                                                                                <rich:dataTable width="1000" rowClasses="linhaImpar, linhaPar"  styleClass="semBorda" columnClasses="colunaCentralizada"
                                                                                                value="#{RenovacaoSinteticoControle.listaPrevisaoRenovacao2}" var="listaPrevisao">
                                                                                    <rich:column >
                                                                                        <rich:dataTable width="1000" rowClasses="linhaImpar, linhaPar"  columnClasses="colunaEsquerda,colunaCentralizada,colunaCentralizada,colunaCentralizada,colunaCentralizada,colunaCentralizada,colunaCentralizada,colunaCentralizada"
                                                                                                        value="#{listaPrevisao.listaRenovacaoSinteticoVOs}" var="renovacao" rowKeyVar="status">

                                                                                            <%@include file="pages/ce/includes/include_contador_richtable.jsp" %>

                                                                                            <rich:column sortBy="#{renovacao.contrato.pessoa.nome}"  filterEvent="onkeyup" >
                                                                                                <f:facet name="header">
                                                                                                    <h:outputText value="Nome"/>
                                                                                                </f:facet>
                                                                                                <a4j:commandLink  action="#{RenovacaoSinteticoControle.irParaTelaCliente}" title="Ir para tela de Edição do Cliente"  value="#{renovacao.contrato.pessoa.nome}" oncomplete="abrirPopup('clienteNav.jsp?page=viewCliente', 'Cliente', 1024, 700);">
                                                                                                    <f:param name="state" value="AC"/>
                                                                                                </a4j:commandLink>
                                                                                            </rich:column>
                                                                                            <rich:column sortBy="#{renovacao.situacaoRenovacao_Apresentar}" filterEvent="onkeyup">
                                                                                                <f:facet name="header">
                                                                                                    <h:outputText value="Situação"/>
                                                                                                </f:facet>
                                                                                                <a4j:commandLink  action="#{RenovacaoSinteticoControle.irParaTelaCliente}" title="Ir para tela de Edição do Cliente"  value="#{renovacao.situacaoRenovacao_Apresentar}" oncomplete="abrirPopup('clienteNav.jsp?page=viewCliente', 'Cliente', 1024, 700);">
                                                                                                    <f:param name="state" value="AC"/>
                                                                                                </a4j:commandLink>
                                                                                            </rich:column>
                                                                                            <rich:column sortBy="#{renovacao.contrato.dataPrevistaRenovar}" filterEvent="onkeyup">
                                                                                                <f:facet name="header">
                                                                                                    <h:outputText value="Previsão"/>
                                                                                                </f:facet>
                                                                                                <a4j:commandLink  action="#{RenovacaoSinteticoControle.irParaTelaCliente}" title="Ir para tela de Edição do Cliente"  value="#{renovacao.contrato.dataPrevistaRenovar_Apresentar}" oncomplete="abrirPopup('clienteNav.jsp?page=viewCliente', 'Cliente', 1024, 700);">
                                                                                                    <f:param name="state" value="AC"/>
                                                                                                </a4j:commandLink>
                                                                                            </rich:column>
                                                                                            <rich:column sortBy="#{renovacao.contrato.contratoDuracao.numeroMeses}" filterEvent="onkeyup">
                                                                                                <f:facet name="header">
                                                                                                    <h:outputText value="Duração"/>
                                                                                                </f:facet>
                                                                                                <a4j:commandLink  action="#{RenovacaoSinteticoControle.irParaTelaCliente}" title="Ir para tela de Edição do Cliente" value="#{renovacao.contrato.contratoDuracao.numeroMeses}" oncomplete="abrirPopup('clienteNav.jsp?page=viewCliente', 'Cliente', 1024, 700);">
                                                                                                    <f:param name="state" value="AC"/>
                                                                                                </a4j:commandLink>
                                                                                            </rich:column>
                                                                                            <rich:column sortBy="#{renovacao.contrato.nomeModalidadesConcatenado}" filterEvent="onkeyup">
                                                                                                <f:facet name="header">
                                                                                                    <h:outputText value="Modalidades"/>
                                                                                                </f:facet>
                                                                                                <a4j:commandLink  action="#{RenovacaoSinteticoControle.irParaTelaCliente}" title="Ir para tela de Edição do Cliente" value="#{renovacao.contrato.nomeModalidadesConcatenado}" oncomplete="abrirPopup('clienteNav.jsp?page=viewCliente', 'Cliente', 1024, 700);">
                                                                                                    <f:param name="state" value="AC"/>
                                                                                                </a4j:commandLink>
                                                                                            </rich:column>
                                                                                            <rich:column sortBy="#{renovacao.planoRenovadoDate}" filterEvent="onkeyup">
                                                                                                <f:facet name="header">
                                                                                                    <h:outputText value="Data Renovação"/>
                                                                                                </f:facet>
                                                                                                <a4j:commandLink  action="#{RenovacaoSinteticoControle.irParaTelaCliente}" title="Ir para tela de Edição do Cliente"  value="#{renovacao.planoRenovado}" oncomplete="abrirPopup('clienteNav.jsp?page=viewCliente', 'Cliente', 1024, 700);">
                                                                                                    <f:param name="state" value="AC"/>
                                                                                                </a4j:commandLink>
                                                                                            </rich:column>
                                                                                            <rich:column sortBy="#{renovacao.planoRenovadoDuracao}" filterEvent="onkeyup">
                                                                                                <f:facet name="header">
                                                                                                    <h:outputText value="DuraR"/>
                                                                                                </f:facet>
                                                                                                <a4j:commandLink  action="#{RenovacaoSinteticoControle.irParaTelaCliente}" title="Ir para tela de Edição do Cliente" value="#{renovacao.planoRenovadoDuracao}" oncomplete="abrirPopup('clienteNav.jsp?page=viewCliente', 'Cliente', 1024, 700);">
                                                                                                    <f:param name="state" value="AC"/>
                                                                                                </a4j:commandLink>
                                                                                            </rich:column>
                                                                                            <rich:column sortBy="#{renovacao.planoRenovadoModalidades}" filterEvent="onkeyup">
                                                                                                <f:facet name="header">
                                                                                                    <h:outputText value="ModalidR"/>
                                                                                                </f:facet>
                                                                                                <a4j:commandLink  action="#{RenovacaoSinteticoControle.irParaTelaCliente}" title="Ir para tela de Edição do Cliente" value="#{renovacao.planoRenovadoModalidades}" oncomplete="abrirPopup('clienteNav.jsp?page=viewCliente', 'Cliente', 1024, 700);" >
                                                                                                    <f:param name="state" value="AC"/>
                                                                                                </a4j:commandLink>
                                                                                            </rich:column>

                                                                                            <rich:column sortBy="#{renovacao.planoRenovadoResponsavelCadastro}" filterEvent="onkeyup">
                                                                                                <f:facet name="header">
                                                                                                    <h:outputText value="RespContrato"/>
                                                                                                </f:facet>
                                                                                                <a4j:commandLink action="#{RenovacaoSinteticoControle.irParaTelaCliente}" title="Ir para tela de Edição do Cliente" value="#{renovacao.planoRenovadoResponsavelCadastro}" oncomplete="abrirPopup('clienteNav.jsp?page=viewCliente', 'Cliente', 1024, 700);">
                                                                                                    <f:param name="state" value="AC"/>
                                                                                                </a4j:commandLink>
                                                                                            </rich:column>
                                                                                        </rich:dataTable>
                                                                                    </rich:column>
                                                                                </rich:dataTable>
                                                                            </rich:tab>
                                                                            <rich:tab switchType="client" rendered="#{RenovacaoSinteticoControle.aprensetarLista3}" styleClass="tituloPre" label="#{RenovacaoSinteticoControle.mes3} / #{RenovacaoSinteticoControle.ano3} ">
                                                                                <rich:dataTable width="1000" rowClasses="linhaImpar, linhaPar"  styleClass="semBorda" columnClasses="colunaCentralizada"
                                                                                                value="#{RenovacaoSinteticoControle.listaPrevisaoRenovacao3}" var="listaPrevisao">
                                                                                    <rich:column >
                                                                                        <rich:dataTable width="1000" rowClasses="linhaImpar, linhaPar"  columnClasses="colunaEsquerda,colunaCentralizada,colunaCentralizada,colunaCentralizada,colunaCentralizada,colunaCentralizada,colunaCentralizada,colunaCentralizada"
                                                                                                        value="#{listaPrevisao.listaRenovacaoSinteticoVOs}" var="renovacao" rowKeyVar="status">

                                                                                            <%@include file="pages/ce/includes/include_contador_richtable.jsp" %>

                                                                                            <rich:column sortBy="#{renovacao.contrato.pessoa.nome}"  filterEvent="onkeyup" >
                                                                                                <f:facet name="header">
                                                                                                    <h:outputText value="Nome"/>
                                                                                                </f:facet>
                                                                                    <a4j:commandLink  action="#{RenovacaoSinteticoControle.irParaTelaCliente}" title="Ir para tela de Edição do Cliente"  value="#{renovacao.contrato.pessoa.nome}" oncomplete="abrirPopup('clienteNav.jsp?page=viewCliente', 'Cliente', 1024, 700);">
                                                                                                    <f:param name="state" value="AC"/>
                                                                                                </a4j:commandLink>
                                                                                            </rich:column>
                                                                                            <rich:column sortBy="#{renovacao.situacaoRenovacao_Apresentar}" filterEvent="onkeyup">
                                                                                                <f:facet name="header">
                                                                                                    <h:outputText value="Situação"/>
                                                                                                </f:facet>
                                                                                                <a4j:commandLink  action="#{RenovacaoSinteticoControle.irParaTelaCliente}" title="Ir para tela de Edição do Cliente"  value="#{renovacao.situacaoRenovacao_Apresentar}" oncomplete="abrirPopup('clienteNav.jsp?page=viewCliente', 'Cliente', 1024, 700);">
                                                                                                    <f:param name="state" value="AC"/>
                                                                                                </a4j:commandLink>
                                                                                            </rich:column>
                                                                                            <rich:column sortBy="#{renovacao.contrato.dataPrevistaRenovar}" filterEvent="onkeyup">
                                                                                                <f:facet name="header">
                                                                                                    <h:outputText value="Previsão"/>
                                                                                                </f:facet>
                                                                                                <a4j:commandLink  action="#{RenovacaoSinteticoControle.irParaTelaCliente}" title="Ir para tela de Edição do Cliente"  value="#{renovacao.contrato.dataPrevistaRenovar_Apresentar}" oncomplete="abrirPopup('clienteNav.jsp?page=viewCliente', 'Cliente', 1024, 700);">
                                                                                                    <f:param name="state" value="AC"/>
                                                                                                </a4j:commandLink>
                                                                                            </rich:column>
                                                                                            <rich:column sortBy="#{renovacao.contrato.contratoDuracao.numeroMeses}" filterEvent="onkeyup">
                                                                                                <f:facet name="header">
                                                                                                    <h:outputText value="Duração"/>
                                                                                                </f:facet>
                                                                                                <a4j:commandLink  action="#{RenovacaoSinteticoControle.irParaTelaCliente}" title="Ir para tela de Edição do Cliente" value="#{renovacao.contrato.contratoDuracao.numeroMeses}" oncomplete="abrirPopup('clienteNav.jsp?page=viewCliente', 'Cliente', 1024, 700);" >
                                                                                                    <f:param name="state" value="AC"/>
                                                                                                </a4j:commandLink>
                                                                                            </rich:column>
                                                                                            <rich:column sortBy="#{renovacao.contrato.nomeModalidadesConcatenado}" filterEvent="onkeyup">
                                                                                                <f:facet name="header">
                                                                                                    <h:outputText value="Modalidades"/>
                                                                                                </f:facet>
                                                                                                <a4j:commandLink  action="#{RenovacaoSinteticoControle.irParaTelaCliente}" title="Ir para tela de Edição do Cliente" value="#{renovacao.contrato.nomeModalidadesConcatenado}" oncomplete="abrirPopup('clienteNav.jsp?page=viewCliente', 'Cliente', 1024, 700);">
                                                                                                    <f:param name="state" value="AC"/>
                                                                                                </a4j:commandLink>
                                                                                            </rich:column>
                                                                                            <rich:column sortBy="#{renovacao.planoRenovadoDate}" filterEvent="onkeyup">
                                                                                                <f:facet name="header">
                                                                                                    <h:outputText value="Data Renovação"/>
                                                                                                </f:facet>
                                                                                                <a4j:commandLink  action="#{RenovacaoSinteticoControle.irParaTelaCliente}" title="Ir para tela de Edição do Cliente" value="#{renovacao.planoRenovado}" oncomplete="abrirPopup('clienteNav.jsp?page=viewCliente', 'Cliente', 1024, 700);">
                                                                                                    <f:param name="state" value="AC"/>
                                                                                                </a4j:commandLink>
                                                                                            </rich:column>
                                                                                            <rich:column sortBy="#{renovacao.planoRenovadoDuracao}" filterEvent="onkeyup">
                                                                                                <f:facet name="header">
                                                                                                    <h:outputText value="DuraR"/>
                                                                                                </f:facet>
                                                                                                <a4j:commandLink  action="#{RenovacaoSinteticoControle.irParaTelaCliente}" title="Ir para tela de Edição do Cliente" value="#{renovacao.planoRenovadoDuracao}" oncomplete="abrirPopup('clienteNav.jsp?page=viewCliente', 'Cliente', 1024, 700);">
                                                                                                    <f:param name="state" value="AC"/>
                                                                                                </a4j:commandLink>
                                                                                            </rich:column>
                                                                                            <rich:column sortBy="#{renovacao.planoRenovadoModalidades}" filterEvent="onkeyup">
                                                                                                <f:facet name="header">
                                                                                                    <h:outputText value="ModalidR"/>
                                                                                                </f:facet>
                                                                                                <a4j:commandLink  action="#{RenovacaoSinteticoControle.irParaTelaCliente}" title="Ir para tela de Edição do Cliente" value="#{renovacao.planoRenovadoModalidades}" oncomplete="abrirPopup('clienteNav.jsp?page=viewCliente', 'Cliente', 1024, 700);">
                                                                                                    <f:param name="state" value="AC"/>
                                                                                                </a4j:commandLink>
                                                                                            </rich:column>

                                                                                            <rich:column sortBy="#{renovacao.planoRenovadoResponsavelCadastro}" filterEvent="onkeyup">
                                                                                                <f:facet name="header">
                                                                                                    <h:outputText value="RespContrato"/>
                                                                                                </f:facet>
                                                                                                <a4j:commandLink action="#{RenovacaoSinteticoControle.irParaTelaCliente}" title="Ir para tela de Edição do Cliente" value="#{renovacao.planoRenovadoResponsavelCadastro}" oncomplete="abrirPopup('clienteNav.jsp?page=viewCliente', 'Cliente', 1024, 700);">
                                                                                                    <f:param name="state" value="AC"/>
                                                                                                </a4j:commandLink>
                                                                                            </rich:column>
                                                                                        </rich:dataTable>
                                                                                    </rich:column>
                                                                                </rich:dataTable>
                                                                            </rich:tab>
                                                                            <rich:tab switchType="client" rendered="#{RenovacaoSinteticoControle.aprensetarLista4}" styleClass="tituloPre" label="#{RenovacaoSinteticoControle.mes4} / #{RenovacaoSinteticoControle.ano4} " >
                                                                                <rich:dataTable width="1000" rowClasses="linhaImpar, linhaPar" styleClass="semBorda" columnClasses="colunaCentralizada"
                                                                                                value="#{RenovacaoSinteticoControle.listaPrevisaoRenovacao4}" var="listaPrevisao">
                                                                                    <rich:column >
                                                                                        <rich:dataTable width="1000" rowClasses="linhaImpar, linhaPar"  columnClasses="colunaEsquerda,colunaCentralizada,colunaCentralizada,colunaCentralizada,colunaCentralizada,colunaCentralizada,colunaCentralizada,colunaCentralizada"
                                                                                                        value="#{listaPrevisao.listaRenovacaoSinteticoVOs}" var="renovacao">
                                                                                            <rich:column sortBy="#{renovacao.contrato.pessoa.nome}"  filterEvent="onkeyup" >
                                                                                                <f:facet name="header">
                                                                                                    <h:outputText value="Nome"/>
                                                                                                </f:facet>
                                                                                                <a4j:commandLink  action="#{RenovacaoSinteticoControle.irParaTelaCliente}" title="Ir para tela de Edição do Cliente"  value="#{renovacao.contrato.pessoa.nome}" oncomplete="abrirPopup('clienteNav.jsp?page=viewCliente', 'Cliente', 1024, 700);">
                                                                                                    <f:param name="state" value="AC"/>
                                                                                                </a4j:commandLink>
                                                                                            </rich:column>
                                                                                            <rich:column sortBy="#{renovacao.situacaoRenovacao_Apresentar}" filterEvent="onkeyup">
                                                                                                <f:facet name="header">
                                                                                                    <h:outputText value="Situação"/>
                                                                                                </f:facet>
                                                                                                <a4j:commandLink  action="#{RenovacaoSinteticoControle.irParaTelaCliente}" title="Ir para tela de Edição do Cliente" value="#{renovacao.situacaoRenovacao_Apresentar}" oncomplete="abrirPopup('clienteNav.jsp?page=viewCliente', 'Cliente', 1024, 700);">
                                                                                                    <f:param name="state" value="AC"/>
                                                                                                </a4j:commandLink>
                                                                                            </rich:column>
                                                                                            <rich:column sortBy="#{renovacao.contrato.dataPrevistaRenovar}" filterEvent="onkeyup">
                                                                                                <f:facet name="header">
                                                                                                    <h:outputText value="Previsão"/>
                                                                                                </f:facet>
                                                                                                <a4j:commandLink  action="#{RenovacaoSinteticoControle.irParaTelaCliente}" title="Ir para tela de Edição do Cliente" value="#{renovacao.contrato.dataPrevistaRenovar_Apresentar}" oncomplete="abrirPopup('clienteNav.jsp?page=viewCliente', 'Cliente', 1024, 700);">
                                                                                                    <f:param name="state" value="AC"/>
                                                                                                </a4j:commandLink>
                                                                                            </rich:column>
                                                                                            <rich:column sortBy="#{renovacao.contrato.contratoDuracao.numeroMeses}" filterEvent="onkeyup">
                                                                                                <f:facet name="header">
                                                                                                    <h:outputText value="Duração"/>
                                                                                                </f:facet>
                                                                                                <a4j:commandLink  action="#{RenovacaoSinteticoControle.irParaTelaCliente}" title="Ir para tela de Edição do Cliente" value="#{renovacao.contrato.contratoDuracao.numeroMeses}" oncomplete="abrirPopup('clienteNav.jsp?page=viewCliente', 'Cliente', 1024, 700);">
                                                                                                    <f:param name="state" value="AC"/>
                                                                                                </a4j:commandLink>
                                                                                            </rich:column>
                                                                                            <rich:column sortBy="#{renovacao.contrato.nomeModalidadesConcatenado}" filterEvent="onkeyup">
                                                                                                <f:facet name="header">
                                                                                                    <h:outputText value="Modalidades"/>
                                                                                                </f:facet>
                                                                                                <a4j:commandLink  action="#{RenovacaoSinteticoControle.irParaTelaCliente}" title="Ir para tela de Edição do Cliente" value="#{renovacao.contrato.nomeModalidadesConcatenado}" oncomplete="abrirPopup('clienteNav.jsp?page=viewCliente', 'Cliente', 1024, 700);">
                                                                                                    <f:param name="state" value="AC"/>
                                                                                                </a4j:commandLink>
                                                                                            </rich:column>
                                                                                            <rich:column sortBy="#{renovacao.planoRenovadoDate}" filterEvent="onkeyup">
                                                                                                <f:facet name="header">
                                                                                                    <h:outputText value="Data Renovação"/>
                                                                                                </f:facet>
                                                                                                <a4j:commandLink  action="#{RenovacaoSinteticoControle.irParaTelaCliente}" title="Ir para tela de Edição do Cliente"  value="#{renovacao.planoRenovado}" oncomplete="abrirPopup('clienteNav.jsp?page=viewCliente', 'Cliente', 1024, 700);">
                                                                                                    <f:param name="state" value="AC"/>
                                                                                                </a4j:commandLink>
                                                                                            </rich:column>
                                                                                            <rich:column sortBy="#{renovacao.planoRenovadoDuracao}" filterEvent="onkeyup">
                                                                                                <f:facet name="header">
                                                                                                    <h:outputText value="DuraR"/>
                                                                                                </f:facet>
                                                                                                <a4j:commandLink  action="#{RenovacaoSinteticoControle.irParaTelaCliente}" title="Ir para tela de Edição do Cliente" value="#{renovacao.planoRenovadoDuracao}" oncomplete="abrirPopup('clienteNav.jsp?page=viewCliente', 'Cliente', 1024, 700);">
                                                                                                    <f:param name="state" value="AC"/>
                                                                                                </a4j:commandLink>
                                                                                            </rich:column>
                                                                                            <rich:column sortBy="#{renovacao.planoRenovadoModalidades}" filterEvent="onkeyup">
                                                                                                <f:facet name="header">
                                                                                                    <h:outputText value="ModalidR"/>
                                                                                                </f:facet>
                                                                                                <a4j:commandLink  action="#{RenovacaoSinteticoControle.irParaTelaCliente}" title="Ir para tela de Edição do Cliente" value="#{renovacao.planoRenovadoModalidades}" oncomplete="abrirPopup('clienteNav.jsp?page=viewCliente', 'Cliente', 1024, 700);">
                                                                                                    <f:param name="state" value="AC"/>
                                                                                                </a4j:commandLink>
                                                                                            </rich:column>

                                                                                            <rich:column sortBy="#{renovacao.planoRenovadoResponsavelCadastro}" filterEvent="onkeyup">
                                                                                                <f:facet name="header">
                                                                                                    <h:outputText value="RespContrato"/>
                                                                                                </f:facet>
                                                                                                <a4j:commandLink action="#{RenovacaoSinteticoControle.irParaTelaCliente}" title="Ir para tela de Edição do Cliente" value="#{renovacao.planoRenovadoResponsavelCadastro}" oncomplete="abrirPopup('clienteNav.jsp?page=viewCliente', 'Cliente', 1024, 700);">
                                                                                                    <f:param name="state" value="AC"/>
                                                                                                </a4j:commandLink>
                                                                                            </rich:column>
                                                                                        </rich:dataTable>
                                                                                    </rich:column>
                                                                                </rich:dataTable>
                                                                            </rich:tab>
                                                                            <rich:tab switchType="client" rendered="#{RenovacaoSinteticoControle.aprensetarLista5}" styleClass="tituloPre" label="#{RenovacaoSinteticoControle.mes5} / #{RenovacaoSinteticoControle.ano5} ">
                                                                                <rich:dataTable width="1000" rowClasses="linhaImpar, linhaPar"  styleClass="semBorda" columnClasses="colunaCentralizada"
                                                                                                value="#{RenovacaoSinteticoControle.listaPrevisaoRenovacao5}" var="listaPrevisao">
                                                                                    <rich:column >
                                                                                        <rich:dataTable width="1000" rowClasses="linhaImpar, linhaPar"  columnClasses="colunaEsquerda,colunaCentralizada,colunaCentralizada,colunaCentralizada,colunaCentralizada,colunaCentralizada,colunaCentralizada,colunaCentralizada"
                                                                                                        value="#{listaPrevisao.listaRenovacaoSinteticoVOs}" var="renovacao">
                                                                                            <rich:column sortBy="#{renovacao.contrato.pessoa.nome}"  filterEvent="onkeyup" >
                                                                                                <f:facet name="header">
                                                                                                    <h:outputText value="Nome"/>
                                                                                                </f:facet>
                                                                                                <a4j:commandLink  action="#{RenovacaoSinteticoControle.irParaTelaCliente}" title="Ir para tela de Edição do Cliente" value="#{renovacao.contrato.pessoa.nome}" oncomplete="abrirPopup('clienteNav.jsp?page=viewCliente', 'Cliente', 1024, 700);">
                                                                                                    <f:param name="state" value="AC"/>
                                                                                                </a4j:commandLink>
                                                                                            </rich:column>
                                                                                            <rich:column sortBy="#{renovacao.situacaoRenovacao_Apresentar}" filterEvent="onkeyup">
                                                                                                <f:facet name="header">
                                                                                                    <h:outputText value="Situação"/>
                                                                                                </f:facet>
                                                                                                <a4j:commandLink  action="#{RenovacaoSinteticoControle.irParaTelaCliente}" title="Ir para tela de Edição do Cliente" value="#{renovacao.situacaoRenovacao_Apresentar}" oncomplete="abrirPopup('clienteNav.jsp?page=viewCliente', 'Cliente', 1024, 700);">
                                                                                                    <f:param name="state" value="AC"/>
                                                                                                </a4j:commandLink>
                                                                                            </rich:column>
                                                                                            <rich:column sortBy="#{renovacao.contrato.dataPrevistaRenovar}" filterEvent="onkeyup">
                                                                                                <f:facet name="header">
                                                                                                    <h:outputText value="Previsão"/>
                                                                                                </f:facet>
                                                                                                <a4j:commandLink  action="#{RenovacaoSinteticoControle.irParaTelaCliente}" title="Ir para tela de Edição do Cliente" value="#{renovacao.contrato.dataPrevistaRenovar_Apresentar}" oncomplete="abrirPopup('clienteNav.jsp?page=viewCliente', 'Cliente', 1024, 700);">
                                                                                                    <f:param name="state" value="AC"/>
                                                                                                </a4j:commandLink>
                                                                                            </rich:column>
                                                                                            <rich:column sortBy="#{renovacao.contrato.contratoDuracao.numeroMeses}" filterEvent="onkeyup">
                                                                                                <f:facet name="header">
                                                                                                    <h:outputText value="Duração"/>
                                                                                                </f:facet>
                                                                                                <a4j:commandLink  action="#{RenovacaoSinteticoControle.irParaTelaCliente}" title="Ir para tela de Edição do Cliente" value="#{renovacao.contrato.contratoDuracao.numeroMeses}" oncomplete="abrirPopup('clienteNav.jsp?page=viewCliente', 'Cliente', 1024, 700);">
                                                                                                    <f:param name="state" value="AC"/>
                                                                                                </a4j:commandLink>
                                                                                            </rich:column>
                                                                                            <rich:column sortBy="#{renovacao.contrato.nomeModalidadesConcatenado}" filterEvent="onkeyup">
                                                                                                <f:facet name="header">
                                                                                                    <h:outputText value="Modalidades"/>
                                                                                                </f:facet>
                                                                                                <a4j:commandLink  action="#{RenovacaoSinteticoControle.irParaTelaCliente}" title="Ir para tela de Edição do Cliente" value="#{renovacao.contrato.nomeModalidadesConcatenado}" oncomplete="abrirPopup('clienteNav.jsp?page=viewCliente', 'Cliente', 1024, 700);">
                                                                                                    <f:param name="state" value="AC"/>
                                                                                                </a4j:commandLink>
                                                                                            </rich:column>
                                                                                            <rich:column sortBy="#{renovacao.planoRenovadoDate}" filterEvent="onkeyup">
                                                                                                <f:facet name="header">
                                                                                                    <h:outputText value="Data Renovação"/>
                                                                                                </f:facet>
                                                                                                <a4j:commandLink  action="#{RenovacaoSinteticoControle.irParaTelaCliente}" title="Ir para tela de Edição do Cliente" value="#{renovacao.planoRenovado}" oncomplete="abrirPopup('clienteNav.jsp?page=viewCliente', 'Cliente', 1024, 700);">
                                                                                                    <f:param name="state" value="AC"/>
                                                                                                </a4j:commandLink>
                                                                                            </rich:column>
                                                                                            <rich:column sortBy="#{renovacao.planoRenovadoDuracao}" filterEvent="onkeyup">
                                                                                                <f:facet name="header">
                                                                                                    <h:outputText value="DuraR"/>
                                                                                                </f:facet>
                                                                                                <a4j:commandLink  action="#{RenovacaoSinteticoControle.irParaTelaCliente}" title="Ir para tela de Edição do Cliente" value="#{renovacao.planoRenovadoDuracao}" oncomplete="abrirPopup('clienteNav.jsp?page=viewCliente', 'Cliente', 1024, 700);">
                                                                                                    <f:param name="state" value="AC"/>
                                                                                                </a4j:commandLink>
                                                                                            </rich:column>
                                                                                            <rich:column sortBy="#{renovacao.planoRenovadoModalidades}" filterEvent="onkeyup">
                                                                                                <f:facet name="header">
                                                                                                    <h:outputText value="ModalidR"/>
                                                                                                </f:facet>
                                                                                                <a4j:commandLink  action="#{RenovacaoSinteticoControle.irParaTelaCliente}" title="Ir para tela de Edição do Cliente" value="#{renovacao.planoRenovadoModalidades}" oncomplete="abrirPopup('clienteNav.jsp?page=viewCliente', 'Cliente', 1024, 700);">
                                                                                                    <f:param name="state" value="AC"/>
                                                                                                </a4j:commandLink>
                                                                                            </rich:column>

                                                                                            <rich:column sortBy="#{renovacao.planoRenovadoResponsavelCadastro}" filterEvent="onkeyup">
                                                                                                <f:facet name="header">
                                                                                                    <h:outputText value="RespContrato"/>
                                                                                                </f:facet>
                                                                                                <a4j:commandLink action="#{RenovacaoSinteticoControle.irParaTelaCliente}" title="Ir para tela de Edição do Cliente" value="#{renovacao.planoRenovadoResponsavelCadastro}" oncomplete="abrirPopup('clienteNav.jsp?page=viewCliente', 'Cliente', 1024, 700);">
                                                                                                    <f:param name="state" value="AC"/>
                                                                                                </a4j:commandLink>
                                                                                            </rich:column>
                                                                                        </rich:dataTable>
                                                                                    </rich:column>
                                                                                </rich:dataTable>
                                                                            </rich:tab>
                                                                            <rich:tab switchType="client" rendered="#{RenovacaoSinteticoControle.aprensetarLista6}" styleClass="tituloPre" label="#{RenovacaoSinteticoControle.mes6} / #{RenovacaoSinteticoControle.ano6} ">
                                                                                <rich:dataTable width="1000" rowClasses="linhaImpar, linhaPar" styleClass="semBorda" columnClasses="colunaCentralizada"
                                                                                                value="#{RenovacaoSinteticoControle.listaPrevisaoRenovacao6}" var="listaPrevisao">
                                                                                    <rich:column >
                                                                                        <rich:dataTable width="1000" rowClasses="linhaImpar, linhaPar"  columnClasses="colunaEsquerda,colunaCentralizada,colunaCentralizada,colunaCentralizada,colunaCentralizada,colunaCentralizada,colunaCentralizada,colunaCentralizada"
                                                                                                        value="#{listaPrevisao.listaRenovacaoSinteticoVOs}" var="renovacao">
                                                                                            <rich:column sortBy="#{renovacao.contrato.pessoa.nome}"  filterEvent="onkeyup" >
                                                                                                <f:facet name="header">
                                                                                                    <h:outputText value="Nome"/>
                                                                                                </f:facet>
                                                                                                <a4j:commandLink  action="#{RenovacaoSinteticoControle.irParaTelaCliente}" title="Ir para tela de Edição do Cliente"  value="#{renovacao.contrato.pessoa.nome}" oncomplete="abrirPopup('clienteNav.jsp?page=viewCliente', 'Cliente', 1024, 700);">
                                                                                                    <f:param name="state" value="AC"/>
                                                                                                </a4j:commandLink>
                                                                                            </rich:column>
                                                                                            <rich:column sortBy="#{renovacao.situacaoRenovacao_Apresentar}" filterEvent="onkeyup">
                                                                                                <f:facet name="header">
                                                                                                    <h:outputText value="Situação"/>
                                                                                                </f:facet>
                                                                                                <a4j:commandLink  action="#{RenovacaoSinteticoControle.irParaTelaCliente}" title="Ir para tela de Edição do Cliente"  value="#{renovacao.situacaoRenovacao_Apresentar}" oncomplete="abrirPopup('clienteNav.jsp?page=viewCliente', 'Cliente', 1024, 700);">
                                                                                                    <f:param name="state" value="AC"/>
                                                                                                </a4j:commandLink>
                                                                                            </rich:column>
                                                                                            <rich:column sortBy="#{renovacao.contrato.dataPrevistaRenovar}" filterEvent="onkeyup">
                                                                                                <f:facet name="header">
                                                                                                    <h:outputText value="Previsão"/>
                                                                                                </f:facet>
                                                                                                <a4j:commandLink  action="#{RenovacaoSinteticoControle.irParaTelaCliente}" title="Ir para tela de Edição do Cliente"  value="#{renovacao.contrato.dataPrevistaRenovar_Apresentar}" oncomplete="abrirPopup('clienteNav.jsp?page=viewCliente', 'Cliente', 1024, 700);">
                                                                                                    <f:param name="state" value="AC"/>
                                                                                                </a4j:commandLink>
                                                                                            </rich:column>
                                                                                            <rich:column sortBy="#{renovacao.contrato.contratoDuracao.numeroMeses}" filterEvent="onkeyup">
                                                                                                <f:facet name="header">
                                                                                                    <h:outputText value="Duração"/>
                                                                                                </f:facet>
                                                                                                <a4j:commandLink  action="#{RenovacaoSinteticoControle.irParaTelaCliente}" title="Ir para tela de Edição do Cliente" value="#{renovacao.contrato.contratoDuracao.numeroMeses}" oncomplete="abrirPopup('clienteNav.jsp?page=viewCliente', 'Cliente', 1024, 700);" >
                                                                                                    <f:param name="state" value="AC"/>
                                                                                                </a4j:commandLink>
                                                                                            </rich:column>
                                                                                            <rich:column sortBy="#{renovacao.contrato.nomeModalidadesConcatenado}" filterEvent="onkeyup">
                                                                                                <f:facet name="header">
                                                                                                    <h:outputText value="Modalidades"/>
                                                                                                </f:facet>
                                                                                                <a4j:commandLink  action="#{RenovacaoSinteticoControle.irParaTelaCliente}" title="Ir para tela de Edição do Cliente" value="#{renovacao.contrato.nomeModalidadesConcatenado}" oncomplete="abrirPopup('clienteNav.jsp?page=viewCliente', 'Cliente', 1024, 700);">
                                                                                                    <f:param name="state" value="AC"/>
                                                                                                </a4j:commandLink>
                                                                                            </rich:column>
                                                                                            <rich:column sortBy="#{renovacao.planoRenovadoDate}" filterEvent="onkeyup">
                                                                                                <f:facet name="header">
                                                                                                    <h:outputText value="Data Renovação"/>
                                                                                                </f:facet>
                                                                                                <a4j:commandLink  action="#{RenovacaoSinteticoControle.irParaTelaCliente}" title="Ir para tela de Edição do Cliente"  value="#{renovacao.planoRenovado}" oncomplete="abrirPopup('clienteNav.jsp?page=viewCliente', 'Cliente', 1024, 700);">
                                                                                                    <f:param name="state" value="AC"/>
                                                                                                </a4j:commandLink>
                                                                                            </rich:column>
                                                                                            <rich:column sortBy="#{renovacao.planoRenovadoDuracao}" filterEvent="onkeyup">
                                                                                                <f:facet name="header">
                                                                                                    <h:outputText value="DuraR"/>
                                                                                                </f:facet>
                                                                                                <a4j:commandLink  action="#{RenovacaoSinteticoControle.irParaTelaCliente}" title="Ir para tela de Edição do Cliente" value="#{renovacao.planoRenovadoDuracao}" oncomplete="abrirPopup('clienteNav.jsp?page=viewCliente', 'Cliente', 1024, 700);">
                                                                                                    <f:param name="state" value="AC"/>
                                                                                                </a4j:commandLink>
                                                                                            </rich:column>
                                                                                            <rich:column sortBy="#{renovacao.planoRenovadoModalidades}" filterEvent="onkeyup">
                                                                                                <f:facet name="header">
                                                                                                    <h:outputText value="ModalidR"/>
                                                                                                </f:facet>
                                                                                                <a4j:commandLink  action="#{RenovacaoSinteticoControle.irParaTelaCliente}" title="Ir para tela de Edição do Cliente" value="#{renovacao.planoRenovadoModalidades}" oncomplete="abrirPopup('clienteNav.jsp?page=viewCliente', 'Cliente', 1024, 700);">
                                                                                                    <f:param name="state" value="AC"/>
                                                                                                </a4j:commandLink>
                                                                                            </rich:column>

                                                                                            <rich:column sortBy="#{renovacao.planoRenovadoResponsavelCadastro}" filterEvent="onkeyup">
                                                                                                <f:facet name="header">
                                                                                                    <h:outputText value="RespContrato"/>
                                                                                                </f:facet>
                                                                                                <a4j:commandLink action="#{RenovacaoSinteticoControle.irParaTelaCliente}" title="Ir para tela de Edição do Cliente" value="#{renovacao.planoRenovadoResponsavelCadastro}" oncomplete="abrirPopup('clienteNav.jsp?page=viewCliente', 'Cliente', 1024, 700);">
                                                                                                    <f:param name="state" value="AC"/>
                                                                                                </a4j:commandLink>
                                                                                            </rich:column>
                                                                                        </rich:dataTable>
                                                                                    </rich:column>
                                                                                </rich:dataTable>
                                                                            </rich:tab>
                                                                        </rich:tabPanel>
                                                                    </rich:tab>

                                                                    <rich:tab  switchType="client" styleClass="tituloPre" label="Sintético" >
                                                                        <rich:tabPanel id="tabPanelFiltrosSintetico" width="100%">
                                                                            <rich:tab switchType="client" rendered="#{RenovacaoSinteticoControle.aprensetarLista1}" styleClass="tituloPre" label="#{RenovacaoSinteticoControle.mes1} / #{RenovacaoSinteticoControle.ano1} " >
                                                                                <rich:dataTable width="1100" rowClasses="linhaImpar, linhaPar" styleClass="semBorda" columnClasses="colunaCentralizada"
                                                                                                value="#{RenovacaoSinteticoControle.listaPrevisaoRenovacao1}" var="listaPrevisao">
                                                                                    <rich:column >
                                                                                        <h:panelGrid id="panelGrafico1" columns="2"  width="100%" columnClasses="colunaCentralizada">
                                                                                            <h:panelGrid>
                                                                                                <jsfChart:chart id="chart" rendered="#{listaPrevisao.barra}" datasource="#{listaPrevisao.dataSetBarra}"
                                                                                                                type="bar" is3d="true" background="#B4CDCD" foreground="#FFFAFA" depth="35" styleClass="semBorda"
                                                                                                                colors="#6CA6CD, #00BFFF,#7CFC00, #00FF00, #FFD700, #EE9A00, #7D26CD, #00C5CD"
                                                                                                                lineStokeWidth="2" alpha="65" antialias="true"
                                                                                                                title="#{msg_aplic.prt_SituacaoRenovacaoSinteticoDW_tituloGrafico}"
                                                                                                                xlabel="#{msg_aplic.prt_SituacaoRenovacaoSinteticoDW_situacaoRenovacao}"
                                                                                                                ylabel="#{msg_aplic.prt_SituacaoRenovacaoSinteticoDW_qtdeRenovacao}"
                                                                                                                height="300" width="470" legend="true">
                                                                                                </jsfChart:chart>
                                                                                                <jsfChart:chart rendered="#{listaPrevisao.pizza}" id="chart1" datasource="#{listaPrevisao.dataSetPizza}"
                                                                                                                type="pie" is3d="true" background="#B4CDCD" foreground="#FFFAFA" depth="24" styleClass="semBorda"
                                                                                                                colors="#6CA6CD, #00BFFF,#7CFC00, #00FF00, #FFD700, #EE9A00, #7D26CD, #00C5CD" alpha="65" antialias="true"
                                                                                                                title="#{msg_aplic.prt_SituacaoRenovacaoSinteticoDW_tituloGrafico}" legend="false"  height="300" width="470">
                                                                                                </jsfChart:chart>
                                                                                                <h:panelGrid id="panelMenuBotoesGrafico" columns="1"  width="100%"  columnClasses="colunaCentralizada">
                                                                                                    <h:panelGroup>
                                                                                                        <%--a4j:commandButton  title="#{msg_aplic.prt_SituacaoRenovacaoSinteticoDW_graficoBarra}"  action="#{RenovacaoSinteticoControle.alterarParaBarra}" image="../imagens/botaoBarraGrande.png" reRender="form:panelRelatorioRenovacao,form:mensagem, form:tabPanelFiltros, form:tabPanelGeral, form:tabPanelFiltrosAnalitico, form:tabPanelFiltrosSintetico" /--%>
                                                                                                        <a4j:commandButton title="#{msg_aplic.prt_SituacaoRenovacaoSinteticoDW_graficoBarra}" action="#{RenovacaoSinteticoControle.alterarParaBarra}" image="../imagens/botaoBarraGrande.png" reRender="form:tabPanelFiltrosAnalitico, form:mensagem, panelGrafico1" />
                                                                                                        <rich:spacer width="8" />
                                                                                                        <%--a4j:commandButton  title="#{msg_aplic.prt_SituacaoRenovacaoSinteticoDW_graficoPizza}"   action="#{RenovacaoSinteticoControle.alterarParaPizza}"image="../imagens/botaoPizzaGrande.png" reRender="form:panelRelatorioRenovacao,form:mensagem, form:tabPanelFiltros, form:tabPanelGeral, form:tabPanelFiltrosAnalitico, form:tabPanelFiltrosSintetico" /--%>
                                                                                                        <a4j:commandButton title="#{msg_aplic.prt_SituacaoRenovacaoSinteticoDW_graficoPizza}" action="#{RenovacaoSinteticoControle.alterarParaPizza}" image="../imagens/botaoPizzaGrande.png" reRender="form:tabPanelFiltrosAnalitico, form:mensagem, panelGrafico1" />
                                                                                                    </h:panelGroup>
                                                                                                </h:panelGrid>
                                                                                            </h:panelGrid>

                                                                                            <h:panelGrid columns="5" columnClasses="colunaEsquerda, colunaEsquerda, colunaDireita, colunaDireita, colunaDireita">
                                                                                                <h:outputText styleClass="tituloCamposAzulGrandeNegrito" value="> Total de Previsão Renovação: "/>
                                                                                                <rich:spacer width="5"/>
                                                                                                <h:outputText styleClass="tituloCamposAzulGrandeNegrito" value=" #{listaPrevisao.qtdTotalPrevisaoRenovacao}"/>
                                                                                                <rich:spacer width="5"/>
                                                                                                <h:outputText styleClass="tituloCamposAzulGrandeNegrito" value=" 100,00%"/>

                                                                                                <h:outputText styleClass="tituloCamposAzulGrandeNegrito"  value="> Total de Renovações: "/>
                                                                                                <rich:spacer width="5"/>
                                                                                                <h:outputText styleClass="tituloCamposAzulGrandeNegrito"  value="#{listaPrevisao.qtdTotalRenovacoes}"/>
                                                                                                <rich:spacer width="5"/>
                                                                                                <h:outputText rendered="#{listaPrevisao.alertRenovacoes}" styleClass="tituloCamposVermelhoNegrito" value=" #{listaPrevisao.qtdTotalRenovacoesPercentual}%"/>
                                                                                                <h:outputText rendered="#{!listaPrevisao.alertRenovacoes}" styleClass="tituloCamposAzulGrandeNegrito" value=" #{listaPrevisao.qtdTotalRenovacoesPercentual}%"/>

                                                                                                <h:outputText styleClass="tituloCamposAzulGrandeNegrito" value="> Total de Não Renovados: "/>
                                                                                                <rich:spacer width="5"/>
                                                                                                <h:outputText styleClass="tituloCamposAzulGrandeNegrito" value=" #{listaPrevisao.qtdTotalNRenovados}"/>
                                                                                                <rich:spacer width="5"/>
                                                                                                <h:outputText styleClass="tituloCamposAzulGrandeNegrito" value=" #{listaPrevisao.qtdTotalNRenovadosPercentual}%"/>
                                                                                            </h:panelGrid>
                                                                                        </h:panelGrid>
                                                                                    </rich:column>
                                                                                </rich:dataTable>

                                                                                <h:panelGrid columns="2" width="1100">
                                                                                    <rich:dataTable width="100%" rowClasses="linhaImpar, linhaPar" styleClass="semBorda" columnClasses="colunaCentralizada"
                                                                                                    value="#{RenovacaoSinteticoControle.listaPrevisaoRenovacao1}" var="listaPrevisao">
                                                                                        <rich:column >
                                                                                            <h:panelGrid id="panelGraficoRenovado1" columns="2"  width="100%" columnClasses="colunaCentralizada">
                                                                                                <h:panelGrid>
                                                                                                    <jsfChart:chart id="chartRenovado1" rendered="#{listaPrevisao.barraRenovados}" datasource="#{listaPrevisao.dataSetBarraRenovados}"
                                                                                                                    type="bar" is3d="true" background="#B4CDCD" foreground="#FFFAFA" depth="35" styleClass="semBorda"
                                                                                                                    colors="#6CA6CD, #00BFFF,#7CFC00, #00FF00, #FFD700, #EE9A00, #7D26CD, #00C5CD"
                                                                                                                    lineStokeWidth="2" alpha="65" antialias="true"
                                                                                                                    title="Relatório Gráfico de Renovações"
                                                                                                                    xlabel="#{msg_aplic.prt_SituacaoRenovacaoSinteticoDW_situacaoRenovacao}"
                                                                                                                    ylabel="#{msg_aplic.prt_SituacaoRenovacaoSinteticoDW_qtdeRenovacao}"
                                                                                                                    height="200" width="350" legend="true">
                                                                                                    </jsfChart:chart>
                                                                                                    <jsfChart:chart rendered="#{listaPrevisao.pizzaRenovados}" id="chartRenovado2" datasource="#{listaPrevisao.dataSetPizzaRenovados}"
                                                                                                                    type="pie" is3d="true" background="#B4CDCD" foreground="#FFFAFA" depth="24" styleClass="semBorda"
                                                                                                                    colors="#6CA6CD, #00BFFF,#7CFC00, #00FF00, #FFD700, #EE9A00, #7D26CD, #00C5CD" alpha="65" antialias="true"
                                                                                                                    title="Relatório Gráfico de Renovações" legend="false"  height="200" width="350">
                                                                                                    </jsfChart:chart>
                                                                                                    <h:panelGrid id="panelMenuBotoesGrafico" columns="1"  width="100%"  columnClasses="colunaCentralizada">
                                                                                                        <h:panelGroup>
                                                                                                            <%--a4j:commandButton  title="#{msg_aplic.prt_SituacaoRenovacaoSinteticoDW_graficoBarra}"  action="#{RenovacaoSinteticoControle.alterarParaBarra}" image="../imagens/botaoBarraGrande.png" reRender="form:panelRelatorioRenovacao,form:mensagem, form:tabPanelFiltros, form:tabPanelGeral, form:tabPanelFiltrosAnalitico, form:tabPanelFiltrosSintetico" /--%>
                                                                                                            <a4j:commandButton title="#{msg_aplic.prt_SituacaoRenovacaoSinteticoDW_graficoBarra}" action="#{RenovacaoSinteticoControle.alterarParaBarra}" image="../imagens/botaoBarraGrande.png" reRender="form:tabPanelFiltrosAnalitico, form:mensagem, panelGraficoRenovado1" />
                                                                                                            <rich:spacer width="8" />
                                                                                                            <%--a4j:commandButton  title="#{msg_aplic.prt_SituacaoRenovacaoSinteticoDW_graficoPizza}"   action="#{RenovacaoSinteticoControle.alterarParaPizza}"image="../imagens/botaoPizzaGrande.png" reRender="form:panelRelatorioRenovacao,form:mensagem, form:tabPanelFiltros, form:tabPanelGeral, form:tabPanelFiltrosAnalitico, form:tabPanelFiltrosSintetico" /--%>
                                                                                                            <a4j:commandButton title="#{msg_aplic.prt_SituacaoRenovacaoSinteticoDW_graficoPizza}" action="#{RenovacaoSinteticoControle.alterarParaPizza}" image="../imagens/botaoPizzaGrande.png" reRender="form:tabPanelFiltrosAnalitico, form:mensagem, panelGraficoRenovado1" />
                                                                                                        </h:panelGroup>
                                                                                                    </h:panelGrid>
                                                                                                </h:panelGrid>

                                                                                                <h:panelGrid columns="2" columnClasses="colunaEsquerda, colunaDireita">
                                                                                                    <h:outputText styleClass="tituloPre"  value="> Renovações Antecipadas: "/>
                                                                                                    <h:outputText styleClass="tituloPre"  value=" #{listaPrevisao.qtdRenovacoesAntecipadas}"/>
                                                                                                    <h:outputText styleClass="tituloPre"  value="> Renovações do Dia: "/>
                                                                                                    <h:outputText styleClass="tituloPre"  value=" #{listaPrevisao.qtdRenovacoesDia}"/>
                                                                                                    <h:outputText styleClass="tituloPre"  value="> Renovações Atrasadas: "/>
                                                                                                    <h:outputText styleClass="tituloPre"  value=" #{listaPrevisao.qtdRenovacoesAtrasadas}"/>
                                                                                                </h:panelGrid>
                                                                                            </h:panelGrid>
                                                                                        </rich:column>
                                                                                    </rich:dataTable>
                                                                                    <rich:dataTable width="100%" rowClasses="linhaImpar, linhaPar" styleClass="semBorda" columnClasses="colunaCentralizada"
                                                                                                    value="#{RenovacaoSinteticoControle.listaPrevisaoRenovacao1}" var="listaPrevisao">
                                                                                        <rich:column >
                                                                                            <h:panelGrid id="panelGraficoNaoRenovado1" columns="2"  width="100%" columnClasses="colunaCentralizada">
                                                                                                <h:panelGrid>
                                                                                                    <jsfChart:chart id="chartNaoRenovado1" rendered="#{listaPrevisao.barraNaoRenovados}" datasource="#{listaPrevisao.dataSetBarraNaoRenovados}"
                                                                                                                    type="bar" is3d="true" background="#B4CDCD" foreground="#FFFAFA" depth="35" styleClass="tabConsulta"
                                                                                                                    colors="#6CA6CD, #00BFFF,#7CFC00, #00FF00, #FFD700, #EE9A00, #7D26CD, #00C5CD"
                                                                                                                    lineStokeWidth="2" alpha="65" antialias="true"
                                                                                                                    title="Relatório Gráfico de Não Renovados"
                                                                                                                    xlabel="#{msg_aplic.prt_SituacaoRenovacaoSinteticoDW_situacaoRenovacao}"
                                                                                                                    ylabel="#{msg_aplic.prt_SituacaoRenovacaoSinteticoDW_qtdeRenovacao}"
                                                                                                                    height="200" width="350" legend="true">
                                                                                                    </jsfChart:chart>
                                                                                                    <jsfChart:chart rendered="#{listaPrevisao.pizzaNaoRenovados}" id="chartNaoRenovado2" datasource="#{listaPrevisao.dataSetPizzaNaoRenovados}"
                                                                                                                    type="pie" is3d="true" background="#B4CDCD" foreground="#FFFAFA" depth="24"
                                                                                                                    colors="#6CA6CD, #00BFFF,#7CFC00, #00FF00, #FFD700, #EE9A00, #7D26CD, #00C5CD" alpha="65" antialias="true"
                                                                                                                    title="Relatório Gráfico de Não Renovados" legend="false"  height="200" width="350">
                                                                                                    </jsfChart:chart>
                                                                                                    <h:panelGrid id="panelMenuBotoesGrafico" columns="1"  width="100%"  columnClasses="colunaCentralizada">
                                                                                                        <h:panelGroup>
                                                                                                            <%--a4j:commandButton  title="#{msg_aplic.prt_SituacaoRenovacaoSinteticoDW_graficoBarra}"  action="#{RenovacaoSinteticoControle.alterarParaBarra}" image="../imagens/botaoBarraGrande.png" reRender="form:panelRelatorioRenovacao,form:mensagem, form:tabPanelFiltros, form:tabPanelGeral, form:tabPanelFiltrosAnalitico, form:tabPanelFiltrosSintetico" /--%>
                                                                                                            <a4j:commandButton title="#{msg_aplic.prt_SituacaoRenovacaoSinteticoDW_graficoBarra}" action="#{RenovacaoSinteticoControle.alterarParaBarra}" image="../imagens/botaoBarraGrande.png" reRender="form:tabPanelFiltrosAnalitico, form:mensagem, panelGraficoNaoRenovado1" />
                                                                                                            <rich:spacer width="8" />
                                                                                                            <%--a4j:commandButton  title="#{msg_aplic.prt_SituacaoRenovacaoSinteticoDW_graficoPizza}"   action="#{RenovacaoSinteticoControle.alterarParaPizza}"image="../imagens/botaoPizzaGrande.png" reRender="form:panelRelatorioRenovacao,form:mensagem, form:tabPanelFiltros, form:tabPanelGeral, form:tabPanelFiltrosAnalitico, form:tabPanelFiltrosSintetico" /--%>
                                                                                                            <a4j:commandButton title="#{msg_aplic.prt_SituacaoRenovacaoSinteticoDW_graficoPizza}" action="#{RenovacaoSinteticoControle.alterarParaPizza}" image="../imagens/botaoPizzaGrande.png" reRender="form:tabPanelFiltrosAnalitico, form:mensagem, panelGraficoNaoRenovado1" />
                                                                                                        </h:panelGroup>
                                                                                                    </h:panelGrid>
                                                                                                </h:panelGrid>

                                                                                                <h:panelGrid columns="2" columnClasses="colunaEsquerda, colunaDireita">
                                                                                                    <h:outputText styleClass="tituloPre"  value="> A vencer desta previsão: "/>
                                                                                                    <h:outputText styleClass="tituloPre"  value="#{listaPrevisao.qtdNRenovadosAVencer}"/>
                                                                                                    <h:outputText styleClass="tituloPre"  value="> Vencidos desta previsão:"/>
                                                                                                    <h:outputText styleClass="tituloPre"  value=" #{listaPrevisao.qtdNRenovadosVencidos}"/>
                                                                                                    <h:outputText styleClass="tituloPre"  value="> Desistentes desta previsão:"/>
                                                                                                    <h:outputText styleClass="tituloPre"  value="#{listaPrevisao.qtdNRenovadosDesistentes}"/>
                                                                                                </h:panelGrid>
                                                                                            </h:panelGrid>
                                                                                        </rich:column>
                                                                                    </rich:dataTable>
                                                                                </h:panelGrid>
                                                                            </rich:tab>
                                                                            <rich:tab switchType="client" rendered="#{RenovacaoSinteticoControle.aprensetarLista2}" styleClass="tituloPre" label="#{RenovacaoSinteticoControle.mes2} / #{RenovacaoSinteticoControle.ano2} " >
                                                                                <rich:dataTable width="1100" rowClasses="linhaImpar, linhaPar" styleClass="semBorda" columnClasses="colunaCentralizada"
                                                                                                value="#{RenovacaoSinteticoControle.listaPrevisaoRenovacao2}" var="listaPrevisao">
                                                                                    <rich:column >
                                                                                        <h:panelGrid id="panelGrafico2" columns="2"  width="100%" columnClasses="colunaCentralizada">
                                                                                            <h:panelGrid>
                                                                                                <jsfChart:chart id="chart" rendered="#{listaPrevisao.barra}" datasource="#{listaPrevisao.dataSetBarra}"
                                                                                                                type="bar" is3d="true" background="#B4CDCD" foreground="#FFFAFA" depth="35" styleClass="semBorda"
                                                                                                                colors="#6CA6CD, #00BFFF,#7CFC00, #00FF00, #FFD700, #EE9A00, #7D26CD, #00C5CD"
                                                                                                                lineStokeWidth="2" alpha="65" antialias="true"
                                                                                                                title="#{msg_aplic.prt_SituacaoRenovacaoSinteticoDW_tituloGrafico}"
                                                                                                                xlabel="#{msg_aplic.prt_SituacaoRenovacaoSinteticoDW_situacaoRenovacao}"
                                                                                                                ylabel="#{msg_aplic.prt_SituacaoRenovacaoSinteticoDW_qtdeRenovacao}"
                                                                                                                height="300" width="470" legend="true">
                                                                                                </jsfChart:chart>
                                                                                                <jsfChart:chart rendered="#{listaPrevisao.pizza}" id="chart1" datasource="#{listaPrevisao.dataSetPizza}"
                                                                                                                type="pie" is3d="true" background="#B4CDCD" foreground="#FFFAFA" depth="24" styleClass="semBorda"
                                                                                                                colors="#6CA6CD, #00BFFF,#7CFC00, #00FF00, #FFD700, #EE9A00, #7D26CD, #00C5CD" alpha="65" antialias="true"
                                                                                                                title="#{msg_aplic.prt_SituacaoRenovacaoSinteticoDW_tituloGrafico}" legend="false"  height="300" width="470">
                                                                                                </jsfChart:chart>
                                                                                                <h:panelGrid id="panelMenuBotoesGrafico" columns="1"  width="100%"  columnClasses="colunaCentralizada">
                                                                                                    <h:panelGroup>
                                                                                                        <%--a4j:commandButton  title="#{msg_aplic.prt_SituacaoRenovacaoSinteticoDW_graficoBarra}"  action="#{RenovacaoSinteticoControle.alterarParaBarra}" image="../imagens/botaoBarraGrande.png" reRender="form:panelRelatorioRenovacao,form:mensagem, form:tabPanelFiltros, form:tabPanelGeral, form:tabPanelFiltrosAnalitico, form:tabPanelFiltrosSintetico" /--%>
                                                                                                        <a4j:commandButton title="#{msg_aplic.prt_SituacaoRenovacaoSinteticoDW_graficoBarra}" action="#{RenovacaoSinteticoControle.alterarParaBarra}" image="../imagens/botaoBarraGrande.png" reRender="form:tabPanelFiltrosAnalitico, form:mensagem, panelGrafico2" />
                                                                                                        <rich:spacer width="8" />
                                                                                                        <%--a4j:commandButton  title="#{msg_aplic.prt_SituacaoRenovacaoSinteticoDW_graficoPizza}"   action="#{RenovacaoSinteticoControle.alterarParaPizza}"image="../imagens/botaoPizzaGrande.png" reRender="form:panelRelatorioRenovacao,form:mensagem, form:tabPanelFiltros, form:tabPanelGeral, form:tabPanelFiltrosAnalitico, form:tabPanelFiltrosSintetico" /--%>
                                                                                                        <a4j:commandButton title="#{msg_aplic.prt_SituacaoRenovacaoSinteticoDW_graficoPizza}" action="#{RenovacaoSinteticoControle.alterarParaPizza}" image="../imagens/botaoPizzaGrande.png" reRender="form:tabPanelFiltrosAnalitico, form:mensagem, panelGrafico2" />
                                                                                                    </h:panelGroup>
                                                                                                </h:panelGrid>
                                                                                            </h:panelGrid>

                                                                                            <h:panelGrid columns="5" columnClasses="colunaEsquerda, colunaEsquerda, colunaDireita, colunaDireita, colunaDireita">
                                                                                                <h:outputText styleClass="tituloCamposAzulGrandeNegrito" value="> Total de Previsão Renovação: "/>
                                                                                                <rich:spacer width="5"/>
                                                                                                <h:outputText styleClass="tituloCamposAzulGrandeNegrito" value=" #{listaPrevisao.qtdTotalPrevisaoRenovacao}"/>
                                                                                                <rich:spacer width="5"/>
                                                                                                <h:outputText styleClass="tituloCamposAzulGrandeNegrito" value=" 100,00%"/>

                                                                                                <h:outputText styleClass="tituloCamposAzulGrandeNegrito"  value="> Total de Renovações: "/>
                                                                                                <rich:spacer width="5"/>
                                                                                                <h:outputText styleClass="tituloCamposAzulGrandeNegrito"  value="#{listaPrevisao.qtdTotalRenovacoes}"/>
                                                                                                <rich:spacer width="5"/>
                                                                                                <h:outputText rendered="#{listaPrevisao.alertRenovacoes}" styleClass="tituloCamposVermelhoNegrito" value=" #{listaPrevisao.qtdTotalRenovacoesPercentual}%"/>
                                                                                                <h:outputText rendered="#{!listaPrevisao.alertRenovacoes}" styleClass="tituloCamposAzulGrandeNegrito" value=" #{listaPrevisao.qtdTotalRenovacoesPercentual}%"/>

                                                                                                <h:outputText styleClass="tituloCamposAzulGrandeNegrito" value="> Total de Não Renovados: "/>
                                                                                                <rich:spacer width="5"/>
                                                                                                <h:outputText styleClass="tituloCamposAzulGrandeNegrito" value=" #{listaPrevisao.qtdTotalNRenovados}"/>
                                                                                                <rich:spacer width="5"/>
                                                                                                <h:outputText styleClass="tituloCamposAzulGrandeNegrito" value=" #{listaPrevisao.qtdTotalNRenovadosPercentual}%"/>
                                                                                            </h:panelGrid>
                                                                                        </h:panelGrid>
                                                                                    </rich:column>
                                                                                </rich:dataTable>

                                                                                <h:panelGrid columns="2" width="1100">
                                                                                    <rich:dataTable width="100%" rowClasses="linhaImpar, linhaPar" styleClass="semBorda" columnClasses="colunaCentralizada"
                                                                                                    value="#{RenovacaoSinteticoControle.listaPrevisaoRenovacao2}" var="listaPrevisao">
                                                                                        <rich:column >
                                                                                            <h:panelGrid id="panelGraficoRenovado2" columns="2"  width="100%" columnClasses="colunaCentralizada">
                                                                                                <h:panelGrid>
                                                                                                    <jsfChart:chart id="chartRenovado1" rendered="#{listaPrevisao.barraRenovados}" datasource="#{listaPrevisao.dataSetBarraRenovados}"
                                                                                                                    type="bar" is3d="true" background="#B4CDCD" foreground="#FFFAFA" depth="35" styleClass="semBorda"
                                                                                                                    colors="#6CA6CD, #00BFFF,#7CFC00, #00FF00, #FFD700, #EE9A00, #7D26CD, #00C5CD"
                                                                                                                    lineStokeWidth="2" alpha="65" antialias="true"
                                                                                                                    title="Relatório Gráfico de Renovações"
                                                                                                                    xlabel="#{msg_aplic.prt_SituacaoRenovacaoSinteticoDW_situacaoRenovacao}"
                                                                                                                    ylabel="#{msg_aplic.prt_SituacaoRenovacaoSinteticoDW_qtdeRenovacao}"
                                                                                                                    height="200" width="350" legend="true">
                                                                                                    </jsfChart:chart>
                                                                                                    <jsfChart:chart rendered="#{listaPrevisao.pizzaRenovados}" id="chartRenovado2" datasource="#{listaPrevisao.dataSetPizzaRenovados}"
                                                                                                                    type="pie" is3d="true" background="#B4CDCD" foreground="#FFFAFA" depth="24" styleClass="semBorda"
                                                                                                                    colors="#6CA6CD, #00BFFF,#7CFC00, #00FF00, #FFD700, #EE9A00, #7D26CD, #00C5CD" alpha="65" antialias="true"
                                                                                                                    title="Relatório Gráfico de Renovações" legend="false"  height="200" width="350">
                                                                                                    </jsfChart:chart>
                                                                                                    <h:panelGrid id="panelMenuBotoesGrafico" columns="1"  width="100%"  columnClasses="colunaCentralizada">
                                                                                                        <h:panelGroup>
                                                                                                            <%--a4j:commandButton  title="#{msg_aplic.prt_SituacaoRenovacaoSinteticoDW_graficoBarra}"  action="#{RenovacaoSinteticoControle.alterarParaBarra}" image="../imagens/botaoBarraGrande.png" reRender="form:panelRelatorioRenovacao,form:mensagem, form:tabPanelFiltros, form:tabPanelGeral, form:tabPanelFiltrosAnalitico, form:tabPanelFiltrosSintetico" /--%>
                                                                                                            <a4j:commandButton  title="#{msg_aplic.prt_SituacaoRenovacaoSinteticoDW_graficoBarra}"  action="#{RenovacaoSinteticoControle.alterarParaBarra}" image="../imagens/botaoBarraGrande.png" reRender="form:tabPanelFiltrosAnalitico, form:mensagem, panelGraficoRenovado2" />
                                                                                                            <rich:spacer width="8" />
                                                                                                            <%--a4j:commandButton  title="#{msg_aplic.prt_SituacaoRenovacaoSinteticoDW_graficoPizza}"   action="#{RenovacaoSinteticoControle.alterarParaPizza}"image="../imagens/botaoPizzaGrande.png" reRender="form:panelRelatorioRenovacao,form:mensagem, form:tabPanelFiltros, form:tabPanelGeral, form:tabPanelFiltrosAnalitico, form:tabPanelFiltrosSintetico" /--%>
                                                                                                            <a4j:commandButton  title="#{msg_aplic.prt_SituacaoRenovacaoSinteticoDW_graficoPizza}"   action="#{RenovacaoSinteticoControle.alterarParaPizza}" image="../imagens/botaoPizzaGrande.png" reRender="form:tabPanelFiltrosAnalitico, form:mensagem, panelGraficoRenovado2" />
                                                                                                        </h:panelGroup>
                                                                                                    </h:panelGrid>
                                                                                                </h:panelGrid>

                                                                                                <h:panelGrid columns="2" columnClasses="colunaEsquerda, colunaDireita">
                                                                                                    <h:outputText styleClass="tituloPre"  value="> Renovações Antecipadas: "/>
                                                                                                    <h:outputText styleClass="tituloPre"  value=" #{listaPrevisao.qtdRenovacoesAntecipadas}"/>
                                                                                                    <h:outputText styleClass="tituloPre"  value="> Renovações do Dia: "/>
                                                                                                    <h:outputText styleClass="tituloPre"  value=" #{listaPrevisao.qtdRenovacoesDia}"/>
                                                                                                    <h:outputText styleClass="tituloPre"  value="> Renovações Atrasadas: "/>
                                                                                                    <h:outputText styleClass="tituloPre"  value=" #{listaPrevisao.qtdRenovacoesAtrasadas}"/>
                                                                                                </h:panelGrid>
                                                                                            </h:panelGrid>
                                                                                        </rich:column>
                                                                                    </rich:dataTable>
                                                                                    <rich:dataTable width="100%" rowClasses="linhaImpar, linhaPar" styleClass="semBorda" columnClasses="colunaCentralizada"
                                                                                                    value="#{RenovacaoSinteticoControle.listaPrevisaoRenovacao2}" var="listaPrevisao">
                                                                                        <rich:column >
                                                                                            <h:panelGrid id="panelGraficoNaoRenovado2" columns="2"  width="100%" columnClasses="colunaCentralizada">
                                                                                                <h:panelGrid>
                                                                                                    <jsfChart:chart id="chartNaoRenovado1" rendered="#{listaPrevisao.barraNaoRenovados}" datasource="#{listaPrevisao.dataSetBarraNaoRenovados}"
                                                                                                                    type="bar" is3d="true" background="#B4CDCD" foreground="#FFFAFA" depth="35" styleClass="tabConsulta"
                                                                                                                    colors="#6CA6CD, #00BFFF,#7CFC00, #00FF00, #FFD700, #EE9A00, #7D26CD, #00C5CD"
                                                                                                                    lineStokeWidth="2" alpha="65" antialias="true"
                                                                                                                    title="Relatório Gráfico de Não Renovados"
                                                                                                                    xlabel="#{msg_aplic.prt_SituacaoRenovacaoSinteticoDW_situacaoRenovacao}"
                                                                                                                    ylabel="#{msg_aplic.prt_SituacaoRenovacaoSinteticoDW_qtdeRenovacao}"
                                                                                                                    height="200" width="350" legend="true">
                                                                                                    </jsfChart:chart>
                                                                                                    <jsfChart:chart rendered="#{listaPrevisao.pizzaNaoRenovados}" id="chartNaoRenovado2" datasource="#{listaPrevisao.dataSetPizzaNaoRenovados}"
                                                                                                                    type="pie" is3d="true" background="#B4CDCD" foreground="#FFFAFA" depth="24"
                                                                                                                    colors="#6CA6CD, #00BFFF,#7CFC00, #00FF00, #FFD700, #EE9A00, #7D26CD, #00C5CD" alpha="65" antialias="true"
                                                                                                                    title="Relatório Gráfico de Não Renovados" legend="false"  height="200" width="350">
                                                                                                    </jsfChart:chart>
                                                                                                    <h:panelGrid id="panelMenuBotoesGrafico" columns="1"  width="100%"  columnClasses="colunaCentralizada">
                                                                                                        <h:panelGroup>
                                                                                                            <%--a4j:commandButton  title="#{msg_aplic.prt_SituacaoRenovacaoSinteticoDW_graficoBarra}"  action="#{RenovacaoSinteticoControle.alterarParaBarra}" image="../imagens/botaoBarraGrande.png" reRender="form:panelRelatorioRenovacao,form:mensagem, form:tabPanelFiltros, form:tabPanelGeral, form:tabPanelFiltrosAnalitico, form:tabPanelFiltrosSintetico" /--%>
                                                                                                            <a4j:commandButton  title="#{msg_aplic.prt_SituacaoRenovacaoSinteticoDW_graficoBarra}"  action="#{RenovacaoSinteticoControle.alterarParaBarra}" image="../imagens/botaoBarraGrande.png" reRender="form:tabPanelFiltrosAnalitico, form:mensagem, panelGraficoNaoRenovado2" />
                                                                                                            <rich:spacer width="8" />
                                                                                                            <%--a4j:commandButton  title="#{msg_aplic.prt_SituacaoRenovacaoSinteticoDW_graficoPizza}"   action="#{RenovacaoSinteticoControle.alterarParaPizza}"image="../imagens/botaoPizzaGrande.png" reRender="form:panelRelatorioRenovacao,form:mensagem, form:tabPanelFiltros, form:tabPanelGeral, form:tabPanelFiltrosAnalitico, form:tabPanelFiltrosSintetico" /--%>
                                                                                                            <a4j:commandButton  title="#{msg_aplic.prt_SituacaoRenovacaoSinteticoDW_graficoPizza}"   action="#{RenovacaoSinteticoControle.alterarParaPizza}" image="../imagens/botaoPizzaGrande.png" reRender="form:tabPanelFiltrosAnalitico, form:mensagem, panelGraficoNaoRenovado2" />
                                                                                                        </h:panelGroup>
                                                                                                    </h:panelGrid>
                                                                                                </h:panelGrid>

                                                                                                <h:panelGrid columns="2" columnClasses="colunaEsquerda, colunaDireita">
                                                                                                    <h:outputText styleClass="tituloPre"  value="> A vencer desta previsão: "/>
                                                                                                    <h:outputText styleClass="tituloPre"  value="#{listaPrevisao.qtdNRenovadosAVencer}"/>
                                                                                                    <h:outputText styleClass="tituloPre"  value="> Vencidos desta previsão:"/>
                                                                                                    <h:outputText styleClass="tituloPre"  value=" #{listaPrevisao.qtdNRenovadosVencidos}"/>
                                                                                                    <h:outputText styleClass="tituloPre"  value="> Desistentes desta previsão:"/>
                                                                                                    <h:outputText styleClass="tituloPre"  value="#{listaPrevisao.qtdNRenovadosDesistentes}"/>
                                                                                                </h:panelGrid>
                                                                                            </h:panelGrid>
                                                                                        </rich:column>
                                                                                    </rich:dataTable>
                                                                                </h:panelGrid>
                                                                            </rich:tab>
                                                                            <rich:tab switchType="client" rendered="#{RenovacaoSinteticoControle.aprensetarLista3}" styleClass="tituloPre" label="#{RenovacaoSinteticoControle.mes3} / #{RenovacaoSinteticoControle.ano3} " >
                                                                                <rich:dataTable width="1100" rowClasses="linhaImpar, linhaPar" styleClass="semBorda" columnClasses="colunaCentralizada"
                                                                                                value="#{RenovacaoSinteticoControle.listaPrevisaoRenovacao3}" var="listaPrevisao">
                                                                                    <rich:column >
                                                                                        <h:panelGrid id="panelGrafico3" columns="2"  width="100%" columnClasses="colunaCentralizada">
                                                                                            <h:panelGrid>
                                                                                                <jsfChart:chart id="chart" rendered="#{listaPrevisao.barra}" datasource="#{listaPrevisao.dataSetBarra}"
                                                                                                                type="bar" is3d="true" background="#B4CDCD" foreground="#FFFAFA" depth="35" styleClass="semBorda"
                                                                                                                colors="#6CA6CD, #00BFFF,#7CFC00, #00FF00, #FFD700, #EE9A00, #7D26CD, #00C5CD"
                                                                                                                lineStokeWidth="2" alpha="65" antialias="true"
                                                                                                                title="#{msg_aplic.prt_SituacaoRenovacaoSinteticoDW_tituloGrafico}"
                                                                                                                xlabel="#{msg_aplic.prt_SituacaoRenovacaoSinteticoDW_situacaoRenovacao}"
                                                                                                                ylabel="#{msg_aplic.prt_SituacaoRenovacaoSinteticoDW_qtdeRenovacao}"
                                                                                                                height="300" width="470" legend="true">
                                                                                                </jsfChart:chart>
                                                                                                <jsfChart:chart rendered="#{listaPrevisao.pizza}" id="chart1" datasource="#{listaPrevisao.dataSetPizza}"
                                                                                                                type="pie" is3d="true" background="#B4CDCD" foreground="#FFFAFA" depth="24" styleClass="semBorda"
                                                                                                                colors="#6CA6CD, #00BFFF,#7CFC00, #00FF00, #FFD700, #EE9A00, #7D26CD, #00C5CD" alpha="65" antialias="true"
                                                                                                                title="#{msg_aplic.prt_SituacaoRenovacaoSinteticoDW_tituloGrafico}" legend="false"  height="300" width="470">
                                                                                                </jsfChart:chart>
                                                                                                <h:panelGrid id="panelMenuBotoesGrafico" columns="1"  width="100%"  columnClasses="colunaCentralizada">
                                                                                                    <h:panelGroup>
                                                                                                        <%--a4j:commandButton  title="#{msg_aplic.prt_SituacaoRenovacaoSinteticoDW_graficoBarra}"  action="#{RenovacaoSinteticoControle.alterarParaBarra}" image="../imagens/botaoBarraGrande.png" reRender="form:panelRelatorioRenovacao,form:mensagem, form:tabPanelFiltros, form:tabPanelGeral, form:tabPanelFiltrosAnalitico, form:tabPanelFiltrosSintetico" /--%>
                                                                                                        <a4j:commandButton  title="#{msg_aplic.prt_SituacaoRenovacaoSinteticoDW_graficoBarra}"  action="#{RenovacaoSinteticoControle.alterarParaBarra}" image="../imagens/botaoBarraGrande.png" reRender="form:tabPanelFiltrosAnalitico, form:mensagem, panelGrafico3" />
                                                                                                        <rich:spacer width="8" />
                                                                                                        <%--a4j:commandButton  title="#{msg_aplic.prt_SituacaoRenovacaoSinteticoDW_graficoPizza}"   action="#{RenovacaoSinteticoControle.alterarParaPizza}"image="../imagens/botaoPizzaGrande.png" reRender="form:panelRelatorioRenovacao,form:mensagem, form:tabPanelFiltros, form:tabPanelGeral, form:tabPanelFiltrosAnalitico, form:tabPanelFiltrosSintetico" /--%>
                                                                                                        <a4j:commandButton  title="#{msg_aplic.prt_SituacaoRenovacaoSinteticoDW_graficoPizza}"   action="#{RenovacaoSinteticoControle.alterarParaPizza}" image="../imagens/botaoPizzaGrande.png" reRender="form:tabPanelFiltrosAnalitico, form:mensagem, panelGrafico3" />
                                                                                                    </h:panelGroup>
                                                                                                </h:panelGrid>
                                                                                            </h:panelGrid>

                                                                                            <h:panelGrid columns="5" columnClasses="colunaEsquerda, colunaEsquerda, colunaDireita, colunaDireita, colunaDireita">
                                                                                                <h:outputText styleClass="tituloCamposAzulGrandeNegrito" value="> Total de Previsão Renovação: "/>
                                                                                                <rich:spacer width="5"/>
                                                                                                <h:outputText styleClass="tituloCamposAzulGrandeNegrito" value=" #{listaPrevisao.qtdTotalPrevisaoRenovacao}"/>
                                                                                                <rich:spacer width="5"/>
                                                                                                <h:outputText styleClass="tituloCamposAzulGrandeNegrito" value=" 100,00%"/>

                                                                                                <h:outputText styleClass="tituloCamposAzulGrandeNegrito"  value="> Total de Renovações: "/>
                                                                                                <rich:spacer width="5"/>
                                                                                                <h:outputText styleClass="tituloCamposAzulGrandeNegrito"  value="#{listaPrevisao.qtdTotalRenovacoes}"/>
                                                                                                <rich:spacer width="5"/>
                                                                                                <h:outputText rendered="#{listaPrevisao.alertRenovacoes}" styleClass="tituloCamposVermelhoNegrito" value=" #{listaPrevisao.qtdTotalRenovacoesPercentual}%"/>
                                                                                                <h:outputText rendered="#{!listaPrevisao.alertRenovacoes}" styleClass="tituloCamposAzulGrandeNegrito" value=" #{listaPrevisao.qtdTotalRenovacoesPercentual}%"/>

                                                                                                <h:outputText styleClass="tituloCamposAzulGrandeNegrito" value="> Total de Não Renovados: "/>
                                                                                                <rich:spacer width="5"/>
                                                                                                <h:outputText styleClass="tituloCamposAzulGrandeNegrito" value=" #{listaPrevisao.qtdTotalNRenovados}"/>
                                                                                                <rich:spacer width="5"/>
                                                                                                <h:outputText styleClass="tituloCamposAzulGrandeNegrito" value=" #{listaPrevisao.qtdTotalNRenovadosPercentual}%"/>
                                                                                            </h:panelGrid>
                                                                                        </h:panelGrid>
                                                                                    </rich:column>
                                                                                </rich:dataTable>

                                                                                <h:panelGrid columns="2" width="1100">
                                                                                    <rich:dataTable width="100%" rowClasses="linhaImpar, linhaPar" styleClass="semBorda" columnClasses="colunaCentralizada"
                                                                                                    value="#{RenovacaoSinteticoControle.listaPrevisaoRenovacao3}" var="listaPrevisao">
                                                                                        <rich:column >
                                                                                            <h:panelGrid id="panelGraficoRenovado3" columns="2"  width="100%" columnClasses="colunaCentralizada">
                                                                                                <h:panelGrid>
                                                                                                    <jsfChart:chart id="chartRenovado1" rendered="#{listaPrevisao.barraRenovados}" datasource="#{listaPrevisao.dataSetBarraRenovados}"
                                                                                                                    type="bar" is3d="true" background="#B4CDCD" foreground="#FFFAFA" depth="35" styleClass="semBorda"
                                                                                                                    colors="#6CA6CD, #00BFFF,#7CFC00, #00FF00, #FFD700, #EE9A00, #7D26CD, #00C5CD"
                                                                                                                    lineStokeWidth="2" alpha="65" antialias="true"
                                                                                                                    title="Relatório Gráfico de Renovações"
                                                                                                                    xlabel="#{msg_aplic.prt_SituacaoRenovacaoSinteticoDW_situacaoRenovacao}"
                                                                                                                    ylabel="#{msg_aplic.prt_SituacaoRenovacaoSinteticoDW_qtdeRenovacao}"
                                                                                                                    height="200" width="350" legend="true">
                                                                                                    </jsfChart:chart>
                                                                                                    <jsfChart:chart rendered="#{listaPrevisao.pizzaRenovados}" id="chartRenovado2" datasource="#{listaPrevisao.dataSetPizzaRenovados}"
                                                                                                                    type="pie" is3d="true" background="#B4CDCD" foreground="#FFFAFA" depth="24" styleClass="semBorda"
                                                                                                                    colors="#6CA6CD, #00BFFF,#7CFC00, #00FF00, #FFD700, #EE9A00, #7D26CD, #00C5CD" alpha="65" antialias="true"
                                                                                                                    title="Relatório Gráfico de Renovações" legend="false"  height="200" width="350">
                                                                                                    </jsfChart:chart>
                                                                                                    <h:panelGrid id="panelMenuBotoesGrafico" columns="1"  width="100%"  columnClasses="colunaCentralizada">
                                                                                                        <h:panelGroup>
                                                                                                            <%--a4j:commandButton  title="#{msg_aplic.prt_SituacaoRenovacaoSinteticoDW_graficoBarra}"  action="#{RenovacaoSinteticoControle.alterarParaBarra}" image="../imagens/botaoBarraGrande.png" reRender="form:panelRelatorioRenovacao,form:mensagem, form:tabPanelFiltros, form:tabPanelGeral, form:tabPanelFiltrosAnalitico, form:tabPanelFiltrosSintetico" /--%>
                                                                                                            <a4j:commandButton  title="#{msg_aplic.prt_SituacaoRenovacaoSinteticoDW_graficoBarra}"  action="#{RenovacaoSinteticoControle.alterarParaBarra}" image="../imagens/botaoBarraGrande.png" reRender="form:tabPanelFiltrosAnalitico, form:mensagem, panelGraficoRenovado3" />
                                                                                                            <rich:spacer width="8" />
                                                                                                            <%--a4j:commandButton  title="#{msg_aplic.prt_SituacaoRenovacaoSinteticoDW_graficoPizza}"   action="#{RenovacaoSinteticoControle.alterarParaPizza}"image="../imagens/botaoPizzaGrande.png" reRender="form:panelRelatorioRenovacao,form:mensagem, form:tabPanelFiltros, form:tabPanelGeral, form:tabPanelFiltrosAnalitico, form:tabPanelFiltrosSintetico" /--%>
                                                                                                            <a4j:commandButton  title="#{msg_aplic.prt_SituacaoRenovacaoSinteticoDW_graficoPizza}"   action="#{RenovacaoSinteticoControle.alterarParaPizza}" image="../imagens/botaoPizzaGrande.png" reRender="form:tabPanelFiltrosAnalitico, form:mensagem, panelGraficoRenovado3" />
                                                                                                        </h:panelGroup>
                                                                                                    </h:panelGrid>
                                                                                                </h:panelGrid>

                                                                                                <h:panelGrid columns="2" columnClasses="colunaEsquerda, colunaDireita">
                                                                                                    <h:outputText styleClass="tituloPre"  value="> Renovações Antecipadas: "/>
                                                                                                    <h:outputText styleClass="tituloPre"  value=" #{listaPrevisao.qtdRenovacoesAntecipadas}"/>
                                                                                                    <h:outputText styleClass="tituloPre"  value="> Renovações do Dia: "/>
                                                                                                    <h:outputText styleClass="tituloPre"  value=" #{listaPrevisao.qtdRenovacoesDia}"/>
                                                                                                    <h:outputText styleClass="tituloPre"  value="> Renovações Atrasadas: "/>
                                                                                                    <h:outputText styleClass="tituloPre"  value=" #{listaPrevisao.qtdRenovacoesAtrasadas}"/>
                                                                                                </h:panelGrid>
                                                                                            </h:panelGrid>
                                                                                        </rich:column>
                                                                                    </rich:dataTable>
                                                                                    <rich:dataTable width="100%" rowClasses="linhaImpar, linhaPar" styleClass="semBorda" columnClasses="colunaCentralizada"
                                                                                                    value="#{RenovacaoSinteticoControle.listaPrevisaoRenovacao3}" var="listaPrevisao">
                                                                                        <rich:column >
                                                                                            <h:panelGrid id="panelGraficoNaoRenovado3" columns="2"  width="100%" columnClasses="colunaCentralizada">
                                                                                                <h:panelGrid>
                                                                                                    <jsfChart:chart id="chartNaoRenovado1" rendered="#{listaPrevisao.barraNaoRenovados}" datasource="#{listaPrevisao.dataSetBarraNaoRenovados}"
                                                                                                                    type="bar" is3d="true" background="#B4CDCD" foreground="#FFFAFA" depth="35" styleClass="tabConsulta"
                                                                                                                    colors="#6CA6CD, #00BFFF,#7CFC00, #00FF00, #FFD700, #EE9A00, #7D26CD, #00C5CD"
                                                                                                                    lineStokeWidth="2" alpha="65" antialias="true"
                                                                                                                    title="Relatório Gráfico de Não Renovados"
                                                                                                                    xlabel="#{msg_aplic.prt_SituacaoRenovacaoSinteticoDW_situacaoRenovacao}"
                                                                                                                    ylabel="#{msg_aplic.prt_SituacaoRenovacaoSinteticoDW_qtdeRenovacao}"
                                                                                                                    height="200" width="350" legend="true">
                                                                                                    </jsfChart:chart>
                                                                                                    <jsfChart:chart rendered="#{listaPrevisao.pizzaNaoRenovados}" id="chartNaoRenovado2" datasource="#{listaPrevisao.dataSetPizzaNaoRenovados}"
                                                                                                                    type="pie" is3d="true" background="#B4CDCD" foreground="#FFFAFA" depth="24"
                                                                                                                    colors="#6CA6CD, #00BFFF,#7CFC00, #00FF00, #FFD700, #EE9A00, #7D26CD, #00C5CD" alpha="65" antialias="true"
                                                                                                                    title="Relatório Gráfico de Não Renovados" legend="false"  height="200" width="350">
                                                                                                    </jsfChart:chart>
                                                                                                    <h:panelGrid id="panelMenuBotoesGrafico" columns="1"  width="100%"  columnClasses="colunaCentralizada">
                                                                                                        <h:panelGroup>
                                                                                                            <%--a4j:commandButton  title="#{msg_aplic.prt_SituacaoRenovacaoSinteticoDW_graficoBarra}"  action="#{RenovacaoSinteticoControle.alterarParaBarra}" image="../imagens/botaoBarraGrande.png" reRender="form:panelRelatorioRenovacao,form:mensagem, form:tabPanelFiltros, form:tabPanelGeral, form:tabPanelFiltrosAnalitico, form:tabPanelFiltrosSintetico" /--%>
                                                                                                            <a4j:commandButton  title="#{msg_aplic.prt_SituacaoRenovacaoSinteticoDW_graficoBarra}"  action="#{RenovacaoSinteticoControle.alterarParaBarra}" image="../imagens/botaoBarraGrande.png" reRender="form:tabPanelFiltrosAnalitico, form:mensagem, panelGraficoNaoRenovado3" />
                                                                                                            <rich:spacer width="8" />
                                                                                                            <%--a4j:commandButton  title="#{msg_aplic.prt_SituacaoRenovacaoSinteticoDW_graficoPizza}"   action="#{RenovacaoSinteticoControle.alterarParaPizza}"image="../imagens/botaoPizzaGrande.png" reRender="form:panelRelatorioRenovacao,form:mensagem, form:tabPanelFiltros, form:tabPanelGeral, form:tabPanelFiltrosAnalitico, form:tabPanelFiltrosSintetico" /--%>
                                                                                                            <a4j:commandButton  title="#{msg_aplic.prt_SituacaoRenovacaoSinteticoDW_graficoPizza}"   action="#{RenovacaoSinteticoControle.alterarParaPizza}" image="../imagens/botaoPizzaGrande.png" reRender="form:tabPanelFiltrosAnalitico, form:mensagem, panelGraficoNaoRenovado3" />
                                                                                                        </h:panelGroup>
                                                                                                    </h:panelGrid>
                                                                                                </h:panelGrid>

                                                                                                <h:panelGrid columns="2" columnClasses="colunaEsquerda, colunaDireita">
                                                                                                    <h:outputText styleClass="tituloPre"  value="> A vencer desta previsão: "/>
                                                                                                    <h:outputText styleClass="tituloPre"  value="#{listaPrevisao.qtdNRenovadosAVencer}"/>
                                                                                                    <h:outputText styleClass="tituloPre"  value="> Vencidos desta previsão:"/>
                                                                                                    <h:outputText styleClass="tituloPre"  value=" #{listaPrevisao.qtdNRenovadosVencidos}"/>
                                                                                                    <h:outputText styleClass="tituloPre"  value="> Desistentes desta previsão:"/>
                                                                                                    <h:outputText styleClass="tituloPre"  value="#{listaPrevisao.qtdNRenovadosDesistentes}"/>
                                                                                                </h:panelGrid>
                                                                                            </h:panelGrid>
                                                                                        </rich:column>
                                                                                    </rich:dataTable>
                                                                                </h:panelGrid>
                                                                            </rich:tab>
                                                                            <rich:tab switchType="client" rendered="#{RenovacaoSinteticoControle.aprensetarLista4}" styleClass="tituloPre" label="#{RenovacaoSinteticoControle.mes4} / #{RenovacaoSinteticoControle.ano4} " >
                                                                                <rich:dataTable width="1100" rowClasses="linhaImpar, linhaPar" styleClass="semBorda" columnClasses="colunaCentralizada"
                                                                                                value="#{RenovacaoSinteticoControle.listaPrevisaoRenovacao4}" var="listaPrevisao">
                                                                                    <rich:column >
                                                                                        <h:panelGrid id="panelGrafico4" columns="2"  width="100%" columnClasses="colunaCentralizada">
                                                                                            <h:panelGrid>
                                                                                                <jsfChart:chart id="chart" rendered="#{listaPrevisao.barra}" datasource="#{listaPrevisao.dataSetBarra}"
                                                                                                                type="bar" is3d="true" background="#B4CDCD" foreground="#FFFAFA" depth="35" styleClass="semBorda"
                                                                                                                colors="#6CA6CD, #00BFFF,#7CFC00, #00FF00, #FFD700, #EE9A00, #7D26CD, #00C5CD"
                                                                                                                lineStokeWidth="2" alpha="65" antialias="true"
                                                                                                                title="#{msg_aplic.prt_SituacaoRenovacaoSinteticoDW_tituloGrafico}"
                                                                                                                xlabel="#{msg_aplic.prt_SituacaoRenovacaoSinteticoDW_situacaoRenovacao}"
                                                                                                                ylabel="#{msg_aplic.prt_SituacaoRenovacaoSinteticoDW_qtdeRenovacao}"
                                                                                                                height="300" width="470" legend="true">
                                                                                                </jsfChart:chart>
                                                                                                <jsfChart:chart rendered="#{listaPrevisao.pizza}" id="chart1" datasource="#{listaPrevisao.dataSetPizza}"
                                                                                                                type="pie" is3d="true" background="#B4CDCD" foreground="#FFFAFA" depth="24" styleClass="semBorda"
                                                                                                                colors="#6CA6CD, #00BFFF,#7CFC00, #00FF00, #FFD700, #EE9A00, #7D26CD, #00C5CD" alpha="65" antialias="true"
                                                                                                                title="#{msg_aplic.prt_SituacaoRenovacaoSinteticoDW_tituloGrafico}" legend="false"  height="300" width="470">
                                                                                                </jsfChart:chart>
                                                                                                <h:panelGrid id="panelMenuBotoesGrafico" columns="1"  width="100%"  columnClasses="colunaCentralizada">
                                                                                                    <h:panelGroup>
                                                                                                        <%--a4j:commandButton  title="#{msg_aplic.prt_SituacaoRenovacaoSinteticoDW_graficoBarra}"  action="#{RenovacaoSinteticoControle.alterarParaBarra}" image="../imagens/botaoBarraGrande.png" reRender="form:panelRelatorioRenovacao,form:mensagem, form:tabPanelFiltros, form:tabPanelGeral, form:tabPanelFiltrosAnalitico, form:tabPanelFiltrosSintetico" /--%>
                                                                                                        <a4j:commandButton  title="#{msg_aplic.prt_SituacaoRenovacaoSinteticoDW_graficoBarra}"  action="#{RenovacaoSinteticoControle.alterarParaBarra}" image="../imagens/botaoBarraGrande.png" reRender="form:tabPanelFiltrosAnalitico, form:mensagem, panelGrafico4" />
                                                                                                        <rich:spacer width="8" />
                                                                                                        <%--a4j:commandButton  title="#{msg_aplic.prt_SituacaoRenovacaoSinteticoDW_graficoPizza}"   action="#{RenovacaoSinteticoControle.alterarParaPizza}"image="../imagens/botaoPizzaGrande.png" reRender="form:panelRelatorioRenovacao,form:mensagem, form:tabPanelFiltros, form:tabPanelGeral, form:tabPanelFiltrosAnalitico, form:tabPanelFiltrosSintetico" /--%>
                                                                                                        <a4j:commandButton  title="#{msg_aplic.prt_SituacaoRenovacaoSinteticoDW_graficoPizza}"   action="#{RenovacaoSinteticoControle.alterarParaPizza}" image="../imagens/botaoPizzaGrande.png" reRender="form:tabPanelFiltrosAnalitico, form:mensagem, panelGrafico4" />
                                                                                                    </h:panelGroup>
                                                                                                </h:panelGrid>
                                                                                            </h:panelGrid>

                                                                                            <h:panelGrid columns="5" columnClasses="colunaEsquerda, colunaEsquerda, colunaDireita, colunaDireita, colunaDireita">
                                                                                                <h:outputText styleClass="tituloCamposAzulGrandeNegrito" value="> Total de Previsão Renovação: "/>
                                                                                                <rich:spacer width="5"/>
                                                                                                <h:outputText styleClass="tituloCamposAzulGrandeNegrito" value=" #{listaPrevisao.qtdTotalPrevisaoRenovacao}"/>
                                                                                                <rich:spacer width="5"/>
                                                                                                <h:outputText styleClass="tituloCamposAzulGrandeNegrito" value=" 100,00%"/>

                                                                                                <h:outputText styleClass="tituloCamposAzulGrandeNegrito"  value="> Total de Renovações: "/>
                                                                                                <rich:spacer width="5"/>
                                                                                                <h:outputText styleClass="tituloCamposAzulGrandeNegrito"  value="#{listaPrevisao.qtdTotalRenovacoes}"/>
                                                                                                <rich:spacer width="5"/>
                                                                                                <h:outputText rendered="#{listaPrevisao.alertRenovacoes}" styleClass="tituloCamposVermelhoNegrito" value=" #{listaPrevisao.qtdTotalRenovacoesPercentual}%"/>
                                                                                                <h:outputText rendered="#{!listaPrevisao.alertRenovacoes}" styleClass="tituloCamposAzulGrandeNegrito" value=" #{listaPrevisao.qtdTotalRenovacoesPercentual}%"/>

                                                                                                <h:outputText styleClass="tituloCamposAzulGrandeNegrito" value="> Total de Não Renovados: "/>
                                                                                                <rich:spacer width="5"/>
                                                                                                <h:outputText styleClass="tituloCamposAzulGrandeNegrito" value=" #{listaPrevisao.qtdTotalNRenovados}"/>
                                                                                                <rich:spacer width="5"/>
                                                                                                <h:outputText styleClass="tituloCamposAzulGrandeNegrito" value=" #{listaPrevisao.qtdTotalNRenovadosPercentual}%"/>
                                                                                            </h:panelGrid>
                                                                                        </h:panelGrid>
                                                                                    </rich:column>
                                                                                </rich:dataTable>

                                                                                <h:panelGrid columns="2" width="1100">
                                                                                    <rich:dataTable width="100%" rowClasses="linhaImpar, linhaPar" styleClass="semBorda" columnClasses="colunaCentralizada"
                                                                                                    value="#{RenovacaoSinteticoControle.listaPrevisaoRenovacao4}" var="listaPrevisao">
                                                                                        <rich:column >
                                                                                            <h:panelGrid id="panelGraficoRenovado4" columns="2"  width="100%" columnClasses="colunaCentralizada">
                                                                                                <h:panelGrid>
                                                                                                    <jsfChart:chart id="chartRenovado1" rendered="#{listaPrevisao.barraRenovados}" datasource="#{listaPrevisao.dataSetBarraRenovados}"
                                                                                                                    type="bar" is3d="true" background="#B4CDCD" foreground="#FFFAFA" depth="35" styleClass="semBorda"
                                                                                                                    colors="#6CA6CD, #00BFFF,#7CFC00, #00FF00, #FFD700, #EE9A00, #7D26CD, #00C5CD"
                                                                                                                    lineStokeWidth="2" alpha="65" antialias="true"
                                                                                                                    title="Relatório Gráfico de Renovações"
                                                                                                                    xlabel="#{msg_aplic.prt_SituacaoRenovacaoSinteticoDW_situacaoRenovacao}"
                                                                                                                    ylabel="#{msg_aplic.prt_SituacaoRenovacaoSinteticoDW_qtdeRenovacao}"
                                                                                                                    height="200" width="350" legend="true">
                                                                                                    </jsfChart:chart>
                                                                                                    <jsfChart:chart rendered="#{listaPrevisao.pizzaRenovados}" id="chartRenovado2" datasource="#{listaPrevisao.dataSetPizzaRenovados}"
                                                                                                                    type="pie" is3d="true" background="#B4CDCD" foreground="#FFFAFA" depth="24" styleClass="semBorda"
                                                                                                                    colors="#6CA6CD, #00BFFF,#7CFC00, #00FF00, #FFD700, #EE9A00, #7D26CD, #00C5CD" alpha="65" antialias="true"
                                                                                                                    title="Relatório Gráfico de Renovações" legend="false"  height="200" width="350">
                                                                                                    </jsfChart:chart>
                                                                                                    <h:panelGrid id="panelMenuBotoesGrafico" columns="1"  width="100%"  columnClasses="colunaCentralizada">
                                                                                                        <h:panelGroup>
                                                                                                            <%--a4j:commandButton  title="#{msg_aplic.prt_SituacaoRenovacaoSinteticoDW_graficoBarra}"  action="#{RenovacaoSinteticoControle.alterarParaBarra}" image="../imagens/botaoBarraGrande.png" reRender="form:panelRelatorioRenovacao,form:mensagem, form:tabPanelFiltros, form:tabPanelGeral, form:tabPanelFiltrosAnalitico, form:tabPanelFiltrosSintetico" /--%>
                                                                                                            <a4j:commandButton  title="#{msg_aplic.prt_SituacaoRenovacaoSinteticoDW_graficoBarra}"  action="#{RenovacaoSinteticoControle.alterarParaBarra}" image="../imagens/botaoBarraGrande.png" reRender="form:tabPanelFiltrosAnalitico, form:mensagem, panelGraficoRenovado4" />
                                                                                                            <rich:spacer width="8" />
                                                                                                            <%--a4j:commandButton  title="#{msg_aplic.prt_SituacaoRenovacaoSinteticoDW_graficoPizza}"   action="#{RenovacaoSinteticoControle.alterarParaPizza}"image="../imagens/botaoPizzaGrande.png" reRender="form:panelRelatorioRenovacao,form:mensagem, form:tabPanelFiltros, form:tabPanelGeral, form:tabPanelFiltrosAnalitico, form:tabPanelFiltrosSintetico" /--%>
                                                                                                            <a4j:commandButton  title="#{msg_aplic.prt_SituacaoRenovacaoSinteticoDW_graficoPizza}"   action="#{RenovacaoSinteticoControle.alterarParaPizza}" image="../imagens/botaoPizzaGrande.png" reRender="form:tabPanelFiltrosAnalitico, form:mensagem, panelGraficoRenovado4" />
                                                                                                        </h:panelGroup>
                                                                                                    </h:panelGrid>
                                                                                                </h:panelGrid>

                                                                                                <h:panelGrid columns="2" columnClasses="colunaEsquerda, colunaDireita">
                                                                                                    <h:outputText styleClass="tituloPre"  value="> Renovações Antecipadas: "/>
                                                                                                    <h:outputText styleClass="tituloPre"  value=" #{listaPrevisao.qtdRenovacoesAntecipadas}"/>
                                                                                                    <h:outputText styleClass="tituloPre"  value="> Renovações do Dia: "/>
                                                                                                    <h:outputText styleClass="tituloPre"  value=" #{listaPrevisao.qtdRenovacoesDia}"/>
                                                                                                    <h:outputText styleClass="tituloPre"  value="> Renovações Atrasadas: "/>
                                                                                                    <h:outputText styleClass="tituloPre"  value=" #{listaPrevisao.qtdRenovacoesAtrasadas}"/>
                                                                                                </h:panelGrid>
                                                                                            </h:panelGrid>
                                                                                        </rich:column>
                                                                                    </rich:dataTable>
                                                                                    <rich:dataTable width="100%" rowClasses="linhaImpar, linhaPar" styleClass="semBorda" columnClasses="colunaCentralizada"
                                                                                                    value="#{RenovacaoSinteticoControle.listaPrevisaoRenovacao4}" var="listaPrevisao">
                                                                                        <rich:column >
                                                                                            <h:panelGrid id="panelGraficoNaoRenovado4" columns="2"  width="100%" columnClasses="colunaCentralizada">
                                                                                                <h:panelGrid>
                                                                                                    <jsfChart:chart id="chartNaoRenovado1" rendered="#{listaPrevisao.barraNaoRenovados}" datasource="#{listaPrevisao.dataSetBarraNaoRenovados}"
                                                                                                                    type="bar" is3d="true" background="#B4CDCD" foreground="#FFFAFA" depth="35" styleClass="tabConsulta"
                                                                                                                    colors="#6CA6CD, #00BFFF,#7CFC00, #00FF00, #FFD700, #EE9A00, #7D26CD, #00C5CD"
                                                                                                                    lineStokeWidth="2" alpha="65" antialias="true"
                                                                                                                    title="Relatório Gráfico de Não Renovados"
                                                                                                                    xlabel="#{msg_aplic.prt_SituacaoRenovacaoSinteticoDW_situacaoRenovacao}"
                                                                                                                    ylabel="#{msg_aplic.prt_SituacaoRenovacaoSinteticoDW_qtdeRenovacao}"
                                                                                                                    height="200" width="350" legend="true">
                                                                                                    </jsfChart:chart>
                                                                                                    <jsfChart:chart rendered="#{listaPrevisao.pizzaNaoRenovados}" id="chartNaoRenovado2" datasource="#{listaPrevisao.dataSetPizzaNaoRenovados}"
                                                                                                                    type="pie" is3d="true" background="#B4CDCD" foreground="#FFFAFA" depth="24"
                                                                                                                    colors="#6CA6CD, #00BFFF,#7CFC00, #00FF00, #FFD700, #EE9A00, #7D26CD, #00C5CD" alpha="65" antialias="true"
                                                                                                                    title="Relatório Gráfico de Não Renovados" legend="false"  height="200" width="350">
                                                                                                    </jsfChart:chart>
                                                                                                    <h:panelGrid id="panelMenuBotoesGrafico" columns="1"  width="100%"  columnClasses="colunaCentralizada">
                                                                                                        <h:panelGroup>
                                                                                                            <%--a4j:commandButton  title="#{msg_aplic.prt_SituacaoRenovacaoSinteticoDW_graficoBarra}"  action="#{RenovacaoSinteticoControle.alterarParaBarra}" image="../imagens/botaoBarraGrande.png" reRender="form:panelRelatorioRenovacao,form:mensagem, form:tabPanelFiltros, form:tabPanelGeral, form:tabPanelFiltrosAnalitico, form:tabPanelFiltrosSintetico" /--%>
                                                                                                            <a4j:commandButton  title="#{msg_aplic.prt_SituacaoRenovacaoSinteticoDW_graficoBarra}"  action="#{RenovacaoSinteticoControle.alterarParaBarra}" image="../imagens/botaoBarraGrande.png" reRender="form:tabPanelFiltrosAnalitico, form:mensagem, panelGraficoNaoRenovado4" />
                                                                                                            <rich:spacer width="8" />
                                                                                                            <%--a4j:commandButton  title="#{msg_aplic.prt_SituacaoRenovacaoSinteticoDW_graficoPizza}"   action="#{RenovacaoSinteticoControle.alterarParaPizza}"image="../imagens/botaoPizzaGrande.png" reRender="form:panelRelatorioRenovacao,form:mensagem, form:tabPanelFiltros, form:tabPanelGeral, form:tabPanelFiltrosAnalitico, form:tabPanelFiltrosSintetico" /--%>
                                                                                                            <a4j:commandButton  title="#{msg_aplic.prt_SituacaoRenovacaoSinteticoDW_graficoPizza}"   action="#{RenovacaoSinteticoControle.alterarParaPizza}" image="../imagens/botaoPizzaGrande.png" reRender="form:tabPanelFiltrosAnalitico, form:mensagem, panelGraficoNaoRenovado4" />
                                                                                                        </h:panelGroup>
                                                                                                    </h:panelGrid>
                                                                                                </h:panelGrid>

                                                                                                <h:panelGrid columns="2" columnClasses="colunaEsquerda, colunaDireita">
                                                                                                    <h:outputText styleClass="tituloPre"  value="> A vencer desta previsão: "/>
                                                                                                    <h:outputText styleClass="tituloPre"  value="#{listaPrevisao.qtdNRenovadosAVencer}"/>
                                                                                                    <h:outputText styleClass="tituloPre"  value="> Vencidos desta previsão:"/>
                                                                                                    <h:outputText styleClass="tituloPre"  value=" #{listaPrevisao.qtdNRenovadosVencidos}"/>
                                                                                                    <h:outputText styleClass="tituloPre"  value="> Desistentes desta previsão:"/>
                                                                                                    <h:outputText styleClass="tituloPre"  value="#{listaPrevisao.qtdNRenovadosDesistentes}"/>
                                                                                                </h:panelGrid>
                                                                                            </h:panelGrid>
                                                                                        </rich:column>
                                                                                    </rich:dataTable>
                                                                                </h:panelGrid>
                                                                            </rich:tab>
                                                                            <rich:tab switchType="client" rendered="#{RenovacaoSinteticoControle.aprensetarLista5}" styleClass="tituloPre" label="#{RenovacaoSinteticoControle.mes5} / #{RenovacaoSinteticoControle.ano5} " >
                                                                                <rich:dataTable width="1100" rowClasses="linhaImpar, linhaPar" styleClass="semBorda" columnClasses="colunaCentralizada"
                                                                                                value="#{RenovacaoSinteticoControle.listaPrevisaoRenovacao5}" var="listaPrevisao">
                                                                                    <rich:column >
                                                                                        <h:panelGrid id="panelGrafico5" columns="2"  width="100%" columnClasses="colunaCentralizada">
                                                                                            <h:panelGrid>
                                                                                                <jsfChart:chart id="chart" rendered="#{listaPrevisao.barra}" datasource="#{listaPrevisao.dataSetBarra}"
                                                                                                                type="bar" is3d="true" background="#B4CDCD" foreground="#FFFAFA" depth="35" styleClass="semBorda"
                                                                                                                colors="#6CA6CD, #00BFFF,#7CFC00, #00FF00, #FFD700, #EE9A00, #7D26CD, #00C5CD"
                                                                                                                lineStokeWidth="2" alpha="65" antialias="true"
                                                                                                                title="#{msg_aplic.prt_SituacaoRenovacaoSinteticoDW_tituloGrafico}"
                                                                                                                xlabel="#{msg_aplic.prt_SituacaoRenovacaoSinteticoDW_situacaoRenovacao}"
                                                                                                                ylabel="#{msg_aplic.prt_SituacaoRenovacaoSinteticoDW_qtdeRenovacao}"
                                                                                                                height="300" width="470" legend="true">
                                                                                                </jsfChart:chart>
                                                                                                <jsfChart:chart rendered="#{listaPrevisao.pizza}" id="chart1" datasource="#{listaPrevisao.dataSetPizza}"
                                                                                                                type="pie" is3d="true" background="#B4CDCD" foreground="#FFFAFA" depth="24" styleClass="semBorda"
                                                                                                                colors="#6CA6CD, #00BFFF,#7CFC00, #00FF00, #FFD700, #EE9A00, #7D26CD, #00C5CD" alpha="65" antialias="true"
                                                                                                                title="#{msg_aplic.prt_SituacaoRenovacaoSinteticoDW_tituloGrafico}" legend="false"  height="300" width="470">
                                                                                                </jsfChart:chart>
                                                                                                <h:panelGrid id="panelMenuBotoesGrafico" columns="1"  width="100%"  columnClasses="colunaCentralizada">
                                                                                                    <h:panelGroup>
                                                                                                        <%--a4j:commandButton  title="#{msg_aplic.prt_SituacaoRenovacaoSinteticoDW_graficoBarra}"  action="#{RenovacaoSinteticoControle.alterarParaBarra}" image="../imagens/botaoBarraGrande.png" reRender="form:panelRelatorioRenovacao,form:mensagem, form:tabPanelFiltros, form:tabPanelGeral, form:tabPanelFiltrosAnalitico, form:tabPanelFiltrosSintetico" /--%>
                                                                                                        <a4j:commandButton  title="#{msg_aplic.prt_SituacaoRenovacaoSinteticoDW_graficoBarra}"  action="#{RenovacaoSinteticoControle.alterarParaBarra}" image="../imagens/botaoBarraGrande.png" reRender="form:tabPanelFiltrosAnalitico, form:mensagem, panelGrafico5" />
                                                                                                        <rich:spacer width="8" />
                                                                                                        <%--a4j:commandButton  title="#{msg_aplic.prt_SituacaoRenovacaoSinteticoDW_graficoPizza}"   action="#{RenovacaoSinteticoControle.alterarParaPizza}"image="../imagens/botaoPizzaGrande.png" reRender="form:panelRelatorioRenovacao,form:mensagem, form:tabPanelFiltros, form:tabPanelGeral, form:tabPanelFiltrosAnalitico, form:tabPanelFiltrosSintetico" /--%>
                                                                                                        <a4j:commandButton  title="#{msg_aplic.prt_SituacaoRenovacaoSinteticoDW_graficoPizza}"   action="#{RenovacaoSinteticoControle.alterarParaPizza}" image="../imagens/botaoPizzaGrande.png" reRender="form:tabPanelFiltrosAnalitico, form:mensagem, panelGrafico5" />
                                                                                                    </h:panelGroup>
                                                                                                </h:panelGrid>
                                                                                            </h:panelGrid>

                                                                                            <h:panelGrid columns="5" columnClasses="colunaEsquerda, colunaEsquerda, colunaDireita, colunaDireita, colunaDireita">
                                                                                                <h:outputText styleClass="tituloCamposAzulGrandeNegrito" value="> Total de Previsão Renovação: "/>
                                                                                                <rich:spacer width="5"/>
                                                                                                <h:outputText styleClass="tituloCamposAzulGrandeNegrito" value=" #{listaPrevisao.qtdTotalPrevisaoRenovacao}"/>
                                                                                                <rich:spacer width="5"/>
                                                                                                <h:outputText styleClass="tituloCamposAzulGrandeNegrito" value=" 100,00%"/>

                                                                                                <h:outputText styleClass="tituloCamposAzulGrandeNegrito"  value="> Total de Renovações: "/>
                                                                                                <rich:spacer width="5"/>
                                                                                                <h:outputText styleClass="tituloCamposAzulGrandeNegrito"  value="#{listaPrevisao.qtdTotalRenovacoes}"/>
                                                                                                <rich:spacer width="5"/>
                                                                                                <h:outputText rendered="#{listaPrevisao.alertRenovacoes}" styleClass="tituloCamposVermelhoNegrito" value=" #{listaPrevisao.qtdTotalRenovacoesPercentual}%"/>
                                                                                                <h:outputText rendered="#{!listaPrevisao.alertRenovacoes}" styleClass="tituloCamposAzulGrandeNegrito" value=" #{listaPrevisao.qtdTotalRenovacoesPercentual}%"/>

                                                                                                <h:outputText styleClass="tituloCamposAzulGrandeNegrito" value="> Total de Não Renovados: "/>
                                                                                                <rich:spacer width="5"/>
                                                                                                <h:outputText styleClass="tituloCamposAzulGrandeNegrito" value=" #{listaPrevisao.qtdTotalNRenovados}"/>
                                                                                                <rich:spacer width="5"/>
                                                                                                <h:outputText styleClass="tituloCamposAzulGrandeNegrito" value=" #{listaPrevisao.qtdTotalNRenovadosPercentual}%"/>
                                                                                            </h:panelGrid>
                                                                                        </h:panelGrid>
                                                                                    </rich:column>
                                                                                </rich:dataTable>

                                                                                <h:panelGrid columns="2" width="1100">
                                                                                    <rich:dataTable width="100%" rowClasses="linhaImpar, linhaPar" styleClass="semBorda" columnClasses="colunaCentralizada"
                                                                                                    value="#{RenovacaoSinteticoControle.listaPrevisaoRenovacao5}" var="listaPrevisao">
                                                                                        <rich:column >
                                                                                            <h:panelGrid id="panelGraficoRenovado5" columns="2"  width="100%" columnClasses="colunaCentralizada">
                                                                                                <h:panelGrid>
                                                                                                    <jsfChart:chart id="chartRenovado1" rendered="#{listaPrevisao.barraRenovados}" datasource="#{listaPrevisao.dataSetBarraRenovados}"
                                                                                                                    type="bar" is3d="true" background="#B4CDCD" foreground="#FFFAFA" depth="35" styleClass="semBorda"
                                                                                                                    colors="#6CA6CD, #00BFFF,#7CFC00, #00FF00, #FFD700, #EE9A00, #7D26CD, #00C5CD"
                                                                                                                    lineStokeWidth="2" alpha="65" antialias="true"
                                                                                                                    title="Relatório Gráfico de Renovações"
                                                                                                                    xlabel="#{msg_aplic.prt_SituacaoRenovacaoSinteticoDW_situacaoRenovacao}"
                                                                                                                    ylabel="#{msg_aplic.prt_SituacaoRenovacaoSinteticoDW_qtdeRenovacao}"
                                                                                                                    height="200" width="350" legend="true">
                                                                                                    </jsfChart:chart>
                                                                                                    <jsfChart:chart rendered="#{listaPrevisao.pizzaRenovados}" id="chartRenovado2" datasource="#{listaPrevisao.dataSetPizzaRenovados}"
                                                                                                                    type="pie" is3d="true" background="#B4CDCD" foreground="#FFFAFA" depth="24" styleClass="semBorda"
                                                                                                                    colors="#6CA6CD, #00BFFF,#7CFC00, #00FF00, #FFD700, #EE9A00, #7D26CD, #00C5CD" alpha="65" antialias="true"
                                                                                                                    title="Relatório Gráfico de Renovações" legend="false"  height="200" width="350">
                                                                                                    </jsfChart:chart>
                                                                                                    <h:panelGrid id="panelMenuBotoesGrafico" columns="1"  width="100%"  columnClasses="colunaCentralizada">
                                                                                                        <h:panelGroup>
                                                                                                            <%--a4j:commandButton  title="#{msg_aplic.prt_SituacaoRenovacaoSinteticoDW_graficoBarra}"  action="#{RenovacaoSinteticoControle.alterarParaBarra}" image="../imagens/botaoBarraGrande.png" reRender="form:panelRelatorioRenovacao,form:mensagem, form:tabPanelFiltros, form:tabPanelGeral, form:tabPanelFiltrosAnalitico, form:tabPanelFiltrosSintetico" /--%>
                                                                                                            <a4j:commandButton title="#{msg_aplic.prt_SituacaoRenovacaoSinteticoDW_graficoBarra}" action="#{RenovacaoSinteticoControle.alterarParaBarra}" image="../imagens/botaoBarraGrande.png" reRender="form:tabPanelFiltrosAnalitico, form:mensagem, panelGraficoRenovado5" />
                                                                                                            <rich:spacer width="8" />
                                                                                                            <%--a4j:commandButton  title="#{msg_aplic.prt_SituacaoRenovacaoSinteticoDW_graficoPizza}"   action="#{RenovacaoSinteticoControle.alterarParaPizza}"image="../imagens/botaoPizzaGrande.png" reRender="form:panelRelatorioRenovacao,form:mensagem, form:tabPanelFiltros, form:tabPanelGeral, form:tabPanelFiltrosAnalitico, form:tabPanelFiltrosSintetico" /--%>
                                                                                                            <a4j:commandButton title="#{msg_aplic.prt_SituacaoRenovacaoSinteticoDW_graficoPizza}" action="#{RenovacaoSinteticoControle.alterarParaPizza}" image="../imagens/botaoPizzaGrande.png" reRender="form:tabPanelFiltrosAnalitico, form:mensagem, panelGraficoRenovado5" />
                                                                                                        </h:panelGroup>
                                                                                                    </h:panelGrid>
                                                                                                </h:panelGrid>

                                                                                                <h:panelGrid columns="2" columnClasses="colunaEsquerda, colunaDireita">
                                                                                                    <h:outputText styleClass="tituloPre"  value="> Renovações Antecipadas: "/>
                                                                                                    <h:outputText styleClass="tituloPre"  value=" #{listaPrevisao.qtdRenovacoesAntecipadas}"/>
                                                                                                    <h:outputText styleClass="tituloPre"  value="> Renovações do Dia: "/>
                                                                                                    <h:outputText styleClass="tituloPre"  value=" #{listaPrevisao.qtdRenovacoesDia}"/>
                                                                                                    <h:outputText styleClass="tituloPre"  value="> Renovações Atrasadas: "/>
                                                                                                    <h:outputText styleClass="tituloPre"  value=" #{listaPrevisao.qtdRenovacoesAtrasadas}"/>
                                                                                                </h:panelGrid>
                                                                                            </h:panelGrid>
                                                                                        </rich:column>
                                                                                    </rich:dataTable>
                                                                                    <rich:dataTable width="100%" rowClasses="linhaImpar, linhaPar" styleClass="semBorda" columnClasses="colunaCentralizada"
                                                                                                    value="#{RenovacaoSinteticoControle.listaPrevisaoRenovacao5}" var="listaPrevisao">
                                                                                        <rich:column >
                                                                                            <h:panelGrid id="panelGraficoNaoRenovado5" columns="2"  width="100%" columnClasses="colunaCentralizada">
                                                                                                <h:panelGrid>
                                                                                                    <jsfChart:chart id="chartNaoRenovado1" rendered="#{listaPrevisao.barraNaoRenovados}" datasource="#{listaPrevisao.dataSetBarraNaoRenovados}"
                                                                                                                    type="bar" is3d="true" background="#B4CDCD" foreground="#FFFAFA" depth="35" styleClass="tabConsulta"
                                                                                                                    colors="#6CA6CD, #00BFFF,#7CFC00, #00FF00, #FFD700, #EE9A00, #7D26CD, #00C5CD"
                                                                                                                    lineStokeWidth="2" alpha="65" antialias="true"
                                                                                                                    title="Relatório Gráfico de Não Renovados"
                                                                                                                    xlabel="#{msg_aplic.prt_SituacaoRenovacaoSinteticoDW_situacaoRenovacao}"
                                                                                                                    ylabel="#{msg_aplic.prt_SituacaoRenovacaoSinteticoDW_qtdeRenovacao}"
                                                                                                                    height="200" width="350" legend="true">
                                                                                                    </jsfChart:chart>
                                                                                                    <jsfChart:chart rendered="#{listaPrevisao.pizzaNaoRenovados}" id="chartNaoRenovado2" datasource="#{listaPrevisao.dataSetPizzaNaoRenovados}"
                                                                                                                    type="pie" is3d="true" background="#B4CDCD" foreground="#FFFAFA" depth="24"
                                                                                                                    colors="#6CA6CD, #00BFFF,#7CFC00, #00FF00, #FFD700, #EE9A00, #7D26CD, #00C5CD" alpha="65" antialias="true"
                                                                                                                    title="Relatório Gráfico de Não Renovados" legend="false"  height="200" width="350">
                                                                                                    </jsfChart:chart>
                                                                                                    <h:panelGrid id="panelMenuBotoesGrafico" columns="1"  width="100%"  columnClasses="colunaCentralizada">
                                                                                                        <h:panelGroup>
                                                                                                            <%--a4j:commandButton  title="#{msg_aplic.prt_SituacaoRenovacaoSinteticoDW_graficoBarra}"  action="#{RenovacaoSinteticoControle.alterarParaBarra}" image="../imagens/botaoBarraGrande.png" reRender="form:panelRelatorioRenovacao,form:mensagem, form:tabPanelFiltros, form:tabPanelGeral, form:tabPanelFiltrosAnalitico, form:tabPanelFiltrosSintetico" /--%>
                                                                                                            <a4j:commandButton  title="#{msg_aplic.prt_SituacaoRenovacaoSinteticoDW_graficoBarra}"  action="#{RenovacaoSinteticoControle.alterarParaBarra}" image="../imagens/botaoBarraGrande.png" reRender="form:tabPanelFiltrosAnalitico, form:mensagem, panelGraficoNaoRenovado5" />
                                                                                                            <rich:spacer width="8" />
                                                                                                            <%--a4j:commandButton  title="#{msg_aplic.prt_SituacaoRenovacaoSinteticoDW_graficoPizza}"   action="#{RenovacaoSinteticoControle.alterarParaPizza}"image="../imagens/botaoPizzaGrande.png" reRender="form:panelRelatorioRenovacao,form:mensagem, form:tabPanelFiltros, form:tabPanelGeral, form:tabPanelFiltrosAnalitico, form:tabPanelFiltrosSintetico" /--%>
                                                                                                            <a4j:commandButton  title="#{msg_aplic.prt_SituacaoRenovacaoSinteticoDW_graficoPizza}"  action="#{RenovacaoSinteticoControle.alterarParaPizza}" image="../imagens/botaoPizzaGrande.png" reRender="form:tabPanelFiltrosAnalitico, form:mensagem, panelGraficoNaoRenovado5" />
                                                                                                        </h:panelGroup>
                                                                                                    </h:panelGrid>
                                                                                                </h:panelGrid>

                                                                                                <h:panelGrid columns="2" columnClasses="colunaEsquerda, colunaDireita">
                                                                                                    <h:outputText styleClass="tituloPre"  value="> A vencer desta previsão: "/>
                                                                                                    <h:outputText styleClass="tituloPre"  value="#{listaPrevisao.qtdNRenovadosAVencer}"/>
                                                                                                    <h:outputText styleClass="tituloPre"  value="> Vencidos desta previsão:"/>
                                                                                                    <h:outputText styleClass="tituloPre"  value=" #{listaPrevisao.qtdNRenovadosVencidos}"/>
                                                                                                    <h:outputText styleClass="tituloPre"  value="> Desistentes desta previsão:"/>
                                                                                                    <h:outputText styleClass="tituloPre"  value="#{listaPrevisao.qtdNRenovadosDesistentes}"/>
                                                                                                </h:panelGrid>
                                                                                            </h:panelGrid>
                                                                                        </rich:column>
                                                                                    </rich:dataTable>
                                                                                </h:panelGrid>
                                                                            </rich:tab>
                                                                            <rich:tab switchType="client" rendered="#{RenovacaoSinteticoControle.aprensetarLista6}" styleClass="tituloPre" label="#{RenovacaoSinteticoControle.mes6} / #{RenovacaoSinteticoControle.ano6} " >
                                                                                <rich:dataTable width="1100" rowClasses="linhaImpar, linhaPar" styleClass="semBorda" columnClasses="colunaCentralizada"
                                                                                                value="#{RenovacaoSinteticoControle.listaPrevisaoRenovacao6}" var="listaPrevisao">
                                                                                    <rich:column >
                                                                                        <h:panelGrid id="panelGrafico6" columns="2"  width="100%" columnClasses="colunaCentralizada">
                                                                                            <h:panelGrid>
                                                                                                <jsfChart:chart id="chart" rendered="#{listaPrevisao.barra}" datasource="#{listaPrevisao.dataSetBarra}"
                                                                                                                type="bar" is3d="true" background="#B4CDCD" foreground="#FFFAFA" depth="35" styleClass="semBorda"
                                                                                                                colors="#6CA6CD, #00BFFF,#7CFC00, #00FF00, #FFD700, #EE9A00, #7D26CD, #00C5CD"
                                                                                                                lineStokeWidth="2" alpha="65" antialias="true"
                                                                                                                title="#{msg_aplic.prt_SituacaoRenovacaoSinteticoDW_tituloGrafico}"
                                                                                                                xlabel="#{msg_aplic.prt_SituacaoRenovacaoSinteticoDW_situacaoRenovacao}"
                                                                                                                ylabel="#{msg_aplic.prt_SituacaoRenovacaoSinteticoDW_qtdeRenovacao}"
                                                                                                                height="300" width="470" legend="true">
                                                                                                </jsfChart:chart>
                                                                                                <jsfChart:chart rendered="#{listaPrevisao.pizza}" id="chart1" datasource="#{listaPrevisao.dataSetPizza}"
                                                                                                                type="pie" is3d="true" background="#B4CDCD" foreground="#FFFAFA" depth="24" styleClass="semBorda"
                                                                                                                colors="#6CA6CD, #00BFFF,#7CFC00, #00FF00, #FFD700, #EE9A00, #7D26CD, #00C5CD" alpha="65" antialias="true"
                                                                                                                title="#{msg_aplic.prt_SituacaoRenovacaoSinteticoDW_tituloGrafico}" legend="false"  height="300" width="470">
                                                                                                </jsfChart:chart>
                                                                                                <h:panelGrid id="panelMenuBotoesGrafico" columns="1"  width="100%"  columnClasses="colunaCentralizada">
                                                                                                    <h:panelGroup>
                                                                                                        <%--a4j:commandButton  title="#{msg_aplic.prt_SituacaoRenovacaoSinteticoDW_graficoBarra}"  action="#{RenovacaoSinteticoControle.alterarParaBarra}" image="../imagens/botaoBarraGrande.png" reRender="form:panelRelatorioRenovacao,form:mensagem, form:tabPanelFiltros, form:tabPanelGeral, form:tabPanelFiltrosAnalitico, form:tabPanelFiltrosSintetico" /--%>
                                                                                                        <a4j:commandButton title="#{msg_aplic.prt_SituacaoRenovacaoSinteticoDW_graficoBarra}" action="#{RenovacaoSinteticoControle.alterarParaBarra}" image="../imagens/botaoBarraGrande.png" reRender="form:tabPanelFiltrosAnalitico, form:mensagem, panelGrafico6" />
                                                                                                        <rich:spacer width="8" />
                                                                                                        <%--a4j:commandButton  title="#{msg_aplic.prt_SituacaoRenovacaoSinteticoDW_graficoPizza}"   action="#{RenovacaoSinteticoControle.alterarParaPizza}"image="../imagens/botaoPizzaGrande.png" reRender="form:panelRelatorioRenovacao,form:mensagem, form:tabPanelFiltros, form:tabPanelGeral, form:tabPanelFiltrosAnalitico, form:tabPanelFiltrosSintetico" /--%>
                                                                                                        <a4j:commandButton title="#{msg_aplic.prt_SituacaoRenovacaoSinteticoDW_graficoPizza}" action="#{RenovacaoSinteticoControle.alterarParaPizza}" image="../imagens/botaoPizzaGrande.png" reRender="form:tabPanelFiltrosAnalitico, form:mensagem, panelGrafico6" />
                                                                                                    </h:panelGroup>
                                                                                                </h:panelGrid>
                                                                                            </h:panelGrid>

                                                                                            <h:panelGrid columns="5" columnClasses="colunaEsquerda, colunaEsquerda, colunaDireita, colunaDireita, colunaDireita">
                                                                                                <h:outputText styleClass="tituloCamposAzulGrandeNegrito" value="> Total de Previsão Renovação: "/>
                                                                                                <rich:spacer width="5"/>
                                                                                                <h:outputText styleClass="tituloCamposAzulGrandeNegrito" value=" #{listaPrevisao.qtdTotalPrevisaoRenovacao}"/>
                                                                                                <rich:spacer width="5"/>
                                                                                                <h:outputText styleClass="tituloCamposAzulGrandeNegrito" value=" 100,00%"/>

                                                                                                <h:outputText styleClass="tituloCamposAzulGrandeNegrito"  value="> Total de Renovações: "/>
                                                                                                <rich:spacer width="5"/>
                                                                                                <h:outputText styleClass="tituloCamposAzulGrandeNegrito"  value="#{listaPrevisao.qtdTotalRenovacoes}"/>
                                                                                                <rich:spacer width="5"/>
                                                                                                <h:outputText rendered="#{listaPrevisao.alertRenovacoes}" styleClass="tituloCamposVermelhoNegrito" value=" #{listaPrevisao.qtdTotalRenovacoesPercentual}%"/>
                                                                                                <h:outputText rendered="#{!listaPrevisao.alertRenovacoes}" styleClass="tituloCamposAzulGrandeNegrito" value=" #{listaPrevisao.qtdTotalRenovacoesPercentual}%"/>

                                                                                                <h:outputText styleClass="tituloCamposAzulGrandeNegrito" value="> Total de Não Renovados: "/>
                                                                                                <rich:spacer width="5"/>
                                                                                                <h:outputText styleClass="tituloCamposAzulGrandeNegrito" value=" #{listaPrevisao.qtdTotalNRenovados}"/>
                                                                                                <rich:spacer width="5"/>
                                                                                                <h:outputText styleClass="tituloCamposAzulGrandeNegrito" value=" #{listaPrevisao.qtdTotalNRenovadosPercentual}%"/>
                                                                                            </h:panelGrid>
                                                                                        </h:panelGrid>
                                                                                    </rich:column>
                                                                                </rich:dataTable>

                                                                                <h:panelGrid columns="2" width="1100">
                                                                                    <rich:dataTable width="100%" rowClasses="linhaImpar, linhaPar" styleClass="semBorda" columnClasses="colunaCentralizada"
                                                                                                    value="#{RenovacaoSinteticoControle.listaPrevisaoRenovacao6}" var="listaPrevisao">
                                                                                        <rich:column >
                                                                                            <h:panelGrid id="panelGraficoRenovado6" columns="2"  width="100%" columnClasses="colunaCentralizada">
                                                                                                <h:panelGrid>
                                                                                                    <jsfChart:chart id="chartRenovado1" rendered="#{listaPrevisao.barraRenovados}" datasource="#{listaPrevisao.dataSetBarraRenovados}"
                                                                                                                    type="bar" is3d="true" background="#B4CDCD" foreground="#FFFAFA" depth="35" styleClass="semBorda"
                                                                                                                    colors="#6CA6CD, #00BFFF,#7CFC00, #00FF00, #FFD700, #EE9A00, #7D26CD, #00C5CD"
                                                                                                                    lineStokeWidth="2" alpha="65" antialias="true"
                                                                                                                    title="Relatório Gráfico de Renovações"
                                                                                                                    xlabel="#{msg_aplic.prt_SituacaoRenovacaoSinteticoDW_situacaoRenovacao}"
                                                                                                                    ylabel="#{msg_aplic.prt_SituacaoRenovacaoSinteticoDW_qtdeRenovacao}"
                                                                                                                    height="200" width="350" legend="true">
                                                                                                    </jsfChart:chart>
                                                                                                    <jsfChart:chart rendered="#{listaPrevisao.pizzaRenovados}" id="chartRenovado2" datasource="#{listaPrevisao.dataSetPizzaRenovados}"
                                                                                                                    type="pie" is3d="true" background="#B4CDCD" foreground="#FFFAFA" depth="24" styleClass="semBorda"
                                                                                                                    colors="#6CA6CD, #00BFFF,#7CFC00, #00FF00, #FFD700, #EE9A00, #7D26CD, #00C5CD" alpha="65" antialias="true"
                                                                                                                    title="Relatório Gráfico de Renovações" legend="false"  height="200" width="350">
                                                                                                    </jsfChart:chart>
                                                                                                    <h:panelGrid id="panelMenuBotoesGrafico" columns="1"  width="100%"  columnClasses="colunaCentralizada">
                                                                                                        <h:panelGroup>
                                                                                                            <%--a4j:commandButton  title="#{msg_aplic.prt_SituacaoRenovacaoSinteticoDW_graficoBarra}"  action="#{RenovacaoSinteticoControle.alterarParaBarra}" image="../imagens/botaoBarraGrande.png" reRender="form:panelRelatorioRenovacao,form:mensagem, form:tabPanelFiltros, form:tabPanelGeral, form:tabPanelFiltrosAnalitico, form:tabPanelFiltrosSintetico" /--%>
                                                                                                            <a4j:commandButton title="#{msg_aplic.prt_SituacaoRenovacaoSinteticoDW_graficoBarra}" action="#{RenovacaoSinteticoControle.alterarParaBarra}" image="../imagens/botaoBarraGrande.png" reRender="form:tabPanelFiltrosAnalitico, form:mensagem, panelGraficoRenovado6" />
                                                                                                            <rich:spacer width="8" />
                                                                                                            <%--a4j:commandButton  title="#{msg_aplic.prt_SituacaoRenovacaoSinteticoDW_graficoPizza}"   action="#{RenovacaoSinteticoControle.alterarParaPizza}"image="../imagens/botaoPizzaGrande.png" reRender="form:panelRelatorioRenovacao,form:mensagem, form:tabPanelFiltros, form:tabPanelGeral, form:tabPanelFiltrosAnalitico, form:tabPanelFiltrosSintetico" /--%>
                                                                                                            <a4j:commandButton title="#{msg_aplic.prt_SituacaoRenovacaoSinteticoDW_graficoPizza}" action="#{RenovacaoSinteticoControle.alterarParaPizza}" image="../imagens/botaoPizzaGrande.png" reRender="form:tabPanelFiltrosAnalitico, form:mensagem, panelGraficoRenovado6" />
                                                                                                        </h:panelGroup>
                                                                                                    </h:panelGrid>
                                                                                                </h:panelGrid>

                                                                                                <h:panelGrid columns="2" columnClasses="colunaEsquerda, colunaDireita">
                                                                                                    <h:outputText styleClass="tituloPre"  value="> Renovações Antecipadas: "/>
                                                                                                    <h:outputText styleClass="tituloPre"  value=" #{listaPrevisao.qtdRenovacoesAntecipadas}"/>
                                                                                                    <h:outputText styleClass="tituloPre"  value="> Renovações do Dia: "/>
                                                                                                    <h:outputText styleClass="tituloPre"  value=" #{listaPrevisao.qtdRenovacoesDia}"/>
                                                                                                    <h:outputText styleClass="tituloPre"  value="> Renovações Atrasadas: "/>
                                                                                                    <h:outputText styleClass="tituloPre"  value=" #{listaPrevisao.qtdRenovacoesAtrasadas}"/>
                                                                                                </h:panelGrid>
                                                                                            </h:panelGrid>
                                                                                        </rich:column>
                                                                                    </rich:dataTable>
                                                                                    <rich:dataTable width="100%" rowClasses="linhaImpar, linhaPar" styleClass="semBorda" columnClasses="colunaCentralizada"
                                                                                                    value="#{RenovacaoSinteticoControle.listaPrevisaoRenovacao6}" var="listaPrevisao">
                                                                                        <rich:column >
                                                                                            <h:panelGrid id="panelGraficoNaoRenovado6" columns="2"  width="100%" columnClasses="colunaCentralizada">
                                                                                                <h:panelGrid>
                                                                                                    <jsfChart:chart id="chartNaoRenovado1" rendered="#{listaPrevisao.barraNaoRenovados}" datasource="#{listaPrevisao.dataSetBarraNaoRenovados}"
                                                                                                                    type="bar" is3d="true" background="#B4CDCD" foreground="#FFFAFA" depth="35" styleClass="tabConsulta"
                                                                                                                    colors="#6CA6CD, #00BFFF,#7CFC00, #00FF00, #FFD700, #EE9A00, #7D26CD, #00C5CD"
                                                                                                                    lineStokeWidth="2" alpha="65" antialias="true"
                                                                                                                    title="Relatório Gráfico de Não Renovados"
                                                                                                                    xlabel="#{msg_aplic.prt_SituacaoRenovacaoSinteticoDW_situacaoRenovacao}"
                                                                                                                    ylabel="#{msg_aplic.prt_SituacaoRenovacaoSinteticoDW_qtdeRenovacao}"
                                                                                                                    height="200" width="350" legend="true">
                                                                                                    </jsfChart:chart>
                                                                                                    <jsfChart:chart rendered="#{listaPrevisao.pizzaNaoRenovados}" id="chartNaoRenovado2" datasource="#{listaPrevisao.dataSetPizzaNaoRenovados}"
                                                                                                                    type="pie" is3d="true" background="#B4CDCD" foreground="#FFFAFA" depth="24"
                                                                                                                    colors="#6CA6CD, #00BFFF,#7CFC00, #00FF00, #FFD700, #EE9A00, #7D26CD, #00C5CD" alpha="65" antialias="true"
                                                                                                                    title="Relatório Gráfico de Não Renovados" legend="false"  height="200" width="350">
                                                                                                    </jsfChart:chart>
                                                                                                    <h:panelGrid id="panelMenuBotoesGrafico" columns="1"  width="100%"  columnClasses="colunaCentralizada">
                                                                                                        <h:panelGroup>
                                                                                                            <%--a4j:commandButton  title="#{msg_aplic.prt_SituacaoRenovacaoSinteticoDW_graficoBarra}"  action="#{RenovacaoSinteticoControle.alterarParaBarra}" image="../imagens/botaoBarraGrande.png" reRender="form:panelRelatorioRenovacao,form:mensagem, form:tabPanelFiltros, form:tabPanelGeral, form:tabPanelFiltrosAnalitico, form:tabPanelFiltrosSintetico" /--%>
                                                                                                            <a4j:commandButton title="#{msg_aplic.prt_SituacaoRenovacaoSinteticoDW_graficoBarra}" action="#{RenovacaoSinteticoControle.alterarParaBarra}" image="../imagens/botaoBarraGrande.png" reRender="form:tabPanelFiltrosAnalitico, form:mensagem, panelGraficoNaoRenovado6" />
                                                                                                            <rich:spacer width="8" />
                                                                                                            <%--a4j:commandButton  title="#{msg_aplic.prt_SituacaoRenovacaoSinteticoDW_graficoPizza}"   action="#{RenovacaoSinteticoControle.alterarParaPizza}"image="../imagens/botaoPizzaGrande.png" reRender="form:panelRelatorioRenovacao,form:mensagem, form:tabPanelFiltros, form:tabPanelGeral, form:tabPanelFiltrosAnalitico, form:tabPanelFiltrosSintetico" /--%>
                                                                                                            <a4j:commandButton title="#{msg_aplic.prt_SituacaoRenovacaoSinteticoDW_graficoPizza}" action="#{RenovacaoSinteticoControle.alterarParaPizza}" image="../imagens/botaoPizzaGrande.png" reRender="form:tabPanelFiltrosAnalitico, form:mensagem, panelGraficoNaoRenovado6" />
                                                                                                        </h:panelGroup>
                                                                                                    </h:panelGrid>
                                                                                                </h:panelGrid>

                                                                                                <h:panelGrid columns="2" columnClasses="colunaEsquerda, colunaDireita">
                                                                                                    <h:outputText styleClass="tituloPre"  value="> A vencer desta previsão: "/>
                                                                                                    <h:outputText styleClass="tituloPre"  value="#{listaPrevisao.qtdNRenovadosAVencer}"/>
                                                                                                    <h:outputText styleClass="tituloPre"  value="> Vencidos desta previsão:"/>
                                                                                                    <h:outputText styleClass="tituloPre"  value=" #{listaPrevisao.qtdNRenovadosVencidos}"/>
                                                                                                    <h:outputText styleClass="tituloPre"  value="> Desistentes desta previsão:"/>
                                                                                                    <h:outputText styleClass="tituloPre"  value="#{listaPrevisao.qtdNRenovadosDesistentes}"/>
                                                                                                </h:panelGrid>
                                                                                            </h:panelGrid>
                                                                                        </rich:column>
                                                                                    </rich:dataTable>
                                                                                </h:panelGrid>
                                                                            </rich:tab>
                                                                        </rich:tabPanel>
                                                                    </rich:tab>

                                                                    <rich:tab switchType="client" styleClass="tituloPre" label="Gerencial" >
                                                                        <rich:tabPanel id="tabPanelResumo" width="100%">
                                                                            <rich:tab switchType="client" styleClass="tituloPre" rendered="#{RenovacaoSinteticoControle.apresentarListaResumo1}"
                                                                                      label="#{RenovacaoSinteticoControle.mes1} / #{RenovacaoSinteticoControle.ano1} ">
                                                                                <rich:dataTable width="1000" styleClass="semBorda"
                                                                                                value="#{RenovacaoSinteticoControle.listaResumoConsultor1}" var="grupo">
                                                                                    <rich:column styleClass="semBorda">
                                                                                        <h:outputText styleClass="tituloCamposAzulGrandeNegrito" value="#{grupo.descricao}"/>
                                                                                        <rich:dataTable width="1000" rowClasses="linhaImpar, linhaPar"
                                                                                                        columnClasses="colunaEsquerda,colunaCentralizada,colunaCentralizada,colunaCentralizada,
                                                                                                        colunaCentralizada,colunaCentralizada,colunaCentralizada,colunaCentralizada"
                                                                                                        value="#{grupo.listaConsultores}" var="renovacao">
                                                                                            <rich:column sortBy="#{renovacao.colaborador.pessoa.nome}" filterEvent="onkeyup" width="400">
                                                                                                <f:facet name="header">
                                                                                                    <h:outputText value="Nome"/>
                                                                                                </f:facet>
                                                                                                <h:outputText value="#{renovacao.colaborador.pessoa.nome}"/>
                                                                                            </rich:column>
                                                                                            <rich:column sortBy="#{renovacao.qtdePrevista}" width="200">
                                                                                                <f:facet name="header">
                                                                                                    <h:outputText value="Previstos"/>
                                                                                                </f:facet>
                                                                                                <h:outputText value="#{renovacao.qtdePrevista}" />
                                                                                            </rich:column>
                                                                                            <rich:column sortBy="#{renovacao.qtdeRenovados}" width="200">
                                                                                                <f:facet name="header">
                                                                                                    <h:outputText value="Renovados"/>
                                                                                                </f:facet>
                                                                                                <h:outputText value="#{renovacao.qtdeRenovados}"/>
                                                                                            </rich:column>
                                                                                            <rich:column sortBy="#{renovacao.percentualRenovados}" width="200">
                                                                                                <f:facet name="header">
                                                                                                    <h:outputText value="% de Renovação"/>
                                                                                                </f:facet>
                                                                                                <h:outputText rendered="#{renovacao.alert}" styleClass="tituloCamposVermelhoNegrito" value="#{renovacao.percentualRenovados}%" />
                                                                                                <h:outputText rendered="#{!renovacao.alert}" styleClass="tituloCamposAzulGrandeNegrito" value="#{renovacao.percentualRenovados}%" />
                                                                                            </rich:column>
                                                                                        </rich:dataTable>
                                                                                        <rich:dataTable styleClass="semBorda" width="1000" value="" var=""
                                                                                                        columnClasses="colunaEsquerda,colunaCentralizada,colunaCentralizada,colunaCentralizada">
                                                                                            <rich:column width="400" styleClass="semBorda">
                                                                                                <h:outputText value=""/>
                                                                                            </rich:column>
                                                                                            <rich:column width="200" styleClass="semBorda">
                                                                                                <h:outputText styleClass="tituloCamposAzulGrandeNegrito" value="#{grupo.qtdePrevista}"/>
                                                                                            </rich:column>
                                                                                            <rich:column width="200" styleClass="semBorda">
                                                                                                <h:outputText styleClass="tituloCamposAzulGrandeNegrito" value="#{grupo.qtdeRenovados}"/>
                                                                                            </rich:column>
                                                                                            <rich:column width="200" styleClass="semBorda">
                                                                                                <h:outputText rendered="#{grupo.alert}" styleClass="tituloCamposVermelhoNegrito" value="#{grupo.percentualRenovados}%" />
                                                                                                <h:outputText rendered="#{!grupo.alert}" styleClass="tituloCamposAzulGrandeNegrito" value="#{grupo.percentualRenovados}%" />
                                                                                            </rich:column>
                                                                                        </rich:dataTable>
                                                                                        <rich:spacer style="display:block;" height="20"/>
                                                                                    </rich:column>
                                                                    </rich:dataTable>
                                                                            </rich:tab>
                                                                            <rich:tab switchType="client" styleClass="tituloPre" rendered="#{RenovacaoSinteticoControle.apresentarListaResumo2}"
                                                                                      label="#{RenovacaoSinteticoControle.mes2} / #{RenovacaoSinteticoControle.ano2} ">
                                                                                <rich:dataTable width="1000" styleClass="semBorda"
                                                                                                value="#{RenovacaoSinteticoControle.listaResumoConsultor2}" var="grupo">
                                                                                    <rich:column styleClass="semBorda">
                                                                                        <h:outputText styleClass="tituloCamposAzulGrandeNegrito" value="#{grupo.descricao}"/>
                                                                                        <rich:dataTable width="1000" rowClasses="linhaImpar, linhaPar"
                                                                                                        columnClasses="colunaEsquerda,colunaCentralizada,colunaCentralizada,colunaCentralizada,
                                                                                                        colunaCentralizada,colunaCentralizada,colunaCentralizada,colunaCentralizada"
                                                                                                        value="#{grupo.listaConsultores}" var="renovacao">
                                                                                            <rich:column sortBy="#{renovacao.colaborador.pessoa.nome}" filterEvent="onkeyup" width="400">
                                                                                                <f:facet name="header">
                                                                                                    <h:outputText value="Nome"/>
                                                                                                </f:facet>
                                                                                                <h:outputText value="#{renovacao.colaborador.pessoa.nome}"/>
                                                                                            </rich:column>
                                                                                            <rich:column sortBy="#{renovacao.qtdePrevista}">
                                                                                                <f:facet name="header">
                                                                                                    <h:outputText value="Previstos"/>
                                                                                                </f:facet>
                                                                                                <h:outputText value="#{renovacao.qtdePrevista}" />
                                                                                            </rich:column>
                                                                                            <rich:column sortBy="#{renovacao.qtdeRenovados}">
                                                                                                <f:facet name="header">
                                                                                                    <h:outputText value="Renovados"/>
                                                                                                </f:facet>
                                                                                                <h:outputText value="#{renovacao.qtdeRenovados}"/>
                                                                                </rich:column>
                                                                                            <rich:column sortBy="#{renovacao.percentualRenovados}">
                                                                                                <f:facet name="header">
                                                                                                    <h:outputText value="% de Renovação"/>
                                                                                                </f:facet>
                                                                                                <h:outputText rendered="#{renovacao.alert}" styleClass="tituloCamposVermelhoNegrito" value="#{renovacao.percentualRenovados}%" />
                                                                                                <h:outputText rendered="#{!renovacao.alert}" styleClass="tituloCamposAzulGrandeNegrito" value="#{renovacao.percentualRenovados}%" />
                                                                                            </rich:column>
                                                                                        </rich:dataTable>
                                                                                        <rich:dataTable styleClass="semBorda" width="1000" value="" var=""
                                                                                                        columnClasses="colunaEsquerda,colunaCentralizada,colunaCentralizada,colunaCentralizada">
                                                                                            <rich:column width="400" styleClass="semBorda">
                                                                                                <h:outputText value=""/>
                                                                                            </rich:column>
                                                                                            <rich:column width="200" styleClass="semBorda">
                                                                                                <h:outputText styleClass="tituloCamposAzulGrandeNegrito" value="#{grupo.qtdePrevista}"/>
                                                                                            </rich:column>
                                                                                            <rich:column width="200" styleClass="semBorda">
                                                                                                <h:outputText styleClass="tituloCamposAzulGrandeNegrito" value="#{grupo.qtdeRenovados}"/>
                                                                                            </rich:column>
                                                                                            <rich:column width="200" styleClass="semBorda">
                                                                                                <h:outputText rendered="#{grupo.alert}" styleClass="tituloCamposVermelhoNegrito" value="#{grupo.percentualRenovados}%" />
                                                                                                <h:outputText rendered="#{!grupo.alert}" styleClass="tituloCamposAzulGrandeNegrito" value="#{grupo.percentualRenovados}%" />
                                                                                            </rich:column>
                                                                                        </rich:dataTable>
                                                                                        <rich:spacer style="display:block;" height="20"/>
                                                                                    </rich:column>
                                                                                </rich:dataTable>
                                                                            </rich:tab>
                                                                            <rich:tab switchType="client" styleClass="tituloPre" rendered="#{RenovacaoSinteticoControle.apresentarListaResumo3}"
                                                                                      label="#{RenovacaoSinteticoControle.mes3} / #{RenovacaoSinteticoControle.ano3} ">
                                                                                <rich:dataTable width="1000" styleClass="semBorda"
                                                                                                value="#{RenovacaoSinteticoControle.listaResumoConsultor3}" var="grupo">
                                                                                    <rich:column styleClass="semBorda">
                                                                                        <h:outputText styleClass="tituloCamposAzulGrandeNegrito" value="#{grupo.descricao}"/>
                                                                                        <rich:dataTable width="1000" rowClasses="linhaImpar, linhaPar"
                                                                                                        columnClasses="colunaEsquerda,colunaCentralizada,colunaCentralizada,colunaCentralizada,
                                                                                                        colunaCentralizada,colunaCentralizada,colunaCentralizada,colunaCentralizada"
                                                                                                        value="#{grupo.listaConsultores}" var="renovacao">
                                                                                            <rich:column sortBy="#{renovacao.colaborador.pessoa.nome}" filterEvent="onkeyup" width="400">
                                                                                                <f:facet name="header">
                                                                                                    <h:outputText value="Nome"/>
                                                                                                </f:facet>
                                                                                                <h:outputText value="#{renovacao.colaborador.pessoa.nome}"/>
                                                                                            </rich:column>
                                                                                            <rich:column sortBy="#{renovacao.qtdePrevista}">
                                                                                                <f:facet name="header">
                                                                                                    <h:outputText value="Previstos"/>
                                                                                                </f:facet>
                                                                                                <h:outputText value="#{renovacao.qtdePrevista}" />
                                                                                            </rich:column>
                                                                                            <rich:column sortBy="#{renovacao.qtdeRenovados}">
                                                                                                <f:facet name="header">
                                                                                                    <h:outputText value="Renovados"/>
                                                                                                </f:facet>
                                                                                                <h:outputText value="#{renovacao.qtdeRenovados}"/>
                                                                                            </rich:column>
                                                                                            <rich:column sortBy="#{renovacao.percentualRenovados}">
                                                                                                <f:facet name="header">
                                                                                                    <h:outputText value="% de Renovação"/>
                                                                                                </f:facet>
                                                                                                <h:outputText rendered="#{renovacao.alert}" styleClass="tituloCamposVermelhoNegrito" value="#{renovacao.percentualRenovados}%" />
                                                                                                <h:outputText rendered="#{!renovacao.alert}" styleClass="tituloCamposAzulGrandeNegrito" value="#{renovacao.percentualRenovados}%" />
                                                                                            </rich:column>
                                                                                        </rich:dataTable>
                                                                                        <rich:dataTable styleClass="semBorda" width="1000" value="" var=""
                                                                                                        columnClasses="colunaEsquerda,colunaCentralizada,colunaCentralizada,colunaCentralizada">
                                                                                            <rich:column width="400" styleClass="semBorda">
                                                                                                <h:outputText value=""/>
                                                                                            </rich:column>
                                                                                            <rich:column width="200" styleClass="semBorda">
                                                                                                <h:outputText styleClass="tituloCamposAzulGrandeNegrito" value="#{grupo.qtdePrevista}"/>
                                                                                            </rich:column>
                                                                                            <rich:column width="200" styleClass="semBorda">
                                                                                                <h:outputText styleClass="tituloCamposAzulGrandeNegrito" value="#{grupo.qtdeRenovados}"/>
                                                                                            </rich:column>
                                                                                            <rich:column width="200" styleClass="semBorda">
                                                                                                <h:outputText rendered="#{grupo.alert}" styleClass="tituloCamposVermelhoNegrito" value="#{grupo.percentualRenovados}%" />
                                                                                                <h:outputText rendered="#{!grupo.alert}" styleClass="tituloCamposAzulGrandeNegrito" value="#{grupo.percentualRenovados}%" />
                                                                                            </rich:column>
                                                                                        </rich:dataTable>
                                                                                        <rich:spacer style="display:block;" height="20"/>
                                                                                    </rich:column>
                                                                                </rich:dataTable>
                                                                            </rich:tab>
                                                                            <rich:tab switchType="client" styleClass="tituloPre" rendered="#{RenovacaoSinteticoControle.apresentarListaResumo4}"
                                                                                      label="#{RenovacaoSinteticoControle.mes4} / #{RenovacaoSinteticoControle.ano4} ">
                                                                                <rich:dataTable width="1000" styleClass="semBorda"
                                                                                                value="#{RenovacaoSinteticoControle.listaResumoConsultor4}" var="grupo">
                                                                                    <rich:column styleClass="semBorda">
                                                                                        <h:outputText styleClass="tituloCamposAzulGrandeNegrito" value="#{grupo.descricao}"/>
                                                                                        <rich:dataTable width="1000" rowClasses="linhaImpar, linhaPar"
                                                                                                        columnClasses="colunaEsquerda,colunaCentralizada,colunaCentralizada,colunaCentralizada,
                                                                                                        colunaCentralizada,colunaCentralizada,colunaCentralizada,colunaCentralizada"
                                                                                                        value="#{grupo.listaConsultores}" var="renovacao">
                                                                                            <rich:column sortBy="#{renovacao.colaborador.pessoa.nome}" filterEvent="onkeyup" width="400">
                                                                                                <f:facet name="header">
                                                                                                    <h:outputText value="Nome"/>
                                                                                                </f:facet>
                                                                                                <h:outputText value="#{renovacao.colaborador.pessoa.nome}"/>
                                                                                            </rich:column>
                                                                                            <rich:column sortBy="#{renovacao.qtdePrevista}">
                                                                                                <f:facet name="header">
                                                                                                    <h:outputText value="Previstos"/>
                                                                                                </f:facet>
                                                                                                <h:outputText value="#{renovacao.qtdePrevista}" />
                                                                                            </rich:column>
                                                                                            <rich:column sortBy="#{renovacao.qtdeRenovados}">
                                                                                                <f:facet name="header">
                                                                                                    <h:outputText value="Renovados"/>
                                                                                                </f:facet>
                                                                                                <h:outputText value="#{renovacao.qtdeRenovados}"/>
                                                                                            </rich:column>
                                                                                            <rich:column sortBy="#{renovacao.percentualRenovados}">
                                                                                                <f:facet name="header">
                                                                                                    <h:outputText value="% de Renovação"/>
                                                                                                </f:facet>
                                                                                                <h:outputText rendered="#{renovacao.alert}" styleClass="tituloCamposVermelhoNegrito" value="#{renovacao.percentualRenovados}%" />
                                                                                                <h:outputText rendered="#{!renovacao.alert}" styleClass="tituloCamposAzulGrandeNegrito" value="#{renovacao.percentualRenovados}%" />
                                                                                            </rich:column>
                                                                                        </rich:dataTable>
                                                                                        <rich:dataTable styleClass="semBorda" width="1000" value="" var=""
                                                                                                        columnClasses="colunaEsquerda,colunaCentralizada,colunaCentralizada,colunaCentralizada">
                                                                                            <rich:column width="400" styleClass="semBorda">
                                                                                                <h:outputText value=""/>
                                                                                            </rich:column>
                                                                                            <rich:column width="200" styleClass="semBorda">
                                                                                                <h:outputText styleClass="tituloCamposAzulGrandeNegrito" value="#{grupo.qtdePrevista}"/>
                                                                                            </rich:column>
                                                                                            <rich:column width="200" styleClass="semBorda">
                                                                                                <h:outputText styleClass="tituloCamposAzulGrandeNegrito" value="#{grupo.qtdeRenovados}"/>
                                                                                            </rich:column>
                                                                                            <rich:column width="200" styleClass="semBorda">
                                                                                                <h:outputText rendered="#{grupo.alert}" styleClass="tituloCamposVermelhoNegrito" value="#{grupo.percentualRenovados}%" />
                                                                                                <h:outputText rendered="#{!grupo.alert}" styleClass="tituloCamposAzulGrandeNegrito" value="#{grupo.percentualRenovados}%" />
                                                                                            </rich:column>
                                                                                        </rich:dataTable>
                                                                                        <rich:spacer style="display:block;" height="20"/>
                                                                                    </rich:column>
                                                                                </rich:dataTable>
                                                                            </rich:tab>
                                                                            <rich:tab switchType="client" styleClass="tituloPre" rendered="#{RenovacaoSinteticoControle.apresentarListaResumo5}"
                                                                                      label="#{RenovacaoSinteticoControle.mes5} / #{RenovacaoSinteticoControle.ano5} ">
                                                                                <rich:dataTable width="1000" styleClass="semBorda"
                                                                                                value="#{RenovacaoSinteticoControle.listaResumoConsultor5}" var="grupo">
                                                                                    <rich:column styleClass="semBorda">
                                                                                        <h:outputText styleClass="tituloCamposAzulGrandeNegrito" value="#{grupo.descricao}"/>
                                                                                        <rich:dataTable width="1000" rowClasses="linhaImpar, linhaPar"
                                                                                                        columnClasses="colunaEsquerda,colunaCentralizada,colunaCentralizada,colunaCentralizada,
                                                                                                        colunaCentralizada,colunaCentralizada,colunaCentralizada,colunaCentralizada"
                                                                                                        value="#{grupo.listaConsultores}" var="renovacao">
                                                                                            <rich:column sortBy="#{renovacao.colaborador.pessoa.nome}" filterEvent="onkeyup" width="400">
                                                                                                <f:facet name="header">
                                                                                                    <h:outputText value="Nome"/>
                                                                                                </f:facet>
                                                                                                <h:outputText value="#{renovacao.colaborador.pessoa.nome}"/>
                                                                                            </rich:column>
                                                                                            <rich:column sortBy="#{renovacao.qtdePrevista}">
                                                                                                <f:facet name="header">
                                                                                                    <h:outputText value="Previstos"/>
                                                                                                </f:facet>
                                                                                                <h:outputText value="#{renovacao.qtdePrevista}" />
                                                                                            </rich:column>
                                                                                            <rich:column sortBy="#{renovacao.qtdeRenovados}">
                                                                                                <f:facet name="header">
                                                                                                    <h:outputText value="Renovados"/>
                                                                                                </f:facet>
                                                                                                <h:outputText value="#{renovacao.qtdeRenovados}"/>
                                                                                            </rich:column>
                                                                                            <rich:column sortBy="#{renovacao.percentualRenovados}">
                                                                                                <f:facet name="header">
                                                                                                    <h:outputText value="% de Renovação"/>
                                                                                                </f:facet>
                                                                                                <h:outputText rendered="#{renovacao.alert}" styleClass="tituloCamposVermelhoNegrito" value="#{renovacao.percentualRenovados}%" />
                                                                                                <h:outputText rendered="#{!renovacao.alert}" styleClass="tituloCamposAzulGrandeNegrito" value="#{renovacao.percentualRenovados}%" />
                                                                                            </rich:column>
                                                                                        </rich:dataTable>
                                                                                        <rich:dataTable styleClass="semBorda" width="1000" value="" var=""
                                                                                                        columnClasses="colunaEsquerda,colunaCentralizada,colunaCentralizada,colunaCentralizada">
                                                                                            <rich:column width="400" styleClass="semBorda">
                                                                                                <h:outputText value=""/>
                                                                                            </rich:column>
                                                                                            <rich:column width="200" styleClass="semBorda">
                                                                                                <h:outputText styleClass="tituloCamposAzulGrandeNegrito" value="#{grupo.qtdePrevista}"/>
                                                                                            </rich:column>
                                                                                            <rich:column width="200" styleClass="semBorda">
                                                                                                <h:outputText styleClass="tituloCamposAzulGrandeNegrito" value="#{grupo.qtdeRenovados}"/>
                                                                                            </rich:column>
                                                                                            <rich:column width="200" styleClass="semBorda">
                                                                                                <h:outputText rendered="#{grupo.alert}" styleClass="tituloCamposVermelhoNegrito" value="#{grupo.percentualRenovados}%" />
                                                                                                <h:outputText rendered="#{!grupo.alert}" styleClass="tituloCamposAzulGrandeNegrito" value="#{grupo.percentualRenovados}%" />
                                                                                            </rich:column>
                                                                                        </rich:dataTable>
                                                                                        <rich:spacer style="display:block;" height="20"/>
                                                                                    </rich:column>
                                                                                </rich:dataTable>
                                                                            </rich:tab>
                                                                            <rich:tab switchType="client" styleClass="tituloPre" rendered="#{RenovacaoSinteticoControle.apresentarListaResumo6}"
                                                                                      label="#{RenovacaoSinteticoControle.mes6} / #{RenovacaoSinteticoControle.ano6} ">
                                                                                <rich:dataTable width="1000" styleClass="semBorda"
                                                                                                value="#{RenovacaoSinteticoControle.listaResumoConsultor6}" var="grupo">
                                                                                    <rich:column styleClass="semBorda">
                                                                                        <h:outputText styleClass="tituloCamposAzulGrandeNegrito" value="#{grupo.descricao}"/>
                                                                                        <rich:dataTable width="1000" rowClasses="linhaImpar, linhaPar"
                                                                                                        columnClasses="colunaEsquerda,colunaCentralizada,colunaCentralizada,colunaCentralizada,
                                                                                                        colunaCentralizada,colunaCentralizada,colunaCentralizada,colunaCentralizada"
                                                                                                        value="#{grupo.listaConsultores}" var="renovacao">
                                                                                            <rich:column sortBy="#{renovacao.colaborador.pessoa.nome}" filterEvent="onkeyup" width="400">
                                                                                                <f:facet name="header">
                                                                                                    <h:outputText value="Nome"/>
                                                                                                </f:facet>
                                                                                                <h:outputText value="#{renovacao.colaborador.pessoa.nome}"/>
                                                                                            </rich:column>
                                                                                            <rich:column sortBy="#{renovacao.qtdePrevista}">
                                                                                                <f:facet name="header">
                                                                                                    <h:outputText value="Previstos"/>
                                                                                                </f:facet>
                                                                                                <h:outputText value="#{renovacao.qtdePrevista}" />
                                                                                            </rich:column>
                                                                                            <rich:column sortBy="#{renovacao.qtdeRenovados}">
                                                                                                <f:facet name="header">
                                                                                                    <h:outputText value="Renovados"/>
                                                                                                </f:facet>
                                                                                                <h:outputText value="#{renovacao.qtdeRenovados}"/>
                                                                                            </rich:column>
                                                                                            <rich:column sortBy="#{renovacao.percentualRenovados}">
                                                                                                <f:facet name="header">
                                                                                                    <h:outputText value="% de Renovação"/>
                                                                                                </f:facet>
                                                                                                <h:outputText rendered="#{renovacao.alert}" styleClass="tituloCamposVermelhoNegrito" value="#{renovacao.percentualRenovados}%" />
                                                                                                <h:outputText rendered="#{!renovacao.alert}" styleClass="tituloCamposAzulGrandeNegrito" value="#{renovacao.percentualRenovados}%" />
                                                                                            </rich:column>
                                                                                        </rich:dataTable>
                                                                                        <rich:dataTable styleClass="semBorda" width="1000" value="" var=""
                                                                                                        columnClasses="colunaEsquerda,colunaCentralizada,colunaCentralizada,colunaCentralizada">
                                                                                            <rich:column width="400" styleClass="semBorda">
                                                                                                <h:outputText value=""/>
                                                                                            </rich:column>
                                                                                            <rich:column width="200" styleClass="semBorda">
                                                                                                <h:outputText styleClass="tituloCamposAzulGrandeNegrito" value="#{grupo.qtdePrevista}"/>
                                                                                            </rich:column>
                                                                                            <rich:column width="200" styleClass="semBorda">
                                                                                                <h:outputText styleClass="tituloCamposAzulGrandeNegrito" value="#{grupo.qtdeRenovados}"/>
                                                                                            </rich:column>
                                                                                            <rich:column width="200" styleClass="semBorda">
                                                                                                <h:outputText rendered="#{grupo.alert}" styleClass="tituloCamposVermelhoNegrito" value="#{grupo.percentualRenovados}%" />
                                                                                                <h:outputText rendered="#{!grupo.alert}" styleClass="tituloCamposAzulGrandeNegrito" value="#{grupo.percentualRenovados}%" />
                                                                                            </rich:column>
                                                                                        </rich:dataTable>
                                                                                        <rich:spacer style="display:block;" height="20"/>
                                                                                    </rich:column>
                                                                                </rich:dataTable>
                                                                            </rich:tab>
                                                                        </rich:tabPanel>
                                                                    </rich:tab>
                                                                </rich:tabPanel>
                                                            </h:panelGrid>
                                            </h:panelGroup>
                                        </td>
                                    </tr>
                                </table>
                            </td>
                        </tr>

                    </table>
                </td>
            </tr>
        </table>
    </h:form>
</f:view>
<style>
    body::-webkit-scrollbar {
        width: 12px;
        height: 12px/* width of the entire scrollbar */
    }
    body::-webkit-scrollbar-thumb {
        background-color: silver;
        border-radius: 10px;       /* roundness of the scroll thumb */
        border: 1px solid silver;  /* creates padding around scroll thumb */
    }
    body::-webkit-scrollbar-thumb:hover {
        background-color: grey;
    }
</style>
