    <%@include file="includes/imports.jsp" %>
<h:panelGroup id="selecionadas">
  											<h:panelGroup id="panParcelasSelecionadas" rendered="#{MovParcelaControle.apresentarParcelasSelecionadas}">
 											<table width="100%" border="0" cellspacing="0" cellpadding="0" >
                                                                    <tr>
                                                                        <td align="left" valign="top"><div style="clear:both;" class="text">
                                                                                <p style="margin-bottom:4px;"><img src="${root}/images/arrow2.gif" width="16" height="16" style="vertical-align:middle;margin-right:6px;">
                                                                                Parcelas Selecionadas</p>
                                                                            </div>
                                                                            <rich:separator height="2px" lineType="dotted"></rich:separator>
                                                                    </tr>
                                                          </table>
                                              <rich:spacer height="10px"></rich:spacer>           
                                                          <rich:dataTable id="tabelaParcelasSelecionadas" width="100%" columnClasses="colunaEsquerda, colunaCentralizada, colunaCentralizada" headerClass="consultaCEA" rowClasses="linhaPar"
                                                                          value="#{MovParcelaControle.itensSelecionados}" var="selecionada">
                                                                                                
                                                                                                <rich:column width="60%" style="vertical-align: middle;">
                                                                                                	<f:facet name="header">
                                                                                                		<h:outputText value="Pessoa - Nr. Parcelas Selecionadas"/>
                                                                                                	</f:facet>
                                                                                                	<rich:spacer width="30px"/>
                                                                                                	<a4j:commandButton id="btnRemoverPar" image="images/bt_remove.png" title="Remover todas as parcelas desta pessoa"
                                                                                                    				   action="#{MovParcelaControle.removerListaParcelaPagar}"
                                                                                                    				   reRender="selecionadas,tabelaItens,tabelaParcelas, cliente,parcela,responsavel,valorTotalParcela,responsavelAbaixo,valorAbaixo">
                                                                                                  	</a4j:commandButton>
                                                                                                  	<rich:spacer width="3px"></rich:spacer>
                                                                                                	<h:outputText id="nomeClienteParcela" value=" #{selecionada.nomeAbreviado} - #{selecionada.nrParcelasSelecionadas}"/>
                                                                                                </rich:column>
                                                                                                
                                                                                                <rich:column width="15%">
                                                                                                	<f:facet name="header">
                                                                                                		<h:outputText value="Valor"/>
                                                                                                	</f:facet>
                                                                                                    <h:outputText id="valorParcelasSel" value="#{selecionada.valorParcelasSelecionadas}">
                                                                                                        <f:converter converterId="FormatadorNumerico"/>
                                                                                                    </h:outputText>
                                                                                                </rich:column>
                                                                                                
                                                                          </rich:dataTable>
                                                                          <rich:spacer height="20px"></rich:spacer>     
                                                                          
                                                                           </h:panelGroup>
                                           </h:panelGroup>                                                                
										