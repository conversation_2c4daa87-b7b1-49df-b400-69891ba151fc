<%@page pageEncoding="ISO-8859-1"%>
<script src="script/packJQueryPlugins.min.js" type="text/javascript" ></script>
<%@include file="/includes/include_import_minifiles.jsp" %>
<%@taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<link href="css_pacto.css" rel="stylesheet" type="text/css">
<link href="${root}/beta/css/pacto-icon-font4.0.min.css" type="text/css" rel="stylesheet"/>
<script type="text/javascript" language="javascript" src="script/time_1.3.js"></script>
<body class="paginaFontResponsiva">
<f:view>
    <jsp:include page="includes/include_carregando_ripple.jsp" flush="true"/>
    <a4j:keepAlive beanName="TelaClienteControle"/>
    <a4j:keepAlive beanName="ConvidadoControle"/>
    <a4j:keepAlive beanName="FamiliaresControle"/>
    <title>Edição Cliente</title>
    <style>
        .textoAtencaoNovaVersao {
            color: #AF0404;
            font-family: "Nunito Sans";
            font-size: 16px;
            font-style: normal;
            font-weight: 700;
            padding-right: 5px;
        }
        .textoSaibaMaisNovaVersao {
            color: #1E60FA;
            font-family: "Nunito Sans";
            font-size: 16px;
            font-style: normal;
            font-weight: 700;
            padding-left: 5px;
        }
        .textoNovaVersao {
            color: #55585E;
            font-family: "Nunito Sans";
            font-size: 16px;
            font-style: normal;
            font-weight: 400;
        }
        .labelHoraNovaVersaoData {
            color: #AF0404;
            font-family: "Nunito Sans";
            font-size: 16px;
            font-style: normal;
            font-weight: 500;
        }
        .labelNovaVersaoData {
            color: #494B50;
            font-family: "Nunito Sans";
            font-size: 12px;
            font-style: normal;
            font-weight: 400;
        }

        .alignCenter{
            display: flex;
            justify-content: center;
        }
         .rich-table-footercell {
             padding: 0px 0px !important;
             background-color: white !important;
         }
         .rich-dtascroller-table {
             border: none !important;
         }
         .rich-datascr-button{
            color: #29ABE2 !important;
             cursor: pointer !important;
             background-image: none !important;
             background-color: white;
        }
        .rich-datascr-button:hover{
            background-color: white !important;
        }
        .rich-datascr-button-dsbld {
            color: #d3d3d3 !important;
            cursor: default !important;
        }
        .paginatorRemoveHover table > tbody > tr:hover{
            background-color: white !important;
            cursor: default;
        }

         .situacaoBallon {
             border-radius: 100px;
             color: white;
             padding: 4px 4px 4px 4px;
             text-align: center;
             display: inline-block;
             width: 65px;
             height: 15px;
             line-height: normal;
         }


        .btn-experimente {
            box-shadow: 0 2px 2px 0 rgba(169,169,169,.14), 0 3px 1px -2px rgba(169,169,169,.2), 0 1px 5px 0 rgba(169,169,169,.12);
            transition: .2s ease-in;
            background-color: #fff !important;
            color: #67757c !important;
            border-color: #b1b8bb !important;
            display: inline-block;
            font-weight: 400;
            text-align: center;
            white-space: nowrap;
            vertical-align: middle;
            -webkit-user-select: none;
            -moz-user-select: none;
            -ms-user-select: none;
            user-select: none;
            border: 1px solid transparent;
            transition: color .15s ease-in-out,background-color .15s ease-in-out,border-color .15s ease-in-out,box-shadow .15s ease-in-out;
            background: #fff !important;
            padding: 7px 12px !important;
            /*font-size: 15px !important;*/
            line-height: 1.5;
            border-radius: 5px;
            font-family: "Nunito Sans",sans-serif !important;
        }

        .btn-experimente:hover {
            box-shadow: 0 14px 26px -12px rgba(169,169,169,.42), 0 4px 23px 0 rgba(0,0,0,.12), 0 8px 10px -5px rgba(169,169,169,.2);
            color: #212529 !important;
            background-color: #b3b2b2 !important;
            border-color: #acacac !important;
        }

        .div-geral-experimente {
            display: grid;
            padding: 1.5% 0 0 1.5%;
            width: 96%;
            grid-template-columns: 2fr 0fr;
        }

        .div-experimente2 {
            justify-content: end;
            display: flex;
            padding-left: 15px;
        }

        .div-experimente {
            background-color: #fff !important;
            border-radius: 5px;
            font-family: "Nunito Sans",sans-serif !important;
            padding: 10px;
            align-items: center;
            display: flex;
        }

        .switch {
            position: relative;
            display: inline-block;
            width: 30px;
            height: 17px;
        }

        /* Hide default HTML checkbox */
        .switch input {
            opacity: 0;
            width: 0;
            height: 0;
        }

        /* The slider */
        .slider {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: #ccc;
            -webkit-transition: .4s;
            transition: .4s;
        }

        .slider:before {
            position: absolute;
            content: "";
            height: 13px;
            width: 13px;
            left: 2px;
            bottom: 2px;
            background-color: white;
            -webkit-transition: .4s;
            transition: .4s;
        }

        input:checked + .slider {
            background-color: #2196F3;
        }

        input:focus + .slider {
            box-shadow: 0 0 1px #2196F3;
        }

        input:checked + .slider:before {
            -webkit-transform: translateX(13px);
            -ms-transform: translateX(13px);
            transform: translateX(13px);
        }

        /* Rounded sliders */
        .slider.round {
            border-radius: 17px;
        }

        .slider.round:before {
            border-radius: 50%;
        }

        .padrao-cliente {
            display: flex;
            color: #797D86;
            font-size: 16px;
            font-weight: 400;
            line-height: 16px;
            padding: 30px 0px 0 40px;
            width: calc(100% - 80px);
            justify-content: flex-end;
        }

        .padrao-cliente label {
            margin-left: 8px;
        }

        .padrao-cliente .clicavel {
            cursor: pointer;
        }
    </style>

    <h:form id="form">

        <h:outputLink id="btnAtualizaCliente"
                      value="clienteNav.jsp?page=cliente&matricula=#{TelaClienteControle.cliente.matricula}"
                      target="_self" >
        </h:outputLink>
        <h:outputLink id="btnRederCliente"
                      value="clienteNav.jsp?page=cliente&matricula=#{TelaClienteControle.cliente.matricula}"
                      target="_self" >
        </h:outputLink>
        <a4j:commandLink id="btnAtualizaFotosCliente" reRender="panelFoto,painelAlunosMarcados" styleClass="googleAnalytics"
                         style="display: none;"/>


        <h:panelGroup layout="block" style="display: inline-table;width: 100%;" styleClass="fundoCinza googleAnalytics">
            <c:choose>
                <c:when test="${!param.readOnly}">
                    <h:panelGroup layout="block" styleClass="bgtop topoZW"
                                  rendered="#{MenuControle.apresentarTopo}">
                        <h:panelGroup styleClass="tudo">
                            <jsp:include page="include_topo_novo.jsp" flush="false"/>
                        </h:panelGroup>
                        <jsp:include page="include_menu_zw_flat.jsp" flush="false"/>
                    </h:panelGroup>
                </c:when>
                <c:otherwise>

                </c:otherwise>
            </c:choose>
            <c:if test="${SuperControle.menuZwUi}">
                <style>
                    .caixaCorpo{
                        width: calc(100vw - 266px);
                        margin-left: 266px;
                        margin-top: 85px;
                        overflow: auto;
                    }
                    .caixaCorpo.fechado-zwui{
                        width: calc(100vw - 84px);
                        margin-left: 84px;
                    }
                </style>
                <jsp:include page="include_box_menulateral.jsp" flush="true">
                    <jsp:param name="menu" value="ADM-INICIO" />
                </jsp:include>
            </c:if>
            <h:panelGroup layout="block" styleClass="caixaCorpo zw_ui"  rendered="#{LoginControle.permissaoAcessoMenuVO.cliente}">
                <h:panelGroup layout="block" style="height: 80%;width: 100%">
                    <h:panelGroup layout="block" styleClass="caixaMenuLatel">
                        <h:panelGroup layout="block" id="panelRecarregar">
                            <table width="100%" border="0" cellpadding="0" cellspacing="0">
                                <tr>
                                    <td width="150" align="left" valign="top" class="bglateraltop googleAnalytics" style="background-color: white;">
                                        <jsp:include page="include_box_usuario_flat.jsp" flush="false">
                                            <jsp:param name="readOnly" value="${param.readOnly}"/>
                                        </jsp:include>
                                    </td>
                                    <td align="left" valign="top" style="padding-left: 5px; padding-right: 0px;" class="scrollInfinito">

                                        <h:panelGroup layout="block" styleClass="div-geral-experimente"
                                                      rendered="#{LoginControle.apresentarModuloNovoTreino}">

                                            <h:panelGroup layout="block"
                                                          id="divMsgExperimenteNovaVersao"
                                                          styleClass="div-experimente">
                                                <h:graphicImage value="images/pct-alert-triangle.svg" style="width: 16px; padding-right: 5px; padding-bottom: 2px;"/>
                                                <h:outputText value="Nos próximos meses essa tela será descontinuada.
                                                              Experimente a nova versão e teste as melhorias.
                                                              Você poderá nos enviar feedbacks e ajudar a construir um sistema melhor para o seu dia a dia."
                                                              styleClass="texto-size-14"/>
                                            </h:panelGroup>

                                            <h:panelGroup layout="block"
                                                          styleClass="div-experimente2"
                                                          rendered="#{TelaClienteControle.novaTelaAlunoPadraoEmpresa}">
<%--                                                <a4j:commandButton id="abrirNovaTelaClienteExperimente"--%>
<%--                                                                   action="#{TelaClienteControle.abrirNovaTelaClienteExperimente}"--%>
<%--                                                                   value="Experimentar nova versão"--%>
<%--                                                                   alt="Experimentar nova versão"--%>
<%--                                                                   styleClass="btn-experimente texto-size-14"--%>
<%--                                                                   oncomplete="#{TelaClienteControle.msgAlert}"/>--%>
                                            </h:panelGroup>
                                        </h:panelGroup>

                                        <h:panelGroup layout="block"
                                                      rendered="#{!TelaClienteControle.novaTelaAlunoPadraoEmpresa && LoginControle.apresentarModuloNovoTreino}">
                                            <span class="padrao-cliente"
                                                  id="div-switch-nova-versao">
                                                <span class="clicavel" onclick="abrirNovaTelaClientePadrao()">Usar nova versão</span>
                                                <label class="switch clicavel" onclick="abrirNovaTelaClientePadrao()">
                                                    <input type="checkbox"
                                                           id="switch-nova-versao">
                                                    <span class="slider round"></span>
                                                </label>
                                            </span>
                                        </h:panelGroup>

                                        <h:panelGroup layout="block"
                                                      id="divContadorNovaVersaoTelaAluno"
                                                      style="display: none">
                                            <h:panelGroup layout="block"
                                                          style="background: #FAFAFA; border-radius: 8px; display: grid; margin: 1.5% 0 0 1.5%; width: 96%;">

                                                <h:panelGroup layout="block" id="divGeralContadorNovaVersao"
                                                              styleClass="divGeralContadorNovaVersao" style="padding: 20px;">

                                                    <h:panelGroup layout="block" id="divGeralContadorNovaVersaoSuperior"
                                                                  styleClass="divGeralContadorNovaVersaoSuperior"
                                                                  style="padding-bottom: 20px; display: flex;align-items: center;">
                                                        <h:panelGroup layout="block">
                                                            <h:graphicImage value="images/pct-alert-triangle-ds3.svg"
                                                                            style="width: 32px; padding-right: 5px;"/>
                                                        </h:panelGroup>
                                                        <h:panelGroup layout="block" style="display: flex">
                                                            <h:outputText styleClass="textoAtencaoNovaVersao" value="Atenção: "/>
                                                            <h:outputText styleClass="textoNovaVersao" value="Esta tela será desligada em "/>
                                                            <h:outputText id="novaVersaoTelaAlunoData" styleClass="textoNovaVersao" style="padding-left: 5px" value=""/>
                                                            <h:outputText styleClass="textoNovaVersao" value=". Recomendamos que utilize a nova tela para obter familiaridade até o desligamento dessa. "/>
                                                            <h:outputLink styleClass="textoSaibaMaisNovaVersao"
                                                                          target="_blank"
                                                                          value="https://pactosolucoes.com.br/ajuda/conhecimento/adm-sua-antiga-tela-do-perfil-do-aluno-sera-desativada/">
                                                                Saiba mais
                                                            </h:outputLink>
                                                        </h:panelGroup>
                                                    </h:panelGroup>

                                                    <h:panelGroup layout="block" id="divGeralContadorNovaVersaoInferior"
                                                                  styleClass="divGeralContadorNovaVersaoInferior"
                                                                  style="justify-content: center;display: flex;">
                                                        <h:panelGroup layout="block"
                                                                      style="padding: 15px;display: grid;grid-template-columns: 1fr 1fr 1fr 1fr;text-align: center;width: 45%;border: 1px solid #E23661;border-radius: 8px">
                                                            <h:panelGroup layout="block" style="display: grid">
                                                                <h:outputText id="qtdDiasNovaVersao"
                                                                              styleClass="labelHoraNovaVersaoData"
                                                                              value="00"/>
                                                                <h:outputText styleClass="labelNovaVersaoData" value="Dias"/>
                                                            </h:panelGroup>
                                                            <h:panelGroup layout="block" style="display: grid; border-left: 1px solid #D7D8DB;">
                                                                <h:outputText id="qtdHorasNovaVersao"
                                                                              styleClass="labelHoraNovaVersaoData"
                                                                              value="00"/>
                                                                <h:outputText styleClass="labelNovaVersaoData" value="Horas"/>
                                                            </h:panelGroup>
                                                            <h:panelGroup layout="block" style="display: grid; border-left: 1px solid #D7D8DB;">
                                                                <h:outputText id="qtdMinutosNovaVersao"
                                                                              styleClass="labelHoraNovaVersaoData"
                                                                              value="00"/>
                                                                <h:outputText styleClass="labelNovaVersaoData" value="Minutos"/>
                                                            </h:panelGroup>
                                                            <h:panelGroup layout="block" style="display: grid; border-left: 1px solid #D7D8DB;">
                                                                <h:outputText id="qtdSegundosNovaVersao"
                                                                              styleClass="labelHoraNovaVersaoData"
                                                                              value="00"/>
                                                                <h:outputText styleClass="labelNovaVersaoData" value="Segundos"/>
                                                            </h:panelGroup>
                                                        </h:panelGroup>
                                                    </h:panelGroup>
                                                </h:panelGroup>
                                            </h:panelGroup>
                                        </h:panelGroup>

                                        <div style="width: 100%;" class="googleAnalytics">
                                            <div class="painelDadosAluno tudo step1">
                                                <jsp:include page="include_box_dados_cliente.jsp" flush="false">
                                                    <jsp:param name="readOnly" value="${param.readOnly}"/>
                                                </jsp:include>
                                            </div>
                                            <div class="painelDadosAluno" style="box-shadow: none; background-color: transparent;">
                                                <div class="painelDadosAluno tudo" style="margin: 0; width: 100%; height: 21vh; display: table; margin-bottom: 3vh;">
                                                    <div class="tituloPainelAluno">
                                                        <h:outputText value="Avisos" styleClass="texto-size-14 negrito cinzaEscuro" id="tableAvisosCliente"
                                                                      style="margin-left: 1vw;"/>
                                                        <h:outputLink styleClass="pl5"
                                                                      value="#{SuperControle.urlBaseConhecimento}tela-do-cliente-modulo-adm/" id="abrirWikiAvisosCliente"
                                                                      title="Clique e saiba mais: Aviso(s) Aluno"
                                                                      target="_blank" >
                                                            <i id="abrirWikiAvisosCliente" class="fa-icon-question-sign cinzaClaro" style="font-size: 18px"></i>
                                                        </h:outputLink>

                                                        <a4j:commandLink oncomplete="Richfaces.showModalPanel('mdlAvisos');" id="abrirAvisosCliente"
                                                                         styleClass="linkAzul floatright"
                                                                         style="margin-right: 1vw;"
                                                                         reRender="painelmdlAvisos"
                                                                         action="#{TelaClienteControle.verTodosAvisos}">
                                                            <h:outputText id="abrirAvisosClienteText" value="Ver todos"/>
                                                            <h:outputText id="abrirAvisosClienteIcon" styleClass="pl5 fa-icon-angle-right"/>
                                                        </a4j:commandLink>
                                                    </div>
                                                    <h:dataTable
                                                            id="avisosCliente" width="100%" rows="2"
                                                            value="#{TelaClienteControle.avisos}"
                                                            styleClass="tabFormSubordinada" var="avisoCliente">
                                                        <h:column>
                                                            <h:panelGroup layout="block"
                                                                          style="min-height: 30px;background-color: #{avisoCliente.tipomensagem.grave ? '#F4C9D1' : ''}; width: calc(100% - 2vw); line-height: 30px; padding: 10px 1vw;">
                                                                <h:panelGroup style="color:red;"
                                                                              styleClass="pulsante"
                                                                              rendered="#{avisoCliente.tipomensagem.grave}">
                                                                    <i class="fa fa-icon-warning-sign"></i>
                                                                </h:panelGroup>
                                                                <h:panelGroup rendered="#{avisoCliente.navegacaoFrame}">
                                                                    <a4j:commandLink id="linkMsgCliente"  title="#{avisoCliente.tipomensagem}"
                                                                                     action="#{TelaClienteControle.abreTela}"
                                                                                     actionListener="#{TelaClienteControle.selecionarClienteMensagemListener}">
                                                                        <f:attribute name="objClienteMensagem" value="#{avisoCliente}"/>
                                                                        <h:outputText id="textoMsgCliente" value="#{avisoCliente.mensagemTratadaMin}"  styleClass="texto-size-14 cinza"  escape="false"/>
                                                                        <i class="fa-icon-arrow-right cinza" style="float: right; margin: 7px;"></i>
                                                                    </a4j:commandLink>
                                                                </h:panelGroup>
                                                                <h:panelGroup rendered="#{avisoCliente.navegacaoPopUp}">
                                                                    <a4j:commandLink id="linkMsgCliente1" title="#{avisoCliente.tipomensagem}"
                                                                                     action="#{TelaClienteControle.abreTela}"
                                                                                     actionListener="#{TelaClienteControle.selecionarClienteMensagemListener}"
                                                                                     oncomplete="abrirPopup('#{avisoCliente.tipomensagem.navegacao}', '#{avisoCliente.tipomensagem}', 780, 600);">
                                                                        <f:attribute name="objClienteMensagem" value="#{avisoCliente}"/>
                                                                        <h:outputText id="textoMsgCliente1"  styleClass="texto-size-14 cinza" value="#{avisoCliente.mensagemTratadaMin}" escape="false"/>
                                                                        <i class="fa-icon-arrow-right cinza" style="float: right; margin: 7px;"></i>

                                                                    </a4j:commandLink>
                                                                </h:panelGroup>
                                                                <h:panelGroup rendered="#{!avisoCliente.navegacaoPopUp and !avisoCliente.navegacaoFrame and avisoCliente.tipomensagem.sigla != 'ES'}">
                                                                    <h:outputText styleClass="texto-size-14 cinza tooltipster"
                                                                                  value="#{avisoCliente.mensagemTratadaMin}  <span style=\"color: lightgray;\"> (Passe o mouse para visualizar a mensagem)</span>" escape="false"
                                                                                  title="#{avisoCliente.mensagemTratada}"/>
                                                                </h:panelGroup>
                                                                <h:panelGroup rendered="#{!avisoCliente.navegacaoPopUp and !avisoCliente.navegacaoFrame and avisoCliente.tipomensagem.sigla == 'ES'}">
                                                                    <h:outputText styleClass="texto-size-14 cinza tooltipster"
                                                                                  value="<b>Estorno por desistência de compra </b> <br> #{avisoCliente.mensagemTratadaMin} <span style=\"color: lightgray;\"> (Passe o mouse para visualizar a mensagem)</span>" escape="false"
                                                                                  title="#{avisoCliente.mensagemTratada}"/>
                                                                </h:panelGroup>


                                                            </h:panelGroup>
                                                        </h:column>
                                                    </h:dataTable>
                                                </div>
                                                <h:panelGroup id="ultimaObservacao" layout="block"
                                                              styleClass="painelDadosAluno tudo googleAnalytics"
                                                              style="margin: 0; width: 100%; height: 21vh; display: table;">
                                                    <h:panelGroup layout="block" styleClass="tituloPainelAluno">
                                                        <h:outputText value="Observação" styleClass="texto-size-14 negrito cinzaEscuro"
                                                                      style="margin-left: 1vw;"/>
                                                        <h:outputLink styleClass="pl5" id="wikiObservacoesAluno"
                                                                      value="#{SuperControle.urlBaseConhecimento}tela-do-cliente-modulo-adm/"
                                                                      title="Clique e saiba mais: Observações Aluno"
                                                                      target="_blank" >
                                                            <i class="fa-icon-question-sign cinzaClaro" style="font-size: 18px" id="wikiObservacoesAlunoIcon"></i>
                                                        </h:outputLink>

                                                        <a4j:commandLink oncomplete="Richfaces.showModalPanel('mdlObservacoes');"
                                                                         id="verTodasObservacoes"
                                                                         styleClass="linkAzul floatright"
                                                                         style="margin-right: 1vw;"
                                                                         reRender="mdlObservacoes"
                                                                         rendered="#{TelaClienteControle.apresentarAlteracao}"
                                                                         action="#{TelaClienteControle.prepararAbrirObservacao}">
                                                            <h:outputText value="Ver todas"/>
                                                            <h:outputText id="verTodasObservacoesIcon" styleClass="pl5 fa-icon-angle-right"/>
                                                        </a4j:commandLink>

                                                        <a4j:commandLink
                                                                id="modalObservacao"
                                                                oncomplete="Richfaces.showModalPanel('mdlObservacoesAdicionar');"
                                                                styleClass="linkAzul floatright googleAnalytics"
                                                                style="margin-right: 1vw;"
                                                                reRender="mdlObservacoesAdicionar"
                                                                action="#{TelaClienteControle.prepararAbrirObservacaoAdicionar}">
                                                            <h:outputText value="Adicionar" id="adicionarObservacaoText"/>
                                                            <h:outputText styleClass="pl5 fa-icon-plus-sign" id="adicionarObservacaoIcon"/>
                                                        </a4j:commandLink>

                                                    </h:panelGroup>
                                                    <h:panelGroup layout="block" styleClass="containerPequeno" style="height: 8vh; overflow-x: auto;">
                                                        <h:outputText styleClass="texto-size-14 cinza nop"
                                                                      style="padding: 0;"
                                                                      value="#{TelaClienteControle.ultimaObservacao.observacaoTratada}" escape="false"/>
                                                    </h:panelGroup>
                                                    <h:panelGroup layout="block" styleClass="containerPequeno">
                                                        <h:panelGroup rendered="#{TelaClienteControle.ultimaObservacao.codigo > 0}">
                                                            <h:outputText value="#{TelaClienteControle.ultimaObservacao.usuarioVO.nomeAbreviado}"
                                                                          id="nomeAbreviadoUsuario"
                                                                          styleClass="texto-size-12" style="color: #9E9E9E;"/>
                                                            <h:outputText value="#{TelaClienteControle.ultimaObservacao.dataCadastro}"
                                                                          rendered="#{TelaClienteControle.apresentarAlteracao}"
                                                                          styleClass="texto-size-12"
                                                                          style="float: right;color: #9E9E9E;">
                                                                <f:convertDateTime type="date" dateStyle="short"
                                                                                   locale="pt"
                                                                                   pattern="dd 'de' MMMM 'de' yyyy 'às' HH:mm" />
                                                            </h:outputText>
                                                        </h:panelGroup>
                                                    </h:panelGroup>

                                                </h:panelGroup>

                                            </div>
                                        </div>
                                        <h:panelGroup style="width: 96%; height: auto !important;z-index: 2;min-width: 700px;"
                                                      styleClass="painelDadosAluno step3 tudo googleAnalytics"
                                                      id="idpainelcontrato">

                                            <jsp:include page="includes/cliente/include_linha_tempo_contrato.jsp"/>
                                            <div class="tituloPainelAluno informacaoMatriculaAluno tudo">
                                                <h:outputText styleClass="texto-size-14 cinza pl20 tooltipster"
                                                              escape="false"
                                                              rendered="#{TelaClienteControle.cliente.situacaoClienteSinteticoVO.dataMatricula != null}"
                                                              value=" #{TelaClienteControle.dataMatriculaPorExtenso}"
                                                              title="#{TelaClienteControle.dataMatriculaPorExtensoAteFimContato}"/>
                                                <h:outputText styleClass="texto-size-14 cinza pl20 tooltipster"
                                                              escape="false"
                                                              rendered="#{TelaClienteControle.possuiPermissaoVerCacBiLtv and TelaClienteControle.cliente.situacaoClienteSinteticoVO.dataMatricula != null}"
                                                              value="(CAC atual: R$ #{LtvControle.cacClienteIndividual})"
                                                              title="O Custo de Aquisição de Clientes (CAC), como o próprio nome sugere, é o quanto sua empresa investe mensalmente para conquistar novos clientes" />

                                            </div>
                                        </h:panelGroup>

                                        <jsp:include page="includes/cliente/include_contratos_dependente.jsp"/>

                                        <h:panelGroup id="idlistacontratos" style="width: 96%; height: auto;margin: 0 0 0 1.5%;min-width: 700px;"
                                                      styleClass="painelDadosAluno contratos blocoContratos blocosPrincipais visivel tudo step5 googleAnalytics">
                                            <c:if test="${!param.readOnly}">
                                                <h:panelGroup layout="block"
                                                              rendered="#{TelaClienteControle.contratoSelecionado == null}"
                                                              styleClass="tituloPainelAluno">
                                                    <h:outputText value="Contratos"
                                                                  styleClass="texto-size-14 negrito cinzaEscuro pl20"/>
                                                    <h:outputLink styleClass="pl5"
                                                                  value="#{SuperControle.urlBaseConhecimento}tela-do-cliente-modulo-adm/"
                                                                  title="Clique e saiba mais: Contratos"
                                                                  target="_blank">
                                                        <i class="fa-icon-question-sign cinzaClaro" style="font-size: 18px"></i>
                                                    </h:outputLink>
                                                    <h:panelGroup layout="block" rendered="#{TelaClienteControle.apresentarTopo && TelaClienteControle.menuContrato}">
                                                        <a4j:commandLink id="btnNovoContratoLista"
                                                                         styleClass="texto-cor-azul texto-size-14 linkPadrao pull-right"
                                                                         action="#{ContratoControle.novoContratoViaTelaJsf}"
                                                                         reRender="panelIncludeMensagem"
                                                                         style="padding-right: 20px"
                                                                         rendered="#{!TelaClienteControle.novaNegociacaoPadrao}"
                                                                         oncomplete="#{ClienteControle.mensagemNotificar}#{ClienteControle.mostrarRichModalPanelListaContratosARenovarOuRematricular}">
                                                            <i class="fa-icon-plus-sign"></i> Novo Contrato
                                                        </a4j:commandLink>

                                                        <a4j:commandLink id="btnNovoContratoListaNova"
                                                                         styleClass="texto-cor-azul texto-size-14 linkPadrao pull-right"
                                                                         action="#{TelaClienteControle.abrirNovNegociacao()}"
                                                                         rendered="#{TelaClienteControle.novaNegociacaoPadrao}"
                                                                         style="padding-right: 20px"
                                                                         oncomplete="#{TelaClienteControle.msgAlert}#{TelaClienteControle.mensagemNotificar}">
                                                            <i class="fa-icon-plus-sign"></i> Novo Contrato
                                                        </a4j:commandLink>


                                                    </h:panelGroup>
                                                </h:panelGroup>
                                            </c:if>
                                            <h:outputText value="Nenhum contrato."
                                                          style="margin-top: 20px; display: block; margin-bottom: 20px;"
                                                          styleClass="texto-size-14 cinza pl20"
                                                          rendered="#{empty TelaClienteControle.listaContratos}"/>

                                            <jsp:include page="includes/cliente/include_panelgrid_lista_contratos_viewcliente_flat.jsp" flush="false"/>
                                            <jsp:include page="includes/cliente/include_detalhes_contrato.jsp" flush="false">
                                                <jsp:param name="readOnly" value="${param.readOnly}"/>
                                            </jsp:include>
                                            <div class="rodapePainelAluno" style="text-align: right;">
                                                <c:if test="${fn:length(TelaClienteControle.listaContratos) eq 3}">
                                                    <a4j:commandLink reRender="idlistacontratos"
                                                                     id="listarTodoContratos" styleClass="linkAzul texto-size-14"
                                                                     style="margin-right: 20px;"
                                                                     action="#{TelaClienteControle.verMaisContratos}">
                                                        <h:outputText value="Ver mais" />
                                                        <i class="fa-icon-angle-right"></i>

                                                    </a4j:commandLink>
                                                </c:if>
                                                <c:if test="${fn:length(TelaClienteControle.listaContratos) gt 3}">
                                                    <a4j:commandLink reRender="idlistacontratos"
                                                                     styleClass="linkAzul texto-size-14"
                                                                     style="margin-right: 20px;"
                                                                     action="#{TelaClienteControle.verMenosContratos}">
                                                        <h:outputText value="Ver menos" />
                                                        <i class="fa-icon-angle-left"></i>
                                                    </a4j:commandLink>
                                                </c:if>
                                            </div>
                                        </h:panelGroup>

                                        <div  style="width: 96%; height: auto;margin: 0 0 0 1.5%; display: none;min-width: 700px;"
                                              class="painelDadosAluno blocoRelacionamento blocosPrincipais tudo googleAnalytics">
                                            <h:panelGroup layout="block" id="idblocoRelacionamento">
                                                <c:if test="${!TelaClienteControle.carregarRelacionamento}">

                                                    <div class="tituloPainelAluno">
                                                        <div id="botoesTLR" style="display: inline-flex; padding: 10px;">
                                                            <a4j:commandLink status="false" id="idbtnContatos"
                                                                             styleClass="botaoModoTimeLine ativo blocoContatos botaoBlocoFinanceiro"
                                                                             onclick="trocarBloco('.blocoContatos', '.containerCompras', '.botaoBlocoFinanceiro');">
                                                                <h:outputText value="Contatos"/>
                                                            </a4j:commandLink>
                                                            <a4j:commandLink styleClass="botaoModoTimeLine blocoAmigoFit botaoBlocoFinanceiro" rendered="#{ClienteControle.apresentarpanelAmigoFit}"
                                                                             status="false" id="idbtnAmigoFit"
                                                                             onclick="trocarBloco('.blocoAmigoFit', '.containerCompras', '.botaoBlocoFinanceiro');">
                                                                <h:outputText value="AmigoFit"/>
                                                            </a4j:commandLink>

                                                        </div>
                                                    </div>

                                                    <h:panelGroup id="idListaCompras1"
                                                                  layout="block" styleClass="containerCompras blocoContatos visivel">

                                                        <h:outputText value="Nenhum contato."
                                                                      style="margin-top: 20px; display: block; margin-bottom: 20px;"
                                                                      styleClass="texto-size-14 cinza pl20"
                                                                      rendered="#{empty TelaClienteControle.listaHistoricoContato}"/>

                                                        <c:if test="${!TelaClienteControle.carregarRelacionamento}">
                                                            <h:panelGroup rendered="#{!empty TelaClienteControle.listaHistoricoContato}">
                                                                <table class="tblHeaderLeft semZebra">
                                                                    <tr>
                                                                        <th><h:outputText styleClass="texto-size-14 cinza negrito" value="DIA"/></th>
                                                                        <th><h:outputText styleClass="texto-size-14 cinza negrito" value="TIPO CONTATO"/></th>
                                                                        <th><h:outputText styleClass="texto-size-14 cinza negrito" value="RESULTADO"/></th>
                                                                        <th><h:outputText styleClass="texto-size-14 cinza negrito" value="RESPONSÁVEL"/></th>
                                                                        <th></th>
                                                                    </tr>
                                                                    <a4j:repeat value="#{TelaClienteControle.listaHistoricoContato}"
                                                                                var="contato">
                                                                        <tr>
                                                                            <td><h:outputText styleClass="texto-size-14 cinza"
                                                                                              value="#{contato.dia_Apresentar}"/></td>
                                                                            <td><h:outputText styleClass="texto-size-14 cinza"
                                                                                              value="#{contato.tipoContato_Apresentar}"/></td>
                                                                            <td><h:outputText styleClass="texto-size-14 cinza"
                                                                                              value="#{contato.resultado}"/></td>
                                                                            <td><h:outputText styleClass="texto-size-14 cinza"
                                                                                              value="#{contato.responsavelApresentar}"/></td>
                                                                            <td><h:outputText styleClass="fa-icon-info-sign texto-size-14 cinza tooltipster"
                                                                                              title="#{contato.observacao}"/></td>
                                                                        </tr>
                                                                    </a4j:repeat>

                                                                </table>
                                                            </h:panelGroup>
                                                            <script>
                                                                carregarTooltipster();
                                                            </script>
                                                        </c:if>
                                                        <h:panelGroup styleClass="rodapePainelAluno" layout="block" style="text-align: right;">
                                                            <a4j:commandLink styleClass="linkAzul" style="margin-right: 20px;" id="verIndicacoesAmigoFit"
                                                                             action="#{TelaClienteControle.verMaisHistoricoContato}" reRender="idblocoRelacionamento">
                                                                <i id="vercontatosLinhaTempoClienteIcon" class="fa-icon-angle-right"></i> Ver contatos
                                                            </a4j:commandLink>
                                                        </h:panelGroup>
                                                    </h:panelGroup>
                                                    <h:panelGroup layout="block" styleClass="containerCompras blocoAmigoFit" style="display: none;">
                                                        <h:outputText value="Nenhum Indicação."
                                                                      style="margin-top: 20px; display: block; margin-bottom: 20px;"
                                                                      styleClass="texto-size-14 cinza pl20"
                                                                      rendered="#{empty TelaClienteControle.listaIndicacoesAmigoFit}"/>

                                                        <c:if test="${!TelaClienteControle.carregarRelacionamento}">
                                                            <h:panelGroup rendered="#{!empty TelaClienteControle.listaIndicacoesAmigoFit}">
                                                                <table class="tblHeaderLeft semZebra">
                                                                    <tr>
                                                                        <th><h:outputText styleClass="texto-size-14 cinza negrito" value="DATA INDICAÇÃO"/></th>
                                                                        <th><h:outputText styleClass="texto-size-14 cinza negrito" value="NOME INDICADO"/></th>
                                                                        <th><h:outputText styleClass="texto-size-14 cinza negrito" value="CPF"/></th>
                                                                        <th><h:outputText styleClass="texto-size-14 cinza negrito" value="CONTATO"/></th>
                                                                        <th></th>
                                                                    </tr>
                                                                    <a4j:repeat value="#{TelaClienteControle.listaIndicacoesAmigoFit}"
                                                                                var="indicado">
                                                                        <tr>
                                                                            <td><h:outputText styleClass="texto-size-14 cinza"
                                                                                              value="#{indicado.dataLancamento_Apresentar}"/></td>
                                                                            <td><h:outputText styleClass="texto-size-14 cinza"
                                                                                              value="#{indicado.nomeIndicado}"/></td>
                                                                            <td><h:outputText styleClass="texto-size-14 cinza"
                                                                                              value="#{indicado.cpf}"/></td>
                                                                            <td><h:outputText styleClass="texto-size-14 cinza"
                                                                                              value="#{indicado.telefone}"/></td>
                                                                        </tr>
                                                                    </a4j:repeat>

                                                                </table>
                                                            </h:panelGroup>
                                                            <script>
                                                                carregarTooltipster();
                                                            </script>
                                                        </c:if>

                                                    </h:panelGroup>
                                                    <script>
                                                        carregarTooltipster();
                                                    </script>

                                                </c:if>
                                            </h:panelGroup>
                                        </div>



                                        <h:panelGroup
                                                id="blocoGeralNotasFiscais"
                                                rendered="#{LoginControle.mostrarBotaoNotaFiscalPerfilCliente || LoginControle.configuracaoSistema.utilizarServicoSesiSC }"
                                                style="width: 96%; height: auto;margin: 0 0 0 1.5%; display: none;min-width: 700px;"
                                                styleClass="painelDadosAluno blocoNotasFiscais blocosPrincipais tudo googleAnalytics">

                                            <div class="tituloPainelAluno">
                                                <h:outputText value="Notas Fiscais" styleClass="texto-size-14 negrito cinzaEscuro pl20"/>
                                            </div>

                                            <h:panelGroup id="idblocoNotasFiscais" >
                                                <h:outputText value="Nenhuma nota fiscal."
                                                              style="margin-top: 20px; display: block; margin-bottom: 20px;"
                                                              styleClass="texto-size-14 cinza pl20"
                                                              rendered="#{empty TelaClienteControle.listaNotasFiscais}"/>

                                                <c:if test="${!TelaClienteControle.carregarNotasFiscais}">
                                                    <h:panelGroup rendered="#{!empty TelaClienteControle.listaNotasFiscais}">
                                                        <table class="tblHeaderLeft semZebra">
                                                            <tr>
                                                                <c:if test="${!LoginControle.configuracaoSistema.utilizarServicoSesiSC}">
                                                                    <th><h:outputText styleClass="texto-size-14 cinza negrito" value="TIPO"/></th>
                                                                    <th><h:outputText styleClass="texto-size-14 cinza negrito" value="ID"/></th>
                                                                </c:if>
                                                                <th><h:outputText styleClass="texto-size-14 cinza negrito" value="SÉRIE"/></th>
                                                                <th><h:outputText styleClass="texto-size-14 cinza negrito" value="NR. RPS"/></th>
                                                                <c:if test="${!LoginControle.configuracaoSistema.utilizarServicoSesiSC}">
                                                                    <th><h:outputText styleClass="texto-size-14 cinza negrito" value="NR. NOTA"/></th>
                                                                    <th><h:outputText styleClass="texto-size-14 cinza negrito" value="RAZÃO SOCIAL"/></th>
                                                                </c:if>
                                                                <th><h:outputText styleClass="texto-size-14 cinza negrito" value="DT. EMISSÃO"/></th>
                                                                <th><h:outputText styleClass="texto-size-14 cinza negrito" value="VALOR"/></th>
                                                                <th><h:outputText styleClass="texto-size-14 cinza negrito" value="STATUS"/></th>
                                                                <c:if test="${!LoginControle.configuracaoSistema.utilizarServicoSesiSC}">
                                                                     <th><h:outputText styleClass="texto-size-14 cinza negrito" value="DT. PROCESSAMENTO"/></th>
                                                                </c:if>
                                                                <c:if test="${LoginControle.configuracaoSistema.utilizarServicoSesiSC}">
                                                                    <th><h:outputText styleClass="texto-size-14 cinza negrito" value="DT. ENVIO"/></th>
                                                                </c:if>
                                                                <th><h:outputText styleClass="texto-size-14 cinza negrito" value="AÇÕES"/></th>
                                                            </tr>
                                                            <a4j:repeat value="#{TelaClienteControle.listaNotasFiscais}"
                                                                        var="nota">
                                                                <tr>
                                                                    <c:if test="${!LoginControle.configuracaoSistema.utilizarServicoSesiSC}">
                                                                        <td><h:outputText styleClass="texto-size-14 cinza"
                                                                                          value="#{nota.tipoNotaApresentar}"/></td>
                                                                        <td><h:outputText styleClass="texto-size-14 cinza"
                                                                                          value="#{nota.idReferencia}"/></td>
                                                                    </c:if>
                                                                    <td><h:outputText styleClass="texto-size-14 cinza"
                                                                                      value="#{nota.serie}"/></td>
                                                                    <td><h:outputText styleClass="texto-size-14 cinza"
                                                                                      value="#{nota.nrRPS}"/></td>
                                                                    <c:if test="${!LoginControle.configuracaoSistema.utilizarServicoSesiSC}">
                                                                        <td><h:outputText styleClass="texto-size-14 cinza"
                                                                                          value="#{nota.nrNota}"/></td>
                                                                        <td><h:outputText styleClass="texto-size-14 cinza"
                                                                                          value="#{nota.razaoSocial}"/></td>
                                                                    </c:if>
                                                                    <td><h:outputText styleClass="texto-size-14 cinza"
                                                                                      value="#{nota.dataEmissao}"/></td>
                                                                    <td><h:outputText styleClass="texto-size-14 cinza"
                                                                                      value="#{nota.valor}"/></td>
                                                                    <td><h:outputText styleClass="texto-size-14 cinza"
                                                                                      value="#{nota.status}"/></td>
                                                                    <td><h:outputText styleClass="texto-size-14 cinza"
                                                                                      value="#{nota.dataProcessamento}"/></td>
                                                                    <td style="display: flex;">
                                                                        <c:if test="${!LoginControle.configuracaoSistema.utilizarServicoSesiSC}">
                                                                            <a4j:commandLink styleClass="fa-icon-paper-plane texto-size-18 linkAzul tooltipster"
                                                                                             rendered="#{nota.mostrarAcoesNotaFiscalTelaCliente}"
                                                                                             action="#{TelaClienteControle.prepararEnvioEmailNota}"
                                                                                             reRender="modalEnviarNotaEmail"
                                                                                             oncomplete="#{TelaClienteControle.onCompleteNota}"
                                                                                             title="Solicitar envio por email"/>

                                                                            <h:outputLink styleClass="fa-icon-file-pdf texto-size-18 linkAzul tooltipster"
                                                                                          rendered="#{nota.mostrarAcoesNotaFiscalTelaCliente}"
                                                                                          title="Download do PDF da nota fiscal"
                                                                                          value="#{nota.linkPDF}"
                                                                                          target="_blank"/>

                                                                            <h:outputText styleClass="fa-icon-info-sign texto-size-14 cinza tooltipster"
                                                                                          rendered="#{!nota.mostrarAcoesNotaFiscalTelaCliente}"
                                                                                          title="Nota não está autorizada"/>
                                                                        </c:if>
                                                                        <c:if test="${LoginControle.configuracaoSistema.utilizarServicoSesiSC}">
                                                                            <a4j:commandLink id="btnPDF"
                                                                                             styleClass="exportadores margin-h-10"
                                                                                             action="#{TelaClienteControle.imprimirNotaSesc}"
                                                                                             oncomplete="abrirPopup('UpdateServlet?op=downloadfile&file=#{TelaClienteControle.fileNameNotaSesi}&mimetype=application/pdf','Transacoes', 800,200);#{ExportadorListaControle.msgAlert}"
                                                                                             accesskey="4">

                                                                                <h:outputText title="#{msg_aplic.prt_exportar_form_pdf}" styleClass="btn-print-2 pdf"/>
                                                                            </a4j:commandLink>
                                                                        </c:if>
                                                                    </td>
                                                                </tr>
                                                            </a4j:repeat>

                                                        </table>
                                                    </h:panelGroup>
                                                    <script>
                                                        carregarTooltipster();
                                                    </script>
                                                </c:if>

                                                <h:panelGroup styleClass="rodapePainelAluno" layout="block" style="text-align: right;">
                                                    <a4j:commandLink styleClass="linkAzul" style="margin-right: 20px;"
                                                                     id="verMaisNotasFiscais"
                                                                     action="#{TelaClienteControle.verMaisNotasFiscais}"
                                                                     reRender="idblocoNotasFiscais">
                                                        <i class="fa-icon-angle-right"></i> Ver mais
                                                    </a4j:commandLink>
                                                </h:panelGroup>
                                            </h:panelGroup>
                                        </h:panelGroup>

                                        <h:panelGroup rendered="#{LoginControle.apresentarLinkCE}"
                                                      style="width: 96%; height: auto;margin: 0 0 0 1.5%; display: none;min-width: 700px;"
                                                      styleClass="painelDadosAluno blocoCentral blocosPrincipais tudo googleAnalytics">

                                            <div class="tituloPainelAluno">
                                                <h:outputText value="Eventos"
                                                              styleClass="texto-size-14 negrito cinzaEscuro pl20"/>
                                            </div>

                                            <h:panelGroup id="idblocoCentral">
                                                <h:outputText value="Nenhum evento."
                                                              style="margin-top: 20px; display: block; margin-bottom: 20px;"
                                                              styleClass="texto-size-14 cinza pl20"
                                                              rendered="#{empty TelaClienteControle.eventos}"/>

                                                <c:if test="${!TelaClienteControle.carregarCentral}">
                                                    <h:panelGroup rendered="#{!empty TelaClienteControle.eventos}">
                                                        <table class="tblHeaderLeft semZebra">
                                                            <tr>
                                                                <th><h:outputText
                                                                        styleClass="texto-size-14 cinza negrito"
                                                                        value="DATA EVENTO"/></th>
                                                                <th><h:outputText
                                                                        styleClass="texto-size-14 cinza negrito"
                                                                        value="DESCRIÇÃO"/></th>
                                                                <th><h:outputText
                                                                        styleClass="texto-size-14 cinza negrito"
                                                                        value="VALOR"/></th>
                                                                <th></th>
                                                            </tr>
                                                            <a4j:repeat value="#{TelaClienteControle.eventos}"
                                                                        var="evento">
                                                                <tr>
                                                                    <td><h:outputText styleClass="texto-size-14 cinza"
                                                                                      value="#{evento.dataEventoApresentar}"/></td>
                                                                    <td>
                                                                        <h:outputText styleClass="texto-size-14 cinza"
                                                                                      value="#{evento.nomeEvento}"/>


                                                                    </td>
                                                                    <td><h:outputText styleClass="texto-size-14 cinza"
                                                                                      value="#{evento.valorApresentar}"/></td>

                                                                    <td>
                                                                        <h:commandLink id="voltarCE"
                                                                                       styleClass="linkAzul texto-size-14"
                                                                                       action="#{CadastroInicialControle.abrirDetalhamento}"
                                                                                       actionListener="#{CadastroInicialControle.selCadastroInicialListener}">
                                                                            <f:attribute name="codigoEventoInteresse" value="#{evento.codigoEvento}"/>
                                                                            <i class="fa-icon-search"></i>
                                                                        </h:commandLink>
                                                                    </td>
                                                                </tr>
                                                            </a4j:repeat>

                                                        </table>
                                                    </h:panelGroup>

                                                </c:if>

                                            </h:panelGroup>

                                        </h:panelGroup>

                                        <h:panelGroup
                                                style="width: 96%; height: auto;margin: 0 0 0 1.5%; display: none;min-width: 700px;"
                                                styleClass="painelDadosAluno blocoStudio blocosPrincipais tudo googleAnalytics">

                                            <c:if test="${LoginControle.apresentarLinkEstudio}">
                                                <div class="tituloPainelAluno">
                                                    <h:outputText value="Histórico de Agendamento" styleClass="texto-size-14 negrito cinzaEscuro pl20"/>

                                                    <a4j:commandLink oncomplete="#{rich:component('modalAgendaExcecao')}.show()"
                                                                     styleClass="linkAzul floatright texto-size-14"
                                                                     style="margin-right: 20px;"
                                                                     action="#{clienteEstudioControle.acaoListarAgendaExcecao}"
                                                                     reRender="mainPanelModalAgendaExcecao">
                                                        <i id="outListaIcon" class="fa-icon-ban-circle"></i>
                                                        <h:outputText id="outLista"
                                                                      style="#{fn:length(clienteEstudioControle.listaAgendaExcecao) > 0 ? 'color:red;' : ''}"
                                                                      value="#{fn:length(clienteEstudioControle.listaAgendaExcecao)} Falta#{fn:length(clienteEstudioControle.listaAgendaExcecao) > 1 ? 's' : ''} Justificada#{fn:length(clienteEstudioControle.listaAgendaExcecao) > 1 ? 's' : ''}"/>
                                                    </a4j:commandLink>
                                                    <a4j:commandLink action="#{clienteEstudioControle.fecharModalAgendaAluno}"
                                                                     styleClass="linkAzul floatright texto-size-14"
                                                                     style="margin-right: 20px;"
                                                                     oncomplete="abrirPopup('popup_panelgrid_estudio_cliente.jsp', 'HistoricoAgendamento', 920, 640);">
                                                        <i id="visualizarHistoricoStudioIcon" class="fa-icon-history"></i>
                                                        <h:outputText id="visualizarHistoricoStudio" value="Visualizar histórico"/>
                                                    </a4j:commandLink>
                                                </div>
                                            </c:if>

                                            <h:panelGroup rendered="#{LoginControle.apresentarLinkEstudio}" id="idblocoStudio" >
                                                <c:if test="${!TelaClienteControle.carregarEstudio}">


                                                    <h:outputText value="Vendidos"
                                                                  style="margin-top: 20px; display: block;"
                                                                  styleClass="texto-size-14 negrito cinzaEscuro pl20"/>
                                                    <h:dataTable id="clienteEstudioControle-listaItemVenda"
                                                                 rows="7"
                                                                 styleClass="tabelaDados semZebra"
                                                                 value="#{clienteEstudioControle.listaItemVenda}"
                                                                 var="item">

                                                        <h:column>
                                                            <f:facet name="header">
                                                                <h:outputText value="Id"/>
                                                            </f:facet>
                                                            <h:outputText value="#{item.vendaAvulsa}"/>
                                                        </h:column>
                                                        <h:column>
                                                            <f:facet name="header">
                                                                <h:outputText value="Data Venda"/>
                                                            </f:facet>
                                                            <h:outputText value="#{item.dataVenda}">
                                                                <f:convertDateTime pattern="dd/MM/yyyy HH:mm:ss"/>
                                                            </h:outputText>
                                                        </h:column>
                                                        <h:column>
                                                            <f:facet name="header">
                                                                <h:outputText value="Produto"/>
                                                            </f:facet>
                                                            <h:outputText value="#{item.produto.descricao}"/>
                                                        </h:column>
                                                        <h:column>
                                                            <f:facet name="header">
                                                                <h:outputText value="Quantidade"/>
                                                            </f:facet>
                                                            <h:outputText value="#{item.quantidade}"/>
                                                        </h:column>
                                                        <h:column>
                                                            <f:facet name="header">
                                                                <h:outputText value="Valor Parcial"/>
                                                            </f:facet>
                                                            <h:outputText value="#{item.valorParcial}">
                                                                <f:convertNumber maxFractionDigits="2" groupingUsed="true" maxIntegerDigits="14" type="currency"
                                                                                 currencySymbol="#{MovPagamentoControle.empresaLogado.moeda} "/>
                                                            </h:outputText>
                                                        </h:column>
                                                        <h:column>
                                                            <f:facet name="header">
                                                                <h:outputText value="Pacote"/>
                                                            </f:facet>
                                                            <h:outputText value="#{item.pacoteVO.titulo}">
                                                            </h:outputText>
                                                        </h:column>
                                                        <h:column>
                                                            <f:facet name="header">
                                                                <h:outputText value="Agend."/>
                                                            </f:facet>
                                                            <a4j:commandButton action="#{clienteEstudioControle.consultarAgendamentos}"
                                                                               image="imagens/botalVisualizarLog.png"
                                                                               oncomplete="abrirPopup('pages/estudio/includes/popup_dados_agendamento.jsp', 'Agendamentos', 905, 660);"
                                                                               reRender="modalAgenda"/>
                                                        </h:column>
                                                        <c:if  test="${clienteEstudioControle.listaItemVendaSize > 7}">
                                                            <f:facet name="footer">
                                                            <rich:datascroller for="clienteEstudioControle-listaItemVenda" pageIndexVar="indiceEstudioVendidos" pagesVar="pagesEstudioVendidos" style="background: white;" styleClass="paginatorRemoveHover">
                                                                <f:facet name="first">
                                                                    <h:outputText value="<<" />
                                                                </f:facet>
                                                                <f:facet name="first_disabled">
                                                                    <h:outputText value="<<" />
                                                                </f:facet>
                                                                <f:facet name="fastrewind">
                                                                    <h:outputText value="<" />
                                                                </f:facet>
                                                                <f:facet name="fastrewind_disabled">
                                                                    <h:outputText value="<" />
                                                                </f:facet>
                                                                <f:facet name="pages" >
                                                                    <h:outputText value="Página #{indiceEstudioVendidos}/#{pagesEstudioVendidos}"/>
                                                                </f:facet>
                                                                <f:facet name="fastforward">
                                                                    <h:outputText value=">" />
                                                                </f:facet>
                                                                <f:facet name="fastforward_disabled">
                                                                    <h:outputText value=">" />
                                                                </f:facet>
                                                                <f:facet name="last">
                                                                    <h:outputText value=">>" />
                                                                </f:facet>
                                                                <f:facet name="last_disabled">
                                                                    <h:outputText value=">>" />
                                                                </f:facet>
                                                            </rich:datascroller>
                                                        </f:facet>
                                                        </c:if>
                                                    </h:dataTable>

                                                    <h:outputText value="A agendar"
                                                                  style="margin-top: 20px; display: block;"
                                                                  styleClass="texto-size-14 negrito cinzaEscuro pl20"/>
                                                    <h:dataTable
                                                            id="clienteEstudioControle-listaAAgendar"
                                                            rows="#{clienteEstudioControle.nmrPaginaAgendar}"
                                                            styleClass="tabelaDados semZebra"
                                                            value="#{clienteEstudioControle.listaAAgendar}"
                                                            var="item">

                                                        <h:column>
                                                            <f:facet name="header">
                                                                <h:outputText value="Saldo"/>
                                                            </f:facet>
                                                            <h:outputText value="#{item.saldoProduto} #{item.saldoProduto == 1 ? ' sessão' :' sessões'}"/>
                                                        </h:column>

                                                        <h:column>
                                                            <f:facet name="header">
                                                                <h:outputText value="Produto"/>
                                                            </f:facet>
                                                            <h:outputText value="#{item.produtoVO.descricao}"/>
                                                        </h:column>

                                                        <h:column>
                                                            <f:facet name="header">
                                                                <h:outputText value="Vencimento"/>
                                                            </f:facet>
                                                            <h:outputText value="#{item.dataFimAgendar_Apresentar}"/>
                                                        </h:column>

                                                        <h:column>
                                                            <f:facet name="header">
                                                                <h:selectBooleanCheckbox
                                                                        id="selecionarTodosAgendar"
                                                                        value="#{clienteEstudioControle.selecionarTodosAgendar}">
                                                                    <a4j:support
                                                                            action="#{clienteEstudioControle.acaoSelecionarTodosAgendar}"
                                                                            event="onclick"
                                                                            reRender="clienteEstudioControle-listaAAgendar">
                                                                    </a4j:support>
                                                                </h:selectBooleanCheckbox>
                                                            </f:facet>
                                                            <h:selectBooleanCheckbox
                                                                    id="itemSolicitacao-selecionado-agendar"
                                                                    value="#{item.selecionarAgendar}">
                                                                <a4j:support
                                                                        status="statusHora"
                                                                        action="#{clienteEstudioControle.acaoSelecionarUmAgendar}"
                                                                        event="onclick"
                                                                        reRender="selecionarTodosAgendar">
                                                                </a4j:support>
                                                            </h:selectBooleanCheckbox>
                                                        </h:column>

                                                    </h:dataTable>

                                                    <a4j:commandLink title="Agendar Selecionados"
                                                                     style="margin-left:20px;"
                                                                     styleClass="pure-button pure-button-small"
                                                                     action="#{clienteEstudioControle.acaoDisponibilidade}">
                                                        <i id="agendaSelecionadosStudioIcon" class="fa-icon-calendar"></i>
                                                        <h:outputText value="Agendar selecionados" id="agendaSelecionadosStudio"/>
                                                    </a4j:commandLink>

                                                    <h:outputText value="A faturar"
                                                                  style="margin-top: 20px; display: block;"
                                                                  styleClass="texto-size-14 negrito cinzaEscuro pl20"/>

                                                    <h:dataTable
                                                            id="clienteEstudioControle-listaAFaturar"
                                                            styleClass="tabelaDados semZebra"
                                                            value="#{clienteEstudioControle.listaAFaturar}"
                                                            var="item">

                                                        <h:column>
                                                            <f:facet name="header">
                                                                <h:outputText value="Produto"/>
                                                            </f:facet>
                                                            <h:outputText value="#{item.produtoVO.descricao}"/>
                                                        </h:column>

                                                        <h:column>
                                                            <f:facet name="header">
                                                                <h:outputText value="Data Aula"/>
                                                            </f:facet>
                                                            <h:outputText value="#{item.dataAula}" converter="dataConverter"/>
                                                        </h:column>

                                                        <h:column>
                                                            <f:facet name="header">
                                                                <h:selectBooleanCheckbox
                                                                        id="selecionarTodosFaturar"
                                                                        value="#{clienteEstudioControle.selecionarTodosFaturar}">
                                                                    <a4j:support
                                                                            action="#{clienteEstudioControle.acaoSelecionarTodosFaturar}"
                                                                            event="onclick"
                                                                            reRender="clienteEstudioControle-listaAFaturar">
                                                                    </a4j:support>
                                                                </h:selectBooleanCheckbox>
                                                            </f:facet>
                                                            <h:selectBooleanCheckbox
                                                                    id="itemSolicitacao-selecionado-faturar"
                                                                    value="#{item.selecionarPagarFatura}">
                                                                <a4j:support
                                                                        action="#{clienteEstudioControle.acaoSelecionarUmFaturar}"
                                                                        event="onclick"
                                                                        reRender="selecionarTodosFaturar"/>
                                                            </h:selectBooleanCheckbox>
                                                        </h:column>

                                                    </h:dataTable>

                                                    <a4j:commandLink title="Pagar selecionados"
                                                                     style="margin-left:20px; margin-bottom: 20px;"
                                                                     styleClass="pure-button pure-button-small"
                                                                     action="#{clienteEstudioControle.acaoPagarFaturar}">
                                                        <i id="pagarSelecionadosStudioIcon" class="fa-icon-money"></i>
                                                        <h:outputText value="Pagar selecionados" id="pagarSelecionadosStudio"/>
                                                    </a4j:commandLink>
                                                </c:if>
                                            </h:panelGroup>



                                            <div class="rodapePainelAluno" style="text-align: right;">
                                                <c:if test="${fn:length(TelaClienteControle.listaHistoricoContato) eq 10}">
                                                    <a4j:commandLink reRender="idlistacontratos"
                                                                     styleClass="linkAzul texto-size-14"
                                                                     style="margin-right: 20px;"
                                                                     action="#{TelaClienteControle.verMaisContatos}">
                                                        <h:outputText value="Ver mais" />
                                                        <i class="fa-icon-angle-right"></i>

                                                    </a4j:commandLink>
                                                </c:if>
                                                <c:if test="${fn:length(TelaClienteControle.listaHistoricoContato) gt 10}">
                                                    <a4j:commandLink reRender="idlistacontratos"
                                                                     styleClass="linkAzul texto-size-14"
                                                                     style="margin-right: 20px;"
                                                                     action="#{TelaClienteControle.verMenosContatos}">
                                                        <h:outputText value="Ver menos" />
                                                        <i class="fa-icon-angle-left"></i>
                                                    </a4j:commandLink>
                                                </c:if>
                                            </div>
                                        </h:panelGroup>


                                        <div  style="width: 96%; height: auto;margin: 0 0 0 1.5%; display: none;min-width: 700px;"
                                              class="painelDadosAluno blocoFinanceiro blocosPrincipais tudo googleAnalytics">
                                            <h:panelGroup layout="block" id="idblocoFinanceiro">
                                                <c:if test="${!TelaClienteControle.carregarFinanceiro}">

                                                    <div class="tituloPainelAluno">
                                                        <div id="botoesTL" style="display: inline-flex; padding: 10px;">
                                                            <a4j:commandLink status="false" id="idbtnCompras"
                                                                             styleClass="botaoModoTimeLine ativo blocoCompras botaoBlocoFinanceiro"
                                                                             onclick="trocarBloco('.blocoCompras', '.containerCompras', '.botaoBlocoFinanceiro');">
                                                                <h:outputText value="Compras"/>
                                                            </a4j:commandLink>
                                                            <a4j:commandLink styleClass="botaoModoTimeLine blocoCobranca botaoBlocoFinanceiro"
                                                                             status="false" id="idbtnCobrancas"
                                                                             onclick="trocarBloco('.blocoCobranca', '.containerCompras', '.botaoBlocoFinanceiro');">
                                                                <h:outputText value="Cobranças"/>
                                                            </a4j:commandLink>
                                                            <a4j:commandLink status="false" id="idbtnPVencimento"
                                                                             styleClass="botaoModoTimeLine blocoProdutosVencimento botaoBlocoFinanceiro"
                                                                             onclick="trocarBloco('.blocoProdutosVencimento', '.containerCompras', '.botaoBlocoFinanceiro');">
                                                                <h:outputText value="Produtos com vencimento"/>
                                                            </a4j:commandLink>
                                                        </div>
                                                    </div>

                                                    <%@include file="includes/include_extratoaluno.jsp"%>

                                                    <h:panelGroup id="idListaCompras"
                                                                  layout="block" styleClass="containerCompras blocoCompras visivel">

                                                        <h:outputText value="Histórico de compras" rendered="#{not TelaClienteControle.integraProtheus}"
                                                                      style="margin-top: 20px; display: block;"
                                                                      styleClass="texto-size-14 negrito cinzaEscuro pl20"/>

                                                        <h:outputText value="Nenhum produto."
                                                                      style="margin-top: 20px; display: block;"
                                                                      styleClass="texto-size-14 cinza pl20"
                                                                      rendered="#{TelaClienteControle.listaCompras.count <= 0 and not TelaClienteControle.integraProtheus}"/>

                                                        <h:panelGroup layout="block" id="tabelaMovProdutos" rendered="#{not TelaClienteControle.integraProtheus}">
                                                            <h:dataTable styleClass="tabelaDados semZebra"
                                                                         id="listaHistoricoProdutoFinanceior"
                                                                         value="#{TelaClienteControle.listaHistoricoProduto}"
                                                                         rendered="#{TelaClienteControle.listaCompras.count > 0 and not TelaClienteControle.integraProtheus}"
                                                                         var="historicoCompras">
                                                                <h:column>
                                                                    <f:facet name="header">
                                                                        <h:outputText value="Código" />
                                                                    </f:facet>
                                                                    <h:outputText id="codigo" value="#{historicoCompras.codigo}" />
                                                                </h:column>
                                                                <h:column>
                                                                    <f:facet name="header">
                                                                        <h:outputText value="#{msg_aplic.prt_HistoricoComprasCliente_contrato}" />
                                                                    </f:facet>
                                                                    <h:outputText id="numeroContrato" value="#{historicoCompras.contrato.codigo}" />
                                                                </h:column>
                                                                <h:column rendered="#{TelaClienteControle.apresentarEmpresa}">
                                                                    <f:facet name="header">
                                                                        <h:outputText value="Empresa" />
                                                                    </f:facet>
                                                                    <h:outputText value="#{historicoCompras.empresa_Apresentar}" />
                                                                </h:column>
                                                                <h:column>
                                                                    <f:facet name="header">
                                                                        <h:outputText value="#{msg_aplic.prt_HistoricoComprasCliente_descricao}" />
                                                                    </f:facet>
                                                                    <h:outputText id="descricao" value="#{historicoCompras.descricao}" />
                                                                </h:column>

                                                                <h:column>
                                                                    <f:facet name="header">
                                                                        <h:outputText value="#{msg_aplic.prt_HistoricoComprasCliente_dataLancamento}" />
                                                                    </f:facet>
                                                                    <h:outputText id="dataLancamento" value="#{historicoCompras.dataLancamentoComHora_Apresentar}" />
                                                                </h:column>

                                                                <rich:column styleClass="col-text-align-right" headerClass="col-text-align-right">
                                                                    <f:facet name="header">
                                                                        <h:outputText value="#{msg_aplic.prt_HistoricoComprasCliente_quantidade}" />
                                                                    </f:facet>
                                                                    <h:outputText id="quantidade" value="#{historicoCompras.quantidade}"
                                                                                  style="float: right;margin-right:5px;"/>
                                                                </rich:column>
                                                                <rich:column styleClass="col-text-align-right" headerClass="col-text-align-right">
                                                                    <f:facet name="header">
                                                                        <h:outputText value="#{msg_aplic.prt_HistoricoComprasCliente_unitario}" />
                                                                    </f:facet>
                                                                    <h:outputText id="valorUnitario" value="#{historicoCompras.precoUnitario}"
                                                                                  style="float: right;margin-right:5px;">
                                                                        <f:converter converterId="FormatadorNumerico" />
                                                                    </h:outputText>
                                                                </rich:column>
                                                                <rich:column styleClass="col-text-align-right" headerClass="col-text-align-right">

                                                                    <f:facet name="header">
                                                                        <h:outputText value="#{msg_aplic.prt_HistoricoComprasCliente_desconto}" />
                                                                    </f:facet>
                                                                    <h:outputText id="desconto" value="#{historicoCompras.valorDesconto}" style="float: right;margin-right:5px;">
                                                                        <f:converter converterId="FormatadorNumerico" />
                                                                    </h:outputText>
                                                                </rich:column>
                                                                <rich:column styleClass="col-text-align-right" headerClass="col-text-align-right">
                                                                    <f:facet name="header">
                                                                        <h:outputText value="#{msg_aplic.prt_HistoricoComprasCliente_totalFinal}" />
                                                                    </f:facet>
                                                                    <h:outputText id="valorTotal" value="#{historicoCompras.totalFinal}" style="float: right;margin-right:5px;">
                                                                        <f:converter converterId="FormatadorNumerico" />
                                                                    </h:outputText>
                                                                </rich:column>

                                                                <rich:column styleClass="col-text-align-center" headerClass="col-text-align-center" rendered="#{TelaClienteControle.colunaCupomDesconto}">
                                                                    <f:facet name="header">
                                                                        <h:outputText value="Cupom Desconto"/>
                                                                    </f:facet>
                                                                    <h:outputText styleClass="col-text-align-center" value="#{historicoCompras.numeroCupomDesconto}">
                                                                    </h:outputText>
                                                                </rich:column>

                                                                <h:column>
                                                                    <f:facet name="header">
                                                                        <h:outputText value="#{msg_aplic.prt_HistoricoComprasCliente_situacao}" />
                                                                    </f:facet>
                                                                    <h:outputText id="situacao" value="#{historicoCompras.situacao_Apresentar}"
                                                                                  styleClass="tooltipster #{historicoCompras.mudarCorSituacaoEmAberto}"
                                                                                  title="#{historicoCompras.situacao eq 'EA' ? 'Aguardando Pagamento' : historicoCompras.situacao eq 'CA' ? historicoCompras.dataCancelamento_Hint : historicoCompras.dataPagamento_Hint}" />
                                                                </h:column>
                                                                <h:column
                                                                        rendered="#{TelaClienteControle.apresentarValorParcialmentePago}">
                                                                    <f:facet name="header">
                                                                        <h:outputText value="Parc. Pago" />
                                                                    </f:facet>
                                                                    <h:outputText value="#{historicoCompras.valorParcialmentePagoApresentar}"/>
                                                                </h:column>

                                                                <h:column rendered="#{TelaClienteControle.integraProtheus}" >
                                                                    <f:facet name="header">
                                                                        <h:outputText value="Protheus" />
                                                                    </f:facet>

                                                                    <div style="text-align: center">
                                                                        <a4j:commandLink action="#{TelaClienteControle.obterStatusProtheus}"
                                                                                         reRender="statusProtheus"
                                                                                         rendered="#{not empty historicoCompras.statusProtheus and historicoCompras.situacao eq 'PG'}"
                                                                                         oncomplete="#{TelaClienteControle.msgAlert}"
                                                                                         style="display: inline; font-size: 14px">
                                                                            <h:outputText rendered="#{historicoCompras.statusProtheus eq 'SUCESSO'}"
                                                                                          styleClass="fa-icon-ok-sign" style="color: #3cdb5c;"/>

                                                                            <h:outputText rendered="#{historicoCompras.statusProtheus eq 'ERRO'}"
                                                                                          styleClass="fa-icon-ban-circle" style="color: #db394f;"/>

                                                                            <h:outputText rendered="#{historicoCompras.statusProtheus eq 'AGUARDANDO'}"
                                                                                          styleClass="fa-icon-time" style="color: #6e6ddb;"/>
                                                                        </a4j:commandLink>

                                                                        <h:outputLink rendered="#{not empty historicoCompras.linkNota and historicoCompras.situacao eq 'PG'}"
                                                                                      target="_blank" style="margin-left: 10px; font-size: 14px; color: #29abe2; display: inline;"
                                                                                      value="#{historicoCompras.linkNota}">Nota</h:outputLink>
                                                                    </div>
                                                                </h:column>

                                                                <h:column rendered="#{TelaClienteControle.apresentarOpcaoEstornoProduto || TelaClienteControle.apresentarOpcaoCancelarSessao}">
                                                                    <f:facet name="header">
                                                                        <h:outputText style="font-weight: bold" value="Opções"/>
                                                                    </f:facet>
                                                                    <c:if test="${not param.readOnly}">
                                                                        <a4j:commandLink id="estornoMovProduto"
                                                                                         rendered="#{historicoCompras.aprensetarBotaoEstorno}"
                                                                                         styleClass="linkAzul texto-size-14"
                                                                                         title="Estornar produto"
                                                                                         action="#{EstornoMovProdutoControle.novo}"
                                                                                         value="Estornar"
                                                                                         oncomplete="abrirPopup('estornoMovProdutoForm.jsp', 'Produto', 950, 600);"/>
                                                                        <rich:spacer width="5px"/>
                                                                        <a4j:commandLink id="cancelarSessao"
                                                                                         rendered="#{historicoCompras.apresentarBotaoCancelarSessao}"
                                                                                         styleClass="linkAzul texto-size-14"
                                                                                         action="#{CancelamentoSessaoControle.novo}"
                                                                                         value="Cancelar"
                                                                                         oncomplete="abrirPopup('cancelamentoSessaoForm.jsp', 'Produto', 800, 680);"/>

                                                                    </c:if>
                                                                    <rich:spacer width="5px"/>
                                                                    <a4j:commandLink id="btnImprimirReciboProdutoCan"
                                                                                     action="#{ClienteControle.imprimirReciboDevolucaoMovProduto}"
                                                                                     title="Imprimir Recibo Devolução"
                                                                                     rendered="#{historicoCompras.possuiReciboDevolucao}"
                                                                                     styleClass="linkAzul texto-size-14 tooltipster"
                                                                                     oncomplete="abrirPopupPDFImpressao('relatorio/#{ClienteControle.nomeArquivoReciboDevolucao}','', 780, 595);"/>
                                                                    <a4j:commandLink rendered="#{historicoCompras.apresentarBotaoImprimirContrato}"
                                                                                     id="btnContratoProduto"
                                                                                     title="Imprimir Contrato de  Prestação de Serviço"
                                                                                     value="Contrato" styleClass="linkAzul texto-size-14 tooltipster"
                                                                                     action="#{ClienteControle.prepararVendaAvulsa}"
                                                                                     reRender="form:panelMensagemSuperior,form:panelMensagemInferior,panelIncludeContratoPrestacao"
                                                                                     oncomplete="#{ClienteControle.mostrarRichModalPanelContratoPrestacaoServico}"/>
                                                                </h:column>
                                                            </h:dataTable>
                                                            <h:panelGrid columns="1" width="100%" columnClasses="colunaCentralizada" rendered="#{TelaClienteControle.listaCompras.count > 0 and not TelaClienteControle.integraProtheus}">
                                                                <table width="100%" border="0" cellspacing="0" cellpadding="0">
                                                                    <tr>
                                                                        <td align="center" valign="middle">
                                                                            <h:panelGroup layout="block"
                                                                                          styleClass="paginador-container">
                                                                                <h:panelGroup styleClass="pull-left"
                                                                                              layout="block">
                                                                                    <h:outputText
                                                                                            styleClass="texto-size-14 cinza"
                                                                                            value="Total #{TelaClienteControle.listaCompras.count} itens"></h:outputText>
                                                                                </h:panelGroup>
                                                                                <h:panelGroup layout="block"
                                                                                              style="align-items: center">
                                                                                    <a4j:commandLink
                                                                                            styleClass="linkPadrao texto-cor-azul texto-size-20-real"
                                                                                            reRender="tabelaMovProdutos"
                                                                                            actionListener="#{TelaClienteControle.primeiraPagina}">
                                                                                        <i class="fa-icon-double-angle-left" id="primPaginaProduto"></i>
                                                                                        <f:attribute name="tipo"
                                                                                                     value="LISTA_COMPRAS"/>
                                                                                    </a4j:commandLink>
                                                                                    <a4j:commandLink
                                                                                            styleClass="linkPadrao texto-cor-azul texto-size-20-real"
                                                                                            reRender="tabelaMovProdutos"
                                                                                            actionListener="#{TelaClienteControle.paginaAnterior}">
                                                                                        <i class="fa-icon-angle-left" id="pagAntProduto"></i>
                                                                                        <f:attribute name="tipo"
                                                                                                     value="LISTA_COMPRAS"/>
                                                                                    </a4j:commandLink>
                                                                                    <h:outputText
                                                                                            styleClass="texto-font texto-cor-cinza texto-size-16-real"
                                                                                            value="#{msg_aplic.prt_msg_pagina} #{TelaClienteControle.listaCompras.paginaAtualApresentar}"
                                                                                            rendered="true"/>
                                                                                    <a4j:commandLink
                                                                                            styleClass="linkPadrao texto-font texto-cor-azul texto-size-20-real"
                                                                                            reRender="tabelaMovProdutos"
                                                                                            actionListener="#{TelaClienteControle.proximaPagina}">
                                                                                        <i class="fa-icon-angle-right" id="proxPagProduto"></i>
                                                                                        <f:attribute name="tipo"
                                                                                                     value="LISTA_COMPRAS"/>
                                                                                    </a4j:commandLink>
                                                                                    <a4j:commandLink
                                                                                            styleClass="linkPadrao texto-cor-azul texto-size-20-real"
                                                                                            reRender="tabelaMovProdutos"
                                                                                            actionListener="#{TelaClienteControle.ultimaPagina}">
                                                                                        <i class="fa-icon-double-angle-right" id="ultimaPaginaProd"></i>
                                                                                        <f:attribute name="tipo"
                                                                                                     value="LISTA_COMPRAS"/>
                                                                                    </a4j:commandLink>
                                                                                </h:panelGroup>
                                                                                <h:panelGroup layout="block" styleClass="paginador-itens-pagina">
                                                                                    <h:panelGroup styleClass="pull-right" layout="block">
                                                                                        <h:outputText
                                                                                                styleClass="texto-size-14 cinza "
                                                                                                value="Itens por página "/>
                                                                                    </h:panelGroup>
                                                                                    <h:panelGroup styleClass="cb-container pl20" layout="block">
                                                                                        <h:selectOneMenu value="#{TelaClienteControle.listaCompras.limit}" id="qtdeItensPaginaProd">
                                                                                            <f:selectItem itemValue="#{6}"/>
                                                                                            <f:selectItem itemValue="#{10}"/>
                                                                                            <f:selectItem itemValue="#{20}"/>
                                                                                            <f:selectItem itemValue="#{50}"/>
                                                                                            <f:selectItem itemValue="#{100}"/>
                                                                                            <a4j:support event="onchange" actionListener="#{TelaClienteControle.atualizarNumeroItensPagina}" reRender="tabelaMovProdutos">
                                                                                                <f:attribute name="tipo" value="LISTA_COMPRAS"/>
                                                                                            </a4j:support>
                                                                                        </h:selectOneMenu>
                                                                                    </h:panelGroup>

                                                                                </h:panelGroup>
                                                                            </h:panelGroup>
                                                                        </td>
                                                                    </tr>
                                                                </table>
                                                            </h:panelGrid>
                                                        </h:panelGroup>
                                                        <h:outputText value="Histórico de parcelas geradas"  rendered="#{not TelaClienteControle.integraProtheus}"
                                                                      style="margin-top: 20px; display: block;"
                                                                      styleClass="texto-size-14 negrito cinzaEscuro pl20"/>
                                                        <h:outputText value="Nenhuma parcela."
                                                                      style="margin-top: 20px; display: block;"
                                                                      styleClass="texto-size-14 cinza pl20"
                                                                      rendered="#{TelaClienteControle.listaParcelas.count <= 0 and not TelaClienteControle.integraProtheus}"/>
                                                        <h:panelGroup layout="block" id="tabelaMovParcelas" rendered="#{not TelaClienteControle.integraProtheus}">
                                                            <rich:dataTable styleClass="tabelaDados semZebra"
                                                                            id="listaHistoricoParcela"
                                                                            value="#{TelaClienteControle.listaHistoricoParcelas}"
                                                                            rendered="#{TelaClienteControle.listaParcelas.count > 0}"
                                                                            var="historicoParcela">

                                                                <rich:column>
                                                                    <f:facet name="header">
                                                                        <h:outputText value="#{msg_aplic.prt_HistoricoParcelaCliente_codigo}" />
                                                                    </f:facet>
                                                                    <h:outputText id="listaHistoricoParcelaCodigo" value="#{historicoParcela.contrato.codigo}" />
                                                                </rich:column>

                                                                <rich:column>
                                                                    <f:facet name="header">
                                                                        <h:outputText value="Código" />
                                                                    </f:facet>
                                                                    <h:outputText value="#{historicoParcela.codigo}" />
                                                                </rich:column>

                                                                <rich:column rendered="#{TelaClienteControle.apresentarEmpresa}">
                                                                    <f:facet name="header">
                                                                        <h:outputText value="Empresa" />
                                                                    </f:facet>
                                                                    <h:outputText id="listaHistoricoParcelaEmpresa" value="#{historicoParcela.empresa_Apresentar}" />
                                                                </rich:column>

                                                                <rich:column>
                                                                    <f:facet name="header">
                                                                        <h:outputText value="#{msg_aplic.prt_HistoricoParcelaCliente_descricao}" />
                                                                    </f:facet>
                                                                    <h:outputText id="listaHistoricoParcelaDescricao"
                                                                                  styleClass="#{not empty historicoParcela.toolTipsterDescricaoParcela ? 'tooltipster' : ''}"
                                                                                  title="#{historicoParcela.toolTipsterDescricaoParcela}"
                                                                                  value="#{historicoParcela.descricao}" />
                                                                    <h:outputText rendered="#{LoginControle.usuarioLogado.usuarioPactoSolucoes and !historicoParcela.existeMovProdutoParcela and historicoParcela.situacao eq 'EA'}"
                                                                                  title="Parcela não tem \"MovProdutoParcela\".<br/>Devido a isso a parcela não será cobrada automaticamente.<br/>Informar a Squad AD"
                                                                                  style="padding-left: 5px; padding-right: 5px;"
                                                                                  styleClass="tooltipster red"
                                                                                  value="Obs."/>
                                                                    <h:outputText rendered="#{not empty historicoParcela.cupomDesconto}" value=" - CUPOM #{historicoParcela.cupomDesconto}"/>
                                                                </rich:column>

                                                                <rich:column>
                                                                    <f:facet name="header">
                                                                        <h:outputText value="#{msg_aplic.prt_HistoricoParcelaCliente_DataLancada}" />
                                                                    </f:facet>
                                                                    <h:outputText id="listaHistoricoParcelaRegistro" value="#{historicoParcela.dataRegistro_Apresentar}">

                                                                    </h:outputText>
                                                                </rich:column>
                                                                <rich:column>
                                                                    <f:facet name="header">
                                                                        <a4j:commandLink reRender="tabelaMovParcelas"
                                                                                         actionListener="#{TelaClienteControle.ordenarLista}"
                                                                                         style="padding: 7px 0;font-size: 1em; color: #777; font-weight: bold; border: 0; text-transform: uppercase; background-color: white;">
                                                                            <f:attribute name="tipo"
                                                                                         value="LISTA_PARCELAS"/>
                                                                            <f:attribute name="orderBy"
                                                                                         value="dataVencimento"/>
                                                                            <h:outputText
                                                                                    value="#{msg_aplic.prt_HistoricoParcelaCliente_DataVencimento}"/>
                                                                            <i class="${TelaClienteControle.listaParcelas.icon}" style="transform: rotate(90deg);"></i>
                                                                        </a4j:commandLink>
                                                                    </f:facet>
                                                                    <h:outputText id="listaHistoricoParcelaVencimento" value="#{historicoParcela.dataVencimento_Apresentar}"/>
                                                                </rich:column>
                                                                <rich:column>
                                                                    <f:facet name="header">
                                                                        <h:outputText value="#{msg_aplic.prt_HistoricoParcelaCliente_valor}" />
                                                                    </f:facet>
                                                                    <h:outputText id="listaHistoricoParcelaParcela" value="#{historicoParcela.valorParcela}"
                                                                                  style="float: right;margin-right:5px;">
                                                                        <f:converter converterId="FormatadorNumerico" />
                                                                    </h:outputText>
                                                                </rich:column>
                                                                <rich:column>
                                                                    <f:facet name="header">
                                                                        <h:outputText value="#{msg_aplic.prt_HistoricoParcelaCliente_situacao}" />
                                                                    </f:facet>
                                                                    <h:outputText id="listaHistoricoParcelaSituacao"
                                                                                  value="#{historicoParcela.situacao_Apresentar}"
                                                                                  styleClass="tooltipster #{historicoParcela.mudarCorSituacaoEmAberto}"
                                                                                  title="#{historicoParcela.situacao eq 'EA' ? 'Aguardando Pagamento' : historicoParcela.situacao eq 'CA' ? historicoParcela.dataCancelamento_Hint : historicoParcela.dataPagamento_Hint}"/>
                                                                </rich:column>
                                                            </rich:dataTable>
                                                            <h:panelGrid columns="1" width="100%" rendered="#{TelaClienteControle.listaParcelas.count > 0}" columnClasses="colunaCentralizada">
                                                                <table width="100%" border="0" cellspacing="0" cellpadding="0">
                                                                    <tr>
                                                                        <td align="center" valign="middle">
                                                                            <h:panelGroup layout="block"
                                                                                          styleClass="paginador-container">
                                                                                <h:panelGroup styleClass="pull-left"
                                                                                              layout="block">
                                                                                    <h:outputText
                                                                                            styleClass="texto-size-14 cinza"
                                                                                            value="Total #{TelaClienteControle.listaParcelas.count} itens"></h:outputText>
                                                                                </h:panelGroup>
                                                                                <h:panelGroup layout="block"
                                                                                              style="align-items: center">
                                                                                    <a4j:commandLink styleClass="linkPadrao texto-cor-azul texto-size-20-real"  reRender="tabelaMovParcelas"
                                                                                                     actionListener="#{TelaClienteControle.primeiraPagina}">
                                                                                        <i class="fa-icon-double-angle-left" id="primPagParcelas"></i>
                                                                                        <f:attribute name="tipo" value="LISTA_PARCELAS" />
                                                                                    </a4j:commandLink>

                                                                                    <a4j:commandLink styleClass="linkPadrao texto-cor-azul texto-size-20-real" reRender="tabelaMovParcelas"
                                                                                                     actionListener="#{TelaClienteControle.paginaAnterior}">
                                                                                        <i class="fa-icon-angle-left" id="pagAntParcelas"></i>
                                                                                        <f:attribute name="tipo" value="LISTA_PARCELAS" />
                                                                                    </a4j:commandLink>

                                                                                    <h:outputText styleClass="texto-font texto-cor-cinza texto-size-16-real"
                                                                                                  value="#{msg_aplic.prt_msg_pagina} #{TelaClienteControle.listaParcelas.paginaAtualApresentar}" rendered="true"/>
                                                                                    <a4j:commandLink styleClass="linkPadrao texto-font texto-cor-azul texto-size-20-real" reRender="tabelaMovParcelas"
                                                                                                     actionListener="#{TelaClienteControle.proximaPagina}">
                                                                                        <i class="fa-icon-angle-right" id="proxPagParcela"></i>
                                                                                        <f:attribute name="tipo" value="LISTA_PARCELAS" />
                                                                                    </a4j:commandLink>

                                                                                    <a4j:commandLink styleClass="linkPadrao texto-cor-azul texto-size-20-real" reRender="tabelaMovParcelas"
                                                                                                     actionListener="#{TelaClienteControle.ultimaPagina}">
                                                                                        <i class="fa-icon-double-angle-right" id="ultPagParcelas"></i>
                                                                                        <f:attribute name="tipo" value="LISTA_PARCELAS" />
                                                                                    </a4j:commandLink>
                                                                                </h:panelGroup>
                                                                                <h:panelGroup layout="block" styleClass="paginador-itens-pagina">
                                                                                    <h:panelGroup styleClass="pull-right" layout="block">
                                                                                        <h:outputText
                                                                                                styleClass="texto-size-14 cinza "
                                                                                                value="Itens por página "></h:outputText>
                                                                                    </h:panelGroup>
                                                                                    <h:panelGroup styleClass="cb-container pl20" layout="block">
                                                                                        <h:selectOneMenu value="#{TelaClienteControle.listaParcelas.limit}" id="qtdeItensPaginaParc">
                                                                                            <f:selectItem itemValue="#{6}"></f:selectItem>
                                                                                            <f:selectItem itemValue="#{10}"></f:selectItem>
                                                                                            <f:selectItem itemValue="#{20}"></f:selectItem>
                                                                                            <f:selectItem itemValue="#{50}"></f:selectItem>
                                                                                            <f:selectItem itemValue="#{100}"></f:selectItem>
                                                                                            <a4j:support event="onchange" actionListener="#{TelaClienteControle.atualizarNumeroItensPagina}" reRender="tabelaMovParcelas">
                                                                                                <f:attribute name="tipo" value="LISTA_PARCELAS" />
                                                                                            </a4j:support>
                                                                                        </h:selectOneMenu>
                                                                                    </h:panelGroup>

                                                                                </h:panelGroup>
                                                                            </h:panelGroup>
                                                                        </td>
                                                                    </tr>
                                                                </table>
                                                            </h:panelGrid>
                                                        </h:panelGroup>


                                                        <h:outputText value="Histórico de pagamentos efetuados"
                                                                      style="margin-top: 20px; display: block;"
                                                                      styleClass="texto-size-14 negrito cinzaEscuro pl20"/>
                                                        <h:outputText value="Nenhum pagamento."
                                                                      style="margin-top: 20px; display: block; margin-bottom: 20px;"
                                                                      styleClass="texto-size-14 cinza pl20"
                                                                      rendered="#{TelaClienteControle.listaPagamentos.count <= 0}"/>
                                                        <h:panelGroup layout="block" id="tabelaMovPagamentos">
                                                            <h:dataTable styleClass="tabelaDados semZebra"
                                                                         id="listaHistoricoPagamentos"
                                                                         rendered="#{TelaClienteControle.listaPagamentos.count > 0}"
                                                                         value="#{TelaClienteControle.listaHistoricoPagamentos}"
                                                                         var="historicoPagamentos">
                                                                <h:column>
                                                                    <f:facet name="header">
                                                                        <h:outputText style="font-weight: bold"
                                                                                      value="#{msg_aplic.prt_HistoricoComprasCliente_codigo}" />
                                                                    </f:facet>
                                                                    <h:outputText id="codigo" value="#{historicoPagamentos.codigo}" />
                                                                </h:column>
                                                                <h:column>
                                                                    <f:facet name="header">
                                                                        <h:outputText style="font-weight: bold"
                                                                                      value="#{msg_aplic.prt_HistoricoComprasCliente_nrRecibo}" />
                                                                    </f:facet>
                                                                    <h:outputText id="recibo" value="#{historicoPagamentos.reciboPagamento.codigo}" />
                                                                </h:column>

                                                                <h:column rendered="#{TelaClienteControle.apresentarEmpresa}">
                                                                    <f:facet name="header">
                                                                        <h:outputText value="Empresa" />
                                                                    </f:facet>
                                                                    <h:outputText id="empresa" value="#{historicoPagamentos.empresa_Apresentar}" />
                                                                </h:column>

                                                                <h:column>
                                                                    <f:facet name="header">
                                                                        <h:outputText value="#{msg_aplic.prt_HistoricoComprasCliente_nomePagador}" />
                                                                    </f:facet>
                                                                    <h:outputText id="nomePagador" value="#{historicoPagamentos.nomePagador}" />
                                                                </h:column>
                                                                <h:column>
                                                                    <f:facet name="header">
                                                                        <h:outputText value="#{msg_aplic.prt_HistoricoComprasCliente_dataLancamento}" />
                                                                    </f:facet>
                                                                    <h:outputText id="dataLancamento" value="#{historicoPagamentos.dataLancamento_Apresentar}">
                                                                    </h:outputText>
                                                                </h:column>
                                                                <h:column>
                                                                    <f:facet name="header">
                                                                        <h:outputText value="#{msg_aplic.prt_HistoricoComprasCliente_formaPagamento}" />
                                                                    </f:facet>
                                                                    <h:outputText id="tipoFormaPagamento" value="#{historicoPagamentos.formaPagamento.tipoFormaPagamento_Apresentar} #{historicoPagamentos.creditoApresentar}" />
                                                                </h:column>
                                                                <h:column>
                                                                    <f:facet name="header">
                                                                        <h:outputText value="#{msg_aplic.prt_HistoricoComprasCliente_valor}" />
                                                                    </f:facet>
                                                                    <h:outputText id="valorTotal" value="#{historicoPagamentos.valorTotal}"
                                                                                  style="float: right;margin-right:5px;">
                                                                        <f:converter converterId="FormatadorNumerico" />
                                                                    </h:outputText>
                                                                </h:column>

                                                                <h:column>
                                                                    <f:facet name="header">
                                                                        <h:outputText value="#{msg_aplic.prt_HistoricoComprasCliente_recibo}" style="margin-left: 10px;"/>
                                                                    </f:facet>
                                                                    <a4j:commandLink id="imprimir" styleClass="linkAzul icon"
                                                                                     title="Imprimir recibo"
                                                                                     style="margin-left: 10px;"
                                                                                     actionListener="#{ReciboControle.prepareRecibo}"
                                                                                     action="#{ReciboControle.imprimirReciboPDF}"
                                                                                     oncomplete="abrirPopupPDFImpressao('relatorio/#{ReciboControle.nomeArquivoRelatorioGeradoAgora}','', 780, 595);">
                                                                        <f:attribute name="reciboPagamentoVO" value="#{historicoPagamentos.reciboPagamento}" />
                                                                        <i id="imprimirIcon" class="fa-icon-print texto-size-18 linkAzul"></i>
                                                                    </a4j:commandLink>
                                                                    &nbsp;
                                                                        <a4j:commandLink id="enviarRecibo" styleClass="linkAzul icon"
                                                                                         reRender="panelMensagemSuperior, panelMensagemInferior, panelConfirmacao, modalEnviarContratoEmail"
                                                                                         title="Enviar recibo"
                                                                                         oncomplete="#{ReciboControle.mensagemNotificar}#{ReciboControle.msgAlert}"
                                                                                         actionListener="#{ReciboControle.prepararModalEnvioReciboPorEmail}">
                                                                            <f:attribute name="reciboPagamentoVO" value="#{historicoPagamentos.reciboPagamento}"/>
                                                                            <f:attribute name="pessoaVO" value="#{TelaClienteControle.cliente.pessoa}"/>
                                                                            <i id="enviarReciboIcon" class="fa-icon-paper-plane texto-size-18 linkAzul"></i>
                                                                        </a4j:commandLink>
                                                                        &nbsp;
                                                                        <c:if test="${not param.readOnly}">
                                                                            <a4j:commandLink id="editarRecibo" styleClass="linkAzul icon"
                                                                                             actionListener="#{EdicaoPagamentoControle.prepareRecibo}"
                                                                                             title="Editar Recibo"
                                                                                             reRender="panelMensagemSuperior, panelMensagemInferior, panelConfirmacao,formAutorizarEdicaoPagamento"
                                                                                             oncomplete="#{EdicaoPagamentoControle.msgAlert}#{EdicaoPagamentoControle.mensagemNotificar}">
                                                                                <f:attribute name="pagamentoVO" value="#{historicoPagamentos}"/>
                                                                                <f:attribute name="tipoEdicao"
                                                                                             value="#{historicoPagamentos.formaPagamento.tipoFormaPagamento}"/>
                                                                                <i id="editarReciboIcon" class="fa-icon-edit texto-size-18 linkAzul"></i>
                                                                            </a4j:commandLink>
                                                                            &nbsp;
                                                                        </c:if>
                                                                        <c:if test="${!param.readOnly}">
                                                                        <a4j:commandLink id="visualizarRecibo"
                                                                                         styleClass="linkAzul icon"
                                                                                         title="Visualizar Recibo"
                                                                                         actionListener="#{EstornoReciboControle.preparaRecibo}"
                                                                                         action="#{EstornoReciboControle.preencherRecibo}"
                                                                                         oncomplete="abrirPopup('estornoReciboForm.jsp', 'EstornoRecibo', 1000, 650);">
                                                                            <f:attribute name="reciboPagamentoVO" value="#{historicoPagamentos.reciboPagamento}"/>
                                                                            <i id="visualizarReciboIcon" class="fa-icon-search texto-size-18 linkAzul"></i>
                                                                        </a4j:commandLink>
                                                                    </c:if>
                                                                    &nbsp;
                                                                    <c:if test="${!param.readOnly}">
                                                                        <a4j:commandLink id="gerarNotaFiscal2" reRender="panelAutorizacaoFuncionalidade"
                                                                                         value="NFSe"
                                                                                         styleClass="linkAzul icon"
                                                                                         onclick="#{historicoPagamentos.reciboPagamento.onclickNFSe}"
                                                                                         actionListener="#{ReciboControle.prepareRecibo}"
                                                                                         action="#{ReciboControle.emitirNFSe}"
                                                                                         rendered="#{ClienteControle.clienteVO.empresa.apresentarBotoesFaturamentoNFSe && LoginControle.permissaoAcessoMenuVO.apresentarNfseRecibo}"
                                                                                         oncomplete="#{ReciboControle.onComplete}">
                                                                            <f:attribute name="reciboPagamentoVO" value="#{historicoPagamentos.reciboPagamento}"/>
                                                                            <f:attribute name="maisDetalhes" value="false"/>
                                                                        </a4j:commandLink>
                                                                        &nbsp;
                                                                        <a4j:commandLink id="gerarNFCe"
                                                                                         value="NFC-e"
                                                                                         reRender="mdlMensagemGenerica"
                                                                                         style="text-transform: none;"
                                                                                         styleClass="linkAzul icon"
                                                                                         rendered="#{ClienteControle.clienteVO.empresa.usarNFCe && LoginControle.permissaoAcessoMenuVO.gestaoNFCe}"
                                                                                         actionListener="#{ReciboControle.prepareRecibo}"
                                                                                         action="#{ReciboControle.confirmarEmitirNFCe}"
                                                                                         oncomplete="#{ReciboControle.modalMensagemGenerica}">
                                                                            <f:attribute name="reciboPagamentoVO" value="#{historicoPagamentos.reciboPagamento}"/>
                                                                            <f:attribute name="maisDetalhes" value="false"/>
                                                                        </a4j:commandLink>
                                                                    </c:if>
                                                                </h:column>

                                                                <rich:column style="text-align: center"
                                                                        rendered="#{TelaClienteControle.apresentarStatusConciliadora}">
                                                                    <f:facet name="header">
                                                                        <h:outputText value="STATUS"
                                                                                      title="STATUS DA CONCILIADORA"
                                                                                      styleClass="tooltipster"
                                                                                      style="width: 100%; text-align: center; display: block"/>
                                                                    </f:facet>

                                                                    <h:panelGroup layout="block" style="text-align: center;" rendered="#{!historicoPagamentos.credito}">
                                                                        <a4j:commandLink
                                                                                action="#{TelaClienteControle.obterStatusConciliadora}"
                                                                                reRender="panelStatusConciliadora"
                                                                                styleClass="tooltipster"
                                                                                title="#{historicoPagamentos.statusConciliadora.descricao}"
                                                                                oncomplete="#{TelaClienteControle.msgAlert}"
                                                                                style="display: inline; font-size: 14px; float: right">

                                                                            <h:outputText
                                                                                    styleClass="#{historicoPagamentos.statusConciliadora.styleClass}"
                                                                                    style="#{historicoPagamentos.statusConciliadora.style}"/>
                                                                        </a4j:commandLink>
                                                                    </h:panelGroup>

                                                                </rich:column>
                                                            </h:dataTable>
                                                            <h:panelGrid columns="1" rendered="#{TelaClienteControle.listaPagamentos.count > 0}" width="100%" columnClasses="colunaCentralizada">
                                                                <table width="100%" border="0" cellspacing="0" cellpadding="0">
                                                                    <tr>
                                                                        <td align="center" valign="middle">
                                                                            <h:panelGroup layout="block"
                                                                                          styleClass="paginador-container">
                                                                                <h:panelGroup styleClass="pull-left"
                                                                                              layout="block">
                                                                                    <h:outputText
                                                                                            styleClass="texto-size-14 cinza"
                                                                                            value="Total #{TelaClienteControle.listaPagamentos.count} itens"></h:outputText>
                                                                                </h:panelGroup>
                                                                                <h:panelGroup layout="block"
                                                                                              style="align-items: center">
                                                                                    <a4j:commandLink  styleClass="linkPadrao texto-cor-azul texto-size-20-real"  reRender="tabelaMovPagamentos"
                                                                                                      actionListener="#{TelaClienteControle.primeiraPagina}">
                                                                                        <i id="primeiraPagPagamentos" class="fa-icon-double-angle-left"></i>
                                                                                        <f:attribute name="tipo" value="LISTA_PAGAMENTOS" />
                                                                                    </a4j:commandLink>

                                                                                    <a4j:commandLink styleClass="linkPadrao texto-cor-azul texto-size-20-real" reRender="tabelaMovPagamentos"
                                                                                                     actionListener="#{TelaClienteControle.paginaAnterior}">
                                                                                        <i id="paginaAnteirorPagamentos" class="fa-icon-angle-left"></i>
                                                                                        <f:attribute name="tipo" value="LISTA_PAGAMENTOS" />
                                                                                    </a4j:commandLink>

                                                                                    <h:outputText styleClass="texto-font texto-cor-cinza texto-size-16-real"
                                                                                                  value="#{msg_aplic.prt_msg_pagina} #{TelaClienteControle.listaPagamentos.paginaAtualApresentar}" rendered="true"/>
                                                                                    <a4j:commandLink styleClass="linkPadrao texto-font texto-cor-azul texto-size-20-real" reRender="tabelaMovPagamentos"
                                                                                                     actionListener="#{TelaClienteControle.proximaPagina}">
                                                                                        <i id="proxpagPagamento" class="fa-icon-angle-right"></i>
                                                                                        <f:attribute name="tipo" value="LISTA_PAGAMENTOS" />
                                                                                    </a4j:commandLink>

                                                                                    <a4j:commandLink styleClass="linkPadrao texto-cor-azul texto-size-20-real" reRender="tabelaMovPagamentos"
                                                                                                     actionListener="#{TelaClienteControle.ultimaPagina}">
                                                                                        <i id="ultpagPagamento" class="fa-icon-double-angle-right"></i>
                                                                                        <f:attribute name="tipo" value="LISTA_PAGAMENTOS" />
                                                                                    </a4j:commandLink>
                                                                                </h:panelGroup>
                                                                                <h:panelGroup layout="block" styleClass="paginador-itens-pagina">
                                                                                    <h:panelGroup styleClass="pull-right" layout="block">
                                                                                        <h:outputText
                                                                                                styleClass="texto-size-14 cinza "
                                                                                                value="Itens por página "></h:outputText>
                                                                                    </h:panelGroup>
                                                                                    <h:panelGroup styleClass="cb-container pl20" layout="block">
                                                                                        <h:selectOneMenu value="#{TelaClienteControle.listaPagamentos.limit}" id="qtdeItensPag">
                                                                                            <f:selectItem itemValue="#{6}"></f:selectItem>
                                                                                            <f:selectItem itemValue="#{10}"></f:selectItem>
                                                                                            <f:selectItem itemValue="#{20}"></f:selectItem>
                                                                                            <f:selectItem itemValue="#{50}"></f:selectItem>
                                                                                            <f:selectItem itemValue="#{100}"></f:selectItem>
                                                                                            <a4j:support event="onchange" actionListener="#{TelaClienteControle.atualizarNumeroItensPagina}" reRender="tabelaMovPagamentos">
                                                                                                <f:attribute name="tipo" value="LISTA_PAGAMENTOS" />
                                                                                            </a4j:support>
                                                                                        </h:selectOneMenu>
                                                                                    </h:panelGroup>

                                                                                </h:panelGroup>
                                                                            </h:panelGroup>
                                                                        </td>
                                                                    </tr>
                                                                </table>
                                                            </h:panelGrid>
                                                        </h:panelGroup>
                                                    </h:panelGroup>
                                                    <h:panelGroup layout="block" styleClass="containerCompras blocoCobranca" style="display: none; padding-bottom: 20px">

                                                        <%@include file="includes/remessas/include_itens_paginado_remessaestorno.jsp" %>

                                                        <%@include file="includes/remessas/include_itens_paginado_remessa_boleto.jsp" %>

                                                        <%@include file="includes/transacoes/include_itens_paginado_boleto.jsp" %>

                                                        <%@include file="includes/transacoes/include_itens_paginado_transacoes.jsp" %>

                                                        <%@include file="includes/transacoes/include_itens_paginado_transacoes_verificacao.jsp" %>

                                                        <%@include file="includes/transacoes/include_itens_paginado_pix.jsp" %>

                                                        <%@include file="includes/transacoes/include_itens_paginado_getCard.jsp" %>

                                                        <%@include file="includes/transacoes/include_itens_paginado_mov_conta.jsp" %>

                                                    </h:panelGroup>
                                                    <div class="containerCompras blocoProdutosVencimento" style="display: none;">
                                                        <h:panelGroup id="conteinerProdutoValidade" layout="block" >
                                                            <h:outputText value="Nenhum produto."
                                                                          style="margin-top: 20px; display: block; margin-bottom: 20px;"
                                                                          styleClass="texto-size-14 cinza pl20"
                                                                          rendered="#{fn:length(TelaClienteControle.listaProdutosComValidade) eq 0}"/>

                                                            <h:dataTable id="listaProdutoComValidade" width="100%" border="0"
                                                                         styleClass="tabelaDados semZebra"
                                                                         value="#{TelaClienteControle.listaProdutosComValidade}"
                                                                         rendered="#{fn:length(TelaClienteControle.listaProdutosComValidade) gt 0}"
                                                                         var="movProduto">
                                                                <h:column>
                                                                    <f:facet name="header">
                                                                        <h:outputText style="font-weight: bold" value="Código" />
                                                                    </f:facet>
                                                                    <h:outputText styleClass="cinza"
                                                                                  value="#{movProduto.codigo}" />
                                                                </h:column>
                                                                <h:column>
                                                                    <f:facet name="header">
                                                                        <h:outputText style="font-weight: bold" value="Produto" />
                                                                    </f:facet>
                                                                    <h:outputText styleClass="cinza"
                                                                                  value="#{movProduto.produto.descricao}" />
                                                                </h:column>
                                                                <h:column>
                                                                    <f:facet name="header">
                                                                        <h:outputText style="font-weight: bold"
                                                                                      value="Data Compra" />
                                                                    </f:facet>
                                                                    <h:outputText styleClass="cinza"
                                                                                  value="#{movProduto.dataLancamento_Apresentar}" />
                                                                </h:column>
                                                                <h:column>
                                                                    <f:facet name="header">
                                                                        <h:outputText style="font-weight: bold"
                                                                                      value="Data Inicio Vigência" />
                                                                    </f:facet>
                                                                    <h:outputText styleClass="cinza"
                                                                                  value="#{movProduto.dataInicioVigencia_Apresentar}" />
                                                                </h:column>
                                                                <h:column>
                                                                    <f:facet name="header">
                                                                        <h:outputText style="font-weight: bold"
                                                                                      value="Data Final Vigência" />
                                                                    </f:facet>
                                                                    <h:outputText styleClass="cinza"
                                                                                  value="#{movProduto.dataFinalVigencia_Apresentar}" />
                                                                </h:column>
                                                                <h:column>
                                                                    <f:facet name="header">
                                                                        <h:outputText style="font-weight: bold"
                                                                                      value="Renovável Automaticamente" />
                                                                    </f:facet>
                                                                    <h:panelGrid columns="2">
                                                                        <h:panelGroup layout="block" style="margin: 0 5px;">
                                                                            <h:outputText styleClass="cinza"
                                                                                          value="#{movProduto.renovavelAutomaticamente_Apresentar}" />
                                                                        </h:panelGroup>
                                                                        <a4j:commandLink rendered="#{movProduto.apresentarAlterarRenovavelAutomaticamente}" style="color: #4d90fe; " styleClass="texto-size-14" id="alterarRenovavelAltomaticamenteContrato"
                                                                                         action="#{TelaClienteControle.confirmarAlterarMovprodutoRenovarAutomaticamente}"
                                                                                         actionListener="#{TelaClienteControle.selecionarProdutoComValidade}"
                                                                                         reRender="panelAutorizacaoFuncionalidade" value=" Alterar">
                                                                            <f:attribute name="movProdutoVO" value="#{movProduto}"/>
                                                                        </a4j:commandLink>
                                                                    </h:panelGrid>
                                                                </h:column>
                                                                <h:column>
                                                                    <a4j:commandLink id="botaoValidarEditarData" style="margin-left: 8px;" title="Alterar a data final de vigência"
                                                                                     rendered="#{movProduto.produto.tipoVigencia != 'VV' && movProduto.produto.tipoProduto != 'TR' && movProduto.produto.tipoProduto != 'DS'}"
                                                                                     action="#{MovProdutoControle.confirmarAlteracaoDataValidade}"
                                                                                     actionListener="#{MovProdutoControle.selecionarMovProdutoListener}"
                                                                                     reRender="panelAutorizacaoFuncionalidade, modalEditarVigenciaFinalProdutoCliente"
                                                                                     styleClass="texto-cor-azul linkPadrao tooltipster inline">
                                                                        <f:attribute name="objMovProdutoEdicao" value="#{movProduto}"/>
                                                                        <i class="fa-icon-edit" ></i>
                                                                    </a4j:commandLink>

                                                                    <a4j:commandLink id="btnRenovarProduto" style="margin-left: 8px;" title="Renovar um produto com Validade"
                                                                                     styleClass="texto-cor-azul linkPadrao tooltipster inline"
                                                                                     rendered="#{movProduto.produto.apresentarRenovar  && movProduto.produto.tipoProduto != 'DS'}"
                                                                                     action="#{RenovarProdutoControle.confirmarLancarProduto}"
                                                                                     actionListener="#{RenovarProdutoControle.renovarProduto}"
                                                                                     reRender="panelAutorizacaoFuncionalidade, modalEscolherProdutoRenovar">
                                                                        <f:attribute name="movProdutoVO" value="#{movProduto}"/>
                                                                        <f:attribute name="clienteVO" value="#{TelaClienteControle.cliente}"/>
                                                                        <i class="fa-icon-dollar" ></i>
                                                                    </a4j:commandLink>

                                                                    <a4j:commandLink id="btnVisualizarUtilizacao" style="margin-left: 8px;" title="Visualizar utilizações"
                                                                                       rendered="#{movProduto.produto.categoriaProduto.avaliacaoFisica}"
                                                                                       actionListener="#{TelaClienteControle.prepararVisualizacaoUtilizacaoAvaliacao}"
                                                                                       oncomplete="Richfaces.showModalPanel('modalUtilizacoesAvaliacaoFisica')"
                                                                                       reRender="modalUtilizacoesAvaliacaoFisica"
                                                                                       styleClass="texto-cor-azul linkPadrao tooltipster inline">
                                                                        <f:attribute name="movProdutoVO" value="#{movProduto}"/>
                                                                        <i class="fa-icon-search" ></i>
                                                                    </a4j:commandLink>

                                                                    <a4j:commandLink id="btnEditarAtestado" style="margin-left: 8px;" title="Visualizar Atestado"
                                                                                     rendered="#{movProduto.produto.tipoVigencia == 'VV'}"
                                                                                     action="#{AtestadoControle.editarAtestado}"
                                                                                     actionListener="#{AtestadoControle.prepararEdicaoAtestado}"
                                                                                     oncomplete="abrirPopup('atestadoAptidaoFisica.jsp', 'AptidaoFisica', 880, 650); return false;"
                                                                                     reRender="panelAutorizacaoFuncionalidade, modalEscolherProdutoRenovar"
                                                                                     styleClass="texto-cor-azul linkPadrao tooltipster inline">
                                                                        <f:attribute name="movProdutoVO" value="#{movProduto}"/>
                                                                        <f:attribute name="clienteVO" value="#{TelaClienteControle.cliente}"/>
                                                                        <i class="fa-icon-search" ></i>
                                                                    </a4j:commandLink>

                                                                    <a4j:commandLink id="btnExcluirAtestado" style="margin-left: 8px;" title="Excluir Atestado"
                                                                                     rendered="#{movProduto.produto.tipoVigencia == 'VV'}"
                                                                                     action="#{AtestadoControle.prepararExcluirAtestado}"
                                                                                     styleClass="texto-cor-azul linkPadrao tooltipster inline"
                                                                                     reRender="panelAutorizacaoFuncionalidade">
                                                                        <i class="fa-icon-remove" ></i>
                                                                    </a4j:commandLink>
                                                                </h:column>
                                                            </h:dataTable>
                                                        </h:panelGroup>
                                                    </div>

                                                    <script>
                                                        carregarTooltipster();
                                                    </script>

                                                </c:if>
                                            </h:panelGroup>
                                        </div>
                                        <h:panelGroup style="width: 96%; height: auto; margin-bottom: 20px;min-width: 700px;" styleClass="painelDadosAluno tudo googleAnalytics"
                                                      layout="block"
                                                      id="painelConvites"
                                                      rendered="#{fn:length(TelaClienteControle.convites) > 0}">

                                            <div class="tituloPainelAluno">
                                                <h:outputText value="Convites" styleClass="texto-size-14 negrito cinzaEscuro pl20"/>

                                            </div>

                                            <table class="tabelaDados semZebra">
                                                <tr>
                                                    <th style="padding-left: 10px;">Quem convidou</th>
                                                    <th class="centro">Data</th>
                                                    <th>Convite</th>
                                                    <th class="direita">Qtd. Aulas</th>
                                                    <th class="centro">Vigência</th>
                                                    <th>Modalidades</th>
                                                </tr>
                                                <c:forEach items="${TelaClienteControle.convites}" var="convite">
                                                    <tr>
                                                        <td style="padding-left: 10px;"><a class="texto-cor-azul linkPadrao" style="text-transform: capitalize;"
                                                                                           href="clienteNav.jsp?page=cliente&matricula=${convite.clienteConvidou.matricula}"
                                                                                           target="cli_${convite.clienteConvidou.matricula}">${convite.clienteConvidou.pessoa.nomeMinusculo}</a></td>
                                                        <td class="centro">${convite.dataLancamentoApresentar}</td>
                                                        <td>${convite.tipoConviteAulaExperimentalVO.descricao}</td>

                                                        <td class="direita">${convite.tipoConviteAulaExperimentalVO.quantidadeAulaExperimental}</td>
                                                        <td class="centro">${convite.tipoConviteAulaExperimentalVO.vigenciaSemHoraApresentar}</td>
                                                        <td>${convite.modalidades}</td>
                                                    </tr>
                                                    <c:if test="${not empty convite.reposicoes}">
                                                        <tr style="border: #e5e5e5 1px solid !important; ">
                                                            <td colspan="6">
                                                                <table class="tabelaDados semZebra"
                                                                       style="margin: 0 !important; min-width: 50%; width: auto !important;">
                                                                    <tr>
                                                                        <th colspan="4" style="padding-left: 10px;">Aulas :</th>
                                                                    </tr>
                                                                    <c:forEach items="${convite.reposicoes}" var="reposicao">
                                                                        <tr style="padding: 0;">
                                                                            <td style="padding-left: 10px;">
                                                                                    ${reposicao.horarioTurma.identificadorTurma}
                                                                            </td>
                                                                            <td style="padding-left: 10px;">
                                                                                    ${reposicao.dataReposicao_Apresentar}
                                                                            </td>
                                                                            <td style="padding-left: 10px;">
                                                                                    ${reposicao.horarioTurma.horaInicial}
                                                                            </td>
                                                                            <td style="padding-left: 10px;">
                                                                                    ${reposicao.horarioTurma.horaFinal}
                                                                            </td>
                                                                        </tr>
                                                                    </c:forEach>
                                                                </table>
                                                            </td>
                                                        </tr>
                                                    </c:if>
                                                </c:forEach>
                                            </table>
                                        </h:panelGroup>

                                        <h:panelGroup style="width: 96%; height: auto; margin-bottom: 20px;min-width: 700px;" styleClass="painelDadosAluno tudo googleAnalytics"
                                                      layout="block"
                                                      id="panelArmarioCliente"
                                                      rendered="#{fn:length(TelaClienteControle.cliente.armariosAlugados) > 0}">

                                            <div class="tituloPainelAluno">
                                                <h:outputText value="Armários" styleClass="texto-size-14 negrito cinzaEscuro pl20"/>
                                                <h:outputLink styleClass="pl5"
                                                              value="#{SuperControle.urlWiki}Inicial:Informações_do_Cliente"
                                                              title="Clique e saiba mais: Armários"
                                                              target="_blank" >
                                                    <i class="fa-icon-external-link-square cinzaClaro"></i>
                                                </h:outputLink>
                                            </div>


                                            <rich:dataTable id="listaArmarioAlugado" width="100%" border="0"
                                                            cellspacing="0" cellpadding="10" styleClass="tabelaDados semZebra"
                                                            value="#{TelaClienteControle.cliente.armariosAlugados}"
                                                            var="aluguel"
                                                            rows="7">
                                                <rich:column>
                                                    <f:facet name="header">
                                                        <h:outputText style="font-weight: bold" value="Armário" />
                                                    </f:facet>
                                                    <h:outputText value="#{aluguel.armario.descricao}" />
                                                </rich:column>
                                                <rich:column>
                                                    <f:facet name="header">
                                                        <h:outputText style="font-weight: bold"
                                                                      value="Lançamento Locação" />
                                                    </f:facet>
                                                    <h:outputText value="#{aluguel.dataCadastro_Apresentar}" />
                                                </rich:column>
                                                <rich:column>
                                                    <f:facet name="header">
                                                        <h:outputText style="font-weight: bold"
                                                                      value="Início Locação" />
                                                    </f:facet>
                                                    <h:outputText value="#{aluguel.dataInicioAluguelApresentar}" />
                                                </rich:column>
                                                <rich:column>
                                                    <f:facet name="header">
                                                        <h:outputText style="font-weight: bold"
                                                                      value="Vencimento Locação" />
                                                    </f:facet>
                                                    <h:outputText value="#{aluguel.dataFimOriginalApresentar}" />
                                                </rich:column>
                                                <rich:column>
                                                    <f:facet name="header">
                                                        <h:outputText style="font-weight: bold"
                                                                      value="Chave Devolvida" />
                                                    </f:facet>
                                                        <a4j:commandLink id="check1" status="none" styleClass="texto-cor-azul botao-checkbox texto-size-14"
                                                                         actionListener="#{TelaClienteControle.prepararAtualizarStatusChaveArmarioDevolvida}"
                                                                         action="#{TelaClienteControle.atualizarStatusChaveArmarioDevolvida}"
                                                                         oncomplete="#{TelaClienteControle.msgAlert}"
                                                                         reRender="check1,lchk1">
                                                            <h:outputText styleClass="icon #{aluguel.chaveDevolvida ? 'fa-icon-check' : 'fa-icon-check-empty'}"/>
                                                            <f:setPropertyActionListener value="#{!aluguel.chaveDevolvida}"
                                                                                         target="#{aluguel.chaveDevolvida}"/>
                                                        </a4j:commandLink>
                                                </rich:column>
                                                <rich:column  rendered="#{ArmarioControleRel.habilitadoGestaoArmarios}">
                                                    <f:facet name="header">
                                                        <h:outputText style="font-weight: bold"
                                                                      value="Contrato Assinado" ></h:outputText>
                                                    </f:facet>
                                                    <a4j:commandLink  styleClass="botaoAssinarCotrato" action="#{ClienteControle.selecionarArmarioContratoAssinado}"
                                                                      oncomplete="#{rich:component('modalPanelContratoArmario')}.show();" reRender="modalPanelContratoArmario">
                                                        <h:outputText style="color: #29ABE2  !important" styleClass="contratoarmario #{aluguel.contratoAssinadoApresentar}" />
                                                    </a4j:commandLink>

                                                    <rich:spacer width="10px"/>
                                                </rich:column>

                                                <f:facet name="footer">
                                                    <rich:datascroller for="listaArmarioAlugado" pageIndexVar="indiceArmario" pagesVar="pagesArmario" style="background: white;" styleClass="paginatorRemoveHover">
                                                        <f:facet name="first">
                                                            <h:outputText value="<<" />
                                                        </f:facet>
                                                        <f:facet name="first_disabled">
                                                            <h:outputText value="<<" />
                                                        </f:facet>
                                                        <f:facet name="fastrewind">
                                                            <h:outputText value="<" />
                                                        </f:facet>
                                                        <f:facet name="fastrewind_disabled">
                                                            <h:outputText value="<" />
                                                        </f:facet>
                                                        <f:facet name="pages" >
                                                            <h:outputText value="Página #{indiceArmario}/#{pagesArmario}"/>
                                                        </f:facet>
                                                        <f:facet name="fastforward">
                                                            <h:outputText value=">" />
                                                        </f:facet>
                                                        <f:facet name="fastforward_disabled">
                                                            <h:outputText value=">" />
                                                        </f:facet>
                                                        <f:facet name="last">
                                                            <h:outputText value=">>" />
                                                        </f:facet>
                                                        <f:facet name="last_disabled">
                                                            <h:outputText value=">>" />
                                                        </f:facet>
                                                    </rich:datascroller>
                                                </f:facet>
                                            </rich:dataTable>

                                        </h:panelGroup>

                                        <jsp:include page="includes/cliente/include_grafico_acessos_cliente.jsp" flush="false"/>
                                    </td>
                                </tr>
                            </table>
                        </h:panelGroup>
                    </h:panelGroup>
                </h:panelGroup>
            </h:panelGroup>

            <h:panelGroup  layout="block"   rendered="#{!LoginControle.permissaoAcessoMenuVO.cliente}" styleClass="googleAnalytics">
                <h:panelGrid width="100%" columns="1" cellpadding="10" id="msgPermissao"
                             style="background: #eff2f7" columnClasses="colunaCentralizada">
                    <rich:spacer height="150"/>
                    <h:outputText  escape="false" style="font-weight: bold; font-size: x-large;" value="Usuário sem permissão \"2.04 - Clientes\" para consultar clientes no módulo ADM.
                    <br/><br/>Por favor, peça ao administrador para liberar essa permissão <br/>ou tente acessar pelo módulo Treino." />
                    <c:if test="${param.readOnly}">
                        <h:commandLink id="fechar" value="Fechar" title="Fechar Janela" onclick="fecharJanela();" styleClass="pure-button pure-button-primary"/>
                    </c:if>
                </h:panelGrid>
            </h:panelGroup>
            <h:panelGroup  layout="block"   rendered="#{LoginControle.permissaoAcessoMenuVO.cliente}" styleClass="googleAnalytics">
                <jsp:include page="include_rodape_flat.jsp" flush="false"/>
            </h:panelGroup>
        </h:panelGroup>
        <rich:panel styleClass="painelMarcarAluno">

            <rich:dropSupport acceptedTypes="alunos" dropValue="aluno"
                              reRender="#{SuperControle.menuZwUi ? 'painelAlunosMarcadosNovoMenu' : 'painelAlunosMarcados'}"
                              oncomplete="carregarTooltipster();"
                              dropListener="#{ClientesMarcadosControle.processDrop}">
            </rich:dropSupport>

            <div class="textoMarcarAluno">
                <i class="fa-icon-plus" style="margin-right: 10px;"></i>
                <h:outputText value="Marcar aluno"/>
            </div>
        </rich:panel>
        <a4j:jsFunction action="#{DicasControle.marcarEsconder}" name="marcarExibirTutorial" reRender="containerFaqToid">
            <f:setPropertyActionListener value="#{DicasControle.exibirDicaTutorial}" target="#{DicasControle.naoMostrarMais}"/>
        </a4j:jsFunction>
        <a4j:jsFunction name="selecionarPrimeiroContrato"  reRender="idpainelcontrato,idlistacontratos"
                        action="#{TelaClienteControle.selecionarPrimeiroContrato}" >

        </a4j:jsFunction>
        <a4j:jsFunction reRender="idpainelcontrato, idlistacontratos"
                        name="voltarContrato"
                        action="#{TelaClienteControle.voltarContratos}">
        </a4j:jsFunction>

        <a4j:jsFunction name="abrirNovaTelaClientePadrao"
                        oncomplete="#{TelaClienteControle.msgAlert}"
                        action="#{TelaClienteControle.abrirNovaTelaClientePadrao}">
        </a4j:jsFunction>
        <a4j:jsFunction name="verificarAbrirNovaTelaCliente" status="false"
                        oncomplete="#{TelaClienteControle.msgAlert}"
                        action="#{TelaClienteControle.verificarAbrirNovaTelaCliente}">
        </a4j:jsFunction>

        <h:panelGroup layout="block" rendered="#{DicasControle.exibirDicaTutorial && not empty TelaClienteControle.listaContratos}" id="containerFaqToid" >

        </h:panelGroup>
    </h:form>

    <a4j:jsFunction name="limparCampoEditavel" action="#{TelaClienteControle.limparCamposEditaveis}"/>

    <rich:modalPanel id="mdlObservacoes" width="550" autosized="true" shadowOpacity="true" styleClass="novaModal googleAnalytics" onhide="limparCampoEditavel()">
        <f:facet name="header">
            <h:panelGroup>
                Observação
            </h:panelGroup>
        </f:facet>
        <f:facet name="controls">
            <h:panelGroup id="fecharObservacoes">
                <h:outputText
                        styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign btnSelecionarModalidade"
                        id="hidelinkmdlObservacoes"/>
                <rich:componentControl for="mdlObservacoes" attachTo="hidelinkmdlObservacoes" operation="hide"
                                       event="onclick"/>
            </h:panelGroup>
        </f:facet>

        <a4j:form prependId="true" id="formMdlObservacoes" >
            <h:panelGroup layout="block"  styleClass="texto-size-16-real texto-font texto-cor-cinza" style="position: absolute;left: 25%;">
                <h:outputText id="retornoInformacao" value="#{TelaClienteControle.retornoListaModal}"/>
            </h:panelGroup>
            <%--rendered="# { TelaClienteControle.permitirAlteracaoMensagemObservacao}"--%>
            <div class="googleAnalytics">
                <a4j:repeat value="#{TelaClienteControle.clienteObservacoes}" var="clienteMensagem" rowKeyVar="index" id="listaObservacoes">
                    <div class="containerPequeno">
                        <h:panelGroup rendered="#{clienteMensagem.alterarObservacao}">
                            <h:inputTextarea styleClass="texto-size-14 cinza nop"
                                             style="width: calc(100% - 2vw); margin-top:1vw; margin-left:1vw; height: 70px;margin-bottom: 10px;"
                                             value="#{clienteMensagem.observacao}" />
                        </h:panelGroup>

                        <h:outputText styleClass="texto-size-14 cinza nop"
                                      style="padding: 0;"
                                      value="#{clienteMensagem.observacaoComLimite}" escape="false"
                                      rendered="#{!clienteMensagem.alterarObservacao}"/>
                    </div>

                    <a4j:commandLink styleClass="nvoBt googleAnalytics" style="float:right; display:block;margin-top: -2px;margin-top: -21px;" id="detalharObservacao"
                                     rendered="#{!clienteMensagem.alterarObservacao}"
                                     action="#{TelaClienteControle.selecionarObservacaoParaVisualizacao}"
                                     reRender="mdlObservacoesVizualizar"
                                     oncomplete="#{TelaClienteControle.onCompleteVisualizar};#{TelaClienteControle.mensagemNotificar}">
                        Ver Mais
                    </a4j:commandLink>
                    <a4j:commandLink id="gravarObservacaoMensagem" styleClass="botoes nvoBt"
                                     style="float:right; display:block;margin-top: -2px;"
                                     action="#{TelaClienteControle.alterarObservacao}"
                                     rendered="#{clienteMensagem.alterarObservacao}"
                                     reRender="formMdlObservacoes, ultimaObservacao"
                                     oncomplete="#{TelaClienteControle.mensagemNotificar}">
                        Gravar
                    </a4j:commandLink>

                    <a4j:commandLink id="excluirObservacaoMensagem" title="Excluir" styleClass="botoes nvoBt btSec btPerigo tooltipster"
                                     style="float:right; display:block;margin-top: -1px;"
                                     action="#{TelaClienteControle.prepararObservacaoExclussao}"
                                     oncomplete="Richfaces.showModalPanel('modalObservacaoConfirmarcao');"
                                     reRender="formMdlObservacoes, ultimaObservacao,retornoInformacao">
                        <i class="fa-icon-trash"/>
                    </a4j:commandLink>

                    <a4j:commandLink id="alterarObservacaoMensagem" title="Editar" styleClass="botoes nvoBt btSec btPerigo tooltipster"
                                     style="float:right; display:block;margin-top: -1px;"
                                     action="#{TelaClienteControle.selecionarObservacao}"
                                     reRender="formMdlObservacoes, ultimaObservacao">
                        <i class="fa-icon-edit"/>
                    </a4j:commandLink>

                    <div class="containerPequeno" style="border-bottom: #E5E5E5 1px solid; margin-bottom: 2vh; max-width: 350px;">
                        <h:outputText value="#{clienteMensagem.usuarioVO.nomeAbreviado}"
                                      styleClass="texto-size-14" style="color: #9E9E9E;"/>
                        <h:outputText value="#{clienteMensagem.dataCadastro}"
                                      styleClass="texto-size-14"
                                      style="float: right;color: #9E9E9E;">
                            <f:convertDateTime type="date" dateStyle="short"
                                               locale="pt" timeZone="#{SuperControle.timeZoneDefault}"
                                               pattern="dd 'de' MMMM 'de' yyyy 'às' HH:mm" />
                        </h:outputText>
                    </div>
                </a4j:repeat>
            </div>
            <h:panelGroup layout="block"
                          style="align-items: center;margin: 0 auto;width: 23%;">
                <a4j:commandLink styleClass="linkPadrao texto-cor-azul texto-size-20-real"  reRender="formMdlObservacoes, ultimaObservacao"
                                 actionListener="#{TelaClienteControle.primeiraPagina}">
                    <i class="fa-icon-double-angle-left"></i>
                    <f:attribute name="tipo" value="LISTA_OBSERVACAO" />
                </a4j:commandLink>

                <a4j:commandLink styleClass="linkPadrao texto-cor-azul texto-size-20-real" reRender="formMdlObservacoes, ultimaObservacao"
                                 actionListener="#{TelaClienteControle.paginaAnterior}">
                    <i class="fa-icon-angle-left"></i>
                    <f:attribute name="tipo" value="LISTA_OBSERVACAO" />
                </a4j:commandLink>

                <h:outputText styleClass="texto-font texto-cor-cinza texto-size-16-real"
                              value="#{msg_aplic.prt_msg_pagina} #{TelaClienteControle.listaClienteObservacao.paginaAtualApresentar}" rendered="true"/>
                <a4j:commandLink styleClass="linkPadrao texto-font texto-cor-azul texto-size-20-real" reRender="formMdlObservacoes, ultimaObservacao"
                                 actionListener="#{TelaClienteControle.proximaPagina}">
                    <i class="fa-icon-angle-right"></i>
                    <f:attribute name="tipo" value="LISTA_OBSERVACAO" />
                </a4j:commandLink>

                <a4j:commandLink styleClass="linkPadrao texto-cor-azul texto-size-20-real" reRender="formMdlObservacoes, ultimaObservacao"
                                 actionListener="#{TelaClienteControle.ultimaPagina}">
                    <i class="fa-icon-double-angle-right"></i>
                    <f:attribute name="tipo" value="LISTA_OBSERVACAO" />
                </a4j:commandLink>
            </h:panelGroup>
        </a4j:form>
    </rich:modalPanel>

    <rich:modalPanel id="mdlObservacoesAdicionar" width="450" autosized="true" shadowOpacity="true" styleClass="novaModal">
        <f:facet name="header">
            <h:panelGroup>
                Observação
            </h:panelGroup>
        </f:facet>
        <f:facet name="controls">
            <h:panelGroup>
                <h:outputText
                        styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign btnSelecionarModalidade"
                        id="hidelinkmdlObservacoesAdicionar"/>
                <rich:componentControl for="mdlObservacoesAdicionar" attachTo="hidelinkmdlObservacoesAdicionar" operation="hide"
                                       event="onclick"/>
            </h:panelGroup>
        </f:facet>

        <a4j:form prependId="true" id="formMdlObservacoesAdicionar">
            <h:inputTextarea styleClass="inputTextClean" value="#{TelaClienteControle.mensagemObservacaoCliente.mensagem}"
                             id="textoObservacaoClienteAdicionar"
                             style="width: calc(100% - 2vw); margin-top:1vw; margin-left:1vw; height: 70px;"/>
            <a4j:commandLink styleClass="botoes nvoBt" style="float: right; margin: 5px 1vw;"
                             id="confirmarObservacaoAdicionar"
                             action="#{TelaClienteControle.permisaoClienteMensagemObservacaoGeral}"
                             oncomplete="#{TelaClienteControle.mensagemNotificar};Richfaces.hideModalPanel('mdlObservacoesAdicionar')"
                             reRender="formMdlObservacoes, ultimaObservacao">
                Adicionar
            </a4j:commandLink>
        </a4j:form>
    </rich:modalPanel>

    <rich:modalPanel id="mdlObservacoesVizualizar" width="750" height="400" autosized="true" shadowOpacity="true" styleClass="novaModal">
        <f:facet name="header">
            <h:panelGroup>
                Observação
            </h:panelGroup>
        </f:facet>
        <f:facet name="controls">
            <h:panelGroup>
                <h:outputText
                        styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign btnSelecionarModalidade"
                        id="hidelinkmdlObservacoesVizualizar"/>
                <rich:componentControl for="mdlObservacoesVizualizar" attachTo="hidelinkmdlObservacoesVizualizar" operation="hide"
                                       event="onclick"/>
            </h:panelGroup>
        </f:facet>

        <a4j:form prependId="true" id="formMdlObservacoesVizualizar">
            <h:inputTextarea styleClass="inputTextClean" value="#{TelaClienteControle.oservacaoParaVisualizacao.observacao}"
                             id="observacaoVizualisar"
                             readonly="true"
                             style="width: calc(100% - 2vw); margin-top:1vw; margin-left:1vw; height: 400px;"/>
        </a4j:form>
    </rich:modalPanel>

    <rich:modalPanel id="modalObservacaoConfirmarcao" width="450" autosized="true" shadowOpacity="true" styleClass="novaModal">
        <f:facet name="header">
            <h:panelGroup>
                Excluir Observação
            </h:panelGroup>
        </f:facet>
        <f:facet name="controls">
            <h:panelGroup>
                <h:outputText
                        styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign btnSelecionarModalidade"
                        id="hideModalObservacaoConfirmarcao"/>
                <rich:componentControl for="modalObservacaoConfirmarcao" attachTo="hideModalObservacaoConfirmarcao" operation="hide"
                                       event="onclick"/>
            </h:panelGroup>
        </f:facet>

        <a4j:form prependId="true" id="formMdlObservacoesConfirmarcao">
            <h:panelGroup layout="block" styleClass="texto-size-16-real texto-font texto-cor-cinza" style="position: absolute;left: 25%;">
                <h:outputText value="Deseja excluir a Observação ?"/>
            </h:panelGroup>
            <h:panelGroup layout="block" styleClass="container-botoes" style="margin-bottom: 15px;margin-top: 20px;">
                <h:panelGroup styleClass="margin-box">
                    <a4j:commandLink styleClass="botaoPrimario texto-size-16-real"
                                     id="confirmarExclussaoObservacao"
                                     action="#{TelaClienteControle.excluirObservacao}"
                                     oncomplete="#{TelaClienteControle.mensagemNotificar};#{TelaClienteControle.retornoOncompleteListaObservacao}"
                                     reRender="formMdlObservacoes, ultimaObservacao">
                        Sim
                    </a4j:commandLink>
                    <rich:spacer width="30px;"/>
                    <a4j:commandLink styleClass="botaoPrimario texto-size-16-real"
                                     id="naoExcluirObservacao"
                                     action="#{TelaClienteControle.prepararAbrirObservacao}"
                                     oncomplete="Richfaces.hideModalPanel('modalObservacaoConfirmarcao')">
                        Não
                    </a4j:commandLink>
                </h:panelGroup>
            </h:panelGroup>
        </a4j:form>
    </rich:modalPanel>

    <rich:modalPanel id="mdlAvisos" width="450" autosized="true" shadowOpacity="true" styleClass="novaModal" >
        <f:facet name="header">
            <h:panelGroup>
                Avisos
            </h:panelGroup>
        </f:facet>
        <f:facet name="controls">
            <h:panelGroup>
                <h:outputText
                        styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign btnSelecionarModalidade"
                        id="hidelinkmdlAvisos"/>
                <rich:componentControl for="mdlAvisos" attachTo="hidelinkmdlAvisos" operation="hide"
                                       event="onclick"/>
            </h:panelGroup>
        </f:facet>

        <a4j:form prependId="true" id="formAvisos">
            <h:panelGroup id="painelmdlAvisos" style="margin-top: 0px; height: 40vh; overflow-x: hidden; overflow-y: auto;" styleClass="googleAnalytics"
                          layout="block">
                <h:dataTable width="100%"
                             value="#{TelaClienteControle.avisosModal}"
                             styleClass="tabFormSubordinada" var="avisoCliente">
                    <h:column>
                        <h:panelGroup layout="block" styleClass="googleAnalytics"
                                      style="min-height: 30px;background-color: #{avisoCliente.tipomensagem.grave ? '#F4C9D1' : ''}; width: calc(100% - 2vw); line-height: 30px; padding: 10px 1vw;">
                            <h:panelGroup rendered="#{avisoCliente.navegacaoFrame}">
                                <a4j:commandLink  title="#{avisoCliente.tipomensagem}"
                                                  action="#{TelaClienteControle.abreTela}"
                                                  actionListener="#{TelaClienteControle.selecionarClienteMensagemListener}">
                                    <f:attribute name="objClienteMensagem" value="#{avisoCliente}"/>
                                    <h:outputText  value="#{avisoCliente.mensagemTratada}"  styleClass="texto-size-14 cinza"  escape="false"/>
                                    <i class="fa-icon-arrow-right cinza" style="float: right; margin: 7px;"></i>
                                </a4j:commandLink>
                            </h:panelGroup>
                            <h:panelGroup rendered="#{avisoCliente.navegacaoPopUp}" styleClass="googleAnalytics">
                                <a4j:commandLink  title="#{avisoCliente.tipomensagem}" id="detalharMensagemTexto"
                                                  action="#{TelaClienteControle.abreTela}"
                                                  actionListener="#{TelaClienteControle.selecionarClienteMensagemListener}"
                                                  oncomplete="abrirPopup('#{avisoCliente.tipomensagem.navegacao}', '#{avisoCliente.tipomensagem}', 780, 595);">
                                    <f:attribute name="objClienteMensagem" value="#{avisoCliente}"/>
                                    <h:outputText styleClass="texto-size-14 cinza" value="#{avisoCliente.mensagemTratada}" escape="false"/>
                                    <i id="detalharMensagemAvisoIcon" class="fa-icon-arrow-right cinza" style="float: right; margin: 7px;"></i>

                                </a4j:commandLink>
                            </h:panelGroup>
                            <h:panelGroup rendered="#{!avisoCliente.navegacaoPopUp and !avisoCliente.navegacaoFrame}">
                                <h:outputText styleClass="texto-size-14 cinza tooltipster"
                                              value="#{avisoCliente.mensagemTratadaMin} <span style=\"color: lightgray;\"> (Passe o mouse para visualizar a mensagem)</span>" escape="false"
                                              title="#{avisoCliente.mensagemTratada}"/>


                            </h:panelGroup>


                        </h:panelGroup>
                    </h:column>
                </h:dataTable>
                <script>
                    carregarTooltipster();
                </script>
            </h:panelGroup>
        </a4j:form>
    </rich:modalPanel>
    <rich:modalPanel id="modalAnexoCliente" autosized="true"
                     shadowOpacity="true" width="580" height="350" styleClass="novaModal">
        <f:facet name="header">
            <h:panelGroup>
                <h:outputText value="Anexo Cliente" />
            </h:panelGroup>
        </f:facet>
        <f:facet name="controls">
            <h:panelGroup>
                <h:outputText
                        styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign btnSelecionarModalidade"
                        id="hiperlinkAnexoCliente"/>
                <rich:componentControl for="modalAnexoCliente" attachTo="hiperlinkAnexoCliente" operation="hide" event="onclick" />
            </h:panelGroup>
        </f:facet>
        <a4j:form id="formAnexoCliente">
            <h:outputText value="INSIRA O ANEXO AQUI" style="color: gainsboro;font-size: 44px;margin-left: 44px;" rendered="#{TelaClienteControle.cliente.anexo == ''}"/>
            <h:panelGrid columns="1" width="100%" cellpadding="0" cellspacing="0">

                <h:panelGrid columns="4" rendered="#{TelaClienteControle.cliente.anexo != ''}">
                    <h:panelGroup>
                        <h:outputText value="Nome Anexo" styleClass="texto-size-14-real texto-cor-cinza-2 texto-font texto-bold"/>
                        <br/>
                        <h:outputText value="#{TelaClienteControle.cliente.nomeAnexo}"
                                      styleClass="texto-size-14-real texto-cor-cinza-2 texto-font"/>
                    </h:panelGroup>
                    <h:panelGroup>
                        <h:outputText value="Data" styleClass="texto-size-14-real texto-cor-cinza-2 texto-font texto-bold" style="margin-left: 200px;"/>
                        <br/>
                        <h:outputText value="#{TelaClienteControle.cliente.dataCadastroAnexo_Apresentar}" style="margin-left: 200px;"
                                      styleClass="texto-size-14-real texto-cor-cinza-2 texto-font"/>
                    </h:panelGroup>
                    <h:outputLink styleClass="linkAzul icon tooltipster" style="margin-left: 150px;" title="Download Anexo"
                                  target="_blank"
                                  value="#{TelaClienteControle.cliente.urlCompletaAnexo}">
                        <i class="fa-icon-download-alt"/>
                    </h:outputLink>
                    <a4j:commandLink reRender="formAnexoCliente"
                                     style="margin-left: 10px;"
                                     styleClass="linkAzul icon tooltipster"
                                     title="Excluir Anexo"
                                     action="#{TelaClienteControle.excluirAnexo}"
                                     oncomplete="#{TelaClienteControle.mensagemNotificar}">

                        <i class="fa-icon-trash"/>
                    </a4j:commandLink>
                </h:panelGrid>
                <br/>
                <br/>
                <br/>
                <h:panelGroup id="panelAnexoCliente" rendered="#{TelaClienteControle.mostrarBotaoGravarAnexo}">
                    <h:outputText value="ANEXAR ARQUIVO"
                                  styleClass="texto-size-14-real texto-cor-cinza-2 texto-font texto-bold"/>
                    <rich:fileUpload id="upload"
                                     listHeight="60"
                                     listWidth="565"
                                     noDuplicate="false"
                                     fileUploadListener="#{TelaClienteControle.upload}"
                                     maxFilesQuantity="1"
                                     allowFlash="false"
                                     immediateUpload="false"
                                     acceptedTypes="jpg, jpeg, gif, png, bmp, pdf, JPG, JPEG, GIF, PNG, BMP, PDF,ZIP"
                                     addControlLabel="Adicionar"
                                     cancelEntryControlLabel="Cancelar"
                                     doneLabel="Pronto"
                                     sizeErrorLabel="Arquivo não Enviado. Excedeu o tamanho permitido."
                                     progressLabel="Enviando"
                                     clearControlLabel="Limpar"
                                     clearAllControlLabel="Limpar todos"
                                     stopControlLabel="Parar"
                                     uploadControlLabel="Enviar"
                                     transferErrorLabel="Falha de Transmissão"
                                     stopEntryControlLabel="Parar">
                        <a4j:support event="onadd" oncomplete="#{TelaClienteControle.onCompleteAnexo}"/>
                        <a4j:support event="onerror" oncomplete="#{TelaClienteControle.onCompleteAnexo}"/>
                        <a4j:support event="onupload" oncomplete="#{TelaClienteControle.onCompleteAnexo}"/>
                        <a4j:support event="onuploadcomplete" oncomplete="#{TelaClienteControle.onCompleteAnexo}"/>
                        <a4j:support event="onclear" reRender="panelAnexoCliente" action="#{TelaClienteControle.limparAnexo}"/>
                    </rich:fileUpload>
                </h:panelGroup>
                <h:panelGrid columns="1" width="100%" id="gridBotoes" style="margin-top: 155px;">
                    <h:panelGrid columns="1" width="100%" style="margin-top: 13px;">
                        <h:panelGrid columns="1" width="100%" columnClasses="colunaCentralizada">
                            <h:panelGroup>
                                <a4j:commandLink reRender="formAnexoCliente"
                                                 id="consultar"
                                                 styleClass="pure-button"
                                                 accesskey="2"
                                                 action="#{TelaClienteControle.cancelarOperacaoCadastroAnexo}"
                                                 oncomplete="Richfaces.hideModalPanel('modalAnexoCliente')">
                                    Cancelar
                                </a4j:commandLink>
                                <h:outputText value="    "/>
                                <a4j:commandLink reRender="formAnexoCliente"
                                                 rendered="#{TelaClienteControle.mostrarBotaoGravarAnexo}"
                                                 id="salvar"
                                                 styleClass="pure-button pure-button-primary"
                                                 accesskey="2"
                                                 action="#{TelaClienteControle.gravarAnexoCliente}"
                                                 oncomplete="#{TelaClienteControle.mensagemNotificar}">
                                    Gravar
                                </a4j:commandLink>
                                <h:outputText value="    "/>
                                <a4j:commandLink reRender="formAnexoCliente"
                                                 id="substituirArquivo"
                                                 styleClass="pure-button pure-button-primary"
                                                 accesskey="2"
                                                 rendered="#{TelaClienteControle.cliente.anexo != '' and !TelaClienteControle.mostrarBotaoGravarAnexo}"
                                                 action="#{TelaClienteControle.mostrarBotaoGravarAnexo}">
                                    <i id="substituirArquivoIcon" class="fa-icon-upload-alt"></i> Substituir arquivo
                                </a4j:commandLink>
                                <a4j:commandLink reRender="formAnexoCliente"
                                                 id="anexarArquivo"
                                                 styleClass="pure-button pure-button-primary"
                                                 accesskey="2"
                                                 rendered="#{TelaClienteControle.cliente.anexo == '' and !TelaClienteControle.mostrarBotaoGravarAnexo}"
                                                 action="#{TelaClienteControle.mostrarBotaoGravarAnexo}">
                                    <i id="anexarArquivoIcon" class="fa-icon-upload-alt"></i> Anexar arquivo
                                </a4j:commandLink>
                            </h:panelGroup>
                        </h:panelGrid>
                    </h:panelGrid>
                </h:panelGrid>
            </h:panelGrid>
        </a4j:form>
    </rich:modalPanel>
    <rich:modalPanel id="modalPanelHistoricoVinculo" autosized="true"
                     shadowOpacity="true" width="900" height="500" styleClass="novaModal" >
        <f:facet name="header">
            <h:panelGroup>
                <h:outputText value="Histórico Vínculo" />
            </h:panelGroup>
        </f:facet>
        <f:facet name="controls">
            <h:panelGroup>
                <h:outputText
                        styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign btnSelecionarModalidade"
                        id="hiperlinkHistoricoVinculo"/>
                <rich:componentControl for="modalPanelHistoricoVinculo" attachTo="hiperlinkHistoricoVinculo" operation="hide" event="onclick" />
            </h:panelGroup>
        </f:facet>
        <a4j:form id="formHistoricoVinculo">
            <h:panelGroup layout="block" id="panelHistoricoVinculo">
                <h:panelGrid id="panelListaHistoricoVinculo" columns="1" width="100%">
                    <h:dataTable id="listaHistoricoVinculo" width="100%"
                                 rows="7"
                                 styleClass="tabelaDados"
                                 value="#{HistoricoVinculoControle.listaHistoricoVinculo}"
                                 rendered="#{!empty HistoricoVinculoControle.listaHistoricoVinculo}"
                                 var="historicoVinculo">
                        <h:column>
                            <f:facet name="header">
                                <h:outputText value="#{msg_aplic.prt_HistoricoVinculo_dataRegistro}" />
                            </f:facet>
                            <h:outputText id="dataRegistro" value="#{historicoVinculo.dataRegistro_Apresentar}" />
                        </h:column>
                        <h:column>
                            <f:facet name="header">
                                <h:outputText value="#{msg_aplic.prt_HistoricoVinculo_colaborador}" />
                            </f:facet>
                            <h:outputText id="colaborador" value="#{historicoVinculo.colaborador.pessoa.nomeAbreviado}" />
                        </h:column>
                        <h:column>
                            <f:facet name="header">
                                <h:outputText value="#{msg_aplic.prt_HistoricoVinculo_tipoColaborador}" />
                            </f:facet>
                            <h:outputText id="tipoColaborador" value="#{historicoVinculo.tipoColaborador_Apresentar}" />
                        </h:column>
                        <h:column>
                            <f:facet name="header">
                                <h:outputText value="#{msg_aplic.prt_HistoricoVinculo_tipoHistoricoVinculo}" />
                            </f:facet>
                            <h:outputText id="tipoHistoricoVinculo" value="#{historicoVinculo.tipoHistoricoVinculo_Apresentar}" />
                        </h:column>
                        <h:column>
                            <f:facet name="header">
                                <h:outputText value="#{msg_aplic.prt_HistoricoVinculo_origem}"/>
                            </f:facet>
                            <h:outputText id="origemHistoricoVinculo" value="#{historicoVinculo.origem}"/>
                        </h:column>
                        <h:column>
                            <f:facet name="header">
                                <h:outputText value="#{msg_aplic.prt_HistoricoVinculo_respAlteracao}"/>
                            </f:facet>
                            <h:outputText id="respAlteracaoHistoricoVinculo" value="#{historicoVinculo.nomeUsuario}"/>
                        </h:column>
                    </h:dataTable>
                    <rich:datascroller align="center" for="formHistoricoVinculo:listaHistoricoVinculo"
                                       renderIfSinglePage="false" styleClass="scrollPureCustom"
                                       id="scHistoricoVinculo"/>
                </h:panelGrid>
            </h:panelGroup>
        </a4j:form>
    </rich:modalPanel>

    <rich:modalPanel id="panelConfirmaLiberacaoDeVaga" autosized="true" styleClass="novaModal" shadowOpacity="true" width="350" height="150"
                     onshow="document.getElementById('formConfirmacaoLiberarVaga:btn_fechar_confirmacao').focus();">
        <f:facet name="header">
            <h:panelGroup>
                <h:outputText value="Vaga(s) liberada(s) com sucesso!"/>
            </h:panelGroup>
        </f:facet>
        <f:facet name="controls">
            <h:panelGroup>
                <h:outputText
                        styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign btnSelecionarModalidade"
                        id="hidelinkConfirmacaoLiberacaoDeVaga"/>
                <rich:componentControl for="panelConfirmaLiberacaoDeVaga"
                                       attachTo="hidelinkConfirmacaoLiberacaoDeVaga" operation="hide" event="onclick"/>
            </h:panelGroup>
        </f:facet>
        <a4j:form id="formConfirmacaoLiberarVaga">
            <h:panelGrid columns="1"  width="100%" id="modalPanel" columnClasses="colunaCentralizada" styleClass="font-size-Em">
                <h:panelGroup layout="block" styleClass="container-botoes">
                    <a4j:commandLink id="btn_fechar_confirmacao"
                                     oncomplete="Richfaces.hideModalPanel('panelConfirmaLiberacaoDeVaga');"
                                     styleClass="botaoPrimario texto-size-14"
                                     reRender="form:idlistacontratos"
                                     value="#{msg_bt.btn_fechar}"/>
                </h:panelGroup>
            </h:panelGrid>
        </a4j:form>
    </rich:modalPanel>
    <rich:modalPanel id="panelRetornoPendente" autosized="true" styleClass="novaModal" shadowOpacity="true" width="400" height="150"
                     onshow="document.getElementById('formRetornoPendente:btn_fechar_retorno').focus();">
        <f:facet name="header">
            <h:panelGroup>
                <h:outputText value="Não foi possível executar essa operação"/>
            </h:panelGroup>
        </f:facet>
        <f:facet name="controls">
            <h:panelGroup>
                <h:outputText
                        styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign btnSelecionarModalidade"
                        id="hidelinkRetornoPendente"/>
                <rich:componentControl for="panelRetornoPendente"
                                       attachTo="hidelinkRetornoPendente" operation="hide" event="onclick"/>
            </h:panelGroup>
        </f:facet>
        <a4j:form id="formRetornoPendente">
            <h:panelGrid columns="1"  width="100%" id="modalPanel" columnClasses="colunaCentralizada" styleClass="font-size-Em">
                <h:panelGroup>
                    <h:outputText styleClass="texto-cor-vermelho texto-size-14" value="Existem operações de Retorno pendentes."/>
                </h:panelGroup>
                <h:panelGroup layout="block" styleClass="container-botoes">
                    <a4j:commandLink id="btn_fechar_retorno"
                                     oncomplete="Richfaces.hideModalPanel('panelRetornoPendente');"
                                     styleClass="botaoPrimario texto-size-14"
                                     reRender="form:idlistacontratos"
                                     value="#{msg_bt.btn_fechar}"/>
                </h:panelGroup>
            </h:panelGrid>
        </a4j:form>
    </rich:modalPanel>


    <rich:modalPanel id="modalLancarConvite" autosized="true" styleClass="novaModal" shadowOpacity="true" width="400" height="150">
        <f:facet name="header">
            <h:panelGroup>
                <h:outputText value="Convite de Aula Experimental"/>
            </h:panelGroup>
        </f:facet>
        <f:facet name="controls">
            <h:panelGroup>
                <h:outputText
                        styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign "
                        id="hidelinkmodalLancarConvite"/>
                <rich:componentControl for="modalLancarConvite"
                                       attachTo="hidelinkmodalLancarConvite" operation="hide" event="onclick"/>
            </h:panelGroup>
        </f:facet>
        <a4j:form>
            <h:panelGroup id="panelmodalLancarConvite">
                <h:panelGrid width="100%" columns="2">
                    <h:outputText styleClass="cinza" value="Tipo convite:"/>
                    <h:selectOneMenu id="situacao" styleClass="newComboBox noBorderLeft"
                                     style="vertical-align:middle; min-width: 200px;"
                                     value="#{TelaClienteControle.codigoTipoConvite}">
                        <f:selectItem itemValue="" itemLabel="Tipo de convite"/>
                        <f:selectItems value="#{TelaClienteControle.tiposConvites}"/>
                    </h:selectOneMenu>
                </h:panelGrid>
                <center>
                    <a4j:commandLink oncomplete="#{TelaClienteControle.msgAlert}"
                                     style="margin-top: 20px;"
                                     styleClass="pure-button pure-button-small pure-button-primary texto-size-14"
                                     reRender="panelRecarregar"
                                     action="#{TelaClienteControle.enviarConvite}">
                        <i class="fa-icon-ok"></i> &nbsp Confirmar
                    </a4j:commandLink>
                </center>

            </h:panelGroup>
        </a4j:form>
    </rich:modalPanel>

    <rich:modalPanel id="statusProtheus" autosized="true" styleClass="novaModal" shadowOpacity="true" width="400"
                     height="150">
        <f:facet name="header">
            <h:panelGroup>
                <h:outputText value="Status do título no Protheus"/>
            </h:panelGroup>
        </f:facet>
        <f:facet name="controls">
            <h:panelGroup>
                <h:outputText
                        styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign "
                        id="hidelinkstatusProtheus"/>
                <rich:componentControl for="statusProtheus"
                                       attachTo="hidelinkstatusProtheus" operation="hide" event="onclick"/>
            </h:panelGroup>
        </f:facet>
        <a4j:form>
            <h:panelGroup>
                <div style="font-size: 18px;">
                    <div style="padding: 10px; padding-bottom: 0;">
                        <h:outputText value="Status: "/>
                        <h:outputText value="#{TelaClienteControle.statusProtheus['status']}"/>
                    </div>

                    <div style="padding: 10px; padding-bottom: 0;">
                        <h:outputText value="Código Pacto: "/>
                        <h:outputText value="#{TelaClienteControle.statusProtheus['codPacto']}"/>
                    </div>

                    <div style="padding: 10px; padding-bottom: 0;">
                        <h:outputText value="Código no Protheus: "/>
                        <h:outputText value="#{TelaClienteControle.statusProtheus['codprotheus']}"/>
                    </div>


                    <div style="padding: 10px; padding-bottom: 0;">
                        <h:outputText value="Log de erro: " rendered="#{TelaClienteControle.statusProtheus['status'] eq 'ERRO'}"/>
                        <h:outputText value="#{TelaClienteControle.statusProtheus['logerro']}" rendered="#{TelaClienteControle.statusProtheus['status'] eq 'ERRO'}"/>
                    </div>

                    <div style="padding: 10px; ">
                        <h:outputText value="Última tentativa: " rendered="#{TelaClienteControle.statusProtheus['status'] eq 'ERRO'}"/>
                        <h:outputText value="Envio: " rendered="#{TelaClienteControle.statusProtheus['status'] ne 'ERRO'}"/>
                        <h:outputText value="#{TelaClienteControle.statusProtheus['ultimatentativa']}"/>
                    </div>
                </div>

            </h:panelGroup>
        </a4j:form>
    </rich:modalPanel>

    <rich:modalPanel id="modalStatusConciliadora" autosized="true" styleClass="novaModal" shadowOpacity="true" width="600">
        <f:facet name="header">
            <h:panelGroup>
                <h:outputText value="Status Integração Conciliadora"/>
            </h:panelGroup>
        </f:facet>
        <f:facet name="controls">
            <h:panelGroup>
                <h:outputText
                        styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign "
                        id="hidelinkmodalStatusConciliadora"/>
                <rich:componentControl for="modalStatusConciliadora"
                                       attachTo="hidelinkmodalStatusConciliadora" operation="hide" event="onclick"/>
            </h:panelGroup>
        </f:facet>
        <a4j:form>
            <h:panelGroup layout="block" id="panelStatusConciliadora">
                <h:dataTable id="logConciliacao" width="100%" cellpadding="5"
                             rowClasses="texto-size-14" columnClasses="colunaCentralizada,colunaCentralizada,colunaEsquerda"
                             value="#{TelaClienteControle.listaLogConciliadora}"
                             var="log">
                    <h:column>
                        <f:facet name="header">
                            <h:outputText value="Data"/>
                        </f:facet>
                        <h:outputText styleClass="cinza negrito"
                                      value="#{log.data_Apresentar}" />
                    </h:column>
                    <h:column>
                        <f:facet name="header">
                            <h:outputText value="Sucesso"/>
                        </f:facet>
                        <h:outputText styleClass="cinza"
                                      value="#{log.sucesso ? 'SIM' : 'NÃO'}" />
                    </h:column>
                    <h:column>
                        <f:facet name="header">
                            <h:outputText value="Resultado"/>
                        </f:facet>
                        <h:outputText styleClass="cinza"
                                      escape="false"
                                      value="#{log.resultado}" />
                    </h:column>
                </h:dataTable>

            </h:panelGroup>
        </a4j:form>
    </rich:modalPanel>


    <rich:modalPanel id="modalTodosVinculos" autosized="true" styleClass="novaModal" shadowOpacity="true" width="400" height="150">
        <f:facet name="header">
            <h:panelGroup>
                <h:outputText value="Vínculos de #{TelaClienteControle.cliente.pessoa.nomeAbreviadoMinusculo}" style="text-transform:capitalize;"/>
            </h:panelGroup>
        </f:facet>
        <f:facet name="controls">
            <h:panelGroup>
                <h:outputText
                        styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign "
                        id="hidelinkmodalTodosVinculos"/>
                <rich:componentControl for="modalTodosVinculos"
                                       attachTo="hidelinkmodalTodosVinculos" operation="hide" event="onclick"/>
            </h:panelGroup>
        </f:facet>
        <a4j:form>
            <h:panelGroup>
                <h:dataTable id="vinculos" width="100%" cellpadding="5"
                             rowClasses="texto-size-14" columnClasses="colunaEsquerda"
                             value="#{TelaClienteControle.cliente.vinculoVOs}"
                             var="vinculo">
                    <h:column>
                        <h:outputText styleClass="cinza negrito"
                                      value="#{vinculo.tipoVinculo_Apresentar}" />
                    </h:column>
                    <h:column>
                        <h:outputText styleClass="cinza" style="text-transform: capitalize;"
                                      value="#{vinculo.colaborador.pessoa.nomeAbreviadoMinusculo}" />
                    </h:column>
                </h:dataTable>

            </h:panelGroup>
        </a4j:form>
    </rich:modalPanel>

    <rich:modalPanel id="transferenciaClienteEmpresa" autosized="true" shadowOpacity="true" styleClass="novaModal"
                     showWhenRendered="#{TelaClienteControle.clienteVO.apresentarRichModalErro}" width="450">
        <f:facet name="header">
            <h:outputText value="Transferência de empresa"/>
        </f:facet>
        <a4j:form id="formExisteCliente">
            <jsp:include page="includes/include_persiste_modulo.jsp"/>
            <h:panelGrid rendered="#{TelaClienteControle.diasVigenciaDeContratosAtivos == 0 and TelaClienteControle.cliente.situacao != 'IN'}"
                         columns="1"
                         columnClasses="colunaCentralizada"
                         width="100%">
                <h:outputText styleClass="texto-size-14 texto-font texto-cor-cinza"
                              value="Este cliente não pode ser transferido" />
                <h:outputText styleClass="texto-size-14 texto-font texto-cor-cinza"
                              value="Um cliente só pode ser transferido se ele possuir um contrato ativo a mais de 1 dia." />

                <rich:spacer height="15px"/>
                <a4j:commandLink styleClass="botaoSecundario texto-size-16-real"
                                 oncomplete="Richfaces.hideModalPanel('transferenciaClienteEmpresa')"
                                 value="Fechar"/>
            </h:panelGrid>
            <h:panelGrid rendered="#{TelaClienteControle.diasVigenciaDeContratosAtivos > 0 or TelaClienteControle.cliente.situacao == 'IN'}"
                         columns="1"
                         columnClasses="colunaCentralizada"
                         width="100%">
                <h:outputText value="Escolha a unidade de destino do cliente:" />
                <h:inputText  id="empresaTransferencia"
                              size="50"
                              style="width: 490px;"
                              maxlength="50"
                              styleClass="inputTextClean"
                              value="#{TelaClienteControle.empresaTransferenciaCliente.nome}" />
                <rich:suggestionbox
                        height="200"
                        width="490"
                        for="empresaTransferencia"
                        suggestionAction="#{TelaClienteControle.consultarEmpresaPorNome}"
                        fetchValue="#{empresa.nome}"
                        minChars="0"
                        rowClasses="20"
                        status="statusHora"
                        nothingLabel="Nenhum empresa encontrada com este nome"
                        var="empresa"
                        id="suggestionEmpresaTransferencia">
                    <a4j:support event="onselect"
                                 action="#{TelaClienteControle.selecionarEmpresaTransferencia}"
                                 reRender="panelBotoesControle, mensagem,panelDadosProdutosPeriodo"/>
                    <h:column>
                        <h:outputText styleClass="texto-font texto-size-14-real" value="#{empresa.nome}"/>
                    </h:column>
                </rich:suggestionbox>
                <h:panelGroup rendered="#{TelaClienteControle.permiteTransferirContrato}"
                              layout="block"
                              style="padding-top: 10px;">
                    <div>
                        <h:outputText styleClass="texto-size-14 texto-font texto-cor-cinza"
                                      value="Ao transferir este aluno, o contrato será cancelado na empresa de origem e um novo contrato com a mesma vigência final
                                  será gerado na empresa de destino. Sendo assim, todas as parcelas em aberto do cliente serão transferidas para empresa de destino.
                                  Caso o cliente possua parcela(s) vencida(s) em aberto ele não será transferido.">
                        </h:outputText>
                    </div>
                    <div style="padding-top: 10px;">
                        <h:outputText rendered="#{!TelaClienteControle.permiteTransferirContrato}"
                                      style="color: orangered; padding-top: 10px;"
                                      styleClass="texto-size-14 texto-font texto-cor-cinza"
                                      value="O aluno não pode ser transferido porque o seu plano
                                  (#{TelaClienteControle.contratoBloqueioTransferenciaPorNaoPermitirVendaEmpresa.plano.descricao}) não pode ser vendido nesta unidade.
                              Para realizar esta transferência você precisa fazer o cancelamento manual do contrato atual do aluno e lançar um novo contrato nesta empresa.">
                        </h:outputText>
                    </div>
                </h:panelGroup>
                <h:panelGrid columns="1" width="100%" columnClasses="colunaCentralizada">
                    <h:panelGroup layout="block" styleClass="container-botoes">
                        <a4j:commandLink id="btnTransferirClienteEmpresa"
                                         rendered="#{TelaClienteControle.clienteVO.apresentarBotaoTransferirClienteEmpresa && LoginControle.permissaoAcessoMenuVO.permissaoTransferirClienteEmpresa}"
                                         accesskey="9"
                                         oncomplete="#{TelaClienteControle.mensagemNotificar}"
                                         styleClass="botaoPrimario texto-size-16-real texto-cor-branco"
                                         action="#{TelaClienteControle.transferirClienteEmpresa}">
                            Transferir Cliente <i class="fa-icon-exchange"></i>
                        </a4j:commandLink>

                        <a4j:commandLink id="btnFechar"
                                         reRender="codigo,empresa,panelFoto,nomeCliente,dataNascCliente,categoria,nomeMae,nomePai,sexo,
                                   profissao,grauIntrucao,estadoCivil,cpf,rg,rgOrgao,rgUf,dataCadastro,residencial,comercial,
                                   celular,email,webPage,CEP,endereco,complento,bairro,numero,pais,estado,cidade,estado"
                                         accesskey="5"
                                         styleClass="botaoSecundario texto-size-16-real"
                                         action="#{TelaClienteControle.resetTransferenciaClienteEmpresa}"
                                         oncomplete="Richfaces.hideModalPanel('transferenciaClienteEmpresa')"
                                         value="Cancelar"/>
                    </h:panelGroup>
                    <h:outputText rendered="#{TelaClienteControle.clienteVO.apresentarBotaoTransferirClienteEmpresa && !LoginControle.permissaoAcessoMenuVO.permissaoTransferirClienteEmpresa}"
                                  styleClass="mensagemDetalhada"
                                  value="Usuário sem permissão para transferir aluno: '2.52 - Permissão para transferir cliente de empresa'"/>

                </h:panelGrid>
            </h:panelGrid>
        </a4j:form>
    </rich:modalPanel>

    <rich:modalPanel id="mudancaPlano"
                     autosized="true"
                     shadowOpacity="true"
                         styleClass="novaModal"
                         showWhenRendered="#{TelaClienteControle.clienteVO.apresentarRichModalErro}"
                     width="450">
        <f:facet name="header">
            <h:outputText value="Mudança de plano"/>
        </f:facet>
        <a4j:form id="formMudancaPlano">
            <jsp:include page="includes/include_persiste_modulo.jsp"/>
            <h:panelGrid rendered="#{TelaClienteControle.apresentaMudancaPlano}"
                         columns="1"
                         width="100%">
                <h:outputText value="Escolha o novo plano:" />
                <h:inputText  id="inputPlanoMudanca"
                              size="50"
                              style="width: 490px;"
                              maxlength="50"
                              onfocus="scrollLock();"
                              styleClass="inputTextClean"
                              value="#{TelaClienteControle.planoParaTransferencia.descricao}"/>
                <h:outputText value="* Mostra apenas planos recorrentes e sem turma." />
                <h:outputText value="* O plano escolhido deve ser compatível com o plano atual." />
                <rich:suggestionbox
                        height="200"
                        width="590"
                        for="inputPlanoMudanca"
                        suggestionAction="#{TelaClienteControle.consultarPlanoParaTransferencia}"
                        fetchValue="#{plano.descricao}"
                        minChars="1"
                        rowClasses="20"
                        status="statusInComponent"
                        immediate="true"
                        nothingLabel="Nenhum plano encontrado com esta descrição"
                        var="plano">
                    <a4j:support event="onselect"
                                 oncomplete="#{TelaClienteControle.mensagemNotificar}"
                                 action="#{TelaClienteControle.selecionarPlanoParaTransferencia}"
                                 reRender="formMudancaPlano"/>
                    <h:column>
                        <h:outputText styleClass="texto-font texto-size-14-real" value="#{plano.descricao}"/>
                        <h:panelGroup style="width: 100%;">
                            <h:outputText rendered="#{TelaClienteControle.contratoParaTransferenciaDePlano.plano.codigo eq plano.codigo}"
                                          styleClass="texto-font texto-size-14-real texto-bold"
                                          style="float: right; margin-right: 5px;"
                                          value="Plano atual"/>
                        </h:panelGroup>
                    </h:column>
                </rich:suggestionbox>
            </h:panelGrid>

            <h:panelGroup rendered="#{!TelaClienteControle.apresentaMudancaPlano}">
                <h:panelGroup rendered="#{!TelaClienteControle.contratoVigenteMudancaPlano}">
                    <div style="text-align: center; margin-top: 10px;">
                        <span class="texto-size-18 texto-font texto-cor-cinza">
                            Este contrato não pode ter o plano alterado<br/>
                            Um cliente só pode realizar a mudança de plano se o contrato atual estiver ativo há mais de 1 dia.
                        </span>
                    </div>
                </h:panelGroup>
                <h:panelGroup rendered="#{TelaClienteControle.contratoRenovadoMudancaPlano}">
                    <div style="text-align: center; margin-top: 10px;">
                        <span class="texto-size-18 texto-font texto-cor-cinza">
                            Este contrato não pode ter o plano alterado<br/>
                            Contrato já foi renovado, portanto não pode ter seu plano alterado.
                        </span>
                    </div>
                </h:panelGroup>
            </h:panelGroup>

            <h:panelGroup rendered="#{TelaClienteControle.planoSelecionadoParaTransferenciaIgualAtual}">
                <div style="text-align: center; margin-top: 10px;">
                    <span class="col-md-12 texto-size-20 texto-cor-cinza texto-font texto-bold">
                       O aluno já possui o plano selecionado.
                    </span>
                        <span class="col-md-12 texto-size-14 texto-cor-cinza texto-font">
                        Para realizar a transferência de plano você precisa selecionar outro plano.
                    </span>
                </div>
            </h:panelGroup>

            <!-- INFOBOX COM DESCRIÇAO DO PLANO ATUAL / NOVO PLANO -->
            <h:panelGroup rendered="#{TelaClienteControle.novoContratoParaTransferenciaDePlano != null and !TelaClienteControle.planoSelecionadoParaTransferenciaIgualAtual}">
                <div style="margin-top: 10px; margin-bottom: 10px; padding: 8px; background-color: #E5F6FD; display: flex; flex-direction: column; gap: 10px; border-radius: 8px">
                    <div style="display: flex; gap: 10px;">
                        <span style="font-weight: 700" class="texto-size-18">Plano atual:</span>
                        <span class="texto-size-18">
                            <h:outputText value="#{TelaClienteControle.contratoParaTransferenciaDePlano.plano.descricao} (R$ #{TelaClienteControle.contratoParaTransferenciaDePlano.valorFinal})"/>
                        </span>
                    </div>
                    <div style="display: flex; gap: 10px;">
                        <span style="font-weight: 700" class="texto-size-18">Novo plano:</span>
                        <span class="texto-size-18">
                            <h:outputText value="#{TelaClienteControle.planoParaTransferencia.descricao}"/>
                        </span>
                    </div>
                </div>
            </h:panelGroup>

            <h:panelGrid rendered="#{TelaClienteControle.novoContratoParaTransferenciaDePlano != null and !TelaClienteControle.planoSelecionadoParaTransferenciaIgualAtual}"
                          id="mudancaPlanoDadosNovoPlano" columns="3"
                          style="margin-top: 10px">

                <rich:spacer width="5px"/>
                <h:panelGroup>
                    <span class=" texto-size-16 texto-cor-cinza texto-font texto-bold" style="text-align: center;">
                            Manutenção (anual):
                    </span>
                </h:panelGroup>
                <h:panelGroup styleClass="texto-size-16 texto-cor-cinza-2 mtop20 texto-font texto-bold">
                    <h:outputText value="#{MovPagamentoControle.empresaLogado.moeda} #{TelaClienteControle.novoContratoParaTransferenciaDePlano.valorPorAnuidadeApresentar}"/>
                </h:panelGroup>
                <rich:spacer width="5px"/>

                <h:panelGroup>
                    <span class=" texto-size-16 texto-cor-cinza texto-font texto-bold" >
                        Plano:
                    </span>
                </h:panelGroup>
                <h:panelGroup styleClass="texto-size-16 texto-cor-cinza-2 mtop20 texto-font texto-bold">
                    <h:outputText value="#{MovPagamentoControle.empresaLogado.moeda} #{TelaClienteControle.novoContratoParaTransferenciaDePlano.valorContratoNovo}"/>
                </h:panelGroup>
                <rich:spacer width="5px"/>

                <h:panelGroup>
                    <span class=" texto-size-16 texto-cor-cinza texto-font texto-bold" >
                        Valor Final:
                    </span>
                </h:panelGroup>
                <h:panelGroup styleClass="texto-size-16 texto-cor-cinza-2 mtop20 texto-font texto-bold">
                    <h:outputText value="#{MovPagamentoControle.empresaLogado.moeda} #{TelaClienteControle.valorFinalContrato}"/>
                </h:panelGroup>
                <rich:spacer width="5px"/>

                <h:panelGroup>
                    <span class=" texto-size-16 texto-cor-cinza texto-font texto-bold" >
                            Mensalidade:
                        </span>
                </h:panelGroup>
                <h:panelGroup styleClass="texto-size-16 texto-cor-cinza-2 mtop20 texto-font texto-bold">
                    <h:outputText value="#{MovPagamentoControle.empresaLogado.moeda} #{TelaClienteControle.novoContratoParaTransferenciaDePlano.valorContratoReferenteMensalApresentar}"/>
                </h:panelGroup>
                <rich:spacer width="5px"/>

                <h:panelGroup>
                    <span class=" texto-size-16 texto-cor-cinza texto-font texto-bold" >
                            Data Inicio:
                    </span>
                </h:panelGroup>
                <h:panelGroup styleClass="texto-size-16 texto-cor-cinza-2 mtop20 texto-font texto-bold">
                    <h:outputText value="#{TelaClienteControle.novoContratoParaTransferenciaDePlano.vigenciaDe_ApresentarTela7}"/>
                </h:panelGroup>
                <rich:spacer width="5px"/>

                <h:panelGroup>
                    <span class=" texto-size-16 texto-cor-cinza texto-font texto-bold " >
                            Data Fim:
                    </span>
                </h:panelGroup>
                <h:panelGroup styleClass=" texto-size-16 texto-cor-cinza-2 mtop20 mlef20 texto-font texto-bold" >
                    <h:outputText value="#{TelaClienteControle.dataFinalContrato}"/>
                </h:panelGroup>
                <rich:spacer width="5px"/>

                <div class="col-md-12 title-space mtop20" style="text-align: center;">
                    <span class="texto-size-18 texto-cor-cinza-2 texto-font texto-bold  mtop20" >
                                    <h:panelGroup
                                                  layout="block"
                                                  style="">
                                        <h:panelGroup
                                                      id="diaVencimentoContratoRecorrencia"
                                                      style=""
                                                      layout="block">
                                            <h:outputText value=" Dia de cobrança das parcelas: #{TelaClienteControle.diaVencimentoCartaoTransferenciaDePlano}"
                                                          styleClass="texto-size-14 cinza" style=""/>
                                        </h:panelGroup>

                                    </h:panelGroup>


                        <h:outputText rendered="#{TelaClienteControle.contratoParaTransferenciaDePlano.diaPrimeiraParcela == null}"
                                      value=" #{TelaClienteControle.contratoParaTransferenciaDePlano.diaLancamento}"/>
                    </span>

                </div>
            </h:panelGrid>
            <h:panelGroup rendered="#{TelaClienteControle.novoContratoParaTransferenciaDePlano != null and !TelaClienteControle.planoSelecionadoParaTransferenciaIgualAtual}" layout="block"
                          style="text-align: center; margin-top: 10px;">
                <span class="texto-size-18 texto-cor-cinza texto-font">
                    Ao realizar a transferência de plano, as próximas parcelas em aberto serão atualizadas com os novos valores acima.</br>
                </span>
                <span class="texto-size-18 texto-cor-cinza texto-font">
                    O horario será o mesmo do antigo contrato.
                </span>
            </h:panelGroup>
            <!-- MSG ALERTA UPGRADE --->
            <h:panelGroup id="msg-alerta-upgrade"
                          rendered="#{TelaClienteControle.contratoParaTransferenciaDePlano.empresa.aplicarMultaMudancaPlano and TelaClienteControle.contratoParaTransferenciaDePlano.valorFinal < TelaClienteControle.novoContratoParaTransferenciaDePlano.valorFinal}" layout="block"
                          style="text-align: center; margin-top: 5px; background-color: #EDF7ED; padding: 10px; border-radius: 5px;">
                <h:outputText style="margin-bottom: 5px; font-size: 20px; font-weight: 700; color: #408944" value="Multa: R$ 0,00"/>
                <div style="display: flex; flex-direction: column; width: 100%; justify-content: center; align-items: center;">
                       <span class="texto-size-18 texto-font" style="color: #408944">
                           <i class="fa-icon-check texto-size-22"></i> Tudo certo. Por ser um <span style="font-weight: 700">upgrade de plano</span>, não há multas incidentes nesta alteração.
                </span>
                </div>
            </h:panelGroup>
            <!-- MSG ALERTA DOWNGRADE --->
            <h:panelGroup id="msg-alerta-downgrade"
                          rendered="#{TelaClienteControle.contratoParaTransferenciaDePlano.empresa.aplicarMultaMudancaPlano and TelaClienteControle.contratoParaTransferenciaDePlano.valorFinal > TelaClienteControle.novoContratoParaTransferenciaDePlano.valorFinal}" layout="block"
                          style="text-align: center; margin-top: 5px; background-color: #FDEDED; padding: 10px; border-radius: 5px;">
                <h:outputText style="font-size: 20px; font-weight: 700; color: #FF5555" value="Multa: R$ #{TelaClienteControle.calcularValorMultaMudancaPlano()}"/>
                <div style="display: flex; flex-direction: column; width: 100%; justify-content: center; align-items: center;">
                       <span class="texto-size-18 texto-cor-vermelho texto-font">
                    <i class="fa-icon-warning-sign texto-size-22 texto-cor-vermelho"></i> <span style="font-weight: 700"></span>Em razão da alteração para um plano com valor inferior, incidirá uma multa correspondente a <h:outputText value="#{TelaClienteControle.contratoParaTransferenciaDePlano.plano.percentualMultaCancelamento}%"/> do valor total do contrato original.
                </span>
                </div>
            </h:panelGroup>
            <h:panelGroup rendered="#{TelaClienteControle.novoContratoParaTransferenciaDePlano != null and !TelaClienteControle.planoSelecionadoParaTransferenciaIgualAtual}" layout="block"
                          style="text-align: center; margin-top: 5px; margin-bottom: 5px; background-color: #FDEDED; padding: 10px; border-radius: 5px;">
                <span class="texto-size-18 texto-cor-vermelho texto-font">
                  <i class="fa-icon-warning-sign texto-size-22 texto-cor-vermelho"></i><span style="font-weight: 700"> Atenção:</span> Esta operação não poderá ser desfeita.<br/> Esta mudança não pode ser realizada duas vezes em um mesmo dia.
                </span>
            </h:panelGroup>
            <h:panelGrid columns="1"
                         width="100%"
                         columnClasses="colunaCentralizada">
                <h:panelGroup layout="block" styleClass="container-botoes">
                    <a4j:commandLink rendered="#{TelaClienteControle.novoContratoParaTransferenciaDePlano != null and !TelaClienteControle.planoSelecionadoParaTransferenciaIgualAtual}"
                                     accesskey="9"
                                     oncomplete="#{TelaClienteControle.mensagemNotificar}"
                                     styleClass="botaoPrimario texto-size-16-real texto-cor-branco"
                                     action="#{TelaClienteControle.transferirPlanoDoCliente}">
                        Confirmar alteração do plano <i class="fa-icon-exchange"></i>
                    </a4j:commandLink>

                    <a4j:commandLink accesskey="5"
                                     styleClass="botaoSecundario texto-size-16-real"
                                     action="#{TelaClienteControle.resetarDadosTansferenciaPlano}"
                                     oncomplete="Richfaces.hideModalPanel('mudancaPlano')"
                                     value="Cancelar"/>
                </h:panelGroup>
                <h:outputText rendered="#{!TelaClienteControle.permiteTransferirPlano}"
                              styleClass="mensagemDetalhada"
                              value="Usuário sem permissão para realizar a mudança de plano: '2.52 - Permissão para alterar o plano do cliente'"/>

            </h:panelGrid>
        </a4j:form>
    </rich:modalPanel>

    <rich:modalPanel id="mudancaDiaVencimentoParcela"
                     autosized="true"
                     shadowOpacity="true"
                     styleClass="novaModal"
                     showWhenRendered="#{TelaClienteControle.clienteVO.apresentarRichModalErro}"
                     width="450">
        <f:facet name="header">
            <h:outputText value="Mudança no dia de vencimento da parcela"/>
        </f:facet>
        <a4j:form>
            <jsp:include page="includes/include_persiste_modulo.jsp"/>
            <h:panelGrid columns="1"
                         columnClasses="colunaCentralizada alignCenter"
                         width="100%">
                <h:panelGroup layout="block" styleClass="col-md-12 title-space mtop20">
                    <span class="texto-size-14 texto-cor-cinza texto-font texto-bold">Escolha o novo dia de cobrança das parcelas:</span>
                </h:panelGroup>
                <h:panelGroup layout="block"
                              styleClass="col-md-2 cb-container margenVertical">
                    <h:selectOneMenu value="#{TelaClienteControle.diaVencimentoParcelasAlterado}">
                        <f:selectItems value="#{TelaClienteControle.listaDiaVencimentoParcelas}"/>
                        <a4j:support reRender="mudancaDiaVencimentoParcelaBotoes, descricaoMudancaDiaVencimentoParcela"
                                     action="#{TelaClienteControle.calcularProRataDeAlteracaoVencimentoParcelas}"
                                     event="onchange"/>
                    </h:selectOneMenu>
                </h:panelGroup>

                <h:panelGroup layout="block"
                              id="descricaoMudancaDiaVencimentoParcela"
                              style="padding-top: 10px;">

                    <h:panelGroup layout="block" style="padding-bottom: 15px"
                                  rendered="#{TelaClienteControle.permiteLiberarCobrancaDataVencimentoParcelas && TelaClienteControle.valorProRataAlteracaoDataVencimentoParcelas > 0.0}">
                        <h:selectBooleanCheckbox value="#{TelaClienteControle.liberarValorProRataVencimentoParcelas}">
                            <a4j:support
                                    reRender="mudancaDiaVencimentoParcelaBotoes, descricaoMudancaDiaVencimentoParcela"
                                    action="#{TelaClienteControle.calcularProRataDeAlteracaoVencimentoParcelas}"
                                    event="onchange"/>
                        </h:selectBooleanCheckbox>
                        <h:outputText value="Liberar cobrança de pró-rata"
                                      style="padding-left: 5px"
                                      styleClass="texto-size-14 texto-font texto-cor-cinza"/>
                    </h:panelGroup>

                    <h:panelGroup
                            rendered="#{TelaClienteControle.valorProRataAlteracaoDataVencimentoParcelas > 0.0 && !TelaClienteControle.liberarValorProRataVencimentoParcelas}"
                            layout="block">
                        <span class="texto-size-14 texto-cor-cinza texto-font">
                            Será gerado uma parcela de <strong>Pro Rata</strong><br>para o dia
                        </span>
                        <h:outputText value="#{TelaClienteControle.diaVencimentoParcelasAlterado}"
                                      styleClass="texto-size-14 texto-font texto-cor-cinza texto-bold"/>
                        <span class="texto-size-14 texto-cor-cinza texto-font">no valor de </span>
                        <h:outputText styleClass="texto-size-14 texto-font texto-cor-cinza texto-bold"
                                      id="valorProrataaaaa"
                                      value="#{TelaClienteControle.valorProRataAlteracaoDataVencimentoParcelas_Apresentar} ">
                            <f:convertNumber type="number" minFractionDigits="2" pattern="#0.00"
                                             currencySymbol="#{TelaClienteControle.empresa.moeda}"/>
                        </h:outputText>
                        <span class="texto-size-14 texto-cor-cinza texto-font">
                            referente a
                        </span> <h:outputText value="#{TelaClienteControle.diferencaDiasNovoVencimento} dias"
                                              styleClass="texto-size-14 texto-font texto-cor-cinza texto-bold"/>
                    </h:panelGroup>

                    <h:panelGroup rendered="#{TelaClienteControle.valorProRataAlteracaoDataVencimentoParcelas == 0.0 || TelaClienteControle.liberarValorProRataVencimentoParcelas}"
                                  layout="block">
                        <h:outputText styleClass="texto-size-14 texto-font texto-cor-cinza"
                                      value="A data de vencimento das parcelas será alterada e nenhuma parcela de Pro Rata será cobrada. " />
                    </h:panelGroup>

                    <h:panelGroup rendered="#{TelaClienteControle.cliente.empresa.toleranciaProrata >= TelaClienteControle.diferencaDiasNovoVencimento}"
                                  layout="block">
                        <h:outputText
                                styleClass="texto-size-14 texto-font texto-cor-cinza"
                                value="Não será gerado nenhum valor de pro-rata pois a configuração da empresa está configurada para tolerar os dias em questão." />
                    </h:panelGroup>

                </h:panelGroup>
                <h:panelGrid id="mudancaDiaVencimentoParcelaBotoes"
                             columns="1"
                             width="100%"
                             columnClasses="colunaCentralizada">
                    <h:panelGroup layout="block" styleClass="container-botoes">
                        <a4j:commandLink accesskey="9"
                                         rendered="#{TelaClienteControle.permiteAlterarDataVencimentoParcelas}"
                                         oncomplete="#{TelaClienteControle.mensagemNotificar};Richfaces.hideModalPanel('mudancaDiaVencimentoParcela');document.getElementById('form:btnClienteFinanceiro').click();"
                                         styleClass="botaoPrimario texto-size-16-real texto-cor-branco"
                                         reRender="panelRecarregar"
                                         action="#{TelaClienteControle.alterarDataVencimentoParcela}" >
                            Confirmar <i class="fa-icon-exchange"></i>
                        </a4j:commandLink>

                        <a4j:commandLink reRender="codigo,empresa,panelFoto,nomeCliente,dataNascCliente,categoria,nomeMae,nomePai,sexo,
                                   profissao,grauIntrucao,estadoCivil,cpf,rg,rgOrgao,rgUf,dataCadastro,residencial,comercial,
                                   celular,email,webPage,CEP,endereco,complento,bairro,numero,pais,estado,cidade,estado,diaVencimentoContratoRecorrencia,idListaCompras"
                                         accesskey="5"
                                         styleClass="botaoSecundario texto-size-16-real"
                                         oncomplete="Richfaces.hideModalPanel('mudancaDiaVencimentoParcela')"
                                         value="Cancelar"/>
                    </h:panelGroup>
                </h:panelGrid>
            </h:panelGrid>
        </a4j:form>
    </rich:modalPanel>

    <rich:modalPanel id="modalRemoverParcelaBoleto" domElementAttachment="parent"
                     autosized="true" shadowOpacity="false" width="500"
                     styleClass="novaModal">

        <f:facet name="header">
            <h:panelGroup>
                <h:outputText value="Remover Parcela - Remessa Boleto"/>
            </h:panelGroup>
        </f:facet>
        <f:facet name="controls">
            <h:panelGroup>
                <h:outputText
                        styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign "
                        id="hidelinkModalRemoverParcelaBoleto"/>
                <rich:componentControl for="modalRemoverParcelaBoleto" attachTo="hidelinkModalRemoverParcelaBoleto"
                                       operation="hide" event="onclick">
                </rich:componentControl>
            </h:panelGroup>
        </f:facet>

        <a4j:form id="formModalRemoverParcelaBoleto" ajaxSubmit="true">
            <h:panelGroup id="panelModalRemoverParcelaBoleto"
                          layout="block" style="padding: 20px">

                <h:panelGroup layout="block" id="panelTitleRemoverBoleto"
                              style="padding-top: 10px;padding-bottom: 10px;">
                    <h:outputText value="#{TelaClienteControle.textoRemoverItemRemessa}"
                                  styleClass="texto-size-20 cinza"/>
                </h:panelGroup>

                <h:panelGroup layout="block" id="panelBtnModalRemoverParcelaBoleto"
                              style="text-align: center; padding: 25px 0 20px 0;">
                    <a4j:commandLink id="confirmarSincronizarTransacao"
                                     reRender="form:tabelaTransacoes"
                                     value="Confirmar"
                                     action="#{TelaClienteControle.confirmarRemoverParcelaBoleto}"
                                     oncomplete="#{TelaClienteControle.mensagemNotificar};#{TelaClienteControle.onCompleteGenerico}"
                                     styleClass="botaoPrimario texto-size-16"/>
                </h:panelGroup>
            </h:panelGroup>
        </a4j:form>
    </rich:modalPanel>

    <script>
        carregarTooltipster();

        window.onload = function () {
            ${FuncionalidadeControle.abrirPopTelaCliente}
        };
    </script>

    <jsp:include page="include_modal/aulasDesmarcadas.jsp" flush="false"/>
    <jsp:include page="include_modal/modalControleCreditoTreino.jsp" flush="false"/>
    <jsp:include page="include_modal/modalConvidado.jsp" flush="false"/>
    <jsp:include page="include_modal/modalConvidadoHistorico.jsp" flush="false"/>
    <jsp:include page="include_modal/modalDesfazerCancelamento.jsp" flush="false"/>
    <jsp:include page="include_modal/modalEnviarNotaEmail.jsp" flush="false"/>
    <jsp:include page="include_modal/modalFaltasAluno.jsp" flush="false"/>
    <jsp:include page="include_modal/modalFiltroCredito.jsp" flush="false"/>
    <jsp:include page="include_modal/modalGymPass.jsp" flush="false"/>
    <jsp:include page="include_modal/modalTotalPassHistorico.jsp" flush="false"/>
    <jsp:include page="include_modal/modalGymPassHistorico.jsp" flush="false"/>
    <jsp:include page="include_modal/modalTotalPass.jsp" flush="false"/>
    <jsp:include page="include_modal/modalPanelContratoArmario.jsp" flush="false"/>
    <jsp:include page="include_modal/modalRemoverObjecaoDefinitiva.jsp" flush="false"/>
    <c:if test="${LoginControle.apresentarLinkEstudio}">
        <jsp:include page="include_modal/panelAgendaAluno.jsp" flush="false"/>
        <jsp:include page="include_modal/modaisClienteEstudio.jsp" flush="false"/>
    </c:if>
    <jsp:include page="include_modal/panelAlterarMatricula.jsp" flush="false"/>
    <jsp:include page="include_modal/panelAulaDesmarcada.jsp" flush="false"/>
    <jsp:include page="include_modal/panelAutorizarEdicaoPagamento.jsp" flush="false"/>
    <jsp:include page="include_modal/panelContratoOperacao.jsp" flush="false"/>
    <jsp:include page="include_modal/panelHistoricoContrato.jsp" flush="false"/>
    <jsp:include page="include_modal/panelIncludeContratoPrestacao.jsp" flush="false"/>
    <jsp:include page="include_modal/panelParcelasRemessas.jsp" flush="false"/>
    <jsp:include page="include_modal/panelStatusTrocaCartao.jsp" flush="false"/>
    <jsp:include page="include_modal/panelUsuarioSenhaObservacaoGeral.jsp" flush="false"/>
    <jsp:include page="include_modal/panelTransferirContrato.jsp" flush="false"/>
    <jsp:include page="include_modal/panelAfastamentoContratoDependente.jsp" flush="false"/>
    <jsp:include page="includes/cliente/include_modal_editar_vigencia_final_produto_cliente.jsp" flush="false"/>
    <jsp:include page="includes/cliente/include_modal_escolher_produto_renovar_cliente.jsp" flush="false"/>
    <jsp:include page="includes/cliente/include_modal_reposicoes.jsp" flush="false"/>
    <jsp:include page="include_modal_visualizarDocs.jsp" flush="false"/>
    <h:panelGroup id="panelIncludeMensagem" layout="block">
        <jsp:include page="include_mensagem_cliente.jsp" flush="false"/>
    </h:panelGroup>
    <jsp:include page="includes/cliente/include_modal_capfoto_html5.jsp" flush="false"/>
    <jsp:include page="includes/autorizacao/include_autorizacao_funcionalidade_nova.jsp" flush="false"/>
    <jsp:include page="includes/parcelas/include_modal_alterarVencimentoParcelas.jsp" flush="false"/>
    <jsp:include page="includes/include_modal_mensagem_generica.jsp" flush="false"/>
    <jsp:include page="includes/include_envio_contrato_email.jsp" flush="false"/>
    <jsp:include page="includes/include_envio_orcamento_email.jsp" flush="false"/>
    <jsp:include page="includes/include_modal_usuarioSenhaTipoContrato.jsp" flush="false"/>

    <jsp:include page="includes/include_panelMensagem_goBackBlock.jsp" flush="false"/>

    <jsp:include page="include_modal_historico_aulas.jsp"/>
    <jsp:include page="include_modal_novo_membro_familia.jsp"/>
    <jsp:include page="include_compartilharLinkPagamento.jsp"/>
    <jsp:include page="includes/remessas/include_itens_paginado_gerarrecibo.jsp"/>
    <jsp:include page="include_modal/modais_BloqueioCobranca.jsp"/>
    <jsp:include page="includes/transacoes/include_paramspanel_transacao.jsp"/>
    <jsp:include page="includes/transacoes/include_paramspanel_pix.jsp"/>
    <jsp:include page="includes/transacoes/include_paramspanel_getcard.jsp"/>
    <jsp:include page="includes/cliente/include_modal_utilizacoes_avaliacaofisica.jsp"/>
    <jsp:include page="includes/include_modal_boleto.jsp"/>
    <jsp:include page="include_modal/modalCarteirinhaHistorico.jsp" flush="false"/>

</f:view>
</body>



<%@taglib prefix="f" uri="http://java.sun.com/jsf/core" %>


<head>
    <link rel="icon" type="image/png" href=".${LoginControle.favIconModule}"/>
    <link href="css/pacto_flat_2.16.min.css" rel="stylesheet" type="text/css"/>
    <link href="css/gestaoRecebiveis1.0.min.css" rel="stylesheet" type="text/css">
    <link href="css/linhaTempoContrato_1.0.min.css" rel="stylesheet" type="text/css">

    <script src="script/all_script_3.4.min.js" type="text/javascript" ></script>

    <script type="text/javascript" language="javascript" src="script/gobackblock.js"></script>
    <link href="css/otimizeOt.min.css" rel="stylesheet" type="text/css">
    <script type="text/javascript" src="script/jquery.maskedinput-1.7.6.js"></script>
</head>
<script type="text/javascript">
    setTimeout(function () {
        setDocumentCookie('popupsImportante', '', 1);
    }, 500);

    function trocarBloco(bloco, container, tipoBotao, sumirLinha) {
        jQuery(container + '.visivel').slideUp();
        jQuery(container + bloco).slideDown();
        jQuery(container + bloco).addClass('visivel');
        jQuery(tipoBotao).removeClass('ativo');
        jQuery(tipoBotao + bloco).addClass('ativo');
        if (sumirLinha) {
            jQuery('.containerLinhaTempoContrato').hide();
            jQuery('.informacaoMatriculaAluno').hide();
        } else {
            jQuery('.containerLinhaTempoContrato').show();
            jQuery('.informacaoMatriculaAluno').show();
        }
    }
    function baixarPainelMarcarAluno() {
        jQuery('.painelMarcarAluno').slideDown('slow');
    }
    function sumirPainelMarcarAluno() {
        jQuery('.painelMarcarAluno').slideUp();
    }
    function atualizarFotos(){
        document.getElementById('form:btnAtualizaFotosCliente').click();

    }
    function carregarTooltipsterContrato() {
        carregarTooltipContrato(jQuery('.tooltipster'));
    }
    function carregarTooltipContrato(el) {
        el.tooltipster({
            theme: 'tooltipster-light',
            position: 'bottom',
            animation: 'grow',
            contentAsHTML: true
        });
    }

    function scrollLock(){
        console.log( 'lock');
        jQuery('body').css('overflow', 'hidden');
    }

    function scrollRelease(){
        console.log( 'release');
        jQuery('body').css('overflow', 'auto');
    }

    const novaversao_second = 1000;
    const novaversao_minute = novaversao_second * 60;
    const novaversao_hour = novaversao_minute * 60;
    const novaversao_day = novaversao_hour * 24;
    let novaversao_dataDesativar = null;

    let novaversao_count_down = null;
    let novaversao_x = null;

    async function obterDataDesativarNovaTelaAluno() {
        if (novaversao_dataDesativar == null) {
            var url = '${SuperControle.urloamdrecursomigracao}/prest/migracao-recurso/data-migracao/${LoginControle.key}/TELA_ALUNO';
            const response =  fetch(url, {
                method: "GET",
                headers: {
                    'Content-Type': 'application/json'
                }
            });
            const data = (await response).json();
            return data;
        }
    }

    function countDownDesativarNovaTelaAluno() {
        let now = new Date(Date.now()).getTime();
        let diff = novaversao_count_down - now;
        if (diff < 0) {
            diff = 0;
        }
        document.getElementById('form:qtdDiasNovaVersao').innerText = Math.floor(diff / novaversao_day);
        document.getElementById('form:qtdHorasNovaVersao').innerText = Math.floor(diff % novaversao_day / novaversao_hour);
        document.getElementById('form:qtdMinutosNovaVersao').innerText = Math.floor(diff % novaversao_hour / novaversao_minute);
        document.getElementById('form:qtdSegundosNovaVersao').innerText = Math.floor(diff % novaversao_minute / novaversao_second);
    }

    function iniciarCountDownDesativarNovaTelaAluno() {
        try {
            if (novaversao_x) {
                clearInterval(novaversao_x);
            }
            obterDataDesativarNovaTelaAluno().then((resultado) => {
                if (resultado.content) {
                    novaversao_dataDesativar = resultado.content;
                    const dataNova = new Date(resultado.content);
                    document.getElementById('form:novaVersaoTelaAlunoData').innerText = dataNova.toLocaleDateString();
                    novaversao_count_down = dataNova;
                    novaversao_x = setInterval(() => countDownDesativarNovaTelaAluno(), novaversao_second);
                    const divContadorNovaVersaoTelaAluno = document.getElementById('form:divContadorNovaVersaoTelaAluno');
                    if (divContadorNovaVersaoTelaAluno) {
                        divContadorNovaVersaoTelaAluno.style.display = 'block';
                    }
                    const divMsgExperimenteNovaVersao = document.getElementById('form:divMsgExperimenteNovaVersao');
                    if (divMsgExperimenteNovaVersao) {
                        divMsgExperimenteNovaVersao.style.display = 'none';
                    }
                }
            })
        } catch (e) {
            console.log(e);
        }
    }
</script>
<c:if test="${SuperControle.ativarGoogleAnalytics}">
    <script type="text/javascript" src="${root}/script/googleAnalytics_v1.0.js"></script>
</c:if>
<c:if test="${SuperControle.menuZwUi}">
    <script>
        document.addEventListener('DOMContentLoaded', function () {
            iniciarCountDownDesativarNovaTelaAluno();
            fecharMenu();
            try {
                verificarAbrirNovaTelaCliente();
            } catch (e) {
                console.log(e);
            }
        });
    </script>
</c:if>
<script >
    moduloAtivoAtual = '${LoginControle.moduloAberto}';
</script>
