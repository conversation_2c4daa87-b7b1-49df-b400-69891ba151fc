<%@page contentType="text/html"%>
<%@page pageEncoding="ISO-8859-1"%>
<head><%@include file="/includes/include_import_minifiles.jsp" %></head>
<link href="./css/otimize.css" rel="stylesheet" type="text/css">
<%@taglib prefix="f" uri="http://java.sun.com/jsf/core"%>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html"%>
<script type="text/javascript" language="javascript" src="hoverform.js"></script>
<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax"%>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich"%>
<f:loadBundle var="msg_bt" basename="propriedades.Botoes"/>
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao"/>
<f:loadBundle var="msg" basename="propriedades.Mensagens"/>

<f:view>
    <jsp:include page="includes/include_carregando_ripple.jsp" flush="true"/>
    <a4j:loadScript src="/script/jquery.maskedinput-1.2.2.js" />
    <title>
        <h:outputText value="#{msg_aplic.prt_Colaborador_tituloForm}"/>
    </title>
    <rich:modalPanel id="panelExistePessoa" autosized="true" shadowOpacity="true" showWhenRendered="#{ColaboradorControle.pessoaVO.apresentarRichModalErro}" width="450" height="150">
        <f:facet name="header">
            <h:panelGroup>
                <h:outputText value="Atenção!"></h:outputText>
            </h:panelGroup>
        </f:facet>
        <a4j:form id="formExistePessoa" ajaxSubmit="true">
            <h:panelGrid columns="1" width="100%" columnClasses="colunaCentralizada" >
                <h:panelGrid columns="1" footerClass="colunaCentralizada" width="100%">
                    <h:outputText styleClass="tituloCampos" value="#{msg.msg_existePessoa}"/>
                </h:panelGrid>

                <h:panelGrid columns="2" width="20%" headerClass="subordinado" columnClasses="colunaCentralizada">
                    <a4j:commandButton action="#{ColaboradorControle.adicionarPessoa}" reRender="form" oncomplete="Richfaces.hideModalPanel('panelExistePessoa')" value="#{msg_bt.btn_sim}" image="./imagens/botaoSim.png" accesskey="5" styleClass="botaoEspecial"/>
                    <a4j:commandButton action="#{ColaboradorControle.setarFalso}"reRender="form" onclick="Richfaces.hideModalPanel('panelExistePessoa')"  value="#{msg_bt.btn_nao}" image="./imagens/botaoNao.png"accesskey="6" styleClass="botaoEspecial"/>
                </h:panelGrid>
            </h:panelGrid>
        </a4j:form>
    </rich:modalPanel>

    <rich:modalPanel id="panelExisteColaborador" autosized="true" shadowOpacity="true" showWhenRendered="#{ColaboradorControle.colaboradorVO.apresentarRichModalErro}" width="450" height="150">
        <f:facet name="header">
            <h:panelGroup>
                <h:outputText value="Atenção!"></h:outputText>
            </h:panelGroup>
        </f:facet>
        <a4j:form id="formExisteColaborador" ajaxSubmit="true">
            <h:panelGrid columns="1" width="100%" columnClasses="colunaCentralizada">
                <h:panelGrid columns="1" footerClass="colunaCentralizada" width="100%">
                    <h:outputText styleClass="tituloCampos" value="#{ColaboradorControle.colaboradorVO.msgErroExisteColaborador}"/>
                    <h:panelGrid columns="2" width="20%" headerClass="subordinado" columnClasses="colunaCentralizada">
                        <a4j:commandButton  rendered="#{ColaboradorControle.colaboradorVO.apresentarBotaoTransferirColaboradorEmpresa}" action="#{ColaboradorControle.gravarColaboradorTrocandoEmpresa}" reRender="form" oncomplete="Richfaces.hideModalPanel('panelExisteCliente')" image="./imagens/botaoTransferirColaboradorParaEstaEmpresa.png"/>
                        <a4j:commandButton  rendered="#{!ClienteControle.clienteVO.apresentarBotaoTransferirClienteEmpresa}"action="#{ColaboradorControle.editarColaborador}" reRender="form" oncomplete="Richfaces.hideModalPanel('panelExisteCliente')" image="./imagens/botaoEditarDadosDoCliente.png"/>
                        <a4j:commandButton reRender="form" oncomplete="Richfaces.hideModalPanel('panelExisteColaborador')" value="Fechar" image="./imagens/botaoFechar.png" accesskey="5" styleClass="botaoEspecial"/>

                    </h:panelGrid>
                </h:panelGrid>


                <h:panelGrid columns="1" width="20%" headerClass="subordinado" columnClasses="colunaCentralizada">
                    <a4j:commandButton reRender="form" oncomplete="Richfaces.hideModalPanel('panelExisteColaborador')" value="Fechar" image="./imagens/botaoFechar.png" accesskey="5" styleClass="botaoEspecial"/>
                </h:panelGrid>
            </h:panelGrid>
        </a4j:form>
    </rich:modalPanel>

    <rich:modalPanel id="panelProfissao" autosized="true" shadowOpacity="true" width="450" height="250">
        <f:facet name="header">

            <jsp:include page="topoReduzido.jsp"/>

        </f:facet>
        <f:facet name="header">
            <h:panelGroup>
                <h:outputText value="Cadastro de Profissão"></h:outputText>
            </h:panelGroup>
        </f:facet>
        <f:facet name="controls">
            <h:panelGroup>
                <h:graphicImage value="/imagens/close.png" style="cursor:pointer" id="hidelink1"/>
                <rich:componentControl for="panelProfissao" attachTo="hidelink1" operation="hide" event="onclick"/>
            </h:panelGroup>
        </f:facet>

        <a4j:form id="formProfissao" ajaxSubmit="true">
            <h:panelGrid columns="1"  width="100%" columnClasses="colunaCentralizada">
                <h:panelGrid columns="1" style="height:25px; background-image:url('./imagens/fundoBarraTopo.png');background-repeat: repeat-x;" columnClasses="colunaCentralizada" width="100%">
                    <h:outputText styleClass="tituloFormulario" value="#{msg_aplic.prt_Profissao_tituloForm}"/>
                </h:panelGrid>

                <h:panelGrid columns="2"  rowClasses="linhaImpar, linhaPar"  columnClasses="classEsquerda, classDireita" width="100%">

                    <h:outputText   value="#{msg_aplic.prt_Profissao_codigo}" />
                    <h:panelGroup>
                        <h:inputText  id="codigo"  size="10" maxlength="10" readonly="true"  onblur="blurinput(this);"  onfocus="focusinput(this);" styleClass="camposSomenteLeitura"  value="#{ColaboradorControle.profissaoVO.codigo}" />
                        <h:message for="codigo" styleClass="mensagemDetalhada"/>
                    </h:panelGroup>
                    <h:outputText   value="#{msg_aplic.prt_Profissao_descricao}" />
                    <h:panelGroup>
                        <h:inputText  id="nomeProfissao" required="true" size="45" maxlength="45"  onblur="blurinput(this);"  onfocus="focusinput(this);" styleClass="form"  value="#{ColaboradorControle.profissaoVO.descricao}" />
                        <h:message for="nome_Profissao" styleClass="mensagemDetalhada"/>
                    </h:panelGroup>

                </h:panelGrid>

                <h:panelGrid id="panelMensagem" columns="1" width="100%" styleClass="tabMensagens">
                    <h:panelGrid columns="1" width="100%">
                        <h:outputText styleClass="mensagem"  value="#{ColaboradorControle.mensagem}"/>
                        <h:outputText styleClass="mensagemDetalhada" value="#{ColaboradorControle.mensagemDetalhada}"/>
                    </h:panelGrid>
                    <h:panelGrid columns="1" width="100%" columnClasses="colunaCentralizada">
                        <h:panelGroup>
                            <a4j:commandButton id="salvar" reRender="form" action="#{ColaboradorControle.gravarProfissao}" oncomplete="Richfaces.hideModalPanel('panelProfissao')" value="#{msg_bt.btn_gravar}" image="./imagens/botaoGravar.png" alt="#{msg.msg_gravar_dados}" accesskey="2" styleClass="botoes"/>
                        </h:panelGroup>
                    </h:panelGrid>
                </h:panelGrid>
            </h:panelGrid>

        </a4j:form>
    </rich:modalPanel>


    <h:panelGrid columns="1" width="100%" cellpadding="0" cellspacing="0">
        <f:facet name="header">

            <jsp:include page="topoReduzido.jsp"/>

        </f:facet>

        <h:form id="form">
            <h:panelGrid columns="1" width="100%">
                <h:panelGrid columns="1" style="height:25px; background-image:url('./imagens/fundoBarraTopo.png');background-repeat: repeat-x;" columnClasses="colunaCentralizada" width="100%">
                    <h:outputText styleClass="tituloFormulario" value="#{msg_aplic.prt_Colaborador_tituloForm}"/>
                </h:panelGrid>

                <h:panelGrid columns="2" rowClasses="linhaImpar, linhaPar" columnClasses="classEsquerda, classDireita" width="100%">
                    <h:outputText  rendered="#{ColaboradorControle.colaboradorVO.usuarioVO.administrador}" value="#{msg_aplic.prt_Colaborador_empresa}" />
                    <h:panelGroup rendered="#{ColaboradorControle.colaboradorVO.usuarioVO.administrador}">
                        <h:selectOneMenu  id="empresa" styleClass="form"  onblur="blurinput(this);"  onfocus="focusinput(this);" value="#{ColaboradorControle.colaboradorVO.empresa.codigo}" >
                            <f:selectItems  value="#{ColaboradorControle.listaSelectItemEmpresa}" />
                        </h:selectOneMenu>
                        <a4j:commandButton id="atualizar_empresa" action="#{ColaboradorControle.montarListaSelectItemEmpresa}" image="imagens/atualizar.png" ajaxSingle="true" reRender="form:empresa"/>
                        <h:message for="empresa" styleClass="mensagemDetalhada"/>
                    </h:panelGroup>
                </h:panelGrid>
                <rich:tabPanel width="100%" activeTabClass="true" headerAlignment="rigth" >
                    <rich:tab label="Dados Pessoais">
                        <h:panelGrid columns="2" rowClasses="linhaImpar, linhaPar" columnClasses="classEsquerda, classDireita" width="100%">
                            <%-- <h:outputText   value="#{msg_aplic.prt_Pessoa_codigo}" />
                            <h:panelGroup>
                                <h:inputText  id="codigo"  size="10" maxlength="10" readonly="true" styleClass="camposSomenteLeitura" value="#{ColaboradorControle.colaboradorVO.codigo}" />
                                <h:message for="codigo" styleClass="mensagemDetalhada"/>
                            </h:panelGroup>--%>

                            <h:outputText   value="#{msg_aplic.prt_Pessoa_profissao}" />
                            <h:panelGroup>
                                <h:selectOneMenu  id="profissao" onblur="blurinput(this);"  onfocus="focusinput(this);" styleClass="form" value="#{ColaboradorControle.pessoaVO.profissao.codigo}" >
                                    <f:selectItems  value="#{ColaboradorControle.listaSelectItemProfissao}" />
                                </h:selectOneMenu>
                                <a4j:commandButton id="atualizar_profissao" action="#{ColaboradorControle.montarListaSelectItemProfissao}" image="imagens/atualizar.png" immediate="true" ajaxSingle="true" reRender="form:profissao"/>
                                <a4j:commandButton id="consultaDadosProfissao" action="#{ProfissaoControle.inicializarProfisaoControle}"  focus="nomeProfissao" alt="Cadastrar Profissão" reRender="formProfissao" oncomplete="Richfaces.showModalPanel('panelProfissao'), setFocus(formProfissao,'formProfissao:nomeProfissao');" image="./images/icon_add.gif" />
                                <h:message for="profissao" styleClass="mensagemDetalhada"/>
                            </h:panelGroup>
                            <%--
                            <h:outputText   value="#{msg_aplic.prt_Colaborador_tipoColaborador}" />
                            <h:panelGroup>
                                <h:selectOneMenu  id="tipoColaborador" onblur="blurinput(this);"  onfocus="focusinput(this);" styleClass="form" value="#{ColaboradorControle.colaboradorVO.tipoColaborador}" >
                                    <f:selectItems  value="#{ColaboradorControle.listaSelectItemTipoColaborador}" />
                                </h:selectOneMenu>
                            </h:panelGroup>--%>
                            <h:outputText   value="#{msg_aplic.prt_Pessoa_dataCadastro}" />
                            <%--rich:calendar id="dataCadastro" value="#{ColaboradorControle.pessoaVO.dataCadastro}" inputSize="10"  inputClass="form"
                                           oninputchange="return mascara(this.form, this.id, '99/99/9999', event);" oninputblur="blurinput(this);return validar_Data(this.id);"
                                           oninputfocus="focusinput(this);"  datePattern="dd/MM/yyyy" enableManualInput="true" zindex="2" showWeeksBar="false" />
                            <h:inputText  id="dataCadastro" readonly="true" onchange="return mascara(this.form, 'form:dataCadastro', '99/99/9999', event);" size="10" maxlength="10" onblur="blurinput(this);return validar_Data('form:dataCadastro');"  onfocus="focusinput(this);" styleClass="form" value="#{ColaboradorControle.pessoaVO.dataCadastro}" >
                                <f:convertDateTime pattern="dd/MM/yyyy"/>
                            </h:inputText--%>
                            <h:panelGroup>
                                <rich:calendar id="dataCadastro"
                                               value="#{ColaboradorControle.pessoaVO.dataCadastro}"
                                               inputSize="10"
                                               inputClass="form"
                                               oninputblur="blurinput(this);"
                                               oninputfocus="focusinput(this);"
                                               oninputchange="return validar_Data(this.id);"
                                               datePattern="dd/MM/yyyy"
                                               enableManualInput="true"
                                               zindex="2"
                                               showWeeksBar="false"/>
                                <h:message for="dataCadastro" styleClass="mensagemDetalhada"/>
                            </h:panelGroup>
                            <h:outputText   value="#{msg_aplic.prt_Pessoa_nome}" />
                            <h:panelGroup>
                                <h:inputText  id="nome"  size="50" maxlength="50" onblur="blurinput(this);"  onfocus="focusinput(this);" styleClass="form" value="#{ColaboradorControle.pessoaVO.nome}" />
                                <h:message for="nome" styleClass="mensagemDetalhada"/>
                            </h:panelGroup>
                            <h:outputText   value="#{msg_aplic.prt_Pessoa_dataNasc}" />
                            <h:panelGroup>
                                <rich:calendar id="dataNasc"
                                               value="#{ColaboradorControle.pessoaVO.dataNasc}"
                                               inputSize="10"
                                               inputClass="form"
                                               oninputblur="blurinput(this);"
                                               oninputfocus="focusinput(this);"
                                               oninputchange="return validar_Data(this.id);"
                                               datePattern="dd/MM/yyyy"
                                               enableManualInput="true"
                                               zindex="2"
                                               showWeeksBar="false"
                                               oncollapse="selecionarRichCalendarColaborador_dataNasc(form);"/>
                                <a4j:commandLink id="dataNascCommandLink" reRender="panelExistePessoa , panelExisteColaborador " action="#{ColaboradorControle.consultarCliente}" style="display: none" />
                                <h:message for="dataNasc" styleClass="mensagemDetalhada"/>
                            </h:panelGroup>
                            <h:outputText   value="#{msg_aplic.prt_Pessoa_nomePai}" />
                            <h:inputText  id="nomePai" size="50" maxlength="50" onblur="blurinput(this);"  onfocus="focusinput(this);" styleClass="form" value="#{ColaboradorControle.pessoaVO.nomePai}" />
                            <h:outputText   value="#{msg_aplic.prt_Pessoa_nomeMae}" />
                            <h:inputText  id="nomeMae" size="50" maxlength="50" onblur="blurinput(this);"  onfocus="focusinput(this);" styleClass="form" value="#{ColaboradorControle.pessoaVO.nomeMae}" />
                            <h:outputText   value="#{msg_aplic.prt_Pessoa_cfp}" />
                            <h:panelGroup>
                                <h:inputText  id="cfp"  onkeypress="return mascara(this.form, 'form:cfp', '999.999.999-99', event);" size="14" maxlength="14" onblur="blurinput(this);"  onfocus="focusinput(this);" styleClass="form" value="#{ColaboradorControle.pessoaVO.cfp}" />
                                <h:message for="cfp" styleClass="mensagemDetalhada"/>
                            </h:panelGroup>
                            <h:outputText   value="#{msg_aplic.prt_Pessoa_rg}" />
                            <h:inputText  id="rg" size="20" maxlength="20" onblur="blurinput(this);"  onfocus="focusinput(this);" styleClass="form" value="#{ColaboradorControle.pessoaVO.rg}" />
                            <h:outputText   value="#{msg_aplic.prt_Pessoa_rgOrgao}" />
                            <h:inputText  id="rgOrgao" size="10" maxlength="10" onblur="blurinput(this);"  onfocus="focusinput(this);" styleClass="form" value="#{ColaboradorControle.pessoaVO.rgOrgao}" />
                            <h:outputText   value="#{msg_aplic.prt_Pessoa_rgUf}" />
                            <h:selectOneMenu  id="rgUf" onblur="blurinput(this);"  onfocus="focusinput(this);" styleClass="form" value="#{ColaboradorControle.pessoaVO.rgUf}" >
                                <f:selectItems  value="#{ColaboradorControle.listaSelectItemRgUfPessoa}" />
                            </h:selectOneMenu>
                            <h:outputText   value="#{msg_aplic.prt_Pessoa_pais}" />
                            <h:panelGroup>
                                <h:selectOneMenu  id="pais" onblur="blurinput(this);"  onfocus="focusinput(this);" styleClass="form" value="#{ColaboradorControle.pessoaVO.pais.codigo}" >
                                    <f:selectItems  value="#{ColaboradorControle.listaSelectItemPais}" />
                                    <a4j:support  event="onchange"   reRender="form:estado,cidade" focus="pais" action="#{ColaboradorControle.montarListaSelectItemEstado}"/>
                                </h:selectOneMenu>
                                <a4j:commandButton id="atualizar_pais" action="#{ColaboradorControle.montarListaSelectItemPais}" image="imagens/atualizar.png" immediate="true" ajaxSingle="true" reRender="form:pais"/>
                                <h:message for="pais" styleClass="mensagemDetalhada"/>

                            </h:panelGroup>
                            <h:outputText    value="#{msg_aplic.prt_Pessoa_estado}" />
                            <h:panelGroup>
                                <h:selectOneMenu  id="estado" onblur="blurinput(this);"  onfocus="focusinput(this);" styleClass="form"  value="#{ColaboradorControle.pessoaVO.estadoVO.codigo}" >
                                    <f:selectItems  value="#{ColaboradorControle.listaSelectItemEstado}" />
                                    <a4j:support  event="onchange" reRender="cidade" action="#{ColaboradorControle.montarListaSelectItemCidade}"/>
                                </h:selectOneMenu>
                                <a4j:commandButton id="atualizar_estado" action="#{ColaboradorControle.montarListaSelectItemEstado}" image="imagens/atualizar.png" ajaxSingle="true" reRender="form:estado"/>
                                <h:message for="estado" styleClass="mensagemDetalhada"/>
                            </h:panelGroup>
                            <h:outputText   value="#{msg_aplic.prt_Pessoa_cidade}" />
                            <h:panelGroup>
                                <h:selectOneMenu  id="cidade" onblur="blurinput(this);"  onfocus="focusinput(this);" styleClass="form" value="#{ColaboradorControle.pessoaVO.cidade.codigo}" >
                                    <f:selectItems  value="#{ColaboradorControle.listaSelectItemCidade}" />
                                </h:selectOneMenu>
                                <a4j:commandButton id="atualizar_cidade" action="#{ColaboradorControle.montarListaSelectItemCidade}" image="imagens/atualizar.png" immediate="true" ajaxSingle="true" reRender="form:cidade"/>
                                <h:message for="cidade" styleClass="mensagemDetalhada"/>
                            </h:panelGroup>

                            <h:outputText   value="#{msg_aplic.prt_Pessoa_estadoCivil}" />
                            <h:selectOneMenu  id="estadoCivil" onblur="blurinput(this);"  onfocus="focusinput(this);" styleClass="form" value="#{ColaboradorControle.pessoaVO.estadoCivil}" >
                                <f:selectItems  value="#{ColaboradorControle.listaSelectItemEstadoCivilPessoa}" />
                            </h:selectOneMenu>
                            <h:outputText   value="#{msg_aplic.prt_Pessoa_nacionalidade}" />
                            <h:inputText  id="nacionalidade" size="20" maxlength="20" onblur="blurinput(this);"  onfocus="focusinput(this);" styleClass="form" value="#{ColaboradorControle.pessoaVO.nacionalidade}" />
                            <h:outputText   value="#{msg_aplic.prt_Pessoa_naturalidade}" />
                            <h:inputText  id="naturalidade" size="20" maxlength="20" onblur="blurinput(this);"  onfocus="focusinput(this);" styleClass="form" value="#{ColaboradorControle.pessoaVO.naturalidade}" />
                            <h:outputText   value="#{msg_aplic.prt_Pessoa_sexo}" />
                            <h:selectOneMenu  id="sexo" onblur="blurinput(this);"  onfocus="focusinput(this);" styleClass="form" value="#{ColaboradorControle.pessoaVO.sexo}" >
                                <f:selectItems  value="#{ColaboradorControle.listaSelectItemSexoPessoa}" />
                            </h:selectOneMenu>
                            <h:outputText    value="#{msg_aplic.prt_Pessoa_webPage}" />
                            <h:inputText  id="webPage" size="50" maxlength="50" onblur="blurinput(this);"  onfocus="focusinput(this);"   styleClass="form" value="#{ColaboradorControle.pessoaVO.webPage}" />
                        </h:panelGrid>
                    </rich:tab>
                    <rich:tab label="Colaborador">
                        <h:panelGrid columns="2" rowClasses="linhaImpar, linhaPar" columnClasses="classEsquerda, classDireita" width="100%">

                            <h:outputText   value="#{msg_aplic.prt_Colaborador_codigo}" />
                            <h:panelGroup>
                                <h:inputText  id="codigo"  size="10" maxlength="10" readonly="true" onblur="blurinput(this);"  onfocus="focusinput(this);" styleClass="form" value="#{ColaboradorControle.colaboradorVO.codigo}" />
                                <h:message for="codigo" styleClass="mensagemDetalhada"/>
                            </h:panelGroup>

                            <h:outputText   value="#{msg_aplic.prt_Colaborador_situacao}" />
                            <h:panelGroup>
                                <h:selectOneMenu  id="situacao" onblur="blurinput(this);"  onfocus="focusinput(this);" styleClass="form" value="#{ColaboradorControle.colaboradorVO.situacao}" >
                                    <f:selectItems  value="#{ColaboradorControle.listaSelectItemSituacaoColaborador}" />
                                </h:selectOneMenu>
                                <h:message for="situacao" styleClass="mensagemDetalhada"/>
                            </h:panelGroup>
                            <h:outputText  value="#{msg_aplic.prt_Colaborador_codAcesso}" />
                            <h:panelGroup>
                                <h:inputText  id="codAcesso"  size="10" maxlength="10" onblur="blurinput(this);"  onfocus="focusinput(this);" styleClass="form" value="#{ColaboradorControle.colaboradorVO.codAcesso}" />
                                <h:message for="codAcesso" styleClass="mensagemDetalhada"/>
                            </h:panelGroup>
                            <h:outputText    value="#{msg_aplic.prt_Colaborador_codAcessoAlternativo}" />
                            <h:inputText  id="codAcessoAlternativo"  onblur="blurinput(this);"  onfocus="focusinput(this);" styleClass="form"  size="10" maxlength="15"  value="#{ColaboradorControle.colaboradorVO.codAcessoAlternativo}" />

                            <%-- FOI RETIRADO E ADICIONADO O TIPO COLABORADOR FUNCIONARIO - PROJETO 182232 --%>
                            <%--<h:outputText   value="#{msg_aplic.prt_Colaborador_funcionario}" />--%>
                            <%--<h:selectBooleanCheckbox id="funcionario" styleClass="campos" value="#{ColaboradorControle.colaboradorVO.funcionario}"/>--%>

                        </h:panelGrid>
                        <h:panelGrid id="panelTipoColaborador" columns="2" rowClasses="linhaImpar, linhaPar" columnClasses="classEsquerda, classDireita" width="100%">
                            <h:outputText   value="#{msg_aplic.prt_Colaborador_tipoColaborador}" />
                            <h:panelGroup>
                                <h:selectOneMenu  id="tipoColaboradorSetProfessor" onblur="blurinput(this);"  onfocus="focusinput(this);" styleClass="form" value="#{ColaboradorControle.tipoColaboradorVO.descricao}" >
                                    <f:selectItems  value="#{ColaboradorControle.listaSelectItemTipoColaborador}" />
                                </h:selectOneMenu>
                                <a4j:commandButton action="#{ColaboradorControle.adicionarTipoColaborador}" reRender="panelTipoColaborador , dataTabletipoColaboradorVO, panelMensagem" focus="enderecoColaborador" value="#{msg_bt.btn_adicionar}" image= "./imagens/botaoAdicionar.png" accesskey="5" styleClass="botoes"/>
                            </h:panelGroup>
                        </h:panelGrid>
                        <h:panelGrid columns="1" width="100%" styleClass="tabFormSubordinada">
                            <h:dataTable id="dataTabletipoColaboradorVO" width="100%" headerClass="subordinado" styleClass="tabFormSubordinada"
                                         rowClasses="linhaImpar, linhaPar" columnClasses="colunaAlinhamento"
                                         value="#{ColaboradorControle.colaboradorVO.listaTipoColaboradorVOs}" var="tipoColaboradorVO">
                                <h:column>
                                    <f:facet name="header">
                                        <h:outputText  value="#{msg_aplic.prt_Colaborador_tipoColaborador}" />
                                    </f:facet>
                                    <h:outputText value="#{tipoColaboradorVO.descricao_Apresentar}" />
                                </h:column>
                                <h:column>
                                    <f:facet name="header">
                                        <h:outputText  value="#{msg_bt.btn_opcoes}" />
                                    </f:facet>
                                    <a4j:commandButton id="removerTipoColaborador" action="#{ColaboradorControle.removerTipoColaborador}" reRender="dataTabletipoColaboradorVO, panelMensagem" value="#{msg_bt.btn_excluir}" image="./imagens/botaoRemover.png" accesskey="7" styleClass="botoes"/>
                                </h:column>
                            </h:dataTable>
                        </h:panelGrid>
                    </rich:tab>

                    <rich:tab label="Endereço">
                        <h:panelGrid id="panelEnderecoColaborador" columns="1" width="100%" headerClass="subordinado" columnClasses="colunaCentralizada">
                            <f:facet name="header">
                                <h:outputText value="#{msg_aplic.prt_Endereco_tituloForm}"/>
                            </f:facet>
                            <h:panelGrid id="panelEnderecoColaborador1" columns="2" rowClasses="linhaImpar, linhaPar" columnClasses="classEsquerda, classDireita" width="100%">
                                <%--<h:outputText   value="#{msg_aplic.prt_Endereco_codigo}" />
                                <h:inputText  size="10" maxlength="10" readonly="true" onblur="blurinput(this);"  onfocus="focusinput(this);" styleClass="form" value="#{ColaboradorControle.enderecoVO.codigo}" /> --%>

                                <h:outputText   value="#{msg_aplic.prt_Endereco_endereco}" />
                                <h:inputText  size="40" maxlength="40" onblur="blurinput(this);"  onfocus="focusinput(this);" styleClass="form" value="#{ColaboradorControle.enderecoVO.endereco}" />
                                <h:outputText   value="#{msg_aplic.prt_Endereco_complemento}" />
                                <h:inputText  size="40" maxlength="40" onblur="blurinput(this);"  onfocus="focusinput(this);" styleClass="form" value="#{ColaboradorControle.enderecoVO.complemento}" />
                                <h:outputText   value="#{msg_aplic.prt_Endereco_numero}" />
                                <h:inputText  size="10" maxlength="10" onblur="blurinput(this);"  onfocus="focusinput(this);" styleClass="form" value="#{ColaboradorControle.enderecoVO.numero}" />
                                <h:outputText   value="#{msg_aplic.prt_Endereco_bairro}" />
                                <h:inputText  size="35" maxlength="35" onblur="blurinput(this);"  onfocus="focusinput(this);" styleClass="form" value="#{ColaboradorControle.enderecoVO.bairro}" />
                                <h:outputText   value="#{msg_aplic.prt_Endereco_cep}" />
                                <h:inputText  id="cep" size="10" maxlength="10" onkeypress="return mascara(this.form, 'form:cep', '99.999-999', event);" onblur="blurinput(this);"  onfocus="focusinput(this);" styleClass="form" value="#{ColaboradorControle.enderecoVO.cep}" />
                                <h:outputText   value="#{msg_aplic.prt_Endereco_enderecoCorrespondencia}"/>
                                <h:selectBooleanCheckbox id="enderecoCorresponencia" value="#{ColaboradorControle.enderecoVO.enderecoCorrespondencia}"/>
                                <h:outputText   value="#{msg_aplic.prt_Endereco_tipoEndereco}" />
                                <h:selectOneMenu  id="Endereco_tipoEndereco" onblur="blurinput(this);"  onfocus="focusinput(this);" styleClass="form" value="#{ColaboradorControle.enderecoVO.tipoEndereco}" >
                                    <f:selectItems  value="#{ColaboradorControle.listaSelectItemTipoEnderecoEndereco}" />
                                </h:selectOneMenu>
                            </h:panelGrid>
                            <h:commandButton action="#{ColaboradorControle.adicionarEndereco}" value="#{msg_bt.btn_adicionar}" image= "./imagens/botaoAdicionar.png" accesskey="5" styleClass="botoes"/>
                        </h:panelGrid>
                        <h:panelGrid columns="1" width="100%" styleClass="tabFormSubordinada">
                            <h:dataTable id="enderecoVO" width="100%" headerClass="subordinado" styleClass="tabFormSubordinada"
                                         rowClasses="linhaImpar, linhaPar" columnClasses="colunaAlinhamento"
                                         value="#{ColaboradorControle.pessoaVO.enderecoVOs}" var="endereco">
                                <%-- <h:column>
                                    <f:facet name="header">
                                        <h:outputText  value="#{msg_aplic.prt_Endereco_codigo}" />
                                    </f:facet>
                                    <h:outputText  value="#{endereco.codigo}" />
                                </h:column>--%>
                                <h:column>
                                    <f:facet name="header">
                                        <h:outputText  value="#{msg_aplic.prt_Endereco_endereco}" />
                                    </f:facet>
                                    <h:outputText  value="#{endereco.endereco}" />
                                </h:column>
                                <h:column>
                                    <f:facet name="header">
                                        <h:outputText  value="#{msg_aplic.prt_Endereco_complemento}" />
                                    </f:facet>
                                    <h:outputText  value="#{endereco.complemento}" />
                                </h:column>
                                <h:column>
                                    <f:facet name="header">
                                        <h:outputText  value="#{msg_aplic.prt_Endereco_numero}" />
                                    </f:facet>
                                    <h:outputText  value="#{endereco.numero}" />
                                </h:column>
                                <h:column>
                                    <f:facet name="header">
                                        <h:outputText  value="#{msg_aplic.prt_Endereco_bairro}" />
                                    </f:facet>
                                    <h:outputText  value="#{endereco.bairro}" />
                                </h:column>
                                <h:column>
                                    <f:facet name="header">
                                        <h:outputText  value="#{msg_aplic.prt_Endereco_cep}" />
                                    </f:facet>
                                    <h:outputText  value="#{endereco.cep}" />
                                </h:column>
                                <h:column>
                                    <f:facet name="header">
                                        <h:outputText  value="#{msg_aplic.prt_Endereco_tipoEndereco}" />
                                    </f:facet>
                                    <h:outputText  value="#{endereco.tipoEndereco_Apresentar}" />
                                </h:column>
                                <h:column>
                                    <f:facet name="header">
                                        <h:outputText  value="#{msg_bt.btn_opcoes}" />
                                    </f:facet>
                                    <h:panelGroup>
                                        <h:commandButton id="editarItemVenda" action="#{ColaboradorControle.editarEndereco}" value="#{msg_bt.btn_editar}" image="./imagens/botaoEditar.png" accesskey="6" styleClass="botoes"/>

                                        <h:outputText value="    "/>

                                        <h:commandButton id="removerItemVenda" immediate="true" action="#{ColaboradorControle.removerEndereco}" value="#{msg_bt.btn_excluir}" image="./imagens/botaoRemover.png" accesskey="7" styleClass="botoes"/>
                                    </h:panelGroup>
                                </h:column>
                            </h:dataTable>
                        </h:panelGrid>
                    </rich:tab>
                    <rich:tab label="Email">
                        <h:panelGrid columns="1" width="100%" headerClass="subordinado" columnClasses="colunaCentralizada">
                            <f:facet name="header">
                                <h:outputText value="#{msg_aplic.prt_Email_tituloForm}"/>
                            </f:facet>
                            <h:panelGrid columns="2" rowClasses="linhaImpar, linhaPar" columnClasses="classEsquerda, classDireita" width="100%">
                                <h:outputText    value="#{msg_aplic.prt_Email_email}" />
                                <h:inputText  size="40" maxlength="50" onblur="blurinput(this);"  onfocus="focusinput(this);"   styleClass="form" value="#{ColaboradorControle.emailVO.email}" />
                                <h:outputText    value="#{msg_aplic.prt_Email_emailCorrespondencia}" />
                                <h:selectBooleanCheckbox  styleClass="campos" value="#{ColaboradorControle.emailVO.emailCorrespondencia}" />
                            </h:panelGrid>
                            <h:commandButton action="#{ColaboradorControle.adicionarEmail}" value="#{msg_bt.btn_adicionar}" image= "./imagens/botaoAdicionar.png" accesskey="5" styleClass="botoes"/>
                        </h:panelGrid>
                        <h:panelGrid columns="1" width="100%" styleClass="tabFormSubordinada">
                            <h:dataTable id="emailVO" width="100%" headerClass="subordinado"
                                         rowClasses="linhaImpar, linhaPar" columnClasses="colunaAlinhamento"
                                         value="#{ColaboradorControle.pessoaVO.emailVOs}" var="email">
                                <h:column>
                                    <f:facet name="header">
                                        <h:outputText  value="#{msg_aplic.prt_Email_email}" />
                                    </f:facet>
                                    <h:outputText  value="#{email.email}" />
                                </h:column>
                                <h:column>
                                    <f:facet name="header">
                                        <h:outputText  value="#{msg_aplic.prt_Email_emailCorrespondencia}" />
                                    </f:facet>
                                    <h:outputText  value="#{email.emailCorrespondencia_Apresentar}" />
                                </h:column>
                                <h:column>
                                    <f:facet name="header">
                                        <h:outputText  value="#{msg_bt.btn_opcoes}" />
                                    </f:facet>
                                    <h:panelGroup>
                                        <h:commandButton id="editarItemVenda"  action="#{ColaboradorControle.editarEmail}" value="#{msg_bt.btn_editar}" image="./imagens/botaoEditar.png" accesskey="6" styleClass="botoes"/>

                                        <h:outputText value="    "/>

                                        <h:commandButton id="removerItemVenda"  action="#{ColaboradorControle.removerEmail}" value="#{msg_bt.btn_excluir}" image="./imagens/botaoRemover.png" accesskey="7" styleClass="botoes"/>
                                    </h:panelGroup>
                                </h:column>
                            </h:dataTable>
                        </h:panelGrid>
                    </rich:tab>
                    <rich:tab label="Telefone">
                        <h:panelGrid columns="1" width="100%" headerClass="subordinado" columnClasses="colunaCentralizada">
                            <f:facet name="header">
                                <h:outputText value="#{msg_aplic.prt_Telefone_tituloForm}"/>
                            </f:facet>
                            <h:panelGrid columns="2" rowClasses="linhaImpar, linhaPar" columnClasses="classEsquerda, classDireita" width="100%">
                                <h:outputText   value="#{msg_aplic.prt_Telefone_numero}" />
                                <h:inputText  id="numero" size="13"
                                              maxlength="13"
                                              onchange="return validar_Telefone(this.id);"
                                              onblur="blurinput(this);"
                                              onkeypress="return mascara(this.form, this.id , '(99)999999999', event);"
                                              onfocus="focusinput(this);"
                                              styleClass="form" value="#{ColaboradorControle.telefoneVO.numero}" />
                                <h:outputText   value="#{msg_aplic.prt_Telefone_tipoTelefone}" />
                                <h:selectOneMenu  id="Telefone_tipoTelefone" onblur="blurinput(this);"  onfocus="focusinput(this);" styleClass="form" value="#{ColaboradorControle.telefoneVO.tipoTelefone}" >
                                    <f:selectItems  value="#{ColaboradorControle.listaSelectItemTipoTelefoneTelefone}" />
                                </h:selectOneMenu>
                            </h:panelGrid>
                            <h:commandButton action="#{ColaboradorControle.adicionarTelefone}" value="#{msg_bt.btn_adicionar}" image= "./imagens/botaoAdicionar.png" accesskey="6" styleClass="botoes"/>
                        </h:panelGrid>
                        <h:panelGrid columns="1" width="100%" styleClass="tabFormSubordinada">
                            <h:dataTable id="telefoneVO" width="100%" headerClass="subordinado" styleClass="tabFormSubordinada"
                                         rowClasses="linhaImpar, linhaPar" columnClasses="colunaAlinhamento"
                                         value="#{ColaboradorControle.pessoaVO.telefoneVOs}" var="telefone">
                                <h:column>
                                    <f:facet name="header">
                                        <h:outputText  value="#{msg_aplic.prt_Telefone_numero}" />
                                    </f:facet>
                                    <h:outputText  value="#{telefone.numero}" />
                                </h:column>
                                <h:column>
                                    <f:facet name="header">
                                        <h:outputText  value="#{msg_aplic.prt_Telefone_tipoTelefone}" />
                                    </f:facet>
                                    <h:outputText  value="#{telefone.tipoTelefone_Apresentar}" />
                                </h:column>
                                <h:column>
                                    <f:facet name="header">
                                        <h:outputText  value="#{msg_bt.btn_opcoes}" />
                                    </f:facet>
                                    <h:panelGroup>
                                        <h:commandButton id="editarItemVenda"  action="#{ColaboradorControle.editarTelefone}" value="#{msg_bt.btn_editar}" image="./imagens/botaoEditar.png" accesskey="6" styleClass="botoes"/>

                                        <h:outputText value="    "/>

                                        <h:commandButton id="removerItemVenda" immediate="true" action="#{ColaboradorControle.removerTelefone}" value="#{msg_bt.btn_excluir}" image="./imagens/botaoRemover.png" accesskey="7" styleClass="botoes"/>
                                    </h:panelGroup>
                                </h:column>
                            </h:dataTable>
                        </h:panelGrid>
                    </rich:tab>

                    <rich:tab id="abaFoto" label="Foto">
                        <h:panelGrid id="panelFoto"columns="2" rowClasses="linhaImpar, linhaPar" columnClasses="classEsquerda, classDireita">
                            <h:outputText    value="" />
                            <rich:fileUpload listHeight="30"
                                             listWidth="180"
                                             noDuplicate="false"
                                             fileUploadListener="#{ColaboradorControle.upload}"
                                             maxFilesQuantity="1"
                                             addControlLabel="Adicionar"
                                             cancelEntryControlLabel="Cancelar"
                                             doneLabel="Pronto"
                                             sizeErrorLabel="Arquivo não Enviado. Tamanho Máximo Permitido 100 MB"
                                             progressLabel="Enviando"
                                             stopControlLabel="Parar"
                                             uploadControlLabel="Enviar"
                                             transferErrorLabel="Falha de Transmissão"
                                             stopEntryControlLabel="Parar"
                                             id="upload"
                                             immediateUpload="true"
                                             autoclear="true"
                                             acceptedTypes="png,gif,jpg,jpeg,ico,bmp,PNG,GIF,JPG,JPEG,ICO,BMP">

                                <a4j:support event="onuploadcomplete" reRender="form:panelFoto" />
                            </rich:fileUpload>
                            <h:graphicImage width="120" height="160" rendered="#{ColaboradorControle.pessoaVO.existeFoto}" value="#{ColaboradorControle.pessoaVO.nomeFoto}" />
                        </h:panelGrid>
                    </rich:tab>
                </rich:tabPanel>
                <h:panelGrid columns="1" width="100%" styleClass="tabMensagens">
                    <h:panelGrid id="panelMensagem" columns="3" width="100%" styleClass="tabMensagens">
                        <h:panelGrid columns="1" width="100%">

                            <h:outputText value=" "/>

                        </h:panelGrid>
                        <h:commandButton  rendered="#{ColaboradorControle.sucesso}"image="./imagens/sucesso.png"/>
                        <h:commandButton rendered="#{ColaboradorControle.erro}" image="./imagens/erro.png"/>
                        <h:panelGrid columns="1" width="100%">
                            <h:outputText styleClass="mensagem"  value="#{ColaboradorControle.mensagem}"/>
                            <h:outputText styleClass="mensagemDetalhada" value="#{ColaboradorControle.mensagemDetalhada}"/>
                        </h:panelGrid>
                    </h:panelGrid>
                    <h:panelGrid columns="1" width="100%" columnClasses="colunaCentralizada">
                        <h:panelGroup>
                            <a4j:commandButton id="salvar" reRender="form, formExistePessoa, panelExisteColaborador " action="#{ColaboradorControle.gravarColaboradorAPartirDoFormProfessor}" value="#{msg_bt.btn_gravar}" image="./imagens/botaoGravar.png" alt="#{msg.msg_gravar_dados}" accesskey="2" styleClass="botoes"/>
                        </h:panelGroup>
                    </h:panelGrid>
                </h:panelGrid>
            </h:panelGrid>
            <rich:jQuery id="mskData" selector=".rich-calendar-input" timing="onload" query="mask('99/99/9999')" />
        </h:form>
    </h:panelGrid>
</f:view>
<script>
    document.getElementById("form:pessoa").focus();
</script>