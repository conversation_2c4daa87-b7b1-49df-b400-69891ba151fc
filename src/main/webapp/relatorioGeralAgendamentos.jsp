<%-- 
    Document   : relatorioGeralAgendamentos
    Created on : Jul 24, 2013, 4:20:59 PM
    Author     : francisco
--%>

<%@include file="pages/estudio/includes/include_imports.jsp"%>
<%@taglib uri="http://java.sun.com/jsp/jstl/functions" prefix="fn"%>
<c:set var="moduloSession" value="1" scope="session"/>

<html>
    <head>
        <script type="text/javascript" src="${contexto}/script/basico.js"></script>
        <link href="${contexto}/css/otimize.css" rel="stylesheet" type="text/css">
        <link href="${contexto}/css/estudio.css" rel="stylesheet" type="text/css">
        <link href="${contexto}/css/otimizeCRM.css" rel="stylesheet" type="text/css">
        <jsp:include page="pages/estudio/includes/include_head.jsp"/>
        
        <style>
			.rich-table, .rich-table-header, .rich-table-headercell, .rich-table-cell, .rich-subtable-cell,
		 	.rich-table-footercell,.rich-subtable-footercell {
		    border-width: 1px;
		}
		</style>
    </head>

    <body>
        <f:view>
            <jsp:include page="includes/include_carregando_ripple.jsp" flush="true"/>
        <title>
            <h:outputText value="Gest�o de Agendamentos"/>
        </title>
        <a4j:loadScript src="/script/jquery.maskedinput-1.2.2.js" />
        <h:form id="agendaGeral" prependId="false">
            <h:panelGroup layout="block" style="display: inline-table;width: 100%;" styleClass="fundoCinza">
                <h:panelGroup layout="block" styleClass="bgtop topoZW" rendered="#{MenuControle.apresentarTopo}">
                    <jsp:include page="include_topo_novo.jsp" flush="true"/>
                    <jsp:include page="include_menu_zw_flat.jsp" flush="true"/>
                    <rich:jQuery selector=".item3" query="addClass('menuItemAtual')"/>
                </h:panelGroup>

                <h:panelGroup layout="block" styleClass="caixaCorpo">
                    <h:panelGroup layout="block" style="height: 80%;width: 100%">
                        <h:panelGroup layout="block" styleClass="caixaMenuLatel">
                            <h:panelGroup layout="block" styleClass="container-imagem container-conteudo-central">
                                <h:panelGroup layout="block" styleClass="container-box zw_ui especial ">
                                    <h:panelGroup styleClass="container-box-header" layout="block">
                                        <h:panelGroup layout="block" styleClass="margin-box">
                                            <h:outputText value="Gest�o de Agendamentos" styleClass="container-header-titulo"/>
                                            <h:outputLink styleClass="linkWiki"
                                                          value="#{SuperControle.urlBaseConhecimento}relatorio-agendamentos-studio/"
                                                          title="Clique e saiba mais: Disponibilidade"
                                                          target="_blank">
                                                <i class="fa-icon-question-sign" style="font-size: 18px"></i>
                                            </h:outputLink>
                                        </h:panelGroup>
                                    </h:panelGroup>
                                    <h:panelGroup layout="block" styleClass="margin-box">
                                        <h:panelGrid columns="1" columnClasses="column1" style="width:100%">
                                            <h:panelGrid columns="1" columnClasses="column1" style="width:100%;">
                                                <h:panelGrid columns="10" style="padding-bottom: 10px;">
                                                    <h:outputText value="Filtrar por:" styleClass="texto_disponibilidade"/>

                                                    <a4j:commandButton id="btnBuscarAmbiente"
                                                                       image="imagens/estudio/ambiente.png"
                                                                       value="Ambiente" styleClass="texto_disponibilidade"
                                                                       title="Filtro(s) selecionado(s): #{RelatorioGeralAgendamentosControle.listaNomeAmbiente}"
                                                                       oncomplete="#{rich:component('panelFiltroAmbiente')}.show()"
                                                                       action="#{RelatorioGeralAgendamentosControle.acaoBuscarAmbiente}"
                                                                       reRender="formPanelFiltroAmbiente">
                                                    </a4j:commandButton>

                                                    <rich:spacer width="20px;"/>

                                                    <a4j:commandButton id="btnBuscarPessoa"
                                                                       image="imagens/estudio/profissional.png"
                                                                       value="Profissional" styleClass="texto_disponibilidade"
                                                                       title="Filtro(s) selecionado(s): #{RelatorioGeralAgendamentosControle.listaNomeColaborador}"
                                                                       action="#{RelatorioGeralAgendamentosControle.acaoBuscarPessoa}"
                                                                       oncomplete="#{rich:component('panelFiltroProfissional')}.show()"
                                                                       reRender="formPanelFiltroProfissional">
                                                    </a4j:commandButton>

                                                    <rich:spacer width="20px;"/>

                                                    <a4j:commandButton id="btnBuscarServico"
                                                                       image="imagens/estudio/servico.png"
                                                                       value="Servi�o" styleClass="texto_disponibilidade"
                                                                       title="Filtro(s) selecionado(s): #{RelatorioGeralAgendamentosControle.listaNomeServico}"
                                                                       action="#{RelatorioGeralAgendamentosControle.acaoEntrarModalServico}"
                                                                       oncomplete="#{rich:component('panelFiltroServico')}.show()"
                                                                       reRender="formPanelFiltroServico">
                                                    </a4j:commandButton>

                                                    <rich:spacer width="20px;"/>

                                                    <a4j:commandButton id="btnBuscarTipoHorario"
                                                                       image="imagens/estudio/tipo_horario.png"
                                                                       value="Tipos de Hor�rios" styleClass="texto_disponibilidade"
                                                                       action="#{RelatorioGeralAgendamentosControle.acaoBuscarTipoHorario}"
                                                                       title="Filtro(s) selecionado(s): #{RelatorioGeralAgendamentosControle.listaNomeTipoHorario}"
                                                                       oncomplete="#{rich:component('panelFiltroTipoHorario')}.show()"
                                                                       reRender="formPanelFiltroTipoHorario">
                                                    </a4j:commandButton>

                                                    <rich:spacer width="20px;"/>

                                                    <a4j:commandButton id="btnBuscarStatus"
                                                                       image="imagens/estudio/status_sessao.png"
                                                                       value="Status da Sess�o" styleClass="texto_disponibilidade"
                                                                       title="Filtro(s) selecionado(s): #{RelatorioGeralAgendamentosControle.listaNomeStatus}"
                                                                       oncomplete="#{rich:component('panelFiltroStatus')}.show()"
                                                                       reRender="formPanelFiltroStatus">
                                                    </a4j:commandButton>
                                                </h:panelGrid>

                                                <h:panelGrid columns="10" id="periodos">
                                                    <h:outputLabel value="Situa��o:" styleClass="texto_disponibilidade"/>
                                                    <h:selectOneMenu
                                                            id="relatorioGeralAgendamentos-listSituacao"
                                                            value="#{RelatorioGeralAgendamentosControle.situacaoEnum}"
                                                            onblur="blurinput(this);"
                                                            onfocus="focusinput(this);"
                                                            styleClass="form">
                                                        <f:selectItems value="#{RelatorioGeralAgendamentosControle.situacaoSelect}" />
                                                        <a4j:support  event="onchange"
                                                                      action="#{RelatorioGeralAgendamentosControle.acaoPeriodoIntervalo}"
                                                                      reRender="relatorioGeralAgendamentos-periodoInicial,relatorioGeralAgendamentos-periodoFinal" />
                                                    </h:selectOneMenu>

                                                    <rich:spacer width="10px"/>

                                                    <h:outputLabel value="Intervalo:" styleClass="texto_disponibilidade"/>
                                                    <h:selectOneMenu
                                                            id="relatorioGeralAgendamentos-listPeriodo"
                                                            value="#{RelatorioGeralAgendamentosControle.periodoEnum}"
                                                            onblur="blurinput(this);"
                                                            onfocus="focusinput(this);"
                                                            styleClass="form">
                                                        <f:selectItems value="#{RelatorioGeralAgendamentosControle.periodoSelect}" />
                                                        <a4j:support  event="onchange"
                                                                      action="#{RelatorioGeralAgendamentosControle.acaoPeriodoIntervalo}"
                                                                      reRender="relatorioGeralAgendamentos-periodoInicial,relatorioGeralAgendamentos-periodoFinal" />
                                                    </h:selectOneMenu>

                                                    <rich:spacer width="10px"/>

                                                    <h:outputLabel value="Per�odo:" styleClass="texto_disponibilidade" />
                                                    <rich:calendar
                                                            locale="pt_BR"
                                                            inputSize="10"
                                                            inputClass="form"
                                                            oninputblur="blurinput(this);"
                                                            oninputfocus="focusinput(this);"
                                                            oninputchange="return validar_Data(this.id);"
                                                            datePattern="dd/MM/yyyy"
                                                            enableManualInput="true"
                                                            zindex="2"
                                                            showWeeksBar="false"
                                                            value="#{RelatorioGeralAgendamentosControle.periodoInicial}"
                                                            id="relatorioGeralAgendamentos-periodoInicial"
                                                            popup="true"  styleClass="texto_disponibilidade">
                                                    </rich:calendar>

                                                    <h:outputLabel value=" a " styleClass="texto_disponibilidade"/>
                                                    <rich:calendar
                                                            locale="pt_BR"
                                                            inputSize="10"
                                                            inputClass="form"
                                                            oninputblur="blurinput(this);"
                                                            oninputfocus="focusinput(this);"
                                                            oninputchange="return validar_Data(this.id);"
                                                            datePattern="dd/MM/yyyy"
                                                            enableManualInput="true"
                                                            zindex="2"
                                                            showWeeksBar="false"
                                                            value="#{RelatorioGeralAgendamentosControle.periodoFinal}"
                                                            id="relatorioGeralAgendamentos-periodoFinal"
                                                            popup="true" styleClass="texto_disponibilidade" >
                                                    </rich:calendar>
                                                    <rich:jQuery id="mskData" selector=".rich-calendar-input" timing="onload" query="mask('99/99/9999')" />
                                                </h:panelGrid>

                                                <h:panelGrid columns="2" style="width:100%;">
                                                    <h:panelGrid columns="10">
                                                        <h:outputLabel value="Nome:" styleClass="texto_disponibilidade"/>
                                                        <h:inputText
                                                                value="#{RelatorioGeralAgendamentosControle.nomeCliente}"
                                                                size="30" styleClass="form"/>
                                                        <rich:spacer width="20px"/>

                                                        <h:outputLabel value="Hor�rios:" styleClass="texto_disponibilidade"/>
                                                        <h:selectOneMenu
                                                                id="relatorioGeralAgendamentos-listaHorarioInicial"
                                                                value="#{RelatorioGeralAgendamentosControle.horaInicial}"
                                                                onblur="blurinput(this);"
                                                                onfocus="focusinput(this);"
                                                                converter="timeConverter2"
                                                                styleClass="form">
                                                            <f:selectItem itemLabel="" itemValue=""/>
                                                            <f:selectItems value="#{RelatorioGeralAgendamentosControle.selectHorario}" />
                                                        </h:selectOneMenu>

                                                        <h:outputLabel value=" a " styleClass="texto_disponibilidade"/>

                                                        <h:selectOneMenu
                                                                id="relatorioGeralAgendamentos-listaHorarioFinal"
                                                                value="#{RelatorioGeralAgendamentosControle.horaFinal}"
                                                                onblur="blurinput(this);"
                                                                onfocus="focusinput(this);"
                                                                converter="timeConverter2"
                                                                styleClass="form">
                                                            <f:selectItem itemLabel="" itemValue=""/>
                                                            <f:selectItems value="#{RelatorioGeralAgendamentosControle.selectHorario}" />
                                                        </h:selectOneMenu>

                                                        <rich:spacer width="20px"/>

                                                        <a4j:commandButton image="imagens/estudio/limpar_filtros.png"
                                                                           reRender="agendaGeral"
                                                                           title="Limpar Filtros"
                                                                           action="#{RelatorioGeralAgendamentosControle.limparDados}">
                                                        </a4j:commandButton>

                                                        <a4j:commandButton style="float:right;"
                                                                           action="#{RelatorioGeralAgendamentosControle.acaoPesquisar}"
                                                                           image="imagens/estudio/pesquisar.png"
                                                                           title="Pesquisa Inteligente"
                                                                           reRender="agendaGeral">
                                                        </a4j:commandButton>
                                                    </h:panelGrid>

                                                    <h:panelGrid id="Impressao" columns="3" style="float:right;">
                                                        <a4j:commandButton id="imprimirPDF"
                                                                           value="Imprimir"
                                                                           ajaxSingle="false"
                                                                           styleClass="botoes"
                                                                           style="float:right;"
                                                                           image="imagens/estudio/imprimir.png"
                                                                           action="#{RelatorioGeralAgendamentosControle.acaoImprimir}"
                                                                           rendered="#{not empty RelatorioGeralAgendamentosControle.listaRelatorioGeralAgendamentos}"
                                                                           oncomplete="abrirPopupPDFImpressao('relatorio/#{RelatorioGeralAgendamentosControle.nomeArquivoRelatorioGeradoAgora}','', 780, 595);">
                                                        </a4j:commandButton>

                                                        <a4j:commandButton id="exportarExcel"
                                                                           value="Exportar como planilha"
                                                                           styleClass="botoes"
                                                                           style="float:right;"
                                                                           image="imagens/estudio/excel.png"
                                                                           action="#{RelatorioGeralAgendamentosControle.acaoImprimirExcel}"
                                                                           rendered="#{not empty RelatorioGeralAgendamentosControle.listaRelatorioGeralAgendamentos}"
                                                                           oncomplete="location.href='DownloadSV?mimeType=application/vnd.ms-excel&relatorio=geralAgendamentosEstudioPactoExcel.xlsx'">
                                                        </a4j:commandButton>
                                                    </h:panelGrid>
                                                </h:panelGrid>
                                            </h:panelGrid>

                                            <h:panelGrid id="mensagemConsulta"
                                                         columns="3"
                                                         width="100%"
                                                         styleClass="tabMensagens">
                                                <a4j:commandButton rendered="#{RelatorioGeralAgendamentosControle.sucesso}" image="./imagens/sucesso.png" />
                                                <a4j:commandButton rendered="#{RelatorioGeralAgendamentosControle.erro}" image="./imagens/erro.png" />
                                                <h:panelGrid columns="1" width="100%">
                                                    <h:outputText id="msgNormal" styleClass="mensagem" value="#{RelatorioGeralAgendamentosControle.mensagem}" />
                                                    <h:outputText id="msgDetalhada" styleClass="mensagemDetalhada" value="#{RelatorioGeralAgendamentosControle.mensagemDetalhada}" />
                                                </h:panelGrid>
                                            </h:panelGrid>

                                            <h:panelGrid id="tabelaTodosAgendamentos" columns="1" width="100%">
                                                <rich:extendedDataTable id="tabelaAgendamentos"
                                                                        width="100%"
                                                                        height="500px"
                                                                        headerClass="consulta"
                                                                        columnClasses="colunaEsquerda,colunaEsquerda,colunaEsquerda,colunaEsquerda,colunaEsquerda,colunaEsquerda,colunaEsquerda,
                                                            					               colunaEsquerda,colunaEsquerda,colunaEsquerda,colunaEsquerda,colunaDireita,colunaDireita,colunaDireita,colunaDireita"
                                                                        rowClasses="linhaImpar, linhaPar"
                                                                        styleClass="textsmall"
                                                                        rendered="#{not empty RelatorioGeralAgendamentosControle.listaRelatorioGeralAgendamentos}"
                                                                        value="#{RelatorioGeralAgendamentosControle.listaRelatorioGeralAgendamentos}"
                                                                        binding="#{RelatorioGeralAgendamentosControle.extendedDataTable}"
                                                                        rowKeyVar="numeroLinha"
                                                                        var="item">
                                                    <rich:column label="Sequencial"
                                                                 id="item-sequencial"
                                                                 width="3%">
                                                        <f:facet name="header">
                                                            <h:outputText title="Sequencial" value="S."/>
                                                        </f:facet>

                                                        <h:outputText value="#{numeroLinha + 1}"/>
                                                    </rich:column>

                                                    <rich:column label="Matr�cula"
                                                                 id="codgMatricula"
                                                                 sortable="true"
                                                                 width="4%"
                                                                 selfSorted="true"
                                                                 sortBy="#{item.codgMatricula}"
                                                                 filterEvent="onkeyup">
                                                        <f:facet name="header">
                                                            <h:outputText title="Matr�cula" value="Mt."/>
                                                        </f:facet>

                                                        <a4j:commandLink action="#{RelatorioGeralAgendamentosControle.editarAgendamento}"
                                                                         reRender="toolTipAgenda">
                                                            <h:outputText value="#{item.codgMatricula}"/>
                                                        </a4j:commandLink>
                                                    </rich:column>

                                                    <rich:column label="Cliente"
                                                                 id="descCliente"
                                                                 width="16%"
                                                                 sortable="true"
                                                                 selfSorted="true"
                                                                 sortBy="#{item.descCliente}"
                                                                 filterEvent="onkeyup">
                                                        <f:facet name="header">
                                                            <h:outputText title="Cliente" value="Cliente"/>
                                                        </f:facet>

                                                        <a4j:commandLink action="#{RelatorioGeralAgendamentosControle.editarAgendamento}"
                                                                         reRender="toolTipAgenda">
                                                            <h:outputText value="#{item.descCliente}"/>
                                                        </a4j:commandLink>
                                                    </rich:column>

                                                    <rich:column label="Data"
                                                                 id="dataAula"
                                                                 width="6%"
                                                                 sortable="true"
                                                                 selfSorted="true"
                                                                 sortBy="#{item.dataAula}"
                                                                 filterEvent="onkeyup">
                                                        <f:facet name="header">
                                                            <h:outputText title="Data Agendada" value="Data"/>
                                                        </f:facet>

                                                        <a4j:commandLink action="#{RelatorioGeralAgendamentosControle.editarAgendamento}"
                                                                         reRender="toolTipAgenda">
                                                            <h:outputText value="#{item.dataAula}" converter="dataConverter"/>
                                                        </a4j:commandLink>
                                                    </rich:column>

                                                    <rich:column label="Hor�rio"
                                                                 id="horaInicio"
                                                                 sortable="true"
                                                                 width="4%"
                                                                 selfSorted="true"
                                                                 sortBy="#{item.horaInicio}"
                                                                 filterEvent="onkeyup">
                                                        <f:facet name="header">
                                                            <h:outputText title="Hor�rio" value="Hr."/>
                                                        </f:facet>

                                                        <a4j:commandLink action="#{RelatorioGeralAgendamentosControle.editarAgendamento}"
                                                                         reRender="toolTipAgenda">
                                                            <h:outputText value="#{item.horaInicio}" converter="timeConverter"/>
                                                            <h:outputText rendered="#{item.horaTermino!=null}" value="-" escape="false"/>
                                                            <h:outputText rendered="#{item.horaTermino!=null}" value="#{item.horaTermino}" converter="timeConverter"/>
                                                        </a4j:commandLink>
                                                    </rich:column>

                                                    <rich:column label="Servi�o"
                                                                 id="descProduto"
                                                                 width="12%"
                                                                 sortable="true"
                                                                 selfSorted="true"
                                                                 sortBy="#{item.descProduto}"
                                                                 filterEvent="onkeyup">
                                                        <f:facet name="header">
                                                            <h:outputText title="Servi�o" value="Servi�o"/>
                                                        </f:facet>

                                                        <a4j:commandLink action="#{RelatorioGeralAgendamentosControle.editarAgendamento}"
                                                                         reRender="toolTipAgenda"
                                                                         title="#{item.descProduto}">
                                                            <h:outputText value="#{item.descProduto}"/>
                                                        </a4j:commandLink>
                                                    </rich:column>

                                                    <rich:column label="Profissional"
                                                                 id="descColaborador"
                                                                 width="10%"
                                                                 sortable="true"
                                                                 selfSorted="true"
                                                                 sortBy="#{item.descColaborador}"
                                                                 filterEvent="onkeyup">
                                                        <f:facet name="header">
                                                            <h:outputText title="Profissional" value="Profissional"/>
                                                        </f:facet>

                                                        <a4j:commandLink action="#{RelatorioGeralAgendamentosControle.editarAgendamento}"
                                                                         reRender="toolTipAgenda">
                                                            <h:outputText value="#{item.descColaborador}"/>
                                                        </a4j:commandLink>
                                                    </rich:column>

                                                    <rich:column label="Ambiente"
                                                                 id="descAmbiente"
                                                                 width="10%"
                                                                 sortable="true"
                                                                 selfSorted="true"
                                                                 sortBy="#{item.descAmbiente}"
                                                                 filterEvent="onkeyup">
                                                        <f:facet name="header">
                                                            <h:outputText title="Ambiente" value="Ambiente"/>
                                                        </f:facet>

                                                        <a4j:commandLink action="#{RelatorioGeralAgendamentosControle.editarAgendamento}"
                                                                         reRender="toolTipAgenda">
                                                            <h:outputText value="#{item.descAmbiente}"/>
                                                        </a4j:commandLink>
                                                    </rich:column>

                                                    <rich:column label="Status"
                                                                 id="descStatus"
                                                                 sortable="true"
                                                                 selfSorted="true"
                                                                 width="8%"
                                                                 sortBy="#{item.descStatus_Apresentar}"
                                                                 filterEvent="onkeyup">
                                                        <f:facet name="header">
                                                            <h:outputText title="Status" value="Status"/>
                                                        </f:facet>

                                                        <a4j:commandLink action="#{RelatorioGeralAgendamentosControle.editarAgendamento}"
                                                                         reRender="toolTipAgenda">
                                                            <h:outputText value="#{item.descStatus_Apresentar}"/>
                                                        </a4j:commandLink>
                                                    </rich:column>

                                                    <rich:column label="Tipo do Hor�rio"
                                                                 id="descTipoHorario"
                                                                 sortable="true"
                                                                 width="3%"
                                                                 selfSorted="true"
                                                                 sortBy="#{item.descTipoHorario.sigla}"
                                                                 filterEvent="onkeyup">
                                                        <f:facet name="header">
                                                            <h:outputText title="Tipo do Hor�rio" value="T.H."/>
                                                        </f:facet>

                                                        <a4j:commandLink action="#{RelatorioGeralAgendamentosControle.editarAgendamento}"
                                                                         reRender="toolTipAgenda" >
                                                            <h:outputText value="#{item.descTipoHorario.sigla}"/>
                                                        </a4j:commandLink>
                                                    </rich:column>

                                                    <rich:column label="Situa��o"
                                                                 id="situacaoParcela"
                                                                 sortable="true"
                                                                 width="8%"
                                                                 selfSorted="true"
                                                                 sortBy="#{item.situacaoParcela.descricao}"
                                                                 filterEvent="onkeyup">
                                                        <f:facet name="header">
                                                            <h:outputText title="Situa��o" value="Situa��o"/>
                                                        </f:facet>

                                                        <a4j:commandLink action="#{RelatorioGeralAgendamentosControle.editarAgendamento}"
                                                                         reRender="toolTipAgenda" >
                                                            <h:outputText value="#{item.situacaoParcela.descricao}"/>
                                                        </a4j:commandLink>
                                                    </rich:column>

                                                    <rich:column label="Quantidade Vendida"
                                                                 id="qtdVendida"
                                                                 width="4%"
                                                                 sortable="true"
                                                                 selfSorted="true"
                                                                 sortBy="#{item.qtdVendida}"
                                                                 filterEvent="onkeyup">
                                                        <f:facet name="header">
                                                            <h:outputText title="Quantidade Vendida" value="Q.V."/>
                                                        </f:facet>

                                                        <a4j:commandLink action="#{RelatorioGeralAgendamentosControle.editarAgendamento}"
                                                                         reRender="toolTipAgenda"
                                                                         title="#{item.qtdVendida}">
                                                            <h:outputText value="#{item.qtdVendida}"/>
                                                        </a4j:commandLink>
                                                    </rich:column>

                                                    <rich:column label="Quantidade Utilizada"
                                                                 id="qtdUtilizada"
                                                                 width="4%"
                                                                 sortable="true"
                                                                 selfSorted="true"
                                                                 sortBy="#{item.qtdUtilizada}"
                                                                 filterEvent="onkeyup">
                                                        <f:facet name="header">
                                                            <h:outputText title="Quantidade Utilizada" value="Q.U."/>
                                                        </f:facet>

                                                        <a4j:commandLink action="#{RelatorioGeralAgendamentosControle.editarAgendamento}"
                                                                         reRender="toolTipAgenda"
                                                                         title="#{item.qtdUtilizada}">
                                                            <h:outputText value="#{item.qtdUtilizada}"/>
                                                        </a4j:commandLink>
                                                    </rich:column>

                                                    <rich:column label="Quantidade a Agendar"
                                                                 id="qtdAgendar"
                                                                 width="4%"
                                                                 sortable="true"
                                                                 selfSorted="true"
                                                                 sortBy="#{item.qtdAgendar}"
                                                                 filterEvent="onkeyup">
                                                        <f:facet name="header">
                                                            <h:outputText title="Quantidade a Agendar" value="Q.A."/>
                                                        </f:facet>

                                                        <a4j:commandLink action="#{RelatorioGeralAgendamentosControle.editarAgendamento}"
                                                                         reRender="toolTipAgenda"
                                                                         title="#{item.qtdAgendar}">
                                                            <h:outputText value="#{item.qtdAgendar}"/>
                                                        </a4j:commandLink>
                                                    </rich:column>

                                                    <rich:column label="Quantidade Restante"
                                                                 id="qtdRestante"
                                                                 width="4%"
                                                                 sortable="true"
                                                                 selfSorted="true"
                                                                 sortBy="#{item.qtdRestante}"
                                                                 filterEvent="onkeyup">
                                                        <f:facet name="header">
                                                            <h:outputText title="Quantidade Restante" value="Q.R."/>
                                                        </f:facet>

                                                        <a4j:commandLink action="#{RelatorioGeralAgendamentosControle.editarAgendamento}"
                                                                         reRender="toolTipAgenda"
                                                                         title="#{item.qtdRestante}">
                                                            <h:outputText value="#{item.qtdRestante}"/>
                                                        </a4j:commandLink>
                                                    </rich:column>
                                                </rich:extendedDataTable></h:panelGrid>

                                            <h:panelGrid id="totalizadores" columns="3" rendered="true"
                                                         columnClasses="colunaTopCentralizada, colunaTopCentralizada, colunaTopCentralizada">
                                                <rich:dataTable columnClasses="colunaTopCentralizada"
                                                                rowClasses="linhaImpar, linhaPar"
                                                                headerClass="consulta"
                                                                rendered="#{not empty RelatorioGeralAgendamentosControle.statusTotalizador}"
                                                                value="#{RelatorioGeralAgendamentosControle.statusTotalizador}"
                                                                var="status">
                                                    <rich:column style="text-align:left;">
                                                        <f:facet name="header">
                                                            <h:outputText title="Status" value="Status"/>
                                                        </f:facet>
                                                        <h:outputText value="#{status.descStatus_Apresentar}"/>
                                                    </rich:column>

                                                    <rich:column style="text-align:right;">
                                                        <f:facet name="header">
                                                            <h:outputText title="Quantidade" value="Qtd."/>
                                                        </f:facet>
                                                        <h:outputText value="#{status.totalizador}"/>
                                                    </rich:column>
                                                </rich:dataTable>

                                                <rich:dataTable columnClasses="colunaTopCentralizada"
                                                                rowClasses="linhaImpar, linhaPar"
                                                                headerClass="consulta"
                                                                rendered="#{not empty RelatorioGeralAgendamentosControle.tipoHorarioTotalizador}"
                                                                value="#{RelatorioGeralAgendamentosControle.tipoHorarioTotalizador}"
                                                                var="tipo">
                                                    <rich:column style="text-align:left;">
                                                        <f:facet name="header">
                                                            <h:outputText title="Tipo do Hor�rio" value="Tipo do Hor�rio"/>
                                                        </f:facet>
                                                        <h:outputText value="#{tipo.descTipoHorario_Apresentar}"/>
                                                    </rich:column>

                                                    <rich:column style="text-align:right;">
                                                        <f:facet name="header">
                                                            <h:outputText title="Quantidade" value="Qtd."/>
                                                        </f:facet>
                                                        <h:outputText value="#{tipo.totalizador}"/>
                                                    </rich:column>
                                                </rich:dataTable>

                                                <rich:dataTable columnClasses="colunaTopCentralizada"
                                                                rowClasses="linhaImpar, linhaPar"
                                                                headerClass="consulta"
                                                                rendered="#{not empty RelatorioGeralAgendamentosControle.situacaoTotalizador}"
                                                                value="#{RelatorioGeralAgendamentosControle.situacaoTotalizador}"
                                                                var="situacao">
                                                    <rich:column style="text-align:left;">
                                                        <f:facet name="header">
                                                            <h:outputText title="Situa��o" value="Situa��o"/>
                                                        </f:facet>
                                                        <h:outputText value="#{situacao.situacaoParcela_Apresentar}"/>
                                                    </rich:column>

                                                    <rich:column style="text-align:right;">
                                                        <f:facet name="header">
                                                            <h:outputText title="Quantidade" value="Qtd."/>
                                                        </f:facet>
                                                        <h:outputText value="#{situacao.totalizador}"/>
                                                    </rich:column>
                                                </rich:dataTable>
                                            </h:panelGrid>
                                        </h:panelGrid>
                                    </h:panelGroup>
                                </h:panelGroup>

                            </h:panelGroup>
                            <jsp:include page="menuRelatorio.jsp" flush="true"/>
                        </h:panelGroup>
                    </h:panelGroup>
                </h:panelGroup>
                <jsp:include page="include_rodape_flat.jsp" flush="true" />
            </h:panelGroup>
        </h:form>

<%------------------------------------------------------------ MODAL FILTRO SERVI�O ------------------------------------------------------------%>
        <rich:modalPanel id="panelFiltroServico"
        				 autosized="true"
        				 shadowOpacity="true"
        				 showWhenRendered="false"
        				 width="50" 
                         height="240"
                         onmaskclick="#{rich:component('panelFiltroServico')}.hide();"
                         onshow="focusAt('panelFiltroServico-servico-codigo'); #{rich:component('mykeyservico')}.enable();"
                         onhide="#{rich:component('mykeyservico')}.disable();">
            <f:facet name="header">
                <h:panelGroup>
                    <h:outputText value="Servi�o" styleClass="texto_disponibilidade"/>
                </h:panelGroup>
            </f:facet>
            
            <f:facet name="controls">
                <h:panelGroup>
                    <h:graphicImage value="imagens/close.png" style="cursor:pointer" id="hidelinkPanelFiltroServico"/>
                    <rich:componentControl for="panelFiltroServico" attachTo="hidelinkPanelFiltroServico" operation="hide" event="onclick"/>
                </h:panelGroup>
            </f:facet>

            <a4j:form id="formPanelFiltroServico" prependId="false" ajaxSingle="true">
            	<a4j:commandButton reRender="" id="btnLimpaConsulta" action="#{RelatorioGeralAgendamentosControle.acaoSairModalServico}" style="visibility: hidden;"/>
				<h:inputHidden id="teste" value="#{RelatorioGeralAgendamentosControle.produtoVO.codigo}" />
            	
                <h:panelGrid columns="3" id="dadosServico">
                    <h:outputLabel value="C�digo" styleClass="texto_disponibilidade"/>
                    
                    <rich:spacer width="20px"/>
                    
                    <h:outputLabel value="Descri��o" styleClass="texto_disponibilidade"/>

                    <h:inputText maxlength="4"
                        		 autocomplete="off"
                        		 onkeydown="return tabOnEnter(event, 'panelFiltroServico-servico-descricao');"
                        		 size="3"
                        		 onblur="blurinput(this);"
                        		 onfocus="focusinput(this); getById('panelFiltroServico-servico-codigo').select();"
                        		 styleClass="form"
                        		 value="#{RelatorioGeralAgendamentosControle.produtoVO.codigo}"
                        		 id="panelFiltroServico-servico-codigo">
                        <a4j:support event="onchange"
                        			 oncomplete="focusAt('add');"
                        			 action="#{RelatorioGeralAgendamentosControle.acaoProcurarServico}"
                                     reRender="panelFiltroServico-servico-descricao,modalServicoSuggestion,modalPanelErro">
                        </a4j:support>
                    </h:inputText>
                    
                    <rich:spacer width="20px"/>
                    
                    <h:panelGrid columns="4">
                        <h:inputText maxlength="50"
                            		 autocomplete="off"
                            		 style="width: 290px;"
                            		 onblur="blurinput(this);"
                            		 onfocus="focusinput(this); getById('panelFiltroServico-servico-descricao').select();"
                            		 onkeydown="return tabOnEnter(event, 'add');"
                            		 styleClass="form"
                            		 value="#{RelatorioGeralAgendamentosControle.produtoVO.descricao}"
                            		 id="panelFiltroServico-servico-descricao">
                        </h:inputText>
                        
                        <rich:suggestionbox id="modalServicoSuggestion"
                            				width="290"
                            				status="none"
                            				immediate="true"
                            				for="panelFiltroServico-servico-descricao"
                            				suggestionAction="#{RelatorioGeralAgendamentosControle.listarServico}"
                            				minChars="1"
											rowClasses="30"
                            				fetchValue="#{item.descricao}"
                            				var="item"
                            				nothingLabel="Nenhum dado encontrado">
                            <h:column>
                                <h:outputText value="#{item.descricao}"/>
                            </h:column>
                            
                            <a4j:support event="onselect" reRender="panelFiltroServico-servico-codigo" focus="add">
                                <f:setPropertyActionListener
                                    target="#{RelatorioGeralAgendamentosControle.produtoVO}"
                                    value="#{item}"/>
                            </a4j:support>
                        </rich:suggestionbox>
                        
                        <rich:spacer width="10px"/>
                        
                        <a4j:commandButton image="imagens/estudio/adicionar.png"
                            			   value="Adicionar"
                            			   id="add"
                            			   focus="panelFiltroServico-servico-codigo"
                                           reRender="relatorioGeralAgendamentosControle-listaServico,dadosServico,modalServicoSuggestion,modalPanelErro"
                            			   action="#{RelatorioGeralAgendamentosControle.acaoAdicionarServico}"
                            			   title="Adiciona o servi�o informado a tabela.">
                        </a4j:commandButton>
                    </h:panelGrid>
                </h:panelGrid>
				
                <rich:spacer height="5"/>
                
                <h:panelGrid columns="1" width="100%">
                    <rich:scrollableDataTable id="relatorioGeralAgendamentosControle-listaServico"
                    						  columnClasses="colunaTopCentralizada"
											  rowClasses="linhaImpar, linhaPar"
											  headerClass="consulta"
											  frozenColCount="1"
                        					  height="180px"
                        					  width="482px"
                        					  var="item"
                        					  value="#{RelatorioGeralAgendamentosControle.listaServicoTabela}"
                        					  onRowClick="getById('relatorioGeralAgendamentosControle-listaServico:'+ this.rowIndex +':filtro').onclick();">

                        <rich:column width="30px"
                                     style="text-align:center;"
                        			 sortable="false">
                            <f:facet name="header">
                                <h:selectBooleanCheckbox id="selecionarTodosProduto"
                                						 value="#{RelatorioGeralAgendamentosControle.selecionarTodosItemServico}">
                                    <a4j:support event="onclick"
                                        		 action="#{RelatorioGeralAgendamentosControle.acaoSelecionarTodosServico}"
                                        		 reRender="itemSolicitacao-selecionado-servico">
                                    </a4j:support>
                                </h:selectBooleanCheckbox>
                            </f:facet>
                            
                            <h:selectBooleanCheckbox id="itemSolicitacao-selecionado-servico"
                                					 value="#{item.selecionado}">
                                <a4j:support event="onclick"
                                    		 action="#{RelatorioGeralAgendamentosControle.acaoSelecionarUmServico}"
                                    		 reRender="selecionarTodosProduto">
                                </a4j:support>
                            </h:selectBooleanCheckbox>
                        </rich:column>

                        <rich:column width="50px"
                                     style="text-align:right;"
                                     sortable="true"
                                     selfSorted="true"
                                     filterEvent="onkeyup"
                                     sortBy="#{item.codigo}">
                            <f:facet name="header">
                                <h:outputText value="C�digo"/>
                            </f:facet>
                            
                            <h:outputText value="#{item.codigo}"/>
                        </rich:column>
                        
                        <rich:column width="365px"
                                     style="text-align:left;"
                                     sortable="true"
                                     selfSorted="true"
                                     filterEvent="onkeyup"
                                     sortBy="#{item.descricao}">
                            <f:facet name="header">
                                <h:outputText value="Descri��o"/>
                            </f:facet>
                            
                            <h:outputText value="#{item.descricao}"/>
                        </rich:column>
                        
                        <rich:column width="30px"
                                     style="text-align:center;"
                        			 sortable="false">
                            <a4j:commandLink action="#{RelatorioGeralAgendamentosControle.acaoRemoverServico}"
                                             reRender="relatorioGeralAgendamentosControle-listaServico">
                                <h:graphicImage value="imagens/estudio/icon_delete.png" style="cursor:pointer" id="removerItem"/>
                                <f:setPropertyActionListener target="#{RelatorioGeralAgendamentosControle.produtoVO}" value="#{item}"/>
                            </a4j:commandLink>
                        </rich:column>
                    </rich:scrollableDataTable>
                </h:panelGrid>

				<rich:hotKey id="mykeyservico" key="esc" handler="#{rich:component('panelFiltroServico')}.hide();"/>
            </a4j:form>
        </rich:modalPanel>

<%------------------------------------------------------------ MODAL FILTRO AMBIENTE ------------------------------------------------------------%>
        <rich:modalPanel id="panelFiltroAmbiente"
        				 autosized="true"
        				 shadowOpacity="true"
                         showWhenRendered="false"
                         width="550"
                         height="280"
                         onshow="#{rich:component('mykeyambiente')}.enable();"
                         onhide="#{rich:component('mykeyambiente')}.disable();"
                         onmaskclick="#{rich:component('panelFiltroAmbiente')}.hide();">
            <f:facet name="header">
                <h:panelGroup>
                    <h:outputText value="Ambiente" styleClass="texto_disponibilidade"/>
                </h:panelGroup>
            </f:facet>
            
            <f:facet name="controls">
                <h:panelGroup>
                    <h:graphicImage value="imagens/close.png" style="cursor:pointer" id="hidelinkPanelFiltroAmbiente"/>
                    <rich:componentControl for="panelFiltroAmbiente" attachTo="hidelinkPanelFiltroAmbiente"
                                           operation="hide" event="onclick"/>
                </h:panelGroup>
            </f:facet>

            <a4j:form id="formPanelFiltroAmbiente" prependId="false">
                <h:outputText value="Selecione o ambiente que deseja pesquisar:" styleClass="texto_disponibilidade"/>
                
                <div style="overflow-y: auto; overflow-x: hidden; height: 180px; padding-top: 05px;">
                    <h:selectManyCheckbox id="ambienteSelect"
                        				  style="text-align: left;"
                        				  styleClass="texto_disponibilidade"
                        				  layout="pageDirection"
                        				  value="#{RelatorioGeralAgendamentosControle.listaAmbienteSelecionado}">
                        <f:selectItems value="#{RelatorioGeralAgendamentosControle.listaAmbienteSelect}"/>
                        <a4j:support event="onchange" reRender="formPanelFiltroAmbiente"/>
                    </h:selectManyCheckbox>
                </div>
                
                <rich:hotKey id="mykeyambiente" key="esc" handler="#{rich:component('panelFiltroAmbiente')}.hide();"/>
            </a4j:form>
        </rich:modalPanel>

<%------------------------------------------------------------ MODAL FILTRO PROFISSIONAL ------------------------------------------------------------%>
        <rich:modalPanel id="panelFiltroProfissional"
        				 autosized="true"
        				 shadowOpacity="true"
                         showWhenRendered="false"
                         width="700"
                         height="280"
                         onshow="#{rich:component('mykeyprofissional')}.enable();"
                         onhide="#{rich:component('mykeyprofissional')}.disable();"
                         onmaskclick="#{rich:component('panelFiltroProfissional')}.hide();">
            <f:facet name="header">
                <h:panelGroup>
                    <h:outputText value="Profissional" styleClass="texto_disponibilidade"/>
                </h:panelGroup>
            </f:facet>

            <f:facet name="controls">
                <h:panelGroup>
                    <h:graphicImage value="imagens/close.png" style="cursor:pointer" id="hidelinkPanelFiltroProfissional"/>
                    <rich:componentControl for="panelFiltroProfissional" attachTo="hidelinkPanelFiltroProfissional"
                                           operation="hide" event="onclick"/>
                </h:panelGroup>
            </f:facet>

            <a4j:form id="formPanelFiltroProfissional" >
                <h:outputText value="Selecione o profissional que deseja pesquisar:" styleClass="texto_disponibilidade"/>
                
                <div style="overflow-y: auto; overflow-x: hidden; height: 180px; padding-top: 05px;">
                    <h:panelGrid id="panelProfessores" width="100%" columns="2">
                        <h:selectManyCheckbox id="profissionalSelect1"
                                              style="text-align: left;"
                                              styleClass="texto_disponibilidade"
                                              layout="pageDirection"
                                              value="#{RelatorioGeralAgendamentosControle.listaUmColaboradorSelecionado}">
                            <f:selectItems value="#{RelatorioGeralAgendamentosControle.listaUmPessoaSelect}"/>
                            <a4j:support event="onchange" reRender="formPanelFiltroProfissional"/>
                        </h:selectManyCheckbox>

                        <h:selectManyCheckbox id="profissionalSelect2"
                                              style="text-align: left;"
                                              styleClass="texto_disponibilidade"
                                              layout="pageDirection"
                                              value="#{RelatorioGeralAgendamentosControle.listaDoisColaboradorSelecionado}">
                            <f:selectItems value="#{RelatorioGeralAgendamentosControle.listaDoisPessoaSelect}"/>
                            <a4j:support event="onchange" reRender="formPanelFiltroProfissional"/>
                        </h:selectManyCheckbox>
                    </h:panelGrid>
                </div>
                
                <rich:hotKey id="mykeyprofissional" key="esc" handler="#{rich:component('panelFiltroProfissional')}.hide();"/>
            </a4j:form>
        </rich:modalPanel>

<%------------------------------------------------------------ MODAL FILTRO TIPO HOR�RIO ------------------------------------------------------------%>
        <rich:modalPanel id="panelFiltroTipoHorario"
        				 autosized="true"
        				 shadowOpacity="true"
                         showWhenRendered="false"
                         width="550"
                         height="280"
                         onshow="#{rich:component('mykeytipohorario')}.enable();"
                         onhide="#{rich:component('mykeytipohorario')}.disable();"
                         onmaskclick="#{rich:component('panelFiltroTipoHorario')}.hide();">
            <f:facet name="header">
                <h:panelGroup>
                    <h:outputText value="Tipo Hor�rio" styleClass="texto_disponibilidade"/>
                </h:panelGroup>
            </f:facet>
            
            <f:facet name="controls">
                <h:panelGroup>
                    <h:graphicImage value="imagens/close.png" style="cursor:pointer" id="hidelinkPanelFiltroTipoHorario"/>
                    <rich:componentControl for="panelFiltroTipoHorario" attachTo="hidelinkPanelFiltroTipoHorario"
                                           operation="hide" event="onclick"/>
                </h:panelGroup>
            </f:facet>

            <a4j:form id="formPanelFiltroTipoHorario" prependId="false">
                <h:outputText value="Selecione o tipo de hor�rio que deseja pesquisar:" styleClass="texto_disponibilidade"/>
                
                <div style="overflow-y: auto; overflow-x: hidden; height: 180px; padding-top: 05px;">
                    <h:selectManyCheckbox id="tipoHorarioSelect"
                        				  style="text-align: left;"
                        				  styleClass="texto_disponibilidade"
                        				  layout="pageDirection"
                        				  value="#{RelatorioGeralAgendamentosControle.listaTipoHorarioSelecionado}">
                        <f:selectItems value="#{RelatorioGeralAgendamentosControle.listaTipoHorarioSelect}"/>
                        <a4j:support event="onchange" reRender="formPanelFiltroTipoHorario"/>
                    </h:selectManyCheckbox>
                </div>
                
                <rich:hotKey id="mykeytipohorario" key="esc" handler="#{rich:component('panelFiltroTipoHorario')}.hide();"/>
            </a4j:form>
        </rich:modalPanel>

<%------------------------------------------------------------ MODAL FILTRO STATUS ------------------------------------------------------------%>
        <rich:modalPanel id="panelFiltroStatus"
						 autosized="true"
						 shadowOpacity="true"
                         showWhenRendered="false"
                         width="550"
                         height="280"
                         onshow="#{rich:component('mykeystatus')}.enable();"
                         onhide="#{rich:component('mykeystatus')}.disable();"
                         onmaskclick="#{rich:component('panelFiltroStatus')}.hide();">
            <f:facet name="header">
                <h:panelGroup>
                    <h:outputText value="Status da Sess�o" styleClass="texto_disponibilidade"/>
                </h:panelGroup>
            </f:facet>
            
            <f:facet name="controls">
                <h:panelGroup>
                    <h:graphicImage value="imagens/close.png" style="cursor:pointer" id="hidelinkPanelFiltroStatus"/>
                    <rich:componentControl for="panelFiltroStatus" attachTo="hidelinkPanelFiltroStatus"
                                           operation="hide" event="onclick"/>
                </h:panelGroup>
            </f:facet>

            <a4j:form id="formPanelFiltroStatus" prependId="false">
                <h:outputText value="Selecione o status da sess�o que deseja pesquisar:" styleClass="texto_disponibilidade"/>
                
                <div style="overflow-y: auto; overflow-x: hidden; height: 180px; padding-top: 05px;">
                    <h:selectManyCheckbox id="statusSelect"
                        				  style="text-align: left;"
                        				  styleClass="texto_disponibilidade"
                        				  layout="pageDirection"
                        				  value="#{RelatorioGeralAgendamentosControle.listaStatusSelecionado}">
                        <f:selectItems value="#{RelatorioGeralAgendamentosControle.listaStatusSelect}"/>
                        <a4j:support event="onchange" reRender="formPanelFiltroStatus"/>
                    </h:selectManyCheckbox>
                </div>
                
                <rich:hotKey id="mykeystatus" key="esc" handler="#{rich:component('panelFiltroStatus')}.hide();"/>
            </a4j:form>
        </rich:modalPanel>
        
                    
        <%@include file="pages/estudio/includes/include_modal_dados_aluno.jsp"%>
        <%@include file="pages/estudio/includes/include_modal_agenda_aluno.jsp"%>
        <%@include file="pages/estudio/includes/include_modal_erro.jsp" %>
        <%@include file="pages/estudio/includes/include_modal_sucesso.jsp" %>
        
    	</f:view>
	</body>
</html>
