<%@page contentType="text/html" %>
<%@page pageEncoding="ISO-8859-1" %>
<head>
    <%@include file="/includes/include_import_minifiles.jsp" %>
</head>
<head>
    <link href="./beta/css/pure-min.css" rel="stylesheet" type="text/css">
    <link href="./beta/css/pure-ext.css" rel="stylesheet" type="text/css">
    <link href="./css/otimize.css" rel="stylesheet" type="text/css">
    <link href="./css/smartbox/smartbox.css" rel="stylesheet" type="text/css">
    <link href="./css_pacto.css" rel="stylesheet" type="text/css"/>
    <link href="./beta/css/font-awesome_2.0.min.css" rel="stylesheet" type="text/css">
    <style type="text/css">
        .porcentagemBoxBody{
            font-size: 30px;
            color: rgb(71, 71, 71);
        }
        .pecentBoxBody{
            font-size: 20px;

        }
        .tituloTotalMetas{
            font-family:"Trebuchet MS", Verdana, Arial, Helvetica, sans-serif;
            color:black;
            font-weight: 100;
            font-size: 16px;
        }
        .rich-table-subfooter {
            background-color: #CCCCCC;;

        }
        .rich-table-subfootercell {
            border-right: solid 1px transparent !important;
            text-align: center;
        }
        .resultadoBoxHeader{
            height: 37px;
            width: 121px;
            background-color: rgb(192, 192, 192);

        }
        .resultadoBoxBody{
            height: 77px;
            width: 119px;
            background-color: rgba(235, 255, 73, 0);

            border-bottom:solid 1px rgb(192, 192, 192) ;
            border-left:solid 1px rgb(192, 192, 192);
            border-right: solid 1px rgb(192, 192, 192);
        }

        .rich-table-subheadercell  {
            border-right: solid 1px transparent !important;
            padding: 4px 4px 4px 4px;
            height: 25px;
            text-align: left;
            font-size: 11px;
            color: #474747;
            order-bottom: solid 1px #C0C0C0;
            padding: 4px 4px 4px 4px;
            font-size: 11px;
            color: #474747;
            font-family: Arial,Verdana,sans-serif;
            white-space: nowrap;
        }
        .alinharDireita{
            text-align: right;
        }
        .rich-table-subheadercell{
            border-right: none;
        }
        body {
            margin: 0;
            padding: 0;
        }
        .rich-table-subfooter.classeTabela{
            height: 33px;
        }
        .rich-table-cell.colunaEsquerda.tabelaSumario  {
            padding: 0 !important;
        }

    </style>
</head>

<%@taglib prefix="f" uri="http://java.sun.com/jsf/core" %>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html" %>

<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax" %>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>

<f:loadBundle var="msg_bt" basename="propriedades.Botoes"/>
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao"/>
<f:loadBundle var="msg" basename="propriedades.Mensagens"/>
<f:view>
    <jsp:include page="includes/include_carregando_ripple.jsp" flush="true"/>
    <h:panelGrid columns="1" width="100%" styleClass="crm" cellpadding="0" cellspacing="0">
        <f:facet name="header">
            <jsp:include page="topoReduzidoCRM.jsp"/>
        </f:facet>


        <h:panelGroup styleClass="inicioCarteiras">

                <h:panelGroup layout="block" styleClass="blocoCarteiras pure-u-11-12 margin-0-auto" style="width: 100%;border-radius:0;height:40px;">
                    <h3>Totalizador de Metas</h3>

                        <h:form id="formTotalizadorMetaResultado">
                            <h:panelGrid id="panelGridForm" columns="1" width="100%"
                                         rendered="true">

                                <h:panelGrid columns="1" width="100%">
                                    <rich:spacer style="display:block" height="12"/>
                                    <h:panelGrid columns="2" width="100%" cellpadding="0" style="margin-left: 7px">

                                        <h:outputText styleClass="tituloCampos"
                                                      value="#{TotalizadorMetaControle.periodoFiltro}"/>
                                        <rich:spacer width="10px;"/>
                                        <h:outputText styleClass="tituloCampos"
                                                      value="#{TotalizadorMetaControle.metaFiltro}"/>
                                        <rich:spacer width="10px;"/>
                                        <h:outputText styleClass="tituloCampos"
                                                      value="#{TotalizadorMetaControle.colaboradoresFiltro}"/>
                                    </h:panelGrid>
                                    <rich:spacer style="display:block" height="5"/>
                                   <%--<h:panelGrid columns="9" style="background-color: rgb(204, 204, 204);width: 100%;height:25px">--%>
                                        <%--<rich:spacer width="20"/>--%>
                                        <%--<h:outputText  style="font-size: 8pt;--%>
                                        <%--font-weight: bold;border-right:solid 1px silver;" styleClass="textsmall"   value="        Identificador Metas"/>--%>
                                        <%--<rich:spacer width="0"/>--%>
                                        <%--<h:outputText styleClass="textsmall" style="color: #474747;font-size: 8pt;font-weight: bold"  value="Metas"/>--%>
                                        <%--<rich:spacer width="0"/>--%>
                                        <%--<h:outputText styleClass="textsmall" style="color: #474747;font-size: 8pt;border-right:solid 1px rgb(192, 192, 192);;font-weight: bold"  value="Meta Atingida"/>--%>
                                        <%--<rich:spacer width="20"/>--%>
                                        <%--<h:outputText styleClass="textsmall" style="color: #474747;font-size: 8pt;border-right:solid 1px rgb(192, 192, 192);;font-weight: bold"  value="Repescagem"/>--%>
                                        <%--<rich:spacer width="0"/>--%>
                                        <%--<h:outputText styleClass="textsmall" style="color: #474747;font-size: 8pt;border-right:solid 1px rgb(192, 192, 192);;font-weight: bold"  value="%"/>--%>
                                    <%--</h:panelGrid >--%>

                                    <rich:spacer height="9"/>

                                    <h:outputText style="font-family:Trebuchet MS, Verdana, Arial, Helvetica, sans-serif;color:black;font-weight: 100;font-size: 16px;;margin-left:4px;margin-left: 7px"  value="Relacionamento de Vendas"/>
                                    <rich:spacer height="5"/>
                                    <rich:dataTable id="itemsFecharMetaRenovacao" width="98%" headerClass="consulta"
                                                    columnClasses="colunaEsquerda"
                                                    rowClasses="linhaImpar, linhaPar" style="border:none;margin-left: 7px"


                                                    value="#{TotalizadorMetaControle.fasesRenovacao}" var="fecharMeta">
                                      <rich:column style="border:none;border-right: none;text-align: left !important;">
                                          <div style="text-align: left!important;">
                                          <f:facet name="header">
                                              <h:outputText style="vertical-align: middle;text-align: left!important;" value="#{msg_aplic.prt_FechamentoDia_identificadorMeta}"/>
                                          </f:facet>
                                          </div>
                                          <f:facet name="footer">
                                              <h:outputText styleClass="camposIndicadorVendasCRM"  style="position: absolute;margin-top: -7px;margin-left:-13.2%;" value="Total"/>
                                          </f:facet>
                                            <h:outputText style="#{fecharMeta.corMeta}" styleClass="camposIndicadorVendasCRM"
                                                          value="#{fecharMeta.identificadorMeta_Apresentar}"/>
                                        </rich:column>
                                        <rich:column

                                                style="border:none; vertical-align: top; text-align: center; width: 18%;">
                                            <f:facet name="header">
                                                <h:outputText   value="#{msg_aplic.prt_FechamentoDia_metas}"/>
                                            </f:facet>
                                            <f:facet name="footer">
                                                <h:outputText styleClass="camposIndicadorVendasCRM" value="#{TotalizadorMetaControle.metaRenovacao.meta_Apresentar}"/>
                                            </f:facet>
                                            <h:outputText style="#{fecharMeta.corMeta}" styleClass="camposIndicadorVendasMetaCRM"
                                                          value="#{fecharMeta.meta_Apresentar}"/>
                                        </rich:column>
                                        <rich:column
                                                style="border:none; vertical-align: top; text-align: center;width: 18%;">
                                            <f:facet name="header">
                                                <h:outputText styleClass="textsmall"   value="#{msg_aplic.prt_FechamentoDia_metaAtingida}"/>
                                            </f:facet>
                                            <f:facet name="footer">
                                                <h:outputText styleClass="camposIndicadorVendasCRM" value="#{TotalizadorMetaControle.metaRenovacao.metaAtingida_Apresentar}"/>
                                            </f:facet>
                                            <h:outputText style="#{fecharMeta.corMeta}" styleClass="camposIndicadorVendasMetaAtingidaCRM"
                                                          value="#{fecharMeta.metaAtingida_Apresentar}"/>
                                        </rich:column>
                                        <rich:column
                                                style="border:none; vertical-align: top; text-align: center; width: 18%;">
                                            <f:facet name="header">
                                                <h:outputText styleClass="textsmall"   value="#{msg_aplic.prt_FechamentoDia_repescagem}"/>
                                            </f:facet>
                                            <f:facet name="footer">
                                                <h:outputText styleClass="camposIndicadorVendasCRM" value="#{TotalizadorMetaControle.metaRenovacao.repescagem_Apresentar}"/>
                                            </f:facet>
                                            <h:outputText style="#{fecharMeta.corMeta}" styleClass="camposIndicadorVendasMetaAtingidaCRM"
                                                          value="#{fecharMeta.repescagem_Apresentar}">
                                            </h:outputText>
                                        </rich:column>
                                        <rich:column
                                                style="border:none; vertical-align: top; text-align: center; width: 18%;">
                                            <f:facet name="header">
                                                <h:outputText styleClass="textsmall"   value="%"/>
                                            </f:facet>
                                            <f:facet name="footer">
                                                <h:outputText  styleClass="camposIndicadorVendasCRM"  style="#{TotalizadorMetaControle.metaRenovacao.corPorcentagemBox}" value="#{TotalizadorMetaControle.metaRenovacao.porcentagem}">
                                                    <f:converter converterId="FormatadorNumerico"/>
                                                </h:outputText>

                                            </f:facet>
                                            <h:outputText style="#{fecharMeta.corPorcentagemBox}" value="#{fecharMeta.porcentagem}"
                                                          styleClass="camposIndicadorVendasMetaPorcentagemCRM">
                                                <f:converter converterId="FormatadorNumerico"/>
                                            </h:outputText>
                                        </rich:column>
                                       </rich:dataTable>
                                    <rich:spacer height="15"/>
                                    <h:outputText style="margin-left:7px;font-family:Trebuchet MS, Verdana, Arial, Helvetica, sans-serif;color:black;font-weight: 100;font-size: 16px;"  value="Relacionamento de Fidelização"/>
                                    <rich:spacer height="5"/>
                                    <rich:dataTable id="itemsFecharMetaRelacionamento" width="98%"  headerClass="consulta"
                                                    columnClasses="colunaEsquerda"
                                                    rowClasses="linhaImpar, linhaPar" style="border:none;margin-left: 7px"

                                                    value="#{TotalizadorMetaControle.fasesRelacionamento}" var="fecharMeta">
                                        <rich:column style="border:none; width: 28%;text-align: left !important;">
                                            <f:facet name="header">
                                                <h:outputText style="vertical-align: middle;text-align: center" value="#{msg_aplic.prt_FechamentoDia_identificadorMeta}"/>
                                            </f:facet>
                                            <f:facet name="footer">
                                                <h:outputText styleClass="camposIndicadorVendasCRM"  style="position: absolute;margin-top: -7px;margin-left: -13.2%;" value="Total"/>
                                            </f:facet>
                                            <h:outputText style="#{fecharMeta.corMeta}" styleClass="camposIndicadorVendasCRM"
                                                          value="#{fecharMeta.identificadorMeta_Apresentar}"/>
                                        </rich:column>
                                        <rich:column
                                                style="border:none; vertical-align: top; text-align: center; width: 18%;">
                                            <f:facet name="header">
                                                <h:outputText   value="#{msg_aplic.prt_FechamentoDia_metas}"/>
                                            </f:facet>
                                            <f:facet name="footer">
                                                <h:outputText   styleClass="camposIndicadorVendasMetaCRM" value="#{TotalizadorMetaControle.metaFidelizacao.meta_Apresentar}"/>
                                            </f:facet>
                                            <h:outputText style="#{fecharMeta.corMeta}" styleClass="camposIndicadorVendasMetaCRM"
                                                          value="#{fecharMeta.meta_Apresentar}"/>
                                        </rich:column>
                                        <rich:column
                                                style="border:none; vertical-align: top; text-align: center;width: 18%;">
                                            <f:facet name="header">
                                                <h:outputText styleClass="textsmall"   value="#{msg_aplic.prt_FechamentoDia_metaAtingida}"/>
                                            </f:facet>
                                            <f:facet name="footer">
                                                <h:outputText  styleClass="camposIndicadorVendasCRM"  value="#{TotalizadorMetaControle.metaFidelizacao.metaAtingida_Apresentar}"/>
                                            </f:facet>
                                            <h:outputText style="#{fecharMeta.corMeta}" styleClass="camposIndicadorVendasMetaAtingidaCRM"
                                                          value="#{fecharMeta.metaAtingida_Apresentar}"/>
                                        </rich:column>
                                        <rich:column
                                                style="border:none; vertical-align: top; text-align: center; width: 18%;">
                                            <f:facet name="header">
                                                <h:outputText styleClass="textsmall"  value="#{msg_aplic.prt_FechamentoDia_repescagem}"/>
                                            </f:facet>
                                            <f:facet name="footer">
                                                <h:outputText  styleClass="camposIndicadorVendasCRM"  value="#{TotalizadorMetaControle.metaFidelizacao.repescagem_Apresentar}"/>
                                            </f:facet>
                                            <h:outputText style="#{fecharMeta.corMeta}" styleClass="camposIndicadorVendasMetaAtingidaCRM"
                                                          value="#{fecharMeta.repescagem_Apresentar}">
                                            </h:outputText>
                                        </rich:column>
                                        <rich:column
                                                style="border:none; vertical-align: top; text-align: center; width: 18%;">
                                            <f:facet name="header">
                                                <h:outputText styleClass="textsmall"   value="%"/>
                                            </f:facet>
                                            <f:facet name="footer">
                                                <h:outputText  styleClass="camposIndicadorVendasCRM"  style="#{TotalizadorMetaControle.metaFidelizacao.corPorcentagemBox}" value="#{TotalizadorMetaControle.metaFidelizacao.porcentagem}">
                                                    <f:converter converterId="FormatadorNumerico"/>
                                                </h:outputText>
                                            </f:facet>

                                            <h:outputText style="#{fecharMeta.corPorcentagemBox}" value="#{fecharMeta.porcentagem}"
                                                          styleClass="camposIndicadorVendasMetaPorcentagemCRM">
                                                <f:converter converterId="FormatadorNumerico"/>
                                            </h:outputText>
                                        </rich:column>
                                    </rich:dataTable>
                                    <rich:spacer height="15"/>

                                    <rich:dataTable width="98%" headerClass="consulta"
                                                    id="teste"
                                                    columnClasses="colunaEsquerda tabelaSumario"
                                                    footerClass="classeTabela"
                                                    rowClasses="linhaImpar, linhaPar"
                                                    styleClass="classeTabela"
                                                    style="border:none;margin-left: 7px"
                                                    value="1"
                                                    >

                                        <rich:column style="border:none;text-align: left !important;">
                                            <f:facet name="footer">
                                                <h:outputText styleClass="tituloTotalMetas" style="position: absolute;margin-left: -13.2%;margin-top: -9px;height: 40px"  value="Total Metas"/>
                                            </f:facet>
                                        </rich:column>
                                        <rich:column
                                                style="border:none; vertical-align: top; text-align: center; width: 18%;">
                                            <f:facet name="footer">
                                                <h:outputText styleClass="tituloTotalMetas" value="#{TotalizadorMetaControle.metaTotal.meta_Apresentar}"/>
                                            </f:facet>
                                        </rich:column>
                                        <rich:column style="border:none; vertical-align: top; text-align: center;width: 18%;">
                                            <f:facet name="footer">
                                                <h:outputText  styleClass="tituloTotalMetas"
                                                              value="#{TotalizadorMetaControle.metaTotal.metaAtingida_Apresentar}"/>
                                            </f:facet>
                                        </rich:column>
                                        <rich:column
                                                style="border:none; vertical-align: top; text-align: center; width: 18%;">
                                            <f:facet name="footer">
                                                <h:outputText styleClass="tituloTotalMetas"
                                                              value="#{TotalizadorMetaControle.metaTotal.repescagem_Apresentar}"/>
                                            </f:facet>
                                        </rich:column>
                                        <rich:column style="border:none; vertical-align: top; text-align: center; width: 18%;">
                                            <f:facet name="footer">

                                                <h:outputText  styleClass="tituloTotalMetas"  style="#{TotalizadorMetaControle.metaTotal.corPorcentagemBox}" value="#{TotalizadorMetaControle.metaTotal.porcentagem}">
                                                    <f:converter converterId="FormatadorNumerico"/>
                                                </h:outputText>
                                            </f:facet>

                                        </rich:column>
                                    </rich:dataTable>
                                   <rich:spacer height="20"/>
                                  <rich:spacer style="display:block" height="2"/>
                                   <h:panelGrid columns="3" width="100%">
                                       <h:panelGroup layout="block" style="margin: 0 auto; width: 800px;">
                                         <h:panelGrid style="margin-left: 7px" columns="2" width="100%">
                                           <h:outputText styleClass="tituloTotalMetas"
                                                         value="#{msg_aplic.prt_FechamentoDia_resultado}:"/>
                                       </h:panelGrid>
                                   <h:panelGrid columns="6" cellpadding="6">
                                    <c:forEach   var="meta" items="#{TotalizadorMetaControle.totaisResultado}">
                                    <h:panelGroup style="height:500px">
                                    <div class="resultadoBoxHeader" style="text-align: center;">
                                    <h:panelGrid columns="1" style="text-align: center" width="121">
                                    <c:if test="${meta.quebrarLinhaPorcentagem}">
                                    <rich:spacer height="7"/>
                                    </c:if>
                                    <c:if test="${!meta.quebrarLinhaPorcentagem}">
                                            <rich:spacer height="4"/>
                                        </c:if>
                                        <h:panelGroup layout="block" style="text-align: center;margin:0 auto;width:85px;padding-left: 10px;padding-right: 10px;word-wrap: break-word" >
                                      <h:outputText  style="width: 85px;font-family:Trebuchet MS;font-size: 11px;text-decoration: none;color: rgb(51,51,51);"
                                                  value="#{meta.identificadorMeta_Apresentar}"/>
                                        </h:panelGroup>
                                    </h:panelGrid>
                                    </div>
                                    <div class="resultadoBoxBody">
                                    <h:panelGrid columns="1" width="121" style="text-align: center;padding-top: 40px;margin-top: 12px;position: absolute;">
                                        <h:panelGroup>
                                        <h:outputText  styleClass="porcentagemBoxBody" style="#{meta.corPorcentagemBox}" value="#{meta.porcentagemBoxResultado_Apresentar}"/>
                                            <h:outputText  style="#{meta.corPorcentagemBox}" styleClass="percentBoxBody"  value="%" />
                                        </h:panelGroup>
                                        <h:outputText value="#{meta.metaBoxResultado_Apresentar}"/>
                                    </h:panelGrid>
                                    </div>
                                    </h:panelGroup>
                                    </c:forEach>

                                        </h:panelGrid>
                                       </h:panelGroup>

                                </h:panelGrid>

                                <rich:spacer height="12"/>
                                <h:panelGrid columns="2" style="    position: absolute;right: 12px;">

                                        <h:panelGrid columns="2" columnClasses="alinharDireita,alinharDireita">
                                        <h:commandLink id="consultar1" immediate="true"
                                                         action="#{TotalizadorMetaControle.inicializarConsultar}"
                                                         value="Voltar"

                                                         styleClass="pure-button"
                                                         title="#{msg.msg_consultar_dados}"
                                                         accesskey="2" />

                                        <a4j:commandLink id="imprimirPDF"
                                                         value="Imprimir"
                                                           action="#{TotalizadorMetaControle.imprimirRelatorio}"
                                                           oncomplete="location.href=\"relatorio/#{TotalizadorMetaControle.nomeArquivoRelatorioGeradoAgora}\""

                                                           reRender="form"
                                                         styleClass="pure-button pure-button-primary pull-right"
                                                         />
                                        </h:panelGrid>
                                </h:panelGrid>
                                <h:panelGrid columns="2" width="100%" cellpadding="0" style="margin-top: -25;margin-left: 7px">
                                    <h:panelGrid columns="1" width="100%" cellpadding="0">
                                        <f:verbatim>
                                            <h:outputText value=" "/>
                                        </f:verbatim>
                                    </h:panelGrid>
                                    <rich:spacer height="16"/>
                                    <h:panelGroup>
                                        <rich:spacer height="15"/>
                                    <h:commandButton rendered="#{TotalizadorMetaControle.sucesso}"
                                                     image="./imagensCRM/sucesso.png"/>
                                    <rich:spacer width="3px"/>
                                    <h:commandButton rendered="#{TotalizadorMetaControle.erro}"
                                                     image="./imagensCRM/erro.png"/>
                                        <rich:spacer width="3px"/>
                                    <h:outputText  styleClass="mensagemDetalhada"
                                                  value="#{TotalizadorMetaControle.mensagemDetalhada}"/>
                                        <rich:spacer width="3px"/>
                                    <h:outputText  styleClass="mensagem"
                                                  value="#{TotalizadorMetaControle.mensagem}"/>
                                    </h:panelGroup>
                                </h:panelGrid>
                            </h:panelGrid>
                            </h:panelGrid>
                        </h:form>
                </h:panelGroup>
            </h:panelGroup>


    </h:panelGrid>
</f:view>