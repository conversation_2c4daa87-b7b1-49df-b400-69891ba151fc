<%@page pageEncoding="ISO-8859-1" %>
<head><%@include file="/includes/include_import_minifiles.jsp" %></head>
<link href="./css/otimize.css" rel="stylesheet" type="text/css">
<link href="beta/css/font-awesome_2.0.min.css" type="text/css" rel="stylesheet"/>
<%@taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<%@taglib prefix="f" uri="http://java.sun.com/jsf/core"%>
<%@taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions"%>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html"%>
<script type="text/javascript" language="javascript" src="hoverform.js"></script>
<script type="text/javascript" language="javascript" src="script/time_1.3.js"></script>
<script src="script/packJQueryPlugins.min.js" type="text/javascript"></script>
<script type="text/javascript" src="script/tooltipster/jquery.tooltipster.min.js"></script>
<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax"%>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich"%>
<f:loadBundle var="msg_bt" basename="propriedades.Botoes"/>
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao"/>
<f:loadBundle var="msg" basename="propriedades.Mensagens"/>
<%@include file="/includes/verificaModulo.jsp" %>
<c:set var="contexto" value="${pageContext.request.contextPath}" scope="request" />

<script type="text/javascript" language="javascript">
    setTimeout(function () {
        setDocumentCookie('popupsImportante', '', 1);
    }, 500);
    function validar() {

        if (document.getElementById('formConsultarCEP:estadoCEP').value == ""
            && document.getElementById('formConsultarCEP:cidadeCEP').value == ""
            && document.getElementById('formConsultarCEP:bairroCEP').value == ""
            && document.getElementById('formConsultarCEP:logradouroCEP').value == "") {

            alert("Ao menos um parâmetro deve ser informado!");
            return false;
        }


        return true;
    }
</script>

<f:view locale="#{SuperControle.idioma}">
    <a4j:loadScript src="/script/jquery.maskedinput-1.2.2.js" />
    <title>${msg_aplic.prt_Colaborador_tituloForm}</title>

    <c:set var="titulo" scope="session" value="${msg_aplic.prt_Colaborador_tituloForm}"/>
    <c:set var="urlWiki" scope="session" value="${SuperControle.urlBaseConhecimento}como-cadastrar-um-novo-colaborador/"/>

    <rich:modalPanel id="panelExistePessoa" autosized="true" shadowOpacity="true" showWhenRendered="#{ColaboradorControle.pessoaVO.apresentarRichModalErro}" width="450" height="150">
        <f:facet name="header">
            <h:panelGroup>
                <h:outputText value="Atenção!"/>
            </h:panelGroup>
        </f:facet>
        <a4j:form id="formExistePessoa" ajaxSubmit="true">
            <h:panelGrid columns="1" width="100%" columnClasses="colunaCentralizada" >
                <h:panelGrid columns="1" footerClass="colunaCentralizada" width="100%">
                    <h:outputText styleClass="tituloCampos" value="#{msg.msg_existePessoa}"/>
                </h:panelGrid>

                <h:panelGrid columns="2" width="20%" headerClass="subordinado" columnClasses="colunaCentralizada">
                    <a4j:commandButton action="#{ColaboradorControle.adicionarPessoa}" reRender="form" oncomplete="Richfaces.hideModalPanel('panelExistePessoa')" value="#{msg_bt.btn_sim}" image="./imagens/botaoSim.png" accesskey="5" styleClass="botaoEspecial"/>
                    <a4j:commandButton action="#{ColaboradorControle.setarFalso}" reRender="form" onclick="Richfaces.hideModalPanel('panelExistePessoa')"  value="#{msg_bt.btn_nao}" image="./imagens/botaoNao.png" accesskey="6" styleClass="botaoEspecial"/>
                </h:panelGrid>
            </h:panelGrid>
        </a4j:form>
    </rich:modalPanel>

    <rich:modalPanel id="panelExisteColaborador" autosized="true" shadowOpacity="true" showWhenRendered="#{ColaboradorControle.colaboradorVO.apresentarRichModalErro}" width="450" height="150">
        <f:facet name="header">
            <h:panelGroup>
                <h:outputText value="Atenção!"/>
            </h:panelGroup>
        </f:facet>
        <a4j:form id="formExisteColaborador">
            <h:panelGrid columns="1" width="100%" columnClasses="colunaCentralizada">
                <h:panelGrid columns="1" footerClass="colunaCentralizada" width="100%">
                    <h:outputText styleClass="tituloCampos" value="#{ColaboradorControle.colaboradorVO.msgErroExisteColaborador}"/>
                </h:panelGrid>

                <h:panelGrid columns="2" width="20%" headerClass="subordinado" columnClasses="colunaCentralizada">
                    <%--- <a4j:commandButton  rendered="#{ColaboradorControle.colaboradorVO.apresentarBotaoTransferirColaboradorEmpresa}" action="#{ColaboradorControle.gravarColaboradorTrocandoEmpresa}" reRender="form" oncomplete="Richfaces.hideModalPanel('panelExisteCliente')" image="./imagens/botaoTransferirColaboradorParaEstaEmpresa.png"/> --%>
                    <a4j:commandButton  rendered="#{!ClienteControle.clienteVO.apresentarBotaoTransferirClienteEmpresa}" action="#{ColaboradorControle.editarColaborador}" reRender="form" oncomplete="Richfaces.hideModalPanel('panelExisteCliente')" image="./imagens/botaoEditarDadosDoCliente.png"/>
                    <a4j:commandButton reRender="form" oncomplete="Richfaces.hideModalPanel('panelExisteColaborador')" value="Fechar" image="./imagens/botaoFechar.png" accesskey="5" styleClass="botaoEspecial"/>

                </h:panelGrid>
            </h:panelGrid>
        </a4j:form>
    </rich:modalPanel>

    <rich:modalPanel id="panelProfissao" styleClass="novaModal" autosized="true" shadowOpacity="true" width="450" height="250">
        <f:facet name="header">
            <h:panelGroup>
                <h:outputText value="Cadastro de Profissão"/>
            </h:panelGroup>
        </f:facet>
        <f:facet name="controls">
            <h:panelGroup>
                <h:outputText
                        styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign btnSelecionarModalidade"
                        id="hidelink1"/>
                <rich:componentControl for="panelProfissao" attachTo="hidelink1" operation="hide" event="onclick"/>
            </h:panelGroup>
        </f:facet>

        <a4j:form id="formProfissao" ajaxSubmit="true" styleClass="paginaFontResponsiva">
            <h:panelGrid columns="1"  width="100%" columnClasses="colunaCentralizada">
                <h:panelGrid columns="2"  rowClasses="linhaImpar, linhaPar"  columnClasses="classEsquerda, classDireita" width="100%">

                    <h:outputText   value="#{msg_aplic.prt_Profissao_codigo}" />
                    <h:panelGroup>
                        <h:inputText  id="codigo"  size="10" maxlength="10" readonly="true"  onblur="blurinput(this);"  onfocus="focusinput(this);" styleClass="camposSomenteLeitura"  value="#{ColaboradorControle.profissaoVO.codigo}" />
                        <h:message for="codigo" styleClass="mensagemDetalhada"/>
                    </h:panelGroup>
                    <h:outputText   value="#{msg_aplic.prt_Profissao_descricao}" />
                    <h:panelGroup>
                        <h:inputText  id="nomeProfissao" required="true" size="45" maxlength="45"  onblur="blurinput(this);"  onfocus="focusinput(this);" styleClass="form"  value="#{ColaboradorControle.profissaoVO.descricao}" />
                        <h:message for="nomeProfissao" styleClass="mensagemDetalhada"/>
                    </h:panelGroup>

                </h:panelGrid>

                <h:panelGrid id="panelMensagem" columns="1" width="100%" styleClass="tabMensagens">
                    <h:panelGrid columns="1" width="100%">
                        <h:outputText styleClass="mensagem"  value="#{ColaboradorControle.mensagem}"/>
                        <h:outputText styleClass="mensagemDetalhada" value="#{ColaboradorControle.mensagemDetalhada}" escape="false"/>
                    </h:panelGrid>
                    <h:panelGroup layout="block" styleClass="container-botoes">

                            <c:if test="${modulo eq 'zillyonWeb'}">
                                <a4j:commandLink id="salvar" reRender="form" action="#{ColaboradorControle.gravarProfissao}"
                                                 oncomplete="Richfaces.hideModalPanel('panelProfissao')"
                                                 value="#{msg_bt.btn_gravar}"
                                                 title="#{msg.msg_gravar_dados}" accesskey="2" styleClass="botaoPrimario texto-size-14"/>
                            </c:if>
                            <c:if test="${modulo eq 'centralEventos'}">
                                <a4j:commandLink id="salvar" reRender="form"
                                                   action="#{ColaboradorControle.gravarProfissao}" oncomplete="Richfaces.hideModalPanel('panelProfissao')"
                                                   value="#{msg_bt.btn_gravar}" title="#{msg.msg_gravar_dados}" accesskey="2" styleClass="botaoPrimario texto-size-14"/>
                            </c:if>

                    </h:panelGroup>
                </h:panelGrid>
            </h:panelGrid>

        </a4j:form>
    </rich:modalPanel>

    <rich:modalPanel id="panelDepartamento" styleClass="novaModal" autosized="true" shadowOpacity="true" width="450" height="250">
        <f:facet name="header">
            <h:panelGroup>
                <h:outputText value="Cadastro de Departamento"/>
            </h:panelGroup>
        </f:facet>
        <f:facet name="controls">
            <h:panelGroup>
                <h:outputText
                        styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign btnSelecionarModalidade"
                        id="hidelinkDep"/>
                <rich:componentControl for="panelDepartamento" attachTo="hidelinkDep" operation="hide" event="onclick"/>
            </h:panelGroup>
        </f:facet>

        <a4j:form id="formDepartamento" ajaxSubmit="true">
            <h:panelGrid columns="1"  width="100%" columnClasses="colunaCentralizada">

                <h:panelGrid columns="2"  rowClasses="linhaImpar, linhaPar"  columnClasses="classEsquerda, classDireita" width="100%">

                    <h:outputText   value="#{msg_aplic.prt_Departamento_codigo}" />
                    <h:panelGroup>
                        <h:inputText  id="codigoDep"  size="10" maxlength="10" readonly="true"  onblur="blurinput(this);"  onfocus="focusinput(this);" styleClass="camposSomenteLeitura"  value="#{ColaboradorControle.departamentoVO.codigo}" />
                        <h:message for="codigo" styleClass="mensagemDetalhada"/>
                    </h:panelGroup>
                    <h:outputText   value="#{msg_aplic.prt_Departamento_nome}" />
                    <h:panelGroup>
                        <h:inputText  id="nomeDepartamento" required="true" size="45" maxlength="100"  onblur="blurinput(this);"  onfocus="focusinput(this);" styleClass="form"  value="#{ColaboradorControle.departamentoVO.nome}" />
                        <h:message for="nomeDepartamento" styleClass="mensagemDetalhada"/>
                    </h:panelGroup>
                    <h:outputText   value="#{msg_aplic.prt_Departamento_concessionario}" />
                    <h:selectBooleanCheckbox value="#{ColaboradorControle.departamentoVO.concessionario}"/>

                </h:panelGrid>

                <h:panelGrid id="panelMensagem" columns="1" width="100%" styleClass="tabMensagens">
                    <h:panelGrid columns="1" width="100%">
                        <h:outputText styleClass="mensagem"  value="#{ColaboradorControle.mensagem}"/>
                        <h:outputText styleClass="mensagemDetalhada" value="#{ColaboradorControle.mensagemDetalhada}" escape="false"/>
                    </h:panelGrid>
                    <h:panelGroup layout="block" styleClass="container-botoes">

                            <c:if test="${modulo eq 'zillyonWeb'}">
                                <a4j:commandLink id="salvar" reRender="form" action="#{ColaboradorControle.gravarDepartamento}"
                                                 oncomplete="Richfaces.hideModalPanel('panelDepartamento')"
                                                 value="#{msg_bt.btn_gravar}"
                                                 title="#{msg.msg_gravar_dados}" accesskey="2" styleClass="botaoPrimario texto-size-14"/>
                            </c:if>
                            <c:if test="${modulo eq 'centralEventos'}">
                                <a4j:commandLink id="salvar" reRender="form"
                                                   action="#{ColaboradorControle.gravarDepartamento}" oncomplete="Richfaces.hideModalPanel('panelDepartamento')"
                                                   value="#{msg_bt.btn_gravar}" title="#{msg.msg_gravar_dados}" accesskey="2" styleClass="botaoPrimario texto-size-14"/>
                            </c:if>

                     </h:panelGroup>
                </h:panelGrid>
            </h:panelGrid>

        </a4j:form>
    </rich:modalPanel>

    <rich:modalPanel id="panelCEP" autosized="true" styleClass="novaModal" shadowOpacity="true" width="500" height="400">
        <f:facet name="header">
            <h:panelGroup>
                <h:outputText value="#{msg_aplic.prt_CEP_tituloConsulta}"/>
            </h:panelGroup>
        </f:facet>
        <f:facet name="controls">
            <h:panelGroup>
                <h:outputText
                        styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign btnSelecionarModalidade"
                        id="hidelinkCEP"/>
                <rich:componentControl for="panelCEP" attachTo="hidelinkCEP" operation="hide" event="onclick"/>
            </h:panelGroup>
        </f:facet>
        <a4j:form id="formConsultarCEP" ajaxSubmit="true" styleClass="paginaFontResponsiva">
            <h:panelGrid columns="1" width="100%" columnClasses="colunaCentralizada">
                <h:panelGrid columns="2" columnClasses="classEsquerda" width="100%">
                    <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_CEP_estadoC_maiusculo}" />
                    <h:panelGroup layout="block" styleClass="cb-container" style="height: 40px;">
                        <h:selectOneMenu id="estadoCEP" styleClass="campos" value="#{ColaboradorControle.cepControle.cepVO.ufSigla}">
                            <f:selectItems value="#{ColaboradorControle.listaSelectItemRgUfPessoa}" />
                        </h:selectOneMenu>
                    </h:panelGroup>
                    <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_CEP_cidadeC_maiusculo}" />
                    <h:inputText id="cidadeCEP" size="20" styleClass="campos" value="#{ColaboradorControle.cepControle.cepVO.cidadeDescricao}" />
                    <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_CEP_bairro_maiusculo}" />
                    <h:inputText id="bairroCEP" size="20" styleClass="campos" value="#{ColaboradorControle.cepControle.cepVO.bairroDescricao}" />
                    <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_CEP_logradouro_maiusculo}" />
                    <h:inputText id="logradouroCEP" size="20" styleClass="campos" value="#{ColaboradorControle.cepControle.cepVO.enderecoLogradouro}" />
                </h:panelGrid>
                <h:panelGrid columns="1">

                    <h:panelGroup>
                        <h:outputText styleClass="textsmall" value="Informe o nome ou parte do seu logradouro, rua ou avenida. Não Inclua o tipo da via nem o número da sua casa." />
                    </h:panelGroup>
                </h:panelGrid>
                <h:panelGroup>
                    <c:if test="${modulo eq 'zillyonWeb'}">
                        <a4j:commandLink id="btnConsultarCEP" onclick="if(!validar()){return false;};"
                        reRender=":formConsultarCEP"
                        action="#{ColaboradorControle.cepControle.consultarCEPDetalhe}" styleClass="botaoPrimario texto-size-14" value="#{msg_bt.btn_consultar}"
                        title="#{msg.msg_consultar_dados}"/>
                    </c:if>
                    <c:if test="${modulo eq 'centralEventos'}">
                        <a4j:commandLink id="btnConsultarCEP" onclick="if(!validar()){return false;};"
                                            reRender="formConsultarCEP:mensagemConsultaCEP, formConsultarCEP:resultadoConsultaCEP, formConsultarCEP:scResultadoCEP"
                                            action="#{ColaboradorControle.cepControle.consultarCEPDetalhe}"
                                         styleClass="botaoPrimario texto-size-14" value="#{msg_bt.btn_consultar}"
                                         title="#{msg.msg_consultar_dados}"/>
                    </c:if>
                </h:panelGroup>
                <h:panelGroup layout="block" >
                    <rich:dataTable id="resultadoConsultaCEP" width="100%" styleClass="tabelaSimplesCustom"
                                    rendered="#{not empty ColaboradorControle.cepControle.listaConsultaCep}" value="#{ColaboradorControle.cepControle.listaConsultaCep}" rows="4" var="cep">
                        <rich:column>
                            <f:facet name="header">
                                <h:outputText style="float: left;" styleClass="texto-font texto-bold texto-cor-cinza texto-size-14" value="#{msg_aplic.prt_CEP_titulo}" />
                            </f:facet>
                            <h:panelGroup>
                                <a4j:commandLink action="#{ColaboradorControle.selecionarCep}" focus="CEP"
                                                 styleClass="texto-font texto-cor-cinza texto-size-14"
                                                 reRender="form:panelEnderecoColaborador1"
                                                 oncomplete="Richfaces.hideModalPanel('panelCEP')" value="#{cep.enderecoCep}" />
                            </h:panelGroup>
                        </rich:column>
                        <rich:column>
                            <f:facet name="header">
                                <h:outputText style="float: left;" styleClass="texto-font texto-bold texto-cor-cinza texto-size-14"  value="#{msg_aplic.prt_CEP_cidadeC}" />
                            </f:facet>
                            <h:panelGroup>
                                <a4j:commandLink action="#{ColaboradorControle.selecionarCep}" focus="CEP"
                                                 styleClass="texto-font texto-cor-cinza texto-size-14"
                                                 reRender="form:panelEnderecoColaborador1"
                                                 oncomplete="Richfaces.hideModalPanel('panelCEP')" value="#{cep.cidadeDescricao}" />
                            </h:panelGroup>
                        </rich:column>
                        <rich:column>
                            <f:facet name="header">
                                <h:outputText style="float: left;" styleClass="texto-font texto-bold texto-cor-cinza texto-size-14"  value="#{msg_aplic.prt_CEP_bairroC}" />
                            </f:facet>
                            <a4j:commandLink action="#{ColaboradorControle.selecionarCep}" focus="CEP"
                                             styleClass="texto-font texto-cor-cinza texto-size-14"
                                             reRender="form:panelEnderecoColaborador1"
                                             oncomplete="Richfaces.hideModalPanel('panelCEP')" value="#{cep.bairroDescricao}" />
                        </rich:column>
                        <rich:column>
                            <f:facet name="header">
                                <h:outputText styleClass="texto-font texto-bold texto-cor-cinza texto-size-14"  value="#{msg_aplic.prt_CEP_logradouroC}" />
                            </f:facet>
                            <a4j:commandLink action="#{ColaboradorControle.selecionarCep}" focus="CEP"
                                             styleClass="texto-font texto-cor-cinza texto-size-14"
                                             reRender="form:panelEnderecoColaborador1"
                                             oncomplete="Richfaces.hideModalPanel('panelCEP')" value="#{cep.enderecoLogradouro}" />
                        </rich:column>
                    </rich:dataTable>
                    <rich:datascroller id="scResultadoCEP" align="center" style="margin-top: 10px" styleClass="scrollPureCustom" renderIfSinglePage="false" for="formConsultarCEP:resultadoConsultaCEP" maxPages="10" />
                </h:panelGroup>
                <h:panelGrid id="mensagemConsultaCEP" columns="1" width="100%" styleClass="tabMensagens">
                    <h:panelGrid columns="1" width="100%">
                        <h:outputText styleClass="mensagem" value="#{ColaboradorControle.cepControle.mensagem}" />
                        <h:outputText styleClass="mensagemDetalhada" value="#{ColaboradorControle.cepControle.mensagemDetalhada}" />
                    </h:panelGrid>
                </h:panelGrid>
            </h:panelGrid>
        </a4j:form>
    </rich:modalPanel>

    <rich:modalPanel id="panelProduto" styleClass="novaModal" autosized="true" shadowOpacity="true" width="550"
                     height="300" onshow="document.getElementById('formProduto:consultarProduto').focus();">
        <f:facet name="header">
            <h:panelGroup>
                <h:outputText value="#{msg_aplic.prt_ItemVendaAvulsa_consultarProduto}"/>
            </h:panelGroup>
        </f:facet>
        <f:facet name="controls">
            <h:panelGroup>
                <h:outputText
                        styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign btnSelecionarModalidade"
                        id="hiperlinkProduto"/>
                <rich:componentControl for="panelProduto" attachTo="hiperlinkProduto" operation="hide" event="onclick"/>
            </h:panelGroup>
        </f:facet>
        <a4j:form id="formProduto" ajaxSubmit="true" styleClass="paginaFontResponsiva" >
            <h:panelGrid columns="1" width="100%">
                <h:panelGrid columns="4" width="100%" styleClass="paginaFontResponsiva">
                    <h:outputText styleClass="tituloCampos" value="#{msg.msg_consultar_por}"/>
                    <h:inputText id="valorConsultarProduto" styleClass="campos" value="#{ColaboradorControle.valorConsultarProduto}"/>
                    <a4j:commandLink id="btnConsultarProduto"
                                       reRender="formProduto:mensagemConsultarProduto, formProduto:resultadoConsultaProduto, formProduto:scResultadoProduto, formProduto"
                                       action="#{ColaboradorControle.consultarProduto}" styleClass="botaoPrimario texto-size-14"
                                       value="#{msg_bt.btn_consultar}" >

                    </a4j:commandLink>
                </h:panelGrid>
                <rich:dataTable id="resultadoConsultaProduto" width="100%" styleClass="tabelaSimplesCustom" rendered="#{not empty ColaboradorControle.listaProdutos}"
                                value="#{ColaboradorControle.listaProdutos}" rows="10" var="produto">
                    <rich:column styleClass="col-text-align-left" headerClass="col-text-align-left">
                        <f:facet name="header">
                            <h:outputText styleClass="rotuloCampos" value="CÓDIGO"/>
                        </f:facet>
                        <h:outputText styleClass="texto-font texto-size-14 texto-cor-cinza" value="#{produto.codigo}"/>
                    </rich:column>
                    <rich:column styleClass="col-text-align-left" headerClass="col-text-align-left">
                        <f:facet name="header">
                            <h:outputText value="DESCRIÇAO"/>
                        </f:facet>
                        <h:outputText styleClass="texto-font texto-size-14 texto-cor-cinza" value="#{produto.descricao}"/>
                    </rich:column>
                    <rich:column styleClass="col-text-align-center" headerClass="col-text-align-center">
                        <a4j:commandLink action="#{ColaboradorControle.selecionarProduto}" focus="produto"
                                           reRender="panelDadosColaborador, mensagem"
                                           oncomplete="Richfaces.hideModalPanel('panelProduto')"
                                           styleClass="linkPadrao texto-cor-azul texto-size-16" >
                            <h:outputText styleClass="texto-font" value="#{msg_bt.btn_selecionar}"/>
                            <h:outputText styleClass="fa-icon-arrow-right"/>
                        </a4j:commandLink>
                    </rich:column>
                </rich:dataTable>
                <rich:datascroller align="center" for="formProduto:resultadoConsultaProduto" renderIfSinglePage="false" styleClass="scrollPureCustom" maxPages="10" id="scResultadoProduto"/>
                <h:panelGrid id="mensagemConsultaProduto" columns="1" width="100%" styleClass="tabMensagens">
                    <h:panelGrid columns="1" width="100%">
                        <h:outputText styleClass="mensagem" value="#{ColaboradorControle.mensagem}"/>
                        <h:outputText styleClass="mensagemDetalhada" value="#{ColaboradorControle.mensagemDetalhada}" escape="false"/>
                    </h:panelGrid>
                </h:panelGrid>
            </h:panelGrid>
        </a4j:form>
    </rich:modalPanel>

    <h:panelGrid columns="1" width="100%" cellpadding="0" cellspacing="0">
        <f:facet name="header">
                <jsp:include page="topoReduzido_material.jsp"/>
        </f:facet>

        <h:form id="form">

            <h:panelGroup layout="block" style="display: flex; background: #FDFDB4; padding: 10px">
                <h:graphicImage value="images/pct-alert-triangle-yellow.svg" style="width: 16px; padding-right: 5px; padding-bottom: 2px;"/>
                <h:outputText style="color: #7D7D03; font-size: 14px"
                              value="Em breve, lançaremos uma nova estrutura para o perfil do colaborador. Fique de olho para não perder as novidades!"></h:outputText>
            </h:panelGroup>

            <input type="hidden" value="${modulo}" name="modulo"/>
            <h:panelGrid columns="1" width="100%">
                <hr style="border: none;"/>
                <h:panelGrid columns="2" rowClasses="linhaImpar, linhaPar" columnClasses="classEsquerda, classDireita" width="100%">
                    <h:outputText  rendered="#{ColaboradorControle.colaboradorVO.usuarioVO.administrador}" value="#{msg_aplic.prt_Colaborador_empresa}" />
                    <h:panelGroup rendered="#{ColaboradorControle.colaboradorVO.usuarioVO.administrador}">
                        <h:selectOneMenu id="empresa" styleClass="form" onblur="blurinput(this);"
                                         onfocus="focusinput(this);"
                                         value="#{ColaboradorControle.colaboradorVO.empresa.codigo}">
                            <f:selectItems value="#{ColaboradorControle.listaSelectItemEmpresa}"/>
                            <a4j:support event="onchange" oncomplete="#{ColaboradorControle.mensagemNotificar}"
                                         ignoreDupResponses="true"
                                         action="#{ColaboradorControle.preencherNacionalidade}"
                                         reRender="form"/>
                        </h:selectOneMenu>
                        <a4j:commandButton id="atualizar_empresa" action="#{ColaboradorControle.montarListaSelectItemEmpresa}" image="imagens/atualizar.png" ajaxSingle="true" reRender="form:empresa"/>
                        <h:message for="empresa" styleClass="mensagemDetalhada"/>
                    </h:panelGroup>
                </h:panelGrid>
                <rich:tabPanel width="100%" activeTabClass="true" headerAlignment="rigth" switchType="ajax">

                    <rich:tab id="abaDadosPessoais" label="Dados Pessoais" switchType="client">
                        <h:panelGrid id="panelDadosPessoaisColaborador" columns="1" width="100%" headerClass="subordinado" columnClasses="colunaCentralizada">
                            <f:facet name="header">
                                <h:outputText value="#{msg_aplic.prt_DadosPessoais_tituloForm}"/>
                            </f:facet>
                            <h:panelGrid columns="2" rowClasses="linhaImpar, linhaPar" columnClasses="classEsquerda, classDireita" width="100%">
                                <h:outputText value="#{msg_aplic.prt_Colaborador_departamento}" />
                                <h:panelGroup>
                                    <h:selectOneMenu onblur="blurinput(this);" onfocus="focusinput(this);" styleClass="form" id="departamento"
                                                     value="#{ColaboradorControle.colaboradorVO.departamentoVO.codigo}">
                                        <f:selectItems
                                                value="#{ColaboradorControle.listSelectItemDepartamentos}"/>
                                    </h:selectOneMenu>
                                    <a4j:commandButton id="atualizar_departamento" action="#{ColaboradorControle.montarListaDepartamentos}" image="imagens/atualizar.png" immediate="true" ajaxSingle="true" reRender="form:departamento" title="Atualizar departamentos"/>
                                    <a4j:commandButton id="consultaDadosDepartamento" alt="Cadastrar Departamento" reRender="formDepartamento" oncomplete="Richfaces.showModalPanel('panelDepartamento'), setFocus(formDepartamento,'formDepartamento:nomeDepartamento');" image="./images/icon_add.gif" title="Cadastrar departamento" />
                                </h:panelGroup>

                                <c:if test="${fn:contains(ColaboradorControle.listaCamposMostrarColaboradorDinamico, 'PROFISSAO')}">
                                    <h:panelGroup>
                                        <c:if test="${fn:contains(ColaboradorControle.listaCamposObrigatorioColaboradorDinamico, 'PROFISSAO')}">
                                            <h:outputLabel value="* "/>
                                        </c:if>
                                        <h:outputText   value="#{msg_aplic.prt_Pessoa_profissao}" />
                                    </h:panelGroup>
                                </c:if>
                                <c:if test="${fn:contains(ColaboradorControle.listaCamposMostrarColaboradorDinamico, 'PROFISSAO')}">
                                    <h:panelGroup>
                                        <h:selectOneMenu  id="profissao" onblur="blurinput(this);"  onfocus="focusinput(this);" styleClass="form" value="#{ColaboradorControle.pessoaVO.profissao.codigo}" >
                                            <f:selectItems  value="#{ColaboradorControle.listaSelectItemProfissao}" />
                                        </h:selectOneMenu>
                                        <a4j:commandButton id="atualizar_profissao" action="#{ColaboradorControle.montarListaSelectItemProfissao}" image="imagens/atualizar.png" immediate="true" ajaxSingle="true" reRender="form:profissao" title="Atualizar profissão"/>
                                        <a4j:commandButton id="consultaDadosProfissao" action="#{ProfissaoControle.inicializarProfisaoControle}"  alt="Cadastrar Profissão" reRender="formProfissao" oncomplete="Richfaces.showModalPanel('panelProfissao'), setFocus(formProfissao,'formProfissao:nomeProfissao');" image="./images/icon_add.gif" title="Cadastrar profissão" />
                                        <h:message for="profissao" styleClass="mensagemDetalhada"/>
                                    </h:panelGroup>
                                </c:if>
                                <c:if test="${fn:contains(ColaboradorControle.listaCamposMostrarColaboradorDinamico, 'DATACADASTRO')}">
                                    <h:panelGroup>
                                        <c:if test="${fn:contains(ColaboradorControle.listaCamposObrigatorioColaboradorDinamico, 'DATACADASTRO')}">
                                            <h:outputLabel value="* "/>
                                        </c:if>
                                        <h:outputText   value="#{msg_aplic.prt_Pessoa_dataCadastro}" />
                                    </h:panelGroup>
                                </c:if>
                                <c:if test="${fn:contains(ColaboradorControle.listaCamposMostrarColaboradorDinamico, 'DATACADASTRO')}">
                                    <h:panelGroup>
                                        <rich:calendar id="dataCadastro"
                                                       value="#{ColaboradorControle.pessoaVO.dataCadastro}"
                                                       inputSize="10"
                                                       inputClass="form"
                                                       oninputblur="blurinput(this);"
                                                       oninputfocus="focusinput(this);"
                                                       oninputchange="return validar_Data(this.id);"
                                                       datePattern="dd/MM/yyyy"
                                                       enableManualInput="true"
                                                       zindex="2" readonly="true"
                                                       showWeeksBar="false"  />
                                        <h:message for="dataCadastro"  styleClass="mensagemDetalhada"/>
                                    </h:panelGroup>
                                </c:if>

                                  <h:outputText   value="#{msg_aplic.prt_Colaborador_nome}" />

                                <h:panelGroup>
                                    <h:inputText  id="nome"  size="50" maxlength="80" onblur="blurinput(this);"  onfocus="focusinput(this);" styleClass="form" value="#{ColaboradorControle.pessoaVO.nome}" />
                                    <h:message for="nome" styleClass="mensagemDetalhada"/>
                                </h:panelGroup>

                                <h:outputText   value="#{msg_aplic.prt_Colaborador_matricula_aluno}" rendered="#{LoginControle.usuarioLogado.administrador && ColaboradorControle.clienteVinculadoColaborador}"/>

                                <h:panelGroup rendered="#{LoginControle.usuarioLogado.administrador && ColaboradorControle.clienteVinculadoColaborador}">
                                    <h:inputText  id="vinculoAluno"  size="14" maxlength="80" disabled="true" styleClass="form" value="#{ColaboradorControle.clienteVinculado.matricula}" />
                                    <h:message for="nome" styleClass="mensagemDetalhada"/>
                                    <a4j:commandButton action="#{ColaboradorControle.confirmarSepararVinculoClienteColaborador}"
                                                       title="Separe os dados cadastrais do aluno e colaborador. Esta ação é utilizada quando o cliente e o colaborador não são a mesma pessoa."
                                                       reRender="form,panelExistePessoa,panelExisteColaborador,modalVinculoCliente,mdlMensagemGenerica"
                                                       oncomplete="#{ColaboradorControle.msgAlert}"
                                                       value="#{msg_bt.btn_separarClienteEColaborador}"
                                                       accesskey="6"
                                                       styleClass="botaoEspecial"/>
                                </h:panelGroup>

                                <c:if test="${fn:contains(ColaboradorControle.listaCamposMostrarColaboradorDinamico, 'NOMEREGISTRO')}">
                                    <h:panelGroup>
                                        <c:if test="${fn:contains(ColaboradorControle.listaCamposObrigatorioColaboradorDinamico, 'NOMEREGISTRO')}">
                                            <h:outputLabel value="* "/>
                                        </c:if>
                                        <h:outputText   value="#{msg_aplic.prt_Pessoa_nome_registro}" />
                                    </h:panelGroup>
                                </c:if>
                                <c:if test="${fn:contains(ColaboradorControle.listaCamposMostrarColaboradorDinamico, 'NOMEREGISTRO')}">
                                    <h:panelGroup>
                                        <h:inputText id="nomeRegistro" size="50" maxlength="80"
                                                     onblur="blurinput(this);" onfocus="focusinput(this);"
                                                     styleClass="form"
                                                     value="#{ColaboradorControle.pessoaVO.nomeRegistro}"/>
                                        <h:message for="nomeRegistro" styleClass="mensagemDetalhada"/>
                                    </h:panelGroup>
                                </c:if>

                                <h:outputText   value="#{msg_aplic.prt_Colaborador_dataNasc}" />

                                <h:panelGroup rendered="#{!ColaboradorControle.configuracaoSistema.utilizarFormatoMMDDYYYDtNascimento}">
                                    <rich:calendar id="dataNasc"
                                                   value="#{ColaboradorControle.pessoaVO.dataNasc}"
                                                   inputSize="10"
                                                   inputClass="form"
                                                   oninputblur="blurinput(this);"
                                                   oninputfocus="focusinput(this);"
                                                   oninputchange="return validar_Data(this.id);"
                                                   datePattern="dd/MM/yyyy"
                                                   enableManualInput="true"
                                                   zindex="2"
                                                   showWeeksBar="false"
                                                   oncollapse="selecionarRichCalendarColaborador_dataNasc(form);"/>
                                    <rich:jQuery id="mskData1" selector=".rich-calendar-input" timing="onload" query="mask('99/99/9999')" />
                                    <a4j:commandLink id="dataNascCommandLink" reRender="panelExistePessoa , panelExisteColaborador " action="#{ColaboradorControle.consultarCliente}" style="display: none" />
                                    <h:message for="dataNasc"  styleClass="mensagemDetalhada"/>
                                </h:panelGroup>

                                <h:panelGroup rendered="#{ColaboradorControle.configuracaoSistema.utilizarFormatoMMDDYYYDtNascimento}">
                                    <rich:calendar id="dataNascMMDDYYYY"
                                                   value="#{ColaboradorControle.pessoaVO.dataNasc}"
                                                   inputSize="10"
                                                   inputClass="form"
                                                   oninputblur="blurinput(this);"
                                                   oninputfocus="focusinput(this);"
                                                   datePattern="MM/dd/yyyy"
                                                   enableManualInput="true"
                                                   zindex="2"
                                                   showWeeksBar="false"
                                                   oncollapse="selecionarRichCalendarColaborador_dataNasc(form);"/>
                                    <rich:jQuery id="mskData1MMDDYYYY" selector=".rich-calendar-input" timing="onload" query="mask('99/99/9999')" />
                                    <a4j:commandLink id="dataNascCommandLinkMMDDYYYY" reRender="panelExistePessoa , panelExisteColaborador " action="#{ColaboradorControle.consultarCliente}" style="display: none" />
                                    <h:message for="dataNascMMDDYYYY"  styleClass="mensagemDetalhada"/>
                                </h:panelGroup>
                                <c:if test="${fn:contains(ColaboradorControle.listaCamposMostrarColaboradorDinamico, 'NOMEPAI')}">
                                    <h:panelGroup>
                                        <c:if test="${fn:contains(ColaboradorControle.listaCamposObrigatorioColaboradorDinamico, 'NOMEPAI')}">
                                            <h:outputLabel value="* "/>
                                        </c:if>
                                        <h:outputText   value="#{msg_aplic.prt_Pessoa_nomePai}" />
                                    </h:panelGroup>
                                </c:if>
                                <c:if test="${fn:contains(ColaboradorControle.listaCamposMostrarColaboradorDinamico, 'NOMEPAI')}">
                                  <h:inputText  id="nomePai" size="50" maxlength="50" onblur="blurinput(this);"  onfocus="focusinput(this);" styleClass="form" value="#{ColaboradorControle.pessoaVO.nomePai}" />
                                </c:if>
                                <c:if test="${fn:contains(ColaboradorControle.listaCamposMostrarColaboradorDinamico, 'NOMEMAE')}">
                                    <h:panelGroup>
                                        <c:if test="${fn:contains(ColaboradorControle.listaCamposObrigatorioColaboradorDinamico, 'NOMEMAE')}">
                                            <h:outputLabel value="* "/>
                                        </c:if>
                                        <h:outputText   value="#{msg_aplic.prt_Pessoa_nomeMae}" />
                                    </h:panelGroup>
                                </c:if>
                                <c:if test="${fn:contains(ColaboradorControle.listaCamposMostrarColaboradorDinamico, 'NOMEMAE')}">
                                  <h:inputText  id="nomeMae" size="50" maxlength="50" onblur="blurinput(this);"  onfocus="focusinput(this);" styleClass="form" value="#{ColaboradorControle.pessoaVO.nomeMae}" />
                                </c:if>

                                <c:if test="${fn:contains(ColaboradorControle.listaCamposMostrarColaboradorDinamico, 'CPFMAE')}">
                                    <h:panelGroup>
                                        <c:if test="${fn:contains(ColaboradorControle.listaCamposObrigatorioColaboradorDinamico, 'CPFMAE')}">
                                            <h:outputLabel value="* "/>
                                        </c:if>
                                        <h:outputText   value="#{msg_aplic.prt_Pessoa_cpfMae}" />
                                    </h:panelGroup>
                                </c:if>
                                <c:if test="${fn:contains(ColaboradorControle.listaCamposMostrarColaboradorDinamico, 'CPFMAE')}">
                                    <h:inputText  id="cpfMae"
                                                  size="14"
                                                  maxlength="14"
                                                  onblur="blurinput(this);"
                                                  onfocus="focusinput(this);"
                                                  styleClass="form"
                                                  onkeypress="return mascara(this.form, this.id, '999.999.999-99', event);"
                                                  value="#{ColaboradorControle.pessoaVO.cpfMae}" />
                                </c:if>

                                <c:if test="${fn:contains(ColaboradorControle.listaCamposMostrarColaboradorDinamico, 'CONTATOEMERGENCIA')}">
                                    <h:panelGroup>
                                        <c:if test="${fn:contains(ColaboradorControle.listaCamposObrigatorioColaboradorDinamico, 'CONTATOEMERGENCIA')}">
                                            <h:outputLabel value="* "/>
                                        </c:if>
                                        <h:outputText   value="Contato para Emergência" />
                                    </h:panelGroup>
                                </c:if>
                                <c:if test="${fn:contains(ColaboradorControle.listaCamposMostrarColaboradorDinamico, 'CONTATOEMERGENCIA')}">
                                    <h:inputText  id="contatoEmergenciaColaborador" size="15" maxlength="30" onblur="blurinput(this);"  onfocus="focusinput(this);" styleClass="form" value="#{ColaboradorControle.pessoaVO.contatoEmergencia}" />
                                </c:if>

                                <c:if test="${fn:contains(ColaboradorControle.listaCamposMostrarColaboradorDinamico, 'TELEFONEEMERGENCIA')}">
                                    <h:panelGroup>
                                        <c:if test="${fn:contains(ColaboradorControle.listaCamposObrigatorioColaboradorDinamico, 'TELEFONEEMERGENCIA')}">
                                            <h:outputLabel value="* "/>
                                        </c:if>
                                        <h:outputText   value="Telefone para Emergência" />
                                    </h:panelGroup>
                                </c:if>
                                <c:if test="${fn:contains(ColaboradorControle.listaCamposMostrarColaboradorDinamico, 'TELEFONEEMERGENCIA')}">
                                    <h:inputText id="numeroTelefoneClienteEmergenciaColaborador"
                                                 size="15"
                                                 maxlength="15"
                                                 onchange="return validar_Telefone(this.id);"
                                                 onblur="blurinput(this);"
                                                 onkeypress="return mascara(this.form, this.id , '(99)9.9999-9999', event);"
                                                 onfocus="focusinput(this);"
                                                 styleClass = "form"
                                                 value="#{ColaboradorControle.pessoaVO.telefoneEmergencia}"/>
                                </c:if>

                                <c:if test="${fn:contains(ColaboradorControle.listaCamposMostrarColaboradorDinamico, 'CPF')}">
                                    <h:panelGroup>
                                        <h:outputText    value="#{msg_aplic.prt_Pessoa_tipoPessoa}" />
                                    </h:panelGroup>
                                    <h:panelGroup>
                                        <h:selectOneMenu id="comboCategoriaPessoa" onblur="blurinput(this);"  onfocus="focusinput(this);"
                                                         styleClass="form" value="#{ColaboradorControle.pessoaVO.categoriaPessoa}">
                                            <f:selectItems value="#{ColaboradorControle.itensCategoriaPessoa}"/>
                                            <a4j:support event="onchange" reRender="panelDadosPessoaisColaborador"/>
                                        </h:selectOneMenu>
                                    </h:panelGroup>
                                </c:if>

                                <c:if test="${fn:contains(ColaboradorControle.listaCamposMostrarColaboradorDinamico, 'CPF') && ColaboradorControle.pessoaVO.pessoaFisica && !ColaboradorControle.configuracaoSistema.usarSistemaInternacional}">
                                    <h:panelGroup>
                                        <c:if test="${fn:contains(ColaboradorControle.listaCamposObrigatorioColaboradorDinamico, 'CPF')}">
                                            <h:outputLabel value="* "/>
                                         </c:if>
                                        <h:outputText   value="#{ColaboradorControle.displayIdentificadorFront[0]}" />
                                    </h:panelGroup>
                                    <h:panelGroup>
                                        <h:inputText  id="cfp"
                                                      size="14"
                                                      maxlength="14"
                                                      onblur="blurinput(this);"
                                                      onfocus="focusinput(this);"
                                                      styleClass="form"
                                                      value="#{ColaboradorControle.pessoaVO.cfp}"
                                                      onkeypress="return mascara(this.form, this.id, '999.999.999-99', event);"/>
                                    </h:panelGroup>
                                </c:if>

                                <c:if test="${fn:contains(ColaboradorControle.listaCamposMostrarColaboradorDinamico, 'CPF') && ColaboradorControle.pessoaVO.pessoaFisica && ColaboradorControle.configuracaoSistema.usarSistemaInternacional}">
                                    <h:panelGroup>
                                        <c:if test="${fn:contains(ColaboradorControle.listaCamposObrigatorioColaboradorDinamico, 'CPF')}">
                                            <h:outputLabel value="* "/>
                                        </c:if>
                                        <h:outputText   value="#{ColaboradorControle.displayIdentificadorFront[0]}" />
                                    </h:panelGroup>

                                    <h:panelGroup>
                                        <h:inputText  id="cfp"  size="14" maxlength="14" onblur="blurinput(this);"  onfocus="focusinput(this);" styleClass="form" value="#{ColaboradorControle.pessoaVO.cfp}" />
                                    </h:panelGroup>
                                </c:if>

                                <c:if test="${fn:contains(ColaboradorControle.listaCamposMostrarColaboradorDinamico, 'CPF') && ColaboradorControle.pessoaVO.pessoaJuridica}">
                                    <h:outputText value="#{msg_aplic.prt_Empresa_inscEstadualNaoObrigatorio}"/>
                                    <h:panelGroup>
                                        <h:inputText  id="inscEstadual" size="20" maxlength="20" onblur="blurinput(this);"  onfocus="focusinput(this);" styleClass="form" value="#{ColaboradorControle.pessoaVO.inscEstadual}" />
                                        <h:message for="inscEstadual" styleClass="mensagemDetalhada"/>

                                        <h:outputText value="     "/>


                                        <h:outputText  styleClass="tituloCampos" value="#{ColaboradorControle.apresentarCnpj}"/>
                                        <h:panelGroup>
                                            <c:if test="${!ColaboradorControle.configuracaoSistema.usarSistemaInternacional}">
                                                <h:outputText value="     "/>

                                                <h:inputText id="CNPJ" size="18" maxlength="18"
                                                             onkeypress="return mascara(this.form, 'form:CNPJ', '99.999.999/9999-99', event);"
                                                             onblur="blurinput(this);" onfocus="focusinput(this);"
                                                             styleClass="form"
                                                             value="#{ColaboradorControle.pessoaVO.cnpj}"/>
                                            </c:if>
                                            <c:if test="${ColaboradorControle.configuracaoSistema.usarSistemaInternacional}">
                                                <h:outputText value="     "/>

                                                <h:inputText id="CNPJInternacional" size="18" maxlength="20"
                                                             onblur="blurinput(this);" onfocus="focusinput(this);"
                                                             styleClass="form"
                                                             value="#{ColaboradorControle.pessoaVO.cnpj}"/>
                                            </c:if>
                                            <h:message for="CNPJ" styleClass="mensagemDetalhada"/>
                                        </h:panelGroup>
                                    </h:panelGroup>

                                    <h:outputText value="Inscrição Municipal:"/>
                                    <h:panelGroup rendered="#{ColaboradorControle.pessoaVO.pessoaJuridica}">
                                        <h:inputText  id="inscMunicipal" size="20" maxlength="20" onblur="blurinput(this);"  onfocus="focusinput(this);" styleClass="form" value="#{ColaboradorControle.pessoaVO.inscMunicipal}"/>
                                        <h:message for="inscMunicipal" styleClass="mensagemDetalhada"/>
                                    </h:panelGroup>
                                </c:if>

                                <c:if test="${fn:contains(ColaboradorControle.listaCamposMostrarColaboradorDinamico, 'RG') && ColaboradorControle.pessoaVO.pessoaFisica && !ColaboradorControle.configuracaoSistema.usarSistemaInternacional }">
                                    <h:panelGroup>
                                        <c:if test="${fn:contains(ColaboradorControle.listaCamposObrigatorioColaboradorDinamico, 'RG')}">
                                            <h:outputLabel value="* "/>
                                        </c:if>
                                        <h:outputText   value="#{ColaboradorControle.displayIdentificadorFront[1]}" />
                                    </h:panelGroup>

                                  <h:inputText  id="rg" size="20" maxlength="20" onblur="blurinput(this);"  onfocus="focusinput(this);" styleClass="form" value="#{ColaboradorControle.pessoaVO.rg}" />
                                </c:if>

                                <c:if test="${fn:contains(ColaboradorControle.listaCamposMostrarColaboradorDinamico, 'RG') && ColaboradorControle.pessoaVO.pessoaFisica && ColaboradorControle.configuracaoSistema.usarSistemaInternacional }">
                                    <h:panelGroup>
                                        <c:if test="${fn:contains(ColaboradorControle.listaCamposObrigatorioColaboradorDinamico, 'RG')}">
                                            <h:outputLabel value="* "/>
                                        </c:if>
                                        <h:outputText   value="#{ColaboradorControle.displayIdentificadorFront[1]}" />
                                    </h:panelGroup>

                                    <h:inputText  id="rg" size="20" maxlength="20" onblur="blurinput(this);"  onfocus="focusinput(this);" styleClass="form" value="#{ColaboradorControle.pessoaVO.rg}" />
                                </c:if>

                                <c:if test="${fn:contains(ColaboradorControle.listaCamposMostrarColaboradorDinamico, 'ORGAOEMISSOR') && ColaboradorControle.pessoaVO.pessoaFisica && !ColaboradorControle.configuracaoSistema.usarSistemaInternacional}" >
                                    <h:panelGroup>
                                        <c:if test="${fn:contains(ColaboradorControle.listaCamposObrigatorioColaboradorDinamico, 'ORGAOEMISSOR')}">
                                            <h:outputLabel value="* "/>
                                        </c:if>
                                        <h:outputText   value="#{msg_aplic.prt_Pessoa_rgOrgao}" />
                                    </h:panelGroup>

                                  <h:inputText  id="rgOrgao" size="10" maxlength="10" onblur="blurinput(this);"  onfocus="focusinput(this);" styleClass="form" value="#{ColaboradorControle.pessoaVO.rgOrgao}" />
                                </c:if>
                                <c:if test="${fn:contains(ColaboradorControle.listaCamposMostrarColaboradorDinamico, 'ESTADOEMISSAO') && ColaboradorControle.pessoaVO.pessoaFisica && !ColaboradorControle.configuracaoSistema.usarSistemaInternacional}">
                                    <h:panelGroup>
                                            <h:outputLabel value="* "/>
                                        <h:outputText   value="#{msg_aplic.prt_Pessoa_rgUf}" />
                                    </h:panelGroup>

                                    <h:selectOneMenu  id="rgUf" onblur="blurinput(this);"  onfocus="focusinput(this);" styleClass="form" value="#{ColaboradorControle.pessoaVO.rgUf}" >
                                        <f:selectItems  value="#{ColaboradorControle.listaSelectItemRgUfPessoa}" />
                                    </h:selectOneMenu>
                                </c:if>

                                <c:if test="${fn:contains(ColaboradorControle.listaCamposMostrarColaboradorDinamico, 'PAIS')}">
                                    <h:panelGroup>
                                        <c:if test="${fn:contains(ColaboradorControle.listaCamposObrigatorioColaboradorDinamico, 'PAIS')}">
                                            <h:outputLabel value="* "/>
                                        </c:if>
                                        <h:outputText   value="#{msg_aplic.prt_Pessoa_pais}" />
                                    </h:panelGroup>
                                </c:if>
                                <c:if test="${fn:contains(ColaboradorControle.listaCamposMostrarColaboradorDinamico, 'PAIS')}">
                                    <h:panelGroup>
                                        <h:selectOneMenu  id="pais" onblur="blurinput(this);"  onfocus="focusinput(this);" styleClass="form" value="#{ColaboradorControle.pessoaVO.pais.codigo}" >
                                            <f:selectItems  value="#{ColaboradorControle.listaSelectItemPais}" />
                                            <a4j:support  event="onchange"   reRender="form:estado,cidade" focus="pais" action="#{ColaboradorControle.montarListaSelectItemEstado}"/>
                                        </h:selectOneMenu>
                                        <a4j:commandButton id="atualizar_pais" action="#{ColaboradorControle.montarListaSelectItemPais}" image="imagens/atualizar.png" immediate="true" ajaxSingle="true" reRender="form:pais"/>
                                        <h:message for="pais" styleClass="mensagemDetalhada"/>

                                    </h:panelGroup>
                                </c:if>
                                <c:if test="${fn:contains(ColaboradorControle.listaCamposMostrarColaboradorDinamico, 'ESTADO')}">
                                    <h:panelGroup>
                                        <c:if test="${fn:contains(ColaboradorControle.listaCamposObrigatorioColaboradorDinamico, 'ESTADO')}">
                                            <h:outputLabel value="* "/>
                                        </c:if>
                                        <h:outputText    value="#{msg_aplic.prt_Pessoa_estado}" />
                                    </h:panelGroup>
                                </c:if>
                                <c:if test="${fn:contains(ColaboradorControle.listaCamposMostrarColaboradorDinamico, 'ESTADO')}">
                                    <h:panelGroup>
                                        <h:selectOneMenu id="estado" onblur="blurinput(this);"
                                                         onfocus="focusinput(this);" styleClass="form"
                                                         value="#{ColaboradorControle.pessoaVO.estadoVO.codigo}">
                                            <f:selectItems value="#{ColaboradorControle.listaSelectItemEstado}"/>
                                            <a4j:support event="onchange" reRender="pnlCidade"
                                                         action="#{ColaboradorControle.montarListaSelectItemCidade}"/>
                                        </h:selectOneMenu>
                                        <a4j:commandButton id="atualizar_estado"
                                                           action="#{ColaboradorControle.montarListaSelectItemEstado}"
                                                           image="imagens/atualizar.png" ajaxSingle="true"
                                                           reRender="form:estado"/>
                                        <h:message for="estado" styleClass="mensagemDetalhada"/>
                                    </h:panelGroup>
                                </c:if>
                                <c:if test="${fn:contains(ColaboradorControle.listaCamposMostrarColaboradorDinamico, 'CIDADE')}">
                                    <h:panelGroup>
                                        <c:if test="${fn:contains(ColaboradorControle.listaCamposObrigatorioColaboradorDinamico, 'CIDADE')}">
                                            <h:outputLabel value="* "/>
                                        </c:if>
                                        <h:outputText   value="#{msg_aplic.prt_Pessoa_cidade}" />
                                    </h:panelGroup>
                                </c:if>
                                <c:if test="${fn:contains(ColaboradorControle.listaCamposMostrarColaboradorDinamico, 'CIDADE')}">
                                    <h:panelGroup id="pnlCidade">
                                        <h:selectOneMenu  id="cidade" onblur="blurinput(this);"  onfocus="focusinput(this);" styleClass="form" value="#{ColaboradorControle.pessoaVO.cidade.codigo}" >
                                            <f:selectItems  value="#{ColaboradorControle.listaSelectItemCidade}" />
                                        </h:selectOneMenu>
                                        <a4j:commandButton id="atualizar_cidade" action="#{ColaboradorControle.montarListaSelectItemCidade}" image="imagens/atualizar.png" immediate="true" ajaxSingle="true" reRender="form:cidade"/>
                                        <a4j:commandButton id="consultaDadosCidade" alt="Cadastrar Cidade"
                                                           rendered="#{ColaboradorControle.apresentarCadastrarCidade}"
                                                           reRender="formCidade"
                                                           actionListener="#{CidadeControle.prepararCadastro}"
                                                           oncomplete="Richfaces.showModalPanel('panelCidade');setFocus(formCidade,'panelCidade:cidade_nome');"
                                                           image="./images/icon_add.gif"
                                                           title="Cadastrar cidade">
                                            <f:param name="estado" value="#{ColaboradorControle.pessoaVO.estadoVO.codigo}"/>
                                            <f:param name="pais" value="#{ColaboradorControle.pessoaVO.pais.codigo}"/>
                                            <f:param name="onCompleteModal" value="document.getElementById('form:atualizar_cidade').click();"/>
                                        </a4j:commandButton>
                                        <h:message for="cidade" styleClass="mensagemDetalhada"/>
                                    </h:panelGroup>
                                </c:if>
                                <c:if test="${fn:contains(ColaboradorControle.listaCamposMostrarColaboradorDinamico, 'ESTADOCIVIL')}">
                                    <h:panelGroup>
                                        <c:if test="${fn:contains(ColaboradorControle.listaCamposObrigatorioColaboradorDinamico, 'ESTADOCIVIL')}">
                                            <h:outputLabel value="* "/>
                                        </c:if>
                                        <h:outputText   value="#{msg_aplic.prt_Pessoa_estadoCivil}" />
                                    </h:panelGroup>
                                </c:if>
                                <c:if test="${fn:contains(ColaboradorControle.listaCamposMostrarColaboradorDinamico, 'ESTADOCIVIL')}">
                                    <h:selectOneMenu  id="estadoCivil" onblur="blurinput(this);"  onfocus="focusinput(this);" styleClass="form" value="#{ColaboradorControle.pessoaVO.estadoCivil}" >
                                        <f:selectItems  value="#{ColaboradorControle.listaSelectItemEstadoCivilPessoa}" />
                                    </h:selectOneMenu>
                                </c:if>
                                <c:if test="${fn:contains(ColaboradorControle.listaCamposMostrarColaboradorDinamico, 'NACIONALIDADE')}">
                                    <h:panelGroup>
                                        <c:if test="${fn:contains(ColaboradorControle.listaCamposObrigatorioColaboradorDinamico, 'NACIONALIDADE')}">
                                            <h:outputLabel value="* "/>
                                        </c:if>
                                        <h:outputText   value="#{msg_aplic.prt_Pessoa_nacionalidade}" />
                                    </h:panelGroup>
                                </c:if>
                                <c:if test="${fn:contains(ColaboradorControle.listaCamposMostrarColaboradorDinamico, 'NACIONALIDADE')}">
                                  <h:inputText  id="nacionalidade" size="20" maxlength="20" onblur="blurinput(this);"  onfocus="focusinput(this);" styleClass="form" value="#{ColaboradorControle.pessoaVO.nacionalidade}" />
                                </c:if>
                                <c:if test="${fn:contains(ColaboradorControle.listaCamposMostrarColaboradorDinamico, 'NATURALIDADE')}">
                                    <h:panelGroup>
                                        <c:if test="${fn:contains(ColaboradorControle.listaCamposObrigatorioColaboradorDinamico, 'NATURALIDADE')}">
                                            <h:outputLabel value="* "/>
                                        </c:if>
                                        <h:outputText   value="#{msg_aplic.prt_Pessoa_naturalidade}" />
                                    </h:panelGroup>
                                </c:if>
                                <c:if test="${fn:contains(ColaboradorControle.listaCamposMostrarColaboradorDinamico, 'NATURALIDADE')}">
                                  <h:inputText  id="naturalidade" size="20" maxlength="20" onblur="blurinput(this);"  onfocus="focusinput(this);" styleClass="form" value="#{ColaboradorControle.pessoaVO.naturalidade}" />
                                </c:if>
                                <c:if test="${fn:contains(ColaboradorControle.listaCamposMostrarColaboradorDinamico, 'SEXO')}">
                                    <h:panelGroup>
                                        <c:if test="${fn:contains(ColaboradorControle.listaCamposObrigatorioColaboradorDinamico, 'SEXO')}">
                                            <h:outputLabel value="* "/>
                                        </c:if>
                                        <h:outputText   value="#{msg_aplic.prt_Pessoa_sexo}" />
                                    </h:panelGroup>
                                </c:if>
                                <c:if test="${fn:contains(ColaboradorControle.listaCamposMostrarColaboradorDinamico, 'SEXO')}">
                                    <h:selectOneMenu  id="sexo" onblur="blurinput(this);"  onfocus="focusinput(this);" styleClass="form" value="#{ColaboradorControle.pessoaVO.sexo}" >
                                        <f:selectItems  value="#{ColaboradorControle.listaSelectItemSexoPessoa}" />
                                    </h:selectOneMenu>
                                </c:if>

                                <c:if test="${fn:contains(ColaboradorControle.listaCamposMostrarColaboradorDinamico, 'GENERO')}">
                                    <h:panelGroup>
                                        <c:if test="${fn:contains(ColaboradorControle.listaCamposObrigatorioColaboradorDinamico, 'GENERO')}">
                                            <h:outputLabel value="* "/>
                                        </c:if>
                                        <h:outputText   value="#{msg_aplic.prt_Pessoa_genero}" />
                                    </h:panelGroup>
                                </c:if>
                                <c:if test="${fn:contains(ColaboradorControle.listaCamposMostrarColaboradorDinamico, 'GENERO')}">
                                    <h:selectOneMenu  id="genero" onblur="blurinput(this);"  onfocus="focusinput(this);" styleClass="form" value="#{ColaboradorControle.pessoaVO.genero}" >
                                        <f:selectItems  value="#{ColaboradorControle.listaSelectItemGeneroPessoa}" />
                                    </h:selectOneMenu>
                                </c:if>

                                <c:if test="${fn:contains(ColaboradorControle.listaCamposMostrarColaboradorDinamico, 'WEBPAGE')}">
                                    <h:panelGroup>
                                        <c:if test="${fn:contains(ColaboradorControle.listaCamposObrigatorioColaboradorDinamico, 'WEBPAGE')}">
                                            <h:outputLabel value="* "/>
                                        </c:if>
                                        <h:outputText    value="#{msg_aplic.prt_Pessoa_webPage}" />
                                    </h:panelGroup>
                                </c:if>
                                <c:if test="${fn:contains(ColaboradorControle.listaCamposMostrarColaboradorDinamico, 'WEBPAGE')}">
                                  <h:inputText  id="webPage" size="50" maxlength="50" onblur="blurinput(this);"  onfocus="focusinput(this);"   styleClass="form" value="#{ColaboradorControle.pessoaVO.webPage}" />
                                </c:if>

                                <h:outputText rendered="#{ColaboradorControle.colaboradorVO.codigo > 0}"   value="#{msg_aplic.prt_Pessoa_definirSenhaAcesso}" />
                                <a4j:commandButton rendered="#{ColaboradorControle.colaboradorVO.codigo > 0}" action="#{ColaboradorControle.abriTelaDefinirSenhaAcesso}"
                                                   title="Definir senha de acesso para utilizar na catraca."
                                                   oncomplete="abrirPopup('definirSenhaAcesso.jsp', 'Definir senha de acesso', 500, 300);"
                                                   value="#{msg_bt.btn_cadastrarSenhaAcesso}"
                                                   accesskey="6"
                                                   styleClass="botaoEspecial"/>

                                <h:outputText
                                        rendered="#{ColaboradorControle.colaboradorVO.empresa.usarParceiroFidelidade}"
                                        value="Utiliza DOTZ:"/>
                                <h:selectBooleanCheckbox
                                        rendered="#{ColaboradorControle.colaboradorVO.empresa.usarParceiroFidelidade}"
                                        value="#{ColaboradorControle.colaboradorVO.pessoa.utilizaDotz}"/>

                            </h:panelGrid>
                        </h:panelGrid>
                    </rich:tab>
                    <rich:tab id="abaDadosColaborador" label="Dados do Colaborador" actionListener="#{ColaboradorControle.preencherAssinaturasBiometricas}">
                        <h:panelGrid id="panelDadosColaborador" columns="1" width="100%" headerClass="subordinado" columnClasses="colunaCentralizada">
                            <f:facet name="header">
                                <h:outputText value="#{msg_aplic.prt_DadosColaborador_tituloForm}"/>
                            </f:facet>
                            <h:panelGrid columns="2" rowClasses="linhaImpar, linhaPar" columnClasses="classEsquerda, classDireita" width="100%">

                                <h:outputText   value="#{msg_aplic.prt_Colaborador_codigo}" />
                                <h:panelGroup>
                                    <h:inputText  id="codigo"  size="10" maxlength="10" readonly="true" onblur="blurinput(this);"  onfocus="focusinput(this);" styleClass="form" value="#{ColaboradorControle.colaboradorVO.codigo}" />
                                    <h:message for="codigo" styleClass="mensagemDetalhada"/>
                                </h:panelGroup>

                                <h:outputText   value="#{msg_aplic.prt_Colaborador_situacao}" />
                                <h:panelGroup>
                                    <h:selectOneMenu  id="situacao" onblur="blurinput(this);"  onfocus="focusinput(this);" styleClass="form" value="#{ColaboradorControle.colaboradorVO.situacao}" >
                                        <a4j:support event="onchange"
                                                     action="#{ColaboradorControle.verificarSituacaoColaborador}"
                                                     reRender="form,mdlVinculoAgenda" focus="situacao" oncomplete="#{ColaboradorControle.onComplete}"/>
                                        <f:selectItems  value="#{ColaboradorControle.listaSelectItemSituacaoColaborador}" />
                                    </h:selectOneMenu>
                                    <h:message for="situacao" styleClass="mensagemDetalhada"/>
                                </h:panelGroup>
                                <h:outputText value="#{msg_aplic.prt_Colaborador_tempoEntreAcessos}"/>
                                <h:selectBooleanCheckbox id="configurarTempoEntreAcessos" styleClass="campos" value="#{ColaboradorControle.colaboradorVO.configurarTempoEntreAcessos}">
                                    <a4j:support event="onchange" reRender="panelDadosColaborador"/>
                                </h:selectBooleanCheckbox>

                                <h:outputText rendered="#{ColaboradorControle.colaboradorVO.configurarTempoEntreAcessos}" value="#{msg_aplic.prt_Colaborador_tempoEntreAcessos}"/>
                                <h:panelGroup rendered="#{ColaboradorControle.colaboradorVO.configurarTempoEntreAcessos}">
                                    <h:inputText id="tempocolaborador" size="5" maxlength="5" onblur="blurinput(this);"  onfocus="focusinput(this);" styleClass="form"  value="#{ColaboradorControle.colaboradorVO.tempoEntreAcessos}" />
                                    <h:outputText styleClass="tituloCampos" value="(min)" />
                                    <h:message for="tempocolaborador" styleClass="mensagemDetalhada"/>
                                </h:panelGroup>
                                <c:if test="${fn:contains(ColaboradorControle.listaCamposMostrarColaboradorDinamico, 'CREF')}">
                                    <h:panelGroup>
                                        <c:if test="${fn:contains(ColaboradorControle.listaCamposObrigatorioColaboradorDinamico, 'CREF')}">
                                            <h:outputLabel value="* "></h:outputLabel>
                                        </c:if>
                                        <h:outputText  value="#{msg_aplic.prt_Colaborador_cref}" />
                                    </h:panelGroup>
                                </c:if>
                                <c:if test="${fn:contains(ColaboradorControle.listaCamposMostrarColaboradorDinamico, 'CREF')}">
                                    <h:panelGroup>
                                        <h:inputText  id="cref"  size="10" maxlength="25" onblur="blurinput(this);"  onfocus="focusinput(this);" styleClass="form" value="#{ColaboradorControle.colaboradorVO.cref}" />
                                        <h:message for="cref" styleClass="mensagemDetalhada"/>
                                    </h:panelGroup>
                                </c:if>
                                <c:if test="${fn:contains(ColaboradorControle.listaCamposMostrarColaboradorDinamico, 'CREF')}">
                                    <h:panelGroup>
                                        <c:if test="${fn:contains(ColaboradorControle.listaCamposObrigatorioColaboradorDinamico, 'CREF')}">
                                            <h:outputLabel value="* "></h:outputLabel>
                                        </c:if>
                                        <h:outputText  value="#{msg_aplic.prt_Colaborador_Validade_cref}" />
                                    </h:panelGroup>
                                </c:if>
                                <c:if test="${fn:contains(ColaboradorControle.listaCamposMostrarColaboradorDinamico, 'CREF')}">
                                    <h:panelGroup>
                                        <rich:calendar id="validadeCref"
                                                       value="#{ColaboradorControle.colaboradorVO.validadeCref}"
                                                       inputSize="10"
                                                       inputClass="form"
                                                       oninputblur="blurinput(this);"
                                                       oninputfocus="focusinput(this);"
                                                       oninputchange="return validar_Data(this.id);"
                                                       datePattern="dd/MM/yyyy"
                                                       enableManualInput="true"
                                                       zindex="2" readonly="false"
                                                       showWeeksBar="false"  />
                                        <h:message for="validadeCref"  styleClass="mensagemDetalhada"/>
                                    </h:panelGroup>
                                </c:if>
                                <h:outputText  value="#{msg_aplic.prt_Colaborador_codAcesso}" />
                                <h:panelGroup>
                                    <h:inputText  id="codAcesso"  size="10" maxlength="10" onblur="blurinput(this);"  onfocus="focusinput(this);" styleClass="form" value="#{ColaboradorControle.colaboradorVO.codAcesso}" />
                                    <h:message for="codAcesso" styleClass="mensagemDetalhada"/>
                                </h:panelGroup>
                                <h:outputText value="#{msg_aplic.prt_Colaborador_codAcessoAlternativo}" />
                                <h:inputText id="codAcessoAlternativo"  onblur="blurinput(this);"  onfocus="focusinput(this);" styleClass="form"  size="10" maxlength="15"  value="#{ColaboradorControle.colaboradorVO.codAcessoAlternativo}" />

                                <h:outputText rendered="#{ClienteControle.clienteVO.empresa.validaUtilizacaoVitio}" value="Código de afiliado Vitio:" />
                                <h:inputText id="codigoAfiliadoVitio" rendered="#{ClienteControle.clienteVO.empresa.validaUtilizacaoVitio}" onblur="blurinput(this);"  onfocus="focusinput(this);" styleClass="form"  size="10" maxlength="15"  value="#{ColaboradorControle.colaboradorVO.codigoAfiliadoVitio}" />

                                <h:panelGroup>
                                    <h:outputText value="Template Digital:" />
                                </h:panelGroup>
                                <h:panelGroup layout="block" id="panelDigital" style="display: inline-flex;">
                                    <h:inputText  id="assinaturaBiometriaDigital"
                                                  size="20"
                                                  disabled="true"
                                                  styleClass="form"
                                                  value="#{ColaboradorControle.colaboradorVO.pessoa.assinaturaBiometriaDigital}" />
                                    <a4j:commandButton id="excluirAssinaturaDigital" immediate="true"
                                                       action="#{ColaboradorControle.confirmarExcluirAssinaturaDigital}"
                                                       oncomplete="#{ColaboradorControle.msgAlert}"
                                                       reRender="form:panelDadosColaborador,form:panelMensagemErro,mdlMensagemGenerica"
                                                       rendered="#{not empty ColaboradorControle.colaboradorVO.pessoa.assinaturaBiometriaDigital}"
                                                       title="Excluir template digital"
                                                       value="#{msg_bt.btn_excluir}" image="./imagens/botaoRemover.png" accesskey="7" styleClass="botoes"/>
                                </h:panelGroup>

                                <h:panelGroup>
                                    <h:outputText value="Template Facial:" />
                                </h:panelGroup>
                                <h:panelGroup layout="block" id="panelFacial" style="display: inline-flex;">
                                    <h:inputText  id="assinaturaBiometriaFacial"
                                                  size="20"
                                                  disabled="true"
                                                  styleClass="form"
                                                  value="#{ColaboradorControle.colaboradorVO.pessoa.assinaturaBiometriaFacial}" />
                                    <a4j:commandButton id="excluirAssinaturaFacial" immediate="true"
                                                       action="#{ColaboradorControle.confirmarExcluirAssinaturaFacial}"
                                                       oncomplete="#{ColaboradorControle.msgAlert}"
                                                       reRender="form:panelDadosColaborador,form:panelMensagemErro,mdlMensagemGenerica"
                                                       rendered="#{not empty ColaboradorControle.colaboradorVO.pessoa.assinaturaBiometriaFacial}"
                                                       title="Excluir template facial"
                                                       value="#{msg_bt.btn_excluir}" image="./imagens/botaoRemover.png" accesskey="7" styleClass="botoes"/>
                                </h:panelGroup>

                                <h:outputText value="#{msg_aplic.prt_Colaborador_diaVencimento}" />
                                <h:inputText id="diaVencimento" onblur="blurinput(this);" onfocus="focusinput(this);"
                                             styleClass="form" size="5" maxlength="5" value="#{ColaboradorControle.colaboradorVO.diaVencimento}" />
                                <c:if test="${fn:contains(ColaboradorControle.listaCamposMostrarColaboradorDinamico, 'PRODUTODEFAULTPERSONAL')}">
                                    <h:panelGroup>
                                        <c:if test="${fn:contains(ColaboradorControle.listaCamposObrigatorioColaboradorDinamico, 'PRODUTODEFAULTPERSONAL')}">
                                            <h:outputLabel value="* "></h:outputLabel>
                                        </c:if>
                                        <h:outputText value="#{msg_aplic.prt_Colaborador_produtoDefault}" />
                                    </h:panelGroup>
                                </c:if>
                                <c:if test="${fn:contains(ColaboradorControle.listaCamposMostrarColaboradorDinamico, 'PRODUTODEFAULTPERSONAL')}">
                                    <h:panelGroup>
                                        <h:inputText id="produtoDefault" readonly="true"
                                                     styleClass="form" size="40" maxlength="40" value="#{ColaboradorControle.colaboradorVO.produtoDefault.descricao}" />
                                        <a4j:commandButton id="consultarProdutoPer" oncomplete="Richfaces.showModalPanel('panelProduto')" image="imagens/informacao.gif"
                                                           alt="#{msg_aplic.prt_ItemVendaAvulsa_consultarProduto}"/>
                                        <a4j:commandButton id="removeProduto" alt="Remove Produto Default"
                                                           action="#{ColaboradorControle.colaboradorVO.removerProdutoDefault}" reRender="produtoDefault"
                                                           image="./imagens/limpar.gif" style="margin-left: 5px;" title="Remover Produto Default" />
                                    </h:panelGroup>
                                </c:if>
                                <%-- FOI RETIRADO E ADICIONADO O TIPO COLABORADOR FUNCIONARIO - PROJETO 182232 --%>
                                <%--<h:outputText value="#{msg_aplic.prt_Colaborador_funcionario}" />--%>
                                <%--<h:selectBooleanCheckbox id="funcionario" styleClass="campos" value="#{ColaboradorControle.colaboradorVO.funcionario}"/>--%>

                                <c:if test="${fn:contains(ColaboradorControle.listaCamposMostrarColaboradorDinamico, 'PORCENTAGEMCOMISSAO')}">
                                    <h:panelGroup>
                                        <c:if test="${fn:contains(ColaboradorControle.listaCamposObrigatorioColaboradorDinamico, 'PORCENTAGEMCOMISSAO')}">
                                            <h:outputLabel value="* "></h:outputLabel>
                                        </c:if>
                                        <h:outputText value="#{msg_aplic.prt_Colaborador_porcComissao}" />
                                    </h:panelGroup>
                                </c:if>
                                <c:if test="${fn:contains(ColaboradorControle.listaCamposMostrarColaboradorDinamico, 'PORCENTAGEMCOMISSAO')}">
                                    <h:inputText id="porcComissao"  size="10" maxlength="10"  onblur="blurinput(this);"
                                                 onkeypress="return formatar_moeda(this,'.',',',event);" onfocus="focusinput(this);"
                                                 styleClass="form" value="#{ColaboradorControle.colaboradorVO.porcComissao}">
                                        <f:converter converterId="FormatadorNumerico" />
                                    </h:inputText>
                                </c:if>
                                <c:if test="${fn:contains(ColaboradorControle.listaCamposMostrarColaboradorDinamico, 'VALORFIXOCOMISSAO')}">
                                    <h:panelGroup>
                                        <c:if test="${fn:contains(ColaboradorControle.listaCamposObrigatorioColaboradorDinamico, 'VALORFIXOCOMISSAO')}">
                                            <h:outputLabel value="* "></h:outputLabel>
                                        </c:if>
                                        <h:outputText styleClass="tooltipster"
                                                      value="#{msg_aplic.prt_Colaborador_valorComissao}"
                                                      title="Valor fixo por cada aula."/>
                                    </h:panelGroup>
                                </c:if>
                                <c:if test="${fn:contains(ColaboradorControle.listaCamposMostrarColaboradorDinamico, 'VALORFIXOCOMISSAO')}">
                                    <h:inputText id="valorComissao"  size="10" maxlength="10"  onblur="blurinput(this);"
                                                 onkeypress="return formatar_moeda(this,'.',',',event);" onfocus="focusinput(this);"
                                                 styleClass="form" value="#{ColaboradorControle.colaboradorVO.valorComissao}">
                                        <f:converter converterId="FormatadorNumerico" />
                                    </h:inputText>
                                </c:if>

                                <c:if test="${ColaboradorControle.verificarSeTemTipoColaboradorEstudio}">
                                    <h:panelGroup>
                                        <h:outputText value="#{msg_aplic.prt_Colaborador_porcComissaoEstudio}" />
                                    </h:panelGroup>
                                </c:if>
                                <c:if test="${ColaboradorControle.verificarSeTemTipoColaboradorEstudio}">
                                    <h:inputText id="porcComissaoEstudio"  size="10" maxlength="10"  onblur="blurinput(this);"
                                                 onkeypress="return formatar_moeda(this,'.',',',event);" onfocus="focusinput(this);"
                                                 styleClass="form" value="#{ColaboradorControle.colaboradorVO.porcComissaoIndicacaoEstudio}">
                                        <f:converter converterId="FormatadorNumerico" />
                                    </h:inputText>
                                </c:if>

                                <h:outputText rendered="#{ColaboradorControle.verificarSeTemTipoColaboradorEstudio}"
                                              value="#{msg_aplic.prt_Colaborador_corAgendaProfissional}" />
                                <h:panelGroup rendered="#{ColaboradorControle.verificarSeTemTipoColaboradorEstudio}">
                                    <rich:colorPicker colorMode="hex" id="corAgendaProf" inputSize="15"
                                                      value="#{ColaboradorControle.colaboradorVO.corAgendaProfissional}"/>
                                </h:panelGroup>

                                <h:outputText styleClass="tooltipster"
                                              value="Valor máximo para deixar no caixa em aberto (Venda Avulsa):"
                                              title="Caso não queria colocar um limite, deixar em branco (R$ 0,00)."/>
                                <h:inputText id="valorLimiteCaixaAbertoVendaAvulsa"
                                             size="10" maxlength="10" onblur="blurinput(this);"
                                             onkeypress="return formatar_moeda(this,'.',',',event);" onfocus="focusinput(this);"
                                             styleClass="form"
                                             value="#{ColaboradorControle.colaboradorVO.pessoa.valorLimiteCaixaAbertoVendaAvulsa}">
                                    <f:converter converterId="FormatadorNumerico"/>
                                </h:inputText>
                            </h:panelGrid>

                            <c:if test="${fn:contains(ColaboradorControle.listaCamposMostrarColaboradorDinamico, 'AUTENTICARGOOGLE')}">
                                <h:panelGrid id="panelAutorizacaoGoogle" columns="2" rowClasses="linhaImpar, linhaPar"
                                             columnClasses="classEsquerda, classDireita" width="100%">
                                    <h:panelGroup>
                                        <c:if test="${fn:contains(ColaboradorControle.listaCamposObrigatorioColaboradorDinamico, 'AUTENTICARGOOGLE')}">
                                            <h:outputLabel value="* "></h:outputLabel>
                                        </c:if>
                                        <h:outputText value="#{msg_aplic.prt_Colaborador_autenticarGoogle}"/>
                                    </h:panelGroup>
                                    <h:panelGroup>
                                        <h:panelGrid columns="1">
                                            <h:outputLink target="_blank"
                                                          value="#{ColaboradorControle.urlGoogle}">
                                                GERAR TOKEN GOOGLE
                                            </h:outputLink>

                                            <h:inputTextarea value="#{ColaboradorControle.colaboradorVO.tokenGoogle}"
                                                             cols="80" rows="4"/>

                                            <a4j:commandButton value="Sincronizar" action="#{ColaboradorControle.sincronizarAgenda}"
                                                               rendered="#{!empty ColaboradorControle.colaboradorVO.tokenGoogle}"
                                                               reRender="panelMensagemErro"/>
                                            <a4j:commandButton value="Dessincronizar" action="#{ColaboradorControle.dessincronizarAgenda}"
                                                               rendered="#{!empty ColaboradorControle.colaboradorVO.tokenGoogle}"
                                                               reRender="panelMensagemErro"/>
                                        </h:panelGrid>
                                    </h:panelGroup>
                                </h:panelGrid>
                            </c:if>

                            <h:panelGrid id="panelTipoColaborador" columns="2" rowClasses="linhaImpar, linhaPar" columnClasses="classEsquerda, classDireita" width="100%">


                                <h:outputText rendered="#{ColaboradorControle.mostrarUsoCredito}"
                                              value="Saldo créditos:" />
                                <h:outputText rendered="#{ColaboradorControle.mostrarUsoCredito}"
                                              value="#{ColaboradorControle.colaboradorVO.saldoCreditoPersonal}" />


                                <h:outputText rendered="#{ColaboradorControle.mostrarUsoCredito and ColaboradorControle.verificarSoTemTipoPersonalExterno}"
                                              value="Bloquear acesso se não houver check-in ativo" />
                                <h:selectBooleanCheckbox id="bloquearAcessoSemCheckin" styleClass="campos"
                                                         rendered="#{ColaboradorControle.mostrarUsoCredito and ColaboradorControle.verificarSoTemTipoPersonalExterno}"
                                                         value="#{ColaboradorControle.colaboradorVO.bloquearAcessoSemCheckin}">
                                </h:selectBooleanCheckbox>


                                <h:outputText rendered="#{ColaboradorControle.mostrarUsoCredito}"
                                              value="#{msg_aplic.prt_Colaborador_usoCreditoPersonal}" />
                                <h:selectOneMenu  id="mostrarUsoCredito" onblur="blurinput(this);"
                                                  rendered="#{ColaboradorControle.mostrarUsoCredito}"
                                                  onfocus="focusinput(this);" styleClass="form"
                                                  value="#{ColaboradorControle.colaboradorVO.usoCreditosPersonal}" >
                                    <f:selectItems  value="#{ColaboradorControle.listaUsoCreditos}" />
                                </h:selectOneMenu>

                                <h:outputText   value="#{msg_aplic.prt_Colaborador_cargaHoraria}" />
                                <h:selectOneMenu  id="cargaHoraria" onblur="blurinput(this);"  onfocus="focusinput(this);" styleClass="form" value="#{ColaboradorControle.colaboradorVO.cargaHoraria}" >
                                    <f:selectItems  value="#{ColaboradorControle.listaSelectItemCargaHoraria}" />
                                </h:selectOneMenu>

                                <c:if test="${LoginControle.utilizaModuloGestaoRedes}">
                                    <h:outputText value="#{msg_aplic.prt_Colaborador_permitirAcessoRedeEmpresa}"/>
                                    <h:selectBooleanCheckbox id="permitirAcessoRedeEmpresa" styleClass="campos"
                                                             value="#{ColaboradorControle.colaboradorVO.permitirAcessoRedeEmpresa}"/>
                                </c:if>


                                <h:outputText   value="#{msg_aplic.prt_Colaborador_tipoColaborador}" />
                                <h:panelGroup>
                                    <h:selectOneMenu  id="tipoColaborador" onblur="blurinput(this);"  onfocus="focusinput(this);" styleClass="form" value="#{ColaboradorControle.tipoColaboradorVO.descricao}" >
                                        <f:selectItems  value="#{ColaboradorControle.listaSelectItemTipoColaborador}" />
                                    </h:selectOneMenu>
                                </h:panelGroup>
                            </h:panelGrid>
                            <a4j:commandButton id="addTipoColaborador" action="#{ColaboradorControle.adicionarTipoColaborador}" reRender="panelTipoColaborador ,panelDadosColaborador, dataTabletipoColaboradorVO, panelMensagemErro" focus="enderecoColaborador" value="#{msg_bt.btn_adicionar}" image= "./imagens/botaoAdicionar.png" accesskey="5" styleClass="botoes"/>
                            <h:panelGrid columns="1" width="100%" styleClass="tabFormSubordinada">
                                <h:dataTable id="dataTabletipoColaboradorVO" width="100%" headerClass="subordinado" styleClass="tabFormSubordinada"
                                             rowClasses="linhaImpar, linhaPar" columnClasses="colunaAlinhamento"
                                             value="#{ColaboradorControle.colaboradorVO.listaTipoColaboradorVOs}" var="tipoColaboradorVO">
                                    <h:column>
                                        <f:facet name="header">
                                            <h:outputText  value="#{msg_aplic.prt_Colaborador_tipoColaborador}" />
                                        </f:facet>
                                        <h:outputText id="desTipoColaborador" value="#{tipoColaboradorVO.descricao_Apresentar}" />
                                    </h:column>
                                    <h:column>
                                        <f:facet name="header">
                                            <h:outputText  value="#{msg_bt.btn_opcoes}" />
                                        </f:facet>
                                        <a4j:commandButton id="removerTipoColaborador" action="#{ColaboradorControle.removerTipoColaborador}"
                                                           reRender="dataTabletipoColaboradorVO, panelMensagemErro,panelDadosColaborador" value="#{msg_bt.btn_excluir}"
                                                           image="./imagens/botaoRemover.png" accesskey="7" styleClass="botoes"/>
                                    </h:column>
                                </h:dataTable>
                            </h:panelGrid>
                        </h:panelGrid>
                    </rich:tab>
                    <%-- Endereço --%>
                    <rich:tab id="abaEndereco" rendered="#{ColaboradorControle.mostrarAbaEndereco}" label="Endereço" switchType="client">
                        <h:panelGrid id="panelEnderecoColaborador" columns="1" width="100%" headerClass="subordinado" columnClasses="colunaCentralizada">
                            <f:facet name="header">
                                <h:outputText value="#{msg_aplic.prt_Endereco_tituloForm}"/>
                            </f:facet>
                            <h:panelGrid id="panelEnderecoColaborador1" columns="2" rowClasses="linhaImpar, linhaPar" columnClasses="classEsquerda, classDireita" width="100%">
                                <c:if test="${ClienteControle.configuracaoSistema.usarSistemaInternacional}">
                                    <c:if test="${fn:contains(ColaboradorControle.listaCamposMostrarColaboradorDinamico, 'CEP')}">
                                        <h:panelGroup>
                                            <c:if test="${fn:contains(ColaboradorControle.listaCamposObrigatorioColaboradorDinamico, 'CEP')}">
                                                <h:outputLabel value="* "></h:outputLabel>
                                            </c:if>
                                            <h:outputText value="#{msg_aplic.prt_Endereco_cep}"/>
                                        </h:panelGroup>
                                    </c:if>
                                    <c:if test="${fn:contains(ColaboradorControle.listaCamposMostrarColaboradorDinamico, 'CEP')}">
                                        <h:panelGroup>
                                            <h:inputText id="cep" size="10" maxlength="10"
                                                         onblur="blurinput(this);" onfocus="focusinput(this);"
                                                         styleClass="form"
                                                         value="#{ColaboradorControle.enderecoVO.cep}"/>
                                        </h:panelGroup>
                                    </c:if>
                                </c:if>

                                <c:if test="${!ClienteControle.configuracaoSistema.usarSistemaInternacional}">
                                    <c:if test="${fn:contains(ColaboradorControle.listaCamposMostrarColaboradorDinamico, 'CEP')}">
                                        <h:panelGroup>
                                            <c:if test="${fn:contains(ColaboradorControle.listaCamposObrigatorioColaboradorDinamico, 'CEP')}">
                                                <h:outputLabel value="* "></h:outputLabel>
                                            </c:if>
                                            <h:outputText value="#{msg_aplic.prt_Endereco_cep}"/>
                                        </h:panelGroup>
                                    </c:if>
                                    <c:if test="${fn:contains(ColaboradorControle.listaCamposMostrarColaboradorDinamico, 'CEP')}">
                                        <h:panelGroup>
                                            <h:inputText id="cep" size="10" maxlength="10"
                                                         onkeypress="return mascara(this.form, 'form:cep', '99.999-999', event);"
                                                         onblur="blurinput(this);" onfocus="focusinput(this);"
                                                         styleClass="form"
                                                         value="#{ColaboradorControle.enderecoVO.cep}"/>
                                            <rich:spacer width="10"/>
                                            <a4j:commandLink id="linkConsultaCEP"
                                                             reRender="form:panelEnderecoColaborador1"
                                                             focus="form:enderecoCorresponencia"
                                                             action="#{ColaboradorControle.consultarCEPCadastroCompleto}">Consulte o CEP</a4j:commandLink>
                                            <rich:spacer width="10"/>
                                            <a4j:commandButton id="consultaDadosCep" alt="Consultar CEP"
                                                               reRender="formConsultarCEP"
                                                               oncomplete="Richfaces.showModalPanel('panelCEP') , setFocus(formConsultarCEP,'formConsultarCEP:estadoCEP');"
                                                               image="./imagens/informacao.gif"/>
                                        </h:panelGroup>
                                    </c:if>
                                </c:if>




                                <c:if test="${fn:contains(ColaboradorControle.listaCamposMostrarColaboradorDinamico, 'ENDERECO')}">
                                    <h:panelGroup>
                                        <c:if test="${fn:contains(ColaboradorControle.listaCamposObrigatorioColaboradorDinamico, 'ENDERECO')}">
                                            <h:outputLabel value="* "></h:outputLabel>
                                        </c:if>
                                        <h:outputText   value="#{msg_aplic.prt_Endereco_endereco}" />
                                    </h:panelGroup>
                                </c:if>
                                <c:if test="${fn:contains(ColaboradorControle.listaCamposMostrarColaboradorDinamico, 'ENDERECO')}">
                                  <h:inputText  id="enderecoColaborador" size="40" maxlength="40" onblur="blurinput(this);"  onfocus="focusinput(this);" styleClass="form" value="#{ColaboradorControle.enderecoVO.endereco}" />
                                </c:if>
                                <c:if test="${fn:contains(ColaboradorControle.listaCamposMostrarColaboradorDinamico, 'NUMERO')}">
                                    <h:panelGroup>
                                        <c:if test="${fn:contains(ColaboradorControle.listaCamposObrigatorioColaboradorDinamico, 'NUMERO')}">
                                            <h:outputLabel value="* "></h:outputLabel>
                                        </c:if>
                                        <h:outputText   value="#{msg_aplic.prt_Endereco_numero}" />
                                    </h:panelGroup>
                                </c:if>
                                <c:if test="${fn:contains(ColaboradorControle.listaCamposMostrarColaboradorDinamico, 'NUMERO')}">
                                  <h:inputText  size="10" maxlength="10" onblur="blurinput(this);"  onfocus="focusinput(this);" styleClass="form" value="#{ColaboradorControle.enderecoVO.numero}" />
                                </c:if>
                                <c:if test="${fn:contains(ColaboradorControle.listaCamposMostrarColaboradorDinamico, 'COMPLEMENTO')}">
                                    <h:panelGroup>
                                        <c:if test="${fn:contains(ColaboradorControle.listaCamposObrigatorioColaboradorDinamico, 'COMPLEMENTO')}">
                                            <h:outputLabel value="* "></h:outputLabel>
                                        </c:if>
                                        <h:outputText   value="#{msg_aplic.prt_Endereco_complemento}" />
                                    </h:panelGroup>
                                </c:if>
                                <c:if test="${fn:contains(ColaboradorControle.listaCamposMostrarColaboradorDinamico, 'COMPLEMENTO')}">
                                  <h:inputText  size="40" maxlength="40" onblur="blurinput(this);"  onfocus="focusinput(this);" styleClass="form" value="#{ColaboradorControle.enderecoVO.complemento}" />
                                </c:if>
                                <c:if test="${fn:contains(ColaboradorControle.listaCamposMostrarColaboradorDinamico, 'BAIRRO')}">
                                    <h:panelGroup>
                                        <c:if test="${fn:contains(ColaboradorControle.listaCamposObrigatorioColaboradorDinamico, 'BAIRRO')}">
                                            <h:outputLabel value="* "></h:outputLabel>
                                        </c:if>
                                        <h:outputText   value="#{msg_aplic.prt_Endereco_bairro}" />
                                    </h:panelGroup>
                                </c:if>
                                <c:if test="${fn:contains(ColaboradorControle.listaCamposMostrarColaboradorDinamico, 'BAIRRO')}">
                                  <h:inputText  size="35" maxlength="35" onblur="blurinput(this);"  onfocus="focusinput(this);" styleClass="form" value="#{ColaboradorControle.enderecoVO.bairro}" />
                                </c:if>
                                <c:if test="${fn:contains(ColaboradorControle.listaCamposMostrarColaboradorDinamico, 'ENDERECOCORRESPONDENCIA')}">
                                    <h:panelGroup>
                                        <c:if test="${fn:contains(ColaboradorControle.listaCamposObrigatorioColaboradorDinamico, 'ENDERECOCORRESPONDENCIA')}">
                                            <h:outputLabel value="* "></h:outputLabel>
                                        </c:if>
                                        <h:outputText   value="#{msg_aplic.prt_Endereco_enderecoCorrespondencia}"/>
                                    </h:panelGroup>
                                </c:if>
                                <c:if test="${fn:contains(ColaboradorControle.listaCamposMostrarColaboradorDinamico, 'ENDERECOCORRESPONDENCIA')}">
                                  <h:selectBooleanCheckbox id="enderecoCorresponencia" value="#{ColaboradorControle.enderecoVO.enderecoCorrespondencia}"/>
                                </c:if>
                                <c:if test="${fn:contains(ColaboradorControle.listaCamposMostrarColaboradorDinamico, 'TIPOENDERECO')}">
                                    <h:panelGroup>
                                        <c:if test="${fn:contains(ColaboradorControle.listaCamposObrigatorioColaboradorDinamico, 'TIPOENDERECO')}">
                                            <h:outputLabel value="* "></h:outputLabel>
                                        </c:if>
                                        <h:outputText   value="#{msg_aplic.prt_Endereco_tipoEndereco}" />
                                    </h:panelGroup>
                                </c:if>
                                <c:if test="${fn:contains(ColaboradorControle.listaCamposMostrarColaboradorDinamico, 'TIPOENDERECO')}">
                                    <h:selectOneMenu  id="Endereco_tipoEndereco" onblur="blurinput(this);"  onfocus="focusinput(this);" styleClass="form" value="#{ColaboradorControle.enderecoVO.tipoEndereco}" >
                                        <f:selectItems  value="#{ColaboradorControle.listaSelectItemTipoEnderecoEndereco}" />
                                    </h:selectOneMenu>
                                </c:if>
                            </h:panelGrid>
                            <a4j:commandButton id="addEndereco" action="#{ColaboradorControle.adicionarEndereco}" reRender="panelEnderecoColaborador, panelMensagemErro" focus="enderecoColaborador" value="#{msg_bt.btn_adicionar}" image= "./imagens/botaoAdicionar.png" accesskey="5" styleClass="botoes"/>

                            <h:panelGrid columns="1" width="100%" styleClass="tabFormSubordinada">
                                <h:dataTable id="enderecoVO" width="100%" headerClass="subordinado" styleClass="tabFormSubordinada"
                                             rowClasses="linhaImpar, linhaPar" columnClasses="colunaAlinhamento"
                                             value="#{ColaboradorControle.pessoaVO.enderecoVOs}" var="endereco">
                                    <h:column>
                                        <f:facet name="header">
                                            <h:outputText  value="#{msg_aplic.prt_Endereco_endereco}" />
                                        </f:facet>
                                        <h:outputText  value="#{endereco.endereco}" />
                                    </h:column>
                                    <h:column>
                                        <f:facet name="header">
                                            <h:outputText  value="#{msg_aplic.prt_Endereco_complemento}" />
                                        </f:facet>
                                        <h:outputText  value="#{endereco.complemento}" />
                                    </h:column>
                                    <h:column>
                                        <f:facet name="header">
                                            <h:outputText  value="#{msg_aplic.prt_Endereco_numero}" />
                                        </f:facet>
                                        <h:outputText  value="#{endereco.numero}" />
                                    </h:column>
                                    <h:column>
                                        <f:facet name="header">
                                            <h:outputText  value="#{msg_aplic.prt_Endereco_bairro}" />
                                        </f:facet>
                                        <h:outputText  value="#{endereco.bairro}" />
                                    </h:column>
                                    <h:column>
                                        <f:facet name="header">
                                            <h:outputText  value="#{msg_aplic.prt_Endereco_cep}" />
                                        </f:facet>
                                        <h:outputText  value="#{endereco.cep}" />
                                    </h:column>
                                    <h:column>
                                        <f:facet name="header">
                                            <h:outputText  value="#{msg_aplic.prt_Endereco_tipoEndereco}" />
                                        </f:facet>
                                        <h:outputText  value="#{endereco.tipoEndereco_Apresentar}" />
                                    </h:column>
                                    <h:column>
                                        <f:facet name="header">
                                            <h:outputText  value="#{msg_bt.btn_opcoes}" />
                                        </f:facet>
                                        <h:panelGroup>
                                            <h:commandButton id="editarItemVenda" action="#{ColaboradorControle.editarEndereco}" value="#{msg_bt.btn_editar}" image="./imagens/botaoEditar.png" accesskey="6" styleClass="botoes"/>

                                            <h:outputText value="    "/>

                                            <h:commandButton id="removerItemVenda" immediate="true" action="#{ColaboradorControle.removerEndereco}" value="#{msg_bt.btn_excluir}" image="./imagens/botaoRemover.png" accesskey="7" styleClass="botoes"/>
                                        </h:panelGroup>
                                    </h:column>
                                </h:dataTable>
                            </h:panelGrid>
                        </h:panelGrid>
                    </rich:tab>
                    <%-- Email --%>
                    <c:if test="${fn:contains(ColaboradorControle.listaCamposMostrarColaboradorDinamico, 'EMAIL')}">
                        <rich:tab id="abaEmail" label="Email" switchType="client">
                            <h:panelGrid id="panelEmailColaborador" columns="1" width="100%" headerClass="subordinado" columnClasses="colunaCentralizada">
                                <f:facet name="header">
                                        <h:outputText value="#{msg_aplic.prt_Email_tituloForm}"/>

                                </f:facet>
                                <h:panelGrid columns="2" rowClasses="linhaImpar, linhaPar" columnClasses="classEsquerda, classDireita" width="100%">
                                    <h:panelGroup>
                                        <c:if test="${fn:contains(ColaboradorControle.listaCamposObrigatorioColaboradorDinamico, 'EMAIL')}">
                                            <h:outputLabel value="* "></h:outputLabel>
                                        </c:if>
                                        <h:outputText    value="#{msg_aplic.prt_Email_email}" />
                                    </h:panelGroup>

                                    <h:inputText  id="emailColaborador" size="40" maxlength="50" onblur="blurinput(this);"  onfocus="focusinput(this);"   styleClass="form" value="#{ColaboradorControle.emailVO.email}" />
                                    <h:outputText    value="#{msg_aplic.prt_Email_emailCorrespondencia}" />
                                    <h:selectBooleanCheckbox id="selectEmailCorres" styleClass="campos" value="#{ColaboradorControle.emailVO.emailCorrespondencia}" />
                                </h:panelGrid>
                                <a4j:commandButton id="addEmail" action="#{ColaboradorControle.adicionarEmail}" reRender="panelEmailColaborador, panelMensagemErro" focus="emailColaborador" value="#{msg_bt.btn_adicionar}" image= "./imagens/botaoAdicionar.png" accesskey="5" styleClass="botoes"/>

                                <h:panelGrid columns="1" width="100%" styleClass="tabFormSubordinada">
                                    <h:dataTable id="emailVO" width="100%" headerClass="subordinado"
                                                 rowClasses="linhaImpar, linhaPar" columnClasses="colunaAlinhamento"
                                                 value="#{ColaboradorControle.pessoaVO.emailVOs}" var="email">
                                        <h:column>
                                            <f:facet name="header">
                                                <h:outputText  value="#{msg_aplic.prt_Email_email}" />
                                            </f:facet>
                                            <h:outputText  value="#{email.email}" />
                                        </h:column>
                                        <h:column>
                                            <f:facet name="header">
                                                <h:outputText  value="#{msg_aplic.prt_Email_emailCorrespondencia}" />
                                            </f:facet>
                                            <h:outputText  value="#{email.emailCorrespondencia_Apresentar}" />
                                        </h:column>
                                        <h:column>
                                            <f:facet name="header">
                                                <h:outputText  value="#{msg_bt.btn_opcoes}" />
                                            </f:facet>
                                            <h:panelGroup>
                                                <h:commandButton id="editarEmail" title="Editar e-mail" action="#{ColaboradorControle.editarEmail}" value="#{msg_bt.btn_editar}" image="./imagens/botaoEditar.png" accesskey="6" styleClass="botoes"/>

                                                <h:outputText value="    "/>

                                                <h:commandButton id="removerEmail" title="Remover e-mail" action="#{ColaboradorControle.removerEmail}" value="#{msg_bt.btn_excluir}" image="./imagens/botaoRemover.png" accesskey="7" styleClass="botoes"/>
                                            </h:panelGroup>
                                        </h:column>
                                    </h:dataTable>
                                </h:panelGrid>
                            </h:panelGrid>
                        </rich:tab>
                    </c:if>
                    <%-- Telefone --%>
                    <c:if test="${fn:contains(ColaboradorControle.listaCamposMostrarColaboradorDinamico, 'TELEFONE')}">
                        <rich:tab id="abaTelefone" label="Telefone" switchType="client">
                            <h:panelGrid id="panelTelefoneColaborador" columns="1" width="100%" headerClass="subordinado" columnClasses="colunaCentralizada">
                                <f:facet name="header">
                                        <h:outputText value="#{msg_aplic.prt_Telefone_tituloForm}"/>

                                </f:facet>
                                <h:panelGrid columns="2" rowClasses="linhaImpar, linhaPar" columnClasses="classEsquerda, classDireita" width="100%">
                                    <h:panelGroup>
                                        <c:if test="${fn:contains(ColaboradorControle.listaCamposObrigatorioColaboradorDinamico, 'TELEFONE')}">
                                            <h:outputLabel value="* "></h:outputLabel>
                                        </c:if>
                                        <h:outputText   value="#{msg_aplic.prt_Telefone_numero}" />
                                        <h:inputText id="ddiTelefoneColaborador"
                                                     size="4" title="DDI"
                                                     maxlength="4"
                                                     onblur="blurinput(this);"
                                                     onkeypress="return mascara(this.form, this.id , '+999', event);"
                                                     onfocus="focusinput(this);"
                                                     styleClass="form"
                                                     rendered="#{ColaboradorControle.configuracaoSistema.usarSistemaInternacional}"
                                                     value="#{ColaboradorControle.telefoneVO.ddi}"
                                                     style="margin: 0 3px"/>

                                    </h:panelGroup>
                                    <h:panelGroup>
                                        <c:if test="${!ColaboradorControle.configuracaoSistema.usarSistemaInternacional}">
                                        <h:inputText  id="numeroTelefone"
                                                      size="13"
                                                      maxlength="13"
                                                      onchange="return validar_Telefone(this.id);"
                                                      onblur="blurinput(this);"
                                                      onkeypress="return mascara(this.form, this.id , '(99)999999999', event);"
                                                      onfocus="focusinput(this);"
                                                      styleClass="form"
                                                      value="#{ColaboradorControle.telefoneVO.numero}" />
                                        </c:if>
                                        <c:if test="${ColaboradorControle.configuracaoSistema.usarSistemaInternacional}">
                                            <h:inputText  id="numeroTelefoneInt"
                                                          size="13"
                                                          maxlength="11"
                                                          onblur="blurinput(this);"
                                                          onfocus="focusinput(this);"
                                                          styleClass="form"
                                                          value="#{ColaboradorControle.telefoneVO.numero}" />
                                            </c:if>
                                        <%--<rich:jQuery id="mskTelefone" selector="#numeroTelefone" timing="onload" query="mask('(99)9999-9999')" />--%>
                                    </h:panelGroup>
                                    <h:panelGroup>
                                        <c:if test="${fn:contains(ColaboradorControle.listaCamposObrigatorioColaboradorDinamico, 'TELEFONE')}">
                                            <h:outputLabel value="* "></h:outputLabel>
                                        </c:if>
                                        <h:outputText   value="#{msg_aplic.prt_Telefone_tipoTelefone}" />
                                    </h:panelGroup>
                                    <h:selectOneMenu  id="Telefone_tipoTelefone" onblur="blurinput(this);"  onfocus="focusinput(this);" styleClass="form" value="#{ColaboradorControle.telefoneVO.tipoTelefone}" >
                                        <f:selectItems  value="#{ColaboradorControle.listaSelectItemTipoTelefoneTelefone}" />
                                    </h:selectOneMenu>
                                </h:panelGrid>
                                <a4j:commandButton id="addTelefone" action="#{ColaboradorControle.adicionarTelefone}" reRender="panelTelefoneColaborador, panelMensagemErro" focus="numeroTelefone" value="#{msg_bt.btn_adicionar}" image= "./imagens/botaoAdicionar.png" accesskey="6" styleClass="botoes"/>

                                <h:panelGrid columns="1" width="100%" styleClass="tabFormSubordinada">
                                    <h:dataTable id="telefoneVO" width="100%" headerClass="subordinado" styleClass="tabFormSubordinada"
                                                 rowClasses="linhaImpar, linhaPar" columnClasses="colunaAlinhamento"
                                                 value="#{ColaboradorControle.pessoaVO.telefoneVOs}" var="telefone">
                                        <h:column>
                                            <f:facet name="header">
                                                <h:outputText  value="#{msg_aplic.prt_Telefone_numero}" />
                                            </f:facet>
                                            <h:outputText  value="#{telefone.numeroApresentar}" />
                                        </h:column>
                                        <h:column>
                                            <f:facet name="header">
                                                <h:outputText  value="#{msg_aplic.prt_Telefone_tipoTelefone}" />
                                            </f:facet>
                                            <h:outputText  value="#{telefone.tipoTelefone_Apresentar}" />
                                        </h:column>
                                        <h:column>
                                            <f:facet name="header">
                                                <h:outputText  value="#{msg_bt.btn_opcoes}" />
                                            </f:facet>
                                            <h:panelGroup>
                                                <h:commandButton id="editarItemVenda"  action="#{ColaboradorControle.editarTelefone}" value="#{msg_bt.btn_editar}" image="./imagens/botaoEditar.png" accesskey="6" styleClass="botoes"/>

                                                <h:outputText value="    "/>

                                                <h:commandButton id="removerItemVenda" immediate="true" action="#{ColaboradorControle.removerTelefone}" value="#{msg_bt.btn_excluir}" image="./imagens/botaoRemover.png" accesskey="7" styleClass="botoes"/>
                                            </h:panelGroup>
                                        </h:column>
                                    </h:dataTable>
                                </h:panelGrid>
                            </h:panelGrid>
                        </rich:tab>
                    </c:if>

                    <%-- Foto--%>
                    <rich:tab id="abaFoto" label="Foto" action="#{ColaboradorControle.prepararParaCapturarFoto}" reRender="form,panelExistePessoa,panelExisteColaborador">
                        <rich:tabPanel tabClass="aba" width="100%" activeTabClass="true" headerAlignment="left"  >
                            <rich:tab id="fotoPessoal" label="Foto" >
                                <a4j:jsFunction name="updateFoto" action="#{ColaboradorControle.recarregarFoto}" reRender="panelFotoColaborador"/>
                                <h:panelGrid id="panelFotoColaborador" columns="1" width="100%" headerClass="subordinado" columnClasses="colunaEsquerda">
                                    <h:panelGrid id="panelFoto" columns="1">                                        
                                        <a4j:mediaOutput element="img" id="imagem1"  align="left" style="left:0px;width:150px;height:150px "  cacheable="false" session="false"
                                                         rendered="#{!SuperControle.fotosNaNuvem}"
                                                         createContent="#{ColaboradorControle.paintFoto}" value="#{ImagemData}" mimeType="image/jpeg" >
                                            <f:param value="#{SuperControle.timeStamp}" name="time"/>
                                            <f:param name="largura" value="150"/>
                                            <f:param name="altura" value="150"/>
                                        </a4j:mediaOutput>
                                        <h:graphicImage rendered="#{SuperControle.fotosNaNuvem}"
                                                        width="150" height="150"
                                                        style="width:150px;height:150px"
                                                        url="#{ColaboradorControle.paintFotoDaNuvem}?time=#{SuperControle.timeStamp}">
                                        </h:graphicImage>
                                    </h:panelGrid>
                                    <h:panelGrid columns="2" columnClasses="centralizado">
                                        <a4j:commandButton actionListener="#{CapturaFotoControle.selecionarPessoa}"
                                                           action="#{ColaboradorControle.verificaColaborador}"
                                                           id="btnAlterarFoto" value="#{msg_bt.btn_capturarfoto}" image="./imagens/webcam.png"
                                                           oncomplete="#{ColaboradorControle.onComplete}"
                                                           reRender="form"
                                                           alt="#{msg_bt.btn_capturarfoto}" title="#{msg_bt.btn_capturarfoto}" styleClass="botoes">
                                            <f:attribute name="pessoa" value="#{ColaboradorControle.colaboradorVO.pessoa.codigo}"/>
                                        </a4j:commandButton>


                                        <h:panelGroup id="grupoBtnRemoverFoto">
                                            <a4j:commandButton id="btnRemoverFoto" reRender="mdlMensagemGenerica"
                                                               oncomplete="#{ColaboradorControle.msgAlert}" action="#{ColaboradorControle.confirmarExcluir}"  image="./images/icon_delete.png"
                                                               accesskey="3" styleClass="botoes" style="border: 0px" >
                                                <f:param name="metodochamar" value="removerFoto"/>
                                            </a4j:commandButton>
                                        </h:panelGroup>

                                    </h:panelGrid>
                                </h:panelGrid>
                            </rich:tab>
                            <rich:tab id="logotipo" label="Foto Personal" rendered="#{ColaboradorControle.mostrarFotoParaPersonal}">
                                <h:panelGrid id="panelLogotipo" columns="1" width="100%" headerClass="subordinado" columnClasses="colunaCentralizada">

                                    <h:panelGrid columns="1" width="100%" >

                                        <a4j:mediaOutput element="img" id="foto"  style="width:120px;height:160px "  cacheable="false"
                                                         createContent="#{ColaboradorControle.paintFotoSemPesquisarNoBanco}"  value="#{ImagemData}" mimeType="image/jpeg" >
                                            <f:param value="#{SuperControle.timeStamp}" name="time"/>
                                            <f:param name="largura" value="120"/>
                                            <f:param name="altura" value="160"/>
                                        </a4j:mediaOutput>
                                        <h:panelGrid columns="2" columnClasses="centralizado">
                                            <rich:fileUpload listHeight="0"
                                                             listWidth="150"
                                                             noDuplicate="false"
                                                             fileUploadListener="#{ColaboradorControle.uploadFotoPersonal}"
                                                             maxFilesQuantity="1"
                                                             addControlLabel="Adicionar"
                                                             cancelEntryControlLabel="Cancelar"
                                                             doneLabel="Pronto"
                                                             sizeErrorLabel="Arquivo não Enviado. Tamanho Máximo Permitido 100 MB"
                                                             progressLabel="Enviando"
                                                             stopControlLabel="Parar"
                                                             uploadControlLabel="Enviar"
                                                             transferErrorLabel="Falha de Transmissão"
                                                             stopEntryControlLabel="Parar"
                                                             id="upload"
                                                             immediateUpload="true"
                                                             autoclear="true"
                                                             acceptedTypes="png,gif,jpg,jpeg,ico,bmp,PNG,GIF,JPG,JPEG,ICO,BMP">

                                                <a4j:support event="onuploadcomplete" ajaxSingle="true" reRender="panelLogotipo" />
                                                <a4j:support event="oncomplete" ajaxSingle="true" reRender="panelLogotipo, form:panelErroMensagem" />
                                            </rich:fileUpload>
                                            <a4j:commandButton id="btnRemoverFotoPersonal" value="#{msg_bt.btn_removerfoto}" image="./images/icon_delete.png"
                                                               action="#{ColaboradorControle.removerFotoPersonal}"
                                                               reRender="panelLogotipo"
                                                               alt="#{msg_bt.btn_removerfoto}" title="#{msg_bt.btn_removerfoto}" styleClass="botoes"/>
                                        </h:panelGrid>

                                    </h:panelGrid>
                                </h:panelGrid>
                            </rich:tab>
                        </rich:tabPanel>

                    </rich:tab>
                    <rich:tab id="abaComissao" label="Comissão">
                        <h:panelGrid id="panelComissao" columns="1" width="100%" headerClass="subordinado" columnClasses="colunaCentralizada">
                            <h:panelGrid id="panelModalidadeComissao" columns="1" width="100%" headerClass="subordinado" columnClasses="colunaCentralizada">
                                <f:facet name="header">
                                    <h:outputText value="#{msg_aplic.prt_Colaborador_modalidade}"/>
                                </f:facet>
                                <h:panelGrid id="modalidade" columns="2" rowClasses="linhaImpar, linhaPar" columnClasses="classEsquerda, classDireita" width="100%">
                                    <h:outputText    value="#{msg_aplic.prt_Colaborador_modalidade}" />
                                    <h:panelGroup>
                                        <h:inputText styleClass="form" id="nomeModalidade" size="70" value="#{ColaboradorControle.modalidadeComissaoColaboradorVO.modalidade.nome}" />
                                        <rich:suggestionbox   height="200" width="200"
                                                              for="nomeModalidade"
                                                              status="statusInComponent"
                                                              minChars="1"
                                                              immediate="true"
                                                              suggestionAction="#{ColaboradorControle.autocompleteModalidade}"
                                                              nothingLabel="Nenhuma Modalidade encontrada !" var="result" id="suggestionModalidade">

                                            <a4j:support event="onselect"
                                                         action="#{ColaboradorControle.selecionarModalidade}"/>
                                            <h:column>
                                                <h:outputText value="#{result.nome}" />
                                            </h:column>
                                        </rich:suggestionbox>
                                        <rich:spacer width="15px" />
                                        <a4j:commandButton action="#{ColaboradorControle.limparCampoModalidadeSuggestion}" alt="Limpar Nome da Modalidade"
                                                           reRender="modalidade" image="./images/limpar.gif"/>
                                    </h:panelGroup>
                                    <h:outputText value="#{msg_aplic.prt_Colaborador_porcComissao}" />
                                    <h:inputText id="porcComissaoModalidade"  size="10" maxlength="10"  onblur="blurinput(this);"
                                                 onkeypress="return formatar_moeda(this,'.',',',event);" onfocus="focusinput(this);"
                                                 styleClass="form" value="#{ColaboradorControle.modalidadeComissaoColaboradorVO.porcComissao}">
                                        <f:converter converterId="FormatadorNumerico" />
                                    </h:inputText>
                                    <h:outputText styleClass="tooltipster"
                                                  value="#{msg_aplic.prt_Colaborador_valorComissao}"
                                                  title="Valor fixo por cada aula."/>
                                    <h:inputText id="valorComissaoModalidade"  size="10" maxlength="10"  onblur="blurinput(this);"
                                                 onkeypress="return formatar_moeda(this,'.',',',event);" onfocus="focusinput(this);"
                                                 styleClass="form" value="#{ColaboradorControle.modalidadeComissaoColaboradorVO.valorComissao}">
                                        <f:converter converterId="FormatadorNumerico" />
                                    </h:inputText>
                                </h:panelGrid>
                                <a4j:commandButton id="addModalidadeCom" action="#{ColaboradorControle.adicionarModalidade}"
                                                   reRender="modalidadeComissaoVO,panelMensagemErro,panelModalidadeComissao" focus="form:modalidade"
                                                   value="     #{msg_bt.btn_adicionar} " accesskey="9"/>

                                <h:panelGrid columns="1" width="100%" styleClass="tabFormSubordinada">
                                    <h:dataTable id="modalidadeComissaoVO" width="100%" headerClass="subordinado"
                                                 rowClasses="linhaImpar, linhaPar" columnClasses="colunaAlinhamento,colunaAlinhamento,colunaAlinhamento,colunaAlinhamento"
                                                 value="#{ColaboradorControle.colaboradorVO.listaModalidadesComissaoColaboradorVO}" var="modalidadeComissao">
                                        <h:column>
                                            <f:facet name="header">
                                                <h:outputText  value="#{msg_aplic.prt_Colaborador_modalidade}" />
                                            </f:facet>
                                            <h:outputText  value="#{modalidadeComissao.modalidade.nome}" />
                                        </h:column>
                                        <h:column>
                                            <f:facet name="header">
                                                <h:outputText  value="Porc. Comis.(%)" />
                                            </f:facet>
                                            <h:outputText  value="#{modalidadeComissao.porcComissao}" >
                                                <f:converter converterId="FormatadorNumerico" />
                                            </h:outputText>
                                        </h:column>
                                        <h:column>
                                            <f:facet name="header">
                                                <h:outputText  value="Valor Comis.(R$)" />
                                            </f:facet>
                                            <h:outputText  value="#{modalidadeComissao.valorComissao}" >
                                                <f:converter converterId="FormatadorNumerico" />
                                            </h:outputText>
                                        </h:column>
                                        <h:column>
                                            <f:facet name="header">
                                                <h:outputText  value="#{msg_bt.btn_opcoes}" />
                                            </f:facet>
                                            <h:panelGroup>
                                                <h:commandButton id="editarModalidade" action="#{ColaboradorControle.editarModalidade}"
                                                                 value="#{msg_bt.btn_editar}" image="./imagens/botaoEditar.png" accesskey="6" styleClass="botoes"/>
                                                <a4j:commandButton id="removerModalidade" reRender="modalidadeComissaoVO"
                                                                   action="#{ColaboradorControle.removerModalidade}"
                                                                   value="#{msg_bt.btn_excluir}" image="./imagens/botaoRemover.png"
                                                                   accesskey="7" styleClass="botoes"/>
                                            </h:panelGroup>
                                        </h:column>
                                    </h:dataTable>
                                </h:panelGrid>
                            </h:panelGrid>

                            <h:panelGrid id="panelTurma" columns="1" width="100%" headerClass="subordinado" columnClasses="colunaCentralizada">
                                <f:facet name="header">
                                    <h:outputText value="#{msg_aplic.prt_Colaborador_turma}"/>
                                </f:facet>
                                <h:panelGrid id="turma" columns="2" rowClasses="linhaImpar, linhaPar" headerClass="subordinado"
                                             columnClasses="classEsquerda, classDireita" width="100%">
                                    <h:outputText    value="#{msg_aplic.prt_Colaborador_turma}" />
                                    <h:panelGroup>
                                        <h:inputText styleClass="form" id="nomeTurma" size="70" value="#{ColaboradorControle.turmaComissaoColaboradorVO.turma.descricao}" />
                                        <rich:suggestionbox   height="200" width="200"
                                                              for="nomeTurma"
                                                              status="statusInComponent"
                                                              minChars="1"
                                                              immediate="true"
                                                              suggestionAction="#{ColaboradorControle.autocompleteTurma}"
                                                              nothingLabel="Nenhuma Turma encontrada !" var="result" id="suggestionTurma">

                                            <a4j:support event="onselect"
                                                         action="#{ColaboradorControle.selecionarTurma}">
                                            </a4j:support>
                                            <h:column>
                                                <h:outputText value="#{result.descricao}" />
                                            </h:column>
                                        </rich:suggestionbox>
                                        <rich:spacer width="15px" />
                                        <a4j:commandButton action="#{ColaboradorControle.limparCampoTurmaSuggestion}" alt="Limpar Nome da Turma"
                                                           reRender="turma" image="./images/limpar.gif"/>
                                    </h:panelGroup>
                                    <h:outputText value="#{msg_aplic.prt_Colaborador_porcComissao}" />
                                    <h:inputText id="porcComissaoTurma"  size="10" maxlength="10"  onblur="blurinput(this);"
                                                 onkeypress="return formatar_moeda(this,'.',',',event);" onfocus="focusinput(this);"
                                                 styleClass="form" value="#{ColaboradorControle.turmaComissaoColaboradorVO.porcComissao}">
                                        <f:converter converterId="FormatadorNumerico" />
                                    </h:inputText>
                                    <h:outputText styleClass="tooltipster"
                                                  value="#{msg_aplic.prt_Colaborador_valorComissao}"
                                                  title="Valor fixo por cada aula."/>
                                    <h:inputText id="valorComissaoTurma"  size="10" maxlength="10"  onblur="blurinput(this);"
                                                 onkeypress="return formatar_moeda(this,'.',',',event);" onfocus="focusinput(this);"
                                                 styleClass="form" value="#{ColaboradorControle.turmaComissaoColaboradorVO.valorComissao}">
                                        <f:converter converterId="FormatadorNumerico" />
                                    </h:inputText>
                                </h:panelGrid>
                                <a4j:commandButton id="addTurma" action="#{ColaboradorControle.adicionarTurma}"
                                                   reRender="panelTurma,panelMensagemErro,turmaComissaoVO"
                                                   focus="nomeTurma"
                                                   value="     #{msg_bt.btn_adicionar}     " accesskey="10"/>

                                <h:panelGrid columns="1" width="100%" styleClass="tabFormSubordinada">
                                    <h:dataTable id="turmaComissaoVO" width="100%" headerClass="subordinado" styleClass="tabFormSubordinada"
                                                 rowClasses="linhaImpar, linhaPar" columnClasses="colunaAlinhamento,colunaAlinhamento,colunaAlinhamento,colunaAlinhamento"
                                                 value="#{ColaboradorControle.colaboradorVO.listaTurmaComissaoColaboradorVO}" var="turmaComissaoVO">
                                        <h:column>
                                            <f:facet name="header">
                                                <h:outputText  value="#{msg_aplic.prt_Colaborador_turma}" />
                                            </f:facet>
                                            <h:outputText  value="#{turmaComissaoVO.turma.descricao}" />
                                        </h:column>
                                        <h:column>
                                            <f:facet name="header">
                                                <h:outputText  value="Porc. Comis.(%)" />
                                            </f:facet>
                                            <h:outputText  value="#{turmaComissaoVO.porcComissao}" >
                                                <f:converter converterId="FormatadorNumerico" />
                                            </h:outputText>
                                        </h:column>
                                        <h:column>
                                            <f:facet name="header">
                                                <h:outputText  value="Valor Comis.(R$)" />
                                            </f:facet>
                                            <h:outputText  value="#{turmaComissaoVO.valorComissao}" >
                                                <f:converter converterId="FormatadorNumerico" />
                                            </h:outputText>
                                        </h:column>
                                        <h:column>
                                            <f:facet name="header">
                                                <h:outputText  value="#{msg_bt.btn_opcoes}" />
                                            </f:facet>
                                            <h:panelGroup>
                                                <h:commandButton id="editarTurma" action="#{ColaboradorControle.editarTurma}"
                                                                 value="#{msg_bt.btn_editar}" image="./imagens/botaoEditar.png" accesskey="6" styleClass="botoes"/>
                                                <a4j:commandButton id="removerTurmaComissao"  reRender="turmaComissaoVO"
                                                                   action="#{ColaboradorControle.removerTurma}"
                                                                   value="#{msg_bt.btn_excluir}" image="./imagens/botaoRemover.png" accesskey="7" styleClass="botoes"/>
                                            </h:panelGroup>
                                        </h:column>
                                    </h:dataTable>
                                </h:panelGrid>
                            </h:panelGrid>
                            <h:panelGrid id="panelAlunoComissao" columns="1" width="100%" headerClass="subordinado" columnClasses="colunaCentralizada">
                                <f:facet name="header">
                                    <h:outputText value="#{msg_aplic.prt_Colaborador_aluno}"/>
                                </f:facet>
                                <h:panelGrid id="aluno" columns="2" rowClasses="linhaImpar, linhaPar" headerClass="subordinado" columnClasses="classEsquerda, classDireita" width="100%">
                                    <h:outputText    value="#{msg_aplic.prt_Colaborador_aluno}" />
                                    <h:panelGroup>
                                        <h:inputText styleClass="form" id="nomeAluno" size="70" value="#{ColaboradorControle.alunoComissaoColaboradorVO.pessoaVO.nome}" />
                                        <rich:suggestionbox   height="200" width="200"
                                                              for="nomeAluno"
                                                              status="statusInComponent"
                                                              minChars="1"
                                                              immediate="true"
                                                              suggestionAction="#{ColaboradorControle.autocompleteAluno}"
                                                              nothingLabel="Nenhum Aluno encontrado !" var="result" id="suggestionAluno">

                                            <a4j:support event="onselect"
                                                         action="#{ColaboradorControle.selecionarAluno}">
                                            </a4j:support>
                                            <h:column>
                                                <h:outputText value="#{result.nome}" />
                                            </h:column>
                                        </rich:suggestionbox>
                                        <rich:spacer width="15px" />
                                        <a4j:commandButton action="#{ColaboradorControle.limparCampoAlunoSuggestion}"
                                                           alt="Limpar Nome do Aluno" reRender="aluno" image="./images/limpar.gif"/>
                                    </h:panelGroup>
                                    <h:outputText value="#{msg_aplic.prt_Colaborador_porcComissao}" />
                                    <h:inputText id="porcComissaoAluno"  size="10" maxlength="10"  onblur="blurinput(this);"
                                                 onkeypress="return formatar_moeda(this,'.',',',event);" onfocus="focusinput(this);"
                                                 styleClass="form" value="#{ColaboradorControle.alunoComissaoColaboradorVO.porcComissao}">
                                        <f:converter converterId="FormatadorNumerico" />
                                    </h:inputText>
                                    <h:outputText styleClass="tooltipster"
                                                  value="#{msg_aplic.prt_Colaborador_valorComissao}"
                                                  title="Valor fixo por cada aula."/>
                                    <h:inputText id="valorComissaoAluno"  size="10" maxlength="10"  onblur="blurinput(this);"
                                                 onkeypress="return formatar_moeda(this,'.',',',event);" onfocus="focusinput(this);"
                                                 styleClass="form" value="#{ColaboradorControle.alunoComissaoColaboradorVO.valorComissao}">
                                        <f:converter converterId="FormatadorNumerico" />
                                    </h:inputText>
                                </h:panelGrid>
                                <a4j:commandButton id="addAlunoComissao" action="#{ColaboradorControle.adicionarAluno}" reRender="panelAlunoComissao,panelMensagemErro,alunoComissaoVO "
                                                   focus="nomeAluno"
                                                   value="     #{msg_bt.btn_adicionar}     " accesskey="10"/>

                                <h:panelGrid columns="1" width="100%" styleClass="tabFormSubordinada">
                                    <h:dataTable id="alunoComissaoVO" width="100%" headerClass="subordinado" styleClass="tabFormSubordinada"
                                                 rowClasses="linhaImpar, linhaPar" columnClasses="colunaAlinhamento,colunaAlinhamento,colunaAlinhamento,colunaAlinhamento"
                                                 value="#{ColaboradorControle.colaboradorVO.listaAlunoComissaoColaboradorVOs}" var="alunoComissaoVO">
                                        <h:column>
                                            <f:facet name="header">
                                                <h:outputText  value="#{msg_aplic.prt_Colaborador_aluno}" />
                                            </f:facet>
                                            <h:outputText  value="#{alunoComissaoVO.pessoaVO.nome}" />
                                        </h:column>
                                        <h:column>
                                            <f:facet name="header">
                                                <h:outputText  value="Porc. Comis.(%)" />
                                            </f:facet>
                                            <h:outputText  value="#{alunoComissaoVO.porcComissao}" >
                                                <f:converter converterId="FormatadorNumerico" />
                                            </h:outputText>
                                        </h:column>
                                        <h:column>
                                            <f:facet name="header">
                                                <h:outputText  value="Valor Comis.(R$)" />
                                            </f:facet>
                                            <h:outputText  value="#{alunoComissaoVO.valorComissao}" >
                                                <f:converter converterId="FormatadorNumerico" />
                                            </h:outputText>
                                        </h:column>
                                        <h:column>
                                            <f:facet name="header">
                                                <h:outputText  value="#{msg_bt.btn_opcoes}" />
                                            </f:facet>
                                            <h:panelGroup>
                                                <h:commandButton id="editarAluno" action="#{ColaboradorControle.editarAluno}"
                                                                 value="#{msg_bt.btn_editar}" image="./imagens/botaoEditar.png" accesskey="6" styleClass="botoes"/>
                                                <a4j:commandButton id="removerAlunoComissao"  reRender="alunoComissaoVO"
                                                                   action="#{ColaboradorControle.removerAluno}"
                                                                   value="#{msg_bt.btn_excluir}" image="./imagens/botaoRemover.png"
                                                                   accesskey="7" styleClass="botoes"/>
                                            </h:panelGroup>
                                        </h:column>
                                    </h:dataTable>
                                </h:panelGrid>
                            </h:panelGrid>
                        </h:panelGrid>
                    </rich:tab>
                    <%-- Colaborador x Modalidade --%>
                    <rich:tab
                            id="dadosColaboradorModalidade" label="Modalidade">
                        <h:panelGrid id="panelColaboradorModalidade" columns="1" width="100%" headerClass="subordinado"
                                     columnClasses="colunaCentralizada">
                            <f:facet name="header">
                                <h:outputText value="#{msg_aplic.prt_ColaboradorModalidade_tituloForm}"/>
                            </f:facet>
                            <h:panelGrid columns="2" width="100%" rowClasses="linhaImpar, linhaPar"
                                         columnClasses="classEsquerda, classDireita" footerClass="colunaCentralizada">
                                <h:outputText styleClass="tituloCampos"
                                              value="#{msg_aplic.prt_ColaboradorModalidade_modalidade}"/>
                                <h:panelGroup>
                                    <h:selectOneMenu id="colaboradorModalidade" onblur="blurinput(this);"
                                                     onfocus="focusinput(this);" styleClass="form"
                                                     value="#{ColaboradorControle.colaboradorModalidadeVO.modalidadeVO.codigo}">
                                        <f:selectItems value="#{ColaboradorControle.listaSelectItemColaboradorModalidade}"/>
                                    </h:selectOneMenu>
                                    <rich:spacer width="5px"/>
                                    <a4j:commandButton id="atualizarColaboradorModalidade"
                                                       action="#{ColaboradorControle.montarListaSelectItemColaboradorModalidade}"
                                                       image="imagens/atualizar.png" immediate="true" ajaxSingle="true"
                                                       reRender="panelColaboradorModalidade"/>
                                    <rich:spacer width="5px"/>
                                    <a4j:commandButton id="cadastroNovaModalidade" alt="Cadastrar Modalidade"
                                                       onclick="abrirPopup('modalidadeCons.jsp', 'Modalidade', 800, 595);"
                                                       action="#{ModalidadeControle.novoSemRedirect}"
                                                       image="./images/icon_add.gif"/>
                                </h:panelGroup>
                            </h:panelGrid>
                            <a4j:commandButton id="addColaboradorModalidade" action="#{ColaboradorControle.adicionarColaboradorModalidade}"
                                               reRender="panelColaboradorModalidade, panelMensagem"
                                               focus="form:colaboradorModalidade" value="#{msg_bt.btn_adicionar}"
                                               image="./imagens/botaoAdicionar.png" styleClass="botoes"/>

                            <h:panelGrid columns="1" width="100%" styleClass="tabFormSubordinada">
                                <h:dataTable id="colaboradorModalidadeVO" width="100%" headerClass="subordinado"
                                             styleClass="tabFormSubordinada"
                                             rowClasses="linhaImpar, linhaPar" columnClasses="colunaAlinhamento"
                                             value="#{ColaboradorControle.colaboradorVO.colaboradorModalidadeVOS}" var="colaboradorModalidade">
                                    <h:column>
                                        <f:facet name="header">
                                            <h:outputText value="#{msg_aplic.prt_ColaboradorModalidade_modalidade}"/>
                                        </f:facet>
                                        <h:outputText value="#{colaboradorModalidade.modalidadeVO.codigo} - "/>
                                        <h:outputText value="#{colaboradorModalidade.modalidadeVO.nome}"/>
                                    </h:column>
                                    <h:column>
                                        <f:facet name="header">
                                            <h:outputText value="#{msg_bt.btn_opcoes}"/>
                                        </f:facet>
                                        <h:panelGroup>
                                            <a4j:commandButton id="removerColaboradorModalidade" reRender="form" ajaxSingle="true"
                                                               immediate="true"
                                                               oncomplete="#{ColaboradorControle.mensagemNotificar}"
                                                               action="#{ColaboradorControle.removerColaboradorModalidade}"
                                                               value="#{msg_bt.btn_excluir}"
                                                               image="./imagens/botaoRemover.png" accesskey="7"
                                                               styleClass="botoes"/>
                                        </h:panelGroup>
                                    </h:column>
                                </h:dataTable>
                            </h:panelGrid>
                        </h:panelGrid>
                    </rich:tab>

                    <%-- Últimos acessos --%>
                    <rich:tab id="abaUltimosAcessos" label="Últimos Acessos" action="#{ColaboradorControle.montarListaUltimosAcessos}">
                        <h:panelGrid id="panelUltimosAcessos" columns="1" rowClasses="linhaImpar, linhaPar" columnClasses="classEsquerda">

                            <rich:dataTable id="listaUltimosAcessosCliente" width="100%" border="0" rows="5" cellspacing="0" cellpadding="2" styleClass="textsmall"
                                            columnClasses="centralizado, centralizado, centralizado" value="#{ColaboradorControle.listaUltimosAcessos}" var="ultimoAcesso">
                                <rich:column>
                                    <f:facet name="header">
                                        <h:outputText  style="font-weight: bold" value="#{msg_aplic.prt_AcessoCliente_codigo}"/>
                                    </f:facet>
                                    <h:panelGroup>
                                        <h:outputText style="font-weight: bold" styleClass="red" value="#{ultimoAcesso.codigo}"/>
                                    </h:panelGroup>
                                </rich:column>
                                <rich:column>
                                    <f:facet name="header">
                                        <h:outputText  style="font-weight: bold" value="#{msg_aplic.prt_AcessoCliente_sentido}"/>
                                    </f:facet>
                                    <h:outputText style="font-weight: bold" styleClass="red" value="#{ultimoAcesso.sentido}"/>
                                </rich:column>
                                <rich:column>
                                    <f:facet name="header">
                                        <h:outputText  style="font-weight: bold" value="#{msg_aplic.prt_AcessoCliente_localAcesso}"/>
                                    </f:facet>
                                    <h:outputText style="font-weight: bold" styleClass="red" value="#{ultimoAcesso.localAcesso.descricao}"/>
                                </rich:column>
                                <rich:column>
                                    <f:facet name="header">
                                        <h:outputText  style="font-weight: bold;" value="#{msg_aplic.prt_AcessoCliente_coletor}"/>
                                    </f:facet>
                                    <h:outputText value="#{ultimoAcesso.coletor.descricao}"/>
                                </rich:column>
                                <rich:column>
                                    <f:facet name="header">
                                        <h:outputText  style="font-weight: bold;" value="#{msg_aplic.prt_AcessoCliente_dataHoraEntrada}"/>
                                    </f:facet>
                                    <h:outputText style="font-weight: bold" styleClass="red" value="#{ultimoAcesso.dataHoraEntrada}">
                                        <f:convertDateTime type="date" dateStyle="short" locale="pt" pattern="dd/MM/yyyy HH:mm"/>
                                    </h:outputText>
                                    -
                                    <h:outputText style="font-weight: bold" styleClass="red" value="#{ultimoAcesso.nomeDiaSemanaAcesso}"/>
                                </rich:column>
                                <rich:column>
                                    <f:facet name="header">
                                        <h:outputText  style="font-weight: bold;" value="#{msg_aplic.prt_AcessoCliente_dataHoraSaida}"/>
                                    </f:facet>
                                    <h:outputText style="font-weight: bold" styleClass="red" value="#{ultimoAcesso.dataHoraSaida}">
                                        <f:convertDateTime type="date" dateStyle="short" locale="pt" pattern="dd/MM/yyyy HH:mm:ss"/>
                                    </h:outputText>
                                </rich:column>
                                <rich:column>
                                    <f:facet name="header">
                                        <h:outputText style="font-weight: bold;"
                                                      value="#{msg_aplic.prt_AcessoColaborador_meioIdentificacaoEntrada}" />
                                    </f:facet>
                                    <h:outputText style="font-weight: bold" styleClass="red"
                                                  value="#{ultimoAcesso.meioIdentificacaoEntrada.descricao}" />
                                </rich:column>
                            </rich:dataTable>
                        </h:panelGrid>
                    </rich:tab>
                    <%-- Histórico de Compras--%>
                    <rich:tab label="Histórico de Compras"
                              action="#{ColaboradorControle.montarListaHistoricoCompras}"
                              id="abaHistoricoCompras">
                        <f:attribute name="codigoColaborador"
                                     value="#{ColaboradorControle.clienteVO.codigo}" />
                        <div style="clear: both;">

                            <table width="98%" border="0" align="left" cellpadding="0"
                                   cellspacing="0" bgcolor="#e6e6e6"
                                   style="margin-right: 30px; margin-bottom: 30px; padding: 10px;">

                                <tr>
                                    <td align="left" valign="top" style="padding-bottom: 5px;">
                                        <div style="clear: both;" class="text">
                                            <p style="margin-bottom: 6px;">
                                                <img src="images/arrow2.gif" width="16" height="16" style="vertical-align: middle; margin-right: 6px;">
                                                <h:outputText style="font-weight: bold;" value="Planos personal" />
                                            </p>
                                        </div>
                                        <div class="sep" style="margin: 4px 0 10px 0;">
                                            <img src="images/shim.gif">
                                        </div>
                                    </td>
                                </tr>
                                <tr>
                                    <td align="left" valign="top">
                                        <rich:dataTable id="controlesTaxaPersonal"
                                                        width="100%"
                                                        border="0"
                                                        rows="#{ColaboradorControle.nrPaginaPlano}"
                                                        cellspacing="0"
                                                        cellpadding="0"
                                                        styleClass="textsmall"
                                                        columnClasses="centralizado, centralizado, centralizado"
                                                        value="#{ColaboradorControle.controlesTaxaPersonal}" var="controleTaxaPersonal">

                                            <rich:column>
                                                <f:facet name="header">
                                                    <h:outputText style="font-weight: bold" value="Código do plano" />
                                                </f:facet>
                                                <h:outputText style="font-weight: bold" styleClass="blue" value="#{controleTaxaPersonal.plano.codigo}" />
                                            </rich:column>
                                            <rich:column>
                                                <f:facet name="header">
                                                    <h:outputText style="font-weight: bold" value="Resp. Lançamento" />
                                                </f:facet>
                                                <h:outputText style="font-weight: bold" styleClass="blue" value="#{controleTaxaPersonal.responsavel.nomeAbreviado}" />
                                            </rich:column>
                                            <rich:column>
                                                <f:facet name="header">
                                                    <h:outputText style="font-weight: bold" value="Situação" />
                                                </f:facet>
                                                <h:outputText style="font-weight: bold; #{controleTaxaPersonal.situacao eq 'Inativo' ? 'color: red;' : ''}" styleClass="blue" value="#{controleTaxaPersonal.situacao}" />
                                            </rich:column>
                                            <rich:column>
                                                <f:facet name="header">
                                                    <h:outputText style="font-weight: bold"
                                                                  value="Plano" />
                                                </f:facet>
                                                <h:outputText style="font-weight: bold" styleClass="blue"
                                                              value="#{controleTaxaPersonal.plano.descricao}" />
                                            </rich:column>
                                            <rich:column>
                                                <f:facet name="header">
                                                    <h:outputText style="font-weight: bold"
                                                                  value="Data de início do plano" />
                                                </f:facet>
                                                <h:outputText style="font-weight: bold" styleClass="blue"
                                                              value="#{controleTaxaPersonal.dataInicioVigencia_apresentar}" />
                                            </rich:column>
                                            <rich:column>
                                                <f:facet name="header">
                                                    <h:outputText style="font-weight: bold"
                                                                  value="Data de término" />
                                                </f:facet>
                                                <h:outputText style="font-weight: bold; #{ controleTaxaPersonal.vigente ? 'color: forestgreen;' : ''}"
                                                              styleClass="blue"
                                                              value="#{controleTaxaPersonal.dataFimVigencia_apresentar}" />
                                            </rich:column>
                                            <rich:column>
                                                <f:facet name="header">
                                                    <h:outputText style="font-weight: bold"
                                                                  value="Parcelas em atraso" />
                                                </f:facet>
                                                <h:outputText style="font-weight: bold; #{controleTaxaPersonal.qtdeParcelasAtrasadas > 0 ? 'color: red;' : ''}"
                                                              styleClass="blue"
                                                              value="#{controleTaxaPersonal.qtdeParcelasAtrasadas}" />
                                            </rich:column>
                                            <rich:column>
                                                <f:facet name="header">
                                                    <h:outputText style="font-weight: bold"
                                                                  value="Parcelas pagas" />
                                                </f:facet>
                                                <h:outputText style="font-weight: bold"
                                                              styleClass="blue"
                                                              value="#{controleTaxaPersonal.qtdeParcelasPagas}" />
                                            </rich:column>
                                            <rich:column>
                                                <f:facet name="header">
                                                    <h:outputText style="font-weight: bold"
                                                                  value="Parcelas em aberto" />
                                                </f:facet>
                                                <h:outputText style="font-weight: bold"
                                                              styleClass="blue"
                                                              value="#{controleTaxaPersonal.qtdeParcelasEmAberto}" />
                                            </rich:column>
                                            <rich:column>
                                                <f:facet name="header">
                                                    <h:outputText style="font-weight: bold" value="Opções" />
                                                </f:facet>
                                                <a4j:commandButton rendered="#{!controleTaxaPersonal.cancelado && controleTaxaPersonal.qtdeParcelasEmAberto > 0}"
                                                                 actionListener="#{ColaboradorControle.selecionarControleTaxaPersonalParaCancelamento}"
                                                                 oncomplete="Richfaces.showModalPanel('cancelamentoPlanoPersonal');"
                                                                 reRender="cancelamentoPlanoPersonal, abaHistoricoCompras"
                                                                 value="#{msg_bt.btn_cancelar}" >

                                                    <f:attribute name="controleTaxaPersonal" value="#{controleTaxaPersonal}" />
                                                </a4j:commandButton>
                                                <a4j:commandButton rendered="#{!controleTaxaPersonal.cancelado}"
                                                                   actionListener="#{ColaboradorControle.imprimirPlanoPersonal}"
                                                                   oncomplete="#{ColaboradorControle.mensagemNotificar}#{ColaboradorControle.msgAlert}"
                                                                   reRender="planoPersonalImpressao"
                                                                   value="Imprimir" >

                                                    <f:attribute name="controleTaxaPersonalImpressao" value="#{controleTaxaPersonal}" />
                                                </a4j:commandButton>
                                            </rich:column>
                                        </rich:dataTable>
                                        <h:panelGrid columns="1" columnClasses="colunaCentralizada" width="100%">
                                            <h:panelGroup>
                                                <h:panelGrid columns="2" columnClasses="colunaCentralizada,colunaCentralizada">
                                                    <rich:datascroller for="controlesTaxaPersonal"
                                                                       maxPages="100" id="scResultadoHistoricoPlano" />
                                                    <rich:inputNumberSpinner id="numPlanoExibir" inputSize="5" styleClass="form"
                                                                             enableManualInput="true" minValue="1" maxValue="100"
                                                                             value="#{ColaboradorControle.nrPaginaPlano}">
                                                        <a4j:support event="onchange"
                                                                     focus="scResultadoHistoricoPlano"
                                                                     reRender="controlesTaxaPersonal,scResultadoHistoricoPlano" />
                                                    </rich:inputNumberSpinner>
                                                </h:panelGrid>
                                            </h:panelGroup>
                                        </h:panelGrid>
                                    </td>
                                </tr>

                                <h:panelGroup rendered="#{!ColaboradorControle.integraProtheus}">
                                    <tr>
                                        <td align="left" valign="top" style="padding-bottom: 5px;">
                                            <div style="clear: both;" class="text">
                                                <p style="margin-bottom: 6px;"><img
                                                        src="images/arrow2.gif" width="16" height="16"
                                                        style="vertical-align: middle; margin-right: 6px;">
                                                    <h:outputText style="font-weight: bold;"
                                                                  value="#{msg_aplic.prt_HistóricoCompras}" /></p>
                                            </div>
                                            <div class="sep" style="margin: 4px 0 10px 0;"><img
                                                    src="images/shim.gif"></div>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td align="left" valign="top">
                                            <rich:dataTable
                                                    id="listaHistoricoCompras" width="100%" border="0"
                                                    rows="#{ColaboradorControle.nrPaginaMovProduto}"
                                                    cellspacing="0" cellpadding="0" styleClass="textsmall"
                                                    columnClasses="centralizado, centralizado, centralizado"
                                                    value="#{ColaboradorControle.clienteVO.listaHistoricoProduto}"
                                                    var="historicoCompras">

                                                <rich:column>
                                                    <f:facet name="header">
                                                        <h:outputText style="font-weight: bold"
                                                                      value="#{msg_aplic.prt_HistoricoComprasCliente_contrato}" />
                                                    </f:facet>
                                                    <h:outputText style="font-weight: bold" styleClass="blue"
                                                                  value="#{historicoCompras.contrato.codigo}" />
                                                </rich:column>
                                                <rich:column>
                                                    <f:facet name="header">
                                                        <h:outputText style="font-weight: bold"
                                                                      value="#{msg_aplic.prt_HistoricoComprasCliente_descricao}" />
                                                    </f:facet>
                                                    <h:outputText style="font-weight: bold" styleClass="blue"
                                                                  value="#{historicoCompras.descricao}" />
                                                </rich:column>

                                                <rich:column>
                                                    <f:facet name="header">
                                                        <h:outputText style="font-weight: bold"
                                                                      value="#{msg_aplic.prt_HistoricoComprasCliente_dataLancamento}" />
                                                    </f:facet>
                                                    <h:outputText style="font-weight: bold" styleClass="blue"
                                                                  value="#{historicoCompras.dataLancamentoComHora_Apresentar}" />
                                                </rich:column>

                                                <rich:column>
                                                    <f:facet name="header">
                                                        <h:outputText style="font-weight: bold"
                                                                      value="#{msg_aplic.prt_HistoricoComprasCliente_quantidade}" />
                                                    </f:facet>
                                                    <h:outputText style="font-weight: bold" styleClass="blue"
                                                                  value="#{historicoCompras.quantidade}" />
                                                </rich:column>
                                                <rich:column>
                                                    <f:facet name="header">
                                                        <h:outputText style="font-weight: bold"
                                                                      value="#{msg_aplic.prt_HistoricoComprasCliente_unitario}" />
                                                    </f:facet>
                                                    <h:outputText style="font-weight: bold" styleClass="blue"
                                                                  value="#{historicoCompras.precoUnitario}">
                                                        <f:converter converterId="FormatadorNumerico" />
                                                    </h:outputText>
                                                </rich:column>
                                                <rich:column>

                                                    <f:facet name="header">
                                                        <h:outputText style="font-weight: bold"
                                                                      value="#{msg_aplic.prt_HistoricoComprasCliente_desconto}" />
                                                    </f:facet>
                                                    <h:outputText style="font-weight: bold" styleClass="blue"
                                                                  value="#{historicoCompras.valorDesconto}">
                                                        <f:converter converterId="FormatadorNumerico" />
                                                    </h:outputText>
                                                </rich:column>
                                                <rich:column>
                                                    <f:facet name="header">
                                                        <h:outputText style="font-weight: bold"
                                                                      value="#{msg_aplic.prt_HistoricoComprasCliente_totalFinal}" />
                                                    </f:facet>
                                                    <h:outputText style="font-weight: bold" styleClass="blue"
                                                                  value="#{historicoCompras.totalFinal}">
                                                        <f:converter converterId="FormatadorNumerico" />
                                                    </h:outputText>
                                                </rich:column>

                                                <rich:column>
                                                    <f:facet name="header">
                                                        <h:outputText style="font-weight: bold"
                                                                      value="#{msg_aplic.prt_HistoricoComprasCliente_situacao}" />
                                                    </f:facet>
                                                    <h:outputText style="font-weight: bold"
                                                                  styleClass="#{historicoCompras.mudarCorSituacaoEmAberto}"
                                                                  value="#{historicoCompras.situacao_Apresentar}" />
                                                </rich:column>
                                                <rich:column
                                                        rendered="#{ColaboradorControle.apresentarOpcaoEstornoProduto}">
                                                    <f:facet name="header">
                                                        <h:outputText style="font-weight: bold" value="Opções" />
                                                    </f:facet>
                                                    <a4j:commandButton id="estornoMovProduto"
                                                                       rendered="#{historicoCompras.aprensetarBotaoEstornoComParcela}"
                                                                       image="./imagens/botaoEstornaProduto.png"
                                                                       action="#{EstornoMovProdutoControle.novoParaColaborador}"
                                                                       value="Estornar"
                                                                       oncomplete="abrirPopup('estornoMovProdutoForm.jsp', 'Produto',  950, 600);" />
                                                    <a4j:commandButton id="excluirMovProduto"
                                                                       rendered="#{historicoCompras.aprensetarBotaoExcluirSemParcela}"
                                                                       reRender="listaHistoricoCompras"
                                                                       onclick="if(!confirm('Confirma exclusão dos dados?')){return true;}"
                                                                       image="./imagens/botaoExcluirProduto.png"
                                                                       alt="#{msg.msg_excluir_dados}"
                                                                       action="#{EstornoMovProdutoControle.excluirMovProduto}"
                                                                       value="#{msg_bt.btn_excluir}"
                                                                       oncomplete="#{EstornoMovProdutoControle.mensagemNotificar}"
                                                                       styleClass="botaoExcluir"/>
                                                </rich:column>
                                            </rich:dataTable> <h:panelGrid columns="1" columnClasses="colunaCentralizada,colunaCentralizada"
                                                                           width="100%">
                                            <h:panelGroup>
                                                <h:panelGrid columns="2" columnClasses="colunaCentralizada,colunaCentralizada">
                                                    <rich:datascroller align="center"
                                                                       for="listaHistoricoCompras" maxPages="100"
                                                                       id="scResultadoHistoricoProdutos" />
                                                    <rich:inputNumberSpinner id="numHistoricoExibir" inputSize="5" styleClass="form"
                                                                             enableManualInput="true" minValue="1" maxValue="100"
                                                                             value="#{ColaboradorControle.nrPaginaMovProduto}">
                                                        <a4j:support event="onchange"
                                                                     focus="scResultadoHistoricoProdutos"
                                                                     reRender="listaHistoricoCompras,scResultadoHistoricoProdutos" />
                                                    </rich:inputNumberSpinner>
                                                </h:panelGrid>
                                            </h:panelGroup>
                                        </h:panelGrid></td>
                                    </tr>
                                </h:panelGroup>



                                <tr>
                                    <td align="left" valign="top" style="padding-bottom: 5px;">
                                        <div style="clear: both;" class="text">
                                            <p style="margin-bottom: 6px;"><img
                                                    src="images/arrow2.gif" width="16" height="16"
                                                    style="vertical-align: middle; margin-right: 6px;"> <h:outputText style="font-weight: bold;"
                                                                                                                      value="#{msg_aplic.prt_HistoricoParcelasGeradas}" /></p>
                                        </div>
                                        <div class="sep" style="margin: 4px 0 10px 0;"><img
                                                src="images/shim.gif"></div>
                                    </td>
                                </tr>
                                <tr>
                                    <td align="left" valign="top">
                                        <rich:dataTable
                                            id="listaHistoricoParcela" width="100%" border="0"
                                            rows="#{ColaboradorControle.nrPaginaMovParcela}"
                                            cellspacing="0" cellpadding="0" styleClass="textsmall"
                                            columnClasses="centralizado, centralizado, centralizado"
                                            value="#{ColaboradorControle.clienteVO.listaParcelas}"
                                            var="historicoParcela">

                                        <rich:column>
                                            <f:facet name="header">
                                                <h:outputText style="font-weight: bold"
                                                              value="Plano personal" />
                                            </f:facet>
                                            <h:outputText style="font-weight: bold" styleClass="blue"
                                                          value="#{historicoParcela.planoPersonal}" />
                                        </rich:column>
                                        <rich:column>
                                            <f:facet name="header">
                                                <h:outputText style="font-weight: bold"
                                                              value="#{msg_aplic.prt_HistoricoParcelaCliente_codigo}" />
                                            </f:facet>
                                            <h:outputText style="font-weight: bold" styleClass="blue"
                                                          value="#{historicoParcela.contrato.codigo}" />
                                        </rich:column>
                                        <rich:column>
                                            <f:facet name="header">
                                                <h:outputText style="font-weight: bold" value="Código" />
                                            </f:facet>
                                            <h:outputText style="font-weight: bold" styleClass="blue"
                                                          value="#{historicoParcela.codigo}" />
                                        </rich:column>
                                        <rich:column>
                                            <f:facet name="header">
                                                <h:outputText style="font-weight: bold"
                                                              value="#{msg_aplic.prt_HistoricoParcelaCliente_descricao}" />
                                            </f:facet>
                                            <h:outputText style="font-weight: bold" styleClass="blue"
                                                          value="#{historicoParcela.descricao}" />
                                        </rich:column>

                                        <rich:column>
                                            <f:facet name="header">
                                                <h:outputText style="font-weight: bold"
                                                              value="#{msg_aplic.prt_HistoricoParcelaCliente_DataLancada}" />
                                            </f:facet>
                                            <h:outputText style="font-weight: bold" styleClass="blue"
                                                          value="#{historicoParcela.dataRegistro_Apresentar}">

                                            </h:outputText>
                                        </rich:column>
                                        <rich:column>
                                            <f:facet name="header">
                                                <h:outputText style="font-weight: bold"
                                                              value="#{msg_aplic.prt_HistoricoParcelaCliente_DataVencimento}" />
                                            </f:facet>
                                            <h:outputText style="font-weight: bold" styleClass="blue"
                                                          value="#{historicoParcela.dataVencimento_Apresentar}">

                                            </h:outputText>
                                        </rich:column>
                                        <rich:column>
                                            <f:facet name="header">
                                                <h:outputText style="font-weight: bold"
                                                              value="#{msg_aplic.prt_HistoricoParcelaCliente_valor}" />
                                            </f:facet>
                                            <h:outputText style="font-weight: bold" styleClass="blue"
                                                          value="#{historicoParcela.valorParcela}">
                                                <f:converter converterId="FormatadorNumerico" />
                                            </h:outputText>
                                        </rich:column>
                                        <rich:column>
                                            <f:facet name="header">
                                                <h:outputText style="font-weight: bold"
                                                              value="#{msg_aplic.prt_HistoricoParcelaCliente_situacao}" />
                                            </f:facet>
                                            <h:outputText style="font-weight: bold"
                                                          styleClass="#{historicoParcela.mudarCorSituacaoEmAberto}"
                                                          value="#{historicoParcela.situacao_Apresentar}" />
                                        </rich:column>
                                    </rich:dataTable> <h:panelGrid columns="1" columnClasses="colunaCentralizada"
                                                                   width="100%">
                                        <h:panelGroup>
                                            <h:panelGrid columns="2" columnClasses="colunaCentralizada,colunaCentralizada">
                                                <rich:datascroller for="listaHistoricoParcela"
                                                                   maxPages="100" id="scResultadoHistoricoParcela" />
                                                <rich:inputNumberSpinner id="numParcelaExibir" inputSize="5" styleClass="form"
                                                                         enableManualInput="true" minValue="1" maxValue="100"
                                                                         value="#{ColaboradorControle.nrPaginaMovParcela}">
                                                    <a4j:support event="onchange"
                                                                 focus="scResultadoHistoricoParcela"
                                                                 reRender="listaHistoricoParcela,scResultadoHistoricoParcela" />
                                                </rich:inputNumberSpinner>
                                            </h:panelGrid>

                                        </h:panelGroup>
                                    </h:panelGrid></td>
                                </tr>
                                <h:panelGroup rendered="#{!ColaboradorControle.integraProtheus}">
                                    <tr>
                                        <td align="left" valign="top" style="padding-bottom: 5px;">
                                            <div style="clear: both;" class="text">
                                                <p style="margin-bottom: 6px;"><img src="images/arrow2.gif" width="16" height="16"
                                                                                    style="vertical-align: middle; margin-right: 6px;">
                                                    <h:outputText style="font-weight: bold;" value="#{msg_aplic.prt_HistoricoPagamentosEfetuados}" />
                                                </p>
                                            </div>
                                            <div class="sep" style="margin: 4px 0 10px 0;"><img
                                                    src="images/shim.gif"></div>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td align="left" valign="top"><rich:dataTable
                                                id="listaHistoricoPagamentos" width="100%" border="0"
                                                rows="#{ColaboradorControle.nrPaginaMovPagamento}"
                                                cellspacing="0" cellpadding="0" styleClass="textsmall"
                                                columnClasses="centralizado, centralizado, centralizado"
                                                value="#{ColaboradorControle.clienteVO.listaHistoricoPagamento}"
                                                var="historicoPagamentos">
                                            <rich:column>
                                                <f:facet name="header">
                                                    <h:outputText style="font-weight: bold"
                                                                  value="#{msg_aplic.prt_HistoricoComprasCliente_nrRecibo}" />
                                                </f:facet>
                                                <h:outputText style="font-weight: bold" styleClass="blue"
                                                              value="#{historicoPagamentos.reciboPagamento.codigo}" />
                                            </rich:column>
                                            <rich:column>
                                                <f:facet name="header">
                                                    <h:outputText style="font-weight: bold"
                                                                  value="#{msg_aplic.prt_HistoricoComprasCliente_nomePagador}" />
                                                </f:facet>
                                                <h:outputText style="font-weight: bold" styleClass="blue"
                                                              value="#{historicoPagamentos.nomePagador}" />
                                            </rich:column>
                                            <rich:column>
                                                <f:facet name="header">
                                                    <h:outputText style="font-weight: bold"
                                                                  value="#{msg_aplic.prt_HistoricoComprasCliente_dataLancamento}" />
                                                </f:facet>
                                                <h:outputText style="font-weight: bold" styleClass="blue"
                                                              value="#{historicoPagamentos.dataLancamento_Apresentar}">
                                                </h:outputText>
                                            </rich:column>
                                            <rich:column>
                                                <f:facet name="header">
                                                    <h:outputText style="font-weight: bold"
                                                                  value="#{msg_aplic.prt_HistoricoComprasCliente_formaPagamento}" />
                                                </f:facet>
                                                <h:outputText style="font-weight: bold" styleClass="blue"
                                                              value="#{historicoPagamentos.formaPagamento.tipoFormaPagamento_Apresentar}" />
                                            </rich:column>
                                            <rich:column>
                                                <f:facet name="header">
                                                    <h:outputText style="font-weight: bold"
                                                                  value="#{msg_aplic.prt_HistoricoComprasCliente_valor}" />
                                                </f:facet>
                                                <h:outputText style="font-weight: bold" styleClass="blue"
                                                              value="#{historicoPagamentos.valor}">
                                                    <f:converter converterId="FormatadorNumerico" />
                                                </h:outputText>
                                            </rich:column>
                                            <rich:column>
                                                <f:facet name="header">
                                                    <h:outputText style="font-weight: bold"
                                                                  value="#{msg_aplic.prt_HistoricoComprasCliente_recibo}" />
                                                </f:facet>
                                                <a4j:commandLink id="imprimir"
                                                                 title="#{msg_bt.btn_ImprimirReciboPagamento}"
                                                                 actionListener="#{ReciboControle.prepareRecibo}"
                                                                 action="#{ReciboControle.imprimirReciboPDF}"
                                                                 oncomplete="abrirPopupPDFImpressao('relatorio/#{ReciboControle.nomeArquivoRelatorioGeradoAgora}','', 780, 595);">
                                                    <f:attribute name="reciboPagamentoVO" value="#{historicoPagamentos.reciboPagamento}" />
                                                    <i class="fa-icon-print" style="font-size: 20px"></i>
                                                </a4j:commandLink>
                                                &nbsp;
                                                <a4j:commandLink id="enviarReciboC"
                                                                 reRender="panelMensagemErro, panelConfirmacao, modalEnviarContratoEmail"
                                                                 title="Enviar"
                                                                 oncomplete="#{ReciboControle.mensagemNotificar}#{ReciboControle.msgAlert}"
                                                                 actionListener="#{ReciboControle.prepararModalEnvioReciboPorEmailColaborador}">
                                                    <f:attribute name="reciboPagamentoVO" value="#{historicoPagamentos.reciboPagamento}"/>
                                                    <f:attribute name="pessoaVO" value="#{ColaboradorControle.colaboradorVO.pessoa}"/>
                                                    <i class="fa-icon-envelope" style="font-size: 20px"></i>
                                                </a4j:commandLink>
                                                <a4j:commandLink id="gerarNFCeColaborador"
                                                                 title="NFCe"
                                                                 reRender="mdlMensagemGenerica"
                                                                 rendered="#{ClienteControle.clienteVO.empresa.usarNFCe && LoginControle.permissaoAcessoMenuVO.gestaoNFCe}"
                                                                 actionListener="#{ReciboControle.prepareRecibo}"
                                                                 action="#{ReciboControle.confirmarEmitirNFCe}"
                                                                 oncomplete="#{ReciboControle.modalMensagemGenerica}">
                                                    <f:attribute name="reciboPagamentoVO" value="#{historicoPagamentos.reciboPagamento}"/>
                                                    <f:attribute name="origemEmissaoNFCe" value="COLABORADOR"/>
                                                    <h:graphicImage value="images/icon-NFCe.svg" style="width: 14px; padding-left: 5px;"/>
                                                </a4j:commandLink>

                                                <a4j:commandLink id="gerarNFSeColaborador"
                                                                 title="NFSe"
                                                                 reRender="mdlMensagemGenerica"
                                                                 rendered="#{ClienteControle.clienteVO.empresa.usarNFSe && LoginControle.permissaoAcessoMenuVO.gestaoNotas}"
                                                                 actionListener="#{ReciboControle.prepareRecibo}"
                                                                 action="#{ReciboControle.confirmarEmitirNFSe}"
                                                                 oncomplete="#{ReciboControle.modalMensagemGenerica}">
                                                    <f:attribute name="reciboPagamentoVO" value="#{historicoPagamentos.reciboPagamento}"/>
                                                    <f:attribute name="origemEmissaoNFSe" value="COLABORADOR"/>
                                                    <h:graphicImage value="images/icon-NFSe.svg" style="width: 14px; padding-left: 5px;"/>
                                                </a4j:commandLink>
                                            </rich:column>

                                        </rich:dataTable> <h:panelGrid columns="1" columnClasses="colunaCentralizada"
                                                                       width="100%">
                                            <h:panelGroup>
                                                <h:panelGrid columns="2" columnClasses="colunaCentralizada,colunaCentralizada">
                                                    <rich:datascroller align="center"
                                                                       for="listaHistoricoPagamentos" maxPages="10"
                                                                       id="scResultadoHistoricoPgto" />
                                                    <rich:inputNumberSpinner id="numReciboExibir" inputSize="5" styleClass="form"
                                                                             enableManualInput="true" minValue="1" maxValue="100"
                                                                             value="#{ColaboradorControle.nrPaginaMovPagamento}">
                                                        <a4j:support event="onchange"
                                                                     focus="scResultadoHistoricoPgto"
                                                                     reRender="listaHistoricoPagamentos,scResultadoHistoricoPgto" />
                                                    </rich:inputNumberSpinner>
                                                </h:panelGrid>

                                            </h:panelGroup>
                                        </h:panelGrid>
                                        </td>
                                    </tr>
                                </h:panelGroup>

                            </table>
                        </div>
                    </rich:tab>
                    <%-- Últimos acessos no sistema--%>
                    <rich:tab id="abaUltimosAcessosSistema" label="Últimos Acessos no Sistema" action="#{ColaboradorControle.montarListaUltimosAcessosSistema}">
                        <f:attribute name="codigoColaborador"
                                     value="#{ColaboradorControle.colaboradorVO.codigo}" />
                        <div style="clear: both;">

                            <table width="98%" border="0" align="left" cellpadding="0"
                                   cellspacing="0" bgcolor="#e6e6e6"
                                   style="margin-right: 30px; margin-bottom: 30px; padding: 10px;">
                                <tr>
                                    <td align="left" valign="top" style="padding-bottom: 5px;">
                                        <div style="clear: both;" class="text">
                                            <p style="margin-bottom: 6px;"><img
                                                    src="images/arrow2.gif" width="16" height="16"
                                                    style="vertical-align: middle; margin-right: 6px;">
                                                <h:outputText style="font-weight: bold;"
                                                              value="#{msg_aplic.prt_UltimosAcessosSistema}" /></p>
                                        </div>
                                        <div class="sep" style="margin: 4px 0 10px 0;"><img
                                                src="images/shim.gif"></div>
                                    </td>
                                </tr>
                                <tr>
                                    <td align="left" valign="top">
                                        <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_UltimosAcessosSistema_periodoPesquisa}" />
                                        <h:panelGroup>
                                            <h:panelGroup>
                                                <rich:calendar id="dataInicio"
                                                               value="#{ColaboradorControle.dataInicio}"
                                                               inputSize="10"
                                                               inputClass="form"
                                                               oninputblur="blurinput(this);"
                                                               oninputfocus="focusinput(this);"
                                                               oninputchange="return validar_Data(this.id);"
                                                               oninputkeypress="return mascara(this.form, this.id, '99/99/9999', event);"
                                                               datePattern="dd/MM/yyyy"
                                                               enableManualInput="true"
                                                               zindex="2"
                                                               showWeeksBar="false" />
                                                <h:message for="dataInicio"  styleClass="mensagemDetalhada"/>
                                            </h:panelGroup>
                                            <h:outputText  styleClass="tituloCampos" style="position:relative; top:0px; left:10px;" value="#{msg_aplic.prt_UltimosAcessosSistema_ate}" />
                                            <rich:spacer width="12px"/>
                                            <h:panelGroup>
                                                <rich:calendar id="dataTermino"
                                                               value="#{ColaboradorControle.dataTermino}"
                                                               inputSize="10"
                                                               inputClass="form"
                                                               oninputblur="blurinput(this);"
                                                               oninputfocus="focusinput(this);"
                                                               oninputchange="return validar_Data(this.id);"
                                                               oninputkeypress="return mascara(this.form, this.id, '99/99/9999', event);"
                                                               datePattern="dd/MM/yyyy"
                                                               enableManualInput="true"
                                                               zindex="2"
                                                               showWeeksBar="true" />
                                                <h:message for="dataTermino"  styleClass="mensagemDetalhada"/>
                                            </h:panelGroup>
                                        </h:panelGroup>
                                        <rich:spacer width="15px"/>
                                        <h:selectOneMenu  id="tipoComprador" onblur="blurinput(this);"  onfocus="focusinput(this);"
                                                          styleClass="form" value="#{ColaboradorControle.acao}" >
                                            <f:selectItems value="#{ColaboradorControle.listaSelectItemAcao}" />
                                        </h:selectOneMenu>
                                        <rich:spacer width="15px"/>

                                        <c:if test="${modulo eq 'zillyonWeb'}">
                                            <h:panelGroup id="imprimir">
                                                <a4j:commandLink styleClass="botoes nvoBt"
                                                                 id="consultarUltimosAcessos"
                                                                 style="margin-left: 0px"
                                                                 reRender="listaUltimosAcessosSistema"
                                                                 action="#{ColaboradorControle.montarListaUltimosAcessosSistema}"
                                                                 oncomplete="#{ColaboradorControle.mensagemNotificar}#{ColaboradorControle.msgAlert}">
                                                    Buscar&nbsp<i class="fa-icon-search"></i>
                                                </a4j:commandLink>
                                            </h:panelGroup>

                                            <a4j:commandLink style="margin-left:5px;margin-top: 8px;"
                                                             value="Imprimir"
                                                             action="#{ColaboradorControle.imprimirListaUltimosAcessosSistema}"
                                                             oncomplete="#{ColaboradorControle.mensagemNotificar}#{ColaboradorControle.msgAlert}"
                                                             styleClass="botoes nvoBt btSec">
                                                <i class="fa-icon-print"></i>
                                            </a4j:commandLink>

                                            <a4j:commandLink style="margin-left:5px;margin-top: 8px;"
                                                             value="Imprimir Catraca"
                                                             action="#{ColaboradorControle.imprimirListaUltimosAcessosCatraca}"
                                                             oncomplete="#{ColaboradorControle.mensagemNotificar}#{ColaboradorControle.msgAlert}"
                                                             styleClass="botoes nvoBt btSec">
                                                <i class="fa-icon-print"></i>
                                            </a4j:commandLink>
                                        </c:if>
                                        <c:if test="${modulo eq 'centralEventos'}">
                                            <h:commandButton id="consultarUltimosAcessos" action="#{ColaboradorControle.montarListaUltimosAcessosSistema}"
                                                             value="Buscar" accesskey="1" styleClass="botoes"
                                                             image="./imagens/botoesCE/buscar.png"/>
                                        </c:if>
                                        <br>
                                        <br>
                                        <rich:dataTable
                                                id="listaUltimosAcessosSistema" width="100%" border="0"
                                                rows="#{ColaboradorControle.nrPaginaUltimosAcessosSistema}"
                                                cellspacing="0" cellpadding="0" styleClass="textsmall"
                                                columnClasses="centralizado, centralizado, centralizado"
                                                value="#{ColaboradorControle.listaUltimosAcessosSistema}"
                                                var="ultimosAcessosSistema">
                                            <rich:column>
                                                <f:facet name="header">
                                                    <h:outputText style="font-weight: bold"
                                                                  value="#{msg_aplic.prt_UltimosAcessosSistema_codigo}" />
                                                </f:facet>
                                                <h:outputText style="font-weight: bold" styleClass="blue"
                                                              value="#{ultimosAcessosSistema.codigo}" />
                                            </rich:column>
                                            <rich:column>
                                                <f:facet name="header">
                                                    <h:outputText style="font-weight: bold"
                                                                  value="#{msg_aplic.prt_UltimosAcessosSistema_empresa}" />
                                                </f:facet>
                                                <h:outputText style="font-weight: bold" styleClass="blue"
                                                              value="#{ultimosAcessosSistema.empresa.nome}" />
                                            </rich:column>
                                            <rich:column>
                                                <f:facet name="header">
                                                    <h:outputText style="font-weight: bold"
                                                                  value="#{msg_aplic.prt_UltimosAcessosSistema_dataRegistro}" />
                                                </f:facet>
                                                <h:outputText style="font-weight: bold" styleClass="blue"
                                                              value="#{ultimosAcessosSistema.dataRegistro_Apresentar}" />
                                            </rich:column>
                                        </rich:dataTable>

                                        <h:panelGrid columns="1" columnClasses="colunaCentralizada,colunaCentralizada"
                                                     width="100%">
                                            <h:panelGroup>
                                                <h:panelGrid columns="2" columnClasses="colunaCentralizada,colunaCentralizada">
                                                    <rich:datascroller align="center"
                                                                       for="listaUltimosAcessosSistema" maxPages="100"
                                                                       id="scResultadoListaUltimosAcessos" />
                                                    <rich:inputNumberSpinner id="numAcessoSisExibir" inputSize="5" styleClass="form"
                                                                             enableManualInput="true" minValue="1" maxValue="100"
                                                                             value="#{ColaboradorControle.nrPaginaUltimosAcessosSistema}">
                                                        <a4j:support event="onchange"
                                                                     focus="scResultadoListaUltimosAcessos"
                                                                     reRender="listaUltimosAcessosSistema,scResultadoListaUltimosAcessos" />
                                                    </rich:inputNumberSpinner>
                                                </h:panelGrid>
                                            </h:panelGroup>
                                        </h:panelGrid>
                                        <h:panelGrid id="panelMensagemErro1" columns="3" width="100%" styleClass="tabMensagens">
                                            <h:panelGrid columns="1" width="100%">

                                                <h:outputText value=" "/>

                                            </h:panelGrid>
                                            <h:panelGrid columns="1" width="100%">
                                                <h:outputText styleClass="mensagem"  value="#{ColaboradorControle.mensagemUltimosAcessosSistema}"/>
                                            </h:panelGrid>
                                        </h:panelGrid>
                                    </td>
                                </tr>
                            </table>
                        </div>
                    </rich:tab>
                    <%-- Indisponibilidade --%>
                    <rich:tab label="Indisponibilidade"
                              rendered="#{LoginControle.apresentarLinkEstudio || LoginControle.apresentarLinkCRM}">
                        <h:panelGrid  styleClass="paginaFontResponsiva" columns="1" width="100%" rendered="#{LoginControle.apresentarLinkEstudio || LoginControle.apresentarLinkCRM}" >

                            <h:outputText   value="As configurações de indisponibilidades foram migradas para a aba ao lado, com o nome de 'RH' " />


                        </h:panelGrid>
                    </rich:tab>

                    <%-- Tab RH --%>
                    <rich:tab label="RH">

                        <h:panelGrid id="panelInfoRHBasica" columns="1" width="100%" headerClass="subordinado"
                                     columnClasses="colunaCentralizada">

                            <f:facet name="header">
                                <h:outputText value="Informações básica"/>
                            </f:facet>

                            <h:panelGrid columns="2" rowClasses="linhaImpar, linhaPar"
                                         columnClasses="classEsquerda, classDireita" width="100%">

                                <h:outputText value="Tamanho uniforme(Camisa):"/>
                                <h:panelGroup>
                                    <h:selectOneMenu onblur="blurinput(this);" onfocus="focusinput(this);"
                                                     styleClass="form"
                                                     value="#{ColaboradorControle.colaboradorVO.colaboradorInfoRhVO.tamanhoUniformeCamisa}">
                                        <f:selectItems value="#{ColaboradorControle.listaTamanhoCamisa}"/>
                                    </h:selectOneMenu>
                                </h:panelGroup>

                                <h:outputText value="Tamanho uniforme(Calça):"/>
                                <h:panelGroup>
                                    <h:selectOneMenu onblur="blurinput(this);" onfocus="focusinput(this);"
                                                     styleClass="form"
                                                     value="#{ColaboradorControle.colaboradorVO.colaboradorInfoRhVO.tamanhoUniformeCalca}">
                                        <f:selectItems value="#{ColaboradorControle.listaTamanhoCalca}"/>
                                    </h:selectOneMenu>
                                </h:panelGroup>

                                <h:outputText value="Valor salário:"/>
                                <h:inputText size="10" maxlength="10" onblur="blurinput(this);"
                                             onkeypress="return formatar_moeda(this,'.',',',event);"
                                             onfocus="focusinput(this);"
                                             styleClass="form"
                                             value="#{ColaboradorControle.colaboradorVO.colaboradorInfoRhVO.valorSalario}">
                                    <f:converter converterId="FormatadorNumerico"/>
                                </h:inputText>

                                <h:outputText value="Observações:"/>
                                <h:inputTextarea
                                        value="#{ColaboradorControle.colaboradorVO.colaboradorInfoRhVO.observacao}"
                                        cols="80" rows="4"/>


                            </h:panelGrid>
                        </h:panelGrid>

                        <h:panelGrid id="panelDocumentosRH" columns="1" width="100%"
                                     style="margin-top: 20px; background-color: gainsboro" headerClass="subordinado"
                                     columnClasses="colunaCentralizada">
                            <f:facet name="header">
                                <h:outputText value="Anexar documentos"/>
                            </f:facet>

                            <h:panelGrid columns="2"
                                         rendered="#{ColaboradorControle.colaboradorDocumentoRhVO != null}"
                                         rowClasses="linhaImpar, linhaPar"
                                         columnClasses="classEsquerda, classDireita" width="100%">
                                <h:outputText value="*Descrição do documento:"></h:outputText>
                                <h:inputText id="idDescricaoDocumento" size="50" maxlength="50"
                                             onblur="blurinput(this);" onfocus="focusinput(this);" styleClass="form"
                                             value="#{ColaboradorControle.colaboradorDocumentoRhVO.descricaoDocumento}"/>
                                <h:outputText value="Anexar arquivo"/>
                                <rich:fileUpload id="uploadDocumentoRH"
                                                 listHeight="60"
                                                 listWidth="565"
                                                 noDuplicate="false"
                                                 fileUploadListener="#{ColaboradorControle.uploadDocumentoRh}"
                                                 maxFilesQuantity="1"
                                                 allowFlash="false"
                                                 immediateUpload="false"
                                                 acceptedTypes="jpg, jpeg, gif, png, bmp, pdf, JPG, JPEG, GIF, PNG, BMP, PDF,ZIP"
                                                 addControlLabel="Adicionar"
                                                 cancelEntryControlLabel="Cancelar"
                                                 doneLabel="Pronto"
                                                 sizeErrorLabel="Arquivo não Enviado. Excedeu o tamanho permitido."
                                                 progressLabel="Enviando"
                                                 clearControlLabel="Limpar"
                                                 clearAllControlLabel="Limpar todos"
                                                 stopControlLabel="Parar"
                                                 uploadControlLabel="Enviar"
                                                 transferErrorLabel="Falha de Transmissão"
                                                 stopEntryControlLabel="Parar">
                                    <a4j:support event="onadd"
                                                 oncomplete="#{ColaboradorControle.colaboradorDocumentoRhVO.onCompleteAnexoDocumentoRh}"/>
                                    <a4j:support event="onerror"
                                                 oncomplete="#{ColaboradorControle.colaboradorDocumentoRhVO.onCompleteAnexoDocumentoRh}"/>
                                    <a4j:support event="onupload"
                                                 oncomplete="#{ColaboradorControle.colaboradorDocumentoRhVO.onCompleteAnexoDocumentoRh}"/>
                                    <a4j:support event="onuploadcomplete"
                                                 oncomplete="#{ColaboradorControle.colaboradorDocumentoRhVO.onCompleteAnexoDocumentoRh}"/>
                                    <a4j:support event="onclear" reRender="panelAnexoCliente"
                                                 action="#{ColaboradorControle.limparAnexoDocumentoRh}"/>
                                </rich:fileUpload>

                                <h:outputText value="    "/>
                                <a4j:commandLink reRender="panelDocumentosRH"
                                                 styleClass="botaoPrimario texto-size-12-real"
                                                 accesskey="2"
                                                 action="#{ColaboradorControle.gravarAnexoDocumentoRh}"
                                                 oncomplete="#{ColaboradorControle.mensagemNotificar}">
                                    Gravar arquivo
                                </a4j:commandLink>
                            </h:panelGrid>

                            <h:panelGrid columns="1" width="100%" style="margin-top: 10px;" headerClass="subordinado"
                                         columnClasses="colunaCentralizada">
                                <a4j:commandLink reRender="panelDocumentosRH"
                                                 rendered="#{ColaboradorControle.colaboradorDocumentoRhVO == null}"
                                                 styleClass="botaoPrimario texto-size-12-real"
                                                 accesskey="2"
                                                 action="#{ColaboradorControle.anexarNovoDocumentoRh}"
                                                 focus="form:idDescricaoDocumento"
                                                 oncomplete="#{ColaboradorControle.mensagemNotificar}">
                                    Anexar novo documento
                                </a4j:commandLink>
                            </h:panelGrid>
                            <h:panelGrid columns="1" width="100%" style="margin-top: 20px;"
                                         styleClass="tabFormSubordinada">

                                <rich:dataTable id="tableDocumentosRh" width="100%" headerClass="subordinado"
                                                rowClasses="linhaImpar, linhaPar"
                                                columnClasses="esquerda,esquerda,esquerda,centralizado, centralizado"
                                                value="#{ColaboradorControle.colaboradorVO.listaDocumentoRh}"
                                                var="objDocumento">
                                    <f:facet name="footer">
                                        <h:outputText value="Nenhum documento adicionado" styleClass="mensagem"
                                                      rendered="#{empty ColaboradorControle.colaboradorVO.listaDocumentoRh}"/>
                                    </f:facet>
                                    <rich:column headerClass="esquerda">
                                        <f:facet name="header">
                                            <h:outputText value="Descrição documento"/>
                                        </f:facet>
                                        <h:outputText value="#{objDocumento.descricaoDocumento}"/>
                                    </rich:column>

                                    <rich:column headerClass="esquerda">
                                        <f:facet name="header">
                                            <h:outputText value="Nome arquivo"/>
                                        </f:facet>
                                        <h:outputText value="#{objDocumento.nomeAnexo}"/>
                                    </rich:column>
                                    <rich:column headerClass="esquerda">
                                        <f:facet name="header">
                                            <h:outputText value="Usuário"/>
                                        </f:facet>
                                        <h:outputText value="#{objDocumento.usuarioVO.nome}"/>
                                    </rich:column>

                                    <rich:column headerClass="centralizado">
                                        <f:facet name="header">
                                            <h:outputText value="Data lancamento"/>
                                        </f:facet>
                                        <h:outputText value="#{objDocumento.dataLancamento_apresentar}"/>
                                    </rich:column>
                                    <rich:column headerClass="centralizado">
                                        <f:facet name="header">
                                            <h:outputText value="#{msg_bt.btn_opcoes}"/>
                                        </f:facet>
                                        <h:panelGroup rendered="#{objDocumento.codigo != null}">
                                            <h:outputLink styleClass="linkAzul icon tooltipster"
                                                          style="font-size: 14px;" title="Visualizar Anexo"
                                                          target="_blank"
                                                          value="#{objDocumento.urlCompletaAnexo}">
                                                <i class="fa-icon-download-alt"/>
                                            </h:outputLink>

                                            <a4j:commandLink reRender="mdlMensagemGenerica"
                                                             style="margin-left: 10px;font-size: 14px;"
                                                             styleClass="linkAzul icon tooltipster"
                                                             title="Excluir Anexo"
                                                             action="#{ColaboradorControle.confirmaExcluirAnexoDocumentoRh(objDocumento)}"
                                                             oncomplete="#{ColaboradorControle.msgAlert}">

                                                <i class="fa-icon-trash"/>
                                            </a4j:commandLink>
                                        </h:panelGroup>
                                    </rich:column>
                                </rich:dataTable>
                            </h:panelGrid>
                        </h:panelGrid>


                        <h:panelGrid rendered="#{LoginControle.apresentarLinkCRM}" id="panelColaboradorIndisponivelCrm"
                                     columns="1" width="100%"
                                     style="margin-top: 30px"
                                     headerClass="subordinado" columnClasses="colunaCentralizada">

                            <f:facet name="header">
                                <h:outputText value="Indisponibilidade CRM"/>
                            </f:facet>

                            <h:panelGrid columns="2" columnClasses="classEsquerda, classDireita" width="100%">

                                <h:outputLabel value="Data Início" styleClass="texto_disponibilidade"/>
                                <h:panelGroup>
                                    <rich:calendar
                                            id="dtInicialColaborador"
                                            locale="pt_BR"
                                            inputSize="10"
                                            inputClass="form"
                                            oninputblur="blurinput(this);"
                                            oninputfocus="focusinput(this);"
                                            oninputchange="return validar_Data(this.id);"
                                            oninputkeypress="return mascara(this, '99/99/9999', event); "
                                            oninputkeydown="return tabOnEnter(event, 'hInicial');"
                                            datePattern="dd/MM/yyyy"
                                            enableManualInput="true"
                                            zindex="99"
                                            showWeeksBar="false"
                                            value="#{ColaboradorControle.colaboradorIndisponivelCrmVO.dtInicio}"
                                            popup="true" styleClass="texto_disponibilidade">
                                    </rich:calendar>

                                    <rich:spacer width="5px"/>
                                    <h:outputLabel value="Data Fim" style="color: #000000;
                                                    font: 10pt 'Trebuchet MS',verdana,arial,helvetica,sans-serif;
                                                    text-align: right; "/>
                                    <rich:calendar
                                            id="dtFinalColaborador"
                                            locale="pt_BR"
                                            inputSize="10"
                                            inputClass="form"
                                            oninputblur="blurinput(this);"
                                            oninputfocus="focusinput(this);"
                                            oninputchange="return validar_Data(this.id);"
                                            oninputkeypress="return mascara(this, '99/99/9999', event); "
                                            oninputkeydown="return tabOnEnter(event, 'hInicial');"
                                            datePattern="dd/MM/yyyy"
                                            enableManualInput="true"
                                            zindex="99"
                                            showWeeksBar="false"
                                            value="#{ColaboradorControle.colaboradorIndisponivelCrmVO.dtFim}"
                                            popup="true" styleClass="texto_disponibilidade">
                                    </rich:calendar>
                                </h:panelGroup>

                                <h:outputText styleClass="texto_disponibilidade" value="Motivo:"/>
                                <h:inputText onkeydown="return tabOnEnter(event, 'add');"
                                             value="#{ColaboradorControle.colaboradorIndisponivelCrmVO.motivo}"
                                             size="59"
                                             maxlength="60"/>


                                <h:outputText value="Colaborador Substituto"/>
                                <h:panelGroup>
                                    <h:inputText id="colaboradorSuplente" size="59" maxlength="60"
                                                 onblur="blurinput(this);"
                                                 onfocus="focusinput(this);"
                                                 styleClass="form"
                                                 value="#{ColaboradorControle.colaboradorIndisponivelCrmVO.colaboradorSuplenteVO.pessoa.nome}"/>
                                    <rich:suggestionbox height="200" width="200"
                                                        for="colaboradorSuplente"
                                                        fetchValue="#{colaborador.pessoa.nome}"
                                                        suggestionAction="#{ColaboradorControle.consultarColaboradorSuplente}"
                                                        minChars="1" rowClasses="20"
                                                        status="statusHora"
                                                        nothingLabel="Nenhum Colaborador encontrado !"
                                                        var="colaborador" id="suggestionNomeColaborador">
                                        <a4j:support event="onselect"
                                                     action="#{ColaboradorControle.selecionarColaboradorSuplenteSuggestionBox}"/>
                                        <h:column>
                                            <h:outputText value="#{colaborador.pessoa.nome}"/>
                                        </h:column>
                                    </rich:suggestionbox>
                                    <%--<a4j:commandButton id="consultaDadosColaborador"--%>
                                    <%--alt="Consultar Colaborador"--%>
                                    <%--action="#{ColaboradorControle.inicializarConsultaColaborador}"--%>
                                    <%--reRender="formColaborador"--%>
                                    <%--oncomplete="Richfaces.showModalPanel('panelColaborador')"--%>
                                    <%--image="./imagens/informacao.gif"/>--%>
                                </h:panelGroup>

                            </h:panelGrid>
                            <h:panelGroup layout="block" styleClass="container-botoes">
                                <a4j:commandLink
                                        value="Adicionar"
                                        styleClass="botaoSecundario texto-size-14-real"
                                        reRender="form"
                                        action="#{ColaboradorControle.prepararAdicionarColaboradorIndisponivelCrm}"
                                        title="Adicionar"/>
                            </h:panelGroup>
                            <h:panelGrid columns="1" width="100%" styleClass="tabFormSubordinada">
                                <h:dataTable
                                        rendered="#{!empty ColaboradorControle.colaboradorVO.listaColaboradorIndisponivelCrmVOS}"
                                        var="colaboradorIndisponivel"
                                        value="#{ColaboradorControle.colaboradorVO.listaColaboradorIndisponivelCrmVOS}"
                                        width="100%" headerClass="subordinado" styleClass="tabFormSubordinada"
                                        rowClasses="linhaImpar, linhaPar" columnClasses="colunaAlinhamento">

                                    <h:column>
                                        <f:facet name="header">
                                            <h:outputText value="Data Início"/>
                                        </f:facet>
                                        <h:outputText converter="dataConverter"
                                                      value="#{colaboradorIndisponivel.dtInicio}"/>
                                    </h:column>
                                    <h:column>
                                        <f:facet name="header">
                                            <h:outputText value="Data Fim"/>
                                        </f:facet>
                                        <h:outputText converter="dataConverter"
                                                      value="#{colaboradorIndisponivel.dtFim}"/>
                                    </h:column>
                                    <h:column>
                                        <f:facet name="header">
                                            <h:outputText value="Motivo"/>
                                        </f:facet>
                                        <h:outputText value="#{colaboradorIndisponivel.motivo}"/>
                                    </h:column>

                                    <h:column>
                                        <f:facet name="header">
                                            <h:outputText value="Colaborador Substituto"/>
                                        </f:facet>
                                        <h:outputText
                                                value="#{colaboradorIndisponivel.colaboradorSuplenteVO.pessoa.nome}"/>
                                    </h:column>


                                    <h:column>
                                        <f:facet name="header">
                                            <h:outputText value="Opções"/>
                                        </f:facet>
                                        <a4j:commandButton id="removerProduto"
                                                           action="#{ColaboradorControle.prepararRemoverColaboradorIndisponivel}"
                                                           reRender="form"
                                                           focus="dtInicialColaborador"
                                                           value="#{msg_bt.btn_excluir}"
                                                           image="./imagens/botaoRemover.png"/>
                                        <a4j:commandButton id="editarProduto"
                                                           action="#{ColaboradorControle.editarColaboradorIndisponibilidade}"
                                                           reRender="form"
                                                           focus="dtInicialColaborador"
                                                           value="#{msg_bt.btn_editar}"
                                                           image="./imagens/botaoEditar.png"/>
                                    </h:column>
                                </h:dataTable>
                            </h:panelGrid>
                        </h:panelGrid>

                        <h:panelGrid id="panelIndisponibilidade" styleClass="paginaFontResponsiva" columns="2" width="100%" rendered="#{LoginControle.apresentarLinkEstudio}" >
                            <%@include file="pages/estudio/indisponibilidadeColaborador.jsp" %>
                        </h:panelGrid>

                    </rich:tab>


                    <rich:tab label="Histórico Cobrança"
                              action="#{ColaboradorControle.preencherItensCobranca}"
                              oncomplete="carregarTooltipsterColaborador()" id="abaHistoricoCobranca">
                        <div style="clear: both;">
                            <table width="98%" border="0" align="left" cellpadding="0" cellspacing="0" bgcolor="#e6e6e6"
                                   style="margin-right: 30px; margin-bottom: 30px; padding: 10px;">
                                <tr>
                                    <td align="left" valign="top" style="padding-bottom: 5px;">
                                        <div style="clear: both;" class="text">
                                            <p style="margin-bottom: 6px;">
                                                <img src="images/arrow2.gif" width="16" height="16"
                                                     style="vertical-align: middle; margin-right: 6px;">
                                                <h:outputText style="font-weight: bold;" value="#{msg_aplic.prt_HistoricoCobranca}"/>
                                            </p>
                                        </div>
                                        <div class="sep" style="margin: 4px 0 10px 0;">
                                            <img src="images/shim.gif">
                                        </div>
                                    </td>
                                </tr>
                                <tr>
                                    <td align="left" valign="top">
                                        <jsp:include page="includes/remessas/include_itens_remessaestorno.jsp" flush="true"/>
                                    </td>
                                </tr>
                            </table>
                        </div>

                        <div style="clear: both;">
                            <table width="98%" border="0" align="left" cellpadding="0" cellspacing="0"
                                   style="margin-right: 30px; margin-bottom: 30px; padding: 10px;">
                                <tr>
                                    <td align="left" valign="top">
                                        <jsp:include page="includes/transacoes/include_itens_paginado_transacoes_colaborador.jsp" flush="true"/>
                                    </td>
                                </tr>
                            </table>
                        </div>
                        <div style="clear: both;">
                            <table width="98%" border="0" align="left" cellpadding="0" cellspacing="0"
                                   style="margin-right: 30px; margin-bottom: 30px; padding: 10px;">
                                <tr>
                                    <td align="left" valign="top">
                                        <jsp:include page="includes/transacoes/include_itens_paginado_transacoes_verificacao_colaborador.jsp" flush="true"/>
                                    </td>
                                </tr>
                            </table>
                        </div>

                        <div style="clear: both;">
                            <table width="98%" border="0" align="left" cellpadding="0" cellspacing="0"
                                   style="margin-right: 30px; margin-bottom: 30px; padding: 10px;">
                                <tr>
                                    <td align="left" valign="top">
                                        <jsp:include page="includes/transacoes/include_itens_paginado_pix_colaborador.jsp" flush="true"/>
                                    </td>
                                </tr>
                            </table>
                        </div>


                    </rich:tab>

                    <rich:tab actionListener="#{ColaboradorControle.preencherAutorizacoesCobranca}"
                              id="abaCobranca" label="Cobrança" reRender="form">
                        <f:attribute name="colaborador" value="#{ColaboradorControle.colaboradorVO}"/>
                        <%@include file="includes/cliente/include_box_autorizacao_cobranca.jsp" %>
                    </rich:tab>

                    <rich:tab id="abaUsuarioMovidesk" label="Usuário Movidesk"
                              actionListener="#{MovideskControle.carregarInformacoes}"
                              rendered="#{LoginControle.usuarioLogado.administrador}">
                        <f:attribute name="colaborador" value="#{ColaboradorControle.colaboradorVO}"/>
                        <%@include file="includes/integracao/include_usuariomovidesk.jsp" %>
                    </rich:tab>

                    <c:if test="${ColaboradorControle.exibirReplicarRedeEmpresa}">
                        <rich:tab label="Replicar Empresa">

                            <h:panelGrid columns="1" width="100%" headerClass="subordinado"
                                         columnClasses="colunaCentralizada">
                                <f:facet name="header">
                                    <h:outputText value="#{msg_aplic.prt_PlanoReplicarEmpresa_tituloForm}"/>
                                </f:facet>
                                <h:panelGrid columns="3" style="border-style: solid;" id="contadorReplicaPlano"
                                             columnClasses="colunaCentralizada, colunaCentralizada, colunaCentralizada"
                                             width="100%">
                                    <h:outputText value="Unidades" styleClass="botoes nvoBt"/>
                                    <h:outputText value="Replicadas" styleClass="botoes nvoBt"/>
                                    <h:outputText value="Não Replicadas" styleClass="botoes nvoBt"/>
                                    <h:outputText value="#{ColaboradorControle.listaColaboradorRedeEmpresaSize}"
                                                  style="font-size: 20pt; font-weight: bold;"/>
                                    <h:outputText value="#{ColaboradorControle.listaColaboradorRedeEmpresaSincronizado}"
                                                  style="color: #0f4c36; font-size: 20pt; font-weight: bold;"/>
                                    <h:outputText
                                            value="#{ColaboradorControle.listaColaboradorRedeEmpresaSize - ColaboradorControle.listaColaboradorRedeEmpresaSincronizado}"
                                            style="color: #8b0000; font-size: 20pt; font-weight: bold;"/>
                                </h:panelGrid>
                                <h:panelGrid columns="1" id="contadorReplicaPlano2"
                                             columnClasses="colunaDireita"
                                             width="100%"
                                             style="margin-top: 20px; margin-bottom: 1px">
                                    <h:panelGroup layout="block">
                                        <a4j:commandButton value="Replicar Todas" styleClass="botoes nvoBt"
                                                           action="#{ColaboradorControle.replicarTodas}"
                                                           reRender="listaEmpresasReplicar, contadorReplicaPlano"
                                                           ajaxSingle="true" immediate="true"/>
                                        <a4j:commandButton value="Replicar Selecionadas" styleClass="botoes nvoBt btSec"
                                                           action="#{ColaboradorControle.replicarSelecionadas}"
                                                           reRender="listaEmpresasReplicar, contadorReplicaPlano"
                                                           ajaxSingle="true" immediate="true"/>
                                        <a4j:commandButton value="Limpar Selecionadas" styleClass="botoes nvoBt btSec"
                                                           action="#{ColaboradorControle.limparReplicar}"
                                                           reRender="listaEmpresasReplicar, contadorReplicaPlano"
                                                           ajaxSingle="true" immediate="true"/>
                                    </h:panelGroup>
                                </h:panelGrid>
                                <h:panelGrid columns="1" rowClasses="linhaImpar, linhaPar"
                                             columnClasses="colunaCentralizada" width="100%">

                                    <h:dataTable id="listaEmpresasReplicar" width="100%" headerClass="subordinado"
                                                 styleClass="tabFormSubordinada"
                                                 rowClasses="linhaImpar, linhaPar"
                                                 columnClasses="colunaEsquerda, colunaEsquerda, colunaCentralizada, colunaEsquerda"
                                                 style="text-align: center;"
                                                 value="#{ColaboradorControle.listaColaboradorRedeEmpresa}"
                                                 var="colaboradorRedeEmpresaReplicacao">
                                        <h:column>
                                            <f:facet name="header">
                                                <h:outputText value=""/>
                                            </f:facet>
                                            <h:selectBooleanCheckbox id="check" styleClass="form"
                                                                     rendered="#{!colaboradorRedeEmpresaReplicacao.dataAtualizacaoInformada}"
                                                                     value="#{colaboradorRedeEmpresaReplicacao.selecionado}">
                                                <a4j:support event="onchange" reRender="listaEmpresasReplicar"/>
                                            </h:selectBooleanCheckbox>
                                        </h:column>
                                        <h:column>
                                            <f:facet name="header">
                                                <h:outputText value="#{msg_aplic.prt_PlanoRedeEmpresa_nomeUnidade}"/>
                                            </f:facet>
                                            <h:outputText value="#{colaboradorRedeEmpresaReplicacao.nomeUnidade}"/>
                                        </h:column>
                                        <h:column>
                                            <f:facet name="header">
                                                <h:outputText value="#{msg_aplic.prt_PlanoRedeEmpresa_chave}"/>
                                            </f:facet>
                                            <h:outputText value="#{colaboradorRedeEmpresaReplicacao.chaveDestino}"/>
                                        </h:column>
                                        <h:column>
                                            <f:facet name="header">
                                                <h:outputText value=""/>
                                            </f:facet>
                                            <h:panelGroup>
                                                <a4j:commandButton id="replicarPlano"
                                                                   reRender="listaEmpresasReplicar, contadorReplicaPlano"
                                                                   ajaxSingle="true" immediate="true"
                                                                   rendered="#{!colaboradorRedeEmpresaReplicacao.dataAtualizacaoInformada}"
                                                                   action="#{ColaboradorControle.replicarColaboradorRedeEmpresaGeral}"
                                                                   value="Replicar"/>
                                                <h:graphicImage url="./images/check.png"
                                                                rendered="#{colaboradorRedeEmpresaReplicacao.dataAtualizacaoInformada}"/>
                                            </h:panelGroup>
                                        </h:column>
                                        <h:column>
                                            <f:facet name="header">
                                                <h:outputText
                                                        value="#{msg_aplic.prt_PlanoRedeEmpresa_mensagemSituacao}"/>
                                            </f:facet>
                                            <h:outputText value="#{colaboradorRedeEmpresaReplicacao.mensagemSituacao}"/>
                                        </h:column>
                                        <h:column>
                                            <f:facet name="header">
                                                <h:outputText
                                                        value="Vínculo"/>
                                            </f:facet>
                                            <h:panelGroup>
                                                <a4j:commandButton
                                                        rendered="#{colaboradorRedeEmpresaReplicacao.dataAtualizacaoInformada}"
                                                        reRender="listaEmpresasReplicar, contadorReplicaPlano"
                                                        ajaxSingle="true" immediate="true"
                                                        action="#{ColaboradorControle.retirarVinculoReplicacao}"
                                                        value="Retirar"/>
                                            </h:panelGroup>
                                        </h:column>
                                    </h:dataTable>
                                </h:panelGrid>
                            </h:panelGrid>
                        </rich:tab>
                    </c:if>
                </rich:tabPanel>
                <h:panelGrid columns="1" width="100%" styleClass="tabMensagens">
                    <h:panelGrid id="panelMensagemErro" columns="3" width="100%" styleClass="tabMensagens">
                        <h:panelGrid columns="1" width="100%">
                            <h:outputText value=" "/>
                        </h:panelGrid>
                        <h:commandButton id="imgAtencao"  rendered="#{ColaboradorControle.atencao}" image="./imagens/atencao.png"/>
                        <h:commandButton id="imgSucesso"  rendered="#{ColaboradorControle.sucesso}" image="./imagens/sucesso.png"/>
                        <h:commandButton id="imgErro" rendered="#{ColaboradorControle.erro}" image="./imagens/erro.png"/>
                        <h:panelGrid columns="1" width="100%">
                            <h:outputText id="msgColaborador" styleClass="mensagem"  value="#{ColaboradorControle.mensagem}"/>
                            <h:outputText id="msgColaboradorDet" styleClass="mensagemDetalhada" value="#{ColaboradorControle.mensagemDetalhada}" escape="false"/>
                        </h:panelGrid>
                    </h:panelGrid>
                    <h:panelGrid columns="1" width="100%" columnClasses="colunaCentralizada">

                        <c:if test="${modulo eq 'zillyonWeb'}">

                            <h:panelGroup >
                                <a4j:commandButton id="novo" rendered="#{ColaboradorControle.apresentarBotoes}" immediate="true"
                                                 action="#{ColaboradorControle.novo}" value="#{msg_bt.btn_novo}"
                                                 alt="#{msg.msg_novo_dados}" accesskey="1"  styleClass="botoes nvoBt btSec"/>

                                <h:outputText value="    "/>

                                <a4j:commandButton id="salvar" reRender="form, panelExistePessoa, panelExisteColaborador,modalVinculoCliente"
                                                   action="#{ColaboradorControle.gravar}" value="#{msg_bt.btn_gravar}" oncomplete="#{ColaboradorControle.msgAlert}"
                                                   alt="#{msg.msg_gravar_dados}"
                                                   accesskey="2" styleClass="botoes nvoBt"/>

                                <h:outputText value="    "/>

                                <h:panelGroup id="grupoBtnExcluir">
                                    <a4j:commandButton id="excluir" reRender="mdlMensagemGenerica"
                                                       oncomplete="#{ColaboradorControle.msgAlert}" action="#{ColaboradorControle.confirmarExcluir}" value="#{msg_bt.btn_excluir}"
                                                       alt="#{msg.msg_excluir_dados}" accesskey="3" styleClass="botoes nvoBt btSec" style="border: 0px" >
                                        <f:param name="metodochamar" value="excluir"/>
                                    </a4j:commandButton>
                                </h:panelGroup>

                                <h:outputText value="    "/>

                                <a4j:commandButton id="consultar" rendered="#{ColaboradorControle.apresentarBotoes}"
                                                 immediate="true" action="#{ColaboradorControle.inicializarConsultar}"
                                                 value="#{msg_bt.btn_voltar_lista}"
                                                 alt="#{msg.msg_consultar_dados}" accesskey="4" styleClass="botoes nvoBt btSec"/>
                                
                                <h:outputText value="    "/>

                                <a4j:commandButton id="cadastrarUsuario" rendered="#{ColaboradorControle.colaboradorVO.codigo != 0}"
                                                 value="Ver Usuario" accesskey="3" styleClass="botoes nvoBt btSec"
                                                 action="#{ColaboradorControle.redirecionarUsuario}" reRender="form:panelMensagemErro"
                                                 oncomplete="if (#{not empty UsuarioControle.usuarioVO}) abrirPopup('usuarioForm.jsp', 'Usuario', 820, 600);">

                                </a4j:commandButton>
                                
                                <rich:spacer width="10px"/>
                                <a4j:commandLink id="logColaborador"  rendered="#{ColaboradorControle.apresentarBotoes}"
                                                   action="#{ColaboradorControle.realizarConsultaLogObjetoSelecionado}"
                                                   reRender="form" oncomplete="abrirPopup('visualizadorLogForm.jsp', 'VisualizadorLog', 785, 595);"
                                                   title="Visualizar Log" styleClass="botoes nvoBt btSec"
                                                   style="display: inline-block; padding: 8px 15px;">
                                    <i class="fa-icon-list"></i>
                                </a4j:commandLink>
                            </h:panelGroup>

                        </c:if>

                        <c:if test="${modulo eq 'centralEventos' }">

                            <h:panelGroup >
                                <h:commandButton id="novo" rendered="#{ColaboradorControle.apresentarBotoes}" immediate="true"
                                                 action="#{ColaboradorControle.novo}" value="#{msg_bt.btn_novo}"
                                                 alt="#{msg.msg_novo_dados}" accesskey="1"
                                                 styleClass="botoes nvoBt btSec"/>

                                <h:outputText value="    "/>

                                <a4j:commandButton id="salvar" reRender="form, panelExistePessoa, panelExisteColaborador"
                                                   action="#{ColaboradorControle.gravarCE}"
                                                   value="#{msg_bt.btn_gravar}"
                                                   alt="#{msg.msg_gravar_dados}"
                                                   accesskey="2" styleClass="botoes nvoBt"
                                                   actionListener="#{ColaboradorControle.autorizacao}">
                                    <!-- Entidade.colaborador -->
                                    <f:attribute name="entidade" value="112" />
                                    <!-- operacao.gravar -->
                                    <f:attribute name="operacao" value="G" />
                                </a4j:commandButton>


                                <h:outputText value="    "/>

                                <a4j:commandButton id="excluir"
                                                 rendered="#{ColaboradorControle.apresentarBotoes}"
                                                 onclick="if(!confirm('Confirma exclusão dos dados?')){return true;}"
                                                 action="#{ColaboradorControle.excluirCE}" value="#{msg_bt.btn_excluir}"
                                                 accesskey="3" styleClass="botoes nvoBt btSec btPerigo"
                                                 actionListener="#{ColaboradorControle.autorizacao}">
                                    <!-- Entidade.colaborador -->
                                    <f:attribute name="entidade" value="112" />
                                    <!-- operacao.excluir -->
                                    <f:attribute name="operacao" value="E" />

                                </a4j:commandButton>


                                <h:outputText value="    "/>

                                <a4j:commandButton id="consultar"
                                                 rendered="#{ColaboradorControle.apresentarBotoes}"
                                                 immediate="true" action="#{ColaboradorControle.inicializarConsultar}"
                                                 value="#{msg_bt.btn_consultar}"
                                                 alt="#{msg.msg_consultar_dados}" accesskey="4" styleClass="botoes nvoBt btSec"
                                                 actionListener="#{ColaboradorControle.autorizacao}">
                                    <!-- Entidade.colaborador -->
                                    <f:attribute name="entidade" value="112" />
                                    <!-- operacao.consultarr -->
                                    <f:attribute name="operacao" value="C" />

                                </a4j:commandButton>

                                <h:outputText value="    "/>

                                <c:if test="${modulo eq 'zillyonWeb'}">
                                    <a4j:commandLink  rendered="#{ColaboradorControle.apresentarBotoes}"
                                                        action="#{ColaboradorControle.realizarConsultaLogObjetoSelecionado}"
                                                        reRender="form" oncomplete="abrirPopup('visualizadorLogForm.jsp', 'VisualizadorLog', 785, 595);"
                                                        title="Visualizar LOG"
                                                        style="display: inline-block; padding: 8px 15px;"
                                                        styleClass="botoes nvoBt btSec">
                                        <i class="fa-icon-list"></i>
                                    </a4j:commandLink>
                                </c:if>
                            </h:panelGroup>
                        </c:if>

                    </h:panelGrid>
                </h:panelGrid>
            </h:panelGrid>
        </h:form>
    </h:panelGrid>
    <jsp:include page="includes/include_carregando_ripple.jsp" flush="true"/>
    <jsp:include page="includes/cliente/include_modal_capfoto_html5.jsp" flush="true"/>
    <jsp:include page="includes/include_modal_vinculo_agenda.jsp" flush="true"/>
    <jsp:include page="includes/include_modal_mensagem_generica.jsp" flush="true"/>
    <jsp:include page="includes/include_envio_contrato_email.jsp" flush="true"/>
    <rich:modalPanel id="cancelamentoPlanoPersonal"
                     autosized="true"
                     shadowOpacity="true"
                     styleClass="novaModal"
                     width="600">
        <f:facet name="header">
            <h:outputText value="Cancelamento de plano personal"/>
        </f:facet>
        <a4j:form>
            <div style="overflow-y: auto; max-height: 640px;">

                <h:panelGroup rendered="#{fn:length(ColaboradorControle.parcelasACancelarPlanoPersonal) == 0}" >
                    <div style="text-align: center; margin-top: 10px;">
                        <span class="col-md-12 texto-size-20 texto-cor-cinza texto-font texto-bold">
                               Não existem parcelas elegíveis para cancelamento neste plano.
                        </span>
                        <span class="col-md-12 texto-size-20 texto-cor-cinza texto-font texto-bold">
                               Este plano não pode ser cancelado.
                        </span>
                    </div>
                </h:panelGroup>

                <h:panelGroup rendered="#{!empty ColaboradorControle.parcelasEmAbertoPlanoPersonal}" >
                    <div style="text-align: center; margin-top: 10px;">
                        <span class="col-md-12 texto-size-20 texto-cor-cinza texto-font texto-bold">
                               As seguintes parcelas ficarão em aberto:
                        </span>
                    </div>
                    <rich:dataTable value="#{ColaboradorControle.parcelasEmAbertoPlanoPersonal}"
                                    var="parcela"
                                    width="100%"
                                    styleClass="tabelaSimplesCustom">
                        <h:column>
                            <f:facet name="header">
                                <h:outputText value="Parcela"/>
                            </f:facet>
                            <h:outputText value="#{parcela.descricao}" />
                        </h:column>
                        <h:column>
                            <f:facet name="header">
                                <h:outputText value="Valor"/>
                            </f:facet>
                            <h:outputText value="#{parcela.valorParcela_Apresentar}" />
                        </h:column>
                    </rich:dataTable>
                </h:panelGroup>
                <h:panelGroup rendered="#{!empty ColaboradorControle.parcelasACancelarPlanoPersonal}" >
                    <div style="text-align: center; margin-top: 10px;">
                        <span class="col-md-12 texto-size-20 texto-cor-cinza texto-font texto-bold">
                               As parcelas abaixo serão canceladas:
                        </span>
                    </div>
                    <rich:dataTable value="#{ColaboradorControle.parcelasACancelarPlanoPersonal}"
                                    var="parcela"
                                    width="100%"
                                    styleClass="tabelaSimplesCustom">
                        <h:column >
                            <f:facet name="header">
                                <h:outputText value="Parcela"/>
                            </f:facet>
                            <h:outputText value="#{parcela.descricao}" />
                        </h:column >
                        <h:column>
                            <f:facet name="header">
                                <h:outputText value="Valor"/>
                            </f:facet>
                            <h:outputText value="#{parcela.valorParcela_Apresentar}" />
                        </h:column>
                    </rich:dataTable>
                </h:panelGroup>

            </div>

            <h:panelGrid columns="1"
                         width="100%"
                         style="margin-top: 20px;"
                         columnClasses="colunaCentralizada">
                <h:panelGroup layout="block" styleClass="container-botoes" >
                    <a4j:commandLink oncomplete="Richfaces.hideModalPanel('cancelamentoPlanoPersonal');#{ColaboradorControle.mensagemNotificar}"
                                     styleClass="botaoPrimario texto-size-16-real texto-cor-branco"
                                     rendered="#{fn:length(ColaboradorControle.parcelasACancelarPlanoPersonal) > 0}"
                                     reRender="form, abaHistoricoCompras"
                                     style="margin-right: 20%;"
                                     action="#{ColaboradorControle.cancelarPlanoPersonal}">
                        Confirmar <i class="fa-icon-exchange"></i>
                    </a4j:commandLink>

                    <a4j:commandLink styleClass="botaoSecundario texto-size-16-real"
                                     oncomplete="Richfaces.hideModalPanel('cancelamentoPlanoPersonal')"
                                     value="Fechar"/>
                </h:panelGroup>
                <h:outputText rendered="#{!ColaboradorControle.permiteCancelarPlanoPersonal}"
                              styleClass="mensagemDetalhada"
                              value="Usuário sem permissão para realizar o cancelamento de plano: '3.07 - Cancelar contratos'"/>

            </h:panelGrid>
        </a4j:form>
    </rich:modalPanel>

    <rich:modalPanel id="modalVinculoCliente" autosized="true" shadowOpacity="true" width="800" height="350" styleClass="novaModal">

        <f:facet name="header">
            <h:panelGroup>
                <h:outputText value="Vínculos de Clientes"/>
            </h:panelGroup>
        </f:facet>
        <f:facet name="controls">
            <h:panelGroup>
                <h:graphicImage value="/imagens/close.png" style="cursor:pointer" id="hidelinkVincCliente"/>
                <rich:componentControl for="modalVinculoCliente" attachTo="hidelinkVincCliente"
                                       operation="hide" event="onclick">
                </rich:componentControl>
            </h:panelGroup>
        </f:facet>

        <a4j:form id="formVinculClientes">

            <div style="overflow: auto;">
                <h:panelGroup id="panelBasico">
                    <%@include file="include_vinculos_colaborador.jsp" %>
                </h:panelGroup>
            </div>

            <h:panelGrid id="pgBotoes" columns="1" width="100%" columnClasses="colunaCentralizada">
                <h:panelGroup>
                    <a4j:commandButton id="excluirPerfilAcessoTransferirVinculo"  rendered="#{ColaboradorControle.listaColaboradoresTrocarVinculosSize > 1}"
                                       action="#{ColaboradorControle.transferirVinculosColaborador}" value="Transferir Vínculos"
                                       reRender="modalVinculoCliente"
                                       oncomplete="#{ColaboradorControle.msgAlert}#{ColaboradorControle.mensagemNotificar}"
                                       alt="#{msg.msg_gravar_dados}" accesskey="2"
                                       styleClass="botoes nvoBt"/>
                    <a4j:commandButton rendered="#{ColaboradorControle.tipoVinculoListaColaborador.sigla != 'CO' &&  ColaboradorControle.tipoVinculoListaColaborador.sigla != 'TW'}" id="excluirPerfilAcessoRemoverVinculo"
                                       action="#{ColaboradorControle.removerVinculoColaborador}" value="Remover Vínculos"
                                       reRender="modalVinculoCliente"
                                       oncomplete="#{ColaboradorControle.msgAlert}#{ColaboradorControle.mensagemNotificar}"
                                       alt="#{msg.msg_gravar_dados}" accesskey="2"
                                       styleClass="botoes nvoBt"/>
                </h:panelGroup>
            </h:panelGrid>


        </a4j:form>
    </rich:modalPanel>

    <jsp:include page="include_modal/include_cidade.jsp" flush="true"/>
    <jsp:include page="includes/transacoes/include_paramspanel_transacao.jsp"/>
    <jsp:include page="includes/include_modal_tipoProdutoAutorizacao.jsp" flush="true" />
    <jsp:include page="includes/transacoes/include_paramspanel_pix.jsp"/>
</f:view>


<script>
    function somenteNumeros(num) {
        var er = /[^0-9.]/;
        er.lastIndex = 0;
        var campo = num;
        if (er.test(campo.value)) {
            campo.value = "";
        }
    }

    function carregarTooltipsterColaborador() {
        carregarTooltipCol(jQuery('.tooltipster'));
    }

    function carregarTooltipCol(el) {
        el.tooltipster({
            theme: 'tooltipster-light',
            position: 'bottom',
            animation: 'grow',
            contentAsHTML: true
        });
    }

    carregarTooltipsterColaborador();
</script>
