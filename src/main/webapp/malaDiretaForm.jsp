<%@page contentType="text/html"%>
<%@page pageEncoding="ISO-8859-1"%>
<head><%@include file="/includes/include_import_minifiles.jsp" %></head>
<script type="text/javascript" language="Javascript" src="script/scriptSMS.js"></script>
<link href="./css/otimize.css" rel="stylesheet" type="text/css">

<%@taglib prefix="f" uri="http://java.sun.com/jsf/core"%>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html"%>

<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax"%>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich"%>

<f:loadBundle var="msg_bt" basename="propriedades.Botoes"/>
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao"/>
<f:loadBundle var="msg" basename="propriedades.Mensagens"/>

<style type="text/css">
    body {
        margin: 0px;
        padding: 0px;
    }
    .panelAgendamento{
        vertical-align: top;
    }
    .colDireita{
        width: 80%;
        vertical-align: top;
    }
    .colEsquerda{
        width: 20%;
        vertical-align: top;
    }
    .vintecinco{
        width: 25%;
    }
    .quinze{
        width: 15%;
    }
    .vinte{
        width: 20%;
    }
    .rich-table-cell {
        border-bottom: none;
        border-right: none;
        border-left: none;
         
    }
</style>
<script>

    function validarRemetente(){
        var validade = true;
        var remetente = document.getElementById('form:textColaboradorResponsavel').value;
	
        if ((remetente == null || remetente == "")) {
            validade = confirm('REMETENTE está vazio. Deseja enviar com o remetente padrão?');
        }
        return validade;
    }

    function mascarahora(field, sMask, evtKeyPress) {
        var i, nCount, sValue, fldLen, mskLen, bolMask, sCod, nTecla;

        var nTecla = (evtKeyPress.charCode || evtKeyPress.keyCode || evtKeyPress.which);
        if (evtKeyPress.keyCode != 0
            && ((nTecla == 8) || (nTecla == 9) || (nTecla == 18) || (nTecla == 27) || (nTecla == 33) || (nTecla == 34) || (nTecla == 35) || (nTecla == 36)
            || (nTecla == 37) || (nTecla == 38) || (nTecla == 39) || (nTecla == 40) || (nTecla == 45) || (nTecla == 46))) {
            return true;
        }
  
        sValue = field.value;
  
        // Limpa todos os caracteres de formatação que já estiverem no campo.
        sValue = sValue.toString().replace(/[_\W]/g, "" );
        fldLen = sValue.length;
        mskLen = sMask.length;
  
 
        i = 0;
        nCount = 0;
        sCod = "";
        mskLen = fldLen;

        while (i <= mskLen) {
            bolMask = sMask.charAt(i).search(/[_\W]/) >= 0;

            if (bolMask) {
                sCod += sMask.charAt(i);
                mskLen++;
            } else {
                sCod += sValue.charAt(nCount);
                nCount++;
            }
      
            i++;
        }
        if (sMask.length == sCod.length) {
            return false;
        }
  
        field.value = sCod;
  
        if (sMask.charAt(i-1) == "9") { // apenas números...
            return ((nTecla > 47) && (nTecla < 58));
        } // números de 0 a 9
        else { // qualquer caracter...
            return true;
        }
    }

</script>
<f:view>
    <jsp:include page="includes/include_carregando_ripple.jsp" flush="true"/>
    <title>
        <h:outputText value="#{msg_aplic.prt_MalaDireta_tituloForm}"/>
    </title>




    <a4j:loadScript src="/script/jquery.maskedinput-1.2.2.js" />
    <rich:modalPanel id="listaEmail" autosized="true" shadowOpacity="true" width="550"
                     height="300" onshow="document.getElementById('formListaEmail:consultarEmail').focus();">
        <f:facet name="header">
            <h:panelGroup>
                <h:outputText value="#{msg_aplic.prt_MalaDireta_listaEmail}"/>
            </h:panelGroup>
        </f:facet>
        <f:facet name="controls">
            <h:panelGroup>
                <h:graphicImage value="/imagensCRM/close.png" style="cursor:pointer" id="hiperlinkRemetente"/>
                <rich:componentControl for="listaEmail" attachTo="hiperlinkRemetente" operation="hide" event="onclick"/>
            </h:panelGroup>
        </f:facet>
        <a4j:form id="formListaEmail" >
            <h:panelGrid columns="1" width="100%" >

                <rich:dataTable id="resultadoConsultaListaEmail"  width="100%" headerClass="consulta" rowClasses="linhaImpar, linhaPar" 
                                columnClasses="colunaCentralizada" value="#{MalaDiretaControle.listaEmail}" rows="10" var="malaDireta">
                    <rich:column>
                        <f:facet name="header">
                            <h:outputText value="#{msg_aplic.prt_MalaDireta_email}"/>
                        </f:facet>
                        <h:outputText value="#{malaDireta.email}" styleClass="campos"/>
                    </rich:column>
                </rich:dataTable>

            </h:panelGrid>
        </a4j:form>
    </rich:modalPanel>

    <rich:modalPanel id="panelModeloMensagem" autosized="true" shadowOpacity="true" width="550"
                     height="300" onshow="document.getElementById('formModeloMensagem:consultarModeloMensagem').focus();">
        <f:facet name="header">
            <h:panelGroup>
                <h:outputText value="#{msg_aplic.prt_MalaDireta_consultarModeloMensagem}"/>
            </h:panelGroup>
        </f:facet>
        <f:facet name="controls">
            <h:panelGroup>
                <h:graphicImage value="/imagensCRM/close.png" style="cursor:pointer" id="hiperlinkModeloMensagem"/>
                <rich:componentControl for="panelModeloMensagem" attachTo="hiperlinkModeloMensagem" operation="hide" event="onclick"/>
            </h:panelGroup>
        </f:facet>
        <a4j:form id="formModeloMensagem">
            <h:panelGrid columns="1" width="100%">
                <h:panelGrid columns="4" width="100%">
                    <h:outputText styleClass="tituloCampos" value="#{msg.msg_consultar_por}"/>
                    <h:selectOneMenu styleClass="campos" id="consultarModeloMensagem" value="#{MalaDiretaControle.campoConsultarModeloMensagem}">
                        <f:selectItems value="#{MalaDiretaControle.tipoConsultarComboModeloMensagem}"/>
                    </h:selectOneMenu>
                    <h:inputText id="valorConsultarModeloMensagem" onblur="blurinput(this);" onfocus="focusinput(this);"
                                 styleClass="form" value="#{MalaDiretaControle.valorConsultarModeloMensagem}"/>
                    <a4j:commandButton id="btnConsultarModeloMensagem" 
                                       reRender="formModeloMensagem:mensagemConsultarModeloMensagem, formModeloMensagem:resultadoConsultaModeloMensagem , formModeloMensagem:scResultadoModeloMensagem , formModeloMensagem"
                                       action="#{MalaDiretaControle.consultarModeloMensagem}" styleClass="botoes" value="#{msg_bt.btn_consultar}" image="./imagensCRM/botaoConsultar.png"/>
                </h:panelGrid>
                <rich:dataTable id="resultadoConsultaModeloMensagem" width="100%" headerClass="consulta" rowClasses="linhaImpar, linhaPar" columnClasses="colunaAlinhamento"
                                value="#{MalaDiretaControle.listaConsultarModeloMensagem}" rows="10" var="modeloMensagem">
                    <rich:column>
                        <f:facet name="header">
                            <h:outputText value="#{msg_aplic.prt_ModeloMensagem_codigo}"/>
                        </f:facet>
                        <h:outputText value="#{modeloMensagem.codigo}"/> 
                    </rich:column>
                    <rich:column>
                        <f:facet name="header">
                            <h:outputText value="#{msg_aplic.prt_ModeloMensagem_titulo}"/>
                        </f:facet>
                        <h:outputText value="#{modeloMensagem.titulo}"/> 
                    </rich:column>
                    <rich:column>
                        <f:facet name="header">
                            <h:outputText value="#{msg_bt.btn_opcoes}"/>
                        </f:facet>
                        <a4j:commandButton action="#{MalaDiretaControle.selecionarModeloMensagem}" focus="modeloMensagem" reRender="form, formModeloMensagem" oncomplete="Richfaces.hideModalPanel('panelModeloMensagem')" value="#{msg_bt.btn_selecionar}" styleClass="botoes" image="./imagensCRM/botaoEditar.png"/>
                    </rich:column>
                </rich:dataTable>
                <rich:datascroller align="center" for="formModeloMensagem:resultadoConsultaModeloMensagem" maxPages="10" id="scResultadoModeloMensagem"/>
                <h:panelGrid id="mensagemConsultaModeloMensagem" columns="1" width="100%" styleClass="tabMensagens">
                    <h:panelGrid columns="1" width="100%">
                        <h:outputText styleClass="mensagem" value="#{MalaDiretaControle.mensagem}"/>
                        <h:outputText styleClass="mensagemDetalhada" value="#{MalaDiretaControle.mensagemDetalhada}"/>
                    </h:panelGrid>
                </h:panelGrid>
            </h:panelGrid>
        </a4j:form>
    </rich:modalPanel>

    <rich:modalPanel id="panelEmail" autosized="true" shadowOpacity="true" width="300" height="150">
        <f:facet name="header">
            <h:panelGroup>
                <h:outputText value="Marcadores de Email"></h:outputText>
            </h:panelGroup>
        </f:facet>
        <f:facet name="controls">
            <h:panelGroup>
                <h:graphicImage value="/imagens/close.png" style="cursor:pointer" id="hidelink4" />
                <rich:componentControl for="panelEmail" attachTo="hidelink4" operation="hide" event="onclick" />
            </h:panelGroup>
        </f:facet>

        <a4j:form id="formMarcadorEmail">
            <h:panelGrid columns="1" width="95%" columnClasses="colunaCentralizada">
                <rich:dataTable id="MarcadoEmail" width="440px" headerClass="consulta" rowClasses="linhaImpar, linhaPar" columnClasses="colunaAlinhamento" var="marcadorEmail" rows="40" value="#{MalaDiretaControle.listaSelectItemMarcadoEmail}">
                    <f:facet name="header">
                        <h:outputText value="Email" />
                    </f:facet>
                    <rich:column width="170px">
                        <f:facet name="header">
                            <h:outputText value="Tags" />
                        </f:facet>
                        <h:outputText styleClass="campos" value="#{marcadorEmail.nome}" />
                    </rich:column>
                    <rich:column width="240px">
                        <f:facet name="header">
                            <h:outputText value="Opções" />
                        </f:facet>
                        <a4j:commandButton action="#{MalaDiretaControle.executarInsercaoTag}" reRender="form:imputMensagem" oncomplete="Richfaces.hideModalPanel('panelEmail');" image="./imagens/botaoAlteracaoIncluir.gif" value="Adicionar" styleClass="botoes" />
                    </rich:column>
                </rich:dataTable>
            </h:panelGrid>
        </a4j:form>
    </rich:modalPanel>
    <h:form id="form">

        <h:panelGrid columns="1" width="100%" cellpadding="0" cellspacing="0">
            <f:facet name="header">
                <jsp:include page="topoReduzidoCRM.jsp"/>
            </f:facet>




            <h:panelGrid id="panelGridForm" columns="1" styleClass="tabForm" width="100%">
                <h:panelGrid columns="1" style="height:25px; background-image:url('./imagens/fundoBarraTopo.png');background-repeat: repeat-x;" columnClasses="colunaCentralizada" width="100%">
                    <h:outputText styleClass="tituloFormulario" value="#{msg_aplic.prt_MalaDireta_tituloForm}">
                        <h:outputLink rendered="#{LoginControle.permissaoAcessoMenuVO.plano}" value="#{SuperControle.urlBaseConhecimento}como-realizar-um-contato-avulso-contato-pessoal/"
                                      title="Clique e saiba mais: Mailing" target="_blank">
                            <i class="fa-icon-question-sign" style="font-size: 18px"></i>
                        </h:outputLink>
                    </h:outputText>
                </h:panelGrid>

                <rich:tabPanel width="100%" activeTabClass="true" switchType="client" id="tabpanelenvio">

                    <!-- INICIO - ABA DADOS DO E-MAIL -->
                    <rich:tab id="dadosEmail" label="Dados de Envio" switchType="client">
                        <h:panelGrid columns="2" rowClasses="linhaImpar, linhaPar" columnClasses="classEsquerda, classDireita" width="100%">

                            <h:outputText rendered="#{MalaDiretaControle.permissaoConsultaTodasEmpresas}"
                                          styleClass="tituloCampos" value="#{msg_aplic.prt_Colaborador_empresa}" />
                            <h:panelGroup rendered="#{MalaDiretaControle.permissaoConsultaTodasEmpresas}">
                                <h:selectOneMenu  id="empresa" styleClass="form"  onblur="blurinput(this);"  onfocus="focusinput(this);"
                                                  value="#{MalaDiretaControle.malaDiretaVO.empresa.codigo}" >
                                    <f:selectItems  value="#{MalaDiretaControle.listaEmpresas}" />
                                </h:selectOneMenu>
                            </h:panelGroup>

                            <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_MalaDireta_meioEnvio}" />
                            <h:selectOneMenu onblur="blurinput(this);" onfocus="focusinput(this);" rendered="#{MalaDiretaControle.malaDiretaVO.novoObj}"
                                             styleClass="form" id="meioDeEnvio"
                                             value="#{MalaDiretaControle.malaDiretaVO.meioDeEnvio}">
                                <f:selectItems value="#{MalaDiretaControle.listaSelectItemMeioDeEnvio}" />
                                <a4j:support event="onchange" action="#{MalaDiretaControle.limparCampos}" reRender="panelGridForm,panelTamanhoRestante"></a4j:support>
                            </h:selectOneMenu>

                            <h:outputText rendered="#{!MalaDiretaControle.malaDiretaVO.novoObj}" 
                                          styleClass="tituloCampos"
                                          value="#{MalaDiretaControle.malaDiretaVO.meioDeEnvioEnum.descricao}" />

                            <h:outputText rendered="#{!MalaDiretaControle.malaDiretaVO.usarAgendamento}" 
                                          styleClass="tituloCampos" value="#{msg_aplic.prt_MalaDireta_dataEnvio}" />
                            <h:outputText rendered="#{MalaDiretaControle.malaDiretaVO.usarAgendamento}" 
                                          styleClass="tituloCampos" value="* #{msg_aplic.prt_MalaDireta_IniciarEm}:" />

                            <h:panelGroup style="vertical-align: middle;">
                                <h:panelGrid columns="4">
                                    <rich:calendar  value="#{MalaDiretaControle.malaDiretaVO.dataEnvio}"
                                                    datePattern="dd/MM/yyyy"
                                                    inputSize="8"
                                                    oninputchange="return validar_Data(this.id);"
                                                    enableManualInput="true" zindex="2" showWeeksBar="false"/>
                                    <h:outputText rendered="#{MalaDiretaControle.malaDiretaVO.usarAgendamento}"
                                                  styleClass="tituloCampos" value=" às" />
                                    <rich:inputNumberSpinner value="#{MalaDiretaControle.malaDiretaVO.horaInicio}"
                                                             rendered="#{MalaDiretaControle.malaDiretaVO.usarAgendamento}"
                                                             inputSize="1"
                                                             maxValue="23"
                                                             minValue="0">
                                    </rich:inputNumberSpinner>
                                    <h:outputText rendered="#{MalaDiretaControle.malaDiretaVO.usarAgendamento}"
                                                  styleClass="tituloCampos" value=" horas." />
                                </h:panelGrid>

                            </h:panelGroup>


                            <h:outputText rendered="#{MalaDiretaControle.malaDiretaVO.usarAgendamento}"
                                          styleClass="tituloCampos" value="#{msg_aplic.prt_MalaDireta_VigenteAte}:" />
                            <rich:calendar  value="#{MalaDiretaControle.malaDiretaVO.vigenteAte}"
                                            datePattern="dd/MM/yyyy"
                                            rendered="#{MalaDiretaControle.malaDiretaVO.usarAgendamento}"
                                            inputSize="8"
                                            oninputchange="return validar_Data(this.id);"
                                            enableManualInput="true" zindex="2" showWeeksBar="false"/>

                            <rich:jQuery id="mskData" selector=".rich-calendar-input" timing="onload" query="mask('99/99/9999')" />
                            <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_MalaDireta_remetente}" />
                            <h:panelGroup id="panelResponsavelGrupo">
                                <h:inputText id="textColaboradorResponsavel" size="50"
                                             onblur="blurinput(this);" onfocus="focusinput(this);" styleClass="form"
                                             value="#{MalaDiretaControle.malaDiretaVO.remetente.nome}"/>
                                <rich:suggestionbox   height="200" width="200"
                                                      for="textColaboradorResponsavel"
                                                      fetchValue="#{result.nome}"
                                                      suggestionAction="#{MalaDiretaControle.executarAutocompleteRemetente}"
                                                      minChars="1" rowClasses="20"
                                                      status="statusInComponent"
                                                      nothingLabel="Nenhum Responsável encontrado !"
                                                      var="result"  id="suggestionResponsavel">

                                    <h:column>
                                        <h:outputText value="#{result.nome}" />
                                    </h:column>
                                    <a4j:support event="onselect" action="#{MalaDiretaControle.setarRemetente}"/>
                                </rich:suggestionbox>
                            </h:panelGroup>

                            <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_MalaDireta_modeloMensagem}" />
                            <h:panelGroup>
                                <h:inputText  id="modeloMensagem" size="70" maxlength="100" readonly="true" 
                                              onblur="blurinput(this);" onfocus="focusinput(this);" styleClass="form" 
                                              value="#{MalaDiretaControle.malaDiretaVO.modeloMensagem.titulo}" />
                                <a4j:commandButton oncomplete="Richfaces.showModalPanel('panelModeloMensagem')" reRender="panelModeloMensagem"
                                                   image="imagensCRM/informacao.gif"
                                                   alt="#{msg_aplic.prt_MalaDireta_consultarModeloMensagem}"/>
                                <rich:spacer width="5" />
                                <a4j:commandButton id="limparModeloMensagem"
                                                   action="#{MalaDiretaControle.limparCampoModeloMensagem}"
                                                   image="imagensCRM/limpar.gif" alt="#{msg_aplic.prt_limparCampo}" reRender="modeloMensagem,form:textComentario,form:imputMensagem "/>
                            </h:panelGroup>

                            <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_MalaDireta_titulo}" />
                            <h:panelGroup>
                                <h:inputText  id="titulo" size="70" maxlength="50" 
                                              onblur="blurinput(this);" onfocus="focusinput(this);" styleClass="form"
                                              value="#{MalaDiretaControle.malaDiretaVO.titulo}" />
                                <rich:toolTip for="titulo" followMouse="true" direction="top-right"  style="width:300px; height:#{MalaDiretaControle.tamanhoToolTip}; " showDelay="200">
                                    <h:outputText styleClass="tituloCampos" escape="false"
                                                  value="#{msg.msg_tip_tituloMail}#{MalaDiretaControle.termosFiscalizados}#{msg.msg_tip_tituloMailPontos}" />
                                </rich:toolTip>
                            </h:panelGroup>

                            <h:outputText rendered="#{MalaDiretaControle.malaDiretaVO.meioDeEnvio!=1}" styleClass="tituloCampos" value="#{msg_aplic.prt_ModeloMensagem_adicionarTag}" />
                            <h:panelGroup >
                                <a4j:commandButton rendered="#{MalaDiretaControle.malaDiretaVO.meioDeEnvio==2}" id="tagNome" reRender="textComentario" image="./imagensCRM/botaoTagNome.png" title="Tag Nome" action="#{MalaDiretaControle.incluirTagNome}"/>
                                <%--  <a4j:commandButton rendered="#{MalaDiretaControle.malaDiretaVO.meioDeEnvio==1}" id="tagEmail" oncomplete="Richfaces.showModalPanel('panelEmail');" reRender="formMarcadorEmail" value="Tag E-mail" title="Tag E-mail" />--%>
                            </h:panelGroup>
                        </h:panelGrid>
                        <%--Comentário SMS--%>
                        <h:panelGrid columns="1" rendered="#{MalaDiretaControle.malaDiretaVO.meioDeEnvio==2}"  id="textComentario" styleClass="tabForm"
                                     style="height:25px; background-image:url('./imagens/fundoBarraTopo.png');background-repeat: repeat-x;"
                                     columnClasses="colunaCentralizada" width="100%" >
                            <h:outputText styleClass="tituloFormulario" value="#{msg_aplic.prt_Agenda_comentario}" />
                            <rich:spacer height="20"/>
                            <h:outputLink value="#{SuperControle.urlWikiCRM}Recursos_Extras:Mailing#SMS"
                                          title="Clique e saiba mais: Mailing SMS" target="_blank" >
                                <i class="fa-icon-question-sign" style="font-size: 18px"></i>
                                <h:outputText value="Mailing - SMS" ></h:outputText>
                            </h:outputLink>
                            <h:inputTextarea
                                onkeypress="soma(this.value);" onkeyup="soma(this.value);"
                                style="align:center;" id="comentarioTextArea" cols="100" rows="2"
                                value="#{MalaDiretaControle.malaDiretaVO.mensagem}"/>

                            <rich:toolTip for="comentarioTextArea" followMouse="true"
                                          rendered="#{MalaDiretaControle.toolTipSMS}"
                                          direction="top-right"  style="width:300px; height:#{MalaDiretaControle.tamanhoToolTipSMS}; " showDelay="200">
                                <h:outputText styleClass="tituloCampos" escape="false"
                                              value="#{MalaDiretaControle.termosFiscalizados}" />
                            </rich:toolTip>

                            <h:panelGroup id="panelTamanhoRestante" style="align:center;" layout="block" rendered="#{MalaDiretaControle.malaDiretaVO.meioDeEnvio==2}">
                                <h:inputText style="align:center;" disabled="true" size="3" id="tamanhoRestante"/>
                            </h:panelGroup>
                        </h:panelGrid>
                        <%--Comentário Email--%>
                        <h:panelGrid rendered="#{MalaDiretaControle.malaDiretaVO.meioDeEnvio!=2}" columns="1" styleClass="tabForm" columnClasses="colunaCentralizada" width="100%">
                            <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_MalaDireta_mensagem}" />
                            <rich:editor configuration="editorpropriedades" 
                                         viewMode="visual"
                                         theme="advanced" id="imputMensagem" height="500" width="700" value="#{MalaDiretaControle.malaDiretaVO.mensagem}"/>

                        </h:panelGrid>
                    </rich:tab>
                    <!-- FIM - ABA DADOS DO E-MAIL -->

                    <!-- INICIO - ABA FILTRAR CLIENTES -->
                    <rich:tab id="listaClientes" label="Filtrar Clientes" switchType="client"  rendered="#{MalaDiretaControle.malaDiretaVO.agendamentoPrevisto}">


                        <h:panelGrid id="filtros" columns="4" styleClass="text">

                            <h:outputText styleClass="text" value="#{msg_aplic.prt_MalaDireta_EventosPreDefinidos}:" />
                            <h:selectOneMenu value="#{MalaDiretaControle.malaDiretaVO.evento}" style="form">
                                <f:selectItem itemValue="" itemLabel="-- Selecione --"/>
                                <f:selectItems value="#{MalaDiretaControle.listaSelectItemEventos}"/>
                                <a4j:support event="onchange" reRender="tabpanelenvio"></a4j:support>
                            </h:selectOneMenu>

                            <h:panelGroup/>
                            <h:panelGroup/>

                            <h:outputText rendered="#{MalaDiretaControle.mostrarCategoria}" style="vertical-align:middle;" value="Categoria:"/>
                            <h:panelGroup rendered="#{MalaDiretaControle.mostrarCategoria}" >
                                <h:selectOneMenu  style="vertical-align:middle;" id="categoria"  
                                                  value="#{MalaDiretaControle.malaDiretaVO.mailingFiltros.categoria}" >
                                    <f:selectItems  value="#{MalaDiretaControle.listaSelectItemCategoria}" />
                                </h:selectOneMenu>
                            </h:panelGroup>

                            <h:outputText  rendered="#{MalaDiretaControle.mostrarSituacao}"  style="vertical-align:middle;" value="Situação:"/>
                            <h:panelGroup>
                                <h:selectOneMenu  rendered="#{MalaDiretaControle.mostrarSituacao}" 
                                                  id="situacao" style="vertical-align:middle;"
                                                  value="#{MalaDiretaControle.malaDiretaVO.mailingFiltros.situacao}" >
                                    <f:selectItems  value="#{MalaDiretaControle.listaSelectItemSituacaoCliente}" />
                                </h:selectOneMenu>
                            </h:panelGroup>

                            <h:outputText rendered="#{MalaDiretaControle.mostrarVinculo}" style="margin-bottom:10px;" value="Vínculo de Colaborador:"/>
                            <h:panelGroup>
                                <h:inputText rendered="#{MalaDiretaControle.mostrarVinculo}"  style="margin-bottom:10px;" id="vinculoCarteira" size="25"
                                             maxlength="50" onblur="blurinput(this);"  onfocus="focusinput(this);" styleClass="form"
                                             value="#{MalaDiretaControle.malaDiretaVO.mailingFiltros.filtroVinculoCarteira.colaborador.pessoa.nome}"/>
                                <rich:suggestionbox  height="200" width="200"
                                                     for="vinculoCarteira"
                                                     fetchValue="#{result.colaborador.pessoa.nome}"
                                                     suggestionAction="#{MalaDiretaControle.executarAutocompleteConsultaVinculoCarteira}"
                                                     minChars="1" rowClasses="20"
                                                     status="statusHora"
                                                     nothingLabel="Nenhum Colaborador encontrado com vínculo!"
                                                     var="result"  id="suggestionVinculoCarteira" reRender="mensagem">
                                    <a4j:support event="onselect" action="#{MalaDiretaControle.selecionarVinculoCarteiraSuggestionBox}"/>
                                    <h:column>
                                        <h:outputText value="#{result.colaborador.pessoa.nome}" />
                                    </h:column>
                                </rich:suggestionbox>

                            </h:panelGroup>

                            <h:outputText rendered="#{MalaDiretaControle.mostrarModalidade}" style="vertical-align:middle;" value="Modalidade:"/>
                            <h:panelGroup rendered="#{MalaDiretaControle.mostrarModalidade}">
                                <h:inputText  id="modalidade" size="25"
                                              maxlength="50" onblur="blurinput(this);"  onfocus="focusinput(this);" styleClass="form"
                                              value="#{MalaDiretaControle.malaDiretaVO.mailingFiltros.filtroModalidade.nome}"/>
                                <rich:suggestionbox  height="200" width="200"
                                                     for="modalidade"
                                                     fetchValue="#{result.nome}"
                                                     suggestionAction="#{MalaDiretaControle.executarAutocompleteConsultaModalidade}"
                                                     minChars="1" rowClasses="20"
                                                     status="statusHora"
                                                     nothingLabel="Nenhuma modalidade encontrada"
                                                     var="result"  id="suggestionModalidade" reRender="mensagem">
                                    <a4j:support event="onselect" action="#{MalaDiretaControle.selecionarModalidadeSuggestionBox}"/>
                                    <h:column>
                                        <h:outputText value="#{result.nome}" />
                                    </h:column>
                                </rich:suggestionbox>
                            </h:panelGroup>

                            <h:outputText rendered="#{MalaDiretaControle.mostrarDuracao}" style="vertical-align:middle;" value="Duração:"/>
                            <h:panelGroup rendered="#{MalaDiretaControle.mostrarDuracao}">
                                <h:selectOneMenu  id="duracao" style="vertical-align:middle;"  
                                                  value="#{MalaDiretaControle.malaDiretaVO.mailingFiltros.duracao}" >
                                    <f:selectItems  value="#{MalaDiretaControle.listaDuracoesContratos}" />
                                </h:selectOneMenu>
                                

                            </h:panelGroup>

                            <h:outputText rendered="#{MalaDiretaControle.mostrarEvento}" style="vertical-align:middle;" value="Evento:"/>
                            <h:panelGroup rendered="#{MalaDiretaControle.mostrarEvento}">
                                <h:selectOneMenu  id="evento" style="vertical-align:middle;"  
                                                  value="#{MalaDiretaControle.malaDiretaVO.mailingFiltros.evento}" >
                                    <f:selectItems  value="#{MalaDiretaControle.listaEventos}" />
                                </h:selectOneMenu>
                                <rich:spacer width="5px;"/>
                                <a4j:commandButton  id="limparFiltros" title="Limpar Filtros"
                                                    action="#{MalaDiretaControle.limparFiltros}" image="images/limpar.gif"
                                                    alt="#{msg_aplic.prt_limparCampo}" reRender="form"/>
                            </h:panelGroup>
                        </h:panelGrid>

                        <br/>
                        <center>
                            <a4j:commandButton id="btnBuscarFiltro" style="vertical-align:middle;" 
                                               value="Buscar" action="#{MalaDiretaControle.inicializarConsultarClientes}"
                                               reRender="items, scitems, totalClientesConsultados,panelGridMensagens" 
                                               image="images/btn_buscar.png" title="#{msg.msg_consultar_dados}"/>
                        </center>

                        <h:outputText styleClass="mensagemDetalhada" value="O botão buscar apresentará uma apenas um exemplo dos destinatários da mensagem agendada. A lista real será composta por alunos que se encaixam nos filtros selecionados na data da execução do Agendamento."/>

                        <rich:dataTable id="items" width="100%" headerClass="consulta" rowClasses="linhaPar,linhaImpar "
                                        columnClasses="centralizado, esquerda, esquerda, centralizado, centralizado, centralizado"
                                        value="#{MalaDiretaControle.clientes}" rows="10" var="cliente">

                            <rich:column filterEvent="onkeyup">
                                <f:facet name="header">
                                    <h:outputText value="#{msg_aplic.prt_Cliente_matricula}"/>
                                </f:facet>
                                <h:outputText value="#{cliente.matricula}"/>
                            </rich:column>
                            <rich:column filterEvent="onkeyup">
                                <f:facet name="header">
                                    <h:outputText value="#{msg_aplic.prt_Cliente_pessoa}"/>
                                </f:facet>
                                <h:outputText value="#{cliente.pessoa.nome}"/>
                            </rich:column>
                            <rich:column>
                                <f:facet name="header">
                                    <h:outputText value="#{msg_aplic.prt_Cliente_categoria}"/>
                                </f:facet>
                                <h:outputText  value="#{cliente.categoria.nome}"/>
                            </rich:column>
                            <rich:column>
                                <f:facet name="header">
                                    <h:outputText value="Endereço E-mail"/>
                                </f:facet>
                                <h:dataTable value="#{cliente.pessoa.emailVOs}" var="email">
                                	<h:column>
                                		<h:outputText value="#{email.email}" style="font-size: 11px; color: #474747; font-family: Arial, Verdana, sans-serif;">
                                </h:outputText>
                                	</h:column>
                                </h:dataTable>
                                
                            </rich:column>
                            
                            <rich:column>
                                <f:facet name="header">
                                    <h:outputText value="Telefones"/>
                                </f:facet>
                                <h:dataTable value="#{cliente.pessoa.telefoneVOs}" var="telefone">
                                	<h:column>
                                		<h:outputText value="#{telefone.numero}" style="font-size: 11px; color: #474747; font-family: Arial, Verdana, sans-serif;">
                                </h:outputText>
                                	</h:column>
                                </h:dataTable>
                                
                            </rich:column>

                            <rich:column>
                                <f:facet name="header">
                                    <h:outputText value="#{msg_aplic.prt_Cliente_situacao}"/>
                                </f:facet>
                                <h:dataTable id="clienteSituacao" width="100%" columnClasses="centralizado"
                                             value="#{cliente.clienteSituacaoVOs}" var="clienteSituacao">
                                    <h:column>
                                        <h:graphicImage value="./images/botaoFreePass.png" rendered="#{clienteSituacao.visitanteFreePass}"  />
                                        <h:graphicImage value="./images/botaoAtivo.png" rendered="#{clienteSituacao.ativo}"   />
                                        <h:graphicImage value="./images/botaoInativo.png" rendered="#{clienteSituacao.inativo}"  />
                                        <h:graphicImage value="./images/botaoVisitante.png" rendered="#{clienteSituacao.visitante}"   />
                                        <h:graphicImage value="./images/botaoTrancamento.png" rendered="#{clienteSituacao.trancado}"  />
                                        <h:graphicImage value="./images/botaoNormal.png" rendered="#{clienteSituacao.ativoNormal}"  />
                                        <h:graphicImage value="./images/botaoTrancadoVencido.png" rendered="#{clienteSituacao.trancadoVencido}"  />
                                        <h:graphicImage value="./images/botaoAulaAvulsa.png" rendered="#{clienteSituacao.visitanteAulaAvulsa}"  />
                                        <h:graphicImage value="./images/botaoDiaria.png" rendered="#{clienteSituacao.visitanteDiaria}"  />
                                        <h:graphicImage value="./images/botaoCancelamento.png" rendered="#{clienteSituacao.inativoCancelamento}"  />
                                        <h:graphicImage value="./images/botaoDesistente.png" rendered="#{clienteSituacao.inativoDesistente}"  />
                                        <h:graphicImage value="./images/botaoAvencer.png" rendered="#{clienteSituacao.ativoAvencer}"  />
                                        <h:graphicImage value="./images/botaoVencido.png" rendered="#{clienteSituacao.inativoVencido}"  />
                                        <h:graphicImage value="./images/botaoCarencia.png" rendered="#{clienteSituacao.ativoCarencia}"  />
                                        <h:graphicImage value="./images/botaoAtestado.png" rendered="#{clienteSituacao.ativoAtestado}"  />
                                    </h:column>
                                </h:dataTable>
                            </rich:column>
                        </rich:dataTable>
                        
                        
                        <h:panelGrid columns="1" width="100%" columnClasses="colunaCentralizada" id="scitems">
                            <h:panelGroup id="painelPaginacaoCliente" rendered="#{MalaDiretaControle.confPaginacaoClientes.existePaginacao}">
                                <a4j:commandLink id="pagiInicialCliente" styleClass="tituloCampos" value="  <<  " reRender="items, paginaAtualCliente, painelPaginacaoCliente" rendered="#{MalaDiretaControle.confPaginacaoClientes.apresentarPrimeiro}" actionListener="#{MalaDiretaControle.consultarPaginadoListenerClientes}">
                                    <f:attribute name="pagNavegacao" value="pagInicial" />
                                </a4j:commandLink>
                                <a4j:commandLink id="pagiAnteriorCliente" styleClass="tituloCampos" value="  <  "  reRender="items, paginaAtualCliente, painelPaginacaoCliente"  rendered="#{MalaDiretaControle.confPaginacaoClientes.apresentarAnterior}" actionListener="#{MalaDiretaControle.consultarPaginadoListenerClientes}">
                                    <f:attribute name="pagNavegacao" value="pagAnterior" />
                                </a4j:commandLink>
                                <h:outputText id="paginaAtualCliente" styleClass="tituloCampos" value="#{msg_aplic.prt_msg_pagina} #{MalaDiretaControle.confPaginacaoClientes.paginaAtualDeTodas}" rendered="true"/>
                                <a4j:commandLink id="pagiPosteriorCliente" styleClass="tituloCampos" value="  >  "  reRender="items, paginaAtualCliente, painelPaginacaoCliente"  rendered="#{MalaDiretaControle.confPaginacaoClientes.apresentarPosterior}" actionListener="#{MalaDiretaControle.consultarPaginadoListenerClientes}">
                                    <f:attribute name="pagNavegacao" value="pagPosterior" />
                                </a4j:commandLink>
                                <a4j:commandLink id="pagiFinalCliente" styleClass="tituloCampos" value="  >>  "  reRender="items, paginaAtualCliente, painelPaginacaoCliente"  rendered="#{MalaDiretaControle.confPaginacaoClientes.apresentarUltimo}" actionListener="#{MalaDiretaControle.consultarPaginadoListenerClientes}">
                                    <f:attribute name="pagNavegacao" value="pagFinal" />
                                </a4j:commandLink>
                                <h:outputText id="totalItensCliente" styleClass="tituloCampos" value=" [#{msg_aplic.prt_msg_itens} #{MalaDiretaControle.confPaginacaoClientes.numeroTotalItens}]" rendered="true"/>
                            </h:panelGroup>
                        </h:panelGrid>



                    </rich:tab>
                    <!-- FIM - ABA FILTRAR CLIENTES -->

                    <!----------------------- INICIO - ABA AGENDAMENTO --------------------------->
                    <rich:tab id="agendamento" label="Agendamento" switchType="client" rendered="#{MalaDiretaControle.malaDiretaVO.agendamentoPrevisto}">
                        <a4j:support status="false" event="ontabenter" reRender="outputiniciarem"></a4j:support>
                        <h:panelGrid width="80%" columns="2" columnClasses="colEsquerda, colDireita" styleClass="panelAgendamento" id="idpanelAgendamento">

                            <h:panelGroup>
                                <h:selectOneRadio styleClass="text" layout="pageDirection"
                                                  value="#{MalaDiretaControle.ocorrenciaSelecionada}">
                                    <f:selectItems value="#{MalaDiretaControle.listaSelectItemOcorrencia}"/>
                                    <a4j:support event="onchange" action="#{MalaDiretaControle.alterarTipo}" reRender="idpanelAgendamento"></a4j:support>
                                </h:selectOneRadio>
                            </h:panelGroup>

                            <h:panelGroup>
                                <h:panelGrid columns="2" columnClasses="colEsquerda" width="100%" >
                                    <h:outputText styleClass="text" value="#{msg_aplic.prt_mailing_agendamento_Iniciar}:"></h:outputText>
                                    <h:outputText id="outputiniciarem" styleClass="text" value="#{MalaDiretaControle.malaDiretaVO.dataEnvio_Apresentar} às #{MalaDiretaControle.malaDiretaVO.horaInicio} horas">

                                    </h:outputText>

                                    <h:outputText styleClass="text" value="#{msg_aplic.prt_mailing_agendamento_faixa}:"></h:outputText>
                                    <h:panelGroup>
                                        <h:inputText styleClass="form" size="2" value="#{MalaDiretaControle.malaDiretaVO.agendamento.horaInicio}"
                                                     onkeypress="return mascarahora(this, '99', event);"
                                                     id="idhoraInicio"></h:inputText>
                                        <h:outputText styleClass="text" value="#{msg_aplic.prt_mailing_agendamento_ate}"></h:outputText>
                                        <h:inputText styleClass="form" size="2" value="#{MalaDiretaControle.malaDiretaVO.agendamento.horaFim}"
                                                     id="idhoraFim"
                                                     onkeypress="return mascarahora(this, '99', event);"></h:inputText>
                                    </h:panelGroup>

                                    <h:outputText styleClass="text"  rendered="#{MalaDiretaControle.mensalmente}"
                                                  value="#{msg_aplic.prt_mailing_agendamento_meses}:"></h:outputText>
                                    <h:panelGrid columns="1">
                                        <h:panelGroup rendered="#{MalaDiretaControle.mensalmente}">
                                            <h:selectBooleanCheckbox value="#{MalaDiretaControle.malaDiretaVO.agendamento.todosMeses}">
                                                <a4j:support reRender="idpanelAgendamento" event="onclick"></a4j:support>
                                            </h:selectBooleanCheckbox>
                                            <h:outputText styleClass="text"  value="#{msg_aplic.prt_mailing_agendamento_todosMeses}"></h:outputText>
                                        </h:panelGroup>
                                        <rich:dataGrid style="border: none;" value="#{MalaDiretaControle.malaDiretaVO.agendamento.mes}" var="mes" columns="4"
                                                       rendered="#{MalaDiretaControle.mensalmente && !MalaDiretaControle.malaDiretaVO.agendamento.todosMeses}">
                                            <h:selectBooleanCheckbox value="#{mes.selecionado}"></h:selectBooleanCheckbox>
                                            <h:outputText value="#{mes.label}"></h:outputText>
                                        </rich:dataGrid>

                                    </h:panelGrid>

                                    <h:outputText styleClass="text" rendered="#{MalaDiretaControle.mensalmente}"
                                                  value="#{msg_aplic.prt_mailing_agendamento_dia}:"></h:outputText>

                                    <h:panelGrid columns="1">
                                        <h:panelGroup  rendered="#{MalaDiretaControle.mensalmente}">
                                            <h:selectBooleanCheckbox value="#{MalaDiretaControle.malaDiretaVO.agendamento.todosDias}">
                                                <a4j:support reRender="idpanelAgendamento" event="onclick"></a4j:support>
                                            </h:selectBooleanCheckbox>
                                            <h:outputText styleClass="text"  value="#{msg_aplic.prt_mailing_agendamento_todosDias}"></h:outputText>
                                        </h:panelGroup>
                                        <rich:dataGrid style="border: none;" value="#{MalaDiretaControle.malaDiretaVO.agendamento.diasMes}" var="diaM" columns="8"
                                                       rendered="#{MalaDiretaControle.mensalmente && !MalaDiretaControle.malaDiretaVO.agendamento.todosDias}">
                                            <h:selectBooleanCheckbox value="#{diaM.selecionado}"></h:selectBooleanCheckbox>
                                            <h:outputText value="#{diaM.label}"></h:outputText>
                                        </rich:dataGrid>
                                    </h:panelGrid>
                                </h:panelGrid>

                                <h:selectBooleanCheckbox rendered="#{MalaDiretaControle.semanalmente}" value="#{MalaDiretaControle.malaDiretaVO.agendamento.todosDiasSemana}">
                                    <a4j:support reRender="idpanelAgendamento" event="onclick"></a4j:support>
                                </h:selectBooleanCheckbox>
                                <h:outputText styleClass="text" rendered="#{MalaDiretaControle.semanalmente}" value="#{msg_aplic.prt_mailing_agendamento_todosDiasSemana}"></h:outputText>
                                <rich:dataGrid style="border: none;" value="#{MalaDiretaControle.malaDiretaVO.agendamento.diasSemana}" var="dia" columns="4"
                                               rendered="#{MalaDiretaControle.semanalmente && !MalaDiretaControle.malaDiretaVO.agendamento.todosDiasSemana}">
                                    <h:selectBooleanCheckbox value="#{dia.selecionado}"></h:selectBooleanCheckbox>
                                    <h:outputText value="#{dia.label}"></h:outputText>
                                </rich:dataGrid>

                            </h:panelGroup>
                        </h:panelGrid>
                    </rich:tab>
                    <!----------------------- FIM - ABA AGENDAMENTO --------------------------->

                    <rich:tab id="historico" label="Execuções" switchType="client" rendered="#{!MalaDiretaControle.malaDiretaVO.novoObj && MalaDiretaControle.malaDiretaVO.agendamentoPrevisto}">
                        <a4j:support status="false" event="ontabenter" reRender="descricaoAgendamento"></a4j:support>
                        <h:panelGrid width="100%" columns="1"
                                     columnClasses="colEsquerda" styleClass="panelAgendamento" id="descricaoAgendamento">
                            <h:outputText  styleClass="text" style="font-weight: bold; font-size: 11px;"  value="Mailing Agendado: #{MalaDiretaControle.malaDiretaVO.descricaoEvento}#{MalaDiretaControle.malaDiretaVO.agendamento.descricaoAgendamento}"></h:outputText>
                        </h:panelGrid>
                        <h:outputText rendered="#{empty MalaDiretaControle.malaDiretaVO.historico}" styleClass="text" value="Nenhuma execução foi realizada para este agendamento."></h:outputText>
                        <h:dataTable id="itens" value="#{MalaDiretaControle.malaDiretaVO.historico}"
                                     rendered="#{not empty MalaDiretaControle.malaDiretaVO.historico}" var="historico" width="100%"
                                     headerClass="consulta" rowClasses="linhaImpar, linhaPar" columnClasses="centralizado vintecinco, centralizado vintecinco, centralizado vintecinco, centralizado dez, centralizado dez, centralizado dez">

                            <h:column>
                                <f:facet name="header">
                                    <h:outputText value="Início"/>
                                </f:facet>
                                <h:outputText value="#{historico.dataInicio}">
                                    <f:convertDateTime pattern="dd/MM/yyyy HH:mm:ss"/>
                                </h:outputText>
                            </h:column>

                            <h:column>
                                <f:facet name="header">
                                    <h:outputText value="Fim"/>
                                </f:facet>
                                <h:outputText value="#{historico.dataFim}">
                                    <f:convertDateTime pattern="dd/MM/yyyy HH:mm:ss"/>
                                </h:outputText>
                            </h:column>


                            <h:column>
                                <f:facet name="header">
                                    <h:outputText value="Status"/>
                                </f:facet>
                                <h:outputText value="#{historico.status.descricao}">
                                </h:outputText>
                            </h:column>

                            <h:column>
                                <f:facet name="header">
                                    <h:outputText value="Qtd. Pessoas"/>
                                </f:facet>

                                <h:outputText value="#{historico.pessoasAfetadas}">
                                </h:outputText>
                            </h:column>

                            <h:column>
                                <f:facet name="header">
                                    <h:outputText value="Log"/>
                                </f:facet>
                                <a4j:commandLink action="#{MalaDiretaControle.mostrarLog}"
                                                 reRender="panelTextoLivreItem"
                                                 oncomplete="Richfaces.showModalPanel('panelTextoLivreItem');">
                                    <h:outputText style="font-weight: bold;"  value="..." title="LOG">
                                    </h:outputText>
                                </a4j:commandLink>
                            </h:column>

                            <h:column>
                                <f:facet name="header">
                                    <h:outputText value="SQL"/>
                                </f:facet>
                                <a4j:commandLink action="#{MalaDiretaControle.mostrarSql}"
                                                 reRender="panelTextoLivreItem"
                                                 oncomplete="Richfaces.showModalPanel('panelTextoLivreItem');">
                                    <h:outputText style="font-weight: bold;" value="..." title="SQL">
                                    </h:outputText>
                                </a4j:commandLink>
                            </h:column>

                        </h:dataTable>

                        <h:panelGrid columns="1" width="100%" columnClasses="colunaCentralizada">
                            <h:panelGroup id="painelPaginacao" rendered="#{MalaDiretaControle.confPaginacaoHistorico.existePaginacao}">
                                <a4j:commandLink id="pagiInicial" styleClass="tituloCampos" value="  <<  " reRender="itens, paginaAtual, painelPaginacao" rendered="#{MalaDiretaControle.confPaginacaoHistorico.apresentarPrimeiro}" actionListener="#{MalaDiretaControle.consultarPaginadoListenerHistorico}">
                                    <f:attribute name="pagNavegacao" value="pagInicial" />
                                </a4j:commandLink>
                                <a4j:commandLink id="pagiAnterior" styleClass="tituloCampos" value="  <  "  reRender="itens, paginaAtual, painelPaginacao"  rendered="#{MalaDiretaControle.confPaginacaoHistorico.apresentarAnterior}" actionListener="#{MalaDiretaControle.consultarPaginadoListenerHistorico}">
                                    <f:attribute name="pagNavegacao" value="pagAnterior" />
                                </a4j:commandLink>
                                <h:outputText id="paginaAtual" styleClass="tituloCampos" value="#{msg_aplic.prt_msg_pagina} #{MalaDiretaControle.confPaginacaoHistorico.paginaAtualDeTodas}" rendered="true"/>
                                <a4j:commandLink id="pagiPosterior" styleClass="tituloCampos" value="  >  "  reRender="itens, paginaAtual, painelPaginacao"  rendered="#{MalaDiretaControle.confPaginacaoHistorico.apresentarPosterior}" actionListener="#{MalaDiretaControle.consultarPaginadoListenerHistorico}">
                                    <f:attribute name="pagNavegacao" value="pagPosterior" />
                                </a4j:commandLink>
                                <a4j:commandLink id="pagiFinal" styleClass="tituloCampos" value="  >>  "  reRender="itens, paginaAtual, painelPaginacao"  rendered="#{MalaDiretaControle.confPaginacaoHistorico.apresentarUltimo}" actionListener="#{MalaDiretaControle.consultarPaginadoListenerHistorico}">
                                    <f:attribute name="pagNavegacao" value="pagFinal" />
                                </a4j:commandLink>
                                <h:outputText id="totalItens" styleClass="tituloCampos" value=" [#{msg_aplic.prt_msg_itens} #{MalaDiretaControle.confPaginacaoHistorico.numeroTotalItens}]" rendered="true"/>
                            </h:panelGroup>
                        </h:panelGrid>
                    </rich:tab>

                    <rich:tab id="emailsEnviados" label="Enviar Para:" switchType="client" rendered="#{!MalaDiretaControle.malaDiretaVO.novoObj && MalaDiretaControle.malaDiretaVO.agendamentoInstantaneo}">
                        <h:panelGrid id="panelCliente" columns="1" width="100%" headerClass="subordinado" columnClasses="colunaCentralizada">
                            <f:facet name="header">
                                <h:outputText value="#{msg_aplic.prt_MalaDiretaEnviada_tituloForm}"/>
                            </f:facet>
                            <h:panelGrid columns="2" width="100%" styleClass="tabFormSubordinada" footerClass="colunaCentralizada">
                                <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_MalaDiretaEnviada_pessoa}" />
                                <h:panelGroup id="malaDiretaPessoa">
                                    <h:inputText id="textPessoa" size="50" onblur="blurinput(this);" onfocus="focusinput(this);" styleClass="form" readonly="#{!MalaDiretaControle.malaDiretaVO.novoObj}" value="#{MalaDiretaControle.malaDiretaEnviadaVO.clienteVO.pessoa.nome}"/>
                                    <rich:suggestionbox   height="200" width="200"
                                                          for="textPessoa"
                                                          status="statusHora"
                                                          fetchValue="#{result.nome}"
                                                          suggestionAction="#{MalaDiretaControle.executarAutocompletePessoa}"
                                                          minChars="1" rowClasses="20"
                                                          nothingLabel="Nenhum Cliente encontrado !"
                                                          var="result"  id="suggestionPessoa">

                                        <h:column>
                                            <h:outputText value="#{result.nome}" />
                                        </h:column>
                                    </rich:suggestionbox>
                                </h:panelGroup>

                            </h:panelGrid>
                            <a4j:commandButton id="addEmailMailing" action="#{MalaDiretaControle.adicionarMalaDiretaEnviada}" focus="form:textPessoa" rendered="#{MalaDiretaControle.malaDiretaVO.novoObj}" reRender="form:panelCliente, form:resultadoMalaDiretaEnviada, form:panelGridMensagens" value="#{msg_bt.btn_adicionar}" image= "./imagensCRM/botaoAdicionar.png" accesskey="5" styleClass="botoes"/>
                            <h:panelGroup>
                                <h:outputText style="vertical-align:middle;" value="Número de clientes adicionados: "/>
                                <h:outputText id="totalClientesAdicionados" value="#{MalaDiretaControle.totalClientesAdicionados}" />
                            </h:panelGroup>
                        </h:panelGrid>
                        <h:panelGrid  id="resultadoMalaDiretaEnviada" columns="1" width="100%"  styleClass="tabFormSubordinada">
                            <h:dataTable id="malaDiretaEnviadaVO" rendered="#{!empty MalaDiretaControle.malaDiretaVO.malaDiretaEnviadaVOs}" width="100%" headerClass="subordinado" styleClass="tabFormSubordinada"
                                         rowClasses="linhaImparSubordinado, linhaParSubordinado" columnClasses="colunaCentralizada"
                                         value="#{MalaDiretaControle.malaDiretaVO.malaDiretaEnviadaVOs}" var="malaDiretaEnviada">
                                <h:column>
                                    <f:facet name="header">
                                        <h:outputText  value="#{msg_aplic.prt_MalaDiretaEnviada_pessoa}" />
                                    </f:facet>
                                    <h:outputText rendered="#{malaDiretaEnviada.apresentarColunaPessoa}" value="#{malaDiretaEnviada.clienteVO.pessoa.nome}" />
                                    <h:outputText rendered="#{malaDiretaEnviada.apresentarColunaPassivo}" value="#{malaDiretaEnviada.passivoVO.nome}" />
                                    <h:outputText rendered="#{malaDiretaEnviada.apresentarColunaIndicado}" value="#{malaDiretaEnviada.indicadoVO.nomeIndicado}" />
                                </h:column>
                                <h:column rendered="#{MalaDiretaControle.malaDiretaVO.meioDeEnvio==1}">
                                    <f:facet name="header">
                                        <h:outputText  value="#{msg_aplic.prt_MalaDiretaEnviada_Email}" />
                                    </f:facet>
                                    <h:panelGroup>
                                        <%--<a4j:commandButton rendered="#{malaDiretaEnviada.apresentarColunaPessoa}" id="vizualizarEmail"
                                                           reRender="formListaEmail, listaEmail" action="#{MalaDiretaControle.listarEmailPessoa}"
                                                           oncomplete="Richfaces.showModalPanel('listaEmail')" value="#{msg_bt.btn_excluir}"
                                                           image="./imagensCRM/botaoEmail.png" title="#{msg_aplic.prt_vizualizarEmail}" accesskey="7" styleClass="botoes"/>--%>
                                        <h:outputText rendered="#{MalaDiretaControle.malaDiretaVO.meioDeEnvio==1}" value="#{malaDiretaEnviada.emails}" />
                                        <h:outputText rendered="#{malaDiretaEnviada.apresentarColunaPassivo}" value="#{malaDiretaEnviada.passivoVO.email}" />
                                        <h:outputText rendered="#{malaDiretaEnviada.apresentarColunaIndicado}" value="#{malaDiretaEnviada.indicadoVO.email}" />
                                    </h:panelGroup>
                                </h:column>
                                <h:column rendered="#{MalaDiretaControle.malaDiretaVO.meioDeEnvio==2}">
                                    <f:facet name="header">
                                        <h:outputText  value="#{msg_aplic.prt_MalaDiretaEnviada_Telefone_Celular}" />
                                    </f:facet>
                                    <h:outputText rendered="#{MalaDiretaControle.malaDiretaVO.meioDeEnvio==2}" value="#{malaDiretaEnviada.telefones}" />
                                </h:column>
                                <h:column>
                                    <f:facet name="header">
                                        <h:outputText value="#{msg_bt.btn_opcoes}"/>
                                    </f:facet>
                                    <a4j:commandButton id="btnExluirEmail" action="#{MalaDiretaControle.excluirMalaDiretaEnviada}" value="#{msg_bt.btn_excluir}"
                                                       image="./imagens/botaoRemover.png" alt="#{msg.msg_excluir_dados}" styleClass="botoes"
                                                       reRender="panelCliente, resultadoMalaDiretaEnviada" />
                                </h:column>

                            </h:dataTable>
                        </h:panelGrid>
                    </rich:tab>

                </rich:tabPanel>

                <h:panelGrid columns="1" width="100%" styleClass="tabMensagens">
                    <h:panelGrid id="panelGridMensagens" columns="1" width="100%">
                        <h:outputText id="msgMailing" styleClass="mensagem"  value="#{MalaDiretaControle.mensagem}"/>
                        <h:outputText id="msgMailingDet" styleClass="mensagemDetalhada" value="#{MalaDiretaControle.mensagemDetalhada}"/>
                    </h:panelGrid>
                    <h:panelGrid columns="1" width="100%" columnClasses="colunaCentralizada">
                        <h:panelGroup layout="block" style="margin-top: 20px; text-align: center;" rendered="#{!MalaDiretaControle.malaDiretaVO.crmExtra}">
                            <a4j:commandButton id="baaaaa"
                                               action="#{MalaDiretaControle.alterar}"
                                               value="Voltar"
                                               styleClass="botoes nvoBt btSec"
                                               title="#{msg.msg_novo_dados}" accesskey="1"
                                               reRender="mailingPanel"/>
                            <a4j:commandButton id="enviar" onclick="if(!validarRemetente()){return false;}"
                                               action="#{MalaDiretaControle.gravar}"
                                               reRender="tabpanelenvio" rendered="#{MalaDiretaControle.malaDiretaVO.agendamentoPrevisto}"
                                               oncomplete="#{MalaDiretaControle.msgAlert}"
                                               value="Gravar"
                                               title="#{msg.msg_gravar_dados}"
                                               accesskey="2" styleClass="botoes nvoBt"/>
                        </h:panelGroup>
                    </h:panelGrid>
                </h:panelGrid>
            </h:panelGrid>

        </h:panelGrid>
    </h:form>
    <rich:modalPanel id="panelTextoLivreItem" autosized="true" shadowOpacity="true" width="420" height="180">
        <f:facet name="header">
            <h:panelGroup>
                <h:outputText value="#{MalaDiretaControle.labelModal}"></h:outputText>
            </h:panelGroup>
        </f:facet>
        <f:facet name="controls">
            <h:panelGroup>
                <h:graphicImage value="/imagens/close.png" style="cursor:pointer" id="hidelinkTextoLivreItem"/>
                <rich:componentControl for="panelTextoLivreItem" attachTo="hidelinkTextoLivreItem" operation="hide" event="onclick"/>
            </h:panelGroup>
        </f:facet>
        <h:outputText id="textoLivreItem" escape="false" value="#{MalaDiretaControle.logSql}" />
    </rich:modalPanel>


</f:view>
