<%@page contentType="text/html"%>
<%@page pageEncoding="ISO-8859-1"%>
<head><%@include file="/includes/include_import_minifiles.jsp" %></head>
<link href="./css/otimize.css" rel="stylesheet" type="text/css">

<%@taglib prefix="f" uri="http://java.sun.com/jsf/core"%>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html"%>
<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax"%>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich"%>

<f:loadBundle var="msg_bt" basename="propriedades.Botoes" />
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao" />
<f:loadBundle var="msg" basename="propriedades.Mensagens" />

<style type="text/css">
    body {
        margin: 0px;
        padding: 0px;
        min-width: 0 !important;
    }
</style>

<f:view>
    <jsp:include page="includes/include_carregando_ripple.jsp" flush="true"/>
    <h:panelGrid columns="1" styleClass="tabForm" width="100%">
        <h:panelGrid columns="1" style="height:10px; background-image:url('./imagens/fundoBarraTopo.png');background-repeat: repeat-x;" columnClasses="colunaCentralizada" width="100%">
            <h:outputText styleClass="tituloFormulario" value="#{msg_aplic.prt_HistoricoIndicacao_titulo}" />
        </h:panelGrid>
        <f:facet name="controls">
            <h:panelGroup>
                <h:graphicImage value="/imagensCRM/close.png" style="cursor:pointer" id="hiperlinkAgendamento" />
                <rich:componentControl for="panelAgendamento" attachTo="hiperlinkAgendamento" operation="hide" event="onclick" />
            </h:panelGroup>
        </f:facet>
        <h:form id="form">
            <h:panelGrid columns="1" styleClass="tabForm" width="100%">
                <h:panelGrid columns="1" width="100%" style="border:1px solid black">
                    <h:panelGrid columns="2" width="100%" style="border:1px solid black" columnClasses="colunaEsquerda">
                        <h:panelGrid columns="1" width="50px" cellpadding="0" cellspacing="0" columnClasses="colunaEsquerda">
                            <a4j:outputPanel id="panelFoto">
                                <a4j:mediaOutput element="img" id="imagem1" 
                                                 style="width:60px;height:80px" 
                                                 rendered="#{!SuperControle.fotosNaNuvem}" 
                                                 cacheable="false" session="true" 
                                                 createContent="#{HistoricoContatoControle.paintFoto}" 
                                                 value="#{ImagemData}" mimeType="image/jpeg">
                                    <f:param value="#{SuperControle.timeStamp}" name="time" />
                                    <f:param name="largura" value="60"/>
                                    <f:param name="altura" value="80"/>
                                </a4j:mediaOutput>
                                <h:graphicImage rendered="#{SuperControle.fotosNaNuvem}" 
                                                width="60" height="80"                                        
                                                style="width:60px;height:80px"
                                                url="#{HistoricoContatoControle.paintFotoDaNuvem}"/>
                            </a4j:outputPanel>
                        </h:panelGrid>
                        <h:panelGrid columns="1" columnClasses="colunaEsquerda" cellpadding="0" cellspacing="0" style="text-align: top;" width="100%">
                            <h:panelGrid columns="4" columnClasses="colunaEsquerda" width="100%">
                                <h:panelGrid columns="1" columnClasses="colunaEsquerda" width="100%">
                                    <h:panelGroup>
                                        <h:outputText styleClass="tituloCamposAgenda" value="#{msg_aplic.prt_HistoricoIndicacao_pessoa}" />
                                    </h:panelGroup>
                                    <h:panelGroup>
                                        <h:outputText rendered="#{IndicacaoControle.indicacaoVO.clienteQueIndicou.codigo!=0}"
                                                      styleClass="camposAgenda" value="#{IndicacaoControle.indicacaoVO.clienteQueIndicou.pessoa.nome}" />
                                        <h:outputText rendered="#{IndicacaoControle.indicacaoVO.colaboradorQueIndicou.codigo!=0}"
                                                      styleClass="camposAgenda" value="#{IndicacaoControle.indicacaoVO.colaboradorQueIndicou.pessoa.nome}" />
                                    </h:panelGroup>
                                </h:panelGrid>
                                <h:panelGrid columns="1" width="100%" columnClasses="colunaCentralizada">
                                    <h:outputText styleClass="tituloCamposAgenda" value="#{msg_aplic.prt_HistoricoIndicacao_idade}" />
                                    <h:outputText rendered="#{IndicacaoControle.indicacaoVO.clienteQueIndicou.codigo!=0}" styleClass="camposAgenda"
                                                  value="#{IndicacaoControle.idadeCliente}" />
                                    <h:outputText rendered="#{IndicacaoControle.indicacaoVO.colaboradorQueIndicou.codigo!=0}" styleClass="camposAgenda"
                                                  value="#{IndicacaoControle.idadeColaborador}" />
                                </h:panelGrid>
                                <h:panelGrid columns="1" width="100%" columnClasses="colunaCentralizada">
                                    <h:outputText styleClass="tituloCamposAgenda" value="#{msg_aplic.prt_HistoricoIndicacao_estadoCivil}" />
                                    <h:outputText rendered="#{IndicacaoControle.indicacaoVO.clienteQueIndicou.codigo!=0}"
                                                  styleClass="camposAgenda" value="#{IndicacaoControle.indicacaoVO.clienteQueIndicou.pessoa.estadoCivil_Apresentar}" />
                                    <h:outputText rendered="#{IndicacaoControle.indicacaoVO.colaboradorQueIndicou.codigo!=0}"
                                                  styleClass="camposAgenda" value="#{IndicacaoControle.indicacaoVO.colaboradorQueIndicou.pessoa.estadoCivil_Apresentar}" />
                                </h:panelGrid>  
                                <h:panelGrid columns="1" columnClasses="colunaCentralizada" width="100%">
                                    <h:outputText styleClass="tituloCamposAgenda" value="#{msg_aplic.prt_HistoricoIndicacao_dataCadastro}" />
                                    <h:outputText rendered="#{IndicacaoControle.indicacaoVO.colaboradorQueIndicou.codigo!=0}"
                                                  styleClass="camposAgenda" 
                                                  value="#{IndicacaoControle.indicacaoVO.colaboradorQueIndicou.pessoa.dataCadastro_Apresentar}"/>
                                     <h:outputText rendered="#{IndicacaoControle.indicacaoVO.clienteQueIndicou.codigo!=0}"
                                                   styleClass="camposAgenda" 
                                                   value="#{IndicacaoControle.indicacaoVO.clienteQueIndicou.pessoa.dataCadastro_Apresentar}" />
                                </h:panelGrid>
                            </h:panelGrid>
                        </h:panelGrid>
                    </h:panelGrid>
                    <h:panelGrid id="listaContatos" columns="1" width="100%" columnClasses="colunaCentralizada">
                        <rich:dataTable value="#{IndicacaoControle.listaIndicacoes}" id="listaIndicacoes" width="100%"
                                        headerClass="subordinado" rowClasses="linhaImpar, linhaPar" columnClasses="colunaCentralizada"
                                        var="indicacoes">
                            <rich:column>
                                <f:facet name="header">
                                    <h:outputText value="#{msg_aplic.prt_HistoricoIndicacao_dataHora}" />
                                </f:facet>
                                <h:outputText value="#{indicacoes.dia}" >
                                    <f:convertDateTime pattern="dd/MM/yyyy"/>
                                </h:outputText>
                            </rich:column>
                            <rich:column>
                                <f:facet name="header">
                                    <h:outputText value="#{msg_aplic.prt_HistoricoIndicacao_evento}" />
                                </f:facet>
                                <h:outputText value="#{indicacoes.evento.descricao}" />
                            </rich:column>
                            <rich:column>
                                <f:facet name="header">
                                    <h:outputText value="#{msg_aplic.prt_HistoricoIndicacao_indicados}" />
                                </f:facet>
                                <h:outputText value="#{indicacoes.indicados}" />
                            </rich:column>
                            <rich:column>
                                <f:facet name="header">
                                    <h:outputText value="#{msg_aplic.prt_HistoricoIndicacao_responsavel}" />
                                </f:facet>
                                <h:outputText value="#{indicacoes.responsavelCadastro.colaboradorVO.pessoa.primeiroNomeConcatenado}" />
                            </rich:column>
                        </rich:dataTable>
                    </h:panelGrid>
                </h:panelGrid>
            </h:panelGrid>
        </h:form>
    </h:panelGrid>
</f:view>