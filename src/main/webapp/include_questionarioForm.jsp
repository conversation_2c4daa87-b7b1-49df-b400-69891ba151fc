<head>
    <%@include file="/includes/include_import_minifiles.jsp" %>
    <link href="./css/otimize.css" rel="stylesheet" type="text/css">
    <script type="text/javascript" language="javascript" src="script/script.js"></script>


</head>
<link href="./css/otimize.css" rel="stylesheet" type="text/css">
<link href="beta/css/font-awesome_2.0.min.css" type="text/css" rel="stylesheet"/>
<%@taglib prefix="f" uri="http://java.sun.com/jsf/core" %>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html" %>
<script type="text/javascript" language="javascript" src="hoverform.js"></script>
<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax" %>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich" %>
<%@taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<f:loadBundle var="msg_bt" basename="propriedades.Botoes"/>
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao"/>
<f:loadBundle var="msg" basename="propriedades.Mensagens"/>
<link href="./css/font-awesome_2.0.min.css" type="text/css" rel="stylesheet"/>
<link href="./beta/css/font-awesome.css" type="text/css" rel="stylesheet"/>
<link href="css/gestaoRecebiveis1.0.min.css" rel="stylesheet" type="text/css">
<link href="../css/tooltipster/tooltipster.css" rel="stylesheet" type="text/css"/>
<link href="../css/tooltipster/tooltipster-light.css" rel="stylesheet" type="text/css"/>
<link href="css/otimize.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" language="javascript" src="hoverform.js"></script>


<script>
    function carregarTooltipsterQuestionario(){
        carregarQuestionario(jQuery('.tooltipster'));
    }
    function carregarQuestionario(el) {
        el.tooltipster({
            theme: 'tooltipster-light',
            position: 'bottom',
            animation: 'grow',
            contentAsHTML: true
        });
    }
</script>
<style type="text/css">
    .rich-color-picker-input {
        width: 0px;
        visibility: hidden;
    }
    .rich-tabpanel-content {
        background: none !important;
        border: none !important;
        padding-top: 7px !important;
        padding-left: 0px !important;
        padding-right: 0px !important;
        text-align: left !important;
    }
    .rich-fileupload-list-decor {
        border: none;
        padding: 0 0 0 0;
        margin: 0 0 0 0;
    }

    .rich-fileupload-toolbar-decor {
        background-color: transparent;
        border: none;
    }

    .rich-fileupload-table-td {
        border: none;
    }
    .rich-fileupload-ico-add {
        background-image: url(images/drive-upload.png);
    }

    .rich-fileupload-button {
        background-color: transparent;
        background-image: none;
    }

    .rich-fileupload-button-border {
        border: none;
    }

    .rich-fileupload-button-light, .rich-fileupload-button-press {
        background-image: none;
        background-color: transparent;
    }
    .rich-table {
        background: none !important;
        border: none !important;
        text-align: left !important;
    }

    .rich-table-cell {
        border: none !important;
        text-align: left !important;
    }

    .rich-table-row {
        padding-top: 10px !important;
    }

    .rich-tab-active {
        background: none !important;
        background-color: #ffffff !important;
        border-color: #C0C0C0;
        font-weight: bold;

        border-bottom-color: white;
        border-bottom-width: 1px;
    }

    .rich-tab-inactive {
        background: none !important;
        border: none !important;
    }

    .rich-tab-header {
        font-size: 14px;
        padding: 10px;

        font-family: arial, helvetica, sans-serif;
    }

    .rich-tabhdr-cell-disabled {
        background: none !important;
        border: none !important;
    }

    .rich-tabhdr-cell-inactive {
        background: none !important;
        border: none !important;
    }

    .rich-tabhdr-side-cell {
        background: none !important;
        border: none !important;
    }

    .rich-tabhdr-side-border {
        background: none !important;
        border: none !important;
    }

    .rich-tab-bottom-line {
        border-width: 1px;
        border-color: #C0C0C0;
        background-color: #EEEEEE !important;
    }

    .rich-tab-bottom-line table:first-child {
        width: 100%;
    }
</style>

<h:panelGrid columns="2" rowClasses="linhaImpar, linhaPar"
             columnClasses="classEsquerda, classDireita"
             width="100%">

    <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_Questionario_codigo}"/>
    <h:panelGroup>
        <h:inputText id="codigo" size="10" maxlength="10" readonly="true" onblur="blurinput(this);"
                     onfocus="focusinput(this);" styleClass="camposSomenteLeitura"
                     value="#{QuestionarioControle.questionarioVO.codigo}"/>
        <h:message for="codigo" styleClass="mensagemDetalhada"/>
    </h:panelGroup>
    <h:outputText styleClass="tituloCampos"
                  value="#{QuestionarioControle.cadastroPesquisa ? msg_aplic.prt_Questionario_titulo : msg_aplic.prt_OperadoraCartao_descricao}"/>
    <h:panelGroup rendered="#{QuestionarioControle.cadastroPesquisa}">
        <h:inputText id="nomeInterno" size="70" maxlength="100" title="#{msg_aplic.prt_titulo_pesquisa}" onblur="blurinput(this);"
                     onfocus="focusinput(this);" styleClass="form tooltipster"
                     value="#{QuestionarioControle.questionarioVO.tituloPesquisa}"/>
        <h:message for="nomeInterno" styleClass="mensagemDetalhada"/>
    </h:panelGroup>
    <h:panelGroup rendered="#{!QuestionarioControle.cadastroPesquisa}">
        <h:inputText id="descricaoQuestionario" size="70" maxlength="100" onblur="blurinput(this);"
                     onfocus="focusinput(this);" styleClass="form"
                     value="#{QuestionarioControle.questionarioVO.nomeInterno}"/>
        <h:message for="descricaoQuestionario" styleClass="mensagemDetalhada"/>
    </h:panelGroup>
    <h:outputText rendered="#{QuestionarioControle.cadastroPesquisa}" styleClass="tituloCampos"
                  value="* Identificador:" />
    <h:panelGroup rendered="#{QuestionarioControle.cadastroPesquisa}">
        <h:inputText id="Titulo" size="70" maxlength="100" onblur="blurinput(this);"
                     onfocus="focusinput(this);" styleClass="form tooltipster" title="Identificador para controle interno das pesquisas"
                     value="#{QuestionarioControle.questionarioVO.nomeInterno}"/>
        <h:message for="Titulo" styleClass="mensagemDetalhada"/>
    </h:panelGroup>


    <h:outputText rendered="#{QuestionarioControle.pesquisa}"
                  styleClass="tituloCampos" value="Ativo:"/>
    <h:panelGroup layout="block" styleClass="chk-fa-container"
                  rendered="#{QuestionarioControle.pesquisa}">
        <h:selectBooleanCheckbox styleClass="form"
                                 value="#{QuestionarioControle.questionarioVO.ativo}"
                                 id="ativo"/>
        <a4j:support reRender="form" event="onclick" status="false"/>
        <span/>
    </h:panelGroup>
    <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_Questionario_tipo}"
                  rendered="#{!QuestionarioControle.cadastroPesquisa}"/>
    <h:selectOneMenu id="tipo" onblur="blurinput(this);" onfocus="focusinput(this);"
                     styleClass="tituloCampos"
                     rendered="#{!QuestionarioControle.cadastroPesquisa}"
                     value="#{QuestionarioControle.questionarioVO.tipoQuestionario}">
        <f:selectItems value="#{QuestionarioControle.listaSelectItemTipo}"/>
        <a4j:support event="onchange" reRender="form"/>
    </h:selectOneMenu>

    <h:outputText rendered="#{QuestionarioControle.pesquisa}"
                  styleClass="tituloCampos" value="Limitar a 1 resposta:"/>
    <h:panelGroup layout="block" styleClass="chk-fa-container"
                  rendered="#{QuestionarioControle.pesquisa}">
        <h:selectBooleanCheckbox styleClass="tooltipster"
                                 value="#{QuestionarioControle.questionarioVO.somenteUmaResposta}"
                                 id="somenteUmaResposta" title="#{msg_aplic.prt_Limitar_Uma_Pesquisa}"/>
        <a4j:support reRender="form" event="onclick" status="false"/>
        <span/>
    </h:panelGroup>

    <h:outputText rendered="#{QuestionarioControle.pesquisa}"
                  styleClass="tituloCampos" value="#{msg_aplic.prt_Introducao_Pesquisa}"/>
    <h:panelGroup rendered="#{QuestionarioControle.pesquisa}">
        <h:inputTextarea id="textoInicio" styleClass="inputTextClean" value="#{QuestionarioControle.questionarioVO.textoInicio}"
                         cols="50" rows="3"/>
    </h:panelGroup>

    <h:outputText rendered="#{QuestionarioControle.pesquisa}"
                  styleClass="tituloCampos"
                  value="Texto de encerramento / Agradecimento:"/>
    <h:panelGroup style="vertical-align: middle;"
                  rendered="#{QuestionarioControle.pesquisa}">
        <h:inputTextarea id="textoFim" styleClass="inputTextClean" value="#{QuestionarioControle.questionarioVO.textoFim}"
                         cols="50" rows="3"/>
    </h:panelGroup>

    <h:outputText rendered="#{QuestionarioControle.pesquisa}"
                  styleClass="tituloCampos" value="Cor de Fundo:"/>
    <h:panelGroup style="vertical-align: middle;"
                  rendered="#{QuestionarioControle.pesquisa}">
        <rich:colorPicker colorMode="hex" value="#{QuestionarioControle.questionarioVO.fundoCor}" />
    </h:panelGroup>

    <h:outputText value="Imagem de fundo:<br/>Formato (.JPG, .JPEG, .PNG)"
                  escape="false"
                  title="#{msg_aplic.prt_imagem_fundo}"
                  rendered="#{QuestionarioControle.pesquisa}"
                  styleClass="tituloCampos tooltipster"/>
    <h:panelGroup style="vertical-align: middle;"
                  id="panelArquivo"
                  rendered="#{QuestionarioControle.pesquisa}">

        <h:graphicImage rendered="#{not empty QuestionarioControle.questionarioVO.fundoImagem}"
                        style="width:100px;height:100px;"
                        url="#{QuestionarioControle.paintFotoDaNuvem}">
        </h:graphicImage>

        <rich:fileUpload id="upload"
                         rendered="#{empty QuestionarioControle.questionarioVO.fundoImagem}"
                         listHeight="0"
                         listWidth="0"
                         noDuplicate="false"
                         fileUploadListener="#{QuestionarioControle.upload}"
                         maxFilesQuantity="1"
                         allowFlash="false"
                         immediateUpload="true"
                         acceptedTypes="jpg, jpeg, png, JPG, JPEG, PNG"
                         addControlLabel="Adicionar Imagem"
                         cancelEntryControlLabel="Cancelar"
                         doneLabel="Pronto"
                         progressLabel="Enviando"
                         clearAllControlLabel="Remover"
                         clearControlLabel="Remover"
                         stopControlLabel="Parar"
                         uploadControlLabel="Enviar"
                         transferErrorLabel="Falha de Transmissão"
                         stopEntryControlLabel="Parar">
            <a4j:support event="onerror" oncomplete="#{QuestionarioControle.onCompleteArquivo}"/>
            <a4j:support event="onuploadcomplete"
                         oncomplete="#{QuestionarioControle.onCompleteArquivo};atualizarPanelArquivo()"/>
        </rich:fileUpload>

        <a4j:commandLink action="#{QuestionarioControle.limparArquivo}"
                         id="excluirImagemFundo"
                         style="padding-left: 10px"
                         oncomplete="#{QuestionarioControle.onCompleteArquivo};atualizarPanelArquivo()"
                         rendered="#{QuestionarioControle.arquivo != null || not empty QuestionarioControle.questionarioVO.fundoImagem}"
                         value="Excluir imagem de fundo"
                         reRender="panelArquivo"/>
    </h:panelGroup>
</h:panelGrid>

<a4j:jsFunction name="atualizarPanelArquivo" reRender="form:panelArquivo"/>
<h:panelGrid columns="1" styleClass="painelDadosAluno"
             style="width: calc(98.5% - 10px); height: auto; font-family: Arial,Verdana,sans-serif;">
    <f:facet name="header">
        <h:outputText styleClass="negrito cinzaEscuro pl20" style="line-height: 40px;font-size: 14px !important; " value="Perguntas"/>
    </f:facet>

    <h:panelGrid columns="2" width="100%" styleClass="tabFormSubordinada">
        <h:panelGrid columns="1" width="100%" styleClass="novaModal" headerClass="subordinado"
                     columnClasses="colunaCentralizada">
            <h:panelGroup id="perguntaGrupo" style="width: auto">
                <h:outputText styleClass="tituloCampos" style=" vertical-align: baseline!important;"
                              value="Selecionar Pergunta: "/>
                <h:inputText id="nomePergunta"
                             style="width: 320px; font-size: 12px !important;"
                             size="50"
                             maxlength="50"
                             onblur="blurinput(this);"
                             onfocus="toggleSuggestions ( #{rich:component('suggestionPergunta')} )"
                             value="#{QuestionarioControle.pergunta}"/>
                <a4j:commandLink id="criarNovaPergunta"
                                 onclick="abrirPopup('perguntaForm.jsp', 'Pergunta', 800, 595);"
                                 action="#{PerguntaControle.novo}"
                                 style=" margin-left: 1%"
                                 value="Criar Nova Pergunta"/>
                <rich:suggestionbox for="nomePergunta"
                                    width="320"
                                    status="statusInComponent"
                                    suggestionAction="#{QuestionarioControle.executarAutocompletePesq}"
                                    minChars="1"
                                    reRender="panelMensagemErro"
                                    rowClasses="linhaImpar, linhaPar"
                                    ajaxSingle="false"
                                    var="result"
                                    id="suggestionPergunta">
                    <a4j:support event="onselect"
                                 reRender="form, questionarioPerguntaVO, nomePergunta"
                                 focus="form:formPergunta"
                                 oncomplete="#{QuestionarioControle.mensagemNotificar}"
                                 action="#{QuestionarioControle.selecionarPergunta}">
                    </a4j:support>
                    <h:column>
                        <f:facet name="header">
                            <h:outputText value="Nome" styleClass="textverysmall"/>
                        </f:facet>
                        <h:outputText styleClass="form tooltipster"
                                      value="#{result.descricaoAbreviada}"
                                      title="#{result.descricao}"/>
                    </h:column>
                    <h:panelGroup styleClass="tituloCampos">
                    </h:panelGroup>
                </rich:suggestionbox>
                <script>
                    function toggleSuggestions(suggestionBox) {
                        if (suggestionBox.active)
                            suggestionBox.hide();
                        else
                            suggestionBox.callSuggestion(true);
                    }
                </script>
            </h:panelGroup>
        </h:panelGrid>
    </h:panelGrid>
    <h:dataTable id="questionarioPerguntaVO"
                 width="100%"
                 styleClass="ttblHeaderLeft"
                 rowClasses="linhaImpar, linhaPar"
                 columnClasses="colunaAlinhamento"
                 value="#{QuestionarioControle.questionarioVO.questionarioPerguntaVOs}"
                 var="questionarioPergunta">
        <rich:column style="padding: 5px !important; background-color: blue !important;">
            <f:facet name="header">
                <h:outputText value="#{msg_bt.btn_ordenar}"
                              styleClass="texto-size-15 cinza negrito upper"/>
            </f:facet>
            <h:panelGrid columns="2">
                <a4j:commandLink id="moverBaico" reRender="questionarioPerguntaVO"
                                 action="#{QuestionarioControle.moverParaBaixo}"
                                 title="Mover Pergunta Para Baixo"
                                 accesskey="6" styleClass="fa fa-icon-arrow-down fa-icon-large"/>
                <a4j:commandLink id="moverCima" reRender="questionarioPerguntaVO"
                                 action="#{QuestionarioControle.moverParaCima}"
                                 title="Mover Pergunta Para Cima"
                                 accesskey="6" styleClass="fa fa-icon-arrow-up fa-icon-large"/>
            </h:panelGrid>
        </rich:column>

        <h:column>
            <f:facet name="header">
                <h:outputText value="#{msg_aplic.prt_QuestionarioPergunta_pergunta}"
                              styleClass="texto-size-15 cinza negrito upper"/>
            </f:facet>
            <h:panelGroup layout="block">
                <h:outputText value="#{questionarioPergunta.pergunta.descricao}"
                              styleClass="texto-size-15 cinza"/>
            </h:panelGroup>

        </h:column>

        <h:column>
            <h:panelGrid title="#{msg_aplic.prt_QuestionarioPergunta_obrigatorio_titulo}" styleClass="tooltipster" width="100%" >
                <f:facet name="header">
                    <h:outputText value="#{msg_aplic.prt_QuestionarioPergunta_obrigatorio}"
                                  styleClass="texto-size-15 cinza negrito upper tooltipster"/>
                </f:facet>
                <h:panelGroup layout="block" styleClass="chk-fa-container">
                    <h:selectBooleanCheckbox id="perguntaObrigatorio" styleClass="form tooltipster"
                                             value="#{questionarioPergunta.obrigatoria}" disabled="#{QuestionarioControle.empresaLogado.bvObrigatorio}" />
                    <a4j:support reRender="form" event="onclick" status="fase"/>
                    <span/>
                </h:panelGroup>
            </h:panelGrid>
        </h:column>

        <h:column>
            <f:facet name="header">
                <h:outputText value="#{msg_bt.btn_opcoes}"
                              styleClass="texto-size-15 cinza negrito upper"/>
            </f:facet>
            <h:commandLink id="removerItemVenda" immediate="true"
                           action="#{QuestionarioControle.removerQuestionarioPergunta}"
                           title="#{msg_bt.btn_excluir}"
                           accesskey="7"
                           styleClass="linkPadrao texto-size-25 fa-icon-remove btnSelecionarModalidade"/>
        </h:column>
    </h:dataTable>
</h:panelGrid>