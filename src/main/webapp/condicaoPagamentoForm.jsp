<%@page contentType="text/html;charset=UTF-8" %> 
<head><%@include file="/includes/include_import_minifiles.jsp" %>
</head>
<link href="./css/otimize.css" rel="stylesheet" type="text/css">
<link href="beta/css/font-awesome_2.0.min.css" type="text/css" rel="stylesheet"/>
<script type="text/javascript" language="javascript" src="hoverform.js"></script>
<%@taglib prefix="f" uri="http://java.sun.com/jsf/core"%>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html"%>
<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax"%>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich"%>
<f:loadBundle var="msg_bt" basename="propriedades.Botoes"/>
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao"/>
<f:loadBundle var="msg" basename="propriedades.Mensagens"/>

<f:view>
    <jsp:include page="includes/include_carregando_ripple.jsp" flush="true"/>
    <title>
        <h:outputText value="#{msg_aplic.prt_CondicaoPagamento_tituloForm}"/>
    </title>


    <rich:modalPanel id="panelGerarParcelas" autosized="true" shadowOpacity="true" showWhenRendered="#{CondicaoPagamentoControle.condicaoPagamentoVO.msgErroGerarParcela}" width="300" height="150">
        <f:facet name="header">
            <h:panelGroup>
                <h:outputText value="Atenção!"></h:outputText>
            </h:panelGroup>
        </f:facet>
        <a4j:form id="formGerarParcelas" ajaxSubmit="true">
            <h:panelGrid columns="1" width="100%" columnClasses="colunaCentralizada">
                <h:panelGrid columns="1" footerClass="colunaCentralizada" width="100%">
                    <h:outputText id="msgGerarParcelas" styleClass="tituloCampos" value="#{msg.msg_gerarParcela}"/>
                </h:panelGrid>

                <h:panelGrid columns="2" width="20%" headerClass="subordinado" columnClasses="colunaCentralizada">
                    <a4j:commandButton id="btnGerarParcelasS" action="#{CondicaoPagamentoControle.adicionarCondicaoPagamentoParcela}" reRender="form" oncomplete="Richfaces.hideModalPanel('panelGerarParcelas')" value="#{msg_bt.btn_sim}" image="./imagens/botaoSim.png" accesskey="5" styleClass="botaoEspecial"/>
                    <a4j:commandButton id="btnGerarParcelasN" action="#{CondicaoPagamentoControle.ErroGerarParcela}" reRender="form" onclick="Richfaces.hideModalPanel('panelGerarParcelas')"  value="#{msg_bt.btn_nao}" image="./imagens/botaoNao.png" accesskey="6" styleClass="botaoEspecial"/>
                </h:panelGrid>
            </h:panelGrid>
        </a4j:form>
    </rich:modalPanel>


    <c:set var="titulo" scope="session" value="${msg_aplic.prt_CondicaoPagamento_tituloForm}"/>
    <c:set var="urlWiki" scope="session" value="${SuperControle.urlWiki}Cadastros:PPT:Condicao_de_Pagamento"/>
    <h:panelGrid columns="1" width="100%" cellpadding="0" cellspacing="0">
        <f:facet name="header">
            <jsp:include page="topoReduzido_material.jsp"/>
        </f:facet>

        <h:form id="form">
            <hr style="border-color: #e6e6e6;"/>
            <h:panelGrid columns="1"  width="100%">
                <h:panelGrid id="pnlDados" columns="2" rowClasses="linhaImpar, linhaPar" columnClasses="classEsquerda, classDireita" width="100%">

                    <h:outputText   value="#{msg_aplic.prt_CondicaoPagamento_codigo}" />
                    <h:panelGroup>
                        <h:inputText  id="codigo"  size="10" maxlength="10" readonly="true" styleClass="camposSomenteLeitura" onblur="blurinput(this);"  onfocus="focusinput(this);" value="#{CondicaoPagamentoControle.condicaoPagamentoVO.codigo}" />
                        <h:message for="codigo" styleClass="mensagemDetalhada"/>
                    </h:panelGroup>
                    <h:outputText   value="#{msg_aplic.prt_CondicaoPagamento_descricao}" />
                    <h:panelGroup>
                        <h:inputText  id="descricao"  size="45" maxlength="45" styleClass="form" onblur="blurinput(this);"  onfocus="focusinput(this);"  value="#{CondicaoPagamentoControle.condicaoPagamentoVO.descricao}" />
                        <h:message for="descricao" styleClass="mensagemDetalhada"/>
                    </h:panelGroup>
                    <h:outputText   value="#{msg_aplic.prt_CondicaoPagamento_nrParcelas}" />
                    <h:panelGroup>
                        <h:inputText  id="nrParcelas"  onkeypress="return Tecla(event);" size="10" maxlength="10" styleClass="form" onblur="blurinput(this);"  onfocus="focusinput(this);"  value="#{CondicaoPagamentoControle.condicaoPagamentoVO.nrParcelas}" >
                            <a4j:support event="onchange" reRender="form" focus="intervaloEntreParcelas" action="#{CondicaoPagamentoControle.validarEntrada}"/>
                        </h:inputText>
                        <h:message for="nrParcelas" styleClass="mensagemDetalhada"/>
                    </h:panelGroup>
                    <h:outputText   value="#{msg_aplic.prt_CondicaoPagamento_intervaloEntreParcelas}" />
                    <h:panelGroup>
                        <h:inputText  id="intervaloEntreParcelas"  onkeypress="return Tecla(event);" size="4" styleClass="form" onblur="blurinput(this);"  onfocus="focusinput(this);"  value="#{CondicaoPagamentoControle.condicaoPagamentoVO.intervaloEntreParcela}" />
                        <h:message for="nrParcelas" styleClass="mensagemDetalhada"/>
                    </h:panelGroup>
                    <h:outputText rendered="#{CondicaoPagamentoControle.visualizarEntrada}" value="#{msg_aplic.prt_CondicaoPagamento_entrada}" />
                    <h:selectBooleanCheckbox id="entrada" rendered="#{CondicaoPagamentoControle.visualizarEntrada}" styleClass="campos" value="#{CondicaoPagamentoControle.condicaoPagamentoVO.entrada}">
                        <a4j:support event="onclick" reRender="form" focus="percentualValorEntrada" action="#{CondicaoPagamentoControle.limparValorEntrada}"/>
                    </h:selectBooleanCheckbox>
                    <h:outputText   rendered="#{CondicaoPagamentoControle.condicaoPagamentoVO.entrada}" value="#{msg_aplic.prt_CondicaoPagamento_percentualValorEntrada}" />
                    <h:inputText id="percentualValorEntrada" rendered="#{CondicaoPagamentoControle.condicaoPagamentoVO.entrada}" onkeypress="return Tecla(event);" size="20" maxlength="20" styleClass="form" onblur="blurinput(this);"  onfocus="focusinput(this);"  value="#{CondicaoPagamentoControle.condicaoPagamentoVO.percentualValorEntrada}" >
                        <f:converter converterId="FormatadorNumerico" />
                    </h:inputText>

                    <h:outputText value="#{msg_aplic.prt_CondicaoPagamento_tipoConvenioCobranca}" />
                    <h:selectOneMenu id="tipoConvenioCobranca" value="#{CondicaoPagamentoControle.condicaoPagamentoVO.tipoConvenioCobranca}">
                        <f:selectItems value="#{CondicaoPagamentoControle.tiposConvenioCobranca}"/>
                        <a4j:support event="onchange" reRender="form" action="#{CondicaoPagamentoControle.alterarRecebimentoPrePago}" />
                    </h:selectOneMenu>
                    <h:panelGroup>
                    <h:outputText value="Recorrência:" />
                    </h:panelGroup>
                    <h:panelGroup>
                    <h:selectBooleanCheckbox id="recorrencia" styleClass="campos" value="#{CondicaoPagamentoControle.condicaoPagamentoVO.recorrencia}"/>
                    </h:panelGroup>
                    <%--h:outputText  value="#{msg_aplic.prt_CondicaoPagamento_condicaoPagamentoDefault}" />
                    <h:selectBooleanCheckbox   id="condicaoPagamentoDefault"styleClass="campos" value="#{CondicaoPagamentoControle.condicaoPagamentoVO.condicaoPagamentoDefault}"/--%>
                    <h:panelGroup>
                        <h:outputText id="rotPrepago" rendered="#{CondicaoPagamentoControle.visualizarCobrancaPrePago}"   value="Recebimento Pré-pago:" />
                    </h:panelGroup>
                    <h:panelGroup>
                        <h:selectBooleanCheckbox id="prepago"  rendered="#{CondicaoPagamentoControle.visualizarCobrancaPrePago}"  styleClass="campos" value="#{CondicaoPagamentoControle.condicaoPagamentoVO.recebimentoPrePago}">
                        </h:selectBooleanCheckbox>
                    </h:panelGroup>

                </h:panelGrid>
                <h:panelGrid id="panelCondicaoPagamento" columns="1" width="100%" headerClass="subordinado"  columnClasses="colunaCentralizada">
                    <f:facet name="header">
                        <h:panelGrid columns="1" style="height:25px; background-image:url('./imagens/fundoBarraTopo.png');background-repeat: repeat-x;" columnClasses="colunaCentralizada" width="100%">
                          <h:outputText styleClass="tituloFormulario" value="#{msg_aplic.prt_CondicaoPagamentoParcela_tituloForm}"/>
                        </h:panelGrid>
                    </f:facet>
                    <a4j:commandButton id="btnAddCondicao" action="#{CondicaoPagamentoControle.adicionarCondicaoPagamentoParcela}" reRender="panelCondicaoPagamento, panelMensagemErro"  focus="form:descricao" value="#{msg_bt.btn_adicionar}" image= "./imagens/botaoAdicionar.png" accesskey="5" styleClass="botoes"/>

                    <h:panelGrid columns="1" width="100%" >
                        <h:dataTable id="condicaoPagamentoParcelaVO" width="100%" headerClass="subordinado"
                                     rowClasses="linhaImpar, linhaPar" columnClasses="colunaAlinhamento"
                                     value="#{CondicaoPagamentoControle.condicaoPagamentoVO.condicaoPagamentoParcelaVOs}" var="condicaoPagamentoParcela">
                            <h:column>
                                <f:facet name="header">
                                    <h:outputText  value="#{msg_aplic.prt_CondicaoPagamentoParcela_nrParcela}" />
                                </f:facet>
                                <h:outputText  value="#{condicaoPagamentoParcela.nrParcela}" />
                            </h:column>
                            <h:column>
                                <f:facet name="header">
                                    <h:outputText  value="#{msg_aplic.prt_CondicaoPagamentoParcela_nrDiasParcela}" />
                                </f:facet>
                                <h:outputText value="#{condicaoPagamentoParcela.nrDiasParcela}" />
                            </h:column>
                        </h:dataTable>
                    </h:panelGrid>
                </h:panelGrid>

                <!--- INICIO CONDICAO PAGAMENTO COM PLANO -->

                <h:panelGrid id="panelPlanoCondicaoPagamentoGeral" columns="1" width="100%" headerClass="subordinado" columnClasses="colunaCentralizada">
                    <h:panelGrid id="panelPlanoCondicaoPagamento" columns="1" width="100%" headerClass="subordinado" columnClasses="colunaCentralizada">
                        <h:panelGrid columns="1" style="height:25px; background-image:url('./imagens/fundoBarraTopo.png');background-repeat: repeat-x;" columnClasses="colunaCentralizada" width="100%">
                            <h:outputText styleClass="tituloFormulario" value="Incluir condição de pagamento no cadastro de plano"/>
                        </h:panelGrid>
                        <h:panelGrid columns="2" width="100%" rowClasses="linhaImpar, linhaPar" columnClasses="classEsquerda, classDireita" footerClass="colunaCentralizada">

                            <h:outputText  styleClass="tituloCampos" value="Plano:" />
                            <h:panelGroup>
                                <h:selectOneMenu  onblur="blurinput(this);"  onfocus="focusinput(this);" styleClass="form"
                                                  id="comboPlano"
                                                  value="#{CondicaoPagamentoControle.condicaoPagamentoPlanoTO.planoVO.codigo}" >
                                    <f:selectItems  value="#{CondicaoPagamentoControle.listaPlano}" />
                                    <a4j:support event="onchange" action="#{CondicaoPagamentoControle.consultarDuracoesPlano}"
                                                 reRender="pgDuracoesPlano"/>
                                </h:selectOneMenu>
                            </h:panelGroup>

                            <h:outputText  value="Duração do Plano:" />
                            <h:panelGroup id="pgDuracoesPlano">
                                <h:selectOneMenu  onblur="blurinput(this);"  onfocus="focusinput(this);" styleClass="form" value="#{CondicaoPagamentoControle.condicaoPagamentoPlanoTO.duracaoPlano}" >
                                    <f:selectItems  value="#{CondicaoPagamentoControle.listaDuracao}" />
                                </h:selectOneMenu>
                            </h:panelGroup>

                        </h:panelGrid>
                        <a4j:commandButton id="addCondicaoPG" action="#{CondicaoPagamentoControle.adicionarCondicaoAoCadastroPlano}"
                                           oncomplete="#{CondicaoPagamentoControle.mensagemNotificar}"
                                           reRender="planoCondicaoPagamentoVO, panelPlanoCondicaoPagamento, panelMensagem, panelMensagemErro"
                                           focus="form:comboPlano" value="#{msg_bt.btn_adicionar}" image= "./imagens/botaoAdicionar.png" accesskey="6" styleClass="botoes"/>

                        <h:panelGrid columns="1" width="100%" >

                               <rich:dataTable id="planoCondicaoPagamentoVO" width="100%" headerClass="subordinado" styleClass="tabFormSubordinada"
                                         rowClasses="linhaImpar, linhaPar" columnClasses="colunaEsquerda, colunaCentralizada, colunaCentralizada"
                                         rows="5"
                                         value="#{CondicaoPagamentoControle.condicaoPagamentoVO.listaCondicaoPagamentoPlano}" var="planoCondicaoPagamento">
                                <h:column>
                                    <f:facet name="header">
                                        <h:outputText  value="Plano" />
                                    </f:facet>
                                    <h:outputText  value="#{planoCondicaoPagamento.planoVO.descricao}" />
                                </h:column>
                                <h:column>
                                    <f:facet name="header">
                                        <h:outputText  value="Duração" />
                                    </f:facet>
                                    <h:outputText  value="#{planoCondicaoPagamento.duracaoPlano}" />
                                </h:column>
                                <h:column>
                                    <f:facet name="header">
                                        <h:outputText  value="#{msg_bt.btn_opcoes}" />
                                    </f:facet>
                                    <h:panelGroup>
                                        <a4j:commandButton id="removerItem" reRender="planoCondicaoPagamentoVO, panelPlanoCondicaoPagamento, panelMensagem, panelMensagemErro" ajaxSingle="true"  immediate="true"
                                                           oncomplete="#{CondicaoPagamentoControle.mensagemNotificar}" action="#{CondicaoPagamentoControle.removerCondicaoPagamentoPlano}"
                                                           value="#{msg_bt.btn_excluir}" image="./imagens/botaoRemover.png" accesskey="7" styleClass="botoes">
                                            <f:setPropertyActionListener value="#{planoCondicaoPagamento}" target="#{CondicaoPagamentoControle.condicaoPagamentoPlanoTO}"></f:setPropertyActionListener>
                                        </a4j:commandButton>
                                    </h:panelGroup>
                                </h:column>
                            </rich:dataTable>
                            <rich:datascroller align="center" for="planoCondicaoPagamentoVO" maxPages="5" />
                        </h:panelGrid>

                    </h:panelGrid>
                </h:panelGrid>
                <!--- FIM CONDICAO PAGAMENTO COM PLANO -->

                <h:panelGrid columns="1" width="100%" styleClass="tabMensagens">
                    <h:panelGrid id="panelMensagemErro" columns="3" width="100%" styleClass="tabMensagens">
                        <h:panelGrid columns="1" width="100%">

                            <h:outputText value=" "/>

                        </h:panelGrid>
                        <h:commandButton id="icCondPagSuc" rendered="#{CondicaoPagamentoControle.sucesso}" image="./imagens/sucesso.png"/>
                        <h:commandButton id="icCondPagFal" rendered="#{CondicaoPagamentoControle.erro}" image="./imagens/erro.png"/>
                        <h:panelGrid columns="1" width="100%">
                            <h:outputText id="msgCondPag" styleClass="mensagem"  value="#{CondicaoPagamentoControle.mensagem}"/>
                            <h:outputText id="msgCondPagDet" styleClass="mensagemDetalhada" value="#{CondicaoPagamentoControle.mensagemDetalhada}"/>
                        </h:panelGrid>
                    </h:panelGrid>
                    <h:panelGrid columns="1" width="100%" columnClasses="colunaCentralizada">
                        <h:panelGroup>
                            <a4j:commandButton id="novo" immediate="true" action="#{CondicaoPagamentoControle.novo}" value="#{msg_bt.btn_novo}" alt="#{msg.msg_novo_dados}" accesskey="1" styleClass="botoes nvoBt btSec"/>

                            <h:outputText value="    "/>

                            <a4j:commandButton id="salvar" action="#{CondicaoPagamentoControle.gravar}" oncomplete="#{CondicaoPagamentoControle.mensagemNotificar}" reRender="form" value="#{msg_bt.btn_gravar}" alt="#{msg.msg_gravar_dados}" accesskey="2" styleClass="botoes nvoBt"/>

                            <h:outputText value="    "/>

                            <h:panelGroup id="grupoBtnExcluir">
                                <a4j:commandButton id="excluir" reRender="mdlMensagemGenerica"
                                                   oncomplete="#{CondicaoPagamentoControle.msgAlert}" action="#{CondicaoPagamentoControle.confirmarExcluir}" value="#{msg_bt.btn_excluir}"
                                                   alt="#{msg.msg_excluir_dados}" accesskey="3" styleClass="botoes nvoBt btSec" style="border: 0px" />
                            </h:panelGroup>

                            <h:outputText value="    "/>

                            <a4j:commandButton id="consultar" immediate="true" action="#{CondicaoPagamentoControle.inicializarConsultar}" value="#{msg_bt.btn_voltar_lista}" alt="#{msg.msg_consultar_dados}" accesskey="4" styleClass="botoes nvoBt btSec"/>
                            
                            <h:outputText value="    "/>
                        <a4j:commandLink action="#{CondicaoPagamentoControle.realizarConsultaLogObjetoSelecionado}"
                                           reRender="form"
                                           oncomplete="abrirPopup('visualizadorLogForm.jsp', 'VisualizadorLog', 785, 595);fireElementFromAnyParent('form:btnAtualizaTempo')"
                                           title="Visualizar Log" styleClass="botoes nvoBt btSec" style="display: inline-block;padding-bottom: 10px;">
                            <i class="fa-icon-list"></i>
                        </a4j:commandLink>
                        </h:panelGroup>
                    </h:panelGrid>
                </h:panelGrid>
            </h:panelGrid>
        </h:form>
    </h:panelGrid>

    <jsp:include page="includes/cliente/include_modal_capfoto_html5.jsp" flush="true"/>
    <jsp:include page="includes/include_modal_vinculo_agenda.jsp" flush="true"/>
    <jsp:include page="includes/include_modal_mensagem_generica.jsp" flush="true"/>
    <jsp:include page="includes/include_envio_contrato_email.jsp" flush="true"/>
</f:view>
<script>
    document.getElementById("form:descricao").focus();
</script>
