<%@page contentType="text/html"%>
<%@page pageEncoding="UTF-8"%>
<%@include file="/includes/imports.jsp" %>
<c:set var="root" value="${pageContext.request.contextPath}" scope="request" />
<%@taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions"%>

<%--<div class="box tudo dicaBoxEmbaixo">--%>
    <%--<div class="boxtop"><h:graphicImage url="/images/box_top.png"/></div>--%>

    <%--<div class="boxmiddle">--%>
        <%--<div style="text-align:center;padding:0 0px;">--%>
            <h:panelGroup layout="block" styleClass="menuBoxDescricao fundoBranco">
                <%--<c:choose>--%>
                    <%--<c:when test="${LoginControle.empresa.codigo > 0}">--%>
                        <h:panelGroup rendered="#{LoginControle.empresa.codigo > 0}">
                        <a4j:mediaOutput element="img" id="fotoEmpresa"  style="width:120px;height:160px;margin-top: 2px"  cacheable="false"
                                         session="true"
                                         rendered="#{empty LoginControle.empresa.fotoKey}"
                                         title="#{SuperControle.keyComURLConexao}"
                                         createContent="#{LoginControle.paintFoto}"  value="#{ImagemData}" mimeType="image/jpeg" >
                            <f:param value="#{SuperControle.timeStamp}" name="time"/>
                            <f:param name="largura" value="120"/>
                            <f:param name="altura" value="160"/>
                        </a4j:mediaOutput>
                        <h:graphicImage rendered="#{not empty LoginControle.empresa.fotoKey}" 
                                        width="120" height="160"                                        
                                        style="width:120px;height:160px"
                                        url="/#{LoginControle.paintFotoDaNuvem}">
                        </h:graphicImage>
                        </h:panelGroup>
                    <%--</c:when>--%>
                    <%--<c:otherwise>--%>
                <h:panelGroup rendered="#{LoginControle.empresa.codigo <= 0}">
                        <h:graphicImage url="/fotos/logoPadraoRelatorio.jpg"/>
                </h:panelGroup>
                    <%--</c:otherwise>--%>
                <%--</c:choose>--%>
            </h:panelGroup>
        <%--</div>--%>

        <%--<div style="font-family:Arial, Helvetica, sans-serif;--%>
             <%--font-size: 8pt;font-weight: bold;">--%>
            <%--<h:panelGrid style="font-family:Arial, Helvetica, sans-serif;--%>
                         <%--font-size: 8pt;font-weight: bold;" columns="2">--%>
                <%--<h:outputText rendered="#{LoginControle.usuario.nome == ''}" value="#{LoginControle.usuario.nome}"/>--%>
                <%--<h:outputText rendered="#{LoginControle.usuario.nome != ''}" value="#{LoginControle.usuario.nome} - #{LoginControle.usuario.userOamd}"/>--%>

                <%--<a4j:commandLink rendered="#{LoginControle.usuario.permiteAlterarPropriaSenha}" type="button"--%>
                                 <%--onclick="abrirPopup('#{root}/faces/alterarSenhaClienteForm.jsp', 'AlterarSenha', 410, 350);"--%>
                                 <%--title="Alterar senha" styleClass="text2"--%>
                                 <%--style="valign:middle;cursor:pointer;">--%>
                    <%--<h:graphicImage style="border:none;" value="/images/icon_chave.png"/>--%>
                <%--</a4j:commandLink>--%>

                <%--<h:outputText value="IP.: #{LoginControle.ipCliente}"/>--%>



            <%--</h:panelGrid>--%>

            <%--<h:panelGroup layout="block" style="font-family:Arial, Helvetica, sans-serif;--%>
                          <%--font-size: 8pt;font-weight: bold;">--%>
                <%--<h:outputLink target="_blank"--%>
                              <%--value="#{SuperControle.urlWikiVersaoAtual}"--%>
                              <%--title="#{SuperControle.dataVersaoComoString}">--%>
                    <%--<h:outputText value="#{SuperControle.versaoSistema}"/>--%>
                <%--</h:outputLink>--%>

                <%--<h:outputLink value="#{SuperControle.urlWikiVersoes}"--%>
                              <%--title="Clique e saiba mais: Novos Recursos da Versão #{SuperControle.versaoSistema}" target="_blank">--%>
                    <%--<h:graphicImage styleClass="linkWiki" style="margin-left:5px;" url="/imagens/wiki_link2.gif"/>--%>
                <%--</h:outputLink>--%>
            <%--</h:panelGroup>--%>
        <%--</div>--%>

    <%--</div>--%>
    <%--<div class="boxbottom"><h:graphicImage url="/images/box_bottom.png"/></div>--%>
<%--</div>--%>

<%--<div class="box">--%>
    <%--<div class="boxtop"><h:graphicImage url="/images/box_top.png"/></div>--%>

    <%--<div class="boxmiddle">--%>
        <%--<div style="text-align:left;padding:0 0px;">--%>

            <%--<h:panelGrid columns="1" style="font-family:Arial, Helvetica, sans-serif;font-size: 8pt;">--%>
                <%--<c:if test="${!empty LoginControle.empresa.nome}">--%>
                    <%--<h:outputText value="#{LoginControle.empresa.nome}"/>--%>
                <%--</c:if>--%>
                <%--<h:panelGroup>--%>
                    <%--<h:outputText value="#{LoginControle.empresa.endereco}"/>--%>
                    <%--N°: <h:outputText value="#{LoginControle.empresa.numero}"/>  <h:outputText value="  #{LoginControle.empresa.complemento}"/>--%>
                    <%--<h:outputText value="#{LoginControle.empresa.setor}"/>--%>
                <%--</h:panelGroup>                --%>
                <%--<h:panelGroup>--%>
                    <%--Cep: <h:outputText value="#{LoginControle.empresa.CEP}"/>                    --%>
                <%--</h:panelGroup>--%>
                <%--<h:panelGroup>--%>
                    <%--<h:outputText value="#{LoginControle.empresa.cidade.nome}"/>  --%>
                    <%--<h:outputText value=" - #{LoginControle.empresa.estado.descricao}"/>--%>
                    <%--<h:outputText value=" - #{LoginControle.empresa.pais.nome}"/>--%>
                <%--</h:panelGroup>    --%>
            <%--</h:panelGrid>--%>

            <%--<b class="textverysmall">${LoginControle.instanceName}</b>--%>
            <%--<c:if test="${LoginControle.usuario.administrador}">--%>
                <%--<br/><b class="textverysmall">Mem.Livre: <%=Runtime.getRuntime().freeMemory() / 1048576 + " MB"%></b>--%>
            <%--</c:if>--%>
        <%--</div>--%>
    <%--</div>--%>
    <%--<div class="boxbottom"><h:graphicImage url="/images/box_bottom.png"/></div>--%>
<%--</div>--%>