<%@page contentType="text/html"%>
<%@page pageEncoding="ISO-8859-1"%>
<head><%@include file="/includes/include_import_minifiles.jsp" %></head>
<link href="./css/otimize.css" rel="stylesheet" type="text/css">

<%@taglib prefix="f" uri="http://java.sun.com/jsf/core"%>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html"%>

<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax"%>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich"%>

<f:loadBundle var="msg_bt" basename="propriedades.Botoes"/>
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao"/>
<f:loadBundle var="msg" basename="propriedades.Mensagens"/>

<style type="text/css">
    body {
        margin: 0px;
        padding: 0px;
    }
</style>

<f:view>
    <jsp:include page="includes/include_carregando_ripple.jsp" flush="true"/>
    <title>
        <h:outputText value="#{msg_aplic.prt_Agenda_tituloForm}"/>
    </title>

    <h:panelGrid columns="1" width="100%" cellpadding="0" cellspacing="0">
        <f:facet name="header">
            <jsp:include page="topoReduzidoCRM.jsp"/>
        </f:facet>

        <h:form id="form">
            <h:commandLink action="#{AgendaControle.liberarBackingBeanMemoria}" id="idLiberarBackingBeanMemoria" style="display: none" />
            <h:panelGrid id="panelGridForm" columns="1" styleClass="tabForm" width="100%" >
                <h:panelGrid columns="1" style="height:25px; background-image:url('./imagens/fundoBarraTopo.png');background-repeat: repeat-x;" columnClasses="colunaCentralizada" width="100%">
                    <h:outputText styleClass="tituloFormulario" value="#{msg_aplic.prt_Agenda_tituloForm}"/>
                </h:panelGrid>
                <h:panelGrid columns="5" footerClass="colunaCentralizada" width="100%">
                    <h:outputText styleClass="tituloCampos" value="#{msg.msg_consultar_por}"/> 
                    <h:selectOneMenu styleClass="campos" id="consulta" required="true" value="#{AgendaControle.controleConsulta.campoConsulta}">
                        <f:selectItems value="#{AgendaControle.tipoConsultaCombo}" />
                        <a4j:support event="onchange" reRender="panelGridForm" />
                    </h:selectOneMenu>
                    <h:inputText id="valorConsulta" rendered="#{!AgendaControle.apresentarCalendarDia}" styleClass="campos" value="#{AgendaControle.controleConsulta.valorConsulta}"/>
                    <a4j:outputPanel>
                        <rich:calendar id="dia" rendered="#{AgendaControle.apresentarCalendarDia}" oninputkeypress="return mascara(this.form, this.id, '99/99/9999', event);" value="#{AgendaControle.dataConsulta}" enableManualInput="true" popup="true" inputSize="10" datePattern="dd/MM/yyyy" showApplyButton="false" cellWidth="24px" cellHeight="24px" style="width:200px" inputClass="campos" showFooter="false"/>
                    </a4j:outputPanel>
                    <h:commandButton id="consultar" styleClass="botoes" value="#{msg_bt.btn_consultar}" action="#{AgendaControle.consultar}" image="./imagensCRM/botaoConsultar.png" title="#{msg.msg_consultar_dados}" accesskey="2"/>
                </h:panelGrid>

                <h:dataTable id="items" width="100%" headerClass="consulta" rowClasses="linhaImpar, linhaPar" columnClasses="colunaAlinhamento"
                             value="#{AgendaControle.listaConsulta}" rendered="#{AgendaControle.apresentarResultadoConsulta}" rows="10" var="agenda">

                    <h:column>
                        <f:facet name="header">
                            <h:outputText value="#{msg_aplic.prt_Agenda_codigo}"/>
                        </f:facet>
                        <h:commandLink action="#{AgendaControle.editar}" id="codigo" value="#{agenda.codigo}"/>
                    </h:column>
                    <h:column>
                        <f:facet name="header">
                            <h:outputText value="#{msg_aplic.prt_Agenda_colaborador}"/>
                        </f:facet>
                        <h:commandLink action="#{AgendaControle.editar}" id="colaboradorResponsavel" value="#{agenda.colaboradorResponsavel.pessoa.nome}"/>
                    </h:column>
                    <h:column>
                        <f:facet name="header">
                            <h:outputText value="#{msg_aplic.prt_Agenda_usuarioResponsavelCadastro}"/>
                        </f:facet>
                        <h:commandLink action="#{AgendaControle.editar}" id="responsavelCadastro" value="#{agenda.responsavelCadastro.pessoa.nome}"/>
                    </h:column>
                    <h:column>
                        <f:facet name="header">
                            <h:outputText value="#{msg_aplic.prt_Agenda_dia}"/>
                        </f:facet>
                        <h:commandLink action="#{AgendaControle.editar}" id="dia" value="#{agenda.dia_Apresentar}"/>
                    </h:column>


                    <h:column>
                        <f:facet name="header">
                            <h:outputText value="#{msg_bt.btn_opcoes}"/>
                        </f:facet>
                        <h:commandButton action="#{AgendaControle.editar}" value="#{msg_bt.btn_editar}" image="./imagensCRM/botaoEditar.png" title="#{msg.msg_editar_dados}" styleClass="botoes"/>
                    </h:column>
                </h:dataTable>
                <rich:datascroller align="center" for="form:items"  id="scResultadoConsulta" rendered="#{AgendaControle.apresentarResultadoConsulta}" />

                <h:panelGrid id="panelGridMensagens" columns="1" width="100%" styleClass="tabMensagens">
                    <h:panelGrid columns="1" width="100%">
                        <h:outputText styleClass="mensagem" value="#{AgendaControle.mensagem}"/>
                        <h:outputText styleClass="mensagemDetalhada" value="#{AgendaControle.mensagemDetalhada}"/>
                    </h:panelGrid>
                    <h:panelGrid columns="1" width="100%" columnClasses="colunaCentralizada">
                        <h:commandButton id="novo" action="#{AgendaControle.novo}" value="#{msg_bt.btn_novo}" styleClass="botoes" image="./imagensCRM/botaoNovo.png" title="#{msg.msg_novo_dados}" accesskey="1"/>
                    </h:panelGrid>
                </h:panelGrid>
            </h:panelGrid>
        </h:form>
    </h:panelGrid>
</f:view>
<script>
    document.getElementById("form:valorConsulta").focus();
</script>