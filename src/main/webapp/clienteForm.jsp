
<%@page contentType="text/html;charset=UTF-8" %>
<head><%@include file="/includes/include_import_minifiles.jsp" %></head>
<link href="./css/otimize.css" rel="stylesheet" type="text/css">
<script type="text/javascript" language="javascript" src="hoverform.js"></script>
<script type="text/javascript" language="javascript" src="script/time_1.3.js"></script>
<script src="${root}/script/packJQueryPlugins.min.js" type="text/javascript"></script>
<script type="text/javascript" src="${root}/script/tooltipster/jquery.tooltipster.min.js"></script>

<%@taglib prefix="f" uri="http://java.sun.com/jsf/core"%>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html"%>
<%@taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax"%>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich"%>
<%@ taglib prefix="a4" uri="http://richfaces.org/a4j" %>

<%@include file="/includes/verificaModulo.jsp" %>
<c:set var="contexto" value="${pageContext.request.contextPath}" scope="request" />

<c:if test="${modulo eq 'centralEventos'}">
    <!-- Inclui o elemento HEAD da página -->
    <%@include file="/pages/ce/includes/include_head.jsp" %>

    <script language="javascript">
        // Coloque esse script na janela PopUp
        function atualizar() {
            var resultadoSalvarClienteInteressado = document.getElementById('form:resultadoSalvarClienteInteressado');

            if (resultadoSalvarClienteInteressado.value != ""){
                window.opener.update('Cliente salvo com sucesso.');
                window.close();
            } else {
                window.opener.update('Erro ao salvar cliente.');
            }

        }

    </script>
</c:if>
    <script type="text/javascript" language="javascript">
         setTimeout(setInterval(function() { getDocumentCookie('popupsImportante') == 'close' ? this.close() : ''; }, 500), 500);
            function validar(){

            if (document.getElementById('formConsultarCEP:estadoCEP').value == ""
                && document.getElementById('formConsultarCEP:cidadeCEP').value == ""
                && document.getElementById('formConsultarCEP:bairroCEP').value == ""
                && document.getElementById('formConsultarCEP:logradouroCEP').value == ""){

                alert("Ao menos um parâmetro deve ser informado!");
                return false;
            }


            return true;
        }
    </script>
    <script type="text/javascript" language="javascript">
        function atualizaPai(){
            setTimeout(function(){window.opener.location.reload();},3000);
        }
    </script>
    <script type="text/javascript" language="javascript">
        function redirecionarPaginaPrincipal(matricula) {
            // Concatena a string da URL recebida com a URL da página atual
            var contexto = window.location.pathname.split('/')[1];
            var urlCompleta;
            if (contexto=== null || contexto === "") {
                urlCompleta = window.location.origin + '/faces/clienteNav.jsp?page=cliente&matricula=' + matricula;
            } else {
                urlCompleta = window.location.origin + '/'+contexto+ '/faces/clienteNav.jsp?page=cliente&matricula=' + matricula;
            }
            // Redireciona para a página principal
            window.opener.location.href = urlCompleta;
        }
    </script>

<script>

    function carregarTooltipsterClienteForm() {
        carregarTooltipsterCliente(jQuery('.tooltipster'));
    }

    function carregarTooltipsterCliente(el) {
        el.tooltipster({
            theme: 'tooltipster-light',
            position: 'bottom',
            animation: 'grow',
            contentAsHTML: true
        });
    }
</script>

<f:loadBundle var="msg_bt" basename="propriedades.Botoes"/>
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao"/>
<f:loadBundle var="msg" basename="propriedades.Mensagens"/>

<f:view>
    <jsp:include page="includes/include_carregando_ripple.jsp" flush="true"/>
    <a4j:loadScript src="/script/jquery.maskedinput-1.2.2.js" />
    <title>
        <h:outputText value="#{msg_aplic.prt_Cliente_tituloForm}"/>
    </title>

    <rich:modalPanel id="panelHistoricoVinculo" autosized="true" shadowOpacity="true" width="750" height="500">
        <f:facet name="header">
            <h:panelGroup>
                <h:outputText value="Histórico Vínculo" />
            </h:panelGroup>
        </f:facet>
        <f:facet name="controls">
            <h:panelGroup>
                <h:graphicImage value="/imagens/close.png" style="cursor:pointer" id="hiperlinkHistoricoVinculo" />
                <rich:componentControl for="panelHistoricoVinculo" attachTo="hiperlinkHistoricoVinculo" operation="hide" event="onclick" />
            </h:panelGroup>
        </f:facet>
        <a4j:form>
            <jsp:include page="historicoVinculo.jsp"/>
        </a4j:form>
    </rich:modalPanel>

    <rich:modalPanel id="panelExistePessoa" autosized="true" shadowOpacity="true" showWhenRendered="#{ClienteControle.pessoaVO.apresentarRichModalErro}" width="300" height="150">
        <f:facet name="header">
            <h:panelGroup>
                <h:outputText value="Atenção!"></h:outputText>
            </h:panelGroup>
        </f:facet>
        <a4j:form id="formExistePessoa" ajaxSubmit="true">
            <h:panelGrid columns="1" width="100%" columnClasses="colunaCentralizada">
                <h:panelGrid columns="1" footerClass="colunaCentralizada" width="100%">
                    <h:outputText styleClass="tituloCampos" value="#{msg.msg_existePessoa}"/>
                </h:panelGrid>
                <h:panelGrid columns="2" width="20%" headerClass="subordinado" columnClasses="colunaCentralizada">
                    <a4j:commandButton action="#{ClienteControle.adicionarPessoa}" reRender="form" oncomplete="Richfaces.hideModalPanel('panelExistePessoa')" value="#{msg_bt.btn_sim}" image="./imagens/botaoSim.png" accesskey="5" styleClass="botaoEspecial"/>
                    <a4j:commandButton action="#{ClienteControle.setarFalso}" reRender="form" onclick="Richfaces.hideModalPanel('panelExistePessoa')"  value="#{msg_bt.btn_nao}" image="./imagens/botaoNao.png" accesskey="6" styleClass="botaoEspecial"/>
                </h:panelGrid>
            </h:panelGrid>
        </a4j:form>
    </rich:modalPanel>
    <rich:modalPanel id="panelExisteCliente" autosized="true" shadowOpacity="true" showWhenRendered="#{ClienteControle.clienteVO.apresentarRichModalErro}" width="450" height="150">
        <f:facet name="header">
            <h:panelGroup>
                <h:outputText value="Atenção!"></h:outputText>
            </h:panelGroup>
        </f:facet>
        <a4j:form id="formExisteCliente">
            <h:panelGrid columns="1" width="100%" columnClasses="colunaCentralizada" >
                <h:panelGrid columns="1" footerClass="colunaCentralizada" width="100%">
                    <h:outputText styleClass="tituloCampos" value="#{ClienteControle.clienteVO.msgErroExisteCliente}"/>
                </h:panelGrid>

                <h:panelGrid columns="2" width="20%" headerClass="subordinado" columnClasses="colunaCentralizada">
                    <a4j:commandButton rendered="#{ClienteControle.clienteVO.apresentarBotaoTransferirClienteEmpresa}"
                                       oncomplete="Richfaces.hideModalPanel('panelExisteCliente');fireElementFromParent('form:btnRederCliente');#{ClienteControle.mensagemNotificar}"
                                       accesskey="9" styleClass="botoes" action="#{ClienteControle.gravarClienteTrocandoEmpresa}"
                                       value="Transferir Cliente de Empresa" image="./imagens/botaoTransferirClienteParaEstaEmpresa.png"
                                       reRender="form"/>
                    <a4j:commandButton id="botaoIrParaTelaCliente" rendered="#{!ClienteControle.clienteVO.apresentarBotaoTransferirClienteEmpresa}" styleClass="botoes" value="Editar Cliente" action="#{ClienteControle.editarCliente}" image="./imagens/botaoEditarDadosDoCliente.png"/>
                    <a4j:commandButton reRender="form" oncomplete="Richfaces.hideModalPanel('panelExisteCliente');fireElementFromParent('form:btnRederCliente')" value="Fechar" image="./imagens/botaoFechar.png" accesskey="5" styleClass="botaoEspecial"/>
                </h:panelGrid>
            </h:panelGrid>
        </a4j:form>
    </rich:modalPanel>

    <rich:modalPanel id="panelProfissao" styleClass="novaModal" autosized="true" shadowOpacity="true" width="450" height="250">
        <f:facet name="header">
            <h:panelGroup>
                <h:outputText value="Cadastro de Profissão"></h:outputText>
            </h:panelGroup>
        </f:facet>
        <f:facet name="controls">
            <h:panelGroup>
                <h:outputText
                        styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign btnSelecionarModalidade"
                        id="hidelink1"/>
                <rich:componentControl for="panelProfissao" attachTo="hidelink1" operation="hide" event="onclick"/>
            </h:panelGroup>
        </f:facet>
        <a4j:form id="formProfissao" ajaxSubmit="true" styleClass="paginaFontResponsiva">
            <h:panelGrid columns="1"  width="100%">
               <h:panelGrid columns="2"   width="100%">
                    <h:outputText styleClass="texto-font texto-size-16 texto-cor-cinza" value="#{msg_aplic.prt_Profissao_codigo}" />
                    <h:panelGroup>
                        <h:inputText  id="codigoProfissao"  size="10" maxlength="10" readonly="true"  onblur="blurinput(this);"  onfocus="focusinput(this);" styleClass="camposSomenteLeitura"  value="#{ClienteControle.profissaoVO.codigo}" />
                        <h:message for="codigoProfissao" styleClass="mensagemDetalhada"/>
                    </h:panelGroup>
                    <h:outputText styleClass="texto-font texto-size-16 texto-cor-cinza" value="#{msg_aplic.prt_Profissao_descricao}" />
                    <h:panelGroup>
                        <h:inputText id="nomeProfissao" size="35" maxlength="45"  onblur="blurinput(this);"  onfocus="focusinput(this);" styleClass="form"  value="#{ClienteControle.profissaoVO.descricao}" />
                        <h:message for="nomeProfissao" styleClass="mensagemDetalhada"/>
                    </h:panelGroup>
                </h:panelGrid>
                <h:panelGrid id="panelMensagem" columns="1" width="100%" styleClass="tabMensagens">
                    <h:panelGrid columns="1" width="100%" columnClasses="colunaCentralizada">
                        <h:panelGroup layout="block" styleClass="container-botoes">
                            <c:if test="${modulo eq 'zillyonWeb'}">
                                <a4j:commandLink id="salvar" reRender="profissao" focus="profissao"
                                                 action="#{ClienteControle.gravarProfissao}"
                                                 oncomplete="Richfaces.hideModalPanel('panelProfissao')"
                                                 value="#{msg_bt.btn_gravar}"
                                                 title="#{msg.msg_gravar_dados}" accesskey="2" styleClass="botaoPrimario texto-size-16"/>
                            </c:if>
                            <c:if test="${modulo eq 'centralEventos'}">
                                <a4j:commandButton id="salvar" reRender="profissao" focus="profissao"
                                                   action="#{ClienteControle.gravarProfissao}" oncomplete="Richfaces.hideModalPanel('panelProfissao')"
                                                   value="#{msg_bt.btn_gravar}" image="./imagens/botoesCE/gravar.png"
                                                   alt="#{msg.msg_gravar_dados}" accesskey="2" styleClass="botaoPrimario texto-size-16"/>
                            </c:if>
                        </h:panelGroup>
                    </h:panelGrid>
                    <h:panelGrid columns="1" width="100%">
                        <h:outputText styleClass="mensagem"  value="#{ClienteControle.mensagem}"/>
                        <h:outputText styleClass="mensagemDetalhada" value="#{ClienteControle.mensagemDetalhada}"/>
                    </h:panelGrid>

                </h:panelGrid>
            </h:panelGrid>

        </a4j:form>
    </rich:modalPanel>

    <rich:modalPanel id="panelCategoria" styleClass="novaModal" autosized="true" shadowOpacity="true" width="450" height="250">
        <f:facet name="header">
            <h:panelGroup>
                <h:outputText value="Cadastro de Categoria"></h:outputText>
            </h:panelGroup>
        </f:facet>
        <f:facet name="controls">
            <h:panelGroup>
                <h:outputText
                        styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign btnSelecionarModalidade"
                        id="hidelink"/>
                <rich:componentControl for="panelCategoria" attachTo="hidelink" operation="hide" event="onclick"/>
            </h:panelGroup>
        </f:facet>
        <a4j:form id="formCategoria" ajaxSubmit="true" styleClass="paginaFontResponsiva">
            <h:panelGrid columns="1" width="100%" columnClasses="colunaCentralizada">
                <h:panelGrid columns="2" width="100%">
                    <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_Categoria_codigo}" />
                    <h:panelGroup>
                        <h:inputText  id="codigoCategoria" size="10" maxlength="10" readonly="true" onblur="blurinput(this);"  onfocus="focusinput(this);" styleClass="form" value="#{ClienteControle.categoriaVO.codigo}" />
                        <h:message for="codigoCategoria" styleClass="mensagemDetalhada"/>
                    </h:panelGroup>
                    <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_Categoria_nome}" />
                    <h:panelGroup>
                        <h:inputText  id="nome_Categoria" size="35" maxlength="50" onblur="blurinput(this);"  onfocus="focusinput(this);" styleClass="form" value="#{ClienteControle.categoriaVO.nome}" />
                        <h:message for="nome_Categoria" styleClass="mensagemDetalhada"/>
                    </h:panelGroup>
                    <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_Categoria_tipoCategoria}" />
                    <h:panelGroup layout="block" styleClass="cb-container">
                        <h:selectOneMenu id="tipoCategoria" onblur="blurinput(this);" onfocus="focusinput(this);"
                                         styleClass="form" value="#{ClienteControle.categoriaVO.tipoCategoria}">
                            <f:selectItems value="#{CategoriaControle.listaSelectItemTipoCategoriaCategoria}"/>
                        </h:selectOneMenu>
                    </h:panelGroup>
                    <%--CAMPO ESCONDIDO DEVIDO SER UM CAMPO NÃO UTILIZADO E QUE PODE GERAR CONFUSÃO AO CLIENTE--%>
                    <%--<h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_Categoria_nrConvitePermitido}" />--%>
                    <%--<h:inputText  id="nrConvitePermitido" size="10" maxlength="10" onblur="blurinput(this);"  onfocus="focusinput(this);" styleClass="form" value="#{ClienteControle.categoriaVO.nrConvitePermitido}" />--%>
                </h:panelGrid>
                <h:panelGrid id="panelMensagem" columns="1" width="100%" styleClass="tabMensagens">
                    <h:panelGrid columns="1" width="100%" columnClasses="colunaCentralizada">
                        <h:panelGroup layout="block" styleClass="container-botoes">

                            <c:if test="${modulo eq 'zillyonWeb'}">
                                <a4j:commandLink id="salvar" reRender="categoria" focus="categoria"
                                                   action="#{ClienteControle.gravarCategoria}"
                                                   oncomplete="Richfaces.hideModalPanel('panelCategoria')"
                                                   value="#{msg_bt.btn_gravar}" title="#{msg.msg_gravar_dados}" accesskey="2" styleClass="botaoPrimario texto-size-16"/>
                            </c:if>
                            <c:if test="${modulo eq 'centralEventos'}">
                                <a4j:commandButton id="salvar" reRender="categoria" focus="categoria"
                                                   action="#{ClienteControle.gravarCategoria}" oncomplete="Richfaces.hideModalPanel('panelCategoria')" value="#{msg_bt.btn_gravar}"
                                                   image="./imagens/botoesCE/gravar.png" alt="#{msg.msg_gravar_dados}" accesskey="2" styleClass="botoes"/>
                            </c:if>
                        </h:panelGroup>
                    </h:panelGrid>
                    <h:panelGrid columns="1" width="100%">
                        <h:outputText styleClass="mensagem"  value="#{ClienteControle.mensagem}"/>
                        <h:outputText styleClass="mensagemDetalhada" value="#{ClienteControle.mensagemDetalhada}"/>
                    </h:panelGrid>
                </h:panelGrid>
            </h:panelGrid>
        </a4j:form>
    </rich:modalPanel>

    <rich:modalPanel id="panelFamiliar" autosized="true" shadowOpacity="true" width="450" height="250">
        <f:facet name="header">

            <c:if test="${modulo eq 'zillyonWeb'}">
                <jsp:include page="topoReduzido.jsp"/>
            </c:if>
            <c:if test="${modulo eq 'centralEventos'}">
                <jsp:include page="pages/ce/includes/topoReduzido.jsp"/>
            </c:if>

        </f:facet>
        <f:facet name="header">
            <h:panelGroup>
                <h:outputText value="Consulta de Dependentes"/>
            </h:panelGroup>
        </f:facet>
        <f:facet name="controls">
            <h:panelGroup>
                <h:graphicImage value="/imagens/close.png" style="cursor:pointer" id="hidelink2"/>
                <rich:componentControl for="panelFamiliar" attachTo="hidelink2" operation="hide" event="onclick"/>
            </h:panelGroup>
        </f:facet>
        <a4j:form id="formFamiliar" ajaxSubmit="true">
            <h:panelGrid columns="1" width="100%" columnClasses="colunaCentralizada">
                <h:panelGrid columns="4" footerClass="colunaCentralizada" width="100%">
                    <h:outputText styleClass="tituloCampos" value="#{msg.msg_consultar_por}"/>
                    <h:selectOneMenu styleClass="campos" id="consultaFamiliar" value="#{ClienteControle.campoConsultaFamiliar}">
                        <f:selectItems value="#{ClienteControle.tipoConsultaComboFamiliar}" />
                    </h:selectOneMenu>
                    <h:inputText id="valorConsultaFamiliar" size="10" styleClass="campos" value="#{ClienteControle.valorConsultaFamiliar}"/>

                    <c:if test="${modulo eq 'zillyonWeb'}">
                        <a4j:commandButton  id="btnConsultar" reRender="formFamiliar:mensagemConsultaFamiliar, formFamiliar:resultadoConsultaFamiliar, formFamiliar:scResultadoFamiliar" action="#{ClienteControle.consultarFamiliar}" styleClass="botoes" value="#{msg_bt.btn_consultar}" image="./imagens/botaoConsultar.png" alt="#{msg.msg_consultar_dados}"/>
                    </c:if>
                    <c:if test="${modulo eq 'centralEventos'}">
                        <a4j:commandButton  id="btnConsultar" reRender="formFamiliar:mensagemConsultaFamiliar, formFamiliar:resultadoConsultaFamiliar, formFamiliar:scResultadoFamiliar"
                                            action="#{ClienteControle.consultarFamiliar}" styleClass="botoes" value="#{msg_bt.btn_consultar}"
                                            image="./imagens/botoesCE/buscar.png" alt="#{msg.msg_consultar_dados}"/>
                    </c:if>
                </h:panelGrid>
                <rich:dataTable id="resultadoConsultaFamiliar" width="100%" headerClass="consulta" rowClasses="linhaImpar, linhaPar" columnClasses="colunaAlinhamento"
                                value="#{ClienteControle.listaConsultaFamiliar}" rows="5" var="familiar">
                    <rich:column>
                        <f:facet name="header">
                            <h:outputText value="#{msg_aplic.prt_Familiar_nome}"/>
                        </f:facet>
                        <h:panelGroup>
                            <a4j:commandLink action="#{ClienteControle.selecionarFamiliar}" focus="nomeFamiliar"
                                             reRender="form" oncomplete="Richfaces.hideModalPanel('panelFamiliar')" value="#{familiar.pessoa.nome}"/>
                        </h:panelGroup>
                    </rich:column>
                    <rich:column>
                        <f:facet name="header">
                            <h:outputText value="#{msg_bt.btn_opcoes}"/>
                        </f:facet>
                        <a4j:commandButton action="#{ClienteControle.selecionarFamiliar}"
                                           focus="nomeFamiliar" reRender="form" oncomplete="Richfaces.hideModalPanel('panelFamiliar')"
                                           value="#{msg_bt.btn_selecionar}" image="./imagens/botaoEditar.png" alt="#{msg.msg_selecionar_dados}"
                                           styleClass="botoes"/>
                    </rich:column>
                </rich:dataTable>
                <rich:datascroller align="center" for="formFamiliar:resultadoConsultaFamiliar" maxPages="10"
                                   id="scResultadoFamiliar" />
                <h:panelGrid id="mensagemConsultaFamiliar" columns="1" width="100%" styleClass="tabMensagens">
                    <h:panelGrid columns="1" width="100%">
                        <h:outputText styleClass="mensagem"  value="#{ClienteControle.mensagem}"/>
                        <h:outputText  styleClass="mensagemDetalhada" value="#{ClienteControle.mensagemDetalhada}"/>

                    </h:panelGrid>
                </h:panelGrid>
            </h:panelGrid>
        </a4j:form>
    </rich:modalPanel>

    <rich:modalPanel id="panelCEP" styleClass="novaModal" autosized="true" shadowOpacity="true" width="500" height="400">
        <f:facet name="header">
            <h:panelGroup>
                <h:outputText value="#{msg_aplic.prt_CEP_tituloConsulta}"></h:outputText>
            </h:panelGroup>
        </f:facet>
        <f:facet name="controls">
            <h:panelGroup>
                <h:outputText
                        styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign btnSelecionarModalidade"
                        id="hidelinkCEP"/>
                <rich:componentControl for="panelCEP" attachTo="hidelinkCEP" operation="hide" event="onclick"/>
            </h:panelGroup>
        </f:facet>
        <a4j:form id="formConsultarCEP" ajaxSubmit="true" styleClass="paginaFontResponsiva">
            <h:panelGrid columns="1" width="100%" columnClasses="colunaCentralizada">
                <h:panelGrid columns="2" columnClasses="classEsquerda" width="100%">
                    <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_CEP_estadoC_maiusculo}" />
                    <h:panelGroup layout="block" styleClass="cb-container" style="height: 40px;">
                        <h:selectOneMenu id="estadoCEP" styleClass="campos" value="#{ClienteControle.cepControle.cepVO.ufSigla}">
                            <f:selectItems value="#{ClienteControle.listaSelectItemRgUfPessoa}" />
                        </h:selectOneMenu>
                    </h:panelGroup>
                    <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_CEP_cidadeC_maiusculo}" />
                    <h:inputText id="cidadeCEP" size="20" styleClass="campos" value="#{ClienteControle.cepControle.cepVO.cidadeDescricao}" />
                    <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_CEP_bairro_maiusculo}" />
                    <h:inputText id="bairroCEP" size="20" styleClass="campos" value="#{ClienteControle.cepControle.cepVO.bairroDescricao}" />
                    <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_CEP_logradouro_maiusculo}" />
                    <h:inputText id="logradouroCEP" size="20" styleClass="campos" value="#{ClienteControle.cepControle.cepVO.enderecoLogradouro}" />
                </h:panelGrid>
                <h:panelGrid columns="1">

                    <h:panelGroup>
                        <h:outputText styleClass="textsmall" value="Informe o nome ou parte do seu logradouro, rua ou avenida. Não Inclua o tipo da via nem o número da sua casa." />
                    </h:panelGroup>
                </h:panelGrid>
                <h:panelGroup>
                    <c:if test="${modulo eq 'zillyonWeb'}">
                        <a4j:commandLink  id="btnConsultarCEP" onclick="if(!validar()){return false;};" reRender="formConsultarCEP"
                                            action="#{ClienteControle.cepControle.consultarCEPDetalhe}" styleClass="botaoPrimario texto-size-16"
                                            value="#{msg_bt.btn_consultar}"
                                            title="#{msg.msg_consultar_dados}" />
                    </c:if>
                    <c:if test="${modulo eq 'centralEventos'}">
                        <a4j:commandLink id="btnConsultarCEP"  onclick="if(!validar()){return false;};"
                                           reRender="formConsultarCEP"
                                           action="#{ClienteControle.cepControle.consultarCEPDetalhe}"
                                           styleClass="botaoPrimario texto-size-16" value="#{msg_bt.btn_consultar}"
                                           title="#{msg.msg_consultar_dados}" />
                    </c:if>
                </h:panelGroup>
                <h:panelGroup layout="block" >
                    <rich:dataTable id="resultadoConsultaCEP" width="100%" styleClass="tabelaSimplesCustom"
                                    rendered="#{not empty ClienteControle.cepControle.listaConsultaCep}" value="#{ClienteControle.cepControle.listaConsultaCep}" rows="4" var="cep">
                        <rich:column>
                            <f:facet name="header">
                                <h:outputText style="float: left;" styleClass="texto-font texto-bold texto-cor-cinza texto-size-14" value="#{msg_aplic.prt_CEP_titulo}" />
                            </f:facet>
                            <h:panelGroup>
                                <a4j:commandLink action="#{ClienteControle.selecionarCep}" focus="CEP"
                                                 styleClass="texto-font texto-cor-cinza texto-size-14"
                                                 reRender="form:panelEnderecoCliente"
                                                 oncomplete="Richfaces.hideModalPanel('panelCEP')" value="#{cep.enderecoCep}" />
                            </h:panelGroup>
                        </rich:column>
                        <rich:column>
                            <f:facet name="header">
                                <h:outputText style="float: left;" styleClass="texto-font texto-bold texto-cor-cinza texto-size-14"  value="#{msg_aplic.prt_CEP_cidadeC}" />
                            </f:facet>
                            <h:panelGroup>
                                <a4j:commandLink action="#{ClienteControle.selecionarCep}" focus="CEP"
                                                 styleClass="texto-font texto-cor-cinza texto-size-14"
                                                 reRender="form:panelEnderecoCliente"
                                                 oncomplete="Richfaces.hideModalPanel('panelCEP')" value="#{cep.cidadeDescricao}" />
                            </h:panelGroup>
                        </rich:column>
                        <rich:column>
                            <f:facet name="header">
                                <h:outputText style="float: left;" styleClass="texto-font texto-bold texto-cor-cinza texto-size-14"  value="#{msg_aplic.prt_CEP_bairroC}" />
                            </f:facet>
                            <a4j:commandLink action="#{ClienteControle.selecionarCep}" focus="CEP"
                                             styleClass="texto-font texto-cor-cinza texto-size-14"
                                             reRender="form:panelEnderecoCliente"
                                             oncomplete="Richfaces.hideModalPanel('panelCEP')" value="#{cep.bairroDescricao}" />
                        </rich:column>
                        <rich:column>
                            <f:facet name="header">
                                <h:outputText styleClass="texto-font texto-bold texto-cor-cinza texto-size-14"  value="#{msg_aplic.prt_CEP_logradouroC}" />
                            </f:facet>
                            <a4j:commandLink action="#{ClienteControle.selecionarCep}" focus="CEP"
                                             styleClass="texto-font texto-cor-cinza texto-size-14"
                                             reRender="form:panelEnderecoCliente"
                                             oncomplete="Richfaces.hideModalPanel('panelCEP')" value="#{cep.enderecoLogradouro}" />
                        </rich:column>
                    </rich:dataTable>
                    <rich:datascroller id="scResultadoCEP" align="center" style="margin-top: 10px" styleClass="scrollPureCustom" renderIfSinglePage="false" for="formConsultarCEP:resultadoConsultaCEP" maxPages="10" />
                </h:panelGroup>
                <h:panelGrid id="mensagemConsultaCEP" columns="1" width="100%" styleClass="tabMensagens">
                    <h:panelGrid columns="1" width="100%">
                        <h:outputText styleClass="mensagem" value="#{ClienteControle.cepControle.mensagem}" />
                        <h:outputText styleClass="mensagemDetalhada" value="#{ClienteControle.cepControle.mensagemDetalhada}" />
                    </h:panelGrid>
                </h:panelGrid>
            </h:panelGrid>
            <%--<h:panelGrid columns="1" width="100%" columnClasses="colunaCentralizada">--%>
            <%--<h:panelGrid columns="2" rowClasses="linhaImpar, linhaPar" columnClasses="classEsquerda" width="100%">--%>
                  <%--<h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_CEP_estado}" />--%>
                            <%--<h:selectOneMenu id="estadoCEP" styleClass="campos" value="#{ClienteControle.cepControle.cepVO.ufSigla}">--%>
                                <%--<f:selectItems value="#{ClienteControle.listaSelectItemRgUfPessoa}" />--%>
                            <%--</h:selectOneMenu>--%>
                            <%--<h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_CEP_cidade}" />--%>
                            <%--<h:inputText id="cidadeCEP" size="20" styleClass="campos" value="#{ClienteControle.cepControle.cepVO.cidadeDescricao}" />--%>
                            <%--<h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_CEP_bairro}" />--%>
                            <%--<h:inputText id="bairroCEP" size="20" styleClass="campos" value="#{ClienteControle.cepControle.cepVO.bairroDescricao}" />--%>
                             <%--<h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_CEP_logradouro}" />--%>
                            <%--<h:inputText id="logradouroCEP" size="20" styleClass="campos" value="#{ClienteControle.cepControle.cepVO.enderecoLogradouro}" />--%>
            <%--</h:panelGrid>--%>
            <%--<h:panelGrid columns="1">--%>

                        <%--<h:panelGroup>--%>
                            <%--<h:outputText styleClass="textsmall" value="Informe o nome ou parte do seu logradouro, rua ou avenida. Não Inclua o tipo da via nem o número da sua casa." />--%>
                        <%--</h:panelGroup>--%>
                    <%--</h:panelGrid>--%>
             <%--<h:panelGroup>--%>
                        <%--<c:if test="${modulo eq 'zillyonWeb'}">--%>
                            <%--<a4j:commandButton  id="btnConsultarCEP" onclick="if(!validar()){return false;};" reRender="formConsultarCEP:mensagemConsultaCEP, formConsultarCEP:resultadoConsultaCEP, formConsultarCEP:scResultadoCEP"--%>
                                               <%--action="#{ClienteControle.cepControle.consultarCEPDetalhe}" styleClass="botoes" value="#{msg_bt.btn_consultar}"--%>
                                               <%--image="./imagens/botaoConsultar.png" alt="#{msg.msg_consultar_dados}" />--%>
                        <%--</c:if>--%>
                        <%--<c:if test="${modulo eq 'centralEventos'}">--%>
                            <%--<a4j:commandButton id="btnConsultarCEP"  onclick="if(!validar()){return false;};" reRender="formConsultarCEP:mensagemConsultaCEP, formConsultarCEP:resultadoConsultaCEP, formConsultarCEP:scResultadoCEP"--%>
                                               <%--action="#{ClienteControle.cepControle.consultarCEPDetalhe}" styleClass="botoes" value="#{msg_bt.btn_consultar}"--%>
                                               <%--image="./imagens/botoesCE/buscar.png" alt="#{msg.msg_consultar_dados}" />--%>
                        <%--</c:if>--%>

                        <%--<rich:spacer style='display:block' height="35"/>--%>
               <%--</h:panelGroup>--%>
                        <%--<h:panelGroup layout="block" style="overflow:scroll; height:300px" >--%>
                <%--<rich:dataTable id="resultadoConsultaCEP" width="100%" headerClass="consulta" rowClasses="linhaImpar, linhaPar"--%>
                                <%--columnClasses="colunaAlinhamento" value="#{ClienteControle.cepControle.listaConsultaCep}" rows="8" var="cep">--%>
                    <%--<rich:column>--%>
                        <%--<f:facet name="header">--%>
                            <%--<h:outputText value="#{msg_aplic.prt_CEP_titulo}"/>--%>
                        <%--</f:facet>--%>
                        <%--<h:panelGroup>--%>
                            <%--<a4j:commandLink action="#{ClienteControle.selecionarCep}" focus="CEP"--%>
                                             <%--reRender="form:panelEnderecoCliente" oncomplete="Richfaces.hideModalPanel('panelCEP')"--%>
                                             <%--value="#{cep.enderecoCep}"/>--%>
                        <%--</h:panelGroup>--%>
                    <%--</rich:column>--%>
                        <%--<rich:column>--%>
                       <%--<f:facet name="header">--%>
                           <%--<h:outputText value="#{msg_aplic.prt_CEP_cidadeC}" />--%>
                       <%--</f:facet>--%>
                       <%--<h:panelGroup>--%>
                             <%--<a4j:commandLink action="#{ClienteControle.selecionarCep}" focus="CEP"--%>
                                             <%--reRender="form:panelEnderecoCliente" oncomplete="Richfaces.hideModalPanel('panelCEP')"--%>
                                             <%--value="#{cep.cidadeDescricao}"/>--%>
                       <%--</h:panelGroup>--%>
                   <%--</rich:column>--%>
                    <%--<rich:column>--%>
                        <%--<f:facet name="header">--%>
                            <%--<h:outputText value="#{msg_aplic.prt_CEP_bairroC}"/>--%>
                        <%--</f:facet>--%>
                        <%--<a4j:commandLink action="#{ClienteControle.selecionarCep}" focus="CEP"--%>
                                         <%--reRender="form:panelEnderecoCliente" oncomplete="Richfaces.hideModalPanel('panelCEP')"--%>
                                         <%--value="#{cep.bairroDescricao}"/>--%>
                    <%--</rich:column>--%>
                    <%--<rich:column>--%>
                        <%--<f:facet name="header">--%>
                            <%--<h:outputText value="#{msg_aplic.prt_CEP_logradouroC}"/>--%>
                        <%--</f:facet>--%>
                        <%--<a4j:commandLink action="#{ClienteControle.selecionarCep}" focus="CEP"--%>
                                         <%--reRender="form:panelEnderecoCliente" oncomplete="Richfaces.hideModalPanel('panelCEP')"--%>
                                         <%--value="#{cep.enderecoLogradouro}"/>--%>
                    <%--</rich:column>--%>
                <%--</rich:dataTable>--%>
                <%--<rich:datascroller align="center" for="formConsultarCEP:resultadoConsultaCEP" maxPages="10"--%>
                                   <%--id="scResultadoCEP" />--%>
                <%--</h:panelGroup>--%>
                <%--<h:panelGrid id="mensagemConsultaCEP" columns="1" width="100%" styleClass="tabMensagens">--%>
                    <%--<h:panelGrid columns="1" width="100%">--%>
                        <%--<h:outputText styleClass="mensagem"  value="#{ClienteControle.cepControle.mensagem}"/>--%>
                        <%--<h:outputText styleClass="mensagemDetalhada" value="#{ClienteControle.cepControle.mensagemDetalhada}"/>--%>
                    <%--</h:panelGrid>--%>
                <%--</h:panelGrid>--%>
            <%--</h:panelGrid>--%>
        </a4j:form>
    </rich:modalPanel>

    <h:panelGrid columns="1" width="100%" cellpadding="0" cellspacing="0">
        <%-- INICIO HEADER --%>
        <c:set var="titulo" scope="session" value="${msg_aplic.prt_Cliente_tituloForm}"/>
        <c:set var="urlWiki" scope="session" value="${SuperControle.urlBaseConhecimento}como-alterar-dados-dos-alunos-clientes/"/>
        <f:facet name="header">
                <jsp:include page="topoReduzido_material.jsp"/>
        </f:facet>

        <h:form id="form">


            <input type="hidden" value="${modulo}" name="modulo"/>
            <h:panelGrid id="panelGeral" columns="1" width="100%">


                <h:panelGrid columns="1" rowClasses="linhaImpar, linhaPar" width="100%" >
                    <h:panelGroup>
                        <h:panelGroup>
                            <h:outputText styleClass="camposColunaEsquerda" value="#{msg_aplic.prt_Asterisco}" rendered="#{ClienteControle.configuracaoSistema.matriculaOb}"/>
                            <h:outputText styleClass="camposColunaEsquerda"  value="#{msg_aplic.prt_Cliente_matricula}" />
                        </h:panelGroup>
                        <rich:spacer width="5"/>
                        <h:outputText  id="matriculaOUT"  value="#{ClienteControle.clienteVO.matricula}" />
                        <rich:spacer width="20"/>
                        <h:panelGroup>
                            <h:outputText styleClass="camposColunaEsquerda" value="#{msg_aplic.prt_Asterisco}" rendered="#{ClienteControle.configuracaoSistema.nomeOb}"/>
                            <h:outputText styleClass="camposColunaEsquerda" value="#{msg_aplic.prt_Pessoa_nome}" />
                        </h:panelGroup>
                        <rich:spacer width="5"/>
                        <h:outputText id="usuarioOUT" styleClass="camposColunaEsquerda" value="#{ClienteControle.clienteVO.pessoa.nome}"/>
                        <rich:spacer width="20"/>
                    </h:panelGroup>
                    <h:panelGroup>
                        <h:outputText styleClass="camposColunaEsquerda" value="#{msg_aplic.prt_Colaborador_empresa}" />
                        <rich:spacer width="5"/>
                        <h:selectOneMenu  id="empresaOUT" style="font-family:Arial, Helvetica, sans-serif;font-size:11px;color:#767676 ;" disabled="#{!ClienteControle.clienteVO.abilitarEmpresa}" onblur="blurinput(this);"  onfocus="focusinput(this);" value="#{ClienteControle.clienteVO.empresa.codigo}" >
                            <f:selectItems value="#{ClienteControle.listaSelectItemEmpresa}" />
                        </h:selectOneMenu>
                    </h:panelGroup>
                    <h:panelGroup rendered="#{ClienteControle.clienteVO.abilitarEmpresa}">
                        <a4j:commandButton id="atualizar_empresa2" action="#{ClienteControle.montarListaSelectItemEmpresa}" image="imagens/atualizar.png" ajaxSingle="true" reRender="form:empresa"/>
                    </h:panelGroup>

                </h:panelGrid>
                <rich:tabPanel width="100%" selectedTab="#{ClienteControle.abaAtual}" switchType="ajax">
                    <rich:tab id="dadosPessoais" label="Dados Pessoais">
                        <h:panelGrid id="panelDadosPessoais" columns="1" width="100%" headerClass="subordinado" columnClasses="colunaCentralizada">
                            <f:facet name="header">
                                <h:outputText value="#{msg_aplic.prt_DadosPessoais_tituloForm}"/>
                            </f:facet>

                            <h:panelGrid columns="2" rowClasses="linhaImpar, linhaPar" columnClasses="classEsquerda, classDireita"  width="100%">
                                <%-- <h:outputText    value="#{msg_aplic.prt_Pessoa_codigo}" />
                                <h:panelGroup>
                                    <h:inputText  id="codigoPessoa"  size="10" maxlength="10" readonly="true" styleClass="camposSomenteLeitura" value="#{ClienteControle.pessoaVO.codigo}" />
                                    <h:message for="codigoPessoa" styleClass="mensagemDetalhada"/>
                                </h:panelGroup>--%>
                                <%--
                                <h:outputText    value="#{msg_aplic.prt_Pessoa_tipoPessoa}" />
                                <h:panelGroup>
                                    <h:selectOneMenu  id="tipoPessoa" styleClass="camposObrigatorios" value="#{ClienteControle.pessoaVO.tipoPessoa}" >
                                        <f:selectItems  value="#{ClienteControle.listaSelectItemTipoPessoaPessoa}" />
                                    </h:selectOneMenu>
                                    <h:message for="tipoPessoa" styleClass="mensagemDetalhada"/>
                                </h:panelGroup>--%>
                                <h:outputText rendered="#{ClienteControle.clienteVO.usuarioVO.administrador}"  value="#{msg_aplic.prt_Colaborador_empresa}" />
                                <h:panelGroup rendered="#{ClienteControle.clienteVO.usuarioVO.administrador}">
                                    <h:selectOneMenu  id="empresa" styleClass="form"  onblur="blurinput(this);"  onfocus="focusinput(this);" value="#{ClienteControle.clienteVO.empresa.codigo}" >
                                        <a4j:support event="onchange" action="#{ClienteControle.atualizarDadosEmpresa}" reRender="form:empresa"/>
                                        <f:selectItems  value="#{ClienteControle.listaSelectItemEmpresa}" />
                                    </h:selectOneMenu>
                                    <a4j:commandButton id="atualizar_empresa" action="#{ClienteControle.montarListaSelectItemEmpresa}" image="imagens/atualizar.png" ajaxSingle="true" reRender="form:empresa"/>
                                    <h:message for="empresa" styleClass="mensagemDetalhada"/>
                                </h:panelGroup>
                                <h:panelGroup>
                                    <h:outputText value="#{msg_aplic.prt_Pessoa_dataCadastro}" />
                                </h:panelGroup>
                                <h:panelGroup>
                                    <rich:calendar id="dataCadastro" readonly="true"
                                                   value="#{ClienteControle.pessoaVO.dataCadastro}"
                                                   inputSize="10"
                                                   inputClass="form"
                                                   oninputblur="blurinput(this);"
                                                   oninputfocus="focusinput(this);"
                                                   oninputchange="return validar_Data(this.id);"
                                                   oninputkeypress="return mascara(this.form, this.id, '99/99/9999', event);"
                                                   datePattern="dd/MM/yyyy"
                                                   enableManualInput="true"
                                                   zindex="2"
                                                   showWeeksBar="false" />
                                    <h:message for="dataCadastro"  styleClass="mensagemDetalhada"/>
                                </h:panelGroup>

                                <h:panelGroup>
                                    <h:outputText value="#{msg_aplic.prt_Pessoa_tipoPessoa}" />
                                </h:panelGroup>
                                <h:panelGroup>
                                    <h:selectOneMenu id="comboCategoriaPessoa" onblur="blurinput(this);"  onfocus="focusinput(this);"
                                                     styleClass="form" value="#{ClienteControle.clienteVO.pessoa.categoriaPessoa}">
                                        <f:selectItems value="#{ClienteControle.itensCategoriaPessoa}"/>
                                        <a4:support event="onchange" reRender="panelGeral"/>
                                    </h:selectOneMenu>
                                </h:panelGroup>

                                <h:panelGroup>
                                    <c:if test="${modulo eq 'centralEventos'}">
                                        <%@include file="/pages/ce/includes/include_obrigatorio.jsp" %>
                                    </c:if>
                                    <h:outputText value="#{msg_aplic.prt_Asterisco}" rendered="#{ClienteControle.configuracaoSistema.nomeOb}"/>
                                    <h:outputText value="#{msg_aplic.prt_Pessoa_nome}:" />
                                </h:panelGroup>
                                <h:panelGroup>
                                    <h:inputText  id="nome"  size="50" maxlength="80" onblur="blurinput(this);"  onfocus="focusinput(this);"   styleClass="form" value="#{ClienteControle.pessoaVO.nome}" />
                                    <h:message for="nome" styleClass="mensagemDetalhada"/>
                                </h:panelGroup>

                                <h:panelGroup>
                                    <h:outputText value="#{msg_aplic.prt_Asterisco}" rendered="#{ClienteControle.configuracaoSistema.nomeRegistroOb}"/>
                                    <h:outputText value="#{msg_aplic.prt_Pessoa_nome_registro}" />
                                </h:panelGroup>
                                <h:panelGroup>
                                    <h:inputText  id="nomeRegistro"  size="50" maxlength="80" onblur="blurinput(this);"  onfocus="focusinput(this);"   styleClass="form" value="#{ClienteControle.pessoaVO.nomeRegistro}" />
                                    <h:message for="nome" styleClass="mensagemDetalhada"/>
                                </h:panelGroup>

                                <h:panelGroup rendered="#{ClienteControle.clienteVO.pessoa.pessoaFisica}">
                                    <h:outputText value="#{msg_aplic.prt_Asterisco}" rendered="#{ClienteControle.configuracaoSistema.dataNascOb}"/>
                                    <h:outputText    value="#{msg_aplic.prt_Pessoa_dataNasc}:" />
                                </h:panelGroup>
                                <h:panelGroup rendered="#{ClienteControle.clienteVO.pessoa.pessoaFisica && !ClienteControle.configuracaoSistema.utilizarFormatoMMDDYYYDtNascimento}">
                                    <rich:calendar id="dataNasc"
                                                   value="#{ClienteControle.pessoaVO.dataNasc}"
                                                   inputSize="10"
                                                   inputClass="form"
                                                   oninputblur="blurinput(this);"
                                                   oninputfocus="focusinput(this);"
                                                   datePattern="dd/MM/yyyy"
                                                   enableManualInput="true"
                                                   zindex="2"
                                                   showWeeksBar="false">
                                        <a4j:support event="onchanged"
                                                     action="#{ClienteControle.validarIdade}"
                                                     reRender="form,panelMensagemErro,panelDadosPessoais"/>
                                        <a4j:support event="oninputchange"
                                                     action="#{ClienteControle.validarIdade}"
                                                     reRender="form,panelMensagemErro,panelDadosPessoais"/>
                                    </rich:calendar>

                                    <h:outputText value=" **" rendered="#{ClienteControle.configuracaoSistema.dataNascPendente}"/>

                                    <a4j:commandLink id="dataNascCommandLink" reRender="panelExistePessoa , panelExisteCliente " action="#{ClienteControle.consultarPessoaJaCadastrada}" style="display: none" />
                                    <rich:jQuery id="mskData" selector=".rich-calendar-input" timing="onload" query="mask('99/99/9999')" />
                                    <h:message for="dataNasc"  styleClass="mensagemDetalhada"/>
                                </h:panelGroup>
                                <h:panelGroup rendered="#{ClienteControle.clienteVO.pessoa.pessoaFisica && ClienteControle.configuracaoSistema.utilizarFormatoMMDDYYYDtNascimento}">
                                    <rich:calendar id="dataNascMMDDYYYY"
                                                   value="#{ClienteControle.pessoaVO.dataNasc}"
                                                   inputSize="10"
                                                   inputClass="form"
                                                   oninputblur="blurinput(this);"
                                                   oninputfocus="focusinput(this);"
                                                   datePattern="MM/dd/yyyy"
                                                   enableManualInput="true"
                                                   zindex="2"
                                                   showWeeksBar="false">
                                        <a4j:support event="onchanged"
                                                     action="#{ClienteControle.validarIdade}"
                                                     reRender="form,panelMensagemErro,panelDadosPessoais"/>
                                        <a4j:support event="oninputchange"
                                                     action="#{ClienteControle.validarIdade}"
                                                     reRender="form,panelMensagemErro,panelDadosPessoais"/>
                                    </rich:calendar>

                                    <h:outputText value=" **" rendered="#{ClienteControle.configuracaoSistema.dataNascPendente}"/>

                                    <a4j:commandLink id="dataNascCommandLinkMMDDYYYY" reRender="panelExistePessoa , panelExisteCliente " action="#{ClienteControle.consultarPessoaJaCadastrada}" style="display: none" />
                                    <rich:jQuery id="mskDataMMDDYYYY" selector=".rich-calendar-input" timing="onload" query="mask('99/99/9999')" />
                                    <h:message for="dataNascMMDDYYYY"  styleClass="mensagemDetalhada"/>
                                </h:panelGroup>
                                <h:panelGroup rendered="#{ClienteControle.clienteVO.pessoa.pessoaFisica}">
                                    <h:outputText value="#{msg_aplic.prt_Asterisco}" rendered="#{ClienteControle.configuracaoSistema.nomePaiOb}"/>
                                    <h:outputText    value="#{msg_aplic.prt_Pessoa_nomePai_responsavel}" />
                                </h:panelGroup>
                                <h:panelGroup rendered="#{ClienteControle.clienteVO.pessoa.pessoaFisica}">
                                    <h:inputText  id="nomePai" size="50" maxlength="50" onblur="blurinput(this);"  onfocus="focusinput(this);"   styleClass="form" value="#{ClienteControle.pessoaVO.nomePai}" />
                                    <h:outputText value=" ** " rendered="#{ClienteControle.configuracaoSistema.nomePaiPendente}"/>
                                </h:panelGroup>

                                <h:panelGroup rendered="#{ClienteControle.clienteVO.pessoa.pessoaFisica}">
                                    <h:outputText rendered="#{ClienteControle.configuracaoSistema.cpfPaiOb}"
                                                  value="#{msg_aplic.prt_Asterisco}"/>
                                    <h:outputText value="#{ClienteControle.displayIdentificadorFront[0]} do Pai: " />
                                </h:panelGroup>
                                <c:if test="${!ClienteControle.configuracaoSistema.usarSistemaInternacional}">
                                    <h:panelGroup rendered="#{ClienteControle.clienteVO.pessoa.pessoaFisica}">
                                        <h:inputText id="CPFPai" size="10" maxlength="14" onblur="blurinput(this);"
                                                     onfocus="focusinput(this);"
                                                     onkeypress="return mascara(this.form, this.id, '999.999.999-99', event);"
                                                     styleClass="form tamanhoInputPequeno"
                                                     value="#{ClienteControle.pessoaVO.cpfPai}"/>
                                        <h:outputText rendered="#{ClienteControle.configuracaoSistema.cpfPaiApresentar}"
                                                      value=" ** "/>
                                    </h:panelGroup>

                                </c:if>

                                <c:if test="${ClienteControle.configuracaoSistema.usarSistemaInternacional}">
                                    <h:panelGroup rendered="#{ClienteControle.clienteVO.pessoa.pessoaFisica}">
                                        <h:inputText id="CPFPaiInternacioanl" size="10" maxlength="14" onblur="blurinput(this);"
                                                     onfocus="focusinput(this);"
                                                     styleClass="form tamanhoInputPequeno"
                                                     value="#{ClienteControle.pessoaVO.cpfPai}"/>
                                        <h:outputText rendered="#{ClienteControle.configuracaoSistema.cpfPaiApresentar}"
                                                      value=" ** "/>
                                    </h:panelGroup>
                                </c:if>

                                <h:panelGroup rendered="#{ClienteControle.clienteVO.pessoa.pessoaFisica}">
                                    <h:outputText value="#{msg_aplic.prt_Asterisco}" rendered="#{ClienteControle.configuracaoSistema.rgPaiOb}"/>
                                    <h:outputText value="#{msg_aplic.prt_Pessoa_RgPai_responsavel}" />
                                </h:panelGroup>
                                <h:panelGroup rendered="#{ClienteControle.clienteVO.pessoa.pessoaFisica}">
                                    <h:inputText  id="rgPai" size="10" maxlength="20" onblur="blurinput(this);"  onfocus="focusinput(this);"   styleClass="form" value="#{ClienteControle.pessoaVO.rgPai}" />
                                    <h:outputText value=" ** " rendered="#{ClienteControle.configuracaoSistema.rgPaiApresentar}"/>
                                </h:panelGroup>

                                <h:panelGroup rendered="#{ClienteControle.clienteVO.pessoa.pessoaFisica}">
                                    <h:outputText value="Email do Pai: " />
                                </h:panelGroup>

                                <h:panelGroup rendered="#{ClienteControle.clienteVO.pessoa.pessoaFisica}">
                                    <h:inputText  id="emailResponsavelPai" size="50" maxlength="50" onblur="atualizarResponsavelPai(this.value);" styleClass="form" onfocus="focusinput(this);" value="#{ClienteControle.pessoaVO.emailPai}"/>
                                </h:panelGroup>

                                <h:panelGroup rendered="#{ClienteControle.clienteVO.pessoa.pessoaFisica}">
                                    <h:outputText value="#{msg_aplic.prt_Asterisco}" rendered="#{ClienteControle.configuracaoSistema.nomeMaeOb}"/>
                                    <h:outputText    value="#{msg_aplic.prt_Pessoa_nomeMae}:" />
                                </h:panelGroup>
                                <h:panelGroup rendered="#{ClienteControle.clienteVO.pessoa.pessoaFisica}">
                                    <h:inputText  id="nomeMae" size="50" maxlength="50" onblur="blurinput(this);"  onfocus="focusinput(this);"   styleClass="form" value="#{ClienteControle.pessoaVO.nomeMae}" />
                                    <h:outputText value=" ** " rendered="#{ClienteControle.configuracaoSistema.nomeMaePendente}"/>
                                </h:panelGroup>

                                <h:panelGroup rendered="#{ClienteControle.clienteVO.pessoa.pessoaFisica}">
                                    <h:outputText rendered="#{ClienteControle.configuracaoSistema.cpfMaeOb}"
                                                  value="#{msg_aplic.prt_Asterisco}"/>
                                    <h:outputText value="#{ClienteControle.displayIdentificadorFront[0]} da Mãe: " />
                                </h:panelGroup>

                                <c:if test="${!ClienteControle.configuracaoSistema.usarSistemaInternacional}">
                                    <h:panelGroup rendered="#{ClienteControle.clienteVO.pessoa.pessoaFisica}">
                                        <h:inputText id="CPFMae" size="10" maxlength="14" onblur="blurinput(this);" onfocus="focusinput(this);"
                                                     onkeypress="return mascara(this.form, this.id, '999.999.999-99', event);"
                                                     styleClass="form tamanhoInputPequeno" value="#{ClienteControle.pessoaVO.cpfMae}"/>
                                        <h:outputText rendered="#{ClienteControle.configuracaoSistema.cpfMaeApresentar}"
                                                      value=" ** " />
                                    </h:panelGroup>
                                </c:if>

                                <c:if test="${ClienteControle.configuracaoSistema.usarSistemaInternacional}">
                                    <h:panelGroup rendered="#{ClienteControle.clienteVO.pessoa.pessoaFisica}">
                                        <h:inputText id="CPFMaeInternacional" size="10" maxlength="14" onblur="blurinput(this);" onfocus="focusinput(this);"
                                                     styleClass="form tamanhoInputPequeno" value="#{ClienteControle.pessoaVO.cpfMae}"/>
                                        <h:outputText rendered="#{ClienteControle.configuracaoSistema.cpfMaeApresentar}"
                                                      value=" ** " />
                                    </h:panelGroup>
                                </c:if>

                                <h:panelGroup rendered="#{ClienteControle.clienteVO.pessoa.pessoaFisica}">
                                    <h:outputText value="#{msg_aplic.prt_Asterisco}" rendered="#{ClienteControle.configuracaoSistema.rgMaeOb}"/>
                                    <h:outputText value="#{msg_aplic.prt_Pessoa_RgMae_responsavel}" />
                                </h:panelGroup>
                                <h:panelGroup rendered="#{ClienteControle.clienteVO.pessoa.pessoaFisica}">
                                    <h:inputText  id="rgMae" size="10" maxlength="20" onblur="blurinput(this);"  onfocus="focusinput(this);"   styleClass="form" value="#{ClienteControle.pessoaVO.rgMae}" />
                                    <h:outputText value=" ** " rendered="#{ClienteControle.configuracaoSistema.rgMaeApresentar}"/>
                                </h:panelGroup>

                                <h:panelGroup rendered="#{ClienteControle.clienteVO.pessoa.pessoaFisica}">
                                    <h:outputText value="Email da Mãe: " />
                                </h:panelGroup>

                                <h:panelGroup rendered="#{ClienteControle.clienteVO.pessoa.pessoaFisica}">
                                    <h:inputText  id="emailResponsavelMae" size="50" maxlength="50" onblur="atualizarResponsavelMae(this.value);" styleClass="form" onfocus="focusinput(this);" value="#{ClienteControle.pessoaVO.emailMae}"/>
                                </h:panelGroup>

                                <h:panelGroup rendered="#{ClienteControle.clienteVO.pessoa.pessoaFisica}">
                                    <h:outputText value="Data de Nascimento do Responsável:" />
                                </h:panelGroup>
                                <h:panelGroup rendered="#{ClienteControle.clienteVO.pessoa.pessoaFisica}">
                                    <rich:calendar id="dataNascResponsavel"
                                                   value="#{ClienteControle.pessoaVO.dataNascimentoResponsavel}"
                                                   inputSize="10"
                                                   inputClass="form"
                                                   oninputblur="blurinput(this);"
                                                   oninputfocus="focusinput(this);"
                                                   datePattern="dd/MM/yyyy"
                                                   enableManualInput="true"
                                                   zindex="2"
                                                   showWeeksBar="false"/>
                                </h:panelGroup>

                                <h:outputText value="#{msg_aplic.prt_Pessoa_EmitirNotaNomeMae}"
                                              id="labelEmitirNotaNomeMae"
                                              rendered="#{ClienteControle.configuracaoSistema.usarNomeResponsavelNota && ClienteControle.clienteVO.pessoa.pessoaFisica && ClienteControle.clienteVO.pessoaResponsavel.codigo == 0 && ClienteControle.apresentarOpcaoResponsavelNota}"/>
                                <h:panelGroup layout="block"
                                              id="panelEmitirNotaNomeMae"
                                              rendered="#{ClienteControle.configuracaoSistema.usarNomeResponsavelNota && ClienteControle.clienteVO.pessoa.pessoaFisica && ClienteControle.clienteVO.pessoaResponsavel.codigo == 0 && ClienteControle.apresentarOpcaoResponsavelNota}">
                                    <h:selectBooleanCheckbox id="emitirNotaNomeMae"
                                                             styleClass="form"
                                                             value="#{ClienteControle.pessoaVO.emitirNotaNomeMae}"/>

                                    <rich:toolTip direction="bottom-right"
                                                  for="labelEmitirNotaNomeMae"
                                                  value="Ao desmarcar essa configuração a nota será emitida no nome do pai, caso o aluno seja menor de idade."/>

                                    <rich:toolTip direction="bottom-right"
                                                  for="emitirNotaNomeMae"
                                                  value="Ao desmarcar essa configuração a nota será emitida no nome do pai, caso o aluno seja menor de idade."/>
                                </h:panelGroup>
                                <h:panelGroup>
                                    <h:outputText value="#{msg_aplic.prt_Asterisco}" rendered="#{ClienteControle.configuracaoSistema.contatoEmergenciaOb}"/>
                                    <h:outputText value="#{msg_aplic.prt_contato_emergencia}"/>
                                </h:panelGroup>
                                <h:inputText id="nomeContatoEmergencia"
                                             size="13"
                                             maxlength="30"
                                             onfocus="focusin(this);"
                                             styleClass="form"
                                             value="#{ClienteControle.clienteVO.pessoa.contatoEmergencia}"/>
                                <h:panelGroup>
                                    <h:outputText value="#{msg_aplic.prt_Asterisco}" rendered="#{ClienteControle.configuracaoSistema.telefoneEmergenciaOb}"/>
                                    <h:outputText value="#{msg_aplic.prt_Telefone_numero_emergencia} "/>
                                </h:panelGroup>
                                <c:if test="${!ClienteControle.configuracaoSistema.usarSistemaInternacional}">
                                    <h:inputText id="numeroTelefoneClienteEmergencia"
                                                 size="13"
                                                 maxlength="13"
                                                 onchange="return validar_Telefone(this.id);"
                                                 onblur="blurinput(this);"
                                                 onkeypress="return mascara(this.form, this.id , '(99)999999999', event);"
                                                 onfocus="focusinput(this);"
                                                 styleClass = "form"
                                                 value="#{ClienteControle.clienteVO.pessoa.telefoneEmergencia}"/>
                                </c:if>

                                <c:if test="${ClienteControle.configuracaoSistema.usarSistemaInternacional}">
                                    <h:inputText id="numeroTelefoneClienteEmergenciaInternacional"
                                                 size="13"
                                                 maxlength="13"
                                                 onblur="blurinput(this);"
                                                 onfocus="focusinput(this);"
                                                 styleClass = "form"
                                                 value="#{ClienteControle.clienteVO.pessoa.telefoneEmergencia}"/>
                                </c:if>

                                <h:outputText value="Emitir nota no nome de terceiro:"/>
                                <h:selectBooleanCheckbox
                                        value="#{ClienteControle.clienteVO.pessoa.emitirNomeTerceiro}"
                                        disabled="#{ClienteControle.clienteVO.pessoa.emitirNotaNomeAluno}">
                                    <a4j:support event="onclick" reRender="form:panelDadosPessoais"/>
                                </h:selectBooleanCheckbox>
                                <c:if test="${ClienteControle.clienteVO.pessoa.emitirNomeTerceiro}">
                                    <h:outputText value="Nome Terceiro:"/>
                                    <h:inputText id="nomeTerceiro" size="50" maxlength="50" styleClass="form"
                                                 value="#{ClienteControle.clienteVO.pessoa.nomeTerceiro}"/>
                                    <h:outputText
                                            value="#{ClienteControle.displayIdentificadorFront[0]}/#{ClienteControle.displayIdentificadorFront[2]} Terceiro:"/>
                                    <h:inputText id="cpfCNPJTerceiro" size="20" maxlength="20" styleClass="form"
                                                 value="#{ClienteControle.clienteVO.pessoa.cpfCNPJTerceiro}"/>
                                    <h:outputText value="#{msg_aplic.prt_Empresa_label_inscEstadual_Terceiro}"/>
                                    <h:inputText id="inscEstadualTerceiro"
                                                 size="20"
                                                 maxlength="20"
                                                 onblur="blurinput(this);"
                                                 onfocus="focusinput(this);"
                                                 styleClass="form"
                                                 value="#{ClienteControle.clienteVO.pessoa.inscEstadualTerceiro}">
                                        <h:message for="inscEstadualTerceiro" styleClass="mensagemDetalhada"/>
                                    </h:inputText>
                                    <h:outputText value="#{msg_aplic.prt_Pessoa_cfdf_Terceiro}"/>
                                    <h:inputText id="cfdfTerceiro" size="20" maxlength="20"
                                                 onblur="blurinput(this);"
                                                 onfocus="focusinput(this);" styleClass="form"
                                                 value="#{ClienteControle.pessoaVO.cfdfTerceiro}"/>
                                </c:if>

                                <h:outputText value="Emitir nota no nome do aluno:"/>
                                <h:selectBooleanCheckbox
                                        value="#{ClienteControle.clienteVO.pessoa.emitirNotaNomeAluno}"
                                        disabled="#{ClienteControle.clienteVO.pessoa.emitirNomeTerceiro}">
                                    <a4j:support event="onclick" reRender="form:panelDadosPessoais"/>
                                </h:selectBooleanCheckbox>

                                <h:outputText styleClass="tooltipster"
                                              value="Utilizar responsável do cliente para pagamentos:"
                                              title="#{ClienteControle.utilizarRespAlunoPagamentoTitle}"/>
                                <h:selectBooleanCheckbox id="utilizarResponsavelPagamento" styleClass="tooltipster"
                                                         title="#{ClienteControle.utilizarRespAlunoPagamentoTitle}"
                                                         value="#{ClienteControle.clienteVO.utilizarResponsavelPagamento}"/>

                                <h:panelGroup rendered="#{ClienteControle.clienteVO.pessoa.pessoaFisica}">
                                    <h:outputText value="#{msg_aplic.prt_Asterisco}" rendered="#{ClienteControle.configuracaoSistema.cfpOb}"/>
                                    <h:outputText    value="#{ClienteControle.displayIdentificadorFront[0]}:" />
                                </h:panelGroup>
                                <c:if test="${!ClienteControle.configuracaoSistema.usarSistemaInternacional}">
                                    <h:panelGroup id="cpfMsk" rendered="#{ClienteControle.clienteVO.pessoa.pessoaFisica}">
                                        <h:panelGroup id="cpfAluno" rendered="#{ClienteControle.clienteVO.pessoaResponsavel.codigo == 0}">
                                            <h:inputText  id="cpf" size="14" maxlength="14" styleClass="form" value="#{ClienteControle.pessoaVO.cfp}" onblur="blurinput(this);"  onfocus="focusinput(this);" onkeypress="return mascara(this.form, this.id, '999.999.999-99', event);"/>
                                            <h:outputText value=" ** " rendered="#{ClienteControle.configuracaoSistema.cpfPendente}"/>
                                        </h:panelGroup>
                                        <h:panelGroup id="cpfREsponsavel" rendered="#{ClienteControle.clienteVO.pessoaResponsavel.codigo != 0}">
                                            <h:inputText disabled="true" id="cpfRes" size="14" maxlength="14" onblur="blurinput(this);"  onfocus="focusinput(this);"
                                                         styleClass="form" value="#{ClienteControle.clienteVO.pessoaResponsavel.cfp}" onkeypress="return mascara(this.form, this.id, '999.999.999-99', event);"/>
                                            <rich:spacer width="20"/>
                                            <h:outputText value="#{ClienteControle.clienteVO.pessoaResponsavel.nome}" styleClass="text" style="font-weight: bold;"/>
                                            <rich:spacer width="20"/>
                                            <a4j:commandButton id="btnRemoverResp" action="#{ClienteControle.removerResponsavelCPF}" value="Remover Responsável"
                                                               reRender="cpfMsk,panelDadosPessoais" title="remover Responsavel" accesskey="2" styleClass="botoes"/>

                                            <rich:toolTip direction="auto" for="cpfREsponsavel" rendered="#{ClienteControle.apresentarTooltipCPFResponsavel}"
                                                          value="Este será o responsável pelas Notas Fiscais do cliente caso o mesmo seja menor de 18 anos.<br/>Se o aluno que é menor de idade fizer 18 anos, sua nota fiscal terá retorno com mensagem de erro informando que falta o #{ClienteControle.displayIdentificadorFront[0]}."/>
                                        </h:panelGroup>
                                    </h:panelGroup>
                                </c:if>

                                <c:if test="${ClienteControle.configuracaoSistema.usarSistemaInternacional}">
                                    <h:panelGroup id="cpfMskInternacional" rendered="#{ClienteControle.clienteVO.pessoa.pessoaFisica}">
                                        <h:panelGroup id="cpfAlunoInternacional" rendered="#{ClienteControle.clienteVO.pessoaResponsavel.codigo == 0}">
                                            <h:inputText  id="cpfInternacional" size="14" maxlength="20" onblur="blurinput(this);"  onfocus="focusinput(this);" styleClass="form" value="#{ClienteControle.pessoaVO.cfp}" />
                                            <h:outputText value=" ** " rendered="#{ClienteControle.configuracaoSistema.cpfPendente}"/>
                                        </h:panelGroup>
                                        <h:panelGroup id="cpfREsponsavelInternacional" rendered="#{ClienteControle.clienteVO.pessoaResponsavel.codigo != 0}">
                                            <h:inputText disabled="true" id="cpfResInternacional" size="14" maxlength="20" onblur="blurinput(this);" onfocus="focusinput(this);"
                                                         styleClass="form" value="#{ClienteControle.clienteVO.pessoaResponsavel.cfp}">
                                            </h:inputText>
                                            <rich:spacer width="20"/>
                                            <h:outputText value="#{ClienteControle.clienteVO.pessoaResponsavel.nome}" styleClass="text" style="font-weight: bold;"/>
                                            <rich:spacer width="20"/>
                                            <a4j:commandButton id="btnRemoverRespInternacional" action="#{ClienteControle.removerResponsavelCPF}" value="Remover Responsável"
                                                               reRender="cpfMsk,panelDadosPessoais" title="remover Responsavel" accesskey="2" styleClass="botoes"/>

                                            <rich:toolTip direction="auto" for="cpfREsponsavelInternacional" rendered="#{ClienteControle.apresentarTooltipCPFResponsavel}"
                                                          value="Este será o responsável pelas Notas Fiscais do cliente caso o mesmo seja menor de 18 anos.<br/>Se o aluno que é menor de idade fizer 18 anos, sua nota fiscal terá retorno com mensagem de erro informando que falta o #{ClienteControle.displayIdentificadorFront[0]}."/>
                                        </h:panelGroup>
                                    </h:panelGroup>
                                </c:if>

                                <c:if test="${ClienteControle.configuracaoSistema.utilizarServicoSesiSC && ClienteControle.configuracaoSistema.apresentarCnpjSesi}">
                                    <h:panelGroup>
                                        <h:outputText value="#{msg_aplic.prt_Asterisco}"
                                                      rendered="#{!ClienteControle.configuracaoSistema.pendenteCnpjSesi and
                                                       ClienteControle.configuracaoSistema.objCnpjSesi}"/>
                                        <h:outputText styleClass="tituloCampos" value="CNPJ Sesi Indústria: "/>
                                    </h:panelGroup>
                                </c:if>
                                <c:if test="${ClienteControle.configuracaoSistema.utilizarServicoSesiSC && ClienteControle.configuracaoSistema.apresentarCnpjSesi}">
                                    <h:panelGroup>
                                        <h:inputText id="CNPJClienteSesi" size="18" maxlength="18"
                                                     onkeypress="return mascara(this.form, 'form:CNPJClienteSesi', '99.999.999/9999-99', event);"
                                                     onblur="blurinput(this);" onfocus="focusinput(this);"
                                                     styleClass="form"
                                                     value="#{ClienteControle.clienteVO.pessoa.cnpjSesi}"/>
                                        <h:outputText value="#{msg_aplic.prt_Asterisco}#{msg_aplic.prt_Asterisco}"
                                                      rendered="#{ClienteControle.configuracaoSistema.pendenteCnpjSesi}"/>
                                        <h:message for="CNPJClienteSesi" styleClass="mensagemDetalhada"/>
                                    </h:panelGroup>
                                </c:if>

                                <h:panelGroup rendered="#{ClienteControle.clienteVO.pessoa.pessoaFisica}">
                                    <h:outputText value="RNE:" />
                                </h:panelGroup>
                                <h:panelGroup rendered="#{ClienteControle.clienteVO.pessoa.pessoaFisica}">
                                    <h:inputText  id="rne" size="20" maxlength="32" onblur="blurinput(this);"  onfocus="focusinput(this);"   styleClass="form" value="#{ClienteControle.pessoaVO.rne}" />
                                    <h:outputText rendered="#{ClienteControle.configuracaoSistema.cfpOb}"/>
                                </h:panelGroup>

                                <h:panelGroup rendered="#{ClienteControle.clienteVO.pessoa.pessoaFisica}">
                                    <h:outputText value="Passaporte:" />
                                </h:panelGroup>
                                <h:panelGroup rendered="#{ClienteControle.clienteVO.pessoa.pessoaFisica}">
                                    <h:inputText  id="passaporte" size="20" maxlength="32" onblur="blurinput(this);"  onfocus="focusinput(this);"   styleClass="form" value="#{ClienteControle.pessoaVO.passaporte}" />
                                    <h:outputText rendered="#{ClienteControle.configuracaoSistema.cfpOb}"/>
                                </h:panelGroup>

                                <h:outputText value="Nome Responsável Empresa:" rendered="#{ClienteControle.clienteVO.pessoa.pessoaJuridica}"/>
                                <h:panelGroup rendered="#{ClienteControle.clienteVO.pessoa.pessoaJuridica}">
                                    <h:inputText  id="nomeResponsavelEmpresa" size="50" maxlength="50" onblur="blurinput(this);"  onfocus="focusinput(this);" styleClass="form" value="#{ClienteControle.pessoaVO.nomeResponsavelEmpresa}" />
                                    <h:message for="nomeResponsavelEmpresa" styleClass="mensagemDetalhada"/>
                                </h:panelGroup>

                                <h:outputText value="CPF Responsável Empresa:" rendered="#{ClienteControle.clienteVO.pessoa.pessoaJuridica}"/>
                                <h:panelGroup rendered="#{ClienteControle.clienteVO.pessoa.pessoaJuridica}">
                                    <h:inputText  id="cpfResponsavelEmpresa" size="14" maxlength="14" styleClass="form" value="#{ClienteControle.pessoaVO.cpfResponsavelEmpresa}" onkeypress="return mascara(this.form, this.id, '999.999.999-99', event);"/>
                                </h:panelGroup>


                                <h:outputText value="#{ClienteControle.displayIdentificadorFront[2]}:"
                                              rendered="#{ClienteControle.clienteVO.pessoa.pessoaJuridica}"/>
                                <h:panelGroup rendered="#{ClienteControle.clienteVO.pessoa.pessoaJuridica}">
                                    <c:if test="${!ClienteControle.configuracaoSistema.usarSistemaInternacional}">
                                        <h:inputText id="CNPJ" size="18" maxlength="18"
                                                     onkeypress="return mascara(this.form, 'form:CNPJ', '99.999.999/9999-99', event);"
                                                     onblur="blurinput(this);" onfocus="focusinput(this);"
                                                     styleClass="form"
                                                     value="#{ClienteControle.clienteVO.pessoa.cnpj}"/>
                                    </c:if>
                                    <c:if test="${ClienteControle.configuracaoSistema.usarSistemaInternacional}">
                                        <h:inputText id="CNPJInternacional" size="18" maxlength="20"
                                                     onblur="blurinput(this);" onfocus="focusinput(this);"
                                                     styleClass="form"
                                                     value="#{ClienteControle.clienteVO.pessoa.cnpj}"/>
                                    </c:if>
                                    <h:message for="CNPJ" styleClass="mensagemDetalhada"/>
                                </h:panelGroup>

                                <h:outputText value="#{msg_aplic.prt_Empresa_inscEstadual}" rendered="#{ClienteControle.clienteVO.pessoa.pessoaJuridica}"/>
                                <h:panelGroup rendered="#{ClienteControle.clienteVO.pessoa.pessoaJuridica}">
                                    <h:inputText  id="inscEstadual" size="20" maxlength="20" onblur="blurinput(this);"  onfocus="focusinput(this);" styleClass="form" value="#{ClienteControle.clienteVO.pessoa.inscEstadual}" />
                                    <h:message for="inscEstadual" styleClass="mensagemDetalhada"/>
                                </h:panelGroup>

                                <h:outputText value="Inscrição Municipal:" rendered="#{ClienteControle.clienteVO.pessoa.pessoaJuridica}"/>
                                <h:panelGroup rendered="#{ClienteControle.clienteVO.pessoa.pessoaJuridica}">
                                    <h:inputText  id="inscMunicipal" size="20" maxlength="20" onblur="blurinput(this);"  onfocus="focusinput(this);" styleClass="form" value="#{ClienteControle.clienteVO.pessoa.inscMunicipal}"/>
                                    <h:message for="inscMunicipal" styleClass="mensagemDetalhada"/>
                                </h:panelGroup>

                                <h:panelGroup rendered="#{ClienteControle.clienteVO.pessoa.pessoaJuridica}">
                                    <h:outputText value="#{msg_aplic.prt_Pessoa_cfdf}" />
                                </h:panelGroup>
                                <h:panelGroup rendered="#{ClienteControle.clienteVO.pessoa.pessoaJuridica}">
                                    <h:inputText  id="cfdf" size="20" maxlength="20" onblur="blurinput(this);"  onfocus="focusinput(this);"   styleClass="form" value="#{ClienteControle.pessoaVO.cfdf}" />
                                </h:panelGroup>


                                <h:panelGroup rendered="#{ClienteControle.clienteVO.pessoa.pessoaFisica}">
                                    <h:outputText value="#{msg_aplic.prt_Asterisco}" rendered="#{ClienteControle.configuracaoSistema.rgOb}"/>
                                    <h:outputText value="#{ClienteControle.displayIdentificadorFront[1]}:" />
                                </h:panelGroup>

                                <c:if test="${!ClienteControle.configuracaoSistema.usarSistemaInternacional}">
                                    <h:panelGroup rendered="#{ClienteControle.clienteVO.pessoa.pessoaFisica}">
                                        <h:inputText  id="rg" size="20" maxlength="20" onblur="blurinput(this);"  onfocus="focusinput(this);"   styleClass="form" value="#{ClienteControle.pessoaVO.rg}" />
                                        <h:outputText value=" **" rendered="#{ClienteControle.configuracaoSistema.rgPendente}"/>
                                    </h:panelGroup>

                                    <h:panelGroup rendered="#{ClienteControle.clienteVO.pessoa.pessoaFisica}">
                                        <h:outputText value="#{msg_aplic.prt_Asterisco}" rendered="#{ClienteControle.configuracaoSistema.rgOb}"/>
                                        <h:outputText value="#{msg_aplic.prt_Pessoa_rgOrgao}" />
                                    </h:panelGroup>
                                    <h:panelGroup rendered="#{ClienteControle.clienteVO.pessoa.pessoaFisica}">
                                        <h:inputText  id="rgOrgao" size="10" maxlength="10" onblur="blurinput(this);"  onfocus="focusinput(this);"   styleClass="form" value="#{ClienteControle.pessoaVO.rgOrgao}" />
                                        <h:outputText value=" **" rendered="#{ClienteControle.configuracaoSistema.rgPendente}"/>
                                    </h:panelGroup>

                                    <h:panelGroup rendered="#{ClienteControle.clienteVO.pessoa.pessoaFisica}">
                                        <h:outputText value="#{msg_aplic.prt_Asterisco}" rendered="#{ClienteControle.configuracaoSistema.rgOb}"/>
                                        <h:outputText value="#{msg_aplic.prt_Pessoa_rgUf}" />
                                    </h:panelGroup>
                                    <h:panelGroup rendered="#{ClienteControle.clienteVO.pessoa.pessoaFisica}">
                                        <h:selectOneMenu  id="rgUf" onblur="blurinput(this);"  onfocus="focusinput(this);"   styleClass="form" value="#{ClienteControle.pessoaVO.rgUf}" >
                                            <f:selectItems  value="#{ClienteControle.listaSelectItemRgUfPessoa}" />
                                        </h:selectOneMenu>
                                        <h:outputText value=" **" rendered="#{ClienteControle.configuracaoSistema.rgPendente}"/>
                                    </h:panelGroup>
                                </c:if>

                                <c:if test="${ClienteControle.configuracaoSistema.usarSistemaInternacional}">
                                    <h:panelGroup rendered="#{ClienteControle.clienteVO.pessoa.pessoaFisica}">
                                        <h:inputText  id="rgInternacional" size="20" maxlength="20" onblur="blurinput(this);"  onfocus="focusinput(this);"   styleClass="form" value="#{ClienteControle.pessoaVO.rg}" />
                                        <h:outputText value=" **" rendered="#{ClienteControle.configuracaoSistema.rgPendente}"/>
                                    </h:panelGroup>
                                </c:if>

                                <h:panelGroup>
                                    <h:outputText value="#{msg_aplic.prt_Asterisco}" rendered="#{ClienteControle.configuracaoSistema.paisOb}"/>
                                    <h:outputText    value="#{msg_aplic.prt_Pessoa_pais}" />
                                </h:panelGroup>
                                <h:panelGroup>
                                    <h:selectOneMenu  id="pais" onblur="blurinput(this);"   onfocus="focusinput(this);"   styleClass="form" value="#{ClienteControle.pessoaVO.pais.codigo}" >
                                        <a4j:support  event="onchange" reRender="estado,cidade" action="#{ClienteControle.montarListaSelectItemEstado}"/>
                                        <f:selectItems  value="#{ClienteControle.listaSelectItemPais}" />
                                    </h:selectOneMenu>
                                    <a4j:commandButton id="atualizar_pais" action="#{ClienteControle.montarListaSelectItemPais}" image="imagens/atualizar.png" immediate="true" ajaxSingle="true" reRender="form:pais"/>
                                    <h:message for="pais" styleClass="mensagemDetalhada"/>
                                    <h:outputText value=" **" rendered="#{ClienteControle.configuracaoSistema.paisPendente}"/>
                                </h:panelGroup>

                                <h:panelGroup>
                                    <h:outputText value="#{msg_aplic.prt_Asterisco}" rendered="#{ClienteControle.configuracaoSistema.estadoOb}"/>
                                    <h:outputText    value="#{msg_aplic.prt_Pessoa_estado}" />
                                </h:panelGroup>
                                <h:panelGroup>
                                    <h:selectOneMenu id="estado" onblur="blurinput(this);"  onfocus="focusinput(this);" styleClass="form"  value="#{ClienteControle.pessoaVO.estadoVO.codigo}" >
                                        <f:selectItems value="#{ClienteControle.listaSelectItemEstado}" />
                                        <a4j:support event="onchange" reRender="cidade" action="#{ClienteControle.montarListaSelectItemCidade}"/>
                                    </h:selectOneMenu>
                                    <a4j:commandButton id="atualizar_estado" action="#{ClienteControle.montarListaSelectItemEstado}" image="imagens/atualizar.png" ajaxSingle="true" reRender="form:estado"/>
                                    <h:outputText value=" **" rendered="#{ClienteControle.configuracaoSistema.estadoPendente}"/>
                                    <h:message for="estado" styleClass="mensagemDetalhada"/>
                                </h:panelGroup>

                                <h:panelGroup>
                                    <h:outputText value="#{msg_aplic.prt_Asterisco}" rendered="#{ClienteControle.configuracaoSistema.cidadeOb}"/>
                                    <h:outputText    value="#{msg_aplic.prt_Pessoa_cidade}:" />
                                </h:panelGroup>
                                <h:panelGroup>
                                    <h:selectOneMenu  id="cidade" onblur="blurinput(this);"  onfocus="focusinput(this);"   styleClass="form" value="#{ClienteControle.pessoaVO.cidade.codigo}" >
                                        <f:selectItems  value="#{ClienteControle.listaSelectItemCidade}" />
                                    </h:selectOneMenu>
                                    <a4j:commandButton id="atualizar_cidade" action="#{ClienteControle.montarListaSelectItemCidade}" image="imagens/atualizar.png" immediate="true" ajaxSingle="true" reRender="form:cidade"/>
                                    <h:outputText value=" **" rendered="#{ClienteControle.configuracaoSistema.cidadePendente}"/>
                                    <h:message for="cidade" styleClass="mensagemDetalhada"/>
                                </h:panelGroup>

                                <h:panelGroup rendered="#{ClienteControle.clienteVO.pessoa.pessoaFisica}">
                                    <h:outputText value="#{msg_aplic.prt_Asterisco}" rendered="#{ClienteControle.configuracaoSistema.profissaoOb}"/>
                                    <h:outputText    value="#{msg_aplic.prt_Pessoa_profissao}" />
                                </h:panelGroup>
                                <h:panelGroup rendered="#{ClienteControle.clienteVO.pessoa.pessoaFisica}">
                                    <h:selectOneMenu  id="profissao" onblur="blurinput(this);"  onfocus="focusinput(this);"   styleClass="form" value="#{ClienteControle.pessoaVO.profissao.codigo}" >
                                        <f:selectItems  value="#{ClienteControle.listaSelectItemProfissao}" />
                                    </h:selectOneMenu>
                                    <a4j:commandButton id="atualizar_profissao" action="#{ClienteControle.montarListaSelectItemProfissao}" image="imagens/atualizar.png" immediate="true" ajaxSingle="true" reRender="form:profissao"/>
                                    <a4j:commandButton id="consultaDadosProfissao" action="#{ProfissaoControle.inicializarProfisaoControle}"  focus="nomeProfissao" alt="Cadastrar Profissão" reRender="formProfissao" oncomplete="Richfaces.showModalPanel('panelProfissao'),setFocus(formProfissao,'formProfissao:nomeProfissao');" image="./images/icon_add.gif" />
                                    <h:outputText value=" **" rendered="#{ClienteControle.configuracaoSistema.profissaoPendente}"/>
                                    <h:message for="profissao" styleClass="mensagemDetalhada"/>
                                </h:panelGroup>

                                <h:panelGroup rendered="#{ClienteControle.clienteVO.pessoa.pessoaFisica}">
                                    <h:outputText value="#{msg_aplic.prt_Pessoa_nacionalidade}" />
                                </h:panelGroup>
                                <h:inputText rendered="#{ClienteControle.clienteVO.pessoa.pessoaFisica}"  id="nacionalidade" size="20" maxlength="20" onblur="blurinput(this);"  onfocus="focusinput(this);"   styleClass="form" value="#{ClienteControle.pessoaVO.nacionalidade}" />

                                <h:panelGroup rendered="#{ClienteControle.clienteVO.pessoa.pessoaFisica}">
                                    <h:outputText value="#{msg_aplic.prt_Pessoa_naturalidade}:" />
                                </h:panelGroup>
                                <h:inputText rendered="#{ClienteControle.clienteVO.pessoa.pessoaFisica}"  id="naturalidade" size="20" maxlength="20" onblur="blurinput(this);"  onfocus="focusinput(this);"   styleClass="form" value="#{ClienteControle.pessoaVO.naturalidade}" />

                                <h:panelGroup rendered="#{ClienteControle.clienteVO.pessoa.pessoaFisica}">
                                    <h:outputText value="#{msg_aplic.prt_Asterisco}" rendered="#{ClienteControle.configuracaoSistema.sexoOb}"/>
                                    <h:outputText value="#{msg_aplic.prt_Pessoa_sexo}" />
                                </h:panelGroup >
                                <h:selectOneRadio rendered="#{ClienteControle.clienteVO.pessoa.pessoaFisica}" id="sexo" onblur="blurinput(this);" style="font-size: 13px;" onfocus="focusinput(this);" value="#{ClienteControle.pessoaVO.sexo}" >
                                    <f:selectItems value="#{ClienteControle.listaSelectItemSexoPessoa}" />
                                </h:selectOneRadio>

                                <h:panelGroup rendered="#{ClienteControle.clienteVO.pessoa.pessoaFisica}">
                                    <h:outputText value="#{msg_aplic.prt_Asterisco}" rendered="#{ClienteControle.configuracaoSistema.generoOb}"/>
                                    <h:outputText value="#{msg_aplic.prt_Pessoa_genero}" />
                                </h:panelGroup >
                                <h:panelGroup rendered="#{ClienteControle.clienteVO.pessoa.pessoaFisica}">
                                    <h:selectOneMenu  id="genero" onblur="blurinput(this);"  onfocus="focusinput(this);"   styleClass="form" value="#{ClienteControle.pessoaVO.genero}" >
                                        <f:selectItems  value="#{ClienteControle.listaSelectItemGeneroPessoa}" />
                                    </h:selectOneMenu>
                                </h:panelGroup>

                                <h:panelGroup rendered="#{ClienteControle.clienteVO.pessoa.pessoaFisica}">
                                    <h:outputText value="#{msg_aplic.prt_Asterisco}" rendered="#{ClienteControle.configuracaoSistema.estadoCivilOb}"/>
                                    <h:outputText value="#{msg_aplic.prt_Pessoa_estadoCivil}" />
                                </h:panelGroup>

                                <h:panelGroup rendered="#{ClienteControle.clienteVO.pessoa.pessoaFisica}">
	                                <h:selectOneMenu  id="estadoCivil"  onblur="blurinput(this);"  styleClass="form"  onfocus="focusinput(this);"  value="#{ClienteControle.pessoaVO.estadoCivil}" >
	                                    <f:selectItems  value="#{ClienteControle.listaSelectItemEstadoCivilPessoa}" />
	                                </h:selectOneMenu>
                                	<h:outputText value=" **" rendered="#{ClienteControle.configuracaoSistema.estadoCivilPendente}"/>
                                </h:panelGroup>

                                <h:panelGroup rendered="#{ClienteControle.clienteVO.pessoa.pessoaFisica}">
                                    <h:outputText value="#{msg_aplic.prt_Asterisco}" rendered="#{ClienteControle.configuracaoSistema.grauInstrucaoOb}"/>
                                    <h:outputText value="#{msg_aplic.prt_Pessoa_grauInstrucao}" />
                                </h:panelGroup>
                                <h:panelGroup rendered="#{ClienteControle.clienteVO.pessoa.pessoaFisica}">
                                    <h:selectOneMenu  id="grauIntrucao"   onblur="blurinput(this);"  onfocus="focusinput(this);"  styleClass="form" value="#{ClienteControle.pessoaVO.grauInstrucao.codigo}" >
                                        <f:selectItems  value="#{ClienteControle.listaSelectItemGrauInstrucao}" />
                                    </h:selectOneMenu>
                                    <a4j:commandButton id="atualizar_GrauDeInstrucao" action="#{ClienteControle.montarListaSelectItemGrauInstrucao}" image="imagens/atualizar.png" immediate="true" ajaxSingle="true" reRender="form:grauIntrucao"/>
                                    <h:outputText value=" **" rendered="#{ClienteControle.configuracaoSistema.grauInstrucaoPendente}"/>
                                </h:panelGroup>

                                <h:panelGroup>
                                    <h:outputText value="#{msg_aplic.prt_Asterisco}" rendered="#{ClienteControle.configuracaoSistema.webPageOb}"/>
                                    <h:outputText value="#{msg_aplic.prt_Pessoa_webPage}" />
                                </h:panelGroup>
                                <h:panelGroup>
                                    <h:inputText  id="webPage" size="50" onblur="blurinput(this);"  onfocus="focusinput(this);"  styleClass="form" maxlength="50"  value="#{ClienteControle.pessoaVO.webPage}" />
                                    <h:outputText value=" **" rendered="#{ClienteControle.configuracaoSistema.webPagePendente}"/>
                                </h:panelGroup>

                                <h:outputText
                                        rendered="#{ClienteControle.clienteVO.empresa.usarParceiroFidelidade}"
                                        value="Utiliza DOTZ:"/>
                                <h:selectBooleanCheckbox
                                        rendered="#{ClienteControle.clienteVO.empresa.usarParceiroFidelidade}"
                                        value="#{ClienteControle.clienteVO.pessoa.utilizaDotz}"/>

                                <!-- INICIO CONFIGURA��ES SESI CE -->
                                <c:if test="${ConfiguracaoSistemaControle.configuracaoSistemaVO.sesiCe}">

                                    <h:panelGroup>
                                        <h:outputText value="Necessidades Especiais: "/>
                                    </h:panelGroup>
                                    <h:panelGroup
                                            id="sesiCE_campo_necessidades_especiais">
                                        <h:selectOneMenu id="necessidadesEspeciais" onblur="blurinput(this);"
                                                         onfocus="focusinput(this);" styleClass="form"
                                                         value="#{ClienteControle.clienteVO.necessidadesEspeciaisSesiCe}">
                                            <f:selectItems  value="#{ClienteControle.listaSelectItemNecessidadesEspeciaisSesiCe}" />
                                        </h:selectOneMenu>
                                    </h:panelGroup>

                                    <h:panelGroup>
                                        <h:outputText value="Data de validade do cadastro: "/>
                                    </h:panelGroup>
                                    <h:panelGroup>
                                        <rich:calendar id="sesiCE_campo_data_validade_cadastro"
                                                       value="#{ClienteControle.clienteVO.dataValidadeCadastroSesiCe}"
                                                       jointPoint="top-left"
                                                       direction="top-right"
                                                       inputSize="10"
                                                       inputClass="form"
                                                       oninputblur="blurinput(this);"
                                                       oninputfocus="focusinput(this);"
                                                       datePattern="dd/MM/yyyy"
                                                       enableManualInput="true"
                                                       zindex="2"
                                                       showWeeksBar="false"/>
                                    </h:panelGroup>


                                    <h:panelGroup>
                                        <h:outputText value="Empresa: "/>
                                    </h:panelGroup>
                                    <h:panelGroup rendered="#{!ClienteControle.empresaLogado.habilitarCadastroEmpresaSesi}">
                                        <h:inputText id="sesiCE_campo_razao_social_empresa" size="30" maxlength="60"
                                                     styleClass="form"
                                                     value="#{ClienteControle.clienteVO.razaoSocialEmpresaSesiCe}"/>
                                    </h:panelGroup>
                                    <h:panelGroup rendered="#{ClienteControle.empresaLogado.habilitarCadastroEmpresaSesi}">
                                        <h:selectOneMenu  id="empresaFornecedor"   onblur="blurinput(this);"  onfocus="focusinput(this);"
                                                          styleClass="form" value="#{ClienteControle.clienteVO.empresaFornecedor.codigo}" >
                                            <f:selectItems  value="#{ClienteControle.listaSelectItemEmpresaFornecedor}" />
                                        </h:selectOneMenu>
                                    </h:panelGroup>

                                    <h:panelGroup>
                                        <h:outputText value="Status da matrícula: "/>
                                    </h:panelGroup>
                                    <h:panelGroup>
                                        <h:selectOneMenu id="sesiCE_campo_status_matricula" styleClass="form"
                                                         value="#{ClienteControle.clienteVO.statusMatriculaSesiCe}">
                                            <f:selectItems  value="#{ClienteControle.listaSelectItemStatusMatriculaSesiCe}" />
                                        </h:selectOneMenu>
                                    </h:panelGroup>
                                </c:if>
                                <!-- FIM CONFIGURA��ES SESI CE -->

                            </h:panelGrid>
                        </h:panelGrid>
                    </rich:tab>
                    <rich:tab id="abaDadosCliente" label="Dados do Cliente" actionListener="#{ClienteControle.abrirAbaDadosCliente}">
                        <h:panelGrid id="panelDadosCliente" columns="1" width="100%" headerClass="subordinado" columnClasses="colunaCentralizada">
                            <f:facet name="header">
                                <h:outputText value="#{msg_aplic.prt_DadosCliente_tituloForm}"/>
                            </f:facet>
                            <h:panelGrid columns="2" rowClasses="linhaImpar, linhaPar" columnClasses="classEsquerda, classDireita" width="100%">
                                <h:outputText value="#{msg_aplic.prt_Cliente_codigo}" />
                                <h:panelGroup>
                                    <h:inputText id="codigo" size="10" maxlength="10" readonly="true" styleClass="form" value="#{ClienteControle.clienteVO.codigo}" />
                                    <h:message for="codigo" styleClass="mensagemDetalhada"/>
                                </h:panelGroup>
                                <h:panelGroup>
                                    <h:outputText value="#{msg_aplic.prt_Cliente_situacao}" />
                                </h:panelGroup>
                                <h:panelGroup>
                                    <h:selectOneMenu  id="situacao"  disabled="true" onblur="blurinput(this);"  onfocus="focusinput(this);" styleClass="form" value="#{ClienteControle.clienteVO.situacao}" >
                                        <f:selectItems  value="#{ClienteControle.listaSelectItemSituacaoCliente}" />
                                    </h:selectOneMenu>
                                </h:panelGroup>
                                <h:panelGroup>
                                    <h:outputText value="#{msg_aplic.prt_Asterisco}" rendered="#{ClienteControle.configuracaoSistema.matriculaOb}"/>
                                    <h:outputText value="#{msg_aplic.prt_Cliente_matricula}" />
                                </h:panelGroup>
                                <h:panelGroup >
                                    <h:inputText  id="matricula"  readonly="true"  onblur="blurinput(this);" styleClass="form" onfocus="focusinput(this);" size="10" maxlength="10" value="#{ClienteControle.clienteVO.matricula}" >
                                        <a4j:support event="onchange" reRender="form"/>
                                    </h:inputText>
                                    <h:message for="matricula" styleClass="mensagemDetalhada"/>
                                </h:panelGroup>
                                <h:panelGroup>
                                    <h:outputText value="#{msg_aplic.prt_Asterisco}" rendered="#{ClienteControle.configuracaoSistema.categoriaOb}"/>
                                    <h:outputText    value="#{msg_aplic.prt_Cliente_categoria}" />
                                </h:panelGroup>
                                <h:panelGroup>
                                    <h:selectOneMenu  id="categoria"   onblur="blurinput(this);"  onfocus="focusinput(this);" styleClass="form"  value="#{ClienteControle.clienteVO.categoria.codigo}" >
                                        <f:selectItems  value="#{ClienteControle.listaSelectItemCategoria}" />
                                    </h:selectOneMenu>
                                    <a4j:commandButton id="atualizar_categoria" action="#{ClienteControle.montarListaSelectItemCategoria}" image="imagens/atualizar.png" immediate="true" ajaxSingle="true" reRender="form:categoria"/>
                                    <a4j:commandButton id="consultaDadosCategoria" action="#{ProfissaoControle.inicializarProfisaoControle}" alt="Cadastrar Categoria" reRender="formCategoria" oncomplete="Richfaces.showModalPanel('panelCategoria') , setFocus(formCategoria,'formCategoria:nome_Categoria');" image="./images/icon_add.gif" />
                                    <h:outputText value=" **" rendered="#{ClienteControle.configuracaoSistema.categoriaPendente}"/>
                                    <h:message for="categoria" styleClass="mensagemDetalhada"/>
                                </h:panelGroup>

                                <h:panelGroup>
                                    <h:outputText value="#{msg_aplic.prt_Cliente_codAcesso}" />
                                </h:panelGroup>
                                <h:inputText id="codAcesso" readonly="true"  size="20" onblur="blurinput(this);"  onfocus="focusinput(this);"   styleClass="form" maxlength="20"  value="#{ClienteControle.clienteVO.codAcesso}" />
                                <h:panelGroup>
                                    <h:outputText    value="#{msg_aplic.prt_Cliente_codAcessoAlternativo}" />
                                </h:panelGroup>
                                <h:inputText  id="codAcessoAlternativo" size="20" onblur="blurinput(this);"  onfocus="focusinput(this);" styleClass="form"  maxlength="25"  value="#{ClienteControle.clienteVO.codAcessoAlternativo}" />

                                <h:panelGroup>
                                    <h:outputText value="Template Digital:" />
                                </h:panelGroup>

                                <h:panelGroup layout="block" id="panelDigital" style="display: inline-flex;">
                                    <h:inputText  id="assinaturaBiometriaDigital"
                                                  size="20"
                                                  disabled="true"
                                                  styleClass="form"
                                                  value="#{ClienteControle.clienteVO.pessoa.assinaturaBiometriaDigital}" />
                                    <a4j:commandButton id="excluirAssinaturaDigital" immediate="true"
                                                       action="#{ClienteControle.confirmarExcluirAssinaturaDigital}"
                                                       oncomplete="#{ClienteControle.msgAlert}"
                                                       rendered="#{not empty ClienteControle.clienteVO.pessoa.assinaturaBiometriaDigital}"
                                                       reRender="form:panelDadosCliente,form:pnlGridMensagens,mdlMensagemGenerica"
                                                       title="Excluir template digital"
                                                       value="#{msg_bt.btn_excluir}" image="./imagens/botaoRemover.png" accesskey="7" styleClass="botoes"/>
                                </h:panelGroup>

                                <h:panelGroup>
                                    <h:outputText value="Template Facial:" />
                                </h:panelGroup>

                                <h:panelGroup layout="block" id="panelFacial" style="display: inline-flex;">
                                    <h:inputText  id="assinaturaBiometriaFacial"
                                                  size="20"
                                                  disabled="true"
                                                  styleClass="form"
                                                  value="#{ClienteControle.clienteVO.pessoa.assinaturaBiometriaFacial}" />
                                    <a4j:commandButton id="excluirAssinaturaFacial" immediate="true"
                                                       action="#{ClienteControle.confirmarExcluirAssinaturaFacial}"
                                                       oncomplete="#{ClienteControle.msgAlert}"
                                                       rendered="#{not empty ClienteControle.clienteVO.pessoa.assinaturaBiometriaFacial}"
                                                       reRender="form:panelDadosCliente,form:pnlGridMensagens,mdlMensagemGenerica"
                                                       title="Excluir template facial"
                                                       value="#{msg_bt.btn_excluir}" image="./imagens/botaoRemover.png" accesskey="7" styleClass="botoes"/>
                                </h:panelGroup>

                                <h:panelGroup>
                                    <h:outputText    value="#{msg_aplic.prt_Cliente_banco}" />
                                </h:panelGroup>
                                <h:panelGroup>
                                    <h:inputText  id="banco" size="30"   onblur="blurinput(this);"  onfocus="focusinput(this);" styleClass="form"  maxlength="30" value="#{ClienteControle.clienteVO.banco}" />
                                    <h:message for="banco" styleClass="mensagemDetalhada"/>
                                </h:panelGroup>
                                <h:panelGroup>
                                    <h:outputText    value="#{msg_aplic.prt_Cliente_agencia}" />
                                </h:panelGroup>
                                <h:panelGroup>
                                    <h:inputText  id="agencia"   onblur="blurinput(this);"  styleClass="form"  onfocus="focusinput(this);" size="20" maxlength="20"  value="#{ClienteControle.clienteVO.agencia}" />
                                    <h:message for="agencia" styleClass="mensagemDetalhada"/>
                                </h:panelGroup>
                                <h:panelGroup>
                                    <h:outputText    value="#{msg_aplic.prt_Cliente_agenciaDigito}" />
                                </h:panelGroup>
                                <h:inputText  id="agenciaDigito"  styleClass="form"  onblur="blurinput(this);"  onfocus="focusinput(this);" size="10" maxlength="10"  value="#{ClienteControle.clienteVO.agenciaDigito}" />
                                <h:panelGroup>
                                    <h:outputText    value="#{msg_aplic.prt_Cliente_conta}" />
                                </h:panelGroup>
                                <h:panelGroup>
                                    <h:inputText  id="conta"  styleClass="form"  onblur="blurinput(this);"  onfocus="focusinput(this);" size="20" maxlength="20"  value="#{ClienteControle.clienteVO.conta}" />
                                    <h:message for="conta" styleClass="mensagemDetalhada"/>
                                </h:panelGroup>
                                <h:panelGroup>
                                    <h:outputText    value="#{msg_aplic.prt_Cliente_contaDigito}" />
                                </h:panelGroup>
                                <h:inputText  id="contaDigito" styleClass="form"   onblur="blurinput(this);"  onfocus="focusinput(this);"  size="10" maxlength="10"  value="#{ClienteControle.clienteVO.contaDigito}" />
                                <h:outputText    value="#{msg_aplic.prt_Cliente_identificadorParaCobranca}" />
                                <h:inputText  id="identificadorParaCobranca" styleClass="form"  onblur="blurinput(this);"  onfocus="focusinput(this);" size="20" maxlength="20"  value="#{ClienteControle.clienteVO.identificadorParaCobranca}" />
                                <h:outputText value="Valor do desconto em % para pagamento antecipado do boleto:"/>
                                <rich:inputNumberSpinner maxValue="99" minValue="0" styleClass="form" value="#{ClienteControle.clienteVO.porcentagemDescontoBoletoPagAntecipado}"/>

                                <c:if test="${ClienteControle.existeConvenioVindi && (LoginControle.permissaoAcessoMenuVO.alterarIdVindi || LoginControle.usuarioLogado.usuarioPactoSolucoes)}">
                                    <h:outputText value="IdVindi:"/>
                                    <h:panelGroup layout="block" id="panelIdVindi" style="display: inline-flex;">
                                        <h:inputText id="idVindi" size="10" maxlength="15"
                                                     styleClass="form tooltipster"
                                                     onblur="blurinput(this);" onfocus="focusinput(this);"
                                                     onkeypress="return mascara(this.form, this.id, '99999999', event);"
                                                     onkeyup="somenteNumeros(this);"
                                                     title="IdVindi utilizado para integração com a Vindi"
                                                     value="#{ClienteControle.clienteVO.pessoa.idVindi}"/>
                                        <a4j:commandLink id="confirmarAlterarIdVindi"
                                                         style="padding-left: 5px"
                                                         action="#{ClienteControle.confirmarAlterarIdVindi}"
                                                         oncomplete="#{ClienteControle.mensagemNotificar}"
                                                         title="Salvar IdVindi"
                                                         value="Salvar IdVindi"/>
                                    </h:panelGroup>
                                </c:if>

                                <c:if test="${ClienteControle.existeConvenioPagoLivre && LoginControle.usuarioLogado.usuarioPactoSolucoes}">
                                    <h:outputText value="CustomerId PagoLivre:"/>
                                    <h:panelGroup layout="block" id="panelIdPagoLivre" style="display: inline-flex;">
                                        <h:outputText id="idPagoLivre"
                                                      styleClass="form tooltipster"
                                                      title="CustomerId utilizado para integração com a PagoLivre"
                                                      value="#{ClienteControle.clienteVO.pessoa.customerIdPagoLivre == '' || ClienteControle.clienteVO.pessoa.customerIdPagoLivre == null ? 'Aluno não possui integração' : ClienteControle.clienteVO.pessoa.customerIdPagoLivre}"/>
                                    </h:panelGroup>
                                </c:if>

                                <c:if test="${ClienteControle.existeConvenioStoneV5 && LoginControle.usuarioLogado.usuarioPactoSolucoes}">
                                    <h:outputText value="CustomerId Stone:"/>
                                    <h:panelGroup layout="block" id="panelIdStoneV5" style="display: inline-flex;">
                                        <h:outputText id="idStone"
                                                      styleClass="form tooltipster"
                                                      title="CustomerId utilizado para integração com a Stone"
                                                      value="#{ClienteControle.clienteVO.pessoa.idStone == '' || ClienteControle.clienteVO.pessoa.idStone == null ? 'Aluno não possui integração' : ClienteControle.clienteVO.pessoa.idStone}"/>
                                    </h:panelGroup>
                                </c:if>

                                <c:if test="${ClienteControle.existeConvenioMaxipago && LoginControle.usuarioLogado.usuarioPactoSolucoes}">
                                    <h:outputText value="CustomerId MaxiPago:"/>
                                    <h:panelGroup layout="block" id="panelIdMaxiPago" style="display: inline-flex;">
                                        <h:outputText id="idMaxipago"
                                                      styleClass="form tooltipster"
                                                      title="CustomerId utilizado para integração com a Maxipago"
                                                      value="#{ClienteControle.clienteVO.pessoa.idMaxiPago == '' || ClienteControle.clienteVO.pessoa.idMaxiPago == null ? 'Aluno não possui integração' : ClienteControle.clienteVO.pessoa.idMaxiPago}"/>
                                    </h:panelGroup>
                                </c:if>

                                <c:if test="${ClienteControle.existeConvenioPagarMe && LoginControle.usuarioLogado.usuarioPactoSolucoes}">
                                    <h:outputText value="Id PagarMe:"/>
                                    <h:panelGroup layout="block" id="panelIdPagarMe" style="display: inline-flex;">
                                        <h:outputText id="idPagarMe"
                                                      styleClass="form tooltipster"
                                                      title="Id da pagarme utilizado para integração com a PagarMe."
                                                      value="#{ClienteControle.clienteVO.pessoa.idPagarMe == '' || ClienteControle.clienteVO.pessoa.idPagarMe == null ? 'Aluno não possui integração' : ClienteControle.clienteVO.pessoa.idPagarMe}"/>
                                    </h:panelGroup>
                                </c:if>

                                <c:if test="${ClienteControle.existeConvenioAsaas && LoginControle.usuarioLogado.usuarioPactoSolucoes}">
                                    <h:outputText value="Id Asaas:"
                                                  styleClass="tooltipster"
                                                  title="Id do cliente utilizado para integração com o Asaas."/>
                                    <h:panelGroup layout="block" id="panelIdAsaas" style="display: inline-flex;">
                                        <h:panelGrid id="panelDadosClienteAsaas" columns="2" width="100%"
                                                     headerClass="subordinado" columnClasses="colunaCentralizada">

                                            <h:inputText id="idAsaas"
                                                         rendered="#{ClienteControle.possuiIdAsaas}"
                                                         disabled="true"
                                                         styleClass="form tooltipster"
                                                         value="#{ClienteControle.clienteVO.pessoa.idAsaas}"/>

                                            <h:outputText id="idAsaasSemIntegracao"
                                                          rendered="#{!ClienteControle.possuiIdAsaas}"
                                                          styleClass="form tooltipster"
                                                          title="Este aluno ainda não foi incluído no portal do Asaas"
                                                          value="Aluno não possui integração"/>

                                            <h:panelGrid id="panelDadosAlteracaoAsaas" columns="2" width="100%"
                                                         rendered="#{ClienteControle.clienteVO.pessoa.dataAlteracaoIdAsaas != null && ClienteControle.possuiIdAsaas}"
                                                         title="Data da última alteração"
                                                         styleClass="tooltipster"
                                                         headerClass="subordinado"
                                                         columnClasses="colunaCentralizada">
                                                <h:outputText value="Últ. alteração: "/>
                                                <h:outputText id="dataAlteracaoidAsaas"
                                                              styleClass="form tooltipster"
                                                              value="#{ClienteControle.clienteVO.pessoa.dataAlteracaoIdAsaasApresentar}"/>
                                            </h:panelGrid>
                                        </h:panelGrid>
                                    </h:panelGroup>
                                </c:if>

                                <h:outputText styleClass="tooltipster"
                                              value="Valor máximo para deixar no caixa em aberto (Venda Avulsa):"
                                              title="Caso não queria colocar um limite, deixar em branco (R$ 0,00)."/>
                                <h:inputText id="valorLimiteCaixaAbertoVendaAvulsa"
                                             size="10" maxlength="10" onblur="blurinput(this);"
                                             onkeypress="return formatar_moeda(this,'.',',',event);" onfocus="focusinput(this);"
                                             styleClass="form"
                                             value="#{ClienteControle.clienteVO.pessoa.valorLimiteCaixaAbertoVendaAvulsa}">
                                    <f:converter converterId="FormatadorNumerico"/>
                                </h:inputText>
                            </h:panelGrid>
                        </h:panelGrid>
                    </rich:tab>

                    <rich:tab id="abaEndereco" label="Endereço">
                        <h:panelGrid id="panelEnderecoCliente" styleClass="paginaFontResponsiva" columns="1" width="100%" headerClass="subordinado" columnClasses="colunaCentralizada">
                            <f:facet name="header">
                                <h:outputText value="#{msg_aplic.prt_Endereco_tituloForm}"/>
                            </f:facet>
                            <h:panelGrid id="panelEnderecoCliente1" columns="2" rowClasses="linhaImpar, linhaPar" columnClasses="classEsquerda, classDireita" width="100%">
                                <c:if test="${ClienteControle.configuracaoSistema.usarSistemaInternacional}">
                                    <h:panelGroup>
                                        <h:outputText value="#{msg_aplic.prt_Asterisco}"
                                                      rendered="#{ClienteControle.configuracaoSistema.cepOb}"/>
                                        <h:outputText value="#{msg_aplic.prt_Endereco_cep}"/>
                                    </h:panelGroup>
                                    <h:panelGroup>
                                        <h:inputText id="CEP" size="10" maxlength="10"
                                                     onblur="blurinput(this);" onfocus="focusinput(this);"
                                                     styleClass="form" value="#{ClienteControle.enderecoVO.cep}"/>
                                    </h:panelGroup>
                                </c:if>
                                <c:if test="${!ClienteControle.configuracaoSistema.usarSistemaInternacional}">
                                    <h:panelGroup>
                                        <h:outputText value="#{msg_aplic.prt_Asterisco}"
                                                      rendered="#{ClienteControle.configuracaoSistema.cepOb}"/>
                                        <h:outputText value="#{msg_aplic.prt_Endereco_cep}"/>
                                    </h:panelGroup>
                                    <h:panelGroup>
                                        <h:inputText id="CEP" size="10" maxlength="10"
                                                     onkeypress="return mascara(this.form, 'form:CEP', '99.999-999', event);"
                                                     onblur="blurinput(this);" onfocus="focusinput(this);"
                                                     styleClass="form" value="#{ClienteControle.enderecoVO.cep}"/>
                                        <rich:spacer width="10"/>
                                        <a4j:commandLink id="linkConsultarCEP"
                                                         reRender="form:panelEnderecoCliente1,panelMensagemErro"
                                                         focus="form:enderecoCorresponencia"
                                                         action="#{ClienteControle.consultarCEPCadastroCompleto}">Consulte o CEP</a4j:commandLink>
                                        <rich:spacer width="10"/>
                                        <a4j:commandButton id="consultaDadosCep" alt="Consultar CEP"
                                                           reRender="formConsultarCEP"
                                                           oncomplete="Richfaces.showModalPanel('panelCEP') , setFocus(formConsultarCEP,'formConsultarCEP:estadoCEP');"
                                                           image="./imagens/informacao.gif"/>
                                        <h:outputText value=" **"
                                                      rendered="#{ClienteControle.configuracaoSistema.cepPendente}"/>
                                    </h:panelGroup>
                                </c:if>

                                <h:panelGroup>
                                    <h:outputText value="#{msg_aplic.prt_Asterisco}" rendered="#{ClienteControle.configuracaoSistema.enderecoOb}"/>
                                    <h:outputText value="#{msg_aplic.prt_Endereco_endereco}" />
                                </h:panelGroup>
                                <h:panelGroup>
	                                <h:inputText  id="enderecoCliente" size="40" maxlength="40" onblur="blurinput(this);"  onfocus="focusinput(this);"   styleClass="form" value="#{ClienteControle.enderecoVO.endereco}" />
	                                <%--<h:outputText value=" **" rendered="#{ClienteControle.configuracaoSistema.enderecoPendente}"/>--%>
                                </h:panelGroup>

                                <h:panelGroup>
                                    <h:outputText value="#{msg_aplic.prt_Asterisco}" rendered="#{ClienteControle.configuracaoSistema.numeroOb}"/>
                                    <h:outputText value="#{msg_aplic.prt_Endereco_numero}" />
                                </h:panelGroup>
                                <h:panelGroup>
                                    <h:inputText id="enderecoNumero" size="10" maxlength="10" onblur="blurinput(this);"  onfocus="focusinput(this);"   styleClass="form" value="#{ClienteControle.enderecoVO.numero}" />
                                    <h:outputText value=" **" rendered="#{ClienteControle.configuracaoSistema.numeroPendente}"/>
                                </h:panelGroup>

                                <h:panelGroup>
                                    <h:outputText value="#{msg_aplic.prt_Asterisco}" rendered="#{ClienteControle.configuracaoSistema.enderecoComplementoOb}"/>
                                    <h:outputText value="#{msg_aplic.prt_Endereco_complemento}" />
                                </h:panelGroup>
                                <h:panelGroup>
                                    <h:inputText id="clienteComplemento" size="40" maxlength="40" onblur="blurinput(this);"  onfocus="focusinput(this);"   styleClass="form" value="#{ClienteControle.enderecoVO.complemento}" />
                                    <h:outputText value=" **" rendered="#{ClienteControle.configuracaoSistema.enderecoComplementoPendente}"/>
                                </h:panelGroup>

                                <h:panelGroup>
                                    <h:outputText value="#{msg_aplic.prt_Asterisco}" rendered="#{ClienteControle.configuracaoSistema.bairroOb}"/>
                                    <h:outputText value="#{msg_aplic.prt_Endereco_bairro}" />
                                </h:panelGroup>
                                <h:panelGroup>
	                                <h:inputText id="clienteBairro" size="35" maxlength="35" onblur="blurinput(this);"  onfocus="focusinput(this);"   styleClass="form" value="#{ClienteControle.enderecoVO.bairro}" />
	                                <h:outputText value=" **" rendered="#{ClienteControle.configuracaoSistema.bairroPendente}"/>
                                </h:panelGroup>

                                <h:outputText value="#{msg_aplic.prt_Endereco_enderecoCorrespondencia}"/>
                                <h:selectBooleanCheckbox id="enderecoCorresponencia" value="#{ClienteControle.enderecoVO.enderecoCorrespondencia}"/>
                                <h:panelGroup>
                                    <h:outputText value="#{msg_aplic.prt_Endereco_tipoEndereco}" />
                                </h:panelGroup>
                                <h:selectOneMenu id="Endereco_tipoEndereco" onblur="blurinput(this);" onfocus="focusinput(this);" styleClass="form" value="#{ClienteControle.enderecoVO.tipoEndereco}" >
                                    <f:selectItems  value="#{ClienteControle.listaSelectItemTipoEnderecoEndereco}" />
                                </h:selectOneMenu>
                            </h:panelGrid>

                            <c:if test="${modulo eq 'zillyonWeb'}">
                                <h:panelGroup layout="block" styleClass="container-botoes">
                                    <a4j:commandLink id="addEndereco"
                                                 action="#{ClienteControle.adicionarEndereco}"
                                                 styleClass="botaoSecundario texto-size-14"
                                                 reRender="panelEnderecoCliente,panelMensagemErro"
                                                 focus="form:enderecoCliente"
                                                 value="     #{msg_bt.btn_adicionar}     " accesskey="5"/>
                                </h:panelGroup>
                            </c:if>
                            <c:if test="${modulo eq 'centralEventos'}">
                                <a4j:commandButton id="addEndereco" action="#{ClienteControle.adicionarEndereco}" reRender="panelEnderecoCliente,panelMensagemErro"
                                                   focus="form:enderecoCliente" value="     #{msg_bt.btn_adicionar}     " accesskey="5"
                                                   image="./imagens/botoesCE/incluir.png"/>
                            </c:if>

                            <h:panelGrid columns="1" width="100%" >
                                <h:dataTable id="enderecoVO" width="100%" headerClass="subordinado"
                                             rowClasses="linhaImpar, linhaPar" columnClasses="colunaAlinhamento"
                                             value="#{ClienteControle.pessoaVO.enderecoVOs}" var="endereco">
                                    <h:column >
                                        <h:graphicImage rendered="#{endereco.ltdlng ne null and not empty endereco.ltdlng}"
                                                        style="width: 25px; margin-right: 10px;"
                                                        title="#{endereco.ltdlng}"
                                                        url="./imagens/icon-map.png" />
                                    </h:column>
                                    <h:column>
                                        <f:facet name="header">
                                            <h:outputText value="#{msg_aplic.prt_Endereco_endereco}" />
                                        </f:facet>
                                        <h:outputText value="#{endereco.endereco}" />
                                    </h:column>
                                    <h:column>
                                        <f:facet name="header">
                                            <h:outputText value="#{msg_aplic.prt_Endereco_numero}" />
                                        </f:facet>
                                        <h:outputText value="#{endereco.numero}" />
                                    </h:column>
                                    <h:column>
                                        <f:facet name="header">
                                            <h:outputText value="#{msg_aplic.prt_Endereco_complemento}" />
                                        </f:facet>
                                        <h:outputText value="#{endereco.complemento}" />
                                    </h:column>
                                    <h:column>
                                        <f:facet name="header">
                                            <h:outputText value="#{msg_aplic.prt_Endereco_bairro}" />
                                        </f:facet>
                                        <h:outputText value="#{endereco.bairro}" />
                                    </h:column>
                                    <h:column>
                                        <f:facet name="header">
                                            <h:outputText value="#{msg_aplic.prt_Endereco_cep}" />
                                        </f:facet>
                                        <h:outputText value="#{endereco.cep}" />
                                    </h:column>
                                    <h:column>
                                        <f:facet name="header">
                                            <h:outputText value="#{msg_aplic.prt_Endereco_tipoEndereco}" />
                                        </f:facet>
                                        <h:outputText value="#{endereco.tipoEndereco_Apresentar}" />
                                    </h:column>
                                    <h:column>
                                        <f:facet name="header">
                                            <h:outputText value="#{msg_aplic.prt_Endereco_enderecoCorrespondencia}" />
                                        </f:facet>
                                        <h:outputText value="#{endereco.enderecoCorrespondencia_Apresentar}" />
                                    </h:column>
                                    <h:column>
                                        <f:facet name="header">
                                            <h:outputText  value="#{msg_bt.btn_opcoes}" />
                                        </f:facet>
                                        <h:panelGroup>
                                            <h:commandButton id="editarItemVenda"  action="#{ClienteControle.editarEndereco}" value="#{msg_bt.btn_editar}" image="./imagens/botaoEditar.png" accesskey="6" styleClass="botoes"/>

                                            <h:outputText value="    "/>

                                            <h:commandButton id="removerItemVenda"  action="#{ClienteControle.removerEndereco}" value="#{msg_bt.btn_excluir}" image="./imagens/botaoRemover.png" accesskey="7" styleClass="botoes"/>
                                        </h:panelGroup>
                                    </h:column>
                                </h:dataTable>
                            </h:panelGrid>
                        </h:panelGrid>
                    </rich:tab>
                    <rich:tab id="abaTelefone" label="Telefone">
                        <h:panelGrid id="panelTelefoneCliente" columns="1" width="100%" headerClass="subordinado" columnClasses="colunaCentralizada">
                            <f:facet name="header">
                                <h:outputText value="#{msg_aplic.prt_Telefone_tituloForm}"/>
                            </f:facet>
                            <h:panelGrid columns="2" rowClasses="linhaImpar, linhaPar" columnClasses="classEsquerda, classDireita" width="100%">
                                <h:panelGroup>
                                    <h:outputText value="#{msg_aplic.prt_Asterisco}" rendered="#{ClienteControle.configuracaoSistema.telefoneOb}"/>
                                    <h:outputText value="#{msg_aplic.prt_Telefone_numero}" />
                                </h:panelGroup>
                                <h:panelGroup>
                                    <c:if test="${ClienteControle.configuracaoSistema.usarSistemaInternacional}">
                                        <h:inputText id="ddiTelefoneCliente"
                                                     size="4" title="DDI"
                                                     maxlength="4"
                                                     onblur="blurinput(this);"
                                                     onkeypress="return mascara(this.form, this.id , '+999', event);"
                                                     onfocus="focusinput(this);"
                                                     styleClass="form"
                                                     value="#{ClienteControle.telefoneVO.ddi}"
                                                     style="margin-right: 3px"/>

                                    <h:inputText id="numeroTelefoneClienteInt"
                                                 size="13"
                                                 maxlength="11"
                                                 onblur="blurinput(this);"
                                                 onfocus="focusinput(this);"
                                                 styleClass="form"
                                                 value="#{ClienteControle.telefoneVO.numero}"/>
                                    </c:if>
                                    <c:if test="${!ClienteControle.configuracaoSistema.usarSistemaInternacional}">
                                        <h:inputText id="numeroTelefoneCliente"
                                                     size="13"
                                                     maxlength="13"
                                                     onblur="blurinput(this);"
                                                     onkeypress="return mascara(this.form, this.id , '(99)999999999', event);"
                                                     onfocus="focusinput(this);"
                                                     styleClass="form"
                                                     value="#{ClienteControle.telefoneVO.numero}"/>
                                    </c:if>
                                    <%--<rich:jQuery id="mskTelefone" selector="#numeroTelefoneCliente" timing="onload" query="mask('(99)9999-9999')" />--%>
                                    <h:outputText value=" **" rendered="#{ClienteControle.configuracaoSistema.telefonePendente}"/>
                                </h:panelGroup>

                                <h:outputText value="#{msg_aplic.prt_Telefone_tipoTelefone}" />
                                <h:selectOneMenu id="Telefone_tipoTelefone" onblur="blurinput(this);"  onfocus="focusinput(this);"   styleClass="form" value="#{ClienteControle.telefoneVO.tipoTelefone}" >
                                    <f:selectItems value="#{ClienteControle.listaSelectItemTipoTelefoneTelefone}" />
                                </h:selectOneMenu>
                                <h:outputText value="#{msg_aplic.prt_Telefone_descricao}" />
                                <h:inputText id="descricao" size="25" maxlength="20" styleClass="form" value="#{ClienteControle.telefoneVO.descricao}"/>

                            </h:panelGrid>

                            <c:if test="${modulo eq 'zillyonWeb'}">
                                <a4j:commandButton id="addTelefone" action="#{ClienteControle.adicionarTelefone}" reRender="panelTelefoneCliente,panelMensagemErro" focus="form:numeroTelefoneCliente" value="#{msg_bt.btn_adicionar}" accesskey="6"/>
                            </c:if>
                            <c:if test="${modulo eq 'centralEventos'}">
                                <a4j:commandButton id="addTelefone"  action="#{ClienteControle.adicionarTelefone}" reRender="panelTelefoneCliente,panelMensagemErro"
                                                   focus="form:numeroTelefoneCliente" value="#{msg_bt.btn_adicionar}" accesskey="6"
                                                   image="./imagens/botoesCE/incluir.png"/>
                            </c:if>

                            <h:panelGrid columns="1" width="100%" styleClass="tabFormSubordinada">
                                <h:dataTable id="telefoneVO" width="100%" headerClass="subordinado"
                                             rowClasses="linhaImpar, linhaPar" columnClasses="colunaAlinhamento"
                                             value="#{ClienteControle.pessoaVO.telefoneVOs}" var="telefone">
                                    <h:column>
                                        <f:facet name="header">
                                            <h:outputText  value="#{msg_aplic.prt_Telefone_numero}" />
                                        </f:facet>
                                        <h:outputText  value="#{telefone.numeroApresentar}" />
                                    </h:column>
                                    <h:column>
                                        <f:facet name="header">
                                            <h:outputText  value="#{msg_aplic.prt_Telefone_tipoTelefone}" />
                                        </f:facet>
                                        <h:outputText  value="#{telefone.tipoTelefone_Apresentar}" />
                                    </h:column>
                                    <h:column>
                                        <f:facet name="header">
                                            <h:outputText  value="#{msg_aplic.prt_Telefone_descricao}" />
                                        </f:facet>
                                        <h:outputText  value="#{telefone.descricao}" />
                                    </h:column>
                                    <h:column>
                                        <f:facet name="header">
                                            <h:outputText  value="#{msg_bt.btn_opcoes}" />
                                        </f:facet>
                                        <h:panelGroup>
                                            <h:commandButton id="editarItemVenda"  action="#{ClienteControle.editarTelefone}" value="#{msg_bt.btn_editar}" image="./imagens/botaoEditar.png" accesskey="6" styleClass="botoes"/>

                                            <h:outputText value="    "/>

                                            <h:commandButton id="removerItemVenda"  action="#{ClienteControle.removerTelefone}" value="#{msg_bt.btn_excluir}" image="./imagens/botaoRemover.png" accesskey="7" styleClass="botoes"/>
                                        </h:panelGroup>
                                    </h:column>
                                </h:dataTable>
                            </h:panelGrid>
                        </h:panelGrid>
                    </rich:tab>
                    <rich:tab id="abaDependentes" label="Familiares" action="#{ClienteControle.preencherDadosPlanoCompartilhado}"  rendered="#{ClienteControle.pessoaVO.pessoaFisica}">
                        <h:panelGrid columns="1" id="panelDependentes" width="100%" headerClass="subordinado" columnClasses="colunaCentralizada">
                            <f:facet name="header">
                                <h:outputText value="#{msg_aplic.prt_Familiar_tituloForm}"/>
                            </f:facet>
                            <h:panelGrid columns="2" rowClasses="linhaImpar, linhaPar" columnClasses="classEsquerda, classDireita" width="100%">
                                <h:outputText value="*#{msg_aplic.prt_Familiar_familiar}"/>
                                <h:panelGroup>
                                    <h:inputText id="nomeFamiliar" readonly="true" size="40" maxlength="45"
                                                 onblur="blurinput(this);" onfocus="focusinput(this);" styleClass="form"
                                                 value="#{ClienteControle.familiarVO.nome}"/>
                                    <a4j:commandButton id="consultaDadosFamiliar" alt="Consulta Familiar"
                                                       reRender="formFamiliar"
                                                       rendered="#{ClienteControle.permitirEditarFamiliarComPlanoCompartilhado}"
                                                       oncomplete="Richfaces.showModalPanel('panelFamiliar') ,  setFocus(formFamiliar,'formFamiliar:consultaFamiliar');"
                                                       image="./imagens/informacao.gif"/>
                                </h:panelGroup>

                                <h:outputText value="*#{msg_aplic.prt_Familiar_parentesco}"/>
                                <h:panelGroup>
                                    <h:selectOneMenu id="Familiar_parentesco" onblur="blurinput(this);"
                                                     onfocus="focusinput(this);" styleClass="form"
                                                     value="#{ClienteControle.familiarVO.parentesco.codigo}">
                                        <f:selectItems value="#{ClienteControle.listaSelectItemParentesco}"/>
                                    </h:selectOneMenu>
                                    <a4j:commandButton id="atualizar_Familiar_parentesco"
                                                       action="#{ClienteControle.montarListaSelectItemParentesco}"
                                                       image="imagens/atualizar.png" immediate="true" ajaxSingle="true"
                                                       reRender="form:Familiar_parentesco"/>
                                </h:panelGroup>

                                <h:outputText value="#{msg_aplic.prt_Familiar_codAcesso}"/>
                                <h:inputText size="10" maxlength="10" onblur="blurinput(this);"
                                             readonly="true"
                                             onfocus="focusinput(this);" styleClass="form"
                                             value="#{ClienteControle.familiarVO.codAcesso}"/>

                                <h:outputText value="#{msg_aplic.prt_Familiar_identificador}"/>
                                <h:inputText size="5" maxlength="5" onblur="blurinput(this);"
                                             onfocus="focusinput(this);" styleClass="form"
                                             value="#{ClienteControle.familiarVO.identificador}"/>

                                <h:outputText value="#{msg_aplic.prt_Familiar_compartilharPlano}"/>
                                <h:panelGroup id="pnlCompartilharPlano" style="display: flex" layout="block">
                                    <h:selectBooleanCheckbox onblur="blurinput(this);"
                                                             disabled="#{ClienteControle.quantidadeCompartilhamentosPlano <= 0 or !ClienteControle.permiteCompartilharPlano or !ClienteControle.permitirEditarFamiliarComPlanoCompartilhado}"
                                                             onfocus="focusinput(this);" styleClass="form tooltipster"
                                                             value="#{ClienteControle.familiarVO.compartilharPlano}"/>
                                    <h:outputText style="margin-left: 8px"
                                                  styleClass="tooltipster"
                                                  title="#{ClienteControle.titleCompartilharPlano}"
                                                  value="Quantidade de compartilhamentos possíveis: #{ClienteControle.quantidadeCompartilhamentosPlano}"/>
                                </h:panelGroup>
                            </h:panelGrid>

                            <c:if test="${modulo eq 'zillyonWeb'}">
                                <a4j:commandButton id="addDependente" action="#{ClienteControle.adicionarFamiliar}" reRender="panelDependentes,panelMensagemErro" focus="form:nomeFamiliar" value="     #{msg_bt.btn_adicionar}     " accesskey="7"/>
                            </c:if>
                            <c:if test="${modulo eq 'centralEventos'}">
                                <a4j:commandButton id="addDependente" action="#{ClienteControle.adicionarFamiliar}" reRender="panelDependentes,panelMensagemErro"
                                                   focus="form:nomeFamiliar" value="     #{msg_bt.btn_adicionar}     " accesskey="7"
                                                   image="./imagens/botoesCE/incluir.png"/>
                            </c:if>

                            <h:panelGrid columns="1" width="100%" styleClass="tabFormSubordinada">
                                <rich:dataTable id="familiarVO" width="100%" headerClass="subordinado" styleClass="tabFormSubordinada"
                                             rowClasses="linhaImpar, linhaPar" columnClasses="esquerda,esquerda,esquerda,centralizado,esquerda,esquerda  "
                                             value="#{ClienteControle.clienteVO.familiarVOs}" var="familiar">
                                    <rich:column sortBy="#{familiar.matricula}">
                                        <f:facet name="header">
                                            <h:outputText value="#{msg_aplic.prt_Familiar_matricula}"/>
                                        </f:facet>
                                        <h:commandLink
                                                onclick="redirecionarPaginaPrincipal('#{familiar.matricula}');window.close()"
                                                title="Acessar Familar">
                                            <h:outputText value="#{familiar.matricula}"/>
                                        </h:commandLink>
                                    </rich:column>
                                    <rich:column sortBy="#{familiar.nome}">
                                        <f:facet name="header">
                                            <h:outputText value="#{msg_aplic.prt_Familiar_familiar}"/>
                                        </f:facet>
                                        <h:commandLink
                                                       onclick="redirecionarPaginaPrincipal('#{familiar.matricula}');window.close()"
                                                       title="Acessar Familar">
                                            <h:outputText value="#{familiar.nome}"/>
                                        </h:commandLink>

                                    </rich:column>
                                    <rich:column sortBy="#{familiar.situacaoApresentar}">
                                        <f:facet name="header">
                                            <h:outputText  value="Sit." />
                                        </f:facet>
                                        <h:outputText  value="#{familiar.situacaoApresentar}" />
                                    </rich:column>
                                    <rich:column sortBy="#{familiar.parentesco.descricao}">
                                        <f:facet name="header">
                                            <h:outputText  value="#{msg_aplic.prt_Familiar_parentesco}" />
                                        </f:facet>
                                        <h:outputText  value="#{familiar.parentesco.descricao}" />
                                    </rich:column>
                                    <rich:column sortBy="#{familiar.codAcesso}">
                                        <f:facet name="header">
                                            <h:outputText  value="#{msg_aplic.prt_Familiar_codAcesso}" />
                                        </f:facet>
                                        <h:outputText  value="#{familiar.codAcesso}" />
                                    </rich:column>
                                    <rich:column sortBy="#{familiar.identificador}">
                                        <f:facet name="header">
                                            <h:outputText  value="#{msg_aplic.prt_Familiar_identificador}" />
                                        </f:facet>
                                        <h:outputText  value="#{familiar.identificador}" />
                                    </rich:column>
                                    <rich:column sortBy="#{familiar.compartilharPlano}">
                                        <f:facet name="header">
                                            <h:outputText value="#{msg_aplic.prt_Familiar_compartilharPlano}"/>
                                        </f:facet>
                                        <h:outputText rendered="#{familiar.compartilharPlano}"
                                                      value="#{msg_aplic.sim}"/>

                                        <h:outputText rendered="#{!familiar.compartilharPlano}"
                                                      value="#{msg_aplic.nao}"/>
                                    </rich:column>
                                    <rich:column>
                                        <f:facet name="header">
                                            <h:outputText  value="#{msg_bt.btn_opcoes}" />
                                        </f:facet>
                                        <h:panelGroup>
                                            <a4j:commandButton id="editarItemVenda" reRender="panelDependentes, pnlCompartilharPlano"
                                                               action="#{ClienteControle.editarFamiliar}"
                                                               value="#{msg_bt.btn_editar}"
                                                               image="./imagens/botaoEditar.png" accesskey="6"
                                                               styleClass="botoes"/>

                                            <a4j:commandButton id="removerItemVenda" reRender="familiarVO, pnlCompartilharPlano"
                                                               action="#{ClienteControle.removerFamiliar}"
                                                               value="#{msg_bt.btn_excluir}"
                                                               image="./imagens/botaoRemover.png" accesskey="7"
                                                               style="margin-left: 8px" styleClass="botoes"/>
                                        </h:panelGroup>
                                    </rich:column>
                                </rich:dataTable>
                            </h:panelGrid>
                        </h:panelGrid>
                    </rich:tab>
                    <rich:tab id="abaFamlia" label="Familia" rendered="#{ClienteControle.pessoaVO.pessoaJuridica}">
                        <f:facet name="header">
                            <h:outputText value="#{msg_aplic.prt_Familia_tituloForm}"/>
                        </f:facet>
                        <h:panelGroup id="panelAbaFamilia">
                            <h:panelGrid columns="2" rowClasses="linhaImpar, linhaPar"
                                         columnClasses="classEsquerda, classDireita" width="100%">
                                <h:outputText value="#{msg_aplic.prt_Cliente_tituloForm}"/>
                                <h:panelGroup>
                                    <h:inputText id="clienteDependente" size="40" maxlength="50"
                                                 value="#{ClienteControle.dependenteTO.clienteDependente.pessoa.nome}"
                                                 onblur="blurinput(this);"
                                                 onfocus="focusinput(this);" styleClass="form"/>
                                    <rich:suggestionbox height="200" width="299"

                                                        for="clienteDependente"
                                                        frequency="0"
                                                        popupStyle="margin-left: 12px;background-color: #000066;"
                                                        suggestionAction="#{ClienteControle.executarAutocompleteFuncionalidade}"
                                                        minChars="1" rowClasses="20"
                                                        status="true"
                                                        nothingLabel="Nenhum Cliente Encontrado!"
                                                        var="result" id="suggestionClienteDependente">
                                        <a4j:support event="onselect"
                                                     action="#{ClienteControle.selecionarCliente}"
                                                     reRender="abaFamlia">

                                        </a4j:support>
                                        <h:column>
                                            <h:outputText value="#{result.pessoa.nome}"/>
                                        </h:column>
                                    </rich:suggestionbox>
                                </h:panelGroup>
                                <h:outputText value="#{msg_aplic.prt_Familiar_parentesco}"/>
                                <h:panelGroup layout="block">
                                    <h:panelGroup layout="block" style="display: inline-block">
                                    <h:selectOneMenu value="#{ClienteControle.dependenteTO.parentesco.codigo}"
                                                     label="#{ClienteControle.dependenteTO.parentesco.descricao}">
                                        <f:selectItems value="#{ClienteControle.listaSelectItemParentesco}"/>
                                    </h:selectOneMenu>
                                    </h:panelGroup>
                                    <h:panelGroup layout="block" style="display: inline-block;vertical-align: middle">
                                    <a4j:commandButton action="#{ClienteControle.adicionarFamilia}"
                                                       oncomplete="#{ClienteControle.msgAlert}"
                                                       reRender="form,mdlMensagemGenerica"
                                                       image="./imagens/botaoAdicionar.png">
                                        <a4j:support event="onclick"
                                                     reRender="panelAbaFamilia"/>
                                    </a4j:commandButton>
                                    </h:panelGroup>
                                </h:panelGroup>

                            </h:panelGrid>
                        </h:panelGroup>
                        <h:panelGroup layout="block" style="width: 50%;margin: 0 auto;">
                        <rich:dataTable   width="100%" headerClass="subordinado"
                                          rowClasses="linhaImpar, linhaPar" columnClasses="colunaAlinhamento"
                                          value="#{ClienteControle.dependentes}" var="dependente">

                            <rich:column sortBy="#{dependente.clienteDependente.matricula}">
                                <f:facet name="header">
                                    <h:outputText  value="#{msg_aplic.prt_Cliente_matricula}" />
                                </f:facet>
                                <h:outputText  value="#{dependente.clienteDependente.matricula}" />
                            </rich:column>

                            <rich:column sortBy="#{dependente.clienteDependente.nome_Apresentar}">
                                <f:facet name="header">
                                    <h:outputText  value="#{msg_aplic.prt_Cliente_label_nome}" />
                                </f:facet>
                                <h:outputText  value="#{dependente.clienteDependente.nome_Apresentar}" />
                            </rich:column>
                            <rich:column sortBy="#{dependente.parentesco.descricao}">
                                <f:facet name="header">
                                    <h:outputText  value="#{msg_aplic.prt_Parentesco_tituloForm}" />
                                </f:facet>
                                <h:outputText  value="#{dependente.parentesco.descricao}" />
                            </rich:column>
                            <rich:column >
                                <f:facet name="header">
                                    <h:outputText  value="Opções" />
                                </f:facet>

                            <a4j:commandButton action="#{ClienteControle.removerDependenteFamiliar}"
                                               oncomplete="#{ClienteControle.msgAlert}"
                                               reRender="form,mdlMensagemGenerica"
                                               image="./imagens/botaoRemover.png"/>
                            </rich:column>
                        </rich:dataTable>
                        </h:panelGroup>
                    </rich:tab>
                    <rich:tab id="abaEmail" label="Email">
                        <h:panelGrid id="panelEmail" columns="1" width="100%" headerClass="subordinado" columnClasses="colunaCentralizada">
                            <f:facet name="header">
                                <h:outputText value="#{msg_aplic.prt_Email_tituloForm}"/>
                            </f:facet>
                            <h:panelGrid columns="3" rowClasses="linhaImpar, linhaPar" columnClasses="classEsquerda, classDireita" width="100%">
                                <h:panelGroup>
                                    <h:outputText value="#{msg_aplic.prt_Asterisco}" rendered="#{ClienteControle.configuracaoSistema.emailOb}"/>
                                    <h:outputText value="#{msg_aplic.prt_Email_email}" />
                                </h:panelGroup>

                                <h:panelGroup>
	                                <h:inputText id="emailCliente" size="40" maxlength="50" onblur="blurinput(this);"  onfocus="focusinput(this);"   styleClass="form" value="#{ClienteControle.emailVO.email}" />
	                                <h:outputText value=" **" rendered="#{ClienteControle.configuracaoSistema.emailPendente}"/>
                                </h:panelGroup>

                                <rich:spacer width="1"/>
                                <h:outputText value="#{msg_aplic.prt_Email_emailCorrespondencia}" />
                                <h:selectBooleanCheckbox id="selectEmailCorres"  styleClass="campos" value="#{ClienteControle.emailVO.emailCorrespondencia}" />
                            </h:panelGrid>

                            <c:if test="${modulo eq 'zillyonWeb'}">
                                <a4j:commandButton id="addEmail" action="#{ClienteControle.adicionarEmail}" reRender="panelEmail,panelMensagemErro" focus="form:emailCliente" value="     #{msg_bt.btn_adicionar}     " accesskey="8" />
                            </c:if>
                            <c:if test="${modulo eq 'centralEventos'}">
                                <a4j:commandButton id="addEmail" action="#{ClienteControle.adicionarEmail}" reRender="panelEmail,panelMensagemErro"
                                                   focus="form:emailCliente" value="     #{msg_bt.btn_adicionar}     " accesskey="8"
                                                   image="./imagens/botoesCE/incluir.png"/>
                            </c:if>

                            <h:panelGrid columns="1" width="100%" styleClass="tabFormSubordinada">
                                <h:dataTable id="emailVO" width="100%" headerClass="subordinado"
                                             rowClasses="linhaImpar, linhaPar" columnClasses="colunaAlinhamento, colunaAlinhamento, colunaAlinhamento"
                                             value="#{ClienteControle.pessoaVO.emailVOs}" var="email">
                                    <h:column>
                                        <f:facet name="header">
                                            <h:outputText  value="#{msg_aplic.prt_Email_email}" />
                                        </f:facet>
                                        <h:outputText  value="#{email.email}" />
                                    </h:column>
                                    <h:column>
                                        <f:facet name="header">
                                            <h:outputText  value="#{msg_aplic.prt_Email_emailCorrespondencia}" />
                                        </f:facet>
                                        <h:outputText  value="#{email.emailCorrespondencia_Apresentar}" />
                                    </h:column>

                                    <h:column>
                                        <f:facet name="header">
                                            <h:outputText value="#{msg_aplic.prt_Email_emailBloqueadoBounce}"/>
                                        </f:facet>
                                        <h:outputText value="#{email.bloqueadoBounce_Apresentar}"/>
                                    </h:column>

                                    <h:column>
                                        <f:facet name="header">
                                            <h:outputText  value="#{msg_bt.btn_opcoes}" />
                                        </f:facet>
                                        <h:panelGroup>
                                            <a4j:commandButton id="editarItemVenda" reRender="panelEmail" action="#{ClienteControle.editarEmail}" value="#{msg_bt.btn_editar}" image="./imagens/botaoEditar.png" accesskey="6" styleClass="botoes"/>

                                            <h:outputText value="    "/>

                                            <a4j:commandButton id="removerItemVenda" reRender="emailVO" action="#{ClienteControle.removerEmail}" value="#{msg_bt.btn_excluir}" image="./imagens/botaoRemover.png" accesskey="7" styleClass="botoes"/>
                                        </h:panelGroup>
                                    </h:column>
                                </h:dataTable>
                            </h:panelGrid>
                        </h:panelGrid>
                    </rich:tab>
                    <rich:tab id="abaPlaca" label="Placa">
                        <h:panelGrid id="panelPlaca" columns="1" width="100%" headerClass="subordinado"
                                     columnClasses="colunaCentralizada">
                            <f:facet name="header">
                                <h:outputText value="#{msg_aplic.prt_Placa_tituloForm}"/>
                            </f:facet>
                            <h:panelGrid columns="2" rowClasses="linhaImpar, linhaPar"
                                         columnClasses="classEsquerda, classDireita" width="100%">
                                    <h:outputText value="#{msg_aplic.prt_Placa_placa}"/>
                                    <h:inputText id="placaCliente" size="10" maxlength="10" onblur="blurinput(this);"
                                                 onfocus="focusinput(this);" styleClass="form"
                                                 value="#{ClienteControle.placaVO.placa}"/>

                                <h:outputText value="#{msg_aplic.prt_Placa_descricao}"/>
                                <h:inputText id="descricaoPlacaCliente" size="20" maxlength="50" onblur="blurinput(this);"
                                             onfocus="focusinput(this);" styleClass="form"
                                             value="#{ClienteControle.placaVO.descricao}"/>
                            </h:panelGrid>

                            <c:if test="${modulo eq 'zillyonWeb'}">
                                <a4j:commandButton id="addPlaca" action="#{ClienteControle.adicionarPlaca}"
                                                   reRender="panelPlaca,panelMensagemErro" focus="form:placaCliente"
                                                   value="     #{msg_bt.btn_adicionar}     "/>
                            </c:if>
                            <c:if test="${modulo eq 'centralEventos'}">
                                <a4j:commandButton id="addPlaca" action="#{ClienteControle.adicionarPlaca}"
                                                   reRender="panelPlaca,panelMensagemErro"
                                                   focus="form:placaCliente" value="     #{msg_bt.btn_adicionar}     "
                                                   image="./imagens/botoesCE/incluir.png"/>
                            </c:if>

                            <h:panelGrid columns="1" width="100%" styleClass="tabFormSubordinada">
                                <h:dataTable id="placaVO" width="100%" headerClass="subordinado"
                                             rowClasses="linhaImpar, linhaPar" columnClasses="colunaAlinhamento"
                                             value="#{ClienteControle.pessoaVO.placaVOs}" var="placaVO">
                                    <h:column>
                                        <f:facet name="header">
                                            <h:outputText value="#{msg_aplic.prt_Placa_placa}"/>
                                        </f:facet>
                                        <h:outputText value="#{placaVO.placa}"/>
                                    </h:column>
                                    <h:column>
                                        <f:facet name="header">
                                            <h:outputText value="#{msg_aplic.prt_Placa_descricao}"/>
                                        </f:facet>
                                        <h:outputText value="#{placaVO.descricao}"/>
                                    </h:column>
                                    <h:column>
                                        <f:facet name="header">
                                            <h:outputText value="#{msg_bt.btn_opcoes}"/>
                                        </f:facet>
                                        <h:panelGroup>
                                            <a4j:commandButton reRender="panelPlaca"
                                                               action="#{ClienteControle.editarPlaca}"
                                                               value="#{msg_bt.btn_editar}"
                                                               image="./imagens/botaoEditar.png"
                                                               styleClass="botoes"/>

                                            <h:outputText value="    "/>

                                            <a4j:commandButton id="removerItemVenda" reRender="placaVO"
                                                               action="#{ClienteControle.removerPlaca}"
                                                               value="#{msg_bt.btn_excluir}"
                                                               image="./imagens/botaoRemover.png"
                                                               styleClass="botoes"/>
                                        </h:panelGroup>
                                    </h:column>
                                </h:dataTable>
                            </h:panelGrid>
                        </h:panelGrid>
                    </rich:tab>
                    <rich:tab id="abaGrupo" label="Grupo / Classificação">
                        <h:panelGrid id="panelGrupoCliente" columns="1" width="100%" headerClass="subordinado" columnClasses="colunaCentralizada">
                            <f:facet name="header">
                                <h:outputText value="#{msg_aplic.prt_ClienteGrupo_tituloForm}"/>
                            </f:facet>
                            <h:panelGrid columns="2" rowClasses="linhaImpar, linhaPar" columnClasses="classEsquerda, classDireita" width="100%">
                                <h:outputText    value="#{msg_aplic.prt_ClienteGrupo_grupo}" />
                                <h:panelGroup>
                                    <h:selectOneMenu  id="ClienteGrupo_grupo" onblur="blurinput(this);"  onfocus="focusinput(this);"   styleClass="form" value="#{ClienteControle.clienteGrupoVO.grupo.codigo}" >
                                        <f:selectItems  value="#{ClienteControle.listaSelectItemGrupo}" />
                                    </h:selectOneMenu>
                                    <a4j:commandButton id="atualizar_ClienteGrupo_grupo" action="#{ClienteControle.montarListaSelectItemGrupo}" image="imagens/atualizar.png" immediate="true" ajaxSingle="true" reRender="form:ClienteGrupo_grupo"/>
                                </h:panelGroup>
                            </h:panelGrid>

                            <c:if test="${modulo eq 'zillyonWeb'}">
                                <a4j:commandButton id="addGrupo" action="#{ClienteControle.adicionarClienteGrupo}" reRender="panelGrupoCliente,panelMensagemErro" focus="form:ClienteGrupo_grupo" value="     #{msg_bt.btn_adicionar}     " accesskey="9"/>
                            </c:if>
                            <c:if test="${modulo eq 'centralEventos'}">
                                <a4j:commandButton id="addGrupo" action="#{ClienteControle.adicionarClienteGrupo}" reRender="panelGrupoCliente,panelMensagemErro"
                                                   focus="form:ClienteGrupo_grupo" value="     #{msg_bt.btn_adicionar}     " accesskey="9"
                                                   image="./imagens/botoesCE/incluir.png"/>
                            </c:if>

                            <h:panelGrid columns="1" width="100%" styleClass="tabFormSubordinada">
                                <h:dataTable id="clienteGrupoVO" width="100%" headerClass="subordinado"
                                             rowClasses="linhaImpar, linhaPar" columnClasses="colunaAlinhamento"
                                             value="#{ClienteControle.clienteVO.clienteGrupoVOs}" var="clienteGrupo">
                                    <h:column>
                                        <f:facet name="header">
                                            <h:outputText  value="#{msg_aplic.prt_ClienteGrupo_grupo}" />
                                        </f:facet>
                                        <h:outputText  value="#{clienteGrupo.grupo.descricao}" />
                                    </h:column>
                                    <h:column>
                                        <f:facet name="header">
                                            <h:outputText  value="#{msg_bt.btn_opcoes}" />
                                        </f:facet>
                                        <h:panelGroup>
                                            <a4j:commandButton id="removerClienteGrupo" reRender="clienteGrupoVO" action="#{ClienteControle.removerClienteGrupo}" value="#{msg_bt.btn_excluir}" image="./imagens/botaoRemover.png" accesskey="7" styleClass="botoes"/>
                                        </h:panelGroup>
                                    </h:column>
                                </h:dataTable>
                            </h:panelGrid>
                        </h:panelGrid>

                        <h:panelGrid id="panelClassificacaoCliente" columns="1" width="100%" headerClass="subordinado" columnClasses="colunaCentralizada">
                            <f:facet name="header">
                                <h:outputText value="#{msg_aplic.prt_ClienteClassificacao_tituloForm}"/>
                            </f:facet>
                            <h:panelGrid columns="2" rowClasses="linhaImpar, linhaPar" headerClass="subordinado" columnClasses="classEsquerda, classDireita" width="100%">
                                <h:outputText    value="#{msg_aplic.prt_ClienteClassificacao_classificacao}" />
                                <h:panelGroup>
                                    <h:selectOneMenu  id="ClienteClassificacao_classificacao" onblur="blurinput(this);"  onfocus="focusinput(this);"   styleClass="form" value="#{ClienteControle.clienteClassificacaoVO.classificacao.codigo}" >
                                        <f:selectItems  value="#{ClienteControle.listaSelectItemClassificacao}" />
                                    </h:selectOneMenu>
                                    <a4j:commandButton id="atualizar_ClienteClassificacao_classificacao" action="#{ClienteControle.montarListaSelectItemClassificacao}" image="imagens/atualizar.png" immediate="true" ajaxSingle="true" reRender="form:ClienteClassificacao_classificacao"/>
                                </h:panelGroup>
                            </h:panelGrid>

                            <c:if test="${modulo eq 'zillyonWeb'}">
                                <a4j:commandButton id="addClassificacao" action="#{ClienteControle.adicionarClienteClassificacao}" reRender="panelClassificacaoCliente,panelMensagemErro" focus="form:ClienteClassificacao_classificacao" value="     #{msg_bt.btn_adicionar}     " accesskey="10"/>
                            </c:if>
                            <c:if test="${modulo eq 'centralEventos'}">
                                <a4j:commandButton id="addClassificacao" action="#{ClienteControle.adicionarClienteClassificacao}" reRender="panelClassificacaoCliente,panelMensagemErro"
                                                   focus="form:ClienteClassificacao_classificacao" value="     #{msg_bt.btn_adicionar}     " accesskey="10"
                                                   image="./imagens/botoesCE/incluir.png"/>
                            </c:if>

                            <h:panelGrid columns="1" width="100%" styleClass="tabFormSubordinada">
                                <h:dataTable id="clienteClassificacaoVO" width="100%" headerClass="subordinado" styleClass="tabFormSubordinada"
                                             rowClasses="linhaImpar, linhaPar" columnClasses="colunaAlinhamento"
                                             value="#{ClienteControle.clienteVO.clienteClassificacaoVOs}" var="clienteClassificacao">
                                    <h:column>
                                        <f:facet name="header">
                                            <h:outputText  value="#{msg_aplic.prt_ClienteClassificacao_classificacao}" />
                                        </f:facet>
                                        <h:outputText  value="#{clienteClassificacao.classificacao.nome}" />
                                    </h:column>
                                    <h:column>
                                        <f:facet name="header">
                                            <h:outputText  value="#{msg_bt.btn_opcoes}" />
                                        </f:facet>
                                        <h:panelGroup>
                                            <a4j:commandButton id="removerClienteClassificacao"  reRender="clienteClassificacaoVO" action="#{ClienteControle.removerClienteClassificacao}" value="#{msg_bt.btn_excluir}" image="./imagens/botaoRemover.png" accesskey="7" styleClass="botoes"/>
                                        </h:panelGroup>
                                    </h:column>
                                </h:dataTable>
                            </h:panelGrid>
                        </h:panelGrid>
                    </rich:tab>
                    <rich:tab id="abaVinculo" label="Vínculos">
                        <h:panelGrid id="panelVinculo" columns="1" width="100%"  headerClass="subordinado" columnClasses="colunaCentralizada">
                            <f:facet name="header">
                                <h:outputText value="#{msg_aplic.prt_Vinculo_tituloForm}"/>
                            </f:facet>
                            <h:panelGrid columns="2" rowClasses="linhaImpar, linhaPar" headerClass="subordinado" columnClasses="classEsquerda, classDireita" width="100%">
                                <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_Vinculo_tipoVinculo}" />
                                <h:panelGroup>
                                    <h:selectOneMenu  id="tipoVinculo" styleClass="camposObrigatorios" value="#{ClienteControle.vinculoVO.tipoVinculo}" >
                                        <a4j:support event="onchange" action="#{ClienteControle.montarTipoVinculoColaborador}" reRender="panelVinculo"/>
                                        <f:selectItems  value="#{ClienteControle.listaSelectItemTipoVinculo}" />
                                    </h:selectOneMenu>
                                </h:panelGroup>
                                <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_Vinculo_colaborador}" />
                                <h:panelGroup>
                                    <h:selectOneMenu  id="colaboradorVinculo" styleClass="camposObrigatorios" value="#{ClienteControle.vinculoVO.colaborador.codigo}" >
                                        <f:selectItems  value="#{ClienteControle.listaSelectVinculoColaborador}" />
                                    </h:selectOneMenu>
                                    <a4j:commandButton id="atualizar_colaborador" action="#{ClienteControle.montarTipoVinculoColaborador}" image="imagens/atualizar.png" ajaxSingle="true" reRender="form:colaboradorVinculo"/>
                                </h:panelGroup>
                            </h:panelGrid>
                            <h:panelGroup>
                                <c:if test="${modulo eq 'zillyonWeb'}">
                                    <a4j:commandButton id="adicionarVinculor" action="#{ClienteControle.autorizarVinculo}"
                                                       value="#{msg_bt.btn_adicionar}" focus="tipoVinculo"
                                                       reRender="panelVinculo, panelMensagemErro, panelAutorizacaoFuncionalidade"/>
                                </c:if>
                                <c:if test="${modulo eq 'centralEventos'}">
                                    <a4j:commandButton id="adicionarVinculor" action="#{ClienteControle.autorizarVinculo}"
                                                       value="#{msg_bt.btn_adicionar}" focus="tipoVinculo"
                                                       reRender="panelVinculo, panelMensagemErro, panelAutorizacaoFuncionalidade"
                                                       image="./imagens/botoesCE/incluir.png"/>
                                </c:if>
                                <rich:spacer width="10"/>
                                <c:if test="${modulo eq 'centralEventos'}">
                                    <a4j:commandButton action="#{HistoricoVinculoControle.historicoCliente}"
                                                       reRender="panelHistoricoVinculo"
                                                       oncomplete="Richfaces.showModalPanel('panelHistoricoVinculo')"
                                                       title="Ver Histórico Vínculo" styleClass="botoes" value="Histórico"/>
                                </c:if>
                                <c:if test="${modulo eq 'centralEventos'}">
                                    <a4j:commandButton action="#{HistoricoVinculoControle.historicoCliente}"
                                                       reRender="panelHistoricoVinculo"
                                                       oncomplete="Richfaces.showModalPanel('panelHistoricoVinculo')"
                                                       image="./imagens/botoesCE/historico.png"
                                                       title="Ver Histórico Vínculo" styleClass="botoes" value="Histórico"/>
                                </c:if>
                            </h:panelGroup>
                            <h:panelGrid id="panelClienteVinculoVO" columns="1" width="100%" styleClass="tabFormSubordinada">
                                <h:dataTable id="clienteVinculoVO" width="100%" headerClass="subordinado" styleClass="tabFormSubordinada"
                                             rowClasses="linhaImpar, linhaPar" columnClasses="colunaAlinhamento"
                                             value="#{ClienteControle.clienteVO.vinculoVOs}" var="vinculo">
                                    <h:column>
                                        <f:facet name="header">
                                            <h:outputText  value="#{msg_aplic.prt_Vinculo_tipoVinculo}" />
                                        </f:facet>
                                        <h:outputText  value="#{vinculo.tipoVinculo_Apresentar}" />
                                    </h:column>
                                    <h:column>
                                        <f:facet name="header">
                                            <h:outputText  value="#{msg_aplic.prt_Vinculo_colaborador}" />
                                        </f:facet>
                                        <h:outputText  value="#{vinculo.colaborador.pessoa.nome}" />
                                    </h:column>
                                    <h:column>
                                        <f:facet name="header">
                                            <h:outputText  value="#{msg_bt.btn_opcoes}" />
                                        </f:facet>
                                        <h:panelGroup>
                                            <a4j:commandButton id="removerItemVenda"  action="#{ClienteControle.acaoRemoverVinculo}"
                                                               value="#{msg_bt.btn_excluir}" reRender="panelVinculo, panelMensagemErro, panelAutorizacaoFuncionalidade, mdlVinculos"
                                                               oncomplete="#{ClienteControle.onCompleteVinculoAgenda}"
                                                               image="./imagens/botaoRemover.png" accesskey="7" styleClass="botoes"/>
                                        </h:panelGroup>
                                    </h:column>
                                </h:dataTable>
                            </h:panelGrid>
                        </h:panelGrid>
                    </rich:tab>
                    <rich:tab id="abaFoto" label="Foto">
                        <a4j:jsFunction name="updateFoto" action="#{ClienteControle.recarregarFoto}" reRender="panelFoto1"/>
                        <h:panelGrid id="panelFoto" columns="1" headerClass="subordinado" columnClasses="colunaCentralizada">
                            <f:facet name="header">
                                <h:outputText value="#{msg_aplic.prt_Foto_tituloForm}"/>
                            </f:facet>
                            <h:panelGrid id="panelFoto1" columns="1">
                                <a4j:mediaOutput element="img" id="imagem1"  align="left" style="left:0px;width:150px;height:150px "  cacheable="false" session="false"
                                                 rendered="#{!SuperControle.fotosNaNuvem}"
                                                 createContent="#{ClienteControle.paintFoto}" value="#{ImagemData}" mimeType="image/jpeg" >
                                    <f:param value="#{SuperControle.timeStamp}" name="time"/>
                                    <f:param name="largura" value="150"/>
                                    <f:param name="altura" value="150"/>
                                </a4j:mediaOutput>
                                <h:graphicImage rendered="#{SuperControle.fotosNaNuvem}"
                                    width="150" height="150"
                                    style="width:150px;height:150px"
                                    url="#{ClienteControle.paintFotoDaNuvem}?time=#{SuperControle.timeStamp}"/>
                            </h:panelGrid>
                            <h:panelGrid columns="2">

                                <a4j:commandButton
                                    actionListener="#{CapturaFotoControle.selecionarPessoa}"
                                    action="#{CapturaFotoControle.vazio}"
                                    id="btnAlterarFoto" value="#{msg_bt.btn_capturarfoto}" image="./imagens/webcam.png"
                                    oncomplete="setAttributesModalCapFoto(
                                            '#{ClienteControle.key}',
                                            '#{ClienteControle.clienteVO.pessoa.codigo}',
                                            '#{ClienteControle.contextPath}');
                                        Richfaces.showModalPanel('modalCapFotoHTML5');"
                                    alt="#{msg_bt.btn_capturarfoto}" title="#{msg_bt.btn_capturarfoto}" styleClass="botoes">
                                    <f:attribute name="pessoa" value="#{ClienteControle.clienteVO.pessoa.codigo}"/>
                                </a4j:commandButton>
                                <a4j:commandButton id="btnRemoverFoto" value="#{msg_bt.btn_removerfoto}" image="./images/icon_delete.png"
                                                   onclick="if (!confirm('Confirma exclusão do registro?')){return false;}"
                                                   action="#{ClienteControle.removerFoto}"
                                                   reRender="panelFoto"
                                                   alt="#{msg_bt.btn_removerfoto}" title="#{msg_bt.btn_removerfoto}" styleClass="botoes"/>
                            </h:panelGrid>
                        </h:panelGrid>
                    </rich:tab>

                    <rich:tab actionListener="#{ClienteControle.preencherAutorizacoesCobranca}"
                              id="abaCobranca" label="Cobrança" reRender="form">
                        <f:attribute name="cliente" value="#{ClienteControle.clienteVO}"/>
                        <h:panelGroup layout="block" styleClass="formNovo">
                            <%@include file="includes/cliente/include_box_autorizacao_cobranca.jsp" %>
                        </h:panelGroup>
                    </rich:tab>

                    <rich:tab id="abaUsuarioMovel" label="Usuário Móvel">
                        <f:attribute name="cliente" value="#{ClienteControle.clienteVO}"/>
                        <%@include file="includes/integracao/include_usuariomovel_cliente.jsp" %>
                    </rich:tab>

                    <rich:tab id="abaNota" label="Nota Fiscal" rendered="#{ClienteControle.clienteVO.empresa.usarNFSe}">
                        <h:panelGrid id="panelNotaFiscal" columns="1" width="100%" headerClass="subordinado"
                                     columnClasses="colunaCentralizada">
                            <f:facet name="header">
                                <h:outputText value="Nota Fiscal"/>
                            </f:facet>
                            <h:panelGrid columns="2" rowClasses="linhaImpar, linhaPar"
                                         columnClasses="classEsquerda, classDireita" width="100%">

                                <h:outputText style="font-weight: bold" value="Observação NFSe: "
                                              rendered="#{ClienteControle.clienteVO.empresa.usarNFSe}"/>
                                <h:panelGroup rendered="#{ClienteControle.clienteVO.empresa.usarNFSe}">
                                    <h:inputTextarea id="observacaoNota" value="#{ClienteControle.clienteVO.pessoa.observacaoNota}" cols="50" rows="3"/>
                                </h:panelGroup>
                            </h:panelGrid>
                        </h:panelGrid>
                    </rich:tab>
                    <rich:tab id="abaAmigoFit" label="Dados Amigo Fit" rendered="#{ClienteControle.apresentarpanelAmigoFit}">
                        <f:attribute name="cliente" value="#{ClienteControle.clienteVO}"/>
                        <%@include file="includes/integracao/include_usarioAmigoFit_cliente.jsp" %>
                    </rich:tab>
                </rich:tabPanel>




                <h:panelGrid id="pnlGridMensagens" columns="1" width="100%" styleClass="tabMensagens">
                    <h:panelGrid id="panelMensagemErro" columns="3" width="100%" styleClass="tabMensagens">
                        <h:panelGrid columns="1" width="100%">

                            <h:outputText value=" "/>

                        </h:panelGrid>
                        <h:commandButton  rendered="#{ClienteControle.atencao}" image="./imagens/atencao.png"/>
                        <h:commandButton  rendered="#{ClienteControle.sucesso}" image="./imagens/sucesso.png"/>
                        <h:commandButton rendered="#{ClienteControle.erro}" image="./imagens/erro.png"/>
                        <h:panelGrid columns="1" width="100%">
                            <h:outputText id="msgCadasCliSimples" styleClass="mensagem"  value="#{ClienteControle.mensagem}"/>
                            <h:outputText id="msgCadasCliDet" styleClass="mensagemDetalhada" value="#{ClienteControle.mensagemDetalhada}"/>
                        </h:panelGrid>
                    </h:panelGrid>
                    <h:panelGrid columns="1" width="100%" columnClasses="colunaCentralizada">
                        <h:panelGroup>
                            <%--  <c:if test="${modulo eq 'zillyonWeb'}">
                                  <h:commandButton id="novo" immediate="true" rendered="#{ClienteControle.botaoNovo}"  action="#{ClienteControle.novo}" value="#{msg_bt.btn_novo}" image="./imagens/botaoNovo.png" alt="#{msg.msg_novo_dados}" accesskey="1" styleClass="botoes"/>
                              </c:if> --%>

                            <h:outputText value="    "/>

                            <c:if test="${modulo eq 'zillyonWeb'}">
                                <a4j:commandButton id="salvar" reRender="form:panelGeral, panelExisteCliente,modalValidacaoCPF, panelExistePessoa"
                                                   action="#{ClienteControle.gravar}"
                                                   value="#{msg_bt.btn_gravar}" alt="#{msg.msg_gravar_dados}" accesskey="2" styleClass="botoes nvoBt"
                                                   oncomplete="#{ClienteControle.msgAlert}"/>
                            </c:if>
                            <c:if test="${modulo eq 'centralEventos'}">
                                <a4j:commandButton id="salvar"
                                                   reRender="resultadoSalvarClienteInteressado,panelMensagemErro"
                                                   action="#{ClienteControle.salvarClienteInteressado}"
                                                   oncomplete="return atualizar();"
                                                   value="#{msg_bt.btn_gravar}"
                                                   alt="#{msg.msg_gravar_dados}" accesskey="2" styleClass="botoes nvoBt"
                                                   actionListener="#{ClienteControle.autorizacao}">
                                    <!-- Entidade.cliente -->
                                    <f:attribute name="entidade" value="111" />
                                    <!-- operacao.gravar -->
                                    <f:attribute name="operacao" value="G" />
                                </a4j:commandButton>


                                <h:inputHidden value="#{ClienteControle.resultadoSalvarClienteInteressado}" id="resultadoSalvarClienteInteressado" />
                            </c:if>
                            <c:if test="${modulo eq 'zillyonWeb'}">

                                <h:outputText value="    "/>

                                <a4j:commandButton id="excluir" reRender="mdlMensagemGenerica" rendered="#{ClienteControle.botaoExcluir}"
                                                   oncomplete="#{ClienteControle.msgAlert}" action="#{ClienteControle.confirmarExcluir}" value="#{msg_bt.btn_excluir}"
                                                   alt="#{msg.msg_excluir_dados}" accesskey="3" styleClass="botoes nvoBt btSec btPerigo"/>

                                <h:outputText value="    "/>


                                <a4j:commandButton id="consultar"  rendered="#{ClienteControle.botaoConsulta}" immediate="true" action="#{ClienteControle.inicializarConsultar}"
                                                   value="#{msg_bt.btn_voltar_lista}" alt="#{msg.msg_consultar_dados}" accesskey="4" styleClass="botoes nvoBt btSec"/>

                                <c:if test="${clienteExcluido eq 'clienteExcluido'}">
                                    <% session.removeAttribute("clienteExcluido"); %>
                                </c:if>
                            </c:if>
                            <c:if test="${modulo eq 'centralEventos'}">

                                <h:outputText value="    "/>

                                <a4j:commandButton id="consultar"  rendered="#{ClienteControle.botaoConsulta}"
                                                 immediate="true" action="#{ClienteControle.inicializarConsultar}"
                                                 value="#{msg_bt.btn_voltar_lista}"
                                                 alt="#{msg.msg_consultar_dados}" accesskey="4" styleClass="botoes nvoBt btSec"/>

                                <h:outputText value="    "/>

                            </c:if>
                            <rich:spacer width="10px"/>
                            <a4j:commandLink action="#{ClienteControle.realizarConsultaLogObjetoSelecionado}"
                                               reRender="form" oncomplete="abrirPopup('visualizadorLogForm.jsp', 'VisualizadorLog', 785, 595);"
                                               title="Visualizar Log" styleClass="botoes nvoBt btSec"
                                               style="display: inline-block; padding: 8px 15px;">
                                <i class="fa-icon-list"></i>
                            </a4j:commandLink>
                        </h:panelGroup>
                    </h:panelGrid>
                </h:panelGrid>
            </h:panelGrid>
        </h:form>
    </h:panelGrid>
    <jsp:include page="includes/include_modal_mensagem_generica.jsp" flush="true"/>
    <jsp:include page="includes/include_modal_vinculo_agenda.jsp"  flush="true"/>
    <jsp:include page="includes/autorizacao/include_autorizacao_funcionalidade.jsp" flush="true"/>
    <jsp:include page="includes/include_modal_validacaoCpf.jsp" flush="true" />
    <jsp:include page="includes/cliente/include_modal_capfoto_html5.jsp" flush="true"/>
    <jsp:include page="includes/include_modal_tipoProdutoAutorizacao.jsp" flush="true" />
</f:view>
<script>
    carregarTooltipsterClienteForm();

    function somenteNumeros(num) {
        var er = /[^0-9.]/;
        er.lastIndex = 0;
        var campo = num;
        if (er.test(campo.value)) {
            campo.value = "";
        }
    }
</script>
