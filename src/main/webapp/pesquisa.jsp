<%@page contentType="text/html" %>
<%@page pageEncoding="ISO-8859-1" %>
<head>
    <%@include file="/includes/include_import_minifiles.jsp" %>
    <script type="text/javascript" language="javascript" src="script/negociacaoContrato_1.0.min.js"></script>
    <script language="javascript" src="script/required.js" type="text/javascript"></script>
    <script type="text/javascript" language="javascript" src="bootstrap/jquery.js"></script>
</head>
<link rel="icon" type="image/png" href="./imagens/iconZW/favicon-32x32.png"/>
<link href="./css/otimize.css" rel="stylesheet" type="text/css">
<link href="beta/css/font-awesome_2.0.min.css" type="text/css" rel="stylesheet"/>
<link href="css/tooltipster/tooltipster.css" rel="stylesheet" type="text/css"/>
<link href="css/tooltipster/tooltipster-light.css" rel="stylesheet" type="text/css"/>


<script type="text/javascript" language="javascript" src="bootstrap/jquery.js"></script>
<script type="text/javascript" language="javascript" src="script/tooltipster/jquery.tooltipster.min.js"></script>

<script type="text/javascript" language="javascript" src="hoverform.js"></script>
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<link href="beta/css/pure-min.css" type="text/css" rel="stylesheet"/>
<link href="beta/css/bootstrap-tabs.css" type="text/css" rel="stylesheet"/>
<link href="beta/css/pure-ext.css" type="text/css" rel="stylesheet"/>
<script src="beta/js/DT_bootstrap.js" type="text/javascript"></script>
<script src="beta/js/bootstrap-tab.js" type="text/javascript"></script>


<script>
    jQuery.noConflict();
</script>
<%@taglib prefix="f" uri="http://java.sun.com/jsf/core" %>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html" %>
<%@taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>

<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax" %>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich" %>

<f:loadBundle var="msg_bt" basename="propriedades.Botoes"/>
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao"/>
<f:loadBundle var="msg" basename="propriedades.Mensagens"/>

<f:view>
    <c:if test="${!PesquisaVisualizacaoControle.visulizacao}">
        <jsp:include page="includes/include_carregando_ripple.jsp" flush="true"/>
    </c:if>
    <a4j:keepAlive beanName="PesquisaVisualizacaoControle"/>

    <title>${PesquisaVisualizacaoControle.questionarioClienteVO.questionario.tituloPesquisa}</title>

    <style>

        body, html {
            width: 100%;
            height: 100%;
        }

        .backgroundCor {
            background: ${PesquisaVisualizacaoControle.questionarioClienteVO.questionario.fundoCor};
        }

        .backgroundImagem {
            background: url(${PesquisaVisualizacaoControle.urlImagemFundo}) no-repeat center center fixed;
            -webkit-background-size: cover;
            -moz-background-size: cover;
            -o-background-size: cover;
            background-size: 100% 100%;
        }

        .panelGeralPesquisa {
            /*padding-bottom: 5%;*/
        }

        .panelInfoEmpresa {
            text-align: center;
            margin-top: 10%;
            padding-top: 5%;
        }

        .panelPesquisa {
            width: 50%;
            background: white;
            /*padding-top: 3%;*/
            -webkit-box-shadow: 0px 0px 30px -1px rgba(0, 0, 0, 0.2);
            -moz-box-shadow: 0px 0px 30px -1px rgba(0, 0, 0, 0.2);
            box-shadow: 0px 0px 30px -1px rgba(0, 0, 0, 0.2);
            -webkit-border-radius: 10px;
            -moz-border-radius: 10px;
            border-radius: 10px;
            /*padding-bottom: 3%;*/
            margin: auto;
        }

        .panelTituloPesquisa {
            text-align: center;
            margin-top: 4%;
        }

        .panelDescricaoPesquisa {
            padding-top: 4%;
            width: 80%;
            margin: auto;
        }

        .panelDescricaoPesquisaFinalizado {
            padding-top: 4%;
            width: 80%;
            margin: auto;
            padding-bottom: 5%;
        }

        .panelObrigatorioPesquisa {
            padding-top: 1%;
            padding-bottom: 1%;
            width: 80%;
            margin: auto;
        }

        .panelBtnEnviar {
            padding: 5%;
            margin-bottom: 10%;
        }

        .nomePesquisa {
            font-size: 34px;
            color: #333;
            font-weight: bold;
        }

        .descricaoInfoPergunta {
            font-size: 16px;
            color: #666666;
        }

        .descricaoMsgFinalizado {
            color: #333;
            font-size: 20px;
            line-height: 150%;
            width: 100%;
            font-weight: bold;
        }

        .textoObrigatorio {
            font-size: 12px;
        }

        .tituloPergunta {
            color: #333;
            font-size: 20px;
            line-height: 150%;
            width: 100%;
            font-weight: bold;
        }

        .opcoesPergunta {
            font-family: Arial;
            line-height: 140%;
            font-size: 14px;
            color: #29abe2;
            cursor: pointer;
        }

        .opcoesPergunta:hover {
            font-weight: bold;
        }

        .obrigatorio {
            color: #c5221f;
        }

        .panelOpcoesPergunta {
            margin-top: 1%;
            display: grid;
        }
        .panelOpcoesPerguntaNPS {
            margin-top: 1%;
            display: inline-flex;
        }

        .panelOpcaoPergunta {
            margin-top: 0.3%;
        }

        .required.missing {
            background-color: rgb(252, 232, 230);
        }

        .panelGeralPergunta {
            padding-top: 10px;
            padding-left: 5px;
            padding-right: 5px;
            padding-bottom: 10px;
            width: 80%;
            margin: auto;
        }

        .btnEnviar {
            font-size: 16px;
            padding: 2% 3% 2% 3%;
            border-radius: 7px;
            color: white;
            text-decoration: none;
            background-color: #094771;
        }

        .btnEnviar:hover {
            background-color: #073a5d;
            text-decoration: none;
            color: white;
        }

        @media screen and (max-width: 992px) {
            .panelPesquisa {
                width: 90%;
            }
        }

        @media screen and (max-width: 600px) {
            .panelPesquisa {
                width: 90%;
            }
        }


    </style>

    <h:form id="form">
        <html class="${empty PesquisaVisualizacaoControle.urlImagemFundo ? 'backgroundCor' : 'backgroundImagem'}">
        <body>

        <h:panelGroup layout="block" id="panelGeralPesquisa"
                      styleClass="panelGeralPesquisa">

            <h:panelGroup layout="block" id="panelPesquisaFinalizada"
                          rendered="#{PesquisaVisualizacaoControle.finalizado}"
                          styleClass="panelPesquisa">

                <h:panelGroup layout="block"
                              styleClass="panelInfoEmpresa">
                    <h:graphicImage style="width:200px;height:56px"
                                    value="#{PesquisaVisualizacaoControle.urlLogoEmpresa}">
                    </h:graphicImage>
                </h:panelGroup>

                <h:panelGroup layout="block"
                              styleClass="panelTituloPesquisa">
                    <h:outputText styleClass="nomePesquisa"
                                  value="#{PesquisaVisualizacaoControle.questionarioClienteVO.questionario.tituloPesquisa}"/>
                </h:panelGroup>

                <h:panelGroup layout="block"
                              rendered="#{not empty PesquisaVisualizacaoControle.questionarioClienteVO.questionario.textoFim}"
                              styleClass="panelDescricaoPesquisa">

                    <h:outputText styleClass="descricaoInfoPergunta"
                                  escape="false"
                                  value="#{PesquisaVisualizacaoControle.questionarioClienteVO.questionario.textoFim_Apresentar}"/>
                </h:panelGroup>

                <h:panelGroup layout="block"
                              styleClass="panelDescricaoPesquisaFinalizado">
                    <h:outputText styleClass="descricaoMsgFinalizado"
                                  escape="false"
                                  value="#{PesquisaVisualizacaoControle.msgFinalizado}"/>
                </h:panelGroup>

            </h:panelGroup>

            <h:panelGroup layout="block" id="panelPesquisa"
                          rendered="#{!PesquisaVisualizacaoControle.finalizado}"
                          styleClass="panelPesquisa">

                <h:panelGroup layout="block" id="panelInfoEmpresa"
                              styleClass="panelInfoEmpresa">
                    <h:graphicImage id="urlFotoEmpresa" style="width:200px;height:56px"
                                    value="#{PesquisaVisualizacaoControle.urlLogoEmpresa}">
                    </h:graphicImage>
                </h:panelGroup>

                <h:panelGroup layout="block" id="panelTituloPesquisa"
                              styleClass="panelTituloPesquisa">
                    <h:outputText styleClass="nomePesquisa"
                                  value="#{PesquisaVisualizacaoControle.questionarioClienteVO.questionario.tituloPesquisa}"/>
                </h:panelGroup>

                <h:panelGroup layout="block" id="panelDescricaoPesquisa"
                              rendered="#{not empty PesquisaVisualizacaoControle.questionarioClienteVO.questionario.textoInicio}"
                              styleClass="panelDescricaoPesquisa">

                    <h:outputText styleClass="descricaoInfoPergunta"
                                  escape="false"
                                  value="#{PesquisaVisualizacaoControle.questionarioClienteVO.questionario.textoInicio_Apresentar}"/>
                </h:panelGroup>

                <h:panelGroup layout="block" id="panelObrigatorioPesquisa"
                              rendered="#{PesquisaVisualizacaoControle.temPerguntaObrigatoria}"
                              styleClass="panelObrigatorioPesquisa">

                    <h:outputText styleClass="textoObrigatorio obrigatorio"
                                  value="*Obrigatório Responder"/>
                </h:panelGroup>

                <h:panelGroup layout="block" id="panelPerguntas">

                    <a4j:repeat
                            value="#{PesquisaVisualizacaoControle.questionarioClienteVO.questionarioPerguntaClienteVOs}"
                            var="pergunta">

                        <h:panelGroup layout="block"
                                      id="panelGeralPergunta"
                                      styleClass="required panel#{pergunta.perguntaCliente.campo}">

                            <h:panelGroup layout="block" styleClass="panelGeralPergunta">

                                <h:panelGroup layout="block" styleClass="tituloPergunta">

                                    <h:outputText title="#{pergunta.perguntaCliente.descricao_ApresentarPesquisa}"
                                                  value="#{pergunta.perguntaCliente.descricao_ApresentarPesquisa}"/>

                                    <h:outputText styleClass="obrigatorio"
                                                  rendered="#{pergunta.perguntaCliente.obrigatoria}"
                                                  value=" *"/>
                                </h:panelGroup>

                                <h:panelGroup layout="block" styleClass="panelOpcoesPergunta #{pergunta.perguntaCliente.nps ? 'panelOpcoesPerguntaNPS' : ''}" id="opcoesPergunta">

                                    <a4j:repeat value="#{pergunta.perguntaCliente.respostaPergClienteVOs}"
                                                var="repostaPergCliente">


                                        <h:panelGroup layout="block" styleClass="panelOpcaoPergunta" id="opcaoPergunta">

                                            <h:panelGroup layout="block" rendered="#{pergunta.perguntaCliente.multipla}"
                                                          style="display: inline-block;" styleClass="chk-fa-container">
                                                <div onclick="marcarCheckBoxPesquisa(this,event, false);"
                                                     class="font-size-em">
                                                    <h:outputText id="idPerguntaClienteMultipla"
                                                                  styleClass="opcoesPergunta #{repostaPergCliente.respostaOpcao ? 'fa-icon-check' : 'fa-icon-check-empty'}"
                                                                  escape="false"
                                                                  value="&nbsp;#{repostaPergCliente.descricaoRespota}"/>
                                                    <h:selectBooleanCheckbox value="#{repostaPergCliente.respostaOpcao}"
                                                                             style="display: none;"/>
                                                </div>
                                            </h:panelGroup>

                                            <h:panelGroup layout="block" rendered="#{pergunta.perguntaCliente.textual}">
                                                <h:inputTextarea id="idPerguntaClienteTextual"
                                                                 styleClass="inputTextClean"
                                                                 value="#{repostaPergCliente.descricaoRespota}"
                                                                 cols="50" rows="3"
                                                                 style="width:100%"
                                                                 onblur="blurinput(this);"
                                                                 onfocus="focusinput(this);"/>
                                            </h:panelGroup>

                                            <h:panelGroup layout="block" rendered="#{pergunta.perguntaCliente.simples}"
                                                          style="display: inline-block" styleClass="chk-fa-container">
                                                <div onclick="marcarRadioPesquisa(this, event);" class="font-size-em">
                                                    <h:outputText id="idPerguntaClienteSimples"
                                                                  styleClass="opcoesPergunta #{repostaPergCliente.respostaOpcao ? 'fa-icon-circle' : 'fa-icon-circle-blank'} text#{pergunta.perguntaCliente.campo}"
                                                                  escape="false"
                                                                  value="&nbsp;#{repostaPergCliente.descricaoRespota}"/>
                                                    <h:selectBooleanCheckbox value="#{repostaPergCliente.respostaOpcao}"
                                                                             styleClass="#{pergunta.perguntaCliente.campo}"
                                                                             style="display: none;"/>
                                                </div>
                                            </h:panelGroup>

                                        </h:panelGroup>

                                    </a4j:repeat>
                                    <a4j:repeat value="#{pergunta.perguntaCliente.respostaPergClienteVOs}"
                                                var="repostaPergCliente">
                                        <h:panelGroup layout="block" styleClass="panelOpcaoPergunta" id="opcaoPerguntaNPS">
                                            <h:panelGroup layout="block" rendered="#{pergunta.perguntaCliente.nps}"
                                                          style="display: inline-block" styleClass="chk-fa-container">
                                                <div onclick="marcarRadioPesquisa(this, event);" class="font-size-em">
                                                    <h:outputText id="idPerguntaClienteSimples"
                                                                  styleClass="opcoesPergunta #{repostaPergCliente.respostaOpcao ? 'fa-icon-circle' : 'fa-icon-circle-blank'} text#{pergunta.perguntaCliente.campo}"
                                                                  escape="false"
                                                                  style="padding-right: 5%"
                                                                  value="&nbsp;#{repostaPergCliente.descricaoRespota}"/>
                                                    <h:selectBooleanCheckbox value="#{repostaPergCliente.respostaOpcao}"
                                                                             styleClass="#{pergunta.perguntaCliente.campo}"
                                                                             style="display: none;"/>
                                                </div>
                                            </h:panelGroup>
                                        </h:panelGroup>
                                    </a4j:repeat>
                                </h:panelGroup>
                                <%--<h:panelGroup layout="block" styleClass="panelOpcoesPerguntaNPS" id="opcoesPerguntaNPS">

                                </h:panelGroup>--%>
                            </h:panelGroup>
                        </h:panelGroup>
                    </a4j:repeat>
                </h:panelGroup>


                <h:panelGroup layout="block" styleClass="panelBtnEnviar text-center">
                    <a4j:commandLink id="btnEnviar"
                                     value="ENVIAR"
                                     styleClass="btnEnviar"
                                     rendered="#{PesquisaVisualizacaoControle.clienteVO.codigo > 0}"
                                     action="#{PesquisaVisualizacaoControle.gravar}"
                                     oncomplete="#{PesquisaVisualizacaoControle.msgAlert}"
                                     reRender="form:panelGeralPesquisa"/>
                </h:panelGroup>

            </h:panelGroup>
        </h:panelGroup>

        </body>
        </html>

    </h:form>
</f:view>
<script type="text/javascript">

    function marcarRadioPesquisa(target, evt) {

        var chk = jQuery(target).children('input[type="checkbox"]');
        var span = jQuery(target).children('span');
        var buscar = (jQuery(chk).attr('class'));
        var elementosCheck = jQuery('.' + buscar);
        var elementosText = jQuery('.text' + buscar);

        elementosCheck.not(chk).prop('checked', false);
        elementosText.not(span).removeClass('fa-icon-circle');
        elementosText.not(span).addClass('fa-icon-circle-blank');

        marcarCheckBoxPesquisa(target, evt, true);
    }

    function marcarCheckBoxPesquisa(target, e, radio) {

        var val;
        var containerCheck = jQuery(target);
        var chk = jQuery(target).children('input[type="checkbox"]');
        val = chk.is(":checked");

        if (radio) {
            if (!val) {
                jQuery(containerCheck).children('span').addClass('fa-icon-circle');
                jQuery(containerCheck).children('span').removeClass('fa-icon-circle-blank');
                jQuery(chk).prop("checked", true);
            } else {
                jQuery(containerCheck).children('span').addClass('fa-icon-circle-blank');
                jQuery(containerCheck).children('span').removeClass('fa-icon-circle');
                jQuery(chk).prop("checked", false);
            }

        } else {

            if (!val) {
                jQuery(containerCheck).children('span').addClass('fa-icon-check');
                jQuery(containerCheck).children('span').removeClass('fa-icon-check-empty');
                jQuery(chk).attr("checked", true);
            } else {
                jQuery(containerCheck).children('span').addClass('fa-icon-check-empty');
                jQuery(containerCheck).children('span').removeClass('fa-icon-check');
                jQuery(chk).attr("checked", false);
            }

        }
    }

</script>