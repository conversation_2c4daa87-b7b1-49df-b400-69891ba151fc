<%@page contentType="text/html"%>
<%@page pageEncoding="ISO-8859-1"%>
<head><%@include file="/includes/include_import_minifiles.jsp" %></head>

<link href="./css/otimize.css" rel="stylesheet" type="text/css">
<script type="text/javascript" language="javascript" src="hoverform.js"></script>

<%@taglib prefix="f" uri="http://java.sun.com/jsf/core"%>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html"%>
<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax"%>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich"%>
<%@taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>

<f:loadBundle var="msg_bt" basename="propriedades.Botoes"/>
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao"/>
<f:loadBundle var="msg" basename="propriedades.Mensagens"/>

<meta name="viewport" content="width=device-width, initial-scale=1.0">
<link href="beta/css/pure-min.css" type="text/css" rel="stylesheet"/>
<link href="beta/css/bootstrap-tabs.css" type="text/css" rel="stylesheet"/>
<link href="beta/css/pure-ext.css" type="text/css" rel="stylesheet"/>
<link href="beta/css/font-awesome_2.0.min.css" type="text/css" rel="stylesheet"/>
<script src="beta/js/jquery.js" type="text/javascript"></script>
<script src="beta/js/jquery.dataTables.min.js" type="text/javascript"></script>
<script src="beta/js/DT_bootstrap.js" type="text/javascript"></script>
<script src="beta/js/bootstrap-tab.js" type="text/javascript"></script>
<script>
    jQuery.noConflict();
</script>
<style type="text/css">
    .colunaEsquerdaFiltrosPersonal{
        width: 100px;
    }
</style>
<f:view>
    <jsp:include page="includes/include_carregando_ripple.jsp" flush="true"/>
    <title>
        <h:outputText value="Relatório de Personal"/>
    </title>


    <rich:modalPanel id="panelDesconto" autosized="true" shadowOpacity="true" width="250" height="150">
        <f:facet name="header">
            <jsp:include page="topoReduzido.jsp"/>
        </f:facet>
    </rich:modalPanel>

    <rich:modalPanel id="panelpp" autosized="true" shadowOpacity="true" width="550" height="260">
        <f:facet name="header">
            <h:panelGroup>
                <h:outputText value="Produtos da Parcela #{GestaoPersonalControle.parcela.codigo}"/>
            </h:panelGroup>
        </f:facet>
        <f:facet name="controls">
            <h:panelGroup>
                <h:graphicImage value="/imagens/close.png" style="cursor:pointer" id="hiperlinkpp"/>
                <rich:componentControl for="panelpp" attachTo="hiperlinkpp" operation="hide" event="onclick"/>
            </h:panelGroup>
        </f:facet>
        <a4j:form id="formpp" ajaxSubmit="true" >
            <h:panelGrid columns="1" width="100%">
                <rich:dataTable id="listapp" width="100%" headerClass="consulta"
                                rowClasses="linhaImpar, linhaPar" columnClasses="colunaEsquerda, colunaEsquerda, colunaDireita"
                                value="#{GestaoPersonalControle.parcela.personal.alunos}" rows="7" var="aluno">
                    <rich:column>
                        <f:facet name="header">
                            <h:outputText value="#{msg_aplic.prt_GestaoPersonal_aluno}"/>
                        </f:facet>
                        <h:outputText value="#{aluno.aluno.pessoa.nome}"/>
                    </rich:column>
                    <rich:column>
                        <f:facet name="header">
                            <h:outputText value="#{msg_aplic.prt_GestaoPersonal_produto}"/>
                        </f:facet>
                        <h:outputText value="#{aluno.produto.descricao}"/>
                    </rich:column>
                    <rich:column>
                        <f:facet name="header">
                            <h:outputText value="#{msg_aplic.prt_GestaoPersonal_valorProduto} R$"/>
                        </f:facet>
                        <h:outputText value="#{aluno.valorFinal}">
                            <f:converter converterId="FormatadorNumerico"/>
                        </h:outputText>
                    </rich:column>
                </rich:dataTable>
                <rich:datascroller align="center" for="formpp:listapp" maxPages="10" id="scResultadopp"/>
            </h:panelGrid>
        </a4j:form>
    </rich:modalPanel>

    <h:form id="form" >
        <a4j:keepAlive beanName="ExportadorListaControle"/>
        <html>
        <jsp:include page="include_head.jsp" flush="true" />
        <body>

        <h:panelGroup layout="block" style="display: inline-table;width: 100%;" styleClass="fundoCinza">
            <h:panelGroup layout="block" styleClass="bgtop topoZW" rendered="#{MenuControle.apresentarTopo}">
                <jsp:include page="include_topo_novo.jsp" flush="true"/>
                <jsp:include page="include_menu_zw_flat.jsp" flush="true"/>
            </h:panelGroup>

            <h:panelGroup layout="block" styleClass="caixaCorpo">
                <h:panelGroup layout="block" style="height: 80%;width: 100%">
                    <h:panelGroup layout="block" styleClass="caixaMenuLatel">
                        <h:panelGroup layout="block" styleClass="container-imagem container-conteudo-central">
                            <h:panelGroup layout="block" styleClass="container-box zw_ui especial">
                            <h:panelGroup styleClass="container-box-header" layout="block">
                                <h:panelGroup layout="block" styleClass="margin-box">
                                    <h:outputText value="Relatório de Personal" styleClass="container-header-titulo"/>
                                    <h:outputLink styleClass="linkWiki"
                                                  value="#{SuperControle.urlWiki}Inicial:Relatorio_Personal"
                                                  title="Clique e saiba mais: Gestao de Personal Trainer" target="_blank">
                                        <i class="fa-icon-question-sign" style="font-size: 18px"></i>
                                    </h:outputLink>

                                </h:panelGroup>
                            </h:panelGroup>
                            <h:panelGroup layout="block" styleClass="margin-box">
                                <div class="sep" style="margin:4px 0 10px 0;"><img src="images/shim.gif"></div>
                                <h:panelGrid id="mensagem" columns="2" width="100%" styleClass="tabMensagens" style="margin:0 0 5px 0;">
                                    <h:panelGrid columns="1" width="100%">
                                        <h:outputText id="msgPersonal2" styleClass="mensagem" value="#{GestaoPersonalControle.mensagem}"/>
                                        <h:outputText id="msgPersonalDet2" styleClass="mensagemDetalhada" value="#{GestaoPersonalControle.mensagemDetalhada}"/>
                                    </h:panelGrid>
                                </h:panelGrid>
                                <h:panelGrid id="panelGeral" columns="2" width="100%" rowClasses="linhaImpar" columnClasses="colunaEsquerdaFiltrosPersonal" cellspacing="0" cellpadding="0">
                                    <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_GestaoPersonal_empresa}"
                                                  rendered="#{GestaoPersonalControle.apresentarEmpresa}"/>
                                    <h:panelGroup rendered="#{GestaoPersonalControle.apresentarEmpresa}">
                                        <h:selectOneMenu id="empresa" onblur="blurinput(this);" onfocus="focusinput(this);" styleClass="form"
                                                         value="#{GestaoPersonalControle.empresa.codigo}" >
                                            <f:selectItems value="#{GestaoPersonalControle.listaEmpresas}" />
                                            <a4j:support event="onchange" action="#{GestaoPersonalControle.limparDados}"
                                                         reRender="panelGeral, formProduto, formDesconto, formCliente, formPersonal"/>
                                        </h:selectOneMenu>
                                        <rich:spacer width="5" />
                                        <a4j:commandButton id="atualizar_empresa" action="#{GestaoPersonalControle.montarListaSelectItemEmpresa}"
                                                           style="vertical-align:middle;" image="imagens/atualizar.png" immediate="true"
                                                           ajaxSingle="true" reRender="form:empresa"/>
                                    </h:panelGroup>
                                    <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_GestaoPersonal_mes}" />
                                    <h:panelGroup id="panelMesReferencia">
                                        <h:inputText id="mesReferencia" size="15" readonly="true"
                                                     maxlength="15" onblur="blurinput(this);" onfocus="focusinput(this);" styleClass="form"
                                                     value="#{GestaoPersonalControle.apresentarMesReferencia}" />
                                        <rich:calendar id="calendario"
                                                       value="#{GestaoPersonalControle.mesReferencia}"
                                                       showInput="false"
                                                       zindex="2"
                                                       showWeeksBar="false">
                                            <a4j:support event="onchanged" reRender="panelMesReferencia"/>
                                        </rich:calendar>

                                        <h:panelGroup layout="block">
                                            <a4j:commandLink id="consultar" styleClass="pure-button pure-button-small"
                                                             action="#{GestaoPersonalControle.consultarRelatorio}"
                                                             title="#{msg.msg_consultar_dados}" accesskey="2"
                                                             reRender="relPersonais, totalLancado, totalPago, mensagem, mensagem2">
                                                <i class="fa-icon-search " ></i> &nbsp ${msg_bt.btn_consultar}
                                            </a4j:commandLink>
                                        </h:panelGroup>
                                    </h:panelGroup>

                                    <h:outputText styleClass="tituloCampos" value="Situação:"/>
                                    <h:panelGroup>
                                        <h:selectBooleanCheckbox value="#{GestaoPersonalControle.negociada}" style="vertical-align:text-bottom">
                                            <a4j:support event="onchange" status="statusHora"/>
                                        </h:selectBooleanCheckbox>
                                        <h:outputText value="Negociado" styleClass="text"/>
                                        <rich:spacer width="10"/>
                                        <h:selectBooleanCheckbox value="#{GestaoPersonalControle.paga}" style="vertical-align:text-bottom">
                                            <a4j:support event="onchange" status="statusHora"/>
                                        </h:selectBooleanCheckbox>
                                        <h:outputText styleClass="text" value="Pago"/>
                                        <rich:spacer width="10"/>
                                        <h:selectBooleanCheckbox value="#{GestaoPersonalControle.vencida}" style="vertical-align:text-bottom">
                                            <a4j:support event="onchange" status="statusHora"/>
                                        </h:selectBooleanCheckbox>
                                        <h:outputText styleClass="text" value="Vencido"/>
                                        <rich:spacer width="10"/>
                                        <h:selectBooleanCheckbox value="#{GestaoPersonalControle.livre}"  style="vertical-align:text-bottom">
                                            <a4j:support event="onchange" status="statusHora"/>
                                        </h:selectBooleanCheckbox>
                                        <h:outputText styleClass="text" value="Livre"/>
                                    </h:panelGroup>

                                </h:panelGrid>

                                <div class="sep" style="margin:4px 0 10px 0;"><img src="images/shim.gif"></div>
                                <rich:dataGrid id="relPersonais" value="#{GestaoPersonalControle.relatorioPersonais}" var="personal" columns="1">
                                    <h:outputText styleClass="titulo2" value="#{personal.personal.pessoa_Apresentar}"/>
                                    <rich:spacer width="10"/>
                                    <h:outputText styleClass="titulo3" rendered="#{personal.personal.diaVencimento != 0}" value="Dia Vencimento: #{personal.personal.diaVencimento}"/>
                                    <h:outputText styleClass="titulo3" rendered="#{personal.personal.diaVencimento == 0}" value="Dia Vencimento: #{GestaoPersonalControle.diaConfiguracaoSistema}"/>
                                    <h:panelGrid rendered="#{not empty personal.listaParcelas}" width="60%" columns="1" style="margin:5px 0 0 0;">
                                        <rich:dataTable width="100%" value="#{personal.listaParcelas}"
                                                        var="parcela" id="listaParcelas" headerClass="subordinado"
                                                        rowClasses="linhaImpar, linhaPar" rows="5"
                                                        styleClass="pure-table pure-table-horizontal pure-table-striped pure-table-links"
                                                        columnClasses="centralizado, colunaEsquerda, colunaDireita, centralizado, centralizado">
                                            <h:column>
                                                <f:facet name="header">
                                                    <h:outputText value="Parcela"/>
                                                </f:facet>
                                                <h:outputText value="#{parcela.codigo}"/>
                                            </h:column>
                                            <h:column>
                                                <f:facet name="header">
                                                    <h:outputText value="Descrição"/>
                                                </f:facet>
                                                <h:outputText value="#{parcela.descricao}"/>
                                            </h:column>
                                            <h:column>
                                                <f:facet name="header">
                                                    <h:outputText value="Valor R$"/>
                                                </f:facet>
                                                <h:outputText value="#{parcela.valorParcela}">
                                                    <f:converter converterId="FormatadorNumerico"/>
                                                </h:outputText>
                                            </h:column>
                                            <h:column>
                                                <f:facet name="header">
                                                    <h:outputText value="Vencimento"/>
                                                </f:facet>
                                                <h:outputText value="#{parcela.dataVencimento_Apresentar}"/>
                                            </h:column>
                                            <h:column>
                                                <f:facet name="header">
                                                    <h:outputText value="Opções"/>
                                                </f:facet>
                                                <h:panelGroup>
                                                    <a4j:commandButton id="visualizarAlunos" image="images/icon_cadastros.gif" title="Ver os Produtos Desta Parcela."
                                                                       oncomplete="Richfaces.showModalPanel('panelpp')" reRender="panelpp, formpp"
                                                                       action="#{GestaoPersonalControle.verLista}"/>
                                                </h:panelGroup>

                                            </h:column>
                                        </rich:dataTable>
                                        <rich:datascroller for="listaParcelas" maxPages="1000"/>
                                    </h:panelGrid>
                                    <h:panelGrid rendered="#{not empty personal.listaAlunos}" id="alunos" columns="1" width="100%" styleClass="tabFormSubordinada">
                                        <rich:dataTable id="listaAlunos" width="100%" headerClass="subordinado" rowClasses="linhaImpar, linhaPar"
                                                        columnClasses="colunaEsquerda, colunaEsquerda, colunaEsquerda, colunaDireita, colunaDireita, colunaDireita, colunaAlinhamento"
                                                        styleClass="pure-table pure-table-horizontal pure-table-striped pure-table-links"
                                                        value="#{personal.listaAlunos}" var="aluno">
                                            <h:column>
                                                <f:facet name="header">
                                                    <h:outputText value="#{msg_aplic.prt_GestaoPersonal_aluno}" />
                                                </f:facet>
                                                <h:outputText value="#{aluno.aluno.pessoa.nome}" />
                                            </h:column>
                                            <h:column>
                                                <f:facet name="header">
                                                    <h:outputText value="#{msg_aplic.prt_GestaoPersonal_produto}" />
                                                </f:facet>
                                                <h:outputText value="#{aluno.produto.descricao}" />
                                            </h:column>
                                            <h:column>
                                                <f:facet name="header">
                                                    <h:outputText value="#{msg_aplic.prt_GestaoPersonal_mes}" />
                                                </f:facet>
                                                <h:outputText value="#{GestaoPersonalControle.apresentarMesReferencia}" />
                                            </h:column>
                                            <h:column>
                                                <f:facet name="header">
                                                    <h:outputText value="#{msg_aplic.prt_GestaoPersonal_valorProduto} R$ " />
                                                </f:facet>

                                                <h:outputText value="#{aluno.produto.valorFinal}" >
                                                    <f:converter converterId="FormatadorNumerico"/>
                                                </h:outputText>
                                            </h:column>
                                            <h:column>
                                                <f:facet name="header">
                                                    <h:outputText value="#{msg_aplic.prt_GestaoPersonal_desconto} R$" />
                                                </f:facet>

                                                <h:outputText value="#{aluno.valorDesconto}" rendered="#{!aluno.mostrarDesconto}">
                                                    <f:converter converterId="FormatadorNumerico"/>
                                                </h:outputText>
                                                <h:outputText value=" %" rendered="#{aluno.produto.desconto.apresentarDescontoPorcentagem}"/>
                                                <h:panelGroup rendered="#{aluno.livre}">
                                                    <h:outputText >
                                                        <f:converter converterId="FormatadorNumerico"/>
                                                    </h:outputText><rich:spacer width="5" />
                                                </h:panelGroup>
                                            </h:column>
                                            <h:column>
                                                <f:facet name="header">
                                                    <h:outputText  value="#{msg_aplic.prt_GestaoPersonal_total} R$" />
                                                </f:facet>

                                                <h:outputText value="#{aluno.valorFinal}" >
                                                    <f:converter converterId="FormatadorNumerico"/>
                                                </h:outputText>
                                            </h:column>
                                            <h:column>
                                                <f:facet name="header">
                                                    <h:outputText value="Opções"/>
                                                </f:facet>
                                                <h:panelGroup rendered="#{aluno.gerado}">
                                                    <rich:spacer width="5"/>
                                                    <h:graphicImage id="parcelaGerada"
                                                                    url="images/quadrado_azul.gif"
                                                                    title="Parcela para este cliente está Gerada."/>
                                                </h:panelGroup>
                                                <h:panelGroup rendered="#{aluno.pago}">
                                                    <rich:spacer width="5"/>
                                                    <h:graphicImage id="parcelaPaga"
                                                                    url="images/quadrado_verde.gif"
                                                                    title="Parcela para este cliente está Paga."/>
                                                </h:panelGroup>
                                                <h:panelGroup rendered="#{aluno.vencido}">
                                                    <rich:spacer width="5"/>
                                                    <h:graphicImage id="parcelaVencida"
                                                                    url="images/quadrado_vermelho.gif"
                                                                    title="Parcela para este cliente está Vencida."/>
                                                </h:panelGroup>
                                                <h:panelGroup rendered="#{aluno.livre}">
                                                    <rich:spacer width="5"/>
                                                    <h:graphicImage id="naoNegociado"
                                                                    url="images/quadrado_cinza.gif"
                                                                    title="Aluno Não Negociado Ainda."/>
                                                </h:panelGroup>
                                            </h:column>
                                        </rich:dataTable>
                                    </h:panelGrid>
                                    <h:panelGrid rendered="#{empty personal.listaParcelas and empty personal.listaAlunos}"  id="mensagem" columns="1" width="100%" styleClass="tabFormSubordinada">
                                        <h:outputText styleClass="textblack" value="Não há parcelas a serem geradas para este Personal."/>
                                    </h:panelGrid>
                                    <br/>
                                    <br/>
                                </rich:dataGrid>

                                <h:panelGrid width="100%" columns="2" columnClasses="colunaDireita">
                                    <h:panelGroup>
                                        <h:outputText styleClass="tituloCampos" value="Legenda:"/>
                                        <rich:spacer width="10"/>
                                        <h:graphicImage url="images/quadrado_azul.gif" title="Parcela para este cliente está Gerada."/>
                                        <rich:spacer width="10"/>
                                        <h:outputText styleClass="tituloCampos" value="Negociado"/>
                                        <rich:spacer width="10"/>
                                        <h:graphicImage url="images/quadrado_verde.gif" title="Parcela para este cliente está Paga."/>
                                        <rich:spacer width="10"/>
                                        <h:outputText styleClass="tituloCampos" value="Pago"/>
                                        <rich:spacer width="10"/>
                                        <h:graphicImage url="images/quadrado_vermelho.gif" title="Parcela para este cliente está Vencida."/>
                                        <rich:spacer width="10"/>
                                        <h:outputText styleClass="tituloCampos" value="Vencido"/>
                                        <rich:spacer width="10"/>
                                        <h:graphicImage url="images/quadrado_cinza.gif" title="Aluno Não Negociado Ainda."/>
                                        <rich:spacer width="10"/>
                                        <h:outputText styleClass="tituloCampos" value="Livre"/>
                                        <rich:spacer width="10"/>
                                    </h:panelGroup>
                                </h:panelGrid>
                                <h:panelGroup layout="block" styleClass="pure-form pure-form-aligned pure-u-2-3">
                                    <a4j:commandLink id="btnExcel"
                                                     styleClass="pure-button pure-button-small"
                                                     actionListener="#{GestaoPersonalControle.exportarRelatorio}"
                                                     oncomplete="abrirPopup('UpdateServlet?op=downloadfile&file=#{ExportadorListaControle.fileName}&mimetype=application/vnd.ms-excel','Transacoes', 800,200);#{ExportadorListaControle.msgAlert}"
                                                     accesskey="3">
                                        <f:attribute name="tipo" value="xls"/>
                                        <f:attribute name="atributos" value="personal=Personal,nomeAluno=Aluno,descricaoProduto=Produto,mesReferencia=Mês,valorProduto=Valor,valorDesconto=Desconto,valorFinal=ValorFinal"/>
                                        <f:attribute name="prefixo" value="Personal"/>

                                        <i class="fa-icon-excel" ></i> &nbsp Excel
                                    </a4j:commandLink>

                                    <a4j:commandLink id="btnPDF"
                                                     styleClass="pure-button pure-button-small margin-h-10"
                                                     actionListener="#{GestaoPersonalControle.exportarRelatorio}"
                                                     oncomplete="abrirPopup('UpdateServlet?op=downloadfile&file=#{ExportadorListaControle.fileName}&mimetype=application/pdf','Transacoes', 800,200);#{ExportadorListaControle.msgAlert}"
                                                     accesskey="4">
                                        <f:attribute name="tipo" value="pdf"/>
                                        <f:attribute name="atributos" value="personal=Personal,nomeAluno=Aluno,descricaoProduto=Produto,mesReferencia=Mês,valorProduto=Valor,valorDesconto=Desconto,valorFinal=ValorFinal"/>
                                        <f:attribute name="prefixo" value="Personal"/>

                                        <i class="fa-icon-pdf" ></i> &nbsp PDF
                                    </a4j:commandLink>
                                </h:panelGroup>
                                <table width="100%" border="0" cellspacing="0" cellpadding="0" class="tablepadding2" >
                                    <tr>
                                        <td align="left" valign="top">
                                            <table id="tablepreviewtotal" width="100%" border="0" cellspacing="0" cellpadding="0" class="tablepreviewtotal">
                                                <tr>
                                                    <td width="50%" align="right" valign="middle">
                                                        <div>
                                                            Total Pago =
                                                                    <span class="verde">R$
                                                                        <h:outputText id="totalPago"
                                                                                      value="#{GestaoPersonalControle.valorPago}"><f:converter
                                                                                converterId="FormatadorNumerico"/></h:outputText>
                                                                    </span>
                                                        </div>
                                                        <br/>

                                                        <div>
                                                            Total Lan&ccedil;ado =
                                                                    <span class="verde">R$
                                                                        <h:outputText id="totalLancado"
                                                                                      value="#{GestaoPersonalControle.valorTotal}"><f:converter
                                                                                converterId="FormatadorNumerico"/></h:outputText>
                                                                    </span>
                                                        </div>
                                                    </td>
                                                </tr>
                                            </table>
                                        </td>
                                    </tr>
                                </table>
                            </h:panelGroup>
                            </h:panelGroup>
                        </h:panelGroup>
                        <jsp:include page="include_box_menulateral.jsp" flush="true"/>
                    </h:panelGroup>
                </h:panelGroup>
            </h:panelGroup>
            <jsp:include page="include_rodape_flat.jsp" flush="true" />
        </h:panelGroup>
        </body>
        </html>
    </h:form>
</f:view>
