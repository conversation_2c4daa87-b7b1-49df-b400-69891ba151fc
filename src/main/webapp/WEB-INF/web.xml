<?xml version="1.0" encoding="UTF-8"?>
<web-app version="3.0" xmlns="http://java.sun.com/xml/ns/javaee" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://java.sun.com/xml/ns/javaee            http://java.sun.com/xml/ns/javaee/web-app_3_0.xsd">
    <distributable/>
    <context-param>
        <param-name>com.sun.faces.sendPoweredByHeader</param-name>
        <param-value>false</param-value>
    </context-param>
    <context-param>
        <param-name>com.sun.faces.verifyObjects</param-name>
        <param-value>false</param-value>
    </context-param>
    <context-param>
        <param-name>com.sun.faces.validateXml</param-name>
        <param-value>true</param-value>
    </context-param>
    <context-param>
        <param-name>javax.faces.STATE_SAVING_METHOD</param-name>
        <param-value>server</param-value>
    </context-param>
    <context-param>
        <param-name>javax.faces.PARTIAL_STATE_SAVING</param-name>
        <param-value>false</param-value>
    </context-param>
    <context-param>
        <param-name>com.sun.faces.enableAgressiveSessionDirtying</param-name>
        <param-value>true</param-value>
    </context-param>
    <context-param>
        <param-name>javax.faces.CONFIG_FILES</param-name>
        <param-value>
            /WEB-INF/faces/faces-ce-config.xml,
            /WEB-INF/faces/faces-ce-navigation.xml,
            /WEB-INF/faces/faces-config.xml,
            /WEB-INF/faces/faces-navigation.xml,
            /WEB-INF/faces/faces-db-config.xml,
            /WEB-INF/faces/faces-finan-config.xml,
            /WEB-INF/faces/faces-estudio-navigation.xml,
            /WEB-INF/faces/faces-estudio-config.xml,
            /WEB-INF/faces/faces-estudio-converter.xml,
            /WEB-INF/faces/faces-finan-navigation.xml
        </param-value>
    </context-param>
    <context-param>
        <param-name>javax.faces.DISABLE_FACELET_JSF_VIEWHANDLER</param-name>
        <param-value>true</param-value>
    </context-param>
    <listener>
        <listener-class>com.sun.xml.ws.transport.http.servlet.WSServletContextListener</listener-class>
    </listener>
    <servlet>
        <servlet-name>Faces Servlet</servlet-name>
        <servlet-class>javax.faces.webapp.FacesServlet</servlet-class>
        <load-on-startup>1</load-on-startup>
    </servlet>
    <servlet>
        <servlet-name>RemessaServlet</servlet-name>
        <servlet-class>servlet.financeiro.RemessaServlet</servlet-class>
    </servlet>
    <servlet>
        <servlet-name>IntegracaoCadastrosWS</servlet-name>
        <servlet-class>com.sun.xml.ws.transport.http.servlet.WSServlet</servlet-class>
        <load-on-startup>1</load-on-startup>
    </servlet>
    <servlet>
        <servlet-name>ValidacaoAcessoWS</servlet-name>
        <servlet-class>com.sun.xml.ws.transport.http.servlet.WSServlet</servlet-class>
        <load-on-startup>1</load-on-startup>
    </servlet>
    <servlet>
        <servlet-name>NegociacaoWS</servlet-name>
        <servlet-class>com.sun.xml.ws.transport.http.servlet.WSServlet</servlet-class>
        <load-on-startup>1</load-on-startup>
    </servlet>
    <servlet>
        <servlet-name>RegistrarAcessoWS</servlet-name>
        <servlet-class>com.sun.xml.ws.transport.http.servlet.WSServlet</servlet-class>
        <load-on-startup>1</load-on-startup>
    </servlet>
    <servlet>
        <servlet-name>ConviteAulaExperimentalWS</servlet-name>
        <servlet-class>com.sun.xml.ws.transport.http.servlet.WSServlet</servlet-class>
        <load-on-startup>1</load-on-startup>
    </servlet>
    <servlet>
        <servlet-name>EmissorCupomWS</servlet-name>
        <servlet-class>com.sun.xml.ws.transport.http.servlet.WSServlet</servlet-class>
        <load-on-startup>1</load-on-startup>
    </servlet>
    <servlet>
        <servlet-name>IntegracaoNFSeWS</servlet-name>
        <servlet-class>com.sun.xml.ws.transport.http.servlet.WSServlet</servlet-class>
        <load-on-startup>1</load-on-startup>
    </servlet>
    <servlet>
        <servlet-name>AdmWS</servlet-name>
        <servlet-class>com.sun.xml.ws.transport.http.servlet.WSServlet</servlet-class>
        <load-on-startup>1</load-on-startup>
    </servlet>
    <servlet>
        <servlet-name>FinanceiroWS</servlet-name>
        <servlet-class>com.sun.xml.ws.transport.http.servlet.WSServlet</servlet-class>
        <load-on-startup>1</load-on-startup>
    </servlet>
    <servlet>
        <servlet-name>IntegracaoTurmasWS</servlet-name>
        <servlet-class>com.sun.xml.ws.transport.http.servlet.WSServlet</servlet-class>
        <load-on-startup>1</load-on-startup>
    </servlet>
    <servlet-mapping>
        <servlet-name>Faces Servlet</servlet-name>
        <url-pattern>/faces/*</url-pattern>
    </servlet-mapping>
    <!-- comentar aki para glassfish
   <listener>
       <listener-class>com.sun.xml.ws.transport.http.servlet.WSServletContextListener</listener-class>
   </listener>
   <servlet>
        <servlet-name>IntegracaoNFSeWS</servlet-name>
        <servlet-class>com.sun.xml.ws.transport.http.servlet.WSServlet</servlet-class>
        <load-on-startup>1</load-on-startup>
    </servlet>
   <servlet>
       <servlet-name>IntegracaoCadastrosWS</servlet-name>
       <servlet-class>com.sun.xml.ws.transport.http.servlet.WSServlet</servlet-class>
       <load-on-startup>1</load-on-startup>
   </servlet>
   <servlet>
       <servlet-name>IntegracaoTurmasWS</servlet-name>
       <servlet-class>com.sun.xml.ws.transport.http.servlet.WSServlet</servlet-class>
       <load-on-startup>1</load-on-startup>
   </servlet>
   <servlet>
       <servlet-name>ConviteAulaExperimentalWS</servlet-name>
       <servlet-class>com.sun.xml.ws.transport.http.servlet.WSServlet</servlet-class>
       <load-on-startup>1</load-on-startup>
   </servlet>

   <servlet>
       <servlet-name>ValidacaoAcessoWS</servlet-name>
       <servlet-class>com.sun.xml.ws.transport.http.servlet.WSServlet</servlet-class>
       <load-on-startup>1</load-on-startup>
   </servlet>
   <servlet>
       <servlet-name>RegistrarAcessoWS</servlet-name>
       <servlet-class>com.sun.xml.ws.transport.http.servlet.WSServlet</servlet-class>
       <load-on-startup>1</load-on-startup>
   </servlet>
   <servlet>
       <servlet-name>EmissorCupomWS</servlet-name>
       <servlet-class>com.sun.xml.ws.transport.http.servlet.WSServlet</servlet-class>
       <load-on-startup>1</load-on-startup>
   </servlet>
   <servlet>
       <servlet-name>AdmWS</servlet-name>
       <servlet-class>com.sun.xml.ws.transport.http.servlet.WSServlet</servlet-class>
       <load-on-startup>1</load-on-startup>
   </servlet>
   <servlet>
       <servlet-name>NegociacaoWS</servlet-name>
       <servlet-class>com.sun.xml.ws.transport.http.servlet.WSServlet</servlet-class>
       <load-on-startup>1</load-on-startup>
   </servlet>
    <servlet>
        <servlet-name>FinanceiroWS</servlet-name>
        <servlet-class>com.sun.xml.ws.transport.http.servlet.WSServlet</servlet-class>
        <load-on-startup>1</load-on-startup>
    </servlet>
   <servlet-mapping>
       <servlet-name>IntegracaoCadastrosWS</servlet-name>
       <url-pattern>/IntegracaoCadastrosWS</url-pattern>
   </servlet-mapping>
   <servlet-mapping>
       <servlet-name>IntegracaoNFSeWS</servlet-name>
       <url-pattern>/IntegracaoNFSeWS</url-pattern>
   </servlet-mapping>
   <servlet-mapping>
       <servlet-name>IntegracaoTurmasWS</servlet-name>
       <url-pattern>/IntegracaoTurmasWS</url-pattern>
   </servlet-mapping>
   <servlet-mapping>
       <servlet-name>ConviteAulaExperimentalWS</servlet-name>
       <url-pattern>/ConviteAulaExperimentalWS</url-pattern>
   </servlet-mapping>

   <servlet-mapping>
       <servlet-name>ValidacaoAcessoWS</servlet-name>
       <url-pattern>/ValidacaoAcessoWS</url-pattern>
   </servlet-mapping>
   <servlet-mapping>
       <servlet-name>RegistrarAcessoWS</servlet-name>
       <url-pattern>/RegistrarAcessoWS</url-pattern>
   </servlet-mapping>
   <servlet-mapping>
       <servlet-name>EmissorCupomWS</servlet-name>
       <url-pattern>/EmissorCupomWS</url-pattern>
   </servlet-mapping>
   <servlet-mapping>
       <servlet-name>AdmWS</servlet-name>
       <url-pattern>/AdmWS</url-pattern>
   </servlet-mapping>
   <servlet-mapping>
       <servlet-name>NegociacaoWS</servlet-name>
       <url-pattern>/NegociacaoWS</url-pattern>
   </servlet-mapping>

    <servlet-mapping>
        <servlet-name>FinanceiroWS</servlet-name>
        <url-pattern>/FinanceiroWS</url-pattern>
    </servlet-mapping>-->
    <!-- Quando alterar esse tempo, lembre-se de alterar a variavel 'tempoEmMillis' no arquivo 'time.js'-->
    <servlet-mapping>
        <servlet-name>RemessaServlet</servlet-name>
        <url-pattern>/RemessaServlet</url-pattern>
    </servlet-mapping>
    <servlet-mapping>
        <servlet-name>IntegracaoCadastrosWS</servlet-name>
        <url-pattern>/IntegracaoCadastrosWS</url-pattern>
    </servlet-mapping>
    <servlet-mapping>
        <servlet-name>ValidacaoAcessoWS</servlet-name>
        <url-pattern>/ValidacaoAcessoWS</url-pattern>
    </servlet-mapping>
    <servlet-mapping>
        <servlet-name>NegociacaoWS</servlet-name>
        <url-pattern>/NegociacaoWS</url-pattern>
    </servlet-mapping>
    <servlet-mapping>
        <servlet-name>RegistrarAcessoWS</servlet-name>
        <url-pattern>/RegistrarAcessoWS</url-pattern>
    </servlet-mapping>
    <servlet-mapping>
        <servlet-name>ConviteAulaExperimentalWS</servlet-name>
        <url-pattern>/ConviteAulaExperimentalWS</url-pattern>
    </servlet-mapping>
    <servlet-mapping>
        <servlet-name>EmissorCupomWS</servlet-name>
        <url-pattern>/EmissorCupomWS</url-pattern>
    </servlet-mapping>
    <servlet-mapping>
        <servlet-name>IntegracaoNFSeWS</servlet-name>
        <url-pattern>/IntegracaoNFSeWS</url-pattern>
    </servlet-mapping>
    <servlet-mapping>
        <servlet-name>AdmWS</servlet-name>
        <url-pattern>/AdmWS</url-pattern>
    </servlet-mapping>
    <servlet-mapping>
        <servlet-name>FinanceiroWS</servlet-name>
        <url-pattern>/FinanceiroWS</url-pattern>
    </servlet-mapping>
    <servlet-mapping>
        <servlet-name>IntegracaoTurmasWS</servlet-name>
        <url-pattern>/IntegracaoTurmasWS</url-pattern>
    </servlet-mapping>
    <session-config>
        <session-timeout>120</session-timeout>
    </session-config>
    <welcome-file-list>
        <welcome-file>inicio.jsp</welcome-file>
    </welcome-file-list>
    <listener>
        <listener-class>com.sun.faces.config.ConfigureListener</listener-class>
    </listener>
    <listener>
        <listener-class>controle.arquitetura.session.listener.SessionListener</listener-class>
    </listener>
    <listener>
        <display-name>Oamd Context Factory Listener</display-name>
        <listener-class>br.com.pactosolucoes.oamd.controle.basico.OamdContextListener</listener-class>
    </listener>
    <context-param>
        <param-name>org.richfaces.SKIN</param-name>
        <param-value>#{SkinControle.skin}</param-value>
    </context-param>
    <!--parametro que habilita usar controle de aparencia do richfaces-->
    <context-param>
        <param-name>org.richfaces.CONTROL_SKINNING</param-name>
        <param-value>enable</param-value>
    </context-param>
    <filter>
        <display-name>Ajax4jsf Filter</display-name>
        <filter-name>ajax4jsf</filter-name>
        <filter-class>org.ajax4jsf.Filter</filter-class>
    </filter>
    <filter-mapping>
        <filter-name>ajax4jsf</filter-name>
        <servlet-name>Faces Servlet</servlet-name>
        <dispatcher>REQUEST</dispatcher>
        <dispatcher>FORWARD</dispatcher>
        <dispatcher>INCLUDE</dispatcher>
    </filter-mapping>
    <!-- Filtro para evitar sessão expirada-->
    <filter>
        <filter-name>SessionFilter</filter-name>
        <filter-class>controle.arquitetura.filter.SessionFilter</filter-class>
    </filter>
    <filter>
        <filter-name>CacheControlFilter</filter-name>
        <filter-class>controle.arquitetura.filter.CacheControlFilter</filter-class>
    </filter>
    <filter-mapping>
        <filter-name>CacheControlFilter</filter-name>
        <url-pattern>*.jpg</url-pattern>
        <url-pattern>*.jpeg</url-pattern>
        <url-pattern>*.svg</url-pattern>
        <url-pattern>*.gif</url-pattern>
        <url-pattern>*.png</url-pattern>
        <url-pattern>*.css</url-pattern>
        <url-pattern>*.otf</url-pattern>
        <url-pattern>*.ttf</url-pattern>
    </filter-mapping>

    <filter-mapping>
        <filter-name>SessionFilter</filter-name>
        <url-pattern>*.jsp</url-pattern>
    </filter-mapping>
    <filter>
        <filter-name>CorsFilter</filter-name>
        <filter-class>controle.arquitetura.filter.CorsFilter</filter-class>
    </filter>
    <filter-mapping>
        <filter-name>CorsFilter</filter-name>
        <url-pattern>/prest/validar-permissao/*</url-pattern>
    </filter-mapping>
    <filter-mapping>
        <filter-name>CorsFilter</filter-name>
        <url-pattern>/prest/integracao/*</url-pattern>
    </filter-mapping>
    <filter-mapping>
        <filter-name>CorsFilter</filter-name>
        <url-pattern>/prest/pacto/*</url-pattern>
    </filter-mapping>
<!--    Recurso foi desabilitado porque é preciso terminar uma implementação se envio de token no treino-->
<!--    Este recurso está sendo feito pelo João Alcides, e retornaremos com este recurso após sua conclusão-->
<!--    <filter>-->
<!--        <filter-name>RequisicaoFilter</filter-name>-->
<!--        <filter-class>controle.arquitetura.filter.RequisicaoFilter</filter-class>-->
<!--    </filter>-->
<!--    <filter-mapping>-->
<!--        <filter-name>RequisicaoFilter</filter-name>-->
<!--        <url-pattern>/prest/*</url-pattern>-->
<!--    </filter-mapping>-->
    <filter-mapping>
        <filter-name>CorsFilter</filter-name>
        <url-pattern>/prest/tela-cliente/*</url-pattern>
    </filter-mapping>
    <filter-mapping>
        <filter-name>CorsFilter</filter-name>
        <url-pattern>/prest/validar-permissao/*</url-pattern>
    </filter-mapping>
    <filter-mapping>
        <filter-name>CorsFilter</filter-name>
        <url-pattern>/prest/contratoassinatura/*</url-pattern>
    </filter-mapping>
    <servlet>
        <servlet-name>DownloadSV</servlet-name>
        <servlet-class>controle.arquitetura.servlet.DownloadServlet</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>DownloadSV</servlet-name>
        <url-pattern>/DownloadSV</url-pattern>
    </servlet-mapping>
    <servlet>
        <servlet-name>VisualizadorRelatorio</servlet-name>
        <servlet-class>relatorio.arquitetura.VisualizadorRelatorio</servlet-class>
    </servlet>
    <servlet>
        <servlet-name>VisualizarContrato</servlet-name>
        <servlet-class>controle.plano.VisualizarContrato</servlet-class>
    </servlet>
    <servlet>
        <servlet-name>ImageServlet</servlet-name>
        <servlet-class>controle.arquitetura.servlet.ImageServlet</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>ImageServlet</servlet-name>
        <url-pattern>/relatorio/image</url-pattern>
    </servlet-mapping>
    <servlet>
        <servlet-name>ImageServletAcesso</servlet-name>
        <servlet-class>acesso.webservice.ImageServletAcesso</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>ImageServletAcesso</servlet-name>
        <url-pattern>/acesso</url-pattern>
    </servlet-mapping>
    <servlet-mapping>
        <servlet-name>ImageServlet</servlet-name>
        <url-pattern>/image</url-pattern>
    </servlet-mapping>
    <servlet>
        <servlet-name>ImageServletRedeSocial</servlet-name>
        <servlet-class>acesso.webservice.ImageServletRedeSocial</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>ImageServletRedeSocial</servlet-name>
        <url-pattern>/imageredesocial</url-pattern>
    </servlet-mapping>
    <servlet-mapping>
        <servlet-name>VisualizadorRelatorio</servlet-name>
        <url-pattern>/VisualizadorRelatorio</url-pattern>
    </servlet-mapping>
    <servlet-mapping>
        <servlet-name>VisualizarContrato</servlet-name>
        <url-pattern>/VisualizarContrato</url-pattern>
    </servlet-mapping>
    <servlet>
        <servlet-name>UpdateServlet</servlet-name>
        <servlet-class>controle.arquitetura.servlet.update.UpdateServlet</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>UpdateServlet</servlet-name>
        <url-pattern>/UpdateServlet</url-pattern>
    </servlet-mapping>
    <servlet>
        <servlet-name>SincronizarUsuarioServlet</servlet-name>
        <servlet-class>controle.arquitetura.servlet.SincronizarUsuarioServlet</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>SincronizarUsuarioServlet</servlet-name>
        <url-pattern>/sincronizarUsuario</url-pattern>
    </servlet-mapping>
    <servlet>
        <servlet-name>GameServlet</servlet-name>
        <servlet-class>controle.arquitetura.servlet.GameServlet</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>GameServlet</servlet-name>
        <url-pattern>/GameServlet</url-pattern>
    </servlet-mapping>
    <servlet>
        <servlet-name>MailingServlet</servlet-name>
        <servlet-class>controle.arquitetura.servlet.MailingServlet</servlet-class>
    </servlet>
    <servlet>
        <servlet-name>OIDServlet</servlet-name>
        <servlet-class>controle.arquitetura.servlet.OIDServlet</servlet-class>
        <load-on-startup>1</load-on-startup>
        <async-supported>false</async-supported>
    </servlet>
    <servlet>
        <servlet-name>RedirectServlet</servlet-name>
        <servlet-class>controle.arquitetura.servlet.RedirectServlet</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>MailingServlet</servlet-name>
        <url-pattern>/ms</url-pattern>
    </servlet-mapping>

    <servlet>
        <servlet-name>PactoPayComunicacaoServlet</servlet-name>
        <servlet-class>controle.arquitetura.servlet.PactoPayComunicacaoServlet</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>PactoPayComunicacaoServlet</servlet-name>
        <url-pattern>/pactopay/comunicacao</url-pattern>
    </servlet-mapping>

    <servlet>
        <servlet-name>PactoPayComunicacaoEmailServlet</servlet-name>
        <servlet-class>controle.arquitetura.servlet.PactoPayComunicacaoEmailServlet</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>PactoPayComunicacaoEmailServlet</servlet-name>
        <url-pattern>/pactopay/comunicacaoemail</url-pattern>
    </servlet-mapping>

    <servlet>
        <servlet-name>EnvioRelatorioServlet</servlet-name>
        <servlet-class>controle.arquitetura.servlet.EnvioRelatorioServlet</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>EnvioRelatorioServlet</servlet-name>
        <url-pattern>/envioRelatorio</url-pattern>
    </servlet-mapping>
    <servlet>
        <servlet-name>AvatarServlet</servlet-name>
        <servlet-class>controle.arquitetura.servlet.AvatarServlet</servlet-class>
    </servlet>
    <servlet>
        <servlet-name>WebHookNotaServlet</servlet-name>
        <servlet-class>controle.arquitetura.servlet.WebHookNotaServlet</servlet-class>
    </servlet>
    <servlet>
        <servlet-name>WebHookEnotasServlet</servlet-name>
        <servlet-class>servlet.enotas.WebHookEnotasServlet</servlet-class>
    </servlet>
    <servlet>
        <servlet-name>WebHookIAServlet</servlet-name>
        <servlet-class>servlet.ia.WebHookIAServlet</servlet-class>
    </servlet>
    <servlet>
        <servlet-name>NotaFiscalServlet</servlet-name>
        <servlet-class>servlet.notaFiscal.NotaFiscalServlet</servlet-class>
    </servlet>
    <servlet>
        <servlet-name>NFSeServlet</servlet-name>
        <servlet-class>controle.arquitetura.servlet.NFSeServlet</servlet-class>
    </servlet>
    <servlet>
        <servlet-name>NFCeServlet</servlet-name>
        <servlet-class>controle.arquitetura.servlet.NFCeServlet</servlet-class>
    </servlet>
    <servlet>
        <servlet-name>ModuloNotasServlet</servlet-name>
        <servlet-class>controle.arquitetura.servlet.ModuloNotasServlet</servlet-class>
    </servlet>
    <servlet>
        <servlet-name>ImpressaoServlet</servlet-name>
        <servlet-class>controle.arquitetura.servlet.ImpressaoServlet</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>AvatarServlet</servlet-name>
        <url-pattern>/av</url-pattern>
    </servlet-mapping>
    <servlet-mapping>
        <servlet-name>WebHookEnotasServlet</servlet-name>
        <url-pattern>/retorno/notaFiscal</url-pattern>
    </servlet-mapping>
    <servlet-mapping>
        <servlet-name>WebHookIAServlet</servlet-name>
        <url-pattern>/riscoEvasao</url-pattern>
    </servlet-mapping>
    <servlet-mapping>
        <servlet-name>NotaFiscalServlet</servlet-name>
        <url-pattern>/notaFiscal</url-pattern>
    </servlet-mapping>
    <servlet-mapping>
        <servlet-name>WebHookNotaServlet</servlet-name>
        <url-pattern>/retorno/nota</url-pattern>
    </servlet-mapping>
    <servlet-mapping>
        <servlet-name>NFSeServlet</servlet-name>
        <url-pattern>/nota</url-pattern>
    </servlet-mapping>
    <servlet-mapping>
        <servlet-name>NFCeServlet</servlet-name>
        <url-pattern>/nfce</url-pattern>
    </servlet-mapping>
    <servlet-mapping>
        <servlet-name>ModuloNotasServlet</servlet-name>
        <url-pattern>/notaFiscalEmpresa</url-pattern>
    </servlet-mapping>
    <servlet-mapping>
        <servlet-name>ImpressaoServlet</servlet-name>
        <url-pattern>/impressao</url-pattern>
    </servlet-mapping>
    <servlet-mapping>
        <servlet-name>OIDServlet</servlet-name>
        <url-pattern>/oid</url-pattern>
    </servlet-mapping>
    <servlet-mapping>
        <servlet-name>RedirectServlet</servlet-name>
        <url-pattern>/redir</url-pattern>
    </servlet-mapping>
    <servlet>
        <servlet-name>MidiaZWInternalServlet</servlet-name>
        <servlet-class>servicos.operacoes.midias.zwinternal.MidiaZWInternalServlet</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>MidiaZWInternalServlet</servlet-name>
        <url-pattern>/zw-photos/*</url-pattern>
    </servlet-mapping>
    <!--
        Inicio Servlets para consultas;
    -->
    <!--
        Inicio Servlets do pacote acesso;
    -->
    <servlet>
        <servlet-name>LocalAcessoServlet</servlet-name>
        <servlet-class>servlet.acesso.LocalAcessoServlet</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>LocalAcessoServlet</servlet-name>
        <url-pattern>/prest/acesso/localAcesso</url-pattern>
    </servlet-mapping>
    <servlet>
        <servlet-name>AutorizacaoAcessoGrupoEmpresarialServlet</servlet-name>
        <servlet-class>servlet.acesso.AutorizacaoAcessoGrupoEmpresarialServlet</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>AutorizacaoAcessoGrupoEmpresarialServlet</servlet-name>
        <url-pattern>/prest/acesso/autorizacaoAcessoGrupoEmpresarialServlet</url-pattern>
    </servlet-mapping>
    <servlet-mapping>
        <servlet-name>AutorizacaoAcessoGrupoEmpresarialServlet</servlet-name>
        <url-pattern>/prest/autorizacaoAcesso/findAutorizacaoAcesso</url-pattern>
    </servlet-mapping>

    <servlet>
        <servlet-name>IntegracaoAcessoGrupoEmpresarialServlet</servlet-name>
        <servlet-class>servlet.acesso.IntegracaoAcessoGrupoEmpresarialServlet</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>IntegracaoAcessoGrupoEmpresarialServlet</servlet-name>
        <url-pattern>/prest/acesso/integracaoAcessoGrupoEmpresarialServlet</url-pattern>
    </servlet-mapping>
    <servlet>
        <servlet-name>LogServlet</servlet-name>
        <servlet-class>servlet.acesso.LogServlet</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>LogServlet</servlet-name>
        <url-pattern>/prest/acesso/log</url-pattern>
    </servlet-mapping>
    <servlet>
        <servlet-name>ServidorFacialServlet</servlet-name>
        <servlet-class>servlet.acesso.ServidorFacialServlet</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>ServidorFacialServlet</servlet-name>
        <url-pattern>/prest/acesso/servidorFacial</url-pattern>
    </servlet-mapping>
    <!--
        Final Servlets do pacote acesso;
    -->
    <!--
        Inicio Servlets do pacote arquitetura;
    -->
    <servlet>
        <servlet-name>AtualizaDBServlet</servlet-name>
        <servlet-class>servlet.arquitetura.AtualizaDBServlet</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>AtualizaDBServlet</servlet-name>
        <url-pattern>/prest/atualizarbd</url-pattern>
    </servlet-mapping>
    <servlet>
        <servlet-name>AtualizadorBDServlet</servlet-name>
        <servlet-class>servlet.arquitetura.AtualizadorBDServlet</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>AtualizadorBDServlet</servlet-name>
        <url-pattern>/prest/arquitetura/atualizadorBD</url-pattern>
    </servlet-mapping>

    <servlet>
        <servlet-name>ConsultaClienteServlet</servlet-name>
        <servlet-class>servlet.arquitetura.ConsultaClienteServlet</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>ConsultaClienteServlet</servlet-name>
        <url-pattern>/prest/cliente/search</url-pattern>
    </servlet-mapping>

    <servlet>
        <servlet-name>PerfilAcessoServlet</servlet-name>
        <servlet-class>servlet.arquitetura.PerfilAcessoServlet</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>PerfilAcessoServlet</servlet-name>
        <url-pattern>/prest/arquitetura/perfilAcesso</url-pattern>
    </servlet-mapping>
    <servlet>
        <servlet-name>UsuarioServlet</servlet-name>
        <servlet-class>servlet.arquitetura.UsuarioServlet</servlet-class>
    </servlet>
    <servlet>
        <servlet-name>UsuarioServletV2</servlet-name>
        <servlet-class>servlet.arquitetura.UsuarioServletV2</servlet-class>
    </servlet>
    <servlet>
        <servlet-name>HorarioAcessoServlet</servlet-name>
        <servlet-class>servlet.appConsultor.HorarioAcessoServlet</servlet-class>
    </servlet>
    <servlet>
        <servlet-name>StoneOpenBankServletControle</servlet-name>
        <servlet-class>br.com.pactosolucoes.controle.json.pactobank.StoneOpenBankServletControle</servlet-class>
    </servlet>
    <servlet>
        <servlet-name>RDStationLeadServletControle</servlet-name>
        <servlet-class>br.com.pactosolucoes.controle.json.crm.RDStationLeadServletControle</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>UsuarioServlet</servlet-name>
        <url-pattern>/prest/arquitetura/usuario</url-pattern>
    </servlet-mapping>
    <servlet-mapping>
        <servlet-name>UsuarioServletV2</servlet-name>
        <url-pattern>/prest/auth/usuario</url-pattern>
    </servlet-mapping>
    <servlet-mapping>
        <servlet-name>UsuarioServletV2</servlet-name>
        <url-pattern>/prest/auth/usuario/*</url-pattern>
    </servlet-mapping>
    <servlet-mapping>
        <servlet-name>HorarioAcessoServlet</servlet-name>
        <url-pattern>/prest/horarioacesso</url-pattern>
    </servlet-mapping>

    <servlet>
        <servlet-name>UsuarioServletV3</servlet-name>
        <servlet-class>servlet.arquitetura.UsuarioServletV3</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>UsuarioServletV3</servlet-name>
        <url-pattern>/insec/validateToken</url-pattern>
    </servlet-mapping>

    <servlet>
        <servlet-name>ObjecaoRegistroContatoAIServlet</servlet-name>
        <servlet-class>servlet.arquitetura.ObjecaoRegistroContatoAIServlet</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>ObjecaoRegistroContatoAIServlet</servlet-name>
        <url-pattern>/ai/resgisto/contato/objecao</url-pattern>
    </servlet-mapping>

    <servlet>
        <servlet-name>SimplesRegistroContatoAIServlet</servlet-name>
        <servlet-class>servlet.arquitetura.SimplesRegistroContatoAIServlet</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>SimplesRegistroContatoAIServlet</servlet-name>
        <url-pattern>/ai/resgisto/contato</url-pattern>
    </servlet-mapping>

    <servlet>
        <servlet-name>AgendamentoRegistroContatoAIServlet</servlet-name>
        <servlet-class>servlet.arquitetura.AgendamentoRegistroContatoAIServlet</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>AgendamentoRegistroContatoAIServlet</servlet-name>
        <url-pattern>/ai/resgisto/contato/agendamento</url-pattern>
    </servlet-mapping>

    <servlet>
        <servlet-name>AdmServlet</servlet-name>
        <servlet-class>servlet.arquitetura.AdmServlet</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>AdmServlet</servlet-name>
        <url-pattern>/insec/adm</url-pattern>
    </servlet-mapping>

    <servlet>
        <servlet-name>UsuarioLoginServlet</servlet-name>
        <servlet-class>servlet.arquitetura.UsuarioLoginServlet</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>UsuarioLoginServlet</servlet-name>
        <url-pattern>/prest/auth/v2/usuario</url-pattern>
    </servlet-mapping>

    <servlet>
        <servlet-name>UsuarioNewLoginServlet</servlet-name>
        <servlet-class>servlet.arquitetura.UsuarioNewLoginServlet</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>UsuarioNewLoginServlet</servlet-name>
        <url-pattern>/prest/auth/v2/usuario/sincronizarNovoLogin</url-pattern>
    </servlet-mapping>

    <servlet>
        <servlet-name>RecursoEmpresaServlet</servlet-name>
        <servlet-class>servlet.arquitetura.RecursoEmpresaServlet</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>RecursoEmpresaServlet</servlet-name>
        <url-pattern>/insec/recursoEmpresa</url-pattern>
    </servlet-mapping>


    <servlet>
        <servlet-name>NotificacaoRecursoEmpresaServlet</servlet-name>
        <servlet-class>servlet.arquitetura.NotificacaoRecursoEmpresaServlet</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>NotificacaoRecursoEmpresaServlet</servlet-name>
        <url-pattern>/prest/recursoempresa</url-pattern>
    </servlet-mapping>


    <servlet-mapping>
        <servlet-name>StoneOpenBankServletControle</servlet-name>
        <url-pattern>/prest/pactobank/stone</url-pattern>
    </servlet-mapping>
    <servlet-mapping>
        <servlet-name>RDStationLeadServletControle</servlet-name>
        <url-pattern>/prest/lead/rdstation</url-pattern>
    </servlet-mapping>

    <servlet>
        <servlet-name>TestersServlet</servlet-name>
        <servlet-class>servlet.arquitetura.TestersServlet</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>TestersServlet</servlet-name>
        <url-pattern>/prest/testers</url-pattern>
    </servlet-mapping>


    <servlet>
        <servlet-name>ConfigCRMRest</servlet-name>
        <servlet-class>servlet.crm.ConfigCRMRestServlet</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>ConfigCRMRest</servlet-name>
        <url-pattern>/prest/crm/config</url-pattern>
    </servlet-mapping>

    <servlet>
        <servlet-name>ConfigBitirxRestServlet</servlet-name>
        <servlet-class>servlet.crm.ConfigBitirxRestServlet</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>ConfigBitirxRestServlet</servlet-name>
        <url-pattern>/prest/crm/configBitrix</url-pattern>
    </servlet-mapping>

    <servlet>
        <servlet-name>ConfigHubspoRest</servlet-name>
        <servlet-class>servlet.crm.ConfigHubspoRestServlet</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>ConfigHubspoRest</servlet-name>
        <url-pattern>/prest/crm/confighubspt</url-pattern>
    </servlet-mapping>

    <servlet>
        <servlet-name>EmpresaRest</servlet-name>
        <servlet-class>servlet.empresa.EmpresaRestServlet</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>EmpresaRest</servlet-name>
        <url-pattern>/prest/empresa/todas</url-pattern>
    </servlet-mapping>

    <servlet>
        <servlet-name>EstadoRest</servlet-name>
        <servlet-class>servlet.estado.EstadoServlet</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>EstadoRest</servlet-name>
        <url-pattern>/prest/estado</url-pattern>
    </servlet-mapping>

    <servlet>
        <servlet-name>TemplateRestServlet</servlet-name>
        <servlet-class>servlet.crm.TemplateRestServlet</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>TemplateRestServlet</servlet-name>
        <url-pattern>/prest/modelos</url-pattern>
    </servlet-mapping>

    <servlet>
        <servlet-name>ModalidadeRest</servlet-name>
        <servlet-class>servlet.modalidade.ModalidadeServlet</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>ModalidadeRest</servlet-name>
        <url-pattern>/prest/modalidade/todos</url-pattern>
    </servlet-mapping>

    <servlet>
        <servlet-name>MgbRest</servlet-name>
        <servlet-class>servlet.mgb.MgbServlet</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>MgbRest</servlet-name>
        <url-pattern>/prest/mgb/findByMatricula</url-pattern>
    </servlet-mapping>

    <servlet-mapping>
        <servlet-name>MgbRest</servlet-name>
        <url-pattern>/prest/mgb/publicidMgb</url-pattern>
    </servlet-mapping>

    <servlet-mapping>
        <servlet-name>MgbRest</servlet-name>
        <url-pattern>/prest/mgb/tokenMgb</url-pattern>
    </servlet-mapping>

    <servlet-mapping>
        <servlet-name>MgbRest</servlet-name>
        <url-pattern>/prest/mgb/insertToken</url-pattern>
    </servlet-mapping>

    <servlet-mapping>
        <servlet-name>MgbRest</servlet-name>
        <url-pattern>/prest/mgb/updateToken</url-pattern>
    </servlet-mapping>

    <servlet-mapping>
        <servlet-name>MgbRest</servlet-name>
        <url-pattern>/prest/mgb/tokensEmpresas</url-pattern>
    </servlet-mapping>

    <servlet-mapping>
        <servlet-name>MgbRest</servlet-name>
        <url-pattern>/prest/mgb/updatePublicIdMgb</url-pattern>
    </servlet-mapping>

    <servlet-mapping>
        <servlet-name>MgbRest</servlet-name>
        <url-pattern>/prest/mgb/sincronizar-alunos-ativos</url-pattern>
    </servlet-mapping>

    <servlet-mapping>
        <servlet-name>MgbRest</servlet-name>
        <url-pattern>/prest/mgb/sync-horario-turma-mgb</url-pattern>
    </servlet-mapping>

    <servlet-mapping>
        <servlet-name>MgbRest</servlet-name>
        <url-pattern>/prest/mgb/sync-turma-mgb</url-pattern>
    </servlet-mapping>

    <servlet-mapping>
        <servlet-name>MgbRest</servlet-name>
        <url-pattern>/prest/mgb/sync-todas-turmas-com-mgb</url-pattern>
    </servlet-mapping>

    <servlet-mapping>
        <servlet-name>MgbRest</servlet-name>
        <url-pattern>/prest/mgb/inativar-turma-mgb</url-pattern>
    </servlet-mapping>

    <servlet-mapping>
        <servlet-name>MgbRest</servlet-name>
        <url-pattern>/prest/mgb/deletar-turma-mgb</url-pattern>
    </servlet-mapping>

    <servlet-mapping>
        <servlet-name>MgbRest</servlet-name>
        <url-pattern>/prest/mgb/deletar-todas-turmas-no-mgb</url-pattern>
    </servlet-mapping>

    <servlet-mapping>
        <servlet-name>MgbRest</servlet-name>
        <url-pattern>/prest/pacto/mgb/sincronizar-aluno-mgb</url-pattern>
    </servlet-mapping>

    <servlet-mapping>
        <servlet-name>MgbRest</servlet-name>
        <url-pattern>/prest/pacto/mgb/consultar-dados-aluno</url-pattern>
    </servlet-mapping>

    <servlet-mapping>
        <servlet-name>MgbRest</servlet-name>
        <url-pattern>/prest/mgb/deletar-publicid-professor</url-pattern>
    </servlet-mapping>

    <servlet-mapping>
        <servlet-name>MgbRest</servlet-name>
        <url-pattern>/prest/mgb/remover-utilizacao-piscina</url-pattern>
    </servlet-mapping>

    <servlet>
        <servlet-name>JoinRestServlet</servlet-name>
        <servlet-class>servlet.join.JoinRestServlet</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>JoinRestServlet</servlet-name>
        <url-pattern>/prest/join/lead</url-pattern>
    </servlet-mapping>

    <servlet>
        <servlet-name>ConviteServlet</servlet-name>
        <servlet-class>servlet.cliente.ConviteServlet</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>ConviteServlet</servlet-name>
        <url-pattern>/prest/convite/gerar-link-convite</url-pattern>
    </servlet-mapping>

    <servlet>
        <servlet-name>ClienteSiteServlet</servlet-name>
        <servlet-class>servlet.cliente.ClienteSiteServlet</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>ClienteSiteServlet</servlet-name>
        <url-pattern>/prest/cliente-site/add</url-pattern>
    </servlet-mapping>
    <servlet>
        <servlet-name>TreinoServlet</servlet-name>
        <servlet-class>servlet.treino.TreinoServlet</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>TreinoServlet</servlet-name>
        <url-pattern>/prest/treino/ativos</url-pattern>
    </servlet-mapping>
    <servlet-mapping>
        <servlet-name>TreinoServlet</servlet-name>
        <url-pattern>/prest/treino/prescricao-colaboradores</url-pattern>
    </servlet-mapping>
    <servlet-mapping>
        <servlet-name>TreinoServlet</servlet-name>
        <url-pattern>/prest/treino/prescricao-clientes</url-pattern>
    </servlet-mapping>
    <servlet-mapping>
        <servlet-name>TreinoServlet</servlet-name>
        <url-pattern>/prest/treino/prescricao-colaborador-data</url-pattern>
    </servlet-mapping>
    <servlet-mapping>
        <servlet-name>TreinoServlet</servlet-name>
        <url-pattern>/prest/treino/ativos-profesor-data</url-pattern>
    </servlet-mapping>
    <servlet-mapping>
        <servlet-name>TreinoServlet</servlet-name>
        <url-pattern>/prest/treino/matriculas-ativos</url-pattern>
    </servlet-mapping>
    <servlet-mapping>
        <servlet-name>TreinoServlet</servlet-name>
        <url-pattern>/prest/treino/ativos-professor</url-pattern>
    </servlet-mapping>
    <servlet-mapping>
        <servlet-name>TreinoServlet</servlet-name>
        <url-pattern>/prest/treino/prescricao-lista</url-pattern>
    </servlet-mapping>
    <servlet-mapping>
        <servlet-name>TreinoServlet</servlet-name>
        <url-pattern>/prest/treino/codigos-professores</url-pattern>
    </servlet-mapping>
    <servlet-mapping>
        <servlet-name>TreinoServlet</servlet-name>
        <url-pattern>/prest/treino/aluno-parcela-vencida</url-pattern>
    </servlet-mapping>
    <servlet-mapping>
        <servlet-name>TreinoServlet</servlet-name>
        <url-pattern>/prest/treino/aluno-contrato-assinatura-digital</url-pattern>
    </servlet-mapping>
    <servlet-mapping>
        <servlet-name>TreinoServlet</servlet-name>
        <url-pattern>/prest/treino/aluno-contrato-assinatura-digital-by-contrato</url-pattern>
    </servlet-mapping>
    <servlet-mapping>
        <servlet-name>TreinoServlet</servlet-name>
        <url-pattern>/prest/treino/aluno-contrato-assinatura-digital-incluir</url-pattern>
    </servlet-mapping>
    <servlet-mapping>
        <servlet-name>TreinoServlet</servlet-name>
        <url-pattern>/prest/treino/nr-professor</url-pattern>
    </servlet-mapping>
    <servlet-mapping>
        <servlet-name>TreinoServlet</servlet-name>
        <url-pattern>/prest/treino/matriculas-ativos-professor</url-pattern>
    </servlet-mapping>
    <servlet-mapping>
        <servlet-name>TreinoServlet</servlet-name>
        <url-pattern>/prest/treino/matriculas-professor</url-pattern>
    </servlet-mapping>
    <servlet-mapping>
        <servlet-name>TreinoServlet</servlet-name>
        <url-pattern>/prest/treino/matriculas-nao-importadas</url-pattern>
    </servlet-mapping>
    <servlet-mapping>
        <servlet-name>TreinoServlet</servlet-name>
        <url-pattern>/prest/treino/autorizados</url-pattern>
    </servlet-mapping>
    <servlet-mapping>
        <servlet-name>TreinoServlet</servlet-name>
        <url-pattern>/prest/treino/matriculas-externas</url-pattern>
    </servlet-mapping>
    <servlet-mapping>
        <servlet-name>TreinoServlet</servlet-name>
        <url-pattern>/prest/treino/matriculas-externas-prospects</url-pattern>
    </servlet-mapping>
    <servlet-mapping>
        <servlet-name>TreinoServlet</servlet-name>
        <url-pattern>/prest/treino/alunos-aviso-medico</url-pattern>
    </servlet-mapping>
    <servlet-mapping>
        <servlet-name>TreinoServlet</servlet-name>
        <url-pattern>/prest/treino/cliente-mensagem</url-pattern>
    </servlet-mapping>
    <servlet-mapping>
        <servlet-name>TreinoServlet</servlet-name>
        <url-pattern>/prest/treino/usuario-importador-treino</url-pattern>
    </servlet-mapping>

    <servlet>
        <servlet-name>TicketMedioServletRest</servlet-name>
        <servlet-class>servlet.financeiro.TicketMedioServlet</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>TicketMedioServletRest</servlet-name>
        <url-pattern>/prest/ticket-medio</url-pattern>
    </servlet-mapping>

    <servlet>
        <servlet-name>TicketMedioAppServlet</servlet-name>
        <servlet-class>servlet.financeiro.TicketMedioAppServlet</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>TicketMedioAppServlet</servlet-name>
        <url-pattern>/prest/app-pacto/ticket-medio</url-pattern>
    </servlet-mapping>
    <servlet>
        <servlet-name>OptinServlet</servlet-name>
        <servlet-class>servlet.optin.OptinServlet</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>OptinServlet</servlet-name>
        <url-pattern>/prest/optin/salvar</url-pattern>
    </servlet-mapping>
    <servlet-mapping>
        <servlet-name>OptinServlet</servlet-name>
        <url-pattern>/prest/optin/atualizar</url-pattern>
    </servlet-mapping>
    <servlet>
        <servlet-name>AutorizacaoAcessoServlet</servlet-name>
        <servlet-class>servlet.arquitetura.AutorizacaoAcessoServlet</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>AutorizacaoAcessoServlet</servlet-name>
        <url-pattern>/prest/validar-permissao</url-pattern>
    </servlet-mapping>
    <servlet>
        <servlet-name>TurmaVendasOnlineServlet</servlet-name>
        <servlet-class>servlet.turma.TurmaVendasOnlineServlet</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>TurmaVendasOnlineServlet</servlet-name>
        <url-pattern>/prest/turmasVendasOnline/turmas</url-pattern>
    </servlet-mapping>
    <servlet>
        <servlet-name>AulaCheiaServlet</servlet-name>
        <servlet-class>servlet.aulacheia.AulaCheiaServlet</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>AulaCheiaServlet</servlet-name>
        <url-pattern>/prest/aulacheia/alunos-confirmados</url-pattern>
    </servlet-mapping>
    <servlet-mapping>
        <servlet-name>AulaCheiaServlet</servlet-name>
        <url-pattern>/prest/aulacheia/modalidades-titular</url-pattern>
    </servlet-mapping>
    <servlet-mapping>
        <servlet-name>AulaCheiaServlet</servlet-name>
        <url-pattern>/prest/aulacheia/aulas</url-pattern>
    </servlet-mapping>
    <servlet-mapping>
        <servlet-name>AulaCheiaServlet</servlet-name>
        <url-pattern>/prest/aulacheia/aluno-em-aula</url-pattern>
    </servlet-mapping>
    <servlet-mapping>
        <servlet-name>AulaCheiaServlet</servlet-name>
        <url-pattern>/prest/aulacheia/aluno-gestao-rede-em-aula</url-pattern>
    </servlet-mapping>
    <servlet-mapping>
        <servlet-name>AulaCheiaServlet</servlet-name>
        <url-pattern>/prest/aulacheia/agendamentos-dia</url-pattern>
    </servlet-mapping>
    <servlet-mapping>
        <servlet-name>AulaCheiaServlet</servlet-name>
        <url-pattern>/prest/aulacheia/agendamentos-dia-aulas-coletivas</url-pattern>
    </servlet-mapping>
    <servlet-mapping>
        <servlet-name>AulaCheiaServlet</servlet-name>
        <url-pattern>/prest/aulacheia/log-aula</url-pattern>
    </servlet-mapping>
    <servlet-mapping>
        <servlet-name>AulaCheiaServlet</servlet-name>
        <url-pattern>/prest/aulacheia/log-agenda-aula</url-pattern>
    </servlet-mapping>
    <servlet-mapping>
        <servlet-name>AulaCheiaServlet</servlet-name>
        <url-pattern>/prest/aulacheia/log-agenda-aulas</url-pattern>
    </servlet-mapping>
    <servlet-mapping>
        <servlet-name>AulaCheiaServlet</servlet-name>
        <url-pattern>/prest/aulacheia/autorizados</url-pattern>
    </servlet-mapping>
    <servlet-mapping>
        <servlet-name>AulaCheiaServlet</servlet-name>
        <url-pattern>/prest/aulacheia/consultar-autorizado-criando</url-pattern>
    </servlet-mapping>
    <servlet-mapping>
        <servlet-name>AulaCheiaServlet</servlet-name>
        <url-pattern>/prest/aulacheia/validar-modalidade</url-pattern>
    </servlet-mapping>
    <servlet-mapping>
        <servlet-name>AulaCheiaServlet</servlet-name>
        <url-pattern>/prest/aulacheia/consultar-autorizado-gestao-rede</url-pattern>
    </servlet-mapping>
    <servlet-mapping>
        <servlet-name>AulaCheiaServlet</servlet-name>
        <url-pattern>/prest/aulacheia/gravar-log-outra-unidade</url-pattern>
    </servlet-mapping>
    <servlet-mapping>
        <servlet-name>AulaCheiaServlet</servlet-name>
        <url-pattern>/prest/aulacheia/inserir-fila-espera</url-pattern>
    </servlet-mapping>
    <servlet-mapping>
        <servlet-name>AulaCheiaServlet</servlet-name>
        <url-pattern>/prest/aulacheia/remover-fila-espera</url-pattern>
    </servlet-mapping>
    <servlet-mapping>
        <servlet-name>AulaCheiaServlet</servlet-name>
        <url-pattern>/prest/aulacheia/consultar-fila-espera</url-pattern>
    </servlet-mapping>
    <servlet-mapping>
        <servlet-name>AulaCheiaServlet</servlet-name>
        <url-pattern>/prest/aulacheia/ambientes-agendados</url-pattern>
    </servlet-mapping>
    <servlet-mapping>
        <servlet-name>AulaCheiaServlet</servlet-name>
        <url-pattern>/prest/aulacheia/log-outra-unidade</url-pattern>
    </servlet-mapping>
    <servlet-mapping>
        <servlet-name>AulaCheiaServlet</servlet-name>
        <url-pattern>/prest/aulacheia/verificar-aulas-crossfit-aluno</url-pattern>
    </servlet-mapping>
    <servlet-mapping>
        <servlet-name>AulaCheiaServlet</servlet-name>
        <url-pattern>/prest/aulacheia/reset</url-pattern>
    </servlet-mapping>
    <servlet-mapping>
        <servlet-name>AulaCheiaServlet</servlet-name>
        <url-pattern>/prest/aulacheia/professor-alunos-crossfit</url-pattern>
    </servlet-mapping>
    <servlet-mapping>
        <servlet-name>AulaCheiaServlet</servlet-name>
        <url-pattern>/prest/aulacheia/dados-booking</url-pattern>
    </servlet-mapping>
    <servlet-mapping>
        <servlet-name>AulaCheiaServlet</servlet-name>
        <url-pattern>/prest/aulacheia/dados-booked</url-pattern>
    </servlet-mapping>
    <servlet-mapping>
        <servlet-name>AulaCheiaServlet</servlet-name>
        <url-pattern>/prest/aulacheia/bi-aulas</url-pattern>
    </servlet-mapping>
    <servlet-mapping>
        <servlet-name>AulaCheiaServlet</servlet-name>
        <url-pattern>/prest/aulacheia/listagem-aulas</url-pattern>
    </servlet-mapping>
    <servlet-mapping>
        <servlet-name>AulaCheiaServlet</servlet-name>
        <url-pattern>/prest/aulacheia/listagem-aulas-bonus</url-pattern>
    </servlet-mapping>
    <servlet-mapping>
        <servlet-name>AulaCheiaServlet</servlet-name>
        <url-pattern>/prest/aulacheia/listagem-modalidades</url-pattern>
    </servlet-mapping>
    <servlet-mapping>
        <servlet-name>AulaCheiaServlet</servlet-name>
        <url-pattern>/prest/aulacheia/listagem-professores</url-pattern>
    </servlet-mapping>
    <servlet-mapping>
        <servlet-name>AulaCheiaServlet</servlet-name>
        <url-pattern>/prest/aulacheia/listagem-frequencia-alunos</url-pattern>
    </servlet-mapping>
    <servlet-mapping>
        <servlet-name>AulaCheiaServlet</servlet-name>
        <url-pattern>/prest/aulacheia/listagem-presenca</url-pattern>
    </servlet-mapping>
    <servlet-mapping>
        <servlet-name>AulaCheiaServlet</servlet-name>
        <url-pattern>/prest/aulacheia/desmarcacoes-aluno</url-pattern>
    </servlet-mapping>
    <servlet-mapping>
        <servlet-name>AulaCheiaServlet</servlet-name>
        <url-pattern>/prest/aulacheia/linha-tempo-agendado-booking</url-pattern>
    </servlet-mapping>
    <servlet-mapping>
        <servlet-name>AulaCheiaServlet</servlet-name>
        <url-pattern>/prest/aulacheia/marcar-desmarcar-aulas-app</url-pattern>
    </servlet-mapping>
    <servlet-mapping>
        <servlet-name>AulaCheiaServlet</servlet-name>
        <url-pattern>/prest/aulacheia/processo-desmarcar-aulas-parcelas-vencidas</url-pattern>
    </servlet-mapping>
    <servlet-mapping>
        <servlet-name>AulaCheiaServlet</servlet-name>
        <url-pattern>/prest/aulacheia/modalidades-contrato</url-pattern>
    </servlet-mapping>
    <servlet-mapping>
        <servlet-name>AulaCheiaServlet</servlet-name>
        <url-pattern>/prest/aulacheia/consultar-saldo-aluno-app</url-pattern>
    </servlet-mapping>
    <servlet-mapping>
        <servlet-name>AulaCheiaServlet</servlet-name>
        <url-pattern>/prest/aulacheia/consultar-saldo-aluno-repor-e-marcar-app</url-pattern>
    </servlet-mapping>
    <servlet-mapping>
        <servlet-name>AulaCheiaServlet</servlet-name>
        <url-pattern>/prest/aulacheia/remover-autorizado-aula</url-pattern>
    </servlet-mapping>
    <servlet-mapping>
        <servlet-name>AulaCheiaServlet</servlet-name>
        <url-pattern>/prest/aulacheia/listar-ids-horario-turma</url-pattern>
    </servlet-mapping>
    <servlet-mapping>
        <servlet-name>AulaCheiaServlet</servlet-name>
        <url-pattern>/prest/aulacheia/autorizado-aulas</url-pattern>
    </servlet-mapping>
    <servlet-mapping>
        <servlet-name>AulaCheiaServlet</servlet-name>
        <url-pattern>/prest/aulacheia/is-aluno-contrato-concomitante</url-pattern>
    </servlet-mapping>
    <servlet-mapping>
        <servlet-name>AulaCheiaServlet</servlet-name>
        <url-pattern>/prest/aulacheia/validar-creditos</url-pattern>
    </servlet-mapping>
    <servlet-mapping>
        <servlet-name>AulaCheiaServlet</servlet-name>
        <url-pattern>/prest/aulacheia/marcar-cancelar-comparecimento-meta-presenciais-crm</url-pattern>
    </servlet-mapping>
    <servlet>
        <servlet-name>GraduacaoServlet</servlet-name>
        <servlet-class>servlet.graduacao.GraduacaoServlet</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>GraduacaoServlet</servlet-name>
        <url-pattern>/prest/graduacao/aulas-aluno</url-pattern>
    </servlet-mapping>
    <servlet-mapping>
        <servlet-name>GraduacaoServlet</servlet-name>
        <url-pattern>/prest/graduacao/consulta-alunos</url-pattern>
    </servlet-mapping>
    <servlet-mapping>
        <servlet-name>GraduacaoServlet</servlet-name>
        <url-pattern>/prest/graduacao/consulta-alunos-todos</url-pattern>
    </servlet-mapping>
    <servlet-mapping>
        <servlet-name>GraduacaoServlet</servlet-name>
        <url-pattern>/prest/graduacao/filtros-alunos</url-pattern>
    </servlet-mapping>
    <servlet-mapping>
        <servlet-name>GraduacaoServlet</servlet-name>
        <url-pattern>/prest/graduacao/consultar-informacoes-aluno</url-pattern>
    </servlet-mapping>
    <servlet-mapping>
        <servlet-name>GraduacaoServlet</servlet-name>
        <url-pattern>/prest/graduacao/consultar-horarios-dias-aluno-por-periodo</url-pattern>
    </servlet-mapping>

    <servlet>
        <servlet-name>ManutencaoServlet</servlet-name>
        <servlet-class>servlet.manutencao.ManutencaoServlet</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>ManutencaoServlet</servlet-name>
        <url-pattern>/prest/manutencao/corrigir-nome-usuario-pacto</url-pattern>
    </servlet-mapping>
    <servlet-mapping>
        <servlet-name>ManutencaoServlet</servlet-name>
        <url-pattern>/prest/manutencao/obter-dados-alunos-para-sincronizar</url-pattern>
    </servlet-mapping>
    <servlet-mapping>
        <servlet-name>ManutencaoServlet</servlet-name>
        <url-pattern>/prest/manutencao/set-aluno-parq-false</url-pattern>
    </servlet-mapping>
    <servlet-mapping>
        <servlet-name>ManutencaoServlet</servlet-name>
        <url-pattern>/prest/manutencao/corrigir-datas-matriculaalunohorarioturma</url-pattern>
    </servlet-mapping>
    <servlet>
        <servlet-name>LinksAcessoRapidoAppsPactoServlet</servlet-name>
        <servlet-class>servlet.linksAcessoRapidoAppsPacto.LinksAcessoRapidoAppsPactoServlet</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>LinksAcessoRapidoAppsPactoServlet</servlet-name>
        <url-pattern>/prest/obter-links-apps-pacto</url-pattern>
    </servlet-mapping>

    <servlet>
        <servlet-name>ConfirmarPresencaAlunoServlet</servlet-name>
        <servlet-class>servlet.confirmarPresencaAluno.ConfirmarPresencaAlunoServlet</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>ConfirmarPresencaAlunoServlet</servlet-name>
        <url-pattern>/prest/confirmar-presenca/confirmar-todos</url-pattern>
    </servlet-mapping>
    <servlet-mapping>
        <servlet-name>ConfirmarPresencaAlunoServlet</servlet-name>
        <url-pattern>/prest/confirmar-presenca/presenca</url-pattern>
    </servlet-mapping>
    <servlet-mapping>
        <servlet-name>ConfirmarPresencaAlunoServlet</servlet-name>
        <url-pattern>/prest/confirmar-presenca/autorizado</url-pattern>
    </servlet-mapping>
    <servlet-mapping>
        <servlet-name>ConfirmarPresencaAlunoServlet</servlet-name>
        <url-pattern>/prest/confirmar-presenca/remover-aula</url-pattern>
    </servlet-mapping>

    <servlet>
        <servlet-name>RankingProfessoresServlet</servlet-name>
        <servlet-class>servlet.rankingprofessores.RankingProfessoresServlet</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>RankingProfessoresServlet</servlet-name>
        <url-pattern>/prest/rankingprofessores/listar-todos</url-pattern>
    </servlet-mapping>
    <servlet-mapping>
        <servlet-name>RankingProfessoresServlet</servlet-name>
        <url-pattern>/prest/rankingprofessores/detalhes</url-pattern>
    </servlet-mapping>
    <servlet-mapping>
        <servlet-name>RankingProfessoresServlet</servlet-name>
        <url-pattern>/prest/rankingprofessores/detalhes-professor</url-pattern>
    </servlet-mapping>
    <servlet>
        <servlet-name>GeolocalizarRestServlet</servlet-name>
        <servlet-class>servlet.cliente.GeolocalizarRestServlet</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>GeolocalizarRestServlet</servlet-name>
        <url-pattern>/prest/geolocalizacao/lista-geolocalizar</url-pattern>
    </servlet-mapping>
    <servlet-mapping>
        <servlet-name>GeolocalizarRestServlet</servlet-name>
        <url-pattern>/prest/geolocalizacao/empresa</url-pattern>
    </servlet-mapping>
    <servlet-mapping>
        <servlet-name>GeolocalizarRestServlet</servlet-name>
        <url-pattern>/prest/geolocalizacao/geolocalizar</url-pattern>
    </servlet-mapping>

    <servlet>
        <servlet-name>UsuarioPerfilAcessoServlet</servlet-name>
        <servlet-class>servlet.arquitetura.UsuarioPerfilAcessoServlet</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>UsuarioPerfilAcessoServlet</servlet-name>
        <url-pattern>/prest/arquitetura/usuarioPerfilAcesso</url-pattern>
    </servlet-mapping>
    <servlet>
        <servlet-name>RegistroRemessa</servlet-name>
        <servlet-class>servicos.remessa.api.RegistrarRemessaServlet</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>RegistroRemessa</servlet-name>
        <url-pattern>/prest/remessa/cadastrar</url-pattern>
    </servlet-mapping>
    <servlet>
        <servlet-name>ConsultarRemessa</servlet-name>
        <servlet-class>servicos.remessa.api.ConsultarRemessaServlet</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>ConsultarRemessa</servlet-name>
        <url-pattern>/prest/remessa/consultar</url-pattern>
    </servlet-mapping>
    <servlet>
        <servlet-name>ProcessarRemessa</servlet-name>
        <servlet-class>servicos.remessa.api.ProcessarRemessaSerlvet</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>ProcessarRemessa</servlet-name>
        <url-pattern>/prest/remessa/processarremessa</url-pattern>
    </servlet-mapping>
    <servlet>
        <servlet-name>ContratoAssinaturaDigitalServletControle</servlet-name>
        <servlet-class>br.com.pactosolucoes.controle.json.contrato.ContratoAssinaturaDigitalServletControle</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>ContratoAssinaturaDigitalServletControle</servlet-name>
        <url-pattern>/prest/contratoassinatura</url-pattern>
    </servlet-mapping>
    <servlet>
        <servlet-name>ContratoOperacaoServletControle</servlet-name>
        <servlet-class>br.com.pactosolucoes.controle.json.contratooperacao.ContratoOperacaoServletControle</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>ContratoOperacaoServletControle</servlet-name>
        <url-pattern>/prest/contratooperacao</url-pattern>
    </servlet-mapping>
    <servlet>
        <servlet-name>IntegracaoSesiCeServletControle</servlet-name>
        <servlet-class>br.com.pactosolucoes.controle.json.sesice.IntegracaoSesiCeServletControle</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>IntegracaoSesiCeServletControle</servlet-name>
        <url-pattern>/prest/integracaosesice</url-pattern>
    </servlet-mapping>
    <servlet>
        <servlet-name>IntegracaoNuvemshopServletControle</servlet-name>
        <servlet-class>br.com.pactosolucoes.controle.json.nuvemshop.IntegracaoNuvemshopServletControle</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>IntegracaoNuvemshopServletControle</servlet-name>
        <url-pattern>/prest/integracaonuvemshop</url-pattern>
    </servlet-mapping>
    <servlet>
        <servlet-name>IntegracaoRdStationApiMarketingServletControle</servlet-name>
        <servlet-class>br.com.pactosolucoes.controle.json.rdstationmarketing.IntegracaoRdStationApiMarketingServletControle</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>IntegracaoRdStationApiMarketingServletControle</servlet-name>
        <url-pattern>/prest/integracaordstationmarketing</url-pattern>
    </servlet-mapping>
    <servlet>
        <servlet-name>OperacoesContratoServletControle</servlet-name>
        <servlet-class>br.com.pactosolucoes.controle.json.operacoescontrato.OperacoesContratoServletControle</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>OperacoesContratoServletControle</servlet-name>
        <url-pattern>/prest/operacoescontratoservlet</url-pattern>
    </servlet-mapping>
    <servlet>
        <servlet-name>ModoEmergenciaServletControle</servlet-name>
        <servlet-class>br.com.pactosolucoes.controle.json.modoemergencia.ModoEmergenciaServletControle</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>ModoEmergenciaServletControle</servlet-name>
        <url-pattern>/prest/modoemergencia/obter-usuarios-permissao</url-pattern>
    </servlet-mapping>
    <servlet>
        <servlet-name>GestaoAulaServletControle</servlet-name>
        <servlet-class>br.com.pactosolucoes.controle.json.gestaoaula.GestaoAulaServletControle</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>GestaoAulaServletControle</servlet-name>
        <url-pattern>/prest/gestaoaula</url-pattern>
    </servlet-mapping>
    <!--
        Final Servlets do pacote arquitetura;
    -->
    <!--
        Inicio Servlets do pacote basico;
    -->
    <servlet>
        <servlet-name>CategoriaServlet</servlet-name>
        <servlet-class>servlet.basico.CategoriaServlet</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>CategoriaServlet</servlet-name>
        <url-pattern>/prest/basico/categoria</url-pattern>
    </servlet-mapping>
    <servlet>
        <servlet-name>ClienteObservacaoServlet</servlet-name>
        <servlet-class>servlet.basico.ClienteObservacaoServlet</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>ClienteObservacaoServlet</servlet-name>
        <url-pattern>/prest/basico/clienteObservacao</url-pattern>
    </servlet-mapping>
    <servlet>
        <servlet-name>CidadeServlet</servlet-name>
        <servlet-class>servlet.basico.CidadeServlet</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>CidadeServlet</servlet-name>
        <url-pattern>/prest/basico/cidade</url-pattern>
    </servlet-mapping>
    <servlet>
        <servlet-name>IndiceFinanceiroReajustePrecoServlet</servlet-name>
        <servlet-class>servlet.basico.IndiceFinanceiroReajustePrecoServlet</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>IndiceFinanceiroReajustePrecoServlet</servlet-name>
        <url-pattern>/prest/basico/indiceFinanceiroReajustePreco</url-pattern>
    </servlet-mapping>
    <servlet>
        <servlet-name>ClassificacaoServlet</servlet-name>
        <servlet-class>servlet.basico.ClassificacaoServlet</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>ClassificacaoServlet</servlet-name>
        <url-pattern>/prest/basico/classificacao</url-pattern>
    </servlet-mapping>
    <servlet>
        <servlet-name>ClienteServlet</servlet-name>
        <servlet-class>servlet.basico.ClienteServlet</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>ClienteServlet</servlet-name>
        <url-pattern>/prest/basico/cliente</url-pattern>
    </servlet-mapping>
    <servlet>
        <servlet-name>ClienteADServlet</servlet-name>
        <servlet-class>servlet.cliente.ClienteADServlet</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>ClienteADServlet</servlet-name>
        <url-pattern>/prest/clientead</url-pattern>
    </servlet-mapping>
    <servlet>
        <servlet-name>BrindeServlet</servlet-name>
        <servlet-class>servlet.basico.BrindeServlet</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>BrindeServlet</servlet-name>
        <url-pattern>/prest/basico/brinde</url-pattern>
    </servlet-mapping>
    <servlet>
        <servlet-name>ColaboradorServlet</servlet-name>
        <servlet-class>servlet.basico.ColaboradorServlet</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>ColaboradorServlet</servlet-name>
        <url-pattern>/prest/basico/colaborador</url-pattern>
    </servlet-mapping>
    <servlet>
        <servlet-name>ColaboradorV2Servlet</servlet-name>
        <servlet-class>servlet.basico.ColaboradorV2Servlet</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>ColaboradorV2Servlet</servlet-name>
        <url-pattern>/prest/colaborador/consultar</url-pattern>
    </servlet-mapping>
    <servlet>
        <servlet-name>PessoaServlet</servlet-name>
        <servlet-class>servlet.basico.PessoaServlet</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>PessoaServlet</servlet-name>
        <url-pattern>/prest/basico/pessoa</url-pattern>
    </servlet-mapping>
    <servlet>
        <servlet-name>EmpresaServlet</servlet-name>
        <servlet-class>servlet.basico.EmpresaServlet</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>EmpresaServlet</servlet-name>
        <url-pattern>/prest/basico/empresa</url-pattern>
    </servlet-mapping>
    <servlet>
        <servlet-name>GrauInstrucaoServlet</servlet-name>
        <servlet-class>servlet.basico.GrauInstrucaoServlet</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>GrauInstrucaoServlet</servlet-name>
        <url-pattern>/prest/basico/grauInstrucao</url-pattern>
    </servlet-mapping>
    <servlet>
        <servlet-name>GrupoServlet</servlet-name>
        <servlet-class>servlet.basico.GrupoServlet</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>GrupoServlet</servlet-name>
        <url-pattern>/prest/basico/grupo</url-pattern>
    </servlet-mapping>
    <servlet>
        <servlet-name>GympassServlet</servlet-name>
        <servlet-class>servlet.basico.GympassServlet</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>GympassServlet</servlet-name>
        <url-pattern>/prest/basico/gympass</url-pattern>
    </servlet-mapping>
    <servlet>
        <servlet-name>PaisServlet</servlet-name>
        <servlet-class>servlet.basico.PaisServlet</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>PaisServlet</servlet-name>
        <url-pattern>/prest/basico/pais</url-pattern>
    </servlet-mapping>
    <servlet>
        <servlet-name>ParentescoServlet</servlet-name>
        <servlet-class>servlet.basico.ParentescoServlet</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>ParentescoServlet</servlet-name>
        <url-pattern>/prest/basico/parentesco</url-pattern>
    </servlet-mapping>
    <servlet>
        <servlet-name>PerguntaServlet</servlet-name>
        <servlet-class>servlet.basico.PerguntaServlet</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>PerguntaServlet</servlet-name>
        <url-pattern>/prest/basico/pergunta</url-pattern>
    </servlet-mapping>
    <servlet>
        <servlet-name>ProfissaoServlet</servlet-name>
        <servlet-class>servlet.basico.ProfissaoServlet</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>ProfissaoServlet</servlet-name>
        <url-pattern>/prest/basico/profissao</url-pattern>
    </servlet-mapping>
    <servlet>
        <servlet-name>QuestionarioServlet</servlet-name>
        <servlet-class>servlet.basico.QuestionarioServlet</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>QuestionarioServlet</servlet-name>
        <url-pattern>/prest/basico/questionario</url-pattern>
    </servlet-mapping>
    <servlet>
        <servlet-name>DepartamentoServlet</servlet-name>
        <servlet-class>servlet.basico.DepartamentoServlet</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>DepartamentoServlet</servlet-name>
        <url-pattern>/prest/basico/departamento</url-pattern>
    </servlet-mapping>
    <!--
            Final dos servlets do pacote basico
    -->
    <!--
            Início dos servlets do pacote contrato
    -->
    <servlet>
        <servlet-name>ConvenioDescontoServlet</servlet-name>
        <servlet-class>servlet.contrato.ConvenioDescontoServlet</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>ConvenioDescontoServlet</servlet-name>
        <url-pattern>/prest/contrato/convenioDesconto</url-pattern>
    </servlet-mapping>
    <servlet>
        <servlet-name>JustificativaOperacaoServlet</servlet-name>
        <servlet-class>servlet.contrato.JustificativaOperacaoServlet</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>JustificativaOperacaoServlet</servlet-name>
        <url-pattern>/prest/contrato/justificativaOperacao</url-pattern>
    </servlet-mapping>
    <servlet>
        <servlet-name>MovProdutoServlet</servlet-name>
        <servlet-class>servlet.contrato.MovProdutoServlet</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>MovProdutoServlet</servlet-name>
        <url-pattern>/prest/contrato/movProduto</url-pattern>
    </servlet-mapping>
    <servlet>
        <servlet-name>ComissaoGeralConfiguracaoServlet</servlet-name>
        <servlet-class>servlet.contrato.ComissaoGeralConfiguracaoServlet</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>ComissaoGeralConfiguracaoServlet</servlet-name>
        <url-pattern>/prest/contrato/comissao</url-pattern>
    </servlet-mapping>
    <!--
            Final dos servlets do pacote contrato
    -->
    <!--
        Início dos servlets do pacote estoque
     -->
    <servlet>
        <servlet-name>BalancoServlet</servlet-name>
        <servlet-class>servlet.estoque.BalancoServlet</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>BalancoServlet</servlet-name>
        <url-pattern>/prest/estoque/balanco</url-pattern>
    </servlet-mapping>
    <servlet>
        <servlet-name>CardexServlet</servlet-name>
        <servlet-class>servlet.estoque.CardexServlet</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>CardexServlet</servlet-name>
        <url-pattern>/prest/estoque/cardex</url-pattern>
    </servlet-mapping>
    <servlet>
        <servlet-name>CompraServlet</servlet-name>
        <servlet-class>servlet.estoque.CompraServlet</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>CompraServlet</servlet-name>
        <url-pattern>/prest/estoque/compra</url-pattern>
    </servlet-mapping>
    <servlet>
        <servlet-name>ProdutoEstoqueServlet</servlet-name>
        <servlet-class>servlet.estoque.ProdutoEstoqueServlet</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>ProdutoEstoqueServlet</servlet-name>
        <url-pattern>/prest/estoque/produtoEstoque</url-pattern>
    </servlet-mapping>
    <!--
        Final dos servlets do pacote Estoque
     -->
    <!--
        Início dos servlets do pacote Financeiro
     -->
    <servlet>
         <servlet-name>AdquirenteServlet</servlet-name>
        <servlet-class>servlet.financeiro.AdquirenteServlet</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>AdquirenteServlet</servlet-name>
        <url-pattern>/prest/financeiro/adquirente</url-pattern>
    </servlet-mapping>
    <servlet>
        <servlet-name>BancoServlet</servlet-name>
        <servlet-class>servlet.financeiro.BancoServlet</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>BancoServlet</servlet-name>
        <url-pattern>/prest/financeiro/banco</url-pattern>
    </servlet-mapping>
    <servlet>
        <servlet-name>ChequeAvulsoServlet</servlet-name>
        <servlet-class>servlet.financeiro.ChequeAvulsoServlet</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>ChequeAvulsoServlet</servlet-name>
        <url-pattern>/prest/financeiro/chequeAvulso</url-pattern>
    </servlet-mapping>
    <servlet>
        <servlet-name>CartaoAvulsoServlet</servlet-name>
        <servlet-class>servlet.financeiro.CartaoAvulsoServlet</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>CartaoAvulsoServlet</servlet-name>
        <url-pattern>/prest/financeiro/cartaoAvulso</url-pattern>
    </servlet-mapping>
    <servlet>
        <servlet-name>ContaCorrenteServlet</servlet-name>
        <servlet-class>servlet.financeiro.ContaCorrenteServlet</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>ContaCorrenteServlet</servlet-name>
        <url-pattern>/prest/financeiro/contaCorrente</url-pattern>
    </servlet-mapping>
    <servlet>
        <servlet-name>ConvenioCobrancaServlet</servlet-name>
        <servlet-class>servlet.financeiro.ConvenioCobrancaServlet</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>ConvenioCobrancaServlet</servlet-name>
        <url-pattern>/prest/financeiro/convenioCobranca</url-pattern>
    </servlet-mapping>
    <servlet>
        <servlet-name>PagBankServlet</servlet-name>
        <servlet-class>servlet.pagbank.PagBankServlet</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>PagBankServlet</servlet-name>
        <url-pattern>/prest/pagbank</url-pattern>
    </servlet-mapping>
    <servlet>
        <servlet-name>PinPadServlet</servlet-name>
        <servlet-class>servlet.financeiro.PinPadServlet</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>PinPadServlet</servlet-name>
        <url-pattern>/prest/financeiro/pinpad</url-pattern>
    </servlet-mapping>
    <servlet>
        <servlet-name>ContaContabilServlet</servlet-name>
        <servlet-class>servlet.financeiro.ContaContabilServlet</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>ContaContabilServlet</servlet-name>
        <url-pattern>/prest/financeiro/contacontabil</url-pattern>
    </servlet-mapping>
    <servlet>
        <servlet-name>CupomFiscalServlet</servlet-name>
        <servlet-class>servlet.financeiro.CupomFiscalServlet</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>CupomFiscalServlet</servlet-name>
        <url-pattern>/prest/financeiro/cupomFiscal</url-pattern>
    </servlet-mapping>
    <servlet>
        <servlet-name>FormaPagamentoServlet</servlet-name>
        <servlet-class>servlet.financeiro.FormaPagamentoServlet</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>FormaPagamentoServlet</servlet-name>
        <url-pattern>/prest/financeiro/formaPagamento</url-pattern>
    </servlet-mapping>
    <servlet>
        <servlet-name>FormaPagamentoAppServlet</servlet-name>
        <servlet-class>servlet.financeiro.FormaPagamentoAppServlet</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>FormaPagamentoAppServlet</servlet-name>
        <url-pattern>/prest/app-pacto/formaPagamento</url-pattern>
    </servlet-mapping>
    <servlet>
        <servlet-name>MetaFinanceiraEmpresaServlet</servlet-name>
        <servlet-class>servlet.financeiro.MetaFinanceiraEmpresaServlet</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>MetaFinanceiraEmpresaServlet</servlet-name>
        <url-pattern>/prest/financeiro/metaFinanceira</url-pattern>
    </servlet-mapping>
    <servlet>
        <servlet-name>RelatorioOrcamentarioConfigServlet</servlet-name>
        <servlet-class>servlet.financeiro.RelatorioOrcamentarioConfigServlet</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>RelatorioOrcamentarioConfigServlet</servlet-name>
        <url-pattern>/prest/financeiro/relatorioOrcamentario</url-pattern>
    </servlet-mapping>
    <servlet>
        <servlet-name>MovimentoContaCorrenteClienteServlet</servlet-name>
        <servlet-class>servlet.financeiro.MovimentoContaCorrenteClienteServlet</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>MovimentoContaCorrenteClienteServlet</servlet-name>
        <url-pattern>/prest/financeiro/movimentoCCC</url-pattern>
    </servlet-mapping>
    <servlet>
        <servlet-name>MovPagamentoServlet</servlet-name>
        <servlet-class>servlet.financeiro.MovPagamentoServlet</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>MovPagamentoServlet</servlet-name>
        <url-pattern>/prest/financeiro/movPagamento</url-pattern>
    </servlet-mapping>
    <servlet>
        <servlet-name>MovParcelaServlet</servlet-name>
        <servlet-class>servlet.financeiro.MovParcelaServlet</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>PagamentoServlet</servlet-name>
        <url-pattern>/prest/pagamento</url-pattern>
    </servlet-mapping>
    <servlet>
        <servlet-name>PagamentoServlet</servlet-name>
        <servlet-class>servlet.financeiro.MovParcelaServlet</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>MovParcelaServlet</servlet-name>
        <url-pattern>/prest/financeiro/movParcela</url-pattern>
    </servlet-mapping>
    <servlet>
        <servlet-name>OperadoraCartaoServlet</servlet-name>
        <servlet-class>servlet.financeiro.OperadoraCartaoServlet</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>OperadoraCartaoServlet</servlet-name>
        <url-pattern>/prest/financeiro/operadoraCartao</url-pattern>
    </servlet-mapping>
    <servlet>
        <servlet-name>ReciboPagamentoServlet</servlet-name>
        <servlet-class>servlet.financeiro.ReciboPagamentoServlet</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>ReciboPagamentoServlet</servlet-name>
        <url-pattern>/prest/financeiro/reciboPagamento</url-pattern>
    </servlet-mapping>
    <servlet>
        <servlet-name>ImpressaoReciboServlet</servlet-name>
        <servlet-class>servlet.financeiro.ImpressaoReciboServlet</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>ImpressaoReciboServlet</servlet-name>
        <url-pattern>/prest/financeiro/impressaoRecibo</url-pattern>
    </servlet-mapping>
    <servlet>
        <servlet-name>TipoRemessaServlet</servlet-name>
        <servlet-class>servlet.financeiro.TipoRemessaServlet</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>TipoRemessaServlet</servlet-name>
        <url-pattern>/prest/financeiro/tipoRemessa</url-pattern>
    </servlet-mapping>
    <servlet>
        <servlet-name>TipoRetornoServlet</servlet-name>
        <servlet-class>servlet.financeiro.TipoRetornoServlet</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>TipoRetornoServlet</servlet-name>
        <url-pattern>/prest/financeiro/tipoRetorno</url-pattern>
    </servlet-mapping>


    <servlet>
        <servlet-name>ConfigTokenMqv</servlet-name>
        <servlet-class>servlet.crm.ConfigTokenMqvServlet</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>ConfigTokenMqv</servlet-name>
        <url-pattern>/prest/crm/ConfigTokenMqv</url-pattern>
    </servlet-mapping>

    <servlet>
        <servlet-name>VendaConsumidorServlet</servlet-name>
        <servlet-class>servlet.financeiro.VendaAvulsaServlet</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>VendaConsumidorServlet</servlet-name>
        <url-pattern>/prest/financeiro/vendaConsumidor</url-pattern>
    </servlet-mapping>
    <!--
        Final dos servlets do pacote Financeiro
     -->
    <!--
        Início dos servlets do pacote Plano
     -->
    <servlet>
        <servlet-name>PerfilEventoServlet</servlet-name>
        <servlet-class>servlet.ce.PerfilEventoServlet</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>PerfilEventoServlet</servlet-name>
        <url-pattern>/prest/ce/perfilevento</url-pattern>
    </servlet-mapping>
    <servlet>
        <servlet-name>AmbienteServlet</servlet-name>
        <servlet-class>servlet.plano.AmbienteServlet</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>AmbienteServlet</servlet-name>
        <url-pattern>/prest/plano/ambiente</url-pattern>
    </servlet-mapping>
    <servlet>
        <servlet-name>CategoriaProdutoServlet</servlet-name>
        <servlet-class>servlet.plano.CategoriaProdutoServlet</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>CategoriaProdutoServlet</servlet-name>
        <url-pattern>/prest/plano/categoriaProduto</url-pattern>
    </servlet-mapping>
    <servlet>
        <servlet-name>TamanhoArmarioServlet</servlet-name>
        <servlet-class>servicos.armario.TamanhoArmarioServlet</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>TamanhoArmarioServlet</servlet-name>
        <url-pattern>/prest/armario/TamanhoArmario</url-pattern>
    </servlet-mapping>
    <servlet>
        <servlet-name>ComposicaoServlet</servlet-name>
        <servlet-class>servlet.plano.ComposicaoServlet</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>ComposicaoServlet</servlet-name>
        <url-pattern>/prest/plano/pacote</url-pattern>
    </servlet-mapping>
    <servlet>
        <servlet-name>CondicaoPagamentoServlet</servlet-name>
        <servlet-class>servlet.plano.CondicaoPagamentoServlet</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>CondicaoPagamentoServlet</servlet-name>
        <url-pattern>/prest/plano/condicaoPagamento</url-pattern>
    </servlet-mapping>
    <servlet>
        <servlet-name>DescontoServlet</servlet-name>
        <servlet-class>servlet.plano.DescontoServlet</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>DescontoServlet</servlet-name>
        <url-pattern>/prest/plano/desconto</url-pattern>
    </servlet-mapping>
    <servlet>
        <servlet-name>HorarioServlet</servlet-name>
        <servlet-class>servlet.plano.HorarioServlet</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>HorarioServlet</servlet-name>
        <url-pattern>/prest/plano/horario</url-pattern>
    </servlet-mapping>
    <servlet>
        <servlet-name>ModalidadeServlet</servlet-name>
        <servlet-class>servlet.plano.ModalidadeServlet</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>ModalidadeServlet</servlet-name>
        <url-pattern>/prest/plano/modalidade</url-pattern>
    </servlet-mapping>
    <servlet>
        <servlet-name>TipoModalidadeServlet</servlet-name>
        <servlet-class>servlet.plano.TipoModalidadeServlet</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>TipoModalidadeServlet</servlet-name>
        <url-pattern>/prest/plano/tipoModalidade</url-pattern>
    </servlet-mapping>
    <servlet>
        <servlet-name>TipoConviteAulaExperimentalServlet</servlet-name>
        <servlet-class>servlet.basico.TipoConviteAulaExperimentalServlet</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>TipoConviteAulaExperimentalServlet</servlet-name>
        <url-pattern>/prest/basico/conviteaulaexperimental</url-pattern>
    </servlet-mapping>
    <servlet>
        <servlet-name>CampanhaCupomDescontoServlet</servlet-name>
        <servlet-class>servlet.oamd.CampanhaCupomDescontoServlet</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>CampanhaCupomDescontoServlet</servlet-name>
        <url-pattern>/prest/basico/campanhaCupomDesconto</url-pattern>
    </servlet-mapping>
    <servlet>
        <servlet-name>NivelTurmaServlet</servlet-name>
        <servlet-class>servlet.plano.NivelTurmaServlet</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>NivelTurmaServlet</servlet-name>
        <url-pattern>/prest/plano/nivelTurma</url-pattern>
    </servlet-mapping>
    <servlet>
        <servlet-name>PlanoServlet</servlet-name>
        <servlet-class>servlet.plano.PlanoServlet</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>PlanoServlet</servlet-name>
        <url-pattern>/prest/plano/plano</url-pattern>
    </servlet-mapping>
    <servlet>
        <servlet-name>PlanoTextoPadraoServlet</servlet-name>
        <servlet-class>servlet.plano.PlanoTextoPadraoServlet</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>PlanoTextoPadraoServlet</servlet-name>
        <url-pattern>/prest/plano/planoTextoPadrao</url-pattern>
    </servlet-mapping>
    <servlet>
        <servlet-name>ModeloOrcamentoServlet</servlet-name>
        <servlet-class>servlet.plano.ModeloOrcamentoServlet</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>ModeloOrcamentoServlet</servlet-name>
        <url-pattern>/prest/plano/modeloOrcamento</url-pattern>
    </servlet-mapping>
    <servlet>
        <servlet-name>OrcamentoRelServlet</servlet-name>
        <servlet-class>servlet.plano.OrcamentoRelServlet</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>OrcamentoRelServlet</servlet-name>
        <url-pattern>/prest/plano/orcamentos</url-pattern>
    </servlet-mapping>
    <servlet>
        <servlet-name>ProdutoServlet</servlet-name>
        <servlet-class>servlet.plano.ProdutoServlet</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>ProdutoServlet</servlet-name>
        <url-pattern>/prest/plano/produto</url-pattern>
    </servlet-mapping>
    <servlet>
        <servlet-name>TurmaServlet</servlet-name>
        <servlet-class>servlet.plano.TurmaServlet</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>TurmaServlet</servlet-name>
        <url-pattern>/prest/plano/turma</url-pattern>
    </servlet-mapping>
    <servlet>
        <servlet-name>PlanoTipoServlet</servlet-name>
        <servlet-class>servlet.basico.PlanoTipoServlet</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>PlanoTipoServlet</servlet-name>
        <url-pattern>/prest/basico/planotipo</url-pattern>
    </servlet-mapping>
    <!--
        Final dos servlets do pacote Plano
     -->
    <!--
      Inicio dos servlets do pacote Relatorio
    -->
    <servlet>
        <servlet-name>CarteirasRelServlet</servlet-name>
        <servlet-class>servlet.relatorio.CarteirasServlet</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>CarteirasRelServlet</servlet-name>
        <url-pattern>/prest/relatorio/carteiras</url-pattern>
    </servlet-mapping>
    <servlet>
        <servlet-name>PendenciaServlet</servlet-name>
        <servlet-class>servlet.relatorio.PendenciaServlet</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>PendenciaServlet</servlet-name>
        <url-pattern>/prest/relatorio/pendencia</url-pattern>
    </servlet-mapping>
    <servlet>
        <servlet-name>RenovacaoSinteticoServlet</servlet-name>
        <servlet-class>servlet.relatorio.RenovacaoSinteticoServlet</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>RenovacaoSinteticoServlet</servlet-name>
        <url-pattern>/prest/relatorio/renovacaoSintetico</url-pattern>
    </servlet-mapping>
    <servlet>
        <servlet-name>LTVServlet</servlet-name>
        <servlet-class>servlet.relatorio.LTVServlet</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>LTVServlet</servlet-name>
        <url-pattern>/prest/ltv/lifetime</url-pattern>
    </servlet-mapping>
    <servlet>
        <servlet-name>TicketMedioServlet</servlet-name>
        <servlet-class>servlet.relatorio.TicketMedioServlet</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>TicketMedioServlet</servlet-name>
        <url-pattern>/prest/relatorio/ticketMedio</url-pattern>
    </servlet-mapping>
    <servlet>
        <servlet-name>ContratosCanceladosServlet</servlet-name>
        <servlet-class>servlet.relatorio.ContratosCanceladosServlet</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>ContratosCanceladosServlet</servlet-name>
        <url-pattern>/prest/relatorio/contratosCancelados</url-pattern>
    </servlet-mapping>
    <servlet>
        <servlet-name>InadimplenciaServlet</servlet-name>
        <servlet-class>servlet.relatorio.InadimplenciaServlet</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>InadimplenciaServlet</servlet-name>
        <url-pattern>/prest/relatorio/inadimplencia/consultar-parcelas</url-pattern>
    </servlet-mapping>
    <!--
        Final dos servlets do pacote Relatorio
    -->
    <!--
        Inicio dos servlets do pacote CRM
    -->
    <servlet>
        <servlet-name>TextoPadraoServlet</servlet-name>
        <servlet-class>servlet.crm.TextoPadraoServlet</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>TextoPadraoServlet</servlet-name>
        <url-pattern>/prest/crm/textoPadrao</url-pattern>
    </servlet-mapping>
    <servlet>
        <servlet-name>PassivoServlet</servlet-name>
        <servlet-class>servlet.crm.PassivoServlet</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>PassivoServlet</servlet-name>
        <url-pattern>/prest/crm/passivo</url-pattern>
    </servlet-mapping>
    <servlet>
        <servlet-name>FeriadoServlet</servlet-name>
        <servlet-class>servlet.crm.FeriadoServlet</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>FeriadoServlet</servlet-name>
        <url-pattern>/prest/crm/feriado</url-pattern>
    </servlet-mapping>
    <servlet>
        <servlet-name>ModeloMensagemServlet</servlet-name>
        <servlet-class>servlet.crm.ModeloMensagemServlet</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>ModeloMensagemServlet</servlet-name>
        <url-pattern>/prest/crm/modeloMensagem</url-pattern>
    </servlet-mapping>
    <servlet>
        <servlet-name>EventoServlet</servlet-name>
        <servlet-class>servlet.crm.EventoServlet</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>EventoServlet</servlet-name>
        <url-pattern>/prest/crm/evento</url-pattern>
    </servlet-mapping>
    <servlet>
        <servlet-name>ObjecaoServlet</servlet-name>
        <servlet-class>servlet.crm.ObjecaoServlet</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>ObjecaoServlet</servlet-name>
        <url-pattern>/prest/crm/objecao</url-pattern>
    </servlet-mapping>
    <servlet>
        <servlet-name>GrupoColaboradorServlet</servlet-name>
        <servlet-class>servlet.crm.GrupoColaboradorServlet</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>GrupoColaboradorServlet</servlet-name>
        <url-pattern>/prest/crm/grupoColaborador</url-pattern>
    </servlet-mapping>
    <servlet>
        <servlet-name>IndicacaoServlet</servlet-name>
        <servlet-class>servlet.crm.IndicacaoServlet</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>IndicacaoServlet</servlet-name>
        <url-pattern>/prest/crm/indicacao</url-pattern>
    </servlet-mapping>
    <servlet>
        <servlet-name>CRMExtraServlet</servlet-name>
        <servlet-class>servlet.crm.CRMExtraServlet</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>CRMExtraServlet</servlet-name>
        <url-pattern>/prest/crm/crmExtra</url-pattern>
    </servlet-mapping>
    <!--
        Final dos servlets do pacote Relatorio
    -->
    <servlet>
        <servlet-name>LancamentoProdutoColetivoServlet</servlet-name>
        <servlet-class>servlet.plano.LancamentoProdutoColetivoServlet</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>LancamentoProdutoColetivoServlet</servlet-name>
        <url-pattern>/prest/plano/lancamentoProdutoColetivo</url-pattern>
    </servlet-mapping>
    <!--
       Inicio servlet controle acesso
   -->
    <servlet>
        <servlet-name>ValidacaoAcessoServletControle</servlet-name>
        <servlet-class>br.com.pactosolucoes.controle.json.acesso.v1.ValidacaoAcessoServletControle</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>ValidacaoAcessoServletControle</servlet-name>
        <url-pattern>/prest/validaracesso</url-pattern>
    </servlet-mapping>

    <servlet>
        <servlet-name>ValidacaoAcessoV2ServletControle</servlet-name>
        <servlet-class>br.com.pactosolucoes.controle.json.acesso.v2.ValidacaoAcessoServletControle</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>ValidacaoAcessoV2ServletControle</servlet-name>
        <url-pattern>/prest/v2/validaracesso</url-pattern>
    </servlet-mapping>
    <!--
        Fim servlet controle acesso
    -->
    <servlet>
        <servlet-name>RegistrarAcessoServletControle</servlet-name>
        <servlet-class>br.com.pactosolucoes.controle.json.acesso.RegistrarAcessoServletControle</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>RegistrarAcessoServletControle</servlet-name>
        <url-pattern>/prest/registraracesso</url-pattern>
    </servlet-mapping>
    <!--
        Inicio servlet aula cheia
    -->
    <servlet>
        <servlet-name>AulaCheiaServletControle</servlet-name>
        <servlet-class>br.com.pactosolucoes.controle.json.aulacheia.AulaCheiaServletControle</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>AulaCheiaServletControle</servlet-name>
        <url-pattern>/prest/aulacheia</url-pattern>
    </servlet-mapping>
    <!--
        Inicio servlet aula cheia
    -->
    <!--
Inicio servlet simples
-->
    <servlet>
        <servlet-name>MSFinanceiroServlet</servlet-name>
        <servlet-class>servlet.appGestor.MSFinanceiroServlet</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>MSFinanceiroServlet</servlet-name>
        <url-pattern>/prest/financeiroGestor</url-pattern>
    </servlet-mapping>
        <!--
    Inicio servlet finaneiroGestor
-->
    <servlet>
        <servlet-name>MSAlunoServlet</servlet-name>
        <servlet-class>servlet.appGestor.MSAlunoServlet</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>MSAlunoServlet</servlet-name>
        <url-pattern>/prest/alunoGestor</url-pattern>
    </servlet-mapping>
    <!--
Inicio servlet finaneiroGestor
-->

    <servlet>
        <servlet-name>MSVendasServlet</servlet-name>
        <servlet-class>servlet.appGestor.MSVendasServlet</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>MSVendasServlet</servlet-name>
        <url-pattern>/prest/vendasGestor</url-pattern>
    </servlet-mapping>
    <servlet>
        <!--
    Inicio servlet simples
    -->
        <servlet-name>ToggleSimplesServlet</servlet-name>
        <servlet-class>br.com.pactosolucoes.controle.json.empresa.ToggleSimplesServlet</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>ToggleSimplesServlet</servlet-name>
        <url-pattern>/prest/toggle</url-pattern>
    </servlet-mapping>
    <!--
            Fim servlet simples
        -->
    <!--
        Inicio servlet CLIENTE
    -->
    <servlet>
        <servlet-name>ClienteServletControle</servlet-name>
        <servlet-class>br.com.pactosolucoes.controle.json.cliente.ClienteServletControle</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>ClienteServletControle</servlet-name>
        <url-pattern>/prest/cliente</url-pattern>
    </servlet-mapping>
    <!--
        FIM servlet CLIENTE
    -->
    <!--
    Inicio servlet ESTOQUE
-->
    <servlet>
        <servlet-name>EstoqueServletControle</servlet-name>
        <servlet-class>br.com.pactosolucoes.controle.json.estoque.EstoqueServletControle</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>EstoqueServletControle</servlet-name>
        <url-pattern>/prest/estoque/v2/</url-pattern>
    </servlet-mapping>
    <!--
        FIM servlet ESTOQUE
    -->
    <!--
        Final Servlets para consultas;
    -->
    <error-page>
        <exception-type>controle.arquitetura.exceptions.SessaoExpiradaException</exception-type>
        <location>/login.jsp</location>
    </error-page>
    <error-page>
        <exception-type>java.lang.Exception</exception-type>
        <location>/paginaErro.jsp</location>
    </error-page>
    <error-page>
        <exception-type>javax.faces.FacesException</exception-type>
        <location>/paginaErro.jsp</location>
    </error-page>
    <error-page>
        <exception-type>javax.servlet.ServletException</exception-type>
        <location>/paginaErro.jsp</location>
    </error-page>
    <error-page>
        <error-code>500</error-code>
        <location>/paginaErro.jsp</location>
    </error-page>
    <error-page>
        <error-code>404</error-code>
        <location>/paginaErro.jsp</location>
    </error-page>
    <error-page>
        <error-code>403</error-code>
        <location>/paginaErro.jsp</location>
    </error-page>
    <!-- INÃƒï¿½CIO: Central de Eventos -->
    <servlet>
        <description>Servlet para exibiÃƒÂ§ÃƒÂ£o de imagens armazenadas no SO</description>
        <servlet-name>CEImg</servlet-name>
        <servlet-class>controle.arquitetura.servlet.ImgServlet</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>CEImg</servlet-name>
        <url-pattern>/ce/img</url-pattern>
    </servlet-mapping>
    <servlet>
        <description>Servlet para exibiÃƒÂ§ÃƒÂ£o dos contratos de locaÃƒÂ§ÃƒÂ£o de espaÃƒÂ§o</description>
        <servlet-name>CEContrato</servlet-name>
        <servlet-class>controle.arquitetura.servlet.ContratoServlet</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>CEContrato</servlet-name>
        <url-pattern>/faces/pages/ce/eventos/contrato</url-pattern>
    </servlet-mapping>
    <!--Produto Locação Central Eventos(CE)-->
    <servlet>
        <servlet-name>ProdutoLocacaoServlet</servlet-name>
        <servlet-class>servlet.basico.ProdutoLocacaoServlet</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>ProdutoLocacaoServlet</servlet-name>
        <url-pattern>/prest/basico/produtolocacao</url-pattern>
    </servlet-mapping>
    <!--Tipo Ambiente Central Eventos(CE)-->
    <servlet>
        <servlet-name>TipoAmbienteServlet</servlet-name>
        <servlet-class>servlet.basico.TipoAmbienteServlet</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>TipoAmbienteServlet</servlet-name>
        <url-pattern>/prest/basico/tipoambiente</url-pattern>
    </servlet-mapping>
    <!--Serviço Central Eventos(CE)-->
    <servlet>
        <servlet-name>ServicoServlet</servlet-name>
        <servlet-class>servlet.basico.ServicoServlet</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>ServicoServlet</servlet-name>
        <url-pattern>/prest/basico/servico</url-pattern>
    </servlet-mapping>
    <!-- FIM: Central de Eventos -->
    <servlet>
        <servlet-name>HistoricoArmarioServlet</servlet-name>
        <servlet-class>servlet.basico.HistoricoArmarioServlet</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>HistoricoArmarioServlet</servlet-name>
        <url-pattern>/prest/basico/armario</url-pattern>
    </servlet-mapping>
    <!--Fornecedor Financeiro-->
    <servlet>
        <servlet-name>FornecedorServlet</servlet-name>
        <servlet-class>servlet.basico.FornecedorServlet</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>FornecedorServlet</servlet-name>
        <url-pattern>/prest/basico/fornecedor</url-pattern>
    </servlet-mapping>
    <servlet>
        <servlet-name>ConfiguracaoNotaFiscalServlet</servlet-name>
        <servlet-class>servlet.basico.ConfiguracaoNotaFiscalServlet</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>ConfiguracaoNotaFiscalServlet</servlet-name>
        <url-pattern>/prest/basico/configuracaoNotaFiscal</url-pattern>
    </servlet-mapping>
    <!--Contas Financeiro-->
    <servlet>
        <servlet-name>ContaServlet</servlet-name>
        <servlet-class>servlet.basico.ContaServlet</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>ContaServlet</servlet-name>
        <url-pattern>/prest/basico/conta</url-pattern>
    </servlet-mapping>
    <!--Tipo Conta Financeiro-->
    <servlet>
        <servlet-name>TipoContaServlet</servlet-name>
        <servlet-class>servlet.basico.TipoContaServlet</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>TipoContaServlet</servlet-name>
        <url-pattern>/prest/basico/tipoconta</url-pattern>
    </servlet-mapping>
    <!--Tipo Documento Financeiro-->
    <servlet>
        <servlet-name>TipoDocumentoServlet</servlet-name>
        <servlet-class>servlet.basico.TipoDocumentoServlet</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>TipoDocumentoServlet</servlet-name>
        <url-pattern>/prest/basico/tipodocumento</url-pattern>
    </servlet-mapping>
    <servlet>
        <servlet-name>QRCode</servlet-name>
        <servlet-class>controle.arquitetura.servico.QRCodeServlet</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>QRCode</servlet-name>
        <url-pattern>/QRCode</url-pattern>
    </servlet-mapping>
    <!--Sorteio-->
    <servlet>
        <servlet-name>SorteioServlet</servlet-name>
        <servlet-class>servlet.basico.SorteioServlet</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>SorteioServlet</servlet-name>
        <url-pattern>/prest/basico/sorteio</url-pattern>
    </servlet-mapping>
    <!--Operação Coletiva-->
    <servlet>
        <servlet-name>OperacaoColetivaServlet</servlet-name>
        <servlet-class>servlet.basico.OperacaoColetivaServlet</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>OperacaoColetivaServlet</servlet-name>
        <url-pattern>/prest/basico/operacaoColetiva</url-pattern>
    </servlet-mapping>
    <!--Acesso Cliente-->
    <servlet>
        <servlet-name>AcessoClienteServlet</servlet-name>
        <servlet-class>servlet.acesso.AcessoClienteServlet</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>AcessoClienteServlet</servlet-name>
        <url-pattern>/prest/acessocliente</url-pattern>
    </servlet-mapping>
    <servlet>
        <servlet-name>LogTotalPassClienteServlet</servlet-name>
        <servlet-class>servlet.acesso.LogTotalPassClienteServlet</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>LogTotalPassClienteServlet</servlet-name>
        <url-pattern>/prest/logTotalPasscliente</url-pattern>
    </servlet-mapping>
    <servlet>
        <servlet-name>BINovoTreinoServlet</servlet-name>
        <servlet-class>servlet.acesso.BINovoTreinoServlet</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>BINovoTreinoServlet</servlet-name>
        <url-pattern>/prest/bi-novotreino</url-pattern>
    </servlet-mapping>
    <servlet>
        <servlet-name>AcessoColaboradorServlet</servlet-name>
        <servlet-class>servlet.acesso.AcessoColaboradorServlet</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>AcessoColaboradorServlet</servlet-name>
        <url-pattern>/prest/acessoColaborador</url-pattern>
    </servlet-mapping>
    <servlet>
        <servlet-name>WebHookVindiServlet</servlet-name>
        <servlet-class>servlet.financeiro.WebHookVindiServlet</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>WebHookVindiServlet</servlet-name>
        <url-pattern>/webhook</url-pattern>
    </servlet-mapping>
    <servlet>
        <servlet-name>LancarBrindeClienteServlet</servlet-name>
        <servlet-class>servlet.relatorio.LancarBrindeClienteServlet</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>LancarBrindeClienteServlet</servlet-name>
        <url-pattern>/prest/relatorio/lancarbrindecliente</url-pattern>
    </servlet-mapping>
    <servlet>
        <servlet-name>WebHookRDStation</servlet-name>
        <servlet-class>servlet.crm.WebHookRDStation</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>WebHookRDStation</servlet-name>
        <url-pattern>/webhookrd</url-pattern>
    </servlet-mapping>
    <servlet>
        <servlet-name>MockServlet</servlet-name>
        <servlet-class>servlet.arquitetura.MockServlet</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>MockServlet</servlet-name>
        <url-pattern>/mock</url-pattern>
    </servlet-mapping>

    <servlet>
        <servlet-name>PactoGenericoServlet</servlet-name>
        <servlet-class>servlet.arquitetura.PactoGenericoServlet</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>PactoGenericoServlet</servlet-name>
        <url-pattern>/prest/generico</url-pattern>
    </servlet-mapping>

    <mime-mapping>
        <extension>csv</extension>
        <mime-type>text/csv</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>csv</extension>
        <mime-type>application/csv</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>csv</extension>
        <mime-type>application/excel</mime-type>
    </mime-mapping>

    <servlet>
        <servlet-name>ConfirmacaoEmailCompraServlet</servlet-name>
        <servlet-class>servlet.cliente.ConfirmacaoEmailCompraServlet</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>ConfirmacaoEmailCompraServlet</servlet-name>
        <url-pattern>/confirmacaoEmailCompra</url-pattern>
    </servlet-mapping>

    <servlet>
        <servlet-name>IndicadoresClienteServlet</servlet-name>
        <servlet-class>servlet.indicadores.IndicadoresClienteServlet</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>IndicadoresClienteServlet</servlet-name>
        <url-pattern>/prest/indicadores/cliente</url-pattern>
    </servlet-mapping>

    <servlet>
        <servlet-name>PrintServlet</servlet-name>
        <servlet-class>servlet.arquitetura.PrintServlet</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>PrintServlet</servlet-name>
        <url-pattern>/monitor/print</url-pattern>
    </servlet-mapping>

    <servlet>
        <servlet-name>AragornServlet</servlet-name>
        <servlet-class>servlet.arquitetura.AragornServlet</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>AragornServlet</servlet-name>
        <url-pattern>/prest/aragorn/ativar</url-pattern>
    </servlet-mapping>
    <servlet-mapping>
        <servlet-name>AragornServlet</servlet-name>
        <url-pattern>/prest/aragorn/migrador</url-pattern>
    </servlet-mapping>
    <servlet-mapping>
        <servlet-name>AragornServlet</servlet-name>
        <url-pattern>/prest/aragorn/revert</url-pattern>
    </servlet-mapping>
    <servlet-mapping>
        <servlet-name>AragornServlet</servlet-name>
        <url-pattern>/prest/aragorn/removerHistorico</url-pattern>
    </servlet-mapping>

    <servlet>
        <servlet-name>RetornoCartaoServlet</servlet-name>
        <servlet-class>servlet.cobranca.RetornoCartaoServlet</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>RetornoCartaoServlet</servlet-name>
        <url-pattern>/prest/cobranca</url-pattern>
    </servlet-mapping>

    <servlet>
        <servlet-name>VendasOnlineServlet</servlet-name>
        <servlet-class>servlet.cobranca.VendasOnlineServlet</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>VendasOnlineServlet</servlet-name>
        <url-pattern>/prest/vendasOnline</url-pattern>
    </servlet-mapping>

    <servlet>
        <servlet-name>AutorizacaoCobrancaServlet</servlet-name>
        <servlet-class>servlet.cobranca.AutorizacaoCobrancaServlet</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>AutorizacaoCobrancaServlet</servlet-name>
        <url-pattern>/prest/autorizacaoCobranca</url-pattern>
    </servlet-mapping>

    <servlet>
        <servlet-name>VendasOnLineCampanhaIcvServlet</servlet-name>
        <servlet-class>servlet.vendasonline.VendasOnLineCampanhaIcvServlet</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>VendasOnLineCampanhaIcvServlet</servlet-name>
        <url-pattern>/prest/vendasOnlineCampanhaIcv</url-pattern>
    </servlet-mapping>

    <servlet>
        <servlet-name>VendaRapidaServlet</servlet-name>
        <servlet-class>servlet.cobranca.VendaRapidaServlet</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>VendaRapidaServlet</servlet-name>
        <url-pattern>/prest/vendarapida</url-pattern>
    </servlet-mapping>

    <servlet>
        <servlet-name>RecebiveisServlet</servlet-name>
        <servlet-class>servlet.financeiro.RecebiveisServlet</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>RecebiveisServlet</servlet-name>
        <url-pattern>/prest/recebiveis</url-pattern>
    </servlet-mapping>

    <servlet>
        <servlet-name>HotSiteServlet</servlet-name>
        <servlet-class>servlet.cobranca.HotSiteServlet</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>HotSiteServlet</servlet-name>
        <url-pattern>/prest/hotSite</url-pattern>
    </servlet-mapping>


    <servlet>
        <servlet-name>PactoStoreServlet</servlet-name>
        <servlet-class>servlet.canalCliente.PactoStoreServlet</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>PactoStoreServlet</servlet-name>
        <url-pattern>/prest/pactostore</url-pattern>
    </servlet-mapping>

    <servlet>
        <servlet-name>PactoPayServlet</servlet-name>
        <servlet-class>servlet.pactoPay.PactoPayServlet</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>PactoPayServlet</servlet-name>
        <url-pattern>/prest/pactoPay</url-pattern>
    </servlet-mapping>

    <servlet>
        <servlet-name>PactoPayDashServlet</servlet-name>
        <servlet-class>servlet.pactoPay.PactoPayDashServlet</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>PactoPayDashServlet</servlet-name>
        <url-pattern>/prest/pactopay/dash</url-pattern>
    </servlet-mapping>

    <servlet>
        <servlet-name>PactoPayConfigServlet</servlet-name>
        <servlet-class>servlet.pactoPay.PactoPayConfigServlet</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>PactoPayConfigServlet</servlet-name>
        <url-pattern>/prest/pactopay/config</url-pattern>
    </servlet-mapping>

    <servlet>
        <servlet-name>PactoPayUtilServlet</servlet-name>
        <servlet-class>servlet.pactoPay.PactoPayUtilServlet</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>PactoPayUtilServlet</servlet-name>
        <url-pattern>/prest/pactopay/util</url-pattern>
    </servlet-mapping>

    <servlet>
        <servlet-name>PactoPayTransacaoServlet</servlet-name>
        <servlet-class>servlet.pactoPay.PactoPayTransacaoServlet</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>PactoPayTransacaoServlet</servlet-name>
        <url-pattern>/prest/pactopay/transacao</url-pattern>
    </servlet-mapping>

    <servlet>
        <servlet-name>PactoPayPixServlet</servlet-name>
        <servlet-class>servlet.pactoPay.PactoPayPixServlet</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>PactoPayPixServlet</servlet-name>
        <url-pattern>/prest/pactopay/pix</url-pattern>
    </servlet-mapping>

    <servlet>
        <servlet-name>PactoPayBoletoServlet</servlet-name>
        <servlet-class>servlet.pactoPay.PactoPayBoletoServlet</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>PactoPayBoletoServlet</servlet-name>
        <url-pattern>/prest/pactopay/boleto</url-pattern>
    </servlet-mapping>

    <servlet>
        <servlet-name>TransacaoWebhook</servlet-name>
        <servlet-class>servlet.transacao.WebhookTransacao</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>TransacaoWebhook</servlet-name>
        <url-pattern>/prest/transacao/webhook</url-pattern>
    </servlet-mapping>

    <servlet>
        <servlet-name>PixWebhookServlet</servlet-name>
        <servlet-class>servlet.pix.PixWebhookServlet</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>PixWebhookServlet</servlet-name>
        <url-pattern>/prest/pix/webhook</url-pattern>
    </servlet-mapping>

    <servlet>
        <servlet-name>KobanaWebhookServlet</servlet-name>
        <servlet-class>servlet.kobana.KobanaWebhookServlet</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>KobanaWebhookServlet</servlet-name>
        <url-pattern>/prest/kobana/webhook</url-pattern>
    </servlet-mapping>

    <servlet>
        <servlet-name>PinpadWebhookServlet</servlet-name>
        <servlet-class>servlet.pinpad.PinpadWebhookServlet</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>PinpadWebhookServlet</servlet-name>
        <url-pattern>/prest/pinpad/webhook</url-pattern>
    </servlet-mapping>

    <servlet>
        <servlet-name>BoletoServlet</servlet-name>
        <servlet-class>servlet.boleto.BoletoServlet</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>BoletoServlet</servlet-name>
        <url-pattern>/prest/boleto/todos</url-pattern>
    </servlet-mapping>

    <servlet>
        <servlet-name>BoletoWebhookServlet</servlet-name>
        <servlet-class>servlet.boleto.BoletoWebhookServlet</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>BoletoWebhookServlet</servlet-name>
        <url-pattern>/prest/boleto/webhook</url-pattern>
    </servlet-mapping>

    <servlet>
        <servlet-name>TotemServlet</servlet-name>
        <servlet-class>servlet.totem.TotemServlet</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>TotemServlet</servlet-name>
        <url-pattern>/prest/totem</url-pattern>
    </servlet-mapping>

    <servlet>
        <servlet-name>BoletoImpressaoServlet</servlet-name>
        <servlet-class>servlet.boleto.BoletoImpressaoServlet</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>BoletoImpressaoServlet</servlet-name>
        <url-pattern>/prest/boleto/imprimir</url-pattern>
    </servlet-mapping>

    <servlet>
        <servlet-name>BoletoGeradoServlet</servlet-name>
        <servlet-class>servlet.boleto.TokenBoletoServlet</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>BoletoGeradoServlet</servlet-name>
        <url-pattern>/prest/token-boleto</url-pattern>
    </servlet-mapping>

    <servlet>
        <servlet-name>CreditoDCCServlet</servlet-name>
        <servlet-class>servlet.arquitetura.CreditoDCCServlet</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>CreditoDCCServlet</servlet-name>
        <url-pattern>/prest/creditoPacto</url-pattern>
    </servlet-mapping>


    <servlet>
        <servlet-name>UteisPactoServlet</servlet-name>
        <servlet-class>servlet.arquitetura.UteisPactoServlet</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>UteisPactoServlet</servlet-name>
        <url-pattern>/prest/pacto</url-pattern>
    </servlet-mapping>

    <servlet>
        <servlet-name>UteisProcessoServlet</servlet-name>
        <servlet-class>servlet.arquitetura.UteisProcessoServlet</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>UteisProcessoServlet</servlet-name>
        <url-pattern>/prest/processos</url-pattern>
    </servlet-mapping>

    <servlet>
        <servlet-name>TelaClienteServlet</servlet-name>
        <servlet-class>servlet.arquitetura.TelaClienteServlet</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>TelaClienteServlet</servlet-name>
        <url-pattern>/prest/tela-cliente</url-pattern>
    </servlet-mapping>

    <servlet>
        <servlet-name>UpdateCacheServlet</servlet-name>
        <servlet-class>servlet.arquitetura.cache.UpdateCacheServlet</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>UpdateCacheServlet</servlet-name>
        <url-pattern>/prest/update-cache</url-pattern>
    </servlet-mapping>

    <servlet>
        <servlet-name>SinteticoClienteServlet</servlet-name>
        <servlet-class>servlet.arquitetura.SinteticoClienteServlet</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>SinteticoClienteServlet</servlet-name>
        <url-pattern>/prest/sintetico-cliente</url-pattern>
    </servlet-mapping>

    <servlet>
        <servlet-name>ImportacaoCartaoServlet</servlet-name>
        <servlet-class>servlet.arquitetura.ImportacaoCartaoServlet</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>ImportacaoCartaoServlet</servlet-name>
        <url-pattern>/prest/importcc</url-pattern>
    </servlet-mapping>

    <servlet>
        <servlet-name>UteisTransacaoServlet</servlet-name>
        <servlet-class>servlet.arquitetura.UteisTransacaoServlet</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>UteisTransacaoServlet</servlet-name>
        <url-pattern>/prest/util/transacao</url-pattern>
    </servlet-mapping>

    <servlet>
        <servlet-name>DadosGerenciaisServlet</servlet-name>
        <servlet-class>servlet.dadosgerenciais.DadosGerenciaisServlet</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>DadosGerenciaisServlet</servlet-name>
        <url-pattern>/prest/dadosgerenciais</url-pattern>
    </servlet-mapping>

    <servlet>
        <servlet-name>GympassBookingServlet</servlet-name>
        <servlet-class>servlet.integracao.GympassBookingServlet</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>GympassBookingServlet</servlet-name>
        <url-pattern>/prest/gympassbooking</url-pattern>
    </servlet-mapping>

    <servlet>
        <servlet-name>GoGoodCheckinServlet</servlet-name>
        <servlet-class>servlet.integracao.GoGoodCheckinServlet</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>GoGoodCheckinServlet</servlet-name>
        <url-pattern>/prest/gogood/checkin</url-pattern>
    </servlet-mapping>

    <servlet>
        <servlet-name>GympassCheckinServlet</servlet-name>
        <servlet-class>servlet.integracao.GympassCheckinServlet</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>GympassCheckinServlet</servlet-name>
        <url-pattern>/prest/gympass/checkin</url-pattern>
    </servlet-mapping>
    <servlet-mapping>
        <servlet-name>GympassCheckinServlet</servlet-name>
        <url-pattern>/prest/gympass/checkinAcessControl</url-pattern>
    </servlet-mapping>
    <servlet-mapping>
        <servlet-name>GympassCheckinServlet</servlet-name>
        <url-pattern>/prest/gympass/checkinRemoveAcessControl</url-pattern>
    </servlet-mapping>
    <servlet-mapping>
        <servlet-name>GympassCheckinServlet</servlet-name>
        <url-pattern>/prest/gympass/gymid</url-pattern>
    </servlet-mapping>

    <servlet>
        <servlet-name>ModuloFinanceiroServlet</servlet-name>
        <servlet-class>servlet.financeiro.ModuloFinanceiroServlet</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>ModuloFinanceiroServlet</servlet-name>
        <url-pattern>/prest/financeiro</url-pattern>
    </servlet-mapping>

    <servlet>
        <servlet-name>FinanceiroServlet</servlet-name>
        <servlet-class>servlet.canalCliente.FinanceiroServlet</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>FinanceiroServlet</servlet-name>
        <url-pattern>/prest/canalCliente/consultar-parcelas</url-pattern>
    </servlet-mapping>
    <servlet-mapping>
        <servlet-name>FinanceiroServlet</servlet-name>
        <url-pattern>/prest/canalCliente/emitir-boleto</url-pattern>
    </servlet-mapping>
    <servlet-mapping>
        <servlet-name>FinanceiroServlet</servlet-name>
        <url-pattern>/prest/canalCliente/consultar-notas</url-pattern>
    </servlet-mapping>
    <servlet-mapping>
        <servlet-name>FinanceiroServlet</servlet-name>
        <url-pattern>/prest/canalCliente/consultar-cnpj</url-pattern>
    </servlet-mapping>
    <servlet-mapping>
        <servlet-name>FinanceiroServlet</servlet-name>
        <url-pattern>/prest/canalCliente/consultar-empresa</url-pattern>
    </servlet-mapping>
    <servlet-mapping>
        <servlet-name>FinanceiroServlet</servlet-name>
        <url-pattern>/prest/canalCliente/imprimir-nota</url-pattern>
    </servlet-mapping>

    <servlet>
        <servlet-name>VersaoServlet</servlet-name>
        <servlet-class>servlet.arquitetura.VersaoServlet</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>VersaoServlet</servlet-name>
        <url-pattern>/prest/versao</url-pattern>
    </servlet-mapping>

    <servlet>
        <servlet-name>HealthCheckServlet</servlet-name>
        <servlet-class>servlet.arquitetura.HealthCheckServlet</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>HealthCheckServlet</servlet-name>
        <url-pattern>/prest/health</url-pattern>
    </servlet-mapping>

    <servlet>
        <servlet-name>RoboServlet</servlet-name>
        <servlet-class>servlet.arquitetura.RoboServlet</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>RoboServlet</servlet-name>
        <url-pattern>/prest/robo</url-pattern>
    </servlet-mapping>

    <servlet>
        <servlet-name>IntegracaoTurmaServletControle</servlet-name>
        <servlet-class>br.com.pactosolucoes.controle.json.turma.IntegracaoTurmaServletControle</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>IntegracaoTurmaServletControle</servlet-name>
        <url-pattern>/prest/integracaoTurma</url-pattern>
    </servlet-mapping>
    <servlet>
        <servlet-name>ImportacaoServlet</servlet-name>
        <servlet-class>br.com.pactosolucoes.controle.json.importacao.ImportacaoServletControle</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>ImportacaoServlet</servlet-name>
        <url-pattern>/prest/importacao</url-pattern>
    </servlet-mapping>
    <servlet>
        <servlet-name>NegociacaoServlet</servlet-name>
        <servlet-class>br.com.pactosolucoes.controle.json.negociacao.NegociacaoServletControle</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>NegociacaoServlet</servlet-name>
        <url-pattern>/prest/negociacao</url-pattern>
    </servlet-mapping>
    <servlet>
        <servlet-name>UsuarioServletV4</servlet-name>
        <servlet-class>servlet.arquitetura.UsuarioServletV4</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>UsuarioServletV4</servlet-name>
        <url-pattern>/user/pactoConversa/add</url-pattern>
    </servlet-mapping>
    <servlet>
        <servlet-name>IntegracaoCadastroServletControle</servlet-name>
        <servlet-class>br.com.pactosolucoes.controle.json.cadastro.IntegracaoCadastroServletControle</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>IntegracaoCadastroServletControle</servlet-name>
        <url-pattern>/prest/integracaoCadastro</url-pattern>
    </servlet-mapping>
    <servlet>
        <servlet-name>InadimplenteServletControle</servlet-name>
        <servlet-class>br.com.pactosolucoes.controle.json.inadimplente.InadimplenteServletControle</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>InadimplenteServletControle</servlet-name>
        <url-pattern>/prest/inadimplentes</url-pattern>
    </servlet-mapping>
    <servlet>
        <servlet-name>IntegracaoImportacaoServletControle</servlet-name>
        <servlet-class>br.com.pactosolucoes.controle.json.cadastro.IntegracaoImportacaoServletControle</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>IntegracaoImportacaoServletControle</servlet-name>
        <url-pattern>/prest/integracaoImportacao</url-pattern>
    </servlet-mapping>
    <servlet>
        <servlet-name>MovProdutoServletControle</servlet-name>
        <servlet-class>br.com.pactosolucoes.controle.json.movproduto.MovProdutoServletControle</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>MovProdutoServletControle</servlet-name>
        <url-pattern>/prest/movProduto</url-pattern>
    </servlet-mapping>

    <servlet>
        <servlet-name>IntegracaoCadastroServletControlePersonal</servlet-name>
        <servlet-class>br.com.pactosolucoes.controle.json.turma.IntegracaoCadastroServletControle</servlet-class>
    </servlet>

    <servlet>
        <servlet-name>BiAppGestorRestServlet</servlet-name>
        <servlet-class>servlet.appGestor.BiAppGestorRestServlet</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>BiAppGestorRestServlet</servlet-name>
        <url-pattern>/prest/dadosBiAppGestor</url-pattern>
    </servlet-mapping>

    <servlet-mapping>
        <servlet-name>IntegracaoCadastroServletControlePersonal</servlet-name>
        <url-pattern>/prest/consultarInformacoesPersonal</url-pattern>
    </servlet-mapping>

    <servlet-mapping>
        <servlet-name>IntegracaoCadastroServletControlePersonal</servlet-name>
        <url-pattern>/prest/cancelarPersonal</url-pattern>
    </servlet-mapping>

    <servlet-mapping>
        <servlet-name>IntegracaoCadastroServletControlePersonal</servlet-name>
        <url-pattern>/prest/existeEmail</url-pattern>
    </servlet-mapping>
    <servlet>
        <servlet-name>HomeFitRestServlet</servlet-name>
        <servlet-class>servlet.homeFit.HomeFitRestServlet</servlet-class>
    </servlet>

    <servlet-mapping>
        <servlet-name>HomeFitRestServlet</servlet-name>
        <url-pattern>/prest/validarProdutoHomeFit</url-pattern>
    </servlet-mapping>

    <servlet>
        <servlet-name>BioTotemRestServlet</servlet-name>
        <servlet-class>servlet.bioTotem.BioTotemRestServlet</servlet-class>
    </servlet>

    <servlet-mapping>
        <servlet-name>BioTotemRestServlet</servlet-name>
        <url-pattern>/prest/validarProdutoBioTotem</url-pattern>
    </servlet-mapping>

    <servlet>
        <servlet-name>ConsultaNutricionalRestServlet</servlet-name>
        <servlet-class>servlet.consultaNutricional.ConsultaNutricionalRestServlet</servlet-class>
    </servlet>

    <servlet-mapping>
        <servlet-name>ConsultaNutricionalRestServlet</servlet-name>
        <url-pattern>/prest/validarProdutoConsultaNutricional</url-pattern>
    </servlet-mapping>

    <servlet>
        <servlet-name>LoginGestorRestServlet</servlet-name>
        <servlet-class>servlet.appGestor.LoginGestorRestServlet</servlet-class>
    </servlet>

    <servlet-mapping>
        <servlet-name>LoginGestorRestServlet</servlet-name>
        <url-pattern>/prest/usuarioAtivo</url-pattern>
    </servlet-mapping>

    <servlet>
        <servlet-name>StatusPersonalRestServlet</servlet-name>
        <servlet-class>servlet.appGestor.StatusPersonalRestServlet</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>StatusPersonalRestServlet</servlet-name>
        <url-pattern>/prest/obterStatusPersonal</url-pattern>
    </servlet-mapping>

    <servlet>
        <servlet-name>PixServlet</servlet-name>
        <servlet-class>servlet.pix.PixServlet</servlet-class>
    </servlet>

    <servlet-mapping>
        <servlet-name>PixServlet</servlet-name>
        <url-pattern>/pix</url-pattern>
    </servlet-mapping>


    <servlet>
        <servlet-name>DadosGerenciaisServletControle</servlet-name>
        <servlet-class>br.com.pactosolucoes.controle.json.dadosGerenciaisPmg.DadosGerenciaisServletControle</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>DadosGerenciaisServletControle</servlet-name>
        <url-pattern>/prest/dadosGerenciais</url-pattern>
    </servlet-mapping>

    <servlet>
        <servlet-name>FluxoCaixaServlet</servlet-name>
        <servlet-class>servlet.financeiro.FluxoCaixaServlet</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>FluxoCaixaServlet</servlet-name>
        <url-pattern>/prest/fluxo-caixa</url-pattern>
    </servlet-mapping>

    <servlet>
        <servlet-name>VendaServlet</servlet-name>
        <servlet-class>servlet.negociacao.VendaServlet</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>VendaServlet</servlet-name>
        <url-pattern>/prest/venda</url-pattern>
    </servlet-mapping>

    <servlet>
        <servlet-name>LancamentoServlet</servlet-name>
        <servlet-class>servlet.financeiro.LancamentoServlet</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>LancamentoServlet</servlet-name>
        <url-pattern>/prest/lancamentos</url-pattern>
    </servlet-mapping>

    <servlet>
        <servlet-name>MetaDiariaServlet</servlet-name>
        <servlet-class>servlet.crm.MetaDiariaServlet</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>MetaDiariaServlet</servlet-name>
        <url-pattern>/prest/meta-diaria</url-pattern>
    </servlet-mapping>

    <servlet>
        <servlet-name>RankingMetaDiariaServlet</servlet-name>
        <servlet-class>servlet.crm.RankingMetaDiariaServlet</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>RankingMetaDiariaServlet</servlet-name>
        <url-pattern>/prest/ranking-meta-diaria</url-pattern>
    </servlet-mapping>

    <servlet>
        <servlet-name>FavorecidoServlet</servlet-name>
        <servlet-class>servlet.canalCliente.FavorecidoServlet</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>FavorecidoServlet</servlet-name>
        <url-pattern>/prest/obter-favorecidos-financeiros</url-pattern>
    </servlet-mapping>

    <servlet>
        <servlet-name>ResultadoServlet</servlet-name>
        <servlet-class>servlet.crm.ResultadoServlet</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>ResultadoServlet</servlet-name>
        <url-pattern>/prest/resultado-crm</url-pattern>
    </servlet-mapping>

    <servlet>
        <servlet-name>PagamentosServlet</servlet-name>
        <servlet-class>servlet.caixaemaberto.PagamentosServlet</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>PagamentosServlet</servlet-name>
        <url-pattern>/prest/app-pacto/pagamentos</url-pattern>
    </servlet-mapping>
    <servlet-mapping>
        <servlet-name>PagamentosServlet</servlet-name>
        <url-pattern>/prest/pagamentos</url-pattern>
    </servlet-mapping>

    <servlet>
        <servlet-name>CaixaEmAbertoServlet</servlet-name>
        <servlet-class>servlet.caixaemaberto.CaixaEmAbertoServlet</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>CaixaEmAbertoServlet</servlet-name>
        <url-pattern>/prest/app-pacto/caixa-em-aberto</url-pattern>
    </servlet-mapping>

    <servlet>
        <servlet-name>AcessosComGymPassServlet</servlet-name>
        <servlet-class>servlet.acesso.AcessosComGymPassServlet</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>AcessosComGymPassServlet</servlet-name>
        <url-pattern>/prest/app-pacto/acessos-com-gympass</url-pattern>
    </servlet-mapping>

    <servlet>
        <servlet-name>AlunosGymPassServlet</servlet-name>
        <servlet-class>servlet.acesso.AlunosGymPassServlet</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>AlunosGymPassServlet</servlet-name>
        <url-pattern>/prest/app-pacto/alunos-gympass</url-pattern>
    </servlet-mapping>

    <servlet>
        <servlet-name>FormasPagamentoServlet</servlet-name>
        <servlet-class>servlet.caixaemaberto.FormasPagamentoServlet</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>FormasPagamentoServlet</servlet-name>
        <url-pattern>/prest/formas-pagamento</url-pattern>
    </servlet-mapping>

    <servlet>
        <servlet-name>AdquirentesServlet</servlet-name>
        <servlet-class>servlet.caixaemaberto.AdquirentesServlet</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>AdquirentesServlet</servlet-name>
        <url-pattern>/prest/adquirentes</url-pattern>
    </servlet-mapping>

    <servlet>
        <servlet-name>OperadorasCartaoServlet</servlet-name>
        <servlet-class>servlet.caixaemaberto.OperadorasCartaoServlet</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>OperadorasCartaoServlet</servlet-name>
        <url-pattern>/prest/operadoras-cartao</url-pattern>
    </servlet-mapping>

    <servlet>
        <servlet-name>PluggyServlet</servlet-name>
        <servlet-class>servlet.pluggy.PluggyServlet</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>PluggyServlet</servlet-name>
        <url-pattern>/prest/integracao/pluggy</url-pattern>
    </servlet-mapping>

    <servlet>
        <servlet-name>BotConversaServlet</servlet-name>
        <servlet-class>servlet.botConversa.BotConversaServlet</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>BotConversaServlet</servlet-name>
        <url-pattern>/prest/integracao/botConversa</url-pattern>
    </servlet-mapping>

    <servlet>
        <servlet-name>IntegracaoKobanaServlet</servlet-name>
        <servlet-class>servlet.kobana.IntegracaoKobanaServlet</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>IntegracaoKobanaServlet</servlet-name>
        <url-pattern>/prest/integracao/kobana</url-pattern>
    </servlet-mapping>

    <servlet>
        <servlet-name>IntegracaoEmpresaServlet</servlet-name>
        <servlet-class>servlet.integracao.IntegracaoEmpresaServlet</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>IntegracaoEmpresaServlet</servlet-name>
        <url-pattern>/prest/integracao/integracao-empresa</url-pattern>
    </servlet-mapping>

    <servlet>
        <servlet-name>ConciliadoraServletControle</servlet-name>
        <servlet-class>br.com.pactosolucoes.controle.json.conciliadora.ConciliadoraServletControle</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>ConciliadoraServletControle</servlet-name>
        <url-pattern>/prest/conciliadora</url-pattern>
    </servlet-mapping>

    <servlet>
        <servlet-name>PagoLivreServletControle</servlet-name>
        <servlet-class>br.com.pactosolucoes.controle.json.pagolivre.PagoLivreServletControle</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>PagoLivreServletControle</servlet-name>
        <url-pattern>/prest/pagolivre</url-pattern>
    </servlet-mapping>

    <servlet>
        <servlet-name>TokenOperacaoServlet</servlet-name>
        <servlet-class>servlet.tokenOperacao.TokenOperacaoServlet</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>TokenOperacaoServlet</servlet-name>
        <url-pattern>/prest/tokenOperacao</url-pattern>
    </servlet-mapping>

    <servlet>
        <servlet-name>ImpressaoPendenteServletIntegracaoDelphi</servlet-name>
        <servlet-class>servlet.pactoprint.ImpressaoPendenteServletIntegracaoDelphi</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>ImpressaoPendenteServletIntegracaoDelphi</servlet-name>
        <url-pattern>/prest/pactoprint/impressoesPendentes</url-pattern>
    </servlet-mapping>
    <servlet>
        <servlet-name>CarteirinhaClienteServletIntegracaoDelphi</servlet-name>
        <servlet-class>servlet.pactoprint.CarteirinhaClienteServletIntegracaoDelphi</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>CarteirinhaClienteServletIntegracaoDelphi</servlet-name>
        <url-pattern>/prest/pactoprint/impressoesCarteirinhas</url-pattern>
    </servlet-mapping>
    <servlet>
        <servlet-name>LocalImpressaoServlet</servlet-name>
        <servlet-class>servlet.pactoprint.LocalImpressaoServlet</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>LocalImpressaoServlet</servlet-name>
        <url-pattern>/prest/basico/localImpressao</url-pattern>
    </servlet-mapping>
    <servlet>
        <servlet-name>LocalImpressaoServletIntegracaoDelphi</servlet-name>
        <servlet-class>servlet.pactoprint.LocalImpressaoServletIntegracaoDelphi</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>LocalImpressaoServletIntegracaoDelphi</servlet-name>
        <url-pattern>/prest/pactoprint/localImpressaoIntegracaoDelphi</url-pattern>
    </servlet-mapping>
    <servlet>
        <servlet-name>contratosCliente</servlet-name>
        <servlet-class>servlet.vendasonline.VendasOnlineContratosClienteServlet</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>contratosCliente</servlet-name>
        <url-pattern>/prest/contatrosCliente</url-pattern>
    </servlet-mapping>
    <servlet>
        <servlet-name>RecebiveisChargebackServlet</servlet-name>
        <servlet-class>servlet.financeiro.RecebiveisChargebackServlet</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>RecebiveisChargebackServlet</servlet-name>
        <url-pattern>/prest/recebiveis/chargeback</url-pattern>
    </servlet-mapping>
    <servlet>
        <servlet-name>ComissaoConsultorServlet</servlet-name>
        <servlet-class>servlet.financeiro.ComissaoConsultorServlet</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>ComissaoConsultorServlet</servlet-name>
        <url-pattern>/prest/relatorios/comissao-consultor</url-pattern>
    </servlet-mapping>
    <servlet>
        <servlet-name>AtualizarContratoServlet</servlet-name>
        <servlet-class>controle.arquitetura.servlet.AtualizaContratoServlet</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>AtualizarContratoServlet</servlet-name>
        <url-pattern>/prest/pacto/atualizar-contrato</url-pattern>
    </servlet-mapping>
    <servlet>
        <servlet-name>ProcessarMetaServlet</servlet-name>
        <servlet-class>servlet.basico.ProcessarMetaServlet</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>ProcessarMetaServlet</servlet-name>
        <url-pattern>/prest/basico/processarMetas</url-pattern>
    </servlet-mapping>
    <servlet>
        <servlet-name>DREFinanceiroServlet</servlet-name>
        <servlet-class>servlet.financeiro.DREFinanceiroServlet</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>DREFinanceiroServlet</servlet-name>
        <url-pattern>/prest/relatorios/dre-financeiro</url-pattern>
    </servlet-mapping>
    <servlet>
        <servlet-name>DemonstrativoFinanceiroServlet</servlet-name>
        <servlet-class>servlet.financeiro.DemonstrativoFinanceiroServlet</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>DemonstrativoFinanceiroServlet</servlet-name>
        <url-pattern>/prest/relatorios/demonstrativo-financeiro</url-pattern>
    </servlet-mapping>
</web-app>
