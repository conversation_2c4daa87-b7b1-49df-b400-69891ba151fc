<faces-config
        version="1.2"
        xmlns="http://java.sun.com/xml/ns/javaee"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://java.sun.com/xml/ns/javaee http://java.sun.com/xml/ns/javaee/web-facesconfig_1_2.xsd">
    <navigation-rule>
        <description>Navegacao da pagina</description>
        <from-view-id>/*</from-view-id>

        <navigation-case>
            <from-outcome>pessoaCons</from-outcome>
            <to-view-id>/pessoaCons.jsp</to-view-id>
            <redirect/>
        </navigation-case>

        <navigation-case>
            <from-outcome>mensagemBuilder</from-outcome>
            <to-view-id>/mensagemBuilder.jsp</to-view-id>
            <redirect/>
        </navigation-case>

        <navigation-case>
            <from-outcome>pagamento</from-outcome>
            <to-view-id>/pagamento.jsp</to-view-id>
            <redirect/>
        </navigation-case>

        <navigation-case>
            <from-outcome>login</from-outcome>
            <to-view-id>/tela1.jsp</to-view-id>
            <redirect/>
        </navigation-case>

        <navigation-case>
            <from-outcome>preCadastro</from-outcome>
            <to-view-id>/preCadastro.jsp</to-view-id>
            <redirect/>
        </navigation-case>

        <navigation-case>
            <from-outcome>geradorConsulta</from-outcome>
            <to-view-id>/telaGeradorConsultas.jsp</to-view-id>
            <redirect/>
        </navigation-case>

        <navigation-case>
            <from-outcome>controleEstoque</from-outcome>
            <to-view-id>/indexControleEstoque.jsp</to-view-id>
            <redirect/>
        </navigation-case>

        <navigation-case>
            <from-outcome>indexBasico</from-outcome>
            <to-view-id>/indexBasico.jsp</to-view-id>
            <redirect/>
        </navigation-case>

        <navigation-case>
            <from-outcome>indexPlano</from-outcome>
            <to-view-id>/indexPlano.jsp</to-view-id>
            <redirect/>
        </navigation-case>

        <navigation-case>
            <from-outcome>indexFinanceiro</from-outcome>
            <to-view-id>/indexFinanceiro.jsp</to-view-id>
            <redirect/>
        </navigation-case>

        <navigation-case>
            <from-outcome>indexContrato</from-outcome>
            <to-view-id>/indexContrato.jsp</to-view-id>
            <redirect/>
        </navigation-case>

        <navigation-case>
            <from-outcome>indexArquitetura</from-outcome>
            <to-view-id>/indexArquitetura.jsp</to-view-id>
            <redirect/>
        </navigation-case>

        <navigation-case>
            <from-outcome>relatorioVisitantes</from-outcome>
            <to-view-id>/listasRelatoriosVisitantes.jsp</to-view-id>
            <redirect/>
        </navigation-case>

        <navigation-case>
            <from-outcome>relatorioAlunoCancelados</from-outcome>
            <to-view-id>/listasRelatoriosAlunosCancelados.jsp</to-view-id>
            <redirect/>
        </navigation-case>

        <navigation-case>
            <from-outcome>relatorioAlunoTrancados</from-outcome>
            <to-view-id>/listasRelatoriosAlunosTrancados.jsp</to-view-id>
            <redirect/>
        </navigation-case>

        <navigation-case>
            <from-outcome>relatorioAlunosBonus</from-outcome>
            <to-view-id>/listasRelatoriosAlunosBonus.jsp</to-view-id>
            <redirect/>
        </navigation-case>

        <navigation-case>
            <from-outcome>relatorioAlunosAtestado</from-outcome>
            <to-view-id>/listasRelatoriosAlunosAtestado.jsp</to-view-id>
            <redirect/>
        </navigation-case>

        <navigation-case>
            <from-outcome>cadastros</from-outcome>
            <to-view-id>/telaModulo.jsp</to-view-id>
            <redirect/>
        </navigation-case>

        <navigation-case>
            <from-outcome>listasRelatorios</from-outcome>
            <to-view-id>/telaListaRelatorio.jsp</to-view-id>
            <redirect/>
        </navigation-case>

        <navigation-case>
            <from-outcome>notaFiscal</from-outcome>
            <to-view-id>/notaFiscal.jsp</to-view-id>
            <redirect/>
        </navigation-case>

        <navigation-case>
            <from-outcome>crm</from-outcome>
            <to-view-id>/telaInicialCRM.jsp</to-view-id>
            <redirect/>
        </navigation-case>
        <navigation-case>
            <from-outcome>telaBICRM</from-outcome>
            <to-view-id>/telaBICRM.jsp</to-view-id>
            <redirect/>
        </navigation-case>
        <navigation-case>
            <from-outcome>robo</from-outcome>
            <to-view-id>/robo.jsp</to-view-id>
            <redirect/>
        </navigation-case>
        <navigation-case>
            <from-outcome>erroLogin</from-outcome>
            <to-view-id>/login.jsp</to-view-id>
            <redirect/>
        </navigation-case>

        <navigation-case>
            <from-outcome>alterarSenhaok</from-outcome>
            <to-view-id>/alterarSenhaClienteForm.jsp</to-view-id>
            <redirect/>
        </navigation-case>
        <navigation-case>
            <from-outcome>alterarCadastro</from-outcome>
            <to-view-id>/alterarDadosCadastraisUsuario.jsp</to-view-id>
            <redirect/>
        </navigation-case>
        <navigation-case>
            <from-outcome>importacao</from-outcome>
            <to-view-id>/importacao.jsp</to-view-id>
            <redirect/>
        </navigation-case>
        <navigation-case>
            <from-outcome>listaClientes</from-outcome>
            <to-view-id>/tela3.jsp</to-view-id>
        </navigation-case>
        <navigation-case>
            <from-outcome>vendaAvulsa</from-outcome>
            <to-view-id>/vendaAvulsaForm.jsp</to-view-id>
            <redirect/>
        </navigation-case>
        <navigation-case>
            <from-outcome>aulaAvulsa</from-outcome>
            <to-view-id>/aulaAvulsaForm.jsp</to-view-id>
            <redirect/>
        </navigation-case>
        <navigation-case>
            <from-outcome>diaria</from-outcome>
            <to-view-id>/diariaForm.jsp</to-view-id>
            <redirect/>
        </navigation-case>
        <navigation-case>
            <from-outcome>calculadora</from-outcome>
            <to-view-id>/calculadoraForm.jsp</to-view-id>
            <redirect/>
        </navigation-case>
        <navigation-case>
            <from-outcome>showcaseComponentes</from-outcome>
            <to-view-id>/showcase/componentes/showcase_componentes.jsp</to-view-id>
            <redirect/>
        </navigation-case>
        <navigation-case>
            <from-outcome>tela8</from-outcome>
            <to-view-id>/tela8.jsp</to-view-id>
            <redirect/>
        </navigation-case>
        <navigation-case>
            <from-outcome>realizarOrcamento</from-outcome>
            <to-view-id>/realizarOrcamento.jsp</to-view-id>
            <redirect/>
        </navigation-case>
        <navigation-case>
            <from-outcome>freePass</from-outcome>
            <to-view-id>/freePassForm.jsp</to-view-id>
            <redirect/>
        </navigation-case>
        <navigation-case>
            <from-outcome>indexRelatorio</from-outcome>
            <to-view-id>/indexRelatorio.jsp</to-view-id>
            <redirect/>
        </navigation-case>
        <navigation-case>
            <from-outcome>detalheBI</from-outcome>
            <to-view-id>/detalheBI.jsp</to-view-id>            
        </navigation-case>
        <navigation-case>
            <from-outcome>telaBI</from-outcome>
            <to-view-id>/detalheBI.jsp</to-view-id>
            <redirect/>
        </navigation-case>
        <navigation-case>
            <from-outcome>tela3</from-outcome>
            <to-view-id>/tela3.jsp</to-view-id>
        </navigation-case>

        <navigation-case>
            <from-outcome>renegociacao</from-outcome>
            <to-view-id>/renegociacao.jsp</to-view-id>
            <redirect/>
        </navigation-case>

        <navigation-case>
            <from-outcome>financeiroPacto</from-outcome>
            <to-view-id>/financeiro.jsp</to-view-id>
            <redirect/>
        </navigation-case>

        <navigation-case>
            <from-outcome>financeiroInicio</from-outcome>
            <to-view-id>/financeiroInicio.jsp</to-view-id>
            <redirect/>
        </navigation-case>

        <navigation-case>
            <from-outcome>editarTipoConviteAulaExperimental</from-outcome>
            <to-view-id>/tipoConviteAulaExperimentalForm.jsp</to-view-id>
            <redirect/>
        </navigation-case>

        <navigation-case>
            <from-outcome>atendimentoPacto</from-outcome>
            <to-view-id>/atendimentoPacto.jsp</to-view-id>
            <redirect/>
        </navigation-case>

        <navigation-case>
            <from-outcome>minhaContaPacto</from-outcome>
            <to-view-id>/minhaContaPacto.jsp</to-view-id>
            <redirect/>
        </navigation-case>

        <navigation-case>
            <from-outcome>minhaContaPactoSolicitacoesEmAberto</from-outcome>
            <to-view-id>/minhaContaPactoSolicitacoesEmAberto.jsp</to-view-id>
            <redirect/>
        </navigation-case>

        <navigation-case>
            <from-outcome>minhaContaPactoSolicitacoesConcluidas</from-outcome>
            <to-view-id>/minhaContaPactoSolicitacoesConcluida.jsp</to-view-id>
            <redirect/>
        </navigation-case>

        <navigation-case>
            <from-outcome>solicitacoesEmAberto</from-outcome>
            <to-view-id>/solicitacoesEmAberto.jsp</to-view-id>
            <redirect/>
        </navigation-case>

        <navigation-case>
            <from-outcome>solicitacoesConcluidas</from-outcome>
            <to-view-id>/solicitacoesConcluidas.jsp</to-view-id>
            <redirect/>
        </navigation-case>

        <navigation-case>
            <from-outcome>consultarTipoConviteAulaExperimental</from-outcome>
            <to-view-id>/tipoConviteAulaExperimentalCons.jsp</to-view-id>
            <redirect/>
        </navigation-case>
        <navigation-case>
            <from-outcome>editarIndiceFinanceiro</from-outcome>
            <to-view-id>/indiceFinanceiroReajustePrecoForm.jsp</to-view-id>
            <redirect/>
        </navigation-case>
        <navigation-case>
            <from-outcome>consultarIndiceFinanceiro</from-outcome>
            <to-view-id>/indiceFinanceiroReajustePrecoCons.jsp</to-view-id>
            <redirect/>
        </navigation-case>
        <navigation-case>
            <from-outcome>editarCampanhaCupomDesconto</from-outcome>
            <to-view-id>/campanhaCupomDescontoForm.jsp</to-view-id>
            <redirect/>
        </navigation-case>
        <navigation-case>
            <from-outcome>consultarCampanhaCupomDesconto</from-outcome>
            <to-view-id>/campanhaCupomDescontoCons.jsp</to-view-id>
            <redirect/>
        </navigation-case>



        <!--parametro de Modulo Estudio-->
        <navigation-case>
            <from-outcome>modulo_visualiza_cliente?modulo=4bf2add2267962ea87f029fef8f75a2f</from-outcome>
            <to-view-id>/modulo_visualiza_cliente.jsp?modulo=4bf2add2267962ea87f029fef8f75a2f</to-view-id>
            <redirect />
        </navigation-case>
        <navigation-case>
            <from-outcome>modulo_lista_clientes?modulo=4bf2add2267962ea87f029fef8f75a2f</from-outcome>
            <to-view-id>/modulo_lista_clientes.jsp?modulo=4bf2add2267962ea87f029fef8f75a2f</to-view-id>
            <redirect />
        </navigation-case>
        <navigation-case>
            <from-outcome>modulo_incluir_cliente?modulo=4bf2add2267962ea87f029fef8f75a2f</from-outcome>
            <to-view-id>/modulo_incluir_cliente.jsp?modulo=4bf2add2267962ea87f029fef8f75a2f</to-view-id>
            <redirect />
        </navigation-case>
        <navigation-case>
            <from-outcome>modulo_venda_avulsa?modulo=4bf2add2267962ea87f029fef8f75a2f</from-outcome>
            <to-view-id>/modulo_venda_avulsa.jsp?modulo=4bf2add2267962ea87f029fef8f75a2f</to-view-id>
            <redirect />
        </navigation-case>
        <navigation-case>
            <from-outcome>relatorioClientes</from-outcome>
            <to-view-id>/relatorioClientes.jsp</to-view-id>
            <redirect />
        </navigation-case>
        <navigation-case>
            <from-outcome>gestaoTurma</from-outcome>
            <to-view-id>/gestaoTurma.jsp</to-view-id>
            <redirect/>
        </navigation-case>
        <navigation-case>
            <from-outcome>gestaoFamilia</from-outcome>
            <to-view-id>/gestaoFamilia.jsp</to-view-id>
            <redirect/>
        </navigation-case>
        <navigation-case>
            <from-outcome>vendasOnline</from-outcome>
            <to-view-id>/gestaoVendasOnline.jsp</to-view-id>
            <redirect/>
        </navigation-case>
        <navigation-case>
            <from-outcome>vendasOnlineAdquira</from-outcome>
            <to-view-id>/vendasOnlineAdquira.jsp</to-view-id>
            <redirect/>
        </navigation-case>
        <navigation-case>
            <from-outcome>gestaoPersonal</from-outcome>
            <to-view-id>/gestaoPersonal.jsp</to-view-id>
            <redirect/>
        </navigation-case>
        <navigation-case>
            <from-outcome>relatorioPersonal</from-outcome>
            <to-view-id>/relatorioPersonal.jsp</to-view-id>
            <redirect/>
        </navigation-case>
        <navigation-case>
            <from-outcome>gestaoTransacoes</from-outcome>
            <to-view-id>/gestaoTransacoes.jsp</to-view-id>
            <redirect />
        </navigation-case>
        <navigation-case>
            <from-outcome>gestaoRemessas</from-outcome>
            <to-view-id>/gestaoRemessas.jsp</to-view-id>
            <redirect />
        </navigation-case>
        <navigation-case>
            <from-outcome>gestaoBoletosOnline</from-outcome>
            <to-view-id>/gestaoBoletosOnline.jsp</to-view-id>
            <redirect />
        </navigation-case>
        <navigation-case>
            <from-outcome>gestaoComissao</from-outcome>
            <to-view-id>/gestaoComissao.jsp</to-view-id>
            <redirect />
        </navigation-case>
        <navigation-case>
            <from-outcome>gestaoArmario</from-outcome>
            <to-view-id>/gestaoArmario.jsp</to-view-id>
            <redirect />
        </navigation-case>
        <navigation-case>
            <from-outcome>listaClientesDadosBasicos</from-outcome>
            <to-view-id>/listaClientesDadosBasicos.jsp</to-view-id>
            <redirect />
        </navigation-case>
        <navigation-case>
            <from-outcome>clubeVantagens</from-outcome>
            <to-view-id>/clubeVantagens.jsp</to-view-id>
            <redirect />
        </navigation-case>

        <navigation-case>
            <from-outcome>smartbox</from-outcome>
            <to-view-id>/smartbox.jsp</to-view-id>
            <redirect/>
        </navigation-case>

        <navigation-case>
            <from-outcome>realizarContato</from-outcome>
            <to-view-id>realizarContatoForm.jsp</to-view-id>
            <redirect/>
        </navigation-case>

        <navigation-case>
            <from-outcome>newRealizarContato</from-outcome>
            <to-view-id>newRealizarContatoForm.jsp</to-view-id>
            <redirect/>
        </navigation-case>

         <navigation-case>
            <from-outcome>socialMailing</from-outcome>
            <to-view-id>/socialMailing.jsp</to-view-id>
            <redirect />
        </navigation-case>


        <navigation-case>
            <from-outcome>viewSocialMailing</from-outcome>
            <to-view-id>/viewSocialMailing.jsp</to-view-id>
            <redirect />
        </navigation-case>

        <navigation-case>
            <from-outcome>questionario</from-outcome>
            <to-view-id>/tela4.jsp</to-view-id>
            <redirect />
        </navigation-case>

        <navigation-case>
            <from-outcome>menusAntigos</from-outcome>
            <to-view-id>/itensDescontinuados.jsp</to-view-id>
            <redirect />
        </navigation-case>
        <navigation-case>
            <from-outcome>geralClientes</from-outcome>
            <to-view-id>/listasRelatorios.jsp</to-view-id>
            <redirect />
        </navigation-case>

        <navigation-case>
            <from-outcome>relatorioVisitantes</from-outcome>
            <to-view-id>/listasRelatoriosVisitantes.jsp</to-view-id>
            <redirect />
        </navigation-case>
        <navigation-case>
           <from-outcome>carteirasCRM</from-outcome>
           <to-view-id>/telaInicialCarteiras.jsp</to-view-id>
           <redirect/>
        </navigation-case>

        <navigation-case>
            <from-outcome>telaPrincipalCRM</from-outcome>
            <to-view-id>/telaPrincipalCRM.jsp</to-view-id>
            <redirect />
        </navigation-case>
        <navigation-case>
            <from-outcome>telaBICRM</from-outcome>
            <to-view-id>/telaBICRM.jsp</to-view-id>
            <redirect />
        </navigation-case>
        
        <navigation-case>
            <from-outcome>todosClientes</from-outcome>
            <to-view-id>/clientes.jsp</to-view-id>
            <redirect />
        </navigation-case>

        <navigation-case>
            <from-outcome>indicadores</from-outcome>
            <to-view-id>/indicadores.jsp</to-view-id>
            <redirect />
        </navigation-case>

        <navigation-case>
            <from-outcome>biClubeVantagens</from-outcome>
            <to-view-id>/biClubeVantagens.jsp</to-view-id>
            <redirect />
        </navigation-case>

        <navigation-case>
            <from-outcome>ativarClubeVantagens</from-outcome>
            <to-view-id>/ativarClubeVantagens.jsp</to-view-id>
            <redirect />
        </navigation-case>
    </navigation-rule>
    <!--parametro de Modulo Estudio-->
    <navigation-rule>
        <from-view-id>/modulo_incluir_cliente.jsp</from-view-id>
        <navigation-case>
            <from-outcome>cancelar?modulo=4bf2add2267962ea87f029fef8f75a2f</from-outcome>
            <to-view-id>/modulo_lista_clientes.jsp?modulo=4bf2add2267962ea87f029fef8f75a2f</to-view-id>
            <redirect/>
        </navigation-case>
        <navigation-case>
            <from-outcome>sucesso</from-outcome>
            <to-view-id>/modulo_lista_clientes.jsp?modulo=4bf2add2267962ea87f029fef8f75a2f</to-view-id>
            <redirect/>
        </navigation-case>
    </navigation-rule>

    <navigation-rule>
        <description>Navegacao da pagina</description>
        <from-view-id>/respostaPerguntaClienteForm.jsp</from-view-id>
        <navigation-case>
            <from-outcome>editar</from-outcome>
            <to-view-id>/respostaPerguntaClienteForm.jsp</to-view-id>
            <redirect />
        </navigation-case>
        <navigation-case>
            <from-outcome>consultar</from-outcome>
            <to-view-id>/respostaPerguntaClienteCons.jsp</to-view-id>
            <redirect />
        </navigation-case>
    </navigation-rule>
    <navigation-rule>
        <description>Navegacao da pagina</description>
        <from-view-id>/respostaPerguntaClienteCons.jsp</from-view-id>
        <navigation-case>
            <from-outcome>editar</from-outcome>
            <to-view-id>/respostaPerguntaClienteForm.jsp</to-view-id>
            <redirect />
        </navigation-case>
        <navigation-case>
            <from-outcome>consultar</from-outcome>
            <to-view-id>/respostaPerguntaClienteCons.jsp</to-view-id>
            <redirect />
        </navigation-case>
    </navigation-rule>
    <navigation-rule>
        <description>Navegacao da pagina</description>
        <from-view-id>/ambienteForm.jsp</from-view-id>
        <navigation-case>
            <from-outcome>editar</from-outcome>
            <to-view-id>/ambienteForm.jsp</to-view-id>
        </navigation-case>
        <navigation-case>
            <from-outcome>consultar</from-outcome>
            <to-view-id>/ambienteCons.jsp</to-view-id>
        </navigation-case>
    </navigation-rule>
    <navigation-rule>
        <description>Navegacao da pagina</description>
        <from-view-id>/ambienteCons.jsp</from-view-id>
        <navigation-case>
            <from-outcome>editar</from-outcome>
            <to-view-id>/ambienteForm.jsp</to-view-id>
        </navigation-case>
        <navigation-case>
            <from-outcome>consultar</from-outcome>
            <to-view-id>/ambienteCons.jsp</to-view-id>
        </navigation-case>
    </navigation-rule>
    <navigation-rule>
        <description>Navegacao da pagina</description>
        <from-view-id>/localAcessoCons.jsp</from-view-id>
        <navigation-case>
            <from-outcome>editar</from-outcome>
            <to-view-id>/localAcessoForm.jsp</to-view-id>
            <redirect />
        </navigation-case>
        <navigation-case>
            <from-outcome>consultar</from-outcome>
            <to-view-id>/localAcessoCons.jsp</to-view-id>
            <redirect />
        </navigation-case>
    </navigation-rule>
    <navigation-rule>
        <description>Navegacao da pagina</description>
        <from-view-id>/localAcessoForm.jsp</from-view-id>
        <navigation-case>
            <from-outcome>consultar</from-outcome>
            <to-view-id>/localAcessoCons.jsp</to-view-id>
            <redirect />
        </navigation-case>
        <navigation-case>
            <from-outcome>editar</from-outcome>
            <to-view-id>/localAcessoForm.jsp</to-view-id>
            <redirect />
        </navigation-case>
    </navigation-rule>
    <navigation-rule>
        <description>Navegacao da pagina</description>
        <from-view-id>/servidorFacialCons.jsp</from-view-id>
        <navigation-case>
            <from-outcome>editar</from-outcome>
            <to-view-id>/servidorFacialForm.jsp</to-view-id>
            <redirect />
        </navigation-case>
        <navigation-case>
            <from-outcome>consultar</from-outcome>
            <to-view-id>/servidorFacialCons.jsp</to-view-id>
            <redirect />
        </navigation-case>
    </navigation-rule>
    <navigation-rule>
        <description>Navegacao da pagina</description>
        <from-view-id>/servidorFacialForm.jsp</from-view-id>
        <navigation-case>
            <from-outcome>consultar</from-outcome>
            <to-view-id>/servidorFacialCons.jsp</to-view-id>
            <redirect />
        </navigation-case>
        <navigation-case>
            <from-outcome>editar</from-outcome>
            <to-view-id>/servidorFacialForm.jsp</to-view-id>
            <redirect />
        </navigation-case>
    </navigation-rule>
    <navigation-rule>
        <description>Navegacao da pagina</description>
        <from-view-id>/matriculaAlunoHorarioTurmaRel.jsp</from-view-id>
        <navigation-case>
            <from-outcome>editar</from-outcome>
            <to-view-id>/contratoForm.jsp</to-view-id>
            <redirect />
        </navigation-case>
    </navigation-rule>
    <navigation-rule>
        <description>Navegacao da pagina</description>
        <from-view-id>/clienteRel.jsp</from-view-id>
        <navigation-case>
            <from-outcome>editar</from-outcome>
            <to-view-id>/clienteRel.jsp</to-view-id>
            <redirect />
        </navigation-case>
        <navigation-case>
            <from-outcome>novo</from-outcome>
            <to-view-id>/clienteRel.jsp</to-view-id>
            <redirect />
        </navigation-case>
    </navigation-rule>
    <navigation-rule>
        <description>Navegacao da pagina</description>
        <from-view-id>/contratoForm.jsp</from-view-id>
        <navigation-case>
            <from-outcome>editar</from-outcome>
            <to-view-id>/contratoForm.jsp</to-view-id>
            <redirect />
        </navigation-case>
        <navigation-case>
            <from-outcome>consultar</from-outcome>
            <to-view-id>/contratoCons.jsp</to-view-id>
            <redirect />
        </navigation-case>
    </navigation-rule>
    <navigation-rule>
        <description>Navegacao da pagina</description>
        <from-view-id>/contratoCons.jsp</from-view-id>
        <navigation-case>
            <from-outcome>editar</from-outcome>
            <to-view-id>/contratoForm.jsp</to-view-id>
            <redirect />
        </navigation-case>
        <navigation-case>
            <from-outcome>consultar</from-outcome>
            <to-view-id>/contratoCons.jsp</to-view-id>
            <redirect />
        </navigation-case>
    </navigation-rule>
    <navigation-rule>
        <description>Navegacao da pagina</description>
        <from-view-id>/grupoForm.jsp</from-view-id>
        <navigation-case>
            <from-outcome>editar</from-outcome>
            <to-view-id>/grupoForm.jsp</to-view-id>
            <redirect />
        </navigation-case>
        <navigation-case>
            <from-outcome>consultar</from-outcome>
            <to-view-id>/grupoCons.jsp</to-view-id>
            <redirect />
        </navigation-case>
    </navigation-rule>
    <navigation-rule>
        <description>Navegacao da pagina</description>
        <from-view-id>/grupoCons.jsp</from-view-id>
        <navigation-case>
            <from-outcome>editar</from-outcome>
            <to-view-id>/grupoForm.jsp</to-view-id>
            <redirect />
        </navigation-case>
        <navigation-case>
            <from-outcome>consultar</from-outcome>
            <to-view-id>/grupoCons.jsp</to-view-id>
            <redirect />
        </navigation-case>
    </navigation-rule>
    <navigation-rule>
        <description>Navegacao da pagina</description>
        <from-view-id>/paisForm.jsp</from-view-id>
        <navigation-case>
            <from-outcome>editar</from-outcome>
            <to-view-id>/paisForm.jsp</to-view-id>
        </navigation-case>
        <navigation-case>
            <from-outcome>consultar</from-outcome>
            <to-view-id>/paisCons.jsp</to-view-id>
        </navigation-case>
    </navigation-rule>
    <navigation-rule>
        <description>Navegacao da pagina</description>
        <from-view-id>/adquirenteForm.jsp</from-view-id>
        <navigation-case>
            <from-outcome>editar</from-outcome>
            <to-view-id>/adquirenteForm.jsp</to-view-id>
        </navigation-case>
        <navigation-case>
            <from-outcome>consultar</from-outcome>
            <to-view-id>/adquirenteCons.jsp</to-view-id>
        </navigation-case>
    </navigation-rule>
    <navigation-rule>
        <description>Navegacao da pagina</description>
        <from-view-id>/paisCons.jsp</from-view-id>
        <navigation-case>
            <from-outcome>editar</from-outcome>
            <to-view-id>/paisForm.jsp</to-view-id>
        </navigation-case>
        <navigation-case>
            <from-outcome>consultar</from-outcome>
            <to-view-id>/paisCons.jsp</to-view-id>
        </navigation-case>
    </navigation-rule>
    <navigation-rule>
        <description>Navegacao da pagina</description>
        <from-view-id>/adquirenteCons.jsp</from-view-id>
        <navigation-case>
            <from-outcome>editar</from-outcome>
            <to-view-id>/adquirenteForm.jsp</to-view-id>
        </navigation-case>
        <navigation-case>
            <from-outcome>consultar</from-outcome>
            <to-view-id>/adquirenteCons.jsp</to-view-id>
        </navigation-case>
    </navigation-rule>
    <navigation-rule>
        <description>Navegacao da pagina</description>
        <from-view-id>/relatorio/relatorioArmarioRel.jsp</from-view-id>
        <navigation-case>
            <from-outcome>resultado</from-outcome>
            <to-view-id>/relatorio/relatorioArmarioResumo.jsp</to-view-id>
        </navigation-case>
    </navigation-rule>
    <navigation-rule>
        <description>Navegacao da pagina</description>
        <from-view-id>/relatorio/relatorioArmarioResumo.jsp</from-view-id>
        <navigation-case>
            <from-outcome>consulta</from-outcome>
            <to-view-id>/relatorio/relatorioArmarioRel.jsp</to-view-id>
        </navigation-case>
    </navigation-rule>
    <navigation-rule>
        <description>Navegacao da pagina</description>
        <from-view-id>/produtoForm.jsp</from-view-id>
        <navigation-case>
            <from-outcome>editar</from-outcome>
            <to-view-id>/produtoForm.jsp</to-view-id>
            <redirect />
        </navigation-case>
        <navigation-case>
            <from-outcome>consultar</from-outcome>
            <to-view-id>/produtoCons.jsp</to-view-id>
            <redirect />
        </navigation-case>
    </navigation-rule>
    <navigation-rule>
        <description>Navegacao da pagina</description>
        <from-view-id>/uploadNfe.jsp</from-view-id>
        <navigation-case>
            <from-outcome>produtoCons</from-outcome>
            <to-view-id>/produtoCons.jsp</to-view-id>
            <redirect />
        </navigation-case>
    </navigation-rule>
    <navigation-rule>
        <description>Navegacao da pagina</description>
        <from-view-id>/produtoCons.jsp</from-view-id>
        <navigation-case>
            <from-outcome>editar</from-outcome>
            <to-view-id>/produtoForm.jsp</to-view-id>
            <redirect />
        </navigation-case>
        <navigation-case>
            <from-outcome>consultar</from-outcome>
            <to-view-id>/produtoCons.jsp</to-view-id>
            <redirect />
        </navigation-case>
        <navigation-case>
            <from-outcome>uploadNfe</from-outcome>
            <to-view-id>/uploadNfe.jsp</to-view-id>
            <redirect />
        </navigation-case>
    </navigation-rule>
    <navigation-rule>
        <description>Navegacao da pagina</description>
        <from-view-id>/questionarioClienteForm.jsp</from-view-id>
        <navigation-case>
            <from-outcome>editar</from-outcome>
            <to-view-id>/questionarioClienteForm.jsp</to-view-id>
            <redirect />
        </navigation-case>
        <navigation-case>
            <from-outcome>consultar</from-outcome>
            <to-view-id>/questionarioClienteCons.jsp</to-view-id>
            <redirect />
        </navigation-case>
    </navigation-rule>
    <navigation-rule>
        <description>Navegacao da pagina</description>
        <from-view-id>/questionarioClienteCRMForm.jsp</from-view-id>
        <navigation-case>
            <from-outcome>editar</from-outcome>
            <to-view-id>/questionarioClienteCRMForm.jsp</to-view-id>
            <redirect />
        </navigation-case>
        <navigation-case>
            <from-outcome>consultar</from-outcome>
            <to-view-id>/questionarioClienteCons.jsp</to-view-id>
            <redirect />
        </navigation-case>
    </navigation-rule>
    <navigation-rule>
        <description>Navegacao da pagina</description>
        <from-view-id>/questionarioClienteCons.jsp</from-view-id>
        <navigation-case>
            <from-outcome>editar</from-outcome>
            <to-view-id>/questionarioClienteForm.jsp</to-view-id>
            <redirect />
        </navigation-case>
        <navigation-case>
            <from-outcome>consultar</from-outcome>
            <to-view-id>/questionarioClienteCons.jsp</to-view-id>
            <redirect />
        </navigation-case>
    </navigation-rule>
    <navigation-rule>
        <description>Navegacao da pagina</description>
        <from-view-id>/planoForm.jsp</from-view-id>
        <navigation-case>
            <from-outcome>editar</from-outcome>
            <to-view-id>/planoForm.jsp</to-view-id>
            <redirect />
        </navigation-case>
        <navigation-case>
            <from-outcome>consultar</from-outcome>
            <to-view-id>/planoCons.jsp</to-view-id>
            <redirect />
        </navigation-case>
    </navigation-rule>
    <navigation-rule>
        <description>Navegacao da pagina</description>
        <from-view-id>/planoCons.jsp</from-view-id>
        <navigation-case>
            <from-outcome>editar</from-outcome>
            <to-view-id>/planoForm.jsp</to-view-id>
            <redirect />
        </navigation-case>
        <navigation-case>
            <from-outcome>consultar</from-outcome>
            <to-view-id>/planoCons.jsp</to-view-id>
            <redirect />
        </navigation-case>
    </navigation-rule>
    <navigation-rule>
        <description>Navegacao da pagina</description>
        <from-view-id>/planoModalidadeVezesSemanaForm.jsp</from-view-id>
        <navigation-case>
            <from-outcome>editar</from-outcome>
            <to-view-id>/planoModalidadeVezesSemanaForm.jsp</to-view-id>
            <redirect />
        </navigation-case>
        <navigation-case>
            <from-outcome>consultar</from-outcome>
            <to-view-id>/planoModalidadeVezesSemanaCons.jsp</to-view-id>
            <redirect />
        </navigation-case>
    </navigation-rule>
    <navigation-rule>
        <description>Navegacao da pagina</description>
        <from-view-id>/planoModalidadeVezesSemanaCons.jsp</from-view-id>
        <navigation-case>
            <from-outcome>editar</from-outcome>
            <to-view-id>/planoModalidadeVezesSemanaForm.jsp</to-view-id>
            <redirect />
        </navigation-case>
        <navigation-case>
            <from-outcome>consultar</from-outcome>
            <to-view-id>/planoModalidadeVezesSemanaCons.jsp</to-view-id>
            <redirect />
        </navigation-case>
    </navigation-rule>
    <navigation-rule>
        <description>Navegacao da pagina</description>
        <from-view-id>/categoriaProdutoForm.jsp</from-view-id>
        <navigation-case>
            <from-outcome>editar</from-outcome>
            <to-view-id>/categoriaProdutoForm.jsp</to-view-id>
            <redirect />
        </navigation-case>
        <navigation-case>
            <from-outcome>consultar</from-outcome>
            <to-view-id>/categoriaProdutoCons.jsp</to-view-id>
            <redirect />
        </navigation-case>
    </navigation-rule>
    <navigation-rule>
        <description>Navegacao da pagina</description>
        <from-view-id>/categoriaProdutoCons.jsp</from-view-id>
        <navigation-case>
            <from-outcome>editar</from-outcome>
            <to-view-id>/categoriaProdutoForm.jsp</to-view-id>
            <redirect />
        </navigation-case>
        <navigation-case>
            <from-outcome>consultar</from-outcome>
            <to-view-id>/categoriaProdutoCons.jsp</to-view-id>
            <redirect />
        </navigation-case>
    </navigation-rule>
    <navigation-rule>
        <description>Navegacao da pagina</description>
        <from-view-id>/movimentoContaCorrenteClienteForm.jsp</from-view-id>
        <navigation-case>
            <from-outcome>editar</from-outcome>
            <to-view-id>/movimentoContaCorrenteClienteForm.jsp</to-view-id>
        </navigation-case>
        <navigation-case>
            <from-outcome>consultar</from-outcome>
            <to-view-id>/movimentoContaCorrenteClienteCons.jsp</to-view-id>
        </navigation-case>
    </navigation-rule>
    <navigation-rule>
        <description>Navegacao da pagina</description>
        <from-view-id>/movimentoContaCorrenteClienteCons.jsp</from-view-id>
        <navigation-case>
            <from-outcome>editar</from-outcome>
            <to-view-id>/movimentoContaCorrenteClienteForm.jsp</to-view-id>
        </navigation-case>
        <navigation-case>
            <from-outcome>consultar</from-outcome>
            <to-view-id>/movimentoContaCorrenteClienteCons.jsp</to-view-id>
        </navigation-case>
    </navigation-rule>
    <navigation-rule>
        <description>Navegacao da pagina</description>
        <from-view-id>/movParcelaForm.jsp</from-view-id>
        <navigation-case>
            <from-outcome>editar</from-outcome>
            <to-view-id>/movParcelaForm.jsp</to-view-id>
            <redirect />
        </navigation-case>
        <navigation-case>
            <from-outcome>consultar</from-outcome>
            <to-view-id>/movParcelaCons.jsp</to-view-id>
            <redirect />
        </navigation-case>
    </navigation-rule>
    <navigation-rule>
        <description>Navegacao da pagina</description>
        <from-view-id>/movParcelaCons.jsp</from-view-id>
        <navigation-case>
            <from-outcome>editar</from-outcome>
            <to-view-id>/movParcelaForm.jsp</to-view-id>
            <redirect />
        </navigation-case>
        <navigation-case>
            <from-outcome>consultar</from-outcome>
            <to-view-id>/movParcelaCons.jsp</to-view-id>
            <redirect />
        </navigation-case>
    </navigation-rule>
    <navigation-rule>
        <description>Navegacao da pagina</description>
        <from-view-id>/profissaoForm.jsp</from-view-id>
        <navigation-case>
            <from-outcome>editar</from-outcome>
            <to-view-id>/profissaoForm.jsp</to-view-id>
        </navigation-case>
        <navigation-case>
            <from-outcome>consultar</from-outcome>
            <to-view-id>/profissaoCons.jsp</to-view-id>
        </navigation-case>
    </navigation-rule>
    <navigation-rule>
        <description>Navegacao da pagina</description>
        <from-view-id>/profissaoCons.jsp</from-view-id>
        <navigation-case>
            <from-outcome>editar</from-outcome>
            <to-view-id>/profissaoForm.jsp</to-view-id>
        </navigation-case>
        <navigation-case>
            <from-outcome>consultar</from-outcome>
            <to-view-id>/profissaoCons.jsp</to-view-id>
        </navigation-case>
    </navigation-rule>
    <navigation-rule>
        <description>Navegacao da pagina</description>
        <from-view-id>/grauInstrucaoForm.jsp</from-view-id>
        <navigation-case>
            <from-outcome>editar</from-outcome>
            <to-view-id>/grauInstrucaoForm.jsp</to-view-id>
        </navigation-case>
        <navigation-case>
            <from-outcome>consultar</from-outcome>
            <to-view-id>/grauInstrucaoCons.jsp</to-view-id>
        </navigation-case>
    </navigation-rule>
    <navigation-rule>
        <description>Navegacao da pagina</description>
        <from-view-id>/grauInstrucaoCons.jsp</from-view-id>
        <navigation-case>
            <from-outcome>editar</from-outcome>
            <to-view-id>/grauInstrucaoForm.jsp</to-view-id>
        </navigation-case>
        <navigation-case>
            <from-outcome>consultar</from-outcome>
            <to-view-id>/grauInstrucaoCons.jsp</to-view-id>
        </navigation-case>
    </navigation-rule>
    <navigation-rule>
        <description>Navegacao da pagina</description>
        <from-view-id>/convenioForm.jsp</from-view-id>
        <navigation-case>
            <from-outcome>editar</from-outcome>
            <to-view-id>/convenioForm.jsp</to-view-id>
            <redirect />
        </navigation-case>
        <navigation-case>
            <from-outcome>consultar</from-outcome>
            <to-view-id>/convenioCons.jsp</to-view-id>
            <redirect />
        </navigation-case>
    </navigation-rule>
    <navigation-rule>
        <description>Navegacao da pagina</description>
        <from-view-id>/convenioCons.jsp</from-view-id>
        <navigation-case>
            <from-outcome>editar</from-outcome>
            <to-view-id>/convenioForm.jsp</to-view-id>
            <redirect />
        </navigation-case>
        <navigation-case>
            <from-outcome>consultar</from-outcome>
            <to-view-id>/convenioCons.jsp</to-view-id>
            <redirect />
        </navigation-case>
    </navigation-rule>
    <navigation-rule>
        <description>Navegacao da pagina</description>
        <from-view-id>/pessoaForm.jsp</from-view-id>
        <navigation-case>
            <from-outcome>editar</from-outcome>
            <to-view-id>/pessoaForm.jsp</to-view-id>
            <redirect />
        </navigation-case>
        <navigation-case>
            <from-outcome>consultar</from-outcome>
            <to-view-id>/pessoaCons.jsp</to-view-id>
            <redirect />
        </navigation-case>
    </navigation-rule>
    <navigation-rule>
        <description>Navegacao da pagina</description>
        <from-view-id>/pessoaCons.jsp</from-view-id>
        <navigation-case>
            <from-outcome>editar</from-outcome>
            <to-view-id>/pessoaForm.jsp</to-view-id>
            <redirect />
        </navigation-case>
        <navigation-case>
            <from-outcome>consultar</from-outcome>
            <to-view-id>/pessoaCons.jsp</to-view-id>
            <redirect />
        </navigation-case>
    </navigation-rule>
    <navigation-rule>
        <description>Navegacao da pagina</description>
        <from-view-id>/movPagamentoForm.jsp</from-view-id>
        <navigation-case>
            <from-outcome>editar</from-outcome>
            <to-view-id>/movPagamentoForm.jsp</to-view-id>
            <redirect />
        </navigation-case>
        <navigation-case>
            <from-outcome>consultar</from-outcome>
            <to-view-id>/movPagamentoCons.jsp</to-view-id>
            <redirect />
        </navigation-case>
    </navigation-rule>
    <navigation-rule>
        <description>Navegacao da pagina</description>
        <from-view-id>/movPagamentoCons.jsp</from-view-id>
        <navigation-case>
            <from-outcome>editar</from-outcome>
            <to-view-id>/movPagamentoForm.jsp</to-view-id>
            <redirect />
        </navigation-case>
        <navigation-case>
            <from-outcome>movPagamentoCons.jsp</from-outcome>
            <to-view-id>/movPagamentoCons.jsp</to-view-id>
        </navigation-case>
        <navigation-case>
            <from-outcome>consultar</from-outcome>
            <to-view-id>/movPagamentoCons.jsp</to-view-id>
            <redirect />
        </navigation-case>
    </navigation-rule>
    <navigation-rule>
        <description>Navegacao da pagina</description>
        <from-view-id>/empresaForm.jsp</from-view-id>
        <navigation-case>
            <from-outcome>editar</from-outcome>
            <to-view-id>/empresaForm.jsp</to-view-id>
        </navigation-case>
        <navigation-case>
            <from-outcome>consultar</from-outcome>
            <to-view-id>/empresaCons.jsp</to-view-id>
            <redirect/>
        </navigation-case>
    </navigation-rule>
    <navigation-rule>
        <description>Navegacao da pagina</description>
        <from-view-id>/empresaCons.jsp</from-view-id>
        <navigation-case>
            <from-outcome>editar</from-outcome>
            <to-view-id>/empresaForm.jsp</to-view-id>
            <redirect/>
        </navigation-case>
        <navigation-case>
            <from-outcome>consultar</from-outcome>
            <to-view-id>/empresaCons.jsp</to-view-id>
            <redirect/>
        </navigation-case>
    </navigation-rule>
    <navigation-rule>
        <description>Navegacao da pagina</description>
        <from-view-id>/produtoSugeridoForm.jsp</from-view-id>
        <navigation-case>
            <from-outcome>editar</from-outcome>
            <to-view-id>/produtoSugeridoForm.jsp</to-view-id>
            <redirect />
        </navigation-case>
        <navigation-case>
            <from-outcome>consultar</from-outcome>
            <to-view-id>/produtoSugeridoCons.jsp</to-view-id>
            <redirect />
        </navigation-case>
    </navigation-rule>
    <navigation-rule>
        <description>Navegacao da pagina</description>
        <from-view-id>/produtoSugeridoCons.jsp</from-view-id>
        <navigation-case>
            <from-outcome>editar</from-outcome>
            <to-view-id>/produtoSugeridoForm.jsp</to-view-id>
            <redirect />
        </navigation-case>
        <navigation-case>
            <from-outcome>consultar</from-outcome>
            <to-view-id>/produtoSugeridoCons.jsp</to-view-id>
            <redirect />
        </navigation-case>
    </navigation-rule>
    <navigation-rule>
        <description>Navegacao da pagina</description>
        <from-view-id>/questionarioForm.jsp</from-view-id>
        <navigation-case>
            <from-outcome>editar</from-outcome>
            <to-view-id>/questionarioForm.jsp</to-view-id>
            <redirect />
        </navigation-case>
        <navigation-case>
            <from-outcome>consultar</from-outcome>
            <to-view-id>/questionarioCons.jsp</to-view-id>
            <redirect />
        </navigation-case>
    </navigation-rule>
    <navigation-rule>
        <description>Navegacao da pagina</description>
        <from-view-id>/questionarioCons.jsp</from-view-id>
        <navigation-case>
            <from-outcome>editar</from-outcome>
            <to-view-id>/questionarioForm.jsp</to-view-id>
            <redirect />
        </navigation-case>
        <navigation-case>
            <from-outcome>consultar</from-outcome>
            <to-view-id>/questionarioCons.jsp</to-view-id>
            <redirect />
        </navigation-case>
    </navigation-rule>
    <navigation-rule>
        <description>Navegacao da pagina</description>
        <from-view-id>/departamentoForm.jsp</from-view-id>
        <navigation-case>
            <from-outcome>editar</from-outcome>
            <to-view-id>/departamentoForm.jsp</to-view-id>
            <redirect/>
        </navigation-case>
        <navigation-case>
            <from-outcome>consultar</from-outcome>
            <to-view-id>/departamentoCons.jsp</to-view-id>
            <redirect/>
        </navigation-case>
    </navigation-rule>
    <navigation-rule>
        <description>Navegacao da pagina</description>
        <from-view-id>/departamentoCons.jsp</from-view-id>
        <navigation-case>
            <from-outcome>editar</from-outcome>
            <to-view-id>/departamentoForm.jsp</to-view-id>
            <redirect/>
        </navigation-case>
        <navigation-case>
            <from-outcome>consultar</from-outcome>
            <to-view-id>/departamentoCons.jsp</to-view-id>
            <redirect/>
        </navigation-case>
    </navigation-rule>
    <navigation-rule>
        <description>Navegacao da pagina</description>
        <from-view-id>/nivelTurmaForm.jsp</from-view-id>
        <navigation-case>
            <from-outcome>editar</from-outcome>
            <to-view-id>/nivelTurmaForm.jsp</to-view-id>
            <redirect />
        </navigation-case>
        <navigation-case>
            <from-outcome>consultar</from-outcome>
            <to-view-id>/nivelTurmaCons.jsp</to-view-id>
            <redirect />
        </navigation-case>
    </navigation-rule>
    <navigation-rule>
        <description>Navegacao da pagina</description>
        <from-view-id>/nivelTurmaCons.jsp</from-view-id>
        <navigation-case>
            <from-outcome>editar</from-outcome>
            <to-view-id>/nivelTurmaForm.jsp</to-view-id>
            <redirect />
        </navigation-case>
        <navigation-case>
            <from-outcome>consultar</from-outcome>
            <to-view-id>/nivelTurmaCons.jsp</to-view-id>
            <redirect />
        </navigation-case>
    </navigation-rule>
    <navigation-rule>
        <description>Navegacao da pagina</description>
        <from-view-id>/categoriaForm.jsp</from-view-id>
        <navigation-case>
            <from-outcome>editar</from-outcome>
            <to-view-id>/categoriaForm.jsp</to-view-id>
        </navigation-case>
        <navigation-case>
            <from-outcome>consultar</from-outcome>
            <to-view-id>/categoriaCons.jsp</to-view-id>
        </navigation-case>
    </navigation-rule>
    <navigation-rule>
        <description>Navegacao da pagina</description>
        <from-view-id>/categoriaCons.jsp</from-view-id>
        <navigation-case>
            <from-outcome>editar</from-outcome>
            <to-view-id>/categoriaForm.jsp</to-view-id>
        </navigation-case>
        <navigation-case>
            <from-outcome>consultar</from-outcome>
            <to-view-id>/categoriaCons.jsp</to-view-id>
        </navigation-case>
    </navigation-rule>
    <navigation-rule>
        <description>Navegacao da pagina</description>
        <from-view-id>/horarioForm.jsp</from-view-id>
        <navigation-case>
            <from-outcome>editar</from-outcome>
            <to-view-id>/horarioForm.jsp</to-view-id>
            <redirect />
        </navigation-case>
        <navigation-case>
            <from-outcome>consultar</from-outcome>
            <to-view-id>/horarioCons.jsp</to-view-id>
            <redirect />
        </navigation-case>
    </navigation-rule>
    <navigation-rule>
        <description>Navegacao da pagina</description>
        <from-view-id>/horarioCons.jsp</from-view-id>
        <navigation-case>
            <from-outcome>editar</from-outcome>
            <to-view-id>/horarioForm.jsp</to-view-id>
            <redirect />
        </navigation-case>
        <navigation-case>
            <from-outcome>consultar</from-outcome>
            <to-view-id>/horarioCons.jsp</to-view-id>
            <redirect />
        </navigation-case>
    </navigation-rule>
    <navigation-rule>
        <description>Navegacao da pagina</description>
        <from-view-id>/classificacaoForm.jsp</from-view-id>
        <navigation-case>
            <from-outcome>editar</from-outcome>
            <to-view-id>/classificacaoForm.jsp</to-view-id>
            <redirect />
        </navigation-case>
        <navigation-case>
            <from-outcome>consultar</from-outcome>
            <to-view-id>/classificacaoCons.jsp</to-view-id>
            <redirect />
        </navigation-case>
    </navigation-rule>
    <navigation-rule>
        <description>Navegacao da pagina</description>
        <from-view-id>/classificacaoCons.jsp</from-view-id>
        <navigation-case>
            <from-outcome>editar</from-outcome>
            <to-view-id>/classificacaoForm.jsp</to-view-id>
            <redirect />
        </navigation-case>
        <navigation-case>
            <from-outcome>consultar</from-outcome>
            <to-view-id>/classificacaoCons.jsp</to-view-id>
            <redirect />
        </navigation-case>
    </navigation-rule>
    <navigation-rule>
        <description>Navegacao da pagina</description>
        <from-view-id>/vezesSemanaForm.jsp</from-view-id>
        <navigation-case>
            <from-outcome>editar</from-outcome>
            <to-view-id>/vezesSemanaForm.jsp</to-view-id>
            <redirect />
        </navigation-case>
        <navigation-case>
            <from-outcome>consultar</from-outcome>
            <to-view-id>/vezesSemanaCons.jsp</to-view-id>
            <redirect />
        </navigation-case>
    </navigation-rule>
    <navigation-rule>
        <description>Navegacao da pagina</description>
        <from-view-id>/vezesSemanaCons.jsp</from-view-id>
        <navigation-case>
            <from-outcome>editar</from-outcome>
            <to-view-id>/vezesSemanaForm.jsp</to-view-id>
            <redirect />
        </navigation-case>
        <navigation-case>
            <from-outcome>consultar</from-outcome>
            <to-view-id>/vezesSemanaCons.jsp</to-view-id>
            <redirect />
        </navigation-case>
    </navigation-rule>
    <navigation-rule>
        <description>Navegacao da pagina</description>
        <from-view-id>/modalidadeForm.jsp</from-view-id>
        <navigation-case>
            <from-outcome>editar</from-outcome>
            <to-view-id>/modalidadeForm.jsp</to-view-id>
            <redirect />
        </navigation-case>
        <navigation-case>
            <from-outcome>consultar</from-outcome>
            <to-view-id>/modalidadeCons.jsp</to-view-id>
            <redirect />
        </navigation-case>
    </navigation-rule>
    <navigation-rule>
        <description>Navegacao da pagina</description>
        <from-view-id>/tipoModalidadeForm.jsp</from-view-id>
        <navigation-case>
            <from-outcome>editar</from-outcome>
            <to-view-id>/tipoModalidadeForm.jsp</to-view-id>
            <redirect />
        </navigation-case>
        <navigation-case>
            <from-outcome>consultar</from-outcome>
            <to-view-id>/tipoModalidadeCons.jsp</to-view-id>
            <redirect />
        </navigation-case>
    </navigation-rule>
    <navigation-rule>
        <description>Navegacao da pagina</description>
        <from-view-id>/modalidadeCons.jsp</from-view-id>
        <navigation-case>
            <from-outcome>editar</from-outcome>
            <to-view-id>/modalidadeForm.jsp</to-view-id>
            <redirect />
        </navigation-case>
        <navigation-case>
            <from-outcome>consultar</from-outcome>
            <to-view-id>/modalidadeCons.jsp</to-view-id>
            <redirect />
        </navigation-case>
    </navigation-rule>
    <navigation-rule>
        <description>Navegacao da pagina</description>
        <from-view-id>/tipoModalidadeCons.jsp</from-view-id>
        <navigation-case>
            <from-outcome>editar</from-outcome>
            <to-view-id>/tipoModalidadeForm.jsp</to-view-id>
            <redirect />
        </navigation-case>
        <navigation-case>
            <from-outcome>consultar</from-outcome>
            <to-view-id>/tipoModalidadeCons.jsp</to-view-id>
            <redirect />
        </navigation-case>
    </navigation-rule>
    <navigation-rule>
        <description>Navegacao da pagina</description>
        <from-view-id>/compraForm.jsp</from-view-id>
        <navigation-case>
            <from-outcome>editar</from-outcome>
            <to-view-id>/compraForm.jsp</to-view-id>
            <redirect />
        </navigation-case>
        <navigation-case>
            <from-outcome>consultar</from-outcome>
            <to-view-id>/compraCons.jsp</to-view-id>
            <redirect />
        </navigation-case>
    </navigation-rule>
    <navigation-rule>
        <description>Navegacao da pagina</description>
        <from-view-id>/compraCons.jsp</from-view-id>
        <navigation-case>
            <from-outcome>editar</from-outcome>
            <to-view-id>/compraForm.jsp</to-view-id>
            <redirect />
        </navigation-case>
        <navigation-case>
            <from-outcome>consultar</from-outcome>
            <to-view-id>/compraCons.jsp</to-view-id>
            <redirect />
        </navigation-case>
    </navigation-rule>

    <navigation-rule>
        <description>Navegacao da pagina</description>
        <from-view-id>/cardexCons.jsp</from-view-id>
        <navigation-case>
            <from-outcome>consultar</from-outcome>
            <to-view-id>/cardexCons.jsp</to-view-id>
            <redirect />
        </navigation-case>
    </navigation-rule>


    <navigation-rule>
        <description>Navegacao da pagina</description>
        <from-view-id>/produtoEstoqueForm.jsp</from-view-id>
        <navigation-case>
            <from-outcome>editar</from-outcome>
            <to-view-id>/produtoEstoqueForm.jsp</to-view-id>
            <redirect />
        </navigation-case>
        <navigation-case>
            <from-outcome>consultar</from-outcome>
            <to-view-id>/produtoEstoqueCons.jsp</to-view-id>
            <redirect />
        </navigation-case>
    </navigation-rule>

    <navigation-rule>
        <description>Navegacao da pagina</description>
        <from-view-id>/produtoEstoqueCons.jsp</from-view-id>
        <navigation-case>
            <from-outcome>editar</from-outcome>
            <to-view-id>/produtoEstoqueForm.jsp</to-view-id>
            <redirect />
        </navigation-case>
        <navigation-case>
            <from-outcome>consultar</from-outcome>
            <to-view-id>/produtoEstoqueCons.jsp</to-view-id>
            <redirect />
        </navigation-case>
    </navigation-rule>


    <navigation-rule>
        <description>Navegacao da pagina</description>
        <from-view-id>/balancoForm.jsp</from-view-id>
        <navigation-case>
            <from-outcome>editar</from-outcome>
            <to-view-id>/balancoForm.jsp</to-view-id>
            <redirect />
        </navigation-case>
        <navigation-case>
            <from-outcome>consultar</from-outcome>
            <to-view-id>/balancoCons.jsp</to-view-id>
            <redirect />
        </navigation-case>
    </navigation-rule>
    <navigation-rule>
        <description>Navegacao da pagina</description>
        <from-view-id>/balancoCons.jsp</from-view-id>
        <navigation-case>
            <from-outcome>editar</from-outcome>
            <to-view-id>/balancoForm.jsp</to-view-id>
            <redirect />
        </navigation-case>
        <navigation-case>
            <from-outcome>consultar</from-outcome>
            <to-view-id>/balancoCons.jsp</to-view-id>
            <redirect />
        </navigation-case>
    </navigation-rule>


    <navigation-rule>
        <description>Navegacao da pagina</description>
        <from-view-id>/planoTextoPadraoForm.jsp</from-view-id>
        <navigation-case>
            <from-outcome>editar</from-outcome>
            <to-view-id>/planoTextoPadraoForm.jsp</to-view-id>
            <redirect />
        </navigation-case>
        <navigation-case>
            <from-outcome>consultar</from-outcome>
            <to-view-id>/planoTextoPadraoCons.jsp</to-view-id>
            <redirect />
        </navigation-case>
    </navigation-rule>
    <navigation-rule>
        <description>Navegacao da pagina</description>
        <from-view-id>/planoTextoPadraoCons.jsp</from-view-id>
        <navigation-case>
            <from-outcome>editar</from-outcome>
            <to-view-id>/planoTextoPadraoForm.jsp</to-view-id>
            <redirect />

        </navigation-case>
        <navigation-case>
            <from-outcome>consultar</from-outcome>
            <to-view-id>/planoTextoPadraoCons.jsp</to-view-id>

            <redirect />
        </navigation-case>
    </navigation-rule>

    <navigation-rule>
        <description>Navegacao da pagina</description>
        <from-view-id>/modeloOrcamentoForm.jsp</from-view-id>
        <navigation-case>
            <from-outcome>editar</from-outcome>
            <to-view-id>/modeloOrcamentoForm.jsp</to-view-id>
            <redirect />
        </navigation-case>
        <navigation-case>
            <from-outcome>consultar</from-outcome>
            <to-view-id>/modeloOrcamentoCons.jsp</to-view-id>
            <redirect />
        </navigation-case>
    </navigation-rule>
    <navigation-rule>
        <description>Navegacao da pagina</description>
        <from-view-id>/orcamentoCons.jsp</from-view-id>
        <navigation-case>
            <from-outcome>editar</from-outcome>
            <to-view-id>/realizarOrcamento.jsp</to-view-id>
            <redirect />
        </navigation-case>
        <navigation-case>
            <from-outcome>consultar</from-outcome>
            <to-view-id>/orcamentoCons.jsp</to-view-id>
            <redirect />
        </navigation-case>
    </navigation-rule>
    <navigation-rule>
        <description>Navegacao da pagina</description>
        <from-view-id>/modeloOrcamentoCons.jsp</from-view-id>
        <navigation-case>
            <from-outcome>editar</from-outcome>
            <to-view-id>/modeloOrcamentoForm.jsp</to-view-id>
            <redirect />
        </navigation-case>
        <navigation-case>
            <from-outcome>consultar</from-outcome>
            <to-view-id>/modeloOrcamentoCons.jsp</to-view-id>

            <redirect />
        </navigation-case>
    </navigation-rule>

    <navigation-rule>
        <description>Navegacao da pagina</description>
        <from-view-id>/colaboradorForm.jsp</from-view-id>
        <navigation-case>
            <from-outcome>editar</from-outcome>
            <to-view-id>/colaboradorForm.jsp</to-view-id>
        </navigation-case>
        <navigation-case>
            <from-outcome>consultar</from-outcome>
            <to-view-id>/colaboradorCons.jsp</to-view-id>
        </navigation-case>
    </navigation-rule>
    <navigation-rule>
        <description>Navegacao da pagina</description>
        <from-view-id>/colaboradorCons.jsp</from-view-id>
        <navigation-case>
            <from-outcome>editar</from-outcome>
            <to-view-id>/colaboradorForm.jsp</to-view-id>
            <redirect />
        </navigation-case>
        <navigation-case>
            <from-outcome>consultar</from-outcome>
            <to-view-id>/colaboradorCons.jsp</to-view-id>
        </navigation-case>
    </navigation-rule>
    <navigation-rule>
        <description>Navegacao da pagina</description>
        <from-view-id>/pessoaCons.jsp</from-view-id>
        <navigation-case>
            <from-outcome>editar</from-outcome>
            <to-view-id>/pages/ce/cadastros/pessoa.jsp</to-view-id>
        </navigation-case>
        <navigation-case>
            <from-outcome>consultar</from-outcome>
            <to-view-id>/pessoaCons.jsp</to-view-id>
        </navigation-case>
    </navigation-rule>
    <navigation-rule>
        <description>Navegacao da pagina</description>
        <from-view-id>/pages/ce/cadastros/pessoa.jsp</from-view-id>
        <navigation-case>
            <from-outcome>editar</from-outcome>
            <to-view-id>/pages/ce/cadastros/pessoa.jsp</to-view-id>
        </navigation-case>
        <navigation-case>
            <from-outcome>consultar</from-outcome>
            <to-view-id>/pessoaCons.jsp</to-view-id>
            <redirect />
        </navigation-case>
    </navigation-rule>
    <navigation-rule>
        <description>Navegacao da pagina</description>
        <from-view-id>/perguntaForm.jsp</from-view-id>
        <navigation-case>
            <from-outcome>editar</from-outcome>
            <to-view-id>/perguntaForm.jsp</to-view-id>
            <redirect />
        </navigation-case>
        <navigation-case>
            <from-outcome>consultar</from-outcome>
            <to-view-id>/perguntaCons.jsp</to-view-id>
            <redirect />
        </navigation-case>
    </navigation-rule>
    <navigation-rule>
        <description>Navegacao da pagina</description>
        <from-view-id>/perguntaCons.jsp</from-view-id>
        <navigation-case>
            <from-outcome>editar</from-outcome>
            <to-view-id>/perguntaForm.jsp</to-view-id>
            <redirect />
        </navigation-case>
        <navigation-case>
            <from-outcome>consultar</from-outcome>
            <to-view-id>/perguntaCons.jsp</to-view-id>
            <redirect />
        </navigation-case>
    </navigation-rule>
    <navigation-rule>
        <description>Navegacao da pagina</description>
        <from-view-id>/composicaoForm.jsp</from-view-id>
        <navigation-case>
            <from-outcome>editar</from-outcome>
            <to-view-id>/composicaoForm.jsp</to-view-id>
            <redirect />
        </navigation-case>
        <navigation-case>
            <from-outcome>consultar</from-outcome>
            <to-view-id>/composicaoCons.jsp</to-view-id>
            <redirect />
        </navigation-case>
    </navigation-rule>
    <navigation-rule>
        <description>Navegacao da pagina</description>
        <from-view-id>/composicaoCons.jsp</from-view-id>
        <navigation-case>
            <from-outcome>editar</from-outcome>
            <to-view-id>/composicaoForm.jsp</to-view-id>
            <redirect />
        </navigation-case>
        <navigation-case>
            <from-outcome>consultar</from-outcome>
            <to-view-id>/composicaoCons.jsp</to-view-id>
            <redirect />
        </navigation-case>
    </navigation-rule>
    <navigation-rule>
        <description>Navegacao da pagina</description>
        <from-view-id>/novoPerfilAcessoForm.jsp</from-view-id>
        <navigation-case>
            <from-outcome>editar</from-outcome>
            <to-view-id>/novoPerfilAcessoForm.jsp</to-view-id>
        </navigation-case>
        <navigation-case>
            <from-outcome>consultar</from-outcome>
            <to-view-id>/perfilAcessoCons.jsp</to-view-id>
        </navigation-case>
    </navigation-rule>
    <navigation-rule>
        <description>Navegacao da pagina</description>
        <from-view-id>/perfilAcessoCons.jsp</from-view-id>
        <navigation-case>
            <from-outcome>editar</from-outcome>
            <to-view-id>/novoPerfilAcessoForm.jsp</to-view-id>
        </navigation-case>
        <navigation-case>
            <from-outcome>consultar</from-outcome>
            <to-view-id>/perfilAcessoCons.jsp</to-view-id>
        </navigation-case>
    </navigation-rule>
    <navigation-rule>
        <description>Navegacao da pagina</description>
        <from-view-id>/usuarioForm.jsp</from-view-id>
        <navigation-case>
            <from-outcome>editar</from-outcome>
            <to-view-id>/usuarioForm.jsp</to-view-id>
        </navigation-case>
        <navigation-case>
            <from-outcome>consultar</from-outcome>
            <to-view-id>/usuarioCons.jsp</to-view-id>
        </navigation-case>
    </navigation-rule>
    <navigation-rule>
        <description>Navegacao da pagina</description>
        <from-view-id>/usuarioCons.jsp</from-view-id>
        <navigation-case>
            <from-outcome>editar</from-outcome>
            <to-view-id>/usuarioForm.jsp</to-view-id>
        </navigation-case>
        <navigation-case>
            <from-outcome>consultar</from-outcome>
            <to-view-id>/usuarioCons.jsp</to-view-id>
        </navigation-case>
    </navigation-rule>
    <navigation-rule>
        <description>Navegacao da pagina</description>
        <from-view-id>/parentescoForm.jsp</from-view-id>
        <navigation-case>
            <from-outcome>editar</from-outcome>
            <to-view-id>/parentescoForm.jsp</to-view-id>
            <redirect />
        </navigation-case>
        <navigation-case>
            <from-outcome>consultar</from-outcome>
            <to-view-id>/parentescoCons.jsp</to-view-id>
            <redirect />
        </navigation-case>
    </navigation-rule>
    <navigation-rule>
        <description>Navegacao da pagina</description>
        <from-view-id>/parentescoCons.jsp</from-view-id>
        <navigation-case>
            <from-outcome>editar</from-outcome>
            <to-view-id>/parentescoForm.jsp</to-view-id>
            <redirect />
        </navigation-case>
        <navigation-case>
            <from-outcome>consultar</from-outcome>
            <to-view-id>/parentescoCons.jsp</to-view-id>
            <redirect />
        </navigation-case>
    </navigation-rule>
    <navigation-rule>
        <description>Navegacao da pagina</description>
        <from-view-id>/duracaoForm.jsp</from-view-id>
        <navigation-case>
            <from-outcome>editar</from-outcome>
            <to-view-id>/duracaoForm.jsp</to-view-id>
            <redirect />
        </navigation-case>
        <navigation-case>
            <from-outcome>consultar</from-outcome>
            <to-view-id>/duracaoCons.jsp</to-view-id>
            <redirect />
        </navigation-case>
    </navigation-rule>
    <navigation-rule>
        <description>Navegacao da pagina</description>
        <from-view-id>/duracaoCons.jsp</from-view-id>
        <navigation-case>
            <from-outcome>editar</from-outcome>
            <to-view-id>/duracaoForm.jsp</to-view-id>
            <redirect />
        </navigation-case>
        <navigation-case>
            <from-outcome>consultar</from-outcome>
            <to-view-id>/duracaoCons.jsp</to-view-id>
            <redirect />
        </navigation-case>
    </navigation-rule>
    <navigation-rule>
        <description>Navegacao da pagina</description>
        <from-view-id>/formaPagamentoForm.jsp</from-view-id>
        <navigation-case>
            <from-outcome>editar</from-outcome>
            <to-view-id>/formaPagamentoForm.jsp</to-view-id>
        </navigation-case>
        <navigation-case>
            <from-outcome>consultar</from-outcome>
            <to-view-id>/formaPagamentoCons.jsp</to-view-id>
        </navigation-case>
    </navigation-rule>
    <navigation-rule>
        <description>Navegacao da pagina</description>
        <from-view-id>/formaPagamentoCons.jsp</from-view-id>
        <navigation-case>
            <from-outcome>editar</from-outcome>
            <to-view-id>/formaPagamentoForm.jsp</to-view-id>
        </navigation-case>
        <navigation-case>
            <from-outcome>consultar</from-outcome>
            <to-view-id>/formaPagamentoCons.jsp</to-view-id>
        </navigation-case>
    </navigation-rule>
    <navigation-rule>
        <description>Navegacao da pagina</description>
        <from-view-id>/condicaoPagamentoForm.jsp</from-view-id>
        <navigation-case>
            <from-outcome>editar</from-outcome>
            <to-view-id>/condicaoPagamentoForm.jsp</to-view-id>
            <redirect />
        </navigation-case>
        <navigation-case>
            <from-outcome>consultar</from-outcome>
            <to-view-id>/condicaoPagamentoCons.jsp</to-view-id>
            <redirect />
        </navigation-case>
    </navigation-rule>
    <navigation-rule>
        <description>Navegacao da pagina</description>
        <from-view-id>/condicaoPagamentoCons.jsp</from-view-id>
        <navigation-case>
            <from-outcome>editar</from-outcome>
            <to-view-id>/condicaoPagamentoForm.jsp</to-view-id>
            <redirect />
        </navigation-case>
        <navigation-case>
            <from-outcome>consultar</from-outcome>
            <to-view-id>/condicaoPagamentoCons.jsp</to-view-id>
            <redirect />
        </navigation-case>
    </navigation-rule>
    <navigation-rule>
        <description>Navegacao da pagina</description>
        <from-view-id>/contratoModalidadeTurmaForm.jsp</from-view-id>
        <navigation-case>
            <from-outcome>editar</from-outcome>
            <to-view-id>/contratoModalidadeTurmaForm.jsp</to-view-id>
            <redirect />
        </navigation-case>
        <navigation-case>
            <from-outcome>consultar</from-outcome>
            <to-view-id>/contratoModalidadeTurmaCons.jsp</to-view-id>
            <redirect />
        </navigation-case>
    </navigation-rule>
    <navigation-rule>
        <description>Navegacao da pagina</description>
        <from-view-id>/contratoModalidadeTurmaCons.jsp</from-view-id>
        <navigation-case>
            <from-outcome>editar</from-outcome>
            <to-view-id>/contratoModalidadeTurmaForm.jsp</to-view-id>
            <redirect />
        </navigation-case>
        <navigation-case>
            <from-outcome>consultar</from-outcome>
            <to-view-id>/contratoModalidadeTurmaCons.jsp</to-view-id>
            <redirect />
        </navigation-case>
    </navigation-rule>
    <navigation-rule>
        <description>Navegacao da pagina</description>
        <from-view-id>/perguntaClienteForm.jsp</from-view-id>
        <navigation-case>
            <from-outcome>editar</from-outcome>
            <to-view-id>/perguntaClienteForm.jsp</to-view-id>
            <redirect />
        </navigation-case>
        <navigation-case>
            <from-outcome>consultar</from-outcome>
            <to-view-id>/perguntaClienteCons.jsp</to-view-id>
            <redirect />
        </navigation-case>
    </navigation-rule>
    <navigation-rule>
        <description>Navegacao da pagina</description>
        <from-view-id>/perguntaClienteCons.jsp</from-view-id>
        <navigation-case>
            <from-outcome>editar</from-outcome>
            <to-view-id>/perguntaClienteForm.jsp</to-view-id>
            <redirect />
        </navigation-case>
        <navigation-case>
            <from-outcome>consultar</from-outcome>
            <to-view-id>/perguntaClienteCons.jsp</to-view-id>
            <redirect />
        </navigation-case>
    </navigation-rule>
    <navigation-rule>
        <description>Navegacao da pagina</description>
        <from-view-id>/cidadeForm.jsp</from-view-id>
        <navigation-case>
            <from-outcome>editar</from-outcome>
            <to-view-id>/cidadeForm.jsp</to-view-id>
        </navigation-case>
        <navigation-case>
            <from-outcome>consultar</from-outcome>
            <to-view-id>/cidadeCons.jsp</to-view-id>
        </navigation-case>
    </navigation-rule>
    <navigation-rule>
        <description>Navegacao da pagina</description>
        <from-view-id>/clienteMensagemForm.jsp</from-view-id>
        <navigation-case>
            <from-outcome>editar</from-outcome>
            <to-view-id>/clienteMensagemForm.jsp</to-view-id>
            <redirect />
        </navigation-case>
        <navigation-case>
            <from-outcome>consultar</from-outcome>
            <to-view-id>/clienteMensagemCons.jsp</to-view-id>
            <redirect />
        </navigation-case>
    </navigation-rule>
    <navigation-rule>
        <description>Navegacao da pagina</description>
        <from-view-id>/cidadeCons.jsp</from-view-id>
        <navigation-case>
            <from-outcome>editar</from-outcome>
            <to-view-id>/cidadeForm.jsp</to-view-id>
        </navigation-case>
        <navigation-case>
            <from-outcome>consultar</from-outcome>
            <to-view-id>/cidadeCons.jsp</to-view-id>
        </navigation-case>
    </navigation-rule>
    <navigation-rule>
        <description>Navegacao da pagina</description>
        <from-view-id>/configuracaoSistemaForm.jsp</from-view-id>
        <navigation-case>
            <from-outcome>editar</from-outcome>
            <to-view-id>/configuracaoSistemaForm.jsp</to-view-id>
            <redirect />
        </navigation-case>
        <navigation-case>
            <from-outcome>consultar</from-outcome>
            <to-view-id>/configuracaoSistemaCons.jsp</to-view-id>
            <redirect />
        </navigation-case>
    </navigation-rule>
    <navigation-rule>
        <description>Navegacao da pagina</description>
        <from-view-id>/configuracaoSistemaCons.jsp</from-view-id>
        <navigation-case>
            <from-outcome>editar</from-outcome>
            <to-view-id>/configuracaoSistemaForm.jsp</to-view-id>
            <redirect />
        </navigation-case>
        <navigation-case>
            <from-outcome>consultar</from-outcome>
            <to-view-id>/configuracaoSistemaCons.jsp</to-view-id>
            <redirect />
        </navigation-case>
    </navigation-rule>
    <navigation-rule>
        <description>Navegacao da pagina</description>
        <from-view-id>/respostaPerguntaForm.jsp</from-view-id>
        <navigation-case>
            <from-outcome>editar</from-outcome>
            <to-view-id>/respostaPerguntaForm.jsp</to-view-id>
            <redirect />
        </navigation-case>
        <navigation-case>
            <from-outcome>consultar</from-outcome>
            <to-view-id>/respostaPerguntaCons.jsp</to-view-id>
            <redirect />
        </navigation-case>
    </navigation-rule>
    <navigation-rule>
        <description>Navegacao da pagina</description>
        <from-view-id>/respostaPerguntaCons.jsp</from-view-id>
        <navigation-case>
            <from-outcome>editar</from-outcome>
            <to-view-id>/respostaPerguntaForm.jsp</to-view-id>
            <redirect />
        </navigation-case>
        <navigation-case>
            <from-outcome>consultar</from-outcome>
            <to-view-id>/respostaPerguntaCons.jsp</to-view-id>
            <redirect />
        </navigation-case>
    </navigation-rule>
    <navigation-rule>
        <description>Navegacao da pagina</description>
        <from-view-id>/horarioTurmaForm.jsp</from-view-id>
        <navigation-case>
            <from-outcome>editar</from-outcome>
            <to-view-id>/horarioTurmaForm.jsp</to-view-id>
            <redirect />
        </navigation-case>
        <navigation-case>
            <from-outcome>consultar</from-outcome>
            <to-view-id>/horarioTurmaCons.jsp</to-view-id>
            <redirect />
        </navigation-case>
    </navigation-rule>
    <navigation-rule>
        <description>Navegacao da pagina</description>
        <from-view-id>/horarioTurmaCons.jsp</from-view-id>
        <navigation-case>
            <from-outcome>editar</from-outcome>
            <to-view-id>/horarioTurmaForm.jsp</to-view-id>
            <redirect />
        </navigation-case>
        <navigation-case>
            <from-outcome>consultar</from-outcome>
            <to-view-id>/horarioTurmaCons.jsp</to-view-id>
            <redirect />
        </navigation-case>
    </navigation-rule>
    <navigation-rule>
        <description>Navegacao da pagina</description>
        <from-view-id>/clienteForm.jsp</from-view-id>
        <navigation-case>
            <from-outcome>editar</from-outcome>
            <to-view-id>/clienteForm.jsp</to-view-id>
        </navigation-case>
        <navigation-case>
            <from-outcome>novo</from-outcome>
            <to-view-id>/clienteForm.jsp</to-view-id>
        </navigation-case>
        <navigation-case>
            <from-outcome>consultar</from-outcome>
            <to-view-id>/clienteCons.jsp</to-view-id>
        </navigation-case>
    </navigation-rule>
    <navigation-rule>
        <description>Navegacao da pagina</description>
        <from-view-id>/tela1.jsp</from-view-id>
        <navigation-case>
            <from-outcome>tela3</from-outcome>
            <to-view-id>/tela3.jsp</to-view-id>
        </navigation-case>
        <navigation-case>
            <from-outcome>novo</from-outcome>
            <to-view-id>/tela2.jsp</to-view-id>
            <redirect />
        </navigation-case>
        <navigation-case>
            <from-outcome>tela8</from-outcome>
            <to-view-id>/tela8.jsp</to-view-id>
            <redirect />
        </navigation-case>
        <navigation-case>
            <from-outcome>vendaAvulsa</from-outcome>
            <to-view-id>/vendaAvulsaForm.jsp</to-view-id>
            <redirect />
        </navigation-case>
        <navigation-case>
            <from-outcome>aulaAvulsa</from-outcome>
            <to-view-id>/aulaAvulsaForm.jsp</to-view-id>
            <redirect />
        </navigation-case>
        <navigation-case>
            <from-outcome>diaria</from-outcome>
            <to-view-id>/diariaForm.jsp</to-view-id>
            <redirect />
        </navigation-case>
        <navigation-case>
            <from-outcome>realizarOrcamento</from-outcome>
            <to-view-id>/realizarOrcamento.jsp</to-view-id>
            <redirect/>
        </navigation-case>
        <navigation-case>
            <from-outcome>freePass</from-outcome>
            <to-view-id>/freePassForm.jsp</to-view-id>
            <redirect />
        </navigation-case>
        <navigation-case>
            <from-outcome>indexRelatorio</from-outcome>
            <to-view-id>/indexRelatorio.jsp</to-view-id>
            <redirect />
        </navigation-case>
        <navigation-case>
            <from-outcome>notaFiscal</from-outcome>
            <to-view-id>/notaFiscal.jsp</to-view-id>
            <redirect />
        </navigation-case>
    </navigation-rule>
    <navigation-rule>
        <description>Navegacao da pagina</description>
        <from-view-id>/tela2.jsp</from-view-id>
        <navigation-case>
            <from-outcome>editar</from-outcome>
            <to-view-id>/tela4.jsp</to-view-id>
            <redirect />
        </navigation-case>
        <navigation-case>
            <from-outcome>tela3</from-outcome>
            <to-view-id>/tela3.jsp</to-view-id>
        </navigation-case>
        <navigation-case>
            <from-outcome>cancelar</from-outcome>
            <to-view-id>/tela3.jsp</to-view-id>
        </navigation-case>
        <navigation-case>
            <from-outcome>erro</from-outcome>
            <to-view-id>/tela2.jsp</to-view-id>
            <redirect />
        </navigation-case>
        <navigation-case>
            <from-outcome>tela8</from-outcome>
            <to-view-id>/tela8.jsp</to-view-id>
            <redirect />
        </navigation-case>
        <navigation-case>
            <from-outcome>tela2</from-outcome>
            <to-view-id>/tela2.jsp</to-view-id>
        </navigation-case>
    </navigation-rule>
    <navigation-rule>
        <description>Navegacao da pagina</description>
        <from-view-id>/tela4.jsp</from-view-id>
        <navigation-case>
            <from-outcome>editar</from-outcome>
            <to-view-id>/negociacaoContrato.jsp</to-view-id>
            <redirect />
        </navigation-case>
        <navigation-case>
            <from-outcome>tela3</from-outcome>
            <to-view-id>/tela3.jsp</to-view-id>
        </navigation-case>
        <navigation-case>
            <from-outcome>cancelar</from-outcome>
            <to-view-id>/tela3.jsp</to-view-id>
        </navigation-case>
        <navigation-case>
            <from-outcome>voltar</from-outcome>
            <to-view-id>/tela2.jsp</to-view-id>
            <redirect />
        </navigation-case>
        <navigation-case>
            <from-outcome>vendaAvulsa</from-outcome>
            <to-view-id>/vendaAvulsaForm.jsp</to-view-id>
            <redirect />
        </navigation-case>
     </navigation-rule>
     <navigation-rule>
         <description>Navegacao da pagina</description>
        <from-view-id>/clientes.jsp</from-view-id>
        <navigation-case>
            <from-outcome>novo</from-outcome>
            <to-view-id>/tela2.jsp</to-view-id>
            <redirect />
        </navigation-case>
        <navigation-case>
            <from-outcome>erro</from-outcome>
            <to-view-id>/tela3.jsp</to-view-id>
        </navigation-case>
        <navigation-case>
            <from-outcome>tela8</from-outcome>
            <to-view-id>/tela8.jsp</to-view-id>
            <redirect />
        </navigation-case>
        <navigation-case>
            <from-outcome>tela3</from-outcome>
            <to-view-id>/tela3.jsp</to-view-id>
        </navigation-case>
    </navigation-rule>
    <navigation-rule>
        <description>Navegacao da pagina</description>
        <from-view-id>/clienteCons.jsp</from-view-id>
        <navigation-case>
            <from-outcome>editar</from-outcome>
            <to-view-id>/clienteForm.jsp</to-view-id>
        </navigation-case>
        <navigation-case>
            <from-outcome>consultar</from-outcome>
            <to-view-id>/clienteCons.jsp</to-view-id>
        </navigation-case>
        <navigation-case>
            <from-outcome>novo</from-outcome>
            <to-view-id>/clienteForm.jsp</to-view-id>
        </navigation-case>
        <navigation-case>
            <from-outcome>clienteCons.jsp</from-outcome>
            <to-view-id>/clienteCons.jsp</to-view-id>
        </navigation-case>
    </navigation-rule>
    <navigation-rule>
        <description>Navegacao da pagina</description>
        <from-view-id>/turmaForm.jsp</from-view-id>
        <navigation-case>
            <from-outcome>editar</from-outcome>
            <to-view-id>/turmaForm.jsp</to-view-id>
            <redirect />
        </navigation-case>
        <navigation-case>
            <from-outcome>consultar</from-outcome>
            <to-view-id>/turmaCons.jsp</to-view-id>
            <redirect />
        </navigation-case>
    </navigation-rule>
    <navigation-rule>
        <description>Navegacao da pagina</description>
        <from-view-id>/turmaCons.jsp</from-view-id>
        <navigation-case>
            <from-outcome>editar</from-outcome>
            <to-view-id>/turmaForm.jsp</to-view-id>
            <redirect />
        </navigation-case>
        <navigation-case>
            <from-outcome>consultar</from-outcome>
            <to-view-id>/turmaCons.jsp</to-view-id>
            <redirect />
        </navigation-case>
    </navigation-rule>
    <navigation-rule>
        <description>Navegacao da pagina</description>
        <from-view-id>/bancoForm.jsp</from-view-id>
        <navigation-case>
            <from-outcome>editar</from-outcome>
            <to-view-id>/bancoForm.jsp</to-view-id>
            <redirect />
        </navigation-case>
        <navigation-case>
            <from-outcome>consultar</from-outcome>
            <to-view-id>/bancoCons.jsp</to-view-id>
            <redirect />
        </navigation-case>
    </navigation-rule>
    <navigation-rule>
        <description>Navegacao da pagina</description>
        <from-view-id>/bancoCons.jsp</from-view-id>
        <navigation-case>
            <from-outcome>editar</from-outcome>
            <to-view-id>/bancoForm.jsp</to-view-id>
            <redirect />
        </navigation-case>
        <navigation-case>
            <from-outcome>consultar</from-outcome>
            <to-view-id>/bancoCons.jsp</to-view-id>
            <redirect />
        </navigation-case>
    </navigation-rule>
    <navigation-rule>
        <description>Navegacao da pagina</description>
        <from-view-id>/configuracaoNotaFiscalForm.jsp</from-view-id>
        <navigation-case>
            <from-outcome>editar</from-outcome>
            <to-view-id>/configuracaoNotaFiscalForm.jsp</to-view-id>
            <redirect />
        </navigation-case>
        <navigation-case>
            <from-outcome>consultar</from-outcome>
            <to-view-id>/configuracaoNotaFiscalCons.jsp</to-view-id>
            <redirect />
        </navigation-case>
    </navigation-rule>
    <navigation-rule>
        <description>Navegacao da pagina</description>
        <from-view-id>/configuracaoNotaFiscalCons.jsp</from-view-id>
        <navigation-case>
            <from-outcome>editar</from-outcome>
            <to-view-id>/configuracaoNotaFiscalForm.jsp</to-view-id>
            <redirect />
        </navigation-case>
        <navigation-case>
            <from-outcome>consultar</from-outcome>
            <to-view-id>/configuracaoNotaFiscalCons.jsp</to-view-id>
            <redirect />
        </navigation-case>
    </navigation-rule>
    <navigation-rule>
        <description>Navegacao da pagina</description>
        <from-view-id>/contaCorrenteForm.jsp</from-view-id>
        <navigation-case>
            <from-outcome>editar</from-outcome>
            <to-view-id>/contaCorrenteForm.jsp</to-view-id>
            <redirect />
        </navigation-case>
        <navigation-case>
            <from-outcome>consultar</from-outcome>
            <to-view-id>/contaCorrenteCons.jsp</to-view-id>
            <redirect />
        </navigation-case>
    </navigation-rule>
    <navigation-rule>
        <description>Navegacao da pagina</description>
        <from-view-id>/contaCorrenteCons.jsp</from-view-id>
        <navigation-case>
            <from-outcome>editar</from-outcome>
            <to-view-id>/contaCorrenteForm.jsp</to-view-id>
            <redirect />
        </navigation-case>
        <navigation-case>
            <from-outcome>consultar</from-outcome>
            <to-view-id>/contaCorrenteCons.jsp</to-view-id>
            <redirect />
        </navigation-case>
    </navigation-rule>
    <navigation-rule>
        <description>Navegacao da pagina</description>
        <from-view-id>/tipoRetornoForm.jsp</from-view-id>
        <navigation-case>
            <from-outcome>editar</from-outcome>
            <to-view-id>/tipoRetornoForm.jsp</to-view-id>
            <redirect />
        </navigation-case>
        <navigation-case>
            <from-outcome>consultar</from-outcome>
            <to-view-id>/tipoRetornoCons.jsp</to-view-id>
            <redirect />
        </navigation-case>
    </navigation-rule>
    <navigation-rule>
        <description>Navegacao da pagina</description>
        <from-view-id>/tipoRetornoCons.jsp</from-view-id>
        <navigation-case>
            <from-outcome>editar</from-outcome>
            <to-view-id>/tipoRetornoForm.jsp</to-view-id>
            <redirect />
        </navigation-case>
        <navigation-case>
            <from-outcome>consultar</from-outcome>
            <to-view-id>/tipoRetornoCons.jsp</to-view-id>
            <redirect />
        </navigation-case>
    </navigation-rule>
    <navigation-rule>
        <description>Navegacao da pagina</description>
        <from-view-id>/tipoRemessaForm.jsp</from-view-id>
        <navigation-case>
            <from-outcome>editar</from-outcome>
            <to-view-id>/tipoRemessaForm.jsp</to-view-id>
            <redirect />
        </navigation-case>
        <navigation-case>
            <from-outcome>consultar</from-outcome>
            <to-view-id>/tipoRemessaCons.jsp</to-view-id>
            <redirect />
        </navigation-case>
    </navigation-rule>
    <navigation-rule>
        <description>Navegacao da pagina</description>
        <from-view-id>/tipoRemessaCons.jsp</from-view-id>
        <navigation-case>
            <from-outcome>editar</from-outcome>
            <to-view-id>/tipoRemessaForm.jsp</to-view-id>
            <redirect />
        </navigation-case>
        <navigation-case>
            <from-outcome>consultar</from-outcome>
            <to-view-id>/tipoRemessaCons.jsp</to-view-id>
            <redirect />
        </navigation-case>
    </navigation-rule>
    <navigation-rule>
        <description>Navegacao da pagina</description>
        <from-view-id>/convenioCobrancaForm.jsp</from-view-id>
        <navigation-case>
            <from-outcome>editar</from-outcome>
            <to-view-id>/convenioCobrancaForm.jsp</to-view-id>
            <redirect />
        </navigation-case>
        <navigation-case>
            <from-outcome>consultar</from-outcome>
            <to-view-id>/convenioCobrancaCons.jsp</to-view-id>
            <redirect />
        </navigation-case>
    </navigation-rule>
    <navigation-rule>
        <description>Navegacao da pagina</description>
        <from-view-id>/convenioCobrancaCons.jsp</from-view-id>
        <navigation-case>
            <from-outcome>editar</from-outcome>
            <to-view-id>/convenioCobrancaForm.jsp</to-view-id>
            <redirect />
        </navigation-case>
        <navigation-case>
            <from-outcome>consultar</from-outcome>
            <to-view-id>/convenioCobrancaCons.jsp</to-view-id>
            <redirect />
        </navigation-case>
    </navigation-rule>
    <navigation-rule>
        <description>Navegacao da pagina</description>
        <from-view-id>/pinPadForm.jsp</from-view-id>
        <navigation-case>
            <from-outcome>editar</from-outcome>
            <to-view-id>/pinPadForm.jsp</to-view-id>
            <redirect />
        </navigation-case>
        <navigation-case>
            <from-outcome>consultar</from-outcome>
            <to-view-id>/pinPadCons.jsp</to-view-id>
            <redirect />
        </navigation-case>
    </navigation-rule>
    <navigation-rule>
        <description>Navegacao da pagina</description>
        <from-view-id>/pinPadCons.jsp</from-view-id>
        <navigation-case>
            <from-outcome>editar</from-outcome>
            <to-view-id>/pinPadForm.jsp</to-view-id>
            <redirect />
        </navigation-case>
        <navigation-case>
            <from-outcome>consultar</from-outcome>
            <to-view-id>/pinPadCons.jsp</to-view-id>
            <redirect />
        </navigation-case>
    </navigation-rule>
    <navigation-rule>
        <description>Navegacao da pagina</description>
        <from-view-id>/convenioDescontoForm.jsp</from-view-id>
        <navigation-case>
            <from-outcome>editar</from-outcome>
            <to-view-id>/convenioDescontoForm.jsp</to-view-id>
            <redirect />
        </navigation-case>
        <navigation-case>
            <from-outcome>consultar</from-outcome>
            <to-view-id>/convenioDescontoCons.jsp</to-view-id>
            <redirect />
        </navigation-case>
    </navigation-rule>
    <navigation-rule>
        <description>Navegacao da pagina</description>
        <from-view-id>/convenioDescontoCons.jsp</from-view-id>
        <navigation-case>
            <from-outcome>editar</from-outcome>
            <to-view-id>/convenioDescontoForm.jsp</to-view-id>
            <redirect />
        </navigation-case>
        <navigation-case>
            <from-outcome>consultar</from-outcome>
            <to-view-id>/convenioDescontoCons.jsp</to-view-id>
            <redirect />
        </navigation-case>
    </navigation-rule>
    <navigation-rule>
        <description>Navegacao da pagina</description>
        <from-view-id>/descontoForm.jsp</from-view-id>
        <navigation-case>
            <from-outcome>editar</from-outcome>
            <to-view-id>/descontoForm.jsp</to-view-id>
            <redirect />
        </navigation-case>
        <navigation-case>
            <from-outcome>consultar</from-outcome>
            <to-view-id>/descontoCons.jsp</to-view-id>
            <redirect />
        </navigation-case>
    </navigation-rule>
    <navigation-rule>
        <description>Navegacao da pagina</description>
        <from-view-id>/descontoCons.jsp</from-view-id>
        <navigation-case>
            <from-outcome>editar</from-outcome>
            <to-view-id>/descontoForm.jsp</to-view-id>
            <redirect />
        </navigation-case>
        <navigation-case>
            <from-outcome>consultar</from-outcome>
            <to-view-id>/descontoCons.jsp</to-view-id>
            <redirect />
        </navigation-case>
    </navigation-rule>
    <navigation-rule>
        <description>Navegacao da pagina</description>
        <from-view-id>/negociacaoContrato.jsp</from-view-id>
        <navigation-case>
            <from-outcome>tela7</from-outcome>
            <to-view-id>/conferirNegociacaoContrato.jsp</to-view-id>
            <redirect />
        </navigation-case>
        <navigation-case>
            <from-outcome>erro</from-outcome>
            <to-view-id>/negociacaoContrato.jsp</to-view-id>
            <redirect />
        </navigation-case>
        <navigation-case>
            <from-outcome>tela3</from-outcome>
            <to-view-id>/tela3.jsp</to-view-id>
        </navigation-case>
        <navigation-case>
            <from-outcome>novo</from-outcome>
            <to-view-id>/tela2.jsp</to-view-id>
            <redirect />
        </navigation-case>
    </navigation-rule>
    <navigation-rule>
        <description>Navegacao da pagina</description>
        <from-view-id>/conferirNegociacaoContrato.jsp</from-view-id>
        <navigation-case>
            <from-outcome>voltar</from-outcome>
            <to-view-id>/negociacaoContrato.jsp</to-view-id>
            <redirect />
        </navigation-case>
        <navigation-case>
            <from-outcome>concluirNegociacao</from-outcome>
            <to-view-id>/tela8.jsp</to-view-id>
            <redirect />
        </navigation-case>
        <navigation-case>
            <from-outcome>erro</from-outcome>
            <to-view-id>/conferirNegociacaoContrato.jsp</to-view-id>
            <redirect />
        </navigation-case>
        <navigation-case>
            <from-outcome>tela3</from-outcome>
            <to-view-id>/tela3.jsp</to-view-id>
        </navigation-case>
        <navigation-case>
            <from-outcome>novo</from-outcome>
            <to-view-id>/tela2.jsp</to-view-id>
            <redirect />
        </navigation-case>
        <navigation-case>
            <from-outcome>concluirCadastroCliente</from-outcome>
            <to-view-id>/tela2.jsp</to-view-id>
            <redirect />
        </navigation-case>
    </navigation-rule>
    <navigation-rule>
        <description>Navegacao da pagina</description>
        <from-view-id>/movProdutoForm.jsp</from-view-id>
        <navigation-case>
            <from-outcome>editar</from-outcome>
            <to-view-id>/movProdutoForm.jsp</to-view-id>
            <redirect />
        </navigation-case>
        <navigation-case>
            <from-outcome>consultar</from-outcome>
            <to-view-id>/movProdutoCons.jsp</to-view-id>
            <redirect />
        </navigation-case>
    </navigation-rule>
    <navigation-rule>
        <description>Navegacao da pagina</description>
        <from-view-id>/movProdutoCons.jsp</from-view-id>
        <navigation-case>
            <from-outcome>editar</from-outcome>
            <to-view-id>/movProdutoForm.jsp</to-view-id>
            <redirect />
        </navigation-case>
        <navigation-case>
            <from-outcome>consultar</from-outcome>
            <to-view-id>/movProdutoCons.jsp</to-view-id>
            <redirect />
        </navigation-case>
    </navigation-rule>
    <navigation-rule>
        <description>Navegacao da pagina</description>
        <from-view-id>/justificativaOperacaoForm.jsp</from-view-id>
        <navigation-case>
            <from-outcome>editar</from-outcome>
            <to-view-id>/justificativaOperacaoForm.jsp</to-view-id>
            <redirect />
        </navigation-case>
        <navigation-case>
            <from-outcome>consultar</from-outcome>
            <to-view-id>/justificativaOperacaoCons.jsp</to-view-id>
            <redirect />
        </navigation-case>
    </navigation-rule>
    <navigation-rule>
        <description>Navegacao da pagina</description>
        <from-view-id>/justificativaOperacaoCons.jsp</from-view-id>
        <navigation-case>
            <from-outcome>editar</from-outcome>
            <to-view-id>/justificativaOperacaoForm.jsp</to-view-id>
            <redirect />
        </navigation-case>
        <navigation-case>
            <from-outcome>consultar</from-outcome>
            <to-view-id>/justificativaOperacaoCons.jsp</to-view-id>
            <redirect />
        </navigation-case>
    </navigation-rule>
    <navigation-rule>
        <description>Navegacao da pagina</description>
        <from-view-id>/tela8.jsp</from-view-id>
        <navigation-case>
            <from-outcome>tela3</from-outcome>
            <to-view-id>/tela3.jsp</to-view-id>
        </navigation-case>
        <navigation-case>
            <from-outcome>erro</from-outcome>
            <to-view-id>/tela8.jsp</to-view-id>
            <redirect />
        </navigation-case>
        <navigation-case>
            <from-outcome>gravar</from-outcome>
            <to-view-id>/pagamento.jsp</to-view-id>
            <redirect />
        </navigation-case>
        <navigation-case>
            <from-outcome>gravarBoleto</from-outcome>
            <to-view-id>/telaBoleto.jsp</to-view-id>
            <redirect />
        </navigation-case>
        <navigation-case>
            <from-outcome>novo</from-outcome>
            <to-view-id>/tela2.jsp</to-view-id>
            <redirect />
        </navigation-case>
    </navigation-rule>
    <navigation-rule>
        <description>Navegacao da pagina</description>
        <from-view-id>/contratoOperacaoForm.jsp</from-view-id>
        <navigation-case>
            <from-outcome>editar</from-outcome>
            <to-view-id>/contratoOperacaoForm.jsp</to-view-id>
            <redirect />
        </navigation-case>
        <navigation-case>
            <from-outcome>consultar</from-outcome>
            <to-view-id>/contratoOperacaoCons.jsp</to-view-id>
            <redirect />
        </navigation-case>
    </navigation-rule>
    <navigation-rule>
        <description>Navegacao da pagina</description>
        <from-view-id>/contratoOperacaoCons.jsp</from-view-id>
        <navigation-case>
            <from-outcome>editar</from-outcome>
            <to-view-id>/contratoOperacaoForm.jsp</to-view-id>
            <redirect />
        </navigation-case>
        <navigation-case>
            <from-outcome>consultar</from-outcome>
            <to-view-id>/contratoOperacaoCons.jsp</to-view-id>
            <redirect />
        </navigation-case>
    </navigation-rule>
    <navigation-rule>
        <description>Navegacao da pagina</description>
        <from-view-id>/cancelamentoForm.jsp</from-view-id>
        <navigation-case>
            <from-outcome>editar</from-outcome>
            <to-view-id>/cancelamentoForm.jsp</to-view-id>
            <redirect />
        </navigation-case>
        <navigation-case>
            <from-outcome>resultadoCalculo</from-outcome>
            <to-view-id>/cancelamentoCalculoForm.jsp</to-view-id>
            <redirect />
        </navigation-case>
    </navigation-rule>
    <navigation-rule>
        <description>Navegacao da pagina</description>
        <from-view-id>/cancelamentoForm.jsp</from-view-id>
        <navigation-case>
            <from-outcome>editar</from-outcome>
            <to-view-id>/cancelamentoForm.jsp</to-view-id>
            <redirect />
        </navigation-case>
        <navigation-case>
            <from-outcome>finalizarCancelamento</from-outcome>
            <to-view-id>/cancelamentoListaPagamentoForm.jsp</to-view-id>
            <redirect />
        </navigation-case>
    </navigation-rule>
    <navigation-rule>
        <description>Navegacao da pagina</description>
        <from-view-id>/cancelamentoCalculoForm.jsp</from-view-id>
        <navigation-case>
            <from-outcome>voltarCancelamento</from-outcome>
            <to-view-id>/cancelamentoForm.jsp</to-view-id>
            <redirect />
        </navigation-case>
        <navigation-case>
            <from-outcome>resultadoCalculo</from-outcome>
            <to-view-id>/cancelamentoCalculoForm.jsp</to-view-id>
            <redirect />
        </navigation-case>
        <navigation-case>
            <from-outcome>finalizarCancelamento</from-outcome>
            <to-view-id>/cancelamentoListaPagamentoForm.jsp</to-view-id>
            <redirect />
        </navigation-case>
        <navigation-case>
            <from-outcome>cancelamentoTransferencia</from-outcome>
            <to-view-id>/cancelamentoTransferenciaForm.jsp</to-view-id>
            <redirect />
        </navigation-case>
    </navigation-rule>
    <navigation-rule>
        <description>Navegacao da pagina</description>
        <from-view-id>/cancelamentoListaPagamentoForm.jsp</from-view-id>
        <navigation-case>
            <from-outcome>voltarCancelamento</from-outcome>
            <to-view-id>/cancelamentoCalculoForm.jsp</to-view-id>
            <redirect />
        </navigation-case>
        <navigation-case>
            <from-outcome>voltarCancelamentoInicio</from-outcome>
            <to-view-id>/cancelamentoForm.jsp</to-view-id>
            <redirect />
        </navigation-case>
        <navigation-case>
            <from-outcome>resultadoCalculo</from-outcome>
            <to-view-id>/cancelamentoListaPagamentoForm.jsp</to-view-id>
            <redirect />
        </navigation-case>
        <navigation-case>
            <from-outcome>finalizarCancelamento</from-outcome>
            <to-view-id>/cancelamentoFinalizadoForm.jsp</to-view-id>
            <redirect />
        </navigation-case>
        <navigation-case>
            <from-outcome>cancelamentoListaCheque</from-outcome>
            <to-view-id>/cancelamentoListaChequeForm.jsp</to-view-id>
            <redirect />
        </navigation-case>
        <navigation-case>
            <from-outcome>cancelamentoListaRecibo</from-outcome>
            <to-view-id>/cancelamentoListaRecibosForm.jsp</to-view-id>
            <redirect />
        </navigation-case>
    </navigation-rule>
    <navigation-rule>
        <description>Navegacao da pagina</description>
        <from-view-id>/cancelamentoListaRecibosForm.jsp</from-view-id>
        <navigation-case>
            <from-outcome>voltarCancelamento</from-outcome>
            <to-view-id>/cancelamentoListaPagamentoForm.jsp</to-view-id>
            <redirect />
        </navigation-case>
        <navigation-case>
            <from-outcome>voltarCancelamentoInicio</from-outcome>
            <to-view-id>/cancelamentoForm.jsp</to-view-id>
            <redirect />
        </navigation-case>
        <navigation-case>
            <from-outcome>cancelamentoListaCheque</from-outcome>
            <to-view-id>/cancelamentoListaChequeForm.jsp</to-view-id>
            <redirect />
        </navigation-case>
        <navigation-case>
            <from-outcome>finalizarCancelamento</from-outcome>
            <to-view-id>/cancelamentoFinalizadoForm.jsp</to-view-id>
            <redirect />
        </navigation-case>
    </navigation-rule>
    <navigation-rule>
        <description>Navegacao da pagina</description>
        <from-view-id>/cancelamentoListaChequeForm.jsp</from-view-id>
        <navigation-case>
            <from-outcome>voltarCancelamento</from-outcome>
            <to-view-id>/cancelamentoListaPagamentoForm.jsp</to-view-id>
            <redirect />
        </navigation-case>
        <navigation-case>
            <from-outcome>voltarCancelamentoInicio</from-outcome>
            <to-view-id>/cancelamentoForm.jsp</to-view-id>
            <redirect />
        </navigation-case>
        <navigation-case>
            <from-outcome>resultadoCalculo</from-outcome>
            <to-view-id>/cancelamentoListaChequeForm.jsp</to-view-id>
            <redirect />
        </navigation-case>
        <navigation-case>
            <from-outcome>finalizarCancelamento</from-outcome>
            <to-view-id>/cancelamentoFinalizadoForm.jsp</to-view-id>
            <redirect />
        </navigation-case>
    </navigation-rule>
    <navigation-rule>
        <description>Navegacao da pagina</description>
        <from-view-id>/cancelamentoFinalizadoForm.jsp</from-view-id>
        <navigation-case>
            <from-outcome>voltarCancelamento</from-outcome>
            <to-view-id>/cancelamentoListaPagamentoForm.jsp</to-view-id>
            <redirect />
        </navigation-case>
        <navigation-case>
            <from-outcome>voltarCancelamentoInicio</from-outcome>
            <to-view-id>/cancelamentoListaPagamentoForm.jsp</to-view-id>
            <redirect />
        </navigation-case>
    </navigation-rule>
    <navigation-rule>
        <description>Navegacao da pagina</description>
        <from-view-id>/cancelamentoTransferenciaForm.jsp</from-view-id>
        <navigation-case>
            <from-outcome>editar</from-outcome>
            <to-view-id>/cancelamentoTransferenciaForm.jsp</to-view-id>
            <redirect />
        </navigation-case>
        <navigation-case>
            <from-outcome>voltarCancelamento</from-outcome>
            <to-view-id>/cancelamentoCalculoForm.jsp</to-view-id>
            <redirect />
        </navigation-case>
        <navigation-case>
            <from-outcome>voltarCancelamentoInicio</from-outcome>
            <to-view-id>/cancelamentoForm.jsp</to-view-id>
            <redirect />
        </navigation-case>
        <navigation-case>
            <from-outcome>resultadoTransferencia</from-outcome>
            <to-view-id>/cancelamentoTransferenciaClienteForm.jsp</to-view-id>
            <redirect />
        </navigation-case>
    </navigation-rule>
    <navigation-rule>
        <description>Navegacao da pagina</description>
        <from-view-id>/cancelamentoTransferenciaClienteForm.jsp</from-view-id>
        <navigation-case>
            <from-outcome>voltarTransferencia</from-outcome>
            <to-view-id>/cancelamentoTransferenciaForm.jsp</to-view-id>
            <redirect />
        </navigation-case>
    </navigation-rule>
    <navigation-rule>
        <description>Navegacao da pagina</description>
        <from-view-id>/freePassForm.jsp</from-view-id>
        <navigation-case>
            <from-outcome>tela3</from-outcome>
            <to-view-id>/tela3.jsp</to-view-id>
        </navigation-case>
        <navigation-case>
            <from-outcome>novo</from-outcome>
            <to-view-id>/tela2.jsp</to-view-id>
            <redirect />
        </navigation-case>
        <navigation-case>
            <from-outcome>tela8</from-outcome>
            <to-view-id>/tela8.jsp</to-view-id>
            <redirect />
        </navigation-case>
        <navigation-case>
            <from-outcome>realizarOrcamento</from-outcome>
            <to-view-id>/realizarOrcamento.jsp</to-view-id>
            <redirect/>
        </navigation-case>
        <navigation-case>
            <from-outcome>freePass</from-outcome>
            <to-view-id>/freePassForm.jsp</to-view-id>
            <redirect />
        </navigation-case>
        <navigation-case>
            <from-outcome>vendaAvulsa</from-outcome>
            <to-view-id>/vendaAvulsaForm.jsp</to-view-id>
            <redirect />
        </navigation-case>
        <navigation-case>
            <from-outcome>aulaAvulsa</from-outcome>
            <to-view-id>/aulaAvulsaForm.jsp</to-view-id>
            <redirect />
        </navigation-case>
        <navigation-case>
            <from-outcome>diaria</from-outcome>
            <to-view-id>/diariaForm.jsp</to-view-id>
            <redirect />
        </navigation-case>
        <navigation-case>
            <from-outcome>erro</from-outcome>
            <to-view-id>/freePassForm.jsp</to-view-id>
            <redirect />
        </navigation-case>
        <navigation-case>
            <from-outcome>consultar</from-outcome>
            <to-view-id>/tela3.jsp</to-view-id>
        </navigation-case>
        <navigation-case>
            <from-outcome>editar</from-outcome>
            <to-view-id>/freePassForm.jsp</to-view-id>
            <redirect />
        </navigation-case>
    </navigation-rule>
    <navigation-rule>
        <description>Navegacao da pagina</description>
        <from-view-id>/vendaAvulsaForm.jsp</from-view-id>
        <navigation-case>
            <from-outcome>tela3</from-outcome>
            <to-view-id>/tela3.jsp</to-view-id>
        </navigation-case>
        <navigation-case>
            <from-outcome>novo</from-outcome>
            <to-view-id>/tela2.jsp</to-view-id>
            <redirect />
        </navigation-case>
        <navigation-case>
            <from-outcome>tela8</from-outcome>
            <to-view-id>/tela8.jsp</to-view-id>
            <redirect />
        </navigation-case>
        <navigation-case>
            <from-outcome>vendaAvulsa</from-outcome>
            <to-view-id>/vendaAvulsaForm.jsp</to-view-id>
            <redirect />
        </navigation-case>
        <navigation-case>
            <from-outcome>aulaAvulsa</from-outcome>
            <to-view-id>/aulaAvulsaForm.jsp</to-view-id>
            <redirect />
        </navigation-case>
        <navigation-case>
            <from-outcome>diaria</from-outcome>
            <to-view-id>/diariaForm.jsp</to-view-id>
            <redirect />
        </navigation-case>
        <navigation-case>
            <from-outcome>realizarOrcamento</from-outcome>
            <to-view-id>/realizarOrcamento.jsp</to-view-id>
            <redirect/>
        </navigation-case>
        <navigation-case>
            <from-outcome>freePass</from-outcome>
            <to-view-id>/freePassForm.jsp</to-view-id>
            <redirect />
        </navigation-case>
        <navigation-case>
            <from-outcome>erro</from-outcome>
            <to-view-id>/vendaAvulsaForm.jsp</to-view-id>
            <redirect />
        </navigation-case>
        <navigation-case>
            <from-outcome>consultar</from-outcome>
            <to-view-id>/tela3.jsp</to-view-id>
        </navigation-case>
    </navigation-rule>
    <navigation-rule>
        <description>Navegacao da pagina</description>
        <from-view-id>/trancamentoContratoForm.jsp</from-view-id>
        <navigation-case>
            <from-outcome>erro</from-outcome>
            <to-view-id>/trancamentoContratoForm.jsp</to-view-id>
            <redirect />
        </navigation-case>
        <navigation-case>
            <from-outcome>trancamentoContratoFinalizado</from-outcome>
            <to-view-id>/trancamentoContratoFinalizadoForm.jsp</to-view-id>
            <redirect />
        </navigation-case>
    </navigation-rule>
    <navigation-rule>
        <description>Navegacao da pagina</description>
        <from-view-id>/trancamentoContratoFinalizadoForm.jsp</from-view-id>
        <navigation-case>
            <from-outcome>voltar</from-outcome>
            <to-view-id>/trancamentoContratoForm.jsp</to-view-id>
            <redirect />
        </navigation-case>
        <navigation-case>
            <from-outcome>erro</from-outcome>
            <to-view-id>/trancamentoContratoFinalizadoForm.jsp</to-view-id>
            <redirect />
        </navigation-case>
    </navigation-rule>
    <navigation-rule>
        <description>Navegacao da pagina</description>
        <from-view-id>/retornoTrancamentoContratoForm.jsp</from-view-id>
        <navigation-case>
            <from-outcome>retornoTracamento</from-outcome>
            <to-view-id>/retornoTrancamentoContratoForm.jsp</to-view-id>
            <redirect />
        </navigation-case>
        <navigation-case>
            <from-outcome>erro</from-outcome>
            <to-view-id>/retornoTrancamentoContratoForm.jsp</to-view-id>
            <redirect />
        </navigation-case>
    </navigation-rule>
    <navigation-rule>
        <description>Navegacao da pagina</description>
        <from-view-id>/aulaAvulsaForm.jsp</from-view-id>
        <navigation-case>
            <from-outcome>tela3</from-outcome>
            <to-view-id>/tela3.jsp</to-view-id>
        </navigation-case>
        <navigation-case>
            <from-outcome>novo</from-outcome>
            <to-view-id>/tela2.jsp</to-view-id>
            <redirect />
        </navigation-case>
        <navigation-case>
            <from-outcome>aulaAvulsa</from-outcome>
            <to-view-id>/aulaAvulsaForm.jsp</to-view-id>
            <redirect />
        </navigation-case>
        <navigation-case>
            <from-outcome>vendaAvulsa</from-outcome>
            <to-view-id>/vendaAvulsaForm.jsp</to-view-id>
            <redirect />
        </navigation-case>
        <navigation-case>
            <from-outcome>diaria</from-outcome>
            <to-view-id>/diariaForm.jsp</to-view-id>
            <redirect />
        </navigation-case>
        <navigation-case>
            <from-outcome>realizarOrcamento</from-outcome>
            <to-view-id>/realizarOrcamento.jsp</to-view-id>
            <redirect/>
        </navigation-case>
        <navigation-case>
            <from-outcome>freePass</from-outcome>
            <to-view-id>/freePassForm.jsp</to-view-id>
            <redirect />
        </navigation-case>
        <navigation-case>
            <from-outcome>erro</from-outcome>
            <to-view-id>/aulaAvulsaForm.jsp</to-view-id>
            <redirect />
        </navigation-case>
        <navigation-case>
            <from-outcome>consultar</from-outcome>
            <to-view-id>/tela3.jsp</to-view-id>
        </navigation-case>
    </navigation-rule>
    <navigation-rule>
        <description>Navegacao da pagina</description>
        <from-view-id>/diariaForm.jsp</from-view-id>
        <navigation-case>
            <from-outcome>tela3</from-outcome>
            <to-view-id>/tela3.jsp</to-view-id>
        </navigation-case>
        <navigation-case>
            <from-outcome>novo</from-outcome>
            <to-view-id>/tela2.jsp</to-view-id>
            <redirect />
        </navigation-case>
        <navigation-case>
            <from-outcome>tela8</from-outcome>
            <to-view-id>/tela8.jsp</to-view-id>
            <redirect />
        </navigation-case>
        <navigation-case>
            <from-outcome>vendaAvulsa</from-outcome>
            <to-view-id>/vendaAvulsaForm.jsp</to-view-id>
            <redirect />
        </navigation-case>
        <navigation-case>
            <from-outcome>aulaAvulsa</from-outcome>
            <to-view-id>/aulaAvulsaForm.jsp</to-view-id>
            <redirect />
        </navigation-case>
        <navigation-case>
            <from-outcome>diaria</from-outcome>
            <to-view-id>/diariaForm.jsp</to-view-id>
            <redirect />
        </navigation-case>
        <navigation-case>
            <from-outcome>realizarOrcamento</from-outcome>
            <to-view-id>/realizarOrcamento.jsp</to-view-id>
            <redirect/>
        </navigation-case>
        <navigation-case>
            <from-outcome>freePass</from-outcome>
            <to-view-id>/freePassForm.jsp</to-view-id>
            <redirect />
        </navigation-case>
        <navigation-case>
            <from-outcome>erro</from-outcome>
            <to-view-id>/diariaForm.jsp</to-view-id>
            <redirect />
        </navigation-case>
        <navigation-case>
            <from-outcome>consultar</from-outcome>
            <to-view-id>/tela3.jsp</to-view-id>
        </navigation-case>
    </navigation-rule>
    <navigation-rule>
        <description>Navegacao da pagina</description>
        <from-view-id>/situacaoContratoSinteticoDW.jsp</from-view-id>
        <navigation-case>
            <from-outcome>clienteAnalitico</from-outcome>
            <to-view-id>/situacaoContratoAnaliticoDW.jsp</to-view-id>
            <redirect />
        </navigation-case>
    </navigation-rule>
    <navigation-rule>
        <description>Navegacao da pagina</description>
        <from-view-id>/situacaoRenovacaoSinteticoDW.jsp</from-view-id>
        <navigation-case>
            <from-outcome>renovacaoAnalitico</from-outcome>
            <to-view-id>/situacaoRenovacaoAnaliticoDW.jsp</to-view-id>
            <redirect />
        </navigation-case>
    </navigation-rule>
    <navigation-rule>
        <description>Navegacao da pagina</description>
        <from-view-id>/include_box_menuclienteeditar.jsp</from-view-id>
        <navigation-case>
            <from-outcome>vendaAvulsa</from-outcome>
            <to-view-id>/vendaAvulsaForm.jsp</to-view-id>
            <redirect />
        </navigation-case>
    </navigation-rule>
    <navigation-rule>
        <description>Navegacao da pagina</description>
        <from-view-id>/alterarHorarioContrato.jsp</from-view-id>
        <navigation-case>
            <from-outcome>tela8</from-outcome>
            <to-view-id>/tela8.jsp</to-view-id>
            <redirect />
        </navigation-case>
        <navigation-case>
            <from-outcome>erro</from-outcome>
            <to-view-id>/alterarHorarioContrato.jsp</to-view-id>
            <redirect />
        </navigation-case>
    </navigation-rule>
    <navigation-rule>
        <description>Navegacao da pagina</description>
        <from-view-id>/manutencaoModalidade.jsp</from-view-id>
        <navigation-case>
            <from-outcome>erro</from-outcome>
            <to-view-id>/alterarHorarioContrato.jsp</to-view-id>
            <redirect />
        </navigation-case>
        <navigation-case>
            <from-outcome>proximo</from-outcome>
            <to-view-id>/manutencaoModalidadeFinalizar.jsp</to-view-id>
            <redirect />
        </navigation-case>
    </navigation-rule>
    <navigation-rule>
        <description>Navegacao da pagina</description>
        <from-view-id>/manutencaoModalidadeFinalizar.jsp</from-view-id>
        <navigation-case>
            <from-outcome>tela8</from-outcome>
            <to-view-id>/tela8.jsp</to-view-id>
            <redirect />
        </navigation-case>
        <navigation-case>
            <from-outcome>voltar</from-outcome>
            <to-view-id>/manutencaoModalidade.jsp</to-view-id>
            <redirect />
        </navigation-case>
        <navigation-case>
            <from-outcome>erro</from-outcome>
            <to-view-id>/manutencaoModalidadeFinalizar.jsp</to-view-id>
            <redirect />
        </navigation-case>
    </navigation-rule>
    <navigation-rule>
        <description>Navegacao da pagina</description>
        <from-view-id>/relatorio/faturamentoSinteticoRel.jsp</from-view-id>
        <navigation-case>
            <from-outcome>consulta</from-outcome>
            <to-view-id>/relatorio/faturamentoSinteticoRel.jsp</to-view-id>
            <redirect />
        </navigation-case>
        <navigation-case>
            <from-outcome>relatorio</from-outcome>
            <to-view-id>/relatorio/faturamentoSinteticoRelForm.jsp</to-view-id>
            <redirect />
        </navigation-case>
    </navigation-rule>
    <navigation-rule>
        <description>Navegacao da pagina</description>
        <from-view-id>/relatorio/faturamentoSinteticoRelForm.jsp</from-view-id>
        <navigation-case>
            <from-outcome>consulta</from-outcome>
            <to-view-id>/relatorio/faturamentoSinteticoRel.jsp</to-view-id>
            <redirect />
        </navigation-case>
        <navigation-case>
            <from-outcome>voltar</from-outcome>
            <to-view-id>/relatorio/faturamentoSinteticoRel.jsp</to-view-id>
            <redirect />
        </navigation-case>
        <navigation-case>
            <from-outcome>relatorio</from-outcome>
            <to-view-id>/relatorio/faturamentoSinteticoRelForm.jsp</to-view-id>
            <redirect />
        </navigation-case>
    </navigation-rule>

    <navigation-rule>
        <description>Navegacao da pagina</description>
        <from-view-id>/relatorio/resultadoConvenioCobrancaRel.jsp</from-view-id>
        <navigation-case>
            <from-outcome>consulta</from-outcome>
            <to-view-id>/relatorio/resultadoConvenioCobrancaRel.jsp</to-view-id>
            <redirect />
        </navigation-case>
        <navigation-case>
            <from-outcome>relatorio</from-outcome>
            <to-view-id>/relatorio/resultadoConvenioCobrancaRelForm.jsp</to-view-id>
            <redirect />
        </navigation-case>
    </navigation-rule>
    <navigation-rule>
        <description>Navegacao da pagina</description>
        <from-view-id>/relatorio/resultadoConvenioCobrancaRelForm.jsp</from-view-id>
        <navigation-case>
            <from-outcome>consulta</from-outcome>
            <to-view-id>/relatorio/resultadoConvenioCobrancaRel.jsp</to-view-id>
            <redirect />
        </navigation-case>
    </navigation-rule>


    <navigation-rule>
        <description>Navegacao da pagina</description>
        <from-view-id>/relatorio/competenciaSinteticoRel.jsp</from-view-id>
        <navigation-case>
            <from-outcome>consulta</from-outcome>
            <to-view-id>/relatorio/competenciaSinteticoRel.jsp</to-view-id>
            <redirect />
        </navigation-case>
        <navigation-case>
            <from-outcome>relatorio</from-outcome>
            <to-view-id>/relatorio/competenciaSinteticoRelForm.jsp</to-view-id>
            <redirect />
        </navigation-case>
    </navigation-rule>
    <navigation-rule>
        <description>Navegacao da pagina</description>
        <from-view-id>/relatorio/competenciaSinteticoRelForm.jsp</from-view-id>
        <navigation-case>
            <from-outcome>consulta</from-outcome>
            <to-view-id>/relatorio/competenciaSinteticoRel.jsp</to-view-id>
            <redirect />
        </navigation-case>
        <navigation-case>
            <from-outcome>voltar</from-outcome>
            <to-view-id>/relatorio/competenciaSinteticoRel.jsp</to-view-id>
            <redirect />
        </navigation-case>
        <navigation-case>
            <from-outcome>relatorio</from-outcome>
            <to-view-id>/relatorio/competenciaSinteticoRelForm.jsp</to-view-id>
            <redirect />
        </navigation-case>
    </navigation-rule>
    <navigation-rule>
        <description>Navegacao da pagina</description>
        <from-view-id>/relatorio/clientePorDuracaoRel.jsp</from-view-id>
        <navigation-case>
            <from-outcome>relatorio</from-outcome>
            <to-view-id>/relatorio/clientePorDuracaoForm.jsp</to-view-id>
            <redirect />
        </navigation-case>
        <navigation-case>
            <from-outcome>editar</from-outcome>
            <to-view-id>/relatorio/clientePorDuracaoRel.jsp</to-view-id>
            <redirect />
        </navigation-case>
    </navigation-rule>
    <navigation-rule>
        <description>Navegacao da pagina</description>
        <from-view-id>/relatorio/clientePorDuracaoForm.jsp</from-view-id>
        <navigation-case>
            <from-outcome>voltar</from-outcome>
            <to-view-id>/relatorio/clientePorDuracaoRel.jsp</to-view-id>
            <redirect />
        </navigation-case>
    </navigation-rule>
    <navigation-rule>
        <description>Navegacao da pagina</description>
        <from-view-id>/relatorio/sgpModalidadesComTurma.jsp</from-view-id>
        <navigation-case>
            <from-outcome>consultar</from-outcome>
            <to-view-id>/relatorio/sgpModalidadesComTurma.jsp</to-view-id>
            <redirect />
        </navigation-case>
        <navigation-case>
            <from-outcome>relatorio</from-outcome>
            <to-view-id>/relatorio/sgpModalidadesComTurmaRel.jsp</to-view-id>
            <redirect />
        </navigation-case>
    </navigation-rule>
    <navigation-rule>
        <description>Navegacao da pagina</description>
        <from-view-id>/relatorio/sgpTurma.jsp</from-view-id>
        <navigation-case>
            <from-outcome>consultarTurma</from-outcome>
            <to-view-id>/relatorio/sgpTurma.jsp</to-view-id>
            <redirect />
        </navigation-case>
        <navigation-case>
            <from-outcome>relatorioTurma</from-outcome>
            <to-view-id>/relatorio/sgpTurmaRel.jsp</to-view-id>
            <redirect />
        </navigation-case>
    </navigation-rule>
    <navigation-rule>
        <description>Navegacao da pagina</description>
        <from-view-id>/relatorio/sgpModalidadesComTurmaRel.jsp</from-view-id>
        <navigation-case>
            <from-outcome>consultar</from-outcome>
            <to-view-id>/relatorio/sgpModalidadesComTurma.jsp</to-view-id>
            <redirect />
        </navigation-case>
    </navigation-rule>
    <navigation-rule>
        <description>Navegacao da pagina</description>
        <from-view-id>/relatorio/sgpTurmaRel.jsp</from-view-id>
        <navigation-case>
            <from-outcome>consultarTurma</from-outcome>
            <to-view-id>/relatorio/sgpTurma.jsp</to-view-id>
            <redirect />
        </navigation-case>
    </navigation-rule>

    <navigation-rule>
        <description>Navegacao da pagina</description>
        <from-view-id>/relatorio/sgpModalidadesSemTurma.jsp</from-view-id>
        <navigation-case>
            <from-outcome>consultar</from-outcome>
            <to-view-id>/relatorio/sgpModalidadesSemTurma.jsp</to-view-id>
            <redirect />
        </navigation-case>
        <navigation-case>
            <from-outcome>relatorio</from-outcome>
            <to-view-id>/relatorio/sgpModalidadesSemTurmaRel.jsp</to-view-id>
            <redirect />
        </navigation-case>
    </navigation-rule>
    <navigation-rule>
        <description>Navegacao da pagina</description>
        <from-view-id>/relatorio/sgpModalidadesSemTurmaRel.jsp</from-view-id>
        <navigation-case>
            <from-outcome>consultar</from-outcome>
            <to-view-id>/relatorio/sgpModalidadesSemTurma.jsp</to-view-id>
            <redirect />
        </navigation-case>
    </navigation-rule>


    <navigation-rule>
        <description>Navegacao da pagina</description>
        <from-view-id>/relatorio/sgpAvaliacaoFisicaCons.jsp</from-view-id>
        <navigation-case>
            <from-outcome>consultar</from-outcome>
            <to-view-id>/relatorio/sgpAvaliacaoFisicaCons.jsp</to-view-id>
            <redirect />
        </navigation-case>
        <navigation-case>
            <from-outcome>relatorio</from-outcome>
            <to-view-id>/relatorio/sgpAvaliacaoFisicaRel.jsp</to-view-id>
            <redirect />
        </navigation-case>
    </navigation-rule>
    <navigation-rule>
        <description>Navegacao da pagina</description>
        <from-view-id>/relatorio/sgpAvaliacaoFisicaRel.jsp</from-view-id>
        <navigation-case>
            <from-outcome>consultar</from-outcome>
            <to-view-id>/relatorio/sgpAvaliacaoFisicaCons.jsp</to-view-id>
            <redirect />
        </navigation-case>
    </navigation-rule>

    <navigation-rule>
        <description>Navegacao da pagina</description>
        <from-view-id>/relatorio/clientePorAniversarioRel.jsp</from-view-id>
        <navigation-case>
            <from-outcome>relatorio</from-outcome>
            <to-view-id>/relatorio/clientePorAniversarioResumo.jsp</to-view-id>
        </navigation-case>
    </navigation-rule>
    <navigation-rule>
        <description>Navegacao da pagina</description>
        <from-view-id>/relatorio/clientePorAniversarioResumo.jsp</from-view-id>
        <navigation-case>
            <from-outcome>voltar</from-outcome>
            <to-view-id>/relatorio/clientePorAniversarioRel.jsp</to-view-id>
        </navigation-case>
    </navigation-rule>
    <navigation-rule>
        <description>Navegacao da pagina</description>
        <from-view-id>/relatorio/historicoPontosParceiroRel.jsp</from-view-id>
        <navigation-case>
            <from-outcome>relatorio</from-outcome>
            <to-view-id>/relatorio/historicoPontosParceiroResumo.jsp</to-view-id>
        </navigation-case>
    </navigation-rule>
    <navigation-rule>
        <description>Navegacao da pagina</description>
        <from-view-id>/relatorio/historicoPontosParceiroResumo.jsp</from-view-id>
        <navigation-case>
            <from-outcome>voltar</from-outcome>
            <to-view-id>/relatorio/historicoPontosParceiroRel.jsp</to-view-id>
        </navigation-case>
    </navigation-rule>
    <navigation-rule>
        <description>Navegacao da pagina</description>
        <from-view-id>/relatorio/saldoCreditoRel.jsp</from-view-id>
        <navigation-case>
            <from-outcome>relatorio</from-outcome>
            <to-view-id>/relatorio/saldoCreditoResumo.jsp</to-view-id>
        </navigation-case>
    </navigation-rule>
    <navigation-rule>
        <description>Navegacao da pagina</description>
        <from-view-id>/relatorio/saldoCreditoResumo.jsp</from-view-id>
        <navigation-case>
            <from-outcome>voltar</from-outcome>
            <to-view-id>/relatorio/saldoCreditoRel.jsp</to-view-id>
        </navigation-case>
    </navigation-rule>
    <navigation-rule>
        <description>Navegacao da pagina</description>
        <from-view-id>/renovacaoAnaliticoForm.jsp</from-view-id>
        <navigation-case>
            <from-outcome>renovacaoAnaliticoForm</from-outcome>
            <to-view-id>/renovacaoAnaliticoForm.jsp</to-view-id>
            <redirect />
        </navigation-case>
    </navigation-rule>
    <navigation-rule>
        <description>Navegacao da pagina</description>
        <from-view-id>/carenciaContrato.jsp</from-view-id>
        <navigation-case>
            <from-outcome>carencia</from-outcome>
            <to-view-id>/carenciaContrato.jsp</to-view-id>
            <redirect />
        </navigation-case>
        <navigation-case>
            <from-outcome>erro</from-outcome>
            <to-view-id>/carenciaContrato.jsp</to-view-id>
            <redirect />
        </navigation-case>
    </navigation-rule>
    <navigation-rule>
        <description>Navegacao da pagina</description>
        <from-view-id>/consultarTurmaForm.jsp</from-view-id>
        <navigation-case>
            <from-outcome>listaAlunosTurma</from-outcome>
            <to-view-id>/listaAlunosTurma.jsp</to-view-id>
        </navigation-case>
        <navigation-case>
            <from-outcome>listaChamada</from-outcome>
            <to-view-id>/listaChamada.jsp</to-view-id>
        </navigation-case>
    </navigation-rule>
    <navigation-rule>
        <description>Navegacao da pagina</description>
        <from-view-id>/estornoReciboCons.jsp</from-view-id>
        <navigation-case>
            <from-outcome>editar</from-outcome>
            <to-view-id>/estornoReciboForm.jsp</to-view-id>
        </navigation-case>
        <navigation-case>
            <from-outcome>consultar</from-outcome>
            <to-view-id>/estornoReciboCons.jsp</to-view-id>
        </navigation-case>
    </navigation-rule>
    <navigation-rule>
        <description>Navegacao da pagina</description>
        <from-view-id>/estornoReciboForm.jsp</from-view-id>
        <navigation-case>
            <from-outcome>editar</from-outcome>
            <to-view-id>/estornoReciboForm.jsp</to-view-id>
        </navigation-case>
        <navigation-case>
            <from-outcome>consultar</from-outcome>
            <to-view-id>/estornoReciboCons.jsp</to-view-id>
        </navigation-case>
    </navigation-rule>
    <navigation-rule>
        <description>Navegacao da pagina</description>
        <from-view-id>/grupoColaboradorCons.jsp</from-view-id>
        <navigation-case>
            <from-outcome>editar</from-outcome>
            <to-view-id>/grupoColaboradorForm.jsp</to-view-id>
            <redirect />
        </navigation-case>
        <navigation-case>
            <from-outcome>consultar</from-outcome>
            <to-view-id>/grupoColaboradorCons.jsp</to-view-id>
            <redirect />
        </navigation-case>
    </navigation-rule>
    <navigation-rule>
        <description>Navegacao da pagina</description>
        <from-view-id>/grupoColaboradorForm.jsp</from-view-id>
        <navigation-case>
            <from-outcome>consultar</from-outcome>
            <to-view-id>/grupoColaboradorCons.jsp</to-view-id>
            <redirect />
        </navigation-case>
        <navigation-case>
            <from-outcome>editar</from-outcome>
            <to-view-id>/grupoColaboradorForm.jsp</to-view-id>
            <redirect />
        </navigation-case>
    </navigation-rule>
    <navigation-rule>
        <description>Navegacao da pagina</description>
        <from-view-id>/definirLayoutForm.jsp</from-view-id>
        <navigation-case>
            <from-outcome>consultar</from-outcome>
            <to-view-id>/definirLayoutCons.jsp</to-view-id>
            <redirect />
        </navigation-case>
        <navigation-case>
            <from-outcome>editar</from-outcome>
            <to-view-id>/definirLayoutForm.jsp</to-view-id>
            <redirect />
        </navigation-case>
    </navigation-rule>
    <navigation-rule>
        <description>Navegacao da pagina</description>
        <from-view-id>/definirLayoutCons.jsp</from-view-id>
        <navigation-case>
            <from-outcome>editar</from-outcome>
            <to-view-id>/definirLayoutForm.jsp</to-view-id>
            <redirect />
        </navigation-case>
        <navigation-case>
            <from-outcome>consultar</from-outcome>
            <to-view-id>/definirLayoutCons.jsp</to-view-id>
            <redirect />
        </navigation-case>
    </navigation-rule>
    <navigation-rule>
        <description>Navegacao da pagina</description>
        <from-view-id>/indexRelatorio.jsp</from-view-id>
        <navigation-case>
            <from-outcome>login</from-outcome>
            <to-view-id>/tela1.jsp</to-view-id>
            <redirect />
        </navigation-case>
        <navigation-case>
            <from-outcome>relatorioBvs</from-outcome>
            <to-view-id>/relatorioBVsForm.jsp</to-view-id>
            <redirect />
        </navigation-case>
    </navigation-rule>
    <navigation-rule>
        <description>Navegacao da pagina</description>
        <from-view-id>/telaInicialCRM.jsp</from-view-id>
        <navigation-case>
            <from-outcome>carteirasCRM</from-outcome>
            <to-view-id>/telaInicialCarteiras.jsp</to-view-id>
            <redirect/>
        </navigation-case>
        <navigation-case>
            <from-outcome>telaPrincipalCRM</from-outcome>
            <to-view-id>/telaPrincipalCRM.jsp</to-view-id>
            <redirect />
        </navigation-case>
        <navigation-case>
            <from-outcome>telaBICRM</from-outcome>
            <to-view-id>/telaBICRM.jsp</to-view-id>
            <redirect />
        </navigation-case>
        <navigation-case>
            <from-outcome>telaAntigoCRM</from-outcome>
            <to-view-id>/telaAntigoCRM.jsp</to-view-id>
            <redirect />
        </navigation-case>
    </navigation-rule>

    <navigation-rule>
        <description>Navegacao da pagina</description>
        <from-view-id>/telaPrincipalCRM.jsp</from-view-id>
        <navigation-case>
            <from-outcome>carteirasCRM</from-outcome>
            <to-view-id>/telaInicialCarteiras.jsp</to-view-id>
            <redirect/>
        </navigation-case>
        <navigation-case>
            <from-outcome>telaAntigoCRM</from-outcome>
            <to-view-id>/telaAntigoCRM.jsp</to-view-id>
            <redirect />
        </navigation-case>
        <navigation-case>
            <from-outcome>telaBICRM</from-outcome>
            <to-view-id>/telaBICRM.jsp</to-view-id>
            <redirect />
        </navigation-case>

        <navigation-case>
            <from-outcome>notaFiscal</from-outcome>
            <to-view-id>/notaFiscal.jsp</to-view-id>
            <redirect/>
        </navigation-case>
        <navigation-case>
            <from-outcome>crm</from-outcome>
            <to-view-id>/telaInicialCRM.jsp</to-view-id>
            <redirect />
        </navigation-case>
    </navigation-rule>

    <navigation-rule>
        <description>Navegacao da pagina</description>
        <from-view-id>/telaBICRM.jsp</from-view-id>
        <navigation-case>
            <from-outcome>carteirasCRM</from-outcome>
            <to-view-id>/telaInicialCarteiras.jsp</to-view-id>
            <redirect/>
        </navigation-case>
        <navigation-case>
            <from-outcome>telaAntigoCRM</from-outcome>
            <to-view-id>/telaAntigoCRM.jsp</to-view-id>
            <redirect />
        </navigation-case>
        <navigation-case>
            <from-outcome>notaFiscal</from-outcome>
            <to-view-id>/notaFiscal.jsp</to-view-id>
            <redirect/>
        </navigation-case>
        <navigation-case>
            <from-outcome>crm</from-outcome>
            <to-view-id>/telaInicialCRM.jsp</to-view-id>
            <redirect />
        </navigation-case>
        <navigation-case>
            <from-outcome>telaPrincipalCRM</from-outcome>
            <to-view-id>/telaPrincipalCRM.jsp</to-view-id>
            <redirect />
        </navigation-case>
    </navigation-rule>

    <navigation-rule>
        <description>Navegacao da pagina</description>
        <from-view-id>/telaAntigoCRM.jsp</from-view-id>
        <navigation-case>
            <from-outcome>crm</from-outcome>
            <to-view-id>/telaInicialCRM.jsp</to-view-id>
            <redirect />
        </navigation-case>
        <navigation-case>
            <from-outcome>carteirasCRM</from-outcome>
            <to-view-id>/telaInicialCarteiras.jsp</to-view-id>
            <redirect/>
        </navigation-case>
        <navigation-case>
            <from-outcome>telaBICRM</from-outcome>
            <to-view-id>/telaBICRM.jsp</to-view-id>
            <redirect />
        </navigation-case>
        <navigation-case>
            <from-outcome>telaPrincipalCRM</from-outcome>
            <to-view-id>/telaPrincipalCRM.jsp</to-view-id>
            <redirect />
        </navigation-case>
    </navigation-rule>

    <navigation-rule>
        <description>Navegacao da pagina</description>
        <from-view-id>/passivoCons.jsp</from-view-id>
        <navigation-case>
            <from-outcome>editar</from-outcome>
            <to-view-id>/passivoForm.jsp</to-view-id>
            <redirect />
        </navigation-case>
        <navigation-case>
            <from-outcome>consultar</from-outcome>
            <to-view-id>/passivoCons.jsp</to-view-id>
            <redirect />
        </navigation-case>
    </navigation-rule>
    <navigation-rule>
        <description>Navegacao da pagina</description>
        <from-view-id>/passivoForm.jsp</from-view-id>
        <navigation-case>
            <from-outcome>consultar</from-outcome>
            <to-view-id>/passivoCons.jsp</to-view-id>
            <redirect />
        </navigation-case>
        <navigation-case>
            <from-outcome>editar</from-outcome>
            <to-view-id>/passivoForm.jsp</to-view-id>
            <redirect />
        </navigation-case>
    </navigation-rule>
    <navigation-rule>
        <description>Navegacao da pagina</description>
        <from-view-id>/modeloMensagemCons.jsp</from-view-id>
        <navigation-case>
            <from-outcome>editar</from-outcome>
            <to-view-id>/modeloMensagemForm.jsp</to-view-id>
            <redirect />
        </navigation-case>
        <navigation-case>
            <from-outcome>consultar</from-outcome>
            <to-view-id>/modeloMensagemCons.jsp</to-view-id>
            <redirect />
        </navigation-case>
    </navigation-rule>
    <navigation-rule>
        <description>Navegacao da pagina</description>
        <from-view-id>/modeloMensagemForm.jsp</from-view-id>
        <navigation-case>
            <from-outcome>consultar</from-outcome>
            <to-view-id>/modeloMensagemCons.jsp</to-view-id>
            <redirect />
        </navigation-case>
        <navigation-case>
            <from-outcome>editar</from-outcome>
            <to-view-id>/modeloMensagemForm.jsp</to-view-id>
            <redirect />
        </navigation-case>
    </navigation-rule>
    <navigation-rule>
        <description>Navegacao da pagina</description>
        <from-view-id>/malaDiretaCons.jsp</from-view-id>
        <navigation-case>
            <from-outcome>editar</from-outcome>
            <to-view-id>/malaDiretaForm.jsp</to-view-id>
            <redirect/>
        </navigation-case>
        <navigation-case>
            <from-outcome>consultar</from-outcome>
            <to-view-id>/malaDiretaCons.jsp</to-view-id>
            <redirect/>
        </navigation-case>
    </navigation-rule>
    <navigation-rule>
        <description>Navegacao da pagina</description>
        <from-view-id>/malaDiretaForm.jsp</from-view-id>
        <navigation-case>
            <from-outcome>consultar</from-outcome>
            <to-view-id>/malaDiretaCons.jsp</to-view-id>
            <redirect />
        </navigation-case>
        <navigation-case>
            <from-outcome>editar</from-outcome>
            <to-view-id>/malaDiretaForm.jsp</to-view-id>
            <redirect />
        </navigation-case>
    </navigation-rule>
    <navigation-rule>
        <description>Navegacao da pagina</description>
        <from-view-id>/indicacaoForm.jsp</from-view-id>
        <navigation-case>
            <from-outcome>consultar</from-outcome>
            <to-view-id>/indicacaoCons.jsp</to-view-id>
            <redirect />
        </navigation-case>
        <navigation-case>
            <from-outcome>editar</from-outcome>
            <to-view-id>/indicacaoForm.jsp</to-view-id>
            <redirect />
        </navigation-case>
    </navigation-rule>
    <navigation-rule>
        <description>Navegacao da pagina</description>
        <from-view-id>/indicacaoCons.jsp</from-view-id>
        <navigation-case>
            <from-outcome>editar</from-outcome>
            <to-view-id>/indicacaoForm.jsp</to-view-id>
            <redirect />
        </navigation-case>
        <navigation-case>
            <from-outcome>consultar</from-outcome>
            <to-view-id>/indicacaoCons.jsp</to-view-id>
            <redirect />
        </navigation-case>
    </navigation-rule>
    <navigation-rule>
        <description>Navegacao da pagina</description>
        <from-view-id>/indicacaoForm.jsp</from-view-id>
        <navigation-case>
            <from-outcome>consultar</from-outcome>
            <to-view-id>/indicacaoCons.jsp</to-view-id>
            <redirect />
        </navigation-case>
        <navigation-case>
            <from-outcome>editar</from-outcome>
            <to-view-id>/indicacaoForm.jsp</to-view-id>
            <redirect />
        </navigation-case>
    </navigation-rule>
    <navigation-rule>
        <description>Navegacao da pagina</description>
        <from-view-id>/feriadoCons.jsp</from-view-id>
        <navigation-case>
            <from-outcome>editar</from-outcome>
            <to-view-id>/feriadoForm.jsp</to-view-id>
            <redirect />
        </navigation-case>
        <navigation-case>
            <from-outcome>consultar</from-outcome>
            <to-view-id>/feriadoCons.jsp</to-view-id>
            <redirect />
        </navigation-case>
    </navigation-rule>

    <navigation-rule>
        <description>Navegacao da pagina</description>
        <from-view-id>/crmExtraCons.jsp</from-view-id>
        <navigation-case>
            <from-outcome>editarCRMExtra</from-outcome>
            <to-view-id>/crmExtraForm.jsp</to-view-id>
            <redirect />
        </navigation-case>
        <navigation-case>
            <from-outcome>consultarCRMExtra</from-outcome>
            <to-view-id>/crmExtraCons.jsp</to-view-id>
            <redirect />
        </navigation-case>
    </navigation-rule>

    <navigation-rule>
        <description>Navegacao da pagina</description>
        <from-view-id>/crmExtraForm.jsp</from-view-id>
        <navigation-case>
            <from-outcome>consultarCRMExtra</from-outcome>
            <to-view-id>/crmExtraCons.jsp</to-view-id>
            <redirect />
        </navigation-case>
        <navigation-case>
            <from-outcome>editarCRMExtra</from-outcome>
            <to-view-id>/crmExtraForm.jsp</to-view-id>
            <redirect />
        </navigation-case>
    </navigation-rule>

	<!-- Pedro -->
    <navigation-rule>
        <description>Navegacao da pagina</description>
        <from-view-id>/feriadoForm.jsp</from-view-id>
        <navigation-case>
            <from-outcome>consultar</from-outcome>
            <to-view-id>/feriadoCons.jsp</to-view-id>
            <redirect />
        </navigation-case>
        <navigation-case>
            <from-outcome>editar</from-outcome>
            <to-view-id>/feriadoForm.jsp</to-view-id>
            <redirect />
        </navigation-case>
    </navigation-rule>
    <navigation-rule>
        <description>Navegacao da pagina</description>
        <from-view-id>/agendaCons.jsp</from-view-id>
        <navigation-case>
            <from-outcome>editar</from-outcome>
            <to-view-id>/agendaForm.jsp</to-view-id>
            <redirect />
        </navigation-case>
        <navigation-case>
            <from-outcome>consultar</from-outcome>
            <to-view-id>/agendaCons.jsp</to-view-id>
            <redirect />
        </navigation-case>
    </navigation-rule>
    <navigation-rule>
        <description>Navegacao da pagina</description>
        <from-view-id>/agendaForm.jsp</from-view-id>
        <navigation-case>
            <from-outcome>consultar</from-outcome>
            <to-view-id>/agendaCons.jsp</to-view-id>
            <redirect />
        </navigation-case>
        <navigation-case>
            <from-outcome>editar</from-outcome>
            <to-view-id>/agendaForm.jsp</to-view-id>
            <redirect />
        </navigation-case>
    </navigation-rule>
    <navigation-rule>
        <description>Navegacao da pagina</description>
        <from-view-id>/eventoCons.jsp</from-view-id>
        <navigation-case>
            <from-outcome>editar</from-outcome>
            <to-view-id>/eventoForm.jsp</to-view-id>
            <redirect />
        </navigation-case>
        <navigation-case>
            <from-outcome>consultar</from-outcome>
            <to-view-id>/eventoCons.jsp</to-view-id>
            <redirect />
        </navigation-case>
    </navigation-rule>
    <navigation-rule>
        <description>Navegacao da pagina</description>
        <from-view-id>/eventoForm.jsp</from-view-id>
        <navigation-case>
            <from-outcome>consultar</from-outcome>
            <to-view-id>/eventoCons.jsp</to-view-id>
            <redirect />
        </navigation-case>
        <navigation-case>
            <from-outcome>editar</from-outcome>
            <to-view-id>/eventoForm.jsp</to-view-id>
            <redirect />
        </navigation-case>
    </navigation-rule>
    <navigation-rule>
        <description>Navegacao da pagina</description>
        <from-view-id>/historicoVinculoCons.jsp</from-view-id>
        <navigation-case>
            <from-outcome>editar</from-outcome>
            <to-view-id>/historicoVinculoForm.jsp</to-view-id>
            <redirect />
        </navigation-case>
        <navigation-case>
            <from-outcome>consultar</from-outcome>
            <to-view-id>/historicoVinculoCons.jsp</to-view-id>
            <redirect />
        </navigation-case>
    </navigation-rule>
    <navigation-rule>
        <description>Navegacao da pagina</description>
        <from-view-id>/historicoVinculoForm.jsp</from-view-id>
        <navigation-case>
            <from-outcome>consultar</from-outcome>
            <to-view-id>/historicoVinculoCons.jsp</to-view-id>
            <redirect />
        </navigation-case>
        <navigation-case>
            <from-outcome>editar</from-outcome>
            <to-view-id>/historicoVinculoForm.jsp</to-view-id>
            <redirect />
        </navigation-case>
    </navigation-rule>
    <navigation-rule>
        <description>Navegacao da pagina</description>
        <from-view-id>/operadoraCartaoCons.jsp</from-view-id>
        <navigation-case>
            <from-outcome>editar</from-outcome>
            <to-view-id>/operadoraCartaoForm.jsp</to-view-id>
        </navigation-case>
        <navigation-case>
            <from-outcome>consultar</from-outcome>
            <to-view-id>/operadoraCartaoCons.jsp</to-view-id>
        </navigation-case>
    </navigation-rule>
    <navigation-rule>
        <description>Navegacao da pagina</description>
        <from-view-id>/operadoraCartaoForm.jsp</from-view-id>
        <navigation-case>
            <from-outcome>consultar</from-outcome>
            <to-view-id>/operadoraCartaoCons.jsp</to-view-id>
        </navigation-case>
        <navigation-case>
            <from-outcome>editar</from-outcome>
            <to-view-id>/operadoraCartaoForm.jsp</to-view-id>
        </navigation-case>
    </navigation-rule>


	<!-- Carlos -->
    <navigation-rule>
        <description>Navegacao da pagina</description>
        <from-view-id>/objecaoCons.jsp</from-view-id>
        <navigation-case>
            <from-outcome>editar</from-outcome>
            <to-view-id>/objecaoForm.jsp</to-view-id>
            <redirect />
        </navigation-case>
        <navigation-case>
            <from-outcome>consultar</from-outcome>
            <to-view-id>/objecaoCons.jsp</to-view-id>
            <redirect />
        </navigation-case>
    </navigation-rule>
    <navigation-rule>
        <description>Navegacao da pagina</description>
        <from-view-id>/objecaoForm.jsp</from-view-id>
        <navigation-case>
            <from-outcome>consultar</from-outcome>
            <to-view-id>/objecaoCons.jsp</to-view-id>
            <redirect />
        </navigation-case>
        <navigation-case>
            <from-outcome>editar</from-outcome>
            <to-view-id>/objecaoForm.jsp</to-view-id>
            <redirect />
        </navigation-case>
    </navigation-rule>


    <navigation-rule>
        <description>Navegacao da pagina</description>
        <from-view-id>/fechamentoDiaAberturaMetaCons.jsp</from-view-id>
        <navigation-case>
            <from-outcome>editar</from-outcome>
            <to-view-id>/fechamentoDiaAberturaMetaForm.jsp</to-view-id>
            <redirect />
        </navigation-case>
        <navigation-case>
            <from-outcome>consultar</from-outcome>
            <to-view-id>/fechamentoDiaAberturaMetaCons.jsp</to-view-id>
            <redirect />
        </navigation-case>
    </navigation-rule>
    <navigation-rule>
        <description>Navegacao da pagina</description>
        <from-view-id>/fechamentoDiaAberturaMetaForm.jsp</from-view-id>
        <navigation-case>
            <from-outcome>consultar</from-outcome>
            <to-view-id>/fechamentoDiaAberturaMetaCons.jsp</to-view-id>
            <redirect />
        </navigation-case>
        <navigation-case>
            <from-outcome>editar</from-outcome>
            <to-view-id>/fechamentoDiaAberturaMetaForm.jsp</to-view-id>
            <redirect />
        </navigation-case>
    </navigation-rule>

    <navigation-rule>
        <description>Navegacao da pagina</description>
        <from-view-id>/vendaConsumidorCons.jsp</from-view-id>
        <navigation-case>
            <from-outcome>consultar</from-outcome>
            <to-view-id>/vendaConsumidorCons.jsp</to-view-id>
            <redirect />
        </navigation-case>
        <navigation-case>
            <from-outcome>editar</from-outcome>
            <to-view-id>/vendaConsumidorForm.jsp</to-view-id>
            <redirect />
        </navigation-case>
    </navigation-rule>
    <navigation-rule>
        <description>Navegacao da pagina</description>
        <from-view-id>/vendaConsumidorForm.jsp</from-view-id>
        <navigation-case>
            <from-outcome>consultar</from-outcome>
            <to-view-id>/vendaConsumidorCons.jsp</to-view-id>
            <redirect />
        </navigation-case>
        <navigation-case>
            <from-outcome>editar</from-outcome>
            <to-view-id>/vendaConsumidorForm.jsp</to-view-id>
            <redirect />
        </navigation-case>
    </navigation-rule>
    <navigation-rule>
        <description>Navegacao da pagina</description>
        <from-view-id>/totalizadorMeta.jsp</from-view-id>
        <navigation-case>
            <from-outcome>consultar</from-outcome>
            <to-view-id>/totalizadorMetaResultado.jsp</to-view-id>
            <redirect />
        </navigation-case>
    </navigation-rule>
    <navigation-rule>
        <description>Navegacao da pagina</description>
        <from-view-id>/totalizadorMetaResultado.jsp</from-view-id>
        <navigation-case>
            <from-outcome>consultar</from-outcome>
            <to-view-id>/totalizadorMeta.jsp</to-view-id>
            <redirect />
        </navigation-case>
    </navigation-rule>
    <navigation-rule>
        <description>Navegacao da pagina</description>
        <from-view-id>/afastamentoContratoForm.jsp</from-view-id>
        <navigation-case>
            <from-outcome>carencia</from-outcome>
            <to-view-id>/carenciaContrato.jsp</to-view-id>
            <redirect />
        </navigation-case>
        <navigation-case>
            <from-outcome>erroCarencia</from-outcome>
            <to-view-id>/carenciaContrato.jsp</to-view-id>
            <redirect />
        </navigation-case>
        <navigation-case>
            <from-outcome>atestado</from-outcome>
            <to-view-id>/atestadoContrato.jsp</to-view-id>
            <redirect />
        </navigation-case>
        <navigation-case>
            <from-outcome>erroAtestado</from-outcome>
            <to-view-id>/atestadoContrato.jsp</to-view-id>
            <redirect />
        </navigation-case>
        <navigation-case>
            <from-outcome>trancamento</from-outcome>
            <to-view-id>/trancamentoContratoForm.jsp</to-view-id>
            <redirect />
        </navigation-case>
        <navigation-case>
            <from-outcome>erroTrancamento</from-outcome>
            <to-view-id>/trancamentoContratoForm.jsp</to-view-id>
            <redirect />
        </navigation-case>
        <navigation-case>
            <from-outcome>cancelamento</from-outcome>
            <to-view-id>/cancelamentoForm.jsp</to-view-id>
            <redirect />
        </navigation-case>
        <navigation-case>
            <from-outcome>erroCancelamento</from-outcome>
            <to-view-id>/cancelamentoForm.jsp</to-view-id>
            <redirect />
        </navigation-case>
        <navigation-case>
            <from-outcome>cancelamentoProporcional</from-outcome>
            <to-view-id>/cancelamentoCustoForm.jsp</to-view-id>
            <redirect/>
        </navigation-case>
        <navigation-case>
            <from-outcome>cancelamentoAvaliandoParcelas</from-outcome>
            <to-view-id>/cancelamentoCalculoAvaliandoParcelaForm.jsp</to-view-id>
            <redirect/>
        </navigation-case>
    </navigation-rule>
    <navigation-rule>
        <description>Navegacao da pagina</description>
        <from-view-id>/cancelamentoCustoForm.jsp</from-view-id>
        <navigation-case>
            <from-outcome>editar</from-outcome>
            <to-view-id>/cancelamentoCustoForm.jsp</to-view-id>
            <redirect/>
        </navigation-case>
        <navigation-case>
            <from-outcome>voltarCancelamento</from-outcome>
            <to-view-id>/afastamentoContratoForm.jsp</to-view-id>
            <redirect/>
        </navigation-case>
        <navigation-case>
            <from-outcome>voltarCancelamentoInicio</from-outcome>
            <to-view-id>/cancelamentoForm.jsp</to-view-id>
            <redirect />
        </navigation-case>
    </navigation-rule>


    <navigation-rule>
        <description>Navegacao da pagina</description>
        <from-view-id>/cancelamentoCalculoAvaliandoParcelaForm.jsp</from-view-id>
        <navigation-case>
            <from-outcome>voltarCancelamento</from-outcome>
            <to-view-id>/cancelamentoForm.jsp</to-view-id>
            <redirect />
        </navigation-case>
    </navigation-rule>

     <!--parametro de Modulo Estudio-->
    <navigation-rule>
        <description>Navegacao da pagina</description>
        <from-view-id>/modulo_incluir_cliente.jsp?modulo=4bf2add2267962ea87f029fef8f75a2f</from-view-id>
        <navigation-case>
            <from-outcome>erro</from-outcome>
            <to-view-id>/modulo_incluir_cliente.jsp?modulo=4bf2add2267962ea87f029fef8f75a2f</to-view-id>
        </navigation-case>
    </navigation-rule>
    <navigation-rule>
        <from-view-id>/modulo_incluir_cliente.jsp</from-view-id>
        <navigation-case>
            <from-outcome>cancelar?modulo=4bf2add2267962ea87f029fef8f75a2f</from-outcome>
            <to-view-id>/modulo_lista_clientes.jsp?modulo=4bf2add2267962ea87f029fef8f75a2f</to-view-id>
            <redirect/>
        </navigation-case>
        <navigation-case>
            <from-outcome>sucesso</from-outcome>
            <to-view-id>/modulo_lista_clientes.jsp?modulo=4bf2add2267962ea87f029fef8f75a2f</to-view-id>
            <redirect/>
        </navigation-case>
    </navigation-rule>
    <navigation-rule>
        <from-view-id>/modulo_venda_avulsa.jsp?modulo=4bf2add2267962ea87f029fef8f75a2f</from-view-id>
        <navigation-case>
            <from-outcome>erro</from-outcome>
            <to-view-id>/modulo_venda_avulsa.jsp?modulo=4bf2add2267962ea87f029fef8f75a2f</to-view-id>
            <redirect/>
        </navigation-case>
    </navigation-rule>
    <navigation-rule>
        <description>Navegacao da pagina</description>
        <from-view-id>/nfe/usuarioCons.jsp</from-view-id>
        <navigation-case>
            <from-outcome>editar</from-outcome>
            <to-view-id>/nfe/usuarioForm.jsp</to-view-id>
        </navigation-case>
        <navigation-case>
            <from-outcome>consultar</from-outcome>
            <to-view-id>/nfe/usuarioCons.jsp</to-view-id>
        </navigation-case>
        <navigation-case>
            <from-outcome>novo</from-outcome>
            <to-view-id>/nfe/usuarioForm.jsp</to-view-id>
        </navigation-case>
        <navigation-case>
            <from-outcome>/nfe/usuarioCons.jsp</from-outcome>
            <to-view-id>/nfe/usuarioCons.jsp</to-view-id>
        </navigation-case>
    </navigation-rule>

    <navigation-rule>
        <description>Navegacao da pagina</description>
        <from-view-id>/nfe/usuarioForm.jsp</from-view-id>
        <navigation-case>
            <from-outcome>editar</from-outcome>
            <to-view-id>/nfe/usuarioForm.jsp</to-view-id>
            <redirect/>
        </navigation-case>
        <navigation-case>
            <from-outcome>consultar</from-outcome>
            <to-view-id>/nfe/usuarioCons.jsp</to-view-id>
            <redirect/>
        </navigation-case>
    </navigation-rule>

    <navigation-rule>
        <description>Navegacao da pagina</description>
        <from-view-id>/nfe/empresaNFeCons.jsp</from-view-id>
        <navigation-case>
            <from-outcome>editar</from-outcome>
            <to-view-id>/nfe/empresaNFeForm.jsp</to-view-id>
        </navigation-case>
        <navigation-case>
            <from-outcome>consultar</from-outcome>
            <to-view-id>/nfe/empresaNFeCons.jsp</to-view-id>
        </navigation-case>
        <navigation-case>
            <from-outcome>novo</from-outcome>
            <to-view-id>/nfe/empresaNFeForm.jsp</to-view-id>
        </navigation-case>
        <navigation-case>
            <from-outcome>/nfe/empresaNFeCons.jsp</from-outcome>
            <to-view-id>/nfe/empresaNFeCons.jsp</to-view-id>
        </navigation-case>
    </navigation-rule>
    <navigation-rule>
        <description>Navegacao da pagina</description>
        <from-view-id>/nfe/empresaNFeForm.jsp</from-view-id>
        <navigation-case>
            <from-outcome>editar</from-outcome>
            <to-view-id>/nfe/empresaNFeForm.jsp</to-view-id>
            <redirect/>
        </navigation-case>
        <navigation-case>
            <from-outcome>consultar</from-outcome>
            <to-view-id>/nfe/empresaNFeCons.jsp</to-view-id>
            <redirect/>
        </navigation-case>
    </navigation-rule>

    <navigation-rule>
        <description>Navegacao da pagina</description>
        <from-view-id>/nfe/perfilNFeCons.jsp</from-view-id>
        <navigation-case>
            <from-outcome>editar</from-outcome>
            <to-view-id>/nfe/perfilNFeForm.jsp</to-view-id>
        </navigation-case>
        <navigation-case>
            <from-outcome>consultar</from-outcome>
            <to-view-id>/nfe/perfilNFeCons.jsp</to-view-id>
        </navigation-case>
        <navigation-case>
            <from-outcome>novo</from-outcome>
            <to-view-id>/nfe/perfilNFeForm.jsp</to-view-id>
        </navigation-case>
        <navigation-case>
            <from-outcome>/nfe/perfilNFeCons.jsp</from-outcome>
            <to-view-id>/nfe/perfilNFeCons.jsp</to-view-id>
        </navigation-case>
    </navigation-rule>
    <navigation-rule>
        <description>Navegacao da pagina</description>
        <from-view-id>/nfe/perfilNFeForm.jsp</from-view-id>
        <navigation-case>
            <from-outcome>editar</from-outcome>
            <to-view-id>/nfe/perfilNFeForm.jsp</to-view-id>
            <redirect/>
        </navigation-case>
        <navigation-case>
            <from-outcome>consultar</from-outcome>
            <to-view-id>/nfe/perfilNFeCons.jsp</to-view-id>
            <redirect/>
        </navigation-case>
    </navigation-rule>
    <navigation-rule>
        <description>Navegacao da pagina</description>
        <from-view-id>/integracaoAcessoCons.jsp</from-view-id>
        <navigation-case>
            <from-outcome>editar</from-outcome>
            <to-view-id>/integracaoAcessoForm.jsp</to-view-id>
            <redirect />
        </navigation-case>
        <navigation-case>
            <from-outcome>consultar</from-outcome>
            <to-view-id>/integracaoAcessoCons.jsp</to-view-id>
            <redirect />
        </navigation-case>
    </navigation-rule>
    <navigation-rule>
        <description>Navegacao da pagina</description>
        <from-view-id>/integracaoAcessoForm.jsp</from-view-id>
        <navigation-case>
            <from-outcome>editar</from-outcome>
            <to-view-id>/integracaoAcessoForm.jsp</to-view-id>
            <redirect />
        </navigation-case>
        <navigation-case>
            <from-outcome>consultar</from-outcome>
            <to-view-id>/integracaoAcessoCons.jsp</to-view-id>
            <redirect />
        </navigation-case>
    </navigation-rule>

    <navigation-rule>
        <description>Navegacao da pagina</description>
        <from-view-id>/autorizacaoAcessoGrupoForm.jsp</from-view-id>
        <navigation-case>
            <from-outcome>editar</from-outcome>
            <to-view-id>/autorizacaoAcessoGrupoForm.jsp</to-view-id>
            <redirect />
        </navigation-case>
        <navigation-case>
            <from-outcome>consultar</from-outcome>
            <to-view-id>/autorizacaoAcessoGrupoCons.jsp</to-view-id>
            <redirect />
        </navigation-case>
    </navigation-rule>

    <navigation-rule>
        <description>Navegacao da pagina</description>
        <from-view-id>/autorizacaoAcessoGrupoCons.jsp</from-view-id>
        <navigation-case>
            <from-outcome>editar</from-outcome>
            <to-view-id>/autorizacaoAcessoGrupoForm.jsp</to-view-id>
            <redirect />
        </navigation-case>
        <navigation-case>
            <from-outcome>consultar</from-outcome>
            <to-view-id>/autorizacaoAcessoGrupoCons.jsp</to-view-id>
            <redirect />
        </navigation-case>
    </navigation-rule>

    <navigation-rule>
        <description>Navegacao da pagina</description>
        <from-view-id>/textoPadraoForm.jsp</from-view-id>
        <navigation-case>
            <from-outcome>editar</from-outcome>
            <to-view-id>/textoPadraoForm.jsp</to-view-id>
            <redirect />
        </navigation-case>
        <navigation-case>
            <from-outcome>consultar</from-outcome>
            <to-view-id>/textoPadraoCons.jsp</to-view-id>
            <redirect />
        </navigation-case>
    </navigation-rule>
    <navigation-rule>
        <description>Navegacao da pagina</description>
        <from-view-id>/textoPadraoCons.jsp</from-view-id>
        <navigation-case>
            <from-outcome>editar</from-outcome>
            <to-view-id>/textoPadraoForm.jsp</to-view-id>
            <redirect />
        </navigation-case>
        <navigation-case>
            <from-outcome>consultar</from-outcome>
            <to-view-id>/textoPadraoCons.jsp</to-view-id>
            <redirect />
        </navigation-case>
    </navigation-rule>

    <navigation-rule>
        <description>Navegacao da pagina</description>
        <from-view-id>/comissaoGeralConfiguracaoForm.jsp</from-view-id>
        <navigation-case>
            <from-outcome>editar</from-outcome>
            <to-view-id>/comissaoGeralConfiguracaoForm.jsp</to-view-id>
        </navigation-case>
        <navigation-case>
            <from-outcome>consultar</from-outcome>
            <to-view-id>/comissaoGeralConfiguracaoCons.jsp</to-view-id>
        </navigation-case>
    </navigation-rule>
    <navigation-rule>
        <description>Navegacao da pagina</description>
        <from-view-id>/comissaoGeralConfiguracaoCons.jsp</from-view-id>
        <navigation-case>
            <from-outcome>editar</from-outcome>
            <to-view-id>/comissaoGeralConfiguracaoForm.jsp</to-view-id>
        </navigation-case>
        <navigation-case>
            <from-outcome>consultar</from-outcome>
            <to-view-id>/comissaoGeralConfiguracaoCons.jsp</to-view-id>
        </navigation-case>
    </navigation-rule>

    <navigation-rule>
        <description>Navegacao da pagina</description>
        <from-view-id>/lancamentoProdutoColetivoForm.jsp</from-view-id>
        <navigation-case>
            <from-outcome>editar</from-outcome>
            <to-view-id>/lancamentoProdutoColetivoForm.jsp</to-view-id>
            <redirect />
        </navigation-case>
        <navigation-case>
            <from-outcome>consultar</from-outcome>
            <to-view-id>/lancamentoProdutoColetivoCons.jsp</to-view-id>
            <redirect />
        </navigation-case>
    </navigation-rule>

    <navigation-rule>
        <description>Navegacao da pagina</description>
        <from-view-id>/lancamentoProdutoColetivoCons.jsp</from-view-id>
        <navigation-case>
            <from-outcome>editar</from-outcome>
            <to-view-id>/lancamentoProdutoColetivoForm.jsp</to-view-id>
            <redirect />
        </navigation-case>
        <navigation-case>
            <from-outcome>consultar</from-outcome>
            <to-view-id>/lancamentoProdutoColetivoCons.jsp</to-view-id>
            <redirect />
        </navigation-case>
    </navigation-rule>
    
    <navigation-rule>
        <description>Navegacao da pagina</description>
        <from-view-id>/preCadastro.jsp</from-view-id>
        <navigation-case>
            <from-outcome>novo</from-outcome>
            <to-view-id>/tela2.jsp</to-view-id>
            <redirect />
        </navigation-case>
    </navigation-rule>
    
     <navigation-rule>
        <description>Navegacao da pagina</description>
        <from-view-id>/tamanhoArmarioForm.jsp</from-view-id>
        <navigation-case>
            <from-outcome>editar</from-outcome>
            <to-view-id>/tamanhoArmarioForm.jsp</to-view-id>
            <redirect />
        </navigation-case>
        <navigation-case>
            <from-outcome>consultar</from-outcome>
            <to-view-id>/tamanhoArmarioCons.jsp</to-view-id>
            <redirect />
        </navigation-case>
    </navigation-rule>
    <navigation-rule>
        <description>Navegacao da pagina</description>
        <from-view-id>/tamanhoArmarioCons.jsp</from-view-id>
        <navigation-case>
            <from-outcome>editar</from-outcome>
            <to-view-id>/tamanhoArmarioForm.jsp</to-view-id>
            <redirect />
        </navigation-case>
        <navigation-case>
            <from-outcome>consultar</from-outcome>
            <to-view-id>/tamanhoArmarioCons.jsp</to-view-id>
            <redirect />
        </navigation-case>
    </navigation-rule>

    <navigation-rule>
        <description>Navegacoes dos Sorteios</description>
        <from-view-id>/sorteioCons.jsp</from-view-id>
        <navigation-case>
            <from-outcome>novo</from-outcome>
            <to-view-id>/sorteioForm.jsp</to-view-id>
            <redirect/>
        </navigation-case>
        <navigation-case>
            <from-outcome>configurar</from-outcome>
            <to-view-id>/sorteioConf.jsp</to-view-id>
            <redirect/>
        </navigation-case>
    </navigation-rule>

    <navigation-rule>
        <description>Navegacoes dos Sorteios</description>
        <from-view-id>/sorteioForm.jsp</from-view-id>
        <navigation-case>
            <from-outcome>consultar</from-outcome>
            <to-view-id>/sorteioCons.jsp</to-view-id>
            <redirect/>
        </navigation-case>
        <navigation-case>
            <from-outcome>configurar</from-outcome>
            <to-view-id>/sorteioConf.jsp</to-view-id>
            <redirect/>
        </navigation-case>
    </navigation-rule>

    <navigation-rule>
        <description>Navegacoes dos Sorteios</description>
        <from-view-id>/sorteioConf.jsp</from-view-id>
        <navigation-case>
            <from-outcome>consultar</from-outcome>
            <to-view-id>/sorteioCons.jsp</to-view-id>
            <redirect/>
        </navigation-case>
        <navigation-case>
            <from-outcome>novo</from-outcome>
            <to-view-id>/sorteioForm.jsp</to-view-id>
            <redirect/>
        </navigation-case>
    </navigation-rule>
    
    <navigation-rule>
        <description>Navegacao da pagina</description>
        <from-view-id>/cliente.jsp</from-view-id>
        <navigation-case>
            <from-outcome>tela3</from-outcome>
            <to-view-id>/tela3.jsp</to-view-id>
        </navigation-case>
        <navigation-case>
            <from-outcome>novo</from-outcome>
            <to-view-id>/tela2.jsp</to-view-id>
            <redirect />
        </navigation-case>
        <navigation-case>
            <from-outcome>contrato</from-outcome>
            <to-view-id>/negociacaoContrato.jsp</to-view-id>
            <redirect />
        </navigation-case>
        <navigation-case>
            <from-outcome>contratoNovo</from-outcome>
            <to-view-id>/negociacaoContrato.jsp</to-view-id>
            <redirect />
        </navigation-case>
        <navigation-case>
            <from-outcome>questionario</from-outcome>
            <to-view-id>/tela4.jsp</to-view-id>
            <redirect />
        </navigation-case>
        <navigation-case>
            <from-outcome>tela8</from-outcome>
            <to-view-id>/tela8.jsp</to-view-id>
            <redirect />
        </navigation-case>
        <navigation-case>
            <from-outcome>vendaAvulsa</from-outcome>
            <to-view-id>/vendaAvulsaForm.jsp</to-view-id>
            <redirect />
        </navigation-case>
        <navigation-case>
            <from-outcome>aulaAvulsa</from-outcome>
            <to-view-id>/aulaAvulsaForm.jsp</to-view-id>
            <redirect />
        </navigation-case>
        <navigation-case>
            <from-outcome>diaria</from-outcome>
            <to-view-id>/diariaForm.jsp</to-view-id>
            <redirect />
        </navigation-case>
        <navigation-case>
            <from-outcome>cliente</from-outcome>
            <to-view-id>/clienteForm.jsp</to-view-id>
            <redirect />
        </navigation-case>
        <navigation-case>
            <from-outcome>editarOrcamentoCliente</from-outcome>
            <to-view-id>/realizarOrcamento.jsp</to-view-id>
            <redirect />
        </navigation-case>
    </navigation-rule>
    <navigation-rule>
        <description>Navegacao da pagina</description>
        <from-view-id>/consultarTurmaForm.jsp</from-view-id>
        <navigation-case>
            <from-outcome>chamada</from-outcome>
            <to-view-id>/relatorio/listaChamada.jsp</to-view-id>
            <redirect />
        </navigation-case>
    </navigation-rule>
      <navigation-rule>
        <description>Navegacao da pagina</description>
        <from-view-id>/relatorio/listaChamada.jsp</from-view-id>
        <navigation-case>
            <from-outcome>consultaturma</from-outcome>
            <to-view-id>/consultarTurmaForm.jsp</to-view-id>
            <redirect />
        </navigation-case>
    </navigation-rule>
    <navigation-rule>
        <description>Navegacao da pagina</description>
        <from-view-id>/brindeCons.jsp</from-view-id>
        <navigation-case>
            <from-outcome>editar</from-outcome>
            <to-view-id>/brindeForm.jsp</to-view-id>
            <redirect />
        </navigation-case>
        <navigation-case>
            <from-outcome>consultar</from-outcome>
            <to-view-id>/brindeCons.jsp</to-view-id>
            <redirect />
        </navigation-case>
    </navigation-rule>
    <navigation-rule>
        <description>Navegacao da pagina</description>
        <from-view-id>/brindeForm.jsp</from-view-id>
        <navigation-case>
            <from-outcome>editar</from-outcome>
            <to-view-id>/brindeForm.jsp</to-view-id>
            <redirect />
        </navigation-case>
        <navigation-case>
            <from-outcome>consultar</from-outcome>
            <to-view-id>/brindeCons.jsp</to-view-id>
            <redirect />
        </navigation-case>
    </navigation-rule>
    <navigation-rule>
        <description>Navegacao da pagina</description>
        <from-view-id>/relatorio/historicoPontosRel.jsp</from-view-id>
        <navigation-case>
            <from-outcome>editar</from-outcome>
            <to-view-id>/relatorio/historicoPontosForm.jsp</to-view-id>
            <redirect />
        </navigation-case>
        <navigation-case>
            <from-outcome>consultar</from-outcome>
            <to-view-id>/relatorio/historicoPontosRel.jsp</to-view-id>
            <redirect />
        </navigation-case>
    </navigation-rule>
    <navigation-rule>
        <description>Navegacao da pagina</description>
        <from-view-id>/relatorio/historicoPontosForm.jsp</from-view-id>
        <navigation-case>
            <from-outcome>editar</from-outcome>
            <to-view-id>/relatorio/historicoPontosForm.jsp</to-view-id>
            <redirect />
        </navigation-case>
        <navigation-case>
            <from-outcome>consultar</from-outcome>
            <to-view-id>/relatorio/historicoPontosRel.jsp</to-view-id>
            <redirect />
        </navigation-case>
    </navigation-rule>
    <navigation-rule>
        <description>Navegacao da pagina</description>
        <from-view-id>/lancarBrindeClienteCons.jsp</from-view-id>
        <navigation-case>
            <from-outcome>lancar</from-outcome>
            <to-view-id>/lancarBrindeClienteForm.jsp</to-view-id>
            <redirect />
        </navigation-case>
    </navigation-rule>

    <navigation-rule>
        <description>Navegacao da pagina</description>
        <from-view-id>/lancarBrindeClienteForm.jsp</from-view-id>
        <navigation-case>
            <from-outcome>consultar</from-outcome>
            <to-view-id>/lancarBrindeClienteCons.jsp</to-view-id>
            <redirect />
        </navigation-case>
    </navigation-rule>

    <navigation-rule>
        <description>Navegacao da pagina</description>
        <from-view-id>/gympassCons.jsp</from-view-id>
        <navigation-case>
            <from-outcome>editar</from-outcome>
            <to-view-id>/gympassForm.jsp</to-view-id>
            <redirect />
        </navigation-case>
        <navigation-case>
            <from-outcome>consultar</from-outcome>
            <to-view-id>/gympassCons.jsp</to-view-id>
            <redirect />
        </navigation-case>
    </navigation-rule>
    <navigation-rule>
        <description>Navegacao da pagina</description>
        <from-view-id>/gympassForm.jsp</from-view-id>
        <navigation-case>
            <from-outcome>editar</from-outcome>
            <to-view-id>/gympassForm.jsp</to-view-id>
            <redirect />
        </navigation-case>
        <navigation-case>
            <from-outcome>consultar</from-outcome>
            <to-view-id>/gympassCons.jsp</to-view-id>
            <redirect />
        </navigation-case>
    </navigation-rule>

    <navigation-rule>
        <description>Navegacao do recurso tipo de plano</description>
        <from-view-id>/planoTipoForm.jsp</from-view-id>
        <navigation-case>
            <from-outcome>editar</from-outcome>
            <to-view-id>/planoTipoForm.jsp</to-view-id>
            <redirect />
        </navigation-case>
        <navigation-case>
            <from-outcome>consultar</from-outcome>
            <to-view-id>/planoTipoCons.jsp</to-view-id>
            <redirect />
        </navigation-case>
    </navigation-rule>
    <navigation-rule>
        <description>Navegacao do recurso tipo de plano</description>
        <from-view-id>/planoTipoCons.jsp</from-view-id>
        <navigation-case>
            <from-outcome>editar</from-outcome>
            <to-view-id>/planoTipoForm.jsp</to-view-id>
            <redirect />
        </navigation-case>
        <navigation-case>
            <from-outcome>consultar</from-outcome>
            <to-view-id>/planoTipoCons.jsp</to-view-id>
            <redirect />
        </navigation-case>
    </navigation-rule>

    <navigation-rule>
        <description>Navegacao da pagina</description>
        <from-view-id>/relatorioTokensOperacoes.jsp</from-view-id>
        <navigation-case>
            <from-outcome>consultar</from-outcome>
            <to-view-id>/relatorioTokensOperacoes.jsp</to-view-id>
            <redirect />
        </navigation-case>
    </navigation-rule>

    <navigation-rule>
        <description>Navegacao da pagina</description>
        <from-view-id>/localImpressaoCons.jsp</from-view-id>
        <navigation-case>
            <from-outcome>editar</from-outcome>
            <to-view-id>/localImpressaoForm.jsp</to-view-id>
        </navigation-case>
        <navigation-case>
            <from-outcome>consultar</from-outcome>
            <to-view-id>/localImpressaoCons.jsp</to-view-id>
        </navigation-case>
    </navigation-rule>
    <navigation-rule>
        <description>Navegacao da pagina</description>
        <from-view-id>/localImpressaoForm.jsp</from-view-id>
        <navigation-case>
            <from-outcome>editar</from-outcome>
            <to-view-id>/localImpressaoForm.jsp</to-view-id>
        </navigation-case>
        <navigation-case>
            <from-outcome>consultar</from-outcome>
            <to-view-id>/localImpressaoCons.jsp</to-view-id>
        </navigation-case>
    </navigation-rule>
</faces-config>
