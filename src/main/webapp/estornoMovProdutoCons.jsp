<%@page contentType="text/html"%>
<%@page pageEncoding="ISO-8859-1"%>
<head><%@include file="/includes/include_import_minifiles.jsp" %></head>
<link href="./css/otimize.css" rel="stylesheet" type="text/css">
<script type="text/javascript" language="javascript" src="hoverform.js"></script>
<%@taglib prefix="f" uri="http://java.sun.com/jsf/core"%>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html"%>
<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax"%>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich"%>

<f:loadBundle var="msg_bt" basename="propriedades.Botoes" />
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao" />
<f:loadBundle var="msg" basename="propriedades.Mensagens" />

<f:view>
    <jsp:include page="includes/include_carregando_ripple.jsp" flush="true"/>
    <title><h:outputText
            value="#{msg_aplic.prt_EstornoMovProduto_tituloForm}" /></title>

    <h:panelGrid columns="1" width="100%" cellpadding="0" cellspacing="0">
        <f:facet name="header">
            <jsp:include page="topoReduzido.jsp" />
        </f:facet>
        <h:form id="form">
            <h:panelGrid columns="1" width="100%">
                <h:panelGrid columns="1"
                             style="height:25px; background-image:url('./imagens/fundoBarraTopo.png');background-repeat: repeat-x;"
                             columnClasses="colunaCentralizada" width="100%">
                    <h:outputText styleClass="tituloFormulario"
                                  value="#{msg_aplic.prt_EstornoMovProduto_tituloForm}" />
                </h:panelGrid>

                <h:panelGrid columns="4" footerClass="colunaCentralizada"
                             width="100%">
                    <h:outputText styleClass="tituloCampos"
                                  value="#{msg.msg_consultar_por}" />
                    <h:selectOneMenu styleClass="campos" id="consulta" required="true"
                                     value="#{EstornoMovProdutoControle.controleConsulta.campoConsulta}">
                        <f:selectItems value="#{EstornoMovProdutoControle.tipoConsultaCombo}" />
                    </h:selectOneMenu>
                    <h:inputText id="valorConsulta" styleClass="campos"
                                 value="#{EstornoMovProdutoControle.controleConsulta.valorConsulta}" />
                    <h:commandButton id="consultar" styleClass="botoes"
                                     value="#{msg_bt.btn_consultar}"
                                     action="#{EstornoMovProdutoControle.consultar}"
                                     image="./imagens/botaoConsultar.png"
                                     title="#{msg.msg_consultar_dados}" accesskey="2" />
                </h:panelGrid>

                <rich:dataTable id="items" width="100%" headerClass="consulta"
                                rowClasses="linhaImpar, linhaPar" columnClasses="colunaAlinhamento"
                                value="#{EstornoMovProdutoControle.listaConsulta}"
                                rendered="#{EstornoMovProdutoControle.apresentarResultadoConsulta}"
                                rows="10" var="reciboPagamentoVO">

                    <rich:column>
                        <f:facet name="header">
                            <h:outputText value="#{msg_aplic.prt_EstornoRecibo_codigo}" />
                        </f:facet>
                        <h:commandLink action="#{EstornoMovProdutoControle.selecionarRecibo}"
                                       id="codigo" value="#{reciboPagamentoVO.codigo}" />
                    </rich:column>
                    <rich:column>
                        <f:facet name="header">
                            <h:outputText value="#{msg_aplic.prt_EstornoRecibo_nomePessoa}" />
                        </f:facet>
                        <h:commandLink action="#{EstornoMovProdutoControle.selecionarRecibo}"
                                       id="nomePessoa" value="#{reciboPagamentoVO.nomePessoaPagador}" />
                    </rich:column>
                    <rich:column>
                        <f:facet name="header">
                            <h:outputText
                                value="#{msg_aplic.prt_EstornoRecibo_responsavelLancamento}" />
                        </f:facet>
                        <h:commandLink action="#{EstornoMovProdutoControle.selecionarRecibo}"
                                       id="responsavelLancamento"
                                       value="#{reciboPagamentoVO.responsavelLancamento.nome}" />
                    </rich:column>
                    <rich:column>
                        <f:facet name="header">
                            <h:outputText value="#{msg_aplic.prt_EstornoRecibo_valorRecibo}" />
                        </f:facet>
                        <h:commandLink action="#{EstornoMovProdutoControle.selecionarRecibo}"
                                       id="valorTotal">
                            <h:outputText value="#{reciboPagamentoVO.valorTotal}">
                                <f:converter converterId="FormatadorNumerico" />
                            </h:outputText>
                        </h:commandLink>
                    </rich:column>
                    <rich:column>
                        <f:facet name="header">
                            <h:outputText value="#{msg_aplic.prt_EstornoRecibo_data}" />
                        </f:facet>
                        <h:commandLink action="#{EstornoMovProdutoControle.selecionarRecibo}"
                                       id="data" value="#{reciboPagamentoVO.data_Apresentar}" />
                    </rich:column>
                    <rich:column>
                        <f:facet name="header">
                            <h:outputText value="#{msg_bt.btn_opcoes}" />
                        </f:facet>
                        <h:commandButton
                            action="#{EstornoMovProdutoControle.selecionarRecibo}"
                            value="#{msg_bt.btn_editar}" image="./imagens/botaoEditar.png"
                            title="#{msg.msg_editar_dados}" styleClass="botoes" />
                    </rich:column>
                </rich:dataTable>
                <rich:datascroller align="center" for="form:items"
                                   id="scResultadoConsulta"
                                   rendered="#{EstornoMovProdutoControle.apresentarResultadoConsulta}" />

                <h:panelGrid columns="1" width="100%" styleClass="tabMensagens">
                    <h:panelGrid columns="3" width="100%" styleClass="tabMensagens">
                        <h:panelGrid columns="1" width="100%">
                            <h:outputText value=" " />
                        </h:panelGrid>
                        <h:commandButton rendered="#{EstornoMovProdutoControle.sucesso}"
                                         image="./imagens/sucesso.png" />
                        <h:commandButton rendered="#{EstornoMovProdutoControle.erro}"
                                         image="./imagens/erro.png" />
                        <h:panelGrid columns="1" width="100%">
                            <h:outputText styleClass="mensagem"
                                          value="#{EstornoMovProdutoControle.mensagem}" />
                            <h:outputText styleClass="mensagemDetalhada"
                                          value="#{EstornoMovProdutoControle.mensagemDetalhada}" />
                        </h:panelGrid>
                    </h:panelGrid>
                </h:panelGrid>
            </h:panelGrid>
        </h:form>

    </h:panelGrid>
</f:view>
<script>
    document.getElementById("form:valorConsulta").focus();
</script>