<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<%@page contentType="text/html" %>
<%@page pageEncoding="ISO-8859-1" %>
<head>
    <%@include file="/includes/include_import_minifiles.jsp" %>
    <script language="javascript" src="script/telaCliente1.3.js" type="text/javascript"></script>
    <script language="javascript" src="script/required.js" type="text/javascript"></script>
    <script type="text/javascript" language="javascript" src="bootstrap/jquery.js"></script>
    <script type="text/javascript" src="script/tooltipster/jquery.tooltipster.min.js"></script>
    <style>
        .linhainformacoesaluno td {
            background-color: transparent !important;
            padding-left: 3px;
        }
    </style>
</head>
<link href="./css/otimize.css" rel="stylesheet" type="text/css">
<link href="beta/css/pure-min.css" type="text/css" rel="stylesheet"/>
<link href="beta/css/bootstrap-tabs.css" type="text/css" rel="stylesheet"/>
<link href="beta/css/pure-ext.css" type="text/css" rel="stylesheet"/>
<link href="beta/css/font-awesome_2.0.min.css" type="text/css" rel="stylesheet"/>
<script type="text/javascript" language="javascript" src="hoverform.js"></script>
<script type="text/javascript" src="script/validador-cpf.js"></script>
<script src="beta/js/DT_bootstrap.js" type="text/javascript"></script>
<script src="${root}/script/inclusao_aluno_v1.js" type="text/javascript"></script>
<%@taglib prefix="f" uri="http://java.sun.com/jsf/core" %>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html" %>
<%@taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax" %>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich" %>

<jsp:include page="include_head.jsp" flush="true"/>


<f:loadBundle var="msg_bt" basename="propriedades.Botoes"/>
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao"/>
<f:loadBundle var="msg" basename="propriedades.Mensagens"/>
<f:loadBundle var="CElabels" basename="propriedades.CElabels"/>
<f:loadBundle var="Mensagens" basename="propriedades.Mensagens"/>
<f:view>
    <jsp:include page="includes/include_carregando_ripple.jsp" flush="true"/>
    <a4j:keepAlive beanName="InclusaoVendaRapidaControle"/>
    <title>
        <h:outputText value="Clientes"/>
    </title>
    <h:form id="form" onkeypress="return event.keyCode != 13; " style="overflow: hidden !important;">
        <html>
        <body class="zw_ui_not_hidden">
        <h:panelGroup layout="block" styleClass="bgtop topoZW">
            <jsp:include page="include_topo_novo.jsp" flush="true"/>
            <jsp:include page="include_menu_zw_flat.jsp" flush="true"/>
            <rich:jQuery selector=".item8" query="addClass('menuItemAtual')"/>
            <link href="${root}/css/telaCliente.css" rel="stylesheet" type="text/css"/>
            <link href="${root}/css/required.css" rel="stylesheet" type="text/css"/>
        </h:panelGroup>

        <table width="100%" border="0" cellpadding="0" cellspacing="0">


            <tr>
                <td align="left" valign="top" class="bglateral">
                    <table width="100%" border="0" cellpadding="0" cellspacing="0">
                        <tr>
                            <td width="180" align="left" valign="top" class="bglateraltop"
                                style="padding: 0 !important;">
                                <jsp:include page="include_box_menulateral.jsp" flush="true"/>
                            </td>
                            <td align="center" valign="top">
                                <jsp:include page="/includes/cliente/include_inclusaoAlunoVenda.jsp" flush="true"/>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
            <tr>
                <td>
                    <jsp:include page="include_rodape_flat.jsp" flush="true"/>
                </td>
            </tr>
        </table>

        <a4j:jsFunction name="ativarVariaveisVendaRapida" status="false"
                        action="#{InclusaoVendaRapidaControle.atualizarMsg}"
                        reRender="form:panelpollMsgCarregando"/>
        <a4j:jsFunction name="ativarPoll" status="false"
                        action="#{InclusaoVendaRapidaControle.ativarPoll}"
                        reRender="form:panelpollMsgCarregando"/>
        <a4j:jsFunction name="desativarPoll" status="false"
                        action="#{InclusaoVendaRapidaControle.desativarPoll}"
                        reRender="form:panelpollMsgCarregando"/>
        <h:panelGroup layout="block" style="display: none" id="panelpollMsgCarregando">
            <a4j:poll id="pollMsgCarregando"
                      status="false" interval="200"
                      oncomplete="atualizarMensagem()"
                      enabled="#{InclusaoVendaRapidaControle.pollAtivo}"
                      reRender="divMsgCarregando"/>
        </h:panelGroup>

        </body>
        </html>

        <script>
            // carregarTooltipster();
            fecharMenu();
        </script>
    </h:form>


    <rich:modalPanel id="panelCEP" styleClass="novaModal vAlignTop" autosized="true" shadowOpacity="true" width="500"
                     height="450">
        <f:facet name="header">
            <h:panelGroup>
                <h:outputText value="#{msg_aplic.prt_CEP_tituloConsulta}"/>
            </h:panelGroup>
        </f:facet>
        <f:facet name="controls">
            <h:panelGroup>
                <h:outputText
                        styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign btnSelecionarModalidade"
                        id="hidelinkCEP"/>
                <rich:componentControl for="panelCEP" attachTo="hidelinkCEP" operation="hide" event="onclick"/>
            </h:panelGroup>
        </f:facet>
        <a4j:form id="formConsultarCEP" style="overflow: hidden" ajaxSubmit="true">
            <jsp:include page="includes/include_persiste_modulo.jsp"/>
            <h:panelGrid columns="1" width="100%" columnClasses="colunaCentralizada">
                <h:panelGrid columns="2" columnClasses="classEsquerda" width="100%">
                    <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_CEP_estadoC_maiusculo}"/>
                    <h:panelGroup layout="block" styleClass="cb-container" style="height: 40px;">
                        <h:selectOneMenu id="estadoCEP" styleClass="campos"
                                         value="#{InclusaoVendaRapidaControle.cepControle.cepVO.ufSigla}">
                            <f:selectItems value="#{InclusaoVendaRapidaControle.listaSelectItemRgUfPessoa}"/>
                        </h:selectOneMenu>
                    </h:panelGroup>
                    <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_CEP_cidadeC_maiusculo}"/>
                    <h:inputText id="cidadeCEP" size="20" styleClass="campos"
                                 value="#{InclusaoVendaRapidaControle.cepControle.cepVO.cidadeDescricao}"/>
                    <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_CEP_bairro_maiusculo}"/>
                    <h:inputText id="bairroCEP" size="20" styleClass="campos"
                                 value="#{InclusaoVendaRapidaControle.cepControle.cepVO.bairroDescricao}"/>
                    <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_CEP_logradouro_maiusculo}"/>
                    <h:inputText id="logradouroCEP" size="20" styleClass="campos"
                                 value="#{InclusaoVendaRapidaControle.cepControle.cepVO.enderecoLogradouro}"/>
                </h:panelGrid>
                <h:panelGrid columns="1">

                    <h:panelGroup>
                        <h:outputText styleClass="textsmall"
                                      value="Informe o nome ou parte do seu logradouro, rua ou avenida. Não Inclua o tipo da via nem o número da sua casa."/>
                    </h:panelGroup>
                </h:panelGrid>
                <h:panelGroup>
                    <a4j:commandLink id="btnConsultarCEP"
                                     reRender="formConsultarCEP"

                                     action="#{InclusaoVendaRapidaControle.cepControle.consultarCEPDetalhe}"
                                     styleClass="botaoPrimario texto-size-16"
                                     value="#{msg_bt.btn_consultar}" title="#{msg.msg_consultar_dados}"/>
                    <rich:spacer style='display:block' height="35"/>
                </h:panelGroup>
                <h:panelGroup layout="block">
                    <rich:dataTable id="resultadoConsultaCEP" width="100%" styleClass="tabelaSimplesCustom"
                                    rendered="#{not empty InclusaoVendaRapidaControle.cepControle.listaConsultaCep}"
                                    value="#{InclusaoVendaRapidaControle.cepControle.listaConsultaCep}" rows="4"
                                    var="cep">
                        <rich:column>
                            <f:facet name="header">
                                <h:outputText style="float: left;"
                                              styleClass="texto-font texto-bold texto-cor-cinza texto-size-14"
                                              value="#{msg_aplic.prt_CEP_titulo}"/>
                            </f:facet>
                            <h:panelGroup>
                                <a4j:commandLink action="#{InclusaoVendaRapidaControle.selecionarCepSimplificado}"
                                                 focus="CEP"
                                                 styleClass="texto-font texto-cor-cinza texto-size-14"
                                                 reRender="painelEndereco"
                                                 oncomplete="Richfaces.hideModalPanel('panelCEP')"
                                                 value="#{cep.enderecoCep}"/>
                            </h:panelGroup>
                        </rich:column>
                        <rich:column>
                            <f:facet name="header">
                                <h:outputText style="float: left;"
                                              styleClass="texto-font texto-bold texto-cor-cinza texto-size-14"
                                              value="#{msg_aplic.prt_CEP_cidadeC}"/>
                            </f:facet>
                            <h:panelGroup>
                                <a4j:commandLink action="#{InclusaoVendaRapidaControle.selecionarCepSimplificado}"
                                                 focus="CEP"
                                                 styleClass="texto-font texto-cor-cinza texto-size-14"
                                                 reRender="painelEndereco"
                                                 oncomplete="Richfaces.hideModalPanel('panelCEP')"
                                                 value="#{cep.cidadeDescricao}"/>
                            </h:panelGroup>
                        </rich:column>
                        <rich:column>
                            <f:facet name="header">
                                <h:outputText style="float: left;"
                                              styleClass="texto-font texto-bold texto-cor-cinza texto-size-14"
                                              value="#{msg_aplic.prt_CEP_bairroC}"/>
                            </f:facet>
                            <a4j:commandLink action="#{InclusaoVendaRapidaControle.selecionarCepSimplificado}"
                                             focus="CEP"
                                             styleClass="texto-font texto-cor-cinza texto-size-14"
                                             reRender="painelEndereco"
                                             oncomplete="Richfaces.hideModalPanel('panelCEP')"
                                             value="#{cep.bairroDescricao}"/>
                        </rich:column>
                        <rich:column>
                            <f:facet name="header">
                                <h:outputText styleClass="texto-font texto-bold texto-cor-cinza texto-size-14"
                                              value="#{msg_aplic.prt_CEP_logradouroC}"/>
                            </f:facet>
                            <a4j:commandLink action="#{InclusaoVendaRapidaControle.selecionarCepSimplificado}"
                                             focus="CEP"
                                             styleClass="texto-font texto-cor-cinza texto-size-14"
                                             reRender="painelEndereco"
                                             oncomplete="Richfaces.hideModalPanel('panelCEP')"
                                             value="#{cep.enderecoLogradouro}"/>
                        </rich:column>
                    </rich:dataTable>
                    <rich:datascroller id="scResultadoCEP" align="center" style="margin-top: 10px"
                                       styleClass="scrollPureCustom" renderIfSinglePage="false"
                                       for="formConsultarCEP:resultadoConsultaCEP" maxPages="10"/>
                </h:panelGroup>
                <h:panelGrid id="mensagemConsultaCEP" columns="1" width="100%" styleClass="tabMensagens">
                    <h:panelGrid columns="1" width="100%">
                        <h:outputText styleClass="mensagem"
                                      value="#{InclusaoVendaRapidaControle.cepControle.mensagem}"/>
                        <h:outputText styleClass="mensagemDetalhada"
                                      value="#{InclusaoVendaRapidaControle.cepControle.mensagemDetalhada}"/>
                    </h:panelGrid>
                </h:panelGrid>
            </h:panelGrid>
        </a4j:form>
    </rich:modalPanel>

    <rich:modalPanel id="panelCEPComercial" styleClass="novaModal vAlignTop" autosized="true" shadowOpacity="true"
                     width="500" height="450">
        <f:facet name="header">
            <h:panelGroup>
                <h:outputText value="#{msg_aplic.prt_CEP_tituloConsulta}"/>
            </h:panelGroup>
        </f:facet>
        <f:facet name="controls">
            <h:panelGroup>
                <h:outputText
                        styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign btnSelecionarModalidade"
                        id="hidelinkCEPComercial"/>
                <rich:componentControl for="panelCEPComercial" attachTo="hidelinkCEPComercial" operation="hide"
                                       event="onclick"/>
            </h:panelGroup>
        </f:facet>
        <a4j:form id="formConsultarCEPComercial" style="overflow: hidden" ajaxSubmit="true">
            <jsp:include page="includes/include_persiste_modulo.jsp"/>
            <h:panelGrid columns="1" width="100%" columnClasses="colunaCentralizada">
                <h:panelGrid columns="2" columnClasses="classEsquerda" width="100%">
                    <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_CEP_estadoC_maiusculo}"/>
                    <h:panelGroup layout="block" styleClass="cb-container" style="height: 40px;">
                        <h:selectOneMenu id="estadoCEPComercial" styleClass="campos"
                                         value="#{InclusaoVendaRapidaControle.cepControle.cepVO.ufSigla}">
                            <f:selectItems value="#{InclusaoVendaRapidaControle.listaSelectItemRgUfPessoa}"/>
                        </h:selectOneMenu>
                    </h:panelGroup>
                    <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_CEP_cidadeC_maiusculo}"/>
                    <h:inputText id="cidadeCEPComercial" size="20" styleClass="campos"
                                 value="#{InclusaoVendaRapidaControle.cepControle.cepVO.cidadeDescricao}"/>
                    <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_CEP_bairro_maiusculo}"/>
                    <h:inputText id="bairroCEPComercial" size="20" styleClass="campos"
                                 value="#{InclusaoVendaRapidaControle.cepControle.cepVO.bairroDescricao}"/>
                    <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_CEP_logradouro_maiusculo}"/>
                    <h:inputText id="logradouroCEPComercial" size="20" styleClass="campos"
                                 value="#{InclusaoVendaRapidaControle.cepControle.cepVO.enderecoLogradouro}"/>
                </h:panelGrid>
                <h:panelGrid columns="1">

                    <h:panelGroup>
                        <h:outputText styleClass="textsmall"
                                      value="Informe o nome ou parte do seu logradouro, rua ou avenida. Não Inclua o tipo da via nem o número da sua casa."/>
                    </h:panelGroup>
                </h:panelGrid>
                <h:panelGroup>
                    <a4j:commandLink id="btnConsultarCEPComercial"
                                     reRender="formConsultarCEPComercial"
                                     action="#{InclusaoVendaRapidaControle.cepControle.consultarCEPDetalhe}"
                                     styleClass="botaoPrimario texto-size-16"
                                     value="#{msg_bt.btn_consultar}" title="#{msg.msg_consultar_dados}"/>
                    <rich:spacer style='display:block' height="35"/>
                </h:panelGroup>
                <h:panelGroup layout="block">
                    <rich:dataTable id="resultadoConsultaCEPComercial" width="100%" styleClass="tabelaSimplesCustom"
                                    rendered="#{not empty InclusaoVendaRapidaControle.cepControle.listaConsultaCep}"
                                    value="#{InclusaoVendaRapidaControle.cepControle.listaConsultaCep}" rows="4"
                                    var="cep">
                        <rich:column>
                            <f:facet name="header">
                                <h:outputText style="float: left;"
                                              styleClass="texto-font texto-bold texto-cor-cinza texto-size-14"
                                              value="#{msg_aplic.prt_CEP_titulo}"/>
                            </f:facet>
                            <h:panelGroup>
                                <a4j:commandLink action="#{InclusaoVendaRapidaControle.selecionarCepComercial}"
                                                 focus="CEP"
                                                 styleClass="texto-font texto-cor-cinza texto-size-14"
                                                 reRender="painelEnderecoComercial"
                                                 oncomplete="Richfaces.hideModalPanel('panelCEPComercial')"
                                                 value="#{cep.enderecoCep}"/>
                            </h:panelGroup>
                        </rich:column>
                        <rich:column>
                            <f:facet name="header">
                                <h:outputText style="float: left;"
                                              styleClass="texto-font texto-bold texto-cor-cinza texto-size-14"
                                              value="#{msg_aplic.prt_CEP_cidadeC}"/>
                            </f:facet>
                            <h:panelGroup>
                                <a4j:commandLink action="#{InclusaoVendaRapidaControle.selecionarCepComercial}"
                                                 focus="CEP"
                                                 styleClass="texto-font texto-cor-cinza texto-size-14"
                                                 reRender="painelEnderecoComercial"
                                                 oncomplete="Richfaces.hideModalPanel('panelCEPComercial')"
                                                 value="#{cep.cidadeDescricao}"/>
                            </h:panelGroup>
                        </rich:column>
                        <rich:column>
                            <f:facet name="header">
                                <h:outputText style="float: left;"
                                              styleClass="texto-font texto-bold texto-cor-cinza texto-size-14"
                                              value="#{msg_aplic.prt_CEP_bairroC}"/>
                            </f:facet>
                            <a4j:commandLink action="#{InclusaoVendaRapidaControle.selecionarCepComercial}" focus="CEP"
                                             styleClass="texto-font texto-cor-cinza texto-size-14"
                                             reRender="painelEnderecoComercial"
                                             oncomplete="Richfaces.hideModalPanel('panelCEPComercial')"
                                             value="#{cep.bairroDescricao}"/>
                        </rich:column>
                        <rich:column>
                            <f:facet name="header">
                                <h:outputText styleClass="texto-font texto-bold texto-cor-cinza texto-size-14"
                                              value="#{msg_aplic.prt_CEP_logradouroC}"/>
                            </f:facet>
                            <a4j:commandLink action="#{InclusaoVendaRapidaControle.selecionarCepComercial}" focus="CEP"
                                             styleClass="texto-font texto-cor-cinza texto-size-14"
                                             reRender="painelEnderecoComercial"
                                             oncomplete="Richfaces.hideModalPanel('panelCEPComercial')"
                                             value="#{cep.enderecoLogradouro}"/>
                        </rich:column>
                    </rich:dataTable>
                    <rich:datascroller id="scResultadoCEPComercial" align="center" style="margin-top: 10px"
                                       styleClass="scrollPureCustom" renderIfSinglePage="false"
                                       for="formConsultarCEPComercial:resultadoConsultaCEPComercial" maxPages="10"/>
                </h:panelGroup>
                <h:panelGrid id="mensagemConsultaCEPComercial" columns="1" width="100%" styleClass="tabMensagens">
                    <h:panelGrid columns="1" width="100%">
                        <h:outputText styleClass="mensagem"
                                      value="#{InclusaoVendaRapidaControle.cepControle.mensagem}"/>
                        <h:outputText styleClass="mensagemDetalhada"
                                      value="#{InclusaoVendaRapidaControle.cepControle.mensagemDetalhada}"/>
                    </h:panelGrid>
                </h:panelGrid>
            </h:panelGrid>
        </a4j:form>
    </rich:modalPanel>

    <rich:modalPanel id="modalColaboradoresPorCpf"
                     styleClass="novaModal"
                     autosized="true"
                     shadowOpacity="true"
                     width="720"
                     height="300">
        <style type="text/css">
            .coluna-colaborador-foto {
                width: 90px;
            }

            .linhainformacoesaluno .info-item {
                margin-left: 3px;
            }
        </style>
        <f:facet name="header">
            <h:panelGroup>
                <h:outputText value="Colaboradores"/>
            </h:panelGroup>
        </f:facet>
        <h:form>
            <rich:dataTable value="#{InclusaoVendaRapidaControle.colaboradoresPersonal}"
                            width="700px"
                            var="colaborador"
                            styleClass="pure-table pure-table-striped pure-table-noCellPadding pure-table-links th3fix"
                            columnClasses="centralizado,esquerda,centralizado,centralizado,esquerda">
                <rich:column>
                    <f:facet name="header">
                        <h:outputText value="Colaborador"
                                      styleClass="text"
                                      style="font-weight: bold;"/>
                    </f:facet>
                    <h:panelGrid columns="2"
                                 width="100%"
                                 columnClasses="coluna-colaborador-foto"
                                 styleClass="linhainformacoesaluno">
                        <a4j:mediaOutput element="img"
                                         align="left"
                                         style="left:0px;border:none;width: 90px; height: auto;margin: 0px;"
                                         title="#{colaborador.pessoa.nome}"
                                         createContent="#{InclusaoVendaRapidaControle.paintFotoModal}"
                                         styleClass="shadow"
                                         value="#{ImagemData}" mimeType="image/jpeg">
                            <f:param value="#{SuperControle.timeStamp}" name="time"/>
                            <f:param name="largura" value="70"/>
                            <f:param name="altura" value="90"/>
                            <f:param name="pessoa" value="#{colaborador.pessoa.codigo}"></f:param>
                        </a4j:mediaOutput>

                        <h:panelGrid columns="1"
                                     width="100%"
                                     styleClass="linhainformacoesaluno">

                            <h:panelGroup styleClass="info-item">
                                <h:outputText value="Código: "
                                              styleClass="text"
                                              style="font-weight: bold; "/>
                                <h:outputText value="#{colaborador.codigo}"
                                              styleClass="text"/>
                            </h:panelGroup>
                            <h:panelGroup styleClass="info-item">
                                <h:outputText value="Nome: "
                                              styleClass="text"
                                              style="font-weight: bold;"/>
                                <h:outputText value="#{colaborador.pessoa.nome}"
                                              styleClass="text"/>
                            </h:panelGroup>
                            <h:panelGroup styleClass="info-item">
                                <h:outputText value="CPF: "
                                              styleClass="text"
                                              style="font-weight: bold;"/>
                                <h:outputText value="#{colaborador.pessoa.cfp}" styleClass="text"/>

                            </h:panelGroup>
                            <h:panelGroup styleClass="info-item">
                                <h:outputText value="Empresa: "
                                              styleClass="text"
                                              style="font-weight: bold;"/>
                                <h:outputText value="#{colaborador.empresa.nome}" styleClass="text"/>
                            </h:panelGroup>
                        </h:panelGrid>
                    </h:panelGrid>
                </rich:column>
                <rich:column style="padding: 0px 5px 0px 5px" width="200px">
                    <f:facet name="header">
                        <h:outputText value="Tipo"
                                      styleClass="text"
                                      style="font-weight: bold;"/>
                    </f:facet>
                    <h:outputText value="#{colaborador.tipoColaborador_Apresentar}"
                                  styleClass="text"/>
                </rich:column>
                <rich:column style="padding: 0px 9px 0px 5px">
                    <f:facet name="header">
                        <h:outputText value="Opções"
                                      styleClass="text"
                                      style="font-weight: bold;"/>
                    </f:facet>
                    <h:panelGrid columns="1" styleClass="centralizabotoes">
                        <a4j:commandLink styleClass="pure-button pure-button-small"
                                         style="font-size: 11px !important; width: 82% !important;"
                                         action="#{InclusaoVendaRapidaControle.selecionarColaboradorPersonal}"
                                         reRender="dadosCliente, painelEndereco"
                                         oncomplete="#{InclusaoVendaRapidaControle.msgAlert};Richfaces.hideModalPanel('modalColaboradoresPorCpf');">
                            <i class="fa-icon-edit"></i> &nbsp Selecionar
                        </a4j:commandLink>
                    </h:panelGrid>
                </rich:column>
            </rich:dataTable>
            <c:if test="${InclusaoVendaRapidaControle.exibirValidacaoCpf}">
                <a4j:commandLink styleClass="pure-button pure-button-primary pull-right"
                                 style="margin-top: 3px;"
                                 action="#{InclusaoVendaRapidaControle.informarNovoCpf}"
                                 accesskey="1"
                                 reRender="dadosCliente"
                                 onclick="Richfaces.hideModalPanel('modalColaboradoresPorCpf');"
                                 oncomplete="setFocusCampoCpf();">
                    <i class="fa-icon-refresh"></i> &nbsp Informar outro CPF
                </a4j:commandLink>
            </c:if>

            <c:if test="${!InclusaoVendaRapidaControle.exibirValidacaoCpf}">
                <a4j:commandLink styleClass="pure-button pure-button-primary pull-right"
                                 style="margin-top: 3px;"
                                 action="#{InclusaoVendaRapidaControle.informarNovoCadastro}"
                                 accesskey="1"
                                 onclick="Richfaces.hideModalPanel('modalColaboradoresPorCpf');"
                                 oncomplete="setFocusCampoNome();">
                    <i class="fa-icon-plus-sign"></i> &nbsp Novo cadastro
                </a4j:commandLink>
            </c:if>
        </h:form>
    </rich:modalPanel>

    <rich:modalPanel id="modalValidacaoCPF" styleClass="novaModal" autosized="true" shadowOpacity="true" width="750"
                     height="300">
        <f:facet name="header">
            <h:panelGroup>
                <c:if test="${InclusaoVendaRapidaControle.exibirValidacaoCpf}">
                    <c:if test="${!InclusaoVendaRapidaControle.pessoaEstrangeira}">
                        <h:outputText value="Pessoas com mesmo CPF"/>
                    </c:if>
                    <c:if test="${InclusaoVendaRapidaControle.pessoaEstrangeira}">
                        <h:outputText value="Pessoas com mesmo RNE"/>
                    </c:if>
                </c:if>
                <c:if test="${!InclusaoVendaRapidaControle.exibirValidacaoCpf}">
                    <h:outputText value="Pessoas"/>
                </c:if>
            </h:panelGroup>
        </f:facet>
        <h:form id="formValidacaoCPF">
            <h:panelGroup id="painelDados">
                <rich:dataTable value="#{InclusaoVendaRapidaControle.clientesComMesmoCpf}"
                                var="cliente" width="100%"
                                id="tableResults"
                                styleClass="pure-table pure-table-striped pure-table-noCellPadding pure-table-links th3fix"
                                rendered="#{fn:length(InclusaoVendaRapidaControle.clientesComMesmoCpf) > 0}"
                                columnClasses="centralizado,esquerda,centralizado,centralizado,esquerda"
                >
                    <rich:column>
                        <f:facet name="header">
                            <h:outputText value="Aluno"
                                          styleClass="text"
                                          style="font-weight: bold;"/>
                        </f:facet>
                        <h:panelGrid columns="2" styleClass="linhainformacoesaluno">
                            <a4j:mediaOutput element="img"
                                             align="left" style="left:0px;width:70px;height:90px; border:none; "
                                             cacheable="false" session="true"
                                             title="#{cliente.pessoa.nome}"
                                             createContent="#{InclusaoVendaRapidaControle.paintFotoModal}"
                                             styleClass="shadow"
                                             value="#{ImagemData}" mimeType="image/jpeg">
                                <f:param value="#{SuperControle.timeStamp}" name="time"/>
                                <f:param name="largura" value="70"/>
                                <f:param name="altura" value="90"/>
                                <f:param name="pessoa" value="#{cliente.pessoa.codigo}"></f:param>
                            </a4j:mediaOutput>
                            <h:panelGroup>
                                <h:panelGrid columns="1" width="100%" styleClass="linhainformacoesaluno">

                                    <h:panelGroup>
                                        <h:outputText value="Matricula: "
                                                      styleClass="text"
                                                      style="font-weight: bold;"/>
                                        <h:outputText value="#{cliente.matricula}" styleClass="text"/>
                                    </h:panelGroup>
                                    <h:panelGroup>
                                        <h:outputText value="Nome: "
                                                      styleClass="text"
                                                      style="font-weight: bold;"/>
                                        <h:outputText value="#{cliente.pessoa.nome}" styleClass="text"/>
                                    </h:panelGroup>
                                    <h:panelGroup>
                                        <c:if test="${InclusaoVendaRapidaControle.pessoaEstrangeira}">
                                            <h:outputText value="RNE: "
                                                          styleClass="text"
                                                          style="font-weight: bold;"/>
                                            <h:outputText value="#{cliente.pessoa.rne}" styleClass="text"/>
                                        </c:if>
                                        <c:if test="${!InclusaoVendaRapidaControle.pessoaEstrangeira}">
                                            <h:outputText value="#{msg_aplic.prt_ConfiguracaoSistema_cfpOb}: "
                                                          styleClass="text"
                                                          style="font-weight: bold;"/>
                                            <h:outputText value="#{cliente.pessoa.cfp}" styleClass="text"/>
                                        </c:if>

                                    </h:panelGroup>
                                    <h:panelGroup>
                                        <h:outputText value="Empresa: "
                                                      styleClass="text"
                                                      style="font-weight: bold;"/>
                                        <h:outputText value="#{cliente.empresa.nome}" styleClass="text"/>
                                    </h:panelGroup>
                                </h:panelGrid>
                            </h:panelGroup>
                        </h:panelGrid>
                    </rich:column>
                    <rich:column style="padding: 0px 5px 0px 5px">
                        <f:facet name="header">
                            <h:outputText value="Dependentes"
                                          styleClass="text"
                                          style="font-weight: bold;"/>
                        </f:facet>
                        <h:outputText rendered="#{fn:length(cliente.clienteDenpentedes) == 0}"
                                      value="Ainda nao tem vínculos" styleClass="text"/>
                        <rich:dataTable value="#{cliente.clienteDenpentedes}"
                                        var="dep" width="100%"
                                        id="tableResultsDep"
                                        styleClass="pure-table pure-table-horizontal pure-table-striped pure-table-noCellPadding pure-table-links"
                                        rendered="#{fn:length(cliente.clienteDenpentedes) > 0}"
                                        columnClasses="centralizado,esquerda,centralizado,centralizado,esquerda"
                        >
                            <rich:column>
                                <a4j:mediaOutput element="img"
                                                 align="left" style="left:0px;width:35px;height:35px; border:none; "
                                                 cacheable="false" session="true"
                                                 title="#{dep.pessoa.nome}"
                                                 createContent="#{InclusaoVendaRapidaControle.paintFotoModal}"
                                                 styleClass="shadow"
                                                 value="#{ImagemData}" mimeType="image/jpeg">
                                    <f:param value="#{SuperControle.timeStamp}" name="time"/>
                                    <f:param name="largura" value="35"/>
                                    <f:param name="altura" value="35"/>
                                    <f:param name="pessoa" value="#{dep.pessoa.codigo}"></f:param>
                                </a4j:mediaOutput>

                            </rich:column>
                            <rich:column>
                                <h:outputText value="#{dep.pessoa.nome}" styleClass="text"/>
                            </rich:column>
                            <rich:column rendered="#{!cliente.apresentarBotaoTransferirClienteEmpresa}"
                                         style="padding: 0px 5px 0px 5px">
                                <a4j:commandLink
                                        styleClass="button-small pure-button pure-button-primary"
                                        style="font-size: 11px !important;padding: 5px 0px 5px 0px;"
                                        actionListener="#{InclusaoVendaRapidaControle.preparaClienteOperacaoCadastro}"
                                        action="#{InclusaoVendaRapidaControle.editarCliente}"
                                        accesskey="1" rendered="#{!dep.apresentarBotaoTransferirClienteEmpresa}"
                                        reRender="form">
                                    <f:attribute name="identificador" value="dep"/>
                                    <i class="fa-icon-edit"></i> &nbsp Editar
                                </a4j:commandLink>
                            </rich:column>

                        </rich:dataTable>

                    </rich:column>
                    <rich:column style="padding: 0px 5px 0px 5px">
                        <f:facet name="header">
                            <h:outputText value="Indicação" styleClass="text" style="font-weight: bold;"/>
                        </f:facet>
                        <h:panelGrid columns="1" width="100%" styleClass="linhainformacoesaluno">
                            <h:panelGroup>
                                <h:outputText rendered="#{empty cliente.indicacao.clienteQueIndicou.nome_Apresentar}"
                                              value="Não possui indicação" styleClass="text"/>
                            </h:panelGroup>
                            <h:panelGroup>
                                <h:outputText rendered="#{not empty cliente.indicacao.clienteQueIndicou.nome_Apresentar}"
                                              value="Nome: "
                                              styleClass="text"
                                              style="font-weight: bold;"/>
                                <h:outputText value="#{cliente.indicacao.clienteQueIndicou.nome_Apresentar}" styleClass="text"/>
                            </h:panelGroup>
                            <h:panelGroup>
                                <h:outputText rendered="#{not empty cliente.indicacao.clienteQueIndicou.nome_Apresentar}"
                                              value="Data: "
                                              styleClass="text"
                                              style="font-weight: bold;"/>
                                <h:outputText rendered="#{not empty cliente.indicacao.clienteQueIndicou.nome_Apresentar}"
                                        value="#{cliente.indicacao.dia_Apresentar}" styleClass="text"/>
                            </h:panelGroup>
                        </h:panelGrid>
                    </rich:column>
                    <rich:column style="padding: 0px 9px 0px 5px">
                        <f:facet name="header">
                            <h:outputText value="Opções"
                                          styleClass="text"
                                          style="font-weight: bold;"/>
                        </f:facet>
                        <h:panelGrid columns="1" styleClass="centralizabotoes">
                            <a4j:commandLink styleClass="pure-button pure-button-small"
                                             style="font-size: 11px !important; width: 82% !important;"
                                             action="#{InclusaoVendaRapidaControle.selecionarClienteVenda}"
                                             oncomplete="#{InclusaoVendaRapidaControle.msgAlert}"
                                             reRender="dadosCliente, painelEndereco, valores, comboplano">
                                <i class="fa-icon-edit"></i> &nbsp Selecionar
                            </a4j:commandLink>


                        </h:panelGrid>
                    </rich:column>

                </rich:dataTable>
                <c:if test="${InclusaoVendaRapidaControle.exibirValidacaoCpf}">
                    <h:outputText styleClass="mensagemDetalhada"
                                  value="#{InclusaoVendaRapidaControle.msgNrAlunosMesmoCpf}"/>
                </c:if>
                <br/>

                <c:if test="${InclusaoVendaRapidaControle.exibirValidacaoCpf}">
                    <a4j:commandLink styleClass="pure-button pure-button-primary pull-right"
                                     style="margin: 3px;"
                                     action="#{InclusaoVendaRapidaControle.informarNovoCpf}"
                                     accesskey="1"
                                     reRender="dadosCliente, painelEndereco"
                                     onclick="Richfaces.hideModalPanel('modalValidacaoCPF');"
                                     oncomplete="setFocusCampoCpf();fecharMenu();">
                        <c:if test="${InclusaoVendaRapidaControle.pessoaEstrangeira}">
                            <i class="fa-icon-refresh"></i> &nbsp Informar Outro RNE
                        </c:if>
                        <c:if test="${!InclusaoVendaRapidaControle.pessoaEstrangeira}">
                            <i class="fa-icon-refresh"></i> &nbsp Informar outro CPF
                        </c:if>
                    </a4j:commandLink>
                </c:if>

                <c:if test="${!InclusaoVendaRapidaControle.exibirValidacaoCpf}">
                    <a4j:commandLink styleClass="pure-button pure-button-primary pull-right"
                                     style="margin: 3px;"
                                     action="#{InclusaoVendaRapidaControle.informarNovoCadastro}"
                                     accesskey="1"
                                     reRender="form"
                                     onclick="Richfaces.hideModalPanel('modalValidacaoCPF');"
                                     oncomplete="setFocusCampoNome();">
                        <i class="fa-icon-plus-sign"></i> &nbsp Novo cadastro
                    </a4j:commandLink>
                </c:if>

                <a4j:commandLink id="btnIgnorar"
                                 styleClass="pure-button pure-button-primary pull-right"
                                 style="margin: 3px;"
                                 reRender="panelMensagemErro,panelExisteCliente"
                                 action="#{InclusaoVendaRapidaControle.escolherIgnorarCpfDuplicado}"
                                 accesskey="1"
                                 rendered="#{InclusaoVendaRapidaControle.apresentarBotaoIgnorarValidacaoCPF}"
                                 onclick="Richfaces.hideModalPanel('modalValidacaoCPF');"
                                 oncomplete="setFocusCampoCpf();">
                    <i class="fa-icon-remove"></i> &nbsp Ignorar
                </a4j:commandLink>
            </h:panelGroup>
        </h:form>
    </rich:modalPanel>

    <rich:modalPanel id="panelExisteCliente" autosized="true" shadowOpacity="true" styleClass="novaModal"
                     showWhenRendered="#{ClienteControle.clienteVO.apresentarRichModalErro}" width="450">
        <f:facet name="header">
            <h:outputText value="Atenção!"/>
        </f:facet>
        <a4j:form id="formExisteCliente">
            <jsp:include page="includes/include_persiste_modulo.jsp"/>
            <h:panelGrid columns="1" columnClasses="colunaCentralizada" width="100%">
                <h:panelGroup rendered="#{InclusaoVendaRapidaControle.permiteTransferirContrato}"
                              layout="block"
                              style="padding-top: 10px;">
                    <div>
                        <h:outputText styleClass="texto-size-14 texto-font texto-cor-cinza"
                                      value="Ao transferir este aluno, o contrato será cancelado na empresa de origem e um novo contrato com a mesma vigência final
                                  será gerado na empresa de destino. Sendo assim, todas as parcelas em aberto do cliente serão transferidas para empresa de destino.">
                        </h:outputText>
                    </div>
                    <div style="padding-top: 10px;">
                        <h:outputText rendered="#{!InclusaoVendaRapidaControle.permiteTransferirContrato}"
                                      style="color: orangered; padding-top: 10px;"
                                      styleClass="texto-size-14 texto-font texto-cor-cinza"
                                      value="O aluno não pode ser transferido porque o seu plano
                                  (#{InclusaoVendaRapidaControle.contratoBloqueioTransferenciaPorNaoPermitirVendaEmpresa.plano.descricao}) não pode ser vendido nesta unidade.
                              Para realizar esta transferência você precisa fazer o cancelamento manual do contrato atual do aluno e lançar um novo contrato nesta empresa.">
                        </h:outputText>
                    </div>
                </h:panelGroup>
                <h:panelGrid columns="1" width="100%" columnClasses="colunaCentralizada">
                    <h:panelGroup layout="block" styleClass="container-botoes">
                        <a4j:jsFunction name="selecionarCliente"
                                        action="#{InclusaoVendaRapidaControle.selecionarClienteVenda}"
                                        reRender="form"
                                        oncomplete="Richfaces.hideModalPanel('modalValidacaoCPF');fireElementFromParent('form:btnRederCliente');"/>
                        <a4j:commandLink id="btnTransferirClienteEmpresa"
                                         rendered="#{InclusaoVendaRapidaControle.clienteVO.apresentarBotaoTransferirClienteEmpresa && LoginControle.permissaoAcessoMenuVO.permissaoTransferirClienteEmpresa}"
                                         accesskey="9"
                                         oncomplete="selecionarCliente();Richfaces.hideModalPanel('panelExisteCliente');#{InclusaoVendaRapidaControle.mensagemNotificar}"
                                         styleClass="botaoPrimario texto-size-16-real texto-cor-branco"
                                         action="#{InclusaoVendaRapidaControle.gravarClienteTrocandoEmpresa}">
                            Transferir Cliente de Empresa <i class="fa-icon-exchange"></i>
                        </a4j:commandLink>

                        <a4j:commandButton id="btnIrParaTelaCliente"
                                           rendered="#{!InclusaoVendaRapidaControle.clienteVO.apresentarBotaoTransferirClienteEmpresa}"
                                           styleClass="botaoPrimario texto-size-16-real texto-cor-branco"
                                           style="margin-right: 30%; height: 36px;"
                                           value="Ver informações do cliente"
                                           action="#{InclusaoVendaRapidaControle.editarCliente}"/>
                        <rich:spacer width="12px"/>

                        <a4j:commandLink id="btnFechar"
                                         reRender="codigo,empresa,panelFoto,nomeCliente,dataNascCliente,categoria,nomeMae,nomePai,sexo,
                                   profissao,grauIntrucao,estadoCivil,cpf,rg,rgOrgao,rgUf,dataCadastro,residencial,comercial,
                                   celular,email,webPage,CEP,endereco,complento,bairro,numero,pais,estado,cidade,estado"
                                         accesskey="5"
                                         styleClass="botaoSecundario texto-size-16-real"
                                         oncomplete="Richfaces.hideModalPanel('panelExisteCliente')"
                                         value="Fechar"/>
                    </h:panelGroup>
                    <h:outputText
                            rendered="#{InclusaoVendaRapidaControle.clienteVO.apresentarBotaoTransferirClienteEmpresa && !LoginControle.permissaoAcessoMenuVO.permissaoTransferirClienteEmpresa}"
                            styleClass="mensagemDetalhada"
                            value="Usuário sem permissão para transferir aluno: '2.52 - Permissão para transferir cliente de empresa'"/>

                </h:panelGrid>
            </h:panelGrid>
        </a4j:form>
    </rich:modalPanel>

    <rich:modalPanel id="modalCupomDesconto" autosized="true" styleClass="novaModal" shadowOpacity="true" width="450"
                     height="200">
        <f:facet name="header">
            <h:panelGroup>
                <h:outputText value="Cupom de Desconto"></h:outputText>
            </h:panelGroup>
        </f:facet>
        <f:facet name="controls">
            <h:panelGroup>
                <h:outputText
                        styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign btnSelecionarModalidade"
                        id="hidelinkCupomDesc"/>
                <rich:componentControl for="modalCupomDesconto" attachTo="hidelinkCupomDesc" operation="hide"
                                       event="onclick"/>
            </h:panelGroup>
        </f:facet>

        <a4j:form id="formCupomDesc" ajaxSubmit="true" styleClass="paginaFontResponsiva">

            <h:panelGrid columns="1" columnClasses="col-text-align-center" width="100%" cellpadding="5">
                <h:outputText styleClass="tituloCampos" value="Número do Cupom "/>
                <h:panelGroup>
                    <h:inputText id="nrCupom" style="padding-left: 0px;padding-right: 0px" size="15"
                                 onblur="blurinput(this);" onfocus="focusinput(this);"
                                 styleClass="col-text-align-center"
                                 value="#{InclusaoVendaRapidaControle.numeroCupomAplicar}"/>
                    <h:message for="nrCupom" styleClass="mensagemDetalhada"/>
                </h:panelGroup>
            </h:panelGrid>
            <h:panelGroup layout="block" styleClass="container-botoes">
                <a4j:commandLink
                        id="botaoConfirmarCupomDesconto"
                        reRender="panelCupomDesconto,form:idValorMensalidade"
                        action="#{InclusaoVendaRapidaControle.aplicarCupomDesconto}"
                        oncomplete="#{InclusaoVendaRapidaControle.msgAlert};#{InclusaoVendaRapidaControle.mensagemNotificar}"
                        styleClass="botaoPrimario texto-size-16">
                    Confirmar
                </a4j:commandLink>
            </h:panelGroup>

        </a4j:form>
    </rich:modalPanel>


    <rich:modalPanel id="modalConcluir" autosized="true" styleClass="novaModal" shadowOpacity="true" width="450"
                     height="200">
        <f:facet name="header">
            <h:panelGroup>
                <h:outputText value="Boletim de visita"></h:outputText>
            </h:panelGroup>
        </f:facet>
        <f:facet name="controls">
            <h:panelGroup>
                <h:outputText
                        styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign btnSelecionarModalidade"
                        id="hidelinkConcluir"/>
                <rich:componentControl for="modalConcluir" attachTo="hidelinkConcluir" operation="hide"
                                       event="onclick"/>
            </h:panelGroup>
        </f:facet>

        <a4j:form id="formConcluir" ajaxSubmit="true" styleClass="paginaFontResponsiva">

            <h:panelGrid columns="1" columnClasses="col-text-align-center" width="100%" cellpadding="5">
                <h:outputText styleClass="tituloCampos" value="Deseja responder o boletim de visita?"/>

            </h:panelGrid>
            <h:panelGroup layout="block" styleClass="container-botoes">
                <a4j:commandButton id="salvarbv"
                                   onclick="limparMsgCarregando();ativarPoll()"
                                   value="Sim" alt="Concluir" styleClass="botoes nvoBt btSec"
                                   style="margin: 0; margin-top: 20px;"
                                   action="#{InclusaoVendaRapidaControle.gravarBV}"
                                   oncomplete="limparMsgCarregando();desativarPoll();#{InclusaoVendaRapidaControle.msgAlert}"/>


                <a4j:commandButton id="salvar"
                                   onclick="limparMsgCarregando();ativarPoll()"
                                   value="Apenas concluir" alt="Concluir" styleClass="botoes nvoBt"
                                   style="margin: 0; margin-top: 20px; margin-left: 10px"
                                   action="#{InclusaoVendaRapidaControle.gravar}"
                                   oncomplete="limparMsgCarregando();desativarPoll();#{InclusaoVendaRapidaControle.msgAlert}"/>
            </h:panelGroup>

        </a4j:form>
    </rich:modalPanel>

    <rich:modalPanel id="mdlRenovacaoContrato" autosized="true" styleClass="novaModal" shadowOpacity="true" width="450" height="200">
        <f:facet name="header">
            <h:panelGroup>
                <h:outputText value="Renovação de contrato"/>
            </h:panelGroup>
        </f:facet>
        <f:facet name="controls">
            <h:panelGroup>
                <h:outputText styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign" id="hdLnkRenovacaoContrato"/>
                <rich:componentControl for="mdlRenovacaoContrato" attachTo="hdLnkRenovacaoContrato" operation="hide" event="onclick"/>
            </h:panelGroup>
        </f:facet>

        <a4j:form id="fmRenovacaoContrato" ajaxSubmit="true" styleClass="paginaFontResponsiva">

            <h:panelGrid columns="1" columnClasses="col-text-align-center" width="100%" cellpadding="5">
                <h:outputText value="Esse aluno já possui um contrato ativo até #{InclusaoVendaRapidaControle.vigenciaAteAjustadaContratoBase}, deseja renovar?"/>
            </h:panelGrid>

            <h:panelGroup layout="block" styleClass="container-botoes">
                <a4j:commandButton id="btnRenovarSim"
                                   onclick="limparMsgCarregando();ativarPoll()"
                                   action="#{InclusaoVendaRapidaControle.validarSemConcomitante}"
                                   value="Sim"
                                   alt="Sim"
                                   styleClass="botoes nvoBt"
                                   style="margin: 20px 0 0;"
                                   oncomplete="limparMsgCarregando();desativarPoll();#{InclusaoVendaRapidaControle.msgAlert}"/>

                <a4j:commandButton id="btnRenovarNao2"
                                   rendered="#{InclusaoVendaRapidaControle.empresaLogado.permiteContratosConcomintante}"
                                   value="Não, lançar um novo contrato" alt="Não" styleClass="botoes nvoBt"
                                   style="margin: 20px 0 0 10px;"
                                   action="#{InclusaoVendaRapidaControle.validarConcomitante}"
                                   oncomplete="limparMsgCarregando();desativarPoll();#{InclusaoVendaRapidaControle.msgAlert}"/>


                <a4j:commandButton id="btnRenovarNao"
                                   rendered="#{!InclusaoVendaRapidaControle.empresaLogado.permiteContratosConcomintante}"
                                   value="Não" alt="Não" styleClass="botoes nvoBt"
                                   style="margin: 20px 0 0 10px;"
                                   oncomplete="limparMsgCarregando();desativarPoll();Richfaces.hideModalPanel('mdlRenovacaoContrato');"/>
            </h:panelGroup>
        </a4j:form>
    </rich:modalPanel>


    <rich:modalPanel id="modalpendenciaprotheus" autosized="true" styleClass="novaModal" shadowOpacity="true"
                     width="450" height="230">
        <f:facet name="header">
            <h:panelGroup>
                <h:outputText value="Aluno com pendência "/>
            </h:panelGroup>
        </f:facet>

        <h:form id="formpendenciaprotheus" style="overflow: hidden">

            <h:outputText value="#{InclusaoVendaRapidaControle.msgInadimplencia}" style="font-size: 15px "/>

            <h:panelGroup layout="block" styleClass="container-botoes">


                <a4j:commandButton id="pagardebito"
                                   value="Pagar débito" styleClass="botoes nvoBt "
                                   style="margin: 0; margin-top: 20px;" reRender="form"
                                   action="#{InclusaoVendaRapidaControle.adicionarAlunoInadimplente}"
                                   oncomplete="#{InclusaoVendaRapidaControle.msgAlert}"/>

                <a4j:commandButton id="cancelarvenda"
                                   value="Cancelar venda" styleClass="botoes nvoBt btSec"
                                   style="margin: 0; margin-top: 20px; margin-left: 10px"
                                   action="#{InclusaoVendaRapidaControle.cancelarVenda}"/>
            </h:panelGroup>

        </h:form>

    </rich:modalPanel>

    <%@include file="/include_load_configs.jsp" %>

    <h:panelGroup layout="block" id="divMsgCarregando">
        <h:outputText id="msgCarregando" style="display: none"
                      styleClass="msgCarregando"
                      value="#{InclusaoVendaRapidaControle.msgCarregando}"/>
    </h:panelGroup>

</f:view>
