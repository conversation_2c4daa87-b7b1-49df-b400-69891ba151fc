<%@page contentType="text/html"  %>
<%@page pageEncoding="ISO-8859-1" %>

<head>
    <%@include file="/includes/include_import_minifiles.jsp" %>
    <link href="./css/otimize.css" rel="stylesheet" type="text/css">
    <link href="beta/css/font-awesome_2.0.min.css" type="text/css" rel="stylesheet" />
    <link href="css/gestaoRecebiveis1.0.min.css" rel="stylesheet" type="text/css">
    <script type="text/javascript" language="javascript" src="hoverform.js"></script>
    <script type="text/javascript" language="javascript" src="script/script.js"></script>
    <script src="${root}/script/packJQueryPlugins.min.js" type="text/javascript"></script>
</head>
<link href="./css/otimize.css" rel="stylesheet" type="text/css">
<%@taglib prefix="f" uri="http://java.sun.com/jsf/core"%>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html"%>
<link href="beta/css/font-awesome.css" type="text/css" rel="stylesheet" />
<script type="text/javascript" language="javascript" src="hoverform.js"></script>
<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax"%>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich"%>
<%@taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<f:loadBundle var="msg_bt" basename="propriedades.Botoes" />
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao" />
<f:loadBundle var="msg" basename="propriedades.Mensagens" />
<%@include file="/includes/verificaModulo.jsp" %>
<c:set var="contexto" value="${pageContext.request.contextPath}" scope="request" />

<style>
    .linkscriptemailEmpresa{
        width: 100%;
        position: relative;
        padding: 10px;
        border: 1px solid #ddd!important;
        background-color: #fff!important;
        border-radius: 3px;
        color: #b4b4b4!important;
        margin-bottom: 20px;
    }
</style>

<f:view>
    <jsp:include page="includes/include_carregando_ripple.jsp" flush="true"/>
    <title>
        <h:outputText value="#{msg_aplic.prt_Empresa_tituloForm}" />
    </title>
    <a4j:loadScript src="/script/jquery.maskedinput-1.2.2.js" />
    <h:panelGrid columns="1" width="100%" cellpadding="0" cellspacing="0">
        <%-- INICIO HEADER --%>
        <c:set var="titulo" scope="session" value="${msg_aplic.prt_Empresa_tituloForm}" />
        <c:set var="urlWiki" scope="session" value="${SuperControle.urlBaseConhecimento}conhecimento/configuracoes-da-empresa/"/>
        <f:facet name="header">
            <jsp:include page="topoReduzido_material.jsp" />
        </f:facet>

        <h:form id="form">

            <h:panelGroup id="verificaCodigoEdicao">
                <script>
                    function compareCodigoEdicao(){
                        const codigoBack = ${EmpresaControle.codigoEdicao};
                        // console.log('---- codigoEmpresaEditando ' + codigoEmpresaEditando);
                        // console.log('---- codigoBack ' + codigoBack);
                        if(codigoBack && (codigoEmpresaEditando != codigoBack)){
                            window.close();
                        }

                    }
                    compareCodigoEdicao();
                </script>
            </h:panelGroup>

            <a4j:jsFunction name="updateVerificaCodigoEdicao"
                            process="none"
                            status="false"
                            reRender="verificaCodigoEdicao"/>

            <hr style="border-color: #e6e6e6;" />
            <input type="hidden" value="${modulo}" name="modulo" />

            <h:panelGrid columns="1" width="100%">
                <rich:tabPanel id="panelsEmpresa" width="100%" activeTabClass="true" headerAlignment="rigth"
                    switchType="ajax">
                    <rich:tab label="Dados Empresa">
                        <h:panelGrid columns="2" rowClasses="linhaImpar, linhaPar"
                            columnClasses="classEsquerda, classDireita" width="100%">

                            <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_Empresa_codigo}" />
                            <h:panelGroup>
                                <h:inputText id="codigo" size="10" maxlength="10" onblur="blurinput(this);"
                                    onfocus="focusinput(this);" styleClass="camposSomenteLeitura"
                                    value="#{EmpresaControle.empresaVO.codigo}" />
                                <h:message for="codigo" styleClass="mensagemDetalhada" />
                            </h:panelGroup>

                            <h:outputText styleClass="tituloCampos" value="ID Externo:" />
                            <h:panelGroup>
                                <h:inputText id="idexterno" size="10" onblur="blurinput(this);"
                                             onfocus="focusinput(this);" styleClass="form"
                                             value="#{EmpresaControle.empresaVO.idExterno}" />
                                <h:message for="idexterno" styleClass="mensagemDetalhada" />
                            </h:panelGroup>

                            <h:outputText styleClass="tituloCampos tooltipster" value="ID Favorecido:"
                                          title="Somente ADMINSTRADOR PACTO pode alterar, favor entrar em contato com a Pacto!" />
                            <h:panelGroup id="pnlCodFinanceiro" layout="block">
                                <h:inputText id="cod_financeiro" size="10" maxlength="50" onblur="blurinput(this);"
                                             onfocus="focusinput(this);" styleClass="form"
                                             disabled="#{!EmpresaControle.usuarioLogado.usuarioPactoSolucoes}"
                                             value="#{EmpresaControle.empresaVO.codEmpresaFinanceiro}"/>
                                <h:outputText style="margin-left: 8px"
                                              value="(#{EmpresaControle.empresaVO.sincronizacaoFinanceiro_Apresentar})"/>
                                <a4j:commandLink action="#{EmpresaControle.sincronizarComFinanceiro}"
                                                 style="margin-left: 8px"
                                                 rendered="#{EmpresaControle.usuarioLogado.usuarioPactoSolucoes}"
                                                 value="Sincronizar agora com Financeiro"
                                                 reRender="pnlCodFinanceiro,mensagem,mensagemDetalhada"/>
                            </h:panelGroup>

                            <h:outputText styleClass="tituloCampos tooltipster" value="Tipo de Empresa: "
                                          title="Somente ADMINSTRADOR PACTO pode alterar, favor entrar em contato com a Pacto!"
                                          rendered="#{EmpresaControle.usuarioLogado.usuarioPactoSolucoes}"/>
                            <h:panelGroup id="pnlTipoEmpresa" layout="block" rendered="#{EmpresaControle.usuarioLogado.usuarioPactoSolucoes}">
                                <h:selectOneMenu id="tipoEmpresa" onblur="blurinput(this);" onfocus="focusinput(this);"
                                                 styleClass="form" value="#{EmpresaControle.empresaVO.tipoEmpresa}" rendered="#{EmpresaControle.usuarioLogado.usuarioPactoSolucoes}">
                                    <f:selectItems value="#{EmpresaControle.listaSelectItemTipoEmpresa}" />
                                </h:selectOneMenu>
                            </h:panelGroup>

                            <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_Empresa_nome}" />
                            <h:panelGroup>
                                <h:inputText id="nome" size="50" maxlength="50" title="Nome da empresa"
                                    onblur="blurinput(this);" onfocus="focusinput(this);" styleClass="form tooltipster"
                                    value="#{EmpresaControle.empresaVO.nome}" />
                                <h:message for="nome" styleClass="mensagemDetalhada" />
                            </h:panelGroup>



                            <h:outputText styleClass="tituloCampos" value="Ativa:" />
                            <h:panelGroup layout="block" id="pnlAtivo">
                                <h:selectBooleanCheckbox disabled="#{!EmpresaControle.usuarioLogado.administrador}"
                                    id="situacaoEmpresa" styleClass="campos" value="#{EmpresaControle.empresaVO.ativa}">
                                    <a4j:support event="onchange" reRender="pnlAtivo"/>
                                </h:selectBooleanCheckbox>

                                <c:if test="${not EmpresaControle.empresaVO.ativa}">
                                    <h:outputText styleClass="tituloCampos" value="Transferida de banco:" />
                                    <h:selectBooleanCheckbox disabled="#{!EmpresaControle.usuarioLogado.administrador}"
                                                             id="transferida" styleClass="campos"
                                                             value="#{EmpresaControle.empresaVO.transferida}">
                                        <a4j:support event="onchange" reRender="pnlAtivo"/>
                                    </h:selectBooleanCheckbox>
                                </c:if>

                                <c:if test="${not EmpresaControle.empresaVO.ativa and EmpresaControle.empresaVO.transferida}">
                                    <h:panelGrid columns="2">
                                        <h:outputText styleClass="tituloCampos" value="Nova chave:" />
                                        <h:inputText size="50" maxlength="50"
                                                     disabled="#{!EmpresaControle.usuarioLogado.administrador}"
                                                     id="novaChave" styleClass="campos"
                                                     value="#{EmpresaControle.empresaVO.novaChaveTransferencia}"/>

                                        <h:outputText styleClass="tituloCampos" value="Novo código:" />
                                        <rich:inputNumberSpinner id="novoCodigo" styleClass="form" minValue="1"
                                                                 disabled="#{!EmpresaControle.usuarioLogado.administrador}"
                                                                 maxValue="999"
                                                                 value="#{EmpresaControle.empresaVO.novoCodigoTransferencia}" />
                                    </h:panelGrid>

                                </c:if>
                            </h:panelGroup>
                            <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_Empresa_razaoSocial}" />
                            <h:panelGroup>
                                <h:inputText id="razaoSocial" size="60" maxlength="100" onblur="blurinput(this);"
                                    onfocus="focusinput(this);" styleClass="form"
                                    value="#{EmpresaControle.empresaVO.razaoSocial}" />
                                <h:message for="razaoSocial" styleClass="mensagemDetalhada" />
                            </h:panelGroup>

                            <h:outputText styleClass="tituloCampos" value="Nome curto:" />
                            <h:panelGroup>
                                <h:inputText id="nomeCurto" size="50" maxlength="50" title="Nome curto"
                                             onblur="blurinput(this);" onfocus="focusinput(this);" styleClass="form tooltipster"
                                             value="#{EmpresaControle.empresaVO.nomeCurto}" />
                            </h:panelGroup>

                            <c:if test="${EmpresaControle.configuracaoSistema.usarSistemaInternacional}">
                                <h:outputText styleClass="tituloCampos"
                                              value="#{EmpresaControle.displayIdentificadorFront[2]}"/>
                            </c:if>
                            <c:if test="${!EmpresaControle.configuracaoSistema.usarSistemaInternacional}">
                                <h:outputText styleClass="tituloCampos"
                                              value="* #{EmpresaControle.displayIdentificadorFront[2]}"/>
                            </c:if>
                            <h:panelGroup>
                                <c:if test="${!EmpresaControle.configuracaoSistema.usarSistemaInternacional}">
                                    <h:inputText id="CNPJ" size="20" maxlength="18"
                                                 disabled="#{!UsuarioControle.usuarioLogado.administrador}"
                                                 title="Para alteração do CNPJ, é necessário solicitar via e-<NAME_EMAIL>"
                                                 onkeypress="return mascara(this.form, 'form:CNPJ', '99.999.999/9999-99', event);"
                                                 onblur="blurinput(this);" onfocus="focusinput(this);" styleClass="form"
                                                 value="#{EmpresaControle.empresaVO.CNPJ}"/>
                                </c:if>
                                <c:if test="${EmpresaControle.configuracaoSistema.usarSistemaInternacional}">
                                    <h:inputText id="CNPJ" size="20" maxlength="20"
                                                 disabled="#{!UsuarioControle.usuarioLogado.administrador}"
                                                 title="Para alteração do CNPJ, é necessário solicitar via e-<NAME_EMAIL>"
                                                 onblur="blurinput(this);" onfocus="focusinput(this);" styleClass="form"
                                                 value="#{EmpresaControle.empresaVO.CNPJ}"/>
                                </c:if>
                                <h:message for="CNPJ" styleClass="mensagemDetalhada" />
                            </h:panelGroup>
                            <c:if test="${EmpresaControle.configuracaoSistema.usarSistemaInternacional}">
                                <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_Empresa_inscEstadualNaoObrigatorio}"/>
                            </c:if>
                            <c:if test="${!EmpresaControle.configuracaoSistema.usarSistemaInternacional}">
                                <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_Empresa_inscEstadual}"/>
                            </c:if>
                            <h:panelGroup>
                                <h:inputText id="inscEstadual" size="20" maxlength="20" onblur="blurinput(this);"
                                    onfocus="focusinput(this);" styleClass="form"
                                    value="#{EmpresaControle.empresaVO.inscEstadual}" />
                                <h:message for="inscEstadual" styleClass="mensagemDetalhada" />
                            </h:panelGroup>
                            <h:outputText styleClass="tituloCampos" value="Inscrição Municipal:" />
                            <h:panelGroup>
                                <h:inputText id="inscMunicipal" size="20" maxlength="50" onblur="blurinput(this);"
                                    onfocus="focusinput(this);" styleClass="form"
                                    value="#{EmpresaControle.empresaVO.inscMunicipal}" />
                                <h:message for="inscMunicipal" styleClass="mensagemDetalhada" />
                            </h:panelGroup>
                            <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_Empresa_CEP}" />
                            <h:panelGroup>

                                <h:outputText value="     " />
                                <c:if test="${ClienteControle.configuracaoSistema.usarSistemaInternacional}">
                                    <h:inputText id="CEP" size="10" maxlength="10"
                                                 onblur="blurinput(this);" onfocus="focusinput(this);" styleClass="form"
                                                 value="#{EmpresaControle.empresaVO.CEP}"/>
                                </c:if>
                                <c:if test="${!ClienteControle.configuracaoSistema.usarSistemaInternacional}">
                                    <h:inputText id="CEP" size="10" maxlength="10"
                                                 onkeypress="return mascara(this.form, 'form:CEP', '99.999-999', event);"
                                                 onblur="blurinput(this);" onfocus="focusinput(this);" styleClass="form"
                                                 value="#{EmpresaControle.empresaVO.CEP}"/>
                                    <img border="0" style="margin-left:4px;margin-right:4px;vertical-align:middle;"
                                         src="images/icon_lupa.png" title="Consulte o CEP" width="16" height="16">
                                    <a4j:commandLink
                                            reRender="CEP, endereco, complemento, setor, numero, pais, estado, cidade, mensagem, mensagemDetalhada"
                                            action="#{EmpresaControle.consultarCEP}">Consulte o CEP</a4j:commandLink>
                                </c:if>
                            </h:panelGroup>

                            <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_Empresa_endereco}" />
                            <h:panelGroup>
                                <h:inputText id="endereco" size="50" maxlength="50" onblur="blurinput(this);"
                                    onfocus="focusinput(this);" styleClass="form"
                                    value="#{EmpresaControle.empresaVO.endereco}" />
                                <h:message for="endereco" styleClass="mensagemDetalhada" />
                            </h:panelGroup>

                            <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_Empresa_complemento}" />
                            <h:panelGroup>
                                <h:inputText id="complemento" size="50" maxlength="100" onblur="blurinput(this);"
                                    onfocus="focusinput(this);" styleClass="form"
                                    value="#{EmpresaControle.empresaVO.complemento}" />

                                <h:outputText value="     " />

                            </h:panelGroup>

                            <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_Empresa_latitude}" />
                            <h:panelGroup>
                                <h:inputText id="latitude" size="30" maxlength="20" onblur="blurinput(this);"
                                    onfocus="focusinput(this);" styleClass="form"
                                    value="#{EmpresaControle.empresaVO.latitude}" />
                            </h:panelGroup>

                            <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_Empresa_longitude}" />
                            <h:panelGroup>
                                <h:inputText id="longitude" size="30" maxlength="20" onblur="blurinput(this);"
                                    onfocus="focusinput(this);" styleClass="form"
                                    value="#{EmpresaControle.empresaVO.longitude}" />
                            </h:panelGroup>


                            <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_Empresa_setor}" />
                            <h:panelGroup>
                                <h:inputText id="setor" size="32" maxlength="20" onblur="blurinput(this);"
                                    onfocus="focusinput(this);" styleClass="form"
                                    value="#{EmpresaControle.empresaVO.setor}" />

                                <h:outputText value="     " />

                                <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_Empresa_numero}" />

                                <h:outputText value="     " />

                                <h:inputText id="numero" size="5" maxlength="5" onblur="blurinput(this);"
                                    onfocus="focusinput(this);" styleClass="form"
                                    value="#{EmpresaControle.empresaVO.numero}" />


                                <h:outputText value="     " />

                            </h:panelGroup>

                            <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_Empresa_pais}" />
                            <h:panelGroup>

                                <h:outputText value="     " />

                                <h:selectOneMenu id="pais" onblur="blurinput(this);" onfocus="focusinput(this);"
                                    styleClass="form" value="#{EmpresaControle.empresaVO.pais.codigo}">
                                    <f:selectItems value="#{EmpresaControle.listaSelectItemPais}" />
                                    <a4j:support event="onchange" reRender="estado,cidade" focus="pais"
                                        action="#{EmpresaControle.montarListaSelectItemEstado}" />
                                </h:selectOneMenu>
                                <a4j:commandButton id="atualizar_pais"
                                    action="#{EmpresaControle.montarListaSelectItemPais}" image="imagens/atualizar.png"
                                    immediate="true" ajaxSingle="true" reRender="form:pais" />
                                <h:message for="pais" styleClass="mensagemDetalhada" />

                                <h:outputText value="     " />

                            </h:panelGroup>

                            <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_Empresa_estado}" />
                            <h:panelGroup>

                                <h:selectOneMenu id="estado" onblur="blurinput(this);" onfocus="focusinput(this);"
                                    styleClass="form" value="#{EmpresaControle.empresaVO.estado.codigo}">
                                    <f:selectItems value="#{EmpresaControle.listaSelectItemEstado}" />
                                    <a4j:support event="onchange" reRender="cidade"
                                        action="#{EmpresaControle.montarListaSelectItemCidade}" />
                                </h:selectOneMenu>
                                <a4j:commandButton id="atualizar_estado"
                                    action="#{EmpresaControle.montarListaSelectItemEstado}"
                                    image="imagens/atualizar.png" ajaxSingle="true" reRender="form:estado" />
                                <h:message for="estado" styleClass="mensagemDetalhada" />
                            </h:panelGroup>

                            <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_Empresa_cidade}" />
                            <h:panelGroup>

                                <h:outputText value="     " />

                                <h:selectOneMenu id="cidade" onblur="blurinput(this);" onfocus="focusinput(this);"
                                    styleClass="form" value="#{EmpresaControle.empresaVO.cidade.codigo}">
                                    <f:selectItems value="#{EmpresaControle.listaSelectItemCidade}" />
                                </h:selectOneMenu>
                                <a4j:commandButton id="atualizar_cidade"
                                    action="#{EmpresaControle.montarListaSelectItemCidade}"
                                    image="imagens/atualizar.png" immediate="true" ajaxSingle="true"
                                    reRender="form:cidade" />
                                <h:message for="cidade" styleClass="mensagemDetalhada" />
                            </h:panelGroup>

                            <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_Empresa_telComercial1}" />
                            <h:panelGroup>

                                <h:outputText value="     " />

                            <c:if test="${!EmpresaControle.configuracaoSistema.usarSistemaInternacional}">
                                <h:inputText id="telComercial1" size="13" maxlength="13"
                                    onchange="return validar_Telefone(this.id);" onblur="blurinput(this);"
                                    onkeypress="return mascara(this.form, this.id , '(99)999999999', event);"
                                    onfocus="focusinput(this);" styleClass="form"
                                    value="#{EmpresaControle.empresaVO.telComercial1}" />
                            </c:if>
                            <c:if test="${EmpresaControle.configuracaoSistema.usarSistemaInternacional}">
                                <h:inputText id="telComercial1" size="13" maxlength="14"
                                             onfocus="focusinput(this);" styleClass="form"
                                             value="#{EmpresaControle.empresaVO.telComercial1}" />
                            </c:if>
                                <h:outputText value="     " />

                                <h:outputText styleClass="tituloCampos"
                                    value="#{msg_aplic.prt_Empresa_telComercial2}" />

                                <h:outputText value="     " />
                                <c:if test="${!EmpresaControle.configuracaoSistema.usarSistemaInternacional}">
                                    <h:inputText id="telComercial2" size="13" maxlength="13"
                                        onchange="return validar_Telefone(this.id);" onblur="blurinput(this);"
                                        onkeypress="return mascara(this.form, this.id , '(99)999999999', event);"
                                        onfocus="focusinput(this);" styleClass="form"
                                        value="#{EmpresaControle.empresaVO.telComercial2}" />
                                </c:if>
                                <c:if test="${EmpresaControle.configuracaoSistema.usarSistemaInternacional}">
                                    <h:inputText id="telComercial2" size="13" maxlength="14"
                                                 onfocus="focusinput(this);" styleClass="form"
                                                 value="#{EmpresaControle.empresaVO.telComercial2}" />
                                </c:if>
                            </h:panelGroup>

                            <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_Empresa_telComercial3}" />
                            <h:panelGroup>

                                <h:outputText value="     " />
                                <c:if test="${!EmpresaControle.configuracaoSistema.usarSistemaInternacional}">
                                    <h:inputText id="numeroTelefone" size="13" maxlength="13"
                                        onchange="return validar_Telefone(this.id);" onblur="blurinput(this);"
                                        onkeypress="return mascara(this.form, this.id , '(99)999999999', event);"
                                        onfocus="focusinput(this);" styleClass="form"
                                        value="#{EmpresaControle.empresaVO.telComercial3}" />
                                </c:if>
                                <c:if test="${EmpresaControle.configuracaoSistema.usarSistemaInternacional}">
                                    <h:inputText id="numeroTelefone" size="13" maxlength="14"
                                                 onfocus="focusinput(this);" styleClass="form"
                                                 value="#{EmpresaControle.empresaVO.telComercial3}" />
                                </c:if>

                                <h:outputText value="     " />

                                <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_Empresa_fax}" />

                                <h:outputText value="     " />
                                <c:if test="${!EmpresaControle.configuracaoSistema.usarSistemaInternacional}">
                                    <h:inputText id="fax" size="13" maxlength="13"
                                        onchange="return validar_Telefone(this.id);" onblur="blurinput(this);"
                                        onkeypress="return mascara(this.form, this.id , '(99)999999999', event);"
                                        onfocus="focusinput(this);" styleClass="form"
                                        value="#{EmpresaControle.empresaVO.fax}" />
                                </c:if>
                                <c:if test="${EmpresaControle.configuracaoSistema.usarSistemaInternacional}">
                                    <h:inputText id="faxIntr" size="13" maxlength="20"
                                                 onfocus="focusinput(this);" styleClass="form"
                                                 value="#{EmpresaControle.empresaVO.fax}" />
                                </c:if>
                            </h:panelGroup>
                            <c:if test="${LoginControle.usuarioLogado.administrador}">
                                <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_Empresa_email}" />
                                <h:inputText id="email" size="60" maxlength="100" onblur="blurinput(this);"
                                    onfocus="focusinput(this);" styleClass="form"
                                    value="#{EmpresaControle.empresaVO.email}" />

                                <h:outputText styleClass="tituloCampos tooltipster"
                                    value="#{msg_aplic.prt_Empresa_emailNotificacaoVendasOnline}"
                                    title="#{msg_aplic.prt_Empresa_emailNotificacaoVendasOnline_tooltip}" />
                                <h:inputText id="emailNotificacaoVendasOnline" size="50" maxlength="50"
                                    onblur="blurinput(this);" onfocus="focusinput(this);" styleClass="form"
                                    value="#{EmpresaControle.empresaVO.emailNotificacaoVendasOnline}" />

                                <h:outputText rendered="#{EmpresaControle.configuracaoSistema.usarSistemaInternacional}"
                                              styleClass="tituloCampos tooltipster" value="Moeda da empresa"
                                              title="(R$, US$, MZN)"/>
                                <h:inputText rendered="#{EmpresaControle.configuracaoSistema.usarSistemaInternacional}"
                                             id="moedaEmpresa" size="5" maxlength="5" onblur="blurinput(this);"
                                             onfocus="focusinput(this);" styleClass="form"
                                             value="#{EmpresaControle.empresaVO.moeda}"/>

                                <h:outputText rendered="#{EmpresaControle.configuracaoSistema.usarSistemaInternacional}"
                                              styleClass="tituloCampos tooltipster" value="Descrição da Moeda no singular"
                                              title="(Real, Dolar, Euro, etc...) no singular"/>
                                <h:inputText rendered="#{EmpresaControle.configuracaoSistema.usarSistemaInternacional}"
                                             id="Descmoeda" size="5" onblur="blurinput(this);"
                                             onfocus="focusinput(this);" styleClass="form"
                                             value="#{EmpresaControle.empresaVO.descMoeda}"/>

                                <h:outputText rendered="#{EmpresaControle.configuracaoSistema.usarSistemaInternacional}"
                                              styleClass="tituloCampos tooltipster" value="Locale"
                                              title="(pt_BR, en_US, pt_MZ)"/>

                                <h:selectOneMenu id="localeEmpresa" onblur="blurinput(this);"
                                                 rendered="#{EmpresaControle.configuracaoSistema.usarSistemaInternacional}"
                                                 onfocus="focusinput(this);"
                                                 value="#{EmpresaControle.empresaVO.localeTexto}">
                                    <f:selectItems value="#{EmpresaControle.locales}" />
                                </h:selectOneMenu>

                            </c:if>
                            <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_Empresa_site}" />
                            <h:inputText id="site" size="50" maxlength="50" onblur="blurinput(this);"
                                onfocus="focusinput(this);" styleClass="form"
                                value="#{EmpresaControle.empresaVO.site}" />
                            <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_Empresa_fusoHorairo}" />
                            <h:panelGroup>
                                <h:selectOneMenu id="comboTimeZone"
                                    value="#{EmpresaControle.empresaVO.timeZoneDefault}">
                                    <f:selectItems value="#{EmpresaControle.listaTimeZones}" />
                                </h:selectOneMenu>
                            </h:panelGroup>
                            <c:if test="${EmpresaControle.configuracaoSistema.isUtilizarServicoSesiSC()}">
                                <h:outputText styleClass="tituloCampos" value="Conta bancaria Sesi:" />
                                <h:inputText id="contaBancariaUnidadeSESI" size="10" maxlength="10" onblur="blurinput(this);"
                                             onfocus="focusinput(this);" styleClass="form"
                                             value="#{EmpresaControle.empresaVO.idContabancariaSesi}" />

                                <h:outputText styleClass="tituloCampos" value="Código Unidade Sesi:" />
                                <h:inputText id="cdigoExternoUnidadeSESI" size="10" maxlength="10" onblur="blurinput(this);"
                                             onfocus="focusinput(this);" styleClass="form"
                                             value="#{EmpresaControle.empresaVO.codExternoUnidadeSesi}" />
                                <h:outputText styleClass="tituloCampos" value="Cnpj Cliente Sesi:" />
                                <h:inputText id="cnjpClienteSesi" size="20" maxlength="20"
                                             onblur="blurinput(this);" onfocus="focusinput(this);" styleClass="form"
                                             value="#{EmpresaControle.empresaVO.cnpjClienteSesi}"/>
                            </c:if>

                            <c:if test="${LoginControle.utilizaModuloGestaoRedes}">
                                <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_Empresa_codigoRede}" />
                                <h:inputText id="codigoRede" size="20" maxlength="20" onblur="blurinput(this);"
                                             onfocus="focusinput(this);" styleClass="form"
                                             value="#{EmpresaControle.empresaVO.codigoRede}" />
                            </c:if>

                        </h:panelGrid>
                    </rich:tab>
                    <rich:tab label="Configurações da Empresa" id="configuracaoEmpresa">
                        <h:panelGrid id="cfgEmpresa" columns="2" rowClasses="linhaImpar, linhaPar"
                            columnClasses="classEsquerda, classDireita" width="100%">
                            <h:outputText styleClass="tituloCampos"
                                value="#{msg_aplic.prt_Empresa_questionarioPrimeiraVisita}" />
                            <h:panelGroup>
                                <h:selectOneMenu id="questionarioPrimeiraVisita" onblur="blurinput(this);"
                                    onfocus="focusinput(this);" styleClass="form"
                                    value="#{EmpresaControle.empresaVO.questionarioPrimeiraVisita.codigo}">
                                    <f:selectItems
                                        value="#{EmpresaControle.listaSelectItemQuestionarioPrimeiraVisita}" />
                                </h:selectOneMenu>
                                <a4j:commandButton id="atualizar_questionarioPrimeiraVisita"
                                    action="#{EmpresaControle.montarListaSelectItemQuestionarioPrimeiraVisita}"
                                    image="imagens/atualizar.png" immediate="true" ajaxSingle="true"
                                    reRender="form:questionarioPrimeiraVisita" />
                                <h:message for="questionarioPrimeiraVisita" styleClass="mensagemDetalhada" />
                            </h:panelGroup>

                            <h:outputText styleClass="tituloCampos"
                                value="#{msg_aplic.prt_ConfiguracaoSistema_nrdiasquestionariovisita}" />
                            <h:inputText id="nrdiasvigentevisita" size="10" onblur="blurinput(this);"
                                onfocus="focusinput(this);" styleClass="form"
                                value="#{EmpresaControle.empresaVO.nrDiasVigenteQuestionarioVista}" />

                            <h:outputText styleClass="tituloCampos"
                                value="#{msg_aplic.prt_Empresa_questionarioRetorno}" />
                            <h:panelGroup>
                                <h:selectOneMenu id="questionarioRetorno" onblur="blurinput(this);"
                                    onfocus="focusinput(this);" styleClass="form"
                                    value="#{EmpresaControle.empresaVO.questionarioRetorno.codigo}">
                                    <f:selectItems value="#{EmpresaControle.listaSelectItemQuestionarioRetorno}" />
                                </h:selectOneMenu>
                                <a4j:commandButton id="atualizar_questionarioRetorno"
                                    action="#{EmpresaControle.montarListaSelectItemQuestionarioRetorno}"
                                    image="imagens/atualizar.png" immediate="true" ajaxSingle="true"
                                    reRender="form:questionarioRetorno" />
                                <h:message for="questionarioRetorno" styleClass="mensagemDetalhada" />
                            </h:panelGroup>

                            <h:outputText styleClass="tituloCampos"
                                value="#{msg_aplic.prt_ConfiguracaoSistema_nrdiasquestionarioretorno}" />
                            <h:inputText id="nrdiasvigenteretorno" size="10" onblur="blurinput(this);"
                                onfocus="focusinput(this);" styleClass="form"
                                value="#{EmpresaControle.empresaVO.nrDiasVigenteQuestionarioRetorno}" />

                            <h:outputText styleClass="tituloCampos"
                                value="#{msg_aplic.prt_Empresa_questionarioReMatricula}" />
                            <h:panelGroup>
                                <h:selectOneMenu id="questionarioReMatricula" onblur="blurinput(this);"
                                    onfocus="focusinput(this);" styleClass="form"
                                    value="#{EmpresaControle.empresaVO.questionarioReMatricula.codigo}">
                                    <f:selectItems value="#{EmpresaControle.listaSelectItemQuestionarioReMatricula}" />
                                </h:selectOneMenu>
                                <a4j:commandButton id="atualizar_questionarioReMatricula"
                                    action="#{EmpresaControle.montarListaSelectItemQuestionarioReMatricula}"
                                    image="imagens/atualizar.png" immediate="true" ajaxSingle="true"
                                    reRender="form:questionarioReMatricula" />
                                <h:message for="questionarioReMatricula" styleClass="mensagemDetalhada" />
                            </h:panelGroup>

                            <h:outputText styleClass="tituloCampos"
                                value="#{msg_aplic.prt_ConfiguracaoSistema_nrdiasquestionariorematricula}" />
                            <h:inputText id="nrdiasvigenterematricula" size="10" onblur="blurinput(this);"
                                onfocus="focusinput(this);" styleClass="form"
                                value="#{EmpresaControle.empresaVO.nrDiasVigenteQuestionarioRematricula}" />

                            <h:outputText styleClass="tituloCampos" rendered="#{LoginControle.apresentarLinkEstudio}"
                                value="#{msg_aplic.prt_ConfiguracaoSistema_questionarioPrimeiraCompra}" />
                            <h:panelGroup rendered="#{LoginControle.apresentarLinkEstudio}">
                                <h:selectOneMenu id="questionarioPrimeiraCompra" onblur="blurinput(this);"
                                    onfocus="focusinput(this);" styleClass="form"
                                    value="#{EmpresaControle.empresaVO.questionarioPrimeiraCompra.codigo}">
                                    <f:selectItems
                                        value="#{EmpresaControle.listaSelectItemQuestionarioPrimeiraCompra}" />
                                </h:selectOneMenu>
                                <a4j:commandButton id="atualizar_questionarioPrimeiraCompra"
                                    action="#{EmpresaControle.montarListaSelectItemQuestionarioPrimeiraCompra}"
                                    image="imagens/atualizar.png" immediate="true" ajaxSingle="true"
                                    reRender="form:questionarioPrimeiraCompra" />
                                <h:message for="questionarioPrimeiraCompra" styleClass="mensagemDetalhada" />
                            </h:panelGroup>
                            <h:outputText styleClass="tituloCampos" rendered="#{LoginControle.apresentarLinkEstudio}"
                                value="#{msg_aplic.prt_ConfiguracaoSistema_nrdiasquestionariovisita}" />
                            <h:inputText id="nrDiasVigentePrimeiraCompra" size="10"
                                rendered="#{LoginControle.apresentarLinkEstudio}"
                                value="#{EmpresaControle.empresaVO.nrDiasVigenteQuestionarioPrimeiraCompra}" />
                            <h:outputText styleClass="tituloCampos" rendered="#{LoginControle.apresentarLinkEstudio}"
                                value="#{msg_aplic.prt_ConfiguracaoSistema_questionarioRetorno}" />
                            <h:panelGroup rendered="#{LoginControle.apresentarLinkEstudio}">
                                <h:selectOneMenu id="questionarioRetornoCompra" onblur="blurinput(this);"
                                    onfocus="focusinput(this);" styleClass="form"
                                    value="#{EmpresaControle.empresaVO.questionarioRetornoCompra.codigo}">
                                    <f:selectItems
                                        value="#{EmpresaControle.listaSelectItemQuestionarioRetornoCompra}" />
                                </h:selectOneMenu>
                                <a4j:commandButton id="atualizar_questionarioRetornoCompra"
                                    action="#{EmpresaControle.montarListaSelectItemQuestionarioRetornoCompra}"
                                    image="imagens/atualizar.png" immediate="true" ajaxSingle="true"
                                    reRender="form:questionarioRetornoCompra" />
                                <h:message for="questionarioRetornoCompra" styleClass="mensagemDetalhada" />
                            </h:panelGroup>
                            <h:outputText styleClass="tituloCampos" rendered="#{LoginControle.apresentarLinkEstudio}"
                                value="#{msg_aplic.prt_ConfiguracaoSistema_nrdiasquestionarioretorno}" />
                            <h:inputText id="nrDiasVigenteRetornoCompra" size="10"
                                rendered="#{LoginControle.apresentarLinkEstudio}"
                                value="#{EmpresaControle.empresaVO.nrDiasVigenteQuestionarioRetornoCompra}" />

                            <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_Empresa_mascaraMatricula}" />
                            <h:inputText id="matricula" size="10" maxlength="10" onblur="blurinput(this);"
                                         onkeypress="return mascaraMatricula(this.form,'form:matricula', event);"
                                         onfocus="focusinput(this);" styleClass="form"
                                         value="#{EmpresaControle.empresaVO.mascaraMatricula}" />

                            <h:outputText styleClass="tituloCampos"
                                value="#{msg_aplic.prt_Empresa_permiteContratosConcomintante}" />
                            <h:selectBooleanCheckbox id="permiteContratosConcomintante" styleClass="campos"
                                value="#{EmpresaControle.empresaVO.permiteContratosConcomintante}" />

                            <h:outputText styleClass="tituloCampos"
                                value="#{msg_aplic.prt_ConfiguracaoSistema_carenciaRenovacao}" />
                            <h:inputText id="carenciaRenovacao" size="10" onblur="blurinput(this);"
                                onfocus="focusinput(this);" styleClass="form"
                                value="#{EmpresaControle.empresaVO.carenciaRenovacao}" />

                            <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_Empresa_nrDiasAvencer}" />
                            <h:inputText id="nrDiasAvencer" size="10" onblur="blurinput(this);"
                                onfocus="focusinput(this);" styleClass="form"
                                value="#{EmpresaControle.empresaVO.nrDiasAvencer}" />

                            <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_Empresa_somaDv}" />
                            <h:inputText id="somaDv" size="10" onblur="blurinput(this);" onfocus="focusinput(this);"
                                styleClass="form" value="#{EmpresaControle.empresaVO.somaDv}" />

                            <h:outputText styleClass="tituloCampos"
                                value="#{msg_aplic.prt_ConfiguracaoSistema_carencia}" />
                            <h:inputText id="carencia" size="10" onblur="blurinput(this);" onfocus="focusinput(this);"
                                styleClass="form" value="#{EmpresaControle.empresaVO.carencia}" />

                            <h:outputText styleClass="tituloCampos"
                                value="#{msg_aplic.prt_ConfiguracaoSistema_nrDiasProrata}" />
                            <h:inputText id="nrDiasProrata" size="10" onblur="blurinput(this);"
                                onfocus="focusinput(this);" styleClass="form"
                                value="#{EmpresaControle.empresaVO.nrDiasProrata}" />

                            <h:outputText styleClass="tituloCampos"
                                value="#{msg_aplic.prt_ConfiguracaoSistema_toleranciaProrata}" />
                            <h:inputText id="toleranciaprorata" size="10" onblur="blurinput(this);"
                                onfocus="focusinput(this);" styleClass="form"
                                value="#{EmpresaControle.empresaVO.toleranciaProrata}" />

                            <h:outputText styleClass="tituloCampos"
                                value="Número de dias para renovação automática antecipada" />
                            <h:inputText id="nrDiasRenovacaoAntecipadaAutomatica" size="10" onblur="blurinput(this);"
                                onfocus="focusinput(this);" styleClass="form"
                                value="#{EmpresaControle.empresaVO.diasRenovacaoAutomaticaAntecipada}" />

                            <h:outputText styleClass="tituloCampos tooltipster"
                                value="#{msg_aplic.prt_Finan_lancarVariasParcelasSaldoDevedor}"
                                title="Marque esta opção somente se deseja utilizar a opção de lançar várias parcelas de saldo devedor do cliente" />
                            <h:selectBooleanCheckbox id="habilitarPermitirLancarParcelaSaldoDevedor"
                                value="#{EmpresaControle.empresaVO.permitirLancarVariasParcelasSaldoDevedor}"
                                styleClass="tooltipster tituloCampos"
                                title="Marque esta opção somente se deseja utilizar a opção de lançar várias parcelas de saldo devedor do cliente" />

                            <h:outputText styleClass="tituloCampos"
                                value="#{msg_aplic.prt_ConfiguracaoSistema_fecharNegociacaoSemAutorizacaoDCC}" />
                            <h:selectBooleanCheckbox id="permiteFecharNegociacaoSemAutorizacaoDCC" styleClass="campos"
                                value="#{EmpresaControle.empresaVO.fecharNegociacaoSemAutorizacaoDCC}" />

                            <h:outputText styleClass="tituloCampos"
                                value="#{msg_aplic.prt_ConfiguracaoSistema_minimoVencimento2parcela}:" />
                            <h:selectBooleanCheckbox id="forcarMinimoVencimento2parcela" styleClass="campos"
                                value="#{EmpresaControle.empresaVO.forcarMinimoVencimento2parcela}" />

                            <c:if test="${LoginControle.usuarioLogado.administrador}">
                                <h:outputText styleClass="tituloCampos"
                                    value="#{msg_aplic.prt_Empresa_serviceUsuario}" />
                                <h:inputText id="serviceUsuario" size="50" maxlength="100" onblur="blurinput(this);"
                                    onfocus="focusinput(this);" styleClass="form"
                                    value="#{EmpresaControle.empresaVO.serviceUsuario}" />
                            </c:if>

                            <c:if test="${LoginControle.usuarioLogado.administrador}">
                                <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_Empresa_serviceSenha}" />
                                <h:inputSecret id="serviceSenha" size="14" maxlength="20" onblur="blurinput(this);"
                                    redisplay="true" onfocus="focusinput(this);" styleClass="form"
                                    value="#{EmpresaControle.empresaVO.serviceSenha}" />
                            </c:if>

                            <h:outputText styleClass="tituloCampos"
                                rendered="#{EmpresaControle.apresentarCampoConsultorVendaAvulsa}"
                                value="#{msg_aplic.prt_ConfiguracaoSistema_ConsultorResponsavelVendaAvulsa}" />
                            <h:panelGroup rendered="#{EmpresaControle.apresentarCampoConsultorVendaAvulsa}">
                                <h:selectOneMenu id="consultorVendaAvulsa" onblur="blurinput(this);"
                                    onfocus="focusinput(this);" styleClass="form"
                                    value="#{EmpresaControle.empresaVO.consultorVendaAvulsa.codigo}">
                                    <f:selectItems value="#{EmpresaControle.listaSelectItemConsultor}" />
                                </h:selectOneMenu>
                                <a4j:commandButton id="atualizarRecibosSemConsultores" immediate="true"
                                    ajaxSingle="true" action="#{EmpresaControle.atualizarRecibosSemConsultores}"
                                    rendered="#{EmpresaControle.usuarioLogado.administrador}"
                                    value="Atualizar vendas sem consultores"
                                    reRender="form:consultorVendaAvulsa, form:panelErroMensagem" />
                                <h:message for="consultorVendaAvulsa" styleClass="mensagemDetalhada" />
                            </h:panelGroup>

                            <h:outputText styleClass="tituloCampos" value="Obrigatorio preencher o BV:" />
                            <h:selectBooleanCheckbox id="bvObrigatorio" styleClass="campos"
                                value="#{EmpresaControle.empresaVO.bvObrigatorio}" />



                            <h:outputText styleClass="tituloCampos"
                                value="#{msg_aplic.prt_ConfiguracaoSistema_tempoAposFaltaReposicao}" />
                            <h:inputText id="tempoAposFaltaReposicao" size="10" onblur="blurinput(this);"
                                onfocus="focusinput(this);" styleClass="form"
                                value="#{EmpresaControle.empresaVO.tempoAposFaltaReposicao}" />

                            <h:outputText styleClass="tituloCampos"
                                value="#{msg_aplic.prt_ConfiguracaoSistema_cobrarRematriculaApos}" />
                            <h:inputText id="cobrarRematriculaApos" size="10" onblur="blurinput(this);"
                                onfocus="focusinput(this);" styleClass="form"
                                value="#{EmpresaControle.empresaVO.qtdDiasCobrarRematricula}" />

                            <h:outputText styleClass="tituloCampos tooltipster"
                                title="#{msg_aplic.prt_ConfiguracaoSistema_msgAlertaVinculos}"
                                value="#{msg_aplic.prt_ConfiguracaoSistema_RemoverVinculosAposDesistencia}" />
                            <h:selectBooleanCheckbox id="removerVinculosAposDesistencia" styleClass="campos tooltipster"
                                title="#{msg_aplic.prt_ConfiguracaoSistema_msgAlertaVinculos}"
                                value="#{EmpresaControle.empresaVO.removerVinculosAposDesistencia}">
                                <a4j:support event="onclick" reRender="cfgEmpresa" />
                            </h:selectBooleanCheckbox>

                            <c:if test="${EmpresaControle.empresaVO.removerVinculosAposDesistencia eq true}">
                                <h:outputText styleClass="tituloCampos"
                                    value="#{msg_aplic.prt_ConfiguracaoSistema_nrDiasDesistenteRemoverVinculoTreino}" />
                                <h:inputText id="nrDiasDesistenteRemoverVinculoTreino" size="10"
                                    onblur="blurinput(this);" onfocus="focusinput(this);" styleClass="form"
                                    value="#{EmpresaControle.empresaVO.nrDiasDesistenteRemoverVinculoTreino}" />
                            </c:if>

                            <h:outputText styleClass="tituloCampos"
                                value="#{msg_aplic.prt_ConfiguracaoSistema_liberarpersonalcomtaxaemaberto}" />
                            <h:selectBooleanCheckbox id="liberarPersonalComTaxaEmAberto" styleClass="campos"
                                value="#{EmpresaControle.empresaVO.liberarPersonalComTaxaEmAberto}" />


                            <h:outputText styleClass="tituloCampos tooltipster" title="Trata apenas produtos vencidos" value="Liberar Personal/Professor com Débito" />
                            <h:selectBooleanCheckbox id="liberarPersonalProfessorDebito" styleClass="campos tooltipster"
                                                     title="Trata apenas produtos vencidos"
                                value="#{EmpresaControle.empresaVO.liberarPersonalProfessorDebito}" />

                            <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_usar_manutencao_comissao}" />
                            <h:selectBooleanCheckbox id="********************************" styleClass="campos"
                                value="#{EmpresaControle.empresaVO.********************************}" />

                            <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_arredondamento_parcelas}" />
                            <h:selectOneMenu value="#{EmpresaControle.empresaVO.arredondamento}"
                                id="Arredondar"
                                onblur="blurinput(this);" onfocus="focusinput(this);" styleClass="form">
                                <f:selectItems value="#{EmpresaControle.listaArredondamento}" />
                            </h:selectOneMenu>

                            <h:outputText styleClass="tituloCampos"
                                value="Permite Renovar Contratos em Turmas Lotadas" />
                            <h:selectBooleanCheckbox id="permiteRenovarContratosEmTurmasLotadas" styleClass="campos"
                                value="#{EmpresaControle.empresaVO.permiteRenovarContratosEmTurmasLotadas}" />

                            <h:outputText styleClass="tituloCampos"
                                          value="Trabalhar com capacidade de horários por categoria (Turma)" />
                            <h:selectBooleanCheckbox id="horariocapacidadeporcategoria" styleClass="campos"
                                                     value="#{EmpresaControle.empresaVO.horariocapacidadeporcategoria}" />

                            <h:outputText
                                rendered="#{EmpresaControle.usuarioLogado.administrador && LoginControle.apresentarGestaoPersonal}"
                                styleClass="tituloCampos" value="Usar gestão de crédito de personal:" />
                            <h:selectBooleanCheckbox
                                rendered="#{EmpresaControle.usuarioLogado.administrador && LoginControle.apresentarGestaoPersonal}"
                                styleClass="campos" value="#{EmpresaControle.empresaVO.usarGestaoCreditosPersonal}">
                                <a4j:support event="onclick" reRender="form" />
                            </h:selectBooleanCheckbox>

                            <h:outputText styleClass="tituloCampos"
                                value="Pagar comissão para consultor somente se atingir meta financeira:" />
                            <h:selectBooleanCheckbox id="pagarCommSeAtingirMeta" styleClass="campos"
                                value="#{EmpresaControle.empresaVO.pagarComissaoSeAtingirMetaFinanceira}">
                            </h:selectBooleanCheckbox>
                            <h:outputText styleClass="tituloCampos"
                                value="Calcular comissão para consultor em produtos Matrícula e Rematricula: " />
                            <h:selectBooleanCheckbox id="comissaoMatriculaRematricula" styleClass="campos"
                                value="#{EmpresaControle.empresaVO.comissaoMatriculaRematricula}" />
                            <h:outputText styleClass="tituloCampos"
                                value="Calcular comissão para consultor em Manutenção de Modalidade: " />
                            <h:selectBooleanCheckbox id="comissaoManutModalidade" styleClass="campos"
                                value="#{EmpresaControle.empresaVO.pagarComissaoManutencaoModalidade}" />
                            <h:outputText styleClass="tituloCampos"
                                value="Calcular comissão para consultor para Produtos: " />
                            <h:selectBooleanCheckbox id="comissaoProdutos" styleClass="campos"
                                value="#{EmpresaControle.empresaVO.pagarComissaoProdutos}" />
                            <h:outputText styleClass="tituloCampos"
                                value="Bloquear venda de produtos de estoque com posição atual igual a 0: " />
                            <h:selectBooleanCheckbox styleClass="campos"
                                value="#{EmpresaControle.empresaVO.somenteVendaProdutosComEstoque}" />
                            <h:outputText styleClass="tituloCampos"
                                title="Essa configuração, se marcada, não permite que aluno pague duas parcelas no mesmo mês"
                                value="Permitir contrato pós-pago na Renovação Automática: " />
                            <h:selectBooleanCheckbox styleClass="campos"
                                value="#{EmpresaControle.empresaVO.permiteContratoPosPagoRenovacaoAuto}" />

                            <h:outputText styleClass="tituloCampos" title="Será enviado um e-mail para novos clientes após a venda online."
                                value="Modelo de Mensagem concluiu 1ª compra (Vendas Online): " />
                            <h:selectOneMenu id="modeloMensagemVendasOnline" onblur="blurinput(this);"
                                onfocus="focusinput(this);" styleClass="form"
                                value="#{EmpresaControle.empresaVO.modeloMensagemVendasOnline.codigo}">
                                <f:selectItems value="#{EmpresaControle.listaSelectItemModeloMensagem}" />
                            </h:selectOneMenu>
                            <h:outputText styleClass="tituloCampos"
                                value="Modelo de Mensagem esqueci minha senha(Vendas Online): " />
                            <h:selectOneMenu id="modeloMensagemEsqueciMinhaSenhaVendasOnline" onblur="blurinput(this);"
                                onfocus="focusinput(this);" styleClass="form"
                                value="#{EmpresaControle.empresaVO.modeloMensagemEsqueciMinhaSenhaVendasOnline.codigo}">
                                <f:selectItems value="#{EmpresaControle.listaSelectItemModeloMensagem}" />
                            </h:selectOneMenu>


                            <h:outputText styleClass="tituloCampos"
                                value="Liberar vagas para trancamentos acima de: " />
                            <h:panelGroup>
                                <h:inputText id="qtdDiasParaLiberacaoDeVagaEmTrancamento" size="3" maxlength="3"
                                    onblur="blurinput(this);" onfocus="focusinput(this);"
                                    title="Para liberação automática de vaga informar um valor maior do que zero"
                                    styleClass="form"
                                    value="#{EmpresaControle.empresaVO.qtdDiasParaLiberacaoDeVagaEmTrancamento}" />
                                <h:outputText styleClass="form" value=" Dias" />
                            </h:panelGroup>

                            <h:outputText styleClass="tituloCampos"
                                value="Considerar data de início do contrato para ICV" />
                            <h:panelGroup>
                                <h:selectBooleanCheckbox styleClass="campos" id="usarDataInicioDeContratoNoBI_ICV"
                                    title="Altera regra para cálculo dos índices de ICV no B.I"
                                    value="#{EmpresaControle.empresaVO.usarDataInicioDeContratoNoBI_ICV}" />
                            </h:panelGroup>

                            <h:outputText styleClass="tituloCampos"
                                title="Essa configuração, se marcada, ao concluir a negociação de plano envia por email o login de acesso ao Vendas Online"
                                value="Gerar login API ao incluir contrato: " />
                            <h:selectBooleanCheckbox styleClass="campos"
                                value="#{EmpresaControle.empresaVO.gerarLoginAPIAoIncluirContrato}" />

                            <h:outputText styleClass="tituloCampos"
                                title="Caso marcada, a venda de contratos poderá ser impedida caso o horário escolhido pelo cliente tenha reposição marcada, anteriormente, em algum dia da vigência do contrato"
                                value="Impedir a Venda de Contrato caso haja Reposição marcada previamente" />
                            <h:selectBooleanCheckbox styleClass="campos"
                                title="Caso marcada, a venda de contratos poderá ser impedida caso o horário escolhido pelo cliente tenha reposição marcada, anteriormente, em algum dia da vigência do contrato"
                                value="#{EmpresaControle.empresaVO.impedirVendaContratoPorConflitoReposicao}" />

                            <h:outputText styleClass="tituloCampos"
                                          value="Bloquear renovação automática para planos fora da vigência" />
                            <h:selectBooleanCheckbox id="bloquearRenovacaoAutomaticaPlanosForaDaVigencia" styleClass="campos"
                                                     value="#{EmpresaControle.empresaVO.bloquearRenovacaoAutomaticaPlanosForaDaVigencia}" />

                            <h:outputText styleClass="tituloCampos"
                                          value="Bloquear renovação automática para planos com categoria diferente do aluno" />
                            <h:selectBooleanCheckbox id="bloquearRenovacaoAutomaticaPlanosForaCategoriaCliente" styleClass="campos"
                                                     value="#{EmpresaControle.empresaVO.bloquearRenovacaoAutomaticaPlanosForaCategoriaCliente}" />

                            <h:outputText styleClass="tituloCampos"
                                value="Bloquear para não renovar contratos do tipo recorrência automaticamente sem o Indice Financeiro cadastrado " />
                            <h:selectBooleanCheckbox styleClass="campos" id="marcarIGPM"
                                value="#{EmpresaControle.empresaVO.naoRenovarContratoSemIndiceFinanceiro}" />

                            <h:outputText value="Limite inicial para exibição dos itens no BI de pendências do cliente:"
                                title="Para desconsiderar esta configuração, deixe o campo em branco."
                                styleClass="tooltipster tituloCampos" />
                            <h:panelGroup>
                                <h:panelGroup id="panelGroupDataLimite" styleClass="panelGroupDataLimite">

                                    <rich:calendar id="limiteInicialItensBIPendencia"
                                        value="#{EmpresaControle.empresaVO.limiteInicialItensBIPendencia}"
                                        inputSize="10" inputClass="form" oninputblur="blurinput(this);"
                                        oninputfocus="focusinput(this);" oninputchange="return validar_Data(this.id);"
                                        datePattern="dd/MM/yyyy" enableManualInput="true" zindex="2"
                                        showWeeksBar="false">
                                    </rich:calendar>
                                </h:panelGroup>
                                <h:message for="limiteInicialItensBIPendencia" styleClass="mensagemDetalhada" />
                                <rich:jQuery id="mskData" selector=".rich-calendar-input" timing="onload"
                                    query="mask('99/99/9999')" />
                                <rich:toolTip value="Para desconsiderar esta configuração, deixe o campo em branco."
                                    for="panelGroupDataLimite" />
                            </h:panelGroup>

                            <h:outputText styleClass="tituloCampos" value="Código GymPass" />
                            <h:inputText id="codigoGymPass" size="10" onblur="blurinput(this);"
                                onfocus="focusinput(this);" styleClass="form" rendered="#{!SuperControle.pontoInterrogacaoHabilitado}"
                                value="#{EmpresaControle.empresaVO.codigoGymPass}" />

                            <h:outputLink value="https://pactosolucoes.com.br/ajuda/conhecimento/como-fazer-a-integracao-do-sistema-pacto-com-a-gympass/"
                               style="font-size: 12px; margin: 4px;"
                                          rendered="#{SuperControle.pontoInterrogacaoHabilitado}"
                               target="_blank">O cadastro da integração com a Gympass mudou, clique aqui e entenda.</h:outputLink>

                            <h:outputText styleClass="tituloCampos" value="Token API GymPass" rendered="#{!SuperControle.pontoInterrogacaoHabilitado}" />
                            <h:inputText id="tokenApiGymPass" size="10" onblur="blurinput(this);"
                                onfocus="focusinput(this);" styleClass="form"
                                         rendered="#{!SuperControle.pontoInterrogacaoHabilitado}"
                                value="#{EmpresaControle.empresaVO.tokenApiGymPass}" />

                            <h:outputText styleClass="tituloCampos" value="URL Webhook GymPass" rendered="#{!SuperControle.pontoInterrogacaoHabilitado}"/>
                            <h:inputText id="URLWebHook" size="90" onblur="blurinput(this);"
                                         disabled="true" onfocus="focusinput(this);" styleClass="form"
                                         rendered="#{!SuperControle.pontoInterrogacaoHabilitado}"
                                         value="#{EmpresaControle.empresaVO.URLWebHookGympass}" />

                            <h:outputText styleClass="tituloCampos" value="Secret Gympass" rendered="#{!SuperControle.pontoInterrogacaoHabilitado}"/>
                            <h:inputText id="SecretGymPass" size="20" onblur="blurinput(this);"
                                         rendered="#{!SuperControle.pontoInterrogacaoHabilitado}"
                                         disabled="true" onfocus="focusinput(this);" styleClass="form"
                                         value="P4CT0#GyMP4S5" />

                            <h:outputText styleClass="tooltipster tituloCampos" title="Ao realizar uma operação em \"
                                Manutenção de modalidade\", o sistema reconhecerá as presenças, reposições, faltas e as
                                desmarcações da antiga modalidade, ou seja, essas informações não serão perdidas."
                                value="Considerar as  mudanças de modalidade dos contratos, para os cálculos em \"
                                Modalidades\"" />
                            <h:selectBooleanCheckbox styleClass="tooltipster campos"
                                id="habilitarSomaAulaTrocaModalidade" title="Ao realizar uma operação em \" Manutenção
                                de modalidade\", o sistema reconhecerá as presenças, reposições, faltas e as
                                desmarcações da antiga modalidade, ou seja, essas informações não serão perdidas."
                                value="#{EmpresaControle.empresaVO.habilitarSomaDeAulaNaoVigente}" />

                            <h:outputText styleClass="tooltipster tituloCampos"
                                value="Retirar edição pagamento do relatorio de comissão de consultor" />
                            <h:selectBooleanCheckbox styleClass="tooltipster campos" id="retirarEdicaoPagamento"
                                value="#{EmpresaControle.empresaVO.retirarEdicaoPagamento}" />

                            <h:outputText styleClass="tooltipster tituloCampos"
                                title="Serão consideradas as aulas desmarcadas do contrato anterior (sem reposição) no novo contrato"
                                value="Considerar aulas desmarcadas do contrato anterior"
                                rendered="#{EmpresaControle.usuarioLogado.administrador}" />
                            <h:selectBooleanCheckbox styleClass="tooltipster campos"
                                id="adicionarAulasDesmarcadasContratoAnterior"
                                title="Serão consideradas as aulas desmarcadas do contrato anterior (sem reposição) no novo contrato"
                                rendered="#{EmpresaControle.usuarioLogado.administrador}"
                                value="#{EmpresaControle.empresaVO.adicionarAulasDesmarcadasContratoAnterior}" />

                            <h:outputText styleClass="tituloCampos" value="URL Site Cadastrar Cliente:" />
                            <h:inputText id="urlLinkSiteCadastro" size="80" onblur="blurinput(this);"
                                onfocus="focusinput(this);" styleClass="form"
                                value="#{EmpresaControle.empresaVO.urlLinkSiteCadastro}" />

                            <h:outputText styleClass="tooltipster tituloCampos"
                                value="Permite estornar contrato que possui parcela(s) paga(s)" />
                            <h:selectBooleanCheckbox styleClass="tooltipster campos"
                                id="permitirEstornarContratoComParcelasPagas"
                                value="#{EmpresaControle.empresaVO.permitirEstornarContratoComParcelasPG}" />
                            <h:outputText styleClass="tituloCampos"
                                value="Permite marcar aulas no AulaCheia em dias de Feriado" />
                            <h:selectBooleanCheckbox id="permMarcarAulaFeriado" styleClass="campos"
                                value="#{EmpresaControle.empresaVO.permMarcarAulaFeriado}">
                                <a4j:support event="onchange" reRender="form" focus="horaAberturaFeriado" />
                            </h:selectBooleanCheckbox>
                            <h:outputText rendered="#{EmpresaControle.empresaVO.permMarcarAulaFeriado}"
                                styleClass="tituloCampos" value="Hora de Abertura no Feriado" />
                            <h:inputText rendered="#{EmpresaControle.empresaVO.permMarcarAulaFeriado}"
                                id="horaAberturaFeriado" size="10" onblur="blurinput(this);" onfocus="focusinput(this);"
                                onkeypress="return mascaraTodos(this.form, 'form:horaAberturaFeriado', '99:99', event);"
                                styleClass="form" maxlength="5"
                                value="#{EmpresaControle.empresaVO.horaAberturaFeriado}" />
                            <h:outputText rendered="#{EmpresaControle.empresaVO.permMarcarAulaFeriado}"
                                styleClass="tituloCampos" value="Hora de Fechamento no Feriado" />
                            <h:inputText rendered="#{EmpresaControle.empresaVO.permMarcarAulaFeriado}"
                                id="horaFechamentoFeriado" size="10" onblur="blurinput(this);"
                                onfocus="focusinput(this);"
                                onkeypress="return mascaraTodos(this.form, 'form:horaFechamentoFeriado', '99:99', event);"
                                styleClass="form" maxlength="5"
                                value="#{EmpresaControle.empresaVO.horaFechamentoFeriado}" />

                            <h:outputText styleClass="tooltipster tituloCampos" title="Permite adicionar o aluno automaticamente no TreinoWeb caso o mesmo não esteja cadastrado.
                                          <br/>(Sistema irá adicionar com um professor aleatório)"
                                value="Permite adicionar cliente ao TreinoWeb automático" />
                            <h:selectBooleanCheckbox styleClass="tooltipster campos" id="addAutoClienteTreinoWeb"
                                value="#{EmpresaControle.empresaVO.addAutoClienteTreinoWeb}" />

                            <h:outputText styleClass="tituloCampos"
                                          value="Manter marcações futuras em renovação de contrato de crédito" />
                            <h:selectBooleanCheckbox id="manterMarcacoesFuturasCreditoRenovacao" styleClass="campos"
                                                     value="#{EmpresaControle.empresaVO.manterMarcacoesFuturasCreditoRenovacao}">
                            </h:selectBooleanCheckbox>

                            <h:outputText styleClass="tituloCampos"
                                value="Quantidade de dias limite para pesquisa no Fechamento de Caixa:" />
                            <h:panelGroup id="comboNumeroDiasFechamentoCX">
                                <rich:inputNumberSpinner id="diasParaRetirarRelFecCx" styleClass="form" minValue="1"
                                    maxValue="99999"
                                    value="#{EmpresaControle.empresaVO.diasParaRetirarRelFechamentoDeCaixa}" />
                                <rich:toolTip
                                    value="Valor que define quantidade de dias para retirar o relatório.(Valor deve ser maior que 0)" />
                            </h:panelGroup>

                            <h:outputText styleClass="tituloCampos"
                                          value="Mostra Item com valor zerados em relatórios:" />
                            <h:selectBooleanCheckbox styleClass="tooltipster campos" id="mostrarValoresZeradosRel"
                                                     value="#{EmpresaControle.empresaVO.mostrarValoresZeradosRel}" />

                            <h:outputText styleClass="tituloCampos"
                                          value="Utiliza leitor código de barras:" />
                            <h:selectBooleanCheckbox styleClass="tooltipster campos" id="utilizaLeitorCodigoBarras"
                                                     value="#{EmpresaControle.empresaVO.utilizaLeitorCodigoBarras}">
                                <a4j:support event="onclick" reRender="cfgEmpresa" />
                            </h:selectBooleanCheckbox>

                            <h:outputText styleClass="tituloCampos tooltipster"
                                          value="Valor máximo para deixar no caixa em aberto (Venda Avulsa):"
                                          title="Caso não queria colocar um limite, deixar em branco (R$ 0,00)."/>
                            <h:inputText id="valorLimiteCaixaAbertoVendaAvulsa"
                                         size="10" maxlength="10" onblur="blurinput(this);"
                                         onkeypress="return formatar_moeda(this,'.',',',event);" onfocus="focusinput(this);"
                                         styleClass="form"
                                         value="#{EmpresaControle.empresaVO.valorLimiteCaixaAbertoVendaAvulsa}">
                                <f:converter converterId="FormatadorNumerico"/>
                            </h:inputText>

                            <h:outputText styleClass="tituloCampos tooltipster"
                                          value="Restringir convidado a ter somente um convite por mês por convidante"
                                          title="Ao restringir, o convidado não poderá ser convidado pela mesma pessoa no mesmo mês, apenas por outros convidantes"/>
                            <h:selectBooleanCheckbox id="restringirConvidadoUmaVezPorMes" styleClass="campos"
                                                     value="#{EmpresaControle.empresaVO.restringirConvidadoUmaVezPorMes}" />

                            <h:outputText styleClass="tituloCampos tooltipster"
                                          value="Responder BV ao lançar uma venda rápida"
                                          title="Se desmarcado, não é necessário responder o BV ao lançar uma venda rápida. "/>
                            <h:selectBooleanCheckbox id="responderBVNaVendaRapida" styleClass="campos"
                                                     value="#{EmpresaControle.empresaVO.responderBVNaVendaRapida}" />

                            <h:outputText styleClass="tituloCampos tooltipster" value="Limitar descontos por perfil de acesso:"
                                          title="Ao habilitar essa configuração os descontos manuais serão limitados pelas configurações do perfil de acesso." />
                            <h:selectBooleanCheckbox id="limitarDescontosPorPerfil" styleClass="campos tooltipster"
                                                     title="Ao habilitar essa configuração os descontos manuais serão limitados pelas configurações do perfil de acesso."
                                                     value="#{EmpresaControle.empresaVO.limitarDescontosPorPerfil}" />

                            <h:outputText styleClass="tituloCampos tooltipster" value="Obrigatório preenchimento dos campos do cartão durante pagamento:"
                                          title="Ao habilitar essa configuração será obrigatório o preenchimento dos campos do cartão durante um pagamento no sistema." />
                            <h:selectBooleanCheckbox id="********************************" styleClass="campos tooltipster"
                                                     title="Ao habilitar essa configuração será obrigatório o preenchimento dos campos do cartão durante um pagamento no sistema."
                                                     value="#{EmpresaControle.empresaVO.********************************}" />

                            <h:outputText styleClass="tituloCampos tooltipster" value="Habilitar Cadastro de Empresa SESI:" />
                            <h:selectBooleanCheckbox id="habilitarCadastroEmpresaSesi" styleClass="campos tooltipster"
                                                     value="#{EmpresaControle.empresaVO.habilitarCadastroEmpresaSesi}" />

                            <h:outputText styleClass="tituloCampos tooltipster" value="Número de dias para vencimento do par-q:"
                                          title="Quantidade de dias para vencimento do par-q assinado. Data base será a data de assinaturado do mesmo." />
                            <h:panelGroup id="comboNrDiasVencimentoParq">
                                <rich:inputNumberSpinner id="nrDiasVencimentoParq" styleClass="form" minValue="0"
                                                         maxValue="99999"
                                                         value="#{EmpresaControle.empresaVO.diasParaVencimentoParq}" />
                                <rich:toolTip
                                        value="Quantidade de dias para vencimento do par-q assinado. Data base será a data de assinaturado mesmo. \"0 = Desativado\"." />
                            </h:panelGroup>

                            <c:if test="${LoginControle.permissaoAcessoMenuVO.ativarVerificacaoClientesAtivos}">
                                <h:outputText styleClass="tooltipster tituloCampos" value=""/>
                                <a4j:commandLink styleClass="tooltipster" value="Ativar Verificação de Clientes"
                                                 action="#{EmpresaControle.ativarClientesBI}"
                                                 oncomplete="#{EmpresaControle.mensagemNotificar}"
                                                 id="ativarClientesBI" reRender="abaLogotipo, panelErroMensagem"
                                                 accesskey="2"/>
                            </c:if>

                            <h:outputText styleClass="tituloCampos tooltipster"
                                          value="Utiliza gestão de clientes com restrições:" title=""/>
                            <h:selectBooleanCheckbox id="utilizaGestaoClientesComRestricoes"
                                                     styleClass="campos tooltipster" title=""
                                                     value="#{EmpresaControle.empresaVO.utilizaGestaoClientesComRestricoes}"/>

                            <h:outputText styleClass="tituloCampos tooltipster"
                                          value="Permite compartilhamento de plano com cliente ativo(plano crédito):" title="Ao habilitar essa configuração, o sistema permitirá o compartilhamento de plano com cliente ativo que tenha apenas plano crédito."/>
                            <h:selectBooleanCheckbox id="permiteCompartilhamentoPlanoClienteAtivoPlanoCredito"
                                                     styleClass="campos tooltipster" title="Ao habilitar essa configuração, o sistema permitirá o compartilhamento de plano com cliente ativo que tenha apenas plano crédito."
                                                     value="#{EmpresaControle.empresaVO.permiteCompartilhamentoPlanoClienteAtivoPlanoCredito}"/>


                            <h:outputText styleClass="tituloCampos tooltipster"
                                          value="Habilitar validação de horários da mesma turma" title="Ao habilitar essa configuração, o usuário ao selecionar um horário específico, só permitirá que ele selecione horários da mesma turma."/>
                            <h:selectBooleanCheckbox id="habilitarValidacaoHorariosMesmaTurma"
                                                     styleClass="campos tooltipster" title="Ao habilitar essa configuração, o usuário ao selecionar um horário específico, só permitirá que ele selecione horários da mesma turma."
                                                     value="#{EmpresaControle.empresaVO.habilitarValidacaoHorariosMesmaTurma}"/>
                            <h:outputText styleClass="tituloCampos tooltipster"
                                          value="Emitir NFC-e imediatamente após o recebimento:" title="Ao habilitar essa configuração, o sistema realizará a emissão da NFC-e imediatamente após o clique no botão ?Concluir? no lançamento de pagamento."/>
                            <h:selectBooleanCheckbox id="checkbox_config_envioNFCeAutomaticoNoPagamento"
                                                     styleClass="campos tooltipster" title="Ao habilitar essa configuração, o sistema realizará a emissão da NFC-e imediatamente após o clique no botão ?Concluir? no lançamento de pagamento."
                                                     value="#{EmpresaControle.empresaVO.envioNFCeAutomaticoNoPagamento}"/>
                        </h:panelGrid>

                        <h:panelGrid id="panelDayUse" columns="1" width="100%" headerClass="subordinado" columnClasses="colunaCentralizada">
                            <f:facet name="header">
                                <h:outputText value="Configurações para Day Use e Dia Plus"/>
                            </f:facet>
                        </h:panelGrid>
                        <h:panelGrid columns="2" rowClasses="linhaImpar, linhaPar" columnClasses="classEsquerda, classDireita" width="100%">
                            <h:outputText styleClass="tituloCampos" value="Produto sugerido ao Day Use:"/>
                            <h:selectOneMenu value="#{EmpresaControle.empresaVO.produtoDayUse.codigo}"
                                             onblur="blurinput(this);" onfocus="focusinput(this);" styleClass="form">
                                <f:selectItems value="#{EmpresaControle.listaSelectItemProdutoDayUse}"/>
                            </h:selectOneMenu>

                            <h:outputText styleClass="tituloCampos" value="Modalidade sugerida ao Day Use:"/>
                            <h:selectOneMenu value="#{EmpresaControle.empresaVO.modalidadeDayUse.codigo}"
                                             onblur="blurinput(this);" onfocus="focusinput(this);" styleClass="form">
                                <f:selectItems value="#{EmpresaControle.listaSelectItemModalidadeDayUse}"/>
                            </h:selectOneMenu>

                            <h:outputText styleClass="tituloCampos" value="Tipos de Planos que podem receber Dia Plus"/>
                            <h:selectOneMenu value="#{EmpresaControle.empresaVO.tipoPlanoDiaPlus.codigo}"
                                             onblur="blurinput(this);" onfocus="focusinput(this);" styleClass="form">
                                <f:selectItems value="#{EmpresaControle.listaSelectItemTipoPlano}"/>
                            </h:selectOneMenu>

                            <h:outputText styleClass="tituloCampos" value="Produto sugerido ao Dia Plus:"/>
                            <h:selectOneMenu value="#{EmpresaControle.empresaVO.produtoDiaPlus.codigo}"
                                             onblur="blurinput(this);" onfocus="focusinput(this);" styleClass="form">
                                <f:selectItems value="#{EmpresaControle.listaSelectItemProdutoDayUse}"/>
                            </h:selectOneMenu>

                            <h:outputText styleClass="tituloCampos" value="Modalidade sugerida ao Dia Plus:"/>
                            <h:selectOneMenu value="#{EmpresaControle.empresaVO.modalidadeDiaPlus.codigo}"
                                             onblur="blurinput(this);" onfocus="focusinput(this);" styleClass="form">
                                <f:selectItems value="#{EmpresaControle.listaSelectItemModalidadeDayUse}"/>
                            </h:selectOneMenu>

                        </h:panelGrid>
                        <h:panelGrid id="panelSMS" columns="1" width="100%" headerClass="subordinado" columnClasses="colunaCentralizada">
                            <f:facet name="header">
                                <h:outputText value="Configurações SMS"/>
                            </f:facet>
                        </h:panelGrid>
                        <h:panelGrid columns="2" rowClasses="linhaImpar, linhaPar" columnClasses="classEsquerda, classDireita" width="100%">
                            <h:outputText styleClass="tituloCampos"
                                          value="#{msg_aplic.prt_Empresa_tokenSMS}" />
                            <h:inputText id="tokenSMStokenSMSApresentar" disabled="true"
                                         rendered="#{!EmpresaControle.podeInformarTokenSMS}"
                                         size="50" maxlength="100" onblur="blurinput(this);" onfocus="focusinput(this);"
                                         styleClass="form" value="#{EmpresaControle.empresaVO.tokenSMSApresentar}" />
                            <h:inputText id="tokenSMS"
                                         rendered="#{EmpresaControle.podeInformarTokenSMS}"
                                         size="50" maxlength="100" onblur="blurinput(this);" onfocus="focusinput(this);"
                                         styleClass="form" value="#{EmpresaControle.empresaVO.tokenSMS}" />

                            <h:outputText styleClass="tituloCampos"
                                          value="#{msg_aplic.prt_Empresa_tokenSMSShortCode}" />
                            <h:inputText id="tokenSMSShortCodeApresentar" disabled="true"
                                         rendered="#{!EmpresaControle.podeInformarTokenSMSShortCode}"
                                         size="50" maxlength="100" onblur="blurinput(this);" onfocus="focusinput(this);"
                                         styleClass="form" value="#{EmpresaControle.empresaVO.tokenSMSShortCodeApresentar}"/>
                            <h:inputText id="tokenSMSShortCode"
                                         rendered="#{EmpresaControle.podeInformarTokenSMSShortCode}"
                                         size="50" maxlength="100" onblur="blurinput(this);" onfocus="focusinput(this);"
                                         styleClass="form" value="#{EmpresaControle.empresaVO.tokenSMSShortCode}"/>
                        </h:panelGrid>
                    </rich:tab>

                    <rich:tab label="Cancelamento">

                        <h:panelGrid id="cfgCancelamentoEmpresa" columns="2" rowClasses="linhaImpar, linhaPar"
                                     columnClasses="classEsquerda, classDireita" width="100%">

                            <h:outputText styleClass="tituloCampos"
                                          value="#{msg_aplic.prt_retrocederValorMensalPlanoCancelamento}:" />
                            <h:selectBooleanCheckbox id="retrocederValorMensalPlanoCancelamento" styleClass="campos"
                                                     value="#{EmpresaControle.empresaVO.retrocederValorMensalPlanoCancelamento}" />

                            <h:outputText styleClass="tituloCampos tooltipster" value="Enviar Email Cancelamento:"
                                          title="Ao habilitar essa configuração será apresentado o botão \" Enviar Email\" ao
                                    término do cancelamento." />
                            <h:selectBooleanCheckbox id="enviarEmailCancelamento" styleClass="campos tooltipster"
                                                     title="Ao habilitar essa configuração será apresentado o botão \" Enviar Email\" ao
                                    término do cancelamento."
                                                     value="#{EmpresaControle.empresaVO.enviarEmailCancelamento}" />

                            <h:outputText styleClass="tituloCampos"
                                          value="Tipo de cancelamento de planos recorrentes:"/>
                            <h:selectOneMenu value="#{EmpresaControle.empresaVO.tipoCancelamento}"
                                             onblur="blurinput(this);" onfocus="focusinput(this);" styleClass="form">
                                <f:selectItems value="#{EmpresaControle.listaTipoCancelamento}"/>
                                <a4j:support event="onchange" reRender="cfgCancelamentoEmpresa"/>
                            </h:selectOneMenu>

                            <h:panelGroup>
                                <h:outputText styleClass="tituloCampos"
                                              rendered="#{EmpresaControle.empresaVO.tipoCancelamento == 'CANCELAMENTO_AVALIANDO_PARCELAS'
                                                          or EmpresaControle.empresaVO.tipoCancelamento == 'CANCELAMENTO_ANTECEDENCIA'}"
                                              title="Aplicar percentual da multa(definido no cadastro do plano) em relação ao total do contrato"
                                              value="Aplicar percentual da multa em relação ao total do contrato:" />
                            </h:panelGroup>
                            <h:panelGroup>
                                <h:selectBooleanCheckbox styleClass="tooltipster campos" id="chkMultaTotalContrato"
                                                         rendered="#{EmpresaControle.empresaVO.tipoCancelamento == 'CANCELAMENTO_AVALIANDO_PARCELAS'
                                                                     or EmpresaControle.empresaVO.tipoCancelamento == 'CANCELAMENTO_ANTECEDENCIA'}"
                                                         title="Aplicar percentual da multa(definido no cadastro do plano) em relação ao total do contrato"
                                                         value="#{EmpresaControle.empresaVO.aplicarMultaSobreValorTotalContrato}" />
                             </h:panelGroup>

                            <c:if test="${EmpresaControle.empresaVO.cancelamentoAntecipado eq true && EmpresaControle.empresaVO.cancelamentoObrigatoriedadePagamento eq false}">

                                <h:outputText styleClass="tituloCampos"
                                              value="Avisar com até quantos dias de antecedência?" />
                                <h:inputText id="cancelamentoAntecipadoDias" size="10" onblur="blurinput(this);"
                                             onfocus="focusinput(this);" styleClass="form"
                                             value="#{EmpresaControle.empresaVO.cancelamentoAntecipadoDias}" />

                                <h:outputText styleClass="tituloCampos"
                                              value="Porcentagem da multa sobre parcelas atrasadas:" />
                                <h:panelGroup>
                                    <h:inputText id="cancelamentoAntecipadoMulta" size="5"
                                                 value="#{EmpresaControle.empresaVO.cancelamentoAntecipadoMulta}"
                                                 onkeypress="return formatar_moeda(this,'.',',',event);"
                                                 onblur="blurinput(this);" onfocus="focusinput(this);" styleClass="form">
                                        <f:converter converterId="FormatadorNumerico" />
                                    </h:inputText>
                                    <rich:spacer width="5" />
                                    <h:outputText styleClass="tituloCampos" value="%" />
                                </h:panelGroup>


                                <h:outputText styleClass="tituloCampos"
                                              value="Cobrar multa sobre parcelas restantes em aberto dos seguintes planos:" />
                                <h:inputText size="50" maxlength="120" id="cancelamentoAntecipadoPlanos"
                                             onblur="blurinput(this);" onfocus="focusinput(this);" styleClass="form"
                                             title="Informe os códigos dos planos SEPARADO POR VIRGULA (,). Exemplo: 1,2,13,31"
                                             value="#{EmpresaControle.empresaVO.cancelamentoAntecipadoPlanos}" />


                                <h:outputText styleClass="tituloCampos"
                                              value="Porcentagem da multa sobre parcelas restantes em aberto:" />
                                <h:panelGroup>
                                    <h:inputText id="cancelamentoAntecipadoPlanosMulta" size="5"
                                                 value="#{EmpresaControle.empresaVO.cancelamentoAntecipadoPlanosMulta}"
                                                 onkeypress="return formatar_moeda(this,'.',',',event);"
                                                 onblur="blurinput(this);" onfocus="focusinput(this);" styleClass="form">
                                        <f:converter converterId="FormatadorNumerico" />
                                    </h:inputText>
                                    <rich:spacer width="5" />
                                    <h:outputText styleClass="tituloCampos" value="%" />
                                </h:panelGroup>


                                <h:outputText styleClass="tituloCampos"
                                              value="Cobrar multa sobre parcelas restantes para planos lançados até a data:" />
                                <rich:calendar id="cancelamentoAntecipadoPlanosData"
                                               value="#{EmpresaControle.empresaVO.cancelamentoAntecipadoPlanosData}" inputSize="10"
                                               oninputblur="blurinput(this);" oninputfocus="focusinput(this);"
                                               oninputchange="return validar_Data(this.id);" datePattern="dd/MM/yyyy"
                                               enableManualInput="true" zindex="2" showWeeksBar="false" />

                                <h:outputText styleClass="tituloCampos"
                                              value=" Atingir somente contratos lançados a partir do dia:" />
                                <rich:calendar id="cancelamentoAntecipadoSomenteContratosDepoisDe"
                                               value="#{EmpresaControle.empresaVO.cancelamentoAntecipadoContratosDepoisDe}"
                                               inputSize="10" oninputblur="blurinput(this);" oninputfocus="focusinput(this);"
                                               oninputchange="return validar_Data(this.id);" datePattern="dd/MM/yyyy"
                                               enableManualInput="true" zindex="2" showWeeksBar="false" />
                            </c:if>

                            <c:if test="${EmpresaControle.empresaVO.cancelamentoAntecipado}">
                                <h:outputText styleClass="tituloCampos"
                                              title="Ao ativar esta configuração, o sistema criará uma parcela separada para a multa do cancelamento com cálculo de antecedência. Além disso, as parcelas que ainda precisam ser cobradas de acordo com o cálculo do cancelamento, permanecerão em aberto."
                                              value="Gerar parcela de multa separada no cancelamento com cálculo de antecedência:"/>
                                <h:selectBooleanCheckbox id="cancelamentoAntecipadoGerarParcelaMultaSeparada"
                                                         styleClass="tooltipster campos"
                                                         value="#{EmpresaControle.empresaVO.cancelamentoAntecipadoGerarParcelaMultaSeparada}"/>
                                <h:outputText styleClass="tituloCampos"
                                              title="Ao ativar esta configuração, o usuário poderá informar manualmente a data final do contrato durante o cancelamento."
                                              value="Permitir alterar data final de contrato no cancelamento:"/>
                                <h:selectBooleanCheckbox id="permitirAlterarDataFinalContratoNoCancelamento"
                                                         styleClass="tooltipster campos"
                                                         value="#{EmpresaControle.empresaVO.permitirAlterarDataFinalContratoNoCancelamento}"/>
                                <h:outputText styleClass="tituloCampos"
                                              title="Ao ativar esta configuração, o cancelamento automático de contratos fora do regime de recorrência, utilizará o cálculo de antecedência para o vencimento da próxima parcela ."
                                              value="Utilizar cálculo de antecedência também no cancelamento automático de contratos fora do regime de recorrência"/>
                                <h:selectBooleanCheckbox id="cancelamentoAutomaticoAntecipadoContratoForaRecorrencia"
                                                         styleClass="tooltipster campos"
                                                         value="#{EmpresaControle.empresaVO.cancelamentoAutomaticoAntecipadoContratoForaRecorrencia}"/>
                            </c:if>

                            <c:if test="${EmpresaControle.empresaVO.cancelamentoAntecipado eq true || EmpresaControle.empresaVO.cancelamentoObrigatoriedadePagamento eq true}">
                                <h:outputText styleClass="tituloCampos tooltipster"
                                              title="Ao marcar esta configuração, o sistema irá utilizar a data informada no cancelamento para validar as parcelas vencidas que seram cobradas do aluno."
                                              value="Avaliar Data Cancelamento para Validar Parcelas Vencidas:" />
                                <h:selectBooleanCheckbox id="utilizarDataCancelamentoParaValidarParcelas" styleClass="campos tooltipster"
                                                         title="Ao marcar esta configuração, o sistema irá utilizar a data informada no cancelamento para validar as parcelas vencidas que seram cobradas do aluno. "
                                                         value="#{EmpresaControle.empresaVO.utilizarDataCancelamentoParaValidarParcelas}" />
                            </c:if>
                            <h:outputText styleClass="tituloCampos tooltipster"
                                          title="Ao marcar esta configuração, o sistema irá gerar remessas de produtos do tipo QU, que nada mais é do que parcelas que foram geradas após o cancelamento do contrato de um aluno. Serão geradas na data de cobrança da parcela normalmente. "
                                          value="Gerar Remessa de Produto do Tipo \" QU \" Quitação de Cancelamento:" />
                            <h:selectBooleanCheckbox id="gerarRemessaContratoCancelado" styleClass="campos tooltipster"
                                                     title="Ao marcar esta configuração, o sistema irá gerar remessas de produtos do tipo QU, que nada mais é do que parcelas que foram geradas após o cancelamento do contrato de um aluno. Serão geradas na data de cobrança da parcela normalmente. "
                                                     value="#{EmpresaControle.empresaVO.gerarRemessaContratoCancelado}" />

                            <h:outputText styleClass="tituloCampos"
                                          value="Cancelamento automático de parcelas para contratos em regime de recorrência:" />
                            <h:panelGroup>
                                <h:selectOneMenu id="comboTipoParcelaCancelamento" onblur="blurinput(this);"
                                                 onfocus="focusinput(this);" styleClass="form"
                                                 value="#{EmpresaControle.empresaVO.tipoParcelaCancelamento}">
                                    <f:selectItems value="#{EmpresaControle.listaTipoParcelasCancelamento}" />
                                    <a4j:support event="onchange" reRender="cfgEmpresa,cfgCancelamentoEmpresa" />
                                </h:selectOneMenu>
                                <rich:toolTip
                                        value="Esta configuração afeta somente contratos em regime de recorrência."
                                        for="comboTipoParcelaCancelamento" />
                            </h:panelGroup>
                            <h:outputText styleClass="tituloCampos tooltipster" rendered="#{EmpresaControle.empresaVO.tipoParcelaCancelamento eq 'MI' or EmpresaControle.empresaVO.tipoParcelaCancelamento eq 'MA'}"
                                          title="Ao marcar esta configuração, o sistema irá zerar os valores a serem transferidos no cancelamento. "
                                          value="Zerar valor no Cancelamento:" />
                            <h:panelGroup rendered="#{EmpresaControle.empresaVO.tipoParcelaCancelamento eq 'MI' or EmpresaControle.empresaVO.tipoParcelaCancelamento eq'MA'}">

                                <h:selectBooleanCheckbox id="zerarValorCancelamento" styleClass="campos tooltipster"
                                                             title="Ao marcar esta configuração, o sistema irá zerar os valores a serem transferidos ou devolvidos no cancelamento.  "
                                                             value="#{EmpresaControle.empresaVO.zerarValorCancelamentoTransferencia}" />
                            </h:panelGroup>
                            <h:panelGroup id="labelNumeroParcelas">
                                <h:outputText styleClass="tituloCampos"
                                              value="Tolerância de parcelas vencidas seguidas para cancelamento automático de contratos em regime de recorrência:" />
                            </h:panelGroup>
                            <h:panelGroup id="comboNumeroParcelas">
                                <rich:inputNumberSpinner id="spinnerNumeroParcelasCancelamento"
                                                         value="#{EmpresaControle.empresaVO.quantidadeParcelasSeguidasCancelamento}"
                                                         maxValue="99" minValue="0" />
                                <rich:toolTip
                                        value="Até o valor informado o sistema não cancela.Acima desse valor, os contratos serão cancelados. Caso informado 0 será considerado como desabilitado, afeta somente contratos em regime de recorrência."
                                        for="spinnerNumeroParcelasCancelamento" />
                            </h:panelGroup>
                            <h:outputText styleClass="tituloCampos"
                                          value="Cancelamento automático de parcelas para contratos sem regime de recorrência:"
                                          rendered="false" />
                            <h:panelGroup rendered="false">
                                <h:selectOneMenu id="comboTipoParcelaCancelamentoForaRegimeRecorrencia"
                                                 onblur="blurinput(this);" onfocus="focusinput(this);" styleClass="form"
                                                 value="#{EmpresaControle.empresaVO.tipoParcelaCancelamentoForaRegimeRecorrencia}">
                                    <f:selectItems value="#{EmpresaControle.listaTipoParcelasCancelamento}" />
                                    <a4j:support event="onchange" reRender="cfgEmpresa" />
                                </h:selectOneMenu>
                                <rich:toolTip
                                        value="Esta configuração afeta somente contratos fora do regime de recorrência."
                                        for="comboTipoParcelaCancelamentoForaRegimeRecorrencia" />
                            </h:panelGroup>
                            <h:panelGroup id="labelNumeroParcelasForaRegimeRecorrencia"
                                          rendered="#{EmpresaControle.empresaVO.cancelamentoTodasParcelasForaRegimeRecorrencia}">
                                <h:outputText styleClass="tituloCampos"
                                              value="Tolerância de parcelas vencidas seguidas para cancelamento automático de contratos fora do regime de recorrência:" />
                            </h:panelGroup>
                            <h:panelGroup id="comboNumeroParcelasForaRegimeRecorrencia"
                                          rendered="#{EmpresaControle.empresaVO.cancelamentoTodasParcelasForaRegimeRecorrencia}">
                                <rich:inputNumberSpinner id="spinnerNumeroParcelasCancelamentoForaRegimeRecorrencia"
                                                         value="#{EmpresaControle.empresaVO.quantidadeParcelasSeguidasCancelamentoForaRegimeRecorrencia}"
                                                         maxValue="99" minValue="0" />
                                <rich:toolTip
                                        value="Até o valor informado o sistema não cancela.Acima desse valor, os contratos serão cancelados. Caso informado 0 será considerado como desabilitado, afeta somente contratos fora do regime de recorrência que são Renováveis Automaticamente."
                                        for="spinnerNumeroParcelasCancelamentoForaRegimeRecorrencia" />
                            </h:panelGroup>
                            <h:panelGroup id="labelQuantidadeDiasUteisAposVencimentoParaCancelarContratoForaRegimeRecorrencia">
                                <h:outputText styleClass="tituloCampos"
                                              value="Quantidade de dias úteis após o vencimento da parcela para que o contrato seja cancelado fora do regime de recorrência:" />
                            </h:panelGroup>
                            <h:panelGroup id="comboQuantidadeDiasUteisAposVencimentoParaCancelarContratoForaRegimeRecorrencia">
                                <rich:inputNumberSpinner id="spinnerQuantidadeDiasUteisAposVencimentoParaCancelarContrato"
                                                         value="#{EmpresaControle.empresaVO.quantidadeDiasUteisAposVencimentoParaCancelarContrato}"
                                                         maxValue="99" minValue="0" />
                                <rich:toolTip
                                        value="Até o valor informado o sistema não cancela.Acima desse valor, os contratos serão cancelados. Caso informado 0 será considerado como desabilitado, afeta somente contratos fora do regime de recorrência que são Renováveis Automaticamente."
                                        for="spinnerQuantidadeDiasUteisAposVencimentoParaCancelarContrato" />
                            </h:panelGroup>
                            <h:outputText styleClass="tituloCampos"
                                          title="Essa configuração, se marcada, irá considerar apenas parcelas que tem produtos do tipo PM no cancelamento automático"
                                          value="Considerar apenas parcelas do tipo Plano para o cancelamento automático: " />
                            <h:selectBooleanCheckbox styleClass="campos"
                                                     value="#{EmpresaControle.empresaVO.considerarSomenteParcelasPlanos}" />

                            <c:if test="${EmpresaControle.empresaVO.cancelamentoObrigatoriedadePagamento eq true || !(EmpresaControle.empresaVO.cancelamentoAntecipado eq true && !EmpresaControle.empresaVO.cancelamentoObrigatoriedadePagamento)}">
                                <h:outputText styleClass="tituloCampos"
                                              value="Não gerar valores no cancelamento automático:" />
                                <h:selectBooleanCheckbox styleClass="tooltipster campos"
                                                         id="naoGerarResiduoCancelamentoAutomatico"
                                                         value="#{EmpresaControle.empresaVO.naoGerarResiduoCancelamentoAutomatico}">
                                    <a4j:support event="onchange" reRender="cfgCancelamentoEmpresa"/>
                                </h:selectBooleanCheckbox>

                                <c:if test="${not EmpresaControle.empresaVO.naoGerarResiduoCancelamentoAutomatico}">
<%--                                    <h:outputText styleClass="tituloCampos"--%>
<%--                                                  value="Depositar o resíduo do cancelamento automático na conta corrente do aluno:" />--%>
<%--                                    <h:selectBooleanCheckbox styleClass="tooltipster campos" id="depositarResiduoCancelamentoNaContaCorrente"--%>
<%--                                                             value="#{EmpresaControle.empresaVO.depositarResiduoCancelamentoNaContaCorrente}" />--%>

                                    <h:outputText styleClass="tituloCampos"
                                                  title="Essa configuração, se marcada, gera uma parcela para quitação do cancelamento ao invés deixar o valor do cancelamento na conta corrente do aluno"
                                                  value="Gerar parcela de quitação no cancelamento automático: "/>
                                    <h:selectBooleanCheckbox styleClass="campos"
                                                             value="#{EmpresaControle.empresaVO.gerarQuitacaoCancelamentoAuto}"/>
                                </c:if>
                            </c:if>

                            <h:outputText styleClass="tituloCampos"
                                          title="Caso essa configuração esteja ativada, ao realizar uma mudança de plano que seja um downgrade (mudar para um plano de valor menor), uma multa será aplicada."
                                          value="Aplicar multa na mudança de plano: "/>
                            <h:selectBooleanCheckbox styleClass="campos"
                                                     value="#{EmpresaControle.empresaVO.aplicarMultaMudancaPlano}"/>

                            <h:outputText styleClass="tituloCampos"
                                          title="Essa configuração, se marcada, aplicará  todos os valores oriundos do cancelamento na conta corrente do aluno"
                                          value="Aplicar Multa/Juros nas parcelas atrasadas do cancelamento: "/>
                            <h:selectBooleanCheckbox styleClass="campos"
                                                     value="#{EmpresaControle.empresaVO.aplicarMultaeJurosNoCancelamentoAutomatico}"/>

                            <h:outputText styleClass="tituloCampos"
                                          title="Essa configuração, se marcada, permite que no cancelamento de um contrato seja gerado um saldo proporcional aos dias utilizados durante o contrato."
                                          value="Gerar parcela de quitação proporcional com saldo remanescente: " />
                            <h:selectBooleanCheckbox styleClass="campos" value="#{EmpresaControle.empresaVO.gerarQuitacaoCancelamentoRemanescente}" />

                            <h:outputText styleClass="tituloCampos"
                                          value="Não cobrar multa de cancelamento de contrato do tipo renovação:" />
                            <h:selectBooleanCheckbox styleClass="tooltipster campos" id="cobrarMultaDeContratoRenovado"
                                                     value="#{EmpresaControle.empresaVO.naoCobrarMultaDeContratoRenovado}" />
                            <h:outputText styleClass="tituloCampos"
                                          value="Não cobrar multa de cancelamento se todas as parcelas estiverem pagas:" />
                            <h:selectBooleanCheckbox styleClass="tooltipster campos" id="cobrarMultaDeTodasParcelasPagas"
                                                     value="#{EmpresaControle.empresaVO.naoCobrarMultaDeTodasParcelasPagas}" />

                            <c:if test="${EmpresaControle.usuarioLogado.usuarioPactoSolucoes && EmpresaControle.empresaVO.tipoCancelamento == 'CANCELAMENTO_AVALIANDO_PARCELAS'}">
                                <h:outputText styleClass="tituloCampos"
                                              value="Apresentar transações aprovadas no cancelamento:"/>
                                <h:selectBooleanCheckbox id="cancelamentoApresentarTransacoes"
                                                         styleClass="tooltipster campos"
                                                         value="#{EmpresaControle.empresaVO.cancelamentoApresentarTransacoes}"/>
                            </c:if>

                                <h:outputText styleClass="tituloCampos"
                                              value="Isentar cancelamento até 7 dias:"/>
                                <h:selectBooleanCheckbox id="isentarCancelamento7Dias"
                                                         styleClass="tooltipster campos"
                                                         value="#{EmpresaControle.empresaVO.isentarCancelamento7Dias}"/>

                            <h:outputText styleClass="tituloCampos"
                                          value="Cancelar automaticamente contratos não renováveis fora do regime recorrência:"/>
                            <h:selectBooleanCheckbox id="cancelarContratosNaoRenovaveisForaRecorrencia"
                                                     styleClass="tooltipster campos"
                                                     value="#{EmpresaControle.empresaVO.cancelarContratosNaoRenovaveisForaRecorrencia}"/>
                            <h:outputText styleClass="tituloCampos"
                                          title="Informe quantos dias será tolerado para cancelar automaticamente contratos não assinados (0 = Desativado)"
                                          value="Tolerância para cancelar automaticamente contratos não assinados:"/>
                            <h:inputText onkeypress="return mascara(this.form, this.id, '999', event);"
                                         id="toleranciaCancelarContratosNaoAssinados" size="3" maxlength="3"
                                         onblur="blurinput(this);" onfocus="focusinput(this);"
                                         styleClass="form tooltipster"
                                         title="Informe quantos dias será tolerado para cancelar automaticamente contratos não assinados (0 = Desativado)"
                                         value="#{EmpresaControle.empresaVO.toleranciaCancelarContratosNaoAssinados}"/>
                            <h:outputText styleClass="tituloCampos"
                                          value="Marcar pra devolver recebíveis automaticamente de cheque e cartão:"/>
                            <h:selectBooleanCheckbox id="********************************"
                                                     styleClass="tooltipster campos"
                                                     value="#{EmpresaControle.empresaVO.********************************Cancelamento}"/>
                            <c:if test="${EmpresaControle.configuracaoSistema.sesc}">
                                <h:outputText styleClass="tituloCampos"
                                              value="Utiliza configuração cancelamento SESC:"/>
                                <h:selectBooleanCheckbox id="utilizaConfigCancelamentoSesc"
                                                         styleClass="tooltipster campos"
                                                         value="#{EmpresaControle.empresaVO.utilizaConfigCancelamentoSesc}"/>
                            </c:if>
                        </h:panelGrid>

                        <h:panelGrid id="panelCancelDevolveProdEmpresa" columns="1" width="100%" headerClass="subordinado"
                                     columnClasses="colunaCentralizada">
                            <f:facet name="header">
                                <h:outputText value="#{msg_aplic.prt_CancelDevolveProdEmpresa_tituloForm}" />
                            </f:facet>
                            <h:panelGrid columns="2" width="100%" rowClasses="linhaImpar, linhaPar"
                                         columnClasses="classEsquerda, classDireita">
                                <h:outputText styleClass="tituloCampos"
                                              value="#{msg_aplic.prt_Produto_tituloForm}" />
                                <h:panelGroup>
                                    <h:selectOneMenu id="CancelDevolveProdEmpresa_Produto" onblur="blurinput(this);"
                                                     onfocus="focusinput(this);" styleClass="form"
                                                     value="#{EmpresaControle.produtoDevolverCancelamentoEmpresaVO.produto}">
                                        <f:selectItems value="#{EmpresaControle.listaSelectItemProdutoDevolverCancelamentoEmpresa}" />
                                    </h:selectOneMenu>
                                    <a4j:commandButton id="atualizar_CancelDevolveProdEmpresa"
                                                       action="#{EmpresaControle.montarListaSelectItemProdutoDevolverCancelamentoEmpresa()}"
                                                       image="imagens/atualizar.png" immediate="true" ajaxSingle="true"
                                                       reRender="form:CancelDevolveProdEmpresa_Produto" />
                                </h:panelGroup>
                            </h:panelGrid>
                            <a4j:commandButton action="#{EmpresaControle.adicionarProdutoDevolverCancelamentoeEmpresa}"
                                               reRender="panelCancelDevolveProdEmpresa, panelErroMensagem"
                                               focus="form:ContaCorrenteEmpresa_contaCorrente" value="#{msg_bt.btn_adicionar}"
                                               image="./imagens/botaoAdicionar.png" accesskey="5" styleClass="botoes" />

                            <h:panelGrid columns="1" width="100%" styleClass="tabFormSubordinada">
                                <rich:dataTable id="produtoDevolvCancelamentoEmpresaVO" width="100%" headerClass="subordinado"
                                             styleClass="tabFormSubordinada" rowClasses="linhaImpar, linhaPar"
                                             columnClasses="colunaAlinhamento"
                                             value="#{EmpresaControle.empresaVO.produtoDevolverCancelamentoEmpresaVOS}"
                                             var="produtoDevolvCancelamentoEmpresa">
                                    <rich:column width="10%">
                                        <f:facet name="header">
                                            <h:outputText value="C¥digo" />
                                        </f:facet>
                                        <h:outputText value="#{produtoDevolvCancelamentoEmpresa.codigo}" />
                                    </rich:column>
                                    <rich:column width="10%">
                                        <f:facet name="header">
                                            <h:outputText value="C¥digo Produto" />
                                        </f:facet>
                                        <h:outputText value="#{produtoDevolvCancelamentoEmpresa.produtoVO.codigo}" />
                                    </rich:column>
                                    <rich:column>
                                        <f:facet name="header">
                                            <h:outputText value="Produto" />
                                        </f:facet>
                                        <h:outputText value="#{produtoDevolvCancelamentoEmpresa.produtoVO.descricao}" />
                                    </rich:column>
                                    <rich:column width="10%">
                                        <f:facet name="header">
                                            <h:outputText value="#{msg_bt.btn_opcoes}" />
                                        </f:facet>
                                        <h:commandButton id="removerItemVendaProdDeolv" immediate="true"
                                                         action="#{EmpresaControle.removerProdDevolvCancelEmpresa}"
                                                         value="#{msg_bt.btn_excluir}" image="./imagens/botaoRemover.png"
                                                         accesskey="7" styleClass="botoes" />
                                    </rich:column>
                                </rich:dataTable>
                            </h:panelGrid>
                        </h:panelGrid>
                    </rich:tab>

                    <rich:tab label="Acesso">
                        <h:panelGrid columns="2" rowClasses="linhaImpar, linhaPar"
                            columnClasses="classEsquerda, classDireita" width="100%">
                            <h:outputText styleClass="tituloCampos"
                                value="#{msg_aplic.prt_ConfiguracaoSistema_toleranciaPagamento}" />
                            <h:inputText id="toleranciaPagamento" size="10" maxlength="20" onblur="blurinput(this);"
                                onfocus="focusinput(this);" styleClass="form"
                                value="#{EmpresaControle.empresaVO.toleranciaPagamento}" />
                            <h:outputText styleClass="tituloCampos"
                                value="#{msg_aplic.prt_ConfiguracaoSistema_tentativasliberarparcelavencida}" />
                            <h:inputText id="tentativasliberarparcelavencida"
                                title="O aluno terá acesso liberado caso a parcela esteja em remessa ainda não processada e o número de tentativas seja menor ou igual ao informado."
                                size="10" maxlength="20" onblur="blurinput(this);" onfocus="focusinput(this);"
                                styleClass="form"
                                value="#{EmpresaControle.empresaVO.tentativasLiberarParcelaVencida}" />

                            <h:outputText styleClass="tituloCampos"
                                value="#{msg_aplic.prt_ConfiguracaoSistema_toleranciaConratoVencido}" />
                            <h:inputText id="toleranciaContratoVencido" size="10" maxlength="20"
                                onblur="blurinput(this);" onfocus="focusinput(this);"
                                title="Informe a quantidade de dias que o cliente poderá acessar a academia após o vencimento do contrato."
                                styleClass="form" value="#{EmpresaControle.empresaVO.toleranciaDiasContratoVencido}" />
                            <h:outputText styleClass="tituloCampos"
                                value="#{msg_aplic.prt_ConfiguracaoSistema_toleranciaOcupacaoTurma}" />
                            <h:inputText id="toleranciaOcupacaoTurma" size="10" maxlength="20" onblur="blurinput(this);"
                                onfocus="focusinput(this);"
                                title="Informe a quantidade de dias que o cliente continuará na turma após o vencimento do contrato."
                                styleClass="form" value="#{EmpresaControle.empresaVO.toleranciaOcupacaoTurma}" />

                            <h:outputText styleClass="tituloCampos"
                                value="Minutos após último acesso para diminuir crédito de aula:" />
                            <h:inputText id="minutosDiminuirCreditoAula" size="10" maxlength="20"
                                onblur="blurinput(this);" onfocus="focusinput(this);"
                                title="Informe a quantidade de minutos que será considerado após o último acesso para que o aluno perca um crédito de aula do total que tem direito."
                                styleClass="form"
                                value="#{EmpresaControle.empresaVO.minutosAposUltimoAcessoDiminuirCredito}" />

                            <h:outputText styleClass="tituloCampos"
                                value="#{msg_aplic.prt_ConfiguracaoSistema_bloquearacessoseparcelaaberta}" />
                            <h:selectBooleanCheckbox styleClass="campos"
                                title="#{msg_aplic.prt_ConfiguracaoSistema_bloquearacessoseparcelaaberta_title}"
                                value="#{EmpresaControle.empresaVO.bloquearAcessoSeParcelaAberta}" />
                            <h:outputText styleClass="tituloCampos"
                                          value="#{msg_aplic.prt_ConfiguracaoSistema_bloquearacessosedebitoemconta}" />
                            <h:selectBooleanCheckbox styleClass="campos"
                                                     title="#{msg_aplic.prt_ConfiguracaoSistema_bloquearacessosedebitoemconta_title}"
                                                     value="#{EmpresaControle.empresaVO.bloquearAcessoSeDebitoEmConta}" />

                            <h:outputText styleClass="tituloCampos" title="Essa configuração irá bloquear o acesso de todos os alunos com contratos sem assinatura digital! Cuidado pois muitos alunos poderão ser bloqueados."
                                          value="#{msg_aplic.prt_ConfiguracaoSistema_bloquearAcessosSemAssinaturaDigital}" />
                            <h:selectBooleanCheckbox styleClass="campos" id="idBloquearAcessoSemAssinaturaDigital" title="Essa configuração irá bloquear o acesso de todos os alunos com contratos sem assinatura digital! Cuidado pois muitos alunos poderão ser bloqueados."
                                                     value="#{EmpresaControle.empresaVO.bloquearAcessoSemAssinaturaDigital}" />

                            <h:outputText styleClass="tituloCampos" title="Essa configuração irá bloquear o acesso de todos os alunos sem o termo de responsabilidade assinado! Cuidado pois muitos alunos poderão ser bloqueados."
                                          value="#{msg_aplic.prt_ConfiguracaoSistema_bloquearAcessoSemTermoResponsabilidade}" />
                            <h:selectBooleanCheckbox styleClass="campos" id="idBloquearAcessoSemTermoResponsabilidade" title="Essa configuração irá bloquear o acesso de todos os alunos sem o termo de responsabilidade assinado! Cuidado pois muitos alunos poderão ser bloqueados."
                                                     value="#{EmpresaControle.empresaVO.bloquearAcessoSemTermoResponsabilidade}" />

                            <h:outputText styleClass="tituloCampos" title="Essa configuração irá bloquear o acesso de todos os Professores e/ou Personais com CREF vencido! Cuidado pois muitos Professores e/ou Personais poderão ser bloqueados."
                                          value="#{msg_aplic.prt_ConfiguracaoSistema_bloquearAcessoCrefVencido}" />
                            <h:selectBooleanCheckbox styleClass="campos" id="bloquearAcessoCrefVencido" title="Essa configuração irá bloquear o acesso de todos os Professores e/ou Personais com CREF vencido! Cuidado pois muitos Professores e/ou Personais poderão ser bloqueados."
                                                     value="#{EmpresaControle.empresaVO.bloquearAcessoCrefVencido}" />

                            <h:outputText styleClass="tituloCampos"
                                value="#{msg_aplic.prt_ConfiguracaoSistema_bloquearacessosearmariovigenciavencida}" />
                            <h:selectBooleanCheckbox styleClass="campos"
                                value="#{EmpresaControle.empresaVO.bloquearAcessoArmarioVigenciaVencida}" />

                            <h:outputText styleClass="tituloCampos"
                                          value="#{msg_aplic.prt_ConfiguracaoSistema_bloquearacessomatricularematriculatotemsempagamento}" />
                            <h:selectBooleanCheckbox styleClass="campos"
                                                     value="#{EmpresaControle.empresaVO.bloquearAcessoMatriculaRematriculaTotemSemPagamento}" />

                            <c:if test="${EmpresaControle.usuarioLogado.administrador}">
                                <h:outputText styleClass="tituloCampos"
                                              value="Utilizar senha de 11 dígitos na catraca:"/>
                                <h:selectBooleanCheckbox rendered="#{EmpresaControle.usuarioLogado.administrador}"
                                                         styleClass="campos"
                                                         value="#{EmpresaControle.empresaVO.senhaAcessoOnzeDigitos}">
                                    <a4j:support event="onchange"
                                                 action="#{EmpresaControle.desmarcarDefinirCpfComoSenha}"
                                                 reRender="labelDefinirCpfComoSenha,chkDefinirCpfComoSenha"> </a4j:support>
                                </h:selectBooleanCheckbox>
                            </c:if>

                            <c:if test="${((EmpresaControle.usuarioLogado.administrador) && (EmpresaControle.empresaVO.senhaAcessoOnzeDigitos))}">
                                <h:panelGroup id="labelDefinirCpfComoSenha">
                                    <h:outputText
                                            title="Ao marcar esta opção o sistema ira definir o CPF do aluno como senha de acesso, assim que o contrato é lançado."
                                            styleClass="tituloCampos"
                                            value="Definir CPF como senha de acesso automaticamente:"/>
                                </h:panelGroup>
                                <h:panelGroup id="chkDefinirCpfComoSenha">
                                    <h:selectBooleanCheckbox
                                            styleClass="campos"
                                            title="Ao marcar esta opção o sistema ira definir o CPF do aluno como senha de acesso, assim que o contrato é lançado."
                                            value="#{EmpresaControle.empresaVO.definirCpfComoSenhaCatraca}"/>
                                </h:panelGroup>
                            </c:if>

                            <h:outputText styleClass="tituloCampos"
                                value="#{msg_aplic.prt_Empresa_codigoChaveIntegracaoDigitais}" />
                            <h:inputText onkeypress="return mascara(this.form, this.id, '9999', event);"
                                id="codigoChaveIntegracaoDigitais" size="4" maxlength="4" onblur="blurinput(this);"
                                onfocus="focusinput(this);" styleClass="form"
                                value="#{EmpresaControle.empresaVO.codigoChaveIntegracaoDigitais}" />


                            <h:outputText styleClass="tituloCampos"
                                          value="* Tempo medio para considerar saida da academia" />
                            <h:inputText id="horaSaidaAcademia" size="10" onblur="blurinput(this);"
                                         onfocus="focusinput(this);"
                                         onkeypress="return mascaraTodos(this.form, 'form:horaSaidaAcademia', '99:99', event);"
                                         styleClass="form" maxlength="5"
                                         value="#{EmpresaControle.empresaVO.tempoSaidaAcademiaFormatada}" />

                            <h:outputText title="Ao marcar esta opção o ZillyonAcessoWeb enviará todas as tentativas de acesso para serem registradas."
                                          styleClass="tituloCampos tooltipster"
                                          value="Registrar tentativas de acesso:"/>
                            <h:selectBooleanCheckbox styleClass="campos tooltipster"
                                                     title="Ao marcar esta opção o ZillyonAcessoWeb enviará todas as tentativas de acesso para serem registradas."
                                                     value="#{EmpresaControle.empresaVO.registrarTentativasAcesso}"/>

                            <h:outputText title="#{msg_aplic.prt_ConfiguracaoSistema_bloquearAcessoAlunoParqNaoAssinado_title}"
                                          styleClass="tituloCampos tooltipster"
                                          value="#{msg_aplic.prt_ConfiguracaoSistema_bloquearAcessoAlunoParqNaoAssinado}"/>
                            <h:selectBooleanCheckbox styleClass="campos tooltipster"
                                                     title="#{msg_aplic.prt_ConfiguracaoSistema_bloquearAcessoAlunoParqNaoAssinado_title}"
                                                     value="#{EmpresaControle.empresaVO.bloquearAcessoAlunoParqNaoAssinado}"/>

                            <h:outputText styleClass="tituloCampos" title="Essa configuração irá bloquear o acesso de todos os alunos com diárias de outras unidades."
                                          value="#{msg_aplic.prt_ConfiguracaoSistema_bloquearAcessoDiariaEmpresaDiferente}" />
                            <h:selectBooleanCheckbox styleClass="campos" id="idBloquearAcessoDiariaEmpresaDiferente" title="Essa configuração irá bloquear o acesso de todos os alunos com diárias de outras unidades! Cuidado pois muitos alunos poderão ser bloqueados."
                                                     value="#{EmpresaControle.empresaVO.bloquearAcessoDiariaEmpresaDiferente}" />

                        </h:panelGrid>
                        <h:panelGrid id="panelWebHookAcesso" columns="1" width="100%" headerClass="subordinado" columnClasses="colunaCentralizada">
                            <f:facet name="header">
                                <h:outputText value="Configuração webhook de envio de acessos" />
                            </f:facet>
                        </h:panelGrid>
                        <h:panelGrid columns="2" rowClasses="linhaImpar, linhaPar"
                                     columnClasses="classEsquerda, classDireita" width="100%">
                            <h:outputText styleClass="tituloCampos" value="URL envio acesso" />
                            <h:inputText id="URLIntegracaoAcesso" size="90" onblur="blurinput(this);"
                                         onfocus="focusinput(this);" styleClass="form"
                                         title="Ao registrar um acesso, sistema irá enviar dados do acesso de alunos que são da integração(contém um id externo)"
                                         value="#{EmpresaControle.empresaVO.urlEnvioAcesso}" />
                            <h:outputText styleClass="tituloCampos" value="Access-Token:" />
                            <h:inputText id="tokenIntegracaoAcesso" size="90" onblur="blurinput(this);"
                                         onfocus="focusinput(this);" styleClass="form"
                                         title="Enviado no header da requisição"
                                         value="#{EmpresaControle.empresaVO.tokenEnvioAcesso}" />
                        </h:panelGrid>

                        <h:panelGrid id="panelAcessoCovid" columns="1" width="100%" headerClass="subordinado" columnClasses="colunaCentralizada">
                            <f:facet name="header">
                                <h:outputText value="Configuração de Acesso durante a Pandemia" />
                            </f:facet>
                        </h:panelGrid>

                        <h:panelGrid columns="2" rowClasses="linhaImpar, linhaPar"
                                     columnClasses="classEsquerda, classDireita" width="100%">
                            <h:outputText styleClass="tituloCampos"
                                          value="Permitir acesso na catraca somente para alunos agendados no Aula Cheia (Recurso COVID-19)"/>
                            <h:panelGroup layout="block" id="panelAcessoSomenteComAgendamento">
                                <h:selectBooleanCheckbox
                                        value="#{EmpresaControle.empresaVO.acessoSomenteComAgendamento}" >
                                    <a4j:support event="onchange" reRender="form" />
                                </h:selectBooleanCheckbox>
                                <a4j:commandLink id="atualizar_AcessoSomenteComAgendamento"
                                                 styleClass="tooltipster"
                                                 title="Atualizar essa configuração para todas as empresas"
                                                 value="Replicar para todas empresas"
                                                 oncomplete="#{EmpresaControle.mensagemNotificar}"
                                                 action="#{EmpresaControle.replicarConfigAcessoSomenteComAgendamento}"/>
                            </h:panelGroup>

                            <h:outputText rendered="#{EmpresaControle.empresaVO.acessoSomenteComAgendamento}"
                                          styleClass="tituloCampos"
                                          value="Tolerância para acesso (em minutos)"/>
                            <h:panelGroup rendered="#{EmpresaControle.empresaVO.acessoSomenteComAgendamento}"
                                          layout="block" id="pnlToleranciaAcesso1">
                                <h:inputText onkeypress="return mascara(this.form, this.id, '999', event);"
                                             id="toleranciaAcesso1" size="3" maxlength="3"
                                             onblur="blurinput(this);" onfocus="focusinput(this);"
                                             styleClass="form tooltipster"
                                             title="Informe quanto tempo antes da aula o acesso dos alunos será liberado"
                                             value="#{EmpresaControle.empresaVO.toleranciaAcessoAula}"/>
                            </h:panelGroup>

                            <h:outputText styleClass="tituloCampos"
                                          title="Ao atingir essa lotação simultânea, o acesso será bloqueado na catraca"
                                          value="Capacidade de alunos simultâneos (Recurso COVID-19)"/>
                            <h:panelGroup layout="block" id="pnlCapacidadeAlunos">
                                <h:inputText onkeypress="return mascara(this.form, this.id, '9999', event);"
                                             id="capacidadeSimultanea" size="4" maxlength="4"
                                             onblur="blurinput(this);" onfocus="focusinput(this);"
                                             styleClass="form tooltipster"
                                             title="Ao atingir essa lotação simultânea, o acesso será bloqueado na catraca"
                                             value="#{EmpresaControle.empresaVO.capacidadeSimultanea}"/>
                            </h:panelGroup>
                            <h:outputText styleClass="tituloCampos" title="Essa configuração irá bloquear o acesso de todas as pessoas que estão acima da idade configurada e não tem cartão de vacina cadastrado"
                                          value="Bloquear alunos sem cartão de vacinação cadastrado:" />
                            <h:selectBooleanCheckbox styleClass="campos" id="idBloquearsemcartaovacina" title="Essa configuração irá bloquear o acesso de todas as pessoas que estão acima da idade configurada e não tem cartão de vacina cadastrado."
                                                     value="#{EmpresaControle.empresaVO.bloquearSemCartaoVacina}" />
                            <h:outputText styleClass="tituloCampos"
                                          value="Idade minima para exigir cartão de vacina:" />
                            <h:inputText id="idadeVacina" size="10" maxlength="20" onblur="blurinput(this);"
                                         onfocus="focusinput(this);" styleClass="form"
                                         value="#{EmpresaControle.empresaVO.idadeMinimaCartaoVacina}" />
                            <h:outputText styleClass="tituloCampos"
                                          value="Tipo Cartão de Vacinação exigido:"/>
                            <h:selectOneMenu value="#{EmpresaControle.empresaVO.tipoAnexoCartaoVacina}"
                                             onblur="blurinput(this);" onfocus="focusinput(this);" styleClass="form">
                                <f:selectItems value="#{EmpresaControle.listaTipoAnexoCartaoVacina}"/>
                            </h:selectOneMenu>
                        </h:panelGrid>
                        <h:panelGrid id="panelAcesso" columns="1" width="100%" headerClass="subordinado" columnClasses="colunaCentralizada">
                            <f:facet name="header">
                                <h:outputText value="Configuração de Acesso para Cliente Em Risco" />
                            </f:facet>
                        </h:panelGrid>
                        <h:panelGrid columns="2" rowClasses="linhaImpar, linhaPar"
                            columnClasses="classEsquerda, classDireita" width="100%">
                            <h:outputText styleClass="tituloCampos"
                                value="Quantidade Máxima de Faltas para Cliente obter Peso 1" />
                            <h:inputText id="qtdFaltaPeso1" size="10" maxlength="20" onblur="blurinput(this);"
                                onfocus="focusinput(this);" styleClass="form"
                                value="#{EmpresaControle.empresaVO.qtdFaltaPeso1}" />
                            <h:outputText styleClass="tituloCampos"
                                value="Quantidade Mínima e Máxima de Faltas para Cliente obter Peso 2" />
                            <h:panelGroup>
                                <h:inputText id="qtdFaltaInicioPeso2" size="10" maxlength="20" onblur="blurinput(this);"
                                    onfocus="focusinput(this);" styleClass="form"
                                    value="#{EmpresaControle.empresaVO.qtdFaltaInicioPeso2}" />
                                <rich:spacer width="5px" />
                                <h:outputText styleClass="tituloCampos" value="até" />
                                <rich:spacer width="5px" />
                                <h:inputText id="qtdFaltaTerminoPeso2" size="10" maxlength="20"
                                    onblur="blurinput(this);" onfocus="focusinput(this);" styleClass="form"
                                    value="#{EmpresaControle.empresaVO.qtdFaltaTerminoPeso2}" />
                            </h:panelGroup>
                            <h:outputText styleClass="tituloCampos"
                                value="Quantidade Mínima de Faltas para Cliente obter Peso 3" />
                            <h:inputText id="qtdFaltaPeso3" size="10" maxlength="20" onblur="blurinput(this);"
                                onfocus="focusinput(this);" styleClass="form"
                                value="#{EmpresaControle.empresaVO.qtdFaltaPeso3}" />
                        </h:panelGrid>

                        <h:panelGrid id="panelAcessoChamada" columns="1" width="100%" headerClass="subordinado"
                            columnClasses="colunaCentralizada">
                            <f:facet name="header">
                                <h:outputText value="Configuração de Acesso para Lista de Chamada" />
                            </f:facet>
                        </h:panelGrid>

                        <h:panelGrid columns="1" width="100%">
                            <h:panelGrid columns="2" rowClasses="linhaImpar, linhaPar"
                                columnClasses="classEsquerda, classDireita" width="100%">
                                <h:outputText styleClass="tituloCampos"
                                    value="#{msg_aplic.prt_ConfiguracaoSistema_acessoListaChamada}" />
                                <h:selectBooleanCheckbox styleClass="campos"
                                    value="#{EmpresaControle.empresaVO.acessoChamada}" />
                                <h:outputText styleClass="tituloCampos"
                                    value="#{msg_aplic.prt_ConfiguracaoSistema_acessoLocalAcesso}" />
                                <h:panelGroup>
                                    <h:selectOneMenu id="localAcessoChamada" onblur="blurinput(this);"
                                        onfocus="focusinput(this);" styleClass="form"
                                        value="#{EmpresaControle.empresaVO.localAcessoChamada.codigo}">
                                        <f:selectItems value="#{EmpresaControle.listaSelectItemLocalAcessoChamada}" />
                                        <a4j:support event="onchange" reRender="coletorChamada"
                                            action="#{EmpresaControle.montarListaSelectItemColetorChamada}" />
                                    </h:selectOneMenu>
                                    <h:message for="localAcessoChamada" styleClass="mensagemDetalhada" />
                                </h:panelGroup>
                                <h:outputText styleClass="tituloCampos"
                                    value="#{msg_aplic.prt_ConfiguracaoSistema_acessoColetor}" />
                                <h:panelGroup>
                                    <h:selectOneMenu id="coletorChamada" onblur="blurinput(this);"
                                        onfocus="focusinput(this);" styleClass="form"
                                        value="#{EmpresaControle.empresaVO.coletorChamada.codigo}">
                                        <f:selectItems value="#{EmpresaControle.listaSelectItemColetorChamada}" />
                                    </h:selectOneMenu>
                                    <h:message for="coletorChamada" styleClass="mensagemDetalhada" />
                                </h:panelGroup>
                            </h:panelGrid>
                        </h:panelGrid>
                    </rich:tab>

                    <rich:tab label="Cobrança" id="tabConfgCobranca">
                        <%@include file="includes/empresa/include_cobranca.jsp"%>
                    </rich:tab>

                    <rich:tab label="Conta Corrente da Empresa">
                        <h:panelGrid id="panelContaCorrentEmpresa" columns="1" width="100%" headerClass="subordinado"
                            columnClasses="colunaCentralizada">
                            <f:facet name="header">
                                <h:outputText value="#{msg_aplic.prt_ContaCorrenteEmpresa_tituloForm}" />
                            </f:facet>
                            <h:panelGrid columns="2" width="100%" rowClasses="linhaImpar, linhaPar"
                                columnClasses="classEsquerda, classDireita">
                                <h:outputText styleClass="tituloCampos"
                                    value="#{msg_aplic.prt_ContaCorrenteEmpresa_contaCorrente}" />
                                <h:panelGroup>
                                    <h:selectOneMenu id="ContaCorrenteEmpresa_contaCorrente" onblur="blurinput(this);"
                                        onfocus="focusinput(this);" styleClass="form"
                                        value="#{EmpresaControle.contaCorrenteEmpresaVO.contaCorrente.codigo}">
                                        <f:selectItems value="#{EmpresaControle.listaSelectItemContaCorrente}" />
                                    </h:selectOneMenu>
                                    <a4j:commandButton id="atualizar_ContaCorrenteEmpresa_contaCorrente"
                                        action="#{EmpresaControle.montarListaSelectItemContaCorrente}"
                                        image="imagens/atualizar.png" immediate="true" ajaxSingle="true"
                                        reRender="form:ContaCorrenteEmpresa_contaCorrente" />
                                </h:panelGroup>
                            </h:panelGrid>
                            <a4j:commandButton action="#{EmpresaControle.adicionarContaCorrenteEmpresa}"
                                reRender="panelContaCorrentEmpresa, panelErroMensagem"
                                focus="form:ContaCorrenteEmpresa_contaCorrente" value="#{msg_bt.btn_adicionar}"
                                image="./imagens/botaoAdicionar.png" accesskey="5" styleClass="botoes" />

                            <h:panelGrid columns="1" width="100%" styleClass="tabFormSubordinada">
                                <h:dataTable id="contaCorrenteEmpresaVO" width="100%" headerClass="subordinado"
                                    styleClass="tabFormSubordinada" rowClasses="linhaImpar, linhaPar"
                                    columnClasses="colunaAlinhamento"
                                    value="#{EmpresaControle.empresaVO.contaCorrenteEmpresaVOs}"
                                    var="contaCorrenteEmpresa">
                                    <h:column>
                                        <f:facet name="header">
                                            <h:outputText value="#{msg_aplic.prt_ContaCorrente_agencia}" />
                                        </f:facet>
                                        <h:outputText
                                            value="#{contaCorrenteEmpresa.contaCorrente.agencia} / #{contaCorrenteEmpresa.contaCorrente.agenciaDV} " />
                                    </h:column>
                                    <h:column>
                                        <f:facet name="header">
                                            <h:outputText value="#{msg_aplic.prt_ContaCorrenteEmpresa_contaCorrente}" />
                                        </f:facet>
                                        <h:outputText
                                            value="#{contaCorrenteEmpresa.contaCorrente.contaCorrente} / #{contaCorrenteEmpresa.contaCorrente.contaCorrenteDV}" />
                                    </h:column>
                                    <h:column>
                                        <f:facet name="header">
                                            <h:outputText value="Cod. Banco" />
                                        </f:facet>
                                        <h:outputText value="#{contaCorrenteEmpresa.contaCorrente.banco.codigoBanco}" />
                                    </h:column>
                                    <h:column>
                                        <f:facet name="header">
                                            <h:outputText value="#{msg_aplic.prt_ContaCorrente_banco}" />
                                        </f:facet>
                                        <h:outputText value="#{contaCorrenteEmpresa.contaCorrente.banco.nome}" />
                                    </h:column>
                                    <h:column>
                                        <f:facet name="header">
                                            <h:outputText value="#{msg_bt.btn_opcoes}" />
                                        </f:facet>
                                        <h:commandButton id="removerItemVenda" immediate="true"
                                            action="#{EmpresaControle.removerContaCorrenteEmpresa}"
                                            value="#{msg_bt.btn_excluir}" image="./imagens/botaoRemover.png"
                                            accesskey="7" styleClass="botoes" />
                                    </h:column>
                                </h:dataTable>
                            </h:panelGrid>

                            <h:panelGroup layout="block" style="display: inline-flex">
                                <h:outputText styleClass="tituloCampos"
                                    value="Replicar configuração para outras empresas:" />
                                <a4j:commandLink style="display: inline-table;" id="preparaReplicarContaCorrente"
                                    action="#{EmpresaControle.preparaReplicarConfiguracaoContaCorrente}"
                                    oncomplete="Richfaces.showModalPanel('modalReplicarContaCorrente')"
                                    reRender="panelGeralConta, panelErroMensagem" value="Replicar configuração"
                                    styleClass="botoes" />
                            </h:panelGroup>
                        </h:panelGrid>
                    </rich:tab>

                    <rich:tab label="Logotipo" id="abaLogotipo" action="#{EmpresaControle.prepararFotos}">
                        <h:panelGrid columns="1" width="100%" styleClass="tabConsulta" headerClass="subordinado"
                            columnClasses="colunaCentralizada">
                            <rich:tabPanel tabClass="aba" width="100%" activeTabClass="true" headerAlignment="left">
                                <rich:tab id="logotipo" label="Empresa">
                                    <h:panelGrid id="panelLogotipo" columns="1" width="100%" headerClass="subordinado"
                                        columnClasses="colunaCentralizada">
                                        <f:facet name="header">
                                            <h:outputText value="#{msg_aplic.prt_LogoTipo_tituloForm}" />
                                        </f:facet>
                                        <h:panelGrid columns="1" width="100%" rowClasses="linhaImpar, linhaPar"
                                            columnClasses="colunaCentralizada">

                                            <a4j:mediaOutput element="img" id="foto" style="width:120px;height:160px "
                                                cacheable="false"
                                                createContent="#{EmpresaControle.paintFotoSemPesquisarNoBanco}"
                                                value="#{ImagemData}" mimeType="image/jpeg">
                                                <f:param value="#{SuperControle.timeStamp}" name="time" />
                                                <f:param name="largura" value="120" />
                                                <f:param name="altura" value="160" />
                                            </a4j:mediaOutput>
                                            <rich:fileUpload listHeight="0" listWidth="150" noDuplicate="false"
                                                fileUploadListener="#{EmpresaControle.upload}" maxFilesQuantity="1"
                                                addControlLabel="Adicionar" cancelEntryControlLabel="Cancelar"
                                                doneLabel="Pronto"
                                                sizeErrorLabel="Arquivo não Enviado. Tamanho Máximo Permitido 100 MB"
                                                progressLabel="Enviando" stopControlLabel="Parar"
                                                uploadControlLabel="Enviar" transferErrorLabel="Falha de Transmissão"
                                                stopEntryControlLabel="Parar" id="upload" immediateUpload="true"
                                                autoclear="true"
                                                acceptedTypes="png,gif,jpg,jpeg,ico,bmp,PNG,GIF,JPG,JPEG,ICO,BMP">

                                                <a4j:support event="onuploadcomplete" ajaxSingle="true"
                                                    reRender="form" />
                                                <a4j:support event="oncomplete" ajaxSingle="true" reRender="form" />
                                            </rich:fileUpload>


                                            <h:outputText styleClass="tituloCampos"
                                                value="Tamanho do Arquivo Padrão para Layout 120x160" />
                                            <h:outputText value="" />
                                            <h:panelGroup rendered="#{EmpresaControle.empresaVO.existeFoto}">
                                                <h:outputText styleClass="tituloCampos"
                                                    value="Tamanho do Arquivo Adicionado: " />
                                                <h:outputText styleClass="tituloCampos"
                                                    value="#{EmpresaControle.empresaVO.larguraFotoEmpresa}" />
                                                <h:outputText styleClass="tituloCampos" value=" x " />
                                                <h:outputText styleClass="tituloCampos"
                                                    value="#{EmpresaControle.empresaVO.alturaFotoEmpresa}" />
                                            </h:panelGroup>
                                        </h:panelGrid>
                                    </h:panelGrid>
                                </rich:tab>
                                <rich:tab id="logotipoRelatorio" label="Relatório">
                                    <h:panelGrid id="panelLogotipoRelatorio" columns="1" width="100%"
                                        headerClass="subordinado" columnClasses="colunaCentralizada">
                                        <f:facet name="header">
                                            <h:outputText value="#{msg_aplic.prt_LogoTipo_tituloForm}" />
                                        </f:facet>
                                        <h:panelGrid columns="1" width="100%" rowClasses="linhaImpar, linhaPar"
                                            columnClasses="colunaCentralizada">

                                            <a4j:mediaOutput element="img" id="fotoRelatorio"
                                                style="width:200px;height:56px " cacheable="false"
                                                createContent="#{EmpresaControle.paintFotoRelatorioSemPesquisarNoBanco}"
                                                value="#{ImagemData}" mimeType="image/jpeg">
                                                <f:param value="#{SuperControle.timeStamp}" name="time" />
                                            </a4j:mediaOutput>
                                            <rich:fileUpload listHeight="0" listWidth="150" noDuplicate="false"
                                                fileUploadListener="#{EmpresaControle.uploadRelatorio}"
                                                maxFilesQuantity="1" addControlLabel="Adicionar"
                                                cancelEntryControlLabel="Cancelar" doneLabel="Pronto"
                                                sizeErrorLabel="Arquivo não Enviado. Tamanho Máximo Permitido 100 MB"
                                                progressLabel="Enviando" stopControlLabel="Parar"
                                                uploadControlLabel="Enviar" transferErrorLabel="Falha de Transmissão"
                                                stopEntryControlLabel="Parar" id="uploadRelatorio"
                                                immediateUpload="true" autoclear="true"
                                                acceptedTypes="png,gif,jpg,jpeg,ico,bmp,PNG,GIF,JPG,JPEG,ICO,BMP">

                                                <a4j:support event="onuploadcomplete" ajaxSingle="true"
                                                    reRender="form" />
                                                <a4j:support event="oncomplete" ajaxSingle="true" reRender="form" />
                                            </rich:fileUpload>


                                            <h:outputText styleClass="tituloCampos"
                                                value="Tamanho do Arquivo Padrão para Layout Relatorio 200x56" />
                                            <h:outputText value="" />
                                            <h:panelGroup rendered="#{EmpresaControle.empresaVO.existeFotoRelatorio}">
                                                <h:outputText styleClass="tituloCampos"
                                                    value="Tamanho do Arquivo Adicionado: " />
                                                <h:outputText styleClass="tituloCampos"
                                                    value="#{EmpresaControle.empresaVO.larguraFotoRelatorio}" />
                                                <h:outputText styleClass="tituloCampos" value=" x " />
                                                <h:outputText styleClass="tituloCampos"
                                                    value="#{EmpresaControle.empresaVO.alturaFotoRelatorio}" />
                                            </h:panelGroup>
                                        </h:panelGrid>
                                    </h:panelGrid>
                                </rich:tab>

                                <rich:tab id="logotipoEmail" label="E-mail">
                                    <h:panelGrid id="panelLogotipoEmail" columns="1" width="100%"
                                        headerClass="subordinado" columnClasses="colunaCentralizada">
                                        <f:facet name="header">
                                            <h:outputText value="#{msg_aplic.prt_LogoTipo_tituloForm}" />
                                        </f:facet>
                                        <h:panelGrid columns="1" width="100%" rowClasses="linhaImpar, linhaPar"
                                            columnClasses="colunaCentralizada">

                                            <a4j:mediaOutput element="img" id="fotoEmail"
                                                style="width:190px;height:78px " cacheable="false"
                                                createContent="#{EmpresaControle.paintFotoEmailSemPesquisarNoBanco}"
                                                value="#{ImagemData}" mimeType="image/jpeg">
                                                <f:param value="#{SuperControle.timeStamp}" name="time" />
                                            </a4j:mediaOutput>
                                            <rich:fileUpload listHeight="0" listWidth="150" noDuplicate="false"
                                                fileUploadListener="#{EmpresaControle.uploadImgEmail}"
                                                maxFilesQuantity="1" addControlLabel="Adicionar"
                                                cancelEntryControlLabel="Cancelar" doneLabel="Pronto"
                                                sizeErrorLabel="Arquivo não Enviado. Tamanho Máximo Permitido 100 MB"
                                                progressLabel="Enviando" stopControlLabel="Parar"
                                                uploadControlLabel="Enviar" transferErrorLabel="Falha de Transmissão"
                                                stopEntryControlLabel="Parar" id="uploadEmail" immediateUpload="true"
                                                autoclear="true"
                                                acceptedTypes="png,gif,jpg,jpeg,ico,bmp,PNG,GIF,JPG,JPEG,ICO,BMP">

                                                <a4j:support event="onuploadcomplete" ajaxSingle="true"
                                                    reRender="form" />
                                                <a4j:support event="oncomplete" ajaxSingle="true" reRender="form" />
                                            </rich:fileUpload>


                                            <h:outputText styleClass="tituloCampos"
                                                value="Tamanho do Arquivo Padrão para Layout E-mail 190x78" />
                                            <h:outputText value="" />
                                            <h:panelGroup rendered="#{EmpresaControle.empresaVO.existeFotoEmail}">
                                                <h:outputText styleClass="tituloCampos"
                                                    value="Tamanho do Arquivo Adicionado: " />
                                                <h:outputText styleClass="tituloCampos"
                                                    value="#{EmpresaControle.empresaVO.larguraFotoEmail}" />
                                                <h:outputText styleClass="tituloCampos" value=" x " />
                                                <h:outputText styleClass="tituloCampos"
                                                    value="#{EmpresaControle.empresaVO.alturaFotoEmail}" />
                                            </h:panelGroup>
                                        </h:panelGrid>
                                    </h:panelGrid>
                                </rich:tab>

                                <rich:tab id="logotipoRedeSocial" label="Rede Social">
                                    <h:panelGrid id="panelLogotipoRedeSocial" columns="1" width="100%"
                                        headerClass="subordinado" columnClasses="colunaCentralizada">
                                        <f:facet name="header">
                                            <h:outputText value="#{msg_aplic.prt_LogoTipo_tituloForm}" />
                                        </f:facet>
                                        <h:panelGrid columns="1" width="100%" rowClasses="linhaImpar, linhaPar"
                                            columnClasses="colunaCentralizada">

                                            <a4j:mediaOutput element="img" id="fotoRedesocial"
                                                style="width:320px;height:320px " cacheable="false"
                                                createContent="#{EmpresaControle.paintFotoRedeSocialSemPesquisarNoBanco}"
                                                value="#{ImagemData}" mimeType="image/jpeg">
                                                <f:param value="#{SuperControle.timeStamp}" name="time" />
                                            </a4j:mediaOutput>
                                            <rich:fileUpload listHeight="0" listWidth="150" noDuplicate="false"
                                                fileUploadListener="#{EmpresaControle.uploadImgRedeSocial}"
                                                maxFilesQuantity="1" addControlLabel="Adicionar"
                                                cancelEntryControlLabel="Cancelar" doneLabel="Pronto"
                                                sizeErrorLabel="Arquivo não Enviado. Tamanho Máximo Permitido 100 MB"
                                                progressLabel="Enviando" stopControlLabel="Parar"
                                                uploadControlLabel="Enviar" transferErrorLabel="Falha de Transmissão"
                                                stopEntryControlLabel="Parar" id="uploadRedeSocial"
                                                immediateUpload="true" autoclear="true"
                                                acceptedTypes="png,gif,jpg,jpeg,ico,bmp,PNG,GIF,JPG,JPEG,ICO,BMP">

                                                <a4j:support event="onuploadcomplete" ajaxSingle="true"
                                                    reRender="form" />
                                                <a4j:support event="oncomplete" ajaxSingle="true" reRender="form" />
                                            </rich:fileUpload>


                                            <h:outputText styleClass="tituloCampos"
                                                value="Tamanho do Arquivo Padrão para compartilhamento em rede social: 640x640 (Aqui sendo exibido em tamanho reduzido)" />
                                            <h:outputText value="" />
                                            <h:panelGroup rendered="#{EmpresaControle.empresaVO.existeFotoRedeSocial}">
                                                <h:outputText styleClass="tituloCampos"
                                                    value="Tamanho do Arquivo Adicionado: " />
                                                <h:outputText styleClass="tituloCampos"
                                                    value="#{EmpresaControle.empresaVO.larguraFotoRedeSocial}" />
                                                <h:outputText styleClass="tituloCampos" value=" x " />
                                                <h:outputText styleClass="tituloCampos"
                                                    value="#{EmpresaControle.empresaVO.alturaFotoRedeSocial}" />
                                            </h:panelGroup>
                                        </h:panelGrid>
                                    </h:panelGrid>
                                </rich:tab>
                                <rich:tab id="logotipoAppGrande" label="Fundo App Grande">
                                    <h:panelGrid id="panelLogotipoAppGrande" columns="1" width="100%"
                                        headerClass="subordinado" columnClasses="colunaCentralizada">
                                        <f:facet name="header">
                                            <h:outputText value="#{msg_aplic.prt_LogoTipo_tituloForm}" />
                                        </f:facet>
                                        <h:panelGrid columns="1" width="100%" rowClasses="linhaImpar, linhaPar"
                                            columnClasses="colunaCentralizada">

                                            <a4j:mediaOutput element="img" id="fotoAppGrande"
                                                style="width:320px;height:276px " cacheable="false"
                                                createContent="#{EmpresaControle.paintFotoAppGrandeSemPesquisarNoBanco}"
                                                value="#{ImagemData}" mimeType="image/jpeg">
                                                <f:param value="#{SuperControle.timeStamp}" name="time" />
                                            </a4j:mediaOutput>
                                            <rich:fileUpload listHeight="0" listWidth="150" noDuplicate="false"
                                                fileUploadListener="#{EmpresaControle.uploadImgAppGrande}"
                                                maxFilesQuantity="1" addControlLabel="Adicionar"
                                                cancelEntryControlLabel="Cancelar" doneLabel="Pronto"
                                                sizeErrorLabel="Arquivo não Enviado. Tamanho Máximo Permitido 100 MB"
                                                progressLabel="Enviando" stopControlLabel="Parar"
                                                uploadControlLabel="Enviar" transferErrorLabel="Falha de Transmissão"
                                                stopEntryControlLabel="Parar" id="uploadAppGrande"
                                                immediateUpload="true" autoclear="true"
                                                acceptedTypes="png,gif,jpg,jpeg,ico,bmp,PNG,GIF,JPG,JPEG,ICO,BMP">

                                                <a4j:support event="onuploadcomplete" ajaxSingle="true"
                                                    reRender="form" />
                                                <a4j:support event="oncomplete" ajaxSingle="true" reRender="form" />
                                            </rich:fileUpload>


                                            <h:outputText styleClass="tituloCampos"
                                                value="Tamanho do Arquivo Padrão para compartilhamento em rede social: 640x551 (Aqui sendo exibido em tamanho reduzido)" />
                                            <h:outputText value="" />
                                            <h:panelGroup
                                                rendered="#{EmpresaControle.empresaVO.homeBackground640x551 != null}">
                                                <h:outputText styleClass="tituloCampos"
                                                    value="Tamanho do Arquivo Adicionado: " />
                                                <h:outputText styleClass="tituloCampos"
                                                    value="#{EmpresaControle.empresaVO.largurahomeBackground640x551}" />
                                                <h:outputText styleClass="tituloCampos" value=" x " />
                                                <h:outputText styleClass="tituloCampos"
                                                    value="#{EmpresaControle.empresaVO.alturahomeBackground640x551}" />
                                            </h:panelGroup>
                                        </h:panelGrid>
                                    </h:panelGrid>
                                </rich:tab>
                                <rich:tab id="logotipoAppPequena" label="Fundo App Pequeno">
                                    <h:panelGrid id="panelLogotipoAppPequena" columns="1" width="100%"
                                        headerClass="subordinado" columnClasses="colunaCentralizada">
                                        <f:facet name="header">
                                            <h:outputText value="#{msg_aplic.prt_LogoTipo_tituloForm}" />
                                        </f:facet>
                                        <h:panelGrid columns="1" width="100%" rowClasses="linhaImpar, linhaPar"
                                            columnClasses="colunaCentralizada">

                                            <a4j:mediaOutput element="img" id="fotoAppPequena"
                                                style="width:320px;height:276px" cacheable="false"
                                                createContent="#{EmpresaControle.paintFotoAppPequenaSemPesquisarNoBanco}"
                                                value="#{ImagemData}" mimeType="image/jpeg">
                                                <f:param value="#{SuperControle.timeStamp}" name="time" />
                                            </a4j:mediaOutput>
                                            <rich:fileUpload listHeight="0" listWidth="150" noDuplicate="false"
                                                fileUploadListener="#{EmpresaControle.uploadImgAppPequena}"
                                                maxFilesQuantity="1" addControlLabel="Adicionar"
                                                cancelEntryControlLabel="Cancelar" doneLabel="Pronto"
                                                sizeErrorLabel="Arquivo não Enviado. Tamanho Máximo Permitido 100 MB"
                                                progressLabel="Enviando" stopControlLabel="Parar"
                                                uploadControlLabel="Enviar" transferErrorLabel="Falha de Transmissão"
                                                stopEntryControlLabel="Parar" id="uploadAppPequena"
                                                immediateUpload="true" autoclear="true"
                                                acceptedTypes="png,gif,jpg,jpeg,ico,bmp,PNG,GIF,JPG,JPEG,ICO,BMP">

                                                <a4j:support event="onuploadcomplete" ajaxSingle="true"
                                                    reRender="form" />
                                                <a4j:support event="oncomplete" ajaxSingle="true" reRender="form" />
                                            </rich:fileUpload>


                                            <h:outputText styleClass="tituloCampos"
                                                value="Tamanho do Arquivo Padrão para compartilhamento em rede social: 320x276" />
                                            <h:outputText value="" />
                                            <h:panelGroup
                                                rendered="#{EmpresaControle.empresaVO.homeBackground320x276 != null}">
                                                <h:outputText styleClass="tituloCampos"
                                                    value="Tamanho do Arquivo Adicionado: " />
                                                <h:outputText styleClass="tituloCampos"
                                                    value="#{EmpresaControle.empresaVO.largurahomeBackground320x276}" />
                                                <h:outputText styleClass="tituloCampos" value=" x " />
                                                <h:outputText styleClass="tituloCampos"
                                                    value="#{EmpresaControle.empresaVO.alturahomeBackground320x276}" />
                                            </h:panelGroup>
                                        </h:panelGrid>
                                    </h:panelGrid>
                                </rich:tab>
                                <rich:tab id="propagandaBoleto" label="Propaganda do Boleto">
                                    <h:panelGrid id="panelPropagandaBoleto" columns="1" width="100%"
                                        headerClass="subordinado" columnClasses="colunaCentralizada">
                                        <f:facet name="header">
                                            <h:outputText value="#{msg_aplic.prt_LogoTipo_tituloForm}" />
                                        </f:facet>
                                        <h:panelGrid columns="1" width="100%" rowClasses="linhaImpar, linhaPar"
                                            columnClasses="colunaCentralizada">

                                            <a4j:mediaOutput element="img" id="fotoPropagandaBoleto"
                                                style="width:238px;height:238px" cacheable="false"
                                                createContent="#{EmpresaControle.paintFotoPropagandaBoletoSemPesquisarNoBanco}"
                                                value="#{ImagemData}" mimeType="image/jpeg">
                                                <f:param value="#{SuperControle.timeStamp}" name="time" />
                                            </a4j:mediaOutput>
                                            <rich:fileUpload listHeight="0" listWidth="150" noDuplicate="false"
                                                fileUploadListener="#{EmpresaControle.uploadImgPropagandaBoleto}"
                                                maxFilesQuantity="1" addControlLabel="Adicionar"
                                                cancelEntryControlLabel="Cancelar" doneLabel="Pronto"
                                                sizeErrorLabel="Arquivo não Enviado. Tamanho Máximo Permitido 100 MB"
                                                progressLabel="Enviando" stopControlLabel="Parar"
                                                uploadControlLabel="Enviar" transferErrorLabel="Falha de Transmissão"
                                                stopEntryControlLabel="Parar" id="uploadPropagandaBoleto"
                                                immediateUpload="true" autoclear="true"
                                                acceptedTypes="png,gif,jpg,jpeg,ico,bmp,PNG,GIF,JPG,JPEG,ICO,BMP">

                                                <a4j:support event="onuploadcomplete" ajaxSingle="true"
                                                    reRender="form" />
                                                <a4j:support event="oncomplete" ajaxSingle="true" reRender="form" />
                                            </rich:fileUpload>

                                            <h:outputText styleClass="tituloCampos"
                                                value="Tamanho do Arquivo Padrão para propaganda em boleto: 238x238 (Aqui sendo exibido em tamanho reduzido)" />
                                            <h:outputText value="" />
                                            <h:panelGroup
                                                rendered="#{EmpresaControle.empresaVO.propagandaBoleto != null}">
                                                <h:outputText styleClass="tituloCampos"
                                                    value="Tamanho do Arquivo Adicionado: " />
                                                <h:outputText styleClass="tituloCampos"
                                                    value="#{EmpresaControle.empresaVO.larguraPropagandaBoleto}" />
                                                <h:outputText styleClass="tituloCampos" value=" x " />
                                                <h:outputText styleClass="tituloCampos"
                                                    value="#{EmpresaControle.empresaVO.alturaPropagandaBoleto}" />
                                            </h:panelGroup>
                                        </h:panelGrid>
                                    </h:panelGrid>
                                </rich:tab>
                            </rich:tabPanel>
                        </h:panelGrid>
                    </rich:tab>

                    <rich:tab label="Recibo">
                        <h:panelGrid id="pnlRecibo" columns="2" rowClasses="linhaImpar, linhaPar"
                            columnClasses="classEsquerda, classDireita" width="100%">

                            <h:outputText styleClass="tituloCampos" value="Recibo para Impressoras Térmicas" />
                            <h:selectBooleanCheckbox id="reciboParaImpressoraTermica" styleClass="campos"
                                value="#{EmpresaControle.empresaVO.reciboParaImpressoraTermica}">
                                <a4j:support event="onchange" reRender="pnlRecibo" />
                            </h:selectBooleanCheckbox>

                            <h:outputText styleClass="tituloCampos" value="Mostrar #{EmpresaControle.displayIdentificadorFront[2]} da Empresa no Recibo" />
                            <h:selectBooleanCheckbox id="mostrarCnpj" styleClass="campos"
                                value="#{EmpresaControle.empresaVO.mostrarCnpj}" />

                            <h:outputText styleClass="tituloCampos" value="Mostrar Modalidade no Recibo" />
                            <h:selectBooleanCheckbox id="mostrarModalidade" styleClass="campos"
                                value="#{EmpresaControle.empresaVO.mostrarModalidade}" />

                            <h:outputText styleClass="tituloCampos" value="Detalhar período do Produto" />
                            <h:selectBooleanCheckbox id="detalharPeriodoProduto" styleClass="campos"
                                value="#{EmpresaControle.empresaVO.detalharPeriodoProduto}" />

                            <h:outputText styleClass="tituloCampos" value="Detalhar Parcelas" />
                            <h:selectBooleanCheckbox id="detalharParcelas" styleClass="campos"
                                value="#{EmpresaControle.empresaVO.detalharParcelas}" />

                            <h:outputText styleClass="tituloCampos" value="Detalhar Pagamentos" />
                            <h:selectBooleanCheckbox id="detalharPagamentos" styleClass="campos"
                                value="#{EmpresaControle.empresaVO.detalharPagamentos}" />

                            <h:outputText styleClass="tituloCampos" value="Detalhar Descontos" />
                            <h:selectBooleanCheckbox id="detalharDescontos" styleClass="campos"
                                value="#{EmpresaControle.empresaVO.detalharDescontos}" />

                            <h:outputText styleClass="tituloCampos" value="Apresentar Assinaturas" />
                            <h:selectBooleanCheckbox id="apresentarAssinaturas" styleClass="campos"
                                value="#{EmpresaControle.empresaVO.apresentarAssinaturas}" />

                            <h:outputText styleClass="tituloCampos"
                                rendered="#{!EmpresaControle.empresaVO.reciboParaImpressoraTermica}"
                                value="Quantidade de Vias do Recibo" />
                            <h:inputText id="qtdViasRecibo" size="10" onblur="blurinput(this);"
                                rendered="#{!EmpresaControle.empresaVO.reciboParaImpressoraTermica}"
                                onfocus="focusinput(this);" styleClass="form"
                                value="#{EmpresaControle.empresaVO.qtdVias}" />

                            <h:outputText styleClass="tituloCampos" value="Quebrar Página entre vias" />
                            <h:selectBooleanCheckbox id="quebrarPaginaVias" styleClass="campos"
                                value="#{EmpresaControle.empresaVO.quebrarPaginaRecibo}" />

                            <h:outputText styleClass="tituloCampos"
                                value="Mostrar Mensagem Valores abertos no rodapé" />
                            <h:selectBooleanCheckbox id="mostrarValores" styleClass="campos"
                                value="#{EmpresaControle.empresaVO.mostrarMensagemValoresRodape}" />

                            <h:outputText styleClass="tituloCampos"
                                          value="Apresentar detalhes parcelas renegociadas no recibo" />
                            <h:selectBooleanCheckbox id="apresentarDescricaoparcelaRen" styleClass="campos"
                                                     value="#{EmpresaControle.empresaVO.mostrarDescricaoParcelaRenegociada}" />

                            <h:outputText styleClass="tituloCampos"
                                          value="Detalhar turmas(Nível, professor e horário) nas observações do recibo: " />
                            <h:selectBooleanCheckbox id="detalharNiveisModalidades" styleClass="campos"
                                                     value="#{EmpresaControle.empresaVO.detalharNiveisModalidades}" />

                            <h:outputText styleClass="tituloCampos"
                                          value="Emitir no nome do responsável quando o cliente for menor de idade: " />
                            <h:selectBooleanCheckbox id="emitirNoNomeResponsavel" styleClass="campos"
                                                     value="#{EmpresaControle.empresaVO.emitirNoNomeResponsavel}" />

                            <h:outputText styleClass="tituloCampos" value="Observações" />
                            <h:inputTextarea id="observacoesRecibo" styleClass="form" rows="3" cols="80"
                                value="#{EmpresaControle.empresaVO.observacaoRecibo}" />
                        </h:panelGrid>
                    </rich:tab>

                    <rich:tab label="Assinatura digital">
                        <script>
                            function sleep(milliseconds) {
                                var start = new Date().getTime();
                                for (var i = 0; i < 1e7; i++) {
                                    if ((new Date().getTime() - start) > milliseconds) {
                                        break;
                                    }
                                }
                            }
                        </script>
                        <h:panelGrid id="panelAssinaturaDigital" columns="1" width="100%"
                            columnClasses="colunaCentralizada">

                            <h:panelGrid columns="1" width="100%" rowClasses="linhaImpar, linhaPar"
                                         columnClasses="colunaCentralizada" id="painelAssDig" headerClass="subordinado">

                                <f:facet name="header">
                                    <h:outputText value="Assinatura da academia para contrato digital" />
                                </f:facet>

                                <h:panelGroup rendered="#{not empty EmpresaControle.urlImagemAssinatura}">
                                    <img style="width: 40%;" src="${EmpresaControle.urlImagemAssinatura}" />
                                </h:panelGroup>

                                <rich:fileUpload listHeight="0" listWidth="150" noDuplicate="false"
                                                 fileUploadListener="#{EmpresaControle.uploadImgAssinatura}" maxFilesQuantity="1"
                                                 addControlLabel="Adicionar" cancelEntryControlLabel="Cancelar" doneLabel="Pronto"
                                                 sizeErrorLabel="Arquivo não Enviado. Tamanho Máximo Permitido 100 MB"
                                                 progressLabel="Enviando" stopControlLabel="Parar" uploadControlLabel="Enviar"
                                                 transferErrorLabel="Falha de Transmissão" stopEntryControlLabel="Parar"
                                                 id="uploadAssinaturaDigital" immediateUpload="true" autoclear="true"
                                                 acceptedTypes="png,gif,jpg,jpeg,ico,bmp,PNG,GIF,JPG,JPEG,ICO,BMP">

                                    <a4j:support event="onuploadcomplete" reRender="panelErroMensagem,painelAssDig"
                                                 oncomplete="sleep(2000);" />
                                </rich:fileUpload>
                            </h:panelGrid>

                            <h:panelGrid id="painelAssDigConfig" columns="2" rowClasses="linhaImpar, linhaPar"
                                         columnClasses="classEsquerda, classDireita" width="100%" headerClass="subordinado">

                                <f:facet name="header">
                                    <h:outputText value="Configurações Assinatura Digital" />
                                </f:facet>

                                <h:outputText styleClass="tituloCampos"
                                              value="Exigir assinatura do responsável financeiro no contrato: "/>
                                <h:selectBooleanCheckbox id="assResponsavel" styleClass="campos"
                                                         value="#{EmpresaControle.empresaVO.exigirAssinaturaDigitalResponsavelFinanceiro}"/>

                            </h:panelGrid>
                        </h:panelGrid>

                    </rich:tab>

                    <rich:tab label="#{msg_aplic.prt_autoatendimento}" id="tabzwauto">
                        <%@include file="includes/include_configsTotem.jsp"%>
                    </rich:tab>

                    <rich:tab label="#{msg_aplic.prt_abaNotas_titulo}" id="tabModuloNotas"
                              rendered="#{EmpresaControle.usuarioLogado.usuarioPactoSolucoes || EmpresaControle.empresaVO.usarNFSe || EmpresaControle.empresaVO.usarNFCe}">
                        <%@include file="includes/empresa/include_notaFiscal.jsp"%>
                    </rich:tab>

                    <rich:tab label="Créditos personal"
                        rendered="#{EmpresaControle.empresaVO.usarGestaoCreditosPersonal}">
                        <h:panelGrid columns="2" rowClasses="linhaImpar, linhaPar"
                            columnClasses="classEsquerda, classDireita" width="100%">

                            <h:outputText styleClass="tituloCampos" value="Duração por crédito (minutos):" />
                            <h:inputText id="tempoCredito" styleClass="form"
                                value="#{EmpresaControle.empresaVO.configsPersonal.duracaoCredito}" />

                            <h:outputText styleClass="tituloCampos" value="Obrigatório associar aluno no check-in:" />
                            <h:selectBooleanCheckbox id="alunocheckIn" styleClass="campos"
                                value="#{EmpresaControle.empresaVO.configsPersonal.obrigatorioAssociarAlunoAoCheckIn}" />

                            <h:outputText styleClass="tituloCampos" value="Bloquear personal pré-pago sem crédito:" />
                            <h:selectBooleanCheckbox id="bloquearAcessoPersonalSemCredito" styleClass="campos"
                                value="#{EmpresaControle.empresaVO.configsPersonal.bloquearAcessoPersonalSemCredito}" />

                            <h:outputText styleClass="tituloCampos" value="Consumir crédito por aluno vinculado:" />
                            <h:selectBooleanCheckbox id="consumirCreditoPorAlunoVinculado" styleClass="campos"
                                value="#{EmpresaControle.empresaVO.configsPersonal.consumirCreditoPorAlunoVinculado}" />

                            <%--                          <h:outputText styleClass="tituloCampos" value="Gerar créditos somente ao pagar:"/>
                            <h:selectBooleanCheckbox id="gerarCreditoSomenteAoPagar" styleClass="campos"
                                                     value="#{EmpresaControle.empresaVO.configsPersonal.gerarCreditoSomenteAoPagar}"/>--%>

                            <h:outputText styleClass="tituloCampos" value="Mostrar fotos dos alunos no monitor:" />
                            <h:selectBooleanCheckbox id="mostrarFotosAlunosMonitor" styleClass="campos"
                                value="#{EmpresaControle.empresaVO.configsPersonal.mostrarFotosAlunosMonitor}" />

                            <h:outputText styleClass="tituloCampos" value="Usar fotos dos personais separadas:" />
                            <h:selectBooleanCheckbox id="usarFotoPersonal" styleClass="campos"
                                value="#{EmpresaControle.empresaVO.configsPersonal.usarFotoPersonal}" />

                            <h:outputText styleClass="tituloCampos"
                                value="Dias para bloqueio de personal com parcela em aberto:" />
                            <h:inputText id="diasBloqueioParcelaEmAberto" styleClass="form"
                                value="#{EmpresaControle.empresaVO.configsPersonal.diasBloqueioParcelaEmAberto}" />


                            <h:outputText styleClass="tituloCampos"
                                value="Tempo para check-out automático (minutos):" />
                            <h:inputText id="tempoCheckOutAutomatico" styleClass="form"
                                value="#{EmpresaControle.empresaVO.configsPersonal.tempoCheckOutAutomatico}" />

                            <h:outputText
                                rendered="#{EmpresaControle.usuarioLogado.administrador && LoginControle.apresentarGestaoPersonal}"
                                styleClass="tituloCampos"
                                value="Permitir alterar data/horário de início/fim do check-in/checkout:" />
                            <h:selectBooleanCheckbox
                                rendered="#{EmpresaControle.usuarioLogado.administrador && LoginControle.apresentarGestaoPersonal}"
                                styleClass="campos"
                                value="#{EmpresaControle.empresaVO.alterarDataHoraCheckGestaoPersonal}">
                                <a4j:support event="onclick" reRender="form" />
                            </h:selectBooleanCheckbox>

                        </h:panelGrid>

                    </rich:tab>

                    <rich:tab label="APP" id="abaAPP">
                        <h:panelGrid id="panelAbaAPP" columns="2" rowClasses="linhaImpar, linhaPar"
                            columnClasses="classEsquerda, classDireita" width="100%">

                            <h:outputText value="Permite renovar contrato via APP: " />
                            <h:selectBooleanCheckbox id="permiteRenovarContratoViaAPP"
                                value="#{EmpresaControle.empresaVO.permiteRenovarContratoViaAPP}" />

                        </h:panelGrid>
                    </rich:tab>

                    <rich:tab label="Crédito Pacto" action="#{EmpresaControle.abrirCreditoPacto}">

                        <h:panelGrid id="panelCreditoPacto" columns="1" width="100%" headerClass="subordinado"
                            columnClasses="colunaCentralizada">

                            <f:facet name="header">
                                <h:outputText value="Crédito Pacto" />
                            </f:facet>

                            <h:panelGrid columns="2" width="100%" rowClasses="linhaImpar, linhaPar"
                                columnClasses="classEsquerda, classDireita">

                                <h:outputText styleClass="tituloCampos" value="Dt. Última Cobrança: " />
                                <h:outputText styleClass="tituloCampos"
                                    value="#{EmpresaControle.empresaVO.dtUltimaCobrancaPactoApresentar}" />

                                <h:outputText styleClass="tituloCampos" value="Tipo de Cobrança: " />
                                <h:outputText styleClass="tituloCampos"
                                    value="#{EmpresaControle.empresaVO.tipoCobrancaPactoApresentar}" />

                                <h:outputText styleClass="tituloCampos" value="Renovação Automática: "
                                              rendered="#{EmpresaControle.empresaVO.tipoCobrancaPactoPrePago}"/>
                                <h:outputText styleClass="tituloCampos" value="Transação Recorrente: "
                                              rendered="#{!EmpresaControle.empresaVO.tipoCobrancaPactoPrePago}"/>
                                <h:outputText styleClass="tituloCampos"
                                    value="#{EmpresaControle.empresaVO.gerarCobrancaAutomaticaPactoApresentar}" />

                                <c:if test="${!EmpresaControle.empresaVO.tipoCobrancaPactoPosPagoMensal}">
                                    <h:outputText styleClass="tituloCampos" value="Valor Unitário: "
                                                  rendered="#{EmpresaControle.empresaVO.gerarCobrancaAutomaticaPacto}"/>
                                    <h:outputText styleClass="tituloCampos"
                                                  value="#{EmpresaControle.empresaVO.valorCreditoPactoApresentar}"
                                                  rendered="#{EmpresaControle.empresaVO.gerarCobrancaAutomaticaPacto}"/>

                                    <h:outputText styleClass="tituloCampos" value="Qtd Crédito Renovar: "
                                                  rendered="#{EmpresaControle.empresaVO.tipoCobrancaPactoPrePago && EmpresaControle.empresaVO.gerarCobrancaAutomaticaPacto}"/>
                                    <h:outputText styleClass="tituloCampos"
                                                  value="#{EmpresaControle.empresaVO.qtdCreditoRenovarPrePagoCobrancaPacto}"
                                                  rendered="#{EmpresaControle.empresaVO.tipoCobrancaPactoPrePago && EmpresaControle.empresaVO.gerarCobrancaAutomaticaPacto}"/>

                                    <h:outputText styleClass="tituloCampos" value="Qtd Dias Fechar: "
                                                  rendered="#{!EmpresaControle.empresaVO.tipoCobrancaPactoPrePago && EmpresaControle.empresaVO.gerarCobrancaAutomaticaPacto}"/>
                                    <h:outputText styleClass="tituloCampos"
                                                  value="#{EmpresaControle.empresaVO.qtdDiasFechamentoCobrancaPacto}"
                                                  rendered="#{!EmpresaControle.empresaVO.tipoCobrancaPactoPrePago && EmpresaControle.empresaVO.gerarCobrancaAutomaticaPacto}"/>

                                    <h:outputText styleClass="tituloCampos" value="Qtd Parcelas: "
                                                  rendered="#{EmpresaControle.empresaVO.gerarCobrancaAutomaticaPacto}" />
                                    <h:outputText styleClass="tituloCampos"
                                                  value="#{EmpresaControle.empresaVO.qtdParcelasCobrancaPacto}"
                                                  rendered="#{EmpresaControle.empresaVO.gerarCobrancaAutomaticaPacto}" />
                                </c:if>

                                <c:if test="${EmpresaControle.empresaVO.tipoCobrancaPactoPosPagoMensal}">

                                    <h:outputText styleClass="tituloCampos" value="Qtd Máxima Crédito:"/>
                                    <h:outputText styleClass="tituloCampos"
                                                  value="#{EmpresaControle.empresaVO.configCobrancaMensalJSON.qtdMaxima}"/>

                                    <h:outputText styleClass="tituloCampos" value="Valor Mensal:"/>
                                    <h:outputText styleClass="tituloCampos"
                                                  value="#{EmpresaControle.empresaVO.configCobrancaMensalJSON.valorMensalApresentar}"/>

                                    <h:outputText styleClass="tituloCampos" value="Valor Unitário Excedente:"/>
                                    <h:outputText styleClass="tituloCampos"
                                                  value="#{EmpresaControle.empresaVO.configCobrancaMensalJSON.valorUnitarioExcedenteApresentar}"/>

                                    <h:outputText styleClass="tituloCampos" value="Dia do fechamento:"/>
                                    <h:outputText styleClass="tituloCampos"
                                                  value="#{EmpresaControle.empresaVO.qtdDiasFechamentoCobrancaPacto}"/>

                                    <h:outputText styleClass="tituloCampos" value="Dia vencimento:"/>
                                    <h:outputText styleClass="tituloCampos"
                                                  value="#{EmpresaControle.empresaVO.diaVencimentoCobrancaPacto}"/>
                                </c:if>

                                <h:outputText styleClass="tituloCampos" value="Gerar Nota Fiscal: "
                                    rendered="#{EmpresaControle.empresaVO.gerarCobrancaAutomaticaPacto}" />
                                <h:outputText styleClass="tituloCampos"
                                    value="#{EmpresaControle.empresaVO.gerarNotaFiscalCobrancaPactoApresentar}"
                                    rendered="#{EmpresaControle.empresaVO.gerarCobrancaAutomaticaPacto}" />

                                <h:outputText styleClass="tituloCampos" value="Cobrar transações Vindi: "
                                              rendered="#{EmpresaControle.usuarioLogado.usuarioPactoSolucoes}"/>
                                <h:selectBooleanCheckbox styleClass="tituloCampos"
                                                         rendered="#{EmpresaControle.usuarioLogado.usuarioPactoSolucoes}"
                                                         value="#{EmpresaControle.empresaVO.cobrarCreditoVindi}" />

                            </h:panelGrid>
                        </h:panelGrid>


                        <h:panelGrid id="panelAtualCreditoPacto" columns="1" width="100%" headerClass="subordinado"
                            columnClasses="colunaCentralizada">

                            <f:facet name="header">
                                <h:outputText value="Atual" />
                            </f:facet>

                            <h:panelGrid columns="2" width="100%" rowClasses="linhaImpar, linhaPar"
                                columnClasses="classEsquerda, classDireita">

                                <h:outputText
                                    rendered="#{EmpresaControle.empresaVO.tipoCobrancaPactoPrePago or EmpresaControle.empresaVO.tipoCobrancaPactoPrePagoRede or EmpresaControle.empresaVO.tipoCobrancaPactoPrePagoEfetivado}"
                                    styleClass="tituloCampos" value="Crédito Atual: " />
                                <h:outputText
                                    rendered="#{EmpresaControle.empresaVO.tipoCobrancaPactoPrePago or EmpresaControle.empresaVO.tipoCobrancaPactoPrePagoRede or EmpresaControle.empresaVO.tipoCobrancaPactoPrePagoEfetivado}"
                                    styleClass="tituloCampos" value="#{EmpresaControle.empresaVO.creditoDCC}" />

                                <h:outputText styleClass="tituloCampos" value="Total Crédito Utilizado: "
                                    rendered="#{!EmpresaControle.empresaVO.tipoCobrancaPactoPrePago and !EmpresaControle.empresaVO.tipoCobrancaPactoPrePagoRede}" />
                                <a4j:commandLink
                                    rendered="#{!EmpresaControle.empresaVO.tipoCobrancaPactoPrePago and !EmpresaControle.empresaVO.tipoCobrancaPactoPrePagoRede}"
                                    value="#{EmpresaControle.empresaVO.qtdCreditoRemessa + EmpresaControle.empresaVO.qtdCreditoTransacao + EmpresaControle.empresaVO.qtdCreditoPix}"
                                    action="#{EmpresaControle.abrirListaTotalItensCobranca}"
                                    reRender="formModalItensCobranca"
                                    oncomplete="#{EmpresaControle.msgAlert};#{EmpresaControle.mensagemNotificar}"
                                    styleClass="tooltipster"
                                    title="#{EmpresaControle.mensagemTooltipsterTotaisCreditoPacto}"/>

                                <h:outputText styleClass="tituloCampos" value="Qtd Crédito Utilizado Remessa: "
                                    rendered="#{!EmpresaControle.empresaVO.tipoCobrancaPactoPrePago and !EmpresaControle.empresaVO.tipoCobrancaPactoPrePagoRede}" />
                                <a4j:commandLink
                                    rendered="#{!EmpresaControle.empresaVO.tipoCobrancaPactoPrePago and !EmpresaControle.empresaVO.tipoCobrancaPactoPrePagoRede}"
                                    value="#{EmpresaControle.empresaVO.qtdCreditoRemessa}"
                                    action="#{EmpresaControle.abrirListaRemessaItem}" reRender="formModalItensCobranca"
                                    oncomplete="#{EmpresaControle.msgAlert};#{EmpresaControle.mensagemNotificar}"
                                    styleClass="tooltipster"
                                    title="#{EmpresaControle.mensagemTooltipsterTotaisCreditoPacto}"/>

                                <h:outputText styleClass="tituloCampos" value="Qtd Crédito Utilizado Transação: "
                                    rendered="#{!EmpresaControle.empresaVO.tipoCobrancaPactoPrePago and !EmpresaControle.empresaVO.tipoCobrancaPactoPrePagoRede}" />
                                <a4j:commandLink
                                    rendered="#{!EmpresaControle.empresaVO.tipoCobrancaPactoPrePago and !EmpresaControle.empresaVO.tipoCobrancaPactoPrePagoRede}"
                                    value="#{EmpresaControle.empresaVO.qtdCreditoTransacao}"
                                    action="#{EmpresaControle.abrirListaTransacao}" reRender="formModalItensCobranca"
                                    oncomplete="#{EmpresaControle.msgAlert};#{EmpresaControle.mensagemNotificar}"
                                    styleClass="tooltipster"
                                    title="#{EmpresaControle.mensagemTooltipsterTotaisCreditoPacto}"/>

                                <h:outputText styleClass="tituloCampos" value="Qtd Crédito Utilizado Pix: "
                                              rendered="#{!EmpresaControle.empresaVO.tipoCobrancaPactoPrePago and !EmpresaControle.empresaVO.tipoCobrancaPactoPrePagoRede}" />
                                <a4j:commandLink
                                        rendered="#{!EmpresaControle.empresaVO.tipoCobrancaPactoPrePago and !EmpresaControle.empresaVO.tipoCobrancaPactoPrePagoRede}"
                                        value="#{EmpresaControle.empresaVO.qtdCreditoPix}"
                                        action="#{EmpresaControle.abrirListaPix}" reRender="formModalItensCobranca"
                                        oncomplete="#{EmpresaControle.msgAlert};#{EmpresaControle.mensagemNotificar}"
                                        styleClass="tooltipster"
                                        title="#{EmpresaControle.mensagemTooltipsterTotaisCreditoPacto}"/>

                                <h:outputText styleClass="tituloCampos" value="Qtd Crédito Bônus: "
                                              rendered="#{EmpresaControle.empresaVO.tipoCobrancaPactoPosPago}" />
                                <h:outputText styleClass="tituloCampos"
                                              value="#{EmpresaControle.empresaVO.creditoDCCBonus}"
                                              rendered="#{EmpresaControle.empresaVO.tipoCobrancaPactoPosPago}"/>

                            </h:panelGrid>
                        </h:panelGrid>

                        <h:panelGrid id="panelHistoricoCobrancaPacto" columns="1" width="100%" headerClass="subordinado"
                            rendered="#{(!EmpresaControle.empresaVO.tipoCobrancaPactoPrePagoRede) or (EmpresaControle.empresaVO.tipoCobrancaPactoPrePago and EmpresaControle.empresaVO.gerarCobrancaAutomaticaPacto)}"
                            columnClasses="colunaCentralizada">

                            <f:facet name="header">
                                <h:outputText value="Histórico" />
                            </f:facet>

                            <h:panelGroup layout="block" style="overflow:auto; max-height: 300px;">

                                <rich:dataTable width="100%" headerClass="subordinado" rowClasses="linhaImpar, linhaPar"
                                    value="#{EmpresaControle.empresaVO.logCobrancaPacto}" var="logCobranca">

                                    <rich:column style="text-align: center">
                                        <f:facet name="header">
                                            <h:outputText value="Codigo" />
                                        </f:facet>
                                        <h:outputText value="#{logCobranca.codigo}" />
                                    </rich:column>
                                    <rich:column style="text-align: center">
                                        <f:facet name="header">
                                            <h:outputText value="Data" />
                                        </f:facet>
                                        <h:outputText value="#{logCobranca.dataCobranca_Apresentar}" />
                                    </rich:column>
                                    <rich:column style="text-align: center">
                                        <f:facet name="header">
                                            <h:outputText value="Quantidade" />
                                        </f:facet>
                                        <h:outputText value="#{logCobranca.quantidade}" />
                                    </rich:column>
                                    <rich:column style="text-align: center">
                                        <f:facet name="header">
                                            <h:outputText value="Valor R$" />
                                        </f:facet>
                                        <h:outputText value="#{logCobranca.valorTotal_Apresentar}" />
                                    </rich:column>
                                    <rich:column style="text-align: center"
                                                 rendered="#{EmpresaControle.usuarioLogado.usuarioPactoSolucoes}">
                                        <f:facet name="header">
                                            <h:outputText value="Usuário" />
                                        </f:facet>
                                        <h:outputText value="#{logCobranca.nomeUsuarioOAMD}" />
                                    </rich:column>
                                    <rich:column style="text-align: left"
                                                 rendered="#{EmpresaControle.usuarioLogado.usuarioPactoSolucoes}">
                                        <f:facet name="header">
                                            <h:outputText value="Observação" />
                                        </f:facet>
                                        <h:outputText escape="false" value="#{logCobranca.observacao_Apresentar}"/>
                                    </rich:column>
                                    <rich:column style="text-align: center"
                                                 rendered="#{!EmpresaControle.empresaVO.tipoCobrancaPactoPrePago}">
                                        <f:facet name="header">
                                            <h:outputText value="Itens" />
                                        </f:facet>
                                        <a4j:commandLink rendered="#{not empty logCobranca.itensCobrancaPacto}"
                                            value="Itens" action="#{EmpresaControle.abrirListaLogCobranca}"
                                            reRender="formModalItensCobranca"
                                            oncomplete="#{EmpresaControle.msgAlert};#{EmpresaControle.mensagemNotificar}" />
                                    </rich:column>
                                </rich:dataTable>
                            </h:panelGroup>
                        </h:panelGrid>
                    </rich:tab>

                    <rich:tab label="Fypay" id="facilitePayAba"
                              action="#{EmpresaControle.acoesAbaFacilitePay}"
                              oncomplete="#{EmpresaControle.mensagemNotificar}">

                        <h:panelGroup layout="block"
                                      style="display: flex; justify-content: center; padding: 7px;">
                            <h:graphicImage id="iconFacilitePay"
                                            styleClass="tooltipster"
                                            width="100px"
                                            value="../imagens/logo_facilitepay.png"
                                            title="Fypay é um gateway de pagamentos ao qual o Sistema Pacto possui integração"/>
                        </h:panelGroup>

                        <h:panelGroup layout="block"
                                      style="display: flex; justify-content: center; padding: 10px;"
                                      rendered="#{!EmpresaControle.possuiModuloFacilitePay}">
                            <h:outputText value="Não é possível mostrar as configurações sem ter adquirido o módulo Fypay. Entre em contato com a Pacto para adquirir!"
                                          styleClass="tituloCampos"/>
                        </h:panelGroup>

                        <rich:tabPanel id="tabPanelConfigFacilitePay" width="100%" activeTabClass="true"
                                       rendered="#{EmpresaControle.possuiModuloFacilitePay}"
                                       headerAlignment="rigth" switchType="ajax">

                            <rich:tab label="Fypay" id="tabFacilitePay"
                                      action="#{EmpresaControle.carregarDadosMerchantFacilitePay}"
                                      oncomplete="#{EmpresaControle.mensagemNotificar}">
                                <jsp:include flush="true"
                                             page="includes/empresa/include_cadastrar_empresa_facilitepay.jsp"/>
                            </rich:tab>

                            <rich:tab label="Configurações Gerais" id="tabConfigFacilitePay"
                                      rendered="#{EmpresaControle.usuarioLogado.usuarioPactoSolucoes}">
                                <h:panelGrid id="panelGeralFacilite" columns="1" width="100%" headerClass="subordinado" columnClasses="colunaCentralizada">
                                    <f:facet name="header">
                                        <h:outputText value="Configurações Gerais"/>
                                    </f:facet>
                                </h:panelGrid>
                                <h:panelGrid id="panelfacilitePayAba"
                                             columns="2" rowClasses="linhaImpar, linhaPar"
                                             columnClasses="classEsquerda, classDireita" width="100%">

                                    <h:outputText value="TPV:"
                                                  styleClass="tituloCampos"/>
                                    <h:inputText id="valorMetaFacilitePay" size="20"
                                                 value="#{EmpresaControle.empresaVO.valorMetaFacilitePay}"
                                                 onkeypress="return formatar_moeda(this,'.',',',event);"
                                                 onblur="blurinput(this);" onfocus="focusinput(this);" styleClass="form">
                                        <f:converter converterId="FormatadorNumerico" />
                                    </h:inputText>
                                    <h:outputText value="Habilitar Régua de Cobrança:"
                                                  styleClass="tituloCampos"/>
                                    <h:selectBooleanCheckbox id="facilitePayReguaCobranca"
                                                             value="#{EmpresaControle.empresaVO.facilitePayReguaCobranca}"/>
                                    <h:outputText value="Stone Connect:"
                                                  styleClass="tituloCampos"/>
                                    <h:selectBooleanCheckbox id="facilitePayStoneConnect"
                                                             value="#{EmpresaControle.empresaVO.facilitePayStoneConnect}"/>
                                </h:panelGrid>

                            </rich:tab>

                            <rich:tab label="Conciliação" id="tabConfigConcFacilitePay"
                                      action="#{EmpresaControle.abrirAbaConciliacaoFacilite}"
                                      rendered="#{EmpresaControle.usuarioLogado.usuarioPactoSolucoes}">

                                <h:panelGrid id="panelFinanConcCartFacilite" columns="1" width="100%" headerClass="subordinado" columnClasses="colunaCentralizada">
                                    <f:facet name="header">
                                        <h:outputText value="Configurações Gerais"/>
                                    </f:facet>
                                </h:panelGrid>

                                <h:panelGrid columns="2" rowClasses="linhaImpar, linhaPar" columnClasses="classEsquerda, classDireita" width="100%">
                                    <h:outputText value="Habilitar Conciliação de Cartão de Crédito:"
                                                  styleClass="tituloCampos"/>
                                    <h:selectBooleanCheckbox id="facilitePayConciliacaoCartao"
                                                             value="#{EmpresaControle.empresaVO.facilitePayConciliacaoCartao}"/>
                                    <h:outputText value="Habilitar Conciliação de Contas a Pagar:"
                                                  styleClass="tituloCampos"/>
                                    <h:selectBooleanCheckbox id="habilitarConcContasPagarIntFacilitePay"
                                                             value="#{EmpresaControle.empresaVO.concContasPagarFacilitePay}"/>
                                    <h:outputText value="Habilitar Conciliação de Contas a Receber:"
                                                  styleClass="tituloCampos"/>
                                    <h:selectBooleanCheckbox id="habilitarConcContasReceberIntFacilitePay"
                                                             value="#{EmpresaControle.empresaVO.concContasReceberFacilitePay}"/>
                                </h:panelGrid>

                                <h:panelGrid id="panelFinanConcContasBancFacilite" columns="1" width="100%" headerClass="subordinado" columnClasses="colunaCentralizada">
                                    <f:facet name="header">
                                        <h:outputText value="Configurações Contas Bancárias Conectadas"/>
                                    </f:facet>
                                </h:panelGrid>


                                    <h:dataTable id="tableContasConfigFacilite" width="100%"
                                                 rowClasses="linhaImpar, linhaPar"
                                                 rendered="#{EmpresaControle.possuiAccountsConectadas}"
                                                 columnClasses="colunaCentralizada, colunaCentralizada, colunaCentralizada"
                                                 value="#{EmpresaControle.conectoresAtivosPluggy}" var="item">
                                        <h:column>
                                            <f:facet name="header">
                                                <h:outputText value="Item ID"/>
                                            </f:facet>
                                            <h:outputText value="#{item.pluggyItemVO.id}" />
                                        </h:column>
                                        <h:column>
                                            <f:facet name="header">
                                                <h:outputText value="Banco" />
                                            </f:facet>
                                            <h:outputText value="#{item.pluggyItemVO.nameConnector}" />
                                        </h:column>
                                        <h:column>
                                            <h:dataTable id="contasDispo" width="100%" headerClass="subordinado"
                                                         styleClass="tabFormSubordinada" rowClasses="linhaImpar, linhaPar"
                                                         columnClasses="colunaCentralizada, colunaEsquerda, colunaCentralizada"
                                                         value="#{item.pluggyItemVO.pluggyAccountsDTO}" var="account">
                                                <h:column>
                                                    <f:facet name="header">
                                                        <h:outputText value="Associações"/>
                                                    </f:facet>

                                                    <h:outputText rendered="#{account.bloqueadaParaExibicao}"
                                                                  id="textoBlocked"
                                                                  styleClass="tooltipster"
                                                                  title="Este item está bloqueado para não exibir suas operações na conciliação de contas a pagar/receber"
                                                                  style="opacity: 50%;" value="#{account.textoComplementarApresentar}" />
                                                    <h:outputText rendered="#{!account.bloqueadaParaExibicao}" value="#{account.textoComplementarApresentar}" />

                                                    <a4j:commandLink actionListener="#{EmpresaControle.bloquearExibicaoAccountPluggy}"
                                                                     rendered="#{!account.bloqueadaParaExibicao}"
                                                                     styleClass="pure-button pure-button-primary tooltipster"
                                                                     status="false" style="margin-left: 10px;"
                                                                     title="Não exibir transações deste item na conciliação de contas a pagar/receber"
                                                                     reRender="form"
                                                                     oncomplete="#{EmpresaControle.mensagemNotificar}">
                                                        <i class="fa-icon-ban-circle"></i> &nbsp Bloquear Exibição
                                                        <f:attribute name="account" value="#{account}"/>
                                                    </a4j:commandLink>
                                                    <a4j:commandLink actionListener="#{EmpresaControle.desbloquearExibicaoAccountPluggy}"
                                                                     rendered="#{account.bloqueadaParaExibicao}"
                                                                     styleClass="pure-button pure-button-primary tooltipster"
                                                                     status="false" style="margin-left: 10px;"
                                                                     title="Desbloquear para que as transações deste item apareçam na conciliação de contas a pagar/receber"
                                                                     reRender="form"
                                                                     oncomplete="#{EmpresaControle.mensagemNotificar}">
                                                        <i class="fa-icon-ban-circle"></i> &nbsp Desbloquear Exibição
                                                        <f:attribute name="account" value="#{account}"/>
                                                    </a4j:commandLink>
                                                </h:column>
                                            </h:dataTable>
                                        </h:column>
                                    </h:dataTable>
                                <h:panelGroup id="panelNenhumaConta" style="display: block; text-align-last: center; margin-top: 10px;">
                                <h:outputText rendered="#{!EmpresaControle.possuiAccountsConectadas}"
                                              value="Não há contas bancárias conectadas para exibir"/>
                                    <a4j:commandLink action="#{EmpresaControle.abrirConhecimentoConciliacaoFaciliteNovaAba}"
                                                     rendered="#{!EmpresaControle.possuiAccountsConectadas}"
                                                     style="text-decoration: none"
                                                     styleClass="tooltipster"
                                                     oncomplete="#{EmpresaControle.onComplete}"
                                                     title="Clique e veja como cadastrar/conectar suas contas bancárias.">
                                        <i class="fa-icon-question-sign tooltipster"></i>
                                    </a4j:commandLink>
                                </h:panelGroup>
                            </rich:tab>
                        </rich:tabPanel>
                    </rich:tab>

                    <rich:tab id="abaCarteirinha" label="Pacto Print(Carteirinhas)">
                        <h:panelGrid columns="2" rowClasses="linhaImpar, linhaPar"
                                     columnClasses="classEsquerda, classDireita"
                                     width="100%">
                            <h:outputText styleClass="tituloCampos" value="Utilizar Pacto Print"/>
                            <h:selectBooleanCheckbox id="utilizaCarteirinha" styleClass="campos"
                                                     value="#{EmpresaControle.empresaVO.utilizarPactoPrint}">
                                <a4j:support event="onchange" reRender="pnlConfigsPactoPrint"/>
                            </h:selectBooleanCheckbox>
                        </h:panelGrid>

                        <h:panelGroup id="pnlConfigsPactoPrint">
                            <h:panelGrid rendered="#{EmpresaControle.empresaVO.utilizarPactoPrint}"
                                         columns="2" rowClasses="linhaImpar, linhaPar"
                                         columnClasses="classEsquerda, classDireita"
                                         width="100%">

                                <h:outputText styleClass="tituloCampos" value="Validade carteirinha:"/>
                                <h:panelGroup>
                                    <h:inputText id="validadeCartSocio" size="3" onblur="blurinput(this);"
                                                 onfocus="focusinput(this);"
                                                 styleClass="form"
                                                 value="#{EmpresaControle.empresaVO.validadeMesesCarteirinhaSocio}"/>
                                    <h:outputText styleClass="tituloCampos" value="meses"/>
                                </h:panelGroup>

                                <h:outputText styleClass="tituloCampos" value="Presidente:"/>

                                <h:inputText id="presidente" size="80" onblur="blurinput(this);" onfocus="focusinput(this);"
                                             styleClass="form" value="#{EmpresaControle.empresaVO.presidente}"/>

                                <h:outputText styleClass="tituloCampos" value="Superintendente:"/>

                                <h:inputText id="superintendente" size="80" onblur="blurinput(this);"
                                             onfocus="focusinput(this);"
                                             styleClass="form" value="#{EmpresaControle.empresaVO.superintendente}"/>
                            </h:panelGrid>
                        </h:panelGroup>
                    </rich:tab>

                </rich:tabPanel>


                <h:panelGrid columns="1" width="100%" styleClass="tabMensagens">
                    <h:panelGrid id="panelErroMensagem" columns="3" width="100%" styleClass="tabMensagens">
                        <h:panelGrid columns="1" width="100%">

                            <h:outputText value=" " />

                        </h:panelGrid>
                        <h:commandButton rendered="#{EmpresaControle.sucesso}" image="./imagens/sucesso.png" />
                        <h:commandButton rendered="#{EmpresaControle.erro}" image="./imagens/erro.png" />
                        <h:panelGrid columns="1" width="100%">
                            <h:outputText id="mensagem" styleClass="mensagem" value="#{EmpresaControle.mensagem}" />
                            <h:outputText id="mensagemDetalhada" styleClass="mensagemDetalhada"
                                value="#{EmpresaControle.mensagemDetalhada}" />
                        </h:panelGrid>
                    </h:panelGrid>
                    <h:panelGrid columns="1" width="100%" columnClasses="colunaCentralizada">
                        <c:if test="${modulo eq 'zillyonWeb'}">
                            <h:panelGroup>
                                <a4j:commandButton id="novo" immediate="true" action="#{EmpresaControle.novo}"
                                    rendered="#{EmpresaControle.usuarioLogado.usuarioPactoSolucoes}" value="#{msg_bt.btn_novo}"
                                    alt="#{msg.msg_novo_dados}" accesskey="1" styleClass="botoes nvoBt btSec" />
                                <h:outputText value="    " />
                                <a4j:commandButton id="salvar" action="#{EmpresaControle.gravar}"
                                                   value="#{msg_bt.btn_gravar}"
                                                   oncomplete="#{EmpresaControle.mensagemNotificar};#{EmpresaControle.msgAlert};#{EmpresaControle.abrirFecharModalTokenOperacao}"
                                                   reRender="abaLogotipo, panelErroMensagem,mdlMensagemGenerica,modalTokenOperacao,form" accesskey="2"
                                                   styleClass="botoes nvoBt">
                                </a4j:commandButton>
                                <h:outputText value="    " />
                                <a4j:commandButton id="excluir" action="#{EmpresaControle.confirmarExcluir}"
                                    value="#{msg_bt.btn_excluir}" reRender="form,mdlMensagemGenerica"
                                    oncomplete="#{EmpresaControle.msgAlert}" alt="#{msg.msg_excluir_dados}"
                                    accesskey="3" styleClass="botoes nvoBt btSec btPerigo" >
                                <f:param name="metodochamar" value="excluir"/>
                                </a4j:commandButton>
                                <h:outputText value="    " />

                                <a4j:commandButton id="clonar"
                                                   rendered="#{EmpresaControle.usuarioLogado.usuarioPactoSolucoes && EmpresaControle.empresaVO.codigo > 0}"
                                                   oncomplete="#{EmpresaControle.mensagemNotificar};fireElementFromAnyParent('form:btnAtualizaTempo')"
                                                   action="#{EmpresaControle.clonar}"
                                                   value="#{msg_bt.btn_Clonar}"
                                                   reRender="form, mdlAviso, panelMensagem"
                                                   alt="#{msg.msg_clonar_dados}" accesskey="4"
                                                   styleClass="botoes nvoBt btSec"/>

                                <h:outputText value="    "/>

                                <a4j:commandButton id="consultar" immediate="true"
                                    action="#{EmpresaControle.inicializarConsultar}" value="#{msg_bt.btn_voltar_lista}"
                                    alt="#{msg.msg_consultar_dados}" accesskey="4" styleClass="botoes nvoBt btSec" />

                                <a4j:commandLink action="#{EmpresaControle.realizarConsultaLogObjetoSelecionado}"
                                    oncomplete="abrirPopup('./visualizadorLogForm.jsp', 'VisualizadorLog', 785, 595);"
                                    title="Visualizar Log" style="display: inline-block; padding: 8px 15px;"
                                    styleClass="botoes nvoBt btSec btPerigo">
                                    <i style="text-decoration: none" class="fa-icon-list" />
                                </a4j:commandLink>

                            </h:panelGroup>

                        </c:if>
                        <c:if test="${modulo eq 'centralEventos'}">
                            <h:panelGroup>
                                <a4j:commandButton id="novo" immediate="true" action="#{EmpresaControle.novo}"
                                    value="#{msg_bt.btn_novo}" alt="#{msg.msg_novo_dados}" accesskey="1"
                                    styleClass="botoes nvoBt btSec" />
                                <h:outputText value="    " />
                                <a4j:commandButton id="salvar" action="#{EmpresaControle.gravarCE}"
                                    value="#{msg_bt.btn_gravar}" alt="#{msg.msg_gravar_dados}" accesskey="2"
                                    styleClass="botoes nvoBt" actionListener="#{EmpresaControle.autorizacao}">
                                    <!-- entidade.empresa -->
                                    <f:attribute name="entidade" value="119" />
                                    <!-- operacao.gravar-->
                                    <f:attribute name="operacao" value="G" />
                                </a4j:commandButton>
                                <h:outputText value="    " />
                                <a4j:commandButton id="excluir"
                                    onclick="if(!confirm('Confirma exclusão dos dados?')){return true;}"
                                    action="#{EmpresaControle.excluirCE}" value="#{msg_bt.btn_excluir}"
                                    alt="#{msg.msg_excluir_dados}" accesskey="3"
                                    styleClass="botoes nvoBt btSec btPerigo"
                                    actionListener="#{EmpresaControle.autorizacao}">
                                    <!-- entidade.empresa -->
                                    <f:attribute name="entidade" value="119" />
                                    <!-- operacao.excluir-->
                                    <f:attribute name="operacao" value="E" />
                                </a4j:commandButton>
                                <h:outputText value="    " />
                                <a4j:commandButton id="consultar" immediate="true"
                                    action="#{EmpresaControle.inicializarConsultar}" value="#{msg_bt.btn_consultar}"
                                    alt="#{msg.msg_consultar_dados}" accesskey="4" styleClass="botoes nvoBt btSec"
                                    actionListener="#{EmpresaControle.autorizacao}">
                                    <!-- entidade.empresa -->
                                    <f:attribute name="entidade" value="119" />
                                    <!-- operacao.consultar-->
                                    <f:attribute name="operacao" value="C" />
                                </a4j:commandButton>
                            </h:panelGroup>
                        </c:if>
                    </h:panelGrid>
                </h:panelGrid>
            </h:panelGrid>
        </h:form>
    </h:panelGrid>

    <rich:modalPanel id="modalProcessoRealizado" autosized="true" shadowOpacity="true" width="500" height="150">
        <f:facet name="header">
            <h:panelGroup>
                <h:outputText value="Atualização de pagamentos com cartão de débito" />
            </h:panelGroup>
        </f:facet>
        <f:facet name="controls">
            <h:panelGroup>
                <h:graphicImage value="/imagens/close.png" style="cursor:pointer" id="hideListaClientes" />
                <rich:componentControl for="modalProcessoRealizado" attachTo="hideListaClientes" operation="hide"
                    event="onclick" />
            </h:panelGroup>
        </f:facet>
        <a4j:form id="formProcessoRealizado" ajaxSubmit="true">
            <h:outputText escape="false" styleClass="tituloCamposMenor"
                value="#{EmpresaControle.descricaoProcessoRealizado}" />
            <br />&nbsp;
            <div style="text-align: center;">
                <a4j:commandButton oncomplete="Richfaces.hideModalPanel('modalProcessoRealizado');"
                    image="imagens/OK_Modal.png">
                </a4j:commandButton>
            </div>
        </a4j:form>
    </rich:modalPanel>
    <rich:modalPanel id="modalEditarFormasPagamentoNFCe" autosized="true" shadowOpacity="true" width="450" height="250"
        styleClass="novaModal"
        onshow="document.getElementById('formFormasPagamentoNFCe:comboTipoProdutoNFCe').focus();">
        <f:facet name="header">
            <h:panelGroup>
                <h:outputText value="Alterar formas de pagamento para emissão automática de NFCe" />
            </h:panelGroup>
        </f:facet>
        <f:facet name="controls">
            <h:panelGroup>
                <h:outputText
                    styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign btnSelecionarModalidade"
                    id="hidelinkFormasPagamentoNFCe" />
                <rich:componentControl for="modalEditarFormasPagamentoNFCe" attachTo="hidelinkFormasPagamentoNFCe"
                    operation="hide" event="onclick" />
            </h:panelGroup>
        </f:facet>
        <a4j:form id="formFormasPagamentoNFCe" styleClass="font-size-Em-max">
            <input type="hidden" value="${modulo}" name="modulo" />
            <h:panelGrid columns="1" width="100%" columnClasses="colunaCentralizada">
                <h:panelGrid id="panelFormaPagamentoNFCe" columns="1" width="100%" cellpadding="10"
                    columnClasses="colunaEsquerda" styleClass="font-size-Em-max">
                    <h:panelGroup>
                        <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font"
                            style="padding-right: 10px" value="Forma de Pagamento:" />
                        <h:selectOneMenu id="comboFormasPagamentoNFCe" onblur="blurinput(this);"
                            onfocus="focusinput(this);" styleClass="form"
                            value="#{EmpresaControle.codigoFormaPagamentoNFCe}">
                            <f:selectItems value="#{EmpresaControle.listaFormasPagamento}" />
                        </h:selectOneMenu>
                    </h:panelGroup>
                </h:panelGrid>
                <a4j:commandButton id="addFormaPagamentoNFCe" action="#{EmpresaControle.adicionarFormaPagamentoNFCe}"
                    reRender="tableFormasPagamentoNFCe, panelMensagemFormaPagamentoNFCe, comboFormasPagamentoNFCe"
                    focus="formFormasPagamentoNFCe:comboFormasPagamentoNFCe" value="#{msg_bt.btn_adicionar}"
                    image="./imagens/botaoAdicionar.png" accesskey="8" styleClass="botoes" />
                <rich:spacer height="10px" />
            </h:panelGrid>
            <div style="overflow:auto; border:1px solid #ccc; height: 150px;">
                <h:dataTable id="tableFormasPagamentoNFCe" width="100%" headerClass="subordinado"
                    styleClass="tabFormSubordinada" rowClasses="linhaImpar, linhaPar"
                    columnClasses="colunaCentralizada, colunaEsquerda, colunaCentralizada"
                    value="#{EmpresaControle.listaFormasPagamentoNFCe}" var="tpFormaPagamento">
                    <h:column>
                        <f:facet name="header">
                            <h:outputText value="Código"/>
                        </f:facet>
                        <h:outputText value="#{tpFormaPagamento.value.sigla}" />
                    </h:column>
                    <h:column>
                        <f:facet name="header">
                            <h:outputText value="Descrição" />
                        </f:facet>
                        <h:outputText value="#{tpFormaPagamento.label}" />
                    </h:column>
                    <h:column>
                        <f:facet name="header">
                            <h:outputText value="#{msg_bt.btn_opcoes}" />
                        </f:facet>
                        <h:panelGroup>
                            <h:outputText value="    " />
                            <a4j:commandButton id="removerFormaPagamentoNFCe" reRender="tableFormasPagamentoNFCe"
                                action="#{EmpresaControle.removerFormaPagamentoNFCe}" value="#{msg_bt.btn_excluir}"
                                image="./imagens/botaoRemover.png" accesskey="7" styleClass="botoes" />
                        </h:panelGroup>
                    </h:column>

                </h:dataTable>
            </div>
            <rich:spacer height="10px"></rich:spacer>
            <h:panelGrid id="panelMensagemFormaPagamentoNFCe" columns="2" width="100%">
                <h:panelGrid columns="1" width="100%">
                    <h:outputText styleClass="mensagemDetalhada" value="#{EmpresaControle.mensagemDetalhada}" />
                </h:panelGrid>
            </h:panelGrid>
            <rich:spacer height="10px"></rich:spacer>
            <h:panelGroup>
                <a4j:commandButton value="Gravar" styleClass="botoes nvoBt"
                    action="#{EmpresaControle.gravarFormasPagamentoNFCe}" oncomplete="#{EmpresaControle.msgAlert}"
                    reRender="form" />
            </h:panelGroup>

        </a4j:form>
    </rich:modalPanel>

    <rich:modalPanel id="modalEditarFormasPagamentoNFSe" autosized="true" shadowOpacity="true" width="450" height="250"
        styleClass="novaModal"
        onshow="document.getElementById('formFormasPagamentoNFSe:comboTipoProdutoNFSe').focus();">
        <f:facet name="header">
            <h:panelGroup>
                <h:outputText value="Alterar formas de pagamento para emissão automática de NFSe" />
            </h:panelGroup>
        </f:facet>
        <f:facet name="controls">
            <h:panelGroup>
                <h:outputText
                    styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign btnSelecionarModalidade"
                    id="hidelinkFormasPagamentoNFSe" />
                <rich:componentControl for="modalEditarFormasPagamentoNFSe" attachTo="hidelinkFormasPagamentoNFSe"
                    operation="hide" event="onclick" />
            </h:panelGroup>
        </f:facet>
        <a4j:form id="formFormasPagamentoNFSe" styleClass="font-size-Em-max">
            <input type="hidden" value="${modulo}" name="modulo" />
            <h:panelGrid columns="1" width="100%" columnClasses="colunaCentralizada">
                <h:panelGrid id="panelFormaPagamentoNFSe" columns="1" width="100%" cellpadding="10"
                    columnClasses="colunaEsquerda" styleClass="font-size-Em-max">
                    <h:panelGroup>
                        <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font"
                            style="padding-right: 10px" value="Forma de Pagamento:" />
                        <h:selectOneMenu id="comboFormasPagamentoNFSe" onblur="blurinput(this);"
                            onfocus="focusinput(this);" styleClass="form"
                            value="#{EmpresaControle.codigoFormaPagamentoNFSe}">
                            <f:selectItems value="#{EmpresaControle.listaFormasPagamento}" />
                        </h:selectOneMenu>
                    </h:panelGroup>
                </h:panelGrid>
                <a4j:commandButton id="addFormaPagamentoNFSe" action="#{EmpresaControle.adicionarFormaPagamentoNFSe}"
                    reRender="tableFormasPagamentoNFSe, panelMensagemFormaPagamentoNFSe, comboFormasPagamentoNFSe"
                    focus="formFormasPagamentoNFSe:comboFormasPagamentoNFSe" value="#{msg_bt.btn_adicionar}"
                    image="./imagens/botaoAdicionar.png" accesskey="8" styleClass="botoes" />
                <rich:spacer height="10px" />
            </h:panelGrid>
            <div style="overflow:auto; border:1px solid #ccc; height: 150px;">
                <h:dataTable id="tableFormasPagamentoNFSe" width="100%" headerClass="subordinado"
                    styleClass="tabFormSubordinada" rowClasses="linhaImpar, linhaPar"
                    columnClasses="colunaCentralizada, colunaEsquerda, colunaCentralizada"
                    value="#{EmpresaControle.listaFormasPagamentoNFSe}" var="tpFormaPagamento">
                    <h:column>
                        <f:facet name="header">
                            <h:outputText value="Código" />
                        </f:facet>
                        <h:outputText value="#{tpFormaPagamento.value.sigla}" />
                    </h:column>
                    <h:column>
                        <f:facet name="header">
                            <h:outputText value="Descrição" />
                        </f:facet>
                        <h:outputText value="#{tpFormaPagamento.label}" />
                    </h:column>
                    <h:column>
                        <f:facet name="header">
                            <h:outputText value="#{msg_bt.btn_opcoes}" />
                        </f:facet>
                        <h:panelGroup>
                            <h:outputText value="    " />
                            <a4j:commandButton id="removerFormaPagamentoNFSe" reRender="tableFormasPagamentoNFSe"
                                action="#{EmpresaControle.removerFormaPagamentoNFSe}" value="#{msg_bt.btn_excluir}"
                                image="./imagens/botaoRemover.png" accesskey="7" styleClass="botoes" />
                        </h:panelGroup>
                    </h:column>
                </h:dataTable>
            </div>
            <rich:spacer height="10px"></rich:spacer>
            <h:panelGrid id="panelMensagemFormaPagamentoNFSe" columns="2" width="100%">
                <h:panelGrid columns="1" width="100%">
                    <h:outputText styleClass="mensagemDetalhada" value="#{EmpresaControle.mensagemDetalhada}" />
                </h:panelGrid>
            </h:panelGrid>
            <rich:spacer height="10px"></rich:spacer>
            <h:panelGroup>
                <a4j:commandButton value="Gravar" styleClass="botoes nvoBt"
                    action="#{EmpresaControle.gravarFormasPagamentoNFSe}" oncomplete="#{EmpresaControle.msgAlert}"
                    reRender="form" />
            </h:panelGroup>

        </a4j:form>
    </rich:modalPanel>

    <rich:modalPanel id="modalReplicarRetentativa" autosized="true" shadowOpacity="true" width="500"
        styleClass="novaModal">
        <f:facet name="header">
            <h:panelGroup>
                <h:outputText value="Replicar configuração retentativa" />
            </h:panelGroup>
        </f:facet>
        <f:facet name="controls">
            <h:panelGroup>
                <h:outputText
                    styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign btnSelecionarModalidade"
                    id="hideModalReplicarRetentativa" />
                <rich:componentControl for="modalReplicarRetentativa" attachTo="hideModalReplicarRetentativa"
                    operation="hide" event="onclick" />
            </h:panelGroup>
        </f:facet>
        <a4j:form id="formReplicarRetentativa" ajaxSubmit="true">

            <h:panelGroup layout="block" id="panelGeralReten">

                <h:panelGroup layout="block" id="panelSuperRetenta">

                    <h:outputText styleClass="texto-size-14 texto-cor-cinza texto-font"
                        value="Obs: Caso o recurso esteja desabilitado nas outras empresas, será habilitado automaticamente e replicado de acordo com as configurações que você definiu agora." />

                </h:panelGroup>

                <h:panelGroup layout="block" id="panelPickRetenta" style="padding-top: 10px">

                    <h:outputText styleClass="texto-size-14 texto-cor-cinza texto-font texto-bold"
                        value="Selecione abaixo as empresas que irão receber as configurações:" />

                    <rich:pickList value="#{EmpresaControle.empresasReplicar}" copyAllControlLabel="Adicionar todas"
                        copyControlLabel="Adicionar selecionada" removeAllControlLabel="Remover todas"
                        removeControlLabel="Remover selecionada">
                        <f:selectItems value="#{EmpresaControle.listaEmpresasGeral}" />
                    </rich:pickList>
                </h:panelGroup>

                <h:panelGroup layout="block" style="text-align: center; padding-top: 20px; padding-bottom: 10px"
                    id="panelBtnRetenta">

                    <a4j:commandLink action="#{EmpresaControle.replicarConfiguracaoRetentativa}"
                        oncomplete="#{EmpresaControle.mensagemNotificar};#{EmpresaControle.msgAlert}"
                        reRender="panelReenvioCobranca, panelErroMensagem" value="Replicar todas empresas"
                        styleClass="botoes nvoBt" />

                    <a4j:commandLink onclick="Richfaces.hideModalPanel('modalReplicarRetentativa')"
                        reRender="panelReenvioCobranca, panelErroMensagem, modalReplicarRetentativa" value="Fechar"
                        styleClass="botoes nvoBt btSec btPerigo" />
                </h:panelGroup>

            </h:panelGroup>
        </a4j:form>
    </rich:modalPanel>

    <rich:modalPanel id="modalAtualizarAutorizacao" autosized="true" shadowOpacity="true" width="500"
        styleClass="novaModal">
        <f:facet name="header">
            <h:panelGroup>
                <h:outputText value="Atualizar autorização de cobrança" />
            </h:panelGroup>
        </f:facet>
        <f:facet name="controls">
            <h:panelGroup>
                <h:outputText
                    styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign btnSelecionarModalidade"
                    id="hideModalAtualizarAutorizacao" />
                <rich:componentControl for="modalAtualizarAutorizacao" attachTo="hideModalAtualizarAutorizacao"
                    operation="hide" event="onclick" />
            </h:panelGroup>
        </f:facet>
        <a4j:form id="formAtualizarAuto" ajaxSubmit="true">

            <h:panelGroup layout="block">

                <h:panelGroup layout="block">

                    <h:outputText escape="false" styleClass="texto-size-14 texto-cor-cinza texto-font texto-bold"
                        value="Deseja atualizar o(s) convênio(s) de cobrança(s) padrão de todas as empresas para os alunos já lançados anteriormente?" />
                    <br />
                    <br />
                    <h:outputText escape="false" styleClass="texto-size-14 texto-cor-cinza texto-font" value="Obs: Serão ajustados os convênios de cobrança de todos os alunos, de todas as unidades, já lançados anteriormente.<br/>
                                    Ex: Unidade 1: Convênio padrão \" CIELO\", todos os alunos da UNIDADE 1 que já
                        possuem convênio de cobrança qualquer, serão atualizados para o convênio CIELO que é o padrão
                        dessa primeira unidade.<br />
                    Unidade 2: Convênio padrão \"GETNET\", todos os alunos da UNIDADE 2 que já possuem convênio de
                    cobrança qualquer, serão atualizados para o convênio GETNET que é o padrão dessa segunda unidade."/>

                </h:panelGroup>

                <h:panelGroup layout="block" style="text-align: center; padding-top: 20px; padding-bottom: 10px">

                    <a4j:commandLink action="#{EmpresaControle.atualizarAutorizacaoCobrancaCliente}"
                        oncomplete="#{EmpresaControle.mensagemNotificar};#{EmpresaControle.msgAlert}"
                        reRender="panelReenvioCobranca, panelErroMensagem" value="Atualizar"
                        styleClass="botoes nvoBt" />

                    <a4j:commandLink onclick="Richfaces.hideModalPanel('modalAtualizarAutorizacao')"
                        reRender="panelReenvioCobranca, panelErroMensagem, modalAtualizarAutorizacao" value="Fechar"
                        styleClass="botoes nvoBt btSec btPerigo" />
                </h:panelGroup>

            </h:panelGroup>
        </a4j:form>
    </rich:modalPanel>

    <rich:modalPanel id="modalReplicarContaCorrente" autosized="true" shadowOpacity="true" width="500"
        styleClass="novaModal">
        <f:facet name="header">
            <h:panelGroup>
                <h:outputText value="Replicar configuração Conta Corrente" />
            </h:panelGroup>
        </f:facet>
        <f:facet name="controls">
            <h:panelGroup>
                <h:outputText
                    styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign btnSelecionarModalidade"
                    id="hideModalReplicarContaCorrente" />
                <rich:componentControl for="modalReplicarContaCorrente" attachTo="hideModalReplicarContaCorrente"
                    operation="hide" event="onclick" />
            </h:panelGroup>
        </f:facet>
        <a4j:form id="formReplicarContaCorrente" ajaxSubmit="true">

            <h:panelGroup layout="block" id="panelGeralConta">

                <h:panelGroup layout="block" id="panelSuperConta">

                    <%--<h:outputText--%>
                    <%--styleClass="texto-size-14 texto-cor-cinza texto-font"--%>
                    <%--value="Obs: Caso o recurso esteja desabilitado nas outras empresas, será habilitado automaticamente e replicado de acordo com as configurações que você definiu agora."/>--%>

                </h:panelGroup>

                <h:panelGroup layout="block" id="panelPickConta" style="padding-top: 10px">

                    <h:outputText styleClass="texto-size-14 texto-cor-cinza texto-font texto-bold"
                        value="Selecione abaixo as empresas que irão receber as configurações:" />

                    <rich:pickList value="#{EmpresaControle.empresasReplicarContaCorrente}"
                        copyAllControlLabel="Adicionar todas" copyControlLabel="Adicionar selecionada"
                        removeAllControlLabel="Remover todas" removeControlLabel="Remover selecionada">
                        <f:selectItems value="#{EmpresaControle.listaEmpresasGeral}" />
                    </rich:pickList>
                </h:panelGroup>

                <h:panelGroup layout="block" style="text-align: center; padding-top: 20px; padding-bottom: 10px"
                    id="panelBtnConta">

                    <a4j:commandLink action="#{EmpresaControle.replicarConfiguracaoContaCorrente}"
                        oncomplete="#{EmpresaControle.mensagemNotificar};#{EmpresaControle.msgAlert}"
                        reRender="panelReenvioCobranca, panelErroMensagem" value="Replicar" styleClass="botoes nvoBt" />

                    <a4j:commandLink onclick="Richfaces.hideModalPanel('modalReplicarContaCorrente')"
                        reRender="panelReenvioCobranca, panelErroMensagem, modalReplicarRetentativa" value="Fechar"
                        styleClass="botoes nvoBt btSec btPerigo" />
                </h:panelGroup>

            </h:panelGroup>
        </a4j:form>
    </rich:modalPanel>

    <rich:modalPanel id="modalProdutoParceiro" autosized="true" shadowOpacity="true" width="500" styleClass="novaModal">

        <f:facet name="header">
            <h:panelGroup>
                <h:outputText value="Produtos para resgate" />
            </h:panelGroup>
        </f:facet>
        <f:facet name="controls">
            <h:panelGroup>
                <h:outputText
                    styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign btnSelecionarModalidade"
                    id="hidelinkModalProdutoParceiro" />
                <rich:componentControl for="modalProdutoParceiro" attachTo="hidelinkModalProdutoParceiro"
                    operation="hide" event="onclick" />
            </h:panelGroup>
        </f:facet>

        <a4j:form id="formProdutoParceiro" ajaxSubmit="true">
            <h:panelGroup layout="block" style="overflow:auto; border:1px solid #ccc; max-height: 300px;"
                id="panelModalProdutoParceiro">
                <h:dataTable width="100%" headerClass="subordinado" styleClass="tabFormSubordinada"
                    rowClasses="linhaImpar, linhaPar"
                    columnClasses="colunaCentralizada, colunaEsquerda, colunaCentralizada"
                    value="#{EmpresaControle.listaProdutosParceiro}" var="produtoParceiro">
                    <h:column>
                        <f:facet name="header">
                            <h:selectBooleanCheckbox value="#{EmpresaControle.marcarTodosProdutosParceiro}">
                                <a4j:support event="onclick"
                                    action="#{EmpresaControle.acaoMarcarTodosProdutosParceiroFidelidade}"
                                    reRender="panelModalProdutoParceiro" />
                            </h:selectBooleanCheckbox>
                        </f:facet>
                        <h:selectBooleanCheckbox value="#{produtoParceiro.selecionado}" />
                    </h:column>
                    <h:column>
                        <f:facet name="header">
                            <h:outputText value="Descrição" />
                        </f:facet>
                        <h:outputText value="#{produtoParceiro.descricao}" />
                    </h:column>
                    <h:column>
                        <f:facet name="header">
                            <h:outputText value="Pontos" />
                        </f:facet>
                        <h:outputText value="#{produtoParceiro.pontos}" />
                    </h:column>
                    <h:column>
                        <f:facet name="header">
                            <h:outputText value="Valor R$" />
                        </f:facet>
                        <h:inputText value="#{produtoParceiro.valor}"
                            onkeypress="return formatar_moeda(this,'.',',',event);">
                            <f:converter converterId="FormatadorNumerico" />
                        </h:inputText>
                    </h:column>
                    <h:column>
                        <f:facet name="header">
                            <h:outputText value="Código Externo (SKU)" />
                        </f:facet>
                        <h:outputText value="#{produtoParceiro.codigoExterno}" />
                    </h:column>
                </h:dataTable>
            </h:panelGroup>

            <h:panelGroup layout="block" style="padding-top: 10px; text-align: center">
                <a4j:commandButton value="Adicionar Produtos" styleClass="botoes nvoBt"
                    action="#{EmpresaControle.adicionarProdutosParceiroFidelidade}"
                    oncomplete="#{EmpresaControle.msgAlert};#{EmpresaControle.mensagemNotificar}"
                    reRender="form:produtosParceiroFidelidade" />
            </h:panelGroup>
        </a4j:form>
    </rich:modalPanel>

    <rich:modalPanel id="modalItensCobranca" autosized="true" shadowOpacity="true" width="800" styleClass="novaModal">

        <f:facet name="header">
            <h:panelGroup>
                <h:outputText value="Itens Cobrança" />
            </h:panelGroup>
        </f:facet>
        <f:facet name="controls">
            <h:panelGroup>
                <h:outputText
                    styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign btnSelecionarModalidade"
                    id="hidelinkModalItensCobranca" />
                <rich:componentControl for="modalItensCobranca" attachTo="hidelinkModalItensCobranca" operation="hide"
                    event="onclick" />
            </h:panelGroup>
        </f:facet>

        <a4j:form id="formModalItensCobranca" ajaxSubmit="true">
            <h:panelGroup layout="block" style="overflow:auto; border:1px solid #ccc; max-height: 300px;"
                id="panelModalItensCobranca">
                <rich:dataTable width="100%" headerClass="subordinado" styleClass="tabFormSubordinada"
                    rowClasses="linhaImpar, linhaPar" value="#{EmpresaControle.itensCobrancaPacto}" var="item">

                    <rich:column sortBy="#{item.nome}">
                        <f:facet name="header">
                            <h:outputText value="Nome" />
                        </f:facet>
                        <h:outputText value="#{item.nome}" />
                    </rich:column>
                    <rich:column sortBy="#{item.movParcela}">
                        <f:facet name="header">
                            <h:outputText value="Parcela" />
                        </f:facet>
                        <h:outputText value="#{item.movParcela}" />
                    </rich:column>
                    <rich:column sortBy="#{item.descricaoParcela}">
                        <f:facet name="header">
                            <h:outputText value="Descrição" />
                        </f:facet>
                        <h:outputText value="#{item.descricaoParcela}" />
                    </rich:column>
                    <rich:column sortBy="#{item.valor}">
                        <f:facet name="header">
                            <h:outputText value="Valor R$" />
                        </f:facet>
                        <h:outputText value="#{item.valor_Apresentar}" />
                    </rich:column>
                    <rich:column sortBy="#{item.data_Ordenar}">
                        <f:facet name="header">
                            <h:outputText value="Data" />
                        </f:facet>
                        <h:outputText value="#{item.data}" />
                    </rich:column>
                    <rich:column sortBy="#{item.tipoItem}">
                        <f:facet name="header">
                            <h:outputText value="Tipo" />
                        </f:facet>
                        <h:outputText value="#{item.tipoItem}" />
                    </rich:column>
                </rich:dataTable>
            </h:panelGroup>

            <h:panelGroup layout="block" id="panelExportarCobrancaPacto" style="padding: 10px; text-align: left">
                <a4j:commandLink styleClass="exportadores" id="btnExportarCobrancaPacto"
                    actionListener="#{ExportadorListaControle.exportar}"
                    oncomplete="abrirPopup('../../UpdateServlet?op=downloadfile&file=#{ExportadorListaControle.fileName}&mimetype=application/vnd.ms-excel','Transacoes', 800,200);#{ExportadorListaControle.msgAlert}"
                    accesskey="2">
                    <f:attribute name="lista" value="#{EmpresaControle.itensCobrancaPacto}" />
                    <f:attribute name="tipo" value="xls" />
                    <f:attribute name="prefixo" value="ItensCobranca" />
                    <f:attribute name="atributos"
                        value="movParcela=Parcela,descricaoParcela=Descrição,pessoa=Pessoa,nome=Nome,valor_Apresentar=Valor,data=Data,tipoItem=Tipo,remessaItem=RemessaItem,transacao=Transação" />
                    <h:outputText title="Exportar para o formato Excel" styleClass="btn-print-2 excel tooltipster" />
                </a4j:commandLink>
            </h:panelGroup>
        </a4j:form>
    </rich:modalPanel>

    <rich:modalPanel id="modalEditarTipoProdutoNFSe" autosized="true"
                     shadowOpacity="true" width="450" height="250"
                     styleClass="novaModal"
                     onshow="document.getElementById('formTipoProdutoNFSe:comboTipoProduto').focus();">
        <f:facet name="header">
            <h:panelGroup>
                <h:outputText value="Alterar tipos de produtos que emitem NFSe"/>
            </h:panelGroup>
        </f:facet>
        <f:facet name="controls">
            <h:panelGroup>
                <h:outputText styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign btnSelecionarModalidade" id="hidelink"/>
                <rich:componentControl for="modalEditarTipoProdutoNFSe"
                                       attachTo="hidelink" operation="hide"
                                       event="onclick" />
            </h:panelGroup>
        </f:facet>
        <a4j:form id="formTipoProdutoNFSe" styleClass="font-size-Em-max">
            <input type="hidden" value="${modulo}" name="modulo"/>
            <h:panelGrid columns="1" width="100%"
                         columnClasses="colunaCentralizada">
                <h:panelGrid id="panelConfirmacao" columns="1" width="100%" cellpadding="10"
                             columnClasses="colunaEsquerda" styleClass="font-size-Em-max">
                    <h:panelGroup>
                        <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font" style="padding-right: 10px" value="Tipo de Produto:" />
                        <h:selectOneMenu  id="comboTipoProduto" onblur="blurinput(this);"  onfocus="focusinput(this);" styleClass="form" value="#{EmpresaControle.codigoTipoProduto}" >
                            <f:selectItems  value="#{EmpresaControle.listaTipoProduto}" />
                        </h:selectOneMenu>
                    </h:panelGroup>
                </h:panelGrid>
                <a4j:commandButton id="addTipoProduto" action="#{EmpresaControle.adicionarTipoProduto}" reRender="tableTipoProduto, panelMensagem, comboTipoProduto"  focus="formTipoProdutoNFSe:comboTipoProduto" value="#{msg_bt.btn_adicionar}" image= "./imagens/botaoAdicionar.png" accesskey="8" styleClass="botoes"/>
                <rich:spacer height="10px"/>
            </h:panelGrid>
            <div style="overflow:auto; border:1px solid #ccc; height: 150px;">
                <h:dataTable id="tableTipoProduto" width="100%" headerClass="subordinado" styleClass="tabFormSubordinada"
                             rowClasses="linhaImpar, linhaPar" columnClasses="colunaCentralizada, colunaEsquerda, colunaCentralizada"
                             value="#{EmpresaControle.listaTipoProdutoNFSe}" var="tpProduto">
                    <h:column>
                        <f:facet name="header">
                            <h:outputText  value="Código" />
                        </f:facet>
                        <h:outputText  value="#{tpProduto.value}" />
                    </h:column>
                    <h:column>
                        <f:facet name="header">
                            <h:outputText  value="Descrição" />
                        </f:facet>
                        <h:outputText  value="#{tpProduto.label}" />
                    </h:column>
                    <h:column>
                        <f:facet name="header">
                            <h:outputText  value="#{msg_bt.btn_opcoes}" />
                        </f:facet>
                        <h:panelGroup>
                            <h:outputText value="    "/>
                            <a4j:commandButton id="removerTipoProduto" reRender="tableTipoProduto" action="#{EmpresaControle.removerTipoProdutoNFSe}" value="#{msg_bt.btn_excluir}" image="./imagens/botaoRemover.png" accesskey="7" styleClass="botoes"/>
                        </h:panelGroup>
                    </h:column>

                </h:dataTable>
            </div>
            <rich:spacer height="10px"></rich:spacer>
            <h:panelGrid id="panelMensagem" columns="2" width="100%">
                <h:panelGrid columns="1" width="100%">
                    <h:outputText styleClass="mensagemDetalhada"
                                  value="#{EmpresaControle.mensagemDetalhada}" />
                </h:panelGrid>
            </h:panelGrid>
            <rich:spacer height="10px"></rich:spacer>
            <h:panelGroup>
                <a4j:commandButton id="login" value="Gravar"
                                   styleClass="botoes nvoBt"
                                   action="#{EmpresaControle.gravarTiposProdutoNFSe}"
                                   oncomplete="#{EmpresaControle.msgAlert}"
                                   reRender="form" />

                <a4j:commandButton value="Restaurar Padrão"
                                   action="#{EmpresaControle.restaurarTiposProdutoNFSePadrao}"
                                   reRender="tableTipoProduto"
                                   styleClass="botoes nvoBt btSec" />
            </h:panelGroup>


        </a4j:form>
    </rich:modalPanel>

    <rich:modalPanel id="modalEditarTipoProdutoNFCe" autosized="true"
                     shadowOpacity="true" width="450" height="250"
                     styleClass="novaModal"
                     onshow="document.getElementById('formTipoProdutoNFCe:comboTipoProdutoNFCe').focus();">
        <f:facet name="header">
            <h:panelGroup>
                <h:outputText value="Alterar tipos de produtos que emitem NFCe"/>
            </h:panelGroup>
        </f:facet>
        <f:facet name="controls">
            <h:panelGroup>
                <h:outputText styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign btnSelecionarModalidade" id="hidelinkNFCe"/>
                <rich:componentControl for="modalEditarTipoProdutoNFCe"
                                       attachTo="hidelinkNFCe" operation="hide"
                                       event="onclick" />
            </h:panelGroup>
        </f:facet>
        <a4j:form id="formTipoProdutoNFCe" styleClass="font-size-Em-max">
            <input type="hidden" value="${modulo}" name="modulo"/>
            <h:panelGrid columns="1" width="100%"
                         columnClasses="colunaCentralizada">
                <h:panelGrid id="panelConfirmacaoNFCe" columns="1" width="100%" cellpadding="10"
                             columnClasses="colunaEsquerda" styleClass="font-size-Em-max">
                    <h:panelGroup>
                        <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font" style="padding-right: 10px" value="Tipo de Produto:" />
                        <h:selectOneMenu  id="comboTipoProdutoNFCe" onblur="blurinput(this);"  onfocus="focusinput(this);" styleClass="form" value="#{EmpresaControle.codigoTipoProdutoNFCe}" >
                            <f:selectItems  value="#{EmpresaControle.listaTipoProduto}" />
                        </h:selectOneMenu>
                    </h:panelGroup>
                </h:panelGrid>
                <a4j:commandButton id="addTipoProdutoNFCe" action="#{EmpresaControle.adicionarTipoProdutoNFCe}"
                                   reRender="tableTipoProdutoNFCe, panelMensagemNFCe, comboTipoProdutoNFCe"  focus="formTipoProdutoNFCe:comboTipoProdutoNFCe"
                                   value="#{msg_bt.btn_adicionar}" image= "./imagens/botaoAdicionar.png" accesskey="8" styleClass="botoes"/>
                <rich:spacer height="10px"/>
            </h:panelGrid>
            <div style="overflow:auto; border:1px solid #ccc; height: 150px;">
                <h:dataTable id="tableTipoProdutoNFCe" width="100%" headerClass="subordinado" styleClass="tabFormSubordinada"
                             rowClasses="linhaImpar, linhaPar" columnClasses="colunaCentralizada, colunaEsquerda, colunaCentralizada"
                             value="#{EmpresaControle.listaTipoProdutoNFCe}" var="tpProduto">
                    <h:column>
                        <f:facet name="header">
                            <h:outputText  value="Código" />
                        </f:facet>
                        <h:outputText  value="#{tpProduto.value}" />
                    </h:column>
                    <h:column>
                        <f:facet name="header">
                            <h:outputText  value="Descrição" />
                        </f:facet>
                        <h:outputText  value="#{tpProduto.label}" />
                    </h:column>
                    <h:column>
                        <f:facet name="header">
                            <h:outputText  value="#{msg_bt.btn_opcoes}" />
                        </f:facet>
                        <h:panelGroup>
                            <h:outputText value="    "/>
                            <a4j:commandButton id="removerTipoProdutoNFCe"
                                               reRender="tableTipoProdutoNFCe"
                                               action="#{EmpresaControle.removerTipoProdutoNFCe}"
                                               value="#{msg_bt.btn_excluir}" image="./imagens/botaoRemover.png"
                                               accesskey="7" styleClass="botoes"/>
                        </h:panelGroup>
                    </h:column>

                </h:dataTable>
            </div>
            <rich:spacer height="10px"></rich:spacer>
            <h:panelGrid id="panelMensagemNFCe" columns="2" width="100%">
                <h:panelGrid columns="1" width="100%">
                    <h:outputText styleClass="mensagemDetalhada"
                                  value="#{EmpresaControle.mensagemDetalhada}" />
                </h:panelGrid>
            </h:panelGrid>
            <rich:spacer height="10px"></rich:spacer>
            <h:panelGroup>
                <a4j:commandButton value="Gravar"
                                   styleClass="botoes nvoBt"
                                   action="#{EmpresaControle.gravarTiposProdutoNFCe}"
                                   oncomplete="#{EmpresaControle.msgAlert}"
                                   reRender="form" />

                <a4j:commandButton value="Restaurar Padrão"
                                   action="#{EmpresaControle.restaurarTiposProdutoNFCePadrao}"
                                   reRender="tableTipoProdutoNFCe"
                                   styleClass="botoes nvoBt btSec" />
            </h:panelGroup>


        </a4j:form>
    </rich:modalPanel>

    <rich:jQuery id="mskData" selector=".rich-calendar-input" timing="onload" query="mask('99/99/9999')" />
    <%@include file="includes/include_modal_mensagem_generica.jsp"%>
    <jsp:include page="includes/empresa/include_configurar_tiposProduto.jsp" flush="true"/>
    <jsp:include page="includes/modal_token_operacao.jsp" flush="true"/>
    <jsp:include page="includes/include_modal_token_operacao_explicacao.jsp" flush="true"/>
</f:view>
<script>

    carregarTooltipster();
    function copiar(copyText) {
        var el = document.createElement('textarea');
        el.value = copyText;
        document.body.appendChild(el);
        el.select();
        document.execCommand('copy');
        document.body.removeChild(el);
        Notifier.info('Link copiado para a área de transferência.');
    }

    document.getElementById("form:nome").focus();
    const codigoEmpresaEditando = ${EmpresaControle.empresaVO.codigo};
    setInterval(function(){
        console.log('verificando se já existe popup aberto...' + new Date());
        updateVerificaCodigoEdicao();
    }, 2000);
</script>

