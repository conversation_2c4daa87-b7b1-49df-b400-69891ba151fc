<%@page contentType="text/html"%>
<%@page pageEncoding="ISO-8859-1"%>
<head>
    <%@include file="/includes/include_import_minifiles.jsp" %>
</head>
<link href="./css/otimize.css" rel="stylesheet" type="text/css">
<link href="./beta/css/font-awesome_2.0.min.css" rel="stylesheet" type="text/css">
<script type="text/javascript" language="javascript" src="hoverform.js"></script>

<%@taglib prefix="f" uri="http://java.sun.com/jsf/core"%>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html"%>
<%@taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax"%>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich"%>
<%@ taglib prefix="jps" uri="http://richfaces.org/a4j" %>

<f:loadBundle var="msg_bt" basename="propriedades.Botoes" />
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao" />
<f:loadBundle var="msg" basename="propriedades.Mensagens" />

<style type="text/css">
    body {
        margin: 0px;
        padding: 0px;
    }
    .gridManyCheckBox td {
        display: block;
        float: left;
        white-space: nowrap;
        width: 33.33%;
        box-sizing: border-box;
        font: 10pt 'Trebuchet MS', verdana, arial, helvetica, sans-serif;
        color: #000000;
    }
    .gridManyCheckBox td:nth-child(3n+4) {
        clear: left;
    }

    .filtro-resultado td{
        vertical-align: top;
        text-align: left;
        margin: ;
    }

</style>

<f:view>
    <jsp:include page="includes/include_carregando_ripple.jsp" flush="true"/>
    <title>
        <h:outputText value="#{msg_aplic.prt_HistoricoContato_tituloForm}" />
    </title>
    <a4j:loadScript src="/script/jquery.maskedinput-1.2.2.js" />
    <c:set var="titulo" scope="session" value="${msg_aplic.prt_HistoricoContato_tituloForm}"/>
    <c:set var="urlWiki" scope="session" value="${SuperControle.urlBaseConhecimento}relatorio-historico-de-contatos-crm/"/>
    <c:set var="titleWiki" scope="request" value="Clique e saiba mais: Consulta Histórico Contato"/>
    <h:panelGrid columns="1" width="100%" cellpadding="0" cellspacing="0">
        <f:facet name="header">
            <jsp:include page="topoReduzido_material_crm.jsp" />
        </f:facet>

        <h:form id="form">
            <a4j:keepAlive beanName="ExportadorListaControle"/>
            <h:commandLink action="#{HistoricoContatoControle.liberarBackingBeanMemoria}" id="idLiberarBackingBeanMemoria" style="display: none" />
            <h:panelGrid id="panelGridForm" columns="1"  width="100%">

                <h:panelGrid id="panelFiltros" columns="2"  width="100%">
                    <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_HistoricoContato_colaborador}" />
                    <h:panelGroup id="panelColaborador" >
                        <h:inputText styleClass="form" id="nomeColaborador" size="70" value="#{HistoricoContatoControle.colaboradorConsultar.nome}" />
                        <rich:suggestionbox   height="200" width="200"
                                              for="nomeColaborador"
                                              status="statusInComponent"
                                              minChars="3"
                                              immediate="true"
                                              suggestionAction="#{HistoricoContatoControle.autocompleteUsuarioColaborador}"
                                              nothingLabel="Nenhum Colaborador encontrado !" var="result" id="suggestionResponsavel">
                            <a4j:support event="onselect"
                                         action="#{HistoricoContatoControle.setarColaboradorEscolhido}">
                            </a4j:support>
                            <h:column>
                                <h:outputText value="#{result.nome}" />
                            </h:column>
                        </rich:suggestionbox>

                        <rich:spacer width="15px" />
                        <a4j:commandButton action="#{HistoricoContatoControle.limparCampoColaboradorSuggestion}" alt="Limpar Nome do Colaborador" reRender="panelColaborador" image="./images/limpar.gif"/>
                        <rich:spacer width="30px" />

                        <h:panelGroup>
                            <h:outputText styleClass="tituloCamposDestaqueNegrito" value="#{msg_aplic.prt_AberturaMeta_totalizadorClientePotencial}" />
                            <rich:spacer width="12px" />
                            <h:outputText styleClass="tituloCamposDestaqueNegrito" value="#{HistoricoContatoControle.confPaginacao.numeroTotalItens}" />
                        </h:panelGroup>
                    </h:panelGroup>

                    <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_HistoricoContato_fase}" />
                    <h:panelGrid width="100%" style="border:1px solid black">
                        <h:panelGroup id="teste" layout="block" >
                            <h:selectManyCheckbox  styleClass="gridManyCheckBox" style="width: 100%;"   id="fases" value="#{HistoricoContatoControle.faseSelecionadas}">
                                <f:selectItems value="#{HistoricoContatoControle.listaFasesSelecionar}"/>
                           </h:selectManyCheckbox>
                        </h:panelGroup>
                    </h:panelGrid>
                    
                    <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_HistoricoContato_OutrosOpcoes}" />
                    <h:panelGrid width="100%" columns="6" style="border:1px solid black">
                        <h:panelGroup>
                            <h:selectBooleanCheckbox id="agendamento" value="#{HistoricoContatoControle.gerouAgendamento}" />
                            <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_HistoricoContato_gerouAgendamento}"  title="Atendimentos que geraram agendamentos dentro do período"/>
                        </h:panelGroup>
                    </h:panelGrid>

                    <h:outputText styleClass="tituloCampos" value="Tipo Contato" />

                    <h:panelGrid width="100%" columns="6" style="border:1px solid black">
                        <h:panelGroup>
                            <h:selectBooleanCheckbox id="contatoPessoal" value="#{HistoricoContatoControle.filtroOperacaoPessoal}" />
                            <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_HistoricoContato_contatoPessoa}" />
                        </h:panelGroup>

                        <h:panelGroup>
                            <h:selectBooleanCheckbox id="contatoWhatsApp" value="#{HistoricoContatoControle.filtroOperacaoWhatsApp}" />
                            <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_HistoricoContato_contatoWhatsApp}" />
                        </h:panelGroup>

                        <h:panelGroup>
                            <h:selectBooleanCheckbox id="contatoTelefonico" value="#{HistoricoContatoControle.filtroOperacaoTelefone}" />
                            <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_HistoricoContato_contatoTelefonico}" />
                        </h:panelGroup>

                        <h:panelGroup>
                            <h:selectBooleanCheckbox id="contatoEmail" value="#{HistoricoContatoControle.filtroOperacaoEmail}" />
                            <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_HistoricoContato_contatoEmail}" />
                        </h:panelGroup>

                        <h:panelGroup>
                            <h:selectBooleanCheckbox id="contatoSMS" value="#{HistoricoContatoControle.filtroOperacaoSMS}" />
                            <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_HistoricoContato_contatoSMS}" />
                        </h:panelGroup>

                        <h:panelGroup>
                            <h:selectBooleanCheckbox id="ligacaoSemContato" value="#{HistoricoContatoControle.filtroOperacaoLigacaoSemContato}" />
                            <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_HistoricoContato_ligacaoSemContato}" />
                        </h:panelGroup>
                    </h:panelGrid>

                    <h:outputText styleClass="tituloCampos" value="Resultado" />
                    <h:panelGrid width="100%" columns="6" style="border:1px solid black">
                        <h:panelGroup>
                            <h:selectBooleanCheckbox id="resultadoSimplesRegistro" value="#{HistoricoContatoControle.filtroResultadoSimplesRegistro}" />
                            <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_HistoricoContato_resultadoSimplesContato}" />
                        </h:panelGroup>

                        <h:panelGroup>
                            <h:selectBooleanCheckbox id="resultadoObjecao" onchange="submit()" value="#{HistoricoContatoControle.filtroResultadoObjecao}"  immediate="true" />
                            <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_HistoricoContato_resultadoObjecao}" />
                        </h:panelGroup>

                        <h:panelGroup>
                            <h:selectBooleanCheckbox id="resultadoAgAula" value="#{HistoricoContatoControle.filtroResultadoAgAula}"/>
                            <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_HistoricoContato_resultadoAgAula}" />
                        </h:panelGroup>

                        <h:panelGroup>
                            <h:selectBooleanCheckbox id="resultadoAgLigacao" value="#{HistoricoContatoControle.filtroResultadoAgLigacao}"/>
                            <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_HistoricoContato_resultadoAgLigacao}" />
                        </h:panelGroup>

                        <h:panelGroup>
                            <h:selectBooleanCheckbox id="resultadoAgVisita" value="#{HistoricoContatoControle.filtroResultadoAgVisita}"/>
                            <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_HistoricoContato_resultadoAgVisita}" />
                        </h:panelGroup>

                        <h:panelGroup>
                            <h:selectBooleanCheckbox id="resultadoOutros" value="#{HistoricoContatoControle.filtroResultadoOutros}"/>
                            <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_HistoricoContato_resultadoOutros}" />
                        </h:panelGroup>

                    </h:panelGrid>

                    <h:outputText id="motivoObjecoesTitle" rendered="#{HistoricoContatoControle.filtroResultadoObjecao}" styleClass="tituloCampos" value="Motivo da objeção" />
                    <h:panelGrid  rendered="#{HistoricoContatoControle.filtroResultadoObjecao}" width="100%" columns="3" style="border:1px solid black;" styleClass="filtro-resultado">
                        <h:outputText styleClass="tituloCampos" style="margin-left: 15px" value="Não Definitiva" />
                        <h:outputText styleClass="tituloCampos" style="margin-left: 15px" value="Definitiva" />
                        <h:outputText styleClass="tituloCampos" style="margin-left: 15px" value="Desistencia" />

                        <h:panelGrid width="100%" style="vertica: top"  >
                            <h:selectManyCheckbox layout="pageDirection"  styleClass="gridManyCheckBox" style="width: 100%;"   id="objecaoDefinitiva" value="#{HistoricoContatoControle.objecoesSelecionadas}">
                                <f:selectItems value="#{HistoricoContatoControle.listaObjecoesSelecionarNaoDefinitiva}"/>
                            </h:selectManyCheckbox>
                        </h:panelGrid>


                        <h:panelGrid width="100%" style="vertical-align: top"  >
                            <h:selectManyCheckbox  layout="pageDirection"  styleClass="gridManyCheckBox" style="width: 100%;"   id="objecao" value="#{HistoricoContatoControle.objecoesSelecionadasDefinitiva}">
                                <f:selectItems value="#{HistoricoContatoControle.listaObjecoesSelecionarDefinitiva}"/>
                            </h:selectManyCheckbox>
                        </h:panelGrid>


                        <h:panelGrid width="100%" style="vertical-align: top"  >
                            <h:selectManyCheckbox layout="pageDirection"  styleClass="gridManyCheckBox" style="width: 100%;"   id="objecaoDesistencia" value="#{HistoricoContatoControle.objecoesSelecionadasDesistencia}">
                                <f:selectItems value="#{HistoricoContatoControle.listaObjecoesSelecionarDesistencia}"/>
                            </h:selectManyCheckbox>
                        </h:panelGrid>


                    </h:panelGrid>

                    <h:outputText value="Empresa"
                                  rendered="#{HistoricoContatoControle.permiteConsultarTodasEmpresas}"
                                  styleClass="tituloCampos"
                                  style="position:relative; top:7px;"  />
                    <h:panelGroup rendered="#{HistoricoContatoControle.permiteConsultarTodasEmpresas}" >
                        <h:selectOneMenu value="#{HistoricoContatoControle.filtroEmpresa}">
                            <f:selectItems value="#{HistoricoContatoControle.filtroEmpresas}" />
                        </h:selectOneMenu>
                    </h:panelGroup>

                    <h:outputText styleClass="tituloCampos" style="position:relative;" value="#{msg_aplic.prt_Agendados_dataAgendamento}" />

                    <h:panelGroup>
                        <rich:calendar id="dataInicioAgendamento" value="#{HistoricoContatoControle.dataInicioAgendamento}"
                                       inputSize="10" inputClass="form" oninputblur="blurinput(this);"
                                       oninputfocus="focusinput(this);" oninputchange="return validar_Data(this.id);"
                                       datePattern="dd/MM/yyyy" enableManualInput="true" zindex="2"
                                       showWeeksBar="false"/>
                        <h:message for="dataInicioAgendamento" styleClass="mensagemDetalhada"/>
                        <rich:spacer width="12px"/>
                        <h:outputText styleClass="tituloCampos" style="position:relative; " value="#{msg_aplic.prt_AberturaMeta_ate}"/>

                        <rich:spacer width="12px"/>
                        <rich:calendar id="dataTerminoAgendamento" value="#{HistoricoContatoControle.dataTerminoAgendamento}"
                                       inputSize="10" inputClass="form" oninputblur="blurinput(this);"
                                       oninputfocus="focusinput(this);" oninputchange="return validar_Data(this.id);"
                                       datePattern="dd/MM/yyyy" enableManualInput="true" zindex="2"
                                       showWeeksBar="true"/>
                        <h:message for="dataTerminoAgendamento" styleClass="mensagemDetalhada"/>

                        <rich:spacer width="5px" />
                        <a4j:commandButton id="limparDataAgendamento"
                                           onclick="document.getElementById('form:dataInicioAgendamentoInputDate').value = '';
                                                       document.getElementById('form:dataTerminoAgendamentoInputDate').value = '';"
                                           image="/images/limpar.gif" title="Limpar data agendamento."
                                           status="false"
                                           action="#{HistoricoContatoControle.limparDataAgendamento}"/>
                        <rich:spacer width="12px" />
                    </h:panelGroup>

                    <h:outputText styleClass="tituloCampos" style="position:relative; " value="#{msg_aplic.prt_AberturaMeta_periodo}" />

                    <h:panelGroup>
                        <rich:calendar id="dataInicio" value="#{HistoricoContatoControle.dataInicio}"
                                       inputSize="10" inputClass="form" oninputblur="blurinput(this);"
                                       oninputfocus="focusinput(this);" oninputchange="return validar_Data(this.id);"
                                       datePattern="dd/MM/yyyy" enableManualInput="true" zindex="2" showWeeksBar="false" />
                        <h:message for="dataInicio" styleClass="mensagemDetalhada" />
                        <rich:spacer width="12px" />

                        <h:outputText styleClass="tituloCampos" style="position:relative; top:4px;" value="#{msg_aplic.prt_AberturaMeta_ate}" />

                        <rich:spacer width="12px" />
                        <rich:calendar id="dataTermino" value="#{HistoricoContatoControle.dataTermino}" inputSize="10" inputClass="form" oninputblur="blurinput(this);" oninputfocus="focusinput(this);" oninputchange="return validar_Data(this.id);" datePattern="dd/MM/yyyy" enableManualInput="true" zindex="2" showWeeksBar="true" />

                        <h:message for="dataTermino" styleClass="mensagemDetalhada" />

                        <rich:spacer width="5px" />
                        <a4j:commandButton id="limparData"
                                           onclick="document.getElementById('form:dataInicioInputDate').value = '';
                                                       document.getElementById('form:dataTerminoInputDate').value = '';"
                                           image="/images/limpar.gif" title="Limpar data."
                                           status="false"
                                           action="#{HistoricoContatoControle.limparData}"/>
                        <rich:spacer width="12px"/>
                    </h:panelGroup>
                    <h:panelGroup>
                        <h:outputText styleClass="tituloCampos" style="position:relative;" value="Ordenação" />
                    </h:panelGroup>
                    <h:panelGroup>

                        <h:selectOneRadio styleClass="tituloCampos" onblur="blurinput(this);" onfocus="focusinput(this);"
                                          value="#{HistoricoContatoControle.colunaOrdenar}">
                            <f:selectItems value="#{HistoricoContatoControle.listColunasOrdenar}"/>
                        </h:selectOneRadio>

                        <rich:spacer width="5" />

                        <h:selectOneMenu onblur="blurinput(this);"  onfocus="focusinput(this);" styleClass="form" value="#{HistoricoContatoControle.direcaoOrdenar}">
                            <f:selectItems value="#{HistoricoContatoControle.listDirecaoOrdenar}"/>
                        </h:selectOneMenu>

                    </h:panelGroup>
                    <h:panelGroup/>
                    <h:panelGroup layout="block">

                        <rich:spacer height="5px" />
                        <a4j:commandLink styleClass="botoes nvoBt" reRender="form"
                                         actionListener="#{HistoricoContatoControle.consultarPaginadoListenerHistoricoContato}"
                                >
                            Pesquisar&nbsp<i class="fa-icon-search"></i>
                            <f:attribute name="tipoConsulta" value="detalhada"/>
                            <f:attribute name="paginaInicial" value="paginaInicial"/>
                        </a4j:commandLink>

                        <a4j:commandButton id="exportarExcel"
                                           rendered="#{!HistoricoContatoControle.permiteConsultarTodasEmpresas}"
                                           image="imagens/btn_excel.png"
                                           style="margin-left: 8px; position: relative; top: 8px;"
                                           actionListener="#{HistoricoContatoControle.exportar}"
                                           oncomplete="abrirPopup('UpdateServlet?op=downloadfile&file=#{ExportadorListaControle.fileName}&mimetype=application/vnd.ms-excel','Histórico de Contato', 800,200);#{ExportadorListaControle.msgAlert}"
                                           accesskey="3" >

                            <f:attribute name="tipo" value="xls"/>
                            <f:attribute name="atributos"
                                         value="matricula_Apresentar=Matrícula,nome_Apresentar=Nome,faseAtual=Fase Atual,fase_Apresentar=Fase Anterior,faseInicio=Fase Inicio,resultado=Resultado,tipoContato_Apresentar=Tipo Contato,dia_Apresentar=Data Contato,responsavelApresentar=Responsável Cadastro,observacao_Exportar=Observação"/>
                            <f:attribute name="prefixo" value="HistoricoContato"/>
                            <f:attribute name="titulo" value="Histórico de Contatos"/>
                        </a4j:commandButton>

                        <a4j:commandButton id="exportarExcel2"
                                           rendered="#{HistoricoContatoControle.permiteConsultarTodasEmpresas}"
                                           image="imagens/btn_excel.png"
                                           style="margin-left: 8px; position: relative; top: 8px;"
                                           actionListener="#{HistoricoContatoControle.exportar}"
                                           oncomplete="abrirPopup('UpdateServlet?op=downloadfile&file=#{ExportadorListaControle.fileName}&mimetype=application/vnd.ms-excel','Histórico de Contato', 800,200);#{ExportadorListaControle.msgAlert}"
                                           accesskey="3" >

                            <f:attribute name="tipo" value="xls"/>
                            <f:attribute name="atributos"
                                         value="matricula_Apresentar=Matrícula,clienteVO.empresa.nome=Empresa,nome_Apresentar=Nome,faseAtual=Fase Atual,fase_Apresentar=Fase Anterior,faseInicio=Fase Inicio,resultado=Resultado,tipoContato_Apresentar=Tipo Contato,dia_Apresentar=Data Contato,responsavelApresentar=Responsável Cadastro,observacao_Exportar=Observação"/>
                            <f:attribute name="prefixo" value="HistoricoContato"/>
                            <f:attribute name="titulo" value="Histórico de Contatos"/>
                        </a4j:commandButton>

                        <a4j:commandButton id="exportarPdf"
                                           rendered="#{!HistoricoContatoControle.permiteConsultarTodasEmpresas}"
                                           image="/imagens/imprimir.png"
                                           style="margin-left: 8px; position: relative; top: 8px;;"
                                           actionListener="#{HistoricoContatoControle.exportar}"
                                           oncomplete="abrirPopup('UpdateServlet?op=downloadfile&file=#{ExportadorListaControle.fileName}&mimetype=application/pdf','Histórico de Contato', 800,200);#{ExportadorListaControle.msgAlert}"
                                           accesskey="4">

                            <f:attribute name="tipo" value="pdf"/>
                            <f:attribute name="atributos"
                                         value="matricula_Apresentar=Matrícula,nome_Apresentar=Nome,faseAtual=Fase Atual,fase_Apresentar=Fase Anterior,faseInicio=Fase Inicio,resultado=Resultado,tipoContato_Apresentar=Tipo Contato,dia_Apresentar=Data Contato,responsavelApresentar=Responsável Cadastro,observacao_Exportar=Observação"/>
                            <f:attribute name="prefixo" value="HistoricoContato"/>
                            <f:attribute name="titulo" value="Histórico de Contatos"/>
                        </a4j:commandButton>

                        <a4j:commandButton id="exportarPdf2"
                                           rendered="#{HistoricoContatoControle.permiteConsultarTodasEmpresas}"
                                           image="/imagens/imprimir.png"
                                           style="margin-left: 8px; position: relative; top: 8px;;"
                                           actionListener="#{HistoricoContatoControle.exportar}"
                                           oncomplete="abrirPopup('UpdateServlet?op=downloadfile&file=#{ExportadorListaControle.fileName}&mimetype=application/pdf','Histórico de Contato', 800,200);#{ExportadorListaControle.msgAlert}"
                                           accesskey="4">

                            <f:attribute name="tipo" value="pdf"/>
                            <f:attribute name="atributos"
                                         value="matricula_Apresentar=Matrícula,clienteVO.empresa.nome=Empresa,nome_Apresentar=Nome,faseAtual=Fase Atual,fase_Apresentar=Fase Anterior,faseInicio=Fase Inicio,resultado=Resultado,tipoContato_Apresentar=Tipo Contato,dia_Apresentar=Data Contato,responsavelApresentar=Responsável Cadastro,observacao_Exportar=Observação"/>
                            <f:attribute name="prefixo" value="HistoricoContato"/>
                            <f:attribute name="titulo" value="Histórico de Contatos"/>
                        </a4j:commandButton>
                    </h:panelGroup>
                </h:panelGrid>

                <h:panelGrid columns="3" width="100%" styleClass="tabMensagens">
                    <h:panelGrid columns="1" width="100%">
                        <f:verbatim>
                            <h:outputText value=" " />
                        </f:verbatim>
                    </h:panelGrid>

                    <h:commandButton rendered="#{HistoricoContatoControle.sucesso}" image="./imagens/sucesso.png" />
                    <h:commandButton rendered="#{HistoricoContatoControle.erro}" image="./imagens/erro.png" />

                    <h:panelGrid columns="1" width="100%">
                        <h:outputText styleClass="mensagem" value="#{HistoricoContatoControle.mensagem}" />
                        <h:outputText styleClass="mensagemDetalhada" value="#{HistoricoContatoControle.mensagemDetalhada}" />
                    </h:panelGrid>
                </h:panelGrid>
            </h:panelGrid>

            <h:panelGrid width="100%" columns="1" columnClasses="colunaEsquerda">
                <rich:dataTable  id="items" width="100%"
                                 headerClass="consulta" rowClasses="linhaImparPequeno, linhaParPequeno"
                                 columnClasses="colunaEsquerda" value="#{HistoricoContatoControle.listaHistoricoContato}"
                                 var="historicoContato" rowKeyVar="codigoLinha" stateVar="codigoLinhaStatus">
                    <rich:column>
                        <f:facet name="header">
                            <h:outputText style="float:left; vertical-align:top; text-align: left;"
                                          value="#{msg_aplic.prt_HistoricoContato_matricula}" />
                        </f:facet>
                        <a4j:commandLink action="#{HistoricoContatoControle.abrirPopUp}" oncomplete="#{HistoricoContatoControle.abrirPopUpAdequada}"
                                         value="#{historicoContato.clienteVO.matricula}" />
                    </rich:column>

                    <rich:column rendered="#{HistoricoContatoControle.permiteConsultarTodasEmpresas and HistoricoContatoControle.filtroEmpresa eq 0}">
                        <f:facet name="header">
                            <h:outputText style="float:left; vertical-align:top; text-align: left;" value="Empresa" />
                        </f:facet>
                        <h:outputText value="#{historicoContato.clienteVO.empresa.nome}" />
                    </rich:column>

                    <rich:column>
                        <f:facet name="header">
                            <h:outputText style="float:left; vertical-align:top; text-align: left;" value="#{msg_aplic.prt_HistoricoContato_nome}" />
                        </f:facet>
                        <a4j:commandLink rendered="#{historicoContato.historicoPassivo}" action="#{HistoricoContatoControle.abrirPopUp}"
                                         oncomplete="#{HistoricoContatoControle.abrirPopUpAdequada}" value="#{historicoContato.passivoVO.nome}" />
                        <a4j:commandLink rendered="#{historicoContato.historicoCliente}" action="#{HistoricoContatoControle.abrirPopUp}"
                                         oncomplete="#{HistoricoContatoControle.abrirPopUpAdequada}" value="#{historicoContato.clienteVO.pessoa.nome}" />
                        <a4j:commandLink rendered="#{historicoContato.historicoIndicado}" action="#{HistoricoContatoControle.abrirPopUp}"
                                         oncomplete="#{HistoricoContatoControle.abrirPopUpAdequada}" value="#{historicoContato.indicadoVO.nomeIndicado}" />
                    </rich:column>

                    <rich:column>
                        <f:facet name="header">
                            <h:outputText style="float:left; vertical-align:top; text-align: left;" value="#{msg_aplic.prt_HistoricoContato_faseAtual}" />
                        </f:facet>
                        <a4j:commandLink action="#{HistoricoContatoControle.abrirPopUp}"
                                         oncomplete="#{HistoricoContatoControle.abrirPopUpAdequada}" value="#{historicoContato.faseAtual}" />
                          <a4j:commandLink rendered="#{historicoContato.contatoAvulso}" action="#{HistoricoContatoControle.abrirPopUp}"
                                         oncomplete="#{HistoricoContatoControle.abrirPopUpAdequada}" value=" - avulso" />
                    </rich:column>

                    <rich:column>
                        <f:facet name="header">
                            <h:outputText style="float:left; vertical-align:top; text-align: left;"
                                          value="#{msg_aplic.prt_HistoricoContato_faseAnterior}" />
                        </f:facet>
                        <a4j:commandLink action="#{HistoricoContatoControle.abrirPopUp}"
                                         oncomplete="#{HistoricoContatoControle.abrirPopUpAdequada}"
                                         value="#{historicoContato.fase_Apresentar}">
                        </a4j:commandLink>
                    </rich:column>

                    <rich:column>
                        <f:facet name="header">
                            <h:outputText style="float:left; vertical-align:top; text-align: left;"
                                          value="#{msg_aplic.prt_HistoricoContato_faseInicio}" />
                        </f:facet>
                        <a4j:commandLink action="#{HistoricoContatoControle.abrirPopUp}" oncomplete="#{HistoricoContatoControle.abrirPopUpAdequada}"
                                         value="#{historicoContato.faseInicio}" />
                    </rich:column>

                    <rich:column>
                        <f:facet name="header">
                            <h:outputText style="float:left; vertical-align:top; text-align: left;" value="#{msg_aplic.prt_HistoricoContato_resultado}" />
                        </f:facet>
                        <a4j:commandLink action="#{HistoricoContatoControle.abrirPopUp}" oncomplete="#{HistoricoContatoControle.abrirPopUpAdequada}"
                                         value="#{historicoContato.resultado}" />
                    </rich:column>

                    <rich:column>
                        <f:facet name="header">
                            <h:outputText style="float:left; vertical-align:top; text-align: left;" value="#{msg_aplic.prt_HistoricoContato_contatos}" />
                        </f:facet>
                        <a4j:commandLink action="#{HistoricoContatoControle.abrirPopUp}" oncomplete="#{HistoricoContatoControle.abrirPopUpAdequada}"
                                         value="#{historicoContato.tipoContato_Apresentar}" />
                    </rich:column>

                    <rich:column>
                        <f:facet name="header">
                            <h:outputText style="float:left; vertical-align:top; text-align: left;" value="#{msg_aplic.prt_HistoricoContato_dataContato}" />
                        </f:facet>
                        <a4j:commandLink action="#{HistoricoContatoControle.abrirPopUp}" oncomplete="#{HistoricoContatoControle.abrirPopUpAdequada}">
                                <%--<h:outputText value="#{historicoContato.dia}">
                                    <f:convertDateTime pattern="dd/MM/yyyy"/>
                                </h:outputText> --%>
                            <h:outputText value="#{historicoContato.dia_Apresentar}"/>

                         </a4j:commandLink>
                    </rich:column>

                    <rich:column>
                        <f:facet name="header">
                            <h:outputText style="float:left; vertical-align:top; text-align: left;" value="#{msg_aplic.prt_HistoricoContato_responsavelCadastro}" />
                        </f:facet>
                        <a4j:commandLink action="#{HistoricoContatoControle.abrirPopUp}" oncomplete="#{HistoricoContatoControle.abrirPopUpAdequada}"
                                         value="#{historicoContato.responsavelCadastro.nome}" />
                    </rich:column>

                    <rich:column colspan="9" style="vertical-align:top; text-align: left;">
                        <h:outputText value="#{historicoContato.observacao}" escape="false" />
                    </rich:column>
                </rich:dataTable>
            </h:panelGrid>

            <h:panelGrid columns="1" width="100%" columnClasses="colunaCentralizada" id="paginacao">
                <table width="100%" border="0" cellspacing="0" cellpadding="0">
                    <tr>
                        <td align="center" valign="middle">
                            <h:panelGroup id="painelPaginacao" rendered="#{HistoricoContatoControle.confPaginacao.existePaginacao}">
                                <a4j:commandLink id="pagiInicial" styleClass="tituloCampos" value="  <<  " reRender="items, paginaAtual, painelPaginacao"
                                                 rendered="#{HistoricoContatoControle.confPaginacao.apresentarPrimeiro}"
                                                 actionListener="#{HistoricoContatoControle.consultarPaginadoListenerHistoricoContato}">
                                    <f:attribute name="pagNavegacao" value="pagInicial" />
                                </a4j:commandLink>

                                <a4j:commandLink id="pagiAnterior" styleClass="tituloCampos" value="  <  " reRender="items, paginaAtual, painelPaginacao"
                                                 rendered="#{HistoricoContatoControle.confPaginacao.apresentarAnterior}"
                                                 actionListener="#{HistoricoContatoControle.consultarPaginadoListenerHistoricoContato}">
                                    <f:attribute name="pagNavegacao" value="pagAnterior" />
                                </a4j:commandLink>

                                <h:outputText id="paginaAtual" styleClass="tituloCampos"
                                              value="#{msg_aplic.prt_msg_pagina} #{HistoricoContatoControle.confPaginacao.paginaAtualDeTodas}" rendered="true"/>
                                <a4j:commandLink id="pagiPosterior" styleClass="tituloCampos" value="  >  " reRender="items, paginaAtual, painelPaginacao"
                                                 rendered="#{HistoricoContatoControle.confPaginacao.apresentarPosterior}"
                                                 actionListener="#{HistoricoContatoControle.consultarPaginadoListenerHistoricoContato}">
                                    <f:attribute name="pagNavegacao" value="pagPosterior" />
                                </a4j:commandLink>

                                <a4j:commandLink id="pagiFinal" styleClass="tituloCampos" value="  >>  " reRender="items, paginaAtual, painelPaginacao"
                                                 rendered="#{HistoricoContatoControle.confPaginacao.apresentarUltimo}"
                                                 actionListener="#{HistoricoContatoControle.consultarPaginadoListenerHistoricoContato}">
                                    <f:attribute name="pagNavegacao" value="pagFinal" />
                                </a4j:commandLink>

                                <h:outputText id="totalItens" styleClass="tituloCampos"
                                              value=" [#{msg_aplic.prt_msg_itens} #{HistoricoContatoControle.confPaginacao.numeroTotalItens}]" rendered="true"/>
                            </h:panelGroup>
                        </td>
                    </tr>
                </table>
            </h:panelGrid>
            <rich:jQuery id="mskData" selector=".rich-calendar-input" timing="onload" query="mask('99/99/9999')" />
        </h:form>
    </h:panelGrid>

</f:view>

<script>
    document.getElementById("form:consulta").focus();
</script>
