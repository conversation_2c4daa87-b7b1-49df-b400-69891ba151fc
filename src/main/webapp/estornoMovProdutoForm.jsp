<%@page contentType="text/html" %>
<%@page pageEncoding="ISO-8859-1" %>
<head>
    <%@include file="/includes/include_import_minifiles.jsp" %>
</head>
<link href="./css/otimize.css" rel="stylesheet" type="text/css">
<link href="./beta/css/font-awesome_2.0.min.css" rel="stylesheet" type="text/css">
<link href="beta/css/font-awesome_2.0.min.css" type="text/css" rel="stylesheet"/>
<link href="beta/css/pure-buttons.css" type="text/css" rel="stylesheet"/>
<link href="beta/css/pure-ext.css" type="text/css" rel="stylesheet"/>
<script type="text/javascript" language="javascript" src="hoverform.js">
    setTimeout(setInterval(function() { getDocumentCookie('popupsImportante') == 'close' ? this.close() : ''; }, 500), 500);
</script>
<%@taglib prefix="f" uri="http://java.sun.com/jsf/core" %>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html" %>
<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax" %>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich" %>
<f:loadBundle var="msg_bt" basename="propriedades.Botoes"/>
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao"/>
<f:loadBundle var="msg" basename="propriedades.Mensagens"/>
<style type="text/css">
    .pure-button{
        font-family: sans-serif;
        font-size:100%;
        margin:0;
        vertical-align:baseline;
        *vertical-align:middle
    }

    td.w48{
        width: 48%;
    }
    body{
        background-color: #fff !important;
    }
</style>
<f:view>
    <%@include file="includes/include_carregando_ripple.jsp" %>
    <title><h:outputText value="#{msg_aplic.prt_EstornoMovProduto_tituloForm}"/></title>
    <rich:modalPanel id="panelMensagemOutroProduto" autosized="true" shadowOpacity="true" width="450" height="250" onshow="document.getElementById('formMensagemOutroProduto:fechar').focus();">
        <f:facet name="header">
            <h:panelGroup>
                <h:outputText value="Atenção"></h:outputText>
            </h:panelGroup>
        </f:facet>
        <a4j:form id="formMensagemOutroProduto">
            <h:panelGrid columns="1" width="100%" columnClasses="colunaCentralizada">
                <h:panelGrid columns="1" style="height:25px; background-image:url('./imagens/fundoBarraTopo.png');background-repeat: repeat-x;"
                             columnClasses="colunaCentralizada" width="100%">
                </h:panelGrid>
                <h:panelGrid columns="1" width="100%" columnClasses="colunaEsquerda" styleClass="tabForm">
                    <h:outputText styleClass="text"
                                  value="Este estorno afetará também outras Movimentações de Produtos que foram pagas pelo mesmo recibo. Segue abaixo o(s) aluno(s) que terá(ão) o recibo estornado:"/>
                    <rich:dataTable id="listaPessoa" width="100%" border="0" cellspacing="0" cellpadding="0" styleClass="textsmall" columnClasses="centralizado, centralizado, centralizado"
                                    value="#{EstornoMovProdutoControle.listaPessoaVOs}" var="pessoa">
                        <f:facet name="header">
                            <h:outputText styleClass="tituloFormulario" value="Lista de  Pessoas"/>
                        </f:facet>
                        <rich:column>
                            <f:facet name="header">
                                <h:outputText style="font-weight: bold" value="Aluno"/>
                            </f:facet>
                            <h:outputText style="font-weight: bold" styleClass="red" value="#{pessoa.nome}"/>
                        </rich:column>
                    </rich:dataTable>
                </h:panelGrid>
                <a4j:commandButton id="fechar" value="#{msg_bt.btn_fechar}" image="./imagens/botaoFechar.png"
                                   alt="#{msg.msg_gravar_dados}"
                                   action="#{EstornoMovProdutoControle.abrirModalConfirmacaoEstorno}"
                                   oncomplete="#{EstornoMovProdutoControle.mensagemNotificar}"
                                   reRender="form,panelMensagem,panelAutorizacaoFuncionalidade"/>
            </h:panelGrid>
        </a4j:form>
    </rich:modalPanel>

    <h:panelGrid columns="1" width="100%" cellpadding="0" cellspacing="0">
        <c:set var="titulo" scope="session" value="Estorno de Produto"/>
        <f:facet name="header">
            <jsp:include page="topoReduzido_material.jsp"/>
        </f:facet>
        <div styleClass="col-md-12"><hr class="dividerFundoClaro"/></div>
    </h:panelGrid>
    <h:form id="form">
        <h:panelGrid columns="1" width="100%" columnClasses="colunaCentralizada">
            <h:panelGrid columns="1" columnClasses="colunaCentralizada" width="100%" styleClass="font-size-em-max">
                <h:panelGrid columns="1" headerClass="subordinado" width="100%" columnClasses="colunaEsquerda" styleClass="font-size-em-max">
                    <h:panelGroup rendered="#{EstornoMovProdutoControle.mostrarNomeMatriculaAluno}">
                        <h:outputText value="NOME DO CLIENTE: " styleClass="texto-size-14 texto-cor-cinza texto-font texto-bold"/>
                        <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font" value="#{EstornoMovProdutoControle.estornoMovProdutoVO.clienteVO.pessoa.nome}"/>
                        <div styleClass="col-md-12"><hr class="dividerFundoClaro"/></div>
                    </h:panelGroup>
                    <h:panelGroup rendered="#{EstornoMovProdutoControle.mostrarNomeMatriculaAluno}">
                        <h:outputText value="MATRÍCULA: " styleClass="texto-size-14 texto-cor-cinza texto-font texto-bold"/>
                        <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font" value="#{EstornoMovProdutoControle.estornoMovProdutoVO.clienteVO.matricula}"/>
                        <div styleClass="col-md-12"><hr class="dividerFundoClaro"/></div>
                    </h:panelGroup>
                </h:panelGrid>
                <h:outputText styleClass="texto-size-14-real texto-font texto-bold" value="PRODUTOS"/>
                <rich:dataTable id="movProdutoEstornado" width="100%" styleClass="tabelaDados semZebra" value="#{EstornoMovProdutoControle.estornoMovProdutoVO.listaMovProduto}"
                                var="movProduto">
                    <rich:column>
                        <f:facet name="header">
                            <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold" value="CONTRATO"/>
                        </f:facet>
                        <h:outputText styleClass="texto-size-14-real texto-font" style="color: red" value="#{movProduto.contrato.codigo}"/>
                    </rich:column>
                    <rich:column>
                        <f:facet name="header">
                            <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold" value="#{msg_aplic.prt_HistoricoComprasCliente_descricao}"/>
                        </f:facet>
                        <h:outputText styleClass="texto-size-14-real texto-font" style="color: red" value="#{movProduto.descricao}"/>
                    </rich:column>
                    <rich:column>
                        <f:facet name="header">
                            <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold" value="LANÇAMENTO"/>
                        </f:facet>
                        <h:outputText styleClass="texto-size-14-real texto-font" style="color: red" value="#{movProduto.dataLancamento_Apresentar}"/>
                    </rich:column>
                    <rich:column>
                        <f:facet name="header">
                            <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold" value="QTD."/>
                        </f:facet>
                        <h:outputText styleClass="texto-size-14-real texto-font" style="color: red" value="#{movProduto.quantidade}"/>
                    </rich:column>
                    <rich:column>
                        <f:facet name="header">
                            <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold" value="#{msg_aplic.prt_HistoricoComprasCliente_unitario}"/>
                        </f:facet>
                        <h:outputText styleClass="texto-size-14-real texto-font" style="color: red" value="#{movProduto.precoUnitario}">
                            <f:converter converterId="FormatadorNumerico"/>
                        </h:outputText>
                    </rich:column>
                    <rich:column>
                        <f:facet name="header">
                            <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold" value="#{msg_aplic.prt_HistoricoComprasCliente_desconto}"/>
                        </f:facet>
                        <h:outputText styleClass="texto-size-14-real texto-font" style="color: red" value="#{movProduto.valorDesconto}">
                            <f:converter converterId="FormatadorNumerico"/>
                        </h:outputText>
                    </rich:column>
                    <rich:column>
                        <f:facet name="header">
                            <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold" value="#{msg_aplic.prt_HistoricoComprasCliente_totalFinal}"/>
                        </f:facet>
                        <h:outputText styleClass="texto-size-14-real texto-font" style="color: red" value="#{movProduto.totalFinal}">
                            <f:converter converterId="FormatadorNumerico"/>
                        </h:outputText>
                    </rich:column>
                    <rich:column>
                        <f:facet name="header">
                            <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold" value="#{msg_aplic.prt_HistoricoComprasCliente_situacao}"/>
                        </f:facet>
                        <h:outputText styleClass="texto-size-14-real texto-font" style="color: red" value="#{movProduto.situacao_Apresentar}"/>
                    </rich:column>
                </rich:dataTable>

                <h:panelGrid columns="1" width="100%" rendered="#{!EstornoMovProdutoControle.apresentarListaPagamento}" styleClass="tabFormSubordinada" columnClasses="colunaCentralizada">
                    <rich:dataTable id="movParcelaDiretaContrato" width="100%" style="border:0;height:100px;background-color: transparent;" cellspacing="0" cellpadding="0" columnClasses="centralizado"
                                    value="#{EstornoMovProdutoControle.estornoMovProdutoVO.listaMovParcela}" var="historicoParcela">
                        <f:facet name="header">
                            <h:outputText styleClass="tituloFormularioPreto" value="Parcelas "/>
                        </f:facet>
                        <rich:column>
                            <f:facet name="header">
                                <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold" value="#{msg_aplic.prt_HistoricoParcelaCliente_codigo}"/>
                            </f:facet>
                            <h:outputText style="font-weight: bold" styleClass="red" value="#{historicoParcela.contrato.codigo}"/>
                        </rich:column>
                        <rich:column>
                            <f:facet name="header">
                                <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold" value="#{msg_aplic.prt_HistoricoParcelaCliente_nomeAluno}"/>
                            </f:facet>
                            <h:panelGroup
                                    rendered="#{EstornoMovProdutoControle.desenharColunaNomeContrato}">
                                <h:outputText style="font-weight: bold" styleClass="red" value="#{historicoParcela.contrato.pessoa.nome}"/>
                            </h:panelGroup>
                            <h:panelGroup
                                    rendered="#{EstornoMovProdutoControle.desenharColunaNomeVendaAvulsa}">
                                <h:outputText style="font-weight: bold" styleClass="red" value="#{historicoParcela.vendaAvulsaVO.nomeComprador}"/>
                            </h:panelGroup>
                            <h:panelGroup
                                    rendered="#{EstornoMovProdutoControle.desenharColunaNomeAulaAvusa}">
                                <h:outputText style="font-weight: bold" styleClass="red" value="#{historicoParcela.aulaAvulsaDiariaVO.nomeComprador}"/>
                            </h:panelGroup>

                            <h:panelGroup
                                    rendered="#{EstornoMovProdutoControle.desenharColunaNomePersonal}">
                                <h:outputText style="font-weight: bold" styleClass="red" value="#{historicoParcela.personal.personal.pessoa.nome}"/>
                            </h:panelGroup>
                        </rich:column>

                        <rich:column>
                            <f:facet name="header">
                                <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold" value="#{msg_aplic.prt_HistoricoParcelaCliente_descricao}"/>
                            </f:facet>
                            <h:outputText style="font-weight: bold" styleClass="red" value="#{historicoParcela.descricao}"/>
                        </rich:column>

                        <rich:column>
                            <f:facet name="header">
                                <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold" value="#{msg_aplic.prt_HistoricoParcelaCliente_DataVencimento}"/>
                            </f:facet>
                            <h:outputText style="font-weight: bold" styleClass="red" value="#{historicoParcela.dataVencimento_Apresentar}">
                            </h:outputText>
                        </rich:column>
                        <rich:column>
                            <f:facet name="header">
                                <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold" value="#{msg_aplic.prt_HistoricoParcelaCliente_valor}"/>
                            </f:facet>
                            <h:outputText style="font-weight: bold" styleClass="red" value="#{historicoParcela.valorParcela}">
                                <f:converter converterId="FormatadorNumerico"/>
                            </h:outputText>
                        </rich:column>
                        <rich:column>
                            <f:facet name="header">
                                <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold" value="#{msg_aplic.prt_HistoricoParcelaCliente_situacao}"/>
                            </f:facet>
                            <h:outputText style="font-weight: bold" styleClass="red" value="#{historicoParcela.situacao_Apresentar}"/>
                        </rich:column>
                    </rich:dataTable>
                </h:panelGrid>
                <h:outputText styleClass="texto-size-14-real texto-font texto-bold" value="PARCELAS"/>
                <h:panelGrid columns="1" width="100%" rendered="#{EstornoMovProdutoControle.apresentarListaPagamento}" styleClass="tabFormSubordinada" >
                    <rich:dataTable id="movParcela" width="100%" style="border:0;height:100px;background-color: transparent;" value="#{EstornoMovProdutoControle.estornoMovProdutoVO.listaEstornoRecibo}" var="estornoRecibo">
                        <rich:column style="border:0;height:100px;background-color: transparent;">
                            <rich:dataTable id="listaParcelas" width="100%" styleClass="tabelaDados semZebra" value="#{estornoRecibo.listaMovParcela}" var="historicoParcela">
                                <f:facet name="header">
                                    <h:outputText styleClass="texto-size-14-real texto-font texto-bold"  value="Parcelas Pagas pelo Recibo #{estornoRecibo.reciboPagamentoVO.codigo}"/>
                                </f:facet>
                                <rich:column>
                                    <f:facet name="header">
                                        <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold" value="#{msg_aplic.prt_HistoricoParcelaCliente_codigo}"/>
                                    </f:facet>
                                    <h:outputText styleClass="texto-size-14-real texto-font" style="color: red" value="#{historicoParcela.contrato.codigo}"/>
                                </rich:column>
                                <rich:column>
                                    <f:facet name="header">
                                        <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold" value="#{msg_aplic.prt_HistoricoParcelaCliente_nomeAluno}"/>
                                    </f:facet>
                                    <h:panelGroup rendered="#{EstornoMovProdutoControle.desenharColunaNomeContrato}">
                                        <h:outputText styleClass="texto-size-14-real texto-font" style="color: red" value="#{historicoParcela.contrato.pessoa.nome}"/>
                                    </h:panelGroup>

                                    <h:panelGroup rendered="#{EstornoMovProdutoControle.desenharColunaNomeVendaAvulsa}">
                                        <h:outputText styleClass="texto-size-14-real texto-font" style="color: red" value="#{historicoParcela.vendaAvulsaVO.nomeComprador}"/>
                                    </h:panelGroup>

                                    <h:panelGroup rendered="#{EstornoMovProdutoControle.desenharColunaNomeAulaAvusa}">
                                        <h:outputText  styleClass="texto-size-14-real texto-font" style="color: red" value="#{historicoParcela.aulaAvulsaDiariaVO.nomeComprador}"/>
                                    </h:panelGroup>
                                    <h:panelGroup rendered="#{EstornoMovProdutoControle.desenharColunaNomePersonal}">
                                        <h:outputText  styleClass="texto-size-14-real texto-font" style="color: red" value="#{historicoParcela.personal.personal.pessoa.nome}"/>
                                    </h:panelGroup>
                                </rich:column>
                                <rich:column>
                                    <f:facet name="header">
                                        <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold" value="#{msg_aplic.prt_HistoricoParcelaCliente_descricao}"/>
                                    </f:facet>
                                    <h:outputText  styleClass="texto-size-14-real texto-font" style="color: red" value="#{historicoParcela.descricao}"/>
                                </rich:column>

                                <rich:column>
                                    <f:facet name="header">
                                        <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold" value="#{msg_aplic.prt_HistoricoParcelaCliente_DataVencimento}"/>
                                    </f:facet>
                                    <h:outputText  styleClass="texto-size-14-real texto-font" style="color: red" value="#{historicoParcela.dataVencimento_Apresentar}"/>
                                </rich:column>
                                <rich:column>
                                    <f:facet name="header">
                                        <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold" value="#{msg_aplic.prt_HistoricoParcelaCliente_valor}"/>
                                    </f:facet>
                                    <h:outputText  styleClass="texto-size-14-real texto-font" style="color: red" value="#{historicoParcela.valorParcela}">
                                        <f:converter converterId="FormatadorNumerico"/>
                                    </h:outputText>
                                </rich:column>
                                <rich:column>
                                    <f:facet name="header">
                                        <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold" value="#{msg_aplic.prt_HistoricoParcelaCliente_situacao}"/>
                                    </f:facet>
                                    <h:outputText  styleClass="texto-size-14-real texto-font" style="color: red" value="#{historicoParcela.situacao_Apresentar}"/>
                                </rich:column>
                            </rich:dataTable>
                        </rich:column>
                    </rich:dataTable>
                </h:panelGrid>

                <h:panelGrid columns="1" width="100%" rendered="#{!empty EstornoMovProdutoControle.estornoMovProdutoVO.listaTransacoes}" styleClass="tabFormSubordinada,tituloCamposNegritoMaior"
                             columnClasses="colunaCentralizada">
                    <%@include file="includes/transacoes/include_table_transacoes.jsp" %>
                </h:panelGrid>

                <h:panelGrid columns="1" width="100%" id="panelSelectEstornarTransacoes"
                             rendered="#{!empty EstornoMovProdutoControle.estornoMovProdutoVO.listaTransacoes}">
                    <h:panelGroup layout="block" style="padding: 10px 0;display: flex;">
                        <h:outputText style="padding-top: 15px; padding-right: 5px;"
                                      styleClass="texto-size-14-real texto-cor-cinza-2 texto-font texto-bold"
                                      value="CANCELAR TRANSAÇÕES"/>
                        <h:panelGroup layout="block" styleClass="cb-container pl20">
                            <h:selectOneMenu id="estornarTransacoes" styleClass="form" onblur="blurinput(this);"
                                             onfocus="focusinput(this);"
                                             value="#{EstornoMovProdutoControle.estornarTransacoes}">
                                <f:selectItems
                                        value="#{EstornoMovProdutoControle.listaSelectItemEstornarTransacoes}"/>
                            </h:selectOneMenu>
                        </h:panelGroup>
                    </h:panelGroup>
                </h:panelGrid>

                <h:panelGrid id="panelMensagem" columns="3" width="100%" columnClasses="colunaEsquerda">
                    <h:panelGrid columns="1" width="100%">
                        <h:outputText value=" " />
                    </h:panelGrid>
                    <h:commandButton rendered="#{EstornoMovProdutoControle.sucesso}" image="./imagens/sucesso.png" />
                    <h:commandButton rendered="#{EstornoMovProdutoControle.erro}" image="./imagens/erro.png" />
                    <h:panelGrid columns="1" width="100%">
                        <h:outputText id="msgEstorno" styleClass="mensagem" value="#{EstornoMovProdutoControle.mensagem}"/>
                        <h:outputText id="msgEstornoDet" styleClass="mensagemDetalhada" value="#{EstornoMovProdutoControle.mensagemDetalhada}"/>
                    </h:panelGrid>
                    <rich:spacer rendered="#{EstornoMovProdutoControle.temChequeOuCartaoEmlote}" width="20px"/>
                    <rich:spacer rendered="#{EstornoMovProdutoControle.temChequeOuCartaoEmlote}" width="20px"/>
                    <h:panelGroup>
                        <h:outputText styleClass="mensagemDetalhada"
                                      rendered="#{EstornoMovProdutoControle.temChequeOuCartaoEmlote}"
                                      value="#{msg.msg_produto_nao_pode_estornar}"/>


                        <h:outputLink styleClass="linkWiki"
                                      value="#{SuperControle.urlBaseConhecimento}como-registrar-vendas-para-quem-nao-e-cliente/"
                                      rendered="#{EstornoMovProdutoControle.temChequeOuCartaoEmlote}"
                                      title="Clique e saiba mais: RecebivelLote"
                                      target="_blank" >
                            <h:outputText styleClass="mensagemDetalhada" value=" Clique Aqui."/>
                        </h:outputLink>

                    </h:panelGroup>
                </h:panelGrid>

                <h:panelGroup>
                    <a4j:commandLink id="fechar" title="Fechar Janela" onclick="fecharJanela();executePostMessage({close: true});"  styleClass="pure-button texto-font">
                        <i class="fa-icon-remove"></i>&nbsp;Fechar
                    </a4j:commandLink>
                    <rich:spacer width="20px"/>
                    <a4j:commandLink id="estornar"
                                     oncomplete="#{EstornoMovProdutoControle.msgAlert};#{EstornoMovProdutoControle.mensagemNotificar}"
                                     action="#{EstornoMovProdutoControle.validarQualModalAbrir}"
                                     rendered="#{EstornoMovProdutoControle.apresentarBotaoEstorno && !EstornoMovProdutoControle.temChequeOuCartaoEmlote}"  styleClass="pure-button texto-font pure-button-primary"
                                     reRender="modalAbrirCaixa, panelAutorizacaoFuncionalidade, panelMensagemOutroProduto">
                        <i class="fa-icon-money"></i>&nbsp;Estorno
                    </a4j:commandLink>
                </h:panelGroup>
            </h:panelGrid>
        </h:panelGrid>
    </h:form>
    <%@include file="/pages/finan/includes/include_modal_abrirCaixa.jsp" %>
    <%@include file="includes/autorizacao/include_autorizacao_funcionalidade.jsp" %>
</f:view>
<script>
    document.getElementById("form:valorConsulta").focus();
</script>
