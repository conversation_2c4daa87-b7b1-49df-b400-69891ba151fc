<%-- 
    Document   : include_modal_sucesso
    Created on : 14/06/2010, 11:05:41
    Author     : carla
--%>


<%@page contentType="text/html"%>
<%@page pageEncoding="ISO-8859-1"%>
<head><%@include file="/includes/include_import_minifiles.jsp" %></head>
<link href="./css/otimize.css" rel="stylesheet" type="text/css">
<script type="text/javascript" language="javascript" src="hoverform.js"></script>
<%@taglib prefix="f" uri="http://java.sun.com/jsf/core"%>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html"%>
<%@taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>

<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax"%>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich"%>

<f:loadBundle var="msg_bt" basename="propriedades.Botoes" />
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao" />
<f:loadBundle var="msg" basename="propriedades.Mensagens" />


<rich:modalPanel id="panelDadosGravadosComSucesso" autosized="true"
                 shadowOpacity="true" width="450" height="250" rendered="#{SuperControle.apresentarModalPanelSucesso}">
    <f:facet name="header">
        <h:panelGroup>
            <h:outputText value="Confirmação"></h:outputText>
        </h:panelGroup>
    </f:facet>
    <f:facet name="controls">
        <h:panelGroup>
            <h:graphicImage value="/imagens/close.png" style="cursor:pointer"
                            id="hidelink12" />
            <rich:componentControl for="panelDadosGravadosComSucesso"
                                   attachTo="hidelink12" operation="hide" event="onclick" />
        </h:panelGroup>
    </f:facet>
    <a4j:form id="formPagamentoEfetuadoComSucesso">
        <h:panelGrid columns="1" width="100%"
                     columnClasses="colunaCentralizada">
            <h:panelGrid columns="1"
                         style="height:25px; background-image:url('./imagens/fundoBarraTopo.png');background-repeat: repeat-x;"
                         columnClasses="colunaCentralizada" width="100%">
                <h:outputText styleClass="tituloFormulario" value="#{SuperControle.mensagem}" />
            </h:panelGrid>
        </h:panelGrid>
    </a4j:form>
</rich:modalPanel>
