package servlet.financeiro;

import br.com.pactosolucoes.comuns.json.SuperJSON;
import negocio.interfaces.bi.FiltroJSON;

public class FiltroFluxoCaixaJSON extends SuperJSON implements FiltroJSON {

    private Integer empresa;
    private Long dataInicio;
    private Long dataFim;
    private boolean incluirParcelasRecorrencia;

    public FiltroFluxoCaixaJSON(Integer empresa, Long dataInicio, Long dataFim, boolean incluirParcelasRecorrencia) {
        this.empresa = empresa;
        this.dataInicio = dataInicio;
        this.dataFim = dataFim;
        this.incluirParcelasRecorrencia = incluirParcelasRecorrencia;
    }

    public String getToken() {
        return "empresa=" + getEmpresa() +
                "|dataInicio=" + getDataInicio() +
                "|dataFim=" + getDataFim()+
                "|incluirParcelasRecorrencia="+isIncluirParcelasRecorrencia();
    }

    public Integer getEmpresa() {
        return empresa;
    }

    public void setEmpresa(Integer empresa) {
        this.empresa = empresa;
    }

    public Long getDataInicio() {
        return dataInicio;
    }

    public void setDataInicio(Long dataInicio) {
        this.dataInicio = dataInicio;
    }

    public Long getDataFim() {
        return dataFim;
    }

    public void setDataFim(Long dataFim) {
        this.dataFim = dataFim;
    }

    public boolean isIncluirParcelasRecorrencia() {
        return incluirParcelasRecorrencia;
    }

    public void setIncluirParcelasRecorrencia(boolean incluirParcelasRecorrencia) {
        this.incluirParcelasRecorrencia = incluirParcelasRecorrencia;
    }
}
