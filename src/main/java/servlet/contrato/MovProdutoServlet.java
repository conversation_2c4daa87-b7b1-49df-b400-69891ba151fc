package servlet.contrato;

import controle.arquitetura.security.LoginControle;
import servlet.arquitetura.SuperServlet;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.PrintWriter;
import java.util.Date;

/**
 * Created with IntelliJ IDEA.
 * User: franciscoanjos
 * Date: 04/09/13
 * Time: 11:09
 * To change this template use File | Settings | File Templates.
 */

public class MovProdutoServlet extends SuperServlet {
    @Override
    protected void processRequest(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        super.processRequest(request, response);
        PrintWriter out = response.getWriter();
        String json;
        try {

            Integer empresa = ((LoginControle)getAttribute(request, "LoginControle")).getEmpresa().getCodigo();
            Date dtInicio = obterParametroDate(request.getParameter("dtInicio"));
            Date dtFim = obterParametroDate(request.getParameter("dtFim"));

            String sEcho = obterParametroString(request.getParameter("sEcho"));
            Integer offset = obterParametro(request.getParameter("iDisplayStart"));
            Integer limit = obterParametro(request.getParameter("iDisplayLength"));
            String clausulaLike = obterParametroString(request.getParameter("sSearch"));
            Integer colOrdenar = obterParametro(request.getParameter("iSortCol_0"));
            String dirOrdenar = obterParametroString(request.getParameter("sSortDir_0"));


            json = ff(request).getMovProduto().consultarJSON(sEcho, offset, limit, clausulaLike, colOrdenar, dirOrdenar,
                    dtInicio, dtFim, empresa);

        } catch (Exception e) {
            //json = (e.getMessage() == null) ? NullPointerException.class.getName() : e.getMessage();

            StringBuilder sb = new StringBuilder();
            sb.append("{");
            sb.append("  \"draw\": 1,");
            sb.append("  \"recordsTotal\": null,");
            sb.append("  \"recordsFiltered\": null,");
            sb.append("  \"error\": [");
            sb.append("    \"code\": -32601,");
            sb.append("    \"message\": \"Sem resposta no momento, por favor tente novamente\"");
            sb.append("  ]");
            sb.append("}");

            json = sb.toString();

        }
        out.println(json);
        json = null;
    }
}