package servlet.boleto;

import br.com.pactosolucoes.oamd.controle.basico.DAO;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.financeiro.enumerador.TipoCobrancaEnum;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.Usuario;
import negocio.facade.jdbc.basico.TokenBoleto;
import negocio.facade.jdbc.telaCliente.TelaClienteBoleto;
import org.json.JSONArray;
import org.json.JSONObject;
import servicos.boleto.TokenBoletoVO;
import servlet.arquitetura.SuperServlet;

import javax.servlet.ServletException;
import javax.servlet.ServletRequest;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.nio.charset.StandardCharsets;
import java.sql.Connection;


/**
 * Created with IntelliJ IDEA.
 * User: <PERSON><PERSON> <PERSON>
 * Date: 28/01/2025
 * <p>
 * #########################################################################################
 * ######################################## ATENÇÃO ########################################
 * <p>
 * ESSA CLASSE FOI CRIADA COM O OBJETIVO DE GERAR O PDF DO BOLETO ONLINE
 * SÓ É ACEITO REQUISAÇÃO GET !
 * <p>
 * ######################################## REGRAS ########################################
 * <p>
 * 1 - Passar a chave da empresa via parametro (key)
 * 2 - Passar o token do token boleto via parametro (tk)
 * 3 - Passar a operação via parametro (op)
 * Exemplo:
 * http://localhost:8084/zw/prest/token-boleto?key=teste&tk=TOKEN_UUID&op=imprimir
 * <p>
 * #########################################################################################
 * #########################################################################################
 */
public class TokenBoletoServlet extends SuperServlet {

    @Override
    protected void processRequest(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        try {
            response.setHeader("Access-Control-Allow-Origin", "*");
            response.setHeader("Access-Control-Allow-Methods", "GET,POST");
            response.setHeader("Access-Control-Allow-Headers", "*");
            response.setContentType("application/json");
            response.setCharacterEncoding("UTF-8");

            String method = request.getMethod();

//            if (!method.equalsIgnoreCase("GET")) {
//                throw new Exception("Methodo não suportado para esse recurso");
//            }

            //key parametro "key"
            String key = request.getParameter("key");
            if (UteisValidacao.emptyString(key)) {
                throw new Exception("Key não informada");
            }
            //token parametro "tk"
            String token = request.getParameter("tk");
            if (UteisValidacao.emptyString(token)) {
                throw new Exception("Token não informado");
            }
            //operacao parametro "op"
            String operacao = request.getParameter("op");
            if (UteisValidacao.emptyString(operacao)) {
                throw new Exception("Operacao não informada");
            }
            //usuario parametro "u"
            Integer usuario = UteisValidacao.converterInteiro(request.getParameter("u"));

            try (Connection con = new DAO().obterConexaoEspecifica(key)) {
                TokenBoleto tokenBoletoDAO = new TokenBoleto(con);
                TokenBoletoVO tokenBoletoVO = tokenBoletoDAO.consultarPorToken(token);
                if (tokenBoletoVO == null) {
                    throw new Exception("Boleto não encontrado");
                }

                if (operacao.equalsIgnoreCase("enviarEmail")) {
                    String body = obterBody(request);
                    JSONObject bodyReq = new JSONObject(body);
                    String email = bodyReq.getString("email");

                    JSONObject bodyDados = new JSONObject(tokenBoletoVO.getDados());
                    JSONArray boletos = bodyDados.getJSONArray("boletos");
                    for (int i = 0; i < boletos .length(); i++) {
                        JSONObject bol = boletos.getJSONObject(i);
                        Integer tipoCobranca = bol.getInt("tipoCobranca");
                        Integer codigo = bol.getInt("codigo");
                        if (tipoCobranca.equals(TipoCobrancaEnum.BOLETO_ONLINE.getId())) {
                            new TelaClienteBoleto().enviarEmailBoleto(key, codigo, usuario, email, request, con);
                        } else if (tipoCobranca.equals(TipoCobrancaEnum.BOLETO.getId())) {
                            new TelaClienteBoleto().enviarEmailRemessaItem(key, codigo, usuario, email, request, con);
                        }
                    }
                }
            }
        } catch (Exception ex) {
            response.setStatus(400);
            PrintWriter out = response.getWriter();
            out.print(this.toJSON(false, ex.getMessage()));
            out.flush();
        }
    }

    private JSONObject toJSON(boolean sucesso, Object obj) {
        JSONObject json = new JSONObject();
        json.put("sucesso", sucesso);
        json.put("dados", obj);
        return json;
    }

    private String obterBody(ServletRequest request) throws Exception {
        StringBuffer body = new StringBuffer();
        try {
            InputStream inputStream = request.getInputStream();
            BufferedReader reader = new BufferedReader(new InputStreamReader(inputStream, StandardCharsets.UTF_8));
            String line = null;

            while ((line = reader.readLine()) != null) {
                body.append(line);
            }
            return body.toString();
        } catch (Exception ex) {
            ex.printStackTrace();
            throw new Exception("Erro body: " + ex.getMessage());
        }
    }
}
