package servlet.arquitetura;

import br.com.pactosolucoes.atualizadb.processo.AjustarAberturaMetaDuplicada;
import br.com.pactosolucoes.atualizadb.processo.CorrigirMovParcela;
import br.com.pactosolucoes.atualizadb.processo.CorrigirMovProdutos;
import br.com.pactosolucoes.atualizadb.processo.CriarProdutosContaCorrente;
import br.com.pactosolucoes.atualizadb.processo.SincronizarUsuarioNovoLogin;
import br.com.pactosolucoes.atualizadb.processo.SubsitituirProdutos;
import br.com.pactosolucoes.atualizadb.processo.VerificarRecibosPagamentosSemVinculo;
import br.com.pactosolucoes.atualizadb.processo.ajustebd.AdicionarModalidadeEmPlano;
import br.com.pactosolucoes.atualizadb.processo.ajustebd.RetirarModalidadeEmPlano;
import br.com.pactosolucoes.comuns.util.Formatador;
import br.com.pactosolucoes.enumeradores.TipoColaboradorEnum;
import br.com.pactosolucoes.enumeradores.TipoInfoMigracaoEnum;
import br.com.pactosolucoes.enumeradores.TipoTelefoneEnum;
import br.com.pactosolucoes.integracao.login.dto.InfoBloqueioDTO;
import br.com.pactosolucoes.integracao.pactopay.dto.EmpresaInfoDTO;
import br.com.pactosolucoes.integracao.pactopay.dto.EnderecoDTO;
import br.com.pactosolucoes.oamd.controle.basico.DAO;
import com.fasterxml.jackson.databind.ObjectMapper;
import controle.arquitetura.SuperControle;
import controle.arquitetura.security.LoginControle;
import controle.arquitetura.security.UsuarioControle;
import controle.arquitetura.servico.RecursoMsService;
import controle.arquitetura.servlet.update.UpdateServlet;
import negocio.comuns.arquitetura.*;
import negocio.comuns.basico.ColaboradorVO;
import negocio.comuns.basico.EmailVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.basico.TelefoneVO;
import negocio.comuns.contrato.ContratoVO;
import negocio.comuns.crm.ConfiguracaoSistemaCRMVO;
import negocio.comuns.feed.PerfilUsuarioEnum;
import negocio.comuns.financeiro.ConvenioCobrancaVO;
import negocio.comuns.financeiro.FormaPagamentoVO;
import negocio.comuns.financeiro.PixVO;
import negocio.comuns.plano.ModalidadeVO;
import negocio.comuns.plano.PlanoVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.SolicitacaoVO;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisEmail;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.*;
import negocio.facade.jdbc.basico.*;
import negocio.facade.jdbc.contrato.Contrato;
import negocio.facade.jdbc.crm.ConfiguracaoSistemaCRM;
import negocio.facade.jdbc.financeiro.ConvenioCobranca;
import negocio.facade.jdbc.financeiro.Pix;
import negocio.facade.jdbc.financeiro.ProdutosPagosServico;
import negocio.facade.jdbc.plano.Modalidade;
import negocio.facade.jdbc.plano.Plano;
import negocio.facade.jdbc.utilitarias.Conexao;
import negocio.facade.jdbc.utilitarias.Solicitacao;
import negocio.modulos.integracao.usuariomovel.UsuarioMovel;
import negocio.modulos.integracao.usuariomovel.UsuarioMovelVO;
import org.json.JSONArray;
import org.json.JSONObject;
import relatorio.negocio.comuns.sad.SituacaoClienteSinteticoEnum;
import servicos.impl.dcc.base.ReprocessarExtratos;
import servicos.impl.email.EmailNovoLoginService;
import servicos.impl.login.DesvincularUsuarioDTO;
import servicos.integracao.adm.AdmWS;
import servicos.integracao.adm.beans.EmpresaWS;
import servicos.pix.PixPagamentoService;
import servicos.pix.PixStatusEnum;
import servicos.propriedades.PropsService;
import servicos.usuario.UsuarioService;
import servicos.util.ExecuteRequestHttpService;

import javax.servlet.ServletException;
import javax.servlet.ServletRequest;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.ResultSetMetaData;
import java.sql.SQLException;
import java.util.*;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.stream.Collectors;

public class UteisPactoServlet extends SuperServlet {

    @Override
    protected void processRequest(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        try {
            response.setContentType("application/json");

            String operacao = request.getParameter("op");
            if (UteisValidacao.emptyString(operacao)) {
                throw new Exception("Operação informada");
            }

            EnvelopeRespostaDTO envelopeRespostaDTO;
            if (operacao.equalsIgnoreCase("enviar-email-nova-senha")) {
                envelopeRespostaDTO = enviarEmailRedefinirSenha(request);
            } else if (operacao.equalsIgnoreCase("enviar-email-ativar-usuario")) {
                envelopeRespostaDTO = enviarEmailAtivarUsuario(request);
            } else if (operacao.equalsIgnoreCase("enviar-email-boas-vindas")) {
                envelopeRespostaDTO = enviarEmailBoasVindas(request);
            } else if (operacao.equalsIgnoreCase("enviar-email-codigo-email")) {
                envelopeRespostaDTO = enviarEmailCodigoEmail(request);
            } else if (operacao.equalsIgnoreCase("alterar-senha-usuario")) {
                envelopeRespostaDTO = alterarSenhaUsuario(request);
            } else if (operacao.equalsIgnoreCase("alterar-email-usuario")) {
                envelopeRespostaDTO = alterarEmailUsuario(request);
            } else if (operacao.equalsIgnoreCase("alterar-telefone-usuario")) {
                envelopeRespostaDTO = alterarTelefoneUsuario(request);
            } else if (operacao.equalsIgnoreCase("alterar-usuario-geral")) {
                envelopeRespostaDTO = alterarUsuarioGeral(request);
            } else if (operacao.equalsIgnoreCase("sincronizar-usuario-rede-empresa")) {
                envelopeRespostaDTO = sincronizarUsuarioRedeEmpresa(request);
            } else if (operacao.equalsIgnoreCase("info-empresa")) {
                envelopeRespostaDTO = informacoesEmpresa(request);
            } else if (operacao.equalsIgnoreCase("alterar-idexterno-empresa")) {
                envelopeRespostaDTO = alterarIdExternoEmpresa(request);
            } else if (operacao.equalsIgnoreCase("validar-data-expiracao")) {
                envelopeRespostaDTO = informacoesDaraExpiracao(request);
            } else if (operacao.equalsIgnoreCase("validar-modulo-nota-fiscal")) {
                envelopeRespostaDTO = validarUsoModuloNotaFiscal(request);
            } else if (operacao.equalsIgnoreCase("login-usuario-sem-senha")) {
                envelopeRespostaDTO = loginUsuarioSemSenha(request);
            } else if (operacao.equalsIgnoreCase("configuracao-sistema")) {
                envelopeRespostaDTO = configuracaoSistema(request);
            } else if (operacao.equalsIgnoreCase("obter-usuario-geral")) {
                envelopeRespostaDTO = obterUsuarioGeral(request);
            } else if (operacao.equalsIgnoreCase("desvincular-usuario-geral")) {
                envelopeRespostaDTO = desvincularUsuario(request);
            } else if (operacao.equalsIgnoreCase("enviar-email-teste")) {
                envelopeRespostaDTO = enviarEmailTeste(request, response);
            } else if (operacao.equalsIgnoreCase("reprocessar-extratos")) {
                envelopeRespostaDTO = reprocessarExtratos(request, response);
            } else if (operacao.equalsIgnoreCase("consultar-pagamento-parcelas-sem-recibo")) {
                envelopeRespostaDTO = consultarPagamentoParcelasSemRecibo(request, response);
            } else if (operacao.equalsIgnoreCase("corrigir-pagamento-parcelas-sem-recibo")) {
                envelopeRespostaDTO = corrigirPagamentoParcelasSemRecibo(request, response);
            } else if (operacao.equalsIgnoreCase("consultar-pagamento-sem-produto-pago")) {
                envelopeRespostaDTO = consultarPagamentoSemProdutoPago(request, response);
            } else if (operacao.equalsIgnoreCase("corrigir-pagamento-sem-produto-pago")) {
                envelopeRespostaDTO = corrigirPagamentoSemProdutoPago(request, response);
            } else if (operacao.equalsIgnoreCase("consultar-pagamento-sem-vinculo-parcela")) {
                envelopeRespostaDTO = consultarPagamentoSemVinculoParcela(request, response);
            } else if (operacao.equalsIgnoreCase("consultar-parcelas-sem-produtos")) {
                envelopeRespostaDTO = consultarParcelasSemProdutos(request, response);
            } else if (operacao.equalsIgnoreCase("corrigir-parcelas-sem-produtos")) {
                envelopeRespostaDTO = corrigirParcelasSemProdutos(request, response);
            } else if (operacao.equalsIgnoreCase("consultar-produtos-sem-parcelas")) {
                envelopeRespostaDTO = consultarProdutosSemParcelas(request, response);
            } else if (operacao.equalsIgnoreCase("corrigir-produtos-sem-parcelas")) {
                envelopeRespostaDTO = corrigirProdutosSemParcelas(request, response);
            } else if (operacao.equalsIgnoreCase("consultar-parcelas-em-aberto-com-pagamento")) {
                envelopeRespostaDTO = consultarParcelasEmAbertoComPagamento(request, response);
            } else if (operacao.equalsIgnoreCase("corrigir-parcelas-em-aberto-com-pagamento")) {
                envelopeRespostaDTO = corrigirParcelasEmAbertoComPagamento(request, response);
            } else if (operacao.equalsIgnoreCase("consultar-recibos-sem-produtos-cc")) {
                envelopeRespostaDTO = consultarRecibosSemProdutoContaCorrente(request, response);
            } else if (operacao.equalsIgnoreCase("corrigir-recibos-sem-produtos-cc")) {
                envelopeRespostaDTO = corrigirRecibosSemProdutoContaCorrente(request, response);
            } else if (operacao.equalsIgnoreCase("substituir-produtos")) {
                envelopeRespostaDTO = substituirProdutos(request, response);
            } else if (operacao.equalsIgnoreCase("corrigir-valores-contrato-recibo-produtos-pagos")) {
                envelopeRespostaDTO = corrigirValoresContratoReciboProdutosPagos(request, response);
            } else if (operacao.equalsIgnoreCase("consultar-total-contratos-plano")) {
                envelopeRespostaDTO = consultarTotalContratosPlano(request, response);
            } else if (operacao.equalsIgnoreCase("corrigir-ferias-contrato")) {
                envelopeRespostaDTO = corrigirFeriasContrato(request, response);
            } else if (operacao.equalsIgnoreCase("corrigir-itens-meta-diaria-duplicados")) {
                envelopeRespostaDTO = corrigirItensMetaDiariaDuplicados(request, response);
            } else if(operacao.equalsIgnoreCase("consultar-contratos-retirar-modalidade")){
                envelopeRespostaDTO = consultarContratosRetirarModalidade(request, response);
            } else if(operacao.equalsIgnoreCase("contratos-retirar-modalidade")){
                envelopeRespostaDTO = contratoRetirarModalidade(request, response);
            } else if(operacao.equalsIgnoreCase("consultar-contratos-adicionar-modalidade")){
                envelopeRespostaDTO = consultarContratosAdicionarModalidade(request, response);
            } else if(operacao.equalsIgnoreCase("contratos-adicionar-modalidade")){
                envelopeRespostaDTO = contratoAdicionarModalidade(request, response);
            } else if(operacao.equalsIgnoreCase("consultar-contratos-adicionar-modalidade")){
                envelopeRespostaDTO = consultarContratosAdicionarModalidade(request, response);
            } else if (operacao.equalsIgnoreCase("processar-pix")) {
                envelopeRespostaDTO = processarPix(request, response);
            } else if (operacao.equalsIgnoreCase("invalidar-cache-nicho")) {
                envelopeRespostaDTO = invalidarCacheNicho(request, response);
            } else if (operacao.equalsIgnoreCase("recurso-padrao")) {
                envelopeRespostaDTO = recursoPadrao(request);
            } else if (operacao.equalsIgnoreCase("enviar-email-link-solicitacao")) {
                envelopeRespostaDTO = enviarEmailLinkSolicitacao(request, response);
            } else if (operacao.equalsIgnoreCase("desvincularIdFavorecido")) {
                envelopeRespostaDTO = desvincularIdFavorecido(request, response);
            } else if (operacao.equalsIgnoreCase("obter-empresas-usuario")) {
                envelopeRespostaDTO = obterEmpresasUsuario(request);
            } else if (operacao.equalsIgnoreCase("obter-empresas-com-situacao")) {
                envelopeRespostaDTO = obterEmpresasComSituacao(request);
            } else {
                throw new Exception("Nenhuma operação executada");
            }

            if(!(envelopeRespostaDTO.getMeta() != null && envelopeRespostaDTO.getMeta().getError() != null)){
                response.setStatus(HttpServletResponse.SC_OK);
            }
            response.getWriter().append(new JSONObject(envelopeRespostaDTO).toString());
        } catch (Exception ex) {
            ex.printStackTrace();
            response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
            response.getWriter().append(new JSONObject(EnvelopeRespostaDTO.erro("Erro", ex.getMessage())).toString());
        }
    }

    private EnvelopeRespostaDTO desvincularIdFavorecido(HttpServletRequest request, HttpServletResponse response) throws Exception {
        Connection con = null;
        try {
            JSONObject json = new JSONObject(obterBody(request));
            String chave = json.optString("chave");
            Integer codigoEmpresa = json.optInt("codigo");
            if (UteisValidacao.emptyString(chave)) {
                throw new Exception("Chave não informada");
            }
            if (UteisValidacao.emptyNumber(codigoEmpresa)) {
                throw new Exception("Codigo da empresa não informado");
            }

            con = obterConexao(request, chave);

            Empresa empresaDAO = new Empresa(con);
            EmpresaVO empresa = empresaDAO.consultarPorCodigo(codigoEmpresa, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            empresa.setCodEmpresaFinanceiro(0);
            empresa.setSincronizacaoFinanceiro(null);
            empresaDAO.alterarDataSincronizacaoFinanceiro(empresa);

            return EnvelopeRespostaDTO.of("ok");
        } catch (Exception ex) {
            ex.printStackTrace();
            throw ex;
        } finally {
            finalizarConexao(con);
        }
    }

    private EnvelopeRespostaDTO enviarEmailLinkSolicitacao(HttpServletRequest request, HttpServletResponse response) throws Exception {
        Connection con = null;
        try {
            JSONObject json = new JSONObject(obterBody(request));
            String chave = json.optString("chave");
            Integer codigoSolicitacao = json.optInt("codigoSolicitacao");
            if (UteisValidacao.emptyString(chave)) {
                throw new Exception("Chave não informada");
            }
            if (UteisValidacao.emptyNumber(codigoSolicitacao)) {
                throw new Exception("Codigo da solicitação não informado");
            }

            con = obterConexao(request, chave);
            Solicitacao solicitacaoDao = new Solicitacao(con);
            SolicitacaoVO solicitacaoVO = solicitacaoDao.consultarPorChavePrimaria(codigoSolicitacao, Uteis.NIVELMONTARDADOS_DADOSENTIDADESPRINCIPAIS);
            StringBuilder mensagem = solicitacaoVO.gerarCorpoEmailSolicitacaoNotas();
            enviarEmailNoReply(solicitacaoVO.getUsuarioSolicitante().getEmail(), "Solicitação Download Notas Sistema Pacto", mensagem.toString());
            return EnvelopeRespostaDTO.of("ok");
        } catch (Exception ex) {
            ex.printStackTrace();
            throw ex;
        } finally {
            finalizarConexao(con);
        }
    }


    private EnvelopeRespostaDTO invalidarCacheNicho(HttpServletRequest request, HttpServletResponse response) {
        EnvelopeRespostaDTO envelopeRespostaDTO;
        try {
            envelopeRespostaDTO = EnvelopeRespostaDTO.of(RecursoMsService.invalidarCache(request.getParameter("chave")));

            String propagable = request.getParameter("propagable");
            if (propagable != null && propagable.equals("s")) {
                UpdateServlet.propagarRequestInner("", response.getWriter(), request.getParameterMap(), request.getRequestURL().toString());
            }
        } catch (Exception e) {
            response.setStatus(500);
            envelopeRespostaDTO = EnvelopeRespostaDTO.erro("Falha invalidar cache.", e.getMessage());
        }

        return envelopeRespostaDTO;
    }

    private EnvelopeRespostaDTO contratoAdicionarModalidade(HttpServletRequest request, HttpServletResponse response) {
        Connection connection = null;
        EnvelopeRespostaDTO envelopeRespostaDTO = null;
        try {
            JSONObject json = new JSONObject(obterBody(request));
            String chave = json.optString("chave");
            Integer empresa = json.optInt("empresa");
            Integer modalidade = json.optInt("modalidade");
            Integer plano = json.optInt("plano");
            Integer usuario = json.optInt("usuario");
            String descricaoHorario = json.optString("descricaoHorario");

            if(chave == null || chave.isEmpty()){
                response.setStatus(400);
                throw new Exception("Chave não informada");
            }

            if(empresa == null || empresa == 0){
                response.setStatus(400);
                throw new Exception("Empresa não informada");
            }

            if(modalidade == null || modalidade == 0){
                response.setStatus(400);
                throw new Exception("Modalidade não informada");
            }

            if(plano == null || plano == 0){
                response.setStatus(400);
                throw new Exception("Plano não informado");
            }

            if(usuario == null || usuario == 0){
                response.setStatus(400);
                throw new Exception("Usuário não informado");
            }

            connection = new DAO().obterConexaoEspecifica(chave);
            Conexao.guardarConexaoForJ2SE(chave, connection);

            Modalidade modalidadeDAO = new Modalidade(connection);
            Usuario usuarioDAO = new Usuario(connection);
            ZillyonWebFacade zillyonWebFacadeDAO = new ZillyonWebFacade(connection);
            Plano planoDAO = new Plano(connection);
            Log logDAO = new Log(connection);

            ModalidadeVO modalidadeVO = modalidadeDAO.consultarPorChavePrimaria(modalidade, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            UsuarioVO usuarioVO = usuarioDAO.consultarPorChavePrimaria(usuario, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            PlanoVO planoVO = planoDAO.consultarPorChavePrimaria(plano, Uteis.NIVELMONTARDADOS_DADOSBASICOS);

            AdicionarModalidadeEmPlano adicionarModalidadeEmPlano = new AdicionarModalidadeEmPlano();
            List<ContratoVO> contratoVOS = adicionarModalidadeEmPlano.consultarContratosAdicionarModalidade(empresa, planoVO, modalidadeVO, descricaoHorario);

            int qtdContratosAfetados = 0;
            StringBuilder contratosNaoAdicionados = new StringBuilder("");
            for (ContratoVO contratoVO : contratoVOS) {
                try {
                    adicionarModalidadeEmPlano.adicionarModalidadeContrato(contratoVO, modalidadeVO, usuarioVO);
                    zillyonWebFacadeDAO.atualizarSintetico(contratoVO.getCliente(), Calendario.hoje(), SituacaoClienteSinteticoEnum.GRUPO_CONTRATO, true);
                    qtdContratosAfetados++;
                } catch (Exception ex) {
                    contratosNaoAdicionados.append(contratoVO.getCodigo()).append(",");
                }
            }

            LogVO obj = new LogVO();
            obj.setChavePrimaria("0");
            obj.setPessoa(0);
            obj.setNomeEntidade("CONFIGURACAOSISTEMA-ADICIONAR_MODALIDADE_CONTRATO");
            obj.setNomeEntidadeDescricao("Configurações");
            obj.setOperacao("Adicionar Modalidade (Processo)");
            obj.setResponsavelAlteracao(usuarioVO.getNome());
            obj.setUserOAMD(usuarioVO.getUserOamd());
            obj.setDataAlteracao(negocio.comuns.utilitarias.Calendario.hoje());
            obj.setNomeCampo("");
            obj.setValorCampoAnterior("");
            String msg = "Adicionada a modalidade " + modalidadeVO.getNome() + " para os contratos do plano " + planoVO.getDescricao() +
                    (!UteisValidacao.emptyString(descricaoHorario) ? " e horário " + descricaoHorario: "") +
                    " (Total: " + qtdContratosAfetados + ").";
            if (contratosNaoAdicionados.length() > 0) {
                msg += " Contratos não afetados: " + contratosNaoAdicionados.substring(0, contratosNaoAdicionados.length() - 2);
            }
            obj.setValorCampoAlterado(msg);
            logDAO.incluir(obj);

            envelopeRespostaDTO = EnvelopeRespostaDTO.of(msg);
        } catch (Exception e) {
            envelopeRespostaDTO = EnvelopeRespostaDTO.erro("Falha ao adicionar modalidades aos contratos", e.getMessage());
        }finally {
            finalizarConexao(connection);
        }

        return envelopeRespostaDTO;
    }

    private EnvelopeRespostaDTO consultarContratosAdicionarModalidade(HttpServletRequest request, HttpServletResponse response) {
        Connection connection = null;
        EnvelopeRespostaDTO envelopeRespostaDTO = null;
        try {
            String chave = request.getParameter("chave");
            String empresaParam = request.getParameter("empresa");
            String modalidadeParam = request.getParameter("modalidade");
            String planoParam = request.getParameter("plano");
            String descricaoHorario = request.getParameter("descricaoHorario");

            Integer empresa;
            try{
                empresa = Integer.parseInt(empresaParam.toString());
            }catch (Exception e){
                throw new Exception("Empresa inválida");
            }

            Integer modalidade;
            try{
                modalidade = Integer.parseInt(modalidadeParam.toString());
            }catch (Exception e){
                throw new Exception("Modalidade inválida");
            }

            Integer plano;
            try{
                plano = Integer.parseInt(planoParam.toString());
            }catch (Exception e){
                throw new Exception("Plano inválido");
            }

            if(chave == null || chave.isEmpty()){
                response.setStatus(400);
                throw new Exception("Chave não informada");
            }

            if(empresa == null || empresa == 0){
                response.setStatus(400);
                throw new Exception("Empresa não informada");
            }

            if(modalidade == null || modalidade == 0){
                response.setStatus(400);
                throw new Exception("Modalidade não informada");
            }

            if(plano == null || plano == 0){
                response.setStatus(400);
                throw new Exception("Plano não informado");
            }

            connection = new DAO().obterConexaoEspecifica(chave);
            Conexao.guardarConexaoForJ2SE(chave, connection);

            Modalidade modalidadeDAO = new Modalidade(connection);
            Plano planoDAO = new Plano(connection);

            ModalidadeVO modalidadeVO = modalidadeDAO.consultarPorChavePrimaria(modalidade, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            PlanoVO planoVO = planoDAO.consultarPorChavePrimaria(plano, Uteis.NIVELMONTARDADOS_DADOSBASICOS);

            AdicionarModalidadeEmPlano adicionarModalidadeEmPlano = new AdicionarModalidadeEmPlano();
            List<ContratoVO> contratoVOS = adicionarModalidadeEmPlano.consultarContratosAdicionarModalidade(empresa, planoVO, modalidadeVO, descricaoHorario);
            List<ContratoPlanoVO> contratoPlanoVOS = contratoVOS.stream().map(contratoVO -> {
                ContratoPlanoVO contratoPlanoVO = new ContratoPlanoVO();
                contratoPlanoVO.setContrato(contratoVO.getCodigo());
                contratoPlanoVO.setPlano(contratoVO.getPlano().getDescricao());
                contratoPlanoVO.setPessoa(contratoVO.getPessoa().getNome());

                return contratoPlanoVO;
            }).collect(Collectors.toList());

            envelopeRespostaDTO = EnvelopeRespostaDTO.of(contratoPlanoVOS);
        } catch (Exception e) {
            envelopeRespostaDTO = EnvelopeRespostaDTO.erro("Falha ao consultar contratos para adicionar plano.", e.getMessage());
        }finally {
            finalizarConexao(connection);
        }

        return envelopeRespostaDTO;
    }

    private EnvelopeRespostaDTO consultarContratosRetirarModalidade(HttpServletRequest request, HttpServletResponse response) {
        Connection connection = null;
        EnvelopeRespostaDTO envelopeRespostaDTO = null;
        try {
            String chave = request.getParameter("chave");
            String empresaParam = request.getParameter("empresa");
            String modalidadeParam = request.getParameter("modalidade");
            String planoParam = request.getParameter("plano");

            Integer empresa;
            try{
                empresa = Integer.parseInt(empresaParam.toString());
            }catch (Exception e){
                throw new Exception("Empresa inválida");
            }

            Integer modalidade;
            try{
                modalidade = Integer.parseInt(modalidadeParam.toString());
            }catch (Exception e){
                throw new Exception("Modalidade inválida");
            }

            Integer plano;
            try{
                plano = Integer.parseInt(planoParam.toString());
            }catch (Exception e){
                throw new Exception("Plano inválido");
            }

            if(chave == null || chave.isEmpty()){
                response.setStatus(400);
                throw new Exception("Chave não informada");
            }

            if(chave == null || chave.isEmpty()){
                response.setStatus(400);
                throw new Exception("Chave não informada");
            }

            if(empresa == null || empresa == 0){
                response.setStatus(400);
                throw new Exception("Empresa não informada");
            }

            if(modalidade == null || modalidade == 0){
                response.setStatus(400);
                throw new Exception("Modalidade não informada");
            }

            if(plano == null || plano == 0){
                response.setStatus(400);
                throw new Exception("Plano não informado");
            }

            connection = new DAO().obterConexaoEspecifica(chave);
            Conexao.guardarConexaoForJ2SE(chave, connection);

            Modalidade modalidadeDAO = new Modalidade(connection);
            Plano planoDAO = new Plano(connection);

            ModalidadeVO modalidadeVO = modalidadeDAO.consultarPorChavePrimaria(modalidade, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            PlanoVO planoVO = planoDAO.consultarPorChavePrimaria(plano, Uteis.NIVELMONTARDADOS_DADOSBASICOS);

            RetirarModalidadeEmPlano retirarModalidadeEmPlano = new RetirarModalidadeEmPlano();
            List<ContratoVO> contratoVOS = retirarModalidadeEmPlano.consultarContratosRetirarModalidade(empresa, planoVO, modalidadeVO);
            List<ContratoPlanoVO> contratoPlanoVOS = contratoVOS.stream().map(contratoVO -> {
                ContratoPlanoVO contratoPlanoVO = new ContratoPlanoVO();
                contratoPlanoVO.setContrato(contratoVO.getCodigo());
                contratoPlanoVO.setPlano(contratoVO.getPlano().getDescricao());
                contratoPlanoVO.setPessoa(contratoVO.getPessoa().getNome());

                return contratoPlanoVO;
            }).collect(Collectors.toList());

            envelopeRespostaDTO = EnvelopeRespostaDTO.of(contratoPlanoVOS);
        } catch (Exception e) {
            envelopeRespostaDTO = EnvelopeRespostaDTO.erro("Falha ao consultar contratos para retirar modalidade.", e.getMessage());
        }finally {
            finalizarConexao(connection);
        }

        return envelopeRespostaDTO;
    }

    private EnvelopeRespostaDTO contratoRetirarModalidade(HttpServletRequest request, HttpServletResponse response) {
        Connection connection = null;
        EnvelopeRespostaDTO envelopeRespostaDTO = null;
        try {
            JSONObject json = new JSONObject(obterBody(request));
            String chave = json.optString("chave");
            Integer empresa = json.optInt("empresa");
            Integer modalidade = json.optInt("modalidade");
            Integer plano = json.optInt("plano");
            Integer usuario = json.optInt("usuario");

            if(chave == null || chave.isEmpty()){
                response.setStatus(400);
                throw new Exception("Chave não informada");
            }

            if(empresa == null || empresa == 0){
                response.setStatus(400);
                throw new Exception("Empresa não informada");
            }

            if(modalidade == null || modalidade == 0){
                response.setStatus(400);
                throw new Exception("Modalidade não informada");
            }

            if(plano == null || plano == 0){
                response.setStatus(400);
                throw new Exception("Plano não informado");
            }

            if(usuario == null || usuario == 0){
                response.setStatus(400);
                throw new Exception("Usuário não informado");
            }

            connection = new DAO().obterConexaoEspecifica(chave);
            Conexao.guardarConexaoForJ2SE(chave, connection);

            Modalidade modalidadeDAO = new Modalidade(connection);
            Usuario usuarioDAO = new Usuario(connection);
            ZillyonWebFacade zillyonWebFacadeDAO = new ZillyonWebFacade(connection);
            Plano planoDAO = new Plano(connection);
            Log logDAO = new Log(connection);

            ModalidadeVO modalidadeVO = modalidadeDAO.consultarPorChavePrimaria(modalidade, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            UsuarioVO usuarioVO = usuarioDAO.consultarPorChavePrimaria(usuario, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            PlanoVO planoVO = planoDAO.consultarPorChavePrimaria(plano, Uteis.NIVELMONTARDADOS_DADOSBASICOS);

            RetirarModalidadeEmPlano retirarModalidadeEmPlano = new RetirarModalidadeEmPlano();
            modalidadeDAO.consultarPorChavePrimaria(modalidade, Uteis.NIVELMONTARDADOS_DADOSBASICOS);

            List<ContratoVO> contratoVOS = retirarModalidadeEmPlano.consultarContratosRetirarModalidade(empresa, planoVO, modalidadeVO);

            int qtdContratosAfetados = 0;
            StringBuilder contratosNaoRetirados = new StringBuilder("");
            for (ContratoVO contratoVO : contratoVOS) {
                try {
                    retirarModalidadeEmPlano.retirarModalidadeContrato(contratoVO, modalidadeVO, usuarioVO);
                    zillyonWebFacadeDAO.atualizarDadosSintetico(contratoVO.getCliente(), modalidadeVO);
                    qtdContratosAfetados++;
                } catch (Exception ex) {
                    contratosNaoRetirados.append(contratoVO.getCodigo()).append(",");
                }
            }

            LogVO obj = new LogVO();
            obj.setChavePrimaria("0");
            obj.setPessoa(0);
            obj.setNomeEntidade("CONFIGURACAOSISTEMA-RETIRAR_MODALIDADE_CONTRATO");
            obj.setNomeEntidadeDescricao("Configurações");
            obj.setOperacao("Retirar Modalidade (Processo)");
            obj.setResponsavelAlteracao(usuarioVO.getNome());
            obj.setUserOAMD(usuarioVO.getUserOamd());
            obj.setDataAlteracao(negocio.comuns.utilitarias.Calendario.hoje());
            obj.setNomeCampo("");
            obj.setValorCampoAnterior("");
            String msg = "Retirada a modalidade " + modalidadeVO.getNome() + " para os contratos do plano " + planoVO.getDescricao() + " (Total: " + qtdContratosAfetados + ").";
            if (contratosNaoRetirados.length() > 0) {
                msg += " Contratos não afetados: " + contratosNaoRetirados.substring(0, contratosNaoRetirados.length() - 2);
            }
            obj.setValorCampoAlterado(msg);
            logDAO.incluir(obj);

            envelopeRespostaDTO = EnvelopeRespostaDTO.of(msg);
        } catch (Exception e) {
            envelopeRespostaDTO = EnvelopeRespostaDTO.erro("Falha ao retirar modalidade dos contratos", e.getMessage());
        }finally {
            finalizarConexao(connection);
        }

        return envelopeRespostaDTO;
    }

    private EnvelopeRespostaDTO processarPix(HttpServletRequest request, HttpServletResponse response) {
        Connection connection = null;
        EnvelopeRespostaDTO envelopeRespostaDTO = null;
        try {
            JSONObject json = new JSONObject(obterBody(request));
            String chave = json.optString("chave");
            String dataPagamentoPram = json.optString("dataPagamento");
            String codigosPix = json.optString("codigosPix");

            if(chave == null || chave.isEmpty()){
                response.setStatus(400);
                throw new Exception("Chave não informada");
            }

            if(dataPagamentoPram == null || dataPagamentoPram.isEmpty()){
                response.setStatus(400);
                throw new Exception("Data do pagmaento não informada");
            }

            if(codigosPix == null || codigosPix.length() == 0){
                response.setStatus(400);
                throw new Exception("Códigos PIX não informados");
            }

            Date dataPagamento = Calendario.getDate("dd/MM/yyyy", dataPagamentoPram);
            dataPagamento = Calendario.inicioDoDia(dataPagamento);

            connection = new DAO().obterConexaoEspecifica(chave);
            Conexao.guardarConexaoForJ2SE(chave, connection);
            Pix pix = new Pix(connection);
            ConvenioCobranca convenioCobranca = new ConvenioCobranca(connection);
            Empresa empresa = new Empresa(connection);
            ConfiguracaoSistema configuracaoSistema = new ConfiguracaoSistema(connection);
            ZillyonWebFacade zillyonWebFacade = new ZillyonWebFacade(connection);


            Integer qtdProcessados = 0;
            Integer qtdErros = 0;

            List<String> listaSucesso = new ArrayList<>();
            List<String> listaErro = new ArrayList<>();

            for (String item : codigosPix.split(",")) {
                Integer codigoItem = null;
                PixVO pixVOClone = new PixVO();
                try {
                    Connection con;
                    con = Conexao.getFromSession();
                    codigoItem = Integer.parseInt(item);
                    PixVO pixVO = pix.consultarPorCodigo(codigoItem, true);

                    //usado para adicionar na lista de erros caso entre no catch
                    pixVOClone = pixVO;

                    if (pixVO != null && pixVO.getStatus().equals("ATIVA")) {
                        ConvenioCobrancaVO convenioCobrancaVO = convenioCobranca.consultarDadosPix(pixVO.getConveniocobranca().getCodigo());
                        EmpresaVO empresaVO = empresa.consultarPorCodigo(pixVO.getEmpresa(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                        FormaPagamentoVO formaPagamentoVO = pix.obterFormaPagamentoPix(pixVO, convenioCobrancaVO);
                        if (formaPagamentoVO == null || UteisValidacao.emptyNumber(formaPagamentoVO.getCodigo())) {
                            throw new Exception("Não foi encontrado uma forma de pagamento PIX!");
                        }
                        convenioCobrancaVO.setEmpresa(empresaVO);

                        PixPagamentoService pixPagamentoService = new PixPagamentoService(con);
                        pixVO.setDataPagamento(dataPagamento);
                        pixPagamentoService.processarPagamento(pixVO, convenioCobrancaVO, formaPagamentoVO);
                        qtdProcessados++;
                        listaSucesso.add(pixVO.getCodigo().toString());
                        pix.cancelar(pixVO);
                        pix.alterarStatusAjusteManual(pixVO, PixStatusEnum.CONCLUIDA);
                        try {
                            if (!UteisValidacao.emptyNumber(pixVO.getReciboPagamento()) && configuracaoSistema.realizarEnvioSesiSC()) {
                                List<Integer> listaRecibo = new ArrayList<>();
                                listaRecibo.add(pixVO.getReciboPagamento());
                                zillyonWebFacade.startThreadIntegracaoFiesc(pixVO.getEmpresa(), listaRecibo, "incluirPix");
                            }
                        } catch (Exception e){
                        }
                    } else {
                        qtdErros++;
                        listaErro.add(pixVO.getCodigo().toString() + " - Cobrança nula ou não está com status 'AGUARDANDO PAGAMENTO'");
                    }
                } catch (Exception e) {
                    qtdErros++;
                    listaErro.add(codigoItem.toString());
                }
            }

            JSONObject jsonRetorno = new JSONObject();
            jsonRetorno.put("processados",listaSucesso);
            jsonRetorno.put("erros",listaErro);
            jsonRetorno.put("msg","Pix processados");

            envelopeRespostaDTO = EnvelopeRespostaDTO.of(jsonRetorno);
        } catch (Exception e) {
            envelopeRespostaDTO = EnvelopeRespostaDTO.erro("Falha ao processar pix", e.getMessage());
        }finally {
            finalizarConexao(connection);
        }

        return envelopeRespostaDTO;
    }

    private EnvelopeRespostaDTO corrigirItensMetaDiariaDuplicados(HttpServletRequest request, HttpServletResponse response) {
        Connection connection = null;
        EnvelopeRespostaDTO envelopeRespostaDTO = null;
        try {
            JSONObject json = new JSONObject(obterBody(request));
            String chave = json.optString("chave");
            String dataInicioParam = json.optString("dataInicio");

            if(chave == null || chave.isEmpty()){
                response.setStatus(400);
                throw new Exception("Chave não informada");
            }

            if(dataInicioParam == null || dataInicioParam.isEmpty()){
                response.setStatus(400);
                throw new Exception("Data Inicio não informada");
            }

            Date dataInicio = Calendario.getDate("dd/MM/yyyy", dataInicioParam);
            dataInicio = Calendario.inicioDoDia(dataInicio);

            connection = new DAO().obterConexaoEspecifica(chave);
            Conexao.guardarConexaoForJ2SE(chave, connection);


            AjustarAberturaMetaDuplicada.ajustarDuplicacoes(connection, dataInicio);

            envelopeRespostaDTO = EnvelopeRespostaDTO.of("Os itens de meta diaria duplicados foram corrigidos.");
        } catch (Exception e) {
            envelopeRespostaDTO = EnvelopeRespostaDTO.erro("Falha ao corrigir itens de meta duplicados.", e.getMessage());
        }finally {
            finalizarConexao(connection);
        }

        return envelopeRespostaDTO;
    }

    private EnvelopeRespostaDTO consultarTotalContratosPlano(HttpServletRequest request, HttpServletResponse response) {
        Connection connection = null;
        EnvelopeRespostaDTO envelopeRespostaDTO = null;
        try {
            String chave = request.getParameter("chave");
            String planoParam = request.getParameter("plano");
            Integer plano;

            try{
                plano  = Integer.parseInt(planoParam);
            }catch (Exception e){
                response.setStatus(400);
                throw new Exception("Plano inválido");
            }

            if(chave == null || chave.isEmpty()){
                response.setStatus(400);
                throw new Exception("Chave não informada");
            }

            if(plano == null || plano == 0){
                response.setStatus(400);
                throw new Exception("Plano não informado");
            }

            connection = new DAO().obterConexaoEspecifica(chave);
            Conexao.guardarConexaoForJ2SE(chave, connection);

            Contrato contrato = new Contrato(connection);
            Integer totalContratos = contrato.consultarTotalContratos(plano);

            envelopeRespostaDTO = EnvelopeRespostaDTO.of(totalContratos);
        } catch (Exception e) {
            envelopeRespostaDTO = EnvelopeRespostaDTO.erro("Erro ao consultar total de contratos", e.getMessage());
        }finally {
            finalizarConexao(connection);
        }

        return envelopeRespostaDTO;
    }

    private EnvelopeRespostaDTO corrigirFeriasContrato(HttpServletRequest request, HttpServletResponse response) {
        Connection connection = null;
        EnvelopeRespostaDTO envelopeRespostaDTO = null;
        try {
            JSONObject json = new JSONObject(obterBody(request));
            String chave = json.optString("chave");
            Integer plano = json.optInt("plano");

            if(chave == null || chave.isEmpty()){
                response.setStatus(400);
                throw new Exception("Chave não informada");
            }

            if(plano == null || plano == 0){
                response.setStatus(400);
                throw new Exception("Plano não informado");
            }

            connection = new DAO().obterConexaoEspecifica(chave);
            Conexao.guardarConexaoForJ2SE(chave, connection);

            Contrato contrato = new Contrato(connection);
            contrato.alterarCarenciaTodosContratoParaCarenciaQueEstaNoPlano(plano);

            envelopeRespostaDTO = EnvelopeRespostaDTO.of("Todos os contratos foram corrigidos.");
        } catch (Exception e) {
            envelopeRespostaDTO = EnvelopeRespostaDTO.erro("Erro ao substituir produtos", e.getMessage());
        }finally {
            finalizarConexao(connection);
        }

        return envelopeRespostaDTO;
    }

    private EnvelopeRespostaDTO corrigirValoresContratoReciboProdutosPagos(HttpServletRequest request, HttpServletResponse response) {
        Connection connection = null;
        EnvelopeRespostaDTO envelopeRespostaDTO = null;
        try {
            JSONObject json = new JSONObject(obterBody(request));
            String chave = json.optString("chave");

            if(chave == null || chave.isEmpty()){
                response.setStatus(400);
                throw new Exception("Chave não informada");
            }

            connection = new DAO().obterConexaoEspecifica(chave);
            Conexao.guardarConexaoForJ2SE(chave, connection);

            ProdutosPagosServico.contratosViculosZoados(connection);

            envelopeRespostaDTO = EnvelopeRespostaDTO.of("Valores corrigidos");
        } catch (Exception e) {
            envelopeRespostaDTO = EnvelopeRespostaDTO.erro("Erro ao corrigir valores de contrato com recibo e produtos pagos" , e.getMessage());
        }finally {
            finalizarConexao(connection);
        }

        return envelopeRespostaDTO;
    }

    private EnvelopeRespostaDTO substituirProdutos(HttpServletRequest request, HttpServletResponse response) {
        Connection connection = null;
        EnvelopeRespostaDTO envelopeRespostaDTO = null;
        try {
            JSONObject json = new JSONObject(obterBody(request));
            String chave = json.optString("chave");
            int produtoSubstituir = json.optInt("produtoSubstituir");
            int produtoSubstituido = json.optInt("produtoSubstituido");
            boolean deletar = json.optBoolean("deletar");

            if(chave == null || chave.isEmpty()){
                response.setStatus(400);
                throw new Exception("Chave não informada");
            }

            if(produtoSubstituir == 0){
                response.setStatus(400);
                throw new Exception("Produto a substituir não informado");
            }

            if(produtoSubstituido == 0){
                response.setStatus(400);
                throw new Exception("Produto a substituir não informado");
            }

            if(deletar && produtoSubstituir == produtoSubstituido){
                response.setStatus(400);
                throw new Exception("Produto a substituir não pode ser igual ao produto substituido");
            }

            connection = new DAO().obterConexaoEspecifica(chave);
            Conexao.guardarConexaoForJ2SE(chave, connection);

            SubsitituirProdutos.substituir(produtoSubstituir, produtoSubstituido, deletar, connection);

            envelopeRespostaDTO = EnvelopeRespostaDTO.of("Produtos substituidos");
        } catch (Exception e) {
            envelopeRespostaDTO = EnvelopeRespostaDTO.erro("Erro ao substituir produtos" ,e.getMessage());
        }finally {
            finalizarConexao(connection);
        }

        return envelopeRespostaDTO;
    }

    private EnvelopeRespostaDTO corrigirRecibosSemProdutoContaCorrente(HttpServletRequest request, HttpServletResponse response) {
        Connection connection = null;
        EnvelopeRespostaDTO envelopeRespostaDTO = null;
        try {
            JSONObject json = new JSONObject(obterBody(request));
            String chave = json.optString("chave");

            if(chave == null || chave.isEmpty()){
                response.setStatus(400);
                throw new Exception("Chave não informada");
            }

            connection = new DAO().obterConexaoEspecifica(chave);
            Conexao.guardarConexaoForJ2SE(chave, connection);

            new CriarProdutosContaCorrente().executarProcessoRegerarReciboClienteConsultor();

            envelopeRespostaDTO = EnvelopeRespostaDTO.of("Todos os recibos sem produto foram corrigidos.");
        } catch (Exception e) {
            envelopeRespostaDTO = EnvelopeRespostaDTO.erro("Erro ao corrigir recibos sem produto", e.getMessage());
        }finally {
            finalizarConexao(connection);
        }

        return envelopeRespostaDTO;
    }

    private EnvelopeRespostaDTO consultarRecibosSemProdutoContaCorrente(HttpServletRequest request, HttpServletResponse response) {
        Connection connection = null;
        EnvelopeRespostaDTO envelopeRespostaDTO = null;
        try {
            String chave = request.getParameter("chave");

            if(chave == null || chave.isEmpty()){
                response.setStatus(400);
                throw new Exception("Chave não informada");
            }

            connection = new DAO().obterConexaoEspecifica(chave);
            Conexao.guardarConexaoForJ2SE(chave, connection);

            VerificarRecibosPagamentosSemVinculo verificarRecibosPagamentosSemVinculo = new VerificarRecibosPagamentosSemVinculo(connection);
            verificarRecibosPagamentosSemVinculo.obterRecibosSemProdutoContaCorrente();

            envelopeRespostaDTO = EnvelopeRespostaDTO.of(verificarRecibosPagamentosSemVinculo.getRecibosSemProdutoContaCorrente());
        } catch (Exception e) {
            envelopeRespostaDTO = EnvelopeRespostaDTO.erro("Erro ao consultar parcelas em aberto com pagamento", e.getMessage());
        }finally {
            finalizarConexao(connection);
        }

        return envelopeRespostaDTO;
    }

    private EnvelopeRespostaDTO corrigirParcelasEmAbertoComPagamento(HttpServletRequest request, HttpServletResponse response) {
        Connection connection = null;
        EnvelopeRespostaDTO envelopeRespostaDTO = null;
        try {
            JSONObject json = new JSONObject(obterBody(request));
            String chave = json.optString("chave");

            if(chave == null || chave.isEmpty()){
                response.setStatus(400);
                throw new Exception("Chave não informada");
            }

            connection = new DAO().obterConexaoEspecifica(chave);
            Conexao.guardarConexaoForJ2SE(chave, connection);

            new CriarProdutosContaCorrente().executarProcessoRegerarReciboClienteConsultor();

            envelopeRespostaDTO = EnvelopeRespostaDTO.of("Todos os pagamentos com parcelas em aberto foram corrigidos.");
        } catch (Exception e) {
            envelopeRespostaDTO = EnvelopeRespostaDTO.erro("Erro ao corrigir parcelas em aberto com pagamento", e.getMessage());
        }finally {
            finalizarConexao(connection);
        }

        return envelopeRespostaDTO;
    }

    private EnvelopeRespostaDTO consultarParcelasEmAbertoComPagamento(HttpServletRequest request, HttpServletResponse response) {
        Connection connection = null;
        EnvelopeRespostaDTO envelopeRespostaDTO = null;
        try {
            String chave = request.getParameter("chave");

            if(chave == null || chave.isEmpty()){
                response.setStatus(400);
                throw new Exception("Chave não informada");
            }

            connection = new DAO().obterConexaoEspecifica(chave);
            Conexao.guardarConexaoForJ2SE(chave, connection);

            VerificarRecibosPagamentosSemVinculo verificarRecibosPagamentosSemVinculo = new VerificarRecibosPagamentosSemVinculo(connection);
            verificarRecibosPagamentosSemVinculo.obterParcelasEAComPagamento();
            List<VerificarRecibosPagamentosSemVinculo.ClientesProblemasPagamento> parcelasEAComPagamento = verificarRecibosPagamentosSemVinculo.getParcelasEAComPagamento();

            envelopeRespostaDTO = EnvelopeRespostaDTO.of(parcelasEAComPagamento);
        } catch (Exception e) {
            envelopeRespostaDTO = EnvelopeRespostaDTO.erro("Erro ao consultar parcelas em aberto com pagamento", e.getMessage());
        }finally {
            finalizarConexao(connection);
        }

        return envelopeRespostaDTO;
    }

    private EnvelopeRespostaDTO corrigirPagamentoParcelasSemRecibo(ServletRequest request, HttpServletResponse response){
        Connection connection = null;
        EnvelopeRespostaDTO envelopeRespostaDTO = null;
        try {
            JSONObject json = new JSONObject(obterBody(request));
            String chave = json.optString("chave");

            if(chave == null || chave.isEmpty()){
                response.setStatus(400);
                throw new Exception("Chave não informada");
            }

            connection = new DAO().obterConexaoEspecifica(chave);
            Conexao.guardarConexaoForJ2SE(chave, connection);

            VerificarRecibosPagamentosSemVinculo verificarRecibosPagamentosSemVinculo = new VerificarRecibosPagamentosSemVinculo(connection);
            verificarRecibosPagamentosSemVinculo.obterPgtosParcelasSemRecibo();
            verificarRecibosPagamentosSemVinculo.corrigirPgtosParcelasSemRecibo();

            envelopeRespostaDTO = EnvelopeRespostaDTO.of("Todos os pagamentos com parcelas sem recibo foram corrigidos.");
        } catch (Exception e) {
            envelopeRespostaDTO = EnvelopeRespostaDTO.erro("Erro ao corrigir pagamento com parcelas sem recibo", e.getMessage());
        }finally {
            finalizarConexao(connection);
        }

        return envelopeRespostaDTO;
    }

    private EnvelopeRespostaDTO corrigirProdutosSemParcelas(ServletRequest request, HttpServletResponse response){
        Connection connection = null;
        EnvelopeRespostaDTO envelopeRespostaDTO = null;
        try {
            JSONObject json = new JSONObject(obterBody(request));
            String chave = json.optString("chave");

            if(chave == null || chave.isEmpty()){
                response.setStatus(400);
                throw new Exception("Chave não informada");
            }

            connection = new DAO().obterConexaoEspecifica(chave);
            Conexao.guardarConexaoForJ2SE(chave, connection);

            new CorrigirMovProdutos().executarProcesso();
            envelopeRespostaDTO = EnvelopeRespostaDTO.of("Todos os produtos sem parcelas foram corrigidos.");
        } catch (Exception e) {
            response.setStatus(400);
            envelopeRespostaDTO = EnvelopeRespostaDTO.erro("Erro ao corrigir produtos sem parcelas", e.getMessage());
        }finally {
            finalizarConexao(connection);
        }

        return envelopeRespostaDTO;
    }

    private EnvelopeRespostaDTO consultarParcelasSemProdutos(ServletRequest request, HttpServletResponse response){
        Connection connection = null;
        EnvelopeRespostaDTO envelopeRespostaDTO = null;
        try {
            String chave = request.getParameter("chave");

            if(chave == null || chave.isEmpty()){
                response.setStatus(400);
                throw new Exception("Chave não informada");
            }

            connection = new DAO().obterConexaoEspecifica(chave);
            Conexao.guardarConexaoForJ2SE(chave, connection);

            VerificarRecibosPagamentosSemVinculo verificarRecibosPagamentosSemVinculo = new VerificarRecibosPagamentosSemVinculo(connection);
            verificarRecibosPagamentosSemVinculo.obterMovparcelasSemMovprodutos();

            envelopeRespostaDTO = EnvelopeRespostaDTO.of(verificarRecibosPagamentosSemVinculo.getMovparcelasSemMovprodutos());
        } catch (Exception e) {
            response.setStatus(400);
            envelopeRespostaDTO = EnvelopeRespostaDTO.erro("Erro ao consultar parcelas sem produtos", e.getMessage());
        }finally {
            finalizarConexao(connection);
        }

        return envelopeRespostaDTO;
    }

    private EnvelopeRespostaDTO consultarProdutosSemParcelas(ServletRequest request, HttpServletResponse response){
        Connection connection = null;
        EnvelopeRespostaDTO envelopeRespostaDTO = null;
        try {
            String chave = request.getParameter("chave");

            if(chave == null || chave.isEmpty()){
                response.setStatus(400);
                throw new Exception("Chave não informada");
            }

            connection = new DAO().obterConexaoEspecifica(chave);
            Conexao.guardarConexaoForJ2SE(chave, connection);

            VerificarRecibosPagamentosSemVinculo verificarRecibosPagamentosSemVinculo = new VerificarRecibosPagamentosSemVinculo(connection);
            verificarRecibosPagamentosSemVinculo.obterMovprodutosSemMovparcelas();

            envelopeRespostaDTO = EnvelopeRespostaDTO.of(verificarRecibosPagamentosSemVinculo.getMovprodutosSemMovparcelas());
        } catch (Exception e) {
            response.setStatus(400);
            envelopeRespostaDTO = EnvelopeRespostaDTO.erro("Erro ao consultar produtos sem parcelas", e.getMessage());
        }finally {
            finalizarConexao(connection);
        }

        return envelopeRespostaDTO;
    }

    private EnvelopeRespostaDTO corrigirParcelasSemProdutos(ServletRequest request, HttpServletResponse response){
        Connection connection = null;
        EnvelopeRespostaDTO envelopeRespostaDTO = null;
        try {
            JSONObject json = new JSONObject(obterBody(request));
            String chave = json.optString("chave");

            if(chave == null || chave.isEmpty()){
                response.setStatus(400);
                throw new Exception("Chave não informada");
            }

            connection = new DAO().obterConexaoEspecifica(chave);
            Conexao.guardarConexaoForJ2SE(chave, connection);

            String erros = new CorrigirMovParcela().executarProcesso(true);

            if(erros != null){
                throw new Exception(erros);
            }

            envelopeRespostaDTO = EnvelopeRespostaDTO.of("Todas as parcelas sem produtos foram corrigidas.");
        } catch (Exception e) {
            response.setStatus(400);
            envelopeRespostaDTO = EnvelopeRespostaDTO.erro("Erro ao corrigir parcelas sem produtos", e.getMessage());
        }finally {
            finalizarConexao(connection);
        }

        return envelopeRespostaDTO;
    }


    private EnvelopeRespostaDTO corrigirPagamentoSemProdutoPago(ServletRequest request, HttpServletResponse response){
        Connection connection = null;
        EnvelopeRespostaDTO envelopeRespostaDTO = null;
        try {
            JSONObject json = new JSONObject(obterBody(request));
            String chave = json.optString("chave");

            if(chave == null || chave.isEmpty()){
                response.setStatus(400);
                throw new Exception("Chave não informada");
            }

            connection = new DAO().obterConexaoEspecifica(chave);
            Conexao.guardarConexaoForJ2SE(chave, connection);

            VerificarRecibosPagamentosSemVinculo verificarRecibosPagamentosSemVinculo = new VerificarRecibosPagamentosSemVinculo(connection);
            verificarRecibosPagamentosSemVinculo.obterPgtosSemProdutosPagos();
            verificarRecibosPagamentosSemVinculo.corrigirPagementosSemProdutosPagos();

            envelopeRespostaDTO = EnvelopeRespostaDTO.of("Todos os pagamentos sem produtos pagos foram corrigidos.");
        } catch (Exception e) {
            response.setStatus(400);
            envelopeRespostaDTO = EnvelopeRespostaDTO.erro("Erro ao corrigir pagamentos sem produtos pagos", e.getMessage());
        }finally {
            finalizarConexao(connection);
        }

        return envelopeRespostaDTO;
    }

    private EnvelopeRespostaDTO consultarPagamentoSemVinculoParcela(ServletRequest request, HttpServletResponse response){
        Connection connection = null;
        EnvelopeRespostaDTO envelopeRespostaDTO = null;
        try {
            String chave = request.getParameter("chave");

            if(chave == null || chave.isEmpty()){
                response.setStatus(400);
                throw new Exception("Chave não informada");
            }

            connection = new DAO().obterConexaoEspecifica(chave);
            Conexao.guardarConexaoForJ2SE(chave, connection);

            VerificarRecibosPagamentosSemVinculo verificarRecibosPagamentosSemVinculo = new VerificarRecibosPagamentosSemVinculo(connection);
            verificarRecibosPagamentosSemVinculo.obterPgtosSemVinculoParcela();

            envelopeRespostaDTO = EnvelopeRespostaDTO.of(verificarRecibosPagamentosSemVinculo.getPgtosSemVinculosParcelas());
        } catch (Exception e) {
            response.setStatus(400);
            envelopeRespostaDTO = EnvelopeRespostaDTO.erro("Erro ao consultar pagamento sem vinculos de parcelas", e.getMessage());
        }finally {
            finalizarConexao(connection);
        }

        return envelopeRespostaDTO;
    }


    private EnvelopeRespostaDTO consultarPagamentoSemProdutoPago(ServletRequest request, HttpServletResponse response){
        Connection connection = null;
        EnvelopeRespostaDTO envelopeRespostaDTO = null;
        try {
            String chave = request.getParameter("chave");

            if(chave == null || chave.isEmpty()){
                response.setStatus(400);
                throw new Exception("Chave não informada");
            }

            connection = new DAO().obterConexaoEspecifica(chave);
            Conexao.guardarConexaoForJ2SE(chave, connection);

            VerificarRecibosPagamentosSemVinculo verificarRecibosPagamentosSemVinculo = new VerificarRecibosPagamentosSemVinculo(connection);
            verificarRecibosPagamentosSemVinculo.obterPgtosSemProdutosPagos();

            envelopeRespostaDTO = EnvelopeRespostaDTO.of(verificarRecibosPagamentosSemVinculo.getPgtosSemProdutosPagos());
        } catch (Exception e) {
            response.setStatus(400);
            envelopeRespostaDTO = EnvelopeRespostaDTO.erro("Erro ao consultar pagamento sem produto pago", e.getMessage());
        }finally {
            finalizarConexao(connection);
        }

        return envelopeRespostaDTO;
    }


    private EnvelopeRespostaDTO consultarPagamentoParcelasSemRecibo(ServletRequest request, HttpServletResponse response){
        Connection connection = null;
        EnvelopeRespostaDTO envelopeRespostaDTO = null;
        try {
            String chave = request.getParameter("chave");

            if(chave == null || chave.isEmpty()){
                response.setStatus(400);
                throw new Exception("Chave não informada");
            }

            connection = new DAO().obterConexaoEspecifica(chave);
            Conexao.guardarConexaoForJ2SE(chave, connection);

            VerificarRecibosPagamentosSemVinculo verificarRecibosPagamentosSemVinculo = new VerificarRecibosPagamentosSemVinculo(connection);
            verificarRecibosPagamentosSemVinculo.obterPgtosParcelasSemRecibo();

            envelopeRespostaDTO = EnvelopeRespostaDTO.of(verificarRecibosPagamentosSemVinculo.getPgtosParcelasSemRecibo());
        } catch (Exception e) {
            response.setStatus(500);
            envelopeRespostaDTO = EnvelopeRespostaDTO.erro("Erro ao consultar pagamento com parcelas sem recibo", e.getMessage());
        }finally {
            finalizarConexao(connection);
        }

        return envelopeRespostaDTO;
    }


    private EnvelopeRespostaDTO reprocessarExtratos(ServletRequest request, HttpServletResponse response){
        Connection connection = null;
        EnvelopeRespostaDTO envelopeRespostaDTO = null;
        try {
            JSONObject json = new JSONObject(obterBody(request));
            String chave = json.optString("chave");
            String dataInicioParam = json.optString("dataInicio");
            String dataFimParam = json.optString("dataFim");

            if(chave == null || chave.isEmpty()){
                response.setStatus(400);
                throw new Exception("Chave não informada");
            }

            if(dataInicioParam == null || dataInicioParam.isEmpty()){
                response.setStatus(400);
                throw new Exception("Data Inicio não informada");
            }

            if(dataFimParam == null || dataFimParam.isEmpty()){
                response.setStatus(400);
                throw new Exception("Data Fim não informada");
            }

            Date dataInicio = Calendario.getDate("dd/MM/yyyy", dataInicioParam);
            dataInicio = Calendario.inicioDoDia(dataInicio);
            Date dataFim = Calendario.getDate("dd/MM/yyyy", dataFimParam);
            dataFim = Calendario.fimDoDia(dataFim);

            if (Calendario.diferencaEmDias(dataInicio, dataFim) > 31) {
                throw new Exception("Período não deve ter mais que 31 dias!");
            }

            connection = new DAO().obterConexaoEspecifica(chave);
            Conexao.guardarConexaoForJ2SE(chave, connection);
            ReprocessarExtratos reprocessarExtratos = new ReprocessarExtratos(dataInicio, dataFim);
            reprocessarExtratos.reprocessarExtratos(chave);

            envelopeRespostaDTO = EnvelopeRespostaDTO.of(
                    "Extratos reprocessados com sucesso. Iniciado: " +
                            Calendario.getData(dataInicio, "dd/MM/yyyy HH:mm:ss") + "" +
                            ", Finalizado: " + Calendario.getData(dataFim, "dd/MM/yyyy HH:mm:ss"));
        } catch (Exception e) {
            response.setStatus(500);
            envelopeRespostaDTO = EnvelopeRespostaDTO.erro("Erro ao reprocessar extrato", e.getMessage() );
        }finally {
            finalizarConexao(connection);
        }

        return envelopeRespostaDTO;
    }

    private EnvelopeRespostaDTO validarUsoModuloNotaFiscal(HttpServletRequest request) throws Exception {
        Connection con = null;

        try {
            JSONObject json = new JSONObject(obterBody(request));

            String chave = json.optString("chave");
            Integer empresa = json.optInt("codigoEmpresa");
            Integer codigoUsuario = json.optInt("codigoUsuario");

            boolean utilizaEnotas = false;
            boolean possuiPermissaoModuloNotas = false;
            boolean funcionamentoParcial = false;
            if (UteisValidacao.emptyString(chave)) {
                throw new Exception("Chave informada");
            }

            try {
                con = obterConexao(request, chave);
            } catch (Exception ex) {
                ex.printStackTrace();
                return EnvelopeRespostaDTO.of(false);
            }

            StringBuilder sql = new StringBuilder();
            sql.append("select \n");
            sql.append("exists(select codigo from configuracaonotafiscal  where enotas = true ");
            if (!UteisValidacao.emptyNumber(empresa)) {
                sql.append(" and empresa = ").append(empresa);
            }
            sql.append(" limit 1) as enotas \n");

            try (PreparedStatement ps = con.prepareStatement(sql.toString())) {
                try (ResultSet rs = ps.executeQuery()) {
                    if (rs.next()) {
                        utilizaEnotas = rs.getBoolean("enotas");
                    }
                }
            }

            if (utilizaEnotas) {
                String sbPermissaoAcessoModuloNotas = "SELECT EXISTS( SELECT u.codigo FROM usuarioperfilacesso u\n" +
                        "INNER JOIN perfilacesso pa ON pa.codigo = u.perfilacesso\n" +
                        "INNER JOIN permissao p ON p.codperfilacesso = pa.codigo\n" +
                        "WHERE u.usuario = " + codigoUsuario + "\n" +
                        "AND u.empresa = " + empresa + "\n" +
                        "AND p.tituloapresentacao ILIKE '4.41 - Acesso ao módulo de Notas') as acessarmodulonotas";

                try (PreparedStatement ps = con.prepareStatement(sbPermissaoAcessoModuloNotas)) {
                    try (ResultSet rs = ps.executeQuery()) {
                        if (rs.next()) {
                            possuiPermissaoModuloNotas = rs.getBoolean("acessarmodulonotas");
                        }
                    }
                }

            }

            return EnvelopeRespostaDTO.of(!funcionamentoParcial && utilizaEnotas && possuiPermissaoModuloNotas);
        } catch (Exception ex) {
            ex.printStackTrace();
            throw ex;
        } finally {
            finalizarConexao(con);
        }
    }

    private EnvelopeRespostaDTO informacoesDaraExpiracao(HttpServletRequest request) throws Exception {
        Connection con = null;
        try {
            JSONObject json = new JSONObject(obterBody(request));
            String chave = json.optString("chave");
            if (UteisValidacao.emptyString(chave)) {
                throw new Exception("Chave informada");
            }

            con = obterConexao(request, chave);
            return EnvelopeRespostaDTO.of(processaoInfoBloqueio(json, con, chave));
        } catch (Exception ex) {
            ex.printStackTrace();
            throw ex;
        } finally {
            finalizarConexao(con);
        }
    }

    private InfoBloqueioDTO processaoInfoBloqueio(JSONObject json, Connection con, String chave) throws Exception {
        try {

            Integer empresa = json.optInt("codigoEmpresa");
            Integer codigoUsuario = json.optInt("codigoUsuario");
            String username = json.getString("username");


            StringBuilder sql = new StringBuilder();
            sql.append("select  e.codigo as codigoempresa, e.datasuspensao,  \n");
            sql.append("e.total_dias_solicitados, e.concessao_dia_extra, \n");
            sql.append("e.dataexpiracao, e.cod_empresafinanceiro, e.nome, pf.tipo as tipoperfil, u.administrador \n");
            sql.append("from usuario u \n");
            sql.append("left join usuarioperfilacesso up on up.usuario = u.codigo \n");
            sql.append("left join perfilacesso pf on pf.codigo = up.perfilacesso  \n");
            sql.append("left join empresa e on e.codigo = up.empresa  \n");
            sql.append(" where u.codigo = ").append(codigoUsuario);
            sql.append(" and (e.codigo = ").append(empresa);
            sql.append(" or e.codigo is null)");

            InfoBloqueioDTO infoBloqueioDTO = new InfoBloqueioDTO();
            infoBloqueioDTO.setChave(chave);
            infoBloqueioDTO.setCodigoEmpresa(empresa);
            boolean dadosProcesados = false;
            try (PreparedStatement ps = con.prepareStatement(sql.toString())) {
                try (ResultSet rs = ps.executeQuery()) {
                    if (rs.next()) {
                        if (UteisValidacao.notEmptyNumber(rs.getInt("codigoempresa"))) {
                            infoBloqueioDTO.setPerfilAdministrador(PerfilUsuarioEnum.ADMINISTRADOR.getId() == rs.getInt("tipoperfil"));
                            processarValidacaoDatas(rs, infoBloqueioDTO, username);
                            try {
                                infoBloqueioDTO.setTipoPerfilAcesso(PerfilUsuarioEnum.getFromOrdinal(rs.getInt("tipoperfil")).getNome());
                            } catch (Exception ex) {
                                ex.printStackTrace();
                            }
                            dadosProcesados = true;
                        } else if (rs.getBoolean("administrador")) {
                            dadosProcesados = true;
                        }
                    }
                }
            }
            if (!dadosProcesados) {
                StringBuilder sqlEmpresa = new StringBuilder();
                sqlEmpresa.append("select  e.codigo as codigoempresa, e.datasuspensao,e.nome, \n");
                sqlEmpresa.append("e.total_dias_solicitados, e.concessao_dia_extra, \n");
                sqlEmpresa.append("e.dataexpiracao, e.cod_empresafinanceiro \n");
                sqlEmpresa.append("from empresa e \n");
                sqlEmpresa.append(" where e.codigo = ").append(empresa);
                try (PreparedStatement ps = con.prepareStatement(sqlEmpresa.toString())) {
                    try (ResultSet rs = ps.executeQuery()) {
                        if (rs.next()) {
                            infoBloqueioDTO.setPerfilAdministrador(false);
                            processarValidacaoDatas(rs, infoBloqueioDTO, username);
                        }
                    }
                }
            }

            return infoBloqueioDTO;
        } catch (Exception ex) {
            ex.printStackTrace();
            throw ex;
        } finally {
            finalizarConexao(con);
        }
    }


    private void processarValidacaoDatas(ResultSet rs, InfoBloqueioDTO infoBloqueioDTO, final String username) throws SQLException {
        Date meioDia = Calendario.getDataComHora(Calendario.hoje(), "12:00:00");
        Date dataSuspensao = rs.getDate("datasuspensao");
        infoBloqueioDTO.setDiasParaBloqueio(-1);
        if (dataSuspensao != null &&
                (Calendario.menor(dataSuspensao, Calendario.hoje()) || (Calendario.igual(dataSuspensao, Calendario.hoje()) && Calendario.menorOuIgualComHora(meioDia, Calendario.hoje())))) {
            infoBloqueioDTO.setSistemaSuspenso(true);
            if (infoBloqueioDTO.getPerfilAdministrador()) {
                EmpresaVO empresaVO = new EmpresaVO();
                empresaVO.setCodigo(rs.getInt("codigoempresa"));
                empresaVO.setCodEmpresaFinanceiro(rs.getInt("cod_empresafinanceiro"));
                empresaVO.setNome(rs.getString("nome"));
                if (username != null) {
                    infoBloqueioDTO.setLinkWhatsapp(LoginControle.obterLinkWhatsFinanceiro(empresaVO, username));
                }
            }
        } else {
            Date dataExpiracao = rs.getDate("dataexpiracao");
            if (dataExpiracao != null) {
                if (Calendario.menor(dataExpiracao, Calendario.hoje()) || (Calendario.igual(dataExpiracao, Calendario.hoje()) && Calendario.menorOuIgualComHora(meioDia, Calendario.hoje()))) {
                    Date dataConcessao = rs.getDate("concessao_dia_extra");
                    if (dataConcessao == null || Calendario.menor(dataConcessao, dataExpiracao) || (Calendario.menor(dataConcessao, Calendario.hoje()) || (Calendario.igual(dataConcessao, Calendario.hoje()) && Calendario.menorOuIgualComHora(meioDia, Calendario.hoje())))) {
                        infoBloqueioDTO.setSistemaBloqueado(true);
                    } else {
                        infoBloqueioDTO.setFuncionamentoParcial(true);
                        Long l = Uteis.nrDiasEntreDatas(Calendario.hoje(), dataConcessao != null && Calendario.menor(dataExpiracao, dataConcessao) ? dataConcessao : dataExpiracao);
                        if (l != null && (l.intValue() <= 0 || l.intValue() <= 5)) {
                            if (l.intValue() < 0) {
                                l = 0L;
                            }
                            infoBloqueioDTO.setDiasParaBloqueio(l.intValue());
                        }
                    }
                } else {
                    Long l = Uteis.nrDiasEntreDatas(Calendario.hoje(), dataExpiracao);
                    if (l != null && (l.intValue() <= 0 || l.intValue() <= 5)) {
                        if (l.intValue() < 0) {
                            l = 0L;
                        }
                        infoBloqueioDTO.setDiasParaBloqueio(l.intValue());
                    }
                }
            }
        }
    }

    private String obterBody(ServletRequest request) throws Exception {
        StringBuffer body = new StringBuffer();
        try {
            InputStream inputStream = request.getInputStream();
            BufferedReader reader = new BufferedReader(new InputStreamReader(inputStream, StandardCharsets.UTF_8));
            String line = null;

            while ((line = reader.readLine()) != null) {
                body.append(line);
            }
            return body.toString();
        } catch (Exception ex) {
            ex.printStackTrace();
            throw new Exception("Erro body: " + ex.getMessage());
        }
    }

    private Connection obterConexao(ServletRequest request, String chave) throws Exception {
        if (!UteisValidacao.emptyString(chave)) {
            return new DAO().obterConexaoEspecifica(chave.trim());
        }

        String key = request.getParameter("key");
        if (UteisValidacao.emptyString(key)) {
            throw new Exception("Chave não informada.");
        }
        return new DAO().obterConexaoEspecifica(key.trim());
    }

    private void finalizarConexao(Connection con) {
        try {
            if (con != null) {
                con.close();
            }
        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }

    private EnvelopeRespostaDTO enviarEmailRedefinirSenha(ServletRequest request) throws Exception {
        try {
            JSONObject json = new JSONObject(obterBody(request));
            String email = json.getString("email");
            String nomeCliente = json.getString("nome");
            String urlLink = json.getString("link");
            String empresa = json.getString("empresa");

            EmailNovoLoginService service = new EmailNovoLoginService();
            service.enviarEmailRedefinirSenha(email, nomeCliente, empresa, urlLink);
            return EnvelopeRespostaDTO.of("ok");
        } catch (Exception ex) {
            ex.printStackTrace();
            throw ex;
        }
    }

    private EnvelopeRespostaDTO enviarEmailBoasVindas(ServletRequest request) throws Exception {
        try {
            JSONObject json = new JSONObject(obterBody(request));
            String email = json.getString("email");
            String nomeCliente = json.getString("nome");
            String nomeEmpresa = json.getString("empresa");
            String urlLink = json.getString("link");

            EmailNovoLoginService service = new EmailNovoLoginService();
            service.enviarEmailBoasVindas(email, nomeCliente, nomeEmpresa, urlLink);
            return EnvelopeRespostaDTO.of("ok");
        } catch (Exception ex) {
            ex.printStackTrace();
            throw ex;
        }
    }

    private EnvelopeRespostaDTO enviarEmailCodigoEmail(ServletRequest request) throws Exception {
        try {
            JSONObject json = new JSONObject(obterBody(request));
            String email = json.getString("email");
            String nomeCliente = json.getString("nome");
            String codigoVerificacao = json.getString("codigoVerificacao");
            String urlLink = json.optString("link");
            String empresa = json.optString("empresa");
            Boolean trocaEmailVincularDados = json.optBoolean("trocaEmailVincularDados");
            boolean enviarLink = (json.optBoolean("enviarLink") && !UteisValidacao.emptyString(urlLink));

            EmailNovoLoginService service = new EmailNovoLoginService();
            service.enviarEmailCodigoVerificacao(email, nomeCliente, empresa, codigoVerificacao, urlLink, enviarLink, trocaEmailVincularDados);
            return EnvelopeRespostaDTO.of("ok");
        } catch (Exception ex) {
            ex.printStackTrace();
            throw ex;
        }
    }

    private EnvelopeRespostaDTO enviarEmailAtivarUsuario(ServletRequest request) throws Exception {
        try {
            JSONObject json = new JSONObject(obterBody(request));
            String email = json.getString("email");
            String nomeCliente = json.getString("nome");
            String urlLink = json.getString("link");
            String empresa = json.optString("empresa");

            EmailNovoLoginService service = new EmailNovoLoginService();
            service.enviarEmailAtivarConta(email, nomeCliente, empresa, urlLink);
            return EnvelopeRespostaDTO.of("ok");
        } catch (Exception ex) {
            ex.printStackTrace();
            throw ex;
        }
    }

    private EnvelopeRespostaDTO alterarSenhaUsuario(ServletRequest request) throws Exception {
        Usuario usuarioDAO;
        UsuarioEmail usuarioEmailDAO;
        UsuarioMovel usuarioMovelDAO;
        Connection con = null;
        try {
            JSONObject json = new JSONObject(obterBody(request));

            String chave = json.optString("chave");
            String username = json.optString("username");
            Integer usuario_zw = json.optInt("usuario_zw");
            Integer usuario_tw = json.optInt("usuario_tw");
            String senha_cripto = json.optString("senha_cripto");

            con = obterConexao(request, chave);
            usuarioDAO = new Usuario(con);
            usuarioMovelDAO = new UsuarioMovel(con);
            usuarioEmailDAO = new UsuarioEmail(con);

            UsuarioVO usuarioVO = usuarioDAO.consultarPorChavePrimaria(usuario_zw, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);

            //alterar senha do usuario
            usuarioDAO.alterarSenhaUsuario(usuarioVO, true, senha_cripto);
            usuarioVO = usuarioDAO.consultarPorChavePrimaria(usuario_zw, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
            UsuarioEmailVO usuarioEmailVO = usuarioEmailDAO.consultarPorUsuario(usuarioVO.getCodigo());

            //alterar usuario movel e sincronizar treino
            UsuarioMovelVO usuarioMovelVO = usuarioMovelDAO.consultarPorUsuario(usuarioVO, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
            if (usuarioMovelVO != null && !UteisValidacao.emptyNumber(usuarioMovelVO.getCodigo())) {
                usuarioMovelDAO.alterarSenha(usuarioMovelVO.getCodigo(), "", "", senha_cripto);
            }
            adicionarUsuarioServicoDescobrir(usuarioVO, usuarioEmailVO, chave);
            return EnvelopeRespostaDTO.of("ok");
        } catch (Exception ex) {
            ex.printStackTrace();
            throw ex;
        } finally {
            usuarioDAO = null;
            usuarioMovelDAO = null;
            finalizarConexao(con);
        }
    }

    private void adicionarUsuarioServicoDescobrir(UsuarioVO usuarioVO, UsuarioEmailVO usuarioEmailVO, String key) {
        try {
            if (!UteisValidacao.emptyString(usuarioEmailVO.getEmail())) {
                String url = String.format("%s/prest/usuarioapp/%s/v3/gerarUsuarioRedeEmpresa",
                        PropsService.getPropertyValue(PropsService.urlOamd),
                        key);
                Map<String, String> params = new HashMap<>();
                params.put("email", usuarioEmailVO.getEmail());
                params.put("cpf", usuarioVO.getColaboradorVO().getPessoa().getCfp());
                params.put("telefone", usuarioVO.getColaboradorVO().getPessoa().getTelefones());
                params.put("dataNascimento", Calendario.getDataAplicandoFormatacao(usuarioVO.getColaboradorVO().getPessoa().getDataNasc(), "dd/MM/yyyy"));
                params.put("senha", usuarioVO.getSenha());
                String result = ExecuteRequestHttpService.executeRequest(url, params, false, "utf-8");
                if (result.contains("erro")) {
                    throw new Exception(new JSONObject(result).optString("erro"));
                }
            }
        } catch (Exception e) {
            Uteis.logar("Erro ao enviar o usuarioapp para aplicação OAMD");
            Uteis.logar(e, UsuarioControle.class);
        }
    }

    private EnvelopeRespostaDTO alterarEmailUsuario(ServletRequest request) throws Exception {
        Usuario usuarioDAO;
        UsuarioEmail usuarioEmailDAO;
        Email emailDAO;
        Pessoa pessoaDAO;
        Connection con = null;
        try {
            JSONObject json = new JSONObject(obterBody(request));

            String chave = json.optString("chave");
            String username = json.optString("username");
            Integer usuario_zw = json.optInt("usuario_zw");
            Integer usuario_tw = json.optInt("usuario_tw");
            String email = json.optString("email");

            con = obterConexao(request, chave);
            usuarioDAO = new Usuario(con);
            usuarioEmailDAO = new UsuarioEmail(con);
            emailDAO = new Email(con);
            pessoaDAO = new Pessoa(con);

            UsuarioVO usuarioVO = usuarioDAO.consultarPorChavePrimaria(usuario_zw, Uteis.NIVELMONTARDADOS_METACOLABORADORRESPONSAVEL);
            UsuarioEmailVO usuarioEmailVO = usuarioEmailDAO.consultarPorUsuario(usuarioVO.getCodigo());

            if (!UteisValidacao.emptyNumber(usuarioVO.getColaboradorVO().getCodigo())) {
                List<EmailVO> emailsColaborador = new ArrayList<>();
                if (!UteisValidacao.emptyString(usuarioEmailVO.getEmail())) {
                    emailsColaborador = emailDAO.consultarEmailExiste(usuarioEmailVO.getEmail(), null, usuarioVO.getColaboradorVO().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                }
                if (emailsColaborador.isEmpty()) {
                    EmailVO emailVO = new EmailVO();
                    emailVO.setPessoa(pessoaDAO.obterPessoaColaborador(usuarioVO.getColaboradorVO().getCodigo()));
                    emailVO.getPessoaVO().setCodigo(emailVO.getPessoa());
                    emailVO.setEmail(email);
                    emailDAO.incluir(emailVO);
                } else {
                    for (EmailVO emailVO : emailsColaborador) {
                        emailVO.setEmail(email);
                        emailDAO.alterar(emailVO);
                    }
                }
            }

            usuarioEmailVO.setUsuario(usuarioVO.getCodigo());
            usuarioEmailVO.setUsuarioVO(usuarioVO);
            usuarioEmailVO.setEmail(email);
            usuarioEmailVO.setVerificado(true);
            try {
                usuarioEmailDAO.gravar(usuarioEmailVO);
            } catch (Exception e) {
                usuarioEmailDAO.excluir(usuarioEmailVO);
                usuarioEmailVO.setCodigo(null);
                usuarioEmailDAO.gravar(usuarioEmailVO);
            }
            usuarioVO.setEmail(email);
            usuarioDAO.adicionarUsuarioServicoDescobrir(chave, usuarioVO);

            return EnvelopeRespostaDTO.of("ok");
        } catch (Exception ex) {
            ex.printStackTrace();
            throw ex;
        } finally {
            usuarioDAO = null;
            usuarioEmailDAO = null;
            emailDAO = null;
            pessoaDAO = null;
            finalizarConexao(con);
        }
    }

    private EnvelopeRespostaDTO alterarTelefoneUsuario(ServletRequest request) throws Exception {
        Usuario usuarioDAO;
        UsuarioTelefone usuarioTelefoneDAO;
        Telefone telefoneDAO;
        Pessoa pessoaDAO;
        Connection con = null;
        try {
            JSONObject json = new JSONObject(obterBody(request));

            String chave = json.optString("chave");
            String username = json.optString("username");
            Integer usuario_zw = json.optInt("usuario_zw");
            Integer usuario_tw = json.optInt("usuario_tw");
            String telefone = json.optString("telefone");

            con = obterConexao(request, chave);
            usuarioDAO = new Usuario(con);
            usuarioTelefoneDAO = new UsuarioTelefone(con);
            telefoneDAO = new Telefone(con);
            pessoaDAO = new Pessoa(con);

            UsuarioVO usuarioVO = usuarioDAO.consultarPorChavePrimaria(usuario_zw, Uteis.NIVELMONTARDADOS_METACOLABORADORRESPONSAVEL);
            UsuarioTelefoneVO usuarioTelefoneVO = usuarioTelefoneDAO.consultarPorUsuario(usuarioVO.getCodigo());

            //dynamo sempre manda no formato "DDI|NUMERO_TELEFONE" somente n?meros
            String[] tel = telefone.split("\\|"); //dynamo salva "DDI|NUMERO_TELEFONE"
            String novoDDI = tel[0];
            String novoTelefone = tel[1];

            if (!UteisValidacao.emptyNumber(usuarioVO.getColaboradorVO().getCodigo())) {
                List<TelefoneVO> telefonesColaborador = new ArrayList<>();
                if (!UteisValidacao.emptyString(usuarioTelefoneVO.getNumero())) {
                    telefonesColaborador = telefoneDAO.consultarTelefoneExiste(usuarioTelefoneVO.getNumero(), null, usuarioVO.getColaboradorVO().getCodigo(), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
                }
                if (telefonesColaborador.isEmpty()) {
                    TelefoneVO telefoneVO = new TelefoneVO();
                    telefoneVO.setPessoa(pessoaDAO.obterPessoaColaborador(usuarioVO.getColaboradorVO().getCodigo()));
                    telefoneVO.setDdi(novoDDI);
                    telefoneVO.setNumero(Formatador.formataTelefoneZW(novoTelefone));
                    telefoneVO.setTipoTelefone(TipoTelefoneEnum.CELULAR.getCodigo());
                    telefoneDAO.incluir(telefoneVO);
                } else {
                    for (TelefoneVO telefoneVO : telefonesColaborador) {
                        telefoneVO.setDdi(novoDDI);
                        telefoneVO.setNumero(Formatador.formataTelefoneZW(telefone));
                        telefoneVO.setTipoTelefone(TipoTelefoneEnum.CELULAR.getCodigo());
                        telefoneDAO.alterar(telefoneVO);
                    }
                }
            }

            usuarioTelefoneVO.setUsuario(usuarioVO.getCodigo());
            usuarioTelefoneVO.setUsuarioVO(usuarioVO);
            usuarioTelefoneVO.setDdi(novoDDI);
            usuarioTelefoneVO.setNumero(Formatador.formataTelefoneZW(novoTelefone));
            usuarioTelefoneVO.setVerificado(true);
            usuarioTelefoneDAO.gravar(usuarioTelefoneVO);

            return EnvelopeRespostaDTO.of("ok");
        } catch (Exception ex) {
            ex.printStackTrace();
            throw ex;
        } finally {
            usuarioDAO = null;
            usuarioTelefoneDAO = null;
            telefoneDAO = null;
            pessoaDAO = null;
            finalizarConexao(con);
        }
    }

    private EnvelopeRespostaDTO alterarUsuarioGeral(ServletRequest request) throws Exception {
        Usuario usuarioDAO;
        Connection con = null;
        try {
            JSONObject json = new JSONObject(obterBody(request));

            String chave = json.optString("chave");
            String username = json.optString("username");
            Integer usuario_zw = json.optInt("usuario_zw");
            Integer usuario_tw = json.optInt("usuario_tw");
            String usuarioGeral = json.optString("usuarioGeral");

            con = obterConexao(request, chave);
            usuarioDAO = new Usuario(con);

            UsuarioVO usuarioVO = usuarioDAO.consultarPorChavePrimaria(usuario_zw, Uteis.NIVELMONTARDADOS_METACOLABORADORRESPONSAVEL);
            usuarioVO.setUsuarioGeral(usuarioGeral);
            usuarioDAO.alterarUsuarioGeral(usuarioVO);
            return EnvelopeRespostaDTO.of("ok");
        } catch (Exception ex) {
            ex.printStackTrace();
            throw ex;
        } finally {
            usuarioDAO = null;
            finalizarConexao(con);
        }
    }

    private EnvelopeRespostaDTO sincronizarUsuarioRedeEmpresa(ServletRequest request) throws Exception {
        Usuario usuarioDAO;
        UsuarioEmail usuarioEmailDAO;
        UsuarioTelefone usuarioTelefoneDAO;
        Connection con = null;
        try {
            JSONObject json = new JSONObject(obterBody(request));

            String chave = json.optString("chave");
            String usuarioGeral = json.optString("usuarioGeral");
            String email = json.optString("email");
            String telefone = json.optString("telefone");

            if (UteisValidacao.emptyString(usuarioGeral)) {
                throw new Exception("UsuarioGeral não informado");
            }

            con = obterConexao(request, chave);
            usuarioDAO = new Usuario(con);
            usuarioEmailDAO = new UsuarioEmail(con);
            usuarioTelefoneDAO = new UsuarioTelefone(con);

            Integer codUsuario;
            if (!UteisValidacao.emptyString(email)) {
                UsuarioEmailVO usuarioEmailVO = usuarioEmailDAO.consultarPorEmail(email);
                if (UteisValidacao.emptyString(usuarioEmailVO.getEmail())) {
                    throw new Exception("Usuário não encontrado com o e-mail " + email);
                }
                codUsuario = usuarioEmailVO.getUsuario();
            } else if (!UteisValidacao.emptyString(telefone)) {

                //dynamo sempre manda no formato "DDI|NUMERO_TELEFONE" somente n?meros
                String[] tel = telefone.split("\\|"); //dynamo salva "DDI|NUMERO_TELEFONE"
                String novoDDI = tel[0];
                String novoTelefone = tel[1];

                UsuarioTelefoneVO usuarioTelefoneVO = usuarioTelefoneDAO.consultarPorNumero(novoTelefone);
                if (UteisValidacao.emptyString(usuarioTelefoneVO.getNumero())) {
                    throw new Exception("Usuário não encontrado com o telefone " + telefone);
                }
                codUsuario = usuarioTelefoneVO.getUsuario();
            } else {
                throw new Exception("E-mail ou telefone não informado");
            }

            if (UteisValidacao.emptyNumber(codUsuario)) {
                throw new Exception("Usuário não encontrado");
            }

            UsuarioVO usuarioVO = usuarioDAO.consultarPorChavePrimaria(codUsuario, Uteis.NIVELMONTARDADOS_METACOLABORADORRESPONSAVEL);
            if (!UteisValidacao.emptyString(usuarioVO.getUsuarioGeral())) {
                throw new Exception("Usuário encontrado já tem um usuário geral " + usuarioVO.getUsuarioGeral());
            }

            usuarioVO.setUsuarioGeral(usuarioGeral);
            usuarioDAO.alterarUsuarioGeral(usuarioVO);

            SincronizarUsuarioNovoLogin.atualizarUsuarioGeral(usuarioVO.getCodigo(), con,
                    null, usuarioDAO.getUsuarioRecorrencia(), "", true);

            return EnvelopeRespostaDTO.of("ok");
        } catch (Exception ex) {
            ex.printStackTrace();
            throw ex;
        } finally {
            usuarioDAO = null;
            usuarioEmailDAO = null;
            usuarioTelefoneDAO = null;
            finalizarConexao(con);
        }
    }

    //todo ajustar para registrar log das altera??es
    private void registrarLog(Integer pessoa, Integer chavePrimaria, String entidade, String entidadeDescricao,
                              UsuarioVO usuarioVO, String operacao, String nomeCampo, String valorAnterior,
                              String valorAlterado, Connection con) {
        Log logDAO;
        try {
            logDAO = new Log(con);

            LogVO log = new LogVO();
            log.setOperacao(operacao);
            if ("Erro".equals(nomeCampo)) {
                log.setChavePrimaria("");
            } else {
                log.setChavePrimaria(chavePrimaria.toString());
            }
            if (usuarioVO == null) {
                log.setResponsavelAlteracao("AUTOMATICO");
            } else {
                log.setResponsavelAlteracao(usuarioVO.getNome());
                log.setUserOAMD(usuarioVO.getUserOamd());
            }
            log.setNomeEntidade(entidade);
            log.setNomeEntidadeDescricao(entidadeDescricao);
            log.setDataAlteracao(Calendario.hoje());
            log.setNomeCampo(nomeCampo);
            log.setValorCampoAnterior(valorAnterior);
            log.setValorCampoAlterado(valorAlterado);
            log.setPessoa(pessoa);
            logDAO.incluirSemCommit(log);
        } catch (Exception ex) {
            ex.printStackTrace();
        } finally {
            logDAO = null;
        }
    }

    private EnvelopeRespostaDTO informacoesEmpresa(ServletRequest request) throws Exception {
        Connection con = null;
        try {
            JSONObject json = new JSONObject(obterBody(request));

            String chave = json.optString("chave");
            Integer empresa = json.optInt("empresa");

            if (UteisValidacao.emptyString(chave)) {
                throw new Exception("Chave informada");
            }

            con = obterConexao(request, chave);

            StringBuilder sql = new StringBuilder();
            sql.append("select  \n");
            sql.append("em.codigo, \n");
            sql.append("em.nome, \n");
            sql.append("em.razaosocial, \n");
            sql.append("em.cnpj, \n");
            sql.append("em.cod_empresafinanceiro, \n");
            sql.append("em.idexterno, \n");
            sql.append("em.setor, \n");
            sql.append("em.endereco, \n");
            sql.append("em.numero, \n");
            sql.append("em.complemento, \n");
            sql.append("em.cep, \n");
            sql.append("p.nome as pais, \n");
            sql.append("es.sigla as uf, \n");
            sql.append("ci.nome as cidade, \n");
            sql.append("em.latitude, \n");
            sql.append("em.longitude, \n");
            sql.append("em.ativa \n");
            sql.append("from empresa em \n");
            sql.append("left join cidade ci on ci.codigo = em.cidade \n");
            sql.append("left join estado es on es.codigo = em.estado \n");
            sql.append("left join pais p on p.codigo = em.pais \n");
            if (!UteisValidacao.emptyNumber(empresa)) {
                sql.append("where em.codigo = ").append(empresa).append(" \n");
            }
            sql.append("order by em.codigo \n");


            List<EmpresaInfoDTO> lista = new ArrayList<>();
            try (PreparedStatement ps = con.prepareStatement(sql.toString())) {
                try (ResultSet rs = ps.executeQuery()) {
                    while (rs.next()) {
                        EmpresaInfoDTO dto = new EmpresaInfoDTO();
                        dto.setCodigo(rs.getInt("codigo"));
                        dto.setNome_fantasia(rs.getString("nome"));
                        dto.setRazao_social(rs.getString("razaosocial"));
                        dto.setCnpj(rs.getString("cnpj"));
                        dto.setEmpresa_financeiro(rs.getInt("cod_empresafinanceiro"));
                        dto.setId_externo(rs.getInt("idexterno"));
                        dto.setLatitude(rs.getString("latitude"));
                        dto.setLongitude(rs.getString("longitude"));
                        dto.setAtiva(rs.getBoolean("ativa"));

                        EnderecoDTO enderecoDTO = new EnderecoDTO();
                        enderecoDTO.setCep(rs.getString("cep"));
                        enderecoDTO.setEndereco(rs.getString("endereco"));
                        enderecoDTO.setNumero(rs.getString("numero"));
                        enderecoDTO.setComplemento(rs.getString("complemento"));
                        enderecoDTO.setBairro(rs.getString("setor"));
                        enderecoDTO.setCidade(rs.getString("cidade"));
                        enderecoDTO.setUf(rs.getString("uf"));
                        enderecoDTO.setPais(rs.getString("pais"));
                        dto.setEndereco(enderecoDTO);
                        lista.add(dto);
                    }
                }
            }

            return EnvelopeRespostaDTO.of(lista);
        } catch (Exception ex) {
            ex.printStackTrace();
            throw ex;
        } finally {
            finalizarConexao(con);
        }
    }

    private EnvelopeRespostaDTO alterarIdExternoEmpresa(ServletRequest request) throws Exception {
        Connection con = null;
        try {
            JSONObject json = new JSONObject(obterBody(request));

            String chave = json.optString("chave");
            Integer empresa = json.optInt("empresa");
            String idExterno = json.optString("idExterno");

            if (UteisValidacao.emptyString(chave)) {
                throw new Exception("Chave informada");
            }
            if (UteisValidacao.emptyNumber(empresa)) {
                throw new Exception("empresa informada");
            }
            if (UteisValidacao.emptyString(idExterno)) {
                throw new Exception("idExterno informada");
            }

            con = obterConexao(request, chave);

            String sql = "update empresa set idexterno = ? where codigo = ?";
            try (PreparedStatement pst = con.prepareStatement(sql)) {
                pst.setString(1, idExterno);
                pst.setInt(2, empresa);
                pst.execute();
            }
            return EnvelopeRespostaDTO.of("ok");
        } catch (Exception ex) {
            ex.printStackTrace();
            throw ex;
        } finally {
            finalizarConexao(con);
        }
    }

    private EnvelopeRespostaDTO loginUsuarioSemSenha(HttpServletRequest request) throws Exception {
        try {
            JSONObject json = new JSONObject(obterBody(request));

            String chave = json.optString("chave");
            if (UteisValidacao.emptyString(chave)) {
                throw new Exception("Chave informada");
            }

            String username = json.optString("username");
            if (UteisValidacao.emptyString(username)) {
                throw new Exception("Username informado");
            }

            try (Connection con = new DAO().obterConexaoEspecifica(chave);
                 UsuarioService usuarioService = new UsuarioService(con)) {

                UsuarioTO usuarioTO = usuarioService.loginUsuarioSemSenha(chave, username);
                return EnvelopeRespostaDTO.of(usuarioTO);
            }

        } catch (Exception ex) {
            ex.printStackTrace();
            throw ex;
        }
    }

    private EnvelopeRespostaDTO configuracaoSistema(ServletRequest request) throws Exception {
        Connection con = null;
        try {
            String chave = request.getParameter("chave");

            if (UteisValidacao.emptyString(chave)) {
                throw new Exception("Chave informada");
            }
            con = obterConexao(request, chave);

            JSONObject json = new JSONObject();
            try (PreparedStatement ps = con.prepareStatement("SELECT * FROM ConfiguracaoSistema ORDER BY codigo limit 1")) {
                try (ResultSet rs = ps.executeQuery()) {
                    final ResultSetMetaData meta = rs.getMetaData();
                    final int columnCount = meta.getColumnCount();
                    if (rs.next()) {
                        for (int column = 1; column <= columnCount; ++column) {
                            try {
                                final String nomeColuna = meta.getColumnName(column);
                                final Object value = rs.getObject(column);
                                json.put(nomeColuna.toLowerCase(), value);
                            } catch (Exception ex) {
                                ex.printStackTrace();
                            }
                        }
                    }
                }
            }

            return EnvelopeRespostaDTO.of(json);
        } catch (Exception ex) {
            ex.printStackTrace();
            throw ex;
        } finally {
            finalizarConexao(con);
        }
    }

    private EnvelopeRespostaDTO obterUsuarioGeral(ServletRequest request) throws Exception {
        Connection con = null;
        try {
            JSONObject json = new JSONObject(obterBody(request));

            String chave = json.optString("chave");
            String username = json.optString("username");
            Integer usuario_zw = json.optInt("usuario_zw");
            con = obterConexao(request, chave);

            String usuarioGeral = "";

            if (!UteisValidacao.emptyNumber(usuario_zw)) {
                try (PreparedStatement ps = con.prepareStatement("select usuariogeral from usuario where codigo = " + usuario_zw)) {
                    try (ResultSet rs = ps.executeQuery()) {
                        if (rs.next()) {
                            usuarioGeral = rs.getString("usuariogeral");
                        }
                    }
                }
            }

            if (UteisValidacao.emptyString(usuarioGeral) && !UteisValidacao.emptyString(username)) {
                try (PreparedStatement ps = con.prepareStatement("select usuariogeral from usuario where username ilike '" + username + "'")) {
                    try (ResultSet rs = ps.executeQuery()) {
                        if (rs.next()) {
                            usuarioGeral = rs.getString("usuariogeral");
                        }
                    }
                }
            }
            return EnvelopeRespostaDTO.of(usuarioGeral);
        } catch (Exception ex) {
            ex.printStackTrace();
            throw ex;
        } finally {
            finalizarConexao(con);
        }
    }

    private EnvelopeRespostaDTO enviarEmailTeste(ServletRequest request, HttpServletResponse response){
        Connection connection = null;
        EnvelopeRespostaDTO envelopeRespostaDTO = null;
        try {
            JSONObject json = new JSONObject(obterBody(request));
            String email = json.optString("email");
            String chave = json.optString("chave");
            Integer empresa = json.optInt("empresa");
            boolean integracaoPacto = json.optBoolean("integracaoPacto");
            boolean conexaoSegura = json.optBoolean("conexaoSegura");
            String login = json.optString("login");
            String senha = json.optString("senha");
            String remetentePadrao = json.optString("remetentePadrao");
            String emailPadrao = json.optString("emailPadrao");
            String mailServer = json.optString("mailServer");
            String portaServer = json.optString("portaServer");
            boolean usaSMTPS = json.optBoolean("usaSMTPS");
            boolean iniciarTls = json.getBoolean("iniciarTls");

            if(chave == null || chave.isEmpty()){
                response.setStatus(400);
                throw new Exception("Chave não informada");
            }

            if(email == null || email.isEmpty()){
                response.setStatus(400);
                throw new Exception("Email não informado");
            }

            if(empresa == null || empresa == 0){
                response.setStatus(400);
                throw new Exception("Empresa não informada");
            }

            connection = new DAO().obterConexaoEspecifica(chave);
            ConfiguracaoSistemaCRM configuracaoSistemaCRM = new ConfiguracaoSistemaCRM(connection);
            Empresa empresaDao = new Empresa(connection);
            EmpresaVO empresaVO = empresaDao.consultarPorCodigo(empresa, Uteis.NIVELMONTARDADOS_MINIMOS);

            if(empresaVO == null){
                response.setStatus(400);
                throw new Exception("Empresa não encontrada");
            }

            ConfiguracaoSistemaCRMVO configuracaoSistemaCRMVO = new ConfiguracaoSistemaCRMVO();
            configuracaoSistemaCRMVO.setIntegracaoPacto(integracaoPacto);
            configuracaoSistemaCRMVO.setConexaoSegura(conexaoSegura);
            configuracaoSistemaCRMVO.setLogin(login);
            configuracaoSistemaCRMVO.setSenha(senha);
            configuracaoSistemaCRMVO.setRemetentePadrao(remetentePadrao);
            configuracaoSistemaCRMVO.setEmailPadrao(emailPadrao);
            configuracaoSistemaCRMVO.setMailServer(mailServer);
            configuracaoSistemaCRMVO.setPortaServer(portaServer);
            configuracaoSistemaCRMVO.setIniciarTLS(iniciarTls);
            configuracaoSistemaCRMVO.setUsaSMTPS(usaSMTPS);

            configuracaoSistemaCRM.obterConfiguracaoEmailAutomatico(email, empresaVO.getNome(), configuracaoSistemaCRMVO);

            envelopeRespostaDTO = EnvelopeRespostaDTO.of("Enviar enviado com sucesso");
        } catch (Exception e) {
            response.setStatus(500);
            envelopeRespostaDTO = EnvelopeRespostaDTO.erro(e.getMessage(), "Erro ao enviar email de teste");
        }finally {
            finalizarConexao(connection);
        }

        return envelopeRespostaDTO;
    }

    private EnvelopeRespostaDTO desvincularUsuario(ServletRequest request) throws Exception {
        Usuario usuarioDao;
        UsuarioEmail usuarioEmailDao = null;
        UsuarioTelefone usuarioTelefoneDao = null;
        UsuarioMovel usuarioMovelDao = null;
        Connection con = null;
        try {
            ObjectMapper objectMapper = new ObjectMapper();
            DesvincularUsuarioDTO desvincularUsuarioDTO = objectMapper.readValue(obterBody(request), DesvincularUsuarioDTO.class);

            con = obterConexao(request, desvincularUsuarioDTO.getChaveDesvincular());
            usuarioDao = new Usuario(con);
            usuarioEmailDao = new UsuarioEmail(con);
            usuarioTelefoneDao = new UsuarioTelefone(con);
            usuarioMovelDao = new UsuarioMovel(con);

            UsuarioVO usuarioVO = usuarioDao.consultarPorChavePrimaria(desvincularUsuarioDTO.getCodigoZw(), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
            UsuarioMovelVO usuarioMovel = usuarioMovelDao.consultarPorUsuario(usuarioVO, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);

            String senhaTemporaria = UUID.randomUUID().toString();

            usuarioVO.setUsuarioGeral(null);
            usuarioVO.setEmail(null);
            usuarioVO.setSenha(senhaTemporaria);
            usuarioDao.alterarUsuarioGeral(usuarioVO);
            usuarioDao.alterarSenhaUsuario(usuarioVO, false);

            usuarioMovelDao.alterarSenha(usuarioMovel.getCodigo(), senhaTemporaria, null);

            UsuarioEmailVO usuarioEmailVO = usuarioEmailDao.consultarPorUsuario(usuarioVO.getCodigo());
            if (usuarioEmailVO.getCodigo() != null) {
                usuarioEmailDao.excluir(usuarioEmailVO);
            }

            UsuarioTelefoneVO usuarioTelefoneVO = usuarioTelefoneDao.consultarPorUsuario(usuarioVO.getCodigo());
            if (usuarioTelefoneVO.getCodigo() != null) {
                usuarioTelefoneDao.excluir(usuarioTelefoneVO);
            }

            return EnvelopeRespostaDTO.of("ok");
        } catch (Exception ex) {
            ex.printStackTrace();
            throw ex;
        } finally {
            usuarioDao = null;
            usuarioEmailDao = null;
            usuarioTelefoneDao = null;
            usuarioMovelDao = null;
            finalizarConexao(con);
        }
    }

    private String obterChave(ServletRequest request) throws Exception {
        String key = request.getParameter("key");
        if (UteisValidacao.emptyString(key)) {
            throw new Exception("Chave não informada.");
        }
        return key.trim();
    }

    private EnvelopeRespostaDTO recursoPadrao(HttpServletRequest request) throws Exception {
        String chave = obterChave(request);
        try (Connection con = new DAO().obterConexaoEspecifica(chave)) {
            JSONObject jsonBody = new JSONObject(obterBody(request));
            Integer codEmpresa = jsonBody.optInt("empresa");
            String tipoInfo = jsonBody.optString("tipoInfo");
            String operacao = jsonBody.optString("operacao");
            Integer usuarioOAMDCodigo = jsonBody.optInt("usuarioOAMDCodigo");
            String usuarioOAMDUsername = jsonBody.optString("usuarioOAMDUsername");

            if (UteisValidacao.emptyString(operacao)) {
                throw new Exception("Operacao não informado");
            }

            if (operacao.equalsIgnoreCase("alterar")) {

                if (UteisValidacao.emptyNumber(usuarioOAMDCodigo)) {
                    throw new Exception("Codigo do usuário OAMD não informado");
                }
                if (UteisValidacao.emptyString(usuarioOAMDUsername)) {
                    throw new Exception("Username do usuário OAMD não informado");
                }

                Empresa empresaDAO;
                Usuario usuarioDAO;
                try {
                    con.setAutoCommit(false);
                    empresaDAO = new Empresa(con);
                    usuarioDAO = new Usuario(con);

                    UsuarioVO usuarioVO = usuarioDAO.getUsuarioRecorrencia();
                    usuarioVO.setUserOamd(usuarioOAMDUsername);

                    Set<TipoInfoMigracaoEnum> tiposAtivos = new HashSet<>();
                    Set<TipoInfoMigracaoEnum> tiposDesativados = new HashSet<>();
                    for (String tipoStr : tipoInfo.split(",")) {
                        if (!UteisValidacao.emptyString(tipoStr)) {
                            tiposAtivos.add(TipoInfoMigracaoEnum.valueOf(tipoStr));
                        }
                    }

                    String tipoOperacao = jsonBody.optString("tipoOperacao");
                    boolean adicionarSomenteAdicionar = tipoOperacao.equalsIgnoreCase("ADICIONAR");
                    boolean adicionarSomenteRemover = tipoOperacao.equalsIgnoreCase("REMOVER");

                    if (adicionarSomenteAdicionar) {
                        //se for somente para adicionar então buscar os da empresa ou geral e adicionar a lista
                        Set<TipoInfoMigracaoEnum> listaAtual = obterTipoInfoMigracaoEnumAtual(codEmpresa, con);
                        if (!UteisValidacao.emptyList(listaAtual)) {
                            tiposAtivos.addAll(listaAtual);
                        }
                    }

                    if (adicionarSomenteRemover) {
                        //se vai somente remover os que foram selecionados então vamos ver se tem já adicionado na empresa o que foi selecionado e remover
                        Set<TipoInfoMigracaoEnum> tipoNovosMarcar = new HashSet<>();
                        Set<TipoInfoMigracaoEnum> listaAtual = obterTipoInfoMigracaoEnumAtual(codEmpresa, con);

                        for (TipoInfoMigracaoEnum tipoAtual : listaAtual) {
                            //verificar se o tipo selecionado existe no atual
                            boolean existe = false;
                            for (TipoInfoMigracaoEnum tipoSel : tiposAtivos) {
                                if (tipoAtual.equals(tipoSel)) {
                                    existe = true;
                                    break;
                                }
                            }
                            if (!existe) {
                                //se não existe vou adicionar ele para poder continuar marcado
                                tipoNovosMarcar.add(tipoAtual);
                            }
                        }

                        tiposAtivos = new HashSet<>(tipoNovosMarcar);
                        for (TipoInfoMigracaoEnum tipoEnum : TipoInfoMigracaoEnum.values()) {
                            if (!tipoEnum.isRecursoPadrao()) {
                                continue;
                            }
                            boolean removido = true;
                            for (TipoInfoMigracaoEnum tipoHabi : tiposAtivos) {
                                if (tipoEnum.equals(tipoHabi)) {
                                    removido = false;
                                    break;
                                }
                            }
                            if (removido) {
                                tiposDesativados.add(tipoEnum);
                            }
                        }

                    } else {

                        for (TipoInfoMigracaoEnum tipoEnum : TipoInfoMigracaoEnum.values()) {
                            if (!tipoEnum.isRecursoPadrao()) {
                                continue;
                            }
                            boolean removido = true;
                            for (TipoInfoMigracaoEnum tipoHabi : tiposAtivos) {
                                if (tipoEnum.equals(tipoHabi)) {
                                    removido = false;
                                    break;
                                }
                            }
                            if (removido) {
                                tiposDesativados.add(tipoEnum);
                            }
                        }

                    }

                    for (TipoInfoMigracaoEnum tipoAdd : tiposAtivos) {
                        empresaDAO.alterarRecursoSistemaUsuarios(tipoAdd, true, codEmpresa, usuarioVO, true, con);
                    }
                    for (TipoInfoMigracaoEnum tipoRemover : tiposDesativados) {
                        empresaDAO.alterarRecursoSistemaUsuarios(tipoRemover, false, codEmpresa, usuarioVO, false, con);
                    }

                    StringBuilder tiposSalvar = new StringBuilder();
                    for (TipoInfoMigracaoEnum tipo : tiposAtivos) {
                        tiposSalvar.append(",").append(tipo.name());
                    }
                    String tiposInfoMigracaoPadraoNovo = tiposSalvar.toString().replaceFirst(",", "");

                    //alterar na empresa
                    StringBuilder sqlEmpresa = new StringBuilder();
                    sqlEmpresa.append("select \n");
                    sqlEmpresa.append("codigo, \n");
                    sqlEmpresa.append("nome, \n");
                    sqlEmpresa.append("tiposInfoMigracaoPadrao \n");
                    sqlEmpresa.append("from empresa \n");
                    sqlEmpresa.append("where 1 = 1");
                    if (!UteisValidacao.emptyNumber(codEmpresa)) {
                        sqlEmpresa.append("and codigo = ").append(codEmpresa).append(" \n");
                    }
                    ResultSet rs = SuperFacadeJDBC.criarConsulta(sqlEmpresa.toString(), con);
                    while (rs.next()) {
                        Integer codigo = rs.getInt("codigo");
                        String nome = rs.getString("nome");

                        String tiposInfoMigracaoPadraoAnterior = rs.getString("tiposInfoMigracaoPadrao");
                        SuperFacadeJDBC.executarUpdate("update empresa set tiposInfoMigracaoPadrao = '" + tiposInfoMigracaoPadraoNovo + "' where codigo = " + codigo + ";", con);
                        empresaDAO.gerarLogAlterarRecursoEmpresa(codigo, usuarioOAMDCodigo, usuarioOAMDUsername, tiposInfoMigracaoPadraoAnterior, tiposInfoMigracaoPadraoNovo, con, "tiposInfoMigracaoPadrao");
                    }

                    //se alterou de alguma empresa então limpa a configuração geral
                    if (!UteisValidacao.emptyNumber(codEmpresa)) {
                        SuperFacadeJDBC.executarUpdate("update configuracaosistema set tiposInfoMigracaoPadrao = '';", con);
                    } else {
                        SuperFacadeJDBC.executarUpdate("update configuracaosistema set tiposInfoMigracaoPadrao = '" + tiposInfoMigracaoPadraoNovo + "';", con);
                    }

                    con.commit();
                } catch (Exception ex) {
                    con.rollback();
                    throw ex;
                } finally {
                    con.setAutoCommit(true);
                    empresaDAO = null;
                }

            } else if (operacao.equalsIgnoreCase("obter")) {

                StringBuilder sqlEmpresa = new StringBuilder();
                sqlEmpresa.append("select \n");
                sqlEmpresa.append("codigo, \n");
                sqlEmpresa.append("tiposInfoMigracaoPadrao \n");
                sqlEmpresa.append("from empresa \n");
                sqlEmpresa.append("where 1 = 1");
                if (!UteisValidacao.emptyNumber(codEmpresa)) {
                    sqlEmpresa.append("and codigo = ").append(codEmpresa).append(" \n");
                }
                ResultSet rs = SuperFacadeJDBC.criarConsulta(sqlEmpresa.toString(), con);
                JSONArray jsonArray = new JSONArray();
                while (rs.next()) {
                    Integer codigo = rs.getInt("codigo");
                    String tiposInfoMigracaoPadrao = rs.getString("tiposInfoMigracaoPadrao");
                    JSONObject jsonEmp = new JSONObject();
                    jsonEmp.put("codigo", codigo);
                    jsonEmp.put("tiposInfoMigracaoPadrao", tiposInfoMigracaoPadrao);
                    jsonArray.put(jsonEmp);
                }
                return EnvelopeRespostaDTO.of(jsonArray.toString());

            } else if (operacao.equalsIgnoreCase("obter_configuracao_sistema")) {

                StringBuilder sqlConfig = new StringBuilder();
                sqlConfig.append("select \n");
                sqlConfig.append("tiposInfoMigracaoPadrao \n");
                sqlConfig.append("from configuracaosistema \n");
                ResultSet rs = SuperFacadeJDBC.criarConsulta(sqlConfig.toString(), con);
                if (rs.next()) {
                    return EnvelopeRespostaDTO.of(rs.getString("tiposInfoMigracaoPadrao"));
                } else {
                    throw new Exception("Erro consultar configuracaosistema");
                }

            } else if (operacao.equalsIgnoreCase("processarConfigEmpresas")) {

                Usuario usuarioDAO = new Usuario(con);
                usuarioDAO.processarRecursoPadraoNovoUsuario(true, true, null);
                usuarioDAO = null;

            } else if (operacao.equalsIgnoreCase("obter_tiposInfoMigracaoPadraoAutomatico")) {

                StringBuilder sqlEmpresa = new StringBuilder();
                sqlEmpresa.append("select \n");
                sqlEmpresa.append("codigo, \n");
                sqlEmpresa.append("tiposInfoMigracaoPadraoAutomatico \n");
                sqlEmpresa.append("from empresa \n");
                sqlEmpresa.append("where 1 = 1");
                if (!UteisValidacao.emptyNumber(codEmpresa)) {
                    sqlEmpresa.append("and codigo = ").append(codEmpresa).append(" \n");
                }
                ResultSet rs = SuperFacadeJDBC.criarConsulta(sqlEmpresa.toString(), con);
                JSONArray jsonArray = new JSONArray();
                while (rs.next()) {
                    Integer codigo = rs.getInt("codigo");
                    String tiposInfoMigracaoPadraoAutomatico = rs.getString("tiposInfoMigracaoPadraoAutomatico");
                    JSONObject jsonEmp = new JSONObject();
                    jsonEmp.put("codigo", codigo);
                    jsonEmp.put("tiposInfoMigracaoPadraoAutomatico", tiposInfoMigracaoPadraoAutomatico == null ? "" : tiposInfoMigracaoPadraoAutomatico);
                    jsonArray.put(jsonEmp);
                }
                return EnvelopeRespostaDTO.of(jsonArray.toString());

            } else if (operacao.equalsIgnoreCase("gravarInfoMigracaoAutomatico")) {

                if (UteisValidacao.emptyNumber(usuarioOAMDCodigo)) {
                    throw new Exception("Codigo do usuário OAMD não informado");
                }
                if (UteisValidacao.emptyString(usuarioOAMDUsername)) {
                    throw new Exception("Username do usuário OAMD não informado");
                }

                Empresa empresaDAO;
                try {
                    con.setAutoCommit(false);
                    empresaDAO = new Empresa(con);

                    List<TipoInfoMigracaoEnum> tiposAtivos = new ArrayList<>();
                    for (String tipoStr : tipoInfo.split(",")) {
                        if (!UteisValidacao.emptyString(tipoStr)) {
                            tiposAtivos.add(TipoInfoMigracaoEnum.valueOf(tipoStr));
                        }
                    }

                    StringBuilder tiposSalvar = new StringBuilder();
                    for (TipoInfoMigracaoEnum tipo : tiposAtivos) {
                        tiposSalvar.append(",").append(tipo.name());
                    }
                    String tiposInfoMigracaoPadraoAutomatico = tiposSalvar.toString().replaceFirst(",", "");

                    //alterar na empresa
                    StringBuilder sqlEmpresa = new StringBuilder();
                    sqlEmpresa.append("select \n");
                    sqlEmpresa.append("codigo, \n");
                    sqlEmpresa.append("nome, \n");
                    sqlEmpresa.append("tiposInfoMigracaoPadraoAutomatico \n");
                    sqlEmpresa.append("from empresa \n");
                    sqlEmpresa.append("where 1 = 1");
                    if (!UteisValidacao.emptyNumber(codEmpresa)) {
                        sqlEmpresa.append("and codigo = ").append(codEmpresa).append(" \n");
                    }
                    ResultSet rs = SuperFacadeJDBC.criarConsulta(sqlEmpresa.toString(), con);
                    while (rs.next()) {
                        Integer codigo = rs.getInt("codigo");
                        String nome = rs.getString("nome");
                        String tiposInfoMigracaoPadraoAutomaticoAnterior = rs.getString("tiposInfoMigracaoPadraoAutomatico");

                        SuperFacadeJDBC.executarUpdate("update empresa set tiposInfoMigracaoPadraoAutomatico = '" + tiposInfoMigracaoPadraoAutomatico + "' where codigo = " + codigo + ";", con);
                        empresaDAO.gerarLogAlterarRecursoEmpresa(codigo, usuarioOAMDCodigo, usuarioOAMDUsername, tiposInfoMigracaoPadraoAutomaticoAnterior, tiposInfoMigracaoPadraoAutomatico, con, "tiposInfoMigracaoPadraoAutomatico");

                        if (jsonBody.optBoolean("processarAgora")) {
                            empresaDAO.processarAutomaticoInfoMigracaoUsuario(codEmpresa, false);
                        }
                    }

                    con.commit();
                } catch (Exception ex) {
                    con.rollback();
                    throw ex;
                } finally {
                    con.setAutoCommit(true);
                    empresaDAO = null;
                }

            } else if (operacao.equalsIgnoreCase("processarAutomaticoInfoMigracaoUsuario")) {

                Empresa empresaDAO;
                try {
                    empresaDAO = new Empresa(con);
                    empresaDAO.processarAutomaticoInfoMigracaoUsuario(codEmpresa, true);
                } finally {
                    empresaDAO = null;
                }

            } else if (operacao.equalsIgnoreCase("alterarUsuarioInfoMigracaoGeral")) {
                Empresa empresaDAO;
                Usuario usuarioDAO;
                try {
                    con.setAutoCommit(false);
                    empresaDAO = new Empresa(con);
                    usuarioDAO = new Usuario(con);

                    UsuarioVO usuarioVO = usuarioDAO.getUsuarioRecorrencia();
                    usuarioVO.setUserOamd(usuarioOAMDUsername);

                    boolean ativar = jsonBody.optString("tipoOperacao").equalsIgnoreCase("ativar");
                    boolean desativar = jsonBody.optString("tipoOperacao").equalsIgnoreCase("desativar");

                    for (String tipoStr : tipoInfo.split(",")) {
                        if (!UteisValidacao.emptyString(tipoStr)) {
                            TipoInfoMigracaoEnum tipoInfoMigracaoEnum = TipoInfoMigracaoEnum.valueOf(tipoStr);
                            if (ativar)  {
                                empresaDAO.alterarRecursoSistemaUsuarios(tipoInfoMigracaoEnum, true, codEmpresa, usuarioVO, true, con);
                            }
                            if (desativar)  {
                                empresaDAO.alterarRecursoSistemaUsuarios(tipoInfoMigracaoEnum, false, codEmpresa, usuarioVO, true, con);
                            }
                        }
                    }

                    con.commit();
                } catch (Exception ex) {
                    con.rollback();
                    throw ex;
                } finally {
                    con.setAutoCommit(true);
                    empresaDAO = null;
                }
            } else {
                throw new Exception("Operacao não identificada");
            }

            return EnvelopeRespostaDTO.of("ok");
        }
    }

    private Set<TipoInfoMigracaoEnum> obterTipoInfoMigracaoEnumAtual(Integer codEmpresa, Connection con) throws Exception {
        Set<TipoInfoMigracaoEnum> tiposAtual = new HashSet<>();
        StringBuilder sqlConfigAtual = new StringBuilder();
        if (!UteisValidacao.emptyNumber(codEmpresa)) {
            sqlConfigAtual.append("select \n");
            sqlConfigAtual.append("codigo, \n");
            sqlConfigAtual.append("tiposInfoMigracaoPadrao \n");
            sqlConfigAtual.append("from empresa \n");
            sqlConfigAtual.append("where codigo = ").append(codEmpresa).append(" \n");
        } else {
            sqlConfigAtual.append("select \n");
            sqlConfigAtual.append("tiposInfoMigracaoPadrao \n");
            sqlConfigAtual.append("from configuracaosistema \n");
        }

        String configAtual = "";
        ResultSet rs = SuperFacadeJDBC.criarConsulta(sqlConfigAtual.toString(), con);
        if (rs.next()) {
            configAtual = rs.getString("tiposInfoMigracaoPadrao");
        }

        if (!UteisValidacao.emptyString(configAtual)) {
            for (String tipoAtual : configAtual.split(",")) {
                if (!UteisValidacao.emptyString(tipoAtual)) {
                    tiposAtual.add(TipoInfoMigracaoEnum.valueOf(tipoAtual));
                }
            }
        }
        return tiposAtual;
    }

    public void enviarEmailNoReply(String email, String assunto, String mensagem) throws Exception {
        try {
            ConfiguracaoSistemaCRMVO configCRMVO = SuperControle.getConfiguracaoSMTPNoReply();
            configCRMVO.setRemetentePadrao("Sistema Pacto");
            if (!configCRMVO.isConfiguracaoEmailValida()) {
                throw new Exception("ConfiguracaoSistemaCRMVO não é válida");
            }
            UteisEmail uteis = new UteisEmail();
            uteis.novo(assunto, configCRMVO);
            uteis.enviarEmail(email, "", mensagem, "",
                    configCRMVO.getIntegracaoPacto(), configCRMVO.preparaEnvioSendy());
        } catch (Exception ex) {
            ex.printStackTrace();
            throw ex;
        }
    }

    public EnvelopeRespostaDTO obterEmpresasUsuario(ServletRequest request) {
        try {
            String key = obterChave(request);

            Usuario usuarioDAO;
            Empresa empresaDAO;
            Colaborador colaboradorDAO;
            UsuarioPerfilAcesso usuarioPerfilAcessoDAO;
            try (Connection con = new DAO().obterConexaoEspecifica(key)) {
                usuarioDAO = new Usuario(con);
                empresaDAO = new Empresa(con);
                colaboradorDAO = new Colaborador(con);
                usuarioPerfilAcessoDAO = new UsuarioPerfilAcesso(con);

                Integer usuarioZW = UteisValidacao.converterInteiro(request.getParameter("usuario"));

                Uteis.logar(null, "====START INTEGRAÇÃO SERVLET obterEmpresasUsuario ==== " + key);
                if (!usuarioDAO.consultarUsuarioAtivo(usuarioZW)) {
                    return EnvelopeRespostaDTO.of(new ArrayList<>());
                }

                List<UsuarioPerfilAcessoVO> listaPerfisUsuario = usuarioPerfilAcessoDAO.consultarUsuarioPerfilAcesso(usuarioZW, Uteis.NIVELMONTARDADOS_MINIMOS);
                List<EmpresaWS> empresas = new ArrayList<>();
                for (UsuarioPerfilAcessoVO perfil : listaPerfisUsuario) {
                    EmpresaVO emp = empresaDAO.consultarPorChavePrimaria(
                            perfil.getEmpresa().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                    ColaboradorVO colaboradorVO = colaboradorDAO.consultarPorUsuarioEmpresaComBasePessoa(usuarioZW, emp.getCodigo());
                    EmpresaWS empWS = new EmpresaWS(emp);
                    if(colaboradorVO != null){
                        empWS.setCodigoColaborador(colaboradorVO.getCodigo());
                    }
                    empWS.setDescricaoPerfil(perfil.getPerfilAcesso().getNome());
                    empresas.add(empWS);
                }

                if (empresas.isEmpty() && listaPerfisUsuario != null && listaPerfisUsuario.isEmpty() && usuarioZW != null && usuarioZW > 0) { // Usuario com acesso só no treino e não é independente.
                    ColaboradorVO colaboradorVO = colaboradorDAO.consultarPorCodigoUsuario(usuarioZW, 0, false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                    boolean temTipo = colaboradorVO.getListaTipoColaboradorVOs().stream().anyMatch(
                            tipo -> (Objects.equals(TipoColaboradorEnum.PROFESSOR.getSigla(), tipo.getDescricao()) || Objects.equals(TipoColaboradorEnum.PROFESSOR_TREINO.getSigla(), tipo.getDescricao()))
                    );
                    if (temTipo) {
                        EmpresaVO emp = empresaDAO.consultarPorChavePrimaria(
                                colaboradorVO.getEmpresa().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                        EmpresaWS empWS = new EmpresaWS(emp);
                        if (colaboradorVO != null) {
                            empWS.setCodigoColaborador(colaboradorVO.getCodigo());
                        }
                        empWS.setDescricaoPerfil("SEM ACESSO AO ADM - SOMENTE TREINO");
                        empresas.add(empWS);
                    }
                }
                Uteis.logar(null, "====END INTEGRAÇÃO SERVLET obterEmpresasUsuario ==== " + empresas.toString());
                return EnvelopeRespostaDTO.of(empresas);
            }
        } catch (Exception ex) {
            Logger.getLogger(AdmWS.class.getName()).log(Level.SEVERE, null, ex);
            return null;
        }
    }

    public EnvelopeRespostaDTO obterEmpresasComSituacao(ServletRequest request) {
        try {
            String key = obterChave(request);
            Empresa empresaDAO;
            try (Connection con = new DAO().obterConexaoEspecifica(key)) {
                empresaDAO = new Empresa(con);

                String situacao = request.getParameter("situacao");
                Boolean ativa = situacao != null ? Boolean.parseBoolean(situacao) : null;

                Uteis.logar(null, "====START INTEGRAÇÃO SERVLET obterEmpresas ==== " + key);
                List<EmpresaWS> empresas = new ArrayList<EmpresaWS>();
                List<EmpresaVO> listEmpresas = empresaDAO.consultarTodas(ativa, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                for (EmpresaVO empresa : listEmpresas) {
                    empresas.add(new EmpresaWS(empresa));
                }
                Uteis.logar(null, "====END INTEGRAÇÃO SERVLET obterEmpresas ==== " + empresas.toString());
                return EnvelopeRespostaDTO.of(empresas);
            }
        } catch (Exception ex) {
            Logger.getLogger(AdmWS.class.getName()).log(Level.SEVERE, null, ex);
            return null;
        }
    }
}
