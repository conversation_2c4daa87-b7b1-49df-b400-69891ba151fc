package servlet.acesso;

import br.com.pactosolucoes.oamd.controle.basico.DAO;
import com.google.common.net.HttpHeaders;
import negocio.facade.jdbc.basico.Cliente;
import negocio.facade.jdbc.utilitarias.Conexao;
import org.json.JSONArray;
import org.json.JSONObject;
import relatorio.negocio.comuns.basico.ResumoPessoaBIAcessoGymPassVO;
import servlet.arquitetura.SuperServlet;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.sql.Connection;
import java.util.List;

public class AlunosGymPassServlet extends SuperServlet {
        @Override
    protected void processRequest(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
            response.setHeader("Access-Control-Allow-Origin", "*");
            response.setHeader("Access-Control-Allow-Methods", "GET, OPTIONS");
            response.setHeader("Access-Control-Max-Age", "3600");
            response.setHeader("Access-Control-Allow-Headers", "Authorization, content-type, xsrf-token");
            response.addHeader("Access-Control-Expose-Headers", "xsrf-token");
            response.setContentType("application/json");

            try {
                validarAuthorizationComChave(request);
                String chave = request.getHeader(HttpHeaders.AUTHORIZATION);

                JSONObject body = getJSONBody(request);
                Integer codigoEmpresa = body.optInt("empresa");
                JSONArray jsonArray = new JSONArray();

                try (Connection connection = new DAO().obterConexaoEspecifica(chave)) {
                    Conexao.guardarConexaoForJ2SE(chave, connection);
                    Cliente cliente = new Cliente(connection);
                    List<ResumoPessoaBIAcessoGymPassVO> lista = cliente.consultarClienteGymPass(codigoEmpresa);

                    for (ResumoPessoaBIAcessoGymPassVO item : lista) {
                        JSONObject jsonObject = new JSONObject();
                        jsonObject.put("matricula", item.getMatricula());
                        jsonObject.put("nome", item.getNome());
                        jsonObject.put("cliente", item.getCliente());
                        jsonObject.put("situacaoCliente", item.getSituacaoCliente());
                        jsonObject.put("dataAcesso", item.getDataAcesso() != null ? item.getDataAcesso().getTime() : JSONObject.NULL);
                        jsonObject.put("tokenGymPass", item.getTokenGymPass());
                        jsonObject.put("dataAcessoFormatado", item.getDataAcesso_Apresentar());

                        jsonArray.put(jsonObject);
                    }

                    response.getWriter().append(jsonArray.toString());
                }
            } catch (Exception e) {
                processarErro(e, request, response, null);
            }
        }
}
