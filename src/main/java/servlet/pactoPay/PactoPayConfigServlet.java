package servlet.pactoPay;

import br.com.pactosolucoes.comuns.json.JSONMapper;
import br.com.pactosolucoes.integracao.pactopay.front.ConvenioCobrancaDTO;
import br.com.pactosolucoes.integracao.pactopay.front.PactoPayConfigDTO;
import br.com.pactosolucoes.integracao.pactopay.front.PactoPayConfiguracaoEmailDTO;
import br.com.pactosolucoes.integracao.pactopay.front.PactoPayConfiguracaoGymBotDTO;
import br.com.pactosolucoes.integracao.pactopay.front.PactoPayConfiguracaoGymBotProDTO;
import br.com.pactosolucoes.oamd.controle.basico.DAO;
import br.com.pactosolucoes.pactopay.PactoPaySuperService;
import negocio.comuns.arquitetura.EnvelopeRespostaDTO;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.PactoPayConfigVO;
import negocio.comuns.crm.ConfiguracaoSistemaCRMVO;
import negocio.comuns.financeiro.ConvenioCobrancaVO;
import negocio.comuns.financeiro.enumerador.AmbienteEnum;
import negocio.comuns.financeiro.enumerador.SituacaoConvenioCobranca;
import negocio.comuns.financeiro.enumerador.TipoCobrancaEnum;
import negocio.comuns.financeiro.enumerador.TipoEnvioPactoPayEnum;
import negocio.comuns.financeiro.enumerador.TipoTransacaoEnum;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.Usuario;
import negocio.facade.jdbc.basico.PactoPayConfig;
import negocio.facade.jdbc.financeiro.ConvenioCobranca;
import org.json.JSONObject;
import servicos.impl.pagolivre.PagoLivreService;
import servicos.impl.pagolivre.PortalUsersPagoLivreDto;
import servlet.arquitetura.SuperServlet;

import javax.servlet.ServletException;
import javax.servlet.ServletRequest;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;
import java.sql.Connection;
import java.util.ArrayList;
import java.util.List;

/**
 * Created with IntelliJ IDEA.
 * User: Luiz Felipe
 * Date: 22/12/2021
 */
public class PactoPayConfigServlet extends SuperServlet {

    @Override
    protected void processRequest(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        try {
            response.addHeader("Access-Control-Allow-Origin", "*");
            response.addHeader("Access-Control-Allow-Methods", "GET,POST");
            response.addHeader("Access-Control-Allow-Headers", "Authorization,empresaId");
            response.setContentType("application/json");

            String operacao = request.getParameter("op");

            EnvelopeRespostaDTO envelopeRespostaDTO;
            if (operacao.equalsIgnoreCase("consultar")) {
                envelopeRespostaDTO = consultar(request);
            } else if (operacao.equalsIgnoreCase("alterar")) {
                envelopeRespostaDTO = alterar(request);
            } else if (operacao.equalsIgnoreCase("convenios")) {
                envelopeRespostaDTO = consultarConvenios(request);
            } else if (operacao.equalsIgnoreCase("envioteste")) {
                envelopeRespostaDTO = envioTeste(request);
            } else if (operacao.equalsIgnoreCase("testeConfigEmail")) {
                envelopeRespostaDTO = testeConfigEmail(request);
            } else if (operacao.equalsIgnoreCase("alterarConfigEmail")) {
                envelopeRespostaDTO = alterarConfigEmail(request);
            } else if (operacao.equalsIgnoreCase("alterarConfigSMS")) {
                envelopeRespostaDTO = alterarConfigSMS(request);
            } else if (operacao.equalsIgnoreCase("alterarConfigGymBot")) {
                envelopeRespostaDTO = alterarConfigGymBot(request);
            } else if (operacao.equalsIgnoreCase("alterarConfigGymBotPro")) {
                envelopeRespostaDTO = alterarConfigGymBotPro(request);
            } else if (operacao.equalsIgnoreCase("criarEmail")) {
                envelopeRespostaDTO = criarEmail(request);
            } else if (operacao.equalsIgnoreCase("testeEmailFacilitePay")) {
                envelopeRespostaDTO = testeEmailFacilitePay(request);
            } else if (operacao.equalsIgnoreCase("reenviarEmailFacilitePay")) {
                envelopeRespostaDTO = reenviarEmailFacilitePay(request);
            } else if (operacao.equalsIgnoreCase("configuracaoInicialGymBot")) {
                envelopeRespostaDTO = configuracaoInicialGymBot(request);
            } else if(operacao.equalsIgnoreCase("salvarAlteracoesCores")) {
                envelopeRespostaDTO = alterarCoresConfigEmail(request);
            }else{
                throw new Exception("Nenhuma operação executada");
            }

            response.setStatus(HttpServletResponse.SC_OK);
            response.getWriter().append(new JSONObject(envelopeRespostaDTO).toString());
        } catch (Exception ex) {
            ex.printStackTrace();
            response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
            response.getWriter().append(new JSONObject(EnvelopeRespostaDTO.erro("Erro", ex.getMessage())).toString());
        }
    }

    private Connection obterConexao(ServletRequest request) throws Exception {
        String key = request.getParameter("key");
        if (UteisValidacao.emptyString(key)) {
            throw new Exception("Chave não informada.");
        }
        return new DAO().obterConexaoEspecifica(key.trim());
    }

    private void finalizarConexao(Connection con) {
        try {
            if (con != null) {
                con.close();
            }
        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }

    private String obterBody(ServletRequest request) throws Exception {
        StringBuffer body = new StringBuffer();
        try {
            InputStream inputStream = request.getInputStream();
            BufferedReader reader = new BufferedReader(new InputStreamReader(inputStream, StandardCharsets.UTF_8));
            String line = null;

            while ((line = reader.readLine()) != null) {
                body.append(line);
            }
            return body.toString();
        } catch (Exception ex) {
            ex.printStackTrace();
            throw new Exception("Erro body: " + ex.getMessage());
        }
    }

    private EnvelopeRespostaDTO consultar(ServletRequest request) throws Exception {
        Connection con = null;
        PactoPayConfig pactoPayConfigDAO;
        try {
            con = obterConexao(request);
            pactoPayConfigDAO = new PactoPayConfig(con);

            Integer empresa = UteisValidacao.converterInteiro(request.getParameter("empresa"));
            if (UteisValidacao.emptyNumber(empresa)) {
                throw new Exception("Empresa não informada");
            }

            PactoPayConfigDTO dto = pactoPayConfigDAO.consultarParaPactoPay(empresa);
            return EnvelopeRespostaDTO.of(dto);
        } catch (Exception ex) {
            ex.printStackTrace();
            throw ex;
        } finally {
            pactoPayConfigDAO = null;
            finalizarConexao(con);
        }
    }

    private EnvelopeRespostaDTO alterar(ServletRequest request) throws Exception {
        Connection con = null;
        PactoPayConfig pactoPayConfigDAO;
        Usuario usuarioDAO;
        try {
            con = obterConexao(request);
            pactoPayConfigDAO = new PactoPayConfig(con);
            usuarioDAO = new Usuario(con);

            Integer empresa = UteisValidacao.converterInteiro(request.getParameter("empresa"));
            if (UteisValidacao.emptyNumber(empresa)) {
                throw new Exception("Empresa não informada");
            }

            String username = request.getParameter("username");
            Integer usuario = UteisValidacao.converterInteiro(request.getParameter("usuario"));
            if (UteisValidacao.emptyNumber(usuario) && UteisValidacao.emptyString(username)) {
                throw new Exception("Usuário não informado");
            }

            UsuarioVO usuarioVO;
            if (!UteisValidacao.emptyString(username)) {
                usuarioVO = usuarioDAO.consultarPorUsername(username, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
            } else {
                usuarioVO = usuarioDAO.consultarPorChavePrimaria(usuario, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
            }

            if (UteisValidacao.emptyNumber(usuarioVO.getCodigo())) {
                throw new Exception("Usuário não encontrado");
            }

            PactoPayConfigDTO configDTO = JSONMapper.getObject(new JSONObject(obterBody(request)), PactoPayConfigDTO.class);
            pactoPayConfigDAO.alterarConfiguracoes(empresa, usuarioVO, configDTO);
            PactoPayConfigDTO dto = pactoPayConfigDAO.consultarParaPactoPay(empresa);
            return EnvelopeRespostaDTO.of(dto);
        } catch (Exception ex) {
            ex.printStackTrace();
            throw ex;
        } finally {
            pactoPayConfigDAO = null;
            usuarioDAO = null;
            finalizarConexao(con);
        }
    }

    private EnvelopeRespostaDTO consultarConvenios(ServletRequest request) throws Exception {
        Connection con = null;
        ConvenioCobranca convenioCobrancaDAO;
        try {
            con = obterConexao(request);
            convenioCobrancaDAO = new ConvenioCobranca(con);

            Integer empresa = UteisValidacao.converterInteiro(request.getParameter("empresa"));
            if (UteisValidacao.emptyNumber(empresa)) {
                throw new Exception("Empresa não informada");
            }

            String tipo = request.getParameter("tipo");
            if (UteisValidacao.emptyString(tipo)) {
                throw new Exception("Tipo convenio não informado");
            }

            List<ConvenioCobrancaVO> listaConvenio = new ArrayList<>();
            if (tipo.equalsIgnoreCase("retentativa")) {
                listaConvenio = convenioCobrancaDAO.obterListaConvenioCobrancaRetentativa(empresa);
            } else if (tipo.equalsIgnoreCase("pix")) {
                listaConvenio = convenioCobrancaDAO.consultarPorTipoCobranca(new TipoCobrancaEnum[]{TipoCobrancaEnum.PIX},
                        empresa, SituacaoConvenioCobranca.ATIVO, null, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            } else if (tipo.toLowerCase().startsWith("boleto")) {
                TipoCobrancaEnum[] tipos = new TipoCobrancaEnum[]{TipoCobrancaEnum.BOLETO, TipoCobrancaEnum.BOLETO_ONLINE};
                if (tipo.equalsIgnoreCase("boleto_online")) {
                    tipos = new TipoCobrancaEnum[]{TipoCobrancaEnum.BOLETO_ONLINE};
                } else if (tipo.equalsIgnoreCase("boleto_edi")) {
                    tipos = new TipoCobrancaEnum[]{TipoCobrancaEnum.BOLETO};
                }
                listaConvenio = convenioCobrancaDAO.consultarPorTipoCobranca(tipos,
                        empresa, SituacaoConvenioCobranca.ATIVO, null, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            }

            List<ConvenioCobrancaDTO> lista = new ArrayList<>();
            for (ConvenioCobrancaVO obj : listaConvenio) {
                lista.add(new ConvenioCobrancaDTO(obj));
            }
            return EnvelopeRespostaDTO.of(lista);
        } catch (Exception ex) {
            ex.printStackTrace();
            throw ex;
        } finally {
            convenioCobrancaDAO = null;
            finalizarConexao(con);
        }
    }

    private EnvelopeRespostaDTO envioTeste(ServletRequest request) throws Exception {
        Connection con = null;
        PactoPayConfig pactoPayConfigDAO;
        try {
            con = obterConexao(request);
            pactoPayConfigDAO = new PactoPayConfig(con);

            Integer empresa = UteisValidacao.converterInteiro(request.getParameter("empresa"));
            if (UteisValidacao.emptyNumber(empresa)) {
                throw new Exception("Empresa não informada");
            }

            PactoPayConfigVO configVO = pactoPayConfigDAO.consultarPorEmpresa(empresa, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            ConfiguracaoSistemaCRMVO configCRMVO = configVO.getConfiguracaoEmail().obterConfigCRMVO();

            boolean configuracaoEmailValida = configCRMVO.isConfiguracaoEmailValida();

            String email = request.getParameter("email");
            String celular = request.getParameter("celular");
            String meioEnvio = request.getParameter("meioEnvio");

            if (UteisValidacao.emptyString(meioEnvio)) {
                meioEnvio = "TODOS";
            }

            if (UteisValidacao.emptyString(email) &&
                    UteisValidacao.emptyString(celular)) {
                throw new Exception("Necessário informar e-mail ou celular");
            }

            if (!UteisValidacao.emptyString(email)) {
                if (!UteisValidacao.validaEmail(email)) {
                    throw new Exception("E-mail inválido");
                }
                if (!configuracaoEmailValida) {
                    throw new Exception("Configuração e-mail inválida");
                }
            }

            if (!UteisValidacao.emptyString(celular)) {
                if (!Uteis.validarTelefoneCelular(celular)) {
                    throw new Exception("Celular inválido");
                }
            }

            String modelo = request.getParameter("modelo");
            if (UteisValidacao.emptyString(modelo)) {
                throw new Exception("Modelo não informado");
            }

//            modelo tipos
//            aprovada
//            negada
//            cancelada
//            todas

            pactoPayConfigDAO.realizarEnvioExemploResultadoCobranca(empresa, celular, email, modelo, meioEnvio);
            return EnvelopeRespostaDTO.of("Sucesso");
        } catch (Exception ex) {
            ex.printStackTrace();
            throw ex;
        } finally {
            finalizarConexao(con);
            pactoPayConfigDAO = null;
        }
    }

    private EnvelopeRespostaDTO testeConfigEmail(ServletRequest request) throws Exception {
        Connection con = null;
        PactoPayConfig pactoPayConfigDAO;
        try {
            con = obterConexao(request);
            pactoPayConfigDAO = new PactoPayConfig(con);

            Integer empresa = UteisValidacao.converterInteiro(request.getParameter("empresa"));
            String email = request.getParameter("email");

            String body = obterBody(request);
            pactoPayConfigDAO.testeConfigEmail(empresa, email, new PactoPayConfiguracaoEmailDTO(body, true));
            return EnvelopeRespostaDTO.of("Sucesso");
        } catch (Exception ex) {
            ex.printStackTrace();
            throw ex;
        } finally {
            finalizarConexao(con);
            pactoPayConfigDAO = null;
        }
    }

    private EnvelopeRespostaDTO alterarConfigEmail(ServletRequest request) throws Exception {
        Connection con = null;
        PactoPayConfig pactoPayConfigDAO;
        try {
            con = obterConexao(request);
            pactoPayConfigDAO = new PactoPayConfig(con);

            Integer empresa = UteisValidacao.converterInteiro(request.getParameter("empresa"));
            if (UteisValidacao.emptyNumber(empresa)) {
                throw new Exception("Empresa não informada");
            }

            String body = obterBody(request);

            JSONObject jsonBody = new JSONObject(body);
            Integer usuario = jsonBody.optInt("usuario");
            String username = jsonBody.optString("username");

            UsuarioVO usuarioVO = obterUsuarioVO(usuario, username, true, request, con);

            PactoPayConfigVO pactoPayConfigVO = pactoPayConfigDAO.consultarPorEmpresa(empresa, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            pactoPayConfigVO.setConfiguracaoEmail(new PactoPayConfiguracaoEmailDTO(body, false));
            pactoPayConfigDAO.alterarConfiguracaoEmail(empresa, pactoPayConfigVO, usuarioVO);
            return EnvelopeRespostaDTO.of("Sucesso");
        } catch (Exception ex) {
            ex.printStackTrace();
            throw ex;
        } finally {
            finalizarConexao(con);
            pactoPayConfigDAO = null;
        }
    }

    private EnvelopeRespostaDTO criarEmail(ServletRequest request) throws Exception {
        Connection con = null;
        PactoPayConfig pactoPayConfigDAO;
        try {
            con = obterConexao(request);
            pactoPayConfigDAO = new PactoPayConfig(con);

            JSONObject jsonBody = new JSONObject(obterBody(request));

            Integer empresa = jsonBody.optInt("empresa");
            String email = jsonBody.optString("email");
            String remetente = jsonBody.optString("remetente");
            Integer usuario = jsonBody.optInt("usuario");
            String username = jsonBody.optString("username");

            UsuarioVO usuarioVO = obterUsuarioVO(usuario, username, true, request, con);

            pactoPayConfigDAO.criarEmail(empresa, email, remetente, usuarioVO);
            return EnvelopeRespostaDTO.of("Sucesso");
        } catch (Exception ex) {
            ex.printStackTrace();
            throw ex;
        } finally {
            finalizarConexao(con);
            pactoPayConfigDAO = null;
        }
    }

    private EnvelopeRespostaDTO alterarConfigSMS(ServletRequest request) throws Exception {
        Connection con = null;
        PactoPayConfig pactoPayConfigDAO;
        try {
            con = obterConexao(request);
            pactoPayConfigDAO = new PactoPayConfig(con);

            String body = obterBody(request);
            JSONObject jsonBody = new JSONObject(body);
            Integer usuario = jsonBody.optInt("usuario");
            String username = jsonBody.optString("username");

            Integer empresa = UteisValidacao.converterInteiro(request.getParameter("empresa"));
            if (UteisValidacao.emptyNumber(empresa)) {
                empresa = jsonBody.optInt("empresa");
            }

            if (UteisValidacao.emptyNumber(empresa)) {
                throw new Exception("Empresa não informada");
            }

            UsuarioVO usuarioVO = obterUsuarioVO(usuario, username, true, request, con);

            String remetenteSMS = jsonBody.optString("remetenteSMS");

            PactoPayConfigVO pactoPayConfigVO = pactoPayConfigDAO.consultarPorEmpresa(empresa, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            pactoPayConfigVO.getConfiguracaoEmail().setRemetenteSMS(remetenteSMS);
            pactoPayConfigDAO.alterarConfiguracaoEmail(empresa, pactoPayConfigVO, usuarioVO);
            return EnvelopeRespostaDTO.of("Sucesso");
        } catch (Exception ex) {
            ex.printStackTrace();
            throw ex;
        } finally {
            finalizarConexao(con);
            pactoPayConfigDAO = null;
        }
    }

    private UsuarioVO obterUsuarioVO(Integer codigoUsuario, String usernameUsuario, boolean validarUsuario,
                                     ServletRequest request, Connection con) throws Exception {
        Usuario usuarioDAO;
        try {
            usuarioDAO = new Usuario(con);

            if (UteisValidacao.emptyNumber(codigoUsuario)) {
                codigoUsuario = UteisValidacao.converterInteiro(request.getParameter("usuario"));
            }
            if (UteisValidacao.emptyString(usernameUsuario)) {
                usernameUsuario = request.getParameter("username");
            }

            UsuarioVO usuarioVO = null;
            if (!UteisValidacao.emptyString(usernameUsuario)) {
                usuarioVO = usuarioDAO.consultarPorUsername(usernameUsuario, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
            }

            if ((usuarioVO == null || UteisValidacao.emptyNumber(usuarioVO.getCodigo())) &&
                    !UteisValidacao.emptyNumber(codigoUsuario)) {
                usuarioVO = usuarioDAO.consultarPorChavePrimaria(codigoUsuario, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
            }

            if (validarUsuario &&
                    (usuarioVO == null ||UteisValidacao.emptyNumber(usuarioVO.getCodigo()))) {
                throw new Exception("Usuário não informado");
            }
            return usuarioVO;
        } finally {
            usuarioDAO = null;
        }
    }

    private EnvelopeRespostaDTO testeEmailFacilitePay(ServletRequest request) throws Exception {
        Connection con = null;
        PagoLivreService pagoLivreService;
        try {
            con = obterConexao(request);
            pagoLivreService = new PagoLivreService(con, AmbienteEnum.PRODUCAO, TipoTransacaoEnum.FACILITEPAY);

            String email = request.getParameter("email");
            if (UteisValidacao.emptyString(email) || !UteisValidacao.validaEmail(email)) {
                throw new Exception("E-mail inválido");
            }

            PortalUsersPagoLivreDto dto = new PortalUsersPagoLivreDto();
            dto.setEmail(email);
            dto.setPassword("teste123");

            String cnpj = "99.999.999/0001-99";
            String nomeEmpresa = "Empresa de Teste";

            pagoLivreService.enviarEmailUserPagoLivre(email, nomeEmpresa, cnpj, dto);
            return EnvelopeRespostaDTO.of("Email enviado");
        } catch (Exception ex) {
            ex.printStackTrace();
            throw ex;
        } finally {
            finalizarConexao(con);
            pagoLivreService = null;
        }
    }

    private EnvelopeRespostaDTO reenviarEmailFacilitePay(ServletRequest request) throws Exception {
        Connection con = null;
        PagoLivreService pagoLivreService;
        try {
            con = obterConexao(request);
            pagoLivreService = new PagoLivreService(con, AmbienteEnum.PRODUCAO, TipoTransacaoEnum.FACILITEPAY);

            String email = request.getParameter("email");
            if (UteisValidacao.emptyString(email) || !UteisValidacao.validaEmail(email)) {
                throw new Exception("E-mail inválido");
            }

            String senha = request.getParameter("senha");
            if (UteisValidacao.emptyString(senha)) {
                throw new Exception("Pass inválido");
            }

            String cnpj = request.getParameter("cnpj");
            if (UteisValidacao.emptyString(cnpj)) {
                throw new Exception("CNPJ inválido");
            }

            String nomeEmpresa = request.getParameter("empresa");
            if (UteisValidacao.emptyString(nomeEmpresa)) {
                throw new Exception("Nome Empresa inválido");
            }

            PortalUsersPagoLivreDto dto = new PortalUsersPagoLivreDto();
            dto.setEmail(email);
            dto.setPassword(senha);

            pagoLivreService.enviarEmailUserPagoLivre(email, nomeEmpresa, cnpj, dto);
            return EnvelopeRespostaDTO.of("Email enviado");
        } catch (Exception ex) {
            ex.printStackTrace();
            throw ex;
        } finally {
            finalizarConexao(con);
            pagoLivreService = null;
        }
    }

    private EnvelopeRespostaDTO configuracaoInicialGymBot(ServletRequest request) throws Exception {
        Connection con = null;
        PactoPaySuperService pactoPaySuperService;
        try {
            con = obterConexao(request);
            pactoPaySuperService = new PactoPaySuperService(con);

            JSONObject jsonBody = new JSONObject(obterBody(request));

            String url = jsonBody.optString("url");
            TipoEnvioPactoPayEnum tipoEnvioPactoPayEnum = TipoEnvioPactoPayEnum.obterPorId(jsonBody.optInt("tipo"));

            if (!UteisValidacao.emptyString(url)) {
                pactoPaySuperService.configuracaoInicialGymBot(url, tipoEnvioPactoPayEnum);
                return EnvelopeRespostaDTO.of("ok");
            } else {

                String urlCobrancaAntecipada = jsonBody.optString("urlCobrancaAntecipada");
                String urlCobrancaPendente = jsonBody.optString("urlCobrancaPendente");
                String urlCobrancaCartao = jsonBody.optString("urlCobrancaCartao");
                String urlCobrancaResultadoCobranca = jsonBody.optString("urlCobrancaResultadoCobranca");
                if (UteisValidacao.emptyString(urlCobrancaAntecipada) &&
                        UteisValidacao.emptyString(urlCobrancaPendente) &&
                        UteisValidacao.emptyString(urlCobrancaCartao) &&
                        UteisValidacao.emptyString(urlCobrancaResultadoCobranca)) {
                    throw new Exception("URL Webhook não informada");
                }

                if (!UteisValidacao.emptyString(urlCobrancaAntecipada)) {
                    pactoPaySuperService.configuracaoInicialGymBot(urlCobrancaAntecipada, TipoEnvioPactoPayEnum.COBRANCA_ANTECIPADA);
                }
                if (!UteisValidacao.emptyString(urlCobrancaPendente)) {
                    pactoPaySuperService.configuracaoInicialGymBot(urlCobrancaPendente, TipoEnvioPactoPayEnum.PARCELA_PENDENTE);
                }
                if (!UteisValidacao.emptyString(urlCobrancaCartao)) {
                    pactoPaySuperService.configuracaoInicialGymBot(urlCobrancaCartao, TipoEnvioPactoPayEnum.CARTAO_A_VENCER);
                }
                if (!UteisValidacao.emptyString(urlCobrancaResultadoCobranca)) {
                    pactoPaySuperService.configuracaoInicialGymBot(urlCobrancaResultadoCobranca, TipoEnvioPactoPayEnum.RESULTADO_COBRANCA);
                }
            }
            return EnvelopeRespostaDTO.of("ok");
        } catch (Exception ex) {
            ex.printStackTrace();
            throw ex;
        } finally {
            finalizarConexao(con);
            pactoPaySuperService = null;
        }
    }

    private EnvelopeRespostaDTO alterarConfigGymBot(ServletRequest request) throws Exception {
        Connection con = null;
        PactoPayConfig pactoPayConfigDAO;
        try {
            con = obterConexao(request);
            pactoPayConfigDAO = new PactoPayConfig(con);

            String body = obterBody(request);
            JSONObject jsonBody = new JSONObject(body);
            Integer usuario = jsonBody.optInt("usuario");
            String username = jsonBody.optString("username");

            Integer empresa = UteisValidacao.converterInteiro(request.getParameter("empresa"));
            if (UteisValidacao.emptyNumber(empresa)) {
                empresa = jsonBody.optInt("empresa");
            }

            if (UteisValidacao.emptyNumber(empresa)) {
                throw new Exception("Empresa não informada");
            }

            UsuarioVO usuarioVO = obterUsuarioVO(usuario, username, true, request, con);

            String urlCobrancaAntecipada = jsonBody.optString("urlCobrancaAntecipada");
            String urlCobrancaPendente = jsonBody.optString("urlCobrancaPendente");
            String urlCobrancaCartao = jsonBody.optString("urlCobrancaCartao");
            String urlCobrancaResultadoCobranca = jsonBody.optString("urlCobrancaResultadoCobranca");

            PactoPayConfiguracaoGymBotDTO novaConfig = new PactoPayConfiguracaoGymBotDTO();
            novaConfig.setUrlCobrancaAntecipada(urlCobrancaAntecipada);
            novaConfig.setUrlCobrancaPendente(urlCobrancaPendente);
            novaConfig.setUrlCobrancaCartao(urlCobrancaCartao);
            novaConfig.setUrlCobrancaResultadoCobranca(urlCobrancaResultadoCobranca);

            PactoPayConfigVO pactoPayConfigVO = pactoPayConfigDAO.consultarPorEmpresa(empresa, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            pactoPayConfigVO.setConfiguracaoGymBot(novaConfig);
            pactoPayConfigDAO.alterarConfiguracaoGymBot(empresa, pactoPayConfigVO, usuarioVO);
            return EnvelopeRespostaDTO.of("Sucesso");
        } catch (Exception ex) {
            ex.printStackTrace();
            throw ex;
        } finally {
            finalizarConexao(con);
            pactoPayConfigDAO = null;
        }
    }

    private EnvelopeRespostaDTO alterarConfigGymBotPro(ServletRequest request) throws Exception {
        Connection con = null;
        PactoPayConfig pactoPayConfigDAO;
        try {

            con = obterConexao(request);
            pactoPayConfigDAO = new PactoPayConfig(con);

            String body = obterBody(request);
            JSONObject jsonBody = new JSONObject(body);
            Integer usuario = jsonBody.optInt("usuario");
            String username = jsonBody.optString("username");

            Integer empresa = UteisValidacao.converterInteiro(request.getParameter("empresa"));
            if (UteisValidacao.emptyNumber(empresa)) {
                empresa = jsonBody.optInt("empresa");
            }

            if (UteisValidacao.emptyNumber(empresa)) {
                throw new Exception("Empresa não informada");
            }

            UsuarioVO usuarioVO = obterUsuarioVO(usuario, username, true, request, con);

            String tokenCobrancaAntecipada = jsonBody.optString("tokenCobrancaAntecipada");
            String idIntegracaoCobrancaAntecipada = jsonBody.optString("idIntegracaoCobrancaAntecipada");

            String tokenComunicadoResultadoCobranca = jsonBody.optString("tokenComunicadoResultadoCobranca");
            String idIntegracaoComunicadoResultadoCobranca = jsonBody.optString("idIntegracaoComunicadoResultadoCobranca");

            String tokenComunicadoAtraso = jsonBody.optString("tokenComunicadoAtraso");
            String idIntegracaoComunicadoAtraso = jsonBody.optString("idIntegracaoComunicadoAtraso");

            String tokenComunicadoCartao = jsonBody.optString("tokenComunicadoCartao");
            String idIntegracaoComunicadoCartao = jsonBody.optString("idIntegracaoComunicadoCartao");

            PactoPayConfiguracaoGymBotProDTO newPactoPayConfiguracaoGymBotProDTO = new PactoPayConfiguracaoGymBotProDTO();
            newPactoPayConfiguracaoGymBotProDTO.setTokenCobrancaAntecipada(tokenCobrancaAntecipada);
            newPactoPayConfiguracaoGymBotProDTO.setIdIntegracaoCobrancaAntecipada(idIntegracaoCobrancaAntecipada);
            newPactoPayConfiguracaoGymBotProDTO.setTokenComunicadoResultadoCobranca(tokenComunicadoResultadoCobranca);
            newPactoPayConfiguracaoGymBotProDTO.setIdIntegracaoComunicadoResultadoCobranca(idIntegracaoComunicadoResultadoCobranca);
            newPactoPayConfiguracaoGymBotProDTO.setTokenComunicadoAtraso(tokenComunicadoAtraso);
            newPactoPayConfiguracaoGymBotProDTO.setIdIntegracaoComunicadoAtraso(idIntegracaoComunicadoAtraso);
            newPactoPayConfiguracaoGymBotProDTO.setTokenComunicadoCartao(tokenComunicadoCartao);
            newPactoPayConfiguracaoGymBotProDTO.setIdIntegracaoComunicadoCartao(idIntegracaoComunicadoCartao);

            PactoPayConfigVO pactoPayConfigVO = pactoPayConfigDAO.consultarPorEmpresa(empresa, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            pactoPayConfigVO.setConfiguracaoGymBotPro(newPactoPayConfiguracaoGymBotProDTO);
            pactoPayConfigDAO.alterarConfiguracaoGymBotPro(empresa, pactoPayConfigVO, usuarioVO);
            return EnvelopeRespostaDTO.of("Sucesso");

        } catch (Exception ex) {
            ex.printStackTrace();
            throw ex;
        }finally {
            finalizarConexao(con);
            pactoPayConfigDAO = null;
        }
    }

    private EnvelopeRespostaDTO alterarCoresConfigEmail(ServletRequest request) throws Exception {
        Connection con = null;
        PactoPayConfig pactoPayConfigDAO;
        try {
            con = obterConexao(request);
            pactoPayConfigDAO = new PactoPayConfig(con);

            Integer empresa = UteisValidacao.converterInteiro(request.getParameter("empresa"));
            if (UteisValidacao.emptyNumber(empresa)) {
                throw new Exception("Empresa não informada");
            }

            String body = obterBody(request);

            JSONObject jsonBody = new JSONObject(body);
            Integer usuario = jsonBody.optInt("usuario");
            String username = jsonBody.optString("username");

            UsuarioVO usuarioVO = obterUsuarioVO(usuario, username, true, request, con);

            String cssCorEmail = jsonBody.optString("cssCorEmail");
            String cssCorBtnPagar = jsonBody.optString("cssCorBtnPagar");
            String cssCorBtnComprovante = jsonBody.optString("cssCorBtnComprovante");
            String cssCorBtnCadastrar = jsonBody.optString("cssCorBtnCadastrar");

            PactoPayConfigVO pactoPayConfigVO = pactoPayConfigDAO.consultarPorEmpresa(empresa, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            pactoPayConfigVO.getConfiguracaoEmail().setCssCorEmail(cssCorEmail);
            pactoPayConfigVO.getConfiguracaoEmail().setCssCorBtnPagar(cssCorBtnPagar);
            pactoPayConfigVO.getConfiguracaoEmail().setCssCorBtnComprovante(cssCorBtnComprovante);
            pactoPayConfigVO.getConfiguracaoEmail().setCssCorBtnCadastrar(cssCorBtnCadastrar);

            pactoPayConfigDAO.alterarConfiguracaoEmail(empresa, pactoPayConfigVO, usuarioVO);
            return EnvelopeRespostaDTO.of("Sucesso");
        } catch (Exception ex) {
            ex.printStackTrace();
            throw ex;
        } finally {
            finalizarConexao(con);
            pactoPayConfigDAO = null;
        }
    }
}
