package servicos.vendasonline;

import controle.arquitetura.security.LoginControle;
import negocio.comuns.arquitetura.LogVO;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.arquitetura.Log;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.arquitetura.Usuario;
import negocio.facade.jdbc.basico.Cliente;
import negocio.modulos.integracao.usuariomovel.UsuarioMovelVO;
import org.json.JSONObject;
import servicos.http.MetodoHttpEnum;
import servicos.http.RequestHttpService;
import servicos.http.RespostaHttpDTO;
import servicos.integracao.TreinoWSConsumer;
import servicos.legolas.LegolasService;

import java.sql.Connection;
import java.sql.ResultSet;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class ClienteService {

    public void excluirClienteVendas(String cpf, Connection con) throws Exception {
        ResultSet rs = SuperFacadeJDBC.criarConsulta("select matricula from cliente c " +
                " inner join pessoa p on c.pessoa = p.codigo " +
                " where p.cfp = '" + cpf + "' ", con);
        Usuario usuarioDao = new Usuario(con);
        UsuarioVO usuarioAdmin = usuarioDao.consultarPorChavePrimaria(1, Uteis.NIVELMONTARDADOS_MINIMOS);
        if(rs.next()){
            String matricula = rs.getString("matricula");
            Cliente cliDao = new Cliente(con);
            List<ClienteVO> lista = cliDao.consultarPorCodigosMatricula(matricula, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            for (ClienteVO clienteVO : lista) {
                cliDao.excluirClienteETodosSeusRelacionamentos(clienteVO, usuarioAdmin);
                incluirLog(matricula,
                        "CLIENTE",
                        "CLIENTE TESTE VENDAS ONLINE",
                        "ENDPOINT EXCLUSÃO",
                        "EXCLUIR CLIENTE TESTE",
                        "TODOS",
                        con);
            }
        }
    }

    public boolean validarValorParcelaClienteVendas(String cpf, double valor, Integer codPlano, Connection con) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("select sum(mp.valorparcela) from cliente c ");
        sql.append(" inner join pessoa p on c.pessoa = p.codigo ");
        sql.append(" inner join contrato co on co.pessoa = p.codigo ");
        sql.append("inner join plano pl on pl.codigo = co.plano ");
        sql.append(" inner join movparcela mp on mp.contrato = co.codigo ");
        sql.append(" where p.cfp ilike '" + cpf + "' ");
        sql.append(" and pl.codigo = ").append(codPlano);
        sql.append(" and (mp.descricao = 'PARCELA 1' or mp.descricao ilike 'ADES%') ");

        ResultSet rs = SuperFacadeJDBC.criarConsulta(sql.toString(), con);
        if(rs.next()){
            if(Uteis.arredondarForcando2CasasDecimais(valor) == Uteis.arredondarForcando2CasasDecimais(rs.getDouble("sum"))){
                return true;
            }
        }
        return false;
    }

    public static void incluirLog(String chavePrimaria,
                                  String entidade,
                                  String descricao,
                                  String reponsavel,
                                  String operacao,
                                  String campo,
                                  Connection c) {
        LogVO obj = new LogVO();
        obj.setChavePrimaria(chavePrimaria);
        obj.setNomeEntidade(entidade);
        obj.setNomeEntidadeDescricao(descricao);
        obj.setOperacao(operacao);
        obj.setResponsavelAlteracao(reponsavel);
        obj.setNomeCampo(campo);
        obj.setDataAlteracao(Calendario.hoje());
        obj.setValorCampoAnterior("");
        obj.setValorCampoAlterado("");

        try {
            Log dao = new Log(c);
            dao.incluirSemCommit(obj);
        } catch (Exception ignored) {

        }
    }
}
