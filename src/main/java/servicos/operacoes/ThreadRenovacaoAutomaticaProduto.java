package servicos.operacoes;

import negocio.comuns.arquitetura.LogVO;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.basico.ColaboradorVO;
import negocio.comuns.contrato.MovProdutoVO;
import negocio.comuns.financeiro.ItemVendaAvulsaVO;
import negocio.comuns.financeiro.VendaAvulsaVO;
import negocio.comuns.plano.ConfiguracaoProdutoEmpresaVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.comuns.utilitarias.queue.MsgTO;
import negocio.facade.jdbc.arquitetura.Log;
import negocio.facade.jdbc.arquitetura.ZillyonWebFacade;
import negocio.facade.jdbc.financeiro.VendaAvulsa;
import negocio.facade.jdbc.plano.ConfiguracaoProdutoEmpresa;
import negocio.facade.jdbc.utilitarias.Conexao;

import java.sql.Connection;

public class ThreadRenovacaoAutomaticaProduto extends Thread {

    private MovProdutoVO movProdutoVO;

    private Connection con;
    private StringBuffer sb = new StringBuffer();

    public ThreadRenovacaoAutomaticaProduto(MovProdutoVO movProdutoVO, Connection con) {
        this.movProdutoVO = movProdutoVO;
        this.con = con;
    }
    private void logar(String mensagem) {
        Uteis.logar(sb, this.getName() + " - " + mensagem);
    }

    public void run() {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            ZillyonWebFacade zwFacade = new ZillyonWebFacade(c);
            VendaAvulsa vendaAvulsaDao = new VendaAvulsa(c);
            long inicio = System.currentTimeMillis();
            logar(String.format("Iniciando Renovação Automática do movproduto %s da pessoa %s",
                    movProdutoVO.getCodigo(),
                    movProdutoVO.getPessoa().getNome()));
            VendaAvulsaVO vendaAvulsaVO = new VendaAvulsaVO();
            ClienteVO clienteVO = zwFacade.getCliente().consultarPorCodigoPessoa(movProdutoVO.getPessoa().getCodigo(),Uteis.NIVELMONTARDADOS_MINIMOS);
            if(UteisValidacao.notEmptyNumber(clienteVO.getCodigo())){
                vendaAvulsaVO.setTipoComprador("CI");
                vendaAvulsaVO.setCliente(clienteVO);
                vendaAvulsaVO.setNomeComprador(vendaAvulsaVO.getCliente().getPessoa().getNome());
            } else {
                ColaboradorVO colaboradorVO = zwFacade.getColaborador().consultarPorCodigoPessoa(movProdutoVO.getPessoa().getCodigo(),0, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                if(UteisValidacao.notEmptyNumber(colaboradorVO.getCodigo())) {
                    vendaAvulsaVO.setTipoComprador("CO");
                    vendaAvulsaVO.setColaborador(colaboradorVO);
                    vendaAvulsaVO.setNomeComprador(vendaAvulsaVO.getColaborador().getPessoa().getNome());
                } else {
                    throw new Exception("Não foi encontrato cliente ou colaborador para essa venda");
                }
            }
            ConfiguracaoProdutoEmpresa configuracaoProdutoEmpresaDao = new ConfiguracaoProdutoEmpresa(c);
            ConfiguracaoProdutoEmpresaVO cfgEmpresa = configuracaoProdutoEmpresaDao.consultarPorProdutoEmpresa(movProdutoVO.getProduto().getCodigo(), movProdutoVO.getEmpresa().getCodigo());
            if(cfgEmpresa != null){
                movProdutoVO.getProduto().setValorFinal(cfgEmpresa.getValor());
                movProdutoVO.getProduto().setValorBaseCalculo(cfgEmpresa.getValor());
            }

            vendaAvulsaVO.setDataRegistro(Calendario.hoje());
            vendaAvulsaVO.setEmpresa(movProdutoVO.getEmpresa());

            ItemVendaAvulsaVO item = new ItemVendaAvulsaVO();
            item.setDataVenda(Uteis.somarDias(movProdutoVO.getDataFinalVigencia(), 1));
            item.setDataValidade(Uteis.obterDataFutura2(item.getDataVenda(), movProdutoVO.getProduto().getNrDiasVigencia()));
            item.setVigenciaJaCalculada(true);
            item.setQuantidade(1);
            item.setUsuarioVO(zwFacade.getUsuarioRecorrencia());
            item.setProduto(movProdutoVO.getProduto());
            item.setValorParcial(movProdutoVO.getProduto().getValorFinal());
            item.getProduto().setValorFinal(movProdutoVO.getProduto().getValorFinal());
            vendaAvulsaVO.setValorTotal(movProdutoVO.getProduto().getValorFinal());

            vendaAvulsaVO.getItemVendaAvulsaVOs().add(item);
            vendaAvulsaVO.setResponsavel(item.getUsuarioVO());
            vendaAvulsaVO.setVencimentoPrimeiraParcela(item.getDataVenda());

            vendaAvulsaDao.incluirSemCommit(vendaAvulsaVO, false, item.getDataVenda());
            gerarLog(vendaAvulsaVO, vendaAvulsaDao);
            gerarLogRenovacao(vendaAvulsaVO, c);
            long fim = System.currentTimeMillis();
            logar(String.format("Renovação Concluída do movproduto %s da pessoa %s (%s ms)",
                            movProdutoVO.getCodigo(),
                    movProdutoVO.getPessoa().getCodigo(),
                    (fim - inicio)));

        } catch (Exception ex) {
            logar(String.format("Erro ao renovar automaticamente o movproduto %s deviado ao erro: %s",
                    movProdutoVO.getCodigo(), ex.getMessage()));
            UteisValidacao.enfileirarEmail(new MsgTO(movProdutoVO.getEmpresa().getNome(), "Renovação Automática", sb));
        }
    }

    private void gerarLog(VendaAvulsaVO vendaAvulsaVO, VendaAvulsa vendaAvulsaDao) {
        //LOG - INICIO
        try {
            vendaAvulsaVO.setObjetoVOAntesAlteracao(new VendaAvulsaVO());
            vendaAvulsaVO.setNovoObj(true);
            if (vendaAvulsaVO.getTipoComprador().equals("CI")) {
                vendaAvulsaDao.registrarLogObjetoVOGeralAlterandoResponsavel(vendaAvulsaVO, vendaAvulsaVO.getCodigo(), "VENDAAVULSA", vendaAvulsaVO.getCliente().getPessoa().getCodigo(), false, "", vendaAvulsaVO.getResponsavel());
            } else if (vendaAvulsaVO.getTipoComprador().equals("CO")) {
                vendaAvulsaDao.registrarLogObjetoVOGeralAlterandoResponsavel(vendaAvulsaVO, vendaAvulsaVO.getCodigo(), "VENDAAVULSA", vendaAvulsaVO.getColaborador().getPessoa().getCodigo(), false, "", vendaAvulsaVO.getResponsavel());
            } else {
                vendaAvulsaDao.registrarLogObjetoVOGeralAlterandoResponsavel(vendaAvulsaVO, vendaAvulsaVO.getCodigo(), "VENDAAVULSA", 0, false, "", vendaAvulsaVO.getResponsavel());
            }
        } catch (Exception ex) {
            ex.printStackTrace();
            Uteis.logarDebug("Erro ao gerarLog ThreadRenovacaoAutomaticaProduto | " + ex.getMessage());
        }
    }

    private void gerarLogRenovacao(VendaAvulsaVO vendaAvulsaVO, Connection con) {
        Log logDAO;
        try {
            logDAO = new Log(con);

            LogVO log = new LogVO();
            log.setNomeEntidade("VENDAAVULSA");
            log.setNomeEntidadeDescricao("VENDAAVULSA");
            log.setDescricao("RENOVAcao AUTOMATICA - PRODUTO");
            log.setChavePrimaria(vendaAvulsaVO.getCodigo().toString());
            log.setDataAlteracao(Calendario.hoje());
            log.setOperacao("INCLUSÃO");
            log.setNomeCampo("GERAL");
            try {
                log.setUsuarioVO(vendaAvulsaVO.getResponsavel());
                log.setResponsavelAlteracao(vendaAvulsaVO.getResponsavel().getNomeAbreviado());
            } catch (Exception ex) {
                ex.printStackTrace();
            }
            log.setPessoa(vendaAvulsaVO.getPessoaVO().getCodigo());
            log.setValorCampoAnterior("");
            log.setValorCampoAlterado("Renovação Automática de produto -  " + this.movProdutoVO.getCodigo() + " - " + this.movProdutoVO.getDescricao());
            logDAO.incluirSemCommit(log);
        } catch (Exception ex) {
            ex.printStackTrace();
            Uteis.logarDebug("Erro ao gerarLogRenovacao ThreadRenovacaoAutomaticaProduto | " + ex.getMessage());
        } finally {
            logDAO = null;
        }
    }
}
