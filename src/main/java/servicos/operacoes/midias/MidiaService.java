/*
 * To change this template, choose Too<PERSON> | Templates
 * and open the template in the editor.
 */
package servicos.operacoes.midias;

import br.com.pactosolucoes.comuns.util.FileUtilities;
import servicos.operacoes.midias.commons.MidiaConectividadeEnum;
import servicos.operacoes.midias.commons.MidiaEntidadeEnum;
import servicos.propriedades.PropsService;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;

/**
 *
 * <AUTHOR>
 */
public abstract class MidiaService {

    private static MidiaService instance = null;
    private static MidiaService instancePdf = null;
    /**
     * Gera um identificador único de armazenamento, baseado no tipo de mídia e
     * retorna a URL para download
     *
     * @param chave
     * @param tipo
     * @param identificador
     * @return
     * @throws Exception
     */
    public abstract String downloadObject(final String chave, MidiaEntidadeEnum tipo,
            final String identificador) throws Exception;

    /**
     * Gera um identificador único de armazenamento, baseado no tipo de mídia,
     * realiza o download do vetor de bytes e retorna-o
     *
     * @param chave
     * @param tipo
     * @param identificador
     * @param key
     * @return
     * @throws Exception
     */
    public abstract byte[] downloadObjectAsByteArray(final String chave,
                                                     MidiaEntidadeEnum tipo, final String identificador, String key) throws Exception;

    public abstract byte[] downloadObjectWithExtensionAsByteArray(final String chave, MidiaEntidadeEnum tipo, final String identificador, final String extensao, String key) throws Exception;

    /**
     * Verifica se determinada midia com identificador único existe no
     * armazenamento, retornando VERDADEIRO em caso positivo
     *
     * @param key
     * @return
     */
    public abstract boolean exists(final String key);

    /**
     * Realiza upload de um arquivo a partir do seu descritor 'file'
     *
     * @param chave
     * @param tipo
     * @param identificador
     * @param file
     * @return
     * @throws Exception
     */
    public abstract String uploadObject(final String chave, MidiaEntidadeEnum tipo,
            final String identificador, File file) throws Exception;


    public abstract String uploadObjectWithExtension(final String chave, MidiaEntidadeEnum tipo,
                                        final String identificador, File file, String extensao) throws Exception;
    /**
     * Realiza upload de um arquivo a partir do seu conteúdo em vetor de bytes
     *
     * @param chave
     * @param tipo
     * @param identificador
     * @param bytes
     * @return
     * @throws Exception
     */
    public abstract String uploadObjectFromByteArray(final String chave,
            MidiaEntidadeEnum tipo, final String identificador, byte[] bytes) throws Exception;

    public abstract String uploadObjectFromByteArray(final String chave, MidiaEntidadeEnum tipo,
                                            final String identificador, byte[] bytes, boolean encriptarIdentificador) throws Exception;

    /**
     * Deleta da camada de armazenamento o arquivo associado
     *
     * @param key
     */
    public abstract void deleteObject(final String key);

    /**
     * Realiza a geração da chave
     */
    public abstract String genKey(final String chave, MidiaEntidadeEnum tipo, final String identifier) throws Exception;

    public abstract String genKey(final String chave, MidiaEntidadeEnum tipo, final String identifier, final String extensao) throws Exception;

    /**
     * Retorna instância Singleton da implementação do serviço de Midias
     * especificada pela configuração do ambiente
     *
     * @return
     * @throws Exception
     */
    public static MidiaService getInstancePdf() throws Exception {
        if (instancePdf == null) {
            MidiaConectividadeEnum type =
                    MidiaConectividadeEnum.valueOf(
                    "AWS_S3");
            instancePdf = (MidiaService) type.getImpl().newInstance();
        }
        return instancePdf;
    }
    
    public static MidiaService getInstance() throws Exception {
        if (instance == null) {
            MidiaConectividadeEnum type =
                    MidiaConectividadeEnum.valueOf(
                    PropsService.getPropertyValue(PropsService.typeMidiasService));
            instance = (MidiaService) type.getImpl().newInstance();
        }
        return instance;
    }
    
    public abstract void moveObject(final String key, final String keyDestino);

    public abstract void deleteObject(final String chave, MidiaEntidadeEnum tipo, final String identificador) throws Exception;

    protected File droparAlphaChannel(MidiaEntidadeEnum tipo, File file) throws IOException {
        if (tipo.getDroparAlphaChannel()) {
            BufferedImage originalImage = ImageIO.read(file);
            originalImage = dropAlphaChannel(originalImage);

            ByteArrayOutputStream baos = new ByteArrayOutputStream();
            ImageIO.write(originalImage, "jpg", baos);
            baos.flush();
            byte[] imageInByte = baos.toByteArray();
            baos.close();
            FileUtilities.saveToFile(imageInByte, file.getPath());
            return file;
        }
        return file;
    }

    protected byte[] droparAlphaChannel(MidiaEntidadeEnum tipo, byte[] bytes) throws IOException {
        if (tipo.getDroparAlphaChannel()) {
            InputStream in = new ByteArrayInputStream(bytes);
            BufferedImage originalImage = ImageIO.read(in);
            originalImage = dropAlphaChannel(originalImage);

            ByteArrayOutputStream baos = new ByteArrayOutputStream();
            ImageIO.write(originalImage, "jpg", baos);
            baos.flush();
            bytes = baos.toByteArray();
            baos.close();
        }
        return bytes;
    }

    protected BufferedImage dropAlphaChannel(BufferedImage src) {
        BufferedImage convertedImg = new BufferedImage(src.getWidth(), src.getHeight(), BufferedImage.TYPE_INT_RGB);
        convertedImg.getGraphics().drawImage(src, 0, 0, null);

        return convertedImg;
    }
      
}
