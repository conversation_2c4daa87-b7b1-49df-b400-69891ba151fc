/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package servicos.operacoes;

import java.sql.Connection;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

import br.com.pactosolucoes.enumeradores.BIEnum;
import br.com.pactosolucoes.enumeradores.ConfiguracaoBIEnum;
import negocio.comuns.basico.ConfiguracaoBIVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.basico.ConfiguracaoBI;
import negocio.facade.jdbc.basico.ContratoDependente;
import negocio.facade.jdbc.contrato.Contrato;
import relatorio.negocio.comuns.sad.RotatividadeAnaliticoDWVO;
import relatorio.negocio.comuns.sad.RotatividadeSinteticoDWVO;

/**
 *
 * <AUTHOR>
 */
public class RotatividadeService {

    private final Connection con;

    public RotatividadeService(Connection con) {
        this.con = con;
    }

    public RotatividadeSinteticoDWVO montarRelatorioRotatividadeTelaInicial(Integer empresa, Date data) throws Exception {
        Contrato contratoDao = new Contrato(con);

        RotatividadeSinteticoDWVO sintetico = new RotatividadeSinteticoDWVO();
        Date dataIn = Uteis.obterPrimeiroDiaMes(data);
        Date dataFin;

        Calendar cal = Calendario.getInstance();
        cal.setTime(dataIn);
        cal.add(Calendar.DAY_OF_MONTH, -1);

        dataIn = Uteis.obterPrimeiroDiaMes(cal.getTime());
        dataFin = Uteis.obterUltimoDiaMesUltimaHora(cal.getTime());

        String dataInicio = Uteis.getDataFormatoBD(dataIn);
        String dataFim = Uteis.getDataFormatoBD(dataFin);

        sintetico.setQtdTotal(0);
        sintetico.setQtdVigentesMesAnterior(0);
        sintetico.setQtdVencido(0);
        sintetico.setQtdTotal(0);
        sintetico.setQtdeVigenteMesAtual(0);

        ConfiguracaoBI dao = new ConfiguracaoBI(con);
        List<ConfiguracaoBIVO> configuracoes = dao.consultarPorBI(BIEnum.TICKET_MEDIO, empresa);
        Boolean desconsiderarAlunosInadimplentes = getConfiguracao(ConfiguracaoBIEnum.FILTRAR_PARCELAS_VENCIDAS_NAO_PAGAS, configuracoes).getValorAsBoolean();
        dao = null;

        String cEmpresa = (UteisValidacao.emptyNumber(empresa) ? "" : (" and c.empresa = " + empresa));
        String cliEmpresa = (UteisValidacao.emptyNumber(empresa) ? "" : (" and cli.empresa = " + empresa));

        sintetico.setQtdVigentesMesAnterior(contratoDao.contar(
                String.format((desconsiderarAlunosInadimplentes ? Contrato.sqlAtivosVencidosSemInadimplenciaContar : Contrato.sqlAtivosVencidosContar),
                        cEmpresa,
                        dataFim,
                        dataFim,
                        dataFim,
                        dataFim,
                        dataFim,
                        dataFim,
                        dataFim,
                        Uteis.getMesReferenciaData(dataFin))));

        sintetico.setQtdVencido(contratoDao.contar(
                String.format((desconsiderarAlunosInadimplentes ? Contrato.sqlVencidosInicioMesSemInadimplenciaContar : Contrato.sqlVencidosInicioMesContar),
                        cEmpresa,
                        dataInicio,
                        dataFim,
                        dataInicio,
                        dataFim,
                        dataInicio,
                        dataFim,
                        Uteis.getMesReferenciaData(dataFin))));

        sintetico.setQtdDependentesMesAtual(
                contratoDao.contar(
                        String.format(ContratoDependente.SQL_DEPENDENTES_VIGENTE_CONTAR,
                                Uteis.getDataJDBC(Uteis.obterPrimeiroDiaMes(data)),
                                Uteis.getDataJDBC(Uteis.obterPrimeiroDiaMes(data)),
                                cliEmpresa)
                )
        );

        sintetico.setQtdDependentesFinalMesAtual(
                contratoDao.contar(
                        String.format(ContratoDependente.SQL_DEPENDENTES_VIGENTE_CONTAR,
                                Uteis.getDataJDBC(Uteis.obterUltimoDiaMes(data)),
                                Uteis.getDataJDBC(Uteis.obterUltimoDiaMes(data)),
                                cliEmpresa)
                )
        );

        sintetico.setQtdVigentesMesAnterior(sintetico.getQtdVigentesMesAnterior() + sintetico.getQtdVencido());

        sintetico.setQtdTotal(sintetico.getQtdVigentesMesAnterior());

        sintetico.setQtdeVigenteMesAtual(sintetico.getQtdVigentesMesAnterior() - sintetico.getQtdVencido());
        
        RotatividadeAnaliticoDWVO analitico = montarRotatividadeAnaliticoMes(empresa, data, contratoDao, desconsiderarAlunosInadimplentes);
        
        sintetico.setQtdTotal(sintetico.getQtdTotal() + analitico.getQtdSaldo());
        
        sintetico.setQtdeFinalMesAtual(sintetico.getQtdTotal() - analitico.getQtdVencidoMes());

        return sintetico;

    }

    private ConfiguracaoBIVO getConfiguracao(ConfiguracaoBIEnum cfg, List<ConfiguracaoBIVO> cfgs){
        for(ConfiguracaoBIVO c : cfgs){
            if(c.getConfiguracao().equals(cfg)){
                return c;
            }
        }
        return null;
    }

    public RotatividadeAnaliticoDWVO montarRotatividadeAnaliticoMes(Integer empresa, Date data, Contrato contratoDao, Boolean desconsiderarAlunosInadimplentes) throws Exception {
        RotatividadeAnaliticoDWVO analitico = new RotatividadeAnaliticoDWVO();
        //para efeito de cálculo do mês será considerado a data inicio o primeiro dia do mês da data base selecionada
        //e, como data final até a zero-hora da data base selecionada
        Date dataIn = Uteis.obterPrimeiroDiaMes(data);

        Date dataFinAnterior = Uteis.obterDataAnterior(dataIn, 1);
        Date dataInAnterior = Uteis.obterPrimeiroDiaMes(dataFinAnterior);


        String dataInicioAnterior = Uteis.getDataFormatoBD(dataInAnterior);
        String dataFimAnterior = Uteis.getDataFormatoBD(dataFinAnterior);

        String dataInicio = Uteis.getDataFormatoBD(dataIn);
        String dataFim = Uteis.getDataFormatoBD(data);

        String cEmpresa = (UteisValidacao.emptyNumber(empresa) ? "" : (" and c.empresa = " + empresa));
        String cliEmpresa = UteisValidacao.emptyNumber(empresa) ? "" : (" and cli.empresa = " + empresa);

        analitico.setQtdMatriculado(
                contratoDao.contar(
                        String.format((desconsiderarAlunosInadimplentes ? Contrato.sqlMatriculadosSemInadimplenciaContar : Contrato.sqlMatriculadosContar),
                                cEmpresa,
                                dataInicio,
                                dataFim,
                                dataInicio,
                                dataFim,
                                Uteis.getMesReferenciaData(data))));

        analitico.setQtdMatriculadoHoje(
                contratoDao.contar(
                        String.format((desconsiderarAlunosInadimplentes ? Contrato.sqlMatriculadosSemInadimplenciaContar : Contrato.sqlMatriculadosContar),
                                cEmpresa,
                                dataFim,
                                dataFim,
                                dataFim,
                                dataFim,
                                Uteis.getMesReferenciaData(data))));

        analitico.setQtdRematriculado(
                contratoDao.contar(
                        String.format((desconsiderarAlunosInadimplentes ? Contrato.sqlRematriculadosSemInadimplenciaContar : Contrato.sqlRematriculadosContar),
                                cEmpresa,
                                dataInicio,
                                dataFim,
                                dataInicio,
                                dataFim,
                                Uteis.getMesReferenciaData(data))));

        analitico.setQtdRematriculadoHoje(
                contratoDao.contar(
                        String.format((desconsiderarAlunosInadimplentes ? Contrato.sqlRematriculadosSemInadimplenciaContar : Contrato.sqlRematriculadosContar),
                                cEmpresa,
                                dataFim,
                                dataFim,
                                dataFim,
                                dataFim,
                                Uteis.getMesReferenciaData(data))));

        analitico.setQtdCancelado(
                contratoDao.contar(
                        String.format((desconsiderarAlunosInadimplentes ? Contrato.sqlCanceladosSemInadimplenciaContar : Contrato.sqlCanceladosContar),
                                cEmpresa,
                                dataInicio,
                                dataFim,
                                dataInicio,
                                dataFim,
                                Uteis.getMesReferenciaData(data))));

        analitico.setQtdCanceladoHoje(
                contratoDao.contar(
                        String.format((desconsiderarAlunosInadimplentes ? Contrato.sqlCanceladosSemInadimplenciaContar : Contrato.sqlCanceladosContar),
                                cEmpresa,
                                dataFim,
                                dataFim,
                                dataFim,
                                dataFim,
                                Uteis.getMesReferenciaData(data))));

        analitico.setQtdDesistente(
                contratoDao.contar(
                        String.format((desconsiderarAlunosInadimplentes ? Contrato.sqlDesistentesSemInadimplenciaContar : Contrato.sqlDesistentesContar),
                                cEmpresa,
                                dataInicio,
                                dataFim,
                                dataInicio,
                                dataFim,
                                Uteis.getMesReferenciaData(data))));

        analitico.setQtdDesistenteHoje(
                contratoDao.contar(
                        String.format((desconsiderarAlunosInadimplentes ? Contrato.sqlDesistentesSemInadimplenciaContar : Contrato.sqlDesistentesContar),
                                cEmpresa,
                                dataFim,
                                dataFim,
                                dataFim,
                                dataFim,
                                Uteis.getMesReferenciaData(data))));

        analitico.setQtdRetornoTrancamento(
                contratoDao.contar(
                        String.format((desconsiderarAlunosInadimplentes ? Contrato.sqlRetornoTrancamentosSemInadimplenciaContar : Contrato.sqlRetornoTrancamentosContar),
                                cEmpresa,
                                dataInicio,
                                dataFim,
                                dataInicio,
                                dataFim,
                                Uteis.getMesReferenciaData(data))));

        analitico.setQtdRetornoTrancamentoHoje(
                contratoDao.contar(
                        String.format((desconsiderarAlunosInadimplentes ? Contrato.sqlRetornoTrancamentosSemInadimplenciaContar : Contrato.sqlRetornoTrancamentosContar),
                                cEmpresa,
                                dataFim,
                                dataFim,
                                dataFim,
                                dataFim,
                                Uteis.getMesReferenciaData(data))));

        analitico.setQtdTrancamento(
                contratoDao.contar(
                        String.format((desconsiderarAlunosInadimplentes ? Contrato.sqlTrancadosSemInadimplenciaContar : Contrato.sqlTrancadosContar),
                                cEmpresa,
                                dataInicio,
                                dataFim,
                                dataInicio,
                                dataFim,
                                Uteis.getMesReferenciaData(data))));

        analitico.setQtdTrancamentoHoje(
                contratoDao.contar(
                        String.format((desconsiderarAlunosInadimplentes ? Contrato.sqlTrancadosSemInadimplenciaContar : Contrato.sqlTrancadosContar),
                                cEmpresa,
                                dataFim,
                                dataFim,
                                dataFim,
                                dataFim,
                                Uteis.getMesReferenciaData(data))));

        analitico.setQtdContratoTransferido(
                contratoDao.contar(
                        String.format((desconsiderarAlunosInadimplentes ? Contrato.sqlContratoTransferidoSemInadimplenciaContar : Contrato.sqlContratoTransferidoContar),
                                cEmpresa,
                                dataInicio,
                                dataFim,
                                dataInicio,
                                dataFim,
                                Uteis.getMesReferenciaData(data))));

        analitico.setQtdContratoTransferidoHoje(
                contratoDao.contar(
                        String.format((desconsiderarAlunosInadimplentes ? Contrato.sqlContratoTransferidoSemInadimplenciaContar : Contrato.sqlContratoTransferidoContar),
                                cEmpresa,
                                dataFim,
                                dataFim,
                                dataFim,
                                dataFim,
                                Uteis.getMesReferenciaData(data))));


        analitico.setQtdVencidoMes(
                contratoDao.contar(
                        String.format((desconsiderarAlunosInadimplentes ? Contrato.sqlVencidosFinalMesSemInadimplenciaContar : Contrato.sqlVencidosFinalMesContar),
                                cEmpresa,
                                dataFim,
                                dataInicioAnterior,
                                dataFimAnterior,
                                dataInicio,
                                dataFim,
                                Uteis.getMesReferenciaData(data))));

        analitico.setQtdDependentesVinculados(
                contratoDao.contar(
                        String.format(ContratoDependente.SQL_DEPENDENTES_VINCULADOS_CONTAR,
                                dataInicio,
                                dataFim,
                                cliEmpresa
                        )
                ));

        analitico.setQtdDependentesDesvinculados(
                contratoDao.contar(
                        String.format(ContratoDependente.SQL_DEPENDENTES_DESVINCULADOS_CONTAR,
                                dataInicio,
                                dataFim,
                                cliEmpresa
                        )
                ));


        int qtdSaldo =  analitico.getQtdMatriculado()
                + analitico.getQtdRematriculado()
                + analitico.getQtdRetornoTrancamento()
                - analitico.getQtdCancelado()
                - analitico.getQtdDesistente()
                - analitico.getQtdTrancamento()
                + analitico.getQtdContratoTransferido();
        analitico.setQtdSaldo(qtdSaldo);

        return analitico;

    }
}
