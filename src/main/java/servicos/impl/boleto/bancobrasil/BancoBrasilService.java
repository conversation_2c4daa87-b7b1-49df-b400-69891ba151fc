package servicos.impl.boleto.bancobrasil;

import br.com.pactosolucoes.comuns.to.BoletoOnlineTO;
import br.com.pactosolucoes.oamd.controle.basico.DAO;
import com.google.gson.Gson;
import com.google.gson.JsonObject;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.CidadeVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.basico.EnderecoVO;
import negocio.comuns.basico.PessoaVO;
import negocio.comuns.basico.enumerador.TipoPessoa;
import negocio.comuns.financeiro.BoletoVO;
import negocio.comuns.financeiro.ConvenioCobrancaVO;
import negocio.comuns.financeiro.PessoaCPFTO;
import negocio.comuns.financeiro.enumerador.AmbienteEnum;
import negocio.comuns.financeiro.enumerador.SituacaoBoletoEnum;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.Usuario;
import negocio.facade.jdbc.basico.Cidade;
import negocio.facade.jdbc.basico.Cliente;
import negocio.facade.jdbc.basico.Empresa;
import negocio.facade.jdbc.basico.Pessoa;
import negocio.facade.jdbc.financeiro.Boleto;
import negocio.facade.jdbc.financeiro.ConvenioCobranca;
import org.apache.http.HttpResponse;
import org.apache.http.NameValuePair;
import org.apache.http.client.HttpClient;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.message.BasicNameValuePair;
import org.apache.http.util.EntityUtils;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;
import servicos.impl.boleto.AbstractBoletoOnlineServiceComum;
import servicos.impl.boleto.AtributoBoletoEnum;
import servicos.interfaces.BoletoOnlineServiceInterface;
import servicos.pix.TokenVO;
import servicos.propriedades.PropsService;
import servicos.util.ExecuteRequestHttpService;

import java.math.BigDecimal;
import java.nio.charset.StandardCharsets;
import java.sql.Connection;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

/**
 * Created with IntelliJ IDEA.
 * User: Maurin Noleto
 * Date: 20/08/2024
 */
public class BancoBrasilService extends AbstractBoletoOnlineServiceComum implements BoletoOnlineServiceInterface {

    private String chaveBanco;
    private Boleto boletoDAO;
    private ConvenioCobranca convenioCobrancaDAO;
    private Pessoa pessoaDAO;
    private Cliente clienteDAO;
    private Cidade cidadeDAO;
    private Empresa empresaDAO;
    private ConvenioCobrancaVO convenioCobrancaVO;
    private Usuario usuarioDAO;

    public BancoBrasilService(Connection con, Integer empresa, Integer convenioCobranca) throws Exception {
        super(con);
        inicializarDAO(con);
        this.convenioCobrancaVO = this.convenioCobrancaDAO.consultarPorCodigoEmpresa(convenioCobranca, empresa, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
    }

    public BancoBrasilService(Connection con) throws Exception {
        super(con);
    }

    private void inicializarDAO(Connection con) throws Exception {
        this.chaveBanco = DAO.resolveKeyFromConnection(con);
        this.boletoDAO = new Boleto(con);
        this.convenioCobrancaDAO = new ConvenioCobranca(con);
        this.pessoaDAO = new Pessoa(con);
        this.clienteDAO = new Cliente(con);
        this.cidadeDAO = new Cidade(con);
        this.empresaDAO = new Empresa(con);
        this.usuarioDAO = new Usuario(con);
    }

    private String developerApplicationKeyPacto(){
        //disponível no cadastro da Pacto no portal developer BB que a Bruna do Financeiro tem acesso.
        return "72e04f07a08f46e358a0e2e7caf1527a"; //Produção
    }

    public String token(ConvenioCobrancaVO convenioCobrancaVO) throws Exception {
        return token(convenioCobrancaVO.getPixBasicAuth(), convenioCobrancaVO.getAmbiente());
    }

    public String token(String basicToken, AmbienteEnum ambiente) throws Exception {
        HttpPost post = new HttpPost(apiAuthUrl(ambiente));
        List<NameValuePair> params = new ArrayList<>();
        params.add(new BasicNameValuePair("grant_type", "client_credentials"));
        params.add(new BasicNameValuePair("scope", "cobrancas.boletos-requisicao cobrancas.boletos-info"));

        post.setEntity(new UrlEncodedFormEntity(params, "UTF-8"));
        post.addHeader("Content-Type", "application/x-www-form-urlencoded");
        String basicAuthtoken = basicToken.replaceAll(" |Basic", "");
        post.addHeader("Authorization",  "Basic " + basicAuthtoken);

        HttpResponse response = createConnector().execute(post);
        return responseTokenDto(response).getAccess_token();
    }

    private String apiAuthUrl(AmbienteEnum ambienteEnum){
        if(ambienteEnum.equals(AmbienteEnum.HOMOLOGACAO)){
            return PropsService.getPropertyValue(PropsService.urlApiPixBBAuthSandbox);
        }else{
            return PropsService.getPropertyValue(PropsService.urlApiPixBBAuthProducao);
        }
    }

    public HttpClient createConnector() {
        return ExecuteRequestHttpService.createConnector();
    }

    public TokenVO responseTokenDto(HttpResponse response) throws Exception {
        String responseJsonString = EntityUtils.toString(response.getEntity(), StandardCharsets.UTF_8);
        int status = response.getStatusLine().getStatusCode();
        validateResponseError(responseJsonString, status);
        Gson json = new Gson();
        return json.fromJson(responseJsonString, TokenVO.class);
    }

    public void validateResponseError(String responseJsonString, int status) throws Exception {
        if (status != 200 && status != 201) {
            if (status == 500) {
                Uteis.logarDebug("Erro Boleto: " + status + " - Servidor do Banco do Brasil está indisponível. Tente novamente mais tarde.");
                throw new Exception("Erro Boleto: " + status + " - Servidor do Banco do Brasil está indisponível. Tente novamente mais tarde.");
            }
            String mensagemErro = "";
            JSONObject retornoJSON = new JSONObject(responseJsonString);
            JSONArray erros = new JSONArray();
            try {
                erros = retornoJSON.getJSONArray("erros");
            } catch (JSONException e) {
                try {
                    erros = retornoJSON.getJSONArray("errors");
                } catch (JSONException ex) {
                    mensagemErro = retornoJSON.optString("error");
                }
            }

            if (!UteisValidacao.emptyString(mensagemErro)) {
                mensagemErro = retornoJSON.getString("message") + "; ";
            } else {
                for (int i = 0; i < erros.length(); i++) {
                    try {
                        if(!UteisValidacao.emptyString(erros.getJSONObject(i).getString("mensagem"))) {
                            String msgErro = erros.getJSONObject(i).getString("mensagem");
                           if(msgErro.contains("Campo DV CPF Pagador preenchido com dados inválidos.")) {
                               mensagemErro += "O CPF/CNPJ do cliente ou do responsável não é válido, verifique as informações e tente novamente.";
                           }else{
                               mensagemErro += erros.getJSONObject(i).getString("mensagem") + "; ";
                           }
                        }
                    } catch (JSONException e) {
                        try {
                            mensagemErro += erros.getJSONObject(i).getString("message") + "; ";
                        } catch (JSONException ex) {
                            mensagemErro += erros.getJSONObject(i).getString("textoMensagem") + "; ";
                        }
                    }
                }
            }

            if (!mensagemErro.contains("Boleto já baixado/cancelado ou inexistente.")) {
                Uteis.logarDebug("Erro Boleto: " + mensagemErro);
                throw new Exception("Falha na requisição do Boleto: " + mensagemErro);
            }
        }
    }

    private String obterDataString(Date data) {
        String dataVencimentoString = "";
        SimpleDateFormat formatador = new SimpleDateFormat("dd.MM.yyyy");
        dataVencimentoString = formatador.format(data);
        return dataVencimentoString;
    }

    private Long obterCNPJOuCPFApenasNumeros(String valor) {
        // Removendo espaços, pontos e barras, mantendo apenas os dígitos, converte para Integer e retorna.
        valor = valor.replaceAll("[^\\d]", "");
        return Long.parseLong(valor);
    }

    @Override
    public BoletoVO criar(BoletoOnlineTO boletoOnlineTO) throws Exception {
        BoletoVO boletoVO = null;
        try {
            boletoVO = criarBoletoVO(boletoOnlineTO, this.convenioCobrancaVO, this.getCon());
            if (boletoOnlineTO.isVerificarBoletoExistente()) {
                BoletoVO boletoExistenteVO = verificarBoletoExistente(boletoVO);
                if (boletoExistenteVO != null &&
                        !UteisValidacao.emptyNumber(boletoExistenteVO.getCodigo())) {
                    return boletoExistenteVO;
                }
            }

            this.boletoDAO.incluir(boletoVO);

            EmpresaVO empresaVO = this.empresaDAO.consultarPorChavePrimaria(boletoVO.getEmpresaVO().getCodigo(), Uteis.NIVELMONTARDADOS_GESTAOREMESSA);
            PessoaCPFTO pessoaCPFTO = this.boletoDAO.obterDadosPessoaPagador(empresaVO.getCodigo(), boletoVO.getPessoaVO(), true, true);
            PessoaVO pessoaVO = this.pessoaDAO.consultarPorChavePrimaria(pessoaCPFTO.getPessoa(), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);

            CidadeVO cidadeVO = new CidadeVO();
            if (!UteisValidacao.emptyNumber(pessoaVO.getCidade().getCodigo())) {
                cidadeVO = this.cidadeDAO.consultarPorChavePrimaria(pessoaVO.getCidade().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            }

            EnderecoVO enderecoVO = obterEnderecoVO(pessoaVO);

            validarDados(boletoVO);
            validarDadosObrigatorioPagador(pessoaCPFTO);

            String nossoNumero = obterNossoNumeroPadraoBancoBrasil(boletoVO);
            boletoVO.setNossoNumero(nossoNumero);

            try {
                String url = apiUrl(convenioCobrancaVO.getAmbiente()) + "?gw-dev-app-key=" + developerApplicationKeyPacto();
                HttpPost post = new HttpPost(url);

                String token = token(convenioCobrancaVO);
                post.addHeader("Content-Type", "application/json");
                post.addHeader("Authorization",  "Bearer " + token);

                ObjetoJSONBancoBrasil boletoJSONDto = obterJSONCriarBoleto(boletoVO, pessoaCPFTO, pessoaVO);
                boletoVO.setParamsEnvio(body(boletoJSONDto));

                StringEntity params = new StringEntity(boletoVO.getParamsEnvio());
                post.setEntity(params);

                HttpResponse response = createConnector().execute(post);
                validarResponseCriarBoleto(boletoVO, response);
            } catch (Exception ex) {
                ex.printStackTrace();
                boletoVO.adicionarOutrasInformacoes(AtributoBoletoEnum.msg_erro, ex.getMessage());
                boletoVO.setSituacao(SituacaoBoletoEnum.ERRO);
            }

            this.boletoDAO.alterar(boletoVO);
            return boletoVO;
        } catch (Exception ex) {
            ex.printStackTrace();
            marcarBoletoComErro(boletoVO, ex);
            throw ex;
        }
    }

    private ObjetoJSONBancoBrasil obterJSONCriarBoleto(BoletoVO boletoVO, PessoaCPFTO pessoaCPFTO, PessoaVO pessoaVO) {
        ObjetoJSONBancoBrasil boletoJSONDto = new ObjetoJSONBancoBrasil();
        boletoJSONDto.setNumeroConvenio(Integer.parseInt(convenioCobrancaVO.getNumeroContrato()));
        boletoJSONDto.setNumeroCarteira(boletoVO.getConvenioCobrancaVO().getCarteiraBoleto());
        boletoJSONDto.setNumeroVariacaoCarteira(boletoVO.getConvenioCobrancaVO().getVariacao());
        boletoJSONDto.setDataEmissao(obterDataString(Calendario.hoje()));
        boletoJSONDto.setDataVencimento(obterDataString(boletoVO.getDataVencimento()));

        //Para o Banco do Brasil, o valor do boleto precisa ser formatado com 2 casas decimais.
        BigDecimal bd = BigDecimal.valueOf(boletoVO.getValor()).setScale(2, BigDecimal.ROUND_HALF_UP);
        boletoJSONDto.setValorOriginal(bd.doubleValue());

        boletoJSONDto.setNumeroTituloBeneficiario(boletoVO.getIdentificador().toString());
        boletoJSONDto.setNumeroTituloCliente(boletoVO.getNossoNumero());
        boletoJSONDto.setMensagemBloquetoOcorrencia(boletoVO.getConvenioCobrancaVO().getInstrucoesBoleto());

        //Juros Mora
        if (boletoVO.getEmpresaVO().getCobrarAutomaticamenteMultaJuros()) {
            ObjetoJSONJurosMoraBancoBrasil jurosMora = new ObjetoJSONJurosMoraBancoBrasil();
            Integer tipoJuros = obterTipoJurosIncluirBoleto(boletoVO);
            jurosMora.setTipo(tipoJuros);
            jurosMora.setValor(boletoVO.getEmpresaVO().getJurosCobrancaAutomatica() > 0 && tipoJuros == 1 ? boletoVO.getEmpresaVO().getJurosCobrancaAutomatica() : 0);
            jurosMora.setPorcentagem(boletoVO.getEmpresaVO().getJurosCobrancaAutomatica() > 0 && tipoJuros == 2 ? boletoVO.getEmpresaVO().getJurosCobrancaAutomatica() : 0);
            boletoJSONDto.setJurosMora(jurosMora);
        }

        //Multa
        if (boletoVO.getEmpresaVO().getCobrarAutomaticamenteMultaJuros()) {
            ObjetoJSONMultaBancoBrasil multa = new ObjetoJSONMultaBancoBrasil();
            Integer tipoMulta = obterTipoMultaIncluirBoleto(boletoVO);
            multa.setTipo(tipoMulta);
            multa.setData(tipoMulta != 0 ? obterDataString(Calendario.somarDias(boletoVO.getDataVencimento(), 1)) : "");
            multa.setPorcentagem(boletoVO.getEmpresaVO().getMultaCobrancaAutomatica() > 0 && tipoMulta == 2 ? boletoVO.getEmpresaVO().getMultaCobrancaAutomatica() : 0);
            multa.setValor(boletoVO.getEmpresaVO().getMultaCobrancaAutomatica() > 0 && tipoMulta == 1 ? boletoVO.getEmpresaVO().getMultaCobrancaAutomatica() : 0);
            boletoJSONDto.setMulta(multa);
        }

        ObjetoJSONPagadorBancoBrasil pagadorBancoBrasil = new ObjetoJSONPagadorBancoBrasil();
        pagadorBancoBrasil.setTipoInscricao(pessoaVO.getCategoriaPessoa().equals(TipoPessoa.FISICA) ? 1 : 2);
        pagadorBancoBrasil.setNumeroInscricao(obterCNPJOuCPFApenasNumeros(pessoaCPFTO.getCpfResponsavel()));
        pagadorBancoBrasil.setNome(pessoaCPFTO.getNomeResponsavel());

        boletoJSONDto.setPagador(pagadorBancoBrasil);
        return boletoJSONDto;
    }

    public String obterNossoNumeroPadraoBancoBrasil(BoletoVO boletoVO) {
        // Nosso Número precisa ter 20 Caracteres, iniciando por 000, no formato STRING, que deverá ser formatado da seguinte forma: ?000? + (número do convênio com 7 dígitos) +
        // (número de controle com 10 dígitos - se necessário, completar com zeros à esquerda). No caso de convênio tipo 3, não enviar este campo;
        String nossoNumeroRetornar = "000";

        if (boletoVO.getConvenioCobrancaVO().getNumeroContrato().length() < 7) {
            // Adiciona "0" à esquerda até que a string tenha 7 caracteres
            String numeroContrato = String.format("%7s", boletoVO.getConvenioCobrancaVO().getNumeroContrato()).replace(' ', '0');
            nossoNumeroRetornar += numeroContrato;
        } else {
            nossoNumeroRetornar += boletoVO.getConvenioCobrancaVO().getNumeroContrato();
        }

        if (boletoVO.getIdentificador().toString().length() < 10) {
            // Adiciona "0" à esquerda até que a string tenha 10 caracteres
            String numeroIdentificador = String.format("%10s", boletoVO.getIdentificador().toString()).replace(' ', '0');
            nossoNumeroRetornar += numeroIdentificador;
        } else {
            nossoNumeroRetornar += boletoVO.getIdentificador().toString();
        }

        return nossoNumeroRetornar;
    }

    private String apiUrl(AmbienteEnum ambienteEnum){
        if(ambienteEnum.equals(AmbienteEnum.HOMOLOGACAO)){
            return PropsService.getPropertyValue(PropsService.urlApiBoletoBancoBrasilSandbox);
        }else{
            return PropsService.getPropertyValue(PropsService.urlApiBoletoBancoBrasilProducao);
        }
    }

    public String body(Object dto) {
        Gson json = new Gson();
        return json.toJson(dto);
    }

    public void validarResponseCriarBoleto(BoletoVO boletoVO, HttpResponse response) throws Exception {
        String responseJsonString = EntityUtils.toString(response.getEntity(), StandardCharsets.UTF_8);
        boletoVO.setParamsResposta(responseJsonString);
        int status = response.getStatusLine().getStatusCode();
        validateResponseError(responseJsonString, status);
        boletoVO.setSituacao(SituacaoBoletoEnum.AGUARDANDO_PAGAMENTO);

        JSONObject jsonRetorno = new JSONObject(responseJsonString);
        boletoVO.setIdExterno(jsonRetorno.getString("numero"));
        boletoVO.setLinhaDigitavel(jsonRetorno.optString("linhaDigitavel"));
        boletoVO.setCodigoBarrasNumerico(jsonRetorno.optString("codigoBarraNumerico"));
    }

    private void validarDadosObrigatorioPagador(PessoaCPFTO pessoaCPFTO) throws Exception {
        if (UteisValidacao.emptyString(pessoaCPFTO.getCpfResponsavel())) {
            throw new ConsistirException("O CPF/CNPJ do cliente ou do responsável é obrigatório para registrar o boleto no Banco do Brasil.");
        }

        if(!UteisValidacao.isValidCPF(pessoaCPFTO.getCpfResponsavel())) {
            throw new Exception("O CPF/CNPJ do cliente ou do responsável não é válido, verifique as informações e tente novamente. ");
        }

        if (UteisValidacao.emptyString(pessoaCPFTO.getNomeResponsavel())) {
            throw new ConsistirException("O nome do cliente ou do responsável é obrigatório para registrar o boleto no Banco do Brasil.");
        }
    }

    private void validarDados(BoletoVO boletoVO) throws Exception {
        if (UteisValidacao.emptyString(this.chaveBanco)) {
            throw new ConsistirException("Operação não permitida, não foi possível identificar a chaveZW.");
        }
        if (boletoVO.getDataVencimento() == null) {
            throw new ConsistirException("O parâmetro dataVencimento é obrigatório para registrar o boleto no Banco do Brasil.");
        }
        if (UteisValidacao.emptyNumber(boletoVO.getValor())) {
            throw new ConsistirException("O parâmetro valorCobrado é obrigatório para registrar o boleto no Banco do Brasil.");
        }
    }

    @Override
    public void cancelar(BoletoVO boletoVO, UsuarioVO usuarioVO, String operacao, boolean origemProcessoManutencao) throws Exception {
        Boleto boletoDAO;

        // API Banco Brasil só cancela boleto com mais de 30 minutos que foi gerado.
        // Os locais que vem do usuário, exibe a mensagem de erro para o usuário.
        // Os locais que não é o usuário, retorna para seguir para o próximo item do for de boletos sem cancelar.
        if (permitiCancelarPelaAPIBancoBrasil(boletoVO) && (operacao.equals("CancelarBoleto - Tela Cliente") || operacao.equals("CancelarBoletosSelecionados - Tela Cliente")
                || operacao.equals("CancelarBoleto - Tela Cliente - PactoPay") || operacao.equals("Caixa Em Aberto - renegociar")
                || operacao.equals("Caixa Em Aberto - receber") || operacao.equals("Caixa Em Aberto - cancelar"))) {
            throw new Exception("A API do Banco do Brasil só permite cancela boletos após 30 minutos que foi gerado.");
        } else if (permitiCancelarPelaAPIBancoBrasil(boletoVO) && (operacao.equals("Cancelamento pelo ajuste do processo geral") ||
                operacao.equals("Boletos Pendentes de Cancelamento - RemessaService")) || operacao.equals("PactoPay - Cancelar")) {
            return;
        }

        try {
            boletoDAO = new Boleto(this.getCon());
            JsonObject jsonObject = new JsonObject();
            jsonObject.addProperty("numeroConvenio", convenioCobrancaVO.getNumeroContrato());
            boletoDAO.incluirBoletoHistorico(boletoVO, "cancelar", jsonObject.toString());

            String url = apiUrl(convenioCobrancaVO.getAmbiente()) + "/" + boletoVO.getNossoNumero() + "/baixar" + "?gw-dev-app-key=" + developerApplicationKeyPacto();
            HttpPost post = new HttpPost(url);

            String token = token(convenioCobrancaVO);
            post.addHeader("Content-Type", "application/json");
            post.addHeader("Authorization",  "Bearer " + token);

            StringEntity params = new StringEntity(body(jsonObject));
            post.setEntity(params);

            HttpResponse response = createConnector().execute(post);

            String responseJsonString = EntityUtils.toString(response.getEntity(), StandardCharsets.UTF_8);
            int status = response.getStatusLine().getStatusCode();
            validateResponseError(responseJsonString, status);

            // Se não tem erro, nessa parte vai pegar os dados de sucesso.
            boletoVO.setJsonEstorno(responseJsonString);
            boletoDAO.alterarJsonEstorno(boletoVO);
            boletoDAO.incluirBoletoHistorico(boletoVO, "cancelar | resposta", responseJsonString);

            boletoDAO.alterarSituacao(boletoVO, SituacaoBoletoEnum.CANCELADO);
            estornarRecibo(boletoVO, usuarioVO);
        } finally {
            boletoDAO = null;
        }
    }

    @Override
    public void processarWebhook(BoletoVO boletoVO, String json) throws Exception {
        processarPagamentoBoletoBancoBrasil(boletoVO, json, this.usuarioDAO.getUsuarioRecorrencia(), "processarWebhook");
    }

    private boolean permitiCancelarPelaAPIBancoBrasil(BoletoVO boletoVO) {
        // A API do Banco do Brasil só permite cancelar boletos que foram gerados a mais de 30 minutos.
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(boletoVO.getDataRegistro());
        calendar.add(Calendar.MINUTE, 30);
        return calendar.getTime().after(Calendario.hoje());
    }

    @Override
    public void sincronizar(BoletoVO boletoVO, UsuarioVO usuarioVO, String operacao) throws Exception {
        Boleto boletoDAO;
        ConvenioCobranca convenioCobrancaDAO;

        try {
            boletoDAO = new Boleto(this.getCon());
            convenioCobrancaDAO = new ConvenioCobranca(this.getCon());

            if (UteisValidacao.emptyString(boletoVO.getIdExterno())) {
                throw new Exception("Boleto não tem idExterno (id_unico)");
            }

            if (UteisValidacao.emptyString(boletoVO.getConvenioCobrancaVO().getCnpj())) {
                boletoVO.setConvenioCobrancaVO(convenioCobrancaDAO.consultarPorChavePrimaria(boletoVO.getConvenioCobrancaVO().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
            }

            String nossoNumeroConsultado = "Nosso Número Consultado: " + boletoVO.getNossoNumero();
            boletoDAO.incluirBoletoHistorico(boletoVO, "sincronizar", nossoNumeroConsultado);

            String url = apiUrl(convenioCobrancaVO.getAmbiente()) + "/" + boletoVO.getNossoNumero() + "?gw-dev-app-key=" + developerApplicationKeyPacto() +
                    "&numeroConvenio=" + convenioCobrancaVO.getNumeroContrato();

            HttpGet get = new HttpGet(url);

            String token = token(convenioCobrancaVO);
            get.addHeader("Content-Type", "application/json");
            get.addHeader("Authorization",  "Bearer " + token);

            HttpResponse response = createConnector().execute(get);

            String responseJsonString = EntityUtils.toString(response.getEntity(), StandardCharsets.UTF_8);
            int status = response.getStatusLine().getStatusCode();
            validateResponseError(responseJsonString, status);

            // Se não tem erro, nessa parte vai pegar os dados de sucesso.
            JSONObject jsonRetorno = new JSONObject(responseJsonString);
            Integer situacaoBoleto = jsonRetorno.optInt("codigoEstadoTituloCobranca");
//            Lista de Códigos das possíveis situações que pode ser retornado pelo Banco do Brasil.
//            1 - NORMAL
//            2 - MOVIMENTO CARTORIO
//            3 - EM CARTORIO
//            4 - TITULO COM OCORRENCIA DE CARTORIO
//            5 - PROTESTADO ELETRONICO
//            6 - LIQUIDADO
//            7 - BAIXADO
//            8 - TITULO COM PENDENCIA DE CARTORIO
//            9 - TITULO PROTESTADO MANUAL
//            10 - TITULO BAIXADO/PAGO EM CARTORIO
//            11 - TITULO LIQUIDADO/PROTESTADO
//            12 - TITULO LIQUID/PGCRTO
//            13 - TITULO PROTESTADO AGUARDANDO BAIXA
//            14 - TITULO EM LIQUIDACAO
//            15 - TITULO AGENDADO BB
//            16 - TITULO CREDITADO
//            17 - PAGO EM CHEQUE - AGUARD.LIQUIDACAO
//            18 - PAGO PARCIALMENTE
//            19 - PAGO PARCIALMENTE CREDITADO
//            21 - TITULO AGENDADO OUTROS BANCOS

            if (situacaoBoleto == 7) {
                boletoDAO.alterarSituacao(boletoVO, SituacaoBoletoEnum.CANCELADO);
                boletoDAO.incluirBoletoHistorico(boletoVO, "cancelar | resposta", responseJsonString);
                if (operacao.equals("SincronizarBoleto - Tela Cliente")) {
                    throw new Exception("Boleto está cancelado.");
                } else {
                    Uteis.logarDebug("Processo Automatico Pagamento Boleto Banco Brasil Online - Sincronizar - Erro: Boleto está cancelado.");
                    return;
                }
            } else if (situacaoBoleto == 6) {
                boletoDAO.incluirBoletoHistorico(boletoVO, "sincronizar | resposta", response.toString());
                processarPagamentoBoletoBancoBrasil(boletoVO, responseJsonString, usuarioVO, operacao);
            } else if (situacaoBoleto == 1) {
                boletoDAO.incluirBoletoHistorico(boletoVO, "sincronizar | resposta", responseJsonString);
            }
        } catch (Exception ex) {
            ex.printStackTrace();
            throw ex;
        } finally {
            boletoDAO = null;
            convenioCobrancaDAO = null;
        }
    }

    private void processarPagamentoBoletoBancoBrasil(BoletoVO boletoVO, String retornoConsulta, UsuarioVO usuarioVO, String operacao) throws Exception {
        Boleto boletoDAO = null;
        BoletoVO boletoVOAnterior = null;
        try {
            boletoDAO = new Boleto(this.getCon());
            boletoDAO.incluirBoletoHistorico(boletoVO, "processarPagamentoBoletoBancoBrasil | " + operacao, retornoConsulta);

            boletoVOAnterior = (BoletoVO) boletoVO.getClone(true);

            //obter situacao conforme a os dados recebidos
            JSONObject jsonRetorno = new JSONObject(retornoConsulta);

            Double valorPago = 0.0;
            if (operacao.equals("processoAutomaticoPagamentosBoletoBancoBrasil")) {
                valorPago = jsonRetorno.optDouble("valorPago");
            } else {
                valorPago = jsonRetorno.optDouble("valorPagoSacado");
            }

            boletoVO.setValorPago(valorPago);
            boletoVO.setValorLiquido(valorPago);
            boletoVO.setValorTarifa(0.0);

            if (operacao.equals("processarWebhook")) {
                boletoVO.setDataPagamento(Calendario.hoje());
                boletoVO.setDataCredito(Calendario.hoje());
            } else if (operacao.equals("processoAutomaticoPagamentosBoletoBancoBrasil")) {
                String dataCredito = jsonRetorno.optString("dataCredito");
                dataCredito = dataCredito.replace(".", "/");
                if (!UteisValidacao.emptyString(dataCredito)) {
                    boletoVO.setDataPagamento(Calendario.converterEmDate(dataCredito));
                    boletoVO.setDataCredito(Calendario.converterEmDate(dataCredito));
                } else {
                    boletoVO.setDataPagamento(Calendario.hoje());
                    boletoVO.setDataCredito(Calendario.hoje());
                }
            } else {
                String dataPagamento = jsonRetorno.optString("dataRecebimentoTitulo");
                dataPagamento = dataPagamento.replace(".", "/");
                Date dataRecebimento = Calendario.converterEmDate(dataPagamento);
                if (!UteisValidacao.emptyString(dataPagamento) && Calendario.menorOuIgual(dataRecebimento, Calendario.hoje())) {
                    boletoVO.setDataPagamento(dataRecebimento);
                } else {
                    boletoVO.setDataPagamento(Calendario.hoje());
                }

                String dataCreditoLiquidacao = jsonRetorno.optString("dataCreditoLiquidacao");
                dataCreditoLiquidacao = dataCreditoLiquidacao.replace(".", "/");
                Date dataCredito = Calendario.converterEmDate(dataCreditoLiquidacao);
                if (!UteisValidacao.emptyString(dataCreditoLiquidacao) && Calendario.maiorOuIgual(dataCredito, dataRecebimento)) {
                    boletoVO.setDataCredito(dataCredito);
                } else {
                    boletoVO.setDataCredito(Calendario.hoje());
                }
            }

            boletoVO.setValorPossivelDesconto(0.0);
            boletoVO.setSituacao(SituacaoBoletoEnum.PAGO);

            if (boletoVO.getValorPago() > 0.0) {
                if (!UteisValidacao.emptyNumber(boletoVO.getReciboPagamentoVO().getCodigo()) ||
                        !UteisValidacao.emptyNumber(boletoVO.getMovPagamentoVO().getCodigo())) {
                    boletoDAO.incluirBoletoHistorico(boletoVO, operacao, "BOLETO JÁ ESTÁ PAGO");
                } else {
                    //gerar recibo pagamento
                    gerarPagamentoBoleto(boletoVO, usuarioVO, true, false);
                }
            }

            //atualizar dados do boleto
            boletoDAO.alterar(boletoVO);

            Uteis.logarDebug("Processo Automatico Pagamento Boleto Banco Brasil Online - Sincronizar - processarPagamentoBoletoBancoBrasil Sucesso: " + boletoVO.getNossoNumero() + " - " +
                    boletoVO.getSituacao().getDescricao() + " - " + boletoVO.getValorPago() + " - " + boletoVO.getDataPagamento());
        } catch (Exception ex) {
            ex.printStackTrace();
            if (boletoDAO != null) {
                boletoDAO.incluirBoletoHistorico(boletoVO, operacao + " | processarBoletoBancoBrasil | ERRO", ex.getMessage());
            }
            throw ex;
        } finally {
            if (boletoVOAnterior != null && boletoVO != null) {
                if (!boletoVOAnterior.getSituacao().equals(boletoVO.getSituacao())) {
                    JSONObject jsonSituacao = new JSONObject();
                    jsonSituacao.put("situacao_anterior", boletoVOAnterior.getSituacao().getDescricao());
                    jsonSituacao.put("situacao_atual", boletoVO.getSituacao().getDescricao());
                    boletoDAO.incluirBoletoHistorico(boletoVO, "processarBoletoBancoBrasil - ALTERAR SITUAÇÃO", jsonSituacao.toString());
                }
            }
            boletoDAO = null;
        }
    }

    public void processoAutomaticoPagamentosBoletoBancoBrasil(Integer indice) {

        Uteis.logarDebug("Inicio Processo Automatico Pagamento Boleto Banco Brasil Online");

        Boleto boletoDAO = null;
        try {
            boletoDAO = new Boleto(this.getCon());

            //Hoje, vai consultar os boletos de ontem
            Date dataConsulta = Calendario.somarDias(Calendario.hoje(), -1);
            String dataConsultaString = obterDataString(dataConsulta);

            Integer agenciaBeneficiario = Integer.parseInt(this.convenioCobrancaVO.getContaEmpresa().getAgencia());
            Integer contaBeneficiario = Integer.parseInt(this.convenioCobrancaVO.getContaEmpresa().getContaCorrente());

            //No momento está consultando apenas Estado do Título de Cobrança = 6 (LIQUIDADO)
            //Se no futuro, os clientes apresentar novas situações, então precisa alterar o código.
            String url = apiUrl(AmbienteEnum.PRODUCAO) + "?gw-dev-app-key=" + developerApplicationKeyPacto() + "&indicadorSituacao=B&agenciaBeneficiario=" + agenciaBeneficiario +
                    "&contaBeneficiario=" + contaBeneficiario + "&dataInicioMovimento=" + dataConsultaString + "&dataFimMovimento=" + dataConsultaString + "&codigoEstadoTituloCobranca=6&indice=" +
                    indice;

            HttpGet get = new HttpGet(url);

            String token = token(convenioCobrancaVO);
            get.addHeader("Content-Type", "application/json");
            get.addHeader("Authorization",  "Bearer " + token);

            HttpResponse response = createConnector().execute(get);

            String responseJsonString = EntityUtils.toString(response.getEntity(), StandardCharsets.UTF_8);
            int status = response.getStatusLine().getStatusCode();

            if (status == 404 && UteisValidacao.emptyString(responseJsonString)) {
                String mensagem = "Boleto Banco Brasil: " + status + " - Não encontrou dados para retornar. Url Consulta: " + url;
                Uteis.logarDebug(mensagem);
                return;
            }

            validateResponseError(responseJsonString, status);

            // Se não tem erro, nessa parte vai pegar os dados de sucesso.
            JSONObject jsonRetorno = new JSONObject(responseJsonString);
            String indicadorContinuidade = jsonRetorno.optString("indicadorContinuidade");
            Integer proximoIndice = jsonRetorno.optInt("proximoIndice");

            JSONArray arrayBoletos = jsonRetorno.getJSONArray("boletos");
            for (int j = 0; j < arrayBoletos.length(); j++) {
                JSONObject jsonBoleto = arrayBoletos.getJSONObject(j);
                String numeroBoletoBB = jsonBoleto.optString("numeroBoletoBB");

                List<BoletoVO> boletoVOs = boletoDAO.consultarPorNossoNumero(numeroBoletoBB, null, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                for (BoletoVO boletoVO: boletoVOs) {
                    boletoDAO.incluirBoletoHistorico(boletoVO, "sincronizar", numeroBoletoBB);
                    processarPagamentoBoletoBancoBrasil(boletoVO, jsonBoleto.toString(), this.usuarioDAO.getUsuarioRecorrencia(), "processoAutomaticoPagamentosBoletoBancoBrasil");
                }
            }

            //Essa condição é para continuar o processo de consulta, caso tenha mais registros para consultar, pois só vem 300 por consulta.
            //Essa informação se vai ter mais regsitros, é informado pelo Banco do Brasil no retorno.
            if (indicadorContinuidade.toUpperCase().equals("S")) {
                processoAutomaticoPagamentosBoletoBancoBrasil(proximoIndice);
            }

            Uteis.logarDebug("Fim Processo Automatico Pagamento Boleto Banco Brasil Online");

        } catch (Exception ex) {
            ex.printStackTrace();
            Uteis.logarDebug("Erro Processo Automatico Pagamento Boleto Banco Brasil Online");
            Uteis.logarDebug(ex.getMessage());
        } finally {
            boletoDAO = null;
            convenioCobrancaDAO = null;
        }
    }

    private Integer obterTipoJurosIncluirBoleto(BoletoVO boletoVO) {
        Integer valorTipoJuros = TipoCobrancaMultaEJurosBancoBrasilEnum.ISENTO.getCodigo();
        if (boletoVO.getEmpresaVO().getJurosCobrancaAutomatica() > 0 && boletoVO.getEmpresaVO().isUtilizarJurosValorAbsoluto()) {
            valorTipoJuros = TipoCobrancaMultaEJurosBancoBrasilEnum.VALOR_POR_DIA.getCodigo();
        } else if (boletoVO.getEmpresaVO().getJurosCobrancaAutomatica() > 0 && !boletoVO.getEmpresaVO().isUtilizarJurosValorAbsoluto()) {
            valorTipoJuros = TipoCobrancaMultaEJurosBancoBrasilEnum.TAXA_MENSAL.getCodigo();
        }
        return valorTipoJuros;
    }

    private Integer obterTipoMultaIncluirBoleto(BoletoVO boletoVO) {
        Integer valorTipoJuros = TipoCobrancaMultaEJurosBancoBrasilEnum.ISENTO.getCodigo();
        if (boletoVO.getEmpresaVO().getMultaCobrancaAutomatica() > 0 && boletoVO.getEmpresaVO().isUtilizarMultaValorAbsoluto()) {
            valorTipoJuros = TipoCobrancaMultaEJurosBancoBrasilEnum.VALOR_POR_DIA.getCodigo();
        } else if (boletoVO.getEmpresaVO().getMultaCobrancaAutomatica() > 0 && !boletoVO.getEmpresaVO().isUtilizarMultaValorAbsoluto()) {
            valorTipoJuros = TipoCobrancaMultaEJurosBancoBrasilEnum.TAXA_MENSAL.getCodigo();
        }
        return valorTipoJuros;
    }

}
