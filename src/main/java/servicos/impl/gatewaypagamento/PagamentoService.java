package servicos.impl.gatewaypagamento;

import br.com.pactosolucoes.autorizacaocobranca.dao.AutorizacaoCobrancaCliente;
import br.com.pactosolucoes.autorizacaocobranca.modelo.AutorizacaoCobrancaClienteVO;
import br.com.pactosolucoes.autorizacaocobranca.modelo.TipoAutorizacaoCobrancaEnum;
import br.com.pactosolucoes.comuns.to.CartaoCreditoTO;
import br.com.pactosolucoes.integracao.aragorn.AragornService;
import negocio.comuns.arquitetura.LogVO;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.basico.PessoaVO;
import negocio.comuns.contrato.ContratoVO;
import negocio.comuns.financeiro.ConvenioCobrancaVO;
import negocio.comuns.financeiro.FormaPagamentoVO;
import negocio.comuns.financeiro.MovPagamentoVO;
import negocio.comuns.financeiro.MovParcelaVO;
import negocio.comuns.financeiro.ReciboPagamentoVO;
import negocio.comuns.financeiro.TransacaoVO;
import negocio.comuns.financeiro.enumerador.OrigemCobrancaEnum;
import negocio.comuns.financeiro.enumerador.SituacaoTransacaoEnum;
import negocio.comuns.financeiro.enumerador.TipoConvenioCobrancaEnum;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.Log;
import negocio.facade.jdbc.arquitetura.Usuario;
import negocio.facade.jdbc.arquitetura.ZillyonWebFacade;
import negocio.facade.jdbc.basico.Cliente;
import negocio.facade.jdbc.basico.Empresa;
import negocio.facade.jdbc.basico.Pessoa;
import negocio.facade.jdbc.financeiro.Adquirente;
import negocio.facade.jdbc.financeiro.FormaPagamento;
import negocio.facade.jdbc.financeiro.MovPagamento;
import negocio.facade.jdbc.financeiro.MovParcela;
import negocio.facade.jdbc.financeiro.MovParcelaResultadoCobranca;
import negocio.facade.jdbc.financeiro.OperadoraCartao;
import negocio.facade.jdbc.financeiro.Transacao;
import negocio.facade.jdbc.financeiro.TransacaoMovParcela;
import servicos.SuperServico;
import servicos.impl.apf.APF;
import servicos.impl.dcc.base.PagamentoCartaoAgrupadoService;
import servicos.impl.dcc.base.PagamentoCartaoTO;
import servicos.impl.stone.xml.authorization.request.InstalmentTypeInstlmtTp;
import servicos.integracao.impl.parceirofidelidade.base.ParceiroFidelidadeZW;
import servicos.interfaces.AprovacaoServiceInterface;
import servicos.vendasonline.dto.RetornoVendaTO;

import java.sql.Connection;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * Created by johnys on 24/02/2017.
 */
public class PagamentoService extends SuperServico {

    private Usuario usuarioDAO;
    private Pessoa pessoaDAO;
    private MovParcela movParcelaDAO;
    private Empresa empresaDAO;
    private Transacao transacaoDAO;
    private TransacaoMovParcela transacaoMovParcelaDAO;
    private MovPagamento movPagamentoDAO;
    private ConvenioCobrancaVO convenioCobrancaVO;
    private FormaPagamentoVO formaPagamentoCartaoRecorrenteVO;
    private boolean forcarValidarAutorizacaoCobranca = false;

    public PagamentoService(Connection con, ConvenioCobrancaVO convenioCobrancaVO) throws Exception {
        super(con);
        this.usuarioDAO = new Usuario(con);
        this.pessoaDAO = new Pessoa(con);
        this.movParcelaDAO = new MovParcela(con);
        this.empresaDAO = new Empresa(con);
        this.transacaoDAO = new Transacao(con);
        this.transacaoMovParcelaDAO = new TransacaoMovParcela(con);
        this.movPagamentoDAO = new MovPagamento(con);
        this.convenioCobrancaVO = convenioCobrancaVO;

        FormaPagamento formaPagamentoDAO = new FormaPagamento(con);
        this.formaPagamentoCartaoRecorrenteVO = formaPagamentoDAO.obterFormaPagamentoCartaoRecorrente();
        formaPagamentoDAO = null;
    }

    public void processarCobrancasOnlineAutomatico(Date dia, List<String> msgErro, Set<Integer> codParcelasIncrementarTentativa,
                                                   Set<Integer> codParcelasVindi) throws Exception {
        List<MovParcelaVO> listaParcelas = consultarPagamentos(dia);
        UsuarioVO usuarioRecorrenciaVO = this.usuarioDAO.getUsuarioRecorrencia();
        processarCobrancasOnlineNaoPresencial(msgErro, codParcelasIncrementarTentativa, codParcelasVindi, listaParcelas, usuarioRecorrenciaVO, false, OrigemCobrancaEnum.ZW_AUTOMATICO, false, null);
    }

    public Set<Integer> processarCobrancaNaoPresencial(List<MovParcelaVO> listaParcelas, UsuarioVO usuarioVO, List<String> msgErro,
                                                       boolean retentativaManual, OrigemCobrancaEnum origemCobrancaEnum, boolean async,
                                                       String ipCliente) throws Exception {
        return processarCobrancasOnlineNaoPresencial(msgErro, new HashSet<>(), new HashSet<>(), listaParcelas, usuarioVO, retentativaManual, origemCobrancaEnum, async, ipCliente);
    }

    private Set<Integer> processarCobrancasOnlineNaoPresencial(List<String> msgErro, Set<Integer> codParcelasIncrementarTentativa, Set<Integer> codParcelasVindi,
                                                               List<MovParcelaVO> listaParcelas, UsuarioVO usuarioVO, boolean retentativaManual,
                                                               OrigemCobrancaEnum origemCobrancaEnum, boolean async, String ipCliente) throws Exception {
        try {
            Set<Integer> transacaoCriadas = new HashSet<>();

            Uteis.logarDebug("PagamentoService - Total de parcelas " + listaParcelas.size());

            //MONTAR MULTA E JUROS
            this.movParcelaDAO.montarMultaJurosParcelaVencida(this.convenioCobrancaVO.getEmpresa(), this.convenioCobrancaVO.getTipo().getTipoCobranca(), listaParcelas, Calendario.hoje());

            PagamentoCartaoAgrupadoService pagamentoCartaoAgrupadoService = new PagamentoCartaoAgrupadoService(this.convenioCobrancaVO, this.convenioCobrancaVO.getEmpresa(), this.getCon());

            int ordem = 0;
            while (ordem <= 2) {
                //incrementar ordem
                ++ordem;

                //retirar parcelas que já foram pagas ou estão pendentes
                listaParcelas = processarParcelasEmAberto(listaParcelas);

                Uteis.logarDebug("PagamentoService - Total de parcelas " + listaParcelas.size() + " | Ordem " + ordem);

                List<PagamentoCartaoTO> listaPagamentoOnline = pagamentoCartaoAgrupadoService.processarParcelas(listaParcelas, usuarioVO, msgErro, ordem);

                Uteis.logarDebug("PagamentoService - Total de cobranças " + listaPagamentoOnline.size() + " | Ordem " + ordem);

                int i = 0;
                for (PagamentoCartaoTO pagamentoCartaoTO : listaPagamentoOnline) {
                    try {
                        pagamentoCartaoTO.setTransacaoPresencial(false);
                        pagamentoCartaoTO.setTipoParcelamentoStone(this.convenioCobrancaVO.getTipoParcelamentoStone());
                        pagamentoCartaoTO.setRetentativaManual(retentativaManual);
                        pagamentoCartaoTO.setAsync(async);
                        pagamentoCartaoTO.setIpCliente(ipCliente);

                        //se for automático identificar se está sendo retentativa a parcela
                        if (origemCobrancaEnum != null && origemCobrancaEnum.equals(OrigemCobrancaEnum.ZW_AUTOMATICO)) {
                            boolean retentativa = false;
                            for (MovParcelaVO movParcelaVO : pagamentoCartaoTO.getParcelas()) {
                                if (!UteisValidacao.emptyNumber(movParcelaVO.getNrTentativas())) {
                                    retentativa = true;
                                    break;
                                }
                            }
                            pagamentoCartaoTO.setOrigemCobranca(retentativa ? OrigemCobrancaEnum.ZW_AUTOMATICO_RETENTATIVA : origemCobrancaEnum);
                        } else {
                            pagamentoCartaoTO.setOrigemCobranca(origemCobrancaEnum);
                        }

                        Uteis.logarDebug("PagamentoService Cobranca - " + ++i + "/" + listaPagamentoOnline.size());

                        PagamentoOnlineService pagamentoOnlineService = new PagamentoOnlineService(pagamentoCartaoTO, this.getCon());
                        TransacaoVO transacaoVO = pagamentoOnlineService.realizarCobranca();

                        if (transacaoVO != null && !UteisValidacao.emptyNumber(transacaoVO.getCodigo())) {
                            transacaoCriadas.add(transacaoVO.getCodigo());
                        }

                        if (transacaoVO != null && !UteisValidacao.emptyNumber(transacaoVO.getCodigo())
                                && !transacaoVO.getSituacao().equals(SituacaoTransacaoEnum.COM_ERRO)) {
                            //transação com situação ERRO não chegaram a ir na adquirente por isso devem ser desconsideradas.
                            for (MovParcelaVO movParcelaVO : pagamentoCartaoTO.getParcelas()) {
                                if (this.convenioCobrancaVO.getTipo().equals(TipoConvenioCobrancaEnum.DCC_VINDI)) {
                                    codParcelasVindi.add(movParcelaVO.getCodigo());
                                } else {
                                    codParcelasIncrementarTentativa.add(movParcelaVO.getCodigo());
                                }
                            }
                        }
                    } catch (Exception ex) {
                        ex.printStackTrace();
                        String msg = (this.convenioCobrancaVO.getTipo().getDescricao() + " | Pessoa: " + pagamentoCartaoTO.getPessoaPagador().getNome() + " Erro processar pagamento: " + ex.getMessage());
                        Uteis.logarDebug(msg);
                        msgErro.add(msg);
                    }
                }
            }

            if (UteisValidacao.emptyList(transacaoCriadas) && !UteisValidacao.emptyList(msgErro)) {
                throw new Exception(msgErro.get(0));
            }
            return transacaoCriadas;
        } catch (Exception ex) {
            ex.printStackTrace();
            Uteis.logarDebug("PagamentoService - processarCobrancasOnlineNaoPresencial | ERRO: " + ex.getMessage());
            throw ex;
        }
    }

    private List<MovParcelaVO> processarParcelasEmAberto(List<MovParcelaVO> listaBruta) throws Exception {
        //validar se a parcela está em aberto e não está bloqueada por cobrança
        List<MovParcelaVO> listaParcelas = new ArrayList<>();
        for (MovParcelaVO movParcelaVO : listaBruta) {
            try {
                if (this.movParcelaDAO.validarSituacaoParcela(movParcelaVO.getCodigo(), "EA") &&
                    !this.movParcelaDAO.parcelaEstaBloqueadaPorCobranca(movParcelaVO)) {
                    listaParcelas.add(movParcelaVO);
                }
            } catch (Exception e) {
                e.printStackTrace();
                Uteis.logarDebug("PagamentoService - processarParcelasEmAberto | ERRO: " + e.getMessage());
            }
        }
        return listaParcelas;
    }

    private List<MovParcelaVO> processarParcelas(List<MovParcelaVO> listaBruta) throws Exception {

        Uteis.logarDebug("PagamentoService - Total de parcelas brutas " + listaBruta.size());

        Map<Integer, PessoaVO> mapaPessoaBloqueio = this.pessoaDAO.obterMapaPessoasBloqueioCobrancaAutomatica();

        List<MovParcelaVO> listaParcelas = new ArrayList<>();
        for (MovParcelaVO movParcelaVO : listaBruta) {

            /**
             * VERIFICAR SE A PESSOA ESTÁ BLOQUEADA PARA COBRANÇA AUTOMÁTICA
             * by Luiz Felipe 28/04/2020
             */
            if (!this.pessoaDAO.podeCobrarParcelaBloqueioCobrancaAutomatica(movParcelaVO, mapaPessoaBloqueio)) {
                Uteis.logarDebug("PESSOA ESTÁ BLOQUEADA PARA COBRANÇA AUTOMÁTICA - " + movParcelaVO.getCodigo());
                continue;
            }


            /**
             * PARCELA DE MULTA E JUROS NÃO DEVE SER COBRADA AUTOMATICAMENTE
             * SISTEMA DEVE CRIAR NO MOMENTO DA TRANSAÇÃO E CASO NÃO SEJA PAGO A TRANSAÇÃO DEVE SER EXCLUIDA
             * by Luiz Felipe 12/06/2020
             */
            if (movParcelaVO.getDescricao().toUpperCase().startsWith("MULTA E JUROS - PARCELA")) {
                Uteis.logarDebug("IGNORAR | PARCELA DE MULTA E JUROS - " + movParcelaVO.getCodigo());
                continue;
            }

            listaParcelas.add(movParcelaVO);
        }
        return listaParcelas;
    }

    public void verificarPendenciasTransacaoOnline(ConvenioCobrancaVO convenioCobrancaVO, EmpresaVO empresaVO,
                                                   boolean somenteCancelarTransacoesVerificacaoCartao) {
        VerificadorTransacaoService service = null;
        try {
            Uteis.logarDebug("VerificarPendenciasTransacaoOnline -> INÍCIO | " + convenioCobrancaVO.getDescricao());
            service = new VerificadorTransacaoService(getCon());

//            if (convenioCobrancaVO.isPactoPay()) {
//                service.processarPactoPay(convenioCobrancaVO.getCodigo(), empresaVO.getCodigo());
//            }


            if (somenteCancelarTransacoesVerificacaoCartao) {
                service.cancelarTransacoesVerificacaoCartao(convenioCobrancaVO.getTipo().getTipoTransacao(), convenioCobrancaVO.getCodigo(), empresaVO.getCodigo());
                return;
            }

            //separado pois pode haver transações antigas que estão pendentes.
            if (convenioCobrancaVO.getTipo().equals(TipoConvenioCobrancaEnum.DCC_VINDI)) {
                service.processarVindi(convenioCobrancaVO.getCodigo(), empresaVO.getCodigo());
            } else if (convenioCobrancaVO.getTipo().equals(TipoConvenioCobrancaEnum.DCC_CIELO_ONLINE)) {
                service.processarCielo(convenioCobrancaVO.getCodigo(), empresaVO.getCodigo());
            } else if (convenioCobrancaVO.getTipo().equals(TipoConvenioCobrancaEnum.DCC_MUNDIPAGG)) {
                service.processarMundiPagg(convenioCobrancaVO.getCodigo(), empresaVO.getCodigo());
            } else if (convenioCobrancaVO.getTipo().equals(TipoConvenioCobrancaEnum.DCC_PAGAR_ME)) {
                service.processarPagarMe(convenioCobrancaVO.getCodigo(), empresaVO.getCodigo());
            } else if (convenioCobrancaVO.getTipo().equals(TipoConvenioCobrancaEnum.DCC_STRIPE)) {
                service.processarStripe(convenioCobrancaVO.getCodigo(), empresaVO.getCodigo());
            } else if (convenioCobrancaVO.getTipo().equals(TipoConvenioCobrancaEnum.DCC_PAGOLIVRE)) {
                service.processarPagoLivre(convenioCobrancaVO.getCodigo(), empresaVO.getCodigo());
            } else if (convenioCobrancaVO.getTipo().equals(TipoConvenioCobrancaEnum.DCC_FACILITEPAY)) {
                service.processarFacilite(convenioCobrancaVO.getCodigo(), empresaVO.getCodigo());
            }

            service.cancelarTransacoesVerificacaoCartao(convenioCobrancaVO.getTipo().getTipoTransacao(), convenioCobrancaVO.getCodigo(), empresaVO.getCodigo());

        } catch (Exception ex) {
            ex.printStackTrace();
        } finally {
            service = null;
            Uteis.logarDebug("VerificarPendenciasTransacaoOnline -> FIM | " + convenioCobrancaVO.getDescricao());
        }
    }

    public TransacaoVO realizarCobrancaWS(MovParcelaVO movParcelaVO, UsuarioVO usuarioVO, Integer nrParcelasOperadora) throws Exception {
        PagamentoOnlineService pagamentoOnlineService;
        PagamentoCartaoAgrupadoService pagamentoCartaoAgrupadoService;
        try {
            pagamentoCartaoAgrupadoService = new PagamentoCartaoAgrupadoService(this.convenioCobrancaVO, movParcelaVO.getEmpresa(), this.getCon());
            PagamentoCartaoTO pagamentoCartaoTO = pagamentoCartaoAgrupadoService.criarPagamentoOnlineParcelaUnica(movParcelaVO, usuarioVO, nrParcelasOperadora);
            pagamentoCartaoTO.setTransacaoPresencial(false);
            pagamentoCartaoTO.setTipoParcelamentoStone(this.convenioCobrancaVO.getTipoParcelamentoStone());
            pagamentoCartaoTO.setOrigemCobranca(OrigemCobrancaEnum.WEB_SERVICE);
            pagamentoCartaoTO.setAsync(false);
            pagamentoOnlineService = new PagamentoOnlineService(pagamentoCartaoTO, this.getCon());
            return pagamentoOnlineService.realizarCobranca();
        } finally {
            pagamentoOnlineService = null;
            pagamentoCartaoAgrupadoService = null;
        }
    }

    private List<MovParcelaVO> consultarPagamentos(Date dia) throws Exception {
        this.convenioCobrancaVO.setEmpresa(this.empresaDAO.consultarPorChavePrimaria(this.convenioCobrancaVO.getEmpresa().getCodigo(), Uteis.NIVELMONTARDADOS_GESTAOREMESSA));
        List<MovParcelaVO> listaBruta = this.movParcelaDAO.consultarParcelasEmAbertoParaPagamento(this.convenioCobrancaVO, dia, Uteis.NIVELMONTARDADOS_MINIMOS);
        return processarParcelas(listaBruta);
    }

    public TransacaoVO realizarCobrancaAgrupadoVendasOnline(List<MovParcelaVO> listaMovParcelas, PessoaVO pessoaPagadorVO,
                                                            UsuarioVO usuarioVO, Integer nrParcelas, InstalmentTypeInstlmtTp tipoParcelamentoStone,
                                                            String cvv, OrigemCobrancaEnum origemCobrancaEnum, Double desconto,
                                                            List<RetornoVendaTO> retornoVendaTOList, String ipCliente) throws Exception {
        TransacaoVO transacaoVO = null;
        try {
            if (UteisValidacao.emptyList(listaMovParcelas)) {
                throw new Exception("Nenhuma parcela informada.");
            }

            if (usuarioVO == null || UteisValidacao.emptyNumber(usuarioVO.getCodigo())) {
                throw new Exception("Usuário não informado.");
            }

            if (pessoaPagadorVO == null || UteisValidacao.emptyNumber(pessoaPagadorVO.getCodigo())) {
                throw new Exception("Pessoa pagador não informado.");
            }

            try {
                Uteis.logarDebug("Origem: " + origemCobrancaEnum.getDescricao() + " | Start Process | realizarCobrancaAgrupado -> Convênio: " + this.convenioCobrancaVO.getDescricao() + " | Tipo: " + this.convenioCobrancaVO.getTipo().getDescricao() + " | Pagador: " +
                        pessoaPagadorVO.getCodigo() + " - " + pessoaPagadorVO.getNome() + " | Parcelas: " + Uteis.retornarCodigos(listaMovParcelas));
            } catch (Exception ex) {
            }

            /**
             * validar se não existe transação com essa parcela com situação PENDENTE ou Aprovada..
             * Evitar erros de pagamento duplicado.
             * by Luiz Felipe 20/04/2020
             */

            Integer empresa = listaMovParcelas.get(0).getEmpresa().getCodigo();
            for (MovParcelaVO movParcelaVO : listaMovParcelas) {
                if (!empresa.equals(movParcelaVO.getEmpresa().getCodigo())) {
                    throw new Exception("As parcelas devem ser da mesma empresa.");
                }
                this.movParcelaDAO.validarMovParcelaComTransacaoConcluidaOuPendente(movParcelaVO.getCodigo());
            }

            //construir o cartão
            CartaoCreditoTO cartao = construirCartaoCredito(pessoaPagadorVO, listaMovParcelas, empresa, usuarioVO);
            cartao.setOrigemCobranca(origemCobrancaEnum);
            cartao.setIpClientePacto(ipCliente);

            //desconto régua de cobrança PactoPay
            cartao.setDesconto(desconto);

            //se informou cvv é uma transação presencial
            //não recorrencia.
            //Exceção para getnet, que deve enviar como não presencial quando vem do do vendas online, pra evitar problema com o antifraude lá
            boolean isGetNetOrigemVendasOnline = convenioCobrancaVO != null && convenioCobrancaVO.getTipo() != null
                    && convenioCobrancaVO.getTipo().equals(TipoConvenioCobrancaEnum.DCC_GETNET_ONLINE);
            if (!UteisValidacao.emptyString(cvv) && !isGetNetOrigemVendasOnline) {
                cartao.setTransacaoPresencial(true);
                cartao.setCodigoSeguranca(cvv);
            }

            if (!UteisValidacao.emptyNumber(nrParcelas)) {
                cartao.setParcelas(nrParcelas);
                if (convenioCobrancaVO != null &&
                        convenioCobrancaVO.getTipo() != null &&
                        convenioCobrancaVO.getTipo().equals(TipoConvenioCobrancaEnum.DCC_STONE_ONLINE)) {
                    if (nrParcelas > 1) {
                        if (tipoParcelamentoStone == null) {
                            throw new Exception("Tipo Parcelamento Stone não informado.");
                        }
                        cartao.setTipoParcelamentoStone(tipoParcelamentoStone);
                    } else {
                        cartao.setTipoParcelamentoStone(InstalmentTypeInstlmtTp.A_VISTA);
                    }
                }
            }

            //desconto só é utilizado no link de pagamento
            cartao.setAplicarDesconto(!UteisValidacao.emptyNumber(desconto));

            //preencher o valor que será cobrando
            cartao.preencherValor();

            AprovacaoServiceInterface service = CobrancaOnlineService.getImplementacaoAprovacaoService(this.convenioCobrancaVO.getTipo().getTipoTransacao(),
                    empresa, this.convenioCobrancaVO.getCodigo(), this.convenioCobrancaVO.isPactoPay(), getCon());

            new AragornService().povoarCartaoCreditoTO(cartao);
            transacaoVO = service.tentarAprovacao(cartao);

            try {
                if (transacaoVO != null && !UteisValidacao.emptyNumber(transacaoVO.getCodigo())) {
                    //incrementa o número de tentativas da parcela no convenio
                    for (MovParcelaVO movParcelaVO : listaMovParcelas) {
                        this.movParcelaDAO.incrementarNrTentativasParcelaConvenio(movParcelaVO, transacaoVO.getConvenioCobrancaVO());
                    }
                }
                if (transacaoVO != null && transacaoVO.getSituacao().equals(SituacaoTransacaoEnum.CONCLUIDA_COM_SUCESSO)) {

                    //se tem desconto então deve ser aplicado o desconto nas parcelas
                    if (cartao.isAplicarDesconto() && !UteisValidacao.emptyNumber(cartao.getDesconto())) {
                        transacaoVO.setDesconto(Uteis.arredondarForcando2CasasDecimais(cartao.getDesconto()));

                        List<MovParcelaVO> parcelasOriginais = new ArrayList<>();
                        parcelasOriginais.addAll(cartao.getListaParcelas());

                        List<MovParcelaVO> parcelasRenegociar = new ArrayList<>();
                        parcelasRenegociar.addAll(cartao.getListaParcelas());

                        // Parcela desconto
                        MovParcelaVO parcelaRenegociar = new MovParcelaVO();
                        parcelaRenegociar.setDescricao("DESCONTOS");
                        parcelaRenegociar.setValorParcela(Uteis.arredondarForcando2CasasDecimais(cartao.getDesconto()));
                        parcelaRenegociar.setDataVencimento(Calendario.hoje());
                        parcelasRenegociar.add(parcelaRenegociar);

                        MovParcelaVO parcelaDesconto = new MovParcelaVO();
                        parcelaDesconto.setDescricao("");
                        parcelaDesconto.setValorParcela(cartao.getDesconto());
                        parcelaDesconto.setDataVencimento(Calendario.hoje());

                        // Parcelas Renegociadas
                        List<MovParcelaVO> parcelasRenegociadas = new ArrayList<>();
                        MovParcelaVO novaParcela = (MovParcelaVO) cartao.getListaParcelas().get(0).getClone(true);
                        novaParcela.setDescricao("PARCELA RENEGOCIADA");
                        novaParcela.setValorParcela(Uteis.arredondarForcando2CasasDecimais(cartao.getValor()));
                        novaParcela.setDataRegistro(Calendario.hoje());
                        parcelasRenegociadas.add(novaParcela);

                        this.movParcelaDAO.renegociarParcelas(parcelasRenegociar, parcelasRenegociadas, parcelaDesconto, new MovParcelaVO(), "DE", false, null,
                                null, 0.0, false, usuarioVO, true, false, false, null, null);
                        List<MovParcelaVO> parcelasNovas = new ArrayList<>();
                        parcelasNovas.addAll(parcelasRenegociadas);

                        //ajustar transacaomovparcela

                        //excluir o relacionamento que foi criado com as parcelas anteriores
                        this.transacaoMovParcelaDAO.excluirPorTransacao(transacaoVO.getCodigo());

                        //adicionar as parcelas novas
                        cartao.setListaParcelas(parcelasNovas);
                        transacaoVO.setListaParcelas(parcelasNovas);

                        //incluir no banco o relacionamento
                        this.transacaoDAO.incluirTransacaoMovParcelas(transacaoVO);

                        gerarLogRenegociacao(transacaoVO, parcelasOriginais, parcelasNovas, parcelaDesconto, usuarioVO);
                    }

                    List<MovPagamentoVO> listaPagamento = new ArrayList<>();
                    MovPagamentoVO movPagamentoVO = new MovPagamentoVO();
                    boolean temFormaPagamento = false;
                    FormaPagamento fm = new FormaPagamento(getCon());
                    List<FormaPagamentoVO> formasPgtConvenio = fm.consultarFormaPagamentoComConvenioCobranca(true, false, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
                    fm = null;
                    for (FormaPagamentoVO form : formasPgtConvenio) {
                        if (form.getConvenioCobrancaVO() != null && form.getConvenioCobrancaVO().getCodigo().equals(transacaoVO.getConvenioCobrancaVO().getCodigo())) {
                            movPagamentoVO.setFormaPagamento(form);
                            temFormaPagamento = true;
                            break;
                        }
                    }
                    if (!temFormaPagamento) {
                        movPagamentoVO.setFormaPagamento(this.formaPagamentoCartaoRecorrenteVO);
                    }

                    Adquirente adquirenteDAO = null;
                    try {
                        adquirenteDAO = new Adquirente(getCon());
                        movPagamentoVO.setAdquirenteVO(adquirenteDAO.obterAdquirenteTransacao(transacaoVO));
                    } catch (Exception ex) {
                        ex.printStackTrace();
                    } finally {
                        adquirenteDAO = null;
                    }

                    movPagamentoVO.setMovPagamentoEscolhida(true);
                    movPagamentoVO.setValor(cartao.getValor());
                    movPagamentoVO.setValorTotal(cartao.getValor());
                    movPagamentoVO.setPessoa(pessoaPagadorVO);
                    movPagamentoVO.setNomePagador(pessoaPagadorVO.getNome());
                    movPagamentoVO.setOpcaoPagamentoCartaoCredito(true);
                    if (!UteisValidacao.emptyNumber(nrParcelas)) {
                        movPagamentoVO.setNrParcelaCartaoCredito(nrParcelas);
                    } else {
                        movPagamentoVO.setNrParcelaCartaoCredito(1);
                    }

                    OperadoraCartao operadoraDAO;
                    try {
                        operadoraDAO = new OperadoraCartao(getCon());
                        movPagamentoVO.setOperadoraCartaoVO(operadoraDAO.consultarOuCriaPorCodigoIntegracao(this.convenioCobrancaVO.getTipo(), cartao.getParcelas(), cartao.getBand()));
                    } catch (Exception ex) {
                        ex.printStackTrace();
                    } finally {
                        operadoraDAO = null;
                    }

                    movPagamentoVO.setResponsavelPagamento(usuarioVO);
                    movPagamentoVO.setNsu(transacaoVO.getNSU());
                    movPagamentoVO.getEmpresa().setCodigo(empresa);
                    movPagamentoVO.setConvenio(transacaoVO.getConvenioCobrancaVO());

                    prepararMovPagamentoAcumuloAutomatico(movPagamentoVO, listaMovParcelas);

                    //verificar se todas as parcelas são do mesmo contrato;
                    Integer contrato = listaMovParcelas.get(0).getContrato().getCodigo();
                    for (MovParcelaVO movParcelaVO : listaMovParcelas) {
                        if (!contrato.equals(movParcelaVO.getContrato().getCodigo())) {
                            contrato = 0;
                            break;
                        }
                    }
                    ContratoVO contratoVO = new ContratoVO();
                    contratoVO.setCodigo(contrato);

                    listaPagamento.add(movPagamentoVO);
                    ReciboPagamentoVO reciboObj = this.movPagamentoDAO.incluirListaPagamento(
                            listaPagamento, cartao.getListaParcelas(), null,
                            contratoVO, false, 0.0);

                    transacaoVO.setReciboPagamento(reciboObj.getCodigo());
                    transacaoVO.setMovPagamento(reciboObj.getPagamentosDesteRecibo().get(0).getCodigo());

                    if (UteisValidacao.emptyString(transacaoVO.getCodigoExterno())) {
                        transacaoVO.setCodigoExterno(transacaoVO.getValorAtributoResposta(APF.Transacao));
                    }
                    this.transacaoDAO.alterar(transacaoVO);

                    try {
                        if (reciboObj.getEmpresa().isNotificarWebhook()) {
                            Cliente clienteDAO = new Cliente(this.getCon());
                            ClienteVO clienteVO = clienteDAO.consultarPorCodigoPessoa(reciboObj.getPessoaPagador().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                            ZillyonWebFacade zwFacade = new ZillyonWebFacade(this.getCon());
                            zwFacade.notificarPagamento(clienteVO, reciboObj);
                            clienteDAO = null;
                            zwFacade = null;
                        }
                    } catch (Exception ex) {
                        ex.printStackTrace();
                    }
                }
            } catch (Exception e) {
                this.transacaoDAO.alterarMessagemErro(transacaoVO, e.getMessage());
                retornoVendaTOList.get(0).setTransacaoVO(transacaoVO);
                throw e;
            }
        } catch (Exception ex) {
            ex.printStackTrace();
            Uteis.logarDebug("End Process | realizarCobrancaAgrupado -> " + this.convenioCobrancaVO.getTipo().getDescricao() + " | Pagador: " +
                    pessoaPagadorVO.getCodigo() + " - " + pessoaPagadorVO.getNome() + " | Parcelas: " + Uteis.retornarCodigos(listaMovParcelas) + " | Erro processar: " + ex.getMessage());
            throw ex;
        } finally {
            processarMovParcelaResultadoCobranca(transacaoVO);
            Uteis.logarDebug("End Process | realizarCobrancaAgrupado -> " + this.convenioCobrancaVO.getTipo().getDescricao() + " | Pagador: " +
                    pessoaPagadorVO.getCodigo() + " - " + pessoaPagadorVO.getNome() + " | Parcelas: " + Uteis.retornarCodigos(listaMovParcelas));
        }
        return transacaoVO;
    }

    private void gerarLogRenegociacao(TransacaoVO transacaoVO, List<MovParcelaVO> listaMovParcelaOriginais,
                                      List<MovParcelaVO> listaMovParcelaNovas, MovParcelaVO movParcelaDesconto,
                                      UsuarioVO usuarioVO) {
        Log logDAO;
        Cliente clienteDAO;
        Empresa empresaDAO;
        try {
            logDAO = new Log(this.getCon());
            clienteDAO = new Cliente(this.getCon());
            empresaDAO = new Empresa(this.getCon());

            LogVO obj = new LogVO();
            if (listaMovParcelaOriginais.size() == 1) {
                obj.setChavePrimaria(listaMovParcelaOriginais.get(0).getCodigo().toString());
            } else {
                obj.setChavePrimaria("0");
            }
            obj.setNomeEntidade("PARCELA");
            obj.setNomeEntidadeDescricao("parcela");
            obj.setOperacao("RENEGOCIAÇÃO - PARCELA");
            obj.setResponsavelAlteracao(usuarioVO.getNome());
            obj.setUserOAMD(usuarioVO.getUserOamd());
            obj.setNomeCampo("TODOS");
            obj.setPessoa(listaMovParcelaOriginais.get(0).getPessoa().getCodigo());

            Integer codCliente = clienteDAO.obterCodigoClientePorPessoa(listaMovParcelaOriginais.get(0).getPessoa().getCodigo());
            if (!UteisValidacao.emptyNumber(codCliente)) {
                obj.setCliente(codCliente);
            }
            EmpresaVO empresaVO = empresaDAO.consultarPorCodigo(listaMovParcelaOriginais.get(0).getCodigoEmpresa(), Uteis.NIVELMONTARDADOS_MINIMOS);

            StringBuilder campoAlterado = new StringBuilder();
            campoAlterado.append("-------------------------------------- \n\r");
            campoAlterado.append("Transação: ").append(transacaoVO.getCodigo()).append(" \n\r");
            campoAlterado.append("Origem renegociação: ").append(transacaoVO.getOrigem().getDescricao()).append(" \n\r");
            campoAlterado.append("Responsável pelo Lançamento: ").append(usuarioVO.getNome()).append(" \n\r");
            campoAlterado.append("Empresa: ").append(empresaVO.getNome()).append(" \n\r");
            campoAlterado.append("-------------------------------------- \n\r");
            campoAlterado.append("Foi aplicado um desconto no valor de: ").append(movParcelaDesconto.getValorParcela_Apresentar()).append(" \n\r");
            campoAlterado.append("-------------------------------------- \n\r");
            campoAlterado.append("Parcelas renegociadas para: \n\r");
            for (MovParcelaVO parcelaNova : listaMovParcelaNovas) {
                campoAlterado.append("Código: ").append(parcelaNova.getCodigo()).append(" \n\n");
                campoAlterado.append("Descrição: ").append(parcelaNova.getDescricao()).append(" \n\n");
                campoAlterado.append("Valor: ").append(parcelaNova.getValorParcela_Apresentar()).append(" \n\n");
                campoAlterado.append("Dt. Vencimento: ").append(parcelaNova.getDataVencimento_Apresentar()).append("\n\r");
            }
            obj.setValorCampoAlterado(campoAlterado.toString());

            StringBuilder campoAnterior = new StringBuilder();
            campoAnterior.append("Parcelas que foram renegociadas: \n\r");
            for (MovParcelaVO parcelaOriginal : listaMovParcelaOriginais) {
                campoAnterior.append("Código: ").append(parcelaOriginal.getCodigo()).append(" \n\n");
                campoAnterior.append("Descrição: ").append(parcelaOriginal.getDescricao()).append(" \n\n");
                campoAnterior.append("Valor: ").append(parcelaOriginal.getValorParcela_Apresentar()).append(" \n\n");
                campoAnterior.append("Dt. Vencimento: ").append(parcelaOriginal.getDataVencimento_Apresentar()).append("\n\r");
            }
            obj.setValorCampoAnterior(campoAnterior.toString());
            obj.setDataAlteracao(Calendario.hoje());

            logDAO.incluirSemCommit(obj);
        } catch (Exception ex) {
            ex.printStackTrace();
        } finally {
            logDAO = null;
            clienteDAO = null;
            empresaDAO = null;
        }
    }

    private void prepararMovPagamentoAcumuloAutomatico(MovPagamentoVO movPagamentoVO, List<MovParcelaVO> listaMovParcelas) {
        ParceiroFidelidadeZW parceiroFidelidadeZW = null;
        try {
            parceiroFidelidadeZW = new ParceiroFidelidadeZW(getCon());
            for (MovParcelaVO movParcelaVO : listaMovParcelas) {
                parceiroFidelidadeZW.prepararMovPagamentoAcumuloAutomatico(movPagamentoVO, movParcelaVO);
            }
        } catch (Exception ex) {
            Uteis.logar(null, "prepararMovPagamentoAcumuloAutomatico ERRO: " + ex.getMessage());
        } finally {
            parceiroFidelidadeZW = null;
        }
    }

    private void processarMovParcelaResultadoCobranca(TransacaoVO transacaoVO) {
        MovParcelaResultadoCobranca dao = null;
        try {
            if (transacaoVO == null || UteisValidacao.emptyList(transacaoVO.getListaParcelas())) {
                return;
            }

            dao = new MovParcelaResultadoCobranca(getCon());
            dao.processarParcelas(transacaoVO.getListaParcelas());
        } catch (Exception ex) {
            ex.printStackTrace();
            Uteis.logar(null, "processarMovParcelaResultadoCobranca ERRO: " + ex.getMessage());
        } finally {
            dao = null;
        }
    }

    private CartaoCreditoTO construirCartaoCredito(PessoaVO pessoaPagadorVO, List<MovParcelaVO> listaMovParcelas,
                                                   Integer empresa, UsuarioVO usuarioVO) throws Exception {
        CartaoCreditoTO cartao = new CartaoCreditoTO();
        cartao.setIdPessoaCartao(pessoaPagadorVO.getCodigo());
        cartao.setUsuarioResponsavel(usuarioVO);
        cartao.setEmpresa(empresa);
        cartao.getListaParcelas().addAll(listaMovParcelas);
        cartao.setParcelas(1);


        AutorizacaoCobrancaClienteVO autorizacaoVO = obterAutorizacao(pessoaPagadorVO, listaMovParcelas);

        if (autorizacaoVO == null) {
            throw new Exception("Não foi encontrado uma autorização de cobrança compatível. " +
                    "A autorização do cliente está com o 'tipo parcelas a cobrar' que não condiz com a situação à qual está tentando cobrar agora.");
        }

        cartao.setNumero(autorizacaoVO.getNumeroCartao());
        cartao.setValidade(autorizacaoVO.getValidadeCartao());
        cartao.setBand(autorizacaoVO.getOperadoraCartao());
        cartao.setNomeTitular(autorizacaoVO.getNomeTitularCartao());
        cartao.setTokenAragorn(autorizacaoVO.getTokenAragorn());
        cartao.setTokenVindi(autorizacaoVO.getCodigoExterno());
        cartao.setTokenCielo(autorizacaoVO.getTokenCielo());
        cartao.setTokenPagoLivre(autorizacaoVO.getTokenPagoLivre());
        return cartao;
    }

    private AutorizacaoCobrancaClienteVO obterAutorizacao(PessoaVO pessoaVO, List<MovParcelaVO> listaMovParcelas) throws Exception {
        AutorizacaoCobrancaCliente autoDAO = null;
        try {
            autoDAO = new AutorizacaoCobrancaCliente(getCon());

            List<AutorizacaoCobrancaClienteVO> cobrancaClienteVOList = autoDAO.consultarPorPessoaTipoAutorizacao(pessoaVO.getCodigo(),
                    TipoAutorizacaoCobrancaEnum.CARTAOCREDITO, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
            for (AutorizacaoCobrancaClienteVO auto : cobrancaClienteVOList) {
                boolean compativel = auto.getConvenio().getTipo().equals(this.convenioCobrancaVO.getTipo());
                if (!compativel && (this.convenioCobrancaVO.getEmpresa().isHabilitarReenvioAutomaticoRemessa() || isForcarValidarAutorizacaoCobranca())) {
                    compativel = auto.isBandeiraValidaParaTipoConvenioCobranca(this.convenioCobrancaVO.getTipo());
                }
                if (compativel) {
                    boolean todasParcelasSaoCompativeis = true;
                    for (MovParcelaVO movParcelaVO : listaMovParcelas) {
                        //preencer o tipos de produtos
                        movParcelaVO.setTipoProdutos(this.movParcelaDAO.consultaTiposProdutosMovParcela(movParcelaVO.getCodigo()));
                        if (!auto.isParcelaCompativel(movParcelaVO, this.convenioCobrancaVO.getEmpresa().isGerarRemessaContratoCancelado(), getCon())) {
                            todasParcelasSaoCompativeis = false;
                        }
                    }
                    if (todasParcelasSaoCompativeis) {
                        return auto;
                    }
                }
            }
            return null;
        } catch (Exception ex) {
            ex.printStackTrace();
            throw ex;
        } finally {
            autoDAO = null;
        }
    }

    public boolean isForcarValidarAutorizacaoCobranca() {
        return forcarValidarAutorizacaoCobranca;
    }

    public void setForcarValidarAutorizacaoCobranca(boolean forcarValidarAutorizacaoCobranca) {
        this.forcarValidarAutorizacaoCobranca = forcarValidarAutorizacaoCobranca;
    }
}
