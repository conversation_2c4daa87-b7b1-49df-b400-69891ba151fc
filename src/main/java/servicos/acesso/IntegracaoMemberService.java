package servicos.acesso;

import br.com.pactosolucoes.atualizadb.processo.AplicarMatriculaImportacao;
import br.com.pactosolucoes.comuns.json.JSONMapper;
import br.com.pactosolucoes.comuns.util.JSFUtilities;
import br.com.pactosolucoes.enumeradores.OrigemSistemaEnum;
import br.com.pactosolucoes.enumeradores.TipoColaboradorEnum;
import br.com.pactosolucoes.enumeradores.TipoEnderecoEnum;
import br.com.pactosolucoes.enumeradores.TipoTelefoneEnum;
import br.com.pactosolucoes.oamd.controle.basico.DAO;
import importador.ImportacaoCache;
import importador.ImportacaoConfigTO;
import importador.UteisImportacao;
import importador.contrato.ImportarContrato;
import importador.membersevo.ImportadorMembersEvo;
import importador.membersevo.threads.MemberDataApiCallAble;
import importador.membersevo.threads.ThreadImportacaoMembers;
import negocio.comuns.acesso.AutorizacaoAcessoGrupoEmpresarialVO;
import negocio.comuns.acesso.IntegracaoMemberVO;
import negocio.comuns.acesso.integracao.member.*;
import negocio.comuns.acesso.integracao.member.enums.*;
import negocio.comuns.acesso.integracao.member.financeiro.ReceivablesViewTO;
import negocio.comuns.acesso.integracao.member.financeiro.SaleItemApiViewTO;
import negocio.comuns.acesso.integracao.member.financeiro.SalesApiViewTO;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.*;
import negocio.comuns.basico.enumerador.TipoImportacaoEnum;
import negocio.comuns.basico.enumerador.TipoLogEnum;
import negocio.comuns.basico.enumerador.TipoPessoa;
import negocio.comuns.basico.enumerador.TipoPessoaEnum;
import negocio.comuns.contrato.*;
import negocio.comuns.financeiro.*;
import negocio.comuns.financeiro.enumerador.TipoFormaPagto;
import negocio.comuns.plano.*;
import negocio.comuns.plano.enumerador.TipoDesconto;
import negocio.comuns.plano.enumerador.TipoProduto;
import negocio.comuns.utilitarias.*;
import negocio.facade.jdbc.acesso.IntegracaoMember;
import negocio.facade.jdbc.acesso.Member;
import negocio.facade.jdbc.arquitetura.Log;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.arquitetura.Usuario;
import negocio.facade.jdbc.arquitetura.ZillyonWebFacade;
import negocio.facade.jdbc.basico.*;
import negocio.facade.jdbc.basico.webservice.IntegracaoImportacao;
import negocio.facade.jdbc.contrato.*;
import negocio.facade.jdbc.financeiro.*;
import negocio.facade.jdbc.plano.PlanoCondicaoPagamento;
import negocio.facade.jdbc.plano.PlanoDuracao;
import negocio.facade.jdbc.plano.Produto;
import negocio.facade.jdbc.utilitarias.Conexao;
import negocio.oamd.RedeEmpresaVO;
import org.apache.http.HttpEntity;
import org.apache.http.auth.AuthScope;
import org.apache.http.auth.UsernamePasswordCredentials;
import org.apache.http.client.CredentialsProvider;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.impl.client.BasicCredentialsProvider;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClientBuilder;
import org.apache.http.util.EntityUtils;
import org.json.JSONArray;
import org.json.JSONObject;
import relatorio.negocio.comuns.sad.SituacaoClienteSinteticoEnum;
import servicos.impl.microsservice.acessosistema.AcessoSistemaMSService;
import servicos.oamd.RedeEmpresaService;
import servicos.util.ExecuteRequestHttpService;

import java.io.IOException;
import java.net.URLEncoder;
import java.sql.*;
import java.text.ParseException;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.Date;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicLong;
import java.util.concurrent.locks.ReentrantLock;
import java.util.logging.Logger;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

public class IntegracaoMemberService {

    private static final String URL_BASE = "https://evo-integracao.w12app.com.br/api/v1/";
    private static final String URL_BASE_V2 = "https://evo-integracao.w12app.com.br/api/v2/";
    private static final String ENDPOINT = "members";
    private static final String ENDPOINT_PROSPECTS = "prospects";
    private static final String ENDPOINT_SALES = "sales";
    private static final String ENDPOINT_RECEIVABLES = "receivables";
    private static final String ENDPOINT_MEMBER_SHIP = "membership";
    private static final String ENDPOINT_MEMBER_MEMBER_SHIP = "membermembership";
    private static final int DIAS_MAXIMO_CONTRATO_MENSAL = 31;

    private static final Integer apiCallLimiteQuotaPerMinute = 600;
    private static Integer countCalls = 0;
    private static Long milisStartCount = 0l;

    public final Logger logger = Logger.getLogger(IntegracaoMemberService.class.getName());
    private Produto produtoDAO;
    private Member memberDAO;
    private Cliente clienteDAO;
    private Pessoa pessoaDAO;
    private Vinculo vinculoDAO;
    private Familiar familiarDAO;
    private Parentesco parentescoDAO;
    private IntegracaoMember integracaoMemberDAO;
    private ZillyonWebFacade zillyonWebFacadeDAO;
    private Contrato contratoDAO;
    private ContratoOperacao contratoOperacaoDAO;
    private ContratoDependente contratoDependenteDAO;
    private MovParcela movParcelaDAO;
    private MovProduto movProdutoDAO;
    private MovProdutoModalidade movProdutoModalidadeDAO;
    private MovPagamento movPagamentoDAO;
    private ReciboPagamento reciboPagamentoDAO;
    private PlanoDuracao planoDuracaoDAO;
    private PlanoCondicaoPagamento planoCondicaoPagamentoDAO;
    private AulaAvulsaDiaria aulaAvulsaDiariaDao;
    private Log logDAO;
    private Boleto boletoDAO;
    private ProcessoImportacao processoDAO;
    private ProcessoImportacaoLog processoLogDAO;
    private ProcessoImportacaoItem processoItemDAO;
    private ImportacaoConfigTO importacaoConfigTO;
    private ThreadImportacaoMembers threadImportacaoMembers;

    private String chave;
    private RedeEmpresaVO redeEmpresa;

    private ProdutoVO freePassIntegracao;
    private ImportacaoCache cache;
    private Connection con;
    private TipoOperacaoIntegracaoMembersEnum operacao;
    private List<String> falhas;
    private List<String> falhasItemAtual = new ArrayList<>();
    private Map<Integer, List<ReceivablesViewTO>> mapaSalesReceivables = new HashMap<>();
    private Long miliSegundosRequisicoesMember = 0l;
    List<String> userAgents = Arrays.asList(
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********",
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:136.0) Gecko/20100101 Firefox/136.0",
            "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
            "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    );

    public IntegracaoMemberService() {
    }

    public IntegracaoMemberService(Connection con, String chave, ImportacaoCache cache, TipoOperacaoIntegracaoMembersEnum operacao) {
        try {
            this.chave = chave;
            this.con = con;
            this.cache = cache;
            this.operacao = operacao;
            inicializar();
        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }

    public IntegracaoMemberService(Connection con, ImportacaoCache cache, ImportacaoConfigTO importacaoConfigTO) {
        try {
            this.chave = importacaoConfigTO.getChave();
            this.con = con;
            this.cache = cache;
            this.operacao = importacaoConfigTO.getTipoOperacaoIntegracaoMembersEnum();
            this.importacaoConfigTO = importacaoConfigTO;
            inicializar();
        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }

    public void inicializar() {
        try {
            memberDAO = new Member(con);
            integracaoMemberDAO = new IntegracaoMember(con);
            clienteDAO = new Cliente(con);
            zillyonWebFacadeDAO = new ZillyonWebFacade(con);
            produtoDAO = new Produto(con);

            if (operacao.equals(TipoOperacaoIntegracaoMembersEnum.MEMBERS_FREEPASS)) {
                redeEmpresa = RedeEmpresaService.obterRedePorChave(chave);
                if (redeEmpresa != null) {
                    Uteis.logar(null, "Rede de Empresa: " + redeEmpresa.getNome());
                }
                consultarOuCriarProdutoFreePassIntegracao();
            } else {
                Conexao.guardarConexaoForJ2SE(con);
                familiarDAO = new Familiar(con);
                parentescoDAO = new Parentesco(con);
                pessoaDAO = new Pessoa(con);
                vinculoDAO = new Vinculo(con);
                contratoDAO = new Contrato(con);
                contratoOperacaoDAO = new ContratoOperacao(con);
                contratoDependenteDAO = new ContratoDependente(con);
                aulaAvulsaDiariaDao = new AulaAvulsaDiaria(con);
                movParcelaDAO = new MovParcela(con);
                movProdutoDAO = new MovProduto(con);
                movProdutoModalidadeDAO = new MovProdutoModalidade(con);
                movPagamentoDAO = new MovPagamento(con);
                reciboPagamentoDAO = new ReciboPagamento(con);
                planoDuracaoDAO = new PlanoDuracao(con);
                planoCondicaoPagamentoDAO = new PlanoCondicaoPagamento(con);
                logDAO = new Log(con);
                boletoDAO = new Boleto(con);
                processoDAO = new ProcessoImportacao(con);
                processoLogDAO = new ProcessoImportacaoLog(con);
                processoItemDAO = new ProcessoImportacaoItem(con);
            }

            falhas = new ArrayList<>();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public String getAllMembers(IntegracaoMemberVO integracaoMemberVO, Map<String, String> params, Integer take, Integer skip) throws Exception {
        final String url = URL_BASE + ENDPOINT + "?take=%d&skip=%d";

        StringBuilder parametrosExtras = new StringBuilder();
        if (params != null) {
            for (String key : params.keySet()) {
                if (!UteisValidacao.emptyString(params.get(key))) {
                    parametrosExtras.append(String.format("&%s=%s", key, URLEncoder.encode(params.get(key), "iso-8859-1")));
                }
            }
        }

        return executeRequest(String.format(url, take, skip) + parametrosExtras.toString(), integracaoMemberVO);
    }

    public List<MembersApiViewTO> getAllMembers(IntegracaoMemberVO integracaoMemberVO, Map<String, String> params) throws Exception {
        final String url = URL_BASE + ENDPOINT + "?take=%d&skip=%d";

        StringBuilder parametrosExtras = new StringBuilder();
        if (params != null) {
            for (String key : params.keySet()) {
                if (!UteisValidacao.emptyString(params.get(key))) {
                    parametrosExtras.append(String.format("&%s=%s", key, URLEncoder.encode(params.get(key), "iso-8859-1")));
                }
            }
        }

        int take = 50;
        int skip = -50;

        List<MembersApiViewTO> members = new ArrayList<>();

        while (true) {
            skip += take;
            String result = executeRequest(String.format(url, take, skip) + parametrosExtras.toString(), integracaoMemberVO);
            if (!UteisValidacao.emptyString(result)) {
                JSONArray jsonArray = new JSONArray(result);
                if (jsonArray == null || jsonArray.length() == 0) {
                    break;
                }
                for (int i = 0; i < jsonArray.length(); i++) {
                    MembersApiViewTO membersApiViewTO = JSONMapper.getObject(jsonArray.getJSONObject(i), MembersApiViewTO.class);
                    membersApiViewTO.setDataJson(jsonArray.getJSONObject(i));
                    members.add(membersApiViewTO);
                }

                if (jsonArray.length() < take) {
                    break;
                }
            }
        }
        return members;
    }

    public List<ProspectsApiViewTO> getAllProspects(IntegracaoMemberVO integracaoMemberVO) throws Exception {
        final String url = URL_BASE + ENDPOINT_PROSPECTS + "?take=50&skip=%d";
        int skip = -50;

        List<ProspectsApiViewTO> prospects = new ArrayList<>();

        while (true) {
            skip = skip + 50;
            final String formattedUrl = String.format(url, skip);

            if (importacaoConfigTO.getTipoImportacaoEnum().getCodigo().equals(TipoImportacaoEnum.MEMBERS_EVO.getCodigo())) {
                Uteis.logarDebug("Consultando prospects API... - " + formattedUrl);
            }
            HttpGet request = new HttpGet(formattedUrl);

            CredentialsProvider provider = new BasicCredentialsProvider();
            provider.setCredentials(AuthScope.ANY, integracaoMemberVO.getCredentials());

            request.addHeader("User-Agent", userAgents.get(new Random().nextInt(userAgents.size())));

            try (CloseableHttpClient httpClient = HttpClientBuilder.create()
                    .setDefaultCredentialsProvider(provider)
                    .build();
                 CloseableHttpResponse response = httpClient.execute(request)) {

                HttpEntity entity = response.getEntity();
                if (entity != null) {
                    String result = EntityUtils.toString(entity);
                    JSONArray array = new JSONArray(result);
                    if (array.length() == 0) {
                        break;
                    }
                    prospects.addAll(JSONMapper.getList(array, ProspectsApiViewTO.class));
                }
            }
        }
        return prospects;
    }

    public MembersApiViewTO getMember(IntegracaoMemberVO integracaoMemberVO, Integer idMember) throws Exception {
        long inicilMillis = System.currentTimeMillis();

        final String formattedUrl = URL_BASE + ENDPOINT + "/" + idMember;
        System.out.println("\t" + formattedUrl);

        HttpGet request = new HttpGet(formattedUrl);

        CredentialsProvider provider = new BasicCredentialsProvider();
        provider.setCredentials(AuthScope.ANY, integracaoMemberVO.getCredentials());
        request.addHeader("User-Agent", userAgents.get(new Random().nextInt(userAgents.size())));

        MembersApiViewTO membersApiViewTO = null;
        try (CloseableHttpClient httpClient = HttpClientBuilder.create()
                .setDefaultCredentialsProvider(provider)
                .build();
             CloseableHttpResponse response = httpClient.execute(request)) {

            if (response.getStatusLine().getStatusCode() != 200) {
                HttpEntity entity = response.getEntity();
                if (entity != null) {
                    String result = EntityUtils.toString(entity);
                    Uteis.logarDebug("$$$ Status: " + response.getStatusLine().getStatusCode() + " - Result: " + result);
                }
                return null;
            }

            HttpEntity entity = response.getEntity();
            if (entity != null) {
                String result = EntityUtils.toString(entity);
                JSONObject object = new JSONObject(result);
                membersApiViewTO = JSONMapper.getObject(object, MembersApiViewTO.class);
            }
        }
        long totalMiliSengundos = (System.currentTimeMillis() - inicilMillis);
        Uteis.logarDebug("\tIntegracaoMemberService -> getMember levou " + totalMiliSengundos + " milissegundos.");
        miliSegundosRequisicoesMember += totalMiliSengundos;
        return membersApiViewTO;
    }

    public List<MembersApiViewTO> getMembersByDocument(IntegracaoMemberVO integracaoMemberVO, String document) throws Exception {
        String url = URL_BASE + ENDPOINT;
        url += "?take=50&skip=0";
        url += "&document=" + document;

        List<MembersApiViewTO> members = new ArrayList<>();
        String result = executeRequest(url, integracaoMemberVO);
        try {
            members.addAll(JSONMapper.getList(new JSONArray(result), MembersApiViewTO.class));
        } catch (Exception e) {
            e.printStackTrace();
            System.out.println(result);
        }

        return members;
    }


    public List<Integer> obterIdsSales(IntegracaoMemberVO integracaoMemberVO, Integer idMember) throws Exception {
        String url = URL_BASE + ENDPOINT_SALES;
        url += "?take=50&skip=%d&showReceivables=false&onlyMembership=false&atLeastMonthly=false&showOnlyActiveMemberships=false";
        if (!UteisValidacao.emptyNumber(idMember)) {
            url += "&idMember=" + idMember;
        }
        if (integracaoMemberVO.getDataInicialConsiderarLancamentos() != null) {
            url += "&dateSaleStart=" + Calendario.getData(integracaoMemberVO.getDataInicialConsiderarLancamentos(), "yyyy-MM-dd");
        }

        int skip = -50;

        List<Integer> ids = new ArrayList<>();

        while (true) {
            long inicilMillis = System.currentTimeMillis();

            skip = skip + 50;
            final String formattedUrl = String.format(url, skip);
            System.out.println("\t" + formattedUrl);

            HttpGet request = new HttpGet(formattedUrl);
            request.addHeader("User-Agent", userAgents.get(new Random().nextInt(userAgents.size())));

            CredentialsProvider provider = new BasicCredentialsProvider();
            provider.setCredentials(AuthScope.ANY, integracaoMemberVO.getCredentials());
            request.addHeader("User-Agent", userAgents.get(new Random().nextInt(userAgents.size())));

            try (CloseableHttpClient httpClient = HttpClientBuilder.create()
                    .setDefaultCredentialsProvider(provider)
                    .build();
                 CloseableHttpResponse response = httpClient.execute(request)) {

                HttpEntity entity = response.getEntity();
                if (entity != null) {
                    String result = EntityUtils.toString(entity);
                    JSONArray jsonArray = new JSONArray(result);
                    if (jsonArray.length() == 0) {
                        break;
                    }
                    List<SalesApiViewTO> sales = JSONMapper.getList(jsonArray, SalesApiViewTO.class);
                    if (!UteisValidacao.emptyList(sales)) {
                        ids.addAll(sales.stream().map(s -> s.getIdSale()).collect(Collectors.toList()));
                    }
                }
            }
            long totalMiliSengundos = (System.currentTimeMillis() - inicilMillis);
            Uteis.logarDebug("\tIntegracaoMemberService -> obterIdsSales levou " + totalMiliSengundos + " milissegundos.");
            miliSegundosRequisicoesMember += totalMiliSengundos;
        }


        return ids;
    }

    public List<SalesApiViewTO> obterSalesPorIds(IntegracaoMemberVO integracaoMemberVO, List<Integer> ids) throws Exception {
        List<SalesApiViewTO> sales = new ArrayList<>();

        for (Integer id: ids) {
            long inicilMillis = System.currentTimeMillis();

            String url = URL_BASE + ENDPOINT_SALES + "/" + id;
            HttpGet request = new HttpGet(url);
            request.addHeader("User-Agent", userAgents.get(new Random().nextInt(userAgents.size())));

            System.out.println("\t" + url);

            CredentialsProvider provider = new BasicCredentialsProvider();
            provider.setCredentials(AuthScope.ANY, integracaoMemberVO.getCredentials());

            long miliHttpClienteCloseable = System.currentTimeMillis();
            CloseableHttpClient httpClient = HttpClientBuilder.create()
                    .setDefaultCredentialsProvider(provider)
                    .build();
            Uteis.logarDebug("\tIntegracaoMemberService -> getMember HttpClienteCloseable levou " + (System.currentTimeMillis() - miliHttpClienteCloseable) + " milissegundos.");


            try (CloseableHttpResponse response = httpClient.execute(request)) {

                HttpEntity entity = response.getEntity();
                if (entity != null) {
                    String result = EntityUtils.toString(entity);
                    JSONObject jsonObject = new JSONObject(result);
                    sales.add(JSONMapper.getObject(jsonObject, SalesApiViewTO.class));
                }
            }
            long totalMiliSengundos = (System.currentTimeMillis() - inicilMillis);
            Uteis.logarDebug("\tIntegracaoMemberService -> obterSalesPorIds levou " + totalMiliSengundos + " milissegundos.");
            miliSegundosRequisicoesMember += totalMiliSengundos;
        }

        return sales;
    }

    public List<ReceivablesViewTO> obterReceivables(IntegracaoMemberVO integracaoMemberVO, Integer idMember, boolean filterRegistrationDateStart, boolean filterChargeDateStart, Integer idSale, Integer idReceivable) throws Exception {
        String url = URL_BASE + ENDPOINT_RECEIVABLES;
        url += "?take=50&skip=%d";
        if (!UteisValidacao.emptyNumber(idMember)) {
            url += "&memberId=" + idMember;
        }
        if (filterRegistrationDateStart) {
            url += "&registrationDateStart=" + Calendario.getDataAplicandoFormatacao(integracaoMemberVO.getDataInicialConsiderarLancamentos(), "yyyy-MM-dd");
        }
        if (filterChargeDateStart) {
            url += "&chargeDateStart=" + Calendario.getDataAplicandoFormatacao(integracaoMemberVO.getDataInicialConsiderarLancamentos(), "yyyy-MM-dd");
        }
        if (!UteisValidacao.emptyNumber(idSale)) {
            url += "&idSale=" + idSale;
        }
        if (!UteisValidacao.emptyNumber(idReceivable)) {
            url += "&idReceivable=" + idReceivable;
        }
        int skip = -50;

        List<ReceivablesViewTO> receivables = new ArrayList<>();

        while (true) {
            skip = skip + 50;
            final String formattedUrl = String.format(url, skip);

            try {
                String result = executeRequest(formattedUrl, integracaoMemberVO);
                JSONArray jsonArray = new JSONArray(result);
                if (jsonArray == null || jsonArray.length() == 0) {
                    break;
                }
                receivables.addAll(JSONMapper.getList(jsonArray, ReceivablesViewTO.class));
            } catch (Exception e) {
                break;
            }
        }

        return receivables;
    }

    public List<ReceivablesViewTO> obterReceivablesByFilters(IntegracaoMemberVO integracaoMemberVO, Map<String, String> params) throws Exception {
        final String url = URL_BASE + ENDPOINT_RECEIVABLES + "?take=%d&skip=%d";

        StringBuilder parametrosExtras = new StringBuilder();
        if (params != null) {
            for (String key : params.keySet()) {
                if (!UteisValidacao.emptyString(params.get(key))) {
                    parametrosExtras.append(String.format("&%s=%s", key, URLEncoder.encode(params.get(key), "iso-8859-1")));
                }
            }
        }

        int skip = -50;
        int take = 50;

        List<ReceivablesViewTO> receivables = new ArrayList<>();
        while (true) {
            skip += take;
            try {
                String result = executeRequest(String.format(url, take, skip) + parametrosExtras.toString(), integracaoMemberVO);
                JSONArray jsonArray = new JSONArray(result);
                if (jsonArray == null || jsonArray.length() == 0) {
                    break;
                }
                receivables.addAll(JSONMapper.getList(jsonArray, ReceivablesViewTO.class));
                if (jsonArray.length() < take) {
                    break;
                }
            } catch (Exception e) {
                break;
            }
        }

        return receivables;
    }

    public JSONArray executeRequest(String url, UsernamePasswordCredentials credentials) throws Exception {
        int maxTentativas = 5;
        int countTentativas = 0;
        int tempoSegundosEntreTentativas = 2;

        List<String> falhas = new ArrayList<>();

        while (countTentativas <= maxTentativas) {
            countTentativas++;
            try {
                HttpGet request = new HttpGet(url);
                request.addHeader("User-Agent", userAgents.get(new Random().nextInt(userAgents.size())));
                CredentialsProvider provider = new BasicCredentialsProvider();
                provider.setCredentials(AuthScope.ANY, credentials);

                try (CloseableHttpClient httpClient = HttpClientBuilder.create()
                        .setDefaultCredentialsProvider(provider)
                        .build();
                     CloseableHttpResponse response = httpClient.execute(request)) {

                    HttpEntity entity = response.getEntity();
                    if (entity != null) {
                        String result = EntityUtils.toString(entity);
                        return new JSONArray(result);
                    }
                }
            } catch (Exception e) {
                Thread.sleep(tempoSegundosEntreTentativas * 1000);
                if (countTentativas == maxTentativas) {
                    throw new Exception(String.format("Falha ao executar requisitacao! url: %s, falhas: %s ", url, falhas));
                }
            }
        }
        return null;
    }

    public void sincronizar(IntegracaoMemberVO integracaoMemberVO) {
        try {
            List<MemberVO> vipMembers = new ArrayList<>();

            Map<String, String> params = new HashMap<>();
            params.put("status", "1");

            int take = 50;
            int skip = -50;

            while (true) {
                skip += take;

                Uteis.logarDebug("Consultando " + take + " members da integração: " + integracaoMemberVO.getDescricao() + " Skip: " + skip);
                String result = getAllMembers(integracaoMemberVO, params, take, skip);
                List<MembersApiViewTO> members = new ArrayList<>();

                if (!UteisValidacao.emptyString(result)) {
                    JSONArray jsonArray = new JSONArray(result);
                    if (jsonArray == null || jsonArray.length() == 0) {
                        break;
                    }
                    for (int i = 0; i < jsonArray.length(); i++) {
                        MembersApiViewTO membersApiViewTO = JSONMapper.getObject(jsonArray.getJSONObject(i), MembersApiViewTO.class);
                        membersApiViewTO.setDataJson(jsonArray.getJSONObject(i));
                        members.add(membersApiViewTO);
                    }

                    if (jsonArray.length() < take) {
                        break;
                    }
                }

                for (MembersApiViewTO member : members) {
                    MemberMembershipApiViewTO membership = member.getActivePlan(integracaoMemberVO.getDescricaoParcialPlano());
                    member.setMemberships(new ArrayList<>());
                    if (membership != null) {
                        member.getMemberships().add(membership);

                        MemberVO memberVO = member.toMember();
                        memberVO.setIntegracaoMemberVO(integracaoMemberVO);
                        vipMembers.add(memberVO);
                    }
                }
                if (integracaoMemberVO.getPrimeiraSincronizacao() == null) {
                    integracaoMemberVO.setPrimeiraSincronizacao(Calendario.hoje());
                }
                integracaoMemberVO.setUltimaSincronizacao(Calendario.hoje());

                integracaoMemberDAO.alterar(integracaoMemberVO);
            }

            int qtdAtualizada = 0;
            int qtdIncluida = 0;
            for (MemberVO vipMemberNow : vipMembers) {
                MemberVO vipMember = memberDAO.consultarPorIdMember(vipMemberNow.getIdMember());
                if (vipMember == null) {
                    memberDAO.incluir(vipMemberNow);
                    qtdIncluida++;
                } else {
                    atualizarCliente(vipMember, vipMemberNow);
                    qtdAtualizada++;
                }
            }
            Uteis.logar(null, String.format("Foram incluídos %d members e Atualizados %d members", qtdIncluida, qtdAtualizada));
        } catch (Exception ex) {
            Uteis.logar(null, "Erro na hora de sincronizar: " + ex.getMessage());
            ex.printStackTrace();
        }
    }

    public void integrarMembers() throws Exception {
        List<IntegracaoMemberVO> integracaoMembers = integracaoMemberDAO.consultarIntegracoesPorEmpresa(null);
        Map<Integer, IntegracaoMemberVO> mapaIntegracoes = new HashMap<>();
        for (IntegracaoMemberVO integracao : integracaoMembers) {
            mapaIntegracoes.put(integracao.getCodigo(), integracao);
        }

        for (Map.Entry<Integer, IntegracaoMemberVO> entry : mapaIntegracoes.entrySet()) {
            Uteis.logarDebug("### Integrando members da integração " + entry.getValue().getDescricao() + "...");
            List<MemberVO> membersToSync = memberDAO.consultarMembersParaSincronizar(entry.getValue());
            Uteis.logarDebug("### São " + membersToSync.size() + " members para sincronizar.");
            int i = 1;
            for (MemberVO member : membersToSync) {
                Uteis.logarDebug("### Sincronizando " + (i++) + "/" + membersToSync.size());

                IntegracaoMemberVO integracaoMemberVO = mapaIntegracoes.get(member.getIntegracaoMemberVO().getCodigo());

                MembersApiViewTO memberNow = getMember(integracaoMemberVO, member.getIdMember());
                if (memberNow == null) {
                    memberDAO.excluir(member);
                    continue;
                }

                MemberMembershipApiViewTO membership = memberNow.getActivePlan(integracaoMemberVO.getDescricaoParcialPlano());
                memberNow.setMemberships(new ArrayList<>());
                if (membership != null) {
                    memberNow.getMemberships().add(membership);
                }

                try {
                    atualizarCliente(member, memberNow.toMember());
                } catch (Exception ex) {
                    Uteis.logarDebug(ex.getMessage());
                }
            }
        }
    }

    private void atualizarCliente(MemberVO member, MemberVO memberNow) throws Exception {
        boolean statusChanged = !member.getMembershipStatus().equals(memberNow.getMembershipStatus());
        boolean endVipDateChanged = !member.getEndDate().equals(memberNow.getEndDate());

        boolean changed = statusChanged || endVipDateChanged;

        member.updateFields(changed, memberNow);
        memberDAO.alterar(member);

        ClienteVO clienteMember = null;
        try {
            clienteMember = clienteDAO.consultarPorIdExternoCliente(Long.valueOf(member.getIdMember()), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        } catch (ConsistirException ignored) {

        }

        boolean atualizarSintetico = false;
        if (clienteMember == null) {
            clienteMember = member.toCliente();
            clienteDAO.incluirClienteSimplificadoImportacao(clienteMember);
            clienteMember.setCodAcesso(clienteDAO.gerarCodigoAcesso(clienteMember, 0));
            atualizarSintetico = true;
            changed = true;
        }

        LocalDateTime localEndDateTimeNow = null;
        if (!UteisValidacao.emptyString(memberNow.getEndDate())) {
            localEndDateTimeNow = LocalDateTime.parse(memberNow.getEndDate());
        }

        if (changed || (clienteMember.getFreePass() == null || clienteMember.getFreePass().getCodigo() == 0) &&  (localEndDateTimeNow != null && localEndDateTimeNow.isAfter(LocalDateTime.now()))) {
            if ("active".equals(member.getMembershipStatus())) {
                LocalDateTime localStartDateTime = LocalDateTime.parse(member.getStartDate());
                LocalDateTime localEndDateTime = LocalDateTime.parse(member.getEndDate());
                long diff = localEndDateTime.until(localStartDateTime, ChronoUnit.DAYS);

                Date startDate = Date.from(localStartDateTime.atZone(ZoneId.systemDefault()).toInstant());

                clienteMember.setFreePass(freePassIntegracao);
                freePassIntegracao.setNrDiasVigencia((int) diff);

                clienteDAO.incluirFreePassSemCommit(clienteMember, startDate, null, null, false, null);
            } else {
                clienteDAO.removerFreePass(clienteMember);
            }
            atualizarSintetico = true;
        }

        if (atualizarSintetico) {
            zillyonWebFacadeDAO.atualizarSintetico(clienteMember, Calendario.hoje(), SituacaoClienteSinteticoEnum.GRUPO_TODOS, false);
        }

        if (redeEmpresa != null &&
                redeEmpresa.getGestaoRedes() &&
                clienteMember.getSincronizadoRedeEmpresa() == null) {

            AutorizacaoAcessoGrupoEmpresarialVO autorizacao = new AutorizacaoAcessoGrupoEmpresarialVO(clienteMember);
            autorizacao = AcessoSistemaMSService.insertAccessAuthorization(autorizacao, chave, clienteMember.getEmpresa().getCodigo(), redeEmpresa);
            AcessoSistemaMSService.publish(autorizacao, false, redeEmpresa);
            if (!UteisValidacao.emptyNumber(autorizacao.getCodigo())) {
                clienteDAO.alterarSincronizadoRedeEmpresa(clienteMember.getCodigo());
            }
        }
    }

    public void consultarOuCriarProdutoFreePassIntegracao() throws Exception {
        freePassIntegracao = produtoDAO.criarOuConsultarProdutoPorTipoNrDiasVigencia(TipoProduto.FREEPASS.getCodigo(),
                365, "Freepass - Integração", Uteis.NIVELMONTARDADOS_DADOSBASICOS);
    }

    public ClienteVO processarCliente(MembersApiViewTO member, IntegracaoMemberVO integracaoMemberVO) throws Exception {
        try {
            con.setAutoCommit(false);

            if (UteisValidacao.emptyNumber(member.getIdMember())) {
                throw new Exception("Necessário que o cliente tenha um IdExterno");
            }

            ClienteVO clienteVO = clienteDAO.consultarPorCodigoMatriculaExterna(member.getIdMember(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            if (!UteisValidacao.emptyNumber(clienteVO.getCodigo())) {
                return clienteVO;
            }

            UteisImportacao uteisImportacao = new UteisImportacao();

            EstadoVO estadoVO = new EstadoVO();
            CidadeVO cidadeVO = new CidadeVO();
            PaisVO paisVO = new PaisVO();

            String ufCliente = member.getState();
            if (!UteisValidacao.emptyString(ufCliente)) {
                estadoVO = cache.obterEstadoVO(ufCliente);
                if (estadoVO != null && !UteisValidacao.emptyNumber(estadoVO.getCodigo())) {
                    if (!UteisValidacao.emptyNumber(estadoVO.getPais())) {
                        paisVO.setCodigo(estadoVO.getPais());
                    }
                    cidadeVO = uteisImportacao.obterCidade(member.getCity(), estadoVO, con);
                }
            }

            EmpresaVO empresaVO = cache.obterEmpresaVO(integracaoMemberVO.getEmpresa().getCodigo());

            if (cidadeVO != null && !UteisValidacao.emptyNumber(cidadeVO.getCodigo())) {
                estadoVO = cidadeVO.getEstado();
                paisVO = cidadeVO.getPais();
            } else {
                cidadeVO = empresaVO.getCidade();
                estadoVO = empresaVO.getEstado();
                paisVO = empresaVO.getPais();
            }

            PessoaVO pessoaVO = new PessoaVO();
            String nomeCompleto = member.getFirstName() + " " + member.getLastName();
            nomeCompleto = nomeCompleto.length() > 80 ? nomeCompleto.substring(0, 80) : nomeCompleto;
            pessoaVO.setNome(nomeCompleto);
            pessoaVO.setCfp(Uteis.formatarCpfCnpj(member.getDocument(), false));

            Date dataCadastro = UteisImportacao.getDateFromLocalDateTime(member.getRegisterDate());

            if (!UteisValidacao.emptyString(member.getBirthDate())) {
                Date dataNascimento = UteisImportacao.getDateFromLocalDateTime(member.getBirthDate());
                pessoaVO.setDataNasc(dataNascimento);
            }


            pessoaVO.setDataCadastro(dataCadastro);
            pessoaVO.setCategoriaPessoa(TipoPessoa.FISICA);
            pessoaVO.setTipoPessoa(TipoPessoa.FISICA.getLabel());

            pessoaVO.setIdExterno(Long.valueOf(member.getIdMember()));
            if (!UteisValidacao.emptyString(member.getGender())) {
                if (member.getGender().toLowerCase().equals("male")) {
                    pessoaVO.setSexo("M");
                } else if (member.getGender().toLowerCase().equals("female")) {
                    pessoaVO.setSexo("F");
                }
            }
            pessoaVO.setTipoPessoa(TipoPessoaEnum.ALUNO.getTipo());
            pessoaVO.setNacionalidade("");
            pessoaVO.setNaturalidade("");
            pessoaVO.setRg(member.getDocumentId());
            String document = Uteis.removerMascara(member.getDocument());
            if (document.trim().length() == 11) {
                pessoaVO.setCfp(Uteis.formatarCpfCnpj(document, false));
            }
            if (!UteisValidacao.emptyString(member.getPassport()) && member.getPassport().length() <= 32) {
                pessoaVO.setPassaporte(member.getPassport());
            }
            pessoaVO.setPais(paisVO);
            pessoaVO.setEstadoVO(estadoVO);
            pessoaVO.setCidade(cidadeVO);

            EnderecoVO endVO = new EnderecoVO();
            endVO.setCep(uteisImportacao.obterCEPMascarado(member.getZipCode()));
            endVO.setTipoEndereco(TipoEnderecoEnum.RESIDENCIAL.getCodigo());
            endVO.setBairro(member.getNeighborhood());
            endVO.setComplemento(member.getComplement());
            if (!UteisValidacao.emptyString(member.getNumber())) {
                if (member.getNumber().length() <= 10) {
                    endVO.setNumero(member.getNumber());
                } else {
                    endVO.setComplemento(UteisValidacao.emptyString(endVO.getComplemento()) ?
                            member.getNumber() : endVO.getComplemento() + " - " + member.getNumber());
                }
            }
            if (!UteisValidacao.emptyString(endVO.getComplemento())
                    && endVO.getComplemento().length() > 100) {
                endVO.setComplemento(endVO.getComplemento().substring(0, 100));
            }
            endVO.setEndereco(member.getAddress());
            endVO.setEnderecoCorrespondencia(false);
            pessoaVO.getEnderecoVOs().add(endVO);

            List<TelefoneVO> telefoneVOS = new ArrayList<>();
            List<EmailVO> emailVOS = new ArrayList<>();
            for (ContactsApiViewTO contactVO: member.getContacts()) {
                try {
                    ContactTypeEnum contactTypeEnum = ContactTypeEnum.obterPorId(contactVO.getIdContactType());
                    switch (contactTypeEnum) {
                        case TELEPHONE:
                            TelefoneVO telResidencial = uteisImportacao.obterTelefone(contactVO.getDescription(), TipoTelefoneEnum.RESIDENCIAL, integracaoMemberVO.getDddPadrao());
                            if (telResidencial != null) {
                                telefoneVOS.add(telResidencial);
                            }
                            break;
                        case CELLPHONE:
                            TelefoneVO telCelular = uteisImportacao.obterTelefone(contactVO.getDescription(), TipoTelefoneEnum.CELULAR, integracaoMemberVO.getDddPadrao());
                            if (telCelular != null) {
                                telefoneVOS.add(telCelular);
                            }
                            break;
                        case EMAIL:
                            EmailVO emailVO = new EmailVO();
                            emailVO.setEmail(contactVO.getDescription());
                            emailVO.setEmailCorrespondencia(false);
                            emailVOS.add(emailVO);
                            break;
                        default:
                            break;

                    }
                } catch (Exception ex) {
                    ex.printStackTrace();
                }
            }

            pessoaVO.setTelefoneVOs(telefoneVOS);
            pessoaVO.setEmailVOs(emailVOS);

            //incluir pessoa
            pessoaDAO.incluirPessoaImportacao(pessoaVO);
            if (UteisValidacao.emptyNumber(pessoaVO.getCodigo())) {
                throw new Exception("Pessoa não foi incluida!");
            }

            clienteVO.setIdExterno(Long.valueOf(member.getIdMember()));
            clienteVO.setMatriculaExterna(Long.valueOf(member.getIdMember()));
            clienteVO.setPessoa(pessoaVO);
            clienteVO.setEmpresa(empresaVO);

            List<VinculoVO> vinculoVOS = new ArrayList<>();
            VinculoVO vinculoConsultor = new VinculoVO();
            vinculoConsultor.getColaborador().setCodigo(integracaoMemberVO.getConsultorPadrao());
            vinculoConsultor.setTipoVinculo(TipoColaboradorEnum.CONSULTOR.getSigla());
            vinculoVOS.add(vinculoConsultor);

            VinculoVO vinculoProfessor = new VinculoVO();
            vinculoProfessor.getColaborador().setCodigo(integracaoMemberVO.getProfessorPadrao());
            vinculoProfessor.setTipoVinculo(TipoColaboradorEnum.PROFESSOR_TREINO.getSigla());
            vinculoVOS.add(vinculoProfessor);

            clienteVO.setVinculoVOs(vinculoVOS);
            uteisImportacao = null;

            //incluir cliente
            clienteDAO.gerarNumeroMatricula(clienteVO, clienteVO.getEmpresa(), null);
            clienteDAO.incluirClienteImportacao(clienteVO);


            if (clienteVO.getCodigoMatricula().intValue() != clienteVO.getMatriculaExterna().intValue()
                    && podeUtilizarMatriculaExterna(clienteVO.getMatriculaExterna().intValue(), clienteVO.getEmpresa().getCodigo())) {
                AplicarMatriculaImportacao.processar(con, clienteVO.getCodigo());
            }

            //incluir vinculos
            if (!UteisValidacao.emptyList(clienteVO.getVinculoVOs())) {
                vinculoDAO.incluirVinculo(clienteVO.getCodigo(), clienteVO.getVinculoVOs(), "CLIENTE", null, false);
            }

            Log logDao = new Log(con);
            logDao.incluirLogItemImportacao("CLIENTE", clienteVO.getCodigo(), cache.getUsuarioVOImportacao(), clienteVO.getPessoa().getCodigo());

            if (UteisValidacao.emptyNumber(clienteVO.getCodigo())) {
                throw new Exception("Cliente não importado.");
            }
            con.commit();
            return clienteVO;
        } catch (Exception ex) {
            ex.printStackTrace();
            con.rollback();
            throw ex;
        } finally {
            con.setAutoCommit(true);
        }
    }

    public ClienteVO processarProspect(ProspectsApiViewTO prospect, IntegracaoMemberVO integracaoMemberVO) throws Exception {
        try {
            con.setAutoCommit(false);

            if (UteisValidacao.emptyNumber(prospect.getIdProspect())) {
                throw new Exception("Necessário que o prospect tenha um IdExterno");
            }

            String idExterno = "P" + prospect.getIdProspect();
            ResultSet rs = SuperFacadeJDBC.criarConsulta("select pes.codigo from pessoa pes \n" +
                    " inner join cliente cli on cli.pessoa = pes.codigo \n" +
                    " where cli.empresa = " + integracaoMemberVO.getEmpresa().getCodigo() + "\n" +
                    " and pes.idexterno = '" + idExterno + "'", con);
            if (rs.next()) {
                return clienteDAO.consultarPorCodigoPessoa(rs.getInt("codigo"), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            }
            if (!UteisValidacao.emptyNumber(prospect.getIdMember())) {
                ClienteVO clienteVO = clienteDAO.consultarPorCodigoMatriculaExterna(prospect.getIdMember(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                if (!UteisValidacao.emptyNumber(clienteVO.getCodigo())) {
                    return clienteVO;
                }
            }

            UteisImportacao uteisImportacao = new UteisImportacao();

            EstadoVO estadoVO;
            CidadeVO cidadeVO = new CidadeVO();
            PaisVO paisVO = new PaisVO();

            String ufCliente = prospect.getState();
            if (!UteisValidacao.emptyString(ufCliente)) {
                estadoVO = cache.obterEstadoVO(ufCliente);
                if (estadoVO != null && !UteisValidacao.emptyNumber(estadoVO.getCodigo())) {
                    if (!UteisValidacao.emptyNumber(estadoVO.getPais())) {
                        paisVO.setCodigo(estadoVO.getPais());
                    }
                    cidadeVO = uteisImportacao.obterCidade(prospect.getCity(), estadoVO, con);
                }
            }

            EmpresaVO empresaVO = cache.obterEmpresaVO(integracaoMemberVO.getEmpresa().getCodigo());

            if (cidadeVO != null && !UteisValidacao.emptyNumber(cidadeVO.getCodigo())) {
                estadoVO = cidadeVO.getEstado();
                paisVO = cidadeVO.getPais();
            } else {
                cidadeVO = empresaVO.getCidade();
                estadoVO = empresaVO.getEstado();
                paisVO = empresaVO.getPais();
            }

            PessoaVO pessoaVO = new PessoaVO();
            pessoaVO.setIdExterno(Long.valueOf(prospect.getIdProspect()));
            String nomeCompleto = prospect.getFirstName() + " " + prospect.getLastName();
            nomeCompleto = nomeCompleto.length() > 80 ? nomeCompleto.substring(0, 80) : nomeCompleto;
            pessoaVO.setNome(nomeCompleto);
            pessoaVO.setCfp(Uteis.formatarCpfCnpj(prospect.getDocument(), false));

            Date dataCadastro = UteisImportacao.getDateFromLocalDateTime(prospect.getRegisterDate());

            if (!UteisValidacao.emptyString(prospect.getBirthDate())) {
                Date dataNascimento = UteisImportacao.getDateFromLocalDateTime(prospect.getBirthDate());
                pessoaVO.setDataNasc(dataNascimento);
            }

            pessoaVO.setDataCadastro(dataCadastro);
            pessoaVO.setCategoriaPessoa(TipoPessoa.FISICA);
            pessoaVO.setTipoPessoa(TipoPessoa.FISICA.getLabel());

            if (!UteisValidacao.emptyString(prospect.getGender())) {
                if (prospect.getGender().toLowerCase().equals("male")) {
                    pessoaVO.setSexo("M");
                } else if (prospect.getGender().toLowerCase().equals("female")) {
                    pessoaVO.setSexo("F");
                }
            }
            pessoaVO.setTipoPessoa(TipoPessoaEnum.ALUNO.getTipo());
            pessoaVO.setNacionalidade("");
            pessoaVO.setNaturalidade("");
            pessoaVO.setCfp(prospect.getDocument());
            pessoaVO.setPais(paisVO);
            pessoaVO.setEstadoVO(estadoVO);
            pessoaVO.setCidade(cidadeVO);

            EnderecoVO endVO = new EnderecoVO();
            endVO.setCep(uteisImportacao.obterCEPMascarado(prospect.getZipCode()));
            endVO.setTipoEndereco(TipoEnderecoEnum.RESIDENCIAL.getCodigo());
            endVO.setBairro(prospect.getNeighborhood());
            endVO.setComplemento(prospect.getComplement());
            if (!UteisValidacao.emptyString(prospect.getNumber())) {
                if (prospect.getNumber().length() <= 10) {
                    endVO.setNumero(prospect.getNumber());
                } else {
                    endVO.setComplemento(UteisValidacao.emptyString(endVO.getComplemento()) ?
                            prospect.getNumber() : endVO.getComplemento() + " - " + prospect.getNumber());
                }
            }
            if (!UteisValidacao.emptyString(endVO.getComplemento())
                    && endVO.getComplemento().length() > 100) {
                endVO.setComplemento(endVO.getComplemento().substring(0, 100));
            }
            endVO.setEndereco(prospect.getAddress());
            endVO.setEnderecoCorrespondencia(false);
            pessoaVO.getEnderecoVOs().add(endVO);

            List<TelefoneVO> telefoneVOS = new ArrayList<>();
            TelefoneVO telCelular = uteisImportacao.obterTelefone(prospect.getCellphone(), TipoTelefoneEnum.CELULAR, integracaoMemberVO.getDddPadrao());
            if (telCelular != null) {
                telefoneVOS.add(telCelular);
            }

            List<EmailVO> emailVOS = new ArrayList<>();
            if (!UteisValidacao.emptyString(prospect.getEmail())) {
                EmailVO emailVO = new EmailVO();
                emailVO.setEmail(prospect.getEmail());
                emailVO.setEmailCorrespondencia(false);
                emailVOS.add(emailVO);
            }

            pessoaVO.setTelefoneVOs(telefoneVOS);
            pessoaVO.setEmailVOs(emailVOS);

            //incluir pessoa
            pessoaDAO.incluirPessoaImportacao(pessoaVO);
            if (UteisValidacao.emptyNumber(pessoaVO.getCodigo())) {
                throw new Exception("Pessoa não foi incluida!");
            }

            PreparedStatement pstm = con.prepareStatement("update pessoa set idexterno = ? where codigo = ?");
            pstm.setString(1, idExterno);
            pstm.setInt(2, pessoaVO.getCodigo());
            pstm.execute();

            ClienteVO clienteVO = new ClienteVO();

            clienteVO.setIdExterno(0l);
            clienteVO.setMatriculaExterna(0l);
            clienteVO.setPessoa(pessoaVO);
            clienteVO.setEmpresa(empresaVO);

            List<VinculoVO> vinculoVOS = new ArrayList<>();
            VinculoVO vinculoConsultor = new VinculoVO();
            vinculoConsultor.getColaborador().setCodigo(integracaoMemberVO.getConsultorPadrao());
            vinculoConsultor.setTipoVinculo(TipoColaboradorEnum.CONSULTOR.getSigla());
            vinculoVOS.add(vinculoConsultor);

            VinculoVO vinculoProfessor = new VinculoVO();
            vinculoProfessor.getColaborador().setCodigo(integracaoMemberVO.getProfessorPadrao());
            vinculoProfessor.setTipoVinculo(TipoColaboradorEnum.PROFESSOR_TREINO.getSigla());
            vinculoVOS.add(vinculoProfessor);

            clienteVO.setVinculoVOs(vinculoVOS);
            uteisImportacao = null;

            //incluir cliente
            clienteDAO.gerarNumeroMatricula(clienteVO, clienteVO.getEmpresa(), null);
            clienteDAO.incluirClienteImportacao(clienteVO);

            //incluir vinculos
            if (!UteisValidacao.emptyList(clienteVO.getVinculoVOs())) {
                vinculoDAO.incluirVinculo(clienteVO.getCodigo(), clienteVO.getVinculoVOs(), "CLIENTE", null, false);
            }

            Log logDao = new Log(con);
            logDao.incluirLogItemImportacao("CLIENTE", clienteVO.getCodigo(), cache.getUsuarioVOImportacao(), clienteVO.getPessoa().getCodigo());

            if (UteisValidacao.emptyNumber(clienteVO.getCodigo())) {
                throw new Exception("Cliente não importado.");
            }
            con.commit();
            return clienteVO;
        } catch (Exception ex) {
            ex.printStackTrace();
            con.rollback();
            throw ex;
        } finally {
            con.setAutoCommit(true);
        }
    }

    private boolean podeUtilizarMatriculaExterna(Integer matriculaExterna, Integer codigoEmpresa) throws Exception {
        ResultSet rs = SuperFacadeJDBC.criarConsulta("select pes.idexterno, cli.codigo, cli.situacao, cli.matriculaexterna, cli.empresa from cliente cli \n" +
                " inner join pessoa pes on pes.codigo = cli.pessoa \n" +
                "where cli.codigomatricula = " + matriculaExterna, con);
        if (rs.next()) {
            if (rs.getInt("empresa") != codigoEmpresa) {
                return false;
            }
            ClienteVO clienteVO = clienteDAO.consultarPorCodigo(rs.getInt("codigo"), false, Uteis.NIVELMONTARDADOS_SITUACAOCLIENTESINTETICODW);
            Integer novaMatricula = clienteDAO.obterMatriculaAluno() + 1;
            String matriculaMascara = Uteis.getMontarMatricula(novaMatricula.toString(), cache.obterEmpresaVO(codigoEmpresa).getMascaraMatricula().length());
            clienteVO.setMatricula(matriculaMascara);
            clienteVO.setCodigoMatricula(novaMatricula);
            SuperFacadeJDBC.executarConsulta("update cliente set codigomatricula = " + novaMatricula + ", \n" +
                    "matricula = '" + matriculaMascara + "' where codigo = " + rs.getInt("codigo"), con);
            SuperFacadeJDBC.executarConsulta("update numeromatricula set matricula = " + novaMatricula, con);
            zillyonWebFacadeDAO.atualizarSintetico(clienteVO, Calendario.hoje(), SituacaoClienteSinteticoEnum.GRUPO_CLIENTE, false);
        }
        return true;
    }

    public void processarVendaAulaAvulsaDiaria(ClienteVO clienteVO, SalesApiViewTO sale, Map<Integer, List<MemberMembershipApiViewTO>> mapMemberShip, Integer codModalidade) throws Exception {
        try {
            con.setAutoCommit(false);

            List<MemberMembershipApiViewTO> listMemberShips = mapMemberShip.get(sale.getIdSale());

            if (UteisValidacao.emptyList(listMemberShips)) {
                return;
            }

            Integer idMemberShipReceivables = obterIdMemberShipReceivables(listMemberShips, sale.getSaleItens());

            List<MemberMembershipApiViewTO> listMemberShipsDiaria = mapMemberShip.get(sale.getIdSale());
            listMemberShipsDiaria = listMemberShipsDiaria.stream()
                    .filter(m -> Uteis.nrDiasEntreDatas(m.getDataInicio(), m.getDataFim()) <= 25)
                    .collect(Collectors.toList());

            if (UteisValidacao.emptyList(listMemberShipsDiaria)) {
                return;
            }


            // Descobrir o sale item de cada member ship diaria
            Map<Integer, SaleItemApiViewTO> mapaMemberShipSaleItens = obterMapaMemberShipSaleItens(listMemberShipsDiaria, sale.getSaleItens());

            ResultSet rsJaImportada = SuperFacadeJDBC.criarConsulta("select codigo from aulaavulsadiaria where id_venda = '" + sale.getIdSale() + "'", con);
            if (rsJaImportada.next()) {
                return;
            }

            boolean isImportReceivables;
            List<AulaAvulsaDiariaVO> aulaAvulsaDiariaVOS = new ArrayList<>();
            List<MovProdutoVO> movProdutoVOS = new ArrayList<>();
            List<MovParcelaVO> movParcelaVOS = new ArrayList<>();

            for (MemberMembershipApiViewTO memberShip : listMemberShipsDiaria) {
                isImportReceivables = memberShip.getIdMemberMembership().equals(idMemberShipReceivables);
                SaleItemApiViewTO saleItem = mapaMemberShipSaleItens.get(memberShip.getIdMemberMembership());
                if (saleItem == null) {
                    continue;
                }

                if (!isImportReceivables && (memberShip.getName().toLowerCase().contains("dobro") || memberShip.getName().toLowerCase().contains("pedido de transfer"))) {
                    continue;
                }

                AulaAvulsaDiariaVO aulaAvulsaDiariaVO = new AulaAvulsaDiariaVO();
                aulaAvulsaDiariaVO.setCliente(clienteVO);
                aulaAvulsaDiariaVO.setModalidade(cache.obterModalidadeVO(codModalidade));

                Produto produtoDAO = new Produto(con);
                ProdutoVO produtoDiaria = produtoDAO.criarOuConsultarProdutoPorTipo(TipoProduto.DIARIA.getCodigo(), "Diaria Importação", Uteis.NIVELMONTARDADOS_DADOSBASICOS);

                Date startDate = UteisImportacao.getDateFromLocalDateTime(memberShip.getStartDate());
                Date endDate = UteisImportacao.getDateFromLocalDateTime(memberShip.getEndDate());

                Long dias = Uteis.nrDiasEntreDatas(startDate, endDate);
                produtoDiaria.setNrDiasVigencia(dias.intValue());

                aulaAvulsaDiariaVO.setProduto(produtoDiaria);
                aulaAvulsaDiariaVO.setDataInicio(startDate);
                aulaAvulsaDiariaVO.setEmpresa(clienteVO.getEmpresa());
                if (isImportReceivables) {
                    produtoDiaria.setValorFinal(saleItem.getItemValue());
                    aulaAvulsaDiariaVO.setValor(saleItem.getItemValue());
                } else {
                    aulaAvulsaDiariaVO.setValor(0.0);
                    produtoDiaria.setValorFinal(0.0);
                }
                aulaAvulsaDiariaVO.setResponsavel(cache.getUsuarioVOImportacao());
                aulaAvulsaDiariaVO.setNomeComprador(clienteVO.getPessoa().getNome());
                aulaAvulsaDiariaVO.setDataRegistro(UteisImportacao.getDateFromLocalDateTime(sale.getSaleDate()));
                aulaAvulsaDiariaVO.setDataLancamento(aulaAvulsaDiariaVO.getDataRegistro());
                aulaAvulsaDiariaVO.setUsuarioVO(cache.getUsuarioVOImportacao());
                aulaAvulsaDiariaDao.incluirSemCommit(aulaAvulsaDiariaVO, OrigemSistemaEnum.ZW, false);

                preencherIdVendaAulaAvulsaDiaria(sale.getIdSale(), saleItem.getIdSaleItem(), aulaAvulsaDiariaVO.getCodigo(), con);
                aulaAvulsaDiariaVOS.add(aulaAvulsaDiariaVO);

                MovProdutoVO movProdutoVO = gerarMovProdutoVendaAulaAvulsaDiaria(aulaAvulsaDiariaVO);
                movProdutoVOS.add(movProdutoVO);
                if (isImportReceivables) {
                    movParcelaVOS = gerarParcelasVenda(sale.getReceivables(), clienteVO, aulaAvulsaDiariaVOS.get(0), null, aulaAvulsaDiariaVOS.get(0).getDataRegistro());
                }

                if (UteisValidacao.emptyList(movParcelaVOS)) {
                    MovParcelaVO movParcelaVO = new MovParcelaVO();
                    movParcelaVO.setAulaAvulsaDiariaVO(aulaAvulsaDiariaVO);
                    movParcelaVO.setDescricao("Diaria");
                    movParcelaVO.setDataRegistro(aulaAvulsaDiariaVO.getDataRegistro());
                    movParcelaVO.setDataVencimento(aulaAvulsaDiariaVO.getDataRegistro());
                    movParcelaVO.setResponsavel(cache.getUsuarioVOImportacao());
                    movParcelaVO.setSituacao("PG");
                    movParcelaVO.setValorParcela(0.0);
                    movParcelaVO.setPercentualJuro(0.0);
                    movParcelaVO.setPercentualMulta(0.0);
                    movParcelaVO.setPessoa(clienteVO.getPessoa());
                    movParcelaVO.setEmpresa(clienteVO.getEmpresa());
                    movParcelaDAO.incluirParcelaSemCommit(movParcelaVO);
                    movParcelaVOS.add(movParcelaVO);
                }
            }

            Map<Integer, ReceivablesViewTO> mapReceivables = new HashMap<>();
            sale.getReceivables().forEach(r -> mapReceivables.put(r.getIdReceivable(), r));

            gerarMovprodutoParcela(movParcelaVOS, movProdutoVOS);
            gerarPagamentoParcelas(movParcelaVOS, mapReceivables);
            con.commit();
        } catch (Exception ex) {
            ex.printStackTrace();
            con.rollback();
            throw ex;
        } finally {
            con.setAutoCommit(true);
        }
    }

    private static void preencherIdVendaAulaAvulsaDiaria(int idVenda, int idExternoItemVenda, Integer codigo, Connection con) {
        try {
            String sql = "UPDATE aulaavulsadiaria SET id_venda = ?, idExternoItemVenda = ? WHERE codigo = ?";
            PreparedStatement pstm = con.prepareStatement(sql);
            pstm.setInt(1, idVenda);
            pstm.setInt(2, idExternoItemVenda);
            pstm.setInt(3, codigo);
            pstm.execute();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private List<MovParcelaVO> gerarParcelasVenda(ReceivablesViewTO receivable, ClienteVO clienteVO, VendaAvulsaVO vendaAvulsaVO, Date dataLancamento) throws Exception {
        List<ReceivablesViewTO> receivables = new ArrayList<>();
        receivables.add(receivable);
        return gerarParcelasVenda(receivables, clienteVO, null, vendaAvulsaVO, dataLancamento);
    }

    private List<MovParcelaVO> gerarParcelasVenda(List<ReceivablesViewTO> receivables, ClienteVO clienteVO, AulaAvulsaDiariaVO aulaAvulsaDiariaVO, VendaAvulsaVO vendaAvulsaVO, Date dataLancamento) throws Exception {
        List<MovParcelaVO> movParcelaVOS = new ArrayList<>();

        int numeroParcela = 0;
        for (ReceivablesViewTO receivable: receivables) {
            Double valorParcela = receivable.getAmmount();
            Date dataVencimento;
            if ((!UteisValidacao.emptyString(receivable.getDescription()) && receivable.getDescription().toUpperCase().contains("SALDO DEVEDOR"))
                    || receivable.getCompetenceDate() == null
                    || (vendaAvulsaVO != null && !UteisValidacao.emptyNumber(vendaAvulsaVO.getCodigo()))
            ) {
                dataVencimento = UteisImportacao.getDateFromLocalDateTime(receivable.getDueDate());
            } else {
                dataVencimento = UteisImportacao.getDateFromLocalDateTime(receivable.getCompetenceDate());
            }

            StatusRecevablesEnum statusRecevablesEnum = StatusRecevablesEnum.obterPorId(receivable.getStatus().getId());
            if (statusRecevablesEnum == null) {
                throw new Exception("Falha ao obter status do recebivel id: " + receivable.getIdReceivable() + " idvenda: " + receivable.getIdSale() + " - " + receivable.getDescription());
            }
            if (statusRecevablesEnum.getId().equals(StatusRecevablesEnum.CANCELED.getId())) {
                continue;
            }

            MovParcelaVO movParcelaVO = new MovParcelaVO();
            if (aulaAvulsaDiariaVO != null && !UteisValidacao.emptyNumber(aulaAvulsaDiariaVO.getCodigo())) {
                movParcelaVO.setAulaAvulsaDiariaVO(aulaAvulsaDiariaVO);
                if (aulaAvulsaDiariaVO.getProduto().getTipoProduto().equals("DI")) {
                    movParcelaVO.setDescricao("Diaria" + (receivables.size() > 1 ? " - Parcela " + (++numeroParcela) : ""));
                } else {
                    movParcelaVO.setDescricao("Aula Avulso" + (receivables.size() > 1 ? " - Parcela " + (++numeroParcela) : ""));
                }
            } else {
                movParcelaVO.setVendaAvulsaVO(vendaAvulsaVO);
                if (receivables.size() == 1) {
                    movParcelaVO.setDescricao(vendaAvulsaVO.getDescricaoAdicional().isEmpty() ? "Venda Avulso" : vendaAvulsaVO.getDescricaoAdicional());
                } else {
                    movParcelaVO.setDescricao(vendaAvulsaVO.getDescricaoAdicional().isEmpty() ? "Venda Avulso - Parcela " + (++numeroParcela) : vendaAvulsaVO.getDescricaoAdicional());
                }
                vendaAvulsaVO.getMovParcelaVOs().add(movParcelaVO);
            }
            movParcelaVO.setDataRegistro(dataLancamento);
            movParcelaVO.setDataVencimento(dataVencimento);
            movParcelaVO.setResponsavel(cache.getUsuarioVOImportacao());
            movParcelaVO.setSituacao("EA");
            if (statusRecevablesEnum.getId().equals(StatusRecevablesEnum.CANCELED)) {
                movParcelaVO.setSituacao("CA");
            } else if (statusRecevablesEnum.getId().equals(StatusRecevablesEnum.RECEIVED.getId())
                    || !UteisValidacao.emptyString(receivable.getAuthorization())) {
                movParcelaVO.setSituacao("PG");
            }
            movParcelaVO.setValorParcela(valorParcela);
            movParcelaVO.setPercentualJuro(0.0);
            movParcelaVO.setPercentualMulta(0.0);
            movParcelaVO.setPessoa(clienteVO.getPessoa());
            movParcelaVO.setEmpresa(clienteVO.getEmpresa());
            movParcelaVO.setIdExterno(receivable.getIdReceivable());

            movParcelaDAO.incluirParcelaSemCommit(movParcelaVO);
            movParcelaVOS.add(movParcelaVO);
        }

        return movParcelaVOS;
    }

    private void gerarMovProdutoVendaAvulsa(VendaAvulsaVO vendaAvulsaVO, UsuarioVO usuario, String situacao, Date dataReferencia) throws Exception {
        for (ItemVendaAvulsaVO itemVenda: vendaAvulsaVO.getItemVendaAvulsaVOs()) {
            MovProdutoVO movProdutoVO = new MovProdutoVO();
            movProdutoVO.setProduto(itemVenda.getProduto());
            movProdutoVO.setRenovavelAutomaticamente(itemVenda.getProduto().getRenovavelAutomaticamente());
            movProdutoVO.setApresentarMovProduto(false);
            movProdutoVO.setDescricao(!vendaAvulsaVO.getDescricaoAdicional().isEmpty() ? vendaAvulsaVO.getDescricaoAdicional() : itemVenda.getProduto().getDescricao());
            movProdutoVO.setQuantidade(itemVenda.getQuantidade());
            movProdutoVO.setMesReferencia(Uteis.getMesReferenciaData(dataReferencia));
            movProdutoVO.setAnoReferencia(Uteis.getAnoData(dataReferencia));
            movProdutoVO.setDataInicioVigencia(itemVenda.getDataVenda());
            movProdutoVO.setDataFinalVigencia(itemVenda.getDataValidade());
            movProdutoVO.setVigenciaJaCalculada(itemVenda.getVigenciaJaCalculada());
            movProdutoVO.setDataLancamento(vendaAvulsaVO.getDataRegistro());
            movProdutoVO.setResponsavelLancamento(usuario);
            if (situacao.equals("EA")) {
                movProdutoVO.setMovpagamentocc(itemVenda.getMovpagamentos());
            }
            if (itemVenda.getTabelaDesconto().getValor() != 0.0 && itemVenda.getTabelaDesconto().getTipoDesconto().equals(TipoDesconto.PE)) {
                movProdutoVO.setValorDesconto(Uteis.arredondarForcando2CasasDecimais(itemVenda.getQuantidade() * (itemVenda.getProduto().getValorFinal() * (itemVenda.getTabelaDesconto().getValor() / 100))));
            } else if (itemVenda.getTabelaDesconto().getValor() != 0.0 && itemVenda.getTabelaDesconto().getTipoDesconto().equals(TipoDesconto.VA)) {
                movProdutoVO.setValorDesconto(itemVenda.getTabelaDesconto().getValor());
            } else if (itemVenda.getDescontoManual()) {
                movProdutoVO.setValorDesconto(itemVenda.getValorDescontoManual());
            } else {
                movProdutoVO.setValorDesconto(0.0);
            }
            movProdutoVO.setPrecoUnitario(itemVenda.getProduto().getValorFinal());
            movProdutoVO.setTotalFinal(Uteis.arredondarForcando2CasasDecimais((movProdutoVO.getPrecoUnitario() * movProdutoVO.getQuantidade()) - movProdutoVO.getValorDesconto()));
            movProdutoVO.setValorFaturado(movProdutoVO.getTotalFinal());
            movProdutoVO.setVendaAvulsa(vendaAvulsaVO.getCodigo());
            movProdutoVO.setEmpresa(vendaAvulsaVO.getEmpresa());
            if (vendaAvulsaVO.getTipoComprador().equals("CI")) {
                movProdutoVO.setPessoa(vendaAvulsaVO.getCliente().getPessoa());
            }
            if (vendaAvulsaVO.getTipoComprador().equals("CO")) {
                movProdutoVO.setPessoa(vendaAvulsaVO.getColaborador().getPessoa());
            }
            if (situacao.equals("PG")) {
                movProdutoVO.setSituacao("PG");
            } else {
                movProdutoVO.setSituacao("EA");
            }
            movProdutoVO.setQuitado(true); // apenas para inclusão
            movProdutoDAO.incluirSemCommit(movProdutoVO);
            movProdutoVO.setQuitado(false); // para gerar o relacionamento entre produtos e parcelas corretamente
            vendaAvulsaVO.getMovProdutoVOs().add(movProdutoVO);
        }
    }

    private MovProdutoVO gerarMovProdutoVendaAulaAvulsaDiaria(final AulaAvulsaDiariaVO obj) throws Exception {
        MovProdutoVO movProdutoVO = new MovProdutoVO();
        movProdutoVO.setProduto(obj.getProduto());
        movProdutoVO.setApresentarMovProduto(false);
        movProdutoVO.setDescricao(obj.getProduto().getDescricao());
        movProdutoVO.setMesReferencia(Uteis.getMesReferenciaData(Calendario.hoje()));
        movProdutoVO.setRenovavelAutomaticamente(obj.getProduto().getRenovavelAutomaticamente());
        movProdutoVO.setQuantidade(1);
        movProdutoVO.setAnoReferencia(Uteis.getAnoData(Calendario.hoje()));
        if (obj.getProduto().getDataInicioVigencia() == null){
            movProdutoVO.setDataInicioVigencia(obj.getDataInicio());
        }else{
            movProdutoVO.setDataInicioVigencia(obj.getProduto().getDataInicioVigencia());
        }
        movProdutoVO.setDataFinalVigencia(obj.getProduto().getDataFinalVigencia());
        movProdutoVO.setDataLancamento(obj.getDataLancamento());
        movProdutoVO.setResponsavelLancamento(obj.getResponsavel());
        movProdutoVO.setPrecoUnitario(obj.getValor());
        movProdutoVO.setEmpresa(obj.getEmpresa());
        movProdutoVO.setPessoa(obj.getCliente().getPessoa());
        movProdutoVO.setQuitado(false);
        movProdutoVO.setSituacao("EA");
        if (movProdutoVO.getPrecoUnitario().equals(0.0)) {
            movProdutoVO.setSituacao("PG");
        }
        movProdutoVO.setValorDesconto(0.0);
        movProdutoVO.setTotalFinal(Uteis.arredondarForcando2CasasDecimais((movProdutoVO.getPrecoUnitario() - movProdutoVO.getValorDesconto())));
        movProdutoVO.setValorFaturado(movProdutoVO.getTotalFinal());
        movProdutoVO.setValorPagoMovProdutoParcela(movProdutoVO.getTotalFinal());
        movProdutoDAO.incluirSemCommit(movProdutoVO);

        return movProdutoVO;
    }

    public void processarContratos(ClienteVO clienteVO, SalesApiViewTO sale,  Map<Integer, List<MemberMembershipApiViewTO>> mapMemberShipSale, Map<Integer, MemberMembershipApiViewTO> mapMemberShip, IntegracaoMemberVO integracaoMemberVO) throws Exception {
        List<MemberMembershipApiViewTO> listMemberShips = mapMemberShipSale.get(sale.getIdSale());
        Integer idMemberShipReceivables;
        if (!UteisValidacao.emptyList(listMemberShips)) {
            idMemberShipReceivables = obterIdMemberShipReceivables(listMemberShips, sale.getSaleItens());
            for (MemberMembershipApiViewTO memberShip : listMemberShips) {
                boolean isImportarReceivables = memberShip.getIdMemberMembership().equals(idMemberShipReceivables);
                memberShip.setDataFim(UteisImportacao.getDateFromLocalDateTime(memberShip.getEndDate()));

                Long dias = Uteis.nrDiasEntreDatas(memberShip.getDataInicio(), memberShip.getDataFim());
                if (dias > 25) {
                    ContratoVO contratoVO = importarContrato(clienteVO, memberShip, sale, isImportarReceivables, integracaoMemberVO);
                    if (contratoVO != null && !UteisValidacao.emptyNumber(contratoVO.getCodigo())) {
                        processarCessaoDireitoDeUso(clienteVO, contratoVO, memberShip);
                        processarCompartilhamentoComDependentes(contratoVO, memberShip.getIdMemberMembership(), mapMemberShip);
                    }
                }
            }
        }
    }

    private void processarCessaoDireitoDeUso(ClienteVO clienteTitular, ContratoVO contratoVO, MemberMembershipApiViewTO memberShip) {
        String status = memberShip.getMembershipStatus().toLowerCase();
        MembershipTrasnferDataApiViewTO memberShipTransfer = memberShip.getMemberShipCancellation() != null ? memberShip.getMemberShipCancellation().getMembershipTrasnferData() : null;
        if (status.equals(MemberShipStatusEnum.TRANSFER.getName().toLowerCase())
                && !(memberShip.getName().contains("dobro") && contratoVO.getValorFinal().equals(0.0))
                && memberShipTransfer != null
                && !UteisValidacao.emptyNumber(memberShipTransfer.getIdMemberTransfer())) {
            try {
                UsuarioVO usuarioAdmin = cache.obterUsuarioVO(1);

                ContratoVO contratoVOParaCeder = contratoDAO.consultarPorChavePrimaria(contratoVO.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);

                if (!UteisValidacao.emptyNumber(contratoVOParaCeder.getPessoaOriginal().getCodigo())) {
                    return;
                }

                ClienteVO clienteDependente = clienteDAO.consultarPorCodigoMatriculaExterna(memberShipTransfer.getIdMemberTransfer(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);

                if (UteisValidacao.emptyNumber(clienteDependente.getCodigo())) {
                    String msg = "Não vincular cessão do contrato %s. Dependente não encontrado para o id: %s ";
                    String msgFormatada = String.format(msg, contratoVO.getCodigo(), memberShipTransfer.getIdMemberTransfer());
                    falhasItemAtual.add(msgFormatada);
                    falhas.add(msgFormatada);
                    return;
                }

                if (!UteisValidacao.emptyNumber(clienteDependente.getTitularPlanoCompartilhado())) {
                    String msg = "Não vincular cessão do contrato %s. Dependente plano em dobro: %s %s";
                    String msgFormatada = String.format(msg, contratoVO.getCodigo(), clienteDependente.getMatricula(), clienteDependente.getNome_Apresentar());
                    falhasItemAtual.add(msgFormatada);
                    falhas.add(msgFormatada);
                    return;
                }

                ResultSet rs = SuperFacadeJDBC.criarConsulta("SELECT codigo FROM contrato WHERE coalesce(id_externo,idExterno) = " + memberShipTransfer.getIdMemberMembershipTransfer()
                        + " and empresa = " + clienteDependente.getEmpresa().getCodigo(), con);
                if (rs.next()) {
                    ContratoVO contratoEstornar = contratoDAO.consultarPorCodigo(rs.getInt("codigo"), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                    if (contratoEstornar != null && !UteisValidacao.emptyNumber(contratoEstornar.getCodigo())) {
                        estornarContrato(contratoEstornar);
                    }
                }


                zillyonWebFacadeDAO.transferirDireitoDeUso(contratoVOParaCeder, clienteDependente, usuarioAdmin, false);

                String msgFormatada = String.format("Cessão do contrato %s realizada com sucesso. Titular %s %s - Dependente %s %s",
                        contratoVO.getCodigo(),
                        clienteTitular.getMatricula(), clienteTitular.getNome_Apresentar(),
                        clienteDependente.getMatricula(), clienteDependente.getNome_Apresentar());
                Uteis.logar(true, null, msgFormatada);
            } catch (Exception ex) {
                String msg = String.format("Falha ao processar cessao direito de uso do contrato: %d cliente: %s %s", contratoVO.getCodigo(), clienteTitular.getMatricula(), clienteTitular.getPessoa().getNome());
                falhasItemAtual.add(msg);
                falhas.add(msg);
            }
        }
    }

    private void processarCompartilhamentoComDependentes(ContratoVO contratoTitular, Integer idMemberShipTitular, Map<Integer, MemberMembershipApiViewTO> mapMemberShip) throws Exception {
        if (!contratoTitular.getSituacao().equals("AT") || contratoTitular.getPlano().getQuantidadeCompartilhamentos().equals(0)) {
            return;
        }

        Integer codigoPessoaTitular = contratoTitular.getPessoaOriginal() != null && !UteisValidacao.emptyNumber(contratoTitular.getPessoaOriginal().getCodigo())
                ? contratoTitular.getPessoaOriginal().getCodigo() : contratoTitular.getPessoa().getCodigo();
        ClienteVO clienteTitular = clienteDAO.consultarPorCodigoPessoa(codigoPessoaTitular, Uteis.NIVELMONTARDADOS_DADOSBASICOS);

        for (Integer key: mapMemberShip.keySet()) {
            MemberMembershipApiViewTO memberShip = mapMemberShip.get(key);
            String status = memberShip.getMembershipStatus().toLowerCase();

            if ((memberShip.getName().toLowerCase().contains("dobro") || memberShip.getName().toLowerCase().contains("dependente"))
                    && status.equals(MemberShipStatusEnum.TRANSFER.getName().toLowerCase())
                    && memberShip.getMemberShipCancellation() != null
                    && memberShip.getMemberShipCancellation().getMembershipTrasnferData() != null
                    && !UteisValidacao.emptyNumber(memberShip.getMemberShipCancellation().getMembershipTrasnferData().getIdMemberTransfer())
                    && Calendario.maiorOuIgual(memberShip.getDataFim(), Calendario.hoje())
                    && !key.equals(idMemberShipTitular)
            ) {
                incluirCompatilharmentoDependente(clienteTitular, contratoTitular, memberShip.getMemberShipCancellation().getMembershipTrasnferData());
            }
        }
    }

    private void incluirCompatilharmentoDependente(ClienteVO clienteTitular, ContratoVO contratoVO, MembershipTrasnferDataApiViewTO memberShipTransfer) throws Exception {
        try {
            con.setAutoCommit(false);
            ClienteVO clienteDependente = clienteDAO.consultarPorCodigoMatriculaExterna(memberShipTransfer.getIdMemberTransfer(), clienteTitular.getEmpresa().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);

            ContratoVO contratoVODependente = null;
            ResultSet rsContrato = SuperFacadeJDBC.criarConsulta("SELECT codigo FROM contrato WHERE coalesce(id_externo,idExterno) = " + memberShipTransfer.getIdMemberMembershipTransfer(), con);
            if (rsContrato.next()) {
                contratoVODependente = contratoDAO.consultarPorCodigo(rsContrato.getInt("codigo"), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            }

            if (contratoVODependente != null && contratoVODependente.getValorFinal() > 0.0) {
                String msg = String.format("Não alterar dependência do cliente (Valor do contrato > R$ 0): %s - Titular: %s",
                        clienteDependente.getNome_Apresentar(), clienteTitular.getNome_Apresentar());
                falhasItemAtual.add(msg);
                falhas.add(msg);
                return;
            }

            if (contratoVODependente!= null && !UteisValidacao.emptyNumber(contratoVODependente.getCodigo())) {
                estornarContrato(contratoVODependente);
                clienteDependente = clienteDAO.consultarPorCodigo(clienteDependente.getCodigo(), false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            }

            if (!UteisValidacao.emptyNumber(clienteDependente.getTitularPlanoCompartilhado())) {
                if (!clienteDependente.getTitularPlanoCompartilhado().equals(clienteTitular.getCodigo())) {
                    String msg = String.format( "Depente: %s %s já possui compartilhamento, não será possivel compartilhar com titular: %s %s",
                            clienteDependente.getMatricula(), clienteDependente.getNome_Apresentar(), clienteTitular.getMatricula(), clienteTitular.getNome_Apresentar());
                    falhasItemAtual.add(msg);
                    falhas.add(msg);
                }
                return;
            }

            if (UteisValidacao.emptyNumber(clienteDependente.getCodigo())) {
                String msg = String.format("Falha ao compartilhar contrato do titular: %d %s com o dependente: %d | O cadastro do cliente dependente não encontrato!",
                        clienteTitular.getMatriculaExterna(), clienteTitular.getPessoa().getNome(), memberShipTransfer.getIdMemberTransfer());
                falhasItemAtual.add(msg);
                falhas.add(msg);
                return;
            }

            Optional<ContratoDependenteVO> contratoDependenteVO = contratoDependenteDAO.consultarProximoContratoDependenteDisponivelPorContrato(contratoVO.getCodigo());
            if (!contratoDependenteVO.isPresent()) {
                String msg = String.format("O contrato dependente não foi encontrato para o contrato titular %d ao tentar vincular os clientes %s %s e %s %s",
                        contratoVO.getCodigo(), clienteTitular.getMatricula(), clienteTitular.getNome_Apresentar(),
                        clienteDependente.getMatricula(), clienteDependente.getNome_Apresentar());
                falhasItemAtual.add(msg);
                falhas.add(msg);
                return;
            }

            FamiliarVO familiarVO = familiarDAO.consultarPorDependentePlanoCompartilhado(clienteDependente.getCodigo());
            if (familiarVO.getCodigo() == 0) {
                familiarVO.setCliente(clienteTitular.getCodigo());
                familiarVO.setFamiliar(clienteDependente.getCodigo());
                familiarVO.setNome(clienteDependente.getNome_Apresentar());
                familiarVO.setCodAcesso(clienteDependente.getCodAcesso());
                familiarVO.setParentesco(cache.obterParentesco("TITULAR"));
                familiarVO.setCompartilharPlano(true);
                familiarVO.setContratoCompartilhado(contratoVO.getCodigo());
                familiarVO.setContratoDependente(contratoDependenteVO.get());
                familiarVO.getContratoDependente().setCliente(clienteDependente);
                familiarDAO.incluir(familiarVO, true);
            }

            con.commit();
        } catch (Exception e) {
            String msg = String.format("Falha ao processar compartilhamento entre o titular: %d %s e dependente %d - erro: %s", clienteTitular.getCodigoMatricula(),  clienteTitular.getNome_Apresentar(), memberShipTransfer.getIdMemberTransfer(), e.getMessage());
            falhasItemAtual.add(msg);
            falhas.add(msg);
            con.rollback();
        } finally {
            con.setAutoCommit(true);
        }
    }

    private void estornarContrato(ContratoVO contratoVO) throws Exception {
        ClienteVO cliente = null;
        contratoVO.setUsuarioVO(cache.obterUsuarioVO(1));

        List<ReciboPagamentoVO> listaReciboPagamento = reciboPagamentoDAO.consularReciboPagamParcelaPorCodigoContrato(contratoVO.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        if (!listaReciboPagamento.isEmpty()) {
            contratoVO.setMovParcelaVOs(new ArrayList<>());
            contratoVO.setListaEstornoRecibo(new ArrayList<>());
            for (ReciboPagamentoVO recibo : listaReciboPagamento) {
                EstornoReciboVO estornoRecibo = new EstornoReciboVO();
                estornoRecibo.setReciboPagamentoVO(recibo);
                estornoRecibo.setListaMovPagamento(movPagamentoDAO.consultarPorCodigoRecibo(recibo.getCodigo(), false, Uteis.NIVELMONTARDADOS_CAIXAPARAOPERADOR));
                estornoRecibo.setListaMovParcela(movParcelaDAO.consultarPorCodigoRecibo(recibo.getCodigo(), false, Uteis.NIVELMONTARDADOS_MINIMOS));

                //transações de cartão de crédito
                contratoVO.montarListaTransacoes(estornoRecibo.getListaMovParcela(), con);
                contratoVO.getListaEstornoRecibo().add(estornoRecibo);
            }

        } else {
            contratoVO.setMovParcelaVOs(movParcelaDAO.consultarPorContrato(contratoVO.getCodigo(), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA));

            //transações de cartão de crédito
            contratoVO.montarListaTransacoes(contratoVO.getMovParcelaVOs(), con);
        }

        contratoVO.setPrecisaEstornarTransacoes(false);

        contratoVO.montarListaItensRemessa(movParcelaDAO.consultarPorContrato(contratoVO.getCodigo(), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA), con);

        try {
            cliente = clienteDAO.consultarPorCodigoPessoa(contratoVO.getPessoa().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            contratoDAO.estornoContrato(contratoVO, cliente, null, null);
            zillyonWebFacadeDAO.atualizarSintetico(cliente, Calendario.hoje(), SituacaoClienteSinteticoEnum.GRUPO_TODOS, false);
        } catch (Exception e) {
            String msg = String.format("Erro ao estornar contrato -> %d do cliente -> %d | Exceção -> %s",
                    contratoVO.getCodigo(), cliente.getCodigo(), e.getMessage());
            falhasItemAtual.add(msg);
            falhas.add(msg);
        }

    }

    public ContratoVO importarContrato(ClienteVO clienteVO, MemberMembershipApiViewTO memberShip, SalesApiViewTO sale, boolean isImportReceivables, IntegracaoMemberVO integracaoMemberVO) throws Exception {
        ContratoVO contratoVO;
        try {
            con.setAutoCommit(false);

            ResultSet rsContrato = SuperFacadeJDBC.criarConsulta("SELECT codigo FROM contrato WHERE coalesce(id_externo,idExterno) = " + memberShip.getIdMemberMembership()
                    + " and empresa = " + clienteVO.getEmpresa().getCodigo(), con);
            if (rsContrato.next()) {
                return contratoDAO.consultarPorCodigo(rsContrato.getInt("codigo"), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            }

            ContratoVO contratoRenovar = null;
            if (!UteisValidacao.emptyNumber(memberShip.getIdMemberMembershipRenewed())) {
                ResultSet rsContratoRenovar = SuperFacadeJDBC.criarConsulta("SELECT codigo FROM contrato WHERE coalesce(id_externo,idExterno) = " + memberShip.getIdMemberMembershipRenewed()
                        + " and empresa = " + clienteVO.getEmpresa().getCodigo(), con);
                if (rsContratoRenovar.next()) {
                    contratoRenovar = contratoDAO.consultarPorCodigo(rsContratoRenovar.getInt("codigo"), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                }
            }

            contratoVO = montarContrato(memberShip, sale, isImportReceivables, contratoRenovar, clienteVO, integracaoMemberVO);

            // Não importar contrato do plano em dobro zerado, pois esse é o contrato dependente
            if (contratoVO.getValorFinal() == 0.0 && (memberShip.getName().toLowerCase().contains("dobro") || contratoVO.getPlano().getQuantidadeCompartilhamentos() > 0)) {
                return contratoVO;
            }

            ImportarContrato importarContrato = new ImportarContrato(null, con);
            importarContrato.incluirSemCommit(contratoVO);

            if (contratoVO != null && !UteisValidacao.emptyNumber(contratoVO.getCodigo())) {
                Map<Integer, ReceivablesViewTO> mapReceivables = new HashMap<>();
                sale.getReceivables().forEach(r -> mapReceivables.put(r.getIdReceivable(), r));

                // Historico Contrato
                gerarHistoricoContrato(contratoVO);

                if (UteisValidacao.emptyNumber(contratoVO.getContratoBaseadoRenovacao())) {
                    atualizarContratoBaseadoRenovacao(contratoVO.getContratoBaseadoRenovacao(), contratoVO.getCodigo());
                }

                // Contrato dependente
                if (contratoVO.getPlano().getQuantidadeCompartilhamentos() > 0) {
                    ContratoDependente contratoDependenteDAO = new ContratoDependente(con);
                    contratoDependenteDAO.incluir(contratoVO);
                }

                String descricaoContrato = memberShip.getName().toLowerCase();
                boolean possuiCancelamento = contratoVO.getSituacao().equalsIgnoreCase("CA") || descricaoContrato.contains("cancelled") || descricaoContrato.contains("cancel");

                // Parcelas
                List<MovParcelaVO> movParcelaVOS = gerarParcelas(isImportReceivables ? sale.getReceivables() : new ArrayList<>(), contratoVO, possuiCancelamento);
                atualizarValorContrato(contratoVO, movParcelaVOS);
                // Produtos
                List<MovProdutoVO> movProdutoVOS = gerarMovProdutosMembers(contratoVO);
                gerarMovProdutoModalidade(contratoVO, movProdutoVOS);
                // Relacionamento entre produto e parcelas
                gerarMovprodutoParcela(movParcelaVOS, movProdutoVOS);
                verificarMovProdutosCancelados(contratoVO, movProdutoVOS);
                // Pagamentos
                if (isImportReceivables) {
                    gerarPagamentoParcelas(contratoVO, movParcelaVOS, mapReceivables);
                }

                processarCancelamento(contratoVO, memberShip);

                contratoDAO.gerarHistoricoTemporalUmContrato(contratoVO.getCodigo());

                logDAO.incluirLogItemImportacao("CONTRATO", contratoVO.getCodigo(), cache.getUsuarioVOImportacao(), contratoVO.getPessoa().getCodigo());

            } else {
                throw new Exception("Contrato não importado.");
            }
            con.commit();
        } catch (Exception ex) {
            con.rollback();
            throw ex;
        } finally {
            con.setAutoCommit(true);
        }
        return contratoVO;
    }

    private void processarCancelamento(ContratoVO contratoVO, MemberMembershipApiViewTO memberShip) throws Exception {
        String descricaoContrato = memberShip.getName().toLowerCase();
        ContratoOperacaoVO contratoOperacaoVO = null;
        if (contratoVO.getSituacao().equals("CA")) {
            MemberMemberShipCancellationApiViewTO memberShipCancellation = memberShip.getMemberShipCancellation();
            contratoOperacaoVO = montarOperacaoCancelamentoImportada(contratoVO, memberShipCancellation);

            Date dataCancelamento = UteisImportacao.getDateFromLocalDateTime(memberShip.getCancelDate());

            SuperFacadeJDBC.executarUpdate("update contrato set situacao = 'CA', \n" +
                    "vigenciaate = '" + Calendario.getDataAplicandoFormatacao(dataCancelamento, "yyyy-MM-dd") + "', \n" +
                    "vigenciaateajustada = '" + Calendario.getDataAplicandoFormatacao(dataCancelamento, "yyyy-MM-dd") + "', \n" +
                    "dataprevistarenovar = '" + Calendario.getDataAplicandoFormatacao(dataCancelamento, "yyyy-MM-dd") + "', \n" +
                    "dataprevistarematricula = '" + Calendario.getDataAplicandoFormatacao(dataCancelamento, "yyyy-MM-dd") + "' \n" +
                    " where codigo = " + contratoVO.getCodigo(), con);

            contratoOperacaoDAO.incluirSemCommit(contratoOperacaoVO, true);

        } else if (descricaoContrato.contains("cancelled") || descricaoContrato.contains("cancel")) {
            Pattern pattern = Pattern.compile("\\b(\\d{2})(\\d{2})(\\d{4})\\b");
            Matcher matcher = pattern.matcher(descricaoContrato);
            if (matcher.find()) {
                String dataStr = String.format("%s/%s/%s", matcher.group(1), matcher.group(2), matcher.group(3));
                Date dataCancelamento = Calendario.getDate("dd/MM/yyyy", dataStr);
                contratoOperacaoVO = montarOperacaoCancelamentoAgendadoImportada(contratoVO, dataCancelamento);
                contratoOperacaoDAO.incluirSemCommit(contratoOperacaoVO, true);

                SuperFacadeJDBC.executarConsulta("update movparcela set situacao = 'CA' \n" +
                        "where situacao = 'EA' \n" +
                        "and datavencimento > '" + Calendario.getData(dataCancelamento, "yyyy-MM-dd") + "' \n" +
                        "and contrato = " + contratoVO.getCodigo(), con);
                SuperFacadeJDBC.executarConsulta("update movproduto mpro set situacao = 'CA' from movprodutoparcela mpp, movparcela mpar\n" +
                        "where mpp.movproduto = mpro.codigo \n" +
                        "and mpar.codigo = mpp.movparcela \n" +
                        "and mpro.situacao = 'EA' \n" +
                        "and mpar.situacao = 'CA' \n" +
                        "and mpro.contrato = " + contratoVO.getCodigo(), con);
            }
        }
        if(contratoOperacaoVO != null) {
            // historico contrato
            HistoricoContrato historicoContratoDAO = new HistoricoContrato(con);
            HistoricoContratoVO historicoContratoVO = historicoContratoDAO.obterUltimoHistoricoContratoPorContrato(contratoVO.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            if (historicoContratoVO != null && !UteisValidacao.emptyNumber(historicoContratoVO.getCodigo())) {
                String sql = "update historicocontrato set datafinalsituacao = ? where codigo = ? ";
                PreparedStatement pstm = con.prepareStatement(sql);
                pstm.setDate(1, Uteis.getDataJDBC(Calendario.somarDias(contratoOperacaoVO.getDataInicioEfetivacaoOperacao(), -1)));
                pstm.setInt(2, historicoContratoVO.getCodigo());
                pstm.execute();
            }

            HistoricoContratoVO historicoCancelado = new HistoricoContratoVO();
            historicoCancelado.setContrato(contratoVO.getCodigo());
            historicoCancelado.setDescricao("CANCELADO");
            historicoCancelado.setTipoHistorico("CA");
            historicoCancelado.setResponsavelRegistro(contratoVO.getResponsavelContrato());
            historicoCancelado.setDataRegistro(contratoOperacaoVO.getDataOperacao());
            historicoCancelado.setDataInicioSituacao(contratoOperacaoVO.getDataInicioEfetivacaoOperacao());
            historicoCancelado.setDataFinalSituacao(contratoOperacaoVO.getDataFimEfetivacaoOperacao());
            historicoContratoDAO.incluirSemCommit(historicoCancelado, false);

            // periodo acesso
            PeriodoAcessoCliente periodoAcessoClienteDAO = new PeriodoAcessoCliente(con);
            PeriodoAcessoClienteVO periodoAcessoClienteVO = periodoAcessoClienteDAO.obterUltimoDiaPeriodoAcessoContrato(contratoVO.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            if (periodoAcessoClienteVO != null && !UteisValidacao.emptyNumber(periodoAcessoClienteVO.getCodigo())) {
                String sql = "update periodoacessocliente set dataFinalAcesso = ? where codigo = ? ";
                PreparedStatement pstm = con.prepareStatement(sql);
                pstm.setDate(1, Uteis.getDataJDBC(Calendario.somarDias(contratoOperacaoVO.getDataInicioEfetivacaoOperacao(), -1)));
                pstm.setInt(2, periodoAcessoClienteVO.getCodigo());
                pstm.execute();
            }
            PeriodoAcessoClienteVO periodoAcessoClienteCancelamento = new PeriodoAcessoClienteVO();
            periodoAcessoClienteCancelamento.setPessoa(contratoVO.getPessoa().getCodigo());
            periodoAcessoClienteCancelamento.setContrato(contratoVO.getCodigo());
            periodoAcessoClienteCancelamento.setDataInicioAcesso(contratoOperacaoVO.getDataInicioEfetivacaoOperacao());
            periodoAcessoClienteCancelamento.setDataFinalAcesso(contratoOperacaoVO.getDataFimEfetivacaoOperacao());
            periodoAcessoClienteCancelamento.setTipoAcesso("CN");
            periodoAcessoClienteDAO.incluirSemCommit(periodoAcessoClienteCancelamento);
        }
    }

    private void verificarMovProdutosCancelados(ContratoVO contratoVO, List<MovProdutoVO> movProdutoVOS) throws SQLException {
        if (!UteisValidacao.emptyList(movProdutoVOS)) {
            String sql = "update movproduto set situacao = 'CA' where situacao = 'EA' and codigo in ( \n" +
                    " select mpro.codigo from movproduto mpro \n " +
                    " inner join movprodutoparcela mpp on mpp.movproduto = mpro.codigo \n" +
                    " inner join movparcela mpar on mpar.codigo = mpp.movparcela \n" +
                    " where mpro.codigo in (" + movProdutoVOS.stream().map(m -> m.getCodigo().toString()).collect(Collectors.joining(",")) + ") \n" +
                    " and mpar.situacao = 'CA');";
            SuperFacadeJDBC.executarUpdate(sql, con);
        }
    }

    private ContratoOperacaoVO montarOperacaoCancelamentoImportada(ContratoVO contratoVO, MemberMemberShipCancellationApiViewTO memberShipCancellation) throws Exception {
        ContratoOperacaoVO contratoOperacaoVO = new ContratoOperacaoVO();
        contratoOperacaoVO.setContrato(contratoVO.getCodigo());
        contratoOperacaoVO.setDataFimEfetivacaoOperacao(contratoVO.getVigenciaAteAjustada());
        contratoOperacaoVO.setDataInicioEfetivacaoOperacao(contratoVO.getVigenciaAteAjustada());
        contratoOperacaoVO.setDataOperacao(contratoVO.getVigenciaAteAjustada());
        String descricaCalculo = "Operação Importada" + (memberShipCancellation != null && !UteisValidacao.emptyString(memberShipCancellation.getReasonCancellation())
                ? ": \nMotivo: " + memberShipCancellation.getReasonCancellation() : "");
        contratoOperacaoVO.setDescricaoCalculo(descricaCalculo);
        contratoOperacaoVO.setOperacaoPaga(true);
        contratoOperacaoVO.setResponsavel(cache.getUsuarioVOImportacao());
        contratoOperacaoVO.setTipoOperacao("CA");
        contratoOperacaoVO.setTipoJustificativa(cache.obterJustificativaOperacao(contratoVO.getEmpresa().getCodigo(), "Importação", "CA"));
        return contratoOperacaoVO;
    }

    private ContratoOperacaoVO montarOperacaoCancelamentoAgendadoImportada(ContratoVO contratoVO, Date dataCancelamento) throws Exception {
        ContratoOperacaoVO contratoOperacaoVO = new ContratoOperacaoVO();
        contratoOperacaoVO.setContrato(contratoVO.getCodigo());
        contratoOperacaoVO.setDataOperacao(new Date());
        contratoOperacaoVO.setDataInicioEfetivacaoOperacao(dataCancelamento);
        contratoOperacaoVO.setDataFimEfetivacaoOperacao(dataCancelamento);
        String descricaCalculo = "Operação Importada - Cancelamento agendado";
        contratoOperacaoVO.setDescricaoCalculo(descricaCalculo);
        contratoOperacaoVO.setOperacaoPaga(true);
        contratoOperacaoVO.setResponsavel(cache.getUsuarioVOImportacao());
        contratoOperacaoVO.setTipoOperacao("CA");
        contratoOperacaoVO.setTipoJustificativa(cache.obterJustificativaOperacao(contratoVO.getEmpresa().getCodigo(), "Importação", "CA"));
        return contratoOperacaoVO;
    }

    private void atualizarContratoBaseadoRenovacao(Integer contrato, Integer contratoResponsavelRenovacao) throws SQLException {
        String sql = "update contrato set contratoresponsavelrenovacaomatricula = ? where codigo = ?";
        PreparedStatement pstm = con.prepareStatement(sql);
        pstm.setInt(1, contratoResponsavelRenovacao);
        pstm.setInt(2, contrato);
        pstm.execute();
    }

    private List<MovParcelaVO> gerarParcelas(List<ReceivablesViewTO> receivables, ContratoVO contratoVO, boolean possuiCancelamento) throws Exception {
        List<MovParcelaVO> movParcelaVOS = new ArrayList<>();

        for (ReceivablesViewTO receivable: receivables) {
            PaymentTypeEnum paymentTypeEnum = obterPaymentType(receivable);
            if (paymentTypeEnum != null && paymentTypeEnum.getId() == PaymentTypeEnum.SALES_CREDIT.getId()) {
                continue;
            }
            Double valorParcela = receivable.getAmmount();
            Date dataVencimento;
            String description = !UteisValidacao.emptyString(receivable.getDescription()) ? receivable.getDescription().toUpperCase() : "";
            if (receivable.getCompetenceDate() != null && (description.contains("CARTÃO") || description.contains("CARTAO"))) {
                dataVencimento = UteisImportacao.getDateFromLocalDateTime(receivable.getCompetenceDate());
            } else {
                dataVencimento = UteisImportacao.getDateFromLocalDateTime(receivable.getDueDate());
            }

            StatusRecevablesEnum statusRecevablesEnum = StatusRecevablesEnum.obterPorId(receivable.getStatus().getId());
            if (statusRecevablesEnum == null) {
                throw new Exception("Falha ao obter status do recebivel id: " + receivable.getIdReceivable() + " idvenda: " + receivable.getIdSale() + " - " + receivable.getDescription());
            }

            MovParcelaVO movParcelaVO = new MovParcelaVO();
            movParcelaVO.setContrato(contratoVO);
            movParcelaVO.setDataRegistro(contratoVO.getDataLancamento());
            movParcelaVO.setDataVencimento(dataVencimento);
            movParcelaVO.setResponsavel(contratoVO.getResponsavelContrato());
            movParcelaVO.setSituacao("EA");
            if (statusRecevablesEnum.getId().equals(StatusRecevablesEnum.CANCELED)) {
                movParcelaVO.setSituacao("CA");
            } else if (statusRecevablesEnum.getId().equals(StatusRecevablesEnum.RECEIVED.getId())
                    || !UteisValidacao.emptyString(receivable.getAuthorization())) {
                movParcelaVO.setSituacao("PG");
            }
            movParcelaVO.setValorParcela(valorParcela);
            movParcelaVO.setPercentualJuro(0.0);
            movParcelaVO.setPercentualMulta(0.0);
            movParcelaVO.setPessoa(contratoVO.getPessoa());
            movParcelaVO.setEmpresa(contratoVO.getEmpresa());
            movParcelaVO.setIdExterno(receivable.getIdReceivable());
            movParcelaVO.setRegimeRecorrencia(contratoVO.getRegimeRecorrencia());

            movParcelaVOS.add(movParcelaVO);

        }

        Double valorTotalParcelas = movParcelaVOS.stream()
                .mapToDouble(MovParcelaVO::getValorParcela)
                .sum();
        if (possuiCancelamento && contratoVO.getValorFinal() > valorTotalParcelas) {
            Double valorParcela = contratoVO.getValorFinal() - valorTotalParcelas;
            Date maiorDataVencimentoParcela = null;

            if (!UteisValidacao.emptyList(movParcelaVOS)) {
                for (MovParcelaVO parcelaVO : movParcelaVOS) {
                    if (maiorDataVencimentoParcela == null) {
                        maiorDataVencimentoParcela = parcelaVO.getDataVencimento();
                    } else if (Calendario.maior(parcelaVO.getDataVencimento(), maiorDataVencimentoParcela)) {
                        maiorDataVencimentoParcela = parcelaVO.getDataVencimento();
                    }
                }
            }
            Date dataVencimento = maiorDataVencimentoParcela != null ? Calendario.somarDias(maiorDataVencimentoParcela, 1) : contratoVO.getVigenciaDe();
            dataVencimento = Calendario.maior(dataVencimento, contratoVO.getVigenciaAteAjustada())
                    ? maiorDataVencimentoParcela : dataVencimento;

            MovParcelaVO movParcelaVO = new MovParcelaVO();
            movParcelaVO.setContrato(contratoVO);
            movParcelaVO.setDataRegistro(contratoVO.getDataLancamento());
            movParcelaVO.setDataVencimento(dataVencimento);
            movParcelaVO.setResponsavel(contratoVO.getResponsavelContrato());
            movParcelaVO.setSituacao("CA");
            movParcelaVO.setValorParcela(valorParcela);
            movParcelaVO.setPercentualJuro(0.0);
            movParcelaVO.setPercentualMulta(0.0);
            movParcelaVO.setPessoa(contratoVO.getPessoa());
            movParcelaVO.setEmpresa(contratoVO.getEmpresa());
            movParcelaVOS.add(movParcelaVO);
        }

        if (movParcelaVOS.isEmpty()) {
            Double valorParcela = 0.0;
            Date dataVencimento = contratoVO.getVigenciaDe();

            MovParcelaVO movParcelaVO = new MovParcelaVO();
            movParcelaVO.setContrato(contratoVO);
            movParcelaVO.setDataRegistro(contratoVO.getDataLancamento());
            movParcelaVO.setDataVencimento(dataVencimento);
            movParcelaVO.setResponsavel(contratoVO.getResponsavelContrato());
            movParcelaVO.setSituacao(contratoVO.getValorFinal() == 0.0 ? "PG" : "CA");
            movParcelaVO.setValorParcela(contratoVO.getValorFinal());
            movParcelaVO.setPercentualJuro(0.0);
            movParcelaVO.setPercentualMulta(0.0);
            movParcelaVO.setPessoa(contratoVO.getPessoa());
            movParcelaVO.setEmpresa(contratoVO.getEmpresa());
            movParcelaVOS.add(movParcelaVO);
        }

        // Ordenação das parcelas
        Ordenacao.ordenarLista(movParcelaVOS, "dataVencimento");
        List<MovParcelaVO> parcelasPagas = movParcelaVOS.stream().filter(p -> p.getSituacao().equals("PG")).collect(Collectors.toList());
        List<MovParcelaVO> parcelaAbertas = movParcelaVOS.stream().filter(p -> p.getSituacao().equals("EA")).collect(Collectors.toList());
        if (movParcelaVOS.get(0).getSituacao().equals("EA") && !UteisValidacao.emptyList(parcelasPagas)) {
            // nesse caso, gerar as parcelas pagas primeiro, para evitar estornos automaticos de contratos, por causa da primeira parcela em aberto a x dias.
            movParcelaVOS = new ArrayList<>();
            movParcelaVOS.addAll(parcelasPagas);
            movParcelaVOS.addAll(parcelaAbertas);
        }

        int numero = 0;
        for (MovParcelaVO movParcelaVO: movParcelaVOS) {
            movParcelaVO.setDescricao("PARCELA " + (++numero));
            movParcelaDAO.incluirParcelaSemCommit(movParcelaVO);
        }

        return movParcelaVOS;
    }

    private void gerarMovProdutoModalidade(ContratoVO contratoVO, List<MovProdutoVO> movProdutoVOS) throws Exception {
        for (MovProdutoVO movProdutoVO: movProdutoVOS) {
            movProdutoVO.setMovProdutoModalidades(zillyonWebFacadeDAO.gerarMovProdutoModalidade(MovProdutoModalidade.getValorSomaContratoModalidade(contratoVO.getContratoModalidadeVOs()),
                    contratoVO.getVigenciaDe(), contratoVO.getVigenciaAte(), contratoVO.getContratoModalidadeVOs(), movProdutoVO.getTotalFinal()));
            movProdutoDAO.alterarSemCommit(movProdutoVO);
            movProdutoModalidadeDAO.incluir(movProdutoVO);
        }
    }

    private List<MovProdutoVO> gerarMovProdutosMembers(ContratoVO contratoVO) throws Exception {
        List<MovProdutoVO> lista = new ArrayList<MovProdutoVO>();
        Integer duracao = contratoVO.getContratoDuracao().getNumeroMeses();
        Date dataInicio = contratoVO.getVigenciaDe();
        Date dataFim = Uteis.somarCampoData(dataInicio, Calendar.MONTH, 1);

        Integer codProdutoMensalidade = contratoVO.getPlano().getProdutoPadraoGerarParcelasContrato().getCodigo();
        Double valorMensalidadeContrato = (contratoVO.getValorBaseCalculo());

        if (duracao > 1) {
            Double valorMensalidade = Uteis.arredondarForcando2CasasDecimais(valorMensalidadeContrato / duracao);
            lista.add(montarMensalidadeMembers(contratoVO, dataInicio, dataFim, codProdutoMensalidade, valorMensalidade));
            for (int i = 1; i < duracao; i++) {
                dataInicio = dataFim;
                dataFim = Uteis.somarCampoData(dataInicio, Calendar.MONTH, 1);
                lista.add(montarMensalidadeMembers(contratoVO, dataInicio, dataFim, codProdutoMensalidade, valorMensalidade));
            }
        } else {
            lista.add(montarMensalidadeMembers(contratoVO, dataInicio, contratoVO.getVigenciaAte(), codProdutoMensalidade, valorMensalidadeContrato));
        }

        movProdutoDAO.incluirListaMovProdutos(lista);
        return lista;
    }

    private MovProdutoVO montarMensalidadeMembers(ContratoVO contratoVO, Date inicio, Date fim,
                                                  Integer codProduto, Double valor) {

        Integer ano = Uteis.getAnoData(inicio);
        String mes = Uteis.getMesReferencia(inicio);

        MovProdutoVO movProduto = new MovProdutoVO();
        movProduto.setSituacao(UteisValidacao.emptyNumber(valor) ? "PG" : "EA");
        movProduto.setDataFinalVigencia(fim);
        movProduto.setDataInicioVigencia(inicio);
        movProduto.setAnoReferencia(ano);
        movProduto.setMesReferencia(mes + "/" + ano);
        movProduto.setResponsavelLancamento(contratoVO.getResponsavelContrato());
        movProduto.setDataLancamento(contratoVO.getDataLancamento());
        movProduto.setTotalFinal(valor);
        movProduto.setValorFaturado(valor);
        movProduto.setValorDesconto(0.0);
        movProduto.setPrecoUnitario(valor);
        movProduto.setValorPagoMovProdutoParcela(movProduto.getTotalFinal());
        movProduto.setQuantidade(1);
        movProduto.setDescricao(contratoVO.getPlano().getDescricao() + " - " + mes + "/" + ano);
        movProduto.setEmpresa(contratoVO.getEmpresa());
        movProduto.setPessoa(contratoVO.getPessoa());
        movProduto.setContrato(contratoVO);
        ProdutoVO produto = new ProdutoVO();
        produto.setCodigo(codProduto);
        movProduto.setProduto(produto);
        movProduto.setApresentarMovProduto(true);
        movProduto.setQuitado(movProduto.getSituacao().equals("PG"));
        return movProduto;
    }


    public void gerarHistoricoContrato(ContratoVO contratoVO) throws Exception {

        HistoricoContrato historicoContratoDAO = new HistoricoContrato(con);
        HistoricoContratoVO historicoContratoVO = new HistoricoContratoVO();
        historicoContratoVO.setContrato(contratoVO.getCodigo());
        if (!UteisValidacao.emptyNumber(contratoVO.getContratoBaseadoRenovacao())) {
            historicoContratoVO.setDescricao("Renovado");
            historicoContratoVO.setTipoHistorico("RN");
        } else {
            historicoContratoVO.setDescricao("Matriculado");
            historicoContratoVO.setTipoHistorico("MA");
        }
        historicoContratoVO.setDataInicioTemporal(contratoVO.getVigenciaDe());
        historicoContratoVO.setResponsavelRegistro(contratoVO.getResponsavelContrato());
        historicoContratoVO.setDataRegistro(contratoVO.getDataLancamento());
        historicoContratoVO.setDataInicioSituacao(contratoVO.getVigenciaDe());
        historicoContratoVO.setDataFinalSituacao(contratoVO.getVigenciaAteAjustada());

        historicoContratoDAO.incluirSemCommit(historicoContratoVO, false);
    }

    private void gerarMovprodutoParcela(List<MovParcelaVO> movParcelaVOS, List<MovProdutoVO> movProdutoVOS) throws Exception {
        MovProdutoParcela movProdutoParcelaDAO = new MovProdutoParcela(con);

        for (MovParcelaVO movParcelaVO: movParcelaVOS) {
            if (UteisValidacao.emptyNumber(movParcelaVO.getValorParcela())) {
                continue;
            }

            movParcelaVO.setValorBaseCalculo(movParcelaVO.getValorParcela());
            for (MovProdutoVO movProdutoVO: movProdutoVOS) {
                MovProdutoParcelaVO mpp = new MovProdutoParcelaVO();
                mpp.setReciboPagamento(null);
                mpp.setMovParcela(movParcelaVO.getCodigo());

                if(movProdutoVO.getQuitado()){
                    continue;
                }
                mpp.setMovProduto(movProdutoVO.getCodigo());
                if(Uteis.arredondarForcando2CasasDecimais(movParcelaVO.getValorBaseCalculo()) >= Uteis.arredondarForcando2CasasDecimais(movProdutoVO.getValorPagoMovProdutoParcela())){
                    mpp.setValorPago(movProdutoVO.getValorPagoMovProdutoParcela());
                    movParcelaVO.setValorBaseCalculo(Uteis.arredondarForcando2CasasDecimais(movParcelaVO.getValorBaseCalculo() - movProdutoVO.getValorPagoMovProdutoParcela()));
                    movProdutoVO.setQuitado(true);
                    movProdutoVO.setValorPagoMovProdutoParcela(0.0);
                } else {
                    mpp.setValorPago(movParcelaVO.getValorBaseCalculo());
                    movProdutoVO.setValorPagoMovProdutoParcela(Uteis.arredondarForcando2CasasDecimais( movProdutoVO.getValorPagoMovProdutoParcela() - movParcelaVO.getValorBaseCalculo()) );
                    movParcelaVO.setValorBaseCalculo(0.0);
                }
                movProdutoVO.setQuitado(Uteis.arredondarForcando2CasasDecimais(movProdutoVO.getValorPagoMovProdutoParcela()) == 0.00);
                movProdutoParcelaDAO.incluir(mpp);
                if(movParcelaVO.getValorBaseCalculo() == 0.00){
                    break;
                }
            }
        }
    }

    private void atualizarValorContrato(ContratoVO contratoVO, List<MovParcelaVO> parcelaVOS) throws SQLException {
        Double valorTotalParcelas = parcelaVOS.stream()
                .mapToDouble(MovParcelaVO::getValorParcela)
                .sum();
        contratoVO.setValorFinal(valorTotalParcelas);
        contratoVO.setValorBaseCalculo(valorTotalParcelas);
        String sql = "update contrato set valorfinal = ?, valorbasecalculo = ? where codigo = ?";
        PreparedStatement pstm = con.prepareStatement(sql);
        pstm.setDouble(1, valorTotalParcelas);
        pstm.setDouble(2, valorTotalParcelas);
        pstm.setInt(3, contratoVO.getCodigo());
        pstm.execute();
    }

    private ContratoVO montarContrato(MemberMembershipApiViewTO memberShip, SalesApiViewTO sale, boolean isImportarRecebiveis, ContratoVO contratoRenovar, ClienteVO clienteVO, IntegracaoMemberVO integracaoMemberVO) throws Exception {
        ContratoVO contratoVO = new ContratoVO();

        contratoVO.setIdExterno(memberShip.getIdMemberMembership());
        contratoVO.setPessoa(clienteVO.getPessoa());
        contratoVO.setObservacao("plano evo: " + memberShip.getIdMembership() + " - " + memberShip.getName());

        SaleItemApiViewTO saleItem = sale.getSaleItens().stream()
                .filter(si -> !UteisValidacao.emptyNumber(si.getIdMemberMembership()) ? si.getIdMemberMembership().equals(memberShip.getIdMemberMembership()) : false)
                .findFirst()
                .orElse(null);
        if (isImportarRecebiveis && saleItem != null) {
            Double valorContrato = saleItem.getItemValue();
            contratoVO.setValorBaseCalculo(valorContrato);
            contratoVO.setValorFinal(valorContrato);
        }

        //empresa
        EmpresaVO empresaVO = cache.obterEmpresaVO(clienteVO.getEmpresa().getCodigo());
        contratoVO.setEmpresa(empresaVO);

        // Datas
        Date lancamentoContrato = UteisImportacao.getDateFromLocalDateTime(memberShip.getSaleDate());

        contratoVO.setVigenciaDe(memberShip.getDataInicio());
        contratoVO.setVigenciaAte(memberShip.getDataFim());
        contratoVO.setVigenciaAteAjustada(memberShip.getDataFim());
        contratoVO.setDataLancamento(lancamentoContrato);
        contratoVO.setDataMatricula(memberShip.getDataInicio());
        contratoVO.setDataPrevistaRematricula(memberShip.getDataFim());
        contratoVO.setDataPrevistaRenovar(memberShip.getDataFim());

        // Contrato renovar
        if (contratoRenovar != null && !UteisValidacao.emptyNumber(contratoRenovar.getCodigo())
                && Calendario.maiorOuIgual(contratoVO.getVigenciaDe(), contratoRenovar.getVigenciaAteAjustada())) {
            Long diferencaoDiaContratoBaseRN = Uteis.nrDiasEntreDatas(contratoRenovar.getVigenciaAteAjustada(), contratoVO.getVigenciaDe());
            if (diferencaoDiaContratoBaseRN.intValue() <= empresaVO.getCarenciaRenovacao().intValue()) {
                contratoVO.setSituacaoContrato("RN");
                contratoVO.setContratoBaseadoRenovacao(contratoRenovar.getCodigo());
                contratoVO.setVigenciaDe(Calendario.somarDias(contratoRenovar.getVigenciaAteAjustada(), 1));
            }
        }

        // Duracao
        ContratoDuracaoVO contratoDuracaoVO = new ContratoDuracaoVO();
        contratoDuracaoVO.setCarencia(integracaoMemberVO.getDiasCarencia());

        Integer numeroMeses;
        Long dias = Uteis.nrDiasEntreDatas(contratoVO.getVigenciaDe(), contratoVO.getVigenciaAteAjustada());
        if (dias < 30) {
            numeroMeses = 1;
        } else {
            numeroMeses = (new Long(dias / 30).intValue());
        }
        contratoDuracaoVO.setNumeroMeses(numeroMeses);
        contratoVO.setContratoDuracao(contratoDuracaoVO);


        // Situacao do contrato
        if (Calendario.menor(contratoVO.getVigenciaAteAjustada(), Calendario.hoje())) {
            contratoVO.setSituacao("IN");
        } else {
            contratoVO.setSituacao("AT");
        }
        String status = memberShip.getMembershipStatus().toLowerCase();
        if (status.equals(MemberShipStatusEnum.CANCELED.getName().toLowerCase()) && !UteisValidacao.emptyString(memberShip.getCancelDate())) {
            Date dataCancelamento = UteisImportacao.getDateFromLocalDateTime(memberShip.getCancelDate());
            if (Calendario.menorOuIgual(dataCancelamento, Calendario.hoje())) {
                contratoVO.setSituacao("CA");
                contratoVO.setVigenciaAteAjustada(dataCancelamento);
            }
        }

        // Plano
        PlanoVO planoVO = null;
        if (!UteisValidacao.emptyNumber(memberShip.getIdMembership())) {
            planoVO = cache.obterPlanoPorIdExterno(memberShip.getIdMembership(), integracaoMemberVO.getEmpresa().getCodigo());
            if (planoVO == null || UteisValidacao.emptyNumber(planoVO.getCodigo())) {
                planoVO = cache.getMapaPlanoCorrespondencia().get(memberShip.getIdMembership().toString());
            }
        }
        if (planoVO != null) {
            for (PlanoModalidadeVO planoModalidadeVO: planoVO.getPlanoModalidadeVOs()) {
                contratoVO.getContratoModalidadeVOs().add(montarContratoModalidade(planoModalidadeVO.getModalidade(), contratoVO));
            }
        } else {
            planoVO = cache.obterPlanoVO(integracaoMemberVO.getPlanoPadrao());
            if (planoVO == null) {
                throw new Exception("Falha ao obter plano padrão!");
            }
            ModalidadeVO modalidadeVO = cache.obterModalidadeVO(integracaoMemberVO.getModalidadePadrao());
            contratoVO.getContratoModalidadeVOs().add(montarContratoModalidade(modalidadeVO, contratoVO));
        }
        contratoVO.setPlano(planoVO);

        if (UteisValidacao.emptyList(contratoVO.getContratoModalidadeVOs())) {
            throw new Exception("Falha ao adicionar modalidades no contrato");
        }

        if (planoVO.getRegimeRecorrencia()) {
            if (contratoVO.getValorBaseCalculo().intValue() > 0.0) {
                contratoVO.setRegimeRecorrencia(true);
            } else {
                contratoVO.setPlano(cache.obterPlanoVO(integracaoMemberVO.getPlanoPadrao()));
                contratoVO.setRegimeRecorrencia(false);
            }
        }

        // Responsável e consultor
        Integer usuario = cache.getUsuarioVOImportacao().getCodigo();
        if (UteisValidacao.emptyNumber(usuario)) {
            usuario = cache.getUsuarioVOImportacao().getCodigo();
        }
        contratoVO.setResponsavelContrato(cache.obterUsuarioVO(usuario));

        Integer consultor = cache.getUsuarioVOImportacao().getColaboradorVO().getCodigo();
        if (UteisValidacao.emptyNumber(consultor)) {
            consultor = cache.obterColaboradorPactoBREmpresa(empresaVO.getCodigo()).getCodigo();
        }
        contratoVO.setConsultor(cache.obterColaboradorVO(consultor));

        //plano duracao
        PlanoDuracaoVO planoDuracaoVO = planoDuracaoDAO.consultarPorNumeroMesesPlano(contratoVO.getContratoDuracao().getNumeroMeses(), 1, Uteis.NIVELMONTARDADOS_DADOSBASICOS);

        if (planoDuracaoVO == null || UteisValidacao.emptyNumber(planoDuracaoVO.getCodigo())) {
            planoDuracaoVO = new PlanoDuracaoVO();
            planoDuracaoVO.setCodigo(1);
            contratoVO.setPlanoDuracao(planoDuracaoVO);
            contratoVO.getPlanoDuracao().setDuracaoEscolhida(true);
            contratoVO.getPlanoDuracao().setNrMaximoParcelasCondPagamento(1);
            contratoVO.getPlanoDuracao().setNumeroMeses(contratoVO.getContratoDuracao().getNumeroMeses());
            contratoVO.getPlanoDuracao().setValorDesejado(0.0);
            contratoVO.getPlanoDuracao().setCarencia(integracaoMemberVO.getDiasCarencia());
            contratoVO.getPlanoDuracao().setTipoValor("PD");
            contratoVO.getPlanoDuracao().setTipoOperacao("AC");
        } else {
            planoDuracaoVO.setCarencia(integracaoMemberVO.getDiasCarencia());
            contratoVO.setPlanoDuracao(planoDuracaoVO);
        }

        //horario contrato
        ContratoHorarioVO contratoHorarioVO = new ContratoHorarioVO();
        HorarioVO horarioVO = cache.obterHorarioVO(integracaoMemberVO.getHorarioPadrao());
        contratoHorarioVO.setHorario(horarioVO);
        contratoVO.setContratoHorario(contratoHorarioVO);
        //horario contrato /
        PlanoHorarioVO planoHorarioVO = new PlanoHorarioVO();
        planoHorarioVO.setHorario(horarioVO);
        contratoVO.setPlanoHorario(planoHorarioVO);

        //condição de pagamento
        List listaPlanoCond = planoCondicaoPagamentoDAO.consultaPlanoCondicaoPagamentosNumeroParcelas(contratoVO.getPlanoDuracao().getCodigo(), contratoVO.getPlanoDuracao().getNumeroMeses(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        PlanoCondicaoPagamentoVO planoCondicaoPagamentoVO = new PlanoCondicaoPagamentoVO();
        if (!UteisValidacao.emptyList(listaPlanoCond)) {
            planoCondicaoPagamentoVO = (PlanoCondicaoPagamentoVO) listaPlanoCond.get(0);
        }
        contratoVO.setPlanoCondicaoPagamento(planoCondicaoPagamentoVO);

        //plano texto padrão
        ContratoTextoPadraoVO contratoTextoPadraoVO = new ContratoTextoPadraoVO();
        contratoTextoPadraoVO.setPlanoTextoPadrao(contratoVO.getPlano().getPlanoTextoPadrao());
        contratoVO.setContratoTextoPadrao(contratoTextoPadraoVO);

        // periodoacessocliente
        contratoVO.setPeriodoAcessoClienteVOs(new ArrayList());
        contratoVO.getPeriodoAcessoClienteVOs().add(montarPeriodoAcesso(contratoVO));

        // Recorrencia
        if (contratoVO.isRegimeRecorrencia()) {
            Double valorMensal = Uteis.arredondarForcando2CasasDecimais(contratoVO.getValorBaseCalculo() / contratoVO.getContratoDuracao().getNumeroMeses());
            ContratoRecorrenciaVO contratoRecorrenciaVO = new ContratoRecorrenciaVO();
            contratoRecorrenciaVO.setContrato(contratoVO);
            contratoRecorrenciaVO.setDiaVencimentoAnuidade(Uteis.getDiaMesData(Calendario.hoje()));
            contratoRecorrenciaVO.setMesVencimentoAnuidade(Uteis.getMesData(Calendario.hoje()));
            contratoRecorrenciaVO.setDiaVencimentoCartao(Uteis.getDiaMesData(Calendario.hoje()));
            contratoRecorrenciaVO.setDiasCancelamentoAutomatico(contratoVO.getPlano().getPlanoRecorrencia().getQtdDiasAposVencimentoCancelamentoAutomatico());
            contratoRecorrenciaVO.setFidelidade(contratoVO.getContratoDuracao().getNumeroMeses());
            contratoRecorrenciaVO.setPessoa(contratoVO.getPessoa());
            contratoRecorrenciaVO.setRenovavelAutomaticamente(true);
            contratoRecorrenciaVO.setValorMensal(valorMensal);
            contratoRecorrenciaVO.setValorAnuidade(0.0);
            contratoRecorrenciaVO.setAnuidadeNaParcela(false);
            contratoRecorrenciaVO.setCancelamentoProporcional(false);
            contratoVO.setContratoRecorrenciaVO(contratoRecorrenciaVO);
        }

        return contratoVO;
    }

    private void montarOperacaoCancelamento(ContratoVO contratoVO) {
        ContratoOperacaoVO obj = new ContratoOperacaoVO();
        obj.setDataFimEfetivacaoOperacao(contratoVO.getVigenciaAteAjustada());
        obj.setDataInicioEfetivacaoOperacao(contratoVO.getVigenciaAteAjustada());
        obj.setDataOperacao(contratoVO.getVigenciaAteAjustada());
        obj.setDescricaoCalculo("Operação Importada");
        obj.setOperacaoPaga(true);
        obj.setResponsavel(contratoVO.getResponsavelContrato());
        obj.setTipoOperacao("CA");
        obj.getTipoJustificativa().setCodigo(1);
    }

    private ContratoModalidadeVO montarContratoModalidade(ModalidadeVO modalidadeVO, ContratoVO contratoVO) throws Exception {
        ContratoModalidadeVO contratoModalidadeVO = new ContratoModalidadeVO();
        modalidadeVO.setModalidadeEscolhida(true);
        Double valor = (contratoVO.getValorFinal() - 0.0) / contratoVO.getContratoDuracao().getNumeroMeses();

        contratoModalidadeVO.setModalidade(modalidadeVO);
        contratoModalidadeVO.setNrVezesSemana(7);
        contratoModalidadeVO.setValorModalidade(valor);
        contratoModalidadeVO.setValorFinalModalidade(valor);

        PlanoModalidadeVezesSemanaVO planoVezesSemanaVO = new PlanoModalidadeVezesSemanaVO();
        planoVezesSemanaVO.setNrVezes(modalidadeVO.getNrVezes());
        planoVezesSemanaVO.setVezeSemanaEscolhida(true);
        contratoModalidadeVO.setPlanoVezesSemanaVO(planoVezesSemanaVO);

        contratoModalidadeVO.setModalidade(modalidadeVO);
        contratoModalidadeVO.getModalidade().setUtilizarTurma(false);
        contratoModalidadeVO.getModalidade().setModalidadeEscolhida(true);

        return contratoModalidadeVO;
    }

    private void gerarPagamentoParcelas(List<MovParcelaVO> parcelaVOS, Map<Integer, ReceivablesViewTO> mapaParcelasReceivables) throws Exception {
        gerarPagamentoParcelas(null, parcelaVOS, mapaParcelasReceivables);
    }

    private void gerarPagamentoParcelas(ContratoVO contratoVO, List<MovParcelaVO> parcelaVOS, Map<Integer, ReceivablesViewTO> mapaParcelasReceivables) throws Exception {
        if (mapaParcelasReceivables.size() == 0) {
            return;
        }

        Map<String, MovPagamentoVO> mapaPagamentos = new HashMap<>();

        for (MovParcelaVO parcelaVO: parcelaVOS) {
            if (!parcelaVO.getSituacao().equals("PG")) {
                continue;
            }
            ReceivablesViewTO receivable = mapaParcelasReceivables.get(parcelaVO.getIdExterno());
            if (receivable == null) {
                return;
            }

            FormaPagamentoVO formaPagamento = null;
            PaymentTypeEnum paymentTypeEnum = obterPaymentType(receivable);

            switch (paymentTypeEnum) {
                case CASH:
                    formaPagamento = cache.obterFormaPagamento(TipoFormaPagto.AVISTA);
                    break;
                case CREDIT_CARD:
                    formaPagamento = cache.obterFormaPagamento(TipoFormaPagto.CARTAOCREDITO);
                    break;
                case ONLINE_CREDIT_CARD:
                    formaPagamento = cache.obterFormaPagamento(TipoFormaPagto.CARTAOCREDITO);
                    break;
                case DEBIT_CARD:
                    formaPagamento = cache.obterFormaPagamento(TipoFormaPagto.CARTAODEBITO);
                    break;
                case CHECK:
                    formaPagamento = cache.obterFormaPagamento(TipoFormaPagto.CHEQUE);
                    break;
                case BANK_SLIP:
                    formaPagamento = cache.obterFormaPagamento(TipoFormaPagto.BOLETOBANCARIO);
                    break;
                case SALES_CREDIT:
                    formaPagamento = cache.obterFormaPagamento(TipoFormaPagto.CREDITOCONTACORRENTE);
                    break;
                case TRANSFER:
                    formaPagamento = cache.obterFormaPagamento(TipoFormaPagto.TRANSFERENCIA_BANCARIA);
                    break;
                case DEPOSIT:
                    formaPagamento = cache.obterFormaPagamento(TipoFormaPagto.TRANSFERENCIA_BANCARIA);
                    break;
                case ACCOUNT_DEBIT:
                    formaPagamento = cache.obterFormaPagamento(TipoFormaPagto.TRANSFERENCIA_BANCARIA);
                    break;
                case PIX:
                    formaPagamento = cache.obterFormaPagamento(TipoFormaPagto.PIX);
                    break;
                default:
                    formaPagamento = cache.obterFormaPagamento(TipoFormaPagto.AVISTA);
                    break;
            }

            MovPagamentoVO movPagamentoVO = montarMovPagamento(mapaPagamentos, formaPagamento, parcelaVO.getPessoa(), parcelaVO.getEmpresa(), receivable);

            PagamentoMovParcelaVO pagamentoMovParcelaVO = new PagamentoMovParcelaVO();
            pagamentoMovParcelaVO.setMovParcela(parcelaVO);
            pagamentoMovParcelaVO.setValorPago(parcelaVO.getValorParcela());
            movPagamentoVO.getPagamentoMovParcelaVOs().add(pagamentoMovParcelaVO);
        }


        List<MovPagamentoVO> pagamentoVOS = new ArrayList<>();
        for (String key: mapaPagamentos.keySet()) {
            MovPagamentoVO movPagamentoVO = mapaPagamentos.get(key);
            pagamentoVOS.add(movPagamentoVO);
        }

        Ordenacao.ordenarLista(pagamentoVOS, "dataLancamento");

        for (MovPagamentoVO movPagamentoVO: pagamentoVOS) {
            movPagamentoVO.setValorTotal(movPagamentoVO.getValor());

            List<MovParcelaVO> parcelas = new ArrayList<>();
            movPagamentoVO.getPagamentoMovParcelaVOs().forEach(pmp -> {
                MovParcelaVO movParcelaVO = pmp.getMovParcela();
                movParcelaVO.setSituacao("EA");
                parcelas.add(movParcelaVO);
            });

            incluirPagamentoParcelas(movPagamentoVO, parcelas,contratoVO);
        }
    }

    private void incluirPagamentoParcelas(MovPagamentoVO movPagamentoVO, List<MovParcelaVO> parcelas, ContratoVO contratoVO) throws Exception {
        List<MovPagamentoVO> pagamentoVOS = new ArrayList<>();
        pagamentoVOS.add(movPagamentoVO);
        movPagamentoDAO.incluirListaPagamento(pagamentoVOS, parcelas, null, contratoVO,
                false, 0.0, false, null, null);
    }

    private PaymentTypeEnum obterPaymentType(ReceivablesViewTO receivable) {
        PaymentTypeEnum paymentTypeEnum = null;

        if (!UteisValidacao.emptyNumber(receivable.getPaymentType().getId())) {
            paymentTypeEnum = PaymentTypeEnum.getById(receivable.getPaymentType().getId());
        } else {
            paymentTypeEnum = PaymentTypeEnum.getByName(receivable.getPaymentType().getName());
        }

        if (paymentTypeEnum == null) {
            String descricaoFormaPagamento = receivable.getPaymentType().getName().toLowerCase();
            if (descricaoFormaPagamento.contains("cartão de crédito")
                    || descricaoFormaPagamento.contains("cartão de credito")
                    || descricaoFormaPagamento.contains("cartao de crédito")
                    || descricaoFormaPagamento.contains("cartao de credito")) {
                paymentTypeEnum = PaymentTypeEnum.CREDIT_CARD;
            } else if (descricaoFormaPagamento.contains("cartão de debito")
                    || descricaoFormaPagamento.contains("cartao de débito")
                    || descricaoFormaPagamento.contains("cartão de débito")
                    || descricaoFormaPagamento.contains("cartao de debito")) {
                paymentTypeEnum = PaymentTypeEnum.DEBIT_CARD;
            } else if (descricaoFormaPagamento.contains("cheque")) {
                paymentTypeEnum = PaymentTypeEnum.CHECK;
            } else if (descricaoFormaPagamento.contains("crédito de venda") || descricaoFormaPagamento.contains("credito de venda")) {
                paymentTypeEnum = paymentTypeEnum.SALES_CREDIT;
            } else if (descricaoFormaPagamento.contains("boleto")) {
                paymentTypeEnum = PaymentTypeEnum.BANK_SLIP;
            } else if (descricaoFormaPagamento.contains("pix")){
                paymentTypeEnum = PaymentTypeEnum.PIX;
            } else {
                paymentTypeEnum = PaymentTypeEnum.CASH;
            }
        }
        return paymentTypeEnum;
    }

    private MovPagamentoVO montarMovPagamento(Map<String, MovPagamentoVO> mapaPagamentos, FormaPagamentoVO formaPagamento, PessoaVO pessoaVO, EmpresaVO empresaVO, ReceivablesViewTO receivable) throws Exception {
        Date dataLancamento = UteisImportacao.getDateFromLocalDateTime(receivable.getChargeDate());
        Date dataReceivable = UteisImportacao.getDateFromLocalDateTime(receivable.getReceivingDate());

        if (dataLancamento == null) {
            dataLancamento = UteisImportacao.getDateFromLocalDateTime(receivable.getRegistrationDate());
        }

        String keyPagamento = formaPagamento.getTipoFormaPagamento();
        if (!UteisValidacao.emptyString(receivable.getAuthorization())) {
            keyPagamento += receivable.getAuthorization();
        } else {
            keyPagamento += Calendario.getDataAplicandoFormatacao(dataLancamento, "yyyy-MM-dd");
            if (dataReceivable != null) {
                keyPagamento += Calendario.getDataAplicandoFormatacao(dataReceivable, "yyyy-MM-dd");
            }
        }

        MovPagamentoVO movPagamento = new MovPagamentoVO();
        if (mapaPagamentos.get(keyPagamento) == null) {
            movPagamento.setFormaPagamento(formaPagamento);
            movPagamento.setPessoa(pessoaVO);
            movPagamento.setDataLancamento(dataLancamento);
            movPagamento.setDataPagamento(movPagamento.getDataLancamento());
            movPagamento.setDataQuitacao(movPagamento.getDataLancamento());
            movPagamento.setResponsavelPagamento(cache.getUsuarioVOImportacao());
            movPagamento.setEmpresa(empresaVO);
            movPagamento.setMovPagamentoEscolhida(true);
            movPagamento.setNomePagador(pessoaVO.getNome());
            mapaPagamentos.put(keyPagamento, movPagamento);
        } else {
            movPagamento = mapaPagamentos.get(keyPagamento);
        }

        movPagamento.setValor(movPagamento.getValor() + receivable.getAmmount());
        movPagamento.setValorTotal(movPagamento.getValorTotal() + receivable.getAmmount());

        if (formaPagamento.getTipoFormaPagamento().equals(TipoFormaPagto.CARTAOCREDITO.getSigla())) {
            montarCartaoCredito(movPagamento, receivable);
        } else if (formaPagamento.getTipoFormaPagamento().equals(TipoFormaPagto.CARTAODEBITO.getSigla())) {
            movPagamento.setOperadoraCartaoVO(cache.obterOperadora(receivable.getCardFlag(), false));
            movPagamento.setAdquirenteVO(cache.obterAdquirente(receivable.getCardAcquirer()));
            movPagamento.setAutorizacaoCartao(receivable.getAuthorization());
            movPagamento.setNsu(receivable.getAuthorization());
        } else if (formaPagamento.getTipoFormaPagamento().equals(TipoFormaPagto.CHEQUE.getSigla())) {
            montarCheque(movPagamento, receivable);
        }
        movPagamento.setMovPagamentoEscolhida(true);
        return movPagamento;
    }

    private void montarCheque(MovPagamentoVO movPagamento, ReceivablesViewTO receivable) throws Exception {
        ChequeVO chequeVO = new ChequeVO();
        chequeVO.setBanco(cache.obterBanco(receivable.getBankAccount().getName()));
        chequeVO.setValor(receivable.getAmmount());
        chequeVO.setValorTotal(receivable.getAmmount());
        chequeVO.setConta("");
        chequeVO.setAgencia("");
        chequeVO.setNumero("");
        Date dataCompensacao = UteisImportacao.getDateFromLocalDateTime(receivable.getReceivingDate());
        chequeVO.setDataCompensacao(dataCompensacao);
        if (Calendario.maior(dataCompensacao, movPagamento.getDataLancamento())) {
            chequeVO.setVistaOuPrazo("PR");
        } else {
            chequeVO.setVistaOuPrazo("AV");
        }
        chequeVO.setSituacao("EA");
        movPagamento.getChequeVOs().add(chequeVO);
    }

    private void montarCartaoCredito(MovPagamentoVO movPagamento, ReceivablesViewTO receivable) throws Exception {
        movPagamento.setNrParcelaCartaoCredito(movPagamento.getNrParcelaCartaoCredito() + 1);
        movPagamento.setAutorizacaoCartao(receivable.getAuthorization());
        movPagamento.setNsu(receivable.getAuthorization());
        movPagamento.setAdquirenteVO(cache.obterAdquirente(receivable.getCardAcquirer()));
        movPagamento.setOperadoraCartaoVO(cache.obterOperadora(receivable.getCardFlag(), true));

        CartaoCreditoVO novocc = new CartaoCreditoVO();
        Date dataCompensacao = UteisImportacao.getDateFromLocalDateTime(receivable.getReceivingDate());
        if (dataCompensacao == null) {
            dataCompensacao = UteisImportacao.getDateFromLocalDateTime(receivable.getDueDate());
        }
        novocc.setDataCompensacao(dataCompensacao);
        novocc.setValor(receivable.getAmmount());
        novocc.setSituacao("EA");
        novocc.setOperadora(movPagamento.getOperadoraCartaoVO());
        novocc.setValorTotal(receivable.getAmmount());

        Integer nrParcela = null;
        String descricao = receivable.getDescription();
        if (!UteisValidacao.emptyString(descricao) && descricao.contains("/")) {
            descricao = descricao.trim();
            String descricaoParcelamento = descricao.substring(descricao.lastIndexOf(" "), descricao.length()).trim();
            if (descricaoParcelamento.contains("/")) {
                String[] conteudo = descricaoParcelamento.split("/");
                nrParcela = Integer.valueOf(conteudo[0]);
            }
        }
        if (nrParcela == null){
            nrParcela = !UteisValidacao.emptyNumber(movPagamento.getNrParcelaCartaoCredito())
                    ? movPagamento.getNrParcelaCartaoCredito() + 1 : 1;
        }

        novocc.setNrParcela(nrParcela);

        movPagamento.getCartaoCreditoVOs().add(novocc);
    }

    private PeriodoAcessoClienteVO montarPeriodoAcesso(ContratoVO contratoVO) {
        PeriodoAcessoClienteVO periodoAcesso = new PeriodoAcessoClienteVO();
        periodoAcesso.setPessoa(contratoVO.getPessoa().getCodigo());
        periodoAcesso.setContrato(contratoVO.getCodigo());
        periodoAcesso.setDataInicioAcesso(contratoVO.getVigenciaDe());
        periodoAcesso.setDataFinalAcesso(contratoVO.getVigenciaAteAjustada());
        periodoAcesso.setTipoAcesso("CA");
        periodoAcesso.setContratoBaseadoRenovacao(contratoVO.getContratoBaseadoRenovacao());
        return periodoAcesso;
    }

    public List<Integer> obterListaIds(String idsMembers, int codigoIntegracaoMember) throws Exception {
        if (UteisValidacao.emptyString(idsMembers)) {
            return new ArrayList<>();
        }
        List<Integer> ids = new ArrayList<>();
        for (String id: idsMembers.split(",")) {
            if (id.matches("\\d+") && !ids.contains(Integer.parseInt(id))) {
                ids.add(Integer.parseInt(id));
            }
        }
        if (!UteisValidacao.emptyList(ids) && !importacaoConfigTO.isSincronizarMembersImportados()) {
            this.removerIdsMembersJaExistentes(ids, codigoIntegracaoMember);
        }
        return ids;
    }

    public void processarMembers(IntegracaoMemberVO integracaoMemberVO, List<Integer> idsMembers, boolean importarVendas, boolean importarReceivablesSemVendas) throws Exception {
        if (UteisValidacao.emptyList(idsMembers)) {
            throw new Exception("Nenhuma matricula para processar");
        }

        validarConfiguracoes(integracaoMemberVO);

        int total = idsMembers.size();
        int atual = 0;
        Integer sucesso = 0;
        Integer falha = 0;

        importacaoConfigTO.setTipoImportacaoEnum(TipoImportacaoEnum.MEMBERS_EVO);
        ProcessoImportacaoVO processoVO = processoDAO.iniciarProcessoImportacaoVO(importacaoConfigTO, total);
        processoVO.atualizarJSONStatus(atual, sucesso, falha, total);
        processoDAO.atualizarStatus(processoVO);

        for (Integer idMember : idsMembers) {
            try {
                falhasItemAtual = new ArrayList<>();
                processarMember(integracaoMemberVO, idMember, importarVendas, importarReceivablesSemVendas);

                if (falhasItemAtual.size() > 0) {
                    String msg = String.format("%d foi processado com falhas: \n%s", idMember, falhasItemAtual.stream().map(String::valueOf).collect(Collectors.joining("\n")));
                    ++falha;
                    processoLogDAO.incluir(new ProcessoImportacaoLogVO(processoVO, TipoLogEnum.ERRO, msg));
                } else {
                    processoLogDAO.incluir(new ProcessoImportacaoLogVO(processoVO, TipoLogEnum.SUCESSO, String.format("Matricula: %d - Processada com sucesso", idMember)));
                }
                ++sucesso;
            } catch (Exception ex) {
                handleError(ex, idMember, processoVO);
            } finally {
                processoVO.atualizarJSONStatus(++atual, sucesso, falha, total);
                Uteis.logarDebug(String.format("%d\\%d - Member id: %d processado com sucesso! ", atual + 1, total, idMember));
                processoDAO.atualizarStatus(processoVO);
                processoItemDAO.incluir(new ProcessoImportacaoItemVO(processoVO, new JSONObject().toString()));
                if (Thread.currentThread().isInterrupted()) {
                    JSONObject statusJson = new JSONObject(processoVO.getStatus());
                    statusJson.put("parado", true);
                    processoVO.setStatus(statusJson.toString());
                    processoLogDAO.incluir(new ProcessoImportacaoLogVO(processoVO, TipoLogEnum.SUCESSO, "Parado manualmente"));
                    break;
                }
            }
        }
        processoDAO.finalizarProcessoImportacaoVO(processoVO, importacaoConfigTO);
    }


    private void processarProspects(IntegracaoMemberVO integracaoMemberVO) throws Exception {
        List<ProspectsApiViewTO> prospectsApiViewTOS = getAllProspects(integracaoMemberVO);

        if (UteisValidacao.emptyList(prospectsApiViewTOS)) {
            throw new Exception("Nenhum prospect para processar");
        }

        atualizarIdExternoPessoa();

        int total = prospectsApiViewTOS.size();
        int atual = 0;
        Integer sucesso = 0;
        Integer falha = 0;

        importacaoConfigTO.setTipoImportacaoEnum(TipoImportacaoEnum.PROSPECTS_EVO);
        ProcessoImportacaoVO processoVO = processoDAO.iniciarProcessoImportacaoVO(importacaoConfigTO, total);
        processoVO.atualizarJSONStatus(atual, sucesso, falha, total);
        processoDAO.atualizarStatus(processoVO);

        for (ProspectsApiViewTO prospectsApiViewTO : prospectsApiViewTOS) {
            try {

                processarProspect(prospectsApiViewTO, integracaoMemberVO);
                ++sucesso;
            } catch (Exception ex) {
                handleError(ex, prospectsApiViewTO.getIdProspect(), processoVO);
            } finally {
                processoVO.atualizarJSONStatus(++atual, sucesso, falha, total);
                Uteis.logarDebug(String.format("%d\\%d - Prospect id: %d processado com sucesso!", atual + 1, total, prospectsApiViewTO.getIdProspect()));

                processoDAO.atualizarStatus(processoVO);
                processoItemDAO.incluir(new ProcessoImportacaoItemVO(processoVO, new JSONObject().toString()));
                if (Thread.currentThread().isInterrupted()) {
                    JSONObject statusJson = new JSONObject(processoVO.getStatus());
                    statusJson.put("parado", true);
                    processoVO.setStatus(statusJson.toString());
                    processoLogDAO.incluir(new ProcessoImportacaoLogVO(processoVO, TipoLogEnum.SUCESSO, "Parado manualmente"));
                    break;
                }
            }
        }
        processoDAO.finalizarProcessoImportacaoVO(processoVO, importacaoConfigTO);

    }

    private void atualizarIdExternoPessoa() {
        try {
            SuperFacadeJDBC.executarUpdate("alter table pessoa alter column idexterno type varchar", con);
        } catch (Exception ignore) {}
    }

    public ClienteVO processarMember(IntegracaoMemberVO integracaoMemberVO, Integer idMember, boolean importarVendas, boolean importarReceivablesSemVendas) throws Exception {
        MembersApiViewTO memberApiViewTO = getMember(integracaoMemberVO, idMember);
        if (memberApiViewTO == null || UteisValidacao.emptyNumber(memberApiViewTO.getIdMember())) {
            String msg  = "Falha ao obter o cliente com id " + idMember + " na api";
            falhasItemAtual.add(msg);
            falhas.add(msg);
            return null;
        }
        MemberVO memberNow = memberApiViewTO.toMember();
        memberNow.setIntegracaoMemberVO(integracaoMemberVO);
        MemberVO memberVO = memberDAO.consultarPorIdMember(memberApiViewTO.getIdMember());
        if (memberVO == null) {
            memberDAO.alterar(memberNow);
        } else {
            memberDAO.incluir(memberNow);
        }

        // Cadastro cliente
        ClienteVO clienteVO = processarCliente(memberApiViewTO, integracaoMemberVO);

        if (importarVendas) {
            processarSales(memberApiViewTO, clienteVO, integracaoMemberVO);
        }

        if (importarReceivablesSemVendas) {
            processarRecebimentosSemVendas(memberApiViewTO, clienteVO, integracaoMemberVO);
        }
        if (clienteVO != null && !UteisValidacao.emptyNumber(clienteVO.getCodigo())) {
            clienteVO.setDeveAtualizarDependentesSintetico(true);
            zillyonWebFacadeDAO.atualizarSintetico(clienteVO, Calendario.hoje(), SituacaoClienteSinteticoEnum.GRUPO_TODOS, false);
        }

        return clienteVO;
    }

    private void processarRecebimentosSemVendas(MembersApiViewTO member, ClienteVO clienteVO, IntegracaoMemberVO integracaoMemberVO) throws Exception {
        List<ReceivablesViewTO> receivables = obterReceivables(integracaoMemberVO, member.getIdMember(), true, false, null, null);
        receivables = receivables.stream().filter(r -> UteisValidacao.emptyNumber(r.getIdSale())).collect(Collectors.toList());

        for (ReceivablesViewTO receivable: receivables) {
            if (receivable.getCancellationDate() != null) {
                continue;
            }
            ResultSet rsExiste = SuperFacadeJDBC.criarConsulta("select codigo from movparcela where idexterno = " + receivable.getIdReceivable(), con);
            if (rsExiste.next()) {
                return;
            }

            VendaAvulsa vendaAvulsaDAO = new VendaAvulsa(con);
            ItemVendaAvulsa itemVendaAvulsaDAO = new ItemVendaAvulsa(con);
            Date dataVenda = UteisImportacao.getDateFromLocalDateTime(receivable.getRegistrationDate());

            VendaAvulsaVO vendaAvulsaVO = new VendaAvulsaVO();
            vendaAvulsaVO.setOrigemSistema(OrigemSistemaEnum.ZW);
            vendaAvulsaVO.setTipoComprador("CI");
            vendaAvulsaVO.setCliente(clienteVO);
            vendaAvulsaVO.setDataRegistro(dataVenda);
            vendaAvulsaVO.setEmpresa(clienteVO.getEmpresa());
            vendaAvulsaVO.setNomeComprador(vendaAvulsaVO.getCliente().getPessoa().getNome());
            vendaAvulsaVO.setResponsavel(cache.getUsuarioVOImportacao());

            vendaAvulsaVO.setItemVendaAvulsaVOs(new ArrayList<>());

            ProdutoVO produtoVO = cache.obterProdutoVendaAvulsa("Ajuste negociação", TipoProduto.SERVICO);
            produtoVO.setValorFinal(receivable.getAmmount());
            ItemVendaAvulsaVO item = new ItemVendaAvulsaVO();
            item.setDataVenda(dataVenda);
            item.setQuantidade(1);
            item.setUsuarioVO(cache.getUsuarioVOImportacao());
            item.setProduto(produtoVO);
            vendaAvulsaVO.getItemVendaAvulsaVOs().add(item);

            try (ResultSet rs = vendaAvulsaDAO.incluirSemCommitSomenteVendaAvulsa(vendaAvulsaVO)) {
                if (rs.next()) {
                    for (ItemVendaAvulsaVO itemVendaAvulsaVO : vendaAvulsaVO.getItemVendaAvulsaVOs()) {
                        itemVendaAvulsaDAO.incluir(itemVendaAvulsaVO);
                    }

                    Map<Integer, ReceivablesViewTO> mapaParcelasReceivables = new HashMap<>();
                    mapaParcelasReceivables.put(receivable.getIdReceivable(), receivable);

                    vendaAvulsaVO.setCodigo(rs.getInt("codigo"));
                    gerarParcelasVenda(receivable, clienteVO, vendaAvulsaVO, dataVenda);
                    gerarMovProdutoVendaAvulsa(vendaAvulsaVO, cache.getUsuarioVOImportacao(), "EA", dataVenda);
                    gerarMovprodutoParcela(vendaAvulsaVO.getMovParcelaVOs(), vendaAvulsaVO.getMovProdutoVOs());
                    gerarPagamentoParcelas(vendaAvulsaVO.getMovParcelaVOs(), mapaParcelasReceivables);
                }
            }
        }
    }


    private void processarSales(MembersApiViewTO memberApiViewTO, ClienteVO clienteVO, IntegracaoMemberVO integracaoMemberVO) throws Exception {
        List<Integer> idsSales = obterIdsSales(integracaoMemberVO, memberApiViewTO.getIdMember());
        List<SalesApiViewTO> sales = obterSalesPorIds(integracaoMemberVO, idsSales);;

        Map<Integer, List<MemberMembershipApiViewTO>> mapMemberShipSale = new HashMap<>(); // key = idSale
        Map<Integer, MemberMembershipApiViewTO> mapMemberShip = new HashMap<>(); // key = idMemberMembership
        for (MemberMembershipApiViewTO memberShip: memberApiViewTO.getMemberships()) {
            mapMemberShip.put(memberShip.getIdMemberMembership(), memberShip);
            if (mapMemberShipSale.get(memberShip.getIdSale()) == null) {
                mapMemberShipSale.put(memberShip.getIdSale(), new ArrayList<>());
            }
            mapMemberShipSale.get(memberShip.getIdSale()).add(memberShip);
            // Obter dados de cancelamento\transferencia
            Date cancelDate = UteisImportacao.getDateFromLocalDateTime(memberShip.getCancelDate());
            String status = memberShip.getMembershipStatus().toLowerCase();
            if (cancelDate != null && Calendario.maiorOuIgual(cancelDate, integracaoMemberVO.getDataInicialConsiderarLancamentos())) {

                if (JSFUtilities.isJSFContext()) {
                    memberShip.setMemberShipCancellation(getMemberShipCancellation(integracaoMemberVO, memberShip.getIdMemberMembership(), memberShip.getIdMember(), memberShip.getIdMembership(), cancelDate));
                }
                if (status.equals(MemberShipStatusEnum.TRANSFER.getName().toLowerCase())) {
                    if (memberShip.getMemberShipCancellation() != null && memberShip.getMemberShipCancellation().getMembershipTrasnferData() != null
                            && !UteisValidacao.emptyNumber(memberShip.getMemberShipCancellation().getMembershipTrasnferData().getIdMemberTransfer())) {
                        processarMember(integracaoMemberVO, memberShip.getMemberShipCancellation().getMembershipTrasnferData().getIdMemberTransfer(), false, false);
                    }
                }
            }
        }

        for (MemberMembershipApiViewTO memberShip: memberApiViewTO.getMemberships()) {
            verificarCancelamentoContratos(clienteVO, memberShip, mapMemberShip); // importar cancelamentos gerados no EVO após a ultima importação
        }

        Ordenacao.ordenarLista(sales, "idSale");

        for (SalesApiViewTO sale: sales) {
            processarContratos(clienteVO, sale, mapMemberShipSale, mapMemberShip, integracaoMemberVO);
            processarVendaAulaAvulsaDiaria(clienteVO, sale, mapMemberShipSale, integracaoMemberVO.getModalidadePadrao());
            processarVendaProdutoServico(clienteVO, sale, mapMemberShipSale);
        }
    }

    public void verificarCancelamentoContratos(ClienteVO clienteVO, MemberMembershipApiViewTO memberShip, Map<Integer, MemberMembershipApiViewTO> mapMemberShip) throws Exception {
        ResultSet rs = SuperFacadeJDBC.criarConsulta("select con.codigo from contrato con \n" +
                "where coalesce(id_externo, idexterno) = " + memberShip.getIdMemberMembership() + " \n" +
                "and con.situacao = 'AT' \n" +
                "and not exists (select cp.codigo from contratooperacao cp where cp.contrato = con.codigo and cp.tipooperacao = 'CA')", con);
        if (rs.next()) {
            ContratoVO contratoVO = contratoDAO.consultarPorCodigo(rs.getInt("codigo"), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            String status = memberShip.getMembershipStatus().toLowerCase();
            if (status.equals(MemberShipStatusEnum.CANCELED.getName().toLowerCase()) && !UteisValidacao.emptyString(memberShip.getCancelDate())) {
                Date dataCancelamento = UteisImportacao.getDateFromLocalDateTime(memberShip.getCancelDate());
                if (Calendario.menorOuIgual(dataCancelamento, Calendario.hoje())) {
                    contratoVO.setSituacao("CA");
                    contratoVO.setVigenciaAteAjustada(dataCancelamento);
                }
            }
            processarCancelamento(contratoVO, memberShip);
            if (status.equals(MemberShipStatusEnum.TRANSFER.getName())) {
                processarCessaoDireitoDeUso(clienteVO, contratoVO, memberShip);
                processarCompartilhamentoComDependentes(contratoVO, memberShip.getIdMemberMembership(), mapMemberShip);
            }
        }
    }

    private MemberMemberShipCancellationApiViewTO getMemberShipCancellation(IntegracaoMemberVO integracaoMemberVO, Integer idMemberMemberShip, Integer idMember, Integer idMemberShip, Date cancelDate) throws Exception {
        String url = URL_BASE_V2 + ENDPOINT_MEMBER_MEMBER_SHIP;
        url += "?take=50&skip=0";
        url += "&showTransfers=true";
        url += "&idMembership=" + idMemberShip;
        url += "&idMember=" + idMember;
        url += "&cancelDateStart=" + Calendario.getDataAplicandoFormatacao(cancelDate, "yyyy-MM-dd");
        url += "&cancelDateEnd=" + Calendario.getDataAplicandoFormatacao(cancelDate, "yyyy-MM-dd");

        HttpGet request = new HttpGet(url);
        request.addHeader("User-Agent", userAgents.get(new Random().nextInt(userAgents.size())));
        System.out.println("\t" + url);

        CredentialsProvider provider = new BasicCredentialsProvider();
        provider.setCredentials(AuthScope.ANY, integracaoMemberVO.getCredentials());

        try (CloseableHttpClient httpClient = HttpClientBuilder.create()
                .setDefaultCredentialsProvider(provider)
                .build();
             CloseableHttpResponse response = httpClient.execute(request)) {

            HttpEntity entity = response.getEntity();
            if (entity != null) {
                try {
                    String result = EntityUtils.toString(entity);
                    List<MemberMemberShipCancellationApiViewTO> memberShipsCacellations = JSONMapper.getList(new JSONArray(result), MemberMemberShipCancellationApiViewTO.class);
                    Optional<MemberMemberShipCancellationApiViewTO> memberShipCancelation = memberShipsCacellations.stream()
                            .filter(msc -> msc.getIdMemberMemberShip().equals(idMemberMemberShip))
                            .findFirst();

                    return memberShipCancelation.isPresent() ? memberShipCancelation.get() : null;
                } catch (Exception e) {
                    String msg = String.format("Falha ao tentar obter cancelamentos: idMember: %d | idMemberMemberShip: %d - erro: %s", idMember, idMemberMemberShip, e.getMessage());
                    msg += " | url: " + url;
                    falhasItemAtual.add(msg);
                    falhas.add(msg);
                }
            }
        }
        return null;
    }

    public void processarRecebimentosParcelasPorIdExternoMovParcela(IntegracaoMemberVO integracaoMemberVO, List<Integer> matriculas) throws Exception {
        int total = matriculas.size();
        int atual = 0;
        Integer sucesso = 0;
        Integer falha = 0;

        if (total == 0) {
            throw new Exception("Nenhuma matricula para processar");
        }

        ProcessoImportacaoVO processoVO = processoDAO.iniciarProcessoImportacaoVO(importacaoConfigTO, total);
        processoVO.setTipoImportacaoEnum(TipoImportacaoEnum.MEMBERS_EVO_RECEBIMENTOS_PARCELAS);
        processoVO.atualizarJSONStatus(atual, sucesso, falha, total);
        processoDAO.atualizarStatus(processoVO);

        for (Integer matricula : matriculas) {
            try {
                ClienteVO clienteVO = clienteDAO.consultarPorCodigoMatriculaExterna(matricula, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                if (clienteVO == null || UteisValidacao.emptyNumber(clienteVO.getCodigo())) {
                    throw new Exception("Matricula: " + matricula + " - Cliente não encontrado");
                }

                String sql = "SELECT mpar.codigo, cli.codigomatricula, coalesce(con.observacao,'') as observacaocontrato, coalesce(coalesce(con.id_externo, con.idexterno),0) as idclientecontrato FROM movparcela mpar \n" +
                        "INNER JOIN cliente cli ON cli.pessoa = mpar.pessoa \n" +
                        "LEFT JOIN contrato con ON con.codigo = mpar.contrato \n" +
                        "LEFT JOIN vendaavulsa va ON va.codigo = mpar.vendaavulsa \n" +
                        "LEFT JOIN aulaavulsadiaria a ON a.codigo = mpar.aulaavulsadiaria \n" +
                        "WHERE mpar.situacao = 'EA' \n" +
                        "AND cli.matriculaexterna = " + matricula + " \n" +
                        "AND (" +
                        "   coalesce(mpar.idexterno) > 0" +
                        "   OR coalesce(con.id_externo,con.idexterno) > 0 " +
                        "   OR coalesce(va.id_movimento,0) > 0" +
                        "   OR coalesce(a.id_venda, a.idexternoitemvenda) > 0) \n" +
                        "ORDER BY mpar.codigo";

                ResultSet rs = SuperFacadeJDBC.criarConsulta(sql, con);


                List<String> falhas = new ArrayList<>();
                List<String> sucessos = new ArrayList<>();

                int totalParcelas = SuperFacadeJDBC.contar("select count(sql.*) from (" + sql + ") sql", con);
                if (totalParcelas == 0) {
                    sucessos.add(String.format( "Matricula: %d %s  - Nenhuma parcela em aberto originada da importação foi encontrada para processar", matricula, clienteVO.getPessoa().getNome()));
                }

                Map<Integer, MembersApiViewTO> mapMembers = new HashMap<>();
                Map<Integer, List<ReceivablesViewTO>> mapMemberReceivables = new HashMap<>();

                while (rs.next()) {
                    Integer idclientecontrato = rs.getInt("idclientecontrato");
                    Integer codigoParcela = rs.getInt("codigo");
                    String observacaoContrato = rs.getString("observacaocontrato").toLowerCase();
                    boolean isContratoRecorrenteProximasCobrancasGeradasNaPacto = observacaoContrato.contains("meses adicionados próximas cobranças evo");

                    MovParcelaVO movParcelaVO = movParcelaDAO.consultarPorCodigo(codigoParcela, Uteis.NIVELMONTARDADOS_DADOSBASICOS);

                    if (UteisValidacao.emptyNumber(movParcelaVO.getIdExterno()) && !isContratoRecorrenteProximasCobrancasGeradasNaPacto) {
                        falhas.add(getMsgLogRecebimentoParcela(movParcelaVO,  "Falha! Essa parcela não possui idexterno para consultar na API Evo", null));
                        continue;
                    }

                    List<BoletoVO> boletos = boletoDAO.consultarPorMovParcela(movParcelaVO.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                    if (!UteisValidacao.emptyList(boletos)) {
                        falhas.add(getMsgLogRecebimentoParcela(movParcelaVO,  "Falha! Essa parcela possui boletos gerados", null));
                        continue;
                    }

                    Map<String, String> params = new HashMap<>();
                    params.put("memberId", matricula.toString());
                    List<ReceivablesViewTO> receivablesViewTOS;
                    if (mapMemberReceivables.get(matricula) == null) {
                        receivablesViewTOS = obterReceivablesByFilters(integracaoMemberVO, params);
                        mapMemberReceivables.put(matricula, receivablesViewTOS);
                    } else {
                        receivablesViewTOS = mapMemberReceivables.get(matricula);
                    }

                    ReceivablesViewTO receivablesViewTO = null;
                    Integer idReceivable = movParcelaVO.getIdExterno();
                    if (!UteisValidacao.emptyNumber(idReceivable)) {
                        receivablesViewTO = obterReceivableById(receivablesViewTOS, idReceivable);
                    }

                    if (receivablesViewTO == null) {
                        try {
                            receivablesViewTO = obterReceivablePorContratoParcelaMesReferencia(matricula, idclientecontrato, receivablesViewTOS, mapMembers, movParcelaVO, observacaoContrato, integracaoMemberVO);
                        } catch (Exception e) {
                            if (e.getMessage() != null && e.getMessage().contains("contrato desse mes ainda nao foi gerado")) {
                                sucessos.add(getMsgLogRecebimentoParcela(movParcelaVO, e.getMessage(), receivablesViewTO));
                            } else {
                                falhas.add(getMsgLogRecebimentoParcela(movParcelaVO,  "Falha! " + e.getMessage(), receivablesViewTO));
                            }
                            continue;
                        }
                    }

                    if (receivablesViewTO == null) {
                        falhas.add(getMsgLogRecebimentoParcela(movParcelaVO,  "Falha! Parcela não encontrado na API Evo", receivablesViewTO));
                        continue;
                    }

                    StatusRecevablesEnum statusRecevablesEnum = StatusRecevablesEnum.obterPorId(receivablesViewTO.getStatus().getId());
                    if (statusRecevablesEnum == null) {
                        falhas.add(getMsgLogRecebimentoParcela(movParcelaVO,  "Falha ao obter status do recebivel na API Evo", receivablesViewTO));
                        continue;
                    }

                    if (statusRecevablesEnum.getId().equals(StatusRecevablesEnum.CANCELED.getId())) {
                        MembersApiViewTO member = mapMembers.get(matricula);
                        if (member == null) {
                            member = getMember(integracaoMemberVO, matricula);
                            mapMembers.put(matricula, member);
                        }
                        ReceivablesViewTO proximoReceivable = obterProximoReceivableNaoCanceladoContratoMensal(member, receivablesViewTO.getIdReceivable(), receivablesViewTO.getIdSale(), receivablesViewTOS);
                        if (proximoReceivable != null) {
                            receivablesViewTO = proximoReceivable;
                        }
                    }

                    if (receivablesViewTO.getAmmount() <= 0.0) {
                        falhas.add(getMsgLogRecebimentoParcela(movParcelaVO,  "Receivable EVO com valor ou menor que zero", receivablesViewTO));
                        continue;
                    }

                    movParcelaVO.setIdExterno(receivablesViewTO.getIdReceivable());

                    if (statusRecevablesEnum.getId().equals(StatusRecevablesEnum.RECEIVED.getId())
                            || !UteisValidacao.emptyString(receivablesViewTO.getAuthorization())) {
                        if (Uteis.arredondarForcando2CasasDecimais(receivablesViewTO.getAmmount()) != Uteis.arredondarForcando2CasasDecimais(movParcelaVO.getValorParcela())) {
                            IntegracaoImportacao integracaoImportacao = new IntegracaoImportacao(con);
                            movParcelaVO = integracaoImportacao.renegociarParcela(movParcelaVO, receivablesViewTO.getAmmount(), this.cache.getUsuarioVOImportacao());
                        }
                        movParcelaVO.setSituacao("PG");
                        List<MovParcelaVO> movParcelaVOS = new ArrayList<>();
                        movParcelaVOS.add(movParcelaVO);
                        Map<Integer, ReceivablesViewTO> mapReceivables = new HashMap<>(); // key = idReceivable
                        mapReceivables.put(receivablesViewTO.getIdReceivable(), receivablesViewTO);
                        gerarPagamentoParcelas(movParcelaVO.getContrato(), movParcelaVOS, mapReceivables);
                        SuperFacadeJDBC.executarUpdate("update movparcela set idexterno = " + receivablesViewTO.getIdReceivable()
                                + " where codigo = " + movParcelaVO.getCodigo(), con);
                        sucessos.add(getMsgLogRecebimentoParcela(movParcelaVO,"Paga com sucesso!", receivablesViewTO));
                    } else if (statusRecevablesEnum.getId().equals(StatusRecevablesEnum.CANCELED.getId())) {
                        falhas.add(getMsgLogRecebimentoParcela(movParcelaVO, "Falha! Essa parcela está CANCELADA no EVO! ", receivablesViewTO));
                    } else {
                        sucessos.add(getMsgLogRecebimentoParcela(movParcelaVO, "Está em aberto no EVO! ", receivablesViewTO));
                    }
                }

                TipoLogEnum tipoLogEnum;
                if (falhas.size() > 0 || sucessos.size() == 0) {
                    tipoLogEnum = TipoLogEnum.ERRO;
                    ++falha;
                } else {
                    tipoLogEnum = TipoLogEnum.SUCESSO;
                    ++sucesso;
                }

                String log = String.format( "Matricula: %d %s - Resultados: \n", matricula, clienteVO.getPessoa().getNome());
                log += falhas.stream().map(String::valueOf).collect(Collectors.joining("\n"));
                log += "\n" + sucessos.stream().map(String::valueOf).collect(Collectors.joining("\n"));

                processoLogDAO.incluir(new ProcessoImportacaoLogVO(processoVO, tipoLogEnum, log));
            } catch (Exception ex) {
                ex.printStackTrace();
                ++falha;
                String erro = UteisValidacao.emptyString(ex.getMessage()) ? ex.toString() : ex.getMessage();
                String msgErro = String.format("Matricula: %d - Falha ao processar: %s", matricula, erro);
                processoLogDAO.incluir(new ProcessoImportacaoLogVO(processoVO, TipoLogEnum.ERRO, msgErro));
            } finally {
                processoVO.atualizarJSONStatus(++atual, sucesso, falha, total);
                processoDAO.atualizarStatus(processoVO);
                processoItemDAO.incluir(new ProcessoImportacaoItemVO(processoVO, new JSONObject().toString()));
                if (Thread.currentThread().isInterrupted()) {
                    JSONObject statusJson = new JSONObject(processoVO.getStatus());
                    statusJson.put("parado", true);
                    processoVO.setStatus(statusJson.toString());
                    processoLogDAO.incluir(new ProcessoImportacaoLogVO(processoVO, TipoLogEnum.SUCESSO, "Parado manualmente"));
                    break;
                }
            }
        }
        processoDAO.finalizarProcessoImportacaoVO(processoVO, importacaoConfigTO);
    }

    private ReceivablesViewTO obterProximoReceivableNaoCanceladoContratoMensal(final MembersApiViewTO membersApiViewTO, final Integer idReceivableBase, final Integer idSale, List<ReceivablesViewTO> receivablesViewTOS) {
        if (membersApiViewTO == null || membersApiViewTO.getMemberships().isEmpty()
                || UteisValidacao.emptyNumber(idReceivableBase)
                || UteisValidacao.emptyNumber(idSale)
                || UteisValidacao.emptyList(receivablesViewTOS)) {
            return null;
        }
        List<MemberMembershipApiViewTO> contratosEvo = membersApiViewTO.getMemberships().stream()
                .filter(c -> c.getIdSale().equals(idSale)).collect(Collectors.toList());
        if (!UteisValidacao.emptyList(contratosEvo)) {
            Long dias = Uteis.nrDiasEntreDatas(contratosEvo.get(0).getDataInicio(), contratosEvo.get(0).getDataFim());
            if (dias <= DIAS_MAXIMO_CONTRATO_MENSAL) {
                List<ReceivablesViewTO> receivablesSeguintes = receivablesViewTOS.stream()
                        .filter(r -> r.getIdSale().equals(idSale)
                                && r.getIdReceivable() > idReceivableBase
                                && !r.getStatus().getId().equals(StatusRecevablesEnum.CANCELED.getId()))
                        .collect(Collectors.toList());
                if (!UteisValidacao.emptyList(receivablesSeguintes) && receivablesSeguintes.size() == 1) {
                    return receivablesSeguintes.get(0);
                }
            }
        }
        return null;
    }

    private ReceivablesViewTO obterReceivablePorContratoParcelaMesReferencia(Integer matricula, Integer idclientecontrato, List<ReceivablesViewTO> receivablesViewTOS, Map<Integer, MembersApiViewTO> mapMembers, MovParcelaVO movParcelaVO, String observacaoContrato, IntegracaoMemberVO integracaoMemberVO) throws Exception {
        ReceivablesViewTO receivablesViewTO = null;

        final double valorParcela = Uteis.arredondarForcando2CasasDecimais(movParcelaVO.getValorParcela());
        final Date vencimentoParcela = movParcelaVO.getDataVencimento();
        final Date primeiroDiaMesVencimento = Calendario.primeiroDiaMesDate(vencimentoParcela);
        final Date ultimoDiaMesVencimento = Calendario.ultimoDiaMesDate(vencimentoParcela);

        MemberMembershipApiViewTO memberMembershipApiViewTO = null;
        AtomicInteger idSale = new AtomicInteger(0);
        List<ReceivablesViewTO> receivablesFiltered = new ArrayList<>();

        if (mapMembers.get(matricula) == null) {
            mapMembers.put(matricula, getMember(integracaoMemberVO, matricula));
        }
        MembersApiViewTO member = mapMembers.get(matricula);

        if (observacaoContrato.contains("plano evo:")) {
            Integer idPlanoEvo = Integer.parseInt(observacaoContrato.split("plano evo:")[1].split("-")[0].trim());

            if (member != null) {
                Optional<MemberMembershipApiViewTO> mmsOptional = member.getMemberships().stream()
                        .filter(mms -> mms != null && !UteisValidacao.emptyNumber(mms.getIdMembership()) && mms.getIdMembership().equals(idPlanoEvo)
                                && mms.getDataInicio() != null && Calendario.maiorOuIgual(mms.getDataInicio(), Calendario.primeiroDiaMesDate(vencimentoParcela)))
                        .findFirst();
                memberMembershipApiViewTO = mmsOptional.isPresent() ? mmsOptional.get() : null;

                if (memberMembershipApiViewTO != null && !UteisValidacao.emptyNumber(memberMembershipApiViewTO.getIdSale())) {
                    idSale.set(memberMembershipApiViewTO.getIdSale());
                    receivablesFiltered = receivablesViewTOS.stream()
                            .filter(r -> {
                                try {
                                    boolean dtCompentenciaEstaNoMesVencimentoParcela = false;
                                    if (!UteisValidacao.emptyString(r.getCompetenceDate())) {
                                        Date competenceDate = Calendario.getDate("yyyy-MM-dd", r.getCompetenceDate());
                                        dtCompentenciaEstaNoMesVencimentoParcela = Calendario.maiorOuIgual(competenceDate, primeiroDiaMesVencimento) && Calendario.menorOuIgual(competenceDate, ultimoDiaMesVencimento);
                                    }
                                    return dtCompentenciaEstaNoMesVencimentoParcela
                                            && !UteisValidacao.emptyNumber(r.getIdSale())
                                            && r.getIdSale().equals(idSale.get())
                                            && Uteis.arredondarForcando2CasasDecimais(r.getAmmount()) == Uteis.arredondarForcando2CasasDecimais(valorParcela);
                                } catch (Exception e) {
                                    e.printStackTrace();
                                }
                                return false;
                            }).collect(Collectors.toList());
                }
                if (UteisValidacao.emptyList(receivablesFiltered)) {
                    throw new Exception("Parcela gerada na pacto (processo incluir meses restantes proximas cobrancas) nao foi encontrada na API, pois o contrato desse mes ainda nao foi gerado");
                }
            }
        } else {
            Optional<MemberMembershipApiViewTO> mmsOptional = member.getMemberships().stream().filter(mms -> mms.getIdMemberMembership().equals(idclientecontrato)).findFirst();
            if (mmsOptional.isPresent()) {
                memberMembershipApiViewTO = mmsOptional.isPresent() ? mmsOptional.get() : null;
                idSale.set(memberMembershipApiViewTO.getIdSale());
            }
        }

        if (UteisValidacao.emptyList(receivablesFiltered)) {
            receivablesFiltered = receivablesViewTOS.stream()
                    .filter(r -> {
                        try {
                            boolean dtCompentenciaEstaNoMesVencimentoParcela = false;
                            if (!UteisValidacao.emptyString(r.getCompetenceDate())) {
                                Date competenceDate = Calendario.getDate("yyyy-MM-dd", r.getCompetenceDate());
                                dtCompentenciaEstaNoMesVencimentoParcela = Calendario.maiorOuIgual(competenceDate, primeiroDiaMesVencimento) && Calendario.menorOuIgual(competenceDate, ultimoDiaMesVencimento);
                            }
                            boolean dueDateSaldoDevedorEstaNoMesVencimentoParcela = false;
                            if (!UteisValidacao.emptyString(r.getDueDate()) && r.getDescription().toLowerCase().contains("saldo devedor")) {
                                Date dueDate = Calendario.getDate("yyyy-MM-dd", r.getDueDate());
                                dtCompentenciaEstaNoMesVencimentoParcela = Calendario.maiorOuIgual(dueDate, primeiroDiaMesVencimento) && Calendario.menorOuIgual(dueDate, ultimoDiaMesVencimento);
                            }
                            boolean isIdSaleValidado = !UteisValidacao.emptyNumber(idSale.get()) && !UteisValidacao.emptyNumber(r.getIdSale())
                                    ?  r.getIdSale().equals(idSale.get()) : true;
                            return Uteis.arredondarForcando2CasasDecimais(r.getAmmount()) == Uteis.arredondarForcando2CasasDecimais(valorParcela)
                                    && (dtCompentenciaEstaNoMesVencimentoParcela || dueDateSaldoDevedorEstaNoMesVencimentoParcela)
                                    && isIdSaleValidado;
                        } catch (ParseException e) {
                            e.printStackTrace();
                        }
                        return false;
                    }).collect(Collectors.toList());
        }
        if (!UteisValidacao.emptyList(receivablesFiltered)) {
            if (receivablesViewTOS.size() == 1) {
                receivablesViewTO = receivablesViewTOS.get(0);
                movParcelaVO.setIdExterno(receivablesViewTO.getIdReceivable());
            } else {
                for (ReceivablesViewTO r: receivablesFiltered) {
                    receivablesViewTO = obterReceivableByIdReceivableFrom(receivablesViewTOS, r.getIdReceivable());
                    if (receivablesViewTO != null) {
                        movParcelaVO.setIdExterno(receivablesViewTO.getIdReceivable());
                        break;
                    }
                }
                if (receivablesViewTO == null) {
                    receivablesFiltered = receivablesFiltered.stream()
                            .filter(r -> {
                                try {
                                    return !UteisValidacao.emptyString(r.getCompetenceDate())
                                            ? Calendario.igual(Calendario.getDate("yyyy-MM-dd", r.getCompetenceDate()), vencimentoParcela)
                                            && !r.getStatus().getId().equals(StatusRecevablesEnum.CANCELED.getId()) : false;
                                } catch (ParseException e) {
                                    Uteis.logarDebug("Falha ao converter data: " + r.getCompetenceDate());
                                }
                                return false;
                            }).collect(Collectors.toList());
                    if (receivablesFiltered.isEmpty() && memberMembershipApiViewTO != null && idSale.get() > 0) {
                        boolean contratoMensal = Uteis.nrDiasEntreDatas(memberMembershipApiViewTO.getDataInicio(), memberMembershipApiViewTO.getDataFim()) <= 30;
                        if (contratoMensal) {
                            List<ReceivablesViewTO> receivablesSaleCancelados = receivablesViewTOS.stream().filter(r -> r.getIdSale().equals(idSale.get())
                                            && !r.getStatus().getId().equals(StatusRecevablesEnum.CANCELED.getId())
                                            && Uteis.arredondarForcando2CasasDecimais(r.getAmmount()) == Uteis.arredondarForcando2CasasDecimais(valorParcela))
                                    .collect(Collectors.toList());
                            List<ReceivablesViewTO> receivablesSale = receivablesViewTOS.stream().filter(r -> r.getIdSale().equals(idSale.get())
                                            && !r.getStatus().getId().equals(StatusRecevablesEnum.CANCELED.getId())
                                            && Uteis.arredondarForcando2CasasDecimais(r.getAmmount()) == Uteis.arredondarForcando2CasasDecimais(valorParcela))
                                    .collect(Collectors.toList());
                            if (!receivablesSale.isEmpty() && receivablesSale.size() == 1 && !receivablesSaleCancelados.isEmpty()) {
                                receivablesViewTO = receivablesSale.get(0); // recebimento original foi cancelado e gerado outro
                            }
                        }
                    }
                    if (receivablesFiltered.size() == 1) {
                        receivablesViewTO = receivablesFiltered.get(0);
                        movParcelaVO.setIdExterno(receivablesViewTO.getIdReceivable());
                    } else if (receivablesFiltered.size() > 1) {

                        StringBuilder sb = new StringBuilder();
                        receivablesViewTOS.forEach(r -> sb.append(String.format("idReceivable: %s description: %s ammount: %s competenceDate: %s autorization: %s",
                                r.getIdReceivable(), r.getDescription(), r.getAmmount(), r.getCompetenceDate(), r.getAuthorization())));
                        throw new Exception(String.format("Falha! Mais de um recebimento foi encontrado no EVO! Recebimentos: (%s)", sb.toString()));
                    }
                }
            }
        }
        return receivablesViewTO;
    }


    private ReceivablesViewTO obterReceivableById(List<ReceivablesViewTO> receivablesViewTOS, Integer idReceivable) {
        ReceivablesViewTO receivablesViewTO;
        Optional<ReceivablesViewTO> rOptional = receivablesViewTOS.stream()
                .filter(r -> r.getIdReceivable().equals(idReceivable)).findFirst();
        receivablesViewTO = rOptional.isPresent() ? rOptional.get() : null;
        if (receivablesViewTO == null || receivablesViewTO.getStatus().getId().equals(StatusRecevablesEnum.CANCELED.getId())) {
            receivablesViewTO = obterReceivableByIdReceivableFrom(receivablesViewTOS, idReceivable);
        }
        return receivablesViewTO;
    }

    private ReceivablesViewTO obterReceivableByIdReceivableFrom(List<ReceivablesViewTO> receivablesViewTOS, Integer idReceivable) {
        Optional<ReceivablesViewTO> rOptional = receivablesViewTOS.stream().filter(r -> idReceivable.equals(r.getIdReceivableFrom())).findFirst();
        return rOptional.isPresent() ? rOptional.get() : null;
    }



    private String getMsgLogRecebimentoParcela(MovParcelaVO movParcelaVO, String resultado, ReceivablesViewTO receivablesViewTO) {
        String msgLog = String.format("%d %s %s %s R$ %s - %s", movParcelaVO.getCodigo(), movParcelaVO.getDescricao(), Calendario.getDataAplicandoFormatacao(movParcelaVO.getDataVencimento(), "dd/MM/yyyy"),
                movParcelaVO.getSituacao(), Uteis.arredondarForcando2CasasDecimais(movParcelaVO.getValorParcela()), resultado);
        if (receivablesViewTO != null) {
            msgLog = String.format("%s receivable(%s %s %s R$ %s %s) ", msgLog, receivablesViewTO.getIdReceivable(),
                    receivablesViewTO.getDescription(), receivablesViewTO.getStatus().getName(),
                    Uteis.arredondarForcando2CasasDecimais(receivablesViewTO.getAmmount()),
                    receivablesViewTO.getAuthorization() != null ? receivablesViewTO.getAuthorization() : "");
        }
        return msgLog;
    }

    private MovParcelaVO obterMovParcelaImportadaPorIdExterno(ReceivablesViewTO receivable) throws Exception {
        String sql = "select mpar.codigo, mpar.contrato from movparcela mpar \n" +
                "inner join cliente cli on cli.pessoa = mpar.pessoa  \n" +
                "where 1 = 1 \n" +
                "and mpar.idexterno = " + receivable.getIdReceivable() + " \n";
        sql += " order by mpar.datavencimento ";
        ResultSet rsParcela = SuperFacadeJDBC.criarConsulta(sql, con);
        if (rsParcela.next()) {
            return movParcelaDAO.consultarPorCodigo(rsParcela.getInt("codigo"), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        }
        return null;
    }

    private List<MovParcelaVO> obterMovParcelaImportadaPorDataValor(Integer idMember, ReceivablesViewTO receivable) throws Exception {
        String sql = "select mpar.codigo, mpar.contrato from movparcela mpar \n" +
                "inner join cliente cli on cli.pessoa = mpar.pessoa  \n" +
                "left join contrato con on con.codigo = mpar.contrato  \n" +
                "left join vendaavulsa va on va.codigo = mpar.vendaavulsa  \n" +
                "where 1 = 1 \n" +
                "and cli.matriculaexterna = " + idMember + " \n" +
                "and (coalesce(con.id_externo,con.idexterno) > 0 OR coalesce(va.id_movimento,0) > 0) \n";
        sql += " and (\n";

        if (!UteisValidacao.emptyString(receivable.getCancellationDate())) {
            sql += "mpar.datavencimento = '" + receivable.getCompetenceDate() + "'::date \n";
        } else {
            if (!UteisValidacao.emptyString(receivable.getChargeDate())) {
                sql += "mpar.datavencimento = '" + receivable.getChargeDate() + "'::date \n";
                sql += " or ";
            }
            sql += " mpar.datavencimento = '" + receivable.getDueDate() + "'::date \n";
        }
        sql += ") \n";
        sql += " and trunc(mpar.valorparcela::numeric,2) = trunc(" + receivable.getAmmount() + "::numeric,2) \n";
        sql += " order by mpar.datavencimento ";
        ResultSet rsParcela = SuperFacadeJDBC.criarConsulta(sql, con);

        List<MovParcelaVO> movParcelas = new ArrayList<>();
        while (rsParcela.next()) {
            MovParcelaVO movParcelaVO = movParcelaDAO.consultarPorCodigo(rsParcela.getInt("codigo"), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            movParcelas.add(movParcelaVO);
        }

        return movParcelas;
    }

    private ReceivablesViewTO obterReceivableOriginal(IntegracaoMemberVO integracaoMemberVO, Integer idMember, ReceivablesViewTO receivable) throws Exception {
        List<ReceivablesViewTO> receivablesViewTOS;
        if (mapaSalesReceivables.get(receivable.getIdSale()) != null) {
            receivablesViewTOS = mapaSalesReceivables.get(receivable.getIdSale());
        } else {
            receivablesViewTOS = obterReceivables(integracaoMemberVO, idMember, false, false, receivable.getIdSale(), null);
        }

        List<ReceivablesViewTO> receivablesEncontrados = new ArrayList<>();
        for (ReceivablesViewTO r: receivablesViewTOS) {
            Date cancelledDate = UteisImportacao.getDateFromLocalDateTime(r.getCancellationDate());
            Date competenceDate = UteisImportacao.getDateFromLocalDateTime(r.getCompetenceDate());
            Date competenceDateAtual = UteisImportacao.getDateFromLocalDateTime(receivable.getCompetenceDate());
            if (cancelledDate != null
                    && competenceDate != null && competenceDateAtual != null
                    && Calendario.dataNoMesmoMes(competenceDate, competenceDateAtual)
                    && Calendario.dataNoMesmoMesAno(competenceDate, competenceDateAtual)
                    && r.getIdSale().equals(receivable.getIdSale())
                    && r.getAmmount().equals(receivable.getAmmount())) {
                receivablesEncontrados.add(r);
            }
        }
        if (UteisValidacao.emptyList(receivablesEncontrados)) {
            for (ReceivablesViewTO r : receivablesViewTOS) {
                Date cancelledDate = UteisImportacao.getDateFromLocalDateTime(r.getCancellationDate());
                Date updateDate = UteisImportacao.getDateFromLocalDateTime(r.getUpdateDate());
                Date registrationDateAtual = UteisImportacao.getDateFromLocalDateTime(receivable.getRegistrationDate());
                if (cancelledDate != null
                        && Calendario.dataNoMesmoMes(updateDate, registrationDateAtual)
                        && Calendario.dataNoMesmoMesAno(updateDate, registrationDateAtual)
                        && r.getIdSale().equals(receivable.getIdSale())
                        && r.getAmmount().equals(receivable.getAmmount())) {
                    receivablesEncontrados.add(r);
                }
            }
        }
        return receivablesEncontrados.size() == 1 ? receivablesEncontrados.get(0) : null;
    }

    private void processarVendaProdutoServico(ClienteVO clienteVO, SalesApiViewTO sale, Map<Integer, List<MemberMembershipApiViewTO>> mapMemberShipSale) throws Exception {
        ResultSet rsVendaJaImportada = SuperFacadeJDBC.criarConsulta("select codigo from vendaavulsa where id_movimento = '" + sale.getIdSale() + "'", con);
        if (rsVendaJaImportada.next()) {
            return;
        }

        List<MemberMembershipApiViewTO> listMemberShips = mapMemberShipSale.get(sale.getIdSale());

        Integer idMemberShipReceivables = !UteisValidacao.emptyList(listMemberShips) ? obterIdMemberShipReceivables(listMemberShips, sale.getSaleItens()) : 0;


        boolean importarPagamentos = UteisValidacao.emptyNumber(idMemberShipReceivables);

        VendaAvulsa vendaAvulsaDAO = new VendaAvulsa(con);
        ItemVendaAvulsa itemVendaAvulsaDAO = new ItemVendaAvulsa(con);
        Date dataVenda = UteisImportacao.getDateFromLocalDateTime(sale.getSaleDate());

        VendaAvulsaVO vendaAvulsaVO = new VendaAvulsaVO();
        vendaAvulsaVO.setOrigemSistema(OrigemSistemaEnum.ZW);
        vendaAvulsaVO.setTipoComprador("CI");
        vendaAvulsaVO.setCliente(clienteVO);
        vendaAvulsaVO.setDataRegistro(dataVenda);
        vendaAvulsaVO.setEmpresa(clienteVO.getEmpresa());
        vendaAvulsaVO.setNomeComprador(vendaAvulsaVO.getCliente().getPessoa().getNome());
        vendaAvulsaVO.setResponsavel(cache.getUsuarioVOImportacao());

        vendaAvulsaVO.setItemVendaAvulsaVOs(new ArrayList<>());

        for (SaleItemApiViewTO saleItem: sale.getSaleItens()) {
            TipoProduto tipoProduto;
            String descricao;
            if (!UteisValidacao.emptyNumber(saleItem.getIdService())) {
                tipoProduto = TipoProduto.SERVICO;
                descricao = UteisValidacao.emptyString(saleItem.getItem()) ? "Importação - Serviço" : saleItem.getItem();
            } else if (!UteisValidacao.emptyNumber(saleItem.getIdProduct())) {
                tipoProduto = TipoProduto.PRODUTO_ESTOQUE;
                descricao = "Importação - Produto";
            } else {
                continue;
            }
            ProdutoVO produtoVO = cache.obterProdutoVendaAvulsa(descricao, tipoProduto);
            produtoVO.setValorFinal(importarPagamentos ? saleItem.getItemValue() : 0.0);
            ItemVendaAvulsaVO item = new ItemVendaAvulsaVO();
            item.setDataVenda(dataVenda);
            item.setQuantidade(saleItem.getQuantity());
            item.setUsuarioVO(cache.getUsuarioVOImportacao());
            item.setProduto(produtoVO);
            vendaAvulsaVO.getItemVendaAvulsaVOs().add(item);
        }
        if (UteisValidacao.emptyList(vendaAvulsaVO.getItemVendaAvulsaVOs())) {
            return;
        }

        if (!importarPagamentos) {
            Integer codigo = vendaAvulsaDAO.incluirSemCommit(vendaAvulsaVO, true, dataVenda);
            preencherIdVendaVendaAvulsa(sale.getIdSale(), codigo, con);
        } else {
            try (ResultSet rs = vendaAvulsaDAO.incluirSemCommitSomenteVendaAvulsa(vendaAvulsaVO)) {
                if (rs.next()) {
                    preencherIdVendaVendaAvulsa(sale.getIdSale(), rs.getInt("codigo"), con);

                    Map<Integer, ReceivablesViewTO> mapReceivables = new HashMap<>();
                    sale.getReceivables().forEach(r -> mapReceivables.put(r.getIdReceivable(), r));

                    for (ItemVendaAvulsaVO itemVendaAvulsaVO : vendaAvulsaVO.getItemVendaAvulsaVOs()) {
                        itemVendaAvulsaDAO.incluir(itemVendaAvulsaVO);
                    }
                    vendaAvulsaVO.setCodigo(rs.getInt("codigo"));
                    gerarParcelasVenda(sale.getReceivables(), clienteVO, null, vendaAvulsaVO, dataVenda);
                    gerarMovProdutoVendaAvulsa(vendaAvulsaVO, cache.getUsuarioVOImportacao(), "EA", dataVenda);
                    gerarMovprodutoParcela(vendaAvulsaVO.getMovParcelaVOs(), vendaAvulsaVO.getMovProdutoVOs());
                    gerarPagamentoParcelas(vendaAvulsaVO.getMovParcelaVOs(), mapReceivables);

                    if (UteisValidacao.emptyList(vendaAvulsaVO.getMovParcelaVOs())) {
                        SuperFacadeJDBC.executarUpdate("update movproduto set situacao = 'CA' where vendaavulsa = " + vendaAvulsaVO.getCodigo(), con);
                    }
                }
            }
        }
    }

    private static void preencherIdVendaVendaAvulsa(int idVenda, Integer codigo, Connection con) {
        try {
            con.createStatement().execute("UPDATE vendaavulsa SET id_movimento = " + idVenda + " WHERE codigo = " + codigo);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }


    private boolean validarDataInicialConsiderarLancamentos(Date saleDate, Date dataInicialConsiderarLancamentos) {
        return dataInicialConsiderarLancamentos == null
                || (Calendario.maiorOuIgual(saleDate, dataInicialConsiderarLancamentos));
    }

    public Integer obterIdMemberShipReceivables(List<MemberMembershipApiViewTO> memberShips, List<SaleItemApiViewTO> saleItemApiViewTOS) throws Exception {
        MemberMembershipApiViewTO memberShipReceivables = null;

        if (UteisValidacao.emptyList(memberShips)) {
            return 0;
        }

        Map<Integer, SaleItemApiViewTO> mapMemberShipsSaleItem = obterMapaMemberShipSaleItens(memberShips, saleItemApiViewTOS);

        for (MemberMembershipApiViewTO ms: memberShips) {
            if (memberShipReceivables == null ) {
                memberShipReceivables = ms;
                continue;
            }
            SaleItemApiViewTO saleItem1 = mapMemberShipsSaleItem.get(ms.getIdMemberMembership());
            SaleItemApiViewTO saleItem2 = mapMemberShipsSaleItem.get(memberShipReceivables.getIdMemberMembership());
            if (saleItem1 != null & saleItem2 != null && saleItem1.getSaleValue() > saleItem2.getSaleValue()) {
                memberShipReceivables = ms;
            } else if (!ms.getName().toLowerCase().contains("dependente") && memberShipReceivables.getName().toLowerCase().contains("dependente")) {
                memberShipReceivables = ms;
            } else if (!ms.getName().toLowerCase().contains("dependente")) {
                Date endDate1 = Calendario.getDate("yyyy-MM-dd", memberShipReceivables.getEndDate());
                Date startDate2 = Calendario.getDate("yyyy-MM-dd", ms.getStartDate());
                Date endDate2 = Calendario.getDate("yyyy-MM-dd", ms.getEndDate());
                if (!Calendario.maior(startDate2, endDate1) && Calendario.maior(endDate2, endDate1)) {
                    memberShipReceivables = ms;
                }
            }
        }
        return memberShipReceivables.getIdMemberMembership();
    }

    private Map<Integer, SaleItemApiViewTO> obterMapaMemberShipSaleItens(List<MemberMembershipApiViewTO> memberShips, List<SaleItemApiViewTO> saleItemApiViewTOS) {
        Map<Integer, SaleItemApiViewTO> mapMemberShipsSaleItem = new HashMap<>(); // key = IdMemberMembership
        for (MemberMembershipApiViewTO ms: memberShips) {
            String name = ms.getName().toLowerCase();
            Date inicio = UteisImportacao.getDateFromLocalDateTime(ms.getStartDate());
            saleItemApiViewTOS.forEach(si -> {
                if (!UteisValidacao.emptyNumber(si.getIdMembership()) && si.getIdMembership().equals(ms.getIdMembership())) {
                    if (si.getItem().toLowerCase().contains(name) && si.getDescription().contains(Calendario.getData(inicio, "dd/MM/yyyy"))) {
                        mapMemberShipsSaleItem.put(ms.getIdMemberMembership(), si);
                    }
                }
            });
        }
        return mapMemberShipsSaleItem;
    }

    public void processar(TipoOperacaoIntegracaoMembersEnum operacao, Integer codigoEmpresa, String idsMembers, String cpfs) throws Exception {
        switch (operacao) {
            case MEMBERS_FREEPASS: {
                Empresa empresaDAO = new Empresa(con);
                List<EmpresaVO> empresas = empresaDAO.consultarEmpresas();
                for (EmpresaVO empresa : empresas) {
                    List<IntegracaoMemberVO> integracoes = integracaoMemberDAO.consultarIntegracoesPorEmpresa(empresa.getCodigo());
                    for (IntegracaoMemberVO integracaoMemberVO : integracoes) {
                        if (integracaoMemberVO.isMemberFreePass()) {
                            sincronizar(integracaoMemberVO);
                        }
                    }
                }
                integrarMembers();
                break;
            }
            case MEMBERS: {
                IntegracaoMemberVO integracaoMemberVO = integracaoMemberDAO.consultarIntegracaoPorEmpresa(codigoEmpresa);
                validarConfiguracoes(integracaoMemberVO);

                importacaoConfigTO.setIntegracaoMemberVO(integracaoMemberVO);
                importacaoConfigTO.setTipoImportacaoEnum(TipoImportacaoEnum.MEMBERS_EVO);
                importacaoConfigTO.setTipoOperacaoIntegracaoMembersEnum(TipoOperacaoIntegracaoMembersEnum.MEMBERS);

                List<Integer> ids = obterListaIds(idsMembers, integracaoMemberVO.getCodigo());

                Uteis.logarDebug("Consultando todos os ids members na API, vai demorar...");
                List<MembersApiViewTO> membershipApiViewTOS = new ArrayList<>();
                if (UteisValidacao.emptyList(ids)) {
                    membershipApiViewTOS.addAll(getAllMembers(integracaoMemberVO, null));
                } else {
                    membershipApiViewTOS.addAll(obterMembersByIdsMembersPaginado(integracaoMemberVO, ids));
                }

                if (!UteisValidacao.emptyList(membershipApiViewTOS) && !importacaoConfigTO.isSincronizarMembersImportados()) {
                    removerMembersJaExistentes(membershipApiViewTOS, integracaoMemberVO.getCodigo());
                }

                removerDuplicidade(membershipApiViewTOS);
                if (UteisValidacao.emptyList(membershipApiViewTOS)) {
                    Uteis.logarDebug("Nenhum member para processar!");
                    return;
                }
                ids = membershipApiViewTOS.stream().map(m -> m.getIdMember()).collect(Collectors.toList());

                if (!UteisValidacao.emptyString(idsMembers)) {
                    Uteis.logarDebug("Ids que não foram encontratodos: ");
                    for (String id : idsMembers.split(",")) {
                        if (!ids.contains(Integer.parseInt(id))) {
                            System.out.print(id + ",");
                        }
                    }
                }

                threadImportacaoMembers = new ThreadImportacaoMembers(con, integracaoMemberVO, importacaoConfigTO, ids);
                threadImportacaoMembers.setName("THREAD_IMPORTACAO_MEMBERS_" + chave);
                threadImportacaoMembers.start();
                executeCallAblesMembersDataApi(integracaoMemberVO, membershipApiViewTOS);
                threadImportacaoMembers.join();
                if (importacaoConfigTO.isImportarProspects()) {
                    processarProspects(integracaoMemberVO);
                }
                break;
            }
            case RECEBIMENTOS_PARCELAS: {
                IntegracaoMemberVO integracaoMemberVO = integracaoMemberDAO.consultarIntegracaoPorEmpresa(codigoEmpresa);
                List<Integer> ids;
                if (UteisValidacao.emptyString(idsMembers) && !UteisValidacao.emptyString(cpfs)) {
                    ids = obterIdsMembersPorCpf(integracaoMemberVO, cpfs);
                } else {
                    ids = obterListaIds(idsMembers, integracaoMemberVO.getCodigo());
                }
                importacaoConfigTO.setIntegracaoMemberVO(integracaoMemberVO);
                importacaoConfigTO.setTipoImportacaoEnum(TipoImportacaoEnum.MEMBERS_EVO_RECEBIMENTOS_PARCELAS);
                importacaoConfigTO.setTipoOperacaoIntegracaoMembersEnum(TipoOperacaoIntegracaoMembersEnum.RECEBIMENTOS_PARCELAS);
                processarRecebimentosParcelasPorIdExternoMovParcela(integracaoMemberVO, ids);

                System.out.println("=========================================================");
                System.out.println("TOTAL FALHAS: " + falhas.size());
                falhas.forEach(f -> System.out.println(f));
                break;
            }
            case RECEBIMENTOS_PARCELAS_VENDA_PRODUTO_SERVICO_DIARIA: {
                IntegracaoMemberVO integracaoMemberVO = integracaoMemberDAO.consultarIntegracaoPorEmpresa(codigoEmpresa);

                List<Integer> ids = obterIdsClientesVendaProdutoServicoDiariaEmAberto(integracaoMemberVO);

                importacaoConfigTO.setIntegracaoMemberVO(integracaoMemberVO);
                importacaoConfigTO.setTipoImportacaoEnum(TipoImportacaoEnum.MEMBERS_EVO_RECEBIMENTOS_PARCELAS);
                importacaoConfigTO.setTipoOperacaoIntegracaoMembersEnum(TipoOperacaoIntegracaoMembersEnum.RECEBIMENTOS_PARCELAS);
                processarRecebimentosParcelasPorIdExternoMovParcela(integracaoMemberVO, ids);

                System.out.println("=========================================================");
                System.out.println("TOTAL FALHAS: " + falhas.size());
                falhas.forEach(f -> System.out.println(f));
                break;
            }
            case CORRIGIR_PAGAMENTOS_SEM_AUTORIZACAO_NSU: {
                IntegracaoMemberVO integracaoMemberVO = integracaoMemberDAO.consultarIntegracaoPorEmpresa(codigoEmpresa);

                corrigirPagamentosSemAutorizacaoNsu(integracaoMemberVO);

                System.out.println("=========================================================");
                System.out.println("TOTAL FALHAS: " + falhas.size());
                falhas.forEach(f -> System.out.println(f));
                break;
            }
            case SINCRONIZAR_CANCELAMENTOS: {
                IntegracaoMemberVO integracaoMemberVO = integracaoMemberDAO.consultarIntegracaoPorEmpresa(codigoEmpresa);

                ResultSet rs = SuperFacadeJDBC.criarConsulta("SELECT \n" +
                        " cli.matriculaexterna, " +
                        " con.codigo, \n" +
                        " COALESCE(con.idexterno, con.id_externo) as idexterno\n" +
                        "FROM contrato con \n" +
                        " INNER JOIN cliente cli ON cli.pessoa = con.pessoa \n" +
                        " WHERE con.situacao = 'AT' \n" +
                        "AND COALESCE(con.idexterno, con.id_externo) > 0", con);
                while (rs.next()) {
                    Long matriculaExterna = rs.getLong("matriculaexterna");
                    ClienteVO clienteVO = clienteDAO.consultarPorMatriculaExterna(matriculaExterna, Uteis.NIVELMONTARDADOS_DADOSBASICOS);

                    MembersApiViewTO memberApiViewTO = getMember(integracaoMemberVO, matriculaExterna.intValue());
                    Map<Integer, MemberMembershipApiViewTO> mapMemberShip = new HashMap<>(); // key = idMemberMembership
                    for (MemberMembershipApiViewTO memberShip : memberApiViewTO.getMemberships()) {
                        mapMemberShip.put(memberShip.getIdMemberMembership(), memberShip);
                    }

                    ImportadorMembersEvo importador = new ImportadorMembersEvo(con, integracaoMemberVO, cache);
                    for (MemberMembershipApiViewTO memberShip : memberApiViewTO.getMemberships()) {
                        importador.verificarCancelamentoContrato(clienteVO, memberShip, mapMemberShip);
                    }
                }

            }
            default:
                break;
        }
    }

    private void validarConfiguracoes(IntegracaoMemberVO integracaoMemberVO) throws Exception {
        PlanoVO planoVO = cache.obterPlanoVO(integracaoMemberVO.getPlanoPadrao());
        if (planoVO == null) {
            throw new ConsistirException("O plano padrão deve ser informado!");
        }
        if (planoVO.getRecorrencia()) {
            throw new ConsistirException("O plano padrão não pode ser do tipo recorrencia!");
        }
        HorarioVO horarioVO = cache.obterHorarioVO(integracaoMemberVO.getHorarioPadrao());
        if (horarioVO == null) {
            throw new ConsistirException("O horário padrão deve ser informado!");
        }
    }

    private void removerDuplicidade(List<MembersApiViewTO> membershipApiViewTOS) {
        Set<Integer> ids = new HashSet<>();
        Iterator<MembersApiViewTO> iterator = membershipApiViewTOS.iterator();
        while (iterator.hasNext()) {
            MembersApiViewTO member = iterator.next();
            if (ids.contains(member.getIdMember())) {
                iterator.remove();
            } else {
                ids.add(member.getIdMember());
            }
        }
    }

    private List<MembersApiViewTO> obterMembersByIdsMembersPaginado(IntegracaoMemberVO integracaoMemberVO, List<Integer> ids) throws Exception {
        List<MembersApiViewTO> membershipApiViewTOS = new ArrayList<>();
        Map<String, String> params = new HashMap<>();
        if (!UteisValidacao.emptyList(ids)) {
            int itensPorVez = 49;
            for (int i = 0; i < ids.size(); i += itensPorVez) {
                String idsMembers = ids.subList(i, Math.min(i + itensPorVez, ids.size()))
                        .stream().map(String::valueOf)
                        .collect(Collectors.joining(","));
                if (UteisValidacao.emptyString(idsMembers)) {
                    return membershipApiViewTOS;
                }
                params.put("idsMembers", idsMembers);
                membershipApiViewTOS.addAll(getAllMembers(integracaoMemberVO, params));
            }
        }
        return membershipApiViewTOS;
    }

    private void removerIdsMembersJaExistentes(List<Integer> ids, Integer codigoIntegracaoMember) throws Exception {
        List<Integer> idsMembers = memberDAO.obterIdsMembersPorIntegracaoMember(codigoIntegracaoMember);
        Iterator<Integer> iterator = ids.iterator();
        while (iterator.hasNext()) {
            Integer id = iterator.next();
            if (idsMembers.contains(id)) {
                iterator.remove();
            }
        }
    }

    private void removerMembersJaExistentes(List<MembersApiViewTO> members, Integer codigoIntegracaoMember) throws Exception {
        List<Integer> idsMembers = memberDAO.obterIdsMembersPorIntegracaoMember(codigoIntegracaoMember);
        Iterator<MembersApiViewTO> iterator = members.iterator();
        while (iterator.hasNext()) {
            MembersApiViewTO member = iterator.next();
            if (idsMembers.contains(member.getIdMember())) {
                iterator.remove();
            }
        }
    }

    private void executeCallAblesMembersDataApi(IntegracaoMemberVO integracaoMemberVO, List<MembersApiViewTO> members) throws Exception {
        AtomicInteger countRequestsApi = new AtomicInteger(0);
        AtomicLong milisStartCount = new AtomicLong(0);

        List<MemberDataApiCallAble> callablesMembers = new ArrayList<>();
        ReentrantLock lock = new ReentrantLock();
        for (MembersApiViewTO member : members) {
            callablesMembers.add(new MemberDataApiCallAble(integracaoMemberVO, member, countRequestsApi, milisStartCount, lock));
        }

        Integer qtdThreas = Runtime.getRuntime().availableProcessors();
        Uteis.logarDebug("Iniciando consulta member API... QTD Threads: " + qtdThreas);
        final ExecutorService executorService = Executors.newFixedThreadPool(qtdThreas);
        for (int i = 0; i < callablesMembers.size(); i += qtdThreas) {
            List<MemberDataApiCallAble> batch = callablesMembers.subList(i, Math.min(i + qtdThreas, callablesMembers.size()));
            executorService.invokeAll(batch).forEach(m -> {
                Integer idMember = 0;
                try {
                    MemberDataJson memberDataApiCallAble = m.get();
                    idMember = m.get().getIdMember();
                    MemberDataJson memeberDataJson = m.get();
                    if (!UteisValidacao.emptyString(memberDataApiCallAble.getMsgErro())) {
                        throw new Exception(memberDataApiCallAble.getMsgErro());
                    }
                    MembersApiViewTO membersApiViewTO = JSONMapper.getObject(memeberDataJson.getMemberJson(), MembersApiViewTO.class);
                    MemberVO memberVO = membersApiViewTO.toMember();
                    memberVO.setSincronizado(false);
                    memberVO.setIntegracaoMemberVO(integracaoMemberVO);
                    memberVO.setMemberJson(memeberDataJson.getMemberJson());
                    memberVO.setSalesJson(memeberDataJson.getSalesJson());
                    memberVO.setReceivablesJson(memeberDataJson.getReceivablesJson());
                    memberVO.setLastSync(Calendario.hoje());

                    ResultSet rs = SuperFacadeJDBC.criarConsulta("select codigo from \"member\" \n" +
                            "where idmember = " + memberVO.getIdMember() + " \n" +
                            "and integracaomember = " + integracaoMemberVO.getCodigo(), con);
                    if (rs.next()) {
                        memberVO.setCodigo(rs.getInt("codigo"));
                        memberDAO.alterar(memberVO);
                    } else {
                        memberDAO.incluir(memberVO);
                    }
                } catch (Exception e) {
                    handleError(e, idMember, null);
                }
            });
        }
        executorService.shutdown();
        executorService.awaitTermination(99, TimeUnit.HOURS);
        Uteis.logarDebug("Consulta member API Terminou...");
        threadImportacaoMembers.setConsultaApiTerminou(true);
    }

    private void handleError(Exception ex, Integer idMember, ProcessoImportacaoVO processoImportacaoVO) {
        ex.printStackTrace();
        String erro = UteisValidacao.emptyString(ex.getMessage()) ? ex.toString() : ex.getMessage();
        String msgErro = String.format("Matricula: %d - Falha ao processar: %s", idMember, erro);
        try {
            Uteis.logarDebug(msgErro);
            processoLogDAO.incluir(new ProcessoImportacaoLogVO(processoImportacaoVO, TipoLogEnum.ERRO, msgErro));
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    private List<Integer> obterIdsClientesVendaProdutoServicoDiariaEmAberto(IntegracaoMemberVO integracaoMemberVO) throws Exception {
        String sql = "select array_to_string(array(\n" +
                "\tselect \n" +
                "\t\tdistinct cli.matriculaexterna \n" +
                "\tfrom movparcela m \n" +
                "\tinner join cliente cli on cli.pessoa = m.pessoa \n" +
                "\twhere m.situacao = 'EA'\n" +
                "\tand m.idexterno > 0 \n" +
                "\tand (m.aulaavulsadiaria > 0 or m.vendaavulsa > 0)\n" +
                "\tand not exists (select bmpar.codigo from boletomovparcela bmpar where bmpar.movparcela = m.codigo)\n" +
                "),',')";
        ResultSet rs = SuperFacadeJDBC.criarConsulta(sql, con);
        if (rs.next()) {
            return obterListaIds(rs.getString(1), 0);
        }
        return new ArrayList<>();
    }

    private void corrigirPagamentosSemAutorizacaoNsu(IntegracaoMemberVO integracaoMemberVO) throws Exception {
        ResultSet rs = SuperFacadeJDBC.criarConsulta("select \n" +
                "\tcli.matriculaexterna,\n" +
                "\tmpg.codigo,\n" +
                "\tmpg.nomepagador,\n" +
                "\tmpg.autorizacaocartao,\n" +
                "\tmpar.codigo as movparcela,\n" +
                "\tmpar.descricao,\n" +
                "\tmpar.valorparcela,\n" +
                "\tmpar.idexterno,\n" +
                "\tfpg.tipoformapagamento \n" +
                "from movpagamento mpg\n" +
                "\tinner join cliente cli on cli.pessoa = mpg.pessoa \n" +
                "\tinner join formapagamento fpg on fpg.codigo = mpg.formapagamento \n" +
                "\tinner join pagamentomovparcela pmp on pmp.movpagamento = mpg.codigo\n" +
                "\tinner join movparcela mpar on mpar.codigo = pmp.movparcela \n" +
                "where 1 = 1 \n" +
                "and fpg.tipoformapagamento in ('CA', 'CD')\n" +
                "and (coalesce(mpg.autorizacaocartao,'') = '' or coalesce(mpg.nsu, '') = '') \n" +
                "and cli.empresa = " + integracaoMemberVO.getEmpresa().getCodigo() + "\n" +
                "and coalesce(mpar.idexterno,0) > 0", con);

        while (rs.next()) {
            List<ReceivablesViewTO> receivables = obterReceivables(integracaoMemberVO, rs.getInt("matriculaexterna"), true, false, null, rs.getInt("idexterno"));

            if (!UteisValidacao.emptyList(receivables)) {
                if (UteisValidacao.emptyString(receivables.get(0).getAuthorization())) {
                    continue;
                }

                Uteis.logar(true, null, "Corrigindo movpagamento codigo: " + rs.getInt("codigo") + " - autorização: " + receivables.get(0).getAuthorization() + " - nsu: " + receivables.get(0).getAuthorization());

                MovParcelaVO movParcelaVO = movParcelaDAO.consultarPorCodigo(rs.getInt("movparcela"), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                if (movParcelaVO != null) {
                    SuperFacadeJDBC.executarConsulta("update movpagamento set autorizacaocartao = '" + receivables.get(0).getAuthorization() + "', \n " +
                            " nsu = '" + receivables.get(0).getAuthorization() + "' \n" +
                            "where codigo = " + rs.getInt("codigo"), con);
                }
            }
        }
    }

    public List<Integer> obterIdsMembersPorCpf(IntegracaoMemberVO integracaoMemberVO, String cpfs) throws Exception {
        int count = 0;
        int total = cpfs.split(",").length;
        List<Integer> idsMembers = new ArrayList<>();
        for (String cpf : cpfs.split(",")) {
            Uteis.logar(true, null,++count + "\\" + total + " Consultando member cpf: " + cpf);
            List<MembersApiViewTO> members = getMembersByDocument(integracaoMemberVO, cpf);
            if (UteisValidacao.emptyList(members)) {
                Uteis.logar(true, null,"Aluno não encotrato na api EVO, com cpf: " + cpf);
            } else {
                members.forEach(m -> {
                    if (!idsMembers.contains(m.getIdMember())) {
                        idsMembers.add(m.getIdMember());
                    }
                });
            }
        }
        return idsMembers;
    }

    public List<MemberShipApivViewTO> obterMemberShips(IntegracaoMemberVO integracaoMemberVO) throws Exception {
        long inicilMillis = System.currentTimeMillis();

        String url = URL_BASE + ENDPOINT_MEMBER_SHIP + "?take=50&skip=%d";

        int skip = -50;

        List<MemberShipApivViewTO> memberShips = new ArrayList<>();

        while (true) {
            skip = skip + 50;
            final String formattedUrl = String.format(url, skip);
            System.out.println("\t" + formattedUrl);

            HttpGet request = new HttpGet(formattedUrl);
            request.addHeader("User-Agent", userAgents.get(new Random().nextInt(userAgents.size())));

            CredentialsProvider provider = new BasicCredentialsProvider();
            provider.setCredentials(AuthScope.ANY, integracaoMemberVO.getCredentials());

            try (CloseableHttpClient httpClient = HttpClientBuilder.create()
                    .setDefaultCredentialsProvider(provider)
                    .build();
                 CloseableHttpResponse response = httpClient.execute(request)) {

                HttpEntity entity = response.getEntity();
                if (entity != null) {
                    String result = EntityUtils.toString(entity);
                    JSONArray jsonArray = new JSONArray(result);
                    if (jsonArray.length() == 0) {
                        break;
                    }
                    memberShips.addAll(JSONMapper.getList(jsonArray, MemberShipApivViewTO.class));
                }
            }
        }

        long totalMiliSengundos = (System.currentTimeMillis() - inicilMillis);
        Uteis.logarDebug("\tIntegracaoMemberService -> obterMemberShips levou " + totalMiliSengundos + " milissegundos.");
        miliSegundosRequisicoesMember += totalMiliSengundos;

        return memberShips;
    }

    private void verifyLimiteQuotaApi() {
        try {
            if (UteisValidacao.emptyNumber(milisStartCount)) {
                milisStartCount = System.currentTimeMillis();
            } else if((System.currentTimeMillis() - milisStartCount) >= 60000) {
                Uteis.logarDebug(String.format("Tread: %d %s - Reniciando contador resquisições por minuto...", Thread.currentThread().getId(), Thread.currentThread().getName()));
                countCalls = 0;
                milisStartCount = System.currentTimeMillis();
            }
            if (++countCalls >= apiCallLimiteQuotaPerMinute) {
                long tempoAguardar = 60000 - (System.currentTimeMillis() - milisStartCount);
                if (tempoAguardar > 0) {
                    Uteis.logarDebug(String.format("Thread: %d %s - Aguardando tempo restante limite Quota API: %d ms", Thread.currentThread().getId(), Thread.currentThread().getName(), tempoAguardar));
                    Thread.sleep(tempoAguardar);
                }
                countCalls = 0;
                milisStartCount = System.currentTimeMillis();
            }
        } catch (InterruptedException e) {
            Uteis.logarDebug(String.format("Thread: %d %s - Erro ao tentar aguardar tempo restante limite Quota API", Thread.currentThread().getId(), Thread.currentThread().getName()));
        }
    }

    public String executeRequest(String url, IntegracaoMemberVO integracaoMemberVO) throws Exception {
        int maxTentativas = 5;
        int countTentativas = 0;
        int tempoSegundosEntreTentativas = 5;

        List<String> falhas = new ArrayList<>();

        while (countTentativas <= maxTentativas) {
            countTentativas++;
            try {
                verifyLimiteQuotaApi();
                UsernamePasswordCredentials upc = integracaoMemberVO.getCredentials();
                String urserAgent = userAgents.get(new Random().nextInt(userAgents.size()));

                Uteis.logarDebug(String.format("Thread: %d %s - Executando requisicao: GET %s token: %s useragent: %s", Thread.currentThread().getId(), Thread.currentThread().getName(),
                        url,
                        upc.getPassword().substring(0,4),
                        urserAgent));

                HttpGet request = new HttpGet(url);
                CredentialsProvider provider = new BasicCredentialsProvider();
                provider.setCredentials(AuthScope.ANY, upc);

                request.addHeader("User-Agent", urserAgent);

                try (CloseableHttpClient httpClient = HttpClientBuilder.create()
                        .setDefaultCredentialsProvider(provider)
                        .build();
                     CloseableHttpResponse response = httpClient.execute(request)) {
                    HttpEntity entity = response.getEntity();
                    if (entity != null && response.getStatusLine().getStatusCode() == 200) {
                        return EntityUtils.toString(entity);
                    }
                    String txtResponse = entity != null ? EntityUtils.toString(entity) : "";
                    if (txtResponse.contains("requests per minute has been reached")) {
                        countCalls = apiCallLimiteQuotaPerMinute;
                    } else if (txtResponse.contains("key temporarily blocked due to too many attempts")) {
                        JSONObject jsonResponse = new JSONObject();
                        Date availableAgainAt = jsonResponse.optString("availableAgainAt").trim().isEmpty() ? null : Calendario.getDate("MM/dd/yyyy HH:mm:sss", jsonResponse.optString("availableAgainAt").split("\\\\u")[0].trim());
                        availableAgainAt = availableAgainAt == null ? Calendario.somarMinutos(Calendario.hoje(), 32) : Calendario.somarMinutos(availableAgainAt, 2);
                        integracaoMemberVO.getMapaTokenDateBlocked().put(upc.getPassword(), availableAgainAt);
                    }
                    throw new Exception(String.format("Falha ao executar requisitacao! status: %s response: %s", response.getStatusLine().getStatusCode(), txtResponse));
                }
            } catch (Exception e) {
                Uteis.logarDebug(String.format("Thread: %d %s - tentativa %d\\%d - %s \n%s", Thread.currentThread().getId(), Thread.currentThread().getName(), countTentativas, maxTentativas, url, e.getMessage()));
                long tempoMilis = tempoSegundosEntreTentativas * 1000;

                if (integracaoMemberVO.tokensDisponiveisNaoBloqueados().isEmpty() && integracaoMemberVO.getMapaTokenDateBlocked().size() > 0) {
                    Date dataDesbloqueio = integracaoMemberVO.obterProximaDataHoraPrevistaDesbloqueio();
                    if (Calendario.maiorComHora(dataDesbloqueio, Calendario.hoje())) {
                        long minutosAguardar = Calendario.diferencaEmMinutos(Calendario.hoje(), dataDesbloqueio);
                        tempoMilis = minutosAguardar * 1000;
                        Uteis.logarDebug(String.format("Token(s) Bloqueado(s)! aguardar %d minuto(s) ate o desbloqueio, previsão: %s", minutosAguardar, Calendario.getDataAplicandoFormatacao(dataDesbloqueio, "dd/MM/yyyy")));
                    }
                }
                Thread.sleep(tempoMilis);
                if (countTentativas == maxTentativas) {
                    throw new Exception(String.format("Falha ao executar requisitacao! url: %s, falhas: %s ", url, falhas));
                }
            }
        }
        return null;
    }


    public static void main(String[] args) {
        try {
            String key = "liveunidtorresam";
            Integer codigoEmpresa = 1;

            if (args.length > 1) {
                key = args[0];
                codigoEmpresa  = Integer.valueOf(args[1]);
            }
            String idsMembers = "";
            String cpfs = "";
            TipoOperacaoIntegracaoMembersEnum operacao = TipoOperacaoIntegracaoMembersEnum.MEMBERS;
            boolean importarProspects = false;
            boolean importarVendas = true;
            boolean importarReceivablesSemVendas = true;
            boolean sincronizarMembersImportados = true; // MEMBERS - Se habilitado o sistema irá reprocessar também os members ja consultados e importados anteriormente.
    
            logIp();
            Connection con = new DAO().obterConexaoEspecifica(key);

            Usuario usuarioDAO = new Usuario(con);
            UsuarioVO usuarioVO = usuarioDAO.consultarPorUsername("pactobr", Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            usuarioDAO = null;

            ImportacaoCache importacaoCache = new ImportacaoCache(con, usuarioVO.getCodigo());
            IntegracaoMemberService integracaoMemberService = new IntegracaoMemberService(con, key, importacaoCache, operacao);
            integracaoMemberService.importacaoConfigTO = new ImportacaoConfigTO();
            integracaoMemberService.importacaoConfigTO.setChave(key);
            integracaoMemberService.importacaoConfigTO.setListaEmails(new ArrayList<>());
            integracaoMemberService.importacaoConfigTO.setUsuarioResponsavelImportacao(usuarioVO.getCodigo());
            integracaoMemberService.importacaoConfigTO.setDataInicioProcesso(Calendario.hoje());
            integracaoMemberService.importacaoConfigTO.setImportarProspects(importarProspects);
            integracaoMemberService.importacaoConfigTO.setImportarVendas(importarVendas);
            integracaoMemberService.importacaoConfigTO.setImportarReceivablesSemVendas(importarReceivablesSemVendas);
            integracaoMemberService.importacaoConfigTO.setSincronizarMembersImportados(sincronizarMembersImportados);
            integracaoMemberService.processar(operacao, codigoEmpresa, idsMembers, cpfs);
        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }

    private static void logIp() throws IOException {
        String retorno = ExecuteRequestHttpService.executeHttpRequest("https://app.pactosolucoes.com.br/ip/v2.php", null);
        if (retorno != null && !retorno.isEmpty()) {
            retorno = retorno.replace("\n", "");
            Uteis.logarDebug(String.format("Ip: %s", retorno));
        }
    }

}
