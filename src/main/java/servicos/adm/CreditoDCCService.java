package servicos.adm;

import br.com.pactosolucoes.comuns.json.ConfigCobrancaMensalJSON;
import br.com.pactosolucoes.comuns.json.ItemCobrancaPactoJSON;
import br.com.pactosolucoes.comuns.util.Formatador;
import br.com.pactosolucoes.enumeradores.TipoCobrancaPactoEnum;
import br.com.pactosolucoes.oamd.controle.basico.DAO;
import controle.arquitetura.SuperControle;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.basico.LogCobrancaPactoVO;
import negocio.comuns.crm.ConfiguracaoSistemaCRMVO;
import negocio.comuns.financeiro.enumerador.SituacaoBoletoEnum;
import negocio.comuns.financeiro.enumerador.SituacaoTransacaoEnum;
import negocio.comuns.financeiro.enumerador.TipoRemessaEnum;
import negocio.comuns.financeiro.enumerador.TipoTransacaoEnum;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisEmail;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.basico.Cliente;
import negocio.facade.jdbc.basico.CreditoPactoHistorico;
import negocio.facade.jdbc.basico.Empresa;
import negocio.facade.jdbc.basico.LogCobrancaPacto;
import negocio.facade.jdbc.financeiro.FinanceiroPacto;
import negocio.facade.jdbc.utilitarias.Conexao;
import org.json.JSONArray;
import org.json.JSONObject;
import servicos.http.MetodoHttpEnum;
import servicos.http.RequestHttpService;
import servicos.http.RespostaHttpDTO;
import servicos.pix.PixStatusEnum;
import servicos.propriedades.PropsService;
import ws.TEfeitoParcelaAgendamento;
import ws.TResultadoTransacaoWS;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.*;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * Created by glauco on 19/11/2014.
 */
public class CreditoDCCService extends SuperEntidade {

    private String key;
    private List<EmpresaVO> listaEmpresas;
    private String PROCESSO_AUTOMATICO = "PROCESSAMENTO AUTOMATICO";


    public CreditoDCCService() throws Exception {
        listaEmpresas = getFacade().getEmpresa().consultarTodas(true, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
    }

    public CreditoDCCService(Connection conexao) throws Exception {
        super(conexao);
    }

    public String getKey() {
        if (key == null) {
            key = "chave_nao_informada";
        }
        return key;
    }

    public void setKey(String key) {
        this.key = key;
    }

    public int consultarSaldo(int codigoEmpresa) throws SQLException {
        String sql = "SELECT creditodcc FROM empresa WHERE codigo = ?";
        try (PreparedStatement sqlAlterar = con.prepareStatement(sql)) {
            sqlAlterar.setInt(1, codigoEmpresa);
            try (ResultSet rs = sqlAlterar.executeQuery()) {
                if (rs.next()) {
                    return rs.getInt("creditodcc");
                }
            }
        }
        return 0;
    }

    private void registrarLogInformacoesCobrancaPacto(Integer codigoEmpresa, Integer tipoCobrancaPacto, boolean gerarCobrancaAutomaticaPacto, Integer qtdDiasFechamentoCobrancaPacto,
                                                      Double valorCreditoPacto, boolean gerarNotaFiscalCobrancaPacto, Integer qtdParcelasCobrancaPacto, Integer qtdCreditoRenovarPrePagoCobrancaPacto,
                                                      String nomeUsuarioOAMD, Integer diaVencimentoCobrancaPacto, boolean empresaResponsavelCobrancaPacto, Boolean cobrarCreditoPactoBoleto) throws Exception {

        Empresa empresa = new Empresa(con);
        EmpresaVO empresaVO = empresa.consultarPorChavePrimaria(codigoEmpresa, Uteis.NIVELMONTARDADOS_GESTAOREMESSA);
        StringBuilder anterior = new StringBuilder();
        anterior.append("codigo: ").append(empresaVO.getCodigo());
        anterior.append("\ntipoCobrancaPacto: ").append(empresaVO.getTipoCobrancaPacto());
        anterior.append("\ngerarCobrancaAutomaticaPacto: ").append(empresaVO.isGerarCobrancaAutomaticaPacto());
        anterior.append("\nqtdDiasFechamentoCobrancaPacto: ").append(empresaVO.getQtdDiasFechamentoCobrancaPacto());
        anterior.append("\nvalorCreditoPacto: ").append(empresaVO.getValorCreditoPacto());
        anterior.append("\ngerarNotaFiscalCobrancaPacto: ").append(empresaVO.isGerarNotaFiscalCobrancaPacto());
        anterior.append("\nqtdParcelasCobrancaPacto: ").append(empresaVO.getQtdParcelasCobrancaPacto());
        anterior.append("\nqtdCreditoRenovarPrePagoCobrancaPacto: ").append(empresaVO.getQtdCreditoRenovarPrePagoCobrancaPacto());
        anterior.append("\ndiaVencimentoCobrancaPacto: ").append(empresaVO.getDiaVencimentoCobrancaPacto());
        anterior.append("\nempresaResponsavelCobrancaPacto: ").append(empresaVO.isEmpresaResponsavelCobrancaPacto());

        StringBuilder novo = new StringBuilder();
        novo.append("ALTERACAO DE INFORMAÇÕES DE COBRANÇA PACTO: \n");
        novo.append("\ncodigo: ").append(codigoEmpresa);
        novo.append("\ntipoCobrancaPacto: ").append(tipoCobrancaPacto);
        novo.append("\ngerarCobrancaAutomaticaPacto: ").append(gerarCobrancaAutomaticaPacto);
        novo.append("\nqtdDiasFechamentoCobrancaPacto: ").append(qtdDiasFechamentoCobrancaPacto);
        novo.append("\nvalorCreditoPacto: ").append(valorCreditoPacto);
        novo.append("\ngerarNotaFiscalCobrancaPacto: ").append(gerarNotaFiscalCobrancaPacto);
        novo.append("\nqtdParcelasCobrancaPacto: ").append(qtdParcelasCobrancaPacto);
        novo.append("\nqtdCreditoRenovarPrePagoCobrancaPacto: ").append(qtdCreditoRenovarPrePagoCobrancaPacto);
        novo.append("\ndiaVencimentoCobrancaPacto: ").append(diaVencimentoCobrancaPacto);
        novo.append("\nempresaResponsavelCobrancaPacto: ").append(empresaResponsavelCobrancaPacto);
        if (cobrarCreditoPactoBoleto != null) {
            novo.append("\ncobrarCreditoPactoBoleto: ").append(cobrarCreditoPactoBoleto);
        }

        //REGISTRAR LOG
        String sqlLog = "insert into log(dataalteracao, pessoa, nomeentidade, nomeentidadedescricao, chaveprimaria, nomecampo, valorcampoanterior, valorcampoalterado, responsavelalteracao, operacao) " +
                "values (now(), 0, ?, ?, ?, ?, ?, ?, ?, ?);";

        try (PreparedStatement pst = con.prepareStatement(sqlLog)) {
            int i = 0;
            pst.setString(++i, "EMPRESA");
            pst.setString(++i, "Empresa - Info Cobranca Pacto");
            pst.setString(++i, codigoEmpresa.toString());
            pst.setString(++i, "Informacoes Cobranca Pacto");
            pst.setString(++i, anterior.toString());
            pst.setString(++i, novo.toString());
            pst.setString(++i, "USUARIO OAMD: " + nomeUsuarioOAMD);
            pst.setString(++i, "ALTERAÇÃO");
            pst.execute();
        }
    }

    public int alterarSaldo(int codigoEmpresa, int quantidadeCreditos, String observacao) throws Exception {
        String sql = String.format("UPDATE empresa SET creditodcc=(creditodcc+(%s)) WHERE codigo = %s RETURNING creditodcc", quantidadeCreditos, codigoEmpresa);
        int saldoAtual = 0;
        try {
            con.setAutoCommit(false);

            try (PreparedStatement sqlAlterar = con.prepareStatement(sql)) {
                try (ResultSet rs = sqlAlterar.executeQuery()) {

                    if (rs.next()) {
                        saldoAtual = rs.getInt("creditodcc");
                    }
                }
            }

            inserirLogDCC(codigoEmpresa, quantidadeCreditos, saldoAtual, observacao);

            con.commit();
        } catch (Exception e) {
            con.rollback();
            con.setAutoCommit(true);
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
        return saldoAtual;
    }

    public String alterarInformacoesEmpresaCobrancaPacto(Integer codigoEmpresa, Integer tipoCobrancaPacto, boolean gerarCobrancaAutomaticaPacto, Integer qtdDiasFechamentoCobrancaPacto,
                                                         Double valorCreditoPacto, boolean gerarNotaFiscalCobrancaPacto, Integer qtdParcelasCobrancaPacto,
                                                         Integer qtdCreditoRenovarPrePagoCobrancaPacto, String nomeUsuarioOAMD, Integer diaVencimentoCobrancaPacto,
                                                         boolean empresaResponsavelCobrancaPacto, Boolean cobrarCreditoPactoBoleto) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("UPDATE empresa SET tipoCobrancaPacto = ?, gerarCobrancaAutomaticaPacto = ?, qtdDiasFechamentoCobrancaPacto = ?, valorCreditoPacto = ?, gerarNotaFiscalCobrancaPacto = ?, ");
        sql.append("qtdParcelasCobrancaPacto = ?, qtdCreditoRenovarPrePagoCobrancaPacto = ?, diaVencimentoCobrancaPacto = ?, empresaResponsavelCobrancaPacto = ? ");
        if (cobrarCreditoPactoBoleto != null) {
            sql.append(", cobrarCreditoPactoBoleto = ? ");
        }
        sql.append("WHERE codigo = ?");

        try {
            con.setAutoCommit(false);

            registrarLogInformacoesCobrancaPacto(codigoEmpresa, tipoCobrancaPacto, gerarCobrancaAutomaticaPacto, qtdDiasFechamentoCobrancaPacto,
                    valorCreditoPacto, gerarNotaFiscalCobrancaPacto, qtdParcelasCobrancaPacto, qtdCreditoRenovarPrePagoCobrancaPacto, nomeUsuarioOAMD,
                    diaVencimentoCobrancaPacto, empresaResponsavelCobrancaPacto, cobrarCreditoPactoBoleto);

            try (PreparedStatement pst = con.prepareStatement(sql.toString())) {
                int i = 0;
                pst.setInt(++i, tipoCobrancaPacto);
                pst.setBoolean(++i, gerarCobrancaAutomaticaPacto);
                pst.setInt(++i, qtdDiasFechamentoCobrancaPacto);
                pst.setDouble(++i, valorCreditoPacto);
                pst.setBoolean(++i, gerarNotaFiscalCobrancaPacto);
                pst.setInt(++i, qtdParcelasCobrancaPacto);
                pst.setInt(++i, qtdCreditoRenovarPrePagoCobrancaPacto);
                pst.setInt(++i, diaVencimentoCobrancaPacto);
                pst.setBoolean(++i, empresaResponsavelCobrancaPacto);
                if (cobrarCreditoPactoBoleto != null) {
                    pst.setBoolean(++i, cobrarCreditoPactoBoleto);
                }
                pst.setInt(++i, codigoEmpresa);
                pst.execute();
            }

            con.commit();
            return "Sucesso";
        } catch (Exception e) {
            con.rollback();
            con.setAutoCommit(true);
            return e.getMessage();
        } finally {
            con.setAutoCommit(true);
        }
    }

    public String consultarFavorecidoFinanceiro(String cpf_cnpj) throws Exception {
        FinanceiroPacto financeiroPacto = new FinanceiroPacto(con);
        JSONObject obj = new JSONObject(financeiroPacto.consultarFavorecido(cpf_cnpj));
        return obj.toString();
    }

    private void inserirLogDCC(Integer empresa, Integer quantidade, Integer saldo, String observacao) throws Exception {
        String sql = "INSERT INTO logdcc (data, empresa, quantidade, saldo, observacao) VALUES (now(), ?, ?, ?, ?);";
        try (PreparedStatement pst = con.prepareStatement(sql)) {
            int i = 0;
            pst.setInt(++i, empresa);
            pst.setInt(++i, quantidade);
            pst.setInt(++i, saldo);
            pst.setString(++i, observacao);
            pst.execute();
        }
    }

    private void inserirLogCobrancaPacto(Integer quantidade, Double valorTotal, Integer qtdParcelas, boolean gerarNota,
                                         String nomeUsuarioOAMD, Integer empresa, String justificativa, boolean gerarCobrancaFinanceiro,
                                         String observacao, String itensCobrancaPacto, JSONObject jsonCobranca, Integer tipoCobrancaPacto,
                                         String tabelaCreditoPacto) throws Exception {
        LogCobrancaPactoVO logCobrancaPactoVO = new LogCobrancaPactoVO();
        logCobrancaPactoVO.setDataCobranca(Calendario.hoje());
        logCobrancaPactoVO.setQuantidade(quantidade);
        logCobrancaPactoVO.setValorTotal(valorTotal);
        logCobrancaPactoVO.setQtdParcelas(qtdParcelas);
        logCobrancaPactoVO.setGerarNota(gerarNota);
        logCobrancaPactoVO.setNomeUsuarioOAMD(nomeUsuarioOAMD);
        logCobrancaPactoVO.setEmpresa(empresa);
        logCobrancaPactoVO.setJustificativa(justificativa);
        logCobrancaPactoVO.setGerarCobrancaFinanceiro(gerarCobrancaFinanceiro);
        logCobrancaPactoVO.setObservacao(observacao);
        logCobrancaPactoVO.setItensCobrancaPacto(itensCobrancaPacto);
        if (jsonCobranca != null) {
            logCobrancaPactoVO.setJsonCobranca(jsonCobranca.toString());
        }
        logCobrancaPactoVO.setTipoCobrancaPacto(tipoCobrancaPacto);
        logCobrancaPactoVO.setTabelaCreditoPacto(tabelaCreditoPacto);

        LogCobrancaPacto logCobrancaPactoDAO = new LogCobrancaPacto(con);
        logCobrancaPactoDAO.incluir(logCobrancaPactoVO);
        logCobrancaPactoDAO = null;
    }

    private String adicionarCreditoPrePago(String key, Integer codigoEmpresa, int qtdCredito, Integer qtdParcelas, Double valorTotal, boolean gerarNota, String nomeUsuarioOAMD, String justificativa,
                                           boolean gerarCobrancaFinanceiro, Integer tipoCobrancaPacto) throws Exception {
        String sql = String.format("UPDATE empresa SET dtUltimaCobrancaPacto = now(), creditodcc=(creditodcc+(%s)) WHERE codigo = %s RETURNING creditodcc", qtdCredito, codigoEmpresa);
        int saldoAtual = 0;

        valorTotal = Uteis.arredondarForcando2CasasDecimais(valorTotal);

        try (PreparedStatement sqlAlterar = con.prepareStatement(sql)) {
            try (ResultSet rs = sqlAlterar.executeQuery()) {
                if (rs.next()) {
                    saldoAtual = rs.getInt("creditodcc");
                }
            }
        }

        StringBuilder observacao = new StringBuilder();
        observacao.append("QTD ANTERIOR: ").append(saldoAtual - qtdCredito).append(" \n");
        observacao.append("QTD ADICIONADA: ").append(qtdCredito).append(" \n");
        observacao.append("QTD ATUAL: ").append(saldoAtual);

        inserirLogDCC(codigoEmpresa, qtdCredito, saldoAtual, observacao.toString());

        Double valorUnitario = 0.0;
        try {
            valorUnitario = (valorTotal / qtdCredito);
        } catch (Exception ex) {
            ex.printStackTrace();
        }

        JSONObject jsonCobranca = criarJSONCobranca("adicionarCreditoPrePago", codigoEmpresa, tipoCobrancaPacto, qtdCredito, null, null,
                null, null, null, valorTotal, null, null, null, null, null, null, valorUnitario, null,
                null, null, nomeUsuarioOAMD, justificativa, null);

        inserirLogCobrancaPacto(qtdCredito, valorTotal, qtdParcelas, gerarNota, nomeUsuarioOAMD, codigoEmpresa, justificativa,
                gerarCobrancaFinanceiro, observacao.toString(), "", jsonCobranca, tipoCobrancaPacto, "");

        if (gerarCobrancaFinanceiro) {
            lancarContaFinanceiroPacto(key, codigoEmpresa, qtdCredito, qtdParcelas, valorTotal, gerarNota, 0, tipoCobrancaPacto, null, null);
        }

        return "Sucesso:" + saldoAtual;
    }

    public String processarClientePrePagoCobrancaPactoLancarContaFinanceiro(String key, Integer codigoEmpresa, int qtdCredito, Integer qtdParcelas, Double valorTotal,
                                                                            boolean gerarNota, String nomeUsuarioOAMD, String justificativa, boolean gerarCobrancaFinanceiro, Integer tipoCobrancaPacto) throws Exception {
        try {
            con.setAutoCommit(false);

            String retorno = adicionarCreditoPrePago(key, codigoEmpresa, qtdCredito, qtdParcelas, valorTotal, gerarNota, nomeUsuarioOAMD, justificativa, gerarCobrancaFinanceiro, tipoCobrancaPacto);
            if (!retorno.startsWith("Sucesso")) {
                throw new Exception(retorno);
            }

            con.commit();
            return retorno;
        } catch (Exception e) {
            con.rollback();
            con.setAutoCommit(true);
            return e.getMessage();
        } finally {
            con.setAutoCommit(true);
        }
    }

    public String processarClientePosPagoCobrancaPactoLancarContaFinanceiro(String key, Integer codigoEmpresa, Integer tipoCobrancaPacto,
                                                                            Integer qtdParcelas, Double valorTotal, Double valorCreditoPacto,
                                                                            boolean gerarNota, String nomeUsuarioOAMD, String justificativa,
                                                                            boolean gerarCobrancaFinanceiro, boolean processoAutomatico) throws Exception {
        try {
            con.setAutoCommit(false);

            atualizarDtUltimaCobrancaPacto(codigoEmpresa);

            //CONSULTAR REMESSAITEM
            List<ItemCobrancaPactoJSON> listaCompletaRemessaItem = consultarItensCobrancaPactoRemessasItem(codigoEmpresa, tipoCobrancaPacto, null);
            List<Integer> remessaItem = obterListaRemessasItem(listaCompletaRemessaItem);
            Integer qtdCreditoUtilizadoRemessa = remessaItem.size();
            Uteis.logarDebug("Gerada Cobranca Pos-Pago - Qtd Credito Utilizado REMESSA: " + qtdCreditoUtilizadoRemessa + " - CodEmpresa " + codigoEmpresa);

            //CONSULTAR TRANSACOES
            List<ItemCobrancaPactoJSON> listaCompletaTransacao = consultarItensCobrancaPactoTransacao(codigoEmpresa, tipoCobrancaPacto, null);
            List<Integer> transacao = obterListaTransacao(listaCompletaTransacao);
            Integer qtdCreditoUtilizadoTransacao = transacao.size();
            Uteis.logarDebug("Gerada Cobranca Pos-Pago - Qtd Credito Utilizado TRANSAÇÃO: " + qtdCreditoUtilizadoTransacao + " - CodEmpresa " + codigoEmpresa);

            //CONSULTAR PIX
            List<ItemCobrancaPactoJSON> listaCompletaPix = consultarItensCobrancaPactoPix(codigoEmpresa, tipoCobrancaPacto, null);
            List<Integer> pix = obterListaPix(listaCompletaPix);
            Integer qtdCreditoUtilizadoPix = pix.size();
            Uteis.logarDebug("Gerada Cobranca Pos-Pago - Qtd Credito Utilizado PIX: " + qtdCreditoUtilizadoPix + " - CodEmpresa " + codigoEmpresa);

            boolean cobrarCreditoPactoBoleto = isCobrarCreditoPactoBoleto(codigoEmpresa);

            //CONSULTAR BOLETOS ONLINE
            List<ItemCobrancaPactoJSON> listaCompletaBoletoOnline = new ArrayList<>();
            List<Integer> boletosOnline = new ArrayList<>();
            Integer qtdCreditoUtilizadoBoletoOnline = 0;
            if (cobrarCreditoPactoBoleto) {
                listaCompletaBoletoOnline = consultarItensCobrancaPactoBoletoOnline(codigoEmpresa, tipoCobrancaPacto, null);
                boletosOnline = obterListaBoletoOnline(listaCompletaBoletoOnline);
                qtdCreditoUtilizadoBoletoOnline = boletosOnline.size();
                Uteis.logarDebug("Gerada Cobranca Pos-Pago - Qtd Credito Utilizado BOLETO ONLINE: " + qtdCreditoUtilizadoBoletoOnline + " - CodEmpresa " + codigoEmpresa);
            } else {
                Uteis.logarDebug("Gerada Cobranca Pos-Pago - Qtd Credito Utilizado BOLETO ONLINE: NÃO É COBRADO BOLETO" + " - CodEmpresa " + codigoEmpresa);
            }

            //CONSULTAR BOLETOS REMESSA
            List<ItemCobrancaPactoJSON> listaCompletaBoletoRemessa = new ArrayList<>();
            List<Integer> boletosRemessa = new ArrayList<>();
            Integer qtdCreditoUtilizadoBoletoRemessa = 0;
            if (cobrarCreditoPactoBoleto) {
                listaCompletaBoletoRemessa = consultarItensCobrancaPactoBoletoRemessa(codigoEmpresa, tipoCobrancaPacto, null);
                boletosRemessa = obterListaBoletoRemessa(listaCompletaBoletoRemessa);
                qtdCreditoUtilizadoBoletoRemessa = boletosRemessa.size();
                Uteis.logarDebug("Gerada Cobranca Pos-Pago - Qtd Credito Utilizado BOLETO REMESSA: " + qtdCreditoUtilizadoBoletoRemessa + " - CodEmpresa " + codigoEmpresa);
            } else {
                Uteis.logarDebug("Gerada Cobranca Pos-Pago - Qtd Credito Utilizado BOLETO REMESSA: NÃO É COBRADO BOLETO" + " - CodEmpresa " + codigoEmpresa);
            }

            Integer qtdUtilizado = (qtdCreditoUtilizadoRemessa + qtdCreditoUtilizadoTransacao + qtdCreditoUtilizadoPix + qtdCreditoUtilizadoBoletoOnline + qtdCreditoUtilizadoBoletoRemessa);

            if (qtdUtilizado > 0) {
                Uteis.logarDebug("Gerada Cobranca Pos-Pago - Qtd Credito Utilizado: " + qtdUtilizado + " - CodEmpresa " + codigoEmpresa);
            } else {
                throw new Exception("NÃO FOI GERADA Cobranca Pos-Pago - Qtd Credito Utilizado: " + qtdUtilizado + " - CodEmpresa " + codigoEmpresa);
            }

            //verificar se o cliente possui bonus de crédito para utilizar
            Integer qtdBonusDisponivel = consultarBonus(codigoEmpresa);
            Integer qtdBonusUtilizada = 0;
            Integer qtdBonusRestante = 0;
            if (qtdBonusDisponivel > 0) {
                Uteis.logarDebug("Gerada Cobranca Pos-Pago - Empresa tem bônus de crédito: " + qtdBonusDisponivel);

                //debitar os bônus utilizados!
                if (qtdUtilizado >= qtdBonusDisponivel) { //qtd utilizada maior ou igual a qtd de bonus
                    qtdBonusUtilizada = qtdBonusDisponivel;
                } else {
                    qtdBonusUtilizada = qtdUtilizado;
                }
                qtdBonusRestante = (qtdBonusDisponivel - qtdBonusUtilizada);
                String obsBonus = "Utilizado " + qtdBonusUtilizada + " créditos de bônus, para descontar na utilização de " + qtdUtilizado + " créditos. Bônus restante: " + qtdBonusRestante;
                Uteis.logarDebug("Gerada Cobranca Pos-Pago - " + obsBonus);
                alterarBonus(codigoEmpresa, qtdBonusRestante, obsBonus, nomeUsuarioOAMD, false);
            }

            Integer qtdCobrar = (qtdUtilizado - qtdBonusUtilizada);
            if (qtdCobrar < 0) {
                qtdCobrar = 0;
            }

            marcarItensComoContabilizadoPacto(remessaItem, transacao, pix, boletosOnline, boletosRemessa);

            if (processoAutomatico) {
                valorTotal = Uteis.arredondarForcando2CasasDecimais(qtdCobrar * valorCreditoPacto);
            } else {
                valorTotal = Uteis.arredondarForcando2CasasDecimais(valorTotal);
            }

            StringBuilder observacao = new StringBuilder();
            observacao.append("QTD TOTAL: ").append(qtdUtilizado).append(" \n");
            if (qtdBonusDisponivel > 0) {
                observacao.append("QTD BÔNUS: ").append(qtdBonusDisponivel).append(" \n");
            }
            if (qtdBonusUtilizada > 0) {
                observacao.append("QTD BÔNUS UTILIZADO: ").append(qtdBonusUtilizada).append(" \n");
            }
            if (qtdBonusRestante > 0) {
                observacao.append("QTD BÔNUS RESTANTE: ").append(qtdBonusRestante).append(" \n");
            }
            observacao.append("QTD REMESSA: ").append(qtdCreditoUtilizadoRemessa).append(" \n");
            observacao.append("QTD TRANSACAO: ").append(qtdCreditoUtilizadoTransacao).append(" \n");
            observacao.append("QTD PIX: ").append(qtdCreditoUtilizadoPix);
            observacao.append("QTD BOLETO ONLINE: ").append(qtdCreditoUtilizadoBoletoOnline);
            observacao.append("QTD BOLETO REMESSA: ").append(qtdCreditoUtilizadoBoletoRemessa);

            List<ItemCobrancaPactoJSON> listaCompleta = new ArrayList<ItemCobrancaPactoJSON>();
            listaCompleta.addAll(listaCompletaRemessaItem);
            listaCompleta.addAll(listaCompletaTransacao);
            listaCompleta.addAll(listaCompletaPix);
            listaCompleta.addAll(listaCompletaBoletoOnline);
            listaCompleta.addAll(listaCompletaBoletoRemessa);

            JSONObject jsonCobranca = criarJSONCobranca("processarClientePosPagoCobrancaPactoLancarContaFinanceiro", codigoEmpresa, tipoCobrancaPacto, qtdUtilizado,
                    qtdCreditoUtilizadoRemessa, qtdCreditoUtilizadoTransacao, qtdCreditoUtilizadoPix, qtdCreditoUtilizadoBoletoOnline, qtdCreditoUtilizadoBoletoRemessa, valorTotal,
                    null, null, null, null, null, null, valorCreditoPacto,  qtdBonusDisponivel,
                    qtdBonusUtilizada, qtdBonusRestante, nomeUsuarioOAMD, justificativa, null);

            inserirLogCobrancaPacto(qtdCobrar, valorTotal, qtdParcelas, gerarNota, nomeUsuarioOAMD, codigoEmpresa, justificativa,
                    gerarCobrancaFinanceiro, observacao.toString(), new JSONArray(listaCompleta).toString(), jsonCobranca, tipoCobrancaPacto, "");

            if (gerarCobrancaFinanceiro && valorTotal > 0.0) {
                lancarContaFinanceiroPacto(key, codigoEmpresa, qtdCobrar, qtdParcelas, valorTotal, gerarNota, qtdBonusUtilizada, tipoCobrancaPacto, null, null);
            }

            con.commit();
            return "Sucesso";
        } catch (Exception e) {
            con.rollback();
            con.setAutoCommit(true);
            return e.getMessage();
        } finally {
            con.setAutoCommit(true);
        }
    }

    public void lancarContaFinanceiroPacto(String key, Integer codigoEmpresa, int qtdCredito, Integer qtdParcelas, Double valorTotal,
                                           boolean gerarNota, Integer qtdBonus, Integer tipoCobrancaPacto, Integer diaVencimentoCobrancaPacto,
                                           Date mesReferencia) throws Exception {
        try {
            StringBuilder descricaoAgendamento = new StringBuilder();
            descricaoAgendamento.append("AQUISIÇÃO DE TRANSAÇÕES DE RECORRENCIA ").append(qtdCredito).append(" (CREDITOS)");

            Date dataVencimento1Parcela = Uteis.somarDias(Calendario.hoje(), 16);

            if (tipoCobrancaPacto != null &&
                    tipoCobrancaPacto.equals(TipoCobrancaPactoEnum.POS_PAGO_EFETIVADO_MENSAL.getCodigo())) {

                descricaoAgendamento = new StringBuilder();
                descricaoAgendamento.append("RECORRÊNCIA MENSAL ").append(qtdCredito).append(" (CREDITOS)");
                if (mesReferencia != null) {
                    descricaoAgendamento.append(" - REF: ").append(Uteis.getDataMesAnoConcatenado(mesReferencia));
                }


                // se estiver informado o dia do vencimento
                if (!UteisValidacao.emptyNumber(diaVencimentoCobrancaPacto)) {
                    //se for menor ou igual o dia atual então lança para o próximo mês
                    if (diaVencimentoCobrancaPacto <= Uteis.getDiaMesData(Calendario.hoje())) {
                        dataVencimento1Parcela = Calendario.setDiaMes(Calendario.somarMeses(Calendario.hoje(), 1), diaVencimentoCobrancaPacto);
                    } else {
                        dataVencimento1Parcela = Calendario.setDiaMes(Calendario.hoje(), diaVencimentoCobrancaPacto);
                    }
                }
            }

            String data1Parcela = Uteis.getData(dataVencimento1Parcela);

            TEfeitoParcelaAgendamento efeito;
            if (gerarNota) {
                efeito = TEfeitoParcelaAgendamento.epaFiscal;
            } else {
                efeito = TEfeitoParcelaAgendamento.epaGerencial;
            }

            FinanceiroPacto financeiroPacto = new FinanceiroPacto(con);
            TResultadoTransacaoWS resultadoTransacaoWS = financeiroPacto.lancarAgendamento(key, codigoEmpresa, descricaoAgendamento.toString(), valorTotal, qtdParcelas, data1Parcela, efeito);

            if (!resultadoTransacaoWS.equals(TResultadoTransacaoWS.rtOK)) {
                throw new Exception(resultadoTransacaoWS.getValue());
            }
        } catch (Exception ex) {
            enviarEmailErro("lancarContaFinanceiroPacto | " + ex.getMessage(), codigoEmpresa);
            throw ex;
        }
    }

    public void processarCobrancaPacto(Date dia) {
        Uteis.logarDebug("Iniciando | Processamento de Cobrança Pacto...");
        for (EmpresaVO empresaVO : listaEmpresas) {
            try {
                TipoCobrancaPactoEnum tipoCobrancaPactoEnum = TipoCobrancaPactoEnum.getConsultarPorCodigo(empresaVO.getTipoCobrancaPacto());
                assert tipoCobrancaPactoEnum != null;

                Uteis.logarDebug("Processamento de Cobrança Pacto ... TIPO COBRANÇA -- " + tipoCobrancaPactoEnum.getDescricao().toUpperCase() + " | CodEmpresa " + empresaVO.getCodigo());

                if (empresaVO.getTipoCobrancaPacto().equals(TipoCobrancaPactoEnum.PRE_PAGO.getCodigo())) {
                    if (empresaVO.isGerarCobrancaAutomaticaPacto()) {
                        marcarItensComoContabilizadoPrePago(empresaVO);
                        processoAutomaticoPrePago(empresaVO);
                    } else {
                        Uteis.logarDebug("Empresa não habilitada GerarCobrancaAutomaticaPacto | CodEmpresa " + empresaVO.getCodigo());
                    }
                } else if (empresaVO.getTipoCobrancaPacto().equals(TipoCobrancaPactoEnum.REDE_EMPRESAS_PRE_PAGO.getCodigo())) {
                    //sempre processa não precisa validar GerarCobrancaAutomaticaPacto
                    processoAutomaticoRedePrePago(empresaVO);
                } else if (empresaVO.getTipoCobrancaPacto().equals(TipoCobrancaPactoEnum.PRE_PAGO_EFETIVADO.getCodigo())) {
                    //sempre processa não precisa validar GerarCobrancaAutomaticaPacto
                    processoAutomaticoPrePagoEfetivado(empresaVO);
                } else if (empresaVO.getTipoCobrancaPacto().equals(TipoCobrancaPactoEnum.POS_PAGO_EFETIVADO_MENSAL.getCodigo())) {
                    if (empresaVO.isGerarCobrancaAutomaticaPacto()) {
                        processoAutomaticoPosPagoMensal(empresaVO, dia);
                    } else {
                        Uteis.logarDebug("Empresa não habilitada GerarCobrancaAutomaticaPacto | CodEmpresa " + empresaVO.getCodigo());
                    }
                } else if (empresaVO.getTipoCobrancaPacto().equals(TipoCobrancaPactoEnum.POS_PAGO_EFETIVADO_MENSAL_REDE_EMPRESAS.getCodigo())) {
                    if (empresaVO.isGerarCobrancaAutomaticaPacto()) {
                        if (empresaVO.isEmpresaResponsavelCobrancaPacto()) {
                            processoAutomaticoPosPagoMensalEmpresaResponsavelCobranca(empresaVO, dia);
                        } else {
                            Uteis.logarDebug(TipoCobrancaPactoEnum.POS_PAGO_EFETIVADO_MENSAL_REDE_EMPRESAS.getDescricao() + " | Porem não é a empresa responsável pelo processamento das cobranças | CodEmpresa: " + empresaVO.getCodigo());
                        }
                    } else {
                        Uteis.logarDebug("Empresa não habilitada GerarCobrancaAutomaticaPacto | CodEmpresa " + empresaVO.getCodigo());
                    }
                } else if (empresaVO.getTipoCobrancaPacto().equals(TipoCobrancaPactoEnum.POS_PAGO_EFETIVADO.getCodigo()) ||
                        empresaVO.getTipoCobrancaPacto().equals(TipoCobrancaPactoEnum.POS_PAGO_TENTATIVA.getCodigo())) {
                    if (empresaVO.isGerarCobrancaAutomaticaPacto()) {
                        processoAutomaticoPosPago(empresaVO, dia);
                    } else {
                        Uteis.logarDebug("Empresa não habilitada GerarCobrancaAutomaticaPacto | CodEmpresa " + empresaVO.getCodigo());
                    }
                } else {
                    Uteis.logarDebug("Não identificado tipo de cobranca | Empresa - CodEmpresa: " + empresaVO.getCodigo());
                    enviarEmailErroGeral("Não identificado tipo de cobranca | CodEmpresa " + empresaVO.getCodigo(), empresaVO.getCodigo());
                }

                marcarItensVindiComoContabilizado(empresaVO);
            } catch (Exception e) {
                Uteis.logarDebug("# ERRO ao processar Cobrança Pacto - CodEmpresa: " + empresaVO.getCodigo() + " - " + e.getMessage());
            }
        }
        Uteis.logarDebug("Finalizando | Processamento Cobrança Pacto!");
        preencherCreditoPactoHistorico();
    }

    private void preencherCreditoPactoHistorico() {
        CreditoPactoHistorico creditoDAO;
        try {
            creditoDAO = new CreditoPactoHistorico(this.con);
            creditoDAO.processarTodasEmpresas(Calendario.hoje());
        } catch (Exception ex) {
            ex.printStackTrace();
        } finally {
            creditoDAO = null;
        }
    }

    private boolean isCobrarCreditoPactoBoleto(int codEmpresa) {
        Empresa empresaDAO;
        try {
            empresaDAO = new Empresa(this.con);
            return empresaDAO.isCobrarCreditoPactoBoleto(codEmpresa);
        } catch (Exception ex) {
            ex.printStackTrace();
            return false;
        } finally {
            empresaDAO = null;
        }
    }

    private Integer obterQtdAlunosAtivosComAutorizacaoCobranca(EmpresaVO empresaVO) throws Exception {
        Cliente cliente = new Cliente(con);
        Integer qtdAlunosAtivosDCC = cliente.consultarQtdClientesPorSituacaoComAutorizacaoCobranca("AT", empresaVO.getCodigo());
        cliente = null;
        return (qtdAlunosAtivosDCC * 10 / 100) + qtdAlunosAtivosDCC;
    }

    private void processoAutomaticoPrePago(EmpresaVO empresa) throws Exception {
        Integer qtdAlunosAtivosDCCMargem = obterQtdAlunosAtivosComAutorizacaoCobranca(empresa);
        Integer qtdCreditoDisponivel = empresa.getCreditoDCC();

        if (qtdCreditoDisponivel <= qtdAlunosAtivosDCCMargem) {
            Integer qtdCreditoAdicionar = empresa.getQtdCreditoRenovarPrePagoCobrancaPacto();
            Double valorTotal = qtdCreditoAdicionar * empresa.getValorCreditoPacto();

            String retorno = processarClientePrePagoCobrancaPactoLancarContaFinanceiro(getKey(), empresa.getCodigo(), qtdCreditoAdicionar,
                    empresa.getQtdParcelasCobrancaPacto(), valorTotal, empresa.isGerarNotaFiscalCobrancaPacto(), PROCESSO_AUTOMATICO, "", true, empresa.getTipoCobrancaPacto());

            if (!retorno.startsWith("Sucesso")) {
                throw new Exception(retorno);
            }

            Uteis.logarDebug("Gerada Cobranca Pre-Pago - CodEmpresa " + empresa.getCodigo());
        } else {
            Uteis.logarDebug("Não foi necessário gerar Cobranca Pre-Pago - Qtd Crédito Disponível: " + qtdCreditoDisponivel + " - Qtd Alunos Ativos + 10%: " + qtdAlunosAtivosDCCMargem + " - CodEmpresa " + empresa.getCodigo());
        }
    }

    private void processoAutomaticoPosPago(EmpresaVO empresa, Date dia) throws Exception {
        Integer qtdDiasDesdeUltimaCobranca = 0;
        if (empresa.getDtUltimaCobrancaPacto() != null) {
            Long l = Uteis.nrDiasEntreDatas(empresa.getDtUltimaCobrancaPacto(), dia);
            qtdDiasDesdeUltimaCobranca = l.intValue();
        }

        //VERIFICAR SE DESDE A ULTIMA COBRANCA
        if (empresa.getDtUltimaCobrancaPacto() == null || ((!qtdDiasDesdeUltimaCobranca.equals(0) && !empresa.getQtdDiasFechamentoCobrancaPacto().equals(0)) &&
                (qtdDiasDesdeUltimaCobranca >= empresa.getQtdDiasFechamentoCobrancaPacto()))) {

            String retorno = processarClientePosPagoCobrancaPactoLancarContaFinanceiro(getKey(), empresa.getCodigo(), empresa.getTipoCobrancaPacto(), empresa.getQtdParcelasCobrancaPacto(),
                    0.0, //PROCESSO AUTOMÁTICO PROCESSA O VALOR A SER COBRADO DENTRO DO MÉTODO (processarClientePosPagoCobrancaPactoLancarContaFinanceiro)
                    empresa.getValorCreditoPacto(),
                    empresa.isGerarNotaFiscalCobrancaPacto(), PROCESSO_AUTOMATICO, "", true, true);

            if (!retorno.startsWith("Sucesso")) {
                throw new Exception(retorno);
            }

        } else {
            Uteis.logarDebug("Não foi necessário gerar Cobranca Pos-Pago - CodEmpresa " + empresa.getCodigo());
        }
    }

    private void processoAutomaticoRedePrePago(EmpresaVO empresa) throws Exception {
        Empresa empresaDao = null;
        try {
            empresaDao = new Empresa(con);

            //CONSULTAR REMESSAITEM
            List<Integer> listaRemessa = obterListaRemessasItem(empresa.getCodigo(), empresa.getTipoCobrancaPacto(), null);
            Integer qtdCreditoUtilizadoRemessa = listaRemessa.size();
            Uteis.logarDebug("processoAutomaticoRedePrePago - Qtd Credito Utilizado REMESSA: " + qtdCreditoUtilizadoRemessa + " - CodEmpresa " + empresa.getCodigo());

            //CONSULTAR TRANSACOES
            List<Integer> listaTransacao = obterListaTransacao(empresa.getCodigo(), empresa.getTipoCobrancaPacto(), null);
            Integer qtdCreditoUtilizadoTransacao = listaTransacao.size();
            Uteis.logarDebug("processoAutomaticoRedePrePago - Qtd Credito Utilizado TRANSAÇÃO: " + qtdCreditoUtilizadoTransacao + " - CodEmpresa " + empresa.getCodigo());

            //CONSULTAR PIX
            List<Integer> listaPix = obterListaPix(empresa.getCodigo(), empresa.getTipoCobrancaPacto(), null);
            Integer qtdCreditoUtilizadoPix = listaPix.size();
            Uteis.logarDebug("processoAutomaticoRedePrePago - Qtd Credito Utilizado PIX: " + qtdCreditoUtilizadoPix + " - CodEmpresa " + empresa.getCodigo());

            boolean cobrarCreditoPactoBoleto = isCobrarCreditoPactoBoleto(empresa.getCodigo());

            //CONSULTAR BOLETOS ONLINE
            List<Integer> listaBoletosOnline = new ArrayList<>();
            Integer qtdCreditoUtilizadoBoletoOnline = 0;
            if (cobrarCreditoPactoBoleto) {
                listaBoletosOnline = obterListaBoletoOnline(empresa.getCodigo(), empresa.getTipoCobrancaPacto(), null);
                qtdCreditoUtilizadoBoletoOnline = listaBoletosOnline.size();
                Uteis.logarDebug("processoAutomaticoRedePrePago - Qtd Credito Utilizado BOLETO ONLINE: " + qtdCreditoUtilizadoBoletoOnline + " - CodEmpresa " + empresa.getCodigo());
            } else {
                Uteis.logarDebug("processoAutomaticoRedePrePago - Qtd Credito Utilizado BOLETO ONLINE: NÃO É COBRADO BOLETO" + " - CodEmpresa " + empresa.getCodigo());
            }

            //CONSULTAR BOLETOS REMESSA
            List<Integer> listaBoletosRemessa = new ArrayList<>();
            Integer qtdCreditoUtilizadoBoletoRemessa = 0;
            if (cobrarCreditoPactoBoleto) {
                listaBoletosRemessa = obterListaBoletoRemessa(empresa.getCodigo(), empresa.getTipoCobrancaPacto(), null);
                qtdCreditoUtilizadoBoletoRemessa = listaBoletosRemessa.size();
                Uteis.logarDebug("processoAutomaticoRedePrePago - Qtd Credito Utilizado BOLETO REMESSA: " + qtdCreditoUtilizadoBoletoRemessa + " - CodEmpresa " + empresa.getCodigo());
            } else {
                Uteis.logarDebug("processoAutomaticoRedePrePago - Qtd Credito Utilizado BOLETO REMESSA: NÃO É COBRADO BOLETO" + " - CodEmpresa " + empresa.getCodigo());
            }

            Integer qtdCreditoUtilizado = (qtdCreditoUtilizadoRemessa + qtdCreditoUtilizadoTransacao + qtdCreditoUtilizadoPix + qtdCreditoUtilizadoBoletoOnline + qtdCreditoUtilizadoBoletoRemessa);
            String urlOamd = PropsService.getPropertyValue(getKey(), PropsService.urlOamd);

            if (qtdCreditoUtilizado > 0) {

                empresaDao.debitarCreditoDCCRede(urlOamd, key, qtdCreditoUtilizado, empresa.getCodigo());

                marcarItensComoContabilizadoPacto(listaRemessa, listaTransacao, listaPix, listaBoletosOnline, listaBoletosRemessa);

                Uteis.logarDebug("processoAutomaticoRedePrePago - Qtd Credito Utilizado: " + qtdCreditoUtilizado + " - CodEmpresa " + empresa.getCodigo());
            } else {
                Uteis.logarDebug("processoAutomaticoRedePrePago - NÃO FOI GERADA - Qtd Credito Utilizado: " + qtdCreditoUtilizado + " - CodEmpresa " + empresa.getCodigo());
            }

            if (empresa.isGerarCobrancaAutomaticaPacto()) {
                JSONObject info = empresaDao.obterInfoRedeDCC(urlOamd, getKey());
                Integer qtdCreditoDisponivel = info.getInt("creditos");
                if (qtdCreditoDisponivel <= 500) {

                    Integer qtdCreditoAdicionar = empresa.getQtdCreditoRenovarPrePagoCobrancaPacto();
                    Double valorTotal = qtdCreditoAdicionar * empresa.getValorCreditoPacto();

                    Uteis.logarDebug("processoAutomaticoRedePrePago - Adicionar crédito rede: " + qtdCreditoAdicionar + " - CodEmpresa " + empresa.getCodigo());

                    empresaDao.addCreditoDCCRede(urlOamd, getKey());

                    String retorno = processarClientePrePagoCobrancaPactoLancarContaFinanceiro(getKey(), empresa.getCodigo(), qtdCreditoAdicionar,
                            empresa.getQtdParcelasCobrancaPacto(), valorTotal,
                            empresa.isGerarNotaFiscalCobrancaPacto(), PROCESSO_AUTOMATICO, "", true, empresa.getTipoCobrancaPacto());

                    if (!retorno.startsWith("Sucesso")) {
                        throw new Exception(retorno);
                    }

                    Uteis.logarDebug("Gerada Cobranca Pre-Pago - CodEmpresa " + empresa.getCodigo());
                } else {
                    Uteis.logarDebug("Não foi necessário gerar Cobranca Pre-Pago - CodEmpresa " + empresa.getCodigo() + " | qtdCreditoDisponivel: " + qtdCreditoDisponivel);
                }
            } else {
                Uteis.logarDebug("GerarCobrancaAutomaticaPacto = FALSE | - CodEmpresa " + empresa.getCodigo());
            }
        } finally {
            empresaDao = null;
        }

    }

    public String ajustarEmpresaParaIniciarPosPago(Integer empresa, Date dataAlteracao, String nomeUsuarioOAMD) throws SQLException {
        try {
            con.setAutoCommit(false);

            StringBuilder update = new StringBuilder();
            update.append("UPDATE empresa SET dtultimacobrancapacto = '").append(Uteis.getDataJDBCTimestamp(dataAlteracao)).append("' WHERE codigo = ").append(empresa).append(";\n");
            update.append("UPDATE remessaitem SET contabilizadapacto = true WHERE contabilizadapacto = false AND remessa in (select codigo from remessa where dataregistro < '").append(Uteis.getDataJDBCTimestamp(dataAlteracao)).append("' AND empresa = ").append(empresa).append(");\n");
            update.append("UPDATE remessaitem SET contabilizadapacto = false WHERE contabilizadapacto = true AND remessa in (select codigo from remessa where dataregistro >= '").append(Uteis.getDataJDBCTimestamp(dataAlteracao)).append("' AND empresa = ").append(empresa).append(");\n");
            update.append("UPDATE transacao SET contabilizadapacto = true WHERE contabilizadapacto = false AND dataprocessamento < '").append(Uteis.getDataJDBCTimestamp(dataAlteracao)).append("' AND empresa = ").append(empresa).append(";\n");
            update.append("UPDATE transacao SET contabilizadapacto = false WHERE contabilizadapacto = true AND dataprocessamento >= '").append(Uteis.getDataJDBCTimestamp(dataAlteracao)).append("' AND empresa = ").append(empresa).append(";\n");
            update.append("UPDATE pix SET contabilizadapacto = false WHERE contabilizadapacto = true AND data >= '").append(Uteis.getDataJDBCTimestamp(dataAlteracao)).append("' AND empresa = ").append(empresa).append(";\n");
            update.append("UPDATE boleto SET contabilizadapacto = false WHERE contabilizadapacto = true AND dataregistro >= '").append(Uteis.getDataJDBCTimestamp(dataAlteracao)).append("' AND empresa = ").append(empresa).append(";\n");

            SuperFacadeJDBC.executarConsulta(update.toString(), getCon());

            registrarLogAjustarEmpresaParaIniciarPosPago(empresa, dataAlteracao, nomeUsuarioOAMD, update.toString());

            con.commit();
            return "Sucesso";
        } catch (Exception e) {
            con.rollback();
            con.setAutoCommit(true);
            return "ERRO: " + e.getMessage();
        } finally {
            con.setAutoCommit(true);
        }
    }

    private void registrarLogAjustarEmpresaParaIniciarPosPago(Integer codigoEmpresa, Date dataAlteracao, String nomeUsuarioOAMD, String update) throws Exception {

        StringBuilder novo = new StringBuilder();
        novo.append("AJUSTAR EMPRESA PARA INICIAR COBRANÇA POS-PAGO PACTO: \n");
        novo.append("\ncodigo: ").append(codigoEmpresa);
        novo.append("\ndataInicial: ").append(Uteis.getDataComHHMM(dataAlteracao));
        novo.append("\nnomeUsuarioOAMD: ").append(nomeUsuarioOAMD);
        novo.append("\nComandos Executados: \n").append(update);

        //REGISTRAR LOG
        String sqlLog = "insert into log(dataalteracao, pessoa, nomeentidade, nomeentidadedescricao, chaveprimaria, nomecampo, valorcampoanterior, valorcampoalterado, responsavelalteracao, operacao) " +
                "values (now(), 0, ?, ?, ?, ?, ?, ?, ?, ?);";

        try (PreparedStatement pst = con.prepareStatement(sqlLog)) {
            int i = 0;
            pst.setString(++i, "EMPRESA");
            pst.setString(++i, "Empresa - Info Cobranca Pacto");
            pst.setString(++i, codigoEmpresa.toString());
            pst.setString(++i, "AJUSTAR EMPRESA PARA INICIAR COBRANÇA POS-PAGO PACTO");
            pst.setString(++i, "");
            pst.setString(++i, novo.toString());
            pst.setString(++i, "USUARIO OAMD: " + nomeUsuarioOAMD);
            pst.setString(++i, "ALTERAÇÃO");
            pst.execute();
        }
    }

    public Integer consultarItensCobrancaPactoTransacaoTotal(Integer empresa, Integer tipoCobrancaPacto, Date mesReferencia) throws Exception {
        return consultarItensCobrancaPactoTransacaoTotal(empresa, tipoCobrancaPacto, mesReferencia, false, null, null);
    }

    public Integer consultarItensCobrancaPactoTransacaoTotal(Integer empresa, Integer tipoCobrancaPacto, Date mesReferencia,
                                                             Boolean contabilizadaPacto, Date dataInicio, Date dataFinal) throws Exception {
        StringBuilder sql = consultarItensCobrancaPactoTransacaoSQL(empresa, tipoCobrancaPacto, mesReferencia, contabilizadaPacto, dataInicio, dataFinal, true);
        try (PreparedStatement stm = con.prepareStatement(sql.toString())) {
            try (ResultSet rs = stm.executeQuery()) {
                if (rs.next()) {
                    return rs.getInt(1);
                }
            }
        }
        return 0;
    }

    public List<ItemCobrancaPactoJSON> consultarItensCobrancaPactoTransacao(Integer empresa, Integer tipoCobrancaPacto, Date mesReferencia) throws Exception {
        StringBuilder sql = consultarItensCobrancaPactoTransacaoSQL(empresa, tipoCobrancaPacto, mesReferencia, false, null, null, false);
        List<ItemCobrancaPactoJSON> lista = new ArrayList<>();
        try (PreparedStatement stm = con.prepareStatement(sql.toString())) {
            try (ResultSet rs = stm.executeQuery()) {
                while (rs.next()) {
                    ItemCobrancaPactoJSON item = new ItemCobrancaPactoJSON();
                    item.setMovParcela(rs.getInt("movparcela"));
                    item.setDescricaoParcela(rs.getString("descricaoParcela"));
                    item.setPessoa(rs.getInt("pessoa"));
                    item.setNome(rs.getString("nome"));
                    item.setValor(Uteis.arredondarForcando2CasasDecimais(rs.getDouble("valor")));
                    item.setData(Uteis.getDataAplicandoFormatacao(rs.getTimestamp("data"), "dd/MM/yyyy HH:mm:ss"));
                    item.setTransacao(rs.getInt("transacao"));
                    lista.add(item);
                }
            }
        }
        return lista;
    }

    public StringBuilder consultarItensCobrancaPactoTransacaoSQL(Integer empresa, Integer tipoCobrancaPacto, Date mesReferencia,
                                                                 Boolean contabilizadaPacto, Date dataInicio, Date dataFinal, boolean count) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT \n");
        if (count) {
            sql.append("count(distinct(t.codigo)) as qtd \n");
        } else {
            sql.append("t.codigo as transacao, \n");
            sql.append("tm.movparcela, \n");
            sql.append("m.descricao as descricaoParcela, \n");
            sql.append("t.pessoapagador as pessoa, \n");
            sql.append("p.nome, \n");
            sql.append("CASE WHEN m.codigo is not null THEN m.valorparcela \n");
            sql.append("ELSE t.valor END as valor,  \n");
            sql.append("t.dataprocessamento as data \n");
        }
        sql.append("FROM transacao t \n");
        sql.append("INNER JOIN empresa e on e.codigo = t.empresa \n");
        sql.append("LEFT JOIN pessoa p on t.pessoapagador = p.codigo \n");
        sql.append("LEFT JOIN transacaomovparcela tm on tm.transacao = t.codigo \n");
        sql.append("LEFT JOIN movparcela m on m.codigo = tm.movparcela \n");
        sql.append("WHERE t.empresa = ").append(empresa).append(" \n");
        sql.append("AND t.transacaoVerificarCartao = false \n");
        sql.append("AND (t.tipo <> ").append(TipoTransacaoEnum.VINDI.getId()).append(" or (t.tipo = ").append(TipoTransacaoEnum.VINDI.getId()).append(" and e.cobrarcreditovindi)) \n");
        if (contabilizadaPacto != null) {
            sql.append("AND t.contabilizadapacto = ").append(contabilizadaPacto).append(" \n");
        }

        if (tipoCobrancaPacto.equals(TipoCobrancaPactoEnum.POS_PAGO_EFETIVADO.getCodigo()) ||
                tipoCobrancaPacto.equals(TipoCobrancaPactoEnum.REDE_EMPRESAS_PRE_PAGO.getCodigo()) ||
                tipoCobrancaPacto.equals(TipoCobrancaPactoEnum.PRE_PAGO_EFETIVADO.getCodigo()) ||
                tipoCobrancaPacto.equals(TipoCobrancaPactoEnum.POS_PAGO_EFETIVADO_MENSAL.getCodigo())) {

            sql.append("AND t.situacao in (").append(SituacaoTransacaoEnum.CONCLUIDA_COM_SUCESSO.getId()).append(",");
            sql.append(SituacaoTransacaoEnum.CANCELADA.getId()).append(",").append(SituacaoTransacaoEnum.ESTORNADA.getId()).append(") \n");
        }

        if (dataInicio != null && dataFinal != null) {
            sql.append("AND t.dataprocessamento::date >= '").append(Uteis.getDataFormatoBD(dataInicio)).append("' \n");
            sql.append("AND t.dataprocessamento::date <= '").append(Uteis.getDataFormatoBD(dataFinal)).append("' \n");
        } else if (tipoCobrancaPacto.equals(TipoCobrancaPactoEnum.POS_PAGO_EFETIVADO_MENSAL.getCodigo()) ||
                tipoCobrancaPacto.equals(TipoCobrancaPactoEnum.POS_PAGO_EFETIVADO_MENSAL_REDE_EMPRESAS.getCodigo())) {
            if (mesReferencia == null) {
                mesReferencia = Calendario.somarMeses(Calendario.hoje(), -1);
            }
            sql.append("AND t.dataprocessamento::date >= '").append(Uteis.getDataFormatoBD(Uteis.obterPrimeiroDiaMes(mesReferencia))).append("' \n");
            sql.append("AND t.dataprocessamento::date <= '").append(Uteis.getDataFormatoBD(Uteis.obterUltimoDiaMes(mesReferencia))).append("' \n");
        }
        return sql;
    }

    public Integer consultarItensCobrancaPactoRemessasItemTotal(Integer empresa, Integer tipoCobrancaPacto, Date mesReferencia) throws Exception {
        return consultarItensCobrancaPactoRemessasItemTotal(empresa, tipoCobrancaPacto, mesReferencia, false, null, null);
    }

    public Integer consultarItensCobrancaPactoRemessasItemTotal(Integer empresa, Integer tipoCobrancaPacto, Date mesReferencia,
                                                                Boolean contabilizadaPacto, Date dataInicio, Date dataFinal) throws Exception {
        StringBuilder sql = consultarItensCobrancaPactoRemessasItemSQL(empresa, tipoCobrancaPacto, mesReferencia, contabilizadaPacto, dataInicio, dataFinal, true);
        try (PreparedStatement stm = con.prepareStatement(sql.toString())) {
            try (ResultSet rs = stm.executeQuery()) {
                if (rs.next()) {
                    return rs.getInt(1);
                }
            }
        }
        return 0;
    }

    public List<ItemCobrancaPactoJSON> consultarItensCobrancaPactoRemessasItem(Integer empresa, Integer tipoCobrancaPacto, Date mesReferencia) throws Exception {
        StringBuilder sql = consultarItensCobrancaPactoRemessasItemSQL(empresa, tipoCobrancaPacto, mesReferencia, false, null, null, false);

        List<ItemCobrancaPactoJSON> lista = new ArrayList<ItemCobrancaPactoJSON>();
        try (PreparedStatement stm = con.prepareStatement(sql.toString())) {
            try (ResultSet rs = stm.executeQuery()) {
                while (rs.next()) {
                    ItemCobrancaPactoJSON item = new ItemCobrancaPactoJSON();
                    item.setMovParcela(rs.getInt("movparcela"));
                    item.setDescricaoParcela(rs.getString("descricaoParcela"));
                    item.setPessoa(rs.getInt("pessoa"));
                    item.setNome(rs.getString("nome"));
                    item.setValor(Uteis.arredondarForcando2CasasDecimais(rs.getDouble("valor")));
                    item.setData(Uteis.getDataAplicandoFormatacao(rs.getTimestamp("data"), "dd/MM/yyyy HH:mm:ss"));
                    item.setRemessaItem(rs.getInt("remessaItem"));
                    lista.add(item);
                }
            }
        }
        return lista;
    }

    public StringBuilder consultarItensCobrancaPactoRemessasItemSQL(Integer empresa, Integer tipoCobrancaPacto, Date mesReferencia,
                                                                    Boolean contabilizadaPacto, Date dataInicio, Date dataFinal, boolean count) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT  \n");
        if (count) {
            sql.append("count(distinct(ri.codigo)) as qtd \n");
        } else {
            sql.append("ri.codigo as remessaItem,  \n");
            sql.append("mp.codigo as movparcela,  \n");
            sql.append("mp.descricao as descricaoParcela,  \n");
            sql.append("ri.pessoa,  \n");
            sql.append("p.nome,  \n");
            sql.append("CASE WHEN mp.codigo is not null THEN mp.valorparcela \n");
            sql.append("ELSE ri.valoritemremessa END as valor,  \n");
            sql.append("r.dataregistro as data  \n");
        }
        sql.append("FROM remessaitem ri  \n");
        sql.append("LEFT JOIN remessa r ON r.codigo = ri.remessa  \n");
        sql.append("LEFT JOIN pessoa p ON p.codigo = ri.pessoa  \n");
        sql.append("LEFT JOIN remessaitemmovparcela rim on rim.remessaitem = ri.codigo \n");
        sql.append("LEFT JOIN movparcela mp on (mp.codigo = rim.movparcela or mp.codigo = ri.movparcela) \n");
        sql.append("WHERE r.empresa = ").append(empresa).append(" \n");
        if (contabilizadaPacto != null) {
            sql.append("AND ri.contabilizadaPacto = ").append(contabilizadaPacto).append(" \n");
        }

        sql.append("AND r.tipo in (");
        sql.append(TipoRemessaEnum.EDI_CIELO.getId()).append(",");
        sql.append(TipoRemessaEnum.DCC_BIN.getId()).append(",");
        sql.append(TipoRemessaEnum.GET_NET.getId()).append(") \n");

        if (tipoCobrancaPacto.equals(TipoCobrancaPactoEnum.POS_PAGO_EFETIVADO.getCodigo()) ||
                tipoCobrancaPacto.equals(TipoCobrancaPactoEnum.REDE_EMPRESAS_PRE_PAGO.getCodigo()) ||
                tipoCobrancaPacto.equals(TipoCobrancaPactoEnum.PRE_PAGO_EFETIVADO.getCodigo()) ||
                tipoCobrancaPacto.equals(TipoCobrancaPactoEnum.POS_PAGO_EFETIVADO_MENSAL.getCodigo())) {
            sql.append("AND mp.situacao = 'PG' \n");
            sql.append("AND ri.movpagamento is not null \n");
        }

        if (dataInicio != null && dataFinal != null) {
            sql.append("AND r.dataregistro::date >= '").append(Uteis.getDataFormatoBD(dataInicio)).append("' \n");
            sql.append("AND r.dataregistro::date <= '").append(Uteis.getDataFormatoBD(dataFinal)).append("' \n");
        } else if (tipoCobrancaPacto.equals(TipoCobrancaPactoEnum.POS_PAGO_EFETIVADO_MENSAL.getCodigo()) ||
                tipoCobrancaPacto.equals(TipoCobrancaPactoEnum.POS_PAGO_EFETIVADO_MENSAL_REDE_EMPRESAS.getCodigo())) {
            if (mesReferencia == null) {
                mesReferencia = Calendario.somarMeses(Calendario.hoje(), -1);
            }
            sql.append("AND r.dataregistro::date >= '").append(Uteis.getDataFormatoBD(Uteis.obterPrimeiroDiaMes(mesReferencia))).append("' \n");
            sql.append("AND r.dataregistro::date <= '").append(Uteis.getDataFormatoBD(Uteis.obterUltimoDiaMes(mesReferencia))).append("' \n");
        }
        return sql;
    }

    public Integer consultarItensCobrancaPactoPixTotal(Integer empresa, Integer tipoCobrancaPacto, Date mesReferencia) throws Exception {
        return consultarItensCobrancaPactoPixTotal(empresa, tipoCobrancaPacto, mesReferencia, false, null, null);
    }

    public Integer consultarItensCobrancaPactoPixTotal(Integer empresa, Integer tipoCobrancaPacto, Date mesReferencia,
                                                       Boolean contabilizadaPacto, Date dataInicio, Date dataFinal) throws Exception {
        StringBuilder sql = consultarItensCobrancaPactoPixSQL(empresa, tipoCobrancaPacto, mesReferencia, contabilizadaPacto, dataInicio, dataFinal, true);
        try (PreparedStatement stm = con.prepareStatement(sql.toString())) {
            try (ResultSet rs = stm.executeQuery()) {
                if (rs.next()) {
                    return rs.getInt(1);
                }
            }
        }
        return 0;
    }

    public List<ItemCobrancaPactoJSON> consultarItensCobrancaPactoPix(Integer empresa, Integer tipoCobrancaPacto, Date mesReferencia) throws Exception {
        StringBuilder sql = consultarItensCobrancaPactoPixSQL(empresa, tipoCobrancaPacto, mesReferencia, false, null, null, false);

        List<ItemCobrancaPactoJSON> lista = new ArrayList<>();
        try (PreparedStatement stm = con.prepareStatement(sql.toString())) {
            try (ResultSet rs = stm.executeQuery()) {
                while (rs.next()) {
                    ItemCobrancaPactoJSON item = new ItemCobrancaPactoJSON();
                    item.setMovParcela(rs.getInt("movparcela"));
                    item.setDescricaoParcela(rs.getString("descricaoParcela"));
                    item.setPessoa(rs.getInt("pessoa"));
                    item.setNome(rs.getString("nome"));
                    item.setValor(Uteis.arredondarForcando2CasasDecimais(rs.getDouble("valor")));
                    item.setData(Uteis.getDataAplicandoFormatacao(rs.getTimestamp("data"), "dd/MM/yyyy HH:mm:ss"));
                    item.setPix(rs.getInt("pix"));
                    lista.add(item);
                }
            }
        }
        return lista;
    }

    public StringBuilder consultarItensCobrancaPactoPixSQL(Integer empresa, Integer tipoCobrancaPacto, Date mesReferencia,
                                                           Boolean contabilizadaPacto, Date dataInicio, Date dataFinal, boolean count) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT \n");
        if (count) {
            sql.append("count(distinct(pi.codigo)) as qtd \n");
        } else {
            sql.append("pi.codigo as pix, \n");
            sql.append("pm.movparcela, \n");
            sql.append("m.descricao as descricaoParcela, \n");
            sql.append("pi.pessoa, \n");
            sql.append("p.nome, \n");
            sql.append("CASE WHEN m.codigo is not null THEN m.valorparcela \n");
            sql.append("ELSE pi.valor END as valor, \n");
            sql.append("pi.data as data \n");
        }
        sql.append("FROM pix pi \n");
        sql.append("INNER JOIN empresa e on e.codigo = pi.empresa \n");
        sql.append("INNER JOIN pessoa p on pi.pessoa = p.codigo \n");
        sql.append("LEFT JOIN pixmovparcela pm on pm.pix = pi.codigo \n");
        sql.append("LEFT JOIN movparcela m on m.codigo = pm.movparcela \n");
        sql.append("WHERE pi.empresa = ").append(empresa).append(" \n");
        if (contabilizadaPacto != null) {
            sql.append("AND pi.contabilizadapacto = ").append(contabilizadaPacto).append(" \n");
        }

        if (tipoCobrancaPacto.equals(TipoCobrancaPactoEnum.POS_PAGO_EFETIVADO.getCodigo()) ||
                tipoCobrancaPacto.equals(TipoCobrancaPactoEnum.REDE_EMPRESAS_PRE_PAGO.getCodigo()) ||
                tipoCobrancaPacto.equals(TipoCobrancaPactoEnum.PRE_PAGO_EFETIVADO.getCodigo()) ||
                tipoCobrancaPacto.equals(TipoCobrancaPactoEnum.POS_PAGO_EFETIVADO_MENSAL.getCodigo())) {

            sql.append("AND pi.status in ('").append(PixStatusEnum.CONCLUIDA.name()).append("') \n");
        }

        if (dataInicio != null && dataFinal != null) {
            sql.append("AND pi.data::date >= '").append(Uteis.getDataFormatoBD(dataInicio)).append("' \n");
            sql.append("AND pi.data::date <= '").append(Uteis.getDataFormatoBD(dataFinal)).append("' \n");
        } else if (tipoCobrancaPacto.equals(TipoCobrancaPactoEnum.POS_PAGO_EFETIVADO_MENSAL.getCodigo()) ||
                tipoCobrancaPacto.equals(TipoCobrancaPactoEnum.POS_PAGO_EFETIVADO_MENSAL_REDE_EMPRESAS.getCodigo())) {
            if (mesReferencia == null) {
                mesReferencia = Calendario.somarMeses(Calendario.hoje(), -1);
            }
            sql.append("AND pi.data::date >= '").append(Uteis.getDataFormatoBD(Uteis.obterPrimeiroDiaMes(mesReferencia))).append("' \n");
            sql.append("AND pi.data::date <= '").append(Uteis.getDataFormatoBD(Uteis.obterUltimoDiaMes(mesReferencia))).append("' \n");
        }
        return sql;
    }

    public Integer consultarItensCobrancaPactoBoletoOnlineTotal(Integer empresa, Integer tipoCobrancaPacto, Date mesReferencia) throws Exception {
        return consultarItensCobrancaPactoBoletoOnlineTotal(empresa, tipoCobrancaPacto, mesReferencia, false, null, null);
    }

    public Integer consultarItensCobrancaPactoBoletoOnlineTotal(Integer empresa, Integer tipoCobrancaPacto, Date mesReferencia,
                                                                Boolean contabilizadaPacto, Date dataInicio, Date dataFinal) throws Exception {
        StringBuilder sql = consultarItensCobrancaPactoBoletoOnlineSQL(empresa, tipoCobrancaPacto, mesReferencia, contabilizadaPacto, dataInicio, dataFinal, true);
        try (PreparedStatement stm = con.prepareStatement(sql.toString())) {
            try (ResultSet rs = stm.executeQuery()) {
                if (rs.next()) {
                    return rs.getInt(1);
                }
            }
        }
        return 0;
    }

    public List<ItemCobrancaPactoJSON> consultarItensCobrancaPactoBoletoOnline(Integer empresa, Integer tipoCobrancaPacto, Date mesReferencia) throws Exception {
        StringBuilder sql = consultarItensCobrancaPactoBoletoOnlineSQL(empresa, tipoCobrancaPacto, mesReferencia, false, null, null, false);

        List<ItemCobrancaPactoJSON> lista = new ArrayList<>();
        try (PreparedStatement stm = con.prepareStatement(sql.toString())) {
            try (ResultSet rs = stm.executeQuery()) {
                while (rs.next()) {
                    ItemCobrancaPactoJSON item = new ItemCobrancaPactoJSON();
                    item.setMovParcela(rs.getInt("movparcela"));
                    item.setDescricaoParcela(rs.getString("descricaoParcela"));
                    item.setPessoa(rs.getInt("pessoa"));
                    item.setNome(rs.getString("nome"));
                    item.setValor(Uteis.arredondarForcando2CasasDecimais(rs.getDouble("valor")));
                    item.setData(Uteis.getDataAplicandoFormatacao(rs.getTimestamp("data"), "dd/MM/yyyy HH:mm:ss"));
                    item.setBoleto(rs.getInt("boleto"));
                    lista.add(item);
                }
            }
        }
        return lista;
    }

    public StringBuilder consultarItensCobrancaPactoBoletoOnlineSQL(Integer empresa, Integer tipoCobrancaPacto, Date mesReferencia,
                                                                    Boolean contabilizadaPacto, Date dataInicio, Date dataFinal, boolean count) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT \n");
        if (count) {
            sql.append("count(distinct(bol.codigo)) as qtd \n");
        } else {
            sql.append("bol.codigo as boleto, \n");
            sql.append("bmp.movparcela, \n");
            sql.append("m.descricao as descricaoParcela, \n");
            sql.append("bol.pessoa, \n");
            sql.append("p.nome, \n");
            sql.append("CASE WHEN m.codigo is not null THEN m.valorparcela \n");
            sql.append("ELSE bol.valor END as valor, \n");
            sql.append("bol.dataregistro as data \n");
        }
        sql.append("FROM boleto bol \n");
        sql.append("INNER JOIN empresa e on e.codigo = bol.empresa \n");
        sql.append("INNER JOIN pessoa p on bol.pessoa = p.codigo \n");
        sql.append("LEFT JOIN boletomovparcela bmp on bmp.boleto = bol.codigo \n");
        sql.append("LEFT JOIN movparcela m on m.codigo = bmp.movparcela \n");
        sql.append("WHERE bol.empresa = ").append(empresa).append(" \n");
        if (contabilizadaPacto != null) {
            sql.append("AND bol.contabilizadapacto = ").append(contabilizadaPacto).append(" \n");
        }

        if (tipoCobrancaPacto.equals(TipoCobrancaPactoEnum.POS_PAGO_EFETIVADO.getCodigo()) ||
                tipoCobrancaPacto.equals(TipoCobrancaPactoEnum.REDE_EMPRESAS_PRE_PAGO.getCodigo()) ||
                tipoCobrancaPacto.equals(TipoCobrancaPactoEnum.PRE_PAGO_EFETIVADO.getCodigo()) ||
                tipoCobrancaPacto.equals(TipoCobrancaPactoEnum.POS_PAGO_EFETIVADO_MENSAL.getCodigo())) {
            sql.append("AND bol.situacao in (").append(SituacaoBoletoEnum.PAGO.getCodigo()).append(") \n");
        }

        if (dataInicio != null && dataFinal != null) {
            sql.append("AND bol.dataregistro::date >= '").append(Uteis.getDataFormatoBD(dataInicio)).append("' \n");
            sql.append("AND bol.dataregistro::date <= '").append(Uteis.getDataFormatoBD(dataFinal)).append("' \n");
        } else if (tipoCobrancaPacto.equals(TipoCobrancaPactoEnum.POS_PAGO_EFETIVADO_MENSAL.getCodigo()) ||
                tipoCobrancaPacto.equals(TipoCobrancaPactoEnum.POS_PAGO_EFETIVADO_MENSAL_REDE_EMPRESAS.getCodigo())) {
            if (mesReferencia == null) {
                mesReferencia = Calendario.somarMeses(Calendario.hoje(), -1);
            }
            sql.append("AND bol.dataregistro::date >= '").append(Uteis.getDataFormatoBD(Uteis.obterPrimeiroDiaMes(mesReferencia))).append("' \n");
            sql.append("AND bol.dataregistro::date <= '").append(Uteis.getDataFormatoBD(Uteis.obterUltimoDiaMes(mesReferencia))).append("' \n");
        }
        return sql;
    }

    public Integer consultarItensCobrancaPactoBoletoRemessaTotal(Integer empresa, Integer tipoCobrancaPacto, Date mesReferencia) throws Exception {
        return consultarItensCobrancaPactoBoletoRemessaTotal(empresa, tipoCobrancaPacto, mesReferencia, false, null, null);
    }

    public Integer consultarItensCobrancaPactoBoletoRemessaTotal(Integer empresa, Integer tipoCobrancaPacto, Date mesReferencia,
                                                                 Boolean contabilizadaPacto, Date dataInicio, Date dataFinal) throws Exception {
        StringBuilder sql = consultarItensCobrancaPactoBoletoRemessaSQL(empresa, tipoCobrancaPacto, mesReferencia, contabilizadaPacto, dataInicio, dataFinal, true);
        try (PreparedStatement stm = con.prepareStatement(sql.toString())) {
            try (ResultSet rs = stm.executeQuery()) {
                if (rs.next()) {
                    return rs.getInt(1);
                }
            }
        }
        return 0;
    }

    public List<ItemCobrancaPactoJSON> consultarItensCobrancaPactoBoletoRemessa(Integer empresa, Integer tipoCobrancaPacto, Date mesReferencia) throws Exception {
        StringBuilder sql = consultarItensCobrancaPactoBoletoRemessaSQL(empresa, tipoCobrancaPacto, mesReferencia, false, null, null, false);

        List<ItemCobrancaPactoJSON> lista = new ArrayList<ItemCobrancaPactoJSON>();
        try (PreparedStatement stm = con.prepareStatement(sql.toString())) {
            try (ResultSet rs = stm.executeQuery()) {
                while (rs.next()) {
                    ItemCobrancaPactoJSON item = new ItemCobrancaPactoJSON();
                    item.setMovParcela(rs.getInt("movparcela"));
                    item.setDescricaoParcela(rs.getString("descricaoParcela"));
                    item.setPessoa(rs.getInt("pessoa"));
                    item.setNome(rs.getString("nome"));
                    item.setValor(Uteis.arredondarForcando2CasasDecimais(rs.getDouble("valor")));
                    item.setData(Uteis.getDataAplicandoFormatacao(rs.getTimestamp("data"), "dd/MM/yyyy HH:mm:ss"));
                    item.setRemessaItem(rs.getInt("remessaItem"));
                    lista.add(item);
                }
            }
        }
        return lista;
    }

    public StringBuilder consultarItensCobrancaPactoBoletoRemessaSQL(Integer empresa, Integer tipoCobrancaPacto, Date mesReferencia,
                                                                     Boolean contabilizadaPacto, Date dataInicio, Date dataFinal, boolean count) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT  \n");
        if (count) {
            sql.append("count(distinct(ri.codigo)) as qtd \n");
        } else {
            sql.append("ri.codigo as remessaItem,  \n");
            sql.append("mp.codigo as movparcela,  \n");
            sql.append("mp.descricao as descricaoParcela,  \n");
            sql.append("ri.pessoa,  \n");
            sql.append("p.nome,  \n");
            sql.append("CASE WHEN mp.codigo is not null THEN mp.valorparcela \n");
            sql.append("ELSE ri.valoritemremessa END as valor,  \n");
            sql.append("r.dataregistro as data  \n");
        }
        sql.append("FROM remessaitem ri  \n");
        sql.append("LEFT JOIN remessa r ON r.codigo = ri.remessa  \n");
        sql.append("LEFT JOIN pessoa p ON p.codigo = ri.pessoa  \n");
        sql.append("LEFT JOIN remessaitemmovparcela rim on rim.remessaitem = ri.codigo \n");
        sql.append("LEFT JOIN movparcela mp on (mp.codigo = rim.movparcela or mp.codigo = ri.movparcela) \n");
        sql.append("WHERE r.empresa = ").append(empresa).append(" \n");
        if (contabilizadaPacto != null) {
            sql.append("AND ri.contabilizadaPacto = ").append(contabilizadaPacto).append(" \n");
        }

        sql.append("AND r.tipo in (");
        sql.append(TipoRemessaEnum.ITAU_BOLETO.getId()).append(",");
        sql.append(TipoRemessaEnum.DAYCOVAL_BOLETO.getId()).append(",");
        sql.append(TipoRemessaEnum.BOLETO.getId()).append(",");
        sql.append(TipoRemessaEnum.ITAU_CNAB400.getId()).append(") \n");

        if (tipoCobrancaPacto.equals(TipoCobrancaPactoEnum.POS_PAGO_EFETIVADO.getCodigo()) ||
                tipoCobrancaPacto.equals(TipoCobrancaPactoEnum.REDE_EMPRESAS_PRE_PAGO.getCodigo()) ||
                tipoCobrancaPacto.equals(TipoCobrancaPactoEnum.PRE_PAGO_EFETIVADO.getCodigo()) ||
                tipoCobrancaPacto.equals(TipoCobrancaPactoEnum.POS_PAGO_EFETIVADO_MENSAL.getCodigo())) {
            sql.append("AND mp.situacao = 'PG' \n");
            sql.append("AND ri.movpagamento is not null \n");
        }

        if (dataInicio != null && dataFinal != null) {
            sql.append("AND r.dataregistro::date >= '").append(Uteis.getDataFormatoBD(dataInicio)).append("' \n");
            sql.append("AND r.dataregistro::date <= '").append(Uteis.getDataFormatoBD(dataFinal)).append("' \n");
        } else if (tipoCobrancaPacto.equals(TipoCobrancaPactoEnum.POS_PAGO_EFETIVADO_MENSAL.getCodigo()) ||
                tipoCobrancaPacto.equals(TipoCobrancaPactoEnum.POS_PAGO_EFETIVADO_MENSAL_REDE_EMPRESAS.getCodigo())) {
            if (mesReferencia == null) {
                mesReferencia = Calendario.somarMeses(Calendario.hoje(), -1);
            }
            sql.append("AND r.dataregistro::date >= '").append(Uteis.getDataFormatoBD(Uteis.obterPrimeiroDiaMes(mesReferencia))).append("' \n");
            sql.append("AND r.dataregistro::date <= '").append(Uteis.getDataFormatoBD(Uteis.obterUltimoDiaMes(mesReferencia))).append("' \n");
        }
        return sql;
    }

    private List<Integer> obterListaBoletoRemessa(List<ItemCobrancaPactoJSON> lista) {
        List<Integer> remessaItem = new ArrayList<>();
        for (ItemCobrancaPactoJSON item : lista) {
            if (!UteisValidacao.emptyNumber(item.getRemessaItem()) && !remessaItem.contains(item.getRemessaItem())) {
                remessaItem.add(item.getRemessaItem());
            }
        }
        return remessaItem;
    }

    private List<Integer> obterListaRemessasItem(List<ItemCobrancaPactoJSON> lista) {
        List<Integer> remessaItem = new ArrayList<>();
        for (ItemCobrancaPactoJSON item : lista) {
            if (!UteisValidacao.emptyNumber(item.getRemessaItem()) && !remessaItem.contains(item.getRemessaItem())) {
                remessaItem.add(item.getRemessaItem());
            }
        }
        return remessaItem;
    }

    private List<Integer> obterListaTransacao(List<ItemCobrancaPactoJSON> lista) {
        List<Integer> transacao = new ArrayList<>();
        for (ItemCobrancaPactoJSON item : lista) {
            if (!UteisValidacao.emptyNumber(item.getTransacao()) && !transacao.contains(item.getTransacao())) {
                transacao.add(item.getTransacao());
            }
        }
        return transacao;
    }

    private List<Integer> obterListaPix(List<ItemCobrancaPactoJSON> lista) {
        List<Integer> pix = new ArrayList<>();
        for (ItemCobrancaPactoJSON item : lista) {
            if (!UteisValidacao.emptyNumber(item.getPix()) && !pix.contains(item.getPix())) {
                pix.add(item.getPix());
            }
        }
        return pix;
    }

    private List<Integer> obterListaBoletoOnline(List<ItemCobrancaPactoJSON> lista) {
        List<Integer> boleto = new ArrayList<>();
        for (ItemCobrancaPactoJSON item : lista) {
            if (!UteisValidacao.emptyNumber(item.getBoleto()) && !boleto.contains(item.getBoleto())) {
                boleto.add(item.getBoleto());
            }
        }
        return boleto;
    }

    public List<Integer> obterListaBoletoRemessa(Integer empresa, Integer tipoCobrancaPacto, Date mesReferencia) throws Exception {
        List<ItemCobrancaPactoJSON> lista = consultarItensCobrancaPactoBoletoRemessa(empresa, tipoCobrancaPacto, mesReferencia);
        List<Integer> remessaItens = new ArrayList<>();
        for (ItemCobrancaPactoJSON item : lista) {
            if (!UteisValidacao.emptyNumber(item.getRemessaItem()) && !remessaItens.contains(item.getRemessaItem())) {
                remessaItens.add(item.getRemessaItem());
            }
        }
        return remessaItens;
    }

    public Integer obterQuantidadeListaBoletoRemessa(Integer empresa, Integer tipoCobrancaPacto, Date mesReferencia) throws Exception {
        return consultarItensCobrancaPactoBoletoRemessaTotal(empresa, tipoCobrancaPacto, mesReferencia);
    }

    public List<Integer> obterListaRemessasItem(Integer empresa, Integer tipoCobrancaPacto, Date mesReferencia) throws Exception {
        List<ItemCobrancaPactoJSON> lista = consultarItensCobrancaPactoRemessasItem(empresa, tipoCobrancaPacto, mesReferencia);
        List<Integer> remessaItem = new ArrayList<>();
        for (ItemCobrancaPactoJSON item : lista) {
            if (!UteisValidacao.emptyNumber(item.getRemessaItem()) && !remessaItem.contains(item.getRemessaItem())) {
                remessaItem.add(item.getRemessaItem());
            }
        }
        return remessaItem;
    }

    public Integer obterQuantidadeListaRemessasItem(Integer empresa, Integer tipoCobrancaPacto, Date mesReferencia) throws Exception {
        return consultarItensCobrancaPactoRemessasItemTotal(empresa, tipoCobrancaPacto, mesReferencia);
    }

    public List<Integer> obterListaTransacao(Integer empresa, Integer tipoCobrancaPacto, Date mesReferencia) throws Exception {
        List<ItemCobrancaPactoJSON> lista = consultarItensCobrancaPactoTransacao(empresa, tipoCobrancaPacto, mesReferencia);
        List<Integer> transacao = new ArrayList<>();
        for (ItemCobrancaPactoJSON item : lista) {
            if (!UteisValidacao.emptyNumber(item.getTransacao()) && !transacao.contains(item.getTransacao())) {
                transacao.add(item.getTransacao());
            }
        }
        return transacao;
    }

    public Integer obterQuantidadeListaTransacao(Integer empresa, Integer tipoCobrancaPacto, Date mesReferencia) throws Exception {
        return consultarItensCobrancaPactoTransacaoTotal(empresa, tipoCobrancaPacto, mesReferencia);
    }

    public List<Integer> obterListaPix(Integer empresa, Integer tipoCobrancaPacto, Date mesReferencia) throws Exception {
        List<ItemCobrancaPactoJSON> lista = consultarItensCobrancaPactoPix(empresa, tipoCobrancaPacto, mesReferencia);
        List<Integer> pix = new ArrayList<>();
        for (ItemCobrancaPactoJSON item : lista) {
            if (!UteisValidacao.emptyNumber(item.getPix()) && !pix.contains(item.getPix())) {
                pix.add(item.getPix());
            }
        }
        return pix;
    }

    public Integer obterQuantidadeListaPix(Integer empresa, Integer tipoCobrancaPacto, Date mesReferencia) throws Exception {
        return consultarItensCobrancaPactoPixTotal(empresa, tipoCobrancaPacto, mesReferencia);
    }

    public List<Integer> obterListaBoletoOnline(Integer empresa, Integer tipoCobrancaPacto, Date mesReferencia) throws Exception {
        List<ItemCobrancaPactoJSON> lista = consultarItensCobrancaPactoBoletoOnline(empresa, tipoCobrancaPacto, mesReferencia);
        List<Integer> boletosOnline = new ArrayList<>();
        for (ItemCobrancaPactoJSON item : lista) {
            if (!UteisValidacao.emptyNumber(item.getBoleto()) && !boletosOnline.contains(item.getBoleto())) {
                boletosOnline.add(item.getBoleto());
            }
        }
        return boletosOnline;
    }

    public Integer obterQuantidadeListaBoletoOnline(Integer empresa, Integer tipoCobrancaPacto, Date mesReferencia) throws Exception {
        return consultarItensCobrancaPactoBoletoOnlineTotal(empresa, tipoCobrancaPacto, mesReferencia);
    }


    private void marcarItensComoContabilizadoPacto(List<Integer> remessasItem, List<Integer> transacao, List<Integer> pix, List<Integer> boletosOnline, List<Integer> boletosRemessa) throws Exception {

        if (!UteisValidacao.emptyList(remessasItem)) {
            StringBuilder codigosRemessaItem = new StringBuilder();
            for (Integer cod : remessasItem) {
                if (codigosRemessaItem.toString().equals("")) {
                    codigosRemessaItem.append(cod);
                } else {
                    codigosRemessaItem.append(",").append(cod);
                }
            }
            SuperFacadeJDBC.executarConsulta("UPDATE remessaitem SET contabilizadapacto = true WHERE codigo in (" + codigosRemessaItem + ");", getCon());
        }

        if (!UteisValidacao.emptyList(transacao)) {
            StringBuilder codigosTransacao = new StringBuilder();
            for (Integer cod : transacao) {
                if (codigosTransacao.toString().equals("")) {
                    codigosTransacao.append(cod);
                } else {
                    codigosTransacao.append(",").append(cod);
                }
            }
            SuperFacadeJDBC.executarConsulta("UPDATE transacao SET contabilizadapacto = true WHERE codigo in (" + codigosTransacao + ");", getCon());
        }

        if (!UteisValidacao.emptyList(pix)) {
            StringBuilder codigosPix = new StringBuilder();
            for (Integer cod : pix) {
                if (codigosPix.toString().equals("")) {
                    codigosPix.append(cod);
                } else {
                    codigosPix.append(",").append(cod);
                }
            }
            SuperFacadeJDBC.executarConsulta("UPDATE pix SET contabilizadapacto = true WHERE codigo in (" + codigosPix + ");", getCon());
        }

        if (!UteisValidacao.emptyList(boletosOnline)) {
            StringBuilder codigosBoletos = new StringBuilder();
            for (Integer cod : boletosOnline) {
                if (codigosBoletos.toString().equals("")) {
                    codigosBoletos.append(cod);
                } else {
                    codigosBoletos.append(",").append(cod);
                }
            }
            SuperFacadeJDBC.executarConsulta("UPDATE boleto SET contabilizadapacto = true WHERE codigo in (" + codigosBoletos + ");", getCon());
        }

        if (!UteisValidacao.emptyList(boletosRemessa)) {
            StringBuilder codigosBoletoRemessa = new StringBuilder();
            for (Integer cod : boletosRemessa) {
                if (codigosBoletoRemessa.toString().equals("")) {
                    codigosBoletoRemessa.append(cod);
                } else {
                    codigosBoletoRemessa.append(",").append(cod);
                }
            }
            SuperFacadeJDBC.executarConsulta("UPDATE remessaitem SET contabilizadapacto = true WHERE codigo in (" + codigosBoletoRemessa + ");", getCon());
        }
    }

    private void marcarItensVindiComoContabilizado(EmpresaVO empresaVO) throws Exception {
        //TODO: VINDI NÃO GASTA CREDITO DA PACTO
        Uteis.logarDebug("marcarItensVindiComoContabilizado - CodEmpresa: " + empresaVO.getCodigo());
        StringBuilder sql = new StringBuilder();
        sql.append("update transacao set contabilizadapacto  = true where codigo in ( \n");
        sql.append("select \n");
        sql.append("t.codigo \n");
        sql.append("from transacao t \n");
        sql.append("inner join empresa e on e.codigo = t.empresa \n");
        sql.append("where t.contabilizadapacto = false \n");
        sql.append("and t.empresa = ").append(empresaVO.getCodigo()).append(" \n");
        sql.append("and t.tipo = ").append(TipoTransacaoEnum.VINDI.getId()).append(" \n");
        sql.append("and e.cobrarcreditovindi = false) \n");
        SuperFacadeJDBC.executarConsulta(sql.toString(), getCon());
    }

    private void marcarItensComoContabilizadoPrePago(EmpresaVO empresaVO) throws Exception {
        Uteis.logarDebug("marcarItensComoContabilizadoPrePago - CodEmpresa: " + empresaVO.getCodigo());
        SuperFacadeJDBC.executarConsulta("UPDATE transacao SET contabilizadapacto = true WHERE empresa = " + empresaVO.getCodigo() + " AND contabilizadapacto = false;", getCon());
        SuperFacadeJDBC.executarConsulta("UPDATE remessaitem SET contabilizadapacto = true WHERE remessa in (select codigo from remessa where empresa = " + empresaVO.getCodigo() + ") AND contabilizadapacto = false;", getCon());
    }

    private void processoAutomaticoPrePagoEfetivado(EmpresaVO empresaVO) throws Exception {
        try {
            con.setAutoCommit(false);

            //CONSULTAR REMESSAITEM
            List<Integer> remessaItem = obterListaRemessasItem(empresaVO.getCodigo(), empresaVO.getTipoCobrancaPacto(), null);
            Integer qtdCreditoUtilizadoRemessa = remessaItem.size();
            Uteis.logarDebug("Pre-Pago Efetivado - Qtd Credito Utilizado REMESSA: " + qtdCreditoUtilizadoRemessa + " - CodEmpresa " + empresaVO.getCodigo());

            //CONSULTAR TRANSACOES
            List<Integer> transacao = obterListaTransacao(empresaVO.getCodigo(), empresaVO.getTipoCobrancaPacto(), null);
            Integer qtdCreditoUtilizadoTransacao = transacao.size();
            Uteis.logarDebug("Pre-Pago Efetivado - Qtd Credito Utilizado TRANSAÇÃO: " + qtdCreditoUtilizadoTransacao + " - CodEmpresa " + empresaVO.getCodigo());

            //CONSULTAR PIX
            List<Integer> pix = obterListaPix(empresaVO.getCodigo(), empresaVO.getTipoCobrancaPacto(), null);
            Integer qtdCreditoUtilizadoPix = pix.size();
            Uteis.logarDebug("Pre-Pago Efetivado - Qtd Credito Utilizado PIX: " + qtdCreditoUtilizadoPix + " - CodEmpresa " + empresaVO.getCodigo());

            boolean cobrarCreditoPactoBoleto = isCobrarCreditoPactoBoleto(empresaVO.getCodigo());

            //CONSULTAR BOLETOS ONLINE
            List<Integer> boletosOnline = new ArrayList<>();
            Integer qtdCreditoUtilizadoBoletoOnline = 0;
            if (cobrarCreditoPactoBoleto) {
                boletosOnline = obterListaBoletoOnline(empresaVO.getCodigo(), empresaVO.getTipoCobrancaPacto(), null);
                qtdCreditoUtilizadoBoletoOnline = boletosOnline.size();
                Uteis.logarDebug("Pre-Pago Efetivado - Qtd Credito Utilizado BOLETO ONLINE: " + qtdCreditoUtilizadoBoletoOnline + " - CodEmpresa " + empresaVO.getCodigo());
            } else {
                Uteis.logarDebug("Pre-Pago Efetivado - Qtd Credito Utilizado BOLETO ONLINE: NÃO É COBRADO BOLETO" + " - CodEmpresa " + empresaVO.getCodigo());
            }

            //CONSULTAR BOLETOS REMESSA
            List<Integer> boletosRemessa = new ArrayList<>();
            Integer qtdCreditoUtilizadoBoletoRemessa = 0;
            if (cobrarCreditoPactoBoleto) {
                boletosRemessa = obterListaBoletoRemessa(empresaVO.getCodigo(), empresaVO.getTipoCobrancaPacto(), null);
                qtdCreditoUtilizadoBoletoRemessa = boletosRemessa.size();
                Uteis.logarDebug("Pre-Pago Efetivado - Qtd Credito Utilizado BOLETO REMESSA: " + qtdCreditoUtilizadoBoletoRemessa + " - CodEmpresa " + empresaVO.getCodigo());
            } else {
                Uteis.logarDebug("Pre-Pago Efetivado - Qtd Credito Utilizado BOLETO REMESSA: NÃO É COBRADO BOLETO" + " - CodEmpresa " + empresaVO.getCodigo());
            }

            Integer qtdCreditoUtilizado = (qtdCreditoUtilizadoRemessa + qtdCreditoUtilizadoTransacao + qtdCreditoUtilizadoPix + qtdCreditoUtilizadoBoletoOnline + qtdCreditoUtilizadoBoletoRemessa);

            if (qtdCreditoUtilizado > 0) {
                Uteis.logarDebug("Pre-Pago Efetivado - Debitar Crédito Utilizado: " + qtdCreditoUtilizado + " - CodEmpresa " + empresaVO.getCodigo());

                //DEBITAR OS CREDITOS UTILIZADOS
                Integer saldoAtual = null;
                String sql = String.format("UPDATE empresa SET creditodcc=(creditodcc+(%s)) WHERE codigo = %s RETURNING creditodcc", (-1 * qtdCreditoUtilizado), empresaVO.getCodigo());
                try (PreparedStatement sqlAlterar = con.prepareStatement(sql)) {
                    try (ResultSet rs = sqlAlterar.executeQuery()) {
                        if (rs.next()) {
                            saldoAtual = rs.getInt("creditodcc");
                        }
                    }
                }

                StringBuilder observacao = new StringBuilder();
                observacao.append("QTD ANTERIOR: ").append(empresaVO.getCreditoDCC()).append(" \n");
                observacao.append("QTD UTILIZADA REMESSA: ").append(qtdCreditoUtilizadoRemessa).append(" \n");
                observacao.append("QTD UTILIZADA TRANSACAO: ").append(qtdCreditoUtilizadoTransacao).append(" \n");
                observacao.append("QTD UTILIZADA PIX: ").append(qtdCreditoUtilizadoPix).append(" \n");
                observacao.append("QTD UTILIZADA BOLETOS ONLINE: ").append(qtdCreditoUtilizadoBoletoOnline).append(" \n");
                observacao.append("QTD UTILIZADA BOLETOS REMESSA: ").append(qtdCreditoUtilizadoBoletoRemessa).append(" \n");
                observacao.append("QTD UTILIZADA TOTAL: ").append(qtdCreditoUtilizado).append(" \n");
                observacao.append("QTD ATUAL: ").append(saldoAtual);

                inserirLogDCC(empresaVO.getCodigo(), (-1 * qtdCreditoUtilizado), saldoAtual, observacao.toString());

                marcarItensComoContabilizadoPacto(remessaItem, transacao, pix, boletosOnline, boletosRemessa);

            } else {
                Uteis.logarDebug("Pre-Pago Efetivado - Não Houve Crédito Utilizado - CodEmpresa " + empresaVO.getCodigo());
            }

            //ADICIONAR CREDITO PACTO
            if (empresaVO.isGerarCobrancaAutomaticaPacto()) {
                Integer qtdAlunosAtivosDCCMargem = obterQtdAlunosAtivosComAutorizacaoCobranca(empresaVO);
                Integer qtdCreditoDisponivel = empresaVO.getCreditoDCC();
                if (qtdCreditoDisponivel <= qtdAlunosAtivosDCCMargem) {
                    Uteis.logarDebug("Pre-Pago Efetivado - Adicionar Crédito Automático: " + empresaVO.getQtdCreditoRenovarPrePagoCobrancaPacto() + " - CodEmpresa " + empresaVO.getCodigo());
                    Double valorTotal = Uteis.arredondarForcando2CasasDecimais(empresaVO.getQtdCreditoRenovarPrePagoCobrancaPacto() * empresaVO.getValorCreditoPacto());
                    String retorno = adicionarCreditoPrePago(getKey(), empresaVO.getCodigo(), empresaVO.getQtdCreditoRenovarPrePagoCobrancaPacto(),
                            empresaVO.getQtdParcelasCobrancaPacto(), valorTotal, empresaVO.isGerarNotaFiscalCobrancaPacto(), PROCESSO_AUTOMATICO,
                            "", true, empresaVO.getTipoCobrancaPacto());
                    if (!retorno.startsWith("Sucesso")) {
                        throw new Exception(retorno);
                    }
                } else {
                    Uteis.logarDebug("Pre-Pago Efetivado - Não precisa adicionar crédito | qtdCreditoDisponivel " +
                            qtdCreditoDisponivel + " qtdAlunosAtivosDCCMargem: " + qtdAlunosAtivosDCCMargem + " - CodEmpresa " + empresaVO.getCodigo());
                }
            }

            con.commit();
        } catch (Exception e) {
            con.rollback();
            con.setAutoCommit(true);
            throw new Exception(e);
        } finally {
            con.setAutoCommit(true);
        }
    }

    public Integer consultarBonus(int codigoEmpresa) throws SQLException {
        String sql = "SELECT creditodccbonus FROM empresa WHERE codigo = ?";
        try (PreparedStatement sqlAlterar = con.prepareStatement(sql)) {
            sqlAlterar.setInt(1, codigoEmpresa);
            try (ResultSet rs = sqlAlterar.executeQuery()) {
                if (rs.next()) {
                    return rs.getInt("creditodccbonus");
                }
            }
        }
        return 0;
    }

    public void alterarBonus(int codigoEmpresa, Integer qtdBonus, String observacao, String nomeUsuarioOAMD, boolean controlaTransacao) throws Exception {
        String sql = String.format("UPDATE empresa SET creditodccbonus = %s WHERE codigo = %s", qtdBonus, codigoEmpresa);
        Integer qtdAtual = consultarBonus(codigoEmpresa);
        try {
            if (controlaTransacao) {
                con.setAutoCommit(false);
            }

            try (PreparedStatement sqlAlterar = con.prepareStatement(sql)) {
                sqlAlterar.execute();
            }

            registrarLogBonus(codigoEmpresa, qtdAtual, qtdBonus, observacao, nomeUsuarioOAMD);

            if (controlaTransacao) {
                con.commit();
            }
        } catch (Exception e) {
            if (controlaTransacao) {
                con.rollback();
                con.setAutoCommit(true);
            }
            throw e;
        } finally {
            if (controlaTransacao) {
                con.setAutoCommit(true);
            }
        }
    }

    private void registrarLogBonus(Integer codigoEmpresa, Integer qtdBonusAnterior, Integer qtdBonusAtualNovo, String observacao, String nomeUsuarioOAMD) throws Exception {

        //REGISTRAR LOG
        String sqlLog = "insert into log(dataalteracao, pessoa, nomeentidade, nomeentidadedescricao, chaveprimaria, nomecampo, valorcampoanterior, valorcampoalterado, responsavelalteracao, operacao) " +
                "values (now(), 0, ?, ?, ?, ?, ?, ?, ?, ?);";

        try (PreparedStatement pst = con.prepareStatement(sqlLog)) {
            int i = 0;
            pst.setString(++i, "EMPRESA");
            pst.setString(++i, "Empresa - Bônus Cobranca Pacto");
            pst.setString(++i, codigoEmpresa.toString());
            pst.setString(++i, "Alteração de Bônus - Cobranca Pacto");
            pst.setString(++i, "QTD BÔNUS ANTERIOR: " + qtdBonusAnterior);
            pst.setString(++i, "QTD BÔNUS ATUAL: " + qtdBonusAtualNovo + " \n Observação: " + observacao + " \n Usuário OAMD: " + nomeUsuarioOAMD);
            pst.setString(++i, "USUARIO OAMD: " + nomeUsuarioOAMD);
            pst.setString(++i, "ALTERAÇÃO");
            pst.execute();
        }
    }

    private void processoAutomaticoPosPagoMensal(EmpresaVO empresa, Date dia) throws Exception {

        if (Calendario.getDia(dia) >= empresa.getQtdDiasFechamentoCobrancaPacto() &&
                (empresa.getDtUltimaCobrancaPacto() == null || !Calendario.dataNoMesmoMesAno(empresa.getDtUltimaCobrancaPacto(), dia))) {

            String retorno = processarClientePosPagoMensalCobrancaPactoLancarContaFinanceiro(getKey(), empresa.getCodigo(),
                    empresa.getTipoCobrancaPacto(), empresa.getQtdParcelasCobrancaPacto(),
                    empresa.isGerarNotaFiscalCobrancaPacto(), empresa);

            if (!retorno.startsWith("Sucesso")) {
                throw new Exception(retorno);
            }

        } else {
            Uteis.logarDebug("Não foi necessário gerar Cobranca Pos-Pago Mensal - CodEmpresa " + empresa.getCodigo());
        }
    }

    public String processarClientePosPagoMensalCobrancaPactoLancarContaFinanceiro(String key, Integer codigoEmpresa, Integer tipoCobrancaPacto,
                                                                                  Integer qtdParcelas, boolean gerarNota, EmpresaVO empresaVO) throws Exception {
        Empresa empresaDAO = null;
        try {
            con.setAutoCommit(false);
            empresaDAO = new Empresa(this.con);

            if (empresaVO == null) {
                empresaVO = empresaDAO.consultarPorChavePrimaria(codigoEmpresa, Uteis.NIVELMONTARDADOS_GESTAOREMESSA);
            }

            //consultar informações no OAMD
            ConfigCobrancaMensalJSON configCobrancaMensalJSON = consultarConfigCobrancaMensalJSON(key, empresaVO);

            if (UteisValidacao.emptyNumber(configCobrancaMensalJSON.getValorMensal())) {
                throw new Exception("Configuração Pos-Pago Mensal - Valor Mensal não informado.");
            }

            //Mês passado como referência
            Date mesReferencia = Uteis.somarMeses(Calendario.hoje(), -1);


            //consulta no OAMD
            Integer qtdMaximaCredito = configCobrancaMensalJSON.getQtdMaxima();
            Double valorMensal = configCobrancaMensalJSON.getValorMensal();
            Double valorUnitarioExcedente = configCobrancaMensalJSON.getValorUnitarioExcedente();

            Double valorTotal = 0.0;
            Double valorExcedido = 0.0;
            Integer qtdExcedida = 0;


            atualizarDtUltimaCobrancaPacto(codigoEmpresa);

            //CONSULTAR REMESSAITEM
            List<ItemCobrancaPactoJSON> listaCompletaRemessaItem = consultarItensCobrancaPactoRemessasItem(codigoEmpresa, tipoCobrancaPacto, mesReferencia);
            List<Integer> remessaItem = obterListaRemessasItem(listaCompletaRemessaItem);
            Integer qtdCreditoUtilizadoRemessa = remessaItem.size();
            Uteis.logarDebug("Gerada Cobranca Pos-Pago Mensal - Qtd Credito Utilizado REMESSA: " + qtdCreditoUtilizadoRemessa + " - CodEmpresa " + codigoEmpresa);

            //CONSULTAR TRANSACOES
            List<ItemCobrancaPactoJSON> listaCompletaTransacao = consultarItensCobrancaPactoTransacao(codigoEmpresa, tipoCobrancaPacto, mesReferencia);
            List<Integer> transacao = obterListaTransacao(listaCompletaTransacao);
            Integer qtdCreditoUtilizadoTransacao = transacao.size();
            Uteis.logarDebug("Gerada Cobranca Pos-Pago Mensal - Qtd Credito Utilizado TRANSAÇÃO: " + qtdCreditoUtilizadoTransacao + " - CodEmpresa " + codigoEmpresa);

            //CONSULTAR PIX
            List<ItemCobrancaPactoJSON> listaCompletaPix = consultarItensCobrancaPactoPix(codigoEmpresa, tipoCobrancaPacto, mesReferencia);
            List<Integer> pix = obterListaPix(listaCompletaPix);
            Integer qtdCreditoUtilizadoPix = pix.size();
            Uteis.logarDebug("Gerada Cobranca Pos-Pago Mensal - Qtd Credito Utilizado PIX: " + qtdCreditoUtilizadoPix + " - CodEmpresa " + codigoEmpresa);

            boolean cobrarCreditoPactoBoleto = isCobrarCreditoPactoBoleto(codigoEmpresa);

            //CONSULTAR BOLETOS ONLINE
            List<ItemCobrancaPactoJSON> listaCompletaBoletoOnline = new ArrayList<>();
            List<Integer> boletosOnline = new ArrayList<>();
            Integer qtdCreditoUtilizadoBoletoOnline = 0;
            if (cobrarCreditoPactoBoleto) {
                listaCompletaBoletoOnline = consultarItensCobrancaPactoBoletoOnline(codigoEmpresa, tipoCobrancaPacto, mesReferencia);
                boletosOnline = obterListaBoletoOnline(listaCompletaBoletoOnline);
                qtdCreditoUtilizadoBoletoOnline = boletosOnline.size();
                Uteis.logarDebug("Gerada Cobranca Pos-Pago Mensal - Qtd Credito Utilizado BOLETO ONLINE: " + qtdCreditoUtilizadoBoletoOnline + " - CodEmpresa " + codigoEmpresa);
            } else {
                Uteis.logarDebug("Gerada Cobranca Pos-Pago Mensal - Qtd Credito Utilizado BOLETO ONLINE: NÃO É COBRADO BOLETO" + " - CodEmpresa " + codigoEmpresa);
            }

            //CONSULTAR BOLETOS REMESSA
            List<ItemCobrancaPactoJSON> listaCompletaBoletoRemessa = new ArrayList<>();
            List<Integer> boletosRemessa= new ArrayList<>();
            Integer qtdCreditoUtilizadoBoletoRemessa = 0;
            if (cobrarCreditoPactoBoleto) {
                listaCompletaBoletoRemessa = consultarItensCobrancaPactoBoletoRemessa(codigoEmpresa, tipoCobrancaPacto, mesReferencia);
                boletosRemessa = obterListaBoletoRemessa(listaCompletaBoletoRemessa);
                qtdCreditoUtilizadoBoletoRemessa = boletosRemessa.size();
                Uteis.logarDebug("Gerada Cobranca Pos-Pago Mensal - Qtd Credito Utilizado BOLETO REMESSA: " + qtdCreditoUtilizadoBoletoRemessa+ " - CodEmpresa " + codigoEmpresa);
            } else {
                Uteis.logarDebug("Gerada Cobranca Pos-Pago Mensal - Qtd Credito Utilizado BOLETO REMESSA: NÃO É COBRADO BOLETO" + " - CodEmpresa " + codigoEmpresa);
            }

            Integer qtdUtilizado = (qtdCreditoUtilizadoRemessa + qtdCreditoUtilizadoTransacao + qtdCreditoUtilizadoPix + qtdCreditoUtilizadoBoletoOnline + qtdCreditoUtilizadoBoletoRemessa);

            if (qtdUtilizado > 0) {
                Uteis.logarDebug("Gerada Cobranca Pos-Pago Mensal - Qtd Credito Utilizado: " + qtdUtilizado + " - CodEmpresa " + codigoEmpresa);
            } else {
                if (UteisValidacao.emptyNumber(qtdUtilizado) &&
                        configCobrancaMensalJSON.getQtdMinima().equals(qtdUtilizado)) {
                    //                agora o cliente é cobrado mesmo se não utilizar
                    Uteis.logarDebug("Gerada Cobranca Pos-Pago Mensal - Qtd Credito Utilizado: " + qtdUtilizado + " - CodEmpresa " + codigoEmpresa + " - Qtd Mínima: " + configCobrancaMensalJSON.getQtdMinima());
                } else {
                    throw new Exception("NÃO FOI GERADA Cobranca Pos-Pago Mensal - Qtd Credito Utilizado: " + qtdUtilizado + " - CodEmpresa " + codigoEmpresa + " - Qtd Mínima: " + configCobrancaMensalJSON.getQtdMinima());
                }
            }

            marcarItensComoContabilizadoPacto(remessaItem, transacao, pix, boletosOnline, boletosRemessa);

            if (qtdUtilizado <= qtdMaximaCredito) {
                valorTotal = Uteis.arredondarForcando2CasasDecimais(valorMensal);
            } else {

                qtdExcedida = (qtdUtilizado - qtdMaximaCredito);
                valorExcedido = Uteis.arredondarForcando2CasasDecimais(valorUnitarioExcedente * qtdExcedida);
                valorTotal = Uteis.arredondarForcando2CasasDecimais(valorMensal + valorExcedido);
            }

            StringBuilder observacao = new StringBuilder();
            observacao.append("QTD TOTAL: ").append(qtdUtilizado).append(" \n");
            observacao.append("QTD REMESSA: ").append(qtdCreditoUtilizadoRemessa).append(" \n");
            observacao.append("QTD TRANSACAO: ").append(qtdCreditoUtilizadoRemessa).append(" \n");
            observacao.append("QTD PIX: ").append(qtdCreditoUtilizadoPix);
            observacao.append("QTD BOLETO ONLINE: ").append(qtdCreditoUtilizadoBoletoOnline);
            observacao.append("QTD BOLETO REMESSA: ").append(qtdCreditoUtilizadoBoletoRemessa);
            if (!UteisValidacao.emptyNumber(qtdExcedida)) {
                observacao.append(" \n");
                observacao.append("QTD EXCEDIDO: ").append(qtdExcedida).append(" \n");
                observacao.append("VALOR EXCEDIDO: ").append(Formatador.formatarValorMonetario(valorExcedido)).append(" \n");
            }
            observacao.append(" \nREF: ").append(Uteis.getDataMesAnoConcatenado(mesReferencia)).append(" \n");

            List<ItemCobrancaPactoJSON> listaCompleta = new ArrayList<>();
            listaCompleta.addAll(listaCompletaRemessaItem);
            listaCompleta.addAll(listaCompletaTransacao);
            listaCompleta.addAll(listaCompletaPix);
            listaCompleta.addAll(listaCompletaBoletoOnline);
            listaCompleta.addAll(listaCompletaBoletoRemessa);

            String tabelaCreditoPacto = (configCobrancaMensalJSON.getCodigo() + " - " + configCobrancaMensalJSON.getDescricao());

            JSONObject jsonCobranca = criarJSONCobranca("processarClientePosPagoMensalCobrancaPactoLancarContaFinanceiro", empresaVO.getCodigo(), empresaVO.getTipoCobrancaPacto(),
                    qtdUtilizado, qtdCreditoUtilizadoRemessa, qtdCreditoUtilizadoTransacao, qtdCreditoUtilizadoPix, qtdCreditoUtilizadoBoletoOnline, qtdCreditoUtilizadoBoletoRemessa, valorTotal,
                    qtdExcedida, valorExcedido, null, qtdMaximaCredito, valorMensal, valorUnitarioExcedente,
                    null, null, null, null, PROCESSO_AUTOMATICO, "", tabelaCreditoPacto);
            inserirLogCobrancaPacto(qtdUtilizado, valorTotal, qtdParcelas, gerarNota, PROCESSO_AUTOMATICO, codigoEmpresa, "",
                    true, observacao.toString(), new JSONArray(listaCompleta).toString(), jsonCobranca, tipoCobrancaPacto, tabelaCreditoPacto);

            if (valorTotal > 0.0) {
                lancarContaFinanceiroPacto(key, codigoEmpresa, qtdUtilizado, qtdParcelas, valorTotal, gerarNota, 0, tipoCobrancaPacto, empresaVO.getDiaVencimentoCobrancaPacto(), mesReferencia);
            }

            con.commit();
            return "Sucesso";
        } catch (Exception e) {
            enviarEmailErro("processarClientePosPagoMensalCobrancaPactoLancarContaFinanceiro | " + e.getMessage(), codigoEmpresa);
            e.printStackTrace();
            con.rollback();
            con.setAutoCommit(true);
            return e.getMessage();
        } finally {
            con.setAutoCommit(true);
            empresaDAO = null;
        }
    }

    private void atualizarDtUltimaCobrancaPacto(Integer codigoEmpresa) throws SQLException {
        String sql = String.format("UPDATE empresa SET dtultimacobrancapacto = now() WHERE codigo = %s", codigoEmpresa);
        try (PreparedStatement sqlAlterar = con.prepareStatement(sql)) {
            sqlAlterar.execute();
        }
    }

    public ConfigCobrancaMensalJSON consultarConfigCobrancaMensalJSON(String key, EmpresaVO empresaVO) throws Exception {
        //consultar informações no oamd
        JSONObject body = new JSONObject();
        body.put("chave", key);
        body.put("codigoEmpresa", empresaVO.getCodigo());
        body.put("codigoFinanceiro", empresaVO.getCodEmpresaFinanceiro());

        RequestHttpService service = new RequestHttpService();
        RespostaHttpDTO respostaHttpDTO = service.executeRequest(Uteis.getUrlOAMD() + "/prest/adm/creditoPactoMensal", null, null, body.toString(), MetodoHttpEnum.POST);
        JSONObject jsonRetorno = new JSONObject(respostaHttpDTO.getResponse()).getJSONObject("content");
        return new ConfigCobrancaMensalJSON(jsonRetorno);
    }

    private JSONObject criarJSONCobranca(String metodo, Integer codigoEmpresa, Integer tipoCobrancaPacto, Integer qtdUtilizada, Integer qtdRemessaItem, Integer qtdTransacao, Integer qtdPix,
                                         Integer qtdBoletosOnline, Integer qtdBoletosRemessa, Double valorTotal, Integer qtdExcedida, Double valorExcedido, Integer qtdMinimaCredito,
                                         Integer qtdMaximaCredito, Double valorMensal, Double valorUnitarioExcedente, Double valorUnitarioCredito, Integer qtdBonusDisponivel,
                                         Integer qtdBonusUtilizada, Integer qtdBonusRestante, String nomeUsuarioOAMD, String justificativa, String tabelaCreditoPacto) {
        try {
            JSONObject jsonCobranca = new JSONObject();
            jsonCobranca.put("metodo", metodo);
            jsonCobranca.put("codigoEmpresa", codigoEmpresa);
            jsonCobranca.put("tipoCobrancaPacto", tipoCobrancaPacto);
            jsonCobranca.put("qtdUtilizada", qtdUtilizada);
            jsonCobranca.put("qtdRemessaItem", qtdRemessaItem);
            jsonCobranca.put("qtdTransacao", qtdTransacao);
            jsonCobranca.put("qtdPix", qtdPix);
            jsonCobranca.put("qtdBoletosOnline", qtdBoletosOnline);
            jsonCobranca.put("qtdBoletosRemessa", qtdBoletosRemessa);
            jsonCobranca.put("valorTotal", valorTotal);
            jsonCobranca.put("qtdExcedida", qtdExcedida);
            jsonCobranca.put("valorExcedido", valorExcedido);
            jsonCobranca.put("qtdMinimaCredito", qtdMinimaCredito);
            jsonCobranca.put("qtdMaximaCredito", qtdMaximaCredito);
            jsonCobranca.put("valorMensal", valorMensal);
            jsonCobranca.put("valorUnitarioExcedente", valorUnitarioExcedente);
            jsonCobranca.put("valorUnitarioCredito", valorUnitarioCredito);
            jsonCobranca.put("qtdBonusDisponivel", qtdBonusDisponivel);
            jsonCobranca.put("qtdBonusUtilizada", qtdBonusUtilizada);
            jsonCobranca.put("qtdBonusRestante", qtdBonusRestante);
            jsonCobranca.put("nomeUsuarioOAMD", nomeUsuarioOAMD);
            jsonCobranca.put("justificativa", justificativa);
            jsonCobranca.put("tabelaCreditoPacto", tabelaCreditoPacto);
            return jsonCobranca;
        } catch (Exception ex) {
            ex.printStackTrace();
            return null;
        }
    }

    private void enviarEmailErro(String erro, Integer empresa) {
        if (UteisValidacao.emptyString(erro) || !Uteis.isEnviarEmailErroCreditoDCC()) {
            return;
        }
        enviarEmailErroGeral(erro, empresa);
    }

    private void enviarEmailErroGeral(String erro, Integer empresa) {
        try {
            UteisEmail email = new UteisEmail();
            ConfiguracaoSistemaCRMVO config = SuperControle.getConfiguracaoSMTPNoReply();
            String assunto = "Erro Crédito Pacto - Empresa: " + empresa + " | Chave " + this.key;
            email.novo(assunto, config);

            String[] emailEnviar = new String[]{"<EMAIL>", "<EMAIL>"};

            StringBuilder emailTexto = new StringBuilder();
            emailTexto.append("<h2>Erros ao realizar cobranca Crédito Pacto da empresa ").append(empresa).append(" </h2>");
            emailTexto.append("<h4>Chave: ").append(this.key).append("</h4>");
            emailTexto.append("<p> - ").append(erro).append("</p>");

            email.enviarEmailN(emailEnviar, emailTexto.toString(), assunto, "");
        } catch (Exception ex) {
            ex.printStackTrace();
            Uteis.logarDebug("Erro enviarEmailErro: " + ex.getMessage());
        }
    }

    public String processarClientePosPagoMensalRedeEmpresa(Integer codigoEmpresa) throws Exception {
        Empresa empresaDAO = null;
        try {
            con.setAutoCommit(false);
            empresaDAO = new Empresa(this.con);

            EmpresaVO empresaVO = empresaDAO.consultarPorChavePrimaria(codigoEmpresa, Uteis.NIVELMONTARDADOS_GESTAOREMESSA);

            Date mesReferencia = Uteis.somarMeses(Calendario.hoje(), -1);

            atualizarDtUltimaCobrancaPacto(codigoEmpresa);

            //CONSULTAR REMESSAITEM
            List<ItemCobrancaPactoJSON> listaCompletaRemessaItem = consultarItensCobrancaPactoRemessasItem(codigoEmpresa, empresaVO.getTipoCobrancaPacto(), mesReferencia);
            List<Integer> remessaItem = obterListaRemessasItem(listaCompletaRemessaItem);
            Integer qtdCreditoUtilizadoRemessa = remessaItem.size();
            Uteis.logarDebug("Gerada Cobranca Pos-Pago Mensal - Qtd Credito Utilizado REMESSA: " + qtdCreditoUtilizadoRemessa + " - CodEmpresa " + codigoEmpresa);

            //CONSULTAR TRANSACOES
            List<ItemCobrancaPactoJSON> listaCompletaTransacao = consultarItensCobrancaPactoTransacao(codigoEmpresa, empresaVO.getTipoCobrancaPacto(), mesReferencia);
            List<Integer> transacao = obterListaTransacao(listaCompletaTransacao);
            Integer qtdCreditoUtilizadoTransacao = transacao.size();
            Uteis.logarDebug("Gerada Cobranca Pos-Pago Mensal - Qtd Credito Utilizado TRANSAÇÃO: " + qtdCreditoUtilizadoTransacao + " - CodEmpresa " + codigoEmpresa);

            //CONSULTAR PIX
            List<ItemCobrancaPactoJSON> listaCompletaPix = consultarItensCobrancaPactoPix(codigoEmpresa, empresaVO.getTipoCobrancaPacto(), mesReferencia);
            List<Integer> pix = obterListaPix(listaCompletaPix);
            Integer qtdCreditoUtilizadoPix = pix.size();
            Uteis.logarDebug("Gerada Cobranca Pos-Pago Mensal - Qtd Credito Utilizado PIX: " + qtdCreditoUtilizadoPix + " - CodEmpresa " + codigoEmpresa);

            boolean cobrarCreditoPactoBoleto = isCobrarCreditoPactoBoleto(codigoEmpresa);

            //CONSULTAR BOLETOS ONLINE
            List<ItemCobrancaPactoJSON> listaCompletaBoletoOnline = new ArrayList<>();
            List<Integer> boletosOnline = new ArrayList<>();
            Integer qtdCreditoUtilizadoBoletoOnline = 0;
            if (cobrarCreditoPactoBoleto) {
                listaCompletaBoletoOnline = consultarItensCobrancaPactoBoletoOnline(codigoEmpresa, empresaVO.getTipoCobrancaPacto(), mesReferencia);
                boletosOnline = obterListaBoletoOnline(listaCompletaBoletoOnline);
                qtdCreditoUtilizadoBoletoOnline = boletosOnline.size();
                Uteis.logarDebug("Gerada Cobranca Pos-Pago Mensal - Qtd Credito Utilizado BOLETO ONLINE: " + qtdCreditoUtilizadoBoletoOnline + " - CodEmpresa " + codigoEmpresa);
            } else {
                Uteis.logarDebug("Gerada Cobranca Pos-Pago Mensal - Qtd Credito Utilizado BOLETO ONLINE: NÃO É COBRADO BOLETO" + " - CodEmpresa " + codigoEmpresa);
            }

            //CONSULTAR BOLETOS REMESSA
            List<ItemCobrancaPactoJSON> listaCompletaBoletoRemessa = new ArrayList<>();
            List<Integer> boletosRemessa = new ArrayList<>();
            Integer qtdCreditoUtilizadoBoletoRemessa = 0;
            if (cobrarCreditoPactoBoleto) {
                listaCompletaBoletoRemessa = consultarItensCobrancaPactoBoletoRemessa(codigoEmpresa, empresaVO.getTipoCobrancaPacto(), mesReferencia);
                boletosRemessa = obterListaBoletoRemessa(listaCompletaBoletoRemessa);
                qtdCreditoUtilizadoBoletoRemessa = boletosRemessa.size();
                Uteis.logarDebug("Gerada Cobranca Pos-Pago Mensal - Qtd Credito Utilizado BOLETO ONLINE: " + qtdCreditoUtilizadoBoletoRemessa + " - CodEmpresa " + codigoEmpresa);
            } else {
                Uteis.logarDebug("Gerada Cobranca Pos-Pago Mensal - Qtd Credito Utilizado BOLETO ONLINE: NÃO É COBRADO BOLETO" + " - CodEmpresa " + codigoEmpresa);
            }


            Integer qtdUtilizado = (qtdCreditoUtilizadoRemessa + qtdCreditoUtilizadoTransacao + qtdCreditoUtilizadoPix + qtdCreditoUtilizadoBoletoOnline + qtdCreditoUtilizadoBoletoRemessa);

            if (qtdUtilizado > 0) {
                Uteis.logarDebug("Gerada Cobranca Pos-Pago Mensal - Qtd Credito Utilizado: " + qtdUtilizado + " - CodEmpresa " + codigoEmpresa);
            }

            //marcar itens como processados
            marcarItensComoContabilizadoPacto(remessaItem, transacao, pix, boletosOnline, boletosRemessa);

            StringBuilder observacao = new StringBuilder();
            observacao.append("QTD TOTAL: ").append(qtdUtilizado).append(" \n");
            observacao.append("QTD REMESSA: ").append(qtdCreditoUtilizadoRemessa).append(" \n");
            observacao.append("QTD TRANSACAO: ").append(qtdCreditoUtilizadoTransacao).append(" \n");
            observacao.append("QTD PIX: ").append(qtdCreditoUtilizadoPix).append(" \n");
            observacao.append("QTD BOLETO ONLINE: ").append(qtdCreditoUtilizadoBoletoOnline).append(" \n");
            observacao.append("QTD BOLETO REMESSA: ").append(qtdCreditoUtilizadoBoletoRemessa).append(" \n");
            observacao.append("PROCESSO REDE DE EMPRESA - ").append(empresaVO.getTipoCobrancaPactoApresentar().toUpperCase()).append(" \n");
            observacao.append("REF: ").append(Uteis.getDataMesAnoConcatenado(mesReferencia)).append(" \n");

            List<ItemCobrancaPactoJSON> listaCompleta = new ArrayList<>();
            listaCompleta.addAll(listaCompletaRemessaItem);
            listaCompleta.addAll(listaCompletaTransacao);
            listaCompleta.addAll(listaCompletaPix);
            listaCompleta.addAll(listaCompletaBoletoOnline);
            listaCompleta.addAll(listaCompletaBoletoRemessa);

            JSONObject jsonCobranca = criarJSONCobranca("processarClientePosPagoMensalCobrancaPactoLancarContaFinanceiro", empresaVO.getCodigo(), empresaVO.getTipoCobrancaPacto(), qtdUtilizado,
                    qtdCreditoUtilizadoRemessa, qtdCreditoUtilizadoTransacao, qtdCreditoUtilizadoPix, qtdCreditoUtilizadoBoletoOnline, qtdCreditoUtilizadoBoletoRemessa, null,
                    null, null, null, null, null, null,
                    null, null, null, null, PROCESSO_AUTOMATICO, "", "");
            inserirLogCobrancaPacto(qtdUtilizado, 0.0, empresaVO.getQtdParcelasCobrancaPacto(), empresaVO.isGerarNotaFiscalCobrancaPacto(), PROCESSO_AUTOMATICO, codigoEmpresa, "",
                    true, observacao.toString(), new JSONArray(listaCompleta).toString(), jsonCobranca, empresaVO.getTipoCobrancaPacto(), "");

            JSONObject json = new JSONObject();
            json.put("qtdUtilizada", qtdUtilizado);
            json.put("qtdRemessaItem", qtdCreditoUtilizadoRemessa);
            json.put("qtdTransacao", qtdCreditoUtilizadoTransacao);
            json.put("qtdPix", qtdCreditoUtilizadoPix);
            json.put("qtdBoletosOnline", qtdCreditoUtilizadoBoletoOnline);
            json.put("qtdBoletosRemessa", qtdCreditoUtilizadoBoletoRemessa);

            con.commit();
            return json.toString();
        } catch (Exception e) {
            enviarEmailErroGeral("processarClientePosPagoMensalRedeEmpresa | " + e.getMessage(), codigoEmpresa);
            e.printStackTrace();
            con.rollback();
            con.setAutoCommit(true);
            throw e;
        } finally {
            con.setAutoCommit(true);
            empresaDAO = null;
        }
    }

    private void processoAutomaticoPosPagoMensalEmpresaResponsavelCobranca(EmpresaVO empresaVO, Date dia) throws Exception {
        if (Calendario.getDia(dia) >= empresaVO.getQtdDiasFechamentoCobrancaPacto() &&
                (empresaVO.getDtUltimaCobrancaPacto() == null || !Calendario.dataNoMesmoMesAno(empresaVO.getDtUltimaCobrancaPacto(), dia))) {

            try {
                con.setAutoCommit(false);

                Map<String, String> params = new HashMap<>();
                params.put("chave", this.getKey());

                RequestHttpService service = new RequestHttpService();
                RespostaHttpDTO respostaHttpDTO = service.executeRequest(Uteis.getUrlOAMD() + "/prest/empresaFinanceiro/cobrancaPactoRedeEmpresa", null, params, null, MetodoHttpEnum.POST);
                Uteis.logarDebug("processoAutomaticoPosPagoMensalEmpresaResponsavelCobranca | Resposta: " + respostaHttpDTO.getResponse());
                JSONObject jsonRetorno = new JSONObject(new JSONObject(respostaHttpDTO.getResponse()).getString("content"));

                Integer qtdUtilizado = 0;
                JSONArray lista = jsonRetorno.getJSONArray("empresas");
                for (int e = 0; e < lista.length(); e++) {
                    try {
                        JSONObject obj = lista.getJSONObject(e);
                        JSONObject creditoUtilizado = new JSONObject(obj.getString("creditoUtilizado"));
                        qtdUtilizado += creditoUtilizado.optInt("qtdUtilizada");
                    } catch (Exception ex) {
                        ex.printStackTrace();
                    }
                }

                if (UteisValidacao.emptyNumber(qtdUtilizado)) {
                    throw new Exception("Quantidade utilizado zerado.");
                }

                Uteis.logarDebug("processoAutomaticoPosPagoMensalEmpresaResponsavelCobranca | Qtd Utilizada: " + qtdUtilizado);

                //consultar informações no OAMD
                ConfigCobrancaMensalJSON configCobrancaMensalJSON = consultarConfigCobrancaMensalJSON(this.getKey(), empresaVO);

                if (UteisValidacao.emptyNumber(configCobrancaMensalJSON.getValorMensal())) {
                    throw new Exception("Configuração Pos-Pago Mensal - Valor Mensal não informado.");
                }

                //atualizar data da ultima cobrança
                atualizarDtUltimaCobrancaPacto(empresaVO.getCodigo());

                Date mesReferencia = Uteis.somarMeses(Calendario.hoje(), -1);

                //consulta no OAMD
                Integer qtdMaximaCredito = configCobrancaMensalJSON.getQtdMaxima();
                Double valorMensal = configCobrancaMensalJSON.getValorMensal();
                Double valorUnitarioExcedente = configCobrancaMensalJSON.getValorUnitarioExcedente();

                Double valorTotal = 0.0;
                Double valorExcedido = 0.0;
                Integer qtdExcedida = 0;

                if (qtdUtilizado <= qtdMaximaCredito) {
                    valorTotal = Uteis.arredondarForcando2CasasDecimais(valorMensal);
                } else {

                    qtdExcedida = (qtdUtilizado - qtdMaximaCredito);
                    valorExcedido = Uteis.arredondarForcando2CasasDecimais(valorUnitarioExcedente * qtdExcedida);
                    valorTotal = Uteis.arredondarForcando2CasasDecimais(valorMensal + valorExcedido);
                }

                StringBuilder observacao = new StringBuilder();
                observacao.append("QTD TOTAL: ").append(qtdUtilizado).append(" \n");
                observacao.append("OBS.: EMPRESA RESPONSÁVEL PELA COBRANÇA DA REDE DE EMPRESAS \n");
                if (!UteisValidacao.emptyNumber(qtdExcedida)) {
                    observacao.append(" \nQTD EXCEDIDO: ").append(qtdExcedida).append(" \n");
                    observacao.append("VALOR EXCEDIDO: ").append(Formatador.formatarValorMonetario(valorExcedido)).append(" \n");
                }
                observacao.append(" \nREF: ").append(Uteis.getDataMesAnoConcatenado(mesReferencia)).append(" \n");


                String tabelaCreditoPacto = (configCobrancaMensalJSON.getCodigo() + " - " + configCobrancaMensalJSON.getDescricao());

                JSONObject jsonCobranca = criarJSONCobranca("processoAutomaticoPosPagoMensalEmpresaResponsavelCobranca", empresaVO.getCodigo(), empresaVO.getTipoCobrancaPacto(), qtdUtilizado,
                        0, 0, 0, 0, 0, valorTotal, qtdExcedida, valorExcedido, null, qtdMaximaCredito, valorMensal, valorUnitarioExcedente,
                        null, null, null, null, PROCESSO_AUTOMATICO, "", tabelaCreditoPacto);
                inserirLogCobrancaPacto(qtdUtilizado, valorTotal, empresaVO.getQtdParcelasCobrancaPacto(), empresaVO.isGerarNotaFiscalCobrancaPacto(), PROCESSO_AUTOMATICO, empresaVO.getCodigo(), "",
                        true, observacao.toString(), new JSONArray().toString(), jsonCobranca, empresaVO.getTipoCobrancaPacto(), tabelaCreditoPacto);

                if (valorTotal > 0.0) {
                    lancarContaFinanceiroPacto(key, empresaVO.getCodigo(), qtdUtilizado, empresaVO.getQtdParcelasCobrancaPacto(), valorTotal, empresaVO.isGerarNotaFiscalCobrancaPacto(), 0,
                            empresaVO.getTipoCobrancaPacto(), empresaVO.getDiaVencimentoCobrancaPacto(), mesReferencia);
                }

                con.commit();
            } catch (Exception ex) {
                Uteis.logarDebug("processoAutomaticoPosPagoMensalEmpresaResponsavelCobranca | Erro: " + ex.getMessage());
                enviarEmailErroGeral("processoAutomaticoPosPagoMensalEmpresaResponsavelCobranca | " + ex.getMessage(), empresaVO.getCodigo());
                ex.printStackTrace();
                con.rollback();
                con.setAutoCommit(true);
                throw ex;
            } finally {
                con.setAutoCommit(true);
            }

        } else {
            Uteis.logarDebug("Não foi necessário gerar Cobranca Pos-Pago Mensal - Rede de Empresa - CodEmpresa " + empresaVO.getCodigo());
        }
    }

    public Integer consultarQtdCreditoUtilizado(Integer codigoEmpresa, Integer tipoCobrancaPacto) throws Exception {
        Integer qtdRemessa = obterQuantidadeListaRemessasItem(codigoEmpresa, tipoCobrancaPacto, null);
        Integer qtdTransacao = obterQuantidadeListaTransacao(codigoEmpresa, tipoCobrancaPacto, null);
        Integer qtdPix = obterQuantidadeListaPix(codigoEmpresa, tipoCobrancaPacto, null);
        Integer qtdBoletosOnline = obterQuantidadeListaBoletoOnline(codigoEmpresa, tipoCobrancaPacto, null);
        Integer qtdBoletosRemessa = obterQuantidadeListaBoletoRemessa(codigoEmpresa, tipoCobrancaPacto, null);
        return (qtdRemessa + qtdTransacao + qtdPix + qtdBoletosOnline + qtdBoletosRemessa);
    }

    public static void main(String... args) {
        try {
            String chave = "teste";
            Uteis.debug = true;
            if (args.length > 0) {
                chave = args[0];
                Uteis.logarDebug("Obter conexão para chave: " + chave);
                Conexao.guardarConexaoForJ2SE(new DAO().obterConexaoEspecifica(chave));
            }
            Conexao.guardarConexaoForJ2SE(new DAO().obterConexaoEspecifica(chave));
            CreditoDCCService creditoDCCService = new CreditoDCCService();
            creditoDCCService.setKey(chave);
            creditoDCCService.processarCobrancaPacto(Calendario.hoje());
        } catch (Exception ex) {
            Logger.getLogger(CreditoDCCService.class.getName()).log(Level.SEVERE, null, ex);
        }
    }
}
