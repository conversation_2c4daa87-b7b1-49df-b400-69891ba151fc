
package servicos.integracao.adm.jaxws;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;
import javax.xml.bind.annotation.XmlType;

@XmlRootElement(name = "obterEmpresa", namespace = "http://adm.integracao.servicos/")
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "obterEmpresa", namespace = "http://adm.integracao.servicos/")
public class ObterEmpresa {

    @XmlElement(name = "key", namespace = "")
    private String key;

    /**
     * 
     * @return
     *     returns String
     */
    public String getKey() {
        return this.key;
    }

    /**
     * 
     * @param key
     *     the value for the key property
     */
    public void setKey(String key) {
        this.key = key;
    }

}
