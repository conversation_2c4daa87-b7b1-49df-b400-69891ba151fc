package servicos.integracao.mgb.impl;

import br.com.pactosolucoes.comuns.json.JSONMapper;
import controle.arquitetura.exceptions.ServiceException;
import negocio.comuns.plano.HorarioTurmaVO;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.plano.HorarioTurma;
import negocio.interfaces.plano.HorarioTurmaInterfaceFacade;
import org.json.JSONArray;
import org.json.JSONObject;
import servicos.http.MetodoHttpEnum;
import servicos.http.RequestHttpService;
import servicos.http.RespostaHttpDTO;
import servicos.integracao.enumerador.IntegracoesEnum;
import servicos.integracao.mgb.intf.MgbService;
import servicos.legolas.LegolasService;

import javax.servlet.http.HttpSession;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;

public class MgbServiceImpl extends SuperEntidade implements MgbService {

    private Map<Integer, Boolean> integrado = new HashMap<>();
    private List<String> matriculasSincronizadas = new ArrayList<>();
    private HorarioTurmaInterfaceFacade horarioTurmaDao;

    public MgbServiceImpl() throws Exception {
    }

    public MgbServiceImpl(HttpSession session) throws Exception {
        super(session);
    }

    public MgbServiceImpl(Connection conexao) throws Exception {
        super(conexao);
    }

    public HorarioTurmaInterfaceFacade getHorarioDao() throws Exception {
        if (horarioTurmaDao == null) {
            horarioTurmaDao = new HorarioTurma(getCon());
        }
        horarioTurmaDao.setCon(getCon());
        return horarioTurmaDao;
    }

    public Boolean integradoMgb(Integer empresa) {
        if (integrado.get(empresa) == null) {
            try {
                String token = tokenIntegracao(empresa);
                boolean possuiTokenValido = token != null
                        && token.trim().length() > "Bearer".length()
                        && !token.trim().equalsIgnoreCase("Bearer");

                integrado.put(empresa, possuiTokenValido);
            } catch (Exception e) {
                integrado.put(empresa, Boolean.FALSE);
            }
        }
        return integrado.get(empresa);
    }

    public void postStudentMgb(Integer empresa, Integer matricula, Boolean sincronizarComTurma, boolean alunoJaConsultadoPorMatriculaMgb) throws Exception {
        String publicidmgb = verificarPublicidmgb(matricula);
        if (publicidmgb != null && verificarSeExisteAlunoPorPublicId(empresa, publicidmgb)) {
            putStudent(empresa, matricula, publicidmgb, sincronizarComTurma);
            return;
        }
        String token = tokenIntegracao(empresa);
        if (token != null) {
            String url = getCaminho("apiMgb") + "/api/v1/student";
            StudentDTO studentDTO = findByMatricula(matricula, null);

            // Condição para que aluno seja sincronizado com mgb, é a regra do mgb
            if(studentDTO.getLevelPublicId() == null
                    || studentDTO.getLevelPublicId().isEmpty()
                    || studentDTO.getLevelPublicId().equals("0")){
                throw new ServiceException("Para que a sincronização ocorra, o aluno deve possuir um contrato de turma, e o horário no qual está matriculado deve ter o nível vinculado ao MGB");
            }

            // consultar aluno pela matrícula antes de tentar criar um novo para evitar duplicação de aluno no mgb, e se encontrar associa o aluno ao publicId
            if (!alunoJaConsultadoPorMatriculaMgb) {
                publicidmgb = consultarPublicIdAlunoMgbPorMatriculaValidandoNome(empresa, studentDTO.getRegistration(), studentDTO.getFirstName() + " " + studentDTO.getLastName());
                if (!UteisValidacao.emptyString(publicidmgb)) {
                    // se foi localizado aluno pela matricula, atualiza o publicidmgb do aluno e chama postStudentMgb para atualizar o aluno
                    alterarpublicidmgb(matricula, publicidmgb, null);
                    postStudentMgb(empresa, matricula, true, true);
                } else {
                    // se não foi localizado aluno pela matricula, chama postStudentMgb para sincrinizar como novo aluno
                    postStudentMgb(empresa, matricula, true, true);
                }
            } else {
                Map<String, String> headers = new HashMap<>();
                headers.put("Authorization", tokenIntegracao(empresa));
                headers.put("Content-Type", "application/json");
                RequestHttpService service = new RequestHttpService();
                RespostaHttpDTO respostaHttpDTO = service.executeRequest(url, headers, null, studentDTO.toString(), MetodoHttpEnum.POST);
                JSONObject respostaJson = new JSONObject(respostaHttpDTO.getResponse());
                if (respostaJson.optBoolean("success") && respostaJson.has("data")) {
                    try {
                        String publicId = respostaJson.getJSONObject("data").getString("publicId");
                        if (!UteisValidacao.emptyString(publicId)) {
                            alterarpublicidmgb(matricula, publicId, null);
                            if (sincronizarComTurma) {
                                syncronizarAlunoTurmaMgb(empresa, matricula, publicId, 1);
                            }
                        } else {
                            Uteis.logarDebug("Erro Sincronização MGB aluno MAT: " + studentDTO.getRegistration() + " publicId não encontrado na resposta MGB: " + respostaJson);
                        }
                    } catch (Exception e) {
                        Uteis.logarDebug("Erro Sincronização MGB aluno MAT: " + studentDTO.getRegistration() + " response MGB: " + respostaJson);
                        throw e;
                    }
                } else if (respostaJson.has("errors")) {
                    for (int i = 0; i < respostaJson.getJSONArray("errors").length(); i++) {
                        JSONObject error = respostaJson.getJSONArray("errors").getJSONObject(i);
                        if (!matriculasSincronizadas.contains(studentDTO.registration) && error.optString("code").equals("IOERx2034") || error.optString("message").contains("Essa matrícula já está sendo usada")) {
                            matriculasSincronizadas.add(studentDTO.registration);
                            publicidmgb = consultarPublicIdAlunoMgbPorMatriculaValidandoNome(empresa, studentDTO.getRegistration(), studentDTO.getFirstName() + " " + studentDTO.getLastName());
                            if (!UteisValidacao.emptyString(publicidmgb)) {
                                alterarpublicidmgb(matricula, publicidmgb, null);
                                postStudentMgb(empresa, matricula, true, true);
                                Uteis.logarDebug(" Aluno sincronizado! publicId: " + publicidmgb);
                            } else {
                                Uteis.logarDebug(" Sincronização MGB, aluno MAT: " + matricula + " não sincronizado! erro: " + error);
                                throw new ServiceException("Não foi possível sincronizar o aluno.");
                            }
                            break;
                        }
                    }
                } else {
                    Uteis.logarDebug("Falha na Sincronização MGB aluno MAT: " + studentDTO.getRegistration() + " response MGB: " + respostaJson);
                    throw new ServiceException("Falha ao tentar sincronizar o aluno");
                }
            }
        }
    }

    private String consultarPublicIdAlunoMgbPorMatriculaValidandoNome(Integer empresa, String matricula, String nome) throws Exception {

        // tratar matrícula, pois no mgb é uma string e "000592" é diferente de "592"
        Integer codigoMatricula = Integer.parseInt(matricula);
        Map<String, String> headers = new HashMap<>();
        headers.put("Authorization", tokenIntegracao(empresa));
        headers.put("Content-Type", "application/json");

        String urlStudents = getCaminho("apiMgb") + "/api/v1/students";
        Map<String, String> paramsStudents = new HashMap<>();
        paramsStudents.put("Registration", codigoMatricula.toString());


        RequestHttpService service = new RequestHttpService();
        RespostaHttpDTO respostaHttpDTOStudents = service.executeRequest(urlStudents, headers, paramsStudents, "", MetodoHttpEnum.GET);
        JSONObject respostaStudentsJson = new JSONObject(respostaHttpDTOStudents.getResponse());
        if (respostaStudentsJson.optBoolean("success") && respostaStudentsJson.has("data")) {
            JSONArray students = respostaStudentsJson.getJSONObject("data").getJSONObject("list").getJSONArray("items");
            // o mgb retorna uma lista de alunos localizados onde a matricula no mgb contém o valor enviado, por isso necessário uma segunda validação aqui
            for (int i = 0; i < students.length(); i++) {
                JSONObject student = students.getJSONObject(i);

                Integer registrationMgb = student.optString("registration").matches("\\d+") ? Integer.parseInt(student.optString("registration")) : 0;
                if (registrationMgb.equals(0) || !codigoMatricula.equals(registrationMgb)) {
                    continue;
                }

                nome = Uteis.retirarAcentuacao(nome).replace("  ", " ").trim();
                String nameMGB = Uteis.retirarAcentuacao(student.optString("name")).replace("  ", " ").trim();

                if (nome.equalsIgnoreCase(nameMGB)) {
                    return student.getString("publicId");
                }
            }
        }
        return "";
    }

    public void syncronizarAlunoTurmaMgb(Integer empresa, Integer matricula, String publicIdAluno, Integer action) {
        // ACTION
        // 1 - Add
        // 2 - Remove
        try {
            List<String> listaAlunos = new ArrayList<>();
            listaAlunos.add(publicIdAluno);

            StringBuilder sql = new StringBuilder();
            sql.append(
                    "select h.publicidturmamgb \n" +
                    "from matriculaalunohorarioturma m \n" +
                    "inner join cliente c on c.pessoa = m.pessoa \n" +
                    "inner join horarioturma h on h.codigo = m.horarioturma \n" +
                    "where current_date between m.datainicio and m.datafim and c.codigomatricula = " + matricula
            );
            try (ResultSet rs = SuperFacadeJDBC.criarConsulta(sql.toString(), con)) {
                while (rs.next()) {
                    if (!UteisValidacao.emptyString(rs.getString("publicidturmamgb"))) {
                        putUpdateStudentTurmaMgb(empresa, rs.getString("publicidturmamgb"), action, listaAlunos);
                    }
                }
            } catch (Exception e) {
                Uteis.logarDebug("Ocorreu um erro ao tentar sincronizar o aluno com turma mgb, erro: " + e.getMessage());
            }
        } catch (Exception e) {
            Uteis.logarDebug("Ocorreu um erro ao tentar sincronizar o aluno com turma mgb, erro: " + e.getMessage());
        }
    }

    private String autorizacao(String token) {
        return token == null ? "" : token.contains("Bearer") ? token : ("Bearer " + token);
    }

    public String nivelAluno(Integer empresa, String matricula) throws Exception{
        try {
            Boolean mgb = integradoMgb(empresa);
            if(mgb){
                ResultSet rs = criarConsulta("select nivelmgb from cliente where codigomatricula = " + matricula, con);
                String nivel = rs.next() ? rs.getString("nivelmgb") : null;
                if(!UteisValidacao.emptyString(nivel)){
                    JSONObject jsonObject = getFacade().getMgbService().consultarNiveis(empresa);
                    JSONArray lista = new JSONArray(jsonObject.get("data").toString());
                    for (int e = 0; e < lista.length(); e++) {
                        JSONObject obj = lista.getJSONObject(e);
                        if(nivel.equals(obj.getString("publicId"))){
                            return obj.getString("namePTBR");
                        }
                    }
                }

            }
        }catch (Exception e){
            e.printStackTrace();
            Uteis.logar(e, MgbServiceImpl.class);
        }
        return null;
    }

    public String tokenIntegracao(Integer empresa) throws Exception {
        String token = null;
        StringBuilder sql = new StringBuilder();
        sql.append("select token from integracoes where empresa = ");
        sql.append(empresa);
        sql.append(" and integracao = ");
        sql.append(IntegracoesEnum.MGB.getCodigo());
        PreparedStatement ps = con.prepareStatement(sql.toString());
        ResultSet rs = ps.executeQuery();
        if (rs.next()) {
            token = autorizacao(rs.getString("token"));
        }
        return token;
    }

    public void insertToken(String chave, Integer empresa, String token) throws Exception {
        PreparedStatement ps = con.prepareStatement("insert into integracoes (empresa, integracao, token) values (?,?,?)");
        ps.setInt(1, empresa);
        ps.setInt(2, IntegracoesEnum.MGB.getCodigo());
        ps.setString(3, token);
        ps.execute();
        integrado.put(empresa, !UteisValidacao.emptyString(token));
        putLocationPartner(token, chave);
    }


    public String getCaminho(String app) {
        return LegolasService.obterUrls().optString(app);
    }

    public JSONObject consultarNiveis(Integer empresa ) throws Exception {
        String url = getCaminho("apiMgb") + "/api/v1/levels";
        Map<String, String> m = new HashMap<>();
        m.put("Authorization", tokenIntegracao(empresa));
        m.put("Content-Type", "application/json");

        RequestHttpService service = new RequestHttpService();
        RespostaHttpDTO respostaHttpDTO = service.executeRequest(url, m, null, null, MetodoHttpEnum.GET);

        JSONObject jsonObject;
        try {
            jsonObject = new JSONObject(respostaHttpDTO.getResponse());
        } catch (Exception ex) {
            throw new Exception("Erro ao consultar produtos na DOTZ");
        }
        return jsonObject;
    }

    public StudentDTO findByMatricula(Integer matricula, String publicId) throws Exception {

        Map<String, String> enderecoMap = new HashMap<>();
        JSONObject json = new JSONObject();

        String telefone = consultarTelefone(matricula);
        String email = consultarEmail(matricula);
        String codigoMgb = consultarNivelMgb(matricula);
        enderecoMap = consultarEndereco(matricula);

        if (publicId != null) {
            json.put("publicId", publicId);
        }

        if (enderecoMap != null) {
            json.put("address", enderecoMap.get("endereco"));
            json.put("postalCode", enderecoMap.get("cep"));
            json.put("addressComplement", enderecoMap.get("complemento"));
            json.put("neighborhood", enderecoMap.get("bairro"));
            json.put("addressNumber", enderecoMap.get("numero"));
        }

        StringBuilder sql = new StringBuilder();
        sql.append("select pes.nome, pes.datanasc, pes.sexo, cli.matricula,");
        sql.append(" cid.nome as cidade, est.sigla as sigla, p.nome as pais, cli.situacao");
        sql.append(" from  pessoa pes");
        sql.append(" inner join cliente cli on pes.codigo = cli.pessoa");
        sql.append(" left JOIN estado est on pes.estado = est.codigo");
        sql.append(" left JOIN cidade cid on pes.cidade = cid.codigo ");
        sql.append(" left JOIN pais p on pes.pais = p.codigo");
        sql.append(" left JOIN endereco e on pes.codigo = e.pessoa");
        sql.append(" where cli.codigomatricula = ").append(matricula);
        sql.append(" LIMIT 1");

        try (ResultSet rs = SuperFacadeJDBC.criarConsulta(sql.toString(), con)) {
            if (rs.next()) {
                if (publicId != null) {
                    json.put("publicId", publicId);
                }
                json.put("firstName", (Uteis.getPrimeiroNome(rs.getString("nome"))));
                json.put("lastName", ((Uteis.getSobrenome(rs.getString("nome")))));
                json.put("gender", (getSexo(rs.getString("sexo"))));
                json.put("birthday", (rs.getString("datanasc")));
                if (rs.getString("pais") != null ) {
                    json.put("country", (Uteis.obterNomeComApenasPrimeiraLetraMaiuscula(rs.getString("pais"))));
                }
                json.put("state", (rs.getString("sigla")));
                if (rs.getString("cidade") != null) {
                    json.put("city", (Uteis.obterNomeComApenasPrimeiraLetraMaiuscula(rs.getString("cidade"))));
                }
                json.put("email", email);
                if (telefone != null) {
                    json.put("telephone", (Uteis.tirarCaracteres(telefone, true)));
                }
                json.put("status", rs.getString("situacao").equals("AT"));
                json.put("registration", (rs.getString("matricula")));
                json.put("ticket", 0);
                json.put("levelPublicId", codigoMgb == null ? "" : codigoMgb);
                json.put("sendemail", false);
            }
        }
        return JSONMapper.getObject(json, StudentDTO.class);
    }

    public JSONObject montarDadosProfessor(Integer codColaborador, JSONObject professorMgb) throws Exception {
        Map<String, String> enderecoMap = new HashMap<>();
        JSONObject json = new JSONObject();

        String telefone = consultarTelefoneColaborador(codColaborador);
        String email = consultarEmailColaborador(codColaborador);
        enderecoMap = consultarEnderecoColaborador(codColaborador);

        if (enderecoMap != null) {
            json.put("address", enderecoMap.get("endereco"));
            json.put("postalCode", enderecoMap.get("cep"));
            json.put("addressComplement", enderecoMap.get("complemento"));
            json.put("neighborhood", enderecoMap.get("bairro"));
            json.put("addressNumber", enderecoMap.get("numero"));
        }

        StringBuilder sql = new StringBuilder();
        sql.append(" select pes.nome, pes.datanasc, pes.sexo, clb.codigo,");
        sql.append(" cid.nome as cidade, est.sigla as sigla, p.nome as pais, clb.situacao, clb.publicidprofessormgb");
        sql.append(" from  pessoa pes");
        sql.append(" inner join colaborador clb on pes.codigo = clb.pessoa");
        sql.append(" left join estado est on pes.estado = est.codigo");
        sql.append(" left join cidade cid on pes.cidade = cid.codigo");
        sql.append(" left join pais p on pes.pais = p.codigo");
        sql.append(" left join endereco e on pes.codigo = e.pessoa");
        sql.append(" where clb.codigo = ").append(codColaborador);
        sql.append(" limit 1");

        try (ResultSet rs = SuperFacadeJDBC.criarConsulta(sql.toString(), con)) {
            if (rs.next()) {
                json.put("publicId", UteisValidacao.emptyString(rs.getString("publicidprofessormgb")) ? null : rs.getString("publicidprofessormgb"));
                json.put("firstName", (Uteis.getPrimeiroNome(rs.getString("nome"))));
                json.put("lastName", ((Uteis.getSobrenome(rs.getString("nome")))));
                json.put("gender", (getSexo(rs.getString("sexo"))));
                json.put("birthday", (rs.getString("datanasc")));
                if (rs.getString("pais") != null ) {
                    json.put("country", (Uteis.obterNomeComApenasPrimeiraLetraMaiuscula(rs.getString("pais"))));
                }
                json.put("state", (rs.getString("sigla")));
                if (rs.getString("cidade") != null) {
                    json.put("city", (Uteis.obterNomeComApenasPrimeiraLetraMaiuscula(rs.getString("cidade"))));
                }
                json.put("email", email);
                if (telefone != null) {
                    json.put("telephone", (Uteis.tirarCaracteres(telefone, true)));
                }
                json.put("status", rs.getString("situacao").equals("AT"));
                json.put("sendemail", false);
                json.put("document", "");

                // caso já exista o professorMgb, manter profiles e userPermissions que vem do MGB
                if (professorMgb == null) {
                    Integer[] profiles = new Integer[1];
                    profiles[0] = 3;
                    json.put("profiles", profiles); // 1 - Owner; 2 - Coordinator; 3 - Teacher; 6 - Manager; 8 - Secretary

                    Integer[] userPermissions = new Integer[1];
                    userPermissions[0] = 1;
                    json.put("userPermissions", userPermissions); // 1 - Read; 2 - Assistance; 3 - Teaching; 4 - Administrator
                } else {
                    json.put("profiles", professorMgb.getJSONArray("profiles"));
                    json.put("userPermissions", professorMgb.getJSONArray("userPermissions"));
                }
            }
        }
        return json;
    }

    @Override
    public String verificarPublicidmgb(Integer matricula) throws Exception {
        String codigoMgb = null;
        StringBuilder sql = new StringBuilder();
        sql.append("select publicidmgb from cliente where codigomatricula = ").append(matricula);
        ResultSet rs = SuperFacadeJDBC.criarConsulta(sql.toString(), con);

        if (rs.next()) {
            codigoMgb = !UteisValidacao.emptyString(rs.getString("publicidmgb")) ? rs.getString("publicidmgb") : null;
        }
        return codigoMgb;
    }

    public String consultarTelefone(Integer matricula) throws Exception {
        String telefone = null;

        StringBuilder sql = new StringBuilder();
        sql.append("select tel.numero from telefone tel");
        sql.append(" inner join cliente cli on cli.pessoa = tel.pessoa");
        sql.append(" where cli.codigomatricula = ").append(matricula);
        sql.append(" LIMIT 1");
        PreparedStatement ps = getCon().prepareStatement(sql.toString());
        ResultSet rs = SuperFacadeJDBC.criarConsulta(sql.toString(), con);
        if (rs.next()) {
            telefone = rs.getString("numero");
        }
        return telefone;
    }

    public String consultarTelefoneColaborador(Integer codColaborador) throws Exception {
        String telefone = null;
        StringBuilder sql = new StringBuilder();
        sql.append(" select tel.numero from telefone tel");
        sql.append(" inner join colaborador c on c.pessoa = tel.pessoa");
        sql.append(" where c.codigo = ").append(codColaborador);
        sql.append(" LIMIT 1");
        PreparedStatement ps = getCon().prepareStatement(sql.toString());
        ResultSet rs = SuperFacadeJDBC.criarConsulta(sql.toString(), con);
        if (rs.next()) {
            telefone = rs.getString("numero");
        }
        return telefone;
    }

    public String consultarEmail(Integer matricula) throws Exception {
        String email = null;
        StringBuilder sql = new StringBuilder();
        sql.append("select e.email from email e");
        sql.append(" inner join cliente cli on cli.pessoa = e.pessoa");
        sql.append(" where cli.codigomatricula = ").append(matricula);
        sql.append(" LIMIT 1");

        ResultSet rs = SuperFacadeJDBC.criarConsulta(sql.toString(), con);
        if (rs.next()) {
            email = rs.getString("email");
        }
        return email;
    }

    public String consultarEmailColaborador(Integer codColaborador) throws Exception {
        String email = null;
        StringBuilder sql = new StringBuilder();
        sql.append(" select e.email from email e");
        sql.append(" inner join colaborador c on c.pessoa = e.pessoa");
        sql.append(" where c.codigo = ").append(codColaborador);
        sql.append(" LIMIT 1");

        ResultSet rs = SuperFacadeJDBC.criarConsulta(sql.toString(), con);
        if (rs.next()) {
            email = rs.getString("email");
        }
        return email;
    }

    public Map consultarEndereco(Integer matricula) throws Exception {
        Map<String, String> headersMap = new HashMap<>();

        StringBuilder sql = new StringBuilder();
        sql.append("select e.cep, e.bairro, e.numero, e.complemento, e.endereco");
        sql.append(" from endereco e ");
        sql.append(" inner join cliente cli on e.pessoa = cli.pessoa ");
        sql.append(" where cli.codigomatricula = ").append(matricula);
        sql.append(" limit 1");

        ResultSet rs = SuperFacadeJDBC.criarConsulta(sql.toString(), con);
        if (rs.next()) {
            headersMap.put("cep", rs.getString("cep"));
            headersMap.put("bairro", rs.getString("bairro"));
            headersMap.put("numero", rs.getString("numero"));
            headersMap.put("complemento", rs.getString("complemento"));
            headersMap.put("endereco", rs.getString("endereco"));
        }
        return headersMap;
    }

    public Map consultarEnderecoColaborador(Integer codColaborador) throws Exception {
        Map<String, String> headersMap = new HashMap<>();
        StringBuilder sql = new StringBuilder();
        sql.append(" select e.cep, e.bairro, e.numero, e.complemento, e.endereco");
        sql.append(" from endereco e ");
        sql.append(" inner join colaborador c on e.pessoa = c.pessoa ");
        sql.append(" where c.codigo = ").append(codColaborador);
        sql.append(" limit 1");
        ResultSet rs = SuperFacadeJDBC.criarConsulta(sql.toString(), con);
        if (rs.next()) {
            headersMap.put("cep", rs.getString("cep"));
            headersMap.put("bairro", rs.getString("bairro"));
            headersMap.put("numero", rs.getString("numero"));
            headersMap.put("complemento", rs.getString("complemento"));
            headersMap.put("endereco", rs.getString("endereco"));
        }
        return headersMap;
    }

    public String consultarNivelMgb(Integer matricula) throws Exception {
        String codigoMgb = null;
        StringBuilder sql = new StringBuilder();
        sql.append("select  distinct n.codigomgb from cliente as cli");
        sql.append(" inner join  pessoa pes ON cli.pessoa = pes.codigo");
        sql.append(" inner join matriculaalunohorarioturma m on pes.codigo = m.pessoa");
        sql.append(" inner join horarioturma h2  ON h2.codigo = m.horarioturma");
        sql.append(" inner join turma t ON t.codigo = h2.turma");
        sql.append(" inner join nivelturma n on h2.nivelturma = n.codigo");
        sql.append(" where cli.codigomatricula  = " + matricula);
        sql.append(" and current_date between m.datainicio and m.datafim ");
        sql.append(" and n.codigo is not null and n.codigomgb <> '' and n.codigomgb <> '0'");
        sql.append(" LIMIT 1");
        PreparedStatement ps = getCon().prepareStatement(sql.toString());
        ResultSet rs = ps.executeQuery();
        if (rs.next()) {
            codigoMgb = rs.getString("codigomgb");
        }

        /*
        * Removido trecho que buscava pelo codigomgb diretamente no nívelturma quando o aluno não esta presente em uma turma
        * Segundo a regra do documento, o aluno precisa ter um plano ativo, de turma, e essa turma deve possuir o nível associado ao sistema MGB para poder sincronizar o aluno.
        */

        return codigoMgb;
    }

    public void alterarpublicidmgb(Integer matricula, String publicidmgb, String body) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("update cliente set publicidmgb =  '").append(publicidmgb);
        sql.append("' where codigomatricula = ").append(matricula);
        SuperFacadeJDBC.executarConsultaUpdate(sql.toString(), con);
        if(body == null){
            return;
        }
//        O NÍVEL SERÁ CONSIDERADO O DA TURMA EM QUE O ALUNO ESTÁ INSERIDO
//        try {
//            JSONObject bodyJson = new JSONObject(body);
//            if(!UteisValidacao.emptyString(bodyJson.optString("idNivel"))){
//                sql = new StringBuilder();
//                sql.append("update cliente set nivelmgb = '").append(bodyJson.optString("idNivel"));
//                sql.append("' where codigomatricula = ").append(matricula);
//                SuperFacadeJDBC.executarConsultaUpdate(sql.toString(), con);
//            }
//        }catch (Exception e){
//            Uteis.logar(null, e);
//        }
    }

    public void alterarPublicIdProfessorMgb(Integer codColaborador, String publicIdProfessorMgb) throws Exception {
        try {
            StringBuilder sql = new StringBuilder();
            sql.append(" update colaborador set publicIdProfessorMgb = '").append(publicIdProfessorMgb).append("' ");
            sql.append(" where codigo = ").append(codColaborador);
            SuperFacadeJDBC.executarConsultaUpdate(sql.toString(), con);
        } catch (Exception ex) {
            Uteis.logarDebug("Erro ao atualizar no banco o publicIdProfessorMgb do professor " + ex.getMessage());
            throw new ServiceException("Erro ao atualizar no banco o publicIdProfessorMgb do professor.");
        }
    }

    public void alterarPublicIdTurmaMgb(Integer codHorarioTurma, String publicIdHorarioTurmaMgb) throws Exception {
        try {
            StringBuilder sql = new StringBuilder();
            sql.append(" update horarioturma set publicIdTurmaMgb = '").append(publicIdHorarioTurmaMgb).append("' ");
            sql.append(" where codigo = ").append(codHorarioTurma);
            SuperFacadeJDBC.executarConsultaUpdate(sql.toString(), con);
        } catch (Exception ex) {
            Uteis.logarDebug("Erro ao atualizar no banco o publicIdTurmaMgb do horarioturma " + ex.getMessage());
            throw new ServiceException("Erro ao atualizar no banco o publicIdTurmaMgb do horario turma.");
        }
    }

    public void removerPublicIdTurmaMgb(Integer codHorarioTurma) throws Exception {
        try {
            StringBuilder sql = new StringBuilder();
            sql.append("update horarioturma set publicIdTurmaMgb = null where codigo = ").append(codHorarioTurma);
            SuperFacadeJDBC.executarConsultaUpdate(sql.toString(), con);
        } catch (Exception ex) {
            Uteis.logarDebug("Erro ao remover no banco o publicIdTurmaMgb do horarioturma " + ex.getMessage());
        }
    }

    private Integer getSexo(String sexo) {
        int retorno = 0;
        if (sexo != null) {
            retorno = sexo.contains("F") ? 2 : 1;
        }
        return retorno;
    }

    public void updateToken(String chave, Integer empresa, String token) throws Exception {
        PreparedStatement ps = con.prepareStatement("update integracoes set token = ? where empresa = ? and integracao = ?");
        ps.setString(1, token);
        ps.setInt(2, empresa);
        ps.setInt(3, IntegracoesEnum.MGB.getCodigo());
        ps.execute();
        putLocationPartner(token, chave);
    }

    public void putStudent(Integer empresa, Integer matricula, String publicIdMgb, Boolean sincronizarComTurma) throws Exception {
        String token = tokenIntegracao(empresa);
        if (token != null) {
            String url = getCaminho("apiMgb") + "/api/v1/student/" + publicIdMgb;
            StudentDTO studentDTO = findByMatricula(matricula, publicIdMgb);
            // se aluno não possuir publicid do nivel ou estar inativo chamar o change status
            if (UteisValidacao.emptyString(studentDTO.getLevelPublicId()) || "0".equals(studentDTO.getLevelPublicId()) || !studentDTO.isStatus()) {
                changeStatusStudent(empresa, publicIdMgb, false);
                syncronizarAlunoTurmaMgb(empresa, matricula, publicIdMgb, 2);
            } else {
                Map<String, String> m = new HashMap<>();
                m.put("Authorization", tokenIntegracao(empresa));
                m.put("Content-Type", "application/json");
                RequestHttpService service = new RequestHttpService();
                RespostaHttpDTO respostaHttpDTO = service.executeRequest(url, m, null, studentDTO.toString(), MetodoHttpEnum.PUT);
                try {
                    Boolean success = new JSONObject(respostaHttpDTO.getResponse()).getBoolean("success");
                    if (!success) {
                        Uteis.logarDebug("Não foi possível atualizar o aluno: " + respostaHttpDTO.getResponse());
                        throw new ServiceException("Não foi possível atualizar o aluno");
                    }
                    String publicId = new JSONObject(respostaHttpDTO.getResponse()).getJSONObject("data").getString("publicId");
                    if (sincronizarComTurma) {
                        syncronizarAlunoTurmaMgb(empresa, matricula, publicIdMgb, 1);
                    }
                } catch (Exception e) {
                    Uteis.logarDebug("Não foi possível atualizar o aluno: " + respostaHttpDTO.getResponse());
                    throw new ServiceException("Não foi possível atualizar o aluno");
                }
            }
        }
    }

    public void changeStatusStudent(Integer empresa, String publicId, boolean status) throws Exception {
        String token = tokenIntegracao(empresa);
        if (token != null) {
            String url = getCaminho("apiMgb") + "/api/v1/student/" + publicId + "/change-status";
            JSONObject bodyJson = new JSONObject();
            bodyJson.put("publicId", publicId);
            bodyJson.put("status", status);
            Map<String, String> headers = new HashMap<>();
            headers.put("Authorization", tokenIntegracao(empresa));
            headers.put("Content-Type", "application/json");
            RequestHttpService httpService = new RequestHttpService();
            RespostaHttpDTO respostaHttpDTO = httpService.executeRequest(url, headers, null, bodyJson.toString(), MetodoHttpEnum.PUT);
            try {
                Boolean success = new JSONObject(respostaHttpDTO.getResponse()).getBoolean("success");
                if (!success) {
                    Uteis.logarDebug("Não foi possível atualizar o status do aluno: " + respostaHttpDTO.getResponse());
                    throw new ServiceException("Não foi possível atualizar o status do aluno");
                }
            } catch (Exception e) {
                Uteis.logarDebug("Não foi possível atualizar o status do aluno: " + respostaHttpDTO.getResponse());
                throw new ServiceException("Não foi possível atualizar o status do aluno");
            }
        }
    }

    public boolean verificarSeExisteAlunoPorPublicId(Integer empresa, String publicIdAlunoMgb) throws Exception {
        String token = tokenIntegracao(empresa);
        if (UteisValidacao.emptyString(token)) {
            return false;
        }
        if (UteisValidacao.emptyString(publicIdAlunoMgb)) {
            return false;
        }
        try {
            String url = getCaminho("apiMgb") + "/api/v1/student/" + publicIdAlunoMgb;
            Map<String, String> m = new HashMap<>();
            m.put("Authorization", token);
            m.put("Content-Type", "application/json");
            RequestHttpService service = new RequestHttpService();
            RespostaHttpDTO respostaHttpDTO = service.executeRequest(url, m, null, null, MetodoHttpEnum.GET);
            JSONObject jsonObject;
            jsonObject = new JSONObject(respostaHttpDTO.getResponse());
            if (jsonObject.has("success") && jsonObject.has("data")) {
                return true;
            } else {
                Uteis.logarDebug("Aluno não encontrato no mgb: " + publicIdAlunoMgb + " - " + respostaHttpDTO.getResponse());
            }
        } catch (Exception ex) {
            ex.printStackTrace();
            Uteis.logarDebug("Ocorreu um erro ao tentar verificar se já existe um aluno mgb com o publicId: " + publicIdAlunoMgb + " Erro: " + ex.getMessage());
        }
        return false;
    }

    public void putLocationPartner(String token, String chave) throws Exception {
        try {
            if (token != null) {
                String url = getCaminho("apiMgb") + "/api/v1/locationpartner";
                LocationPartnerDTO locationPartnerDTO = new LocationPartnerDTO();
                locationPartnerDTO.setClientToken(chave);
                Map<String, String> m = new HashMap<>();
                m.put("Authorization", "Bearer " + token);
                m.put("Content-Type", "application/json");
                RequestHttpService service = new RequestHttpService();
                RespostaHttpDTO respostaHttpDTO = service.executeRequest(url, m, null, locationPartnerDTO.toString(), MetodoHttpEnum.PUT);
                Uteis.logarDebug("retorno mgb token update: " + respostaHttpDTO.getResponse());
            }
        }catch (Exception e){
            e.printStackTrace();
            Uteis.logar(e, MgbServiceImpl.class);
        }
    }

    public JSONArray tokens() throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("select token, empresa from integracoes where integracao = ");
        sql.append(IntegracoesEnum.MGB.getCodigo());
        PreparedStatement ps = con.prepareStatement(sql.toString());
        ResultSet rs = ps.executeQuery();
        JSONArray array = new JSONArray();
        while (rs.next()) {
            JSONObject obj = new JSONObject();
            obj.put("empresa", rs.getInt("empresa"));
            obj.put("token", rs.getString("token"));
            array.put(obj);
        }
        return array;
    }

    public String sincronizarAlunosAtivos(Integer empresa) throws Exception {
        if (integradoMgb(empresa)) {
            String sqlAlunosAtivosSemIDMgb = "select codigomatricula, empresa from cliente where situacao = 'AT' and publicidmgb is null and empresa = " + empresa;
            try (ResultSet rs = SuperFacadeJDBC.criarConsulta(sqlAlunosAtivosSemIDMgb, con)) {
                int totalVerificados = 0;
                int totalSincronizados = 0;
                int totalNaoSincronizados = 0;
                while (rs.next()) {
                    try {
                        postStudentMgb(rs.getInt("empresa"), rs.getInt("codigomatricula"), false, false);
                        totalSincronizados++;
                    } catch (Exception ex) {
                        totalNaoSincronizados++;
                        Uteis.logarDebug("Erro ao sincronizar o aluno: " + rs.getInt("codigomatricula") + " - " + ex.getMessage());
                    }
                    totalVerificados++;
                }
                if (totalVerificados == totalSincronizados && totalNaoSincronizados == 0) {
                    return "Todos " + totalSincronizados + " alunos ativos foram sincronizados com sucesso";
                } else {
                    return totalVerificados + " alunos verificados. " + totalSincronizados + " alunos sincronizados. " + totalNaoSincronizados + " não sincronizados.";
                }
            } catch (Exception ex) {
                Logger.getLogger(getClass().getName()).log(Level.SEVERE, null, ex);
                return "error: " + ex.getMessage();
            }
        } else {
            return "Não foi localizada nenhuma integração com o MGB, o procedimento foi cancelado!";
        }
    }

    public JSONObject consultarPiscinas(Integer empresa) throws Exception {
        String url = getCaminho("apiMgb") + "/api/v1/teachingareas";
        Map<String, String> m = new HashMap<>();
        m.put("Authorization", tokenIntegracao(empresa));
        m.put("Content-Type", "application/json");

        RequestHttpService service = new RequestHttpService();
        RespostaHttpDTO respostaHttpDTO = service.executeRequest(url, m, null, null, MetodoHttpEnum.GET);

        JSONObject jsonObject;
        try {
            jsonObject = new JSONObject(respostaHttpDTO.getResponse());
        } catch (Exception ex) {
            throw new Exception("Erro ao consultar as piscinas no MGB");
        }
        return jsonObject;
    }

    public String syncTodasTurmasComMgb(Integer empresa, String ctx, Boolean vaiValidarVagas, Integer margemMinutoInicioFimHorario) {
        // Caso vaiValidarVagas = false, irá ignorar validação de quantidade de vagas no momento da sincronização
        // Caso margemMinutoInicioFimHorario for maior que 0 na validação da hora de inicio ou fim vai adicionar margem de minuto,
        // - por exemplo, se for passado o valor 1, e na pacto o horario da turma terminar as 10:30 e no mgb tiver sido cadastrado 10:29 ou 10:31
        // - será considerado igual e não vai criar uma nova turma no mgb mas vai associar o horario da pacto à turma do mgb
        try {
            if (!integradoMgb(empresa)) {
                return "A integração com o MGB não está ativa!";
            }
            Uteis.logarDebug("#### Iniciando sincronização de todas as turmas com o MGB para a chave: " + ctx + " empresa: " + empresa);
            StringBuilder sql = new StringBuilder();
            // aulacoletiva is false pois apenas turmas são sincronizadas com mgb, e quando esse campo é true indica que é aula coletiva
            sql.append("select codigo from turma t where t.aulacoletiva is false and t.datafinalvigencia > current_date");
            JSONObject listSyncs = new JSONObject();
            try (ResultSet rs = SuperFacadeJDBC.criarConsulta(sql.toString(), con)) {
                while (rs.next()) {
                    String chaveJson = "CodTurma: " + rs.getInt("codigo");
                    String retornoSyncTurma = syncTurmaMgb(ctx, empresa, rs.getInt("codigo"), vaiValidarVagas, margemMinutoInicioFimHorario);
                    listSyncs.put(chaveJson, "Retorno sync: " + retornoSyncTurma);
                }
            }
            JSONObject retorno = new JSONObject();
            retorno.put("Retorno", listSyncs);
            Uteis.logarDebug("#### Terminando sincronização de todas as turmas com o MGB para a chave: " + ctx + " empresa: " + empresa);
            return retorno.toString();
        } catch (Exception ex) {
            ex.printStackTrace();
            Uteis.logarDebug("Erro: " + ex.getMessage());
            return "Erro: " + ex.getMessage();
        }
    }

    public String syncHorarioTurmaMgb(String ctx, Integer empresa, Integer codHorarioTurma, Boolean vaiValidarVagas, Integer margemMinutoInicioFimHorario) {
        try {
            if (!integradoMgb(empresa)) {
                return "A integração com o MGB não está ativa!";
            }
            Uteis.logarDebug("#### Iniciando sincronizacao do horario de turma com cod: " + codHorarioTurma + " com o MGB para a chave: " + ctx + " empresa: " + empresa);
            int total = 0, totalSucesso = 0, totalErro = 0;
            JSONArray turmasMgb = getTurmasMgb(empresa, true, true);
            Map<Integer, String> professoresJaSincronizados = new HashMap<>();

            HorarioTurmaVO ht = getHorarioDao().consultarPorChavePrimaria(codHorarioTurma, Uteis.NIVELMONTARDADOS_TODOS);
            String nomeTurma = "";
            Date datafinalvigencia = null;
            try (ResultSet rs = buscarTurmaPorCodigo(ht.getTurma())) {
                if (rs.next()) {
                    nomeTurma = rs.getString("descricao");
                    datafinalvigencia = rs.getDate("datafinalvigencia");
                }
            }
            total++;
            if (podeSincronizarTurma(datafinalvigencia, ht)) {
                if (sincronizarTurma(ht, nomeTurma, empresa, turmasMgb, professoresJaSincronizados, vaiValidarVagas, margemMinutoInicioFimHorario)) {
                    totalSucesso++;
                } else {
                    totalErro++;
                }
            } else if (!UteisValidacao.emptyString(ht.getPublicIdTurmaMgb())) {
                putChangeStatusTurmaMgb(empresa, ht.getPublicIdTurmaMgb(), false);
            }

            Uteis.logarDebug("#### Terminando sincronizacao do horario de turma com cod: " + codHorarioTurma + " com o MGB para a chave: " + ctx + " empresa: " + empresa);
            return gerarMensagemSincronizacao(total, totalSucesso, totalErro);
        } catch (Exception ex) {
            ex.printStackTrace();
            Uteis.logarDebug("Erro: " + ex.getMessage());
            return "Erro: " + ex.getMessage();
        }
    }

    public String syncTurmaMgb(String ctx, Integer empresa, Integer codTurma, Boolean vaiValidarVagas, Integer margemMinutoInicioFimHorario) {
        try {
            if (!integradoMgb(empresa)) {
                return "A integração com o MGB não está ativa!";
            }
            Uteis.logarDebug("#### Iniciando sincronizacao da turma cod: " + codTurma + " com o MGB para a chave: " + ctx + " empresa: " + empresa);
            int total = 0, totalSucesso = 0, totalErro = 0;
            JSONArray turmasMgb = getTurmasMgb(empresa, true, true);
            Map<Integer, String> professoresJaSincronizados = new HashMap<>();
            try (ResultSet rs = buscarTurmaPorCodigo(codTurma)) {
                if (rs.next()) {
                    List<HorarioTurmaVO> listHorarioTurma = getHorarioDao().consultarTodosHorariosTurma(codTurma, Uteis.NIVELMONTARDADOS_TODOS);
                    for (HorarioTurmaVO ht : listHorarioTurma) {
                        total++;
                        if (podeSincronizarTurma(rs, ht)) {
                            if (sincronizarTurma(ht, rs.getString("descricao"), empresa, turmasMgb, professoresJaSincronizados, vaiValidarVagas, margemMinutoInicioFimHorario)) {
                                totalSucesso++;
                            } else {
                                totalErro++;
                            }
                        } else if (!UteisValidacao.emptyString(ht.getPublicIdTurmaMgb())) {
                            putChangeStatusTurmaMgb(empresa, ht.getPublicIdTurmaMgb(), false);
                        }
                    }
                }
            }
            Uteis.logarDebug("#### Terminando sincronizacao da turma cod: " + codTurma + " com o MGB para a chave: " + ctx + " empresa: " + empresa);
            return gerarMensagemSincronizacao(total, totalSucesso, totalErro);
        } catch (Exception ex) {
            ex.printStackTrace();
            Uteis.logarDebug("Erro: " + ex.getMessage());
            return "Erro: " + ex.getMessage();
        }
    }

    private ResultSet buscarTurmaPorCodigo(Integer codTurma) throws Exception {
        String sql = "SELECT descricao, datafinalvigencia FROM turma WHERE codigo = " + codTurma;
        return SuperFacadeJDBC.criarConsulta(sql, con);
    }

    private boolean podeSincronizarTurma(ResultSet rs, HorarioTurmaVO ht) throws SQLException {
        return !UteisValidacao.dataMenorDataAtual(rs.getDate("datafinalvigencia")) && "AT".equalsIgnoreCase(ht.getSituacao());
    }

    private boolean podeSincronizarTurma(Date datafinalvigencia, HorarioTurmaVO ht) throws SQLException {
        return !UteisValidacao.dataMenorDataAtual(datafinalvigencia) && "AT".equalsIgnoreCase(ht.getSituacao());
    }

    private boolean sincronizarTurma(HorarioTurmaVO ht, String nomeTurma, Integer empresa, JSONArray turmasMgb, Map<Integer, String> professoresJaSincronizados, Boolean vaiValidarVagas, Integer margemMinutoInicioFimHorario) {
        try {
            // verificar se na lista de turmas mgb já existe turma com dados 100% iguais ao horarioturma
            // caso exista apenas vincular o publicid da turma mgb ao horarioturma // caso não exista, prosseguir com a sincronização
            String publicIdTurmaMgbIgualHT = UteisValidacao.emptyString(ht.getPublicIdTurmaMgb())
                    ? existeTurmaMgbIgualHT(turmasMgb, ht, vaiValidarVagas, margemMinutoInicioFimHorario)
                    : null;

            if (publicIdTurmaMgbIgualHT != null) {
                alterarPublicIdTurmaMgb(ht.getCodigo(), publicIdTurmaMgbIgualHT);
                return true;
            }

            // validação para não enviar muitas requisições desnecessariamente para o MGB caso o professor já tenha sido sincronizado no horário anterior
            String publicIdProfessorMgb = obterPublicIdProfessor(ht, empresa, professoresJaSincronizados);

            if (publicIdProfessorMgb.isEmpty()) {
                return false;
            }

            validacoesTurmaParaSincronizar(ht, publicIdProfessorMgb);
            JSONObject jsonHorarioTurma = montarJsonTurma(empresa, nomeTurma, ht, publicIdProfessorMgb);

            if (UteisValidacao.emptyString(ht.getPublicIdTurmaMgb()) || !verificarSeExisteTurmaMgbPorPublicId(empresa, ht.getPublicIdTurmaMgb())) {
                jsonHorarioTurma.put("publicId", "");
                postTurmaMgb(empresa, jsonHorarioTurma, ht.getCodigo());
            } else {
                putTurmaMgb(empresa, jsonHorarioTurma, ht.getPublicIdTurmaMgb());
            }

            return true;
        } catch (Exception e) {
            e.printStackTrace();
            Uteis.logarDebug("Erro ao preparar dados da turma para sincronizar com mgb: " + e.getMessage());
            return false;
        }
    }

    private String obterPublicIdProfessor(HorarioTurmaVO ht, Integer empresa, Map<Integer, String> professoresJaSincronizados) {
        int codProfessor = ht.getProfessor().getCodigo();
        if (professoresJaSincronizados.containsKey(codProfessor)) {
            return professoresJaSincronizados.get(codProfessor);
        }
        try {
            String publicId = salvarProfessorMgb(empresa, codProfessor);
            professoresJaSincronizados.put(codProfessor, publicId);
            return publicId;
        } catch (Exception e) {
            e.printStackTrace();
            Uteis.logarDebug("Erro ao obter publicid do professor: " + e.getMessage());
            return "";
        }
    }

    private String gerarMensagemSincronizacao(int total, int totalSucesso, int totalErro) {
        if (totalSucesso == total) {
            return "Sucesso, os horários da turma foram sincronizados com o MGB, total: " + total;
        }
        return "ATENCAO_" + total + "_" + totalSucesso + "_" + totalErro;
    }




    private String existeTurmaMgbIgualHT(JSONArray turmasMgb, HorarioTurmaVO ht, Boolean vaiValidarVagas, Integer margemMinutoInicioFimHorario) throws Exception {
        // O COMENTÁRIO A SEGUIR DEVERÁ SER UTILIZADO SOMENTE EM CASO DO PROCESSO RODADO MANUALMENTE /prest/mgb/sync-todas-turmas-com-mgb, CASO SEJA FLUXO NORMAL TODAS AS VALIDAÇÕES DEVEM SER RESPEITADAS
        // Caso vaiValidarVagas = false, irá ignorar validação de quantidade de vagas no momento da sincronização
        // Caso margemMinutoInicioFimHorario for maior que 0 na validação da hora de inicio ou fim vai adicionar margem de minuto,
        // - por exemplo, se for passado o valor 1, e na pacto o horario da turma terminar as 10:30 e no mgb tiver sido cadastrado 10:29 ou 10:31
        // - será considerado igual e não vai criar uma nova turma no mgb mas vai associar o horario da pacto à turma do mgb

        try {
            for (int i = 0; i < turmasMgb.length(); i++) {
                JSONObject turmaMgb = turmasMgb.getJSONObject(i);
                if (turmaMgb.has("classDays") && !turmaMgb.getJSONArray("classDays").get(0).equals(obterDiaSemanaMgb(ht)[0])) {
                    continue;
                }
                if (turmaMgb.has("classTeachers") && turmaMgb.getJSONArray("classTeachers").length() > 0) {
                    String email = consultarEmailColaborador(ht.getProfessor().getCodigo());
                    JSONObject jsonProfessor = (JSONObject) turmaMgb.getJSONArray("classTeachers").get(0);
                    if (jsonProfessor.has("email") && !jsonProfessor.getString("email").equals(email)) {
                        continue;
                    }
                }
                if (turmaMgb.has("classTeachingAreaPublicId") && !turmaMgb.getString("classTeachingAreaPublicId").contains(ht.getAmbiente().getCodigoPiscinaMgb())) {
                    continue;
                }
                if (turmaMgb.has("levelPublicId") && !turmaMgb.getString("levelPublicId").contains(ht.getNivelTurma().getCodigoMgb())) {
                    continue;
                }

                if (margemMinutoInicioFimHorario != null && margemMinutoInicioFimHorario > 0) {
                    if (turmaMgb.has("startTime")) {
                        LocalTime startTime = LocalTime.parse(turmaMgb.getString("startTime"), DateTimeFormatter.ofPattern("HH:mm:ss"));
                        LocalTime horaInicialComMargemMais = LocalTime.parse(ht.getHoraInicial(), DateTimeFormatter.ofPattern("HH:mm")).plusMinutes(margemMinutoInicioFimHorario);
                        LocalTime horaInicialComMargemMenos = LocalTime.parse(ht.getHoraInicial(), DateTimeFormatter.ofPattern("HH:mm")).minusMinutes(margemMinutoInicioFimHorario);

                        if (!(startTime.compareTo(horaInicialComMargemMenos) >= 0 && startTime.compareTo(horaInicialComMargemMais) <= 0)) {
                            continue;
                        }
                    }
                    if (turmaMgb.has("endTime")) {
                        LocalTime endTime = LocalTime.parse(turmaMgb.getString("endTime"), DateTimeFormatter.ofPattern("HH:mm:ss"));
                        LocalTime horaInicialComMargemMais = LocalTime.parse(ht.getHoraFinal(), DateTimeFormatter.ofPattern("HH:mm")).plusMinutes(margemMinutoInicioFimHorario);
                        LocalTime horaInicialComMargemMenos = LocalTime.parse(ht.getHoraFinal(), DateTimeFormatter.ofPattern("HH:mm")).minusMinutes(margemMinutoInicioFimHorario);

                        if (!(endTime.compareTo(horaInicialComMargemMenos) >= 0 && endTime.compareTo(horaInicialComMargemMais) <= 0)) {
                            continue;
                        }
                    }
                } else {
                    if (turmaMgb.has("startTime") && !turmaMgb.getString("startTime").contains(ht.getHoraInicial())) {
                        continue;
                    }
                    if (turmaMgb.has("endTime") && !turmaMgb.getString("endTime").contains(ht.getHoraFinal())) {
                        continue;
                    }
                }

                if (vaiValidarVagas) {
                    if (turmaMgb.has("vacancy") && turmaMgb.getInt("vacancy") != ht.getNrMaximoAluno()) {
                        continue;
                    }
                }
                return turmaMgb.getString("publicId");
            }
        } catch (Exception e) {
            e.printStackTrace();
            Uteis.logarDebug("Erro ao verificar se já existe turma mgb semelhante ao horário: " + e.getMessage());
        }
        return null;
    }

    public void validacoesTurmaParaSincronizar(HorarioTurmaVO ht, String publicIdProfessorMgb) throws Exception {
        if (ht.getAmbiente() != null && (UteisValidacao.emptyString(ht.getAmbiente().getCodigoPiscinaMgb()) || "0".equalsIgnoreCase(ht.getAmbiente().getCodigoPiscinaMgb()))) {
            throw new Exception("ERRO_HORARIO_COM_AMBIENTE_INCORRETO");
        }

        if (ht.getNivelTurma() != null && (UteisValidacao.emptyString(ht.getNivelTurma().getCodigoMgb()) || "0".equalsIgnoreCase(ht.getNivelTurma().getCodigoMgb()))) {
            throw new Exception("ERRO_HORARIO_COM_NIVEL_INCORRETO");
        }

        if (UteisValidacao.emptyString(publicIdProfessorMgb)) {
            throw new ServiceException("ERRO_PROFESSOR_NAO_LOCALIZADO_SINCRONIZADO");
        }
    }

    public JSONObject montarJsonTurma(Integer empresa, String nome, HorarioTurmaVO ht, String publicIdProfessorMgb) throws Exception {
        JSONObject jsonHorarioTurma = new JSONObject();
        jsonHorarioTurma.put("publicId", ht.getPublicIdTurmaMgb());
        jsonHorarioTurma.put("name", nome);
        jsonHorarioTurma.put("startTime", ht.getHoraInicial());
        jsonHorarioTurma.put("endTime", ht.getHoraFinal());
        jsonHorarioTurma.put("status", true);
        jsonHorarioTurma.put("classTeachingAreaPublicId", ht.getAmbiente().getCodigoPiscinaMgb());
        jsonHorarioTurma.put("classDays", obterDiaSemanaMgb(ht));
        jsonHorarioTurma.put("levelPublicId", ht.getNivelTurma().getCodigoMgb());
        jsonHorarioTurma.put("vacancy", ht.getNrMaximoAluno());
        jsonHorarioTurma.put("classStudentsPublicIds", prepararAlunosTurma(empresa, ht.getCodigo()));

        String[] classTeachersPublicIds = new String[1];
        classTeachersPublicIds[0] = publicIdProfessorMgb;
        jsonHorarioTurma.put("classTeachersPublicIds", classTeachersPublicIds);

        return jsonHorarioTurma;
    }

    private List<String> prepararAlunosTurma(Integer empresa, Integer codigoHorarioTurma) {
        List<String>  list = new ArrayList();
        // consultar alunos matriculados e vigentes no horario
        StringBuilder sql = new StringBuilder();
        sql.append(
                "select c.codigo, c.codigomatricula, c.publicidmgb \n" +
                "from matriculaalunohorarioturma m \n" +
                "inner join cliente c on c.pessoa = m.pessoa \n" +
                "where current_date between datainicio and datafim \n" +
                "and horarioturma = " + codigoHorarioTurma
        );
        try (ResultSet rs = SuperFacadeJDBC.criarConsulta(sql.toString(), con)) {
            while (rs.next()) {
                String publicIdAluno = rs.getString("publicidmgb");
                try {
                    // este fluxo postStudentMgb será utilizado pois sempre que a turma for sincronzada vai atualizar os alunos dela tbm, para garantir que os dados estejam sempre atualizados no MGB
                    postStudentMgb(empresa, rs.getInt("codigomatricula"), false, false);
                    publicIdAluno = obterPublicIdAlunoPorCodigoCliente(rs.getInt("codigo"));
                } catch (Exception e) {
                    Uteis.logarDebug("Erro ao preparar aluno mat: " + rs.getInt("codigomatricula") + " - erro: " + e.getMessage());
                }
                // só deverá ser sincronizado com a turma alunos que possuem um publicId
                if (!UteisValidacao.emptyString(publicIdAluno)) {
                    list.add(publicIdAluno);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            Uteis.logarDebug("Erro ao preparar alunos para sincronizar com a turma mgb: " + e.getMessage());
        }

        return list;
    }

    private String obterPublicIdAlunoPorCodigoCliente(Integer codigo) {
        try (ResultSet rs = SuperFacadeJDBC.criarConsulta("select publicidmgb from cliente where codigo = " + codigo, con)) {
            if (rs.next()) {
                return rs.getString("publicidmgb");
            }
        } catch (Exception e) {
            e.printStackTrace();
            Uteis.logarDebug("Erro ao preparar alunos para sincronizar com a turma mgb: " + e.getMessage());
        }
        return null;
    }

    public Integer[] obterDiaSemanaMgb(HorarioTurmaVO ht) throws Exception  {
        try {
            Integer[] diaSemana = new Integer[1];
            // 0 - Segunda
            // 1 - Terça
            // 2 - Quarta
            // 3 - Quinta
            // 4 - Sexta
            // 5 - Sábado
            // 6 - Domingo
            switch (ht.getDiaSemana()) {
                case "SG":
                    diaSemana[0] = 0;
                    break;
                case "TR":
                    diaSemana[0] = 1;
                    break;
                case "QA":
                    diaSemana[0] = 2;
                    break;
                case "QI":
                    diaSemana[0] = 3;
                    break;
                case "SX":
                    diaSemana[0] = 4;
                    break;
                case "SB":
                    diaSemana[0] = 5;
                    break;
                case "DM":
                    diaSemana[0] = 6;
                    break;
            }
            return diaSemana;
        } catch (Exception ex) {
            ex.printStackTrace();
            throw new ServiceException("Erro ao obter dia da semana do horario turma");
        }
    }

    public String inativarTurmaMgb(Integer empresa, Integer codigoHorarioTurma) throws Exception {
        try {
            if (!integradoMgb(empresa)) {
                return "A integração com o MGB não está ativa!";
            }

            String publicID = obterPublicIDHorarioTurma(codigoHorarioTurma);
            if (UteisValidacao.emptyString(publicID)) {
                return "O horário não possui um publicID para ser inativado";
            } else {
                putChangeStatusTurmaMgb(empresa, publicID, false);
            }

            return "Sucesso, o horário foi inativado no MGB";
        } catch (Exception ex) {
            ex.printStackTrace();
            String msgRetorno = "Erro: " + ex.getMessage();
            Uteis.logarDebug("Erro: " + ex.getMessage());
            return msgRetorno;
        }
    }

    public String deletarTodasTurmasNoMgb(Integer empresa, Boolean apenasValidar) throws Exception {
        try {
            if (!integradoMgb(empresa)) {
                return "A integração com o MGB não está ativa!";
            }

            if (apenasValidar) {
                StringBuilder sql = new StringBuilder();
                sql.append(
                        "select count(*) as qthorariossincronizados \n" +
                                "from horarioturma h \n" +
                                "inner join turma t on t.codigo = h.turma \n" +
                                "where h.publicidturmamgb is not null \n" +
                                "and t.empresa = " + empresa);
                try (ResultSet rs = SuperFacadeJDBC.criarConsulta(sql.toString(), con)) {
                    if (rs.next()) {
                        return "Existem " + rs.getInt("qthorariossincronizados") + " horários associados a um publicID de turma mgb!";
                    }
                    return "Existem 0 horários associados a um publicID de turma mgb!";
                }
            } else {
                StringBuilder sql = new StringBuilder();
                sql.append(
                        "select h.codigo, h.publicidturmamgb \n" +
                                "from horarioturma h \n" +
                                "inner join turma t on t.codigo = h.turma \n" +
                                "where h.publicidturmamgb is not null \n" +
                                "and t.empresa = " + empresa);
                Integer total = 0;
                Integer sucesso = 0;
                Integer erro = 0;
                JSONObject listErros = new JSONObject();
                try (ResultSet rs = SuperFacadeJDBC.criarConsulta(sql.toString(), con)) {
                    while (rs.next()) {
                        total++;
                        JSONObject retorno = deleteTurmaMgb(empresa, rs.getString("publicidturmamgb"));
                        if (retorno.has("success") && retorno.getBoolean("success")) {
                            sucesso++;
                        } else {
                            if (retorno.has("errors")) {
                                JSONObject error = (JSONObject) retorno.getJSONArray("errors").get(0);
                                if (error.has("message")) {
                                    String chaveErro = "CodHorarioTurma: " + rs.getInt("codigo");
                                    listErros.put(chaveErro, error.getString("message"));
                                }
                            }
                            erro++;
                        }
                        removerPublicIdTurmaMgb(rs.getInt("codigo"));
                    }
                }
                JSONObject retorno = new JSONObject();
                retorno.put("Retorno", "Resultado deletarTodasTurmasNoMgb, total: " + total + " - sucesso: " + sucesso + " - erro: " + erro);
                retorno.put("Retorno listErros", listErros);
                return retorno.toString();
            }
        } catch (Exception ex) {
            ex.printStackTrace();
            String msgRetorno = "Erro ao deletar todas turmas no mgb: " + ex.getMessage();
            Uteis.logarDebug("Erro ao deletar todas turmas no mgb: " + ex.getMessage());
            return msgRetorno;
        }
    }

    public String deletarTurmaMgb(Integer empresa, Integer codigoHorarioTurma) throws Exception {
        try {
            if (!integradoMgb(empresa)) {
                return "A integração com o MGB não está ativa!";
            }

            String publicID = obterPublicIDHorarioTurma(codigoHorarioTurma);
            if (UteisValidacao.emptyString(publicID)) {
                return "O horário não possui um publicID para ser excluído";
            } else {
                deleteTurmaMgb(empresa, publicID);
            }

            return "Sucesso, o horário foi excluído no MGB";
        } catch (Exception ex) {
            ex.printStackTrace();
            String msgRetorno = "Erro: " + ex.getMessage();
            Uteis.logarDebug("Erro: " + ex.getMessage());
            return msgRetorno;
        }
    }

    public String obterPublicIDHorarioTurma(Integer codigoHorarioTurma) {
        StringBuilder sql = new StringBuilder();
        sql.append("select publicidturmamgb from horarioturma where codigo = " + codigoHorarioTurma);
        try (ResultSet rs = SuperFacadeJDBC.criarConsulta(sql.toString(), con)) {
            if (rs.next()) {
                return rs.getString("publicidturmamgb");
            }
        } catch (Exception e) {
            e.printStackTrace();
            Uteis.logarDebug("Erro ao consultar publidID horarioturma: " + e.getMessage());
        }
        return "";
    }

    public JSONArray getTurmasMgb(Integer empresa, Boolean paramGetAll, Boolean paramStatus) throws Exception {
        String url = getCaminho("apiMgb") + "/api/v1/classgrades";
        if (paramGetAll != null) {
            url += "?getAll=" + paramGetAll;
        }
        if (paramStatus != null) {
            url += paramGetAll == null
                    ? "?status=" + paramStatus
                    : "&status=" + paramStatus;
        }

        Map<String, String> m = new HashMap<>();
        m.put("Authorization", tokenIntegracao(empresa));
        m.put("Content-Type", "application/json");
        RequestHttpService service = new RequestHttpService();
        RespostaHttpDTO respostaHttpDTO = service.executeRequest(url, m, null, null, MetodoHttpEnum.GET);
        JSONObject jsonObject;
        try {
            jsonObject = new JSONObject(respostaHttpDTO.getResponse());
            if (jsonObject.has("data") && jsonObject.getJSONObject("data").has("items")) {
                return jsonObject.getJSONObject("data").getJSONArray("items");
            }
        } catch (Exception e) {
            e.printStackTrace();
            Uteis.logarDebug("Erro ao consultar as turmas no MGB: " + e.getMessage());
        }
        return null;
    }

    public String postTurmaMgb(Integer empresa, JSONObject jsonTurma, Integer codHorarioTurma) throws Exception {
        String token = tokenIntegracao(empresa);
        if (token != null) {
            String url = getCaminho("apiMgb") + "/api/v1/classgrade";

            Map<String, String> headers = new HashMap<>();
            headers.put("Authorization", tokenIntegracao(empresa));
            headers.put("Content-Type", "application/json");
            RequestHttpService service = new RequestHttpService();
            RespostaHttpDTO respostaHttpDTO = service.executeRequest(url, headers, null, jsonTurma.toString(), MetodoHttpEnum.POST);
            JSONObject respostaJson = new JSONObject(respostaHttpDTO.getResponse());
            if (respostaJson.optBoolean("success") && respostaJson.has("data")) {
                String publicIdHorarioTurmaMgb = respostaJson.getJSONObject("data").getString("publicId");
                alterarPublicIdTurmaMgb(codHorarioTurma, publicIdHorarioTurmaMgb);
                return publicIdHorarioTurmaMgb;
            } else {
                Uteis.logarDebug("Falha ao se comunicar ou durante processo com API MGB para sincronizar a turma: " + respostaJson);
                throw new ServiceException("Falha ao se comunicar ou durante processo com API MGB para sincronizar a turma.");
            }
        }
        return null;
    }

    public void putTurmaMgb(Integer empresa, JSONObject jsonHorarioTurma, String publicIdTurmaMgb) {
        try {
            String token = tokenIntegracao(empresa);
            if (token != null) {
                String url = getCaminho("apiMgb") + "/api/v1/classgrade/" + publicIdTurmaMgb;
                Map<String, String> m = new HashMap<>();
                m.put("Authorization", tokenIntegracao(empresa));
                m.put("Content-Type", "application/json");
                RequestHttpService service = new RequestHttpService();
                RespostaHttpDTO respostaHttpDTO = service.executeRequest(url, m, null, jsonHorarioTurma.toString(), MetodoHttpEnum.PUT);
                JSONObject respostaJson = new JSONObject(respostaHttpDTO.getResponse());
                String publicId = new JSONObject(respostaHttpDTO.getResponse()).getJSONObject("data").getString("publicId");
            }
        } catch (Exception ex) {
            Uteis.logarDebug("Não foi possível atualizar o horário turma com o publicIdTurmaMgb: " + publicIdTurmaMgb);
        }
    }

    public void putChangeStatusTurmaMgb(Integer empresa, String publicIdTurmaMgb, Boolean status) {
        try {
            JSONObject body = new JSONObject();
            body.put("publicId", publicIdTurmaMgb);
            body.put("status", status);
            String token = tokenIntegracao(empresa);
            if (token != null) {
                String url = getCaminho("apiMgb") + "/api/v1/classgrade/" + publicIdTurmaMgb + "/change-status";
                Map<String, String> m = new HashMap<>();
                m.put("Authorization", token);
                m.put("Content-Type", "application/json");
                RequestHttpService service = new RequestHttpService();
                RespostaHttpDTO respostaHttpDTO = service.executeRequest(url, m, null, body.toString(), MetodoHttpEnum.PUT);
                JSONObject respostaJson = new JSONObject(respostaHttpDTO.getResponse());
            }
        } catch (Exception ex) {
            ex.printStackTrace();
            Uteis.logarDebug("Não foi possível atualizar o status do horário turma com o publicIdTurmaMgb: " + publicIdTurmaMgb);
        }
    }

    public JSONObject deleteTurmaMgb(Integer empresa, String publicIdTurmaMgb) {
        try {
            String token = tokenIntegracao(empresa);
            if (token != null) {
                String url = getCaminho("apiMgb") + "/api/v1/classgrade/" + publicIdTurmaMgb;
                Map<String, String> m = new HashMap<>();
                m.put("Authorization", token);
                m.put("Content-Type", "application/json");
                RequestHttpService service = new RequestHttpService();
                RespostaHttpDTO respostaHttpDTO = service.executeRequest(url, m, null, null, MetodoHttpEnum.DELETE);
                JSONObject respostaJson = new JSONObject(respostaHttpDTO.getResponse());
                return respostaJson;
            }
        } catch (Exception ex) {
            ex.printStackTrace();
            Uteis.logarDebug("Não foi possível deletar a turma com o publicIdTurmaMgb: " + publicIdTurmaMgb);
        }
        return null;
    }

    public void putUpdateStudentTurmaMgb(Integer empresa, String publicIdTurmaMgb, Integer action, List<String> listaAlunos) {
        // ACTION
        // 1 - Add
        // 2 - Remove
        if (listaAlunos == null || listaAlunos.size() < 1) {
            return;
        }
        try {
            JSONObject body = new JSONObject();
            body.put("publicId", publicIdTurmaMgb);
            body.put("action", action);
            body.put("classStudentsPublicIds", listaAlunos);
            String token = tokenIntegracao(empresa);
            if (token != null) {
                String url = getCaminho("apiMgb") + "/api/v1/classgrade/" + publicIdTurmaMgb + "/update-student";
                Map<String, String> m = new HashMap<>();
                m.put("Authorization", token);
                m.put("Content-Type", "application/json");
                RequestHttpService service = new RequestHttpService();
                RespostaHttpDTO respostaHttpDTO = service.executeRequest(url, m, null, body.toString(), MetodoHttpEnum.PUT);
                JSONObject respostaJson = new JSONObject(respostaHttpDTO.getResponse());
            }
        } catch (Exception ex) {
            ex.printStackTrace();
            Uteis.logarDebug("Não foi possível atualizar os alunos da turma publicIdTurmaMgb: " + publicIdTurmaMgb + ex.getMessage());
        }
    }

    public boolean verificarSeExisteTurmaMgbPorPublicId(Integer empresa, String publicIdTurmaMgb) throws Exception {
        if (UteisValidacao.emptyString(publicIdTurmaMgb)) {
            return false;
        }
        try {
            String url = getCaminho("apiMgb") + "/api/v1/classgrade/" + publicIdTurmaMgb;
            Map<String, String> m = new HashMap<>();
            m.put("Authorization", tokenIntegracao(empresa));
            m.put("Content-Type", "application/json");
            RequestHttpService service = new RequestHttpService();
            RespostaHttpDTO respostaHttpDTO = service.executeRequest(url, m, null, null, MetodoHttpEnum.GET);
            JSONObject jsonObject;
            try {
                jsonObject = new JSONObject(respostaHttpDTO.getResponse());
                if (jsonObject.has("data")) {
                    return true;
                }
            } catch (Exception ex) {
                throw new ServiceException("Erro ao verificar se existe turma no MGB com o publicId: " + publicIdTurmaMgb);
            }
        } catch (Exception ex) {
            Uteis.logarDebug("Ocorreu um erro ao tentar verificar se já existe uma turma mgb com o publicId: " + publicIdTurmaMgb + " Erro: " + ex.getMessage());
        }
        return false;
    }

    public String syncAlunoMgb(Integer empresa, Integer codigoCliente, Integer codigoPessoa) throws Exception {
        if (codigoCliente == null && codigoPessoa == null) {
            return "Erro: código de cliente ou código de pessoa devem ser informados!";
        }
        try {
            StringBuilder sql = new StringBuilder();
            sql.append("select codigomatricula, empresa from cliente ");
            if (codigoCliente != null) {
                sql.append("where codigo = ").append(codigoCliente);
            } else {
                sql.append("where pessoa = ").append(codigoPessoa);
            }
            try (ResultSet rs = SuperFacadeJDBC.criarConsulta(sql.toString(), con)) {
                if (rs.next()) {
                    if (empresa == null) {
                        empresa = rs.getInt("empresa");
                    }
                    if (!integradoMgb(empresa)) {
                        throw new ServiceException("A empresa de código " + empresa + " não está integrada");
                    }
                    postStudentMgb(empresa, rs.getInt("codigomatricula"), true, false);
                }
            }

            return "{\"message\": \"Sucesso\"}\n";
        } catch (Exception ex) {
            ex.printStackTrace();
            String msgRetorno = "Erro: " + ex.getMessage();
            Uteis.logarDebug("Erro: " + ex.getMessage());
            return msgRetorno;
        }
    }

    public JSONObject consultarDadosMgbAluno(Integer empresa, Integer codigoCliente) throws Exception {
        JSONObject retorno = new JSONObject();
        if (!integradoMgb(empresa)) {
            retorno.put("integrado", false);
            retorno.put("publicIdAluno", "");
            retorno.put("publicIdNivelAluno", "");
            return retorno;
        }
        try {
            StringBuilder sql = new StringBuilder();
            sql.append("select codigomatricula, empresa, publicidmgb from cliente where codigo = ").append(codigoCliente);
            try (ResultSet rs = SuperFacadeJDBC.criarConsulta(sql.toString(), con)) {
                if (rs.next()) {
                    String nivelMgb = consultarNivelMgb(rs.getInt("codigomatricula"));
                    retorno.put("integrado", true);
                    retorno.put("publicIdAluno", UteisValidacao.emptyString(rs.getString("publicidmgb")) ? "" : rs.getString("publicidmgb"));
                    retorno.put("publicIdNivelAluno", (UteisValidacao.emptyString(nivelMgb) || "0".equals(nivelMgb)) ? "" : nivelMgb);
                }
            }

        } catch (Exception ex) {
            throw new ServiceException("Erro ao consultar os dados do aluno: " + ex.getMessage());
        }
        return retorno;
    }

    public void sincronizarProfessorMgb(Integer empresa, Integer codColaborador) throws Exception {
        try {
            if (colaboradorIsProfessor(codColaborador) && integradoMgb(empresa)) {
                salvarProfessorMgb(empresa, codColaborador);
            }
        } catch (Exception e) {
            e.printStackTrace();
            Uteis.logarDebug("Ocorreu um erro ao tentar sincronizar o professor com o codigo de colaborador: " + codColaborador + " Erro: " + e.getMessage());
        }
    }

    public String deletarPublicIdProfessor(Integer empresa, Integer codColaborador) throws Exception {
        try {
            StringBuilder sql = new StringBuilder();
            sql.append("update colaborador set publicIdProfessorMgb = null where codigo = ").append(codColaborador);
            SuperFacadeJDBC.executarConsultaUpdate(sql.toString(), con);
            return "{\"message\": \"Sucesso\"}\n";
        } catch (Exception ex) {
            ex.printStackTrace();
            Uteis.logarDebug("Erro ao deletar no banco o publicIdProfessorMgb do professor " + ex.getMessage());
            throw new ServiceException("Erro ao atualizar no banco o publicIdProfessorMgb do professor.");
        }
    }

    private Boolean colaboradorIsProfessor(Integer codColaborador) throws Exception {
        try {
            StringBuilder sql = new StringBuilder();
            sql.append(
                    "select exists ( \n" +
                    "   select c.codigo \n" +
                    "   from colaborador c \n" +
                    "   inner join tipocolaborador t on t.colaborador = c.codigo \n" +
                    "   where t.descricao in ('TW', 'PR') \n" +
                    "   and c.codigo = " + codColaborador + " \n" +
                    ") as isProfessor"
            );
            try (ResultSet rs = SuperFacadeJDBC.criarConsulta(sql.toString(), con)) {
                if (rs.next()) {
                    return rs.getBoolean("isProfessor");
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            Uteis.logarDebug("Ocorreu um erro ao verificar se o colaborador é professor cod: " + codColaborador + " Erro: " + e.getMessage());
        }
        return false;
    }

    public String salvarProfessorMgb(Integer empresa, Integer codColaborador) throws Exception {
        try {
            String publicIdProfessorMgb = null;
            StringBuilder sql = new StringBuilder();
            sql.append("select publicIdProfessorMgb from colaborador where codigo = ").append(codColaborador);
            ResultSet rs = SuperFacadeJDBC.criarConsulta(sql.toString(), con);
            if (rs.next()) {
                publicIdProfessorMgb = rs.getString("publicIdProfessorMgb");
                // caso publicid do professor exista, verificar se ele ainda existe do lado do mgb
                if (!UteisValidacao.emptyString(publicIdProfessorMgb)) {
                    JSONObject professor = getProfessorMgbPorPublicId(empresa, publicIdProfessorMgb);
                    // caso não exista mais um professor no mgb com o publicId informado, ele deverá ser cadastrado novamente para gerar um novo publicId
                    if (professor == null) {
                        publicIdProfessorMgb = null;
                    } else {
                        JSONObject jsonProfessor = montarDadosProfessor(codColaborador, professor);
                        if (jsonProfessor.getBoolean("status")) {
                            putProfessorMgb(empresa, jsonProfessor, publicIdProfessorMgb);
                        } else {
                            putChangeStatusProfessorMgb(empresa, publicIdProfessorMgb, false);
                        }
                    }
                }
                if (UteisValidacao.emptyString(publicIdProfessorMgb)) {
                    JSONArray professoressMgb = getProfessoresMgb(empresa);
                    JSONObject jsonProfessor = montarDadosProfessor(codColaborador, null);
                    String publicIdProfessorExistente = obterPublicIdProfessorMgbPorEmail(jsonProfessor, professoressMgb);
                    if (UteisValidacao.emptyString(publicIdProfessorExistente)) {
                        publicIdProfessorMgb = postProfessorMgb(empresa, codColaborador, jsonProfessor);
                    } else {
                        alterarPublicIdProfessorMgb(codColaborador, publicIdProfessorExistente);
                        publicIdProfessorMgb = publicIdProfessorExistente;
                    }
                }
            }
            return publicIdProfessorMgb;
        } catch (Exception ex) {
            throw new ServiceException("Erro ao obter publicId do professor MGB: " + ex.getMessage());
        }
    }

    public String obterPublicIdProfessorMgbPorEmail(JSONObject jsonProfessor, JSONArray professoressMgb) throws Exception {
        if (!jsonProfessor.has("email") || (jsonProfessor.has("email") && UteisValidacao.emptyString(jsonProfessor.getString("email")))) {
            Uteis.logarDebug("O professor " + jsonProfessor.optString("firstName") + " " + jsonProfessor.optString("lastName") + " precisa ter um email válido para ser sincronizado no MGB.");
            throw new ServiceException("ERRO_PROFESSOR_SEM_EMAIL NOME: " + jsonProfessor.optString("firstName") + " " + jsonProfessor.optString("lastName"));
        }
        try {
            for (int i = 0; i < professoressMgb.length(); i++) {
                JSONObject itemLista = professoressMgb.getJSONObject(i);
                if (itemLista.has("email") && itemLista.getString("email").equals(jsonProfessor.getString("email"))) {
                    return itemLista.getString("publicId");
                }
            }
            return null;
        } catch (Exception ex) {
            throw new ServiceException("Erro ao verificar se email professor existe no MGB " + ex.getMessage());
        }
    }

    public JSONObject getProfessorMgbPorPublicId(Integer empresa, String publicIdProfessorMgb) throws Exception {
        String url = getCaminho("apiMgb") + "/api/v1/team/" + publicIdProfessorMgb;
        Map<String, String> m = new HashMap<>();
        m.put("Authorization", tokenIntegracao(empresa));
        m.put("Content-Type", "application/json");
        RequestHttpService service = new RequestHttpService();
        RespostaHttpDTO respostaHttpDTO = service.executeRequest(url, m, null, null, MetodoHttpEnum.GET);
        JSONObject jsonObject;
        try {
            jsonObject = new JSONObject(respostaHttpDTO.getResponse());
            if (jsonObject.has("data")) {
                return jsonObject.getJSONObject("data");
            } else {
                return null;
            }
        } catch (Exception ex) {
            throw new Exception("Erro ao consultar o professor no MGB");
        }
    }

    public JSONArray getProfessoresMgb(Integer empresa) throws Exception {
        String url = getCaminho("apiMgb") + "/api/v1/teams/teachers?PageSize=100";
        Map<String, String> m = new HashMap<>();
        m.put("Authorization", tokenIntegracao(empresa));
        m.put("Content-Type", "application/json");
        RequestHttpService service = new RequestHttpService();
        RespostaHttpDTO respostaHttpDTO = service.executeRequest(url, m, null, null, MetodoHttpEnum.GET);
        JSONObject jsonObject;
        try {
            jsonObject = new JSONObject(respostaHttpDTO.getResponse());
            if (jsonObject.has("data") && jsonObject.getJSONObject("data").has("items")) {
                return jsonObject.getJSONObject("data").getJSONArray("items");
            } else {
                return null;
            }
        } catch (Exception ex) {
            throw new Exception("Erro ao consultar os professores no MGB");
        }
    }

    public String postProfessorMgb(Integer empresa, Integer codColaborador, JSONObject jsonProfessor) throws Exception {
        String token = tokenIntegracao(empresa);
        if (token != null) {
            String url = getCaminho("apiMgb") + "/api/v1/team";

            Map<String, String> headers = new HashMap<>();
            headers.put("Authorization", tokenIntegracao(empresa));
            headers.put("Content-Type", "application/json");
            RequestHttpService service = new RequestHttpService();
            RespostaHttpDTO respostaHttpDTO = service.executeRequest(url, headers, null, jsonProfessor.toString(), MetodoHttpEnum.POST);
            JSONObject respostaJson = new JSONObject(respostaHttpDTO.getResponse());
            if (respostaJson.optBoolean("success") && respostaJson.has("data")) {
                String publicIdProfessorMgb = respostaJson.getJSONObject("data").getString("publicId");
                alterarPublicIdProfessorMgb(codColaborador, publicIdProfessorMgb);
                return publicIdProfessorMgb;
            } else {
                Uteis.logarDebug("Falha na Sincronização MGB professor " + jsonProfessor.optString("firstName") + " " + jsonProfessor.optString("lastName") + " response MGB: " + respostaJson);
                throw new ServiceException("ERRO_SINCRONIZAR_PROFESSOR_POST_MGB NOME: " + jsonProfessor.optString("firstName") + " " + jsonProfessor.optString("lastName"));
            }
        }
        return null;
    }

    public void putProfessorMgb(Integer empresa, JSONObject jsonProfessor, String publicIdProfessorMgb) {
        try {
            String token = tokenIntegracao(empresa);
            if (token != null) {
                String url = getCaminho("apiMgb") + "/api/v1/team/" + publicIdProfessorMgb;
                Map<String, String> m = new HashMap<>();
                m.put("Authorization", token);
                m.put("Content-Type", "application/json");
                RequestHttpService service = new RequestHttpService();
                RespostaHttpDTO respostaHttpDTO = service.executeRequest(url, m, null, jsonProfessor.toString(), MetodoHttpEnum.PUT);
                JSONObject respostaJson = new JSONObject(respostaHttpDTO.getResponse());
                String publicId = new JSONObject(respostaHttpDTO.getResponse()).getJSONObject("data").getString("publicId");
            }
        } catch (Exception e) {
            e.printStackTrace();
            Uteis.logarDebug("Não foi possível atualizar o professor com o publicIdProfessorMgb: " + publicIdProfessorMgb + " - erro: " + e.getMessage());
        }
    }

    public void putChangeStatusProfessorMgb(Integer empresa, String publicIdProfessorMgb, Boolean status) {
        try {
            JSONObject body = new JSONObject();
            body.put("status", status);
            String token = tokenIntegracao(empresa);
            if (token != null) {
                String url = getCaminho("apiMgb") + "/api/v1/team/" + publicIdProfessorMgb + "/change-status";
                Map<String, String> m = new HashMap<>();
                m.put("Authorization", token);
                m.put("Content-Type", "application/json");
                RequestHttpService service = new RequestHttpService();
                RespostaHttpDTO respostaHttpDTO = service.executeRequest(url, m, null, body.toString(), MetodoHttpEnum.PUT);
                JSONObject respostaJson = new JSONObject(respostaHttpDTO.getResponse());
            }
        } catch (Exception e) {
            e.printStackTrace();
            Uteis.logarDebug("Não foi possível atualizar o status do professor com o publicIdProfessorMgb: " + publicIdProfessorMgb + " - erro: " + e.getMessage());
        }
    }

    public void presencaAlunoTurmaMgb(Integer empresa, Integer matricula, Integer codigoHorarioTurma, Date dia, Boolean compareceu) throws Exception {
        try {
            String publicIdAluno = verificarPublicidmgb(matricula);
            HorarioTurmaVO ht = getHorarioDao().consultarPorChavePrimaria(codigoHorarioTurma, Uteis.NIVELMONTARDADOS_TODOS);
            if (UteisValidacao.emptyString(publicIdAluno) || (ht != null && UteisValidacao.emptyString(ht.getPublicIdTurmaMgb()))) {
                return;
            }
            String diaFormatado = Uteis.getDataAplicandoFormatacao(Uteis.getDataComHoraZerada(dia),"yyyy-MM-dd HH:mm:ss");
            String emailProfessor = consultarEmailColaborador(ht.getProfessor().getCodigo());
            postPresencaAlunoTurmaMgb(empresa, compareceu, publicIdAluno, ht.getPublicIdTurmaMgb(), emailProfessor, diaFormatado);
        } catch (Exception e) {
            e.printStackTrace();
            Uteis.logarDebug("Erro ao preparar dados para presença do aluno: " + e.getMessage());
        }
    }

    private void postPresencaAlunoTurmaMgb(Integer empresa, Boolean compareceu, String publicIdAluno, String publicIdTurma, String emailProfessor, String dia) throws Exception {
        try {
            String token = tokenIntegracao(empresa);
            if (token != null && !UteisValidacao.emptyString(publicIdAluno)) {
                JSONObject student = new JSONObject();
                student.put("attended", compareceu);
                student.put("studentPublicId", publicIdAluno);

                JSONArray students = new JSONArray();
                students.put(student);

                JSONObject body = new JSONObject();
                body.put("classDate", dia); // formato data "2025-01-01 00:00:00"
                body.put("classGradePublicId", publicIdTurma);
                body.put("attendanceTakerEmail", emailProfessor);
                body.put("students", students);

                String url = getCaminho("apiMgb") + "/api/v1/class-attendance";

                Map<String, String> headers = new HashMap<>();
                headers.put("Authorization", token);
                headers.put("Content-Type", "application/json");
                RequestHttpService service = new RequestHttpService();
                RespostaHttpDTO respostaHttpDTO = service.executeRequest(url, headers, null, body.toString(), MetodoHttpEnum.POST);
                JSONObject respostaJson = new JSONObject(respostaHttpDTO.getResponse());
            }
        } catch (Exception e) {
            e.printStackTrace();
            Uteis.logarDebug("Erro ao chamar api do mgb de presença: " + e.getMessage());
        }
    }

    @Override
    public String removerUtilizacaoPiscina(String publicIdPiscina) throws Exception {
        // se não tiver ambiente vinculado a piscina retornar ok
        // se tiver ambiente vinculado a piscina, mas ambiente não é utilizado na turma, remover publicId da piscina e retornar ok
        // se tiver ambiente vinculado a piscina e o ambiente é utilizado em uma turma, retornar que não pode remover o publicid da piscina do ambiente
        JSONObject json = new JSONObject();
        try {
            Boolean piscinaRemovidaDeTodosAmbientes = true;
            StringBuilder sqlAmbiente = new StringBuilder();
            sqlAmbiente.append("select a.codigo \n");
            sqlAmbiente.append("from ambiente a \n");
            sqlAmbiente.append("where a.codigopiscinamgb = '").append(publicIdPiscina).append("'");

            String codigosAmbiente = "";
            try (ResultSet rsAmbiente = SuperFacadeJDBC.criarConsulta(sqlAmbiente.toString(), con)) {
                while (rsAmbiente.next()) {
                    codigosAmbiente += "," + rsAmbiente.getInt("codigo");
                }
            }

            if (!codigosAmbiente.isEmpty()) {
                codigosAmbiente = codigosAmbiente.substring(1);

                StringBuilder sqlAmbienteHorario = new StringBuilder();
                sqlAmbienteHorario.append("select exists( \n");
                sqlAmbienteHorario.append("    select a.codigo \n");
                sqlAmbienteHorario.append("    from ambiente a \n");
                sqlAmbienteHorario.append("    inner join horarioturma h on h.ambiente = a.codigo \n");
                sqlAmbienteHorario.append("    where a.codigo in (").append(codigosAmbiente).append(") \n");
                sqlAmbienteHorario.append(")");

                boolean ambienteSendoUsadoTurmaMGB = false;
                try (ResultSet rsAmbienteHorario = SuperFacadeJDBC.criarConsulta(sqlAmbienteHorario.toString(), con)) {
                    if (rsAmbienteHorario.next()) {
                        ambienteSendoUsadoTurmaMGB = rsAmbienteHorario.getBoolean("exists");
                    }
                }

                if (!ambienteSendoUsadoTurmaMGB) {
                    removerPublicIdAmbiente(codigosAmbiente);
                    piscinaRemovidaDeTodosAmbientes = true;
                } else {
                    piscinaRemovidaDeTodosAmbientes = false;
                }
            }

            json.put("success", true);
            json.put("utilizacaoPiscinaRemovida", piscinaRemovidaDeTodosAmbientes);
            return json.toString();
        } catch (Exception e) {
            e.printStackTrace();
            Uteis.logarDebug("Erro ao consultar e remover utilização de piscina: " + e.getMessage());
            json.put("success", false);
            json.put("errors", e.getMessage());
            return json.toString();
        }
    }

    public void removerPublicIdAmbiente(String codigosAmbiente) throws Exception {
        try {
            StringBuilder sql = new StringBuilder();
            sql.append("update ambiente set codigopiscinamgb = null where codigo in (").append(codigosAmbiente).append(")");
            SuperFacadeJDBC.executarConsultaUpdate(sql.toString(), con);
        } catch (Exception ex) {
            ex.printStackTrace();
            Uteis.logarDebug("Erro ao remover no banco o publicId do ambiente " + ex.getMessage());
        }
    }

}
