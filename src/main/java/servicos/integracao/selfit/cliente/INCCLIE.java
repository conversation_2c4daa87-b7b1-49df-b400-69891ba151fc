
package servicos.integracao.selfit.cliente;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;


/**
 * <p>Java class for INCCLIE complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType name="INCCLIE">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="NINCCLIE" type="{http://54.207.46.24:91/}ARRAYOFWSDADCLI"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "INCC<PERSON>IE", propOrder = {
    "nincclie"
})
public class INCCLIE {

    @XmlElement(name = "NINCCLIE", required = true)
    protected ARRAYOFWSDADCLI nincclie;

    /**
     * Gets the value of the nincclie property.
     * 
     * @return
     *     possible object is
     *     {@link ARRAYOFWSDADCLI }
     *     
     */
    public ARRAYOFWSDADCLI getNINCCLIE() {
        return nincclie;
    }

    /**
     * Sets the value of the nincclie property.
     * 
     * @param value
     *     allowed object is
     *     {@link ARRAYOFWSDADCLI }
     *     
     */
    public void setNINCCLIE(ARRAYOFWSDADCLI value) {
        this.nincclie = value;
    }

}
