package servicos.integracao.impl.parceirofidelidade.to;

import negocio.comuns.arquitetura.SuperTO;
import org.codehaus.jackson.annotate.JsonProperty;

/**
 *
 * <AUTHOR>
 */
public class PurchasesResponseTO extends SuperTO {

    @JsonProperty("purchaseValue")
    private Integer purchaseValue;
    @JsonProperty("bonusPoints")
    private Integer bonusPoints;
    @JsonProperty("memberIdentification")
    private String memberIdentification;
    @JsonProperty("totalPoints")
    private Integer totalPoints;
    @JsonProperty("description")
    private String description;
    @JsonProperty("locator")
    private String locator;
    @JsonProperty("transactionId")
    private String transactionId;
    @JsonProperty("points")
    private Integer points;
    @JsonProperty("code")
    private String code;
    @JsonProperty("message")
    private String message;



    public Integer getPurchaseValue() {
        return purchaseValue;
    }

    public void setPurchaseValue(Integer purchaseValue) {
        this.purchaseValue = purchaseValue;
    }

    public Integer getBonusPoints() {
        return bonusPoints;
    }

    public void setBonusPoints(Integer bonusPoints) {
        this.bonusPoints = bonusPoints;
    }

    public String getMemberIdentification() {
        return memberIdentification;
    }

    public void setMemberIdentification(String memberIdentification) {
        this.memberIdentification = memberIdentification;
    }

    public Integer getTotalPoints() {
        return totalPoints;
    }

    public void setTotalPoints(Integer totalPoints) {
        this.totalPoints = totalPoints;
    }

    public String getDescription() {
        if (description == null) {
            description = "";
        }
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getLocator() {
        return locator;
    }

    public void setLocator(String locator) {
        this.locator = locator;
    }

    public String getTransactionId() {
        return transactionId;
    }

    public void setTransactionId(String transactionId) {
        this.transactionId = transactionId;
    }

    public Integer getPoints() {
        return points;
    }

    public void setPoints(Integer points) {
        this.points = points;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }
}
