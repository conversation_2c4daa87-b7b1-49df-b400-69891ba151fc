/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package servicos.integracao;

import br.com.pactosolucoes.atualizadb.processo.sincronizador.Sincronizador;
import br.com.pactosolucoes.comuns.util.JSFUtilities;
import br.com.pactosolucoes.enumeradores.TipoFilaEnum;
import negocio.comuns.basico.PactoPayComunicacaoVO;
import negocio.comuns.crm.MalaDiretaVO;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.modulos.integracao.usuariomovel.UsuarioMovelVO;
import org.json.JSONArray;
import org.json.JSONObject;
import servicos.integracao.push.PushController;
import servicos.integracao.treino.ProfessorSintetico;
import servicos.integracao.treino.TreinoWS;
import servicos.integracao.treino.TreinoWS_Service;
import servicos.integracao.treino.UsuarioZW;
import servicos.propriedades.PropsService;

import javax.xml.namespace.QName;
import javax.xml.ws.BindingProvider;
import java.net.MalformedURLException;
import java.net.URL;
import java.util.Date;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 *
 * <AUTHOR>
 */
public final class TreinoWSConsumer {

    private static final int TIMEOUT = 15000;
    public static final String CONNECT_TIMEOUT = "com.sun.xml.ws.connect.timeout";
    public static final String REQUEST_TIMEOUT = "com.sun.xml.ws.request.timeout";

    private static TreinoWS wrapper = null;

    private static TreinoWS getInstance(final String key) {
        if (wrapper == null) {
            try {
                URL u = new URL(PropsService.getPropertyValue(key, PropsService.urlTreino) + "/TreinoWS?wsdl");
                QName qName = new QName("http://webservice.pacto.com.br/", "TreinoWS");
                TreinoWS servico = new TreinoWS_Service(u, qName).getTreinoWSPort();
                Map<String, Object> reqContext = ((BindingProvider) servico).getRequestContext();
                reqContext.put(CONNECT_TIMEOUT, TIMEOUT);
                reqContext.put(REQUEST_TIMEOUT, TIMEOUT);
                wrapper = servico;
            } catch (Exception ex) {
                Logger.getLogger(TreinoWSConsumer.class.getName()).log(Level.SEVERE, null, ex);
            }
        }
        return wrapper;
    }


    private static TreinoWS getInstance(final String key, Integer timeout ) {
        try {
            URL u = new URL(PropsService.getPropertyValue(key, PropsService.urlTreino) + "/TreinoWS?wsdl");
            QName qName = new QName("http://webservice.pacto.com.br/", "TreinoWS");
            TreinoWS servico = new TreinoWS_Service(u, qName).getTreinoWSPort();
            Map<String, Object> reqContext = ((BindingProvider) servico).getRequestContext();
            reqContext.put(CONNECT_TIMEOUT, timeout);
            reqContext.put(REQUEST_TIMEOUT, timeout);
            return servico;
        } catch (MalformedURLException ex) {
            Logger.getLogger(TreinoWSConsumer.class.getName()).log(Level.SEVERE, null, ex);
        }
        return null;
    }
    
    public static String atualizarStatusAluno(final String key, final Integer codigo, final Date acesso, final Integer empresaAcesso){
        Uteis.logar(null, String.format("TreinoWSConsumer -> Atualizando status do cliente %s da empresa %s", new Object[]{codigo, key}));
        try {
            if(!empresaTemIntegracaoTW(key)) { return ""; }
            return getInstance(key).atualizarStatusAluno(key, codigo, Uteis.getDataAplicandoFormatacao(acesso, "dd/MM/yyyy HH:mm:ss"), empresaAcesso);
        } catch (Exception e) {
            Uteis.logar(e, TreinoWSConsumer.class);
        }
        return "";
    }

    public static String excluirCliente(final String key, final Integer codigoCliente){
        Uteis.logar(null, String.format("TreinoWSConsumer -> Excluindo cliente %s da empresa %s", new Object[]{codigoCliente, key}));
        try {
            if(!empresaTemIntegracaoTW(key)) { return ""; }
            return getInstance(key).excluirAluno(key, codigoCliente);
        } catch (Exception e) {
            Uteis.logar(e, TreinoWSConsumer.class);
        }
        return "";
    }


    public static String sincronizarUsuario(final String key, UsuarioZW usuario) {
        Uteis.logar(null, String.format("TreinoWSConsumer -> Atualizando usuário %s da empresa %s", new Object[]{usuario.getNome(), key}));
        try {
            if(!empresaTemIntegracaoTW(key)) { return ""; }
            return getInstance(key).sincronizarUsuario(key, usuario);
        } catch (Exception e) {
            Sincronizador.inserirNaFila(TipoFilaEnum.ALUNO_TREINO, usuario.getCliente().getCodigoCliente(), key);
            Uteis.logar(e, TreinoWSConsumer.class);
        }
        return "";
    }
    public static void sincronizarUsuario(final String key, UsuarioMovelVO usuario) throws Exception {
        Uteis.logar(null, String.format("TreinoWSConsumer -> Atualizando usuário %s da empresa %s", new Object[]{usuario.getNome(), key}));
        try {
            if(!empresaTemIntegracaoTW(key)) { return ; }
            String retorno = getInstance(key).sincronizarUsuario(key, usuario.toUsuarioTreino());
            if(retorno.contains("ERRO")){
                throw new Exception(retorno);
            }
        } catch (Exception e) {
            if (usuario.getCliente() != null && !UteisValidacao.emptyNumber(usuario.getCliente().getCodigo())) {
                Sincronizador.inserirNaFila(TipoFilaEnum.ALUNO_TREINO, usuario.getCliente().getCodigo(), key);
            }
            String modulos = (String) JSFUtilities.getFromSession("modulosHabilitados");
            if(!UteisValidacao.emptyString(modulos) && modulos.contains("TR") && usuario.getPropagarExcessao()){
                throw e;
            }
            Uteis.logar(e, TreinoWSConsumer.class);
        }
    }
    public static String inserirCreditos(final String key, Integer colaborador, Integer creditos, Integer recibozw, Date dataExpiracao){
        Uteis.logar(null, String.format("TreinoWSConsumer -> Inserindo %s créditos para o colaborador %s da chave %s", 
                new Object[]{creditos, colaborador, key}));
        try {
            if(!empresaTemIntegracaoTW(key)) { return ""; }
            return getInstance(key, 60000).inserirCreditos(key, colaborador, creditos, recibozw, dataExpiracao == null ? "" : Uteis.getData(dataExpiracao));
        } catch (Exception e) {
            Uteis.logar(e, TreinoWSConsumer.class);
            Uteis.logar(null, "O ZW NÃO CONSEGUIU FINALIZAR O PROCESSO DE INSERIR CRÉDITOS | ESTORNANDO CREDITOS DO RECIBO "+recibozw);
            TreinoWSConsumer.estornarCreditos(key, recibozw);
        }
        return "";
    }
    
    public static String estornarCreditos(final String key,Integer recibozw){
        Uteis.logar(null, String.format("TreinoWSConsumer -> Estornando créditos para o recibo %s da chave %s", 
                new Object[]{recibozw, key}));
        try {
            if(!empresaTemIntegracaoTW(key)) { return ""; }
            return getInstance(key,60000).estornarCreditos(key, recibozw);
        } catch (Exception e) {
            Uteis.logar(e, TreinoWSConsumer.class);
            return e.getMessage();
        }
    }
    
    public static String estornarCreditosFechamento(final String key,Integer vendazw){
        Uteis.logar(null, String.format("TreinoWSConsumer -> Estornando créditos para a venda %s da chave %s", 
                new Object[]{vendazw, key}));
        try {
            if(!empresaTemIntegracaoTW(key)) { return ""; }
            return getInstance(key).estornarFechamentoCreditos(key, vendazw);
        } catch (Exception e) {
            Uteis.logar(e, TreinoWSConsumer.class);
            return e.getMessage();
        }
    }
    
    public static String adicionarPersonal(final String key, ProfessorSintetico professorSintetico, Integer empresa){
        Uteis.logar(null, String.format("TreinoWSConsumer -> Inserindo personal de código %s da chave %s", 
                new Object[]{professorSintetico.getCodigoColaborador(), key}));
        try {
            if(!empresaTemIntegracaoTW(key)) { return ""; }
            return getInstance(key).adicionarPersonal(key, professorSintetico, empresa);
        } catch (Exception e) {
            Uteis.logar(e, TreinoWSConsumer.class);
        }
        return "";
    }
    
    public static String sincronizarProfessor(final String key, ProfessorSintetico professorSintetico, Integer empresa){
        Uteis.logar(null, String.format("TreinoWSConsumer -> Inserindo profesor de código %s da chave %s", 
                new Object[]{professorSintetico.getCodigoColaborador(), key}));
        try {
            if(!empresaTemIntegracaoTW(key)) { return ""; }
            return getInstance(key).sincronizarProfessor(key, professorSintetico, empresa);
        } catch (Exception e) {
            Uteis.logar(e, TreinoWSConsumer.class);
        }
        return "";
    }

    public static String alterarProfessor(final String key, ProfessorSintetico professorSintetico, Integer empresa){
        Uteis.logar(null, String.format("TreinoWSConsumer -> Inserindo profesor de código %s da chave %s",
                new Object[]{professorSintetico.getCodigoColaborador(), key}));
        try {
            return getInstance(key).alterarProfessor(key, professorSintetico, empresa);
        } catch (Exception e) {
            Uteis.logar(e, TreinoWSConsumer.class);
        }
        return "";
    }
    
    public static String gerarNotificacoes(final String key, MalaDiretaVO malaDireta, String codigosClientes, String mensagem) {
        Uteis.logar(null, String.format("TreinoWSConsumer -> Gerando notificacoes maladireta %s da chave %s", 
                new Object[]{malaDireta.getCodigo(), key}));
        try {
            if(!empresaTemIntegracaoTW(key)) { return ""; }
            mensagem = Uteis.retiraTags(mensagem, false);
            new PushController(malaDireta,key, mensagem, codigosClientes, null).sendMessage();
            return getInstance(key).enviarNotificacoes(key, malaDireta.getTitulo(), mensagem, malaDireta.getOpcoes(), codigosClientes);
        } catch (Exception e) {
            Uteis.logar(e, TreinoWSConsumer.class);
            return "ERRO: " + e.getMessage();
        }
    }

    public static String gerarNotificacoes(final String key, PactoPayComunicacaoVO pactoPayComunicacaoVO,
                                           String codigoCliente, String mensagem) throws Exception {
        Uteis.logar(null, String.format("TreinoWSConsumer -> Gerando notificacoes PactoPayComunicacao %s da chave %s",
                new Object[]{pactoPayComunicacaoVO.getCodigo(), key}));
        if (!empresaTemIntegracaoTW(key)) {
            throw new Exception("Não tem integração com TW");
        }
        mensagem = Uteis.retiraTags(mensagem, false);
        new PushController(null, key, mensagem, codigoCliente, pactoPayComunicacaoVO).sendMessage();
        return getInstance(key).enviarNotificacoes(key, pactoPayComunicacaoVO.getPactoPayComunicacaoDTO().getAssunto(),
                mensagem, "", codigoCliente);
    }
    
    public static String atualizarParq(String key, Integer codigoCliente, Boolean parq){
        Uteis.logar(null, String.format("TreinoWSConsumer -> Atualizando parq do aluno %s", 
                new Object[]{codigoCliente}));
        try {
            if(!empresaTemIntegracaoTW(key)) { return ""; }
            return getInstance(key).sincronizarParq(key, codigoCliente, parq.toString());
        } catch (Exception e) {
            Uteis.logar(e, TreinoWSConsumer.class);
        }
        return "";
    }
    
    public static String atualizarCrossfit(String key, Integer codigoMatricula, Boolean crossfit) {
        Uteis.logar(null, String.format("TreinoWSConsumer -> Atualizando crossfit do aluno de matricula %s", 
                new Object[]{codigoMatricula}));
        try {
            if(!empresaTemIntegracaoTW(key)) { return ""; }
            return getInstance(key).atualizarCrossfit(key, codigoMatricula, crossfit);
        } catch (Exception e) {
            Uteis.logar(e, TreinoWSConsumer.class);
        }
        return "";
    }
    
    public static String sincronizarRiscoUsuario(final String key, final Integer codigoCliente, final Integer risco) {
        Uteis.logar(null, String.format("TreinoWSConsumer -> Atualizando cliente %s da empresa %s", new Object[]{codigoCliente, key}));
        try {
            if(!empresaTemIntegracaoTW(key)) { return ""; }
            return getInstance(key).sincronizarRiscoCliente(key, codigoCliente, risco);
        } catch (Exception e) {
            Uteis.logar(e, TreinoWSConsumer.class);
        }
        return "";
    }
    
    public static String sincronizarCreditosAluno(final String key, final Integer codigoCliente, final Integer saldo, final Integer totalCreditoCompra) {
        Uteis.logar(null, String.format("TreinoWSConsumer -> Atualizando saldo creditos do aluno %s da empresa %s", new Object[]{codigoCliente, key}));
        try {
            if(!empresaTemIntegracaoTW(key)) { return ""; }
            return getInstance(key).sincronizarCreditosAluno(key, codigoCliente, saldo, totalCreditoCompra);
        } catch (Exception e) {
            Uteis.logar(e, TreinoWSConsumer.class);
        }
        return "";
    }

    public static JSONArray obterTodosAlunosTreino(final String key) {
        try {
            if(!empresaTemIntegracaoTW(key)) { return new JSONArray(); }
            String retorno = getInstance(key).obterTodosAlunos(key);
            return new JSONArray(retorno);
        } catch (Exception e) {
            Uteis.logar(e, TreinoWSConsumer.class);
        }
        return null;
    }
    public static String incrementarVersaoCliente(final String key, final UsuarioZW usuarioZW) {
        try {
            if(!empresaTemIntegracaoTW(key)) { return ""; }
            return getInstance(key).incrementarVersaoCliente(key, usuarioZW);
        } catch (Exception e) {
            Uteis.logar(e, TreinoWSConsumer.class);
}
        return "";
    }

    public static String atualizarFrequenciaSemanal(final String key, final Integer codigoCliente, Integer frequenciaSemanal){
        Uteis.logar(null, String.format("TreinoWSConsumer -> Atualizando frequência semanal do aluno %s da empresa %s", new Object[]{codigoCliente, key}));
        try {
            if(!empresaTemIntegracaoTW(key)) { return ""; }
            return getInstance(key).atualizarFrequenciaSemanal(key,codigoCliente,frequenciaSemanal);
        } catch (Exception e) {
            Uteis.logar(e, TreinoWSConsumer.class);
        }
        return "";
    }
    
    public static String realizouTreino(final String key, final String codigosClientes, final String data){
        Uteis.logar(null, String.format("TreinoWSConsumer -> Verificando se aluno %s da empresa %s realizou treino", new Object[]{codigosClientes, key}));
        try {
            if(!empresaTemIntegracaoTW(key)) { return ""; }
            return getInstance(key).realizouTreinoNovo(key, codigosClientes, data);
        } catch (Exception e) {
            Uteis.logar(e, TreinoWSConsumer.class);
        }
        return "";
    }

    public static String obterTodosProgramasAluno(final String key){
        Uteis.logar(null, String.format("TreinoWSConsumer -> Obtendo Data Vencimento Último Treino de todos os alunos"));
        Uteis.logar(null, String.format(getInstance(key).toString() +" - URL Requisição Treino"));
        try {
            if(!empresaTemIntegracaoTW(key)) { return ""; }
            return getInstance(key).obterTodosProgramasAlunos(key).toString();
        } catch (Exception e) {
            Uteis.logar(e, TreinoWSConsumer.class);
        }
        return "[]";
    }

    public static String consultarConfiguracaoSistemaTW(final String key, String configuracao) throws Exception {
        Uteis.logar(null, String.format("TreinoWSConsumer ->Configuração %s da empresa %s", new Object[]{configuracao, key}));
        try {
            if(!empresaTemIntegracaoTW(key)) { return ""; }
            String retorno = getInstance(key).obterConfiguracaoSistema(key, configuracao);
            if(retorno.contains("ERRO")){
                throw new Exception(retorno);
            }
            return retorno;
        } catch (Exception e) {
            Uteis.logar(e, TreinoWSConsumer.class);
            throw e;
        }
    }

    public static boolean empresaTemIntegracaoTW(String key) {
        String modulos = PropsService.getPropertyValue(key, PropsService.modulos);
        return modulos.contains("TR") || modulos.contains("NTR") || modulos.contains("NAV") || modulos.contains("NCR");
    }

    public static String sincronizarAlunoPesquisa(final String key, JSONObject jsonCliente) {
//        Uteis.logar(null, String.format("TreinoWSConsumer -> Atualizando usuário %s da empresa %s", new Object[]{usuario.getNome(), key}));
        try {
            if(!empresaTemIntegracaoTW(key)) { return ""; }
            return getInstance(key).addAlunoPesquisaTW(key, jsonCliente.toString());
        } catch (Exception e) {
            Sincronizador.inserirNaFila(TipoFilaEnum.ALUNO_TREINO, jsonCliente.getInt("codigo"), key);
            Uteis.logar(e, TreinoWSConsumer.class);
        }
        return "";
    }
    public static String excluirAlunosOrfaos(final String key, final String codigosPessoa){
        Uteis.logar(null, String.format("TreinoWSConsumer -> Excluindo alunos Órfãos no treino"));
        Uteis.logar(null, String.format(getInstance(key).toString() +" - URL Requisição Treino"));
        try {
            return getInstance(key).excluirAlunosOrfaos(key,codigosPessoa);
        } catch (Exception e) {
            Uteis.logar(e, TreinoWSConsumer.class);
        }
        return "[]";
    }

}
