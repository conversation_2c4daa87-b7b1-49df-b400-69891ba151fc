/*
 * To change this template, choose Too<PERSON> | Templates
 * and open the template in the editor.
 */
package controle.armario;

import br.com.pactosolucoes.comuns.notificacao.RecursoSistema;
import br.com.pactosolucoes.comuns.util.JSFUtilities;
import br.com.pactosolucoes.enumeradores.TipoAgendamentoEnum;
import br.com.pactosolucoes.estrutura.exportador.controle.ExportadorListaControle;
import br.com.pactosolucoes.estrutura.paginacao.ConfPaginacao;
import controle.arquitetura.FuncionalidadeSistemaEnum;
import controle.arquitetura.SuperControle;
import controle.arquitetura.security.AutorizacaoFuncionalidadeControle;
import controle.arquitetura.security.AutorizacaoFuncionalidadeListener;
import controle.crm.MalaDiretaControle;
import controle.financeiro.MovParcelaControle;
import controle.financeiro.VendaAvulsaControle;
import controle.plano.ProdutoControle;
import java.io.IOException;
import java.io.OutputStream;
import java.util.*;
import javax.faces.context.FacesContext;
import javax.faces.event.ActionEvent;
import javax.faces.model.SelectItem;
import javax.servlet.http.HttpServletRequest;

import negocio.armario.*;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.basico.ConfiguracaoSistemaVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.basico.enumerador.TipoItemCampanhaEnum;
import negocio.comuns.crm.MailingAgendamentoVO;
import negocio.comuns.crm.MalaDiretaEnviadaVO;
import negocio.comuns.crm.MalaDiretaVO;
import negocio.comuns.crm.MeioEnvio;
import negocio.comuns.financeiro.MovParcelaVO;
import negocio.comuns.financeiro.VendaAvulsaVO;
import negocio.comuns.plano.ConfiguracaoProdutoEmpresaVO;
import negocio.comuns.plano.PlanoTextoPadraoVO;
import negocio.comuns.plano.ProdutoVO;
import negocio.comuns.plano.enumerador.TipoProduto;
import negocio.comuns.utilitarias.*;
import negocio.facade.jdbc.armario.ProrataArmario;
import negocio.facade.jdbc.arquitetura.FacadeManager;
import relatorio.controle.arquitetura.SuperControleRelatorio;
import relatorio.controle.basico.FiltroArmarioTO;

/**
 *
 * <AUTHOR>
 */
public class GestaoArmarioControle extends SuperControleRelatorio {

    private EmpresaVO empresaAdmin;
    private Integer inicio;
    private Integer fim;
    private TamanhoArmarioVO tamanhoArmario = new TamanhoArmarioVO();
    private ArmarioVO armario = new ArmarioVO();
    private boolean podeAlterarVigencia = false;
    private GrupoArmarioEnum grupoArmario;
    private List<SelectItem> grupos = new ArrayList<SelectItem>();
    private List<SelectItem> gruposFiltro = new ArrayList<SelectItem>();
    private List<SelectItem> situacoes = new ArrayList<SelectItem>();
    private List<SelectItem> tamanhos = new ArrayList<SelectItem>();
    private List<SelectItem> produtos = new ArrayList<SelectItem>();
    private List<ArmarioVO> armarios = new ArrayList<ArmarioVO>();
    private List<ArmarioVO> todosArmarios = new ArrayList<ArmarioVO>();
    private Map<Integer, ProdutoVO> mapaProdutos;
    private AluguelArmarioVO aluguelArmario;
    private Date inicioAluguel;
    private Map<Integer, byte[]> fotos = new HashMap<Integer, byte[]>();
    private int situacoesEscolhida = 9;
    private int tamanhosEscolhido;
    private String gruposEscolhido = "0";
    private Date vencimentoPrimeiraParcela = Calendario.hoje();
    private Integer nrVezesParcelamento = 1;
    private Boolean ordenarPorDataFinal = false;
    private Integer codigoArmarioSelecionado;
    private String oncomplete = "";
    private List<SelectItem> armariosTroca = new ArrayList<SelectItem>();
    private Integer armarioTrocaSelecionado;
    private ArmarioVO armarioSelecionado;
    private ProdutoVO produtoSelecionado;
    private Boolean habilitarGestaoArmarios;
    private AluguelArmarioVO aluguelNegociar;
    private ArmarioVO armarioNegociar;
    private Integer modeloContrato;
    private List<SelectItem> modelosContrato;
    private Boolean fecharNegociacao = false;
    private List<TamanhoArmarioVO> tamanhoSelecionados = new ArrayList<TamanhoArmarioVO>();
    private List<TamanhoArmarioVO> tamanhosConsultados;
    private String valorConsultaArmario = "Pesquisar";
    private List<FiltroArmarioTO> filtroItem;
    private Boolean apresentarConfirmarDevolucao = false;
    public GestaoArmarioControle() {
        try {
            inicializarPaginacao();
            carregarArmarios();
            montarFiltros();
            validarConfiguracoesSistema();
        } catch (Exception e) {
            setMensagemDetalhada(e);
        }

    }

    public void inicializarPaginacao() throws Exception {
        setConfPaginacao(new ConfPaginacao());
        getConfPaginacao().setItensPorPagina(12);
        getConfPaginacao().setPaginaAtual(1);
        getConfPaginacao().setNumeroTotalItens(getFacade().getArmario().obterTotalArmarios(obterFiltro()));
        getConfPaginacao().definirVisibilidadeLinksNavegacao();
    }

    public String getSituacao_Apresentar() {
        return StatusArmarioEnum.obterPorOrdinal(situacoesEscolhida) == null ? "" : StatusArmarioEnum.obterPorOrdinal(situacoesEscolhida).getLabel() + ",";
    }

    public String getGrupo_Apresentar() {
        return GrupoArmarioEnum.obterPorCodigo(gruposEscolhido) == null ? "" : GrupoArmarioEnum.obterPorCodigo(gruposEscolhido).getLabel() + ",";
    }

    public void validarConfiguracoesSistema() throws Exception {
        ConfiguracaoSistemaVO aux = FacadeManager.getFacade().getConfiguracaoSistema().consultarPorChavePrimaria(1, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        setHabilitarGestaoArmarios(aux.getHabilitarGestaoArmarios());
    }

    public void mudarData() throws Exception {
        if (getHabilitarGestaoArmarios()) {
            ProdutoVO produtoSelecionado = mapaProdutos.get(getAluguelArmario().getProduto().getCodigo());
            if (produtoSelecionado == null) {
                aluguelArmario.getMovProduto().setDataFinalVigencia(null);
                return;
            }
            if (inicioAluguel == null) {
                return;
            }
            atualizarVenda();
        }
    }

    public void carregarHistorico() {
        try {
            armario = (ArmarioVO) context().getExternalContext().getRequestMap().get("armario");
            armario.setHistoricoAluguel(new ArrayList<AluguelArmarioVO>());
            getFacade().getArmario().preencherHistoricoAluguel(armario);
            limparMsg();
            codigoArmarioSelecionado = armario.getCodigo();
            setMsgAlert("abrirPopup('historicoArmario.jsp?codigoArmarioSelecionado=" +
                    armario.getCodigo() + "', 'HistoricoArmario', 820, 575);");
        } catch (Exception e) {
            mensagensTela(false, "msg_erro", e.getMessage());
        }
    }

    public void selecionarProduto() {
        try {
            limparMsg();
            setMsgAlert("");
            produtoSelecionado = mapaProdutos.get(getAluguelArmario().getProduto().getCodigo());
            if (produtoSelecionado == null) {
                aluguelArmario.setValor(null);
                aluguelArmario.getMovProduto().setDataFinalVigencia(null);
                return;
            }
            if (!produtoSelecionado.getTipoProduto().equals(TipoProduto.ARMARIO.getCodigo())) {
                throw new Exception(getMensagemInternalizacao("produto_nao_armario"));
            }

            if (!getHabilitarGestaoArmarios()) {
                aluguelArmario.getMovProduto().setDataFinalVigencia(Uteis.somarDias(inicioAluguel, produtoSelecionado.getNrDiasVigencia() > 0 ? (produtoSelecionado.getNrDiasVigencia() - 1) : 0));
            }
            if (UteisValidacao.emptyList(produtoSelecionado.getConfiguracoesEmpresa())) {
                aluguelArmario.setValor(produtoSelecionado.getValorFinal());
            } else {
                for (ConfiguracaoProdutoEmpresaVO cpe : produtoSelecionado.getConfiguracoesEmpresa()) {
                    if (cpe.getEmpresa().getCodigo().equals(getCodigoEmpresaArmarios())) {
                        aluguelArmario.setValor(produtoSelecionado.getValorFinal());
                    }
                }
            }
            vencimentoPrimeiraParcela = Uteis.somarDias(inicioAluguel, produtoSelecionado.getNrDiasVigencia() - 1);
            atualizarVenda();
        } catch (Exception e) {
            mensagensTela(false, "msg_erro", e.getMessage());
        }
    }

    public void prepararAluguelArmario() {
        try {
            limparMsg();
            setMsgAlert("");

            nrVezesParcelamento = 1;
            vencimentoPrimeiraParcela = Calendario.hoje();
            armario = (ArmarioVO) context().getExternalContext().getRequestMap().get("armario");

            if (armario.getAluguelAtual() == null) {
                inicioAluguel = Calendario.hoje();
                aluguelArmario = new AluguelArmarioVO();
            } else {
                inicioAluguel = armario.getAluguelAtual().getMovProduto().getDataInicioVigencia();
                vencimentoPrimeiraParcela = Uteis.obterUltimoDiaMes(Calendario.hoje());
                aluguelArmario = armario.getAluguelAtual();
            }
            montarListaSelectProdutos(armario.getTamanhoArmario().getCodigo());
            inicializarPaginacao();
            carregarArmarios();
            limparDadosNegociacao();
            setMsgAlert("Richfaces.showModalPanel('modalAlugarArmarios');");
        } catch (Exception e) {
            mensagensTela(false, "msg_erro", e.getMessage());
        }
    }

    public void limparDadosNegociacao() {
        aluguelNegociar = new AluguelArmarioVO();
        armarioNegociar = new ArmarioVO();
        fecharNegociacao = false;
    }

    public void validarDevolucaoArmario() throws Exception {
        if (getHabilitarGestaoArmarios()) {
            List<MovParcelaVO> parcelas = getFacade().getMovParcela().consultarPorCodigoVendaAvulsaLista(aluguelArmario.getVendaAvulsa().getCodigo(), null, null, "", false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            for (MovParcelaVO parcela : parcelas) {
                if (parcela.getSituacao().equals("EA")) {
                    apresentarConfirmarDevolucao = true;
                    setMensagemDetalhada("Não é possível abrir Armário com parcelas em aberto.");
                    break;
                }
            }
        }
    }

    public String abrirTelaCaixa() throws Exception {
        preparaTelaCaixa(aluguelNegociar);
        apresentarConfirmarDevolucao = false;
        return "tela8";
    }

    public void abrirArmario() throws Exception {
        final AutorizacaoFuncionalidadeControle auto = (AutorizacaoFuncionalidadeControle) getControlador(AutorizacaoFuncionalidadeControle.class);
        auto.setPedirPermissao(false);
        try{
            validarDevolucaoArmario();
        } catch (Exception e) {
            montarErro(e.getMessage());
        }
        if (!apresentarConfirmarDevolucao){
            AutorizacaoFuncionalidadeListener listener = new AutorizacaoFuncionalidadeListener() {

                @Override
                public void onAutorizacaoComSucesso() throws Exception {
                    aluguelArmario.setArmario(armario);
                    getFacade().getArmario().alterarAluguelArmario(aluguelArmario, true);
                    getFacade().getArmario().gerarHistoricoOperacao(aluguelArmario.getCodigo(), armario.getCodigo(), OperacaoArmarioEnum.ABERTURA.getDescricao(), OperacaoArmarioEnum.ABERTURA, auto.getUsuarioLogado().getCodigo());

                    mensagensTela(true, "armario_aberto", "");
                    inicializarPaginacao();
                    carregarArmarios();
                    montarFiltros();
                    setMsgAlert("Richfaces.hideModalPanel('modalAlugarArmarios');");
                }
                @Override
                public String getExecutarAoCompletar() {
                    return getOnComplete();
                }
            };
            autorizarAcao("Abrir Armário", "Alugar Armário", "AlugarArmario", "form:panelConteudo", listener);
        }
    }

    private void montarListaSelectProdutos(Integer tamanho) throws Exception {
        produtos = new ArrayList<SelectItem>();
        mapaProdutos = new HashMap<Integer, ProdutoVO>();
        produtos.add(new SelectItem(null, ""));
        List<ProdutoVO> produtosVO = getFacade().getProduto().consultarPorTamanhoArmario(tamanho, getEmpresaLogado().getCodigo());
        for (ProdutoVO prod : produtosVO) {
            produtos.add(new SelectItem(prod.getCodigo(), prod.getDescricao() + " - " + prod.getNrDiasVigencia() + " dias"));
            mapaProdutos.put(prod.getCodigo(), prod);
        }
    }

    public List<ClienteVO> executarAutocompleteConsultaCliente(Object suggest) {
        String pref = (String) suggest;
        try {
            List<ClienteVO> result = getFacade().getCliente().consultarPorNomeClienteComLimite(armario.getEmpresa().getCodigo(), pref, false, Uteis.NIVELMONTARDADOS_ROBO);
            return result;
        } catch (Exception ex) {
            setMensagemDetalhada("msg_erro", ex.getMessage());
        }
        return new ArrayList<ClienteVO>();
    }

    public void selecionarClienteSuggestionBox() throws Exception {
        ClienteVO clienteVO = (ClienteVO) request().getAttribute("result");
        if (clienteVO != null) {
            getAluguelArmario().setCliente(clienteVO);
        }
    }

    public void novosArmarios() {
        inicio = null;
        fim = null;
        tamanhoArmario = new TamanhoArmarioVO();
        grupoArmario = GrupoArmarioEnum.UNISSEX;
        limparMsg();
        setMsgAlert("Richfaces.showModalPanel('modalGerarArmarios');");
        montarComboTiposArmario();
    }

    public void gerarArmarios() {
        try {
            limparMsg();
            setMsgAlert("");

            if(inicio == null || fim == null){
                throw new Exception("O número inicial e ou final devem ser informados.");
            }

            if (fim < inicio) {
                throw new Exception("O número inicial não pode ser maior que o final.");
            }
            if ((fim - inicio) < 500) {
                getFacade().getArmario().gerarArmarios(inicio, fim, tamanhoArmario, grupoArmario, getUsuarioLogado(), getCodigoEmpresaArmarios());
            } else {
                throw new Exception(" Não é possível cadastrar mais que 500 armários por vez.");
            }
            inicializarPaginacao();
            carregarArmarios();
            setMsgAlert("Richfaces.hideModalPanel('modalGerarArmarios');");
            montarSucessoGrowl("Armários gerados com sucesso!");
        } catch (Exception e) {
            montarErro(e);
        }
    }

    public void excluirTamanhoSelecionado() {
        try {
            FiltroArmarioTO filtro = (FiltroArmarioTO) context().getExternalContext().getRequestMap().get("filtroitem");
            filtroItem.remove(filtro);
            inicializarPaginacao();
            carregarArmarios();
        } catch (Exception erro) {
            setMensagemDetalhada(erro);
        }
    }

    public void excluirArmario() {
        try {
            limparMsg();
            ArmarioVO armarioExc = (ArmarioVO) context().getExternalContext().getRequestMap().get("armario");
            String msg = "armario_excluido";
            try {
                getFacade().getArmario().excluir(armarioExc);
            } catch (Exception e) {
                armarioExc.setStatus(StatusArmarioEnum.INATIVO);
                getFacade().getArmario().inativarArmario(armarioExc);
                msg = "armario_inativado";
            }
            montarFiltros();
            inicializarPaginacao();
            carregarArmarios();
            mensagensTela(true, msg, "");
        } catch (Exception e) {
            mensagensTela(false, "msg_erro", e.getMessage());
        }
    }

    public void abrirTrocaArmario() throws Exception {
        limparMsg();
        armariosTroca.clear();

        FiltroArmarioTO filtro = new FiltroArmarioTO();
        filtro.setEmpresa(getCodigoEmpresaArmarios());
        filtro.setHabilitadoGestaoArmarios(true);
        filtro.setOrdernarDataVigencia(false);

        armario = (ArmarioVO) context().getExternalContext().getRequestMap().get("armario");
        if(UteisValidacao.emptyList(todosArmarios)) {
            todosArmarios = getFacade().getArmario().consultar(filtro);
        }
        for (ArmarioVO armarioVO : todosArmarios) {
            if (armarioVO.getTamanhoArmario().getCodigo() == armario.getTamanhoArmario().getCodigo()) {
                if (armarioVO.getGrupo() == armario.getGrupo() && armarioVO.getAluguelAtual().getCliente().getCodigo() == 0) {
                    armariosTroca.add(new SelectItem(armarioVO.getCodigo(), armarioVO.getDescricao()));
                }
            }

        }
        aluguelArmario.setArmario(armario);
        setMsgAlert("Richfaces.showModalPanel('modalTrocarArmarios');");
    }

    public void selecionarArmarioTrocar() {

        for (ArmarioVO armario : todosArmarios) {
            if (armario.getCodigo().equals(armarioTrocaSelecionado)) {
                armarioSelecionado = armario;
                break;
            }
        }
    }

    public void trocarArmario() {
        try {

            if (armarioSelecionado == null)
                selecionarArmarioTrocar();

            if (armarioSelecionado.isFechado())
                throw new Exception("O Armario de destino está fechado!");
            String descricaoHistorico = "TROCA ARMÁRIO : Armário " + armario.getDescricao() + " para Armário:" + armarioSelecionado.getDescricao();
            getFacade().getArmario().abrirArmario(armario);
            armarioSelecionado.setAluguelAtual(armario.getAluguelAtual());
            armarioSelecionado.getAluguelAtual().setArmario(armarioSelecionado);
            getFacade().getArmario().fecharArmario(armarioSelecionado);
            getFacade().getArmario().trocarArmarioAluguel(armarioSelecionado);
            getFacade().getArmario().gerarHistoricoOperacao(armario.getAluguelAtual().getCodigo(), armario.getCodigo(), descricaoHistorico, OperacaoArmarioEnum.TROCA_ARMARIO, getUsuarioLogado().getCodigo());
            inicializarPaginacao();
            carregarArmarios();
            montarFiltros();
            setMsgAlert("Richfaces.hideModalPanel('modalTrocarArmarios');");
        } catch (Exception erro) {
            setMsgAlert(erro.getMessage());
        }

    }

    public void gravarArmario() {
        try {
            limparMsg();
            getFacade().getArmario().alterar(armario);
            inicializarPaginacao();
            carregarArmarios();
            mensagensTela(true, "armario_alterado", "");
            setMsgAlert("Richfaces.hideModalPanel('modalEditarArmarios');");
        } catch (Exception e) {
            mensagensTela(false, "msg_erro", e.getMessage());
        }
    }

    public void onMudarDataVigencia() {
        AutorizacaoFuncionalidadeListener listener = new AutorizacaoFuncionalidadeListener() {
            @Override
            public void onAutorizacaoComSucesso() {
                podeAlterarVigencia = true;
            }
            @Override
            public String getExecutarAoCompletar() {
                return getOnComplete();
            }
        };
        autorizarAcao(
                "Alterar Vigência Armário", "Alterar Vigência Armário",
                "AlterarVigenciaArmario", "formModalAlugarArmarios", listener);
    }

    public void montarFiltros() throws Exception {
        resetarTiposArmario();
        gruposFiltro = obterListaGrupo();
        grupos = GrupoArmarioEnum.obterSelectItem();
        situacoes = obterListaSituacoes();

    }

    public String obterSomaArmarioGrupo(GrupoArmarioEnum grupo) {
        int sum = 0;
        for (ArmarioVO armarioVO : armarios) {
            if (armarioVO.getGrupo().equals(grupo))
                sum++;
        }
        return "(" + sum + ")";
    }

    public List<SelectItem> obterListaGrupo() throws Exception {
        List<SelectItem> lista = new ArrayList<SelectItem>();
        lista.add(new SelectItem(null, " - "));
        for (GrupoArmarioEnum grupo : GrupoArmarioEnum.values()) {
            lista.add(new SelectItem(grupo, grupo.getLabel() + "(" + getFacade().getArmario().obterSumPorGrupo(grupo.getCodigo(), getCodigoEmpresaArmarios()) + ")"));
        }
        return lista;
    }

    public String obterSomaArmarioSituacao(StatusArmarioEnum status) {
        int sum = 0;
        for (ArmarioVO armarioVO : armarios) {
            if (armarioVO.getStatus().equals(status))
                sum++;
        }
        return "(" + sum + ")";
    }

    public List<SelectItem> obterListaSituacoes() throws Exception {
        List<SelectItem> lista = new ArrayList<SelectItem>();
        lista.add(new SelectItem(9, " - "));
        for (StatusArmarioEnum status : StatusArmarioEnum.values()) {
            lista.add(new SelectItem(status.getId(), status.getLabel() + "(" + getFacade().getArmario().obterSumPorStatus(status.getId(), getCodigoEmpresaArmarios()) + ")"));
        }
        return lista;
    }

    public void editarArmario() {
        limparMsg();
        armario = (ArmarioVO) context().getExternalContext().getRequestMap().get("armario");
        montarComboTiposArmario();
        setMsgAlert("Richfaces.showModalPanel('modalEditarArmarios');");
    }

    public String obterSomaArmarioTamanho(int codigoTamanho) {
        int sum = 0;
        for (ArmarioVO armarioVO : armarios) {
            if (armarioVO.getTamanhoArmario().getCodigo().equals(codigoTamanho))
                sum++;
        }
        return "(" + sum + ")";
    }

    public FiltroArmarioTO obterFiltro() throws Exception {
        FiltroArmarioTO filtro = new FiltroArmarioTO();
        filtro.setValorConsulta(valorConsultaArmario);
        filtro.setEmpresa(getCodigoEmpresaArmarios());
        filtro.setLimit(getConfPaginacao().getItensPorPagina());
        filtro.setOffset(((getConfPaginacao().getPaginaAtual()) * getConfPaginacao().getItensPorPagina()) - getConfPaginacao().getItensPorPagina());
        filtro.setFiltroConsulta(filtroItem);
        filtro.setHabilitadoGestaoArmarios(habilitarGestaoArmarios);
        filtro.setOrdernarDataVigencia(ordenarPorDataFinal);
        return filtro;
    }

    public void carregarArmarios() {
        try {
            limparMsg();
            setMsgAlert("");
            FiltroArmarioTO filtroConsulta = obterFiltro();
            armarios = getFacade().getArmario().consultar(filtroConsulta);
            if (!isFotosNaNuvem()) {
                montarFotos();
            }

            getConfPaginacao().setNumeroTotalItens(getFacade().getArmario().obterTotalArmarios(filtroConsulta));
            getConfPaginacao().definirVisibilidadeLinksNavegacao();
        } catch (Exception e) {
            mensagensTela(false, "msg_erro", e.getMessage());
        }
    }

    public void proximaPaginacao() {
        int pagAtual = getConfPaginacao().getPaginaAtual();
        int nrPaginas = getConfPaginacao().getNrTotalPaginas();
        getConfPaginacao().setPaginaAtual((pagAtual + 1) > nrPaginas ? nrPaginas : pagAtual + 1);
        getConfPaginacao().definirVisibilidadeLinksNavegacao();
        carregarArmarios();
    }

    public void anteriorPaginacao() {
        int pagAtual = getConfPaginacao().getPaginaAtual();
        getConfPaginacao().setPaginaAtual((pagAtual - 1) <= 0 ? pagAtual : (pagAtual - 1));
        getConfPaginacao().definirVisibilidadeLinksNavegacao();
        carregarArmarios();
    }

    public void primeiraPaginacao() {
        getConfPaginacao().setPaginaAtual(1);
        getConfPaginacao().definirVisibilidadeLinksNavegacao();
        carregarArmarios();
    }

    public void ultimaPaginacao() {
        int nrPaginas = getConfPaginacao().getNrTotalPaginas();
        getConfPaginacao().setPaginaAtual(nrPaginas);
        getConfPaginacao().definirVisibilidadeLinksNavegacao();
        carregarArmarios();
    }

    private Integer getCodigoEmpresaArmarios() throws Exception {
        return getEmpresaLogado() == null ? getEmpresaAdmin().getCodigo() : getEmpresaLogado().getCodigo();
    }

    public String abrirGestaoArmarios() {
        notificarRecursoEmpresa(RecursoSistema.fromDescricao(FuncionalidadeSistemaEnum.GESTAO_DE_ARMARIOS.toString()));
        return "gestaoArmario";
    }

    public void resetarTiposArmario() {
        try {
            tamanhos = new ArrayList<SelectItem>();
            List<TamanhoArmarioVO> tipos = getFacade().getTamanhoArmario().consultarPorDescricao("");
            for (TamanhoArmarioVO tipo : tipos) {
                tamanhos.add(new SelectItem(tipo.getCodigo(), tipo.getDescricao() + "(" + getFacade().getArmario().obterSumPorTamanho(tipo.getCodigo(), getCodigoEmpresaArmarios()) + ")"));
            }
            tamanhosConsultados = tipos;
        } catch (Exception e) {
            Uteis.logar(e, ProdutoControle.class);
            tamanhos = new ArrayList<SelectItem>();
        }
    }

    public void montarComboTiposArmario() {
        if (tamanhos == null || tamanhos.isEmpty()) {
            resetarTiposArmario();
        }
    }

    public void paintFoto(OutputStream out, Object data) throws Exception {
        if (getAluguelArmario().getCliente().getPessoa().getFoto() == null || getAluguelArmario().getCliente().getPessoa().getFoto().length == 0) {
            recarregarFoto();
        }
        SuperControle.paintFoto(out, getAluguelArmario().getCliente().getPessoa().getFoto());
    }

    public String getPaintFotoDaNuvem() {
        return getPaintFotoDaNuvem(getAluguelArmario().getCliente().getPessoa().getFotoKey());
    }

    public void recarregarFoto() throws Exception {
        if (isFotosNaNuvem()) {
            final String fotoKey = getFacade().getPessoa().obterFotoKey(
                    getAluguelArmario().getCliente().getPessoa().getCodigo());
            if (!UteisValidacao.emptyString(fotoKey)) {
                getAluguelArmario().getCliente().getPessoa().setFotoKey(fotoKey + "?time=" + getTimeStamp());
            }
        } else {
            getAluguelArmario().getCliente().getPessoa().setFoto(getFacade().getPessoa().obterFoto(
                    getKey(),
                    getAluguelArmario().getCliente().getPessoa().getCodigo()));
        }
    }

    public void enviarMalaDireta() throws Exception, Throwable {
        MalaDiretaVO malaDiretaVO = new MalaDiretaVO();
        malaDiretaVO.setContatoAvulso(true);
        malaDiretaVO.setTipoAgendamento(TipoAgendamentoEnum.AGENDAMENTO_INSTANTANEO);
        malaDiretaVO.setAgendamento(new MailingAgendamentoVO());

        malaDiretaVO.setMeioDeEnvioEnum(MeioEnvio.EMAIL);
        PlanoTextoPadraoVO contrato = getFacade().getPlanoTextoPadrao().consultarPorChavePrimaria(modeloContrato, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        //-----------------------------------------------------------
        malaDiretaVO.setTitulo(contrato.getDescricao());
        malaDiretaVO.setMensagem(contrato.getTexto());
        malaDiretaVO.setEmpresa(getEmpresaLogado());
        malaDiretaVO.setRemetente(getUsuarioLogado());
        //-----------------------------------------------------------
        MalaDiretaEnviadaVO malaDiretaEnviadaVO = new MalaDiretaEnviadaVO();
        malaDiretaEnviadaVO.setMalaDiretaVO(malaDiretaVO);
        malaDiretaEnviadaVO.setClienteVO(getArmario().getAluguelAtual().getCliente());
        //-----------------------------------------------------------
        malaDiretaVO.getMalaDiretaEnviadaVOs().add(malaDiretaEnviadaVO);
        MalaDiretaControle mdc = (MalaDiretaControle) JSFUtilities.getManagedBeanValue("MalaDiretaControle");
        mdc = new MalaDiretaControle();
        mdc.novo();
        mdc.setUsarAgendamento(true);
        mdc.setMalaDiretaVO(malaDiretaVO);
        mdc.gravar();
    }

    public void alugarArmarioSemProrata() {
        final AutorizacaoFuncionalidadeControle auto = (AutorizacaoFuncionalidadeControle) getControlador(AutorizacaoFuncionalidadeControle.class);
        auto.setPedirPermissao(false);
        AutorizacaoFuncionalidadeListener listener = new AutorizacaoFuncionalidadeListener() {

            @Override
            public void onAutorizacaoComSucesso() throws Exception {
                boolean fecharModal = false;
                if (UteisValidacao.emptyNumber(aluguelArmario.getCodigo())) {
                    aluguelArmario.setResponsavelCadastro(auto.getUsuario());
                    VendaAvulsaVO venda = getFacade().getArmario().alugarArmario(armario, aluguelArmario, getUsuarioLogado(), inicioAluguel,
                            aluguelArmario.getMovProduto().getDataFinalVigencia(), nrVezesParcelamento, vencimentoPrimeiraParcela);
                    limparControladoresVenda();
                    limparMsg();
                    if (aluguelArmario.getValor() == 0) {
                        limparMsg();
                        setMsgAlert("");
                        inicializarPaginacao();
                        carregarArmarios();
                        fecharModal = true;
                    } else {
                        VendaAvulsaControle vendaAvulsaControle = (VendaAvulsaControle) getControlador(VendaAvulsaControle.class);
                        if (vendaAvulsaControle == null) {
                            vendaAvulsaControle = new VendaAvulsaControle();
                            JSFUtilities.setManagedBeanValue("VendaAvulsaControle", vendaAvulsaControle);
                        }
                        vendaAvulsaControle.setVendaAvulsaVO(venda);
                        getFacade().getArmario().gerarHistoricoOperacao(aluguelArmario.getCodigo(), aluguelArmario.getArmario().getCodigo(), OperacaoArmarioEnum.LOCACAO.getDescricao(), OperacaoArmarioEnum.LOCACAO, getUsuarioLogado().getCodigo());
                        setPaginaDestino("pagamento");
                        return;
                    }
                } else {
                    getFacade().getArmario().alterarAluguelArmario(aluguelArmario, false);
                    setMsgAlert("Richfaces.hideModalPanel('modalAlugarArmarios');");
                    mensagensTela(Boolean.TRUE, "datafinal_aluguel_alterada_sucesso", "");
                }
                carregarArmarios();
                if (fecharModal) {
                    setMsgAlert("Richfaces.hideModalPanel('modalAlugarArmarios');");
                }
            }
            @Override
            public String getExecutarAoCompletar() {
                return getOnComplete();
            }
        };
        autorizarAcao("Alugar Armário", "Alugar Armário", "AlugarArmario", "form:panelConteudo", listener);
    }

    public void preparaTelaCaixa(AluguelArmarioVO aluguelArmarioVO) throws Exception {
        MovParcelaControle movParcela = (MovParcelaControle) JSFUtilities.getManagedBeanValue("MovParcelaControle");
        movParcela.setValorConsulta(aluguelArmarioVO.getCliente().getPessoa().getNome());
        movParcela.consultaCaixaAberto();
    }

    public void atualizarVenda() {
        try {
            clsMessages();
            resolveVigencia();
            if (getFecharNegociacao()) {
                alugarArmarioProrata();
            }
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    private void resolveVigencia() throws  Exception {
        // Tempo máximo que este armário poderá ser alugado
        Date vigencia = Uteis.obterDataFutura2(inicioAluguel, produtoSelecionado.getNrDiasVigencia() - 1);
        try {
            if (Calendario.maior(vencimentoPrimeiraParcela, vigencia)) {
                // Se o tempo for superior a vigência
                montarInfo("A data máxima de vencimento para o produto será " + Uteis.getData(vigencia));
            } else if (Calendario.maior(inicioAluguel, vencimentoPrimeiraParcela)) {
                // Se a data inicial foi alterada para uma data superior a final
                vigencia = new Date(inicioAluguel.getTime());
            } else {
                vigencia = vencimentoPrimeiraParcela;
            }
        } finally {
            // Mantem os valores atualizados.
            vencimentoPrimeiraParcela = vigencia;
            aluguelArmario.getMovProduto().setDataFinalVigencia(vigencia);
        }
    }

    public void alugarArmarioProrata() throws Exception {
        aluguelNegociar = aluguelArmario.getClone();
        armarioNegociar = armario.getClone();
        ProrataArmario prorataArmario = new ProrataArmario();
        prorataArmario.setConfiguracaoSistemaVO(FacadeManager.getFacade().getConfiguracaoSistema().consultarPorChavePrimaria(1, Uteis.NIVELMONTARDADOS_DADOSBASICOS));
        prorataArmario.setDataInicio(inicioAluguel);
        prorataArmario.setHoje(Calendario.hoje());
        prorataArmario.setVencimentoParcela(vencimentoPrimeiraParcela);
        prorataArmario.setNumeroParcelas(nrVezesParcelamento);
        prorataArmario.setDiaReferenciaProrata(Uteis.getDiaMesData(vencimentoPrimeiraParcela));
        prorataArmario.verificarConfiguracoesSistema();
        VendaAvulsaVO venda = prorataArmario.montarVenda(aluguelNegociar, getUsuarioLogado(), produtoSelecionado, nrVezesParcelamento, vencimentoPrimeiraParcela);
        prorataArmario.setVenda(venda);
        prorataArmario.processarParcelas(aluguelNegociar);
        aluguelNegociar.setVendaAvulsa(prorataArmario.getVenda());
        Ordenacao.ordenarLista(aluguelNegociar.getVendaAvulsa().getMovParcelaVOs(), "dataVencimento");
    }

    public void validarDatas() throws Exception {
        if (Calendario.maior(getInicioAluguel(), Calendario.hoje())) {
            throw new Exception("Não é possível lançar fechar Armário com data futura.");
        }
    }

    public String incluirAluguelProrata() throws Throwable {
        final AutorizacaoFuncionalidadeControle auto = (AutorizacaoFuncionalidadeControle) getControlador(AutorizacaoFuncionalidadeControle.class);
        auto.setPedirPermissao(false);
        AutorizacaoFuncionalidadeListener listener = new AutorizacaoFuncionalidadeListener() {

            @Override
            public void onAutorizacaoComSucesso() throws Exception {
                if (UteisValidacao.emptyNumber(aluguelNegociar.getCodigo())) {
                    validarDatas();
                    aluguelNegociar.setResponsavelCadastro(auto.getUsuario());
                    VendaAvulsaVO venda = getFacade().getArmario().alugarArmarioProrata(armarioNegociar, aluguelNegociar, aluguelNegociar.getVendaAvulsa(), inicioAluguel,
                            aluguelNegociar.getMovProduto().getDataFinalVigencia(), nrVezesParcelamento, vencimentoPrimeiraParcela, Calendario.hoje());
                    limparControladoresVenda();
                    limparMsg();
                    if (aluguelNegociar.getValor() == 0) {
                        limparMsg();
                        setMsgAlert("");
                        inicializarPaginacao();
                        carregarArmarios();
                    } else {
                        VendaAvulsaControle vendaAvulsaControle = (VendaAvulsaControle) getControlador(VendaAvulsaControle.class);
                        if (vendaAvulsaControle == null) {
                            vendaAvulsaControle = new VendaAvulsaControle();
                            JSFUtilities.setManagedBeanValue("VendaAvulsaControle", vendaAvulsaControle);
                        }
                        vendaAvulsaControle.setVendaAvulsaVO(venda);
                        try {
                            enviarMalaDireta();
                        }catch (Throwable ignored){}
                        fecharNegociacao = false;
                        preparaTelaCaixa(aluguelNegociar);
                        getFacade().getArmario().gerarHistoricoOperacao(aluguelNegociar.getCodigo(), aluguelNegociar.getArmario().getCodigo(), OperacaoArmarioEnum.LOCACAO.getDescricao(), OperacaoArmarioEnum.LOCACAO, getUsuarioLogado().getCodigo());
                        setPaginaDestino("tela8");
                        gerarPontuacaoArmario();
                        return;
                    }
                } else {
                    getFacade().getArmario().alterarAluguelArmario(aluguelArmario, false);
                    setMsgAlert("Richfaces.hideModalPanel('modalAlugarArmarios');");
                    mensagensTela(Boolean.TRUE, "datafinal_aluguel_alterada_sucesso", "");
                }
            }

            @Override
            public String getExecutarAoCompletar() {
                return getOnComplete();
            }

            @Override
            public void onFecharModalAutorizacao() {
                getControlador(AutorizacaoFuncionalidadeControle.class).setRenderComponents("panelAutorizacaoFuncionalidade");
            }
        };
        autorizarAcao("Alugar Armário", "Alugar Armário", "AlugarArmario", "form:panelConteudo", listener);
        return "";
    }

    private void gerarPontuacaoArmario() throws Exception{
        if(getEmpresaLogado().isTrabalharComPontuacao() ){
            getFacade().getHistoricoPontos().incluirPontuacaoPorTipo(TipoItemCampanhaEnum.PRODUTO, aluguelArmario.getProduto().getCodigo()," Alugar Armário ", aluguelArmario.getCliente(), getEmpresaLogado());
        }
    }

    public void validarData() throws Exception {
        if (Calendario.menor(vencimentoPrimeiraParcela, inicioAluguel)) {
            throw new Exception("Data de vencimento da parcela não pode ser menor que a data inicio.");
        }
    }

    public String pagarAluguel() throws Throwable {
        try {
            if (habilitarGestaoArmarios) {
                validarData();
                alugarArmarioProrata();
                if (getFecharNegociacao()) {
                    incluirAluguelProrata();
                } else {
                    setFecharNegociacao(true);
                }
            } else {
                validarData();
                alugarArmarioSemProrata();
            }
        } catch (Exception erro) {
            montarErro(erro);
        }
        return "";
    }

    public TamanhoArmarioVO obterTamanhoListaCosulta(int codigo) {
        for (TamanhoArmarioVO tamanho : tamanhosConsultados) {
            if (codigo == tamanho.getCodigo())
                return tamanho;
        }
        return null;
    }

    public void adicionarItemFiltroNaLista() throws Exception {

        FiltroArmarioTO filtro = new FiltroArmarioTO();
        if (grupoArmario != null)
            filtro.setGrupoSelecionado(grupoArmario);
        if (getSituacoesEscolhidas() != 9)
            filtro.setStatus(StatusArmarioEnum.obterPorOrdinal(getSituacoesEscolhidas()));
        for (SelectItem itemSituacao : tamanhos) {
            if (itemSituacao.getValue().equals(tamanhosEscolhido) && !itemSituacao.getValue().equals(0)) {
                filtro.setTamanhoArmario((obterTamanhoListaCosulta(tamanhosEscolhido)));
            }
        }
        filtroItem.add(filtro);
        inicializarPaginacao();
        carregarArmarios();
    }

    public String getQtdTamanhosSelecionadas() {
        return "+" + String.valueOf(tamanhoSelecionados.size());
    }

    public void obterArmariosPaginaInicial() throws Exception {
        inicializarPaginacao();
        carregarArmarios();
    }

    public String getTamanhoSelecionadasApresentar() {
        StringBuilder sb = new StringBuilder("");
        boolean nenhumSelecionado = true;
        for (TamanhoArmarioVO tamanho : tamanhoSelecionados) {
            nenhumSelecionado = false;
            sb.append(tamanho.getDescricao() + obterSomaArmarioTamanho(tamanho.getCodigo())).append("<br/>");
        }
        if (nenhumSelecionado) {
            sb.append("TODAS AS SITUAÇÕES");
        }
        return sb.toString();
    }

    public void limparTamanhos() {
        resetarTiposArmario();
        filtroItem.clear();
        situacoesEscolhida = 9;
        gruposEscolhido = "0";
        carregarArmarios();
    }

    public String getRotuloBotao() throws Exception {

        return getHabilitarGestaoArmarios() ? (getFecharNegociacao() ? "Fechar Negociação" : "Confirmar") : "Fechar Negociação";

    }

    public Integer getInicio() {
        return inicio;
    }

    public void setInicio(Integer inicio) {
        this.inicio = inicio;
    }

    public Integer getFim() {
        return fim;
    }

    public void setFim(Integer fim) {
        this.fim = fim;
    }

    public TamanhoArmarioVO getTamanhoArmario() {
        return tamanhoArmario;
    }

    public void setTamanhoArmario(TamanhoArmarioVO tamanhoArmario) {
        this.tamanhoArmario = tamanhoArmario;
    }

    public GrupoArmarioEnum getGrupoArmario() {
        return grupoArmario;
    }

    public void setGrupoArmario(GrupoArmarioEnum grupoArmario) {
        this.grupoArmario = grupoArmario;
    }

    public List<SelectItem> getGrupos() {
        return grupos;
    }

    public void setGrupos(List<SelectItem> grupos) {
        this.grupos = grupos;
    }

    public List<ArmarioVO> getArmarios() {
        return armarios;
    }

    public void setArmarios(List<ArmarioVO> armarios) {
        this.armarios = armarios;
    }

    public List<SelectItem> getTamanhos() {
        return tamanhos;
    }

    public void setTamanhos(List<SelectItem> tamanhos) {
        this.tamanhos = tamanhos;
    }

    public ArmarioVO getArmario() {
        return armario;
    }

    public void setArmario(ArmarioVO armario) {
        this.armario = armario;
    }

    public List<SelectItem> getProdutos() {
        return produtos;
    }

    public void setProdutos(List<SelectItem> produtos) {
        this.produtos = produtos;
    }

    public EmpresaVO getEmpresaAdmin() {
        if (empresaAdmin == null) {
            empresaAdmin = new EmpresaVO();
        }
        return empresaAdmin;
    }

    public void setEmpresaAdmin(EmpresaVO empresaAdmin) {
        this.empresaAdmin = empresaAdmin;
    }

    public AluguelArmarioVO getAluguelArmario() {
        if (aluguelArmario == null) {
            aluguelArmario = new AluguelArmarioVO();
        }
        return aluguelArmario;
    }

    public void setAluguelArmario(AluguelArmarioVO aluguelArmario) {
        this.aluguelArmario = aluguelArmario;
    }

    public Date getInicioAluguel() {
        return inicioAluguel;
    }

    public void setInicioAluguel(Date inicioAluguel) {
        this.inicioAluguel = inicioAluguel;
    }

    public void paintFotoArmario(OutputStream out, Object data) throws IOException, Exception {
        HttpServletRequest request = (HttpServletRequest) FacesContext.getCurrentInstance().getExternalContext().getRequest();
        String pessoa = request.getParameter("pessoa");
        SuperControle.paintFoto(out, fotos.get(Integer.valueOf(pessoa)));
    }

    private void montarFotos() throws Exception {
        fotos = new HashMap<Integer, byte[]>();
        for (ArmarioVO arm : armarios) {
            if (arm.getAluguelAtual() != null
                    && !UteisValidacao.emptyNumber(arm.getAluguelAtual().getCodigo())
                    && fotos.get(arm.getAluguelAtual().getCliente().getPessoa().getCodigo()) == null) {
                byte[] foto = getFacade().getPessoa().obterFoto(getKey(), arm.getAluguelAtual().getCliente().getPessoa().getCodigo());
                fotos.put(arm.getAluguelAtual().getCliente().getPessoa().getCodigo(), foto);
            }
        }
    }

    public String getOnComplete() {//chamada implícita por 'AutorizacaoFuncionalidadeControle.control.onComplete'
        return getMsgAlert();
    }

    private void autorizarAcao(String titulo, String permissao,
                               String nome, String rerender, AutorizacaoFuncionalidadeListener listener) {
        AutorizacaoFuncionalidadeControle auto = (AutorizacaoFuncionalidadeControle) getControlador(AutorizacaoFuncionalidadeControle.class);
        auto.setPedirPermissao(false);
        //preparar controle de Permissão Especial
        auto.autorizar(titulo, nome,
                "Você precisa da permissão \"" + permissao + "\"",
                rerender, listener);
    }

    public int getTamanhosEscolhidos() {
        return tamanhosEscolhido;
    }

    public void setTamanhosEscolhidos(int tamanhosEscolhidos) {
        this.tamanhosEscolhido = tamanhosEscolhidos;
    }

    public String getGruposEscolhidos() {
        return gruposEscolhido;
    }

    public void setGruposEscolhidos(String gruposEscolhidos) {
        this.gruposEscolhido = gruposEscolhidos;
    }

    public Date getVencimentoPrimeiraParcela() {
        return vencimentoPrimeiraParcela;
    }

    public void setVencimentoPrimeiraParcela(Date vencimentoPrimeiraParcela) {
        this.vencimentoPrimeiraParcela = vencimentoPrimeiraParcela;
    }

    public Integer getNrVezesParcelamento() {
        return nrVezesParcelamento;
    }

    public void setNrVezesParcelamento(Integer nrVezesParcelamento) {
        this.nrVezesParcelamento = nrVezesParcelamento;
    }

    public List<SelectItem> getSituacoes() {
        return situacoes;
    }

    public void setSituacoes(List<SelectItem> situacoes) {
        this.situacoes = situacoes;
    }

    public int getSituacoesEscolhidas() {
        return situacoesEscolhida;
    }

    public void setSituacoesEscolhidas(int situacoesEscolhidas) {
        this.situacoesEscolhida = situacoesEscolhidas;
    }

    public boolean isPodeAlterarVigencia() {
        return podeAlterarVigencia;
    }

    public void setPodeAlterarVigencia(boolean podeAlterarVigencia) {
        this.podeAlterarVigencia = podeAlterarVigencia;
    }

    public Boolean getOrdenarPorDataFinal() {
        return ordenarPorDataFinal;
    }

    public void setOrdenarPorDataFinal(Boolean ordenarPorDataFinal) {
        this.ordenarPorDataFinal = ordenarPorDataFinal;
    }

    public Integer getCodigoArmarioSelecionado() {
        return codigoArmarioSelecionado;
    }

    public void setCodigoArmarioSelecionado(Integer codigoArmarioSelecionado) {
        this.codigoArmarioSelecionado = codigoArmarioSelecionado;
    }

    public List<SelectItem> getArmariosTroca() {
        return armariosTroca;
    }

    public void setArmariosTroca(List<SelectItem> armariosTroca) {
        this.armariosTroca = armariosTroca;
    }

    public void exportar(ActionEvent evt) throws Exception {
        ExportadorListaControle exportadorListaControle = (ExportadorListaControle) JSFUtilities.getFromRequest(ExportadorListaControle.class.getSimpleName());
        String paramsTableFiltrada = context().getExternalContext().getRequestParameterMap().get("paramsTableFiltrada");
        String extensao = (String) JSFUtilities.getFromActionEvent("tipo", evt);

        extensao = extensao.equals("xls") ? "vnd.ms-excel" : "pdf";
        try {
            codigoArmarioSelecionado = Integer.parseInt(request().getSession().getAttribute("codigoArmarioSelecionado").toString());
        } catch (Exception ex) {
        }
        String[] split = paramsTableFiltrada.split(",");
        String campoOrdenacao = split[0].replace("[", "");
        String ordem = split[1];
        String filtro = split[2].replace("''", "");
        filtro = filtro.replace("]", "");
        List listaParaImpressao = getFacade().getArmario().consultarParaImpressao(filtro, ordem, campoOrdenacao, getEmpresaLogado() == null ? 0 : getEmpresaLogado().getCodigo(), codigoArmarioSelecionado.toString());
        if (listaParaImpressao.size() > 0) {
            exportadorListaControle.exportar(evt, listaParaImpressao, filtro, null);
            oncomplete = "abrirPopup('UpdateServlet?op=downloadfile&file=" + exportadorListaControle.getFileName() + "&mimetype=application/" + extensao + "','Transacoes', 800,200);" + exportadorListaControle.getMsgAlert();
        } else {
            oncomplete = "alert('Nenhum resultado para exibir.')";
        }
    }

    public Integer getArmarioTrocaSelecionado() {
        return armarioTrocaSelecionado;
    }

    public void setArmarioTrocaSelecionado(Integer armarioTrocaSelecionado) {
        this.armarioTrocaSelecionado = armarioTrocaSelecionado;
    }

    public ArmarioVO getArmarioSelecionado() {
        if (armarioSelecionado == null)
            armarioSelecionado = new ArmarioVO();
        return armarioSelecionado;
    }

    public Boolean getHabilitarGestaoArmarios() throws Exception {
        if (habilitarGestaoArmarios == null)
            validarConfiguracoesSistema();
        return habilitarGestaoArmarios;
    }

    public void setHabilitarGestaoArmarios(Boolean habilitarGestaoArmarios) {
        this.habilitarGestaoArmarios = habilitarGestaoArmarios;
    }

    public void setArmarioSelecionado(ArmarioVO armarioSelecionado) {
        this.armarioSelecionado = armarioSelecionado;
    }

    public String getOncomplete() {
        return oncomplete;
    }

    public void setOncomplete(String oncomplete) {
        this.oncomplete = oncomplete;
    }


    public Boolean getFecharNegociacao() {

        return fecharNegociacao;
    }

    public void inicializarModeloContrato() throws Exception {
        List<PlanoTextoPadraoVO> modelos = getFacade().getPlanoTextoPadrao().consultarPorCodigoSituacaoTipo(0, "AT", "AM", false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        modelosContrato = new ArrayList<SelectItem>();
        for (PlanoTextoPadraoVO texto : modelos) {
            modelosContrato.add(new SelectItem(texto.getCodigo(), texto.getDescricao()));
        }
    }

    public String getValorConsultaArmario() {
        return valorConsultaArmario;
    }

    public void setValorConsultaArmario(String valorConsultaArmario) {
        this.valorConsultaArmario = valorConsultaArmario;
    }

    public Integer getModeloContrato() {
        return modeloContrato;
    }

    public void setModeloContrato(Integer modeloContrato) {
        this.modeloContrato = modeloContrato;
    }

    public List<SelectItem> getModelosContrato() throws Exception {
        if (modelosContrato == null)
            inicializarModeloContrato();
        return modelosContrato;
    }

    public List<TamanhoArmarioVO> getTamanhoSelecionados() {
        return tamanhoSelecionados;
    }

    public void setTamanhoSelecionados(List<TamanhoArmarioVO> tamanhoSelecionados) {
        this.tamanhoSelecionados = tamanhoSelecionados;
    }

    public void setModelosContrato(List<SelectItem> modelosContrato) {
        this.modelosContrato = modelosContrato;
    }

    public ArmarioVO getArmarioNegociar() {
        return armarioNegociar;
    }

    public void setArmarioNegociar(ArmarioVO armarioNegociar) {
        this.armarioNegociar = armarioNegociar;
    }

    public AluguelArmarioVO getAluguelNegociar() {
        return aluguelNegociar;
    }

    public void setAluguelNegociar(AluguelArmarioVO aluguelNegociar) {
        this.aluguelNegociar = aluguelNegociar;
    }

    public void setFecharNegociacao(Boolean fecharNegociacao) {
        this.fecharNegociacao = fecharNegociacao;
    }

    public List<FiltroArmarioTO> getFiltroItem() {
        if (filtroItem == null)
            filtroItem = new ArrayList<FiltroArmarioTO>();
        return filtroItem;
    }

    public void setFiltroItem(List<FiltroArmarioTO> filtroItem) {
        this.filtroItem = filtroItem;
    }

    public Boolean getApresentarConfirmarDevolucao() {
        return apresentarConfirmarDevolucao;
    }

    public void setApresentarConfirmarDevolucao(Boolean apresentarConfirmarDevolucao) {
        this.apresentarConfirmarDevolucao = apresentarConfirmarDevolucao;
    }

    public List<SelectItem> getGruposFiltro() {
        return gruposFiltro;
    }

    public void setGruposFiltro(List<SelectItem> gruposFiltro) {
        this.gruposFiltro = gruposFiltro;
    }
}
