package controle.nfe;

import br.com.pactosolucoes.comuns.util.Formatador;
import br.com.pactosolucoes.comuns.util.JSFUtilities;
import br.com.pactosolucoes.estrutura.exportador.controle.ExportadorListaControle;
import negocio.comuns.nfe.*;
import org.json.JSONArray;
import org.json.JSONObject;
import controle.arquitetura.SuperControle;
import negocio.comuns.arquitetura.UsuarioNFeVO;
import negocio.comuns.nfe.enumerador.ResultadoEnvioNFSeEnum;
import negocio.comuns.nfe.enumerador.StatusNotaEnum;
import negocio.comuns.nfe.enumerador.TipoNotaEnum;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.ControleConsulta;
import negocio.comuns.utilitarias.Ordenacao;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import servicos.propriedades.PropsService;
import servicos.util.ExecuteRequestHttpService;

import javax.faces.context.FacesContext;
import javax.faces.event.ActionEvent;
import javax.faces.model.SelectItem;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class NotaFiscalDeServicoControle extends SuperControle {

    private NotaFiscalDeServicoVO notaVO;
    private NotaFiscalDeServicoVO notaParaCancelar = new NotaFiscalDeServicoVO();
    private NotaFiscalDeServicoVO notaParaEnviarEmail = new NotaFiscalDeServicoVO();
    private List<NotaFiscalDeServicoVO> listaDeNotas = new ArrayList<NotaFiscalDeServicoVO>();
    private List<OperacaoNotaVO> historicoDaNota;
    private FiltroNFeTO filtro = new FiltroNFeTO();
    private int paginaAtual = 1;
    private int totalDePaginas = 0;
    private String paginacao = "";
    private int itensPorPagina = 20;
    private boolean primeiraPagina = false;
    private boolean voltarPagina = false;
    private boolean proximaPagina = false;
    private boolean ultimaPagina = false;
    private String motivo = "";
    private Double totalizadorAutorizado;
    private Double totalizadorNaoAutorizado;
    private Double totalizadorCancelado;
    private String ordenador = "";

    private Boolean marcarTodos = false;

    private String fileName = "";
    private NotaFiscalDeServicoVO notaReenviar;
    private List<LogOperacaoNotaVO> logOperacaoNota;

    private Integer qtdNotasAutorizado;
    private Integer qtdNotasNaoAutorizado;
    private Integer qtdNotasCancelado;


    private Double totalizadorCancelando;
    private Double totalizadorReenviado;
    private Double totalizadorProcessando;
    private Double totalizadorEnviando;

    private Integer qtdNotasCancelando;
    private Integer qtdNotasReenviado;
    private Integer qtdNotasProcessando;
    private Integer qtdNotasEnviando;

    private boolean permiteNaoAutorizar = false;
    private String listaRPSExcluir;
    private String listaLoteDesvincular;

    private List<NotaFiscalConsumidorNFCeVO> listaDeNotasNFCe;
    private NotaFiscalConsumidorNFCeVO notaNFCe;
    private NotaFiscalConsumidorNFCeVO nfceParaCancelar;

    //CANCELAR VARIAS NOTAS
    private String justificativaParaCancelarVariasNotas;
    private List<NotaFiscalDeServicoVO> notasParaCancelar;
    private List<NotaFiscalDeServicoVO> notasNaoPodeCancelar;
    private String onComplete;

    private List<NotaFiscalConsumidorNFCeVO> listaNFCEInutilizar;
    private List<NotaFiscalConsumidorNFCeVO> listaNFCEInutilizarNaoPode;
    private List<NumerosInutilizarVO> logNumerosInutilizar;

    private NotaFiscalConsumidorNFCeVO nfceReenviar;
    private String sequenciaRPSAtual;
    private String sequenciaLoteAtual;
    private String sequenciaRPS;
    private String sequenciaLote;

    private String serieInutilizarNFe;
    private String inicioInutilizarNFe;
    private String finalInutilizarNFe;
    private boolean permiteInutilizarNFe = false;
    /**
     * <b>TRUE</b> se o usuário acessou diretamente, ou, <b>FALSE</b> se o acesso foi pela tela de login.
     */
    private boolean usuarioAcessouDiretoModuloNotas = false;
    private boolean checkNaoMostrarAlertaAcessoDiretoModuloNotas;
    private List<SelectItem> listaCidades;
    private String siglaEstadoBuscar;
    private List<SelectItem> listaEmpresasCadastradas;


    public NotaFiscalDeServicoControle() throws Exception {
        obterUsuarioLogado();
        inicializarFacades();
        setControleConsulta(new ControleConsulta());
        setMensagemID("msg_entre_prmconsulta");
    }

    public List<SelectItem> getListaOrdenador() throws Exception {
        List<SelectItem> objs = new ArrayList<SelectItem>();
        objs.add(new SelectItem("", "Selecione uma ordenação"));
        objs.add(new SelectItem("SerieRPS", "Série"));
        objs.add(new SelectItem("NumeroNota", "Número Nota"));
        objs.add(new SelectItem("RazaoSocialCons", "Razão Social"));
        objs.add(new SelectItem("DataEmissao", "Data Emissão"));
        objs.add(new SelectItem("ValorTotal", "Valor RPS"));
        objs.add(new SelectItem("Status", "Status"));
        objs.add(new SelectItem("DataProcessamento", "Data Processamento"));

        return objs;
    }

    public void irPaginaInicial() throws Exception {
        paginaAtual = 1;
        this.consultar();
    }

    public void irPaginaAnterior() throws Exception {
        paginaAtual--;
        this.consultar();
    }

    public void irPaginaPosterior() throws Exception {
        paginaAtual++;
        this.consultar();
    }

    public void irPaginaFinal() throws Exception {
        paginaAtual = totalDePaginas;
        this.consultar();
    }

    public String inicializarConsultar() {
        setPaginaAtualDeTodas("0/0");
        setListaConsulta(new ArrayList());
        definirVisibilidadeLinksNavegacao(0, 0);
        setMensagemID("msg_entre_prmconsulta");
        setSucesso(false);
        setErro(false);
        return "consultarNotas";
    }

    protected boolean inicializarFacades() {
        try {
            super.inicializarFacades();
            return true;
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro_conectarBD", e.getMessage());
            return false;
        }
    }

    public void getNrTotalPaginas() {
        Double total;
        if (getFiltro().getTipoNota().equals(TipoNotaEnum.NFCE.getCodigo())) {
            total = (double) getListaDeNotasNFCe().size();
        } else {
            total = (double) getListaDeNotas().size();
        }

        double nrPaginasDouble = Math.ceil(((double) total / itensPorPagina));
        String nrTotalPaginas = String.valueOf(nrPaginasDouble);
        nrTotalPaginas = nrTotalPaginas.substring(0, nrTotalPaginas.indexOf("."));
        totalDePaginas = (Integer.parseInt(nrTotalPaginas));
    }

    void definirLinksDaPaginacao() {
        setPaginacao(paginaAtual + "/" + totalDePaginas);

        if (paginaAtual == 1 && totalDePaginas > 1) {
            setPrimeiraPagina(false);
            setVoltarPagina(false);
            setProximaPagina(true);
            setUltimaPagina(true);
        } else if (paginaAtual == 1 && totalDePaginas == 1) {
            setPrimeiraPagina(false);
            setVoltarPagina(false);
            setProximaPagina(false);
            setUltimaPagina(false);
        } else if (paginaAtual > 1 && paginaAtual < totalDePaginas) {
            setPrimeiraPagina(true);
            setVoltarPagina(true);
            setProximaPagina(true);
            setUltimaPagina(true);
        } else if (paginaAtual == totalDePaginas && paginaAtual != 0) {
            setPrimeiraPagina(true);
            setVoltarPagina(true);
            setProximaPagina(false);
            setUltimaPagina(false);
        } else if (totalDePaginas == 0) {
//            setPaginaAtualDeTodas("0/0");
            setPrimeiraPagina(false);
            setVoltarPagina(false);
            setProximaPagina(false);
            setUltimaPagina(false);
        }
    }

    private void totalizarNotas() {
        Double totalAutorizado = 0.0;
        Double totalNaoAutorizado = 0.0;
        Double totalCancelado = 0.0;
        Double totalCancelando = 0.0;
        Double totalEnviando = 0.0;
        Double totalProcessando = 0.0;
        Double totalReenviado = 0.0;

        Integer qtdAutorizada = 0;
        Integer qtdNaoAutorizado = 0;
        Integer qtdCancelado = 0;
        Integer qtdCancelando = 0;
        Integer qtdEnviando = 0;
        Integer qtdProcessando = 0;
        Integer qtdReenviado = 0;

        if (getFiltro().getTipoNota().equals(TipoNotaEnum.NFSE.getCodigo())) { //NFCe

            for (NotaFiscalDeServicoVO nota : getListaDeNotas()) {
                if (nota.getStatus().equals(StatusNotaEnum.AUTORIZADO.getDescricao())) {
                    qtdAutorizada += 1;
                    totalAutorizado += nota.getValorServicos();
                    continue;
                }
                if (nota.getStatus().equals(StatusNotaEnum.NAO_AUTORIZADO.getDescricao())) {
                    qtdNaoAutorizado += 1;
                    totalNaoAutorizado += nota.getValorServicos();
                    continue;
                }
                if (nota.getStatus().equals(StatusNotaEnum.CANCELADO.getDescricao())) {
                    qtdCancelado += 1;
                    totalCancelado += nota.getValorServicos();
                    continue;
                }
                if (nota.getStatus().equals(StatusNotaEnum.CANCELANDO.getDescricao())) {
                    qtdCancelando += 1;
                    totalCancelando += nota.getValorServicos();
                    continue;
                }
                if (nota.getStatus().equals(StatusNotaEnum.ENVIANDO.getDescricao())) {
                    qtdEnviando += 1;
                    totalEnviando += nota.getValorServicos();
                    continue;
                }
                if (nota.getStatus().equals(StatusNotaEnum.PROCESSANDO.getDescricao())) {
                    qtdProcessando += 1;
                    totalProcessando += nota.getValorServicos();
                    continue;
                }
                if (nota.getStatus().equals(StatusNotaEnum.REENVIADO.getDescricao())) {
                    qtdReenviado += 1;
                    totalReenviado += nota.getValorServicos();
                    continue;
                }
            }

        } else if (getFiltro().getTipoNota().equals(TipoNotaEnum.NFCE.getCodigo())) { //NFCe

            for (NotaFiscalConsumidorNFCeVO nota : getListaDeNotasNFCe()) {
                if (nota.getStatus().equals(StatusNotaEnum.AUTORIZADO.getDescricao())) {
                    qtdAutorizada += 1;
                    totalAutorizado += nota.getValorTotal();
                    continue;
                }
                if (nota.getStatus().equals(StatusNotaEnum.NAO_AUTORIZADO.getDescricao())) {
                    qtdNaoAutorizado += 1;
                    totalNaoAutorizado += nota.getValorTotal();
                    continue;
                }
                if (nota.getStatus().equals(StatusNotaEnum.CANCELADO.getDescricao())) {
                    qtdCancelado += 1;
                    totalCancelado += nota.getValorTotal();
                    continue;
                }
                if (nota.getStatus().equals(StatusNotaEnum.CANCELANDO.getDescricao())) {
                    qtdCancelando += 1;
                    totalCancelando += nota.getValorTotal();
                    continue;
                }
                if (nota.getStatus().equals(StatusNotaEnum.ENVIANDO.getDescricao())) {
                    qtdEnviando += 1;
                    totalEnviando += nota.getValorTotal();
                    continue;
                }
                if (nota.getStatus().equals(StatusNotaEnum.PROCESSANDO.getDescricao())) {
                    qtdProcessando += 1;
                    totalProcessando += nota.getValorTotal();
                    continue;
                }
                if (nota.getStatus().equals(StatusNotaEnum.REENVIADO.getDescricao())) {
                    qtdReenviado += 1;
                    totalReenviado += nota.getValorTotal();
                    continue;
                }
            }
        }

        setTotalizadorAutorizado(totalAutorizado);
        setTotalizadorNaoAutorizado(totalNaoAutorizado);
        setTotalizadorCancelado(totalCancelado);
        setTotalizadorCancelando(totalCancelando);
        setTotalizadorEnviando(totalEnviando);
        setTotalizadorProcessando(totalProcessando);
        setTotalizadorReenviado(totalReenviado);

        setQtdNotasAutorizado(qtdAutorizada);
        setQtdNotasNaoAutorizado(qtdNaoAutorizado);
        setQtdNotasCancelado(qtdCancelado);
        setQtdNotasCancelando(qtdCancelando);
        setQtdNotasEnviando(qtdEnviando);
        setQtdNotasProcessando(qtdProcessando);
        setQtdNotasReenviado(qtdReenviado);
    }

    public String consultar() {
        try {

            boolean buscandoNCFe = getFiltro().getTipoNota().equals(TipoNotaEnum.NFCE.getCodigo());
            setPermiteInutilizarNFe(getFacade().getNotaNFSe().empresaNFE(obterIdEmpresaLogadaSelecionada()));

            UsuarioNFeVO usuarioNFeVO = (UsuarioNFeVO) getUsuarioLogado();
            Integer idEmpresa;

            if (usuarioNFeVO.isAdministrador()) {
                idEmpresa = getFiltro().getIdEmpresa();
            } else {
                idEmpresa = usuarioNFeVO.getPerfilUsuarioNFe().getEmpresa().getId_Empresa();
            }

            if (buscandoNCFe) {
                setListaDeNotasNFCe(getFacade().getNotaNFSe().listarNFCe(idEmpresa, getFiltro(), getOrdenador()));
            } else {
                setListaDeNotas(getFacade().getNotaNFSe().listarNotas(idEmpresa, getFiltro(), getOrdenador()));
            }

            getNrTotalPaginas();
            totalizarNotas();

            if (buscandoNCFe) {
                List<NotaFiscalConsumidorNFCeVO> listaApresentar = getListaDeNotasNFCe(paginaAtual);
                setListaConsulta(listaApresentar);
            } else {
                List<NotaFiscalDeServicoVO> listaApresentar = getListaDeNotas(paginaAtual);
                if (getMarcarTodos()) {
                    for (NotaFiscalDeServicoVO nfseVO : listaApresentar) {
                        nfseVO.setMarcado(true);
                    }
                }
                setListaConsulta(listaApresentar);
            }

            definirLinksDaPaginacao();

            setMensagemID("msg_dados_consultados");
            setSucesso(true);
            setErro(false);
            return "consultarNotas";
        } catch (Exception e) {
            setTotalizadorCancelado(0.0);
            setTotalizadorAutorizado(0.0);
            setTotalizadorNaoAutorizado(0.0);
            setQtdNotasAutorizado(0);
            setQtdNotasNaoAutorizado(0);
            setQtdNotasCancelado(0);
            setListaConsulta(new ArrayList());
            setMensagemDetalhada("msg_erro", e.getMessage());
            setSucesso(false);
            setErro(true);
            return "consultarNotas";
        }
    }

    public NotaFiscalDeServicoVO visualizarNota() throws Exception {
        NotaFiscalDeServicoVO obj = (NotaFiscalDeServicoVO) context().getExternalContext().getRequestMap().get("nota");
        setNotaVO(obj);

        ItemNFSeControle itemControle = (ItemNFSeControle) getControlador(ItemNFSeControle.class.getSimpleName());
        itemControle.getItensDaNota();

        return obj;
    }

    public String getPreVisualizar() {
        return (notaVO == null) ? "none" : "block";
    }

    public void atualizarNota() throws Exception {
        NotaFiscalDeServicoVO obj = (NotaFiscalDeServicoVO) context().getExternalContext().getRequestMap().get("nota");
        NotaFiscalDeServicoVO objAtualizado = getFacade().getNotaNFSe().consultarPorChavePrimaria(obj.getIdRPS(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        atualizarLista(obj, objAtualizado);
    }

    public void cancelarNota() throws Exception {
        int idUsuario = ((UsuarioNFeVO) getUsuarioLogado()).getId_Usuario();

        getFacade().getNotaNFSe().cancelarNota(getNotaParaCancelar(), idUsuario);

        NotaFiscalDeServicoVO notaAntiga = getNotaParaCancelar();
        NotaFiscalDeServicoVO notaNova = getNotaParaCancelar();

        notaNova = getFacade().getNotaNFSe().alterarStatusDaNota(notaNova, StatusNotaEnum.CANCELANDO.getDescricao());

        atualizarLista(notaAntiga, notaNova);
    }

    public void cancelarNotaNFCe() throws Exception {
        int idUsuario = ((UsuarioNFeVO) getUsuarioLogado()).getId_Usuario();

        getFacade().getNotaNFSe().cancelarNotaNFCe(getNfceParaCancelar(), idUsuario);

        NotaFiscalConsumidorNFCeVO notaAntiga = getNfceParaCancelar();
        NotaFiscalConsumidorNFCeVO notaNova = getNfceParaCancelar();
        notaNova.setStatus(StatusNotaEnum.CANCELANDO.getDescricao());
        atualizarListaNFCe(notaAntiga, notaNova);
    }

    private void atualizarLista(NotaFiscalDeServicoVO notaAnterior, NotaFiscalDeServicoVO notaNova) {
        int posicao = getListaConsulta().indexOf(notaAnterior);
        getListaConsulta().remove(notaAnterior);
        getListaConsulta().add(posicao, notaNova);
    }

    public void prepararNotaParaCancelar(ActionEvent event) {
        NotaFiscalDeServicoVO obj = (NotaFiscalDeServicoVO) context().getExternalContext().getRequestMap().get("nota");
        setNotaParaCancelar(obj);
    }

    public void prepararNFCeParaCancelar(ActionEvent event) {
        NotaFiscalConsumidorNFCeVO obj = (NotaFiscalConsumidorNFCeVO) context().getExternalContext().getRequestMap().get("nota");
        setNfceParaCancelar(obj);
    }

    public void prepararNotaParaEnvialEmail(ActionEvent event) {
        setNotaParaEnviarEmail((NotaFiscalDeServicoVO) context().getExternalContext().getRequestMap().get("nota"));
        getNotaParaEnviarEmail().setEmailConsParaEnvio(getNotaParaEnviarEmail().getEmailCons());
    }

    public void prepararMotivoDaNota(ActionEvent event) {
        setMotivo(((NotaFiscalDeServicoVO) context().getExternalContext().getRequestMap().get("nota")).getMotivo());
    }

    public void enviarEmail() throws Exception {
        NotaFiscalDeServicoVO obj = getNotaParaEnviarEmail();

        int idUsuario = ((UsuarioNFeVO) getUsuarioLogado()).getId_Usuario();

        getFacade().getNotaNFSe().enviarEmail(obj, idUsuario, "");
    }

    public FiltroNFeTO filtrar() {
        try {
            if (!getUsuarioLogado().getAdministrador()) {
                validarDatas();
            }
            paginaAtual = 1;
            notaVO = null;
            setMarcarTodos(false);
            consultar();
            return getFiltro();
        } catch (Exception e) {
            setErro(true);
            setSucesso(false);
            setMensagemDetalhada(e);
            return getFiltro();
        }
    }

    private void validarDatas() throws Exception {

        boolean nenhumCampoPreenchido = UteisValidacao.emptyString(getFiltro().getId_Lote()) &&
                UteisValidacao.emptyString(getFiltro().getId_NFCe()) &&
                UteisValidacao.emptyNumber(getFiltro().getNumeroRps()) &&
                UteisValidacao.emptyString(getFiltro().getNumeroDaNota()) &&
                UteisValidacao.emptyString(getFiltro().getCodigoVerificacao()) &&
                UteisValidacao.emptyString(getFiltro().getCpf_cnpj()) &&
                UteisValidacao.emptyString(getFiltro().getRazaoSocial());

        if(nenhumCampoPreenchido) {
            if (getFiltro().getDataEmissao_inicio() == null && getFiltro().getDataEmissao_fim() == null &&
                    getFiltro().getDataProcessamento_inicio() == null && getFiltro().getDataProcessamento_fim() == null) {
                throw new Exception("Informe um período de Emissão ou de Processamento das notas.");
            } else if (getFiltro().getDataEmissao_inicio() != null && getFiltro().getDataEmissao_fim() == null) {
                throw new Exception("Informe a Data Final de Emissão.");
            } else if (getFiltro().getDataEmissao_inicio() == null && getFiltro().getDataEmissao_fim() != null) {
                throw new Exception("Informe a Data Inicial de Emissão.");
            } else if (getFiltro().getDataEmissao_inicio() != null && getFiltro().getDataEmissao_fim() != null) {
                String mesInicial = Uteis.getMesReferencia(getFiltro().getDataEmissao_inicio());
                String mesFinal = Uteis.getMesReferencia(getFiltro().getDataEmissao_fim());

                if (!mesInicial.equals(mesFinal)) {
                    throw new Exception("O período de emissão não pode ser superior a um mês.");
                }

            } else if (getFiltro().getDataProcessamento_inicio() != null && getFiltro().getDataProcessamento_fim() == null) {
                throw new Exception("Informe a Data Final de Processamento.");
            } else if (getFiltro().getDataProcessamento_inicio() == null && getFiltro().getDataProcessamento_fim() != null) {
                throw new Exception("Informe a Data Inicial de Processamento.");
            } else if (getFiltro().getDataProcessamento_inicio() != null && getFiltro().getDataProcessamento_fim() != null) {
                String mesInicial = Uteis.getMesReferencia(getFiltro().getDataProcessamento_inicio());
                String mesFinal = Uteis.getMesReferencia(getFiltro().getDataProcessamento_fim());
                if (!mesInicial.equals(mesFinal)) {
                    throw new Exception("O período de Processamento não pode ser superior a um mês.");
                }
            }
        }
    }

    public FiltroNFeTO limparFiltro() {
        limparMsg();
        paginaAtual = 1;
        notaVO = null;

        Integer tipoFiltro = getFiltro().getTipoNota();

        if (getFiltro().getIdEmpresa() != null && getFiltro().getIdEmpresa() != 0) {
            setFiltro(new FiltroNFeTO(getFiltro().getIdEmpresa()));
            getFiltro().setTipoNota(tipoFiltro);
        } else {
            setFiltro(new FiltroNFeTO());
            getFiltro().setTipoNota(tipoFiltro);
        }

        consultar();
        return getFiltro();
    }

    public List<NotaFiscalDeServicoVO> getListaDeNotas() {
        return listaDeNotas;
    }

    public void setListaDeNotas(List<NotaFiscalDeServicoVO> listaDeNotas) {
        this.listaDeNotas = listaDeNotas;
    }

    public List<NotaFiscalDeServicoVO> getListaDeNotas(int pagina) {
        int inicio = (itensPorPagina * (pagina - 1));
        int fim = ((pagina * itensPorPagina));

        if (inicio < 0) {
            inicio = 0;
        }

        if (fim > getListaDeNotas().size()) {
            fim = (Integer) getTotalDeNotas();
        }

        return listaDeNotas.subList(inicio, fim);
    }

    public NotaFiscalDeServicoVO getNotaParaCancelar() {
        return notaParaCancelar;
    }

    public void setNotaParaCancelar(NotaFiscalDeServicoVO notaParaCancelar) {
        this.notaParaCancelar = notaParaCancelar;
    }

    public NotaFiscalDeServicoVO getNotaVO() {
        return notaVO;
    }

    public void setNotaVO(NotaFiscalDeServicoVO notaVO) {
        this.notaVO = notaVO;
    }

    public FiltroNFeTO getFiltro() {
        if (filtro == null) {
            filtro = new FiltroNFeTO();
        }
        return filtro;
    }

    public void setFiltro(FiltroNFeTO filtro) {
        this.filtro = filtro;
    }

    public List getTipoStatusCombo() {
        List<SelectItem> itens = new ArrayList<SelectItem>();
        for (StatusNotaEnum status : StatusNotaEnum.values()) {
            itens.add(new SelectItem(status.getDescricao(), status.getDescricao()));
        }
        Ordenacao.ordenarLista(itens, "label");
        return itens;
    }

    public NotaFiscalDeServicoVO getNotaParaEnviarEmail() {
        return notaParaEnviarEmail;
    }

    public void setNotaParaEnviarEmail(NotaFiscalDeServicoVO notaParaEnviarEmail) {
        this.notaParaEnviarEmail = notaParaEnviarEmail;
    }

    public Object getTotalDeNotas() {
        if (getFiltro().getTipoNota().equals(TipoNotaEnum.NFSE.getCodigo())) { //NFSE
            return getListaDeNotas().size();
        } else if (getFiltro().getTipoNota().equals(TipoNotaEnum.NFCE.getCodigo())) { //NFCE
            return getListaDeNotasNFCe().size();
        }
        return 0;
    }

    public void fecharPreVisualizacao() {
        notaVO = null;
    }

    public void monteHistoricoDaNota(ActionEvent event) throws Exception {
        NotaFiscalDeServicoVO obj = (NotaFiscalDeServicoVO) context().getExternalContext().getRequestMap().get("nota");
        setHistoricoDaNota(getFacade().getNotaNFSe().historicoDaNota(obj));
    }

    public List<OperacaoNotaVO> getHistoricoDaNota() {
        return historicoDaNota;
    }

    public void setHistoricoDaNota(List<OperacaoNotaVO> historicoDaNota) {
        this.historicoDaNota = historicoDaNota;
    }

    public void downloadXML(ActionEvent event) throws Exception {
        download("xml", false, false);
    }

    public void downloadPDF(ActionEvent event) throws Exception {
        download("pdf", false, false);
    }

    public void downloadXMLNFCe(ActionEvent event) throws Exception {
        download("xml", true, false);
    }

    public void downloadPDFNFCe(ActionEvent event) throws Exception {
        download("pdf", true, false);
    }

    public void downloadXMLNFSeCancelamento(ActionEvent event) throws Exception {
        download("xml", false, true);
    }

    public void downloadXMLNFCeCancelamento(ActionEvent event) throws Exception {
        download("xml", true, true);
    }

    private void download(String extensao, boolean nfce, boolean xmlCancelamento) throws Exception {
        setMensagemDetalhada("");
        Integer codigoReferencia = 0;
        Date dataReferencia = null;
        NotaFiscalConsumidorNFCeVO objNFCe = new NotaFiscalConsumidorNFCeVO();
        NotaFiscalDeServicoVO objNFe = new NotaFiscalDeServicoVO();
        boolean xmlPrefeitura = false;
        boolean xmlManipulado = false;
        boolean xmlManipuladoNFCe = false;
        if (nfce) {
            objNFCe = (NotaFiscalConsumidorNFCeVO) context().getExternalContext().getRequestMap().get("nota");
            codigoReferencia = objNFCe.getId_NFCe();
            dataReferencia = objNFCe.getDataHoraEmissao();
            xmlManipuladoNFCe = getFacade().getNotaNFSe().empresaUsaXMLManipuladoNFCe(codigoReferencia, null);
        } else {
            objNFe = (NotaFiscalDeServicoVO) context().getExternalContext().getRequestMap().get("nota");
            codigoReferencia = objNFe.getIdRPS();
            dataReferencia = objNFe.getDataProcessamento();
            xmlPrefeitura = getFacade().getNotaNFSe().empresaUsaXMLPrefeitura(codigoReferencia, null);
            xmlManipulado = getFacade().getNotaNFSe().empresaUsaXMLManipulado(codigoReferencia, null);
        }

        HttpServletResponse res = (HttpServletResponse) context().getExternalContext().getResponse();
        ServletOutputStream out = res.getOutputStream();

        byte[] b = getFacade().getNotaNFSe().obtenhaArquivoNota(extensao, codigoReferencia, dataReferencia, nfce, xmlPrefeitura, xmlManipulado, this.getServletContext().getRealPath("relatorio"), xmlCancelamento, xmlManipuladoNFCe);

//        if (!nfce && extensao.equals("xml") && b == null) {
//            b = getFacade().getNotaNFSe().obtenhaArquivoDaNota(objNFe, "XML NF");
//        }

        if (b != null && b.length > 0) {
            //tipo layout da cidade = 3 é por ser NFe;
            if (nfce) {
                res.setHeader("Content-disposition", "attachment;filename=\"" + codigoReferencia + " - NFCe." + extensao + "\"");
            } else {
                if (objNFe.getCidadePrest().getTipoLayout() == 3) {
                    if (UteisValidacao.emptyString(objNFe.getCodigoVerificacao())) {
                        res.setHeader("Content-disposition", "attachment;filename=\"" + objNFe.getNumeroRPS() + "-procNFe." + extensao + "\"");
                    } else {
                        res.setHeader("Content-disposition", "attachment;filename=\"" + objNFe.getCodigoVerificacao() + "-procNFe." + extensao + "\"");
                    }
                } else {
                    res.setHeader("Content-disposition", "attachment;filename=\"" + objNFe.getIdRPS() + " - " + objNFe.getNumeroNota() + "." + extensao + "\"");
                }
            }
            res.setContentLength(b.length);
            res.setContentType("application/octet-stream");

            out.write(b);
            out.flush();
            out.close();

            FacesContext.getCurrentInstance().responseComplete();
        } else {
            setMensagemDetalhada("Arquivo não encontrado.");
        }
    }

    public String marcarTodosItens() {
        if (getFiltro().getTipoNota().equals(TipoNotaEnum.NFSE.getCodigo())) { //NFSe
            for (NotaFiscalDeServicoVO notaVO : listaDeNotas) {
                notaVO.setMarcado(getMarcarTodos());
            }
        } else if (getFiltro().getTipoNota().equals(TipoNotaEnum.NFCE.getCodigo())) { //NFCe
            for (NotaFiscalConsumidorNFCeVO notaVO : listaDeNotasNFCe) {
                notaVO.setMarcado(getMarcarTodos());
            }
        }
        return "";
    }

    private void downloadArquivosMarcados(String tipoArquivo, String extensao) {
        try {
            List<byte[]> arquivos = new ArrayList<byte[]>();
            List<String> nomes = new ArrayList<String>();
            setFileName("");
            boolean gerou;

            if (getFiltro().getTipoNota().equals(TipoNotaEnum.NFSE.getCodigo())) { //NFSe
                boolean xmlPrefeitura = getFacade().getNotaNFSe().empresaUsaXMLPrefeitura(null, obterIdEmpresaLogadaSelecionada());
                boolean xmlManipulado = getFacade().getNotaNFSe().empresaUsaXMLManipulado(null, obterIdEmpresaLogadaSelecionada());
                for (NotaFiscalDeServicoVO obj : listaDeNotas) {
                    if (obj.isMarcado()) {
                        byte[] b = getFacade().getNotaNFSe().obtenhaArquivoNota(extensao, obj.getIdRPS(), obj.getDataProcessamento(), false, xmlPrefeitura, xmlManipulado, this.getServletContext().getRealPath("relatorio"), obj.getNotaEstaCancelada(), false);
                        if (b != null && b.length > 0) {
                            arquivos.add(b);
                            //tipo layout da cidade = 3 é por ser NFe;
                            String cancel ="";
                            if (obj.getNotaEstaCancelada()) {
                                cancel = "-CANCELADA";
                            }
                            if (obj.getCidadePrest().getTipoLayout() == 3) {
                                if (UteisValidacao.emptyString(obj.getCodigoVerificacao())) {
                                    nomes.add("IDRPS_"+ obj.getIdRPS() + cancel + "-procNFe." + extensao);
                                } else {
                                    nomes.add(obj.getCodigoVerificacao() + cancel + "-procNFe." + extensao);
                                }

                            } else {
                                nomes.add(obj.getIdRPS() + " - " + obj.getNumeroNota() + cancel + "." + extensao);
                            }
                        }
                    }
                }
            } else if (getFiltro().getTipoNota().equals(TipoNotaEnum.NFCE.getCodigo())) { //NFCe
                for (NotaFiscalConsumidorNFCeVO obj : listaDeNotasNFCe) {
                    if (obj.isMarcado()) {
                        Integer codigoReferencia = obj.getId_NFCe();
                        Date dataReferencia = obj.getDataHoraEmissao();
                        byte[] b = getFacade().getNotaNFSe().obtenhaArquivoNota(extensao, codigoReferencia, dataReferencia, true, false, false, "", false, false);
                        if (b != null && b.length > 0) {
                            arquivos.add(b);
                            nomes.add(obj.getChave() + "-procNFe." + extensao);
                        }
                    }
                }
            }

            if (arquivos.size() > 0) {
                String absolutePath = this.getServletContext().getRealPath("relatorio") + File.separator;
                String nomeArquivo = extensao.toUpperCase() + System.currentTimeMillis() + ".zip";

                String nomeArquivoGerado = absolutePath + nomeArquivo;
                gerou = Uteis.zip(nomeArquivoGerado, arquivos, nomes);
                if (gerou) {
                    setFileName(nomeArquivo);
                }
            } else {
                throw new ConsistirException("Não foi selecionado nenhuma nota.");
            }
        } catch (Exception ex) {
            setMensagemDetalhada(ex);
        }
    }

    public String downloadXMLArquivosMarcados(ActionEvent event) throws Exception {
        downloadArquivosMarcados("XML NF", "xml");
        return "";
    }

    public String downloadPDFArquivosMarcados() throws Exception {
        if (getFiltro().getTipoNota().equals(TipoNotaEnum.NFSE.getCodigo())) {
            downloadPDFNFSeArquivosMarcados();
        } else if (getFiltro().getTipoNota().equals(TipoNotaEnum.NFCE.getCodigo())) {
            downloadPDFNFCeArquivosMarcados();
        }
        return "";
    }

    public List getListaSelectEmpresasCadastradas() throws Exception {
        if (UteisValidacao.emptyList(getListaEmpresasCadastradas())) {
            setListaEmpresasCadastradas(getFacade().getEmpresaNFe().obtenhaEmpresasCadastradas());
        }

        return getListaEmpresasCadastradas();
    }

    public boolean isPrimeiraPagina() {
        return primeiraPagina;
    }

    public void setPrimeiraPagina(boolean primeiraPagina) {
        this.primeiraPagina = primeiraPagina;
    }

    public boolean isVoltarPagina() {
        return voltarPagina;
    }

    public void setVoltarPagina(boolean voltarPagina) {
        this.voltarPagina = voltarPagina;
    }

    public boolean isProximaPagina() {
        return proximaPagina;
    }

    public void setProximaPagina(boolean proximaPagina) {
        this.proximaPagina = proximaPagina;
    }

    public boolean isUltimaPagina() {
        return ultimaPagina;
    }

    public void setUltimaPagina(boolean ultimaPagina) {
        this.ultimaPagina = ultimaPagina;
    }

    public String getPaginacao() {
        return paginacao;
    }

    public void setPaginacao(String paginacao) {
        this.paginacao = paginacao;
    }

    public String getMotivo() {
        return motivo;
    }

    public void setMotivo(String motivo) {
        this.motivo = motivo;
    }

    public Double getTotalizadorAutorizado() {
        if (totalizadorAutorizado == null) {
            totalizadorAutorizado = 0.0;
        }
        return totalizadorAutorizado;
    }

    public void setTotalizadorAutorizado(Double totalizadorAutorizado) {
        this.totalizadorAutorizado = totalizadorAutorizado;
    }

    public String getTotalizadorAutorizado_Apresentar() {
        return Formatador.formatarValorMonetario(totalizadorAutorizado);
    }

    public Double getTotalizadorNaoAutorizado() {
        if (totalizadorNaoAutorizado == null) {
            totalizadorNaoAutorizado = 0.0;
        }
        return totalizadorNaoAutorizado;
    }

    public void setTotalizadorNaoAutorizado(Double totalizadorNaoAutorizado) {
        this.totalizadorNaoAutorizado = totalizadorNaoAutorizado;
    }

    public String getTotalizadorNaoAutorizado_Apresentar() {
        return Formatador.formatarValorMonetario(totalizadorNaoAutorizado);
    }

    public Double getTotalizadorCancelado() {
        if (totalizadorCancelado == null) {
            totalizadorCancelado = 0.0;
        }
        return totalizadorCancelado;
    }

    public void setTotalizadorCancelado(Double totalizadorCancelado) {
        this.totalizadorCancelado = totalizadorCancelado;
    }

    public String getTotalizadorCancelado_Apresentar() {
        return Formatador.formatarValorMonetario(totalizadorCancelado);
    }

    public String getOrdenador() {
        if(ordenador == null){
            ordenador = "";
        }
        return ordenador;
    }

    public void setOrdenador(String ordenador) {
        this.ordenador = ordenador;
    }

    public Boolean getMarcarTodos() {
        return marcarTodos;
    }

    public void setMarcarTodos(Boolean marcarTodos) {
        this.marcarTodos = marcarTodos;
    }

    public String getFileName() {
        return fileName;
    }

    public void setFileName(String fileName) {
        this.fileName = fileName;
    }

    public void exportarExcel(ActionEvent evt) {
        if (getFiltro().getTipoNota().equals(TipoNotaEnum.NFSE.getCodigo()) && UteisValidacao.emptyList(listaDeNotas)) {// NFSe
            montarMsgAlert("Arquivo não foi gerado devido ao Erro: ");
            return;
        } else if (getFiltro().getTipoNota().equals(TipoNotaEnum.NFCE.getCodigo()) && UteisValidacao.emptyList(listaDeNotasNFCe)) {// NFCe
            montarMsgAlert("Arquivo não foi gerado devido ao Erro: ");
            return;
        }

        List ordenarLista = null;
        if (getFiltro().getTipoNota().equals(TipoNotaEnum.NFSE.getCodigo())) {// NFSe
            ordenarLista = new ArrayList(listaDeNotas);
            ordenarLista = Ordenacao.ordenarLista(ordenarLista, "dataEmissao");
        } else if (getFiltro().getTipoNota().equals(TipoNotaEnum.NFCE.getCodigo())) {// NFCe
            ordenarLista = new ArrayList(listaDeNotasNFCe);
            ordenarLista = Ordenacao.ordenarLista(ordenarLista, "dataHoraEmissao");
        }

        ExportadorListaControle exportadorListaControle = (ExportadorListaControle) JSFUtilities.getFromRequest(ExportadorListaControle.class.getSimpleName());
        Collections.reverse(ordenarLista);
        exportadorListaControle.exportar(evt, ordenarLista, "", null);
    }


    public NotaFiscalDeServicoVO getNotaReenviar() {
        if (notaReenviar == null) {
            notaReenviar = new NotaFiscalDeServicoVO();
        }
        return notaReenviar;
    }

    public void setNotaReenviar(NotaFiscalDeServicoVO notaReenviar) {
        this.notaReenviar = notaReenviar;
    }


    public void prepararReenvioNfse() {
        NotaFiscalDeServicoVO obj = (NotaFiscalDeServicoVO) context().getExternalContext().getRequestMap().get("nota");
        setNotaReenviar(obj);
        getNotaReenviar().setDataEmissaoReenvio(Calendario.hoje());

        try {
            Map<String, String> headers = new HashMap<String, String>();
            headers.put("Content-Type", "application/x-www-form-urlencoded");

            String urlConsultar = "";
            String urlBase = getNotaReenviar().getLote().getUrlConfirmacao();

            if (!UteisValidacao.emptyString(urlBase)) {
                urlConsultar = urlBase.replace("idLote=", "obterCliente=true");
            }

            String dadosAlunos = ExecuteRequestHttpService.executeHttpRequestGenerico(urlConsultar, headers, null, ExecuteRequestHttpService.METODO_POST, "ISO-8859-1", "ISO-8859-1");

            JSONObject jsonRetornoDadosAluno = new JSONObject(dadosAlunos);
            JSONObject jsonDadosAluno = new JSONObject(jsonRetornoDadosAluno.getString("return"));

            getNotaReenviar().atualizarDadosReenvio(jsonDadosAluno);
        } catch (Exception ignored) {
        }
        try {
            EmpresaNFeVO ep = getFacade().getEmpresaNFe().consultarPorChavePrimaria(obterIdEmpresaLogadaSelecionada());
            if (!UteisValidacao.emptyString(ep.getParams())) {
                JSONArray arrayParams = new JSONArray(ep.getParams());
                JSONObject jsonNFSe = null;

                for (int i = 0; i < arrayParams.length(); i++) {
                    JSONObject nfse = arrayParams.getJSONObject(i).optJSONObject("nfse");
                    if (nfse != null) {
                        jsonNFSe = nfse;
                        break;
                    }
                }
                if (jsonNFSe != null) {
                    getNotaReenviar().atualizarDadosReenvio(jsonNFSe);
                }
            }
            if (ep.isOptateSimplesNacional()) {
                Double aliquotaSimples = ep.getAliquotaSimples() / 100;
                Double valorISS = getNotaReenviar().getValorTotal() * aliquotaSimples;

                getNotaReenviar().setAliquotaAtividade(aliquotaSimples);
                getNotaReenviar().setValorISSRetido(valorISS);
                getNotaReenviar().setAlterouAliquotaSimples(true);
            }
        } catch (Exception ignored) {
        }
    }


    public void reenviarNfse() {
        try {
            if (Calendario.maior(getNotaReenviar().getDataEmissaoReenvio(), Calendario.hoje())) {
                throw new ConsistirException("A DATA DE EMISSÃO não pode superior a data atual!");
            }
            JSONArray jsonArray = lancarCamposAlterados();
            if (jsonArray.length() > 0) {
                JSONObject jsonObject = new JSONObject();
                jsonObject.put("idRPS", getNotaReenviar().getIdRPS());
                jsonObject.put("alteracoes", jsonArray);
                RetornoEnvioNotaFiscalTO retornoEnvioNotaFiscalTO = getFacade().getLoteNFSe().reenviarRPS(jsonObject, getNotaReenviar().getIdRPS());
                if (!retornoEnvioNotaFiscalTO.getResultadoEnvio().equals(ResultadoEnvioNFSeEnum.OK)) {
                    throw new ConsistirException("Ocorreu um erro ao reeviar a nota!");
                }
                NotaFiscalDeServicoVO notaAntiga = getNotaReenviar();
                NotaFiscalDeServicoVO notaAtualizada = getFacade().getNotaNFSe().consultarPorChavePrimaria(getNotaReenviar().getIdRPS(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                atualizarLista(notaAntiga, notaAtualizada);
                inserirLogNotaReenviada(getNotaReenviar().getDataEmissaoReenvio(), getNotaReenviar().getIdRPS(), TipoNotaEnum.NFSE);
            }

        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }

    }


    private JSONObject montarCamposAlterados(String campo, String valor, boolean aspas) throws Exception {
        JSONObject alteracao = new JSONObject();
        alteracao.put("campo", campo);
        alteracao.put("valor", valor);
        alteracao.put("colocarAspas", aspas);
        return alteracao;
    }

    private JSONArray lancarCamposAlterados() throws Exception {
        JSONArray camposAlterados = new JSONArray();

        //Pode enviar vazio
        camposAlterados.put(montarCamposAlterados("CFDF", getNotaReenviar().getCfdf().toUpperCase(), true));

        if (!Calendario.igual(Calendario.hoje(), getNotaReenviar().getDataEmissaoReenvio())) {
            camposAlterados.put(montarCamposAlterados("DataEmissao", Uteis.getDataAplicandoFormatacao(getNotaReenviar().getDataEmissaoReenvio(), "yyyy-MM-dd HH:mm:ss"), true));
        }

        if (!getNotaReenviar().getRazaoSocialCons().isEmpty()) {
            camposAlterados.put(montarCamposAlterados("RazaoSocialCons", getNotaReenviar().getRazaoSocialCons(), true));
        }
        if (!getNotaReenviar().getCpfCnpjCons().isEmpty()) {
            camposAlterados.put(montarCamposAlterados("CPFCNPJCons", Uteis.removerMascara(getNotaReenviar().getCpfCnpjCons()), true));
        }
        if (!getNotaReenviar().getInscricaoMunicipalCons().isEmpty()) {
            camposAlterados.put(montarCamposAlterados("InscricaoMunicipalCons", getNotaReenviar().getInscricaoMunicipalCons(), true));
        }
        if (!getNotaReenviar().getCepCons().isEmpty()) {
            camposAlterados.put(montarCamposAlterados("CEPCons", Uteis.removerMascara(getNotaReenviar().getCepCons()), true));
        }
        if (!getNotaReenviar().getEmailCons().isEmpty()) {
            camposAlterados.put(montarCamposAlterados("EmailCons", getNotaReenviar().getEmailCons(), true));
        }
        if (!getNotaReenviar().getLogradouroCons().isEmpty()) {
            camposAlterados.put(montarCamposAlterados("LogradouroCons", getNotaReenviar().getLogradouroCons().toUpperCase(), true));
        }
        if (!getNotaReenviar().getComplementoEnderecoCons().isEmpty()) {
            camposAlterados.put(montarCamposAlterados("ComplementoEnderecoCons", getNotaReenviar().getComplementoEnderecoCons().toUpperCase(), true));
        }
        if (!getNotaReenviar().getNumeroEnderecoCons().isEmpty()) {
            camposAlterados.put(montarCamposAlterados("NumeroEnderecoCons", getNotaReenviar().getNumeroEnderecoCons().toUpperCase(), true));
        }
        if (!getNotaReenviar().getBairroCons().isEmpty()) {
            camposAlterados.put(montarCamposAlterados("BairroCons", getNotaReenviar().getBairroCons().toUpperCase(), true));
        }
        if (!getNotaReenviar().getTelefoneCons().isEmpty()) {
            camposAlterados.put(montarCamposAlterados("TelefoneCons", getNotaReenviar().getTelefoneCons().toUpperCase(), true));
        }
        if (!getNotaReenviar().getDescricao().isEmpty()) {
            camposAlterados.put(montarCamposAlterados("Descricao", getNotaReenviar().getDescricao().toUpperCase(), true));
        }
        if (!getNotaReenviar().getInscricaoEstadual().isEmpty()) {
            camposAlterados.put(montarCamposAlterados("InscricaoEstadual", getNotaReenviar().getInscricaoEstadual().toUpperCase(), true));
        }
        if (!getNotaReenviar().getObservacao().isEmpty()) {
            camposAlterados.put(montarCamposAlterados("Observacao", getNotaReenviar().getObservacao().toUpperCase(), true));
        }

        //Referente aos dados de empresa
        if (getNotaReenviar().getAliquotaPIS() != null && !UteisValidacao.emptyString(getNotaReenviar().getAliquotaPIS().toString())) {
            camposAlterados.put(montarCamposAlterados("AliquotaPIS", getNotaReenviar().getAliquotaPIS().toString(), false));
        }

        if (getNotaReenviar().getValorPIS() != null && !UteisValidacao.emptyString(getNotaReenviar().getValorPIS().toString())) {
            camposAlterados.put(montarCamposAlterados("ValorPIS", getNotaReenviar().getValorPIS().toString(), false));
        }

        if (getNotaReenviar().getAliquotaCOFINS() != null && !UteisValidacao.emptyString(getNotaReenviar().getAliquotaCOFINS().toString())) {
            camposAlterados.put(montarCamposAlterados("AliquotaCOFINS", getNotaReenviar().getAliquotaCOFINS().toString(), false));
        }

        if (getNotaReenviar().getValorCOFINS() != null && !UteisValidacao.emptyString(getNotaReenviar().getValorCOFINS().toString())) {
            camposAlterados.put(montarCamposAlterados("ValorCOFINS", getNotaReenviar().getValorCOFINS().toString(), false));
        }

        if (getNotaReenviar().getAliquotaIR() != null && !UteisValidacao.emptyString(getNotaReenviar().getAliquotaIR().toString())) {
            camposAlterados.put(montarCamposAlterados("AliquotaIR", getNotaReenviar().getAliquotaIR().toString(), false));
        }

        if (getNotaReenviar().getValorIR() != null && !UteisValidacao.emptyString(getNotaReenviar().getValorIR().toString())) {
            camposAlterados.put(montarCamposAlterados("ValorIR", getNotaReenviar().getValorIR().toString(), false));
        }

        if (getNotaReenviar().getValorIRRFR() != null && !UteisValidacao.emptyString(getNotaReenviar().getValorIRRFR().toString())) {
            camposAlterados.put(montarCamposAlterados("ValorIRRFR", getNotaReenviar().getValorIRRFR().toString(), false));
        }

        if (!UteisValidacao.emptyString(getNotaReenviar().getItemListaServico())) {
            camposAlterados.put(montarCamposAlterados("ItemListaServico", getNotaReenviar().getItemListaServico(), true));
        }

        if (!UteisValidacao.emptyString(getNotaReenviar().getCodigoTributacaoMunicipio())) {
            camposAlterados.put(montarCamposAlterados("CodigoTributacaoMunicipio", getNotaReenviar().getCodigoTributacaoMunicipio(), true));
        }

        if (!UteisValidacao.emptyString(getNotaReenviar().getCodigoCnae())) {
            camposAlterados.put(montarCamposAlterados("CodigoCnae", getNotaReenviar().getCodigoCnae(), true));
        }

        if (!UteisValidacao.emptyNumber(getNotaReenviar().getExigibilidadeISS())) {
            camposAlterados.put(montarCamposAlterados("ExigibilidadeISS", getNotaReenviar().getExigibilidadeISS().toString(), false));
        }

        if (getNotaReenviar().getAlterouAliquotaSimples()) {
            camposAlterados.put(montarCamposAlterados("AliquotaAtividade", getNotaReenviar().getAliquotaAtividade().toString(), false));
            camposAlterados.put(montarCamposAlterados("ValorISSRetido", getNotaReenviar().getValorISSRetido().toString(), false));
        }

        return camposAlterados;
    }


    public String getAviso() {
        return "<b>Estes campos não serão atualizados no sistema que originou as informações abaixo </b> ";
    }

    public List<LogOperacaoNotaVO> getLogOperacaoNota() {
        return logOperacaoNota;
    }

    public void setLogOperacaoNota(List<LogOperacaoNotaVO> logOperacaoNota) {
        this.logOperacaoNota = logOperacaoNota;
    }

    public void monteLogDaNota(ActionEvent event) throws Exception {
        NotaFiscalDeServicoVO obj = (NotaFiscalDeServicoVO) context().getExternalContext().getRequestMap().get("nota");
        setLogOperacaoNota(getFacade().getNotaNFSe().logDaNota(obj));
        permiteNaoAutorizar = obj.getStatus().equals(StatusNotaEnum.ENVIANDO.getDescricao()) || obj.getStatus().equals(StatusNotaEnum.PROCESSANDO.getDescricao());
    }

    public Integer getQtdNotasAutorizado() {
        if (qtdNotasAutorizado == null) {
            qtdNotasAutorizado = 0;
        }
        return qtdNotasAutorizado;
    }

    public void setQtdNotasAutorizado(Integer qtdNotasAutorizado) {
        this.qtdNotasAutorizado = qtdNotasAutorizado;
    }

    public Integer getQtdNotasNaoAutorizado() {
        if (qtdNotasNaoAutorizado == null) {
            qtdNotasNaoAutorizado = 0;
        }
        return qtdNotasNaoAutorizado;
    }

    public void setQtdNotasNaoAutorizado(Integer qtdNotasNaoAutorizado) {
        this.qtdNotasNaoAutorizado = qtdNotasNaoAutorizado;
    }

    public Integer getQtdNotasCancelado() {
        if (qtdNotasCancelado == null) {
            qtdNotasCancelado = 0;
        }
        return qtdNotasCancelado;
    }

    public void setQtdNotasCancelado(Integer qtdNotasCancelado) {
        this.qtdNotasCancelado = qtdNotasCancelado;
    }

    public Double getTotalizadorCancelando() {
        if (totalizadorCancelando == null) {
            totalizadorCancelando = 0.0;
        }
        return totalizadorCancelando;
    }

    public void setTotalizadorCancelando(Double totalizadorCancelando) {
        this.totalizadorCancelando = totalizadorCancelando;
    }

    public Double getTotalizadorReenviado() {
        if (totalizadorReenviado == null) {
            totalizadorReenviado = 0.0;
        }
        return totalizadorReenviado;
    }

    public void setTotalizadorReenviado(Double totalizadorReenviado) {
        this.totalizadorReenviado = totalizadorReenviado;
    }

    public Double getTotalizadorProcessando() {
        if (totalizadorProcessando == null) {
            totalizadorProcessando = 0.0;
        }
        return totalizadorProcessando;
    }

    public void setTotalizadorProcessando(Double totalizadorProcessando) {
        this.totalizadorProcessando = totalizadorProcessando;
    }

    public Double getTotalizadorEnviando() {
        if (totalizadorEnviando == null) {
            totalizadorEnviando = 0.0;
        }
        return totalizadorEnviando;
    }

    public void setTotalizadorEnviando(Double totalizadorEnviando) {
        this.totalizadorEnviando = totalizadorEnviando;
    }

    public Integer getQtdNotasCancelando() {
        if (qtdNotasCancelando == null) {
            qtdNotasCancelando = 0;
        }
        return qtdNotasCancelando;
    }

    public void setQtdNotasCancelando(Integer qtdNotasCancelando) {
        this.qtdNotasCancelando = qtdNotasCancelando;
    }

    public Integer getQtdNotasReenviado() {
        if (qtdNotasReenviado == null) {
            qtdNotasReenviado = 0;
        }
        return qtdNotasReenviado;
    }

    public void setQtdNotasReenviado(Integer qtdNotasReenviado) {
        this.qtdNotasReenviado = qtdNotasReenviado;
    }

    public Integer getQtdNotasProcessando() {
        if (qtdNotasProcessando == null) {
            qtdNotasProcessando = 0;
        }
        return qtdNotasProcessando;
    }

    public void setQtdNotasProcessando(Integer qtdNotasProcessando) {
        this.qtdNotasProcessando = qtdNotasProcessando;
    }

    public Integer getQtdNotasEnviando() {
        if (qtdNotasEnviando == null) {
            qtdNotasEnviando = 0;
        }
        return qtdNotasEnviando;
    }

    public void setQtdNotasEnviando(Integer qtdNotasEnviando) {
        this.qtdNotasEnviando = qtdNotasEnviando;
    }

    public String getTotalizadorEnviando_Apresentar() {
        return Formatador.formatarValorMonetario(totalizadorEnviando);
    }

    public String getTotalizadorCancelando_Apresentar() {
        return Formatador.formatarValorMonetario(totalizadorCancelando);
    }

    public String getTotalizadorProcessando_Apresentar() {
        return Formatador.formatarValorMonetario(totalizadorProcessando);
    }

    public String getTotalizadorReenviado_Apresentar() {
        return Formatador.formatarValorMonetario(totalizadorReenviado);
    }

    public void naoAutorizarNotaLog(ActionEvent event) throws Exception {
        LogOperacaoNotaVO obj = (LogOperacaoNotaVO) context().getExternalContext().getRequestMap().get("log");
        getFacade().getNotaNFSe().alterarStatusNotaFiscal("Não autorizado", obj.getResultado(), obj.getNotaFiscalVO().getIdRPS());
        NotaFiscalDeServicoVO notaAtualizada = getFacade().getNotaNFSe().consultarPorChavePrimaria(obj.getNotaFiscalVO().getIdRPS(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        atualizarLista(obj.getNotaFiscalVO(), notaAtualizada);
    }

    public boolean isPermiteNaoAutorizar() {
        return permiteNaoAutorizar;
    }

    public void setPermiteNaoAutorizar(boolean permiteNaoAutorizar) {
        this.permiteNaoAutorizar = permiteNaoAutorizar;
    }

    public void limparPeriodoDataEmissao() {
        getFiltro().setDataEmissao_inicio(null);
        getFiltro().setDataEmissao_fim(null);
    }

    public void limparPeriodoDataProcessamento() {
        getFiltro().setDataProcessamento_inicio(null);
        getFiltro().setDataProcessamento_fim(null);
    }

    public void preparaParaExcluirNotas() {
        listaRPSExcluir = "";
    }

    public void preparaParaDesvincularNotas() {
        listaLoteDesvincular = "";
    }

    public String getListaRPSExcluir() {
        if (listaRPSExcluir == null) {
            listaRPSExcluir = "";
        }
        return listaRPSExcluir;
    }

    public void setListaRPSExcluir(String listaRPSExcluir) {
        this.listaRPSExcluir = listaRPSExcluir;
    }

    public void excluirNotas() throws Exception {
        try {
            Integer notasDiferente = getFacade().getNotaNFSe().existeNotaDiferenteDeNaoAutorizada(getListaRPSExcluir());
            if (notasDiferente != 0) {
                throw new Exception("Somente notas NÃO AUTORIZADAS podem ser excluídas!");
            }

            getFacade().getNotaNFSe().alterarNotasParaExcluidas(getListaRPSExcluir());
            getFacade().getNotaNFSe().inserirLogNotasExcluidas("ID_LOTE: "+getListaRPSExcluir());
            setSucesso(true);
            setErro(false);
            setMensagemDetalhada("Notas excluídas com sucesso!");
        } catch (Exception e) {
            setErro(true);
            setSucesso(false);
            setMensagemDetalhada(e);
        }
    }

    public List getListaTipoNotaFiscal() throws Exception {
        List<SelectItem> objs = new ArrayList<SelectItem>();
        for (TipoNotaEnum tipo : TipoNotaEnum.values()) {
            objs.add(new SelectItem(tipo.getCodigo(), tipo.getDescricao()));
        }
        return objs;
    }


    public List<NotaFiscalConsumidorNFCeVO> getListaDeNotasNFCe() {
        if (listaDeNotasNFCe == null) {
            listaDeNotasNFCe = new ArrayList<NotaFiscalConsumidorNFCeVO>();
        }
        return listaDeNotasNFCe;
    }

    public void setListaDeNotasNFCe(List<NotaFiscalConsumidorNFCeVO> listaDeNotasNFCe) {
        this.listaDeNotasNFCe = listaDeNotasNFCe;
    }

    public List<NotaFiscalConsumidorNFCeVO> getListaDeNotasNFCe(int pagina) {
        int inicio = (itensPorPagina * (pagina - 1));
        int fim = ((pagina * itensPorPagina));

        if (inicio < 0) {
            inicio = 0;
        }

        if (fim > getListaDeNotasNFCe().size()) {
            fim = (Integer) getTotalDeNotasNFCe();
        }

        return listaDeNotasNFCe.subList(inicio, fim);
    }

    public Object getTotalDeNotasNFCe() {
        return getListaDeNotasNFCe().size();
    }

    public boolean isApresentarFiltroNFCe() {
        try {
            UsuarioNFeVO usuarioNFeVO = (UsuarioNFeVO) getUsuarioLogado();
            return usuarioNFeVO.isAdministrador() ||
                    (usuarioNFeVO.getPerfilUsuarioNFe().getEmpresa().isUsaModuloNFCe() && usuarioNFeVO.getPerfilUsuarioNFe().getEmpresa().isUsaModuloNFSe());
        } catch (Exception e) {
            return false;
        }
    }

    public NotaFiscalConsumidorNFCeVO getNotaNFCe() {
        if (notaNFCe == null) {
            notaNFCe = new NotaFiscalConsumidorNFCeVO();
        }
        return notaNFCe;
    }

    public void setNotaNFCe(NotaFiscalConsumidorNFCeVO notaNFCe) {
        this.notaNFCe = notaNFCe;
    }


    public void visualizarNFCe() throws Exception {
        NotaFiscalConsumidorNFCeVO obj = (NotaFiscalConsumidorNFCeVO) context().getExternalContext().getRequestMap().get("nota");
        setNotaNFCe(getFacade().getNotaNFSe().consultarPorChavePrimariaNFCe(obj.getId_NFCe(), Uteis.NIVELMONTARDADOS_TODOS));
    }

    public void prepararMotivoDaNFCe(ActionEvent event) {
        setMotivo(((NotaFiscalConsumidorNFCeVO) context().getExternalContext().getRequestMap().get("nota")).getMotivo());
    }

    public void atualizarNFCe() throws Exception {
        NotaFiscalConsumidorNFCeVO obj = (NotaFiscalConsumidorNFCeVO) context().getExternalContext().getRequestMap().get("nota");
        NotaFiscalConsumidorNFCeVO objAtualizado = getFacade().getNotaNFSe().consultarPorChavePrimariaNFCe(obj.getId_NFCe(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        atualizarListaNFCe(obj, objAtualizado);
    }

    private void atualizarListaNFCe(NotaFiscalConsumidorNFCeVO notaAnterior, NotaFiscalConsumidorNFCeVO notaNova) {
        for (Object obj : getListaConsulta()) {
            NotaFiscalConsumidorNFCeVO nfCeVO = (NotaFiscalConsumidorNFCeVO) obj;
            if (notaAnterior.getId_NFCe().equals(nfCeVO.getId_NFCe())){
                notaAnterior = nfCeVO;
                break;
            }
        }

        int posicao = getListaConsulta().indexOf(notaAnterior);
        getListaConsulta().remove(notaAnterior);
        getListaConsulta().add(posicao, notaNova);
    }

    public String getJustificativaParaCancelarVariasNotas() {
        if (justificativaParaCancelarVariasNotas == null) {
            justificativaParaCancelarVariasNotas = "";
        }
        return justificativaParaCancelarVariasNotas;
    }

    public void setJustificativaParaCancelarVariasNotas(String justificativaParaCancelarVariasNotas) {
        this.justificativaParaCancelarVariasNotas = justificativaParaCancelarVariasNotas;
    }

    public List<NotaFiscalDeServicoVO> getNotasParaCancelar() {
        if (notasParaCancelar == null) {
            notasParaCancelar = new ArrayList<NotaFiscalDeServicoVO>();
        }
        return notasParaCancelar;
    }

    public void setNotasParaCancelar(List<NotaFiscalDeServicoVO> notasParaCancelar) {
        this.notasParaCancelar = notasParaCancelar;
    }

    public List<NotaFiscalDeServicoVO> getNotasNaoPodeCancelar() {
        if (notasNaoPodeCancelar == null) {
            notasNaoPodeCancelar = new ArrayList<NotaFiscalDeServicoVO>();
        }
        return notasNaoPodeCancelar;
    }

    public void setNotasNaoPodeCancelar(List<NotaFiscalDeServicoVO> notasNaoPodeCancelar) {
        this.notasNaoPodeCancelar = notasNaoPodeCancelar;
    }

    public void preparaCancelarVariasNotas() {
        try {
            limparMsg();
            setJustificativaParaCancelarVariasNotas("");
            setOnComplete("");
            setNotasParaCancelar(new ArrayList<NotaFiscalDeServicoVO>());
            setNotasNaoPodeCancelar(new ArrayList<NotaFiscalDeServicoVO>());

            for (NotaFiscalDeServicoVO obj : listaDeNotas) {
                if (obj.isMarcado()) {
                    if (obj.getNotaEstaAutorizada() && obj.getPodeCancelarNota()) {
                        getNotasParaCancelar().add(obj);
                    } else {
                        getNotasNaoPodeCancelar().add(obj);
                    }
                }
            }

            if (UteisValidacao.emptyList(getNotasNaoPodeCancelar()) && UteisValidacao.emptyList(getNotasParaCancelar())) {
                throw new Exception("Nenhuma nota foi selecionada.");
            }
            setOnComplete("Richfaces.showModalPanel('modalCancelarVariasNotas')");
        } catch (Exception ex) {
            setMensagemDetalhada(ex);
        }
    }

    public void cancelarVariasNota() throws Exception {
        try {
            setOnComplete("");
            if (UteisValidacao.emptyString(getJustificativaParaCancelarVariasNotas())) {
                throw new Exception("Informe uma justificativa.");
            }

            int idUsuario = ((UsuarioNFeVO) getUsuarioLogado()).getId_Usuario();

            for (NotaFiscalDeServicoVO obj : getNotasParaCancelar()) {
                obj.setJustificativaParaCancelar(getJustificativaParaCancelarVariasNotas());
                getFacade().getNotaNFSe().cancelarNota(obj, idUsuario);

                NotaFiscalDeServicoVO notaAntiga = obj;
                NotaFiscalDeServicoVO notaNova = obj;

                notaNova = getFacade().getNotaNFSe().alterarStatusDaNota(obj, StatusNotaEnum.CANCELANDO.getDescricao());
                atualizarLista(notaAntiga, notaNova);
            }
            totalizarNotas();
            setOnComplete("Richfaces.hideModalPanel('modalCancelarVariasNotas')");
        } catch (Exception ex) {
            setMensagemDetalhada(ex);
        }
    }

    public String getOnComplete() {
        if (onComplete == null) {
            onComplete = "";
        }
        return onComplete;
    }

    public void setOnComplete(String onComplete) {
        this.onComplete = onComplete;
    }

    public Integer getNotasParaCancelar_Total() {
        return getNotasParaCancelar().size();
    }

    public Integer getNotasNaoPodeCancelar_Total() {
        return getNotasNaoPodeCancelar().size();
    }

    public String getAtributosExportar() {
        if (getFiltro().getTipoNota().equals(TipoNotaEnum.NFSE.getCodigo())) { //NFSE
            return "idLote=idLote,serieRPS=Serie,numeroRPS=NumeroRps,numeroNota=Nr.Nota,razaoSocialCons=Razao_Social_Cliente,dataEmissao=Data_Emissao,valorServicos=Valor_RPS,status=Status,dataProcessamento=Data_Processamento,emailFoiEnviado=Email_Enviado,motivo=Motivo,nomeAluno=NomeAluno";
        } else if (getFiltro().getTipoNota().equals(TipoNotaEnum.NFCE.getCodigo())) { //NFCE
            return "id_NFCe=id_NFCe,numeroEnvio=numeroEnvio,destCPFCNPJ=destCPFCNPJ,dataHoraEmissao=dataHoraEmissao,destNome=destNome,valorTotal=valorTotal,status=status,destFone=destFone,destCEP=destCEP,destLogradouro=destLogradouro,destNumero=destNumero,destComplemento=destComplemento,destBairro=destBairro,cnae=cnae";
        } else {
            return "";
        }
    }

    public NotaFiscalConsumidorNFCeVO getNfceParaCancelar() {
        if (nfceParaCancelar == null) {
            nfceParaCancelar = new NotaFiscalConsumidorNFCeVO();
        }
        return nfceParaCancelar;
    }

    public void setNfceParaCancelar(NotaFiscalConsumidorNFCeVO nfceParaCancelar) {
        this.nfceParaCancelar = nfceParaCancelar;
    }

    public List<NotaFiscalConsumidorNFCeVO> getListaNFCEInutilizarNaoPode() {
        if (listaNFCEInutilizarNaoPode == null) {
            listaNFCEInutilizarNaoPode = new ArrayList<NotaFiscalConsumidorNFCeVO>();
        }
        return listaNFCEInutilizarNaoPode;
    }

    public void setListaNFCEInutilizarNaoPode(List<NotaFiscalConsumidorNFCeVO> listaNFCEInutilizarNaoPode) {
        this.listaNFCEInutilizarNaoPode = listaNFCEInutilizarNaoPode;
    }

    public List<NotaFiscalConsumidorNFCeVO> getListaNFCEInutilizar() {
        if (listaNFCEInutilizar == null) {
            listaNFCEInutilizar = new ArrayList<NotaFiscalConsumidorNFCeVO>();
        }
        return listaNFCEInutilizar;
    }

    public void setListaNFCEInutilizar(List<NotaFiscalConsumidorNFCeVO> listaNFCEInutilizar) {
        this.listaNFCEInutilizar = listaNFCEInutilizar;
    }

    public Integer getNotasInutilizar_Total() {
        return getListaNFCEInutilizar().size();
    }

    public Integer getNotasInutilizarNaoPode_Total() {
        return getListaNFCEInutilizarNaoPode().size();
    }

    public void preparaInutilizarNFCE() {
        try {
            limparMsg();
            setOnComplete("");
            setListaNFCEInutilizar(new ArrayList<NotaFiscalConsumidorNFCeVO>());
            setListaNFCEInutilizarNaoPode(new ArrayList<NotaFiscalConsumidorNFCeVO>());

            for (NotaFiscalConsumidorNFCeVO obj : listaDeNotasNFCe) {
                if (obj.isMarcado()) {
                    if (obj.getNotaNaoAutorizada()) {
                        getListaNFCEInutilizar().add(obj);
                    } else {
                        getListaNFCEInutilizarNaoPode().add(obj);
                    }
                }
            }

            if (UteisValidacao.emptyList(getListaNFCEInutilizarNaoPode()) && UteisValidacao.emptyList(getListaNFCEInutilizar())) {
                throw new Exception("Nenhuma NFC-e foi selecionada.");
            }
            setOnComplete("Richfaces.showModalPanel('modalInutilizarNFCE')");
        } catch (Exception ex) {
            setMensagemDetalhada(ex);
        }
    }

    public void inutilizarNFCE() throws Exception {
        try {
            setOnComplete("");
            for (NotaFiscalConsumidorNFCeVO obj : getListaNFCEInutilizar()) {
                JSONObject jsonObject = new JSONObject();
                jsonObject.put("idEmpresa", obj.getEmpresaNFe().getCodigo());
                jsonObject.put("serie", 1);
                jsonObject.put("numeroInicial", Integer.parseInt(obj.getNumeroEnvio()));
                jsonObject.put("numeroFinal", Integer.parseInt(obj.getNumeroEnvio()));
                RetornoEnvioNotaFiscalTO retornoEnvioNotaFiscalTO = getFacade().getNotaFiscalConsumidorEletronica().inutilizarNFCe(jsonObject, obj.getId_NFCe());
                obj.setResultado(retornoEnvioNotaFiscalTO.getMensagem());

                desvincularNFCEInutilizada(obj);

            }
            setOnComplete("Richfaces.hideModalPanel('modalInutilizarNFCE')");
        } catch (Exception ex) {
            setMensagemDetalhada(ex);
        }
    }

    private void desvincularNFCEInutilizada(NotaFiscalConsumidorNFCeVO obj){
        Map<String, String> corpo = new HashMap<String, String>();
        try {
            EmpresaNFeVO ep = (EmpresaNFeVO) getEmpresaLogado();

            if (!UteisValidacao.emptyString(ep.getChaveZW())) {

                Map<String, String> headers = new HashMap<String, String>();
                headers.put("Content-Type", "application/x-www-form-urlencoded");

                corpo.put("nfce", "true");
                corpo.put("listaIdLote", obj.getId_NFCe().toString());
                corpo.put("usuarioOAMD", getUsuarioLogado().toString());

                String urlConsultar = PropsService.getPropertyValue(PropsService.urlOamd) + "/" + ep.getChaveZW() + "/desvincularNotaFiscalEmitida";
                String executeRequest = ExecuteRequestHttpService.executeHttpRequestGenerico(urlConsultar, headers, corpo, ExecuteRequestHttpService.METODO_POST, "ISO-8859-1", "ISO-8859-1");
                org.json.JSONObject retornoJSON = new org.json.JSONObject(executeRequest);
                if (!retornoJSON.has("return")) {
                    throw new Exception(String.format("Erro ao tentar desvincular nota.%s", obj.getNumeroEnvio()));
                }
            }

        } catch (Exception ignoredTemp) {
            try {
                corpo.put("Error", ignoredTemp.getMessage());
                getFacade().getNotaFiscal().incluirLogEmisao(new org.json.JSONObject(corpo).toString(), getEmpresaLogado().getCodigo());
            }catch (Exception ignored){}
        }

    }

    public List<NumerosInutilizarVO> getLogNumerosInutilizar() {
        if (logNumerosInutilizar == null) {
            logNumerosInutilizar = new ArrayList<NumerosInutilizarVO>();
        }
        return logNumerosInutilizar;
    }

    public void setLogNumerosInutilizar(List<NumerosInutilizarVO> logNumerosInutilizar) {
        this.logNumerosInutilizar = logNumerosInutilizar;
    }

    public void monteLogInutilizarNFCE(ActionEvent event) throws Exception {
        try {
            setLogNumerosInutilizar(new ArrayList<NumerosInutilizarVO>());
            NotaFiscalConsumidorNFCeVO obj = (NotaFiscalConsumidorNFCeVO) context().getExternalContext().getRequestMap().get("nota");
            setLogNumerosInutilizar(getFacade().getNotaNFSe().logInutilizarNFCE(obj));
        } catch (Exception e) {
            setLogNumerosInutilizar(new ArrayList<NumerosInutilizarVO>());
            setMensagemDetalhada("msg_erro", e.getMessage());
            setSucesso(false);
            setErro(true);
        }
    }

    public void downloadXMLInutilizadaNFCeArquivosMarcados() {
        try {
            limparMsg();
            setFileName("");

            List<byte[]> arquivos = new ArrayList<byte[]>();
            List<String> nomes = new ArrayList<String>();

            for (NotaFiscalConsumidorNFCeVO objNFCe : listaDeNotasNFCe) {
                if (objNFCe.isMarcado() && objNFCe.isInutilizado()) {
                    String codigoReferencia = objNFCe.getNumeroEnvio();
                    byte[] b = getFacade().getNotaNFSe().obtenhaXMLInutilizarNFCE(objNFCe);

                    if (b != null && b.length > 0) {
                        arquivos.add(b);
                        nomes.add(codigoReferencia + " - XMLIN.xml");
                    }
                }
            }

            boolean gerou;
            if (arquivos.size() > 0) {
                String absolutePath = this.getServletContext().getRealPath("relatorio") + File.separator;
                String nomeArquivo = "XML-IN-" + System.currentTimeMillis() + ".zip";

                String nomeArquivoGerado = absolutePath + nomeArquivo;
                gerou = Uteis.zip(nomeArquivoGerado, arquivos, nomes);
                if (gerou) {
                    setFileName(nomeArquivo);
                }
            } else {
                throw new ConsistirException("Não foi selecionado nenhuma nota.");
            }
        } catch (Exception ex) {
            setMensagemDetalhada(ex);
        }
    }

    public void downloadXMLInutilizadaNFCe(ActionEvent event) throws Exception {
        setMensagemDetalhada("");

        NotaFiscalConsumidorNFCeVO objNFCe = (NotaFiscalConsumidorNFCeVO) context().getExternalContext().getRequestMap().get("nota");
        String codigoReferencia = objNFCe.getNumeroEnvio();

        HttpServletResponse res = (HttpServletResponse) context().getExternalContext().getResponse();
        ServletOutputStream out = res.getOutputStream();

        byte[] b = getFacade().getNotaNFSe().obtenhaXMLInutilizarNFCE(objNFCe);

        if (b != null && b.length > 0) {
            res.setHeader("Content-disposition", "attachment;filename=\"" + codigoReferencia + " - XMLIN.xml\"");
            res.setContentLength(b.length);
            res.setContentType("application/octet-stream");

            out.write(b);
            out.flush();
            out.close();

            FacesContext.getCurrentInstance().responseComplete();
        } else {
            setMensagemDetalhada("Arquivo não encontrado.");
        }
    }

    public NotaFiscalConsumidorNFCeVO getNfceReenviar() {
        if (nfceReenviar == null) {
            nfceReenviar = new NotaFiscalConsumidorNFCeVO();
        }
        return nfceReenviar;
    }

    public void setNfceReenviar(NotaFiscalConsumidorNFCeVO nfceReenviar) {
        this.nfceReenviar = nfceReenviar;
    }

    public void prepararReenvioNFCe() throws Exception {
        NotaFiscalConsumidorNFCeVO obj = (NotaFiscalConsumidorNFCeVO) context().getExternalContext().getRequestMap().get("nota");
        setNfceReenviar(getFacade().getNotaNFSe().consultarPorChavePrimariaNFCe(obj.getId_NFCe(), Uteis.NIVELMONTARDADOS_TODOS));
        getNfceReenviar().setDataHoraEmissao(Calendario.hoje());
        buscarCidade();
    }

    public void reenviarNFCe() throws Exception {
        try {
            if (Calendario.maior(getNfceReenviar().getDataEmissaoReenvio(), Calendario.hoje())) {
                throw new ConsistirException("A DATA DE EMISSÃO não pode superior a data atual!");
            }


            if (UteisValidacao.emptyNumber(getNfceReenviar().getDestCidade().getId_Municipio())) {
                throw new ConsistirException("Informe uma cidade para reenviar a nota.");
            }

            JSONArray jsonArray = lancarCamposAlteradosNFCe();
            if (jsonArray.length() > 0) {
                JSONObject jsonObject = new JSONObject();
                jsonObject.put("idNFCe", getNfceReenviar().getId_NFCe());
                jsonObject.put("alteracoes", jsonArray);
                RetornoEnvioNotaFiscalTO retornoEnvioNotaFiscalTO = getFacade().getNotaFiscalConsumidorEletronica().reenviarNFCe(jsonObject, getNfceReenviar().getId_NFCe());
                if (!retornoEnvioNotaFiscalTO.getResultadoEnvio().equals(ResultadoEnvioNFSeEnum.OK)) {
                    throw new ConsistirException("Ocorreu um erro ao reeviar a NFCe!");
                }
                NotaFiscalConsumidorNFCeVO notaAntiga = getNfceReenviar();
                NotaFiscalConsumidorNFCeVO notaAtualizada = getFacade().getNotaNFSe().consultarPorChavePrimariaNFCe(getNfceReenviar().getId_NFCe(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                atualizarListaNFCe(notaAntiga, notaAtualizada);
                inserirLogNotaReenviada(getNfceReenviar().getDataEmissaoReenvio(), getNfceReenviar().getId_NFCe(), TipoNotaEnum.NFCE);
            }

        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    private JSONArray lancarCamposAlteradosNFCe() throws Exception {
        JSONArray camposAlterados = new JSONArray();

        camposAlterados.put(montarCamposAlterados("NumeroEnvio", getNfceReenviar().getNumeroEnvio(), true));

        if (!Calendario.igual(Calendario.hoje(), getNfceReenviar().getDataEmissaoReenvio())) {
            camposAlterados.put(montarCamposAlterados("DataHoraEmissao", Uteis.getDataAplicandoFormatacao(getNfceReenviar().getDataEmissaoReenvio(), "yyyy-MM-dd HH:mm:ss"), true));
        }

        if (!getNfceReenviar().getDestNome().isEmpty()) {
            camposAlterados.put(montarCamposAlterados("destNome", getNfceReenviar().getDestNome(), true));
        }
        if (!getNfceReenviar().getDestCPFCNPJ().isEmpty()) {
            camposAlterados.put(montarCamposAlterados("destCPFCNPJ", Uteis.removerMascara(getNfceReenviar().getDestCPFCNPJ()), true));
        }
        if (!getNfceReenviar().getDestFone().isEmpty()) {
            camposAlterados.put(montarCamposAlterados("destFone", getNfceReenviar().getDestFone(), true));
        }
        if (!getNfceReenviar().getDestCEP().isEmpty()) {
            camposAlterados.put(montarCamposAlterados("destCEP", Uteis.removerMascara(getNfceReenviar().getDestCEP()), true));
        }
        if (!getNfceReenviar().getDestLogradouro().isEmpty()) {
            camposAlterados.put(montarCamposAlterados("destLogradouro", getNfceReenviar().getDestLogradouro(), true));
        }
        if (!getNfceReenviar().getDestNumero().isEmpty()) {
            camposAlterados.put(montarCamposAlterados("destNumero", getNfceReenviar().getDestNumero().toUpperCase(), true));
        }
        if (!getNfceReenviar().getDestComplemento().isEmpty()) {
            camposAlterados.put(montarCamposAlterados("destComplemento", getNfceReenviar().getDestComplemento().toUpperCase(), true));
        }
        if (!getNfceReenviar().getDestBairro().isEmpty()) {
            camposAlterados.put(montarCamposAlterados("destBairro", getNfceReenviar().getDestBairro().toUpperCase(), true));
        }
        if (!getNfceReenviar().getComplemento().isEmpty()) {
            camposAlterados.put(montarCamposAlterados("complemento", getNfceReenviar().getComplemento().toUpperCase(), true));
        }
        if (!getNfceReenviar().getCnae().isEmpty()) {
            camposAlterados.put(montarCamposAlterados("CNAE", getNfceReenviar().getCnae().toUpperCase(), true));
        }
        if (!UteisValidacao.emptyNumber(getNfceReenviar().getDestCidade().getId_Municipio())) {
            camposAlterados.put(montarCamposAlterados("DestId_Cidade", getNfceReenviar().getDestCidade().getId_Municipio().toString(), false));
        }

        return camposAlterados;
    }

    private void downloadPDFNFCeArquivosMarcados(){
        try {
            setOnComplete("");
            StringBuilder listaNFCe = new StringBuilder();
            int qtdNotasSelecionadas = 0;
            for (NotaFiscalConsumidorNFCeVO obj : listaDeNotasNFCe) {
                if (obj.isMarcado()) {
                    listaNFCe.append(obj.getId_NFCe()).append(",");
                    qtdNotasSelecionadas++;
                }
            }
            if (UteisValidacao.emptyString(listaNFCe.toString())) {
                throw new ConsistirException("Não foi selecionado nenhuma nota.");
            }

            listaNFCe.deleteCharAt(listaNFCe.length() - 1);

//            String moduloNFSe = PropsService.getPropertyValue(PropsService.urlModuloNFSe);
//            if (qtdNotasSelecionadas > 1) {
//                HashMap<String, String> params = new HashMap<>();
//                params.put("nfce", listaNFCe.toString());
//                ExecuteRequestHttpService executeRequestHttpService = new ExecuteRequestHttpService();
//                executeRequestHttpService.readTimeout = 1200000;
//                String retornoRequestGerarPDFs = executeRequestHttpService.executeRequestInner(moduloNFSe + "/nfce", params, "iso-8859-1");
//                JSONObject arquivoGerado = new JSONObject(retornoRequestGerarPDFs);
//                setOnComplete("window.open('" + moduloNFSe + "/" + arquivoGerado.optString("url") + "', '_blank');");
//            } else {
//                setOnComplete("window.open('" + moduloNFSe + "/nfce?nfce=" + listaNFCe.toString() + "', '_blank');");
//            }

            String moduloNFSe = Uteis.getPath(request());
            if (qtdNotasSelecionadas > 1) {

                String nomeArquivo = getFacade().getNotaNFSe().gerarPDFNotasNFCe(listaNFCe.toString(), request());

                setOnComplete("window.open('" + moduloNFSe + "/DownloadSV?mimeType=application/zip&relatorio=" + nomeArquivo + "', '_blank');");
            } else {
                setOnComplete("window.open('" + moduloNFSe + "/nfce?nfce=" + listaNFCe.toString() + "', '_blank');");
            }

        } catch (Exception ex) {
            setOnComplete("");
            setMensagemDetalhada(ex);
        }
    }

    private void downloadPDFNFSeArquivosMarcados(){
        try {
            setOnComplete("");
            StringBuilder listaRPS = new StringBuilder();
            int qtdNotasSelecionadas = 0;
            for (NotaFiscalDeServicoVO obj : listaDeNotas) {
                if (obj.isMarcado()) {
                    listaRPS.append(obj.getIdRPS()).append(",");
                    qtdNotasSelecionadas++;
                }
            }
            if (UteisValidacao.emptyString(listaRPS.toString())) {
                throw new ConsistirException("Não foi selecionado nenhuma nota.");
            }

            listaRPS.deleteCharAt(listaRPS.length() - 1);

//            String moduloNFSe = PropsService.getPropertyValue(PropsService.urlModuloNFSe);
//            if (qtdNotasSelecionadas > 1) {
//                HashMap<String, String> params = new HashMap<String, String>();
//                params.put("rps", listaRPS.toString());
//                ExecuteRequestHttpService executeRequestHttpService = new ExecuteRequestHttpService();
//                executeRequestHttpService.readTimeout = 1200000;
//                String retornoRequestGerarPDFs = executeRequestHttpService.executeRequestInner(moduloNFSe + "/nota", params, "iso-8859-1");
//
//                JSONObject arquivoGerado = new JSONObject(retornoRequestGerarPDFs);
//
//                setOnComplete("window.open('" + moduloNFSe + "/" + arquivoGerado.optString("url") + "', '_blank');");
//            } else {
//                setOnComplete("window.open('" + moduloNFSe + "/nota?rps=" + listaRPS.toString() + "', '_blank');");
//            }

            String moduloNFSe = Uteis.getPath(request());
            if (qtdNotasSelecionadas > 1) {

                String nomeArquivo = getFacade().getNotaNFSe().gerarPDFNotasNFSe(listaRPS.toString(), request());

                setOnComplete("window.open('" + moduloNFSe + "/DownloadSV?mimeType=application/zip&relatorio=" + nomeArquivo + "', '_blank');");
            } else {
                setOnComplete("window.open('" + moduloNFSe + "/nota?rps=" + listaRPS.toString() + "', '_blank');");
            }
        } catch (Exception ex) {
            setOnComplete("");
            setMensagemDetalhada(ex);
        }
    }

    private void inserirLogNotaReenviada(Date dataReenvio, Integer id_Referencia, TipoNotaEnum tipoNotaEnum){
        try {
            UsuarioNFeVO usuarioVOLogado = (UsuarioNFeVO) getUsuarioLogado();
            String usuario = usuarioVOLogado.getUsername() + " - Codigo: " + usuarioVOLogado.getId_Usuario();
            getFacade().getNotaNFSe().inserirLogNotaReenviada(dataReenvio, usuario, id_Referencia, tipoNotaEnum);
        } catch (Exception e) {

        }
    }

    public String getSequenciaRPSAtual() {
        if (sequenciaRPSAtual == null) {
            sequenciaRPSAtual = "";
        }
        return sequenciaRPSAtual;
    }

    public void setSequenciaRPSAtual(String sequenciaRPSAtual) {
        this.sequenciaRPSAtual = sequenciaRPSAtual;
    }

    public String getSequenciaLoteAtual() {
        if (sequenciaLoteAtual == null) {
            sequenciaLoteAtual = "";
        }
        return sequenciaLoteAtual;
    }

    public void setSequenciaLoteAtual(String sequenciaLoteAtual) {
        this.sequenciaLoteAtual = sequenciaLoteAtual;
    }

    public String getSequenciaRPS() {
        if (sequenciaRPS == null) {
            sequenciaRPS = "";
        }
        return sequenciaRPS;
    }

    public void setSequenciaRPS(String sequenciaRPS) {
        this.sequenciaRPS = sequenciaRPS;
    }

    public String getSequenciaLote() {
        if (sequenciaLote == null) {
            sequenciaLote = "";
        }
        return sequenciaLote;
    }

    public void setSequenciaLote(String sequenciaLote) {
        this.sequenciaLote = sequenciaLote;
    }

    private Integer obterIdEmpresaLogadaSelecionada() throws Exception {
        UsuarioNFeVO usuarioNFeVO = (UsuarioNFeVO) getUsuarioLogado();
        Integer idEmpresa;

        if (usuarioNFeVO.isAdministrador()) {
            idEmpresa = getFiltro().getIdEmpresa();
        } else {
            idEmpresa = usuarioNFeVO.getPerfilUsuarioNFe().getEmpresa().getId_Empresa();
        }

        return idEmpresa;

    }

    public void preparaAlterarSequenciaRPS() {
        try {
            limparMsg();
            setSequenciaRPS("");
            setSequenciaLote("");
            setOnComplete("");

            Integer idEmpresa = obterIdEmpresaLogadaSelecionada();

            if (UteisValidacao.emptyNumber(idEmpresa)) {
                throw new Exception("Nenhuma empresa logada ou selecionada");
            }

            String identificadorRPS = "SeqRPS_" + idEmpresa;
            String identificadorLote = "SeqLote_" + idEmpresa;


            setSequenciaRPSAtual(getFacade().getNotaNFSe().consultarProximoCodigoTabelaCodigo(identificadorRPS));
            setSequenciaLoteAtual(getFacade().getNotaNFSe().consultarProximoCodigoTabelaCodigo(identificadorLote));

            setSequenciaRPS(getSequenciaRPSAtual());
            setSequenciaLote(getSequenciaLoteAtual());

            setOnComplete("Richfaces.showModalPanel('modalSequenciaRPS')");
        } catch (Exception ex) {
            setMensagemDetalhada(ex);
        }
    }

    public void alterarSequenciaRPS() {
        try {

            if (UteisValidacao.emptyString(getSequenciaRPS())) {
                throw new ConsistirException("Informe a sequÃªncia do RPS");
            }

            // ATUALMENTE SERÃ INFORMADO UM VALOR E SERÃ ALTERADO PARA OS DOIS ITENS SEQUENCIA DO RPS E DO LOTE
//            if (UteisValidacao.emptyString(getSequenciaLote())) {
//                throw new ConsistirException("Informe a sequÃªncia do Lote");
//            }

            Integer idEmpresa = obterIdEmpresaLogadaSelecionada();

            if (UteisValidacao.emptyNumber(idEmpresa)) {
                throw new Exception("Nenhuma empresa logada ou selecionada");
            }

            String identificadorRPS = "SeqRPS_" + idEmpresa;
            String identificadorLote = "SeqLote_" + idEmpresa;

            getFacade().getNotaNFSe().alterarProximoCodigoTabelaCodigo(getSequenciaRPS(), identificadorRPS);
            // ATUALMENTE SERÃ INFORMADO UM VALOR E SERÃ ALTERADO PARA OS DOIS ITENS SEQUENCIA DO RPS E DO LOTE
            getFacade().getNotaNFSe().alterarProximoCodigoTabelaCodigo(getSequenciaRPS(), identificadorLote);

        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public String getListaLoteDesvincular() {
        if (listaLoteDesvincular == null) {
            listaLoteDesvincular = "";
        }
        return listaLoteDesvincular;
    }

    public void setListaLoteDesvincular(String listaLoteDesvincular) {
        this.listaLoteDesvincular = listaLoteDesvincular;
    }

    public void desvincularNotas() {
        try {
            List<NotaFiscalDeServicoVO> notas = getFacade().getNotaNFSe().consultarPorId_Lote(getListaLoteDesvincular());
            for (NotaFiscalDeServicoVO nota : notas) {
                getFacade().getNotaNFSe().limparIdReferenciaParaReenvio(nota);
            }
            setSucesso(true);
            setErro(false);
            setMensagemDetalhada("Notas desvinculadas com sucesso!");
        } catch (Exception e) {
            setErro(true);
            setSucesso(false);
            setMensagemDetalhada(e);
        }
    }

    public String getSerieInutilizarNFe() {
        if (serieInutilizarNFe == null) {
            serieInutilizarNFe = "";
        }
        return serieInutilizarNFe;
    }

    public void setSerieInutilizarNFe(String serieInutilizarNFe) {
        this.serieInutilizarNFe = serieInutilizarNFe;
    }

    public String getInicioInutilizarNFe() {
        if (inicioInutilizarNFe == null) {
            inicioInutilizarNFe = "";
        }
        return inicioInutilizarNFe;
    }

    public void setInicioInutilizarNFe(String inicioInutilizarNFe) {
        this.inicioInutilizarNFe = inicioInutilizarNFe;
    }

    public String getFinalInutilizarNFe() {
        if (finalInutilizarNFe == null) {
            finalInutilizarNFe = "";
        }
        return finalInutilizarNFe;
    }

    public void setFinalInutilizarNFe(String finalInutilizarNFe) {
        this.finalInutilizarNFe = finalInutilizarNFe;
    }

    public void preparaInutilizarNFE() {
        try {
            limparMsg();
            setOnComplete("");
            setSerieInutilizarNFe("");
            setInicioInutilizarNFe("");
            setFinalInutilizarNFe("");
            setOnComplete("Richfaces.showModalPanel('modalInutilizarNFE')");
        } catch (Exception ex) {
            setMensagemDetalhada(ex);
        }
    }

    public void inutilizarNFE() {
        try {
            setOnComplete("");

            if (UteisValidacao.emptyString(getSerieInutilizarNFe())) {
                throw new Exception("Informe a série");
            }
            if (UteisValidacao.emptyString(getInicioInutilizarNFe())) {
                throw new Exception("Informe a numeração inicial");
            }
            if (UteisValidacao.emptyString(getFinalInutilizarNFe())) {
                throw new Exception("Informe a numeração final");
            }

            Integer numInicial = Integer.parseInt(getInicioInutilizarNFe());
            Integer numFinal = Integer.parseInt(getFinalInutilizarNFe());

            if (numInicial > numFinal) {
                throw new Exception("O número inicial não pode ser superior ao número inicial");
            }

            JSONObject jsonObject = new JSONObject();
            jsonObject.put("idEmpresa", obterIdEmpresaLogadaSelecionada());
            jsonObject.put("serie", Integer.parseInt(getSerieInutilizarNFe()));
            jsonObject.put("numeroInicial", Integer.parseInt(getInicioInutilizarNFe()));
            jsonObject.put("numeroFinal", Integer.parseInt(getFinalInutilizarNFe()));
            RetornoEnvioNotaFiscalTO retornoEnvioNotaFiscalTO = getFacade().getLoteNFSe().inutilizarNFe(jsonObject);
            if (!retornoEnvioNotaFiscalTO.getResultadoEnvio().equals(ResultadoEnvioNFSeEnum.OK)) {
                throw new Exception(retornoEnvioNotaFiscalTO.getMensagem());
            }
            setOnComplete("Richfaces.hideModalPanel('modalInutilizarNFE')");
        } catch (Exception ex) {
            setMensagemDetalhada(ex);
        }
    }

    public boolean isPermiteInutilizarNFe() {
        return permiteInutilizarNFe;
    }

    public void setPermiteInutilizarNFe(boolean permiteInutilizarNFe) {
        this.permiteInutilizarNFe = permiteInutilizarNFe;
    }

    public void montarHistoricoInutilizacaoNFE() {
        try {
            setLogNumerosInutilizar(new ArrayList<NumerosInutilizarVO>());
            setLogNumerosInutilizar(getFacade().getNotaNFSe().logInutilizarNFE(obterIdEmpresaLogadaSelecionada()));
        } catch (Exception e) {
            setLogNumerosInutilizar(new ArrayList<NumerosInutilizarVO>());
            setMensagemDetalhada("msg_erro", e.getMessage());
            setSucesso(false);
            setErro(true);
        }
    }

    public String getMensagemAlertaLoginDiretoModuloNotas() {
        return "Existe uma forma direta de acessar o módulo de notas. " +
                "Para habilitar, basta acessar o Perfil de Acesso e marcar a " +
                "permissão 4.41 - Acesso ao Módulo de Notas, gravar e sair do sistema e entrar novamente.";
    }

    public boolean isApresentarAlertaLoginDiretoModuloNotas() {
        if (usuarioAcessouDiretoModuloNotas) {
            return false;
        }

        try {
            if (getUsuarioLogado().getAdministrador()) {
                return false;
            }

        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }

        return JSFUtilities.recuperarCookie("naoMostrarAlertaAcessoDiretoModuloNotas") == null;
    }

    public void setUsuarioAcessouDiretoModuloNotas(boolean usuarioAcessouDiretoModuloNotas) {
        this.usuarioAcessouDiretoModuloNotas = usuarioAcessouDiretoModuloNotas;
    }

    public void clickOkAlertaAcessoDiretoModuloNotas() {
        if (checkNaoMostrarAlertaAcessoDiretoModuloNotas) {
            JSFUtilities.addCookie(new Cookie("naoMostrarAlertaAcessoDiretoModuloNotas", "true"));
        } else {
            JSFUtilities.removeCookie("naoMostrarAlertaAcessoDiretoModuloNotas");
        }
    }

    public boolean isCheckNaoMostrarAlertaAcessoDiretoModuloNotas() {
        return checkNaoMostrarAlertaAcessoDiretoModuloNotas;
    }

    public void setCheckNaoMostrarAlertaAcessoDiretoModuloNotas(boolean checkNaoMostrarAlertaAcessoDiretoModuloNotas) {
        this.checkNaoMostrarAlertaAcessoDiretoModuloNotas = checkNaoMostrarAlertaAcessoDiretoModuloNotas;
    }

    public List<SelectItem> getListaCidades() {
        if (listaCidades == null) {
            listaCidades = new ArrayList<>();
        }
        return listaCidades;
    }

    public void setListaCidades(List<SelectItem> listaCidades) {
        this.listaCidades = listaCidades;
    }

    public String getSiglaEstadoBuscar() {
        if (siglaEstadoBuscar == null) {
            siglaEstadoBuscar = "";
        }
        return siglaEstadoBuscar;
    }

    public void setSiglaEstadoBuscar(String siglaEstadoBuscar) {
        this.siglaEstadoBuscar = siglaEstadoBuscar;
    }

    public List<SelectItem> getListaEstado() {
        List<SelectItem> objs = new ArrayList<SelectItem>();
        objs.add(new SelectItem("", ""));
        objs.add(new SelectItem("AC", "ACRE"));
        objs.add(new SelectItem("AL", "ALAGOAS"));
        objs.add(new SelectItem("AP", "AMAPÁ"));
        objs.add(new SelectItem("AM", "AMAZONAS"));
        objs.add(new SelectItem("BA", "BAHIA"));
        objs.add(new SelectItem("CE", "CEARÁ"));
        objs.add(new SelectItem("DF", "DISTRITO FEDERAL"));
        objs.add(new SelectItem("ES", "ESPÍRITO SANTO"));
        objs.add(new SelectItem("GO", "GOIÁS"));
        objs.add(new SelectItem("MA", "MARANHÃO"));
        objs.add(new SelectItem("MT", "MATO GROSSO"));
        objs.add(new SelectItem("MS", "MATO GROSSO DO SUL"));
        objs.add(new SelectItem("MG", "MINAS GERAIS"));
        objs.add(new SelectItem("PA", "PARÁ"));
        objs.add(new SelectItem("PB", "PARAÍBA"));
        objs.add(new SelectItem("PR", "PARANÁ"));
        objs.add(new SelectItem("PE", "PERNAMBUCO"));
        objs.add(new SelectItem("PI", "PIAUÍ"));
        objs.add(new SelectItem("RJ", "RIO DE JANEIRO"));
        objs.add(new SelectItem("RN", "RIO GRANDE DO NORTE"));
        objs.add(new SelectItem("RS", "RIO GRANDE DO SUL"));
        objs.add(new SelectItem("RO", "RONDÔNIA"));
        objs.add(new SelectItem("RR", "RORAIMA"));
        objs.add(new SelectItem("SC", "SANTA CATARINA"));
        objs.add(new SelectItem("SP", "SÃO PAULO"));
        objs.add(new SelectItem("SE", "SERGIPE"));
        objs.add(new SelectItem("TO", "TOCANTINS"));
        return objs;
    }

    public void buscarCidade() {
        try {
            setListaCidades(new ArrayList<>());
            if (!UteisValidacao.emptyString(getNfceReenviar().getDestCidade().getUf())) {
                setListaCidades(getFacade().getMunicipioNFe().obtenhaMunicipios(getNfceReenviar().getDestCidade().getUf()));
            }
        } catch (Exception ex){

        }
    }

    public List<SelectItem> getListaEmpresasCadastradas() {
        if (listaEmpresasCadastradas == null) {
            listaEmpresasCadastradas = new ArrayList<>();
        }
        return listaEmpresasCadastradas;
    }

    public void setListaEmpresasCadastradas(List<SelectItem> listaEmpresasCadastradas) {
        this.listaEmpresasCadastradas = listaEmpresasCadastradas;
    }
}
