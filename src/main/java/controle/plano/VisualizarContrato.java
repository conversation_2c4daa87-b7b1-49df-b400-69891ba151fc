//relatorio
package controle.plano;

import java.io.*;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.sql.Connection;
import java.util.HashMap;
import java.util.List;
import javax.servlet.*;
import javax.servlet.http.*;

import br.com.pactosolucoes.comuns.util.JSFUtilities;
import br.com.pactosolucoes.oamd.controle.basico.DAO;
import negocio.comuns.financeiro.MovPagamentoVO;
import negocio.comuns.financeiro.MovParcelaVO;
import negocio.comuns.financeiro.VendaAvulsaVO;
import negocio.comuns.plano.ProdutoVO;
import negocio.comuns.utilitarias.Criptografia;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.Usuario;
import negocio.facade.jdbc.basico.Empresa;
import negocio.facade.jdbc.basico.Pessoa;
import negocio.facade.jdbc.contrato.ContratoTextoPadrao;
import negocio.facade.jdbc.contrato.MovProduto;
import negocio.facade.jdbc.financeiro.MovPagamento;
import negocio.facade.jdbc.financeiro.MovParcela;
import negocio.facade.jdbc.financeiro.VendaAvulsa;
import negocio.facade.jdbc.plano.PlanoTextoPadrao;
import negocio.facade.jdbc.plano.Produto;
import net.sf.jasperreports.engine.JRDataSource;
import net.sf.jasperreports.engine.JasperFillManager;
import net.sf.jasperreports.engine.JasperPrint;
import net.sf.jasperreports.engine.JasperReport;
import net.sf.jasperreports.engine.data.JRBeanArrayDataSource;
import net.sf.jasperreports.engine.data.JRXmlDataSource;
import net.sf.jasperreports.engine.util.JRLoader;
import servicos.discovery.ClientDiscoveryDataDTO;
import servicos.discovery.DiscoveryMsService;

import static controle.arquitetura.SuperControle.Crypt_ALGORITM;
import static controle.arquitetura.SuperControle.Crypt_KEY_Contrato;
import static negocio.comuns.utilitarias.Uteis.getUrl;
import static negocio.facade.jdbc.arquitetura.FacadeManager.getFacade;

/**
 * Servlet responsável por gerar a visualização do relatório e apresentá-lo ao usuário.
 * É utilizado por todos os relatórios do sistema com esta finalidade. É capaz de receber os
 * dados do relatório e compilar o relatório final utilizando o JasperReport.
 */
public class VisualizarContrato extends HttpServlet {

    private HashMap parametrosRelatorio;
    private String nomeRelatorio;
    private String nomeEmpresa;
    private String nomeDesignIReport;
    private String caminhoParserXML;
    private String xmlDados;
    private String tipoRelatorio;
    private String mensagemRel;
    private String tipoImplementacao;
    private List listaObjetos;

    /**
     * Rotina responsável por obter o diretório real da aplicação Web em execução.
     * É importante acessar este diretório para que seja possível utilizar recursos
     * existentes nos pacotes da aplicação.
     */
    public String obterCaminhoBaseAplicacao(HttpServletRequest request, HttpServletResponse response) throws Exception {
        ServletContext servletContext = (ServletContext) this.getServletContext();
        String caminhoBaseAplicacao = servletContext.getRealPath(request.getContextPath());
        File caminhoBaseAplicacaoFile = new File(caminhoBaseAplicacao);
        caminhoBaseAplicacao = caminhoBaseAplicacaoFile.getParent() + File.separator + "WEB-INF" + File.separator + "classes";
        return caminhoBaseAplicacao;
    }

    /**
     * Rotina responsável por obter o diretório real da aplicação Web em execução.
     * É importante acessar este diretório para que seja possível utilizar recursos
     * existentes nos pacotes da aplicação.
     */
    public String obterCaminhoWebAplicacao(HttpServletRequest request, HttpServletResponse response) throws Exception {
        ServletContext servletContext = (ServletContext) this.getServletContext();
        String caminhoBaseAplicacao = servletContext.getRealPath(request.getContextPath());
        File caminhoBaseAplicacaoFile = new File(caminhoBaseAplicacao);
        caminhoBaseAplicacao = caminhoBaseAplicacaoFile.getParent();
        return caminhoBaseAplicacao;
    }

    public JasperPrint gerarRelatorioJasperPrintXML(HttpServletRequest request, HttpServletResponse response, String xmlDados, String caminhoParserXML, String nomeDesignIReport) throws Exception {
        JRXmlDataSource jrxmlds = new JRXmlDataSource(new ByteArrayInputStream(xmlDados.getBytes(), 0, xmlDados.length()), caminhoParserXML);

        //File arquivoIReport = new File(obterCaminhoBaseAplicacao(request, response) + File.separator + nomeDesignIReport);
        //JasperDesign jasperDesign = JRXmlLoader.load( arquivoIReport.getAbsoluteFile() );

        // Gerando relatório jasperReport com datasource do tipo XML
        //JasperReport jasperReport = JasperCompileManager.compileReport( jasperDesign );
        String nomeJasperReportDesignIReport = nomeDesignIReport.substring(0, nomeDesignIReport.lastIndexOf(".")) + ".jasper";
        File arquivoIReport = new File(obterCaminhoBaseAplicacao(request, response) + File.separator + nomeJasperReportDesignIReport);
        JasperReport jasperReport = (JasperReport) JRLoader.loadObject(arquivoIReport);
        JasperPrint print = JasperFillManager.fillReport(jasperReport, getParametrosRelatorio(), jrxmlds);
        return print;
    }

    public JasperPrint gerarRelatorioJasperPrintObjeto(HttpServletRequest request, HttpServletResponse response, String nomeDesignIReport) throws Exception {

        JRDataSource jr = new JRBeanArrayDataSource(getListaObjetos().toArray());
        String nomeJasperReportDesignIReport = nomeDesignIReport.substring(0, nomeDesignIReport.lastIndexOf(".")) + ".jasper";
        File arquivoIReport = new File(obterCaminhoBaseAplicacao(request, response) + File.separator + nomeJasperReportDesignIReport);

        JasperReport jasperReport = (JasperReport) JRLoader.loadObject(arquivoIReport);

        JasperPrint print = JasperFillManager.fillReport(jasperReport, getParametrosRelatorio(), jr);

        return print;
    }

//    protected void visualizarRelatorio( HttpServletRequest request, 
//                                            HttpServletResponse response, 
//                                            String texto) throws ServletException, IOException, Exception {
//        response.setContentType("text/html;charset=ISO-8859-1");
//        
//        // Gerar relatório no padrão HTML
//        JRHtmlExporter jrhtmlexporter = new JRHtmlExporter();
//        
//        Map imagesMap = new HashMap();
//        request.getSession().setAttribute("IMAGES_MAP", imagesMap);
//        jrhtmlexporter.setParameter(JRHtmlExporterParameter.IMAGES_MAP, imagesMap);
//        jrhtmlexporter.setParameter(JRHtmlExporterParameter.IMAGES_URI,"image?image=");
//        jrhtmlexporter.setParameter(JRExporterParameter.JASPER_PRINT, print);
//        jrhtmlexporter.setParameter(JRExporterParameter.OUTPUT_WRITER, response.getWriter() );
//        jrhtmlexporter.setParameter(JRExporterParameter.CHARACTER_ENCODING, "ISO-8859-1" );
//        jrhtmlexporter.exportReport();
//    }
    protected void visualizarRelatorio(HttpServletRequest request,
            HttpServletResponse response,
            String texto) throws ServletException, IOException, Exception {
        response.setContentType("text/html;charset=UTF-8");
        PrintWriter out = response.getWriter();
        out.println(texto);
//        out.println("<head>");
//        out.println("<title>Contrato</title>");
//        out.println("</head>");
//        
//        String nomePDF = getNomeRelatorio() + ".pdf";
//        String nomeRelPDF = "relatorio" + File.separator + nomePDF;
//        File pdfFile = new File(obterCaminhoWebAplicacao(request, response) + File.separator + nomeRelPDF);
//        
//        if (pdfFile.exists()) {
//            try {
//                pdfFile.delete();
//            }
//            catch (Exception e) {
//                DateFormat formatador = DateFormat.getDateInstance(DateFormat.MEDIUM);
//                String dataStr = formatador.format(negocio.comuns.utilitarias.Calendario.hoje());
//                nomePDF = getNomeRelatorio() + dataStr + ".pdf";
//                nomeRelPDF = "relatorios" + File.separator + nomePDF;
//                pdfFile = new File(obterCaminhoWebAplicacao(request, response) + File.separator + nomeRelPDF);
//            }
//        }
//        
//        JRPdfExporter jrpdfexporter = new JRPdfExporter();
//        jrpdfexporter.setParameter(JRExporterParameter.JASPER_PRINT, print);
//        jrpdfexporter.setParameter(JRExporterParameter.OUTPUT_FILE, pdfFile );
//        
//        jrpdfexporter.exportReport();
//        
//        out.println("<frameset cols=\"*\" frameborder=\"NO\" border=\"0\" framespacing=\"0\">");
//        String urlAplicacao = request.getRequestURI().toString();
//        urlAplicacao = urlAplicacao.substring(0, urlAplicacao.lastIndexOf("/"));
//        out.println("<frame src=\"" + urlAplicacao + "/relatorio/" + nomePDF + "\" name=\"mainFrame\">");
//        out.println("</frameset>");
//        out.println("<noframes><body>");
//        out.println("</body></noframes>");
//        out.println("</html>");
        out.close();
    }

    /** Processes requests for both HTTP <code>GET</code> and <code>POST</code> methods.
     * @param request servlet request
     * @param response servlet response
     */
    protected void processRequest(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException, Exception {
        String texto = "";
        Integer contrato = UteisValidacao.converterInteiro(request.getParameter("c"));
        Integer vendaAvulsa = UteisValidacao.converterInteiro(request.getParameter("va"));
        Integer produto = UteisValidacao.converterInteiro(request.getParameter("p"));
        String parcelas = request.getParameter("cp"); //comprovante de compra
        Boolean telaNova = UteisValidacao.converterBooleano(request.getParameter("telaNova"));
        String chave = request.getParameter("k");
        Boolean exibirBotaoAssinatura = UteisValidacao.converterBooleano(request.getParameter("exibirBotaoAssinatura"));

        if (!UteisValidacao.emptyString(chave) &&
                !UteisValidacao.emptyString(parcelas)) {
            PlanoTextoPadrao planoTextoPadraoDAO = null;
            MovParcela movParcelaDAO = null;
            try (Connection con = new DAO().obterConexaoEspecifica(chave)) {
                //Conexao.guardarConexaoForJ2SE(chave, con);
                planoTextoPadraoDAO = new PlanoTextoPadrao(con);
                movParcelaDAO = new MovParcela(con);

                StringBuilder parceBuscar = new StringBuilder();
                for (String pac : parcelas.split("-")) {
                    if (!UteisValidacao.emptyString(pac)) {
                        parceBuscar.append(",").append(pac);
                    }
                }

                List<MovParcelaVO> listaParcelas = movParcelaDAO.consultar("select * from movparcela where codigo in (" + parceBuscar.toString().replaceFirst(",", "") + ") ", Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                listaParcelas.forEach(movParcelaVO -> {
                    movParcelaVO.setParcelaEscolhida(true);
                });
                texto = planoTextoPadraoDAO.gerarComprovanteCompra(listaParcelas);
            } finally {
                planoTextoPadraoDAO = null;
                movParcelaDAO = null;
            }
        } else if (!UteisValidacao.emptyString(chave) &&
                !UteisValidacao.emptyNumber(contrato)) {
            ContratoTextoPadrao contratoTextoPadraoDAO = null;
            try (Connection con = new DAO().obterConexaoEspecifica(chave)) {
                //Conexao.guardarConexaoForJ2SE(chave, con);
                contratoTextoPadraoDAO = new ContratoTextoPadrao(con);
                texto = contratoTextoPadraoDAO.consultarHtmlContrato(contrato,true, true, telaNova);
            } finally {
                contratoTextoPadraoDAO = null;
            }
        } else if (!UteisValidacao.emptyString(chave) &&
                !UteisValidacao.emptyNumber(vendaAvulsa) &&
                !UteisValidacao.emptyNumber(produto)) {
            VendaAvulsa vendaAvulsaDAO;
            PlanoTextoPadrao planoTextoPadraoDAO;
            Pessoa pessoaDAO;
            MovProduto movProdutoDAO;
            Empresa empresaDAO;
            Usuario usuarioDAO;
            MovPagamento movPagamentoDAO;
            Produto produtoDAO;
            try (Connection con = new DAO().obterConexaoEspecifica(chave)) {
                vendaAvulsaDAO = new VendaAvulsa(con);
                planoTextoPadraoDAO = new PlanoTextoPadrao(con);
                pessoaDAO = new Pessoa(con);
                movProdutoDAO = new MovProduto(con);
                empresaDAO = new Empresa(con);
                usuarioDAO = new Usuario(con);
                movPagamentoDAO = new MovPagamento(con);
                produtoDAO = new Produto(con);

                VendaAvulsaVO vendaAvulsaVO = vendaAvulsaDAO.consultarPorChavePrimaria(vendaAvulsa, Uteis.NIVELMONTARDADOS_DADOSENTIDADESUBORDINADAS);
                vendaAvulsaVO.setParcela(UteisValidacao.emptyList(vendaAvulsaVO.getMovParcelaVOs()) ? new MovParcelaVO() : vendaAvulsaVO.getMovParcelaVOs().get(0));
                ProdutoVO prod = produtoDAO.consultarPorChavePrimaria(produto, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                vendaAvulsaVO.setTextoPadrao(planoTextoPadraoDAO.consultarPorChavePrimaria(prod.getContratoTextoPadrao(), Uteis.NIVELMONTARDADOS_TODOS));
                String contratoEpoca = vendaAvulsaDAO.obterContratoProdutoTextoPadrao(vendaAvulsaVO.getCodigo(), prod.getCodigo());
                if (!UteisValidacao.emptyString(contratoEpoca)) {
                    vendaAvulsaVO.getTextoPadrao().setTexto(contratoEpoca);
                }
                vendaAvulsaVO.getParcela().setPessoa(pessoaDAO.consultarPorChavePrimaria(
                        vendaAvulsaVO.getParcela().getPessoa().getCodigo(),
                        Uteis.NIVELMONTARDADOS_TODOS));
                vendaAvulsaVO.setMovProdutoVOs(movProdutoDAO.consultarPorCodigoParcela(vendaAvulsaVO.getParcela().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
                vendaAvulsaVO.setEmpresa(
                        empresaDAO.consultarPorChavePrimaria(
                                vendaAvulsaVO.getEmpresa().getCodigo(),
                                Uteis.NIVELMONTARDADOS_TODOS));
                vendaAvulsaVO.setResponsavel(
                        usuarioDAO.consultarPorChavePrimaria(
                                vendaAvulsaVO.getResponsavel().getCodigo(),
                                Uteis.NIVELMONTARDADOS_DADOSBASICOS));

                List<MovPagamentoVO> pagamentos = movPagamentoDAO.consultarPagamentoDeUmaParcela(
                        vendaAvulsaVO.getParcela().getCodigo().intValue(), false,
                        Uteis.NIVELMONTARDADOS_TODOS);

                vendaAvulsaVO.getTextoPadrao().substituirTagsTextoPadraoVendaAvulsa(chave, con, vendaAvulsaVO, pagamentos, vendaAvulsaVO.getEmpresa().getDescMoeda(), request, telaNova, false, prod.getCodigo());
                texto = (String) request.getSession().getAttribute("textoRelatorio");
            } finally {
                vendaAvulsaDAO = null;
                planoTextoPadraoDAO = null;
                pessoaDAO = null;
                movProdutoDAO = null;
                empresaDAO = null;
                usuarioDAO = null;
                movPagamentoDAO = null;
                produtoDAO = null;
            }
        } else {
            texto = (String) request.getSession().getAttribute("textoRelatorio");
        }
        if (Boolean.TRUE.equals(exibirBotaoAssinatura)) {
            texto = adicionaBotaoAssinatura(texto, contrato, chave, request);
        }

        visualizarRelatorio(request, response, texto);
        //new ImprimirContrato(texto);
    }
       // <editor-fold defaultstate="collapsed" desc="HttpServlet methods. Click on the + sign on the left to edit the code.">

    /** Handles the HTTP <code>GET</code> method.
     * @param request servlet request
     * @param response servlet response
     */
    protected void doGet(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {
        try {
            processRequest(request, response);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /** Handles the HTTP <code>POST</code> method.
     * @param request servlet request
     * @param response servlet response
     */
    protected void doPost(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {
        try {
            processRequest(request, response);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /** Returns a short description of the servlet.
     */
    public String getServletInfo() {
        return "Short description";
    }
    // </editor-fold>

    private String adicionaBotaoAssinatura(String texto, Integer codigoContrato, String chave, HttpServletRequest request) {
        try {
            String urlSistema = "";

            if (JSFUtilities.isJSFContext()) {
                urlSistema = getUrl();
            }

            if (UteisValidacao.emptyString(urlSistema)) {
                ClientDiscoveryDataDTO dataDTO = DiscoveryMsService.urlsChave(chave);
                urlSistema = dataDTO.getServiceUrls().getZwUrl();
            }

            if (!urlSistema.startsWith("http://") && !urlSistema.startsWith("https://")) {
                urlSistema = "https://" + urlSistema;
            }

            String token = "codigoContrato:" + codigoContrato + "; chave:" + chave;
            token = Criptografia.encrypt(token, Crypt_KEY_Contrato, Crypt_ALGORITM);

            String url = urlSistema + "/faces/assinaturaDigital.jsp?token=" + token + "&whats=true";

            StringBuilder builder = new StringBuilder();
            builder.append(texto);
            builder.append("<div style='text-align: center; margin-top: 20px;'>");
            builder.append("<a href='").append(url).append("' target='_blank'>");
            builder.append("<button style='padding: 10px 20px; font-size: 16px; cursor: pointer;'>Assinar Contrato</button>");
            builder.append("</a>");
            builder.append("</div>");

            return builder.toString();
        } catch (Exception e) {
            e.printStackTrace();
            return texto;
        }
    }

    public HashMap getParametrosRelatorio() {
        return parametrosRelatorio;
    }

    public void setParametrosRelatorio(HashMap parametrosRelatorio) {
        this.parametrosRelatorio = parametrosRelatorio;
    }

    public String getNomeDesignIReport() {
        return nomeDesignIReport;
    }

    public void setNomeDesignIReport(String nomeDesignIReport) {
        this.nomeDesignIReport = nomeDesignIReport;
    }

    public String getCaminhoParserXML() {
        return caminhoParserXML;
    }

    public void setCaminhoParserXML(String caminhoParserXML) {
        this.caminhoParserXML = caminhoParserXML;
    }

    public String getXmlDados() {
        return xmlDados;
    }

    public void setXmlDados(String xmlDados) {
        this.xmlDados = xmlDados;
    }

    public String getTipoRelatorio() {
        return tipoRelatorio;
    }

    public void setTipoRelatorio(String tipoRelatorio) {
        this.tipoRelatorio = tipoRelatorio;
    }

    public String getNomeRelatorio() {
        return nomeRelatorio;
    }

    public void setNomeRelatorio(String nomeRelatorio) {
        this.nomeRelatorio = nomeRelatorio;
    }

    public String getNomeEmpresa() {
        return nomeEmpresa;
    }

    public void setNomeEmpresa(String nomeEmpresa) {
        this.nomeEmpresa = nomeEmpresa;
    }

    public String getMensagemRel() {
        return mensagemRel;
    }

    public void setMensagemRel(String mensagemRel) {
        this.mensagemRel = mensagemRel;
    }

    public String getTipoImplementacao() {
        return tipoImplementacao;
    }

    public void setTipoImplementacao(String tipoImplementacao) {
        this.tipoImplementacao = tipoImplementacao;
    }

    public List getListaObjetos() {
        return listaObjetos;
    }

    public void setListaObjetos(List listaObjetos) {
        this.listaObjetos = listaObjetos;
    }
}
