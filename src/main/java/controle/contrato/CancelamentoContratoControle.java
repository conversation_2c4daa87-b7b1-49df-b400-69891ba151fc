package controle.contrato;

import br.com.pactosolucoes.notificacao.ServicoNotificacaoPush;
import br.com.pactosolucoes.comuns.notificacao.RecursoSistema;
import br.com.pactosolucoes.comuns.util.Formatador;
import br.com.pactosolucoes.comuns.util.JSFUtilities;
import br.com.pactosolucoes.contrato.servico.impl.ContratoAssinaturaDigitalServiceImpl;
import br.com.pactosolucoes.contrato.servico.intf.ContratoAssinaturaDigitalServiceInterface;
import br.com.pactosolucoes.enumeradores.TipoParcelaCancelamento;
import controle.arquitetura.SuperControle;
import controle.arquitetura.security.AutorizacaoFuncionalidadeControle;
import controle.arquitetura.security.AutorizacaoFuncionalidadeListener;
import controle.basico.ClienteControle;
import controle.basico.clube.MensagemGenericaControle;
import controle.financeiro.CaixaControle;
import controle.financeiro.RetiradaAutomaticaControle;
import negocio.comuns.arquitetura.UsuarioPerfilAcessoVO;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.*;
import negocio.comuns.contrato.*;
import negocio.comuns.crm.ConfiguracaoEmpresaBitrixVO;
import negocio.comuns.crm.ConfiguracaoSistemaCRMVO;
import negocio.comuns.crm.LeadVO;
import negocio.comuns.financeiro.*;
import negocio.comuns.financeiro.enumerador.SituacaoRemessaEnum;
import negocio.comuns.plano.AlunoHorarioTurmaVO;
import negocio.comuns.plano.PlanoVO;
import negocio.comuns.plano.ProdutoVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.ContadorTempo;
import negocio.comuns.utilitarias.ControleConsulta;
import negocio.comuns.utilitarias.Ordenacao;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisEmail;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.comuns.utilitarias.UtilReflection;
import negocio.facade.jdbc.contrato.ContratoOperacao;
import negocio.facade.jdbc.utilitarias.Conexao;
import negocio.interfaces.plano.PlanoInterfaceFacade;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.io.FileUtils;
import org.json.JSONArray;
import org.json.JSONObject;
import org.richfaces.event.UploadEvent;
import org.richfaces.model.UploadItem;
import relatorio.controle.arquitetura.SuperControleRelatorio;
import servicos.impl.dcc.base.DCCAttEnum;
import servicos.impl.gestaoaula.GestaoAulaService;
import servicos.integracao.enumerador.TipoLeadEnum;
import servicos.integracao.impl.IntegracaoLeadGenericaServiceImpl;
import servicos.propriedades.PropsService;
import servicos.util.ExecuteRequestHttpService;

import javax.faces.model.SelectItem;
import java.io.File;
import java.nio.charset.StandardCharsets;
import java.text.ParseException;
import java.time.YearMonth;
import java.util.*;

/**
 * Classe responsável por implementar a interação entre os componentes JSF das
 * páginas contratoOperacaoForm.jsp contratoOperacaoCons.jsp) com as
 * funcionalidades da classe <code>ContratoOperacao</code>. Implemtação da
 * camada controle (Backing Bean).
 *
 * @see SuperControle
 * @see ContratoOperacao
 * @see ContratoOperacaoVO
 */
public class CancelamentoContratoControle extends SuperControleRelatorio {

    protected CancelamentoContratoVO cancelamentoContratoVO;
    private EmpresaVO empresaVO;
    private ContratoOperacaoVO contratoOperacaoVO;
    protected ContratoVO contratoVO;
    protected ClienteVO clienteVO;
    protected PlanoVO planoVO;
    protected Boolean apresentarBotoes;

    protected List listaSelectItemJustificativaOperacao;
    private JustificativaOperacaoVO justificativaOperacaoVO;

    protected List listaConsultarCliente;
    protected List listaConsultarContrato;
    protected String campoConsultarCliente;
    protected String campoConsultarContrato;
    protected String valorConsultarCliente;
    protected String valorConsultarContrato;
    protected String consultarCliente;
    protected List consultarContrato;
    protected Boolean abrirRichConfirmacaoTransferencia;
    protected Boolean abrirRichConfirmacaoLiberacao;
    protected String msgConfirmacaoLiberacao;
    protected PlanoInterfaceFacade planoFacade;
    private boolean planoVencido;
    private List<ReciboPagamentoTO> recibos;
    private boolean abrirRichConfirmacaoManual;
    private boolean autorizado = false;
    private ReciboDevolucaoVO reciboDevolucao;
    private String onCompleteQuitacao = "";
    private String onCompleteDevolucao = "";
    private boolean alterarValor;
    private boolean usuarioDesejaAlterarMulta;
    private Double valorTotalAlteracaoValor = 0.0;
    private Double valorAlteracaoPercentualMulta = 0.0;
    private Boolean liberarMultaCustos;
    private Boolean retirarAutomatico = Boolean.FALSE;
    private String validarDataRetroativa;
    private boolean permiteAlterarCancelamento = false;
    private boolean alterarCancelamento = false;
    private boolean simular = false;
    private Date dataFinalArmario = null;
    private ContratoDuracaoCreditoTreinoVO contratoDuracaoCreditoTreinoCancelar;
    private String nomeArquivoComprovanteOperacao;
    private boolean liberarMultaCancelamentoAntecipado = false;
    private boolean liberarProximaParcelaCancelamentoAntecipado = false;
    private String modalMensagemGenerica;
    private boolean deveGravarAviso;
    private String mensagemEmailCancelamentoAntecipado;
    private boolean finalizouCancelamento = false;
    private CartaoCreditoVO cartaoDevolucao = new CartaoCreditoVO();
    private MovPagamentoVO movPagamentoVO = new MovPagamentoVO();
    private boolean apresentarDadosEstornoParcial = true;
    private boolean apresentarWarningValor = false;
    private boolean apresentarBotoesTransferenciaContratoDias = true;
    private boolean gerarQuitacaoCancelamentoRemanescente = false;
    private boolean apresentarMensagemTipoParcelaCancelamento = false;
    private boolean apresentarMensagemZerarValorCancelamento = false;
    private boolean apresentarUploadArquivo = false;
    private File arquivoAnexo;
    private String onCompleteArquivoAnexo ="";
    private String extensaoArquivoAnexo = "";
    private boolean dataFinalContratoEmEdicao = true;
    private Date novaDataFinalContrato;

    /**
     * Interface <code>ContratoOperacaoInterfaceFacade</code> responsável pela
     * interconexão da camada de controle com a camada de negócio. Criando uma
     * independência da camada de controle com relação a tenologia de
     * persistência dos dados (DesignPatter: Façade).
     */
    public CancelamentoContratoControle() throws Exception {
        obterUsuarioLogado();
        inicializarFacades();
        //novo();
        setControleConsulta(new ControleConsulta());
    }

    public void calcularDevolucao() throws Exception {
        cancelamentoContratoVO.setValorASerDevolvido((double) 0);
        cancelamentoContratoVO.setValorTotalPagoPeloCliente((double) 0);
        cancelamentoContratoVO.setSaldoContaCorrenteCliente((double) 0);
        getFacade().getZWFacade().obterSaldoContaCorrenteCliente(cancelamentoContratoVO, getContratoVO());
        cancelamentoContratoVO.setValorTotalPagoPeloCliente(getFacade().getZWFacade().obterValorPagoPeloCliente(cancelamentoContratoVO, getContratoVO()));

        cancelamentoContratoVO.obterValorFinalASerDevolvido("DE", getContratoVO());
        cancelamentoContratoVO.setValorSerRecebidoPeloCliente(cancelamentoContratoVO.getValorASerDevolvido());
        setLiberarMultaCustos(false);
        setLiberarMultaCancelamentoAntecipado(false);
        setLiberarProximaParcelaCancelamentoAntecipado(false);
        setMensagemDetalhada("");
    }

    public void inicializarUsuarioLogado(UsuarioVO usuarioVO) throws Exception {
        if (usuarioVO == null) {
            usuarioVO = getUsuarioLogado();
        }
        cancelamentoContratoVO.getResponsavelCancelamento().setCodigo(usuarioVO.getCodigo());
        cancelamentoContratoVO.getResponsavelCancelamento().setUsername(usuarioVO.getUsername());
        cancelamentoContratoVO.getResponsavelCancelamento().setUserOamd(usuarioVO.getUserOamd());
    }

    public void marcarSimular() {
        if (!simular) {
            existePagamentoConjunto();
        }
    }

    public String calculoCancelamentoCliente() {
        return calculoCancelamentoClienteGeral(false,null);
    }

    public String calculoCancelamentoClienteGeral(boolean servlet, ClienteControle clienteControle) {
        try {
            if (justificativaOperacaoVO.getNecessarioAnexarComprovante()) {
                if (getArquivoAnexo() == null) {
                    throw new ConsistirException("Não é possível prosseguir o cancelamento sem anexar um arquivo.");
                }
            }
            cancelamentoContratoVO.setJustificativaOperacao(justificativaOperacaoVO);

            cancelamentoContratoVO.setListaContratos(new ArrayList<>());
            cancelamentoContratoVO.setTipoDevolucaoCancelamento("");
            if (!servlet) {
                clienteControle = (ClienteControle) context().getExternalContext().getSessionMap().get("ClienteControle");
            }
            if (clienteControle != null) {
                setContratoVO(getFacade().getContrato().consultarPorChavePrimaria(
                        getContratoVO().getCodigo(), Uteis.NIVELMONTARDADOS_TODOS));
                ValidacaoContratoOperacao.validarSeExisteTrancamentoSemRetorno(clienteControle.getContratoVO());
                ValidacaoContratoOperacao.validarSeExisteTrancamentoDepoisDoCancelamento(clienteControle.getContratoVO(), cancelamentoContratoVO.getDataCancelamento());
                setErro(false);
                cancelamentoContratoVO.setMensagemErro(false);
                setContratoVO(clienteControle.getContratoVO());
                cancelamentoContratoVO.setContratoCancelar(getContratoVO());
                if (this.contratoVO.isVendaCreditoTreino()) {
                    cancelamentoContratoVO.setContratoVendaCredito(true);
                    this.contratoDuracaoCreditoTreinoCancelar = getFacade().getContratoDuracaoCreditoTreino().consultarDadosParaCancelamentoContrato(this.contratoVO, clienteControle.getContratoVO().getPlano().getCodigo(), getContratoVO().getEmpresa().isRetrocederValorMensalPlanoCancelamento());
                    cancelamentoContratoVO.setValorDevolverSaldoTransferenciaCredito(this.contratoDuracaoCreditoTreinoCancelar.getTotalTransferenciaSaldo());
                    cancelamentoContratoVO.setContratoTransferenciaSaldo(this.contratoDuracaoCreditoTreinoCancelar.getContratoTransferenciaSaldo());
                }
                cancelamentoContratoVO.validarDados(getCancelamentoContratoVO(), "telaCancelamento", getContratoVO(), isAlterarCancelamento());
                cancelamentoContratoVO.getListaContratos().add(getContratoVO());
                Double valorProdutosDevolver = validarProdutoDevolverCancelamento();
                cancelamentoContratoVO.setValorTotalSomaProdutoContratos(getContratoVO().getSomaProduto() - valorProdutosDevolver);
                cancelamentoContratoVO.setValorTotalSomaProdutoContratosDevolver(valorProdutosDevolver);
                obterContratosCancelados();
                cancelamentoContratoVO.setNrDiasContrato(getFacade().getZWFacade().obterNrDiasContrato(getContratoVO()));
                cancelamentoContratoVO.setNrDiasUtilizadosPeloClienteContrato(getFacade().getZWFacade().obterNrDiasUtilizadoAcademiaAteDataEspecifica(getContratoVO(), cancelamentoContratoVO.getDataCancelamento()));
                cancelamentoContratoVO.setNrDiasRestamContrato(getContratoVO().obterNrDiasRestantesProFinalDoContrato(cancelamentoContratoVO.getNrDiasContrato(), cancelamentoContratoVO.getNrDiasUtilizadosPeloClienteContrato()));
                if (cancelamentoContratoVO.getConfiguracaoSesc()) {
                    cancelamentoContratoVO.setNrDiasUtilizadosPeloClienteContrato(cancelamentoContratoVO.getNrDiasUtilizadosPeloClienteContrato() - 1);
                    cancelamentoContratoVO.setNrDiasRestamContrato(cancelamentoContratoVO.getNrDiasRestamContrato() + 1);
                }
                // cálculo com base no valor base do contrato
                calcularValorBaseDoContrato();
                if (getContratoVO().getEmpresa().isRetrocederValorMensalPlanoCancelamento()) {
                    // cálculo com base no valor mensal do contrato
                    calcularValorMensalDoContrato();
                } else {
                    calcularValorBaseDoContratoDevolucao();
                }
                if (!getContratoVO().getPlano().getRecorrencia()) {
                    getContratoVO().setCancelamentoAntecipadoEmpresa(false);
                    getContratoVO().setCancelamentoObrigatoriedadePagamentoEmpresa(false);
                } else {
                    if (getContratoVO().isCancelamentoAntecipado() || getContratoVO().isCancelamentoObrigatoriedadePagamento()) {
                        cancelamentoContratoVO.setTipoDevolucaoCancelamento("DE");
                        obterValoresCancelamento();
                    }
                }
            }
            setMensagemDetalhada("");
            setMensagem("");
            setMensagemID("");
            setSucesso(false);
            setErro(false);

            if (getContratoVO().isCancelamentoObrigatoriedadePagamento() && getContratoVO().getPlano().getRecorrencia()) {
                validarConfirmacaoCancelamento();
                //existePagamentoConjunto();
                //finalizarCancelamentoComCheque();
                return "finalizarCancelamento";
            } else {
                return "resultadoCalculo";
            }
        } catch (Exception e) {
            cancelamentoContratoVO.setMensagemErro(true);
            montarErro(e);
            return "";
        }

    }

    public String validarTransferenciaCliente() {
        try {
            cancelamentoContratoVO.validarDados(getCancelamentoContratoVO(), "telaTransferencia", getContratoVO(), isAlterarCancelamento());
            setListaConsultarCliente(new ArrayList());
            setCampoConsultarCliente("");
            setValorConsultarCliente("");
            setListaConsultarContrato(new ArrayList());
            setCampoConsultarContrato("");
            setValorConsultarContrato("");
            setMensagemDetalhada("");
            cancelamentoContratoVO.setMensagemErro(false);
            return "resultadoTransferencia";

        } catch (Exception e) {
            cancelamentoContratoVO.setMensagemErro(true);
            setMensagemID("Atenção !");
            setMensagemDetalhada(e.getMessage());
            montarErro(e);
            return "";
        }
    }

    public void calcularValorBaseDoContrato() throws Exception {
        cancelamentoContratoVO.setValorBaseContrato(getContratoVO().getValorBaseCalculo());
        cancelamentoContratoVO.setValorDiaContratoValorBase(getFacade().getZWFacade().obterValorDiaContratoValorBase(getContratoVO()));
        if (this.contratoVO.isVendaCreditoTreino()) {
            if (contratoVO.getPlano() != null && contratoVO.getPlano().isCreditoTreinoNaoCumulativo()) {
                this.contratoDuracaoCreditoTreinoCancelar.setQuantidadeCreditoUtilizado(getFacade().getControleCreditoTreino().consultarUtilizacaoCreditoPorContrato(this.contratoVO.getCodigo()));
                cancelamentoContratoVO.setValorUtilizadoPeloClienteBase(this.contratoDuracaoCreditoTreinoCancelar.getValorUtilizadoTotal());
            } else {
                cancelamentoContratoVO.setValorUtilizadoPeloClienteBase(this.contratoDuracaoCreditoTreinoCancelar.getValorUtilizadoMensal());
            }
        } else {
            cancelamentoContratoVO.setValorUtilizadoPeloClienteBase(Uteis.arredondarForcando2CasasDecimais(cancelamentoContratoVO.getNrDiasUtilizadosPeloClienteContrato() * cancelamentoContratoVO.getValorDiaContratoValorBase()));
            if (cancelamentoContratoVO.getConfiguracaoSesc()) {
                List<MovParcelaVO> parcelasCanceladas = getFacade().getMovParcela().consultarParcelasCanceladas(getContratoVO(), cancelamentoContratoVO.getDataCancelamento());
                double valorCancelado = parcelasCanceladas.stream().mapToDouble(MovParcelaVO::getValorParcela).sum();
                cancelamentoContratoVO.setValorUtilizadoPeloClienteBase(cancelamentoContratoVO.getValorUtilizadoPeloClienteBase() - valorCancelado);
            }
        }
        cancelamentoContratoVO.setValorCreditoRestanteContratoValorBase(
                getContratoVO().obterCreditoRestanteContrato(
                        cancelamentoContratoVO.getNrDiasUtilizadosPeloClienteContrato(),
                        cancelamentoContratoVO.getValorDiaContratoValorBase(), false));

        if (cancelamentoContratoVO.getListaContratos().size() != 1) {
            cancelamentoContratoVO.setValorCreditoRestanteContratoValorBase(cancelamentoContratoVO.getValorCreditoRestanteContratoValorBase() + cancelamentoContratoVO.getValorTotalBaseContratoRenovacao());
        }
    }

    public Double validarProdutoDevolverCancelamento() {
        Double valorSomarADevolver = 0.0;
        try {
            List<ProdutoDevolverCancelamentoEmpresaVO> produtosDevolverCancelamentoEmpresaVO = getFacade().getProdutoDevolverCancelamentoEmpresa().consultarPorEmpresa(getEmpresaLogado().getCodigo(), Uteis.NIVELMONTARDADOS_TODOS);
            if (produtosDevolverCancelamentoEmpresaVO != null && produtosDevolverCancelamentoEmpresaVO.size() > 0) {
                ClienteControle cc = (ClienteControle) context().getExternalContext().getSessionMap().get("ClienteControle");
                String ret = ExecuteRequestHttpService.executeHttpRequestGETEncode(
                        PropsService.getPropertyValue(getKey(), PropsService.urlTreino) + "/prest/config/" + getKey() + "/obterProdutoAvaliacao?matricula=" + cc.getClienteVO().getMatricula(),
                        new HashMap<>(), "UTF-8");
                Integer codigoProdutoAvaliacao = 0;
                if (!UteisValidacao.emptyString(ret) && new JSONObject(ret).has("return")) {
                    codigoProdutoAvaliacao = new JSONObject(ret).getInt("return");
                }
                List<ContratoPlanoProdutoSugeridoVO> produtosContrato = getFacade().getContratoPlanoProdutoSugerido().consultarContratoPlanoProdutoSugeridos(getContratoVO().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                for (ProdutoDevolverCancelamentoEmpresaVO obj : produtosDevolverCancelamentoEmpresaVO) {
                    for (ContratoPlanoProdutoSugeridoVO obj2 : produtosContrato) {
                        if (obj.getProdutoVO().getCodigo().equals(obj2.getPlanoProdutoSugerido().getProduto().getCodigo()) &&
                                obj.getProdutoVO().getCodigo().equals(codigoProdutoAvaliacao)) {
                            String ret2 = ExecuteRequestHttpService.executeHttpRequestGETEncode(
                                    PropsService.getPropertyValue(getKey(), PropsService.urlTreino) + "/prest/avaliacao/" + getKey() + "/avaliacaoDentroPeriodo?matricula=" + cc.getClienteVO().getMatricula() +
                                            "&dataInicio=" + Uteis.getData(getContratoVO().getVigenciaDe(), "br") + "&dataFim=" + Uteis.getData(getContratoVO().getVigenciaAteAjustada(), "br"),
                                    new HashMap<>(), "UTF-8");
                            if (!UteisValidacao.emptyString(ret2) && new JSONObject(ret2).has("return") &&
                                    !new JSONObject(ret2).getBoolean("return")) {
                                valorSomarADevolver += obj2.getValorFinalProduto();
                            }
                        } else if (obj.getProdutoVO().getCodigo().equals(obj2.getPlanoProdutoSugerido().getProduto().getCodigo()) &&
                                !obj.getProdutoVO().getCodigo().equals(codigoProdutoAvaliacao)) {
                            valorSomarADevolver += obj2.getValorFinalProduto();
                        }
                    }

                }
            }
            return valorSomarADevolver;
        } catch (Exception ex) {
            ex.printStackTrace();
            return valorSomarADevolver;
        }
    }

    public void calcularValorBaseDoContratoDevolucao() throws Exception {
        cancelamentoContratoVO.setValorBaseContrato(getContratoVO().getValorBaseCalculo());
        cancelamentoContratoVO.setValorDiaContratoValorBase(getFacade().getZWFacade().obterValorDiaContratoValorBase(getContratoVO()));
        if (this.contratoVO.isVendaCreditoTreino()) {
            if (this.contratoVO.getPlano().isCreditoTreinoNaoCumulativo()){
                cancelamentoContratoVO.setValorUtilizadoPeloClienteMensal(this.contratoDuracaoCreditoTreinoCancelar.getValorUtilizadoTotal());
            } else {
                cancelamentoContratoVO.setValorUtilizadoPeloClienteMensal(this.contratoDuracaoCreditoTreinoCancelar.getValorUtilizadoMensal());
            }
            cancelamentoContratoVO.setValorCreditoRestanteContratoVendaCreditoSemTaxaEMulta(getValorCreditoTreinoRestante());
        } else {
            cancelamentoContratoVO.setValorUtilizadoPeloClienteMensal(Uteis.arredondarForcando2CasasDecimais(cancelamentoContratoVO.getNrDiasUtilizadosPeloClienteContrato() * cancelamentoContratoVO.getValorDiaContratoValorBase()));
            cancelamentoContratoVO.setValorCreditoRestanteContratoValorMensalSemTaxaEMulta(
                    getContratoVO().obterCreditoRestanteContrato(
                            cancelamentoContratoVO.getNrDiasUtilizadosPeloClienteContrato(),
                            cancelamentoContratoVO.getValorDiaContratoValorBase(), false));
        }

        if (cancelamentoContratoVO.getConfiguracaoSesc()) {
            double valorDia = 0.0;
            double valorUtilizado = 0.0;

            if (cancelamentoContratoVO.getListaParcelas().isEmpty()) {
                cancelamentoContratoVO.setListaParcelas(getFacade().getMovParcela().consultarParcelaSomentePlanoMensalOrMatriculaOrRenovacaoOrRematricaPorContrato(
                        getContratoVO().getCodigo(), cancelamentoContratoVO.isCancelarParcelaAnuidade(), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA));
            }

            MovParcelaVO parcelaAtual = null;
            Ordenacao.ordenarLista(cancelamentoContratoVO.getListaParcelas(), "dataVencimento");
            for (MovParcelaVO parcela : cancelamentoContratoVO.getListaParcelas()) {
                if (Calendario.menorOuIgual(parcela.getDataVencimento(), cancelamentoContratoVO.getDataCancelamento())) {
                    long nrDiasUtilizados = Uteis.nrDiasEntreDatas(parcela.getDataVencimento(), cancelamentoContratoVO.getDataCancelamento());
                    parcelaAtual = parcela;
                    YearMonth yearMonth = YearMonth.of(Uteis.getAnoData(parcela.getDataVencimento()), Uteis.getMesData(parcela.getDataVencimento()));
                    int totalDiasMes = yearMonth.lengthOfMonth();
                    if (nrDiasUtilizados >= totalDiasMes) {
                        valorUtilizado += parcela.getValorParcela();
                    }
                }
            }

            if (parcelaAtual == null && cancelamentoContratoVO.getListaParcelas().size() > 0) {
                parcelaAtual = cancelamentoContratoVO.getListaParcelas().get(0);
            }

            if (parcelaAtual != null) {
                valorDia = Uteis.arredondarForcando2CasasDecimais(parcelaAtual.getValorParcela() / 30);
                long diasUsadosMesAtual = Uteis.nrDiasEntreDatasSemHoraZerada(parcelaAtual.getDataVencimento(), cancelamentoContratoVO.getDataCancelamento());
                valorUtilizado += Uteis.arredondarForcando2CasasDecimais(valorDia * diasUsadosMesAtual);
            }
            cancelamentoContratoVO.setValorDiaContratoValorBase(valorDia);
            if (cancelamentoContratoVO.getConfiguracaoSesc()) {
                List<MovParcelaVO> parcelasCanceladas = getFacade().getMovParcela().consultarParcelasCanceladas(getContratoVO(), cancelamentoContratoVO.getDataCancelamento());
                double valorCancelado = parcelasCanceladas.stream().mapToDouble(MovParcelaVO::getValorParcela).sum();
                valorUtilizado = valorUtilizado - valorCancelado;
            }
            valorUtilizado = (valorUtilizado - validarProdutoDevolverCancelamento());
            cancelamentoContratoVO.setValorUtilizadoPeloClienteBase(valorUtilizado);
            cancelamentoContratoVO.setValorUtilizadoPeloClienteMensal(valorUtilizado);
            cancelamentoContratoVO.setValorCreditoRestanteContratoValorMensalSemTaxaEMulta(cancelamentoContratoVO.getValorBaseContrato() - valorUtilizado);
        }

        if (cancelamentoContratoVO.getNrDiasUtilizadosPeloClienteContrato() > 7 || !contratoVO.getEmpresa().isIsentarCancelamento7Dias()) {
            obterValorTaxaCancelamentoEMultaPercentual();
        } else {
            cancelamentoContratoVO.setValorTaxaCancelamento(0.0);
            cancelamentoContratoVO.setPercentualMultaCancelamento(0.0);
        }

    }

    public void calcularValorMensalDoContrato() {
        cancelamentoContratoVO.setValorMensalContrato(getContratoVO().obterValorContratoReferenteMensal());
        cancelamentoContratoVO.setValorDiaContratoValorMensal(
                getContratoVO().obterValorDiaContratoValorMensal(cancelamentoContratoVO.getValorMensalContrato()));
        double valorFinalTemp = 0.0;
        if (this.contratoVO.isVendaCreditoTreino()) {
            if (contratoVO.getPlano().isCreditoTreinoNaoCumulativo()) {
                cancelamentoContratoVO.setValorUtilizadoPeloClienteMensal(this.contratoDuracaoCreditoTreinoCancelar.getValorUtilizadoTotal());
            } else {
                cancelamentoContratoVO.setValorUtilizadoPeloClienteMensal(this.contratoDuracaoCreditoTreinoCancelar.getValorUtilizadoMensal());
            }
            cancelamentoContratoVO.setValorCreditoRestanteContratoVendaCreditoSemTaxaEMulta(getValorCreditoTreinoRestante());
            cancelamentoContratoVO.setCredito(cancelamentoContratoVO.getValorCreditoRestanteContratoVendaCreditoSemTaxaEMulta() > 0.0);
            cancelamentoContratoVO.setValorCreditoRestanteContratoVendaCreditoSemTaxaEMulta(Uteis.arredondarForcando2CasasDecimais(cancelamentoContratoVO.getValorCreditoRestanteContratoVendaCreditoSemTaxaEMulta()));
            valorFinalTemp = cancelamentoContratoVO.getValorCreditoRestanteContratoVendaCreditoSemTaxaEMulta();
        } else {
            cancelamentoContratoVO.setValorUtilizadoPeloClienteMensal(Uteis.arredondarForcando2CasasDecimais(cancelamentoContratoVO.getNrDiasUtilizadosPeloClienteContrato() * cancelamentoContratoVO.getValorDiaContratoValorMensal()));
            cancelamentoContratoVO.setValorCreditoRestanteContratoValorMensalSemTaxaEMulta(getContratoVO().obterCreditoRestanteContrato(cancelamentoContratoVO.getNrDiasUtilizadosPeloClienteContrato(), cancelamentoContratoVO.getValorDiaContratoValorMensal(), true));
            cancelamentoContratoVO.setCredito(cancelamentoContratoVO.getValorCreditoRestanteContratoValorMensalSemTaxaEMulta() > 0.0);
            cancelamentoContratoVO.setValorCreditoRestanteContratoValorMensalSemTaxaEMulta(Uteis.arredondarForcando2CasasDecimais(cancelamentoContratoVO.getValorCreditoRestanteContratoValorMensalSemTaxaEMulta()));
            valorFinalTemp = cancelamentoContratoVO.getValorCreditoRestanteContratoValorMensalSemTaxaEMulta();
        }

        if (cancelamentoContratoVO.getNrDiasUtilizadosPeloClienteContrato() > 7 || !contratoVO.getEmpresa().isIsentarCancelamento7Dias()) {
            obterValorTaxaCancelamentoEMultaPercentual();
        } else {
            cancelamentoContratoVO.setValorTaxaCancelamento(0d);
            cancelamentoContratoVO.setPercentualMultaCancelamento(0d);
        }

        cancelamentoContratoVO.setValorCreditoRestanteContratoValorMensal(Uteis.arredondarForcando2CasasDecimais(valorFinalTemp));
        if (cancelamentoContratoVO.getListaContratos().size() != 1) {
            cancelamentoContratoVO.setValorCreditoRestanteContratoValorMensal(cancelamentoContratoVO.getValorCreditoRestanteContratoValorMensal() + cancelamentoContratoVO.getValorTotalBaseContratoRenovacao());
        }
    }

    public void obterContratosCancelados() throws Exception {
        if (getContratoVO().getContratoResponsavelRenovacaoMatricula() != 0) {
            ContratoVO objContratoVO = getFacade().getContrato().consultarPorChavePrimaria(getContratoVO().getContratoResponsavelRenovacaoMatricula(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            if (objContratoVO.getSituacaoRenovacao().equals("AN")) {
                cancelamentoContratoVO.getListaContratos().add(objContratoVO);
                cancelamentoContratoVO.setValorTotalBaseContratoRenovacao(getFacade().getZWFacade().obterValorPagoPeloCliente(cancelamentoContratoVO, objContratoVO));
                cancelamentoContratoVO.setValorTotalSomaProdutoContratos(cancelamentoContratoVO.getValorTotalSomaProdutoContratos() + objContratoVO.getSomaProduto());
            }
            if (objContratoVO.getContratoResponsavelRenovacaoMatricula() != 0) {
                obterContratosCanceladosRenovacoes(objContratoVO);
            }
        }
    }

    public void obterContratosCanceladosRenovacoes(ContratoVO obj) throws Exception {
        ContratoVO contrato = getFacade().getContrato().consultarPorChavePrimaria(obj.getContratoResponsavelRenovacaoMatricula(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        if (contrato.getSituacaoRenovacao().equals("AN")) {
            cancelamentoContratoVO.getListaContratos().add(contrato);
            cancelamentoContratoVO.setValorTotalBaseContratoRenovacao(cancelamentoContratoVO.getValorTotalBaseContratoRenovacao() + getFacade().getZWFacade().obterValorPagoPeloCliente(cancelamentoContratoVO, contrato));
            cancelamentoContratoVO.setValorTotalSomaProdutoContratos(cancelamentoContratoVO.getValorTotalSomaProdutoContratos() + contrato.getSomaProduto());
        }
        if (contrato.getContratoResponsavelRenovacaoMatricula() != 0) {
            obterContratosCanceladosRenovacoes(contrato);
        }
    }

    public void obterValoresCancelamento() throws Exception {
        boolean retrocederValor = getContratoVO().getEmpresa().isRetrocederValorMensalPlanoCancelamento();
        if (this.contratoVO.isVendaCreditoTreino()) {
            this.contratoDuracaoCreditoTreinoCancelar = getFacade().getContratoDuracaoCreditoTreino().consultarDadosParaCancelamentoContrato(this.contratoVO, this.contratoVO.getPlano().getCodigo(), retrocederValor);
            calcularValorBaseDoContrato();
        }
        if (cancelamentoContratoVO.isTransferencia()) {
            retrocederValor = false;
            obterCancelamentoPorTransferencia();
        } else {
            cancelamentoContratoVO.isDevolucao();
            obterCancelamentoPorDevolucao();
        }
    }

    public void obterCancelamentoPorTransferencia() throws Exception {

        if (this.contratoVO.isVendaCreditoTreino()) {
            StringBuilder msg = new StringBuilder();
            msg.append("A TRANSFERÊNCIA é feita no valor base do contrato que é de  " + " "
                    + getEmpresaLogado().getMoeda()).append(Uteis.arrendondarForcando2CadasDecimaisComVirgula(cancelamentoContratoVO.getValorBaseContrato()))
                    .append(". No caso do cancelamento quando é feito a transferência o cliente pode optar por transferir a quantidade do valor restante para outra conta na academia. Segue abaixo os valores: ");
            cancelamentoContratoVO.setMensagemDevolucao(msg.toString());
        } else {

            String mensagemTemp = "A TRANSFERÊNCIA é feita no valor base do contrato que é de  " + " " + getEmpresaLogado().getMoeda()
                    + Uteis.arrendondarForcando2CadasDecimaisComVirgula(cancelamentoContratoVO.getValorBaseContrato())
                    + ". No caso do cancelamento quando é feito a transferência "
                    + "o cliente pode optar por transferir a quantidade do valor restante para outra conta na academia ou transferir esse valor em números de dias para outra pessoa."
                    + " Segue abaixo os valores: ";
            cancelamentoContratoVO.setMensagemDevolucao(mensagemTemp);
        }
    }

    public void obterCancelamentoPorDevolucao() throws Exception {
        StringBuilder msgMulta = new StringBuilder();
        msgMulta.append("No caso do cancelamento quando é feito a devolução, é cobrado deste montante os CUSTOS ADMINISTRATIVOS")
                .append(" no valor de " + getEmpresaLogado().getMoeda()).append(Uteis.arrendondarForcando2CadasDecimaisComVirgula(cancelamentoContratoVO.getValorTaxaCancelamento()))
                .append(" e também é cobrado uma MULTA PELO CANCELAMENTO no valor de ")
                .append(Uteis.arrendondarForcando2CadasDecimaisComVirgula(cancelamentoContratoVO.getPercentualMultaCancelamento()))
                .append(" % do crédito restante." + " Segue abaixo os valores cobrados: ");

        String mensagemTemp = "A DEVOLUÇÃO é feita com base "
                + (getContratoVO().getEmpresa().isRetrocederValorMensalPlanoCancelamento()
                ? "no valor mensal do Plano que é de " + getEmpresaLogado().getMoeda() + " " + Uteis.arrendondarForcando2CadasDecimaisComVirgula(cancelamentoContratoVO.getValorMensalContrato())
                : "no valor base do contrato que é de " + getEmpresaLogado().getMoeda() + " " + Uteis.arrendondarForcando2CadasDecimaisComVirgula(cancelamentoContratoVO.getValorBaseContrato()))
                + ". " + msgMulta.toString();
        if (this.contratoVO.isVendaCreditoTreino()) {
            StringBuilder msgCredito = new StringBuilder();
            msgCredito.append("A DEVOLUÇÃO é feita com base na quantidade de créditos utilizados. Será considerado o valor unitário de '");
            msgCredito.append(Formatador.formatarValorMonetario(this.contratoDuracaoCreditoTreinoCancelar.getContratoDuracaoCreditoTreinoBaseCalculo().getValorUnitario()));
            msgCredito.append("' que é o valor utilizado para a venda de '").append(this.contratoDuracaoCreditoTreinoCancelar.getContratoDuracaoCreditoTreinoBaseCalculo().getQuantidadeCreditoCompra()).append("' créditos. ");
            msgCredito.append(msgMulta.toString());
            mensagemTemp = msgCredito.toString();
        }
        cancelamentoContratoVO.setMensagemDevolucao(mensagemTemp);
    }

    public void obterValorTaxaCancelamentoEMultaPercentual() {
        try {
            PlanoVO objPlanoVO = getFacade().getPlano().consultarPorChavePrimaria(getContratoVO().getPlano().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            ProdutoVO objProdutoVO = getFacade().getProduto().consultarPorChavePrimaria(objPlanoVO.getProdutoTaxaCancelamento().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            cancelamentoContratoVO.setValorTaxaCancelamento(objProdutoVO.getValorFinal());
            if ((getContratoVO().getEmpresa().isNaoCobrarMultaDeContratoRenovado() && getContratoVO().isContratoRenovacao()) ||
                    (getContratoVO().getEmpresa().isNaoCobrarMultaDeTodasParcelasPagas() && getContratoVO().isTodasAsParcelasEstaoPagas()) ||
                    justificativaOperacaoVO.getIsentarMultaCancelamento()) {
                setLiberarMultaCustos(true);
                getCancelamentoContratoVO().setPercentualMultaCancelamento(Uteis.arredondarForcando2CasasDecimais(0.00D));
                getCancelamentoContratoVO().setValorMultaProximaParcelaCancelamentoAntecipado(Uteis.arredondarForcando2CasasDecimais(0.00D));
                getCancelamentoContratoVO().setValorMultaRestanteCancelamentoAntecipado(Uteis.arredondarForcando2CasasDecimais(0.00D));
            } else {
                cancelamentoContratoVO.setPercentualMultaCancelamento(objPlanoVO.getPercentualMultaCancelamento());
            }
        } catch (Exception e) {
            cancelamentoContratoVO.setMensagemErro(true);
            setMensagemID("");
            setMensagemDetalhada(e.getMessage());
        }
    }

    public String listarPagamento() {
        try {
            usuarioDesejaAlterarMulta = false;
            cancelamentoContratoVO.setMensagemErro(false);
            cancelamentoContratoVO.validarDados(getCancelamentoContratoVO(), "telaCancelamentoCalculo", getContratoVO(), isAlterarCancelamento());
            cancelamentoContratoVO.setCancelarParcelaAnuidade(true);
            empresaVO = getFacade().getEmpresa().consultarPorChavePrimaria(contratoVO.getEmpresa().getCodigo(), Uteis.NIVELMONTARDADOS_GESTAOREMESSA);

            if (cancelamentoContratoVO.isDevolucao()) {
                if (getContratoVO().getEmpresa().isRetrocederValorMensalPlanoCancelamento()) {
                    calcularValorMensalDoContrato();
                } else {
                    calcularValorBaseDoContratoDevolucao();
                }
                calcularDevolucao();

                if (getContratoVO().isCancelamentoAntecipado() || getContratoVO().isCancelamentoObrigatoriedadePagamento()) {
                    getFacade().getZWFacade().calcularCancelamentoAntecipado(cancelamentoContratoVO, getContratoVO(), isLiberarProximaParcelaCancelamentoAntecipado(), isLiberarMultaCancelamentoAntecipado());
                }

                return "finalizarCancelamento";
            } else if (cancelamentoContratoVO.isTransferencia()) {
                cancelamentoContratoVO.setValorASerDevolvido((double) 0);
                cancelamentoContratoVO.setValorTotalPagoPeloCliente((double) 0);
                cancelamentoContratoVO.setSaldoContaCorrenteCliente((double) 0);
                getFacade().getZWFacade().obterSaldoContaCorrenteCliente(cancelamentoContratoVO, getContratoVO());
                cancelamentoContratoVO.setValorTotalPagoPeloCliente(getFacade().getZWFacade().obterValorPagoPeloCliente(cancelamentoContratoVO, getContratoVO()));
                cancelamentoContratoVO.obterValorFinalASerDevolvido("TR", getContratoVO());
                cancelamentoContratoVO.setTipoTranferenciaCancelamento("");
                setMensagemDetalhada("");
                return "cancelamentoTransferencia";
            }
            return "";
        } catch (Exception e) {
            cancelamentoContratoVO.setMensagemErro(true);
            setErro(true);
            setSucesso(false);
            setMensagemDetalhada("msg_erro", e.getMessage());
            return "";
        }
    }

    public String getMensagemCancelamentoClienteTransferido() {
        return "Esta operação irá refletir nos dados financeiros na unidade de origem do aluno: "
                + "<div style='font-weight: bold; margin-top: 10px; margin-bottom: 10px'>" + getContratoVO().getEmpresa().getNome() + "</div>"
                + "Deseja prosseguir?";
    }

    private boolean deveExibirConfirmacaoDeAlunoTranferido() throws Exception {
        return getContratoVO().foiTransferidoDeEmpresa();
    }

    public String validarConfirmacaoCancelamento() {
        try {
            setAlterarValor(false);
            if (this.deveExibirConfirmacaoDeAlunoTranferido()) {
                setMsgAlert("");
                MensagemGenericaControle control = (MensagemGenericaControle) getControlador(MensagemGenericaControle.class);
                control.setMensagemDetalhada("", "");
                setMsgAlert("Richfaces.showModalPanel('mdlMensagemGenerica');");
                limparMsg();
                control.init("Cancelamento de Contrato", getMensagemCancelamentoClienteTransferido(), this,
                        "lancarAvisoParaEmpresaOrigemEListarPagamento", "", null, "", "panelConteudo");
            } else {
                return listarPagamento();
            }
        } catch (Exception e) {
            cancelamentoContratoVO.setMensagemErro(true);
            setErro(true);
            setSucesso(false);
            montarErro(e.getMessage());
        }

        return "";
    }

    // Método invocado via reflexão pela classe MensagemGenericaControle
    public String lancarAvisoParaEmpresaOrigemEListarPagamento() throws Exception {
        deveGravarAviso = true;
        return listarPagamento();
    }

    public void finalizarCancelamentoComCheque() {
        try {
            setOnCompleteQuitacao("");
            setMensagemEmailCancelamentoAntecipado("");
            if (cancelamentoContratoVO.getDevolucaoManualCancelamento() && UteisValidacao.emptyNumber(cancelamentoContratoVO.getValorDevolucaoCancelamento())) {
                throw new Exception("Valor da devolução deve ser maior que zero ou selecione a opção \"Não Devolver ao Cliente\"");
            }
            if (cancelamentoContratoVO.getDevolucaoManualCancelamento()
                    && cancelamentoContratoVO.getValorDevolucaoCancelamento() > cancelamentoContratoVO.getValorTotalPagoPeloCliente()) {
                throw new Exception(getMensagemInternalizacao("msg_valor_nao_pode_sermaior_pago"));
            }
            if (!autorizado && (cancelamentoContratoVO.isQuitacaoManualCancelamento() || cancelamentoContratoVO.getLiberacaoCancelamento()
                    || cancelamentoContratoVO.getLiberacaoDevolucao() || cancelamentoContratoVO.getDevolucaoManualCancelamento())) {
                throw new Exception("Autorização não identificada. Informe a autorizaão novamente.");
            }
            if (cancelamentoContratoVO.getValorASerDevolvidoBaseCalculo() < 0.0
                    && !(cancelamentoContratoVO.isQuitacaoManualCancelamento()
                    || cancelamentoContratoVO.getLiberacaoCancelamento()
                    || cancelamentoContratoVO.getQuitacaoCancelamento())
                    && !isAlterarCancelamento()) {
                throw new Exception("Escolha uma forma de quitação.");
            }
        } catch (Exception e) {
            montarErro(e);
            return;
        }
        try {
            reciboDevolucao = null;
            if (cancelamentoContratoVO.getTipoDevolucaoCancelamento().equals("DE")) {
                reciboDevolucao = new ReciboDevolucaoVO();
                if (Uteis.arredondarForcando2CasasDecimaisMantendoSinal(cancelamentoContratoVO.getValorASerDevolvidoBaseCalculo()) < Uteis.arredondarForcando2CasasDecimaisMantendoSinal(cancelamentoContratoVO.getValorASerDevolvido())) {
                    getFacade().getZWFacade().gerarMovProdutoDevolucaoRecebiveis(getContratoVO(), getEmpresaLogado(), cancelamentoContratoVO.getResponsavelCancelamento(),
                            cancelamentoContratoVO, reciboDevolucao);
                }
                cancelamentoContratoVO.setValorRecebiveis(reciboDevolucao.getValorRecebiveis());
            }
            cancelamentoContratoVO.validarDados(getCancelamentoContratoVO(), "telaCancelamentoListaCheque", getContratoVO(), isAlterarCancelamento());
            cancelamentoContratoVO.setMensagemErro(false);
            String nome = "";
            //verificar retirada automatica
            if (retirarAutomatico) {
                List<ChequeVO> cheques = new ArrayList<ChequeVO>();
                List<CartaoCreditoVO> cartoes = new ArrayList<CartaoCreditoVO>();
                for (MovPagamentoVO movPag : getCancelamentoContratoVO().getListaPagamentos()) {
                    nome = movPag.getNomePagador();
                    for (ChequeVO cheque : movPag.getChequeVOs()) {
                        if (cheque.getChequeEscolhido() && cheque.getTemLote()) {
                            cheques.add(cheque);
                        }
                    }
                    for (CartaoCreditoVO cartao : movPag.getCartaoCreditoVOs()) {
                        if ((cartao.isCartaoEscolhido() || cartao.isDevolverParcial()) && cartao.getTemLote()) {
                            cartao.getMovpagamento().setNomePagador(movPag.getNomePagador());
                            cartoes.add(cartao);
                        }
                    }
                }
                for (MovPagamentoVO movPag : getCancelamentoContratoVO().getListaPagamentosMovimento()) {
                    nome = movPag.getNomePagador();
                    for (ChequeVO cheque : movPag.getChequeVOs()) {
                        if (cheque.getChequeEscolhido() && cheque.getTemLote()) {
                            cheques.add(cheque);
                        }
                    }
                    for (CartaoCreditoVO cartao : movPag.getCartaoCreditoVOs()) {
                        if ((cartao.isCartaoEscolhido() || cartao.isDevolverParcial()) && cartao.getTemLote()) {
                            cartao.getMovpagamento().setNomePagador(movPag.getNomePagador());
                            cartoes.add(cartao);
                        }
                    }
                }
                RetiradaAutomaticaControle retiradaAutomatica = (RetiradaAutomaticaControle) JSFUtilities.getFromSession(RetiradaAutomaticaControle.class.getSimpleName());
                if (retiradaAutomatica == null) {
                    retiradaAutomatica = new RetiradaAutomaticaControle();
                }
                if (retiradaAutomatica.preparar(cheques, cartoes, "form:proximoHide", nome)) {
                    setOnCompleteQuitacao("Richfaces.showModalPanel('modalRetiradaAutomatica')");
                    return;
                }
            }

            setMensagemDetalhada("");
            setOnCompleteQuitacao("document.getElementById('form:proximoHide').click();");
        } catch (Exception e) {
            setOnCompleteQuitacao("Richfaces.showModalPanel('panel')");
            setMensagemID("");
            setMensagemDetalhada(e.getMessage());
        }
    }

    public void usuarioTemPermissaoRetiradaAutomatica() {
        try {
            retirarAutomatico = Boolean.FALSE;
            List<UsuarioPerfilAcessoVO> usuarioPerfilAcessoVOs = getUsuarioLogado().getUsuarioPerfilAcessoVOs();
            if (usuarioPerfilAcessoVOs.isEmpty()) {
                if (getUsuarioLogado().getAdministrador()) {
                    return;
                }
                throw new Exception("O usuário informado não tem nenhum perfil acesso informado.");
            }
            for (UsuarioPerfilAcessoVO upa : usuarioPerfilAcessoVOs) {
                if (upa.getEmpresa().getCodigo().equals(getEmpresaLogado().getCodigo())) {
                    getFacade().getControleAcesso().verificarPermissaoUsuarioFuncionalidade(upa.getPerfilAcesso(),
                            getUsuarioLogado(),
                            "RetiradaAutomaticaRecebiveisFinanceiro", "9.33 - Retirada automática de recebíveis do financeiro");
                }
            }
            retirarAutomatico = Boolean.TRUE;
        } catch (Exception e) {
            retirarAutomatico = Boolean.FALSE;
        }
    }

    public String existePagamentoConjunto() {
        limparMsg();
        try {
            if (contratoVO.isCancelamentoAntecipado() && contratoVO.getEmpresa().isPermitirAlterarDataFinalContratoNoCancelamento() && isDataFinalContratoEmEdicao()) {
                throw new Exception("Por favor, verifique e confirme a data final do contrato!");
            }

            simular = false;
            setAlterarCancelamento(false);
            setPermiteAlterarCancelamento(isValidarPermissaoAlterarCancelamento());

            usuarioTemPermissaoRetiradaAutomatica();
            cancelamentoContratoVO.setValorRecebiveis(0.0);
            recibos = new ArrayList<ReciboPagamentoTO>();
            // busca todos os recibos que este contrato possui
            List<ReciboPagamentoVO> lista = getFacade().getReciboPagamento().consularReciboPagamParcelaPorCodigoContrato(contratoVO.getCodigo(), Uteis.NIVELMONTARDADOS_TELACONSULTA);
            for (ReciboPagamentoVO recibo : lista) {
                // busca os contratos de cada recibo
                List<ContratoVO> contratos = getFacade().getContrato().consultaContratosPorCodigoRecibo(recibo.getCodigo(), Uteis.NIVELMONTARDADOS_ROBO);
                // se para este recibo existem varios contratos
                if (contratos.size() > 1) {
                    // prepara os objetos TO para serem mostrados na tela
                    ReciboPagamentoTO reciboTemporario = new ReciboPagamentoTO();
                    reciboTemporario.setCodigo(recibo.getCodigo());
                    reciboTemporario.setNomePagador(recibo.getNomePessoaPagador());
                    reciboTemporario.setDataRecebimento(recibo.getData());
                    reciboTemporario.setValor(recibo.getValorTotal());
                    for (ContratoVO contrato : contratos) {
                        ContratoTO contratoTemporario = new ContratoTO();
                        contratoTemporario.setCodigo(contrato.getCodigo());
                        contratoTemporario.setNomePessoa(contrato.getPessoa().getNome());
                        contratoTemporario.setSituacaoContrato(contrato.getSituacao_Apresentar());
                        reciboTemporario.getContratos().add(contratoTemporario);
                    }
                    recibos.add(reciboTemporario);
                }
            }
            return existePagamentoComCheque();
        } catch (Exception e) {
            montarErro(e);
            cancelamentoContratoVO.setMensagemErro(true);
            setMensagemDetalhada("msg_erro", e.getMessage());
            return "";
        }
    }

    public String existePagamentoComCheque() {
        try {
            cancelamentoContratoVO.setLiberacaoDevolucao(false);
            cancelamentoContratoVO.setListaPagamentos(new ArrayList<>());
            cancelamentoContratoVO.setCartoesComComposicao(new ArrayList<>());
            cancelamentoContratoVO.setChequesComComposicao(new ArrayList<>());
            cancelamentoContratoVO.setSomaValorPagoPeloClienteComCheque(0.0);
            cancelamentoContratoVO.setValorASerDevolvidoBaseCalculo(0.0);
            cancelamentoContratoVO.setValorASerDevolvidoBaseCalculo(cancelamentoContratoVO.getValorASerDevolvido() - cancelamentoContratoVO.getValorDevolverSaldoTransferenciaCredito());
            cancelamentoContratoVO.obterPagamentoEfetuado();
            // validar se existe pagamento com cheque e saber quais estao
            // compensados para pode devolver cheque caso o cliente queria.
            cancelamentoContratoVO.setApresentaListaCheque(cancelamentoContratoVO.validarSeExistePagamentoComCheque(recibos, getContratoVO().getCodigo()));
            cancelamentoContratoVO.setApresentaListaCartao(cancelamentoContratoVO.validarSeExistePagamentoComCartao(recibos, getContratoVO().getCodigo()));
            cancelamentoContratoVO.setApresentaListaChequeMovimento(cancelamentoContratoVO.validarSeExistePagamentoComChequeMovimento());
            cancelamentoContratoVO.setApresentaListaCartaoMovimento(cancelamentoContratoVO.validarSeExistePagamentoComCartaoMovimento());
            cancelamentoContratoVO.obterValorFinalASerDevolvidoComCheque();
            marcarAutomaticamenteDevolucaoRecebiveis();

            if (getContratoVO().isCancelamentoAntecipado() || getContratoVO().isCancelamentoObrigatoriedadePagamento()) {

                if (!cancelamentoContratoVO.getApresentarPanelLancaProdutoLiberacao()) {
                    cancelamentoContratoVO.setLiberacaoDevolucao(true);
                    cancelamentoContratoVO.setDevolucaoManualCancelamento(false);
                    cancelamentoContratoVO.getResponsavelLiberacaoCancelamento().setCodigo(getUsuarioLogado().getCodigo());
                    cancelamentoContratoVO.getResponsavelLiberacaoCancelamento().setUsername(getUsuarioLogado().getUsername());
                    autorizado = true;
                    cancelamentoContratoVO.setMensagemErro(false);
                    finalizarCancelamentoComCheque();
                    return "finalizarCancelamento";
                } else if (cancelamentoContratoVO.getApresentarPanelLancaProdutoLiberacao()) {
                    cancelamentoContratoVO.setLiberacaoCancelamento(true);
                    cancelamentoContratoVO.setQuitacaoCancelamento(false);
                    cancelamentoContratoVO.setQuitacaoManualCancelamento(false);
                    cancelamentoContratoVO.setValorQuitacaoCancelamento(0.0);
                    cancelamentoContratoVO.getResponsavelLiberacaoCancelamento().setCodigo(getUsuarioLogado().getCodigo());
                    cancelamentoContratoVO.getResponsavelLiberacaoCancelamento().setUsername(getUsuarioLogado().getUsername());
                    autorizado = true;
                    cancelamentoContratoVO.setMensagemErro(false);
                    finalizarCancelamentoComCheque();
                    return "finalizarCancelamento";
                }
            }

            if (cancelamentoContratoVO.getApresentaListaCheque() || cancelamentoContratoVO.isApresentaListaCartao() || cancelamentoContratoVO.isApresentaListaCartaoMovimento() || cancelamentoContratoVO.getApresentaListaChequeMovimento()) {
                cancelamentoContratoVO.setSomaValorPagoPeloClienteComCheque(Uteis.arredondarForcando2CasasDecimais(cancelamentoContratoVO.getSomaValorPagoPeloClienteComCheque()));
                cancelamentoContratoVO.setMensagemErro(false);
                setMensagemDetalhada("");
                return "cancelamentoListaCheque";
            }
            setMensagemDetalhada("");
            cancelamentoContratoVO.setMensagemErro(false);

            return "cancelamentoListaCheque";

        } catch (Exception e) {
            cancelamentoContratoVO.setMensagemErro(true);
            setMensagemID("");
            setMensagemDetalhada(e.getMessage());
            return "resultadoCalculo";
        }
    }

    private void marcarAutomaticamenteDevolucaoRecebiveis() throws Exception {
        if (getEmpresaLogado().isMarcarAutoRecebiveisCartaoChequeCancelamento()) {
            double valorDisponivelEstornar = cancelamentoContratoVO.getValorASerDevolvidoBaseCalculo_Apresentar();
            if (cancelamentoContratoVO.getApresentaListaCheque()) {
                for (MovPagamentoVO mPag : cancelamentoContratoVO.getListaPagamentos()) {
                    if (mPag.getChequeVOs() != null && mPag.getChequeVOs().size() > 1) {
                        List<ChequeVO> chequesNaoCompensados = new ArrayList<>();
                        for (ChequeVO cheque : mPag.getChequeVOs()) {
                            if (!Calendario.menorOuIgual(cheque.getDataCompensacao(), cancelamentoContratoVO.getDataCancelamento())) {
                                chequesNaoCompensados.add(cheque);
                            }
                        }
                        if (!chequesNaoCompensados.isEmpty()) {
                            Ordenacao.ordenarListaReverse(chequesNaoCompensados, "codigo");
                            chequesNaoCompensados.get(0).setChequeEscolhido(true);
                            devolverChequeAutomatico(mPag, chequesNaoCompensados.get(0), valorDisponivelEstornar);
                        }
                    }
                }
            }

            if (cancelamentoContratoVO.isApresentaListaCartao()) {
                for (MovPagamentoVO mPag : cancelamentoContratoVO.getListaPagamentos()) {
                    if (mPag.getCartaoCreditoVOs() != null && mPag.getCartaoCreditoVOs().size() > 1) {
                        List<CartaoCreditoVO> cartoesNaoCompensados = new ArrayList<>();
                        for (CartaoCreditoVO cartao : mPag.getCartaoCreditoVOs()) {
                            if (!Calendario.menorOuIgual(cartao.getDataCompensacao(), cancelamentoContratoVO.getDataCancelamento())) {
                                cartoesNaoCompensados.add(cartao);
                            }
                        }
                        if (!cartoesNaoCompensados.isEmpty()) {
                            Ordenacao.ordenarListaReverse(cartoesNaoCompensados, "codigo");
                            cartoesNaoCompensados.get(0).setCartaoEscolhido(true);
                            devolverCartaoAutomatico(mPag, cartoesNaoCompensados.get(0), valorDisponivelEstornar);
                        }
                    }
                }
            }

            if (getCartaoDevolucao() != null && getCartaoDevolucao().getValorParcialDevolverCalculo() > 0.0) {
                aplicarEstornoParcialParcelaSelecionada();
                cancelamentoContratoVO.setQuitacaoCancelamento(true);
            }

            if (cancelamentoContratoVO.getListaPagamentos() != null && !cancelamentoContratoVO.getListaPagamentos().isEmpty()) {
                for (MovPagamentoVO mPag : cancelamentoContratoVO.getListaPagamentos()) {
                    if (cancelamentoContratoVO.getApresentaListaCheque()) {
                        Ordenacao.ordenarLista(mPag.getChequeVOs(), "codigo");
                    }
                    if (cancelamentoContratoVO.isApresentaListaCartao()) {
                        Ordenacao.ordenarLista(mPag.getCartaoCreditoVOs(), "codigo");
                    }
                }
            }
        }
    }

    public void devolverCheque() {
        try {
            ChequeVO obj = (ChequeVO) context().getExternalContext().getRequestMap().get("cheque");

            cancelamentoContratoVO.setMensagemErro(false);

            if (obj == null) {
                throw new Exception("Erro ao posicionar o cheque. Contate Suporte técnico.");
            }
            if (!obj.getChequeEscolhido()) {
                obj.setChequeEscolhido(true);
                return;
            }
            double aux = cancelamentoContratoVO.getSomaValorPagoPeloClienteComCheque();
            aux -= obj.getValor();
            obj.setChequeCompensar(!obj.getChequeEscolhido());
            cancelamentoContratoVO.setSomaValorPagoPeloClienteComCheque(Uteis.arredondarForcando2CasasDecimais(aux));
            cancelamentoContratoVO.setValorASerDevolvidoBaseCalculo(cancelamentoContratoVO.getSomaValorPagoPeloClienteComCheque() - cancelamentoContratoVO.getValorASerPagoPeloCliente());
            cancelamentoContratoVO.setValorRecebiveis(cancelamentoContratoVO.getValorRecebiveis() + obj.getValor());
            limparOpcoesCancelamento();
            verificarValorOperacoesCancelamento();
        } catch (Exception e) {
            cancelamentoContratoVO.setMensagemErro(true);
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public void devolverChequeAutomatico(MovPagamentoVO mPag, ChequeVO cheque, double valorDisponivelEstornar) {
        try {
            if (cheque != null) {
                MovPagamentoVO movPagAnterior = (MovPagamentoVO) new MovPagamentoVO().getClone(getMovPagamentoVO());
                setMovPagamentoVO(mPag);
                double aux = cancelamentoContratoVO.getSomaValorPagoPeloClienteComCheque();
                aux += marcaAbaixoChequeAutomatico(cheque, valorDisponivelEstornar);
                cheque.setChequeCompensar(!cheque.getChequeEscolhido());
                cancelamentoContratoVO.setSomaValorPagoPeloClienteComCheque(Uteis.arredondarForcando2CasasDecimais(aux));
                cancelamentoContratoVO.setValorASerDevolvidoBaseCalculo(cancelamentoContratoVO.getSomaValorPagoPeloClienteComCheque() - cancelamentoContratoVO.getValorASerPagoPeloCliente());
                cancelamentoContratoVO.setValorRecebiveis(cancelamentoContratoVO.getValorRecebiveis() + cheque.getValor());
                limparOpcoesCancelamento();
                verificarValorOperacoesCancelamento();
                setMovPagamentoVO(movPagAnterior);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private void verificarValorOperacoesCancelamento() {
        if (cancelamentoContratoVO.getValorASerDevolvidoBaseCalculo() >= 0.0) {
            cancelamentoContratoVO.setLiberacaoCancelamento(false);
            cancelamentoContratoVO.setQuitacaoCancelamento(false);
            cancelamentoContratoVO.setQuitacaoManualCancelamento(false);
        } else {
            cancelamentoContratoVO.setLiberacaoDevolucao(false);
        }
    }

    public void devolverCartao() {
        try {
            limparMsg();
            apresentarWarningValor = false;
            CartaoCreditoVO obj = (CartaoCreditoVO) context().getExternalContext().getRequestMap().get("cartao");
            if (obj == null) {
                throw new Exception("Erro ao posicionar o cartao. Contate Suporte técnico.");
            }
            if (!obj.isCartaoEscolhido()) {
                obj.setCartaoEscolhido(true);
                return;
            }
            setCartaoDevolucao(obj);
            MovPagamentoVO objPag = (MovPagamentoVO) context().getExternalContext().getRequestMap().get("movPagamento");
            setMovPagamentoVO(objPag);
            double aux = cancelamentoContratoVO.getSomaValorPagoPeloClienteComCheque();
            obj.setCartaoEscolhido(false);
            aux += marcaAbaixo(obj);
            obj.setCartaoCompensar(!obj.isCartaoEscolhido());
            cancelamentoContratoVO.setSomaValorPagoPeloClienteComCheque(Uteis.arredondarForcando2CasasDecimais(aux));
            cancelamentoContratoVO.setValorASerDevolvidoBaseCalculo(cancelamentoContratoVO.getSomaValorPagoPeloClienteComCheque() - cancelamentoContratoVO.getValorASerPagoPeloCliente());
            limparOpcoesCancelamento();
            verificarValorOperacoesCancelamento();
            if (apresentarWarningValor) {
                montarAviso("A(s) última(s) parcela(s) desse pagamento não foi(foram) marcada(s) para o estorno, pois excederia o valor pago pelo contrato que está sendo cancelado");
            }
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public void devolverCartaoAutomatico(MovPagamentoVO mPag, CartaoCreditoVO cartao, double valorDisponivelEstornar) {
        try {
            if (cartao != null) {
                setCartaoDevolucao(cartao);
                setMovPagamentoVO(mPag);
                double aux = cancelamentoContratoVO.getSomaValorPagoPeloClienteComCheque();
                cartao.setCartaoEscolhido(false);
                aux += marcaAbaixoCartaoAutomatico(cartao, valorDisponivelEstornar);
                cartao.setCartaoCompensar(!cartao.isCartaoEscolhido());
                cancelamentoContratoVO.setSomaValorPagoPeloClienteComCheque(Uteis.arredondarForcando2CasasDecimais(aux));
                cancelamentoContratoVO.setValorASerDevolvidoBaseCalculo(cancelamentoContratoVO.getSomaValorPagoPeloClienteComCheque() - cancelamentoContratoVO.getValorASerPagoPeloCliente());
                limparOpcoesCancelamento();
                verificarValorOperacoesCancelamento();
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public void compensarCheque() {
        try {
            ChequeVO obj = (ChequeVO) context().getExternalContext().getRequestMap().get("cheque");
            if (obj == null) {
                throw new Exception("Erro ao posicionar o cheque. Contate Suporte técnico.");
            }
            if (!obj.getChequeCompensar()) {
                obj.setChequeCompensar(true);
                return;
            }
            double aux = cancelamentoContratoVO.getSomaValorPagoPeloClienteComCheque();
            aux += obj.getValor();
            obj.setChequeEscolhido(!obj.getChequeCompensar());
            cancelamentoContratoVO.setSomaValorPagoPeloClienteComCheque(Uteis.arredondarForcando2CasasDecimais(aux));
            cancelamentoContratoVO.setValorASerDevolvidoBaseCalculo(cancelamentoContratoVO.getSomaValorPagoPeloClienteComCheque() - cancelamentoContratoVO.getValorASerPagoPeloCliente());
            limparOpcoesCancelamento();
            verificarValorOperacoesCancelamento();
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public void compensarCartao() {
        try {
            CartaoCreditoVO obj = (CartaoCreditoVO) context().getExternalContext().getRequestMap().get("cartao");
            if (obj == null) {
                throw new Exception("Erro ao posicionar o cartao. Contate Suporte técnico.");
            }
            if (!obj.isCartaoCompensar()) {
                obj.setCartaoCompensar(true);
                return;
            }
            double aux = cancelamentoContratoVO.getSomaValorPagoPeloClienteComCheque();
            aux += marcaAcima(obj);
            obj.setCartaoEscolhido(!obj.isCartaoCompensar());
            cancelamentoContratoVO.setSomaValorPagoPeloClienteComCheque(Uteis.arredondarForcando2CasasDecimais(aux));
            cancelamentoContratoVO.setValorASerDevolvidoBaseCalculo(cancelamentoContratoVO.getSomaValorPagoPeloClienteComCheque() - cancelamentoContratoVO.getValorASerPagoPeloCliente());
            limparOpcoesCancelamento();
            verificarValorOperacoesCancelamento();
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    private double marcaAbaixoChequeAutomatico(ChequeVO obj, double valorDisponivelEstornar) {
        double valorTotalEstornado = 0.0;
        for (MovPagamentoVO movPagamento : cancelamentoContratoVO.getListaPagamentos()) {
            valorTotalEstornado += movPagamento.getValorEstornado();
        }
        double valorAindaPOdeEstornar = Uteis.arredondarForcando2CasasDecimais(valorDisponivelEstornar - valorTotalEstornado);
        double aux = inicializarDadosDevolucao();
        for (MovPagamentoVO movPagamento : cancelamentoContratoVO.getListaPagamentos()) {
            Ordenacao.ordenarListaReverse(movPagamento.getChequeVOs(), "codigo");
            if (movPagamento.getFormaPagamento().getTipoFormaPagamento().equals("CH") && obj.getMovPagamento().equals(movPagamento.getCodigo())) {
                Iterator i = movPagamento.getChequeVOs().iterator();
                boolean marcaTodosAbaixo = false;
                while (i.hasNext()) {
                    ChequeVO cheque = (ChequeVO) i.next();
                    if (!cheque.getApresentarCheque() || cheque.getMarcadoDevolucao()) {
                        continue;
                    }
                    if (!cheque.getChequeCompensar()) {
                        continue;
                    }
                    if (Uteis.arredondarForcando2CasasDecimais(valorAindaPOdeEstornar) < Uteis.arredondarForcando2CasasDecimais(cheque.getValor())) {
                        cheque.setChequeEscolhido(false);
                        cheque.setChequeCompensar(true);
                        continue;
                    }

                    if (cheque.getCodigo().intValue() == obj.getCodigo()) {
                        aux -= Uteis.arredondarForcando2CasasDecimais(obj.getValor());
                        getMovPagamentoVO().setValorEstornado(Uteis.arredondarForcando2CasasDecimais(getMovPagamentoVO().getValorEstornado() + obj.getValor()));
                        valorAindaPOdeEstornar -= Uteis.arredondarForcando2CasasDecimais(obj.getValor());
                        marcaTodosAbaixo = true;
                        cheque.setChequeEscolhido(true);
                        cheque.setChequeCompensar(false);
                    } else if (marcaTodosAbaixo) {
                        if (!cheque.getChequeEscolhido()) {
                            aux -= Uteis.arredondarForcando2CasasDecimais(cheque.getValor());
                            getMovPagamentoVO().setValorEstornado(Uteis.arredondarForcando2CasasDecimais(getMovPagamentoVO().getValorEstornado() + cheque.getValor()));
                            valorAindaPOdeEstornar -= Uteis.arredondarForcando2CasasDecimais(cheque.getValor());
                        }
                        cheque.setChequeEscolhido(true);
                        cheque.setChequeCompensar(false);
                    }
                }
            }
        }
        for (MovPagamentoVO movPagamento : cancelamentoContratoVO.getListaPagamentosMovimento()) {
            if (movPagamento.getFormaPagamento().getTipoFormaPagamento().equals("CH") && obj.getMovPagamento().equals(movPagamento.getCodigo())) {
                Iterator i = movPagamento.getChequeVOs().iterator();
                boolean marcaTodosAbaixo = false;
                while (i.hasNext()) {
                    ChequeVO cheque = (ChequeVO) i.next();
                    if (!cheque.getApresentarCheque()) {
                        continue;
                    }
                    if (cheque.getCodigo().intValue() == obj.getCodigo()) {
                        aux -= obj.getValor();
                        marcaTodosAbaixo = true;
                        cheque.setChequeEscolhido(true);
                        cheque.setChequeCompensar(false);
                    } else if (marcaTodosAbaixo) {
                        if (!cheque.getChequeEscolhido()) {
                            aux -= obj.getValor();
                        }
                        cheque.setChequeEscolhido(true);
                        cheque.setChequeCompensar(false);
                    }
                }
            }
        }
        verificarValorOperacoesCancelamento();
        return aux;
    }

    private double marcaAbaixo(CartaoCreditoVO obj) {
        double aux = inicializarDadosDevolucao();
        for (MovPagamentoVO movPagamento : cancelamentoContratoVO.getListaPagamentos()) {
            if (movPagamento.getFormaPagamento().getTipoFormaPagamento().equals("CA") && obj.getMovpagamento().getCodigo().equals(movPagamento.getCodigo())) {
                Iterator i = movPagamento.getCartaoCreditoVOs().iterator();
                boolean marcaTodosAbaixo = false;
                while (i.hasNext()) {
                    CartaoCreditoVO cartao = (CartaoCreditoVO) i.next();
                    if (!cartao.getApresentarCartao() || cartao.isDevolverParcial()) {
                        continue;
                    }
                    if (!cartao.isCartaoCompensar()) {
                        continue;
                    }
                    if (Uteis.arredondarForcando2CasasDecimais(getMovPagamentoVO().getValorPagaContrato()) < Uteis.arredondarForcando2CasasDecimais(getMovPagamentoVO().getValorEstornado() + obj.getValor())) {
                        cartao.setCartaoEscolhido(false);
                        cartao.setCartaoCompensar(true);
                        apresentarWarningValor = true;
                        continue;
                    }

                    if (cartao.getCodigo().intValue() == obj.getCodigo()) {
                        aux -= Uteis.arredondarForcando2CasasDecimais(obj.getValor());
                        getMovPagamentoVO().setValorEstornado(Uteis.arredondarForcando2CasasDecimais(getMovPagamentoVO().getValorEstornado() + obj.getValor()));
                        marcaTodosAbaixo = true;
                        cartao.setCartaoEscolhido(true);
                        cartao.setCartaoCompensar(false);
                    } else if (marcaTodosAbaixo) {
                        if (!cartao.isCartaoEscolhido()) {
                            aux -= Uteis.arredondarForcando2CasasDecimais(cartao.getValor());
                            getMovPagamentoVO().setValorEstornado(Uteis.arredondarForcando2CasasDecimais(getMovPagamentoVO().getValorEstornado() + cartao.getValor()));
                        }
                        cartao.setCartaoEscolhido(true);
                        cartao.setCartaoCompensar(false);
                    }
                }
            }
        }
        for (MovPagamentoVO movPagamento : cancelamentoContratoVO.getListaPagamentosMovimento()) {
            if (movPagamento.getFormaPagamento().getTipoFormaPagamento().equals("CA") && obj.getMovpagamento().getCodigo().equals(movPagamento.getCodigo())) {
                Iterator i = movPagamento.getCartaoCreditoVOs().iterator();
                boolean marcaTodosAbaixo = false;
                while (i.hasNext()) {
                    CartaoCreditoVO cartao = (CartaoCreditoVO) i.next();
                    if (!cartao.getApresentarCartao()) {
                        continue;
                    }
                    if (cartao.getCodigo().intValue() == obj.getCodigo()) {
                        aux -= obj.getValor();
                        marcaTodosAbaixo = true;
                        cartao.setCartaoEscolhido(true);
                        cartao.setCartaoCompensar(false);
                    } else if (marcaTodosAbaixo) {
                        if (!cartao.isCartaoEscolhido()) {
                            aux -= obj.getValor();
                        }
                        cartao.setCartaoEscolhido(true);
                        cartao.setCartaoCompensar(false);
                    }
                }
            }
        }
        verificarValorOperacoesCancelamento();
        return aux;
    }

    private double marcaAbaixoCartaoAutomatico(CartaoCreditoVO obj, double valorDisponivelEstornar) {
        double valorTotalEstornado = 0.0;
        for (MovPagamentoVO movPagamento : cancelamentoContratoVO.getListaPagamentos()) {
            valorTotalEstornado += movPagamento.getValorEstornado();
        }
        double valorAindaPOdeEstornar = Uteis.arredondarForcando2CasasDecimais(valorDisponivelEstornar - valorTotalEstornado);
        double aux = inicializarDadosDevolucao();

        int qtdMovPagamentosCartao = 0;
        for (MovPagamentoVO movPagamento : cancelamentoContratoVO.getListaPagamentos()) {
            if(movPagamento.getFormaPagamento().getTipoFormaPagamento().equals("CA"))
                qtdMovPagamentosCartao++;
        }

        for (MovPagamentoVO movPagamento : cancelamentoContratoVO.getListaPagamentos()) {
            Ordenacao.ordenarListaReverse(movPagamento.getCartaoCreditoVOs(), "codigo");
            qtdMovPagamentosCartao--;
            if (movPagamento.getFormaPagamento().getTipoFormaPagamento().equals("CA") && obj.getMovpagamento().getCodigo().equals(movPagamento.getCodigo())) {
                Iterator i = movPagamento.getCartaoCreditoVOs().iterator();
                boolean marcaTodosAbaixo = false;
                while (i.hasNext()) {
                    CartaoCreditoVO cartao = (CartaoCreditoVO) i.next();
                    if (!cartao.getApresentarCartao() || cartao.isDevolverParcial()) {
                        continue;
                    }
                    if (!cartao.isCartaoCompensar()) {
                        continue;
                    }
                    if (Uteis.arredondarForcando2CasasDecimais(valorAindaPOdeEstornar) < (Uteis.arredondarForcando2CasasDecimais(cartao.getValor()))) {
                        if (valorAindaPOdeEstornar > 0.0 && qtdMovPagamentosCartao == 0) {
                            setCartaoDevolucao(cartao);
                            cartao.setCartaoEscolhido(true);
                            cartao.setCartaoCompensar(false);
                            getCartaoDevolucao().setDevolverParcial(true);
                            getCartaoDevolucao().setValorParcialDevolverCalculo(Uteis.arredondarForcando2CasasDecimais(valorAindaPOdeEstornar));
                        } else {
                            cartao.setCartaoEscolhido(false);
                            cartao.setCartaoCompensar(true);
                        }
                        break;
                    }

                    if (cartao.getCodigo().intValue() == obj.getCodigo()) {
                        aux -= Uteis.arredondarForcando2CasasDecimais(obj.getValor());
                        getMovPagamentoVO().setValorEstornado(Uteis.arredondarForcando2CasasDecimais(getMovPagamentoVO().getValorEstornado() + obj.getValor()));
                        valorAindaPOdeEstornar -= Uteis.arredondarForcando2CasasDecimais(obj.getValor());
                        marcaTodosAbaixo = true;
                        cartao.setCartaoEscolhido(true);
                        cartao.setCartaoCompensar(false);
                    } else if (marcaTodosAbaixo) {
                        if (!cartao.isCartaoEscolhido()) {
                            aux -= Uteis.arredondarForcando2CasasDecimais(cartao.getValor());
                            getMovPagamentoVO().setValorEstornado(Uteis.arredondarForcando2CasasDecimais(getMovPagamentoVO().getValorEstornado() + cartao.getValor()));
                            valorAindaPOdeEstornar -= Uteis.arredondarForcando2CasasDecimais(cartao.getValor());
                        }
                        cartao.setCartaoEscolhido(true);
                        cartao.setCartaoCompensar(false);
                    }
                }
            }
        }
        for (MovPagamentoVO movPagamento : cancelamentoContratoVO.getListaPagamentosMovimento()) {
            if (movPagamento.getFormaPagamento().getTipoFormaPagamento().equals("CA") && obj.getMovpagamento().getCodigo().equals(movPagamento.getCodigo())) {
                Iterator i = movPagamento.getCartaoCreditoVOs().iterator();
                boolean marcaTodosAbaixo = false;
                while (i.hasNext()) {
                    CartaoCreditoVO cartao = (CartaoCreditoVO) i.next();
                    if (!cartao.getApresentarCartao()) {
                        continue;
                    }
                    if (cartao.getCodigo().intValue() == obj.getCodigo()) {
                        aux -= obj.getValor();
                        marcaTodosAbaixo = true;
                        cartao.setCartaoEscolhido(true);
                        cartao.setCartaoCompensar(false);
                    } else if (marcaTodosAbaixo) {
                        if (!cartao.isCartaoEscolhido()) {
                            aux -= obj.getValor();
                        }
                        cartao.setCartaoEscolhido(true);
                        cartao.setCartaoCompensar(false);
                    }
                }
            }
        }
        verificarValorOperacoesCancelamento();
        return aux;
    }

    private double marcaAcima(CartaoCreditoVO obj) {
        double aux = 0.0;
        for (MovPagamentoVO movPagamento : cancelamentoContratoVO.getListaPagamentos()) {
            if (movPagamento.getFormaPagamento().getTipoFormaPagamento().equals("CA") && obj.getMovpagamento().getCodigo().equals(movPagamento.getCodigo())) {
                Iterator i = movPagamento.getCartaoCreditoVOs().iterator();
                boolean marcaTodosAcima = true;
                while (i.hasNext()) {
                    CartaoCreditoVO cartao = (CartaoCreditoVO) i.next();
                    if (cartao.getCodigo().intValue() == obj.getCodigo()) {
                        aux += obj.getValor();
                        getMovPagamentoVO().setValorEstornado(Uteis.arredondarForcando2CasasDecimais(getMovPagamentoVO().getValorEstornado() - obj.getValor()));
                        marcaTodosAcima = false;
                    } else if (marcaTodosAcima) {
                        if (!cartao.isCartaoCompensar()) {
                            aux += cartao.getValor();
                            getMovPagamentoVO().setValorEstornado(Uteis.arredondarForcando2CasasDecimais(getMovPagamentoVO().getValorEstornado() - cartao.getValor()));
                        }
                        cartao.setCartaoEscolhido(false);
                        cartao.setCartaoCompensar(true);
                    }
                }
            }
        }
        for (MovPagamentoVO movPagamento : cancelamentoContratoVO.getListaPagamentosMovimento()) {
            if (movPagamento.getFormaPagamento().getTipoFormaPagamento().equals("CA") && obj.getMovpagamento().getCodigo().equals(movPagamento.getCodigo())) {
                Iterator i = movPagamento.getCartaoCreditoVOs().iterator();
                boolean marcaTodosAcima = true;
                while (i.hasNext()) {
                    CartaoCreditoVO cartao = (CartaoCreditoVO) i.next();
                    if (!cartao.getApresentarCartao()) {
                        continue;
                    }
                    if (cartao.getCodigo().intValue() == obj.getCodigo()) {
                        aux += obj.getValor();
                        marcaTodosAcima = false;
                    } else if (marcaTodosAcima) {
                        if (!cartao.isCartaoCompensar()) {
                            aux += obj.getValor();
                        }
                        cartao.setCartaoEscolhido(false);
                        cartao.setCartaoCompensar(true);
                    }
                }
            }
        }

        verificarValorOperacoesCancelamento();
        return aux;
    }

    public void obterQuitacaoCancelamento() throws ParseException {
        setApresentarMensagemTipoParcelaCancelamento(false);
        setApresentarMensagemZerarValorCancelamento(false);
        cancelamentoContratoVO.setQuitacaoManualCancelamento(false);
        cancelamentoContratoVO.setLiberacaoCancelamento(false);
        if (cancelamentoContratoVO.getQuitacaoCancelamento()) {
            cancelamentoContratoVO.setValorQuitacaoCancelamento(cancelamentoContratoVO.getValorASerDevolvidoBaseCalculo() * -1);
            double valorQuitacaoCancelamento = Uteis.arredondarForcando2CasasDecimais(cancelamentoContratoVO.getValorQuitacaoCancelamento());

            double valorReduzir = 0.0;
            for (MovParcelaVO parcela : cancelamentoContratoVO.getListaParcelas()) {
                if (!parcela.getSituacao().equals("EA")) {
                    continue;
                }
                if (cancelamentoContratoVO.getTipoParcelaCancelamento().equals(TipoParcelaCancelamento.MAIOR_IGUAL_MES_ATUAL)) {
                    if (!Calendario.vencimentoMaiorOuIgualAoMesAtual(parcela.getDataVencimento())) {
                        valorReduzir += parcela.getValorParcela();
                        continue;
                    }
                }

                if (cancelamentoContratoVO.getTipoParcelaCancelamento().equals(TipoParcelaCancelamento.MAIOR_IGUAL)) {
                    if (!Calendario.maior(parcela.getDataVencimento(), cancelamentoContratoVO.getDataCancelamento()) && !(cancelamentoContratoVO.getContratoCancelar() != null && cancelamentoContratoVO.getContratoCancelar().getContratoRecorrenciaVO().isCancelamentoProporcional() && parcela.getDescricao().contains("ANUIDADE"))) {
                        valorReduzir += parcela.getValorParcela();
                    }
                }
            }

            if (valorReduzir > 0) {
                if (!getContratoVO().getEmpresa().isZerarValorCancelamentoTransferencia()){
                    Double diffValorPagoClienteValorASerPago = Uteis.arredondarForcando2CasasDecimais(cancelamentoContratoVO.getValorASerPagoPeloCliente() - cancelamentoContratoVO.getSomaValorPagoPeloClienteComCheque());
                    if (valorQuitacaoCancelamento != diffValorPagoClienteValorASerPago) {
                        valorQuitacaoCancelamento = cancelamentoContratoVO.getValorQuitacaoCancelamento()
                                + diffValorPagoClienteValorASerPago;
                    }
                }
            }

            if (valorReduzir >= valorQuitacaoCancelamento) {
                cancelamentoContratoVO.setValorQuitacaoCancelamento(0.0);
                setApresentarMensagemTipoParcelaCancelamento(valorReduzir > 0);
                if (getContratoVO().getEmpresa().isZerarValorCancelamentoTransferencia()){
                    setApresentarMensagemZerarValorCancelamento(true);
                }
            } else {
                cancelamentoContratoVO.setValorQuitacaoCancelamento(valorQuitacaoCancelamento);
            }

            cancelamentoContratoVO.setLiberacaoCancelamento(false);
        } else {
            cancelamentoContratoVO.setValorQuitacaoCancelamento(0.0);
        }
    }

    public void obterLiberacaoCancelamento() throws ParseException, Exception {

        AutorizacaoFuncionalidadeControle auto = getControlador(AutorizacaoFuncionalidadeControle.class);
        auto.setPedirPermissao(false);
        AutorizacaoFuncionalidadeListener listener = new AutorizacaoFuncionalidadeListener() {

            @Override
            public void onAutorizacaoComSucesso() throws Exception {
                AutorizacaoFuncionalidadeControle auto = getControlador(AutorizacaoFuncionalidadeControle.class);

                cancelamentoContratoVO.setResponsavelLiberacaoCancelamento(auto.getUsuario());
                autorizado = true;
                montarSucessoGrowl("");

            }

            @Override
            public void onAutorizacaoComErro(Exception e) {
                montarErro(e);
            }

            @Override
            public void onFecharModalAutorizacao() {
                autorizado = false;
                cancelamentoContratoVO.setLiberacaoCancelamento(false);
            }
        };

        limparMsg();
        autorizado = false;
        if (cancelamentoContratoVO.getLiberacaoCancelamento()) {
            cancelamentoContratoVO.setQuitacaoCancelamento(false);
            cancelamentoContratoVO.setQuitacaoManualCancelamento(false);
            cancelamentoContratoVO.setValorQuitacaoCancelamento(0.0);
            auto.autorizar("Confirmação de Liberação Cancelamento de Contrato", "Liberar_CancelamentoContrato_Autorizar",
                    "Você precisa da permissão \"3.08 - Liberar cobrança de valores no Cancelamento Contrato - Autorizar\"",
                    "form", listener);
            setMensagemDetalhada("");
            cancelamentoContratoVO.getResponsavelLiberacaoCancelamento().setCodigo(auto.getUsuario().getCodigo());
            cancelamentoContratoVO.getResponsavelLiberacaoCancelamento().setUsername(auto.getUsuario().getUsername());
        } else {
            cancelamentoContratoVO.setResponsavelLiberacaoCancelamento(new UsuarioVO());
        }
    }

    public void obterDevolucaoManualCancelamento() throws Exception {

        AutorizacaoFuncionalidadeControle auto = getControlador(AutorizacaoFuncionalidadeControle.class);
        auto.setPedirPermissao(false);
        AutorizacaoFuncionalidadeListener listener = new AutorizacaoFuncionalidadeListener() {

            @Override
            public void onAutorizacaoComSucesso() throws Exception {
                AutorizacaoFuncionalidadeControle auto = getControlador(AutorizacaoFuncionalidadeControle.class);
                cancelamentoContratoVO.setResponsavelLiberacaoCancelamento(auto.getUsuario());
                autorizado = true;
                montarSucessoGrowl("");

            }

            @Override
            public void onAutorizacaoComErro(Exception e) {
                montarErro(e);
            }

            @Override
            public void onFecharModalAutorizacao() {
                autorizado = false;
                cancelamentoContratoVO.setDevolucaoManualCancelamento(false);
            }
        };

        limparMsg();
        autorizado = false;
        if (cancelamentoContratoVO.getDevolucaoManualCancelamento()) {
            cancelamentoContratoVO.setLiberacaoDevolucao(false);
            cancelamentoContratoVO.setLiberacaoCancelamento(false);
            auto.autorizar("Confirmação de Devolução Manual de Cancelamento de Contrato", "ValorManual_CancelamentoContrato_Autorizar",
                    "Você precisa da permissão \"3.09 - Valor Manual Cancelamento Contrato - Autorizar\"",
                    "form", listener);
            cancelamentoContratoVO.getResponsavelLiberacaoCancelamento().setCodigo(auto.getUsuario().getCodigo());
            cancelamentoContratoVO.getResponsavelLiberacaoCancelamento().setUsername(auto.getUsuario().getUsername());
            cancelamentoContratoVO.setValorDevolucaoCancelamento(cancelamentoContratoVO.getValorASerDevolvidoBaseCalculo());
        } else {
            setAbrirRichConfirmacaoManual(false);
            cancelamentoContratoVO.setResponsavelLiberacaoCancelamento(new UsuarioVO());
        }
    }

    public void obterQuitacaoManualCancelamento() throws Exception {
        AutorizacaoFuncionalidadeControle auto = getControlador(AutorizacaoFuncionalidadeControle.class);
        auto.setPedirPermissao(false);
        AutorizacaoFuncionalidadeListener listener = new AutorizacaoFuncionalidadeListener() {

            @Override
            public void onAutorizacaoComSucesso() throws Exception {
                AutorizacaoFuncionalidadeControle auto = getControlador(AutorizacaoFuncionalidadeControle.class);

                cancelamentoContratoVO.setResponsavelLiberacaoCancelamento(auto.getUsuario());

                autorizado = true;
                montarSucessoGrowl("");
            }

            @Override
            public void onAutorizacaoComErro(Exception e) {
                montarErro(e);
            }

            @Override
            public void onFecharModalAutorizacao() {
                autorizado = false;
                cancelamentoContratoVO.setQuitacaoManualCancelamento(false);
            }
        };

        limparMsg();
        autorizado = false;

        if (cancelamentoContratoVO.isQuitacaoManualCancelamento()) {
            cancelamentoContratoVO.setQuitacaoCancelamento(false);
            cancelamentoContratoVO.setLiberacaoCancelamento(false);

            auto.autorizar("Confirmação de Quitação Manual de Cancelamento de Contrato", "ValorManual_CancelamentoContrato_Autorizar",
                    "Você precisa da permissão \"3.09 - Valor Manual Cancelamento Contrato - Autorizar\"",
                    "form", listener);
            setMensagemDetalhada("");
            cancelamentoContratoVO.getResponsavelLiberacaoCancelamento().setCodigo(auto.getUsuario().getCodigo());
            cancelamentoContratoVO.getResponsavelLiberacaoCancelamento().setUsername(auto.getUsuario().getUsername());
            cancelamentoContratoVO.setValorQuitacaoCancelamento(cancelamentoContratoVO.getValorASerDevolvidoBaseCalculo() * -1);
        } else {
            cancelamentoContratoVO.setResponsavelLiberacaoCancelamento(new UsuarioVO());
            setOnCompleteQuitacao("");
        }
    }

    public void obterLiberacaoDevolucaoCancelamento() throws Exception {
        AutorizacaoFuncionalidadeControle auto = getControlador(AutorizacaoFuncionalidadeControle.class);
        auto.setPedirPermissao(false);
        AutorizacaoFuncionalidadeListener listener = new AutorizacaoFuncionalidadeListener() {

            @Override
            public void onAutorizacaoComSucesso() throws Exception {
                AutorizacaoFuncionalidadeControle auto = getControlador(AutorizacaoFuncionalidadeControle.class);
                cancelamentoContratoVO.setResponsavelLiberacaoCancelamento(auto.getUsuario());
                autorizado = true;
                montarSucessoGrowl("");
            }

            @Override
            public void onAutorizacaoComErro(Exception e) {
                montarErro(e);
            }

            @Override
            public void onFecharModalAutorizacao() {
                cancelamentoContratoVO.setLiberacaoDevolucao(false);
                autorizado = false;
            }
        };

        limparMsg();
        autorizado = false;
        if (cancelamentoContratoVO.getLiberacaoDevolucao()) {
            cancelamentoContratoVO.setDevolucaoManualCancelamento(false);
            auto.autorizar("Confirmação de Liberação de Devolução", "Liberar_CancelamentoContrato_Autorizar",
                    "Você precisa da permissão \"3.08 - Liberar cobrança de valores no Cancelamento Contrato - Autorizar\"",
                    "form", listener);
            setMensagemDetalhada("", "");
            cancelamentoContratoVO.getResponsavelLiberacaoCancelamento().setCodigo(auto.getUsuario().getCodigo());
            cancelamentoContratoVO.getResponsavelLiberacaoCancelamento().setUsername(auto.getUsuario().getUsername());
        } else {
            cancelamentoContratoVO.setResponsavelLiberacaoCancelamento(new UsuarioVO());
        }
    }

    public Object alterarTipoCancelamento() throws Exception {
        limparSelecoesManuais();

        getCancelamentoContratoVO().setAlterarTipoCancelamento(isAlterarCancelamento());
        if (isAlterarCancelamento()) {
            if (getCancelamentoContratoVO().getValorASerDevolvidoBaseCalculo() < 0) {
                getCancelamentoContratoVO().setDevolucaoManualCancelamento(true);
                getCancelamentoContratoVO().setResponsavelLiberacaoCancelamento(getUsuarioLogado());
            }

            if (getCancelamentoContratoVO().getValorASerDevolvidoBaseCalculo() > 0) {
                getCancelamentoContratoVO().setQuitacaoManualCancelamento(true);
                getCancelamentoContratoVO().setResponsavelLiberacaoCancelamento(getUsuarioLogado());

            }
        }
        autorizado = true;
        return true;
    }

    private void limparSelecoesManuais() {
        getCancelamentoContratoVO().setDevolucaoManualCancelamento(false);
        getCancelamentoContratoVO().setLiberacaoDevolucao(false);

        getCancelamentoContratoVO().setQuitacaoManualCancelamento(false);
        getCancelamentoContratoVO().setQuitacaoCancelamento(false);
        getCancelamentoContratoVO().setLiberacaoCancelamento(false);
    }

    public String obterLiberacaoMultaCustos() throws Exception {
        AutorizacaoFuncionalidadeControle auto = (AutorizacaoFuncionalidadeControle) getControlador(AutorizacaoFuncionalidadeControle.class);
        auto.setPedirPermissao(false);
        AutorizacaoFuncionalidadeListener listener = new AutorizacaoFuncionalidadeListener() {

            @Override
            public void onAutorizacaoComSucesso() throws Exception {
                if (liberarMultaCustos) {
                    cancelamentoContratoVO.setValorTaxaCancelamento(0d);
                    cancelamentoContratoVO.setValorCreditoRestanteContratoValorMensalSemTaxaEMulta(0d);
                    cancelamentoContratoVO.setValorCreditoRestanteContratoVendaCreditoSemTaxaEMulta(0d);
                } else {
                    listarPagamento();
                }
            }

            @Override
            public void onFecharModalAutorizacao() {
                fechar();
            }

            @Override
            public void onAutorizacaoComErro(Exception e) {
                setMensagemDetalhada("msg_erro", e.getMessage());
            }

            @Override
            public String getExecutarAoCompletar() {
                return getOnComplete();
            }
        };
        auto.autorizar("Liberar multa e custos de Cancelamento de Contrato - Autorizar", "LiberarMultaCustosCancelamento_Autorizar",
                "Você precisa da permissão \"Liberar multa e custos de Cancelamento de Contrato - Autorizar\"",
                "formCancelamentoDevolucaoAuto", listener);
        return "";
    }

    public void fechar() {
        setLiberarMultaCustos(false);
        setLiberarMultaCancelamentoAntecipado(false);
    }

    public void consultarCliente() {
        try {
            List objs = new ArrayList();
            ClienteControle clienteControle = (ClienteControle) context().getExternalContext().getSessionMap().get("ClienteControle");
            if (getCampoConsultarCliente().equals("codigo")) {
                if (getValorConsultarCliente().equals("")) {
                    setValorConsultarCliente("0");
                }
                int valorInt = Integer.parseInt(getValorConsultarCliente());
                if (cancelamentoContratoVO.getTransferirEmDias()) {
                    objs = getFacade().getCliente().consultarPorCodigoPessoaSemClienteCancelamento(
                            new Integer(valorInt), getEmpresaLogado().getCodigo().intValue(), 0,
                            "", clienteControle.getClienteVO().getCodigo(), true, Uteis.NIVELMONTARDADOS_TELACONSULTAESPECIAL);
                } else {
                    objs = getFacade().getCliente().consultarPorCodigoPessoaSemClienteCancelamento(
                            new Integer(valorInt), getEmpresaLogado().getCodigo().intValue(), 0,
                            "", clienteControle.getClienteVO().getCodigo(), true, Uteis.NIVELMONTARDADOS_TELACONSULTAESPECIAL);
                }
            }
            if (getCampoConsultarCliente().equals("nomePessoa")) {
                if (cancelamentoContratoVO.getTransferirEmDias()) {
                    objs = getFacade().getCliente().consultarPorNomePessoaSemClienteCancelamento(
                            getValorConsultarCliente(), getEmpresaLogado().getCodigo().intValue(),
                            0, "", clienteControle.getClienteVO().getCodigo(), true, Uteis.NIVELMONTARDADOS_MINIMOS);
                } else {
                    objs = getFacade().getCliente().consultarPorNomePessoaSemClienteCancelamento(
                            getValorConsultarCliente(), getEmpresaLogado().getCodigo().intValue(),
                            0, "", clienteControle.getClienteVO().getCodigo(), true, Uteis.NIVELMONTARDADOS_MINIMOS);
                }
            }
            if (getCampoConsultarCliente().equals("matricula")) {
                if (cancelamentoContratoVO.getTransferirEmDias()) {
                    objs = getFacade().getCliente().consultarPorMatriculaPessoaSemClienteCancelamento(getValorConsultarCliente(),
                            getEmpresaLogado().getCodigo().intValue(), 0, "", contratoVO.getPessoa().getCodigo(), true, Uteis.NIVELMONTARDADOS_MINIMOS);
                } else {
                    objs = getFacade().getCliente().consultarPorMatriculaPessoaSemClienteCancelamento(getValorConsultarCliente(),
                            getEmpresaLogado().getCodigo().intValue(), 0, "", contratoVO.getPessoa().getCodigo(), true, Uteis.NIVELMONTARDADOS_MINIMOS);
                }
            }

            setListaConsultarCliente(objs);
            setMensagemDetalhada("");
            setMensagemID("msg_dados_consultados");
        } catch (Exception e) {
            setListaConsultarCliente(new ArrayList());
            setMensagemID("");
            setMensagemDetalhada(e.getMessage());
        }
    }

    public void consultarContrato() {
        try {
            List objs = new ArrayList();
            objs = getFacade().getContrato().consultarPorSituacaoContratoECodigoPessoaSemBolsa("AT", cancelamentoContratoVO.getCliente().getPessoa().getCodigo().intValue(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            setListaConsultarContrato(objs);
            setMensagemDetalhada("");
            setMensagemID("msg_dados_consultados");
        } catch (Exception e) {
            setListaConsultarCliente(new ArrayList());
            setMensagemID("");
            setMensagemDetalhada(e.getMessage());
        }
    }

    public void selecionarCliente() throws Exception {
        ClienteVO obj = (ClienteVO) context().getExternalContext().getRequestMap().get("cliente");
        obj = getFacade().getCliente().consultarPorChavePrimaria(obj.getCodigo(), Uteis.NIVELMONTARDADOS_TODOS);
        if (getCancelamentoContratoVO().isTransferencia() && obj.obterVinculoConsultor() == null) {
            this.getCancelamentoContratoVO().setCliente(new ClienteVO());
            montarErro("Cliente selecionado não possui consultor, favor verificar seu cadastro!");
            return;
        }
        this.getCancelamentoContratoVO().setCliente(obj);
        this.getCancelamentoContratoVO().setContratoCreditadoNrDias(new ContratoVO());
        setMensagemDetalhada("");
    }

    public void selecionarContrato() throws Exception {
        ContratoVO obj = (ContratoVO) context().getExternalContext().getRequestMap().get("contrato");
        obj = getFacade().getContrato().consultarPorChavePrimaria(obj.getCodigo(), Uteis.NIVELMONTARDADOS_TODOS);
        this.getCancelamentoContratoVO().setContratoCreditadoNrDias(obj);
        setMensagemDetalhada("");
    }

    public void limparCampoCliente() {
        this.getCancelamentoContratoVO().setCliente(new ClienteVO());
        limparCampoContrato();
        listaConsultarContrato = new ArrayList();
    }

    public void limparCampoContrato() {
        this.getCancelamentoContratoVO().setContratoCreditadoNrDias(new ContratoVO());
    }

    public void obterTipoTransferencia() throws Exception {
        cancelamentoContratoVO.obterValorFinalASerDevolvido("TR", getContratoVO());
        cancelamentoContratoVO.setAlteracaoValorTrans(false);
        valorTotalAlteracaoValor = 0.0;
        setAlterarValor(false);
        if (cancelamentoContratoVO.getDepositaNaConta()) {
            if (getContratoVO().getPessoa().getCodigo().intValue() != 0) {
                cancelamentoContratoVO.setCliente(getFacade().getCliente().consultarPorCodigoPessoa(getContratoVO().getPessoa().getCodigo().intValue(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
                cancelamentoContratoVO.setValorASerDevolvido(cancelamentoContratoVO.getValorASerDevolvidoRec() - cancelamentoContratoVO.getValorDevolverSaldoTransferenciaCredito());
            }
        }
        if (cancelamentoContratoVO.getDepositaNaContaTerceiro()) {
            cancelamentoContratoVO.setCliente(new ClienteVO());
            cancelamentoContratoVO.setValorASerDevolvido((cancelamentoContratoVO.getValorASerDevolvidoRec() - cancelamentoContratoVO.getValorDevolverSaldoTransferenciaCredito()) + cancelamentoContratoVO.getSaldoContaCorrenteCliente());

        }
        if (cancelamentoContratoVO.getTransferirEmDias()) {
            cancelamentoContratoVO.setCliente(new ClienteVO());
            cancelamentoContratoVO.setValorASerDevolvido(cancelamentoContratoVO.getValorASerDevolvidoRec() + cancelamentoContratoVO.getSaldoContaCorrenteCliente());

        }
        cancelamentoContratoVO.setValorASerDevolvidoBaseCalculo(cancelamentoContratoVO.getValorASerDevolvido() - cancelamentoContratoVO.getValorDevolverSaldoTransferenciaCredito());
    }

    public void validarParcelasContratoEnviadoPraRemessa() throws Exception {
        ClienteControle clienteControle = (ClienteControle) getControlador(ClienteControle.class.getSimpleName());
    }

    public String novo() {
        return novoGeral(false, null, null);
    }

    public String novoGeral(boolean servlet, ClienteControle clienteControle, UsuarioVO usuarioVO) {
        ContadorTempo.limparCronometro();
        ContadorTempo.iniciarContagem();
        notificarRecursoEmpresa(RecursoSistema.AFASTAMENTO_CANCELAMENTO);
        try {
            setArquivoAnexo(null);
            setExtensaoArquivoAnexo("");
            setApresentarUploadArquivo(false);
            setJustificativaOperacaoVO(new JustificativaOperacaoVO());

            setProcessandoOperacao(false);
            setFinalizouCancelamento(false);
            setContratoOperacaoVO(new ContratoOperacaoVO());
            setCancelamentoContratoVO(new CancelamentoContratoVO());
            setContratoVO(new ContratoVO());
            setApresentarBotoes(true);
            setAbrirRichConfirmacaoTransferencia(false);
            setMsgConfirmacaoLiberacao("");
            inicializarUsuarioLogado(usuarioVO);
            inicializarListasSelectItemTodosComboBox();
            if (!servlet) {
                clienteControle = (ClienteControle) getControlador(ClienteControle.class.getSimpleName());
            }
            setListaConsultarCliente(new ArrayList<>());
            setListaConsultarContrato(new ArrayList<>());
            setErro(false);
            setSucesso(false);
            setReciboDevolucao(new ReciboDevolucaoVO());
            setPlanoVencido(false);
            setValorTotalAlteracaoValor(0.0);
            setContratoVO(clienteControle.getContratoVO());
            cancelamentoContratoVO.setTipoParcelaCancelamento(TipoParcelaCancelamento.obterEnumPorSigla(getContratoVO().getEmpresa().getTipoParcelaCancelamento()));
            validarPlanoVencido(servlet ? clienteControle : null);
            setDataFinalContratoEmEdicao(true);
            setNovaDataFinalContrato(null);

            ConfiguracaoSistemaVO configuracaoSistemaVO = getFacade().getConfiguracaoSistema().consultarPorChavePrimaria(1, Uteis.NIVELMONTARDADOS_TODOS);
            if (configuracaoSistemaVO.getSesc() && contratoVO.getEmpresa().isUtilizaConfigCancelamentoSesc()
                    && verificarDuracaoQuantidadeParcelasParaConfigCancelamentoSesc()) {
                cancelamentoContratoVO.setConfiguracaoSesc(true);
            }

            clienteControle.setListaParcelasRemessa(getContratoComParcelasEnvioDeRemessa(getContratoVO().getCodigo()));
            dataFinalArmario = getFacade().getArmario().verificarAlunoTemArmarioEmAberto(clienteControle.getClienteVO().getCodigo());
            List<MovParcelaVO> parcelasEmRemessas = new ArrayList<MovParcelaVO>();
            for (MovParcelaVO parcelaVO : clienteControle.getListaParcelasRemessa()) {
                RemessaItemVO itemRemessa = getFacade().getRemessaItem().consultarPorParcelaPorCodigoRemessa(parcelaVO.getCodigo(), parcelaVO.getRemessa(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                if (!itemRemessa.getProps().containsKey(DCCAttEnum.StatusVenda.name())) {
                    parcelasEmRemessas.add(parcelaVO);
                }
            }
            clienteControle.setListaParcelasRemessa(parcelasEmRemessas);

            if (clienteControle.getContratoVO() != null && !UteisValidacao.emptyNumber(clienteControle.getContratoVO().getCodigo())) {
                ContratoRecorrenciaVO contratoRecorrenciaVO = getFacade().getContratoRecorrencia().consultarPorContrato(clienteControle.getContratoVO().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                getContratoVO().setContratoRecorrenciaVO(contratoRecorrenciaVO);
                clienteControle.getContratoVO().setContratoRecorrenciaVO(contratoRecorrenciaVO);
            }

            if (clienteControle.getListaParcelasRemessa().isEmpty() || (clienteControle.getContratoVO() != null && clienteControle.getContratoVO().getContratoRecorrenciaVO().isCancelamentoProporcional())) {
                clienteControle.setUrlPopup(null);

                if (clienteControle.getContratoVO() != null && clienteControle.getContratoVO().getContratoRecorrenciaVO().isCancelamentoProporcional()) {
                    getFacade().getZWFacade().calcularCancelamentoProporcional(getCancelamentoContratoVO(), getContratoVO(), Calendario.hoje(), false);
                    return "cancelamentoProporcional";
                } else if (clienteControle.getContratoVO() != null &&
                        (clienteControle.getContratoVO().getContratoRecorrenciaVO() != null &&
                                !UteisValidacao.emptyNumber(clienteControle.getContratoVO().getContratoRecorrenciaVO().getCodigo())) &&
                        clienteControle.getContratoVO().isCancelamentoAvaliandoParcelas()) {
                    getFacade().getZWFacade().calcularCancelamentoAvaliandoParcelas(getCancelamentoContratoVO(), getContratoVO(), Calendario.hoje(), false);
                    return "cancelamentoAvaliandoParcelas";
                } else {
                    return "cancelamento";
                }
            } else {
                clienteControle.setUrlPopup("Richfaces.showModalPanel('panelParcelasRemessas')");
                return null;
            }
        } catch (Exception e) {
            cancelamentoContratoVO.setMensagemErro(true);
            setMensagemDetalhada("msg_erro", e.getMessage());
            return "erroCancelamento";
        }
    }

    private Boolean verificarDuracaoQuantidadeParcelasParaConfigCancelamentoSesc() throws Exception {
        try {
            List<MovParcelaVO> parcelas = getFacade().getMovParcela().consultarParcelaSomentePlanoMensalOrMatriculaOrRenovacaoOrRematricaPorContrato(
                    getContratoVO().getCodigo(), cancelamentoContratoVO.isCancelarParcelaAnuidade(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            if (contratoVO.getContratoDuracao().getNumeroMeses().intValue() > 1 && parcelas.size() == 1) {
                return false;
            }
        } catch (Exception e) {
            throw new Exception("Erro ao verificar a duração do contrato e a quantidade de parcelas.");
        }
        return true;
    }

    public void upload(UploadEvent upload) throws Exception {
        setOnCompleteArquivoAnexo("");
        try {
            UploadItem item = upload.getUploadItem();
            if (item.getFile().length() > 512000) {
                setSucesso(false);
                setErro(true);
                setMensagemDetalhada("operacoes.arquivo.upload.tamanhoLimiteExcedido", "Arquivo tem tamanho superior a 512KB");
                throw new ConsistirException("Tamanho Superior a 512KB");
            }

            setArquivoAnexo(item.getFile());
            String[] partes = item.getFileName().split("\\.");
            setExtensaoArquivoAnexo("." + partes[partes.length - 1]);

            setSucesso(true);
            setErro(false);
            setMensagem("Arquivo enviado com sucesso");
            setMensagemDetalhada("", "");
            setOnCompleteArquivoAnexo(getMensagemNotificar());
        } catch (Exception e) {
            setOnCompleteArquivoAnexo(getMensagemNotificar());
            montarErro(e);
        }
    }

    public void validarPlanoVencido(ClienteControle clienteControle) throws ParseException, Exception {
        if (clienteControle == null) {
            clienteControle = (ClienteControle) getControlador(ClienteControle.class.getSimpleName());
        }
        if (clienteControle != null) {
            if (Uteis.getCompareData(negocio.comuns.utilitarias.Calendario.hoje(), getContratoVO().getVigenciaAteAjustada()) > 0) {
                setPlanoVencido(true);
            } else {
                setPlanoVencido(false);
            }
        }
    }

    /**
     * Rotina responsável por gravar no BD os dados editados de um novo objeto
     * da classe <code>ContratoOperacao</code>. Caso o objeto seja novo (ainda
     * não gravado no BD) é acionado a operação <code>incluir()</code>. Caso
     * contrário é acionado o <code>alterar()</code>. Se houver alguma
     * inconsistência o objeto não é gravado, sendo re-apresentado para o
     * usuário juntamente com uma mensagem de erro.
     */
    public void gravar() throws Exception {
        this.gravarGeral(false, getEmpresaLogado(), getUsuarioLogado());
    }

    public void gravarGeral(boolean servlet, EmpresaVO empresaVO, UsuarioVO usuarioVO) throws Exception {
        limparMsg();
        setFinalizouCancelamento(false);

        if (empresaVO == null) {
            empresaVO = getEmpresaLogado();
        }
        if (usuarioVO == null) {
            usuarioVO = getUsuarioLogado();
        }
        if(cancelamentoContratoVO.getTipoParcelaCancelamento() == null || !cancelamentoContratoVO.getTipoParcelaCancelamento().equals(TipoParcelaCancelamento.CANCELAMENTO_POR_MUDANCA_PLANO)) {
            if (contratoVO.isRegimeRecorrencia()) {
                cancelamentoContratoVO.setTipoParcelaCancelamento(TipoParcelaCancelamento.obterEnumPorSigla(contratoVO.getEmpresa().getTipoParcelaCancelamento()));
            } else {
                cancelamentoContratoVO.setTipoParcelaCancelamento(TipoParcelaCancelamento.TODAS_PARCELAS);
            }
        }

        CancelamentoContratoVO cancelamentoOriginal = (CancelamentoContratoVO) getCancelamentoContratoVO().getClone(false);
        if (this.reciboDevolucao != null && this.contratoVO.isVendaCreditoTreino()) {
            if (contratoDuracaoCreditoTreinoCancelar != null) {
                this.reciboDevolucao.setValorDevolverSaldoTransferenciaCredito(contratoDuracaoCreditoTreinoCancelar.getTotalTransferenciaSaldo());
            } else {
                this.reciboDevolucao.setValorDevolverSaldoTransferenciaCredito(0.0D);
            }
        }

        if (getContratoVO().getVigenciaDe() == null || getContratoVO().getVigenciaDe().equals("")) {
            throw new Exception("Contrato com a vigênciaDe nula. Favor tentar realizar a operação novamente.");
        }

        if (getFacade().getContrato().consultarContratoCancelado(getContratoVO().getCodigo())) {
            throw new Exception("Contrato já cancelado");
        }
        if (empresaVO == null || empresaVO.getCodigo() == 0) {
            throw new Exception("Não foi possível gerar (MovProduto) de Devolução, pois o SISTEMA NÃO ESTÁ LOGADO EM NENHUMA EMPRESA.");
        }

        for (MovParcelaVO movParc : getCancelamentoContratoVO().getListaParcelas()) {
            RemessaItemVO remessaItemVO = getFacade().getRemessaItem().obterUltimoItemRemessaPorCodigoParcela(movParc.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            if (remessaItemVO != null && remessaItemVO.getRemessa() != null && remessaItemVO.getRemessa().getSituacaoRemessa() == SituacaoRemessaEnum.REMESSA_ENVIADA) {
                throw new Exception("Não será possível cancelar o contrato, pois a " + movParc.getDescricao() + " está aguardando retorno de uma remessa.");
            }
        }

        for (MovParcelaVO movParc : getCancelamentoContratoVO().getListaParcelas()) {
            try {
                getFacade().getMovParcela().parcelaEstaBloqueadaPorCobranca(movParc); //Valida Transacao Online, DCO, Boleto Remessa
                boolean parcelaTemBoletoOnlineAguardandoPagamento = getFacade().getMovParcela().parcelaEstaBloqueadaPorBoletoPendente(movParc, true); //Valida Boleto Online
                if (parcelaTemBoletoOnlineAguardandoPagamento) {
                    throw new Exception("Não é possível cancelar o contrato. Primeiro deve Cancelar os boletos Gerado ou Aguardando Pagamento.");
                }
            } catch (Exception e) {
                throw e;
            }
        }

        if (getContratoVO().getBolsa()) {
            cancelamentoContratoVO.validarDados(getCancelamentoContratoVO(), "telaCancelamentoBolsa", getContratoVO(), isAlterarCancelamento());
            cancelamentoContratoVO.setContratoCancelar(getContratoVO());
            getFacade().getContratoOperacao().incluirOperacaoCancelamentoContratoTransferencia(cancelamentoContratoVO, getContratoVO(), cancelamentoContratoVO.getResponsavelCancelamento());
        } else if (cancelamentoContratoVO.getTipoDevolucaoCancelamento().equals("DE")) {
            //Caso haja a alteração do Cancelamento, para satisfazer as condições seguinte;
            if (isAlterarCancelamento()) {
                if (getCancelamentoContratoVO().getValorASerDevolvidoBaseCalculo() > 0) {
                    getCancelamentoContratoVO().setLiberacaoDevolucao(true);
                }

                if (getCancelamentoContratoVO().getValorASerDevolvidoBaseCalculo() < 0) {
                    getCancelamentoContratoVO().setValorASerDevolvidoBaseCalculo(getCancelamentoContratoVO().getValorDevolucaoCancelamento());
                }
            }

            //Continuando o procedimento do cancelamento;
            if ((cancelamentoContratoVO.getQuitacaoCancelamento() || cancelamentoContratoVO.isQuitacaoManualCancelamento())
                    && cancelamentoContratoVO.getValorQuitacaoCancelamento() > 0) {
                cancelamentoContratoVO.gerarMovProdutoQuitacaoCancelamento(getContratoVO(), empresaVO, cancelamentoContratoVO.getResponsavelCancelamento(), null);
            }

            if (cancelamentoContratoVO.getConfiguracaoSesc() && !cancelamentoContratoVO.getLiberacaoCancelamento() && !cancelamentoContratoVO.getLiberacaoDevolucao()) {
                double valorDevolverCliente = 0.0;

                MovParcelaVO parcelaAtual = new MovParcelaVO();
                Ordenacao.ordenarLista(cancelamentoContratoVO.getListaParcelas(), "dataVencimento");
                for (MovParcelaVO parcela : cancelamentoContratoVO.getListaParcelas()) {
                    if (Calendario.menorOuIgual(parcela.getDataVencimento(), cancelamentoContratoVO.getDataCancelamento())) {
                        parcelaAtual = parcela;
                    }
                }

                for (MovParcelaVO parcela : cancelamentoContratoVO.getListaParcelas()) {
                    if (Calendario.maiorOuIgual(parcela.getDataVencimento(), parcelaAtual.getDataVencimento())) {
                        if (parcela.getSituacao().equals("PG")) {
                            valorDevolverCliente += parcela.getValorParcela();
                        }
                    }
                }

                if (parcelaAtual.getSituacao().equals("PG")) {
                    long diasUtilizadosParcela = Uteis.nrDiasEntreDatas(parcelaAtual.getDataVencimento(), cancelamentoContratoVO.getDataCancelamento());
                    double valorProporcionalUtilizadoParcelaAtual;
                    if (diasUtilizadosParcela > 0) {
                        double valorDia = Uteis.arredondarForcando2CasasDecimais(parcelaAtual.getValorParcela() / 30);
                        valorProporcionalUtilizadoParcelaAtual = diasUtilizadosParcela * valorDia;
                    } else {
                        valorProporcionalUtilizadoParcelaAtual = parcelaAtual.getValorParcela();
                    }
                    valorDevolverCliente -= valorProporcionalUtilizadoParcelaAtual;
                }

                cancelamentoContratoVO.setValorASerDevolvidoBaseCalculo(valorDevolverCliente);
            }

            if (cancelamentoContratoVO.getValorASerDevolvidoBaseCalculo() > 0 && cancelamentoContratoVO.getValorQuitacaoCancelamento() == 0 && !cancelamentoContratoVO.getLiberacaoDevolucao()) {
                getFacade().getZWFacade().gerarMovProdutoDevolucaoDinheiro(getContratoVO(), empresaVO, cancelamentoContratoVO.getResponsavelCancelamento(), cancelamentoContratoVO);
            }

            if ((reciboDevolucao != null && reciboDevolucao.getValorRecebiveis() > 0.0) || (!cancelamentoContratoVO.getLiberacaoDevolucao()
                    && !cancelamentoContratoVO.getQuitacaoCancelamento() && !cancelamentoContratoVO.getLiberacaoCancelamento()
                    && !cancelamentoContratoVO.isQuitacaoManualCancelamento())) {
                reciboDevolucao.montarReciboDevolucao(cancelamentoContratoVO, getContratoVO());
            } else {
                reciboDevolucao = null;
            }

            TipoParcelaCancelamento tipoParcelaCancelamento = TipoParcelaCancelamento.obterEnumPorSigla(getContratoVO().getEmpresa().getTipoParcelaCancelamento());
            if(tipoParcelaCancelamento == null){
                tipoParcelaCancelamento = TipoParcelaCancelamento.TODAS_PARCELAS;
            }
            cancelamentoContratoVO.setTipoParcelaCancelamento(tipoParcelaCancelamento);
            cancelamentoContratoVO.setCancelarSaldoContaCorrente(!(tipoParcelaCancelamento.equals(TipoParcelaCancelamento.MAIOR_IGUAL_MES_ATUAL) || tipoParcelaCancelamento.equals(TipoParcelaCancelamento.MAIOR_IGUAL)));

            RetiradaAutomaticaControle retirada = (RetiradaAutomaticaControle) JSFUtilities.getFromSession(RetiradaAutomaticaControle.class.getSimpleName());
            if (retirada == null) {
                getFacade().getZWFacade().incluirOperacaoCancelamentoContratoDevolucao(cancelamentoContratoVO,
                        getContratoVO(), cancelamentoContratoVO.getResponsavelCancelamento(),
                        reciboDevolucao, new ArrayList<ContaVO>(), null, true);
            } else {
                CaixaControle caixaControle = (CaixaControle) JSFUtilities.getFromSession(CaixaControle.class.getSimpleName());
                getFacade().getZWFacade().incluirOperacaoCancelamentoContratoDevolucao(cancelamentoContratoVO,
                        getContratoVO(), cancelamentoContratoVO.getResponsavelCancelamento(),
                        reciboDevolucao, retirada.getContas(), caixaControle.getCaixaVoEmAberto() == null ? null : caixaControle.getCaixaVoEmAberto().getCodigo(), true);
            }
        } else {
            cancelamentoContratoVO.validarDados(getCancelamentoContratoVO(), "telaTransferenciaCliente", getContratoVO(), isAlterarCancelamento());
            cancelamentoContratoVO.setContratoCancelar(getContratoVO());
            if (!getCancelamentoContratoVO().getCliente().getSituacao().equals("AT") && !getCancelamentoContratoVO().getDepositaNaContaTerceiro()) {
                notificarRecursoEmpresa(RecursoSistema.CANCELANDO_TRANSFERINDO_DIAS_EM_CONTRATO);
                apresentarBotoesTransferenciaContratoDias = false;
                getFacade().getZWFacade().transferirContratosDoClienteParaOutraEmpresa(getCancelamentoContratoVO().getCliente(), empresaVO, cancelamentoContratoVO.getResponsavelCancelamento(), getContratoVO(), getCancelamentoContratoVO(), true);
            } else {
                getFacade().getContratoOperacao().incluirOperacaoCancelamentoContratoTransferencia(cancelamentoContratoVO, getContratoVO(), cancelamentoContratoVO.getResponsavelCancelamento());
            }
        }

        try {
            ClienteVO cliente = getFacade().getCliente().consultarPorCodigoPessoa(getContratoVO().getPessoa().getCodigo(), getContratoVO().getEmpresa().getCodigo(), Uteis.NIVELMONTARDADOS_TODOS);
            GestaoAulaService gestaoAulaService = new GestaoAulaService(Conexao.getFromSession(), (String) JSFUtilities.getFromSession("key"));
            List<AlunoHorarioTurmaVO> listaHorarios = getFacade().getHorarioTurma().consultarHorarioTurmasCancelamentoContrato(cliente.getCodigo(), getContratoVO().getEmpresa().getCodigo(),Uteis.NIVELMONTARDADOS_TODOS);
            for(AlunoHorarioTurmaVO horario : listaHorarios){
                getFacade().getHorarioTurma().excluirAlunoHorarioturma(horario.getCodigo());
                gestaoAulaService.incluirLogDesmarcaoAulaCancelamentoContrato(cliente, usuarioVO, horario);
            }
        } catch (Exception e) {
            Uteis.logar(e, this.getClass());
        }
        //LOG - INICIO
        try {
            cancelamentoContratoVO.setObjetoVOAntesAlteracao(new CancelamentoContratoVO());
            cancelamentoContratoVO.setNovoObj(true);
            if (servlet) {
                registrarLogObjetoVOGeralAlterandoResponsavel(cancelamentoContratoVO, cancelamentoContratoVO.getCodigo(), "CANCELAMENTOCONTRATO", getContratoVO().getPessoa().getCodigo(), false, cancelamentoContratoVO.getResponsavelCancelamento().getNome(), usuarioVO, getFacade().getLog().getCon());
            } else {
                registrarLogObjetoVOGeralAlterandoResponsavel(cancelamentoContratoVO, cancelamentoContratoVO.getCodigo(), "CANCELAMENTOCONTRATO", getContratoVO().getPessoa().getCodigo(), false, cancelamentoContratoVO.getResponsavelCancelamento().getNome());
            }
        } catch (Exception e) {
            registrarLogErroObjetoVO("CANCELAMENTOCONTRATO", getContratoVO().getPessoa().getCodigo(), "ERRO AO GERAR LOG DE INCLUSÃO DE CANCELAMENTO CONTRATO", cancelamentoContratoVO.getResponsavelCancelamento().getNome(), usuarioVO.getUserOamd());
            e.printStackTrace();
        }
        //LOG - FIM
        ContratoOperacaoVO comprovanteOperacao = getCancelamentoContratoVO().getContratoOperacaoVO();
        ContratoOperacaoVO comprovanteOperacaoTransSaida = getCancelamentoContratoVO().getComprovanteTransDiasVO();
        boolean devolucaoManualCancelamento = getCancelamentoContratoVO().getDevolucaoManualCancelamento();
        boolean quitacaoManualCancelamento = getCancelamentoContratoVO().isQuitacaoManualCancelamento();
        setCancelamentoContratoVO(cancelamentoOriginal);
        getCancelamentoContratoVO().setDevolucaoManualCancelamento(devolucaoManualCancelamento);
        getCancelamentoContratoVO().setQuitacaoManualCancelamento(quitacaoManualCancelamento);
        getCancelamentoContratoVO().setContratoOperacaoVO(comprovanteOperacao);
        getCancelamentoContratoVO().setComprovanteTransDiasVO(comprovanteOperacaoTransSaida);
        getFacade().getControleCreditoTreino().zerarPorCancelamento(getCancelamentoContratoVO().getCliente().getCodigo(), contratoVO);
        setMensagemDetalhada("", "");
        deletarReposicoes(getContratoVO().getCodigo(), getCancelamentoContratoVO().getDataCancelamento());
        setApresentarBotoes(false);
        setErro(false);
        setSucesso(true);

        cancelamentoNegociacaoBitrix();

        if (getContratoVO().getEmpresa().isEnviarEmailCancelamento() && (getContratoVO().isCancelamentoAntecipado() || getContratoVO().isCancelamentoObrigatoriedadePagamento())) {
            enviarEmailCancelamentoContrato(servlet);
        }
        setFinalizouCancelamento(true);
        notificarRecursoEmpresa(RecursoSistema.AFASTAMENTO_CANCELAMENTO_SUCESSO, ContadorTempo.encerraContagem());
    }

    private void cancelamentoNegociacaoBitrix() {
        try {
            //Notifica bitrix se o cliente originou da integração
            ClienteVO clienteVO1s = getFacade().getCliente().consultarPorCodigoPessoa(getContratoVO().getPessoa().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            LeadVO lead = getFacade().getLead().consultarPorCliente(clienteVO1s.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            if (lead.getTipo() == TipoLeadEnum.BITIRX24) {
                String chave = (String) JSFUtilities.getFromSession("key");
                List<ConfiguracaoEmpresaBitrixVO> listbitrix = getFacade().getConfiguracaoSistemaCRM().consultarConfiguracaoEmpresaBitrix24(chave);
                ConfiguracaoEmpresaBitrixVO config = listbitrix.get(0);
                IntegracaoLeadGenericaServiceImpl bitrix = new IntegracaoLeadGenericaServiceImpl(getFacade().getConfiguracaoSistemaCRM().getCon());

                String jsonString = config.getAcao();
                // Remova as aspas simples e substitua por aspas duplas para tornar a string válida no formato JSON
                jsonString = jsonString.replace("'", "\"");
                // Carrega a lista de campos customizados para recuperar o nome do objeto pelo label
                String jsonFields = bitrix.leadFieldBitrix(config.getUrl(),"l");
                JSONObject jsonObject = new JSONObject(jsonString);
                JSONObject json = Uteis.extractField(jsonFields, "Status Pacto");

                JSONObject fields = new JSONObject();
                JSONObject statuspacto = Uteis.extractField(jsonFields, "Status Pacto");
                JSONObject rg = Uteis.extractField(jsonFields,"RG");
                JSONObject cpf = Uteis.extractField(jsonFields,"CPF");
                fields.put(rg.get("title").toString(), getContratoVO().getPessoa().getRg());
                fields.put("NAME", getContratoVO().getPessoa().getNome());
                fields.put(cpf.get("title").toString(), getContratoVO().getPessoa().getCfp());
                fields.put("STATUS_ID", "JUNK");
                fields.put(statuspacto.get("title").toString(), "CANCELADO");
                bitrix.updateStausBitrix(config.getUrl() + jsonObject.getString("updatelead"), (int) lead.getIdLead(), fields);

                String title = "'Lead #"+ lead.getIdLead() +"'";
                fields =  new JSONObject();
                fields.put("filter",  new JSONObject( "{ \"TITLE\": "+ title +"}"));
                fields.put("select",  new JSONArray( "[ \"ID\", \"TITLE\", \"STAGE_ID\", \"PROBABILITY\", \"OPPORTUNITY\", \"CURRENCY_ID\" ]"));
                Object jsonNegocio = bitrix.listNegociacaoBitrix(config.getUrl() + "/crm.deal.list", fields);
                JSONObject JSONObject = new JSONObject(jsonNegocio.toString());

                JSONArray jsonArray = (JSONArray)JSONObject.get("result");
                JSONObject jsOb = new JSONObject(jsonArray.get(0).toString());
                cancelarNegocioacaoBitrix(String.valueOf(lead.getIdLead()), bitrix, config, jsOb.get("ID").toString());
            }
        }
        catch (Exception e) {
            e.printStackTrace();
        }
    }

    private static void  cancelarNegocioacaoBitrix(String idLead , IntegracaoLeadGenericaServiceImpl bitrix, ConfiguracaoEmpresaBitrixVO config, String id )  throws Exception {
        JSONObject fields =  new JSONObject();
        fields.put("TITLE","Lead #" +  idLead);
        fields.put("TYPE_ID", "SALE");
        fields.put("STAGE_ID", "LOSE");
        fields.put("LEAD_ID", idLead);
        if(!UteisValidacao.emptyString(idLead)){
            bitrix.createNegociacaoBitrix(config.getUrl() + "/crm.deal.update", fields,id);
        }

    }

    private void deletarReposicoes(Integer contrato, Date dataCancelamento) throws Exception {
        List<ReposicaoVO> reposicaoVOS = getFacade().getReposicao()
                .consultarReposicoesApartirDeUmaDataBase(Calendario.getMaior(dataCancelamento, Calendario.hoje()), contrato, Uteis.NIVELMONTARDADOS_COM_CLIENTE);

        GestaoAulaService service = new GestaoAulaService(getFacade().getZWFacade().getCon(), (String) JSFUtilities.getFromSession("key"));
        if (!UteisValidacao.emptyList(reposicaoVOS)) {
            for (ReposicaoVO reposicaoVO : reposicaoVOS) {
                service.desmarcarReposicao(reposicaoVO, true, true);
            }
        }
    }

    public void imprimirReciboDevolucao() {
        imprimirReciboDevolucao(getReciboDevolucao());
    }

    public void validarDadosTransferencia() {
        try {
            notificarRecursoEmpresa(RecursoSistema.CANCELAMENTO_TRANSFERENCIA_CLIENTE);
            //validação sendo feita antes do autorizacaofuncionalidadecontrole devido lançamento de throw não ser gerado no método principal para mostrar na tela
            if (!getUsuarioLogado().isPedirSenhaFuncionalidade()) {
                validarPermissao("MovimentoContaCorrenteCliente", "2.16 - Movimento de Conta Corrente do Cliente", getUsuarioLogado());
            }
            AutorizacaoFuncionalidadeControle auto = getControlador(AutorizacaoFuncionalidadeControle.class);
            auto.setPedirPermissao(false);
            AutorizacaoFuncionalidadeListener listener = new AutorizacaoFuncionalidadeListener() {

                @Override
                public void onAutorizacaoComSucesso() throws Exception {
                    try {
                        cancelamentoContratoVO.setResponsavelCancelamento(getUsuarioLogado());
                        cancelamentoContratoVO.getResponsavelCancelamento().setUserOamd(getUsuarioLogado().getUserOamd());
                        cancelamentoContratoVO.setUsuarioVO(auto.getUsuario());
                        setProcessandoOperacao(true);
                        gravar();
                        setExecutarAoCompletar("fireElementFromParent('form:btnAtualizaCliente');");
                        setMensagemDetalhada("", "");
                        setMensagemID("msg_dados_gravados");
                        setApresentarBotoes(false);
                        setErro(false);
                        setSucesso(true);
                        ServicoNotificacaoPush.enviaNotificacaoCancelamentoContrato(getKey(), contratoVO.getEmpresa().getCodigo(), false, cancelamentoContratoVO.getUsuarioVO() != null ? cancelamentoContratoVO.getUsuarioVO().getCodigo() : 0, contratoVO.getEmpresa().getNome());
                    } catch (Exception ex) {
                        montarErro(ex.getMessage());
                    }
                    setProcessandoOperacao(false);
                }

                @Override
                public void onAutorizacaoComErro(Exception e) {
                    setMensagemID("");
                    setMensagemDetalhada(e);
                }

                @Override
                public String getExecutarAoCompletar() {
                    return getOnComplete();
                }
            };
            limparMsg();
            cancelamentoContratoVO.validarDados(getCancelamentoContratoVO(), "telaTransferenciaCliente", getContratoVO(), isAlterarCancelamento());
            auto.autorizar("Confirmação de Cancelamento de Contrato", "CancelamentoContrato_Autorizar",
                    "Você precisa da permissão \"3.07 - Cancelamento Contrato - Autorizar\"",
                    "form,panelBotoes,panelMensagem", listener);

        } catch (Exception e) {
            setMensagemID("");
            setMensagemDetalhada(e);
            montarErro(getMensagemDetalhada());
        }
    }

    public void consultarUsuarioSenha() throws Exception {
        try {
            cancelamentoContratoVO.setResponsavelCancelamento(getFacade().getUsuario().consultarPorChavePrimaria(cancelamentoContratoVO.getResponsavelCancelamento().getCodigo(), Uteis.NIVELMONTARDADOS_TODOS));
            cancelamentoContratoVO.getResponsavelCancelamento().setUserOamd(getUsuarioLogado().getUserOamd());
            setMensagemID("msg_dados_consultados");
            setMensagemDetalhada("");
        } catch (Exception e) {
            setMensagemID("");
            setMensagemDetalhada(e.getMessage());
        }
    }

    public void consultarUsuarioSenhaResponsavelLiberacao() throws Exception {
        try {
            cancelamentoContratoVO.setResponsavelLiberacaoCancelamento(getFacade().getUsuario().consultarPorChavePrimaria(cancelamentoContratoVO.getResponsavelLiberacaoCancelamento().getCodigo(), Uteis.NIVELMONTARDADOS_TODOS));
            cancelamentoContratoVO.getResponsavelLiberacaoCancelamento().setUserOamd(getUsuarioLogado().getUserOamd());
            setMensagemID("msg_dados_consultados");
            setMensagemDetalhada("");
        } catch (Exception e) {
            setMensagemID("");
            setMensagemDetalhada(e.getMessage());
        }
    }

    public void validarAutorizacao() {
        AutorizacaoFuncionalidadeControle auto = getControlador(AutorizacaoFuncionalidadeControle.class);
        auto.setPedirPermissao(false);
        notificarRecursoEmpresa(RecursoSistema.CANCELAMENTO_DEVOLUCAO_CLIENTE);
        AutorizacaoFuncionalidadeListener listener = new AutorizacaoFuncionalidadeListener() {

            @Override
            public void onAutorizacaoComSucesso() throws Exception {
                setProcessandoOperacao(true);
                cancelamentoContratoVO.setResponsavelCancelamento(auto.getUsuario());
                cancelamentoContratoVO.getResponsavelCancelamento().setUserOamd(auto.getUsuario().getUserOamd());
                cancelamentoContratoVO.setUsuarioVO(auto.getUsuario());
                gravar();
                if (justificativaOperacaoVO.getNecessarioAnexarComprovante()) {
                    byte[] encoded = Base64.encodeBase64(FileUtils.readFileToByteArray(arquivoAnexo));
                    String imagemCancelamento = new String(encoded, StandardCharsets.US_ASCII);

                    ContratoAssinaturaDigitalServiceInterface serviceInterface = new ContratoAssinaturaDigitalServiceImpl(Conexao.getFromSession());
                    serviceInterface.salvarAnexoCancelamento(cancelamentoContratoVO.getContratoCancelar().getCodigo(), getUsuarioLogado(),
                            imagemCancelamento, true);
                }
                if(cancelamentoContratoVO != null && cancelamentoContratoVO.getCodigo() != null) {
                    ServicoNotificacaoPush.enviaNotificacaoCancelamentoContrato(getKey(), contratoVO.getEmpresa().getCodigo(), false, cancelamentoContratoVO.getUsuarioVO() != null ? cancelamentoContratoVO.getUsuarioVO().getCodigo() : 0, contratoVO.getEmpresa().getNome());
                }
                montarSucessoGrowl("");
                setProcessandoOperacao(false);
            }

            @Override
            public void onAutorizacaoComErro(Exception e) {
                montarErro(e);
            }

            @Override
            public String getExecutarAoCompletar() {
                return getOnComplete();
            }
        };

        limparMsg();
        auto.autorizar("Confirmação de Cancelamento de Contrato", "CancelamentoContrato_Autorizar",
                "Você precisa da permissão \"3.07 - Cancelamento Contrato - Autorizar\"",
                "form,panelBotoes,panelMensagem", listener);
        setMsgAlert("");
    }

    public void montarListaSelectItemJustificativaCancelamento(String prm) throws Exception {
        List resultadoConsulta = consultarJustificativaCancelamento("");
        List objs = new ArrayList();
        objs.add(new SelectItem(0, ""));
        for (Object aResultadoConsulta : resultadoConsulta) {
            JustificativaOperacaoVO obj = (JustificativaOperacaoVO) aResultadoConsulta;
            objs.add(new SelectItem(obj.getCodigo(), obj.getDescricao()));
        }
        setListaSelectItemJustificativaOperacao(objs);
    }

    public void montarListaSelectItemJustificativaCancelamento() {
        try {
            montarListaSelectItemJustificativaCancelamento("");
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public List consultarJustificativaCancelamento(String nomePrm) throws Exception {
        return getFacade().getJustificativaOperacao().consultarPorTipoOperacao("CA", getEmpresaLogado().getCodigo(), false, Uteis.NIVELMONTARDADOS_TODOS);
    }

    public void inicializarListasSelectItemTodosComboBox() {
        montarListaSelectItemJustificativaCancelamento();
    }

    public List getListaSelectItemTipoDevolucaoCancelamento() throws Exception {
        List objs = new ArrayList();
        objs.add(new SelectItem("TR", "Transferência"));
        objs.add(new SelectItem("DE", "Devolução"));
        return objs;
    }

    public List getListaSelectItemTipoTransferenciaCancelamento() throws Exception {
        List objs = new ArrayList();
        objs.add(new SelectItem("DE", "Depositar na Conta"));
        objs.add(new SelectItem("DT", "Depositar na Conta de Terceiro"));
        if (!this.contratoVO.isVendaCreditoTreino()) {
            objs.add(new SelectItem("TR", "Transferência de Dias restantes"));
        }
        return objs;
    }

    public List getTipoConsultarComboCliente() {
        List itens = new ArrayList();
        itens.add(new SelectItem("nomePessoa", "Nome"));
        itens.add(new SelectItem("matricula", "Matrícula"));
        return itens;
    }

    public void irPaginaInicial() throws Exception {
        controleConsulta.setPaginaAtual(1);
        this.consultar();
    }

    public void irPaginaAnterior() throws Exception {
        controleConsulta.setPaginaAtual(controleConsulta.getPaginaAtual() - 1);
        this.consultar();
    }

    public void irPaginaPosterior() throws Exception {
        controleConsulta.setPaginaAtual(controleConsulta.getPaginaAtual() + 1);
        this.consultar();
    }

    public void irPaginaFinal() throws Exception {
        controleConsulta.setPaginaAtual(controleConsulta.getNrTotalPaginas());
        this.consultar();
    }

    public String inicializarConsultar() {
        setPaginaAtualDeTodas("0/0");
        setListaConsulta(new ArrayList());
        definirVisibilidadeLinksNavegacao(0, 0);
        return "consultar";
    }

    public String voltarTelaCancelamento() {
        cancelamentoContratoVO.setTipoTranferenciaCancelamento("");
        cancelamentoContratoVO.setMensagemErro(false);
        cancelamentoContratoVO.setQuitacaoCancelamento(false);
        cancelamentoContratoVO.setQuitacaoManualCancelamento(false);
        cancelamentoContratoVO.setLiberacaoCancelamento(false);
        setAbrirRichConfirmacaoManual(false);
        setMensagemDetalhada("");
        setErro(false);
        setSucesso(false);

        if (contratoVO.isCancelamentoObrigatoriedadePagamento()) {
            return "voltarCancelamentoInicio";
        } else {
            return "voltarCancelamento";
        }
    }

    public String voltarTelaTransferencia() {
        cancelamentoContratoVO.setTipoDevolucaoCancelamento("");
        cancelamentoContratoVO.setTipoTranferenciaCancelamento("");
        cancelamentoContratoVO.setMensagemErro(false);
        return "voltarTransferencia";
    }

    public ContratoOperacaoVO getContratoOperacaoVO() {
        return contratoOperacaoVO;
    }

    public void setContratoOperacaoVO(ContratoOperacaoVO contratoOperacaoVO) {
        this.contratoOperacaoVO = contratoOperacaoVO;
    }

    public CancelamentoContratoVO getCancelamentoContratoVO() {
        return cancelamentoContratoVO;
    }

    public void setCancelamentoContratoVO(CancelamentoContratoVO cancelamentoContratoVO) {
        this.cancelamentoContratoVO = cancelamentoContratoVO;
    }

    public ContratoVO getContratoVO() {
        return contratoVO;
    }

    public void setContratoVO(ContratoVO contratoVO) {
        this.contratoVO = contratoVO;
    }

    public Boolean getApresentarBotoes() {
        return apresentarBotoes;
    }

    public void setApresentarBotoes(Boolean apresentarBotoes) {
        this.apresentarBotoes = apresentarBotoes;
    }

    public List getListaSelectItemJustificativaOperacao() {
        return listaSelectItemJustificativaOperacao;
    }

    public void setListaSelectItemJustificativaOperacao(List listaSelectItemJustificativaOperacao) {
        this.listaSelectItemJustificativaOperacao = listaSelectItemJustificativaOperacao;
    }

    public List getListaConsultarCliente() {
        return listaConsultarCliente;
    }

    public void setListaConsultarCliente(List listaConsultarCliente) {
        this.listaConsultarCliente = listaConsultarCliente;
    }

    public List getListaConsultarContrato() {
        return listaConsultarContrato;
    }

    public void setListaConsultarContrato(List listaConsultarContrato) {
        this.listaConsultarContrato = listaConsultarContrato;
    }

    public String getCampoConsultarCliente() {
        return campoConsultarCliente;
    }

    public void setCampoConsultarCliente(String campoConsultarCliente) {
        this.campoConsultarCliente = campoConsultarCliente;
    }

    public String getValorConsultarCliente() {
        return valorConsultarCliente;
    }

    public void setValorConsultarCliente(String valorConsultarCliente) {
        this.valorConsultarCliente = valorConsultarCliente;
    }

    public String getConsultarCliente() {
        return consultarCliente;
    }

    public void setConsultarCliente(String consultarCliente) {
        this.consultarCliente = consultarCliente;
    }

    public void setAbrirRichConfirmacaoTransferencia(Boolean abrirRichConfirmacaoTransferencia) {
        this.abrirRichConfirmacaoTransferencia = abrirRichConfirmacaoTransferencia;
    }


    public List getConsultarContrato() {
        return consultarContrato;
    }

    public void setConsultarContrato(List consultarContrato) {
        this.consultarContrato = consultarContrato;
    }


    public void setCampoConsultarContrato(String campoConsultarContrato) {
        this.campoConsultarContrato = campoConsultarContrato;
    }


    public void setValorConsultarContrato(String valorConsultarContrato) {
        this.valorConsultarContrato = valorConsultarContrato;
    }


    public void setAbrirRichConfirmacaoManual(boolean abrirRichConfirmacaoManual) {
        this.abrirRichConfirmacaoManual = abrirRichConfirmacaoManual;
    }


    public void setMsgConfirmacaoLiberacao(String msgConfirmacaoLiberacao) {
        this.msgConfirmacaoLiberacao = msgConfirmacaoLiberacao;
    }

    @Override
    protected void limparRecursosMemoria() {
        super.limparRecursosMemoria();
        cancelamentoContratoVO = null;
        contratoOperacaoVO = null;
        contratoVO = null;
        clienteVO = null;
        planoVO = null;
        apresentarBotoes = null;
        listaSelectItemJustificativaOperacao = new ArrayList();
        listaConsultarCliente = new ArrayList();
        listaConsultarContrato = new ArrayList();
        campoConsultarCliente = null;
        campoConsultarContrato = null;
        valorConsultarCliente = null;
        valorConsultarContrato = null;
        consultarCliente = null;
        consultarContrato = null;
        abrirRichConfirmacaoTransferencia = null;
        abrirRichConfirmacaoLiberacao = null;
        msgConfirmacaoLiberacao = null;
        planoFacade = null;
    }

    public void alterarValor() {
        limparMsg();
        setMsgAlert("");
        if (alterarValor && !getCancelamentoContratoVO().getDepositaNaContaTerceiro() && !getCancelamentoContratoVO().getDepositaNaConta() && !getCancelamentoContratoVO().getTransferirEmDias()) {
            montarMsgAlert("Selecione primeiro o tipo de transferência");
            setAlterarValor(false);
            return;
        }
        if (!alterarValor) {
            cancelamentoContratoVO.setValorASerDevolvido(cancelamentoContratoVO.getValorASerDevolvidoBaseCalculo() - cancelamentoContratoVO.getValorDevolverSaldoTransferenciaCredito());
            if (this.contratoVO.isVendaCreditoTreino()) {
                cancelamentoContratoVO.setValorDevolverSaldoTransferenciaCredito(this.contratoDuracaoCreditoTreinoCancelar.getTotalTransferenciaSaldo());
            }
            cancelamentoContratoVO.setValorASerDevolvidoRec(cancelamentoContratoVO.getValorASerDevolvidoBaseCalculo());
            cancelamentoContratoVO.setAlteracaoValorTrans(false);
        }
    }

    public void alterarMulta() {
        limparMsg();
        setMsgAlert("");
        if (usuarioDesejaAlterarMulta) {
            valorAlteracaoPercentualMulta = cancelamentoContratoVO.getPercentualMultaCancelamento();
        } else {
            cancelamentoContratoVO.setPercentualMultaCancelamento(cancelamentoContratoVO.getPercentualMultaCancelamentoRec());
        }
    }

    public void aplicarAlterarPercentual() {
        AutorizacaoFuncionalidadeControle auto = (AutorizacaoFuncionalidadeControle) getControlador(AutorizacaoFuncionalidadeControle.class);
        auto.setPedirPermissao(false);
        limparMsg();
        setMsgAlert("");
        AutorizacaoFuncionalidadeListener listener = new AutorizacaoFuncionalidadeListener() {
            @Override
            public void onAutorizacaoComSucesso() throws Exception {
                cancelamentoContratoVO.setResponsavelCancelamento(getUsuarioLogado());
                cancelamentoContratoVO.getResponsavelCancelamento().setUserOamd(getUsuarioLogado().getUserOamd());
                cancelamentoContratoVO.setPercentualMultaCancelamentoRec(cancelamentoContratoVO.getPercentualMultaCancelamento().doubleValue());
                cancelamentoContratoVO.setPercentualMultaCancelamento(valorAlteracaoPercentualMulta);
                calcularDevolucao();
                obterCancelamentoPorDevolucao();
            }

            @Override
            public void onAutorizacaoComErro(Exception e) {
                setMensagemDetalhada(e);
                montarMsgAlert(e.getMessage());
            }

            @Override
            public String getExecutarAoCompletar() {
                return getOnComplete();
            }
        };
        try {
            if (valorAlteracaoPercentualMulta > 100) {
                throw new Exception(getMensagemInternalizacao("percentual_nao_pode_ser_maior_100"));
            }
            auto.autorizar("Autorização Valor Manual Percentual Multa Cancelamento", "ValorManualPercentualMultaCancelamento",
                    "Você precisa da permissão \"Valor Manual Percentual Multa Cancelamento\"",
                    "formCancelamentoDevolucaoAuto", listener);
        } catch (Exception e) {
            setMensagemDetalhada(e);
            montarMsgAlert(e.getMessage());
        }

    }

    public void aplicarAlterarValor() {
        setMsgAlert("");
        AutorizacaoFuncionalidadeControle auto = (AutorizacaoFuncionalidadeControle) getControlador(AutorizacaoFuncionalidadeControle.class);
        auto.setPedirPermissao(false);
        limparMsg();
        try {
            AutorizacaoFuncionalidadeListener listener = new AutorizacaoFuncionalidadeListener() {

                @Override
                public void onAutorizacaoComSucesso() throws Exception {
                    cancelamentoContratoVO.setResponsavelCancelamento(getUsuarioLogado());
                    cancelamentoContratoVO.getResponsavelCancelamento().setUserOamd(getUsuarioLogado().getUserOamd());
                    if ((getCancelamentoContratoVO().getTransferirEmDias() || getCancelamentoContratoVO().getDepositaNaContaTerceiro() && getCancelamentoContratoVO().getSaldoContaCorrenteCliente() > 0)
                            && valorTotalAlteracaoValor < getCancelamentoContratoVO().getSaldoContaCorrenteCliente()) {
                        throw new Exception("Ao transferir dias ou valores para outro cliente, o sistema transfere o que o cliente já tem em conta corrente. Esse cliente já tem um saldo de " + Formatador.formatarValorMonetario(getCancelamentoContratoVO().getSaldoContaCorrenteCliente()) + ". Valor informado para transferência não pode ser menor que isso ou avalie primeiro depositar na conta do cliente e depois transferir para um terceiro");
                    }
                    cancelamentoContratoVO.setValorASerDevolvido(valorTotalAlteracaoValor);
                    cancelamentoContratoVO.setValorDevolverSaldoTransferenciaCredito(0.0);
                    if (getCancelamentoContratoVO().getTransferirEmDias() || getCancelamentoContratoVO().getDepositaNaContaTerceiro()) {
                        cancelamentoContratoVO.setValorASerDevolvidoRec(valorTotalAlteracaoValor - getCancelamentoContratoVO().getSaldoContaCorrenteCliente());
                    } else {
                        cancelamentoContratoVO.setValorASerDevolvidoRec(valorTotalAlteracaoValor);
                    }
                    cancelamentoContratoVO.setAlteracaoValorTrans(true);
                }

                @Override
                public String getExecutarAoCompletar() {
                    return getOnComplete();
                }
            };
            Double valorMaximo = Uteis.arredondarForcando2CasasDecimais(getCancelamentoContratoVO().getValorTotalPagoPeloCliente() - getCancelamentoContratoVO().getValorTotalSomaProdutoContratos());
            if (getCancelamentoContratoVO().getTransferirEmDias() || getCancelamentoContratoVO().getDepositaNaContaTerceiro()) {
                valorMaximo = Uteis.arredondarForcando2CasasDecimais(valorMaximo + getCancelamentoContratoVO().getSaldoContaCorrenteCliente());
            }
            if (valorTotalAlteracaoValor > valorMaximo) {
                throw new Exception("Valor máximo permitido para essa operação, baseado no valor pago e no valor dos produto, é " + Formatador.formatarValorMonetarioSemMoeda(valorMaximo));
            }
            auto.autorizar("Autorização Valor Manual Cancelamento Contrato", "ValorManual_CancelamentoContrato_Autorizar",
                    "Você precisa da permissão \"Valor Manual Cancelamento Contrato - Autorizar\"",
                    "formCancelamentoAuto", listener);
        } catch (Exception e) {
            montarErro(e);
        }
    }

    public void validarDataRetroativa() {
        try {
            ClienteControle clienteControle = (ClienteControle) context().getExternalContext().getSessionMap().get("ClienteControle");
            if (clienteControle.validarPermisaoUsuario("LiberarDataCancelamentoRetroativo_Autorizar", "3.27 - Liberar Data de Cancelamento Retroativo - Autorizar")) {
                setValidarDataRetroativa("");
            } else {
                setValidarDataRetroativa("desabilitarDataRetroativa");
            }
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
            setValidarDataRetroativa("desabilitarDataRetroativa");
        }
    }

    public boolean isValidarPermissaoAlterarCancelamento() {
        try {
            if (getUsuarioLogado().getUsuarioPerfilAcessoVOs().isEmpty()) {
                if (getUsuarioLogado().getAdministrador()) {
                    setMensagemDetalhada("", "");
                    setSucesso(true);
                    return true;
                }
                throw new Exception("O usuário informado não tem nenhum perfil acesso informado.");
            }
            Iterator i = getUsuarioLogado().getUsuarioPerfilAcessoVOs().iterator();
            while (i.hasNext()) {
                UsuarioPerfilAcessoVO usuarioPerfilAcesso = (UsuarioPerfilAcessoVO) i.next();
                if (getEmpresaLogado().getCodigo().equals(usuarioPerfilAcesso.getEmpresa().getCodigo().intValue())) {
                    usuarioPerfilAcesso.getPerfilAcesso().setPermissaoVOs(getFacade().getPermissao().consultarPermissaos(
                            usuarioPerfilAcesso.getPerfilAcesso().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
                    getFacade().getControleAcesso().verificarPermissaoUsuarioFuncionalidade(
                            usuarioPerfilAcesso.getPerfilAcesso(), getUsuarioLogado(), "AlterarTipoCancelamento",
                            "3.28 - Alterar Tipo do Cancelamento");
                }
            }
            return true;
        } catch (Exception e) {
            return false;
        }
    }

    public boolean isPlanoVencido() {
        return planoVencido;
    }

    public void setPlanoVencido(boolean planoVencido) {
        this.planoVencido = planoVencido;
    }

    public List<ReciboPagamentoTO> getRecibos() {
        return recibos;
    }

    public void setRecibos(List<ReciboPagamentoTO> recibos) {
        this.recibos = recibos;
    }

    public void setReciboDevolucao(ReciboDevolucaoVO reciboDevolucao) {
        this.reciboDevolucao = reciboDevolucao;
    }

    public ReciboDevolucaoVO getReciboDevolucao() {
        return reciboDevolucao;
    }

    public boolean getMostrarBotaoRecibo() throws Exception {
        return UtilReflection.objetoMaiorQueZero(reciboDevolucao, "getCodigo()");
    }

    public void setOnCompleteQuitacao(String onCompleteQuitacao) {
        this.onCompleteQuitacao = onCompleteQuitacao;
    }

    public String getOnCompleteQuitacao() {
        return onCompleteQuitacao;
    }

    public void setOnCompleteDevolucao(String onCompleteDevolucao) {
        this.onCompleteDevolucao = onCompleteDevolucao;
    }

    public String getOnCompleteDevolucao() {
        return onCompleteDevolucao;
    }

    public void setAlterarValor(boolean alterarValor) {
        this.alterarValor = alterarValor;
    }

    public boolean isFinalizouCancelamento() {
        return finalizouCancelamento;
    }

    public void setFinalizouCancelamento(boolean finalizouCancelamento) {
        this.finalizouCancelamento = finalizouCancelamento;
    }

    public boolean getAlterarValor() {
        return alterarValor;
    }

    public void setValorTotalAlteracaoValor(Double valorTotalAlteracaoValor) {
        this.valorTotalAlteracaoValor = valorTotalAlteracaoValor;
    }

    public Double getValorTotalAlteracaoValor() {
        return valorTotalAlteracaoValor;
    }

    public String getOnComplete() {//chamada implícita por 'AutorizacaoFuncionalidadeControle.control.onComplete'
        return getMsgAlert();
    }

    public void setLiberarMultaCustos(Boolean liberarMultaCustos) {
        this.liberarMultaCustos = liberarMultaCustos;
    }

    public Boolean getLiberarMultaCustos() {
        return liberarMultaCustos;
    }

    public Boolean getRetirarAutomatico() {
        return retirarAutomatico;
    }

    public void setRetirarAutomatico(Boolean retirarAutomatico) {
        this.retirarAutomatico = retirarAutomatico;
    }

    public String getValidarDataRetroativa() {
        validarDataRetroativa();
        return validarDataRetroativa;
    }

    public void setValidarDataRetroativa(String validarDataRetroativa) {
        this.validarDataRetroativa = validarDataRetroativa;
    }

    public void setAlterarCancelamento(boolean alterarCancelamento) {
        this.alterarCancelamento = alterarCancelamento;
    }

    public boolean isAlterarCancelamento() {
        return alterarCancelamento;
    }

    public boolean isPermiteAlterarCancelamento() {
        return permiteAlterarCancelamento;
    }

    public void setPermiteAlterarCancelamento(boolean permiteAlterarCancelamento) {
        this.permiteAlterarCancelamento = permiteAlterarCancelamento;
    }

    public boolean isSimular() {
        return simular;
    }

    public void setSimular(boolean simular) {
        this.simular = simular;
    }

    public boolean isUsuarioDesejaAlterarMulta() {
        return usuarioDesejaAlterarMulta;
    }

    public void setUsuarioDesejaAlterarMulta(boolean usuarioDesejaAlterarMulta) {
        this.usuarioDesejaAlterarMulta = usuarioDesejaAlterarMulta;
    }

    public Double getValorAlteracaoPercentualMulta() {
        return valorAlteracaoPercentualMulta;
    }

    public void setValorAlteracaoPercentualMulta(Double valorAlteracaoPercentualMulta) {
        this.valorAlteracaoPercentualMulta = valorAlteracaoPercentualMulta;
    }

    public List<MovParcelaVO> getContratoComParcelasEnvioDeRemessa(int contrato) throws Exception {
        return getFacade().getMovParcela().consultarPorContratoParcelasEmRemessa(String.valueOf(contrato));
    }

    public Date getDataFinalArmario() {
        return dataFinalArmario;
    }

    public String getDataFinalArmarioApresentar() {
        return Uteis.getData(dataFinalArmario);
    }

    public void setDataFinalArmario(Date dataFinalArmario) {
        this.dataFinalArmario = dataFinalArmario;
    }

    public String getLabelValorBaseMensal() throws Exception {
        return (this.contratoVO.isVendaCreditoTreino()) ? "Valor unitário crédito: " + getEmpresaLogado().getMoeda() : "Valor Mensal " + getEmpresaLogado().getMoeda();
    }

    public double getValorCreditoTreinoRestante() {
        double totalCredito = this.contratoVO.getContratoDuracao().getContratoDuracaoCreditoTreinoVO().getTotalCompra() + this.contratoDuracaoCreditoTreinoCancelar.getTotalTransferenciaSaldo();
        if (contratoVO.getPlano() != null && contratoVO.getPlano().isCreditoTreinoNaoCumulativo()){
            return totalCredito - contratoDuracaoCreditoTreinoCancelar.getValorUtilizadoTotal();
        }
        return totalCredito - contratoDuracaoCreditoTreinoCancelar.getValorUtilizadoMensal();
    }

    public double getValorBaseMensal() {
        return (this.contratoVO.isVendaCreditoTreino()) ? this.contratoDuracaoCreditoTreinoCancelar.getValorUnitario() : this.cancelamentoContratoVO.getValorMensalContrato();
    }

    public ContratoDuracaoCreditoTreinoVO getContratoDuracaoCreditoTreinoCancelar() {
        return contratoDuracaoCreditoTreinoCancelar;
    }

    public void setContratoDuracaoCreditoTreinoCancelar(ContratoDuracaoCreditoTreinoVO contratoDuracaoCreditoTreinoCancelar) {
        this.contratoDuracaoCreditoTreinoCancelar = contratoDuracaoCreditoTreinoCancelar;
    }

    public String getNomeArquivoComprovanteOperacao() {
        if (nomeArquivoComprovanteOperacao == null) {
            nomeArquivoComprovanteOperacao = "";
        }
        return nomeArquivoComprovanteOperacao;
    }

    public void setNomeArquivoComprovanteOperacao(String nomeArquivoComprovanteOperacao) {
        this.nomeArquivoComprovanteOperacao = nomeArquivoComprovanteOperacao;
    }

    public void imprimirComprovanteOperacao() {
        try {
            if (getCancelamentoContratoVO().getContratoOperacaoVO().getCodigo() != 0) {
                setNomeArquivoComprovanteOperacao(new SuperControleRelatorio().imprimirComprovanteOperacao(getCancelamentoContratoVO().getContratoOperacaoVO(), getEmpresaLogado()));
            } else {
                throw new Exception("Não foi possível imprimir o comprovante da operação.");
            }
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public void imprimirComprovanteOperacaoSaida() {
        try {
            if (getCancelamentoContratoVO().getComprovanteTransDiasVO().getCodigo() != 0) {
                setNomeArquivoComprovanteOperacao(new SuperControleRelatorio().imprimirComprovanteOperacao(getCancelamentoContratoVO().getComprovanteTransDiasVO(), getEmpresaLogado()));
            } else {
                throw new Exception("Não foi possível imprimir o comprovante da operação.");
            }
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public boolean isLiberarMultaCancelamentoAntecipado() {
        return liberarMultaCancelamentoAntecipado;
    }

    public void setLiberarMultaCancelamentoAntecipado(boolean liberarMultaCancelamentoAntecipado) {
        this.liberarMultaCancelamentoAntecipado = liberarMultaCancelamentoAntecipado;
    }

    public String obterLiberacaoMultaCancelamentoAntecipado() throws Exception {
        AutorizacaoFuncionalidadeControle auto = (AutorizacaoFuncionalidadeControle) getControlador(AutorizacaoFuncionalidadeControle.class);
        auto.setPedirPermissao(false);
        AutorizacaoFuncionalidadeListener listener = new AutorizacaoFuncionalidadeListener() {

            @Override
            public void onAutorizacaoComSucesso() throws Exception {
                cancelamentoContratoVO.setResponsavelCancelamento(getUsuarioLogado());
                cancelamentoContratoVO.getResponsavelCancelamento().setUserOamd(getUsuarioLogado().getUserOamd());
                if (liberarMultaCancelamentoAntecipado) {
                    getFacade().getZWFacade().calcularCancelamentoAntecipado(cancelamentoContratoVO, getContratoVO(), isLiberarProximaParcelaCancelamentoAntecipado(), isLiberarMultaCancelamentoAntecipado());
                    cancelamentoContratoVO.setValorMultaRestanteCancelamentoAntecipado(0.0);
                } else {
                    getFacade().getZWFacade().calcularCancelamentoAntecipado(cancelamentoContratoVO, getContratoVO(), isLiberarProximaParcelaCancelamentoAntecipado(), isLiberarMultaCancelamentoAntecipado());
                }
            }

            @Override
            public void onAutorizacaoComErro(Exception e) {
                setMensagemDetalhada("msg_erro", e.getMessage());
            }

            @Override
            public String getExecutarAoCompletar() {
                return getOnComplete();
            }
        };
        auto.autorizar("Liberar multa e custos de Cancelamento de Contrato - Autorizar", "LiberarMultaCustosCancelamento_Autorizar",
                "Você precisa da permissão \"Liberar multa e custos de Cancelamento de Contrato - Autorizar\"",
                "formCancelamentoDevolucaoAuto", listener);
        return "";
    }

    public boolean isLiberarProximaParcelaCancelamentoAntecipado() {
        return liberarProximaParcelaCancelamentoAntecipado;
    }

    public void setLiberarProximaParcelaCancelamentoAntecipado(boolean liberarProximaParcelaCancelamentoAntecipado) {
        this.liberarProximaParcelaCancelamentoAntecipado = liberarProximaParcelaCancelamentoAntecipado;
    }

    public String obterLiberacaoIsentarProximaParcela() throws Exception {
        AutorizacaoFuncionalidadeControle auto = (AutorizacaoFuncionalidadeControle) getControlador(AutorizacaoFuncionalidadeControle.class);
        auto.setPedirPermissao(false);
        AutorizacaoFuncionalidadeListener listener = new AutorizacaoFuncionalidadeListener() {

            @Override
            public void onAutorizacaoComSucesso() throws Exception {
                cancelamentoContratoVO.setResponsavelCancelamento(getUsuarioLogado());
                cancelamentoContratoVO.getResponsavelCancelamento().setUserOamd(getUsuarioLogado().getUserOamd());
                if (liberarProximaParcelaCancelamentoAntecipado) {
                    getFacade().getZWFacade().calcularCancelamentoAntecipado(cancelamentoContratoVO, getContratoVO(), isLiberarProximaParcelaCancelamentoAntecipado(), isLiberarMultaCancelamentoAntecipado());
                    cancelamentoContratoVO.setPagarProximaParcelaCancelamentoAntecipado(false);
                    cancelamentoContratoVO.setValorMultaProximaParcelaCancelamentoAntecipado(0.0);
                } else {
                    getFacade().getZWFacade().calcularCancelamentoAntecipado(cancelamentoContratoVO, getContratoVO(), isLiberarProximaParcelaCancelamentoAntecipado(), isLiberarMultaCancelamentoAntecipado());
                }
            }

            @Override
            public void onAutorizacaoComErro(Exception e) {
                setMensagemDetalhada("msg_erro", e.getMessage());
            }

            @Override
            public String getExecutarAoCompletar() {
                return getOnComplete();
            }
        };
        auto.autorizar("Liberar multa e custos de Cancelamento de Contrato - Autorizar", "LiberarMultaCustosCancelamento_Autorizar",
                "Você precisa da permissão \"Liberar multa e custos de Cancelamento de Contrato - Autorizar\"",
                "formCancelamentoDevolucaoAuto", listener);
        return "";
    }

    public void enviarEmailCancelamentoContrato() throws Exception {
        enviarEmailCancelamentoContrato(false);
    }

    public void enviarEmailCancelamentoContrato(boolean servlet) throws Exception {
        try {
            setModalMensagemGenerica("");
            setMensagemEmailCancelamentoAntecipado("");

            ConfiguracaoSistemaCRMVO configuracaoSistemaCRMEnvio = getConfiguracaoSMTPNoReply();
            if (UteisValidacao.emptyString(configuracaoSistemaCRMEnvio.getMailServer())) {
                throw new Exception("Não foi possível enviar o e-mail. Verifique as configurações de e-mail no CRM!");
            }

            List<ContratoOperacaoVO> operacaoVOList = getFacade().getContratoOperacao().consultarPorTipoOperacaoCodigoContrato("CA", getContratoVO().getCodigo(), false, Uteis.NIVELMONTARDADOS_TODOS);
            ContratoOperacaoVO contratoOperacaoVO = operacaoVOList.get(0);
            contratoOperacaoVO.setNome(getContratoVO().getNome_Apresentar());

            String assuntoEmail = "Cancelamento Contrato " + contratoOperacaoVO.getContrato() + " - " + getContratoVO().getEmpresa().getNome();

            UteisEmail uteisEmail = new UteisEmail();
            uteisEmail.novo(assuntoEmail, configuracaoSistemaCRMEnvio);

            //REMETENTE
            ConfiguracaoSistemaCRMVO configuracaoSistemaCRM = getFacade().getConfiguracaoSistemaCRM().consultarConfiguracaoSistemaCRM(Uteis.NIVELMONTARDADOS_TODOS);
            uteisEmail.setRemetente(configuracaoSistemaCRM.getRemetentePadraoMailing());
            String mensagemEmail = getFacade().getCliente().montarEmailCancelamento(contratoOperacaoVO, getContratoVO().getEmpresa(), "");

            List emailVOList = getFacade().getEmail().consultarEmails(getContratoVO().getPessoa().getCodigo(), false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            if (!UteisValidacao.emptyList(emailVOList)) {
                List<String> emailsValidos = new ArrayList<>();
                for (Object obj : emailVOList) {
                    EmailVO emailVO = (EmailVO) obj;
                    if(UteisValidacao.validaEmail(emailVO.getEmail())) {
                        emailsValidos.add(emailVO.getEmail());
                    }
                }

                if(!emailsValidos.isEmpty()) {
                    String[] emails = emailsValidos.toArray(new String[0]);
                    uteisEmail.enviarEmailN(emails, mensagemEmail, assuntoEmail, getContratoVO().getEmpresa().getNome());
                }
            } else {
                throw new Exception("Cliente não possui e-mail cadastrado.");
            }
            setMensagemEmailCancelamentoAntecipado("E-mail enviado com sucesso.");
            if (!servlet) {
                montarMsgGenerica("Envio de E-mail", "E-mail enviado com sucesso.", true, "", "", "");
            }
        } catch (Exception e) {
            setMensagemEmailCancelamentoAntecipado(e.getMessage());
            montarMsgGenerica("Envio de E-mail", e.getMessage(), true, "", "", "");
        }
    }

    public boolean isDevolverTransferenciaSaldoCredito() {
        return this.contratoVO.isVendaCreditoTreino() && this.contratoDuracaoCreditoTreinoCancelar.getTotalTransferenciaSaldo() > 0;
    }

    public String getModalMensagemGenerica() {
        if (modalMensagemGenerica == null) {
            modalMensagemGenerica = "";
        }
        return modalMensagemGenerica;
    }

    public void setModalMensagemGenerica(String modalMensagemGenerica) {
        this.modalMensagemGenerica = modalMensagemGenerica;
    }

    public void montarMsgGenerica(String titulo, String msg, boolean msgInformacao, String botaoSim, String botaoNao, String reRender) {
        MensagemGenericaControle control = (MensagemGenericaControle) getControlador(MensagemGenericaControle.class);
        control.setMensagemDetalhada("", "");
        control.setMensagemApresentar("");
        setModalMensagemGenerica("Richfaces.showModalPanel('mdlMensagemGenerica')");

        if (msgInformacao) {
            control.init(titulo, msg, this, "Fechar", "", reRender + ",mdlMensagemGenerica");
        } else {
            control.init(titulo, msg, this, botaoSim, "", botaoNao, "", reRender + ",mdlMensagemGenerica");
        }
    }

    private void limparOpcoesCancelamento() throws Exception {
        setAlterarCancelamento(false);
        alterarTipoCancelamento();
    }

    public String getMensagemEmailCancelamentoAntecipado() {
        if (mensagemEmailCancelamentoAntecipado == null) {
            mensagemEmailCancelamentoAntecipado = "";
        }
        return mensagemEmailCancelamentoAntecipado;
    }

    public void setMensagemEmailCancelamentoAntecipado(String mensagemEmailCancelamentoAntecipado) {
        this.mensagemEmailCancelamentoAntecipado = mensagemEmailCancelamentoAntecipado;
    }

    public boolean getMensagemEmailCancelamentoAntecipadoSucesso() {
        return getMensagemEmailCancelamentoAntecipado().toUpperCase().equals("E-mail enviado com sucesso.".toUpperCase());
    }

    public void selecionarCartaoEstornoParcial() {
        try {
            setMsgAlert("");
            CartaoCreditoVO obj = (CartaoCreditoVO) context().getExternalContext().getRequestMap().get("cartao");
            if (obj == null) {
                throw new Exception("Erro ao posicionar o cartao. Contate Suporte técnico.");
            }
            MovPagamentoVO objPag = (MovPagamentoVO) context().getExternalContext().getRequestMap().get("movPagamento");
            setMovPagamentoVO(objPag);
            obj.setValorParcialDevolverCalculo(obj.getValorParcialDevolver());
            setCartaoDevolucao(obj);
            setMsgAlert("Richfaces.showModalPanel('mdlEstornoParcial')");
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public CartaoCreditoVO getCartaoDevolucao() {
        return cartaoDevolucao;
    }

    public void setCartaoDevolucao(CartaoCreditoVO cartaoDevolucao) {
        this.cartaoDevolucao = cartaoDevolucao;
    }

    public MovPagamentoVO getMovPagamentoVO() {
        return movPagamentoVO;
    }

    public void setMovPagamentoVO(MovPagamentoVO movPagamentoVO) {
        this.movPagamentoVO = movPagamentoVO;
    }

    public boolean isApresentarDadosEstornoParcial() {
        return apresentarDadosEstornoParcial;
    }

    public void setApresentarDadosEstornoParcial(boolean apresentarDadosEstornoParcial) {
        this.apresentarDadosEstornoParcial = apresentarDadosEstornoParcial;
    }

    public void apresentarOpcoesPropagacao() {
        try {
            setMsgAlert("");
            if (cartaoDevolucao.getValorParcialDevolverCalculo() == 0.0 && !cartaoDevolucao.isDevolverParcial()) { //se não houve alteração, modal já é fechado
                setMsgAlert("Richfaces.hideModalPanel('mdlEstornoParcial')");
                return;
            }
            if (cartaoDevolucao.getNrParcela() == movPagamentoVO.getNrParcelaCartaoCredito()) {
                aplicarEstornoParcial(false);
            }
            setApresentarDadosEstornoParcial(false);
        } catch (Exception e) {
            setMensagemDetalhada("");
        }
    }

    public void apresentarOpcoesPropagacaoCancelando() {
        limparMsg();
        setMsgAlert("");
        cartaoDevolucao.setValorParcialDevolverCalculo(0.0);
        setApresentarDadosEstornoParcial(false);
    }

    public void atualizarValorParcial() {
        limparMsg();
        if (cartaoDevolucao.getValor() <= cartaoDevolucao.getValorParcialDevolverCalculo()) {
            setMensagemDetalhada("Valor informado para estorno excede valor da parcela! Informe um valor menor que" + Formatador.formatarValorMonetarioSemMoeda(cartaoDevolucao.getValor()));
            cartaoDevolucao.setValorParcialDevolverCalculo(0.0);
        }
    }

    public void aplicarEstornoParcialParcelaSelecionada() {
        limparMsg();
        try {
            aplicarEstornoParcial(false);
        } catch (Exception e) {
            setMensagemDetalhada("");
        }
    }

    public void aplicarEstornoParcialDemaisParcelas() {
        limparMsg();
        try {
            aplicarEstornoParcial(true);
        } catch (Exception e) {
            setMensagemDetalhada("");
        }
    }

    public void aplicarEstornoParcial(boolean propagar) throws Exception {
        setMsgAlert("");
        double aux = cancelamentoContratoVO.getSomaValorPagoPeloClienteComCheque();
        aux += marcaDevolucaoParcial(propagar);
        cancelamentoContratoVO.setSomaValorPagoPeloClienteComCheque(Uteis.arredondarForcando2CasasDecimais(aux));
        cancelamentoContratoVO.setValorASerDevolvidoBaseCalculo(cancelamentoContratoVO.getSomaValorPagoPeloClienteComCheque() - cancelamentoContratoVO.getValorASerPagoPeloCliente());
        limparOpcoesCancelamento();
        verificarValorOperacoesCancelamento();
        setApresentarDadosEstornoParcial(true);
        setMsgAlert("Richfaces.hideModalPanel('mdlEstornoParcial')");
    }

    private double marcaDevolucaoParcial(boolean propagar) {
        double aux = 0.0;
        Iterator i = movPagamentoVO.getCartaoCreditoVOs().iterator();
        aux += inicializarDadosDevolucaoParcial(propagar);
        if (cartaoDevolucao.getValorParcialDevolverCalculo() == 0.0) {
            return aux;
        }
        boolean marcaTodosAbaixo = false;
        while (i.hasNext()) {
            CartaoCreditoVO cartao = (CartaoCreditoVO) i.next();
            if (!cartao.getApresentarCartao()) {
                continue;
            }
            if (cartao.getCodigo().intValue() == cartaoDevolucao.getCodigo() || (propagar && marcaTodosAbaixo)) {
                marcaTodosAbaixo = true;
                double valorRetirar = cartaoDevolucao.getValorParcialDevolverCalculo();
                if (getMovPagamentoVO().getValorPagaContrato() < Uteis.arredondarForcando2CasasDecimais(movPagamentoVO.getValorEstornado() + cartaoDevolucao.getValorParcialDevolverCalculo())) {
                    double valorRestante = Uteis.arredondarForcando2CasasDecimaisMantendoSinal(getMovPagamentoVO().getValorPagaContrato() - movPagamentoVO.getValorEstornado());
                    if (valorRestante <= 0.0) {
                        break;
                    }
                    valorRetirar = valorRestante;
                }
                cartao.setDevolverParcial(true);
                cartao.setCartaoCompensar(true);
                cartao.setCartaoEscolhido(false);
                aux -= valorRetirar;
                cartao.setValorParcialDevolver(valorRetirar);
                getMovPagamentoVO().setValorEstornado(Uteis.arredondarForcando2CasasDecimais(getMovPagamentoVO().getValorEstornado() + valorRetirar));
            }
        }
        cartaoDevolucao.setValorParcialDevolverCalculo(0.0);
        return aux;
    }

    private double inicializarDadosDevolucaoParcial(boolean propagar) {
        double aux = 0.0;
        Iterator i = movPagamentoVO.getCartaoCreditoVOs().iterator();
        boolean alterarTodosAbaixo = false;
        while (i.hasNext()) {

            CartaoCreditoVO cartao = (CartaoCreditoVO) i.next();
            if (!cartao.getApresentarCartao()) {
                continue;
            }
            if (cartao.getCodigo().intValue() == cartaoDevolucao.getCodigo() || (propagar && alterarTodosAbaixo)) {
                alterarTodosAbaixo = true;
                if (cartao.isDevolverParcial()) {
                    movPagamentoVO.setValorEstornado(Uteis.arredondarForcando2CasasDecimaisMantendoSinal(movPagamentoVO.getValorEstornado() - cartao.getValorParcialDevolver()));
                    aux += cartao.getValorParcialDevolver();
                } else if (cartao.isCartaoEscolhido()) {
                    movPagamentoVO.setValorEstornado(Uteis.arredondarForcando2CasasDecimaisMantendoSinal(movPagamentoVO.getValorEstornado() - cartao.getValor()));
                    aux += cartao.getValor();
                }
                cartao.setValorParcialDevolver(0.0);
                cartao.setCartaoEscolhido(false);
                cartao.setCartaoCompensar(true);
                cartao.setDevolverParcial(false);
                if (!propagar) {
                    return aux;
                }
            }
        }
        return aux;
    }

    public void cancelarAlteracaoParcial() {
        limparMsg();
        try {
            cartaoDevolucao.setValorParcialDevolverCalculo(0.0);
            setApresentarDadosEstornoParcial(true);
            setMsgAlert("Richfaces.hideModalPanel('mdlEstornoParcial')");
        } catch (Exception e) {
            setMensagemDetalhada("");
        }
    }

    private double inicializarDadosDevolucao() {
        double aux = 0.0;
        Iterator i = movPagamentoVO.getCartaoCreditoVOs().iterator();
        boolean alterarTodosAbaixo = false;
        while (i.hasNext()) {

            CartaoCreditoVO cartao = (CartaoCreditoVO) i.next();
            if (!cartao.getApresentarCartao() || cartao.isDevolverParcial()) {
                continue;
            }
            if (cartao.getCodigo().intValue() == cartaoDevolucao.getCodigo() || alterarTodosAbaixo) {
                alterarTodosAbaixo = true;
                if (cartao.isCartaoEscolhido()) {
                    movPagamentoVO.setValorEstornado(Uteis.arredondarForcando2CasasDecimaisMantendoSinal(movPagamentoVO.getValorEstornado() - cartao.getValor()));
                    aux += Uteis.arredondarForcando2CasasDecimais(cartao.getValor());
                }
                cartao.setValorParcialDevolver(0.0);
                cartao.setCartaoEscolhido(false);
                cartao.setCartaoCompensar(true);
                cartao.setDevolverParcial(false);
            }
        }
        return aux;
    }

    private void finalizarCancelamentoProporcional() throws Exception {
        limparMsg();
        setFinalizouCancelamento(false);

        cancelamentoContratoVO.getResponsavelCancelamento().setUserOamd(getUsuarioLogado().getUserOamd());
        cancelamentoContratoVO.setContratoCancelar(getContratoVO());

        validarPermissao("CancelamentoContrato_Autorizar", "3.07 - Cancelamento Contrato - Autorizar", cancelamentoContratoVO.getResponsavelCancelamento());

        CancelamentoContratoVO cancelamentoOriginal = (CancelamentoContratoVO) getCancelamentoContratoVO().getClone(false);
        if (getFacade().getContrato().consultarContratoCancelado(getContratoVO().getCodigo())) {
            throw new Exception("Contrato já cancelado");
        }
        if (getEmpresaLogado().getCodigo() == 0) {
            throw new Exception("Não foi possível efetuar o cancelamento, pois o SISTEMA NÃO ESTÁ LOGADO EM NENHUMA EMPRESA.");
        }

        getFacade().getZWFacade().incluirOperacaoCancelamentoContratoDevolucao(cancelamentoContratoVO,
                getContratoVO(), cancelamentoContratoVO.getResponsavelCancelamento(),
                null, new ArrayList<ContaVO>(), null, true);

        cancelamentoNegociacaoBitrix();
        //LOG - INICIO
        try {
            cancelamentoContratoVO.setObjetoVOAntesAlteracao(new CancelamentoContratoVO());
            cancelamentoContratoVO.setNovoObj(true);
            registrarLogObjetoVO(cancelamentoContratoVO, cancelamentoContratoVO.getCodigo(), "CANCELAMENTOCONTRATO", getContratoVO().getPessoa().getCodigo());
        } catch (Exception e) {
            registrarLogErroObjetoVO("CANCELAMENTOCONTRATO", getContratoVO().getPessoa().getCodigo(), "ERRO AO GERAR LOG DE INCLUSÃO DE CANCELAMENTO CONTRATO", this.getUsuarioLogado().getNome(), this.getUsuarioLogado().getUserOamd());
            e.printStackTrace();
        }
        //LOG - FIM
        ContratoOperacaoVO comprovanteOperacao = getCancelamentoContratoVO().getContratoOperacaoVO();
        ContratoOperacaoVO comprovanteOperacaoTransSaida = getCancelamentoContratoVO().getComprovanteTransDiasVO();
        setCancelamentoContratoVO(cancelamentoOriginal);
        getCancelamentoContratoVO().setDevolucaoManualCancelamento(false);
        getCancelamentoContratoVO().setContratoOperacaoVO(comprovanteOperacao);
        getCancelamentoContratoVO().setComprovanteTransDiasVO(comprovanteOperacaoTransSaida);
        setApresentarBotoes(false);
        setErro(false);
        setSucesso(true);
        montarSucessoGrowl("Cancelamento efetuado com sucesso.");
        setFinalizouCancelamento(true);
    }

    public void validarAutorizacaoCancelamentoProporcional() {
        try {
            limparMsg();
            setMsgAlert("");
            AutorizacaoFuncionalidadeControle auto = getControlador(AutorizacaoFuncionalidadeControle.class);
            auto.setPedirPermissao(false);
            AutorizacaoFuncionalidadeListener listener = new AutorizacaoFuncionalidadeListener() {

                @Override
                public void onAutorizacaoComSucesso() {
                    try {
                        if(getProcessandoOperacao()) {
                            while (getProcessandoOperacao()) {
                                Thread.sleep(500);
                            }
                            setMsgAlert(getMensagemNotificar() + ";try { fireElementFromParent('form:btnAtualizaCliente'); } catch(e) {}; executePostMessage({reloadContractPage: true});");
                        } else {
                            setProcessandoOperacao(true);
                            cancelamentoContratoVO.setResponsavelCancelamento(getUsuarioLogado());
                            cancelamentoContratoVO.getResponsavelCancelamento().setUserOamd(getUsuarioLogado().getUserOamd());
                            finalizarCancelamentoProporcional();
                            setMsgAlert(getMensagemNotificar() + ";try { fireElementFromParent('form:btnAtualizaCliente'); } catch(e) {}; executePostMessage({reloadContractPage: true});");
                            ServicoNotificacaoPush.enviaNotificacaoCancelamentoContrato(getKey(), contratoVO.getEmpresa().getCodigo(), false, cancelamentoContratoVO.getUsuarioVO() != null ? cancelamentoContratoVO.getUsuarioVO().getCodigo() : 0, contratoVO.getEmpresa().getNome());
                        }
                    } catch (Exception ex) {
                        montarErro(ex.getMessage());
                        onAutorizacaoComErro(ex);
                    } finally {
                        setProcessandoOperacao(false);
                    }

                }

                @Override
                public void onAutorizacaoComErro(Exception e) {
                    setMsgAlert(getMensagemNotificar());
                    setMensagemID("");
                    setMensagemDetalhada(e);
                }

                @Override
                public String getExecutarAoCompletar() {
                    return getOnComplete();
                }
            };
            cancelamentoContratoVO.validarDados(getCancelamentoContratoVO(), "telaCancelamentoBolsa", getContratoVO(), false);
            auto.autorizar("Confirmação de Cancelamento de Contrato", "CancelamentoContrato_Autorizar",
                    "3.07 - Cancelamento Contrato - Autorizar", "form,panelBotoes,panelMensagem", listener);
            limparMsg();
        } catch (Exception e) {
            montarErro(e.getMessage());
        }
    }

    private void finalizarCancelamentoAvaliandoParcelas() throws Exception {
        limparMsg();
        setFinalizouCancelamento(false);

        cancelamentoContratoVO.getResponsavelCancelamento().setUserOamd(getUsuarioLogado().getUserOamd());
        cancelamentoContratoVO.setContratoCancelar(getContratoVO());

        validarPermissao("CancelamentoContrato_Autorizar", "3.07 - Cancelamento Contrato - Autorizar", cancelamentoContratoVO.getResponsavelCancelamento());

        CancelamentoContratoVO cancelamentoOriginal = (CancelamentoContratoVO) getCancelamentoContratoVO().getClone(false);
        if (getFacade().getContrato().consultarContratoCancelado(getContratoVO().getCodigo())) {
            throw new Exception("Contrato já cancelado");
        }
        if (getEmpresaLogado().getCodigo() == 0) {
            throw new Exception("Não foi possível efetuar o cancelamento, pois o SISTEMA NÃO ESTÁ LOGADO EM NENHUMA EMPRESA.");
        }

        getFacade().getZWFacade().incluirOperacaoCancelamentoContratoDevolucao(cancelamentoContratoVO,
                getContratoVO(), cancelamentoContratoVO.getResponsavelCancelamento(),
                null, new ArrayList<ContaVO>(), null, true);

        //LOG - INICIO
        try {
            cancelamentoContratoVO.setObjetoVOAntesAlteracao(new CancelamentoContratoVO());
            cancelamentoContratoVO.setNovoObj(true);
            registrarLogObjetoVO(cancelamentoContratoVO, cancelamentoContratoVO.getCodigo(), "CANCELAMENTOCONTRATO", getContratoVO().getPessoa().getCodigo());
        } catch (Exception e) {
            registrarLogErroObjetoVO("CANCELAMENTOCONTRATO", getContratoVO().getPessoa().getCodigo(), "ERRO AO GERAR LOG DE INCLUSÃO DE CANCELAMENTO CONTRATO", this.getUsuarioLogado().getNome(), this.getUsuarioLogado().getUserOamd());
            e.printStackTrace();
        }
        //LOG - FIM
        ContratoOperacaoVO comprovanteOperacao = getCancelamentoContratoVO().getContratoOperacaoVO();
        ContratoOperacaoVO comprovanteOperacaoTransSaida = getCancelamentoContratoVO().getComprovanteTransDiasVO();
        setCancelamentoContratoVO(cancelamentoOriginal);
        getCancelamentoContratoVO().setDevolucaoManualCancelamento(false);
        getCancelamentoContratoVO().setContratoOperacaoVO(comprovanteOperacao);
        getCancelamentoContratoVO().setComprovanteTransDiasVO(comprovanteOperacaoTransSaida);
        setApresentarBotoes(false);
        setErro(false);
        setSucesso(true);
        montarSucessoGrowl("Cancelamento efetuado com sucesso.");
        setFinalizouCancelamento(true);
    }

    public void validarAutorizacaoCancelamentoAvaliandoParcelas() {
        limparMsg();
        setMsgAlert("");
        try {
            if (justificativaOperacaoVO.getNecessarioAnexarComprovante()) {
                if (getArquivoAnexo() == null) {
                    throw new ConsistirException("Não é possível prosseguir o cancelamento sem anexar um arquivo.");
                }
            }

            AutorizacaoFuncionalidadeControle auto = getControlador(AutorizacaoFuncionalidadeControle.class);
            auto.setPedirPermissao(false);
            AutorizacaoFuncionalidadeListener listener = new AutorizacaoFuncionalidadeListener() {

                @Override
                public void onAutorizacaoComSucesso() {
                    try {
                        setProcessandoOperacao(true);
                        cancelamentoContratoVO.setResponsavelCancelamento(getUsuarioLogado());
                        cancelamentoContratoVO.getResponsavelCancelamento().setUserOamd(getUsuarioLogado().getUserOamd());
                        finalizarCancelamentoAvaliandoParcelas();
                        setMsgAlert(getMensagemNotificar() + ";try { fireElementFromParent('form:btnAtualizaCliente'); } catch(e) {}; executePostMessage({close: true});");
                        ServicoNotificacaoPush.enviaNotificacaoCancelamentoContrato(getKey(), contratoVO.getEmpresa().getCodigo(), false, cancelamentoContratoVO.getUsuarioVO() != null ? cancelamentoContratoVO.getUsuarioVO().getCodigo() : 0, contratoVO.getEmpresa().getNome());
                    } catch (Exception ex) {
                        montarErro(ex.getMessage());
                        onAutorizacaoComErro(ex);
                    }
                    setProcessandoOperacao(false);
                }

                @Override
                public void onAutorizacaoComErro(Exception e) {
                    setMsgAlert(getMensagemNotificar());
                    setMensagemID("");
                    setMensagemDetalhada(e);
                }

                @Override
                public String getExecutarAoCompletar() {
                    return getOnComplete();
                }
            };
            cancelamentoContratoVO.validarDados(getCancelamentoContratoVO(), "telaCancelamentoBolsa", getContratoVO(), false);
            auto.autorizar("Confirmação de Cancelamento de Contrato", "CancelamentoContrato_Autorizar",
                    "3.07 - Cancelamento Contrato - Autorizar", "form,panelBotoes,panelMensagem", listener);
            limparMsg();
        } catch (Exception e) {
            montarErro(e.getMessage());
        }
    }

    public String obterLiberacaoMultaCancelamentoProporcional() throws Exception {
        limparMsg();
        setMsgAlert("");
        AutorizacaoFuncionalidadeControle auto = (AutorizacaoFuncionalidadeControle) getControlador(AutorizacaoFuncionalidadeControle.class);
        auto.setPedirPermissao(false);
        AutorizacaoFuncionalidadeListener listener = new AutorizacaoFuncionalidadeListener() {

            @Override
            public void onAutorizacaoComSucesso() throws Exception {
                getCancelamentoContratoVO().getCancelamentoProporcional().setNaoCobrarMulta(true);
            }

            @Override
            public void onFecharModalAutorizacao() {
                getCancelamentoContratoVO().getCancelamentoProporcional().setNaoCobrarMulta(false);
            }

            @Override
            public void onAutorizacaoComErro(Exception e) {
                getCancelamentoContratoVO().getCancelamentoProporcional().setNaoCobrarMulta(false);
                setMensagemDetalhada("msg_erro", e.getMessage());
            }

            @Override
            public String getExecutarAoCompletar() {
                return getOnComplete();
            }
        };
        auto.autorizar("Liberar multa e custos de Cancelamento de Contrato - Autorizar", "LiberarMultaCustosCancelamento_Autorizar",
                "Você precisa da permissão \"Liberar multa e custos de Cancelamento de Contrato - Autorizar\"",
                "form,panelBotoes,panelMensagem", listener);
        return "";
    }

    public String obterLiberacaoAlterarValorMultaCancelamentoProporcional() throws Exception {
        limparMsg();
        setMsgAlert("");
        AutorizacaoFuncionalidadeControle auto = (AutorizacaoFuncionalidadeControle) getControlador(AutorizacaoFuncionalidadeControle.class);
        auto.setPedirPermissao(false);
        AutorizacaoFuncionalidadeListener listener = new AutorizacaoFuncionalidadeListener() {

            @Override
            public void onAutorizacaoComSucesso() throws Exception {
                getCancelamentoContratoVO().getCancelamentoProporcional().setAlterarValorMulta(true);
            }

            @Override
            public void onFecharModalAutorizacao() {
                getCancelamentoContratoVO().getCancelamentoProporcional().setAlterarValorMulta(false);
            }

            @Override
            public void onAutorizacaoComErro(Exception e) {
                getCancelamentoContratoVO().getCancelamentoProporcional().setAlterarValorMulta(false);
                setMensagemDetalhada("msg_erro", e.getMessage());
            }

            @Override
            public String getExecutarAoCompletar() {
                return getOnComplete();
            }
        };
        auto.autorizar("Autorização Valor Manual Percentual Multa Cancelamento", "ValorManualPercentualMultaCancelamento",
                "Você precisa da permissão \"Valor Manual Percentual Multa Cancelamento\"",
                "form,formConfirmacaoCancelamento,panelMensagem,panelBotoes,panelAutorizacaoFuncionalidade", listener);
        return "";
    }

    public String aplicarValorAlteradoMultaCancelamentoPropocional() throws Exception {
        try {
            limparMsg();
            setMsgAlert("");
            if (getCancelamentoContratoVO().getValorCreditoRestanteContratoValorMensalSemTaxaEMulta() < getCancelamentoContratoVO().getCancelamentoProporcional().getValorMulta()) {
                throw new Exception("O valor da multa não pode ultrapassar o valor restante do contrato.");
            }
            if ((getContratoVO().getEmpresa().isNaoCobrarMultaDeContratoRenovado() && getContratoVO().isContratoRenovacao()) || (getContratoVO().getEmpresa().isNaoCobrarMultaDeTodasParcelasPagas() && getContratoVO().isTodasAsParcelasEstaoPagas())) {
                setLiberarMultaCustos(true);
                getCancelamentoContratoVO().setPercentualMultaCancelamento(Uteis.arredondarForcando2CasasDecimais(0.00D));
                //cancelamentoContratoVO.setValorMultaProximaParcelaCancelamentoAntecipado(Uteis.arredondarForcando2CasasDecimais(0.00D));
                getCancelamentoContratoVO().setValorMultaRestanteCancelamentoAntecipado(Uteis.arredondarForcando2CasasDecimais(0.00D));
                getCancelamentoContratoVO().getCancelamentoProporcional().setPorcentagemMulta(Uteis.arredondarForcando2CasasDecimais(0.00D));
                getCancelamentoContratoVO().getCancelamentoProporcional().setValorMulta(Uteis.arredondarForcando2CasasDecimais(0.00D));
            } else {
                getCancelamentoContratoVO().getCancelamentoProporcional().setPorcentagemMulta(Uteis.arredondarForcando2CasasDecimais((getCancelamentoContratoVO().getCancelamentoProporcional().getValorMulta() * 100) / getCancelamentoContratoVO().getValorCreditoRestanteContratoValorMensalSemTaxaEMulta()));
                getCancelamentoContratoVO().getCancelamentoProporcional().setUsuarioAlterouValorMulta(true);
            }
        } catch (Exception ex) {
            getCancelamentoContratoVO().getCancelamentoProporcional().setValorMulta(getCancelamentoContratoVO().getCancelamentoProporcional().getValorMultaOriginal());
            montarErro(ex);
        }
        return "";
    }

    public boolean isApresentarBotoesTransferenciaContratoDias() {
        return apresentarBotoesTransferenciaContratoDias;
    }

    public void setApresentarBotoesTransferenciaContratoDias(boolean apresentarBotoesTransferenciaContratoDias) {
        this.apresentarBotoesTransferenciaContratoDias = apresentarBotoesTransferenciaContratoDias;
    }

    public EmpresaVO getEmpresaVO() {
        return empresaVO;
    }

    public void setEmpresaVO(EmpresaVO empresaVO) {
        this.empresaVO = empresaVO;
    }

    public boolean isApresentarMensagemTipoParcelaCancelamento() {
        return apresentarMensagemTipoParcelaCancelamento;
    }

    public void setApresentarMensagemTipoParcelaCancelamento(boolean apresentarMensagemTipoParcelaCancelamento) {
        this.apresentarMensagemTipoParcelaCancelamento = apresentarMensagemTipoParcelaCancelamento;
    }

    public boolean isApresentarMensagemZerarValorCancelamento() {
        return apresentarMensagemZerarValorCancelamento;
    }

    public void setApresentarMensagemZerarValorCancelamento(boolean apresentarMensagemZerarValorCancelamento) {
        this.apresentarMensagemZerarValorCancelamento = apresentarMensagemZerarValorCancelamento;
    }

    public boolean isApresentarUploadArquivo() {
        return apresentarUploadArquivo;
    }

    public void setApresentarUploadArquivo(boolean apresentarUploadArquivo) {
        this.apresentarUploadArquivo = apresentarUploadArquivo;
    }

    public void validarNecessidadeAnexo() {
        try {
            JustificativaOperacaoVO justificativaOperacaoVO = getFacade().getJustificativaOperacao().consultarPorChavePrimaria(cancelamentoContratoVO.getTipoJustificativaOperacao(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            setApresentarUploadArquivo(justificativaOperacaoVO.getNecessarioAnexarComprovante());
            setJustificativaOperacaoVO(justificativaOperacaoVO);
        } catch (Exception ex) {
            montarErro(ex);
        }
    }

    public void validarNecessidadeAnexoAvaliandoParcelas() {
        try {
            JustificativaOperacaoVO justificativaOperacaoVO = getFacade().getJustificativaOperacao().consultarPorChavePrimaria(cancelamentoContratoVO.getTipoJustificativaOperacao(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            cancelamentoContratoVO.setJustificativaOperacao(justificativaOperacaoVO);

            setApresentarUploadArquivo(justificativaOperacaoVO.getNecessarioAnexarComprovante());
            setJustificativaOperacaoVO(justificativaOperacaoVO);

            getFacade().getZWFacade().calcularCancelamentoAvaliandoParcelas(getCancelamentoContratoVO(), getContratoVO(), Calendario.hoje(), false);
        } catch (Exception ex) {
            montarErro(ex);
        }
    }

    public File getArquivoAnexo() {
        return arquivoAnexo;
    }

    public void setArquivoAnexo(File arquivoAnexo) {
        this.arquivoAnexo = arquivoAnexo;
    }

    public String getOnCompleteArquivoAnexo() {
        return onCompleteArquivoAnexo;
    }

    public void setOnCompleteArquivoAnexo(String onCompleteArquivoAnexo) {
        this.onCompleteArquivoAnexo = onCompleteArquivoAnexo;
    }

    public String getExtensaoArquivoAnexo() {
        return extensaoArquivoAnexo;
    }

    public void setExtensaoArquivoAnexo(String extensaoArquivoAnexo) {
        this.extensaoArquivoAnexo = extensaoArquivoAnexo;
    }

    public JustificativaOperacaoVO getJustificativaOperacaoVO() {
        if (justificativaOperacaoVO == null) {
            justificativaOperacaoVO = new JustificativaOperacaoVO();
        }
        return justificativaOperacaoVO;
    }

    public void setJustificativaOperacaoVO(JustificativaOperacaoVO justificativaOperacaoVO) {
        this.justificativaOperacaoVO = justificativaOperacaoVO;
    }

    public boolean isDataFinalContratoEmEdicao() {
        return dataFinalContratoEmEdicao;
    }

    public void setDataFinalContratoEmEdicao(boolean dataFinalContratoEmEdicao) {
        this.dataFinalContratoEmEdicao = dataFinalContratoEmEdicao;
    }

    public Date getNovaDataFinalContrato() {
        if (novaDataFinalContrato == null) {
            novaDataFinalContrato = getCancelamentoContratoVO().getDataFinalAcessoCancelamentoAntecipado() != null
                    ? getCancelamentoContratoVO().getDataFinalAcessoCancelamentoAntecipado() : new Date();
        }
        return novaDataFinalContrato;
    }

    public void setNovaDataFinalContrato(Date novaDataFinalContrato) {
        this.novaDataFinalContrato = novaDataFinalContrato;
    }

    public void aplicarData() throws Exception {
        if (getNovaDataFinalContrato() == null) {
            setErro(true);
            setMensagemDetalhada(getMensagemInternalizacao("msg_datavendaavulsa"));
        } else if (Calendario.menor(getNovaDataFinalContrato(), getCancelamentoContratoVO().getDataCancelamento())) {
            setErro(true);
            setMensagemDetalhada(getMensagemInternalizacao("msg_validacaoDataMenorQueDataCancelamento"));
        } else if (Calendario.maior(getNovaDataFinalContrato(), getContratoVO().getVigenciaAteAjustada())) {
            setErro(true);
            setMensagemDetalhada(getMensagemInternalizacao("msg_validacaoDataMaiorQueDataFimContrato"));
        } else {
            setMensagemDetalhada("");
            setErro(false);
            setDataFinalContratoEmEdicao(false);

            try {
                String tagData = "<b id=\"dtAcessoLimite\">";
                String msg = getCancelamentoContratoVO().getMensagemCancelamentoAntecipado();
                String dataApresentar = Uteis.getData(getNovaDataFinalContrato());
                Integer indexStart = msg.indexOf(tagData) + tagData.length();
                Integer indexEnd = indexStart + dataApresentar.length();
                getCancelamentoContratoVO().setMensagemCancelamentoAntecipado(msg.substring(0, indexStart) + dataApresentar + msg.substring(indexEnd));

                String msgAjustada = getCancelamentoContratoVO().getMensagemCancelamentoAntecipado().toString();
                msgAjustada = msgAjustada.replaceAll("<br>", "\n");
                msgAjustada = msgAjustada.replaceAll(" text-decoration:line-through;", "");
                msgAjustada = msgAjustada.replaceAll("<li style=\"text-align: left;\"><b>", "* ");
                msgAjustada = msgAjustada.replaceAll("</b></li>", "\n");
                if (msgAjustada.contains("\"dtAcessoLimite\"")) {
                    msgAjustada = msgAjustada.replaceAll("<b id=\"dtAcessoLimite\">", "");
                    msgAjustada = msgAjustada.replaceAll("</b>", "");
                }

                getCancelamentoContratoVO().setMensagemGravarOperacaoCancelamentoAntecipado(msgAjustada);

            } catch (Exception ex) {}

            getCancelamentoContratoVO().setDataFinalAcessoCancelamentoAntecipado(getNovaDataFinalContrato());
        }
    }


    public void editarCampoData() throws Exception {
        setMensagemDetalhada("");
        setDataFinalContratoEmEdicao(true);
        setNovaDataFinalContrato(getCancelamentoContratoVO().getDataFinalAcessoCancelamentoAntecipado());
    }
}
