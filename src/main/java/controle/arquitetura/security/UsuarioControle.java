package controle.arquitetura.security;

import br.com.pactosolucoes.atualizadb.processo.SincronizarUsuarioNovoLogin;
import br.com.pactosolucoes.ce.comuns.enumerador.DiaSemana;
import br.com.pactosolucoes.comuns.json.JSONMapper;
import br.com.pactosolucoes.comuns.notificacao.RecursoSistema;
import br.com.pactosolucoes.comuns.to.PerfilTreinoResponseTO;
import br.com.pactosolucoes.comuns.util.JSFUtilities;
import br.com.pactosolucoes.enumeradores.*;
import br.com.pactosolucoes.estrutura.exportador.controle.ExportadorListaControle;
import br.com.pactosolucoes.notificacao.ServicoNotificacaoPush;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import controle.arquitetura.SuperControle;
import controle.arquitetura.exceptions.SessaoExpiradaException;
import controle.basico.CepControle;
import controle.basico.ColaboradorControle;
import controle.basico.VinculoAgendaControle;
import controle.basico.clube.MensagemGenericaControle;
import controle.modulos.integracao.UsuarioMovelControle;
import controle.plano.PlanoControle;
import edu.emory.mathcs.backport.java.util.Arrays;
import negocio.comuns.acesso.DadosAcessoOfflineVO;
import negocio.comuns.arquitetura.*;
import negocio.comuns.basico.*;
import negocio.comuns.basico.enumerador.TipoNotificacaoUsuarioEnum;
import negocio.comuns.basico.enumerador.negocio.comuns.basico.enumerador.cadastrodinamico.CadastroDinamicoColaboradorEnum;
import negocio.comuns.basico.enumerador.negocio.comuns.basico.enumerador.cadastrodinamico.LocaleEnum;
import negocio.comuns.crm.AgendaVO;
import negocio.comuns.crm.ConfiguracaoSistemaCRMVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.CepVO;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.ControleConsulta;
import negocio.comuns.utilitarias.Dominios;
import negocio.comuns.utilitarias.NotificacaoUsuarioVO;
import negocio.comuns.utilitarias.Ordenacao;
import negocio.comuns.utilitarias.SelectItemOrdemValor;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.comuns.utilitarias.UtilReflection;
import negocio.comuns.utilitarias.UteisEmail;
import negocio.facade.jdbc.arquitetura.ControleAcesso;
import negocio.facade.jdbc.arquitetura.Usuario;
import negocio.facade.jdbc.basico.Colaborador;
import negocio.facade.jdbc.utilitarias.Conexao;
import negocio.modulos.integracao.usuariomovel.UsuarioMovelVO;
import org.json.JSONArray;
import org.json.JSONObject;
import org.richfaces.event.UploadEvent;
import org.richfaces.model.UploadItem;
import servicos.discovery.RedeDTO;
import servicos.http.MetodoHttpEnum;
import servicos.http.RequestHttpService;
import servicos.http.RespostaHttpDTO;
import servicos.impl.autenticacaoMs.AutenticacaoMsService;
import servicos.impl.login.TokenDTO;
import servicos.impl.login.UsuarioGeralDTO;
import servicos.impl.pessoaMs.PessoaMsService;
import servicos.oamd.OamdMsService;
import servicos.oamd.RedeEmpresaDataDTO;
import servicos.operacoes.midias.MidiaService;
import servicos.operacoes.midias.commons.MidiaEntidadeEnum;
import servicos.propriedades.PropsService;
import servicos.util.ExecuteRequestHttpService;

import javax.faces.context.FacesContext;
import javax.faces.event.ActionEvent;
import javax.faces.event.ValueChangeEvent;
import javax.faces.model.SelectItem;
import javax.imageio.ImageIO;
import javax.servlet.http.HttpServletRequest;
import java.awt.image.BufferedImage;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.security.MessageDigest;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.stream.Collectors;

import static br.com.pactosolucoes.atualizadb.processo.SincronizarUsuarioNovoLogin.processarUsuarioRede;
import static java.util.Objects.isNull;
import static org.apache.commons.lang3.StringUtils.isNotBlank;

/**
 * Classe responsável por implementar a interação entre os componentes JSF das
 * páginas usuarioForm.jsp usuarioCons.jsp) com as funcionalidades da classe
 * <code>Usuario</code>. Implemtação da camada controle (Backing Bean).
 *
 * @see SuperControle
 * @see Usuario
 * @see UsuarioVO
 */
public class UsuarioControle extends SuperControle {

    private UsuarioVO usuarioVO;
    protected List listaSelectItemCodPerfilAcesso;
    protected List<PerfilTreinoResponseTO> listaSelectItemCodPerfilAcessoTreino;
    protected Integer codPerfilTw;
    private UsuarioPerfilAcessoVO usuarioPerfilAcessoVO;
    protected List listaSelectItemEmpresa;
    private Boolean cliente;
    private Boolean colaborador;
    private Boolean novoColaborador;
    private Boolean apresentarCampoSenhaUsuario;
    private Boolean apresentarComboSituacaoColaborador = false;
    private String campoConsultaUsuario;
    private String valorConsultaUsuario;
    private List listaConsultaUsuario;
    private String situacaoColaboradorConsulta;
    private Map<String, List<HorarioAcessoSistemaVO>> mapHorariosAcessoSistema = new HashMap<String, List<HorarioAcessoSistemaVO>>();
    private HorarioAcessoSistemaVO horarioAcessoSistemaVO = new HorarioAcessoSistemaVO();
    private String codigoDiaSemana = "";
    private List<HorarioAcessoSistemaVO> listaDomingo = new ArrayList<HorarioAcessoSistemaVO>();
    private List<HorarioAcessoSistemaVO> listaSegunda = new ArrayList<HorarioAcessoSistemaVO>();
    private List<HorarioAcessoSistemaVO> listaTerca = new ArrayList<HorarioAcessoSistemaVO>();
    private List<HorarioAcessoSistemaVO> listaQuarta = new ArrayList<HorarioAcessoSistemaVO>();
    private List<HorarioAcessoSistemaVO> listaQuinta = new ArrayList<HorarioAcessoSistemaVO>();
    private List<HorarioAcessoSistemaVO> listaSexta = new ArrayList<HorarioAcessoSistemaVO>();
    private List<HorarioAcessoSistemaVO> listaSabado = new ArrayList<HorarioAcessoSistemaVO>();
    private List<String> listaDiasSemanaEscolhidos = new ArrayList<String>();
    private Integer codigoTemp = 1;
    private boolean pesquisar = false;
    private String senhaAntesDaAlteracao;
    private String pinAntesDaAlteracao;
    private Boolean atualizarAgenda;
    private String onComplete;
    private TipoColaboradorVO tipoColaboradorVO;
    private Boolean erroSincronizarUsuarioMovel;

    private UsuarioPerfilAcessoVO usuarioPerfilSelecionado;
    private String situacaoFiltro;
    private UsuarioEmailVO usuarioEmailVO;
    private String urlImagemAssinatura;
    private Integer empresaFiltro;
    private boolean consultarInfoTodasEmpresas = false;
    private UsuarioVO usuarioVOClone;
    private TelefoneVO telefoneVO;
    private ConfiguracaoSistemaVO configuracaoSistema;
    private String[] displayIdentificadorFront;
    private String msgAlert;
    private TelefoneVO telefoneRemover;
    private TipoColaboradorVO tipoColaboradorVORemover;
    private List<UsuarioVO> listaUsuariosSelecionadosParaInativar = new ArrayList<UsuarioVO>();
    private List<UsuarioVO> lstUsuariosInativosMaisDeUmMes = new ArrayList<UsuarioVO>();
    private int scrollerPage;
    private List<UsuarioSincronizacaoVO> logSincronizacao;
    private String usernameDisponivelIcon;
    private String usernameDisponivelClass;
    private String usernameDisponivelMsg;
    private UsuarioTelefoneVO usuarioTelefoneVO;
    private TokenDTO tokenDTO;
    private UsuarioTelefoneVO usuarioTelefoneAlterarVO;
    private UsuarioEmailVO usuarioEmailAlterarVO;


    private String pin;
    private String modalMensagemGenerica;
    private CepControle cepControle;
    private EnderecoVO enderecoVO;
    private ColaboradorVO colaboradorVO;
    private PessoaVO pessoaVO;
    private List<String> listaCamposMostrarColaboradorDinamico;
    private List<String> listaCamposObrigatorioColaboradorDinamico;
    private List<UsuarioRedeEmpresaVO> listaUsuarioRedeEmpresa;

    private Boolean apresentarCampoExcluirDados;
    private boolean replicarUsuarioPerfilAcesso = false;

    /**
     * Interface
     * <code>UsuarioInterfaceFacade</code> responsável pela interconexão da
     * camada de controle com a camada de negócio. Criando uma independência da
     * camada de controle com relação a tenologia de persistência dos dados
     * (DesignPatter: Façade).
     */
    public UsuarioControle() throws Exception {
        try {
            obterUsuarioLogado();
            inicializarSemUser();
            inicializarPermissaoTodasEmpreas();
            inicializarConfiguracaoSistema();
            identificacaoPessoalInternacional();
            carregarLstVerificarUsuariosAtivos();
        } catch (SessaoExpiradaException e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
            setSucesso(false);
            setErro(true);
        }
    }

    public UsuarioControle(boolean semUser) throws Exception {
        inicializarSemUser();
    }

    private void inicializarSemUser() throws Exception {
        inicializarFacades();
        atualizarNrMsgNaoLidas(true);
        setControleConsulta(new ControleConsulta());
        setMensagemID("");
        this.atualizarAgenda = false;
        horarioAcessoSistemaVO.setNovoObj(true);
    }

    public void inicializarUsuarioLogado() {
        try {
            if (getUsuarioLogado().getCodigo() != 0) {
                usuarioVO.setUsuarioVO(getUsuarioLogado());
                usuarioVO.getUsuarioVO().setCodigo(getUsuarioLogado().getCodigo());
                usuarioVO.getUsuarioVO().setNome(getUsuarioLogado().getNome());
                usuarioVO.getUsuarioVO().setAdministrador(getUsuarioLogado().getAdministrador());
            }
        } catch (Exception exception) {
        }
    }

    private void inicializarPermissaoTodasEmpreas() {
        try {
            boolean temPermissao = permissao("ConsultarInfoTodasEmpresas");
            consultarInfoTodasEmpresas = temPermissao;

            if (getUsuarioLogado().getAdministrador() || temPermissao) {
                montarListaSelectItemEmpresa("", consultarInfoTodasEmpresas);
                empresaFiltro = 0;
                consultarInfoTodasEmpresas = true;
            } else {
                empresaFiltro = getEmpresaLogado().getCodigo();
            }
        } catch (Exception ignored) {
        }
    }

    public void inicializarConfiguracaoSistema() {
        setConfiguracaoSistema((ConfiguracaoSistemaVO) JSFUtilities.getFromSession(JSFUtilities.CONFIGURACAO_SISTEMA));
        try {
            UsuarioVO userVO = getUsuarioLogado();
            getFacade().getUsuario().registrarUltimoLoginAcessoAgora(userVO);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * Rotina responsável por disponibilizar um novo objeto da classe
     * <code>Usuario</code> para edição pelo usuário da aplicação.
     */
    public String novo() throws Exception {
        setUsuarioVO(new UsuarioVO());
        setUsuarioPerfilAcessoVO(new UsuarioPerfilAcessoVO());
        setUsuarioEmailVO(new UsuarioEmailVO());
        setUsuarioTelefoneVO(new UsuarioTelefoneVO());
        setCliente(false);
        setColaborador(false);
        setNovoColaborador(false);
        setApresentarCampoSenhaUsuario(true);
        setPesquisar(false);
        setCampoConsultaUsuario("");
        setValorConsultaUsuario("");
        setListaConsultaUsuario(new ArrayList());
        inicializarUsuarioLogado();
        inicializarListasSelectItemTodosComboBox();
        limparDados();
        preencherHorariosAcessosSistemaPadrao();
        limparMsg();
        usuarioVO.setInternacional(configuracaoSistema.isUsarSistemaInternacional());

        setTipoColaboradorVO(new TipoColaboradorVO());
        return "editar";
    }

    /**
     * Preenche lista de horários de acesso ao sistema padrões ao cadastrar novo
     * usuário
     *
     * @throws Exception
     */
    public void preencherHorariosAcessosSistemaPadrao() throws Exception {
        preencherHorariosAcessosSistema("06:00", "23:00");
    }

    public void preencherHorariosAcessosSistema24Horas() {
        try {
            limparDados();
            preencherHorariosAcessosSistema("00:00", "23:59");

            setMensagemID("msg_dados_adicionados");
            setSucesso(true);
            setErro(false);
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
            setSucesso(false);
            setErro(true);
        }
    }

    public void preencherHorariosAcessosSistema(String horaInicial, String horaFinal) throws Exception {
        HorarioAcessoSistemaVO hor = new HorarioAcessoSistemaVO();
        codigoTemp = getFacade().getHorarioAcessoSistema().consultarUltimoCodigo();
        hor.setCodigo(codigoTemp++);
        hor.setDiaSemana(DiaSemana.DOMINGO);
        hor.setHoraInicial(horaInicial);
        hor.setHoraFinal(horaFinal);
        listaDomingo.add(hor);
        mapHorariosAcessoSistema.put(DiaSemana.DOMINGO.getDescricao(), listaDomingo);
        listaDiasSemanaEscolhidos.add(DiaSemana.DOMINGO.getDescricao());

        HorarioAcessoSistemaVO hor2 = new HorarioAcessoSistemaVO();
        hor2.setCodigo(codigoTemp++);
        hor2.setDiaSemana(DiaSemana.SEGUNDA_FEIRA);
        hor2.setHoraInicial(horaInicial);
        hor2.setHoraFinal(horaFinal);
        listaSegunda.add(hor2);
        mapHorariosAcessoSistema.put(DiaSemana.SEGUNDA_FEIRA.getDescricao(), listaSegunda);
        listaDiasSemanaEscolhidos.add(DiaSemana.SEGUNDA_FEIRA.getDescricao());

        HorarioAcessoSistemaVO hor3 = new HorarioAcessoSistemaVO();
        hor3.setCodigo(codigoTemp++);
        hor3.setDiaSemana(DiaSemana.TERCA_FEIRA);
        hor3.setHoraInicial(horaInicial);
        hor3.setHoraFinal(horaFinal);
        listaTerca.add(hor3);
        mapHorariosAcessoSistema.put(DiaSemana.TERCA_FEIRA.getDescricao(), listaTerca);
        listaDiasSemanaEscolhidos.add(DiaSemana.TERCA_FEIRA.getDescricao());

        HorarioAcessoSistemaVO hor4 = new HorarioAcessoSistemaVO();
        hor4.setCodigo(codigoTemp++);
        hor4.setDiaSemana(DiaSemana.QUARTA_FEIRA);
        hor4.setHoraInicial(horaInicial);
        hor4.setHoraFinal(horaFinal);
        listaQuarta.add(hor4);
        mapHorariosAcessoSistema.put(DiaSemana.QUARTA_FEIRA.getDescricao(), listaQuarta);
        listaDiasSemanaEscolhidos.add(DiaSemana.QUARTA_FEIRA.getDescricao());

        HorarioAcessoSistemaVO hor5 = new HorarioAcessoSistemaVO();
        hor5.setCodigo(codigoTemp++);
        hor5.setDiaSemana(DiaSemana.QUINTA_FEIRA);
        hor5.setHoraInicial(horaInicial);
        hor5.setHoraFinal(horaFinal);
        listaQuinta.add(hor5);
        mapHorariosAcessoSistema.put(DiaSemana.QUINTA_FEIRA.getDescricao(), listaQuinta);
        listaDiasSemanaEscolhidos.add(DiaSemana.QUINTA_FEIRA.getDescricao());

        HorarioAcessoSistemaVO hor6 = new HorarioAcessoSistemaVO();
        hor6.setCodigo(codigoTemp++);
        hor6.setDiaSemana(DiaSemana.SEXTA_FEIRA);
        hor6.setHoraInicial(horaInicial);
        hor6.setHoraFinal(horaFinal);
        listaSexta.add(hor6);
        mapHorariosAcessoSistema.put(DiaSemana.SEXTA_FEIRA.getDescricao(), listaSexta);
        listaDiasSemanaEscolhidos.add(DiaSemana.SEXTA_FEIRA.getDescricao());

        HorarioAcessoSistemaVO hor7 = new HorarioAcessoSistemaVO();
        hor7.setCodigo(codigoTemp++);
        hor7.setDiaSemana(DiaSemana.SABADO);
        hor7.setHoraInicial(horaInicial);
        hor7.setHoraFinal(horaFinal);
        listaSabado.add(hor7);
        mapHorariosAcessoSistema.put(DiaSemana.SABADO.getDescricao(), listaSabado);
        listaDiasSemanaEscolhidos.add(DiaSemana.SABADO.getDescricao());
    }

    /**
     * Preenche combo de dias da semana
     *
     * @return objs Lista de dias da semana
     */
    public List getListaDiaSemana() {
        List objs = new ArrayList();
        objs.add(new SelectItem("", ""));
        try {
            DiaSemana[] diasSemana = DiaSemana.values();
            for (DiaSemana diaSemana : diasSemana) {
                objs.add(new SelectItem(diaSemana.getCodigo(), diaSemana.getDescricao()));
            }
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
        return objs;
    }

    /**
     * Adiciona horário de acesso ao mapa que contém todos os horários de
     * acessos
     */
    public void adicionarHorarioAcesso() {
        try {
            //seta o dia da semana
            if (!"".equals(codigoDiaSemana)) {
                horarioAcessoSistemaVO.setDiaSemana(DiaSemana.getDiaSemana(getCodigoDiaSemana()));
            }
            horarioAcessoSistemaVO.setUsuarioVO(usuarioVO);
            //validar campos vazios
            HorarioAcessoSistemaVO.validarDados(horarioAcessoSistemaVO);
            //verificar conflitos de horário por dia da semana
            validarConflitosHorariosPorDiaSemana(horarioAcessoSistemaVO);
            if (horarioAcessoSistemaVO.getNovoObj()) {
                adicionarHorarioAcessoLista();
            } else {
                //seta o código se o usuário editar lista em memória que não tenha sido gravada ainda
                if (horarioAcessoSistemaVO.getCodigo() == 0) {
                    horarioAcessoSistemaVO.setCodigo(getFacade().getHorarioAcessoSistema().consultarUltimoCodigo());
                }
                editarHorarioAcessoLista(horarioAcessoSistemaVO);
            }
            setMensagemID("msg_dados_adicionados");
            setSucesso(true);
            setErro(false);
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
            setSucesso(false);
            setErro(true);
        } finally {
            //setar novo objeto - sem ele o mapa não insere corretamente
            horarioAcessoSistemaVO = new HorarioAcessoSistemaVO();
            getHorarioAcessoSistemaVO().setNovoObj(true);
        }
    }

    /**
     * Adiciona horário de acesso na lista de dias escolhidos, nas listas de
     * dias da semana e também no mapa que contém todos os horários de todos os
     * dias da semana.
     */
    public void adicionarHorarioAcessoLista() {
        if (codigoDiaSemana.equals(DiaSemana.DOMINGO.getCodigo())) {
            listaDomingo.add(horarioAcessoSistemaVO);
            mapHorariosAcessoSistema.put(DiaSemana.DOMINGO.getDescricao(), listaDomingo);
            if (!getListaDiasSemanaEscolhidos().contains(DiaSemana.DOMINGO.getDescricao())) {
                getListaDiasSemanaEscolhidos().add(DiaSemana.DOMINGO.getDescricao());
            }
        } else if (codigoDiaSemana.equals(DiaSemana.SEGUNDA_FEIRA.getCodigo())) {
            listaSegunda.add(horarioAcessoSistemaVO);
            mapHorariosAcessoSistema.put(DiaSemana.SEGUNDA_FEIRA.getDescricao(), listaSegunda);
            if (!getListaDiasSemanaEscolhidos().contains(DiaSemana.SEGUNDA_FEIRA.getDescricao())) {
                getListaDiasSemanaEscolhidos().add(DiaSemana.SEGUNDA_FEIRA.getDescricao());
            }
        } else if (codigoDiaSemana.equals(DiaSemana.TERCA_FEIRA.getCodigo())) {
            listaTerca.add(horarioAcessoSistemaVO);
            mapHorariosAcessoSistema.put(DiaSemana.TERCA_FEIRA.getDescricao(), listaTerca);
            if (!getListaDiasSemanaEscolhidos().contains(DiaSemana.TERCA_FEIRA.getDescricao())) {
                getListaDiasSemanaEscolhidos().add(DiaSemana.TERCA_FEIRA.getDescricao());
            }
        } else if (codigoDiaSemana.equals(DiaSemana.QUARTA_FEIRA.getCodigo())) {
            listaQuarta.add(horarioAcessoSistemaVO);
            mapHorariosAcessoSistema.put(DiaSemana.QUARTA_FEIRA.getDescricao(), listaQuarta);
            if (!getListaDiasSemanaEscolhidos().contains(DiaSemana.QUARTA_FEIRA.getDescricao())) {
                getListaDiasSemanaEscolhidos().add(DiaSemana.QUARTA_FEIRA.getDescricao());
            }
        } else if (codigoDiaSemana.equals(DiaSemana.QUINTA_FEIRA.getCodigo())) {
            listaQuinta.add(horarioAcessoSistemaVO);
            mapHorariosAcessoSistema.put(DiaSemana.QUINTA_FEIRA.getDescricao(), listaQuinta);
            if (!getListaDiasSemanaEscolhidos().contains(DiaSemana.QUINTA_FEIRA.getDescricao())) {
                getListaDiasSemanaEscolhidos().add(DiaSemana.QUINTA_FEIRA.getDescricao());
            }
        } else if (codigoDiaSemana.equals(DiaSemana.SEXTA_FEIRA.getCodigo())) {
            listaSexta.add(horarioAcessoSistemaVO);
            mapHorariosAcessoSistema.put(DiaSemana.SEXTA_FEIRA.getDescricao(), listaSexta);
            if (!getListaDiasSemanaEscolhidos().contains(DiaSemana.SEXTA_FEIRA.getDescricao())) {
                getListaDiasSemanaEscolhidos().add(DiaSemana.SEXTA_FEIRA.getDescricao());
            }
        } else if (codigoDiaSemana.equals(DiaSemana.SABADO.getCodigo())) {
            listaSabado.add(horarioAcessoSistemaVO);
            mapHorariosAcessoSistema.put(DiaSemana.SABADO.getDescricao(), listaSabado);
            if (!getListaDiasSemanaEscolhidos().contains(DiaSemana.SABADO.getDescricao())) {
                getListaDiasSemanaEscolhidos().add(DiaSemana.SABADO.getDescricao());
            }
        }
        //setar codigo temporario somente para edição de objetos
        if (horarioAcessoSistemaVO.getNovoObj()) {
            horarioAcessoSistemaVO.setCodigo(codigoTemp++);
        }
    }

    public void expirarSenha() {
        try {
            Integer qtdDiasParaExpirarSenha = getFacade().getConfiguracaoSistema().buscarQtdDiasParaExpirarSenha();
            getUsuarioVO().setDataUltimaAlteracaoSenha(Uteis.somarDias(Calendario.getDataComHoraZerada(Calendario.hoje()),
                    -1 * (qtdDiasParaExpirarSenha + 1)));
            setErro(false);
            setSucesso(true);
            setMensagemID("msg_dados_adicionados");
        } catch (Exception e) {
            setErro(true);
            setSucesso(false);
            setMensagemID("msg_erro");
            setMensagemDetalhada(e);
        }
    }

    /**
     * Edita o horário de acesso na lista
     *
     * @param objAtual para editar
     */
    public void editarHorarioAcessoLista(HorarioAcessoSistemaVO objAtual) {
        for (List<HorarioAcessoSistemaVO> listaHorarios : mapHorariosAcessoSistema.values()) {
            Iterator i = listaHorarios.iterator();
            while (i.hasNext()) {
                HorarioAcessoSistemaVO objExistente = (HorarioAcessoSistemaVO) i.next();
                if (objExistente.getCodigo().equals(objAtual.getCodigo())) {
                    //remover o objeto da lista que ele está
                    removerObjetoLista(objAtual);
                    //adicionar na lista do dia da semana escolhido nesse momento
                    adicionarHorarioAcessoLista();
                    return;
                }
            }
        }
    }

    public void removerObjetoLista(HorarioAcessoSistemaVO obj) {
        if (listaDomingo.contains(obj)) {
            listaDomingo.remove(obj);
            if (getListaDiasSemanaEscolhidos().contains(DiaSemana.DOMINGO.getDescricao())) {
                if (listaDomingo.isEmpty()) {
                    getListaDiasSemanaEscolhidos().remove(DiaSemana.DOMINGO.getDescricao());
                }
            }
        } else if (listaSegunda.contains(obj)) {
            listaSegunda.remove(obj);
            if (getListaDiasSemanaEscolhidos().contains(DiaSemana.SEGUNDA_FEIRA.getDescricao())) {
                if (listaSegunda.isEmpty()) {
                    getListaDiasSemanaEscolhidos().remove(DiaSemana.SEGUNDA_FEIRA.getDescricao());
                }
            }
        } else if (listaTerca.contains(obj)) {
            listaTerca.remove(obj);
            if (getListaDiasSemanaEscolhidos().contains(DiaSemana.TERCA_FEIRA.getDescricao())) {
                if (listaTerca.isEmpty()) {
                    getListaDiasSemanaEscolhidos().remove(DiaSemana.TERCA_FEIRA.getDescricao());
                }
            }
        } else if (listaQuarta.contains(obj)) {
            listaQuarta.remove(obj);
            if (getListaDiasSemanaEscolhidos().contains(DiaSemana.QUARTA_FEIRA.getDescricao())) {
                if (listaQuarta.isEmpty()) {
                    getListaDiasSemanaEscolhidos().remove(DiaSemana.QUARTA_FEIRA.getDescricao());
                }
            }
        } else if (listaQuinta.contains(obj)) {
            listaQuinta.remove(obj);
            if (getListaDiasSemanaEscolhidos().contains(DiaSemana.QUINTA_FEIRA.getDescricao())) {
                if (listaQuinta.isEmpty()) {
                    getListaDiasSemanaEscolhidos().remove(DiaSemana.QUINTA_FEIRA.getDescricao());
                }
            }
        } else if (listaSexta.contains(obj)) {
            listaSexta.remove(obj);
            if (getListaDiasSemanaEscolhidos().contains(DiaSemana.QUARTA_FEIRA.getDescricao())) {
                if (listaQuarta.isEmpty()) {
                    getListaDiasSemanaEscolhidos().remove(DiaSemana.QUARTA_FEIRA.getDescricao());
                }
            }
        } else if (listaSabado.contains(obj)) {
            listaSabado.remove(obj);
            if (getListaDiasSemanaEscolhidos().contains(DiaSemana.SABADO.getDescricao())) {
                if (listaSabado.isEmpty()) {
                    getListaDiasSemanaEscolhidos().remove(DiaSemana.SABADO.getDescricao());
                }
            }
        }
    }

    /**
     * Valida cada horário inserido na lista analisando se entra em conflito com
     * algum horário já contido na lista
     *
     * @param obj
     * @throws Exception
     */
    public void validarConflitosHorariosEmMemoria(HorarioAcessoSistemaVO obj) throws Exception {
        for (List<HorarioAcessoSistemaVO> listaHorarios : mapHorariosAcessoSistema.values()) {
            Iterator i = listaHorarios.iterator();
            while (i.hasNext()) {
                HorarioAcessoSistemaVO objExistente = (HorarioAcessoSistemaVO) i.next();
                if (obj.getDiaSemana().equals(objExistente.getDiaSemana()) && !obj.getCodigo().equals(objExistente.getCodigo())) {
                    if (Calendario.validarSeEstaNoIntervaloHoras(obj.getHoraInicial(), obj.getHoraFinal(), objExistente.getHoraInicial(), objExistente.getHoraFinal())) {
                        throw new Exception("O horário (DIA SEMANA: " + obj.getDiaSemana().getDescricao()
                                + ", HORA INICIAL: " + obj.getHoraInicial()
                                + ", HORA FINAL: " + obj.getHoraFinal() + ") entra em conflito com o horário (DIA SEMANA: " + objExistente.getDiaSemana().getDescricao()
                                + ", HORA INICIAL: " + objExistente.getHoraInicial()
                                + ", HORA FINAL: " + objExistente.getHoraFinal() + ")");
                    }
                }
            }
        }
    }

    /**
     * Faz a validação completa contendo a validação de formato das horas e
     * também a validação de horários de acesso que entram em conflito
     *
     * @param obj
     * @throws Exception
     */
    public void validarConflitosHorariosPorDiaSemana(HorarioAcessoSistemaVO obj) throws Exception {
        //valida horas - formato e comparação entre as datas de inicio e termino
        Calendario.validarHoras(obj.getHoraInicial(), obj.getHoraFinal());
        //Valida as horas já cadastradas em memória
        validarConflitosHorariosEmMemoria(obj);
    }

    public void consultarTodosHorariosSemanaPutMap() throws Exception {
        limparDados();
        for (DiaSemana diaSemana : DiaSemana.values()) {
            List<HorarioAcessoSistemaVO> listaDiaSemana = getFacade().getHorarioAcessoSistema().consultarPorDiaSemana(
                    diaSemana.getCodigo(), usuarioVO.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            if (!listaDiaSemana.isEmpty()) {
                povoarListas(diaSemana, listaDiaSemana);
            }
        }
    }

    /**
     * Limpa os dados usados no cadastro de usuário
     */
    public void limparDados() throws Exception {
        mapHorariosAcessoSistema.clear();
        listaDiasSemanaEscolhidos.clear();
        listaDomingo.clear();
        listaSegunda.clear();
        listaTerca.clear();
        listaQuarta.clear();
        listaQuinta.clear();
        listaSexta.clear();
        listaSabado.clear();
        setHorarioAcessoSistemaVO(new HorarioAcessoSistemaVO());
        codigoTemp = getFacade().getHorarioAcessoSistema().consultarUltimoCodigo() + 1;
    }

    /**
     * Povoa os horários de acesso inserindo os mesmos no mapa e na lista do dia
     * da semana correspondente
     *
     * @param diaSemana
     * @param listaDiaSemana
     */
    public void povoarListas(DiaSemana diaSemana, List<HorarioAcessoSistemaVO> listaDiaSemana) {
        if (diaSemana.equals(DiaSemana.DOMINGO)) {
            listaDomingo = listaDiaSemana;
            mapHorariosAcessoSistema.put(DiaSemana.DOMINGO.getDescricao(), listaDomingo);
            if (!getListaDiasSemanaEscolhidos().contains(DiaSemana.DOMINGO.getDescricao())) {
                getListaDiasSemanaEscolhidos().add(DiaSemana.DOMINGO.getDescricao());
            }
        } else if (diaSemana.equals(DiaSemana.SEGUNDA_FEIRA)) {
            listaSegunda = listaDiaSemana;
            mapHorariosAcessoSistema.put(DiaSemana.SEGUNDA_FEIRA.getDescricao(), listaSegunda);
            if (!getListaDiasSemanaEscolhidos().contains(DiaSemana.SEGUNDA_FEIRA.getDescricao())) {
                getListaDiasSemanaEscolhidos().add(DiaSemana.SEGUNDA_FEIRA.getDescricao());
            }
        } else if (diaSemana.equals(DiaSemana.TERCA_FEIRA)) {
            listaTerca = listaDiaSemana;
            mapHorariosAcessoSistema.put(DiaSemana.TERCA_FEIRA.getDescricao(), listaTerca);
            if (!getListaDiasSemanaEscolhidos().contains(DiaSemana.TERCA_FEIRA.getDescricao())) {
                getListaDiasSemanaEscolhidos().add(DiaSemana.TERCA_FEIRA.getDescricao());
            }
        } else if (diaSemana.equals(DiaSemana.QUARTA_FEIRA)) {
            listaQuarta = listaDiaSemana;
            mapHorariosAcessoSistema.put(DiaSemana.QUARTA_FEIRA.getDescricao(), listaQuarta);
            if (!getListaDiasSemanaEscolhidos().contains(DiaSemana.QUARTA_FEIRA.getDescricao())) {
                getListaDiasSemanaEscolhidos().add(DiaSemana.QUARTA_FEIRA.getDescricao());
            }
        } else if (diaSemana.equals(DiaSemana.QUINTA_FEIRA)) {
            listaQuinta = listaDiaSemana;
            mapHorariosAcessoSistema.put(DiaSemana.QUINTA_FEIRA.getDescricao(), listaQuinta);
            if (!getListaDiasSemanaEscolhidos().contains(DiaSemana.QUINTA_FEIRA.getDescricao())) {
                getListaDiasSemanaEscolhidos().add(DiaSemana.QUINTA_FEIRA.getDescricao());
            }
        } else if (diaSemana.equals(DiaSemana.SEXTA_FEIRA)) {
            listaSexta = listaDiaSemana;
            mapHorariosAcessoSistema.put(DiaSemana.SEXTA_FEIRA.getDescricao(), listaSexta);
            if (!getListaDiasSemanaEscolhidos().contains(DiaSemana.SEXTA_FEIRA.getDescricao())) {
                getListaDiasSemanaEscolhidos().add(DiaSemana.SEXTA_FEIRA.getDescricao());
            }
        } else if (diaSemana.equals(DiaSemana.SABADO)) {
            listaSabado = listaDiaSemana;
            mapHorariosAcessoSistema.put(DiaSemana.SABADO.getDescricao(), listaSabado);
            if (!getListaDiasSemanaEscolhidos().contains(DiaSemana.SABADO.getDescricao())) {
                getListaDiasSemanaEscolhidos().add(DiaSemana.SABADO.getDescricao());
            }
        }
    }

    /**
     * Edita o horário de acesso da lista
     */
    public void editarHorarioAcesso() {
        HorarioAcessoSistemaVO horarioDomingo = (HorarioAcessoSistemaVO) context().getExternalContext().getRequestMap().get("horariosDomingo");
        HorarioAcessoSistemaVO horarioSegunda = (HorarioAcessoSistemaVO) context().getExternalContext().getRequestMap().get("horariosSegunda");
        HorarioAcessoSistemaVO horarioTerca = (HorarioAcessoSistemaVO) context().getExternalContext().getRequestMap().get("horariosTerca");
        HorarioAcessoSistemaVO horarioQuarta = (HorarioAcessoSistemaVO) context().getExternalContext().getRequestMap().get("horariosQuarta");
        HorarioAcessoSistemaVO horarioQuinta = (HorarioAcessoSistemaVO) context().getExternalContext().getRequestMap().get("horariosQuinta");
        HorarioAcessoSistemaVO horarioSexta = (HorarioAcessoSistemaVO) context().getExternalContext().getRequestMap().get("horariosSexta");
        HorarioAcessoSistemaVO horarioSabado = (HorarioAcessoSistemaVO) context().getExternalContext().getRequestMap().get("horariosSabado");
        if (horarioDomingo != null) {
            setHorarioAcessoSistemaVO(horarioDomingo);
            setCodigoDiaSemana(DiaSemana.DOMINGO.getCodigo());
        } else if (horarioSegunda != null) {
            setHorarioAcessoSistemaVO(horarioSegunda);
            setCodigoDiaSemana(DiaSemana.SEGUNDA_FEIRA.getCodigo());
        } else if (horarioTerca != null) {
            setHorarioAcessoSistemaVO(horarioTerca);
            setCodigoDiaSemana(DiaSemana.TERCA_FEIRA.getCodigo());
        } else if (horarioQuarta != null) {
            setHorarioAcessoSistemaVO(horarioQuarta);
            setCodigoDiaSemana(DiaSemana.QUARTA_FEIRA.getCodigo());
        } else if (horarioQuinta != null) {
            setHorarioAcessoSistemaVO(horarioQuinta);
            setCodigoDiaSemana(DiaSemana.QUINTA_FEIRA.getCodigo());
        } else if (horarioSexta != null) {
            setHorarioAcessoSistemaVO(horarioSexta);
            setCodigoDiaSemana(DiaSemana.SEXTA_FEIRA.getCodigo());
        } else if (horarioSabado != null) {
            setHorarioAcessoSistemaVO(horarioSabado);
            setCodigoDiaSemana(DiaSemana.SABADO.getCodigo());
        }
        horarioAcessoSistemaVO.setNovoObj(false);
    }

    /**
     * Operação responsável por processar a exclusão um objeto da classe
     * <code>HorarioAcessoSistemaVO</code> da lista de horários
     */
    public void excluirHorarioAcesso() {
        try {
            excluirHorarioAcesso(buscarCodigoParaExclusao());
            setMensagemID("msg_dados_excluidos");
            setSucesso(true);
            setErro(false);
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
            setSucesso(false);
            setErro(true);
        }
    }

    /**
     * Busca por código usado na exclusão do horário
     */
    public Integer buscarCodigoParaExclusao() {
        HorarioAcessoSistemaVO horarioDomingo = (HorarioAcessoSistemaVO) context().getExternalContext().getRequestMap().get("horariosDomingo");
        HorarioAcessoSistemaVO horarioSegunda = (HorarioAcessoSistemaVO) context().getExternalContext().getRequestMap().get("horariosSegunda");
        HorarioAcessoSistemaVO horarioTerca = (HorarioAcessoSistemaVO) context().getExternalContext().getRequestMap().get("horariosTerca");
        HorarioAcessoSistemaVO horarioQuarta = (HorarioAcessoSistemaVO) context().getExternalContext().getRequestMap().get("horariosQuarta");
        HorarioAcessoSistemaVO horarioQuinta = (HorarioAcessoSistemaVO) context().getExternalContext().getRequestMap().get("horariosQuinta");
        HorarioAcessoSistemaVO horarioSexta = (HorarioAcessoSistemaVO) context().getExternalContext().getRequestMap().get("horariosSexta");
        HorarioAcessoSistemaVO horarioSabado = (HorarioAcessoSistemaVO) context().getExternalContext().getRequestMap().get("horariosSabado");
        if (horarioDomingo != null) {
            return horarioDomingo.getCodigo();
        } else if (horarioSegunda != null) {
            return horarioSegunda.getCodigo();
        } else if (horarioTerca != null) {
            return horarioTerca.getCodigo();
        } else if (horarioQuarta != null) {
            return horarioQuarta.getCodigo();
        } else if (horarioQuinta != null) {
            return horarioQuinta.getCodigo();
        } else if (horarioSexta != null) {
            return horarioSexta.getCodigo();
        } else if (horarioSabado != null) {
            return horarioSabado.getCodigo();
        }
        return 0;
    }

    /**
     * Exclui o horário de acesso da lista
     *
     * @param codigo
     * @throws Exception
     */
    public void excluirHorarioAcesso(Integer codigo) throws Exception {
        for (List<HorarioAcessoSistemaVO> listaHorarios : mapHorariosAcessoSistema.values()) {
            Iterator i = listaHorarios.iterator();
            while (i.hasNext()) {
                HorarioAcessoSistemaVO objExistente = (HorarioAcessoSistemaVO) i.next();
                if (objExistente.getCodigo().intValue() == codigo.intValue()) {
                    listaHorarios.remove(objExistente);
                    if (listaHorarios.isEmpty()) {
                        listaDiasSemanaEscolhidos.remove(objExistente.getDiaSemana().getDescricao());
                    }
                    return;
                }
            }
        }
    }

    /**
     * Rotina responsável por disponibilizar os dados de um objeto da classe
     * <code>Usuario</code> para alteração. O objeto desta classe é
     * disponibilizado na session da página (request) para que o JSP
     * correspondente possa disponibilizá-lo para edição.
     */
    public String editar() {
        return editarParametro(null);
    }

    public String editarParametro(UsuarioVO obj) {
        try {
            limparMsg();
            setUsernameDisponivelClass("");
            setUsernameDisponivelIcon("");
            setUsernameDisponivelMsg("");


            if (obj == null) {
                Integer codigoConsulta = Integer.parseInt(request().getParameter("chavePrimaria"));
                obj = getFacade().getUsuario().consultarPorChavePrimaria(codigoConsulta, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            }
            mapHorariosAcessoSistema.clear();

            if (context() != null && obj.getUsername().matches("(A|a)(D|d)(M|m)(I|i)(N|n)|(P|p)(A|a)(C|c)(T|t)(O|o)((B|b)(R|r)|(E|e)(C|c)(F|f))|(R|r)(E|e)(C|c)(O|o)(R|r)") && !getUsuarioLogado().getAdministrador()) {
                throw new Exception("Este usuário não pode ser editado.");
            }


            if (obj.getTipoUsuario().equals("CI") && obj.getClienteVO().getCodigo() > 0) {
                setCliente(true);
                setColaborador(false);
                setNovoColaborador(false);
            }
            if (obj.getTipoUsuario().equals("CE") && obj.getColaboradorVO().getCodigo() > 0) {
                setColaborador(true);
                setCliente(false);
                setNovoColaborador(false);
            }
            if (obj.getTipoUsuario().equals("NC") && obj.getColaboradorVO().getCodigo() > 0) {
                setNovoColaborador(true);
                setCliente(false);
                setColaborador(false);
            }
            inicializarAtributosRelacionados(obj);
            obj.setNovoObj(false);

            setPinAntesDaAlteracao(obj.getPin());
            obj.setPin("0000"); //Para não confundir os usuários na tela;

            setUsuarioVO(obj);
            preencherUsuarioEmail();
            preencherUsuarioTelefone();

            inicializarUsuarioLogado();
            validarPermissaoAlterarSenha();
            validarPermissaoExcluirDados();
//            if (usuarioVO.getUsuarioVO().getAdministrador()) {
//                setApresentarCampoSenhaUsuario(true);
//            } else {
//                setApresentarCampoSenhaUsuario(false);
//            }
            setCampoConsultaUsuario("");
            setValorConsultaUsuario("");
            setListaConsultaUsuario(new ArrayList());
            inicializarListasSelectItemTodosComboBox();
            setUsuarioPerfilAcessoVO(new UsuarioPerfilAcessoVO());
            setSenhaAntesDaAlteracao(getUsuarioVO().getSenha());


            obj.registrarObjetoVOAntesDaAlteracao();
            obj.getColaboradorVO().registrarObjetoVOAntesDaAlteracao();
            obj.getColaboradorVO().getPessoa().registrarObjetoVOAntesDaAlteracao();
            consultarTodosHorariosSemanaPutMap();
            obterURLAssinatura();
            setPesquisar(true);
            getUsuarioVO().setListaVinculoConsultor(new ArrayList<ColaboradorVO>());
            if (isExibirReplicarRedeEmpresa()) {
                prepararListaReplicarEmpresa();
            }
            limparMsg();
        } catch (Exception e) {
            setErro(true);
            setSucesso(false);
            setMensagemDetalhada("msg_erro", e.getMessage());
            return "buscar";
        } finally {
            try {
                usuarioVOClone = (UsuarioVO) usuarioVO.getClone(true);
            } catch (Exception e) {
                e.printStackTrace();
            }

        }
        return "editar";
    }

    public void preencherUsuarioEmail() throws Exception {
        setUsuarioEmailVO(getFacade().getUsuarioEmail().consultarPorUsuario(getUsuarioVO().getCodigo()));
        String emailStr = UteisValidacao.emptyString(getUsuarioEmailVO().getEmail()) ? this.getUsuarioEmailAlterarVO().getEmail() : getUsuarioEmailVO().getEmail();
        if (UteisValidacao.emptyNumber(getUsuarioEmailVO().getCodigo())) {
            getUsuarioEmailVO().setUsuario(getUsuarioVO().getCodigo());
            getUsuarioEmailVO().setUsuarioVO(getUsuarioVO());
            getUsuarioEmailVO().setEmail(emailStr);
            if (!UteisValidacao.emptyString(getUsuarioEmailVO().getEmail())) {
                getFacade().getUsuarioEmail().incluir(getUsuarioEmailVO());
            }
        }
        getUsuarioEmailVO().registrarObjetoVOAntesDaAlteracao();

        EmailVO email = new EmailVO();
        email.setEmail(emailStr);
        getUsuarioVO().setEmailVO(email);
        getUsuarioVO().setEmail(emailStr);
    }

    public void preencherUsuarioTelefone() throws Exception {
        setUsuarioTelefoneVO(getFacade().getUsuarioTelefone().consultarPorUsuario(getUsuarioVO().getCodigo()));
        if (UteisValidacao.emptyNumber(getUsuarioTelefoneVO().getCodigo())) {
            getUsuarioTelefoneVO().setUsuario(getUsuarioVO().getCodigo());
            getUsuarioTelefoneVO().setUsuarioVO(getUsuarioVO());
        }
        getUsuarioTelefoneVO().registrarObjetoVOAntesDaAlteracao();
    }

    public void validarPermissaoExcluirDados() throws Exception {
        if (context() == null) {
            return;
        }
        if (getUsuarioLogado().getUsuarioPerfilAcessoVOs().isEmpty()) {
            if (getUsuarioLogado().getAdministrador()) {
                setApresentarCampoExcluirDados(true);
                return;
            }
            setApresentarCampoExcluirDados(false);
            return;
        }
        try {
            for (Object o : getUsuarioLogado().getUsuarioPerfilAcessoVOs()) {
                UsuarioPerfilAcessoVO usuarioPerfilAcesso = (UsuarioPerfilAcessoVO) o;
                if (getEmpresaLogado().getCodigo().equals(usuarioPerfilAcesso.getEmpresa().getCodigo().intValue())) {
                    usuarioPerfilAcesso.getPerfilAcesso().setPermissaoVOs(getFacade().getPermissao().consultarPermissaos(usuarioPerfilAcesso.getPerfilAcesso().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
                    getFacade().getControleAcesso().verificarPermissaoUsuarioFuncionalidade(usuarioPerfilAcesso.getPerfilAcesso(),
                            getUsuarioLogado(), "ExcluirCliente", "9.69 - Permitir excluir cliente da base de dados");
                }
            }
            setApresentarCampoExcluirDados(true);
        } catch (Exception e) {
            setApresentarCampoExcluirDados(false);
        }
    }

    public void validarPermissaoAlterarSenha() throws Exception {
        if (context() == null) {
            return;
        }
        if (getUsuarioLogado().getUsuarioPerfilAcessoVOs().isEmpty()) {
            if (getUsuarioLogado().getAdministrador()) {
                setApresentarCampoSenhaUsuario(true);
                return;
            }
            setApresentarCampoSenhaUsuario(false);
            return;
        }
        try {
            for (Object o : getUsuarioLogado().getUsuarioPerfilAcessoVOs()) {
                UsuarioPerfilAcessoVO usuarioPerfilAcesso = (UsuarioPerfilAcessoVO) o;
                if (getEmpresaLogado().getCodigo().equals(usuarioPerfilAcesso.getEmpresa().getCodigo().intValue())) {
                    usuarioPerfilAcesso.getPerfilAcesso().setPermissaoVOs(getFacade().getPermissao().consultarPermissaos(usuarioPerfilAcesso.getPerfilAcesso().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
                    getFacade().getControleAcesso().verificarPermissaoUsuarioFuncionalidade(usuarioPerfilAcesso.getPerfilAcesso(),
                            getUsuarioLogado(), "Alterar Senhas", "1.08 - Permissão para alterar senha de outro usuário");
                }
            }
            setApresentarCampoSenhaUsuario(true);
        } catch (Exception e) {
            setApresentarCampoSenhaUsuario(false);
        }
    }

    /**
     * Método responsável inicializar objetos relacionados a classe
     * <code>UsuarioVO</code>. Esta inicialização é necessária por exigência da
     * tecnologia JSF, que não trabalha com valores nulos para estes atributos.
     */
    public void inicializarAtributosRelacionados(UsuarioVO obj) {
//        if (obj.getCodPerfilAcesso() == null) {
//            obj.setCodPerfilAcesso(new PerfilAcessoVO());
//        }
    }

    /**
     * Rotina responsável por gravar no BD os dados editados de um novo objeto
     * da classe
     * <code>Usuario</code>. Caso o objeto seja novo (ainda não gravado no BD) é
     * acionado a operação
     * <code>incluir()</code>. Caso contrário é acionado o
     * <code>alterar()</code>. Se houver alguma inconsistência o objeto não é
     * gravado, sendo re-apresentado para o usuário juntamente com uma mensagem
     * de erro.
     */
    public String gravarSimplesComUsuarioEmail() {
        try {
            usuarioEmailVO = new UsuarioEmailVO();
            EmailVO email = (EmailVO) context().getExternalContext().getRequestMap().get("colaboradorEmail");
            usuarioEmailVO.setEmail(email.getEmail());
            usuarioVO.setEmailVO(email);
            return gravarSimples(false);
        } catch (Exception e) {
            setSucesso(false);
            setErro(true);
            setMensagemDetalhada("msg_erro", e.getMessage());
            return "editar";
        }
    }

    public String  gravarSimples(Boolean centralEventos) throws Exception {
        setMsgAlert("");
        usuarioVO.setInternacional(configuracaoSistema.isUsarSistemaInternacional());
        //SOLICITACAO MAXIMILIANO NÃO ALTERAR -- TICKET #3319!!!
        if (!Uteis.removerEspacosInicioFimString(usuarioVO.getSenha()).equals(usuarioVO.getSenha())) {
            throw new Exception("A senha não pode conter espaços no início ou no final");
        }
        //SOLICITACAO MAXIMILIANO NÃO ALTERAR -- TICKET #3319!!!
        if (!Uteis.removerEspacosInicioFimString(usuarioVO.getUsername()).equals(usuarioVO.getUsername())) {
            throw new Exception("O nome de usuário não pode conter espaços no início ou no final");
        }
        if ((usuarioVO.getUsername().toUpperCase().equals("ADMIN") ||usuarioVO.getUsername().toUpperCase().equals("RECOR")) && !usuarioVO.getAdministrador()) {
            throw new Exception("O campo NOME DE USUÁRIO informado não pode ser usado. Informe outro nome de usuário.");
        }

        if (UteisValidacao.emptyString(this.getUsuarioEmailVO().getEmail())) {
            throw new Exception("O campo EMAIL (Usuário) deve ser informado.");
        }

        if(UteisValidacao.emptyNumber(this.getCodPerfilTw()) && isModuloTreinoHabilitado()){
            throw new Exception("O perfil de acesso do treino não pode ficar vazio");
        }

        if (!"0000".equals(getUsuarioVO().getPin())) {
            try {
                int pinNumber = Integer.parseInt(getUsuarioVO().getPin());
            } catch (Exception ex) {
                throw new Exception("O campo PIN (Usuário) deve ser apenas números.");
            }
        }

        //preenche a lista de horários de acessos ao sistema
        this.usuarioVO.setUsuarioLogado(getUsuarioLogado());
        usuarioVO.setUsuarioHorarioAcessoSistemaVOs(new ArrayList());
        for (List<HorarioAcessoSistemaVO> listaHorarios : mapHorariosAcessoSistema.values()) {
            Iterator i = listaHorarios.iterator();
            while (i.hasNext()) {
                HorarioAcessoSistemaVO objExistente = (HorarioAcessoSistemaVO) i.next();
                validarConflitosHorariosPorDiaSemana(objExistente);
                usuarioVO.getUsuarioHorarioAcessoSistemaVOs().add(objExistente);
            }
        }

        if (!getUsuarioVO().getSenha().equalsIgnoreCase(getUsuarioVO().getSenhaConfirmar())) {
            throw new Exception("As Senhas Informadas não Conferem");// a nova senha é diferente da confirmacao da senha ?
        }

        boolean enviarNovoUsuarioSincronizarLoginGeral = false;

        if (centralEventos) {
            this.verificarAutorizacao();
            validarTipoUsuario();
            if (usuarioVO.isNovoObj()) {
                usuarioVO.setDataUltimaAlteracaoSenha(Calendario.hoje());
                getFacade().getUsuario().incluir(usuarioVO, true);
                //LOG - INICIO
                try {
                    usuarioVO.setObjetoVOAntesAlteracao(new UsuarioVO());
                    usuarioVO.setNovoObj(true);
                    registrarLogObjetoVO(usuarioVO, usuarioVO.getCodigo(), "USUARIO", 0);
                } catch (Exception e) {
                    registrarLogErroObjetoVO("USUARIO", usuarioVO.getCodigo(), "ERRO AO GERAR LOG DE INCLUSÃO DE USUARIO", this.getUsuarioLogado().getNome(), this.getUsuarioLogado().getUserOamd());
                    e.printStackTrace();
                }
                //LOG - FIM
            } else {

                if (!MessageDigest.isEqual(getFacade().getUsuario().consultarSenhaPorCodigoUsuario(usuarioVO.getCodigo()).getBytes(), usuarioVO.getSenha().getBytes())) {
                    usuarioVO.setDataUltimaAlteracaoSenha(Calendario.hoje());
                }
                getFacade().getUsuario().alterar(usuarioVO, true, false, false);
                if (usuarioVO.getColaboradorVO().getCodigo() != 0) {
                    usuarioVO.getColaboradorVO().registrarObjetoVOAntesDaAlteracao();
                    usuarioVO.getColaboradorVO().getPessoa().registrarObjetoVOAntesDaAlteracao();
                    verificarAgenda();
                    getFacade().getColaborador().alterar(usuarioVO.getColaboradorVO(), true, false);
                }
                //LOG - INICIO
                try {
                    registrarLogObjetoVO(usuarioVO, usuarioVO.getCodigo(), "USUARIO", 0);
                } catch (Exception e) {
                    registrarLogErroObjetoVO("USUARIO", usuarioVO.getCodigo(), "ERRO AO GERAR LOG DE ALTERAÇÃO DE USUARIO", this.getUsuarioLogado().getNome(), this.getUsuarioLogado().getUserOamd());
                    e.printStackTrace();
                }
                //LOG - FIM
            }
        } else {
            validarTipoUsuario();

            if (!UteisValidacao.emptyString(this.getUsuarioTelefoneVO().getNumero())) {
                adicionarTelefoneUsuario();
            }

            if (!getTipoColaboradorVO().getDescricao().isEmpty()) {
                getUsuarioVO().getColaboradorVO().adicionarObjTipoColaboradorVOs(getTipoColaboradorVO());
            }

            adicionarEmail();

            if (usuarioVO.isNovoObj()) {

                //caso a senha não seja informada gerar uma senha aleatória
                if (getUsuarioVO().getSenha().isEmpty()) {
                    Integer senhaAleatoria = UteisValidacao.gerarNumeroRandomico(10000, 50000);
                    usuarioVO.setSenha(senhaAleatoria.toString());
                    usuarioVO.setSenhaConfirmar(senhaAleatoria.toString());
                }

                if (getFacade().getUsuarioEmail().existeEmail(getUsuarioEmailVO())) {
                    throw new Exception("Chave (email)=(" + getUsuarioEmailVO().getEmail() + ") já existe.");
                }

                usuarioVO.setSenhaNaoCriptografada(usuarioVO.getSenha());
                usuarioVO.setDataUltimaAlteracaoSenha(Calendario.hoje());

                montarTipoStatusPerfilTw();

                getFacade().getUsuario().incluir(usuarioVO);

                //gravar usuario email
                gravarUsuarioEmail();
                //gravar usuario telefone
                gravarUsuarioTelefone();

                enviarNovoUsuarioSincronizarLoginGeral = true;

                //LOG - INICIO
                registraLogInclusaoUsuario();
                //LOG - FIM
            } else {
                limparMsg();
                boolean alterouSituacao;
                boolean alterouPerfilAcesso;

                UsuarioVO antesAlteracao = (UsuarioVO) usuarioVO.getObjetoVOAntesAlteracao();
                //Verifica se pode inativá-lo
                if (usuarioVO.getColaboradorVO().getSituacao().equals("NA") && !usuarioVO.getColaboradorVO().getSituacao().equals(antesAlteracao.getColaboradorVO().getSituacao())) {

                    if (existeVinculoColaboradoresAssociados()) {
                        return "";
                    }

                    if(usuarioVO != null && !UteisValidacao.emptyNumber(usuarioVO.getCodigo())) {
                        ServicoNotificacaoPush.enviaNotificacaoDesativacaoUsuario(getKey(), getEmpresaLogado().getCodigo(), usuarioVO.getCodigo(), getEmpresaLogado().getNome());
                    }
                }

                if(usuarioVO.getColaboradorVO().getSituacao().equals("AT") && !usuarioVO.getColaboradorVO().getSituacao().equals(antesAlteracao.getColaboradorVO().getSituacao())) {
                    ServicoNotificacaoPush.enviaNotificacaoAtivacaoUsuario(getKey(), getEmpresaLogado().getCodigo(), usuarioVO.getCodigo(), getEmpresaLogado().getNome());
                }

                alterouSituacao = !usuarioVO.getColaboradorVO().getSituacao().equals(antesAlteracao.getColaboradorVO().getSituacao());

                usuarioVO.setAlterouSenha(false);
                if (!usuarioVO.getSenha().equals(getSenhaAntesDaAlteracao())) {

                    if (getUsuarioLogado().getUsername().equals(usuarioVO.getUsername()) && !UteisValidacao.validarSenhaSegura(usuarioVO.getSenha())) {
                        throw new ConsistirException("A senha não atende os requisitos mínimos de segurança! A senha deve conter pelo menos 8 caracteres com uma combinação de letras maiúsculas, minúsculas, números e caracteres especiais.");
                    }

                    usuarioVO.setAlterouSenha(true);
                }

                usuarioVO.setAlterouPIN(false);
                if (!UteisValidacao.emptyString(usuarioVO.getPin()) && !usuarioVO.getPin().equals("0000")) {
                    String pinCriptografado = Uteis.encriptar(usuarioVO.getPin());
                    if (getPinAntesDaAlteracao() != null && !getPinAntesDaAlteracao().equals(pinCriptografado)) {
                        usuarioVO.setAlterouPIN(true);
                    }
                }

                if (!usuarioVO.isAlterouPIN()) {
                    usuarioVO.setPin(pinAntesDaAlteracao);
                }

                if (!MessageDigest.isEqual(getFacade().getUsuario().consultarSenhaPorCodigoUsuario(usuarioVO.getCodigo()).getBytes(), usuarioVO.getSenha().getBytes())) {
                    usuarioVO.setDataUltimaAlteracaoSenha(Calendario.hoje());
                }

                //LISTA UTILIZADA PARA O LOG
                List<UsuarioPerfilAcessoVO> usuarioPerfilAcessoAnt = new ArrayList<UsuarioPerfilAcessoVO>();
                usuarioPerfilAcessoAnt = getFacade().getUsuarioPerfilAcesso().consultarUsuarioPerfilAcesso(usuarioVO.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                if (usuarioVO.getColaboradorVO().getSituacao().equals("AT") && existePerfilAcessoRetiradoComVinculo(usuarioPerfilAcessoAnt)) {
                    return "";
                }

                List<HorarioAcessoSistemaVO> horariosAcessoUsuarioAnt = new ArrayList<HorarioAcessoSistemaVO>();
                if (!usuarioVO.getUsuarioHorarioAcessoSistemaVOs().isEmpty()) {
                    horariosAcessoUsuarioAnt = getFacade().getHorarioAcessoSistema().consultar(usuarioVO.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                }

                //gravar usuario email
                gravarUsuarioEmail();

                montarTipoStatusPerfilTw();

                getFacade().getUsuario().alterar(usuarioVO);
                setPinAntesDaAlteracao(usuarioVO.getPin());
                usuarioVO.setPin("0000");
                usuarioVO.setAlterouPIN(false);

                if (usuarioVO.isAlterouSenha()) {
                    if (!getUsuarioLogado().getUsername().equals(usuarioVO.getUsername())
                            && !UteisValidacao.validarSenhaSegura(usuarioVO.getSenha())) {
                        getFacade().getPessoa().atualizarDados(true, usuarioVO.getColaboradorVO().getPessoa().getCodigo());
                    }
                }

                alterouPerfilAcesso = registraLogAlteracaoUsuario(usuarioPerfilAcessoAnt, horariosAcessoUsuarioAnt);

                if (usuarioVO.getColaboradorVO().getCodigo() != 0) {
                    verificarAgenda();
                    usuarioVO.getColaboradorVO().getPessoa().setEmpresaInternacional(configuracaoSistema.isUsarSistemaInternacional());
                    getFacade().getColaborador().alterar(usuarioVO.getColaboradorVO());
                    //LOG - INICIO
                    try {
                        registrarLogObjetoVO(usuarioVO, usuarioVO.getCodigo(), "USUARIO", usuarioVO.getColaboradorVO().getPessoa().getCodigo());
                        registrarLogObjetoVO(usuarioVO.getColaboradorVO(), usuarioVO.getColaboradorVO().getPessoa().getCodigo());
                        registrarLogObjetoVO(usuarioVO.getColaboradorVO().getPessoa(), usuarioVO.getColaboradorVO().getCodigo(), "COLABORADOR", usuarioVO.getColaboradorVO().getPessoa().getCodigo());
                    } catch (Exception e) {
                        registrarLogErroObjetoVO("COLABORADOR", usuarioVO.getColaboradorVO().getPessoa().getCodigo(), "ERRO AO GERAR LOG DE ALTERAÇÃO DE COLABORADOR", this.getUsuarioLogado().getNome(), this.getUsuarioLogado().getUserOamd());
                        e.printStackTrace();
                    }
                    //LOG - FIM
                }

                if (!UteisValidacao.emptyString(getUsuarioVO().getUsuarioGeral()) &&
                        (alterouPerfilAcesso || alterouSituacao)) {
                    //se alterou o perfil de acesso ou a situação do colaborador então deve sincronizar o usuario
                    try {
                        SincronizarUsuarioNovoLogin.atualizarUsuarioGeral(this.getUsuarioVO().getCodigo(), Conexao.getFromSession(),
                                getEmpresaLogado().getCodigo(), getUsuarioLogado(), getIpCliente(), getIntegracaoNovoLogin());
                    } catch (Exception ex) {
                        ex.printStackTrace();
                    }
                }

                if (alterouSituacao) {
                    getFacade().getUsuario().processarRecursoPadraoNovoUsuario(true, false, getUsuarioLogado());
                }
            }
        }

        try {
            //sincronizar com o treino
            sincronizarUsuarioMovel(true, getKey());
        } finally {
            //caso aconteca algum erro ao sincronizar com o usuario movel ainda sim ele irá enviar os dados para o login geral.
            //enviar email com o link para o cliente alterar a senha - NOVO LOGIN
            if (enviarNovoUsuarioSincronizarLoginGeral) {
                try {
                    //sincronizar o usuário enviando o email que o usuário informou no cadastro
                    SincronizarUsuarioNovoLogin.enviarEmailNovoUsuario(this.getUsuarioVO().getCodigo(), true, false, null,
                            Conexao.getFromSession(), getEmpresaLogado().getCodigo(), getUsuarioLogado(), getIpCliente(), getIntegracaoNovoLogin());
                } catch (Exception ex) {
                    ex.printStackTrace();
                }
            }
        }

        adicionarUsuarioServicoDescobrir();

        usuarioEmailVO.registrarObjetoVOAntesDaAlteracao();
        usuarioTelefoneVO.registrarObjetoVOAntesDaAlteracao();
        usuarioVO.registrarObjetoVOAntesDaAlteracao();
        usuarioVO.getColaboradorVO().registrarObjetoVOAntesDaAlteracao();
        usuarioVO.getColaboradorVO().getPessoa().registrarObjetoVOAntesDaAlteracao();
        if (usuarioVO.isAlterouSenha()) {
            if ((usuarioVO.getColaboradorVO() != null) && (usuarioVO.getColaboradorVO().getCodigo() != null)
                    && (usuarioVO.getColaboradorVO().getCodigo() > 0) && (usuarioVO.getColaboradorVO().getSituacao().equals("AT"))) {
                notificarOuvintes(DadosAcessoOfflineVO.CHAVE_NOTIFICAR_ATUALIZAR_BASE_PESSOA + "(" + usuarioVO.getColaboradorVO().getPessoa().getCodigo() + ")");
            }
        }
        if (isExibirReplicarRedeEmpresa()) {
            prepararListaReplicarEmpresa();
            replicarAutomaticoTodas();
        } else if (!enviarNovoUsuarioSincronizarLoginGeral && isNotBlank(usuarioVO.getUsuarioGeral())) {
            if (!usuarioVO.getSituacao_Usuario().isEmpty()){
                if (usuarioVO.getSituacao_Usuario().equals("Inativo")){
                    desvincularUsuario();
                }else {
                    sincronizarNovoLogin();
                }
            }
        }
        Uteis.reloadTreinoCacheUsuario(getKey(), this.usuarioVO);
        setTipoColaboradorVO(new TipoColaboradorVO());
        setMensagemDetalhada("", "");
        setMensagem("Dados Gravados com Sucesso");
        setSucesso(true);
        setErro(false);
        return "editar";
    }

    public boolean isModuloTreinoHabilitado() {
        String modulos = (String) JSFUtilities.getFromSession("modulosHabilitados");
        return !UteisValidacao.emptyString(modulos) && modulos.contains("TR");
    }

    private void montarTipoStatusPerfilTw() {
        try {
            usuarioVO.setStatusTw(usuarioVO.getColaboradorVO().getSituacao().equals("AT") ? 0 : 1);
            usuarioVO.setPerfilTw(this.getCodPerfilTw());

            if (usuarioVO != null && usuarioVO.getColaboradorVO() != null &&
                    usuarioVO.getColaboradorVO().getListaTipoColaboradorVOs() != null &&
                    !usuarioVO.getColaboradorVO().getListaTipoColaboradorVOs().isEmpty()) {

                usuarioVO.getColaboradorVO().getListaTipoColaboradorVOs().forEach(tipoCo -> {
                    if (tipoCo.getDescricao().equals("AD")) {
                        usuarioVO.setTipoTw(2);
                    }
                });

                usuarioVO.getColaboradorVO().getListaTipoColaboradorVOs().forEach(tipoCo -> {
                    if (tipoCo.getDescricao().equals("CR")) {
                        usuarioVO.setTipoTw(2);
                    }
                });

                usuarioVO.getColaboradorVO().getListaTipoColaboradorVOs().forEach(tipoCo -> {
                    if (tipoCo.getDescricao().equals("PR") || tipoCo.getDescricao().equals("TW")) {
                        usuarioVO.setTipoTw(1);
                    }
                });

                usuarioVO.getColaboradorVO().getListaTipoColaboradorVOs().forEach(tipoCo -> {
                    if (tipoCo.getDescricao().equals("CO")) {
                        usuarioVO.setTipoTw(4);
                    }
                });

                if (UteisValidacao.emptyNumber(usuarioVO.getTipoTw())) {
                    usuarioVO.setTipoTw(4);
                }
            } else {
                usuarioVO.setTipoTw(4);
            }
        } catch (Exception ex) {
            usuarioVO.setTipoTw(4);
            ex.printStackTrace();
        }
    }

    private List<PerfilTreinoResponseTO> buscarPerfisTreino() throws Exception {
        String urlTreino = PropsService.getPropertyValue(getKey(), PropsService.urlTreino);
        String fullUrl = urlTreino + "/prest/psec/perfis-acesso";
        Map<String, String> headers = new HashMap<>();
        headers.put("Authorization", AutenticacaoMsService.token(getKey()));
        RequestHttpService httpService = new RequestHttpService();
        RespostaHttpDTO respostaHttpDTO = httpService.executeRequest(fullUrl, headers, null, null, MetodoHttpEnum.GET);
        List<PerfilTreinoResponseTO> perfisTreino = JSONMapper.getList(new JSONObject(respostaHttpDTO.getResponse()).getJSONArray("content"), PerfilTreinoResponseTO.class);
        return perfisTreino;
    }

    public void adicionarUsuarioServicoDescobrir() {
        try {
            if (usuarioEmailVO != null && !usuarioEmailVO.getEmail().isEmpty() && usuarioEmailVO.getEmail().contains("@")) {
                String url = String.format("%s/prest/usuarioapp/%s/v4/gerarUsuarioRedeEmpresa",
                        PropsService.getPropertyValue(PropsService.urlOamd),
                        getKey());
                String empresas  = usuarioVO.getUsuarioPerfilAcessoVOs().stream().filter(up -> up.getEmpresa().isAtiva()).map(up -> up.getEmpresa().getCodigo().toString()).collect(Collectors.joining(","));

                Map<String, String> params = new HashMap<String, String>();
                params.put("email", usuarioEmailVO.getEmail());
                params.put("cpf", usuarioVO.getColaboradorVO().getPessoa().getCfp());
                params.put("telefone", usuarioVO.getColaboradorVO().getPessoa().getTelefones());
                params.put("dataNascimento", Uteis.getDataAplicandoFormatacao(usuarioVO.getColaboradorVO().getPessoa().getDataNasc(), "dd/MM/yyyy"));
                params.put("senha", usuarioVO.getSenha());
                params.put("empresas", empresas);
                String result = ExecuteRequestHttpService.executeRequest(url, params, false, "utf-8");
                if (result.contains("erro")) {
                    throw new Exception(new JSONObject(result).optString("erro"));
                }
            }
        }catch (Exception e){
            Uteis.logar("Erro ao enviar o usuarioapp para aplicação OAMD");
            Uteis.logar(e, UsuarioControle.class);
        }
    }

    private boolean existeVinculoColaboradoresAssociados() throws Exception {
        List<ColaboradorVO> listaColaboradores = getFacade().getColaborador().consultarPorCodigoPessoa(getUsuarioVO().getColaboradorVO().getPessoa().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        for (ColaboradorVO colaboradorVO : listaColaboradores) {
            if (validarVinculosColaborador(colaboradorVO, "USUÁRIO - INATIVAR")) {
                return true;
            }
        }
        return false;
    }

    private boolean existePerfilAcessoRetiradoComVinculo(List<UsuarioPerfilAcessoVO> usuarioPerfilAcessoAnt) throws Exception {
        setMsgAlert("");
        List<UsuarioPerfilAcessoVO> listaExcluidos = new ArrayList<>();
        for (UsuarioPerfilAcessoVO usuPerfil : usuarioPerfilAcessoAnt) {
            UsuarioPerfilAcessoVO perfilMemoria = null;
            boolean achou = false;
            for (Object object : usuarioVO.getUsuarioPerfilAcessoVOs()) {
                perfilMemoria = (UsuarioPerfilAcessoVO) object;
                if (perfilMemoria.getEmpresa().getCodigo().equals(usuPerfil.getEmpresa().getCodigo())) {
                    achou = true;
                    break;
                }
            }
            if (!achou) {
                listaExcluidos.add(usuPerfil);
            }
        }
        for (UsuarioPerfilAcessoVO usuPerfil : listaExcluidos) {
            if (informarNovoVinculoParaClientes(usuPerfil)) {
                return true;
            }
        }
        return false;
    }

    public String gravar(boolean centralEventos) {
        try {
            setMsgAlert("");
            limparMsg();

            if (UteisValidacao.emptyString(this.getUsuarioEmailVO().getEmail().trim())) {
                throw new ConsistirException("O campo E-MAIL (Usuário) deve ser informado.");
            }

            if (!UteisValidacao.validaEmail(this.getUsuarioEmailVO().getEmail().trim())) {
                throw new Exception("O campo E-MAIL (Usuário) não é inválido!");
            }

            if (getFacade().getUsuarioEmail().existeEmail(this.getUsuarioEmailVO())) {
                throw new ConsistirException("Já existe um usuário com esse e-mail.");
            }
            this.getUsuarioVO().getEmailVO().setEmail(this.getUsuarioEmailVO().getEmail());

            if (!UteisValidacao.emptyString(this.getUsuarioTelefoneVO().getNumero()) &&
                    getFacade().getUsuarioTelefone().existeTelefone(this.getUsuarioTelefoneVO())) {
                throw new ConsistirException("Já existe um usuário com esse telefone.");
            }

            notificarRecursoEmpresaUsuario();
            usuarioVOClone = (UsuarioVO) usuarioVO.getClone(true);
            return gravarSimples(centralEventos);
        } catch (Exception e) {
            montarErro(e);
            Uteis.logar(e, UsuarioControle.class);
            return "editar";
        } finally {
            try {
                adicionarTelefonesUsuarioOAMD(getKey(), getUsuarioVO().getColaboradorVO().getPessoa().getTelefoneVOs());
            } catch (Exception e) {
                montarErro(e);

                Uteis.logar(e, UsuarioControle.class);
            }
        }
    }

    public void adicionarTelefonesUsuarioOAMD(String ctx, List<TelefoneVO> telefones) throws Exception {
        if (telefones.size() > 0) {
            for (Object object : getUsuarioVO().getUsuarioPerfilAcessoVOs()) {
                UsuarioPerfilAcessoVO usu = (UsuarioPerfilAcessoVO) object;
                if (usu.getPerfilAcesso().getNome().toUpperCase().contains("ADMINISTRADOR")) {
                    String url = String.format("%s/prest/usuarioapp/%s/persistirTelefone",
                            PropsService.getPropertyValue(PropsService.urlOamd),
                            ctx);

                    JSONArray telefonesArray = new JSONArray();
                    for (TelefoneVO telefoneVO : telefones) {
                        JSONObject objTelefone = new JSONObject();
                        if (UteisValidacao.emptyString(telefoneVO.getDdi())) {
                            objTelefone.put("telefone", "+55" + telefoneVO.getNumeroSemMascara());
                        } else {
                            objTelefone.put("telefone", telefoneVO.getDdi() + "" + telefoneVO.getNumeroSemMascara());
                        }
                        telefonesArray.put(objTelefone);
                    }


                    Map<String, String> params = new HashMap<>();
                    params.put("codEmpresa", usu.getEmpresa().getCodigo().toString());
                    params.put("codUsuario", getUsuarioVO().getCodigo().toString());
                    params.put("jsonTelefones", telefonesArray.toString());
                    String result = ExecuteRequestHttpService.executeRequest(url, params, false, "utf-8");
                    if (result.contains("erro")) {
                        throw new Exception(new JSONObject(result).optString("erro"));
                    }
                }
            }
        }
    }

    /**
     * Verifica se é necessário atualizar os agendamentos do usuario.
     */
    private void verificarAgenda() throws Exception {
        if (this.atualizarAgenda) {
            this.atualizarAgenda = false;
            VinculoAgendaControle vinculoAgendaControle = (VinculoAgendaControle) getControlador(VinculoAgendaControle.class);
            getUsuarioVO().getColaboradorVO().setCodigoUsuarioColaboradorTransfAgendaAntigo(getUsuarioVO().getCodigo());
            getUsuarioVO().getColaboradorVO().setCodigoUsuarioColaboradorTransfAgendaNovo(vinculoAgendaControle.getUsuarioVOSelecionado());
        }
    }

    public void atualizarNrMsgNaoLidas(boolean apenasNumeros) throws Exception {
        if (context() != null) {
            if (apenasNumeros) {
                getUsuario().setNrMensagensNaoLidas(getFacade().getSocialMailing().conversasNaoLidas(getUsuarioLogado().getColaboradorVO().getPessoa().getCodigo(), null));
            } else {
                getUsuario().setNrMensagensNaoLidas(getFacade().getSocialMailing().conversasNaoLidas(getUsuarioLogado().getColaboradorVO().getPessoa().getCodigo(), null));
                getUsuario().setListaGrupoSolicitacao(getFacade().getSocialMailing().consultarGruposConversaPactoBRSolicitacoesEmAberto(getUsuarioLogado().getColaboradorVO().getPessoa().getCodigo()));
                getUsuario().setListaSolicitacoesRespostasNaoCompreendidas(getFacade().getSocialMailing().consultarSolicitacoesRespostasNaoCompreeendidas(getUsuarioLogado().getColaboradorVO().getPessoa().getCodigo()));
                getUsuario().setExisteConversaSolicitacao(getFacade().getSocialMailing().existeGruposConversaPactoBRSolicitacoesMesmoFinalizada(getUsuarioLogado().getColaboradorVO().getPessoa().getCodigo()));
            }
            atualizarNotificacoesUCP();
            processarApresentarModalSolicitacoes();
        }
    }

    private void processarApresentarModalSolicitacoes() {
        try {
            if (!SuperControle.getHabilitarLembreteSolicitacoes() || getUsuario().getUsuarioPACTOBR()) {
                getUsuario().setDataApresentacaoModalSolicitacoes(Calendario.hoje());
                getUsuario().setApresentarModalSolicitacoesPendentes(false);
                return;
            }

            if (getUsuario().getDataApresentacaoModalSolicitacoes() == null) {
                getUsuario().setDataApresentacaoModalSolicitacoes(Calendario.hoje());
                if (getUsuario().getListaSolicitacoesRespostasNaoCompreendidas().size() > 0) {
                    getUsuario().setApresentarModalSolicitacoesPendentes(true);
                }
            } else {
                Long minutos = Calendario.diferencaEmMinutos(getUsuario().getDataApresentacaoModalSolicitacoes(), Calendario.hoje());
                if (minutos >= 15 && getUsuario().getListaSolicitacoesRespostasNaoCompreendidas().size() > 0) {
                    getUsuario().setApresentarModalSolicitacoesPendentes(true);
                }
            }
        } catch (Exception ignored) {
        }
    }

    public void naoApresentarModalSolicitacoes() {
        try {
            getUsuario().setDataApresentacaoModalSolicitacoes(Calendario.hoje());
            getUsuario().setApresentarModalSolicitacoesPendentes(false);
        } catch (Exception ignored) {
        }
    }

    /**
     * Responsável por chamar o método gravar indicando que não deve usar as
     * funcionalidades do CE
     *
     * @return
     * <AUTHOR> 23/03/2011
     */
    public String gravar() {
        return gravar(false);

    }

    /**
     * Responsável por chamar o método gravar indicando que deve usar as
     * funcionalidades do CE
     *
     * @return
     * <AUTHOR> 23/03/2011
     */
    public String gravarCE() {
        return gravar(true);

    }

    public void validarTipoUsuario() throws ConsistirException, Exception {
        try {
            if (usuarioVO.getTipoUsuario().equals("CI") && (!usuarioVO.getClienteVO().getPessoa().getNome().equals(""))) {
                usuarioVO.setNome(usuarioVO.getClienteVO().getPessoa().getNome());
            }
            if (usuarioVO.getTipoUsuario().equals("CE") && (!usuarioVO.getColaboradorVO().getPessoa().getNome().equals(""))) {
                usuarioVO.setNome(usuarioVO.getColaboradorVO().getPessoa().getNome());
            }
            if (usuarioVO.getTipoUsuario().equals("NC")) {
                consultarColaboradorExistente(usuarioVO);
                if (usuarioVO.getCodigo().intValue() == 0) {
                    usuarioVO.getColaboradorVO().setSituacao("AT");
                }
                usuarioVO.setNome(usuarioVO.getColaboradorVO().getPessoa().getNome());
            }

        } catch (Exception e) {
            throw e;
        }
    }

    public void consultarColaboradorExistente(UsuarioVO obj) throws Exception {
        try {
            if (!obj.getColaboradorVO().getPessoa().getCfp().equals("") && obj.getColaboradorVO().getCodigo().intValue() == 0) {
                ColaboradorVO colabora = new Colaborador().consultarPorCfp(obj.getColaboradorVO().getPessoa().getCfp(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                if (colabora.getCodigo().intValue() != 0) {
                    throw new Exception("O Colaborador de CPF : " + obj.getColaboradorVO().getPessoa().getCfp() + " já está cadastrado!");
                }
            }
        } catch (Exception e) {
            throw e;
        }
    }

    /**
     * Rotina responsavel por executar as consultas disponiveis no JSP
     * UsuarioCons.jsp. Define o tipo de consulta a ser executada, por meio de
     * ComboBox denominado campoConsulta, disponivel neste mesmo JSP. Como
     * resultado, disponibiliza um List com os objetos selecionados na sessao da
     * pagina.
     */
    public String consultar() {
        try {
            super.consultar();
            List objs = new ArrayList();
            if (getControleConsulta().getCampoConsulta().equals("codigo")) {
                if (getControleConsulta().getValorConsulta().equals("")) {
                    getControleConsulta().setValorConsulta("0");
                }
                int valorInt = Integer.parseInt(getControleConsulta().getValorConsulta());
                objs = getFacade().getUsuario().consultarPorCodigo(
                        new Integer(valorInt), true, Uteis.NIVELMONTARDADOS_TODOS);
            }
            if (getControleConsulta().getCampoConsulta().equals("nome")) {
                objs = getFacade().getUsuario().consultarPorNome(
                        getControleConsulta().getValorConsulta(), true,
                        Uteis.NIVELMONTARDADOS_TODOS);
            }
            if (getControleConsulta().getCampoConsulta().equals("username")) {
                objs = getFacade().getUsuario().consultarPorUsername(
                        getControleConsulta().getValorConsulta(), true,
                        Uteis.NIVELMONTARDADOS_TODOS);
            }
            if (getControleConsulta().getCampoConsulta().equals("nomePerfilAcesso")) {
                objs = getFacade().getUsuario().consultarPorNomePerfilAcesso(
                        getControleConsulta().getValorConsulta(),
                        Uteis.NIVELMONTARDADOS_TODOS);
            }
            if (getControleConsulta().getCampoConsulta().equals("situacaoColaborador")) {
                objs = getFacade().getUsuario().consultarPorSituacaoColaborador(
                        getSituacaoColaboradorConsulta(), false,
                        Uteis.NIVELMONTARDADOS_TODOS);
            }
            objs = ControleConsulta.obterSubListPaginaApresentar(objs, controleConsulta);
            definirVisibilidadeLinksNavegacao(controleConsulta.getPaginaAtual(), controleConsulta.getNrTotalPaginas());
            setListaConsulta(objs);
            limparMsg();
            return "consultar";
        } catch (Exception e) {
            setSucesso(false);
            setErro(true);
            setListaConsulta(new ArrayList());
            setMensagemDetalhada("msg_erro", e.getMessage());
            return "consultar";
        }
    }
//    public void redesenhaTela() {
//        try {
//            verificarTipoPessoa();
//            if (getUsuario().getAdministrador().equals(new Boolean(true)) && getUsuario().getTipoUsuario().equals("")) {
//                setMostraNome(true);
//            } else {
//                setMostraNome(false);
//            }
//        } catch (Exception ex) {
//            setMensagemDetalhada("msg_erro", ex.getMessage());
//        }
//    }

    /**
     * Rotina responsavel por executar as consultas disponiveis no JSP
     * UsuarioCons.jsp. Define o tipo de consulta a ser executada, por meio de
     * ComboBox denominado campoConsulta, disponivel neste mesmo JSP. Como
     * resultado, disponibiliza um List com os objetos selecionados na sessao da
     * pagina.
     */
    public void consultarCliente() {
        try {
            super.consultar();
            List objs = new ArrayList();
            if (getCampoConsultaUsuario().equals("codigo")) {
                if (getValorConsultaUsuario().equals("")) {
                    setValorConsultaUsuario("0");
                }
                int valorInt = Integer.parseInt(getValorConsultaUsuario());
                objs = getFacade().getCliente().consultarPorCodigo(new Integer(valorInt), getEmpresaLogado().getCodigo().intValue(), true, Uteis.NIVELMONTARDADOS_TELACONSULTA);
            }
            if (getCampoConsultaUsuario().equals("nome")) {
                objs = getFacade().getCliente().consultarPorNomePessoa(getValorConsultaUsuario(), getEmpresaLogado().getCodigo().intValue(), Uteis.NIVELMONTARDADOS_TELACONSULTA, null);
            }

            setListaConsultaUsuario(objs);
            setMensagemID("msg_dados_consultados");
            setSucesso(true);
            setErro(false);
        } catch (Exception e) {
            setListaConsulta(new ArrayList());
            setSucesso(false);
            setErro(true);
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public void selecionarCliente() {
        ClienteVO obj = (ClienteVO) context().getExternalContext().getRequestMap().get("cliente");
        getUsuarioVO().setClienteVO(obj);
        setCampoConsultaUsuario("");
        setValorConsultaUsuario("");
        setListaConsultaUsuario(new ArrayList());
    }

    /**
     * Rotina responsavel por executar as consultas disponiveis no JSP
     * UsuarioCons.jsp. Define o tipo de consulta a ser executada, por meio de
     * ComboBox denominado campoConsulta, disponivel neste mesmo JSP. Como
     * resultado, disponibiliza um List com os objetos selecionados na sessao da
     * pagina.
     */
    public void consultarColaborador() {
        try {
            super.consultar();
            List objs = new ArrayList();
            if (getCampoConsultaUsuario().equals("codigo")) {
                if (getValorConsultaUsuario().equals("")) {
                    setValorConsultaUsuario("0");
                }
                int valorInt = Integer.parseInt(getValorConsultaUsuario());
                objs = getFacade().getColaborador().consultarPorCodigo(new Integer(valorInt), getEmpresaLogado().getCodigo().intValue(), true, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            }
            if (getCampoConsultaUsuario().equals("nome")) {
                objs = getFacade().getColaborador().consultarPorNomePessoa(getValorConsultaUsuario(), getEmpresaLogado().getCodigo().intValue(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            }
            setListaConsultaUsuario(objs);
            setMensagemID("msg_dados_consultados");
            setSucesso(true);
            setErro(false);
        } catch (Exception e) {
            setListaConsulta(new ArrayList());
            setSucesso(false);
            setErro(true);
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public void selecionarColaborador() {
        selecionarColaboradorParametro(null);
    }

    public void selecionarColaboradorParametro(ColaboradorVO obj) {
        if (obj == null) {
            obj = (ColaboradorVO) context().getExternalContext().getRequestMap().get("colaborador");
            for (EmailVO email : obj.getPessoa().getEmailVOs()) {
                getUsuarioVO().setEmailVO(email);
                break;
            }
            getUsuarioVO().setColaboradorVO(obj);
            setCampoConsultaUsuario("");
            setValorConsultaUsuario("");
            setListaConsultaUsuario(new ArrayList());
        } else {
            for (EmailVO email : obj.getPessoa().getEmailVOs()) {
                getUsuarioVO().setEmailVO(email);
                break;
            }
            getUsuarioVO().setColaboradorVO(obj);
            setCampoConsultaUsuario("");
            setValorConsultaUsuario("");
            setListaConsultaUsuario(new ArrayList());
            getUsuarioVO().setTipoUsuario("CE");
        }
    }

    public void verificarTipoPessoa() throws Exception {
        if (getUsuarioVO().getTipoUsuario() == null || getUsuarioVO().getTipoUsuario().equals("")) {
            setCliente(false);
            setColaborador(false);
            setNovoColaborador(false);
        }
        if (getUsuarioVO().getTipoUsuario().equals("CI")) {
            setCliente(true);
            setColaborador(false);
            setNovoColaborador(false);
            getUsuarioVO().setClienteVO(new ClienteVO());
            setCampoConsultaUsuario("");
            setValorConsultaUsuario("");
            setListaConsultaUsuario(new ArrayList());

        }
        if (getUsuarioVO().getTipoUsuario().equals("CE")) {
            setCliente(false);
            setColaborador(true);
            setNovoColaborador(false);
            getUsuarioVO().setColaboradorVO(new ColaboradorVO());
            setCampoConsultaUsuario("");
            setValorConsultaUsuario("");
            setListaConsultaUsuario(new ArrayList());
        }
        if (getUsuarioVO().getTipoUsuario().equals("NC")) {
            setCliente(false);
            setColaborador(false);
            setNovoColaborador(true);
            getUsuarioVO().setColaboradorVO(new ColaboradorVO());
            getUsuarioVO().getColaboradorVO().setSituacao("AT");
            getUsuarioVO().setClienteVO(new ClienteVO());
            setCampoConsultaUsuario("");
            setValorConsultaUsuario("");
            setListaConsultaUsuario(new ArrayList());
            if (getEmpresaLogado().getCodigo().intValue() != 0) {
                getUsuarioVO().getColaboradorVO().getEmpresa().setCodigo(getEmpresaLogado().getCodigo().intValue());
                getUsuarioVO().getColaboradorVO().getEmpresa().setNome(getEmpresaLogado().getNome());
            }
        }

    }

    /**
     * Verifica se o {@link ColaboradorVO} vinculado do {@link UsuarioVO} é o
     * <code>AgendaVO.colaboradorResponsavel</code>. Caso sim, ele abre o modal
     * do vinculo de agenda.
     *
     * @throws Exception
     */
    public void verificarSituacaoColaborador() throws Exception {
        if (getUsuarioVO().getColaboradorVO() != null && getUsuarioVO().getColaboradorVO().getSituacao().equals("NA") && !UteisValidacao.emptyNumber(getUsuarioVO().getColaboradorVO().getCodigo())) {
            List<AgendaVO> agendas = getFacade().getAgenda().consultarPorColaboradorResponsavel(getUsuarioVO().getColaboradorVO().getCodigo(), Calendario.hoje(), Uteis.NIVELMONTARDADOS_MINIMOS);
            if (!agendas.isEmpty()) {
                VinculoAgendaControle vinculoAgendaControle = (VinculoAgendaControle) getControlador(VinculoAgendaControle.class);
                vinculoAgendaControle.carregarDadosVinculo(getUsuarioVO().getColaboradorVO(), agendas);
                vinculoAgendaControle.setarPropriedades("Agendamentos vinculados ao usuário " + getUsuarioVO().getNome(), "Para inativar o usuário escolha um novo responsável pelos agendamentos dos alunos abaixo:",
                        this, "acaoVinculoAgenda", "", "acaoVinculoAgenda", "", "form", "Selecione o novo responsável pelos agendamentos já cadastrados para esse usuário:");
                setOnComplete("Richfaces.showModalPanel('mdlVinculoAgenda');");
            }
        } else {
            setOnComplete("");
        }
    }

    /**
     * Ação executada quando o usuário clica em alguma operação do
     * {@link VinculoAgendaControle}
     */
    public void acaoVinculoAgenda() {
        VinculoAgendaControle vinculoAgendaControle = (VinculoAgendaControle) getControlador(VinculoAgendaControle.class);
        if (UteisValidacao.emptyNumber(vinculoAgendaControle.getUsuarioVOSelecionado())) {
            getUsuarioVO().getColaboradorVO().setSituacao("AT");
            atualizarAgenda = false;
            setMessageInInput("form:situacao", "É necessário selecionar um novo colaborador para poder inativar");
        } else {
            atualizarAgenda = true;
        }
        setOnComplete("");
    }

    public Boolean getMostraNome() {
        if (getUsuarioVO().getTipoUsuario() == null) {
            getUsuarioVO().setTipoUsuario("");
        }
        if (getUsuarioVO().getAdministrador().equals(new Boolean(true)) && getUsuarioVO().getTipoUsuario().equals("")) {
            return new Boolean(true);
        }
        return new Boolean(false);
    }

    public Boolean getMostraLista() {
        if (!getUsuarioVO().getAdministrador()) {
            return new Boolean(true);
        }
        return new Boolean(false);
    }

    public boolean isMostrarGrupoModuloNotas() throws Exception {
        if (getUsuarioLogado().getAdministrador()) {
            return true;
        }

        List<UsuarioPerfilAcessoVO> usuarioPerfilAcessoVOs = getUsuarioVO().getUsuarioPerfilAcessoVOs();
        if (!isPossuiPeloMenosUmaEmpresaHabilitadaModuloNotas(usuarioPerfilAcessoVOs)) {
            getUsuarioVO().setPermissaoAlterarRPS(false);
            return false;
        }

        return true;
    }

    private boolean isPossuiPeloMenosUmaEmpresaHabilitadaModuloNotas(List<UsuarioPerfilAcessoVO> listaUsuarioPerfilAcessoVO) {
        for (UsuarioPerfilAcessoVO perfilAcessoVO : listaUsuarioPerfilAcessoVO) {
            EmpresaVO empresa = perfilAcessoVO.getEmpresa();
            String chaveNFSe = empresa.getChaveNFSe();
            boolean isEmpresaModuloHabilitado = (empresa.getUsarNFSe() || empresa.isUsarNFCe()) && chaveNFSe != null && (chaveNFSe.length() > 0);

            if (isEmpresaModuloHabilitado) {
                return true;
            }
        }

        return false;
    }

    public Boolean getMostraTipoUsuario() {
        if ((!getUsuarioVO().getAdministrador()) && ((getUsuarioVO().getCodigo() == null) || (getUsuarioVO().getCodigo() == 0))) {
            return new Boolean(true);
        }
        return new Boolean(false);
    }

    public void montarDadosAdministrador() {
        if (!getUsuarioVO().getAdministrador()) {
            getUsuarioVO().setTipoUsuario("");
            return;
        }
        getUsuarioVO().setTipoUsuario("");
        getUsuarioVO().setClienteVO(new ClienteVO());
        getUsuarioVO().setColaboradorVO(new ColaboradorVO());
        setCliente(false);
        setColaborador(false);
        setNovoColaborador(false);

    }

    /**
     * Operação responsável por processar a exclusão um objeto da classe
     * <code>UsuarioVO</code> Após a exclusão ela automaticamente aciona a
     * rotina para uma nova inclusão.
     */
    public String excluir(boolean centralEventos) throws Exception {
        ConfiguracaoSistemaCRMVO c = getFacade().getConfiguracaoSistemaCRM().consultarConfiguracaoSistemaCRM(Uteis.NIVELMONTARDADOS_USUARIO);
        int remetenteCRM = c.getRemetentePadraoMailing().getCodigo();
        int usuarioExcluir = usuarioVO.getCodigo();
        if(remetenteCRM != usuarioExcluir){
            try {
                if (centralEventos) {
                    this.verificarAutorizacao();
                    getFacade().getUsuario().excluir(usuarioVO, true);
                    //registrar log
                    registrarLogExclusaoObjetoVO(usuarioVO, usuarioVO.getCodigo().intValue(), "USUARIO", 0);
                } else {
                    getFacade().getUsuario().excluir(usuarioVO);
                    incluirLogExclusao();

                    //remover do login
                    if (!UteisValidacao.emptyString(usuarioVO.getUsuarioGeral())) {
                        SincronizarUsuarioNovoLogin.desvincularUsuario(usuarioVO, Conexao.getFromSession(), getUsuarioLogado(), getEmpresaLogado().getCodigo(), getIpCliente(), getIntegracaoNovoLogin());
                    }
                }

                setUsuarioVO(new UsuarioVO());
                setUsuarioPerfilAcessoVO(new UsuarioPerfilAcessoVO());
                setCampoConsultaUsuario("");
                setValorConsultaUsuario("");
                setListaConsultaUsuario(new ArrayList());
                setMensagemID("msg_dados_excluidos");
                setSucesso(true);
                setErro(false);
                limparDados();
                return "consultar";
            } catch (Exception e) {
                if (e.getMessage().contains("ERRO: atualização ou exclusão em tabela \"usuario\" viola restrição de chave estrangeira") || e.getMessage().contains("ERROR: update or delete on table \"usuario\" violates foreign key")) {
                    setMensagemDetalhada("Este usuário não pode ser excluído, pois já foi utilizado!");
                } else {
                    setMensagemDetalhada("msg_erro", e.getMessage());
                }
                setSucesso(false);
                setErro(true);
                return "editar";
            }
        } else {
            setMensagemDetalhada("msg_erro", "Usuário definido como remetente padrão, favor alterar as configurações do CRM.");
            setSucesso(false);
            setErro(true);
            return "editar";
        }
    }

    /**
     * Responsável por chamar o método EXCLUIR indicando que não deve usar as
     * funcionalidades do CE
     *
     * @return
     * <AUTHOR> 23/03/2011
     */
    public String excluir() throws Exception {
        return excluir(false);

    }

    /**
     * Responsável por chamar o método EXCLUIR indicando que deve usar as
     * funcionalidades do CE
     *
     * @return
     * <AUTHOR> 23/03/2011
     */
    public String excluirCE() throws Exception {
        return excluir(true);

    }

    public void irPaginaInicial() throws Exception {
        controleConsulta.setPaginaAtual(1);
        this.consultar();
    }

    public void irPaginaAnterior() throws Exception {
        controleConsulta.setPaginaAtual(controleConsulta.getPaginaAtual() - 1);
        this.consultar();
    }

    public void irPaginaPosterior() throws Exception {
        controleConsulta.setPaginaAtual(controleConsulta.getPaginaAtual() + 1);
        this.consultar();
    }

    public void irPaginaFinal() throws Exception {
        controleConsulta.setPaginaAtual(controleConsulta.getNrTotalPaginas());
        this.consultar();


    }

    public void adicionarTodasEmpresasPerfilAcesso() throws Exception {
        notificarRecursoEmpresa(RecursoSistema.ADICIONAR_TODAS_EMPRESAS_CADASTRO_USUARIO);
        Integer pa = getUsuarioPerfilAcessoVO().getPerfilAcesso().getCodigo();
        for (Object o : listaSelectItemEmpresa) {
            Integer codigoEmpresa = (Integer) ((SelectItem) o).getValue();
            if (UteisValidacao.emptyNumber(codigoEmpresa)) {
                continue;
            }
            getUsuarioPerfilAcessoVO().getPerfilAcesso().setCodigo(pa);
            if (!adicionarUsuarioPerfilAcesso(codigoEmpresa)) {
                break;
            }
        }
    }

    public void adicionarUsuarioPerfilAcesso() throws Exception {
        Integer campoEmpresa = getUsuarioPerfilAcessoVO().getEmpresa().getCodigo();
        adicionarUsuarioPerfilAcesso(campoEmpresa);
    }

    public boolean adicionarUsuarioPerfilAcesso(Integer campoEmpresa) throws Exception {
        try {
            if (!getUsuarioVO().getCodigo().equals(new Integer(0))) {
                usuarioPerfilAcessoVO.setUsuario(getUsuarioVO().getCodigo());
            }
            if (campoEmpresa != 0) {

                EmpresaVO empresa = getFacade().getEmpresa().consultarPorChavePrimaria(campoEmpresa, Uteis.NIVELMONTARDADOS_TODOS);
                getUsuarioPerfilAcessoVO().setEmpresa(empresa);
            }
            if (getUsuarioPerfilAcessoVO().getPerfilAcesso().getCodigo() != 0) {
                Integer campoPerfilAcesso = getUsuarioPerfilAcessoVO().getPerfilAcesso().getCodigo();
                PerfilAcessoVO perfilAcesso = getFacade().getPerfilAcesso().consultarPorChavePrimaria(campoPerfilAcesso, Uteis.NIVELMONTARDADOS_TODOS);
                getUsuarioPerfilAcessoVO().setPerfilAcesso(perfilAcesso);
            }

            getUsuarioVO().adicionarObjUsuarioPerfilAcessoVOs(getUsuarioPerfilAcessoVO());
            this.setUsuarioPerfilAcessoVO(new UsuarioPerfilAcessoVO());
            setMensagemID("msg_dados_adicionados");
            setSucesso(true);
            setErro(false);
            return true;
        } catch (Exception e) {
            setSucesso(false);
            setErro(true);
            setMensagemDetalhada("msg_erro", e.getMessage());
            return false;
        }
    }

    public Object realizarConsultaLog() {
        LoginControle loginControle = (LoginControle) context().getExternalContext().getSessionMap().get("LoginControle");
        String nomeClasse = usuarioVO.getClass().getSimpleName();
        nomeClasse = nomeClasse.substring(0, nomeClasse.length() - 2);
        loginControle.setNomeClasse(loginControle.getNomeEntidadeCamposLog("prt_" + nomeClasse + "_tituloForm"));

        loginControle.consultarLogObjetoSelecionado(nomeClasse.toUpperCase(),
                usuarioVO.getCodigo(), usuarioVO.getColaboradorVO().getPessoa().getCodigo());
        return true;
    }

    public void realizarConsultaLogObjetoGeral() {
        usuarioVO = new UsuarioVO();
        realizarConsultaLog();
    }

    /* Método responsável por disponibilizar dados de um objeto da classe <code>EnderecoCobranca</code>
     * para edição pelo usuário.
     */
    public String editarUsuarioPerfilAcesso() throws Exception {
        UsuarioPerfilAcessoVO obj = (UsuarioPerfilAcessoVO) context().getExternalContext().getRequestMap().get("usuarioPerfilAcesso");
        setUsuarioPerfilAcessoVO(obj);
        setSucesso(true);
        setErro(false);
        return "editar";
    }


    /* Método responsável por remover um novo objeto da classe <code>EnderecoCobranca</code>
     * do objeto <code>clienteVO</code> da classe <code>Cliente</code>
     */
    public void removerUsuarioPerfilAcesso() throws Exception {
        try {
            setMsgAlert("");
            this.usuarioPerfilSelecionado = (UsuarioPerfilAcessoVO) context().getExternalContext().getRequestMap().get("usuarioPerfilAcesso");
            getUsuarioVO().getUsuarioPerfilAcessoVOs().remove(this.usuarioPerfilSelecionado);
            limparMsg();
        } catch (Exception e) {
            montarErro(e);
        }
    }


    public void removerTodosPerfisAcesso() throws Exception {
        try {
            setMsgAlert("");
            getUsuarioVO().setUsuarioPerfilAcessoVOs(new ArrayList());
            limparMsg();
        } catch (Exception e) {
            montarErro(e);
        }
    }

    public void removerVinculoColaborador() {
        ColaboradorControle control = (ColaboradorControle) getControlador(ColaboradorControle.class);
        try {
            control.removerVinculosParaInativar();
            gravarSimples(false);
        } catch (Exception e) {
            montarErro(e);
        }
    }

    public void transferirVinculosColaborador() {
        try {
            ColaboradorControle control = (ColaboradorControle) getControlador(ColaboradorControle.class);
            control.transferirVinculosParaInativar();
            gravarSimples(false);
        } catch (Exception e) {
            montarErro(e);
        }
    }

    private boolean informarNovoVinculoParaClientes(UsuarioPerfilAcessoVO usuarioPerfilAcessoVO) throws Exception {
        if (UtilReflection.objetoMaiorQueZero(getUsuarioVO(), "getColaboradorVO().getCodigo()")) {
            ColaboradorVO colaboradorVO = getFacade().getColaborador().consultarPorCodigoPessoa(getUsuarioVO().getColaboradorVO().getPessoa().getCodigo(), usuarioPerfilAcessoVO.getEmpresa().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            return validarVinculosColaborador(colaboradorVO, "USUÁRIO - REMOVER ACESSO EMPRESA");
        }
        return false;
    }

    private boolean validarVinculosColaborador(ColaboradorVO colaboradorVO, String origem) throws Exception {
        ColaboradorControle control = (ColaboradorControle) getControlador(ColaboradorControle.class);
        if (control == null) {
            control = new ColaboradorControle();
        }
        if (control.existeVinculosColaborador(colaboradorVO, origem)) {
            setMsgAlert("Richfaces.showModalPanel('modalVinculoCliente');");
            return true;
        } else {
            setMsgAlert("Richfaces.hideModalPanel('modalVinculoCliente');");
        }
        return false;
    }

    /* Método responsável por inicializar List<SelectItem> de valores do
     * ComboBox correspondente ao atributo <code>situacao</code>
     */
    public List getListaSelectItemTipoUsuario() throws Exception {
        List objs = new ArrayList();
        objs.add(new SelectItem("", ""));
        Hashtable situacaoClientes = (Hashtable) Dominios.getTipoPessoa();
        Enumeration keys = situacaoClientes.keys();
        while (keys.hasMoreElements()) {
            String value = (String) keys.nextElement();
            String label = (String) situacaoClientes.get(value);
            objs.add(new SelectItem(value, label));
        }

        SelectItemOrdemValor ordenador = new SelectItemOrdemValor();
        Collections.sort((List) objs, ordenador);
        return objs;
    }

    /**
     * Método responsável por gerar uma lista de objetos do tipo
     * <code>SelectItem</code> para preencher o comboBox relativo ao atributo
     * <code>CodPerfilAcesso</code>.
     */
    public void montarListaSelectItemCodPerfilAcesso(String prm) throws Exception {
        List resultadoConsulta = consultarPerfilAcessoPorNome(prm);
        Iterator i = resultadoConsulta.iterator();
        List objs = new ArrayList();
        objs.add(new SelectItem(new Integer(0), ""));
        while (i.hasNext()) {
            PerfilAcessoVO obj = (PerfilAcessoVO) i.next();
            objs.add(new SelectItem(obj.getCodigo(), obj.getNome().toString()));
        }
        setListaSelectItemCodPerfilAcesso(objs);
    }

    public void montarListaSelectItemCodPerfilAcessoTreino() {
        try {
            if (getUsuarioVO() != null && getUsuarioVO().getPerfilTw() != null) {
                setCodPerfilTw(getUsuarioVO().getPerfilTw());
            }
            List<PerfilTreinoResponseTO> perfisTreino = buscarPerfisTreino();
            Iterator i = perfisTreino.iterator();
            List objs = new ArrayList();
            objs.add(new SelectItem(null, ""));
            while (i.hasNext()) {
                PerfilTreinoResponseTO obj = (PerfilTreinoResponseTO) i.next();
                objs.add(new SelectItem(obj.getId(), obj.getNome().toString()));
            }
            setListaSelectItemCodPerfilAcessoTreino(objs);
        } catch (Exception ex) {
            List objs = new ArrayList();
            objs.add(new SelectItem(null, ""));
            setListaSelectItemCodPerfilAcessoTreino(objs);
        }
    }

    /**
     * Método responsável por atualizar o ComboBox relativo ao atributo
     * <code>CodPerfilAcesso</code>. Buscando todos os objetos correspondentes a
     * entidade
     * <code>PerfilAcesso</code>. Esta rotina não recebe parâmetros para
     * filtragem de dados, isto é importante para a inicialização dos dados da
     * tela para o acionamento por meio requisições Ajax.
     */
    public void montarListaSelectItemCodPerfilAcesso() {
        try {
            montarListaSelectItemCodPerfilAcesso("");
            //montarListaSelectItemEmpresa("");
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * Método responsável por consultar dados da entidade
     * <code><code> e montar o atributo
     * <code>nome</code> Este atributo é uma lista (
     * <code>List</code>) utilizada para definir os valores a serem apresentados
     * no ComboBox correspondente
     */
    public List consultarPerfilAcessoPorNome(String nomePrm) throws Exception {
        List lista = getFacade().getPerfilAcesso().consultarPorNome(nomePrm, false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        return lista;
    }

    /**
     * Método responsável por gerar uma lista de objetos do tipo
     * <code>SelectItem</code> para preencher o comboBox relativo ao atributo
     * <code>CodPerfilAcesso</code>.
     */
    public void montarListaSelectItemEmpresa(String prm, boolean temPemissaoConsultarTodas) throws Exception {
        List resultadoConsulta = consultarEmpresaPorNome(prm);
        Iterator i = resultadoConsulta.iterator();
        List objs = new ArrayList();
        objs.add(new SelectItem(0, temPemissaoConsultarTodas ? "TODAS EMPRESAS" : ""));
        if (ControleAcesso.isPermiteMultiEmpresas() || getUsuarioLogado().getAdministrador() || temPemissaoConsultarTodas) {
            while (i.hasNext()) {
                EmpresaVO obj = (EmpresaVO) i.next();
                objs.add(new SelectItem(obj.getCodigo(), obj.getNome().toString()));
            }
        } else {
            int empLogado = getEmpresaLogado().getCodigo();
            while (i.hasNext()) {
                EmpresaVO obj = (EmpresaVO) i.next();
                if (empLogado == obj.getCodigo().intValue()) {
                    objs.add(new SelectItem(obj.getCodigo(), obj.getNome().toString()));
                }
            }
        }
        setListaSelectItemEmpresa(objs);
    }

    /**
     * Método responsável por atualizar o ComboBox relativo ao atributo
     * <code>CodPerfilAcesso</code>. Buscando todos os objetos correspondentes a
     * entidade
     * <code>PerfilAcesso</code>. Esta rotina não recebe parâmetros para
     * filtragem de dados, isto é importante para a inicialização dos dados da
     * tela para o acionamento por meio requisições Ajax.
     */
    public void montarListaSelectItemEmpresa() {
        try {
            montarListaSelectItemEmpresa("", false);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * Método responsável por consultar dados da entidade
     * <code><code> e montar o atributo
     * <code>nome</code> Este atributo é uma lista (
     * <code>List</code>) utilizada para definir os valores a serem apresentados
     * no ComboBox correspondente
     */
    public List consultarEmpresaPorNome(String nomePrm) throws Exception {
        return getFacade().getEmpresa().consultarPorNome(nomePrm, true, false, Uteis.NIVELMONTARDADOS_MINIMOS);
    }

    /**
     * Método responsável por inicializar a lista de valores (
     * <code>SelectItem</code>) para todos os ComboBox's.
     */
    public void inicializarListasSelectItemTodosComboBox() {
        montarListaSelectItemCodPerfilAcesso();
        montarListaSelectItemEmpresa();
        montarListaSelectItemCodPerfilAcessoTreino();
    }

    /**
     * Rotina responsável por preencher a combo de consulta da telas.
     */
    public List getTipoConsultaCombo() {
        List itens = new ArrayList();
        itens.add(new SelectItem("nome", "Nome da Pessoa"));
        itens.add(new SelectItem("username", "Nome do Usuário"));
        itens.add(new SelectItem("codigo", "Código"));
        itens.add(new SelectItem("nomePerfilAcesso", "Perfil de Acesso"));
        itens.add(new SelectItem("situacaoColaborador", "Situação Colaborador"));
        return itens;
    }

    public List getTipoConsultaSituacaoColaborador() {
        List itens = new ArrayList();
        itens.add(new SelectItem("AT", "Ativo"));
        itens.add(new SelectItem("NA", "Inativo"));
        return itens;
    }

    public List getTipoConsultaComboUsuario() {
        List itens = new ArrayList();
        itens.add(new SelectItem("nome", "Nome"));
        itens.add(new SelectItem("codigo", "Código"));
        return itens;
    }

    /**
     * Rotina responsável por organizar a paginação entre as páginas resultantes
     * de uma consulta.
     */
    public String inicializarConsultar() {
        setSucesso(false);
        setErro(false);
        setPaginaAtualDeTodas("0/0");
        setListaConsulta(new ArrayList());
        //definirVisibilidadeLinksNavegacao(0, 0);
        setMensagemID("msg_entre_prmconsulta");
        inicializarPermissaoTodasEmpreas();
        return "consultar";
    }

    /**
     * Operação que inicializa as Interfaces Façades com os respectivos objetos
     * de persistência dos dados no banco de dados.
     */
    protected boolean inicializarFacades() {
        try {
            super.inicializarFacades();

            return true;
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro_conectarBD", e.getMessage());
            return false;
        }
    }

    public List getListaSelectItemCodPerfilAcesso() {
        return (listaSelectItemCodPerfilAcesso);
    }

    public void setListaSelectItemCodPerfilAcesso(List listaSelectItemCodPerfilAcesso) {
        this.listaSelectItemCodPerfilAcesso = listaSelectItemCodPerfilAcesso;
    }

    public List<PerfilTreinoResponseTO> getListaSelectItemCodPerfilAcessoTreino() {
        return listaSelectItemCodPerfilAcessoTreino;
    }

    public void setListaSelectItemCodPerfilAcessoTreino(List<PerfilTreinoResponseTO> listaSelectItemCodPerfilAcessoTreino) {
        this.listaSelectItemCodPerfilAcessoTreino = listaSelectItemCodPerfilAcessoTreino;
    }

    public UsuarioVO getUsuarioVO() {
        return usuarioVO;
    }

    public void setUsuarioVO(UsuarioVO usuarioVO) {
        this.usuarioVO = usuarioVO;
    }

    public List getListaSelectItemEmpresa() {
        if (listaSelectItemEmpresa == null) {
            listaSelectItemEmpresa = new ArrayList();
        }
        return listaSelectItemEmpresa;
    }

    public void setListaSelectItemEmpresa(List listaSelectItemEmpresa) {
        this.listaSelectItemEmpresa = listaSelectItemEmpresa;
    }

    public UsuarioPerfilAcessoVO getUsuarioPerfilAcessoVO() {
        return usuarioPerfilAcessoVO;
    }

    public void setUsuarioPerfilAcessoVO(UsuarioPerfilAcessoVO usuarioPerfilAcessoVO) {
        this.usuarioPerfilAcessoVO = usuarioPerfilAcessoVO;
    }

    public Boolean getCliente() {
        return cliente;
    }

    public void setCliente(Boolean cliente) {
        this.cliente = cliente;
    }

    public Boolean getColaborador() {
        return colaborador;
    }

    public void setColaborador(Boolean colaborador) {
        this.colaborador = colaborador;
    }

    public String getCampoConsultaUsuario() {
        return campoConsultaUsuario;
    }

    public void setCampoConsultaUsuario(String campoConsultaUsuario) {
        this.campoConsultaUsuario = campoConsultaUsuario;
    }

    public List getListaConsultaUsuario() {
        return listaConsultaUsuario;
    }

    public void setListaConsultaUsuario(List listaConsultaUsuario) {
        this.listaConsultaUsuario = listaConsultaUsuario;
    }

    public String getValorConsultaUsuario() {
        return valorConsultaUsuario;
    }

    public void setValorConsultaUsuario(String valorConsultaUsuario) {
        this.valorConsultaUsuario = valorConsultaUsuario;
    }

    public Boolean getNovoColaborador() {
        return novoColaborador;
    }

    public void setNovoColaborador(Boolean novoColaborador) {
        this.novoColaborador = novoColaborador;
    }

    public Boolean getApresentarCampoSenhaUsuario() {
        return apresentarCampoSenhaUsuario;
    }

    public void setApresentarCampoSenhaUsuario(Boolean apresentarCampoSenhaUsuario) {
        this.apresentarCampoSenhaUsuario = apresentarCampoSenhaUsuario;
    }

    public void setApresentarComboSituacaoColaborador(Boolean apresentarComboSituacaoColaborador) {
        this.apresentarComboSituacaoColaborador = apresentarComboSituacaoColaborador;
    }

    public Boolean getApresentarComboSituacaoColaborador() {
        return apresentarComboSituacaoColaborador;
    }

    public void habilitarComboSituacaoColaborador() {
        if (getControleConsulta().getCampoConsulta().equals("situacaoColaborador")) {
            apresentarComboSituacaoColaborador = true;
        } else {
            apresentarComboSituacaoColaborador = false;
        }
    }

    public void setSituacaoColaboradorConsulta(String situacaoColaboradorConsulta) {
        this.situacaoColaboradorConsulta = situacaoColaboradorConsulta;
    }

    public String getSituacaoColaboradorConsulta() {
        return situacaoColaboradorConsulta;
    }

    /**
     * @return the horarioAcessoSistemaVO
     */
    public HorarioAcessoSistemaVO getHorarioAcessoSistemaVO() {
        return horarioAcessoSistemaVO;
    }

    /**
     * @param horarioAcessoSistemaVO the horarioAcessoSistemaVO to set
     */
    public void setHorarioAcessoSistemaVO(HorarioAcessoSistemaVO horarioAcessoSistemaVO) {
        this.horarioAcessoSistemaVO = horarioAcessoSistemaVO;
    }

    /**
     * @return the codigoDiaSemana
     */
    public String getCodigoDiaSemana() {
        if (codigoDiaSemana == null) {
            codigoDiaSemana = "";
        }
        return codigoDiaSemana;
    }

    /**
     * @param codigoDiaSemana the codigoDiaSemana to set
     */
    public void setCodigoDiaSemana(String codigoDiaSemana) {
        this.codigoDiaSemana = codigoDiaSemana;
    }

    /**
     * @return the listaHorariosAcessoSistema
     */
    public Map<String, List<HorarioAcessoSistemaVO>> getMapHorariosAcessoSistema() {
        return mapHorariosAcessoSistema;
    }

    /**
     * @param mapHorariosAcessoSistema the listaHorariosAcessoSistema to set
     */
    public void setMapHorariosAcessoSistema(Map<String, List<HorarioAcessoSistemaVO>> mapHorariosAcessoSistema) {
        this.mapHorariosAcessoSistema = mapHorariosAcessoSistema;
    }

    /**
     * @return the listaDomingo
     */
    public List<HorarioAcessoSistemaVO> getListaDomingo() {
        return listaDomingo;
    }

    /**
     * @param listaDomingo the listaDomingo to set
     */
    public void setListaDomingo(List<HorarioAcessoSistemaVO> listaDomingo) {
        this.listaDomingo = listaDomingo;
    }

    /**
     * @return the listaSegunda
     */
    public List<HorarioAcessoSistemaVO> getListaSegunda() {
        return listaSegunda;
    }

    /**
     * @param listaSegunda the listaSegunda to set
     */
    public void setListaSegunda(List<HorarioAcessoSistemaVO> listaSegunda) {
        this.listaSegunda = listaSegunda;
    }

    /**
     * @return the listaTerca
     */
    public List<HorarioAcessoSistemaVO> getListaTerca() {
        return listaTerca;
    }

    /**
     * @param listaTerca the listaTerca to set
     */
    public void setListaTerca(List<HorarioAcessoSistemaVO> listaTerca) {
        this.listaTerca = listaTerca;
    }

    /**
     * @return the listaQuarta
     */
    public List<HorarioAcessoSistemaVO> getListaQuarta() {
        return listaQuarta;
    }

    /**
     * @param listaQuarta the listaQuarta to set
     */
    public void setListaQuarta(List<HorarioAcessoSistemaVO> listaQuarta) {
        this.listaQuarta = listaQuarta;
    }

    /**
     * @return the listaQuinta
     */
    public List<HorarioAcessoSistemaVO> getListaQuinta() {
        return listaQuinta;
    }

    /**
     * @param listaQuinta the listaQuinta to set
     */
    public void setListaQuinta(List<HorarioAcessoSistemaVO> listaQuinta) {
        this.listaQuinta = listaQuinta;
    }

    /**
     * @return the listaSexta
     */
    public List<HorarioAcessoSistemaVO> getListaSexta() {
        return listaSexta;
    }

    /**
     * @param listaSexta the listaSexta to set
     */
    public void setListaSexta(List<HorarioAcessoSistemaVO> listaSexta) {
        this.listaSexta = listaSexta;
    }

    /**
     * @return the listaSabado
     */
    public List<HorarioAcessoSistemaVO> getListaSabado() {
        return listaSabado;
    }

    /**
     * @param listaSabado the listaSabado to set
     */
    public void setListaSabado(List<HorarioAcessoSistemaVO> listaSabado) {
        this.listaSabado = listaSabado;
    }

    /**
     * @return the listaDiasSemanaEscolhidos
     */
    public List<String> getListaDiasSemanaEscolhidos() {
        return listaDiasSemanaEscolhidos;
    }

    /**
     * @param listaDiasSemanaEscolhidos the listaDiasSemanaEscolhidos to set
     */
    public void setListaDiasSemanaEscolhidos(List<String> listaDiasSemanaEscolhidos) {
        this.listaDiasSemanaEscolhidos = listaDiasSemanaEscolhidos;
    }

    public boolean isPermiteMultiEmpresas() {
        return ControleAcesso.isPermiteMultiEmpresas();
    }

    public boolean isPesquisar() {
        return pesquisar;
    }

    public void setPesquisar(boolean pesquisar) {
        this.pesquisar = pesquisar;
    }

    public void setSenhaAntesDaAlteracao(String senhaAntesDaAlteracao) {
        this.senhaAntesDaAlteracao = senhaAntesDaAlteracao;
    }

    public String getSenhaAntesDaAlteracao() {
        return senhaAntesDaAlteracao;
    }

    public String getPinAntesDaAlteracao() {
        return pinAntesDaAlteracao;
    }

    public void setPinAntesDaAlteracao(String pinAntesDaAlteracao) {
        this.pinAntesDaAlteracao = pinAntesDaAlteracao;
    }

    public void exportar(ActionEvent evt) throws Exception {
        ExportadorListaControle exportadorListaControle = (ExportadorListaControle) JSFUtilities.getFromRequest(ExportadorListaControle.class.getSimpleName());
        String paramsTableFiltrada = context().getExternalContext().getRequestParameterMap().get("paramsTableFiltrada");

        String[] split = paramsTableFiltrada.split(",");
        String campoOrdenacao = split[0].replace("[", "");
        String ordem = split[1];
        String filtro = split[2].replace("''", "");
        filtro = filtro.replace("]", "");
        List listaParaImpressao = getFacade().getUsuario().consultarParaImpressao(filtro, ordem, campoOrdenacao, getEmpresaLogado() == null ? 0 : getEmpresaLogado().getCodigo(), getSituacaoFiltro());
        exportadorListaControle.exportar(evt, listaParaImpressao, filtro, null);

    }

    public void registraLogInclusaoUsuario() throws Exception {
        try {

            LogVO obj = new LogVO();
            obj.setChavePrimaria(usuarioVO.getCodigo().toString());
            obj.setNomeEntidade("USUARIO");
            obj.setNomeEntidadeDescricao("Usuário - UsuarioVO");
            obj.setOperacao("INCLUSÃO");
            obj.setResponsavelAlteracao(getUsuarioLogado().getNome());
            obj.setUserOAMD(getUsuarioLogado().getUserOamd());
            obj.setNomeCampo("MENSAGEM");
            obj.setValorCampoAlterado("");

            //INFORMAÇÕES DO CADASTRO DO USUÁRIO
            String str = "\nCódigo Usuário: " + usuarioVO.getCodigo().toString() + "\n";
            str += "Tipo Colaborador: " + usuarioVO.getTipoUsuario_Apresentar() + "\n";
            str += "Situação: " + usuarioVO.getSituacao_Usuario() + "\n";
            str += "Nome Colaborador: " + usuarioVO.getColaboradorVO().getPessoa().getNome() + "\n";
            str += "Data Nascimento: " + usuarioVO.getColaboradorVO().getPessoa_DataNascimento() + "\n";
            str += "CPF: " + usuarioVO.getColaboradorVO().getPessoa().getCfp() + "\n";
            str += "Nome Usuário: " + usuarioVO.getUsername() + "\n";
            str += "Usuário Service: " + usuarioVO.getServiceUsuario() + "\n";
            str += "Data Alteração Senha: " + usuarioVO.getDataUltimaAlteracaoSenha_Apresentar() + "\n";
            str += "Permite Alterar Própria Senha: " + usuarioVO.getPermiteAlterarPropriaSenha() + "\n";

            //INFORMAÇÕES DO PERFIL DE ACESSO DO USUÁRIO
            List<UsuarioPerfilAcessoVO> usuarioPerfilAcessoAtu = getFacade().getUsuarioPerfilAcesso().consultarUsuarioPerfilAcesso(usuarioVO.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);

            str += "PERFIL DE ACESSO: \n";
            Iterator a = usuarioPerfilAcessoAtu.iterator();
            while (a.hasNext()) {
                UsuarioPerfilAcessoVO objetos = (UsuarioPerfilAcessoVO) a.next();
                str += "Empresa: " + objetos.getEmpresa().getCodigo().toString() + " - " + objetos.getEmpresa().getNome() + " Perfil: " + objetos.getPerfilAcesso().getCodigo().toString() + " - " + objetos.getPerfilAcesso().getNome() + "\n";
            }

            //INFORMAÇÕES DO HORARIO DE ACESSO AO SISTEMA DO USUÁRIO
            List<HorarioAcessoSistemaVO> usuarioHorarioAcesso = getFacade().getHorarioAcessoSistema().consultar(usuarioVO.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);

            str += "HORARIO DE ACESSO AO SISTEMA: \n";
            Iterator h = usuarioHorarioAcesso.iterator();
            while (h.hasNext()) {
                HorarioAcessoSistemaVO objetos = (HorarioAcessoSistemaVO) h.next();
                str += objetos.getDiaSemana() + "  " + objetos.getHoraInicial() + " - " + objetos.getHoraFinal() + "\n";
            }

            obj.setValorCampoAlterado(str);
            obj.setDataAlteracao(Calendario.hoje());
            registrarLogObjetoVO(obj, usuarioVO.getCodigo());
        } catch (Exception e) {
            registrarLogErroObjetoVO("USUARIO", usuarioVO.getColaboradorVO().getPessoa().getCodigo(), "ERRO AO GERAR LOG DE ALTERAÇÃO DE USUARIO", this.getUsuarioLogado().getNome(), this.getUsuarioLogado().getUserOamd());
            e.printStackTrace();
        }
    }

    public boolean registraLogAlteracaoUsuario(List<UsuarioPerfilAcessoVO> perfilAcessoUsuarioAnt, List<HorarioAcessoSistemaVO> horariosAcessoUsuarioAnt) throws Exception {
        boolean alterouPerfil = false;
        try {
            LogVO obj = new LogVO();
            obj.setChavePrimaria(usuarioVO.getCodigo().toString());
            obj.setNomeEntidade("USUARIO");
            obj.setNomeEntidadeDescricao("Usuário - UsuarioVO");
            obj.setOperacao("ALTERAÇÃO");
            obj.setResponsavelAlteracao(getUsuarioLogado().getNome());
            obj.setUserOAMD(getUsuarioLogado().getUserOamd());
            obj.setNomeCampo("MENSAGEM");
            obj.setValorCampoAlterado("");
            obj.setValorCampoAnterior("");

            //REGISTRANDO OS PERFIS DE ACESSO
            List<UsuarioPerfilAcessoVO> perfilAcessoUsuarioAtu = getFacade().getUsuarioPerfilAcesso().consultarUsuarioPerfilAcesso(usuarioVO.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);

            List<String> perfilAlterados = new ArrayList<String>();
            List<String> perfilExcluidos = new ArrayList<String>();
            List<String> perfilAdicionados = new ArrayList<String>();

            for (UsuarioPerfilAcessoVO perfilAnt : perfilAcessoUsuarioAnt) {
                boolean existe = false;

                for (UsuarioPerfilAcessoVO perfil : perfilAcessoUsuarioAtu) {
                    if (perfilAnt.getEmpresa().getCodigo().equals(perfil.getEmpresa().getCodigo())) {
                        existe = true;
                        perfil.setVerificado(true);
                        if (!perfilAnt.getPerfilAcesso().getCodigo().equals(perfil.getPerfilAcesso().getCodigo())) {
                            perfilAlterados.add(perfil.getEmpresa().getNome() + " -- " + perfil.getPerfilAcesso().getNome());
                            break;
                        }
                    }
                }

                if (!existe) {
                    perfilExcluidos.add(perfilAnt.getEmpresa().getNome() + " -- " + perfilAnt.getPerfilAcesso().getNome());
                }
            }

            for (UsuarioPerfilAcessoVO perfilAtu : perfilAcessoUsuarioAtu) {
                if (!perfilAtu.isVerificado()) {
                    perfilAdicionados.add(perfilAtu.getEmpresa() + " -- " + perfilAtu.getPerfilAcesso().getNome());
                }
            }

            String str = "";

            String alteradas = listaAlteracaoLog(perfilAlterados);
            String excluidas = listaAlteracaoLog(perfilExcluidos);
            String adicionadas = listaAlteracaoLog(perfilAdicionados);

            if (!alteradas.isEmpty()) {
                str += "PERFIS ALTERADOS: \n" + alteradas + "\n";
                alterouPerfil = true;
            }
            if (!excluidas.isEmpty()) {
                str += "PERFIS EXCLUIDOS: \n" + excluidas + "\n";
                alterouPerfil = true;
            }
            if (!adicionadas.isEmpty()) {
                str += "PERFIS ADICIONADOS: \n" + adicionadas + "\n";
                alterouPerfil = true;
            }


            //REGISTRANDO OS HORARIOS DE ACESSO AO SISTEMA

            List<HorarioAcessoSistemaVO> horariosAcessoUsuarioAtu = getFacade().getHorarioAcessoSistema().consultar(usuarioVO.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);

            List<String> listaHorariosAlterados = new ArrayList<String>();
            List<String> listaHorariosExcluidos = new ArrayList<String>();
            List<String> listaHorariosAdicionados = new ArrayList<String>();

            for (HorarioAcessoSistemaVO horarioAnt : horariosAcessoUsuarioAnt) {
                boolean existe = false;

                for (HorarioAcessoSistemaVO horario : horariosAcessoUsuarioAtu) {
                    if (horarioAnt.getDiaSemana().equals(horario.getDiaSemana())) {
                        existe = true;
                        horario.setVerificado(true);
                        if (!horarioAnt.getHoraInicial().equals(horario.getHoraInicial()) || !horarioAnt.getHoraFinal().equals(horario.getHoraFinal())) {
                            listaHorariosAlterados.add(horario.getDiaSemana().toString() + "  " + horario.getHoraInicial() + " - " + horario.getHoraFinal());
                            break;
                        }
                    }
                }

                if (!existe) {
                    listaHorariosExcluidos.add(horarioAnt.getDiaSemana().toString() + "  " + horarioAnt.getHoraInicial() + " - " + horarioAnt.getHoraFinal());
                }
            }

            for (HorarioAcessoSistemaVO horarioAtu : horariosAcessoUsuarioAtu) {
                if (!horarioAtu.isVerificado()) {
                    listaHorariosAdicionados.add(horarioAtu.getDiaSemana().toString() + "  " + horarioAtu.getHoraInicial() + " - " + horarioAtu.getHoraFinal());
                }
            }

            String horariosAlterados = listaAlteracaoLog(listaHorariosAlterados);
            String horariosExcluidos = listaAlteracaoLog(listaHorariosExcluidos);
            String horariosAdicionados = listaAlteracaoLog(listaHorariosAdicionados);

            if (!horariosAlterados.isEmpty()) {
                str += "HORÁRIOS ALTERADOS: \n" + horariosAlterados + "\n";
            }
            if (!horariosExcluidos.isEmpty()) {
                str += "HORÁRIOS EXCLUIDOS: \n" + horariosExcluidos + "\n";
            }
            if (!horariosAdicionados.isEmpty()) {
                str += "HORÁRIOS ADICIONADOS: \n" + horariosAdicionados + "\n";
            }


            //REGISTRANDO OS VALORES ANTERIORES
            List<String> perfilAcessoAnterior = new ArrayList<String>();
            List<String> horarioAcesoAnterior = new ArrayList<String>();

            String strAnt = "";

            for (UsuarioPerfilAcessoVO perfilAnterior : perfilAcessoUsuarioAnt) {
                perfilAcessoAnterior.add(perfilAnterior.getEmpresa().getNome() + " -- " + perfilAnterior.getPerfilAcesso().getNome());
            }
            for (HorarioAcessoSistemaVO horarioAnterior : horariosAcessoUsuarioAnt) {
                horarioAcesoAnterior.add(horarioAnterior.getDiaSemana().toString() + "  " + horarioAnterior.getHoraInicial() + " - " + horarioAnterior.getHoraFinal());
            }

            String perfilAnterior = listaAlteracaoLog(perfilAcessoAnterior);
            String horarioAnterior = listaAlteracaoLog(horarioAcesoAnterior);

            if (!perfilAnterior.isEmpty() && str.contains("PERFIS")) {
                strAnt += "PERFIS ANTERIORES: \n" + perfilAnterior + "\n";
            }
            if (!horarioAnterior.isEmpty() && str.contains("HORÁRIOS")) {
                strAnt += "HORARIOS ANTERIORES: \n" + horarioAnterior + "\n";
            }

            obj.setValorCampoAnterior(strAnt);
            obj.setDataAlteracao(Calendario.hoje());
            obj.setValorCampoAlterado(str);
            if (!str.isEmpty()) {
                registrarLogObjetoVO(obj, usuarioVO.getColaboradorVO().getPessoa().getCodigo());
            }
        } catch (Exception e) {
            registrarLogErroObjetoVO("USUARIO", usuarioVO.getCodigo(), "ERRO AO GERAR LOG DE ALTERAÇÃO DE USUARIO", this.getUsuarioLogado().getNome(), this.getUsuarioLogado().getUserOamd());
            e.printStackTrace();
        }
        return alterouPerfil;
    }

    public String listaAlteracaoLog(List<String> lista) throws Exception {
        StringBuilder str = new StringBuilder("");

        for (String mensagem : lista) {
            str.append(mensagem).append("\n");
        }
        return str.toString();
    }

    public TipoColaboradorVO getTipoColaboradorVO() {
        if (tipoColaboradorVO == null) {
            tipoColaboradorVO = new TipoColaboradorVO();
        }
        return tipoColaboradorVO;
    }

    public void setTipoColaboradorVO(TipoColaboradorVO tipoColaboradorVO) {
        this.tipoColaboradorVO = tipoColaboradorVO;
    }

    public String getOnComplete() {
        if (onComplete == null) {
            onComplete = "";
        }
        return onComplete;
    }

    public void setOnComplete(String onComplete) {
        this.onComplete = onComplete;
    }

    public List<SelectItem> getListaSelectItemTipoColaborador() throws Exception {
        List<SelectItem> objs = new ArrayList<SelectItem>();
        objs.add(new SelectItem("", ""));
        for (TipoColaboradorEnum tipo : TipoColaboradorEnum.values()) {
            objs.add(new SelectItem(tipo.getSigla(), tipo.getDescricao()));
        }
        SelectItemOrdemValor ordenador = new SelectItemOrdemValor();
        Collections.sort((List) objs, ordenador);
        return objs;
    }

    public void adicionarTipoColaborador() throws Exception {
        try {
            if (!getUsuarioVO().getColaboradorVO().getCodigo().equals(new Integer(0))) {
                getUsuarioVO().getColaboradorVO().setCodigo(getUsuarioVO().getColaboradorVO().getCodigo());
            }
            getUsuarioVO().getColaboradorVO().adicionarObjTipoColaboradorVOs(getTipoColaboradorVO());
            this.setTipoColaboradorVO(new TipoColaboradorVO());
            setMensagemID("msg_dados_adicionados");
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public void removerTipoColaborador() throws Exception {
        try {
            if (!getTipoColaboradorVORemover().getColaborador().equals(0) && getFacade().getVinculo().existeVinculoCodigoColaboradorTipoVinculo(getUsuarioVO().getColaboradorVO().getCodigo(), getTipoColaboradorVORemover().getDescricao())) {
                throw new ConsistirException("Colaborador possui vinculos do Tipo " + getTipoColaboradorVORemover().getDescricao_Apresentar() + ". Acesse o Organizador de Carteiras e retire os vinculos desse tipo antes de fazer essa operação.");
            }
            getUsuarioVO().getColaboradorVO().excluirObjTipoColaboradorVOs(getTipoColaboradorVORemover().getDescricao());
            setMensagemID("msg_dados_excluidos");
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public void adicionarEmail() throws Exception {
        try {
            EmailVO emailVO = new EmailVO();
            emailVO.setEmail(this.getUsuarioEmailVO().getEmail());
            if (!UteisValidacao.emptyNumber(this.getUsuarioVO().getColaboradorVO().getPessoa().getCodigo())) {
                emailVO.setPessoa(this.getUsuarioVO().getColaboradorVO().getPessoa().getCodigo());
            }
            boolean existeEmail = getFacade().getEmail().consultarEmailExiste(emailVO.getEmail(), emailVO.getPessoa());
            if (!existeEmail) {
                getUsuarioVO().getColaboradorVO().getPessoa().adicionarObjEmailVOs(emailVO);
            }
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }

    }

    public void recarregarFoto() throws Exception {
        if (isFotosNaNuvem()) {
            final String fotoKey = getFacade().getPessoa().obterFotoKey(
                    getUsuarioVO().getColaboradorVO().getPessoa().getCodigo());
            if (!UteisValidacao.emptyString(fotoKey)) {
                getUsuarioVO().getColaboradorVO().getPessoa().setFotoKey(fotoKey + "?time=" + getTimeStamp());
            }
        } else {
            getUsuarioVO().getColaboradorVO().getPessoa().setFoto(getFacade().getPessoa().obterFoto(
                    getKey(),
                    getUsuarioVO().getColaboradorVO().getPessoa().getCodigo()));
        }
    }

    public void paintFoto(OutputStream out, Object data) throws IOException, Exception {
        if (getUsuarioVO().getColaboradorVO().getPessoa().getFoto() == null || getUsuarioVO().getColaboradorVO().getPessoa().getFoto().length == 0) {
            recarregarFoto();
        }
        SuperControle.paintFoto(out, getUsuarioVO().getColaboradorVO().getPessoa().getFoto());
    }

    public String getPaintFotoDaNuvem() {
        return getPaintFotoDaNuvem(getUsuarioVO().getColaboradorVO().getPessoa().getFotoKey());
    }

    public void verificaColaborador() {
//        if (getUsuarioVO().getColaboradorVO().getPessoa().getCodigo() == null || getUsuarioVO().getColaboradorVO().getPessoa().getCodigo() == 0) {
//            setSucesso(false);
//            setErro(true);
//            setOnComplete("");
//            setMensagemDetalhada("msg_erro", "Grave o cadastro do colaborador para poder adicionar a foto.");
//        } else {
        setOnComplete(String.format("setAttributesModalCapFoto('%s', '%s', '%s', '');"
                        + "Richfaces.showModalPanel('modalCapFotoHTML5');",
                getKey(), getUsuarioVO().getColaboradorVO().getPessoa().getCodigo(),
                request().getContextPath()));
//        }
    }

    public void removerFoto() {
        try {
            getFacade().getPessoa().removerFoto(getKey(), getUsuarioVO().getColaboradorVO().getPessoa().getCodigo());
            getUsuarioVO().getColaboradorVO().getPessoa().setFoto(null);
            getUsuarioVO().getColaboradorVO().getPessoa().setFotoKey(null);
            setMensagem("msg_dados_excluidos");
            setSucesso(true);
        } catch (Exception e) {
            setErro(true);
            setMensagem(e.getMessage());

        }
    }

    public void sincronizarUsuarioMovel() throws Exception {
        sincronizarUsuarioMovel(false, getKey());
    }

    public void sincronizarUsuarioMovel(boolean propagarExcessao, String key) throws Exception {
        limparMsg();
        try {
            setErroSincronizarUsuarioMovel(null);
            UsuarioMovelVO userMovel = getFacade().getUsuarioMovel().consultarPorColaboradorDoUsuario(usuarioVO, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
            UsuarioMovelControle uMovelControl = null;
            if (context() != null) {
                uMovelControl = (UsuarioMovelControle) getControlador(UsuarioMovelControle.class);
            } else {
                userMovel.setPropagarExcessao(true);
                uMovelControl = new UsuarioMovelControle();
            }
            userMovel.setUsuarioEmailVO(usuarioEmailVO);
            userMovel.getUsuarioEmailVO().setUsuario(getUsuarioVO().getCodigo());
            userMovel.setColaborador(getUsuarioVO().getColaboradorVO());
            userMovel.setUsuarioZW(getUsuarioVO().getCodigo());
            userMovel.getColaborador().setListaTipoColaboradorVOs(
                    getFacade().getTipoColaborador().consultarPorCodigoColaborador(userMovel.getColaborador().getCodigo(), false, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA));
            userMovel.setSenha(getUsuarioVO().getSenha());
            userMovel.setAtivo(userMovel.getColaborador().getSituacao().equals("AT"));
            userMovel.setEmpresa(getUsuarioVO().getColaboradorVO().getEmpresa().getCodigo());
            userMovel.setPerfilTw(getUsuarioVO().getPerfilTw());
            userMovel.setSenhaEncriptada(true);
            userMovel.setPropagarExcessao(true);
            if (UteisValidacao.emptyNumber(userMovel.getCodigo())) {
                userMovel.setNome(getUsuarioVO().getUsername() + "_" + userMovel.getEmpresa());
            }
            uMovelControl.setuMovel(userMovel);
            uMovelControl.gravar(false, key);
            gravarUsuarioEmail();
        } catch (Exception e) {
            setErroSincronizarUsuarioMovel(true);
            if (propagarExcessao) {
                throw e;
            }
            montarErro(e);
        }
    }

    public void gravarUsuarioEmail() throws Exception {
        if (UteisValidacao.emptyString(this.getUsuarioEmailVO().getEmail())) {
            throw new Exception("msg_camp_email_nao_inf_usuario");
        }
        if (getFacade().getUsuarioEmail().existeEmail(this.getUsuarioEmailVO())) {
            throw new Exception("Chave (email)=(" + this.getUsuarioEmailVO().getEmail() + ") já existe.");
        }
        this.getUsuarioEmailVO().setUsuario(getUsuarioVO().getCodigo());
        getFacade().getUsuarioEmail().gravar(this.getUsuarioEmailVO());
    }

    public void gravarUsuarioTelefone() throws Exception {
        getUsuarioTelefoneVO().setUsuario(usuarioVO.getCodigo());
        getUsuarioTelefoneVO().setUsuarioVO(usuarioVO);
        if (!configuracaoSistema.isUsarSistemaInternacional() &&
                UteisValidacao.emptyString(getUsuarioTelefoneVO().getDdi())) {
            getUsuarioTelefoneVO().setDdi("+55");
        }
        getFacade().getUsuarioTelefone().gravar(getUsuarioTelefoneVO());
    }

    public String getModalMensagemSincronizarUsuarioMovel() {
        if (!getErroSincronizarUsuarioMovel()) {
            return "Richfaces.showModalPanel('panelMsgSincronizadorUsuarioMovel')";
        }
        return null;
    }

    public String getMensagemSincronizarUsuarioMovel() {
        if (getErroSincronizarUsuarioMovel()) {
            return "Ocorreu um erro na sincronização!";
        }
        return "Usuário Sincronizado com Sucesso!";
    }

    public List getListaEmail() {
        List objs = new ArrayList();
        objs.add(new SelectItem("", ""));
        try {
            for (EmailVO obj : getUsuarioVO().getColaboradorVO().getPessoa().getEmailVOs()) {
                objs.add(new SelectItem(obj.getEmail(), obj.getEmail()));
            }
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
        return objs;
    }

    public Boolean getMostrarEmailCampo() {
        if (!getUsuarioVO().getTipoUsuario().equals("") && getUsuarioVO().getColaboradorVO().getPessoa().getEmailVOs().isEmpty() && getUsuarioVO().isNovoObj()) {
            return true;
        }
        return false;
    }

    public Boolean getMostrarEmailCombo() {
        if (!getUsuarioVO().getTipoUsuario().equals("") && !getUsuarioVO().getColaboradorVO().getPessoa().getEmailVOs().isEmpty() && getUsuarioVO().isNovoObj()) {
            return true;
        }
        return false;
    }

    public Boolean getErroSincronizarUsuarioMovel() {
        if (erroSincronizarUsuarioMovel == null) {
            erroSincronizarUsuarioMovel = false;
        }
        return erroSincronizarUsuarioMovel;
    }

    public void setErroSincronizarUsuarioMovel(Boolean erroSincronizarUsuarioMovel) {
        this.erroSincronizarUsuarioMovel = erroSincronizarUsuarioMovel;
    }

    public Boolean getMostrarPanelFoto() {
        if (getUsuarioVO().getColaboradorVO().getPessoa().getCodigo() == null || getUsuarioVO().getColaboradorVO().getPessoa().getCodigo() == 0) {
            return false;
        }
        return true;
    }

    public UsuarioPerfilAcessoVO getUsuarioPerfilSelecionado() {
        return usuarioPerfilSelecionado;
    }

    public void setUsuarioPerfilSelecionado(UsuarioPerfilAcessoVO usuarioPerfilSelecionado) {
        this.usuarioPerfilSelecionado = usuarioPerfilSelecionado;
    }


    public boolean getSenhaExpirada() {
        try {
            if (getUsuarioVO().isNovoObj()) {
                return false;
            }
            Integer qtdDiasExpirarSenha = getFacade().getConfiguracaoSistema().buscarQtdDiasParaExpirarSenha();
            Date dataExpiracao = Uteis.obterDataFutura2(getUsuarioVO().getDataUltimaAlteracaoSenha(), qtdDiasExpirarSenha);
            long qtdDiasFaltaExpirar = Uteis.nrDiasEntreDatas(Calendario.hoje(), dataExpiracao);
            if (qtdDiasFaltaExpirar <= 0) {
                return true;
            } else {
                return false;
            }
        } catch (Exception e) {
            setErro(true);
            setSucesso(false);
            setMensagemID("msg_erro");
            setMensagemDetalhada(e);
        }
        return false;
    }

    public List<SelectItem> getListaSelectItemSituacao() throws Exception {
        List<SelectItem> objs = new ArrayList<SelectItem>();
        objs.add(new SelectItem("AT", "Ativo"));
        objs.add(new SelectItem("NA", "Inativo"));
        objs.add(new SelectItem("TD", "Todos"));
        return objs;
    }

    public String getSituacaoFiltro() {
        if (situacaoFiltro == null) {
            situacaoFiltro = "AT";
        }
        return situacaoFiltro;
    }

    public void setSituacaoFiltro(String situacaoFiltro) {
        this.situacaoFiltro = situacaoFiltro;
    }

    public String abrirPerfilAcesso() {
        try {
            setOnComplete("");
            PerfilAcessoControle perfilAcessoControle = (PerfilAcessoControle) getControlador(PerfilAcessoControle.class);
            if (perfilAcessoControle == null) {
                setSucesso(false);
                setErro(true);
                setMensagemDetalhada("msg_erro", "Perfil de Acesso não inicializado.");
                setOnComplete("alert('Perfil de Acesso não inicializado');");
                return "";
            }
            String retorno = perfilAcessoControle.editar();
            if (retorno.equals("editar")) {
                setOnComplete("abrirPopup('novoPerfilAcessoForm.jsp', 'PerfilAcesso', 1000, 650);");

            } else {
                setOnComplete("alert('" + retorno + "');");
            }

        } catch (Exception e) {
            setErro(true);
            setSucesso(false);
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
        return "";
    }


    public void incluirLogExclusao() throws Exception {
        try {
            usuarioVO.setObjetoVOAntesAlteracao(new UsuarioVO());
            usuarioVO.setNovoObj(true);
            registrarLogExclusaoTodosDadosObjetoVO(usuarioVO, usuarioVO.getCodigo(), "USUARIO", 0);
        } catch (Exception e) {
            registrarLogErroObjetoVO("USUARIO", usuarioVO.getCodigo(), "ERRO AO GERAR LOG DE EXCLUSÃO DE USUARIO ", this.getUsuarioLogado().getNome(), this.getUsuarioLogado().getUserOamd());
            e.printStackTrace();
        }
    }

    public void atualizarNotificacoesUCP() {
        try {
            List<NotificacaoUsuarioUCPTO> notificacoesUCP = new ArrayList<NotificacaoUsuarioUCPTO>();

            List<TipoNotificacaoUsuarioEnum> buscar = new ArrayList<TipoNotificacaoUsuarioEnum>();
            buscar.add(TipoNotificacaoUsuarioEnum.ENCONTROS_UCP);
            buscar.add(TipoNotificacaoUsuarioEnum.RESPOSTA_UCP);
            buscar.add(TipoNotificacaoUsuarioEnum.UNIVERSIDADE_UCP);

            List<NotificacaoUsuarioVO> lista = getFacade().getNotificacaoUsuario().consultarPorUsuarioTipo(getUsuarioLogado(), buscar, Uteis.NIVELMONTARDADOS_DADOSBASICOS);

            if (!UteisValidacao.emptyList(lista)) {
                Map<TipoNotificacaoUsuarioEnum, List<NotificacaoUsuarioVO>> mapa = new HashMap<TipoNotificacaoUsuarioEnum, List<NotificacaoUsuarioVO>>();
                for (NotificacaoUsuarioVO obj : lista) {
                    List<NotificacaoUsuarioVO> aa = mapa.get(obj.getTipo());
                    if (aa == null) {
                        aa = new ArrayList<NotificacaoUsuarioVO>();
                        aa.add(obj);
                        mapa.put(obj.getTipo(), aa);
                    } else {
                        aa.add(obj);
                    }
                }

                for (Map.Entry<TipoNotificacaoUsuarioEnum, List<NotificacaoUsuarioVO>> entry : mapa.entrySet()) {
                    TipoNotificacaoUsuarioEnum tipo = entry.getKey();
                    List<NotificacaoUsuarioVO> notifica = entry.getValue();

                    NotificacaoUsuarioUCPTO novo = new NotificacaoUsuarioUCPTO();
                    novo.setTipoNotificacaoUCPEnum(tipo);
                    novo.getListaNotificacoes().addAll(notifica);
                    notificacoesUCP.add(novo);
                }
            }

            getUsuario().setNotificacoesUsuarioUCP(Ordenacao.ordenarLista(notificacoesUCP, "ordenacao"));

        } catch (Exception ex) {
//            montarErro(ex);
        }
    }

    public void excluirNotificacaoUsuario() {
        try {
            NotificacaoUsuarioVO obj = (NotificacaoUsuarioVO) context().getExternalContext().getRequestMap().get("notificacao");
            if (!UteisValidacao.emptyNumber(obj.getCodigo())) {
                getFacade().getNotificacaoUsuario().excluir(obj);
            }
            atualizarNrMsgNaoLidas(false);
        } catch (Exception ex) {
        }
    }

    public void excluirTodasNotificacaoTipoUsuario() {
        try {
            NotificacaoUsuarioUCPTO obj = (NotificacaoUsuarioUCPTO) context().getExternalContext().getRequestMap().get("tipoNotificacao");
            if (obj != null) {
                getFacade().getNotificacaoUsuario().excluirTodasPorTipoUsuario(obj.getListaNotificacoes().get(0), getUsuarioLogado());
            }
            atualizarNrMsgNaoLidas(false);
        } catch (Exception ex) {
        }
    }

    public UsuarioEmailVO getUsuarioEmailVO() {
        return usuarioEmailVO;
    }

    public void setUsuarioEmailVO(UsuarioEmailVO usuarioEmailVO) {
        this.usuarioEmailVO = usuarioEmailVO;
    }

    public String getUrlImagemAssinatura() {
        return urlImagemAssinatura;
    }

    public void setUrlImagemAssinatura(String urlImagemAssinatura) {
        this.urlImagemAssinatura = urlImagemAssinatura;
    }

    public void obterURLAssinatura() {
        try {
            String urlObject = MidiaService.getInstance().downloadObject(getKey(),
                    MidiaEntidadeEnum.ASSINATURA_USUARIO,
                    getUsuarioVO().getCodigo().toString());
            urlImagemAssinatura = UteisValidacao.emptyString(urlObject)
                    ? "" : (Uteis.getPaintFotoDaNuvem(urlObject + "?time=" + getTimeStamp()));
        } catch (Exception e) {
            urlImagemAssinatura = "";
            Uteis.logar(e, UsuarioControle.class);
        }
    }

    public void uploadImgAssinatura(UploadEvent upload) throws Exception {

        if (UteisValidacao.emptyNumber(getUsuarioVO().getCodigo())) {
            throw new Exception("Salve o usuário antes de carregar a imagem da assinatura.");
        }

        UploadItem item = upload.getUploadItem();
        File item1 = item.getFile();
        boolean erroAoLerArquivo = true;
        try {
            BufferedImage outImage = ImageIO.read(item1);
            erroAoLerArquivo = false;
            ByteArrayOutputStream arrayOutputStream = new ByteArrayOutputStream();
            byte buffer[] = new byte[4096];
            int bytesRead = 0;
            FileInputStream fi = new FileInputStream(item1.getAbsolutePath());
            while ((bytesRead = fi.read(buffer)) != -1) {
                arrayOutputStream.write(buffer, 0, bytesRead);
            }
            MidiaService.getInstance().uploadObjectFromByteArray(getKey(),
                    MidiaEntidadeEnum.ASSINATURA_USUARIO,
                    getUsuarioVO().getCodigo().toString(), arrayOutputStream.toByteArray());
            obterURLAssinatura();
            arrayOutputStream.close();
            fi.close();
            setMensagemID("msg_upload_arquivo");
            setSucesso(true);
            setErro(false);
        } catch (Exception e) {
            tratarErroAoCarregarImagem(e, erroAoLerArquivo, item1, "uploadImgAssinatura");
        }
    }

    private void tratarErroAoCarregarImagem(Exception e, boolean erroAoLerArquivo, File file, String nomeMetodo) {
        if ((erroAoLerArquivo) || (!imageEstaNoFormatoCMYK(file))) {
            montarErro("Existe um erro não identificado no arquivo de imagem. Edite a imagem, crie outro arquivo e repita a operação.");
        }
        Logger.getLogger(getClass().getSimpleName()).log(Level.SEVERE, "#### ERRO AO CARREGAR IMAGEM USUARIO. método:" + nomeMetodo + ". Erro: " + e.getMessage());

    }

    private boolean imageEstaNoFormatoCMYK(File item1) {
        if (isCMYK(item1)) {
            montarErro("Essa imagem está no formato inadequado. Por favor converta para RGB.");
            return true;
        }
        return false;
    }

    public List<UsuarioVO> carregarLstVerificarUsuariosAtivos() {
        try {
            lstUsuariosInativosMaisDeUmMes = getFacade().getUsuario().consultarUsuariosSemAcessoPorUmMes();

            if (lstUsuariosInativosMaisDeUmMes != null && !lstUsuariosInativosMaisDeUmMes.isEmpty()) {
                for (UsuarioVO lstUser : lstUsuariosInativosMaisDeUmMes) {
                    lstUser.setDiasSemAcessar(Calendario.diferencaEmDias(lstUser.getUltimoAcesso(), new Date()));
                }
                return lstUsuariosInativosMaisDeUmMes;
            }
        } catch (Exception e) {
            e.printStackTrace();
        }

        return lstUsuariosInativosMaisDeUmMes;
    }

    public void alteraValor() {
        Object object = JSFUtilities.getRequestAttribute("servico");
    }

    public void showModalInativarUsuarios(ValueChangeEvent evt) throws Exception {
        UsuarioVO usuarioVO = getUsuarioLogado();
        if (evt.getNewValue() != null) {
            usuarioVO.setShowModalInativar((Boolean) evt.getNewValue());
            usuarioVO.setExibirModalInativarUsersHoje(new Date());
        }
        try {
            getFacade().getUsuario().alterarShowModalInativar(usuarioVO);
            if (evt.getNewValue().equals(true)) {
                notificarRecursoEmpresa(RecursoSistema.NAO_EXIBIR_MAIS_HOJE_MODAL_INATIVAR_USUARIO);
            }

        } catch (Exception e) {
            montarErro("Erro para não notificar mais hoje o usuario com informações de inativar usuarios.");
            e.printStackTrace();
        }
    }

    public void showModalPlanos(ValueChangeEvent evt) throws Exception {
        UsuarioVO usuarioVO = getUsuarioLogado();
        if (evt.getNewValue() != null) {
            usuarioVO.setShowModalPlanos((Boolean) evt.getNewValue());
            usuarioVO.setDataExibirModalPlanos(Calendario.hoje());
        }
        try {
            getFacade().getUsuario().alterarShowModalPlanos(usuarioVO);
            if (evt.getNewValue().equals(true)) {
                notificarRecursoEmpresa(RecursoSistema.NAO_EXIBIR_MODAL_PLANOS_INATIVOS_MES_SEGUINTE);
            }

        } catch (Exception e) {
            montarErro("Erro ao tentar marcar para não exibir mais hoje o aviso de planos inativos.");
            e.printStackTrace();
        }
    }


    public void inativarUsuarios() {
        if (lstUsuariosInativosMaisDeUmMes.stream().allMatch(n -> n.getColaboradorVO().getSituacaoCheckBox() != true)) {
            montarErro("Você deve selecionar um usuário para inativar");
        }

        lstUsuariosInativosMaisDeUmMes.forEach(r -> {
            if (r.getColaboradorVO().getSituacaoCheckBox()) {
                try {
                    getFacade().getColaborador().alterarSituacaoPorCodigoUsuario("NA", r.getCodigo());
                    inclurLogInativarUsuario(r);
                    notificarRecursoEmpresa(RecursoSistema.INATIVAR_USUARIO);
                    montarSucessoGrowl("Os usuários selecionados foram inativados com sucesso!");
                } catch (Exception e) {
                    montarErro("Erro ao inativar usuário(s)!");
                    setMensagemDetalhada("");
                    e.printStackTrace();
                }
            }
        });

        carregarLstVerificarUsuariosAtivos();
    }

    public void inclurLogInativarUsuario(UsuarioVO u) throws Exception {
        try {
            LogVO log = new LogVO();
            log.setChavePrimaria(getUsuarioLogado().getCodigo().toString());
            log.setNomeEntidade("Colaborador");
            log.setNomeEntidadeDescricao("Colaborador");
            log.setOperacao("Inativar Usuario - " + u.getUsername());
            log.setNomeCampo("situacao");
            log.setValorCampoAlterado("NA");
            log.setResponsavelAlteracao(getUsuarioLogado().getNome());
            log.setDataAlteracao(Calendario.hoje());
            log.setUserOAMD(getUsuarioLogado().getUserOamd());
            log.setPessoa(u.getColaboradorVO().getPessoa().getCodigo());
            registrarLogObjetoVO(log, u.getColaboradorVO().getPessoa().getCodigo());
        } catch (Exception e) {
            registrarLogErroObjetoVO("Colaborador", u.getColaboradorVO().getPessoa().getCodigo(), "ERRO AO GERAR LOG DO COLABORADOR",
                    getUsuarioLogado().getNome(), getUsuarioLogado().getUserOamd());
        }
    }

    public List<UsuarioVO> executarAutocompleteUsuarios(Object suggest) {
        String pref = (String) suggest;
        ArrayList<UsuarioVO> result = new ArrayList<UsuarioVO>();
        try {
            if (pref.equals("%")) {
                result = (ArrayList<UsuarioVO>) getFacade().getUsuario().consultarTodosUsuarioComLimite(
                        false, getEmpresaLogado().getCodigo(), Uteis.NIVELMONTARDADOS_TELACONSULTA);
            } else {
                result = (ArrayList<UsuarioVO>) getFacade().getUsuario().consultarPorNomeUsuarioComLimite(
                        pref, getEmpresaLogado().getCodigo(), false, Uteis.NIVELMONTARDADOS_TELACONSULTA);
            }
        } catch (Exception ex) {
            result = (new ArrayList<UsuarioVO>());
        }
        return result;
    }

    public List<UsuarioVO> getLstUsuariosInativosMaisDeUmMes() {
        return lstUsuariosInativosMaisDeUmMes;
    }

    public void setLstUsuariosInativosMaisDeUmMes(List<UsuarioVO> lstUsuariosInativosMaisDeUmMes) {
        this.lstUsuariosInativosMaisDeUmMes = lstUsuariosInativosMaisDeUmMes;
    }

    public int getScrollerPage() {
        return scrollerPage;
    }

    public void setScrollerPage(int scrollerPage) {
        this.scrollerPage = scrollerPage;
    }

    public Integer getEmpresaFiltro() {
        return empresaFiltro;
    }

    public void setEmpresaFiltro(Integer empresaFiltro) {
        this.empresaFiltro = empresaFiltro;
    }

    public boolean isConsultarInfoTodasEmpresas() {
        return consultarInfoTodasEmpresas;
    }

    public void setConsultarInfoTodasEmpresas(boolean consultarInfoTodasEmpresas) {
        this.consultarInfoTodasEmpresas = consultarInfoTodasEmpresas;
    }

    private void notificarRecursoEmpresaUsuario() {
        if (!usuarioVO.isPedirSenhaFuncionalidade()) {
            if (usuarioVOClone != null) {
                if (usuarioVO.isPedirSenhaFuncionalidade() != usuarioVOClone.isPedirSenhaFuncionalidade()) {
                    notificarRecursoEmpresa(RecursoSistema.CADASTRO_USUARIO_NAO_PEDIR_PERMISSAO_DESMARCOU);
                }
            }
        }
    }

    public List<SelectItem> getSelectItemTipoTelefone() {
        List<SelectItem> objs = new ArrayList<>();
        for (TipoTelefoneEnum tipoTelefone : TipoTelefoneEnum.values()) {
            String value = tipoTelefone.getCodigo();
            String label = tipoTelefone.getDescricao();
            objs.add(new SelectItem(value, label));
        }
        Ordenacao.ordenarLista(objs, "label");
        return objs;
    }

    public void adicionarTelefoneUsuario() {
        try {
            TelefoneVO telefoneVO = new TelefoneVO();
            telefoneVO.setDdi(this.getUsuarioTelefoneVO().getDdi());
            telefoneVO.setNumero(this.getUsuarioTelefoneVO().getNumero());
            if (!UteisValidacao.emptyNumber(getUsuarioVO().getColaboradorVO().getPessoa().getCodigo())) {
                telefoneVO.setPessoa(getUsuarioVO().getColaboradorVO().getPessoa().getCodigo());
            }
            getUsuarioVO().getColaboradorVO().getPessoa().adicionarObjTelefoneVOs(telefoneVO);
            setMensagemID("msg_dados_adicionados");
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public void removerTelefoneUsuario() {
        try {

            getUsuarioVO().getColaboradorVO().getPessoa().excluirObjTelefoneVOs(getTelefoneRemover().getNumero());
            setMensagemID("msg_dados_excluidos");
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public TelefoneVO getTelefoneVO() {
        if (telefoneVO == null) {
            telefoneVO = new TelefoneVO();
        }
        return telefoneVO;
    }

    public String[] identificacaoPessoalInternacional() {
        try {
            displayIdentificadorFront = identificadorPessoaInternacional(getEmpresaLogado().getPais().getNome());
        } catch (Exception e) {
            e.printStackTrace();
        }
        return displayIdentificadorFront;
    }

    public void setTelefoneVO(TelefoneVO telefoneVO) {
        this.telefoneVO = telefoneVO;
    }

    public ConfiguracaoSistemaVO getConfiguracaoSistema() {
        return configuracaoSistema;
    }

    public void setConfiguracaoSistema(ConfiguracaoSistemaVO configuracaoSistema) {
        this.configuracaoSistema = configuracaoSistema;
    }

    public String[] getDisplayIdentificadorFront() {
        return displayIdentificadorFront;
    }

    public void setDisplayIdentificadorFront(String[] displayIdentificadorFront) {
        this.displayIdentificadorFront = displayIdentificadorFront;
    }


    public void setMsgAlert(String msgAlert) {
        this.msgAlert = msgAlert;
    }

    public String getMsgAlert() {
        if (msgAlert == null) {
            return "";
        }
        return msgAlert;
    }

    public List<UsuarioVO> getListaUsuariosSelecionadosParaInativar() {
        return listaUsuariosSelecionadosParaInativar;
    }

    public void setListaUsuariosSelecionadosParaInativar(List<UsuarioVO> listaUsuariosSelecionadosParaInativar) {
        this.listaUsuariosSelecionadosParaInativar = listaUsuariosSelecionadosParaInativar;
    }

    public void confirmarExcluir() {
        HttpServletRequest request = (HttpServletRequest) FacesContext.getCurrentInstance().getExternalContext().getRequest();
        String obj = request.getParameter("metodochamar");

        MensagemGenericaControle control = (MensagemGenericaControle) getControlador(MensagemGenericaControle.class);
        control.setMensagemDetalhada("", "");
        setMsgAlert("Richfaces.showModalPanel('mdlMensagemGenerica');");
        if (obj.equals("excluir")) {
            control.init("Exlusão de Usuário",
                    "Deseja Excluir o Usuário?",
                    this, obj, "", "", "", "grupoBtnExcluir,form");
        } else if (obj.equals("removerTelefoneUsuario")) {
            control.init("Exclusão de Telefone",
                    "Deseja realmente excluir o telefone do colaborador?",
                    this, obj, "", "", "", "grupoClonarCompra,panelTelefone, tabMensagens, form");
        } else {
            control.init("Exclusão de Tipo de Colaborador",
                    "Deseja realmente excluir o Tipo de Colaborador?",
                    this, obj, "", "", "", "grupoClonarCompra,panelTipoColaborador, tabMensagens, form");
        }
        setTelefoneRemover((TelefoneVO) context().getExternalContext().getRequestMap().get("tel"));
        setTipoColaboradorVORemover((TipoColaboradorVO) context().getExternalContext().getRequestMap().get("tipoColaboradorVO"));
    }

    public TelefoneVO getTelefoneRemover() {
        return telefoneRemover;
    }

    public void setTelefoneRemover(TelefoneVO telefoneRemover) {
        this.telefoneRemover = telefoneRemover;
    }

    public TipoColaboradorVO getTipoColaboradorVORemover() {
        return tipoColaboradorVORemover;
    }

    public void setTipoColaboradorVORemover(TipoColaboradorVO tipoColaboradorVORemover) {
        this.tipoColaboradorVORemover = tipoColaboradorVORemover;
    }

    public void solicitarNovaSenha() {
        try {
            limparMsg();
            if (UteisValidacao.emptyString(this.getUsuarioVO().getUsuarioGeral())) {
                throw new Exception("Para alterar a senha é necessário validar o e-mail.");
            }
            SincronizarUsuarioNovoLogin.solicitarTrocaSenha(this.getUsuarioVO().getCodigo(), Conexao.getFromSession(),
                    getEmpresaLogado().getCodigo(), getUsuarioLogado(), getIpCliente(), getIntegracaoNovoLogin());
            montarSucessoGrowl("E-mail enviado");
        } catch (Exception e) {
            e.printStackTrace();
            montarErro(e);
        }
    }

    public List<UsuarioSincronizacaoVO> getLogSincronizacao() {
        if (logSincronizacao == null) {
            logSincronizacao = new ArrayList<>();
        }
        return logSincronizacao;
    }

    public void setLogSincronizacao(List<UsuarioSincronizacaoVO> logSincronizacao) {
        this.logSincronizacao = logSincronizacao;
    }

    public void carregarListaLogSincronizacao() {
        try {
            this.setLogSincronizacao(new ArrayList<>());
            if (!UteisValidacao.emptyNumber(getUsuarioVO().getCodigo())) {
                this.setLogSincronizacao(getFacade().getUsuario().consultarLogSincronizacao(getUsuarioVO().getCodigo(), 20));
            }
        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }

    public void validarUsernameExistente() {
        try {
            if (getUsuarioVO().getUsername().length() < 4) {
                setUsernameDisponivelClass("userindisponivel");
                setUsernameDisponivelMsg("Informar no mínimo 4 letras");
                setUsernameDisponivelIcon("fa-icon-remove");
                return;
            }
            if (getFacade().getUsuario().consultarPorUsernameEDiferenteDoUsuario(getUsuarioVO().getUsername(), getUsuarioVO().getCodigo())) {
                setUsernameDisponivelClass("userindisponivel");
                setUsernameDisponivelMsg("Indisponível");
                setUsernameDisponivelIcon("fa-icon-remove");
            } else {
                setUsernameDisponivelClass("userdisponivel");
                setUsernameDisponivelMsg("Disponível");
                setUsernameDisponivelIcon("fa-icon-ok");
            }
        } catch (Exception ex) {
            ex.printStackTrace();
            setUsernameDisponivelClass("");
            setUsernameDisponivelMsg("");
            setUsernameDisponivelIcon("");
        }
    }

    public String getUsernameDisponivelClass() {
        return usernameDisponivelClass;
    }

    public void setUsernameDisponivelClass(String usernameDisponivelClass) {
        this.usernameDisponivelClass = usernameDisponivelClass;
    }

    public String getUsernameDisponivelMsg() {
        return usernameDisponivelMsg;
    }

    public void setUsernameDisponivelMsg(String usernameDisponivelMsg) {
        this.usernameDisponivelMsg = usernameDisponivelMsg;
    }

    public String getUsernameDisponivelIcon() {
        return usernameDisponivelIcon;
    }

    public void setUsernameDisponivelIcon(String usernameDisponivelIcon) {
        this.usernameDisponivelIcon = usernameDisponivelIcon;
    }

    public UsuarioTelefoneVO getUsuarioTelefoneVO() {
        if (usuarioTelefoneVO == null) {
            usuarioTelefoneVO = new UsuarioTelefoneVO();
        }
        return usuarioTelefoneVO;
    }

    public void setUsuarioTelefoneVO(UsuarioTelefoneVO usuarioTelefoneVO) {
        this.usuarioTelefoneVO = usuarioTelefoneVO;
    }

    public void abrirNovoTelefone() {
        try {
            limparMsg();
            setOnComplete("");
            setMsgAlert("");
            preencherUsuarioGeral();

            if (!isPodeAlterarInformacoes()) {
                throw new Exception("Somente o próprio usuário pode alterar o telefone ou caso tenha a permissão \"1.08 - Permissão para alterar senha de outro usuário\"");
            }
            if (this.getUsuarioEmailVO() == null ||
                    UteisValidacao.emptyString(this.getUsuarioEmailVO().getEmail()) ||
                    !this.getUsuarioEmailVO().isVerificado()) {
                throw new Exception("É necessário validar o e-mail para poder alterar o celular");
            }
            this.setTokenDTO(new TokenDTO());
            this.setUsuarioTelefoneAlterarVO(this.getUsuarioTelefoneVO());
            this.getUsuarioTelefoneAlterarVO().setUsuarioVO(this.getUsuarioVO());
            this.getUsuarioTelefoneAlterarVO().setUsuario(this.getUsuarioVO().getCodigo());
            setOnComplete("Richfaces.showModalPanel('modalAlterarTelefone');document.getElementById('formTelefoneLogin:telefoneUsuarioNovo').focus();");
        } catch (Exception e) {
            e.printStackTrace();
            montarErro(e);
        }
    }

    public void solicitarNovoTelefone() {
        try {
            limparMsg();
            setOnComplete("");
            setMsgAlert("");
            this.setTokenDTO(new TokenDTO());
            if (!Uteis.validarTelefoneCelular(this.getUsuarioTelefoneAlterarVO().getNumero())) {
                throw new Exception("Número celular inválido");
            }
            if (getFacade().getUsuarioTelefone().existeTelefone(this.getUsuarioTelefoneAlterarVO())) {
                throw new ConsistirException("Já existe um usuário com esse telefone.");
            }
            String token = SincronizarUsuarioNovoLogin.solicitarTrocaTelefone(this.getUsuarioVO().getCodigo(),
                    this.getUsuarioTelefoneAlterarVO(), Conexao.getFromSession(),
                    getEmpresaLogado().getCodigo(), getUsuarioLogado(), getIpCliente(), getIntegracaoNovoLogin());
            this.getTokenDTO().setToken(token);
            this.getTokenDTO().setUsuarioGeral(this.getUsuarioVO().getUsuarioGeral());
            setOnComplete("document.getElementById('formTelefoneLogin:usuarioTokenSMSLogin').focus();");
            montarSucessoGrowl("SMS enviado");
        } catch (Exception e) {
            e.printStackTrace();
            montarErro(e);
        }
    }

    public void validarTokenTelefone() {
        try {
            limparMsg();
            setOnComplete("");
            setMsgAlert("panelTrocaTelefoneLogin");
            validarToken("VALIDAR_TOKEN_TELEFONE");
            preencherUsuarioTelefone();
            montarSucessoGrowl("Celular alterado");
            setOnComplete("Richfaces.hideModalPanel('modalAlterarTelefone');");
            setMsgAlert("form");
        } catch (Exception e) {
            e.printStackTrace();
            montarErro(e);
        }
    }

    private void validarToken(String operacao) throws Exception {
        if (UteisValidacao.emptyString(this.getTokenDTO().getCodigoVerificacao())) {
            throw new Exception("Código de verificação não informado");
        }
        if (this.getTokenDTO().getCodigoVerificacao().length() < 6) {
            throw new Exception("Código de verificação incompleto");
        }
        SincronizarUsuarioNovoLogin.validarToken(this.getUsuarioVO().getCodigo(), this.getTokenDTO(),
                Conexao.getFromSession(), getEmpresaLogado().getCodigo(), getUsuarioLogado(), getIpCliente(),
                operacao, getIntegracaoNovoLogin());
        preencherUsuarioGeral();
    }

    private void preencherUsuarioGeral() throws Exception {
        if (UteisValidacao.emptyString(this.getUsuarioVO().getUsuarioGeral())) {
            this.getUsuarioVO().setUsuarioGeral(getFacade().getUsuario().consultarUsuarioGeral(getUsuarioVO()));
        }
    }

    public TokenDTO getTokenDTO() {
        if (tokenDTO == null) {
            tokenDTO = new TokenDTO();
        }
        return tokenDTO;
    }

    public void setTokenDTO(TokenDTO tokenDTO) {
        this.tokenDTO = tokenDTO;
    }

    public UsuarioTelefoneVO getUsuarioTelefoneAlterarVO() {
        if (usuarioTelefoneAlterarVO == null) {
            usuarioTelefoneAlterarVO = new UsuarioTelefoneVO();
        }
        return usuarioTelefoneAlterarVO;
    }

    public void setUsuarioTelefoneAlterarVO(UsuarioTelefoneVO usuarioTelefoneAlterarVO) {
        this.usuarioTelefoneAlterarVO = usuarioTelefoneAlterarVO;
    }

    public UsuarioEmailVO getUsuarioEmailAlterarVO() {
        if (usuarioEmailAlterarVO == null) {
            usuarioEmailAlterarVO = new UsuarioEmailVO();
        }
        return usuarioEmailAlterarVO;
    }

    public void setUsuarioEmailAlterarVO(UsuarioEmailVO usuarioEmailAlterarVO) {
        this.usuarioEmailAlterarVO = usuarioEmailAlterarVO;
    }

    public void abrirNovoEmail() {
        try {
            limparMsg();
            setOnComplete("");
            setMsgAlert("");
            this.setTokenDTO(new TokenDTO());
            this.setUsuarioEmailAlterarVO(this.getUsuarioEmailVO());
            this.getUsuarioEmailAlterarVO().setUsuarioVO(this.getUsuarioVO());
            this.getUsuarioEmailAlterarVO().setUsuario(this.getUsuarioVO().getCodigo());
            preencherUsuarioGeral();

            if (!isPodeAlterarInformacoes()) {
                throw new Exception("Somente o próprio usuário pode alterar o e-mail ou caso tenha a permissão \"1.08 - Permissão para alterar senha de outro usuário\"");
            }

            setOnComplete("Richfaces.showModalPanel('modalAlterarEmail');document.getElementById('formEmailLogin:emailUsuarioNovo').focus();");
        } catch (Exception e) {
            e.printStackTrace();
            montarErro(e);
        }
    }

    public void solicitarNovoEmail() {
        try {
            limparMsg();
            setOnComplete("");
            setMsgAlert("");
            this.setTokenDTO(new TokenDTO());
            this.getUsuarioEmailAlterarVO().setEmail(this.getUsuarioEmailAlterarVO().getEmail().trim());

            if (UteisValidacao.emptyString(this.getUsuarioEmailAlterarVO().getEmail())) {
                throw new Exception("E-mail não informado");
            }

            if (!UteisValidacao.validaEmail(this.getUsuarioEmailAlterarVO().getEmail())) {
                throw new Exception("E-mail inválido");
            }

            if (getFacade().getUsuarioEmail().existeEmail(this.getUsuarioEmailAlterarVO())) {
                throw new ConsistirException("Já existe um usuário com esse e-mail.");
            }

            buscarUsuarioMesmoEmail();
        } catch (Exception e) {
            e.printStackTrace();
            montarErro(e);
        }
    }

    public void validarTokenEmail() {
        try {
            limparMsg();
            setOnComplete("");
            setMsgAlert("panelTrocaEmailLogin");
            validarToken("VALIDAR_TOKEN_EMAIL");
            preencherUsuarioEmail();
            getUsuarioEmailVO().setVerificado(true);

            try {
                //sincronizar com o treino
                sincronizarUsuarioMovel(true, getKey());
            } catch (Exception e) {
                Uteis.logar("Erro ao sincronizar alteração de e-mail com o treino");
                e.printStackTrace();
            }

            adicionarUsuarioServicoDescobrir();
            montarSucessoGrowl("E-mail alterado");
            setOnComplete("Richfaces.hideModalPanel('modalAlterarEmail');");
            setMsgAlert("form");
        } catch (Exception e) {
            e.printStackTrace();
            montarErro(e);
        }
    }

    public void enviarCodigoVerificarEmail() {
        try {
            limparMsg();
            setOnComplete("");
            setMsgAlert("");
            this.setTokenDTO(new TokenDTO());
            //sincronizar o usuário enviando o email que o usuário informou no cadastro
            String token;
            if (isUsuarioGeralSincronizado()) {
                token = SincronizarUsuarioNovoLogin.solicitarTrocaEmail(this.getUsuarioVO().getCodigo(),
                        true, false, usuarioEmailVO, Conexao.getFromSession(),
                        getEmpresaLogado().getCodigo(), getUsuarioLogado(), getIpCliente(), getIntegracaoNovoLogin());
            } else {
                token = SincronizarUsuarioNovoLogin.enviarEmailNovoUsuario(this.getUsuarioVO().getCodigo(), false, true, null,
                        Conexao.getFromSession(), getEmpresaLogado().getCodigo(), getUsuarioLogado(), getIpCliente(), getIntegracaoNovoLogin());
            }
            this.getTokenDTO().setToken(token);
            montarSucessoGrowl("Informe o código recebido por e-mail");
            setOnComplete("Richfaces.showModalPanel('modalValidarToken');document.getElementById('formValidarToken:codigoVerificacaoTokenLogin').focus();");
        } catch (Exception e) {
            e.printStackTrace();
            montarErro(e);
        }
    }

    public void validarCodigoVerificacaoEmail() {
        try {
            limparMsg();
            setOnComplete("");
            setMsgAlert("panelValidarToken");
            validarToken("VALIDAR_TOKEN_ATIVAR_CONTA");
            preencherUsuarioEmail();
            montarSucessoGrowl("E-mail ativado");
            setOnComplete("Richfaces.hideModalPanel('modalValidarToken');");
            setMsgAlert("form");
        } catch (Exception e) {
            e.printStackTrace();
            montarErro(e);
        }
    }

    public boolean isUsuarioGeralSincronizado() {
        return !UteisValidacao.emptyString(this.getUsuarioVO().getUsuarioGeral());
    }

    public void desvincularUsuario() {
        try {
            limparMsg();
            setOnComplete("");
            preencherUsuarioGeral();

            SincronizarUsuarioNovoLogin.desvincularUsuario(this.getUsuarioVO().getCodigo(), Conexao.getFromSession(), getUsuarioLogado(), getEmpresaLogado().getCodigo(), getIpCliente(), getIntegracaoNovoLogin());
            preencherUsuarioEmail();
            preencherUsuarioTelefone();
            getUsuarioVO().setUsuarioGeral("");
            montarSucessoGrowl("Usuário " + getUsuarioVO().getNome() + " desvinculado da academia com sucesso! Atualize os dados de email, telefone e senha!");
            setOnComplete("Richfaces.hideModalPanel('modalConfirmarDesvinculoUsuario');");
            setMsgAlert("form");

        } catch (Exception e) {
            e.printStackTrace();
            montarErro(e);
        }
    }

    public void confirmarDesvinculoUsuario() {
        try {
            limparMsg();
            setOnComplete("");
            validarPermissao("DesvincularUsuarioAcademia", "9.67 - Desvincular Usuário da Academia", getUsuarioLogado());
            setOnComplete("Richfaces.showModalPanel('modalConfirmarDesvinculoUsuario');");
        } catch (Exception e) {
            montarAviso(e.getMessage());
        }
    }

    public void sincronizarNovoLogin() {
        try {
            limparMsg();
            setOnComplete("");
            preencherUsuarioGeral();
            verificarUsuarioGeralPreenchido();
            if (this.isUsuarioGeralSincronizado()) {
                SincronizarUsuarioNovoLogin.atualizarUsuarioGeral(this.getUsuarioVO().getCodigo(), Conexao.getFromSession(),
                        getEmpresaLogado().getCodigo(), getUsuarioLogado(), getIpCliente(), getIntegracaoNovoLogin());
                montarSucessoGrowl("Usuario sincronizado");
            } else {
                SincronizarUsuarioNovoLogin.enviarEmailNovoUsuario(this.getUsuarioVO().getCodigo(), false, false, null,
                        Conexao.getFromSession(), getEmpresaLogado().getCodigo(), getUsuarioLogado(), getIpCliente(), getIntegracaoNovoLogin());
                montarSucessoGrowl("E-mail enviado");
            }
        } catch (Exception e) {
            e.printStackTrace();
            montarErro(e);
        }
    }

    public void verificarUsuarioGeralPreenchido() {
        if (usuarioEmailVO.isVerificado()) {
            String email = this.getUsuarioVO().getEmail();
            if(!UteisValidacao.emptyString(email)){
                try {
                    String url = SincronizarUsuarioNovoLogin.obterUrlBackObterDadosUsuario();
                    JsonObject requestJson = new JsonObject();
                    requestJson.addProperty("username", email);
                    Map<String, String> headers = new HashMap<>();
                    String resposta = ExecuteRequestHttpService.post(url, requestJson.toString(), headers);
                    if (!UteisValidacao.emptyString(resposta)) {
                        JsonObject respostaJson = JsonParser.parseString(resposta).getAsJsonObject();
                        if (respostaJson.has("content") && respostaJson.getAsJsonObject("content").has("id")) {
                            String id = respostaJson.getAsJsonObject("content").get("id").getAsString();
                            if (UteisValidacao.emptyString(this.getUsuarioVO().getUsuarioGeral()) ||
                                    (!UteisValidacao.emptyString(this.getUsuarioVO().getUsuarioGeral()) && !this.getUsuarioVO().getUsuarioGeral().equals(id))){
                                this.getUsuarioVO().setUsuarioGeral(id);
                                Usuario usuarioDAO = new Usuario(Conexao.getFromSession());
                                UsuarioVO usuarioVO = usuarioDAO.consultarPorChavePrimaria(this.getUsuarioVO().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                                if (!UteisValidacao.emptyObject(usuarioVO)) {
                                    usuarioVO.setUsuarioGeral(this.getUsuarioVO().getUsuarioGeral());
                                    usuarioDAO.alterarUsuarioGeral(usuarioVO);
                                }
                            }
                        }
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                    System.out.println("Erro ao fazer a requisição: " + e.getMessage());
                }
            }
        }
    }

    private boolean isPodeAlterarInformacoes() {
        try {
            return (this.getApresentarCampoSenhaUsuario() != null && this.getApresentarCampoSenhaUsuario()) ||
                    getUsuarioLogado().getUsuarioPactoSolucoes() ||
                    getUsuarioVO().getCodigo().equals(getUsuarioLogado().getCodigo()) ||
                    UteisValidacao.emptyNumber(getUsuarioVO().getCodigo());
        } catch (Exception ex) {
            ex.printStackTrace();
            return false;
        }
    }

    public String novoMeusDados() throws Exception {
        setUsuarioVO(new UsuarioVO());
        setOnComplete("");
        inicializarUsuarioLogadoMeusDados();
        montarColaboradorMeusDados();
        incluirTabelaCadastroColaboradorDinamico();
        consultarListaCamposDinamico();
        setMensagemID("msg_entre_dados");
        setSucesso(false);
        setErro(false);
        return "";
    }

    public void inicializarUsuarioLogadoMeusDados() {
        try {
            if (getUsuarioLogado().getCodigo() != 0) {
                setUsuarioVO(getUsuarioLogado());
                getUsuarioVO().setCodigo(getUsuarioLogado().getCodigo());
                getUsuarioVO().setNome(getUsuarioLogado().getNome());
                getUsuarioVO().setAdministrador(getUsuarioLogado().getAdministrador());
                preencherUsuarioEmail();
                preencherUsuarioTelefone();
            }
        } catch (Exception exception) {
        }
    }

    public void montarColaboradorMeusDados() {
        try {
            if (!getUsuarioLogado().getColaboradorVO().getCodigo().equals(0)) {
                ColaboradorVO colaboradorVO = getFacade().getColaborador().consultarPorChavePrimaria(getUsuarioLogado().getColaboradorVO().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                getUsuarioVO().setColaboradorVO(colaboradorVO);
                setColaboradorVO(colaboradorVO);
                setPessoaVO(colaboradorVO.getPessoa());
            }
        } catch (Exception exception) {
        }
    }

    private void incluirTabelaCadastroColaboradorDinamico() throws Exception {
        getFacade().getCadastroDinamico().incluirTabelaCadastroDinamico("colaborador", Arrays.asList(CadastroDinamicoColaboradorEnum.values()));
    }

    private void consultarListaCamposDinamico() throws Exception {
        this.listaCamposMostrarColaboradorDinamico = getFacade().getCadastroDinamicoItem().consultarCamposMostrar("colaborador", Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        this.listaCamposObrigatorioColaboradorDinamico = getFacade().getCadastroDinamicoItem().consultarCamposObrigatorio("colaborador", Uteis.NIVELMONTARDADOS_DADOSBASICOS);
    }

    public void prepararListaReplicarEmpresa() {
        try {
            getListaUsuarioRedeEmpresa().clear();

            Map<String, UsuarioRedeEmpresaVO> mapaUnidadesPorChave = new HashMap<>();

            RedeEmpresaDataDTO redeEmpresaDataDTO = OamdMsService.urlsChaveRedeEmpresa(getKey());
            for (RedeDTO redeDTO : redeEmpresaDataDTO.getRedeEmpresas()) {
                if (redeDTO.getChave().toLowerCase().trim().equals(getKey().toLowerCase().trim())) {
                    continue;
                }
                UsuarioRedeEmpresaVO usuarioRedeEmpresaVO = getFacade().getUsuarioRedeEmpresa().consultarPorChaveEmpresaUsuario(redeDTO.getChave(), redeDTO.getEmpresaZw(), usuarioVO.getCodigo());
                if (usuarioRedeEmpresaVO == null) {
                    usuarioRedeEmpresaVO = new UsuarioRedeEmpresaVO();
                    usuarioRedeEmpresaVO.setMensagemSituacao("AGUARDANDO REPLICAR USUARIO");
                }
                usuarioRedeEmpresaVO.setChaveDestino(redeDTO.getChave().toLowerCase().trim());
                usuarioRedeEmpresaVO.setNomeUnidade(redeDTO.getNomeFantasia());
                usuarioRedeEmpresaVO.setRedeDTO(redeDTO);

                // verifica se ja existe uma unidade com a msm chave
                if (!mapaUnidadesPorChave.containsKey(usuarioRedeEmpresaVO.getChaveDestino())) {
                    mapaUnidadesPorChave.put(usuarioRedeEmpresaVO.getChaveDestino(), usuarioRedeEmpresaVO);
                } else {
                    UsuarioRedeEmpresaVO existente = mapaUnidadesPorChave.get(usuarioRedeEmpresaVO.getChaveDestino());
                    // Apenas substitui se a nova tiver empresaZw == 1 e a existente nao
                    if (redeDTO.getEmpresaZw() == 1 && existente.getRedeDTO().getEmpresaZw() != 1) {
                        mapaUnidadesPorChave.put(usuarioRedeEmpresaVO.getChaveDestino(), usuarioRedeEmpresaVO);
                    }
                }
            }
            getListaUsuarioRedeEmpresa().addAll(mapaUnidadesPorChave.values());
            limparMsg();
        } catch (Exception ex) {
            setSucesso(false);
            setErro(true);
            setMensagemDetalhada(ex);
            Logger.getLogger(PlanoControle.class.getName()).log(Level.SEVERE, null, ex);
        }
    }

    public Integer getListaUsuarioRedeEmpresaSincronizado() {
        Integer cont = 0;
        for (UsuarioRedeEmpresaVO unid : getListaUsuarioRedeEmpresa()) {
            if (unid.getDataAtualizacaoInformada()) {
                cont++;
            }
        }
        return cont;
    }

    public Integer getListaUsuarioRedeEmpresaSize() {
        return getListaUsuarioRedeEmpresa().size();
    }

    public boolean isExibirReplicarRedeEmpresa() {
        boolean integranteFranqueadoraRedeEmpresa = false;
        boolean permiteReplicarRedeEmpresa = false;
        boolean usuarioAdministrador = false;
        try {
            for (UsuarioPerfilAcessoVO userPerfAcess : getControladorTipado(LoginControle.class).getUsuarioLogado().getUsuarioPerfilAcessoVOs()) {
                if (userPerfAcess.getPerfilAcesso().getNome().toUpperCase().contains("ADMINISTRADOR")) {
                    usuarioAdministrador = true;
                }
            }
        } catch (Exception e) {
            usuarioAdministrador = false;
        }
        try {
            integranteFranqueadoraRedeEmpresa = OamdMsService.integranteFranqueadoraRedeEmpresa(getKey());
            permiteReplicarRedeEmpresa = getFacade().getConfiguracaoSistema().obterReplicarRedeEmpresa("permitirReplicarUsuarioRedeEmpresa");
        } catch (Exception e) {
            e.printStackTrace();
        }

        boolean usuarioAdminPacto = false;
        try {
            usuarioAdminPacto = getUsuarioLogado().getUsuarioAdminPACTO();
            if (!usuarioAdminPacto) {
                usuarioAdminPacto = getUsuarioLogado().getUsuarioPACTOBR();
            }
        } catch (Exception e) {
            e.printStackTrace();
        }

        if (integranteFranqueadoraRedeEmpresa && !usuarioVO.isNovoObj() && permiteReplicarRedeEmpresa && (usuarioAdminPacto || usuarioAdministrador)) {
            return true;
        } else {
            return false;
        }
    }

    public void replicarAutomaticoTodas() {
        try {
            int qtdThreads = 0;
            List<ReplicarRedeEmpresaCallable> callableTasks = new ArrayList<>();
            for (UsuarioRedeEmpresaVO obj : getListaUsuarioRedeEmpresa()) {
                if (obj.getDataAtualizacaoInformada()) {
                    obj.setResponsavelOperacao(getUsuarioLogado());
                    callableTasks.add(new ReplicarRedeEmpresaCallable(JSFUtilities.getRequest(),
                            JSFUtilities.getResponse(), ReplicarRedeEmpresaEnum.USUARIO, obj, null, null, null, null, null, null));
                    qtdThreads++;
                }
            }
            final ExecutorService executorService = Executors.newFixedThreadPool(qtdThreads);
            executorService.invokeAll(callableTasks);
            executorService.shutdown();
        } catch (Exception ex) {
            montarErro(ex);
        }
    }

    public void replicarTodas() {
        try {
            int qtdThreads = 0;
            List<ReplicarRedeEmpresaCallable> callableTasks = new ArrayList<>();
            for (UsuarioRedeEmpresaVO obj : getListaUsuarioRedeEmpresa()) {
                if (!obj.getDataAtualizacaoInformada()) {
                    obj.setResponsavelOperacao(getUsuarioLogado());
                    obj.setSelecionado(true);
                    callableTasks.add(new ReplicarRedeEmpresaCallable(JSFUtilities.getRequest(),
                            JSFUtilities.getResponse(), ReplicarRedeEmpresaEnum.USUARIO, obj, null, null, null, null, null, null));
                    qtdThreads++;
                }
            }
            final ExecutorService executorService = Executors.newFixedThreadPool(qtdThreads);
            executorService.invokeAll(callableTasks);
            executorService.shutdown();
        } catch (Exception ex) {
            montarErro(ex);
        }
    }

    public void replicarSelecionadas() {
        try {
            int qtdThreads = 0;
            List<ReplicarRedeEmpresaCallable> callableTasks = new ArrayList<>();
            for (UsuarioRedeEmpresaVO obj : getListaUsuarioRedeEmpresa()) {
                if (obj.isSelecionado()) {
                    obj.setResponsavelOperacao(getUsuarioLogado());
                    callableTasks.add(new ReplicarRedeEmpresaCallable(JSFUtilities.getRequest(),
                            JSFUtilities.getResponse(), ReplicarRedeEmpresaEnum.USUARIO, obj, null, null, null, null, null, null));
                    qtdThreads++;
                }
            }
            final ExecutorService executorService = Executors.newFixedThreadPool(qtdThreads);
            executorService.invokeAll(callableTasks);
            executorService.shutdown();
        } catch (Exception ex) {
            montarErro(ex);
        }
    }

    public void limparReplicar() {
        for(UsuarioRedeEmpresaVO obj : getListaUsuarioRedeEmpresa()){
            obj.setSelecionado(false);
        }
    }

    public void replicarUsuarioRedeEmpresaGeral() {
        UsuarioRedeEmpresaVO obj = (UsuarioRedeEmpresaVO) context().getExternalContext().getRequestMap().get("usuarioRedeEmpresaReplicacao");
        try {
            obj.setResponsavelOperacao(getUsuarioLogado());
        } catch (Exception ex) {
        }
        replicarUsuarioRedeEmpresaUnica(obj);
    }

    public void retirarVinculoReplicacao() {
        limparMsg();
        UsuarioRedeEmpresaVO obj = (UsuarioRedeEmpresaVO) context().getExternalContext().getRequestMap().get("usuarioRedeEmpresaReplicacao");
        try {
            obj.setDataatualizacao(null);
            obj.setMensagemSituacao("AGUARDANDO REPLICAR USUARIO");
            getFacade().getUsuarioRedeEmpresa().limparDataAtualizacao(usuarioVO.getCodigo(), getKey(), obj.getRedeDTO().getChave(), obj.getRedeDTO().getEmpresaZw());
            getFacade().getUsuarioRedeEmpresa().alterarMensagemSituacao(usuarioVO.getCodigo(), getKey(), obj.getRedeDTO().getChave(), obj.getMensagemSituacao(), obj.getRedeDTO().getEmpresaZw());

        } catch (Exception ex) {
            setSucesso(false);
            setErro(true);
            setMensagemDetalhada(ex);
        }
    }

    public void replicarUsuarioRedeEmpresaUnica(UsuarioRedeEmpresaVO obj) {
        List<RedeDTO> listaUnidadesComMesmaChave = null;
        boolean possuiUsuarioReplicadoEmOutraUnidade = false;
        RedeDTO empresaZW1 = null;
        try {
            RedeEmpresaDataDTO redeEmpresaDataDTO = OamdMsService.urlsChaveRedeEmpresa(getKey());
            listaUnidadesComMesmaChave = obterUnidadesComMesmaChave(redeEmpresaDataDTO, obj);
            if(listaUnidadesComMesmaChave != null) {
                /*
                  caso haja multiplas empresa_financeiro (unidades) configurados com a mesma chave:
                    - validar se o usuário já foi replicado em uma das unidades;
                    - caso ja tenha sido replicado, replicar apenas o perfilacesso;
                */
                JSONArray listaUsuarioJaReplicado = PessoaMsService.obterUsuarioPorNomeUsuario(
                            usuarioVO.getNome(), obj.getRedeDTO().getPessoaMsUrl(), obj.getRedeDTO().getChave()
                    );

                    if (listaUsuarioJaReplicado != null && listaUsuarioJaReplicado.length() > 0) {
                        possuiUsuarioReplicadoEmOutraUnidade = true;
                    }

                if (possuiUsuarioReplicadoEmOutraUnidade) {
                    for (RedeDTO ecmc : listaUnidadesComMesmaChave) {
                        if (ecmc.getEmpresaZw() != null && ecmc.getEmpresaZw() == 1) {
                            empresaZW1 = ecmc;
                        }
                    }
                    setReplicarUsuarioPerfilAcesso(true);
                    replicarSomentePerfilAcesso(obj, empresaZW1);
                } else {
                    replicarUsuarioRedeEmpresa(obj);
                }

            }  else {
                replicarUsuarioRedeEmpresa(obj);
            }

            limparMsg();
        } catch (Exception ex) {
            setSucesso(false);
            setErro(true);
            setMensagemDetalhada(ex);
            obj.setMensagemSituacao("ERRO: " + ex.getMessage());
            try {
                getFacade().getUsuarioRedeEmpresa().alterarMensagemSituacao(usuarioVO.getCodigo(), getKey(), obj.getRedeDTO().getChave(), obj.getMensagemSituacao(), obj.getRedeDTO().getEmpresaZw());
                if (obj.getResponsavelOperacao() != null && !UteisValidacao.emptyNumber(obj.getResponsavelOperacao().getCodigo()) &&
                        !UteisValidacao.emptyString(obj.getResponsavelOperacao().getEmail())) {
                    UteisEmail.enviarEmailRobo(obj.getResponsavelOperacao().getEmail(), "Sistema Pacto", "",
                            ("ERRO AO REPLICAR USUÁRIO " + usuarioVO.getNome().toUpperCase()),
                            "Erro ao tentar replicar o usuário " + usuarioVO.getNome().toUpperCase() + " para empresa " + obj.getNomeUnidade().toUpperCase() + " Motivo: " + obj.getMensagemSituacao(), "");
                }
            } catch (Exception e) {
            }
            Logger.getLogger(UsuarioControle.class.getName()).log(Level.SEVERE, null, ex);
        }
    }

    public void replicarUsuarioRedeEmpresa(UsuarioRedeEmpresaVO obj) throws Exception {
        RedeEmpresaDataDTO redeEmpresaDataDTO = OamdMsService.urlsChaveRedeEmpresa(getKey());

        UsuarioRedeEmpresaVO usuarioRedeEmpresaVO = getFacade().getUsuarioRedeEmpresa().consultarPorChaveEmpresaUsuario(obj.getChaveDestino(), obj.getRedeDTO().getEmpresaZw(), usuarioVO.getCodigo());
        if (usuarioRedeEmpresaVO == null) {
            String urlOrigemPessoaMs = redeEmpresaDataDTO.getServiceUrls().getPessoaMsUrl();

            JSONObject cloneUsuarioOrigem = PessoaMsService.clonarUsuario(usuarioVO.getCodigo(), urlOrigemPessoaMs, getKey());
            if (Uteis.isAmbienteDesenvolvimentoTeste()) {
                cloneUsuarioOrigem.put("username", "CÓPIA DE " + cloneUsuarioOrigem.getString("username").toUpperCase().replace("CÓPIA DE ", ""));
            }
            usuarioRedeEmpresaVO = new UsuarioRedeEmpresaVO(usuarioVO.getCodigo(), getKey(), obj.getChaveDestino(), obj.getRedeDTO().getEmpresaZw());
            getFacade().getUsuarioRedeEmpresa().inserir(usuarioRedeEmpresaVO);
            ColaboradorRedeEmpresaVO colaboradorRedeEmpresaVO = new ColaboradorRedeEmpresaVO(usuarioVO.getColaboradorVO().getCodigo(), getKey(), obj.getChaveDestino(), obj.getRedeDTO().getEmpresaZw());
            getFacade().getColaboradorRedeEmpresa().inserir(colaboradorRedeEmpresaVO);
            // Perfil não tem na outra academia da rede, então inclui

            JSONObject usuarioReplicado = PessoaMsService.replicarUsuario(cloneUsuarioOrigem, obj.getRedeDTO().getPessoaMsUrl(), obj.getRedeDTO().getChave(), obj.getRedeDTO().getEmpresaZw().toString());
            usuarioRedeEmpresaVO.setChaveDestino(usuarioReplicado.getString("chaveDestino"));
            usuarioRedeEmpresaVO.setEmpresaDestino(obj.getRedeDTO().getEmpresaZw());
            usuarioRedeEmpresaVO.setDataatualizacao(new Date());
            usuarioRedeEmpresaVO.setMensagemSituacao("REPLICADO EM " + Uteis.getDataComHora(usuarioRedeEmpresaVO.getDataatualizacao()));
            usuarioRedeEmpresaVO.setUsuarioReplicado(usuarioReplicado.getInt("codigoUsuarioNovo"));
            obj.setChaveDestino(usuarioReplicado.getString("chaveDestino"));
            obj.setDataatualizacao(new Date());
            obj.setMensagemSituacao("REPLICADO EM " + Uteis.getDataComHora(obj.getDataatualizacao()));
            getFacade().getUsuarioRedeEmpresa().alterarDataAtualizacao(usuarioVO.getCodigo(), getKey(), usuarioReplicado.getString("chaveDestino"), obj.getMensagemSituacao(), usuarioReplicado.getInt("codigoUsuarioNovo"), obj.getRedeDTO().getEmpresaZw());
            getFacade().getColaboradorRedeEmpresa().alterarDataAtualizacao(usuarioVO.getColaboradorVO().getCodigo(), getKey(), usuarioReplicado.getString("chaveDestino"), obj.getMensagemSituacao(), usuarioReplicado.getInt("codigoColaboradorNovo"), obj.getRedeDTO().getEmpresaZw());
            try {
                processarUsuarioRede(obj.getRedeDTO().getEmpresaZw(), usuarioReplicado.getInt("codigoUsuarioNovo"),
                        getIpCliente(), usuarioReplicado.getString("usuarioGeral"), usuarioReplicado.getString("chaveDestino"), getIntegracaoNovoLogin());
            } catch (Exception ex) {
                ex.getStackTrace();
            }
        } else {
            String urlOrigemPessoaMs = redeEmpresaDataDTO.getServiceUrls().getPessoaMsUrl();

            JSONObject cloneFeriadoOrigem = PessoaMsService.clonarUsuario(usuarioVO.getCodigo(), urlOrigemPessoaMs, getKey());
            if (Uteis.isAmbienteDesenvolvimentoTeste()) {
                cloneFeriadoOrigem.put("username", "CÓPIA DE " + cloneFeriadoOrigem.getString("username").toUpperCase().replace("CÓPIA DE ", ""));
            }
            JSONObject usuarioReplicado = PessoaMsService.replicarUsuario(cloneFeriadoOrigem, obj.getRedeDTO().getPessoaMsUrl(), obj.getRedeDTO().getChave(), obj.getRedeDTO().getEmpresaZw().toString());
            usuarioRedeEmpresaVO.setChaveDestino(usuarioReplicado.getString("chaveDestino"));
            usuarioRedeEmpresaVO.setEmpresaDestino(obj.getRedeDTO().getEmpresaZw());
            usuarioRedeEmpresaVO.setDataatualizacao(new Date());
            usuarioRedeEmpresaVO.setMensagemSituacao("REPLICADO EM " + Uteis.getDataComHora(usuarioRedeEmpresaVO.getDataatualizacao()));
            usuarioRedeEmpresaVO.setUsuarioReplicado(usuarioReplicado.getInt("codigoUsuarioNovo"));
            obj.setChaveDestino(usuarioReplicado.getString("chaveDestino"));
            obj.setDataatualizacao(new Date());
            obj.setMensagemSituacao("REPLICADO EM " + Uteis.getDataComHora(obj.getDataatualizacao()));
            getFacade().getUsuarioRedeEmpresa().alterarDataAtualizacao(usuarioVO.getCodigo(), getKey(), usuarioReplicado.getString("chaveDestino"), obj.getMensagemSituacao(), usuarioReplicado.getInt("codigoUsuarioNovo"), obj.getRedeDTO().getEmpresaZw());
            ColaboradorRedeEmpresaVO colaboradorRedeEmpresaVO = getFacade().getColaboradorRedeEmpresa().consultarPorChaveEmpresaColaborador(obj.getChaveDestino(), obj.getRedeDTO().getEmpresaZw(), usuarioVO.getColaboradorVO().getCodigo());
            if (isNull(colaboradorRedeEmpresaVO)) {
                colaboradorRedeEmpresaVO = new ColaboradorRedeEmpresaVO(usuarioVO.getColaboradorVO().getCodigo(), getKey(), obj.getChaveDestino(), obj.getRedeDTO().getEmpresaZw());
                getFacade().getColaboradorRedeEmpresa().inserir(colaboradorRedeEmpresaVO);
            }
            getFacade().getColaboradorRedeEmpresa().alterarDataAtualizacao(usuarioVO.getColaboradorVO().getCodigo(), getKey(), usuarioReplicado.getString("chaveDestino"), obj.getMensagemSituacao(), usuarioReplicado.getInt("codigoColaboradorNovo"), obj.getRedeDTO().getEmpresaZw());
            try {
                processarUsuarioRede(obj.getRedeDTO().getEmpresaZw(), usuarioReplicado.getInt("codigoUsuarioNovo"),
                        getIpCliente(), usuarioReplicado.getString("usuarioGeral"), usuarioReplicado.getString("chaveDestino"), getIntegracaoNovoLogin());
            } catch (Exception ex) {
                ex.getStackTrace();
            }
        }


    }



    private List<RedeDTO> obterUnidadesComMesmaChave(
            RedeEmpresaDataDTO redeEmpresaDataDTO, UsuarioRedeEmpresaVO usuarioRedeEmpresaVO) {
        RedeDTO redeUsuarioSendoReplicado = usuarioRedeEmpresaVO.getRedeDTO();
        List<RedeDTO> redeEmpresas = redeEmpresaDataDTO.getRedeEmpresas();
        List<RedeDTO> empresasComMesmaChave = new ArrayList<>();

        for (RedeDTO rede : redeEmpresas) {
            if (rede.getChave() != null && rede.getChave().equals(redeUsuarioSendoReplicado.getChave())) {
                empresasComMesmaChave.add(rede);
            }
        }

        if (!UteisValidacao.emptyList(empresasComMesmaChave) && empresasComMesmaChave.size() > 1) {
            return empresasComMesmaChave;
        }
        return null;

    }

    private void replicarSomentePerfilAcesso(UsuarioRedeEmpresaVO usuarioRedeEmpresaVO, RedeDTO empresaZW1) {
        String MSG_SITUACAO_PREFIXO = "Usuário já existe na chave " + empresaZW1.getChave();
        String MSG_SITUACAO_SUCESSO = "Perfil de Acesso foi replicado ao invés do usuário";
        String MSG_SITUACAO_ERRO = "Ocorreu um erro ao tentar replicar Perfil de Acesso ao invés do usuário: ";

        try {
            PerfilAcessoControle perfilAcessoControle = (PerfilAcessoControle) getControlador(PerfilAcessoControle.class);
            List<UsuarioPerfilAcessoVO> listaUsuarioPerfilAcesso = getFacade().getUsuarioPerfilAcesso()
                    .consultarUsuarioPerfilAcesso(usuarioVO.getCodigo(), Uteis.NIVELMONTARDADOS_TODOS);

            for (UsuarioPerfilAcessoVO upaVo : listaUsuarioPerfilAcesso) {
                PerfilAcessoRedeEmpresaVO perfilAcessoRedeEmpresaVO = getFacade()
                        .getPerfilAcessoRedeEmpresa()
                        .consultarPorChavePerfilAcesso(empresaZW1.getChave(), upaVo.getPerfilAcesso().getCodigo());

                if (perfilAcessoRedeEmpresaVO == null) {
                    perfilAcessoRedeEmpresaVO = new PerfilAcessoRedeEmpresaVO();
                }

                perfilAcessoRedeEmpresaVO.setChaveDestino(empresaZW1.getChave().toLowerCase().trim());
                perfilAcessoRedeEmpresaVO.setNomeUnidade(empresaZW1.getNomeFantasia());
                perfilAcessoRedeEmpresaVO.setRedeDTO(empresaZW1);

                perfilAcessoControle.setPerfilAcessoVO(upaVo.getPerfilAcesso());

                perfilAcessoControle.replicarPerfilAcessoRedeEmpresaUnica(perfilAcessoRedeEmpresaVO);

                boolean sucessoReplicandoPerfilAcesso =  perfilAcessoRedeEmpresaVO.getMensagemSituacao().contains("REPLICADO EM");

                if (sucessoReplicandoPerfilAcesso) {
                    iniciarReplicacaoUsuarioPerfilAcesso(usuarioRedeEmpresaVO);
                    usuarioRedeEmpresaVO.setDataatualizacao(new Date());
                    usuarioRedeEmpresaVO.setMensagemSituacao(MSG_SITUACAO_PREFIXO + " - " + MSG_SITUACAO_SUCESSO + " em " + Uteis.getDataComHora(usuarioRedeEmpresaVO.getDataatualizacao()));
                    getFacade().getUsuarioRedeEmpresa().alterarMensagemSituacao(
                            usuarioVO.getCodigo(),
                            usuarioRedeEmpresaVO.getChaveOrigem(),
                            usuarioRedeEmpresaVO.getChaveDestino(),
                            usuarioRedeEmpresaVO.getMensagemSituacao(),
                            empresaZW1.getEmpresaZw()
                    );
                    setSucesso(true);
                } else {
                    usuarioRedeEmpresaVO.setMensagemSituacao(MSG_SITUACAO_PREFIXO + " - " + MSG_SITUACAO_ERRO + perfilAcessoRedeEmpresaVO.getMensagemSituacao());
                }

                return;
            }
        } catch (Exception ex) {
            usuarioRedeEmpresaVO.setMensagemSituacao(MSG_SITUACAO_PREFIXO + " - " + MSG_SITUACAO_ERRO + ex.getMessage());
            ex.printStackTrace();
        }
    }

    public boolean iniciarReplicacaoUsuarioPerfilAcesso(UsuarioRedeEmpresaVO usuarioRedeEmpresaVO) {

        try {
            List<UsuarioPerfilAcessoVO> listaUsuarioPerfilAcesso =
                    getFacade().getUsuarioPerfilAcesso().consultarUsuarioPerfilAcesso(usuarioVO.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            UsuarioPerfilAcessoVO usuarioPerfilAcessoVO1 = listaUsuarioPerfilAcesso.get(0);

            JSONObject usuarioPerfilAcessoJson = new JSONObject();
            usuarioPerfilAcessoJson.put("codigo", usuarioPerfilAcessoVO1.getCodigo());
            usuarioPerfilAcessoJson.put("usuario", usuarioPerfilAcessoVO1.getUsuario());
            usuarioPerfilAcessoJson.put("empresa", usuarioPerfilAcessoVO1.getEmpresa().getCodigo());
            usuarioPerfilAcessoJson.put("perfilAcesso", usuarioPerfilAcessoVO1.getPerfilAcesso().getCodigo());
            usuarioPerfilAcessoJson.put("usuario_nome", usuarioVO.getNome());
            usuarioPerfilAcessoJson.put("perfilAcesso_nome", usuarioPerfilAcessoVO1.getPerfilAcesso().getNome());
            usuarioPerfilAcessoJson.put("codigoEmpresaDestino", usuarioRedeEmpresaVO.getRedeDTO().getEmpresaZw());

            PessoaMsService.replicarUsuarioPerfilAcesso(usuarioPerfilAcessoJson, usuarioRedeEmpresaVO.getRedeDTO().getPessoaMsUrl(), usuarioRedeEmpresaVO.getRedeDTO().getChave());
            return true;
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    };


    public List<String> getListaCamposMostrarColaboradorDinamico() {
        return listaCamposMostrarColaboradorDinamico;
    }

    public void setListaCamposMostrarColaboradorDinamico(List<String> listaCamposMostrarColaboradorDinamico) {
        this.listaCamposMostrarColaboradorDinamico = listaCamposMostrarColaboradorDinamico;
    }

    public List<String> getListaCamposObrigatorioColaboradorDinamico() {
        return listaCamposObrigatorioColaboradorDinamico;
    }

    public void setListaCamposObrigatorioColaboradorDinamico(List<String> listaCamposObrigatorioColaboradorDinamico) {
        this.listaCamposObrigatorioColaboradorDinamico = listaCamposObrigatorioColaboradorDinamico;
    }

    public ColaboradorVO getColaboradorVO() {
        if (colaboradorVO == null) {
            colaboradorVO = new ColaboradorVO();
        }
        return colaboradorVO;
    }

    public void setColaboradorVO(ColaboradorVO colaboradorVO) {
        this.colaboradorVO = colaboradorVO;
    }

    public PessoaVO getPessoaVO() {
        if (pessoaVO == null) {
            pessoaVO = new PessoaVO();
        }
        return pessoaVO;
    }

    public void setPessoaVO(PessoaVO pessoaVO) {
        this.pessoaVO = pessoaVO;
    }

    public EnderecoVO getEnderecoVO() {
        if (enderecoVO == null) {
            enderecoVO = new EnderecoVO();
        }
        return enderecoVO;
    }

    public void setEnderecoVO(EnderecoVO enderecoVO) {
        this.enderecoVO = enderecoVO;
    }

    public CepControle getCepControle() {
        return cepControle;
    }

    public void setCepControle(CepControle cepControle) {
        this.cepControle = cepControle;
    }

    public void montarMsgGenerica(String titulo, String msg, String botaoFechar, String onCompleteFechar, String reRender) {
        MensagemGenericaControle control = (MensagemGenericaControle) getControlador(MensagemGenericaControle.class);
        control.setMensagemDetalhada("", "");
        control.setMensagemApresentar("");
        setModalMensagemGenerica("Richfaces.showModalPanel('mdlMensagemGenerica');");
        control.init(titulo, msg, this, botaoFechar, onCompleteFechar, reRender + ",mdlMensagemGenerica");
    }

    public String getModalMensagemGenerica() {
        return modalMensagemGenerica;
    }

    public void setModalMensagemGenerica(String modalMensagemGenerica) {
        this.modalMensagemGenerica = modalMensagemGenerica;
    }

    public String getPin() {
        if(pin == null)
            pin = "";
        return pin;
    }

    public void setPin(String pin) {
        this.pin = pin;
    }

    public String gravarMeusDados() {
        try {
            if (!UteisValidacao.emptyString(getPin())) {
                getUsuarioVO().setPin(getPin());
                getUsuarioVO().setAlterouPIN(true);
            }
            if (getPessoaVO().getNome().isEmpty()) {
                throw new Exception("Obrigatório informar o Nome.");
            }
            if (getPessoaVO().getCfp().isEmpty()) {
                throw new Exception("Obrigatório informar o CPF.");
            }
            if (getPessoaVO().getDataNasc() == null) {
                throw new Exception("Obrigatório informar a Data de Nascimento.");
            }
            if (UteisValidacao.emptyString(getUsuarioVO().getEmailVO().getEmail().trim())) {
                throw new Exception("Obrigatório informar o E-mail.");
            }
            if (!UteisValidacao.validaEmail(getUsuarioVO().getEmailVO().getEmail().trim())) {
                throw new Exception("E-mail inválido!");
            }
//            if (getUsername().isEmpty()) {
//                throw new Exception("Obrigatório o Username (Nome de Usuário).");
//            }
//
//            boolean existeUsuarioMesmoUsername = getFacade().getUsuario().consultarPorUsernameEDiferenteDoUsuario(getUsername(), getUsuarioVO().getCodigo());
//            if (existeUsuarioMesmoUsername) {
//                throw new Exception("Já existe outro usuário com o mesmo Username (Nome de Usuário).");
//            }

            //ALTERAR O USUÁRIO
            getFacade().getUsuario().alterarSemPermissao(getUsuarioVO());

            getColaboradorVO().setPessoa(getPessoaVO());
            getUsuarioVO().getEmailVO().setPessoa(getColaboradorVO().getPessoa().getCodigo());
            getColaboradorVO().getPessoa().adicionarObjEmailVOs(getUsuarioVO().getEmailVO());

            //ALTERAR O COLABORADOR
            getFacade().getColaborador().alterarSemPermissao(getUsuarioVO().getColaboradorVO());

            sincronizarUsuarioMovel(true , getKey());

            montarMsgGenerica("Meus Dados", "Dados alterados com sucesso.", "Ok", "window.close();fireElementFromParent('form:btnAtualizaFotoUsuario');return false;", "");
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
            montarMsgGenerica("Meus Dados", e.getMessage(), "Fechar", "", "");
        }
        return "";
    }

    public void adicionarEndereco() throws Exception {
        try {
            if (!getPessoaVO().getCodigo().equals(0)) {
                getEnderecoVO().setPessoa(getPessoaVO().getCodigo());
            }
            getPessoaVO().adicionarObjEnderecoVOs(getEnderecoVO());
            this.setEnderecoVO(new EnderecoVO());
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
            setSucesso(false);
            setAtencao(false);
            setErro(true);
        }
    }

    public void editarEndereco() throws Exception {
        EnderecoVO obj = (EnderecoVO) context().getExternalContext().getRequestMap().get("endereco");
        setEnderecoVO(obj);
    }

    public void removerEndereco() throws Exception {
        EnderecoVO obj = (EnderecoVO) context().getExternalContext().getRequestMap().get("endereco");
        getPessoaVO().excluirObjEnderecoVOs(obj);
    }


    public void selecionarCep() {
        // usa a variável de tela
        CepVO obj = (CepVO) context().getExternalContext().getRequestMap().get("cep");
        // atualiza os campos correspondentes
        getEnderecoVO().setCep(obj.getEnderecoCep());
        getEnderecoVO().setEndereco(obj.getEnderecoLogradouro());
        getEnderecoVO().setBairro(obj.getBairroDescricao());
    }

    public List getListaSelectItemTipoEnderecoEndereco() throws Exception {
        List<SelectItem> objs = new ArrayList();
        objs.add(new SelectItem("", ""));
        for (TipoEnderecoEnum obj : TipoEnderecoEnum.values()) {
            String value = obj.getCodigo();
            String label = obj.getDescricao();
            objs.add(new SelectItem(value, label));
        }
        SelectItemOrdemValor ordenador = new SelectItemOrdemValor();
        Collections.sort((List) objs, ordenador);
        return objs;
    }

    public void consultarCEPCadastroCompleto() throws Exception {
        try {
            getCepControle().consultarCEPCadastroCompleto(getEnderecoVO().getCep());
            getEnderecoVO().setBairro(getCepControle().getCepVO().getBairroDescricao().trim());
            getEnderecoVO().setEndereco(getCepControle().getCepVO().getEnderecoLogradouro().trim());
            getEnderecoVO().setComplemento(getCepControle().getCepVO().getEnderecoCompleto().trim());
            setMensagemDetalhada("");
            setMensagemID("");
            setMensagem("");
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }
    public List<SelectItem> getListaSelectItemRgUfPessoa() throws Exception {
        List<SelectItem> objs = new ArrayList<SelectItem>();
        objs.add(new SelectItem("", ""));
        Hashtable estados = (Hashtable) Dominios.getEstado();
        Enumeration keys = estados.keys();
        while (keys.hasMoreElements()) {
            String value = (String) keys.nextElement();
            String label = (String) estados.get(value);
            objs.add(new SelectItem(value, label));
        }

        SelectItemOrdemValor ordenador = new SelectItemOrdemValor();
        Collections.sort((List<SelectItem>) objs, ordenador);
        return objs;
    }

    public List<SelectItem> getLocales(){
        List<SelectItem> is = new ArrayList<>();
        is.add(new SelectItem("", ""));
        for(LocaleEnum le : LocaleEnum.values()){
            is.add(new SelectItem(le.getLocalize(), le.getDescricao()));
        }
        return is;
    }

    public void recarregarFotoMeusDados() throws Exception {
        if (isFotosNaNuvem()) {
            final String fotoKey = getFacade().getPessoa().obterFotoKey(
                    getUsuarioVO().getColaboradorVO().getPessoa().getCodigo());
            if (!UteisValidacao.emptyString(fotoKey)) {
                getUsuarioVO().getColaboradorVO().getPessoa().setFotoKey(fotoKey + "?time=" + getTimeStamp());
            }
        } else {
            getUsuarioVO().getColaboradorVO().getPessoa().setFoto(getFacade().getPessoa().obterFoto(
                    getKey(),
                    getUsuarioVO().getColaboradorVO().getPessoa().getCodigo()));
        }
    }

    public void paintFotoMeusDados(OutputStream out, Object data) throws IOException, Exception {
        if (getUsuarioVO().getColaboradorVO().getPessoa().getFoto() == null || getUsuarioVO().getColaboradorVO().getPessoa().getFoto().length == 0) {
            recarregarFoto();
        }
        SuperControle.paintFoto(out, getUsuarioVO().getColaboradorVO().getPessoa().getFoto());
    }

    public String getPaintFotoDaNuvemMeusDados() {
        return getPaintFotoDaNuvem(getUsuarioVO().getColaboradorVO().getPessoa().getFotoKey());
    }

    public void verificaColaboradorMeusDados() {
        setOnComplete(String.format("setAttributesModalCapFoto('%s', '%s', '%s', '');"
                        + "Richfaces.showModalPanel('modalCapFotoHTML5');",
                getKey(), getUsuarioVO().getColaboradorVO().getPessoa().getCodigo(),
                request().getContextPath()));
    }

    public void removerFotoMeusDados() {
        try {
            getFacade().getPessoa().removerFoto(getKey(), getUsuarioVO().getColaboradorVO().getPessoa().getCodigo());
            getUsuarioVO().getColaboradorVO().getPessoa().setFoto(null);
            getUsuarioVO().getColaboradorVO().getPessoa().setFotoKey(null);
            setMensagem("msg_dados_excluidos");
            setSucesso(true);
        } catch (Exception e) {
            setErro(true);
            setMensagem(e.getMessage());

        }
    }

    private void buscarUsuarioMesmoEmail() {
        try {
            List<UsuarioGeralDTO> usuariosMesmoEmail = SincronizarUsuarioNovoLogin.buscaUsuarioMesmoEmail(getKey(), getUsuarioVO().getUsuarioGeral(), this.getUsuarioEmailAlterarVO().getEmail());
            if (!UteisValidacao.emptyList(usuariosMesmoEmail)) {
                setOnComplete("Richfaces.showModalPanel('mdlDadosAcessosEmailInformado');");
            } else {
                solicitaEmail();
            }
        } catch (Exception e) {
            e.printStackTrace();
            montarErro(e);
        }
    }

    public void trocarEmailRedefinindoSenhaEVinculandoDados() {
        try {
            limparMsg();
            setOnComplete("");
            solicitaEmail(true);
            setOnComplete("Richfaces.hideModalPanel('mdlDadosAcessosEmailInformado');Richfaces.hideModalPanel('modalAlterarEmail');");
            montarSucessoGrowl("Acesse seu e-mail para prosseguir com a alteração e vinculação com os dados!");
            setMsgAlert("form");
        } catch (Exception e) {
            e.printStackTrace();
            montarErro(e);
        }
    }

    public void apresentarModalEscolhaOutroEmail() {
        limparMsg();
        montarInfo("Neste caso será necessário informar outro e-mail!");
        setOnComplete("Richfaces.hideModalPanel('mdlDadosAcessosEmailInformado');");
        setMsgAlert("form");
    }

    public void solicitaEmail() throws Exception {
        solicitaEmail(false);
    }

    public void solicitaEmail(boolean trocaEmailVincularDados) throws Exception {
        String token;
        if (!isUsuarioGeralSincronizado()) {
            token = SincronizarUsuarioNovoLogin.enviarEmailNovoUsuario(this.getUsuarioVO().getCodigo(), false, true,
                    this.getUsuarioEmailAlterarVO().getEmail(), Conexao.getFromSession(), getEmpresaLogado().getCodigo(), getUsuarioLogado(),
                    getIpCliente(), getIntegracaoNovoLogin());
        } else {
            token = SincronizarUsuarioNovoLogin.solicitarTrocaEmail(this.getUsuarioVO().getCodigo(),
                    true, false, this.getUsuarioEmailAlterarVO(), Conexao.getFromSession(),
                    getEmpresaLogado().getCodigo(), getUsuarioLogado(), getIpCliente(), getIntegracaoNovoLogin(), trocaEmailVincularDados);
        }
        this.getTokenDTO().setToken(token);
        this.getTokenDTO().setUsuarioGeral(this.getUsuarioVO().getUsuarioGeral());
        if (!trocaEmailVincularDados) {
            setOnComplete("document.getElementById('formEmailLogin:usuarioTokenEmailLogin').focus();");
        }
        montarSucessoGrowl("E-mail enviado");
    }

    public Boolean getMostrarPanelFotoMeusDados() {
        return getUsuarioVO().getColaboradorVO().getPessoa().getCodigo() != null && getUsuarioVO().getColaboradorVO().getPessoa().getCodigo() != 0;
    }

    public List<UsuarioRedeEmpresaVO> getListaUsuarioRedeEmpresa() {
        if (listaUsuarioRedeEmpresa == null) {
            listaUsuarioRedeEmpresa = new ArrayList<>();
        }
        return listaUsuarioRedeEmpresa;
    }

    public void setListaUsuarioRedeEmpresa(List<UsuarioRedeEmpresaVO> listaUsuarioRedeEmpresa) {
        this.listaUsuarioRedeEmpresa = listaUsuarioRedeEmpresa;
    }

    public Boolean getApresentarCampoExcluirDados() {
        return apresentarCampoExcluirDados;
    }

    public void setApresentarCampoExcluirDados(Boolean apresentarCampoExcluirDados) {
        this.apresentarCampoExcluirDados = apresentarCampoExcluirDados;
    }

    public Integer getCodPerfilTw() {
        return codPerfilTw;
    }

    public void setCodPerfilTw(Integer codPerfilTw) {
        this.codPerfilTw = codPerfilTw;
    }

    public boolean getExibirCodigoUsuarioGeral() {
        try {
            return !UteisValidacao.emptyString(getUsuarioVO().getUsuarioGeral()) && getUsuarioLogado().getUsuarioPactoSolucoes();
        } catch (Exception ex) {
            return false;
        }
    }

    public boolean isReplicarUsuarioPerfilAcesso() {
        return replicarUsuarioPerfilAcesso;
    }

    public void setReplicarUsuarioPerfilAcesso(boolean replicarUsuarioPerfilAcesso) {
        this.replicarUsuarioPerfilAcesso = replicarUsuarioPerfilAcesso;
    }

    public boolean isUsuarioOAMDpossuiPermissaoConfigurarEmergencia(){
        try {
            return getUsuarioLogado().isUsuarioLogadoPodeConfigurarEmergencia();
        } catch (Exception e){
            Uteis.logar("#### ERRO AO OBTER CONFIG OAMD PERMITE_CONFIGURAR_EMERGENCIA: ", e, this.getClass());
            return false;

        }

    }
}
