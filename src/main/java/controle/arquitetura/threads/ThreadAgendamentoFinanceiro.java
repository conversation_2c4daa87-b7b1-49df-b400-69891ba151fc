/*
 * To change this template, choose Too<PERSON> | Templates
 * and open the template in the editor.
 */

package controle.arquitetura.threads;

import br.com.pactosolucoes.enumeradores.OrigemCadEAlteracaoCliente;
import br.com.pactosolucoes.enumeradores.TipoOperacaoLancamento;
import negocio.comuns.arquitetura.LogVO;
import negocio.comuns.financeiro.CentroCustoTO;
import negocio.comuns.financeiro.MovContaRateioVO;
import negocio.comuns.financeiro.MovContaVO;
import negocio.comuns.financeiro.PlanoContaTO;

import java.sql.*;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

import negocio.comuns.financeiro.AgendamentoFinanceiroVO;
import negocio.comuns.financeiro.FormaPagamentoVO;
import negocio.comuns.financeiro.TipoDocumentoVO;
import negocio.comuns.financeiro.enumerador.FrequenciaAgendamento;
import negocio.comuns.financeiro.enumerador.LayoutDescricao;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.Log;
import negocio.facade.jdbc.basico.Empresa;
import negocio.facade.jdbc.crm.Feriado;

/**
 *
 * <AUTHOR>
 */
public class ThreadAgendamentoFinanceiro extends Thread {

    private transient Connection con;
    private String msgErro = "";
    private boolean terminouExecucao = false;
    private AgendamentoFinanceiroVO agendamentoFinanceiroVO; // Utilizado para gerar as parcelas do agendamento que o usuário acabou de incluir na tela de Lançamentos de Pagamento ou Recebimento
    private MovContaVO movContaVO; // Utilizado para gerar as parcelas do agendamento que o usuário acabou de incluir na tela de Lançamentos de Pagamento ou Recebimento
    private Calendar dataHoraAtual;
   

    public ThreadAgendamentoFinanceiro(Connection con, AgendamentoFinanceiroVO prAgendamentoFinanceiroVO, MovContaVO prMovContaVO, Calendar prDataHoraAtual){
        this.con = con;
        this.agendamentoFinanceiroVO = prAgendamentoFinanceiroVO;
        this.movContaVO = prMovContaVO;
        this.dataHoraAtual = prDataHoraAtual;
    }

    public ThreadAgendamentoFinanceiro(Connection con, Calendar prDataHoraAtual){
        this.con = con;
        this.dataHoraAtual = prDataHoraAtual;
    }

    public static void gerarParcelasParaAgendamento(Connection con, AgendamentoFinanceiroVO prAgendamentoFinanceiroVO, MovContaVO prMovContaVO, Calendar prDataHoraAtual) throws Exception {
        ThreadAgendamentoFinanceiro thread = new ThreadAgendamentoFinanceiro(con, prAgendamentoFinanceiroVO, prMovContaVO, prDataHoraAtual);
        thread.start();
        while (!thread.isTerminouExecucao()){
            // Ficar em loop até que a Thread termine a execução do processo.
            Thread.sleep(1000);
        }
        // Verifica se houve erro na execução da thread.
        if (!thread.getMsgErro().equals("")){
            throw new Exception(thread.getMsgErro());
        }
    }

    public static void gerarParcelasParaAgendamento(Connection con, Date prDataHoraAtual){
        Calendar data = Calendar.getInstance();
        data.setTime(prDataHoraAtual);
        ThreadAgendamentoFinanceiro thread = new ThreadAgendamentoFinanceiro(con, data);
        thread.start();
    }

    public void run() {
        try{
            Uteis.logar(null, String.format("$$$ iniciando processamento Agendamento Financeiro em %s $$$",
                    new Object[]{
                            Uteis.getDataComHora(Calendario.hoje())
                    }));
              if (this.agendamentoFinanceiroVO != null)  {
                /* Gerar parcelas para um único agendamento. É utilizado quando o usuário
                 * cria o agendamento, desta forma o sistema deverá gerar as primeiras parcelas
                 * de imediato.
                 *
                 */
                gerarParcelas(this.agendamentoFinanceiroVO, this.movContaVO,1);

              }else{
                 // Este método é executado uma vez por dia, através do Robô.
                 gerarParcelasParaTodosAgendamentos();
              }

        }catch (Exception e){
          this.msgErro = "Erro na execução da Thread 'ThreadAgendamentoFinanceiro'. " +
                          "  Classe do erro: " + e.getClass() + "  MsgErro: " +   e.getMessage();
          Uteis.logar(null, msgErro);
        } finally {
            this.terminouExecucao = true;
        }
        Uteis.logar(null, String.format("$$$ finalizando processamento Agendamento Financeiro em %s $$$",
                new Object[]{
                        Uteis.getDataComHora(Calendario.hoje())
                }));
      }


    private List<AgendamentoFinanceiroVO> montarDadosAgendamento(ResultSet resultDados) throws Exception{
        List<AgendamentoFinanceiroVO> lista = new ArrayList<AgendamentoFinanceiroVO>();
        while (resultDados.next()){
            AgendamentoFinanceiroVO obj = new  AgendamentoFinanceiroVO();
            obj.setNovoObj(false);
            obj.setCodigo(resultDados.getInt("codigo"));
            obj.setDescricao(resultDados.getString("descricao"));
            obj.setFrequencia(FrequenciaAgendamento.getFrequencia(resultDados.getInt("frequencia")));
            obj.setLayoutDescricao(LayoutDescricao.getLayoutDescricao(resultDados.getInt("layoutdescricao")));
            obj.setProximoVencimento(resultDados.getDate("proximovencimento"));
            obj.setVencimentoUltimaParcela(resultDados.getDate("vencimentoultimaparcela"));
            obj.setParcelaInicial(resultDados.getInt("parcelaini"));
            obj.setParcelaFinal(resultDados.getInt("parcelafim"));
            obj.setQtdeParcelasGerar(resultDados.getInt("qtdeParcelasGerar"));
            obj.setQtdeDiasNovaGeracao(resultDados.getInt("qtdeDiasNovaGeracao"));
            obj.setDiaVencimento(resultDados.getInt("diavencimento"));
            
            obj.setUsaParcelasFixas(resultDados.getBoolean("usaParcelasFixas"));
            obj.setDataLancamento(resultDados.getDate("datalancamento"));
        
            lista.add(obj);
        }

        return lista;

    }

    private MovContaVO montarDadosMovConta(ResultSet dadosSQL)throws Exception{
        MovContaVO obj = new MovContaVO();
        obj.setCodigo(dadosSQL.getInt("codigo"));
        obj.setDescricao(dadosSQL.getString("descricao"));
        obj.getPessoaVO().setCodigo(dadosSQL.getInt("pessoa"));
        obj.getUsuarioVO().setCodigo(dadosSQL.getInt("usuario"));
        obj.getEmpresaVO().setCodigo(dadosSQL.getInt("empresa"));
        obj.getContaVO().setCodigo(dadosSQL.getInt("conta"));
        obj.setObservacoes(dadosSQL.getString("observacoes"));
        obj.setValor(dadosSQL.getDouble("valor"));
        obj.setDataQuitacao(dadosSQL.getTimestamp("dataquitacao"));
        obj.setDataLancamento(dadosSQL.getTimestamp("datalancamento"));
        obj.setDataCompetencia(dadosSQL.getTimestamp("datacompetencia"));
        obj.setDataVencimento(dadosSQL.getTimestamp("datavencimento"));
        obj.setAgendamentoFinanceiro(dadosSQL.getInt("agendamentoFinanceiro"));
        obj.setNrParcela(dadosSQL.getInt("nrParcela"));
        obj.setTipoOperacaoLancamento(TipoOperacaoLancamento.getTipoOperacaoLancamento(dadosSQL.getInt("tipooperacao")));
        obj.setValorOriginalAlterado(dadosSQL.getDouble("valororiginalalterado"));
        obj.setNovoObj(false);

        // Consultar os dados da tabela MovContaRateio
        String sql = "select * from movContaRateio where MovConta = ? ";
        PreparedStatement pst = this.con.prepareStatement(sql);
        pst.setInt(1, obj.getCodigo());
        ResultSet resultDados = pst.executeQuery();
        obj.setMovContaRateios(montarDadosMovContaRateio(resultDados));


        return obj;

    }

    private List<MovContaRateioVO> montarDadosMovContaRateio(ResultSet dadosSQL)throws Exception{
        List<MovContaRateioVO> lista = new ArrayList<MovContaRateioVO>();
        while (dadosSQL.next()){
            MovContaRateioVO obj = new MovContaRateioVO();
            obj.setCodigo(new Integer(dadosSQL.getInt("codigo")));
            obj.setMovContaVO(dadosSQL.getInt("movconta"));
            obj.setPlanoContaVO(new PlanoContaTO());
            obj.getPlanoContaVO().setCodigo(dadosSQL.getInt("planoconta"));
            obj.setCentroCustoVO(new CentroCustoTO());
            obj.getCentroCustoVO().setCodigo(dadosSQL.getInt("centrocusto"));
            obj.setTipoDocumentoVO(new TipoDocumentoVO());
            obj.getTipoDocumentoVO().setCodigo(dadosSQL.getInt("tipodocumento"));
            obj.setFormaPagamentoVO(new FormaPagamentoVO());
            obj.getFormaPagamentoVO().setCodigo(dadosSQL.getInt("formapagamento"));
            obj.setTipoES(obj.getTipoES().getTipoPadrao(dadosSQL.getInt("tipoes")));
            obj.setDescricao(dadosSQL.getString("descricao"));
            obj.setValor(dadosSQL.getDouble("valor"));
            obj.setNovoObj(false);

           lista.add(obj);
        }
        return lista;
    }

    private MovContaVO retornarMovContaBaseParaAgendamento(int codigoAgendamento) throws Exception{
        MovContaVO objMovConta = null;
        String sql = "select * from movConta where agendamentoFinanceiro = ? order by nrparcela desc limit 1";
        PreparedStatement pstMovConta = this.con.prepareStatement(sql);
        pstMovConta.setInt(1, codigoAgendamento);
        ResultSet resultDadosMovConta = pstMovConta.executeQuery();
        if (resultDadosMovConta.next()){
           objMovConta =  montarDadosMovConta(resultDadosMovConta);
        }

        return objMovConta;
    }

    private void gerarParcelasParaTodosAgendamentos() throws Exception{
        /*
         * Gerar parcelas para todos os agendamentos cadastrados no banco de dados.
         */
        Uteis.logar(null, String.format("$$ # Consultando Agendamentos financeiros em %s # $$$",
                new Object[]{
                        Uteis.getDataComHora(Calendario.hoje())
                }));
        StringBuilder sql = new StringBuilder();
        sql.append("select * from agendamentoFinanceiro ");
        sql.append("where parcelaini is null and (proximovencimento < coalesce(vencimentoultimaparcela,proximovencimento + 1))  ");
        sql.append("and (? >= (proximovencimento - qtdediasnovageracao))  ");
        sql.append("AND (? < proximovencimento + INTERVAL '1' DAY)"); //garante que a renovação só ocorra quando a data atual for anterior ao próximo vencimento, prevenindo renovações desnecessárias diárias.
        PreparedStatement pstConsulta = this.con.prepareStatement(sql.toString());
        pstConsulta.setDate(1, Uteis.getDataJDBC(dataHoraAtual.getTime()));
        pstConsulta.setDate(2, Uteis.getDataJDBC(dataHoraAtual.getTime()));
        ResultSet resultDados = pstConsulta.executeQuery();
        List<AgendamentoFinanceiroVO> listaAgendamento = montarDadosAgendamento(resultDados);
        Uteis.logar(null, String.format("$$ # Agendamentos Encontratos %s em %s # $$$",
                new Object[]{
                        listaAgendamento.size(),
                        Uteis.getDataComHora(Calendario.hoje())
                }));
        MovContaVO objMovConta = null;
        for (AgendamentoFinanceiroVO agendamento : listaAgendamento){
            Uteis.logar(null, String.format("$$ # iniciando processamento Agendamento Financeiro %s em %s # $$$",
                    new Object[]{
                            agendamento.getCodigo(),
                            Uteis.getDataComHora(Calendario.hoje())
                    }));
            objMovConta = retornarMovContaBaseParaAgendamento(agendamento.getCodigo());
            if (objMovConta != null)
              gerarParcelas(agendamento, objMovConta, objMovConta.getNrParcela());
            Uteis.logar(null, String.format("$$ # terminando  processamento Agendamento Financeiro %s em %s # $$$",
                    new Object[]{
                            agendamento.getCodigo(),
                            Uteis.getDataComHora(Calendario.hoje())
                    }));
        }

    }

    public static String removePrefixo(String desc){
        String[] prefixos = new String[]{" REF. JAN/"," REF. FEV/"," REF. MARÇO/"," REF. ABRIL/"," REF. MAIO/"," REF. JUNHO/"
                ," REF. JULHO/"," REF. AGOSTO/"," REF. SET/"," REF. OUT/"," REF. NOV/"," REF. DEZ/"};
        for (String prefixo : prefixos) {
            int indexof = desc.indexOf(prefixo);
            if(indexof > 0) {
                desc = desc.substring(0, desc.indexOf(prefixo));
            }
        }

        return desc;

    }

    public static String retornarNomeMes(Date data){
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy");
        Calendar cal = Calendario.getInstance();
        cal.setTime(data);
        String ano = sdf.format(cal.getTime());
        switch (cal.get(Calendar.MONTH)){
            case 0: return " REF. JAN/" + ano ;
            case 1: return " REF. FEV/" + ano;
            case 2: return " REF. MARÇO/" + ano;
            case 3: return " REF. ABRIL/" + ano;
            case 4: return " REF. MAIO/" + ano;
            case 5: return " REF. JUNHO/" + ano;
            case 6: return " REF. JULHO/" + ano;
            case 7: return " REF. AGOSTO/" + ano;
            case 8: return " REF. SET/" + ano;
            case 9: return " REF. OUT/" + ano;
            case 10: return " REF. NOV/" + ano;
            case 11: return " REF. DEZ/" + ano;
            default: return " ";
        }
    }

    private int retornarUltimoCodigoTabela(String nomeTabela) throws Exception{
        String sql = "SELECT last_value FROM  " + nomeTabela + "_codigo_seq";
        Statement pstConsultar = this.con.createStatement();
        ResultSet dados = pstConsultar.executeQuery(sql);
        int codigo = 0;
        if (dados.next()){
           codigo= dados.getInt(1);
        }
        return codigo;
    }

    private MovContaVO incluirNovaParcela(MovContaVO prMovContaVOBase, int nrParcela, Date dataVencimento, Date competencia,
            String descricao,boolean novoAgendamento) throws Exception {
        // 1) Prepara INSERT com RETURN_GENERATED_KEYS
        String sql = "INSERT INTO MovConta("
                + "pessoa, usuario, empresa, conta, descricao, "
                + "observacoes, valor, dataLancamento, tipoOperacao, "
                + "dataVencimento, dataCompetencia, agendamentoFinanceiro, "
                + "nrParcela, valororiginalalterado"
                + ") VALUES (?,?,?,?,?,?,?,?,?,?,?,?,?,?)";

        try (PreparedStatement pst = con.prepareStatement(
                sql, Statement.RETURN_GENERATED_KEYS)) {

            // popula parâmetros
            pst.setInt(1, prMovContaVOBase.getPessoaVO().getCodigo());
            pst.setInt(2, prMovContaVOBase.getUsuarioVO().getCodigo());
            pst.setInt(3, prMovContaVOBase.getEmpresaVO().getCodigo());
            if (prMovContaVOBase.getContaVO() != null
                    && prMovContaVOBase.getContaVO().getCodigo() > 0) {
                pst.setInt(4, prMovContaVOBase.getContaVO().getCodigo());
            } else {
                pst.setNull(4, Types.NULL);
            }
            pst.setString(5, descricao);
            pst.setString(6, prMovContaVOBase.getObservacoes());
            pst.setDouble(7, prMovContaVOBase.getValor());
            pst.setTimestamp(8, novoAgendamento
                    ? Uteis.getDataJDBCTimestamp(prMovContaVOBase.getDataLancamento())
                    : Uteis.getDataJDBCTimestamp(Calendario.hoje()));
            pst.setInt(9, prMovContaVOBase.getTipoOperacaoLancamento().getCodigo());
            pst.setDate(10, Uteis.getDataJDBC(dataVencimento));
            pst.setDate(11, Uteis.getDataJDBC(competencia));
            pst.setInt(12, prMovContaVOBase.getAgendamentoFinanceiro());
            pst.setInt(13, nrParcela);
            pst.setDouble(14, prMovContaVOBase.getValorOriginalAlterado());

            pst.executeUpdate();

            // 2) Captura o ID gerado
            int codigoMovConta;
            try (ResultSet rs = pst.getGeneratedKeys()) {
                if (rs.next()) {
                    codigoMovConta = rs.getInt(1);
                } else {
                    throw new SQLException("Falha ao obter chave gerada para MovConta");
                }
            }

            // 3) Cria o VO de retorno e define o código
            MovContaVO movContaVORetornar = (MovContaVO) prMovContaVOBase.getClone(false);
            movContaVORetornar.setCodigo(codigoMovConta);

            // 4) Insere os rateios e anexa ao VO
            List<MovContaRateioVO> rateiosInseridos = new ArrayList<>();
            String sqlRateio = "INSERT INTO MovContaRateio("
                    + "movConta, planoConta, centroCusto, tipoDocumento, "
                    + "formaPagamento, tipoES, descricao, valor, numerodocumento"
                    + ") VALUES (?,?,?,?,?,?,?,?,?)";

            for (MovContaRateioVO obj : prMovContaVOBase.getMovContaRateios()) {
                try (PreparedStatement pstR = con.prepareStatement(sqlRateio)) {
                    pstR.setInt(1, codigoMovConta);
                    if (obj.getPlanoContaVO() != null && obj.getPlanoContaVO().getCodigo() > 0) {
                        pstR.setInt(2, obj.getPlanoContaVO().getCodigo());
                    } else {
                        pstR.setNull(2, Types.NULL);
                    }
                    if (obj.getCentroCustoVO() != null && obj.getCentroCustoVO().getCodigo() > 0) {
                        pstR.setInt(3, obj.getCentroCustoVO().getCodigo());
                    } else {
                        pstR.setNull(3, Types.NULL);
                    }
                    if (obj.getTipoDocumentoVO() != null && obj.getTipoDocumentoVO().getCodigo() > 0) {
                        pstR.setInt(4, obj.getTipoDocumentoVO().getCodigo());
                    } else {
                        pstR.setNull(4, Types.NULL);
                    }
                    if (obj.getFormaPagamentoVO() != null && obj.getFormaPagamentoVO().getCodigo() > 0) {
                        pstR.setInt(5, obj.getFormaPagamentoVO().getCodigo());
                    } else {
                        pstR.setNull(5, Types.NULL);
                    }
                    pstR.setInt(6, obj.getTipoES().getCodigo());
                    pstR.setString(7, descricao);
                    pstR.setDouble(8, obj.getValor());
                    pstR.setString(9, obj.getNumeroDocumento());
                    pstR.executeUpdate();
                }
                // adiciona ao VO de retorno
                MovContaRateioVO movContaRateioVOAddMovContaVORetornar = new MovContaRateioVO();
                movContaRateioVOAddMovContaVORetornar.setMovConta(new MovContaVO());
                movContaRateioVOAddMovContaVORetornar.getMovConta().setCodigo(codigoMovConta);
                // copiar demais campos de 'obj' se necessário
                movContaRateioVOAddMovContaVORetornar.setPlanoContaVO(obj.getPlanoContaVO());
                movContaRateioVOAddMovContaVORetornar.setCentroCustoVO(obj.getCentroCustoVO());
                movContaRateioVOAddMovContaVORetornar.setTipoDocumentoVO(obj.getTipoDocumentoVO());
                movContaRateioVOAddMovContaVORetornar.setFormaPagamentoVO(obj.getFormaPagamentoVO());
                movContaRateioVOAddMovContaVORetornar.setTipoES(obj.getTipoES());
                movContaRateioVOAddMovContaVORetornar.setDescricao(descricao);
                movContaRateioVOAddMovContaVORetornar.setValor(obj.getValor());
                movContaRateioVOAddMovContaVORetornar.setNumeroDocumento(obj.getNumeroDocumento());
                rateiosInseridos.add(movContaRateioVOAddMovContaVORetornar);
            }

            movContaVORetornar.setMovContaRateios(rateiosInseridos);
            return movContaVORetornar;
        }
    }


    public static Date retornarProximoVencimento(Date dataUltimoVenc, int diaVencimento, FrequenciaAgendamento frequencia){
        Calendar novaData = Calendario.getInstance();
        novaData.setTime(dataUltimoVenc);
        if (frequencia == FrequenciaAgendamento.SEMANAL){
            novaData.add(Calendar.DAY_OF_MONTH, 7);
            return novaData.getTime();

        }else{
            int qtdeMeses = (frequencia.getQtdeDias()/30);
            novaData.set(Calendar.DAY_OF_MONTH, 1);
            novaData.add(Calendar.MONTH, qtdeMeses);
            if ( diaVencimento > (novaData.getActualMaximum(Calendar.DAY_OF_MONTH))  ){
                diaVencimento = novaData.getActualMaximum(Calendar.DAY_OF_MONTH);
            }
            novaData.set(Calendar.DAY_OF_MONTH, diaVencimento);
            return novaData.getTime();

        }

    }

    public static String retornarDescricaoMovConta(AgendamentoFinanceiroVO prAgendamentoFinanceiroVO, Date dataVencimento, int parcela){
        String descricao = "";
        if (prAgendamentoFinanceiroVO.getLayoutDescricao() == LayoutDescricao.MES_REFERENCIA){
           descricao = removePrefixo(prAgendamentoFinanceiroVO.getDescricao()) + retornarNomeMes(dataVencimento);
        }else{
           descricao = prAgendamentoFinanceiroVO.getDescricao() + " PARC. " + parcela + "/" + prAgendamentoFinanceiroVO.getParcelaFinal();
        }
        return descricao;
    }


    private void incluirNovoAgendamento(AgendamentoFinanceiroVO prAgendamentoFinanceiroVO, MovContaVO prMovContaVO)throws Exception{
        StringBuilder sql = new StringBuilder();
        sql.append("insert into agendamentoFinanceiro (descricao, frequencia, layoutdescricao, proximovencimento, ");
        sql.append("vencimentoultimaparcela, parcelaini, parcelafim, qtdeparcelasgerar, qtdediasnovageracao, diavencimento,  ");
        sql.append("datalancamento, usaParcelasFixas, gerarapenasdiasuteis)  ");
        sql.append("values(?,?,?,?,?,?,?,?,?,?,?,?,?)");
        PreparedStatement pstIncluir = this.con.prepareStatement(sql.toString());
        pstIncluir.setString(1, prAgendamentoFinanceiroVO.getDescricao());
        pstIncluir.setInt(2, prAgendamentoFinanceiroVO.getFrequencia().getCodigo());
        pstIncluir.setInt(3, prAgendamentoFinanceiroVO.getLayoutDescricao().getCodigo());

        pstIncluir.setDate(4, Uteis.getDataJDBC(prAgendamentoFinanceiroVO.getProximoVencimento()));
        if (prAgendamentoFinanceiroVO.getVencimentoUltimaParcela() ==  null)
          pstIncluir.setNull(5, java.sql.Types.NULL);
        else
          pstIncluir.setDate(5, Uteis.getDataJDBC(prAgendamentoFinanceiroVO.getVencimentoUltimaParcela()));
        if (prAgendamentoFinanceiroVO.getParcelaInicial() <= 0){
            pstIncluir.setNull(6, java.sql.Types.NULL);
            pstIncluir.setNull(7, java.sql.Types.NULL);
        }
        else{
          pstIncluir.setInt(6, prAgendamentoFinanceiroVO.getParcelaInicial());
          pstIncluir.setInt(7, prAgendamentoFinanceiroVO.getParcelaFinal());
        }
        pstIncluir.setInt(8, prAgendamentoFinanceiroVO.getQtdeParcelasGerar());
        pstIncluir.setInt(9, prAgendamentoFinanceiroVO.getQtdeDiasNovaGeracao());
        pstIncluir.setInt(10, prAgendamentoFinanceiroVO.getDiaVencimento());
        pstIncluir.setDate(11, Uteis.getDataJDBC(prAgendamentoFinanceiroVO.getDataLancamento()));
        pstIncluir.setBoolean(12, prAgendamentoFinanceiroVO.isUsaParcelasFixas());
        pstIncluir.setBoolean(13, prAgendamentoFinanceiroVO.getGerarApenasDiasUteis());

        pstIncluir.execute();
        // Consultar o código do Agendamento gerado.
        prAgendamentoFinanceiroVO.setCodigo(retornarUltimoCodigoTabela("agendamentoFinanceiro"));

       /* O lançamento que originou o agendamento, é a primeira parcela.
        * Desta forma, alterar os campos(descricao, nrParcela e agendamentoFinanceiro) da primeira parcela.
        */
       int nrParcela = 1;
       if (prAgendamentoFinanceiroVO.getParcelaInicial() > 0)
         nrParcela = prAgendamentoFinanceiroVO.getParcelaInicial();
       String descricao = retornarDescricaoMovConta(prAgendamentoFinanceiroVO, prMovContaVO.getDataCompetencia(), nrParcela);
       sql.delete(0, sql.toString().length());
       sql.append("update MovConta set descricao = ?, agendamentoFinanceiro = ?, nrParcela = ? where codigo = ?");
       PreparedStatement pst = this.con.prepareStatement(sql.toString());
       pst.setString(1,descricao);
       pst.setInt(2,prAgendamentoFinanceiroVO.getCodigo());
       pst.setInt(3,nrParcela);
       pst.setInt(4,prMovContaVO.getCodigo());
       pst.execute();
       sql.delete(0, sql.toString().length());
       sql.append("update movcontarateio set descricao = ? where movconta = ?");
       pst = this.con.prepareStatement(sql.toString());
       pst.setString(1,descricao);
       pst.setInt(2,prMovContaVO.getCodigo());
       pst.execute();
       prMovContaVO.setAgendamentoFinanceiro(prAgendamentoFinanceiroVO.getCodigo());
    }

    private void gerarParcelas(AgendamentoFinanceiroVO prAgendamentoFinanceiroVO, MovContaVO prMovContaVO, int nrUltimaParcela) throws Exception{
       this.con.setAutoCommit(false);
       Empresa empresaDAO = new Empresa(this.con);
       prMovContaVO.setEmpresaVO(empresaDAO.consultarPorChavePrimaria(prMovContaVO.getEmpresaVO().getCodigo(), Uteis.NIVELMONTARDADOS_GESTAOREMESSA));
        try{
           boolean novoAgendamento = false;
           if (prAgendamentoFinanceiroVO.getCodigo() == 0){
               // Chamou o processo de agendamento através da tela de "Pagamento" ou "Recebimento"
              incluirNovoAgendamento(prAgendamentoFinanceiroVO, prMovContaVO);
              novoAgendamento = true;
           }

           if ((prAgendamentoFinanceiroVO.getParcelaInicial() >0) && (prAgendamentoFinanceiroVO.getParcelaFinal() >0)){
               // Gerar agendamento para uma quantidade fixa de parcelas.
               gerarParc(prAgendamentoFinanceiroVO,
                         prMovContaVO,
                         prAgendamentoFinanceiroVO.getParcelaInicial() + 1,
                         prAgendamentoFinanceiroVO.getParcelaFinal(), novoAgendamento);
           }
           else{
              // Gerar parcelas para um agendamento recorrente.
              int parcIni = nrUltimaParcela + 1;
              int parcFim = 0;
              if (nrUltimaParcela == 1){
                 /* Neste caso, o processo foi chamado para incluir um novo agendamento pela tela de Lançamento de Pagamento ou Recebimento.
                  * Desta forma, a primeira parcela é o próprio lançamento que originou o agendamento.
                  */
                 parcFim = prAgendamentoFinanceiroVO.getQtdeParcelasGerar();
               }
              else{
                 parcFim = nrUltimaParcela + prAgendamentoFinanceiroVO.getQtdeParcelasGerar();
               }

              gerarParc(prAgendamentoFinanceiroVO, prMovContaVO, parcIni, parcFim, novoAgendamento);
           }
           empresaDAO = null;
           this.con.commit();
       }catch(Exception ex) {
          this.con.rollback();
          throw ex;
       }finally{
          this.con.setAutoCommit(true);
       }

    }
    private void gerarParc(AgendamentoFinanceiroVO prAgendamento, MovContaVO prMovContaVO, int parcelaIni, int parcelaFim, boolean novoAgendamento)throws Exception{
      Feriado feriadoDAO = new Feriado(this.con);  
      Date dataVencimentoUtil = prAgendamento.getProximoVencimento();
      Date dataProximoVencimento = prAgendamento.getProximoVencimento();
      if(prAgendamento.getGerarApenasDiasUteis()){
          dataVencimentoUtil = feriadoDAO.obterProximoDiaUtil(dataProximoVencimento, prMovContaVO.getEmpresaVO() );
      }

      Date dataProximaCompetencia = retornarProximoVencimento(prMovContaVO.getDataCompetencia(), prAgendamento.getDiaVencimento(),  prAgendamento.getFrequencia());
      String descricao = retornarDescricaoMovConta(prAgendamento, dataProximaCompetencia, parcelaIni);
      int parcela = parcelaIni;
      do {
          if ((prAgendamento.getVencimentoUltimaParcela() != null)
                  && (Calendario.maior(dataProximoVencimento ,prAgendamento.getVencimentoUltimaParcela()))){
            // Gerar parcelas somente até a data de vencimento da última parcela.
            break;
          }
          MovContaVO movContaVOIncluido = incluirNovaParcela(prMovContaVO, parcela, dataVencimentoUtil, dataProximaCompetencia, descricao, novoAgendamento);
          if (movContaVOIncluido != null){
              try {
                  movContaVOIncluido.setObjetoVOAntesAlteracao(new MovContaVO());
                  movContaVOIncluido.setNovoObj(true);
                  registrarLogInclusaoMovContaAgendada(movContaVOIncluido, this.con);
              } catch (Exception ex) {
                  ex.printStackTrace();
              }
          }
          parcela++;
          dataProximoVencimento = retornarProximoVencimento(dataProximoVencimento, prAgendamento.getDiaVencimento(),  prAgendamento.getFrequencia());
          dataVencimentoUtil = dataProximoVencimento;
          if(prAgendamento.getGerarApenasDiasUteis()){
                dataVencimentoUtil = feriadoDAO.obterProximoDiaUtil(dataProximoVencimento, prMovContaVO.getEmpresaVO());
          }
          dataProximaCompetencia = retornarProximoVencimento(dataProximaCompetencia, prAgendamento.getDiaVencimento(),  prAgendamento.getFrequencia());
          descricao = retornarDescricaoMovConta(prAgendamento, dataProximaCompetencia, parcela);
      } while (parcela <= parcelaFim);
      // Atualizar a data do próximo vencimento do agendamento.
      atualizarDataProximoVencimento(prAgendamento.getCodigo(), dataProximoVencimento);
      feriadoDAO = null;

    }

    public void registrarLogInclusaoMovContaAgendada(MovContaVO movContaVO, Connection con) throws Exception{
        Log lodDAO;
        try {
            lodDAO = new Log(con);

            //Dados individuais de cada campo são adicionados na geração da lista.
            List<LogVO> listaCamposRegistraLog = gerarListaCamposGravarLog(movContaVO);
            for (Object aLista : listaCamposRegistraLog) {
                //Dados padrões para todos registro no Log são adicionados aqui
                LogVO log = (LogVO) aLista;
                log.setNomeEntidade("MovConta");
                log.setNomeEntidadeDescricao("MovContaVO");
                log.setChavePrimaria(movContaVO.getCodigo().toString());
                log.setDataAlteracao(Calendario.hoje());
                log.setPessoa(movContaVO.getUsuarioVO().getCodigo());
                log.setResponsavelAlteracao(movContaVO.getUsuarioVO().getNome());
                log.setOperacao("INCLUSÃO - AGENDAMENTO");
                log.setNovoObj(true);

                log.setOrigem(OrigemCadEAlteracaoCliente.ZW.getDescricao());

                lodDAO.incluirSemCommit(log);
            }
        } finally {
            lodDAO = null;
        }
    }

    private List<LogVO> gerarListaCamposGravarLog(MovContaVO movContaVO) throws Exception {
        List<LogVO> lista = new ArrayList<>();
        if (movContaVO.getPessoaVO() != null && !UteisValidacao.emptyNumber(movContaVO.getPessoaVO().getCodigo())) {
            LogVO log = new LogVO();
            log.setNomeCampo("pagarParaId");
            log.setValorCampoAnterior("");
            log.setValorCampoAlterado(movContaVO.getPessoaVO().getCodigo().toString());
            lista.add(log);
        }
        if (movContaVO.getPessoaVO() != null && !UteisValidacao.emptyString(movContaVO.getPessoaVO().getNome())) {
            LogVO log = new LogVO();
            log.setNomeCampo("pagarParaNome");
            log.setValorCampoAnterior("");
            log.setValorCampoAlterado(movContaVO.getPessoaVO().getNome());
            lista.add(log);
        }
        if (!UteisValidacao.emptyString(movContaVO.getDescricao())) {
            LogVO log = new LogVO();
            log.setNomeCampo("descricao");
            log.setValorCampoAnterior("");
            log.setValorCampoAlterado(movContaVO.getDescricao());
            lista.add(log);
        }
        if (!UteisValidacao.emptyNumber(movContaVO.getValor())) {
            LogVO log = new LogVO();
            log.setNomeCampo("valor");
            log.setValorCampoAnterior("0");
            log.setValorCampoAlterado(movContaVO.getValor().toString());
            lista.add(log);
        }
        if (movContaVO.getDataLancamento() != null) {
            LogVO log = new LogVO();
            log.setNomeCampo("dataLancamento");
            log.setValorCampoAnterior("");
            log.setValorCampoAlterado(movContaVO.getDataLancamento_Apresentar());
            lista.add(log);
        }
        if (movContaVO.getDataVencimento() != null) {
            LogVO log = new LogVO();
            log.setNomeCampo("dataVencimento");
            log.setValorCampoAnterior("");
            log.setValorCampoAlterado(movContaVO.getDataVencimento_Apresentar());
            lista.add(log);
        }
        if (movContaVO.getDataCompetencia() != null) {
            LogVO log = new LogVO();
            log.setNomeCampo("dataCompetencia");
            log.setValorCampoAnterior("");
            log.setValorCampoAlterado(movContaVO.getDataCompetencia_Apresentar());
            lista.add(log);
        }
        if (!UteisValidacao.emptyString(movContaVO.getObservacoes())) {
            LogVO log = new LogVO();
            log.setNomeCampo("observacao");
            log.setValorCampoAnterior("");
            log.setValorCampoAlterado(movContaVO.getObservacoes());
            lista.add(log);
        }
        if (movContaVO.getContaVO() != null && !UteisValidacao.emptyNumber(movContaVO.getContaVO().getCodigo())) {
            LogVO log = new LogVO();
            log.setNomeCampo("contaId");
            log.setValorCampoAnterior("");
            log.setValorCampoAlterado(movContaVO.getContaVO().getCodigo().toString());
            lista.add(log);
        }
        if (movContaVO.getContaVO() != null && !UteisValidacao.emptyString(movContaVO.getContaVO().getDescricao())) {
            LogVO log = new LogVO();
            log.setNomeCampo("contaNome");
            log.setValorCampoAnterior("");
            log.setValorCampoAlterado(movContaVO.getContaVO().getDescricao());
            lista.add(log);
        }
        return lista;
    }

    private void atualizarDataProximoVencimento(int codigoAgendamento, Date dataProximoVenc) throws Exception{
        String sql = "update agendamentoFinanceiro set proximovencimento = ? where codigo = ?";
        PreparedStatement pst = this.con.prepareStatement(sql);
        pst.setDate(1, Uteis.getDataJDBC(dataProximoVenc));
        pst.setInt(2, codigoAgendamento);
        pst.execute();
    }


    public AgendamentoFinanceiroVO getAgendamentoFinanceiroVO() {
        return agendamentoFinanceiroVO;
    }

    public void setAgendamentoFinanceiroVO(AgendamentoFinanceiroVO agendamentoFinanceiroVO) {
        this.agendamentoFinanceiroVO = agendamentoFinanceiroVO;
    }

    public Connection getCon() {
        return con;
    }

    public void setCon(Connection con) {
        this.con = con;
    }

    public String getMsgErro() {
        return msgErro;
    }

    public void setMsgErro(String msgErro) {
        this.msgErro = msgErro;
    }

    public boolean isTerminouExecucao() {
        return terminouExecucao;
    }

    public void setTerminouExecucao(boolean terminouExecucao) {
        this.terminouExecucao = terminouExecucao;
    }

    public MovContaVO getMovContaVO() {
        return movContaVO;
    }

    public void setMovContaVO(MovContaVO movContaVO) {
        this.movContaVO = movContaVO;
    }

 

}
