package controle.crm;

import br.com.pactosolucoes.comuns.util.JSFUtilities;
import br.com.pactosolucoes.enumeradores.FasesCRMEnum;
import br.com.pactosolucoes.enumeradores.MarcadoresEmailEnum;
import br.com.pactosolucoes.enumeradores.TipoAgendamentoEnum;
import br.com.pactosolucoes.enumeradores.TipoFaseCRM;
import br.com.pactosolucoes.estrutura.paginacao.ConfPaginacao;
import br.com.pactosolucoes.oamd.controle.basico.DAO;
import controle.arquitetura.SuperControle;
import controle.arquitetura.SuperControleCRM;
import controle.arquitetura.security.LoginControle;
import controle.financeiro.MovPagamentoControle;
import negocio.comuns.arquitetura.ResultadoServicosVO;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.basico.ColaboradorIndisponivelCrmVO;
import negocio.comuns.basico.ConfiguracaoEmailFechamentoMetaVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.basico.PessoaVO;
import negocio.comuns.basico.enumerador.ServicoEnum;
import negocio.comuns.crm.*;
import negocio.comuns.plano.MarcadorVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.ControleConsulta;
import negocio.comuns.utilitarias.Ordenacao;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisEmail;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.comuns.utilitarias.queue.MsgTO;
import negocio.facade.jdbc.crm.AberturaMeta;
import negocio.facade.jdbc.crm.MalaDireta;
import negocio.facade.jdbc.utilitarias.Conexao;
import relatorio.arquitetura.GeradorRelatorio;

import javax.faces.event.ActionEvent;
import javax.faces.model.SelectItem;
import java.io.File;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * Classe responsável por implementar a interação entre os componentes JSF das
 * páginas aberturaMetaForm.jsp aberturaMetaCons.jsp) com as funcionalidades da
 * classe
 * <code>AberturaMeta</code>. Implemtação da camada controle (Backing Bean).
 *
 * @see SuperControle
 * @see AberturaMeta
 * @see AberturaMetaVO
 */
public class AberturaMetaControle extends SuperControleCRM {

    private AberturaMetaVO aberturaMetaVO;
    private AberturaMetaVO fecharAbertura;
    private MalaDiretaVO malaDiretaVO;
    private Date dataConsulta;
    private Integer resultadoTotalPassivo;
    private Integer resultadoTotalIndicacao;
    private FecharMetaVO fecharMetaPassivoVO;
    private FecharMetaVO fecharMetaAniversarianteVO;
    private FecharMetaVO fecharMetaGrupoRiscoVO;
    private FecharMetaVO fecharMetaRenovacaoVO;
    private FecharMetaVO fecharMetaPosVendaVO;
    private FecharMetaVO fecharMetaFaturamentoVO;
    private FecharMetaVO fecharMetaIndicadoVO;
    private FecharMetaVO fecharMetaAgendadosVO;
    private FecharMetaVO fecharMetaLigacaoAgendadosVO;
    private FecharMetaVO fecharMetaConversaoAgendadosVO;
    private FecharMetaVO fecharMetaConversaoExAlunosVO;
    private FecharMetaVO fecharMetaVinteQuatroHorasVO;
    private FecharMetaVO fecharMetaVisitantesAntigosVO;
    private FecharMetaVO fecharMetaVisitantesRecorrenteVO;
    private FecharMetaVO fecharMetaConversaoVisitantesAntigos;
    private FecharMetaVO fecharMetaConversaoIndicados;
    private FecharMetaVO fecharMetaConversaoDesistentes;
    private FecharMetaVO fecharMetaVendasVO;
    private FecharMetaVO fecharMetaPerdaVO;
    private FecharMetaVO fecharMetaVencidosVO;
    private FecharMetaVO fecharMetaFaltasVO;
    private FecharMetaVO fecharMetaJustificativa;
    private Boolean manterAbertoRichModal;
    protected Integer empresa = 0;
    private List listaConsultaColaboradorResponsavel;
    private String valorConsultaColaboradorSubstituido;
    private String campoConsultaColaboradorSubstituido;
    private Boolean apresentarBotaoGravarEMensagem;
    private Boolean fecharRichModalAberturaMeta;
    private Date dataInicio;
    private Date dataTermino;
    private Boolean apresentarFiltroPesquisaHistorico;
    private String valorConsultaNomePassivo;
    private String valorConsultaNomeAgendados;
    private String valorConsultaNomeConversaoAgendados;
    private String valorConsultaNomeLigacaoAgendados;
    private String valorConsultaNomeVinteQuatroHoras;
    private String valorConsultaNomeVendas;
    private String valorConsultaNomeFaturamento;
    private String valorConsultaNomeFaltas;
    private String valorConsultaNomePerda;
    private String valorConsultaNomeGrupoRisco;
    private String valorConsultaNomePosVenda;
    private String valorConsultaNomeRenovacao;
    private String valorConsultaNomeAniversariante;
    private String valorConsultaNomeIndicado;
    private String valorConsultaNomeVencidos;
    private String campoConsultarModeloMensagem;
    private String valorConsultarModeloMensagem;
    private List listaConsultarModeloMensagem;
    private Boolean abrirPopUpCliente;
    private Boolean abrirPopUpIndicacao;
    private Boolean abrirPopUpPassivo;
    private List listaEmailPessoa;
    private Boolean abrirPopupDownload;
    private List listaConsultaFechamentoDia;
    private Date dataConsultaFechamentoDia;
    private Boolean apresentarBotoesGravarAdicionarJustificativa;
    private Boolean apresentarBotaoBuscar;
    private Boolean metaEmAberto;
    private Boolean apresentarTelaFormularioFechamentoDia;
    private Date dataFechamentoDia;
    private ClienteVO clienteVO;
    private Boolean abrirMalaDireta;
    private Integer codigoEmpresaConsulta;
    private List<SelectItem> listaSelectItemEmpresa;
    private String labelTela = "";
    private FecharMetaVO fecharMetaGenerico = new FecharMetaVO();
    private String valorConsultaNome;
    private boolean mostrarGruposRetencao = false;
    private boolean mostrarGruposVendas = false;
    private boolean mostrarGruposEstudio = false;
    private Boolean marcarUsuarioVendas;
    private Boolean marcarUsuarioEstudio;
    private Boolean marcarUsuarioRetencao;
    private List<ColaboradorIndisponivelCrmVO> listaColaboradorIndisponivel = new ArrayList<ColaboradorIndisponivelCrmVO>();

    /**
     * Interface
     * <code>AberturaMetaInterfaceFacade</code> responsável pela interconexão da
     * camada de controle com a camada de negócio. Criando uma independência da
     * camada de controle com relação a tenologia de persistência dos dados
     * (DesignPatter: Façade).
     */
    public AberturaMetaControle() throws Exception {
        obterUsuarioLogado();
    }

    /**
     * Rotina responsável por disponibilizar um novo objeto da classe
     * <code>AberturaMeta</code> para edição pelo usuário da aplicação.
     */
    public void inicializarDados() throws Exception {
        setAberturaMetaVO(new AberturaMetaVO());
        setAberturaMeta(new AberturaMetaVO());
        setMalaDiretaVO(new MalaDiretaVO());
        setDataConsulta(Calendario.hoje());
        setResultadoTotalPassivo(0);
        setControleConsulta(new ControleConsulta());
        setManterAbertoRichModal(false);
        setListaConsultaColaboradorResponsavel(new ArrayList());
        setValorConsultaColaboradorSubstituido("");
        setCampoConsultaColaboradorSubstituido("");
        setApresentarBotaoGravarEMensagem(false);
        setMensagemID("");
        validarAberturaMetaCRM();
        setConfiguracaoSistemaCRMVO(getFacade().getConfiguracaoSistemaCRM().consultarPorChavePrimaria(1, Uteis.NIVELMONTARDADOS_DADOSBASICOS));
        if (getAberturaMetaVO().getMetasAgrupadasColaboradorIndisponivel()){
            atualizarMetaDiaPorColaboradorVenda();
        }
    }

    /**
     * Metodo responsavel por validar se o Usuario logado ja fez a Abertura da
     * meta, se tiver feito consulta a abertura e seta dentro de abertura,
     * inicializa a lista de Grupo Colaborador, seta o colaborador responsavel e
     * responsavel por pegar a PermissaoAcessoMenuVO do LoginControle
     */
    public void validarAberturaMetaCRM() throws Exception {
        //Valida se o usuario logado ja fez a abertura da meta e inicializa a lista de grupo colaborador
        setAberturaMetaVO(getFacade().getAberturaMeta().validarUsuarioLogadoParaAberturaMeta(getUsuarioLogado(), getAberturaMetaVO(), getEmpresaLogado().getCodigo(), false));
        //Verificando se existe um colaborador responsavel definido
        if (getAberturaMetaVO().getColaboradorResponsavel().getCodigo() != 0) {
            //Setando o colaborador responsavel na variavel do controlador
            setColaboradorResponsavel(getAberturaMetaVO().getColaboradorResponsavel());
            //Setando a meta obtida da validacao na variavel do controlador
            setAberturaMeta(getAberturaMetaVO());
        }
        //Pega a PermissaoAcessoMenuVO do LoginControle
        executarPermissaoAcessoMenuUsuario(getAberturaMetaVO());
    }

    /**
     * Método responsavel por pegar a PermissaoAcessoMenuVO do LoginControle e
     * setar no Obj Usuario.
     *
     * @param obj
     * @throws Exception
     * <AUTHOR>
     */
    public void executarPermissaoAcessoMenuUsuario(AberturaMetaVO obj) throws Exception {
        LoginControle login = (LoginControle) getControlador("LoginControle");
        obj.getColaboradorResponsavel().setPermissaoAcessoMenuVO(login.getPermissaoAcessoMenuVO());
        getFacade().getAberturaMeta().executarInclusaoAberturaMetaFecharMeta(obj);
    }

    /**
     * Rotina responsável por disponibilizar um novo objeto da classe
     * <code>AberturaMeta</code> para edição pelo usuário da aplicação.
     */
    public String novo() throws Exception {
        try {
            //Inicializando variaveis
            setAberturaMetaVO(new AberturaMetaVO());
            setAberturaMeta(new AberturaMetaVO());
            setDataConsulta(negocio.comuns.utilitarias.Calendario.hoje());
            setResultadoTotalPassivo(0);
            setControleConsulta(new ControleConsulta());
            setManterAbertoRichModal(false);
            setListaConsultaColaboradorResponsavel(new ArrayList());
            setValorConsultaColaboradorSubstituido("");
            setCampoConsultaColaboradorSubstituido("");
            setApresentarBotaoGravarEMensagem(false);

            //Chamada ao método responsavel por setar o usuario logado dentro da AberturaMeta
            //ColaboradorResponsavel, ResponsavelCadastro e ResponsavelLiberacaoTrocaColaboradorResponsavel
            inicializarResponsavelCadastroColaboradorResponsavel();

            //Chamada método responsavel por pegar a PermissaoAcessoMenuVO do LoginControle e setar no Obj Usuario.
            executarPermissaoAcessoMenuUsuario(getAberturaMetaVO());

            setMensagem("");
            setMensagemID("");
            setMensagemDetalhada("", "");
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
        return super.novo();
    }

    /**
     * Rotina responsável por gravar no BD os dados editados de um novo objeto
     * da classe
     * <code>AberturaMeta</code>. Caso o objeto seja novo (ainda não gravado no
     * BD) é acionado a operação
     * <code>incluir()</code>. Caso contrário é acionado o
     * <code>alterar()</code>. Se houver alguma inconsistência o objeto não é
     * gravado, sendo re-apresentado para o usuário juntamente com uma mensagem
     * de erro.
     */
    public void gravar() {
        try {
            temMetaAbertaProDia();
            if (Calendario.maior(aberturaMetaVO.getDia(), Calendario.hoje())) {
                throw new Exception("O dia da Abertura é posterior ao dia atual !");
            }
            Date dataIni = Calendario.hoje();
            aberturaMetaVO.setEmpresaVO(getEmpresaLogado());

            LoginControle loginControle = (LoginControle) JSFUtilities.getFromSession(LoginControle.class.getSimpleName());
            getFacade().getAberturaMeta().gravar(aberturaMetaVO, getEmpresaLogado().getCodigo(), loginControle.getPermissaoAcessoMenuVO(), isUsarEstudio(), true);
            Uteis.calculaTempoExecucaoDaFuncionalidade(dataIni, "GRAVACAO DA META");

            getAberturaMetaVO().setExisteMetaParaParticipante(true);
            setColaboradorResponsavel(getAberturaMetaVO().getColaboradorResponsavel());
            setAberturaMeta(getAberturaMetaVO());
            setAberturaMetaVO(getFacade().getAberturaMeta().validarUsuarioLogadoParaAberturaMeta(getAberturaMetaVO().getColaboradorResponsavel(), getAberturaMetaVO(), getEmpresaLogado().getCodigo(), true));
            setMensagem("");
            setMensagemDetalhada("", "");
            setMensagemID("msg_dados_gravados");
            setSucesso(true);
            setErro(false);
            setFecharRichModalAberturaMeta(true);
            atualizarMetaDiaPorColaboradorRetencao();
            montarMsgAlert(getMensagem());
        } catch (Exception e) {
            getAberturaMetaVO().setExisteMetaParaParticipante(false);
            setSucesso(false);
            setErro(true);
            setFecharRichModalAberturaMeta(false);
            setMensagemDetalhada("msg_erro", e.getMessage());
            montarMsgAlert(getMensagemDetalhada());

        }
    }

    /**
     * Rotina responsável por disponibilizar os dados de um objeto da classe
     * <code>AberturaMeta</code> para alteração. O objeto desta classe é
     * disponibilizado na session da página (request) para que o JSP
     * correspondente possa disponibilizá-lo para edição.
     */
    public String editar() {
        AberturaMetaVO obj = (AberturaMetaVO) context().getExternalContext().getRequestMap().get("aberturaMeta");
        obj.setNovoObj(false);
        setAberturaMetaVO(obj);
        setMensagemID("msg_dados_editar");
        return "editar";
    }

    /**
     * Operação responsável por processar a exclusão um objeto da classe
     * <code>AberturaMetaVO</code> Após a exclusão ela automaticamente aciona a
     * rotina para uma nova inclusão.
     */
    public String excluir() {
        try {
            getFacade().getAberturaMeta().excluir(aberturaMetaVO);
            novo();
            setMensagemID("msg_dados_excluidos");
            setSucesso(true);
            setErro(false);
            return "editar";
        } catch (Exception e) {
            setSucesso(false);
            setErro(true);
            setMensagemDetalhada("msg_erro", e.getMessage());
            return "editar";
        }
    }

    /**
     * Rotina responsavel por executar as consultas disponiveis no JSP
     * AberturaMetaCons.jsp. Define o tipo de consulta a ser executada, por meio
     * de ComboBox denominado campoConsulta, disponivel neste mesmo JSP. Como
     * resultado, disponibiliza um List com os objetos selecionados na sessao da
     * pagina.
     */
    public String consultar() {
        try {
            super.consultar();
            List objs = new ArrayList();
            if (getControleConsulta().getCampoConsulta().equals("codigo")) {
                if (getControleConsulta().getValorConsulta().equals("")) {
                    getControleConsulta().setValorConsulta("0");
                }
                int valorInt = Integer.parseInt(getControleConsulta().getValorConsulta());
                objs = getFacade().getAberturaMeta().consultarPorCodigo(valorInt, true, Uteis.NIVELMONTARDADOS_TODOS);
            }
            setListaConsulta(objs);
            setSucesso(true);
            setErro(false);
            setMensagemID("msg_dados_consultados");
            return "consultar";
        } catch (Exception e) {
            setListaConsulta(new ArrayList());
            setSucesso(false);
            setErro(true);
            setMensagemDetalhada("msg_erro", e.getMessage());
            return "consultar";
        }
    }

    /**
     * Responsável por efetuar a consulta, limpando as configurações de
     * paginação antes
     *
     * <AUTHOR> 31/05/2011
     */
    public void consultarAberturaMetas() {
        this.setConfPaginacao(new ConfPaginacao());
        consultarFechamentoDiaAberturaMeta();
    }

    /**
     * Metodo realiza a consulta das metas de acordo com os parametros definidos
     * no richModal "panelAberturaMetaCons" ou "panelFecharAberturaMetaCons".
     * Este metodo esta sendo utilizado na tela "telaInicialCRM.jsp"
     */
    @SuppressWarnings("unchecked")
    public void consultarFechamentoDiaAberturaMeta() {
        try {
            Integer empresa;
            if (!getUsuarioLogado().getAdministrador()) {
                empresa = getEmpresaLogado().getCodigo();
            } else {
                empresa = getCodigoEmpresaConsulta();
            }
            super.consultar();
            setListaConsultaFechamentoDia(getFacade().getAberturaMeta().consultarFechamentoDiaAberturaMeta(getControleConsulta().getValorConsulta(), getControleConsulta().getCampoConsulta(), getDataConsultaFechamentoDia(), getMetaEmAberto(), empresa, this.getConfPaginacao()));
            setSucesso(true);
            setErro(false);
            setMensagemID("msg_dados_consultados");

        } catch (Exception e) {
            setListaConsulta(new ArrayList());
            setSucesso(false);
            setErro(true);
            setMensagemDetalhada("msg_erro", e.getMessage());

        }
    }

    public void obterMetaAberta() {
        try {
            if (!UteisValidacao.emptyNumber(this.getAberturaMeta().getCodigo())
                    && this.getAberturaMeta().getMetaEmAberto()) {
                this.getListaConsultaFechamentoDia().add(this.getAberturaMeta());
            }
        } catch (Exception e) {
            setMensagem(e.getMessage());
        }

    }

    /* ------------------- INICIO - Métodos PAGINAÇÃO --------------------- */
    public void consultarPaginadoListener(ActionEvent evt) {
        try {
            //VERIFICACAO NECESSARIA POR CAUSA DOS FILTROS
            Object compPaginaInicial = evt.getComponent().getAttributes().get("paginaInicial");
            if (compPaginaInicial != null && !"".equals(compPaginaInicial.toString())) {
                if (compPaginaInicial.toString().equals("paginaInicial")) {
                    setConfPaginacao(new ConfPaginacao());
                }
            }
            //Obtendo qual pagina deverá ser exibida
            Object component = evt.getComponent().getAttributes().get("pagNavegacao");
            if (component != null && !"".equals(component.toString())) {
                getConfPaginacao().setPagNavegacao(component.toString());
            }

            this.consultaPaginada();
        }catch (Exception e){
            montarErro(e);
        }
    }

    protected void consultaPaginada() {
        this.consultarFechamentoDiaAberturaMeta();
    }

    public void inicializarConsultaAberturaDia() {
        setListaConsultaFechamentoDia(new ArrayList());
        this.setConfPaginacao(new ConfPaginacao());

    }

    /**
     * Metodo responsavel por inicializar variaveis para a consulta de
     * fechamento de meta do dia. Este metodo esta sendo utilizado na tela
     * "include_box_MenuLateralCRM.jsp"
     */
    @SuppressWarnings("unchecked")
    public void inicializarConsultaFechamentoDia() {
        setMetaEmAberto(true);
        this.setConfPaginacao(new ConfPaginacao());
        getAberturaMetaVO().setDiaFechamento(negocio.comuns.utilitarias.Calendario.hoje());
        setListaConsultaFechamentoDia(new ArrayList());
        obterMetaAberta();
    }

    /**
     * Metodo responsavel por obter a aberturaMetaVO selecionada, realizar as
     * devidas inicializacoes dos dados, verificando se tratasse de uma meta
     * aberta ou fechada
     */
    public void selecionarAberturaDia() {
        try {
            //Obtendo a AberturaMetaVO selecionada;
            AberturaMetaVO obj = (AberturaMetaVO) context().getExternalContext().getRequestMap().get("fechamentoDia");
            exibirMeta(obj);
        } catch (Exception e) {
            setSucesso(false);
            setErro(true);
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }


    /**
     * <AUTHOR> 19/05/2011
     */
    private void exibirMeta(AberturaMetaVO obj) throws Exception {
        //Verificando se e uma meta aberta

        //Setando a AbeturaMetaVO obtida no controlador;
        setAberturaMetaVO(obj);
        //Obtendo todos os dados da AbeturaMetaVO;
        setAberturaMetaVO(getFacade().getAberturaMeta().inicializarDadosAberturaMetaParaComecarTrabalhar(getUsuarioLogado(), getAberturaMetaVO(), getEmpresaLogado().getCodigo()));
        //Definindo o perfil de acesso do colaborador;
        getFacade().getAberturaMeta().consultarPerfilAcessoColaborador(getAberturaMetaVO(), getEmpresaLogado());
        setApresentarTelaFormularioFechamentoDia(false);
        //Setando o colaborador responsavel;
        setColaboradorResponsavel(getAberturaMetaVO().getColaboradorResponsavel());
        //Setando a AberturaMeta no obj FecharMetaVO
        getFacade().getAberturaMeta().executarInclusaoAberturaMetaFecharMeta(getAberturaMetaVO());
        //Setando AbeturaMetaVO preenchida em outra variavel do controlador
        setAberturaMeta(getAberturaMetaVO());
        //Inicializando a variavel "fecharAbertura"
        setFecharAbertura(new AberturaMetaVO());
        if (!obj.getMetaEmAberto()) {
            //Setando o objeto "AberturaMetaVO" selecionado na variavel "fecharAbertura"
            setFecharAbertura(obj);
            //Obtendo todos os dados da AbeturaMetaVO;
            setFecharAbertura(getFacade().getAberturaMeta().inicializarDadosAberturaMetaParaComecarTrabalhar(getUsuarioLogado(), getFecharAbertura(), getEmpresaLogado().getCodigo()));
            //Definindo o perfil de acesso do colaborador;
            getFacade().getAberturaMeta().consultarPerfilAcessoColaborador(getFecharAbertura(), getEmpresaLogado());
            //Adiciona na lista de fechamento do dia as listas de indicador de venda e retencao
            getFacade().getAberturaMeta().executarPreenchimentoListaFechamentoDia(getFecharAbertura());
            //Inicializando algumas variaveis
            setApresentarBotoesGravarAdicionarJustificativa(false);
            setApresentarBotaoBuscar(true);

        }

        consultarColaboradoresIndisponiveis();
        marcarColaboradorSuplenteVenda();
        marcarColaboradorSuplenteRetencao();
        verificarColaboradoresComoIndisponiveisRentecao();
        verificarColaboradoresComoIndisponiveisVenda();
        atualizarMetaDiaPorColaboradorVenda();
        atualizarMetaDiaPorColaboradorRetencao();

        if(getAberturaMetaVO().getFecharMetaVosVendaApresentar()!=null && !getAberturaMetaVO().getFecharMetaVosVendaApresentar().isEmpty())
        Ordenacao.ordenarLista(getAberturaMetaVO().getFecharMetaVosVendaApresentar(), "ordemTotalizadorMeta");
    }
    public void consultarColaboradoresIndisponiveis() throws Exception {
        setListaColaboradorIndisponivel(getFacade().getColaboradorIndisponivelCrm().consultarPorEmpresa(
                getAberturaMetaVO().getEmpresaVO().getCodigo()));
    }
    public void marcarColaboradorSuplenteVenda() throws  Exception{
        int codigoIndisp = getAberturaMetaVO().getColaboradorResponsavel().getColaboradorVO().getCodigo();

        List<ColaboradorIndisponivelCrmVO> listaColaboradorIndisponivel =
                getFacade().getColaboradorIndisponivelCrm().
                        consultarPorColaborador(codigoIndisp, getAberturaMetaVO().getEmpresaVO().getCodigo());
        for(ColaboradorIndisponivelCrmVO indisponivel :listaColaboradorIndisponivel ) {
            if(indisponivel.getColaboradorIndisponivelVO().getCodigo().equals(codigoIndisp))
            for (GrupoColaboradorVO grupoColaborador : getAberturaMetaVO().getGrupoColaboradorListaVenda()) {
                for (GrupoColaboradorParticipanteVO colaboradorIndisp : grupoColaborador.getGrupoColaboradorParticipanteVOs()) {
                    if(colaboradorIndisp.getColaboradorParticipante().getCodigo().equals(indisponivel.getColaboradorSuplenteVO().getCodigo())){
                        colaboradorIndisp.setGrupoColaboradorParticipanteEscolhido(true);
                    }
                    if(colaboradorIndisp.getColaboradorParticipante().getCodigo().equals(codigoIndisp)){
                        colaboradorIndisp.setGrupoColaboradorParticipanteEscolhido(true);
                    }
                }
            }
        }
    }
    public void marcarColaboradorSuplenteRetencao() throws  Exception{
        int codigoIndisp = getAberturaMetaVO().getColaboradorResponsavel().getColaboradorVO().getCodigo();
        List<ColaboradorIndisponivelCrmVO> listaColaboradorIndisponivel =
                getFacade().getColaboradorIndisponivelCrm().
                        consultarPorColaborador(codigoIndisp, getAberturaMetaVO().getEmpresaVO().getCodigo());
        for(ColaboradorIndisponivelCrmVO indisponivel :listaColaboradorIndisponivel ) {
            if(indisponivel.getColaboradorIndisponivelVO().getCodigo().equals(codigoIndisp))
                for (GrupoColaboradorVO grupoColaborador : getAberturaMetaVO().getGrupoColaboradorListaRetencao()) {
                    for (GrupoColaboradorParticipanteVO colaboradorIndisp : grupoColaborador.getGrupoColaboradorParticipanteVOs()) {
                        if(colaboradorIndisp.getColaboradorParticipante().getCodigo().equals(indisponivel.getColaboradorSuplenteVO().getCodigo())){
                            colaboradorIndisp.setGrupoColaboradorParticipanteEscolhido(true);
                        }
                        if(colaboradorIndisp.getColaboradorParticipante().getCodigo().equals(codigoIndisp)){
                            colaboradorIndisp.setGrupoColaboradorParticipanteEscolhido(true);
                        }
                    }
                }
        }
    }

    /**
     * Metodo responsavel por obter a aberturaMetaVO selecionada e preparar ela
     * para o fechamento. Este metodo esta sendo utilizado na tela
     * "telaInicialCRM.jsp"
     */
    public void selecionarFechamentoAberturaDia() throws Exception {
        selecionarFechamentoAberturaDia(getUsuarioLogado());
    }

    public void selecionarFechamentoAberturaDia(UsuarioVO usuario) {
        try {
            //Obtendo a AberturaMetaVO selecionada;
            AberturaMetaVO obj = (AberturaMetaVO) context().getExternalContext().getRequestMap().get("fechamentoDia");
            //Setando a AbeturaMetaVO obtida no controlador;
            setAberturaMetaVO(obj);
            //Obtendo todos os dados da AbeturaMetaVO;
            setAberturaMetaVO(getFacade().getAberturaMeta().inicializarDadosAberturaMetaParaComecarTrabalhar(usuario, getAberturaMetaVO(), getEmpresaLogado().getCodigo()));
            //Obtendo o colaborador responsavel;
            getAberturaMetaVO().setColaboradorResponsavel(getFacade().getUsuario().consultarPorChavePrimaria(getAberturaMetaVO().getColaboradorResponsavel().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSENTIDADESUBORDINADAS));
            //Definindo o perfil de acesso do colaborador;
            getFacade().getAberturaMeta().consultarPerfilAcessoColaborador(getAberturaMetaVO(), getEmpresaLogado());
            //Setando as listas indicador de venda e retencao nas respectivas listas de fechamento;
            getFacade().getAberturaMeta().executarPreenchimentoListaFechamentoDia(getAberturaMetaVO());
            //Inicializando variaveis
            setFecharMetaJustificativa(new FecharMetaVO());
            getAberturaMetaVO().setDiaFechamento(negocio.comuns.utilitarias.Calendario.hoje());
            setApresentarBotoesGravarAdicionarJustificativa(true);
            setApresentarBotaoBuscar(false);
            //Setando AbeturaMetaVO preenchida em outra variavel do controlador
            setAberturaMeta(getAberturaMetaVO());
            try{
            Ordenacao.ordenarLista(getAberturaMetaVO().getFecharMetaVosVendaApresentar(), "ordemTotalizadorMeta");
            }catch(Exception erro){}
        } catch (Exception e) {
            setSucesso(false);
            setErro(true);
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    /**
     * Metodo que verifica se e possivel fechar a Meta, se for o retorna uma
     * string que fechara o richmodal "panelAberturaMetaCons" e abrira um popup
     * com a tela "fechamentoDiaAberturaMetaCons.jsp", senao for o retorna uma
     * string que fechara o richmodal "panelAberturaMetaCons"; Este metodo esta
     * sendo utilizado na tela "telaInicialCRM.jsp"
     */
    public String getMostrarTelaFormularioFechamentoDia() {
        if (getApresentarTelaFormularioFechamentoDia()) {
            return "Richfaces.hideModalPanel('panelAberturaMetaCons');abrirPopup('faces/fechamentoDiaAberturaMetaCons.jsp', 'FechamentoDiaAberturaMetaCons', 780, 595);";
        } else {
            return "Richfaces.hideModalPanel('panelAberturaMetaCons')";
        }

    }

    /**
     * Metodo que verifica se e possivel fechar a Meta, se for o retorna uma
     * string que fechara o richmodal "panelFecharAberturaMetaCons" e abrira um
     * popup com a tela "fechamentoDiaAberturaMetaForm.jsp". Este metodo esta
     * sendo utilizado na tela "telaInicialCRM.jsp"
     */
    public String getMostrarTelaFormularioFechamentoDiaGrava() {
        if (getApresentarBotoesGravarAdicionarJustificativa()) {
            return "Richfaces.hideModalPanel('panelFecharAberturaMetaCons');abrirPopup('faces/fechamentoDiaAberturaMetaForm.jsp', 'FechamentoDiaAberturaMetaForm', 780, 595);";
        } else {
            return "";
        }

    }

    /**
     * Método responsavel por verificar a validade do usuario e senha.
     *
     * @throws Exception
     */
    public void verificarPermissaoUsuario() throws Exception {
        try {
            getFacade().getAberturaMeta().validarPermissaoResponsavelTrocaColaboradorResponsavel(aberturaMetaVO.getResponsavelLiberacaoTrocaColaboradorResponsavel(), getEmpresaLogado().getCodigo());
            setManterAbertoRichModal(true);
            setListaConsultaColaboradorResponsavel(new ArrayList());
            setMensagem("");
            setMensagemID("");
            setMensagemDetalhada("", "");
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public void aberturaListener(final ActionEvent event) {
        try {
            novo();
            Object obj = event.getComponent().getAttributes().get("dia");
            this.getAberturaMetaVO().setDia((Date) obj);
        } catch (Exception e) {
            this.setMensagem(e.getMessage());
        }

    }
    /*
     * Método responsavel por calcular a meta do dia de cada colaborador logado
     */

    public void calcularMetaDia() {
        try {
            limparMsg();
            this.getAberturaMetaVO().setEmpresaVO(this.getEmpresaLogado());

            temMetaAbertaProDia();

            //Setando a empresa logada na abertura de meta
            this.getAberturaMetaVO().setEmpresaVO(this.getEmpresaLogado());

            this.getAberturaMetaVO().setAberturaRetroativa(Calendario.menor(this.getAberturaMetaVO().getDia(), Calendario.hoje()));
            //Metodo que define qual sera o dia a ser consultado para as metas e calcula as metas auxiliares.
            this.getAberturaMetaVO().setEmpresaVO(getEmpresaLogado());

            //Montando quarentena CRM, onde algumas metas deixam de ser abertas. Ex: Grupo de risco, Faltosos.
            QuarentenaVO quarentenaCRM = null;
            try {
                quarentenaCRM = getFacade().getQuarentena().obterAtiva(this.getAberturaMetaVO().getEmpresaVO().getCodigo());
            }catch (Exception ignore){}

            LoginControle loginControle = (LoginControle) JSFUtilities.getFromSession(LoginControle.class.getSimpleName());
            //PY. aqui se verificava a maior do lentidão do sistema
            getFacade().getAberturaMeta().executarDefinicaoDataParaCalcularAberturaDia(this.getAberturaMetaVO(), loginControle.getPermissaoAcessoMenuVO(), isUsarEstudio(), quarentenaCRM);
            //Uteis.calculaTempoExecucaoDaFuncionalidade(dataIni, "CALCULO DE META");

            //Setando a variavel que permitira visualizar o botao [Gravar]
            setApresentarBotaoGravarEMensagem(true);

        } catch (Exception e) {
            setMensagemID("msg_erro");
            setMensagemDetalhada(e);
        }
    }

    /**
     * Responsável por
     *
     * <AUTHOR> Alcides 06/09/2012
     */
    private void temMetaAbertaProDia() throws Exception {
        //Inicio - Verificacao se ja existe meta aberta
        Long qtdeAberturaMetaDia;
        //Verificando se o calculo é pra um colacorador
        if (this.getAberturaMetaVO().getColaboradorResponsavel().getCodigo() > 0) {
            //Consultando se ja existe meta aberta para este colaborador
            qtdeAberturaMetaDia = getFacade().getAberturaMeta().consultarVerificacaoAberturaMetaDia(this.getAberturaMetaVO().getColaboradorResponsavel().getCodigo(), this.getAberturaMetaVO().getDia(), this.getAberturaMetaVO().getEmpresaVO().getCodigo());
        } else {
            //Consultando se ja existe meta aberta para o usuario logado
            qtdeAberturaMetaDia = getFacade().getAberturaMeta().consultarVerificacaoAberturaMetaDia(getUsuarioLogado().getCodigo(), this.getAberturaMetaVO().getDia(), getEmpresaLogado().getCodigo());
        }

        //Se a quantidade de obtida na consulta for diferente de zero
        //e lancado uma excecao dizendo que ja existe meta cadastrada
        if (qtdeAberturaMetaDia != 0) {
            throw new Exception("A Abertura de Meta já foi cadastrada para esse usúario !");
        }
        //Fim - Verificacao se ja existe meta aberta
    }

    // ----------------------------------------- TELA INDICADOR DE RETENCAO
    // -----------------------------------------
    public void atualizarFecharMetaDetalhadaRetencao(final ActionEvent event) {
        String identificador = event.getComponent().getAttributes().get("identificador").toString();
        FecharMetaVO obj = new FecharMetaVO();
        obj.setIdentificadorMeta(identificador);
        consultarFecharMetaDetalhadaRetencao(obj);
    }

    /**
     * Método responsavel por consultar fecharMetaDetalhadoAgendados no momento
     * que clico no indicador de Vendas opção Agendamento, preenchendo a lista
     * que aparecerá na tela metaAgendadosDetalhadaForm.
     */
    public void consultarFecharMetaDetalhadoRetencao() {
        FecharMetaVO obj = (FecharMetaVO) context().getExternalContext().getRequestMap().get("fecharMeta");
        consultarFecharMetaDetalhadaRetencao(obj);
    }

    private void limparTotais(FecharMetaVO meta) {
        meta.setTotalizadorSelecionadoHistorico(0L);
        meta.setTotalizadorSelecionadoHoje(0L);
    }

    private void consultarFecharMetaDetalhadaRetencao(FecharMetaVO obj) {
        try {
            if (obj.getIdentificadorMeta().equals(FasesCRMEnum.ANIVERSARIANTES.getSigla())) {
                setFecharMetaAniversarianteVO(obj);
                getFacade().getFecharMeta().inicializarDadosParaTelaMetaDetalhando(getFecharMetaAniversarianteVO());
                inicializarDadosAbaHojeAniversariante();
                limparTotais(getFecharMetaAniversarianteVO());

            }
            if (obj.getIdentificadorMeta().equals(FasesCRMEnum.GRUPO_RISCO.getSigla())) {
                setFecharMetaGrupoRiscoVO(obj);
                getFacade().getFecharMeta().inicializarDadosParaTelaMetaDetalhando(getFecharMetaGrupoRiscoVO());
                inicializarDadosAbaHojeGrupoRisco();
                limparTotais(getFecharMetaGrupoRiscoVO());
            }
            if (obj.getIdentificadorMeta().equals(FasesCRMEnum.FALTOSOS.getSigla())) {
                setFecharMetaFaltasVO(obj);
                getFacade().getFecharMeta().inicializarDadosParaTelaMetaDetalhando(getFecharMetaFaltasVO());
                inicializarDadosAbaHojeFalta();
                limparTotais(getFecharMetaFaltasVO());
            }

            if (obj.getIdentificadorMeta().equals(FasesCRMEnum.VENCIDOS.getSigla())) {
                setFecharMetaVencidosVO(obj);
                getFacade().getFecharMeta().inicializarDadosParaTelaMetaDetalhando(getFecharMetaVencidosVO());
                inicializarDadosAbaHojeVencidos();
                limparTotais(getFecharMetaVencidosVO());
            }

            if (obj.getIdentificadorMeta().equals(FasesCRMEnum.POS_VENDA.getSigla())) {
                setFecharMetaPosVendaVO(obj);
                getFacade().getFecharMeta().inicializarDadosParaTelaMetaDetalhando(getFecharMetaPosVendaVO());
                inicializarDadosAbaHojePosVenda();
                limparTotais(getFecharMetaPosVendaVO());
            }
            setMensagem("");
            setMensagemID("");
            setMensagemDetalhada("", "");
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    /**
     * Método responsavel por atualizar a meta no momento em que clicar no botão
     * atualizar.
     */
    public void atualizarMetaDiaRetencao() {
        try {
            getFacade().getAberturaMeta().atualizarMetaRetencaoDia(aberturaMetaVO);
            for (GrupoColaboradorVO grupo : aberturaMetaVO.getGrupoColaboradorListaRetencao()) {
                for (GrupoColaboradorParticipanteVO parti : grupo.getGrupoColaboradorParticipanteVOs()) {
                    if (parti.getUsuarioParticipante().getCodigo().equals(aberturaMetaVO.getColaboradorResponsavel().getCodigo())) {
                        parti.setGrupoColaboradorParticipanteEscolhido(true);
                    } else {
                        parti.setGrupoColaboradorParticipanteEscolhido(false);
                    }
                }
            }
            getAberturaMetaVO().setExisteMetaParaParticipante(true);
            setMensagem("");
            setMensagemID("");
            setMensagemDetalhada("", "");
        } catch (Exception e) {
            getAberturaMetaVO().setExisteMetaParaParticipante(false);
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public void selecionarGrupoColaboradorParticipanteRetencao() throws Exception {
        try {
            GrupoColaboradorVO obj = (GrupoColaboradorVO) context().getExternalContext().getRequestMap().get("grupoColaborador");
            for (GrupoColaboradorVO grupo : aberturaMetaVO.getGrupoColaboradorListaRetencao()) {
                if (grupo.getCodigo().equals(obj.getCodigo().intValue()) && obj.getGrupoColaboradorParticipanteVOs().isEmpty()) {
                    consultarGrupoColaboradorParticipante(obj);
                    verificarColaboradoresComoIndisponiveisRentecao();
                    getAberturaMetaVO().setNomeGrupoColaboradorRetencao(grupo.getDescricao());
                    for (GrupoColaboradorParticipanteVO parti : obj.getGrupoColaboradorParticipanteVOs()) {
                        if (parti.getUsuarioParticipante().getCodigo().equals(getAberturaMetaVO().getColaboradorResponsavel().getCodigo())) {
                            parti.getUsuarioParticipante().getColaboradorVO().setColaboradorEscolhidoIndiceConversao(true);
                        }
                    }
                } else {
                    grupo.setGrupoColaboradorParticipanteVOs(new ArrayList());
                }
            }
        } catch (Exception e) {
            setMensagemDetalhada("", e.getMessage());
        }

    }
    
    public void selecionarGrupoColaboradorParticipanteEstudio() throws Exception {
        try {
            GrupoColaboradorVO obj = (GrupoColaboradorVO) context().getExternalContext().getRequestMap().get("grupoColaborador");
            for (GrupoColaboradorVO grupo : aberturaMetaVO.getGrupoColaboradorListaEstudio()) {
                if (grupo.getCodigo().equals(obj.getCodigo().intValue()) && obj.getGrupoColaboradorParticipanteVOs().isEmpty()) {
                    consultarGrupoColaboradorParticipante(obj);
                    verificarColaboradoresComoIndisponiveisEstudio();
                    getAberturaMetaVO().setNomeGrupoColaboradorEstudio(grupo.getDescricao());
                    for (GrupoColaboradorParticipanteVO parti : obj.getGrupoColaboradorParticipanteVOs()) {
                        if (parti.getUsuarioParticipante().getCodigo().equals(getAberturaMetaVO().getColaboradorResponsavel().getCodigo())) {
                            parti.getUsuarioParticipante().getColaboradorVO().setColaboradorEscolhidoIndiceConversao(true);
                        }
                    }
                } else {
                    grupo.setGrupoColaboradorParticipanteVOs(new ArrayList());
                }
            }
        } catch (Exception e) {
            setMensagemDetalhada("", e.getMessage());
        }

    }

    private void consultarGrupoColaboradorParticipante(GrupoColaboradorVO obj) throws Exception {
        if (getFacade().getAberturaMeta().verificarPermitirVisualizarTodasCarteiras()) {
            obj.setGrupoColaboradorParticipanteVOs(getFacade().getGrupoColaboradorParticipante().consultarGrupoColaboradorParticipantesPorCodigoETipoVisaoDiferenteMontandoUsuario(obj.getCodigo(), "", Uteis.NIVELMONTARDADOS_ABERTURAMETA));
        } else {
            obj.setGrupoColaboradorParticipanteVOs(getFacade().getGrupoColaboradorParticipante().consultarGrupoColaboradorParticipantesPorCodigoETipoVisaoDiferenteMontandoUsuario(obj.getCodigo(), "VI", Uteis.NIVELMONTARDADOS_ABERTURAMETA));
        }
    }

    // ----------------------------------------- RELATORIO PDF
    // -----------------------------------------
    /**
     * Método responsavel por gerar Relatorio da Meta Passivo
     *
     * @throws Exception
     */
    public void executarImpressaoPdfPassivo() throws Exception {
        if (getFecharMetaPassivoVO().getAbaSelecionada().equals("HJ")) {
            getFecharMetaPassivoVO().setFecharMetaDetalhadoVOs(getFacade().getFecharMetaDetalhado().consultarFecharMetaDetalhado(aberturaMetaVO, getFecharMetaPassivoVO().getIdentificadorMeta(), true, getEmpresaLogado().getCodigo()));
            getFecharMetaPassivoVO().setTotalApresentarRelatorio(new Double(getFecharMetaPassivoVO().getTotalizadorHoje()));
            imprimirRelatorio(getEmpresaLogado(), getFecharMetaPassivoVO().getFecharMetaDetalhadoVOs(), "Relatório Meta Detalhado Passivo", "Período: " + Uteis.getData(getAberturaMetaVO().getDia()), "MetaDetalhadoRel", getFecharMetaPassivoVO().getTotalApresentarRelatorio());
        } else if (getFecharMetaPassivoVO().getAbaSelecionada().equals("HI")) {
            getFecharMetaPassivoVO().setTotalApresentarRelatorio(new Double(getFecharMetaPassivoVO().getTotalizadorHistorico()));
            imprimirRelatorio(getEmpresaLogado(), getFecharMetaPassivoVO().getListaHistoricoContatoHistorico(), "Relatório Meta Detalhado Passivo", "Período: " + Uteis.getData(getDataInicio()) + " até " + Uteis.getData(getDataTermino()), "MetaDetalhadoRel", getFecharMetaPassivoVO().getTotalApresentarRelatorio());

        }
    }

    /**
     * Método responsavel por gerar Relatorio da Meta Indicado
     *
     * @throws Exception
     */
    public void executarImpressaoPdfIndicado() throws Exception {
        if (getFecharMetaIndicadoVO().getAbaSelecionada().equals("HJ")) {
            getFecharMetaIndicadoVO().setFecharMetaDetalhadoVOs(getFacade().getFecharMetaDetalhado().consultarFecharMetaDetalhado(aberturaMetaVO, getFecharMetaIndicadoVO().getIdentificadorMeta(), true, getEmpresaLogado().getCodigo()));
            getFecharMetaIndicadoVO().setTotalApresentarRelatorio(new Double(getFecharMetaIndicadoVO().getTotalizadorHoje()));
            imprimirRelatorio(getEmpresaLogado(), getFecharMetaIndicadoVO().getFecharMetaDetalhadoVOs(), "Relatório Meta Detalhado Indicado", "Período: " + Uteis.getData(getAberturaMetaVO().getDia()), "MetaDetalhadoIndicadoRel", getFecharMetaIndicadoVO().getTotalApresentarRelatorio());
        } else if (getFecharMetaIndicadoVO().getAbaSelecionada().equals("HI")) {
            getFecharMetaIndicadoVO().setTotalApresentarRelatorio(new Double(getFecharMetaIndicadoVO().getTotalizadorHistorico()));
            imprimirRelatorio(getEmpresaLogado(), getFecharMetaIndicadoVO().getListaHistoricoContatoHistorico(), "Relatório Meta Detalhado Indicado", "Período: " + Uteis.getData(getDataInicio()) + " até " + Uteis.getData(getDataTermino()), "MetaDetalhadoIndicadoRel", getFecharMetaIndicadoVO().getTotalApresentarRelatorio());
        }
    }

    /**
     * Método responsavel por gerar Relatorio da Meta Agendados
     *
     * @throws Exception
     */
    public void executarImpressaoAgendados() throws Exception {
        if (getFecharMetaAgendadosVO().getAbaSelecionada().equals("HJ")) {
            getFecharMetaAgendadosVO().setFecharMetaDetalhadoVOs(getFacade().getFecharMetaDetalhado().consultarFecharMetaDetalhado(aberturaMetaVO, getFecharMetaAgendadosVO().getIdentificadorMeta(), true, getEmpresaLogado().getCodigo()));
            getFecharMetaAgendadosVO().setTotalApresentarRelatorio(new Double(getFecharMetaAgendadosVO().getTotalizadorHoje()));
            imprimirRelatorio(getEmpresaLogado(), getFecharMetaAgendadosVO().getFecharMetaDetalhadoVOs(), "Relatório Meta Detalhado Agendados", "Período: " + Uteis.getData(getAberturaMetaVO().getDia()), "MetaDetalhadoAgendadosRel", getFecharMetaAgendadosVO().getTotalApresentarRelatorio());
        } else if (getFecharMetaAgendadosVO().getAbaSelecionada().equals("HI")) {
            getFecharMetaAgendadosVO().setTotalApresentarRelatorio(new Double(getFecharMetaAgendadosVO().getTotalizadorHistorico()));
            imprimirRelatorio(getEmpresaLogado(), getFecharMetaAgendadosVO().getListaHistoricoContatoHistorico(), "Relatório Meta Detalhado Agendados", "Período: " + Uteis.getData(getDataInicio()) + " até " + Uteis.getData(getDataTermino()), "MetaDetalhadoAgendadosRel", getFecharMetaAgendadosVO().getTotalApresentarRelatorio());
        }
    }

    public void executarImpressaoLigacaoAgendados() throws Exception {
        if (getFecharMetaLigacaoAgendadosVO().getAbaSelecionada().equals("HJ")) {

            getFecharMetaLigacaoAgendadosVO().setFecharMetaDetalhadoVOs(
                    getFacade().getFecharMetaDetalhado().consultarFecharMetaDetalhado(
                    aberturaMetaVO, getFecharMetaLigacaoAgendadosVO().getIdentificadorMeta(), true, getEmpresaLogado().getCodigo()));
            getFecharMetaLigacaoAgendadosVO().setTotalApresentarRelatorio(new Double(getFecharMetaLigacaoAgendadosVO().getTotalizadorHoje()));
            imprimirRelatorio(getEmpresaLogado(), getFecharMetaLigacaoAgendadosVO().getFecharMetaDetalhadoVOs(),
                    "Relatório Meta Detalhado Ligação Agendados", "Período: " + Uteis.getData(getAberturaMetaVO().getDia()),
                    "MetaDetalhadoAgendadosRel", getFecharMetaLigacaoAgendadosVO().getTotalApresentarRelatorio());
        } else if (getFecharMetaLigacaoAgendadosVO().getAbaSelecionada().equals("HI")) {
            getFecharMetaLigacaoAgendadosVO().setTotalApresentarRelatorio(new Double(getFecharMetaLigacaoAgendadosVO().getTotalizadorHistorico()));
            imprimirRelatorio(getEmpresaLogado(), getFecharMetaLigacaoAgendadosVO().getListaHistoricoContatoHistorico(), "Relatório Meta Detalhado Ligação para Agendados de Amanhã", "Período: " + Uteis.getData(getDataInicio()) + " até " + Uteis.getData(getDataTermino()), "MetaDetalhadoAgendadosRel", getFecharMetaLigacaoAgendadosVO().getTotalApresentarRelatorio());

        }
    }

    public void executarImpressaoConversaoAgendados() throws Exception {
        if (getFecharMetaConversaoAgendadosVO().getAbaSelecionada().equals("HJ")) {
            getFecharMetaConversaoAgendadosVO().setFecharMetaDetalhadoVOs(getFacade().getFecharMetaDetalhado().consultarFecharMetaDetalhado(aberturaMetaVO, getFecharMetaConversaoAgendadosVO().getIdentificadorMeta(), true, getEmpresaLogado().getCodigo()));
            getFecharMetaConversaoAgendadosVO().setTotalApresentarRelatorio(new Double(getFecharMetaConversaoAgendadosVO().getTotalizadorHoje()));
            imprimirRelatorio(getEmpresaLogado(), getFecharMetaConversaoAgendadosVO().getFecharMetaDetalhadoVOs(), "Relatório Meta Detalhado Agendados", "Período: " + Uteis.getData(getAberturaMetaVO().getDia()), "MetaDetalhadoAgendadosRel", getFecharMetaConversaoAgendadosVO().getTotalApresentarRelatorio());
        } else if (getFecharMetaConversaoAgendadosVO().getAbaSelecionada().equals("HI")) {
            getFecharMetaConversaoAgendadosVO().setTotalApresentarRelatorio(new Double(getFecharMetaConversaoAgendadosVO().getTotalizadorHistorico()));
            imprimirRelatorio(getEmpresaLogado(), getFecharMetaConversaoAgendadosVO().getListaHistoricoContatoHistorico(), "Relatório Meta Detalhado Agendados", "Período: " + Uteis.getData(getDataInicio()) + " até " + Uteis.getData(getDataTermino()), "MetaDetalhadoAgendadosRel", getFecharMetaConversaoAgendadosVO().getTotalApresentarRelatorio());
        }
    }

    /**
     * Método responsavel por gerar Relatorio da Meta 24h
     *
     * @throws Exception
     */
    public void executarImpressaoVinteQuatroHoras() throws Exception {
        if (getFecharMetaVinteQuatroHorasVO().getAbaSelecionada().equals("HJ")) {
            getFecharMetaVinteQuatroHorasVO().setFecharMetaDetalhadoVOs(getFacade().getFecharMetaDetalhado().consultarFecharMetaDetalhado(aberturaMetaVO, getFecharMetaVinteQuatroHorasVO().getIdentificadorMeta(), true, getEmpresaLogado().getCodigo()));
            getFecharMetaVinteQuatroHorasVO().setTotalApresentarRelatorio(new Double(getFecharMetaVinteQuatroHorasVO().getTotalizadorHoje()));
            imprimirRelatorio(getEmpresaLogado(), getFecharMetaVinteQuatroHorasVO().getFecharMetaDetalhadoVOs(), "Relatório Meta Detalhado 24h", "Período: " + Uteis.getData(getAberturaMetaVO().getDia()), "MetaDetalhadoClienteRel", getFecharMetaVinteQuatroHorasVO().getTotalApresentarRelatorio());
        } else if (getFecharMetaVinteQuatroHorasVO().getAbaSelecionada().equals("HI")) {
            getFecharMetaVinteQuatroHorasVO().setTotalApresentarRelatorio(new Double(getFecharMetaVinteQuatroHorasVO().getTotalizadorHistorico()));
            imprimirRelatorio(getEmpresaLogado(), getFecharMetaVinteQuatroHorasVO().getListaHistoricoContatoHistorico(), "Relatório Meta 24h", "Período: " + Uteis.getData(getDataInicio()) + " até " + Uteis.getData(getDataTermino()), "MetaDetalhadoClienteRel", getFecharMetaVinteQuatroHorasVO().getTotalApresentarRelatorio());
        }
    }

    /**
     * Método responsavel por gerar Relatorio da Meta Vendas
     *
     * @throws Exception
     */
    public void executarImpressaoVendas() throws Exception {
        if (getFecharMetaVendasVO().getAbaSelecionada().equals("HJ")) {
            getFecharMetaVendasVO().setFecharMetaDetalhadoVOs(getFacade().getFecharMetaDetalhado().consultarFecharMetaDetalhado(aberturaMetaVO, getFecharMetaVendasVO().getIdentificadorMeta(), true, getEmpresaLogado().getCodigo()));
            getFecharMetaVendasVO().setTotalApresentarRelatorio((double) getFecharMetaVendasVO().getTotalizadorHoje());
            imprimirRelatorio(getEmpresaLogado(), getFecharMetaVendasVO().getFecharMetaDetalhadoVOs(), "Relatório Meta Detalhado Vendas", "Período: " + Uteis.getData(getAberturaMetaVO().getDia()), "MetaDetalhadoClienteRel", getFecharMetaVendasVO().getTotalApresentarRelatorio());
        } else if (getFecharMetaVendasVO().getAbaSelecionada().equals("HI")) {
            getFecharMetaVendasVO().setTotalApresentarRelatorio((double) getFecharMetaVendasVO().getTotalizadorHistorico());
            imprimirRelatorio(getEmpresaLogado(), getFecharMetaVendasVO().getListaHistoricoContatoHistorico(), "Relatório Meta Vendas", "Período: " + Uteis.getData(getDataInicio()) + " até " + Uteis.getData(getDataTermino()), "MetaDetalhadoClienteRel", getFecharMetaVendasVO().getTotalApresentarRelatorio());
        }
    }

    /**
     * Método responsavel por gerar Relatorio da Meta 24h
     *
     * @throws Exception
     */
    public void executarImpressaoFaturamento() throws Exception {
        if (getFecharMetaFaturamentoVO().getAbaSelecionada().equals("HJ")) {
            getFecharMetaFaturamentoVO().setFecharMetaDetalhadoVOs(getFacade().getFecharMetaDetalhado().consultarFecharMetaDetalhado(aberturaMetaVO, getFecharMetaFaturamentoVO().getIdentificadorMeta(), true, getEmpresaLogado().getCodigo()));
            getFecharMetaFaturamentoVO().setTotalApresentarRelatorio(new Double(getFecharMetaFaturamentoVO().getTotalizadorHoje()));
            imprimirRelatorio(getEmpresaLogado(), getFecharMetaFaturamentoVO().getFecharMetaDetalhadoVOs(), "Relatório Meta Detalhado Faturamento", "Período: " + Uteis.getData(getAberturaMetaVO().getDia()), "MetaDetalhadoClienteRel", getFecharMetaFaturamentoVO().getTotalApresentarRelatorio());
        } else if (getFecharMetaFaturamentoVO().getAbaSelecionada().equals("HI")) {
            getFecharMetaFaturamentoVO().setTotalApresentarRelatorio(new Double(getFecharMetaFaturamentoVO().getTotalizadorHistorico()));
            imprimirRelatorio(getEmpresaLogado(), getFecharMetaFaturamentoVO().getListaHistoricoContatoHistorico(), "Relatório Meta Faturamento", "Período: " + Uteis.getData(getDataInicio()) + " até " + Uteis.getData(getDataTermino()), "MetaDetalhadoClienteRel", getFecharMetaFaturamentoVO().getTotalApresentarRelatorio());
        }
    }

    /**
     * Método responsavel por gerar Relatorio da Meta Renovação
     *
     * @throws Exception
     */
    public void executarImpressaoRenovacao() throws Exception {
        if (getFecharMetaRenovacaoVO().getAbaSelecionada().equals("HJ")) {
            getFecharMetaRenovacaoVO().setFecharMetaDetalhadoVOs(getFacade().getFecharMetaDetalhado().consultarFecharMetaDetalhado(aberturaMetaVO, getFecharMetaRenovacaoVO().getIdentificadorMeta(), true, getEmpresaLogado().getCodigo()));
            getFecharMetaRenovacaoVO().setTotalApresentarRelatorio(new Double(getFecharMetaRenovacaoVO().getTotalizadorHoje()));
            imprimirRelatorio(getEmpresaLogado(), getFecharMetaRenovacaoVO().getFecharMetaDetalhadoVOs(), "Relatório Meta Detalhado Renovação", "Período: " + Uteis.getData(getAberturaMetaVO().getDia()), "MetaDetalhadoClienteRel", getFecharMetaRenovacaoVO().getTotalApresentarRelatorio());
        } else if (getFecharMetaRenovacaoVO().getAbaSelecionada().equals("HI")) {
            getFecharMetaRenovacaoVO().setTotalApresentarRelatorio(new Double(getFecharMetaRenovacaoVO().getTotalizadorHistorico()));
            imprimirRelatorio(getEmpresaLogado(), getFecharMetaRenovacaoVO().getListaHistoricoContatoHistorico(), "Relatório Meta Renovação", "Período: " + Uteis.getData(getDataInicio()) + " até " + Uteis.getData(getDataTermino()), "MetaDetalhadoClienteRel", getFecharMetaRenovacaoVO().getTotalApresentarRelatorio());
        }
    }

    /**
     * Método responsavel por gerar Relatorio da Meta Pos Venda
     *
     * @throws Exception
     */
    public void executarImpressaoPosVenda() throws Exception {
        if (getFecharMetaPosVendaVO().getAbaSelecionada().equals("HJ")) {
            getFecharMetaPosVendaVO().setFecharMetaDetalhadoVOs(getFacade().getFecharMetaDetalhado().consultarFecharMetaDetalhado(aberturaMetaVO, getFecharMetaPosVendaVO().getIdentificadorMeta(), false, getEmpresaLogado().getCodigo()));
            getFecharMetaPosVendaVO().setTotalApresentarRelatorio(new Double(getFecharMetaPosVendaVO().getTotalizadorHoje()));
            imprimirRelatorio(getEmpresaLogado(), getFecharMetaPosVendaVO().getFecharMetaDetalhadoVOs(), "Relatório Meta Detalhado Pós Venda", "Período: " + Uteis.getData(getAberturaMetaVO().getDia()), "MetaDetalhadoClienteRel", getFecharMetaPosVendaVO().getTotalApresentarRelatorio());
        } else if (getFecharMetaPosVendaVO().getAbaSelecionada().equals("HI")) {
            getFecharMetaPosVendaVO().setTotalApresentarRelatorio(new Double(getFecharMetaPosVendaVO().getTotalizadorHistorico()));
            imprimirRelatorio(getEmpresaLogado(), getFecharMetaPosVendaVO().getListaHistoricoContatoHistorico(), "Relatório Meta Pós Venda", "Período: " + Uteis.getData(getDataInicio()) + " até " + Uteis.getData(getDataTermino()), "MetaDetalhadoClienteRel", getFecharMetaPosVendaVO().getTotalApresentarRelatorio());
        }
    }

    /**
     * Método responsavel por gerar Relatorio da Meta Grupo Risco
     *
     * @throws Exception
     * <AUTHOR>
     */
    public void executarImpressaoGrupoRisco() throws Exception {
        if (getFecharMetaGrupoRiscoVO().getAbaSelecionada().equals("HJ")) {
            getFecharMetaGrupoRiscoVO().setFecharMetaDetalhadoVOs(getFacade().getFecharMetaDetalhado().consultarFecharMetaDetalhado(aberturaMetaVO, getFecharMetaGrupoRiscoVO().getIdentificadorMeta(), false, getEmpresaLogado().getCodigo()));
            getFecharMetaGrupoRiscoVO().setTotalApresentarRelatorio(new Double(getFecharMetaGrupoRiscoVO().getTotalizadorHoje()));
            imprimirRelatorio(getEmpresaLogado(), getFecharMetaGrupoRiscoVO().getFecharMetaDetalhadoVOs(), "Relatório Meta Detalhado Grupo Risco", "Período: " + Uteis.getData(getAberturaMetaVO().getDia()), "MetaDetalhadoClienteRel", getFecharMetaGrupoRiscoVO().getTotalApresentarRelatorio());
        } else if (getFecharMetaGrupoRiscoVO().getAbaSelecionada().equals("HI")) {
            getFecharMetaGrupoRiscoVO().setTotalApresentarRelatorio(new Double(getFecharMetaGrupoRiscoVO().getTotalizadorHistorico()));
            imprimirRelatorio(getEmpresaLogado(), getFecharMetaGrupoRiscoVO().getListaHistoricoContatoHistorico(), "Relatório Meta Grupo Risco", "Período: " + Uteis.getData(getDataInicio()) + " até " + Uteis.getData(getDataTermino()), "MetaDetalhadoClienteRel", getFecharMetaGrupoRiscoVO().getTotalApresentarRelatorio());

        }
    }

    /**
     * Método responsavel por gerar Relatorio da Meta Aniversariante
     *
     * @throws Exception
     * <AUTHOR>
     */
    public void executarImpressaoAniversariante() throws Exception {
        if (getFecharMetaAniversarianteVO().getAbaSelecionada().equals("HJ")) {
            getFecharMetaAniversarianteVO().setFecharMetaDetalhadoVOs(getFacade().getFecharMetaDetalhado().consultarFecharMetaDetalhado(aberturaMetaVO, getFecharMetaAniversarianteVO().getIdentificadorMeta(), false, getEmpresaLogado().getCodigo()));
            getFecharMetaAniversarianteVO().setTotalApresentarRelatorio(new Double(getFecharMetaAniversarianteVO().getTotalizadorHoje()));
            imprimirRelatorio(getEmpresaLogado(), getFecharMetaAniversarianteVO().getFecharMetaDetalhadoVOs(), "Relatório Meta Detalhado Aniversariante", "Período: " + Uteis.getData(getAberturaMetaVO().getDia()), "MetaDetalhadoClienteRel", getFecharMetaAniversarianteVO().getTotalApresentarRelatorio());
        } else if (getFecharMetaAniversarianteVO().getAbaSelecionada().equals("HI")) {
            getFecharMetaAniversarianteVO().setTotalApresentarRelatorio(new Double(getFecharMetaAniversarianteVO().getTotalizadorHistorico()));
            imprimirRelatorio(getEmpresaLogado(), getFecharMetaAniversarianteVO().getListaHistoricoContatoHistorico(), "Relatório Meta Aniversariante", "Período: " + Uteis.getData(getDataInicio()) + " até " + Uteis.getData(getDataTermino()), "MetaDetalhadoClienteRel", getFecharMetaAniversarianteVO().getTotalApresentarRelatorio());
        }
    }

    /**
     * Método responsavel por gerar Relatorio da Meta Faltosos
     *
     * @throws Exception
     * <AUTHOR>
     */
    public void executarImpressaoFaltosos() throws Exception {
        if (getFecharMetaFaltasVO().getAbaSelecionada().equals("HJ")) {
            getFecharMetaFaltasVO().setFecharMetaDetalhadoVOs(getFacade().getFecharMetaDetalhado().consultarFecharMetaDetalhado(aberturaMetaVO, getFecharMetaFaltasVO().getIdentificadorMeta(), false, getEmpresaLogado().getCodigo()));
            getFecharMetaFaltasVO().setTotalApresentarRelatorio(new Double(getFecharMetaFaltasVO().getTotalizadorHoje()));
            imprimirRelatorio(getEmpresaLogado(), getFecharMetaFaltasVO().getFecharMetaDetalhadoVOs(), "Relatório Meta Detalhado Faltosos", "Período: " + Uteis.getData(getAberturaMetaVO().getDia()), "MetaDetalhadoClienteRel", getFecharMetaFaltasVO().getTotalApresentarRelatorio());
        } else if (getFecharMetaFaltasVO().getAbaSelecionada().equals("HI")) {
            getFecharMetaFaltasVO().setTotalApresentarRelatorio(new Double(getFecharMetaFaltasVO().getTotalizadorHistorico()));
            imprimirRelatorio(getEmpresaLogado(), getFecharMetaFaltasVO().getListaHistoricoContatoHistorico(), "Relatório Meta Faltosos", "Período: " + Uteis.getData(getDataInicio()) + " até " + Uteis.getData(getDataTermino()), "MetaDetalhadoClienteRel", getFecharMetaFaltasVO().getTotalApresentarRelatorio());
        }
    }

    /**
     * Método responsavel por gerar Relatorio da Meta Perdas
     *
     * @throws Exception
     * <AUTHOR>
     */
    public void executarImpressaoPerda() throws Exception {
        if (getFecharMetaPerdaVO().getAbaSelecionada().equals("HJ")) {
            getFecharMetaPerdaVO().setTotalApresentarRelatorio(new Double(getFecharMetaPerdaVO().getTotalizadorHoje()));
            imprimirRelatorio(getEmpresaLogado(), getFecharMetaPerdaVO().getFecharMetaDetalhadoVOs(), "Relatório Meta Detalhado Desistentes", "Período: " + Uteis.getData(getAberturaMetaVO().getDia()), "MetaDetalhadoClienteRel", getFecharMetaPerdaVO().getTotalApresentarRelatorio());
        } else if (getFecharMetaPerdaVO().getAbaSelecionada().equals("HI")) {
            getFecharMetaPerdaVO().setTotalApresentarRelatorio(new Double(getFecharMetaPerdaVO().getTotalizadorHistorico()));
            imprimirRelatorio(getEmpresaLogado(), getFecharMetaPerdaVO().getListaHistoricoContatoHistorico(), "Relatório Meta Desistentes", "Período: " + Uteis.getData(getDataInicio()) + " até " + Uteis.getData(getDataTermino()), "MetaDetalhadoClienteRel", getFecharMetaPerdaVO().getTotalApresentarRelatorio());

        }
    }

    /**
     * Método responsavel por gerar o relatorio em PDF
     *
     * @param empresa empresa do relatório
     * @param lista para impressão
     * @param tituloRel título do relatório
     * @param filtro filtro usado
     * @param nomeRel nome do relatório
     * <AUTHOR>
     */
    public void imprimirRelatorio(EmpresaVO empresa, List lista, String tituloRel, String filtro, String nomeRel, Double totalPessoas) throws Exception {
        imprimirRelatorio(empresa, lista, tituloRel, filtro, nomeRel, totalPessoas, getUsuarioLogado());
    }

    public void imprimirRelatorio(EmpresaVO empresa, List lista, String tituloRel, String filtro, String nomeRel, Double totalPessoas, UsuarioVO usuario) {
        try {
            String design = AberturaMetaVO.getIsDesignIReportRelatorio(nomeRel);
            String nomeEmpresa = "";
            if (empresa.getCodigo() != 0) {
                nomeEmpresa = empresa.getNome();
            }
            apresentarRelatorioObjetos(nomeRel, tituloRel, nomeEmpresa, "", "", "PDF", "/" + getFacade().getPassivo().getIdEntidade() + "/registros", design, usuario.getNome(), filtro, totalPessoas, lista);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    // ------------------------------------------ RELATORIO EXCEL
    // ------------------------------------------
    /**
     * Método responsavel por gerar a impressão da Meta Passivo em Excel
     *
     * <AUTHOR>
     */
    public void executarImpressaoExcelPassivo() {
        try {
            setAbrirPopupDownload(true);
            GeradorRelatorio obj = new GeradorRelatorio();
            if (getFecharMetaPassivoVO().getAbaSelecionada().equals("HJ")) {
                getFecharMetaPassivoVO().setFecharMetaDetalhadoVOs(getFacade().getFecharMetaDetalhado().consultarFecharMetaDetalhado(aberturaMetaVO, getFecharMetaPassivoVO().getIdentificadorMeta(), true, getEmpresaLogado().getCodigo()));
                obj.montarRelatorio("OBJETO", "EXCEL", "MetaDetalhadoExcelPassivoRel", AberturaMetaVO.getIsDesignIReportRelatorioExcel("MetaDetalhadoExcelPassivoRel"), getFecharMetaPassivoVO().getFecharMetaDetalhadoVOs(), new HashMap());
            } else if (getFecharMetaPassivoVO().getAbaSelecionada().equals("HI")) {
                obj.montarRelatorio("OBJETO", "EXCEL", "MetaDetalhadoExcelPassivoRel", AberturaMetaVO.getIsDesignIReportRelatorioExcel("MetaDetalhadoExcelPassivoRel"), getFecharMetaPassivoVO().getListaHistoricoContatoHistorico(), new HashMap());
            }
        } catch (Exception e) {
            setAbrirPopupDownload(false);
            setMensagemDetalhada("msg_erro", e.getMessage());
        }

    }

    /**
     * Método responsavel por gerar a impressão da Meta Indicado em Excel
     *
     * <AUTHOR>
     */
    public void executarImpressaoExcelIndicado() {
        try {
            setAbrirPopupDownload(true);
            GeradorRelatorio obj = new GeradorRelatorio();
            if (getFecharMetaIndicadoVO().getAbaSelecionada().equals("HJ")) {
                getFecharMetaIndicadoVO().setFecharMetaDetalhadoVOs(getFacade().getFecharMetaDetalhado().consultarFecharMetaDetalhado(aberturaMetaVO, getFecharMetaIndicadoVO().getIdentificadorMeta(), true, getEmpresaLogado().getCodigo()));
                obj.montarRelatorio("OBJETO", "EXCEL", "MetaDetalhadoExcelIndicadoRel", AberturaMetaVO.getIsDesignIReportRelatorioExcel("MetaDetalhadoExcelIndicadoRel"), getFecharMetaIndicadoVO().getFecharMetaDetalhadoVOs(), new HashMap());
            } else if (getFecharMetaIndicadoVO().getAbaSelecionada().equals("HI")) {
                obj.montarRelatorio("OBJETO", "EXCEL", "MetaDetalhadoExcelIndicadoRel", AberturaMetaVO.getIsDesignIReportRelatorioExcel("MetaDetalhadoExcelIndicadoRel"), getFecharMetaIndicadoVO().getListaHistoricoContatoHistorico(), new HashMap());
            }
        } catch (Exception e) {
            setAbrirPopupDownload(false);
            setMensagemDetalhada("msg_erro", e.getMessage());
        }

    }

    /**
     * Método responsavel por gerar a impressão da Meta Agendados em Excel
     *
     * <AUTHOR>
     */
    public void executarImpressaoExcelAgendados() {
        try {
            setAbrirPopupDownload(true);
            GeradorRelatorio obj = new GeradorRelatorio();
            if (getFecharMetaAgendadosVO().getAbaSelecionada().equals("HJ")) {
                getFecharMetaAgendadosVO().setFecharMetaDetalhadoVOs(getFacade().getFecharMetaDetalhado().consultarFecharMetaDetalhado(aberturaMetaVO, getFecharMetaAgendadosVO().getIdentificadorMeta(), true, getEmpresaLogado().getCodigo()));
                obj.montarRelatorio("OBJETO", "EXCEL", "MetaDetalhadoExcelClienteRel", AberturaMetaVO.getIsDesignIReportRelatorioExcel("MetaDetalhadoExcelAgendadosRel"), getFecharMetaAgendadosVO().getFecharMetaDetalhadoVOs(), new HashMap());
            } else if (getFecharMetaAgendadosVO().getAbaSelecionada().equals("HI")) {
                obj.montarRelatorio("OBJETO", "EXCEL", "MetaDetalhadoExcelClienteRel", AberturaMetaVO.getIsDesignIReportRelatorioExcel("MetaDetalhadoExcelAgendadosRel"), getFecharMetaAgendadosVO().getListaHistoricoContatoHistorico(), new HashMap());
            }
        } catch (Exception e) {
            setAbrirPopupDownload(false);
            setMensagemDetalhada("msg_erro", e.getMessage());
        }

    }

    public void executarImpressaoExcelLigacaoAgendados() {
        try {
            setAbrirPopupDownload(true);
            GeradorRelatorio obj = new GeradorRelatorio();
            if (getFecharMetaLigacaoAgendadosVO().getAbaSelecionada().equals("HJ")) {
                getFecharMetaLigacaoAgendadosVO().setFecharMetaDetalhadoVOs(
                        getFacade().getFecharMetaDetalhado().consultarFecharMetaDetalhado(
                        aberturaMetaVO, getFecharMetaLigacaoAgendadosVO().getIdentificadorMeta(),
                        true, getEmpresaLogado().getCodigo()));
                obj.montarRelatorio("OBJETO", "EXCEL", "MetaDetalhadoExcelClienteRel", AberturaMetaVO.getIsDesignIReportRelatorioExcel("MetaDetalhadoExcelAgendadosRel"), getFecharMetaLigacaoAgendadosVO().getFecharMetaDetalhadoVOs(), new HashMap());
            } else if (getFecharMetaLigacaoAgendadosVO().getAbaSelecionada().equals("HI")) {
                obj.montarRelatorio("OBJETO", "EXCEL", "MetaDetalhadoExcelClienteRel", AberturaMetaVO.getIsDesignIReportRelatorioExcel("MetaDetalhadoExcelAgendadosRel"), getFecharMetaLigacaoAgendadosVO().getListaHistoricoContatoHistorico(), new HashMap());
            }
        } catch (Exception e) {
            setAbrirPopupDownload(false);
            setMensagemDetalhada("msg_erro", e.getMessage());
        }

    }

    public void executarImpressaoExcelConversaoAgendados() {
        try {
            setAbrirPopupDownload(true);
            GeradorRelatorio obj = new GeradorRelatorio();
            if (getFecharMetaConversaoAgendadosVO().getAbaSelecionada().equals("HJ")) {
                getFecharMetaConversaoAgendadosVO().setFecharMetaDetalhadoVOs(getFacade().getFecharMetaDetalhado().consultarFecharMetaDetalhado(aberturaMetaVO, getFecharMetaConversaoAgendadosVO().getIdentificadorMeta(), true, getEmpresaLogado().getCodigo()));
                obj.montarRelatorio("OBJETO", "EXCEL", "MetaDetalhadoExcelClienteRel", AberturaMetaVO.getIsDesignIReportRelatorioExcel("MetaDetalhadoExcelConversaoAgendadosRel"), getFecharMetaConversaoAgendadosVO().getFecharMetaDetalhadoVOs(), new HashMap());
            } else if (getFecharMetaConversaoAgendadosVO().getAbaSelecionada().equals("HI")) {
                obj.montarRelatorio("OBJETO", "EXCEL", "MetaDetalhadoExcelClienteRel", AberturaMetaVO.getIsDesignIReportRelatorioExcel("MetaDetalhadoExcelConversaoAgendadosRel"), getFecharMetaConversaoAgendadosVO().getListaHistoricoContatoHistorico(), new HashMap());
            }
        } catch (Exception e) {
            setAbrirPopupDownload(false);
            setMensagemDetalhada("msg_erro", e.getMessage());
        }

    }

    /**
     * Método responsavel por gerar a impressão da Meta 24h em Excel
     *
     * <AUTHOR>
     */
    public void executarImpressaoExcelVinteQuatroHoras() {
        try {
            setAbrirPopupDownload(true);
            GeradorRelatorio obj = new GeradorRelatorio();
            if (getFecharMetaVinteQuatroHorasVO().getAbaSelecionada().equals("HJ")) {
                getFecharMetaVinteQuatroHorasVO().setFecharMetaDetalhadoVOs(getFacade().getFecharMetaDetalhado().consultarFecharMetaDetalhado(aberturaMetaVO, getFecharMetaVinteQuatroHorasVO().getIdentificadorMeta(), true, getEmpresaLogado().getCodigo()));
                obj.montarRelatorio("OBJETO", "EXCEL", "MetaDetalhadoExcelClienteRel", AberturaMetaVO.getIsDesignIReportRelatorioExcel("MetaDetalhadoExcelClienteRel"), getFecharMetaVinteQuatroHorasVO().getFecharMetaDetalhadoVOs(), new HashMap());
            } else if (getFecharMetaVinteQuatroHorasVO().getAbaSelecionada().equals("HI")) {
                obj.montarRelatorio("OBJETO", "EXCEL", "MetaDetalhadoExcelClienteRel", AberturaMetaVO.getIsDesignIReportRelatorioExcel("MetaDetalhadoExcelClienteRel"), getFecharMetaVinteQuatroHorasVO().getListaHistoricoContatoHistorico(), new HashMap());
            }
        } catch (Exception e) {
            setAbrirPopupDownload(false);
            setMensagemDetalhada("msg_erro", e.getMessage());
        }

    }

    /**
     * Método responsavel por gerar a impressão da Meta 24h em Excel
     *
     * <AUTHOR>
     */
    public void executarImpressaoExcelVendas() {
        try {
            setAbrirPopupDownload(true);
            GeradorRelatorio obj = new GeradorRelatorio();
            if (getFecharMetaVendasVO().getAbaSelecionada().equals("HJ")) {
                getFecharMetaVendasVO().setFecharMetaDetalhadoVOs(getFacade().getFecharMetaDetalhado().consultarFecharMetaDetalhado(aberturaMetaVO, getFecharMetaVendasVO().getIdentificadorMeta(), true, getEmpresaLogado().getCodigo()));
                obj.montarRelatorio("OBJETO", "EXCEL", "MetaDetalhadoExcelClienteRel", AberturaMetaVO.getIsDesignIReportRelatorioExcel("MetaDetalhadoExcelClienteRel"), getFecharMetaVendasVO().getFecharMetaDetalhadoVOs(), new HashMap());
            } else if (getFecharMetaVendasVO().getAbaSelecionada().equals("HI")) {
                obj.montarRelatorio("OBJETO", "EXCEL", "MetaDetalhadoExcelClienteRel", AberturaMetaVO.getIsDesignIReportRelatorioExcel("MetaDetalhadoExcelClienteRel"), getFecharMetaVendasVO().getListaHistoricoContatoHistorico(), new HashMap());
            }
        } catch (Exception e) {
            setAbrirPopupDownload(false);
            setMensagemDetalhada("msg_erro", e.getMessage());
        }

    }

    public void executarImpressaoExcelVencidos() {
        try {
            setAbrirPopupDownload(true);
            GeradorRelatorio obj = new GeradorRelatorio();
            if (getFecharMetaVencidosVO().getAbaSelecionada().equals("HJ")) {
                getFecharMetaVencidosVO().setFecharMetaDetalhadoVOs(getFacade().getFecharMetaDetalhado().consultarFecharMetaDetalhado(aberturaMetaVO, getFecharMetaVencidosVO().getIdentificadorMeta(), true, getEmpresaLogado().getCodigo()));
                obj.montarRelatorio("OBJETO", "EXCEL", "MetaDetalhadoExcelClienteRel", AberturaMetaVO.getIsDesignIReportRelatorioExcel("MetaDetalhadoExcelClienteRel"), getFecharMetaVencidosVO().getFecharMetaDetalhadoVOs(), new HashMap());
            } else if (getFecharMetaVencidosVO().getAbaSelecionada().equals("HI")) {
                obj.montarRelatorio("OBJETO", "EXCEL", "MetaDetalhadoExcelClienteRel", AberturaMetaVO.getIsDesignIReportRelatorioExcel("MetaDetalhadoExcelClienteRel"), getFecharMetaVencidosVO().getListaHistoricoContatoHistorico(), new HashMap());
            }
        } catch (Exception e) {
            setAbrirPopupDownload(false);
            setMensagemDetalhada("msg_erro", e.getMessage());
        }

    }

    /**
     * Método responsavel por gerar a impressão da Meta Faturamento em Excel
     *
     * <AUTHOR>
     */
    public void executarImpressaoExcelFaturamento() {
        try {
            setAbrirPopupDownload(true);
            GeradorRelatorio obj = new GeradorRelatorio();
            if (getFecharMetaFaturamentoVO().getAbaSelecionada().equals("HJ")) {
                getFecharMetaFaturamentoVO().setFecharMetaDetalhadoVOs(getFacade().getFecharMetaDetalhado().consultarFecharMetaDetalhado(aberturaMetaVO, getFecharMetaFaturamentoVO().getIdentificadorMeta(), true, getEmpresaLogado().getCodigo()));
                obj.montarRelatorio("OBJETO", "EXCEL", "MetaDetalhadoExcelClienteRel", AberturaMetaVO.getIsDesignIReportRelatorioExcel("MetaDetalhadoExcelClienteRel"), getFecharMetaFaturamentoVO().getFecharMetaDetalhadoVOs(), new HashMap());
            } else if (getFecharMetaFaturamentoVO().getAbaSelecionada().equals("HI")) {
                obj.montarRelatorio("OBJETO", "EXCEL", "MetaDetalhadoExcelClienteRel", AberturaMetaVO.getIsDesignIReportRelatorioExcel("MetaDetalhadoExcelClienteRel"), getFecharMetaFaturamentoVO().getListaHistoricoContatoHistorico(), new HashMap());
            }
        } catch (Exception e) {
            setAbrirPopupDownload(false);
            setMensagemDetalhada("msg_erro", e.getMessage());
        }

    }

    /**
     * Método responsavel por gerar a impressão da Meta Renovação em Excel
     *
     * <AUTHOR>
     */
    public void executarImpressaoExcelRenovacao() {
        try {
            setAbrirPopupDownload(true);
            GeradorRelatorio obj = new GeradorRelatorio();
            if (getFecharMetaRenovacaoVO().getAbaSelecionada().equals("HJ")) {
                getFecharMetaRenovacaoVO().setFecharMetaDetalhadoVOs(getFacade().getFecharMetaDetalhado().consultarFecharMetaDetalhado(aberturaMetaVO, getFecharMetaRenovacaoVO().getIdentificadorMeta(), true, getEmpresaLogado().getCodigo()));
                obj.montarRelatorio("OBJETO", "EXCEL", "MetaDetalhadoExcelClienteRel", AberturaMetaVO.getIsDesignIReportRelatorioExcel("MetaDetalhadoExcelClienteRel"), getFecharMetaRenovacaoVO().getFecharMetaDetalhadoVOs(), new HashMap());
            } else if (getFecharMetaRenovacaoVO().getAbaSelecionada().equals("HI")) {
                obj.montarRelatorio("OBJETO", "EXCEL", "MetaDetalhadoExcelClienteRel", AberturaMetaVO.getIsDesignIReportRelatorioExcel("MetaDetalhadoExcelClienteRel"), getFecharMetaRenovacaoVO().getListaHistoricoContatoHistorico(), new HashMap());
            }
        } catch (Exception e) {
            setAbrirPopupDownload(false);
            setMensagemDetalhada("msg_erro", e.getMessage());
        }

    }

    /**
     * Método responsavel por gerar a impressão da Meta PosVenda em Excel
     *
     * <AUTHOR>
     */
    public void executarImpressaoExcelPosVenda() {
        try {
            setAbrirPopupDownload(true);
            GeradorRelatorio obj = new GeradorRelatorio();
            if (getFecharMetaPosVendaVO().getAbaSelecionada().equals("HJ")) {
                getFecharMetaPosVendaVO().setFecharMetaDetalhadoVOs(getFacade().getFecharMetaDetalhado().consultarFecharMetaDetalhado(aberturaMetaVO, getFecharMetaPosVendaVO().getIdentificadorMeta(), false, getEmpresaLogado().getCodigo()));
                obj.montarRelatorio("OBJETO", "EXCEL", "MetaDetalhadoExcelClienteRel", AberturaMetaVO.getIsDesignIReportRelatorioExcel("MetaDetalhadoExcelClienteRel"), getFecharMetaPosVendaVO().getFecharMetaDetalhadoVOs(), new HashMap());
            } else if (getFecharMetaPosVendaVO().getAbaSelecionada().equals("HI")) {
                obj.montarRelatorio("OBJETO", "EXCEL", "MetaDetalhadoExcelClienteRel", AberturaMetaVO.getIsDesignIReportRelatorioExcel("MetaDetalhadoExcelClienteRel"), getFecharMetaPosVendaVO().getListaHistoricoContatoHistorico(), new HashMap());
            }
        } catch (Exception e) {
            setAbrirPopupDownload(false);
            setMensagemDetalhada("msg_erro", e.getMessage());
        }

    }

    /**
     * Método responsavel por gerar a impressão da Meta GrupoRisco em Excel
     *
     * <AUTHOR>
     */
    public void executarImpressaoExcelGrupoRisco() {
        try {
            setAbrirPopupDownload(true);
            GeradorRelatorio obj = new GeradorRelatorio();
            if (getFecharMetaGrupoRiscoVO().getAbaSelecionada().equals("HJ")) {
                getFecharMetaGrupoRiscoVO().setFecharMetaDetalhadoVOs(getFacade().getFecharMetaDetalhado().consultarFecharMetaDetalhado(aberturaMetaVO, getFecharMetaGrupoRiscoVO().getIdentificadorMeta(), false, getEmpresaLogado().getCodigo()));
                obj.montarRelatorio("OBJETO", "EXCEL", "MetaDetalhadoExcelClienteRel", AberturaMetaVO.getIsDesignIReportRelatorioExcel("MetaDetalhadoExcelClienteRel"), getFecharMetaGrupoRiscoVO().getFecharMetaDetalhadoVOs(), new HashMap());
            } else if (getFecharMetaGrupoRiscoVO().getAbaSelecionada().equals("HI")) {
                obj.montarRelatorio("OBJETO", "EXCEL", "MetaDetalhadoExcelClienteRel", AberturaMetaVO.getIsDesignIReportRelatorioExcel("MetaDetalhadoExcelClienteRel"), getFecharMetaGrupoRiscoVO().getListaHistoricoContatoHistorico(), new HashMap());
            }
        } catch (Exception e) {
            setAbrirPopupDownload(false);
            setMensagemDetalhada("msg_erro", e.getMessage());
        }

    }

    /**
     * Método responsavel por gerar a impressão da Meta Aniversariante em Excel
     *
     * <AUTHOR>
     */
    public void executarImpressaoExcelAniversariante() {
        try {
            setAbrirPopupDownload(true);
            GeradorRelatorio obj = new GeradorRelatorio();
            if (getFecharMetaAniversarianteVO().getAbaSelecionada().equals("HJ")) {
                getFecharMetaAniversarianteVO().setFecharMetaDetalhadoVOs(getFacade().getFecharMetaDetalhado().consultarFecharMetaDetalhado(aberturaMetaVO, getFecharMetaAniversarianteVO().getIdentificadorMeta(), false, getEmpresaLogado().getCodigo()));
                obj.montarRelatorio("OBJETO", "EXCEL", "MetaDetalhadoExcelClienteRel", AberturaMetaVO.getIsDesignIReportRelatorioExcel("MetaDetalhadoExcelClienteRel"), getFecharMetaAniversarianteVO().getFecharMetaDetalhadoVOs(), new HashMap());
            } else if (getFecharMetaAniversarianteVO().getAbaSelecionada().equals("HI")) {
                obj.montarRelatorio("OBJETO", "EXCEL", "MetaDetalhadoExcelClienteRel", AberturaMetaVO.getIsDesignIReportRelatorioExcel("MetaDetalhadoExcelClienteRel"), getFecharMetaAniversarianteVO().getListaHistoricoContatoHistorico(), new HashMap());
            }
        } catch (Exception e) {
            setAbrirPopupDownload(false);
            setMensagemDetalhada("msg_erro", e.getMessage());
        }

    }

    /**
     * Método responsavel por gerar a impressão da Meta Faltosos em Excel
     *
     * <AUTHOR>
     */
    public void executarImpressaoExcelFaltosos() {
        try {
            setAbrirPopupDownload(true);
            GeradorRelatorio obj = new GeradorRelatorio();
            if (getFecharMetaFaltasVO().getAbaSelecionada().equals("HJ")) {
                getFecharMetaFaltasVO().setFecharMetaDetalhadoVOs(getFacade().getFecharMetaDetalhado().consultarFecharMetaDetalhado(aberturaMetaVO, getFecharMetaFaltasVO().getIdentificadorMeta(), false, getEmpresaLogado().getCodigo()));
                obj.montarRelatorio("OBJETO", "EXCEL", "MetaDetalhadoExcelClienteRel", AberturaMetaVO.getIsDesignIReportRelatorioExcel("MetaDetalhadoExcelClienteRel"), getFecharMetaFaltasVO().getFecharMetaDetalhadoVOs(), new HashMap());
            } else if (getFecharMetaFaltasVO().getAbaSelecionada().equals("HI")) {
                obj.montarRelatorio("OBJETO", "EXCEL", "MetaDetalhadoExcelClienteRel", AberturaMetaVO.getIsDesignIReportRelatorioExcel("MetaDetalhadoExcelClienteRel"), getFecharMetaFaltasVO().getListaHistoricoContatoHistorico(), new HashMap());
            }
        } catch (Exception e) {
            setAbrirPopupDownload(false);
            setMensagemDetalhada("msg_erro", e.getMessage());
        }

    }

    /**
     * Método responsavel por gerar a impressão da Meta Perda em Excel
     *
     * <AUTHOR>
     */
    public void executarImpressaoExcelPerda() {
        try {
            setAbrirPopupDownload(true);
            GeradorRelatorio obj = new GeradorRelatorio();
            if (getFecharMetaPerdaVO().getAbaSelecionada().equals("HJ")) {
                obj.montarRelatorio("OBJETO", "EXCEL", "MetaDetalhadoExcelClienteRel", AberturaMetaVO.getIsDesignIReportRelatorioExcel("MetaDetalhadoExcelClienteRel"), getFecharMetaPerdaVO().getFecharMetaDetalhadoVOs(), new HashMap());
            } else if (getFecharMetaPerdaVO().getAbaSelecionada().equals("HI")) {
                obj.montarRelatorio("OBJETO", "EXCEL", "MetaDetalhadoExcelClienteRel", AberturaMetaVO.getIsDesignIReportRelatorioExcel("MetaDetalhadoExcelClienteRel"), getFecharMetaPerdaVO().getListaHistoricoContatoHistorico(), new HashMap());
            }
        } catch (Exception e) {
            setAbrirPopupDownload(false);
            setMensagemDetalhada("msg_erro", e.getMessage());
        }

    }

    // ----------------------------------------- TELA INDICADOR DE VENDAS
    // ---------------------------------
    /**
     * Método responsavel por atualizar a meta no momento em que clicar no botão
     * atualizar.
     */
    public void atualizarMetaDiaVenda() {
        try {
            getFacade().getAberturaMeta().atualizarMetaVendaDia(aberturaMetaVO, getEmpresaLogado().getCodigo());

            for (GrupoColaboradorVO grupo : aberturaMetaVO.getGrupoColaboradorListaVenda()) {
                for (GrupoColaboradorParticipanteVO parti : grupo.getGrupoColaboradorParticipanteVOs()) {
                    if (parti.getUsuarioParticipante().getCodigo().equals(aberturaMetaVO.getColaboradorResponsavel().getCodigo())) {
                        // parti.getUsuarioParticipante().getColaboradorVO().setColaboradorEscolhidoIndiceConversao(true);
                        parti.setGrupoColaboradorParticipanteEscolhido(true);
                    } else {
                        // parti.getUsuarioParticipante().getColaboradorVO().setColaboradorEscolhidoIndiceConversao(false);
                        parti.setGrupoColaboradorParticipanteEscolhido(false);
                    }
                }
            }
            getAberturaMetaVO().setExisteMetaParaParticipante(true);
            limparMsg();

            Ordenacao.ordenarLista(aberturaMetaVO.getFecharMetaVosEstudio(), "ordemTotalizadorMeta");
        } catch (Exception e) {
            getAberturaMetaVO().setExisteMetaParaParticipante(false);
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    /**
     * Metodo responsavel por atualizar Meta do dia no momento em que clicar no
     * selectBooleanCheckbox.
     */
    public void atualizarMetaDiaPorColaboradorVenda() throws Exception {
      // desmarcarColaboradorIndisponivel();
        atualizarMetaDiaPorColaboradorVenda(getUsuarioLogado());
    }
    public void selecionarMetaColaboradorVenda()throws  Exception{
        desmarcarColaboradorIndisponivelVenda();
        atualizarMetaDiaPorColaboradorVenda(getUsuarioLogado());
    }
    public void atualizarMetaDiaPorColaboradorVenda(UsuarioVO usuario) {
        try {
//            if (!colaboradorSelecionado(aberturaMetaVO.getGrupoColaboradorListaVenda())) {
//                if (usuario.getAdministrador()) {
//                    consultarMetaDiaAdmin();
//                } else {
//                    validarExibirMeta(usuario);
//                }
//            } else {
            aberturaMetaVO.setExisteMetaParaParticipante(false);
            aberturaMetaVO.setFecharMetaVosVenda(new ArrayList<FecharMetaVO>());

            for (FasesCRMEnum fase : FasesCRMEnum.values()) {
                if (fase.getTipoFase().equals(TipoFaseCRM.VENDAS)) {
                    FecharMetaVO fm = new FecharMetaVO();
                    fm.setIdentificadorMeta(fase.getSigla());
                    aberturaMetaVO.getFecharMetaVosVenda().add(fm);
                }
            }
            try {
                Ordenacao.ordenarLista(aberturaMetaVO.getFecharMetaVosVenda(), "ordemTotalizadorMeta");
                Ordenacao.ordenarLista(aberturaMetaVO.getFecharMetaVosRetencao(), "ordemTotalizadorMeta");
                Ordenacao.ordenarLista(aberturaMetaVO.getFecharMetaVosEstudio(), "ordemTotalizadorMeta");
            }catch (Exception err){}
            aberturaMetaVO.setEmpresaVO(getEmpresaLogado());
            getFacade().getAberturaMeta().atualizarMetaVendaDia(aberturaMetaVO, getEmpresaLogado().getCodigo());
            getFacade().getAberturaMeta().atualizarMetaVendaPorColaborador(aberturaMetaVO, getEmpresaLogado().getCodigo());
            aberturaMetaVO.setFecharMetaVosVenda(retirarMetaNaoCalculadas(aberturaMetaVO.getFecharMetaVosVenda()));
            aberturaMetaVO.setFecharMetaVosEstudio(retirarMetaNaoCalculadas(aberturaMetaVO.getFecharMetaVosEstudio()));
//            }
            setMensagem("");
            setMensagemID("");
            setMensagemDetalhada("", "");
        } catch (Exception e) {
            getAberturaMetaVO().setExisteMetaParaParticipante(false);
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }
    public ColaboradorIndisponivelCrmVO colaboradorResponsavelIndisponivel() throws Exception{
        for(ColaboradorIndisponivelCrmVO colaboradorIndisponivelCrmVO : getListaColaboradorIndisponivel()){
            if(colaboradorIndisponivelCrmVO.getColaboradorIndisponivelVO().getCodigo().equals(getAberturaMetaVO().getColaboradorResponsavel().getColaboradorVO().getCodigo())
                    && colaboradorIndisponivelCrmVO.indisponibilidadeColaboradorPorData(getAberturaMetaVO().getDia())){
                return colaboradorIndisponivelCrmVO;
            }
        }
        return null;
    }
    public Boolean getMostrarGruposColaboradoresVenda() throws Exception{

        ColaboradorIndisponivelCrmVO indisponivelCrmVO = colaboradorResponsavelIndisponivel();
        if(indisponivelCrmVO == null)
        for (GrupoColaboradorVO grupoColaborador : getAberturaMetaVO().getGrupoColaboradorListaVenda()) {
            for (GrupoColaboradorParticipanteVO colaboradorResponsavel : grupoColaborador.getGrupoColaboradorParticipanteVOs()) {
                if (colaboradorResponsavel.getTipoVisao().equals("AC")
                        && getAberturaMetaVO().getColaboradorResponsavel().getColaboradorVO().getCodigo().equals(colaboradorResponsavel.getColaboradorParticipante().getCodigo()))
                    return false;
            }
        }
        else
            for (GrupoColaboradorVO grupoColaborador : getAberturaMetaVO().getGrupoColaboradorListaVenda()) {
                for (GrupoColaboradorParticipanteVO colaboradorResponsavel : grupoColaborador.getGrupoColaboradorParticipanteVOs()) {
                    if (colaboradorResponsavel.getTipoVisao().equals("AC")
                            && colaboradorResponsavel.getColaboradorParticipante().getCodigo().equals(indisponivelCrmVO.getColaboradorSuplenteVO().getCodigo()))
                        return false;
                }
            }
        return true;
    }
    public ColaboradorIndisponivelCrmVO colaboradorIndisponivelSelecionado(){
      GrupoColaboradorParticipanteVO colaboradorSelecionado =  (GrupoColaboradorParticipanteVO) context().getExternalContext().getRequestMap().get("grupoColaboradorParticipante");
      for(ColaboradorIndisponivelCrmVO indisponivelCrmVO : getListaColaboradorIndisponivel()){
          if((indisponivelCrmVO.getColaboradorSuplenteVO().getCodigo().equals(colaboradorSelecionado.getColaboradorParticipante().getCodigo())
                  || indisponivelCrmVO.getColaboradorIndisponivelVO().getCodigo().equals(colaboradorSelecionado.getColaboradorParticipante().getCodigo()))
                && indisponivelCrmVO.indisponibilidadeColaboradorPorData(getAberturaMetaVO().getDia()))

              return  indisponivelCrmVO;
      }
        return null;
    }
    public Boolean getMostrarGruposColaboradoresRetencao() throws Exception{
        ColaboradorIndisponivelCrmVO indisponivelCrmVO = colaboradorResponsavelIndisponivel();
        if(indisponivelCrmVO == null)
        for (GrupoColaboradorVO grupoColaborador : getAberturaMetaVO().getGrupoColaboradorListaRetencao()) {
            for (GrupoColaboradorParticipanteVO colaboradorResponsavel : grupoColaborador.getGrupoColaboradorParticipanteVOs()) {
                if (colaboradorResponsavel.getTipoVisao().equals("AC")
                        && getAberturaMetaVO().getColaboradorResponsavel().getColaboradorVO().getCodigo().equals(colaboradorResponsavel.getColaboradorParticipante().getCodigo()))
                    return false;
            }
        }else
            for (GrupoColaboradorVO grupoColaborador : getAberturaMetaVO().getGrupoColaboradorListaRetencao()) {
                for (GrupoColaboradorParticipanteVO colaboradorResponsavel : grupoColaborador.getGrupoColaboradorParticipanteVOs()) {
                    if (colaboradorResponsavel.getTipoVisao().equals("AC")
                            && colaboradorResponsavel.getColaboradorParticipante().getCodigo().equals(indisponivelCrmVO.getColaboradorSuplenteVO().getCodigo()))
                        return false;
                }
            }
        return true;
    }
    public Boolean getMostrarGruposColaboradoresEstudio() throws Exception{
        ColaboradorIndisponivelCrmVO indisponivelCrmVO = colaboradorResponsavelIndisponivel();
        if(indisponivelCrmVO == null)
        for (GrupoColaboradorVO grupoColaborador : getAberturaMetaVO().getGrupoColaboradorListaEstudio()) {
            for (GrupoColaboradorParticipanteVO colaboradorResponsavel : grupoColaborador.getGrupoColaboradorParticipanteVOs()) {
                if (colaboradorResponsavel.getTipoVisao().equals("AC")
                        && getAberturaMetaVO().getColaboradorResponsavel().getColaboradorVO().getCodigo().equals(colaboradorResponsavel.getColaboradorParticipante().getCodigo()))
                    return false;
            }
        }else
            for (GrupoColaboradorVO grupoColaborador : getAberturaMetaVO().getGrupoColaboradorListaEstudio()) {
                for (GrupoColaboradorParticipanteVO colaboradorResponsavel : grupoColaborador.getGrupoColaboradorParticipanteVOs()) {
                    if (colaboradorResponsavel.getTipoVisao().equals("AC")
                            && colaboradorResponsavel.getColaboradorParticipante().getCodigo().equals(indisponivelCrmVO.getColaboradorSuplenteVO().getCodigo()))
                        return false;
                }
            }
        return true;
    }
    //Ao desmarcar um colaborador Suplente também é desmarcado o colaborador indisponível vendas
    public void  desmarcarColaboradorIndisponivelVenda() throws  Exception{
        GrupoColaboradorParticipanteVO colaboradorSelecionado =  (GrupoColaboradorParticipanteVO) context().getExternalContext().getRequestMap().get("grupoColaboradorParticipante");

            ColaboradorIndisponivelCrmVO objC = colaboradorIndisponivelSelecionado();
        if(objC!=null)
                for(GrupoColaboradorVO grupoColaborador : getAberturaMetaVO().getGrupoColaboradorListaVenda()){
                    for(GrupoColaboradorParticipanteVO colaboradorIndisp : grupoColaborador.getGrupoColaboradorParticipanteVOs()){
                        if(objC.getColaboradorIndisponivelVO().getCodigo().equals(colaboradorIndisp.getColaboradorParticipante().getCodigo()) ){
                            colaboradorIndisp.setGrupoColaboradorParticipanteEscolhido(colaboradorSelecionado.getGrupoColaboradorParticipanteEscolhido());
                        }
                }
            }

    }
    //Ao desmarcar um colaborador Suplente também é desmarcado o colaborador indisponível retenção
    public void  desmarcarColaboradorIndisponivelRetencao() throws  Exception{
               GrupoColaboradorParticipanteVO colaboradorSelecionado =  (GrupoColaboradorParticipanteVO) context().getExternalContext().getRequestMap().get("grupoColaboradorParticipante");
                 ColaboradorIndisponivelCrmVO objC = colaboradorIndisponivelSelecionado();
                if(objC!=null)
                 for(GrupoColaboradorVO grupoColaborador : getAberturaMetaVO().getGrupoColaboradorListaRetencao()){
                    for(GrupoColaboradorParticipanteVO colaboradorIndisp : grupoColaborador.getGrupoColaboradorParticipanteVOs()){
                        if(objC.getColaboradorIndisponivelVO().getCodigo().equals(colaboradorIndisp.getColaboradorParticipante().getCodigo())){
                            colaboradorIndisp.setGrupoColaboradorParticipanteEscolhido(colaboradorSelecionado.getGrupoColaboradorParticipanteEscolhido());
                        }
                    }
                }
     }

    private void validarExibirMeta(UsuarioVO usuario) throws Exception {
        AberturaMetaVO rec = getFacade().getAberturaMeta().consultarAberturaPorCodigoUsuario(usuario.getCodigo(), aberturaMetaVO.getDia(), getEmpresaLogado().getCodigo(), Uteis.NIVELMONTARDADOS_TODOS);
        if (rec != null && !UteisValidacao.emptyNumber(rec.getCodigo())) {
            exibirMeta(rec);
        } else {
            throw new ConsistirException("Ainda não foi registrado nenhuma abertura de meta para os participantes selecionados.");
        }
    }

    /**
     * <AUTHOR> 20/05/2011
     */
    private void consultarMetaDiaAdmin() throws Exception {
        List<AberturaMetaVO> metas = getFacade().getAberturaMeta().consultarPorResponsavelPeriodo(null, null,
                aberturaMetaVO.getDia(), aberturaMetaVO.getDia(), null, Uteis.NIVELMONTARDADOS_TODOS);
        if (!metas.isEmpty()) {
            for (AberturaMetaVO meta : metas) {
                meta = getFacade().getAberturaMeta().inicializarDadosAberturaMetaParaComecarTrabalhar(getUsuarioLogado(), meta, getEmpresaLogado().getCodigo());
                aberturaMetaVO.addMeta(meta);
            }

            setApresentarTelaFormularioFechamentoDia(false);
            //Setando o colaborador responsavel;
            setColaboradorResponsavel(getAberturaMetaVO().getColaboradorResponsavel());
            //Setando a AberturaMeta no obj FecharMetaVO
            getFacade().getAberturaMeta().executarInclusaoAberturaMetaFecharMeta(getAberturaMetaVO());
            //Setando AbeturaMetaVO preenchida em outra variavel do controlador
            setAberturaMeta(getAberturaMetaVO());
            getAberturaMetaVO().setExisteMetaParaParticipante(true);
        } else {
            getAberturaMetaVO().setExisteMetaParaParticipante(false);
            getAberturaMetaVO().getResponsavelCadastro().setNome("");
            getAberturaMetaVO().getColaboradorResponsavel().setNome("");
            throw new ConsistirException("Ainda não foi registrado nenhuma abertura de meta para o dia selecionado.");
        }
    }

    /**
     * Metodo responsavel por atualizar Meta do dia no momento em que clicar no
     * selectBooleanCheckbox.
     */
    public void atualizarMetaDiaPorColaboradorRetencao() throws Exception {
        atualizarMetaDiaPorColaboradorRetencao(getUsuarioLogado());
    }
    
    public void atualizarMetaDiaPorColaboradorEstudio() throws Exception {
        atualizarMetaDiaPorColaboradorEstudio(getUsuarioLogado());
    }
    
     public void atualizarMetaDiaPorColaboradorEstudio(UsuarioVO usuario) {
        try {
//            if (!colaboradorSelecionado(aberturaMetaVO.getGrupoColaboradorListaRetencao())) {
//                if (usuario.getAdministrador()) {
//                    consultarMetaDiaAdmin();
//                } else {
//                    validarExibirMeta(usuario);
//                }
//            } else {
            aberturaMetaVO.setExisteMetaParaParticipante(false);
            aberturaMetaVO.setFecharMetaVosEstudio(new ArrayList<FecharMetaVO>());

            for (FasesCRMEnum fase : FasesCRMEnum.values()) {
                if (fase.getTipoFase().equals(TipoFaseCRM.ESTUDIO)) {
                    FecharMetaVO fm = new FecharMetaVO();
                    fm.setIdentificadorMeta(fase.getSigla());
                    aberturaMetaVO.getFecharMetaVosRetencao().add(fm);
                }
            }
            try {
                Ordenacao.ordenarLista(aberturaMetaVO.getFecharMetaVosVenda(), "ordemTotalizadorMeta");
                Ordenacao.ordenarLista(aberturaMetaVO.getFecharMetaVosRetencao(), "ordemTotalizadorMeta");
                Ordenacao.ordenarLista(aberturaMetaVO.getFecharMetaVosEstudio(), "ordemTotalizadorMeta");
            }catch (Exception ex){}
            aberturaMetaVO.setEmpresaVO(getEmpresaLogado());
            getFacade().getAberturaMeta().atualizarMetaEstudioDia(aberturaMetaVO);
            getFacade().getAberturaMeta().atualizarMetaEstudioPorColaborador(aberturaMetaVO, getEmpresaLogado().getCodigo());
            aberturaMetaVO.setFecharMetaVosEstudio(retirarMetaNaoCalculadas(aberturaMetaVO.getFecharMetaVosEstudio()));

//            }
            setMensagem("");
            setMensagemID("");
            setMensagemDetalhada("", "");

        } catch (Exception e) {
            getAberturaMetaVO().setExisteMetaParaParticipante(false);
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }
    public void selecionarMetaColaboradorRetencao() throws Exception {
        desmarcarColaboradorIndisponivelRetencao();
        atualizarMetaDiaPorColaboradorRetencao(getUsuarioLogado());
    }
    public void atualizarMetaDiaPorColaboradorRetencao(UsuarioVO usuario) {
        try {
//            if (!colaboradorSelecionado(aberturaMetaVO.getGrupoColaboradorListaRetencao())) {
//                if (usuario.getAdministrador()) {
//                    consultarMetaDiaAdmin();
//                } else {
//                    validarExibirMeta(usuario);
//                }
//            } else {
            aberturaMetaVO.setExisteMetaParaParticipante(false);
            aberturaMetaVO.setFecharMetaVosRetencao(new ArrayList<FecharMetaVO>());

            for (FasesCRMEnum fase : FasesCRMEnum.values()) {
                if (fase.getTipoFase().equals(TipoFaseCRM.RETENCAO)) {
                    FecharMetaVO fm = new FecharMetaVO();
                    fm.setIdentificadorMeta(fase.getSigla());
                    aberturaMetaVO.getFecharMetaVosRetencao().add(fm);
                }
            }
            try {
                Ordenacao.ordenarLista(aberturaMetaVO.getFecharMetaVosVenda(), "ordemTotalizadorMeta");
                Ordenacao.ordenarLista(aberturaMetaVO.getFecharMetaVosRetencao(), "ordemTotalizadorMeta");
                Ordenacao.ordenarLista(aberturaMetaVO.getFecharMetaVosEstudio(), "ordemTotalizadorMeta");
            }catch (Exception ex){}
            aberturaMetaVO.setEmpresaVO(getEmpresaLogado());
            getFacade().getAberturaMeta().atualizarMetaRetencaoDia(aberturaMetaVO);
            getFacade().getAberturaMeta().atualizarMetaRetencaoPorColaborador(aberturaMetaVO, getEmpresaLogado().getCodigo());
            aberturaMetaVO.setFecharMetaVosRetencao(retirarMetaNaoCalculadas(aberturaMetaVO.getFecharMetaVosRetencao()));

//            }
            setMensagem("");
            setMensagemID("");
            setMensagemDetalhada("", "");

        } catch (Exception e) {
            getAberturaMetaVO().setExisteMetaParaParticipante(false);
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    private boolean colaboradorSelecionado(List<GrupoColaboradorVO> listaGrupos) {
        Boolean retorno = Boolean.FALSE;
        EXT:
        for (GrupoColaboradorVO grupo : listaGrupos) {
            for (GrupoColaboradorParticipanteVO participante : grupo.getGrupoColaboradorParticipanteVOs()) {
                if (participante.getGrupoColaboradorParticipanteEscolhido()) {
                    retorno = Boolean.TRUE;
                    break EXT;
                }
            }
        }
        return retorno;

    }

    /**
     * Método responsavel por consultar fecharMetaDetalhadoAgendados no momento
     * que clico no indicador de Vendas opção Agendamento, preenchendo a lista
     * que aparecerá na tela metaAgendadosDetalhadaForm.
     */
    public void consultarFecharMetaDetalhadoVenda() {
        FecharMetaVO obj = (FecharMetaVO) context().getExternalContext().getRequestMap().get("fecharMeta");
        consultarFecharMetaDetalhada(obj);
    }

    public void atualizarFecharMetaDetalhada(final ActionEvent event) {
        String identificador = event.getComponent().getAttributes().get("identificador").toString();
        FecharMetaVO obj = new FecharMetaVO();
        obj.setIdentificadorMeta(identificador);
        consultarFecharMetaDetalhada(obj);
    }

    /**
     *
     * <AUTHOR> 20/05/2011
     */
    private void consultarFecharMetaDetalhada(FecharMetaVO obj) {
        try {
            if (obj.getIdentificadorMeta().equals(FasesCRMEnum.LIGACAO_AGENDADOS_AMANHA.getSigla())) {
                setFecharMetaLigacaoAgendadosVO(obj);
                getFacade().getFecharMeta().inicializarDadosParaTelaMetaDetalhando(getFecharMetaLigacaoAgendadosVO());
                inicializarDadosAbaHojeLigacaoAgendados();
                limparTotais(getFecharMetaLigacaoAgendadosVO());
            } else if (obj.getIdentificadorMeta().equals(FasesCRMEnum.CONVERSAO_AGENDADOS.getSigla())) {
                setFecharMetaConversaoAgendadosVO(obj);
                getFacade().getFecharMeta().inicializarDadosParaTelaMetaDetalhando(getFecharMetaConversaoAgendadosVO());
                inicializarDadosAbaHojeConversaoAgendados();
                limparTotais(getFecharMetaConversaoAgendadosVO());
            } else if (obj.getIdentificadorMeta().equals(FasesCRMEnum.CONVERSAO_EX_ALUNOS.getSigla())) {
                setFecharMetaConversaoExAlunosVO(obj);
                getFacade().getFecharMeta().inicializarDadosParaTelaMetaDetalhando(getFecharMetaConversaoExAlunosVO());
                inicializarDadosAbaHojeConversaoAgendados();
                limparTotais(getFecharMetaConversaoExAlunosVO());
            } else if (obj.getIdentificadorMeta().equals(FasesCRMEnum.CONVERSAO_VISITANTES_ANTIGOS.getSigla())) {
                setFecharMetaConversaoVisitantesAntigos(obj);
                getFacade().getFecharMeta().inicializarDadosParaTelaMetaDetalhando(getFecharMetaConversaoVisitantesAntigos());
                limparTotais(getFecharMetaConversaoVisitantesAntigos());

            } else if (obj.getIdentificadorMeta().equals(FasesCRMEnum.CONVERSAO_INDICADOS.getSigla())) {
                setFecharMetaConversaoIndicados(obj);
                getFacade().getFecharMeta().inicializarDadosParaTelaMetaDetalhando(getFecharMetaConversaoIndicados());
                limparTotais(getFecharMetaConversaoIndicados());
            } else if (obj.getIdentificadorMeta().equals(FasesCRMEnum.CONVERSAO_DESISTENTES.getSigla())) {
                setFecharMetaConversaoDesistentes(obj);
                getFacade().getFecharMeta().inicializarDadosParaTelaMetaDetalhando(getFecharMetaConversaoDesistentes());
                limparTotais(getFecharMetaConversaoDesistentes());
            } else if (obj.getIdentificadorMeta().equals(FasesCRMEnum.AGENDAMENTO.getSigla())) {
                setFecharMetaAgendadosVO(obj);
                getFacade().getFecharMeta().inicializarDadosParaTelaMetaDetalhando(getFecharMetaAgendadosVO());
                inicializarDadosAbaHojeAgendados();
                limparTotais(getFecharMetaAgendadosVO());
            } else if (obj.getIdentificadorMeta().equals(FasesCRMEnum.VINTE_QUATRO_HORAS.getSigla())) {
                setFecharMetaVinteQuatroHorasVO(obj);
                getFacade().getFecharMeta().inicializarDadosParaTelaMetaDetalhando(getFecharMetaVinteQuatroHorasVO());
                inicializarDadosAbaHojeVinteQuatroHoras();
                limparTotais(getFecharMetaVinteQuatroHorasVO());
            } else if (obj.getIdentificadorMeta().equals(FasesCRMEnum.VISITANTES_ANTIGOS.getSigla())) {
                setFecharMetaVisitantesAntigosVO(obj);
                getFacade().getFecharMeta().inicializarDadosParaTelaMetaDetalhando(getFecharMetaVisitantesAntigosVO());
                inicializarDadosAbaHojeVinteQuatroHoras();
                limparTotais(getFecharMetaVinteQuatroHorasVO());
            }else if (obj.getIdentificadorMeta().equals(FasesCRMEnum.VISITA_RECORRENTE.getSigla())) {
                setFecharMetaVisitantesRecorrenteVO(obj);
                getFacade().getFecharMeta().inicializarDadosParaTelaMetaDetalhando(getFecharMetaVisitantesRecorrenteVO());
                inicializarDadosAbaHojeVinteQuatroHoras();
                limparTotais(getFecharMetaVinteQuatroHorasVO());
            }
            else if (obj.getIdentificadorMeta().equals(FasesCRMEnum.INDICACOES.getSigla())) {
                setFecharMetaIndicadoVO(obj);
                getFacade().getFecharMeta().inicializarDadosParaTelaMetaDetalhando(getFecharMetaIndicadoVO());
                inicializarDadosAbaHojeIndicado();
                limparTotais(getFecharMetaIndicadoVO());
            } else if (obj.getIdentificadorMeta().equals("CP")) {
                setFecharMetaPassivoVO(obj);
                getFacade().getFecharMeta().inicializarDadosParaTelaMetaDetalhando(getFecharMetaPassivoVO());
                inicializarDadosAbaHojePassivo();
            } else if (obj.getIdentificadorMeta().equals(FasesCRMEnum.ANIVERSARIANTES.getSigla())) {
                setFecharMetaAniversarianteVO(obj);
                getFacade().getFecharMeta().inicializarDadosParaTelaMetaDetalhando(getFecharMetaAniversarianteVO());
                inicializarDadosAbaHojeAniversariante();
                limparTotais(getFecharMetaAniversarianteVO());
            } else if (obj.getIdentificadorMeta().equals(FasesCRMEnum.RENOVACAO.getSigla())) {
                setFecharMetaRenovacaoVO(obj);
                getFacade().getFecharMeta().inicializarDadosParaTelaMetaDetalhando(getFecharMetaRenovacaoVO());
                inicializarDadosAbaHojeRenovacao();
                limparTotais(getFecharMetaRenovacaoVO());
            } else if (obj.getIdentificadorMeta().equals(FasesCRMEnum.DESISTENTES.getSigla())) {
                setFecharMetaPerdaVO(obj);
                getFacade().getFecharMeta().inicializarDadosParaTelaMetaDetalhando(getFecharMetaPerdaVO());
                inicializarDadosAbaHojePerda();
                limparTotais(getFecharMetaPerdaVO());
            } else if (obj.getIdentificadorMeta().equals(FasesCRMEnum.POS_VENDA.getSigla())) {
                setFecharMetaPosVendaVO(obj);
                getFacade().getFecharMeta().inicializarDadosParaTelaMetaDetalhando(getFecharMetaPosVendaVO());
                inicializarDadosAbaHojePosVenda();
                limparTotais(getFecharMetaPosVendaVO());
            }
            setMensagem("");
            setMensagemID("");
            setMensagemDetalhada("", "");
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    /**
     * Responsável por abrir a listagem de fecharmetadetalhados, filtrando de
     * acordo com o link de origem
     *
     * <AUTHOR> 26/05/2011
     * @throws Exception
     */
    public void filtrarMetaDetalhadaVenda(final ActionEvent event) throws Exception {
        FecharMetaVO obj = (FecharMetaVO) context().getExternalContext().getRequestMap().get("fecharMeta");
        String identificador = event.getComponent().getAttributes().get("identificador").toString();

        setFecharMetaAgendadosVO(obj);
        getFecharMetaAgendadosVO().setAbaSelecionada("HJ");
        if (identificador.equals("COM")) {
            getFecharMetaAgendadosVO().setFecharMetaDetalhadoVOs(getFacade().getFecharMetaDetalhado().consultarComparecimentos(obj.getCodigo(), Uteis.NIVELMONTARDADOS_METAAGENDAMENTODETALHADA));
        } else if (identificador.equals("REA")) {
            getFecharMetaAgendadosVO().setFecharMetaDetalhadoVOs(getFacade().getFecharMetaDetalhado().consultarReagendados(obj.getCodigo(), Uteis.NIVELMONTARDADOS_METAAGENDAMENTODETALHADA));
        } else if (identificador.equals("FEC")) {
            getFecharMetaAgendadosVO().setFecharMetaDetalhadoVOs(getFacade().getFecharMetaDetalhado().consultarFechamentos(obj.getCodigo(), Uteis.NIVELMONTARDADOS_METAAGENDAMENTODETALHADA));
        } else if (identificador.equals("LIG")) {
            getFecharMetaAgendadosVO().setFecharMetaDetalhadoVOs(getConsultarLigacoes(obj));
        }
    }

    private List<FecharMetaDetalhadoVO> getConsultarLigacoes(FecharMetaVO obj) throws Exception {
        List<FecharMetaDetalhadoVO> metaDetalhadoVOs = new ArrayList<FecharMetaDetalhadoVO>();
        if (obj.getCodigo() == null || obj.getCodigo() == 0) {
            metaDetalhadoVOs = getFacade().getFecharMetaDetalhado().consultarLigacoes(obj.getCodigosConcat(), Uteis.NIVELMONTARDADOS_METAAGENDAMENTODETALHADA);
        } else {
            metaDetalhadoVOs = getFacade().getFecharMetaDetalhado().consultarLigacoes(obj.getCodigo(), Uteis.NIVELMONTARDADOS_METAAGENDAMENTODETALHADA);
        }
        return metaDetalhadoVOs;
    }

    /**
     * Metodo responsavel por obter o grupo colaborador selecionado na tela e
     * obter os participantes do grupo.
     *
     * Utilizado em: - include_indicadorDeVendas.jsp
     */
    public void selecionarGrupoColaboradorParticipanteVenda() throws Exception {
        try {
            //Obtendo o grupo colaborador que foi selecionado
            GrupoColaboradorVO obj = (GrupoColaboradorVO) context().getExternalContext().getRequestMap().get("grupoColaborador");

            //Fazendo uma interacao em todos os grupos
            for (GrupoColaboradorVO grupo : aberturaMetaVO.getGrupoColaboradorListaVenda()) {
                //Verificando se o codigo do grupo da interacao e o grupo obtido da tela e igual e se a lista de participantes
                //do grupo obtido da tela é vazia. Se o resultado da verificacao for falso e setado um arraylist vazio
                if (grupo.getCodigo().equals(obj.getCodigo().intValue()) && obj.getGrupoColaboradorParticipanteVOs().isEmpty()) {

                    //Verificando se o usuario possui permissao de ver todas as carteiras
                    consultarGrupoColaboradorParticipante(obj);
                    verificarColaboradoresComoIndisponiveisVenda();
                    getAberturaMetaVO().setNomeGrupoColaboradorVenda(grupo.getDescricao());

                    //Laco que verifica se o participante ja esta selecionado
                    for (GrupoColaboradorParticipanteVO parti : obj.getGrupoColaboradorParticipanteVOs()) {
                        if (parti.getUsuarioParticipante().getCodigo().equals(getAberturaMetaVO().getColaboradorResponsavel().getCodigo())) {
                            parti.getUsuarioParticipante().getColaboradorVO().setColaboradorEscolhidoIndiceConversao(true);
                        }
                    }
                } else {
                    grupo.setGrupoColaboradorParticipanteVOs(new ArrayList());
                }
            }
        } catch (Exception e) {
            setMensagemDetalhada("", e.getMessage());
        }

    }
    // Tem como finalidade atribuir a propiedade 'indisponível' aos colaboradores que estejam
    // indisponiveis no periodo do abertura meta.
    public void verificarColaboradoresComoIndisponiveisVenda(){
            for(GrupoColaboradorVO grupoColaboradorVO : getAberturaMetaVO().getGrupoColaboradorListaVenda()){
                for(GrupoColaboradorParticipanteVO participante : grupoColaboradorVO.getGrupoColaboradorParticipanteVOs()){
                        for(ColaboradorIndisponivelCrmVO indisponivel:  getListaColaboradorIndisponivel()){
                            if(indisponivel.getColaboradorIndisponivelVO().getCodigo()
                                    .equals(participante.getColaboradorParticipante().getCodigo()) && indisponivel.indisponibilidadeColaboradorPorData(getAberturaMetaVO().getDia())){
                                participante.setColaboradorIndisponivel(true);
                            }
                        }
                }
            }
    }
    // Tem como finalidade atribuir a propiedade 'indisponível' aos colaboradores que estejam
    // indisponiveis no periodo do abertura meta.
    public void verificarColaboradoresComoIndisponiveisRentecao(){
        for(GrupoColaboradorVO grupoColaboradorVO : getAberturaMetaVO().getGrupoColaboradorListaRetencao()){
            for(GrupoColaboradorParticipanteVO participante : grupoColaboradorVO.getGrupoColaboradorParticipanteVOs()){
                for(ColaboradorIndisponivelCrmVO indisponivel:  getListaColaboradorIndisponivel()){
                    if(indisponivel.getColaboradorIndisponivelVO().getCodigo()
                            .equals(participante.getColaboradorParticipante().getCodigo()) && indisponivel.indisponibilidadeColaboradorPorData(getAberturaMetaVO().getDia())){
                        participante.setColaboradorIndisponivel(true);
                    }
                }
            }
        }
    }
    
    public void verificarColaboradoresComoIndisponiveisEstudio(){
        for(GrupoColaboradorVO grupoColaboradorVO : getAberturaMetaVO().getGrupoColaboradorListaEstudio()){
            for(GrupoColaboradorParticipanteVO participante : grupoColaboradorVO.getGrupoColaboradorParticipanteVOs()){
                for(ColaboradorIndisponivelCrmVO indisponivel:  getListaColaboradorIndisponivel()){
                    if(indisponivel.getColaboradorIndisponivelVO().getCodigo()
                            .equals(participante.getColaboradorParticipante().getCodigo()) && indisponivel.indisponibilidadeColaboradorPorData(getAberturaMetaVO().getDia())){
                        participante.setColaboradorIndisponivel(true);
                    }
                }
            }
        }
    }
    public Boolean getApresentarCalendarDia() {
        if (getControleConsulta() == null || getControleConsulta().getCampoConsulta() == null){
            return false;
        }
        return getControleConsulta().getCampoConsulta().equals("dia");
    }

    public String getValidarAbrirPopUpPassivo() {
        if (getFecharMetaPassivoVO().getIdentificadorMeta().equals("CP")) {
            return "abrirPopup('./metaPassivoDetalhadaForm.jsp', 'ClientePotencialLista', 1024, 700);";
        }
        return "";
    }

    public String getValidarAbrirPopUpIndicado() {
        if (getFecharMetaIndicadoVO().getIdentificadorMeta().equals("IN")) {
            return "abrirPopup('./metaIndicacaoDetalhadaForm.jsp', 'MetaIndicacaoForm', 1024, 700);";
        }
        return "";
    }

    public String getValidarAbrirPopUpAgendados() {
        if (getFecharMetaAgendadosVO().getIdentificadorMeta().equals("AG")) {
            return "abrirPopup('./metaAgendadosDetalhadaForm.jsp', 'AgendadosForm', 1024, 700);";
        }
        return "";
    }

    public String getValidarAbrirPopUpLigacaoAgendadosAmanha() {
        if (getFecharMetaLigacaoAgendadosVO().getIdentificadorMeta().equals(FasesCRMEnum.LIGACAO_AGENDADOS_AMANHA.getSigla())) {
            return "abrirPopup('./metaLigacaoAgendadosDetalhadaForm.jsp', 'AgendadosAmanhaForm', 1024, 700);";
        }
        return "";
    }

    public String getValidarAbrirPopUpConversaoAgendados() {
        if (getFecharMetaConversaoAgendadosVO().getIdentificadorMeta().equals(FasesCRMEnum.CONVERSAO_AGENDADOS.getSigla())) {
            return "abrirPopup('./metaConversaoAgendadosDetalhadaForm.jsp', 'ConversaoAgendadosForm', 1024, 700);";
        }
        return "";
    }

    public String getValidarAbrirPopUpRenovacao() {
        if (getFecharMetaRenovacaoVO().getIdentificadorMeta().equals("RE")) {
            return "abrirPopup('./metaRenovacaoDetalhadaForm.jsp', 'RenovacaoForm', 1024, 700);";
        }
        return "";
    }

    public String getValidarAbrirPopUpVinteQuatroHoras() {
        if (getFecharMetaVinteQuatroHorasVO().getIdentificadorMeta().equals("HO")) {
            return "abrirPopup('./metaVinteQuatroHorasDetalhadaForm.jsp', 'VinteQuatroHorasForm', 1024, 700);";
        }
        return "";
    }

    public String getValidarAbrirPopUpVendas() {
        if (getFecharMetaVendasVO().getIdentificadorMeta().equals("MQ")) {
            return "abrirPopup('./metaVendaDetalhadaForm.jsp', 'VendaForm', 1024, 700);";
        }
        return "";
    }

    public String getValidarAbrirPopUpFaltas() {
        if (getFecharMetaFaltasVO().getIdentificadorMeta().equals("FA")) {
            return "abrirPopup('./metaFaltasDetalhadaForm.jsp', 'FaltasForm', 1024, 700);";
        }
        return "";
    }

    public String getValidarAbrirPopUpPerda() {
        if (getFecharMetaPerdaVO().getIdentificadorMeta().equals("PE")) {
            return "abrirPopup('./metaPerdaDetalhadaForm.jsp', 'VinteQuatroHorasForm', 1024, 700);";
        }
        return "";
    }

    public String getValidarAbrirPopUpVencidos() {
        if (getFecharMetaVencidosVO().getIdentificadorMeta().equals("VE")) {
            return "abrirPopup('./metaVencidosDetalhadaForm.jsp', 'VencidosForm', 1024, 700);";
        }
        return "";
    }

    public String getValidarAbrirPopUpGrupoRisco() {
        if (getFecharMetaGrupoRiscoVO().getIdentificadorMeta().equals("RI")) {
            return "abrirPopup('./metaGrupoRiscoDetalhadaForm.jsp', 'GrupoRiscoForm', 1024, 700);";
        }
        return "";
    }

    public String getValidarAbrirPopUpFaturamento() {
        if (getFecharMetaFaturamentoVO().getIdentificadorMeta().equals("MF")) {
            return "abrirPopup('./metaFaturamentoDetalhadaForm.jsp', 'FaturamentoForm', 1024, 700);";
        }
        return "";
    }

    public String getValidarAbrirPopUpPosVenda() {
        if (getFecharMetaPosVendaVO().getIdentificadorMeta().equals("PV")) {
            return "abrirPopup('./metaPosVendaDetalhadaForm.jsp', 'PosVendaForm', 1024, 700);";
        }
        return "";
    }

    public String getValidarAbrirPopUpAniversariante() {
        if (getFecharMetaAniversarianteVO().getIdentificadorMeta().equals("AN")) {
            return "abrirPopup('./metaAniversarianteDetalhadaForm.jsp', 'AniversarianteForm', 1024, 700);";
        }
        return "";
    }

    /**
     * Método responsavel por iniciar os dados que enviara o email para as
     * pessoas selecionadas
     */
    public void executarAberturaPopupEmailColetivoPassivo() {
        try {
            validarPermissao("RealizarContato", "7.23 - Realizar Contato", getUsuarioLogado());
            this.setAbrirMalaDireta(true);
            setMalaDiretaVO(new MalaDiretaVO());
            this.malaDiretaVO.setMeioDeEnvioEnum(MeioEnvio.EMAIL);
            getFacade().getFecharMetaDetalhado().inicializarDadosEnviarEmailColetivo(fecharMetaPassivoVO, malaDiretaVO, getUsuarioLogado());
            setMensagemDetalhada("", "");
            setMensagem("");
            setMensagemID("");
        } catch (Exception e) {
            this.setAbrirMalaDireta(false);
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    /**
     * Método responsavel por iniciar os dados que enviara o email para as
     * pessoas selecionadas
     */
    public void executarAberturaPopupSMSColetivoPassivo() {
        try {
            validarPermissao("RealizarContato", "7.23 - Realizar Contato", getUsuarioLogado());
            this.setAbrirMalaDireta(true);
            setMalaDiretaVO(new MalaDiretaVO());
            getMalaDiretaVO().setMeioDeEnvioEnum(MeioEnvio.SMS);
            getFacade().getFecharMetaDetalhado().inicializarDadosEnviarSMSColetivo(fecharMetaPassivoVO, malaDiretaVO, getUsuarioLogado());
            setMensagemDetalhada("", "");
            setMensagem("");
            setMensagemID("");
        } catch (Exception e) {
            this.setAbrirMalaDireta(false);
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    /**
     * Método responsavel por iniciar os dados que enviara o email para as
     * pessoas selecionadas
     */
    public void executarAberturaPopupEmailColetivoIndicado() {
        try {
            validarPermissao("RealizarContato", "7.23 - Realizar Contato", getUsuarioLogado());
            this.setAbrirMalaDireta(true);
            this.malaDiretaVO.setMeioDeEnvioEnum(MeioEnvio.EMAIL);
            getFacade().getFecharMetaDetalhado().inicializarDadosEnviarEmailColetivo(fecharMetaIndicadoVO, malaDiretaVO, getUsuarioLogado());
            setMensagemDetalhada("", "");
            setMensagem("");
            setMensagemID("msg_dados_consultados");
        } catch (Exception e) {
            this.setAbrirMalaDireta(false);
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    /**
     * Método responsavel por iniciar os dados que enviara o email para as
     * pessoas selecionadas
     */
    public void executarAberturaPopupSMSColetivoIndicado() {
        try {
            validarPermissao("RealizarContato", "7.23 - Realizar Contato", getUsuarioLogado());
            this.setAbrirMalaDireta(true);
            this.malaDiretaVO.setMeioDeEnvioEnum(MeioEnvio.SMS);
            getFacade().getFecharMetaDetalhado().inicializarDadosEnviarSMSColetivo(fecharMetaIndicadoVO, malaDiretaVO, getUsuarioLogado());
            setMensagemDetalhada("", "");
            setMensagem("");
            setMensagemID("msg_dados_consultados");
        } catch (Exception e) {
            this.setAbrirMalaDireta(false);
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    /**
     * Método responsavel por iniciar os dados que enviara o email para as
     * pessoas selecionadas
     */
    public void executarAberturaPopupEmailColetivoAgendados() {
        try {
            validarPermissao("RealizarContato", "7.23 - Realizar Contato", getUsuarioLogado());
            this.setAbrirMalaDireta(true);
            this.malaDiretaVO.setMeioDeEnvioEnum(MeioEnvio.EMAIL);
            getFacade().getFecharMetaDetalhado().inicializarDadosEnviarEmailColetivo(fecharMetaAgendadosVO, malaDiretaVO, getUsuarioLogado());
            setMensagemDetalhada("", "");
            setMensagem("");
            setMensagemID("msg_dados_consultados");
        } catch (Exception e) {
            this.setAbrirMalaDireta(false);
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    /**
     * Método responsavel por iniciar os dados que enviara o email para as
     * pessoas selecionadas
     */
    public void executarAberturaPopupSMSColetivoAgendados() {
        try {
            validarPermissao("RealizarContato", "7.23 - Realizar Contato", getUsuarioLogado());
            this.setAbrirMalaDireta(true);
            getMalaDiretaVO().setMeioDeEnvioEnum(MeioEnvio.SMS);
            getFacade().getFecharMetaDetalhado().inicializarDadosEnviarSMSColetivo(fecharMetaAgendadosVO, malaDiretaVO, getUsuarioLogado());
            setMensagemDetalhada("", "");
            setMensagem("");
            setMensagemID("msg_dados_consultados");
        } catch (Exception e) {
            this.setAbrirMalaDireta(false);
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public void executarAberturaPopupEmailColetivoConversaoAgendados() {
        try {
            validarPermissao("RealizarContato", "7.23 - Realizar Contato", getUsuarioLogado());
            this.setAbrirMalaDireta(true);
            this.malaDiretaVO.setMeioDeEnvioEnum(MeioEnvio.EMAIL);
            getFacade().getFecharMetaDetalhado().inicializarDadosEnviarEmailColetivo(fecharMetaConversaoAgendadosVO, malaDiretaVO, getUsuarioLogado());
            setMensagemDetalhada("", "");
            setMensagem("");
            setMensagemID("msg_dados_consultados");
        } catch (Exception e) {
            this.setAbrirMalaDireta(false);
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public void executarAberturaPopupSMSColetivoConversaoAgendados() {
        try {
            validarPermissao("RealizarContato", "7.23 - Realizar Contato", getUsuarioLogado());
            this.setAbrirMalaDireta(true);
            this.malaDiretaVO.setMeioDeEnvioEnum(MeioEnvio.SMS);
            getFacade().getFecharMetaDetalhado().inicializarDadosEnviarSMSColetivo(fecharMetaConversaoAgendadosVO, malaDiretaVO, getUsuarioLogado());
            setMensagemDetalhada("", "");
            setMensagem("");
            setMensagemID("msg_dados_consultados");
        } catch (Exception e) {
            this.setAbrirMalaDireta(false);
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    /**
     * Método responsavel por iniciar os dados que enviara o email para as
     * pessoas selecionadas
     */
    public void executarAberturaPopupEmailColetivoVinteQuatroHoras() {
        try {
            validarPermissao("RealizarContato", "7.23 - Realizar Contato", getUsuarioLogado());
            this.setAbrirMalaDireta(true);
            this.malaDiretaVO.setMeioDeEnvioEnum(MeioEnvio.EMAIL);
            getFacade().getFecharMetaDetalhado().inicializarDadosEnviarEmailColetivo(fecharMetaVinteQuatroHorasVO, malaDiretaVO, getUsuarioLogado());
            setMensagemDetalhada("", "");
            setMensagem("");
            setMensagemID("msg_dados_consultados");
        } catch (Exception e) {
            this.setAbrirMalaDireta(false);
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    /**
     * Método responsavel por iniciar os dados que enviara o email para as
     * pessoas selecionadas
     */
    public void executarAberturaPopupSMSColetivoVinteQuatroHoras() {
        try {
            validarPermissao("RealizarContato", "7.23 - Realizar Contato", getUsuarioLogado());
            this.setAbrirMalaDireta(true);
            this.malaDiretaVO.setMeioDeEnvioEnum(MeioEnvio.SMS);
            getFacade().getFecharMetaDetalhado().inicializarDadosEnviarSMSColetivo(fecharMetaVinteQuatroHorasVO, malaDiretaVO, getUsuarioLogado());
            setMensagemDetalhada("", "");
            setMensagem("");
            setMensagemID("msg_dados_consultados");
        } catch (Exception e) {
            this.setAbrirMalaDireta(false);
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    /**
     * Método responsavel por iniciar os dados que enviara o email para as
     * pessoas selecionadas
     */
    public void executarAberturaPopupEmailColetivoVendas() {
        try {
            validarPermissao("RealizarContato", "7.23 - Realizar Contato", getUsuarioLogado());
            this.setAbrirMalaDireta(true);
            this.malaDiretaVO.setMeioDeEnvioEnum(MeioEnvio.EMAIL);
            getFacade().getFecharMetaDetalhado().inicializarDadosEnviarEmailColetivo(fecharMetaVendasVO, malaDiretaVO, getUsuarioLogado());
            setMensagemDetalhada("", "");
            setMensagem("");
            setMensagemID("msg_dados_consultados");
        } catch (Exception e) {
            this.setAbrirMalaDireta(false);
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    /**
     * Método responsavel por iniciar os dados que enviara SMS para as pessoas
     * selecionadas
     */
    public void executarAberturaPopupSMSColetivoVendas() {
        try {
            validarPermissao("RealizarContato", "7.23 - Realizar Contato", getUsuarioLogado());
            this.setAbrirMalaDireta(true);
            this.malaDiretaVO.setMeioDeEnvioEnum(MeioEnvio.SMS);
            getFacade().getFecharMetaDetalhado().inicializarDadosEnviarSMSColetivo(fecharMetaVendasVO, malaDiretaVO, getUsuarioLogado());
            setMensagemDetalhada("", "");
            setMensagem("");
            setMensagemID("msg_dados_consultados");
        } catch (Exception e) {
            this.setAbrirMalaDireta(false);
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    /**
     * Método responsavel por iniciar os dados que enviara o email para as
     * pessoas selecionadas
     */
    public void executarAberturaPopupEmailColetivoFaturamento() {
        try {
            validarPermissao("RealizarContato", "7.23 - Realizar Contato", getUsuarioLogado());
            this.setAbrirMalaDireta(true);
            this.malaDiretaVO.setMeioDeEnvioEnum(MeioEnvio.EMAIL);
            getFacade().getFecharMetaDetalhado().inicializarDadosEnviarEmailColetivo(fecharMetaFaturamentoVO, malaDiretaVO, getUsuarioLogado());
            setMensagemDetalhada("", "");
            setMensagem("");
            setMensagemID("msg_dados_consultados");
        } catch (Exception e) {
            this.setAbrirMalaDireta(false);
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    /**
     * Método responsavel por iniciar os dados que enviara o email para as
     * pessoas selecionadas
     */
    public void executarAberturaPopupSMSColetivoFaturamento() {
        try {
            validarPermissao("RealizarContato", "7.23 - Realizar Contato", getUsuarioLogado());
            this.setAbrirMalaDireta(true);
            this.malaDiretaVO.setMeioDeEnvioEnum(MeioEnvio.SMS);
            getFacade().getFecharMetaDetalhado().inicializarDadosEnviarSMSColetivo(fecharMetaFaturamentoVO, malaDiretaVO, getUsuarioLogado());
            setMensagemDetalhada("", "");
            setMensagem("");
            setMensagemID("msg_dados_consultados");
        } catch (Exception e) {
            this.setAbrirMalaDireta(false);
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    /**
     * Método responsavel por iniciar os dados que enviara o email para as
     * pessoas selecionadas
     */
    public void executarAberturaPopupEmailColetivoFaltas() {
        try {
            validarPermissao("RealizarContato", "7.23 - Realizar Contato", getUsuarioLogado());
            this.setAbrirMalaDireta(true);
            this.malaDiretaVO.setMeioDeEnvioEnum(MeioEnvio.EMAIL);
            getFacade().getFecharMetaDetalhado().inicializarDadosEnviarEmailColetivo(fecharMetaFaltasVO, malaDiretaVO, getUsuarioLogado());
            setMensagemDetalhada("", "");
            setMensagem("");
            setMensagemID("msg_dados_consultados");
        } catch (Exception e) {
            this.setAbrirMalaDireta(false);
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    /**
     * Método responsavel por iniciar os dados que enviara o email para as
     * pessoas selecionadas
     */
    public void executarAberturaPopupSMSColetivoFaltas() {
        try {
            validarPermissao("RealizarContato", "7.23 - Realizar Contato", getUsuarioLogado());
            this.setAbrirMalaDireta(true);
            this.malaDiretaVO.setMeioDeEnvioEnum(MeioEnvio.SMS);
            getFacade().getFecharMetaDetalhado().inicializarDadosEnviarSMSColetivo(fecharMetaFaltasVO, malaDiretaVO, getUsuarioLogado());
            setMensagemDetalhada("", "");
            setMensagem("");
            setMensagemID("msg_dados_consultados");
        } catch (Exception e) {
            this.setAbrirMalaDireta(false);
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    /**
     * Método responsavel por iniciar os dados que enviara o email para as
     * pessoas selecionadas
     */
    public void executarAberturaPopupEmailColetivoPerda() {
        try {
            validarPermissao("RealizarContato", "7.23 - Realizar Contato", getUsuarioLogado());
            this.setAbrirMalaDireta(true);
            this.malaDiretaVO.setMeioDeEnvioEnum(MeioEnvio.EMAIL);
            getFacade().getFecharMetaDetalhado().inicializarDadosEnviarEmailColetivo(fecharMetaPerdaVO, malaDiretaVO, getUsuarioLogado());
            setMensagemDetalhada("", "");
            setMensagem("");
            setMensagemID("msg_dados_consultados");
        } catch (Exception e) {
            this.setAbrirMalaDireta(false);
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    /**
     * Método responsavel por iniciar os dados que enviara o email para as
     * pessoas selecionadas
     */
    public void executarAberturaPopupSMSColetivoPerda() {
        try {
            validarPermissao("RealizarContato", "7.23 - Realizar Contato", getUsuarioLogado());
            this.setAbrirMalaDireta(true);
            this.malaDiretaVO.setMeioDeEnvioEnum(MeioEnvio.SMS);
            getFacade().getFecharMetaDetalhado().inicializarDadosEnviarSMSColetivo(fecharMetaPerdaVO, malaDiretaVO, getUsuarioLogado());
            setMensagemDetalhada("", "");
            setMensagem("");
            setMensagemID("msg_dados_consultados");
        } catch (Exception e) {
            this.setAbrirMalaDireta(false);
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    /**
     * Método responsavel por iniciar os dados que enviara o email para as
     * pessoas selecionadas
     */
    public void executarAberturaPopupEmailColetivoGrupoRisco() {
        try {
            validarPermissao("RealizarContato", "7.23 - Realizar Contato", getUsuarioLogado());
            this.setAbrirMalaDireta(true);
            this.malaDiretaVO.setMeioDeEnvioEnum(MeioEnvio.EMAIL);
            getFacade().getFecharMetaDetalhado().inicializarDadosEnviarEmailColetivo(fecharMetaGrupoRiscoVO, malaDiretaVO, getUsuarioLogado());
            setMensagemDetalhada("", "");
            setMensagem("");
            setMensagemID("msg_dados_consultados");
        } catch (Exception e) {
            this.setAbrirMalaDireta(false);
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    /**
     * Método responsavel por iniciar os dados que enviara o email para as
     * pessoas selecionadas
     */
    public void executarAberturaPopupSMSColetivoGrupoRisco() {
        try {
            validarPermissao("RealizarContato", "7.23 - Realizar Contato", getUsuarioLogado());
            this.setAbrirMalaDireta(true);
            this.malaDiretaVO.setMeioDeEnvioEnum(MeioEnvio.SMS);
            getFacade().getFecharMetaDetalhado().inicializarDadosEnviarSMSColetivo(fecharMetaGrupoRiscoVO, malaDiretaVO, getUsuarioLogado());
            setMensagemDetalhada("", "");
            setMensagem("");
            setMensagemID("msg_dados_consultados");
        } catch (Exception e) {
            this.setAbrirMalaDireta(false);
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    /**
     * Método responsavel por iniciar os dados que enviara o email para as
     * pessoas selecionadas
     */
    public void executarAberturaPopupEmailColetivoPosVenda() {
        try {
            validarPermissao("RealizarContato", "7.23 - Realizar Contato", getUsuarioLogado());

            this.setAbrirMalaDireta(true);
            this.malaDiretaVO.setMeioDeEnvioEnum(MeioEnvio.EMAIL);
            getFacade().getFecharMetaDetalhado().inicializarDadosEnviarEmailColetivo(fecharMetaPosVendaVO, malaDiretaVO, getUsuarioLogado());
            setMensagemDetalhada("", "");
            setMensagem("");
            setMensagemID("msg_dados_consultados");
        } catch (Exception e) {
            this.setAbrirMalaDireta(false);
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    /**
     * Método responsavel por iniciar os dados que enviara SMS para as pessoas
     * selecionadas
     */
    public void executarAberturaPopupSMSColetivoPosVenda() {
        try {
            validarPermissao("RealizarContato", "7.23 - Realizar Contato", getUsuarioLogado());
            this.setAbrirMalaDireta(true);
            this.malaDiretaVO.setMeioDeEnvioEnum(MeioEnvio.SMS);
            getFacade().getFecharMetaDetalhado().inicializarDadosEnviarSMSColetivo(fecharMetaPosVendaVO, malaDiretaVO, getUsuarioLogado());
            setMensagemDetalhada("", "");
            setMensagem("");
            setMensagemID("msg_dados_consultados");
        } catch (Exception e) {
            this.setAbrirMalaDireta(false);
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    /**
     * Método responsavel por iniciar os dados que enviara o email para as
     * pessoas selecionadas
     */
    public void executarAberturaPopupEmailColetivoRenovacao() {
        try {
            validarPermissao("RealizarContato", "7.23 - Realizar Contato", getUsuarioLogado());
            this.setAbrirMalaDireta(true);
            this.malaDiretaVO.setMeioDeEnvioEnum(MeioEnvio.EMAIL);
            getFacade().getFecharMetaDetalhado().inicializarDadosEnviarEmailColetivo(fecharMetaRenovacaoVO, malaDiretaVO, getUsuarioLogado());
            setMensagemDetalhada("", "");
            setMensagem("");
            setMensagemID("msg_dados_consultados");
        } catch (Exception e) {
            this.setAbrirMalaDireta(false);
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    /**
     * Método responsavel por iniciar os dados que enviara SMS para as pessoas
     * selecionadas
     */
    public void executarAberturaPopupSMSColetivoRenovacao() {
        try {
            validarPermissao("RealizarContato", "7.23 - Realizar Contato", getUsuarioLogado());
            this.setAbrirMalaDireta(true);
            this.malaDiretaVO.setMeioDeEnvioEnum(MeioEnvio.SMS);
            getFacade().getFecharMetaDetalhado().inicializarDadosEnviarSMSColetivo(fecharMetaRenovacaoVO, malaDiretaVO, getUsuarioLogado());
            setMensagemDetalhada("", "");
            setMensagem("");
            setMensagemID("msg_dados_consultados");
        } catch (Exception e) {
            this.setAbrirMalaDireta(false);
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public void incluirTagNome() {
        getMalaDiretaVO().setMensagem(getMalaDiretaVO().getMensagem().concat("TAG_NOME"));
    }

    public void incluirTagPNome() {
        getMalaDiretaVO().setMensagem(getMalaDiretaVO().getMensagem().concat("TAG_PNOME"));
    }

    /**
     * Método responsavel por iniciar os dados que enviara o email para as
     * pessoas selecionadas
     */
    public void executarAberturaPopupEmailColetivoAniversariante() {
        try {
            validarPermissao("RealizarContato", "7.23 - Realizar Contato", getUsuarioLogado());
            this.setAbrirMalaDireta(true);
            this.malaDiretaVO.setMeioDeEnvioEnum(MeioEnvio.EMAIL);
            getFacade().getFecharMetaDetalhado().inicializarDadosEnviarEmailColetivo(fecharMetaAniversarianteVO, malaDiretaVO, getUsuarioLogado());
            setMensagemDetalhada("", "");
            setMensagem("");
            setMensagemID("msg_dados_consultados");
        } catch (Exception e) {
            this.setAbrirMalaDireta(false);
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    /**
     * Método responsavel por iniciar os dados que enviara o email para as
     * pessoas selecionadas
     */
    public void executarAberturaPopupSMSColetivoAniversariante() {
        try {
            validarPermissao("RealizarContato", "7.23 - Realizar Contato", getUsuarioLogado());
            this.setAbrirMalaDireta(true);
            this.malaDiretaVO.setMeioDeEnvioEnum(MeioEnvio.SMS);
            getFacade().getFecharMetaDetalhado().inicializarDadosEnviarSMSColetivo(fecharMetaAniversarianteVO, malaDiretaVO, getUsuarioLogado());
            setMensagemDetalhada("", "");
            setMensagem("");
            setMensagemID("msg_dados_consultados");
        } catch (Exception e) {
            this.setAbrirMalaDireta(false);
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public String getNomeParticipanteSelecionadoVenda() throws Exception {
        getFacade().getAberturaMeta().inicializarDadosNomeParticipanteSelecionado(getAberturaMetaVO(), getAberturaMetaVO().getGrupoColaboradorListaVenda(), true);
        if (getAberturaMetaVO().getNomeParticipanteSelecionado().equals("")) {
            return getAberturaMetaVO().getColaboradorResponsavel().getNome();
        } else {
            return getAberturaMetaVO().getNomeParticipanteSelecionado();
        }
    }

    public String getNomeParticipanteSelecionadoRetencao() throws Exception {
        getFacade().getAberturaMeta().inicializarDadosNomeParticipanteSelecionado(getAberturaMetaVO(), getAberturaMetaVO().getGrupoColaboradorListaRetencao(), false);
        if (getAberturaMetaVO().getNomeParticipanteSelecionadoRetencao().equals("")) {
            return getColaboradorResponsavel().getNome();
        } else {
            return getAberturaMetaVO().getNomeParticipanteSelecionadoRetencao();
        }
    }

    // ----------------------------------------- TELA ABERTURA META
    // -----------------------------------------
    /**
     * Método responsavel por setar o usuario logado dentro da AberturaMeta
     * ColaboradorResponsavel, ResponsavelCadastro e
     * ResponsavelLiberacaoTrocaColaboradorResponsavel
     *
     * @throws Exception
     */
    public void inicializarResponsavelCadastroColaboradorResponsavel() throws Exception {

        if ((getUsuarioLogado() == null) || (getUsuarioLogado().getColaboradorVO().getCodigo().intValue() == 0)) {
            throw new ConsistirException("Este usuário não pode cadastrar uma abertura de meta, pois ele não está cadastrado como colaborador.");
        }
        aberturaMetaVO.setColaboradorResponsavel(getFacade().getUsuario().consultarPorCodigoUsuario(getUsuarioLogado().getCodigo().intValue(), true, Uteis.NIVELMONTARDADOS_DADOSBASICOS));
        aberturaMetaVO.setResponsavelCadastro(getFacade().getUsuario().consultarPorCodigoUsuario(getUsuarioLogado().getCodigo().intValue(), true, Uteis.NIVELMONTARDADOS_DADOSBASICOS));
        getAberturaMetaVO().setResponsavelLiberacaoTrocaColaboradorResponsavel(getAberturaMetaVO().getColaboradorResponsavel());
    }

    /**
     * Método responsavel por consultar o usuario apos mudar o foco no codigo.
     *
     */
    public void consultarSubstitutoColaborador() {
        try {
            aberturaMetaVO.setResponsavelLiberacaoTrocaColaboradorResponsavel(getFacade().getUsuario().consultarPorChavePrimaria(aberturaMetaVO.getResponsavelLiberacaoTrocaColaboradorResponsavel().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
            setMensagem("");
            setMensagemID("");
            setMensagemDetalhada("", "");
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    /*
     * Método responsavel por fechar o Rich:Modla do Colaborador Responsavel
     * apos a escolhar do Colaborador.
     */
    public String getRichModalPanelColaboradorResponsavel() {
        if (getManterAbertoRichModal()) {
            return "Richfaces.showModalPanel('panelColaboradorResponsavel'); Richfaces.hideModalPanel('panelAutorizacao')";
        }
        return "";
    }

//	/*
//	 * Metodo responsavel por fechar o Rich:Modal Panel Abertura apos a gravação
//	 * executado com sucesso.
//	 */
//	public String getAbriOuFecharRichModalAberturaMeta() {
//		if (getFecharRichModalAberturaMeta()) {
//			return "Richfaces.hideModalPanel('panelAberturaMeta')";
//		}
//		return "";
//	}
    /**
     * Método responsável por processar a consulta na entidade
     * <code>ModeloMensagem</code> por meio dos parametros informados no
     * richmodal. Esta rotina é utilizada fundamentalmente por requisições Ajax,
     * que realizam busca pelos parâmentros informados no richModal montando
     * automaticamente o resultado da consulta para apresentação.
     */
    public void consultarModeloMensagem() {
        try {
            List objs = new ArrayList();
            if (getCampoConsultarModeloMensagem().equals("codigo")) {
                if (getValorConsultarModeloMensagem().equals("")) {
                    setValorConsultarModeloMensagem("0");
                }
                Integer valorInt = Integer.parseInt(getValorConsultarModeloMensagem());
                objs = getFacade().getModeloMensagem().consultarPorCodigo(valorInt, getMalaDiretaVO().getMeioDeEnvioEnum(), false, Uteis.NIVELMONTARDADOS_TODOS);
            }
            if (getCampoConsultarModeloMensagem().equals("titulo")) {
                objs = getFacade().getModeloMensagem().consultarPorTitulo(getValorConsultarModeloMensagem(), getMalaDiretaVO().getMeioDeEnvioEnum(), false, Uteis.NIVELMONTARDADOS_TODOS);
            }
            setListaConsultarModeloMensagem(objs);
            setMensagemID("msg_dados_consultados");
        } catch (Exception e) {
            setListaConsultarModeloMensagem(new ArrayList());
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    /**
     * Método responsavel por selecionar o modelo da mensagem na hora da
     * consulta no richModal. Após selecionar seta os valores para
     * mensagemNotificação.
     *
     * @throws Exception
     */
    public void selecionarModeloMensagem() throws Exception {
        ModeloMensagemVO obj = (ModeloMensagemVO) context().getExternalContext().getRequestMap().get("modeloMensagem");

        obj.verificarSeExisteImagemModelo(false, getKey());

        if (getMensagemDetalhada().equals("")) {
            this.getMalaDiretaVO().setModeloMensagem(obj);
            this.getMalaDiretaVO().setMensagem(this.getMalaDiretaVO().getModeloMensagem().getMensagem());
        }
        Uteis.liberarListaMemoria(this.getListaConsultarModeloMensagem());
        this.setValorConsultarModeloMensagem(null);
        this.setCampoConsultarModeloMensagem(null);
    }

    /**
     * Método responsavel por limpar o campo modeloMensagem
     */
    public void limparCampoModeloMensagem() {
        this.getMalaDiretaVO().setModeloMensagem(new ModeloMensagemVO());
        this.getMalaDiretaVO().setMensagem("");
    }

    /*
     * Método responsavel por fazer a consulta do colaborador Responsavel caso
     * outro colaborador for trabalhar no seu lugar. Ex: Se algum colaborador
     * estiver de férias e outro colaborador for trabalhar para ele.
     */
    public void consultarColaboradorResponsavelParaSubstituicao() {
        try {
            super.consultar();
            String sql = "";
            List objs = new ArrayList();
            if (getCampoConsultaColaboradorSubstituido().equals("codigo")) {
                if (getValorConsultaColaboradorSubstituido().equals("")) {
                    setValorConsultaColaboradorSubstituido("0");
                }
                int valorInt = Integer.parseInt(getValorConsultaColaboradorSubstituido());
                objs = getFacade().getUsuario().consultarPorCodigoUsuarioSomenteColaborador(new Integer(valorInt), true, Uteis.NIVELMONTARDADOS_TODOS);
            }
            if (getCampoConsultaColaboradorSubstituido().equals("nome")) {
                objs = getFacade().getUsuario().consultarPorNomeUsuarioSomenteColaborador(getValorConsultaColaboradorSubstituido(), true, Uteis.NIVELMONTARDADOS_TODOS);
            }
            setListaConsultaColaboradorResponsavel(objs);
            setManterAbertoRichModal(false);
            setMensagemID("msg_dados_consultados");
            setSucesso(true);
            setErro(false);
        } catch (Exception e) {
            setListaConsultaColaboradorResponsavel(new ArrayList());
            setSucesso(false);
            setErro(true);
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    /**
     * Método responsavel por selecionar o colaborador que esta sendo
     * substituido.
     *
     * @throws Exception
     */
    public void selecionarColaboradorResponsavelSubstituido() {
        try {
            UsuarioVO obj = (UsuarioVO) context().getExternalContext().getRequestMap().get("usuario");
            getFacade().getUsuario().consultarPorChavePrimaria(obj.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSENTIDADESUBORDINADAS);
            getAberturaMetaVO().setColaboradorResponsavel(obj);
            getFacade().getAberturaMeta().consultarPerfilAcessoColaborador(getAberturaMetaVO(), getEmpresaLogado());
            setMensagemID("msg_dados_consultados");
            setSucesso(true);
            setErro(false);
        } catch (Exception e) {
            setSucesso(false);
            setErro(true);
            setMensagemDetalhada("msg_erro", e.getMessage());
        }

    }

    /**
     * Rotina responsável por atribui um javascript com o método de mascara para
     * campos do tipo Data, CPF, CNPJ, etc.
     */
    public String getMascaraConsulta() {
        return "";
    }

    /**
     * Rotina responsável por preencher a combo de consulta da telas.
     */
    public List getTipoConsultaCombo() {
        List itens = new ArrayList();
        itens.add(new SelectItem("codigo", "Código"));
        return itens;
    }

    /**
     * Rotina responsável por preencher a combo de consulta da telas.
     */
    public List getTipoConsultaComboFechamentoDia() {
        List itens = new ArrayList();
        itens.add(new SelectItem("colaboradorResponsavel", "Colaborador Responsável"));
        itens.add(new SelectItem("dia", "Dia"));
        return itens;
    }

    public List getTipoConsultaComboColaboradorResponsavelSubstituido() {
        List itens = new ArrayList();
        itens.add(new SelectItem("nome", "Nome"));
        itens.add(new SelectItem("codigo", "Código"));
        return itens;
    }

    public List getTipoConsultarComboModeloMensagem() {
        List itens = new ArrayList();
        itens.add(new SelectItem("codigo", "Codigo"));
        itens.add(new SelectItem("titulo", "Título"));
        return itens;
    }

    /**
     * Rotina responsável por organizar a paginação entre as páginas resultantes
     * de uma consulta.
     */
    public String inicializarConsultar() {
        setListaConsulta(new ArrayList());
        setListaConsultaFechamentoDia(new ArrayList());
        setMensagemID("msg_entre_prmconsulta");
        return "consultar";
    }

    /**
     * Operação que libera todos os recursos (atributos, listas, objetos) do
     * backing bean. Garantindo uma melhor atuação do Garbage Coletor do Java. A
     * mesma é automaticamente quando realiza o logout.
     */
    protected void limparRecursosMemoria() {
        super.limparRecursosMemoria();
        aberturaMetaVO = null;

    }

    // ********************************* REGRAS PARA HOJE, MES E DIA PARA AS
    // TELAS DE METADETALHADAFORM ****************************
    // ********************************* REGRAS HOJE
    // ****************************
    /**
     * Método responsavel por apresentar o filtro de pesquisa na tela
     * metaDetalhadaForm e setar na aba selecionada os caracteres
     * correspondentes.
     */
    public void inicializarDadosAbaHojePassivo() {
        try {
            setApresentarFiltroPesquisaHistorico(false);
            getFecharMetaPassivoVO().setAbaSelecionada("HJ");
            getFecharMetaPassivoVO().setFecharMetaDetalhadoVOs(getFacade().getFecharMetaDetalhado().consultarFecharMetaDetalhado(aberturaMetaVO, "CP", true, getEmpresaLogado().getCodigo()));
            setMensagem("");
            setMensagemID("msg_dados_consultados");
            setMensagemDetalhada("", "");
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }

    }

    /**
     * Método responsavel por apresentar o filtro de pesquisa na tela
     * metaDetalhadaForm e setar na aba selecionada os caracteres
     * correspondentes.
     */
    public void inicializarDadosAbaHojeAniversariante() {
        try {
            setApresentarFiltroPesquisaHistorico(false);
            getFecharMetaAniversarianteVO().setAbaSelecionada("HJ");
            getFecharMetaAniversarianteVO().setFecharMetaDetalhadoVOs(getFacade().getFecharMetaDetalhado().consultarFecharMetaDetalhado(aberturaMetaVO, "AN", false, getEmpresaLogado().getCodigo()));
            setMensagem("");
            setMensagemDetalhada("", "");
            setMensagemID("msg_dados_consultados");
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }

    }

    /**
     * Método responsavel por apresentar o filtro de pesquisa na tela
     * metaDetalhadaForm e setar na aba selecionada os caracteres
     * correspondentes.
     */
    public void inicializarDadosAbaHojeGrupoRisco() {
        try {
            setApresentarFiltroPesquisaHistorico(false);
            getFecharMetaGrupoRiscoVO().setAbaSelecionada("HJ");
            getFecharMetaGrupoRiscoVO().setFecharMetaDetalhadoVOs(getFacade().getFecharMetaDetalhado().consultarFecharMetaDetalhado(aberturaMetaVO, "RI", false, getEmpresaLogado().getCodigo()));
            setMensagem("");
            setMensagemDetalhada("", "");
            setMensagemID("msg_dados_consultados");
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }

    }

    /**
     * Método responsavel por apresentar o filtro de pesquisa na tela
     * metaDetalhadaForm e setar na aba selecionada os caracteres
     * correspondentes.
     */
    public void inicializarDadosAbaHojeFalta() {
        try {
            setApresentarFiltroPesquisaHistorico(false);
            getFecharMetaFaltasVO().setAbaSelecionada("HJ");
            getFecharMetaFaltasVO().setFecharMetaDetalhadoVOs(getFacade().getFecharMetaDetalhado().consultarFecharMetaDetalhado(aberturaMetaVO, "FA", false, getEmpresaLogado().getCodigo()));
            setMensagem("");
            setMensagemDetalhada("", "");
            setMensagemID("msg_dados_consultados");
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }

    }

    public void inicializarDadosAbaHojeVencidos() {
        try {
            setApresentarFiltroPesquisaHistorico(false);
            getFecharMetaVencidosVO().setAbaSelecionada("HJ");
            getFecharMetaVencidosVO().setFecharMetaDetalhadoVOs(getFacade().getFecharMetaDetalhado().consultarFecharMetaDetalhado(aberturaMetaVO, "VE", false, getEmpresaLogado().getCodigo()));
            getFacade().getFecharMetaDetalhado().setarAtributoEspecificoFecharMetaDetalhados(getFecharMetaVencidosVO().getFecharMetaDetalhadoVOs(), "VE");
            setMensagem("");
            setMensagemID("msg_dados_consultados");
            setMensagemDetalhada("", "");
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }

    }

    /**
     * Método responsavel por apresentar o filtro de pesquisa na tela
     * metaDetalhadaForm e setar na aba selecionada os caracteres
     * correspondentes.
     */
    public void inicializarDadosAbaHojePerda() {
        try {
            setApresentarFiltroPesquisaHistorico(false);
            getFecharMetaPerdaVO().setAbaSelecionada("HJ");
            getFecharMetaPerdaVO().setFecharMetaDetalhadoVOs(getFacade().getFecharMetaDetalhado().consultarFecharMetaDetalhado(aberturaMetaVO, "PE", true, getEmpresaLogado().getCodigo()));
            getFacade().getFecharMetaDetalhado().setarAtributoEspecificoFecharMetaDetalhados(getFecharMetaPerdaVO().getFecharMetaDetalhadoVOs(), "PE");
            setMensagem("");
            setMensagemID("msg_dados_consultados");
            setMensagemDetalhada("", "");
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }

    }

    /**
     * Método responsavel por apresentar o filtro de pesquisa na tela
     * metaDetalhadaForm e setar na aba selecionada os caracteres
     * correspondentes.
     */
    public void inicializarDadosAbaHojeRenovacao() {
        try {
            setApresentarFiltroPesquisaHistorico(false);
            getFecharMetaRenovacaoVO().setAbaSelecionada("HJ");
            getFecharMetaRenovacaoVO().setFecharMetaDetalhadoVOs(getFacade().getFecharMetaDetalhado().consultarFecharMetaDetalhado(aberturaMetaVO, "RE", true, getEmpresaLogado().getCodigo()));
            getFacade().getFecharMetaDetalhado().setarAtributoEspecificoFecharMetaDetalhados(getFecharMetaRenovacaoVO().getFecharMetaDetalhadoVOs(), "RE");
            setMensagem("");
            setMensagemDetalhada("", "");
            setMensagemID("msg_dados_consultados");
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }

    }

    /**
     * Método responsavel por apresentar o filtro de pesquisa na tela
     * metaDetalhadaForm e setar na aba selecionada os caracteres
     * correspondentes.
     */
    public void inicializarDadosAbaHojePosVenda() {
        try {
            setApresentarFiltroPesquisaHistorico(false);
            getFecharMetaPosVendaVO().setAbaSelecionada("HJ");
            getFecharMetaPosVendaVO().setFecharMetaDetalhadoVOs(
                    getFacade().getFecharMetaDetalhado().consultarFecharMetaDetalhado(
                    aberturaMetaVO,
                    FasesCRMEnum.POS_VENDA.getSigla(),
                    false,
                    getEmpresaLogado().getCodigo()));
            setMensagem("");
            setMensagemDetalhada("", "");
            setMensagemID("msg_dados_consultados");
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }

    }

    /**
     * Método responsavel por apresentar o filtro de pesquisa na tela
     * metaDetalhadaForm e setar na aba selecionada os caracteres
     * correspondentes.
     */
    public void inicializarDadosAbaHojeFaturamento() {
        try {
            setApresentarFiltroPesquisaHistorico(false);
            getFecharMetaFaturamentoVO().setAbaSelecionada("HJ");
            getFecharMetaFaturamentoVO().setFecharMetaDetalhadoVOs(getFacade().getFecharMetaDetalhado().consultarFecharMetaDetalhado(aberturaMetaVO, "MF", true, getEmpresaLogado().getCodigo()));
            setMensagem("");
            setMensagemDetalhada("", "");
            setMensagemID("msg_dados_consultados");
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }

    }

    /**
     * Método responsavel por apresentar o filtro de pesquisa na tela
     * metaDetalhadaForm e setar na aba selecionada os caracteres
     * correspondentes.
     */
    public void inicializarDadosAbaHojeVendas() {
        try {
            setApresentarFiltroPesquisaHistorico(false);
            getFecharMetaVendasVO().setAbaSelecionada("HJ");
            getFecharMetaVendasVO().setFecharMetaDetalhadoVOs(getFacade().getFecharMetaDetalhado().consultarFecharMetaDetalhado(aberturaMetaVO, "MQ", true, getEmpresaLogado().getCodigo()));
            setMensagem("");
            setMensagemDetalhada("", "");
            setMensagemID("msg_dados_consultados");
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }

    }

    /**
     * Método responsavel por apresentar o filtro de pesquisa na tela
     * metaDetalhadaForm e setar na aba selecionada os caracteres
     * correspondentes.
     */
    public void inicializarDadosAbaHojeIndicado() {
        try {
            setApresentarFiltroPesquisaHistorico(false);
            getFecharMetaIndicadoVO().setAbaSelecionada("HJ");
            getFecharMetaIndicadoVO().setFecharMetaDetalhadoVOs(getFacade().getFecharMetaDetalhado().consultarFecharMetaDetalhado(aberturaMetaVO, "IN", true, getEmpresaLogado().getCodigo()));
            setMensagem("");
            setMensagemDetalhada("", "");
            setMensagemID("msg_dados_consultados");
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    /**
     * Método responsavel por apresentar o filtro de pesquisa na tela
     * metaDetalhadaForm e setar na aba selecionada os caracteres
     * correspondentes.
     */
    public void inicializarDadosAbaHojeAgendados() {
        try {
            setApresentarFiltroPesquisaHistorico(false);
            getFecharMetaAgendadosVO().setAbaSelecionada("HJ");
            getFecharMetaAgendadosVO().setFecharMetaDetalhadoVOs(getFacade().getFecharMetaDetalhado().consultarFecharMetaDetalhado(aberturaMetaVO, "AG", true, getEmpresaLogado().getCodigo()));
            setMensagem("");
            setMensagemDetalhada("", "");
            setMensagemID("msg_dados_consultados");
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public void inicializarDadosAbaHojeConversaoAgendados() {
        try {
            setApresentarFiltroPesquisaHistorico(false);
            getFecharMetaConversaoAgendadosVO().setAbaSelecionada("HJ");
            getFecharMetaConversaoAgendadosVO().setFecharMetaDetalhadoVOs(getFacade().getFecharMetaDetalhado().consultarFecharMetaDetalhado(aberturaMetaVO, FasesCRMEnum.CONVERSAO_AGENDADOS.getSigla(), true, getEmpresaLogado().getCodigo()));
            setMensagem("");
            setMensagemDetalhada("", "");
            setMensagemID("msg_dados_consultados");
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public void inicializarDadosAbaHojeLigacaoAgendados() {
        try {
            setApresentarFiltroPesquisaHistorico(false);
            getFecharMetaLigacaoAgendadosVO().setAbaSelecionada("HJ");
            getFecharMetaLigacaoAgendadosVO().setFecharMetaDetalhadoVOs(
                    getFacade().getFecharMetaDetalhado().consultarFecharMetaDetalhado(
                    aberturaMetaVO, FasesCRMEnum.LIGACAO_AGENDADOS_AMANHA.getSigla(), true, getEmpresaLogado().getCodigo()));
            setMensagem("");
            setMensagemDetalhada("", "");
            setMensagemID("msg_dados_consultados");
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    /**
     * Método responsavel por apresentar o filtro de pesquisa na tela
     * metaDetalhadaForm e setar na aba selecionada os caracteres
     * correspondentes.
     */
    public void inicializarDadosAbaHojeVinteQuatroHoras() {
        try {
            setApresentarFiltroPesquisaHistorico(false);
            getFecharMetaVinteQuatroHorasVO().setAbaSelecionada("HJ");
            getFecharMetaVinteQuatroHorasVO().setFecharMetaDetalhadoVOs(getFacade().getFecharMetaDetalhado().consultarFecharMetaDetalhado(aberturaMetaVO, "HO", true, getEmpresaLogado().getCodigo()));
            setMensagem("");
            setMensagemDetalhada("", "");
            setMensagemID("msg_dados_consultados");
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }

    }

    // ********************************* REGRAS HISTORICO
    // ****************************
    /**
     * Método responsavel por consultar a meta detalhada do passivo quando o
     * usuario clicar em pesquisar da aba Historico da tela
     * metaIndicacaoDetalhadaForm.
     */
    public void consultarFecharMetaDetalhadoVendaPassivoPorHistorico() {
        try {
            validarDados();
            getFecharMetaPassivoVO().setListaHistoricoContatoHistorico(getFacade().getFecharMetaDetalhado().consultarFecharMetaDetalhadosHistorico("CP", getValorConsultaNomePassivo(), getDataInicio(), getDataTermino(), getAberturaMetaVO(), Uteis.NIVELMONTARDADOS_METAPASSIVODETALHADA, true));
            setMensagem("");
            setMensagemID("msg_dados_consultados");
            setMensagemDetalhada("", "");
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    /**
     * Valida dados de pesquisa de metas de passivo
     */
    public void validarDados() throws Exception {
        if (Calendario.menor(getDataTermino(), getDataInicio())) {
            throw new Exception("A DATA DE TÉRMINO do PERÍODO DE PESQUISA não deve ser menor que a DATA DE INÍCIO");
        }
    }

    /**
     * Método responsavel por consultar a meta detalhada do passivo quando o
     * usuario clicar em pesquisar da aba Historico da tela
     * metaIndicacaoDetalhadaForm.
     */
    public void consultarFecharMetaDetalhadoVendaAgendadosPorHistorico() {
        try {
            validarDados();
            getFecharMetaAgendadosVO().setListaHistoricoContatoHistorico(getFacade().getFecharMetaDetalhado().consultarFecharMetaDetalhadosHistorico("AG", getValorConsultaNomeAgendados(), getDataInicio(), getDataTermino(), getAberturaMetaVO(), Uteis.NIVELMONTARDADOS_METAAGENDAMENTODETALHADA, true));
            setMensagem("");
            setMensagemID("msg_dados_consultados");
            setMensagemDetalhada("", "");
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public void consultarFecharMetaDetalhadoVendaConversaoAgendadosPorHistorico() {
        try {
            validarDados();
            getFecharMetaConversaoAgendadosVO().setListaHistoricoContatoHistorico(getFacade().getFecharMetaDetalhado().consultarFecharMetaDetalhadosHistorico(FasesCRMEnum.CONVERSAO_AGENDADOS.getSigla(), getValorConsultaNomeConversaoAgendados(), getDataInicio(), getDataTermino(), getAberturaMetaVO(), Uteis.NIVELMONTARDADOS_METAAGENDAMENTODETALHADA, true));
            setMensagem("");
            setMensagemID("msg_dados_consultados");
            setMensagemDetalhada("", "");
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public void consultarFecharMetaDetalhadoVendaLigacaoAgendadosPorHistorico() {
        try {
            validarDados();
            getFecharMetaLigacaoAgendadosVO().setListaHistoricoContatoHistorico(getFacade().getFecharMetaDetalhado().consultarFecharMetaDetalhadosHistorico(FasesCRMEnum.LIGACAO_AGENDADOS_AMANHA.getSigla(), getValorConsultaNomeLigacaoAgendados(), getDataInicio(), getDataTermino(), getAberturaMetaVO(), Uteis.NIVELMONTARDADOS_METAAGENDAMENTODETALHADA, true));
            setMensagem("");
            setMensagemID("msg_dados_consultados");
            setMensagemDetalhada("", "");
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    /**
     * Método responsavel por consultar a meta detalhada do VinteQuatroHoras
     * quando o usuario clicar em pesquisar da aba Historico da tela
     * metaIndicacaoDetalhadaForm.
     */
    public void consultarFecharMetaDetalhadoVendaVinteQuatroHorasPorHistorico() {
        try {
            validarDados();
            getFecharMetaVinteQuatroHorasVO().setListaHistoricoContatoHistorico(getFacade().getFecharMetaDetalhado().consultarFecharMetaDetalhadosHistorico("HO", getValorConsultaNomeVinteQuatroHoras(), getDataInicio(), getDataTermino(), getAberturaMetaVO(), Uteis.NIVELMONTARDADOS_HISTORICOCLIENTE, true));
            setMensagem("");
            setMensagemID("msg_dados_consultados");
            setMensagemDetalhada("", "");
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    /**
     * Método responsavel por consultar a meta detalhada do Vendas quando o
     * usuario clicar em pesquisar da aba Historico da tela
     * metaIndicacaoDetalhadaForm.
     */
    public void consultarFecharMetaDetalhadoVendaVendasPorHistorico() {
        try {
            validarDados();
            getFecharMetaVendasVO().setListaHistoricoContatoHistorico(getFacade().getFecharMetaDetalhado().consultarFecharMetaDetalhadosHistorico("MQ", getValorConsultaNomeVendas(), getDataInicio(), getDataTermino(), getAberturaMetaVO(), Uteis.NIVELMONTARDADOS_HISTORICOCLIENTE, true));
            setMensagem("");
            setMensagemID("msg_dados_consultados");
            setMensagemDetalhada("", "");
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    /**
     * Método responsavel por consultar a meta detalhada do Faturamento quando o
     * usuario clicar em pesquisar da aba Historico.
     */
    public void consultarFecharMetaDetalhadoVendaFaturamentoPorHistorico() {
        try {
            validarDados();
            getFecharMetaFaturamentoVO().setListaHistoricoContatoHistorico(getFacade().getFecharMetaDetalhado().consultarFecharMetaDetalhadosHistorico("MF", getValorConsultaNomeFaturamento(), getDataInicio(), getDataTermino(), getAberturaMetaVO(), Uteis.NIVELMONTARDADOS_INDICERENOVACAO, true));
            setMensagem("");
            setMensagemID("msg_dados_consultados");
            setMensagemDetalhada("", "");
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    /**
     * Método responsavel por consultar a meta detalhada do VinteQuatroHoras
     * quando o usuario clicar em pesquisar da aba Historico da tela
     * metaIndicacaoDetalhadaForm.
     */
    public void consultarFecharMetaDetalhadoVendaFaltasPorHistorico() {
        try {
            validarDados();
            getFecharMetaFaltasVO().setListaHistoricoContatoHistorico(getFacade().getFecharMetaDetalhado().consultarFecharMetaDetalhadosHistorico("FA", getValorConsultaNomeFaltas(), getDataInicio(), getDataTermino(), getAberturaMetaVO(), Uteis.NIVELMONTARDADOS_HISTORICOCLIENTE, false));
            getFacade().getFecharMetaDetalhado().setarAtributoEspecificoFecharMetaDetalhados(getFecharMetaFaltasVO().getListaHistoricoContatoHistorico(), "FA");
            setMensagem("");
            setMensagemID("msg_dados_consultados");
            setMensagemDetalhada("", "");
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    /**
     * Método responsavel por consultar a meta detalhada do VinteQuatroHoras
     * quando o usuario clicar em pesquisar da aba Historico da tela
     * metaIndicacaoDetalhadaForm.
     */
    public void consultarFecharMetaDetalhadoVendaPerdaPorHistorico() {
        try {
            validarDados();
            getFecharMetaPerdaVO().setListaHistoricoContatoHistorico(getFacade().getFecharMetaDetalhado().consultarFecharMetaDetalhadosHistorico("PE", getValorConsultaNomePerda(), getDataInicio(), getDataTermino(), getAberturaMetaVO(), Uteis.NIVELMONTARDADOS_HISTORICOCLIENTE, false));
            getFacade().getFecharMetaDetalhado().setarAtributoEspecificoFecharMetaDetalhados(getFecharMetaPerdaVO().getListaHistoricoContatoHistorico(), "PE");
            setMensagem("");
            setMensagemID("msg_dados_consultados");
            setMensagemDetalhada("", "");
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    /**
     * Método responsavel por consultar a meta detalhada do GrupoRisco quando o
     * usuario clicar em pesquisar da aba Historico.
     */
    public void consultarFecharMetaDetalhadoVendaGrupoRiscoPorHistorico() {
        try {
            validarDados();
            getFecharMetaGrupoRiscoVO().setListaHistoricoContatoHistorico(getFacade().getFecharMetaDetalhado().consultarFecharMetaDetalhadosHistorico("RI", getValorConsultaNomeGrupoRisco(), getDataInicio(), getDataTermino(), getAberturaMetaVO(), Uteis.NIVELMONTARDADOS_HISTORICOCLIENTE, false));
            setMensagem("");
            setMensagemID("msg_dados_consultados");
            setMensagemDetalhada("", "");
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    /**
     * Método responsavel por consultar a meta detalhada do PosVenda quando o
     * usuario clicar em pesquisar da aba Historico da tela
     * metaIndicacaoDetalhadaForm.
     */
    public void consultarFecharMetaDetalhadoVendaPosVendaPorHistorico() {
        try {
            validarDados();
            getFecharMetaPosVendaVO().setListaHistoricoContatoHistorico(getFacade().getFecharMetaDetalhado().consultarFecharMetaDetalhadosHistorico("PV", getValorConsultaNomePosVenda(), getDataInicio(), getDataTermino(), getAberturaMetaVO(), Uteis.NIVELMONTARDADOS_INDICERENOVACAO, true));
            setMensagem("");
            setMensagemID("msg_dados_consultados");
            setMensagemDetalhada("", "");
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    /**
     * Método responsavel por consultar a meta detalhada do passivo quando o
     * usuario clicar em pesquisar da aba Historico da tela
     * metaIndicacaoDetalhadaForm.
     */
    public void consultarFecharMetaDetalhadoVendaRenovacaoPorHistorico() {
        try {
            validarDados();
            getFecharMetaRenovacaoVO().setListaHistoricoContatoHistorico(getFacade().getFecharMetaDetalhado().consultarFecharMetaDetalhadosHistorico("RE", getValorConsultaNomeRenovacao(), getDataInicio(), getDataTermino(), getAberturaMetaVO(), Uteis.NIVELMONTARDADOS_HISTORICOCLIENTE, true));
            getFacade().getFecharMetaDetalhado().setarAtributoEspecificoFecharMetaDetalhados(getFecharMetaRenovacaoVO().getListaHistoricoContatoHistorico(), "RE");
            setMensagem("");
            setMensagemID("msg_dados_consultados");
            setMensagemDetalhada("", "");
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    /**
     * Método responsavel por consultar a meta detalhada do passivo quando o
     * usuario clicar em pesquisar da aba Historico da tela
     * metaIndicacaoDetalhadaForm.
     */
    public void consultarFecharMetaDetalhadoRetencaoAniversariantePorHistorico() {
        try {
            validarDados();
            getFecharMetaAniversarianteVO().setListaHistoricoContatoHistorico(getFacade().getFecharMetaDetalhado().consultarFecharMetaDetalhadosHistorico("AN", getValorConsultaNomeAniversariante(), getDataInicio(), getDataTermino(), getAberturaMetaVO(), Uteis.NIVELMONTARDADOS_HISTORICOCLIENTE, false));
            setMensagem("");
            setMensagemID("msg_dados_consultados");
            setMensagemDetalhada("", "");
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    /**
     * Método responsavel por consultar a meta detalhada do passivo quando o
     * usuario clicar em pesquisar da aba Historico da tela
     * metaIndicacaoDetalhadaForm.
     */
    public void consultarFecharMetaDetalhadoVendaIndicadoPorHistorico() {
        try {
            validarDados();
            getFecharMetaIndicadoVO().setListaHistoricoContatoHistorico(getFacade().getFecharMetaDetalhado().consultarFecharMetaDetalhadosHistorico("IN", getValorConsultaNomeIndicado(), getDataInicio(), getDataTermino(), getAberturaMetaVO(), Uteis.NIVELMONTARDADOS_METAINDICACAODETALHADA, true));
            setMensagem("");
            setMensagemID("msg_dados_consultados");
            setMensagemDetalhada("", "");
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    // INDICADOS
    /**
     * Método responsavel por apresentar o filtro de pesquisa na tela
     * metaDetalhadaForm e setar na aba selecionada os caracteres
     * correspondentes.
     */
    public void inicializarDadosAbaHistoricoIndicado() {
        try {
            setApresentarFiltroPesquisaHistorico(true);
            getFecharMetaIndicadoVO().setAbaSelecionada("HI");
            getFecharMetaIndicadoVO().setListaHistoricoContatoHistorico(new ArrayList());
            setMensagem("");
            setMensagemID("msg_dados_consultados");
            setMensagemDetalhada("", "");
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }

    }

    // PASSIVO
    /**
     * Método responsavel por apresentar o filtro de pesquisa na tela
     * metaDetalhadaForm e setar na aba selecionada os caracteres
     * correspondentes.
     */
    public void inicializarDadosAbaHistoricoPassivo() {
        try {
            setApresentarFiltroPesquisaHistorico(true);
            getFecharMetaPassivoVO().setAbaSelecionada("HI");
            getFecharMetaPassivoVO().setListaHistoricoContatoHistorico(new ArrayList());
            setMensagem("");
            setMensagemID("msg_dados_consultados");
            setMensagemDetalhada("", "");
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    /**
     * Método responsavel por apresentar o filtro de pesquisa na tela
     * metaDetalhadaForm e setar na aba selecionada os caracteres
     * correspondentes.
     */
    public void inicializarDadosAbaHistoricoAgendados() {
        try {
            setApresentarFiltroPesquisaHistorico(true);
            getFecharMetaAgendadosVO().setAbaSelecionada("HI");
            getFecharMetaAgendadosVO().setListaHistoricoContatoHistorico(new ArrayList());
            setMensagem("");
            setMensagemID("msg_dados_consultados");
            setMensagemDetalhada("", "");
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    /**
     * Método responsavel por apresentar o filtro de pesquisa na tela
     * metaDetalhadaForm e setar na aba selecionada os caracteres
     * correspondentes.
     */
    public void inicializarDadosAbaHistoricoVinteQuatroHoras() {
        try {
            setApresentarFiltroPesquisaHistorico(true);
            getFecharMetaVinteQuatroHorasVO().setAbaSelecionada("HI");
            getFecharMetaVinteQuatroHorasVO().setListaHistoricoContatoHistorico(new ArrayList());
            setMensagem("");
            setMensagemID("msg_dados_consultados");
            setMensagemDetalhada("", "");
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    /**
     * Método responsavel por apresentar o filtro de pesquisa na tela
     * metaDetalhadaForm e setar na aba selecionada os caracteres
     * correspondentes.
     */
    public void inicializarDadosAbaHistoricoVendas() {
        try {
            setApresentarFiltroPesquisaHistorico(true);
            getFecharMetaVendasVO().setAbaSelecionada("HI");
            getFecharMetaVendasVO().setListaHistoricoContatoHistorico(new ArrayList());
            setMensagem("");
            setMensagemID("msg_dados_consultados");
            setMensagemDetalhada("", "");
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    /**
     * Método responsavel por apresentar o filtro de pesquisa na tela
     * metaDetalhadaForm e setar na aba selecionada os caracteres
     * correspondentes.
     */
    public void inicializarDadosAbaHistoricoFaturamento() {
        try {
            setApresentarFiltroPesquisaHistorico(true);
            getFecharMetaFaturamentoVO().setAbaSelecionada("HI");
            getFecharMetaFaturamentoVO().setListaHistoricoContatoHistorico(new ArrayList());
            setMensagem("");
            setMensagemID("msg_dados_consultados");
            setMensagemDetalhada("", "");
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    /**
     * Método responsavel por apresentar o filtro de pesquisa na tela
     * metaDetalhadaForm e setar na aba selecionada os caracteres
     * correspondentes.
     */
    public void inicializarDadosAbaHistoricoFaltas() {
        try {
            setApresentarFiltroPesquisaHistorico(true);
            getFecharMetaFaltasVO().setAbaSelecionada("HI");
            getFecharMetaFaltasVO().setListaHistoricoContatoHistorico(new ArrayList());
            setMensagem("");
            setMensagemID("msg_dados_consultados");
            setMensagemDetalhada("", "");
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    /**
     * Método responsavel por apresentar o filtro de pesquisa na tela
     * metaDetalhadaForm e setar na aba selecionada os caracteres
     * correspondentes.
     */
    public void inicializarDadosAbaHistoricoPerda() {
        try {
            setApresentarFiltroPesquisaHistorico(true);
            getFecharMetaPerdaVO().setAbaSelecionada("HI");
            getFecharMetaPerdaVO().setListaHistoricoContatoHistorico(new ArrayList());
            setMensagem("");
            setMensagemID("msg_dados_consultados");
            setMensagemDetalhada("", "");
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public void inicializarDadosAbaHistoricoVencidos() {
        try {
            setApresentarFiltroPesquisaHistorico(true);
            getFecharMetaVencidosVO().setAbaSelecionada("HI");
            getFecharMetaVencidosVO().setListaHistoricoContatoHistorico(new ArrayList());
            setMensagem("");
            setMensagemID("msg_dados_consultados");
            setMensagemDetalhada("", "");
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    /**
     * Método responsavel por apresentar o filtro de pesquisa na tela
     * metaDetalhadaForm e setar na aba selecionada os caracteres
     * correspondentes.
     */
    public void inicializarDadosAbaHistoricoGrupoRisco() {
        try {
            setApresentarFiltroPesquisaHistorico(true);
            getFecharMetaGrupoRiscoVO().setAbaSelecionada("HI");
            getFecharMetaGrupoRiscoVO().setListaHistoricoContatoHistorico(new ArrayList());
            setMensagem("");
            setMensagemID("msg_dados_consultados");
            setMensagemDetalhada("", "");
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    /**
     * Método responsavel por apresentar o filtro de pesquisa na tela
     * metaDetalhadaForm e setar na aba selecionada os caracteres
     * correspondentes.
     */
    public void inicializarDadosAbaHistoricoPosVenda() {
        try {
            setApresentarFiltroPesquisaHistorico(true);
            getFecharMetaPosVendaVO().setAbaSelecionada("HI");
            getFecharMetaPosVendaVO().setListaHistoricoContatoHistorico(new ArrayList());
            setMensagem("");
            setMensagemID("msg_dados_consultados");
            setMensagemDetalhada("", "");
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    /**
     * Método responsavel por apresentar o filtro de pesquisa na tela
     * metaDetalhadaForm e setar na aba selecionada os caracteres
     * correspondentes.
     */
    public void inicializarDadosAbaHistoricoRenovacao() {
        try {
            setApresentarFiltroPesquisaHistorico(true);
            getFecharMetaRenovacaoVO().setAbaSelecionada("HI");
            getFecharMetaRenovacaoVO().setListaHistoricoContatoHistorico(new ArrayList());
            setMensagem("");
            setMensagemID("msg_dados_consultados");
            setMensagemDetalhada("", "");
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public void inicializarDadosAbaHistoricoAniversariante() {
        try {
            setApresentarFiltroPesquisaHistorico(true);
            getFecharMetaAniversarianteVO().setAbaSelecionada("HI");
            getFecharMetaAniversarianteVO().setListaHistoricoContatoHistorico(new ArrayList());
            setMensagem("");
            setMensagemID("msg_dados_consultados");
            setMensagemDetalhada("", "");
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    // ********************************* REGRAS E-MAIL COLETIVO
    // *************************************
    public List<MarcadorVO> getListaSelectItemMarcadoEmail() throws Exception {
        List<MarcadorVO> objs = new ArrayList<MarcadorVO>();
        MarcadorVO marcador = new MarcadorVO();
        for (MarcadoresEmailEnum mEE : MarcadoresEmailEnum.values()) {
            marcador.setTag(mEE.getTag());
            marcador.setNome(mEE.getDescricao());
            objs.add(marcador);
            marcador = new MarcadorVO();
        }
        return objs;
    }

    /**
     * Usado para escolher o meio de envio
     *
     * @return
     * @throws Exception
     */
    public List getListaSelectItemMeioDeEnvio() throws Exception {
        List objs = new ArrayList();
        MeioEnvio[] listaMeioEnvio = MeioEnvio.values();
        for (int i = 0; i < listaMeioEnvio.length; i++) {
            int codigo = listaMeioEnvio[i].getCodigo();
            String descricao = listaMeioEnvio[i].getDescricao();
            objs.add(new SelectItem(codigo, descricao));
        }
        return objs;
    }

    /**
     * Método responsavel por adicionar os emails de pessoa.
     */
    public void listarEmailPessoa() {
        MalaDiretaEnviadaVO obj = (MalaDiretaEnviadaVO) context().getExternalContext().getRequestMap().get("malaDiretaEnviada");
        setListaEmailPessoa(obj.getClienteVO().getPessoa().getEmailVOs());

    }

    public void executarInsercaoTag() {
        try {
            MarcadorVO marcador = (MarcadorVO) context().getExternalContext().getRequestMap().get("marcadorEmail");
            getMalaDiretaVO().setMensagem(MalaDireta.executarInsercaoTag(getMalaDiretaVO().getMensagem(), marcador.getTag(), false));
            setMensagemID("msg_dados_gravados");
            setMensagemDetalhada("", "");
            setMensagem("");
            setSucesso(true);
            setErro(false);
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }

    }

    /**
     * Método responsavel por abrir popup que enviara os emails para as pessoas
     * selecionadas.
     *
     * @return
     */
    public String getExecutarAberturaPopupMalaDireta() {
        if (!malaDiretaVO.getMalaDiretaEnviadaVOs().isEmpty() && getAbrirMalaDireta()) {
            setMensagemDetalhada("", "");
            setMensagem("");
            setMensagemID("");
            setListaConsultarModeloMensagem(new ArrayList());
            return "abrirPopup('malaDiretaColetivoForm.jsp', 'MalaDiretaColetivo', 780, 595);";
        }
        return "";
    }

    @SuppressWarnings("unchecked")
    public void montarListaSelectItemEmpresa() throws Exception {
        this.setListaSelectItemEmpresa(new ArrayList<SelectItem>());
        List<EmpresaVO> empresas = new ArrayList<EmpresaVO>();
        empresas = getFacade().getEmpresa().consultarPorNome("",true, false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        for (EmpresaVO empresa : empresas) {
            this.getListaSelectItemEmpresa().add(new SelectItem(empresa.getCodigo(), empresa.getNome()));
        }
    }

    /**
     * Método responsavel por enviar o email apos selecionar as pessoas na tela
     * metaDetalhadaForm
     *
     * @throws Exception
     */
    public void enviarEmailColetivo() {
        try {
            getMalaDiretaVO().setTipoAgendamento(TipoAgendamentoEnum.AGENDAMENTO_INSTANTANEO);
            getMalaDiretaVO().setTitulo(Uteis.retirarPontosGraficos(getMalaDiretaVO().getTitulo()));
            validarTermosSpam();
            malaDiretaVO.setUsuarioVO(getUsuarioLogado());
            getFacade().getAberturaMeta().executarEmailColetivo(malaDiretaVO, getAberturaMetaVO().getDia(), getEmpresaLogado());
            updateJenkinsService(malaDiretaVO, getConfiguracaoSistemaCRMVO());
            setMensagemID("msg_email_enviado");
            montarMsgAlert(getMensagem());
            setSucesso(true);
            setErro(false);
        } catch (Exception e) {
            setMensagemID("msg_erro");
            setMensagemDetalhada(e);
            montarMsgAlert(getMensagemDetalhada());
            setSucesso(false);
            setErro(true);
        }
    }

    private void validarTermosSpam() throws Exception {
        if (getConfiguracaoSistemaCRMVO().getBloquearTermoSpam()) {
            String verificarTermosSpamNoTitulo = getMalaDiretaVO().verificarTermosSpam(getConfiguracaoSistemaCRMVO());
            if (!verificarTermosSpamNoTitulo.isEmpty()) {
                setMensagemID("msg_mail_termobloqueado");
                String msg = getMensagem() + " " + verificarTermosSpamNoTitulo + ".";
                limparMsg();
                throw new Exception(msg);
            }
        }
    }

    /**
     * Método responsavel por enviar o email apos selecionar as pessoas na tela
     * metaDetalhadaForm
     *
     * @throws Exception
     */
    public void enviarSMSColetivo() {
        try {
            setMsgAlert("");
            if(UteisValidacao.emptyString(getEmpresaLogado().getTokenSMS())){
                throw new ConsistirException("Empresa não tem token configurado!");
            }
            getMalaDiretaVO().setTipoAgendamento(TipoAgendamentoEnum.AGENDAMENTO_INSTANTANEO);
            validarTermosSpam();
            malaDiretaVO.setTotalPessoaMalaDiretaEnviada(0);
            malaDiretaVO.setUsuarioVO(getUsuarioLogado());
            getFacade().getMalaDireta().agendarEnvio(malaDiretaVO, getAberturaMetaVO().getDia(), getEmpresaLogado());
            updateJenkinsService(malaDiretaVO, getConfiguracaoSistemaCRMVO());
            setMensagemID("msg_SMS_enviado");
            montarMsgAlert(getMensagem());
            setSucesso(true);
            setErro(false);
        } catch (Exception e) {
            setMensagemDetalhada(e);
            setMensagemID("msg_erro");
            montarMsgAlert(e.getMessage());
            setSucesso(false);
            setErro(true);
        }
    }

    /**
     * Método responsavel por selecionar todas metasDetalhadas quando selecionar
     * o checkBox em Opções.
     */
    public void executarSelecaoTodosFecharMetaDetalhadosIndicacao() {
        try {
            getFacade().getAberturaMeta().executarSelecaoTodosFecharMetaDetalhados(getFecharMetaIndicadoVO());
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    /**
     * Método responsavel por selecionar todas metasDetalhadas quando selecionar
     * o checkBox em Opções.
     */
    public void executarSelecaoTodosFecharMetaDetalhadosAgendados() {
        try {
            getFacade().getAberturaMeta().executarSelecaoTodosFecharMetaDetalhados(getFecharMetaAgendadosVO());
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public void executarSelecaoTodosFecharMetaDetalhadosConversaoAgendados() {
        try {
            getFacade().getAberturaMeta().executarSelecaoTodosFecharMetaDetalhados(getFecharMetaConversaoAgendadosVO());
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public void executarSelecaoTodosFecharMetaDetalhadosLigacaoAgendados() {
        try {
            getFacade().getAberturaMeta().executarSelecaoTodosFecharMetaDetalhados(getFecharMetaLigacaoAgendadosVO());
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    /**
     * Método responsavel por selecionar todas metasDetalhadas quando selecionar
     * o checkBox em Opções.
     */
    public void executarSelecaoTodosFecharMetaDetalhadosVinteQuatroHoras() {
        try {
            getFacade().getAberturaMeta().executarSelecaoTodosFecharMetaDetalhados(getFecharMetaVinteQuatroHorasVO());
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    /**
     * Método responsavel por selecionar todas metasDetalhadas quando selecionar
     * o checkBox em Opções.
     */
    public void executarSelecaoTodosFecharMetaDetalhadosVendas() {
        try {
            getFacade().getAberturaMeta().executarSelecaoTodosFecharMetaDetalhados(getFecharMetaVendasVO());
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public void executarSelecaoTodosFecharMetaDetalhadosVencidos() {
        try {
            getFacade().getAberturaMeta().executarSelecaoTodosFecharMetaDetalhados(getFecharMetaVencidosVO());
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    /**
     * Método responsavel por selecionar todas metasDetalhadas quando selecionar
     * o checkBox em Opções.
     */
    public void executarSelecaoTodosFecharMetaDetalhadosFaturamento() {
        try {
            getFacade().getAberturaMeta().executarSelecaoTodosFecharMetaDetalhados(getFecharMetaFaturamentoVO());
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    /**
     * Método responsavel por selecionar todas metasDetalhadas quando selecionar
     * o checkBox em Opções.
     */
    public void executarSelecaoTodosFecharMetaDetalhadosFaltas() {
        try {
            getFacade().getAberturaMeta().executarSelecaoTodosFecharMetaDetalhados(getFecharMetaFaltasVO());
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    /**
     * Método responsavel por selecionar todas metasDetalhadas quando selecionar
     * o checkBox em Opções.
     */
    public void executarSelecaoTodosFecharMetaDetalhadosPerda() {
        try {
            getFacade().getAberturaMeta().executarSelecaoTodosFecharMetaDetalhados(getFecharMetaPerdaVO());
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    /**
     * Método responsavel por selecionar todas metasDetalhadas quando selecionar
     * o checkBox em Opções.
     */
    public void executarSelecaoTodosFecharMetaDetalhadosGrupoRisco() {
        try {
            getFacade().getAberturaMeta().executarSelecaoTodosFecharMetaDetalhados(getFecharMetaGrupoRiscoVO());
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    /**
     * Método responsavel por selecionar todas metasDetalhadas quando selecionar
     * o checkBox em Opções.
     */
    public void executarSelecaoTodosFecharMetaDetalhadosPosVenda() {
        try {
            getFacade().getAberturaMeta().executarSelecaoTodosFecharMetaDetalhados(getFecharMetaPosVendaVO());
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    /**
     * Método responsavel por selecionar todas metasDetalhadas quando selecionar
     * o checkBox em Opções.
     */
    public void executarSelecaoTodosFecharMetaDetalhadosRenovacao() {
        try {
            getFacade().getAberturaMeta().executarSelecaoTodosFecharMetaDetalhados(getFecharMetaRenovacaoVO());
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    /**
     * Método responsavel por selecionar todas metasDetalhadas quando selecionar
     * o checkBox em Opções.
     */
    public void executarSelecaoTodosFecharMetaDetalhadosAniversariante() {
        try {
            getFacade().getAberturaMeta().executarSelecaoTodosFecharMetaDetalhados(getFecharMetaAniversarianteVO());
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    /**
     * Método responsavel por selecionar todas metasDetalhadas quando selecionar
     * o checkBox em Opções.
     */
    public void executarSelecaoTodosFecharMetaDetalhadosPassivo() {
        try {
            getFacade().getAberturaMeta().executarSelecaoTodosFecharMetaDetalhados(getFecharMetaPassivoVO());

        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    // METODOS DE METAPASSIVODETALHADAFORM PARA TOTALIZADOR DE SELEÇÃO
    public void executarTotalizadorSelecionadoPassivoHoje() {
        try {
            getFecharMetaPassivoVO().setTotalizadorSelecionadoHoje(getFacade().getFecharMeta().executarTotalizadorSelecionado(getFecharMetaPassivoVO().getFecharMetaDetalhadoVOs(), getFecharMetaPassivoVO().getTotalizadorSelecionadoHoje()));
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public void executarTotalizadorSelecionadoPassivoHistorico() {
        try {
            getFecharMetaPassivoVO().setTotalizadorSelecionadoHistorico(getFacade().getFecharMeta().executarTotalizadorSelecionado(getFecharMetaPassivoVO().getListaHistoricoContatoHistorico(), getFecharMetaPassivoVO().getTotalizadorSelecionadoHistorico()));
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    // METODOS DE METAINDICADODETALHADAFORM PARA TOTALIZADOR DE SELEÇÃO
    public void executarTotalizadorSelecionadoIndicacaoHoje() {
        try {
            getFecharMetaIndicadoVO().setTotalizadorSelecionadoHoje(getFacade().getFecharMeta().executarTotalizadorSelecionado(getFecharMetaIndicadoVO().getFecharMetaDetalhadoVOs(), getFecharMetaIndicadoVO().getTotalizadorSelecionadoHoje()));
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public void executarTotalizadorSelecionadoIndicacaoHistorico() {
        try {
            getFecharMetaIndicadoVO().setTotalizadorSelecionadoHistorico(getFacade().getFecharMeta().executarTotalizadorSelecionado(getFecharMetaIndicadoVO().getListaHistoricoContatoHistorico(), getFecharMetaIndicadoVO().getTotalizadorSelecionadoHistorico()));
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    // METODOS DE METAAGENDADOSDETALHADAFORM PARA TOTALIZADOR DE SELEÇÃO
    public void executarTotalizadorSelecionadoAgendadosHoje() {
        try {
            getFecharMetaAgendadosVO().setTotalizadorSelecionadoHoje(getFacade().getFecharMeta().executarTotalizadorSelecionado(getFecharMetaAgendadosVO().getFecharMetaDetalhadoVOs(), getFecharMetaAgendadosVO().getTotalizadorSelecionadoHoje()));
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public void executarTotalizadorSelecionadoAgendadosHistorico() {
        try {
            getFecharMetaAgendadosVO().setTotalizadorSelecionadoHistorico(getFacade().getFecharMeta().executarTotalizadorSelecionado(getFecharMetaAgendadosVO().getListaHistoricoContatoHistorico(), getFecharMetaAgendadosVO().getTotalizadorSelecionadoHistorico()));
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    // METODOS DE METAVINTEQUATROHORASDETALHADAFORM PARA TOTALIZADOR DE SELEÇÃO
    public void executarTotalizadorSelecionadoVinteQuatroHorasHoje() {
        try {
            getFecharMetaVinteQuatroHorasVO().setTotalizadorSelecionadoHoje(getFacade().getFecharMeta().executarTotalizadorSelecionado(getFecharMetaVinteQuatroHorasVO().getFecharMetaDetalhadoVOs(), getFecharMetaVinteQuatroHorasVO().getTotalizadorSelecionadoHoje()));
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public void executarTotalizadorSelecionadoVendasHoje() {
        try {
            getFecharMetaVendasVO().setTotalizadorSelecionadoHoje(getFacade().getFecharMeta().executarTotalizadorSelecionado(getFecharMetaVendasVO().getFecharMetaDetalhadoVOs(), getFecharMetaVendasVO().getTotalizadorSelecionadoHoje()));
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public void executarTotalizadorSelecionadoVencidosHoje() {
        try {
            getFecharMetaVendasVO().setTotalizadorSelecionadoHoje(getFacade().getFecharMeta().executarTotalizadorSelecionado(getFecharMetaVencidosVO().getFecharMetaDetalhadoVOs(), getFecharMetaVencidosVO().getTotalizadorSelecionadoHoje()));
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public void executarTotalizadorSelecionadoFaturamentoHoje() {
        try {
            getFecharMetaFaturamentoVO().setTotalizadorSelecionadoHoje(getFacade().getFecharMeta().executarTotalizadorSelecionado(getFecharMetaFaturamentoVO().getFecharMetaDetalhadoVOs(), getFecharMetaFaturamentoVO().getTotalizadorSelecionadoHoje()));
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public void executarTotalizadorSelecionadoFaltasHoje() {
        try {
            getFecharMetaFaltasVO().setTotalizadorSelecionadoHoje(getFacade().getFecharMeta().executarTotalizadorSelecionado(getFecharMetaFaltasVO().getFecharMetaDetalhadoVOs(), getFecharMetaFaltasVO().getTotalizadorSelecionadoHoje()));
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public void executarTotalizadorSelecionadoPerdaHoje() {
        try {
            getFecharMetaPerdaVO().setTotalizadorSelecionadoHoje(getFacade().getFecharMeta().executarTotalizadorSelecionado(getFecharMetaPerdaVO().getFecharMetaDetalhadoVOs(), getFecharMetaPerdaVO().getTotalizadorSelecionadoHoje()));
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public void executarTotalizadorSelecionadoGrupoRiscoHoje() {
        try {
            getFecharMetaGrupoRiscoVO().setTotalizadorSelecionadoHoje(getFacade().getFecharMeta().executarTotalizadorSelecionado(getFecharMetaGrupoRiscoVO().getFecharMetaDetalhadoVOs(), getFecharMetaGrupoRiscoVO().getTotalizadorSelecionadoHoje()));
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public void executarTotalizadorSelecionadoPosVendaHoje() {
        try {
            getFecharMetaPosVendaVO().setTotalizadorSelecionadoHoje(getFacade().getFecharMeta().executarTotalizadorSelecionado(getFecharMetaPosVendaVO().getFecharMetaDetalhadoVOs(), getFecharMetaPosVendaVO().getTotalizadorSelecionadoHoje()));
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public void executarTotalizadorSelecionadoRenovacaoHoje() {
        try {
            getFecharMetaRenovacaoVO().setTotalizadorSelecionadoHoje(getFacade().getFecharMeta().executarTotalizadorSelecionado(getFecharMetaRenovacaoVO().getFecharMetaDetalhadoVOs(), getFecharMetaRenovacaoVO().getTotalizadorSelecionadoHoje()));
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public void executarTotalizadorSelecionadoAniversarianteHoje() {
        try {
            getFecharMetaAniversarianteVO().setTotalizadorSelecionadoHoje(getFacade().getFecharMeta().executarTotalizadorSelecionado(getFecharMetaAniversarianteVO().getFecharMetaDetalhadoVOs(), getFecharMetaAniversarianteVO().getTotalizadorSelecionadoHoje()));
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public void executarTotalizadorSelecionadoVinteQuatroHorasHistorico() {
        try {
            getFecharMetaVinteQuatroHorasVO().setTotalizadorSelecionadoHistorico(getFacade().getFecharMeta().executarTotalizadorSelecionado(getFecharMetaVinteQuatroHorasVO().getListaHistoricoContatoHistorico(), getFecharMetaVinteQuatroHorasVO().getTotalizadorSelecionadoHistorico()));
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public void executarTotalizadorSelecionadoVendasHistorico() {
        try {
            getFecharMetaVendasVO().setTotalizadorSelecionadoHistorico(getFacade().getFecharMeta().executarTotalizadorSelecionado(getFecharMetaVendasVO().getListaHistoricoContatoHistorico(), getFecharMetaVendasVO().getTotalizadorSelecionadoHistorico()));
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public void executarTotalizadorSelecionadoVencidosHistorico() {
        try {
            getFecharMetaVendasVO().setTotalizadorSelecionadoHistorico(getFacade().getFecharMeta().executarTotalizadorSelecionado(getFecharMetaVencidosVO().getListaHistoricoContatoHistorico(), getFecharMetaVencidosVO().getTotalizadorSelecionadoHistorico()));
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public void executarTotalizadorSelecionadoFaturamentoHistorico() {
        try {
            getFecharMetaFaturamentoVO().setTotalizadorSelecionadoHistorico(getFacade().getFecharMeta().executarTotalizadorSelecionado(getFecharMetaFaturamentoVO().getListaHistoricoContatoHistorico(), getFecharMetaFaturamentoVO().getTotalizadorSelecionadoHistorico()));
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public void executarTotalizadorSelecionadoFaltasHistorico() {
        try {
            getFecharMetaFaltasVO().setTotalizadorSelecionadoHistorico(getFacade().getFecharMeta().executarTotalizadorSelecionado(getFecharMetaFaltasVO().getListaHistoricoContatoHistorico(), getFecharMetaFaltasVO().getTotalizadorSelecionadoHistorico()));
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public void executarTotalizadorSelecionadoPerdaHistorico() {
        try {
            getFecharMetaPerdaVO().setTotalizadorSelecionadoHistorico(getFacade().getFecharMeta().executarTotalizadorSelecionado(getFecharMetaPerdaVO().getListaHistoricoContatoHistorico(), getFecharMetaPerdaVO().getTotalizadorSelecionadoHistorico()));
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public void executarTotalizadorSelecionadoGrupoRiscoHistorico() {
        try {
            getFecharMetaGrupoRiscoVO().setTotalizadorSelecionadoHistorico(getFacade().getFecharMeta().executarTotalizadorSelecionado(getFecharMetaGrupoRiscoVO().getListaHistoricoContatoHistorico(), getFecharMetaGrupoRiscoVO().getTotalizadorSelecionadoHistorico()));
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public void executarTotalizadorSelecionadoPosVendaHistorico() {
        try {
            getFecharMetaPosVendaVO().setTotalizadorSelecionadoHistorico(getFacade().getFecharMeta().executarTotalizadorSelecionado(getFecharMetaPosVendaVO().getListaHistoricoContatoHistorico(), getFecharMetaPosVendaVO().getTotalizadorSelecionadoHistorico()));
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public void executarTotalizadorSelecionadoRenovacaoHistorico() {
        try {
            getFecharMetaRenovacaoVO().setTotalizadorSelecionadoHistorico(getFacade().getFecharMeta().executarTotalizadorSelecionado(getFecharMetaRenovacaoVO().getListaHistoricoContatoHistorico(), getFecharMetaRenovacaoVO().getTotalizadorSelecionadoHistorico()));
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public void executarTotalizadorSelecionadoAniversarianteHistorico() {
        try {
            getFecharMetaAniversarianteVO().setTotalizadorSelecionadoHistorico(getFacade().getFecharMeta().executarTotalizadorSelecionado(getFecharMetaAniversarianteVO().getListaHistoricoContatoHistorico(), getFecharMetaAniversarianteVO().getTotalizadorSelecionadoHistorico()));
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    /**
     * Método responsavel por verificar se a pessoa selecionada é um passivo,
     * indicado ou cliente. Faz a consulta por chave primaria e seta no controle
     * especificado
     *
     * @throws Exception
     */
    public void editarPessoaListaFecharMetaDetalhado() throws Exception {
        FecharMetaDetalhadoVO obj = (FecharMetaDetalhadoVO) context().getExternalContext().getRequestMap().get("fecharMetaDetalhado");
        setClienteVO(obj.getCliente());
        if (obj.getCliente().getCodigo() != 0) {
            limparControlesAntesIrParaTelaCliente();
            irParaTelaCliente(getClienteVO());
            setAbrirPopUpCliente(true);
            setAbrirPopUpPassivo(false);
            setAbrirPopUpIndicacao(false);
        }
        if (obj.getPassivo().getCodigo() != 0) {
            PassivoControle passivoControle = (PassivoControle) getControlador("PassivoControle");
            passivoControle.setPassivoVO(getFacade().getPassivo().consultarPorChavePrimaria(obj.getPassivo().getCodigo(), Uteis.NIVELMONTARDADOS_TODOS));
            passivoControle.setMensagem("");
            passivoControle.setMensagemID("");
            passivoControle.setMensagemDetalhada("", "");
            passivoControle.setSucesso(false);
            passivoControle.setErro(false);
            setAbrirPopUpPassivo(true);
            setAbrirPopUpCliente(false);
            setAbrirPopUpIndicacao(false);
        }
        if (obj.getIndicado().getCodigo() != 0) {
            IndicacaoControle indicacaoControle = (IndicacaoControle) getControlador("IndicacaoControle");
            indicacaoControle.setIndicacaoVO(getFacade().getIndicacao().consultarPorChavePrimaria(obj.getIndicado().getIndicacaoVO().getCodigo(), Uteis.NIVELMONTARDADOS_TODOS));
            indicacaoControle.setMensagem("");
            indicacaoControle.setMensagemID("");
            indicacaoControle.setMensagemDetalhada("", "");
            indicacaoControle.setSucesso(false);
            indicacaoControle.setErro(false);
            setAbrirPopUpIndicacao(true);
            setAbrirPopUpPassivo(false);
            setAbrirPopUpCliente(false);
        }
    }

    public void limparControlesAntesIrParaTelaCliente() {
        MovPagamentoControle movPagamentoControle = (MovPagamentoControle) getControlador("MovPagamentoControle");
        if (movPagamentoControle != null) {
            movPagamentoControle.getMovPagamentoVO().setPessoa(new PessoaVO());
        }
    }

    /**
     * Método responsavel por alterar apenas o campo FecharMeta da AberturaMeta
     * no momento de fazer o Fechamento do Dia.
     *
     * <AUTHOR>
     */
    public void persistirAlteracaoFechamentoDiaAberturaMeta() {
        try {
            fechamentoAberturaMeta(getAberturaMetaVO());
            setAberturaMeta(getAberturaMetaVO());
            enviarEmail();
            setApresentarBotoesGravarAdicionarJustificativa(false);
            setMensagem("");
            setMensagemDetalhada("", "");
            setMensagemID("msg_dados_gravados");
            setSucesso(true);
            setErro(false);
            montarMsgAlert(getMensagem());
        } catch (Exception e) {
            setSucesso(false);
            setErro(true);
            setMensagemDetalhada("msg_erro", e.getMessage());
            montarMsgAlert(getMensagemDetalhada());
        }
    }

    private void fechamentoAberturaMeta(AberturaMetaVO aberturaMeta) throws Exception {
        setAberturaMetaVO(getFacade().getAberturaMeta().persistirAlteracaoFechamentoDiaAberturaMeta(aberturaMeta));
        atualizarMetaDiaPorColaboradorVenda(aberturaMeta.getColaboradorResponsavel());
        atualizarMetaDiaPorColaboradorRetencao(aberturaMeta.getColaboradorResponsavel());
    }

    /**
     * Método responsavel por validar qual popup Abrir.
     */
    public String getValidarQualPopUpAbrirMetaAgendado() {
        if (getAbrirPopUpCliente()) {
            return "abrirPopup('faces/clienteNav.jsp?page=viewCliente', 'Cliente',  1024, 595);";
        } else if (getAbrirPopUpPassivo()) {
            return "abrirPopup('faces/passivoForm.jsp', 'configuracaoSistemaCRM', 780, 595);";
        } else if (getAbrirPopUpIndicacao()) {
            return "abrirPopup('faces/indicacaoForm.jsp', 'configuracaoSistemaCRM', 780, 595);";
        }
        return "";
    }

    public void executarSelecaoFecharMetaJustificativa() {
        try {
            FecharMetaVO obj = (FecharMetaVO) context().getExternalContext().getRequestMap().get("fecharMeta");
            setFecharMetaJustificativa(new FecharMetaVO());
            setFecharMetaJustificativa(obj);
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public void adicionarFecharMetaJustificativa() {
        try {
            getFacade().getAberturaMeta().adicionarFecharMetaJustificativa(getFecharMetaJustificativa(), getAberturaMetaVO().getListaFechametoDia());
            setFecharMetaJustificativa(new FecharMetaVO());
            setMensagemID("msg_adicionados_dados");
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    /**
     * Método responsavel por gerar Relatorio da Meta Perdas
     *
     * @throws Exception
     * <AUTHOR>
     */
    public void executarImpressaoFechamentoDiaAberturaMeta() throws Exception {
        imprimirRelatorio(getEmpresaLogado(), getFecharAbertura().getListaFechametoDia(), "Relatório Fechamento Dia", "", "FechamentoDiaAberturaMetaRel", getFecharAbertura().getTotalMetaDia());
    }

    public void executarImpressaoAnteriorFechamento() throws Exception {
        imprimirRelatorio(getEmpresaLogado(), getAberturaMeta().getListaFechametoDia(), "Relatório Fechamento Dia", "", "FechamentoDiaAberturaMetaRel", getAberturaMeta().getTotalMetaDia());
    }

    /**
     * Método responsavel por gerar Relatorio da Meta Perdas
     *
     * @throws Exception
     * <AUTHOR>
     */
    public void executarImpressaoFechamentoDia() throws Exception {
        imprimirRelatorio(getEmpresaLogado(), getFecharAbertura().getListaFechametoDia(), "Relatório Fechamento Dia", "", "FechamentoDiaAberturaMetaRel", getFecharAbertura().getTotalMetaDia());
    }

    /**
     * <AUTHOR> 30/08/2011
     */
    public void prepararImpressao() {
        if (this.getFecharAbertura() == null || this.getFecharAbertura().getListaFechametoDia().isEmpty()) {
            try {
                exibirMeta(this.getAberturaMetaVO());
            } catch (Exception e) {
                setMensagemDetalhada("msg_erro", e.getMessage());
            }
        }
    }

    public String getValorConsultaNomeVinteQuatroHoras() {
        if (valorConsultaNomeVinteQuatroHoras == null) {
            valorConsultaNomeVinteQuatroHoras = "";
        }
        return valorConsultaNomeVinteQuatroHoras;
    }

    public void setValorConsultaNomeVinteQuatroHoras(String valorConsultaNomeVinteQuatroHoras) {
        this.valorConsultaNomeVinteQuatroHoras = valorConsultaNomeVinteQuatroHoras;
    }

    public FecharMetaVO getFecharMetaAniversarianteVO() {
        if (fecharMetaAniversarianteVO == null) {
            fecharMetaAniversarianteVO = new FecharMetaVO();
        }
        return fecharMetaAniversarianteVO;
    }

    public void setFecharMetaAniversarianteVO(FecharMetaVO fecharMetaAniversarianteVO) {
        this.fecharMetaAniversarianteVO = fecharMetaAniversarianteVO;
    }

    public List getListaEmailPessoa() {
        if (listaEmailPessoa == null) {
            listaEmailPessoa = new ArrayList();
        }
        return listaEmailPessoa;
    }

    public void setListaEmailPessoa(List listaEmailPessoa) {
        this.listaEmailPessoa = listaEmailPessoa;
    }

    public FecharMetaVO getFecharMetaPassivoVO() {
        if (fecharMetaPassivoVO == null) {
            fecharMetaPassivoVO = new FecharMetaVO();
        }
        return fecharMetaPassivoVO;
    }

    public void setFecharMetaPassivoVO(FecharMetaVO fecharMetaPassivoVO) {
        this.fecharMetaPassivoVO = fecharMetaPassivoVO;
    }

    public FecharMetaVO getFecharMetaIndicadoVO() {
        if (fecharMetaIndicadoVO == null) {
            fecharMetaIndicadoVO = new FecharMetaVO();
        }
        return fecharMetaIndicadoVO;
    }

    public void setFecharMetaIndicadoVO(FecharMetaVO fecharMetaIndicadoVO) {
        this.fecharMetaIndicadoVO = fecharMetaIndicadoVO;
    }

    public AberturaMetaVO getAberturaMetaVO() {
        return aberturaMetaVO;
    }

    public void setAberturaMetaVO(AberturaMetaVO aberturaMetaVO) {
        this.aberturaMetaVO = aberturaMetaVO;
    }

    public Date getDataConsulta() {
        return dataConsulta;
    }

    public void setDataConsulta(Date dataConsulta) {
        this.dataConsulta = dataConsulta;
    }

    public Integer getResultadoTotalPassivo() {
        return resultadoTotalPassivo;
    }

    public void setResultadoTotalPassivo(Integer resultadoTotalPassivo) {
        this.resultadoTotalPassivo = resultadoTotalPassivo;
    }

    public Integer getResultadoTotalIndicacao() {
        return resultadoTotalIndicacao;
    }

    public void setResultadoTotalIndicacao(Integer resultadoTotalIndicacao) {
        this.resultadoTotalIndicacao = resultadoTotalIndicacao;
    }

    public Boolean getManterAbertoRichModal() {
        if (manterAbertoRichModal == null) {
            manterAbertoRichModal = false;
        }
        return manterAbertoRichModal;
    }

    public void setManterAbertoRichModal(Boolean manterAbertoRichModal) {
        this.manterAbertoRichModal = manterAbertoRichModal;
    }

    public List getListaConsultaColaboradorResponsavel() {
        return listaConsultaColaboradorResponsavel;
    }

    public void setListaConsultaColaboradorResponsavel(List listaConsultaColaboradorResponsavel) {
        this.listaConsultaColaboradorResponsavel = listaConsultaColaboradorResponsavel;
    }

    public String getValorConsultaColaboradorSubstituido() {
        return valorConsultaColaboradorSubstituido;
    }

    public void setValorConsultaColaboradorSubstituido(String valorConsultaColaboradorSubstituido) {
        this.valorConsultaColaboradorSubstituido = valorConsultaColaboradorSubstituido;
    }

    public String getCampoConsultaColaboradorSubstituido() {
        return campoConsultaColaboradorSubstituido;
    }

    public void setCampoConsultaColaboradorSubstituido(String campoConsultaColaboradorSubstituido) {
        this.campoConsultaColaboradorSubstituido = campoConsultaColaboradorSubstituido;
    }

    public Boolean getApresentarBotaoGravarEMensagem() {
        if (apresentarBotaoGravarEMensagem == null) {
            apresentarBotaoGravarEMensagem = false;
        }
        return apresentarBotaoGravarEMensagem;
    }

    public void setApresentarBotaoGravarEMensagem(Boolean apresentarBotaoGravarEMensagem) {
        this.apresentarBotaoGravarEMensagem = apresentarBotaoGravarEMensagem;
    }

    public Boolean getFecharRichModalAberturaMeta() {
        if (fecharRichModalAberturaMeta == null) {
            fecharRichModalAberturaMeta = false;
        }
        return fecharRichModalAberturaMeta;
    }

    public void setFecharRichModalAberturaMeta(Boolean fecharRichModalAberturaMeta) {
        this.fecharRichModalAberturaMeta = fecharRichModalAberturaMeta;
    }

    public Date getDataInicio() {
        if (dataInicio == null) {
            dataInicio = negocio.comuns.utilitarias.Calendario.hoje();
        }
        return dataInicio;
    }

    public void setDataInicio(Date dataInicio) {
        this.dataInicio = dataInicio;
    }

    public Date getDataTermino() {
        if (dataTermino == null) {
            dataTermino = negocio.comuns.utilitarias.Calendario.hoje();
        }
        return dataTermino;
    }

    public void setDataTermino(Date dataTermino) {
        this.dataTermino = dataTermino;
    }

    public Boolean getApresentarFiltroPesquisaHistorico() {
        if (apresentarFiltroPesquisaHistorico == null) {
            apresentarFiltroPesquisaHistorico = false;
        }

        return apresentarFiltroPesquisaHistorico;
    }

    public void setApresentarFiltroPesquisaHistorico(Boolean apresentarFiltroPesquisaHistorico) {
        this.apresentarFiltroPesquisaHistorico = apresentarFiltroPesquisaHistorico;
    }

    public String getValorConsultaNomePassivo() {
        if (valorConsultaNomePassivo == null) {
            valorConsultaNomePassivo = "";
        }
        return valorConsultaNomePassivo;
    }

    public void setValorConsultaNomePassivo(String valorConsultaNomePassivo) {
        this.valorConsultaNomePassivo = valorConsultaNomePassivo;
    }

    public String getValorConsultaNomeIndicado() {
        if (valorConsultaNomeIndicado == null) {
            valorConsultaNomeIndicado = "";
        }
        return valorConsultaNomeIndicado;
    }

    public void setValorConsultaNomeIndicado(String valorConsultaNomeIndicado) {
        this.valorConsultaNomeIndicado = valorConsultaNomeIndicado;
    }

    public MalaDiretaVO getMalaDiretaVO() {
        if (malaDiretaVO == null) {
            malaDiretaVO = new MalaDiretaVO();
        }
        return malaDiretaVO;
    }

    public void setMalaDiretaVO(MalaDiretaVO malaDiretaVO) {
        this.malaDiretaVO = malaDiretaVO;
    }


    public String getCampoConsultarModeloMensagem() {
        if (campoConsultarModeloMensagem == null) {
            campoConsultarModeloMensagem = "";
        }
        return campoConsultarModeloMensagem;
    }

    public void setCampoConsultarModeloMensagem(String campoConsultarModeloMensagem) {
        this.campoConsultarModeloMensagem = campoConsultarModeloMensagem;
    }

    public String getValorConsultarModeloMensagem() {
        if (valorConsultarModeloMensagem == null) {
            valorConsultarModeloMensagem = "";
        }
        return valorConsultarModeloMensagem;
    }

    public void setValorConsultarModeloMensagem(String valorConsultarModeloMensagem) {
        this.valorConsultarModeloMensagem = valorConsultarModeloMensagem;
    }

    public List getListaConsultarModeloMensagem() {
        if (listaConsultarModeloMensagem == null) {
            listaConsultarModeloMensagem = new ArrayList();
        }
        return listaConsultarModeloMensagem;
    }

    public void setListaConsultarModeloMensagem(List listaConsultarModeloMensagem) {
        this.listaConsultarModeloMensagem = listaConsultarModeloMensagem;
    }

    public FecharMetaVO getFecharMetaAgendadosVO() {
        if (fecharMetaAgendadosVO == null) {
            fecharMetaAgendadosVO = new FecharMetaVO();
        }
        return fecharMetaAgendadosVO;
    }

    public void setFecharMetaAgendadosVO(FecharMetaVO fecharMetaAgendadosVO) {
        this.fecharMetaAgendadosVO = fecharMetaAgendadosVO;
    }

    public String getValorConsultaNomeAgendados() {
        if (valorConsultaNomeAgendados == null) {
            valorConsultaNomeAgendados = "";
        }
        return valorConsultaNomeAgendados;
    }

    public void setValorConsultaNomeAgendados(String valorConsultaNomeAgendados) {
        this.valorConsultaNomeAgendados = valorConsultaNomeAgendados;
    }

    public Boolean getAbrirPopUpCliente() {
        if (abrirPopUpCliente == null) {
            abrirPopUpCliente = false;
        }
        return abrirPopUpCliente;
    }

    public void setAbrirPopUpCliente(Boolean abrirPopUpCliente) {
        this.abrirPopUpCliente = abrirPopUpCliente;
    }

    public Boolean getAbrirPopUpIndicacao() {
        if (abrirPopUpIndicacao == null) {
            abrirPopUpIndicacao = false;
        }
        return abrirPopUpIndicacao;
    }

    public void setAbrirPopUpIndicacao(Boolean abrirPopUpIndicacao) {
        this.abrirPopUpIndicacao = abrirPopUpIndicacao;
    }

    public Boolean getAbrirPopUpPassivo() {
        if (abrirPopUpPassivo == null) {
            abrirPopUpPassivo = false;
        }
        return abrirPopUpPassivo;
    }

    public void setAbrirPopUpPassivo(Boolean abrirPopUpPassivo) {
        this.abrirPopUpPassivo = abrirPopUpPassivo;
    }

    public FecharMetaVO getFecharMetaVinteQuatroHorasVO() {
        if (fecharMetaVinteQuatroHorasVO == null) {
            fecharMetaVinteQuatroHorasVO = new FecharMetaVO();
        }
        return fecharMetaVinteQuatroHorasVO;
    }

    public void setFecharMetaVinteQuatroHorasVO(FecharMetaVO fecharMetaVinteQuatroHorasVO) {
        this.fecharMetaVinteQuatroHorasVO = fecharMetaVinteQuatroHorasVO;
    }

    public String getValorConsultaNomeAniversariante() {
        if (valorConsultaNomeAniversariante == null) {
            valorConsultaNomeAniversariante = "";
        }
        return valorConsultaNomeAniversariante;
    }

    public void setValorConsultaNomeAniversariante(String valorConsultaNomeAniversariante) {
        this.valorConsultaNomeAniversariante = valorConsultaNomeAniversariante;
    }

    public FecharMetaVO getFecharMetaRenovacaoVO() {
        if (fecharMetaRenovacaoVO == null) {
            fecharMetaRenovacaoVO = new FecharMetaVO();
        }
        return fecharMetaRenovacaoVO;
    }

    public void setFecharMetaRenovacaoVO(FecharMetaVO fecharMetaRenovacaoVO) {
        this.fecharMetaRenovacaoVO = fecharMetaRenovacaoVO;
    }

    public String getValorConsultaNomeRenovacao() {
        if (valorConsultaNomeRenovacao == null) {
            valorConsultaNomeRenovacao = "";
        }
        return valorConsultaNomeRenovacao;
    }

    public void setValorConsultaNomeRenovacao(String valorConsultaNomeRenovacao) {
        this.valorConsultaNomeRenovacao = valorConsultaNomeRenovacao;
    }

    public FecharMetaVO getFecharMetaPosVendaVO() {
        if (fecharMetaPosVendaVO == null) {
            fecharMetaPosVendaVO = new FecharMetaVO();
        }
        return fecharMetaPosVendaVO;
    }

    public void setFecharMetaPosVendaVO(FecharMetaVO fecharMetaPosVendaVO) {
        this.fecharMetaPosVendaVO = fecharMetaPosVendaVO;
    }

    public String getValorConsultaNomePosVenda() {
        if (valorConsultaNomePosVenda == null) {
            valorConsultaNomePosVenda = "";
        }
        return valorConsultaNomePosVenda;
    }

    public void setValorConsultaNomePosVenda(String valorConsultaNomePosVenda) {
        this.valorConsultaNomePosVenda = valorConsultaNomePosVenda;
    }

    public FecharMetaVO getFecharMetaGrupoRiscoVO() {
        if (fecharMetaGrupoRiscoVO == null) {
            fecharMetaGrupoRiscoVO = new FecharMetaVO();
        }
        return fecharMetaGrupoRiscoVO;
    }

    public void setFecharMetaGrupoRiscoVO(FecharMetaVO fecharMetaGrupoRiscoVO) {
        this.fecharMetaGrupoRiscoVO = fecharMetaGrupoRiscoVO;
    }

    public String getValorConsultaNomeGrupoRisco() {
        if (valorConsultaNomeGrupoRisco == null) {
            valorConsultaNomeGrupoRisco = "";
        }
        return valorConsultaNomeGrupoRisco;
    }

    public void setValorConsultaNomeGrupoRisco(String valorConsultaNomeGrupoRisco) {
        this.valorConsultaNomeGrupoRisco = valorConsultaNomeGrupoRisco;
    }

    public FecharMetaVO getFecharMetaPerdaVO() {
        if (fecharMetaPerdaVO == null) {
            fecharMetaPerdaVO = new FecharMetaVO();
        }
        return fecharMetaPerdaVO;
    }

    public void setFecharMetaPerdaVO(FecharMetaVO fecharMetaPerdaVO) {
        this.fecharMetaPerdaVO = fecharMetaPerdaVO;
    }

    public String getValorConsultaNomePerda() {
        if (valorConsultaNomePerda == null) {
            valorConsultaNomePerda = "";
        }
        return valorConsultaNomePerda;
    }

    public void setValorConsultaNomePerda(String valorConsultaNomePerda) {
        this.valorConsultaNomePerda = valorConsultaNomePerda;
    }

    public FecharMetaVO getFecharMetaFaltasVO() {
        if (fecharMetaFaltasVO == null) {
            fecharMetaFaltasVO = new FecharMetaVO();
        }
        return fecharMetaFaltasVO;
    }

    public void setFecharMetaFaltasVO(FecharMetaVO fecharMetaFaltasVO) {
        this.fecharMetaFaltasVO = fecharMetaFaltasVO;
    }

    public String getValorConsultaNomeFaltas() {
        if (valorConsultaNomeFaltas == null) {
            valorConsultaNomeFaltas = "";
        }
        return valorConsultaNomeFaltas;
    }

    public void setValorConsultaNomeFaltas(String valorConsultaNomeFaltas) {
        this.valorConsultaNomeFaltas = valorConsultaNomeFaltas;
    }

    public List getListaRelatorio() {
        if (listaRelatorio == null) {
            listaRelatorio = new ArrayList();
        }
        return listaRelatorio;
    }

    public void setListaRelatorio(List listaRelatorio) {
        this.listaRelatorio = listaRelatorio;
    }

    public Boolean getAbrirPopupDownload() {
        if (abrirPopupDownload == null) {
            abrirPopupDownload = false;
        }
        return abrirPopupDownload;
    }

    public void setAbrirPopupDownload(Boolean abrirPopupDownload) {
        this.abrirPopupDownload = abrirPopupDownload;
    }

    public FecharMetaVO getFecharMetaJustificativa() {
        if (fecharMetaJustificativa == null) {
            fecharMetaJustificativa = new FecharMetaVO();
        }
        return fecharMetaJustificativa;
    }

    public void setFecharMetaJustificativa(FecharMetaVO fecharMetaJustificativa) {
        this.fecharMetaJustificativa = fecharMetaJustificativa;
    }

    public List getListaConsultaFechamentoDia() {
        if (listaConsultaFechamentoDia == null) {
            listaConsultaFechamentoDia = new ArrayList();
        }
        return listaConsultaFechamentoDia;
    }

    public void setListaConsultaFechamentoDia(List listaConsultaFechamentoDia) {
        this.listaConsultaFechamentoDia = listaConsultaFechamentoDia;
    }

    public Date getDataConsultaFechamentoDia() {
        if (dataConsultaFechamentoDia == null) {
            dataConsultaFechamentoDia = negocio.comuns.utilitarias.Calendario.hoje();
        }
        return dataConsultaFechamentoDia;
    }

    public void setDataConsultaFechamentoDia(Date dataConsultaFechamentoDia) {
        this.dataConsultaFechamentoDia = dataConsultaFechamentoDia;
    }

    public Boolean getApresentarBotoesGravarAdicionarJustificativa() {
        if (apresentarBotoesGravarAdicionarJustificativa == null) {
            apresentarBotoesGravarAdicionarJustificativa = true;
        }
        return apresentarBotoesGravarAdicionarJustificativa;
    }

    public void setApresentarBotoesGravarAdicionarJustificativa(Boolean apresentarBotoesGravarAdicionarJustificativa) {
        this.apresentarBotoesGravarAdicionarJustificativa = apresentarBotoesGravarAdicionarJustificativa;
    }

    public Boolean getApresentarBotaoBuscar() {
        if (apresentarBotaoBuscar == null) {
            apresentarBotaoBuscar = true;
        }
        return apresentarBotaoBuscar;
    }

    public void setApresentarBotaoBuscar(Boolean apresentarBotaoBuscar) {
        this.apresentarBotaoBuscar = apresentarBotaoBuscar;
    }

    public Date getDataFechamentoDia() {
        if (dataFechamentoDia == null) {
            dataFechamentoDia = negocio.comuns.utilitarias.Calendario.hoje();
        }
        return dataFechamentoDia;
    }

    public String getDataFechamentoDia_Apresentar() {
        dataFechamentoDia = negocio.comuns.utilitarias.Calendario.hoje();
        return Uteis.getData(dataFechamentoDia);
    }

    public void setDataFechamentoDia(Date dataFechamentoDia) {
        this.dataFechamentoDia = dataFechamentoDia;
    }

    public FecharMetaVO getFecharMetaFaturamentoVO() {
        if (fecharMetaFaturamentoVO == null) {
            fecharMetaFaturamentoVO = new FecharMetaVO();
        }
        return fecharMetaFaturamentoVO;
    }

    public void setFecharMetaFaturamentoVO(FecharMetaVO fecharMetaFaturamentoVO) {
        this.fecharMetaFaturamentoVO = fecharMetaFaturamentoVO;
    }

    public String getValorConsultaNomeFaturamento() {
        if (valorConsultaNomeFaturamento == null) {
            valorConsultaNomeFaturamento = "";
        }
        return valorConsultaNomeFaturamento;
    }

    public void setValorConsultaNomeFaturamento(String valorConsultaNomeFaturamento) {
        this.valorConsultaNomeFaturamento = valorConsultaNomeFaturamento;
    }

    public FecharMetaVO getFecharMetaVendasVO() {
        if (fecharMetaVendasVO == null) {
            fecharMetaVendasVO = new FecharMetaVO();
        }
        return fecharMetaVendasVO;
    }

    public void setFecharMetaVendasVO(FecharMetaVO fecharMetaVendasVO) {
        this.fecharMetaVendasVO = fecharMetaVendasVO;
    }

    public String getValorConsultaNomeVendas() {
        if (valorConsultaNomeVendas == null) {
            valorConsultaNomeVendas = "";
        }
        return valorConsultaNomeVendas;
    }

    public void setValorConsultaNomeVendas(String valorConsultaNomeVendas) {
        this.valorConsultaNomeVendas = valorConsultaNomeVendas;
    }

    public Boolean getMetaEmAberto() {
        if (metaEmAberto == null) {
            metaEmAberto = true;
        }
        return metaEmAberto;
    }

    public void setMetaEmAberto(Boolean metaEmAberto) {
        this.metaEmAberto = metaEmAberto;
    }

    public Boolean getApresentarTelaFormularioFechamentoDia() {
        if (apresentarTelaFormularioFechamentoDia == null) {
            apresentarTelaFormularioFechamentoDia = false;
        }
        return apresentarTelaFormularioFechamentoDia;
    }

    public void setApresentarTelaFormularioFechamentoDia(Boolean apresentarTelaFormularioFechamentoDia) {
        this.apresentarTelaFormularioFechamentoDia = apresentarTelaFormularioFechamentoDia;
    }

    public AberturaMetaVO getFecharAbertura() {
        return fecharAbertura;
    }

    public void setFecharAbertura(AberturaMetaVO fecharAbertura) {
        this.fecharAbertura = fecharAbertura;
    }

    public ClienteVO getClienteVO() {
        if (clienteVO == null) {
            clienteVO = new ClienteVO();
        }
        return clienteVO;
    }

    public void setClienteVO(ClienteVO clienteVO) {
        this.clienteVO = clienteVO;
    }

    public Boolean getExibirPaginacao() {
        return (this.getListaConsultaFechamentoDia().size() > 1);
    }

    public Boolean getAberturaRetroativa() {
        if (getAberturaMetaVO() == null || getAberturaMetaVO().getDia() == null){
            return false;
        }
        return Calendario.menor(getAberturaMetaVO().getDia(), Calendario.hoje());
    }

    public void setAbrirMalaDireta(Boolean abrirMalaDireta) {
        this.abrirMalaDireta = abrirMalaDireta;
    }

    public Boolean getAbrirMalaDireta() {
        if (abrirMalaDireta == null) {
            abrirMalaDireta = Boolean.TRUE;
        }
        return abrirMalaDireta;
    }

    public void setCodigoEmpresaConsulta(Integer codigoEmpresaConsulta) {
        this.codigoEmpresaConsulta = codigoEmpresaConsulta;
    }

    public Integer getCodigoEmpresaConsulta() {
        return codigoEmpresaConsulta;
    }

    public List<SelectItem> getListaSelectItemEmpresa() throws Exception {
        if (listaSelectItemEmpresa == null) {
            montarListaSelectItemEmpresa();
        }
        return listaSelectItemEmpresa;
    }

    public void setListaSelectItemEmpresa(List<SelectItem> listaSelectItemEmpresa) {
        this.listaSelectItemEmpresa = listaSelectItemEmpresa;

    }

    /**
     * Responsável por obter o recibo, anexar no email e enviar
     *
     * <AUTHOR> 09/05/2011
     */
    private void enviarEmail() throws Exception {
        StringBuilder texto = new StringBuilder();
        String nomeEmpresa = getFacade().getEmpresa().consultarPorChavePrimaria(getAberturaMetaVO().getEmpresaVO().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS).getNome();
        texto.append("Fechamento da meta do dia ").append(getAberturaMetaVO().getDia_Apresentar()).append(", do(a) colaborador(a) ");
        texto.append(getAberturaMetaVO().getColaboradorResponsavel().getNome()).append(", da empresa ").append(nomeEmpresa).append(", em anexo.");

        setAberturaMeta(getFacade().getAberturaMeta().consultarPorChavePrimaria(getAberturaMetaVO().getCodigo(), Uteis.NIVELMONTARDADOS_TODOS));
        getAberturaMeta().setListaFechametoDia(new ArrayList<FecharMetaVO>());

        for (FecharMetaVO meta : getAberturaMeta().getFecharMetaVosVenda()) {
            meta.setAberturaMetaVO(getAberturaMetaVO());
            getAberturaMeta().getListaFechametoDia().add(meta);
        }
        for (FecharMetaVO meta : getAberturaMeta().getFecharMetaVosRetencao()) {
            getAberturaMeta().getListaFechametoDia().add(meta);
        }
        for (FecharMetaVO meta : getAberturaMeta().getFecharMetaVosEstudio()) {
            getAberturaMeta().getListaFechametoDia().add(meta);
        }

        Ordenacao.ordenarLista(getAberturaMeta().getListaFechametoDia(), "ordem");
        imprimirRelatorio(getEmpresaLogado(), getAberturaMeta().getListaFechametoDia(), "Relatório Fechamento Dia", "", "FechamentoDiaAberturaMetaRel", getAberturaMeta().getTotalMetaDia());

        List<ConfiguracaoEmailFechamentoMetaVO> emailsEnviarFechamento = getFacade().getConfiguracaoSistemaCRM().consultarEmailsEnviarFechamento(getAberturaMetaVO().getEmpresaVO());
        ConfiguracaoSistemaCRMVO configuracaoSistemaCRMVO = getConfiguracaoSMTPNoReply();
        for (ConfiguracaoEmailFechamentoMetaVO emailMeta : emailsEnviarFechamento) {
            // obter o relatorio
            String nomePDF = Uteis.obterCaminhoWeb() + "/relatorio/" + getNomeArquivoRelatorioGeradoAgora();
            File arquivo = new File(nomePDF);
            // obter configurações do envio de email
           
            UteisEmail email = new UteisEmail();
            // assunto do email será "RECIBO"
            email.novo("FECHAMENTO DE META", configuracaoSistemaCRMVO);
            // remetente é o usuario logado
            email.setRemetente(getUsuarioLogado());
            email = email.addAnexo(getNomeArquivoRelatorioGeradoAgora(), arquivo);
            email.enviarEmail(emailMeta.getEmail(), emailMeta.getEmail(), texto.toString(), nomeEmpresa);
        }
    }

    private void enviarEmail(AberturaMetaVO aberturaMetaVO, EmpresaVO empresaVO,  ConfiguracaoSistemaCRMVO configuracaoSistemaCRMVO, List<ConfiguracaoEmailFechamentoMetaVO> emailsEnviarFechamento) throws Exception {
        String nomeEmpresa = empresaVO.getNome();
        AberturaMetaVO aberturaMeta = getFacade().getAberturaMeta().consultarPorChavePrimaria(aberturaMetaVO.getCodigo(), Uteis.NIVELMONTARDADOS_TODOS);
        aberturaMeta.setListaFechametoDia(new ArrayList<FecharMetaVO>());

        for (FecharMetaVO meta : aberturaMeta.getFecharMetaVosVenda()) {
            meta.setAberturaMetaVO(aberturaMetaVO);
            // Atualizar itens meta
            meta.setMeta(Double.valueOf(getFacade().getFecharMetaDetalhado().consultarPorCodigoFecharMeta(meta.getCodigo(), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA).size()));
            aberturaMeta.getListaFechametoDia().add(meta);
        }
        for (FecharMetaVO meta : aberturaMeta.getFecharMetaVosRetencao()) {
            // Atualizar itens meta
            meta.setMeta(Double.valueOf(getFacade().getFecharMetaDetalhado().consultarPorCodigoFecharMeta(meta.getCodigo(), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA).size()));
            aberturaMeta.getListaFechametoDia().add(meta);
        }
        for (FecharMetaVO meta : aberturaMeta.getFecharMetaVosEstudio()) {
            // Atualizar itens meta
            meta.setMeta(Double.valueOf(getFacade().getFecharMetaDetalhado().consultarPorCodigoFecharMeta(meta.getCodigo(), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA).size()));
            aberturaMeta.getListaFechametoDia().add(meta);
        }

        boolean existeMetaEnviar = false;
        for (FecharMetaVO meta : aberturaMeta.getListaFechametoDia()) {
            if (meta.getMeta() > 0 || meta.getMetaAtingida() > 0) {
                existeMetaEnviar = true;
                break;
            }
        }

        if (existeMetaEnviar) {
            Ordenacao.ordenarLista(aberturaMeta.getListaFechametoDia(), "ordem");

            for (ConfiguracaoEmailFechamentoMetaVO emailMeta : emailsEnviarFechamento) {
                Uteis.logar(null, "Enviando email para " + emailMeta.getEmail() + " da empresa " + empresaVO.getCodigo());
                String[] emails = new String[1];
                emails[0] = emailMeta.getEmail();

                MsgTO msg = new MsgTO(textoEmail(aberturaMeta.getListaFechametoDia(),
                        aberturaMeta.getColaboradorResponsavel().getNome(),
                        aberturaMeta.getResponsavelCadastro().getNome(),
                        aberturaMeta.getDia_Apresentar(),
                        aberturaMeta.getTotalMetaDia().toString(), nomeEmpresa), "FECHAMENTO DE META: " + aberturaMeta.getColaboradorResponsavel().getNome(), nomeEmpresa, true,
                        configuracaoSistemaCRMVO, false, emails);

                UteisValidacao.enfileirarEmail(msg);
            }
        }
    }

    private StringBuffer textoEmail(List<FecharMetaVO> fecharMetas, String responsavel, String cadastro, String data, String totalMetaDia, String nomeEmpresa) {

        String dataEnvio = Uteis.getDataAtualAplicandoFormatacao("dd/MM/yyyy");
        String horaEnvio = Uteis.getDataAtualAplicandoFormatacao("HH:mm");
        StringBuffer email = new StringBuffer();
        email.append("<html>\n");
        email.append("<head>\n");
        email.append("    <meta http-equiv=\"Content-Type\" content=\"text/html; charset=utf-8\">\n");
        email.append("    <title>Documento sem título</title>\n");
        email.append("</head>\n");
        email.append("\n");
        email.append("<body style=\"padding: 40px 30px; font-family: Arial;\">\n");
        email.append("<div id=\"cabecalho\">\n");
        email.append("    <h1 style=\"font-size: 16px; text-transform: uppercase; display: inline-block; width: 50%;\">Relatório de fechamento do dia</h1>\n");
        email.append("    <div style=\"display: inline-block; width: 40%; float: right; font-size: 16px; text-align: right; padding-top: 10px;\">").append(dataEnvio).append(" <span style=\"font-size:12px\">").append(horaEnvio).append("</span></div>\n");
        email.append("    <hr>\n");
        email.append("    <div id=\"dadosRel\">\n");
        email.append("        <div id=\"colunaA\" style=\"width:49.5%; display: inline-block; font-weight:bold\">\n");
        email.append("            <div>Empresa: <span style=\"font-weight: normal\">").append(nomeEmpresa).append("</span></div>\n");
        email.append("            <div>Responsável: <span style=\"font-weight: normal\">").append(responsavel).append("</span></div>\n");
        email.append("            <div>Abertura meta: <span style=\"font-weight: normal\">").append(data).append("</span></div>\n");
        email.append("        </div>\n");
        email.append("        <div id=\"colunaB\" style=\"width:49.5%; display: inline-block; font-weight:bold\">\n");
        email.append("            <div>Cadastro: <span style=\"font-weight: normal\">").append(cadastro).append("</span></div>\n");
        email.append("            <div>&nbsp;</div>\n");
        email.append("        </div>\n");
        email.append("    </div>\n");
        email.append("    <hr>\n");
        email.append("    <table style=\"width: 100%;\" cellpadding=\"0\" cellspacing=\"0\">\n");
        email.append("        <thead>\n");
        email.append("        <tr>\n");
        email.append("            <th style=\"padding: 10px 5px;color:#717175;font-size:14px;text-align:left; font-weight: normal;border-bottom: 1px solid #aaa;\">Identificador Meta</th>\n");
        email.append("            <th style=\"padding: 10px 5px;color:#717175;font-size:14px;text-align:left; font-weight: normal;border-bottom: 1px solid #aaa;\">Meta</th>\n");
        email.append("            <th style=\"padding: 10px 5px;color:#717175;font-size:14px;text-align:left; font-weight: normal;border-bottom: 1px solid #aaa;\">Resultado</th>\n");
        email.append("            <th style=\"padding: 10px 5px;color:#717175;font-size:14px;text-align:left; font-weight: normal;border-bottom: 1px solid #aaa;\">Percentual %</th>\n");
        email.append("        </tr>\n");
        email.append("        </thead>\n");
        email.append("        <tbody>\n");
        for (int i = 0; i < fecharMetas.size(); i++) {
            if (i % 2 == 0) {
                email.append("        <tr>\n");
            } else {
                email.append("        <tr style=\"background-color: #ecedef\">\n");
            }
            FecharMetaVO fecharMetaVO = fecharMetas.get(i);
            email.append("            <td style=\"padding: 10px 5px;font-size:14px;text-align:left; font-weight: normal;border-bottom: 1px solid #aaa;\">").append(fecharMetaVO.getFase().getDescricao()).append("</td>\n");
            email.append("            <td style=\"padding: 10px 5px;font-size:14px;text-align:left; font-weight: normal;border-bottom: 1px solid #aaa;\">").append(fecharMetaVO.getMeta().intValue()).append("</td>\n");
            email.append("            <td style=\"padding: 10px 5px;font-size:14px;text-align:left; font-weight: normal;border-bottom: 1px solid #aaa;\">").append(fecharMetaVO.getMetaAtingida().intValue()).append("</td>\n");
            email.append("            <td style=\"padding: 10px 5px;font-size:14px;text-align:left; font-weight: normal;border-bottom: 1px solid #aaa;\">").append(fecharMetaVO.getPorcentagemApresentar()).append("%</td>\n");
            email.append("        </tr>\n");
        }
        email.append("        </tbody>\n");
        email.append("    </table>\n");
        email.append("    <div>\n");
        email.append("        <div style=\"font-size: 14px; display: inline-block; width: 59%; padding-top: 10px;\">\n");
        email.append("            Justificativa: Meta fechada automáticamente pelo sistema\n");
        email.append("        </div>\n");
        email.append("        <div style=\"display: inline-block; width: 40%; float: right; font-size: 14px; text-align: right; padding-top: 10px; font-weight: bold\">\n");
        email.append("            PERCENTUAL DE META DIÁRIA: <span style=\"font-size:14px; font-weight: normal\">").append(Uteis.arredondarForcando2CasasDecimais(Double.parseDouble(totalMetaDia))).append("%</span>\n");
        email.append("        </div>\n");
        email.append("    </div>\n");
        email.append("    <div>\n");
        email.append("        <div style=\"display: inline-block; width: 90%; float: left; font-size: 14px; text-align: left; padding-top: 10px; font-weight: bold\">\n");
        email.append("            Obs: <span style=\"font-size:14px; font-weight: normal\">A meta de Indicações não entra no total das metas</span>\n");
        email.append("        </div>\n");
        email.append("    </div>\n");
        email.append("</div>\n");
        email.append("</body>\n");
        email.append("</html>");
        return email;
    }

    public void setFecharMetaVencidosVO(FecharMetaVO fecharMetaVencidosVO) {
        this.fecharMetaVencidosVO = fecharMetaVencidosVO;
    }
    public FecharMetaVO getFecharMetaVencidosVO() {
        if (fecharMetaVencidosVO == null) {
            fecharMetaVencidosVO = new FecharMetaVO();
        }
        return fecharMetaVencidosVO;
    }

    public void setFecharMetaConversaoAgendadosVO(FecharMetaVO fecharMetaConversaoAgendadosVO) {
        this.fecharMetaConversaoAgendadosVO = fecharMetaConversaoAgendadosVO;
    }

    public FecharMetaVO getFecharMetaConversaoAgendadosVO() {
        if (fecharMetaConversaoAgendadosVO == null) {
            fecharMetaConversaoAgendadosVO = new FecharMetaVO();
        }
        return fecharMetaConversaoAgendadosVO;
    }

    public void setValorConsultaNomeVencidos(String valorConsultaNomeVencidos) {
        this.valorConsultaNomeVencidos = valorConsultaNomeVencidos;
    }

    public String getValorConsultaNomeVencidos() {
        return valorConsultaNomeVencidos;
    }

    public void consultarFecharMetaDetalhadoVencidosPorHistorico() {
        try {
            validarDados();
            getFecharMetaVencidosVO().setListaHistoricoContatoHistorico(getFacade().getFecharMetaDetalhado().consultarFecharMetaDetalhadosHistorico("VE", getValorConsultaNomeVencidos(), getDataInicio(), getDataTermino(), getAberturaMetaVO(), Uteis.NIVELMONTARDADOS_METAAGENDAMENTODETALHADA, true));
            getFacade().getFecharMetaDetalhado().setarAtributoEspecificoFecharMetaDetalhados(getFecharMetaVencidosVO().getListaHistoricoContatoHistorico(), "VE");
            setMensagem("");
            setMensagemID("msg_dados_consultados");
            setMensagemDetalhada("", "");
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public void executarAberturaPopupEmailColetivoVencidos() {
        try {
            validarPermissao("RealizarContato", "7.23 - Realizar Contato", getUsuarioLogado());
            this.setAbrirMalaDireta(true);
            setMalaDiretaVO(new MalaDiretaVO());
            getMalaDiretaVO().setMeioDeEnvioEnum(MeioEnvio.EMAIL);
            getFacade().getFecharMetaDetalhado().inicializarDadosEnviarEmailColetivo(fecharMetaVencidosVO, malaDiretaVO, getUsuarioLogado());
            setMensagemDetalhada("", "");
            setMensagem("");
            setMensagemID("");
        } catch (Exception e) {
            this.setAbrirMalaDireta(false);
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public void executarAberturaPopupSMSColetivoVencidos() {
        try {
            validarPermissao("RealizarContato", "7.23 - Realizar Contato", getUsuarioLogado());
            this.setAbrirMalaDireta(true);
            setMalaDiretaVO(new MalaDiretaVO());
            getMalaDiretaVO().setMeioDeEnvioEnum(MeioEnvio.SMS);
            getFacade().getFecharMetaDetalhado().inicializarDadosEnviarSMSColetivo(fecharMetaVencidosVO, malaDiretaVO, getUsuarioLogado());
            setMensagemDetalhada("", "");
            setMensagem("");
            setMensagemID("");
        } catch (Exception e) {
            this.setAbrirMalaDireta(false);
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public void executarAberturaPopupEmailColetivoLigacaoAgendados() {
        try {
            validarPermissao("RealizarContato", "7.23 - Realizar Contato", getUsuarioLogado());
            this.setAbrirMalaDireta(true);
            getFacade().getFecharMetaDetalhado().inicializarDadosEnviarEmailColetivo(fecharMetaLigacaoAgendadosVO, malaDiretaVO, getUsuarioLogado());
            setMensagemDetalhada("", "");
            setMensagem("");
            setMensagemID("msg_dados_consultados");
        } catch (Exception e) {
            this.setAbrirMalaDireta(false);
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public void executarAberturaPopupSMSColetivoLigacaoAgendados() {
        try {
            validarPermissao("RealizarContato", "7.23 - Realizar Contato", getUsuarioLogado());
            this.setAbrirMalaDireta(true);
            setMalaDiretaVO(new MalaDiretaVO());
            getMalaDiretaVO().setMeioDeEnvioEnum(MeioEnvio.SMS);
            getFacade().getFecharMetaDetalhado().inicializarDadosEnviarSMSColetivo(fecharMetaLigacaoAgendadosVO, malaDiretaVO, getUsuarioLogado());
            setMensagemDetalhada("", "");
            setMensagem("");
            setMensagemID("msg_dados_consultados");
        } catch (Exception e) {
            this.setAbrirMalaDireta(false);
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public void executarImpressaoVencidos() throws Exception {
        if (getFecharMetaVencidosVO().getAbaSelecionada().equals("HJ")) {
            getFecharMetaVencidosVO().setFecharMetaDetalhadoVOs(getFacade().getFecharMetaDetalhado().consultarFecharMetaDetalhado(aberturaMetaVO, getFecharMetaVencidosVO().getIdentificadorMeta(), true, getEmpresaLogado().getCodigo()));
            getFecharMetaVencidosVO().setTotalApresentarRelatorio(new Double(getFecharMetaVencidosVO().getTotalizadorHoje()));
            imprimirRelatorio(getEmpresaLogado(), getFecharMetaVencidosVO().getFecharMetaDetalhadoVOs(), "Relatório Meta Detalhado Vencidos", "Período: " + Uteis.getData(getAberturaMetaVO().getDia()), "MetaDetalhadoClienteRel", getFecharMetaVencidosVO().getTotalApresentarRelatorio());
        } else if (getFecharMetaVencidosVO().getAbaSelecionada().equals("HI")) {
            getFecharMetaVencidosVO().setTotalApresentarRelatorio(new Double(getFecharMetaVencidosVO().getTotalizadorHistorico()));
            imprimirRelatorio(getEmpresaLogado(), getFecharMetaVencidosVO().getListaHistoricoContatoHistorico(), "Relatório Meta Vencidos", "Período: " + Uteis.getData(getDataInicio()) + " até " + Uteis.getData(getDataTermino()), "MetaDetalhadoClienteRel", getFecharMetaVencidosVO().getTotalApresentarRelatorio());

        }
    }

    public void setValorConsultaNomeConversaoAgendados(String valorConsultaNomeConversaoAgendados) {
        this.valorConsultaNomeConversaoAgendados = valorConsultaNomeConversaoAgendados;
    }

    public String getValorConsultaNomeConversaoAgendados() {
        if (valorConsultaNomeConversaoAgendados == null) {
            valorConsultaNomeConversaoAgendados = "";
        }
        return valorConsultaNomeConversaoAgendados;
    }

    public FecharMetaVO getFecharMetaLigacaoAgendadosVO() {
        if (fecharMetaLigacaoAgendadosVO == null) {
            fecharMetaLigacaoAgendadosVO = new FecharMetaVO();
        }
        return fecharMetaLigacaoAgendadosVO;
    }

    public void setFecharMetaLigacaoAgendadosVO(FecharMetaVO fecharMetaLigacaoAgendadosVO) {
        this.fecharMetaLigacaoAgendadosVO = fecharMetaLigacaoAgendadosVO;
    }

    public String getValorConsultaNomeLigacaoAgendados() {
        return valorConsultaNomeLigacaoAgendados;
    }

    public void setValorConsultaNomeLigacaoAgendados(String valorConsultaNomeLigacaoAgendados) {
        this.valorConsultaNomeLigacaoAgendados = valorConsultaNomeLigacaoAgendados;
    }

    public void setLabelTela(String labelTela) {
        this.labelTela = labelTela;
    }

    public String getLabelTela() {
        return labelTela;
    }

    public void setFecharMetaGenerico(FecharMetaVO fecharMetaGenerico) {
        this.fecharMetaGenerico = fecharMetaGenerico;
    }

    public FecharMetaVO getFecharMetaGenerico() {
        return fecharMetaGenerico;
    }

    public void consultarFecharMetaDetalhadoGenerico() throws Exception {
        FecharMetaVO obj = (FecharMetaVO) context().getExternalContext().getRequestMap().get("fecharMeta");
        setFecharMetaGenerico(obj);
        getFacade().getFecharMeta().inicializarDadosParaTelaMetaDetalhando(getFecharMetaGenerico());
        inicializarDadosAbaHojeGenerico();
        limparTotais(getFecharMetaGenerico());
        setLabelTela(getFecharMetaGenerico().getFase().getDescricao());
    }

    public void inicializarDadosAbaHojeGenerico() {
        try {
            setApresentarFiltroPesquisaHistorico(false);
            getFecharMetaGenerico().setAbaSelecionada("HJ");
            getFecharMetaGenerico().setFecharMetaDetalhadoVOs(getFacade().getFecharMetaDetalhado().consultarFecharMetaDetalhado(aberturaMetaVO, getFecharMetaGenerico().getIdentificadorMeta(), true, getEmpresaLogado().getCodigo()));
            limparMsg();
            setMensagemID("msg_dados_consultados");
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public void executarSelecaoTodosFecharMeta() {
        try {
            getFacade().getAberturaMeta().executarSelecaoTodosFecharMetaDetalhados(getFecharMetaGenerico());
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public void executarAberturaPopupEmailColetivo() {
        try {
            validarPermissao("RealizarContato", "7.23 - Realizar Contato", getUsuarioLogado());
            this.setAbrirMalaDireta(true);
            setMalaDiretaVO(new MalaDiretaVO());
            getMalaDiretaVO().setMeioDeEnvioEnum(MeioEnvio.EMAIL);
            getFacade().getFecharMetaDetalhado().inicializarDadosEnviarEmailColetivo(fecharMetaGenerico, malaDiretaVO, getUsuarioLogado());
            limparMsg();
        } catch (Exception e) {
            this.setAbrirMalaDireta(false);
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public void executarAberturaPopupSMSColetivo() {
        try {
            validarPermissao("RealizarContato", "7.23 - Realizar Contato", getUsuarioLogado());
            this.setAbrirMalaDireta(true);
            setMalaDiretaVO(new MalaDiretaVO());
            getMalaDiretaVO().setMeioDeEnvioEnum(MeioEnvio.SMS);
            getFacade().getFecharMetaDetalhado().inicializarDadosEnviarSMSColetivo(fecharMetaGenerico, malaDiretaVO, getUsuarioLogado());
            limparMsg();
        } catch (Exception e) {
            this.setAbrirMalaDireta(false);
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public void executarImpressao() throws Exception {
        if (getFecharMetaGenerico().getAbaSelecionada().equals("HJ")) {
            getFecharMetaGenerico().setFecharMetaDetalhadoVOs(getFacade().getFecharMetaDetalhado().consultarFecharMetaDetalhado(aberturaMetaVO, getFecharMetaGenerico().getIdentificadorMeta(), true, getEmpresaLogado().getCodigo()));
            getFecharMetaGenerico().setTotalApresentarRelatorio(new Double(getFecharMetaGenerico().getTotalizadorHoje()));
            imprimirRelatorio(getEmpresaLogado(), getFecharMetaGenerico().getFecharMetaDetalhadoVOs(), "Relatório Meta Detalhado " + getFecharMetaGenerico().getFase().getDescricao(), "Período: " + Uteis.getData(getAberturaMetaVO().getDia()), "MetaDetalhadoClienteRel", getFecharMetaGenerico().getTotalApresentarRelatorio());
        } else if (getFecharMetaGenerico().getAbaSelecionada().equals("HI")) {
            getFecharMetaGenerico().setTotalApresentarRelatorio(new Double(getFecharMetaGenerico().getTotalizadorHistorico()));
            imprimirRelatorio(getEmpresaLogado(), getFecharMetaGenerico().getListaHistoricoContatoHistorico(), "Relatório Meta " + getFecharMetaGenerico().getFase().getDescricao(), "Período: " + Uteis.getData(getDataInicio()) + " até " + Uteis.getData(getDataTermino()), "MetaDetalhadoClienteRel", getFecharMetaGenerico().getTotalApresentarRelatorio());

        }
    }

    public void executarImpressaoExcel() {
        try {
            setAbrirPopupDownload(true);
            GeradorRelatorio obj = new GeradorRelatorio();
            if (getFecharMetaGenerico().getAbaSelecionada().equals("HJ")) {
                getFecharMetaGenerico().setFecharMetaDetalhadoVOs(getFacade().getFecharMetaDetalhado().consultarFecharMetaDetalhado(aberturaMetaVO, getFecharMetaGenerico().getIdentificadorMeta(), true, getEmpresaLogado().getCodigo()));
                obj.montarRelatorio("OBJETO", "EXCEL", "MetaDetalhadoExcelClienteRel", AberturaMetaVO.getIsDesignIReportRelatorioExcel("MetaDetalhadoExcelClienteRel"), getFecharMetaGenerico().getFecharMetaDetalhadoVOs(), new HashMap());
            } else if (getFecharMetaGenerico().getAbaSelecionada().equals("HI")) {
                obj.montarRelatorio("OBJETO", "EXCEL", "MetaDetalhadoExcelClienteRel", AberturaMetaVO.getIsDesignIReportRelatorioExcel("MetaDetalhadoExcelClienteRel"), getFecharMetaGenerico().getListaHistoricoContatoHistorico(), new HashMap());
            }
        } catch (Exception e) {
            setAbrirPopupDownload(false);
            setMensagemDetalhada("msg_erro", e.getMessage());
        }

    }

    public void setValorConsultaNome(String valorConsultaNome) {
        this.valorConsultaNome = valorConsultaNome;
    }

    public String getValorConsultaNome() {
        return valorConsultaNome;
    }

    public void consultarFecharMetaDetalhadoPorHistorico() {
        try {
            validarDados();
            getFecharMetaGenerico().setListaHistoricoContatoHistorico(getFacade().getFecharMetaDetalhado().consultarFecharMetaDetalhadosHistorico(getFecharMetaGenerico().getFase().getSigla(), getValorConsultaNome(), getDataInicio(), getDataTermino(), getAberturaMetaVO(), Uteis.NIVELMONTARDADOS_METAAGENDAMENTODETALHADA, true));
            getFacade().getFecharMetaDetalhado().setarAtributoEspecificoFecharMetaDetalhados(getFecharMetaGenerico().getListaHistoricoContatoHistorico(), getFecharMetaGenerico().getFase().getSigla());
            limparMsg();
            setMensagemID("msg_dados_consultados");
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public void executarTotalizadorSelecionadoHoje() {
        try {
            getFecharMetaVendasVO().setTotalizadorSelecionadoHoje(getFacade().getFecharMeta().executarTotalizadorSelecionado(getFecharMetaGenerico().getFecharMetaDetalhadoVOs(), getFecharMetaGenerico().getTotalizadorSelecionadoHoje()));
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public void inicializarDadosAbaHistorico() {
        try {
            setApresentarFiltroPesquisaHistorico(true);
            getFecharMetaGenerico().setAbaSelecionada("HI");
            getFecharMetaGenerico().setListaHistoricoContatoHistorico(new ArrayList());
            setMensagem("");
            setMensagemID("msg_dados_consultados");
            setMensagemDetalhada("", "");
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public void executarSelecaoTodosFecharMetaDetalhados() {
        try {
            getFacade().getAberturaMeta().executarSelecaoTodosFecharMetaDetalhados(getFecharMetaGenerico());
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public void atualizarFecharMetaDetalhadaHistorico() throws Exception {
        getFacade().getFecharMeta().inicializarDadosParaTelaMetaDetalhando(getFecharMetaGenerico());
        inicializarDadosAbaHojeGenerico();
        inicializarDadosAbaHistorico();
        limparTotais(getFecharMetaGenerico());
    }

    public void fecharMetas(Date data) throws Exception {
        Uteis.logar(null, "Iniciando Fechamento das metas (CRM) do dia " + data.toString() + " em " + Calendario.hoje().toString());
        List<EmpresaVO> empresas = getFacade().getEmpresa().consultarTodas(true,Uteis.NIVELMONTARDADOS_DADOSBASICOS);
       
        for (int i = 0; i < empresas.size(); i++) {
            System.out.println("Fechando " + (i + 1) + "ª empresa de " + empresas.size() + " empresas");
            EmpresaVO empresa = empresas.get(i);
//            List<ConfiguracaoEmailFechamentoMetaVO> emailsEnviarFechamento = getFacade().getConfiguracaoSistemaCRM().consultarEmailsEnviarFechamento(empresa);
            List<AberturaMetaVO> metasAbertas = getFacade().getAberturaMeta().consultarMetasAbertaPorDia(data, empresa.getCodigo(), Uteis.NIVELMONTARDADOS_TODOS);
            for (int j = 0; j < metasAbertas.size(); j++) {
                System.out.println("Fechando " + (j + 1) + "ª meta de " + metasAbertas.size() + " metas");
                AberturaMetaVO aberturaMetaVO = metasAbertas.get(j);
                fechamentoAberturaMeta(aberturaMetaVO);
//                try {
//                    if(configuracaoSistemaCRMVO.validarInformacoesBasicasEmail()){
//                        enviarEmail(aberturaMetaVO, empresa, configuracaoSistemaCRMVO,emailsEnviarFechamento);
//                    }
//                } catch (Exception ex) {
//                    Logger.getLogger(this.getClass().getName()).log(Level.INFO, "Problemas ao enviar email de fechamento de metas: {0}", new Object[]{ex.getMessage()});
//                }
            }
        }
        Uteis.logar(null, "Terminando Fechamento das metas (CRM) do dia " + data.toString() + " em " + Calendario.hoje().toString());
    }

    public void abrirMetas(Date data) throws Exception {
        // Atualiza Status das Metas Extras com a data de vigência vencida.
        getFacade().getMalaDireta().atualizaStatusCRMMetaExtra(data);

        System.out.println("Iniciando abertura das metas (CRM) do dia " + data.toString() + " em " + Calendario.hoje().toString());
        List<EmpresaVO> empresas = getFacade().getEmpresa().consultarTodas(true,Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        boolean usarEstudio = usaEstudio();
        String clientesObjecaoDefinitiva = getFacade().getHistoricoContato().consultarHistoricoContatoClienteObjecao(true, false, false, "OD");
        String passivosObjecaoDefinitiva = getFacade().getHistoricoContato().consultarHistoricoContatoClienteObjecao(false, true, false, "OD");
        String indicadosObjecaoDefinitiva = getFacade().getHistoricoContato().consultarHistoricoContatoClienteObjecao(false, false, true, "OD");
        String pessaoObjecaoDefinitiva = getFacade().getHistoricoContato().consultarHistoricoContatoPessoaObjecao();
        for (int i = 0; i < empresas.size(); i++) {
            System.out.println("Abrindo " + (i + 1) + "ª empresa de " + empresas.size() + " empresas");
            EmpresaVO empresa = empresas.get(i);
            ResultadoServicosVO resultadoMetas = new ResultadoServicosVO(ServicoEnum.METAS, "METAS DA EMPRESA " + empresa.getCodigo(), empresa.getCodigo());
            List<UsuarioVO> usuarios = getFacade().getUsuario().consultarUsuarioAberturaMeta(empresa.getCodigo(), false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            //Montando quarentena CRM, onde algumas metas deixam de ser abertas. Ex: Grupo de risco, Faltosos.
            QuarentenaVO quarentenaCRM = null;
            try {
                quarentenaCRM = getFacade().getQuarentena().obterAtiva(empresa.getCodigo());
            }catch (Exception ignore){}
            for (int j = 0; j < usuarios.size(); j++) {
                UsuarioVO usuario = usuarios.get(j);
                System.out.println("Abrindo " + (j + 1) + "ª meta de " + usuarios.size() + " metas. Usuario: "+usuario.getNome());
                try {

                        Date dataIni = Calendario.hoje();
                        this.setAberturaMetaVO(new AberturaMetaVO());
                        this.getAberturaMetaVO().setColaboradorResponsavel(usuario);
                        this.getAberturaMetaVO().setEmpresaVO(empresa);
                        this.getAberturaMetaVO().setDia(data);
                        this.getAberturaMetaVO().setCodigoClientesObjecaoDefinitiva(clientesObjecaoDefinitiva);
                        this.getAberturaMetaVO().setCodigoPassivosObjecaoDefinitiva(passivosObjecaoDefinitiva);
                        this.getAberturaMetaVO().setCodigoIndicadosObjecaoDefinitiva(indicadosObjecaoDefinitiva);
                        this.getAberturaMetaVO().setCodigoPessoaObjecaoDefinitiva(pessaoObjecaoDefinitiva);

                        //Calcular as metas;
                        temMetaAbertaProDia();
                        getFacade().getAberturaMeta().consultarPerfilAcessoColaborador(this.getAberturaMetaVO(), empresa);
                        getFacade().getAberturaMeta().executarDefinicaoDataParaCalcularAberturaDia(this.getAberturaMetaVO(), this.getAberturaMetaVO().getColaboradorResponsavel().getPermissaoAcessoMenuVO(), usarEstudio, quarentenaCRM);
                        //Preparar e gravar as metas;
                        getFacade().getAberturaMeta().gravar(this.getAberturaMetaVO(), empresa.getCodigo(), this.getAberturaMetaVO().getColaboradorResponsavel().getPermissaoAcessoMenuVO(), usarEstudio, false);
                        Uteis.calculaTempoExecucaoDaFuncionalidade(dataIni, "ABERTURA DA META");
                } catch (Exception e) {
                    String msgErro = e.getMessage() + " - Usuário: " + usuario.getNome();
                    Uteis.logar(null, msgErro);
                    resultadoMetas.getResultado().put(msgErro);
                }
            }
            getFacade().getResultadoServicos().gravarResultado(resultadoMetas);
        }

        System.out.println("Terminou abertura das metas (CRM) do dia " + data.toString() + " em " + Calendario.hoje().toString());
        clientesObjecaoDefinitiva = null;
        passivosObjecaoDefinitiva = null;
        indicadosObjecaoDefinitiva = null;
        pessaoObjecaoDefinitiva = null;
        
    }

    public void abrirMetasEspecifica(Date data, FasesCRMEnum fase) throws Exception {
        abrirMetasEspecifica(data, fase, false);
    }

    public void abrirMetasEspecifica(Date data, FasesCRMEnum fase, boolean agendadasAmanha) throws Exception {
        // Atualiza Status das Metas Extras com a data de vigência vencida.
        getFacade().getMalaDireta().atualizaStatusCRMMetaExtra(data);

        System.out.println("Iniciando abertura das metas (CRM) do dia " + data.toString() + " em " + Calendario.hoje().toString());
        List<EmpresaVO> empresas = getFacade().getEmpresa().consultarTodas(true,Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        boolean usarEstudio = usaEstudio();
        String clientesObjecaoDefinitiva = getFacade().getHistoricoContato().consultarHistoricoContatoClienteObjecao(true, false, false, "OD");
        String passivosObjecaoDefinitiva = getFacade().getHistoricoContato().consultarHistoricoContatoClienteObjecao(false, true, false, "OD");
        String indicadosObjecaoDefinitiva = getFacade().getHistoricoContato().consultarHistoricoContatoClienteObjecao(false, false, true, "OD");
        String pessaoObjecaoDefinitiva = getFacade().getHistoricoContato().consultarHistoricoContatoPessoaObjecao();
        for (int i = 0; i < empresas.size(); i++) {
            System.out.println("Abrindo " + (i + 1) + "ª empresa de " + empresas.size() + " empresas");
            EmpresaVO empresa = empresas.get(i);
            ResultadoServicosVO resultadoMetas = new ResultadoServicosVO(ServicoEnum.METAS, "METAS DA EMPRESA " + empresa.getCodigo(), empresa.getCodigo());
            List<UsuarioVO> usuarios = getFacade().getUsuario().consultarUsuarioAberturaMeta(empresa.getCodigo(), false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            //Montando quarentena CRM, onde algumas metas deixam de ser abertas. Ex: Grupo de risco, Faltosos.
            QuarentenaVO quarentenaCRM = null;
            try {
                quarentenaCRM = getFacade().getQuarentena().obterAtiva(empresa.getCodigo());
            }catch (Exception ignore){}
            for (int j = 0; j < usuarios.size(); j++) {
                UsuarioVO usuario = usuarios.get(j);
                System.out.println("Abrindo " + (j + 1) + "ª meta de " + usuarios.size() + " metas. Usuario: "+usuario.getNome());
                try {
                    Date dataIni = Calendario.hoje();
                    this.setAberturaMetaVO(getFacade().getAberturaMeta().consultar(usuario.getCodigo(), empresa.getCodigo(),data, Uteis.NIVELMONTARDADOS_DADOSBASICOS));
                    this.getAberturaMetaVO().setColaboradorResponsavel(usuario);
                    this.getAberturaMetaVO().setEmpresaVO(empresa);
                    this.getAberturaMetaVO().setFaseEspecifica(fase);

                    getFacade().getAberturaMeta().consultarPerfilAcessoColaborador(this.getAberturaMetaVO(), empresa);
                    getFacade().getAberturaMeta().executarDefinicaoDataParaCalcularAberturaDia(this.getAberturaMetaVO(), this.getAberturaMetaVO().getColaboradorResponsavel().getPermissaoAcessoMenuVO(), usarEstudio, quarentenaCRM);
                    //Preparar e gravar as metas;
                    if (agendadasAmanha){
                        getFacade().getAberturaMeta().gravarAgendamento(this.getAberturaMetaVO(), empresa.getCodigo(), this.getAberturaMetaVO().getColaboradorResponsavel().getPermissaoAcessoMenuVO(), usarEstudio, true);
                    }else {
                        getFacade().getAberturaMeta().gravar(this.getAberturaMetaVO(), empresa.getCodigo(), this.getAberturaMetaVO().getColaboradorResponsavel().getPermissaoAcessoMenuVO(), usarEstudio, false);
                    }

                    Uteis.calculaTempoExecucaoDaFuncionalidade(dataIni, "ABERTURA DA META");
                } catch (Exception e) {
                    String msgErro = e.getMessage() + " - Usuário: " + usuario.getNome();
                    Uteis.logar(null, msgErro);
                    resultadoMetas.getResultado().put(msgErro);
                }
            }
            getFacade().getResultadoServicos().gravarResultado(resultadoMetas);
        }

        System.out.println("Terminou abertura das metas (CRM) do dia " + data.toString() + " em " + Calendario.hoje().toString());
        clientesObjecaoDefinitiva = null;
        passivosObjecaoDefinitiva = null;
        indicadosObjecaoDefinitiva = null;
        pessaoObjecaoDefinitiva = null;

    }
    private boolean usaEstudio() throws Exception {
        if (!JSFUtilities.isJSFContext()) {
            return DAO.modulosHabilitados.contains("EST");
        } else {
            return isUsarEstudio();
        }
    }

    public static void main(String[] args) {
        try {
            Conexao.guardarConexaoForJ2SE(new DAO().obterConexaoEspecifica(args.length > 0 ? args[0] : "pacto7"));
            AberturaMetaControle control = new AberturaMetaControle();
            control.abrirMetas(Calendario.hoje());
            //control.fecharMetas(Calendario.hoje());
        } catch (Exception ex) {
            Logger.getLogger(AberturaMetaControle.class.getName()).log(Level.SEVERE, null, ex);
        }
    }

    public void setMostrarGruposRetencao(boolean mostrarGruposRetencao) {
        this.mostrarGruposRetencao = mostrarGruposRetencao;
    }

    public boolean isMostrarGruposRetencao() {
        return mostrarGruposRetencao;
    }

    public void toggleMostrarGruposRetencao() {
        setMostrarGruposRetencao(!isMostrarGruposRetencao());
    }

    public boolean isMostrarCheckboxRetencao() throws Exception {
        return !getUsuarioLogado().getAdministrador() && obterColaboradorParticipanteVOLogadoRetencao() != null;
    }

    public void setMarcarUsuarioRetencao(Boolean marcarUsuarioRetencao) {
        this.marcarUsuarioRetencao = marcarUsuarioRetencao;
    }

    public Boolean getMarcarUsuarioRetencao() throws Exception {
        if (getUsuarioLogado().getAdministrador()) {
            return false;
        }
        GrupoColaboradorParticipanteVO logado = obterColaboradorParticipanteVOLogadoRetencao();
        if (logado == null) {
            return false;
        }
        return logado.getGrupoColaboradorParticipanteEscolhido();
    }

    private GrupoColaboradorParticipanteVO obterColaboradorParticipanteVOLogadoRetencao() throws Exception {
        for (GrupoColaboradorVO grupoColaboradorVO : aberturaMetaVO.getGrupoColaboradorListaRetencao()) {
            for (GrupoColaboradorParticipanteVO colaboradorParticipanteVO : grupoColaboradorVO.getGrupoColaboradorParticipanteVOs()) {
                if (getUsuarioLogado().getColaboradorVO().getCodigo().equals(colaboradorParticipanteVO.getColaboradorParticipante().getCodigo())) {
                    return colaboradorParticipanteVO;
                }
            }
        }
        return null;
    }

    public boolean isMostrarGruposVendas() {
        return mostrarGruposVendas;
    }

    public void setMostrarGruposVendas(boolean mostrarGruposVendas) {
        this.mostrarGruposVendas = mostrarGruposVendas;
    }

    public void toggleMostrarGruposVendas() {
        setMostrarGruposVendas(!isMostrarGruposVendas());
    }

    public boolean isMostrarCheckboxVendas() throws Exception {
        return !getUsuarioLogado().getAdministrador() && obterColaboradorParticipanteVOLogadoVendas() != null;
    }

    public void setMarcarUsuarioVendas(Boolean marcarUsuarioVendas) {
        this.marcarUsuarioVendas = marcarUsuarioVendas;
    }

    public Boolean getMarcarUsuarioVendas() throws Exception {
        if (getUsuarioLogado().getAdministrador()) {
            return false;
        }
        GrupoColaboradorParticipanteVO logado = obterColaboradorParticipanteVOLogadoVendas();
        if (logado == null) {
            return false;
        }
        return logado.getGrupoColaboradorParticipanteEscolhido();
    }


    private GrupoColaboradorParticipanteVO obterColaboradorParticipanteVOLogadoVendas() throws Exception {
        for (GrupoColaboradorVO grupoColaboradorVO : aberturaMetaVO.getGrupoColaboradorListaVenda()) {
            for (GrupoColaboradorParticipanteVO colaboradorParticipanteVO : grupoColaboradorVO.getGrupoColaboradorParticipanteVOs()) {
                if (getUsuarioLogado().getColaboradorVO().getCodigo().equals(colaboradorParticipanteVO.getColaboradorParticipante().getCodigo())) {
                    return colaboradorParticipanteVO;
                }
            }
        }
        return null;
    }
    public boolean isMostrarGruposEstudio() {
        return mostrarGruposEstudio;
    }

    public void setMostrarGruposEstudio(boolean mostrarGruposEstudio) {
        this.mostrarGruposEstudio = mostrarGruposEstudio;
    }

    public void toggleMostrarGruposEstudio() {
        setMostrarGruposEstudio(!isMostrarGruposEstudio());
    }

    public boolean isMostrarCheckboxEstudio() throws Exception {
        return !getUsuarioLogado().getAdministrador() && obterColaboradorParticipanteVOLogadoEstudio() != null;
    }

    private GrupoColaboradorParticipanteVO obterColaboradorParticipanteVOLogadoEstudio() throws Exception {
        for (GrupoColaboradorVO grupoColaboradorVO : aberturaMetaVO.getGrupoColaboradorListaEstudio()) {
            for (GrupoColaboradorParticipanteVO colaboradorParticipanteVO : grupoColaboradorVO.getGrupoColaboradorParticipanteVOs()) {
                if (getUsuarioLogado().getColaboradorVO().getCodigo().equals(colaboradorParticipanteVO.getColaboradorParticipante().getCodigo())) {
                    return colaboradorParticipanteVO;
                }
            }
        }
        return null;
    }

    public void setMarcarUsuarioEstudio(Boolean marcarUsuarioEstudio) {
        this.marcarUsuarioEstudio = marcarUsuarioEstudio;
    }

    public Boolean getMarcarUsuarioEstudio() throws Exception {
        if (getUsuarioLogado().getAdministrador()) {
            return false;
        }
        GrupoColaboradorParticipanteVO logado = obterColaboradorParticipanteVOLogadoEstudio();
        if (logado == null) {
            return false;
        }
        return logado.getGrupoColaboradorParticipanteEscolhido();
    }

    public FecharMetaVO getFecharMetaConversaoExAlunosVO() {
        return fecharMetaConversaoExAlunosVO;
    }

    public void setFecharMetaConversaoExAlunosVO(FecharMetaVO fecharMetaConversaoExAlunosVO) {
        this.fecharMetaConversaoExAlunosVO = fecharMetaConversaoExAlunosVO;
    }

    public FecharMetaVO getFecharMetaVisitantesAntigosVO() {
        return fecharMetaVisitantesAntigosVO;
    }

    public void setFecharMetaVisitantesAntigosVO(FecharMetaVO fecharMetaVisitantesAntigosVO) {
        this.fecharMetaVisitantesAntigosVO = fecharMetaVisitantesAntigosVO;
    }

    public void setFecharMetaVisitantesRecorrenteVO(FecharMetaVO fecharMetaVisitantesRecorrenteVO) {
        this.fecharMetaVisitantesAntigosVO = fecharMetaVisitantesRecorrenteVO;
    }

    public FecharMetaVO getFecharMetaVisitantesRecorrenteVO() {
        return fecharMetaVisitantesRecorrenteVO;
    }

    public FecharMetaVO getFecharMetaConversaoVisitantesAntigos() {
        return fecharMetaConversaoVisitantesAntigos;
    }

    public void setFecharMetaConversaoVisitantesAntigos(FecharMetaVO fecharMetaConversaoVisitantesAntigos) {
        this.fecharMetaConversaoVisitantesAntigos = fecharMetaConversaoVisitantesAntigos;
    }

    public FecharMetaVO getFecharMetaConversaoIndicados() {
        return fecharMetaConversaoIndicados;
    }

    public void setFecharMetaConversaoIndicados(FecharMetaVO fecharMetaConversaoIndicados) {
        this.fecharMetaConversaoIndicados = fecharMetaConversaoIndicados;
    }

    public List<ColaboradorIndisponivelCrmVO> getListaColaboradorIndisponivel() {
        return listaColaboradorIndisponivel;
    }

    public void setListaColaboradorIndisponivel(List<ColaboradorIndisponivelCrmVO> listaColaboradorIndisponivel) {
        this.listaColaboradorIndisponivel = listaColaboradorIndisponivel;
    }

    public FecharMetaVO getFecharMetaConversaoDesistentes() {
        return fecharMetaConversaoDesistentes;
    }

    public void setFecharMetaConversaoDesistentes(FecharMetaVO fecharMetaConversaoDesistentes) {
        this.fecharMetaConversaoDesistentes = fecharMetaConversaoDesistentes;
    }

    private List<FecharMetaVO> retirarMetaNaoCalculadas(List<FecharMetaVO> fecharMetaVos) {
        List<FecharMetaVO> calculadas = new ArrayList<FecharMetaVO>();
        for (FecharMetaVO fm : fecharMetaVos) {
            if (fm.getFase() != null) {
                calculadas.add(fm);
            }
        }
        return calculadas;
    }
    
    
    public void enviarMetasFechadas(Date data) throws Exception {
        Uteis.logar(null, "Iniciando  Envio das metas (CRM) do dia " + data.toString() + " em " + Calendario.hoje().toString());
        List<EmpresaVO> empresas = getFacade().getEmpresa().consultarTodas(true,Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        ConfiguracaoSistemaCRMVO configuracaoSistemaCRMVO = SuperControle.getConfiguracaoSMTPNoReply();
       
        for (int i = 0; i < empresas.size(); i++) {
            System.out.println("Enviado " + (i + 1) + "ª empresa de " + empresas.size() + " empresas");
            EmpresaVO empresa = empresas.get(i);
            List<ConfiguracaoEmailFechamentoMetaVO> emailsEnviarFechamento = getFacade().getConfiguracaoSistemaCRM().consultarEmailsEnviarFechamento(empresa);
            List<AberturaMetaVO> metasAbertas = getFacade().getAberturaMeta().consultarMetasFechadasPorDia(data, empresa.getCodigo(), Uteis.NIVELMONTARDADOS_TODOS);
            for (int j = 0; j < metasAbertas.size(); j++) {
                System.out.println("Enviando " + (j + 1) + "ª meta de " + metasAbertas.size() + " metas");
                AberturaMetaVO aberturaMetaVO = metasAbertas.get(j);
                try {
                    if(configuracaoSistemaCRMVO.validarInformacoesBasicasEmail()){
                        enviarEmail(aberturaMetaVO, empresa, configuracaoSistemaCRMVO,emailsEnviarFechamento);
                    }
                } catch (Exception ex) {
                    Logger.getLogger(this.getClass().getName()).log(Level.INFO, "Problemas ao enviar email de fechamento de metas: {0}", new Object[]{ex.getMessage()});
                }
            }
        }
        Uteis.logar(null, "Terminando Envio das metas (CRM) do dia " + data.toString() + " em " + Calendario.hoje().toString());
    }

    
}
