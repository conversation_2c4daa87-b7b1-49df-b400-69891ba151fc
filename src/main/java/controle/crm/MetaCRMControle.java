package controle.crm;

import br.com.pactosolucoes.atualizadb.processo.AjustarAberturaMetaDuplicada;
import br.com.pactosolucoes.comuns.notificacao.RecursoSistema;
import br.com.pactosolucoes.comuns.util.Formatador;
import br.com.pactosolucoes.comuns.util.JSFUtilities;
import br.com.pactosolucoes.enumeradores.FasesCRMEnum;
import br.com.pactosolucoes.enumeradores.GrauSatisfacaoEnum;
import br.com.pactosolucoes.enumeradores.MarcadoresEmailEnum;
import br.com.pactosolucoes.enumeradores.OrigemSistemaEnum;
import br.com.pactosolucoes.enumeradores.SituacaoAtualMetaEnum;
import br.com.pactosolucoes.enumeradores.TipoAgendamentoEnum;
import br.com.pactosolucoes.enumeradores.TipoColaboradorEnum;
import br.com.pactosolucoes.enumeradores.TipoContatoCRM;
import br.com.pactosolucoes.enumeradores.TipoFaseCRM;
import br.com.pactosolucoes.enumeradores.TipoFiltroData;
import br.com.pactosolucoes.enumeradores.TipoTelefoneEnum;
import br.com.pactosolucoes.estrutura.paginacao.ConfPaginacao;
import br.com.pactosolucoes.turmas.servico.dto.ParamAlunoAulaCheiaTO;
import br.com.pactosolucoes.turmas.servico.impl.TurmasServiceImpl;
import br.com.pactosolucoes.turmas.servico.intf.TurmasServiceInterface;
import controle.arquitetura.SuperControle;
import controle.arquitetura.security.LoginControle;
import controle.basico.ClienteControle;
import controle.basico.GrupoTelaControle;
import controle.basico.clube.MensagemGenericaControle;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.CamposGenericosTO;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.basico.ColaboradorIndisponivelCrmVO;
import negocio.comuns.basico.ColaboradorVO;
import negocio.comuns.basico.EmailVO;
import negocio.comuns.basico.PessoaVO;
import negocio.comuns.basico.QuestionarioVO;
import negocio.comuns.basico.TelefoneVO;
import negocio.comuns.basico.enumerador.TipoItemCampanhaEnum;
import negocio.comuns.basico.enumerador.TipoServicoEnum;
import negocio.comuns.crm.*;
import negocio.comuns.financeiro.enumerador.OrigemCobrancaEnum;
import negocio.comuns.plano.HorarioTurmaVO;
import negocio.comuns.plano.MarcadorVO;
import negocio.comuns.plano.ModalidadeVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.ControleConsulta;
import negocio.comuns.utilitarias.Dominios;
import negocio.comuns.utilitarias.Ordenacao;
import negocio.comuns.utilitarias.SelectItemOrdemValor;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.comuns.utilitarias.UtilReflection;
import negocio.facade.jdbc.crm.MalaDireta;
import negocio.facade.jdbc.utilitarias.Conexao;
import org.json.JSONObject;
import org.jsoup.Jsoup;
import org.jsoup.safety.Whitelist;
import relatorio.negocio.comuns.basico.GrupoTipoColaboradorVO;
import relatorio.negocio.comuns.basico.ListaPaginadaTO;
import servicos.http.MetodoHttpEnum;
import servicos.http.RequestHttpService;
import servicos.http.RespostaHttpDTO;
import servicos.impl.autenticacaoMs.AutenticacaoMsService;
import servicos.integracao.TreinoWSConsumer;
import servicos.integracao.botconversa.BotConversaController;
import servicos.integracao.enumerador.TipoLeadEnum;
import servicos.integracao.gymbotpro.GymbotProController;
import servicos.integracao.sms.Message;
import servicos.integracao.sms.SmsController;
import servicos.operacoes.MailingService;
import servicos.propriedades.PropsService;
import servicos.vendasonline.VendasOnlineService;

import javax.faces.context.FacesContext;
import javax.faces.event.ActionEvent;
import javax.faces.model.SelectItem;
import javax.servlet.http.HttpServletRequest;
import java.io.OutputStream;
import java.net.URLEncoder;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.regex.Pattern;

/**
 * Created by ulisses on 01/09/2015.
 */

public class MetaCRMControle extends AberturaMetaControle {

    private static final Integer NUMERO_ITENS_CONSULTAR = 30;
    private static final String RETORNO_GERAR_NOTIFICACOES = "RETORNO_GERAR_NOTIFICACOES";
    private static final String HISTORICO_CONTATO_VO = "HistoricoContatoVO";

    private List<MetaCRMTO> listaMetaCRM;
    private TipoMetaCRMTO tipoMetaSelecionada;
    private List<HistoricoContatoVO> listaHistoricoContatoCliente;
    private UsuarioVO usuarioVO  = new UsuarioVO();
    private List<SelectItem> listaColaborador;
    String colaboradorResponsavelFase;
    String tipoColaboradorSelecionado;
    private List<HistoricoContatoVO> listaHistoricoContatoClienteSemLimite;
    private FecharMetaDetalhadoVO metaDetalhadoVOSelecionado;
    HashMap<Integer, byte[]> fotosClientes = new HashMap<Integer, byte[]>();
    private String onComplete;
    private String filtroClientes;
    private String mensagemRealizarContato;
    private MeioEnvio meioEnvio;
    private Integer tipoMensagemApp = TipoPerguntaEnum.SIMPLES.getCodigo();
    private HistoricoContatoVO historicoContatoVO;
    private CamposGenericosTO campos = new CamposGenericosTO();
    private boolean temUsuarioMovel = false;
    private boolean mostrarPanelObjecao = false;
    private boolean mostrarPanelAgendar = false;
    private boolean mostrarPanelIndicacao = false;
    private boolean mostrarPanelReagendamento = false;
    private String qtdRegistrosHistoricoContato;
    private MalaDiretaEnviadaVO malaDiretaEnviadaVO;
    private AgendaVO agendaVO;
    private MalaDiretaVO malaDiretaVO;
    private String nomeUsuario;
    public Boolean usuarioSelecionado=false;
    private String tituloModalContador;
    private List<HistoricoContatoVO> listaModalContadorCliente;
    private List<HistoricoContatoVO> listaHistoricoObjecoesCliente;
    private IndicacaoVO indicacaoVO;
    private IndicadoVO indicadoVO;
    private List<SelectItem> listaSelectTiposPerguntas;
    protected UsuarioVO colaboradorResponsavel;
    private List listaEmail;
    private boolean mostrarModeloDeMensagem = false;
    private List<UsuarioVO> listaUsuariosSelecionados;
    protected Date dataInicio;
    protected Date dataInicioOld;
    protected Date dataFim;
    protected Date dataFimOld;
    private Integer tipoFiltroData;
    private boolean mostrarPanelRealizarContato = false;
    private PassivoVO passivoVO;
    private boolean mostrarPanelObjecaoPassivo = false;
    private boolean mostrarPanelAgendarPassivo = false;
    private List<PassivoVO> listaPassivoVO;
    private List<PassivoVO> listaPassivoFiltradaVO;
    private boolean somenteEditarPassivo = false;
    private boolean mostrarPanelObjecaoIndicacao = false;
    private boolean mostrarPanelAgendarIndicacao = false;
    private boolean somenteEditarIndicado = false;
    private boolean metasProcessadas = false;
    private String pessoasSemEmailTelefone;
    private boolean mostrarPanelEmailSMSColetivo = false;
    private boolean marcarTodosMetaDetalhado = false;
    private boolean buscarEmTodasFases = false;
    private TextoPadraoVO textoPadraoVO;
    private TipoMetaCRMTO metaAtual;
    private String tokenSMSEmpresa;
    private String tokenSMSEmpresaShortCode;
    private String fasesDoContato;
    private String modalMensagemGenerica;
    private Integer codigoModeloMensagemCriado;
    private List<FecharMetaDetalhadoVO> listaMetaDetalhadaOriginal;
    private boolean apresentarBusca = false;
    private boolean apresentarPanelEnviarEmail = false;
    private List<TelefoneVO> listaTelefones;
    private String totalUsuariosSelecionados;
    private List<SelectItem> listaTextoPadraoVO = new ArrayList<SelectItem>(0);
    private boolean apresentarModalObjecaoDefinitiva = false;
    private List<SelectItem> listaTiposProfessores = new ArrayList<>();
    private List<SelectItem> listaAulasAgenda = new ArrayList<>();
    private List<SelectItem> listaColaboradores = new ArrayList<>();
    private String tipoProfessor;
    private Integer codigoAula;
    private Integer codigoProfessor;
    private List<HorarioTurmaVO> aulasAgenda;

    private List<GrupoTipoColaboradorVO> listaGrupoTipoColaborador;
    private boolean somenteAtivos = true;

    private List<GrupoColaboradorVO> listaGrupoColaborador;
    private ConfiguracaoSistemaCRMVO configuracaoSistemaCRMVO;

    private String urlWpp = "";
    private String urlMqv = "";
    private String ddiWpp = "55";
    private String foneWpp = null;
    private String txtWpp = null;
    private Boolean usarNonoDigitoWApp = null;
    private TipoMetaCRMTO tipoMetaConsultadaAnteriormente;
    private List<QuestionarioVO> listaPesquisa;
    private Integer pesquisa;
    private boolean exibeMqv = false;
    private ListaPaginadaTO listaHistoricoContatoClientePaginada;
    private static final Integer LISTA_PAGINADA_LIMIT = 5;
    private static final String LISTA_CONTATO = "LISTA_CONTATO";

    private Boolean abriuMetaHoje = false;
    private Boolean processarAbertura = false;
    private Boolean metaGerando = false;

    private String onCompleteEdicaoSimplesRegistro = "";
    private HistoricoContatoVO historicoContatoVOEdicao = new HistoricoContatoVO();
    private HistoricoContatoVO historicoContatoVOAntesEdicao = new HistoricoContatoVO();
    private FecharMetaDetalhadoVO fecharMetaDetalhadoVO = new FecharMetaDetalhadoVO();
    // Bot Conversa
    private boolean exibBotConversa = false;
    private String nomeFluxoBotconversa = "";
    private String webhookBotconversa = "";
    private Integer codigoGymBot;

    // Gymbot Pro
    private boolean exibGymbotPro = false;
    private String nomeFluxoGymbotPro = "";
    private String idFluxoGymbotPro = "";
    private String tokenGymbotPro = "";
    private String nomeCliente="";
    private Integer codigoGymBotPro;
    private static final Map<Integer, LocalDateTime> ULTIMO_ENVIO_MAP = new ConcurrentHashMap<>();
    private static final long DUPLICIDADE_MINUTOS = 1;


    public void validarAbrirMeta() throws Exception {
        abriuMetaHoje = getFacade().getAberturaMeta().abriuMetaHoje();
        processarAbertura = !abriuMetaHoje;
        if (processarAbertura) {
            notificarRecursoEmpresa(RecursoSistema.META_DIARIA_NAO_ABERTA);
        }
    }

    public void processarMetas(){
        try {
            System.out.println("Gerando meta automaticamente..." + getKey());
            setMsgAlert("");
            if(!metaGerando){
                metaGerando = true;
                new AberturaMetaControle().fecharMetas(Uteis.somarDias(Calendario.hoje(), -1));
                new AberturaMetaControle().abrirMetas(Calendario.hoje());
                notificarRecursoEmpresa(RecursoSistema.META_DIARIA_ABERTA_TELA_CRM_AUTOMATICA);
                metaGerando = false;
                abriuMetaHoje = true;
                consultarMetas(true, null);
                AjustarAberturaMetaDuplicada.ajustarDuplicacoesSimplificado(getFacade().getZWFacade().getCon(), Calendario.hoje());
                setMsgAlert("try{ Notifier.success(\"Metas geradas com sucesso!\");} catch(e){};initposgeracao();");
            }
        } catch (Exception ex) {
            System.out.println(ex.getMessage());
        }finally {
            metaGerando = false;
        }
    }

    public void abrirWppListener(ActionEvent evt) {
        try{
            setMsgAlert("");
            limparMsg();
            historicoContatoVO.setTipoContato("WA");

            ddiWpp = (String) JSFUtilities.getFromActionEvent("ddiWpp", evt);
            txtWpp = getHistoricoContatoVO().getObservacao();
            foneWpp = (String) JSFUtilities.getFromActionEvent("foneWpp", evt);
            usarNonoDigitoWApp = (Boolean) JSFUtilities.getFromActionEvent("usarNonoDigitoWApp", evt);

            if(ddiWpp.equals("") || ddiWpp == null)
                ddiWpp = "55";

            if (!usarNonoDigitoWApp) {
                if (foneWpp.length() == 11 && foneWpp.charAt(2) == '9') {
                    String ddd = foneWpp.substring(0,2);
                    String numero = foneWpp.substring(3,11);
                    foneWpp = ddd+numero;
                }
            }

            if (UteisValidacao.emptyString(getHistoricoContatoVO().getObservacao())) {
                montarAviso("Digite uma mensagem");
                setMsgAlert(getMensagemNotificar());
            } else {
                try {
                    if (historicoContatoVO.getClienteVO() != null && historicoContatoVO.getClienteVO().getCodigo() != 0) {
                        PessoaVO pessoaVO = historicoContatoVO.getClienteVO().getPessoa();
                        getFacade().getTelefone().alterarTelefones(pessoaVO.getCodigo(), listaTelefones, pessoaVO.getEmpresaInternacional());
                    }
                } catch (Exception ignored) {
                }
                setMsgAlert("abrirPopup('https://web.whatsapp.com/send?phone="+ ddiWpp + foneWpp + "&text= " + getModeloMensagemWhatsAppEncode() + "', 'WhatsApp', 800, 500);return false;");
            }
        } catch (Exception e) {
            montarErro(e);
            setMsgAlert(getMensagemNotificar());
        }
    }

    public void abrirMqvListener(ActionEvent evt) {
        try{
            setMsgAlert("");
            limparMsg();

            String tokenMQV= getFacade().getEmpresa().obterTokenMQV(getEmpresaLogado().getCodigo());
            if (!tokenMQV.equals("")) setExibeMqv(true);
            EmailVO email = (EmailVO)getListaEmail().get(0);
            TelefoneVO  telefone = getListaTelefones().get(0);
            this.urlMqv = "https://mqv.institutodoalivio.com.br/CRM/ADM/PerfilAdm/0/0/"+ tokenMQV +"/"+email.getEmail()+"/"+ telefone.getNumeroSemMascara();
            setMsgAlert("abrirPopup('"+this.urlMqv+"', 'teste', 800, 500);return false;");

        } catch (Exception e) {
            montarErro(e);
            setMsgAlert(getMensagemNotificar());
        }
    }


    public void abrirWpp(){
        try{
            if(txtWpp == null || foneWpp == null || ddiWpp == null){
                urlWpp = "https://web.whatsapp.com";
            }else{
                urlWpp = "https://web.whatsapp.com/send?phone="+ddiWpp+foneWpp+"&text="+txtWpp;
                //urlWpp = "https://web.whatsapp.com/send?phone=+59171068204&text="+txtWpp;
            }
            foneWpp = null;
            txtWpp = null;
        } catch (Exception e) {
            setSucesso(false);
            setErro(true);
            montarMsgGenerica("Meta Diária", e.getMessage(), true, null, null, "");
        }
    }


    public MetaCRMControle() throws Exception {
        super();
        setConfiguracaoSistemaCRMVO(getFacade().getConfiguracaoSistemaCRM().consultarConfiguracaoSistemaCRM(Uteis.NIVELMONTARDADOS_DADOSBASICOS));
        setApresentarModalObjecaoDefinitiva(false);
        setConfPaginacao(new ConfPaginacao(NUMERO_ITENS_CONSULTAR));

        //consultarMetas();
    }

    public void setColaboradorResponsavelFase(String colaboradorResponsavelFase){
        this.colaboradorResponsavelFase = colaboradorResponsavelFase;
    }

    public String getColaboradorResponsavelFase() {
        return colaboradorResponsavelFase;
    }

    public void setTipoColaboradorSelecionado(String tipoColaboradorSelecionado){
        this.tipoColaboradorSelecionado = tipoColaboradorSelecionado;
    }


    public String getTipoColaboradorSelecionado() {
        return tipoColaboradorSelecionado;
    }

    public void setUsuario(UsuarioVO usuarioVO) {
        this.usuarioVO = usuarioVO;
    }

    public UsuarioVO getUsuario() {
        return usuarioVO;
    }

    public List<UsuarioVO> executarAutocompleteCol(Object suggest) {
        List<UsuarioVO> UsuarioVO = null;
        try {
            UsuarioVO = getFacade().getUsuario().consultarPorNomeAtivo(suggest.toString(), false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        } catch (Exception e) {
            setSucesso(false);
            setErro(true);
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
        return UsuarioVO;
    }

    public String getNomeUsuario() {
        if(nomeUsuario==null || nomeUsuario.isEmpty()){
            setUsuarioSelecionado(false);
            return nomeUsuario;
        }else return nomeUsuario;
    }

    public void setNomeUsuario(String nomeUsuario) {
        this.nomeUsuario = nomeUsuario;
    }

    public void setNenhumUsuario() {
        nomeUsuario = "";
        setUsuarioSelecionado(false);
        this.usuarioVO = null;
    }

    public void setUsuarioSelecionado(boolean usuarioSelecionado){
        this.usuarioSelecionado = usuarioSelecionado;
    }

    public Boolean getUsuarioSelecionado(){
        return  usuarioSelecionado;
    }

    public void selecionarUsuario() throws Exception {
        try {
            UsuarioVO usuarioVO = (UsuarioVO) request().getAttribute("result");
            if (usuarioVO == null) {
                this.usuarioVO = new UsuarioVO();
                setUsuarioSelecionado(false);
            } else {
                this.usuarioVO = usuarioVO;
                setUsuarioSelecionado(true);
            }
            setUsuario(usuarioVO);
        }catch (Exception e){
            throw e;
        }
    }

    public String consultarMetas() {
        return consultarMetas(false, null);
    }

    public void dispararFluxoGymbotPro() {
        String numero = null;
        Integer codigoCliente = null;
        limparEntradasAntigas();

        FOR:
        for (Object obj : this.getListaTelefoneClientePorTipoContato()) {
            TelefoneVO tel = (TelefoneVO) obj;
            if (tel != null && Uteis.validarTelefoneCelular(tel.getNumero())) {
                numero = tel.getNumero();
                codigoCliente = tel.getCodigo();
                break FOR;
            }
        }
        try {
            // Se não encontrou nenhum telefone celular, avisa e sai
            if (codigoCliente == null) {
                montarErro("Nenhum telefone celular encontrado para este cliente. Não foi possível disparar GymBot.");
                return;
            }

            // 2) Verifica duplicidade
            boolean jaEnviada = verificarMensagemEnviada(codigoCliente, DUPLICIDADE_MINUTOS);

            if (jaEnviada) {
                montarErro("Já foi enviado GymBot Pro para este cliente há menos de 1 minuto. Aguardando...");
                return;
            }

            // 3) Monta e envia a mensagem
            List<Message> messageList = new ArrayList<>();
            Message m = new Message();
            m.setNumero(numero);
            m.setNome(fecharMetaDetalhadoVO.getNomePessoaRel());
            m.setMsg("ND");
            messageList.add(m);
            GymbotProController service = new GymbotProController(getKey(), malaDiretaVO, getFacade().getEmpresa().getCon());
            String ret =  service.sendMessage(getKey(),getEmpresaLogado().getCodigo() ,getTokenGymbotPro(), getIdFluxoGymbotPro() ,messageList);

            //Grava o Historico
            String msgBotConversa = "\nDisparo GymBot Pro, \"" + nomeFluxoBotconversa + "\", retorno: " + ret;

            this.getHistoricoContatoVO().setObservacao(msgBotConversa);
            this.getHistoricoContatoVO().setTipoGymBotEnum(TipoGymBotEnum.GYMBOT_PRO);
            this.getHistoricoContatoVO().setFluxoGymBot(codigoGymBotPro);
            confirmarSimplesRegistro();
            setSucesso(true);
            setErro(false);
            montarSucessoGrowl("GymBot disparado com sucesso!");
            setMsgAlert(getMensagemNotificar());
        }
        catch (Exception e) {
            montarErro(e.getMessage());
            setMsgAlert(getMensagemNotificar());
        }
    }

    public boolean verificarMensagemEnviada(Integer codigoCliente, long minutos) {
        LocalDateTime agora = LocalDateTime.now();
        LocalDateTime limite = agora.minusMinutes(minutos);

        LocalDateTime ultimoEnvio = ULTIMO_ENVIO_MAP.get(codigoCliente);

        if (ultimoEnvio != null && ultimoEnvio.isAfter(limite)) {
            return true; // já foi enviada nesse intervalo
        }

        // Se ainda não tem no MAP, ou já expirou, atualiza e retorna false
        ULTIMO_ENVIO_MAP.put(codigoCliente, agora);
        return false;
    }

    public void dispararFluxo (){
        String numero = null;
        Integer codigoCliente = null;
        limparEntradasAntigas();

        // 1) Tenta encontrar qualquer telefone que seja celular
        FOR:
        for (Object obj : this.getListaTelefoneClientePorTipoContato()) {
            TelefoneVO tel = (TelefoneVO) obj;
            if (tel != null && Uteis.validarTelefoneCelular(tel.getNumero())) {
                numero = tel.getNumero();
                codigoCliente = tel.getCodigo();
                break FOR;
            }
        }

        try {
             if (numero == null) {
                montarErro("Nenhum telefone celular encontrado para este cliente. Não foi possível disparar GymBot.");
                return;
            }
            // 3) Verifica duplicidade do envio (ex.: nos últimos 1 minuto)
            boolean jaEnviada = verificarMensagemEnviada(codigoCliente, DUPLICIDADE_MINUTOS);
            if (jaEnviada) {
                montarErro("Já foi enviado GymBot para este cliente há menos de 1 minuto. Aguardando...");
                return;
            }
            // 3) Monta e envia a mensagem
            List<Message> messageList = new ArrayList<>();
            Message m = new Message();
            m.setNumero(numero);
            m.setNome(fecharMetaDetalhadoVO.getNomePessoaRel());
            m.setMsg("ND");
            messageList.add(m);

            BotConversaController service = new BotConversaController(getKey(), malaDiretaVO, getFacade().getEmpresa().getCon());
            String ret =  service.sendMessage(getKey(),getEmpresaLogado().getCodigo() ,getWebhookBotconversa() ,messageList );
            String msgBotConversa = "\nDisparo de fluxo GymBot, \"" + nomeFluxoBotconversa + "\", retorno: " + ret;

            this.getHistoricoContatoVO().setObservacao(msgBotConversa);
            this.getHistoricoContatoVO().setTipoGymBotEnum(TipoGymBotEnum.GYMBOT_LITE);
            this.getHistoricoContatoVO().setFluxoGymBot(codigoGymBot);
            confirmarSimplesRegistro();
            notificarRecursoEmpresa(RecursoSistema.USOU_SIMPLES_REGISTRO_NOVO);
            setSucesso(true);
            setErro(false);
            montarSucessoGrowl("GymBot disparado com sucesso!");
            setMsgAlert(getMensagemNotificar());
        }
        catch (Exception e){
            montarErro(e.getMessage());
            setMsgAlert(getMensagemNotificar());
        }
    }

    public String consultarMetas(boolean posaberturaautomatica, Integer codMetaExtraSelecionar) {
        try {
            getFuncionalidadeControle().setarFuncionalidade();
            notificarRecursoEmpresa(RecursoSistema.CRM_META_DIARIA);
            if(!abriuMetaHoje && Calendario.igual(getDataInicio(), Calendario.hoje())){
                validarAbrirMeta();
                if(!abriuMetaHoje){
                    montarListaGrupoColaborador();
                    return "telaPrincipalCRM";
                }
            }
            String tokenMQV= getFacade().getEmpresa().obterTokenMQV(getEmpresaLogado().getCodigo());
            if (!UteisValidacao.emptyString(tokenMQV)) setExibeMqv(true);
            setApresentarModalObjecaoDefinitiva(false);
            Date d1 = Calendario.hoje();
            setTokenSMSEmpresa(getFacade().getEmpresa().obterTokenSMS(getEmpresaLogado().getCodigo()));
            setTokenSMSEmpresaShortCode(getFacade().getEmpresa().obterTokenSMSShortCode(getEmpresaLogado().getCodigo()));
            gerarMetaCRMExtra();

            if (!isMetasProcessadas()) {

                if (getConfiguracaoSistemaCRMVO().isApresentarColaboradoresPorTipoColaborador()) {
                    montarListaUsuariosCRM();
                    adicionarUsuariosCRMListaSelecionados(true);
                } else  {
                    if (UteisValidacao.emptyList(getListaUsuariosSelecionados()) && !getUsuarioLogado().getAdministrador()) {
                        Long aberturaMeta = getFacade().getAberturaMeta().consultarVerificacaoAberturaMetaDia(getUsuarioLogado().getCodigo(), Calendario.hoje(), getEmpresaLogado().getCodigo());
                        if (aberturaMeta != 0) {
                            List<UsuarioVO> usuarioVOList = new ArrayList<UsuarioVO>();
                            usuarioVOList.add(getUsuarioLogado());
                            setListaUsuariosSelecionados(usuarioVOList);
                        }
                    }
                    if(!posaberturaautomatica){
                        montarListaGrupoColaborador();
                    }
                    Integer total = getListaUsuariosSelecionados().size();
                    setTotalUsuariosSelecionados(total.toString());
                }

                consultarMetasCRM(getDataInicio(), getDataFim(), getListaUsuariosSelecionados(), false);
                setMetasProcessadas(true);
            }

            if (!UteisValidacao.emptyNumber(codMetaExtraSelecionar)) {
                for (MetaCRMTO metaCRMTO : getListaMetaCRM()) {
                    for (TipoMetaCRMTO tipoMetaCRMTO : metaCRMTO.getListaTipoMetaVO()) {
                        if (tipoMetaCRMTO.getFasesCRMEnum().equals(FasesCRMEnum.CRM_EXTRA) &&
                                    tipoMetaCRMTO.getListaFecharMetaVO() != null &&
                                    tipoMetaCRMTO.getListaFecharMetaVO().size() > 0 &&
                                    tipoMetaCRMTO.getListaFecharMetaVO().get(0).getMalaDiretaCRMExtra().getCodigo().equals(codMetaExtraSelecionar)) {
                            selecionarMeta(tipoMetaCRMTO);
                            break;
                        }
                    }
                }
            }

            if(metaAtual != null) {
                List<ConfiguracaoIntegracaoBotConversaVO> listafluxoBotConversa = getFacade().getMalaDireta().consultarFluxoEmpresaFase(getEmpresaLogado().getCodigo(), metaAtual.getFasesCRMEnum().getSigla());
                if (listafluxoBotConversa.size() > 0 ? listafluxoBotConversa.get(0).getFase().equals(metaAtual.getFasesCRMEnum().getSigla()) : false) {
                    setExibBotConversa(true);
                    setNomeFluxoBotconversa(listafluxoBotConversa.get(0).getDescricao());
                    setWebhookBotconversa(listafluxoBotConversa.get(0).getUrlwebhoobotconversa());
                    setCodigoGymBot(listafluxoBotConversa.get(0).getCodigo());
                }

                List<ConfiguracaoIntegracaoGymbotProVO> listafluxoGymbotProVO = getFacade().getMalaDireta().consultarFluxoEmpresaFaseGymbotPro(getEmpresaLogado().getCodigo(), metaAtual.getFasesCRMEnum().getSigla());
                if (listafluxoGymbotProVO.size() > 0 ? listafluxoGymbotProVO.get(0).getFase().equals(metaAtual.getFasesCRMEnum().getSigla()) : false) {
                    setExibBotConversa(true);
                    setNomeFluxoGymbotPro(listafluxoGymbotProVO.get(0).getDescricao());
                    setTokenGymbotPro(listafluxoGymbotProVO.get(0).getToken());
                    setIdFluxoGymbotPro(listafluxoGymbotProVO.get(0).getIdFluxo());
                    setCodigoGymBotPro(listafluxoGymbotProVO.get(0).getCodigo());
                }
            }

            Integer total = getListaUsuariosSelecionados().size();
            setTotalUsuariosSelecionados(total.toString());
            montarSucesso("");
            Date d2 = Calendario.hoje();
            System.out.println("Total: " + (d2.getTime() - d1.getTime()));
            return "telaPrincipalCRM";
        } catch (Exception e) {
            e.printStackTrace();
            montarErro(e);
        }
        return null;
    }

    public void consultarMetasUsuariosSelecionadosBtnFront() throws Exception {
        try {
            setModalMensagemGenerica("");
            if (getDataFim() == null || !getControlador(LoginControle.class).getPermissaoAcessoMenuVO().getPesquisarPeriodoCRM()) {
                setDataFim(getDataInicio());
            }

            if (getDataInicio() == null) {
                throw new Exception("Por favor informar uma data inicial para realizar a consulta.");
            } else {
                if (Calendario.maior(getDataInicio(), Calendario.hoje())) {
                    throw new Exception("Não é possível consultar as Metas Futuras, já que estas são geradas diariamente. Se necessário, realize a consulta de agendamentos no menu Consultas.");
                }
                if (getDataFim() != null) {
                    if (Calendario.maior(getDataFim(), Calendario.hoje())) {
                        setDataFim(Calendario.hoje());
                    }
                    if (Calendario.maior(getDataInicio(), getDataFim())) {
                        throw new Exception("Data inicial maior que a final, verifique!");
                    }
                    if (Calendario.diferencaEmDias(getDataInicio(), getDataFim()) > 7) {
                        setDataInicio(getDataInicioOld());
                        setDataFim(getDataFimOld());
                        notificarRecursoEmpresa(RecursoSistema.BUSCOU_INTERVALO_MAIOR_7_DIAS_META_CRM);
                        setModalMensagemGenerica("Richfaces.showModalPanel('mdlMensagemMetaDiaria');");
                        return;
                    }
                }
            }

            if (getConfiguracaoSistemaCRMVO().isApresentarColaboradoresPorTipoColaborador()) {
                adicionarUsuariosCRMListaSelecionados(false);
            } else {
                adicionarUsuarioListaSelecionados();
            }

            validarDados();
            consultarMetasCRM(getDataInicio(), getDataFim(), getListaUsuariosSelecionados(), true);
            setModalMensagemGenerica("mostrarTodasTelas();adicionarPlaceHolderCRM();");
            setDataInicioOld(getDataInicio());
            setDataFimOld(getDataFim());
        } catch (Exception e) {
            e.printStackTrace();
            setSucesso(false);
            setErro(true);
            montarMsgGenerica("Meta Diária", e.getMessage(), true, null, null, "");
        }
    }

    public void consultarMetasUsuariosSelecionados() throws Exception {
        try {
            setModalMensagemGenerica("");
            if (getDataFim() == null || !getControlador(LoginControle.class).getPermissaoAcessoMenuVO().getPesquisarPeriodoCRM()) {
                setDataFim(getDataInicio());
            }

            if (getDataInicio() == null) {
                throw new Exception("Por favor informar uma data inicial para realizar a consulta.");
            } else {
                if (Calendario.maior(getDataInicio(), Calendario.hoje())) {
                    throw new Exception("Não é possível consultar as Metas Futuras, já que estas são geradas diariamente. Se necessário, realize a consulta de agendamentos no menu Consultas.");
                }
                if (getDataFim() != null) {
                    if (Calendario.maior(getDataFim(), Calendario.hoje())) {
                        setDataFim(Calendario.hoje());
                    }
                    if (Calendario.maior(getDataInicio(), getDataFim())) {
                        throw new Exception("Data inicial maior que a final, verifique!");
                    }
                    if (Calendario.diferencaEmDias(getDataInicio(), getDataFim()) > 7) {
                        setDataInicio(getDataInicioOld());
                        setDataFim(getDataFimOld());
                        notificarRecursoEmpresa(RecursoSistema.BUSCOU_INTERVALO_MAIOR_7_DIAS_META_CRM);
                        setModalMensagemGenerica("Richfaces.showModalPanel('mdlMensagemMetaDiaria');");
                        return;
                    }
                }
            }

            if (getConfiguracaoSistemaCRMVO().isApresentarColaboradoresPorTipoColaborador()) {
                adicionarUsuariosCRMListaSelecionados(false);
            } else {
                adicionarUsuarioListaSelecionados();
            }

            validarDados();
            consultarMetasCRM(getDataInicio(), getDataFim(), getListaUsuariosSelecionados(), false);
            setModalMensagemGenerica("mostrarTodasTelas();adicionarPlaceHolderCRM();");
            setDataInicioOld(getDataInicio());
            setDataFimOld(getDataFim());
        } catch (Exception e) {
            e.printStackTrace();
            setSucesso(false);
            setErro(true);
            montarMsgGenerica("Meta Diária", e.getMessage(), true, null, null, "");
        }
    }

    public boolean isMostrarSeletorNome() {
        return !getTipoProfessor().isEmpty();
    }

    public void alterarTipoColaborador(ActionEvent event) throws Exception {
        carregarListaColaboradores();
    }

    public Object carregarListaColaboradores() throws Exception {
        setListaColaboradores(new ArrayList<>());
        getListaColaboradores().add(new SelectItem("", ""));
        if (!getTipoProfessor().isEmpty()) {
            TipoColaboradorEnum tipoColaboradorEnum = TipoColaboradorEnum.getTipo(getTipoProfessor());
            List<ColaboradorVO> colaboradorVOs = getFacade().getColaborador().consultarPorNomeTipoVinculoPossivel(
                    "", tipoColaboradorEnum, getEmpresaLogado().getCodigo(), Uteis.NIVELMONTARDADOS_ORGANIZADORCARTEIRA);
            for (ColaboradorVO colaboradorVO : colaboradorVOs) {
                getListaColaboradores().add(new SelectItem(colaboradorVO.getCodigo(), colaboradorVO.getPessoa_Apresentar()));
            }
        }
        return true;
    }

    public void validarDados() throws Exception {
        if (UteisValidacao.emptyList(getListaUsuariosSelecionados())) {
            throw new Exception("Selecione pelo menos um usuário.");
        }

        if (getDataFim() != null && Calendario.maior(getDataInicio(), getDataFim())) {
            setDataFim(getDataInicio());
        }
    }

    public void adicionarUsuarioListaSelecionados() throws Exception {
        List<UsuarioVO> novaListaUsuario = new ArrayList<UsuarioVO>();
        for (GrupoColaboradorVO grupoColaboradorVO : getListaGrupoColaborador()) {
            for (GrupoColaboradorParticipanteVO participanteVO : grupoColaboradorVO.getGrupoColaboradorParticipanteVOs()) {
                if (participanteVO.getGrupoColaboradorParticipanteEscolhido()) {
                    UsuarioVO usuarioVO;
                    try {
                        usuarioVO = getFacade().getUsuario().consultarPorColaborador(participanteVO.getColaboradorParticipante().getCodigo(), Uteis.NIVELMONTARDADOS_METACOLABORADORRESPONSAVEL);
                    } catch (Exception ignore) {
                        usuarioVO = getFacade().getUsuario().consultarPorCodigoPessoa(participanteVO.getColaboradorParticipante().getPessoa().getCodigo(), Uteis.NIVELMONTARDADOS_METACOLABORADORRESPONSAVEL);
                    }
                    novaListaUsuario.add(usuarioVO);
                }

                if (!participanteVO.getGrupoColaboradorParticipanteEscolhido() && grupoColaboradorVO.getTodosParticipantesSelecionados()) {
                    grupoColaboradorVO.setTodosParticipantesSelecionados(false);
                }
            }
        }
        setListaUsuariosSelecionados(novaListaUsuario);
        setMetasProcessadas(false);
    }

    public void consultarMetasCRM(Date dataInicio, Date dataFim, List<UsuarioVO> usuarioVOList, boolean selecionandoFaseOrigemTela) {
        try {
            inicializarDados();
            carregarListaTiposProfessores();

            if(!getControlador(LoginControle.class).getPermissaoAcessoMenuVO().getPesquisarPeriodoCRM()){
                dataFim = dataInicio;
            }

            List<UsuarioVO> usuariosSelecionadosComSubstitutos = new ArrayList<UsuarioVO>();
            for (UsuarioVO usuarioVO : usuarioVOList) {
                ColaboradorVO colaboradorVO = getFacade().getColaborador().consultarPorCodigoUsuario(usuarioVO.getCodigo(), getEmpresaLogado().getCodigo(), false, Uteis.NIVELMONTARDADOS_MINIMOS);
                List<ColaboradorIndisponivelCrmVO> lista = getFacade().getColaboradorIndisponivelCrm().consultarPorColaboradorSuplentePorPeriodo(colaboradorVO.getCodigo(), getEmpresaLogado().getCodigo(), getDataInicio());

                for (ColaboradorIndisponivelCrmVO colIndis : lista) {
                    UsuarioVO usuOriginal = getFacade().getUsuario().consultarPorColaboradorEmpresa(colIndis.getColaboradorIndisponivelVO().getCodigo(), getEmpresaLogado().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                    usuOriginal.setSelecionado(true);
                    usuariosSelecionadosComSubstitutos.add(usuOriginal);
                }
                usuariosSelecionadosComSubstitutos.add(usuarioVO);
            }

            if (getConfiguracaoSistemaCRMVO().isApresentarColaboradoresPorTipoColaborador()) {
                for (GrupoTipoColaboradorVO grupoTipoColaboradorVO : getListaGrupoTipoColaborador()) {
                    for (UsuarioVO usuarioVO : grupoTipoColaboradorVO.getUsuarios()) {
                        for (UsuarioVO usuarioSelecionado : usuariosSelecionadosComSubstitutos) {
                            if (usuarioSelecionado.getCodigo().equals(usuarioVO.getCodigo())) {
                                usuarioVO.setSelecionado(true);
                            }
                        }
                    }
                }
            } else {
                for (GrupoColaboradorVO grupoColaboradorVO : getListaGrupoColaborador()) {
                    for (GrupoColaboradorParticipanteVO grupoColaboradorParticipanteVO : grupoColaboradorVO.getGrupoColaboradorParticipanteVOs()) {
                        for (UsuarioVO usuarioSelecionado : usuariosSelecionadosComSubstitutos) {
                            if (grupoColaboradorParticipanteVO.getUsuarioParticipante().getCodigo() != 0) {
                                if (grupoColaboradorParticipanteVO.getUsuarioParticipante().getCodigo().equals(usuarioSelecionado.getCodigo())) {
                                    grupoColaboradorParticipanteVO.setGrupoColaboradorParticipanteEscolhido(true);
                                }
                            }
                        }
                    }
                }
            }

            setListaUsuariosSelecionados(usuariosSelecionadosComSubstitutos);
            Integer total = getListaUsuariosSelecionados().size();
            setTotalUsuariosSelecionados(total.toString());
            usuarioVOList = usuariosSelecionadosComSubstitutos;


            this.listaMetaCRM = null;

            Date d1 = Calendario.hoje();

            setTipoMetaSelecionada(new TipoMetaCRMTO());
            getTipoMetaSelecionada().setListaMetaDetalhada(new ArrayList<FecharMetaDetalhadoVO>());
            getTipoMetaSelecionada().setListaMetaDetalhadaFiltrada(new ArrayList<FecharMetaDetalhadoVO>());
            setListaMetaDetalhadaOriginal(new ArrayList<FecharMetaDetalhadoVO>());
            setMetaDetalhadoVOSelecionado(new FecharMetaDetalhadoVO());
            setHistoricoContatoVO(new HistoricoContatoVO());
            setListaHistoricoContatoCliente(new ArrayList<HistoricoContatoVO>());
            setMostrarPanelReagendamento(false);

            if (!UteisValidacao.emptyList(getListaUsuariosSelecionados())) {
                this.listaMetaCRM = getFacade().getFecharMeta().consultarMeta(dataInicio, dataFim, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA, usuarioVOList, getEmpresaLogado().getCodigo(), true);
            }

            Date d2 = Calendario.hoje();
            System.out.println("Consultar Lista de Metas: " + (d2.getTime() - d1.getTime()));


            MetaCRMTO tipoFaseIndicadores = new MetaCRMTO();
            tipoFaseIndicadores.setTipoFaseCRM(TipoFaseCRM.INDICADORES);

            if (!UteisValidacao.emptyList(this.listaMetaCRM)) {

                //IDENTIFICAR OS FECHARMETAS DE AGENDAMENTOS DE INDICAÇÕES PARA SER UTILIZADO NOS ITENS:
                // AGENDAMENTOS DE LIGAÇÕES
                // INDICAÇÕES SEM CONTATO
                List<FecharMetaVO> listaAgendados = new ArrayList<FecharMetaVO>();
                List<FecharMetaVO> listaIndicados = new ArrayList<FecharMetaVO>();
                for (MetaCRMTO metaCRMTO : getListaMetaCRM()) {
                    for (TipoMetaCRMTO tipoMetaCRMTO : metaCRMTO.getListaTipoMetaVO()) {
                        if (tipoMetaCRMTO.getFasesCRMEnum().equals(FasesCRMEnum.AGENDAMENTO)) {
                            listaAgendados = tipoMetaCRMTO.getListaFecharMetaVO();
                        } else if (tipoMetaCRMTO.getFasesCRMEnum().equals(FasesCRMEnum.INDICACOES)) {
                            listaIndicados = tipoMetaCRMTO.getListaFecharMetaVO();
                        }
                    }
                }


                //AGENDAMENTOS DE LIGAÇÕES
                if (!UteisValidacao.emptyList(listaAgendados)) {
                    TipoMetaCRMTO agendamentoLigacoes = new TipoMetaCRMTO();
                    agendamentoLigacoes.setFasesCRMEnum(FasesCRMEnum.AGENDAMENTOS_LIGACOES);
                    agendamentoLigacoes.setListaFecharMetaVO(listaAgendados);

                    List<FecharMetaDetalhadoVO> listaLigacoes = getFacade().getFecharMetaDetalhado().consultarIndicadores(listaAgendados, true, false, false, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
                    Integer totalLigacoes = 0;
                    Integer totalRealizado = 0;

                    if (!UteisValidacao.emptyList(listaLigacoes)) {
                        totalLigacoes = listaLigacoes.size();
                        for (FecharMetaDetalhadoVO ligacao : listaLigacoes) {
                            if (ligacao.isTeveContato()) {
                                ligacao.setObteveSucesso(true);
                                ligacao.setSituacaoAtualMetaEnum(SituacaoAtualMetaEnum.META_ATENDIDA);
                                totalRealizado++;
                            } else {
                                ligacao.setObteveSucesso(false);
                                ligacao.setSituacaoAtualMetaEnum(SituacaoAtualMetaEnum.META_NAO_ATENDIDA);
                            }
                        }
                    }

                    agendamentoLigacoes.setTotalMeta(totalLigacoes);
                    agendamentoLigacoes.setTotalMetaRealizada(totalRealizado);

                    if (agendamentoLigacoes.getTotalMetaRealizada() >= agendamentoLigacoes.getTotalMeta()) {
                        agendamentoLigacoes.setSituacaoAtualMetaEnum(SituacaoAtualMetaEnum.META_ATENDIDA);
                    } else {
                        agendamentoLigacoes.setSituacaoAtualMetaEnum(SituacaoAtualMetaEnum.META_NAO_ATENDIDA);
                    }
                    if (agendamentoLigacoes.getTotalMeta() > 0) {
                        for (MetaCRMTO metaCRMTO : getListaMetaCRM()) {
                            if (metaCRMTO.getTipoFaseCRM().equals(TipoFaseCRM.VENDAS)) {
                                metaCRMTO.getListaTipoMetaVO().add(agendamentoLigacoes);
                                Integer totalMetaAgendamentoLigacoes = new Integer(agendamentoLigacoes.getTotalMeta() + metaCRMTO.getTotalMeta());
                                Integer totalMetaRealizadaAgendamentoLigacoes = new Integer(agendamentoLigacoes.getTotalMetaRealizada() + metaCRMTO.getTotalMetaRealizada());
                                metaCRMTO.setTotalMeta(totalMetaAgendamentoLigacoes);
                                metaCRMTO.setTotalMetaRealizada(totalMetaRealizadaAgendamentoLigacoes);
                            }
                        }
                    }
                }

                //INDICAÇÔES
                if (!UteisValidacao.emptyList(listaIndicados)) {
                    TipoMetaCRMTO indicacoesSemContato = new TipoMetaCRMTO();
                    indicacoesSemContato.setSituacaoAtualMetaEnum(SituacaoAtualMetaEnum.META_ATENDIDA);
                    indicacoesSemContato.setFasesCRMEnum(FasesCRMEnum.INDICACOES_SEM_CONTATO);
                    indicacoesSemContato.setListaFecharMetaVO(listaIndicados);
                    indicacoesSemContato.setTotalMeta(getFacade().getFecharMetaDetalhado().contarIndicadores(listaIndicados, false, true, false));
                    tipoFaseIndicadores.setTotalMeta(tipoFaseIndicadores.getTotalMeta() + indicacoesSemContato.getTotalMeta());
                    tipoFaseIndicadores.getListaTipoMetaVO().add(indicacoesSemContato);
                }


                //ORDENAR AS METAS DE ACORDO COM A CONFIGURAÇÃO DO SISTEMA CRM
                Date tempoOrdenar1 = Calendario.hoje();
                ConfiguracaoSistemaCRMVO configuracaoSistemaCRMVO = getFacade().getConfiguracaoSistemaCRM().consultarPorChavePrimaria(1, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                String[] ordenacaoMetas = configuracaoSistemaCRMVO.getOrdenacaoMetas().split(",");

                Integer posicao = 1;
                for (String meta : ordenacaoMetas) {
                    posicao++;
                    for (MetaCRMTO metaCRMTO : getListaMetaCRM()) {
                        for (TipoMetaCRMTO tipoMetaCRMTO : metaCRMTO.getListaTipoMetaVO()) {
                            if(tipoMetaCRMTO.getFasesCRMEnum().getSigla().equals(FasesCRMEnum.LEADS_HOJE.getSigla())){
                                tipoMetaCRMTO.setOrdemMeta(0);
                            } else if(tipoMetaCRMTO.getFasesCRMEnum().getSigla().equals(FasesCRMEnum.LEADS_ACUMULADAS.getSigla())){
                                tipoMetaCRMTO.setOrdemMeta(1);
                            } else if (tipoMetaCRMTO.getFasesCRMEnum().getSigla().equals(meta)) {
                                tipoMetaCRMTO.setOrdemMeta(posicao);
                            }

                        }
                        Ordenacao.ordenarLista(metaCRMTO.getListaTipoMetaVO(), "ordemMeta");
                    }
                }

                List<Integer> listaTipoMeta = new ArrayList<Integer>();
                listaTipoMeta.add(TipoFaseCRM.LEAD.getCodigo());
                for (String meta : ordenacaoMetas) {
                    FasesCRMEnum fasesCRMEnum = FasesCRMEnum.getFasePorSigla(meta);
                    if (!listaTipoMeta.contains(fasesCRMEnum.getTipoFase().getCodigo())) {
                        listaTipoMeta.add(fasesCRMEnum.getTipoFase().getCodigo());
                    }
                }

                Integer posicaoTipoMeta = 0;
                for (Integer ordemTipo : listaTipoMeta) {
                    for (MetaCRMTO metaCRMTO : getListaMetaCRM()) {
                        if (metaCRMTO.getTipoFaseCRM().getCodigo() == ordemTipo) {
                            metaCRMTO.setOrdemMeta(posicaoTipoMeta);
                            posicaoTipoMeta++;
                        }
                    }
                }
                Ordenacao.ordenarLista(getListaMetaCRM(), "ordemMeta");

                Date tempoOrdenar2 = Calendario.hoje();
                System.out.println("Ordenar Metas: " + (tempoOrdenar2.getTime() - tempoOrdenar1.getTime()));


                //VERIFICAR A META A SER SELECIONADA E CONSULTADA
                boolean metaSelecionada = false;
                for (MetaCRMTO metaCRMTO : getListaMetaCRM()) {
                    for (TipoMetaCRMTO tipoMetaCRMTO : metaCRMTO.getListaTipoMetaVO()) {
                        if ((tipoMetaCRMTO.getSituacaoAtualMetaEnum().equals(SituacaoAtualMetaEnum.META_NAO_ATENDIDA)) && !tipoMetaCRMTO.getFasesCRMEnum().equals(FasesCRMEnum.INDICACOES) && !tipoMetaCRMTO.getFasesCRMEnum().equals(FasesCRMEnum.AGENDAMENTOS_LIGACOES) && !tipoMetaCRMTO.getFasesCRMEnum().equals(FasesCRMEnum.AGENDAMENTO)) {
                            tipoMetaCRMTO.setSituacaoAtualMetaEnum(SituacaoAtualMetaEnum.META_SELECIONADA);
                            setMetaAtual(tipoMetaCRMTO);
                            this.tipoMetaSelecionada = tipoMetaCRMTO;
                            metaSelecionada = true;
                            setConfPaginacao(new ConfPaginacao(NUMERO_ITENS_CONSULTAR));
                            consultarMetaDetalhada(this.tipoMetaSelecionada, selecionandoFaseOrigemTela);
                            break;
                        }
                    }
                    if (metaSelecionada) {
                        break;
                    }
                }

                if (!metaSelecionada) {
                    Integer metasNaoAtendidas = 0;
                    for (MetaCRMTO metaCRMTO : getListaMetaCRM()) {
                        for (TipoMetaCRMTO tipoMetaCRMTO : metaCRMTO.getListaTipoMetaVO()) {
                            if (tipoMetaCRMTO.getSituacaoAtualMetaEnum().equals(SituacaoAtualMetaEnum.META_NAO_ATENDIDA)) {
                                tipoMetaCRMTO.setSituacaoAtualMetaEnum(SituacaoAtualMetaEnum.META_SELECIONADA);
                                setMetaAtual(tipoMetaCRMTO);
                                this.tipoMetaSelecionada = tipoMetaCRMTO;
                                metaSelecionada = true;
                                setConfPaginacao(new ConfPaginacao(NUMERO_ITENS_CONSULTAR));
                                consultarMetaDetalhada(this.tipoMetaSelecionada, false);
                                metasNaoAtendidas++;
                                break;
                            }
                        }
                        if (metaSelecionada) {
                            break;
                        }
                    }
                    if (metasNaoAtendidas == 0) {
                        setMostrarPanelRealizarContato(false);
                    }
                }

            }
            boolean exiteFase = false;
            Date recep1 = Calendario.hoje();
            try {
                validarPermissao("Passivo", "7.12 - Cliente Potencial", getUsuarioLogado());
                //PASSIVO (RECEPTIVO)
                TipoMetaCRMTO passivo = new TipoMetaCRMTO();
                passivo.setSituacaoAtualMetaEnum(SituacaoAtualMetaEnum.META_ATENDIDA);
                passivo.setFasesCRMEnum(FasesCRMEnum.PASSIVO);
                passivo.setTotalMeta(getFacade().getPassivo().contarPassivoPorPeriodoResponsavelCadastro(getListaUsuariosSelecionados(), getDataInicio(), getDataFim(), getEmpresaLogado().getCodigo()));
                tipoFaseIndicadores.getListaTipoMetaVO().add(passivo);
                exiteFase = true;
            }
            catch (Exception e) {
                System.out.println("Nao tem permissão para operar passivo" + e.getMessage());
            }

            for (int i=0; i<= tipoFaseIndicadores.getListaTipoMetaVO().size()-1; i++){
                if (tipoFaseIndicadores.getListaTipoMetaVO().get(i).getFasesCRMEnum().getDescricao().equals(FasesCRMEnum.getFasePorSigla("IS").getDescricao())){
                    exiteFase = true;
                }
            }
            if(exiteFase) this.getListaMetaCRM().add(tipoFaseIndicadores);

            Date recep2 = Calendario.hoje();
            System.out.println("Montar Indicador Receptivo: " + (recep2.getTime() - recep1.getTime()));

            montarSucesso("");
            setMetasProcessadas(true);
        } catch (Exception e) {
            e.printStackTrace();
            montarErro(e);
        }
    }

    @Override
    protected void consultaPaginada() {
        this.selecionarMeta(getTipoMetaConsultadaAnteriormente());
    }

    public void consultarMetaDetalhada(TipoMetaCRMTO tipoMetaCRMTO, boolean selecionandoFaseOrigemTela) {
        prepararParaConsultar();
        int totalMetaRealizada = 0;
        int totalContatoSemBaterMeta = 0;

        try {
            setExibBotConversa(false);
            List<ConfiguracaoIntegracaoBotConversaVO>  listafluxo = getFacade().getMalaDireta().consultarFluxoEmpresaFase(getEmpresaLogado().getCodigo(), getTipoMetaSelecionada().getFasesCRMEnum().getSigla());
            if(listafluxo.size() > 0) {
                setExibBotConversa(listafluxo.get(0).getFase().equals(getTipoMetaSelecionada().getFasesCRMEnum().getSigla()));
                setNomeFluxoBotconversa(listafluxo.get(0).getDescricao());
                setWebhookBotconversa(listafluxo.get(0).getUrlwebhoobotconversa());
                setCodigoGymBot(listafluxo.get(0).getCodigo());
            }
            setExibGymbotPro(false);
            List<ConfiguracaoIntegracaoGymbotProVO> listafluxoGymbotProVO = getFacade().getMalaDireta().consultarFluxoEmpresaFaseGymbotPro(getEmpresaLogado().getCodigo(), getTipoMetaSelecionada().getFasesCRMEnum().getSigla());
            if (listafluxoGymbotProVO.size() > 0 ? listafluxoGymbotProVO.get(0).getFase().equals(getTipoMetaSelecionada().getFasesCRMEnum().getSigla()) : false) {
                setExibGymbotPro(listafluxoGymbotProVO.get(0).getFase().equals(getTipoMetaSelecionada().getFasesCRMEnum().getSigla()));
                setNomeFluxoGymbotPro(listafluxoGymbotProVO.get(0).getDescricao());
                setTokenGymbotPro(listafluxoGymbotProVO.get(0).getToken());
                setIdFluxoGymbotPro(listafluxoGymbotProVO.get(0).getIdFluxo());
                setCodigoGymBotPro(listafluxoGymbotProVO.get(0).getCodigo());
            }

            List<FecharMetaDetalhadoVO> fechamentosDetalhados = new ArrayList<>();
            // SE FOR FASE AGENDAMENTOS PRESENCIAIS DEVE MOSTRAR SOMENTE OS COM AGENDAMENTO DE AULA EXPERIMENTAL E VISITA
            // LIGAÇÃO DEVE IR PARA OUTRA FASE Agendamentos de Ligações
            if (getTipoMetaSelecionada().getFasesCRMEnum().equals(FasesCRMEnum.PASSIVO)) {
                carregarListaPassivos();
            } else if (getTipoMetaSelecionada().getFasesCRMEnum().equals(FasesCRMEnum.INDICACOES_SEM_CONTATO)) {
                fechamentosDetalhados = getListaIndicacoesSemContato(tipoMetaCRMTO);
                totalContatoSemBaterMeta = getTotalSemBaterMetaIndicadores(tipoMetaCRMTO);
            } else {

                final Boolean metaAgendamento = getTipoMetaSelecionada().getFasesCRMEnum().equals(FasesCRMEnum.AGENDAMENTO);
                final Boolean metaIndicacoes = getTipoMetaSelecionada().getFasesCRMEnum().equals(FasesCRMEnum.INDICACOES);
                final boolean metaAgendamentoLigacoes = getTipoMetaSelecionada().getFasesCRMEnum().equals(FasesCRMEnum.AGENDAMENTOS_LIGACOES);

                fechamentosDetalhados = getFechamentosMeta(tipoMetaCRMTO, metaAgendamento, metaIndicacoes, metaAgendamentoLigacoes);

                tipoMetaCRMTO.setListaMetaDetalhadaFiltradaSemPaginacao(getFacade().getFecharMetaDetalhado().consultarMetaDetalhada(tipoMetaCRMTO.getListaFecharMetaVO(), metaAgendamento, metaAgendamentoLigacoes, metaIndicacoes, getEmpresaLogado().getCodigo()));

                if(tipoMetaCRMTO.getFasesCRMEnum().equals(FasesCRMEnum.CRM_EXTRA)) {
                    for (FecharMetaDetalhadoVO itemMeta : fechamentosDetalhados) {
                        FecharMetaDetalhadoVO itemMetaFull = getFacade().getFecharMetaDetalhado().consultarPorChavePrimaria(itemMeta.getCodigo(), Uteis.NIVELMONTARDADOS_TODOS);
                        HistoricoContatoVO historico = getFacade().getHistoricoContato().consultarUltimoHistoricoPorClienteComDiaFase(itemMeta.getCliente().getCodigo(),  itemMeta.getFecharMeta().getDataRegistro(), FasesCRMEnum.CRM_EXTRA.getSigla(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                        if(historico.getCodigo() != 0 && itemMeta.getSituacaoAtualMetaEnum().equals(SituacaoAtualMetaEnum.META_NAO_ATENDIDA)){
                            itemMeta.setHistoricoContatoVO(UteisValidacao.emptyNumber(itemMetaFull.getHistoricoContatoVO().getCodigo()) ? historico : itemMetaFull.getHistoricoContatoVO());
                            itemMeta.setTeveContato(true);
                            itemMeta.setObteveSucesso(true);
                            itemMeta.setRepescagem(Calendario.maior(historico.getDia(), itemMeta.getFecharMeta().getDataRegistro()));
                            itemMeta.setSituacaoAtualMetaEnum(SituacaoAtualMetaEnum.META_ATENDIDA);
                            getFacade().getFecharMetaDetalhado().processarMetaBatidaAnteriormente(itemMeta, tipoMetaCRMTO.getFasesCRMEnum());
                        }
                    }
                }else if(tipoMetaCRMTO.getFasesCRMEnum().equals(FasesCRMEnum.VINTE_QUATRO_HORAS)){
                    for (FecharMetaDetalhadoVO itemMeta : fechamentosDetalhados) {
                        FecharMetaDetalhadoVO itemMetaFull = getFacade().getFecharMetaDetalhado().consultarPorChavePrimaria(itemMeta.getCodigo(), Uteis.NIVELMONTARDADOS_TODOS);
                        HistoricoContatoVO historico = getFacade().getHistoricoContato().consultarUltimoHistoricoPorClienteComDiaFase(itemMeta.getCliente().getCodigo(),  itemMeta.getFecharMeta().getDataRegistro(), FasesCRMEnum.VINTE_QUATRO_HORAS.getSigla(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                        if(historico.getCodigo() != 0 && itemMeta.getSituacaoAtualMetaEnum().equals(SituacaoAtualMetaEnum.META_NAO_ATENDIDA)){
                            itemMeta.setHistoricoContatoVO(UteisValidacao.emptyNumber(itemMetaFull.getHistoricoContatoVO().getCodigo()) ? historico : itemMetaFull.getHistoricoContatoVO());
                            itemMeta.setTeveContato(true);
                            itemMeta.setObteveSucesso(true);
                            itemMeta.setRepescagem(Calendario.maior(historico.getDia(), itemMeta.getFecharMeta().getDataRegistro()));
                            itemMeta.setSituacaoAtualMetaEnum(SituacaoAtualMetaEnum.META_ATENDIDA);
                            getFacade().getFecharMetaDetalhado().baterMetaPorFase(itemMeta.getCliente().getCodigo(), null, itemMeta.getFecharMeta().getDataRegistro(), FasesCRMEnum.VINTE_QUATRO_HORAS.getSigla(), 0, 0, 0);
                            getFacade().getFecharMetaDetalhado().alterarSomenteCamposHistoricoContato(itemMeta.getHistoricoContatoVO().getCodigo(), itemMeta.getCodigo());
                        }
                    }
                }else if(tipoMetaCRMTO.getFasesCRMEnum().equals(FasesCRMEnum.AGENDAMENTOS_LIGACOES)){
                    for (FecharMetaDetalhadoVO itemMeta : fechamentosDetalhados) {
                        FecharMetaDetalhadoVO itemMetaFull = getFacade().getFecharMetaDetalhado().consultarPorChavePrimaria(itemMeta.getCodigo(), Uteis.NIVELMONTARDADOS_TODOS);
                        HistoricoContatoVO historico = getFacade().getHistoricoContato().consultarUltimoHistoricoPorClienteComDiaFase(itemMeta.getCliente().getCodigo(),  itemMeta.getFecharMeta().getDataRegistro(), FasesCRMEnum.AGENDAMENTOS_LIGACOES.getSigla(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                        if(historico.getCodigo() != 0 && itemMeta.getSituacaoAtualMetaEnum().equals(SituacaoAtualMetaEnum.META_NAO_ATENDIDA)){
                            itemMeta.setHistoricoContatoVO(UteisValidacao.emptyNumber(itemMetaFull.getHistoricoContatoVO().getCodigo()) ? historico : itemMetaFull.getHistoricoContatoVO());
                            itemMeta.setTeveContato(true);
                            itemMeta.setObteveSucesso(true);
                            itemMeta.setRepescagem(Calendario.maior(historico.getDia(), itemMeta.getFecharMeta().getDataRegistro()));
                            itemMeta.setSituacaoAtualMetaEnum(SituacaoAtualMetaEnum.META_ATENDIDA);
                            getFacade().getFecharMetaDetalhado().baterMetaPorFase(itemMeta.getCliente().getCodigo(), null, itemMeta.getFecharMeta().getDataRegistro(), FasesCRMEnum.AGENDAMENTOS_LIGACOES.getSigla(), 0, 0, 0);
                            getFacade().getFecharMetaDetalhado().alterarSomenteCamposHistoricoContato(itemMeta.getHistoricoContatoVO().getCodigo(), itemMeta.getCodigo());
                        }
                    }
                }

                totalMetaRealizada = getTotalFechamentoMetaRealizada(tipoMetaCRMTO, metaAgendamento, metaIndicacoes, metaAgendamentoLigacoes);
                if (!metaAgendamentoLigacoes) {
                    totalContatoSemBaterMeta = getTotalSemBaterMetaFechamento(tipoMetaCRMTO);
                }
            }

            if(!tipoMetaCRMTO.getFasesCRMEnum().equals(FasesCRMEnum.AGENDAMENTO) && !tipoMetaCRMTO.getFasesCRMEnum().equals(FasesCRMEnum.AGENDAMENTOS_LIGACOES) && !tipoMetaCRMTO.getFasesCRMEnum().equals(FasesCRMEnum.LIGACAO_AGENDADOS_AMANHA) ){
                ordenarFechamentosDetalhados(fechamentosDetalhados);
            }
            if (!isFotosNaNuvem()) {
                montarFotos(fechamentosDetalhados);
            }


            preencherDadosAdicionais(fechamentosDetalhados);

            tipoMetaCRMTO.setTotalMetaRealizada(totalMetaRealizada);
            tipoMetaCRMTO.setTotalContatoSemBaterMeta(totalContatoSemBaterMeta);
            tipoMetaCRMTO.setListaMetaDetalhada(fechamentosDetalhados);
            tipoMetaCRMTO.setListaMetaDetalhadaFiltrada(fechamentosDetalhados);
            setListaMetaDetalhadaOriginal(fechamentosDetalhados);

            setTipoMetaConsultadaAnteriormente(tipoMetaCRMTO);

            //se for o clique selecionando a fase da meta, então deve entrar neste método
            // pra já selecionar automaticamente o primeiro da lista
            if (selecionandoFaseOrigemTela) {
                consultarHistoricoContatoClienteAutomatico();
            }

            montarSucesso("");
        } catch (Exception e) {
            montarErro(e);
        }
    }

    private void prepararParaConsultar() {
        setMostrarPanelEmailSMSColetivo(false);
        setMarcarTodosMetaDetalhado(false);
        setBuscarEmTodasFases(false);
        setFiltroClientes("");
        setApresentarBusca(false);
    }

    private void preencherDadosAdicionais(List<FecharMetaDetalhadoVO> fechamentosDetalhados) throws Exception {
        if (getTipoMetaSelecionada().getFasesCRMEnum().equals(FasesCRMEnum.AGENDAMENTOS_LIGACOES)) {
            for (FecharMetaDetalhadoVO fecharMetaDetalhadoVO : fechamentosDetalhados) {
                preencherDadosPessoaisDiaMeta(fecharMetaDetalhadoVO);
                preencherSituacaoClienteAgendamentoLigacao(fecharMetaDetalhadoVO);
            }
        } else {
            for (FecharMetaDetalhadoVO fecharMetaDetalhadoVO : fechamentosDetalhados) {
                preencherDadosPessoaisDiaMeta(fecharMetaDetalhadoVO);
                preencherSituacaoCliente(fecharMetaDetalhadoVO);
            }
        }
    }

    private void ordenarFechamentosDetalhados(List<FecharMetaDetalhadoVO> lista) {
        if (getTipoMetaSelecionada().getFasesCRMEnum().equals(FasesCRMEnum.LEADS_HOJE) || getTipoMetaSelecionada().getFasesCRMEnum().equals(FasesCRMEnum.LEADS_ACUMULADAS)) {
            Ordenacao.ordenarListaReverse(lista, "dataLancamentoConversacao");
        } else {
            Ordenacao.ordenarLista(lista, "nomePessoaRel");
        }
    }

    private int getTotalSemBaterMetaFechamento(TipoMetaCRMTO tipoMetaCRMTO) throws Exception {
        int totalContatoSemBaterMeta;
        totalContatoSemBaterMeta = getFacade()
                .getFecharMetaDetalhado()
                .contarTotalizacaoMetaNaoRealizadaAgendamentoIndicacoes(
                        tipoMetaCRMTO.getListaFecharMetaVO(),
                        getEmpresaLogado().getCodigo());
        return totalContatoSemBaterMeta;
    }

    private int getTotalFechamentoMetaRealizada(TipoMetaCRMTO tipoMetaCRMTO, Boolean metaAgendamento, Boolean metaIndicacoes,
                                                Boolean metaAgendamentoLigacoes) throws Exception {
        int totalMetaRealizada;
        totalMetaRealizada = getFacade()
                .getFecharMetaDetalhado()
                .contarTotalizacaoMetaRealizadaAgendamentoIndicacoesLigacoes(
                        tipoMetaCRMTO.getListaFecharMetaVO(),
                        metaAgendamento,
                        metaAgendamentoLigacoes,
                        metaIndicacoes,
                        getEmpresaLogado().getCodigo(), null);
        return totalMetaRealizada;
    }

    private List<FecharMetaDetalhadoVO> getFechamentosMeta(TipoMetaCRMTO tipoMetaCRMTO, Boolean metaAgendamento,
                                                           Boolean metaIndicacoes, Boolean metaAgendamentoLigacoes) throws Exception {
        return getFacade().getFecharMetaDetalhado().consultarMetaDetalhadaPaginado(
                tipoMetaCRMTO.getListaFecharMetaVO(),
                metaAgendamento,
                metaAgendamentoLigacoes,
                metaIndicacoes,
                getEmpresaLogado().getCodigo(),
                getConfPaginacao());
    }

    private int getTotalSemBaterMetaIndicadores(TipoMetaCRMTO tipoMetaCRMTO) throws Exception {
        int totalContatoSemBaterMeta;
        totalContatoSemBaterMeta = getFacade()
                .getFecharMetaDetalhado()
                .contarTotalizacaoMetaNaoRealizadaIndicadores(tipoMetaCRMTO.getListaFecharMetaVO());
        return totalContatoSemBaterMeta;
    }

    private List<FecharMetaDetalhadoVO> getListaIndicacoesSemContato(TipoMetaCRMTO tipoMetaCRMTO) throws Exception {
        return getFacade().getFecharMetaDetalhado().consultarIndicadores(
                tipoMetaCRMTO.getListaFecharMetaVO(),
                false,
                true,
                false,
                1000);
    }

    private void carregarListaPassivos() throws Exception {
        setPassivoVO(new PassivoVO());
        setHistoricoContatoVO(new HistoricoContatoVO());
        setMostrarPanelAgendarPassivo(false);
        setMostrarPanelObjecaoPassivo(false);
        setSomenteEditarPassivo(false);
        setListaPassivoVO(getFacade().getPassivo().consultarPassivoPorPeriodoResponsavelCadastro(getListaUsuariosSelecionados(), getDataInicio(), getDataFim(), getEmpresaLogado().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
        setListaPassivoFiltradaVO(getListaPassivoVO());
    }

    private void preencherSituacaoCliente(FecharMetaDetalhadoVO fecharMetaDetalhadoVO) {
        if (fecharMetaDetalhadoVO.getObteveSucesso()) {
            fecharMetaDetalhadoVO.setSituacaoAtualMetaEnum(SituacaoAtualMetaEnum.META_ATENDIDA);
        } else if (fecharMetaDetalhadoVO.isTeveContato()) {
            fecharMetaDetalhadoVO.setSituacaoAtualMetaEnum(SituacaoAtualMetaEnum.META_NAO_ATENDIDA_CONTATOREALIZADO);
        } else {
            fecharMetaDetalhadoVO.setSituacaoAtualMetaEnum(SituacaoAtualMetaEnum.META_NAO_ATENDIDA);
        }
    }

    private void preencherSituacaoClienteAgendamentoLigacao(FecharMetaDetalhadoVO fecharMetaDetalhadoVO) {
        if (fecharMetaDetalhadoVO.isTeveContato()) {
            fecharMetaDetalhadoVO.setObteveSucesso(true);
            fecharMetaDetalhadoVO.setSituacaoAtualMetaEnum(SituacaoAtualMetaEnum.META_ATENDIDA);
        } else {
            fecharMetaDetalhadoVO.setObteveSucesso(false);
            fecharMetaDetalhadoVO.setSituacaoAtualMetaEnum(SituacaoAtualMetaEnum.META_NAO_ATENDIDA);
        }
    }

    private void preencherDadosPessoaisDiaMeta(FecharMetaDetalhadoVO fecharMetaDetalhadoVO) throws Exception {
        preencherEmailTelefone(fecharMetaDetalhadoVO);
        fecharMetaDetalhadoVO.setDia(fecharMetaDetalhadoVO.getFecharMeta().getDataRegistro());
        fecharMetaDetalhadoVO.setMetaEmAberto(fecharMetaDetalhadoVO.getFecharMeta().getAberturaMetaVO().getMetaEmAberto());
    }

    public void consultarHistoricoContatoClienteManualCodigoAgendamento(Integer codigoAgendamento) throws Exception {
        TipoMetaCRMTO tipoMetaSelecionada = getTipoMetaSelecionada();
        if (tipoMetaSelecionada != null) {

            FecharMetaDetalhadoVO fecharMetaDetalhadoVO = getFacade().getFecharMetaDetalhado().consultarFecharMetaDetalhadoPorCodigoAgenda(//
                    codigoAgendamento,//
                    Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            if (fecharMetaDetalhadoVO.getCodigo() != null && fecharMetaDetalhadoVO.getCodigo() > 0) {
                for (FecharMetaDetalhadoVO fecharMetaDetalhadoFiltrada : tipoMetaSelecionada.getListaMetaDetalhadaFiltrada()){
                    if (fecharMetaDetalhadoVO.getCodigo().equals(fecharMetaDetalhadoFiltrada.getCodigo())) {
                        consultarHistoricoContatoClienteManual(fecharMetaDetalhadoFiltrada);
                        break;
                    }
                }
            }
        }

    }

    public void consultarHistoricoContatoClienteManual() {
        fecharMetaDetalhadoVO = (FecharMetaDetalhadoVO) context().getExternalContext().getRequestMap().get("metaDetalhada");
        consultarHistoricoContatoClienteManual(fecharMetaDetalhadoVO);
    }

    public void consultarHistoricoContatoClienteManual(FecharMetaDetalhadoVO obj) {
        Date d1 = Calendario.hoje();
        try {

            validarPermissao("RealizarContato", "7.23 - Realizar Contato", getUsuarioLogado());
//            FecharMetaDetalhadoVO fecharMetaDetalhadoVO = getFacade().getFecharMetaDetalhado().consultarPorChavePrimaria(obj.getCodigo(), 1000);

            consultarHistoricoContatoCliente(obj);
            listaClientesFiltrada();
        } catch (Exception e) {
            montarErro(e);
        }
        Date d2 = Calendario.hoje();
        System.out.println("Consultar Historico de Contato do Cliente: " + (d2.getTime() - d1.getTime()));
    }

    public void consultarHistoricoContatoClienteAutomatico() {
        try {
            FecharMetaDetalhadoVO fecharMetaDetalhadoVOSelecionar = new FecharMetaDetalhadoVO();
            if (getMetaIndicacao()) {
                setMostrarPanelRealizarContato(false);
            } else {
                setMostrarPanelRealizarContato(false);
                for (FecharMetaDetalhadoVO fecharMetaDetalhadoVO : getTipoMetaSelecionada().getListaMetaDetalhada()) {
                    if (fecharMetaDetalhadoVO.getSituacaoAtualMetaEnum() == SituacaoAtualMetaEnum.META_NAO_ATENDIDA || fecharMetaDetalhadoVO.getSituacaoAtualMetaEnum() == SituacaoAtualMetaEnum.META_NAO_ATENDIDA_CONTATOREALIZADO) {
                        fecharMetaDetalhadoVOSelecionar = fecharMetaDetalhadoVO;
                        setMostrarPanelRealizarContato(true);
                        break;
                    }
                }
            }

            consultarHistoricoContatoCliente(fecharMetaDetalhadoVOSelecionar);
            setFecharMetaDetalhadoVO(fecharMetaDetalhadoVOSelecionar);
        } catch (Exception e) {
            montarErro(e);
        }
    }




    public List<MetaCRMTO> getListaMetaCRM() {
        if(listaMetaCRM == null){
            listaMetaCRM = new ArrayList<MetaCRMTO>();
        }
        return listaMetaCRM;
    }

    public void setListaMetaCRM(List<MetaCRMTO> listaMetaCRM) {
        this.listaMetaCRM = listaMetaCRM;
    }

    public TipoMetaCRMTO getTipoMetaSelecionada() {
        if(tipoMetaSelecionada == null) {
            tipoMetaSelecionada = new TipoMetaCRMTO();
        }
        return tipoMetaSelecionada;
    }

    public void setTipoMetaSelecionada(TipoMetaCRMTO tipoMetaSelecionada) {
        this.tipoMetaSelecionada = tipoMetaSelecionada;
    }

    public List<HistoricoContatoVO> getListaHistoricoContatoCliente() {
        return listaHistoricoContatoCliente;
    }

    public void setListaHistoricoContatoCliente(List<HistoricoContatoVO> listaHistoricoContatoCliente) {
        this.listaHistoricoContatoCliente = listaHistoricoContatoCliente;
    }

    public List<HistoricoContatoVO> getListaHistoricoContatoClienteSemLimite() {
        return listaHistoricoContatoClienteSemLimite;
    }

    public void setListaHistoricoContatoClienteSemLimite(List<HistoricoContatoVO> listaHistoricoContatoClienteSemLimite) {
        this.listaHistoricoContatoClienteSemLimite = listaHistoricoContatoClienteSemLimite;
    }

    public FecharMetaDetalhadoVO getMetaDetalhadoVOSelecionado() {
        if(metaDetalhadoVOSelecionado == null){
            metaDetalhadoVOSelecionado = new FecharMetaDetalhadoVO();
        }
        return metaDetalhadoVOSelecionado;
    }

    public void setMetaDetalhadoVOSelecionado(FecharMetaDetalhadoVO metaDetalhadoVOSelecionado) {
        this.metaDetalhadoVOSelecionado = metaDetalhadoVOSelecionado;
    }

    public void selecionarMetaPorFase(FasesCRMEnum fasesCRM) {
        for (MetaCRMTO metaCRMTO : getListaMetaCRM()){
            for (TipoMetaCRMTO tipoMetaCRMTO : metaCRMTO.getListaTipoMetaVO()) {
                if (tipoMetaCRMTO.getFasesCRMEnum() == fasesCRM) {
                    selecionarMeta(tipoMetaCRMTO);
                    return;
                }
            }
        }
    }

    public void selecionarFaseMeta() {
        setConfPaginacao(new ConfPaginacao(NUMERO_ITENS_CONSULTAR));
        TipoMetaCRMTO obj = (TipoMetaCRMTO) context().getExternalContext().getRequestMap().get("tipoMeta");
        try {
            atualizarMetas();
            obj.setSituacaoAtualMetaEnum(SituacaoAtualMetaEnum.META_SELECIONADA);
            setTipoMetaSelecionada(obj);
            consultarMetaDetalhada(obj, true);
        } catch (Exception e) {
            setSucesso(false);
            setErro(true);
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public void selecionarMeta(TipoMetaCRMTO obj) {
        try {
            atualizarMetas();
            obj.setSituacaoAtualMetaEnum(SituacaoAtualMetaEnum.META_SELECIONADA);
            setTipoMetaSelecionada(obj);
            consultarMetaDetalhada(obj, false);
        } catch (Exception e) {
            setSucesso(false);
            setErro(true);
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    private void atualizarMetas() {
        for (MetaCRMTO metaCRMTO : getListaMetaCRM()) {
            if (metaCRMTO.getTipoFaseCRM().equals(TipoFaseCRM.INDICADORES)) {
                for (TipoMetaCRMTO tipoMetaCRMTO : metaCRMTO.getListaTipoMetaVO()) {
                    tipoMetaCRMTO.setSituacaoAtualMetaEnum(SituacaoAtualMetaEnum.META_ATENDIDA);
                }
            } else {
                for (TipoMetaCRMTO tipoMetaCRMTO : metaCRMTO.getListaTipoMetaVO()) {
                    Integer meta = tipoMetaCRMTO.getTotalMeta();
                    Integer realizado = tipoMetaCRMTO.getTotalMetaRealizada();
                    Integer contatoSemBater = tipoMetaCRMTO.getTotalContatoSemBaterMeta();
                    Integer realizadoMAIScontatoSemBater = realizado + contatoSemBater;

                    if (realizado >= meta) {
                        tipoMetaCRMTO.setSituacaoAtualMetaEnum(SituacaoAtualMetaEnum.META_ATENDIDA);
                    } else if ((realizadoMAIScontatoSemBater >= meta) && (contatoSemBater != 0)) {
                        tipoMetaCRMTO.setSituacaoAtualMetaEnum(SituacaoAtualMetaEnum.META_NAO_ATENDIDA_CONTATOREALIZADO);
                    } else if (realizadoMAIScontatoSemBater < meta) {
                        tipoMetaCRMTO.setSituacaoAtualMetaEnum(SituacaoAtualMetaEnum.META_NAO_ATENDIDA);
                    }
                }
            }
        }
    }

    public void consultarHistoricoContatoCliente(FecharMetaDetalhadoVO detalhadoSelecionado) {
        try {
            montarListaPesquisa();
            setCodigoModeloMensagemCriado(0);
            setMostrarPanelEmailSMSColetivo(false);

            novoHistoricoContato();

            if (detalhadoSelecionado.getCodigo() != 0) {
                setMostrarPanelRealizarContato(true);
                setListaHistoricoContatoClienteSemLimite(getFacade().getHistoricoContato().consultarHistoricoContatoVO(detalhadoSelecionado, false));
            } else {
                setMostrarPanelRealizarContato(false);
            }

            //COLABORADOR RESPONSAVEL
            setColaboradorResponsavel(detalhadoSelecionado.getFecharMeta().getAberturaMetaVO().getColaboradorResponsavel());

            for (FecharMetaDetalhadoVO fecharMetaDetalhadoVO1 : this.getTipoMetaSelecionada().getListaMetaDetalhada()) {
                //A FORMA COMO É DEFINIDA A SITUAÇÃO DOS CLIENTE DE AGENDAMENTO DE LIGAÇÃO É DIFERENTE DAS DEMAIS.
                if (getTipoMetaSelecionada().getFasesCRMEnum().equals(FasesCRMEnum.AGENDAMENTOS_LIGACOES) || getTipoMetaSelecionada().getFasesCRMEnum().equals(FasesCRMEnum.AGENDAMENTO)) {
                    if (fecharMetaDetalhadoVO1.isTeveContato()) {
                        fecharMetaDetalhadoVO1.setObteveSucesso(true);
                        fecharMetaDetalhadoVO1.setSituacaoAtualMetaEnum(SituacaoAtualMetaEnum.META_ATENDIDA);
                        if(!getFacade().getHistoricoContato().existeHistoricoDataExpecifica(fecharMetaDetalhadoVO1)){
                            fecharMetaDetalhadoVO1.setRepescagem(true);
                        }
                    } else {
                        fecharMetaDetalhadoVO1.setObteveSucesso(false);
                        fecharMetaDetalhadoVO1.setSituacaoAtualMetaEnum(SituacaoAtualMetaEnum.META_NAO_ATENDIDA);
                    }

                } else if (!getTipoMetaSelecionada().getFasesCRMEnum().equals(FasesCRMEnum.AGENDAMENTOS_LIGACOES)) {
                    if (fecharMetaDetalhadoVO1.getObteveSucesso()) {
                        fecharMetaDetalhadoVO1.setSituacaoAtualMetaEnum(SituacaoAtualMetaEnum.META_ATENDIDA);
                    } else if (getFacade().getHistoricoContato().existeHistoricoDataExpecifica(fecharMetaDetalhadoVO1)) {
                        fecharMetaDetalhadoVO1.setSituacaoAtualMetaEnum(SituacaoAtualMetaEnum.META_NAO_ATENDIDA_CONTATOREALIZADO);
                    } else {
                        fecharMetaDetalhadoVO1.setSituacaoAtualMetaEnum(SituacaoAtualMetaEnum.META_NAO_ATENDIDA);
                    }
                }
            }
            //MARCAR O CLIENTE COMO SELECIONADO
            if (detalhadoSelecionado.getCodigo() != 0) {
                detalhadoSelecionado.setSituacaoAtualMetaEnum(SituacaoAtualMetaEnum.META_SELECIONADA);

                List<ConfiguracaoIntegracaoBotConversaVO>  listafluxo = getFacade().getMalaDireta().consultarFluxoEmpresaFase(getEmpresaLogado().getCodigo(),  getTipoMetaSelecionada().getFasesCRMEnum().getSigla());
                if(listafluxo.size() > 0 ? listafluxo.get(0).getFase().equals(detalhadoSelecionado.getFecharMeta().getIdentificadorMeta()) : false) {
                    setExibBotConversa(true);
                    setNomeFluxoBotconversa(listafluxo.get(0).getDescricao());
                    setWebhookBotconversa(listafluxo.get(0).getUrlwebhoobotconversa());
                    setNomeCliente(detalhadoSelecionado.getNomePessoaRel());
                }

            } else {
                List <FecharMetaDetalhadoVO> listaFecharMetaDetalhadoVO = getTipoMetaSelecionada().getListaMetaDetalhada();
                if (listaFecharMetaDetalhadoVO.size() != 0) {
                    detalhadoSelecionado = listaFecharMetaDetalhadoVO.stream().findFirst().get();
                    detalhadoSelecionado.setSituacaoAtualMetaEnum(SituacaoAtualMetaEnum.META_SELECIONADA);
                    setMostrarPanelRealizarContato(true);
                }
            }

            this.getTipoMetaSelecionada().setListaMetaDetalhadaFiltrada(this.getTipoMetaSelecionada().getListaMetaDetalhada());
            //this.getTipoMetaSelecionada().setListaMetaDetalhadaFiltrada(Ordenacao.ordenarLista(this.getTipoMetaSelecionada().getListaMetaDetalhada(), "codigoSituacaoAtualMetaEnum"));

            setMetaDetalhadoVOSelecionado(detalhadoSelecionado);
            if (FasesCRMEnum.LEADS_HOJE.getSigla().equals(detalhadoSelecionado.getFecharMeta().getIdentificadorMeta()) ||
                    FasesCRMEnum.LEADS_ACUMULADAS.getSigla().equals(detalhadoSelecionado.getFecharMeta().getIdentificadorMeta())) {
                if (detalhadoSelecionado.getIndicado().getCodigo() > 0) {
                    detalhadoSelecionado.setIndicado(getFacade().getIndicado().consultarPorChavePrimaria(detalhadoSelecionado.getIndicado().getCodigo(), Uteis.NIVELMONTARDADOS_HISTORICOINDICADO));
                    detalhadoSelecionado.getIndicado().getIndicacaoVO().setDia(detalhadoSelecionado.getDataLancamentoConversacao());
                } else if (detalhadoSelecionado.getCliente().getCodigo() > 0){
                    detalhadoSelecionado.setIndicado(getFacade().getIndicado().consultarPorCodigoCliente(detalhadoSelecionado.getCliente().getCodigo(), Uteis.NIVELMONTARDADOS_HISTORICOINDICADO));
                    detalhadoSelecionado.getIndicado().getIndicacaoVO().setDia(detalhadoSelecionado.getDataLancamentoConversacao());
                }
                if (TipoLeadEnum.GENERICO.equals(detalhadoSelecionado.getConversaoLeadVO().getLead().getTipo())
                        && !UteisValidacao.emptyString(detalhadoSelecionado.getConversaoLeadVO().getMensagem())) {
                    detalhadoSelecionado.getIndicado().getIndicacaoVO().setObservacao(detalhadoSelecionado.getConversaoLeadVO().getMensagem());
                }
            }

            if (getMetaDetalhadoVOSelecionado().getFecharMeta().getCodigo() != 0) {
                getMetaDetalhadoVOSelecionado().getFecharMeta().setAberturaMetaVO(getFacade().getAberturaMeta().consultarAberturaPorFecharMeta(getMetaDetalhadoVOSelecionado().getFecharMeta().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
            }

            if (getMetaDetalhadoVOSelecionado().getCodigo() != 0) {
                Integer usuarioMeta = getMetaDetalhadoVOSelecionado().getFecharMeta().getAberturaMetaVO().getColaboradorResponsavel().getCodigo();
                getMetaDetalhadoVOSelecionado().getFecharMeta().getAberturaMetaVO().setColaboradorResponsavel(getFacade().getUsuario().consultarPorCodigo(usuarioMeta, Uteis.NIVELMONTARDADOS_METACOLABORADORRESPONSAVEL));
            }

            if (getMetaDetalhadoVOSelecionado().getCliente().getCodigo() != 0) {
                getMetaDetalhadoVOSelecionado().getCliente().setPessoa(getFacade().getPessoa().consultarPorChavePrimaria(getMetaDetalhadoVOSelecionado().getCliente().getPessoa().getCodigo(), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA));
                preencherFasesDoContato(getMetaDetalhadoVOSelecionado());
            } else {
                setFasesDoContato("");
            }

            if(detalhadoSelecionado.getCliente().getCodigo() != 0) {
                //Salvar historico no cliente, já que o mesmo possui cadastro.
                this.getHistoricoContatoVO().setClienteVO(detalhadoSelecionado.getCliente());

                //CONSULTAR AS INFORMAÇÕES DO PASSIVO
            }else if (detalhadoSelecionado.getPassivo().getCodigo() != 0) {
                this.getHistoricoContatoVO().setPassivoVO(getFacade().getPassivo().consultarPorChavePrimaria(detalhadoSelecionado.getPassivo().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSENTIDADESPRINCIPAIS));

                PassivoVO passivoVOTemp = this.getHistoricoContatoVO().getPassivoVO();
                String email = passivoVOTemp.getEmail();
                String celular = passivoVOTemp.getTelefoneCelular();
                String residencial = passivoVOTemp.getTelefoneResidencial();
                String trabalho = passivoVOTemp.getTelefoneTrabalho();

                if (!UteisValidacao.emptyString(email)) {
                    EmailVO emailVO = new EmailVO();
                    emailVO.setEmail(email);
                    getListaEmail().add(emailVO);
                }

                if (!UteisValidacao.emptyString(celular)) {
                    TelefoneVO telefoneVO = new TelefoneVO();
                    telefoneVO.setNumero(celular);
                    telefoneVO.setTipoTelefone(TipoTelefoneEnum.CELULAR.getCodigo());
                    getListaTelefones().add(telefoneVO);
                }

                if (!UteisValidacao.emptyString(residencial)) {
                    TelefoneVO telefoneVO = new TelefoneVO();
                    telefoneVO.setNumero(residencial);
                    telefoneVO.setTipoTelefone(TipoTelefoneEnum.RESIDENCIAL.getCodigo());
                    getListaTelefones().add(telefoneVO);
                }

                if (!UteisValidacao.emptyString(trabalho)) {
                    TelefoneVO telefoneVO = new TelefoneVO();
                    telefoneVO.setNumero(trabalho);
                    telefoneVO.setTipoTelefone(TipoTelefoneEnum.COMERCIAL.getCodigo());
                    getListaTelefones().add(telefoneVO);
                }


                //CONSULTAR AS INFORMAÇÕES DO INDICADO
            } else if (detalhadoSelecionado.getIndicado().getCodigo() != 0) {
                this.getHistoricoContatoVO().setIndicadoVO(getFacade().getIndicado().consultarPorChavePrimaria(detalhadoSelecionado.getIndicado().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSENTIDADESPRINCIPAIS));


                IndicadoVO indicadoVOTemp = this.getHistoricoContatoVO().getIndicadoVO();
                String email = indicadoVOTemp.getEmail();
                String celular = indicadoVOTemp.getTelefoneIndicado();
                String residencial = indicadoVOTemp.getTelefone();

                if (!UteisValidacao.emptyString(email)) {
                    EmailVO emailVO = new EmailVO();
                    emailVO.setEmail(email);
                    getListaEmail().add(emailVO);
                }

                if (!UteisValidacao.emptyString(celular)) {
                    TelefoneVO telefoneVO = new TelefoneVO();
                    telefoneVO.setNumero(celular);
                    telefoneVO.setTipoTelefone(TipoTelefoneEnum.CELULAR.getCodigo());
                    getListaTelefones().add(telefoneVO);
                }

                if (!UteisValidacao.emptyString(residencial)) {
                    TelefoneVO telefoneVO = new TelefoneVO();
                    telefoneVO.setNumero(residencial);
                    telefoneVO.setTipoTelefone(TipoTelefoneEnum.RESIDENCIAL.getCodigo());
                    getListaTelefones().add(telefoneVO);
                }

                //Pegando Indicacao
                getHistoricoContatoVO().getIndicadoVO().setIndicacaoVO(detalhadoSelecionado.getIndicado().getIndicacaoVO());

            }


            //PREENCHER: DATA CADASTRO - ÚLTIMO ACESSO - VENCIMENTO
            //CONTABILIZAR: LIGAÇÕES - EMAIL - CONTATO PESSOAL - SMS - APP
            if (this.getHistoricoContatoVO().getClienteVO().getCodigo() != 0 || this.getHistoricoContatoVO().getPassivoVO().getCodigo() != 0 || this.getHistoricoContatoVO().getIndicadoVO().getCodigo() != 0) {
                getFacade().getHistoricoContato().preencherHistoricoContato(this.getHistoricoContatoVO());
            }

            if (getMetaDetalhadoVOSelecionado().getOrigem().equals("AGENDA")) {
                getMetaDetalhadoVOSelecionado().setAgenda(getFacade().getAgenda().consultarValidandoMeta(getMetaDetalhadoVOSelecionado(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
            }

            // inicializarPaginacao();
            setListaHistoricoContatoCliente(new ArrayList<HistoricoContatoVO>());
            setListaHistoricoObjecoesCliente(new ArrayList<HistoricoContatoVO>());
            if (detalhadoSelecionado.getCliente().getCodigo() != 0 || detalhadoSelecionado.getPassivo().getCodigo() != 0 || detalhadoSelecionado.getIndicado().getCodigo() != 0) {
                setListaHistoricoContatoCliente(getFacade().getHistoricoContato().consultarHistoricoContatoVO(detalhadoSelecionado, false));
            }

            if (!getListaHistoricoContatoCliente().isEmpty()) {
                setListaHistoricoObjecoesCliente(new ArrayList<HistoricoContatoVO>());
                for (HistoricoContatoVO obj : getListaHistoricoContatoCliente()) {
                    if (obj.getObjecaoVO() != null && obj.getObjecaoVO().getCodigo() != 0) {
                        getListaHistoricoObjecoesCliente().add(obj);
                    }
                }
            }

            //PREENCHER O TELEFONES E EMAILS DO HistoricoContatoVO
            if (!this.getHistoricoContatoVO().getClienteVO().getCodigo().equals(0)) {
                this.getHistoricoContatoVO().getClienteVO().setSituacaoClienteSinteticoVO(getFacade().getSituacaoClienteSinteticoDW().consultarCliente(this.getHistoricoContatoVO().getClienteVO().getCodigo(), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA));
                this.getHistoricoContatoVO().getClienteVO().getPessoa().setTelefoneVOs(getFacade().getTelefone().consultarTelefones(this.getHistoricoContatoVO().getClienteVO().getPessoa().getCodigo(), false, Uteis.NIVELMONTARDADOS_DADOSBASICOS));
                this.getHistoricoContatoVO().getClienteVO().getPessoa().setEmailVOs(getFacade().getEmail().consultarEmails(this.getHistoricoContatoVO().getClienteVO().getPessoa().getCodigo(), false, Uteis.NIVELMONTARDADOS_DADOSBASICOS));
                setListaEmail(this.getHistoricoContatoVO().getClienteVO().getPessoa().getEmailVOs());
                consultarListaTelefones();
            }

            if (getMetaIndicacaoSemContato()) {
                selecionarIndicado(detalhadoSelecionado);
                return;
            }

            if (getTipoMetaSelecionada().getFasesCRMEnum().equals(FasesCRMEnum.POS_VENDA) && getMetaDetalhadoVOSelecionado().getConfiguracaoDiasPosVendaVO().getCodigo() != 0) {
                ConfiguracaoDiasPosVendaVO configuracaoDiasPosVendaVO = getFacade().getConfiguracaoDiasPosVenda().consultarPorChavePrimaria(getMetaDetalhadoVOSelecionado().getConfiguracaoDiasPosVendaVO().getCodigo(), Uteis.NIVELMONTARDADOS_TODOS);
                getMetaDetalhadoVOSelecionado().setConfiguracaoDiasPosVendaVO(configuracaoDiasPosVendaVO);
            }

            this.getHistoricoContatoVO().setTipoContato("TE");
            this.getHistoricoContatoVO().setObservacao("");
            this.getHistoricoContatoVO().setNovoObj(true);
            this.getHistoricoContatoVO().setAberturaMetaEstaEmAberta(getMetaDetalhadoVOSelecionado().getMetaEmAberto());
            this.getHistoricoContatoVO().setDiaAbertura(getMetaDetalhadoVOSelecionado().getDia());
            this.getHistoricoContatoVO().setDia(Calendario.hoje());
            this.getHistoricoContatoVO().setResponsavelCadastro(getUsuarioLogado());
            this.getHistoricoContatoVO().setColaboradorResponsavel(getUsuarioLogado());
            this.getHistoricoContatoVO().setFase(getTipoMetaSelecionada().getFasesCRMEnum().getSigla());
            this.getHistoricoContatoVO().setCodigoFecharMetaDetalhado(detalhadoSelecionado.getCodigo());

            if (UteisValidacao.emptyNumber(detalhadoSelecionado.getCodigo())) {
                logarHistoricoContatoVO("HistoricoContatoVO sem código fechar meta detalhado. ", historicoContatoVO);
            }

        } catch (Exception e) {
            setMensagemRealizarContato(e.getMessage());
            setSucesso(false);
            setErro(true);
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public Integer getTotalMetas() {
        Integer totalMetas = 0;
        try{
            if (!UteisValidacao.emptyList(getListaMetaCRM())) {
                for (MetaCRMTO metaCRMTO : getListaMetaCRM()) {
                    //OS INDICADORES NÃO ENTRA NO TOTAL DE METAS
                    if (!metaCRMTO.getTipoFaseCRM().equals(TipoFaseCRM.INDICADORES)) {
                        for (TipoMetaCRMTO tipoMetaCRMTO : metaCRMTO.getListaTipoMetaVO()) {
                            if (!tipoMetaCRMTO.getFasesCRMEnum().equals(FasesCRMEnum.AGENDAMENTOS_LIGACOES) && !tipoMetaCRMTO.getFasesCRMEnum().equals(FasesCRMEnum.INDICACOES) && !tipoMetaCRMTO.getFasesCRMEnum().getTipoFase().equals(TipoFaseCRM.CRMEXTRA)) {
                                totalMetas += tipoMetaCRMTO.getTotalMeta();
                            }
                        }
                    }
                }
            }
        } catch (Exception e) {
            setSucesso(false);
            setErro(true);
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
        return totalMetas;
    }

    public Double getPorcentagemTotalMetasRealizado() {
        Double porcentagemRealizada = 0.0;
        try {
            if (!UteisValidacao.emptyList(getListaMetaCRM())) {
                Integer totalMetasRealizadas = 0;
                Double totalMetas = getTotalMetas().doubleValue();
                for (MetaCRMTO metaCRMTO : getListaMetaCRM()) {
                    //OS INDICADORES NÃO ENTRA NO TOTAL DE METAS
                    if (!metaCRMTO.getTipoFaseCRM().equals(TipoFaseCRM.INDICADORES)) {
                        for (TipoMetaCRMTO tipoMetaCRMTO : metaCRMTO.getListaTipoMetaVO()) {
                            if (!tipoMetaCRMTO.getFasesCRMEnum().equals(FasesCRMEnum.AGENDAMENTOS_LIGACOES) && !tipoMetaCRMTO.getFasesCRMEnum().equals(FasesCRMEnum.INDICACOES) && !tipoMetaCRMTO.getFasesCRMEnum().getTipoFase().equals(TipoFaseCRM.CRMEXTRA)) {
                                totalMetasRealizadas += tipoMetaCRMTO.getTotalMetaRealizada();
                            }
                        }
                    }
                }
                if (totalMetas == 0.0) {
                    porcentagemRealizada = 0.0;
                } else {
                    porcentagemRealizada = (100 * totalMetasRealizadas) / totalMetas;
                }
            }
        } catch (Exception e) {
            setSucesso(false);
            setErro(true);
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
        return porcentagemRealizada;
    }

    public Integer getTotalMetasMetaExtra() {
        Integer totalMetas = 0;
        try{
            if (!UteisValidacao.emptyList(getListaMetaCRM())) {
                for (MetaCRMTO metaCRMTO : getListaMetaCRM()) {
                    if (metaCRMTO.getTipoFaseCRM().equals(TipoFaseCRM.CRMEXTRA)) {
                        for (TipoMetaCRMTO tipoMetaCRMTO : metaCRMTO.getListaTipoMetaVO()) {
                            totalMetas += tipoMetaCRMTO.getTotalMeta();
                        }
                    }
                }
            }
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
        return totalMetas;
    }

    public Double getPorcentagemTotalMetasRealizadoMetaExtra() {
        Double porcentagemRealizada = 0.0;
        try{
            if (!UteisValidacao.emptyList(getListaMetaCRM())) {
                Integer totalMetasRealizadas = 0;
                Double totalMetas = getTotalMetasMetaExtra().doubleValue();
                for (MetaCRMTO metaCRMTO : getListaMetaCRM()) {
                    if (metaCRMTO.getTipoFaseCRM().equals(TipoFaseCRM.CRMEXTRA)) {
                        for (TipoMetaCRMTO tipoMetaCRMTO : metaCRMTO.getListaTipoMetaVO()) {
                            totalMetasRealizadas += tipoMetaCRMTO.getTotalMetaRealizada();
                        }
                    }
                }
                if (totalMetas == 0.0) {
                    porcentagemRealizada = 0.0;
                } else {
                    porcentagemRealizada = (100 * totalMetasRealizadas) / totalMetas;
                }
            }
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
        return porcentagemRealizada;
    }

    public String getPorcentagemTotalMetasRealizadoMetaExtra_Apresentar() {
        try {
            return Formatador.formatarValorMonetarioSemMoeda(getPorcentagemTotalMetasRealizadoMetaExtra()) + "%";
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
        return "";
    }

    public String getPorcentagemTotalMetasRealizado_Apresentar() {
            try {
                return Formatador.formatarValorMonetarioSemMoeda(getPorcentagemTotalMetasRealizado()) + "%";
            } catch (Exception e) {
                setMensagemDetalhada("msg_erro", e.getMessage());
            }
            return "";
    }

    public void irParaTelaCliente() {
        FecharMetaDetalhadoVO obj = (FecharMetaDetalhadoVO) context().getExternalContext().getRequestMap().get("metaDetalhada");
        try {
            if (obj == null) {
                throw new Exception("Cliente não encontrado.");
            } else {
                ClienteVO clienteVO = new ClienteVO();
                clienteVO.setCodigo(obj.getCliente().getCodigo());
                irParaTelaCliente(clienteVO);
                setOnComplete("abrirPopup('clienteNav.jsp?page=viewCliente', 'Cliente', 1024, 700);");
            }
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public String getOnComplete() {
        if (onComplete == null) {
            onComplete = "";
        }
        return onComplete;
    }

    public void setOnComplete(String onComplete) {
        this.onComplete = onComplete;
    }

    public void listaClientesFiltrada() {
        try {
            if (isBuscarEmTodasFases()) {
                buscarEmTotasAsFases();
            }

            setListaOriginal((ArrayList) tipoMetaSelecionada.getListaMetaDetalhada());

            if (getFiltroClientes().isEmpty()) {
                setListaManipulavel(getListaOriginal() != null ? (ArrayList) getListaOriginal().clone() : new ArrayList());
            } else {
                setListaManipulavel(getListaOriginal() != null ? (ArrayList) getListaOriginal().clone() : new ArrayList());
                List listaAssociada = new ArrayList(getListaManipulavel());
                for (Object object : listaAssociada) {
                    FecharMetaDetalhadoVO detalhadoVO = (FecharMetaDetalhadoVO) object;

                    if (detalhadoVO.getCliente().getCodigo() != 0) {
                        if (!detalhadoVO.getCliente().getPessoa().getNome().toLowerCase().contains(getFiltroClientes().toLowerCase())
                                && !detalhadoVO.getMatricula().contains(getFiltroClientes())) {
                            getListaManipulavel().remove(object);
                        }
                    } else if (detalhadoVO.getPassivo().getCodigo() != 0) {
                        if (!detalhadoVO.getPassivo().getNome().toLowerCase().contains(getFiltroClientes().toLowerCase())) {
                            getListaManipulavel().remove(object);
                        }
                    } else if (detalhadoVO.getIndicado().getCodigo() != 0) {
                        if (!detalhadoVO.getIndicado().getNomeIndicado().toLowerCase().contains(getFiltroClientes().toLowerCase())) {
                            getListaManipulavel().remove(object);
                        }
                    }
                }
            }
            tipoMetaSelecionada.setListaMetaDetalhadaFiltrada(getListaManipulavel());
        } catch (Exception e) {
            montarErro(e);
        }
    }

    public String getFiltroClientes() {
        if (filtroClientes == null) {
            filtroClientes = "";
        }
        return filtroClientes;
    }

    public void setFiltroClientes(String filtroClientes) {
        this.filtroClientes = filtroClientes;
    }

    public void montarFotos(List<FecharMetaDetalhadoVO> fecharMetaDetalhadoVOs) {
        for (FecharMetaDetalhadoVO clientes : fecharMetaDetalhadoVOs) {
            this.fotosClientes.put(clientes.getCliente().getPessoa().getCodigo(), clientes.getCliente().getPessoa().getFoto());
        }
    }

    public void paintFotoCliente(OutputStream out, Object data) {
        try {
            HttpServletRequest request = (HttpServletRequest) FacesContext.getCurrentInstance().getExternalContext().getRequest();
            String pessoa = request.getParameter("pessoa");
            SuperControle.paintFoto(out, fotosClientes.get(Integer.valueOf(pessoa)));
        } catch (Exception ignored){
        }
    }

    public String getMensagemRealizarContato() {
        if (mensagemRealizarContato == null) {
            mensagemRealizarContato = "";
        }
        return mensagemRealizarContato;
    }

    public void setMensagemRealizarContato(String mensagemRealizarContato) {
        this.mensagemRealizarContato = mensagemRealizarContato;
    }

    public Object alterarMeioEnvio() {
        setPesquisa(0);
        getMalaDiretaVO().setModeloMensagem(new ModeloMensagemVO());
        setCodigoModeloMensagemCriado(0);
        setMostrarPanelAgendar(false);
        setMostrarPanelObjecao(false);
        setMostrarPanelIndicacao(false);
        setMostrarModeloDeMensagem(false);
        setMostrarPanelReagendamento(false);
        setApresentarPanelEnviarEmail(false);
        temUsuarioMovel = false;
        campos = new CamposGenericosTO();
        if (this.getHistoricoContatoVO().getTipoContato().equals("CS")) {
            meioEnvio = MeioEnvio.SMS;
        } else if (this.getHistoricoContatoVO().getTipoContato().equals("EM")) {
            meioEnvio = MeioEnvio.EMAIL;
        } else if (this.getHistoricoContatoVO().getTipoContato().equals("AP")) {
            meioEnvio = MeioEnvio.APP;
            try {
                temUsuarioMovel = getFacade().getUsuarioMovel().temUsuarioMovel(historicoContatoVO.getClienteVO().getCodigo());
            } catch (Exception e) {
                Uteis.logar(e, HistoricoContatoControle.class);
            }
        }else{
            meioEnvio = null;
        }
        return meioEnvio;
    }

    public void setHistoricoContatoVO(HistoricoContatoVO historicoContatoVO) {
        this.historicoContatoVO = historicoContatoVO;
    }

    public HistoricoContatoVO getHistoricoContatoVO() {
        if (historicoContatoVO == null) {
            historicoContatoVO = new HistoricoContatoVO();
        }
        return historicoContatoVO;
    }

    public CamposGenericosTO getCampos() {
        return campos;
    }

    public void setCampos(CamposGenericosTO campos) {
        this.campos = campos;
    }

    public boolean isTemUsuarioMovel() {
        return temUsuarioMovel;
    }

    public void setTemUsuarioMovel(boolean temUsuarioMovel) {
        this.temUsuarioMovel = temUsuarioMovel;
    }

    public List getListaTipoContato() throws Exception {

        //ORDEM DEVIDA NO PROJETO
        String[] ordemTipoContato = "TE,WA,AP,CS,EM,LC,PE".split(",");

        List<SelectItem> objs = new ArrayList<SelectItem>();
        Hashtable tipoContato = (Hashtable) Dominios.getTipoContatoHistoricoContato();
        Enumeration keys = tipoContato.keys();
        while (keys.hasMoreElements()) {
            String value = (String) keys.nextElement();
            String label = (String) tipoContato.get(value);
            objs.add(new SelectItem(value, label));
        }

        List<SelectItem> objsOrdenada = new ArrayList<SelectItem>();
        for (String ordem : ordemTipoContato) {
            for (SelectItem obj : objs) {
                if (obj.getValue().equals(ordem)) {
                    objsOrdenada.add(obj);
                    break;
                }
            }

        }
        return objsOrdenada;
    }

    public List getListaGrauSatisfacao() {
        List<SelectItem> objs = new ArrayList<SelectItem>();
        objs.add(new SelectItem("", "Grau de Satisfação"));
        for (GrauSatisfacaoEnum grau : GrauSatisfacaoEnum.values()) {
            objs.add(new SelectItem(grau.getCodigo(), grau.getDescricao()));
        }
        return objs;
    }

    public List getListaObjecao() throws Exception {
        List<SelectItem> listaObjecao = new ArrayList<SelectItem>();
        try {
            List resultadoConsulta = getFacade().getObjecao().consultarPorDescricao("", false, Uteis.NIVELMONTARDADOS_TODOS);
            Ordenacao.ordenarLista(resultadoConsulta, "descricao");
            Iterator i = resultadoConsulta.iterator();

            listaObjecao.add(new SelectItem(0, "Selecione"));
            while (i.hasNext()) {
                ObjecaoVO obj = (ObjecaoVO) i.next();

                String tipo = "";
                if (obj.getTipoGrupo().equals("MD")) {
                    tipo = " - DESISTÊNCIA";
                } else if (obj.getTipoGrupo().equals("OD")) {
                    tipo = " - DEFINITIVA";
                }

                listaObjecao.add(new SelectItem(obj.getCodigo(), obj.getDescricao() + " - " + obj.getGrupo() + tipo));
            }
        } catch (Exception e){
            montarErro(e);
        }
        return listaObjecao;
    }

    public boolean isMostrarPanelObjecao() {
        return mostrarPanelObjecao;
    }

    public void setMostrarPanelObjecao(boolean mostrarPanelObjecao) {
        this.mostrarPanelObjecao = mostrarPanelObjecao;
    }

    public boolean isMostrarPanelAgendar() {
        return mostrarPanelAgendar;
    }

    public void setMostrarPanelAgendar(boolean mostrarPanelAgendar) {
        this.mostrarPanelAgendar = mostrarPanelAgendar;
    }

    public void apresentarObjecao() {
        setMostrarPanelAgendar(false);
        if (mostrarPanelObjecao) {
            setMostrarPanelObjecao(false);
        } else {
            setMostrarPanelObjecao(true);
        }
    }

    public void apresentarAgendar() {
        try {
            setMostrarPanelObjecao(false);
            if (mostrarPanelAgendar) {
                setMostrarPanelAgendar(false);
                setListaAulasAgenda(new ArrayList<>());
            } else {
                setMostrarPanelAgendar(true);
                setUsuarioSelecionado(false);
            }
        }catch (Exception e) {
            e.printStackTrace();
        }
    }

    public String getQtdRegistrosHistoricoContato() {
        if (qtdRegistrosHistoricoContato == null) {
            qtdRegistrosHistoricoContato = "3";
        }
        return qtdRegistrosHistoricoContato;
    }

    public void setQtdRegistrosHistoricoContato(String qtdRegistrosHistoricoContato) {
        this.qtdRegistrosHistoricoContato = qtdRegistrosHistoricoContato;
    }

    public void mostrarTodoHistoricoContato() {
        if (getQtdRegistrosHistoricoContato().isEmpty()) {
            setQtdRegistrosHistoricoContato(null);
        } else {
            setQtdRegistrosHistoricoContato("");
        }
    }

    public String novoHistoricoContato() throws Exception {
        setControleConsulta(new ControleConsulta());
        setDataConsulta(Calendario.hoje());
        setAgendaVO(new AgendaVO());
        setHistoricoContatoVO(new HistoricoContatoVO());
        getHistoricoContatoVO().setNovoObj(true);
        setMalaDiretaVO(new MalaDiretaVO());
        setListaEmail(new ArrayList());
        setListaTelefones(new ArrayList<TelefoneVO>());
        setMalaDiretaEnviadaVO(new MalaDiretaEnviadaVO());
        malaDiretaVO.setRemetente(getUsuarioLogado());
        setIndicacaoVO(new IndicacaoVO());
        setIndicadoVO(new IndicadoVO());
        getIndicacaoVO().setIndicadoVOs(new ArrayList<IndicadoVO>());
        setCampoConsultarModeloMensagem("");
        setValorConsultarModeloMensagem("");
        setManterAbertoRichModal(true);
        setConfiguracaoSistemaCRMVO(getFacade().getConfiguracaoSistemaCRM().consultarPorChavePrimaria(1, Uteis.NIVELMONTARDADOS_DADOSBASICOS));
        setListaConsultarModeloMensagem(new ArrayList());
        setMsgAlert("");
        setSucesso(true);
        setErro(false);
        setListaConsulta(new ArrayList());
        setMensagemDetalhada("");
        setMensagemRealizarContato("");
        setSucesso(true);
        setErro(false);
        setMensagemID("");
        setNomeUsuario("");
        setQtdRegistrosHistoricoContato(null);
        setMostrarPanelIndicacao(false);
        setMostrarPanelAgendar(false);
        setMostrarPanelObjecao(false);
        setMostrarModeloDeMensagem(false);
        setMostrarPanelReagendamento(false);
        setMostrarPanelAgendarIndicacao(false);
        setMostrarPanelObjecaoIndicacao(false);
        setMostrarPanelAgendarPassivo(false);
        setMostrarPanelObjecaoPassivo(false);
        setSomenteEditarIndicado(false);
        setApresentarModalObjecaoDefinitiva(false);
        return "";
    }

    public AgendaVO getAgendaVO() {
        if (agendaVO == null) {
            agendaVO = new AgendaVO();
        }
        return agendaVO;
    }

    public void setAgendaVO(AgendaVO agendaVO) {
        this.agendaVO = agendaVO;
    }

    public MalaDiretaEnviadaVO getMalaDiretaEnviadaVO() {
        if (malaDiretaEnviadaVO == null) {
            malaDiretaEnviadaVO = new MalaDiretaEnviadaVO();
        }
        return malaDiretaEnviadaVO;
    }

    public void setMalaDiretaEnviadaVO(MalaDiretaEnviadaVO malaDiretaEnviadaVO) {
        this.malaDiretaEnviadaVO = malaDiretaEnviadaVO;
    }

    public MalaDiretaVO getMalaDiretaVO() {
        if (malaDiretaVO == null) {
            malaDiretaVO = new MalaDiretaVO();
        }
        return malaDiretaVO;
    }

    public void setMalaDiretaVO(MalaDiretaVO malaDiretaVO) {
        this.malaDiretaVO = malaDiretaVO;
    }

    private boolean validarCfgValidacaoContato() throws Exception {
        return getFacade().getConfiguracaoSistema().verificarValidacaoContatoMeta();
    }

    public List<HistoricoContatoVO> getListaHistoricoObjecoesCliente() {
        if (listaHistoricoObjecoesCliente == null) {
            listaHistoricoObjecoesCliente = new ArrayList<HistoricoContatoVO>();
        }
        return listaHistoricoObjecoesCliente;
    }

    public void setListaHistoricoObjecoesCliente(List<HistoricoContatoVO> listaHistoricoObjecoesCliente) {
        this.listaHistoricoObjecoesCliente = listaHistoricoObjecoesCliente;
    }

    public String getTituloModalContador() {
        if (tituloModalContador == null) {
            tituloModalContador = "";
        }
        return tituloModalContador;
    }

    public void setTituloModalContador(String tituloModalContador) {
        this.tituloModalContador = tituloModalContador;
    }

    public List<HistoricoContatoVO> getListaModalContadorCliente() {
        if (listaModalContadorCliente == null) {
            listaModalContadorCliente = new ArrayList<HistoricoContatoVO>();
        }
        return listaModalContadorCliente;
    }

    public void setListaModalContadorCliente(List<HistoricoContatoVO> listaModalContadorCliente) {
        this.listaModalContadorCliente = listaModalContadorCliente;
    }

    public void abrirLigacoes() {
        try{
            setTituloModalContador("Histórico de Ligações");
            setListaModalContadorCliente(new ArrayList<HistoricoContatoVO>());
            for (HistoricoContatoVO obj : getListaHistoricoContatoClienteSemLimite()) {
                if (obj.getTipoContato().equals("TE")) {
                    getListaModalContadorCliente().add(obj);
                }
            }
        } catch (Exception e) {
            montarErro(e);
        }
    }

    public void abrirEmail() {
        try{
            setTituloModalContador("Histórico de Emails");
            setListaModalContadorCliente(new ArrayList<HistoricoContatoVO>());
            for (HistoricoContatoVO obj : getListaHistoricoContatoClienteSemLimite()) {
                if (obj.getTipoContato().equals("EM")) {
                    getListaModalContadorCliente().add(obj);
                }
            }
        } catch (Exception e) {
            montarErro(e);
        }
    }

    public void abrirContatoPessoal() {
        try{
            setTituloModalContador("Histórico de Contato Pessoal");
            setListaModalContadorCliente(new ArrayList<HistoricoContatoVO>());
            for (HistoricoContatoVO obj : getListaHistoricoContatoClienteSemLimite()) {
                if (obj.getTipoContato().equals("PE")) {
                    getListaModalContadorCliente().add(obj);
                }
            }
        } catch (Exception e) {
            montarErro(e);
        }
    }

    public void abrirSMS() {
        try{
            setTituloModalContador("Histórico de SMS");
            setListaModalContadorCliente(new ArrayList<HistoricoContatoVO>());
            for (HistoricoContatoVO obj : getListaHistoricoContatoClienteSemLimite()) {
                if (obj.getTipoContato().equals("CS")) {
                    getListaModalContadorCliente().add(obj);
                }
            }
        } catch (Exception e) {
            montarErro(e);
        }
    }

    public void abrirAPP() {
        try{
            setTituloModalContador("Histórico de APP");
            setListaModalContadorCliente(new ArrayList<HistoricoContatoVO>());
            for (HistoricoContatoVO obj : getListaHistoricoContatoClienteSemLimite()) {
                if (obj.getTipoContato().equals("AP")) {
                    getListaModalContadorCliente().add(obj);
                }
            }
        } catch (Exception e) {
            montarErro(e);
        }
    }

    public Boolean getApresentarTelefoneCliente() {
        return (this.getHistoricoContatoVO().getTipoContato().equals("TE")
                ||this.getHistoricoContatoVO().getTipoContato().equals("WA")
                || this.getHistoricoContatoVO().getTipoContato().equals("RE")
                || this.getHistoricoContatoVO().getTipoContato().equals("CO")
                || this.getHistoricoContatoVO().getTipoContato().equals("LC")
                || (this.getHistoricoContatoVO().getTipoContato().equals("CS") && !getEmpresaSemTokem()))
                && !this.getListaTelefones().isEmpty();
    }

    public List getListaSelectItemTipoAgendamentoAgenda() throws Exception {
        List objs = new ArrayList();
        Hashtable tipoAgendamentos = (Hashtable) Dominios.getTipoAgendamento();
        Enumeration keys = tipoAgendamentos.keys();
        while (keys.hasMoreElements()) {
            String value = (String) keys.nextElement();
            String label = (String) tipoAgendamentos.get(value);
            objs.add(new SelectItem(value, label));
        }
        SelectItemOrdemValor ordenador = new SelectItemOrdemValor();
        Collections.sort((List) objs, ordenador);
        return objs;
    }

    public boolean isMostrarPanelIndicacao() {
        return mostrarPanelIndicacao;
    }

    public void setMostrarPanelIndicacao(boolean mostrarPanelIndicacao) {
        this.mostrarPanelIndicacao = mostrarPanelIndicacao;
    }

    public void apresentarIndicacao() {
        notificarRecursoEmpresa(RecursoSistema.REALIZAR_CONTATO_FAZER_INDICACAO);
        if (mostrarPanelIndicacao) {
            setMostrarPanelIndicacao(false);
            setIndicadoVO(new IndicadoVO());
            setIndicacaoVO(new IndicacaoVO());
        } else {
            setMostrarPanelIndicacao(true);
            setIndicadoVO(new IndicadoVO());
            setIndicacaoVO(new IndicacaoVO());
        }
    }

    public IndicacaoVO getIndicacaoVO() {
        if (indicacaoVO == null) {
            indicacaoVO = new IndicacaoVO();
        }
        return indicacaoVO;
    }

    public void setIndicacaoVO(IndicacaoVO indicacaoVO) {
        this.indicacaoVO = indicacaoVO;
    }

    public IndicadoVO getIndicadoVO() {
        if (indicadoVO == null) {
            indicadoVO = new IndicadoVO();
        }
        return indicadoVO;
    }

    public void setIndicadoVO(IndicadoVO indicadoVO) {
        this.indicadoVO = indicadoVO;
    }

    public List<EventoVO> executarAutocompleteConsultaEvento(Object suggest) {
        String pref = (String) suggest;
        ArrayList<EventoVO> eventos;
        try {
            if (pref.equals("%")) {
                eventos = (ArrayList<EventoVO>) getFacade().getEvento().consultarTodosEventosAtivos(false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            } else {
                eventos = (ArrayList<EventoVO>) getFacade().getEvento().consultarPorNomeEventoAtivo(pref, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            }
            setMensagemID("msg_dados_consultados");
        } catch (Exception ex) {
            eventos = (new ArrayList<EventoVO>());
            setMensagemDetalhada("msg_erro", ex.getMessage());
        }
        return eventos;
    }

    public void selecionarEventoIndicacao() throws Exception {
        EventoVO obj = (EventoVO) request().getAttribute("eventos");
        try{
            obj = getFacade().getEvento().consultarPorChavePrimaria(obj.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            getIndicacaoVO().setEvento(obj);
            setMensagemDetalhada("");
            setErro(false);
            setSucesso(true);
        } catch (Exception ex) {
           montarErro(ex);
        }
    }

    public void gravarIndicacaoFecharPanel() {
        try {
            gravarIndicacao();
            apresentarIndicacao();
            setMensagemID("msg_dados_gravados");
            setSucesso(true);
            setErro(false);
            montarMsgGenerica("Indicação", "Indicação gravada com sucesso.", true, null, null, "panelGridLeft, panelGridCenter, panelGridRight");
        } catch (Exception e) {
            setSucesso(false);
            setErro(true);
            setMensagemID("msg_erro");
            montarMsgGenerica("Indicação", e.getMessage(), true, null, null, "panelGridLeft, panelGridCenter, panelGridRight");
        }
    }

    public void gravarIndicacao() throws Exception {

        if(usuarioSelecionado && !nomeUsuario.isEmpty()) {
            try {
                verificaColaboradorResponsavelPorMeta();
            } catch (Exception e) {
                setSucesso(false);
                setErro(true);
                throw new ConsistirException("O colaborador selecionado não possui responsabilidade configurada em \"Responsáveis pelas Fases\". " +
                        "\n É necessário configurar as fases de agendamento para este colaborador.");
            }
            AberturaMetaVO aberturaMeta = getFacade().getAberturaMeta().consultarAberturaPorCodigoUsuario(getUsuario().getCodigo(), Calendario.hoje(), getEmpresaLogado().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            if (aberturaMeta == null || aberturaMeta.getCodigo() == null || aberturaMeta.getCodigo() == 0) {
                throw new ConsistirException("Não foi encontrado meta em aberto para o usuário " + getUsuarioLogado().getUsername());
            }
        }else {
            AberturaMetaVO aberturaMeta = getFacade().getAberturaMeta().consultarAberturaPorCodigoUsuario(getUsuarioLogado().getCodigo(), Calendario.hoje(), getEmpresaLogado().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            if (aberturaMeta == null || aberturaMeta.getCodigo() == null || aberturaMeta.getCodigo() == 0) {
                throw new ConsistirException("Não foi encontrado meta em aberto para o usuário " + getUsuarioLogado().getUsername());
            }
        }
        getIndicacaoVO().setEmpresa(getEmpresaLogado());
        getIndicacaoVO().setResponsavelCadastro(getUsuarioLogado());
        getIndicacaoVO().setColaboradorResponsavel(getUsuarioLogado());
        getIndicacaoVO().setClienteQueIndicou(getMetaDetalhadoVOSelecionado().getCliente());
        getIndicacaoVO().setDia(Calendario.hoje());
        getIndicacaoVO().setDiaAbertura(Calendario.hoje());
        getIndicacaoVO().adicionarObjIndicadoVOs(getIndicadoVO());
        setIndicadoVO(new IndicadoVO());
        FecharMetaVO fecharMetaVO = getFacade().getFecharMeta().consultarPorIdentificadorMetaPorDiaPorColaborador(FasesCRMEnum.INDICACOES.getSigla(), getIndicacaoVO().getDiaAbertura(), getIndicacaoVO().getColaboradorResponsavel().getCodigo(), false, getIndicacaoVO().getEmpresa().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        if (getIndicacaoVO().isNovoObj()) {
            if (getIndicacaoVO().getClienteQueIndicou() != null && !UteisValidacao.emptyNumber(getIndicacaoVO().getClienteQueIndicou().getCodigo()) && getIndicacaoVO().getEmpresa().isTrabalharComPontuacao())
                getFacade().getHistoricoPontos().incluirPontuacaoPorTipo(TipoItemCampanhaEnum.INDICACAO, null, "", getIndicacaoVO().getClienteQueIndicou(), getEmpresaLogado());
            getFacade().getIndicacao().incluirIndicacaoMeta(getIndicacaoVO(), fecharMetaVO);
        }
        recarregarIndicadores();
        setIndicacaoVO(new IndicacaoVO());
        getIndicacaoVO().setIndicadoVOs(new ArrayList<IndicadoVO>());
        setIndicadoVO(new IndicadoVO());

    }


    public void gravarIndicacaoTela() {
        try {
            gravarIndicacao();
            setMensagemID("msg_dados_gravados");
            setSucesso(true);
            setErro(false);
            montarMsgGenerica("Indicação", "Indicação gravada com sucesso.", true, null, null, "panelGridLeft, panelGridCenter, panelGridRight");
        } catch (Exception e) {
            setSucesso(false);
            setErro(true);
            setMensagemID("msg_erro");
            montarMsgGenerica("Indicação", e.getMessage(), true, null, null, "panelGridLeft, panelGridCenter, panelGridRight");
        }
    }

    public Boolean getApresentarContatoPessoalTelefonico() {
        return this.getHistoricoContatoVO().getTipoContato().equals("TE") || this.getHistoricoContatoVO().getTipoContato().equals("PE")
                ||    this.getHistoricoContatoVO().getTipoContato().equals("WA") || this.getHistoricoContatoVO().getTipoContato().equals("LC");
    }

    public Boolean getApresentarSMS() {
        return !this.getHistoricoContatoVO().getTipoOperacao().equals("RE") && this.getHistoricoContatoVO().getTipoContato().equals("CS");
    }

    public Boolean getApresentarAPP() {
        return !this.getHistoricoContatoVO().getTipoOperacao().equals("RE") && this.getHistoricoContatoVO().getTipoContato().equals("AP");
    }

    public Boolean getApresentarEmail() {
        return this.getHistoricoContatoVO().getTipoContato().equals("EM");
    }

    public Boolean getClienteTemEmail() {
        return !getListaEmail().isEmpty();
    }

    public Boolean getClienteTemTelefone() {
        return !getListaTelefones().isEmpty();
    }

    public boolean getBotoesApp() {
        return tipoMensagemApp != null && tipoMensagemApp.equals(TipoPerguntaEnum.OBJETIVA.getCodigo())
                && getApresentarAPP();
    }

    public Integer getTipoMensagemApp() {
        return tipoMensagemApp;
    }

    public void setTipoMensagemApp(Integer tipoMensagemApp) {
        this.tipoMensagemApp = tipoMensagemApp;
    }

    public List<SelectItem> getListaSelectTiposPerguntas() {
        if (listaSelectTiposPerguntas == null) {
            listaSelectTiposPerguntas = new ArrayList<SelectItem>();
            listaSelectTiposPerguntas.add(new SelectItem(TipoPerguntaEnum.SIMPLES.getCodigo(), TipoPerguntaEnum.SIMPLES.getDescricao()));
            listaSelectTiposPerguntas.add(new SelectItem(TipoPerguntaEnum.DISSERTATIVA.getCodigo(), TipoPerguntaEnum.DISSERTATIVA.getDescricao()));
            listaSelectTiposPerguntas.add(new SelectItem(TipoPerguntaEnum.OBJETIVA.getCodigo(), TipoPerguntaEnum.OBJETIVA.getDescricao()));
        }
        return listaSelectTiposPerguntas;
    }

    public void alterarTipoPerguntaAPP() {
        try {
            if (getTipoMensagemApp().equals(TipoPerguntaEnum.OBJETIVA.getCodigo())) {
                tipoMensagemApp = TipoPerguntaEnum.OBJETIVA.getCodigo();
            } else if (getTipoMensagemApp().equals(TipoPerguntaEnum.DISSERTATIVA.getCodigo())) {
                tipoMensagemApp = TipoPerguntaEnum.DISSERTATIVA.getCodigo();
            } else if (getTipoMensagemApp().equals(TipoPerguntaEnum.SIMPLES.getCodigo())) {
                tipoMensagemApp = TipoPerguntaEnum.SIMPLES.getCodigo();
            }
        } catch (Exception e) {
            montarErro(e);
        }
    }

    private boolean validarMetaNaoAberta() throws Exception {
        if(usuarioSelecionado && !nomeUsuario.isEmpty()){
            return validarCfgValidacaoContato() &&
                    !getFacade().getAberturaMeta().consultarAberturaPorCodigoUsuario(getUsuario().getCodigo(),
                            getEmpresaLogado().getCodigo(), Calendario.hoje());
        }else {
            return validarCfgValidacaoContato() &&
                    !getFacade().getAberturaMeta().consultarAberturaPorCodigoUsuario(getUsuarioLogado().getCodigo(),
                            getEmpresaLogado().getCodigo(), Calendario.hoje());
        }
    }

    public void gravarEnviandoContatoAPP() {
        try {
            if (validarMetaNaoAberta()) {
                throw new Exception("Para realizar contato abra primeiramente a meta de hoje.");
            }
            validarPermissao("RealizarContato", "7.23 - Realizar Contato", getUsuarioLogado());
            //LOG - INICIO
            boolean historicoContatoVONovoObj = true;
            try {
                if (!historicoContatoVO.isNovoObj()) {
                    historicoContatoVONovoObj = false;
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
            //LOG - FIM
            malaDiretaVO.setTipoPergunta(tipoMensagemApp);
            if (getBotoesApp()) {
                malaDiretaVO.setOpcoes(campos.toString());
            } else if (tipoMensagemApp.equals(TipoPerguntaEnum.DISSERTATIVA.getCodigo())) {
                malaDiretaVO.setOpcoes("Confirmar;TEXTO");
            } else {
                campos = new CamposGenericosTO();
                malaDiretaVO.setOpcoes("");
            }
            MalaDiretaEnviadaVO enviado = new MalaDiretaEnviadaVO();
            enviado.setClienteVO(historicoContatoVO.getClienteVO());
            malaDiretaVO.setmalaDiretaEnviadaVOs(new ArrayList<MalaDiretaEnviadaVO>());
            malaDiretaVO.getMalaDiretaEnviadaVOs().add(enviado);
            historicoContatoVO.setObservacao(Uteis.retiraTags(historicoContatoVO.getObservacao(), false));
            if(!historicoContatoVO.getContatoAvulso()){ // contato originado de meta
                malaDiretaVO.setCodAberturaMeta(getTipoMetaSelecionada().getListaFecharMetaVO().get(0).getAberturaMetaVO().getCodigo());
            }
            malaDiretaVO.setMensagem(UteisValidacao.emptyString(mensagem) ?
                    historicoContatoVO.getObservacao() :
                    historicoContatoVO.getObservacao().replaceFirst(mensagem, mensagem));
            malaDiretaVO.setUsuarioVO(getUsuarioLogado());
            malaDiretaVO.setContatoAvulso(historicoContatoVO.getContatoAvulso());
            malaDiretaVO.setTitulo(Uteis.retirarPontosGraficos(getMalaDiretaVO().getTitulo()));
            malaDiretaVO.setMeioDeEnvioEnum(MeioEnvio.APP);
            malaDiretaVO.setTipoAgendamento(TipoAgendamentoEnum.AGENDAMENTO_INSTANTANEO);
            getFacade().getMalaDireta().incluir(malaDiretaVO);

            String retornoGerarNotificacoes = TreinoWSConsumer.gerarNotificacoes(getKey(),
                    malaDiretaVO, historicoContatoVO.getClienteVO().getCodigo().toString(), malaDiretaVO.getMensagem());

            if (UteisValidacao.emptyString(retornoGerarNotificacoes) || retornoGerarNotificacoes.toUpperCase().contains("ERRO")) {
                throw new Exception("Não foi possível enviar notificação. Tente novamente. " + retornoGerarNotificacoes);
            }
            String[] itens = retornoGerarNotificacoes.split(";");
            for (String item : itens) {
                String[] dados = item.split(",");
                historicoContatoVO.setCodigoNotificacao(Integer.valueOf(dados[1]));
                historicoContatoVO.setTipoContato("AP");
                getFacade().getHistoricoContato().gravarHistoricoContato("SR", historicoContatoVO, agendaVO, malaDiretaVO, getEmpresaLogado().getCodigo());


                String identificadorMeta = getMetaDetalhadoVOSelecionado().getFecharMeta().getIdentificadorMeta();
                String mensagemApresentar = "Envio realizado com sucesso.";

                if (!this.getHistoricoContatoVO().getContatoAvulso() && (getConfiguracaoSistemaCRMVO().isBaterMetaTodasAcoes() || identificadorMeta.equals("AN")
                        || identificadorMeta.equals("PV")
                        || identificadorMeta.equals("RI")
                        || identificadorMeta.equals("FA")
                        || identificadorMeta.equals("SF")
                        || identificadorMeta.equals("SA")
                        || identificadorMeta.equals("LA")
                        || identificadorMeta.equals("CR"))) {
                    mensagemApresentar += " A meta será batida.";
                }
            }

            if (this.getHistoricoContatoVO().getContatoAvulso()) {
                recarregarContatoAvulso();
            } else {
                String tipoContato = "SR";
                baterMeta(tipoContato, getMetaDetalhadoVOSelecionado(), false);
            }

            setSucesso(true);
            setErro(false);
            setMensagemID("msg_app_enviado");
            montarMsgGenerica("Enviar APP", "Notificação enviada com sucesso.", true, null, null, "panelGridLeft, panelGridCenter, panelGridRight");

            historicoContatoVO.setObservacao("");
            malaDiretaVO.setTitulo("");
            malaDiretaVO.setMensagem("");
            campos = new CamposGenericosTO();
        } catch (Exception e) {
            setSucesso(false);
            setErro(true);
            setMensagemID("msg_enviar_emailErro");
            montarMsgGenerica("Enviar APP", e.getMessage(), true, null, null, "panelGridLeft, panelGridCenter, panelGridRight");
        }
    }


    public void gravarEnviandoEmail() {
        String mensagem;
        try {
            if (validarMetaNaoAberta()) {
                throw new Exception("Para realizar contato abra primeiramente a meta de hoje.");
            }
            validarPermissao("RealizarContato", "7.23 - Realizar Contato", getUsuarioLogado());
            //LOG - INICIO
            boolean historicoContatoVONovoObj = true;
            try {
                if (!historicoContatoVO.isNovoObj()) {
                    historicoContatoVONovoObj = false;
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
            //LOG - FIM

            Boolean emailSelecionado = false;
            for (Object obj : this.getListaEmail()) {
                EmailVO email = (EmailVO) obj;
                if (email.getEscolhido()) {
                    emailSelecionado = true;
                    break;
                }
            }
            if (!emailSelecionado) {
                throw new Exception("Nenhum e-mail selecionado.");
            }


            if (!UteisValidacao.emptyNumber(historicoContatoVO.getClienteVO().getPessoa().getCodigo())) {
                for (Object obj : historicoContatoVO.getClienteVO().getPessoa().getEmailVOs()) {
                    EmailVO email = (EmailVO) obj;
                    if (email.getEscolhido()) {
                        historicoContatoVO.getClienteVO().getPessoa().setEmail(email.getEmail());
                        break;
                    }
                }
            }

            malaDiretaVO.setUsuarioVO(getUsuarioLogado());
            malaDiretaVO.setContatoAvulso(historicoContatoVO.getContatoAvulso());
            if(!historicoContatoVO.getContatoAvulso()){ // contato originado de meta
                this.getMalaDiretaVO().setCodAberturaMeta(getTipoMetaSelecionada().getListaFecharMetaVO().get(0).getAberturaMetaVO().getCodigo());
            }
            getMalaDiretaVO().setTitulo(Uteis.retirarPontosGraficos(getMalaDiretaVO().getTitulo()));
            if (getConfiguracaoSistemaCRMVO().getBloquearTermoSpam()) {
                String verificarTermosSpamNoTitulo = getMalaDiretaVO().verificarTermosSpam(getConfiguracaoSistemaCRMVO());
                if (!verificarTermosSpamNoTitulo.isEmpty()) {
                    setMensagemID("msg_mail_termobloqueado");
                    String msg = getMensagem() + " " + verificarTermosSpamNoTitulo + ".";
                    limparMsg();
                    throw new Exception(msg);
                }
            }

            boolean configuracaoEmailValido = (!getConfiguracaoSistemaCRMVO().getIntegracaoPacto() &&
                    !UteisValidacao.emptyString(getConfiguracaoSistemaCRMVO().getLogin()) &&
                    !UteisValidacao.emptyString(getConfiguracaoSistemaCRMVO().getSenha()) &&
                    !UteisValidacao.emptyString(getConfiguracaoSistemaCRMVO().getEmailPadrao()) &&
                    !UteisValidacao.emptyString(getConfiguracaoSistemaCRMVO().getRemetentePadrao()) &&
                    !UteisValidacao.emptyString(getConfiguracaoSistemaCRMVO().getMailServer()))
                    ||
                    (getConfiguracaoSistemaCRMVO().getIntegracaoPacto() &&
                            !UteisValidacao.emptyString(getConfiguracaoSistemaCRMVO().getEmailPadrao()) &&
                            !UteisValidacao.emptyString(getConfiguracaoSistemaCRMVO().getRemetentePadrao()));

            if (configuracaoEmailValido) {
                getFacade().getHistoricoContato().gravarHistoricoContato("", historicoContatoVO, agendaVO, malaDiretaVO, getEmpresaLogado().getCodigo());
                updateJenkinsService(malaDiretaVO, getConfiguracaoSistemaCRMVO());

                String identificadorMeta = getMetaDetalhadoVOSelecionado().getFecharMeta().getIdentificadorMeta();
                mensagem = "E-mail Agendado com Sucesso.";

                if (!this.getHistoricoContatoVO().getContatoAvulso() && (getConfiguracaoSistemaCRMVO().isBaterMetaTodasAcoes() || identificadorMeta.equals("AN")
                        || identificadorMeta.equals("PV")
                        || identificadorMeta.equals("RI")
                        || identificadorMeta.equals("FA")
                        || identificadorMeta.equals("SF")
                        || identificadorMeta.equals("SA")
                        || identificadorMeta.equals("LA")
                        || identificadorMeta.equals("CR"))) {
                    mensagem += " Assim que o e-mail for enviado, a meta será batida.";
                }

                if (this.getHistoricoContatoVO().getContatoAvulso()) {
                    recarregarContatoAvulso();
                } else {
                /*String tipoContato = "SR";
                getMetaDetalhadoVOSelecionado().setObteveSucesso(false);
                baterMeta(tipoContato, getMetaDetalhadoVOSelecionado(), false);*/
                    alterarSituacaoClienteContatoRealizadoMetaNaoAtendida(getMetaDetalhadoVOSelecionado());
                    selecionarProximaMetaOuCliente();
                }
            } else {
                mensagem = "Configurações para envio de e-mail inválidas, verificar nas configurações do CRM.";
            }

            montarMsgGenerica("Enviar Email", mensagem, true, null, null, "panelGridLeft, panelGridCenter, panelGridRight");
        } catch (Exception e) {
            if (e.getMessage() == null) {
                mensagem = "Erro: Email não foi agendado. Entre em contato com o suporte técnico.";
                montarMsgGenerica("Enviar Email", mensagem, true, null, null, "panelGridLeft, panelGridCenter, panelGridRight");
            } else {
                montarMsgGenerica("Enviar Email", "Erro: " +  e.getMessage(), true, null, null, "panelGridLeft, panelGridCenter, panelGridRight");
            }
            e.printStackTrace();
        }
    }

    public void gravarObjecaoMeta() {
        try {
            if(!UtilReflection.objetoNulo(getTipoMetaSelecionada(), "getFasesCRMEnum().getSigla()")){
                this.getHistoricoContatoVO().setFase(getTipoMetaSelecionada().getFasesCRMEnum().getSigla());
            }
            this.getHistoricoContatoVO().setContatoAvulso(false);
            gravarObjecao();
        } catch (Exception e) {
            setSucesso(false);
            setErro(true);
            setMensagemDetalhada("msg_erro", e.getMessage());
            montarMsgGenerica("Objeção", e.getMessage(), true, null, null, "panelGridLeft, panelGridCenter, panelGridRight");
        }

    }

    public void gravarObjecao() {
        try {
            if (validarMetaNaoAberta()) {
                throw new Exception("Para realizar contato abra primeiramente a meta de hoje.");
            }
            validarPermissao("RealizarContato", "7.23 - Realizar Contato", getUsuarioLogado());

            //LOG - INICIO
            boolean historicoContatoVONovoObj = true;
            try {
                if (!historicoContatoVO.isNovoObj()) {
                    historicoContatoVONovoObj = false;
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
            //LOG - FIM

            if (historicoContatoVO.getObjecaoVO().getCodigo() == 0) {
                throw new ConsistirException("Selecione a objeção.");
            }

            malaDiretaVO.setUsuarioVO(getUsuarioLogado());
            getFacade().getHistoricoContato().gravarHistoricoContato("OB", historicoContatoVO, agendaVO, malaDiretaVO, getEmpresaLogado().getCodigo());

            if (this.getHistoricoContatoVO().getContatoAvulso()) {
                recarregarContatoAvulso();
            } else {
                String tipoContato = "OB";
                baterMeta(tipoContato, getMetaDetalhadoVOSelecionado(), false);
            }

            setSucesso(true);
            setErro(false);
            montarMsgGenerica("Objeção", "Objeção gravada com Sucesso.", true, null, null, "panelGridLeft, panelGridRight");
        } catch (Exception e) {
            setSucesso(false);
            setErro(true);
            setMensagemDetalhada("msg_erro_naoGravados", e.getMessage());
            montarMsgGenerica("Objeção", e.getMessage(), true, null, null, "panelGridLeft, panelGridRight");
        }
    }

    public void gravarAgendaMeta() {
        try {
            if(!UtilReflection.objetoNulo(getTipoMetaSelecionada(), "getFasesCRMEnum().getSigla()")){
                this.getHistoricoContatoVO().setFase(getTipoMetaSelecionada().getFasesCRMEnum().getSigla());
            }
            this.getHistoricoContatoVO().setContatoAvulso(false);
            gravarAgenda();

            setNenhumUsuario();
        } catch (Exception e) {
            setSucesso(false);
            setErro(true);
            setMensagemDetalhada("msg_erro", e.getMessage());
            montarMsgGenerica("Agendar", e.getMessage(), true, null, null, "panelGridLeft, panelGridCenter, panelGridRight");
        }

    }

    public void gravarAgenda() {
        try {
            if (validarMetaNaoAberta()) {
                throw new Exception("Para realizar contato abra primeiramente a meta de hoje.");
            }
            validarPermissao("RealizarContato", "7.23 - Realizar Contato", getUsuarioLogado());
            if (agendaVO.getHoraMinuto().isEmpty()) {
                throw new ConsistirException(("Informe o horário."));
            } else if (agendaVO.getHoraMinuto().length() != 5) {
                throw new ConsistirException(("Informe o horário corretamente."));
            }

            if (configuracaoSistemaCRMVO.isDirecionaragendamentosexperimentaisagenda() &&
                    !UteisValidacao.emptyNumber(codigoAula) && (getTipoMetaSelecionada().getFasesCRMEnum().equals(FasesCRMEnum.LEADS_HOJE) ||
                    getTipoMetaSelecionada().getFasesCRMEnum().equals(FasesCRMEnum.LEADS_ACUMULADAS) ||
                    getTipoMetaSelecionada().getFasesCRMEnum().equals(FasesCRMEnum.INDICACOES_SEM_CONTATO) ||
                    getTipoMetaSelecionada().getFasesCRMEnum().equals(FasesCRMEnum.PASSIVO))) {
                String horaDeFato = getAulasAgenda().stream().filter(a -> a.getCodigo().equals(codigoAula)).findFirst().get().getHoraInicial();
                if (!UteisValidacao.emptyString(horaDeFato))
                    agendaVO.setHoraMinuto(horaDeFato);
            }

            String[] horaMinuto = agendaVO.getHoraMinuto().split(":");
            agendaVO.setHora(horaMinuto[0]);
            agendaVO.setMinuto(horaMinuto[1]);

            validarHorario(agendaVO);
            getFacade().getAgenda().verificaExisteAgendamentoDiaHoraMinuto(historicoContatoVO, agendaVO);

            ConfiguracaoSistemaCRMVO configuracaoSistemaCRMVO = getFacade().getConfiguracaoSistemaCRM().consultarConfiguracaoSistemaCRM(Uteis.NIVELMONTARDADOS_DADOSBASICOS);

            Date dataLimite = getFacade().getConfiguracaoSistemaCRM().obterDataCalculadaDiasUteis(Calendario.hoje(), false,  configuracaoSistemaCRMVO.getNrDiasLimiteAgendamentoFuturo(), getEmpresaLogado());

            if (Calendario.maior(agendaVO.getDataAgendamento(), dataLimite) &&
                    (agendaVO.getTipoAgendamento().equals("AE") || agendaVO.getTipoAgendamento().equals("VI"))) {
                throw new ConsistirException("Agendamento com data superior a " + configuracaoSistemaCRMVO.getNrDiasLimiteAgendamentoFuturoDescricao()
                        + " só pode ser do tipo Ligação. Veja campo 'Número de dias limite para agendamento futuro' nas configurações do CRM.");
            }
            if (!agendaVO.getTipoAgendamento().equals("AE")) {
                agendaVO.setModalidade(new ModalidadeVO());
            }
            if (getEmpresaLogado() != null) {
                agendaVO.setEmpresa(getEmpresaLogado().getCodigo());
            }

            if (!getTipoProfessor().isEmpty() && !getCodigoProfessor().equals(0)){
                agendaVO.setTipoProfessor(getTipoProfessor());
                agendaVO.setCodigoProfessor(getCodigoProfessor());
            }

            if (UteisValidacao.emptyString(this.nomeUsuario) && this.configuracaoSistemaCRMVO.isAgendamentoParaMetaConsultor()){
                usuarioVO = this.colaboradorResponsavel;
            }
            String fase = FasesCRMEnum.AGENDAMENTO.getSigla();

            if (Calendario.igual(agendaVO.getDataAgendamento(), Calendario.hoje()) && agendaVO.getTipoAgendamento().equals("LI")  && historicoContatoVO.getFase().equals("AL") ) {
                throw new ConsistirException("Agendamento de ligações só é possível para datas futuras, se preferir crie um simples registro ou um agendamento de visita! ");
            }

            if (configuracaoSistemaCRMVO.isDirecionaragendamentosexperimentaisagenda() &&
                    !UteisValidacao.emptyNumber(codigoAula) && (getTipoMetaSelecionada().getFasesCRMEnum().equals(FasesCRMEnum.LEADS_HOJE) ||
                    getTipoMetaSelecionada().getFasesCRMEnum().equals(FasesCRMEnum.LEADS_ACUMULADAS) ||
                    getTipoMetaSelecionada().getFasesCRMEnum().equals(FasesCRMEnum.INDICACOES_SEM_CONTATO) ||
                    getTipoMetaSelecionada().getFasesCRMEnum().equals(FasesCRMEnum.PASSIVO))) {

                TurmasServiceImpl turmasService = new TurmasServiceImpl(Conexao.getFromSession());
                try {
                    agendaVO.setAlunohorarioturma(turmasService.inserirAulaExperimentalAgenda(codigoAula,
                            ((historicoContatoVO.getClienteVO() != null && !UteisValidacao.emptyNumber(historicoContatoVO.getClienteVO().getCodigo())) ?
                                    historicoContatoVO.getClienteVO() : historicoContatoVO.getPassivoVO().getClienteVO()),
                            historicoContatoVO.getPassivoVO(), null, agendaVO.getDataAgendamento(), getUsuarioLogado()));
                } catch (Exception ex) {
                    ex.printStackTrace();
                } finally {
                    turmasService = null;
                }
            }

            if (usuarioVO != null) {
                getFacade().getHistoricoContato()
                        .gravarHistoricoContatoComColaborador(fase, historicoContatoVO, agendaVO, malaDiretaVO,
                                getEmpresaLogado().getCodigo(), usuarioVO);
            } else {
                getFacade().getHistoricoContato()
                        .gravarHistoricoContato(fase, historicoContatoVO, agendaVO, malaDiretaVO,
                                getEmpresaLogado().getCodigo());
            }
            Date dataAgendamento = agendaVO.getDataAgendamento();
            String TipoAgendamento = agendaVO.getTipoAgendamento();
            if (this.getHistoricoContatoVO().getContatoAvulso()) {
                recarregarContatoAvulso();
            } else {
                String tipoContato = "AG";
                baterMeta(tipoContato, getMetaDetalhadoVOSelecionado(), false);
            }

            if (!TipoAgendamento.equals("LI")){
                //Atualiza a numerção lateral dos agendamentos.
                recarregarAgendamentos(dataAgendamento);
            }

            agendaVO = new AgendaVO();

            setSucesso(true);
            setErro(false);
            montarMsgGenerica("Agendar", "Agendamento Gravado com Sucesso!", true, null, null, "panelGridLeft, panelGridRight");
        } catch (Exception e) {
            setSucesso(false);
            setErro(true);
            setMensagemDetalhada("msg_erro_naoGravados", e.getMessage());
            montarMsgGenerica("Agendar", e.getMessage(), true, null, null, "panelGridLeft, panelGridRight");
        }
    }

    public void confirmarSimplesRegistroMeta() {
        try {
            if(!UtilReflection.objetoNulo(getTipoMetaSelecionada(), "getFasesCRMEnum().getSigla()")){
                this.getHistoricoContatoVO().setFase(getTipoMetaSelecionada().getFasesCRMEnum().getSigla());
            }
            this.getHistoricoContatoVO().setContatoAvulso(false);
            confirmarSimplesRegistro();
        } catch (Exception e) {
            setSucesso(false);
            setErro(true);
            setMensagemDetalhada("msg_erro", e.getMessage());
            montarMsgGenerica("Simples Registro", e.getMessage(), true, null, null, "panelGridRight");
        }

    }

    private Integer inserirAulaExperimentalAgenda(Integer aula, ClienteVO clienteVO, PassivoVO passivo, Date dia, UsuarioVO usuario) throws Exception {
        TurmasServiceInterface turmaService = new TurmasServiceImpl(Conexao.getFromSession());
        Boolean aulaColetiva = turmaService.aulaColetiva(aula);
        try {
            if (aulaColetiva) {
                ParamAlunoAulaCheiaTO param = new ParamAlunoAulaCheiaTO();
                if (clienteVO != null && !UteisValidacao.emptyNumber(clienteVO.getCodigo())) {
                    param.setCodigoCliente(clienteVO.getCodigo());
                } else {
                    param.setCodigoPassivo(passivo.getCodigo());
                }
                param.setCodigoHorarioTurma(aula);
                param.setAulaExperimental(true);
                param.setData(dia);
                param.setCodigoUsuario(usuario.getCodigo());
                param.setOrigemSistema(OrigemSistemaEnum.CRM_META_DIARIA.getCodigo());
                turmaService.inserirAlunoAulaCheiaBooking(param, "");
                return turmaService.consultarUltimoAlunoHorarioTurmaInserido();
            }
            return null;
        }catch(Exception ex){
            ex.printStackTrace();
            return null;
        }finally {
            turmaService = null;
        }
    }

    private void logarHistoricoContatoVO(String msg, HistoricoContatoVO historicoContatoVO) {
        try {
            Logger.getLogger(MetaCRMControle.class.getName()).log(Level.INFO, null, msg + " | DIA_ABERTURA: " + Calendario.getData(historicoContatoVO.getDiaAbertura(), "dd/MM/yyyy")
                    + " | FASE: " + historicoContatoVO.getFase()
                    + " | TIPO CONTATO: " + historicoContatoVO.getTipoContato()
                    + " | RESPONSAVEL: " + historicoContatoVO.getColaboradorResponsavel().getCodigo() + " - " + historicoContatoVO.getColaboradorResponsavel().getNome()
                    + " | OBS: " + historicoContatoVO.getObservacao());
        } catch (Exception ex) {
            Logger.getLogger(MetaCRMControle.class.getName()).log(Level.INFO, null, "Falha ao gerar log historico contato sem codigo fechar meta: " + ex.getMessage());
        }
    }

    public void confirmarSimplesRegistro() {
        try {
            setMsgAlert("");
            limparMsg();
            StringBuilder msgModal = new StringBuilder();
            boolean modalInformacao = false;

            if (this.getHistoricoContatoVO().getObservacao().isEmpty()) {
                throw new Exception("Informe o comentário.");
            } else if (!this.getHistoricoContatoVO().getContatoAvulso() && validarMetaNaoAberta()) {
                throw new Exception("Para realizar contato abra primeiramente a meta de hoje.");
            }
            gravarSimplesRegistro();
        } catch (Exception e) {
            setSucesso(false);
            setErro(true);
            setMensagemDetalhada("msg_erro", e.getMessage());
            montarMsgGenerica("Simples Registro", e.getMessage(), true, null, null, "panelGridRight");
        }
    }

    public void gravarSimplesRegistro() {
        try {

            validarPermissao("RealizarContato", "7.23 - Realizar Contato", getUsuarioLogado());
            setMalaDiretaVO(new MalaDiretaVO());
            setMalaDiretaEnviadaVO(new MalaDiretaEnviadaVO());

            if (historicoContatoVO.getResponsavelCadastro() == null || UteisValidacao.emptyNumber(historicoContatoVO.getResponsavelCadastro().getCodigo())) {
                historicoContatoVO.setResponsavelCadastro(getUsuarioLogado());
            }
            if (UteisValidacao.emptyNumber(historicoContatoVO.getCodigoFecharMetaDetalhado()) || UteisValidacao.emptyNumber(getMetaDetalhadoVOSelecionado().getCodigo())) {
                logarHistoricoContatoVO("HistoricoContatoVO sem código fechar meta detalhado. ", historicoContatoVO);
            }
            getFacade().getHistoricoContato().gravarHistoricoContato("SR", historicoContatoVO, agendaVO, malaDiretaVO, getEmpresaLogado().getCodigo());
            if (this.getHistoricoContatoVO().getContatoAvulso()) {
                inicializarContatoAvulso();
            } else {
                String tipoContato = "SR";
                getMetaDetalhadoVOSelecionado().setTeveContato(true);
                baterMeta(tipoContato, getMetaDetalhadoVOSelecionado(), false);
            }

            setSucesso(true);
            setErro(false);
            montarSucessoGrowl("Simples Registro gravado com sucesso.");
            notificarRecursoEmpresa(RecursoSistema.USOU_SIMPLES_REGISTRO_NOVO);
         } catch (Exception e) {
            setSucesso(false);
            setErro(true);
            setMensagemDetalhada("msg_erro_naoGravados", e.getMessage());
            montarMsgAlert(getMensagemDetalhada());
        }

    }

    public void alterarObjecao() {
        try {
            ObjecaoVO objecaoVO = new ObjecaoVO();
            if (!UteisValidacao.emptyNumber(this.getHistoricoContatoVO().getObjecaoVO().getCodigo())) {
                objecaoVO = getFacade().getObjecao().consultarPorChavePrimaria(this.getHistoricoContatoVO().getObjecaoVO().getCodigo(), Uteis.NIVELMONTARDADOS_TODOS);
            }
            this.getHistoricoContatoVO().setObjecaoVO(objecaoVO);
        } catch (Exception e) {
            montarErro(e);
        }
    }



    public UsuarioVO getColaboradorResponsavel() {
        if (colaboradorResponsavel == null) {
            colaboradorResponsavel = new UsuarioVO();
        }
        return (colaboradorResponsavel);
    }

    public void setColaboradorResponsavel(UsuarioVO colaboradorResponsavel) {
        this.colaboradorResponsavel = colaboradorResponsavel;
    }

    public boolean getMetaIndicacao() {
        if (getTipoMetaSelecionada() != null && getTipoMetaSelecionada().getFasesCRMEnum() != null) {
            return getTipoMetaSelecionada().getFasesCRMEnum().equals(FasesCRMEnum.INDICACOES);
        } else {
            return false;
        }
    }

    public boolean getMetaIndicacaoSemContato() {
        if (getTipoMetaSelecionada() != null && getTipoMetaSelecionada().getFasesCRMEnum() != null) {
            return getTipoMetaSelecionada().getFasesCRMEnum().equals(FasesCRMEnum.INDICACOES_SEM_CONTATO);
        } else {
            return false;
        }
    }

    public List<ModalidadeVO> autocompleteModalidade(Object suggest) {
        String pref = (String) suggest;
        ArrayList<ModalidadeVO> result;
        try {
            if (pref.equals("%")) {
                result = (ArrayList<ModalidadeVO>) getFacade().getModalidade().consultarTodasModalidadesComLimite(getEmpresaLogado().getCodigo(), true, null);
            } else {
                result = (ArrayList<ModalidadeVO>) getFacade().getModalidade().consultarPorNome(pref, getEmpresaLogado().getCodigo(), false, Uteis.NIVELMONTARDADOS_TELACONSULTA);
            }
        } catch (Exception ex) {
            result = (new ArrayList<ModalidadeVO>());
        }
        return result;
    }

//    public void selecionarModalidade() throws Exception {
//        ModalidadeVO obj = (ModalidadeVO) request().getAttribute("modalidade");
//        obj = getFacade().getModalidade().consultarPorChavePrimaria(obj.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
//        this.getHistoricoContatoVO().getAgendaVO().setModalidade(obj);
//        setMensagemDetalhada("");
//        setErro(false);
//        setSucesso(true);
//    }

    public Boolean getApresentarInputTextAulaExperimental() {
        return getAgendaVO().getTipoAgendamento().equals("AE");
    }

    public void baterMeta(String tipoContato, FecharMetaDetalhadoVO fecharMetaDetalhadoVO, boolean atualizarTodasMetas) {
        try {
            // BATER META DE INDICAÇÃO
            if ((getMetaIndicacao() || getMetaIndicacaoSemContato()) && tipoContato.equals("IN")) {

                if (getMetaIndicacao()) {
                    List<FecharMetaDetalhadoVO> lista = getFacade().getFecharMetaDetalhado().consultarMetaDetalhada(getTipoMetaSelecionada().getListaFecharMetaVO(), false, false, true, getEmpresaLogado().getCodigo());
                    Ordenacao.ordenarLista(lista, "nomePessoaRel");

                    for (FecharMetaDetalhadoVO indicacoesMeta : lista) {
                        indicacoesMeta.setObteveSucesso(true);
                        indicacoesMeta.setSituacaoAtualMetaEnum(SituacaoAtualMetaEnum.META_ATENDIDA);
                    }

                    getTipoMetaSelecionada().setListaMetaDetalhada(lista);
                    getTipoMetaSelecionada().setListaMetaDetalhadaFiltrada(lista);
                    getTipoMetaSelecionada().setTotalMetaRealizada(getTipoMetaSelecionada().getTotalMetaRealizada() + 1);

                } else {
                    List<FecharMetaDetalhadoVO> lista = getFacade().getFecharMetaDetalhado().consultarIndicadores(getTipoMetaSelecionada().getListaFecharMetaVO(), false, true, false, 1000);
                    Ordenacao.ordenarLista(lista, "nomePessoaRel");

                    for (FecharMetaDetalhadoVO indSemContato : lista) {
                        indSemContato.setSituacaoAtualMetaEnum(SituacaoAtualMetaEnum.META_NAO_ATENDIDA);
                    }

                    getTipoMetaSelecionada().setListaMetaDetalhada(lista);
                    getTipoMetaSelecionada().setListaMetaDetalhadaFiltrada(lista);
                }

                for (MetaCRMTO metaCRMTO : getListaMetaCRM()) {
                    if (getMetaIndicacaoSemContato()) {
                        for (TipoMetaCRMTO tipoMetaCRMTO : metaCRMTO.getListaTipoMetaVO()) {
                            if (tipoMetaCRMTO.getFasesCRMEnum().equals(FasesCRMEnum.INDICACOES)) {
                                tipoMetaCRMTO.setTotalMetaRealizada(tipoMetaCRMTO.getTotalMetaRealizada() + 1);
                                if (tipoMetaCRMTO.getTotalMetaRealizada() >= tipoMetaCRMTO.getTotalMeta()) {
                                    tipoMetaCRMTO.setSituacaoAtualMetaEnum(SituacaoAtualMetaEnum.META_ATENDIDA);
                                }
                            }

                        }
                    }

                    if (metaCRMTO.getTipoFaseCRM().equals(getTipoMetaSelecionada().getFasesCRMEnum().getTipoFase())) {
                        metaCRMTO.setTotalMetaRealizada(metaCRMTO.getTotalMetaRealizada() + 1);
                    }

                }

                if (atualizarTodasMetas) {
                    FasesCRMEnum faseAtual = getTipoMetaSelecionada().getFasesCRMEnum();
                    consultarMetasUsuariosSelecionados();

                    TipoMetaCRMTO obj = new TipoMetaCRMTO();
                    boolean metaSelecionada = false;
                    for (MetaCRMTO metaCRMTO : getListaMetaCRM()) {
                        for (TipoMetaCRMTO tipoMetaCRMTO : metaCRMTO.getListaTipoMetaVO()) {
                            if (tipoMetaCRMTO.getFasesCRMEnum().equals(faseAtual)) {
                                obj = tipoMetaCRMTO;
                                break;
                            }
                        }
                        if (metaSelecionada) {
                            break;
                        }
                    }

                    for (MetaCRMTO metaCRMTO : getListaMetaCRM()) {
                        if (metaCRMTO.getTipoFaseCRM().equals(TipoFaseCRM.INDICADORES)) {
                            for (TipoMetaCRMTO tipoMetaCRMTO : metaCRMTO.getListaTipoMetaVO()) {
                                tipoMetaCRMTO.setSituacaoAtualMetaEnum(SituacaoAtualMetaEnum.META_ATENDIDA);
                            }
                        } else {
                            for (TipoMetaCRMTO tipoMetaCRMTO : metaCRMTO.getListaTipoMetaVO()) {
                                Integer meta = tipoMetaCRMTO.getTotalMeta();
                                Integer realizado = tipoMetaCRMTO.getTotalMetaRealizada();
                                Integer contatoSemBater = tipoMetaCRMTO.getTotalContatoSemBaterMeta();
                                Integer realizadoMAIScontatoSemBater = realizado + contatoSemBater;

                                if (realizado >= meta) {
                                    tipoMetaCRMTO.setSituacaoAtualMetaEnum(SituacaoAtualMetaEnum.META_ATENDIDA);
                                } else if ((realizadoMAIScontatoSemBater >= meta) && (contatoSemBater != 0)) {
                                    tipoMetaCRMTO.setSituacaoAtualMetaEnum(SituacaoAtualMetaEnum.META_NAO_ATENDIDA_CONTATOREALIZADO);
                                } else if (realizadoMAIScontatoSemBater < meta) {
                                    tipoMetaCRMTO.setSituacaoAtualMetaEnum(SituacaoAtualMetaEnum.META_NAO_ATENDIDA);
                                }
                            }
                        }
                    }
                    obj.setSituacaoAtualMetaEnum(SituacaoAtualMetaEnum.META_SELECIONADA);
                    setTipoMetaSelecionada(obj);
                    consultarMetaDetalhada(obj, false);

                }

            } else {

                boolean bateMeta = false;

                // VERIFICAR SE O TIPO DE CONTATO BATE A META DE ACORDO COM A FASE
                if ((getConfiguracaoSistemaCRMVO().isBaterMetaTodasAcoes() && !getTipoMetaSelecionada().getFasesCRMEnum().equals(FasesCRMEnum.AGENDAMENTO) && !getTipoMetaSelecionada().getFasesCRMEnum().equals(FasesCRMEnum.LEADS_HOJE) && !getTipoMetaSelecionada().getFasesCRMEnum().equals(FasesCRMEnum.LEADS_ACUMULADAS)) || getTipoMetaSelecionada().getFasesCRMEnum().equals(FasesCRMEnum.CRM_EXTRA) || getTipoMetaSelecionada().getFasesCRMEnum().equals(FasesCRMEnum.ALUNO_GYMPASS)) {
                    bateMeta = true;
                } else if (tipoContato.equals("CP")) {
                    if (getTipoMetaSelecionada().getFasesCRMEnum().equals(FasesCRMEnum.AGENDAMENTO)) {
                        bateMeta = true;
                    }
                } else if (tipoContato.equals("RE")) {
                    if (getTipoMetaSelecionada().getFasesCRMEnum().equals(FasesCRMEnum.AGENDAMENTO)
                            || getTipoMetaSelecionada().getFasesCRMEnum().equals(FasesCRMEnum.LIGACAO_AGENDADOS_AMANHA)) {
                        if (getTipoMetaSelecionada().getFasesCRMEnum().equals(FasesCRMEnum.AGENDAMENTO)) {
                            bateMeta = getFacade().getFecharMetaDetalhado().consultarSituacaoObteveSucesso(fecharMetaDetalhadoVO.getCodigo());
                        } else {
                            bateMeta = true;
                        }
                    }
                } else if (tipoContato.equals("AG")) {
                    if (getTipoMetaSelecionada().getFasesCRMEnum().equals(FasesCRMEnum.VINTE_QUATRO_HORAS)
                            || getTipoMetaSelecionada().getFasesCRMEnum().equals(FasesCRMEnum.RENOVACAO)
                            || getTipoMetaSelecionada().getFasesCRMEnum().equals(FasesCRMEnum.LIGACAO_AGENDADOS_AMANHA)
                            || getTipoMetaSelecionada().getFasesCRMEnum().equals(FasesCRMEnum.AGENDAMENTOS_LIGACOES)
                            || getTipoMetaSelecionada().getFasesCRMEnum().equals(FasesCRMEnum.AGENDAMENTO)
                            || getTipoMetaSelecionada().getFasesCRMEnum().equals(FasesCRMEnum.DESISTENTES)
                            || getTipoMetaSelecionada().getFasesCRMEnum().equals(FasesCRMEnum.VISITANTES_ANTIGOS)
                            || getTipoMetaSelecionada().getFasesCRMEnum().equals(FasesCRMEnum.VISITA_RECORRENTE)
                            || getTipoMetaSelecionada().getFasesCRMEnum().equals(FasesCRMEnum.EX_ALUNOS)
                            || getTipoMetaSelecionada().getFasesCRMEnum().equals(FasesCRMEnum.ALUNO_ULTIMO_ACESSO_GYMPASS)
                            || getTipoMetaSelecionada().getFasesCRMEnum().equals(FasesCRMEnum.FILA_ESPERA_TURMA_CRM)
                            || getTipoMetaSelecionada().getFasesCRMEnum().equals(FasesCRMEnum.INDICACOES)
                            || getTipoMetaSelecionada().getFasesCRMEnum().equals(FasesCRMEnum.GRUPO_RISCO)
                            || getTipoMetaSelecionada().getFasesCRMEnum().equals(FasesCRMEnum.VENCIDOS)
                            || getTipoMetaSelecionada().getFasesCRMEnum().equals(FasesCRMEnum.POS_VENDA)
                            || getTipoMetaSelecionada().getFasesCRMEnum().equals(FasesCRMEnum.FALTOSOS)
                            || getTipoMetaSelecionada().getFasesCRMEnum().equals(FasesCRMEnum.ANIVERSARIANTES)
                            || getTipoMetaSelecionada().getFasesCRMEnum().equals(FasesCRMEnum.LEADS_HOJE)
                            || getTipoMetaSelecionada().getFasesCRMEnum().equals(FasesCRMEnum.LEADS_ACUMULADAS)) {
                        bateMeta = true;
                    }
                } else if (tipoContato.equals("OB")) {
                    if (getTipoMetaSelecionada().getFasesCRMEnum().equals(FasesCRMEnum.VINTE_QUATRO_HORAS)
                            || getTipoMetaSelecionada().getFasesCRMEnum().equals(FasesCRMEnum.AGENDAMENTO)
                            || getTipoMetaSelecionada().getFasesCRMEnum().equals(FasesCRMEnum.DESISTENTES)
                            || getTipoMetaSelecionada().getFasesCRMEnum().equals(FasesCRMEnum.LIGACAO_AGENDADOS_AMANHA)
                            || getTipoMetaSelecionada().getFasesCRMEnum().equals(FasesCRMEnum.AGENDAMENTOS_LIGACOES)
                            || getTipoMetaSelecionada().getFasesCRMEnum().equals(FasesCRMEnum.VISITANTES_ANTIGOS)
                            || getTipoMetaSelecionada().getFasesCRMEnum().equals(FasesCRMEnum.VISITA_RECORRENTE)
                            || getTipoMetaSelecionada().getFasesCRMEnum().equals(FasesCRMEnum.EX_ALUNOS)
                            || getTipoMetaSelecionada().getFasesCRMEnum().equals(FasesCRMEnum.ALUNO_ULTIMO_ACESSO_GYMPASS)
                            || getTipoMetaSelecionada().getFasesCRMEnum().equals(FasesCRMEnum.FILA_ESPERA_TURMA_CRM)
                            || getTipoMetaSelecionada().getFasesCRMEnum().equals(FasesCRMEnum.INDICACOES)
                            || getTipoMetaSelecionada().getFasesCRMEnum().equals(FasesCRMEnum.GRUPO_RISCO)
                            || getTipoMetaSelecionada().getFasesCRMEnum().equals(FasesCRMEnum.VENCIDOS)
                            || getTipoMetaSelecionada().getFasesCRMEnum().equals(FasesCRMEnum.POS_VENDA)
                            || getTipoMetaSelecionada().getFasesCRMEnum().equals(FasesCRMEnum.FALTOSOS)
                            || getTipoMetaSelecionada().getFasesCRMEnum().equals(FasesCRMEnum.ANIVERSARIANTES)
                            || getTipoMetaSelecionada().getFasesCRMEnum().equals(FasesCRMEnum.LEADS_HOJE)
                            || getTipoMetaSelecionada().getFasesCRMEnum().equals(FasesCRMEnum.LEADS_ACUMULADAS)) {
                        bateMeta = true;
                        if(getTipoMetaSelecionada().getFasesCRMEnum().equals(FasesCRMEnum.FILA_ESPERA_TURMA_CRM)){
                            getFacade().getFecharMetaDetalhado().deletarAlunoFilaEsperaTurmaCrm(fecharMetaDetalhadoVO.getCodigoOrigem());
                        }
                    }
                } else if (tipoContato.equals("SR")) {
                    if (getTipoMetaSelecionada().getFasesCRMEnum().equals(FasesCRMEnum.LIGACAO_AGENDADOS_AMANHA)
                            || getTipoMetaSelecionada().getFasesCRMEnum().equals(FasesCRMEnum.AGENDAMENTOS_LIGACOES)
                            || getTipoMetaSelecionada().getFasesCRMEnum().equals(FasesCRMEnum.GRUPO_RISCO)
                            || getTipoMetaSelecionada().getFasesCRMEnum().equals(FasesCRMEnum.POS_VENDA)
                            || getTipoMetaSelecionada().getFasesCRMEnum().equals(FasesCRMEnum.ALUNO_ULTIMO_ACESSO_GYMPASS)
                            || getTipoMetaSelecionada().getFasesCRMEnum().equals(FasesCRMEnum.FALTOSOS)
                            || getTipoMetaSelecionada().getFasesCRMEnum().equals(FasesCRMEnum.ANIVERSARIANTES)) {
                        bateMeta = true;
                    }
                }

                //meta extra não precisa atualizar tudo
                if(bateMeta && tipoContato.equals(FasesCRMEnum.AGENDAMENTO.getSigla())){
                    atualizarTodasMetas = false;
                }

                // BATER A META CASO SEJA NECESSÁRIO
                if (bateMeta && !getMetaDetalhadoVOSelecionado().getObteveSucesso()) {
                    int metasBatidas = 0;

                    for (FecharMetaDetalhadoVO clientesMeta : getTipoMetaSelecionada().getListaMetaDetalhada()) {
                        boolean baterMesmoCliente = false; //casos em que o cliente está mais de uma vez na meta. (pode acontecer principalmente no pos-venda)
                        if (!clientesMeta.getCodigo().equals(fecharMetaDetalhadoVO.getCodigo())
                                && (!UteisValidacao.emptyNumber(clientesMeta.getCliente().getCodigo()) && clientesMeta.getCliente().getCodigo().equals(fecharMetaDetalhadoVO.getCliente().getCodigo()))) {
                            baterMesmoCliente = getFacade().getFecharMetaDetalhado().consultarSituacaoObteveSucesso(clientesMeta.getCodigo());
                        }
                        if (clientesMeta.getCodigo().equals(fecharMetaDetalhadoVO.getCodigo()) || baterMesmoCliente) {
                            if(!clientesMeta.getObteveSucesso()){
                                metasBatidas++;
                            }
                            clientesMeta.setObteveSucesso(true);
                            clientesMeta.setSituacaoAtualMetaEnum(SituacaoAtualMetaEnum.META_ATENDIDA);
                            if(getTipoMetaSelecionada().getFasesCRMEnum().equals(FasesCRMEnum.AGENDAMENTOS_LIGACOES) && !getFacade().getHistoricoContato().existeHistoricoDataExpecifica(clientesMeta)){
                                clientesMeta.setRepescagem(true);
                            }
                            if (getTipoMetaSelecionada().getFasesCRMEnum().equals(FasesCRMEnum.AGENDAMENTOS_LIGACOES)) {
                                clientesMeta.setTeveContato(true);
                            }
                            if (getTipoMetaSelecionada().getFasesCRMEnum().equals(FasesCRMEnum.AGENDAMENTO)) {
                                clientesMeta.setTeveContato(true);
                                clientesMeta.setRepescagem(true);
                            }
                        }
                    }

                    getTipoMetaSelecionada().setListaMetaDetalhadaFiltrada(getTipoMetaSelecionada().getListaMetaDetalhada());
                    getTipoMetaSelecionada().setTotalMetaRealizada(getTipoMetaSelecionada().getTotalMetaRealizada() + metasBatidas);

                    if (!getTipoMetaSelecionada().getFasesCRMEnum().equals(FasesCRMEnum.AGENDAMENTOS_LIGACOES)) {
                        for (MetaCRMTO metaCRMTO : getListaMetaCRM()) {
                            if (metaCRMTO.getTipoFaseCRM().equals(getTipoMetaSelecionada().getFasesCRMEnum().getTipoFase())) {
                                metaCRMTO.setTotalMetaRealizada(metaCRMTO.getTotalMetaRealizada() + metasBatidas);
                            }
                        }
                    }

                    if (getTipoMetaSelecionada().getTotalMetaRealizada().equals(getTipoMetaSelecionada().getTotalMeta())) {
                        for (MetaCRMTO metaCRMTO : getListaMetaCRM()) {
                            for (TipoMetaCRMTO tipoMetaCRMTO : metaCRMTO.getListaTipoMetaVO()) {
                                if (tipoMetaCRMTO.getFasesCRMEnum().getSigla().equals(getTipoMetaSelecionada().getFasesCRMEnum().getSigla())) {
                                    tipoMetaCRMTO.setSituacaoAtualMetaEnum(SituacaoAtualMetaEnum.META_ATENDIDA);
                                    break;
                                }
                            }
                        }
                    }

                } else if (!bateMeta && !getMetaDetalhadoVOSelecionado().getObteveSucesso()) {
                    // CASO NÃO BATA A META SOMENTE ALTERAR A SITUACAO DO CLIENTE DA META PARA CONTATO REALIZADO MAS META NÃO ATENDIDA
                    alterarSituacaoClienteContatoRealizadoMetaNaoAtendida(fecharMetaDetalhadoVO);
                }

                if (atualizarTodasMetas) {
                    FasesCRMEnum faseAtual = getTipoMetaSelecionada().getFasesCRMEnum();
                    consultarMetasUsuariosSelecionados();

                    TipoMetaCRMTO obj = new TipoMetaCRMTO();
                    boolean metaSelecionada = false;
                    for (MetaCRMTO metaCRMTO : getListaMetaCRM()) {
                        for (TipoMetaCRMTO tipoMetaCRMTO : metaCRMTO.getListaTipoMetaVO()) {
                            if (tipoMetaCRMTO.getFasesCRMEnum().equals(faseAtual)) {
                                obj = tipoMetaCRMTO;
                                break;
                            }
                        }
                        if (metaSelecionada) {
                            break;
                        }
                    }

                    for (MetaCRMTO metaCRMTO : getListaMetaCRM()) {
                        if (metaCRMTO.getTipoFaseCRM().equals(TipoFaseCRM.INDICADORES)) {
                            for (TipoMetaCRMTO tipoMetaCRMTO : metaCRMTO.getListaTipoMetaVO()) {
                                tipoMetaCRMTO.setSituacaoAtualMetaEnum(SituacaoAtualMetaEnum.META_ATENDIDA);
                            }
                        } else {
                            for (TipoMetaCRMTO tipoMetaCRMTO : metaCRMTO.getListaTipoMetaVO()) {
                                Integer meta = tipoMetaCRMTO.getTotalMeta();
                                Integer realizado = tipoMetaCRMTO.getTotalMetaRealizada();
                                Integer contatoSemBater = tipoMetaCRMTO.getTotalContatoSemBaterMeta();
                                Integer realizadoMAIScontatoSemBater = realizado + contatoSemBater;

                                if (realizado >= meta) {
                                    tipoMetaCRMTO.setSituacaoAtualMetaEnum(SituacaoAtualMetaEnum.META_ATENDIDA);
                                } else if ((realizadoMAIScontatoSemBater >= meta) && (contatoSemBater != 0)) {
                                    tipoMetaCRMTO.setSituacaoAtualMetaEnum(SituacaoAtualMetaEnum.META_NAO_ATENDIDA_CONTATOREALIZADO);
                                } else if (realizadoMAIScontatoSemBater < meta) {
                                    tipoMetaCRMTO.setSituacaoAtualMetaEnum(SituacaoAtualMetaEnum.META_NAO_ATENDIDA);
                                }
                            }
                        }
                    }
                    obj.setSituacaoAtualMetaEnum(SituacaoAtualMetaEnum.META_SELECIONADA);
                    setTipoMetaSelecionada(obj);
                    consultarMetaDetalhada(obj, false);
                }
                //SELECIONAR O PROXIMO CLIENTE OU META
                selecionarProximaMetaOuCliente();
            }
        } catch (Exception e) {
            montarErro(e);
        }
    }

    /**
     * ALTERA A SITUACAO DO CLIENTE DA META PARA CONTATO REALIZADO MAS META NÃO ATENDIDA
     * @param fecharMetaDetalhadoVO
     */
    private void alterarSituacaoClienteContatoRealizadoMetaNaoAtendida(FecharMetaDetalhadoVO fecharMetaDetalhadoVO) {
        for (FecharMetaDetalhadoVO clientesMeta : getTipoMetaSelecionada().getListaMetaDetalhada()) {
            if (clientesMeta.getCodigo().equals(fecharMetaDetalhadoVO.getCodigo())) {
                clientesMeta.setSituacaoAtualMetaEnum(SituacaoAtualMetaEnum.META_NAO_ATENDIDA_CONTATOREALIZADO);
            }
        }
        getTipoMetaSelecionada().setListaMetaDetalhadaFiltrada(getTipoMetaSelecionada().getListaMetaDetalhada());
    }


    public void gravarEnviandoSMS() {
        try {
            if (!this.getHistoricoContatoVO().getContatoAvulso() && validarMetaNaoAberta()) {
                throw new Exception("Para realizar contato abra primeiramente a meta de hoje.");
            }
            //string numero para o envio de sms
            String numero = null;
            //caso o contato seja realizado via sms

            getMalaDiretaVO().setTitulo(Uteis.retirarPontosGraficos(getMalaDiretaVO().getTitulo()));
            if (getConfiguracaoSistemaCRMVO().getBloquearTermoSpam()) {
                String mensagem = Uteis.retirarAcentuacaoRegex(this.getHistoricoContatoVO().getObservacao()).toUpperCase();
                String verificarTermosSpamNoTitulo = MalaDiretaVO.verificar(getConfiguracaoSistemaCRMVO(), mensagem);
                if (!verificarTermosSpamNoTitulo.isEmpty()) {
                    setMensagemID("msg_mail_termobloqueado");
                    String msg = getMensagem() + " " + verificarTermosSpamNoTitulo + ".";
                    limparMsg();
                    throw new Exception(msg);
                }
            }
            //iterar nos telefones procurando o selecionado
            FOR:
            for (Object obj : this.getListaTelefoneClientePorTipoContato()) {
                TelefoneVO tel = (TelefoneVO) obj;
                //armazenar o selecionado na string
                if (tel.getSelecionado()) {
                    numero = tel.getNumero();
                    break FOR;
                }
            }

            //caso nenhum telefone tenha sido escolhido, parar a operação.
            if (UteisValidacao.emptyString(numero)) {
                throw new Exception("SMS não foi enviado, nenhum celular selecionado");
            }

            //LOG - INICIO
            boolean historicoContatoVONovoObj = true;
            try {
                if (!historicoContatoVO.isNovoObj()) {
                    historicoContatoVONovoObj = false;
                }
            } catch (Exception e) {
                e.printStackTrace();
            }

            setSucesso(false);
            setErro(true);
            String retorno = "SMS não foi enviado, nenhum celular selecionado";
            String obs = this.getHistoricoContatoVO().getObservacao().replaceAll(Pattern.quote("<") + "p" + Pattern.quote(">"), "").replaceAll(Pattern.quote("</") + "p" + Pattern.quote(">"), "");

            if (obs.trim().isEmpty()) {
                setSucesso(false);
                setErro(true);
                throw new Exception("Para o envio de SMS, por favor preencha o campo Observação e tente novamente.");
            }

            if (obs.length() > Uteis.TAMANHO_MSG_SMS) {
                throw new Exception("A mensagem não pode ter mais que 140 caracteres");
            }

            // aqui não é necessário fazer a verificação do número pois já foi feita acima
            setTokenSMSEmpresa(getFacade().getEmpresa().obterTokenSMS(getEmpresaLogado().getCodigo()));
            setTokenSMSEmpresaShortCode(getFacade().getEmpresa().obterTokenSMSShortCode(getEmpresaLogado().getCodigo()));

            SmsController smsController = new SmsController((getTokenSMSEmpresa().isEmpty() ? getTokenSMSEmpresaShortCode() : getTokenSMSEmpresa()), getKey(), TimeZone.getTimeZone(malaDiretaVO.getEmpresa().getTimeZoneDefault()));

            if (!UteisValidacao.emptyNumber(historicoContatoVO.getClienteVO().getCodigo())) {
                retorno = smsController.sendMessage(null, new Message().setNumero(numero).setMsg(ModeloMensagemVO.personalizarTagNomePessoa(obs, historicoContatoVO.getClienteVO().getPessoa().getNome())));
            } else if (!UteisValidacao.emptyNumber(historicoContatoVO.getIndicadoVO().getCodigo())) {
                retorno = smsController.sendMessage(null, new Message().setNumero(numero).setMsg(ModeloMensagemVO.personalizarTagNomePessoa(obs, historicoContatoVO.getIndicadoVO().getNomeIndicado())));
            } else {
                retorno = smsController.sendMessage(null, new Message().setNumero(numero).setMsg(ModeloMensagemVO.personalizarTagNomePessoa(obs, historicoContatoVO.getPassivoVO().getNome())));
            }

            String mensagemApresentar = "";
            if (retorno.toUpperCase().contains("STATUS: OK")) {
                malaDiretaVO.setUsuarioVO(getUsuarioLogado());
                getFacade().getHistoricoContato().gravarHistoricoContato("SR", historicoContatoVO, agendaVO, malaDiretaVO, getEmpresaLogado().getCodigo());


                String identificadorMeta = getMetaDetalhadoVOSelecionado().getFecharMeta().getIdentificadorMeta();
                mensagemApresentar = "Envio realizado com sucesso.";

                if (!this.getHistoricoContatoVO().getContatoAvulso() && (getConfiguracaoSistemaCRMVO().isBaterMetaTodasAcoes() || identificadorMeta.equals("AN")
                        || identificadorMeta.equals("PV")
                        || identificadorMeta.equals("RI")
                        || identificadorMeta.equals("FA")
                        || identificadorMeta.equals("SF")
                        || identificadorMeta.equals("SA")
                        || identificadorMeta.equals("LA")
                        || identificadorMeta.equals("CR"))) {
                    mensagemApresentar += " A meta será batida.";
                }

                if (!this.getHistoricoContatoVO().getContatoAvulso()) {
                    String tipoContato = "SR";
                    baterMeta(tipoContato, getMetaDetalhadoVOSelecionado(), false);
                } else {
                    recarregarContatoAvulso();
                    consultarListaTelefones();
                }
            } else {
                throw new Exception(retorno);
            }

            //LOG - INICIO
            try {
                if (historicoContatoVONovoObj) {
                    historicoContatoVO.setObjetoVOAntesAlteracao(new HistoricoContatoVO());
                    historicoContatoVO.setNovoObj(true);
                }
                registrarLogObjetoVO(historicoContatoVO, historicoContatoVO.getCodigo(), "HISTORICOCONTATO - ENVIO SMS", historicoContatoVO.getClienteVO().getPessoa().getCodigo());
            } catch (Exception e) {
                e.printStackTrace();
            }
            //LOG - FIM

            setSucesso(true);
            setErro(false);
            setMensagemID("msg_email_enviado");
            montarMsgGenerica("Enviar SMS", mensagemApresentar, true, null, null, "panelGridLeft, panelGridCenter, panelGridRight");

        } catch (Exception e) {
            setSucesso(false);
            setErro(true);
            setMensagemDetalhada("msg_erro_naoGravados", e.getMessage());
            montarMsgGenerica("Enviar SMS", e.getMessage(), true, null, null, "panelGridLeft, panelGridCenter, panelGridRight");
        }
    }

    public void consultarListaTelefones() {
        setListaTelefones(this.getHistoricoContatoVO().getClienteVO().getPessoa().getTelefoneVOs());
    }

    public List getListaEmail() {
        if (listaEmail == null) {
            listaEmail = new ArrayList();
        }
        return listaEmail;
    }

    public void setListaEmail(List listaEmail) {
        this.listaEmail = listaEmail;
    }

    public boolean isMostrarModeloDeMensagem() {
        return mostrarModeloDeMensagem;
    }

    public void setMostrarModeloDeMensagem(boolean mostrarModeloDeMensagem) {
        this.mostrarModeloDeMensagem = mostrarModeloDeMensagem;
    }

    public void apresentarModeloMensagem() {
        if (mostrarModeloDeMensagem) {
            setMostrarModeloDeMensagem(false);
            this.getMalaDiretaVO().setModeloMensagem(new ModeloMensagemVO());
            this.getMalaDiretaVO().setMensagem("");
            this.getHistoricoContatoVO().setObservacao("");
        } else {
            setMostrarModeloDeMensagem(true);
        }
    }

    public List getListaModeloMensagem() {
        List<SelectItem> listaModeloMensagem = new ArrayList<SelectItem>();
        try{
            List<ModeloMensagemVO> resultadoConsulta = getFacade().getModeloMensagem().consultarPorTitulo("", meioEnvio, false, Uteis.NIVELMONTARDADOS_TODOS);
            Ordenacao.ordenarLista(resultadoConsulta, "titulo");
            listaModeloMensagem.add(new SelectItem(0, "Selecione o Modelo de Mensagem"));
            for (ModeloMensagemVO obj : resultadoConsulta) {
                listaModeloMensagem.add(new SelectItem(obj.getCodigo(), obj.getTitulo() + " (" + obj.getCodigo() + ")"));
            }
        } catch (Exception e) {
            montarErro(e);
        }
        return listaModeloMensagem;
    }

    public void selecionarLinkCadastroCartaoOnline() {
        try {
            this.getHistoricoContatoVO().setObservacao("");
            this.getMalaDiretaVO().setMensagem("");

            VendasOnlineService vendasOnlineService = new VendasOnlineService(null, Conexao.getFromSession());
            String link = vendasOnlineService.obterLinkPagamentoVendasOnline(getKey(), historicoContatoVO.getClienteVO(), getEmpresaLogado().getCodigo(),
                    false, OrigemCobrancaEnum.ZW_HISTORICO_CONTATO, null, getUsuarioLogado(), null, null, null, 1);

            String conteudo = "Este é o seu link para cadastrar o cartão de crédito na empresa: TAG_EMPRESA\n" +
                    "URL_LINK_PAGAMENTO";
            conteudo = conteudo.replaceAll("TAG_EMPRESA", getEmpresaLogado().getNome()+".");
            conteudo = conteudo.replaceAll("URL_LINK_PAGAMENTO", link);

            if (meioEnvio != null && meioEnvio == MeioEnvio.EMAIL) {
                this.getMalaDiretaVO().setMensagem(conteudo);
            }else if (meioEnvio != null && meioEnvio == MeioEnvio.SMS) {
                this.getHistoricoContatoVO().setObservacao(conteudo.replace("cartão de crédito", "cartão"));
            }else{
                this.getHistoricoContatoVO().setObservacao(conteudo);
            }
        }catch (Exception ignore){
        }
    }

    public void selecionarModeloMensagem()  {
        try{
            if (!this.getMalaDiretaVO().getModeloMensagem().getCodigo().equals(0)) {
                if (meioEnvio == MeioEnvio.EMAIL) {
                    ModeloMensagemVO obj = getFacade().getModeloMensagem().consultarPorChavePrimaria(this.getMalaDiretaVO().getModeloMensagem().getCodigo(), Uteis.NIVELMONTARDADOS_TODOS);
                    obj.verificarSeExisteImagemModelo(false, getKey());
                    if (getMensagemDetalhada().equals("")) {
                        this.getMalaDiretaVO().setModeloMensagem(obj);
                        this.getMalaDiretaVO().setMensagem(this.getMalaDiretaVO().getModeloMensagem().getMensagem());
                    }
                } else if (meioEnvio == MeioEnvio.SMS) {
                    ModeloMensagemVO obj = getFacade().getModeloMensagem().consultarPorChavePrimaria(this.getMalaDiretaVO().getModeloMensagem().getCodigo(), Uteis.NIVELMONTARDADOS_TODOS);
                    this.getMalaDiretaVO().setModeloMensagem(obj);
                    this.getHistoricoContatoVO().setObservacao(this.getMalaDiretaVO().getModeloMensagem().getMensagem());
                } else if (meioEnvio == MeioEnvio.APP) {
                    ModeloMensagemVO obj = getFacade().getModeloMensagem().consultarPorChavePrimaria(this.getMalaDiretaVO().getModeloMensagem().getCodigo(), Uteis.NIVELMONTARDADOS_TODOS);
                    this.getMalaDiretaVO().setModeloMensagem(obj);
                    this.getHistoricoContatoVO().setObservacao(this.getMalaDiretaVO().getModeloMensagem().getMensagem());
                }
            } else {
                this.getMalaDiretaVO().setModeloMensagem(new ModeloMensagemVO());
            }
        } catch (Exception e) {
            montarErro(e);
        }
    }

    public void confirmarComparecimento() {
        try {
            FecharMetaDetalhadoVO obj = metaDetalhadoVOSelecionado;

            if (!UteisValidacao.emptyNumber(obj.getAgenda().getModalidade().getCodigo())) {
                obj.getAgenda().setModalidade(getFacade().getModalidade().consultarPorChavePrimaria(obj.getAgenda().getModalidade().getCodigo(), Uteis.NIVELMONTARDADOS_TELACONSULTA));
            }
            getFacade().getAgenda().alterarAgendadosComparecido(obj.getAgenda(), getUsuarioLogado(), true);
            getFacade().getHistoricoContato().inserirHistoricoConfirmarAgendamento(obj.getAgenda(), getTipoMetaSelecionada().getFasesCRMEnum().getSigla(), false);

            String tipoContato = "CP";
            baterMeta(tipoContato, getMetaDetalhadoVOSelecionado(), false);

            confirmarDesmarcarPresencaAulaExperimentalTreino(obj, true);

            setSucesso(true);
            setErro(false);
            montarMsgGenerica("Agendamento", "Agendamento Confirmado", true, null, null, "botoesAgendamento, panelGridLeft, panelGridCenter, panelGridRight");
        } catch (Exception e) {
            setSucesso(false);
            setErro(true);
            setMensagemDetalhada("msg_erro", e.getMessage());
            montarMsgGenerica("Agendamento", e.getMessage(), true, null, null, "botoesAgendamento, panelGridLeft, panelGridCenter, panelGridRight");
        }
    }

    private void confirmarDesmarcarPresencaAulaExperimentalTreino(FecharMetaDetalhadoVO obj, boolean marcar) throws Exception {
        if (configuracaoSistemaCRMVO.isDirecionaragendamentosexperimentaisagenda()) {
            int codigoPassivo = 0;
            int codigoIndicado = 0;
            int codigoCliente = 0;
            if (!UteisValidacao.emptyNumber(obj.getCliente().getCodigo())) {
                codigoCliente = obj.getCliente().getCodigo();
            } else if (!UteisValidacao.emptyNumber(obj.getIndicado().getCodigo())) {
                codigoIndicado = obj.getIndicado().getCodigo();
            } else {
                codigoPassivo = obj.getPassivo().getCodigo();
            }
            HorarioTurmaVO aulaExperimental = getFacade().getTurma().obterAulaExperimentalPassivoIndicado(
                    obj.getDia(), codigoPassivo, codigoIndicado, codigoCliente);

            if (aulaExperimental != null) {
                String urlTreino = PropsService.getPropertyValue(getKey(), PropsService.urlTreino);
                String fullUrl = urlTreino + "/prest/psec/agenda/turmas/" + aulaExperimental.getCodigo() + (marcar ? "/confirmar-presenca" : "/desconfirmar-presenca");

                Map<String, String> headers = new HashMap<>();
                headers.put("Authorization", AutenticacaoMsService.token(getKey()));
                headers.put("empresaId", getEmpresaLogado().getCodigo().toString());
                RequestHttpService httpService = new RequestHttpService();

                Map<String, String> params = new HashMap<>();
                params.put("dia", Calendario.getData(aulaExperimental.getDataAula(), "yyyyMMdd"));
                params.put("codigoPassivo", String.valueOf(codigoPassivo));
                params.put("codigoIndicado", String.valueOf(codigoIndicado));
                if (!UteisValidacao.emptyNumber(codigoCliente)) {
                    params.put("matricula", obj.getCliente().getMatricula());
                }
                params.put("origemSistema", "ZW");
                params.put("alunoVinculoAula", "AULA_EXPERIMENTAL");

                RespostaHttpDTO respostaHttpDTO = httpService.executeRequest(fullUrl, headers, params, null, MetodoHttpEnum.PUT);
                String retornoConsulta = respostaHttpDTO.getResponse();
                new JSONObject(retornoConsulta).has("content");
            }
        }
    }

    public void cancelarComparecimento() {
        try {
            FecharMetaDetalhadoVO obj = metaDetalhadoVOSelecionado;
            getFacade().getAgenda().cancelarAgendadosComparecido(obj.getAgenda());
            getFacade().getHistoricoContato().inserirHistoricoConfirmarAgendamento(obj.getAgenda(), getTipoMetaSelecionada().getFasesCRMEnum().getSigla(), true);
            retiraMetaDeComparecimento(obj);
            confirmarDesmarcarPresencaAulaExperimentalTreino(obj, false);
            setSucesso(true);
            setErro(false);
            montarMsgGenerica("Agendamento", "Este agendamento não está mais confirmado. (Sua meta será modificada)", true, null, null, "botoesAgendamento, panelGridLeft, panelGridCenter, panelGridRight");
        } catch (Exception e) {
            setSucesso(false);
            setErro(true);
            setMensagemDetalhada("msg_erro", e.getMessage());
            montarMsgGenerica("Agendamento", e.getMessage(), true, null, null, "botoesAgendamento, panelGridLeft, panelGridCenter, panelGridRight");
        }
    }

    public void reagendarAgendamento() {
        try {
            setModalMensagemGenerica("");
            if (getEmpresaLogado() != null) {
                agendaVO.setEmpresa(getEmpresaLogado().getCodigo());
            }
            getHistoricoContatoVO().setObjecaoVO(new ObjecaoVO());
            //LOG - INICIO
            boolean historicoContatoVONovoObj = true;
            try {
                if (!historicoContatoVO.isNovoObj()) {
                    historicoContatoVONovoObj = false;
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
            ConfiguracaoSistemaCRMVO configuracaoSistemaCRMVO = getFacade().getConfiguracaoSistemaCRM().consultarConfiguracaoSistemaCRM(Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            Date dataLimite = getFacade().getConfiguracaoSistemaCRM().obterDataCalculadaDiasUteis(Calendario.hoje(), false,
                    configuracaoSistemaCRMVO.getNrDiasLimiteAgendamentoFuturo(), getEmpresaLogado());
            if (Calendario.maior(agendaVO.getDataAgendamento(), dataLimite) && (agendaVO.getTipoAgendamento().equals("AE") || agendaVO.getTipoAgendamento().equals("VI"))) {
                throw new ConsistirException("Agendamento com data superior a " + configuracaoSistemaCRMVO.getNrDiasLimiteAgendamentoFuturoDescricao()
                        + " só pode ser do tipo Ligação. Veja campo 'Número de dias limite para agendamento futuro' nas configurações do CRM.");
            }
            if (!agendaVO.getTipoAgendamento().equals("AE")) {
                agendaVO.setModalidade(new ModalidadeVO());
            }

            if (agendaVO.getHoraMinuto().isEmpty()) {
                throw new ConsistirException(("Informe o horário."));
            } else if (agendaVO.getHoraMinuto().length() != 5) {
                throw new ConsistirException(("Informe o horário corretamente."));
            }

            String[] horaMinuto = agendaVO.getHoraMinuto().split(":");
            agendaVO.setHora(horaMinuto[0]);
            agendaVO.setMinuto(horaMinuto[1]);

            validarHorario(agendaVO);

            getFacade().getHistoricoContato().gravarHistoricoContato("AG", historicoContatoVO, agendaVO, malaDiretaVO, getEmpresaLogado().getCodigo());

            boolean atualizarTodasMetas = false;
            if (Calendario.igual(Calendario.hoje(), agendaVO.getDataAgendamento())) {
                atualizarTodasMetas = true;
            }

            agendaVO = new AgendaVO();
            //LOG - INICIO
            try {
                if (historicoContatoVONovoObj) {
                    historicoContatoVO.setObjetoVOAntesAlteracao(new HistoricoContatoVO());
                    historicoContatoVO.setNovoObj(true);
                }
                registrarLogObjetoVO(historicoContatoVO, historicoContatoVO.getCodigo(), "HISTORICOCONTATO", historicoContatoVO.getClienteVO().getPessoa().getCodigo());
            } catch (Exception e) {
                e.printStackTrace();
            }

            String tipoContato = "RE";
            if(historicoContatoVO.getFase().equals(FasesCRMEnum.AGENDAMENTO.getSigla())){
                    tipoContato = FasesCRMEnum.AGENDAMENTO.getSigla();
            }
            baterMeta(tipoContato, getMetaDetalhadoVOSelecionado(), atualizarTodasMetas);

            setSucesso(true);
            setErro(false);
            montarMsgGenerica("Reagendamento", "Reagendamento realizado com Sucesso", true, null, null, "panelGridLeft, panelGridCenter, panelGridRight");
        } catch (Exception e) {
            String msg = e.getMessage();
            if(msg == null){
                msg = "Ocorre um erro ao salvar o reagendamento.";
            }
            setMensagemDetalhada("", msg);
            montarMsgGenerica("Reagendamento", msg,
                    true, null, null, "panelGridLeft, panelGridCenter, panelGridRight");

        }
    }

    public Boolean getApresentarBotaoConfirmarAgendamento() {
        if (getTipoMetaSelecionada().getFasesCRMEnum() != null) {
            return ("AG").contains(getTipoMetaSelecionada().getFasesCRMEnum().getSigla()) && (getMetaDetalhadoVOSelecionado().getAgenda().getDataComparecimento() == null && !getMetaDetalhadoVOSelecionado().getAgenda().getTipoAgendamento().equals("LI"));
        } else {
            return false;
        }
    }

    public Boolean getApresentarBotaoCancelarAgendamento() {
        if (getTipoMetaSelecionada().getFasesCRMEnum() != null) {
            return ("AG").contains(getTipoMetaSelecionada().getFasesCRMEnum().getSigla()) && (getMetaDetalhadoVOSelecionado().getAgenda().getDataComparecimento() != null && !getMetaDetalhadoVOSelecionado().getAgenda().getTipoAgendamento().equals("LI"));
        } else {
            return false;
        }

    }

    public Boolean  getApresentarBotaoReagendarAgendamento() {
        if (getTipoMetaSelecionada().getFasesCRMEnum() != null) {
            return ("AG").contains(getTipoMetaSelecionada().getFasesCRMEnum().getSigla());
        } else {
            return false;
        }
    }

    public boolean isMostrarPanelReagendamento() {
        return mostrarPanelReagendamento;
    }

    public void setMostrarPanelReagendamento(boolean mostrarPanelReagendamento) {
        this.mostrarPanelReagendamento = mostrarPanelReagendamento;
    }

    public void apresentarPanelReagendamento() throws Exception {
        try{
            String comentario = getHistoricoContatoVO().getObservacao();
            if (mostrarPanelReagendamento) {
                setMostrarPanelReagendamento(false);
            } else {
                novoHistoricoContato();
    //            setHistoricoContatoVO(getFacade().getHistoricoContato().inicializarDadosAgendadosRealizacaoContato(this.getHistoricoContatoVO(), getAgendaVO(), obj, getUsuarioLogado(), getColaboradorResponsavel(), getMalaDiretaVO(), getMalaDiretaEnviadaVO(), null, null, true));
                FecharMetaDetalhadoVO obj = metaDetalhadoVOSelecionado;

                if (obj.getPassivo().getCodigo() != 0) {
                    this.getHistoricoContatoVO().setPassivoVO(getFacade().getPassivo().consultarPorChavePrimaria(obj.getPassivo().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSENTIDADESPRINCIPAIS));
                } else if (obj.getIndicado().getCodigo() != 0) {
                    this.getHistoricoContatoVO().setIndicadoVO(getFacade().getIndicado().consultarPorChavePrimaria(obj.getIndicado().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSENTIDADESPRINCIPAIS));
                } else {
                    this.getHistoricoContatoVO().setClienteVO(getFacade().getCliente().consultarPorChavePrimaria(obj.getCliente().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSENTIDADESPRINCIPAIS));
                }

                this.getHistoricoContatoVO().setTipoOperacao("RE");
                this.getAgendaVO().setNovoObj(true);
                this.getAgendaVO().setTipoAgendamento(getMetaDetalhadoVOSelecionado().getAgenda().getTipoAgendamento());
                this.getAgendaVO().setModalidade(getMetaDetalhadoVOSelecionado().getAgenda().getModalidade());
                this.getHistoricoContatoVO().setFase(getTipoMetaSelecionada().getFasesCRMEnum().getSigla());
                this.getHistoricoContatoVO().setCodigoFecharMetaDetalhado(obj.getCodigo());
                this.getHistoricoContatoVO().setDiaAbertura(obj.getFecharMeta().getAberturaMetaVO().getDia());
                this.getHistoricoContatoVO().setAberturaMetaEstaEmAberta(obj.getFecharMeta().getAberturaMetaVO().getMetaEmAberto());
                this.getHistoricoContatoVO().setObservacao(comentario);
                this.getMalaDiretaVO().setRemetente(getUsuarioLogado());
                this.getHistoricoContatoVO().setNovoObj(true);
                this.getHistoricoContatoVO().setColaboradorResponsavel(getColaboradorResponsavel());
                this.getHistoricoContatoVO().setResponsavelCadastro(getUsuarioLogado());
                this.getHistoricoContatoVO().setReagendamentoVO(obj.getAgenda());
                setMostrarPanelReagendamento(true);

            }
        } catch (Exception e) {
            montarErro(e);

        }
    }

    public List getListaSelectItemTipoAgendamentoReagendamento() throws Exception {
        List objs = new ArrayList();
        Hashtable tipoAgendamentos = (Hashtable) Dominios.getTipoAgendamento();
        Enumeration keys = tipoAgendamentos.keys();
        while (keys.hasMoreElements()) {
            String value = (String) keys.nextElement();
            String label = (String) tipoAgendamentos.get(value);
            objs.add(new SelectItem(value, label));
        }
        SelectItemOrdemValor ordenador = new SelectItemOrdemValor();
        Collections.sort((List) objs, ordenador);
        return objs;
    }

    public List<GrupoColaboradorVO> getListaGrupoColaborador() {
        if (listaGrupoColaborador == null) {
            listaGrupoColaborador = new ArrayList<GrupoColaboradorVO>();
        }
        return listaGrupoColaborador;
    }

    public void setListaGrupoColaborador(List<GrupoColaboradorVO> listaGrupoColaborador) {
        this.listaGrupoColaborador = listaGrupoColaborador;
    }

    public void montarListaGrupoColaborador() throws Exception {
        try {
            Date d1 = Calendario.hoje();

            GrupoTelaControle grupoTelaControle = (GrupoTelaControle) context().getExternalContext().getSessionMap().get("GrupoTelaControle");
            if (grupoTelaControle == null) {
                grupoTelaControle = new GrupoTelaControle();
            }
            List<GrupoColaboradorVO> lista = grupoTelaControle.getListaGrupos();
            Ordenacao.ordenarLista(lista, "descricaoOrdenacao");
            for (GrupoColaboradorVO obj : lista) {
                Ordenacao.ordenarLista(obj.getGrupoColaboradorParticipanteVOs(), "nomeColaboradorParticipante");
            }

            for (UsuarioVO usuario : getListaUsuariosSelecionados()) {
                for (GrupoColaboradorVO grupoColaboradorVO : lista) {
                    for (GrupoColaboradorParticipanteVO grupoColaboradorParticipanteVO : grupoColaboradorVO.getGrupoColaboradorParticipanteVOs()) {
                        if (grupoColaboradorParticipanteVO.getColaboradorParticipante().getCodigo().equals(usuario.getColaboradorVO().getCodigo())) {
                            grupoColaboradorParticipanteVO.setGrupoColaboradorParticipanteEscolhido(true);
                        }
                    }
                    Ordenacao.ordenarLista(grupoColaboradorVO.getGrupoColaboradorParticipanteVOs(), "nomeColaboradorParticipante");
                }
            }

            setListaGrupoColaborador(lista);

            Date d2 = Calendario.hoje();
            System.out.println("Montar Lista de Colaboradores: " + (d2.getTime() - d1.getTime()));
        } catch (Exception e) {
            setListaGrupoColaborador(new ArrayList<GrupoColaboradorVO>());
            setMensagemDetalhada("", e.getMessage());
            montarMsgAlert(e.getMessage());
        }
    }

    public List<UsuarioVO> getListaUsuariosSelecionados() {
        if (listaUsuariosSelecionados == null) {
            listaUsuariosSelecionados = new ArrayList<UsuarioVO>();
        }
        return listaUsuariosSelecionados;
    }

    public void setListaUsuariosSelecionados(List<UsuarioVO> listaUsuariosSelecionados) {
        this.listaUsuariosSelecionados = listaUsuariosSelecionados;
    }

    public List<ClienteVO> executarAutocompleteCliente(Object suggest) {
        String pref = (String) suggest;
        ArrayList<ClienteVO> clientes = new ArrayList<ClienteVO>();
        try {
            if (pref.equals("%")) {
                clientes = (ArrayList<ClienteVO>) getFacade().getCliente().consultarTodosClienteComLimite(getEmpresaLogado().getCodigo(), false, Uteis.NIVELMONTARDADOS_MINIMOS);
            } else {
                clientes = (ArrayList<ClienteVO>) getFacade().getCliente().consultarPorNomeCliente(pref, getEmpresaLogado().getCodigo(), false, Uteis.NIVELMONTARDADOS_MINIMOS, 50);
            }
            getIndicacaoVO().setColaboradorQueIndicou(new ColaboradorVO());
        } catch (Exception ex) {
            clientes = (new ArrayList<ClienteVO>());
        }
        return clientes;
    }

    public List<ColaboradorVO> executarAutocompleteColaborador(Object suggest) {
        String pref = (String) suggest;
        ArrayList<ColaboradorVO> colaboradores = new ArrayList<ColaboradorVO>();
        try {
            if (pref.equals("%")) {
                colaboradores = (ArrayList<ColaboradorVO>) getFacade().getColaborador().consultarTodosColaboradorComLimite(getEmpresaLogado().getCodigo(), false, Uteis.NIVELMONTARDADOS_TELACONSULTA);
            } else {
                colaboradores = (ArrayList<ColaboradorVO>) getFacade().getColaborador().consultarPorNomeColaboradorComLimite(pref, getEmpresaLogado().getCodigo(), false, Uteis.NIVELMONTARDADOS_TELACONSULTA);
            }
            getIndicacaoVO().setClienteQueIndicou(new ClienteVO());
        } catch (Exception ex) {
            colaboradores = (new ArrayList<ColaboradorVO>());
        }
        return colaboradores;
    }

    public void selecionarClienteSuggestionBox() throws Exception {
        ClienteVO clienteVO = (ClienteVO) request().getAttribute("clientes");
        if (clienteVO != null) {
            getIndicacaoVO().setClienteQueIndicou(clienteVO);
            getIndicacaoVO().setColaboradorQueIndicou(new ColaboradorVO());
        }
    }

    public void selecionarColaboradorSuggestionBox() throws Exception {
        ColaboradorVO colaboradorVO = (ColaboradorVO) request().getAttribute("colaboradores");
        if (colaboradorVO != null) {
            getIndicacaoVO().setColaboradorQueIndicou(colaboradorVO);
            getIndicacaoVO().setClienteQueIndicou(new ClienteVO());
        }
    }

    public void verificaColaboradorResponsavelPorMeta() throws Exception{
        List<String> tipos = getFacade().getFecharMeta().consultarTipoColaborador(getUsuario().getColaboradorVO().getCodigo());
        Boolean colaboradorEResponsavelFase = getFacade().getFecharMeta().consultarColaboradorResFase(getTipoMetaSelecionada().getFasesCRMEnum().getCodigo(), tipos);
        if(!colaboradorEResponsavelFase){
            setErro(true);
            throw new Exception();
        }
    }

    public void gravarIndicacaoMetaIndicacao() throws Exception {
        if (validarMetaNaoAberta()) {
            throw new Exception("Não existe meta para a data atual.");
        }
        getIndicacaoVO().setEmpresa(getEmpresaLogado());
        getIndicacaoVO().setResponsavelCadastro(getUsuarioLogado());
        if(usuarioSelecionado && !nomeUsuario.isEmpty()){
            getIndicacaoVO().setColaboradorResponsavel(getUsuario());
        }else {
            getIndicacaoVO().setColaboradorResponsavel(getUsuarioLogado());
        }
        getIndicacaoVO().setDia(Calendario.hoje());
        getIndicacaoVO().setDiaAbertura(Calendario.hoje());
        getIndicacaoVO().adicionarObjIndicadoVOs(getIndicadoVO());
        FecharMetaVO fecharMetaVO = null;
        try{
            fecharMetaVO = getFacade().getFecharMeta().consultarPorIdentificadorMetaPorDiaPorColaborador(FasesCRMEnum.INDICACOES.getSigla(), getIndicacaoVO().getDiaAbertura(), getIndicacaoVO().getColaboradorResponsavel().getCodigo(), false, getIndicacaoVO().getEmpresa().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        }catch(Exception ex){
            throw new ConsistirException("Não foi identificado meta para o dia de hoje. É necessário uma meta aberta!");
        }
        if(usuarioSelecionado && !nomeUsuario.isEmpty()) {
            try {
                verificaColaboradorResponsavelPorMeta();
            } catch (Exception e) {
                setSucesso(false);
                setErro(true);
                throw new ConsistirException("O colaborador selecionado não possui responsabilidade configurada em \"Responsáveis pelas Fases\". " +
                        "\n É necessário configurar as fases de agendamento para este colaborador.");
            }
        }

        if (getIndicacaoVO().isNovoObj()) {
            if (getIndicacaoVO().getClienteQueIndicou() != null && !UteisValidacao.emptyNumber(getIndicacaoVO().getClienteQueIndicou().getCodigo()) && getIndicacaoVO().getEmpresa().isTrabalharComPontuacao())
                getFacade().getHistoricoPontos().incluirPontuacaoPorTipo(TipoItemCampanhaEnum.INDICACAO, null, "", getIndicacaoVO().getClienteQueIndicou(), getEmpresaLogado());
            getFacade().getIndicacao().incluirIndicacaoMeta(getIndicacaoVO(), fecharMetaVO);
        } else {
            if(usuarioSelecionado && !nomeUsuario.isEmpty()) {
                getIndicacaoVO().setColaboradorResponsavel(getUsuario());
                getFacade().getIndicacao().alterar(getIndicacaoVO());
            }else {
                getFacade().getIndicacao().alterar(getIndicacaoVO());
            }
            if (getTipoMetaSelecionada().getFasesCRMEnum().getSigla().equals(FasesCRMEnum.INDICACOES_SEM_CONTATO.getSigla())) {
                setConfPaginacao(new ConfPaginacao(NUMERO_ITENS_CONSULTAR));
                consultarMetaDetalhada(getTipoMetaSelecionada(), false);
            }
        }
    }

    public void gravarIndicacaoMetaIndicacaoSalvar() {
        try {
            gravarIndicacaoMetaIndicacao();

            if (!getTipoMetaSelecionada().getFasesCRMEnum().getSigla().equals(FasesCRMEnum.INDICACOES_SEM_CONTATO.getSigla())) {
                setIndicacaoVO(new IndicacaoVO());
                getIndicacaoVO().setIndicadoVOs(new ArrayList<IndicadoVO>());
                setIndicadoVO(new IndicadoVO());
                setHistoricoContatoVO(new HistoricoContatoVO());
            }
            FasesCRMEnum faseAtual = getTipoMetaSelecionada().getFasesCRMEnum();
            TipoMetaCRMTO obj = new TipoMetaCRMTO();
            boolean metaSelecionada = false;
            for (MetaCRMTO metaCRMTO : getListaMetaCRM()) {
                for (TipoMetaCRMTO tipoMetaCRMTO : metaCRMTO.getListaTipoMetaVO()) {
                    if (tipoMetaCRMTO.getFasesCRMEnum().equals(faseAtual)) {
                        obj = tipoMetaCRMTO;
                        break;
                    }
                }
                if (metaSelecionada) {
                    break;
                }
            }
            obj.setSituacaoAtualMetaEnum(SituacaoAtualMetaEnum.META_SELECIONADA);
            setTipoMetaSelecionada(obj);
            consultarMetaDetalhada(obj, false);
            recarregarIndicadores();
            setMostrarPanelAgendarIndicacao(false);
            setMostrarPanelObjecaoIndicacao(false);
            setSucesso(true);
            setErro(false);
            montarMsgGenerica("Nova Indicação", "Indicação gravada com sucesso!", true, null, null, "panelGridLeft, panelGridCenter, panelGridRight");
            setNenhumUsuario();
            apresentarIndicacao();
        } catch (Exception e) {
            String msgErroIndicacao = "Erro ao gravar indicação." +
                    "\r\n<br/>" + e.getMessage();
            setMensagemDetalhada("msg_erro", e.getMessage());
            setSucesso(false);
            setErro(true);
            montarMsgGenerica("Nova Indicação", msgErroIndicacao, true, null, null, "panelGridLeft, panelGridCenter, panelGridRight");
        }
    }
    public void gravarIndicacaoMetaIndicacaoAgendar() {
        try {
            if (this.getAgendaVO().getHoraMinuto().isEmpty()) {
                throw new ConsistirException(("Informe o horário."));
            } else if (this.getAgendaVO().getHoraMinuto().length() != 5) {
                throw new ConsistirException(("Informe o horário corretamente."));
            }
            String[] horaMinuto = this.getAgendaVO().getHoraMinuto().split(":");
            this.getAgendaVO().setHora(horaMinuto[0]);
            this.getAgendaVO().setMinuto(horaMinuto[1]);
            validarHorario(this.getAgendaVO());
            this.getHistoricoContatoVO().setAgendaVO(this.getAgendaVO());

            gravarIndicacaoMetaIndicacaoAgendarOuObjecao("AG");

            boolean atualizarTodasMetas = false;
            if (Calendario.igual(Calendario.hoje(), this.getHistoricoContatoVO().getAgendaVO().getDataAgendamento())) {
                atualizarTodasMetas = true;
            }
            String tipoContato = "IN";
            baterMeta(tipoContato, getMetaDetalhadoVOSelecionado(), atualizarTodasMetas);

            setSucesso(true);
            setErro(false);
            montarMsgGenerica("Agendar nova indicação", "Agendamento da indicação gravada com sucesso!", true, null, null, "panelGridLeft, panelGridRight");
            setNenhumUsuario();
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
            setSucesso(false);
            setErro(true);
            montarMsgGenerica("Agendar nova indicação", e.getMessage(), true, null, null, "panelGridLeft, panelGridRight");
        }
    }

    public void gravarIndicacaoMetaIndicacaoObjecao() {
        try {
            gravarIndicacaoMetaIndicacaoAgendarOuObjecao("OB");
            String tipoContato = "IN";
            baterMeta(tipoContato, getMetaDetalhadoVOSelecionado(), false);

            setSucesso(true);
            setErro(false);
            montarMsgGenerica("Objeção de indicação", "Objeção da indicação gravada com sucesso!", true, null, null, "panelGridLeft, panelGridCenter, panelGridRight");
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
            setSucesso(false);
            setErro(true);
            montarMsgGenerica("Objeção de indicação", e.getMessage(), true, null, null, "panelGridLeft, panelGridCenter, panelGridRight");
        }
    }

    public void gravarIndicacaoMetaIndicacaoAgendarOuObjecao(String agendaOuObjecao) throws Exception {
        if (!isSomenteEditarIndicado()) {
            gravarIndicacaoMetaIndicacao();
        }
        getIndicacaoVO().setEmpresa(getEmpresaLogado());

        if(usuarioSelecionado && !nomeUsuario.isEmpty()){
            this.getIndicacaoVO().setColaboradorResponsavel(getUsuario());
        }

        if (configuracaoSistemaCRMVO.isDirecionaragendamentosexperimentaisagenda() &&
                !UteisValidacao.emptyNumber(codigoAula) && (getTipoMetaSelecionada().getFasesCRMEnum().equals(FasesCRMEnum.LEADS_HOJE) ||
                getTipoMetaSelecionada().getFasesCRMEnum().equals(FasesCRMEnum.LEADS_ACUMULADAS) ||
                getTipoMetaSelecionada().getFasesCRMEnum().equals(FasesCRMEnum.INDICACOES_SEM_CONTATO) ||
                getTipoMetaSelecionada().getFasesCRMEnum().equals(FasesCRMEnum.PASSIVO))) {
            String horaDeFato = getAulasAgenda().stream().filter(a -> a.getCodigo().equals(codigoAula)).findFirst().get().getHoraInicial();
            if (!UteisValidacao.emptyString(horaDeFato)) {
                this.getHistoricoContatoVO().getAgendaVO().setHoraMinuto(horaDeFato);
            }

            TurmasServiceImpl turmasService = new TurmasServiceImpl(Conexao.getFromSession());
            try {
                this.getHistoricoContatoVO().getAgendaVO().setAlunohorarioturma(
                        turmasService.inserirAulaExperimentalAgenda(codigoAula, null, null, getIndicadoVO(), this.getHistoricoContatoVO().getAgendaVO().getDataAgendamento(), getUsuarioLogado()));
            } catch (Exception ex) {
                ex.printStackTrace();
            } finally {
                turmasService = null;
            }

        }

        getFacade().getIndicacao().incluirHistoricoContatoIndicado(getIndicacaoVO(), getIndicadoVO(), this.getHistoricoContatoVO(), agendaOuObjecao);

        FecharMetaVO fecharMetaVO = getFacade().getFecharMeta().consultarPorIdentificadorMetaPorDiaPorColaborador(FasesCRMEnum.INDICACOES.getSigla(), getIndicacaoVO().getDia(), getIndicacaoVO().getColaboradorResponsavel().getCodigo(), false, getIndicacaoVO().getEmpresa().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        getFacade().getFecharMeta().executarAtualizacaoMetaAtingidaPorIndicado(fecharMetaVO, (long) getIndicacaoVO().getIndicadoVOs().size(), true);

        FecharMetaDetalhadoVO fecharMetaDetalhadoVO = getFacade().getFecharMetaDetalhado().consultarPorCodigoIndicado(getIndicadoVO().getCodigo(), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
        getFacade().getFecharMetaDetalhado().alterarSomenteCampoObteveSucesso(true, fecharMetaDetalhadoVO.getCodigo());

        setIndicacaoVO(new IndicacaoVO());
        getIndicacaoVO().setIndicadoVOs(new ArrayList<IndicadoVO>());
        setIndicadoVO(new IndicadoVO());
        setHistoricoContatoVO(new HistoricoContatoVO());
        recarregarIndicadores();
        setMostrarPanelAgendarIndicacao(false);
        setMostrarPanelObjecaoIndicacao(false);

    }


    public Date getDataInicio() {
        if (dataInicio == null) {
            dataInicio = Calendario.hoje();
        }
        return dataInicio;
    }

    public void setDataInicio(Date dataInicio) {
        this.dataInicio = dataInicio;
    }

    public Date getDataFim() {
        return dataFim;
    }

    public void setDataFim(Date dataFim) {
        this.dataFim = dataFim;
    }

    public Integer getSizeListaUsuariosSelecionados() {
        return getListaUsuariosSelecionados().size();
    }

    public String getNomeUsuarioSelecionado() {
        String nomeUsuarioSelecionado = "";
        if (getListaUsuariosSelecionados().size() == 1) {
            nomeUsuarioSelecionado = getListaUsuariosSelecionados().get(0).getColaboradorVO().getPessoa().getNome();
        }
        return nomeUsuarioSelecionado;
    }

    public List<SelectItem> getTiposFiltroData() throws Exception {
        List<SelectItem> itens = new ArrayList<SelectItem>();
        itens.add(new SelectItem(TipoFiltroData.DATA.getCodigo(), TipoFiltroData.DATA.getDescricao()));
        itens.add(new SelectItem(TipoFiltroData.INTERVALO_DE_DATA.getCodigo(), TipoFiltroData.INTERVALO_DE_DATA.getDescricao()));
        return itens;
    }

    public Integer getTipoFiltroData() {
        if (this.tipoFiltroData == null) {
            this.tipoFiltroData = 1; // filtro de data
        }
        return this.tipoFiltroData;
    }

    public void setTipoFiltroData(final Integer tipoFiltroData) {
        this.tipoFiltroData = tipoFiltroData;
    }

    public void setarDatasFiltro() throws Exception {
        if (getTipoFiltroData().equals(1)) {
            setDataFim(null);
        } else if (getTipoFiltroData().equals(2)) {
            setDataFim(Calendario.hoje());
        }
    }

    public void selecionarProximaMetaOuCliente() {

        //VERIFICAR SE EXISTE CLIENTE DA META SEM BATER META
        boolean existeCliente = false;
        for (FecharMetaDetalhadoVO clientesMeta : getTipoMetaSelecionada().getListaMetaDetalhada()) {
            if (clientesMeta.getSituacaoAtualMetaEnum().equals(SituacaoAtualMetaEnum.META_NAO_ATENDIDA)
                    || clientesMeta.getSituacaoAtualMetaEnum().equals(SituacaoAtualMetaEnum.META_NAO_ATENDIDA_CONTATOREALIZADO)) {
                existeCliente = true;
                break;
            }
        }

        if (existeCliente) {
            consultarHistoricoContatoClienteAutomatico();
        } else {
            if(getConfPaginacao().getPaginaAtual() < getConfPaginacao().getNrTotalPaginas()){
                getConfPaginacao().setPagNavegacao("pagPosterior");
                consultaPaginada();
                selecionarProximaMetaOuCliente();
            }else{
                boolean metaSelecionada = true;
                for (MetaCRMTO metaCRMTO : getListaMetaCRM()) {
                    for (TipoMetaCRMTO tipoMetaCRMTO : metaCRMTO.getListaTipoMetaVO()) {
                        Integer meta = tipoMetaCRMTO.getTotalMeta();
                        Integer realizado = tipoMetaCRMTO.getTotalMetaRealizada();
                        Integer contatoSemBater = tipoMetaCRMTO.getTotalContatoSemBaterMeta();
                        Integer realizadoMAIScontatoSemBater = realizado + contatoSemBater;

                        if (realizado >= meta) {
                            tipoMetaCRMTO.setSituacaoAtualMetaEnum(SituacaoAtualMetaEnum.META_ATENDIDA);
                        } else if ((realizadoMAIScontatoSemBater >= meta) && (contatoSemBater != 0)) {
                            tipoMetaCRMTO.setSituacaoAtualMetaEnum(SituacaoAtualMetaEnum.META_NAO_ATENDIDA_CONTATOREALIZADO);
                        } else if ((realizadoMAIScontatoSemBater < meta && metaSelecionada) && !tipoMetaCRMTO.getFasesCRMEnum().equals(FasesCRMEnum.INDICACOES) && !tipoMetaCRMTO.getFasesCRMEnum().equals(FasesCRMEnum.AGENDAMENTOS_LIGACOES) && !tipoMetaCRMTO.getFasesCRMEnum().equals(FasesCRMEnum.AGENDAMENTO)) {
                            tipoMetaCRMTO.setSituacaoAtualMetaEnum(SituacaoAtualMetaEnum.META_SELECIONADA);
                            setMetaAtual(tipoMetaCRMTO);
                            this.tipoMetaSelecionada = tipoMetaCRMTO;
                            metaSelecionada = false;
                        } else if (realizadoMAIScontatoSemBater < meta && !metaSelecionada) {
                            tipoMetaCRMTO.setSituacaoAtualMetaEnum(SituacaoAtualMetaEnum.META_NAO_ATENDIDA);
                        }
                    }
                }
                if (metaSelecionada) {
                    for (MetaCRMTO metaCRMTO : getListaMetaCRM()) {
                        for (TipoMetaCRMTO tipoMetaCRMTO : metaCRMTO.getListaTipoMetaVO()) {
                            Integer meta = tipoMetaCRMTO.getTotalMeta();
                            Integer realizado = tipoMetaCRMTO.getTotalMetaRealizada();
                            Integer contatoSemBater = tipoMetaCRMTO.getTotalContatoSemBaterMeta();
                            Integer realizadoMAIScontatoSemBater = realizado + contatoSemBater;

                            if (realizado >= meta) {
                                tipoMetaCRMTO.setSituacaoAtualMetaEnum(SituacaoAtualMetaEnum.META_ATENDIDA);
                            } else if ((realizadoMAIScontatoSemBater >= meta) && (contatoSemBater != 0)) {
                                tipoMetaCRMTO.setSituacaoAtualMetaEnum(SituacaoAtualMetaEnum.META_NAO_ATENDIDA_CONTATOREALIZADO);
                            } else if (realizadoMAIScontatoSemBater < meta && metaSelecionada) {
                                tipoMetaCRMTO.setSituacaoAtualMetaEnum(SituacaoAtualMetaEnum.META_SELECIONADA);
                                setMetaAtual(tipoMetaCRMTO);
                                this.tipoMetaSelecionada = tipoMetaCRMTO;
                                metaSelecionada = false;
                            } else if (realizadoMAIScontatoSemBater < meta && !metaSelecionada) {
                                tipoMetaCRMTO.setSituacaoAtualMetaEnum(SituacaoAtualMetaEnum.META_NAO_ATENDIDA);
                            }
                        }
                    }
                }
                setConfPaginacao(new ConfPaginacao(NUMERO_ITENS_CONSULTAR));
                consultarMetaDetalhada(this.tipoMetaSelecionada, false);
            }
        }

    }

    public boolean isMostrarPanelRealizarContato() {
        return mostrarPanelRealizarContato;
    }

    public void setMostrarPanelRealizarContato(boolean mostrarPanelRealizarContato) {
        this.mostrarPanelRealizarContato = mostrarPanelRealizarContato;
    }

    public void adicionarNovaIndicacaoMetaIndicacao() {
        try {
            setMostrarPanelAgendarIndicacao(false);
            setMostrarPanelObjecaoIndicacao(false);
            setMostrarPanelRealizarContato(false);
            setSomenteEditarIndicado(false);
            setIndicacaoVO(new IndicacaoVO());
            getIndicacaoVO().setIndicadoVOs(new ArrayList<IndicadoVO>());
            setIndicadoVO(new IndicadoVO());
            setHistoricoContatoVO(new HistoricoContatoVO());
            getHistoricoContatoVO().setFase(FasesCRMEnum.INDICACOES.getSigla());

//            List<FecharMetaDetalhadoVO> lista = getFacade().getFecharMetaDetalhado().consultarMetaDetalhada(getTipoMetaSelecionada().getListaFecharMetaVO(), false, true, 1000);
//            Ordenacao.ordenarLista(lista, "nomePessoaRel");

//            for (FecharMetaDetalhadoVO indicacoesMeta : lista) {
//                indicacoesMeta.setObteveSucesso(true);
//                indicacoesMeta.setSituacaoAtualMetaEnum(SituacaoAtualMetaEnum.META_ATENDIDA);
//            }
//            getTipoMetaSelecionada().setListaMetaDetalhada(lista);
//            getTipoMetaSelecionada().setListaMetaDetalhadaFiltrada(lista);

        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
            montarMsgAlert(getMensagemDetalhada());
            setSucesso(false);
            setErro(true);
        }
    }

    public boolean getMetaPassivo() {
        if (getTipoMetaSelecionada() != null && getTipoMetaSelecionada().getFasesCRMEnum() != null) {
            return getTipoMetaSelecionada().getFasesCRMEnum().getSigla().equals(FasesCRMEnum.PASSIVO.getSigla());
        } else {
            return false;
        }
    }

    public boolean isMetaExtra(){
        try {
            if(getTipoMetaSelecionada().getFasesCRMEnum().getCodigo()==25){
                return true;
            }
            return false;
        } catch (Exception e) {
            return false;
        }
    }

    public PassivoVO getPassivoVO() {
        if (passivoVO == null) {
            passivoVO = new PassivoVO();
        }
        return passivoVO;
    }

    public void setPassivoVO(PassivoVO passivoVO) {
        this.passivoVO = passivoVO;
    }

    public void selecionarEventoPassivo() throws Exception {
        try {
            EventoVO obj = (EventoVO) request().getAttribute("eventos");
            obj = getFacade().getEvento().consultarPorChavePrimaria(obj.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            getPassivoVO().setEvento(obj);
            setMensagemDetalhada("");
            setErro(false);
            setSucesso(true);
        } catch (Exception e) {
            montarErro(e);
        }
    }

    public void novoPassivo() {
        try {
            setSomenteEditarPassivo(false);
            setMostrarPanelAgendarPassivo(false);
            setMostrarPanelObjecaoPassivo(false);
            setMostrarPanelRealizarContato(false);
            setIndicacaoVO(new IndicacaoVO());
            getIndicacaoVO().setIndicadoVOs(new ArrayList<IndicadoVO>());
            setIndicadoVO(new IndicadoVO());
            setPassivoVO(new PassivoVO());
            setHistoricoContatoVO(new HistoricoContatoVO());
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
            montarMsgAlert(getMensagemDetalhada());
            setSucesso(false);
            setErro(true);
        }
    }

    public void excluirPassivo() {
        try {
            getFacade().getPassivo().excluir(getPassivoVO());

            for (MetaCRMTO metaCRMTO : getListaMetaCRM()) {
                for (TipoMetaCRMTO tipoMetaCRMTO : metaCRMTO.getListaTipoMetaVO()) {
                    if (tipoMetaCRMTO.getFasesCRMEnum().equals(FasesCRMEnum.PASSIVO)) {
                        tipoMetaCRMTO.setTotalMeta(tipoMetaCRMTO.getTotalMeta() - 1);
                        break;
                    }
                }
            }

            carregarListaPassivos();

            setSucesso(true);
            setErro(false);
            setMensagemID("msg_dados_excluidos");
            montarMsgAlert(getMensagem());
        } catch (Exception e) {
            setSucesso(false);
            setErro(true);
            setMensagemDetalhada("msg_erro", e.getMessage());
            montarMsgAlert(getMensagemDetalhada());
        }
    }

    public void gravarPassivo(String agendaOuObjecao) throws Exception {
//            "AG" = agendaOuObjecao
//            "OB" - objecao

        getPassivoVO().setResponsavelCadastro(getUsuarioLogado());

        //VALIDAR O CELULAR
        if (!getPassivoVO().getTelefoneCelular().isEmpty()) {
            if ((Uteis.removerMascaraTelefone(getPassivoVO().getTelefoneCelular()).length() < 11) || (!Uteis.telefoneValido(getPassivoVO().getTelefoneCelular()))) {
                throw new ConsistirException("O número celular está incorreto !");
            }
            getPassivoVO().setTelefoneCelular(Uteis.aplicarMascara(getPassivoVO().getTelefoneCelular(), "(99)999999999"));
        }

        if(usuarioSelecionado && !nomeUsuario.isEmpty()){
            getPassivoVO().setColaboradorResponsavel(getUsuario());
        } else {
            getPassivoVO().setColaboradorResponsavel(getUsuarioLogado());
        }

        getHistoricoContatoVO().setDiaAbertura(Calendario.hoje());
        getHistoricoContatoVO().setFase(FasesCRMEnum.PASSIVO.getSigla());

        if (!getUsuarioLogado().getAdministrador()) {
            getPassivoVO().setEmpresaVO(getEmpresaLogado());
        }

        getFacade().getPassivo().salvar(getPassivoVO(), getHistoricoContatoVO(), agendaOuObjecao, getEmpresaLogado().getCodigo(),
                configuracaoSistemaCRMVO, codigoAula, getTipoMetaSelecionada(), getUsuarioLogado(), getAulasAgenda());
        inicializarLog();
        for (MetaCRMTO metaCRMTO : getListaMetaCRM()) {
            for (TipoMetaCRMTO tipoMetaCRMTO : metaCRMTO.getListaTipoMetaVO()) {
                if (tipoMetaCRMTO.getFasesCRMEnum().equals(FasesCRMEnum.PASSIVO)) {
                    tipoMetaCRMTO.setTotalMeta(tipoMetaCRMTO.getTotalMeta() + 1);
                    break;
                }
            }
        }

        carregarListaPassivos();
    }

    public void gravarAgendaPassivo() {
        try {
            // Verifica se a configuração está ativa E se o tipo de agendamento é AE
            // e se não foi selecionada nenhuma aula (codigoAula está vazio).
            if (configuracaoSistemaCRMVO.isDirecionaragendamentosexperimentaisagenda()
                    && "AE".equals(agendaVO.getTipoAgendamento())
                    && UteisValidacao.emptyNumber(codigoAula)) {

                throw new ConsistirException("É necessário selecionar uma aula para este agendamento.");
            }
            passivoVO.setObjetoVOAntesAlteracao( new PassivoVO());
            if(usuarioSelecionado && !nomeUsuario.isEmpty()) {
                if(getUsuario().getAdministrador() || !verificarAberturaMetaUsuarioSelecionado()){
                    throw new ConsistirException("É necessário fazer a abertura da meta do usuário selecionado para realizar um agendamento.");
                }
                try {
                    verificaColaboradorResponsavelPorMeta();
                } catch (Exception e) {
                    setSucesso(false);
                    setErro(true);
                    throw new ConsistirException("O colaborador selecionado não possui responsabilidade configurada em \"Responsáveis pelas Fases\". " +
                            "\n É necessário configurar as fases de agendamento para este colaborador.");
                }
            } else if (getUsuarioLogado().getAdministrador() || !verificarAberturaMeta()){
                throw new ConsistirException("É necessário fazer a abertura da meta para realizar um agendamento.");
            }
            PassivoVO.validarDados(getPassivoVO(),false);

            getHistoricoContatoVO().setObjecaoVO(new ObjecaoVO());

            if (this.getAgendaVO().getHoraMinuto().isEmpty()) {
                throw new ConsistirException(("Informe o horário."));
            } else if (this.getAgendaVO().getHoraMinuto().length() != 5) {
                throw new ConsistirException(("Informe o horário corretamente."));
            }

            String[] horaMinuto = this.getAgendaVO().getHoraMinuto().split(":");
            this.getAgendaVO().setHora(horaMinuto[0]);
            this.getAgendaVO().setMinuto(horaMinuto[1]);
            validarHorario(this.getAgendaVO());
            this.getHistoricoContatoVO().setAgendaVO(this.getAgendaVO());
            gravarPassivo("AG");
            boolean atualizarTodasMetas = false;
            if (Calendario.igual(Calendario.hoje(), agendaVO.getDataAgendamento())) {
                atualizarTodasMetas = true;
            }
            String tipoContato = "";
            baterMeta(tipoContato, getMetaDetalhadoVOSelecionado(), atualizarTodasMetas);
            setSucesso(true);
            setErro(false);
            montarMsgGenerica("Receptivo", "Agendamento do receptivo gravado com sucesso.", true, null, null, "panelGridLeft, panelGridRight");
            setNenhumUsuario();
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
            setSucesso(false);
            setErro(true);
            montarMsgGenerica("Receptivo", e.getMessage(), true, null, null, "panelGridLeft, panelGridRight");
        }
    }

    public void inicializarLog() throws Exception{
        try {
            passivoVO.setNovoObj(true);
            registrarLogObjetoVO(passivoVO, passivoVO.getCodigo(), "PASSIVO", 0);
        } catch (Exception e) {
            registrarLogErroObjetoVO("PASSIVO", passivoVO.getCodigo(), "ERRO AO GERAR LOG DE ALTERAÇÃO DE RECEPTIVO ", this.getUsuarioLogado().getNome(),this.getUsuarioLogado().getUserOamd());
            e.printStackTrace();
        }
        passivoVO.registrarObjetoVOAntesDaAlteracao();
    }

    public void gravarObjecaoPassivo() {
        try {
            gravarPassivo("OB");
            setSucesso(true);
            setErro(false);
            setMensagemID("msg_app_enviado");
            montarMsgGenerica("Receptivo", "Objeção do receptivo gravada com sucesso.", true, null, null, "panelGridLeft, panelGridCenter, panelGridRight");
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
            setSucesso(false);
            setErro(true);
            montarMsgGenerica("Receptivo", e.getMessage(), true, null, null, "panelGridLeft, panelGridCenter, panelGridRight");
        }
    }

    public boolean isMostrarPanelObjecaoPassivo() {
        return mostrarPanelObjecaoPassivo;
    }

    public void setMostrarPanelObjecaoPassivo(boolean mostrarPanelObjecaoPassivo) {
        this.mostrarPanelObjecaoPassivo = mostrarPanelObjecaoPassivo;
    }

    public boolean isMostrarPanelAgendarPassivo() {
        return mostrarPanelAgendarPassivo;
    }

    public void setMostrarPanelAgendarPassivo(boolean mostrarPanelAgendarPassivo) {
        this.mostrarPanelAgendarPassivo = mostrarPanelAgendarPassivo;
    }

    public void apresentarAgendaPassivo() {
        try {
            setModalMensagemGenerica("");
            if (mostrarPanelAgendarPassivo) {
                setMostrarPanelObjecaoPassivo(false);
                setMostrarPanelAgendarPassivo(false);
            } else {

                if (getPassivoVO().getNome().equals("")) {
                    throw new ConsistirException("O campo NOME não pode ser vazio !");
                }

                if (getPassivoVO().getTelefoneResidencial().equals("") && (getPassivoVO().getTelefoneCelular().equals("")) && (getPassivoVO().getTelefoneTrabalho().equals(""))) {
                    throw new ConsistirException("Informe pelo menos um número de telefone !");
                }

                //VALIDAR O CELULAR
                if (!getPassivoVO().getTelefoneCelular().isEmpty()) {
                    if (getPassivoVO().getTelefoneCelular().length() < 12) {
                        throw new ConsistirException("O número celular está incorreto !");
                    }
                }

                //VALIDAR O TEL RESIDENCIAL
                if (!getPassivoVO().getTelefoneResidencial().isEmpty()) {
                    if (getPassivoVO().getTelefoneResidencial().length() < 12) {
                        throw new ConsistirException("O telefone residencial está incorreto !");
                    }
                }

                //VALIDAR O TEL TRABALHO
                if (!getPassivoVO().getTelefoneTrabalho().isEmpty()) {
                    if (getPassivoVO().getTelefoneTrabalho().length() < 12) {
                        throw new ConsistirException("O telefone do trabalho está incorreto !");
                    }
                }

                setMostrarPanelObjecaoPassivo(false);
                setMostrarPanelAgendarPassivo(true);
                setMensagem("");
                setMensagemDetalhada("");
            }
            setModalMensagemGenerica("");
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
            setSucesso(false);
            setErro(true);
            montarMsgGenerica("Receptivo", e.getMessage(), true, null, null, "panelGridRight");
        }
    }

    public void apresentarObjecaoPassivo() {
        try {
            setModalMensagemGenerica("");
            if (mostrarPanelObjecaoPassivo) {
                setMostrarPanelObjecaoPassivo(false);
                setMostrarPanelAgendarPassivo(false);
                getHistoricoContatoVO().setObjecaoVO(new ObjecaoVO());
            } else {

                if (getPassivoVO().getNome().equals("")) {
                    throw new ConsistirException("O campo NOME não pode ser vazio !");
                }

                if (getPassivoVO().getTelefoneResidencial().equals("") && (getPassivoVO().getTelefoneCelular().equals("")) && (getPassivoVO().getTelefoneTrabalho().equals(""))) {
                    throw new ConsistirException("Informe pelo menos um número de telefone !");
                }

                //VALIDAR O CELULAR
                if (!getPassivoVO().getTelefoneCelular().isEmpty()) {
                    if ((Uteis.removerMascaraTelefone(getPassivoVO().getTelefoneCelular()).length() < 11) || (!Uteis.telefoneValido(getPassivoVO().getTelefoneCelular()))) {
                        throw new ConsistirException("O número celular está incorreto !");
                    }
                    getPassivoVO().setTelefoneCelular(Uteis.aplicarMascara(getPassivoVO().getTelefoneCelular(), "(99)999999999"));
                }

                //VALIDAR O TEL RESIDENCIAL
                if (!getPassivoVO().getTelefoneResidencial().isEmpty()) {
                    if (getPassivoVO().getTelefoneResidencial().length() < 12) {
                        throw new ConsistirException("O telefone residencial está incorreto !");
                    }
                }

                //VALIDAR O TEL TRABALHO
                if (!getPassivoVO().getTelefoneTrabalho().isEmpty()) {
                    if (getPassivoVO().getTelefoneTrabalho().length() < 12) {
                        throw new ConsistirException("O telefone do trabalho está incorreto !");
                    }
                }
                setMostrarPanelObjecaoPassivo(true);
                setMostrarPanelAgendarPassivo(false);
                setMensagem("");
                setMensagemDetalhada("");
            }
            setModalMensagemGenerica("");
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
            setSucesso(false);
            setErro(true);
            montarMsgGenerica("Receptivo", e.getMessage(), true, null, null, "panelGridRight");
        }
    }

    private boolean verificarAberturaMeta() throws Exception {
        return getFacade().getAberturaMeta().consultarAberturaPorCodigoUsuario(getUsuarioLogado().getCodigo(), getEmpresaLogado().getCodigo(), Calendario.hoje());
    }

    private boolean verificarAberturaMetaUsuarioSelecionado() throws Exception {
        return getFacade().getAberturaMeta().consultarAberturaPorCodigoUsuario(getUsuario().getCodigo(), getEmpresaLogado().getCodigo(), Calendario.hoje());
    }

    public List<PassivoVO> getListaPassivoVO() {
        if (listaPassivoVO == null) {
            listaPassivoVO = new ArrayList<PassivoVO>();
        }
        return listaPassivoVO;
    }

    public void setListaPassivoVO(List<PassivoVO> listaPassivoVO) {
        this.listaPassivoVO = listaPassivoVO;
    }

    public void selecionarPassivo() throws Exception {
        try {
            PassivoVO passivo = (PassivoVO) context().getExternalContext().getRequestMap().get("passivo");
            PassivoVO obj = getFacade().getPassivo().consultarPorChavePrimaria(passivo.getCodigo(), Uteis.NIVELMONTARDADOS_TODOS);
            setHistoricoContatoVO(getFacade().getHistoricoContato().consultarHistoricoContatoPorCodigoPassivo(obj.getCodigo(), false, Uteis.NIVELMONTARDADOS_HISTORICOPASSIVO));
            if(!UteisValidacao.emptyString(obj.getObservacao())){
                getHistoricoContatoVO().setObservacao(obj.getObservacao());
            }
            obj.setNovoObj(false);
            setPassivoVO(obj);
            setSomenteEditarPassivo(true);
            listaPassivoFiltrada();
            for (PassivoVO passivoVO : getListaPassivoVO()) {
                if (passivoVO.getCodigo().equals(passivo.getCodigo())) {
                    passivoVO.setSelecionado(true);
                } else {
                    passivoVO.setSelecionado(false);
                }
            }
        } catch (Exception e) {
            setSucesso(true);
            setErro(false);
            setMsgAlert("");
            setMensagemID("msg_dados_editar");
        }
    }

    public void gravarAlteracoesPassivo() {
        try {
            if (!getUsuarioLogado().getAdministrador())
                getPassivoVO().setEmpresaVO(getEmpresaLogado());
            getPassivoVO().setObservacao(getHistoricoContatoVO().getObservacao());
            getFacade().getPassivo().alterarSomentePassivo(getPassivoVO());
            getFacade().getHistoricoContato().alterarSemCommit(getHistoricoContatoVO());
            setMensagemID("msg_dados_gravados");
            montarMsgAlert(getMensagem());
            setSucesso(true);
            setErro(false);
        } catch (Exception e) {
            setSucesso(false);
            setErro(true);
            setMensagemDetalhada("msg_erro", e.getMessage());
            montarMsgAlert(getMensagemDetalhada());
        }
    }

    public boolean isSomenteEditarPassivo() {
        return somenteEditarPassivo;
    }

    public void setSomenteEditarPassivo(boolean somenteEditarPassivo) {
        this.somenteEditarPassivo = somenteEditarPassivo;
    }

    public void listaPassivoFiltrada() throws Exception {
        try{
            List<PassivoVO> listaOriginal = getListaPassivoVO();
            List<PassivoVO> listaFiltrada = new ArrayList<PassivoVO>();
            if (getFiltroClientes().isEmpty()) {
                listaFiltrada = listaOriginal;
            } else {
                for (PassivoVO passivoVO : listaOriginal) {
                    if (passivoVO.getNome().toLowerCase().contains(getFiltroClientes().toLowerCase())) {
                        listaFiltrada.add(passivoVO);
                    }
                }
            }
            setListaPassivoFiltradaVO(listaFiltrada);
        } catch (Exception e) {
            montarErro(e);
        }
    }

    public boolean getMostrarInformacoesMeta() {

        try {
            if (!UtilReflection.objetoMaiorQueZero(getMetaDetalhadoVOSelecionado(),"getCodigo()")) {
                return false;
            }
            if (getMetaPassivo() || getMetaIndicacao() || getMetaIndicacaoSemContato()) {
                return false;
            } else {
                return true;
            }
        } catch (Exception ex) {
            Logger.getLogger(MetaCRMControle.class.getName()).log(Level.SEVERE, null, ex);
        }
        return false;
    }

    public void recarregarAgendamentos(Date agenda) {
        try {
            Date dia = Calendario.hoje();

            for (MetaCRMTO metaCRMTO : getListaMetaCRM()) {
                if (!metaCRMTO.getTipoFaseCRM().equals(TipoFaseCRM.VENDAS)) {
                    continue;
                }
                for (TipoMetaCRMTO tipoMetaCRMTO : metaCRMTO.getListaTipoMetaVO()) {
                    FasesCRMEnum fase = tipoMetaCRMTO.getFasesCRMEnum();
                    if (fase.equals(FasesCRMEnum.LIGACAO_AGENDADOS_AMANHA) || fase.equals(FasesCRMEnum.AGENDAMENTO)) {
                        Integer  totalMeta = tipoMetaCRMTO.getTotalMeta();

                        boolean isAmanha = Calendario.igual(Calendario.amanha(), agenda);
                        boolean isHoje = Uteis.getCompareData(agenda, dia) == 0;

                        if ((isAmanha && fase.equals(FasesCRMEnum.LIGACAO_AGENDADOS_AMANHA)) ||
                            (isHoje && fase.equals(FasesCRMEnum.AGENDAMENTO))) {
                            tipoMetaCRMTO.setTotalMeta(totalMeta+1);
                        }
                    }
                }
            }
        } catch (Exception e) {
            montarErro(e);
        }
    }

    public void recarregarIndicadores() {
        try {

            for (MetaCRMTO metaCRMTO : getListaMetaCRM()) {
                if (metaCRMTO.getTipoFaseCRM().equals(TipoFaseCRM.INDICADORES)) {
                    for (TipoMetaCRMTO tipoMetaCRMTO : metaCRMTO.getListaTipoMetaVO()) {
                        if (tipoMetaCRMTO.getFasesCRMEnum().equals(FasesCRMEnum.INDICACOES_SEM_CONTATO)) {
                            tipoMetaCRMTO.setTotalMeta(getFacade().getFecharMetaDetalhado().contarIndicadores(tipoMetaCRMTO.getListaFecharMetaVO(), false, true, false));
                        } else if (tipoMetaCRMTO.getFasesCRMEnum().equals(FasesCRMEnum.AGENDAMENTOS_LIGACOES)) {
                            List<FecharMetaDetalhadoVO> listaLigacoes = getFacade().getFecharMetaDetalhado().consultarIndicadores(tipoMetaCRMTO.getListaFecharMetaVO(), true, false, false, 1000);
                            Integer totalLigacoes = 0;
                            Integer totalRealizado = 0;

                            if (!UteisValidacao.emptyList(listaLigacoes)) {
                                totalLigacoes = listaLigacoes.size();
                                for (FecharMetaDetalhadoVO ligacao : listaLigacoes) {
                                    if (getFacade().getHistoricoContato().existeHistoricoDataExpecifica(ligacao)) {
                                        ligacao.setObteveSucesso(true);
                                        ligacao.setSituacaoAtualMetaEnum(SituacaoAtualMetaEnum.META_ATENDIDA);
                                        totalRealizado++;
                                    } else {
                                        ligacao.setObteveSucesso(false);
                                        ligacao.setSituacaoAtualMetaEnum(SituacaoAtualMetaEnum.META_NAO_ATENDIDA);
                                    }
                                }
                            }
                            tipoMetaCRMTO.setTotalMeta(totalLigacoes);
                            tipoMetaCRMTO.setTotalMetaRealizada(totalRealizado);
                        } else if (tipoMetaCRMTO.getFasesCRMEnum().equals(FasesCRMEnum.PASSIVO)) {
                            tipoMetaCRMTO.setTotalMeta(getFacade().getPassivo().contarPassivoPorPeriodoResponsavelCadastro(getListaUsuariosSelecionados(), getDataInicio(), getDataFim(), getEmpresaLogado().getCodigo()));
                        }
                    }
                }
            }

        } catch (Exception e) {
            montarErro(e);
        }
    }

    public void selecionarIndicado(FecharMetaDetalhadoVO fecharMetaDetalhadoVO) throws Exception {
        try {

            if (fecharMetaDetalhadoVO.getIndicado().getCodigo() != 0) {
                IndicacaoVO indicacaoVO = getFacade().getIndicacao().consultarPorCodigoIndicado(fecharMetaDetalhadoVO.getIndicado().getCodigo(), Uteis.NIVELMONTARDADOS_TODOS);
                IndicadoVO indicadoVO = getFacade().getIndicado().consultarPorChavePrimaria(fecharMetaDetalhadoVO.getIndicado().getCodigo(), Uteis.NIVELMONTARDADOS_TODOS);

                if (!indicacaoVO.getClienteQueIndicou().getCodigo().equals(0)) {
                    indicacaoVO.setPessoaIndicouVO(indicacaoVO.getClienteQueIndicou().getPessoa());
                } else if (!indicacaoVO.getColaboradorQueIndicou().getCodigo().equals(0)) {
                    indicacaoVO.setPessoaIndicouVO(indicacaoVO.getColaboradorQueIndicou().getPessoa());
                }

                this.getHistoricoContatoVO().setIndicadoVO(indicadoVO);
                if (indicacaoVO.getOrigemSistemaEnum() == OrigemSistemaEnum.APP_TREINO) {
                    indicacaoVO.setClienteQueIndicou(getFacade().getConviteAulaExperimentalService().consultarClienteIndicou(indicadoVO.getCodigo(), fecharMetaDetalhadoVO.getFecharMeta().getAberturaMetaVO().getColaboradorResponsavel().getColaboradorVO().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
                }
                indicacaoVO.setNovoObj(false);
                indicadoVO.setNovoObj(false);
                setSomenteEditarIndicado(true);

                if (getMetaIndicacao()) {
                    setMostrarPanelRealizarContato(true);
                } else {
                    setMostrarPanelRealizarContato(false);
                }

                setIndicacaoVO(indicacaoVO);
                getIndicacaoVO().setDiaAbertura(fecharMetaDetalhadoVO.getDia());
                setIndicadoVO(indicadoVO);

//            setHistoricoContatoVO(getFacade().getHistoricoContato().consultarHistoricoContatoPorCodigoIndicado(indicadoVO.getCodigo(), false, Uteis.NIVELMONTARDADOS_HISTORICOPASSIVO));

            } else {
                setIndicacaoVO(new IndicacaoVO());
                setIndicadoVO(new IndicadoVO());
                setMostrarPanelRealizarContato(false);
                setSomenteEditarIndicado(false);
            }

        } catch (Exception e) {
            setSucesso(true);
            setErro(false);
            setMsgAlert("");
            setMensagemID("msg_dados_editar");
        }
    }

    public boolean isMostrarPanelObjecaoIndicacao() {
        return mostrarPanelObjecaoIndicacao;
    }

    public void setMostrarPanelObjecaoIndicacao(boolean mostrarPanelObjecaoIndicacao) {
        this.mostrarPanelObjecaoIndicacao = mostrarPanelObjecaoIndicacao;
    }

    public boolean isMostrarPanelAgendarIndicacao() {
        return mostrarPanelAgendarIndicacao;
    }

    public void setMostrarPanelAgendarIndicacao(boolean mostrarPanelAgendarIndicacao) {
        this.mostrarPanelAgendarIndicacao = mostrarPanelAgendarIndicacao;
    }

    public void apresentarObjecaoIndicacao() {
        try {
            if (mostrarPanelObjecaoIndicacao) {
                setMostrarPanelObjecaoIndicacao(false);
                setMostrarPanelAgendarIndicacao(false);
                getHistoricoContatoVO().setObjecaoVO(new ObjecaoVO());
            } else {
                if (getIndicadoVO().getNomeIndicado().equals("")) {
                    throw new ConsistirException("O campo NOME INDICADO não pode ser vazio !");
                }
                if (getIndicacaoVO().getClienteQueIndicou().getCodigo() == 0 && getIndicacaoVO().getColaboradorQueIndicou().getCodigo() == 0) {
                    throw new ConsistirException("O campo CLIENTE QUE INDICOU e o campo COLABORADOR QUE INDICOU estão vazios, informe um dos campos !");
                }
                if (getIndicadoVO().getTelefone().equals("") && (getIndicadoVO().getTelefoneIndicado().equals(""))) {
                    throw new ConsistirException("Informe ao menos um número de telefone !");
                }
                setMostrarPanelObjecaoIndicacao(true);
                setMostrarPanelAgendarIndicacao(false);
            }
            setSucesso(true);
            setErro(false);
            setMensagem("");
            setMensagemDetalhada("");
            setModalMensagemGenerica("");
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
            setSucesso(false);
            setErro(true);
            montarMsgGenerica("Indicação", e.getMessage(), true, null, null, "panelGridLeft, panelGridCenter, panelGridRight");
        }
    }

    public void apresentarAgendaIndicacao() {
        try {
            if (mostrarPanelAgendarIndicacao) {
                setMostrarPanelObjecaoIndicacao(false);
                setMostrarPanelAgendarIndicacao(false);
            } else {
                if (getIndicadoVO().getNomeIndicado().equals("")) {
                    throw new ConsistirException("O campo NOME INDICADO não pode ser vazio !");
                }
                if (getIndicacaoVO().getClienteQueIndicou().getCodigo() == 0 && getIndicacaoVO().getColaboradorQueIndicou().getCodigo() == 0) {
                    throw new ConsistirException("O campo CLIENTE QUE INDICOU e o campo COLABORADOR QUE INDICOU estão vazios, informe um dos campos !");
                }
                if (getIndicadoVO().getTelefone().equals("") && (getIndicadoVO().getTelefoneIndicado().equals(""))) {
                    throw new ConsistirException("Informe ao menos um número de telefone !");
                }
                setMostrarPanelObjecaoIndicacao(false);
                setMostrarPanelAgendarIndicacao(true);
            }
            this.setAgendaVO(new AgendaVO());
            setSucesso(true);
            setErro(false);
            setMensagem("");
            setMensagemDetalhada("");
            setModalMensagemGenerica("");
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
            setSucesso(false);
            setErro(true);
            montarMsgGenerica("Indicação", e.getMessage(), true, null, null, "panelGridLeft, panelGridCenter, panelGridRight");
        }
    }

    public boolean isSomenteEditarIndicado() {
        return somenteEditarIndicado;
    }

    public void setSomenteEditarIndicado(boolean somenteEditarIndicado) {
        this.somenteEditarIndicado = somenteEditarIndicado;
    }

    public List getListaTipoContatoIndicacaoPassivo() throws Exception {
        List<SelectItem> objs = new ArrayList<SelectItem>();
        objs.add(new SelectItem("TE", "Contato Telefônico"));
        objs.add(new SelectItem("PE", "Contato Pessoal"));
        return objs;
    }

    public boolean isMetasProcessadas() {
        return metasProcessadas;
    }

    public void setMetasProcessadas(boolean metasProcessadas) {
        this.metasProcessadas = metasProcessadas;
    }

    public List<PassivoVO> getListaPassivoFiltradaVO() {
        if (listaPassivoFiltradaVO == null) {
            listaPassivoFiltradaVO = new ArrayList<PassivoVO>();
        }
        return listaPassivoFiltradaVO;
    }

    public void setListaPassivoFiltradaVO(List<PassivoVO> listaPassivoFiltradaVO) {
        this.listaPassivoFiltradaVO = listaPassivoFiltradaVO;
    }

    public Integer getTotalListaClientes() {
        return getConfPaginacao().getNumeroTotalItens();
    }

    public Integer getSizeListaPassivoFiltrada() {
        if (getListaPassivoFiltradaVO() == null) {
            return 0;
        } else {
            return getListaPassivoFiltradaVO().size();
        }
    }

//    public void selecionarTodosGrupoParticipante(ActionEvent evt) throws Exception {
//        Integer codigoGrupoParticipante = (Integer) evt.getComponent().getAttributes().get("codigoGrupoParticipante");
//
//        List<UsuarioVO> novaListaUsuario = new ArrayList<UsuarioVO>();
//        for (GrupoColaboradorVO grupoColaboradorVO : getListaGrupoColaborador()) {
//            if (!grupoColaboradorVO.getCodigo().equals(codigoGrupoParticipante)) {
//                for (GrupoColaboradorParticipanteVO participanteVO : grupoColaboradorVO.getGrupoColaboradorParticipanteVOs()) {
//                    if (participanteVO.getGrupoColaboradorParticipanteEscolhido()) {
//                        novaListaUsuario.add(participanteVO.getUsuarioParticipante());
//                    }
//                }
//            } else {
//                for (GrupoColaboradorParticipanteVO participanteVO : grupoColaboradorVO.getGrupoColaboradorParticipanteVOs()) {
//                    if (grupoColaboradorVO.getTodosParticipantesSelecionados()) {
//                        participanteVO.setGrupoColaboradorParticipanteEscolhido(true);
//                        novaListaUsuario.add(participanteVO.getUsuarioParticipante());
//                    } else {
//                        participanteVO.setGrupoColaboradorParticipanteEscolhido(false);
//                    }
//                }
//            }
//        }
//        setListaUsuariosSelecionados(novaListaUsuario);
//        setMetasProcessadas(false);
//    }

    public void executarAberturaSMSColetivo() {
        try {
            setMsgAlert("");
            limparMsg();
            MensagemGenericaControle control = (MensagemGenericaControle) getControlador(MensagemGenericaControle.class);
            control.setMensagemDetalhada("", "");
//            validarPermissao("RealizarContato", "7.23 - Realizar Contato", getUsuarioLogado());
            setMostrarPanelRealizarContato(false);
            setMostrarPanelEmailSMSColetivo(true);
            setOnComplete("");
            this.getMalaDiretaVO().setMeioDeEnvioEnum(MeioEnvio.SMS);
            String retorno = getFacade().getFecharMetaDetalhado().inicializarDadosEnviarSMSColetivoNovoCRM(getTipoMetaSelecionada(), this.getMalaDiretaVO(), getUsuarioLogado());

            StringBuilder msgModal = new StringBuilder();
            msgModal.append("");

            if (retorno.equals("OK")) {
                setPessoasSemEmailTelefone("");
                msgModal.append(retorno);
                setMsgAlert("mostrarTelaDireita();adicionarPlaceHolderCRM();");
                setOnComplete(getMsgAlert());
                setMensagemDetalhada("", "");
                setMensagem("");
                setMensagemID("msg_dados_consultados");
            } else if (retorno.startsWith("Não")) {
                setPessoasSemEmailTelefone("");
//                setMsgAlert("alert('" + retorno + "');");
                msgModal.append(retorno);
                setMsgAlert("Richfaces.showModalPanel('mdlMensagemGenerica');");
                setOnComplete(getMsgAlert());
                setMensagemDetalhada("", "");
                setMensagem("");
                setMensagemID("msg_dados_consultados");
            } else {
                setPessoasSemEmailTelefone(retorno);
                retorno = retorno.replaceFirst("<br>", " ");
                retorno = retorno.replaceAll("<br>", ", ");
                msgModal.append(retorno);
                setOnComplete("Richfaces.showModalPanel('mdlMensagemGenerica');mostrarTelaDireita();adicionarPlaceHolderCRM();");
                setMensagemDetalhada("", "");
                setMensagem("");
                setMensagemID("msg_dados_consultados");
            }

            //DESMARCAR O CLIENTE CASO NÃO TENHA EMAIL
            for (FecharMetaDetalhadoVO metaDetalhado : getTipoMetaSelecionada().getListaMetaDetalhadaFiltrada()) {
                if (metaDetalhado.getEnviarEmailSMS() && metaDetalhado.getPassivo().getCodigo() != 0) {
                    //PASSIVO ---- RECEPTIVO
                    metaDetalhado.setPassivo(getFacade().getPassivo().consultarPorChavePrimaria(metaDetalhado.getPassivo().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
                    if (!Uteis.validarTelefoneCelular(metaDetalhado.getPassivo().getTelefoneCelular())) {
                        metaDetalhado.setEnviarEmailSMS(false);
                    }
                } else if (metaDetalhado.getEnviarEmailSMS() && metaDetalhado.getIndicado().getCodigo() != 0) {
                    //INDICADO
                    metaDetalhado.setIndicado(getFacade().getIndicado().consultarPorChavePrimaria(metaDetalhado.getIndicado().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
                    if (!Uteis.validarTelefoneCelular(metaDetalhado.getIndicado().getTelefoneIndicado())) {
                        metaDetalhado.setEnviarEmailSMS(false);
                    }
                } else if (metaDetalhado.getEnviarEmailSMS() && metaDetalhado.getCliente().getCodigo() != 0) {
                    metaDetalhado.getCliente().getPessoa().setEmailVOs(getFacade().getEmail().consultarEmails(metaDetalhado.getCliente().getPessoa().getCodigo(), false, Uteis.NIVELMONTARDADOS_TODOS));
                    boolean temCelular = false;
                    for (TelefoneVO telefoneVO : metaDetalhado.getCliente().getPessoa().getTelefoneVOs()) {
                        if (telefoneVO.getTipoTelefone().equals("CE") && Uteis.validarTelefoneCelular(telefoneVO.getNumero())) {
                            temCelular = true;
                        }
                    }
                    if (!temCelular) {
                        metaDetalhado.setEnviarEmailSMS(false);
                    }
                }
            }

            //MENSAGEM GENÉRICA
            control.init("Enviar SMS", msgModal.toString(), this, "Fechar", "", "adicionarPlaceHolderCRM()");
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public void executarAberturaEmailColetivo() {
        try {
            setMsgAlert("");
            limparMsg();
            MensagemGenericaControle control = (MensagemGenericaControle) getControlador(MensagemGenericaControle.class);
            control.setMensagemDetalhada("", "");

//            validarPermissao("RealizarContato", "7.23 - Realizar Contato", getUsuarioLogado());
            setMostrarPanelRealizarContato(false);
            setMostrarPanelEmailSMSColetivo(true);
            setOnComplete("");
            this.getMalaDiretaVO().setMeioDeEnvioEnum(MeioEnvio.EMAIL);
            String retorno = getFacade().getFecharMetaDetalhado().inicializarDadosEnviarEmailColetivoNovoCRM(getTipoMetaSelecionada(), this.getMalaDiretaVO(), getUsuarioLogado());

            StringBuilder msgModal = new StringBuilder();
            msgModal.append("");

            if (retorno.equals("OK")) {
                setPessoasSemEmailTelefone("");
                setOnComplete("mostrarTelaDireita();adicionarPlaceHolderCRM();");
                setMensagemDetalhada("", "");
                setMensagem("");
                setMensagemID("msg_dados_consultados");
            } else if (retorno.startsWith("Não")) {
                setPessoasSemEmailTelefone("");
                if (getTipoMetaSelecionada().getFasesCRMEnum().equals(FasesCRMEnum.INDICACOES)) {
                    setMostrarPanelRealizarContato(false);
                    setMostrarPanelEmailSMSColetivo(false);
                    msgModal.append(retorno);
                    setMsgAlert("Richfaces.showModalPanel('mdlMensagemGenerica');mostrarTelaDireita();adicionarPlaceHolderCRM();");
                } else {
                    msgModal.append(retorno);
                    setMsgAlert("Richfaces.showModalPanel('mdlMensagemGenerica');");
                }
                setOnComplete(getMsgAlert());
                setMensagemDetalhada("", "");
                setMensagem("");
                setMensagemID("msg_dados_consultados");
            } else {
                setPessoasSemEmailTelefone(retorno);
                retorno = retorno.replaceFirst("<br>", " ");
                retorno = retorno.replaceAll("<br>", ", ");
                msgModal.append(retorno);
                setOnComplete("Richfaces.showModalPanel('mdlMensagemGenerica');mostrarTelaDireita();adicionarPlaceHolderCRM();");
                setMensagemDetalhada("", "");
                setMensagem("");
                setMensagemID("msg_dados_consultados");
            }

            //DESMARCAR O CLIENTE CASO NÃO TENHA EMAIL
            for (FecharMetaDetalhadoVO metaDetalhado : getTipoMetaSelecionada().getListaMetaDetalhadaFiltrada()) {
                if (metaDetalhado.getEnviarEmailSMS() && metaDetalhado.getPassivo().getCodigo() != 0) {
                    //PASSIVO ---- RECEPTIVO
                    metaDetalhado.setPassivo(getFacade().getPassivo().consultarPorChavePrimaria(metaDetalhado.getPassivo().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
                    if (UteisValidacao.emptyString(metaDetalhado.getPassivo().getEmail())) {
                        metaDetalhado.setEnviarEmailSMS(false);
                    }
                } else if (metaDetalhado.getEnviarEmailSMS() && metaDetalhado.getIndicado().getCodigo() != 0) {
                    //INDICADO
                    metaDetalhado.setIndicado(getFacade().getIndicado().consultarPorChavePrimaria(metaDetalhado.getIndicado().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
                    if (UteisValidacao.emptyString(metaDetalhado.getIndicado().getEmail())) {
                        metaDetalhado.setEnviarEmailSMS(false);
                    }
                } else if (metaDetalhado.getEnviarEmailSMS() && metaDetalhado.getCliente().getCodigo() != 0) {
                    metaDetalhado.getCliente().getPessoa().setEmailVOs(getFacade().getEmail().consultarEmails(metaDetalhado.getCliente().getPessoa().getCodigo(), false, Uteis.NIVELMONTARDADOS_TODOS));
                    if (UteisValidacao.emptyList(metaDetalhado.getCliente().getPessoa().getEmailVOs())) {
                        metaDetalhado.setEnviarEmailSMS(false);
                    }
                }
            }


            //MENSAGEM GENÉRICA
            control.init("Enviar Email", msgModal.toString(), this, "Fechar", "", "adicionarPlaceHolderCRM()");

        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public void enviarSMSColetivoCRM() {
        try {
            setMsgAlert("");
            if (UteisValidacao.emptyString(getEmpresaLogado().getTokenSMS())) {
                throw new ConsistirException("Empresa não tem token configurado!");
            }
            getMalaDiretaVO().setTipoAgendamento(TipoAgendamentoEnum.AGENDAMENTO_INSTANTANEO);
            validarTermosSpam();
            this.getMalaDiretaVO().setTotalPessoaMalaDiretaEnviada(0);
            this.getMalaDiretaVO().setUsuarioVO(getUsuarioLogado());
            getFacade().getMalaDireta().agendarEnvio(this.getMalaDiretaVO(), getDataInicio(), getEmpresaLogado());
            updateJenkinsService(malaDiretaVO, getConfiguracaoSistemaCRMVO());

            setSucesso(true);
            setErro(false);
            setMensagemID("msg_SMS_enviado");
            montarMsgGenerica("SMS Coletivo", "SMS foi agendado com sucesso! Dentro de alguns instantes será enviado e a meta será atualizada.", true, null, null, "panelGridLeft, panelGridCenter, panelGridRight");
        } catch (Exception e) {
            setSucesso(false);
            setErro(true);
            setMensagemID("msg_erro");
            montarMsgGenerica("SMS Coletivo", e.getMessage(), true, null, null, "panelGridLeft, panelGridCenter, panelGridRight");
        }
    }

    public void enviarEmailColetivoCRM() {
        try {
            getMalaDiretaVO().setTipoAgendamento(TipoAgendamentoEnum.AGENDAMENTO_INSTANTANEO);
            getMalaDiretaVO().setTitulo(Uteis.retirarPontosGraficos(getMalaDiretaVO().getTitulo()));
            validarTermosSpam();
            this.getMalaDiretaVO().setUsuarioVO(getUsuarioLogado());
            getFacade().getAberturaMeta().executarEmailColetivo(this.getMalaDiretaVO(), getDataInicio(), getEmpresaLogado());
            updateJenkinsService(this.getMalaDiretaVO(), getConfiguracaoSistemaCRMVO());
            String tipoContato = "SR";
            baterMeta(tipoContato, getMetaDetalhadoVOSelecionado(), true);

            setSucesso(true);
            setErro(false);
            setMensagemID("msg_email_enviado");
            montarMsgGenerica("Email Coletivo", "Email foi agendado com sucesso! Dentro de alguns instantes será enviado e a meta será atualizada.", true, null, null, "panelGridLeft, panelGridCenter, panelGridRight");
        } catch (Exception e) {
            setSucesso(false);
            setErro(true);
            setMensagemID("msg_erro");
            montarMsgGenerica("Email Coletivo", e.getMessage(), true, null, null, "panelGridLeft, panelGridCenter, panelGridRight");
        }
    }

    private void validarTermosSpam() throws Exception {
        if (getConfiguracaoSistemaCRMVO().getBloquearTermoSpam()) {
            String verificarTermosSpamNoTitulo = getMalaDiretaVO().verificarTermosSpam(getConfiguracaoSistemaCRMVO());
            if (!verificarTermosSpamNoTitulo.isEmpty()) {
                setMensagemID("msg_mail_termobloqueado");
                String msg = getMensagem() + " " + verificarTermosSpamNoTitulo + ".";
                limparMsg();
                throw new Exception(msg);
            }
        }
    }

    public String getPessoasSemEmailTelefone() {
        if (pessoasSemEmailTelefone == null) {
            pessoasSemEmailTelefone = "";
        }
        return pessoasSemEmailTelefone;
    }

    public void setPessoasSemEmailTelefone(String pessoasSemEmailTelefone) {
        this.pessoasSemEmailTelefone = pessoasSemEmailTelefone;
    }

    public boolean isMostrarPanelEmailSMSColetivo() {
        return mostrarPanelEmailSMSColetivo;
    }

    public void setMostrarPanelEmailSMSColetivo(boolean mostrarPanelEmailSMSColetivo) {
        this.mostrarPanelEmailSMSColetivo = mostrarPanelEmailSMSColetivo;
    }

    public void selecionarTodosMetaDetalhado() {
        try {
            for (FecharMetaDetalhadoVO cliente : getTipoMetaSelecionada().getListaMetaDetalhadaFiltrada()) {
                if (isMarcarTodosMetaDetalhado()) {
                    cliente.setEnviarEmailSMS(true);
                } else {
                    cliente.setEnviarEmailSMS(false);
                }
            }
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public boolean isMarcarTodosMetaDetalhado() {
        return marcarTodosMetaDetalhado;
    }

    public void setMarcarTodosMetaDetalhado(boolean marcarTodosMetaDetalhado) {
        this.marcarTodosMetaDetalhado = marcarTodosMetaDetalhado;
    }

    public List getListaModeloMensagemColetivo() throws Exception {
        List<ModeloMensagemVO> resultadoConsulta = new ArrayList<ModeloMensagemVO>();
        if (this.getMalaDiretaVO().getMeioDeEnvioEnum().equals(MeioEnvio.EMAIL)) {
            resultadoConsulta = getFacade().getModeloMensagem().consultarPorTitulo("", MeioEnvio.EMAIL, false, Uteis.NIVELMONTARDADOS_TODOS);
        } else if (this.getMalaDiretaVO().getMeioDeEnvioEnum().equals(MeioEnvio.SMS)) {
            resultadoConsulta = getFacade().getModeloMensagem().consultarPorTitulo("", MeioEnvio.SMS, false, Uteis.NIVELMONTARDADOS_TODOS);
        }
        Ordenacao.ordenarLista(resultadoConsulta, "titulo");
        List<SelectItem> listaModeloMensagem = new ArrayList<SelectItem>();
        listaModeloMensagem.add(new SelectItem(0, "Selecione o Modelo de Mensagem"));
        for (ModeloMensagemVO obj : resultadoConsulta) {
            listaModeloMensagem.add(new SelectItem(obj.getCodigo(), obj.getTitulo()));
        }
        return listaModeloMensagem;
    }

    public void selecionarModeloMensagemColetivo() throws Exception {
        try{
            if (this.getMalaDiretaVO().getModeloMensagem().getCodigo() != 0) {
                ModeloMensagemVO obj = getFacade().getModeloMensagem().consultarPorChavePrimaria(this.getMalaDiretaVO().getModeloMensagem().getCodigo(), Uteis.NIVELMONTARDADOS_TODOS);
                obj.verificarSeExisteImagemModelo(false, getKey());
                if (getMensagemDetalhada().equals("")) {
                    this.getMalaDiretaVO().setModeloMensagem(obj);
                    this.getMalaDiretaVO().setMensagem(obj.getMensagem());
                }
            } else {
                this.getMalaDiretaVO().setModeloMensagem(new ModeloMensagemVO());
                this.getMalaDiretaVO().setMensagem("");
            }
        } catch (Exception e) {
            montarErro(e);
        }
    }

    public String getAtributos() {
        if (getTipoMetaSelecionada().getFasesCRMEnum() != null) {
            if (getTipoMetaSelecionada().getFasesCRMEnum().equals(FasesCRMEnum.AGENDAMENTO)) {
                return "matricula=Matricula,nomePessoaRel=Nome,faseMeta=Fase,dataMeta=Data Meta,emails=Emails,telefones=Telefones,situacaoCliente=Situação,situacaoContrato=Situação Contrato,idadePessoa=Idade,estadoCivilPessoa=Estado Civil,tipoAgendamento=Tipo Agendamento,dataAgendamento=Data Agendamento,horaAgendamento=Hora Agendamento";
            }
        } else {
            return "matricula=Matricula,nomePessoaRel=Nome,faseMeta=Fase,dataMeta=Data Meta,emails=Emails,telefones=Telefones,situacaoCliente=Situação,situacaoContrato=Situação Contrato,idadePessoa=Idade,estadoCivilPessoa=Estado Civil";
        }
        return "matricula=Matricula,nomePessoaRel=Nome,faseMeta=Fase,dataMeta=Data Meta,emails=Emails,telefones=Telefones,situacaoCliente=Situação,situacaoContrato=Situação Contrato,idadePessoa=Idade,estadoCivilPessoa=Estado Civil";
    }

    public boolean isBuscarEmTodasFases() {
        return buscarEmTodasFases;
    }

    public void setBuscarEmTodasFases(boolean buscarEmTodasFases) {
        this.buscarEmTodasFases = buscarEmTodasFases;
    }

    public void buscarAoMarcarTodasFases() {
        if (isBuscarEmTodasFases() && !UteisValidacao.emptyString(getFiltroClientes())) {
            listaClientesFiltrada();
        } else if (!isBuscarEmTodasFases() && !UteisValidacao.emptyString(getFiltroClientes())) {
            getTipoMetaSelecionada().setListaMetaDetalhada(getListaMetaDetalhadaOriginal());
            listaClientesFiltrada();
        }
    }

    public void buscarEmTotasAsFases() {
        try {
            setOnComplete("");

            if (UteisValidacao.emptyString(getFiltroClientes())) {
                for (MetaCRMTO metaCRMTO : getListaMetaCRM()) {
                    for (TipoMetaCRMTO tipoMetaCRMTO : metaCRMTO.getListaTipoMetaVO()) {
                        if (tipoMetaCRMTO.getSituacaoAtualMetaEnum().equals(SituacaoAtualMetaEnum.META_SELECIONADA)) {
                            setConfPaginacao(new ConfPaginacao(NUMERO_ITENS_CONSULTAR));
                            consultarMetaDetalhada(tipoMetaCRMTO, false);
                            setBuscarEmTodasFases(true);
                            return;
                        }
                    }
                }
            }

            List<FecharMetaVO> listaTodasMetas = new ArrayList<FecharMetaVO>();
            for (MetaCRMTO metaCRMTO : getListaMetaCRM()) {
                for (TipoMetaCRMTO tipoMetaCRMTO : metaCRMTO.getListaTipoMetaVO()) {
                    listaTodasMetas.addAll(tipoMetaCRMTO.getListaFecharMetaVO());
                }
            }

            if (UteisValidacao.emptyList(listaTodasMetas)) {
                setBuscarEmTodasFases(false);
                throw new Exception("Nenhuma meta para buscar.");
            }

            List<FecharMetaDetalhadoVO> lista = getFacade().getFecharMetaDetalhado().consultarMetaDetalhadaTodasFases(listaTodasMetas, getFiltroClientes());

            //PREENCHER AS SITUAÇÕES DOS CLIENTES DA LISTA
            for (FecharMetaDetalhadoVO fecharMetaDetalhadoVO : lista) {
                preencherDadosPessoaisDiaMeta(fecharMetaDetalhadoVO);

                //A FORMA COMO É DEFINIDA A SITUAÇÃO DOS CLIENTE DE AGENDAMENTO DE LIGAÇÃO É DIFERENTE DAS DEMAIS.
                if (getTipoMetaSelecionada().getFasesCRMEnum().equals(FasesCRMEnum.AGENDAMENTOS_LIGACOES)) {
                    preencherSituacaoClienteAgendamentoLigacao(fecharMetaDetalhadoVO);
                } else if (!getTipoMetaSelecionada().getFasesCRMEnum().equals(FasesCRMEnum.AGENDAMENTOS_LIGACOES)) {
                    if (fecharMetaDetalhadoVO.getObteveSucesso()) {
                        fecharMetaDetalhadoVO.setSituacaoAtualMetaEnum(SituacaoAtualMetaEnum.META_ATENDIDA);

                    } else if (Uteis.getData(fecharMetaDetalhadoVO.getDataUltimoContato()).equals(Uteis.getData(fecharMetaDetalhadoVO.getFecharMeta().getDataRegistro()))) {
                        fecharMetaDetalhadoVO.setSituacaoAtualMetaEnum(SituacaoAtualMetaEnum.META_NAO_ATENDIDA_CONTATOREALIZADO);
                    } else {
                        fecharMetaDetalhadoVO.setSituacaoAtualMetaEnum(SituacaoAtualMetaEnum.META_NAO_ATENDIDA);
                    }
                }
            }

            getTipoMetaSelecionada().setListaMetaDetalhada(lista);
            getTipoMetaSelecionada().setListaMetaDetalhadaFiltrada(lista);

        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
            setMsgAlert("alert('" + e.getMessage() + "');");
            setOnComplete(getMsgAlert());
        }
    }

    public List<MarcadorVO> getListaSelectItemMarcadoEmail() throws Exception {
        List<MarcadorVO> objs = new ArrayList<MarcadorVO>();
        MarcadorVO marcador = new MarcadorVO();
        for (MarcadoresEmailEnum mEE : MarcadoresEmailEnum.values()) {
            marcador.setTag(mEE.getTag());
            marcador.setNome(mEE.getDescricao());
            objs.add(marcador);
            marcador = new MarcadorVO();
        }
        return objs;
    }

    public void executarInsercaoTag() {
        try {
            MarcadorVO marcador = (MarcadorVO) context().getExternalContext().getRequestMap().get("marcadorEmail");
            getMalaDiretaVO().setMensagem(MalaDireta.executarInsercaoTag(getMalaDiretaVO().getMensagem(), marcador.getTag(), false));
            setMensagemID("msg_dados_gravados");
            setMensagemDetalhada("");
            setMensagem("");
            setSucesso(true);
            setErro(false);
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }

    }

    public void retiraMetaDeComparecimento(FecharMetaDetalhadoVO fecharMetaDetalhadoVO) {
        try {

            for (FecharMetaDetalhadoVO clientesMeta : getTipoMetaSelecionada().getListaMetaDetalhada()) {
                if (clientesMeta.getCodigo().equals(fecharMetaDetalhadoVO.getCodigo())) {
                    clientesMeta.setObteveSucesso(false);
                    clientesMeta.setSituacaoAtualMetaEnum(SituacaoAtualMetaEnum.META_SELECIONADA);
                }
            }

            getTipoMetaSelecionada().setListaMetaDetalhadaFiltrada(getTipoMetaSelecionada().getListaMetaDetalhada());
            getTipoMetaSelecionada().setTotalMetaRealizada(getTipoMetaSelecionada().getTotalMetaRealizada() - 1);

            for (MetaCRMTO metaCRMTO : getListaMetaCRM()) {
                if (metaCRMTO.getTipoFaseCRM().equals(getTipoMetaSelecionada().getFasesCRMEnum().getTipoFase())) {
                    metaCRMTO.setTotalMetaRealizada(metaCRMTO.getTotalMetaRealizada() - 1);
                }
            }

            setListaHistoricoContatoCliente(getFacade().getHistoricoContato().consultarHistoricoContatoVO(fecharMetaDetalhadoVO, false));

        } catch (Exception e) {
            montarErro(e);
        }
    }

    public TextoPadraoVO getTextoPadraoVO() {
        if (textoPadraoVO == null) {
            textoPadraoVO = new TextoPadraoVO();
        }
        return textoPadraoVO;
    }

    public void setTextoPadraoVO(TextoPadraoVO textoPadraoVO) {
        this.textoPadraoVO = textoPadraoVO;
    }

    public void preencherTextoPadrao() {
        try {
            setMsgAlert("");
            limparMsg();
            MensagemGenericaControle control = (MensagemGenericaControle) getControlador(MensagemGenericaControle.class);
            control.setMensagemApresentar("");
            control.setMensagemDetalhada("", "");

            StringBuilder msgModal = new StringBuilder();
            msgModal.append("");
            List<SelectItem> listaTextoPadrao = getListaTextoPadrao();
            if (listaTextoPadrao.size() >= 1) {
                TextoPadraoVO textoPadraoVO = getFacade().getTextoPadrao().consultarPorChavePrimaria(Integer.parseInt(listaTextoPadrao.get(0).getValue().toString()), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                setTextoPadraoVO(textoPadraoVO);
            } else {
                setTextoPadraoVO(new TextoPadraoVO());
            }

            if (getTextoPadraoVO().getCodigo() == 0) {
                msgModal.append("Não há Script cadastrado para esta fase, para criar um novo acesse o menu \"Cadastros\".");
                setMsgAlert("Richfaces.showModalPanel('mdlMensagemGenerica');");
                setOnComplete(getMsgAlert());
            } else {
                setMsgAlert("");
                setMensagemDetalhada("", "");
                setMensagem("");
                setOnComplete("Richfaces.showModalPanel('panelTextoPadrao');Richfaces.hideModalPanel('mdlMensagemGenerica')");
            }
            control.init("Script", msgModal.toString(), this, "Fechar", "", "adicionarPlaceHolderCRM()");

        } catch (Exception ex) {
            setMensagemDetalhada(ex);
        }
    }

    private void consultarScriptAtendimentoTelaNewRealizarContato()throws Exception{
        historicoContatoVO.setObservacao("");
        FasesCRMEnum faseCRM = FasesCRMEnum.getFasePorSigla(getHistoricoContatoVO().getFase());
        String tipoContato = getHistoricoContatoVO().getTipoContato();
        this.listaTextoPadraoVO = new ArrayList<SelectItem>();
        if (faseCRM != null && !UteisValidacao.emptyString(tipoContato)){
            List<TextoPadraoVO> textoPadraoVOs = getFacade().getTextoPadrao().consultarPorFaseETipoContato(faseCRM.getCodigo(), tipoContato, getEmpresaLogado().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            if (textoPadraoVOs.size() == 1) {
                textoPadraoVOs.get(0).setMensagemPadrao(textoPadraoVOs.get(0).getMensagemPadrao().replace("<p>", ""));
                textoPadraoVOs.get(0).setMensagemPadrao(textoPadraoVOs.get(0).getMensagemPadrao().replace("</p>", "\n"));
                textoPadraoVOs.get(0).setMensagemPadrao(textoPadraoVOs.get(0).getMensagemPadrao().replace("<em>", "_"));
                textoPadraoVOs.get(0).setMensagemPadrao(textoPadraoVOs.get(0).getMensagemPadrao().replace("</em>", "_"));
                textoPadraoVOs.get(0).setMensagemPadrao(textoPadraoVOs.get(0).getMensagemPadrao().replace("<strong>", "*"));
                textoPadraoVOs.get(0).setMensagemPadrao(textoPadraoVOs.get(0).getMensagemPadrao().replace("</strong>", "*"));
                String script = Uteis.trocarAcentuacaoHTMLPorAcentuacao(textoPadraoVOs.get(0).getMensagemPadrao());
                if (script.contains("&nbsp;")) {
                    script = script.replaceAll("&nbsp;", " ");
                }
                getHistoricoContatoVO().setObservacao(script);
            }else{
                if(textoPadraoVOs.size() > 1){
                    popularListaTextoPadraoVO(textoPadraoVOs);
                    this.textoPadraoVO = textoPadraoVOs.get(0);
                }
            }
        }
    }

    private void popularListaTextoPadraoVO(List<TextoPadraoVO> listatextoPadraoVO) {
        for(TextoPadraoVO texto : listatextoPadraoVO){
            this.listaTextoPadraoVO.add(new SelectItem(texto.getCodigo(), texto.getDescricao()));
        }
    }

    public void preencherTextoPadraoDaTelaCliente() {
        notificarRecursoEmpresa(RecursoSistema.REALIZAR_CONTATO_SCRIPT);
        try {
            setMsgAlert("");
            limparMsg();
            setOnComplete("");
            consultarScriptAtendimentoTelaNewRealizarContato();
            if ((getHistoricoContatoVO().getObservacao() == null) || (getHistoricoContatoVO().getObservacao().trim().equals(""))) {
                if(listaTextoPadraoVO.size() > 0){
                    setOnComplete("Richfaces.showModalPanel('panelTextoPadraoNewRealizarContato');");
                }else{
                    StringBuilder msgModal = new StringBuilder();
                    msgModal.append("");
                    MensagemGenericaControle control = (MensagemGenericaControle) getControlador(MensagemGenericaControle.class);
                    control.setMensagemApresentar("");
                    control.setMensagemDetalhada("", "");
                    msgModal.append("Por favor, crie um texto padrão para esta fase e este tipo de contato.");
                    setMsgAlert("Richfaces.showModalPanel('mdlMensagemGenerica');");
                    setOnComplete(getMsgAlert());
                    control.init("Script", msgModal.toString(), this, "Fechar", "", "adicionarPlaceHolderCRM()");
                }
            }

        } catch (Exception ex) {
            setMensagemDetalhada(ex);
        }
    }

    public void setarMensagemTextoPadrao() throws Exception {
        TextoPadraoVO textoPadraoVO = getFacade().getTextoPadrao().consultarPorChavePrimaria(getTextoPadraoVO().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        setTextoPadraoVO(textoPadraoVO);
    }

    public List<SelectItem> getListaTextoPadrao() throws Exception {
        List<SelectItem> listaTextoPadrao = new ArrayList<SelectItem>();
        if (getTipoMetaSelecionada() != null && getTipoMetaSelecionada().getFasesCRMEnum() != null) {
            listaTextoPadrao = getFacade().getTextoPadrao().consultarPorFase(getTipoMetaSelecionada().getFasesCRMEnum().getCodigo(), getEmpresaLogado().getCodigo());
        }
        return listaTextoPadrao;
    }

    public TipoMetaCRMTO getMetaAtual() {
        return metaAtual;
    }

    public void setMetaAtual(TipoMetaCRMTO metaAtual) {
        this.metaAtual = metaAtual;
    }

    //VERIFICA SE É A META QUE DEVE SER REALIZADA. DE ACORDO COM A ORDENAÇÃO
    public boolean getMetaCorreta() {
        if (getTipoMetaSelecionada() != null && getTipoMetaSelecionada().getFasesCRMEnum() != null) {
            if (getTipoMetaSelecionada().getFasesCRMEnum().equals(FasesCRMEnum.AGENDAMENTOS_LIGACOES) || getTipoMetaSelecionada().getFasesCRMEnum().equals(FasesCRMEnum.AGENDAMENTO) || getTipoMetaSelecionada().getFasesCRMEnum().equals(FasesCRMEnum.INDICACOES) || getTipoMetaSelecionada().getFasesCRMEnum().equals(FasesCRMEnum.INDICACOES_SEM_CONTATO) || getTipoMetaSelecionada().getFasesCRMEnum().equals(FasesCRMEnum.LEADS_HOJE) || getTipoMetaSelecionada().getFasesCRMEnum().equals(FasesCRMEnum.LEADS_ACUMULADAS)) {
                return true;
            } else if (getConfiguracaoSistemaCRMVO().isObrigatorioSeguirOrdemMetas()) {
                return getTipoMetaSelecionada().equals(getMetaAtual());
            } else {
                return true;
            }
        } else {
            return false;
        }
    }

    public void gerarMetaCRMExtra() throws Exception {
        try {
            Date d1 = Calendario.hoje();

            List<MalaDiretaVO> listaCRMExtra = getFacade().getMalaDireta().consultarExisteMetaCRMExtraDiaUsuario(Calendario.hoje(), getUsuarioLogado().getCodigo());
            List<MalaDiretaVO> listaMetasGerar = new ArrayList<MalaDiretaVO>();
            for (MalaDiretaVO malaDiretaVO : listaCRMExtra) {
              getFacade().getFecharMeta().excluirMetaExtraGeradaComFalhaUDia(malaDiretaVO.getCodigo(), Calendario.hoje());
            }
            boolean existeMetaCRMExtra = false;
            if (!UteisValidacao.emptyList(listaCRMExtra)) {
                for (MalaDiretaVO malaDiretaVO : listaCRMExtra) {
                    if (!getFacade().getFecharMeta().existeMetaCRMExtraGeradaUsuarioDia(malaDiretaVO.getCodigo(), getUsuarioLogado().getCodigo(), Calendario.hoje(), getEmpresaLogado().getCodigo())) {
                        listaMetasGerar.add(malaDiretaVO);
                        existeMetaCRMExtra = true;
                    }
                }
            }

            if (existeMetaCRMExtra) {
                for (MalaDiretaVO malaDiretaVO : listaMetasGerar) {

                    List<MalaDiretaCRMExtraClienteVO> clientesMeta = new ArrayList<MalaDiretaCRMExtraClienteVO>();

                    if (malaDiretaVO.getMetaExtraIndividual()) {
                        clientesMeta = getFacade().getMalaDiretaCRMExtraCliente().consultarPorMalaDiretaVinculoColaborador(malaDiretaVO.getCodigo(),getUsuarioLogado().getColaboradorVO().getCodigo(),malaDiretaVO.getTipoConsultorMetaExtraIndividual(), Uteis.NIVELMONTARDADOS_DADOSBASICOS, getEmpresaLogado().getCodigo());
                    } else {
                        clientesMeta = getFacade().getMalaDiretaCRMExtraCliente().consultarPorMalaDireta(malaDiretaVO.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS, getEmpresaLogado().getCodigo());
                    }

                    //GERAR A LISTA DE FECHARMETADETALHADO
                    List<FecharMetaDetalhadoVO> fecharMetaDetalhadoVOList = new ArrayList<FecharMetaDetalhadoVO>();

                    Integer totalBatido = 0;
                    for (MalaDiretaCRMExtraClienteVO cliente : clientesMeta) {

                        //VERIFICAR SE O CLIENTE JÁ BATEU A META
                        boolean clienteMetaBatida = getFacade().getFecharMetaDetalhado().verificarDetalhadoMetaBatidaCRMExtra(malaDiretaVO.getCodigo(), cliente.getClienteVO().getCodigo());
                        if (clienteMetaBatida) {
                            totalBatido++;
                        }

                        FecharMetaDetalhadoVO detalhadoVO = new FecharMetaDetalhadoVO();
                        detalhadoVO.setObteveSucesso(clienteMetaBatida);
                        if(cliente.getPassivoVO().getCodigo()==null || cliente.getPassivoVO().getCodigo()==0) {
                            detalhadoVO.getCliente().setCodigo(cliente.getClienteVO().getCodigo());
                            detalhadoVO.setOrigem("CLIENTE");
                        }
                        detalhadoVO.setCodigoOrigem(cliente.getClienteVO().getCodigo());
                        if(cliente.getPassivoVO().getCodigo()!=null && cliente.getPassivoVO().getCodigo()!=0){
                            detalhadoVO.getPassivo().setCodigo(cliente.getPassivoVO().getCodigo());
                            detalhadoVO.setOrigem("AGENDA");
                        }
                        fecharMetaDetalhadoVOList.add(detalhadoVO);
                    }

                    //GERAR AS METAS PARA O USUÁRIO LOGADO
                    AberturaMetaVO aberturaMetaVO = getFacade().getAberturaMeta().consultarAberturaPorCodigoUsuario(getUsuarioLogado().getCodigo(), Calendario.hoje(), getEmpresaLogado().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);

                    FecharMetaVO metaCRM = new FecharMetaVO();
                    metaCRM.setDataRegistro(Uteis.getDataComHoraZerada(Calendario.hoje()));
                    metaCRM.setMeta((double) fecharMetaDetalhadoVOList.size());
                    metaCRM.setMetaAtingida((double) totalBatido);
                    metaCRM.setAberturaMetaVO(aberturaMetaVO);
                    metaCRM.setIdentificadorMeta(FasesCRMEnum.CRM_EXTRA.getSigla());
                    metaCRM.setRepescagem(0.0);
                    metaCRM.setMetaCalculada(true);
                    metaCRM.setFecharMetaDetalhadoVOs(fecharMetaDetalhadoVOList);
                    metaCRM.setNomeMeta(malaDiretaVO.getTitulo());
                    metaCRM.getMalaDiretaCRMExtra().setCodigo(malaDiretaVO.getCodigo());
                    metaCRM.calcularPorcentagem();
                    getFacade().getFecharMeta().incluir(metaCRM);
                }
            }
            Date d2 = Calendario.hoje();
            System.out.println("Gerar Meta CRM-Extra: " + (d2.getTime() - d1.getTime()));
        } catch (Exception e) {
            montarErro(e);
        }
    }

    public void inserirTextoPadrao() throws Exception {
        if (textoPadraoVO != null) {
            String script = Uteis.trocarAcentuacaoHTMLPorAcentuacao(textoPadraoVO.getMensagemPadrao());
            getHistoricoContatoVO().setObservacao(Jsoup.clean(script, Whitelist.none()));
            setOnComplete("Richfaces.hideModalPanel('panelTextoPadrao')");
        }
    }

    public void inserirTextoPadraoNewRealizarContato() throws Exception {
        if (textoPadraoVO != null) {
            String script = Uteis.trocarAcentuacaoHTMLPorAcentuacao(textoPadraoVO.getMensagemPadrao());
            getHistoricoContatoVO().setObservacao(Jsoup.clean(script, Whitelist.none()));
            setOnComplete("Richfaces.hideModalPanel('panelTextoPadraoNewRealizarContato')");
        }
    }

    public void validarHorario(AgendaVO agendaVO) throws ConsistirException {
        if (Integer.parseInt(agendaVO.getHora()) > 23) {
            throw new ConsistirException(("A Hora não pode ser superior a 23."));
        }
        if (Integer.parseInt(agendaVO.getMinuto()) > 59) {
            throw new ConsistirException(("Os Minutos não pode ser superior a 59."));
        }
    }

    public String getTokenSMSEmpresa() {
        if (tokenSMSEmpresa == null) {
            tokenSMSEmpresa = "";
        }
        return tokenSMSEmpresa;
    }

    public void setTokenSMSEmpresa(String tokenSMSEmpresa) {
        this.tokenSMSEmpresa = tokenSMSEmpresa;
    }

    public boolean getEmpresaSemTokem() {
        return (getTokenSMSEmpresa().isEmpty() && getTokenSMSEmpresaShortCode().isEmpty());
    }

    public String consultarMailing() {
        HistoricoContatoVO hist = (HistoricoContatoVO) context().getExternalContext().getRequestMap().get("contatos");
        String retorno = "";
        try {
            if (hist == null) {
                throw new Exception("Cliente não encontrado.");
            } else {
                MalaDiretaVO malaDiretaVO = new MalaDiretaVO();
                malaDiretaVO.setCodigo(hist.getMalaDiretaVO().getCodigo());
                MalaDiretaControle malaDiretaControle = (MalaDiretaControle) getControlador(MalaDiretaControle.class);
                request().setAttribute("malaDireta", malaDiretaVO);
                malaDiretaControle.setAbaAtual("contatoGrupo");
                retorno = malaDiretaControle.editar();
                setOnComplete("abrirPopup('mailing.jsp', 'mailing', 1000, 650);");
            }
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
        return retorno;
    }

    public void incluirTagNomeSMS() {
        this.getHistoricoContatoVO().setObservacao(this.getHistoricoContatoVO().getObservacao().concat("TAG_NOME"));
    }

    public void incluirTagPNomeSMS() {
        this.getHistoricoContatoVO().setObservacao(this.getHistoricoContatoVO().getObservacao().concat("TAG_PNOME"));
    }

    public void incluirTagNomeColetivoSMS() {
        this.getMalaDiretaVO().setMensagem(this.getMalaDiretaVO().getMensagem().concat("TAG_NOME"));
    }

    public void incluirTagPNomeColetivoSMS() {
        this.getMalaDiretaVO().setMensagem(this.getMalaDiretaVO().getMensagem().concat("TAG_PNOME"));
    }

    public String getFasesDoContato() {
        if (fasesDoContato == null) {
            fasesDoContato = "";
        }
        return fasesDoContato;
    }

    public void setFasesDoContato(String fasesDoContato) {
        this.fasesDoContato = fasesDoContato;
    }

    private void preencherFasesDoContato(FecharMetaDetalhadoVO obj) throws Exception {
        StringBuilder sb = new StringBuilder("");
        setFasesDoContato("");
        List<FecharMetaDetalhadoVO> lista = getFacade().getFecharMetaDetalhado().consultarPorClienteAberturaMeta(obj.getCliente().getCodigo(), obj.getFecharMeta().getAberturaMetaVO(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        for (FecharMetaDetalhadoVO fmd : lista) {
            if  (fmd.getAgenda().getTipoAgendamento().equals("LI")) {
                fmd.getFecharMeta().setFase(FasesCRMEnum.AGENDAMENTOS_LIGACOES);
            }
            sb.append(fmd.getFecharMeta().getFase().getDescricao()).append(", ");
        }
        if (sb.length() > 1) {
            sb.deleteCharAt(sb.length() - 1);
            sb.deleteCharAt(sb.length() - 1);
        }

        if (lista.size() > 1) {
            setFasesDoContato(sb.toString());
        }
    }

    public boolean isApresentarFasesDoContato() {
        return !getFasesDoContato().isEmpty();
    }

    public String getNomeResponsaveis() throws Exception {
        StringBuilder resp = new StringBuilder();
        try {
            if (getMetaDetalhadoVOSelecionado().getCliente().getPessoa() != null && getMetaDetalhadoVOSelecionado().getCliente().getPessoa().getCodigo() != 0) {
                PessoaVO pessoa = getMetaDetalhadoVOSelecionado().getCliente().getPessoa();
                if (!pessoa.getNomeMae().isEmpty()) {
                    if (!pessoa.getNomePai().isEmpty()) {
                        resp.append(pessoa.getNomeMae()).append(", ");
                    } else {
                        resp.append(pessoa.getNomeMae());
                    }
                }
                if (!pessoa.getNomePai().isEmpty()) {
                    resp.append(pessoa.getNomePai());
                }
            }
        } catch (Exception e) {
            montarErro(e);
        }
        return resp.toString();
    }

    public String getRotuloResponsavel() throws Exception {
        try{
            boolean pai = !getMetaDetalhadoVOSelecionado().getCliente().getPessoa().getNomePai().isEmpty();
            boolean mae = !getMetaDetalhadoVOSelecionado().getCliente().getPessoa().getNomeMae().isEmpty();
            if (pai && mae) {
                return "Responsáveis pelo Aluno";
            } else if (pai || mae) {
                return "Responsável pelo Aluno";
            }
        } catch (Exception e) {
            montarErro(e);
        }
        return "";
    }

    public Boolean getAlunoMenorIdade() {
        try{
            return Uteis.calcularIdadePessoa(Calendario.hoje(), getMetaDetalhadoVOSelecionado().getCliente().getPessoa().getDataNasc()) < 18
                && (!getMetaDetalhadoVOSelecionado().getCliente().getPessoa().getNomePai().equals("")
                || !getMetaDetalhadoVOSelecionado().getCliente().getPessoa().getNomeMae().equals(""));
        } catch (Exception e) {
            montarErro(e);
        }
        return false;
    }

    public String getModalMensagemGenerica() {
        if(modalMensagemGenerica == null){
            modalMensagemGenerica = "";
        }
        return modalMensagemGenerica;
    }

    public void setModalMensagemGenerica(String modalMensagemGenerica) {
        this.modalMensagemGenerica = modalMensagemGenerica;
    }

    public void montarMsgGenerica(String titulo, String msg, boolean msgInformacao, String botaoSim, String botaoNao, String reRender) {
        MensagemGenericaControle control = (MensagemGenericaControle) getControlador(MensagemGenericaControle.class);
        control.setMensagemDetalhada("", "");
        control.setMensagemApresentar("");
        setModalMensagemGenerica("Richfaces.showModalPanel('mdlMensagemGenerica');adicionarPlaceHolderCRM();");

        if (msgInformacao) {
            control.init(titulo, msg, this, "Fechar", "adicionarPlaceHolderCRM()", reRender);
        } else {
            control.init(titulo, msg, this, botaoSim, "adicionarPlaceHolderCRM()", botaoNao, "adicionarPlaceHolderCRM()", reRender + ",mdlMensagemGenerica");
        }
    }

    public void montarMsgGenericaPergunta(String titulo, String msgInformacao, String metodoInvocarAoClicarBotaoSim, String onCompleteBotaoSim, String metodoInvocarAoClicarBotaoNao, String onCompleteBotaoNao, String reRender) {
        MensagemGenericaControle control = (MensagemGenericaControle) getControlador(MensagemGenericaControle.class);
        control.setMensagemDetalhada("", "");
        control.setMensagemApresentar("");
        setModalMensagemGenerica("Richfaces.showModalPanel('mdlMensagemGenerica');adicionarPlaceHolderCRM();");
        control.init(titulo, msgInformacao, this, metodoInvocarAoClicarBotaoSim, onCompleteBotaoSim, metodoInvocarAoClicarBotaoNao, onCompleteBotaoNao, reRender);
    }

    public void confirmarCriarModeloMensagemEmail() {
        try {
            setMsgAlert("");
            limparMsg();
            StringBuilder msgModal = new StringBuilder();

            msgModal.append("Será criado um modelo de mensagem a partir desse email. Deseja continuar ?");

            MensagemGenericaControle control = (MensagemGenericaControle) getControlador(MensagemGenericaControle.class);
            control.setMensagemDetalhada("", "");
            setModalMensagemGenerica("Richfaces.showModalPanel('mdlMensagemGenerica');adicionarPlaceHolderCRM();");
            control.init("Utilizar como Modelo de Mensagem", msgModal.toString(), this, "criarModeloMensagemEmail", "Richfaces.showModalPanel('mdlMensagemGenerica');adicionarPlaceHolderCRM();return false", null, "adicionarPlaceHolderCRM()", "panelGridLeft, panelGridCenter, panelGridRight");
        } catch (Exception e) {
            setSucesso(false);
            setErro(true);
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public void criarModeloMensagemEmail() {
        try {
            if (UteisValidacao.emptyString(getMalaDiretaVO().getTitulo())) {
                throw new Exception("Necessário informar um título para criar o modelo de mensagem.");
            }
            ModeloMensagemVO novo = new ModeloMensagemVO();
            novo.setMeioDeEnvio(MeioEnvio.EMAIL.getCodigo());
            novo.setTitulo(getMalaDiretaVO().getTitulo());
            novo.setMensagem(getMalaDiretaVO().getMensagem());
            getFacade().getModeloMensagem().incluir(novo);
            setCodigoModeloMensagemCriado(novo.getCodigo());

            setSucesso(true);
            setErro(false);
            MensagemGenericaControle control = (MensagemGenericaControle) getControlador(MensagemGenericaControle.class);
            control.setMensagemDetalhada("", "");
            setModalMensagemGenerica("Richfaces.showModalPanel('mdlMensagemGenerica');adicionarPlaceHolderCRM();");
            control.init("Utilizar como Modelo de Mensagem", "Modelo de Mensagem gravado com sucesso.", this, "Fechar", "adicionarPlaceHolderCRM()", "panelGridLeft, panelGridCenter, panelGridRight");
        } catch (Exception e) {
            setSucesso(false);
            setErro(true);
            setMensagemID("msg_erro");
            montarMsgGenerica("Modelo Mensagem", e.getMessage(), true, null, null, "panelGridLeft, panelGridCenter, panelGridRight");
        }

    }

    public void confirmarCriarModeloMensagemSMS() {
        try {
            setMsgAlert("");
            limparMsg();
            StringBuilder msgModal = new StringBuilder();

            msgModal.append("Será criado um modelo de mensagem a partir desse SMS. Deseja continuar ?");

            MensagemGenericaControle control = (MensagemGenericaControle) getControlador(MensagemGenericaControle.class);
            control.setMensagemDetalhada("", "");
            setModalMensagemGenerica("Richfaces.showModalPanel('mdlMensagemGenerica');adicionarPlaceHolderCRM();");
            control.init("Utilizar como Modelo de Mensagem", msgModal.toString(), this, "criarModeloMensagemSMS", "Richfaces.showModalPanel('mdlMensagemGenerica');adicionarPlaceHolderCRM();return false", null, "adicionarPlaceHolderCRM()", "panelGridLeft, panelGridCenter, panelGridRight");
        } catch (Exception e) {
            setSucesso(false);
            setErro(true);
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public void criarModeloMensagemSMS() {
        try {
            ModeloMensagemVO novo = new ModeloMensagemVO();
            novo.setMeioDeEnvio(MeioEnvio.SMS.getCodigo());
            novo.setTitulo("CRIADO PELA TELA DE CONTATO");
            novo.setMensagem(getHistoricoContatoVO().getObservacao());
            getFacade().getModeloMensagem().incluir(novo);
            setCodigoModeloMensagemCriado(novo.getCodigo());

            setSucesso(true);
            setErro(false);
            MensagemGenericaControle control = (MensagemGenericaControle) getControlador(MensagemGenericaControle.class);
            control.setMensagemDetalhada("", "");
            setModalMensagemGenerica("Richfaces.showModalPanel('mdlMensagemGenerica');adicionarPlaceHolderCRM();");
            control.init("Utilizar como Modelo de Mensagem", "Modelo de Mensagem gravado com sucesso.", this, "Fechar", "adicionarPlaceHolderCRM()", "panelGridLeft, panelGridCenter, panelGridRight");
        } catch (Exception e) {
            setSucesso(false);
            setErro(true);
            setMensagemID("msg_erro");
            montarMsgGenerica("Modelo Mensagem", e.getMessage(), true, null, null, "panelGridLeft, panelGridCenter, panelGridRight");
        }
    }

    public void confirmarCriarModeloMensagemAPP() {
        try {
            setMsgAlert("");
            limparMsg();
            StringBuilder msgModal = new StringBuilder();

            msgModal.append("Será criado um modelo de mensagem a partir dessa mensagem APP. Deseja continuar ?");

            MensagemGenericaControle control = (MensagemGenericaControle) getControlador(MensagemGenericaControle.class);
            control.setMensagemDetalhada("", "");
            setModalMensagemGenerica("Richfaces.showModalPanel('mdlMensagemGenerica');adicionarPlaceHolderCRM();");
            control.init("Utilizar como Modelo de Mensagem", msgModal.toString(), this, "criarModeloMensagemAPP", "Richfaces.showModalPanel('mdlMensagemGenerica');adicionarPlaceHolderCRM();return false", null, "adicionarPlaceHolderCRM()", "panelGridLeft, panelGridCenter, panelGridRight");
        } catch (Exception e) {
            setSucesso(false);
            setErro(true);
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public void criarModeloMensagemAPP() {
        try {
            ModeloMensagemVO novo = new ModeloMensagemVO();
            novo.setMeioDeEnvio(MeioEnvio.APP.getCodigo());
            novo.setTitulo(getMalaDiretaVO().getTitulo());
            novo.setMensagem(getHistoricoContatoVO().getObservacao());
            getFacade().getModeloMensagem().incluir(novo);
            setCodigoModeloMensagemCriado(novo.getCodigo());

            setSucesso(true);
            setErro(false);
            MensagemGenericaControle control = (MensagemGenericaControle) getControlador(MensagemGenericaControle.class);
            control.setMensagemDetalhada("", "");
            setModalMensagemGenerica("Richfaces.showModalPanel('mdlMensagemGenerica');adicionarPlaceHolderCRM();");
            control.init("Utilizar como Modelo de Mensagem", "Modelo de Mensagem gravado com sucesso.", this, "Fechar", "adicionarPlaceHolderCRM()", "panelGridLeft, panelGridCenter, panelGridRight");
        } catch (Exception e) {
            setSucesso(false);
            setErro(true);
            setMensagemID("msg_erro");
            montarMsgGenerica("Modelo Mensagem", e.getMessage(), true, null, null, "panelGridLeft, panelGridCenter, panelGridRight");
        }
    }

    public Integer getCodigoModeloMensagemCriado() {
        if (codigoModeloMensagemCriado == null) {
            codigoModeloMensagemCriado = 0;
        }
        return codigoModeloMensagemCriado;
    }

    public void setCodigoModeloMensagemCriado(Integer codigoModeloMensagemCriado) {
        this.codigoModeloMensagemCriado = codigoModeloMensagemCriado;
    }

    public List<FecharMetaDetalhadoVO> getListaMetaDetalhadaOriginal() {
        if (listaMetaDetalhadaOriginal == null) {
            listaMetaDetalhadaOriginal = new ArrayList<FecharMetaDetalhadoVO>();
        }
        return listaMetaDetalhadaOriginal;
    }

    public void setListaMetaDetalhadaOriginal(List<FecharMetaDetalhadoVO> listaMetaDetalhadaOriginal) {
        this.listaMetaDetalhadaOriginal = listaMetaDetalhadaOriginal;
    }

    public List getListaModalidade() throws Exception {
        List<SelectItem> listaModalidade = new ArrayList<SelectItem>();
        try {
            List<ModalidadeVO> resultadoConsulta = new ArrayList<ModalidadeVO>();
            resultadoConsulta = getFacade().getModalidade().consultarPorNomeModalidadeSemDesativado("", getEmpresaLogado().getCodigo(), false, Uteis.NIVELMONTARDADOS_TELACONSULTA);

            listaModalidade.add(new SelectItem(0, ""));
            for (ModalidadeVO obj : resultadoConsulta) {
                listaModalidade.add(new SelectItem(obj.getCodigo(), obj.getNome()));
            }
            Ordenacao.ordenarLista(listaModalidade, "label");
        } catch (Exception e){
            montarErro(e);
        }
        return listaModalidade;
    }

    public void selecionarModalidade() {
        try {
            if (this.getAgendaVO().getModalidade().getCodigo() != 0) {
                ModalidadeVO obj = getFacade().getModalidade().consultarPorChavePrimaria(this.getAgendaVO().getModalidade().getCodigo(), Uteis.NIVELMONTARDADOS_TODOS);
                this.getHistoricoContatoVO().getAgendaVO().setModalidade(obj);
            } else {
                this.getHistoricoContatoVO().getAgendaVO().setModalidade(new ModalidadeVO());
            }
        } catch (Exception e){
            montarErro(e);
        }
    }

    public void preencherEmailTelefone(FecharMetaDetalhadoVO fecharMetaDetalhadoVO) throws Exception {
        String emails = "";
        String telefones = "";
        String urlRD = "";
        if (!fecharMetaDetalhadoVO.getCliente().getCodigo().equals(0)) {
            List<EmailVO> emailVOList = getFacade().getEmail().consultarEmails(fecharMetaDetalhadoVO.getCliente().getPessoa().getCodigo(), false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            for (EmailVO emailVO : emailVOList) {
                emails += "; " + emailVO.getEmail();
            }
            List<TelefoneVO> telefoneVOList = getFacade().getTelefone().consultarTelefones(fecharMetaDetalhadoVO.getCliente().getPessoa().getCodigo(), false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            for (TelefoneVO telefoneVO : telefoneVOList) {
                telefones += "; " + telefoneVO.getNumero();
            }
            LeadVO lead = getFacade().getLead().consultarPorCliente(fecharMetaDetalhadoVO.getCliente().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            urlRD = lead.getUrlRD();
        } else if (!fecharMetaDetalhadoVO.getPassivo().getCodigo().equals(0)) {
            PassivoVO passivoVO = getFacade().getPassivo().consultarPorChavePrimaria(fecharMetaDetalhadoVO.getPassivo().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            emails = passivoVO.getEmail();
            telefones = passivoVO.getTelefones();
            LeadVO lead = getFacade().getLead().consultarPorPassivo(fecharMetaDetalhadoVO.getPassivo().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            urlRD = lead.getUrlRD();
        } else if (!fecharMetaDetalhadoVO.getIndicado().getCodigo().equals(0)) {
            IndicadoVO indicadoVO = getFacade().getIndicado().consultarPorChavePrimaria(fecharMetaDetalhadoVO.getIndicado().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            emails = indicadoVO.getEmail();
            telefones = indicadoVO.getTelefones();
        }
        fecharMetaDetalhadoVO.setEmails(emails);
        fecharMetaDetalhadoVO.setTelefones(telefones);
        fecharMetaDetalhadoVO.setUrlRD(urlRD);
    }

    public boolean isApresentarBusca() {
        return apresentarBusca;
    }

    public void setApresentarBusca(boolean apresentarBusca) {
        this.apresentarBusca = apresentarBusca;
    }

    public void mostrarCampoBusca() {
        if (apresentarBusca) {
            setApresentarBusca(false);
        } else {
            setApresentarBusca(true);
        }
    }



    /*Metodos usados para paginação*/

    public void inicializarPaginacao() {
        listaHistoricoContatoClientePaginada = new ListaPaginadaTO(LISTA_PAGINADA_LIMIT);
    }

    private ListaPaginadaTO obterPaginacaoPorCodigo(String codigo) throws Exception {
        if (codigo.equals(LISTA_CONTATO)) {
            return listaHistoricoContatoClientePaginada;
        }
        return null;
    }

    private void carregarListaPaginacao(ListaPaginadaTO paginacao,String codigo) throws Exception {
        if (codigo.equals(LISTA_CONTATO)) {
            consultarListaContato(paginacao);
        }
    }

    public void proximaPagina(ActionEvent evt) throws Exception{
        String codigo = (String)evt.getComponent().getAttributes().get("tipo");
        ListaPaginadaTO paginacao = obterPaginacaoPorCodigo(codigo);
        paginacao.proximaPagina();
        carregarListaPaginacao(paginacao,codigo);
    }
    public void paginaAnterior(ActionEvent evt) throws Exception{
        String codigo = (String)evt.getComponent().getAttributes().get("tipo");
        ListaPaginadaTO paginacao = obterPaginacaoPorCodigo(codigo);
        paginacao.paginaAnterior();
        carregarListaPaginacao(paginacao,codigo);
    }
    public void ultimaPagina(ActionEvent evt) throws Exception{
        String codigo = (String)evt.getComponent().getAttributes().get("tipo");
        ListaPaginadaTO paginacao = obterPaginacaoPorCodigo(codigo);
        paginacao.ultimaPagina();
        carregarListaPaginacao(paginacao,codigo);
    }
    public void primeiraPagina(ActionEvent evt) throws Exception{
        String codigo = (String)evt.getComponent().getAttributes().get("tipo");
        ListaPaginadaTO paginacao = obterPaginacaoPorCodigo(codigo);
        paginacao.primeiraPagina();
        carregarListaPaginacao(paginacao,codigo);
    }

    public void atualizarNumeroItensPagina(ActionEvent evt) throws Exception {
        String codigo = (String)evt.getComponent().getAttributes().get("tipo");
        ListaPaginadaTO paginacao = obterPaginacaoPorCodigo(codigo);
        paginacao.setOffset(0);
        carregarListaPaginacao(paginacao,codigo);
    }

    private void consultarListaContato(ListaPaginadaTO paginacao) throws Exception{
        listaHistoricoContatoCliente = getFacade().getHistoricoContato().consultarHistoricoContatoCliente(getHistoricoContatoVO().getClienteVO().getCodigo().intValue(),false,Uteis.NIVELMONTARDADOS_HISTORICOCLIENTE,paginacao.getLimit(),paginacao.getOffset());
    }

    /*Fim métodos usados para paginação*/



    public void inicializarContatoAvulso() {
        try {
            montarListaPesquisa();
            validarPermissao("RealizarContato", "7.23 - Realizar Contato", getUsuarioLogado());
            novoHistoricoContato();
            setTokenSMSEmpresa(getFacade().getEmpresa().obterTokenSMS(getEmpresaLogado().getCodigo()));
            setTokenSMSEmpresaShortCode(getFacade().getEmpresa().obterTokenSMSShortCode(getEmpresaLogado().getCodigo()));
            ClienteVO cliente = null;
            ClienteVO objCliente = (ClienteVO) context().getExternalContext().getRequestMap().get("cliente");
            ClienteControle clienteControle = (ClienteControle) context().getExternalContext().getSessionMap().get(ClienteControle.class.getSimpleName());
            if(objCliente == null){
                if (clienteControle == null) {
                    setSucesso(false);
                    setErro(true);
                    setMensagemDetalhada("msg_erro", "Cliente Não Inicializado.");
                    return;
                }
                clienteControle.pegarClienteTelaCliente();
                cliente = clienteControle.getClienteVO();
            }else{
                cliente = objCliente;
                if (clienteControle != null) {
                    clienteControle.setClienteVO(cliente);
                }
            }

            if (!UteisValidacao.emptyNumber(cliente.getObjecao().getCodigo())) {
                cliente.setObjecao(getFacade().getObjecao().consultarPorChavePrimaria(cliente.getObjecao().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
                setApresentarModalObjecaoDefinitiva(true);
            }

            FecharMetaDetalhadoVO fecharMetaDetalhadoVO = new FecharMetaDetalhadoVO();
            fecharMetaDetalhadoVO.setCliente(cliente);
            setMetaDetalhadoVOSelecionado(fecharMetaDetalhadoVO);
            this.getHistoricoContatoVO().setClienteVO(cliente);

            if (this.getHistoricoContatoVO().getClienteVO().getCodigo() != 0) {
                inicializarPaginacao();
                getFacade().getHistoricoContato().preencherHistoricoContato(this.getHistoricoContatoVO());

                setListaHistoricoContatoCliente(new ArrayList<HistoricoContatoVO>());
                setListaHistoricoObjecoesCliente(new ArrayList<HistoricoContatoVO>());
                setListaHistoricoContatoCliente(getFacade().getHistoricoContato().consultarHistoricoContatoVO(fecharMetaDetalhadoVO,
                        false, listaHistoricoContatoClientePaginada.getLimit(), listaHistoricoContatoClientePaginada.getOffset()));
                listaHistoricoContatoClientePaginada.setCount(getFacade().getHistoricoContato().countHistoricoContatoVO(fecharMetaDetalhadoVO, false));

                setListaHistoricoContatoClienteSemLimite(getFacade().getHistoricoContato().consultarHistoricoContatoVO(fecharMetaDetalhadoVO, false));

                if (!getListaHistoricoContatoCliente().isEmpty()) {
                    setListaHistoricoObjecoesCliente(new ArrayList<HistoricoContatoVO>());
                    for (HistoricoContatoVO obj : getListaHistoricoContatoCliente()) {
                        if (obj.getObjecaoVO() != null && obj.getObjecaoVO().getCodigo() != 0) {
                            getListaHistoricoObjecoesCliente().add(obj);
                        }
                    }
                }

                this.getHistoricoContatoVO().getClienteVO().setSituacaoClienteSinteticoVO(getFacade().getSituacaoClienteSinteticoDW().consultarCliente(this.getHistoricoContatoVO().getClienteVO().getCodigo(), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA));
                this.getHistoricoContatoVO().getClienteVO().getPessoa().setTelefoneVOs(getFacade().getTelefone().consultarTelefones(this.getHistoricoContatoVO().getClienteVO().getPessoa().getCodigo(), false, Uteis.NIVELMONTARDADOS_DADOSBASICOS));
                this.getHistoricoContatoVO().getClienteVO().getPessoa().setEmailVOs(getFacade().getEmail().consultarEmails(this.getHistoricoContatoVO().getClienteVO().getPessoa().getCodigo(), false, Uteis.NIVELMONTARDADOS_DADOSBASICOS));
                setListaEmail(this.getHistoricoContatoVO().getClienteVO().getPessoa().getEmailVOs());
                consultarListaTelefones();
            }

            this.getHistoricoContatoVO().setContatoAvulso(true);
            this.getHistoricoContatoVO().setTipoContato("TE");
            this.getHistoricoContatoVO().setObservacao("");
            this.getHistoricoContatoVO().setNovoObj(true);
//            this.getHistoricoContatoVO().setAberturaMetaEstaEmAberta(getMetaDetalhadoVOSelecionado().getMetaEmAberto());
            this.getHistoricoContatoVO().setDiaAbertura(Calendario.hoje());
            this.getHistoricoContatoVO().setDia(Calendario.hoje());
            this.getHistoricoContatoVO().setResponsavelCadastro(getUsuarioLogado());
            this.getHistoricoContatoVO().setColaboradorResponsavel(getUsuarioLogado());
//            this.getHistoricoContatoVO().setFase(getTipoMetaSelecionada().getFasesCRMEnum().getSigla());
            setMostrarPanelRealizarContato(true);
        } catch (Exception e) {
            setMensagemDetalhada(e);
            montarMsgAlert(getMensagemDetalhada());
        }
    }

    public void recarregarContatoAvulso() throws Exception {
        novoHistoricoContato();
        ClienteControle clienteControle = (ClienteControle) context().getExternalContext().getSessionMap().get(ClienteControle.class.getSimpleName());
        if (clienteControle == null) {
            setSucesso(false);
            setErro(true);
            setMensagemDetalhada("msg_erro", "Cliente Não Inicializado.");
            return;
        }
        ClienteVO cliente = clienteControle.getClienteVO();

        FecharMetaDetalhadoVO fecharMetaDetalhadoVO = new FecharMetaDetalhadoVO();
        fecharMetaDetalhadoVO.setCliente(cliente);
        setMetaDetalhadoVOSelecionado(fecharMetaDetalhadoVO);
        this.getHistoricoContatoVO().setClienteVO(cliente);

        getFacade().getHistoricoContato().preencherHistoricoContato(this.getHistoricoContatoVO());
        setListaHistoricoContatoCliente(new ArrayList<HistoricoContatoVO>());
        setListaHistoricoObjecoesCliente(new ArrayList<HistoricoContatoVO>());
        setListaHistoricoContatoCliente(getFacade().getHistoricoContato().consultarHistoricoContatoVO(fecharMetaDetalhadoVO, false));
        if (!getListaHistoricoContatoCliente().isEmpty()) {
            setListaHistoricoObjecoesCliente(new ArrayList<HistoricoContatoVO>());
            for (HistoricoContatoVO obj : getListaHistoricoContatoCliente()) {
                if (obj.getObjecaoVO() != null && obj.getObjecaoVO().getCodigo() != 0) {
                    getListaHistoricoObjecoesCliente().add(obj);
                }
            }
        }

        this.getHistoricoContatoVO().setContatoAvulso(true);
        this.getHistoricoContatoVO().setTipoContato("TE");
        this.getHistoricoContatoVO().setObservacao("");
        this.getHistoricoContatoVO().setNovoObj(true);
        this.getHistoricoContatoVO().setDiaAbertura(Calendario.hoje());
        this.getHistoricoContatoVO().setDia(Calendario.hoje());
        this.getHistoricoContatoVO().setResponsavelCadastro(getUsuarioLogado());
        this.getHistoricoContatoVO().setColaboradorResponsavel(getUsuarioLogado());
        setMostrarPanelRealizarContato(true);

        this.getHistoricoContatoVO().getClienteVO().setSituacaoClienteSinteticoVO(getFacade().getSituacaoClienteSinteticoDW().consultarCliente(this.getHistoricoContatoVO().getClienteVO().getCodigo(), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA));
        this.getHistoricoContatoVO().getClienteVO().getPessoa().setTelefoneVOs(getFacade().getTelefone().consultarTelefones(this.getHistoricoContatoVO().getClienteVO().getPessoa().getCodigo(), false, Uteis.NIVELMONTARDADOS_DADOSBASICOS));
        this.getHistoricoContatoVO().getClienteVO().getPessoa().setEmailVOs(getFacade().getEmail().consultarEmails(this.getHistoricoContatoVO().getClienteVO().getPessoa().getCodigo(), false, Uteis.NIVELMONTARDADOS_DADOSBASICOS));
        setListaEmail(this.getHistoricoContatoVO().getClienteVO().getPessoa().getEmailVOs());
        consultarListaTelefones();
    }

    public List<SelectItem> getListaSelectItemFaseAtual() throws Exception {
        List<SelectItem> objs = new ArrayList<SelectItem>();
        FasesCRMEnum[] listaFases = FasesCRMEnum.values();
        for (FasesCRMEnum fase : listaFases) {
            String value = fase.getSigla();
            String label = fase.getDescricao();
            String objetivo = fase.getObjetivo();
            if (!objetivo.isEmpty()) {
                objs.add(new SelectItem(value, label));
            }
        }
        Ordenacao.ordenarLista(objs, "label");
        return objs;
    }

    public boolean isApresentarPanelEnviarEmail() {
        return apresentarPanelEnviarEmail;
    }

    public void setApresentarPanelEnviarEmail(boolean apresentarPanelEnviarEmail) {
        this.apresentarPanelEnviarEmail = apresentarPanelEnviarEmail;
    }

    public void mostrarPanelEnviarEmail() {
        if (apresentarPanelEnviarEmail) {
            setApresentarPanelEnviarEmail(false);
        } else {
            setApresentarPanelEnviarEmail(true);
        }
    }

    public void criarNovoEmail() {
        setMostrarModeloDeMensagem(false);
        this.getMalaDiretaVO().setModeloMensagem(new ModeloMensagemVO());
        this.getMalaDiretaVO().setMensagem("");
        this.getMalaDiretaVO().setTitulo("");
        setApresentarPanelEnviarEmail(true);
    }

    public void apresentarModeloMensagemEmail() {
        if (mostrarModeloDeMensagem) {
            setApresentarPanelEnviarEmail(false);
            setMostrarModeloDeMensagem(false);
            this.getMalaDiretaVO().setModeloMensagem(new ModeloMensagemVO());
            this.getMalaDiretaVO().setMensagem("");
            this.getHistoricoContatoVO().setObservacao("");
        } else {
            setApresentarPanelEnviarEmail(false);
            setMostrarModeloDeMensagem(true);
        }
    }

    public void selecionarModeloMensagemEmail() throws Exception {
        try{
            if (!this.getMalaDiretaVO().getModeloMensagem().getCodigo().equals(0)) {
                ModeloMensagemVO obj = getFacade().getModeloMensagem().consultarPorChavePrimaria(this.getMalaDiretaVO().getModeloMensagem().getCodigo(), Uteis.NIVELMONTARDADOS_TODOS);
                obj.verificarSeExisteImagemModelo(false, getKey());
                if (getMensagemDetalhada().equals("")) {
                    this.getMalaDiretaVO().setModeloMensagem(obj);
                    this.getMalaDiretaVO().setMensagem(this.getMalaDiretaVO().getModeloMensagem().getMensagem());
                    this.getMalaDiretaVO().setTitulo(this.getMalaDiretaVO().getModeloMensagem().getTitulo());
                    setApresentarPanelEnviarEmail(true);
                }

            } else {
                this.getMalaDiretaVO().setModeloMensagem(new ModeloMensagemVO());
                this.getMalaDiretaVO().setMensagem("");
                setApresentarPanelEnviarEmail(false);
            }
        } catch (Exception e) {
            montarErro(e);
        }
    }

    public List<TelefoneVO> getListaTelefones() {
        if (listaTelefones == null) {
            listaTelefones = new ArrayList<TelefoneVO>();
        }
        return listaTelefones;
    }

    public void setListaTelefones(List<TelefoneVO> listaTelefones) {
        this.listaTelefones = listaTelefones;
    }

    public List getListaTelefoneClientePorTipoContato() {
        //no futuro, tratar os outros tipos, caso necessite, por enquanto apenas tipo
        //contato por SMS: apresentar apenas telefones celulares
        ArrayList listaTemp = new ArrayList();
        try{
            if (this.getHistoricoContatoVO().getTipoContato().endsWith("CS")) {
                for (TelefoneVO tel : getListaTelefones()) {
                    if (tel.getTipoTelefone().equals("CE")) {
                        listaTemp.add(tel);
                    }
                }
            } else {
                return getListaTelefones();
            }
        } catch (Exception e) {
            montarErro(e);
        }
        return listaTemp;
    }

    public String getTotalUsuariosSelecionados() {
        if (totalUsuariosSelecionados == null) {
            totalUsuariosSelecionados = "0";
        }
        return totalUsuariosSelecionados;
    }

    public void setTotalUsuariosSelecionados(String totalUsuariosSelecionados) {
        this.totalUsuariosSelecionados = totalUsuariosSelecionados;
    }

    public List<PessoaVO> executarAutocompleteConsultaPessoaQueIndicouIndicao(Object suggest) {
        String pref = (String) suggest;
        List<PessoaVO> result = new ArrayList<PessoaVO>();
        try {
            if (UteisValidacao.emptyNumber(getEmpresaLogado().getCodigo())) {
                throw new Exception("Informe a empresa primeiro");
            }
            if (pref.equals("%")) {
                result = getFacade().getPessoa().consultarTodosPessoaComLimiteCRM(getEmpresaLogado().getCodigo(), null, false);
            } else {
                result = getFacade().getPessoa().consultarTodosPessoaComLimiteCRM(getEmpresaLogado().getCodigo(), pref, false);
            }
            setMensagemID("msg_dados_consultados");
            setSucesso(true);
            setErro(false);
        } catch (Exception ex) {
            result = (new ArrayList<PessoaVO>());
            setMensagemDetalhada("msg_erro", ex.getMessage());
        }
        return result;
    }

    public void selecionarPessoaSuggestionBoxIndicacao() throws Exception {
        PessoaVO pessoaVO = (PessoaVO) request().getAttribute("clienteColaborador");
        try {
            if (pessoaVO != null) {
                if (pessoaVO.getTipoPessoa().equals("Cliente")) {
                    ClienteVO clienteVO = getFacade().getCliente().consultarPorCodigoPessoa(pessoaVO.getCodigo(), getEmpresaLogado().getCodigo(), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
                    getIndicacaoVO().setPessoaIndicouVO(pessoaVO);
                    getIndicacaoVO().setClienteQueIndicou(clienteVO);
                    getIndicacaoVO().setColaboradorQueIndicou(new ColaboradorVO());
                } else if (pessoaVO.getTipoPessoa().equals("Colaborador")) {
                    ColaboradorVO colaboradorVO = getFacade().getColaborador().consultarPorCodigoPessoa(pessoaVO.getCodigo(), getEmpresaLogado().getCodigo(), Uteis.NIVELMONTARDADOS_CONSULTA_WS);
                    getIndicacaoVO().setPessoaIndicouVO(pessoaVO);
                    getIndicacaoVO().setColaboradorQueIndicou(colaboradorVO);
                    getIndicacaoVO().setClienteQueIndicou(new ClienteVO());
                }
            }
        } catch (Exception ex) {
            montarErro(ex);
        }
    }

    public List<SelectItem> getListaTextoPadraoVO() {
        return listaTextoPadraoVO;
    }

    public void setListaTextoPadraoVO(List<SelectItem> listaTextoPadraoVO) {
        this.listaTextoPadraoVO = listaTextoPadraoVO;
    }

    public boolean isApresentarModalObjecaoDefinitiva() {
        return apresentarModalObjecaoDefinitiva;
    }

    public void setApresentarModalObjecaoDefinitiva(boolean apresentarModalObjecaoDefinitiva) {
        this.apresentarModalObjecaoDefinitiva = apresentarModalObjecaoDefinitiva;
    }

    public void montarListaUsuariosCRM() {
        try {
            setListaGrupoTipoColaborador(new ArrayList<GrupoTipoColaboradorVO>());
            List<UsuarioVO> usuarios = getFacade().getUsuario().consultarListaUsuariosCRM(getEmpresaLogado().getCodigo(), !getConfiguracaoSistemaCRMVO().isApresentarColaboradoresInativos(), false);
            Map<TipoColaboradorEnum, List<UsuarioVO>> map = new HashMap<TipoColaboradorEnum, List<UsuarioVO>>();
            if(permissao("PermitirVisualizarTodasCarteiras")){
                for (UsuarioVO usuarioVO : usuarios) {
                    if (!map.containsKey(usuarioVO.getTipoColaboradorEnum())) {
                        List<UsuarioVO> lista = new ArrayList<UsuarioVO>();
                        lista.add(usuarioVO);
                        map.put(usuarioVO.getTipoColaboradorEnum(), lista);
                    } else {
                        List<UsuarioVO> lista = map.get(usuarioVO.getTipoColaboradorEnum());
                        boolean contem = false;
                        for (UsuarioVO usu : lista) {
                            if (usu.getCodigo().equals(usuarioVO.getCodigo())) {
                                contem = true;
                            }
                        }
                        if (!contem) {
                            lista.add(usuarioVO);
                        }
                        map.put(usuarioVO.getTipoColaboradorEnum(), lista);
                    }
                }
            }else{
                for (UsuarioVO usuarioVO : usuarios) {
                    if (usuarioVO.getCodigo().equals(getUsuarioLogado().getCodigo())) {
                        if (!map.containsKey(usuarioVO.getTipoColaboradorEnum())) {
                            List<UsuarioVO> lista = new ArrayList<UsuarioVO>();
                            lista.add(usuarioVO);
                            map.put(usuarioVO.getTipoColaboradorEnum(), lista);
                        } else {
                            List<UsuarioVO> lista = map.get(usuarioVO.getTipoColaboradorEnum());
                            boolean contem = false;
                            for (UsuarioVO usu : lista) {
                                if (usu.getCodigo().equals(usuarioVO.getCodigo())) {
                                    contem = true;
                                }
                            }
                            if (!contem) {
                                lista.add(usuarioVO);
                            }
                            map.put(usuarioVO.getTipoColaboradorEnum(), lista);
                        }
                    }
                }
            }

            List<GrupoTipoColaboradorVO> listaGrupos = new ArrayList<GrupoTipoColaboradorVO>();
            for (Map.Entry<TipoColaboradorEnum, List<UsuarioVO>> mapa : map.entrySet()) {
                GrupoTipoColaboradorVO grupo = new GrupoTipoColaboradorVO();
                grupo.setTipoColaborador(mapa.getKey());
                grupo.setUsuarios(mapa.getValue());
                listaGrupos.add(grupo);
            }
            Ordenacao.ordenarLista(listaGrupos, "tipoColaborador_Apresentar");
            setListaGrupoTipoColaborador(listaGrupos);
        } catch (Exception e) {
            setListaGrupoTipoColaborador(new ArrayList<GrupoTipoColaboradorVO>());
        }
    }

    public void adicionarUsuariosCRMListaSelecionados(boolean selecionarUsuarioLogado) throws Exception {
        List<UsuarioVO> novaListaUsuario = new ArrayList<UsuarioVO>();
        try{
            for (GrupoTipoColaboradorVO grupoTipoColaboradorVO : getListaGrupoTipoColaborador()) {
                for (UsuarioVO usuarioVO : grupoTipoColaboradorVO.getUsuarios()) {
                    if (usuarioVO.isSelecionado() || (selecionarUsuarioLogado && usuarioVO.getCodigo().equals(getUsuarioLogado().getCodigo()))) {
                        usuarioVO.setSelecionado(true);
                        novaListaUsuario.add(usuarioVO);
                    }
                }
            }
        } catch (Exception e) {
            montarErro(e);

        }
        setListaUsuariosSelecionados(novaListaUsuario);
        Integer total = getListaUsuariosSelecionados().size();
        setTotalUsuariosSelecionados(total.toString());
    }

    public List<GrupoTipoColaboradorVO> getListaGrupoTipoColaborador() {
        if (listaGrupoTipoColaborador == null) {
            listaGrupoTipoColaborador = new ArrayList<GrupoTipoColaboradorVO>();
        }
        return listaGrupoTipoColaborador;
    }

    public void setListaGrupoTipoColaborador(List<GrupoTipoColaboradorVO> listaGrupoTipoColaborador) {
        this.listaGrupoTipoColaborador = listaGrupoTipoColaborador;
    }

    public boolean isSomenteAtivos() {
        return somenteAtivos;
    }

    public void setSomenteAtivos(boolean somenteAtivos) {
        this.somenteAtivos = somenteAtivos;
    }

    public void selecionarTodosGrupoParticipante(ActionEvent evt) throws Exception {
        Integer codigoGrupoParticipante = (Integer) evt.getComponent().getAttributes().get("codigoGrupoParticipante");

        List<UsuarioVO> novaListaUsuario = new ArrayList<UsuarioVO>();
        for (GrupoColaboradorVO grupoColaboradorVO : getListaGrupoColaborador()) {
            if (!grupoColaboradorVO.getCodigo().equals(codigoGrupoParticipante)) {
                for (GrupoColaboradorParticipanteVO participanteVO : grupoColaboradorVO.getGrupoColaboradorParticipanteVOs()) {
                    if (participanteVO.getGrupoColaboradorParticipanteEscolhido()) {
                        novaListaUsuario.add(participanteVO.getUsuarioParticipante());
                    }
                }
            } else {
                for (GrupoColaboradorParticipanteVO participanteVO : grupoColaboradorVO.getGrupoColaboradorParticipanteVOs()) {
                    if (grupoColaboradorVO.getTodosParticipantesSelecionados()) {
                        participanteVO.setGrupoColaboradorParticipanteEscolhido(true);
                        novaListaUsuario.add(participanteVO.getUsuarioParticipante());
                    } else {
                        participanteVO.setGrupoColaboradorParticipanteEscolhido(false);
                    }
                }
            }
        }
        setListaUsuariosSelecionados(novaListaUsuario);
    }

    public ConfiguracaoSistemaCRMVO getConfiguracaoSistemaCRMVO() {
        return configuracaoSistemaCRMVO;
    }

    public void setConfiguracaoSistemaCRMVO(ConfiguracaoSistemaCRMVO configuracaoSistemaCRMVO) {
        this.configuracaoSistemaCRMVO = configuracaoSistemaCRMVO;
    }

    public void selecionarTelefoneSMS()  {
        try{
            TelefoneVO obj = (TelefoneVO) request().getAttribute("telefone");
            for (Object object : this.getHistoricoContatoVO().getListaTelefoneClientePorTipoContato()) {
                TelefoneVO telefoneVO = (TelefoneVO) object;
                if (telefoneVO.getCodigo().equals(obj.getCodigo()) && obj.getSelecionado()) {
                    telefoneVO.setSelecionado(true);
                } else {
                    telefoneVO.setSelecionado(false);
                }
            }
        } catch (Exception e){
            montarErro(e);
        }
    }

    public String getModeloMensagemWhatsAppEncode() {
        try {

            return URLEncoder.encode(txtWpp, "UTF-8").replaceAll("\\+", "%20");
        } catch (Exception e) {
            return "";
        }
    }

    public String getUrlWpp() {
        if(urlWpp == null){
            return "";
        }
        return urlWpp;
    }

    public void setUrlWpp(String urlWpp) {
        this.urlWpp = urlWpp;
    }

    public void selecionarMetasUsuario(UsuarioVO usuarioVO){
        if (usuarioVO != null && usuarioVO.getCodigo() != null && usuarioVO.getCodigo() > 0) {
            for (GrupoColaboradorVO grupo:getListaGrupoColaborador()) {
                for(GrupoColaboradorParticipanteVO grupoColaborador : grupo.getGrupoColaboradorParticipanteVOs()) {
                    if (usuarioVO.getCodigo().equals(grupoColaborador.getUsuarioParticipante().getCodigo())) {
                        grupoColaborador.setGrupoColaboradorParticipanteEscolhido(Boolean.TRUE);
                    }

                }
            }
        }
    }

    public void setTipoMetaConsultadaAnteriormente(TipoMetaCRMTO tipoMetaConsultadaAnteriormente) {
        this.tipoMetaConsultadaAnteriormente = tipoMetaConsultadaAnteriormente;
    }

    public TipoMetaCRMTO getTipoMetaConsultadaAnteriormente() {
        return tipoMetaConsultadaAnteriormente;
    }

    public void montarListaPesquisa() {
        try {
            setPesquisa(0);
            setListaPesquisa(new ArrayList<QuestionarioVO>());
            setListaPesquisa(getFacade().getQuestionario().consultarQuestionariosPorTipo(TipoServicoEnum.PESQUISA, true, Uteis.NIVELMONTARDADOS_DADOSBASICOS));
        } catch (Exception ex) {
            setListaPesquisa(new ArrayList<QuestionarioVO>());
        }
    }

    public List<QuestionarioVO> getListaPesquisa() {
        if (listaPesquisa == null) {
            listaPesquisa = new ArrayList<QuestionarioVO>();
        }
        return listaPesquisa;
    }

    public void setListaPesquisa(List<QuestionarioVO> listaPesquisa) {
        this.listaPesquisa = listaPesquisa;
    }

    public List<SelectItem> getListaSelectItemPesquisa() {
        List<SelectItem> listaPesquisa = new ArrayList<SelectItem>();
        listaPesquisa.add(new SelectItem(0, "Selecione uma pesquisa"));
        for (QuestionarioVO questionarioVO : getListaPesquisa()) {
            listaPesquisa.add(new SelectItem(questionarioVO.getCodigo(), questionarioVO.getNomeInterno()));
        }
        return listaPesquisa;
    }

    public boolean isExistePesquisa() {
        try {
            return getListaPesquisa().size() > 0 && getMetaDetalhadoVOSelecionado() != null && !UteisValidacao.emptyNumber(getMetaDetalhadoVOSelecionado().getCliente().getCodigo());
        } catch (Exception ex) {
            return false;
        }
    }

    private String obterMensagemComURLPesquisa(String mensagem) throws Exception {
        MailingService mailingService = new MailingService(getFacade().getCliente().getCon());
        Integer questionario = getPesquisa();
        Integer cliente = getMetaDetalhadoVOSelecionado().getCliente().getCodigo();
        Integer colaborador = getUsuarioLogado().getColaboradorVO().getCodigo();
        return mailingService.resolverTagPesquisa(getKey(), mensagem, colaborador, questionario, 0, cliente,false);
    }

    public Integer getPesquisa() {
        if (pesquisa == null) {
            pesquisa = 0;
        }
        return pesquisa;
    }

    public void setPesquisa(Integer pesquisa) {
        this.pesquisa = pesquisa;
    }

    public void adicionarLinkPesquisa() {
        try {
            setOnComplete("");
            limparMsg();
            if (!UteisValidacao.emptyNumber(getPesquisa())) {
                String tagPesquisa = "\nTAG_PESQUISA";
                if (getHistoricoContatoVO().getTipoContato().equals(TipoContatoCRM.CONTATO_EMAIL.getSigla())) {
                    String[] mensagem = getMalaDiretaVO().getMensagem().split("</body>");
                    String mensagemOriginal = "";
                    if (mensagem.length > 1) {
                        mensagemOriginal = mensagem[0].trim() + tagPesquisa + "</body>" + mensagem[1];
                    } else {
                        mensagemOriginal = getMalaDiretaVO().getMensagem() + tagPesquisa;
                    }

                    getMalaDiretaVO().setMensagem(obterMensagemComURLPesquisa(mensagemOriginal));
                } else {
                    getHistoricoContatoVO().setObservacao(obterMensagemComURLPesquisa(getHistoricoContatoVO().getObservacao() + tagPesquisa));
                }
                montarSucessoGrowl("Link pesquisa gerado.");
            }
        } catch (Exception ex) {
            montarErro(ex.getMessage());
        }
    }

    public Object carregarListaTiposProfessores() {
        setListaTiposProfessores(new ArrayList<>());
        getListaTiposProfessores().add(new SelectItem("", ""));

        getListaTiposProfessores().add(new SelectItem(TipoColaboradorEnum.PROFESSOR.getSigla(), TipoColaboradorEnum.PROFESSOR.getDescricao()));
        getListaTiposProfessores().add(new SelectItem(TipoColaboradorEnum.PROFESSOR_TREINO.getSigla(), TipoColaboradorEnum.PROFESSOR_TREINO.getDescricao()));

        return true;
    }

    public ListaPaginadaTO getListaHistoricoContatoClientePaginada() {
        return listaHistoricoContatoClientePaginada;
    }

    public void setListaHistoricoContatoClientePaginada(ListaPaginadaTO listaHistoricoContatoClientePaginada) {
        this.listaHistoricoContatoClientePaginada = listaHistoricoContatoClientePaginada;
    }

    public List<SelectItem> getListaTiposProfessores() {
        return listaTiposProfessores;
    }

    public void setListaTiposProfessores(List<SelectItem> listaTiposProfessores) {
        this.listaTiposProfessores = listaTiposProfessores;
    }

    public List<SelectItem> getListaColaboradores() {
        return listaColaboradores;
    }

    public void montarAulasAgenda() {
        try {
            limparMsg();
            setMsgAlert("");
            setListaAulasAgenda(new ArrayList<>());
            setCodigoAula(0);
            getListaAulasAgenda().add(new SelectItem(0, ""));

            if (getAgendaVO().getModalidade() != null){
                ModalidadeVO modalidadeVOBasico = getFacade().getModalidade().consultarPorChavePrimaria(getAgendaVO().getModalidade().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                if(modalidadeVOBasico.isUtilizarTurma()) {
                    throw new Exception("Para buscar Aulas, é necessário selecionar uma modalidade que não utilize a opção \"Utilizar Turma\" no cadastro.");
                }
            }

            if ((getAgendaVO().getModalidade() == null || UteisValidacao.emptyNumber(getAgendaVO().getModalidade().getCodigo())) ||
                    UteisValidacao.emptyNumber(getCodigoProfessor()) || getAgendaVO().getDataAgendamento() == null ||
                    UteisValidacao.emptyString(getAgendaVO().getHoraMinuto())) {
                throw new Exception("Preencha todos os campos obrigatórios para montar as aulas. \"Modalidade\", \"Professor\", \"Data\" e \"Hora\".");
            }

            if (Calendario.menor(getAgendaVO().getDataAgendamento(), Calendario.hoje())) {
                throw new Exception("Data do agendamento não pode ser inferior a hoje.");
            }

            setAulasAgenda(new ArrayList<>());
            setAulasAgenda(getFacade()
                    .getHorarioTurma().consultarPorModalidadeProfessorDiaColetivas(getAgendaVO().getModalidade().getCodigo(), getCodigoProfessor(), getAgendaVO().getDataAgendamento(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));

            Ordenacao.ordenarLista(getAulasAgenda(), "horaInicial");
            for (HorarioTurmaVO aula : getAulasAgenda()) {
                if (Calendario.maiorOuIgualComHora(Calendario.getDataComHora(getAgendaVO().getDataAgendamento(), aula.getHoraInicial()), Calendario.getDataComHora(getAgendaVO().getDataAgendamento(), getAgendaVO().getHoraMinuto()))) {
                    getListaAulasAgenda().add(new SelectItem(aula.getCodigo(), aula.getCodigo() + " - " + aula.getIdentificadorTurma() + " - " + aula.getDiaSemana_Apresentar().toUpperCase() + " - " + aula.getHoraInicial() + " às " + aula.getHoraFinal()));
                }
            }

            if ((getListaAulasAgenda().isEmpty() || getListaAulasAgenda().size() == 1)) {
                throw new Exception("Nenhuma aula encontrada!");
            }
        } catch (Exception ex) {
            montarErro(ex.getMessage());
        }
    }

    public void setListaColaboradores(List<SelectItem> listaColaboradores) {
        this.listaColaboradores = listaColaboradores;
    }

    public String getTipoProfessor() {
        if (tipoProfessor == null) {
            tipoProfessor = "";
        }
        return tipoProfessor;
    }

    public void setTipoProfessor(String tipoProfessor) {
        this.tipoProfessor = tipoProfessor;
    }

    public Integer getCodigoProfessor() {
        return codigoProfessor;
    }

    public void setCodigoProfessor(Integer codigoProfessor) {
        this.codigoProfessor = codigoProfessor;
    }

    public void acaoWonBuzzlead() {
        try {
            getFacade().getZWFacade().acaoBuzzlead(metaDetalhadoVOSelecionado.getConversaoLeadVO(), true, 0.0, "");
        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }

    public void acaoLostBuzzlead() {
        try {
            getFacade().getZWFacade().acaoBuzzlead(metaDetalhadoVOSelecionado.getConversaoLeadVO(), false, 0.0, "TESTE INTEGRAÇÃO");
        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }

    public Boolean getMetaGerando() {
        return metaGerando;
    }

    public void setMetaGerando(Boolean metaGerando) {
        this.metaGerando = metaGerando;
    }

    public Boolean getAbriuMetaHoje() {
        return abriuMetaHoje;
    }

    public void setAbriuMetaHoje(Boolean abriuMetaHoje) {
        this.abriuMetaHoje = abriuMetaHoje;
    }

    public Boolean getProcessarAbertura() {
        return processarAbertura;
    }

    public void setProcessarAbertura(Boolean processarAbertura) {
        this.processarAbertura = processarAbertura;
    }

    public String getTokenSMSEmpresaShortCode() {
        if(tokenSMSEmpresaShortCode == null){
            tokenSMSEmpresaShortCode = "";
        }
        return tokenSMSEmpresaShortCode;
    }

    public void setTokenSMSEmpresaShortCode(String tokenSMSEmpresaShortCode) {
        this.tokenSMSEmpresaShortCode = tokenSMSEmpresaShortCode;
    }

    public void salvarSimplesRegistro() {
        try {
            limparMsg();
            if (getHistoricoContatoVOEdicao() != null) {
                getHistoricoContatoVOEdicao().setResponsavelCadastro(getUsuarioLogado());
                getFacade().getHistoricoContato().alterar(getHistoricoContatoVOEdicao());

                getHistoricoContatoVOEdicao().setObjetoVOAntesAlteracao(getHistoricoContatoVOAntesEdicao().getClone(true));
                getHistoricoContatoVOEdicao().setNovoObj(false);
                registrarLogObjetoVOGeralAlterandoResponsavel(getHistoricoContatoVOEdicao(),
                                     getHistoricoContatoVOEdicao().getCodigo(),
                                     "HISTORICOCONTATO",
                                     getHistoricoContatoVO().getClienteVO().getPessoa().getCodigo(), false, getUsuarioLogado().getNome());

                consultarHistoricoContatoClienteManual(fecharMetaDetalhadoVO);
            }
        } catch (Exception e) {
            e.printStackTrace();
            montarErro(e);
        }

    }

    public void prepararEdicaoSimplesRegistro() {
        try {
            limparMsg();
            setOnCompleteEdicaoSimplesRegistro("");
            validarPermissao("CrmEditarSimplesRegistro", "7.59 - Editar Simples Registro", getUsuarioLogado());
                HistoricoContatoVO obj = (HistoricoContatoVO) context().getExternalContext().getRequestMap().get("contatos");
                if (obj != null) {
                    setHistoricoContatoVOEdicao((HistoricoContatoVO) obj.getClone(true));
                    setHistoricoContatoVOAntesEdicao((HistoricoContatoVO) obj.getClone(true));
                    setOnCompleteEdicaoSimplesRegistro("Richfaces.showModalPanel('mdlEditarSimplesRegistro');");
                }
        } catch (Exception e) {
            e.printStackTrace();
            montarErro(e);
        }
    }

    public String getOnCompleteEdicaoSimplesRegistro() {
        return onCompleteEdicaoSimplesRegistro;
    }

    public void setOnCompleteEdicaoSimplesRegistro(String onCompleteEdicaoSimplesRegistro) {
        this.onCompleteEdicaoSimplesRegistro = onCompleteEdicaoSimplesRegistro;
    }

    public HistoricoContatoVO getHistoricoContatoVOEdicao() {
        return historicoContatoVOEdicao;
    }

    public void setHistoricoContatoVOEdicao(HistoricoContatoVO historicoContatoVOEdicao) {
        this.historicoContatoVOEdicao = historicoContatoVOEdicao;
    }

    public FecharMetaDetalhadoVO getFecharMetaDetalhadoVO() {
        return fecharMetaDetalhadoVO;
    }

    public void setFecharMetaDetalhadoVO(FecharMetaDetalhadoVO fecharMetaDetalhadoVO) {
        this.fecharMetaDetalhadoVO = fecharMetaDetalhadoVO;
    }

    public HistoricoContatoVO getHistoricoContatoVOAntesEdicao() {
        return historicoContatoVOAntesEdicao;
    }

    public void setHistoricoContatoVOAntesEdicao(HistoricoContatoVO historicoContatoVOAntesEdicao) {
        this.historicoContatoVOAntesEdicao = historicoContatoVOAntesEdicao;
    }

    public void realizarConsultaLogObjetoSelecionado() {
        LoginControle loginControle = (LoginControle) context().getExternalContext().getSessionMap().get("LoginControle");
        List<String> nomesClasse = new ArrayList<String>();
        nomesClasse.add("HISTORICOCONTATO");
        loginControle.setNomeClasse(loginControle.getNomeEntidadeCamposLog("prt_Agenda_SimplesRegistro"));
        loginControle.consultarLogEntidadesSelecionado(nomesClasse, 0, getHistoricoContatoVO().getClienteVO().getPessoa().getCodigo());
    }

    public Date getDataInicioOld() {
        return dataInicioOld;
    }

    public void setDataInicioOld(Date dataInicioOld) {
        this.dataInicioOld = dataInicioOld;
    }

    public Date getDataFimOld() {
        return dataFimOld;
    }

    public void setDataFimOld(Date dataFimOld) {
        this.dataFimOld = dataFimOld;
    }

    public String getUrlMqv() {
        if(urlMqv == null){
            return "";
        }
        return urlMqv;
    }

    private static void limparEntradasAntigas() {

        LocalDateTime agora = LocalDateTime.now();
        ULTIMO_ENVIO_MAP.entrySet().removeIf(entry ->
                entry.getValue().isBefore(agora.minusMinutes(DUPLICIDADE_MINUTOS))
        );
    }

    public void setUrlMqv(String urlMqv) {
        this.urlMqv = urlMqv;
    }

    public boolean isExibeMqv() {
        return exibeMqv;
    }

    public void setExibeMqv(boolean exibeMqv) {
        this.exibeMqv = exibeMqv;
    }

    public boolean isExibBotConversa() {
        return exibBotConversa;
    }

    public void setExibBotConversa(boolean exibBotConversa) {
        this.exibBotConversa = exibBotConversa;
    }

    public boolean isExibGymbotPro() {
        return exibGymbotPro;
    }

    public void setExibGymbotPro(boolean exibGymbotPro) {
        this.exibGymbotPro = exibGymbotPro;
    }

    public String getNomeFluxoBotconversa() {
        return nomeFluxoBotconversa;
    }

    public void setNomeFluxoBotconversa(String nomeFluxoBotconversa) {
        this.nomeFluxoBotconversa = nomeFluxoBotconversa;
    }

    public String getNomeFluxoGymbotPro() {
        return nomeFluxoGymbotPro;
    }

    public void setNomeFluxoGymbotPro(String nomeFluxoGymbotPro) {
        this.nomeFluxoGymbotPro = nomeFluxoGymbotPro;
    }

    public String getTokenGymbotPro() {
        return tokenGymbotPro;
    }

    public void setTokenGymbotPro(String tokenGymbotPro) {
        this.tokenGymbotPro = tokenGymbotPro;
    }

    public String getIdFluxoGymbotPro() {
        return idFluxoGymbotPro;
    }

    public void setIdFluxoGymbotPro(String idFluxoGymbotPro) {
        this.idFluxoGymbotPro = idFluxoGymbotPro;
    }

    public String getWebhookBotconversa() {
        return webhookBotconversa;
    }

    public void setWebhookBotconversa(String webhookBotconversa) {
        this.webhookBotconversa = webhookBotconversa;
    }

    public String getNomeCliente() {
        return nomeCliente;
    }

    public void setNomeCliente(String nomeCliente) {
        this.nomeCliente = nomeCliente;
    }

    public List<SelectItem> getListaAulasAgenda() {
        return listaAulasAgenda;
    }

    public void setListaAulasAgenda(List<SelectItem> listaAulasAgenda) {
        this.listaAulasAgenda = listaAulasAgenda;
    }

    public Integer getCodigoAula() {
        return codigoAula;
    }

    public void setCodigoAula(Integer codigoAula) {
        this.codigoAula = codigoAula;
    }

    public Boolean getMetaLead() {
        if (this.tipoMetaSelecionada != null) {
            return (this.tipoMetaSelecionada.getFasesCRMEnum().equals(FasesCRMEnum.LEADS_HOJE) ||
                    this.tipoMetaSelecionada.getFasesCRMEnum().equals(FasesCRMEnum.LEADS_ACUMULADAS) ||
                    this.tipoMetaSelecionada.getFasesCRMEnum().equals(FasesCRMEnum.INDICACOES_SEM_CONTATO) ||
                    this.tipoMetaSelecionada.getFasesCRMEnum().equals(FasesCRMEnum.PASSIVO));
        }
        return false;
    }

    public List<HorarioTurmaVO> getAulasAgenda() {
        return aulasAgenda;
    }

    public void setAulasAgenda(List<HorarioTurmaVO> aulasAgenda) {
        this.aulasAgenda = aulasAgenda;
    }

    public Integer getCodigoGymBot() {
        return codigoGymBot;
    }

    public void setCodigoGymBot(Integer codigoGymBot) {
        this.codigoGymBot = codigoGymBot;
    }

    public Integer getCodigoGymBotPro() {
        return codigoGymBotPro;
    }

    public void setCodigoGymBotPro(Integer codigoGymBotPro) {
        this.codigoGymBotPro = codigoGymBotPro;
    }
}
