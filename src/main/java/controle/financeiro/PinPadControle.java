package controle.financeiro;

import br.com.pactosolucoes.comuns.util.JSFUtilities;
import br.com.pactosolucoes.estrutura.exportador.controle.ExportadorListaControle;
import controle.arquitetura.SuperControle;
import controle.arquitetura.security.LoginControle;
import controle.basico.clube.MensagemGenericaControle;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.financeiro.ConvenioCobrancaVO;
import negocio.comuns.financeiro.PinPadVO;
import negocio.comuns.financeiro.enumerador.OpcoesPinpadEnum;
import negocio.comuns.utilitarias.ControleConsulta;
import negocio.comuns.utilitarias.Ordenacao;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;

import javax.faces.event.ActionEvent;
import javax.faces.model.SelectItem;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

public class PinPadControle extends SuperControle {

    private PinPadVO pinPadVO;
    private PinPadVO pinPadVOClone;
    private String msgAlert;
    private String onComplete;
    private List<EmpresaVO> empresaVOS;
    private List<ConvenioCobrancaVO> convenioCobrancaVOS;

    public PinPadControle() throws Exception {
        obterUsuarioLogado();
        inicializarFacades();
        setControleConsulta(new ControleConsulta());
        setMensagemID("");
    }

    public void inicializarUsuarioLogado() {
        try {
            UsuarioVO usuario = getUsuarioLogado();
            pinPadVO.setUsuarioVO(usuario);
        } catch (Exception ignored) {
        }
    }

    public String novo() throws Exception {
        limparMsg();
        setPinPadVO(new PinPadVO());
        inicializarUsuarioLogado();
        inicializarListas();
        setMensagemID("msg_entre_dados");
        setSucesso(false);
        setErro(false);
        pinPadVO = (PinPadVO) pinPadVO.getClone(true);
        return "editar";
    }

    public String editar() {
        Integer codigoConsulta = Integer.parseInt(request().getParameter("chavePrimaria"));
        try {
            limparMsg();
            PinPadVO obj = getFacade().getPinPad().consultarPorChavePrimaria(codigoConsulta);
            obj.setNovoObj(false);
            setPinPadVO(obj);
            pinPadVO.registrarObjetoVOAntesDaAlteracao();
            inicializarUsuarioLogado();
            inicializarListas();
        } catch (Exception e) {
            setErro(true);
            setSucesso(false);
            setMensagemDetalhada("msg_erro", e.getMessage());
        } finally {
            try {
                pinPadVOClone = (PinPadVO) pinPadVO.getClone(false);
            } catch (IllegalAccessException | InstantiationException e) {
                e.printStackTrace();
            }
        }
        return "editar";
    }

    public void gravar() {
        try {
            limparMsg();

            if (this.getPinPadVO().isStoneConnect() ||
                    this.getPinPadVO().isGetCard()) {
                if (UteisValidacao.emptyString(this.getPinPadVO().getDescricao())) {
                    throw new Exception("Informe uma descrição para o pinpad");
                }
                if (UteisValidacao.emptyString(this.getPinPadVO().getPdvPinpad())) {
                    if (this.getPinPadVO().isGetCard()) {
                        throw new Exception("Informe o número do PDV");
                    } else {
                        throw new Exception("Informe o Serial Number do pinpad");
                    }
                }
                if (UteisValidacao.emptyNumber(this.getPinPadVO().getConvenioCobranca().getCodigo())) {
                    throw new Exception("Informe o convênio de cobrança do pinpad");
                }
            }

            if (pinPadVO.isNovoObj()) {
                getFacade().getPinPad().incluir(pinPadVO);
                pinPadVOClone = (PinPadVO) pinPadVO.getClone(true);
                incluirLogInclusao();
            } else {
                pinPadVOClone = (PinPadVO) pinPadVO.getClone(true);
                getFacade().getPinPad().alterar(pinPadVO);
                incluirLogAlteracao();
            }
            montarSucessoGrowl("Dados gravados com sucesso!");
        } catch (Exception ex) {
            montarErro(ex);
        }
    }

    public String excluir() {
        try {
            getFacade().getPinPad().excluir(pinPadVO);
            incluirLogExclusao();
            setPinPadVO(new PinPadVO());
            setMensagemID("msg_dados_excluidos");
            setSucesso(true);
            setErro(false);
            return "consultar";
        } catch (Exception e) {
            if (e.getMessage().contains("ERRO: atualização ou exclusão em tabela \"pinpad\" viola restrição de chave estrangeira") || e.getMessage().contains("ERROR: update or delete on table \"pinpad\" violates foreign key")) {
                setMensagemDetalhada("Este pinpad não pode ser excluído, pois está sendo utilizado!");
            } else {
                setMensagemDetalhada("msg_erro", e.getMessage());
            }
            setSucesso(false);
            setErro(true);
            return "editar";
        }
    }

    public void inicializarListas() {
        montarListaEmpresa();
        montarListaConvenioCobranca();
    }

    private void montarListaConvenioCobranca() {
        try {
            setConvenioCobrancaVOS(getFacade().getConvenioCobranca().consultarTodos(Uteis.NIVELMONTARDADOS_DADOSBASICOS));
        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }

    private void montarListaEmpresa() {
        try {
            setEmpresaVOS(getFacade().getEmpresa().consultarTodas(true, Uteis.NIVELMONTARDADOS_EMPRESA_BASICO));
        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }

    protected boolean inicializarFacades() {
        try {
            super.inicializarFacades();
            return true;
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro_conectarBD", e.getMessage());
            return false;
        }
    }

    public List<SelectItem> getListaSelectTipoPinPad() {
        List<SelectItem> lista = new ArrayList<>();
        for (OpcoesPinpadEnum obj : OpcoesPinpadEnum.values()) {
            if (!obj.equals(OpcoesPinpadEnum.STONE_CONNECT)) {
                // a princípio esse cadastro foi criado somente para stone connect para totem
                // os demais são criados na forma de pagamento.
                // de acordo com a necessidade será incluído no cadastro os demais tipo de pinpad
                continue;
            }
            lista.add(new SelectItem(obj.getCodigo(), obj.getNome()));
        }
        lista.add(0, new SelectItem(0, ""));
        return lista;
    }

    public void exportar(ActionEvent evt) throws Exception {
        ExportadorListaControle exportadorListaControle = (ExportadorListaControle) JSFUtilities.getFromRequest(ExportadorListaControle.class.getSimpleName());
        String paramsTableFiltrada = context().getExternalContext().getRequestParameterMap().get("paramsTableFiltrada");

        String[] split = paramsTableFiltrada.split(",");
        String campoOrdenacao = split[0].replace("[", "");
        String ordem = split[1];
        String filtro = split[2].replace("''", "");
        filtro = filtro.replace("]", "");
        List listaParaImpressao = getFacade().getPinPad().consultarParaImpressao(filtro, ordem, campoOrdenacao, getEmpresaLogado() == null ? 0 : getEmpresaLogado().getCodigo());
        exportadorListaControle.exportar(evt, listaParaImpressao, filtro, null);

    }

    public void incluirLogInclusao() throws Exception {
        try {
            pinPadVO.setObjetoVOAntesAlteracao(new PinPadVO());
            pinPadVO.setNovoObj(true);
            registrarLogObjetoVO(pinPadVO, pinPadVO.getCodigo(), "PINPAD", 0);
        } catch (Exception e) {
            registrarLogErroObjetoVO("PINPAD", pinPadVO.getCodigo(), "ERRO AO GERAR LOG DE INCLUSÃO DE PINPAD", this.getUsuarioLogado().getNome(), this.getUsuarioLogado().getUserOamd());
            e.printStackTrace();
        }
        pinPadVO.setNovoObj(new Boolean(false));
        pinPadVO.registrarObjetoVOAntesDaAlteracao();
    }

    public void incluirLogExclusao() throws Exception {
        try {
            pinPadVO.setObjetoVOAntesAlteracao(new PinPadVO());
            pinPadVO.setNovoObj(true);
            registrarLogExclusaoTodosDadosObjetoVO(pinPadVO, pinPadVO.getCodigo(), "PINPAD", 0);
        } catch (Exception e) {
            registrarLogErroObjetoVO("PINPAD", pinPadVO.getCodigo(), "ERRO AO GERAR LOG DE EXCLUSÃO DE PINPAD ", this.getUsuarioLogado().getNome(), this.getUsuarioLogado().getUserOamd());
            e.printStackTrace();
        }
    }

    public void incluirLogAlteracao() throws Exception {
        try {
            registrarLogObjetoVO(pinPadVO, pinPadVO.getCodigo(), "PINPAD", 0);
        } catch (Exception e) {
            registrarLogErroObjetoVO("PINPAD", pinPadVO.getCodigo(), "ERRO AO GERAR LOG DE ALTERAÇÃO DE PINPAD ", this.getUsuarioLogado().getNome(), this.getUsuarioLogado().getUserOamd());
            e.printStackTrace();
        }
        pinPadVO.registrarObjetoVOAntesDaAlteracao();
    }

    public void realizarConsultaLogObjetoSelecionado() {
        LoginControle loginControle = (LoginControle) context().getExternalContext().getSessionMap().get("LoginControle");
        String nomeClasse = pinPadVO.getClass().getSimpleName();
        nomeClasse = nomeClasse.substring(0, nomeClasse.length() - 2);
        loginControle.setNomeClasse(loginControle.getNomeEntidadeCamposLog("prt_" + nomeClasse + "_tituloForm"));
        loginControle.consultarLogObjetoSelecionado(nomeClasse.toUpperCase(), pinPadVO.getCodigo(), 0);
    }

    public void realizarConsultaLogObjetoGeral() {
        pinPadVO = new PinPadVO();
        realizarConsultaLogObjetoSelecionado();
    }

    public void setMsgAlert(String msgAlert) {
        this.msgAlert = msgAlert;
    }

    public String getMsgAlert() {
        if (msgAlert == null) {
            return "";
        }
        return msgAlert;
    }

    public void confirmarExcluir() {
        MensagemGenericaControle control = (MensagemGenericaControle) getControlador(MensagemGenericaControle.class);
        control.setMensagemDetalhada("", "");
        setMsgAlert("Richfaces.showModalPanel('mdlMensagemGenerica');");
        control.init("Exclusão de PinPad",
                "Deseja excluir o PinPad?",
                this, "excluir", "", "", "", "grupoBtnExcluir");
    }

    public String getOnComplete() {
        if (onComplete == null) {
            onComplete = "";
        }
        return onComplete;
    }

    public void setOnComplete(String onComplete) {
        this.onComplete = onComplete;
    }

    public PinPadVO getPinPadVO() {
        if (pinPadVO == null) {
            pinPadVO = new PinPadVO();
        }
        return pinPadVO;
    }

    public void setPinPadVO(PinPadVO pinPadVO) {
        this.pinPadVO = pinPadVO;
    }

    public String inicializarConsultar() {
        setPaginaAtualDeTodas("0/0");
        setListaConsulta(new ArrayList());
        definirVisibilidadeLinksNavegacao(0, 0);
        limparMsg();
        try {
            pinPadVOClone = (PinPadVO) pinPadVO.getClone(false);
        } catch (IllegalAccessException | InstantiationException e) {
            e.printStackTrace();
        }
        return "consultar";
    }

    public PinPadVO getPinPadVOClone() {
        return pinPadVOClone;
    }

    public void setPinPadVOClone(PinPadVO pinPadVOClone) {
        this.pinPadVOClone = pinPadVOClone;
    }

    public List<EmpresaVO> getEmpresaVOS() {
        if (empresaVOS == null) {
            empresaVOS = new ArrayList<>();
        }
        return empresaVOS;
    }

    public void setEmpresaVOS(List<EmpresaVO> empresaVOS) {
        this.empresaVOS = empresaVOS;
    }

    public List<ConvenioCobrancaVO> getConvenioCobrancaVOS() {
        if (convenioCobrancaVOS == null) {
            convenioCobrancaVOS = new ArrayList<>();
        }
        return convenioCobrancaVOS;
    }

    public void setConvenioCobrancaVOS(List<ConvenioCobrancaVO> convenioCobrancaVOS) {
        this.convenioCobrancaVOS = convenioCobrancaVOS;
    }

    public List<SelectItem> getSelectEmpresa() {
        List<SelectItem> lista = new ArrayList<>();
        for (EmpresaVO obj : this.getEmpresaVOS()) {
            lista.add(new SelectItem(obj.getCodigo(), obj.getNome()));
        }
        Ordenacao.ordenarLista(lista, "label");
        lista.add(0, new SelectItem(0, ""));
        return lista;
    }

    public List<SelectItem> getSelectConvenioCobranca() {
        List<SelectItem> lista = new ArrayList<>();

        Set<Integer> adicionados = new HashSet<>();
        for (ConvenioCobrancaVO obj : this.getConvenioCobrancaVOS()) {
            if (this.getPinPadVO() != null &&
                    this.getPinPadVO().getPinpad() != null &&
                    this.getPinPadVO().getPinpadEnum() != null &&
                    obj.getTipo().getOpcoesPinpadEnum() != null &&
                    obj.getTipo().getOpcoesPinpadEnum().equals(this.getPinPadVO().getPinpadEnum()) &&
                    !adicionados.contains(obj.getCodigo())) {
                lista.add(new SelectItem(obj.getCodigo(), obj.getDescricao()));
                adicionados.add(obj.getCodigo());
            }
        }
        Ordenacao.ordenarLista(lista, "label");
        lista.add(0, new SelectItem(0, ""));
        return lista;
    }
}
