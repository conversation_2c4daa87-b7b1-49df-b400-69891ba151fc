package controle.financeiro;

import br.com.pactosolucoes.comuns.util.Formatador;
import br.com.pactosolucoes.comuns.util.JSFUtilities;
import br.com.pactosolucoes.estrutura.exportador.controle.ExportadorListaControle;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.financeiro.PlanoContaTO;
import negocio.comuns.financeiro.enumerador.TipoES;
import negocio.comuns.financeiro.enumerador.TipoRelatorioDF;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.ContadorTempo;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.comuns.utilitarias.UtilReflection;
import negocio.facade.jdbc.financeiro.RelatorioOrcamentarioConfig;
import negocio.facade.jdbc.financeiro.RelatorioOrcamentarioConfigPrevisao;
import net.sf.jasperreports.engine.data.JRBeanCollectionDataSource;
import relatorio.arquitetura.VisualizadorRelatorio;
import relatorio.negocio.comuns.financeiro.TotalizadorMesDF;
import relatorio.negocio.comuns.financeiro.TotalizadorMesRelOrcamentario;
import relatorio.negocio.jdbc.financeiro.DemonstrativoFinanceiro;
import relatorio.negocio.jdbc.financeiro.DetalhamentoOrcamentarioRel;
import relatorio.negocio.jdbc.financeiro.RelatorioDemonstrativoFinanceiro;

import javax.faces.event.ActionEvent;
import javax.servlet.http.HttpServletRequest;
import java.io.File;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class RelatorioOrcamentarioControle extends FinanControle {
    private Date inicio = Calendario.hoje();
    private String tipoEmissao = "";
    private Date inicioGeracao = new Date();
    private Date fimGeracao = new Date();
    private List<DetalhamentoOrcamentarioRel> listaOrcamentarioRel = new ArrayList<>();
    private TotalizadorMesRelOrcamentario mes1TotalFimPagina = new TotalizadorMesRelOrcamentario();
    private TotalizadorMesRelOrcamentario mes2TotalFimPagina = new TotalizadorMesRelOrcamentario();
    private TotalizadorMesRelOrcamentario mes3TotalFimPagina = new TotalizadorMesRelOrcamentario();
    private TotalizadorMesRelOrcamentario mes4TotalFimPagina = new TotalizadorMesRelOrcamentario();
    private TotalizadorMesRelOrcamentario mes5TotalFimPagina = new TotalizadorMesRelOrcamentario();
    private TotalizadorMesRelOrcamentario mes6TotalFimPagina = new TotalizadorMesRelOrcamentario();
    private TotalizadorMesRelOrcamentario mes7TotalFimPagina = new TotalizadorMesRelOrcamentario();
    private TotalizadorMesRelOrcamentario mes8TotalFimPagina = new TotalizadorMesRelOrcamentario();
    private TotalizadorMesRelOrcamentario mes9TotalFimPagina = new TotalizadorMesRelOrcamentario();
    private TotalizadorMesRelOrcamentario mes10TotalFimPagina = new TotalizadorMesRelOrcamentario();
    private TotalizadorMesRelOrcamentario mes11TotalFimPagina = new TotalizadorMesRelOrcamentario();
    private TotalizadorMesRelOrcamentario mes12TotalFimPagina = new TotalizadorMesRelOrcamentario();

    private TotalizadorMesRelOrcamentario totalPrevistoRealizadoFimPagina = new TotalizadorMesRelOrcamentario();
    private String urlRelOrcamentarioExcel = "";

    public RelatorioOrcamentarioControle() throws Exception {
        inicializarEmpresa();
        montarListaSelectItemEmpresa();
    }

    public void abrirTelaRelatorioOrcamentario() {
        try {
            limparFiltros();
            setMsgAlert("abrirPopup('" + request().getContextPath() + "/faces/pages/finan/relatorioOrcamentario.jsp?modulo=financeiroWeb', 'relatorioOrcamentario', 600, 400);");
            limparMsg();
        } catch (Exception e) {
            setMensagemDetalhada(e);
            setMsgAlert(getMensagemDetalhada());
        }
    }

    public void limparFiltros(){
        inicio = Calendario.hoje();
        inicioGeracao = new Date(inicio.getTime());
        fimGeracao = new Date(inicio.getTime());
        tipoEmissao = "";
    }

    public void exportarExcel(ActionEvent evt) throws Exception {
        obterDadosRelatorio();

        ExportadorListaControle exportadorListaControle = (ExportadorListaControle)JSFUtilities.getManagedBean("ExportadorListaControle");
        if(!UteisValidacao.emptyList(listaOrcamentarioRel)){
            exportadorListaControle.exportar(evt, listaOrcamentarioRel, "", null);
        }

        setUrlRelOrcamentarioExcel("location.href=\"../../UpdateServlet?op=downloadfile&file=" + exportadorListaControle.getFileName() + "&mimetype=application/vnd.ms-excel\"");
    }

    public String getAtributosExportarExcel() {
        if (UteisValidacao.emptyList(listaOrcamentarioRel)) {
            return "";
        }
        StringBuilder atributos = new StringBuilder();
        atributos.append("nomeAgrupador=Plano de Contas");
        Integer qtdMeses = Integer.parseInt(tipoEmissao);
        for (int i = 1; i <= qtdMeses; i++) {
            TotalizadorMesRelOrcamentario totalizadorMesRelOrcamentario = (TotalizadorMesRelOrcamentario) UtilReflection.getValorObject(this.listaOrcamentarioRel.get(0), "mes" + i);
            String attrPrevisto = String.format("mes%d.totalPrevistoMes=Previso %s", i, totalizadorMesRelOrcamentario.getMesProcessar().getMesAno());
            String attrRealizado = String.format("mes%d.totalRealizadoMes=Realizado %s", i, totalizadorMesRelOrcamentario.getMesProcessar().getMesAno());
            atributos.append("," + attrPrevisto + "," + attrRealizado);
        }
        atributos.append(",totalPrevisto=Total Previsto,totalRealizado=Total Realizado,saldoFinal=Saldo,percPretendidoStr=Variação(%)");
        return atributos.toString();
    }

    public void exportarRelatorio(ActionEvent evt) throws Exception {

        obterDadosRelatorio();

        try {
            setMsgAlert("");

            Integer emp = getUsuarioLogado().getAdministrador() ? 0 : getEmpresaLogado().getCodigo();
            EmpresaVO empre = new EmpresaVO();
            if (emp != 0) {
                empre = getFacade().getEmpresa().consultarPorChavePrimaria(emp, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            }

            //Parametros obrigatorios
            HttpServletRequest request = (HttpServletRequest) context().getExternalContext().getRequest();
            Map<String, Object> params = new HashMap<>();
            String caminhoSubRelatorio = "relatorio" + File.separator + "designRelatorio" + File.separator + "financeiro" + File.separator;
            String caminhoRelatorio = "";
            if (tipoEmissao.equals("1")) {
                //Parametros obrigatorios
                caminhoRelatorio = caminhoSubRelatorio + "RelatorioOrcamentarioMensal.jrxml";
                request.setAttribute("nomeRelatorio", "RelatorioOrcamentarioMensal");
                request.setAttribute("tituloRelatorio", "Realizacao Orcamentaria Mensal");

            } else if (tipoEmissao.equals("3")) {
                //Parametros obrigatorios
                caminhoRelatorio = caminhoSubRelatorio + "RelatorioOrcamentarioTrimestral.jrxml";
                request.setAttribute("nomeRelatorio", "RelatorioOrcamentarioTrimestral");
                request.setAttribute("tituloRelatorio", "Realizacao Orcamentaria Trimestral");
                params.put("data2", this.listaOrcamentarioRel.get(0).getMes2().getMesProcessar().getMesAno());
                params.put("data3", this.listaOrcamentarioRel.get(0).getMes3().getMesProcessar().getMesAno());

            } else if (tipoEmissao.equals("6")) {
                //Parametros obrigatorios
                caminhoRelatorio = caminhoSubRelatorio + "RelatorioOrcamentarioSemestral.jrxml";
                request.setAttribute("nomeRelatorio", "RelatorioOrcamentarioSemestral");
                request.setAttribute("tituloRelatorio", "Realizacao Orçamentaria Semestral");
                params.put("data2", this.listaOrcamentarioRel.get(0).getMes2().getMesProcessar().getMesAno());
                params.put("data3", this.listaOrcamentarioRel.get(0).getMes3().getMesProcessar().getMesAno());
                params.put("data4", this.listaOrcamentarioRel.get(0).getMes4().getMesProcessar().getMesAno());
                params.put("data5", this.listaOrcamentarioRel.get(0).getMes5().getMesProcessar().getMesAno());
                params.put("data6", this.listaOrcamentarioRel.get(0).getMes6().getMesProcessar().getMesAno());
            } else if (tipoEmissao.equals("12")) {
                caminhoRelatorio = caminhoSubRelatorio + "RelatorioOrcamentarioAnual.jrxml";
                request.setAttribute("nomeRelatorio", "RelatorioOrcamentarioAnual");
                request.setAttribute("tituloRelatorio", "Realizacao Orcamentaria Anual");

                params.put("data2", this.listaOrcamentarioRel.get(0).getMes2().getMesProcessar().getMesAno());
                params.put("data3", this.listaOrcamentarioRel.get(0).getMes3().getMesProcessar().getMesAno());
                params.put("data4", this.listaOrcamentarioRel.get(0).getMes4().getMesProcessar().getMesAno());
                params.put("data5", this.listaOrcamentarioRel.get(0).getMes5().getMesProcessar().getMesAno());
                params.put("data6", this.listaOrcamentarioRel.get(0).getMes6().getMesProcessar().getMesAno());
                params.put("data7", this.listaOrcamentarioRel.get(0).getMes7().getMesProcessar().getMesAno());
                params.put("data8", this.listaOrcamentarioRel.get(0).getMes8().getMesProcessar().getMesAno());
                params.put("data9", this.listaOrcamentarioRel.get(0).getMes9().getMesProcessar().getMesAno());
                params.put("data10", this.listaOrcamentarioRel.get(0).getMes10().getMesProcessar().getMesAno());
                params.put("data11", this.listaOrcamentarioRel.get(0).getMes11().getMesProcessar().getMesAno());
                params.put("data12", this.listaOrcamentarioRel.get(0).getMes12().getMesProcessar().getMesAno());

            }
            String tipoRelatorio = (String) JSFUtilities.getFromActionEvent("tipo", evt);
            request.setAttribute("tipoRelatorio", "PDF");
            request.setAttribute("nomeEmpresa", empre.getNome());
            request.setAttribute("nomeDesignIReport", caminhoRelatorio);
            request.setAttribute("SUBREPORT_DIR", caminhoSubRelatorio);
            request.setAttribute("listaObjetos", this.listaOrcamentarioRel);
            request.setAttribute("tipoImplementacao", "OBJETO");
            params.put("data1", this.listaOrcamentarioRel.get(0).getMes1().getMesProcessar().getMesAno());
            //Parametros opicionais
            parametrosMes1TotalFimPagina(params);
            parametrosMes2TotalFimPagina(params);
            parametrosMes3TotalFimPagina(params);
            parametrosMes4TotalFimPagina(params);
            parametrosMes5TotalFimPagina(params);
            parametrosMes6TotalFimPagina(params);
            parametrosMes7TotalFimPagina(params);
            parametrosMes8TotalFimPagina(params);
            parametrosMes9TotalFimPagina(params);
            parametrosMes10TotalFimPagina(params);
            parametrosMes11TotalFimPagina(params);
            parametrosMes12TotalFimPagina(params);
            calcularTotalPrevistoRealizadoFimPagina();
            parametrosTotalPrevistoRealizadoFimPagina(params);
            calcularSaldosEVariacaoTotalFimPaginaAddParametros(params);

            //Converte lista em dataSorce para usar no param do Jasper Report
            JRBeanCollectionDataSource itensJRBean = new JRBeanCollectionDataSource(this.listaOrcamentarioRel);
            params.put("itemDataSource", itensJRBean);
            request.setAttribute("parametrosRelatorio", params);

            VisualizadorRelatorio visualizador = new VisualizadorRelatorio();
            visualizador.processRequest(request, null);
            visualizador = null;

            setMsgAlert("abrirPopupPDFImpressao('../../relatorio/" + getNomeArquivoRelatorioGeradoAgora() + "','', 780, 595);");
        } catch (Exception e) {
            montarMsgAlert(e.getMessage());
        }
    }

    private void obterDadosRelatorio() {
        listaOrcamentarioRel = new ArrayList<>();
        mes1TotalFimPagina = new TotalizadorMesRelOrcamentario();
        mes2TotalFimPagina = new TotalizadorMesRelOrcamentario();
        mes3TotalFimPagina = new TotalizadorMesRelOrcamentario();
        mes4TotalFimPagina = new TotalizadorMesRelOrcamentario();
        mes5TotalFimPagina = new TotalizadorMesRelOrcamentario();
        mes6TotalFimPagina = new TotalizadorMesRelOrcamentario();
        mes7TotalFimPagina = new TotalizadorMesRelOrcamentario();
        mes8TotalFimPagina = new TotalizadorMesRelOrcamentario();
        mes9TotalFimPagina = new TotalizadorMesRelOrcamentario();
        mes10TotalFimPagina = new TotalizadorMesRelOrcamentario();
        mes11TotalFimPagina = new TotalizadorMesRelOrcamentario();
        mes12TotalFimPagina = new TotalizadorMesRelOrcamentario();

        //Gera lista dados para impressão PDF
        gerarDatasConsulta();
        gerarDemonstrativoComThread();
        gerarDadosPrevisao();

        //Gerar na lista de DetalhamentoOrcamentario os Totais, Saldo e Porcentagem
        gerarTotaisSaldoVariacaoListaDetalhamentoOrcamentario();
        //Gerar totais o final da pagina
        gerarTotaisFinalPaginaMes1();
        gerarTotaisFinalPaginaMes2();
        gerarTotaisFinalPaginaMes3();
        gerarTotaisFinalPaginaMes4();
        gerarTotaisFinalPaginaMes5();
        gerarTotaisFinalPaginaMes6();
        gerarTotaisFinalPaginaMes7();
        gerarTotaisFinalPaginaMes8();
        gerarTotaisFinalPaginaMes9();
        gerarTotaisFinalPaginaMes10();
        gerarTotaisFinalPaginaMes11();
        gerarTotaisFinalPaginaMes12();
    }

    public void calcularTotalPrevistoRealizadoFimPagina() {
        // RECEITA PREVISTA
        totalPrevistoRealizadoFimPagina.setTotalReceitasPrevistoMes(this.mes1TotalFimPagina.getTotalReceitasPrevistoMes());
        totalPrevistoRealizadoFimPagina.setTotalReceitasPrevistoMes(this.totalPrevistoRealizadoFimPagina.getTotalReceitasPrevistoMes() + this.mes2TotalFimPagina.getTotalReceitasPrevistoMes());
        totalPrevistoRealizadoFimPagina.setTotalReceitasPrevistoMes(this.totalPrevistoRealizadoFimPagina.getTotalReceitasPrevistoMes() + this.mes3TotalFimPagina.getTotalReceitasPrevistoMes());
        totalPrevistoRealizadoFimPagina.setTotalReceitasPrevistoMes(this.totalPrevistoRealizadoFimPagina.getTotalReceitasPrevistoMes() + this.mes4TotalFimPagina.getTotalReceitasPrevistoMes());
        totalPrevistoRealizadoFimPagina.setTotalReceitasPrevistoMes(this.totalPrevistoRealizadoFimPagina.getTotalReceitasPrevistoMes() + this.mes5TotalFimPagina.getTotalReceitasPrevistoMes());
        totalPrevistoRealizadoFimPagina.setTotalReceitasPrevistoMes(this.totalPrevistoRealizadoFimPagina.getTotalReceitasPrevistoMes() + this.mes6TotalFimPagina.getTotalReceitasPrevistoMes());
        totalPrevistoRealizadoFimPagina.setTotalReceitasPrevistoMes(this.totalPrevistoRealizadoFimPagina.getTotalReceitasPrevistoMes() + this.mes7TotalFimPagina.getTotalReceitasPrevistoMes());
        totalPrevistoRealizadoFimPagina.setTotalReceitasPrevistoMes(this.totalPrevistoRealizadoFimPagina.getTotalReceitasPrevistoMes() + this.mes8TotalFimPagina.getTotalReceitasPrevistoMes());
        totalPrevistoRealizadoFimPagina.setTotalReceitasPrevistoMes(this.totalPrevistoRealizadoFimPagina.getTotalReceitasPrevistoMes() + this.mes9TotalFimPagina.getTotalReceitasPrevistoMes());
        totalPrevistoRealizadoFimPagina.setTotalReceitasPrevistoMes(this.totalPrevistoRealizadoFimPagina.getTotalReceitasPrevistoMes() + this.mes10TotalFimPagina.getTotalReceitasPrevistoMes());
        totalPrevistoRealizadoFimPagina.setTotalReceitasPrevistoMes(this.totalPrevistoRealizadoFimPagina.getTotalReceitasPrevistoMes() + this.mes11TotalFimPagina.getTotalReceitasPrevistoMes());
        totalPrevistoRealizadoFimPagina.setTotalReceitasPrevistoMes(this.totalPrevistoRealizadoFimPagina.getTotalReceitasPrevistoMes() + this.mes12TotalFimPagina.getTotalReceitasPrevistoMes());

        // RECEITA REALIZADA
        totalPrevistoRealizadoFimPagina.setTotalReceitasRealizadoes(this.mes1TotalFimPagina.getTotalReceitasRealizadoes());
        totalPrevistoRealizadoFimPagina.setTotalReceitasRealizadoes(this.totalPrevistoRealizadoFimPagina.getTotalReceitasRealizadoes() + this.mes2TotalFimPagina.getTotalReceitasRealizadoes());
        totalPrevistoRealizadoFimPagina.setTotalReceitasRealizadoes(this.totalPrevistoRealizadoFimPagina.getTotalReceitasRealizadoes() + this.mes3TotalFimPagina.getTotalReceitasRealizadoes());
        totalPrevistoRealizadoFimPagina.setTotalReceitasRealizadoes(this.totalPrevistoRealizadoFimPagina.getTotalReceitasRealizadoes() + this.mes4TotalFimPagina.getTotalReceitasRealizadoes());
        totalPrevistoRealizadoFimPagina.setTotalReceitasRealizadoes(this.totalPrevistoRealizadoFimPagina.getTotalReceitasRealizadoes() + this.mes5TotalFimPagina.getTotalReceitasRealizadoes());
        totalPrevistoRealizadoFimPagina.setTotalReceitasRealizadoes(this.totalPrevistoRealizadoFimPagina.getTotalReceitasRealizadoes() + this.mes6TotalFimPagina.getTotalReceitasRealizadoes());
        totalPrevistoRealizadoFimPagina.setTotalReceitasRealizadoes(this.totalPrevistoRealizadoFimPagina.getTotalReceitasRealizadoes() + this.mes7TotalFimPagina.getTotalReceitasRealizadoes());
        totalPrevistoRealizadoFimPagina.setTotalReceitasRealizadoes(this.totalPrevistoRealizadoFimPagina.getTotalReceitasRealizadoes() + this.mes8TotalFimPagina.getTotalReceitasRealizadoes());
        totalPrevistoRealizadoFimPagina.setTotalReceitasRealizadoes(this.totalPrevistoRealizadoFimPagina.getTotalReceitasRealizadoes() + this.mes9TotalFimPagina.getTotalReceitasRealizadoes());
        totalPrevistoRealizadoFimPagina.setTotalReceitasRealizadoes(this.totalPrevistoRealizadoFimPagina.getTotalReceitasRealizadoes() + this.mes10TotalFimPagina.getTotalReceitasRealizadoes());
        totalPrevistoRealizadoFimPagina.setTotalReceitasRealizadoes(this.totalPrevistoRealizadoFimPagina.getTotalReceitasRealizadoes() + this.mes11TotalFimPagina.getTotalReceitasRealizadoes());
        totalPrevistoRealizadoFimPagina.setTotalReceitasRealizadoes(this.totalPrevistoRealizadoFimPagina.getTotalReceitasRealizadoes() + this.mes12TotalFimPagina.getTotalReceitasRealizadoes());

        //DESPESA PREVISTA
        totalPrevistoRealizadoFimPagina.setTotalDespesasPrevistoMes(this.mes1TotalFimPagina.getTotalDespesasPrevistoMes());
        totalPrevistoRealizadoFimPagina.setTotalDespesasPrevistoMes(this.totalPrevistoRealizadoFimPagina.getTotalDespesasPrevistoMes() + this.mes2TotalFimPagina.getTotalDespesasPrevistoMes());
        totalPrevistoRealizadoFimPagina.setTotalDespesasPrevistoMes(this.totalPrevistoRealizadoFimPagina.getTotalDespesasPrevistoMes() + this.mes3TotalFimPagina.getTotalDespesasPrevistoMes());
        totalPrevistoRealizadoFimPagina.setTotalDespesasPrevistoMes(this.totalPrevistoRealizadoFimPagina.getTotalDespesasPrevistoMes() + this.mes4TotalFimPagina.getTotalDespesasPrevistoMes());
        totalPrevistoRealizadoFimPagina.setTotalDespesasPrevistoMes(this.totalPrevistoRealizadoFimPagina.getTotalDespesasPrevistoMes() + this.mes5TotalFimPagina.getTotalDespesasPrevistoMes());
        totalPrevistoRealizadoFimPagina.setTotalDespesasPrevistoMes(this.totalPrevistoRealizadoFimPagina.getTotalDespesasPrevistoMes() + this.mes6TotalFimPagina.getTotalDespesasPrevistoMes());
        totalPrevistoRealizadoFimPagina.setTotalDespesasPrevistoMes(this.totalPrevistoRealizadoFimPagina.getTotalDespesasPrevistoMes() + this.mes7TotalFimPagina.getTotalDespesasPrevistoMes());
        totalPrevistoRealizadoFimPagina.setTotalDespesasPrevistoMes(this.totalPrevistoRealizadoFimPagina.getTotalDespesasPrevistoMes() + this.mes8TotalFimPagina.getTotalDespesasPrevistoMes());
        totalPrevistoRealizadoFimPagina.setTotalDespesasPrevistoMes(this.totalPrevistoRealizadoFimPagina.getTotalDespesasPrevistoMes() + this.mes9TotalFimPagina.getTotalDespesasPrevistoMes());
        totalPrevistoRealizadoFimPagina.setTotalDespesasPrevistoMes(this.totalPrevistoRealizadoFimPagina.getTotalDespesasPrevistoMes() + this.mes10TotalFimPagina.getTotalDespesasPrevistoMes());
        totalPrevistoRealizadoFimPagina.setTotalDespesasPrevistoMes(this.totalPrevistoRealizadoFimPagina.getTotalDespesasPrevistoMes() + this.mes11TotalFimPagina.getTotalDespesasPrevistoMes());
        totalPrevistoRealizadoFimPagina.setTotalDespesasPrevistoMes(this.totalPrevistoRealizadoFimPagina.getTotalDespesasPrevistoMes() + this.mes12TotalFimPagina.getTotalDespesasPrevistoMes());

        //DESPESA REALIZADA
        totalPrevistoRealizadoFimPagina.setTotalDespesasRealizadoMes(this.mes1TotalFimPagina.getTotalDespesasRealizadoMes());
        totalPrevistoRealizadoFimPagina.setTotalDespesasRealizadoMes(this.totalPrevistoRealizadoFimPagina.getTotalDespesasRealizadoMes() + this.mes2TotalFimPagina.getTotalDespesasRealizadoMes());
        totalPrevistoRealizadoFimPagina.setTotalDespesasRealizadoMes(this.totalPrevistoRealizadoFimPagina.getTotalDespesasRealizadoMes() + this.mes3TotalFimPagina.getTotalDespesasRealizadoMes());
        totalPrevistoRealizadoFimPagina.setTotalDespesasRealizadoMes(this.totalPrevistoRealizadoFimPagina.getTotalDespesasRealizadoMes() + this.mes4TotalFimPagina.getTotalDespesasRealizadoMes());
        totalPrevistoRealizadoFimPagina.setTotalDespesasRealizadoMes(this.totalPrevistoRealizadoFimPagina.getTotalDespesasRealizadoMes() + this.mes5TotalFimPagina.getTotalDespesasRealizadoMes());
        totalPrevistoRealizadoFimPagina.setTotalDespesasRealizadoMes(this.totalPrevistoRealizadoFimPagina.getTotalDespesasRealizadoMes() + this.mes6TotalFimPagina.getTotalDespesasRealizadoMes());
        totalPrevistoRealizadoFimPagina.setTotalDespesasRealizadoMes(this.totalPrevistoRealizadoFimPagina.getTotalDespesasRealizadoMes() + this.mes7TotalFimPagina.getTotalDespesasRealizadoMes());
        totalPrevistoRealizadoFimPagina.setTotalDespesasRealizadoMes(this.totalPrevistoRealizadoFimPagina.getTotalDespesasRealizadoMes() + this.mes8TotalFimPagina.getTotalDespesasRealizadoMes());
        totalPrevistoRealizadoFimPagina.setTotalDespesasRealizadoMes(this.totalPrevistoRealizadoFimPagina.getTotalDespesasRealizadoMes() + this.mes9TotalFimPagina.getTotalDespesasRealizadoMes());
        totalPrevistoRealizadoFimPagina.setTotalDespesasRealizadoMes(this.totalPrevistoRealizadoFimPagina.getTotalDespesasRealizadoMes() + this.mes10TotalFimPagina.getTotalDespesasRealizadoMes());
        totalPrevistoRealizadoFimPagina.setTotalDespesasRealizadoMes(this.totalPrevistoRealizadoFimPagina.getTotalDespesasRealizadoMes() + this.mes11TotalFimPagina.getTotalDespesasRealizadoMes());
        totalPrevistoRealizadoFimPagina.setTotalDespesasRealizadoMes(this.totalPrevistoRealizadoFimPagina.getTotalDespesasRealizadoMes() + this.mes12TotalFimPagina.getTotalDespesasRealizadoMes());

        //TOTAL INVESTIMENTOS PREVISTOS
        totalPrevistoRealizadoFimPagina.setTotalInvestimentosPrevistoMes(this.mes1TotalFimPagina.getTotalInvestimentosPrevistoMes());
        totalPrevistoRealizadoFimPagina.setTotalInvestimentosPrevistoMes(this.totalPrevistoRealizadoFimPagina.getTotalInvestimentosPrevistoMes() + this.mes2TotalFimPagina.getTotalInvestimentosPrevistoMes());
        totalPrevistoRealizadoFimPagina.setTotalInvestimentosPrevistoMes(this.totalPrevistoRealizadoFimPagina.getTotalInvestimentosPrevistoMes() + this.mes3TotalFimPagina.getTotalInvestimentosPrevistoMes());
        totalPrevistoRealizadoFimPagina.setTotalInvestimentosPrevistoMes(this.totalPrevistoRealizadoFimPagina.getTotalInvestimentosPrevistoMes() + this.mes4TotalFimPagina.getTotalInvestimentosPrevistoMes());
        totalPrevistoRealizadoFimPagina.setTotalInvestimentosPrevistoMes(this.totalPrevistoRealizadoFimPagina.getTotalInvestimentosPrevistoMes() + this.mes5TotalFimPagina.getTotalInvestimentosPrevistoMes());
        totalPrevistoRealizadoFimPagina.setTotalInvestimentosPrevistoMes(this.totalPrevistoRealizadoFimPagina.getTotalInvestimentosPrevistoMes() + this.mes6TotalFimPagina.getTotalInvestimentosPrevistoMes());
        totalPrevistoRealizadoFimPagina.setTotalInvestimentosPrevistoMes(this.totalPrevistoRealizadoFimPagina.getTotalInvestimentosPrevistoMes() + this.mes7TotalFimPagina.getTotalInvestimentosPrevistoMes());
        totalPrevistoRealizadoFimPagina.setTotalInvestimentosPrevistoMes(this.totalPrevistoRealizadoFimPagina.getTotalInvestimentosPrevistoMes() + this.mes8TotalFimPagina.getTotalInvestimentosPrevistoMes());
        totalPrevistoRealizadoFimPagina.setTotalInvestimentosPrevistoMes(this.totalPrevistoRealizadoFimPagina.getTotalInvestimentosPrevistoMes() + this.mes9TotalFimPagina.getTotalInvestimentosPrevistoMes());
        totalPrevistoRealizadoFimPagina.setTotalInvestimentosPrevistoMes(this.totalPrevistoRealizadoFimPagina.getTotalInvestimentosPrevistoMes() + this.mes10TotalFimPagina.getTotalInvestimentosPrevistoMes());
        totalPrevistoRealizadoFimPagina.setTotalInvestimentosPrevistoMes(this.totalPrevistoRealizadoFimPagina.getTotalInvestimentosPrevistoMes() + this.mes11TotalFimPagina.getTotalInvestimentosPrevistoMes());
        totalPrevistoRealizadoFimPagina.setTotalInvestimentosPrevistoMes(this.totalPrevistoRealizadoFimPagina.getTotalInvestimentosPrevistoMes() + this.mes12TotalFimPagina.getTotalInvestimentosPrevistoMes());

        //TOTAL INVESTIMENTOS REALIZADOS
        totalPrevistoRealizadoFimPagina.setTotalInvestimentosRealizadoMes(this.mes1TotalFimPagina.getTotalInvestimentosRealizadoMes());
        totalPrevistoRealizadoFimPagina.setTotalInvestimentosRealizadoMes(this.totalPrevistoRealizadoFimPagina.getTotalInvestimentosRealizadoMes() + this.mes2TotalFimPagina.getTotalInvestimentosRealizadoMes());
        totalPrevistoRealizadoFimPagina.setTotalInvestimentosRealizadoMes(this.totalPrevistoRealizadoFimPagina.getTotalInvestimentosRealizadoMes() + this.mes3TotalFimPagina.getTotalInvestimentosRealizadoMes());
        totalPrevistoRealizadoFimPagina.setTotalInvestimentosRealizadoMes(this.totalPrevistoRealizadoFimPagina.getTotalInvestimentosRealizadoMes() + this.mes4TotalFimPagina.getTotalInvestimentosRealizadoMes());
        totalPrevistoRealizadoFimPagina.setTotalInvestimentosRealizadoMes(this.totalPrevistoRealizadoFimPagina.getTotalInvestimentosRealizadoMes() + this.mes5TotalFimPagina.getTotalInvestimentosRealizadoMes());
        totalPrevistoRealizadoFimPagina.setTotalInvestimentosRealizadoMes(this.totalPrevistoRealizadoFimPagina.getTotalInvestimentosRealizadoMes() + this.mes6TotalFimPagina.getTotalInvestimentosRealizadoMes());
        totalPrevistoRealizadoFimPagina.setTotalInvestimentosRealizadoMes(this.totalPrevistoRealizadoFimPagina.getTotalInvestimentosRealizadoMes() + this.mes7TotalFimPagina.getTotalInvestimentosRealizadoMes());
        totalPrevistoRealizadoFimPagina.setTotalInvestimentosRealizadoMes(this.totalPrevistoRealizadoFimPagina.getTotalInvestimentosRealizadoMes() + this.mes8TotalFimPagina.getTotalInvestimentosRealizadoMes());
        totalPrevistoRealizadoFimPagina.setTotalInvestimentosRealizadoMes(this.totalPrevistoRealizadoFimPagina.getTotalInvestimentosRealizadoMes() + this.mes9TotalFimPagina.getTotalInvestimentosRealizadoMes());
        totalPrevistoRealizadoFimPagina.setTotalInvestimentosRealizadoMes(this.totalPrevistoRealizadoFimPagina.getTotalInvestimentosRealizadoMes() + this.mes10TotalFimPagina.getTotalInvestimentosRealizadoMes());
        totalPrevistoRealizadoFimPagina.setTotalInvestimentosRealizadoMes(this.totalPrevistoRealizadoFimPagina.getTotalInvestimentosRealizadoMes() + this.mes11TotalFimPagina.getTotalInvestimentosRealizadoMes());
        totalPrevistoRealizadoFimPagina.setTotalInvestimentosRealizadoMes(this.totalPrevistoRealizadoFimPagina.getTotalInvestimentosRealizadoMes() + this.mes12TotalFimPagina.getTotalInvestimentosRealizadoMes());

        // TOTAL INVESTIMENTOS DESPESAS PREVISTAS
        totalPrevistoRealizadoFimPagina.setTotalInvestimentosDespesasPrevistoMes(this.mes1TotalFimPagina.getTotalInvestimentosDespesasPrevistoMes());
        totalPrevistoRealizadoFimPagina.setTotalInvestimentosDespesasPrevistoMes(this.totalPrevistoRealizadoFimPagina.getTotalInvestimentosDespesasPrevistoMes() + this.mes2TotalFimPagina.getTotalInvestimentosDespesasPrevistoMes());
        totalPrevistoRealizadoFimPagina.setTotalInvestimentosDespesasPrevistoMes(this.totalPrevistoRealizadoFimPagina.getTotalInvestimentosDespesasPrevistoMes() + this.mes3TotalFimPagina.getTotalInvestimentosDespesasPrevistoMes());
        totalPrevistoRealizadoFimPagina.setTotalInvestimentosDespesasPrevistoMes(this.totalPrevistoRealizadoFimPagina.getTotalInvestimentosDespesasPrevistoMes() + this.mes4TotalFimPagina.getTotalInvestimentosDespesasPrevistoMes());
        totalPrevistoRealizadoFimPagina.setTotalInvestimentosDespesasPrevistoMes(this.totalPrevistoRealizadoFimPagina.getTotalInvestimentosDespesasPrevistoMes() + this.mes5TotalFimPagina.getTotalInvestimentosDespesasPrevistoMes());
        totalPrevistoRealizadoFimPagina.setTotalInvestimentosDespesasPrevistoMes(this.totalPrevistoRealizadoFimPagina.getTotalInvestimentosDespesasPrevistoMes() + this.mes6TotalFimPagina.getTotalInvestimentosDespesasPrevistoMes());
        totalPrevistoRealizadoFimPagina.setTotalInvestimentosDespesasPrevistoMes(this.totalPrevistoRealizadoFimPagina.getTotalInvestimentosDespesasPrevistoMes() + this.mes7TotalFimPagina.getTotalInvestimentosDespesasPrevistoMes());
        totalPrevistoRealizadoFimPagina.setTotalInvestimentosDespesasPrevistoMes(this.totalPrevistoRealizadoFimPagina.getTotalInvestimentosDespesasPrevistoMes() + this.mes8TotalFimPagina.getTotalInvestimentosDespesasPrevistoMes());
        totalPrevistoRealizadoFimPagina.setTotalInvestimentosDespesasPrevistoMes(this.totalPrevistoRealizadoFimPagina.getTotalInvestimentosDespesasPrevistoMes() + this.mes9TotalFimPagina.getTotalInvestimentosDespesasPrevistoMes());
        totalPrevistoRealizadoFimPagina.setTotalInvestimentosDespesasPrevistoMes(this.totalPrevistoRealizadoFimPagina.getTotalInvestimentosDespesasPrevistoMes() + this.mes10TotalFimPagina.getTotalInvestimentosDespesasPrevistoMes());
        totalPrevistoRealizadoFimPagina.setTotalInvestimentosDespesasPrevistoMes(this.totalPrevistoRealizadoFimPagina.getTotalInvestimentosDespesasPrevistoMes() + this.mes11TotalFimPagina.getTotalInvestimentosDespesasPrevistoMes());
        totalPrevistoRealizadoFimPagina.setTotalInvestimentosDespesasPrevistoMes(this.totalPrevistoRealizadoFimPagina.getTotalInvestimentosDespesasPrevistoMes() + this.mes12TotalFimPagina.getTotalInvestimentosDespesasPrevistoMes());

        // TOTAL INVESTIMENTOS DESPESAS REALIZADAS
        totalPrevistoRealizadoFimPagina.setTotalInvestimentosDespesasRealizadoMes(this.mes1TotalFimPagina.getTotalInvestimentosDespesasRealizadoMes());
        totalPrevistoRealizadoFimPagina.setTotalInvestimentosDespesasRealizadoMes(this.totalPrevistoRealizadoFimPagina.getTotalInvestimentosDespesasRealizadoMes() + this.mes2TotalFimPagina.getTotalInvestimentosDespesasRealizadoMes());
        totalPrevistoRealizadoFimPagina.setTotalInvestimentosDespesasRealizadoMes(this.totalPrevistoRealizadoFimPagina.getTotalInvestimentosDespesasRealizadoMes() + this.mes3TotalFimPagina.getTotalInvestimentosDespesasRealizadoMes());
        totalPrevistoRealizadoFimPagina.setTotalInvestimentosDespesasRealizadoMes(this.totalPrevistoRealizadoFimPagina.getTotalInvestimentosDespesasRealizadoMes() + this.mes4TotalFimPagina.getTotalInvestimentosDespesasRealizadoMes());
        totalPrevistoRealizadoFimPagina.setTotalInvestimentosDespesasRealizadoMes(this.totalPrevistoRealizadoFimPagina.getTotalInvestimentosDespesasRealizadoMes() + this.mes5TotalFimPagina.getTotalInvestimentosDespesasRealizadoMes());
        totalPrevistoRealizadoFimPagina.setTotalInvestimentosDespesasRealizadoMes(this.totalPrevistoRealizadoFimPagina.getTotalInvestimentosDespesasRealizadoMes() + this.mes6TotalFimPagina.getTotalInvestimentosDespesasRealizadoMes());
        totalPrevistoRealizadoFimPagina.setTotalInvestimentosDespesasRealizadoMes(this.totalPrevistoRealizadoFimPagina.getTotalInvestimentosDespesasRealizadoMes() + this.mes7TotalFimPagina.getTotalInvestimentosDespesasRealizadoMes());
        totalPrevistoRealizadoFimPagina.setTotalInvestimentosDespesasRealizadoMes(this.totalPrevistoRealizadoFimPagina.getTotalInvestimentosDespesasRealizadoMes() + this.mes8TotalFimPagina.getTotalInvestimentosDespesasRealizadoMes());
        totalPrevistoRealizadoFimPagina.setTotalInvestimentosDespesasRealizadoMes(this.totalPrevistoRealizadoFimPagina.getTotalInvestimentosDespesasRealizadoMes() + this.mes9TotalFimPagina.getTotalInvestimentosDespesasRealizadoMes());
        totalPrevistoRealizadoFimPagina.setTotalInvestimentosDespesasRealizadoMes(this.totalPrevistoRealizadoFimPagina.getTotalInvestimentosDespesasRealizadoMes() + this.mes10TotalFimPagina.getTotalInvestimentosDespesasRealizadoMes());
        totalPrevistoRealizadoFimPagina.setTotalInvestimentosDespesasRealizadoMes(this.totalPrevistoRealizadoFimPagina.getTotalInvestimentosDespesasRealizadoMes() + this.mes11TotalFimPagina.getTotalInvestimentosDespesasRealizadoMes());
        totalPrevistoRealizadoFimPagina.setTotalInvestimentosDespesasRealizadoMes(this.totalPrevistoRealizadoFimPagina.getTotalInvestimentosDespesasRealizadoMes() + this.mes12TotalFimPagina.getTotalInvestimentosDespesasRealizadoMes());

        // TOTAL SEM INVESTIMENTO PREVISTO
        totalPrevistoRealizadoFimPagina.setTotalSemInvestimentoPrevistoMes(this.mes1TotalFimPagina.getTotalSemInvestimentoPrevistoMes());
        totalPrevistoRealizadoFimPagina.setTotalSemInvestimentoPrevistoMes(this.totalPrevistoRealizadoFimPagina.getTotalSemInvestimentoPrevistoMes() + this.mes2TotalFimPagina.getTotalSemInvestimentoPrevistoMes());
        totalPrevistoRealizadoFimPagina.setTotalSemInvestimentoPrevistoMes(this.totalPrevistoRealizadoFimPagina.getTotalSemInvestimentoPrevistoMes() + this.mes3TotalFimPagina.getTotalSemInvestimentoPrevistoMes());
        totalPrevistoRealizadoFimPagina.setTotalSemInvestimentoPrevistoMes(this.totalPrevistoRealizadoFimPagina.getTotalSemInvestimentoPrevistoMes() + this.mes4TotalFimPagina.getTotalSemInvestimentoPrevistoMes());
        totalPrevistoRealizadoFimPagina.setTotalSemInvestimentoPrevistoMes(this.totalPrevistoRealizadoFimPagina.getTotalSemInvestimentoPrevistoMes() + this.mes5TotalFimPagina.getTotalSemInvestimentoPrevistoMes());
        totalPrevistoRealizadoFimPagina.setTotalSemInvestimentoPrevistoMes(this.totalPrevistoRealizadoFimPagina.getTotalSemInvestimentoPrevistoMes() + this.mes6TotalFimPagina.getTotalSemInvestimentoPrevistoMes());
        totalPrevistoRealizadoFimPagina.setTotalSemInvestimentoPrevistoMes(this.totalPrevistoRealizadoFimPagina.getTotalSemInvestimentoPrevistoMes() + this.mes7TotalFimPagina.getTotalSemInvestimentoPrevistoMes());
        totalPrevistoRealizadoFimPagina.setTotalSemInvestimentoPrevistoMes(this.totalPrevistoRealizadoFimPagina.getTotalSemInvestimentoPrevistoMes() + this.mes8TotalFimPagina.getTotalSemInvestimentoPrevistoMes());
        totalPrevistoRealizadoFimPagina.setTotalSemInvestimentoPrevistoMes(this.totalPrevistoRealizadoFimPagina.getTotalSemInvestimentoPrevistoMes() + this.mes9TotalFimPagina.getTotalSemInvestimentoPrevistoMes());
        totalPrevistoRealizadoFimPagina.setTotalSemInvestimentoPrevistoMes(this.totalPrevistoRealizadoFimPagina.getTotalSemInvestimentoPrevistoMes() + this.mes10TotalFimPagina.getTotalSemInvestimentoPrevistoMes());
        totalPrevistoRealizadoFimPagina.setTotalSemInvestimentoPrevistoMes(this.totalPrevistoRealizadoFimPagina.getTotalSemInvestimentoPrevistoMes() + this.mes11TotalFimPagina.getTotalSemInvestimentoPrevistoMes());
        totalPrevistoRealizadoFimPagina.setTotalSemInvestimentoPrevistoMes(this.totalPrevistoRealizadoFimPagina.getTotalSemInvestimentoPrevistoMes() + this.mes12TotalFimPagina.getTotalSemInvestimentoPrevistoMes());

        // TOTAL SEM INVESTIMENTO REALIZADO
        totalPrevistoRealizadoFimPagina.setTotalSemInvestimentoRealizadoMes(this.mes1TotalFimPagina.getTotalSemInvestimentoRealizadoMes());
        totalPrevistoRealizadoFimPagina.setTotalSemInvestimentoRealizadoMes(this.totalPrevistoRealizadoFimPagina.getTotalSemInvestimentoRealizadoMes() + this.mes2TotalFimPagina.getTotalSemInvestimentoRealizadoMes());
        totalPrevistoRealizadoFimPagina.setTotalSemInvestimentoRealizadoMes(this.totalPrevistoRealizadoFimPagina.getTotalSemInvestimentoRealizadoMes() + this.mes3TotalFimPagina.getTotalSemInvestimentoRealizadoMes());
        totalPrevistoRealizadoFimPagina.setTotalSemInvestimentoRealizadoMes(this.totalPrevistoRealizadoFimPagina.getTotalSemInvestimentoRealizadoMes() + this.mes4TotalFimPagina.getTotalSemInvestimentoRealizadoMes());
        totalPrevistoRealizadoFimPagina.setTotalSemInvestimentoRealizadoMes(this.totalPrevistoRealizadoFimPagina.getTotalSemInvestimentoRealizadoMes() + this.mes5TotalFimPagina.getTotalSemInvestimentoRealizadoMes());
        totalPrevistoRealizadoFimPagina.setTotalSemInvestimentoRealizadoMes(this.totalPrevistoRealizadoFimPagina.getTotalSemInvestimentoRealizadoMes() + this.mes6TotalFimPagina.getTotalSemInvestimentoRealizadoMes());
        totalPrevistoRealizadoFimPagina.setTotalSemInvestimentoRealizadoMes(this.totalPrevistoRealizadoFimPagina.getTotalSemInvestimentoRealizadoMes() + this.mes7TotalFimPagina.getTotalSemInvestimentoRealizadoMes());
        totalPrevistoRealizadoFimPagina.setTotalSemInvestimentoRealizadoMes(this.totalPrevistoRealizadoFimPagina.getTotalSemInvestimentoRealizadoMes() + this.mes8TotalFimPagina.getTotalSemInvestimentoRealizadoMes());
        totalPrevistoRealizadoFimPagina.setTotalSemInvestimentoRealizadoMes(this.totalPrevistoRealizadoFimPagina.getTotalSemInvestimentoRealizadoMes() + this.mes9TotalFimPagina.getTotalSemInvestimentoRealizadoMes());
        totalPrevistoRealizadoFimPagina.setTotalSemInvestimentoRealizadoMes(this.totalPrevistoRealizadoFimPagina.getTotalSemInvestimentoRealizadoMes() + this.mes10TotalFimPagina.getTotalSemInvestimentoRealizadoMes());
        totalPrevistoRealizadoFimPagina.setTotalSemInvestimentoRealizadoMes(this.totalPrevistoRealizadoFimPagina.getTotalSemInvestimentoRealizadoMes() + this.mes11TotalFimPagina.getTotalSemInvestimentoRealizadoMes());
        totalPrevistoRealizadoFimPagina.setTotalSemInvestimentoRealizadoMes(this.totalPrevistoRealizadoFimPagina.getTotalSemInvestimentoRealizadoMes() + this.mes12TotalFimPagina.getTotalSemInvestimentoRealizadoMes());

        // TOTAL COM INVESTIMENTO PREVISTO
        totalPrevistoRealizadoFimPagina.setTotalComInvestimentoPrevistoMes(this.mes1TotalFimPagina.getTotalComInvestimentoPrevistoMes());
        totalPrevistoRealizadoFimPagina.setTotalComInvestimentoPrevistoMes(this.totalPrevistoRealizadoFimPagina.getTotalComInvestimentoPrevistoMes() + this.mes2TotalFimPagina.getTotalComInvestimentoPrevistoMes());
        totalPrevistoRealizadoFimPagina.setTotalComInvestimentoPrevistoMes(this.totalPrevistoRealizadoFimPagina.getTotalComInvestimentoPrevistoMes() + this.mes3TotalFimPagina.getTotalComInvestimentoPrevistoMes());
        totalPrevistoRealizadoFimPagina.setTotalComInvestimentoPrevistoMes(this.totalPrevistoRealizadoFimPagina.getTotalComInvestimentoPrevistoMes() + this.mes4TotalFimPagina.getTotalComInvestimentoPrevistoMes());
        totalPrevistoRealizadoFimPagina.setTotalComInvestimentoPrevistoMes(this.totalPrevistoRealizadoFimPagina.getTotalComInvestimentoPrevistoMes() + this.mes5TotalFimPagina.getTotalComInvestimentoPrevistoMes());
        totalPrevistoRealizadoFimPagina.setTotalComInvestimentoPrevistoMes(this.totalPrevistoRealizadoFimPagina.getTotalComInvestimentoPrevistoMes() + this.mes6TotalFimPagina.getTotalComInvestimentoPrevistoMes());
        totalPrevistoRealizadoFimPagina.setTotalComInvestimentoPrevistoMes(this.totalPrevistoRealizadoFimPagina.getTotalComInvestimentoPrevistoMes() + this.mes7TotalFimPagina.getTotalComInvestimentoPrevistoMes());
        totalPrevistoRealizadoFimPagina.setTotalComInvestimentoPrevistoMes(this.totalPrevistoRealizadoFimPagina.getTotalComInvestimentoPrevistoMes() + this.mes8TotalFimPagina.getTotalComInvestimentoPrevistoMes());
        totalPrevistoRealizadoFimPagina.setTotalComInvestimentoPrevistoMes(this.totalPrevistoRealizadoFimPagina.getTotalComInvestimentoPrevistoMes() + this.mes9TotalFimPagina.getTotalComInvestimentoPrevistoMes());
        totalPrevistoRealizadoFimPagina.setTotalComInvestimentoPrevistoMes(this.totalPrevistoRealizadoFimPagina.getTotalComInvestimentoPrevistoMes() + this.mes10TotalFimPagina.getTotalComInvestimentoPrevistoMes());
        totalPrevistoRealizadoFimPagina.setTotalComInvestimentoPrevistoMes(this.totalPrevistoRealizadoFimPagina.getTotalComInvestimentoPrevistoMes() + this.mes11TotalFimPagina.getTotalComInvestimentoPrevistoMes());
        totalPrevistoRealizadoFimPagina.setTotalComInvestimentoPrevistoMes(this.totalPrevistoRealizadoFimPagina.getTotalComInvestimentoPrevistoMes() + this.mes12TotalFimPagina.getTotalComInvestimentoPrevistoMes());

        // TOTAL COM INVESTIMENTO REALIZADO
        totalPrevistoRealizadoFimPagina.setTotalComInvestimentoRealizadoMes(this.mes1TotalFimPagina.getTotalComInvestimentoRealizadoMes());
        totalPrevistoRealizadoFimPagina.setTotalComInvestimentoRealizadoMes(this.totalPrevistoRealizadoFimPagina.getTotalComInvestimentoRealizadoMes() + this.mes2TotalFimPagina.getTotalComInvestimentoRealizadoMes());
        totalPrevistoRealizadoFimPagina.setTotalComInvestimentoRealizadoMes(this.totalPrevistoRealizadoFimPagina.getTotalComInvestimentoRealizadoMes() + this.mes3TotalFimPagina.getTotalComInvestimentoRealizadoMes());
        totalPrevistoRealizadoFimPagina.setTotalComInvestimentoRealizadoMes(this.totalPrevistoRealizadoFimPagina.getTotalComInvestimentoRealizadoMes() + this.mes4TotalFimPagina.getTotalComInvestimentoRealizadoMes());
        totalPrevistoRealizadoFimPagina.setTotalComInvestimentoRealizadoMes(this.totalPrevistoRealizadoFimPagina.getTotalComInvestimentoRealizadoMes() + this.mes5TotalFimPagina.getTotalComInvestimentoRealizadoMes());
        totalPrevistoRealizadoFimPagina.setTotalComInvestimentoRealizadoMes(this.totalPrevistoRealizadoFimPagina.getTotalComInvestimentoRealizadoMes() + this.mes6TotalFimPagina.getTotalComInvestimentoRealizadoMes());
        totalPrevistoRealizadoFimPagina.setTotalComInvestimentoRealizadoMes(this.totalPrevistoRealizadoFimPagina.getTotalComInvestimentoRealizadoMes() + this.mes7TotalFimPagina.getTotalComInvestimentoRealizadoMes());
        totalPrevistoRealizadoFimPagina.setTotalComInvestimentoRealizadoMes(this.totalPrevistoRealizadoFimPagina.getTotalComInvestimentoRealizadoMes() + this.mes8TotalFimPagina.getTotalComInvestimentoRealizadoMes());
        totalPrevistoRealizadoFimPagina.setTotalComInvestimentoRealizadoMes(this.totalPrevistoRealizadoFimPagina.getTotalComInvestimentoRealizadoMes() + this.mes9TotalFimPagina.getTotalComInvestimentoRealizadoMes());
        totalPrevistoRealizadoFimPagina.setTotalComInvestimentoRealizadoMes(this.totalPrevistoRealizadoFimPagina.getTotalComInvestimentoRealizadoMes() + this.mes10TotalFimPagina.getTotalComInvestimentoRealizadoMes());
        totalPrevistoRealizadoFimPagina.setTotalComInvestimentoRealizadoMes(this.totalPrevistoRealizadoFimPagina.getTotalComInvestimentoRealizadoMes() + this.mes11TotalFimPagina.getTotalComInvestimentoRealizadoMes());
        totalPrevistoRealizadoFimPagina.setTotalComInvestimentoRealizadoMes(this.totalPrevistoRealizadoFimPagina.getTotalComInvestimentoRealizadoMes() + this.mes12TotalFimPagina.getTotalComInvestimentoRealizadoMes());
    }

    public void parametrosTotalPrevistoRealizadoFimPagina(Map<String, Object> params) {
        params.put("totalPrevistoRealizadoFimPaginaReceitasPrevisto", this.totalPrevistoRealizadoFimPagina.getTotalReceitasPrevistoMesString());
        params.put("totalPrevistoRealizadoFimPaginaReceitasRealizado", this.totalPrevistoRealizadoFimPagina.getTotalReceitasRealizadoMesString());
        params.put("totalPrevistoRealizadoFimPaginaDespesasPrevisto", this.totalPrevistoRealizadoFimPagina.getTotalDespesasPrevistoMesString());
        params.put("totalPrevistoRealizadoFimPaginaDespesasRealizado", this.totalPrevistoRealizadoFimPagina.getTotalDespesasRealizadoMesString());
        params.put("totalPrevistoRealizadoFimPaginaInvestimentosPrevisto", this.totalPrevistoRealizadoFimPagina.getTotalInvestimentosPrevistoMesString());
        params.put("totalPrevistoRealizadoFimPaginaInvestimentosRealizado", this.totalPrevistoRealizadoFimPagina.getTotalInvestimentosRealizadoMesString());
        params.put("totalPrevistoRealizadoFimPaginaInvestimentosDespesasPrevisto", this.totalPrevistoRealizadoFimPagina.getTotalInvestimentosDespesasPrevistoMesString());
        params.put("totalPrevistoRealizadoFimPaginaInvestimentosDespesasRealizado", this.totalPrevistoRealizadoFimPagina.getTotalInvestimentosDespesasRealizadoMesString());
        params.put("totalPrevistoRealizadoFimPaginaSemInvestimentosPrevisto", this.totalPrevistoRealizadoFimPagina.getTotalSemInvestimentosPrevistoMesString());
        params.put("totalPrevistoRealizadoFimPaginaSemInvestimentosRealizado", this.totalPrevistoRealizadoFimPagina.getTotalSemInvestimentosRealizadoMesString());
        params.put("totalPrevistoRealizadoFimPaginaComInvestimentosPrevisto", this.totalPrevistoRealizadoFimPagina.getTotalComInvestimentosPrevistoMesString());
        params.put("totalPrevistoRealizadoFimPaginaComInvestimentosRealizado", this.totalPrevistoRealizadoFimPagina.getTotalComInvestimentosRealizadoMesString());
    }

    public void calcularSaldosEVariacaoTotalFimPaginaAddParametros(Map<String, Object> params) {

        Double saldoReceita = calcularSaldo(this.totalPrevistoRealizadoFimPagina.getTotalReceitasPrevistoMes(), this.totalPrevistoRealizadoFimPagina.getTotalReceitasRealizadoes());
        Double saldoDespesa = calcularSaldo(this.totalPrevistoRealizadoFimPagina.getTotalDespesasPrevistoMes(), this.totalPrevistoRealizadoFimPagina.getTotalDespesasRealizadoMes());
        Double saldoInvestimentos = calcularSaldo(this.totalPrevistoRealizadoFimPagina.getTotalInvestimentosPrevistoMes(), this.totalPrevistoRealizadoFimPagina.getTotalInvestimentosRealizadoMes());
        Double saldoInvestimentosDespesas = calcularSaldo(this.totalPrevistoRealizadoFimPagina.getTotalInvestimentosDespesasPrevistoMes(), this.totalPrevistoRealizadoFimPagina.getTotalInvestimentosDespesasRealizadoMes());
        Double saldoSemInvestimentos = calcularSaldo(this.totalPrevistoRealizadoFimPagina.getTotalSemInvestimentoPrevistoMes(), this.totalPrevistoRealizadoFimPagina.getTotalSemInvestimentoRealizadoMes());
        Double saldoComInvestimentos = calcularSaldo(this.totalPrevistoRealizadoFimPagina.getTotalComInvestimentoPrevistoMes(), this.totalPrevistoRealizadoFimPagina.getTotalComInvestimentoRealizadoMes());

        params.put("saldoFimPaginaReceitas", transformarDoubleString(saldoReceita));
        params.put("saldoFimPaginaDespesas", transformarDoubleString(saldoDespesa));
        params.put("saldoFimPaginaInvestimentos", transformarDoubleString(saldoInvestimentos));
        params.put("saldoFimPaginaInvestimentosDespesas", transformarDoubleString(saldoInvestimentosDespesas));
        params.put("saldoFimPaginaSemInvestimentos", transformarDoubleString(saldoSemInvestimentos));
        params.put("saldoFimPaginaComInvestimentos", transformarDoubleString(saldoComInvestimentos));

        params.put("variacaoFimPaginaReceitas", transformarDoubleString(
                        calcularVariacao(this.totalPrevistoRealizadoFimPagina.getTotalReceitasPrevistoMes(), saldoReceita, this.totalPrevistoRealizadoFimPagina.getTotalReceitasRealizadoes())
                ) + "%"
        );
        params.put("variacaoFimPaginaDespesas", transformarDoubleString(
                        calcularVariacao(this.totalPrevistoRealizadoFimPagina.getTotalDespesasPrevistoMes(), saldoDespesa, this.totalPrevistoRealizadoFimPagina.getTotalDespesasRealizadoMes())
                ) + "%"
        );
        params.put("variacaoFimPaginaInvestimentos", transformarDoubleString(
                        calcularVariacao(this.totalPrevistoRealizadoFimPagina.getTotalInvestimentosPrevistoMes(), saldoInvestimentos, this.totalPrevistoRealizadoFimPagina.getTotalInvestimentosRealizadoMes())
                ) + "%"
        );
        params.put("variacaoFimPaginaInvestimentosDespesas", transformarDoubleString(
                        calcularVariacao(this.totalPrevistoRealizadoFimPagina.getTotalInvestimentosDespesasPrevistoMes(), saldoInvestimentosDespesas, this.totalPrevistoRealizadoFimPagina.getTotalInvestimentosDespesasRealizadoMes())
                ) + "%"
        );
        params.put("variacaoFimPaginaSemInvestimentos", transformarDoubleString(
                        calcularVariacao(this.totalPrevistoRealizadoFimPagina.getTotalSemInvestimentoPrevistoMes(), saldoSemInvestimentos, this.totalPrevistoRealizadoFimPagina.getTotalSemInvestimentoRealizadoMes())
                ) + "%"
        );
        params.put("variacaoFimPaginaComInvestimentos", transformarDoubleString(
                        calcularVariacao(this.totalPrevistoRealizadoFimPagina.getTotalComInvestimentoPrevistoMes(), saldoComInvestimentos, this.totalPrevistoRealizadoFimPagina.getTotalComInvestimentoRealizadoMes())
                ) + "%"
        );
    }

    public void parametrosMes1TotalFimPagina(Map<String, Object> params) {
        params.put("mes1TotalFimPaginaReceitasPrevisto", this.getMes1TotalFimPagina().getTotalReceitasPrevistoMesString());
        params.put("mes1TotalFimPaginaReceitasRealizado", this.getMes1TotalFimPagina().getTotalReceitasRealizadoMesString());
        params.put("mes1TotalFimPaginaDespesasPrevisto", this.getMes1TotalFimPagina().getTotalDespesasPrevistoMesString());
        params.put("mes1TotalFimPaginaDespesasRealizado", this.getMes1TotalFimPagina().getTotalDespesasRealizadoMesString());
        params.put("mes1TotalFimPaginaInvestimentosPrevisto", this.getMes1TotalFimPagina().getTotalInvestimentosPrevistoMesString());
        params.put("mes1TotalFimPaginaInvestimentosRealizado", this.getMes1TotalFimPagina().getTotalInvestimentosRealizadoMesString());
        params.put("mes1TotalFimPaginaInvestimentosDespesasPrevisto", this.getMes1TotalFimPagina().getTotalInvestimentosDespesasPrevistoMesString());
        params.put("mes1TotalFimPaginaInvestimentosDespesasRealizado", this.getMes1TotalFimPagina().getTotalInvestimentosDespesasRealizadoMesString());
        params.put("mes1TotalFimPaginaSemInvestimentosPrevisto", this.getMes1TotalFimPagina().getTotalSemInvestimentosPrevistoMesString());
        params.put("mes1TotalFimPaginaSemInvestimentosRealizado", this.getMes1TotalFimPagina().getTotalSemInvestimentosRealizadoMesString());
        params.put("mes1TotalFimPaginaComInvestimentosPrevisto", this.getMes1TotalFimPagina().getTotalComInvestimentosPrevistoMesString());
        params.put("mes1TotalFimPaginaComInvestimentosRealizado", this.getMes1TotalFimPagina().getTotalComInvestimentosRealizadoMesString());
    }

    public void parametrosMes2TotalFimPagina(Map<String, Object> params) {
        if (this.listaOrcamentarioRel.get(0).getMes2().getMesProcessar() != null) {
            params.put("mes2TotalFimPaginaReceitasPrevisto", this.getMes2TotalFimPagina().getTotalReceitasPrevistoMesString());
            params.put("mes2TotalFimPaginaReceitasRealizado", this.getMes2TotalFimPagina().getTotalReceitasRealizadoMesString());
            params.put("mes2TotalFimPaginaDespesasPrevisto", this.getMes2TotalFimPagina().getTotalDespesasPrevistoMesString());
            params.put("mes2TotalFimPaginaDespesasRealizado", this.getMes2TotalFimPagina().getTotalDespesasRealizadoMesString());
            params.put("mes2TotalFimPaginaInvestimentosPrevisto", this.getMes2TotalFimPagina().getTotalInvestimentosPrevistoMesString());
            params.put("mes2TotalFimPaginaInvestimentosRealizado", this.getMes2TotalFimPagina().getTotalInvestimentosRealizadoMesString());
            params.put("mes2TotalFimPaginaInvestimentosDespesasPrevisto", this.getMes2TotalFimPagina().getTotalInvestimentosDespesasPrevistoMesString());
            params.put("mes2TotalFimPaginaInvestimentosDespesasRealizado", this.getMes2TotalFimPagina().getTotalInvestimentosDespesasRealizadoMesString());
            params.put("mes2TotalFimPaginaSemInvestimentosPrevisto", this.getMes2TotalFimPagina().getTotalSemInvestimentosPrevistoMesString());
            params.put("mes2TotalFimPaginaSemInvestimentosRealizado", this.getMes2TotalFimPagina().getTotalSemInvestimentosRealizadoMesString());
            params.put("mes2TotalFimPaginaComInvestimentosPrevisto", this.getMes2TotalFimPagina().getTotalComInvestimentosPrevistoMesString());
            params.put("mes2TotalFimPaginaComInvestimentosRealizado", this.getMes2TotalFimPagina().getTotalComInvestimentosRealizadoMesString());
        }
    }

    public void parametrosMes3TotalFimPagina(Map<String, Object> params) {
        if (this.listaOrcamentarioRel.get(0).getMes2().getMesProcessar() != null) {
            params.put("mes3TotalFimPaginaReceitasPrevisto", this.getMes3TotalFimPagina().getTotalReceitasPrevistoMesString());
            params.put("mes3TotalFimPaginaReceitasRealizado", this.getMes3TotalFimPagina().getTotalReceitasRealizadoMesString());
            params.put("mes3TotalFimPaginaDespesasPrevisto", this.getMes3TotalFimPagina().getTotalDespesasPrevistoMesString());
            params.put("mes3TotalFimPaginaDespesasRealizado", this.getMes3TotalFimPagina().getTotalDespesasRealizadoMesString());
            params.put("mes3TotalFimPaginaInvestimentosPrevisto", this.getMes3TotalFimPagina().getTotalInvestimentosPrevistoMesString());
            params.put("mes3TotalFimPaginaInvestimentosRealizado", this.getMes3TotalFimPagina().getTotalInvestimentosRealizadoMesString());
            params.put("mes3TotalFimPaginaInvestimentosDespesasPrevisto", this.getMes3TotalFimPagina().getTotalInvestimentosDespesasPrevistoMesString());
            params.put("mes3TotalFimPaginaInvestimentosDespesasRealizado", this.getMes3TotalFimPagina().getTotalInvestimentosDespesasRealizadoMesString());
            params.put("mes3TotalFimPaginaSemInvestimentosPrevisto", this.getMes3TotalFimPagina().getTotalSemInvestimentosPrevistoMesString());
            params.put("mes3TotalFimPaginaSemInvestimentosRealizado", this.getMes3TotalFimPagina().getTotalSemInvestimentosRealizadoMesString());
            params.put("mes3TotalFimPaginaComInvestimentosPrevisto", this.getMes3TotalFimPagina().getTotalComInvestimentosPrevistoMesString());
            params.put("mes3TotalFimPaginaComInvestimentosRealizado", this.getMes3TotalFimPagina().getTotalComInvestimentosRealizadoMesString());
        }
    }

    public void parametrosMes4TotalFimPagina(Map<String, Object> params) {
        if (this.listaOrcamentarioRel.get(0).getMes2().getMesProcessar() != null) {
            params.put("mes4TotalFimPaginaReceitasPrevisto", this.getMes4TotalFimPagina().getTotalReceitasPrevistoMesString());
            params.put("mes4TotalFimPaginaReceitasRealizado", this.getMes4TotalFimPagina().getTotalReceitasRealizadoMesString());
            params.put("mes4TotalFimPaginaDespesasPrevisto", this.getMes4TotalFimPagina().getTotalDespesasPrevistoMesString());
            params.put("mes4TotalFimPaginaDespesasRealizado", this.getMes4TotalFimPagina().getTotalDespesasRealizadoMesString());
            params.put("mes4TotalFimPaginaInvestimentosPrevisto", this.getMes4TotalFimPagina().getTotalInvestimentosPrevistoMesString());
            params.put("mes4TotalFimPaginaInvestimentosRealizado", this.getMes4TotalFimPagina().getTotalInvestimentosRealizadoMesString());
            params.put("mes4TotalFimPaginaInvestimentosDespesasPrevisto", this.getMes4TotalFimPagina().getTotalInvestimentosDespesasPrevistoMesString());
            params.put("mes4TotalFimPaginaInvestimentosDespesasRealizado", this.getMes4TotalFimPagina().getTotalInvestimentosDespesasRealizadoMesString());
            params.put("mes4TotalFimPaginaSemInvestimentosPrevisto", this.getMes4TotalFimPagina().getTotalSemInvestimentosPrevistoMesString());
            params.put("mes4TotalFimPaginaSemInvestimentosRealizado", this.getMes4TotalFimPagina().getTotalSemInvestimentosRealizadoMesString());
            params.put("mes4TotalFimPaginaComInvestimentosPrevisto", this.getMes4TotalFimPagina().getTotalComInvestimentosPrevistoMesString());
            params.put("mes4TotalFimPaginaComInvestimentosRealizado", this.getMes4TotalFimPagina().getTotalComInvestimentosRealizadoMesString());
        }
    }

    public void parametrosMes5TotalFimPagina(Map<String, Object> params) {
        if (this.listaOrcamentarioRel.get(0).getMes2().getMesProcessar() != null) {
            params.put("mes5TotalFimPaginaReceitasPrevisto", this.getMes5TotalFimPagina().getTotalReceitasPrevistoMesString());
            params.put("mes5TotalFimPaginaReceitasRealizado", this.getMes5TotalFimPagina().getTotalReceitasRealizadoMesString());
            params.put("mes5TotalFimPaginaDespesasPrevisto", this.getMes5TotalFimPagina().getTotalDespesasPrevistoMesString());
            params.put("mes5TotalFimPaginaDespesasRealizado", this.getMes5TotalFimPagina().getTotalDespesasRealizadoMesString());
            params.put("mes5TotalFimPaginaInvestimentosPrevisto", this.getMes5TotalFimPagina().getTotalInvestimentosPrevistoMesString());
            params.put("mes5TotalFimPaginaInvestimentosRealizado", this.getMes5TotalFimPagina().getTotalInvestimentosRealizadoMesString());
            params.put("mes5TotalFimPaginaInvestimentosDespesasPrevisto", this.getMes5TotalFimPagina().getTotalInvestimentosDespesasPrevistoMesString());
            params.put("mes5TotalFimPaginaInvestimentosDespesasRealizado", this.getMes5TotalFimPagina().getTotalInvestimentosDespesasRealizadoMesString());
            params.put("mes5TotalFimPaginaSemInvestimentosPrevisto", this.getMes5TotalFimPagina().getTotalSemInvestimentosPrevistoMesString());
            params.put("mes5TotalFimPaginaSemInvestimentosRealizado", this.getMes5TotalFimPagina().getTotalSemInvestimentosRealizadoMesString());
            params.put("mes5TotalFimPaginaComInvestimentosPrevisto", this.getMes5TotalFimPagina().getTotalComInvestimentosPrevistoMesString());
            params.put("mes5TotalFimPaginaComInvestimentosRealizado", this.getMes5TotalFimPagina().getTotalComInvestimentosRealizadoMesString());
        }
    }

    public void parametrosMes6TotalFimPagina(Map<String, Object> params) {
        if (this.listaOrcamentarioRel.get(0).getMes2().getMesProcessar() != null) {
            params.put("mes6TotalFimPaginaReceitasPrevisto", this.getMes6TotalFimPagina().getTotalReceitasPrevistoMesString());
            params.put("mes6TotalFimPaginaReceitasRealizado", this.getMes6TotalFimPagina().getTotalReceitasRealizadoMesString());
            params.put("mes6TotalFimPaginaDespesasPrevisto", this.getMes6TotalFimPagina().getTotalDespesasPrevistoMesString());
            params.put("mes6TotalFimPaginaDespesasRealizado", this.getMes6TotalFimPagina().getTotalDespesasRealizadoMesString());
            params.put("mes6TotalFimPaginaInvestimentosPrevisto", this.getMes6TotalFimPagina().getTotalInvestimentosPrevistoMesString());
            params.put("mes6TotalFimPaginaInvestimentosRealizado", this.getMes6TotalFimPagina().getTotalInvestimentosRealizadoMesString());
            params.put("mes6TotalFimPaginaInvestimentosDespesasPrevisto", this.getMes6TotalFimPagina().getTotalInvestimentosDespesasPrevistoMesString());
            params.put("mes6TotalFimPaginaInvestimentosDespesasRealizado", this.getMes6TotalFimPagina().getTotalInvestimentosDespesasRealizadoMesString());
            params.put("mes6TotalFimPaginaSemInvestimentosPrevisto", this.getMes6TotalFimPagina().getTotalSemInvestimentosPrevistoMesString());
            params.put("mes6TotalFimPaginaSemInvestimentosRealizado", this.getMes6TotalFimPagina().getTotalSemInvestimentosRealizadoMesString());
            params.put("mes6TotalFimPaginaComInvestimentosPrevisto", this.getMes6TotalFimPagina().getTotalComInvestimentosPrevistoMesString());
            params.put("mes6TotalFimPaginaComInvestimentosRealizado", this.getMes6TotalFimPagina().getTotalComInvestimentosRealizadoMesString());
        }
    }

    public void parametrosMes7TotalFimPagina(Map<String, Object> params) {
        if (this.listaOrcamentarioRel.get(0).getMes7().getMesProcessar() != null) {
            params.put("mes7TotalFimPaginaReceitasPrevisto", this.getMes7TotalFimPagina().getTotalReceitasPrevistoMesString());
            params.put("mes7TotalFimPaginaReceitasRealizado", this.getMes7TotalFimPagina().getTotalReceitasRealizadoMesString());
            params.put("mes7TotalFimPaginaDespesasPrevisto", this.getMes7TotalFimPagina().getTotalDespesasPrevistoMesString());
            params.put("mes7TotalFimPaginaDespesasRealizado", this.getMes7TotalFimPagina().getTotalDespesasRealizadoMesString());
            params.put("mes7TotalFimPaginaInvestimentosPrevisto", this.getMes7TotalFimPagina().getTotalInvestimentosPrevistoMesString());
            params.put("mes7TotalFimPaginaInvestimentosRealizado", this.getMes7TotalFimPagina().getTotalInvestimentosRealizadoMesString());
            params.put("mes7TotalFimPaginaInvestimentosDespesasPrevisto", this.getMes7TotalFimPagina().getTotalInvestimentosDespesasPrevistoMesString());
            params.put("mes7TotalFimPaginaInvestimentosDespesasRealizado", this.getMes7TotalFimPagina().getTotalInvestimentosDespesasRealizadoMesString());
            params.put("mes7TotalFimPaginaSemInvestimentosPrevisto", this.getMes7TotalFimPagina().getTotalSemInvestimentosPrevistoMesString());
            params.put("mes7TotalFimPaginaSemInvestimentosRealizado", this.getMes7TotalFimPagina().getTotalSemInvestimentosRealizadoMesString());
            params.put("mes7TotalFimPaginaComInvestimentosPrevisto", this.getMes7TotalFimPagina().getTotalComInvestimentosPrevistoMesString());
            params.put("mes7TotalFimPaginaComInvestimentosRealizado", this.getMes7TotalFimPagina().getTotalComInvestimentosRealizadoMesString());
        }
    }

    public void parametrosMes8TotalFimPagina(Map<String, Object> params) {
        if (this.listaOrcamentarioRel.get(0).getMes8().getMesProcessar() != null) {
            params.put("mes8TotalFimPaginaReceitasPrevisto", this.getMes8TotalFimPagina().getTotalReceitasPrevistoMesString());
            params.put("mes8TotalFimPaginaReceitasRealizado", this.getMes8TotalFimPagina().getTotalReceitasRealizadoMesString());
            params.put("mes8TotalFimPaginaDespesasPrevisto", this.getMes8TotalFimPagina().getTotalDespesasPrevistoMesString());
            params.put("mes8TotalFimPaginaDespesasRealizado", this.getMes8TotalFimPagina().getTotalDespesasRealizadoMesString());
            params.put("mes8TotalFimPaginaInvestimentosPrevisto", this.getMes8TotalFimPagina().getTotalInvestimentosPrevistoMesString());
            params.put("mes8TotalFimPaginaInvestimentosRealizado", this.getMes8TotalFimPagina().getTotalInvestimentosRealizadoMesString());
            params.put("mes8TotalFimPaginaInvestimentosDespesasPrevisto", this.getMes8TotalFimPagina().getTotalInvestimentosDespesasPrevistoMesString());
            params.put("mes8TotalFimPaginaInvestimentosDespesasRealizado", this.getMes8TotalFimPagina().getTotalInvestimentosDespesasRealizadoMesString());
            params.put("mes8TotalFimPaginaSemInvestimentosPrevisto", this.getMes8TotalFimPagina().getTotalSemInvestimentosPrevistoMesString());
            params.put("mes8TotalFimPaginaSemInvestimentosRealizado", this.getMes8TotalFimPagina().getTotalSemInvestimentosRealizadoMesString());
            params.put("mes8TotalFimPaginaComInvestimentosPrevisto", this.getMes8TotalFimPagina().getTotalComInvestimentosPrevistoMesString());
            params.put("mes8TotalFimPaginaComInvestimentosRealizado", this.getMes8TotalFimPagina().getTotalComInvestimentosRealizadoMesString());
        }
    }

    public void parametrosMes9TotalFimPagina(Map<String, Object> params) {
        if (this.listaOrcamentarioRel.get(0).getMes9().getMesProcessar() != null) {
            params.put("mes9TotalFimPaginaReceitasPrevisto", this.getMes9TotalFimPagina().getTotalReceitasPrevistoMesString());
            params.put("mes9TotalFimPaginaReceitasRealizado", this.getMes9TotalFimPagina().getTotalReceitasRealizadoMesString());
            params.put("mes9TotalFimPaginaDespesasPrevisto", this.getMes9TotalFimPagina().getTotalDespesasPrevistoMesString());
            params.put("mes9TotalFimPaginaDespesasRealizado", this.getMes9TotalFimPagina().getTotalDespesasRealizadoMesString());
            params.put("mes9TotalFimPaginaInvestimentosPrevisto", this.getMes9TotalFimPagina().getTotalInvestimentosPrevistoMesString());
            params.put("mes9TotalFimPaginaInvestimentosRealizado", this.getMes9TotalFimPagina().getTotalInvestimentosRealizadoMesString());
            params.put("mes9TotalFimPaginaInvestimentosDespesasPrevisto", this.getMes9TotalFimPagina().getTotalInvestimentosDespesasPrevistoMesString());
            params.put("mes9TotalFimPaginaInvestimentosDespesasRealizado", this.getMes9TotalFimPagina().getTotalInvestimentosDespesasRealizadoMesString());
            params.put("mes9TotalFimPaginaSemInvestimentosPrevisto", this.getMes9TotalFimPagina().getTotalSemInvestimentosPrevistoMesString());
            params.put("mes9TotalFimPaginaSemInvestimentosRealizado", this.getMes9TotalFimPagina().getTotalSemInvestimentosRealizadoMesString());
            params.put("mes9TotalFimPaginaComInvestimentosPrevisto", this.getMes9TotalFimPagina().getTotalComInvestimentosPrevistoMesString());
            params.put("mes9TotalFimPaginaComInvestimentosRealizado", this.getMes9TotalFimPagina().getTotalComInvestimentosRealizadoMesString());
        }
    }

    public void parametrosMes10TotalFimPagina(Map<String, Object> params) {
        if (this.listaOrcamentarioRel.get(0).getMes10().getMesProcessar() != null) {
            params.put("mes10TotalFimPaginaReceitasPrevisto", this.getMes10TotalFimPagina().getTotalReceitasPrevistoMesString());
            params.put("mes10TotalFimPaginaReceitasRealizado", this.getMes10TotalFimPagina().getTotalReceitasRealizadoMesString());
            params.put("mes10TotalFimPaginaDespesasPrevisto", this.getMes10TotalFimPagina().getTotalDespesasPrevistoMesString());
            params.put("mes10TotalFimPaginaDespesasRealizado", this.getMes10TotalFimPagina().getTotalDespesasRealizadoMesString());
            params.put("mes10TotalFimPaginaInvestimentosPrevisto", this.getMes10TotalFimPagina().getTotalInvestimentosPrevistoMesString());
            params.put("mes10TotalFimPaginaInvestimentosRealizado", this.getMes10TotalFimPagina().getTotalInvestimentosRealizadoMesString());
            params.put("mes10TotalFimPaginaInvestimentosDespesasPrevisto", this.getMes10TotalFimPagina().getTotalInvestimentosDespesasPrevistoMesString());
            params.put("mes10TotalFimPaginaInvestimentosDespesasRealizado", this.getMes10TotalFimPagina().getTotalInvestimentosDespesasRealizadoMesString());
            params.put("mes10TotalFimPaginaSemInvestimentosPrevisto", this.getMes10TotalFimPagina().getTotalSemInvestimentosPrevistoMesString());
            params.put("mes10TotalFimPaginaSemInvestimentosRealizado", this.getMes10TotalFimPagina().getTotalSemInvestimentosRealizadoMesString());
            params.put("mes10TotalFimPaginaComInvestimentosPrevisto", this.getMes10TotalFimPagina().getTotalComInvestimentosPrevistoMesString());
            params.put("mes10TotalFimPaginaComInvestimentosRealizado", this.getMes10TotalFimPagina().getTotalComInvestimentosRealizadoMesString());
        }
    }

    public void parametrosMes11TotalFimPagina(Map<String, Object> params) {
        if (this.listaOrcamentarioRel.get(0).getMes11().getMesProcessar() != null) {
            params.put("mes11TotalFimPaginaReceitasPrevisto", this.getMes11TotalFimPagina().getTotalReceitasPrevistoMesString());
            params.put("mes11TotalFimPaginaReceitasRealizado", this.getMes11TotalFimPagina().getTotalReceitasRealizadoMesString());
            params.put("mes11TotalFimPaginaDespesasPrevisto", this.getMes11TotalFimPagina().getTotalDespesasPrevistoMesString());
            params.put("mes11TotalFimPaginaDespesasRealizado", this.getMes11TotalFimPagina().getTotalDespesasRealizadoMesString());
            params.put("mes11TotalFimPaginaInvestimentosPrevisto", this.getMes11TotalFimPagina().getTotalInvestimentosPrevistoMesString());
            params.put("mes11TotalFimPaginaInvestimentosRealizado", this.getMes11TotalFimPagina().getTotalInvestimentosRealizadoMesString());
            params.put("mes11TotalFimPaginaInvestimentosDespesasPrevisto", this.getMes11TotalFimPagina().getTotalInvestimentosDespesasPrevistoMesString());
            params.put("mes11TotalFimPaginaInvestimentosDespesasRealizado", this.getMes11TotalFimPagina().getTotalInvestimentosDespesasRealizadoMesString());
            params.put("mes11TotalFimPaginaSemInvestimentosPrevisto", this.getMes11TotalFimPagina().getTotalSemInvestimentosPrevistoMesString());
            params.put("mes11TotalFimPaginaSemInvestimentosRealizado", this.getMes11TotalFimPagina().getTotalSemInvestimentosRealizadoMesString());
            params.put("mes11TotalFimPaginaComInvestimentosPrevisto", this.getMes11TotalFimPagina().getTotalComInvestimentosPrevistoMesString());
            params.put("mes11TotalFimPaginaComInvestimentosRealizado", this.getMes11TotalFimPagina().getTotalComInvestimentosRealizadoMesString());
        }
    }

    public void parametrosMes12TotalFimPagina(Map<String, Object> params) {
        if (this.listaOrcamentarioRel.get(0).getMes12().getMesProcessar() != null) {
            params.put("mes12TotalFimPaginaReceitasPrevisto", this.getMes12TotalFimPagina().getTotalReceitasPrevistoMesString());
            params.put("mes12TotalFimPaginaReceitasRealizado", this.getMes12TotalFimPagina().getTotalReceitasRealizadoMesString());
            params.put("mes12TotalFimPaginaDespesasPrevisto", this.getMes12TotalFimPagina().getTotalDespesasPrevistoMesString());
            params.put("mes12TotalFimPaginaDespesasRealizado", this.getMes12TotalFimPagina().getTotalDespesasRealizadoMesString());
            params.put("mes12TotalFimPaginaInvestimentosPrevisto", this.getMes12TotalFimPagina().getTotalInvestimentosPrevistoMesString());
            params.put("mes12TotalFimPaginaInvestimentosRealizado", this.getMes12TotalFimPagina().getTotalInvestimentosRealizadoMesString());
            params.put("mes12TotalFimPaginaInvestimentosDespesasPrevisto", this.getMes12TotalFimPagina().getTotalInvestimentosDespesasPrevistoMesString());
            params.put("mes12TotalFimPaginaInvestimentosDespesasRealizado", this.getMes12TotalFimPagina().getTotalInvestimentosDespesasRealizadoMesString());
            params.put("mes12TotalFimPaginaSemInvestimentosPrevisto", this.getMes12TotalFimPagina().getTotalSemInvestimentosPrevistoMesString());
            params.put("mes12TotalFimPaginaSemInvestimentosRealizado", this.getMes12TotalFimPagina().getTotalSemInvestimentosRealizadoMesString());
            params.put("mes12TotalFimPaginaComInvestimentosPrevisto", this.getMes12TotalFimPagina().getTotalComInvestimentosPrevistoMesString());
            params.put("mes12TotalFimPaginaComInvestimentosRealizado", this.getMes12TotalFimPagina().getTotalComInvestimentosRealizadoMesString());
        }
    }


    public void gerarDatasConsulta() {
        inicioGeracao = new Date(inicio.getTime());
        fimGeracao = new Date(inicio.getTime());
        inicioGeracao.setDate(1);
        switch (Integer.parseInt(tipoEmissao)) {
            case 1:
                inicioGeracao.setMonth(inicio.getMonth());
                fimGeracao.setMonth(inicio.getMonth());
                fimGeracao = Uteis.obterUltimoDiaMes(inicio);
                break;
            case 3:
                int mesInicial = inicio.getMonth();
                int mesFinal;
                // Determinar o trimestre com base no mês de início
                if (mesInicial <= 2) {
                    // Primeiro trimestre (Jan, Fev, Mar)
                    mesInicial = 0; // Janeiro
                    mesFinal = 2;   // Março
                } else if (mesInicial <= 5) {
                    // Segundo trimestre (Abr, Mai, Jun)
                    mesInicial = 3; // Abril
                    mesFinal = 5;   // Junho
                } else if (mesInicial <= 8) {
                    // Terceiro trimestre (Jul, Ago, Set)
                    mesInicial = 6; // Julho
                    mesFinal = 8;   // Setembro
                } else {
                    // Quarto trimestre (Out, Nov, Dez)
                    mesInicial = 9; // Outubro
                    mesFinal = 11;  // Dezembro
                }                
                inicioGeracao.setMonth(mesInicial);
                fimGeracao.setMonth(mesFinal);
                fimGeracao = Uteis.obterUltimoDiaMes(fimGeracao); 
                fimGeracao = Uteis.obterUltimoDiaMes(fimGeracao);
                break;
            case 6:
                obterSemestre(inicio.getMonth());
                fimGeracao = Uteis.obterUltimoDiaMes(fimGeracao);
                break;
            case 12:
                inicioGeracao.setMonth(0);
                fimGeracao.setMonth(11);
                fimGeracao.setDate(31);
                break;
        }
    }

    public void obterSemestre(Integer mes) {
        switch (mes) {
            case 0: case 1: case 2: case 3: case 4: case 5:
                inicioGeracao.setMonth(0);
                fimGeracao.setMonth(5);
                break;
            case 6: case 7: case 8: case 9: case 10: case 11:
                inicioGeracao.setMonth(6);
                fimGeracao.setMonth(11);
                break;
        }
    }

    public void gerarDadosPrevisao() {

    }

    public void gerarDemonstrativoComThread() {
        try {
            gerarDemonstrativo( true, false);
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    private void gerarDemonstrativo(boolean gerarRelatorioUsandoThread, boolean apresentarDevolucoesRel) {
        ContadorTempo.limparCronometro();
        ContadorTempo.iniciarContagem();
        try {
            // Consultar o nome da empresa.
            if(getEmpresaVO() != null && !UteisValidacao.emptyNumber(getEmpresaVO().getCodigo())){
                EmpresaVO objEmpresa = getFacade().getEmpresa().consultarPorCodigo(this.getEmpresaVO().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                setNomeEmpresa(objEmpresa.getNome());
            }

            // Montar a string do Período do relatório.
            SimpleDateFormat sdf2 = new SimpleDateFormat("dd/MM/yyyy");
            setPeriodoRelatorio(sdf2.format(getInicioGeracao()) + " à " + sdf2.format(getFimGeracao()));

            //Tratar parâmetros para gerar listaDF
            List<Integer> listaVasia = new ArrayList<Integer>();
            Calendar dataInicialRel = Calendario.getInstance();
            Calendar dataFinalRel = Calendario.getInstance();
            dataInicialRel.setTime(getInicioGeracao());
            dataFinalRel.setTime(getFimGeracao());

            //Gerar listaDF e DetalhamentoOrcamentario Realizado
            relatorioDF = new RelatorioDemonstrativoFinanceiro();
            listaDF = relatorioDF.gerarDemonstrativo(TipoRelatorioDF.RECEITA,
                    this.getTipoVisualizacao(),
                    dataInicialRel,
                    dataFinalRel,
                    this.getEmpresaVO().getCodigo(),
                    listaVasia,
                    gerarRelatorioUsandoThread,
                    this.getTipoFonteDadosDF(),
                    getConfFinanceiro().getUsarCentralEventos(),
                    isAgruparValorProdutoMMasModalidades(), "", apresentarDevolucoesRel, null);
            montarMesesListaDetalhamentoOrcamentario();
        } catch (Exception e) {
            setHouveErroNoRelatorio(true);
            setMensagemDetalhada("msg_erro", "Erro ao gerar Demonstrativo Financeiro. Classe do erro: " + e.getClass() + "  MsgErro: " + e.getMessage());
        }
    }

    public void montarMesesListaDetalhamentoOrcamentario() {
        DetalhamentoOrcamentarioRel planoPai = null;

        for (DemonstrativoFinanceiro item1 : listaDF) {
            DetalhamentoOrcamentarioRel obj1 = new DetalhamentoOrcamentarioRel(item1.getCodigoAgrupador());
            obj1.setNomeAgrupador(item1.getCodigoAgrupador() + " - " + item1.getNomeAgrupador());
            obj1.setNivelArvore(item1.getCodigoAgrupador().split("\\.").length);
            obj1.setTipoPlanoContas(item1.getTipoES());
            obj1.seteInvestimento(item1.getInvestimento());

            if (obj1.getNivelArvore() == 1) {
                planoPai = obj1;
            }

            int pos = 0;
            for (TotalizadorMesDF item2 : item1.getListaTotalizadorMeses()) {
                TotalizadorMesRelOrcamentario obj2 = new TotalizadorMesRelOrcamentario();
                obj2.setMesProcessar(item2.getMesProcessar());
                obj2.setTotalRealizadoMes(item2.getTotalNivel());
                obj2.setTotalPrevistoMes(montarPrevisto(obj2.getMesProcessar().getMesAno(), item1.getCodigoAgrupador()));

                // Soma os valores dos filhos ao pai
                if (planoPai != null && obj1.getNivelArvore() > 1) {
                    TotalizadorMesRelOrcamentario mesPai = planoPai.getMes(pos + 1);
                    mesPai.setTotalRealizadoMes(mesPai.getTotalRealizadoMes() + obj2.getTotalRealizadoMes());
                    mesPai.setTotalPrevistoMes(mesPai.getTotalPrevistoMes() + obj2.getTotalPrevistoMes());
                }

                // Para preencher os meses
                obj1.setMes(pos + 1, obj2);
                pos++;

            }
            listaOrcamentarioRel.add(obj1);
        }
    }


    public Double montarPrevisto(String mesAno, String codigoAgrupadorPlanoConta) {
        Double valorRetornar = 0.0;
        try {
            RelatorioOrcamentarioConfig relatorioOrcamentarioConfig = new RelatorioOrcamentarioConfig();
            Integer idPrevisaoConfigurada = relatorioOrcamentarioConfig.obterIdRelatorioOrcamentarioPorMesAnoEmpresa(this.getEmpresaVO().getCodigo(), mesAno);

            if (idPrevisaoConfigurada != 0) {
                PlanoContaTO planoContaTO = getFacade().getPlanoConta().consultarPorCodigoPlano(codigoAgrupadorPlanoConta);
                RelatorioOrcamentarioConfigPrevisao relatorioOrcamentarioPrevisao = new RelatorioOrcamentarioConfigPrevisao();
                if (planoContaTO.getTipoPadrao().getCodigo() == 1) {
                    valorRetornar = relatorioOrcamentarioPrevisao.consultarValorPrevisaoPorIdRelatorioOrcamentarioConfigEIdPlanoConta(idPrevisaoConfigurada, planoContaTO.getCodigo());
                } else {
                    valorRetornar = (relatorioOrcamentarioPrevisao.consultarValorPrevisaoPorIdRelatorioOrcamentarioConfigEIdPlanoConta(idPrevisaoConfigurada, planoContaTO.getCodigo()) * -1);
                }
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        this.getEmpresaVO().getCodigo();
        return valorRetornar;
    }

    public void gerarTotaisSaldoVariacaoListaDetalhamentoOrcamentario() {
        for (DetalhamentoOrcamentarioRel item1: listaOrcamentarioRel) {
            Double totalPrevistoCalc = 0.0;
            Double totalRealizadoCalc = 0.0;
            for (int i = 1; i <= 6 ; i++) {
                if (i == 1) {
                    totalPrevistoCalc = totalPrevistoCalc + item1.getMes1().getTotalPrevistoMes();
                    totalRealizadoCalc = totalRealizadoCalc + item1.getMes1().getTotalRealizadoMes();
                } else if (i == 2) {
                    totalPrevistoCalc = totalPrevistoCalc + item1.getMes2().getTotalPrevistoMes();
                    totalRealizadoCalc = totalRealizadoCalc + item1.getMes2().getTotalRealizadoMes();
                } else if (i == 3) {
                    totalPrevistoCalc = totalPrevistoCalc + item1.getMes3().getTotalPrevistoMes();
                    totalRealizadoCalc = totalRealizadoCalc + item1.getMes3().getTotalRealizadoMes();
                } else if (i == 4) {
                    totalPrevistoCalc = totalPrevistoCalc + item1.getMes4().getTotalPrevistoMes();
                    totalRealizadoCalc = totalRealizadoCalc + item1.getMes4().getTotalRealizadoMes();
                } else if (i == 5) {
                    totalPrevistoCalc = totalPrevistoCalc + item1.getMes5().getTotalPrevistoMes();
                    totalRealizadoCalc = totalRealizadoCalc + item1.getMes5().getTotalRealizadoMes();
                }  else if (i == 6) {
                    totalPrevistoCalc = totalPrevistoCalc + item1.getMes6().getTotalPrevistoMes();
                    totalRealizadoCalc = totalRealizadoCalc + item1.getMes6().getTotalRealizadoMes();
                }
            }
            item1.setTotalPrevisto(totalPrevistoCalc);
            item1.setTotalRealizado(totalRealizadoCalc);

            //Calculo Saldo Final
            item1.setSaldoFinal(calcularSaldo(totalPrevistoCalc, totalRealizadoCalc));

            //Calculo Percentual Variacao
            item1.setPercPretendido(calcularVariacao(item1.getTotalPrevisto(), item1.getSaldoFinal(), item1.getTotalRealizado()));
        }
    }

    public Double calcularSaldo(Double previsto, Double realizado) {
        if (previsto != 0) {
            return realizado - previsto;
        } else {
            return realizado;
        }
    }

    public Double calcularVariacao(Double previsto, Double saldo, Double realizado) {
        if (previsto > 0) {
            return saldo / previsto * 100;
        } else if (previsto < 0) {
            return (saldo / previsto * 100) * -1;
        } else {
            if (realizado == 0 && previsto == 0) {
                return 0.0;
            } else if (realizado > 0) {
                return 100.0;
            } else {
                return -100.0;
            }
        }
    }

    public void gerarTotaisFinalPaginaMes1() {
        for (DetalhamentoOrcamentarioRel item: this.listaOrcamentarioRel){
            if (item.getNivelArvore() == 1){
                if (item.getTipoPlanoContas() == TipoES.ENTRADA){
                    this.mes1TotalFimPagina.setTotalReceitasPrevistoMes(this.mes1TotalFimPagina.getTotalReceitasPrevistoMes() + item.getMes1().getTotalPrevistoMes());
                    this.mes1TotalFimPagina.setTotalReceitasRealizadoes(this.mes1TotalFimPagina.getTotalReceitasRealizadoes() + item.getMes1().getTotalRealizadoMes());
                } else if (item.getTipoPlanoContas() == TipoES.SAIDA){
                    this.mes1TotalFimPagina.setTotalDespesasPrevistoMes(this.mes1TotalFimPagina.getTotalDespesasPrevistoMes() + item.getMes1().getTotalPrevistoMes());
                    this.mes1TotalFimPagina.setTotalDespesasRealizadoMes(this.mes1TotalFimPagina.getTotalDespesasRealizadoMes() + item.getMes1().getTotalRealizadoMes());
                } else if (item.getTipoPlanoContas() == TipoES.INVESTIMENTO){
                    this.mes1TotalFimPagina.setTotalInvestimentosPrevistoMes(this.mes1TotalFimPagina.getTotalInvestimentosPrevistoMes() + item.getMes1().getTotalPrevistoMes());
                    this.mes1TotalFimPagina.setTotalInvestimentosRealizadoMes(this.mes1TotalFimPagina.getTotalInvestimentosRealizadoMes() + item.getMes1().getTotalRealizadoMes());
                }
            }
        }
        this.mes1TotalFimPagina.setTotalInvestimentosDespesasPrevistoMes(this.mes1TotalFimPagina.getTotalDespesasPrevistoMes() + this.mes1TotalFimPagina.getTotalInvestimentosPrevistoMes());
        this.mes1TotalFimPagina.setTotalInvestimentosDespesasRealizadoMes(this.mes1TotalFimPagina.getTotalDespesasRealizadoMes() + this.mes1TotalFimPagina.getTotalInvestimentosRealizadoMes());
        this.mes1TotalFimPagina.setTotalSemInvestimentoPrevistoMes(this.mes1TotalFimPagina.getTotalReceitasPrevistoMes() + this.mes1TotalFimPagina.getTotalDespesasPrevistoMes());
        this.mes1TotalFimPagina.setTotalSemInvestimentoRealizadoMes(this.mes1TotalFimPagina.getTotalReceitasRealizadoes() + this.mes1TotalFimPagina.getTotalDespesasRealizadoMes());
        this.mes1TotalFimPagina.setTotalComInvestimentoPrevistoMes(this.mes1TotalFimPagina.getTotalReceitasPrevistoMes() + this.mes1TotalFimPagina.getTotalInvestimentosDespesasPrevistoMes());
        this.mes1TotalFimPagina.setTotalComInvestimentoRealizadoMes(this.mes1TotalFimPagina.getTotalReceitasRealizadoes() + this.mes1TotalFimPagina.getTotalInvestimentosDespesasRealizadoMes());
    }

    public void gerarTotaisFinalPaginaMes2() {
        if (this.listaOrcamentarioRel.get(0).getMes2().getMesProcessar() != null) {
            for (DetalhamentoOrcamentarioRel item: this.listaOrcamentarioRel){
                if (item.getNivelArvore() == 1){
                    if (item.getTipoPlanoContas() == TipoES.ENTRADA){
                        this.mes2TotalFimPagina.setTotalReceitasPrevistoMes(this.mes2TotalFimPagina.getTotalReceitasPrevistoMes() + item.getMes2().getTotalPrevistoMes());
                        this.mes2TotalFimPagina.setTotalReceitasRealizadoes(this.mes2TotalFimPagina.getTotalReceitasRealizadoes() + item.getMes2().getTotalRealizadoMes());
                    } else if (item.getTipoPlanoContas() == TipoES.SAIDA){
                        this.mes2TotalFimPagina.setTotalDespesasPrevistoMes(this.mes2TotalFimPagina.getTotalDespesasPrevistoMes() + item.getMes2().getTotalPrevistoMes());
                        this.mes2TotalFimPagina.setTotalDespesasRealizadoMes(this.mes2TotalFimPagina.getTotalDespesasRealizadoMes() + item.getMes2().getTotalRealizadoMes());
                    } else if (item.getTipoPlanoContas() == TipoES.INVESTIMENTO){
                        this.mes2TotalFimPagina.setTotalInvestimentosPrevistoMes(this.mes2TotalFimPagina.getTotalInvestimentosPrevistoMes() + item.getMes2().getTotalPrevistoMes());
                        this.mes2TotalFimPagina.setTotalInvestimentosRealizadoMes(this.mes2TotalFimPagina.getTotalInvestimentosRealizadoMes() + item.getMes2().getTotalRealizadoMes());
                    }
                }
            }
            this.mes2TotalFimPagina.setTotalInvestimentosDespesasPrevistoMes(this.mes2TotalFimPagina.getTotalDespesasPrevistoMes() + this.mes2TotalFimPagina.getTotalInvestimentosPrevistoMes());
            this.mes2TotalFimPagina.setTotalInvestimentosDespesasRealizadoMes(this.mes2TotalFimPagina.getTotalDespesasRealizadoMes() + this.mes2TotalFimPagina.getTotalInvestimentosRealizadoMes());
            this.mes2TotalFimPagina.setTotalSemInvestimentoPrevistoMes(this.mes2TotalFimPagina.getTotalReceitasPrevistoMes() + this.mes2TotalFimPagina.getTotalDespesasPrevistoMes());
            this.mes2TotalFimPagina.setTotalSemInvestimentoRealizadoMes(this.mes2TotalFimPagina.getTotalReceitasRealizadoes() + this.mes2TotalFimPagina.getTotalDespesasRealizadoMes());
            this.mes2TotalFimPagina.setTotalComInvestimentoPrevistoMes(this.mes2TotalFimPagina.getTotalReceitasPrevistoMes() + this.mes2TotalFimPagina.getTotalInvestimentosDespesasPrevistoMes());
            this.mes2TotalFimPagina.setTotalComInvestimentoRealizadoMes(this.mes2TotalFimPagina.getTotalReceitasRealizadoes() + this.mes2TotalFimPagina.getTotalInvestimentosDespesasRealizadoMes());
        }
    }

    public void gerarTotaisFinalPaginaMes3() {
        if (this.listaOrcamentarioRel.get(0).getMes3().getMesProcessar() != null) {
            for (DetalhamentoOrcamentarioRel item: this.listaOrcamentarioRel){
                if (item.getNivelArvore() == 1){
                    if (item.getTipoPlanoContas() == TipoES.ENTRADA){
                        this.mes3TotalFimPagina.setTotalReceitasPrevistoMes(this.mes3TotalFimPagina.getTotalReceitasPrevistoMes() + item.getMes3().getTotalPrevistoMes());
                        this.mes3TotalFimPagina.setTotalReceitasRealizadoes(this.mes3TotalFimPagina.getTotalReceitasRealizadoes() + item.getMes3().getTotalRealizadoMes());
                    } else if (item.getTipoPlanoContas() == TipoES.SAIDA){
                        this.mes3TotalFimPagina.setTotalDespesasPrevistoMes(this.mes3TotalFimPagina.getTotalDespesasPrevistoMes() + item.getMes3().getTotalPrevistoMes());
                        this.mes3TotalFimPagina.setTotalDespesasRealizadoMes(this.mes3TotalFimPagina.getTotalDespesasRealizadoMes() + item.getMes3().getTotalRealizadoMes());
                    } else if (item.getTipoPlanoContas() == TipoES.INVESTIMENTO){
                        this.mes3TotalFimPagina.setTotalInvestimentosPrevistoMes(this.mes3TotalFimPagina.getTotalInvestimentosPrevistoMes() + item.getMes3().getTotalPrevistoMes());
                        this.mes3TotalFimPagina.setTotalInvestimentosRealizadoMes(this.mes3TotalFimPagina.getTotalInvestimentosRealizadoMes() + item.getMes3().getTotalRealizadoMes());
                    }
                }
            }
            this.mes3TotalFimPagina.setTotalInvestimentosDespesasPrevistoMes(this.mes3TotalFimPagina.getTotalDespesasPrevistoMes() + this.mes3TotalFimPagina.getTotalInvestimentosPrevistoMes());
            this.mes3TotalFimPagina.setTotalInvestimentosDespesasRealizadoMes(this.mes3TotalFimPagina.getTotalDespesasRealizadoMes() + this.mes3TotalFimPagina.getTotalInvestimentosRealizadoMes());
            this.mes3TotalFimPagina.setTotalSemInvestimentoPrevistoMes(this.mes3TotalFimPagina.getTotalReceitasPrevistoMes() + this.mes3TotalFimPagina.getTotalDespesasPrevistoMes());
            this.mes3TotalFimPagina.setTotalSemInvestimentoRealizadoMes(this.mes3TotalFimPagina.getTotalReceitasRealizadoes() + this.mes3TotalFimPagina.getTotalDespesasRealizadoMes());
            this.mes3TotalFimPagina.setTotalComInvestimentoPrevistoMes(this.mes3TotalFimPagina.getTotalReceitasPrevistoMes() + this.mes3TotalFimPagina.getTotalInvestimentosDespesasPrevistoMes());
            this.mes3TotalFimPagina.setTotalComInvestimentoRealizadoMes(this.mes3TotalFimPagina.getTotalReceitasRealizadoes() + this.mes3TotalFimPagina.getTotalInvestimentosDespesasRealizadoMes());
        }
    }

    public void gerarTotaisFinalPaginaMes4() {
        if (this.listaOrcamentarioRel.get(0).getMes4().getMesProcessar() != null) {
            for (DetalhamentoOrcamentarioRel item: this.listaOrcamentarioRel){
                if (item.getNivelArvore() == 1){
                    if (item.getTipoPlanoContas() == TipoES.ENTRADA){
                        this.mes4TotalFimPagina.setTotalReceitasPrevistoMes(this.mes4TotalFimPagina.getTotalReceitasPrevistoMes() + item.getMes4().getTotalPrevistoMes());
                        this.mes4TotalFimPagina.setTotalReceitasRealizadoes(this.mes4TotalFimPagina.getTotalReceitasRealizadoes() + item.getMes4().getTotalRealizadoMes());
                    } else if (item.getTipoPlanoContas() == TipoES.SAIDA){
                        this.mes4TotalFimPagina.setTotalDespesasPrevistoMes(this.mes4TotalFimPagina.getTotalDespesasPrevistoMes() + item.getMes4().getTotalPrevistoMes());
                        this.mes4TotalFimPagina.setTotalDespesasRealizadoMes(this.mes4TotalFimPagina.getTotalDespesasRealizadoMes() + item.getMes4().getTotalRealizadoMes());
                    } else if (item.getTipoPlanoContas() == TipoES.INVESTIMENTO){
                        this.mes4TotalFimPagina.setTotalInvestimentosPrevistoMes(this.mes4TotalFimPagina.getTotalInvestimentosPrevistoMes() + item.getMes4().getTotalPrevistoMes());
                        this.mes4TotalFimPagina.setTotalInvestimentosRealizadoMes(this.mes4TotalFimPagina.getTotalInvestimentosRealizadoMes() + item.getMes4().getTotalRealizadoMes());
                    }
                }
            }
            this.mes4TotalFimPagina.setTotalInvestimentosDespesasPrevistoMes(this.mes4TotalFimPagina.getTotalDespesasPrevistoMes() + this.mes4TotalFimPagina.getTotalInvestimentosPrevistoMes());
            this.mes4TotalFimPagina.setTotalInvestimentosDespesasRealizadoMes(this.mes4TotalFimPagina.getTotalDespesasRealizadoMes() + this.mes4TotalFimPagina.getTotalInvestimentosRealizadoMes());
            this.mes4TotalFimPagina.setTotalSemInvestimentoPrevistoMes(this.mes4TotalFimPagina.getTotalReceitasPrevistoMes() + this.mes4TotalFimPagina.getTotalDespesasPrevistoMes());
            this.mes4TotalFimPagina.setTotalSemInvestimentoRealizadoMes(this.mes4TotalFimPagina.getTotalReceitasRealizadoes() + this.mes4TotalFimPagina.getTotalDespesasRealizadoMes());
            this.mes4TotalFimPagina.setTotalComInvestimentoPrevistoMes(this.mes4TotalFimPagina.getTotalReceitasPrevistoMes() + this.mes4TotalFimPagina.getTotalInvestimentosDespesasPrevistoMes());
            this.mes4TotalFimPagina.setTotalComInvestimentoRealizadoMes(this.mes4TotalFimPagina.getTotalReceitasRealizadoes() + this.mes4TotalFimPagina.getTotalInvestimentosDespesasRealizadoMes());
        }
    }

    public void gerarTotaisFinalPaginaMes5() {
        if (this.listaOrcamentarioRel.get(0).getMes5().getMesProcessar() != null) {
            for (DetalhamentoOrcamentarioRel item: this.listaOrcamentarioRel){
                if (item.getNivelArvore() == 1){
                    if (item.getTipoPlanoContas() == TipoES.ENTRADA){
                        this.mes5TotalFimPagina.setTotalReceitasPrevistoMes(this.mes5TotalFimPagina.getTotalReceitasPrevistoMes() + item.getMes5().getTotalPrevistoMes());
                        this.mes5TotalFimPagina.setTotalReceitasRealizadoes(this.mes5TotalFimPagina.getTotalReceitasRealizadoes() + item.getMes5().getTotalRealizadoMes());
                    } else if (item.getTipoPlanoContas() == TipoES.SAIDA){
                        this.mes5TotalFimPagina.setTotalDespesasPrevistoMes(this.mes5TotalFimPagina.getTotalDespesasPrevistoMes() + item.getMes5().getTotalPrevistoMes());
                        this.mes5TotalFimPagina.setTotalDespesasRealizadoMes(this.mes5TotalFimPagina.getTotalDespesasRealizadoMes() + item.getMes5().getTotalRealizadoMes());
                    } else if (item.getTipoPlanoContas() == TipoES.INVESTIMENTO){
                        this.mes5TotalFimPagina.setTotalInvestimentosPrevistoMes(this.mes5TotalFimPagina.getTotalInvestimentosPrevistoMes() + item.getMes5().getTotalPrevistoMes());
                        this.mes5TotalFimPagina.setTotalInvestimentosRealizadoMes(this.mes5TotalFimPagina.getTotalInvestimentosRealizadoMes() + item.getMes5().getTotalRealizadoMes());
                    }
                }
            }
            this.mes5TotalFimPagina.setTotalInvestimentosDespesasPrevistoMes(this.mes5TotalFimPagina.getTotalDespesasPrevistoMes() + this.mes5TotalFimPagina.getTotalInvestimentosPrevistoMes());
            this.mes5TotalFimPagina.setTotalInvestimentosDespesasRealizadoMes(this.mes5TotalFimPagina.getTotalDespesasRealizadoMes() + this.mes5TotalFimPagina.getTotalInvestimentosRealizadoMes());
            this.mes5TotalFimPagina.setTotalSemInvestimentoPrevistoMes(this.mes5TotalFimPagina.getTotalReceitasPrevistoMes() + this.mes5TotalFimPagina.getTotalDespesasPrevistoMes());
            this.mes5TotalFimPagina.setTotalSemInvestimentoRealizadoMes(this.mes5TotalFimPagina.getTotalReceitasRealizadoes() + this.mes5TotalFimPagina.getTotalDespesasRealizadoMes());
            this.mes5TotalFimPagina.setTotalComInvestimentoPrevistoMes(this.mes5TotalFimPagina.getTotalReceitasPrevistoMes() + this.mes5TotalFimPagina.getTotalInvestimentosDespesasPrevistoMes());
            this.mes5TotalFimPagina.setTotalComInvestimentoRealizadoMes(this.mes5TotalFimPagina.getTotalReceitasRealizadoes() + this.mes5TotalFimPagina.getTotalInvestimentosDespesasRealizadoMes());
        }
    }

    public void gerarTotaisFinalPaginaMes6() {
        if (this.listaOrcamentarioRel.get(0).getMes6().getMesProcessar() != null) {
            for (DetalhamentoOrcamentarioRel item: this.listaOrcamentarioRel){
                if (item.getNivelArvore() == 1){
                    if (item.getTipoPlanoContas() == TipoES.ENTRADA){
                        this.mes6TotalFimPagina.setTotalReceitasPrevistoMes(this.mes6TotalFimPagina.getTotalReceitasPrevistoMes() + item.getMes6().getTotalPrevistoMes());
                        this.mes6TotalFimPagina.setTotalReceitasRealizadoes(this.mes6TotalFimPagina.getTotalReceitasRealizadoes() + item.getMes6().getTotalRealizadoMes());
                    } else if (item.getTipoPlanoContas() == TipoES.SAIDA){
                        this.mes6TotalFimPagina.setTotalDespesasPrevistoMes(this.mes6TotalFimPagina.getTotalDespesasPrevistoMes() + item.getMes6().getTotalPrevistoMes());
                        this.mes6TotalFimPagina.setTotalDespesasRealizadoMes(this.mes6TotalFimPagina.getTotalDespesasRealizadoMes() + item.getMes6().getTotalRealizadoMes());
                    } else if (item.getTipoPlanoContas() == TipoES.INVESTIMENTO){
                        this.mes6TotalFimPagina.setTotalInvestimentosPrevistoMes(this.mes6TotalFimPagina.getTotalInvestimentosPrevistoMes() + item.getMes6().getTotalPrevistoMes());
                        this.mes6TotalFimPagina.setTotalInvestimentosRealizadoMes(this.mes6TotalFimPagina.getTotalInvestimentosRealizadoMes() + item.getMes6().getTotalRealizadoMes());
                    }
                }
            }
            this.mes6TotalFimPagina.setTotalInvestimentosDespesasPrevistoMes(this.mes6TotalFimPagina.getTotalDespesasPrevistoMes() + this.mes6TotalFimPagina.getTotalInvestimentosPrevistoMes());
            this.mes6TotalFimPagina.setTotalInvestimentosDespesasRealizadoMes(this.mes6TotalFimPagina.getTotalDespesasRealizadoMes() + this.mes6TotalFimPagina.getTotalInvestimentosRealizadoMes());
            this.mes6TotalFimPagina.setTotalSemInvestimentoPrevistoMes(this.mes6TotalFimPagina.getTotalReceitasPrevistoMes() + this.mes6TotalFimPagina.getTotalDespesasPrevistoMes());
            this.mes6TotalFimPagina.setTotalSemInvestimentoRealizadoMes(this.mes6TotalFimPagina.getTotalReceitasRealizadoes() + this.mes6TotalFimPagina.getTotalDespesasRealizadoMes());
            this.mes6TotalFimPagina.setTotalComInvestimentoPrevistoMes(this.mes6TotalFimPagina.getTotalReceitasPrevistoMes() + this.mes6TotalFimPagina.getTotalInvestimentosDespesasPrevistoMes());
            this.mes6TotalFimPagina.setTotalComInvestimentoRealizadoMes(this.mes6TotalFimPagina.getTotalReceitasRealizadoes() + this.mes6TotalFimPagina.getTotalInvestimentosDespesasRealizadoMes());
        }
    }

    public void gerarTotaisFinalPaginaMes7() {
        if (this.listaOrcamentarioRel.get(0).getMes7().getMesProcessar() != null) {
            for (DetalhamentoOrcamentarioRel item: this.listaOrcamentarioRel){
                if (item.getNivelArvore() == 1){
                    if (item.getTipoPlanoContas() == TipoES.ENTRADA){
                        this.mes7TotalFimPagina.setTotalReceitasPrevistoMes(this.mes7TotalFimPagina.getTotalReceitasPrevistoMes() + item.getMes7().getTotalPrevistoMes());
                        this.mes7TotalFimPagina.setTotalReceitasRealizadoes(this.mes7TotalFimPagina.getTotalReceitasRealizadoes() + item.getMes7().getTotalRealizadoMes());
                    } else if (item.getTipoPlanoContas() == TipoES.SAIDA){
                        this.mes7TotalFimPagina.setTotalDespesasPrevistoMes(this.mes7TotalFimPagina.getTotalDespesasPrevistoMes() + item.getMes7().getTotalPrevistoMes());
                        this.mes7TotalFimPagina.setTotalDespesasRealizadoMes(this.mes7TotalFimPagina.getTotalDespesasRealizadoMes() + item.getMes7().getTotalRealizadoMes());
                    } else if (item.getTipoPlanoContas() == TipoES.INVESTIMENTO){
                        this.mes7TotalFimPagina.setTotalInvestimentosPrevistoMes(this.mes7TotalFimPagina.getTotalInvestimentosPrevistoMes() + item.getMes7().getTotalPrevistoMes());
                        this.mes7TotalFimPagina.setTotalInvestimentosRealizadoMes(this.mes7TotalFimPagina.getTotalInvestimentosRealizadoMes() + item.getMes7().getTotalRealizadoMes());
                    }
                }
            }
            this.mes7TotalFimPagina.setTotalInvestimentosDespesasPrevistoMes(this.mes7TotalFimPagina.getTotalDespesasPrevistoMes() + this.mes7TotalFimPagina.getTotalInvestimentosPrevistoMes());
            this.mes7TotalFimPagina.setTotalInvestimentosDespesasRealizadoMes(this.mes7TotalFimPagina.getTotalDespesasRealizadoMes() + this.mes7TotalFimPagina.getTotalInvestimentosRealizadoMes());
            this.mes7TotalFimPagina.setTotalSemInvestimentoPrevistoMes(this.mes7TotalFimPagina.getTotalReceitasPrevistoMes() + this.mes7TotalFimPagina.getTotalDespesasPrevistoMes());
            this.mes7TotalFimPagina.setTotalSemInvestimentoRealizadoMes(this.mes7TotalFimPagina.getTotalReceitasRealizadoes() + this.mes7TotalFimPagina.getTotalDespesasRealizadoMes());
            this.mes7TotalFimPagina.setTotalComInvestimentoPrevistoMes(this.mes7TotalFimPagina.getTotalReceitasPrevistoMes() + this.mes7TotalFimPagina.getTotalInvestimentosDespesasPrevistoMes());
            this.mes7TotalFimPagina.setTotalComInvestimentoRealizadoMes(this.mes7TotalFimPagina.getTotalReceitasRealizadoes() + this.mes7TotalFimPagina.getTotalInvestimentosDespesasRealizadoMes());
        }
    }

    public void gerarTotaisFinalPaginaMes8() {
        if (this.listaOrcamentarioRel.get(0).getMes8().getMesProcessar() != null) {
            for (DetalhamentoOrcamentarioRel item: this.listaOrcamentarioRel){
                if (item.getNivelArvore() == 1){
                    if (item.getTipoPlanoContas() == TipoES.ENTRADA){
                        this.mes8TotalFimPagina.setTotalReceitasPrevistoMes(this.mes8TotalFimPagina.getTotalReceitasPrevistoMes() + item.getMes8().getTotalPrevistoMes());
                        this.mes8TotalFimPagina.setTotalReceitasRealizadoes(this.mes8TotalFimPagina.getTotalReceitasRealizadoes() + item.getMes8().getTotalRealizadoMes());
                    } else if (item.getTipoPlanoContas() == TipoES.SAIDA){
                        this.mes8TotalFimPagina.setTotalDespesasPrevistoMes(this.mes8TotalFimPagina.getTotalDespesasPrevistoMes() + item.getMes8().getTotalPrevistoMes());
                        this.mes8TotalFimPagina.setTotalDespesasRealizadoMes(this.mes8TotalFimPagina.getTotalDespesasRealizadoMes() + item.getMes8().getTotalRealizadoMes());
                    } else if (item.getTipoPlanoContas() == TipoES.INVESTIMENTO){
                        this.mes8TotalFimPagina.setTotalInvestimentosPrevistoMes(this.mes8TotalFimPagina.getTotalInvestimentosPrevistoMes() + item.getMes8().getTotalPrevistoMes());
                        this.mes8TotalFimPagina.setTotalInvestimentosRealizadoMes(this.mes8TotalFimPagina.getTotalInvestimentosRealizadoMes() + item.getMes8().getTotalRealizadoMes());
                    }
                }
            }
            this.mes8TotalFimPagina.setTotalInvestimentosDespesasPrevistoMes(this.mes8TotalFimPagina.getTotalDespesasPrevistoMes() + this.mes8TotalFimPagina.getTotalInvestimentosPrevistoMes());
            this.mes8TotalFimPagina.setTotalInvestimentosDespesasRealizadoMes(this.mes8TotalFimPagina.getTotalDespesasRealizadoMes() + this.mes8TotalFimPagina.getTotalInvestimentosRealizadoMes());
            this.mes8TotalFimPagina.setTotalSemInvestimentoPrevistoMes(this.mes8TotalFimPagina.getTotalReceitasPrevistoMes() + this.mes8TotalFimPagina.getTotalDespesasPrevistoMes());
            this.mes8TotalFimPagina.setTotalSemInvestimentoRealizadoMes(this.mes8TotalFimPagina.getTotalReceitasRealizadoes() + this.mes8TotalFimPagina.getTotalDespesasRealizadoMes());
            this.mes8TotalFimPagina.setTotalComInvestimentoPrevistoMes(this.mes8TotalFimPagina.getTotalReceitasPrevistoMes() + this.mes8TotalFimPagina.getTotalInvestimentosDespesasPrevistoMes());
            this.mes8TotalFimPagina.setTotalComInvestimentoRealizadoMes(this.mes8TotalFimPagina.getTotalReceitasRealizadoes() + this.mes8TotalFimPagina.getTotalInvestimentosDespesasRealizadoMes());
        }
    }

    public void gerarTotaisFinalPaginaMes9() {
        if (this.listaOrcamentarioRel.get(0).getMes9().getMesProcessar() != null) {
            for (DetalhamentoOrcamentarioRel item: this.listaOrcamentarioRel){
                if (item.getNivelArvore() == 1){
                    if (item.getTipoPlanoContas() == TipoES.ENTRADA){
                        this.mes9TotalFimPagina.setTotalReceitasPrevistoMes(this.mes9TotalFimPagina.getTotalReceitasPrevistoMes() + item.getMes9().getTotalPrevistoMes());
                        this.mes9TotalFimPagina.setTotalReceitasRealizadoes(this.mes9TotalFimPagina.getTotalReceitasRealizadoes() + item.getMes9().getTotalRealizadoMes());
                    } else if (item.getTipoPlanoContas() == TipoES.SAIDA){
                        this.mes9TotalFimPagina.setTotalDespesasPrevistoMes(this.mes9TotalFimPagina.getTotalDespesasPrevistoMes() + item.getMes9().getTotalPrevistoMes());
                        this.mes9TotalFimPagina.setTotalDespesasRealizadoMes(this.mes9TotalFimPagina.getTotalDespesasRealizadoMes() + item.getMes9().getTotalRealizadoMes());
                    } else if (item.getTipoPlanoContas() == TipoES.INVESTIMENTO){
                        this.mes9TotalFimPagina.setTotalInvestimentosPrevistoMes(this.mes9TotalFimPagina.getTotalInvestimentosPrevistoMes() + item.getMes9().getTotalPrevistoMes());
                        this.mes9TotalFimPagina.setTotalInvestimentosRealizadoMes(this.mes9TotalFimPagina.getTotalInvestimentosRealizadoMes() + item.getMes9().getTotalRealizadoMes());
                    }
                }
            }
            this.mes9TotalFimPagina.setTotalInvestimentosDespesasPrevistoMes(this.mes9TotalFimPagina.getTotalDespesasPrevistoMes() + this.mes9TotalFimPagina.getTotalInvestimentosPrevistoMes());
            this.mes9TotalFimPagina.setTotalInvestimentosDespesasRealizadoMes(this.mes9TotalFimPagina.getTotalDespesasRealizadoMes() + this.mes9TotalFimPagina.getTotalInvestimentosRealizadoMes());
            this.mes9TotalFimPagina.setTotalSemInvestimentoPrevistoMes(this.mes9TotalFimPagina.getTotalReceitasPrevistoMes() + this.mes9TotalFimPagina.getTotalDespesasPrevistoMes());
            this.mes9TotalFimPagina.setTotalSemInvestimentoRealizadoMes(this.mes9TotalFimPagina.getTotalReceitasRealizadoes() + this.mes9TotalFimPagina.getTotalDespesasRealizadoMes());
            this.mes9TotalFimPagina.setTotalComInvestimentoPrevistoMes(this.mes9TotalFimPagina.getTotalReceitasPrevistoMes() + this.mes9TotalFimPagina.getTotalInvestimentosDespesasPrevistoMes());
            this.mes9TotalFimPagina.setTotalComInvestimentoRealizadoMes(this.mes9TotalFimPagina.getTotalReceitasRealizadoes() + this.mes9TotalFimPagina.getTotalInvestimentosDespesasRealizadoMes());
        }
    }

    public void gerarTotaisFinalPaginaMes10() {
        if (this.listaOrcamentarioRel.get(0).getMes10().getMesProcessar() != null) {
            for (DetalhamentoOrcamentarioRel item: this.listaOrcamentarioRel){
                if (item.getNivelArvore() == 1){
                    if (item.getTipoPlanoContas() == TipoES.ENTRADA){
                        this.mes10TotalFimPagina.setTotalReceitasPrevistoMes(this.mes10TotalFimPagina.getTotalReceitasPrevistoMes() + item.getMes10().getTotalPrevistoMes());
                        this.mes10TotalFimPagina.setTotalReceitasRealizadoes(this.mes10TotalFimPagina.getTotalReceitasRealizadoes() + item.getMes10().getTotalRealizadoMes());
                    } else if (item.getTipoPlanoContas() == TipoES.SAIDA){
                        this.mes10TotalFimPagina.setTotalDespesasPrevistoMes(this.mes10TotalFimPagina.getTotalDespesasPrevistoMes() + item.getMes10().getTotalPrevistoMes());
                        this.mes10TotalFimPagina.setTotalDespesasRealizadoMes(this.mes10TotalFimPagina.getTotalDespesasRealizadoMes() + item.getMes10().getTotalRealizadoMes());
                    } else if (item.getTipoPlanoContas() == TipoES.INVESTIMENTO){
                        this.mes10TotalFimPagina.setTotalInvestimentosPrevistoMes(this.mes10TotalFimPagina.getTotalInvestimentosPrevistoMes() + item.getMes10().getTotalPrevistoMes());
                        this.mes10TotalFimPagina.setTotalInvestimentosRealizadoMes(this.mes10TotalFimPagina.getTotalInvestimentosRealizadoMes() + item.getMes10().getTotalRealizadoMes());
                    }
                }
            }
            this.mes10TotalFimPagina.setTotalInvestimentosDespesasPrevistoMes(this.mes10TotalFimPagina.getTotalDespesasPrevistoMes() + this.mes10TotalFimPagina.getTotalInvestimentosPrevistoMes());
            this.mes10TotalFimPagina.setTotalInvestimentosDespesasRealizadoMes(this.mes10TotalFimPagina.getTotalDespesasRealizadoMes() + this.mes10TotalFimPagina.getTotalInvestimentosRealizadoMes());
            this.mes10TotalFimPagina.setTotalSemInvestimentoPrevistoMes(this.mes10TotalFimPagina.getTotalReceitasPrevistoMes() + this.mes10TotalFimPagina.getTotalDespesasPrevistoMes());
            this.mes10TotalFimPagina.setTotalSemInvestimentoRealizadoMes(this.mes10TotalFimPagina.getTotalReceitasRealizadoes() + this.mes10TotalFimPagina.getTotalDespesasRealizadoMes());
            this.mes10TotalFimPagina.setTotalComInvestimentoPrevistoMes(this.mes10TotalFimPagina.getTotalReceitasPrevistoMes() + this.mes10TotalFimPagina.getTotalInvestimentosDespesasPrevistoMes());
            this.mes10TotalFimPagina.setTotalComInvestimentoRealizadoMes(this.mes10TotalFimPagina.getTotalReceitasRealizadoes() + this.mes10TotalFimPagina.getTotalInvestimentosDespesasRealizadoMes());
        }
    }

    public void gerarTotaisFinalPaginaMes11() {
        if (this.listaOrcamentarioRel.get(0).getMes11().getMesProcessar() != null) {
            for (DetalhamentoOrcamentarioRel item: this.listaOrcamentarioRel){
                if (item.getNivelArvore() == 1){
                    if (item.getTipoPlanoContas() == TipoES.ENTRADA){
                        this.mes11TotalFimPagina.setTotalReceitasPrevistoMes(this.mes11TotalFimPagina.getTotalReceitasPrevistoMes() + item.getMes11().getTotalPrevistoMes());
                        this.mes11TotalFimPagina.setTotalReceitasRealizadoes(this.mes11TotalFimPagina.getTotalReceitasRealizadoes() + item.getMes11().getTotalRealizadoMes());
                    } else if (item.getTipoPlanoContas() == TipoES.SAIDA){
                        this.mes11TotalFimPagina.setTotalDespesasPrevistoMes(this.mes11TotalFimPagina.getTotalDespesasPrevistoMes() + item.getMes11().getTotalPrevistoMes());
                        this.mes11TotalFimPagina.setTotalDespesasRealizadoMes(this.mes11TotalFimPagina.getTotalDespesasRealizadoMes() + item.getMes11().getTotalRealizadoMes());
                    } else if (item.getTipoPlanoContas() == TipoES.INVESTIMENTO){
                        this.mes11TotalFimPagina.setTotalInvestimentosPrevistoMes(this.mes11TotalFimPagina.getTotalInvestimentosPrevistoMes() + item.getMes11().getTotalPrevistoMes());
                        this.mes11TotalFimPagina.setTotalInvestimentosRealizadoMes(this.mes11TotalFimPagina.getTotalInvestimentosRealizadoMes() + item.getMes11().getTotalRealizadoMes());
                    }
                }
            }
            this.mes11TotalFimPagina.setTotalInvestimentosDespesasPrevistoMes(this.mes11TotalFimPagina.getTotalDespesasPrevistoMes() + this.mes11TotalFimPagina.getTotalInvestimentosPrevistoMes());
            this.mes11TotalFimPagina.setTotalInvestimentosDespesasRealizadoMes(this.mes11TotalFimPagina.getTotalDespesasRealizadoMes() + this.mes11TotalFimPagina.getTotalInvestimentosRealizadoMes());
            this.mes11TotalFimPagina.setTotalSemInvestimentoPrevistoMes(this.mes11TotalFimPagina.getTotalReceitasPrevistoMes() + this.mes11TotalFimPagina.getTotalDespesasPrevistoMes());
            this.mes11TotalFimPagina.setTotalSemInvestimentoRealizadoMes(this.mes11TotalFimPagina.getTotalReceitasRealizadoes() + this.mes11TotalFimPagina.getTotalDespesasRealizadoMes());
            this.mes11TotalFimPagina.setTotalComInvestimentoPrevistoMes(this.mes11TotalFimPagina.getTotalReceitasPrevistoMes() + this.mes11TotalFimPagina.getTotalInvestimentosDespesasPrevistoMes());
            this.mes11TotalFimPagina.setTotalComInvestimentoRealizadoMes(this.mes11TotalFimPagina.getTotalReceitasRealizadoes() + this.mes11TotalFimPagina.getTotalInvestimentosDespesasRealizadoMes());
        }
    }

    public void gerarTotaisFinalPaginaMes12() {
        if (this.listaOrcamentarioRel.get(0).getMes12().getMesProcessar() != null) {
            for (DetalhamentoOrcamentarioRel item: this.listaOrcamentarioRel){
                if (item.getNivelArvore() == 1){
                    if (item.getTipoPlanoContas() == TipoES.ENTRADA){
                        this.mes12TotalFimPagina.setTotalReceitasPrevistoMes(this.mes12TotalFimPagina.getTotalReceitasPrevistoMes() + item.getMes12().getTotalPrevistoMes());
                        this.mes12TotalFimPagina.setTotalReceitasRealizadoes(this.mes12TotalFimPagina.getTotalReceitasRealizadoes() + item.getMes12().getTotalRealizadoMes());
                    } else if (item.getTipoPlanoContas() == TipoES.SAIDA){
                        this.mes12TotalFimPagina.setTotalDespesasPrevistoMes(this.mes12TotalFimPagina.getTotalDespesasPrevistoMes() + item.getMes12().getTotalPrevistoMes());
                        this.mes12TotalFimPagina.setTotalDespesasRealizadoMes(this.mes12TotalFimPagina.getTotalDespesasRealizadoMes() + item.getMes12().getTotalRealizadoMes());
                    } else if (item.getTipoPlanoContas() == TipoES.INVESTIMENTO){
                        this.mes12TotalFimPagina.setTotalInvestimentosPrevistoMes(this.mes12TotalFimPagina.getTotalInvestimentosPrevistoMes() + item.getMes12().getTotalPrevistoMes());
                        this.mes12TotalFimPagina.setTotalInvestimentosRealizadoMes(this.mes12TotalFimPagina.getTotalInvestimentosRealizadoMes() + item.getMes12().getTotalRealizadoMes());
                    }
                }
            }
            this.mes12TotalFimPagina.setTotalInvestimentosDespesasPrevistoMes(this.mes12TotalFimPagina.getTotalDespesasPrevistoMes() + this.mes12TotalFimPagina.getTotalInvestimentosPrevistoMes());
            this.mes12TotalFimPagina.setTotalInvestimentosDespesasRealizadoMes(this.mes12TotalFimPagina.getTotalDespesasRealizadoMes() + this.mes12TotalFimPagina.getTotalInvestimentosRealizadoMes());
            this.mes12TotalFimPagina.setTotalSemInvestimentoPrevistoMes(this.mes12TotalFimPagina.getTotalReceitasPrevistoMes() + this.mes12TotalFimPagina.getTotalDespesasPrevistoMes());
            this.mes12TotalFimPagina.setTotalSemInvestimentoRealizadoMes(this.mes12TotalFimPagina.getTotalReceitasRealizadoes() + this.mes12TotalFimPagina.getTotalDespesasRealizadoMes());
            this.mes12TotalFimPagina.setTotalComInvestimentoPrevistoMes(this.mes12TotalFimPagina.getTotalReceitasPrevistoMes() + this.mes12TotalFimPagina.getTotalInvestimentosDespesasPrevistoMes());
            this.mes12TotalFimPagina.setTotalComInvestimentoRealizadoMes(this.mes12TotalFimPagina.getTotalReceitasRealizadoes() + this.mes12TotalFimPagina.getTotalInvestimentosDespesasRealizadoMes());
        }
    }

    public void setInicio(Date inicio) {
        this.inicio = inicio;
    }

    public Date getInicio() {
        return inicio;
    }

    public void setTipoEmissao(String tipoEmissao) {
        this.tipoEmissao = tipoEmissao;
    }

    public String getTipoEmissao() {
        return tipoEmissao;
    }

    public void setInicioGeracao(Date inicioGeracao) {
        this.inicioGeracao = inicioGeracao;
    }

    public Date getInicioGeracao() {
        return inicioGeracao;
    }

    public void setFimGeracao(Date fimGeracao) {
        this.fimGeracao = fimGeracao;
    }

    public Date getFimGeracao() {
        return fimGeracao;
    }

    public void setListaOrcamentarioRel(List<DetalhamentoOrcamentarioRel> listaOrcamentarioRel) {
        this.listaOrcamentarioRel = listaOrcamentarioRel;
    }

    public List<DetalhamentoOrcamentarioRel> getListOrcamentarioRel() {
        return listaOrcamentarioRel;
    }

    public TotalizadorMesRelOrcamentario getMes1TotalFimPagina() {
        return mes1TotalFimPagina;
    }

    public void setMes1TotalFimPagina(TotalizadorMesRelOrcamentario mes1TotalFimPagina) {
        this.mes1TotalFimPagina = mes1TotalFimPagina;
    }

    public TotalizadorMesRelOrcamentario getMes2TotalFimPagina() {
        return mes2TotalFimPagina;
    }

    public void setMes2TotalFimPagina(TotalizadorMesRelOrcamentario mes2TotalFimPagina) {
        this.mes2TotalFimPagina = mes2TotalFimPagina;
    }

    public TotalizadorMesRelOrcamentario getMes3TotalFimPagina() {
        return mes3TotalFimPagina;
    }

    public void setMes3TotalFimPagina(TotalizadorMesRelOrcamentario mes3TotalFimPagina) {
        this.mes3TotalFimPagina = mes3TotalFimPagina;
    }

    public TotalizadorMesRelOrcamentario getMes4TotalFimPagina() {
        return mes4TotalFimPagina;
    }

    public void setMes4TotalFimPagina(TotalizadorMesRelOrcamentario mes4TotalFimPagina) {
        this.mes4TotalFimPagina = mes4TotalFimPagina;
    }

    public TotalizadorMesRelOrcamentario getMes5TotalFimPagina() {
        return mes5TotalFimPagina;
    }

    public void setMes5TotalFimPagina(TotalizadorMesRelOrcamentario mes5TotalFimPagina) {
        this.mes5TotalFimPagina = mes5TotalFimPagina;
    }

    public TotalizadorMesRelOrcamentario getMes6TotalFimPagina() {
        return mes6TotalFimPagina;
    }

    public void setMes6TotalFimPagina(TotalizadorMesRelOrcamentario mes6TotalFimPagina) {
        this.mes6TotalFimPagina = mes6TotalFimPagina;
    }

    public TotalizadorMesRelOrcamentario getMes7TotalFimPagina() {
        return mes7TotalFimPagina;
    }

    public void setMes7TotalFimPagina(TotalizadorMesRelOrcamentario mes7TotalFimPagina) {
        this.mes7TotalFimPagina = mes7TotalFimPagina;
    }

    public TotalizadorMesRelOrcamentario getMes8TotalFimPagina() {
        return mes8TotalFimPagina;
    }

    public void setMes8TotalFimPagina(TotalizadorMesRelOrcamentario mes8TotalFimPagina) {
        this.mes8TotalFimPagina = mes8TotalFimPagina;
    }

    public TotalizadorMesRelOrcamentario getMes9TotalFimPagina() {
        return mes9TotalFimPagina;
    }

    public void setMes9TotalFimPagina(TotalizadorMesRelOrcamentario mes9TotalFimPagina) {
        this.mes9TotalFimPagina = mes9TotalFimPagina;
    }

    public TotalizadorMesRelOrcamentario getMes10TotalFimPagina() {
        return mes10TotalFimPagina;
    }

    public void setMes10TotalFimPagina(TotalizadorMesRelOrcamentario mes10TotalFimPagina) {
        this.mes10TotalFimPagina = mes10TotalFimPagina;
    }

    public TotalizadorMesRelOrcamentario getMes11TotalFimPagina() {
        return mes11TotalFimPagina;
    }

    public void setMes11TotalFimPagina(TotalizadorMesRelOrcamentario mes11TotalFimPagina) {
        this.mes11TotalFimPagina = mes11TotalFimPagina;
    }

    public TotalizadorMesRelOrcamentario getMes12TotalFimPagina() {
        return mes12TotalFimPagina;
    }

    public void setMes12TotalFimPagina(TotalizadorMesRelOrcamentario mes12TotalFimPagina) {
        this.mes12TotalFimPagina = mes12TotalFimPagina;
    }

    public String transformarDoubleString(Double valor) {
        return Formatador.formatarValorMonetarioSemMoedaMantendoSinal(valor);
    }

    public String getUrlRelOrcamentarioExcel() {
        return urlRelOrcamentarioExcel;
    }

    public void setUrlRelOrcamentarioExcel(String urlRelOrcamentarioExcel) {
        this.urlRelOrcamentarioExcel = urlRelOrcamentarioExcel;
    }
}
