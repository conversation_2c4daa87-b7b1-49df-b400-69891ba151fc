package controle.financeiro;

import br.com.pactosolucoes.comuns.util.Formatador;
import br.com.pactosolucoes.comuns.util.JSFUtilities;
import br.com.pactosolucoes.enumeradores.TipoOperacaoLancamento;
import controle.arquitetura.SuperControle;
import controle.arquitetura.security.AutorizacaoFuncionalidadeControle;
import controle.arquitetura.security.AutorizacaoFuncionalidadeListener;
import controle.arquitetura.security.LoginControle;
import controle.basico.clube.MensagemGenericaControle;
import negocio.comuns.arquitetura.LogVO;
import negocio.comuns.arquitetura.UsuarioPerfilAcessoVO;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.financeiro.CartaoCreditoTO;
import negocio.comuns.financeiro.CartaoCreditoVO;
import negocio.comuns.financeiro.ChequeTO;
import negocio.comuns.financeiro.ChequeVO;
import negocio.comuns.financeiro.LoteVO;
import negocio.comuns.financeiro.MovContaVO;
import negocio.comuns.financeiro.enumerador.TipoES;
import negocio.comuns.financeiro.enumerador.TipoFormaPagto;
import negocio.comuns.financeiro.filtros.FiltroGestaoLotesTO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Ordenacao;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.comuns.utilitarias.UtilReflection;

import javax.faces.event.ActionEvent;
import javax.faces.model.SelectItem;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.Date;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

/**
 *
 * <AUTHOR> Lhoji Shiozawa
 */
public class GestaoLotesControle extends SuperControle {

    private String codigo = "";
    private String nome = "";
    private String descricao = "";
    private EmpresaVO empresa = new EmpresaVO();
    private UsuarioVO usuarioResponsavel = null;
    private Date dataInicialLancamento = Calendario.hoje();
    private Date dataFinalLancamento = Calendario.hoje();
    private Date dataInicialDeposito = null;
    private Date dataFinalDeposito = null;
    private Date dataInicialCompensacao = Calendario.hoje();
    private Date dataFinalCompensacao = Calendario.hoje();
    private Date dataInicialLancamentoC = Calendario.hoje();
    private Date dataFinalLancamentoC = Calendario.hoje();
    private List<LoteVO> listaLotes = null;
    private List<ChequeTO> listaCheques = null;
    private List<Integer> chequesComposicao = null;
    private List<Integer> cartoesComposicao = null;
    private List<ChequeTO> listaChequesLote = null;
    private List<CartaoCreditoTO> listaCartoes = null;
    private List<CartaoCreditoTO> listaCartoesLote = null;
    private String escolha = "closed";
    private double totalCheques = 0.0;
    private double totalChequesLote = 0.0;
    private double totalCartoes = 0.0;
    private double totalCartoesLote = 0.0;
    private LoteVO lote = new LoteVO();
    private boolean mostrarCampoEmpresa = false;
    private boolean mostrarModal = false;
    private String origem = "";
    private boolean todosChequesMarcados = false;
    private boolean todosCartoesMarcados = false;
    private String filtros;
    private boolean parametro1 = true;
    private boolean parametro2 = true;
    private boolean parametro3 = false;
    private boolean parametro4 = false;
    private String periodoLancamento = "";
    private String periodoDeposito = "";
    private MovContaVO pagaMovConta = new MovContaVO();
    private double valorTotalDaConsulta = 0;
    private MovContaVO lancamento = new MovContaVO();
    private String observacaoRetiradaLote = "";
    private List<ChequeTO> chequesARetirar = new ArrayList<ChequeTO>();
    private List<CartaoCreditoTO> cartoesARetirar = new ArrayList<CartaoCreditoTO>();
    private boolean permitirAlteracaoDataLancamento = false;
    private Integer nrPaginaRetirarLote = 10;
    private Integer codigoLote = null;
    private String contasQueLotePaga = "";
    private List<MovContaVO> listaMovContaQueLotePaga;
    private String labelContasQueLotePaga;
    private MovContaVO movContaPagaEmConjunto;

    public GestaoLotesControle() throws Exception {
    }

    public void retirarGrupo() {
        try {
            limparMsg();
            setMsgAlert("");
            limparListasRemover();
            boolean algumSelecionado = false;
            for (CartaoCreditoTO cc : listaCartoesLote) {
                if (cc.isCartaoEscolhido()) {
                    algumSelecionado = true;
                    cartoesARetirar.add(cc);
                }
            }
            for (ChequeTO ch : listaChequesLote) {
                if (ch.isChequeEscolhido()) {
                    ch.setLoteAvulso(getFacade().getLote().consultarLoteAvulso(ch.getCodigo()));
                    if (ch.getLoteAvulso() > 0 && lote.getCodigo() != ch.getLoteAvulso()) {
                        throw new Exception("Não é possível excluir o cheque de número " + ch.getNumero()
                                + " de " + ch.getNomePagador() + ", ele está em um lote avulso.");
                    }
                    algumSelecionado = true;
                    chequesARetirar.add(ch);

                    LogVO log = new LogVO();
                    log.setNomeEntidade("LOTE");
                    log.setNomeEntidadeDescricao("Lote");
                    log.setChavePrimaria(lote.getCodigo().toString());
                    log.setDataAlteracao(Calendario.hoje());
                    log.setNomeCampo(ch.getNumero());
                    log.setValorCampoAnterior("Lote: " + lote.getCodigo() + " - " + lote.getDescricao());
                    log.setResponsavelAlteracao(getUsuarioLogado().getNome());
                    log.setUserOAMD(getUsuarioLogado().getUserOamd());
                    log.setOperacao("EXCLUSAO");
                    getFacade().getLog().incluir(log);


                }
            }
            if (!algumSelecionado) {
                montarMsgAlert(getMensagemInternalizacao("msg_finan_nenhum_item_selecionado"));
                return;
            }
            if (!getLote().getAvulso() && getDepositado()) {
                verificarCaixa();
                AutorizacaoFuncionalidadeControle auto = (AutorizacaoFuncionalidadeControle) getControlador(AutorizacaoFuncionalidadeControle.class);
                setMsgAlert("Richfaces.showModalPanel('modalRetirarChequeLote')");
            }
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public boolean getExibirBotoesAcimaCheque() {
        return listaChequesLote != null
                && !listaChequesLote.isEmpty()
                && listaChequesLote.size() > 10;
    }

    public boolean getExibirBotoesAcimaCartao() {
        return listaCartoesLote != null
                && !listaCartoesLote.isEmpty()
                && listaCartoesLote.size() > 10;
    }

    public boolean getExibirDataDeposito() {
        return lote.getTipoOperacao() != null && lote.getTipoOperacao().equals(TipoOperacaoLancamento.DEPOSITO);
    }

    public String abrirEdicaoLote() {
        return "editarLote";
    }

    public void listenerEdicaoLote(ActionEvent evt) {
        Map<String, Object> attrMap = evt.getComponent().getAttributes();
        Integer codLote = Integer.valueOf(attrMap.get("codigoLote").toString());
        Object orig = attrMap.get("origem");
        if(orig != null){
            this.setOrigem(orig.toString());   
        }
        
        LoteVO loteObj = new LoteVO(codLote);
        editarLote(loteObj);
    }

    public void alterar() {
        try {
            getFacade().getLote().alterar(lote, permitirAlteracaoDataLancamento);
            permitirAlteracaoDataLancamento = false;

            if (lote.getTipoOperacao() != null && lote.getTipoOperacao().equals(TipoOperacaoLancamento.DEPOSITO)) {
                for (ChequeTO ch : listaChequesLote) {
                    ch.setDataCompensacao(lote.getDataDeposito());
                }
                for (CartaoCreditoTO ca : listaCartoesLote) {
                    ca.setDataCompensacao(lote.getDataDeposito());
                }
            }
            try {
                registrarLogObjetoVO(lote, lote.getCodigo(), "LOTE", getUsuarioLogado().getColaboradorVO().getPessoa().getCodigo());
            } catch (Exception e) {
                registrarLogErroObjetoVO("LOTE", lote.getCodigo().intValue(), "ERRO AO GERAR LOG DE ALTERAÇÃO DE LOTE", this.getUsuarioLogado().getNome(),this.getUsuarioLogado().getUserOamd());
                e.printStackTrace();
            }
            setMensagemID("msg_lote_alterado_sucesso");
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    /**
     * author: Hellison Oliveira 15/05/2014
     */
    public void realizarConsultaLogObjetoLoteSelecionado() {
        LoginControle loginControle = (LoginControle) context().getExternalContext().getSessionMap().get("LoginControle");
        String nomeClasse = lote.getClass().getSimpleName();
        nomeClasse = nomeClasse.substring(0, nomeClasse.length() - 2);
        loginControle.setNomeClasse(loginControle.getNomeEntidadeCamposLog("prt_" + nomeClasse + "_tituloForm"));
        loginControle.consultarLogObjetoSelecionado(nomeClasse.toUpperCase(), lote.getCodigo(), 0);
    }

    public String editarLancamento() throws Exception {
        MovContaControle movContaControle = (MovContaControle) getControlador(MovContaControle.class.getSimpleName());
        movContaControle.setOrigemConsulta("lote");
        return movContaControle.preparaEdicao(getPagaMovConta());
    }

    public String editarLancamentoPagamentoConjunto() throws Exception {
        MovContaControle movContaControle = (MovContaControle) getControlador(MovContaControle.class.getSimpleName());
        movContaControle.setOrigemConsulta("lote");
        return movContaControle.preparaEdicao(this.movContaPagaEmConjunto);
    }


    public String editarLancamentoLote() throws Exception {
        MovContaControle movContaControle = (MovContaControle) getControlador(MovContaControle.class.getSimpleName());
        movContaControle.setOrigemConsulta("lote");
        return movContaControle.preparaEdicao(getLancamento());
    }

    public String visualizarCaixa() throws Exception {
        try {
            CaixaControle caixaControle = (CaixaControle) getControlador(CaixaControle.class.getSimpleName());
            caixaControle.carregarCaixa(getLancamento().getCaixa());
            return "telaCaixa";
        } catch (Exception e) {
            setMensagemID("msg_erro");
            setMensagemDetalhada(e);
            return "";
        }
    }

    public void novo() throws Exception {
        setPagaMovConta(new MovContaVO());
        limparDados();
        restauraFiltros();
        inicializarEmpresa();
        inicializarUsuarioLogado();
        setMensagemID("msg_entre_dados");
        setMensagemDetalhada("", "");
        setMensagem("");
    }
    public void limparDados() {
        codigo = "";
        descricao = "";
        empresa = new EmpresaVO();
        usuarioResponsavel = null;
        dataInicialDeposito = null;
        dataFinalDeposito = null;
        dataInicialLancamento = Calendario.hoje();
        dataFinalLancamento = Calendario.hoje();
        dataInicialCompensacao = Calendario.hoje();
        dataFinalCompensacao = Calendario.hoje();
        dataInicialLancamentoC = Calendario.hoje();
        dataFinalLancamentoC = Calendario.hoje();
        limparDadosConsultados();
        limparDadosConsulta();
        setPeriodoDeposito("");
        setPeriodoLancamento("HJ");
    }

    /**
     * <AUTHOR> Alcides 14/11/2012
     */
    public void transferirCheques() {
        try {
            setMsgAlert("");
            OperacaoContaControle operacaoContaControle = (OperacaoContaControle) getControlador(OperacaoContaControle.class.getSimpleName());
            setMsgAlert(operacaoContaControle.transferirChequesCartoes(listaChequesLote, null,
                    getFacade().getFinanceiro().getConta().consultarPorLote(getLote().getCodigo())));
        } catch (Exception e) {
            montarMsgAlert(e.getMessage());
        }
    }

    /**
     * <AUTHOR> Alcides 14/11/2012
     */
    public void transferirCartoes() {
        try {
            setMsgAlert("");
            OperacaoContaControle operacaoContaControle = (OperacaoContaControle) getControlador(OperacaoContaControle.class.getSimpleName());
            setMsgAlert(operacaoContaControle.transferirChequesCartoes(null, listaCartoesLote,
                    getFacade().getFinanceiro().getConta().consultarPorLote(getLote().getCodigo())));
        } catch (Exception e) {
            montarMsgAlert(e.getMessage());
        }
    }

    /**
     * <AUTHOR> Alcides 14/11/2012
     */
    public void limparDadosConsultados() {
        listaLotes = new ArrayList<LoteVO>();
        listaChequesLote = new ArrayList<ChequeTO>();
        listaCartoesLote = new ArrayList<CartaoCreditoTO>();
        totalChequesLote = 0.0;
        totalCartoesLote = 0.0;
        lote = new LoteVO();
        escolha = "closed";
    }

    /**
     * <AUTHOR> Alcides 14/11/2012
     */
    public void limparDadosConsulta() {
        listaCheques = new ArrayList<ChequeTO>();
        listaCartoes = new ArrayList<CartaoCreditoTO>();
        totalCheques = 0.0;
        totalCartoes = 0.0;
        todosChequesMarcados = false;
        todosCartoesMarcados = false;

    }

    /**
     * <AUTHOR> Alcides 14/11/2012
     */
    public List<SelectItem> getListaPeriodos() {
        List<SelectItem> objs = new ArrayList<SelectItem>();
        objs.add(new SelectItem("", ""));
        objs.add(new SelectItem("HJ", "Hoje"));
        objs.add(new SelectItem("SE", "Esta Semana"));
        objs.add(new SelectItem("ME", "Mês"));
        objs.add(new SelectItem("SEP", "Semana Passada"));
        objs.add(new SelectItem("MEP", "Mês Passado"));
        return objs;
    }

    public void alterarPeriodoLancamento() throws Exception {
        if (periodoLancamento == null) {
            setDataInicialLancamento(null);
            setDataFinalLancamento(null);
        } else if (periodoLancamento.equals("HJ")) {
            dataInicialLancamento = Calendario.hoje();
            dataFinalLancamento = Calendario.hoje();
        } else if (periodoLancamento.equals("SE")) {
            dataInicialLancamento = Uteis.obterPrimeiroEUltimoDiaSemana(true);
            dataFinalLancamento = Uteis.obterPrimeiroEUltimoDiaSemana(false);
        } else if (periodoLancamento.equals("ME")) {
            dataInicialLancamento = Uteis.obterPrimeiroDiaMes(Calendario.hoje());
            dataFinalLancamento = Uteis.obterUltimoDiaMes(Calendario.hoje());
        } else if (periodoLancamento.equals("SEP")) {
            dataInicialLancamento = Uteis.obterPrimeiroEUltimoDiaSemana(true, Uteis.obterDataAnterior(Calendario.hoje(), 7));
            dataFinalLancamento = Uteis.obterPrimeiroEUltimoDiaSemana(false, Uteis.obterDataAnterior(Calendario.hoje(), 7));
        } else if (periodoLancamento.equals("MEP")) {
            dataInicialLancamento = Uteis.obterPrimeiroDiaMes(Uteis.obterDataAnterior(Calendario.hoje(), 30));
            dataFinalLancamento = Uteis.obterUltimoDiaMes(Uteis.obterDataAnterior(Calendario.hoje(), 30));
        }
    }

    public void alterarPeriodoDeposito() throws Exception {
        if (periodoDeposito == null) {
            setDataInicialDeposito(null);
            setDataFinalDeposito(null);
        } else if (periodoDeposito.equals("HJ")) {
            dataInicialDeposito = Calendario.hoje();
            dataFinalDeposito = Calendario.hoje();
        } else if (periodoDeposito.equals("SE")) {
            dataInicialDeposito = Uteis.obterPrimeiroEUltimoDiaSemana(true);
            dataFinalDeposito = Uteis.obterPrimeiroEUltimoDiaSemana(false);
        } else if (periodoDeposito.equals("ME")) {
            dataInicialDeposito = Uteis.obterPrimeiroDiaMes(Calendario.hoje());
            dataFinalDeposito = Uteis.obterUltimoDiaMes(Calendario.hoje());
        } else if (periodoDeposito.equals("SEP")) {
            dataInicialDeposito = Uteis.obterPrimeiroEUltimoDiaSemana(true, Uteis.obterDataAnterior(Calendario.hoje(), 7));
            dataFinalDeposito = Uteis.obterPrimeiroEUltimoDiaSemana(false, Uteis.obterDataAnterior(Calendario.hoje(), 7));
        } else if (periodoDeposito.equals("MEP")) {
            dataInicialDeposito = Uteis.obterPrimeiroDiaMes(Uteis.obterDataAnterior(Calendario.hoje(), 30));
            dataFinalDeposito = Uteis.obterUltimoDiaMes(Uteis.obterDataAnterior(Calendario.hoje(), 30));
        }
    }

    public void limparFiltros() {
        setNome("");
        setDataInicialDeposito(null);
        setDataFinalDeposito(null);
        setDataInicialLancamento(null);
        setDataFinalLancamento(null);
        limparDadosConsultados();
        todosChequesMarcados = false;
        todosCartoesMarcados = false;
        setCodigo("");
        setDescricao("");
        setPeriodoDeposito("");
        setPeriodoLancamento("");
        setValorTotalDaConsulta(0.0);
    }

    public List<SelectItem> getListaSelectItemEmpresa() {
        List<SelectItem> objs = new ArrayList<SelectItem>();
        try {
            List resultadoConsulta = getFacade().getEmpresa().consultarPorNome("",true, false, Uteis.NIVELMONTARDADOS_TODOS);
            Iterator i = resultadoConsulta.iterator();
            while (i.hasNext()) {
                EmpresaVO obj = (EmpresaVO) i.next();
                objs.add(new SelectItem(obj.getCodigo(), obj.getNome()));
            }
        } catch (Exception e) {
            setMensagemDetalhada(e.getMessage());
        }
        return objs;
    }

    public boolean validarPermissaoEmpresas() {
        try {
            if (getUsuarioLogado().getUsuarioPerfilAcessoVOs().isEmpty()) {
                if (getUsuarioLogado().getAdministrador()) {
                    setMensagemDetalhada("");
                    setSucesso(true);
                    return true;
                }
                throw new Exception("O usuário informado não tem nenhum perfil acesso informado.");
            }
            Iterator i = getUsuarioLogado().getUsuarioPerfilAcessoVOs().iterator();
            while (i.hasNext()) {
                UsuarioPerfilAcessoVO usuarioPerfilAcesso = (UsuarioPerfilAcessoVO) i.next();
                if (getEmpresaLogado().getCodigo().equals(usuarioPerfilAcesso.getEmpresa().getCodigo().intValue())) {
                    usuarioPerfilAcesso.getPerfilAcesso().setPermissaoVOs(getFacade().getPermissao().consultarPermissaos(usuarioPerfilAcesso.getPerfilAcesso().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
                    getFacade().getControleAcesso().verificarPermissaoUsuarioFuncionalidade(usuarioPerfilAcesso.getPerfilAcesso(),
                            getUsuarioLogado(), "LancarVisualizarLancamentosEmpresas", "9.23 - Lançar/Visualizar Lançamentos Financeiros para todas as Empresas");
                    return true;
                }
            }
        } catch (Exception e) {
            return false;
        }
        return false;
    }

    public final void inicializarEmpresa() throws Exception {
        mostrarCampoEmpresa = validarPermissaoEmpresas();
        empresa = (EmpresaVO) getEmpresaLogado().getClone(true);
        lote.setEmpresa(empresa);
        if (empresa == null) {
            throw new Exception("Empresa Não Encontrada. Entre Novamente no Sistema.");
        }
    }

    public void inicializarUsuarioLogado() throws Exception {
        usuarioResponsavel = new UsuarioVO();
        usuarioResponsavel.setCodigo(getUsuarioLogado().getCodigo());
        usuarioResponsavel.setUsername(getUsuarioLogado().getUsername());
        usuarioResponsavel.setAdministrador(getUsuarioLogado().getAdministrador());
    }

    public void consultarResponsavel() {
        try {
            setUsuarioResponsavel(getFacade().getUsuario().consultarPorChavePrimaria(getUsuarioResponsavel().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
            setMensagemDetalhada("msg_dados_consultados", "");
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public void autorizarGestaoLotes() throws Exception {
        setMensagemDetalhada("", "");
        if (getUsuarioLogado().getUsuarioPerfilAcessoVOs().isEmpty()) {
            if (getUsuarioLogado().getAdministrador()) {
                return;
            }
            throw new Exception("O usuário informado não tem nenhum perfil acesso informado.");
        }
        Iterator i = getUsuarioLogado().getUsuarioPerfilAcessoVOs().iterator();
        while (i.hasNext()) {
            UsuarioPerfilAcessoVO usuarioPerfilAcesso = (UsuarioPerfilAcessoVO) i.next();
            if (getEmpresaLogado().getCodigo().equals(usuarioPerfilAcesso.getEmpresa().getCodigo().intValue())) {
                usuarioPerfilAcesso.getPerfilAcesso().setPermissaoVOs(getFacade().getPermissao().
                        consultarPermissaos(usuarioPerfilAcesso.getPerfilAcesso().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
                getFacade().getControleAcesso().verificarPermissaoUsuarioFuncionalidade(usuarioPerfilAcesso.getPerfilAcesso(),
                        getUsuarioLogado(), "GestaoLotes", "9.02 - Gestão de Lotes");
            }
        }
    }

    public String abrirTelaConsultaLotes() {
        try {
            if (listaLotes == null || listaLotes.isEmpty()) {
                novo();
            }
            restauraFiltros();
            autorizarGestaoLotes();
            consultarLotes();
            return "gestaoLotes";
        } catch (Exception e) {
            montarMsgAlert(e.getMessage());
            return "";
        }
    }

    private void validarDados() throws Exception {
        if (empresa.getCodigo() == 0) {
            throw new Exception("Informe a Empresa para consultar.");
        }
        // se uma data de um periodo foi informada a outra tbm deve ser
        if (dataInicialLancamento != null && dataFinalLancamento == null) {
            throw new Exception("Informe a data final de Lançamento.");
        }
        if (dataInicialLancamento == null && dataFinalLancamento != null) {
            throw new Exception("Informe a data inicial de Lançamento.");
        }
        // se uma data de um periodo foi informada a outra tbm deve ser
        if (dataInicialDeposito != null && dataFinalDeposito == null) {
            throw new Exception("Informe a data final de Depósito.");
        }
        if (dataInicialDeposito == null && dataFinalDeposito != null) {
            throw new Exception("Informe a data inicial de Depósito.");
        }
        // se nenhum filtro foi informado
        if (codigo.trim().isEmpty() && descricao.trim().isEmpty()
                && dataInicialLancamento == null && dataFinalLancamento == null
                && dataInicialDeposito == null && dataFinalDeposito == null) {
            throw new Exception("Informe pelo menos um filtro para consultar.");
        }
        if (dataInicialDeposito != null && dataInicialDeposito.after(dataFinalDeposito)) {
            throw new Exception("Data Final de Depósito não pode ser menor que a Data Inicial de Depósito.");
        }
        if (dataInicialLancamento != null && dataInicialLancamento.after(dataFinalLancamento)) {
            throw new Exception("Data Final de Lançamento não pode ser menor que a Data Inicial de Lançamento.");
        }
    }

    public void consultarLotes() {
        setMensagemDetalhada("", "");
        try {
            validarDados();
            setValorTotalDaConsulta(0);
            int codLote = 0;
            if (!codigo.trim().isEmpty()) {
                codLote = Integer.parseInt(codigo);
            }
            // consulta os lotes
            listaLotes = getFacade().getLote().consultarPor(empresa.getCodigo(), codLote, descricao,
                    dataInicialDeposito, dataFinalDeposito, dataInicialLancamento, dataFinalLancamento, 0, false, false,
                    Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            salvarFiltroSessao();
            calcularTotalDaConsulta();

            setMensagemID("msg_dados_consultados");
            setFiltros(detalharFiltros());

        } catch (NumberFormatException e) {
            setMensagemDetalhada("msg_erro", "Código digitado contém caracteres Inválidos. Digite somente números");
        } catch (Exception e) {
            listaLotes = new ArrayList<LoteVO>();
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    private void calcularTotalDaConsulta() {
        setValorTotalDaConsulta(0.0);
        for (LoteVO loteVO : listaLotes) {
            setValorTotalDaConsulta(getValorTotalDaConsulta() + loteVO.getValor());
        }
    }

    public String novoLote() {
        setPagaMovConta(new MovContaVO());
        setEscolha("closed");
        limparDadosConsultados();
        limparDadosConsulta();
        lote.setNovoObj(true);
        lote.setEmpresa(new EmpresaVO());
        lote.setDataLancamento(Calendario.hoje());
        lote.setCheques(new ArrayList<ChequeVO>());
        lote.setCartoes(new ArrayList<CartaoCreditoVO>());
        this.setOrigem("gestaoLotes");
        return "editarLote";
    }

    public String voltar() {
        limparMsg();
        return "gestaoRecebiveis";
    }

    public String voltarGestao() {
        try {
            consultarLotes();
        } catch (Exception e) {
        }
        limparMsg();
        return this.getOrigem();
    }

    public String editarLote() {
        limparMsg();
        LoteVO loteObj = (LoteVO) context().getExternalContext().getRequestMap().get("lote");
        if(loteObj == null){
            /* A variável codigoLote é setada pela tag <a4j:actionparam> */
            loteObj = new LoteVO(this.getCodigoLote());
        }
        return editarLote(loteObj);
    }

    public String editarLote(LoteVO obj) {
        setPermitirAlteracaoDataLancamento(false);
        setMensagemDetalhada("", "");
        try {
            // se nao foi encontrado o objeto selecionado
            if (obj == null) {
                throw new Exception("Erro ao posicionar o lote. Contate o suporte técnico.");
            }
            lotePreparado(obj.getCodigo());
            
            if(this.getOrigem() == null){
                this.setOrigem("gestaoLotes");
            }
            
            return "editarLote";
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
        return "";
    }

    private void pesquisarContasQueLotePaga(LoteVO loteVO)throws Exception{
        listaMovContaQueLotePaga = null;

        StringBuilder desc = new StringBuilder();
        if (lote.getPagaMovConta() > 0){
            this.labelContasQueLotePaga = "Lançamento que este lote paga:";
            MovContaVO obj = getFacade().getFinanceiro().getMovConta().consultarPorCodigo(lote.getPagaMovConta(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            desc.append(obj.getDescricao());

        }else{
            Double totalContasPagaPeloLote = 0.0;
            this.labelContasQueLotePaga = "Lançamentos que este lote paga:";
            this.listaMovContaQueLotePaga =  getFacade().getFinanceiro().getMovConta().consultarContasPagamentoComLote(loteVO.getCodigo(),Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            for (MovContaVO obj: this.listaMovContaQueLotePaga){
                totalContasPagaPeloLote = totalContasPagaPeloLote + obj.getValor();
                if (desc.length() <= 0){
                    desc.append(obj.getDescricao());
                }else{
                    desc.append(", ").append(obj.getDescricao());
                }
            }
            MovContaVO movTotal = new MovContaVO();
            movTotal.setDescricao("TOTAL");
            movTotal.setValor(totalContasPagaPeloLote);
            this.listaMovContaQueLotePaga.add(movTotal);
        }
        this.contasQueLotePaga = desc.toString();

    }

    public String prepareLoteCh() throws Exception {
        setMensagemDetalhada("", "");
        try {
            novo();
            try {
                autorizarGestaoLotes();
            } catch (Exception e) {
                montarMsgAlert(e.getMessage());
                throw e;
            }
            ChequeTO ch = (ChequeTO) context().getExternalContext().getRequestMap().get("cheq");
            if (UteisValidacao.emptyNumber(ch.getNumeroLote())) {
                throw new Exception("Lote não foi posicionado corretamente. Contate o suporte técnico.");
            }
            lotePreparado(ch.getNumeroLote());
            this.setOrigem("gestaoRecebiveis");
            this.setCodigoLote(null);
            return "editarLote";
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
        return "";
    }

    public String prepareLoteCc() {
        setMensagemDetalhada("", "");
        try {
            novo();
            try {
                autorizarGestaoLotes();
            } catch (Exception e) {
                montarMsgAlert(e.getMessage());
                throw e;
            }
            CartaoCreditoTO cc = (CartaoCreditoTO) context().getExternalContext().getRequestMap().get("cart");
            if (cc == null) {
                throw new Exception("Recibo não foi posicionado corretamente. Contate o suporte técnico.");
            }
            lotePreparado(cc.getNumeroLote());
            this.setOrigem("gestaoRecebiveis");
            return "editarLote";
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
        return "";
    }

    public void lotePreparado(int codigo) throws Exception {
        JSFUtilities.removeFromSession("codigoLoteSessao");
        lote = getFacade().getLote().consultarPorChavePrimaria(codigo, Uteis.NIVELMONTARDADOS_TODOS);
        pesquisarContasQueLotePaga(lote);
        LoteVO obj = ((LoteVO) lote.getClone(true));
        obj.setNovoObj(false);
        lote.setObjetoVOAntesAlteracao(obj);
        Integer codigoMovContaPagaPorLote = lote.getPagaMovConta();
        if (codigoMovContaPagaPorLote.intValue() == 0){
            codigoMovContaPagaPorLote = getFacade().getFinanceiro().getMovConta().consultarMovContaLotePagou(codigo);
        }
        setPagaMovConta(getFacade().getFinanceiro().getMovConta().consultarPorCodigo(codigoMovContaPagaPorLote, Uteis.NIVELMONTARDADOS_DADOSENTIDADESPRINCIPAIS));
        setLancamento(getFacade().getFinanceiro().getMovConta().consultarPorLote(lote.getCodigo(), lote.getAvulso() ? Uteis.NIVELMONTARDADOS_LOTE_AVULSO : Uteis.NIVELMONTARDADOS_TODOS));
        atualizaTotaisLote();
        if (UteisValidacao.emptyList(listaChequesLote)) {
            escolha = "cartao";
        } else {
            escolha = "cheque";
        }
    }

    public String atualizarTelaLote() {

        try {
            Integer codigoLoteSessao = (Integer) JSFUtilities.getFromSession("codigoLoteSessao");
            if (codigoLoteSessao == null) {
                codigoLoteSessao = lote.getCodigo();
            }
            lotePreparado(codigoLoteSessao);
            JSFUtilities.removeFromSession("codigoLoteSessao");
        } catch (Exception e) {
            setMensagem(e.getMessage());
        }
        return "editarLote";

    }

    public void atualizaTotaisLote() {
        chequesComposicao = new ArrayList<Integer>();
        cartoesComposicao = new ArrayList<Integer>();
        totalChequesLote = 0.0;
        totalCartoesLote = 0.0;

        // consulta os cheques
        ResultSet resultado;
        try {
            resultado = getFacade().getCheque().consultarParaGestaoLote(lote.getCodigo());
            listaChequesLote = new ArrayList<ChequeTO>();
            while (resultado.next()) {
                ChequeTO aux = new ChequeTO();

                aux.setCodigo(resultado.getInt("codigo"));
                aux.setCodigosComposicao(resultado.getString("composicao"));
                if (aux.getCodigosComposicao() != null && !aux.getCodigosComposicao().equals("")) {
                    Boolean presente = false;	// condição para verificar se algum cheque da composicao já está na lista
                    for (Integer codigoCheque : chequesComposicao) {
                        if (codigoCheque.equals(aux.getCodigo())) {
                            presente = true; //cheque já está na lista
                            break;
                        }

                    }
                    if (!presente) { // se cjque não está presente, adicionar na lista os codigos dos cheques que não podem entrar na lista
                        String[] codigos = aux.getCodigosComposicao().split(",");
                        for (String codComposicao : codigos) {
                            chequesComposicao.add(Integer.parseInt(codComposicao));
                        }
                    } else {
                        continue; // cheque já presente na lista, não adicionar
                    }

                }
                aux.setNumeroBanco(resultado.getString("codigobanco"));
                aux.setAgencia(resultado.getString("agencia"));
                aux.setConta(resultado.getString("conta"));
                aux.setNumero(resultado.getString("numero"));
                aux.setDataLancamento(resultado.getDate("datalancamento"));
                aux.setDataCompensacao(resultado.getDate("dataCompesancao"));
                aux.setValor(resultado.getDouble("valortotal"));
                aux.setNomePagador(resultado.getString("nomepagador"));
                aux.setDataOriginal(resultado.getDate("dataOriginal"));
                if(getFacade().getFinanceiro().getHistoricoCheque().chequeJaSaiuLote(aux.getObterTodosChequesComposicao(), lote.getCodigo())){
                    aux.setRemovido(true);
                }
                adicionarChequeLote(aux);
            }
            Ordenacao.ordenarLista(listaChequesLote, "removido");
            Ordenacao.ordenarLista(listaChequesLote, "nomePagador");
            List<ChequeTO> listaChequesRetirados = getFacade().getCheque().consultarChequesRetirados(lote.getCodigo(), null);
            for (ChequeTO ch : listaChequesRetirados) {
                adicionarChequeLote(ch);
            }
            // consulta os cartoes
            resultado = getFacade().getCartaoCredito().consultarParaGestaoLote(lote.getCodigo());
            listaCartoesLote = new ArrayList<CartaoCreditoTO>();
            while (resultado.next()) {
                CartaoCreditoTO aux = new CartaoCreditoTO();
                aux.setCodigo(resultado.getInt("codigo"));
                aux.setCodigosComposicao(resultado.getString("composicao"));
                aux.setRemovido(resultado.getBoolean("saiu"));
                if (aux.getCodigosComposicao() != null && !aux.getCodigosComposicao().equals("")) {
                    Boolean presente = false;	// condição para verificar se algum cheque da composicao já está na lista
                    for (Integer codigoCartao : cartoesComposicao) {
                        if (codigoCartao.equals(aux.getCodigo())) {
                            presente = true; //cheque já está na lista
                            break;
                        }

                    }
                    if (!presente) { // se cjque não está presente, adicionar na lista os codigos dos cheques que não podem entrar na lista
                        String[] codigos = aux.getCodigosComposicao().split(",");
                        for (String codComposicao : codigos) {
                            cartoesComposicao.add(Integer.parseInt(codComposicao));
                        }
                    } else {
                        continue; // cheque já presente na lista, não adicionar
                    }

                }
                aux.setDataLancamento(resultado.getDate("datalancamento"));
                aux.setDataCompensacao(resultado.getDate("dataCompesancao"));
                aux.setValor(resultado.getDouble("valortotal"));
                aux.setNomePagador(resultado.getString("nomepagador"));
                aux.setDataOriginal(resultado.getDate("dataOriginal"));
                aux.setOperadora(resultado.getString("descricao"));
                aux.setAutorizacao(resultado.getString("autorizacaocartao"));
                aux.setNsu(resultado.getString("nsu"));
                adicionarCartaoLote(aux);
            }
            Ordenacao.ordenarLista(listaCartoesLote, "nomePagador");
            List<CartaoCreditoTO> listaCartoesRetirados = getFacade().getCartaoCredito().consultarCartoesRetirados(lote.getCodigo(), null);
            for (CartaoCreditoTO cc : listaCartoesRetirados) {
                adicionarCartaoLote(cc);
            }
        } catch (Exception e) {
            listaChequesLote = new ArrayList<ChequeTO>();
            listaCartoesLote = new ArrayList<CartaoCreditoTO>();
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    private void validarDadosConsulta() throws Exception {
        // se uma data de um periodo foi informada a outra tbm deve ser
        if (dataInicialLancamentoC != null && dataFinalLancamentoC == null) {
            throw new Exception("Informe a data final de Lançamento.");
        }
        if (dataInicialLancamentoC == null && dataFinalLancamentoC != null) {
            throw new Exception("Informe a data inicial de Lançamento.");
        }
        // se uma data de um periodo foi informada a outra tbm deve ser
        if (dataInicialCompensacao != null && dataFinalCompensacao == null) {
            throw new Exception("Informe a data final de Compensação.");
        }
        if (dataInicialCompensacao == null && dataFinalCompensacao != null) {
            throw new Exception("Informe a data inicial de Compensação.");
        }
        // se nenhum filtro foi informado
        if (nome.trim().isEmpty() && dataInicialLancamentoC == null && dataFinalLancamentoC == null
                && dataInicialCompensacao == null && dataFinalCompensacao == null) {
            throw new Exception("Informe pelo menos um filtro para consultar.");
        }
        if (dataInicialCompensacao != null && dataInicialCompensacao.after(dataFinalCompensacao)) {
            throw new Exception("Data Final de Depósito não pode ser menor que a Data Inicial de Compensação.");
        }
        if (dataInicialLancamentoC != null && dataInicialLancamentoC.after(dataFinalLancamentoC)) {
            throw new Exception("Data Final de Lançamento não pode ser menor que a Data Inicial de Lançamento.");
        }
    }

    public void consultarInclusaoCheques() {
        setMensagemDetalhada("", "");
        chequesComposicao = new ArrayList<Integer>();
        try {
            validarDadosConsulta();
            nome = nome.toUpperCase();
            // consulta os cheques
            ResultSet resultado = getFacade().getCheque().consultarPorPeriodoLC(nome, dataInicialLancamentoC, dataFinalLancamentoC,
                    dataInicialCompensacao, dataFinalCompensacao, lote.getEmpresa().getCodigo(), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
            listaCheques = new ArrayList<ChequeTO>();
            while (resultado.next()) {
                ChequeTO aux = new ChequeTO();
                aux.setCodigo(resultado.getInt("codigo"));
                aux.setCodigosComposicao(resultado.getString("composicao"));
                if (aux.getCodigosComposicao() != null && !aux.getCodigosComposicao().equals("")) {
                    Boolean presente = false;	// condição para verificar se algum cheque da composicao já está na lista
                    for (Integer codigo : chequesComposicao) {
                        if (aux.getCodigo() == codigo) {
                            presente = true; //cheque já está na lista
                            break;
                        }

                    }
                    if (!presente) { // se cjque não está presente, adicionar na lista os codigos dos cheques que não podem entrar na lista
                        String[] codigos = aux.getCodigosComposicao().split(",");
                        for (String codComposicao : codigos) {
                            chequesComposicao.add(Integer.parseInt(codComposicao));
                        }
                    } else {
                        continue; // cheque já presente na lista, não adicionar
                    }

                }
                aux.setNumeroBanco(resultado.getString("banco"));
                aux.setAgencia(resultado.getString("agencia"));
                aux.setConta(resultado.getString("conta"));
                aux.setNumero(resultado.getString("numero"));
                aux.setDataLancamento(resultado.getDate("datalancamento"));
                aux.setDataCompensacao(resultado.getDate("dataCompesancao"));
                aux.setValor(resultado.getDouble("valortotal"));
                aux.setNomePagador(resultado.getString("nomepagador"));
                aux.setDataOriginal(resultado.getDate("dataOriginal"));
                getFacade().getFinanceiro().getHistoricoCheque().getContaLoteCheque(aux, true);
                aux.setLoteAvulso(getFacade().getLote().consultarLoteAvulso(aux.getCodigo()));
                if (aux.getLoteAvulso() > 0 || lote.getCodigoContaContido() != aux.getCodigoContaContido()) {
                    continue;
                }
                listaCheques.add(aux);
            }
        } catch (Exception e) {
            listaCheques = new ArrayList<ChequeTO>();
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public void calcularTotalCheques() {
        setTotalCheques(0.0);
        // percorre todos os cheques da lista
        Iterator i = getListaCheques().iterator();
        while (i.hasNext()) {
            ChequeTO c = (ChequeTO) i.next();
            // soma somente os cheques marcados
            if (c.isChequeEscolhido()) {
                setTotalCheques(getTotalCheques() + c.getValor());
            }
        }
    }

    public void consultarInclusaoCartoes() {
        setMensagemDetalhada("", "");
        cartoesComposicao = new ArrayList<Integer>();
        try {
            validarDadosConsulta();
            nome = nome.toUpperCase();
            // consulta os cartoes
            ResultSet resultado = getFacade().getCartaoCredito().consultarPorPeriodoLC(nome, dataInicialLancamentoC, dataFinalLancamentoC,
                    dataInicialCompensacao, dataFinalCompensacao, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA, lote.getEmpresa().getCodigo());
            listaCartoes = new ArrayList<CartaoCreditoTO>();
            while (resultado.next()) {
                CartaoCreditoTO aux = new CartaoCreditoTO();
                aux.setCodigo(resultado.getInt("codigo"));
                aux.setCodigosComposicao(resultado.getString("composicao"));
                if (aux.getCodigosComposicao() != null && !aux.getCodigosComposicao().equals("")) {
                    Boolean presente = false;	// condição para verificar se algum cheque da composicao já está na lista
                    for (Integer codigo : cartoesComposicao) {
                        if (aux.getCodigo() == codigo) {
                            presente = true; //cheque já está na lista
                            break;
                        }

                    }
                    if (!presente) { // se cjque não está presente, adicionar na lista os codigos dos cheques que não podem entrar na lista
                        String[] codigos = aux.getCodigosComposicao().split(",");
                        for (String codComposicao : codigos) {
                            cartoesComposicao.add(Integer.parseInt(codComposicao));
                        }
                    } else {
                        continue; // cheque já presente na lista, não adicionar
                    }

                }
                aux.setDataLancamento(resultado.getDate("datalancamento"));
                aux.setDataCompensacao(resultado.getDate("dataCompesancao"));
                aux.setValor(resultado.getDouble("valortotal"));
                aux.setNomePagador(resultado.getString("nomepagador"));
                aux.setDataOriginal(resultado.getDate("dataOriginal"));
                aux.setOperadora(resultado.getString("descricao"));
                aux.setAutorizacao(resultado.getString("autorizacaocartao"));

                getFacade().getFinanceiro().getHistoricoCartao().getContaLoteCartao(aux, true);
                //aux.setLoteAvulso(getFacade().getLote().consultarLoteAvulso(aux.getCodigo()));
                if (lote.getCodigoContaContido() != aux.getCodigoContaContido()) {
                    continue;
                }
                listaCartoes.add(aux);
            }
        } catch (Exception e) {
            listaCartoes = new ArrayList<CartaoCreditoTO>();
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public void calcularTotalCartoes() {
        setTotalCartoes(0.0);
        // percorre todos os cartoes da lista
        Iterator i = getListaCartoes().iterator();
        while (i.hasNext()) {
            CartaoCreditoTO c = (CartaoCreditoTO) i.next();
            // soma somente os cartoes marcados
            if (c.isCartaoEscolhido() && !c.getApresentarNumeroLote()) {
                setTotalCartoes(getTotalCartoes() + c.getValor());
            }
        }
    }

    public void incluirLote() {
        mostrarModal = false;
        setMsgAlert("");
        setMensagemDetalhada("", "");
        try {
            limparDadosConsulta();
            lote.setDataLancamento(Calendario.hoje());
            lote.setUsuarioResponsavel(usuarioResponsavel);
            lote.validarDadosSimples();
            mostrarModal = true;
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public String getAbrirModalCheque() {
        if (mostrarModal) {
            return "Richfaces.showModalPanel('panelConsultaCheque');";
        } else {
            return "";
        }
    }

    public String getAbrirModalCartao() {
        if (mostrarModal) {
            return "Richfaces.showModalPanel('panelConsultaCartao');";
        } else {
            return "";
        }
    }

    public void depositarCheques() {
        try {
            String abrirModal = "";
            setMensagemDetalhada("", "");
            if (lote.isNovoObj()) {
                // percorre a lista verificando quais elementos estao marcados
                for (ChequeTO ch : listaCheques) {
                    if (ch.isChequeEscolhido()) {
                        ChequeVO aux = new ChequeVO();
                        aux.setCodigo(ch.getCodigo());
                        aux.setComposicao(ch.getCodigosComposicao());
                        aux.setDataCompensacao(ch.getDataCompensacao());
                        aux.setDataOriginal(ch.getDataOriginal());
                        aux.setValor(ch.getValor());
                        lote.adicionarCheque(aux);
                        adicionarChequeLote(ch); // elementos de tela
                    }
                }
                getFacade().getLote().incluir(lote);
            } else {
                // percorre a lista verificando quais elementos estao marcados
                for (ChequeTO ch : listaCheques) {
                    if (ch.isChequeEscolhido()) {
                        ChequeVO aux = new ChequeVO();
                        aux.setCodigo(ch.getCodigo());
                        aux.setComposicao(ch.getCodigosComposicao());
                        aux.setDataCompensacao(ch.getDataCompensacao());
                        aux.setDataOriginal(ch.getDataOriginal());
                        aux.setValor(ch.getValor());
                        lote.adicionarCheque(aux);
                        getFacade().getLote().alterarMinimo(lote, aux, false, false, true);
                        adicionarChequeLote(ch); // elementos de tela
                    }
                }
            }
            limparDadosConsulta();
            setMsgAlert(abrirModal.isEmpty() ? "" : abrirModal + ";");
            setMensagemID("msg_dados_gravados");
            if (lote.getCheques().isEmpty()) {
                escolha = "closed";
            } else {
                escolha = "cheque";
            }
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    private void adicionarChequeLote(ChequeTO ch) {
        listaChequesLote.add(ch);
        totalChequesLote = Uteis.arredondarForcando2CasasDecimais(totalChequesLote + ch.getValor());
        atualizarValorLista(totalChequesLote);
    }

    public void depositarCartoes() {
        try {
            setMsgAlert("");
            String abrirModal = "";
            setMensagemDetalhada("", "");
            if (lote.isNovoObj()) {
                // percorre a lista verificando quais elementos estao marcados
                for (CartaoCreditoTO ca : listaCartoes) {
                    if (ca.isCartaoEscolhido()) {
                        CartaoCreditoVO aux = new CartaoCreditoVO();
                        aux.setCodigo(ca.getCodigo());
                        aux.setComposicao(ca.getCodigosComposicao());
                        aux.setDataCompensacao(ca.getDataCompensacao());
                        aux.setDataOriginal(ca.getDataOriginal());
                        aux.setValor(ca.getValor());
                        lote.adicionarCartao(aux);
                        adicionarCartaoLote(ca);
                    }
                }
                getFacade().getLote().incluir(lote);
            } else {
                // percorre a lista verificando quais elementos estao marcados
                for (CartaoCreditoTO ca : listaCartoes) {
                    if (ca.isCartaoEscolhido()) {
                        CartaoCreditoVO aux = new CartaoCreditoVO();
                        aux.setCodigo(ca.getCodigo());
                        aux.setComposicao(ca.getCodigosComposicao());
                        aux.setDataCompensacao(ca.getDataCompensacao());
                        aux.setDataOriginal(ca.getDataOriginal());
                        aux.setValor(ca.getValor());
                        lote.adicionarCartao(aux);
                        getFacade().getLote().alterarMinimo(lote, aux, false, true);
                        adicionarCartaoLote(ca);
                    }
                }
            }
            limparDadosConsulta();
            setMsgAlert(abrirModal.isEmpty() ? "" : abrirModal + ";");
            if (lote.getCartoes().isEmpty()) {
                escolha = "closed";
            } else {
                escolha = "cartao";
            }
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public void depositarLote(TipoFormaPagto fp, Double valor) {
        try {
            setMensagemDetalhada("", "");
            OperacaoContaControle operacaoContaControle = (OperacaoContaControle) getControlador(OperacaoContaControle.class.getSimpleName());
            setMsgAlert(operacaoContaControle.abrirModal(lote, TipoOperacaoLancamento.CUSTODIA,
                    TipoES.ENTRADA, fp, valor, true, null, false, false, false));
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public void depositarLoteCheques() {
        depositarLote(TipoFormaPagto.CHEQUE, totalChequesLote);
    }

    public void depositarLoteCartoes() {
        depositarLote(TipoFormaPagto.CARTAOCREDITO, totalCartoesLote);
    }

    private void adicionarCartaoLote(CartaoCreditoTO ca) {
        listaCartoesLote.add(ca);
        totalCartoesLote = Uteis.arredondarForcando2CasasDecimais(totalCartoesLote + ca.getValor());
        atualizarValorLista(totalCartoesLote);
    }

    private void removerCartaoLote(CartaoCreditoTO ca) {
        listaCartoesLote.remove(ca);
        totalCartoesLote = Uteis.arredondarForcando2CasasDecimais(totalCartoesLote - ca.getValor());
        atualizarValorLista(totalCartoesLote);
    }

    private void atualizarValorLista(Double total) {
        if(listaLotes == null){
            return;
        }
        for (LoteVO lote : listaLotes) {
            if (lote.getCodigo().equals(getLote().getCodigo())) {
                lote.setValor(total);
            }
        }
        calcularTotalDaConsulta();
    }

    public void confirmarExcluir(){
        MensagemGenericaControle control = (MensagemGenericaControle) getControlador(MensagemGenericaControle.class);
        control.setMensagemDetalhada("", "");
        setMsgAlert("Richfaces.showModalPanel('mdlMensagemGenerica');");
        control.init("Exclusão de Lote",
                "Deseja excluir o lote?",
                this, "excluirLote", "", "cancelarOperacaoExcluir", "", "listaLotes,totaisregistro,totais");
    }

    public void cancelarOperacaoExcluir(){
        MensagemGenericaControle control = (MensagemGenericaControle) getControlador(MensagemGenericaControle.class);
        control.setOnCompleteBotaoSim("");
        control.setOnCompleteBotaoNao("");
        montarSucessoGrowl("");
    }

    public void excluirLote() {
        setMensagemDetalhada("", ""); //Verificar mensagem Duplicar essa msg
        MensagemGenericaControle mensagemGenericaControle = (MensagemGenericaControle) getControlador(MensagemGenericaControle.class);
        try {
            if (!UtilReflection.objetoMaiorQueZero(lote, "getCodigo()")) {
                throw new Exception("Não foi possível identificar o lote a ser excluído.");
            }
            lote = getFacade().getLote().consultarPorChavePrimaria(lote.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            if (lote.getPagaMovConta() > 0) {
                throw new Exception(getMensagemInternalizacao("msg_lotenaopodeexcluirpagamovconta"));
            }else{
                Integer codigoMovContaPagaPorLote = getFacade().getFinanceiro().getMovConta().consultarMovContaLotePagou(lote.getCodigo());
                 if  ((codigoMovContaPagaPorLote != null) && (codigoMovContaPagaPorLote >0)){
                    throw new Exception(getMensagemInternalizacao("msg_lotenaopodeexcluirpagamovconta"));
            }
            }

            if (getLotePagaMovConta()) {
                setMensagemID("msg_naoexcluirChequePagaMovConta");
                throw new Exception(getMensagem());
            }
            if (getDepositado() && getUsarMovimentacao()) {
                CaixaControle caixaControle = (CaixaControle) getControlador(CaixaControle.class.getSimpleName());
                if ((caixaControle.getCaixaVoEmAberto() == null) || (caixaControle.getCaixaVoEmAberto().getCodigo() == 0)) {
                    setMensagemID("msg_naoPossuiCaixaAbertoEscape");
                    throw new Exception(getMensagem());
                }
            }


            List<LoteVO> lotesDerivados = getFacade().getLote().consultarRelacionamentosDoLote(lote);
            if (lotesDerivados.isEmpty() || lote.getAvulso()) {
                getFacade().getLote().excluir(lote);
                listaLotes.remove(lote);
                // exclui o lote do BD tirando o vinculo com os cheques ou cartoes presentes no lote
                montarSucessoGrowl("Lote excluído com sucesso.");
                mensagemGenericaControle.setOnCompleteBotaoSim(getMensagemNotificar());
                //setMensagemID("msg_dados_excluidos");
            } else {
                String lotes = "";
                for (LoteVO l : lotesDerivados) {
                    lotes += ", " + l.getCodigo() + "-" + l.getDescricao();
                }
                throw new Exception(getMensagemInternalizacao("msg_lotesDerivados") + lotes.replaceFirst(",", ""));
            }
            LogVO log = new LogVO();
            log.setNomeEntidade("LOTE");
            log.setNomeEntidadeDescricao("Lote");
            log.setChavePrimaria(lote.getCodigo().toString());
            log.setDataAlteracao(Calendario.hoje());
            log.setNomeCampo("Exclusão");
            log.setValorCampoAnterior("Lote: " + lote.getCodigo() + " - " + lote.getDescricao());
            log.setResponsavelAlteracao(getUsuarioLogado().getNome());
            log.setUserOAMD(getUsuarioLogado().getUserOamd());
            log.setOperacao("EXCLUSAO");
            getFacade().getLog().incluir(log);

            calcularTotalDaConsulta();
        } catch (Exception e) {
            montarErro(e);
            mensagemGenericaControle.setOnCompleteBotaoSim(getMensagemNotificar());
            //setMensagemDetalhada("msg_erro", e.getMessage());
            //getMensagemNotificar();
        }
    }

    /**
     * Consulta de logs de lançamentos
     */
    public void realizarConsultaLogObjetoSelecionado() {
        setMsgAlert("");
        LoginControle loginControle = (LoginControle) context().getExternalContext().getSessionMap().get("LoginControle");
        String nomeClasse = "LOTE";
        loginControle.setNomeClasse(loginControle.getNomeEntidadeCamposLog("prt_Finan_GestaoLotes"));
        loginControle.consultarLogObjetoSelecionado(nomeClasse.toUpperCase(), 0, null);
        setMsgAlert("abrirPopup('" + request().getContextPath() + "/faces/visualizadorLogForm.jsp', 'VisualizadorLog', 785, 595);");

    }

    public void excluirCheque() {
        limparListasRemover();
        setMensagemDetalhada("", "");
        try {
            chequesARetirar = new ArrayList<ChequeTO>();
            setMsgAlert("");
            // pega o objeto da tela
            ChequeTO obj = (ChequeTO) context().getExternalContext().getRequestMap().get("cheque");
            // se nao foi encontrado o objeto selecionado
            if (obj == null) {
                throw new Exception("Erro ao posicionar o cheque. Contate o suporte técnico.");
            }

            obj.setLoteAvulso(getFacade().getLote().consultarLoteAvulso(obj.getCodigo()));
            if (obj.getLoteAvulso() > 0 && lote.getCodigo().intValue() != obj.getLoteAvulso()) {
                throw new Exception("Não é possível excluir o cheque, ele está em um lote avulso.");
            }
            if (!getLote().getAvulso() && getDepositado() && getUsarMovimentacao()) {
                verificarCaixa();
                AutorizacaoFuncionalidadeControle auto = (AutorizacaoFuncionalidadeControle) getControlador(AutorizacaoFuncionalidadeControle.class);
                setMsgAlert("Richfaces.showModalPanel('modalRetirarChequeLote');");
                chequesARetirar.add(obj);
                return;
            }
            acaoExcluirCheque(obj, true);
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    /**
     * Responsável por
     *
     * <AUTHOR> Alcides 30/07/2013
     */
    private void verificarCaixa() throws Exception {
        CaixaControle caixaControle = (CaixaControle) getControlador(CaixaControle.class.getSimpleName());
        if ((caixaControle.getCaixaVoEmAberto() == null) || (caixaControle.getCaixaVoEmAberto().getCodigo() == 0)) {
            setMensagemID("msg_naoPossuiCaixaAberto");
            throw new Exception(getMensagem());
        }
        validarCaixaAbertoConta(getLote().getCodigoContaContido());
    }

    public void acaoExcluirCheque(ChequeTO obj, boolean alterarValores) throws Exception {
        ChequeVO ch = new ChequeVO();
        ch.setNumero(obj.getNumero());
        ch.setCodigo(obj.getCodigo());
        ch.setComposicao(obj.getCodigosComposicao());
        ch.setDataCompensacao(obj.getDataOriginal());
        ch.setDataOriginal(null);
        ch.setValor(obj.getValor());
        if (alterarValores) {
            // remove o elemento da lista de tela
            removerChequeLote(obj);
            // remove o elemento da lista do lote
            lote.removeCheque(ch);
        }
        // remove o elemento do vinculo com o lote
        getFacade().getLote().alterarMinimo(lote, ch, true, true, alterarValores);
        setMensagemID("msg_dados_excluidos");

        atualizarCaixa();
    }

    public void confirmarExclusaoRecebivelLote() {
        final AutorizacaoFuncionalidadeControle auto = (AutorizacaoFuncionalidadeControle) getControlador(AutorizacaoFuncionalidadeControle.class);
        limparMsg();
        setMsgAlert("");
        if (UteisValidacao.emptyString(getObservacaoRetiradaLote())) {
            montarMsgAlert(getMensagemInternalizacao("msg_finan_observacao_retirada_lote"));
            return;
        }
        AutorizacaoFuncionalidadeListener listener = new AutorizacaoFuncionalidadeListener() {

            @Override
            public void onAutorizacaoComSucesso() throws Exception {
                CaixaControle caixaControle = (CaixaControle) getControlador(CaixaControle.class.getSimpleName());
                getFacade().getFinanceiro().getMovConta().gravarRetiradaRecebivelLote(Calendario.hoje(),
                        chequesARetirar, cartoesARetirar, lote, observacaoRetiradaLote,
                        auto.getUsuario(), caixaControle.getCaixaVoEmAberto().getCodigo());
                atualizarDadosListaCartoesCheques();
                setObservacaoRetiradaLote("");
                atualizarCaixa();

                setChequesARetirar(new ArrayList<ChequeTO>());
                setCartoesARetirar(new ArrayList<CartaoCreditoTO>());
            }

            @Override
            public void onAutorizacaoComErro(Exception e) {
            }

            @Override
            public String getExecutarAoCompletar() {
                return getOnComplete();
            }
        };
        auto.autorizar("Autorização Retirada Recebíveis", "RetirarRecebiveisLotes",
                "Você precisa da permissão \"9.22 - Retirar cartões\\cheques de um lote\"",
                "panelAutorizacaoFuncionalidade,form, modalRetirarChequeLote", listener);
    }

    public void confirmaAlterarDataLancamento() {
        AutorizacaoFuncionalidadeControle auto = (AutorizacaoFuncionalidadeControle) getControlador(AutorizacaoFuncionalidadeControle.class);
        limparMsg();
        setMsgAlert("");
        AutorizacaoFuncionalidadeListener listener = new AutorizacaoFuncionalidadeListener() {

            @Override
            public void onAutorizacaoComSucesso() throws Exception {
                setPermitirAlteracaoDataLancamento(true);
            }

            @Override
            public void onAutorizacaoComErro(Exception e) {

            }
            @Override
            public String getExecutarAoCompletar() {
                return getOnComplete();
            }
        };
        auto.autorizar("Autorização edição da data de lançamento", "EditarDataLancamentoLote",
                "Você precisa da permissão \"Editar data de lançamento do lote\"",
                "panelAutorizacaoFuncionalidade,dadosLotePainel", listener);

    }

    private void atualizarCaixa() throws Exception {
        CaixaControle caixaControle = (CaixaControle) getControlador(CaixaControle.class.getSimpleName());
        caixaControle.atualizarCaixa();
    }

    void limparListasRemover() {
        chequesARetirar = new ArrayList<ChequeTO>();
        cartoesARetirar = new ArrayList<CartaoCreditoTO>();
    }

    public void excluirCartao() {
        limparListasRemover();
        setMensagemDetalhada("", "");
        try {
            // pega o objeto da tela
            CartaoCreditoTO obj = (CartaoCreditoTO) context().getExternalContext().getRequestMap().get("cartao");
            // se nao foi encontrado o objeto selecionado
            if (obj == null) {
                throw new Exception("Erro ao posicionar o Cartão. Contate o suporte técnico.");
            }
            if (getDepositado() && getUsarMovimentacao()) {
                verificarCaixa();
                AutorizacaoFuncionalidadeControle auto = (AutorizacaoFuncionalidadeControle) getControlador(AutorizacaoFuncionalidadeControle.class);
                setMsgAlert("Richfaces.showModalPanel('modalRetirarChequeLote');");
                cartoesARetirar.add(obj);
                return;
            }
            acaoExcluirCartao(obj, true);
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    /**
     * <AUTHOR> Alcides 30/07/2013
     */
    private void acaoExcluirCartao(CartaoCreditoTO obj, boolean alterarValores) throws Exception {
        CartaoCreditoVO cc = new CartaoCreditoVO();
        cc.setCodigo(obj.getCodigo());
        cc.setComposicao(obj.getCodigosComposicao());
        cc.setDataCompensacao(obj.getDataOriginal());
        cc.setDataOriginal(null);
        cc.setValor(obj.getValor());
        if (alterarValores) {
            // remove o elemento da lista de tela
            removerCartaoLote(obj);
            // remove o elemento da lista do lote
            lote.removeCartao(cc);
        }
        // remove o elemento do vinculo com o lote
        getFacade().getLote().alterarMinimo(lote, cc, true, alterarValores);
        setMensagemID("msg_dados_excluidos");

        atualizarCaixa();
    }

    private void removerChequeLote(ChequeTO ch) {
        listaChequesLote.remove(ch);
        totalChequesLote = Uteis.arredondarForcando2CasasDecimais(totalChequesLote - ch.getValor());
        atualizarValorLista(totalChequesLote);
    }

    public void limparPeriodoD() {
        dataInicialDeposito = null;
        dataFinalDeposito = null;
        periodoDeposito = "";
    }

    public void limparPeriodoL() {
        dataInicialLancamento = null;
        dataFinalLancamento = null;
        periodoLancamento = "";
    }

    public void limparPeriodoLC() {
        dataInicialLancamentoC = null;
        dataFinalLancamentoC = null;
    }

    public void limparPeriodoC() {
        dataInicialCompensacao = null;
        dataFinalCompensacao = null;
    }

    public void marcaDesmarcaCheques() {
        for (ChequeTO ch : listaCheques) {
            ch.setChequeEscolhido(isTodosChequesMarcados());
        }
        calcularTotalCheques();
    }

    public void marcaDesmarcaCartoes() {
        for (CartaoCreditoTO cc : listaCartoes) {
            if (!cc.getApresentarNumeroLote()) {
                cc.setCartaoEscolhido(isTodosCartoesMarcados());
            }
        }
        calcularTotalCartoes();
    }

    public String getMensagemDetalhadaNovaLinha(){
        String msg = this.getMensagemDetalhada().replace("\\r\\n", "<br />");
        
        return msg;
    }
// *****************************************************************************
    public String getCodigo() {
        return codigo;
    }

    public void setCodigo(String codigo) {
        this.codigo = codigo;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    @Override
    public EmpresaVO getEmpresa() {
        return empresa;
    }

    @Override
    public void setEmpresa(EmpresaVO empresa) {
        this.empresa = empresa;
    }

    public UsuarioVO getUsuarioResponsavel() {
        return usuarioResponsavel;
    }

    public void setUsuarioResponsavel(UsuarioVO usuarioResponsavel) {
        this.usuarioResponsavel = usuarioResponsavel;
    }

    public Date getDataInicialLancamento() {
        return dataInicialLancamento;
    }

    public void setDataInicialLancamento(Date dataInicialLancamento) {
        this.dataInicialLancamento = dataInicialLancamento;
    }

    public Date getDataFinalLancamento() {
        return dataFinalLancamento;
    }

    public void setDataFinalLancamento(Date dataFinalLancamento) {
        this.dataFinalLancamento = dataFinalLancamento;
    }

    public Date getDataInicialDeposito() {
        return dataInicialDeposito;
    }

    public void setDataInicialDeposito(Date dataInicialDeposito) {
        this.dataInicialDeposito = dataInicialDeposito;
    }

    public Date getDataFinalDeposito() {
        return dataFinalDeposito;
    }

    public void setDataFinalDeposito(Date dataFinalDeposito) {
        this.dataFinalDeposito = dataFinalDeposito;
    }

    public Date getDataInicialCompensacao() {
        return dataInicialCompensacao;
    }

    public void setDataInicialCompensacao(Date dataInicialCompensacao) {
        this.dataInicialCompensacao = dataInicialCompensacao;
    }

    public Date getDataFinalCompensacao() {
        return dataFinalCompensacao;
    }

    public void setDataFinalCompensacao(Date dataFinalCompensacao) {
        this.dataFinalCompensacao = dataFinalCompensacao;
    }

    public Date getDataInicialLancamentoC() {
        return dataInicialLancamentoC;
    }

    public void setDataInicialLancamentoC(Date dataInicialLancamentoC) {
        this.dataInicialLancamentoC = dataInicialLancamentoC;
    }

    public Date getDataFinalLancamentoC() {
        return dataFinalLancamentoC;
    }

    public void setDataFinalLancamentoC(Date dataFinalLancamentoC) {
        this.dataFinalLancamentoC = dataFinalLancamentoC;
    }

    public List<LoteVO> getListaLotes() {
        return listaLotes;
    }

    public void setListaLotes(List<LoteVO> listaLotes) {
        this.listaLotes = listaLotes;
    }

    public List<ChequeTO> getListaCheques() {
        return listaCheques;
    }

    public void setListaCheques(List<ChequeTO> listaCheques) {
        this.listaCheques = listaCheques;
    }

    public List<ChequeTO> getListaChequesLote() {
        return listaChequesLote;
    }

    public void setListaChequesLote(List<ChequeTO> listaChequesLote) {
        this.listaChequesLote = listaChequesLote;
    }

    public List<CartaoCreditoTO> getListaCartoes() {
        return listaCartoes;
    }

    public void setListaCartoes(List<CartaoCreditoTO> listaCartoes) {
        this.listaCartoes = listaCartoes;
    }

    public List<CartaoCreditoTO> getListaCartoesLote() {
        return listaCartoesLote;
    }

    public void setListaCartoesLote(List<CartaoCreditoTO> listaCartoesLote) {
        this.listaCartoesLote = listaCartoesLote;
    }

    public String getEscolha() {
        return escolha;
    }

    public void setEscolha(String escolha) {
        this.escolha = escolha;
    }

    public double getTotalCheques() {
        return totalCheques;
    }

    public void setTotalCheques(double totalCheques) {
        this.totalCheques = totalCheques;
    }

    public double getTotalChequesLote() {
        return totalChequesLote;
    }

    public void setTotalChequesLote(double totalChequesLote) {
        this.totalChequesLote = totalChequesLote;
    }

    public double getTotalCartoes() {
        return totalCartoes;
    }

    public void setTotalCartoes(double totalCartoes) {
        this.totalCartoes = totalCartoes;
    }

    public double getTotalCartoesLote() {
        return totalCartoesLote;
    }

    public void setTotalCartoesLote(double totalCartoesLote) {
        this.totalCartoesLote = totalCartoesLote;
    }

    public LoteVO getLote() {
        return lote;
    }

    public void setLote(LoteVO lote) {
        this.lote = lote;
    }

    public boolean isMostrarCampoEmpresa() {
        return mostrarCampoEmpresa;
    }

    public void setMostrarCampoEmpresa(boolean mostrarCampoEmpresa) {
        this.mostrarCampoEmpresa = mostrarCampoEmpresa;
    }

    public boolean isTodosChequesMarcados() {
        return todosChequesMarcados;
    }

    public void setTodosChequesMarcados(boolean todosChequesMarcados) {
        this.todosChequesMarcados = todosChequesMarcados;
    }

    public boolean isTodosCartoesMarcados() {
        return todosCartoesMarcados;
    }

    public void setTodosCartoesMarcados(boolean todosCartoesMarcados) {
        this.todosCartoesMarcados = todosCartoesMarcados;
    }

    public boolean getApresentarFiltros() {
        return !this.getFiltros().isEmpty();
    }

    /**
     * @return the filtros
     */
    public String getFiltros() {
        if (filtros == null) {
            filtros = "";
        }
        return filtros;
    }

    public void setFiltros(String filtros) {
        this.filtros = filtros;
    }

    public String detalharFiltros() {
        String filtros = ""
                + "";
        if (empresa != null && empresa.getCodigo() != 0 && empresa.getCodigo() != null) {
            filtros += " | Empresa:" + empresa.getNome();
        }
        if (dataInicialLancamento != null && dataFinalLancamento != null) {
            filtros += " | Data de Lançamento:" + Uteis.getDataAplicandoFormatacao(dataInicialLancamento, "dd/MM/yyyy");
            filtros += " até " + Uteis.getDataAplicandoFormatacao(dataFinalLancamento, "dd/MM/yyyy");
        }
        if (dataInicialDeposito != null && dataFinalDeposito != null) {
            filtros += " | Data de Depósito:" + Uteis.getDataAplicandoFormatacao(dataInicialDeposito, "dd/MM/yyyy");
            filtros += " até " + Uteis.getDataAplicandoFormatacao(dataFinalDeposito, "dd/MM/yyyy");
        }
        if (!UteisValidacao.emptyString(descricao)) {
            filtros += " | Descrição: " + descricao;
        }
        if (!UteisValidacao.emptyString(codigo)) {
            filtros += " | Código: " + codigo;
        }
        return filtros.replaceFirst("[|]", "");
    }

    public void obterEmpresaEscolhida() throws Exception {
        if (empresa.getCodigo().intValue() != 0) {
            setEmpresa(getFacade().getEmpresa().consultarPorChavePrimaria(getEmpresa().getCodigo().intValue(), Uteis.NIVELMONTARDADOS_TELACONSULTA));
        } else {
            setEmpresa(new EmpresaVO());
        }
    }

    /**
     * @return the parametro1
     */
    public boolean isParametro1() {
        return parametro1;
    }

    /**
     * @param parametro1 the parametro1 to set
     */
    public void setParametro1(boolean parametro1) {
        this.parametro1 = parametro1;
    }

    /**
     * @return the parametro2
     */
    public boolean isParametro2() {
        return parametro2;
    }

    /**
     * @param parametro2 the parametro2 to set
     */
    public void setParametro2(boolean parametro2) {
        this.parametro2 = parametro2;
    }

    /**
     * @return the parametro3
     */
    public boolean isParametro3() {
        return parametro3;
    }

    /**
     * @param parametro3 the parametro3 to set
     */
    public void setParametro3(boolean parametro3) {
        this.parametro3 = parametro3;
    }

    /**
     * @return the parametro4
     */
    public boolean isParametro4() {
        return parametro4;
    }

    /**
     * @param parametro4 the parametro4 to set
     */
    public void setParametro4(boolean parametro4) {
        this.parametro4 = parametro4;
    }

    /**
     * @return the periodoLancamento
     */
    public String getPeriodoLancamento() {
        return periodoLancamento;
    }

    /**
     * @param periodoLancamento the periodoLancamento to set
     */
    public void setPeriodoLancamento(String periodoLancamento) {
        this.periodoLancamento = periodoLancamento;
    }

    /**
     * @return the periodoDeposito
     */
    public String getPeriodoDeposito() {
        return periodoDeposito;
    }

    /**
     * @param periodoDeposito the periodoDeposito to set
     */
    public void setPeriodoDeposito(String periodoDeposito) {
        this.periodoDeposito = periodoDeposito;
    }

    public boolean getDepositado() {
        return !UteisValidacao.emptyString(lote.getConta());
    }

    public void setPagaMovConta(MovContaVO pagaMovConta) {
        this.pagaMovConta = pagaMovConta;
    }

    public MovContaVO getPagaMovConta() {
        return pagaMovConta;
    }

    public boolean getLotePagaMovConta() {
        return pagaMovConta != null && !UteisValidacao.emptyNumber(pagaMovConta.getCodigo());
    }

    public double getValorTotalDaConsulta() {
        return valorTotalDaConsulta;
    }

    public void setValorTotalDaConsulta(double valorTotalDaConsulta) {
        this.valorTotalDaConsulta = valorTotalDaConsulta;
    }

    public String getTotalizador() {
        return Formatador.formatarValorMonetarioSemMoeda(Uteis.arredondarForcando2CasasDecimaisMantendoSinal(getValorTotalDaConsulta()));
    }

    public void setLancamento(MovContaVO lancamento) {
        this.lancamento = lancamento;
    }

    public MovContaVO getLancamento() {
        return lancamento;
    }

    public String getOnclickExcluir() {
        return  getLote().getAvulso() || !getDepositado() ? "if(!confirm('Deseja retirar este "+(lote.getCartoes().isEmpty() ? "cheque" : "cartão")+ " do lote?')){return false};" : "";
    }

    public void setObservacaoRetiradaLote(String observacaoRetiradaLote) {
        this.observacaoRetiradaLote = observacaoRetiradaLote;
    }

    public String getObservacaoRetiradaLote() {
        return observacaoRetiradaLote;
    }

    public void setChequesARetirar(List<ChequeTO> chequesASeremRetirados) {
        this.chequesARetirar = chequesASeremRetirados;
    }

    public List<ChequeTO> getChequesARetirar() {
        return chequesARetirar;
    }
    
    public void addChequesARetirar(ChequeTO cheque){
        if(this.chequesARetirar != null){
            this.chequesARetirar.add(cheque);
        }
    }

    public String getOnComplete() {//chamada implícita por 'AutorizacaoFuncionalidadeControle.control.onComplete'
        return getMsgAlert();
    }

    public boolean getApresentarListaChequesRetirados() {
        return !UteisValidacao.emptyList(chequesARetirar) && chequesARetirar.size() > 0;
    }

    public boolean getApresentarListaCartoesRetirados() {
        return !UteisValidacao.emptyList(cartoesARetirar) && cartoesARetirar.size() > 0;
    }

    public void setCartoesARetirar(List<CartaoCreditoTO> cartoesASeremRetirados) {
        this.cartoesARetirar = cartoesASeremRetirados;
    }

    public List<CartaoCreditoTO> getCartoesARetirar() {
        return cartoesARetirar;
    }
    
    public void addCartoesARetirar(CartaoCreditoTO cartao){
        if(this.cartoesARetirar != null){
            this.cartoesARetirar.add(cartao);
        }
    }

    public boolean getCartao() {
        return escolha.equals("cartao");
    }

    public Boolean getUsarMovimentacao() {
        try {
            ConfiguracaoFinanceiroControle cfg = (ConfiguracaoFinanceiroControle) JSFUtilities.getFromSession(ConfiguracaoFinanceiroControle.class.getSimpleName());
            return cfg.getConfFinanceiro().getUsarMovimentacaoContas();
        } catch (Exception e) {
            return true;
        }
    }

    public Integer getNrPaginaRetirarLote() {
        return nrPaginaRetirarLote;
    }

    public void setNrPaginaRetirarLote(Integer nrPaginaRetirarLote) {
        this.nrPaginaRetirarLote = nrPaginaRetirarLote;
    }

    public boolean isPermitirAlteracaoDataLancamento() {
        return permitirAlteracaoDataLancamento;
    }

    public void setPermitirAlteracaoDataLancamento(boolean permitirAlteracaoDataLancamento) {
        this.permitirAlteracaoDataLancamento = permitirAlteracaoDataLancamento;
    }

    public Integer getCodigoLote() {
        return codigoLote;
    }

    public void setCodigoLote(Integer codigoLote) {
        this.codigoLote = codigoLote;
    }
    
    public String getContasQueLotePaga() {
        return contasQueLotePaga;
    }
    
    public void setContasQueLotePaga(String contasQueLotePaga) {
        this.contasQueLotePaga = contasQueLotePaga;
    }

    public List<MovContaVO> getListaMovContaQueLotePaga() {
        return listaMovContaQueLotePaga;
    }

    public void setListaMovContaQueLotePaga(List<MovContaVO> listaMovContaQueLotePaga) {
        this.listaMovContaQueLotePaga = listaMovContaQueLotePaga;
    }

    public String getLabelContasQueLotePaga() {
        return labelContasQueLotePaga;
    }

    public void setLabelContasQueLotePaga(String labelContasQueLotePaga) {
        this.labelContasQueLotePaga = labelContasQueLotePaga;
    }

    public MovContaVO getMovContaPagaEmConjunto() {
        return movContaPagaEmConjunto;
    }

    public void setMovContaPagaEmConjunto(MovContaVO movContaPagaEmConjunto) {
        this.movContaPagaEmConjunto = movContaPagaEmConjunto;
    }

    public String getOrigem() {
        return origem;
    }

    public void setOrigem(String origem) {
        this.origem = origem;
    }
    public void atualizarDadosListaCartoesCheques(){
        try{
            GestaoRecebiveisControle recebiveisControle = (GestaoRecebiveisControle) getControlador(GestaoRecebiveisControle.class.getSimpleName());
            if(recebiveisControle != null){
                if(!UteisValidacao.emptyList(chequesARetirar) && !UteisValidacao.emptyList(recebiveisControle.getListaCheques()) ){                  
                    for(ChequeTO chequeAlterado: chequesARetirar){
                        for(ChequeTO chequeTO: recebiveisControle.getListaCheques()){
                            if(chequeTO.getCodigo() == chequeAlterado.getCodigo()){
                                getFacade().getFinanceiro().getHistoricoCheque().getContaLoteCheque(chequeTO, false);
                                chequeTO.setLoteAvulso(getFacade().getLote().consultarLoteAvulso(chequeTO.getCodigo()));
                                if (UteisValidacao.emptyNumber(chequeTO.getNumeroLote())) {
                                    chequeTO.setNumeroLote(getFacade().getLote().consultarPorCheque(chequeTO.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS).getCodigo());
                                }
                                if (chequeTO.getDataFim() != null && chequeTO.getLoteAvulso() == 0) {
                                        chequeTO.setNumeroLote(0);
                                        chequeTO.setContaContido("");
                                        chequeTO.setCodigoContaContido(0);
                                }
                                break;
                            }
                        }
                    }
                }
                if(!UteisValidacao.emptyList(cartoesARetirar) && !UteisValidacao.emptyList(recebiveisControle.getListaCartoes())){
                     for(CartaoCreditoTO cartaoAlterado: cartoesARetirar){   
                        for(CartaoCreditoTO cartaoTO: recebiveisControle.getListaCartoes()){
                            if(cartaoTO.getCodigo() == cartaoAlterado.getCodigo()){
                                cartaoTO.setNumeroLote(getFacade().getLote().consultarPorCartao(cartaoTO.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS).getCodigo());
                                break;
                            }
                        }
                    }
                }
            }
        }catch(Exception ignored){
        }
    }
    public void salvarFiltroSessao(){

        FiltroGestaoLotesTO filtroGestaoLotesTO = new FiltroGestaoLotesTO();
        filtroGestaoLotesTO.setCodigoEmpresa(empresa.getCodigo());
        filtroGestaoLotesTO.setDescricao(descricao);
        filtroGestaoLotesTO.setDataInicialDeposito(dataInicialDeposito);
        filtroGestaoLotesTO.setDataFinalDeposito(dataFinalDeposito);
        filtroGestaoLotesTO.setDataInicialLancamento(dataInicialLancamento);
        filtroGestaoLotesTO.setDataFinalLancamento(dataFinalLancamento);

        JSFUtilities.storeOnSession(FiltroGestaoLotesTO.class.getName(), filtroGestaoLotesTO);

    }
    public void restauraFiltros() {
        FiltroGestaoLotesTO filtroSessao = (FiltroGestaoLotesTO) JSFUtilities.getFromSession(FiltroGestaoLotesTO.class.getName());
        if (filtroSessao != null) {
            empresa.setCodigo(filtroSessao.getCodigoEmpresa());
            descricao = filtroSessao.getDescricao();
            dataInicialDeposito = filtroSessao.getDataInicialDeposito();
            dataFinalDeposito = filtroSessao.getDataFinalDeposito();
            dataInicialLancamento = filtroSessao.getDataInicialLancamento();
            dataFinalLancamento = filtroSessao.getDataFinalLancamento();

        }
    }
    
    private void validarCaixaAbertoConta(Integer codigoContaOrigem) throws Exception{
        CaixaControle caixaControle = (CaixaControle) getControlador(CaixaControle.class.getSimpleName());
        caixaControle.validarCaixaAbertoConta(codigoContaOrigem, true);
    }
}
