package importador.membersevo;

import br.com.pactosolucoes.atualizadb.processo.AplicarMatriculaImportacao;
import br.com.pactosolucoes.enumeradores.OrigemSistemaEnum;
import br.com.pactosolucoes.enumeradores.TipoColaboradorEnum;
import br.com.pactosolucoes.enumeradores.TipoEnderecoEnum;
import br.com.pactosolucoes.enumeradores.TipoTelefoneEnum;
import importador.ImportacaoCache;
import importador.UteisImportacao;
import importador.contrato.ImportarContrato;
import importador.financeiro.ImportarPagamento;
import negocio.comuns.acesso.IntegracaoMemberVO;
import negocio.comuns.acesso.integracao.member.ContactsApiViewTO;
import negocio.comuns.acesso.integracao.member.MemberMemberShipCancellationApiViewTO;
import negocio.comuns.acesso.integracao.member.MemberMembershipApiViewTO;
import negocio.comuns.acesso.integracao.member.MembersApiViewTO;
import negocio.comuns.acesso.integracao.member.MembershipTrasnferDataApiViewTO;
import negocio.comuns.acesso.integracao.member.enums.ContactTypeEnum;
import negocio.comuns.acesso.integracao.member.enums.MemberShipStatusEnum;
import negocio.comuns.acesso.integracao.member.enums.PaymentTypeEnum;
import negocio.comuns.acesso.integracao.member.enums.StatusRecevablesEnum;
import negocio.comuns.acesso.integracao.member.financeiro.ReceivablesViewTO;
import negocio.comuns.acesso.integracao.member.financeiro.SaleItemApiViewTO;
import negocio.comuns.acesso.integracao.member.financeiro.SalesApiViewTO;
import negocio.comuns.arquitetura.RoboVO;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.CidadeVO;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.basico.ContratoDependenteVO;
import negocio.comuns.basico.EmailVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.basico.EnderecoVO;
import negocio.comuns.basico.EstadoVO;
import negocio.comuns.basico.FamiliarVO;
import negocio.comuns.basico.PaisVO;
import negocio.comuns.basico.PessoaVO;
import negocio.comuns.basico.TelefoneVO;
import negocio.comuns.basico.VinculoVO;
import negocio.comuns.basico.enumerador.TipoPessoa;
import negocio.comuns.basico.enumerador.TipoPessoaEnum;
import negocio.comuns.contrato.ContratoDuracaoVO;
import negocio.comuns.contrato.ContratoHorarioVO;
import negocio.comuns.contrato.ContratoModalidadeVO;
import negocio.comuns.contrato.ContratoOperacaoVO;
import negocio.comuns.contrato.ContratoRecorrenciaVO;
import negocio.comuns.contrato.ContratoTextoPadraoVO;
import negocio.comuns.contrato.ContratoVO;
import negocio.comuns.contrato.HistoricoContratoVO;
import negocio.comuns.contrato.MovProdutoVO;
import negocio.comuns.contrato.PeriodoAcessoClienteVO;
import negocio.comuns.financeiro.AulaAvulsaDiariaVO;
import negocio.comuns.financeiro.CartaoCreditoVO;
import negocio.comuns.financeiro.ChequeVO;
import negocio.comuns.financeiro.EstornoReciboVO;
import negocio.comuns.financeiro.FormaPagamentoVO;
import negocio.comuns.financeiro.ItemVendaAvulsaVO;
import negocio.comuns.financeiro.MovPagamentoVO;
import negocio.comuns.financeiro.MovParcelaVO;
import negocio.comuns.financeiro.MovProdutoParcelaVO;
import negocio.comuns.financeiro.PagamentoMovParcelaVO;
import negocio.comuns.financeiro.ReciboPagamentoVO;
import negocio.comuns.financeiro.VendaAvulsaVO;
import negocio.comuns.financeiro.enumerador.TipoFormaPagto;
import negocio.comuns.plano.HorarioVO;
import negocio.comuns.plano.ModalidadeVO;
import negocio.comuns.plano.PlanoCondicaoPagamentoVO;
import negocio.comuns.plano.PlanoDuracaoVO;
import negocio.comuns.plano.PlanoHorarioVO;
import negocio.comuns.plano.PlanoModalidadeVO;
import negocio.comuns.plano.PlanoModalidadeVezesSemanaVO;
import negocio.comuns.plano.PlanoVO;
import negocio.comuns.plano.ProdutoVO;
import negocio.comuns.plano.enumerador.TipoDesconto;
import negocio.comuns.plano.enumerador.TipoProduto;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.Ordenacao;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.Log;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.arquitetura.ZillyonWebFacade;
import negocio.facade.jdbc.basico.Cliente;
import negocio.facade.jdbc.basico.ContratoDependente;
import negocio.facade.jdbc.basico.Familiar;
import negocio.facade.jdbc.basico.Pessoa;
import negocio.facade.jdbc.basico.Vinculo;
import negocio.facade.jdbc.contrato.Contrato;
import negocio.facade.jdbc.contrato.ContratoOperacao;
import negocio.facade.jdbc.contrato.HistoricoContrato;
import negocio.facade.jdbc.contrato.MovProduto;
import negocio.facade.jdbc.contrato.MovProdutoModalidade;
import negocio.facade.jdbc.contrato.PeriodoAcessoCliente;
import negocio.facade.jdbc.financeiro.AulaAvulsaDiaria;
import negocio.facade.jdbc.financeiro.ItemVendaAvulsa;
import negocio.facade.jdbc.financeiro.MovPagamento;
import negocio.facade.jdbc.financeiro.MovParcela;
import negocio.facade.jdbc.financeiro.MovProdutoParcela;
import negocio.facade.jdbc.financeiro.ReciboPagamento;
import negocio.facade.jdbc.financeiro.VendaAvulsa;
import negocio.facade.jdbc.plano.PlanoCondicaoPagamento;
import negocio.facade.jdbc.plano.PlanoDuracao;
import negocio.facade.jdbc.plano.Produto;
import negocio.facade.jdbc.utilitarias.Conexao;
import relatorio.negocio.comuns.sad.SituacaoClienteSinteticoEnum;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

public class ImportadorMembersEvo {

    private final Connection con;
    private final IntegracaoMemberVO integracaoMemberVO;
    private final ImportacaoCache cache;
    private final Cliente clienteDAO;
    private final Pessoa pessoaDAO;
    private final Contrato contratoDAO;
    private final ContratoDependente contratoDependenteDAO;
    private final ContratoOperacao contratoOperacaoDAO;
    private final MovParcela movParcelaDAO;
    private final MovProduto movProdutoDAO;
    private final MovProdutoModalidade movProdutoModalidadeDAO;
    private final ReciboPagamento reciboPagamentoDAO;
    private final MovPagamento movPagamentoDAO;
    private final PlanoDuracao planoDuracaoDAO;
    private final PlanoCondicaoPagamento planoCondicaoPagamentoDAO;
    private final AulaAvulsaDiaria aulaAvulsaDiariaDAO;
    private final ZillyonWebFacade zillyonWebFacadeDAO;
    private final Vinculo vinculoDAO;
    private final Familiar familiarDAO;
    private final Log logDAO;
    private final List<String> falhasItemAtual = new ArrayList<>();
    private final List<String> falhas = new ArrayList<>();
    private final ImportarPagamento importarPagamento;

    public ImportadorMembersEvo(Connection con, IntegracaoMemberVO integracaoMemberVO, ImportacaoCache importacaoCache) throws Exception {
        this.con = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con);
        Conexao.guardarConexaoForJ2SE(this.con);

        this.integracaoMemberVO = integracaoMemberVO;
        this.cache = importacaoCache;

        this.clienteDAO = new Cliente(this.con);
        this.pessoaDAO = new Pessoa(this.con);
        this.contratoDAO = new Contrato(this.con);
        this.contratoDependenteDAO = new ContratoDependente(this.con);
        this.contratoOperacaoDAO = new ContratoOperacao(this.con);
        this.movParcelaDAO = new MovParcela(this.con);
        this.movProdutoDAO = new MovProduto(this.con);
        this.movProdutoModalidadeDAO = new MovProdutoModalidade(this.con);
        this.reciboPagamentoDAO = new ReciboPagamento(this.con);
        this.movPagamentoDAO = new MovPagamento(this.con);
        this.planoDuracaoDAO = new PlanoDuracao(this.con);
        this.planoCondicaoPagamentoDAO = new PlanoCondicaoPagamento(this.con);
        this.aulaAvulsaDiariaDAO = new AulaAvulsaDiaria(this.con);
        this.zillyonWebFacadeDAO = new ZillyonWebFacade(this.con);
        this.vinculoDAO = new Vinculo(this.con);
        this.familiarDAO = new Familiar(this.con);
        this.logDAO = new Log(this.con);
        this.importarPagamento = new ImportarPagamento(this.con);
    }

    public ClienteVO importarCliente(MembersApiViewTO member) throws Exception {
        try {
            con.setAutoCommit(false);

            if (UteisValidacao.emptyNumber(member.getIdMember())) {
                throw new Exception("Necessário que o cliente tenha um IdExterno");
            }

            ClienteVO clienteVO = clienteDAO.consultarPorCodigoMatriculaExterna(member.getIdMember(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            if (!UteisValidacao.emptyNumber(clienteVO.getCodigo())) {
                return clienteVO;
            }

            UteisImportacao uteisImportacao = new UteisImportacao();

            EstadoVO estadoVO;
            CidadeVO cidadeVO = new CidadeVO();
            PaisVO paisVO = new PaisVO();

            String ufCliente = member.getState();
            if (!UteisValidacao.emptyString(ufCliente)) {
                estadoVO = cache.obterEstadoVO(ufCliente);
                if (estadoVO != null && !UteisValidacao.emptyNumber(estadoVO.getCodigo())) {
                    if (!UteisValidacao.emptyNumber(estadoVO.getPais())) {
                        paisVO.setCodigo(estadoVO.getPais());
                    }
                    cidadeVO = uteisImportacao.obterCidade(member.getCity(), estadoVO, con);
                }
            }

            EmpresaVO empresaVO = cache.obterEmpresaVO(integracaoMemberVO.getEmpresa().getCodigo());

            if (cidadeVO != null && !UteisValidacao.emptyNumber(cidadeVO.getCodigo())) {
                estadoVO = cidadeVO.getEstado();
                paisVO = cidadeVO.getPais();
            } else {
                cidadeVO = empresaVO.getCidade();
                estadoVO = empresaVO.getEstado();
                paisVO = empresaVO.getPais();
            }

            PessoaVO pessoaVO = new PessoaVO();
            String nomeCompleto = member.getFirstName() + " " + member.getLastName();
            nomeCompleto = nomeCompleto.length() > 80 ? nomeCompleto.substring(0, 80) : nomeCompleto;
            pessoaVO.setNome(nomeCompleto);

            Date dataCadastro = UteisImportacao.getDateFromLocalDateTime(member.getRegisterDate());

            if (!UteisValidacao.emptyString(member.getBirthDate())) {
                Date dataNascimento = UteisImportacao.getDateFromLocalDateTime(member.getBirthDate());
                pessoaVO.setDataNasc(dataNascimento);
            }

            pessoaVO.setDataCadastro(dataCadastro);
            pessoaVO.setCategoriaPessoa(TipoPessoa.FISICA);
            pessoaVO.setTipoPessoa(TipoPessoa.FISICA.getLabel());

            pessoaVO.setIdExterno(Long.valueOf(member.getIdMember()));
            if (!UteisValidacao.emptyString(member.getGender())) {
                if (member.getGender().toLowerCase().equals("male")) {
                    pessoaVO.setSexo("M");
                } else if (member.getGender().toLowerCase().equals("female")) {
                    pessoaVO.setSexo("F");
                }
            }
            pessoaVO.setTipoPessoa(TipoPessoaEnum.ALUNO.getTipo());
            pessoaVO.setNacionalidade("");
            pessoaVO.setNaturalidade("");
            pessoaVO.setRg(member.getDocumentId());
            String document = Uteis.removerMascara(member.getDocument());
            if (document.trim().length() == 11) {
                pessoaVO.setCfp(Uteis.formatarCpfCnpj(document, false));
            }
            if (!UteisValidacao.emptyString(member.getPassport()) && member.getPassport().length() <= 32) {
                pessoaVO.setPassaporte(member.getPassport());
            }
            pessoaVO.setPais(paisVO);
            pessoaVO.setEstadoVO(estadoVO);
            pessoaVO.setCidade(cidadeVO);

            EnderecoVO endVO = new EnderecoVO();
            endVO.setCep(uteisImportacao.obterCEPMascarado(member.getZipCode()));
            endVO.setTipoEndereco(TipoEnderecoEnum.RESIDENCIAL.getCodigo());
            endVO.setBairro(member.getNeighborhood());
            endVO.setComplemento(member.getComplement());
            if (!UteisValidacao.emptyString(member.getNumber())) {
                if (member.getNumber().length() <= 10) {
                    endVO.setNumero(member.getNumber());
                } else {
                    endVO.setComplemento(UteisValidacao.emptyString(endVO.getComplemento()) ?
                            member.getNumber() : endVO.getComplemento() + " - " + member.getNumber());
                }
            }
            if (!UteisValidacao.emptyString(endVO.getComplemento())
                    && endVO.getComplemento().length() > 100) {
                endVO.setComplemento(endVO.getComplemento().substring(0, 100));
            }
            endVO.setEndereco(member.getAddress());
            endVO.setEnderecoCorrespondencia(false);
            pessoaVO.getEnderecoVOs().add(endVO);

            List<TelefoneVO> telefoneVOS = new ArrayList<>();
            List<EmailVO> emailVOS = new ArrayList<>();
            for (ContactsApiViewTO contactVO : member.getContacts()) {
                try {
                    ContactTypeEnum contactTypeEnum = ContactTypeEnum.obterPorId(contactVO.getIdContactType());
                    switch (contactTypeEnum) {
                        case TELEPHONE:
                            TelefoneVO telResidencial = uteisImportacao.obterTelefone(contactVO.getDescription(), TipoTelefoneEnum.RESIDENCIAL, integracaoMemberVO.getDddPadrao());
                            if (telResidencial != null) {
                                telefoneVOS.add(telResidencial);
                            }
                            break;
                        case CELLPHONE:
                            TelefoneVO telCelular = uteisImportacao.obterTelefone(contactVO.getDescription(), TipoTelefoneEnum.CELULAR, integracaoMemberVO.getDddPadrao());
                            if (telCelular != null) {
                                telefoneVOS.add(telCelular);
                            }
                            break;
                        case EMAIL:
                            EmailVO emailVO = new EmailVO();
                            emailVO.setEmail(contactVO.getDescription());
                            emailVO.setEmailCorrespondencia(false);
                            emailVOS.add(emailVO);
                            break;
                        default:
                            break;

                    }
                } catch (Exception ex) {
                    ex.printStackTrace();
                }
            }

            pessoaVO.setTelefoneVOs(telefoneVOS);
            pessoaVO.setEmailVOs(emailVOS);

            //incluir pessoa
            pessoaDAO.incluirPessoaImportacao(pessoaVO);
            if (UteisValidacao.emptyNumber(pessoaVO.getCodigo())) {
                throw new Exception("Pessoa não foi incluida!");
            }

            clienteVO.setIdExterno(Long.valueOf(member.getIdMember()));
            clienteVO.setMatriculaExterna(Long.valueOf(member.getIdMember()));
            clienteVO.setPessoa(pessoaVO);
            clienteVO.setEmpresa(empresaVO);

            List<VinculoVO> vinculoVOS = new ArrayList<>();
            VinculoVO vinculoConsultor = new VinculoVO();
            vinculoConsultor.getColaborador().setCodigo(integracaoMemberVO.getConsultorPadrao());
            vinculoConsultor.setTipoVinculo(TipoColaboradorEnum.CONSULTOR.getSigla());
            vinculoVOS.add(vinculoConsultor);

            VinculoVO vinculoProfessor = new VinculoVO();
            vinculoProfessor.getColaborador().setCodigo(integracaoMemberVO.getProfessorPadrao());
            vinculoProfessor.setTipoVinculo(TipoColaboradorEnum.PROFESSOR_TREINO.getSigla());
            vinculoVOS.add(vinculoProfessor);

            clienteVO.setVinculoVOs(vinculoVOS);
            uteisImportacao = null;

            clienteVO.setGympasUniqueToken(member.getGympassId());

            //incluir cliente
            clienteDAO.gerarNumeroMatricula(clienteVO, clienteVO.getEmpresa(), null);
            clienteDAO.incluirClienteImportacao(clienteVO);


            if (clienteVO.getCodigoMatricula().intValue() != clienteVO.getMatriculaExterna().intValue()
                    && podeUtilizarMatriculaExterna(clienteVO.getMatriculaExterna().intValue(), clienteVO.getEmpresa().getCodigo())) {
                AplicarMatriculaImportacao.processar(con, clienteVO.getCodigo());
            }

            //incluir vinculos
            if (!UteisValidacao.emptyList(clienteVO.getVinculoVOs())) {
                vinculoDAO.incluirVinculo(clienteVO.getCodigo(), clienteVO.getVinculoVOs(), "CLIENTE", null, false);
            }

            Log logDao = new Log(con);
            logDao.incluirLogItemImportacao("CLIENTE", clienteVO.getCodigo(), cache.getUsuarioVOImportacao(), clienteVO.getPessoa().getCodigo());

            if (UteisValidacao.emptyNumber(clienteVO.getCodigo())) {
                throw new Exception("Cliente não importado.");
            }
            con.commit();
            return clienteVO;
        } catch (Exception ex) {
            ex.printStackTrace();
            con.rollback();
            throw ex;
        } finally {
            con.setAutoCommit(true);
        }
    }

    public void importarCadastroDependentes(List<MembersApiViewTO> membersApiViewTOS) throws Exception {
        for (MembersApiViewTO member : membersApiViewTOS) {
            importarCliente(member);
        }
    }

    private boolean podeUtilizarMatriculaExterna(Integer matriculaExterna, Integer codigoEmpresa) throws Exception {
        ResultSet rs = SuperFacadeJDBC.criarConsulta("select pes.idexterno, cli.codigo, cli.situacao, cli.matriculaexterna, cli.empresa from cliente cli \n" +
                " inner join pessoa pes on pes.codigo = cli.pessoa \n" +
                "where cli.codigomatricula = " + matriculaExterna, con);
        if (rs.next()) {
            if (rs.getInt("empresa") != codigoEmpresa) {
                return false;
            }
            ClienteVO clienteVO = clienteDAO.consultarPorCodigo(rs.getInt("codigo"), false, Uteis.NIVELMONTARDADOS_SITUACAOCLIENTESINTETICODW);
            Integer novaMatricula = clienteDAO.obterMatriculaAluno() + 1;
            String matriculaMascara = Uteis.getMontarMatricula(novaMatricula.toString(), cache.obterEmpresaVO(codigoEmpresa).getMascaraMatricula().length());
            clienteVO.setMatricula(matriculaMascara);
            clienteVO.setCodigoMatricula(novaMatricula);
            SuperFacadeJDBC.executarConsulta("update cliente set codigomatricula = " + novaMatricula + ", \n" +
                    "matricula = '" + matriculaMascara + "' where codigo = " + rs.getInt("codigo"), con);
            SuperFacadeJDBC.executarConsulta("update numeromatricula set matricula = " + novaMatricula, con);
            zillyonWebFacadeDAO.atualizarSintetico(clienteVO, Calendario.hoje(), SituacaoClienteSinteticoEnum.GRUPO_CLIENTE, false);
        }
        return true;
    }

    public void importarVendasContratosProdutosServicos(ClienteVO clienteVO, List<SalesApiViewTO> sales, MembersApiViewTO memberApiViewTO) throws Exception {
        Ordenacao.ordenarLista(sales, "idSale");

        Map<Integer, MemberMembershipApiViewTO> mapMemberShip = new HashMap<>(); // key = idMemberMembership
        for (MemberMembershipApiViewTO memberShip : memberApiViewTO.getMemberships()) {
            mapMemberShip.put(memberShip.getIdMemberMembership(), memberShip);
        }

        for (MemberMembershipApiViewTO memberShip : memberApiViewTO.getMemberships()) {
            verificarCancelamentoContrato(clienteVO, memberShip, mapMemberShip); // importar cancelamentos gerados no EVO após a ultima importação
        }

        for (SalesApiViewTO sale : sales) {
            Map<Integer, List<MemberMembershipApiViewTO>> mapMemberShipSale = new HashMap<>(); // key = idSale
            for(SaleItemApiViewTO saleItem: sale.getSaleItens()) {
                if (!UteisValidacao.emptyNumber(saleItem.getIdMemberMembership())) {
                    if (mapMemberShipSale.get(sale.getIdSale()) == null) {
                        mapMemberShipSale.put(sale.getIdSale(), new ArrayList<>());
                    }
                    MemberMembershipApiViewTO memberMembershipApiViewTO = mapMemberShip.get(saleItem.getIdMemberMembership());
                    if (memberMembershipApiViewTO == null) {
                        if (saleItem.getDescription().toLowerCase().contains("dependente")
                                || saleItem.getDescription().toLowerCase().contains("amigo")) {
                            continue;
                        }
                        throw new Exception(String.format("Contrato %s - %s não encontrado para a venda %s", saleItem.getIdMemberMembership(), saleItem.getDescription(), sale.getIdSale()));
                    }
                    memberMembershipApiViewTO.setIdSale(sale.getIdSale());
                    mapMemberShipSale.get(sale.getIdSale()).add(memberMembershipApiViewTO);
                }
            }

            processarContratos(clienteVO, sale, mapMemberShipSale, mapMemberShip, integracaoMemberVO);
            processarVendaAulaAvulsaDiaria(clienteVO, sale, mapMemberShipSale, integracaoMemberVO.getModalidadePadrao());
            processarVendaProdutoServico(clienteVO, sale, mapMemberShipSale);
        }
    }

    private void processarVendaProdutoServico(ClienteVO clienteVO, SalesApiViewTO sale, Map<Integer, List<MemberMembershipApiViewTO>> mapMemberShipSale) throws Exception {
        ResultSet rsVendaJaImportada = SuperFacadeJDBC.criarConsulta("select codigo from vendaavulsa where id_movimento = '" + sale.getIdSale() + "'", con);
        if (rsVendaJaImportada.next()) {
            return;
        }

        List<MemberMembershipApiViewTO> listMemberShips = mapMemberShipSale.get(sale.getIdSale());

        Integer idMemberShipReceivables = !UteisValidacao.emptyList(listMemberShips) ? obterIdMemberShipReceivables(listMemberShips, sale.getSaleItens()) : 0;

        boolean importarPagamentos = UteisValidacao.emptyNumber(idMemberShipReceivables);

        VendaAvulsa vendaAvulsaDAO = new VendaAvulsa(con);
        ItemVendaAvulsa itemVendaAvulsaDAO = new ItemVendaAvulsa(con);
        Date dataVenda = UteisImportacao.getDateFromLocalDateTime(sale.getSaleDate());

        VendaAvulsaVO vendaAvulsaVO = new VendaAvulsaVO();
        vendaAvulsaVO.setValidarDados(false);
        vendaAvulsaVO.setOrigemSistema(OrigemSistemaEnum.ZW);
        vendaAvulsaVO.setTipoComprador("CI");
        vendaAvulsaVO.setCliente(clienteVO);
        vendaAvulsaVO.setDataRegistro(dataVenda);
        vendaAvulsaVO.setEmpresa(clienteVO.getEmpresa());
        vendaAvulsaVO.setNomeComprador(vendaAvulsaVO.getCliente().getPessoa().getNome());
        vendaAvulsaVO.setResponsavel(cache.getUsuarioVOImportacao());

        vendaAvulsaVO.setItemVendaAvulsaVOs(new ArrayList<>());

        for (SaleItemApiViewTO saleItem : sale.getSaleItens()) {
            TipoProduto tipoProduto;
            String descricao;
            if (!UteisValidacao.emptyNumber(saleItem.getIdService())) {
                tipoProduto = TipoProduto.SERVICO;
                descricao = UteisValidacao.emptyString(saleItem.getItem()) ? "Importação - Serviço" : saleItem.getItem();
            } else if (!UteisValidacao.emptyNumber(saleItem.getIdProduct())) {
                tipoProduto = TipoProduto.PRODUTO_ESTOQUE;
                descricao = "Importação - Produto";
            } else {
                continue;
            }
            ProdutoVO produtoVO = cache.obterProdutoVendaAvulsa(descricao, tipoProduto);
            produtoVO.setValorFinal(importarPagamentos ? saleItem.getItemValue() : 0.0);
            ItemVendaAvulsaVO item = new ItemVendaAvulsaVO();
            item.setDataVenda(dataVenda);
            item.setQuantidade(saleItem.getQuantity());
            item.setUsuarioVO(cache.getUsuarioVOImportacao());
            item.setProduto(produtoVO);
            vendaAvulsaVO.getItemVendaAvulsaVOs().add(item);
        }
        if (UteisValidacao.emptyList(vendaAvulsaVO.getItemVendaAvulsaVOs())) {
            return;
        }

        if (!importarPagamentos) {
            Integer codigo = vendaAvulsaDAO.incluirSemCommit(vendaAvulsaVO, true, dataVenda);
            preencherIdVendaVendaAvulsa(sale.getIdSale(), codigo, con);
        } else {
            try (ResultSet rs = vendaAvulsaDAO.incluirSemCommitSomenteVendaAvulsa(vendaAvulsaVO)) {
                if (rs.next()) {
                    preencherIdVendaVendaAvulsa(sale.getIdSale(), rs.getInt("codigo"), con);

                    Map<Integer, ReceivablesViewTO> mapReceivables = new HashMap<>();
                    sale.getReceivables().forEach(r -> mapReceivables.put(r.getIdReceivable(), r));

                    for (ItemVendaAvulsaVO itemVendaAvulsaVO : vendaAvulsaVO.getItemVendaAvulsaVOs()) {
                        itemVendaAvulsaDAO.incluir(itemVendaAvulsaVO);
                    }
                    vendaAvulsaVO.setCodigo(rs.getInt("codigo"));
                    gerarParcelasVenda(sale.getReceivables(), clienteVO, null, vendaAvulsaVO, dataVenda);
                    gerarMovProdutoVendaAvulsa(vendaAvulsaVO, cache.getUsuarioVOImportacao(), "EA", dataVenda);
                    gerarMovprodutoParcela(vendaAvulsaVO.getMovParcelaVOs(), vendaAvulsaVO.getMovProdutoVOs());
                    gerarPagamentoParcelas(vendaAvulsaVO.getMovParcelaVOs(), mapReceivables);

                    if (UteisValidacao.emptyList(vendaAvulsaVO.getMovParcelaVOs())) {
                        SuperFacadeJDBC.executarUpdate("update movproduto set situacao = 'CA' where vendaavulsa = " + vendaAvulsaVO.getCodigo(), con);
                    }
                }
            }
        }
    }

    private void gerarMovProdutoVendaAvulsa(VendaAvulsaVO vendaAvulsaVO, UsuarioVO usuario, String situacao, Date dataReferencia) throws Exception {
        for (ItemVendaAvulsaVO itemVenda : vendaAvulsaVO.getItemVendaAvulsaVOs()) {
            MovProdutoVO movProdutoVO = new MovProdutoVO();
            movProdutoVO.setProduto(itemVenda.getProduto());
            movProdutoVO.setRenovavelAutomaticamente(itemVenda.getProduto().getRenovavelAutomaticamente());
            movProdutoVO.setApresentarMovProduto(false);
            movProdutoVO.setDescricao(!vendaAvulsaVO.getDescricaoAdicional().isEmpty() ? vendaAvulsaVO.getDescricaoAdicional() : itemVenda.getProduto().getDescricao());
            movProdutoVO.setQuantidade(itemVenda.getQuantidade());
            movProdutoVO.setMesReferencia(Uteis.getMesReferenciaData(dataReferencia));
            movProdutoVO.setAnoReferencia(Uteis.getAnoData(dataReferencia));
            movProdutoVO.setDataInicioVigencia(itemVenda.getDataVenda());
            movProdutoVO.setDataFinalVigencia(itemVenda.getDataValidade());
            movProdutoVO.setVigenciaJaCalculada(itemVenda.getVigenciaJaCalculada());
            movProdutoVO.setDataLancamento(vendaAvulsaVO.getDataRegistro());
            movProdutoVO.setResponsavelLancamento(usuario);
            if (situacao.equals("EA")) {
                movProdutoVO.setMovpagamentocc(itemVenda.getMovpagamentos());
            }
            if (itemVenda.getTabelaDesconto().getValor() != 0.0 && itemVenda.getTabelaDesconto().getTipoDesconto().equals(TipoDesconto.PE)) {
                movProdutoVO.setValorDesconto(Uteis.arredondarForcando2CasasDecimais(itemVenda.getQuantidade() * (itemVenda.getProduto().getValorFinal() * (itemVenda.getTabelaDesconto().getValor() / 100))));
            } else if (itemVenda.getTabelaDesconto().getValor() != 0.0 && itemVenda.getTabelaDesconto().getTipoDesconto().equals(TipoDesconto.VA)) {
                movProdutoVO.setValorDesconto(itemVenda.getTabelaDesconto().getValor());
            } else if (itemVenda.getDescontoManual()) {
                movProdutoVO.setValorDesconto(itemVenda.getValorDescontoManual());
            } else {
                movProdutoVO.setValorDesconto(0.0);
            }
            movProdutoVO.setPrecoUnitario(itemVenda.getProduto().getValorFinal());
            movProdutoVO.setTotalFinal(Uteis.arredondarForcando2CasasDecimais((movProdutoVO.getPrecoUnitario() * movProdutoVO.getQuantidade()) - movProdutoVO.getValorDesconto()));
            movProdutoVO.setValorFaturado(movProdutoVO.getTotalFinal());
            movProdutoVO.setVendaAvulsa(vendaAvulsaVO.getCodigo());
            movProdutoVO.setEmpresa(vendaAvulsaVO.getEmpresa());
            if (vendaAvulsaVO.getTipoComprador().equals("CI")) {
                movProdutoVO.setPessoa(vendaAvulsaVO.getCliente().getPessoa());
            }
            if (vendaAvulsaVO.getTipoComprador().equals("CO")) {
                movProdutoVO.setPessoa(vendaAvulsaVO.getColaborador().getPessoa());
            }
            if (situacao.equals("PG")) {
                movProdutoVO.setSituacao("PG");
            } else {
                movProdutoVO.setSituacao("EA");
            }
            movProdutoVO.setQuitado(true); // apenas para inclusão
            movProdutoDAO.incluirSemCommit(movProdutoVO);
            movProdutoVO.setQuitado(false); // para gerar o relacionamento entre produtos e parcelas corretamente
            vendaAvulsaVO.getMovProdutoVOs().add(movProdutoVO);
        }
    }


    private static void preencherIdVendaVendaAvulsa(int idVenda, Integer codigo, Connection con) {
        try {
            con.createStatement().execute("UPDATE vendaavulsa SET id_movimento = " + idVenda + " WHERE codigo = " + codigo);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public void processarVendaAulaAvulsaDiaria(ClienteVO clienteVO, SalesApiViewTO sale, Map<Integer, List<MemberMembershipApiViewTO>> mapMemberShip, Integer codModalidade) throws Exception {
        try {
            con.setAutoCommit(false);

            List<MemberMembershipApiViewTO> listMemberShips = mapMemberShip.get(sale.getIdSale());

            if (UteisValidacao.emptyList(listMemberShips)) {
                return;
            }

            Integer idMemberShipReceivables = obterIdMemberShipReceivables(listMemberShips, sale.getSaleItens());

            List<MemberMembershipApiViewTO> listMemberShipsDiaria = mapMemberShip.get(sale.getIdSale());
            listMemberShipsDiaria = listMemberShipsDiaria.stream()
                    .filter(m -> Uteis.nrDiasEntreDatas(m.getDataInicio(), m.getDataFim()) <= 25)
                    .collect(Collectors.toList());

            if (UteisValidacao.emptyList(listMemberShipsDiaria)) {
                return;
            }


            // Descobrir o sale item de cada member ship diaria
            Map<Integer, SaleItemApiViewTO> mapaMemberShipSaleItens = obterMapaMemberShipSaleItens(listMemberShipsDiaria, sale.getSaleItens());

            ResultSet rsJaImportada = SuperFacadeJDBC.criarConsulta("select codigo from aulaavulsadiaria where id_venda = '" + sale.getIdSale() + "'", con);
            if (rsJaImportada.next()) {
                return;
            }

            boolean isImportReceivables;
            List<AulaAvulsaDiariaVO> aulaAvulsaDiariaVOS = new ArrayList<>();
            List<MovProdutoVO> movProdutoVOS = new ArrayList<>();
            List<MovParcelaVO> movParcelaVOS = new ArrayList<>();

            for (MemberMembershipApiViewTO memberShip : listMemberShipsDiaria) {
                isImportReceivables = memberShip.getIdMemberMembership().equals(idMemberShipReceivables);
                SaleItemApiViewTO saleItem = mapaMemberShipSaleItens.get(memberShip.getIdMemberMembership());
                if (saleItem == null) {
                    continue;
                }

                if (!isImportReceivables && (memberShip.getName().toLowerCase().contains("dobro") || memberShip.getName().toLowerCase().contains("pedido de transfer"))) {
                    continue;
                }

                AulaAvulsaDiariaVO aulaAvulsaDiariaVO = new AulaAvulsaDiariaVO();
                aulaAvulsaDiariaVO.setCliente(clienteVO);
                aulaAvulsaDiariaVO.setModalidade(cache.obterModalidadeVO(codModalidade));

                Produto produtoDAO = new Produto(con);
                ProdutoVO produtoDiaria = produtoDAO.criarOuConsultarProdutoPorTipo(TipoProduto.DIARIA.getCodigo(), "Diaria Importação", Uteis.NIVELMONTARDADOS_DADOSBASICOS);

                Date startDate = UteisImportacao.getDateFromLocalDateTime(memberShip.getStartDate());
                Date endDate = UteisImportacao.getDateFromLocalDateTime(memberShip.getEndDate());

                Long dias = Uteis.nrDiasEntreDatas(startDate, endDate);
                produtoDiaria.setNrDiasVigencia(dias.intValue());

                aulaAvulsaDiariaVO.setProduto(produtoDiaria);
                aulaAvulsaDiariaVO.setDataInicio(startDate);
                aulaAvulsaDiariaVO.setEmpresa(clienteVO.getEmpresa());
                if (isImportReceivables) {
                    produtoDiaria.setValorFinal(saleItem.getItemValue());
                    aulaAvulsaDiariaVO.setValor(saleItem.getItemValue());
                } else {
                    aulaAvulsaDiariaVO.setValor(0.0);
                    produtoDiaria.setValorFinal(0.0);
                }
                aulaAvulsaDiariaVO.setResponsavel(cache.getUsuarioVOImportacao());
                aulaAvulsaDiariaVO.setNomeComprador(clienteVO.getPessoa().getNome());
                aulaAvulsaDiariaVO.setDataRegistro(UteisImportacao.getDateFromLocalDateTime(sale.getSaleDate()));
                aulaAvulsaDiariaVO.setDataLancamento(aulaAvulsaDiariaVO.getDataRegistro());
                aulaAvulsaDiariaVO.setUsuarioVO(cache.getUsuarioVOImportacao());
                aulaAvulsaDiariaDAO.incluirSemCommit(aulaAvulsaDiariaVO, OrigemSistemaEnum.ZW, false);

                preencherIdVendaAulaAvulsaDiaria(sale.getIdSale(), saleItem.getIdSaleItem(), aulaAvulsaDiariaVO.getCodigo(), con);
                aulaAvulsaDiariaVOS.add(aulaAvulsaDiariaVO);

                MovProdutoVO movProdutoVO = gerarMovProdutoVendaAulaAvulsaDiaria(aulaAvulsaDiariaVO);
                movProdutoVOS.add(movProdutoVO);
                if (isImportReceivables) {
                    movParcelaVOS = gerarParcelasVenda(sale.getReceivables(), clienteVO, aulaAvulsaDiariaVOS.get(0), null, aulaAvulsaDiariaVOS.get(0).getDataRegistro());
                }

                if (UteisValidacao.emptyList(movParcelaVOS)) {
                    MovParcelaVO movParcelaVO = new MovParcelaVO();
                    movParcelaVO.setAulaAvulsaDiariaVO(aulaAvulsaDiariaVO);
                    movParcelaVO.setDescricao("Diaria");
                    movParcelaVO.setDataRegistro(aulaAvulsaDiariaVO.getDataRegistro());
                    movParcelaVO.setDataVencimento(aulaAvulsaDiariaVO.getDataRegistro());
                    movParcelaVO.setResponsavel(cache.getUsuarioVOImportacao());
                    movParcelaVO.setSituacao("PG");
                    movParcelaVO.setValorParcela(0.0);
                    movParcelaVO.setPercentualJuro(0.0);
                    movParcelaVO.setPercentualMulta(0.0);
                    movParcelaVO.setPessoa(clienteVO.getPessoa());
                    movParcelaVO.setEmpresa(clienteVO.getEmpresa());
                    movParcelaDAO.incluirParcelaSemCommit(movParcelaVO);
                    movParcelaVOS.add(movParcelaVO);
                }
            }

            Map<Integer, ReceivablesViewTO> mapReceivables = new HashMap<>();
            sale.getReceivables().forEach(r -> mapReceivables.put(r.getIdReceivable(), r));

            gerarMovprodutoParcela(movParcelaVOS, movProdutoVOS);
            gerarPagamentoParcelas(movParcelaVOS, mapReceivables);
            con.commit();
        } catch (Exception ex) {
            ex.printStackTrace();
            con.rollback();
            throw ex;
        } finally {
            con.setAutoCommit(true);
        }
    }

    private static void preencherIdVendaAulaAvulsaDiaria(int idVenda, int idExternoItemVenda, Integer codigo, Connection con) {
        try {
            String sql = "UPDATE aulaavulsadiaria SET id_venda = ?, idExternoItemVenda = ? WHERE codigo = ?";
            PreparedStatement pstm = con.prepareStatement(sql);
            pstm.setInt(1, idVenda);
            pstm.setInt(2, idExternoItemVenda);
            pstm.setInt(3, codigo);
            pstm.execute();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private MovProdutoVO gerarMovProdutoVendaAulaAvulsaDiaria(final AulaAvulsaDiariaVO obj) throws Exception {
        MovProdutoVO movProdutoVO = new MovProdutoVO();
        movProdutoVO.setProduto(obj.getProduto());
        movProdutoVO.setApresentarMovProduto(false);
        movProdutoVO.setDescricao(obj.getProduto().getDescricao());
        movProdutoVO.setMesReferencia(Uteis.getMesReferenciaData(Calendario.hoje()));
        movProdutoVO.setRenovavelAutomaticamente(obj.getProduto().getRenovavelAutomaticamente());
        movProdutoVO.setQuantidade(1);
        movProdutoVO.setAnoReferencia(Uteis.getAnoData(Calendario.hoje()));
        if (obj.getProduto().getDataInicioVigencia() == null) {
            movProdutoVO.setDataInicioVigencia(obj.getDataInicio());
        } else {
            movProdutoVO.setDataInicioVigencia(obj.getProduto().getDataInicioVigencia());
        }
        movProdutoVO.setDataFinalVigencia(obj.getProduto().getDataFinalVigencia());
        movProdutoVO.setDataLancamento(obj.getDataLancamento());
        movProdutoVO.setResponsavelLancamento(obj.getResponsavel());
        movProdutoVO.setPrecoUnitario(obj.getValor());
        movProdutoVO.setEmpresa(obj.getEmpresa());
        movProdutoVO.setPessoa(obj.getCliente().getPessoa());
        movProdutoVO.setQuitado(false);
        movProdutoVO.setSituacao("EA");
        if (movProdutoVO.getPrecoUnitario().equals(0.0)) {
            movProdutoVO.setSituacao("PG");
        }
        movProdutoVO.setValorDesconto(0.0);
        movProdutoVO.setTotalFinal(Uteis.arredondarForcando2CasasDecimais((movProdutoVO.getPrecoUnitario() - movProdutoVO.getValorDesconto())));
        movProdutoVO.setValorFaturado(movProdutoVO.getTotalFinal());
        movProdutoVO.setValorPagoMovProdutoParcela(movProdutoVO.getTotalFinal());
        movProdutoDAO.incluirSemCommit(movProdutoVO);

        return movProdutoVO;
    }

    public void processarContratos(ClienteVO clienteVO, SalesApiViewTO sale, Map<Integer, List<MemberMembershipApiViewTO>> mapMemberShipSale, Map<Integer, MemberMembershipApiViewTO> mapMemberShip, IntegracaoMemberVO integracaoMemberVO) throws Exception {
        List<MemberMembershipApiViewTO> listMemberShips = mapMemberShipSale.get(sale.getIdSale());
        Integer idMemberShipReceivables;
        if (!UteisValidacao.emptyList(listMemberShips)) {
            Ordenacao.ordenarLista(listMemberShips, "idMemberMembership");
            idMemberShipReceivables = obterIdMemberShipReceivables(listMemberShips, sale.getSaleItens());
            for (MemberMembershipApiViewTO memberShip : listMemberShips) {
                boolean isImportarReceivables = memberShip.getIdMemberMembership().equals(idMemberShipReceivables);
                memberShip.setDataFim(UteisImportacao.getDateFromLocalDateTime(memberShip.getEndDate()));

                Long dias = Uteis.nrDiasEntreDatas(memberShip.getDataInicio(), memberShip.getDataFim());
                if (dias > 25) {
                    ContratoVO contratoVO = importarContrato(clienteVO, memberShip, sale, isImportarReceivables, integracaoMemberVO);
                    if (contratoVO != null && !UteisValidacao.emptyNumber(contratoVO.getCodigo())) {
                        processarCessaoDireitoDeUso(clienteVO, contratoVO, memberShip);
                        processarCompartilhamentoComDependentes(contratoVO, memberShip.getIdMemberMembership(), mapMemberShip);
                    }
                }
            }
        }
    }

    public ContratoVO importarContrato(ClienteVO clienteVO, MemberMembershipApiViewTO memberShip, SalesApiViewTO sale, boolean isImportReceivables, IntegracaoMemberVO integracaoMemberVO) throws Exception {
        ContratoVO contratoVO;
        try {
            con.setAutoCommit(false);

            Integer codigoContrato = this.cache.getContratosImportados().get(memberShip.getIdMemberMembership());
            if (!UteisValidacao.emptyNumber(codigoContrato)) {
                return contratoDAO.consultarPorCodigo(codigoContrato, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            }

            codigoContrato = obterContratoRecorrenciaUnificadoProximasCobrancasEvo(clienteVO.getMatriculaExterna(), memberShip);
            if (!UteisValidacao.emptyNumber(codigoContrato)) {
                return contratoDAO.consultarPorCodigo(codigoContrato, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            }

            ContratoVO contratoRenovar = null;
            if (!UteisValidacao.emptyNumber(memberShip.getIdMemberMembershipRenewed())) {
                ResultSet rsContratoRenovar = SuperFacadeJDBC.criarConsulta("SELECT codigo FROM contrato WHERE coalesce(id_externo,idExterno) = " + memberShip.getIdMemberMembershipRenewed()
                        + " and empresa = " + clienteVO.getEmpresa().getCodigo(), con);
                if (rsContratoRenovar.next()) {
                    contratoRenovar = contratoDAO.consultarPorCodigo(rsContratoRenovar.getInt("codigo"), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                }
            }

            contratoVO = montarContrato(memberShip, sale, isImportReceivables, contratoRenovar, clienteVO, integracaoMemberVO);

            // Não importar contrato do plano em dobro zerado, pois esse é o contrato dependente
            if (contratoVO.getValorFinal() == 0.0 && (memberShip.getName().toLowerCase().contains("dobro") || contratoVO.getPlano().getQuantidadeCompartilhamentos() > 0)) {
                return contratoVO;
            }

            ImportarContrato importarContrato = new ImportarContrato(null, con);
            importarContrato.incluirSemCommit(contratoVO);
            contratoVO.setPessoa(clienteVO.getPessoa());
            this.cache.getContratosImportados().put(memberShip.getIdMemberMembership(), contratoVO.getCodigo());

            if (contratoVO != null && !UteisValidacao.emptyNumber(contratoVO.getCodigo())) {
                Map<Integer, ReceivablesViewTO> mapReceivables = new HashMap<>();
                sale.getReceivables().forEach(r -> mapReceivables.put(r.getIdReceivable(), r));

                // Historico Contrato
                gerarHistoricoContrato(contratoVO);

                if (UteisValidacao.emptyNumber(contratoVO.getContratoBaseadoRenovacao())) {
                    atualizarContratoBaseadoRenovacao(contratoVO.getContratoBaseadoRenovacao(), contratoVO.getCodigo());
                }

                // Contrato dependente
                if (contratoVO.getPlano().getQuantidadeCompartilhamentos() > 0) {
                    ContratoDependente contratoDependenteDAO = new ContratoDependente(con);
                    contratoDependenteDAO.incluir(contratoVO);
                }

                String descricaoContrato = memberShip.getName().toLowerCase();
                boolean possuiCancelamento = contratoVO.getSituacao().equalsIgnoreCase("CA") || descricaoContrato.contains("cancelled") || descricaoContrato.contains("cancel");

                // Parcelas
                List<MovParcelaVO> movParcelaVOS = gerarParcelas(isImportReceivables ? sale.getReceivables() : new ArrayList<>(), contratoVO, possuiCancelamento);
                atualizarValorContrato(contratoVO, movParcelaVOS);
                // Produtos
                List<MovProdutoVO> movProdutoVOS = gerarMovProdutosMembers(contratoVO);
                gerarMovProdutoModalidade(contratoVO, movProdutoVOS);
                // Relacionamento entre produto e parcelas
                gerarMovprodutoParcela(movParcelaVOS, movProdutoVOS);
                verificarMovProdutosCancelados(contratoVO, movProdutoVOS);
                // Pagamentos
                if (isImportReceivables) {
                    gerarPagamentoParcelas(contratoVO, movParcelaVOS, mapReceivables);
                }

                processarCancelamento(contratoVO, memberShip);

                gerarHistoricoTemporalContrato(contratoVO);

                logDAO.incluirLogItemImportacao("CONTRATO", contratoVO.getCodigo(), cache.getUsuarioVOImportacao(), contratoVO.getPessoa().getCodigo());

            } else {
                throw new Exception("Contrato não importado.");
            }
            con.commit();
        } catch (Exception ex) {
            con.rollback();
            throw ex;
        } finally {
            con.setAutoCommit(true);
        }
        return contratoVO;
    }

    private Integer obterContratoRecorrenciaUnificadoProximasCobrancasEvo(Long idMember, MemberMembershipApiViewTO memberShip) throws Exception {
        ResultSet rs = SuperFacadeJDBC.criarConsulta("select \n" +
                "\tcon.codigo as contrato,\n" +
                "\tcon.observacao\n" +
                "from contrato con\n" +
                "\tinner join cliente cli on cli.pessoa = con.pessoa\n" +
                "where cli.matriculaexterna = " + idMember + "\n" +
                "and con.vigenciade <= '" + Calendario.getDataAplicandoFormatacao(memberShip.getDataInicio(), "yyyy-MM-dd") + "' \n" +
                "and con.vigenciaateajustada >= '" + Calendario.getDataAplicandoFormatacao(memberShip.getDataInicio(), "yyyy-MM-dd") + "' \n" +
                "and (con.observacao ilike 'plano evo: " + memberShip.getIdMembership() + " -%contratos unificados%' \n" +
                "           \tor con.observacao ilike 'plano evo: " + memberShip.getIdMembership() + " -%Meses adicionados próximas cobranças evo:%')", con);
        if (rs.next()) {
            Uteis.logarDebug(">>>>>>>>>>>>> Contrato unificado(recorrencia proximas cobrancas) encontrado: " + rs.getString("observacao"));
            return rs.getInt("contrato");
        }
        return null;
    }

    private void gerarHistoricoTemporalContrato(ContratoVO contratoVO) throws Exception {
        RoboVO roboVO = new RoboVO();
        roboVO.setUsuarioVO(cache.getUsuarioVOImportacao());
        contratoDAO.processarContrato(contratoVO, integracaoMemberVO.getEmpresa(), roboVO);
    }

    private List<MovParcelaVO> gerarParcelasVenda(ReceivablesViewTO receivable, ClienteVO clienteVO, VendaAvulsaVO vendaAvulsaVO, Date dataLancamento) throws Exception {
        List<ReceivablesViewTO> receivables = new ArrayList<>();
        receivables.add(receivable);
        return gerarParcelasVenda(receivables, clienteVO, null, vendaAvulsaVO, dataLancamento);
    }

    private List<MovParcelaVO> gerarParcelasVenda(List<ReceivablesViewTO> receivables, ClienteVO clienteVO, AulaAvulsaDiariaVO aulaAvulsaDiariaVO, VendaAvulsaVO vendaAvulsaVO, Date dataLancamento) throws Exception {
        List<MovParcelaVO> movParcelaVOS = new ArrayList<>();

        int numeroParcela = 0;
        for (ReceivablesViewTO receivable : receivables) {
            Double valorParcela = receivable.getAmmount();

            PaymentTypeEnum paymentTypeEnum = obterPaymentType(receivable);
            if (paymentTypeEnum == null) {
                throw new ConsistirException("Falha ao obter paymentType do recebivel id: " + receivable.getIdReceivable() + " idvenda: " + receivable.getIdSale() + " - " + receivable.getDescription());
            }
            if (paymentTypeEnum.getId().equals(PaymentTypeEnum.SALES_CREDIT.getId())) {
                continue;
            }

            Date dataVencimento = obterDataVencimentoParcelaFromReceivable(receivable, paymentTypeEnum);

            StatusRecevablesEnum statusRecevablesEnum = StatusRecevablesEnum.obterPorId(receivable.getStatus().getId());
            if (statusRecevablesEnum == null) {
                throw new Exception("Falha ao obter status do recebivel id: " + receivable.getIdReceivable() + " idvenda: " + receivable.getIdSale() + " - " + receivable.getDescription());
            }
            if (statusRecevablesEnum.getId().equals(StatusRecevablesEnum.CANCELED.getId())) {
                continue;
            }

            MovParcelaVO movParcelaVO = new MovParcelaVO();
            if (aulaAvulsaDiariaVO != null && !UteisValidacao.emptyNumber(aulaAvulsaDiariaVO.getCodigo())) {
                movParcelaVO.setAulaAvulsaDiariaVO(aulaAvulsaDiariaVO);
                if (aulaAvulsaDiariaVO.getProduto().getTipoProduto().equals("DI")) {
                    movParcelaVO.setDescricao("Diaria" + (receivables.size() > 1 ? " - Parcela " + (++numeroParcela) : ""));
                } else {
                    movParcelaVO.setDescricao("Aula Avulso" + (receivables.size() > 1 ? " - Parcela " + (++numeroParcela) : ""));
                }
            } else {
                movParcelaVO.setVendaAvulsaVO(vendaAvulsaVO);
                if (receivables.size() == 1) {
                    movParcelaVO.setDescricao(vendaAvulsaVO.getDescricaoAdicional().isEmpty() ? "Venda Avulso" : vendaAvulsaVO.getDescricaoAdicional());
                } else {
                    movParcelaVO.setDescricao(vendaAvulsaVO.getDescricaoAdicional().isEmpty() ? "Venda Avulso - Parcela " + (++numeroParcela) : vendaAvulsaVO.getDescricaoAdicional());
                }
                vendaAvulsaVO.getMovParcelaVOs().add(movParcelaVO);
            }
            movParcelaVO.setDataRegistro(dataLancamento);
            movParcelaVO.setDataVencimento(dataVencimento);
            movParcelaVO.setResponsavel(cache.getUsuarioVOImportacao());
            movParcelaVO.setSituacao("EA");
            if (statusRecevablesEnum.getId().equals(StatusRecevablesEnum.CANCELED.getId())) {
                movParcelaVO.setSituacao("CA");
            } else if (statusRecevablesEnum.getId().equals(StatusRecevablesEnum.RECEIVED.getId())
                    || !UteisValidacao.emptyString(receivable.getAuthorization())) {
                movParcelaVO.setSituacao("PG");
            }
            movParcelaVO.setValorParcela(valorParcela);
            movParcelaVO.setPercentualJuro(0.0);
            movParcelaVO.setPercentualMulta(0.0);
            movParcelaVO.setPessoa(clienteVO.getPessoa());
            movParcelaVO.setEmpresa(clienteVO.getEmpresa());
            movParcelaVO.setIdExterno(receivable.getIdReceivable());

            movParcelaDAO.incluirParcelaSemCommit(movParcelaVO);
            movParcelaVOS.add(movParcelaVO);
        }

        return movParcelaVOS;
    }

    private List<MovParcelaVO> gerarParcelas(List<ReceivablesViewTO> receivables, ContratoVO contratoVO, boolean possuiCancelamento) throws Exception {
        List<MovParcelaVO> movParcelaVOS = new ArrayList<>();

        for (ReceivablesViewTO receivable : receivables) {
            PaymentTypeEnum paymentTypeEnum = obterPaymentType(receivable);
            if (paymentTypeEnum == null) {
                throw new ConsistirException("Falha ao obter paymentType do recebivel id: " + receivable.getIdReceivable() + " idvenda: " + receivable.getIdSale() + " - " + receivable.getDescription());
            }
            if (paymentTypeEnum.getId().equals(PaymentTypeEnum.SALES_CREDIT.getId())) {
                continue;
            }
            Double valorParcela = receivable.getAmmount();
            Date dataVencimento = obterDataVencimentoParcelaFromReceivable(receivable, paymentTypeEnum);

            StatusRecevablesEnum statusRecevablesEnum = StatusRecevablesEnum.obterPorId(receivable.getStatus().getId());
            if (statusRecevablesEnum == null) {
                throw new Exception("Falha ao obter status do recebivel id: " + receivable.getIdReceivable() + " idvenda: " + receivable.getIdSale() + " - " + receivable.getDescription());
            }

            MovParcelaVO movParcelaVO = new MovParcelaVO();
            movParcelaVO.setContrato(contratoVO);
            movParcelaVO.setDataRegistro(contratoVO.getDataLancamento());
            movParcelaVO.setDataVencimento(dataVencimento);
            movParcelaVO.setResponsavel(contratoVO.getResponsavelContrato());
            movParcelaVO.setSituacao("EA");
            if (statusRecevablesEnum.getId().equals(StatusRecevablesEnum.CANCELED.getId())) {
                movParcelaVO.setSituacao("CA");
            } else if ((statusRecevablesEnum.getId().equals(StatusRecevablesEnum.RECEIVED.getId()) &&
                    !(paymentTypeEnum.equals(PaymentTypeEnum.CREDIT_CARD) || paymentTypeEnum.equals(PaymentTypeEnum.ONLINE_CREDIT_CARD)))
                    || !UteisValidacao.emptyString(receivable.getAuthorization())) {
                movParcelaVO.setSituacao("PG");
            }
            movParcelaVO.setValorParcela(valorParcela);
            movParcelaVO.setPercentualJuro(0.0);
            movParcelaVO.setPercentualMulta(0.0);
            movParcelaVO.setPessoa(contratoVO.getPessoa());
            movParcelaVO.setEmpresa(contratoVO.getEmpresa());
            movParcelaVO.setIdExterno(receivable.getIdReceivable());
            movParcelaVO.setRegimeRecorrencia(contratoVO.getRegimeRecorrencia());

            movParcelaVOS.add(movParcelaVO);

        }

        Double valorTotalParcelas = movParcelaVOS.stream()
                .mapToDouble(MovParcelaVO::getValorParcela)
                .sum();
        if (possuiCancelamento && contratoVO.getValorFinal() > valorTotalParcelas) {
            Double valorParcela = contratoVO.getValorFinal() - valorTotalParcelas;
            Date maiorDataVencimentoParcela = null;

            if (!UteisValidacao.emptyList(movParcelaVOS)) {
                for (MovParcelaVO parcelaVO : movParcelaVOS) {
                    if (maiorDataVencimentoParcela == null) {
                        maiorDataVencimentoParcela = parcelaVO.getDataVencimento();
                    } else if (Calendario.maior(parcelaVO.getDataVencimento(), maiorDataVencimentoParcela)) {
                        maiorDataVencimentoParcela = parcelaVO.getDataVencimento();
                    }
                }
            }
            Date dataVencimento = maiorDataVencimentoParcela != null ? Calendario.somarDias(maiorDataVencimentoParcela, 1) : contratoVO.getVigenciaDe();
            dataVencimento = Calendario.maior(dataVencimento, contratoVO.getVigenciaAteAjustada())
                    ? maiorDataVencimentoParcela : dataVencimento;

            MovParcelaVO movParcelaVO = new MovParcelaVO();
            movParcelaVO.setContrato(contratoVO);
            movParcelaVO.setDataRegistro(contratoVO.getDataLancamento());
            movParcelaVO.setDataVencimento(dataVencimento);
            movParcelaVO.setResponsavel(contratoVO.getResponsavelContrato());
            movParcelaVO.setSituacao("CA");
            movParcelaVO.setValorParcela(valorParcela);
            movParcelaVO.setPercentualJuro(0.0);
            movParcelaVO.setPercentualMulta(0.0);
            movParcelaVO.setPessoa(contratoVO.getPessoa());
            movParcelaVO.setEmpresa(contratoVO.getEmpresa());
            movParcelaVOS.add(movParcelaVO);
        }

        if (movParcelaVOS.isEmpty()) {
            Double valorParcela = 0.0;
            Date dataVencimento = contratoVO.getVigenciaDe();

            MovParcelaVO movParcelaVO = new MovParcelaVO();
            movParcelaVO.setContrato(contratoVO);
            movParcelaVO.setDataRegistro(contratoVO.getDataLancamento());
            movParcelaVO.setDataVencimento(dataVencimento);
            movParcelaVO.setResponsavel(contratoVO.getResponsavelContrato());
            movParcelaVO.setSituacao(contratoVO.getValorFinal() == 0.0 ? "PG" : "CA");
            movParcelaVO.setValorParcela(contratoVO.getValorFinal());
            movParcelaVO.setPercentualJuro(0.0);
            movParcelaVO.setPercentualMulta(0.0);
            movParcelaVO.setPessoa(contratoVO.getPessoa());
            movParcelaVO.setEmpresa(contratoVO.getEmpresa());
            movParcelaVOS.add(movParcelaVO);
        }

        // Ordenação das parcelas
        Ordenacao.ordenarLista(movParcelaVOS, "dataVencimento");
        List<MovParcelaVO> parcelasPagas = movParcelaVOS.stream().filter(p -> p.getSituacao().equals("PG")).collect(Collectors.toList());
        List<MovParcelaVO> parcelaAbertas = movParcelaVOS.stream().filter(p -> p.getSituacao().equals("EA")).collect(Collectors.toList());
        if (movParcelaVOS.get(0).getSituacao().equals("EA") && !UteisValidacao.emptyList(parcelasPagas)) {
            // nesse caso, gerar as parcelas pagas primeiro, para evitar estornos automaticos de contratos, por causa da primeira parcela em aberto a x dias.
            movParcelaVOS = new ArrayList<>();
            movParcelaVOS.addAll(parcelasPagas);
            movParcelaVOS.addAll(parcelaAbertas);
        }

        int numero = 0;
        for (MovParcelaVO movParcelaVO : movParcelaVOS) {
            movParcelaVO.setDescricao("PARCELA " + (++numero));
            movParcelaDAO.incluirParcelaSemCommit(movParcelaVO);
        }

        return movParcelaVOS;
    }

    private Date obterDataVencimentoParcelaFromReceivable(ReceivablesViewTO receivable, PaymentTypeEnum paymentTypeEnum) {
        String description = !UteisValidacao.emptyString(receivable.getDescription()) ? receivable.getDescription().toUpperCase() : "";

        Date dataVencimento = UteisImportacao.getDateFromLocalDateTime(receivable.getDueDate());

        if (isPaymentCartao(paymentTypeEnum, description)) {
            if (receivable.getCompetenceDate() != null) {
                dataVencimento = UteisImportacao.getDateFromLocalDateTime(receivable.getCompetenceDate());
            } else if (receivable.getChargeDate() != null && isPaymentCartao(paymentTypeEnum, description)) {
                dataVencimento = UteisImportacao.getDateFromLocalDateTime(receivable.getChargeDate());
            } else {
                dataVencimento = UteisImportacao.getDateFromLocalDateTime(receivable.getRegistrationDate());
            }
        }

        return dataVencimento;
    }

    private boolean isPaymentCartao(PaymentTypeEnum paymentTypeEnum, String description) {
        return (paymentTypeEnum.getId().equals(PaymentTypeEnum.ONLINE_CREDIT_CARD.getId()))
                || (paymentTypeEnum.getId().equals(PaymentTypeEnum.CREDIT_CARD.getId()))
                || (paymentTypeEnum.getId().equals(PaymentTypeEnum.DEBIT_CARD.getId()))
                || description.contains("CARTÃO")
                || description.contains("CARTAO")
                || description.contains("CARD");
    }

    private void atualizarValorContrato(ContratoVO contratoVO, List<MovParcelaVO> parcelaVOS) throws SQLException {
        Double valorTotalParcelas = parcelaVOS.stream()
                .mapToDouble(MovParcelaVO::getValorParcela)
                .sum();
        contratoVO.setValorFinal(valorTotalParcelas);
        contratoVO.setValorBaseCalculo(valorTotalParcelas);
        String sql = "update contrato set valorfinal = ?, valorbasecalculo = ? where codigo = ?";
        PreparedStatement pstm = con.prepareStatement(sql);
        pstm.setDouble(1, valorTotalParcelas);
        pstm.setDouble(2, valorTotalParcelas);
        pstm.setInt(3, contratoVO.getCodigo());
        pstm.execute();
    }

    private void gerarMovProdutoModalidade(ContratoVO contratoVO, List<MovProdutoVO> movProdutoVOS) throws Exception {
        for (MovProdutoVO movProdutoVO : movProdutoVOS) {
            movProdutoVO.setMovProdutoModalidades(zillyonWebFacadeDAO.gerarMovProdutoModalidade(MovProdutoModalidade.getValorSomaContratoModalidade(contratoVO.getContratoModalidadeVOs()),
                    contratoVO.getVigenciaDe(), contratoVO.getVigenciaAte(), contratoVO.getContratoModalidadeVOs(), movProdutoVO.getTotalFinal()));
            movProdutoDAO.alterarSemCommit(movProdutoVO);
            movProdutoModalidadeDAO.incluir(movProdutoVO);
        }
    }

    private void verificarMovProdutosCancelados(ContratoVO contratoVO, List<MovProdutoVO> movProdutoVOS) throws SQLException {
        if (!UteisValidacao.emptyList(movProdutoVOS)) {
            String sql = "update movproduto set situacao = 'CA' where situacao = 'EA' and codigo in ( \n" +
                    " select mpro.codigo from movproduto mpro \n " +
                    " inner join movprodutoparcela mpp on mpp.movproduto = mpro.codigo \n" +
                    " inner join movparcela mpar on mpar.codigo = mpp.movparcela \n" +
                    " where mpro.codigo in (" + movProdutoVOS.stream().map(m -> m.getCodigo().toString()).collect(Collectors.joining(",")) + ") \n" +
                    " and mpar.situacao = 'CA');";
            SuperFacadeJDBC.executarUpdate(sql, con);
        }
    }

    private void gerarMovprodutoParcela(List<MovParcelaVO> movParcelaVOS, List<MovProdutoVO> movProdutoVOS) throws Exception {
        MovProdutoParcela movProdutoParcelaDAO = new MovProdutoParcela(con);

        for (MovParcelaVO movParcelaVO : movParcelaVOS) {
            if (UteisValidacao.emptyNumber(movParcelaVO.getValorParcela())) {
                continue;
            }

            movParcelaVO.setValorBaseCalculo(movParcelaVO.getValorParcela());
            for (MovProdutoVO movProdutoVO : movProdutoVOS) {
                MovProdutoParcelaVO mpp = new MovProdutoParcelaVO();
                mpp.setReciboPagamento(null);
                mpp.setMovParcela(movParcelaVO.getCodigo());

                if (movProdutoVO.getQuitado()) {
                    continue;
                }
                mpp.setMovProduto(movProdutoVO.getCodigo());
                if (Uteis.arredondarForcando2CasasDecimais(movParcelaVO.getValorBaseCalculo()) >= Uteis.arredondarForcando2CasasDecimais(movProdutoVO.getValorPagoMovProdutoParcela())) {
                    mpp.setValorPago(movProdutoVO.getValorPagoMovProdutoParcela());
                    movParcelaVO.setValorBaseCalculo(Uteis.arredondarForcando2CasasDecimais(movParcelaVO.getValorBaseCalculo() - movProdutoVO.getValorPagoMovProdutoParcela()));
                    movProdutoVO.setQuitado(true);
                    movProdutoVO.setValorPagoMovProdutoParcela(0.0);
                } else {
                    mpp.setValorPago(movParcelaVO.getValorBaseCalculo());
                    movProdutoVO.setValorPagoMovProdutoParcela(Uteis.arredondarForcando2CasasDecimais(movProdutoVO.getValorPagoMovProdutoParcela() - movParcelaVO.getValorBaseCalculo()));
                    movParcelaVO.setValorBaseCalculo(0.0);
                }
                movProdutoVO.setQuitado(Uteis.arredondarForcando2CasasDecimais(movProdutoVO.getValorPagoMovProdutoParcela()) == 0.00);
                movProdutoParcelaDAO.incluir(mpp);
                if (movParcelaVO.getValorBaseCalculo() == 0.00) {
                    break;
                }
            }
        }
    }

    private List<MovProdutoVO> gerarMovProdutosMembers(ContratoVO contratoVO) throws Exception {
        List<MovProdutoVO> lista = new ArrayList<MovProdutoVO>();
        Integer duracao = contratoVO.getContratoDuracao().getNumeroMeses();
        Date dataInicio = contratoVO.getVigenciaDe();
        Date dataFim = Uteis.somarCampoData(dataInicio, Calendar.MONTH, 1);

        Integer codProdutoMensalidade = contratoVO.getPlano().getProdutoPadraoGerarParcelasContrato().getCodigo();
        Double valorMensalidadeContrato = (contratoVO.getValorBaseCalculo());

        if (duracao > 1) {
            Double valorMensalidade = Uteis.arredondarForcando2CasasDecimais(valorMensalidadeContrato / duracao);
            lista.add(montarMensalidadeMembers(contratoVO, dataInicio, dataFim, codProdutoMensalidade, valorMensalidade));
            for (int i = 1; i < duracao; i++) {
                dataInicio = dataFim;
                dataFim = Uteis.somarCampoData(dataInicio, Calendar.MONTH, 1);
                lista.add(montarMensalidadeMembers(contratoVO, dataInicio, dataFim, codProdutoMensalidade, valorMensalidade));
            }
        } else {
            lista.add(montarMensalidadeMembers(contratoVO, dataInicio, contratoVO.getVigenciaAte(), codProdutoMensalidade, valorMensalidadeContrato));
        }

        movProdutoDAO.incluirListaMovProdutos(lista);
        return lista;
    }

    private MovProdutoVO montarMensalidadeMembers(ContratoVO contratoVO, Date inicio, Date fim,
                                                  Integer codProduto, Double valor) {

        Integer ano = Uteis.getAnoData(inicio);
        String mes = Uteis.getMesReferencia(inicio);

        MovProdutoVO movProduto = new MovProdutoVO();
        movProduto.setSituacao(UteisValidacao.emptyNumber(valor) ? "PG" : "EA");
        movProduto.setDataFinalVigencia(fim);
        movProduto.setDataInicioVigencia(inicio);
        movProduto.setAnoReferencia(ano);
        movProduto.setMesReferencia(mes + "/" + ano);
        movProduto.setResponsavelLancamento(contratoVO.getResponsavelContrato());
        movProduto.setDataLancamento(contratoVO.getDataLancamento());
        movProduto.setTotalFinal(valor);
        movProduto.setValorFaturado(valor);
        movProduto.setValorDesconto(0.0);
        movProduto.setPrecoUnitario(valor);
        movProduto.setValorPagoMovProdutoParcela(movProduto.getTotalFinal());
        movProduto.setQuantidade(1);
        movProduto.setDescricao(contratoVO.getPlano().getDescricao() + " - " + mes + "/" + ano);
        movProduto.setEmpresa(contratoVO.getEmpresa());
        movProduto.setPessoa(contratoVO.getPessoa());
        movProduto.setContrato(contratoVO);
        ProdutoVO produto = new ProdutoVO();
        produto.setCodigo(codProduto);
        movProduto.setProduto(produto);
        movProduto.setApresentarMovProduto(true);
        movProduto.setQuitado(movProduto.getSituacao().equals("PG"));
        return movProduto;
    }

    private void atualizarContratoBaseadoRenovacao(Integer contrato, Integer contratoResponsavelRenovacao) throws SQLException {
        String sql = "update contrato set contratoresponsavelrenovacaomatricula = ? where codigo = ?";
        PreparedStatement pstm = con.prepareStatement(sql);
        pstm.setInt(1, contratoResponsavelRenovacao);
        pstm.setInt(2, contrato);
        pstm.execute();
    }

    public void gerarHistoricoContrato(ContratoVO contratoVO) throws Exception {

        HistoricoContrato historicoContratoDAO = new HistoricoContrato(con);
        HistoricoContratoVO historicoContratoVO = new HistoricoContratoVO();
        historicoContratoVO.setContrato(contratoVO.getCodigo());
        if (!UteisValidacao.emptyNumber(contratoVO.getContratoBaseadoRenovacao())) {
            historicoContratoVO.setDescricao("Renovado");
            historicoContratoVO.setTipoHistorico("RN");
        } else {
            historicoContratoVO.setDescricao("Matriculado");
            historicoContratoVO.setTipoHistorico("MA");
        }
        historicoContratoVO.setDataInicioTemporal(contratoVO.getVigenciaDe());
        historicoContratoVO.setResponsavelRegistro(contratoVO.getResponsavelContrato());
        historicoContratoVO.setDataRegistro(contratoVO.getDataLancamento());
        historicoContratoVO.setDataInicioSituacao(contratoVO.getVigenciaDe());
        historicoContratoVO.setDataFinalSituacao(contratoVO.getVigenciaAteAjustada());

        historicoContratoDAO.incluirSemCommit(historicoContratoVO, false);
    }

    private ContratoVO montarContrato(MemberMembershipApiViewTO memberShip, SalesApiViewTO sale, boolean isImportarRecebiveis, ContratoVO contratoRenovar, ClienteVO clienteVO, IntegracaoMemberVO integracaoMemberVO) throws Exception {
        ContratoVO contratoVO = new ContratoVO();

        contratoVO.setIdExterno(memberShip.getIdMemberMembership());
        contratoVO.setPessoa(clienteVO.getPessoa());
        contratoVO.setObservacao("plano evo: " + memberShip.getIdMembership() + " - " + memberShip.getName());

        SaleItemApiViewTO saleItem = sale.getSaleItens().stream()
                .filter(si -> !UteisValidacao.emptyNumber(si.getIdMemberMembership()) ? si.getIdMemberMembership().equals(memberShip.getIdMemberMembership()) : false)
                .findFirst()
                .orElse(null);
        if (isImportarRecebiveis && saleItem != null) {
            Double valorContrato = saleItem.getItemValue();
            contratoVO.setValorBaseCalculo(valorContrato);
            contratoVO.setValorFinal(valorContrato);
        }

        //empresa
        EmpresaVO empresaVO = cache.obterEmpresaVO(clienteVO.getEmpresa().getCodigo());
        contratoVO.setEmpresa(empresaVO);

        // Datas
        Date lancamentoContrato = UteisImportacao.getDateFromLocalDateTime(memberShip.getSaleDate());

        contratoVO.setVigenciaDe(memberShip.getDataInicio());
        contratoVO.setVigenciaAte(memberShip.getDataFim());
        contratoVO.setVigenciaAteAjustada(memberShip.getDataFim());
        contratoVO.setDataLancamento(lancamentoContrato);
        contratoVO.setDataMatricula(memberShip.getDataInicio());
        contratoVO.setDataPrevistaRematricula(memberShip.getDataFim());
        contratoVO.setDataPrevistaRenovar(memberShip.getDataFim());

        // Contrato renovar
        if (contratoRenovar != null && !UteisValidacao.emptyNumber(contratoRenovar.getCodigo())) {
            Long diferencaoDiaContratoBaseRN = Uteis.nrDiasEntreDatas(contratoRenovar.getVigenciaAteAjustada(), contratoVO.getVigenciaDe());
            if (diferencaoDiaContratoBaseRN.intValue() <= empresaVO.getCarenciaRenovacao().intValue()) {
                contratoVO.setSituacaoContrato("RN");
                contratoVO.setContratoBaseadoRenovacao(contratoRenovar.getCodigo());
                contratoVO.setVigenciaDe(Calendario.somarDias(contratoRenovar.getVigenciaAteAjustada(), 1));
            }
        }

        // Duracao
        ContratoDuracaoVO contratoDuracaoVO = new ContratoDuracaoVO();
        contratoDuracaoVO.setCarencia(integracaoMemberVO.getDiasCarencia());

        Integer numeroMeses;
        Long dias = Uteis.nrDiasEntreDatas(contratoVO.getVigenciaDe(), contratoVO.getVigenciaAteAjustada());
        if (dias < 30) {
            numeroMeses = 1;
        } else {
            numeroMeses = (new Long(dias / 30).intValue());
        }
        contratoDuracaoVO.setNumeroMeses(numeroMeses);
        contratoVO.setContratoDuracao(contratoDuracaoVO);


        // Situacao do contrato
        if (Calendario.menor(contratoVO.getVigenciaAteAjustada(), Calendario.hoje())) {
            contratoVO.setSituacao("IN");
        } else {
            contratoVO.setSituacao("AT");
        }
        String status = memberShip.getMembershipStatus().toLowerCase();
        if (status.equals(MemberShipStatusEnum.CANCELED.getName().toLowerCase()) && !UteisValidacao.emptyString(memberShip.getCancelDate())) {
            Date dataCancelamento = UteisImportacao.getDateFromLocalDateTime(memberShip.getCancelDate());
            if (Calendario.menorOuIgual(dataCancelamento, Calendario.hoje())) {
                contratoVO.setSituacao("CA");
                contratoVO.setVigenciaAteAjustada(dataCancelamento);
            }
        }

        // Plano
        PlanoVO planoVO = null;
        if (!UteisValidacao.emptyNumber(memberShip.getIdMembership())) {
            planoVO = cache.obterPlanoPorIdExterno(memberShip.getIdMembership(), integracaoMemberVO.getEmpresa().getCodigo());
            if (planoVO == null || UteisValidacao.emptyNumber(planoVO.getCodigo())) {
                planoVO = cache.getMapaPlanoCorrespondencia().get(memberShip.getIdMembership().toString());
            }
        }
        if (planoVO != null) {
            for (PlanoModalidadeVO planoModalidadeVO : planoVO.getPlanoModalidadeVOs()) {
                contratoVO.getContratoModalidadeVOs().add(montarContratoModalidade(planoModalidadeVO.getModalidade(), contratoVO));
            }
        } else {
            planoVO = cache.obterPlanoVO(integracaoMemberVO.getPlanoPadrao());
            if (planoVO == null) {
                throw new Exception("Falha ao obter plano padrão!");
            }
            ModalidadeVO modalidadeVO = cache.obterModalidadeVO(integracaoMemberVO.getModalidadePadrao());
            contratoVO.getContratoModalidadeVOs().add(montarContratoModalidade(modalidadeVO, contratoVO));
        }
        contratoVO.setPlano(planoVO);

        if (UteisValidacao.emptyList(contratoVO.getContratoModalidadeVOs())) {
            throw new Exception("Falha ao adicionar modalidades no contrato");
        }

        if (planoVO.getRegimeRecorrencia()) {
            if (contratoVO.getValorBaseCalculo().intValue() > 0.0) {
                contratoVO.setRegimeRecorrencia(true);
            } else {
                contratoVO.setPlano(cache.obterPlanoVO(integracaoMemberVO.getPlanoPadrao()));
                contratoVO.setRegimeRecorrencia(false);
            }
        }

        // Responsável e consultor
        Integer usuario = cache.getUsuarioVOImportacao().getCodigo();
        if (UteisValidacao.emptyNumber(usuario)) {
            usuario = cache.getUsuarioVOImportacao().getCodigo();
        }
        contratoVO.setResponsavelContrato(cache.obterUsuarioVO(usuario));

        Integer consultor = cache.getUsuarioVOImportacao().getColaboradorVO().getCodigo();
        if (UteisValidacao.emptyNumber(consultor)) {
            consultor = cache.obterColaboradorPactoBREmpresa(empresaVO.getCodigo()).getCodigo();
        }
        contratoVO.setConsultor(cache.obterColaboradorVO(consultor));

        //plano duracao
        PlanoDuracaoVO planoDuracaoVO = this.cache.obterPlanoDuracao(contratoVO.getContratoDuracao().getNumeroMeses(), 1);

        if (planoDuracaoVO == null || UteisValidacao.emptyNumber(planoDuracaoVO.getCodigo())) {
            planoDuracaoVO = new PlanoDuracaoVO();
            planoDuracaoVO.setCodigo(1);
            contratoVO.setPlanoDuracao(planoDuracaoVO);
            contratoVO.getPlanoDuracao().setDuracaoEscolhida(true);
            contratoVO.getPlanoDuracao().setNrMaximoParcelasCondPagamento(1);
            contratoVO.getPlanoDuracao().setNumeroMeses(contratoVO.getContratoDuracao().getNumeroMeses());
            contratoVO.getPlanoDuracao().setValorDesejado(0.0);
            contratoVO.getPlanoDuracao().setCarencia(integracaoMemberVO.getDiasCarencia());
            contratoVO.getPlanoDuracao().setTipoValor("PD");
            contratoVO.getPlanoDuracao().setTipoOperacao("AC");
        } else {
            planoDuracaoVO.setCarencia(integracaoMemberVO.getDiasCarencia());
            contratoVO.setPlanoDuracao(planoDuracaoVO);
        }

        //horario contrato
        ContratoHorarioVO contratoHorarioVO = new ContratoHorarioVO();
        HorarioVO horarioVO = cache.obterHorarioVO(integracaoMemberVO.getHorarioPadrao());
        contratoHorarioVO.setHorario(horarioVO);
        contratoVO.setContratoHorario(contratoHorarioVO);
        //horario contrato /
        PlanoHorarioVO planoHorarioVO = new PlanoHorarioVO();
        planoHorarioVO.setHorario(horarioVO);
        contratoVO.setPlanoHorario(planoHorarioVO);

        //condição de pagamento
        List<PlanoCondicaoPagamentoVO> listaPlanoCond = this.cache.obterPlanoCondicaoDePagamento(contratoVO.getPlanoDuracao().getCodigo(), contratoVO.getPlanoDuracao().getNumeroMeses(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        PlanoCondicaoPagamentoVO planoCondicaoPagamentoVO = new PlanoCondicaoPagamentoVO();
        if (!UteisValidacao.emptyList(listaPlanoCond)) {
            planoCondicaoPagamentoVO = listaPlanoCond.get(0);
        }
        contratoVO.setPlanoCondicaoPagamento(planoCondicaoPagamentoVO);

        //plano texto padrão
        ContratoTextoPadraoVO contratoTextoPadraoVO = new ContratoTextoPadraoVO();
        contratoTextoPadraoVO.setPlanoTextoPadrao(contratoVO.getPlano().getPlanoTextoPadrao());
        contratoVO.setContratoTextoPadrao(contratoTextoPadraoVO);

        // periodoacessocliente
        contratoVO.setPeriodoAcessoClienteVOs(new ArrayList());
        contratoVO.getPeriodoAcessoClienteVOs().add(montarPeriodoAcesso(contratoVO));

        // Recorrencia
        if (contratoVO.isRegimeRecorrencia()) {
            Double valorMensal = Uteis.arredondarForcando2CasasDecimais(contratoVO.getValorBaseCalculo() / contratoVO.getContratoDuracao().getNumeroMeses());
            ContratoRecorrenciaVO contratoRecorrenciaVO = new ContratoRecorrenciaVO();
            contratoRecorrenciaVO.setContrato(contratoVO);
            contratoRecorrenciaVO.setDiaVencimentoAnuidade(planoVO.getPlanoRecorrencia().getDiaAnuidade());
            contratoRecorrenciaVO.setMesVencimentoAnuidade(planoVO.getPlanoRecorrencia().getMesAnuidade());
            contratoRecorrenciaVO.setDiaVencimentoCartao(Uteis.getDiaMesData(contratoVO.getVigenciaDe()));
            contratoRecorrenciaVO.setDiasCancelamentoAutomatico(planoVO.getPlanoRecorrencia().getQtdDiasAposVencimentoCancelamentoAutomatico());
            contratoRecorrenciaVO.setFidelidade(contratoVO.getContratoDuracao().getNumeroMeses());
            contratoRecorrenciaVO.setPessoa(contratoVO.getPessoa());
            contratoRecorrenciaVO.setRenovavelAutomaticamente(planoVO.getPlanoRecorrencia().getRenovavelAutomaticamente());
            contratoRecorrenciaVO.setValorMensal(valorMensal);
            contratoRecorrenciaVO.setValorAnuidade(0.0);
            contratoRecorrenciaVO.setAnuidadeNaParcela(false);
            contratoRecorrenciaVO.setCancelamentoProporcional(planoVO.getPlanoRecorrencia().isCancelamentoProporcional());
            contratoVO.setContratoRecorrenciaVO(contratoRecorrenciaVO);
        }

        return contratoVO;
    }

    private ContratoModalidadeVO montarContratoModalidade(ModalidadeVO modalidadeVO, ContratoVO contratoVO) throws Exception {
        ContratoModalidadeVO contratoModalidadeVO = new ContratoModalidadeVO();
        modalidadeVO.setModalidadeEscolhida(true);
        Double valor = (contratoVO.getValorFinal() - 0.0) / contratoVO.getContratoDuracao().getNumeroMeses();

        contratoModalidadeVO.setModalidade(modalidadeVO);
        contratoModalidadeVO.setNrVezesSemana(7);
        contratoModalidadeVO.setValorModalidade(valor);
        contratoModalidadeVO.setValorFinalModalidade(valor);

        PlanoModalidadeVezesSemanaVO planoVezesSemanaVO = new PlanoModalidadeVezesSemanaVO();
        planoVezesSemanaVO.setNrVezes(modalidadeVO.getNrVezes());
        planoVezesSemanaVO.setVezeSemanaEscolhida(true);
        contratoModalidadeVO.setPlanoVezesSemanaVO(planoVezesSemanaVO);

        contratoModalidadeVO.setModalidade(modalidadeVO);
        contratoModalidadeVO.getModalidade().setUtilizarTurma(false);
        contratoModalidadeVO.getModalidade().setModalidadeEscolhida(true);

        return contratoModalidadeVO;
    }

    private void gerarPagamentoParcelas(List<MovParcelaVO> parcelaVOS, Map<Integer, ReceivablesViewTO> mapaParcelasReceivables) throws Exception {
        gerarPagamentoParcelas(null, parcelaVOS, mapaParcelasReceivables);
    }

    private void gerarPagamentoParcelas(ContratoVO contratoVO, List<MovParcelaVO> parcelaVOS, Map<Integer, ReceivablesViewTO> mapaParcelasReceivables) throws Exception {
        if (mapaParcelasReceivables.size() == 0) {
            return;
        }

        Map<String, MovPagamentoVO> mapaPagamentos = new HashMap<>();

        for (MovParcelaVO parcelaVO : parcelaVOS) {
            if (!parcelaVO.getSituacao().equals("PG")) {
                continue;
            }
            ReceivablesViewTO receivable = mapaParcelasReceivables.get(parcelaVO.getIdExterno());
            if (receivable == null) {
                return;
            }

            FormaPagamentoVO formaPagamento = null;
            PaymentTypeEnum paymentTypeEnum = obterPaymentType(receivable);

            switch (paymentTypeEnum) {
                case CASH:
                    formaPagamento = cache.obterFormaPagamento(TipoFormaPagto.AVISTA);
                    break;
                case CREDIT_CARD:
                    formaPagamento = cache.obterFormaPagamento(TipoFormaPagto.CARTAOCREDITO);
                    break;
                case ONLINE_CREDIT_CARD:
                    formaPagamento = cache.obterFormaPagamento(TipoFormaPagto.CARTAOCREDITO);
                    break;
                case DEBIT_CARD:
                    formaPagamento = cache.obterFormaPagamento(TipoFormaPagto.CARTAODEBITO);
                    break;
                case CHECK:
                    formaPagamento = cache.obterFormaPagamento(TipoFormaPagto.CHEQUE);
                    break;
                case BANK_SLIP:
                    formaPagamento = cache.obterFormaPagamento(TipoFormaPagto.BOLETOBANCARIO);
                    break;
                case SALES_CREDIT:
                    formaPagamento = cache.obterFormaPagamento(TipoFormaPagto.CREDITOCONTACORRENTE);
                    break;
                case TRANSFER:
                    formaPagamento = cache.obterFormaPagamento(TipoFormaPagto.TRANSFERENCIA_BANCARIA);
                    break;
                case DEPOSIT:
                    formaPagamento = cache.obterFormaPagamento(TipoFormaPagto.TRANSFERENCIA_BANCARIA);
                    break;
                case ACCOUNT_DEBIT:
                    formaPagamento = cache.obterFormaPagamento(TipoFormaPagto.TRANSFERENCIA_BANCARIA);
                    break;
                case PIX:
                    formaPagamento = cache.obterFormaPagamento(TipoFormaPagto.PIX);
                    break;
                default:
                    formaPagamento = cache.obterFormaPagamento(TipoFormaPagto.AVISTA);
                    break;
            }

            MovPagamentoVO movPagamentoVO = montarMovPagamento(mapaPagamentos, formaPagamento, parcelaVO.getPessoa(), parcelaVO.getEmpresa(), receivable);

            PagamentoMovParcelaVO pagamentoMovParcelaVO = new PagamentoMovParcelaVO();
            pagamentoMovParcelaVO.setMovParcela(parcelaVO);
            pagamentoMovParcelaVO.setValorPago(parcelaVO.getValorParcela());
            movPagamentoVO.getPagamentoMovParcelaVOs().add(pagamentoMovParcelaVO);
        }


        List<MovPagamentoVO> pagamentoVOS = new ArrayList<>();
        for (String key : mapaPagamentos.keySet()) {
            MovPagamentoVO movPagamentoVO = mapaPagamentos.get(key);
            pagamentoVOS.add(movPagamentoVO);
        }

        Ordenacao.ordenarLista(pagamentoVOS, "dataLancamento");

        for (MovPagamentoVO movPagamentoVO : pagamentoVOS) {
            movPagamentoVO.setValorTotal(movPagamentoVO.getValor());

            List<MovParcelaVO> parcelas = new ArrayList<>();
            movPagamentoVO.getPagamentoMovParcelaVOs().forEach(pmp -> parcelas.add(pmp.getMovParcela()));
            importarPagamento.incluirPagamento(movPagamentoVO, contratoVO != null ? contratoVO.getCodigo() : null);
        }
    }
    private PaymentTypeEnum obterPaymentType(ReceivablesViewTO receivable) {
        PaymentTypeEnum paymentTypeEnum = null;

        if (!UteisValidacao.emptyNumber(receivable.getPaymentType().getId())) {
            paymentTypeEnum = PaymentTypeEnum.getById(receivable.getPaymentType().getId());
        } else {
            paymentTypeEnum = PaymentTypeEnum.getByName(receivable.getPaymentType().getName());
        }

        if (paymentTypeEnum == null) {
            String descricaoFormaPagamento = receivable.getPaymentType().getName().toLowerCase();
            if (descricaoFormaPagamento.contains("cartão de crédito")
                    || descricaoFormaPagamento.contains("cartão de credito")
                    || descricaoFormaPagamento.contains("cartao de crédito")
                    || descricaoFormaPagamento.contains("cartao de credito")) {
                paymentTypeEnum = PaymentTypeEnum.CREDIT_CARD;
            } else if (descricaoFormaPagamento.contains("cartão de debito")
                    || descricaoFormaPagamento.contains("cartao de débito")
                    || descricaoFormaPagamento.contains("cartão de débito")
                    || descricaoFormaPagamento.contains("cartao de debito")) {
                paymentTypeEnum = PaymentTypeEnum.DEBIT_CARD;
            } else if (descricaoFormaPagamento.contains("cheque")) {
                paymentTypeEnum = PaymentTypeEnum.CHECK;
            } else if (descricaoFormaPagamento.contains("crédito de venda") || descricaoFormaPagamento.contains("credito de venda")) {
                paymentTypeEnum = paymentTypeEnum.SALES_CREDIT;
            } else if (descricaoFormaPagamento.contains("boleto")) {
                paymentTypeEnum = PaymentTypeEnum.BANK_SLIP;
            } else if (descricaoFormaPagamento.contains("pix")) {
                paymentTypeEnum = PaymentTypeEnum.PIX;
            } else {
                paymentTypeEnum = PaymentTypeEnum.CASH;
            }
        }
        return paymentTypeEnum;
    }

    private void incluirPagamentoParcelas(MovPagamentoVO movPagamentoVO, List<MovParcelaVO> parcelas, ContratoVO contratoVO) throws Exception {
        List<MovPagamentoVO> pagamentoVOS = new ArrayList<>();
        pagamentoVOS.add(movPagamentoVO);
        movPagamentoDAO.incluirListaPagamento(pagamentoVOS, parcelas, null, contratoVO,
                false, 0.0, false, null, null);
    }

    private MovPagamentoVO montarMovPagamento(Map<String, MovPagamentoVO> mapaPagamentos, FormaPagamentoVO formaPagamento, PessoaVO pessoaVO, EmpresaVO empresaVO, ReceivablesViewTO receivable) throws Exception {
        Date dataLancamento = UteisImportacao.getDateFromLocalDateTime(receivable.getChargeDate());
        Date dataReceivable = UteisImportacao.getDateFromLocalDateTime(receivable.getReceivingDate());

        if (dataLancamento == null) {
            dataLancamento = UteisImportacao.getDateFromLocalDateTime(receivable.getRegistrationDate());
        }

        String keyPagamento = formaPagamento.getTipoFormaPagamento();
        if (!UteisValidacao.emptyString(receivable.getAuthorization())) {
            keyPagamento += receivable.getAuthorization();
        } else {
            keyPagamento += Calendario.getDataAplicandoFormatacao(dataLancamento, "yyyy-MM-dd");
            if (dataReceivable != null) {
                keyPagamento += Calendario.getDataAplicandoFormatacao(dataReceivable, "yyyy-MM-dd");
            }
        }

        MovPagamentoVO movPagamento = new MovPagamentoVO();
        if (mapaPagamentos.get(keyPagamento) == null) {
            movPagamento.setFormaPagamento(formaPagamento);
            movPagamento.setPessoa(pessoaVO);
            movPagamento.setDataLancamento(dataLancamento);
            movPagamento.setDataPagamento(movPagamento.getDataLancamento());
            movPagamento.setDataQuitacao(movPagamento.getDataLancamento());
            movPagamento.setResponsavelPagamento(cache.getUsuarioVOImportacao());
            movPagamento.setEmpresa(empresaVO);
            movPagamento.setMovPagamentoEscolhida(true);
            movPagamento.setNomePagador(pessoaVO.getNome());
            mapaPagamentos.put(keyPagamento, movPagamento);
        } else {
            movPagamento = mapaPagamentos.get(keyPagamento);
        }

        movPagamento.setValor(movPagamento.getValor() + receivable.getAmmount());
        movPagamento.setValorTotal(movPagamento.getValorTotal() + receivable.getAmmount());

        if (formaPagamento.getTipoFormaPagamento().equals(TipoFormaPagto.CARTAOCREDITO.getSigla())) {
            montarCartaoCredito(movPagamento, receivable);
        } else if (formaPagamento.getTipoFormaPagamento().equals(TipoFormaPagto.CARTAODEBITO.getSigla())) {
            movPagamento.setOperadoraCartaoVO(cache.obterOperadora(receivable.getCardFlag(), false));
            movPagamento.setAdquirenteVO(cache.obterAdquirente(receivable.getCardAcquirer()));
            movPagamento.setAutorizacaoCartao(receivable.getAuthorization());
            movPagamento.setNsu(receivable.getAuthorization());
        } else if (formaPagamento.getTipoFormaPagamento().equals(TipoFormaPagto.CHEQUE.getSigla())) {
            montarCheque(movPagamento, receivable);
        }
        movPagamento.setMovPagamentoEscolhida(true);
        return movPagamento;
    }

    private void montarCheque(MovPagamentoVO movPagamento, ReceivablesViewTO receivable) throws Exception {
        ChequeVO chequeVO = new ChequeVO();
        chequeVO.setBanco(cache.obterBanco(receivable.getBankAccount().getName()));
        chequeVO.setValor(receivable.getAmmount());
        chequeVO.setValorTotal(receivable.getAmmount());
        chequeVO.setConta("");
        chequeVO.setAgencia("");
        chequeVO.setNumero("");
        Date dataCompensacao = UteisImportacao.getDateFromLocalDateTime(receivable.getReceivingDate());
        chequeVO.setDataCompensacao(dataCompensacao);
        if (Calendario.maior(dataCompensacao, movPagamento.getDataLancamento())) {
            chequeVO.setVistaOuPrazo("PR");
        } else {
            chequeVO.setVistaOuPrazo("AV");
        }
        chequeVO.setSituacao("EA");
        movPagamento.getChequeVOs().add(chequeVO);
    }

    private void montarCartaoCredito(MovPagamentoVO movPagamento, ReceivablesViewTO receivable) throws Exception {
        movPagamento.setNrParcelaCartaoCredito(movPagamento.getNrParcelaCartaoCredito() + 1);
        movPagamento.setAutorizacaoCartao(receivable.getAuthorization());
        movPagamento.setNsu(receivable.getAuthorization());
        movPagamento.setAdquirenteVO(cache.obterAdquirente(receivable.getCardAcquirer()));
        movPagamento.setOperadoraCartaoVO(cache.obterOperadora(receivable.getCardFlag(), true));

        CartaoCreditoVO novocc = new CartaoCreditoVO();
        Date dataCompensacao = UteisImportacao.getDateFromLocalDateTime(receivable.getReceivingDate());
        if (dataCompensacao == null) {
            dataCompensacao = UteisImportacao.getDateFromLocalDateTime(receivable.getDueDate());
        }
        novocc.setDataCompensacao(dataCompensacao);
        novocc.setValor(receivable.getAmmount());
        novocc.setSituacao("EA");
        novocc.setOperadora(movPagamento.getOperadoraCartaoVO());
        novocc.setValorTotal(receivable.getAmmount());

        Integer nrParcela = null;
        String descricao = receivable.getDescription();
        if (!UteisValidacao.emptyString(descricao) && descricao.contains("/")) {
            descricao = descricao.trim();
            String descricaoParcelamento = descricao.substring(descricao.lastIndexOf(" "), descricao.length()).trim();
            if (descricaoParcelamento.contains("/")) {
                String[] conteudo = descricaoParcelamento.split("/");
                nrParcela = conteudo[0].trim().matches("\\d+") ? Integer.valueOf(conteudo[0]) : null;
            }
        }
        if (nrParcela == null) {
            nrParcela = !UteisValidacao.emptyNumber(movPagamento.getNrParcelaCartaoCredito())
                    ? movPagamento.getNrParcelaCartaoCredito() + 1 : 1;
        }

        novocc.setNrParcela(nrParcela);

        movPagamento.getCartaoCreditoVOs().add(novocc);
    }

    private PeriodoAcessoClienteVO montarPeriodoAcesso(ContratoVO contratoVO) {
        PeriodoAcessoClienteVO periodoAcesso = new PeriodoAcessoClienteVO();
        periodoAcesso.setPessoa(contratoVO.getPessoa().getCodigo());
        periodoAcesso.setContrato(contratoVO.getCodigo());
        periodoAcesso.setDataInicioAcesso(contratoVO.getVigenciaDe());
        periodoAcesso.setDataFinalAcesso(contratoVO.getVigenciaAteAjustada());
        periodoAcesso.setTipoAcesso("CA");
        periodoAcesso.setContratoBaseadoRenovacao(contratoVO.getContratoBaseadoRenovacao());
        return periodoAcesso;
    }


    public Integer obterIdMemberShipReceivables(List<MemberMembershipApiViewTO> memberShips, List<SaleItemApiViewTO> saleItemApiViewTOS) throws Exception {
        MemberMembershipApiViewTO memberShipReceivables = null;

        if (UteisValidacao.emptyList(memberShips)) {
            return 0;
        }

        Map<Integer, SaleItemApiViewTO> mapMemberShipsSaleItem = obterMapaMemberShipSaleItens(memberShips, saleItemApiViewTOS);

        for (MemberMembershipApiViewTO ms : memberShips) {
            if (memberShipReceivables == null) {
                memberShipReceivables = ms;
                continue;
            }
            SaleItemApiViewTO saleItem1 = mapMemberShipsSaleItem.get(ms.getIdMemberMembership());
            SaleItemApiViewTO saleItem2 = mapMemberShipsSaleItem.get(memberShipReceivables.getIdMemberMembership());
            if (saleItem1 != null & saleItem2 != null && saleItem1.getSaleValue() > saleItem2.getSaleValue()) {
                memberShipReceivables = ms;
            } else if (!ms.getName().toLowerCase().contains("dependente") && memberShipReceivables.getName().toLowerCase().contains("dependente")) {
                memberShipReceivables = ms;
            } else if (!ms.getName().toLowerCase().contains("dependente")) {
                Date endDate1 = Calendario.getDate("yyyy-MM-dd", memberShipReceivables.getEndDate());
                Date startDate2 = Calendario.getDate("yyyy-MM-dd", ms.getStartDate());
                Date endDate2 = Calendario.getDate("yyyy-MM-dd", ms.getEndDate());
                if (!Calendario.maior(startDate2, endDate1) && Calendario.maior(endDate2, endDate1)) {
                    memberShipReceivables = ms;
                }
            }
        }
        return memberShipReceivables.getIdMemberMembership();
    }

    private Map<Integer, SaleItemApiViewTO> obterMapaMemberShipSaleItens(List<MemberMembershipApiViewTO> memberShips, List<SaleItemApiViewTO> saleItemApiViewTOS) {
        Map<Integer, SaleItemApiViewTO> mapMemberShipsSaleItem = new HashMap<>(); // key = IdMemberMembership
        for (MemberMembershipApiViewTO ms : memberShips) {
            saleItemApiViewTOS.forEach(si -> {
                if (!UteisValidacao.emptyNumber(si.getIdMemberMembership()) && si.getIdMemberMembership().equals(ms.getIdMemberMembership())) {
                    mapMemberShipsSaleItem.put(ms.getIdMemberMembership(), si);
                }
            });

            if (mapMemberShipsSaleItem.get(ms.getIdMemberMembership()) == null) {
                String name = ms.getName().toLowerCase();
                Date inicio = UteisImportacao.getDateFromLocalDateTime(ms.getStartDate());

                saleItemApiViewTOS.forEach(si -> {
                    if (!UteisValidacao.emptyNumber(si.getIdMembership())
                            && si.getIdMembership().equals(ms.getIdMembership())
                            && !UteisValidacao.emptyString(name)
                            && !UteisValidacao.emptyString(si.getItem())
                            && !UteisValidacao.emptyString(si.getDescription())
                            && inicio != null
                    ) {
                        if (si.getItem().toLowerCase().contains(name) && si.getDescription().contains(Calendario.getData(inicio, "dd/MM/yyyy"))) {
                            mapMemberShipsSaleItem.put(ms.getIdMemberMembership(), si);
                        }
                    }
                });
            }
        }
        return mapMemberShipsSaleItem;
    }


    public void verificarCancelamentoContrato(ClienteVO clienteVO, MemberMembershipApiViewTO memberShip, Map<Integer, MemberMembershipApiViewTO> mapMemberShip) throws Exception {
        Integer codigoContratoImportado = this.cache.getContratosImportados().get(memberShip.getIdMemberMembership());
        if (UteisValidacao.emptyNumber(codigoContratoImportado)) {
            codigoContratoImportado = obterContratoRecorrenciaUnificadoProximasCobrancasEvo(clienteVO.getMatriculaExterna(), memberShip);
        }

        if (UteisValidacao.emptyNumber(codigoContratoImportado)) {
            return;
        }

        boolean contratoPodeSerCancelado = SuperFacadeJDBC.existe("select con.codigo from contrato con \n" +
                "where coalesce(con.id_externo, con.idexterno) = " + memberShip.getIdMemberMembership() + " \n" +
                "and con.situacao = 'AT' \n" +
                "and not exists (select cp.codigo from contratooperacao cp where cp.contrato = con.codigo and cp.tipooperacao = 'CA')", con);
        if (!contratoPodeSerCancelado) {
            return;
        }

        ContratoVO contratoVO = contratoDAO.consultarPorCodigo(codigoContratoImportado, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        String status = memberShip.getMembershipStatus().toLowerCase();
        if (status.equals(MemberShipStatusEnum.CANCELED.getName().toLowerCase()) && !UteisValidacao.emptyString(memberShip.getCancelDate())) {
            Date dataCancelamento = UteisImportacao.getDateFromLocalDateTime(memberShip.getCancelDate());
            if (Calendario.menorOuIgual(dataCancelamento, Calendario.hoje())) {
                contratoVO.setSituacao("CA");
                contratoVO.setVigenciaAteAjustada(dataCancelamento);
            }
        }
        processarCancelamento(contratoVO, memberShip);
        if (status.equals(MemberShipStatusEnum.TRANSFER.getName())) {
            processarCessaoDireitoDeUso(clienteVO, contratoVO, memberShip);
            processarCompartilhamentoComDependentes(contratoVO, memberShip.getIdMemberMembership(), mapMemberShip);
        }
    }

    private void processarCancelamento(ContratoVO contratoVO, MemberMembershipApiViewTO memberShip) throws Exception {
        ContratoOperacaoVO contratoOperacaoVO = null;
        if (contratoVO.getSituacao().equals("CA")) {
            MemberMemberShipCancellationApiViewTO memberShipCancellation = memberShip.getMemberShipCancellation();
            contratoOperacaoVO = montarOperacaoCancelamentoImportada(contratoVO, memberShipCancellation);

            Date dataCancelamento = UteisImportacao.getDateFromLocalDateTime(memberShip.getCancelDate());

            SuperFacadeJDBC.executarUpdate("update contrato set situacao = 'CA', \n" +
                    "vigenciaate = '" + Calendario.getDataAplicandoFormatacao(dataCancelamento, "yyyy-MM-dd") + "', \n" +
                    "vigenciaateajustada = '" + Calendario.getDataAplicandoFormatacao(dataCancelamento, "yyyy-MM-dd") + "', \n" +
                    "dataprevistarenovar = '" + Calendario.getDataAplicandoFormatacao(dataCancelamento, "yyyy-MM-dd") + "', \n" +
                    "dataprevistarematricula = '" + Calendario.getDataAplicandoFormatacao(dataCancelamento, "yyyy-MM-dd") + "' \n" +
                    " where codigo = " + contratoVO.getCodigo(), con);

            SuperFacadeJDBC.executarConsulta("update movparcela set situacao = 'CA' \n" +
                    "where situacao = 'EA' \n" +
                    "and datavencimento > '" + Calendario.getData(dataCancelamento, "yyyy-MM-dd") + "' \n" +
                    "and contrato = " + contratoVO.getCodigo(), con);
            SuperFacadeJDBC.executarConsulta("update movproduto mpro set situacao = 'CA' from movprodutoparcela mpp, movparcela mpar\n" +
                    "where mpp.movproduto = mpro.codigo \n" +
                    "and mpar.codigo = mpp.movparcela \n" +
                    "and mpro.situacao = 'EA' \n" +
                    "and mpar.situacao = 'CA' \n" +
                    "and mpro.contrato = " + contratoVO.getCodigo(), con);

            contratoOperacaoDAO.incluirSemCommit(contratoOperacaoVO, true);
        } else if (!UteisValidacao.emptyString(memberShip.getCancelDateOn())) {
            // Cancelamento Agendado
            Date cancelDateOn = UteisImportacao.getDateFromLocalDateTime(memberShip.getCancelDateOn());
            Date cancelCreationDate = UteisImportacao.getDateFromLocalDateTime(memberShip.getCancelCreationDate());
            cancelCreationDate = cancelCreationDate == null ? new Date() : cancelCreationDate;
            if (cancelDateOn != null) {
                contratoOperacaoVO = montarOperacaoCancelamentoAgendadoImportada(contratoVO, cancelCreationDate, cancelDateOn);
                contratoOperacaoDAO.incluirSemCommit(contratoOperacaoVO, true);

                SuperFacadeJDBC.executarConsulta("update movparcela set situacao = 'CA' \n" +
                        "where situacao = 'EA' \n" +
                        "and datavencimento >= '" + Calendario.getData(cancelDateOn, "yyyy-MM-dd") + "' \n" +
                        "and contrato = " + contratoVO.getCodigo(), con);
                SuperFacadeJDBC.executarConsulta("update movproduto mpro set situacao = 'CA' from movprodutoparcela mpp, movparcela mpar\n" +
                        "where mpp.movproduto = mpro.codigo \n" +
                        "and mpar.codigo = mpp.movparcela \n" +
                        "and mpro.situacao = 'EA' \n" +
                        "and mpar.situacao = 'CA' \n" +
                        "and mpro.contrato = " + contratoVO.getCodigo(), con);
            }
        }
        if (contratoOperacaoVO != null) {
            // historico contrato
            HistoricoContrato historicoContratoDAO = new HistoricoContrato(con);
            HistoricoContratoVO historicoContratoVO = historicoContratoDAO.obterUltimoHistoricoContratoPorContrato(contratoVO.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            if (historicoContratoVO != null && !UteisValidacao.emptyNumber(historicoContratoVO.getCodigo())) {
                String sql = "update historicocontrato set datafinalsituacao = ? where codigo = ? ";
                PreparedStatement pstm = con.prepareStatement(sql);
                pstm.setDate(1, Uteis.getDataJDBC(Calendario.somarDias(contratoOperacaoVO.getDataInicioEfetivacaoOperacao(), -1)));
                pstm.setInt(2, historicoContratoVO.getCodigo());
                pstm.execute();
            }

            HistoricoContratoVO historicoCancelado = new HistoricoContratoVO();
            historicoCancelado.setContrato(contratoVO.getCodigo());
            historicoCancelado.setDescricao("CANCELADO");
            historicoCancelado.setTipoHistorico("CA");
            historicoCancelado.setResponsavelRegistro(contratoVO.getResponsavelContrato());
            historicoCancelado.setDataRegistro(contratoOperacaoVO.getDataOperacao());
            historicoCancelado.setDataInicioSituacao(contratoOperacaoVO.getDataInicioEfetivacaoOperacao());
            historicoCancelado.setDataFinalSituacao(contratoOperacaoVO.getDataFimEfetivacaoOperacao());
            historicoContratoDAO.incluirSemCommit(historicoCancelado, false);

            // periodo acesso
            PeriodoAcessoCliente periodoAcessoClienteDAO = new PeriodoAcessoCliente(con);
            PeriodoAcessoClienteVO periodoAcessoClienteVO = periodoAcessoClienteDAO.obterUltimoDiaPeriodoAcessoContrato(contratoVO.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            if (periodoAcessoClienteVO != null && !UteisValidacao.emptyNumber(periodoAcessoClienteVO.getCodigo())) {
                String sql = "update periodoacessocliente set dataFinalAcesso = ? where codigo = ? ";
                PreparedStatement pstm = con.prepareStatement(sql);
                pstm.setDate(1, Uteis.getDataJDBC(Calendario.somarDias(contratoOperacaoVO.getDataInicioEfetivacaoOperacao(), -1)));
                pstm.setInt(2, periodoAcessoClienteVO.getCodigo());
                pstm.execute();
            }
            PeriodoAcessoClienteVO periodoAcessoClienteCancelamento = new PeriodoAcessoClienteVO();
            periodoAcessoClienteCancelamento.setPessoa(contratoVO.getPessoa().getCodigo());
            periodoAcessoClienteCancelamento.setContrato(contratoVO.getCodigo());
            periodoAcessoClienteCancelamento.setDataInicioAcesso(contratoOperacaoVO.getDataInicioEfetivacaoOperacao());
            periodoAcessoClienteCancelamento.setDataFinalAcesso(contratoOperacaoVO.getDataFimEfetivacaoOperacao());
            periodoAcessoClienteCancelamento.setTipoAcesso("CN");
            periodoAcessoClienteDAO.incluirSemCommit(periodoAcessoClienteCancelamento);
        }
    }

    private ContratoOperacaoVO montarOperacaoCancelamentoImportada(ContratoVO contratoVO, MemberMemberShipCancellationApiViewTO memberShipCancellation) throws Exception {
        ContratoOperacaoVO contratoOperacaoVO = new ContratoOperacaoVO();
        contratoOperacaoVO.setContrato(contratoVO.getCodigo());
        contratoOperacaoVO.setDataFimEfetivacaoOperacao(contratoVO.getVigenciaAteAjustada());
        contratoOperacaoVO.setDataInicioEfetivacaoOperacao(contratoVO.getVigenciaAteAjustada());
        contratoOperacaoVO.setDataOperacao(contratoVO.getVigenciaAteAjustada());
        String descricaCalculo = "Operação Importada" + (memberShipCancellation != null && !UteisValidacao.emptyString(memberShipCancellation.getReasonCancellation())
                ? ": \nMotivo: " + memberShipCancellation.getReasonCancellation() : "");
        contratoOperacaoVO.setDescricaoCalculo(descricaCalculo);
        contratoOperacaoVO.setOperacaoPaga(true);
        contratoOperacaoVO.setResponsavel(cache.getUsuarioVOImportacao());
        contratoOperacaoVO.setTipoOperacao("CA");
        contratoOperacaoVO.setTipoJustificativa(cache.obterJustificativaOperacao(contratoVO.getEmpresa().getCodigo(), "Importação", "CA"));
        return contratoOperacaoVO;
    }

    private ContratoOperacaoVO montarOperacaoCancelamentoAgendadoImportada(ContratoVO contratoVO, Date dataAgendamentoCancelamento, Date dataCancelamento) throws Exception {
        ContratoOperacaoVO contratoOperacaoVO = new ContratoOperacaoVO();
        contratoOperacaoVO.setContrato(contratoVO.getCodigo());
        contratoOperacaoVO.setDataOperacao(dataAgendamentoCancelamento);
        contratoOperacaoVO.setDataInicioEfetivacaoOperacao(dataCancelamento);
        contratoOperacaoVO.setDataFimEfetivacaoOperacao(dataCancelamento);
        String descricaCalculo = "Operação Importada - Cancelamento agendado";
        contratoOperacaoVO.setDescricaoCalculo(descricaCalculo);
        contratoOperacaoVO.setOperacaoPaga(true);
        contratoOperacaoVO.setResponsavel(cache.getUsuarioVOImportacao());
        contratoOperacaoVO.setTipoOperacao("CA");
        contratoOperacaoVO.setTipoJustificativa(cache.obterJustificativaOperacao(contratoVO.getEmpresa().getCodigo(), "Importação", "CA"));
        return contratoOperacaoVO;
    }

    private void processarCessaoDireitoDeUso(ClienteVO clienteTitular, ContratoVO contratoVO, MemberMembershipApiViewTO memberShip) {
        String status = memberShip.getMembershipStatus().toLowerCase();
        MembershipTrasnferDataApiViewTO memberShipTransfer = memberShip.getMemberShipCancellation() != null ? memberShip.getMemberShipCancellation().getMembershipTrasnferData() : null;
        if (status.equals(MemberShipStatusEnum.TRANSFER.getName().toLowerCase())
                && !(memberShip.getName().contains("dobro") && contratoVO.getValorFinal().equals(0.0))
                && memberShipTransfer != null
                && !UteisValidacao.emptyNumber(memberShipTransfer.getIdMemberTransfer())) {
            try {
                UsuarioVO usuarioAdmin = cache.obterUsuarioVO(1);

                ContratoVO contratoVOParaCeder = contratoDAO.consultarPorChavePrimaria(contratoVO.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);

                if (!UteisValidacao.emptyNumber(contratoVOParaCeder.getPessoaOriginal().getCodigo())) {
                    return;
                }

                ClienteVO clienteDependente = clienteDAO.consultarPorCodigoMatriculaExterna(memberShipTransfer.getIdMemberTransfer(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);

                if (UteisValidacao.emptyNumber(clienteDependente.getCodigo())) {
                    String msg = "Não vincular cessão do contrato %s. Dependente não encontrado para o id: %s ";
                    String msgFormatada = String.format(msg, contratoVO.getCodigo(), memberShipTransfer.getIdMemberTransfer());
                    falhasItemAtual.add(msgFormatada);
                    falhas.add(msgFormatada);
                    return;
                }

                if (!UteisValidacao.emptyNumber(clienteDependente.getTitularPlanoCompartilhado())) {
                    String msg = "Não vincular cessão do contrato %s. Dependente plano em dobro: %s %s";
                    String msgFormatada = String.format(msg, contratoVO.getCodigo(), clienteDependente.getMatricula(), clienteDependente.getNome_Apresentar());
                    falhasItemAtual.add(msgFormatada);
                    falhas.add(msgFormatada);
                    return;
                }

                ResultSet rs = SuperFacadeJDBC.criarConsulta("SELECT codigo FROM contrato WHERE coalesce(id_externo,idExterno) = " + memberShipTransfer.getIdMemberMembershipTransfer()
                        + " and empresa = " + clienteDependente.getEmpresa().getCodigo(), con);
                if (rs.next()) {
                    ContratoVO contratoEstornar = contratoDAO.consultarPorCodigo(rs.getInt("codigo"), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                    if (contratoEstornar != null && !UteisValidacao.emptyNumber(contratoEstornar.getCodigo())) {
                        estornarContrato(contratoEstornar);
                    }
                }


                zillyonWebFacadeDAO.transferirDireitoDeUso(contratoVOParaCeder, clienteDependente, usuarioAdmin, false);

                String msgFormatada = String.format("Cessão do contrato %s realizada com sucesso. Titular %s %s - Dependente %s %s",
                        contratoVO.getCodigo(),
                        clienteTitular.getMatricula(), clienteTitular.getNome_Apresentar(),
                        clienteDependente.getMatricula(), clienteDependente.getNome_Apresentar());
                Uteis.logar(true, null, msgFormatada);
            } catch (Exception ex) {
                String msg = String.format("Falha ao processar cessao direito de uso do contrato: %d cliente: %s %s", contratoVO.getCodigo(), clienteTitular.getMatricula(), clienteTitular.getPessoa().getNome());
                falhasItemAtual.add(msg);
                falhas.add(msg);
            }
        }
    }

    private void processarCompartilhamentoComDependentes(ContratoVO contratoTitular, Integer idMemberShipTitular, Map<Integer, MemberMembershipApiViewTO> mapMemberShip) throws Exception {
        if (!contratoTitular.getSituacao().equals("AT") || contratoTitular.getPlano().getQuantidadeCompartilhamentos().equals(0)) {
            return;
        }

        Integer codigoPessoaTitular = contratoTitular.getPessoaOriginal() != null && !UteisValidacao.emptyNumber(contratoTitular.getPessoaOriginal().getCodigo())
                ? contratoTitular.getPessoaOriginal().getCodigo() : contratoTitular.getPessoa().getCodigo();
        ClienteVO clienteTitular = clienteDAO.consultarPorCodigoPessoa(codigoPessoaTitular, Uteis.NIVELMONTARDADOS_DADOSBASICOS);

        for (Integer key : mapMemberShip.keySet()) {
            MemberMembershipApiViewTO memberShip = mapMemberShip.get(key);
            String status = memberShip.getMembershipStatus().toLowerCase();

            if ((memberShip.getName().toLowerCase().contains("dobro") || memberShip.getName().toLowerCase().contains("dependente"))
                    && status.equals(MemberShipStatusEnum.TRANSFER.getName().toLowerCase())
                    && memberShip.getMemberShipCancellation() != null
                    && memberShip.getMemberShipCancellation().getMembershipTrasnferData() != null
                    && !UteisValidacao.emptyNumber(memberShip.getMemberShipCancellation().getMembershipTrasnferData().getIdMemberTransfer())
                    && Calendario.maiorOuIgual(memberShip.getDataFim(), Calendario.hoje())
                    && !key.equals(idMemberShipTitular)
            ) {
                incluirCompatilharmentoDependente(clienteTitular, contratoTitular, memberShip.getMemberShipCancellation().getMembershipTrasnferData());
            }
        }
    }

    private void estornarContrato(ContratoVO contratoVO) throws Exception {
        ClienteVO cliente = null;
        contratoVO.setUsuarioVO(cache.obterUsuarioVO(1));

        List<ReciboPagamentoVO> listaReciboPagamento = reciboPagamentoDAO.consularReciboPagamParcelaPorCodigoContrato(contratoVO.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        if (!listaReciboPagamento.isEmpty()) {
            contratoVO.setMovParcelaVOs(new ArrayList<>());
            contratoVO.setListaEstornoRecibo(new ArrayList<>());
            for (ReciboPagamentoVO recibo : listaReciboPagamento) {
                EstornoReciboVO estornoRecibo = new EstornoReciboVO();
                estornoRecibo.setReciboPagamentoVO(recibo);
                estornoRecibo.setListaMovPagamento(movPagamentoDAO.consultarPorCodigoRecibo(recibo.getCodigo(), false, Uteis.NIVELMONTARDADOS_CAIXAPARAOPERADOR));
                estornoRecibo.setListaMovParcela(movParcelaDAO.consultarPorCodigoRecibo(recibo.getCodigo(), false, Uteis.NIVELMONTARDADOS_MINIMOS));

                //transações de cartão de crédito
                contratoVO.montarListaTransacoes(estornoRecibo.getListaMovParcela(), con);
                contratoVO.getListaEstornoRecibo().add(estornoRecibo);
            }

        } else {
            contratoVO.setMovParcelaVOs(movParcelaDAO.consultarPorContrato(contratoVO.getCodigo(), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA));

            //transações de cartão de crédito
            contratoVO.montarListaTransacoes(contratoVO.getMovParcelaVOs(), con);
        }

        contratoVO.setPrecisaEstornarTransacoes(false);

        contratoVO.montarListaItensRemessa(movParcelaDAO.consultarPorContrato(contratoVO.getCodigo(), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA), con);

        try {
            cliente = clienteDAO.consultarPorCodigoPessoa(contratoVO.getPessoa().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            contratoDAO.estornoContrato(contratoVO, cliente, null, null);
            zillyonWebFacadeDAO.atualizarSintetico(cliente, Calendario.hoje(), SituacaoClienteSinteticoEnum.GRUPO_TODOS, false);
        } catch (Exception e) {
            String msg = String.format("Erro ao estornar contrato -> %d do cliente -> %d | Exceção -> %s",
                    contratoVO.getCodigo(), cliente.getCodigo(), e.getMessage());
            falhasItemAtual.add(msg);
            falhas.add(msg);
        }

    }

    private void incluirCompatilharmentoDependente(ClienteVO clienteTitular, ContratoVO contratoVO, MembershipTrasnferDataApiViewTO memberShipTransfer) throws Exception {
        try {
            con.setAutoCommit(false);
            ClienteVO clienteDependente = clienteDAO.consultarPorCodigoMatriculaExterna(memberShipTransfer.getIdMemberTransfer(), clienteTitular.getEmpresa().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);

            ContratoVO contratoVODependente = null;
            ResultSet rsContrato = SuperFacadeJDBC.criarConsulta("SELECT codigo FROM contrato WHERE coalesce(id_externo,idExterno) = " + memberShipTransfer.getIdMemberMembershipTransfer(), con);
            if (rsContrato.next()) {
                contratoVODependente = contratoDAO.consultarPorCodigo(rsContrato.getInt("codigo"), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            }

            if (contratoVODependente != null && contratoVODependente.getValorFinal() > 0.0) {
                String msg = String.format("Não alterar dependência do cliente (Valor do contrato > R$ 0): %s - Titular: %s",
                        clienteDependente.getNome_Apresentar(), clienteTitular.getNome_Apresentar());
                falhasItemAtual.add(msg);
                falhas.add(msg);
                return;
            }

            if (contratoVODependente != null && !UteisValidacao.emptyNumber(contratoVODependente.getCodigo())) {
                estornarContrato(contratoVODependente);
                clienteDependente = clienteDAO.consultarPorCodigo(clienteDependente.getCodigo(), false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            }

            if (!UteisValidacao.emptyNumber(clienteDependente.getTitularPlanoCompartilhado())) {
                if (!clienteDependente.getTitularPlanoCompartilhado().equals(clienteTitular.getCodigo())) {
                    String msg = String.format("Depente: %s %s já possui compartilhamento, não será possivel compartilhar com titular: %s %s",
                            clienteDependente.getMatricula(), clienteDependente.getNome_Apresentar(), clienteTitular.getMatricula(), clienteTitular.getNome_Apresentar());
                    falhasItemAtual.add(msg);
                    falhas.add(msg);
                }
                return;
            }

            if (UteisValidacao.emptyNumber(clienteDependente.getCodigo())) {
                String msg = String.format("Falha ao compartilhar contrato do titular: %d %s com o dependente: %d | O cadastro do cliente dependente não encontrato!",
                        clienteTitular.getMatriculaExterna(), clienteTitular.getPessoa().getNome(), memberShipTransfer.getIdMemberTransfer());
                falhasItemAtual.add(msg);
                falhas.add(msg);
                return;
            }

            Optional<ContratoDependenteVO> contratoDependenteVO = contratoDependenteDAO.consultarProximoContratoDependenteDisponivelPorContrato(contratoVO.getCodigo());
            if (!contratoDependenteVO.isPresent()) {
                String msg = String.format("O contrato dependente não foi encontrato para o contrato titular %d ao tentar vincular os clientes %s %s e %s %s",
                        contratoVO.getCodigo(), clienteTitular.getMatricula(), clienteTitular.getNome_Apresentar(),
                        clienteDependente.getMatricula(), clienteDependente.getNome_Apresentar());
                falhasItemAtual.add(msg);
                falhas.add(msg);
                return;
            }

            FamiliarVO familiarVO = familiarDAO.consultarPorDependentePlanoCompartilhado(clienteDependente.getCodigo());
            if (familiarVO.getCodigo() == 0) {
                familiarVO.setCliente(clienteTitular.getCodigo());
                familiarVO.setFamiliar(clienteDependente.getCodigo());
                familiarVO.setNome(clienteDependente.getNome_Apresentar());
                familiarVO.setCodAcesso(clienteDependente.getCodAcesso());
                familiarVO.setParentesco(cache.obterParentesco("TITULAR"));
                familiarVO.setCompartilharPlano(true);
                familiarVO.setContratoCompartilhado(contratoVO.getCodigo());
                familiarVO.setContratoDependente(contratoDependenteVO.get());
                familiarVO.getContratoDependente().setCliente(clienteDependente);
                familiarDAO.incluir(familiarVO, true);
            }

            con.commit();
        } catch (Exception e) {
            String msg = String.format("Falha ao processar compartilhamento entre o titular: %d %s e dependente %d - erro: %s", clienteTitular.getCodigoMatricula(), clienteTitular.getNome_Apresentar(), memberShipTransfer.getIdMemberTransfer(), e.getMessage());
            falhasItemAtual.add(msg);
            falhas.add(msg);
            con.rollback();
        } finally {
            con.setAutoCommit(true);
        }
    }

    public void importarRecebimentosSemVendas(List<ReceivablesViewTO> receivables, ClienteVO clienteVO) throws Exception {
        receivables = receivables.stream().filter(r -> UteisValidacao.emptyNumber(r.getIdSale())).collect(Collectors.toList());

        Ordenacao.ordenarLista(receivables, "idReceivable");

        for (ReceivablesViewTO receivable: receivables) {
            if (receivable.getCancellationDate() != null) {
                continue;
            }
            ResultSet rsExiste = SuperFacadeJDBC.criarConsulta("select codigo from movparcela where idexterno = " + receivable.getIdReceivable(), con);
            if (rsExiste.next()) {
                return;
            }

            VendaAvulsa vendaAvulsaDAO = new VendaAvulsa(con);
            ItemVendaAvulsa itemVendaAvulsaDAO = new ItemVendaAvulsa(con);
            Date dataVenda = UteisImportacao.getDateFromLocalDateTime(receivable.getRegistrationDate());

            VendaAvulsaVO vendaAvulsaVO = new VendaAvulsaVO();
            vendaAvulsaVO.setOrigemSistema(OrigemSistemaEnum.ZW);
            vendaAvulsaVO.setTipoComprador("CI");
            vendaAvulsaVO.setCliente(clienteVO);
            vendaAvulsaVO.setDataRegistro(dataVenda);
            vendaAvulsaVO.setEmpresa(clienteVO.getEmpresa());
            vendaAvulsaVO.setNomeComprador(vendaAvulsaVO.getCliente().getPessoa().getNome());
            vendaAvulsaVO.setResponsavel(cache.getUsuarioVOImportacao());

            vendaAvulsaVO.setItemVendaAvulsaVOs(new ArrayList<>());

            ProdutoVO produtoVO = cache.obterProdutoVendaAvulsa("Ajuste negociação", TipoProduto.SERVICO);
            produtoVO.setValorFinal(receivable.getAmmount());
            ItemVendaAvulsaVO item = new ItemVendaAvulsaVO();
            item.setDataVenda(dataVenda);
            item.setQuantidade(1);
            item.setUsuarioVO(cache.getUsuarioVOImportacao());
            item.setProduto(produtoVO);
            vendaAvulsaVO.getItemVendaAvulsaVOs().add(item);

            try (ResultSet rs = vendaAvulsaDAO.incluirSemCommitSomenteVendaAvulsa(vendaAvulsaVO)) {
                if (rs.next()) {
                    for (ItemVendaAvulsaVO itemVendaAvulsaVO : vendaAvulsaVO.getItemVendaAvulsaVOs()) {
                        itemVendaAvulsaDAO.incluir(itemVendaAvulsaVO);
                    }

                    Map<Integer, ReceivablesViewTO> mapaParcelasReceivables = new HashMap<>();
                    mapaParcelasReceivables.put(receivable.getIdReceivable(), receivable);

                    vendaAvulsaVO.setCodigo(rs.getInt("codigo"));
                    gerarParcelasVenda(receivable, clienteVO, vendaAvulsaVO, dataVenda);
                    gerarMovProdutoVendaAvulsa(vendaAvulsaVO, cache.getUsuarioVOImportacao(), "EA", dataVenda);
                    gerarMovprodutoParcela(vendaAvulsaVO.getMovParcelaVOs(), vendaAvulsaVO.getMovProdutoVOs());
                    gerarPagamentoParcelas(vendaAvulsaVO.getMovParcelaVOs(), mapaParcelasReceivables);
                }
            }
        }
    }

}
