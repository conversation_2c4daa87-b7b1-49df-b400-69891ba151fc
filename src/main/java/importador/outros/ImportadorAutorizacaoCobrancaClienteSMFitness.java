package importador.outros;

import br.com.pactosolucoes.autorizacaocobranca.dao.AutorizacaoCobrancaCliente;
import br.com.pactosolucoes.autorizacaocobranca.modelo.AutorizacaoCobrancaClienteVO;
import br.com.pactosolucoes.autorizacaocobranca.modelo.TipoAutorizacaoCobrancaEnum;
import br.com.pactosolucoes.autorizacaocobranca.modelo.TipoObjetosCobrarEnum;
import br.com.pactosolucoes.comuns.util.StringUtilities;
import importador.LeitorExcel2010;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.financeiro.ConvenioCobrancaVO;
import negocio.comuns.financeiro.enumerador.OperadorasExternasAprovaFacilEnum;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.basico.Cliente;
import negocio.facade.jdbc.financeiro.ConvenioCobranca;
import negocio.facade.jdbc.utilitarias.Conexao;
import org.apache.poi.xssf.usermodel.XSSFRow;

import java.sql.Connection;
import java.sql.DriverManager;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * Created by glauco on 19/12/2017.
 */
public class ImportadorAutorizacaoCobrancaClienteSMFitness {

    public static Integer COLUNA_MATRICULA = 0;
    public static Integer COLUNA_NOME_ALUNO = 1;
    public static Integer COLUNA_NOME_CARTAO = 2;
    public static Integer COLUNA_CARTAO_SEM_MASCARA = 3;
    public static Integer COLUNA_CODIGO_SEGURANCA_CARTAO = 5;
    public static Integer COLUNA_VALIDADE_CARTAO = 6;

    public static void main(String[] args) {
        try {
            String pathExcel = "/opt/LEO SM.xlsx";
            Connection connection = new Conexao("****************************************************************", "postgres", "pactodb").getConexao();
            Conexao.guardarConexaoForJ2SE("smfitness", connection);

            int codConvenio = 1;

            importarAutorizacoes(pathExcel, codConvenio, connection);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private static void importarAutorizacoes(String pathExcel_plano, int codConvenio, Connection connection) throws Exception {
        StringBuilder nomesErro = new StringBuilder();
        try {
            System.out.println("INICIO IMPORTACAO");

            AutorizacaoCobrancaCliente autorizacaoCobrancaClienteDAO = new AutorizacaoCobrancaCliente(connection);
            Cliente clienteDAO = new Cliente(connection);
            ConvenioCobranca convenioCobrancaDAO = new ConvenioCobranca(connection);

            ConvenioCobrancaVO convenioCobrancaVO = convenioCobrancaDAO.consultarPorCodigoSemInfoEmpresa(codConvenio, Uteis.NIVELMONTARDADOS_TODOS);


            connection.setAutoCommit(false);

            List<XSSFRow> linhasPlano = LeitorExcel2010.lerLinhas(pathExcel_plano);
            int i = 0;

            for (XSSFRow linha : linhasPlano) {
                i++;
                System.out.println(i + " / " + linhasPlano.size());
                String nomeAluno = LeitorExcel2010.obterString(linha, COLUNA_NOME_ALUNO);
                Integer matricula = LeitorExcel2010.obterNumero(linha, COLUNA_MATRICULA).intValue();

                ClienteVO clienteVO = clienteDAO.consultarPorCodigoMatriculaExterna(matricula, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                try {
                    AutorizacaoCobrancaClienteVO autorizacaoCobrancaClienteVO = lerDadosCartao(linha);
                    autorizacaoCobrancaClienteVO.setCliente(clienteVO);
                    autorizacaoCobrancaClienteVO.setConvenio(convenioCobrancaVO);
                    autorizacaoCobrancaClienteVO.setTipoAutorizacao(TipoAutorizacaoCobrancaEnum.CARTAOCREDITO);
                    autorizacaoCobrancaClienteVO.setTipoACobrar(TipoObjetosCobrarEnum.APENAS_PLANOS);
                    OperadorasExternasAprovaFacilEnum operadoraEnum = null;
                    for (OperadorasExternasAprovaFacilEnum operadora : OperadorasExternasAprovaFacilEnum.values()) {
                        String binStart = "";
                        for (Integer bS : operadora.getBinStart()) {
                            binStart = String.valueOf(bS);
                            if (autorizacaoCobrancaClienteVO.getNumeroCartao().startsWith(binStart)) {
                                operadoraEnum = operadora;
                                break;
                            }
                            if (operadoraEnum != null) {
                                break;
                            }
                        }
                    }

                    if (operadoraEnum != null) {
                        autorizacaoCobrancaClienteVO.setOperadoraCartao(operadoraEnum);
                    } else {
                        throw new ConsistirException("Operadora não suportada");
                    }
                    autorizacaoCobrancaClienteDAO.incluir(autorizacaoCobrancaClienteVO);
                } catch (Exception ex) {
                    nomesErro.append(nomeAluno).append(" - ").append(ex.getMessage()).append("\n");
                }
            }

            connection.commit();
            System.out.println("IMPORTACAO REALIZADA COM SUCESSO");

            StringUtilities.saveToFile(new StringBuilder(nomesErro), "Erros-SMFitness.txt");
        } catch (Exception e) {
            connection.rollback();
            System.out.println("ERRO NA IMPORTACAO.");
            e.printStackTrace();
        } finally {
            connection.setAutoCommit(true);
            connection.close();
        }
    }


    private static AutorizacaoCobrancaClienteVO lerDadosCartao(XSSFRow linha) {
        AutorizacaoCobrancaClienteVO autorizacaoCobrancaClienteVO = new AutorizacaoCobrancaClienteVO();

        String nomeCartao = LeitorExcel2010.obterString(linha, COLUNA_NOME_CARTAO);
        long codCartao = LeitorExcel2010.obterNumero(linha, COLUNA_CARTAO_SEM_MASCARA).longValue();
        long cvv = LeitorExcel2010.obterNumero(linha, COLUNA_CODIGO_SEGURANCA_CARTAO).longValue();
        Date validade = LeitorExcel2010.obterDataEspecifico(linha, COLUNA_VALIDADE_CARTAO);

        autorizacaoCobrancaClienteVO.setNomeTitularCartao(nomeCartao);
        autorizacaoCobrancaClienteVO.setNumeroCartao(String.valueOf(codCartao));
        autorizacaoCobrancaClienteVO.setValidadeCartao(Uteis.getDataAplicandoFormatacao(validade, "MM/yyyy"));
        return autorizacaoCobrancaClienteVO;
    }

    public static Connection obterConexao(String hostBD, String porta, String nomeBD) throws Exception {
        String userBD = "postgres";
        String passwordBD = "pactodb";
        String url = "jdbc:postgresql://" + hostBD + ":" + porta + "/" + nomeBD;
        String driver = "org.postgresql.Driver";
        Class.forName(driver);
        return DriverManager.getConnection(url, userBD, passwordBD);
    }
}
