package negocio.interfaces.plano;

import negocio.comuns.financeiro.MovParcelaVO;
import negocio.comuns.financeiro.VendaAvulsaVO;
import negocio.comuns.plano.PlanoTextoPadraoVO;
import negocio.interfaces.basico.SuperInterface;

import java.io.IOException;
import java.util.Date;
import java.util.List;

/**
 * Interface reponsável por criar uma estrutura padrão de comunidação entre a camada de controle
 * e camada de negócio (em especial com a classe Façade). Com a utilização desta interface
 * é possível substituir tecnologias de uma camada da aplicação com mínimo de impacto nas demais.
 * Além de padronizar as funcionalidades que devem ser disponibilizadas pela camada de negócio, por intermédio
 * de sua classe Façade (responsável por persistir os dados das classes VO).
 */
public interface PlanoTextoPadraoInterfaceFacade extends SuperInterface {


    public PlanoTextoPadraoVO novo() throws Exception;

    public void incluir(PlanoTextoPadraoVO obj) throws Exception;

    public void alterar(PlanoTextoPadraoVO obj) throws Exception;
    public void alterarSemCommit(PlanoTextoPadraoVO obj) throws Exception;

    public void excluir(PlanoTextoPadraoVO obj) throws Exception;

    public PlanoTextoPadraoVO consultarPorChavePrimaria(Integer codigo, int nivelMontarDados) throws Exception;

    public List consultarPorCodigo(Integer valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public PlanoTextoPadraoVO consultarPorCodigoExato(Integer valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public List consultarPorCodigoSituacao(Integer valorConsulta, String situacao, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public List<PlanoTextoPadraoVO> consultarPorCodigoSituacaoTipo(Integer valorConsulta, String situacao,String tipoContrato, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public PlanoTextoPadraoVO consultarPorChavePrimariaSituacaoTipo(Integer valorConsulta, String situacao,String tipoContrato, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public PlanoTextoPadraoVO consultarPorDescricao(String descricao, int nivelMontarDados)throws Exception;

    public List consultarPorDescricao(String valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public List consultarPorDataDefinicao(Date prmIni, Date prmFim, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public List consultarPorSituacaoColaborador(String valorConsulta, int nivelMontarDados) throws Exception;

    public void setIdEntidade(String aIdEntidade);

    public void realizarCriacaoImagemLogo(PlanoTextoPadraoVO planoTexto) throws IOException;

    public String realizarAlteracaoHTML(String texto);

    public void incluirSemCommit(PlanoTextoPadraoVO obj) throws Exception;

    public String consultarJSON() throws Exception;

    public List consultarParaImpressao(String filtro, String ordem, String campoOrdenacao, int i) throws Exception;

    public Integer existeTermoResponsabilidade() throws Exception;

    boolean existePorTipoSituacao(String tipo, String situacao, Integer codigoDiferente);

    PlanoTextoPadraoVO consultarPorTipoSituacao(String tipoContrato, String situacao, int nivelMontarDados) throws Exception;

    String gerarComprovanteCompra(List<MovParcelaVO> listaMovParcelas) throws Exception;
}
