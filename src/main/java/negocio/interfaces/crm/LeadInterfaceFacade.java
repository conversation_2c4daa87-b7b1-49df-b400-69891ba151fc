/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package negocio.interfaces.crm;

import java.util.Date;
import java.util.List;

import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.ColaboradorVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.crm.HistoricoContatoVO;
import negocio.comuns.crm.LeadVO;
import negocio.interfaces.basico.SuperInterface;

/**
 * <AUTHOR>
 */
public interface LeadInterfaceFacade extends SuperInterface {


    void incluir(LeadVO obj) throws Exception;

    void alterar(LeadVO obj) throws Exception;

    void excluir(LeadVO obj) throws Exception;

    LeadVO consultarPorChavePrimaria(Integer codigo, int nivelMontarDados) throws Exception;

    List<LeadVO> consultarPorNome(String valorConsulta, boolean controlarAcesso, EmpresaVO empresa, int nivelMontarDados) throws Exception;

    LeadVO consultarPorPassivo(Integer codigoPassivo, int nivelMontarDados) throws Exception;

    LeadVO consultarPorCliente(Integer codigocliente, int nivelMontarDados) throws Exception;

    void executarAlteracaoClientePassivo(Integer codigoPassivo, Integer cliente) throws Exception;

    LeadVO consultarPorIndicado(Integer codigoIndicado, int nivelMontarDados) throws Exception;

    void executarAlteracaoClienteIndicado(Integer codigoIndicado, Integer cliente) throws Exception;

    LeadVO consultarPorCodigo(final Integer codigo, int nivelMontarDados) throws Exception;

    Integer contarLeadPorPeriodoResponsavelCadastro(List<UsuarioVO> listaUsuarioVO, Date dataInicioMeta, Date dataFimMeta, Integer empresa, boolean contar, boolean conversao) throws Exception;

    List<LeadVO> consultarLeadsConvertidosPorPeriodoResponsavelCadastro(List<UsuarioVO> listaUsuarioVO, Date dataInicioMeta, Date dataFimMeta, Integer empresa, int nivelMontarDados, boolean conversao) throws Exception;
}
