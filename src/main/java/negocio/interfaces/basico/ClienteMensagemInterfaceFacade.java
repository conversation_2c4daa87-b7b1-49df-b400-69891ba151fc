package negocio.interfaces.basico;

import br.com.pactosolucoes.estrutura.paginacao.ConfPaginacao;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.ClienteMensagemVO;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.basico.enumerador.TiposMensagensEnum;
import negocio.comuns.financeiro.MovParcelaVO;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.Date;
import java.util.List;
import negocio.comuns.plano.ProdutoVO;

/**
 * Interface reponsável por criar uma estrutura padrão de comunidação entre a camada de controle
 * e camada de negócio (em especial com a classe Façade). Com a utilização desta interface
 * é possível substituir tecnologias de uma camada da aplicação com mínimo de impacto nas demais.
 * Além de padronizar as funcionalidades que devem ser disponibilizadas pela camada de negócio, por intermédio
 * de sua classe Façade (responsável por persistir os dados das classes VO).
 */
public interface ClienteMensagemInterfaceFacade extends SuperInterface {

    public ClienteMensagemVO novo() throws Exception;

    public void incluir(ClienteMensagemVO obj) throws Exception;

    public void alterar(ClienteMensagemVO obj) throws Exception;

    void alterarSemCommit(ClienteMensagemVO obj) throws Exception;

    public void excluir(ClienteMensagemVO obj) throws Exception;

    public ClienteMensagemVO consultarPorChavePrimaria(Integer codigo, int nivelMontarDados) throws Exception;

    public ClienteMensagemVO consultarPorCodigoTipoMensagemECliente(Integer valorConsulta, String prm, int nivelMontarDados) throws Exception;

    public ClienteMensagemVO consultarPorCodigoTipoMensagemECliente(Integer valorConsulta, String prm, Boolean verificarPermissao, int nivelMontarDados) throws Exception;

    public List consultarMensagemProdutoVencidoPorCliente(Integer cliente, Boolean verificarPermissao, int nivelMontarDados) throws Exception;

    public List consultarPorCodigoTipoMensagem(Integer valorConsulta, String prm, int nivelMontarDados) throws Exception;

    public List consultarPorCodigoTipoMensagem(String prm, int nivelMontarDados) throws Exception;

    public ClienteMensagemVO consultarPorCodigoTipoMensagemReturnObjeto(String prm, int nivelMontarDados) throws Exception;

    public List consultarPorCodigoCliente(Integer valorConsulta, int nivelMontarDados) throws Exception;

    public List consultarPorCodigo(Integer valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public List<ClienteMensagemVO> consultarPorCliente(Integer valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public void setIdEntidade(String aIdEntidade);

    public Boolean consultarClienteMensagemPorClienteQuestionarioCliente(Integer cliente, Integer questionarioCliente, boolean controlarAcesso) throws Exception;

    public void alterarMensagemCadastroCliente(ClienteMensagemVO obj) throws Exception;

    public void incluirSemCommit(ClienteMensagemVO obj) throws Exception;

    public Boolean consultarClienteMensagemPorClienteCadastroIncompleto(Integer valorConsulta, boolean controlarAcesso) throws Exception;

    public List consultarPorTiposMensagens(Integer valorConsulta, List prm, int nivelMontarDados) throws Exception;

    /**
     * Responsável por realizar uma consulta de <code>ClienteMensagem</code> através do valor do atributo
     * <code>Integer codigo</code>. Retorna os objetos com valores iguais ou superiores ao parâmetro fornecido.
     * Faz uso da operação <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o List resultante.
     *
     * @param controlarAcesso Indica se a aplicação deverá verificar se o usuário possui permissão para esta consulta ou não.
     * @return List Contendo vários objetos da classe <code>ClienteMensagemVO</code> resultantes da consulta.
     * @throws Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    public Boolean consultarClienteMensagemPorMovParcela(Integer movParcela, boolean controlarAcesso) throws Exception;

    public Boolean consultarClienteMensagemPorProdutoVencido(Integer cliente, Integer produto, boolean controlarAcesso) throws Exception;

    /**
     * Responsável por realizar uma consulta de <code>ClienteMensagem</code> através do valor do atributo
     * <code>Integer codigo</code>. Retorna os objetos com valores iguais ou superiores ao parâmetro fornecido.
     *
     * @param cliente         Indica o código do cliente a ser pesquisado
     * @param controlarAcesso Indica se a aplicação deverá verificar se o usuário possui permissão para esta consulta ou não.
     * @return Boolean Retorna true caso exista mensagem de risco cadastrada
     * @throws Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    public Boolean consultarPorCodigoClienteRiscoMaiorQueSeis(Integer cliente, boolean controlarAcesso) throws Exception;

    /**
     * Responsável por realizar uma consulta de <code>ClienteMensagem</code> através do valor do atributo
     * <code>Integer cliente</code>. Retorna os objetos com valores iguais ou superiores ao parâmetro fornecido.
     *
     * @param cliente         Indica o código do cliente a ser pesquisado
     * @param controlarAcesso Indica se a aplicação deverá verificar se o usuário possui permissão para esta consulta ou não.
     * @return Boolean Retorna o codigo da mensagem de cadastro incompleto resultado da consulta ao banco para se existir poder alterar os dados desse registro
     * @throws Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    public ResultSet consultarPendenciaClienteMensagemDadosIncompletosPorCodigo(Integer cliente, boolean controlarAcesso) throws Exception;

    /**
     * Responsável por realizar uma consulta de <code>ClienteMensagem</code> através do valor do atributo
     * <code>Integer cliente</code>. Retorna os objetos com valores iguais ou superiores ao parâmetro fornecido.
     *
     * @param cliente         Indica o código do cliente a ser pesquisado
     * @param controlarAcesso Indica se a aplicação deverá verificar se o usuário possui permissão para esta consulta ou não.
     * @return Boolean Retorna o codigo da mensagem de cadastro incompleto resultado da consulta ao banco para se existir poder alterar os dados desse registro
     * @throws Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    public ResultSet consultarPendenciaClienteMensagemRiscoPorCodigo(Integer cliente, boolean controlarAcesso) throws Exception;

    /**
     * Responsável por realizar uma consulta de <code>ClienteMensagem</code> através do valor do atributo
     * <code>Integer codigo</code>. Retorna os objetos com valores iguais ou superiores ao parâmetro fornecido.
     * Faz uso da operação <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o List resultante.
     *
     * @param controlarAcesso Indica se a aplicação deverá verificar se o usuário possui permissão para esta consulta ou não.
     * @return ResultSet Contendo vários objetos da classe <code>ClienteMensagemVO</code> resultantes da consulta.
     * @throws Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    public ResultSet consultarPendenciaClienteMensagemPorEmpresaMovParcela(Integer empresa, boolean controlarAcesso) throws Exception;

    public Boolean consultarPendenciaClienteMensagemRiscoPeso(Integer cliente, Integer peso, boolean controlarAcesso) throws Exception;

    /**
     * Responsável por realizar uma consulta de <code>ClienteMensagem</code> através do valor do atributo
     * <code>Integer codigo</code>. Retorna os objetos com valores iguais ou superiores ao parâmetro fornecido.
     * Faz uso da operação <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o List resultante.
     *
     * @param controlarAcesso Indica se a aplicação deverá verificar se o usuário possui permissão para esta consulta ou não.
     * @return List Contendo vários objetos da classe <code>ClienteMensagemVO</code> resultantes da consulta.
     * @throws Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    public ResultSet consultarClienteMensagemPorEmpresaMovParcela(Integer empresa, String sql, boolean controlarAcesso) throws Exception;

    public ResultSet contarClienteMensagemCadastroIncompleto(Integer empresa, boolean controlarAcesso) throws Exception;

    public ResultSet contarVisitantesMensagemCadastroIncompleto(Integer empresa, boolean controlarAcesso) throws Exception;

    public ResultSet consultarVisitantesMensagemCadastroIncompleto(Integer empresa, boolean controlarAcesso) throws Exception;

    public ResultSet consultarClientesMensagemCadastroIncompleto(Integer empresa, boolean controlarAcesso) throws Exception;

    /**
     *
     * @param empresa               0 para consultar de todas as empresas
     * @param sql
     * @param controlarAcesso
     * @param paginacao
     * @param dataBaseInicio
     * @return
     * @throws Exception
     */
    public ResultSet consultarClienteMensagemCadastroIncompleto(Integer empresa, String sql, boolean controlarAcesso,ConfPaginacao paginacao, Date dataBaseInicio) throws Exception;

    public ResultSet consultarVisitanteMensagemCadastroIncompleto(Integer empresa, String sql, boolean controlarAcesso,ConfPaginacao paginacao, Date dataBaseInicio) throws Exception;

    /**
     * Responsável por realizar uma consulta de <code>ClienteMensagem</code> através do valor do atributo
     * <code>Integer codigo</code>. Retorna os objetos com valores iguais ou superiores ao parâmetro fornecido.
     * Faz uso da operação <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o List resultante.
     *
     * @param controlarAcesso Indica se a aplicação deverá verificar se o usuário possui permissão para esta consulta ou não.
     * @return List Contendo vários objetos da classe <code>ClienteMensagemVO</code> resultantes da consulta.
     * @throws Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    public ResultSet consultarPendenciaClienteMensagemPorEmpresaQuestionarioCliente(Integer empresa, boolean controlarAcesso) throws Exception;

    /**
     *
     * @param empresa             0 para consultar de todas as empresas
     * @param sql
     * @param controlarAcesso
     * @param paginacao
     * @param dataBaseInicio
     * @return
     * @throws Exception
     */
    public ResultSet consultarClienteMensagemPorEmpresaQuestionarioCliente(boolean contar, Integer empresa, String sql, boolean controlarAcesso,ConfPaginacao paginacao, Date dataBaseInicio) throws Exception;

    /**
     * Responsável por realizar uma consulta de <code>ClienteMensagem</code> através do valor do atributo
     * <code>Integer codigo</code>. Retorna os objetos com valores iguais ou superiores ao parâmetro fornecido.
     * Faz uso da operação <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o List resultante.
     *
     * @param controlarAcesso Indica se a aplicação deverá verificar se o usuário possui permissão para esta consulta ou não.
     * @return List Contendo vários objetos da classe <code>ClienteMensagemVO</code> resultantes da consulta.
     * @throws Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    public ResultSet consultarPendenciaClienteMensagemPorEmpresaColaboradorMovParcela(Integer empresa, String colaboradore, boolean controlarAcesso) throws Exception;

    /**
     * Responsável por realizar uma consulta de <code>ClienteMensagem</code> através do valor do atributo
     * <code>Integer codigo</code>. Retorna os objetos com valores iguais ou superiores ao parâmetro fornecido.
     * Faz uso da operação <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o List resultante.
     *
     * @param controlarAcesso Indica se a aplicação deverá verificar se o usuário possui permissão para esta consulta ou não.
     * @return List Contendo vários objetos da classe <code>ClienteMensagemVO</code> resultantes da consulta.
     * @throws Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    public ResultSet consultarPendenciaClienteMensagemPorEmpresaColaboradorQuestionario(Integer empresa, String colaboradore, boolean controlarAcesso, Date dataBaseInicio) throws Exception;

    /**
     *
     * @param empresa               0 para consultar de todas as empresas
     * @param colaboradore
     * @param situacao
     * @param controlarAcesso
     * @param dataBaseInicio
     * @return
     * @throws Exception
     */
    ResultSet contarPendenciaClienteMensagemPorEmpresaColaboradorCadastroIncompleto(Integer empresa, String colaboradore, String situacao, boolean controlarAcesso, Date dataBaseInicio) throws Exception;

    /**
     * Operação responsável por excluir no BD um objeto da classe <code>ClienteMensagemVO</code>.
     * Sempre localiza o registro a ser excluído através da chave primária da entidade.
     * Primeiramente verifica a conexão com o banco de dados e a permissão do usuário
     * para realizar esta operacão na entidade.
     * Isto, através da operação <code>excluir</code> da superclasse.
     */
    public void excluirClienteMensagemPorClienteCadastroIncompleto(Integer cliente, boolean controlarAcesso) throws Exception;

    /**
     * Operação responsável por excluir no BD um objeto da classe <code>ClienteMensagemVO</code>.
     * Sempre localiza o registro a ser excluído através da chave primária da entidade.
     * Primeiramente verifica a conexão com o banco de dados e a permissão do usuário
     * para realizar esta operacão na entidade.
     * Isto, através da operação <code>excluir</code> da superclasse.
     */
    public void excluirClienteMensagemPorClienteQuestionario(Integer cliente, Integer questionario) throws Exception;

    /**
     * Operação responsável por excluir no BD um objeto da classe <code>ClienteMensagemVO</code>.
     * Sempre localiza o registro a ser excluído através da chave primária da entidade.
     * Primeiramente verifica a conexão com o banco de dados e a permissão do usuário
     * para realizar esta operacão na entidade.
     * Isto, através da operação <code>excluir</code> da superclasse.
     */
    public void excluirClienteMensagemPorMovParcela(Integer movParcela) throws Exception;

    void excluirClienteMensagemPorMovParcelaContrato(Integer contrato) throws Exception;

    /**
     * Operação responsável por excluir no BD um objeto da classe <code>ClienteMensagemVO</code>.
     * Sempre localiza o registro a ser excluído através da chave primária da entidade.
     * Primeiramente verifica a conexão com o banco de dados e a permissão do usuário
     * para realizar esta operacão na entidade.
     * Isto, através da operação <code>excluir</code> da superclasse.
     */
    public void excluirClienteMensagemRiscoPeso(Integer pesoRisco) throws Exception;

    /**
     * Operação responsável por excluir no BD um objeto da classe <code>ClienteMensagemVO</code>.
     * Sempre localiza o registro a ser excluído através da chave primária da entidade.
     * Primeiramente verifica a conexão com o banco de dados e a permissão do usuário
     * para realizar esta operacão na entidade.
     * Isto, através da operação <code>excluir</code> da superclasse.
     */
    public void excluirClienteMensagemRisco(Integer cliente) throws Exception;

    public void excluirClienteMensagemProdutoVencido(Integer cliente, Integer produto) throws Exception;

    public ClienteMensagemVO consultarMensagemParcelasConcatenado(ClienteVO cliente) throws Exception;

    public void excluirClienteMensagemBVsPendentes(Integer colaborador, Date dataInicial, Date dataFinal, Integer empresa) throws Exception;

    public void excluirClienteMensagemCartaoVencido(String cartao, Integer cliente) throws Exception;

    public void lancarBloqueioCatraca(String mensagem, Integer codigoPessoa, UsuarioVO usuario) throws Exception;

    public void verificarBloqueados(List<MovParcelaVO> parcelas) throws Exception;

    public void lancarDesbloqueioCatraca(Integer codigoPessoa) throws Exception;
    
    
    public void processarProdutoAtestado(Integer cliente, ProdutoVO produto, Date finalVigencia, UsuarioVO usuario) throws Exception;
    
    
    public ClienteMensagemVO consultarObjClienteMensagemPorProdutoVencido(Integer cliente, Integer produto, boolean controlarAcesso) throws Exception;
    
    public void consultarClienteTipoMensagemConsultor(int codigoCliente, boolean controlarAcesso) throws Exception;

    public void excluirClienteMensagemRiscoClientesInativos() throws Exception;
    
    public List<ClienteMensagemVO> consultarTelaCliente(Integer codigo, Integer limit) throws Exception;

    public void desabilitarMensagem(ClienteMensagemVO obj) throws Exception ;

    public void desabilitarMensagemArmariosVencidosPorCliente(Integer cliente) throws Exception ;

    void excluirMensagemParcelaEmRemessa() throws Exception;

    public void excluirMensagemErradaPorMovParcelaVencida() throws Exception;

    void excluirMensagensErradasParcelasNaoVencidasEmAberto(Date dataAvaliar) throws SQLException;

    void excluirMensagensParcelasVencidasNaPessoaErradaContratosConcedidos() throws SQLException;

    void processarTodosClientesComCartaoCreditoVencido(UsuarioVO usuarioVO, Integer cliente) throws Exception;

    void excluirClienteMensagemClienteTipoMensagem(Integer cliente, TiposMensagensEnum tiposMensagensEnum) throws Exception;

    void processarMensagensCartaoVencidoCliente(Integer cliente, UsuarioVO usuarioVO) throws Exception;

    void excluirClienteMensagemProdutoVencido(ClienteVO clienteVO,
                                              ClienteMensagemVO clienteMensagemVO,
                                              UsuarioVO usuarioVO) throws Exception;

    void desbloquearMsgProdutoVencido(ClienteVO clienteVO,
                                      ClienteMensagemVO clienteMensagemVO, UsuarioVO usuarioVO) throws Exception;

    /**
     * Realiza o cancelamento das {@link ClienteMensagemVO} com base nas {@link MovParcelaVO} vinculadas ao {@link negocio.comuns.contrato.MovProdutoVO}
     * cujo movprodutoorinal seja igual ao parâmetro passado.
     * @param codigoMovProdutoOriginal
     * @throws Exception
     */


}
