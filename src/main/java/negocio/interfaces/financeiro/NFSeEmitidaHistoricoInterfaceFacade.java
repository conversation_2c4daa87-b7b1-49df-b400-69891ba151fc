package negocio.interfaces.financeiro;

import negocio.comuns.financeiro.ItemGestaoNotasTO;
import negocio.comuns.financeiro.NFSeEmitidaHistoricoVO;
import negocio.comuns.financeiro.NFSeEmitidaVO;
import negocio.comuns.notaFiscal.SituacaoNotaFiscalEnum;
import negocio.interfaces.basico.SuperInterface;

import java.sql.SQLException;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
public interface NFSeEmitidaHistoricoInterfaceFacade extends SuperInterface {

    void incluir(NFSeEmitidaHistoricoVO obj) throws Exception;

    void atualizarSituacaoIdRps(NFSeEmitidaVO obj) throws SQLException;

    void atualizarNrNotaManual(NFSeEmitidaVO obj) throws SQLException;

    void atualizarDataReferenciaJsonEnviar(Date dataReferencia, String jsonEnviar, Integer nfseemitida) throws Exception;

    void atualizarDataEnvioSituacao(Date dataEnvio, SituacaoNotaFiscalEnum situacaoNotaFiscal, String nfseemitida) throws Exception;

    void atualizarCartao(Integer codAntigo, Integer codNovo) throws SQLException;

    void atualizarCheque(Integer codAntigo, Integer codNovo) throws SQLException;

    void atualizarMovPagamento(Integer codAntigo, Integer codNovo) throws SQLException;

    List<NFSeEmitidaHistoricoVO> consultarPorEmpresaDataReferencia(Integer empresa, Date dataInicio, Date dataFinal) throws Exception;

    List<ItemGestaoNotasTO> consultarNotasExcluidasApresentar(Integer empresa, Date dataInicio, Date dataFinal) throws Exception;

    boolean existeNotasExcluidas(Integer empresa, Date dataInicio, Date dataFinal) throws Exception;

    void excluirPorNFSeEmitida(NFSeEmitidaVO nfSeEmitidaVO) throws SQLException;

    void atualizarJsonEnviarIdReferencia(NFSeEmitidaVO nfSeEmitidaVO) throws Exception;
}
