package negocio.facade.jdbc.oamd;

import br.com.pacto.priv.utils.Uteis;
import br.com.pactosolucoes.ce.comuns.to.PessoaTO;
import br.com.pactosolucoes.comuns.json.JSONMapper;
import br.com.pactosolucoes.comuns.util.Formatador;
import br.com.pactosolucoes.comuns.util.JSFUtilities;
import br.com.pactosolucoes.enumeradores.TipoConsultaCupomDescontoEnum;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.contrato.ContratoVO;
import negocio.comuns.financeiro.MovParcelaCupomDescontoVO;
import negocio.comuns.financeiro.MovParcelaVO;
import negocio.comuns.financeiro.MovProdutoParcelaVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.facade.jdbc.financeiro.MovParcela;
import negocio.facade.jdbc.financeiro.MovParcelaCupomDesconto;
import negocio.facade.jdbc.financeiro.MovProdutoParcela;
import negocio.interfaces.oamd.CupomDescontoInterfaceFacade;
import negocio.oamd.CampanhaCupomDescontoPremioPortadorVO;
import negocio.oamd.CampanhaCupomDescontoVO;
import negocio.oamd.CupomDescontoVO;
import negocio.oamd.CupomDescontoWS;
import negocio.oamd.HistoricoUtilizacaoCupomDescontoVO;
import org.json.JSONArray;
import org.json.JSONObject;
import servicos.impl.oamd.OAMDService;
import servicos.propriedades.PropsService;
import servicos.util.ExecuteRequestHttpService;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.Statement;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * Created by ulisses on 27/05/2016.
 */
public class CupomDesconto implements CupomDescontoInterfaceFacade {

    public List<CupomDescontoVO> incluirLoteCupomOAMD(CampanhaCupomDescontoVO campanhaCupomDescontoVO, Integer lote, Integer totalCupomGerar, String nomeCupomEspecifico) throws Exception {
        JSONArray jsonArray;
        List<CupomDescontoVO> cupons = new ArrayList<>();
        ExecuteRequestHttpService executeRequestHttpService = new ExecuteRequestHttpService();

        String url = PropsService.getPropertyValue(PropsService.urlOamd) + "/prest/cupomDesconto/incluirLoteCupomOAMD";
        Map<String, String> headers = new HashMap<>();
        headers.put("lote", String.valueOf(lote));
        headers.put("totalCupomGerar", String.valueOf(totalCupomGerar));
        headers.put("nomeCupomEspecifico", nomeCupomEspecifico);

        try {
            String response = executeRequestHttpService.executeRequestCupomDesconto(url, campanhaCupomDescontoVO.toJSON(), headers);
            jsonArray = new JSONObject(response).getJSONArray("sucesso");
            montarListaCupomDescontoJson(jsonArray, cupons);
        } catch (Exception e) {
            throw new Exception("Falha ao obter cupons, recarregue a pagina.");
        }

        return cupons;
    }

    private void montarListaCupomDescontoJson(JSONArray jsonArray, List<CupomDescontoVO> cupons) throws Exception {
        for (int i = 0; i < jsonArray.length(); i++) {
            JSONObject obj = (JSONObject) jsonArray.get(i);
            CupomDescontoVO cupomDescontoVO = JSONMapper.getObject(obj, CupomDescontoVO.class);
            cupons.add(cupomDescontoVO);
        }
    }

    private void montarListaHistoricoCupomDescontoJson(JSONArray jsonArray, List<HistoricoUtilizacaoCupomDescontoVO> historicoCupons) throws Exception {
        for (int i = 0; i < jsonArray.length(); i++) {
            JSONObject obj = (JSONObject) jsonArray.get(i);
            HistoricoUtilizacaoCupomDescontoVO historicoUtilizacaoCupomDescontoVO = JSONMapper.getObject(obj, HistoricoUtilizacaoCupomDescontoVO.class);
            historicoCupons.add(historicoUtilizacaoCupomDescontoVO);
        }
    }

    public void gravarPremioAlunoOAMD(CupomDescontoVO cupomDescontoVO) throws Exception {
        ExecuteRequestHttpService executeRequestHttpService = new ExecuteRequestHttpService();
        String url = PropsService.getPropertyValue(PropsService.urlOamd) + "/prest/cupomDesconto/gravarPremioAlunoOAMD";
        try {
            String response = executeRequestHttpService.executeRequestCupomDesconto(url, cupomDescontoVO.toJSON(), new HashMap<>());
            new JSONObject(response).get("string").toString().equals("sucesso");
        } catch (Exception e) {
            throw new Exception("Falha ao gravar premio do aluno, tente novamente!");
        }
    }

    public List<CupomDescontoWS> consultarCupomDescontoOAMD(String keyRede, String listaCupom) throws Exception {
        JSONArray jsonArray;
        List<CupomDescontoWS> listCupomWS = new ArrayList<>();
        ExecuteRequestHttpService executeRequestHttpService = new ExecuteRequestHttpService();
        String url = PropsService.getPropertyValue(PropsService.urlOamd) + "/prest/cupomDesconto/consultarCupomDescontoOAMD";
        Map<String, String> headers = new HashMap<>();
        headers.put("keyRede", keyRede);
        headers.put("listaCupom", listaCupom);
        try {
            String response = executeRequestHttpService.executeRequestCupomDesconto(url, "", headers);
            jsonArray = new JSONObject(response).getJSONArray("sucesso");
            for (int i = 0; i < jsonArray.length(); i++) {
                JSONObject obj = (JSONObject) jsonArray.get(i);
                CupomDescontoWS cupomDescontoWS = JSONMapper.getObject(obj, CupomDescontoWS.class);
                listCupomWS.add(cupomDescontoWS);
            }
        } catch (Exception e) {
            throw new Exception("Falha ao obter cupons, recarregue a pagina.");
        }
        return listCupomWS;
    }

    public CupomDescontoVO validarCupomPortadorCupomOAMD(Integer codigoFavorecido, String numeroCupom, String nomePlano) throws Exception {
        ExecuteRequestHttpService executeRequestHttpService = new ExecuteRequestHttpService();
        String url = PropsService.getPropertyValue(PropsService.urlOamd) + "/prest/cupomDesconto/validarCupomPortadorCupomOAMD";
        Map<String, String> headers = new HashMap<>();
        headers.put("codigoFavorecido", codigoFavorecido.toString());
        headers.put("numeroCupom", numeroCupom);
        headers.put("nomePlano", nomePlano);
        try {
            String response = executeRequestHttpService.executeRequestCupomDesconto(url, nomePlano, headers);
            if(response.contains("HTTP Status 500")){
                CupomDescontoVO cupomMsg = new CupomDescontoVO();
                cupomMsg.setMsgValidacao("Operação não permitida. Cupom não cadastrado para esta empresa.");
                return cupomMsg;
            }
            return JSONMapper.getObject(new JSONObject(response).getJSONObject("sucesso"), CupomDescontoVO.class);
        } catch (Exception e) {
            Logger.getLogger(getClass().getSimpleName()).log(Level.SEVERE, "#### ERRO CupomDesconto.java - validarCupomPortadorCupomOAMD - ####");
            e.printStackTrace();
            throw new Exception("Falha ao validar portador do cupom, tente novamente!");
        }
    }


    public void gravarObservacaoProcessamentoOAMD(CupomDescontoVO cupomDescontoVO) throws Exception {
        ExecuteRequestHttpService executeRequestHttpService = new ExecuteRequestHttpService();
        String url = PropsService.getPropertyValue(PropsService.urlOamd) + "/prest/cupomDesconto/gravarObservacaoProcessamentoOAMD";
        try {
            String response = executeRequestHttpService.executeRequestCupomDesconto(url, cupomDescontoVO.toJSON(), new HashMap<>());
            new JSONObject(response).get("string").toString().equals("sucesso");
        } catch (Exception e) {
            throw new Exception("Falha ao gravar observação do processamento, tente novamente!");
        }
    }

    public CupomDescontoVO consultarPorNumeroCupomOAMD(String numeroCupom, Integer codigoFavorecido) throws Exception {
        ExecuteRequestHttpService executeRequestHttpService = new ExecuteRequestHttpService();
        String url = PropsService.getPropertyValue(PropsService.urlOamd) + "/prest/cupomDesconto/consultarPorNumeroCupomOAMD";
        Map<String, String> headers = new HashMap<>();
        headers.put("numeroCupom", numeroCupom);
        headers.put("codigoFavorecido", codigoFavorecido.toString());
        try {
            String response = executeRequestHttpService.executeRequestCupomDesconto(url, "", headers);
            return JSONMapper.getObject(new JSONObject(response).getJSONObject("sucesso"), CupomDescontoVO.class);
        } catch (Exception e) {
            throw new Exception("Falha ao consultar cupom por nome, tente novamente!");
        }
    }

    public void concederPremioCupomPortadorCupom(Connection conZillyon, String chaveZW, ContratoVO contratoVO, String numeroCupom, Integer codigoCliente, String nomeCliente, Double valorPremioProdutos) throws Exception {
        CupomDescontoVO cupomDescontoVO = consultarPorNumeroCupomOAMD(numeroCupom, contratoVO.getEmpresa().getCodEmpresaFinanceiro());
        double totalPremioParcelas = concederDescontoMensalidadePortadorCupom(conZillyon, contratoVO, numeroCupom);
        double totalPremioPortadorCupom = totalPremioParcelas + valorPremioProdutos;
        cupomDescontoVO.setChavePortadorCupom(chaveZW);
        cupomDescontoVO.setCodigoClientePortadorCupom(codigoCliente);
        cupomDescontoVO.setNomePortadorCupom(nomeCliente);
        cupomDescontoVO.setValorPremioPortadorCupom(Uteis.arredondarForcando2CasasDecimais(totalPremioPortadorCupom));
        cupomDescontoVO.setDataPremioPortadorCupom(Uteis.getDataJDBCTimestamp(Calendario.hoje()));
        cupomDescontoVO.setValorPremioProdutosPortadorCupom(Uteis.arredondarForcando2CasasDecimais(valorPremioProdutos));
        cupomDescontoVO.setValorPremioMensalidadePortadorCupom(Uteis.arredondarForcando2CasasDecimais(totalPremioParcelas));
        cupomDescontoVO.setEmpresaPortadorCupom(contratoVO.getEmpresa().getCodigo());
        cupomDescontoVO.setQtdUtilizadoCuponsNomeFixo(cupomDescontoVO.getQtdUtilizadoCuponsNomeFixo() + 1);
        alterarCupom(cupomDescontoVO);
        adicionarHistoricoUtilizacaoCupom(cupomDescontoVO, contratoVO.getCodigo());
    }

    private double concederDescontoMensalidadePortadorCupom(Connection conZW, ContratoVO contratoVO, String numeroCupom) throws Exception {
        double totalDescontoParcelas = 0;
        MovParcela movParcelaDao = new MovParcela(conZW);
        MovProdutoParcela movProdutoParcelaDao = new MovProdutoParcela(conZW);
        MovParcelaCupomDesconto movParcelaCupomDescontoDao = new MovParcelaCupomDesconto(conZW);
        CampanhaCupomDescontoPremioPortador campanhaCupomDescontoPremioPortadorDao = new CampanhaCupomDescontoPremioPortador();
        List<CampanhaCupomDescontoPremioPortadorVO> listaPremioPortadorCupom = campanhaCupomDescontoPremioPortadorDao.consultarOAMD(contratoVO.getIdCampanhaCupomDesconto(), contratoVO.getPlano().getDescricao());
        for (Object objParcela : contratoVO.getMovParcelaVOs()) {
            MovParcelaVO movParcelaVO = (MovParcelaVO) objParcela;
            for (CampanhaCupomDescontoPremioPortadorVO premio : listaPremioPortadorCupom) {
                if (premio.getTipoPremio().equals(CampanhaCupomDescontoPremioPortadorVO.TIPO_PREMIO_MENSALIDADE)) {
                    if (premio.getDescricaoPremio().equals(movParcelaVO.getDescricao()) && movParcelaVO.getSituacao().equals("EA")) {
                        double desconto = 0d;
                        if(contratoVO.getPlano() != null && (contratoVO.getPlano().getCobrarProdutoSeparado() || contratoVO.getPlano().getCobrarAdesaoSeparada())) {
                            desconto = premio.calcularDescontoPremio(contratoVO.getValorContrato());
                        }else{
                            desconto = premio.calcularDescontoPremio(movParcelaVO.getValorParcela());
                        }
                        totalDescontoParcelas = totalDescontoParcelas + desconto;
                        UsuarioVO usuarioVO = contratoVO.getUsuarioVO();
                        if (usuarioVO == null) {
                            usuarioVO = contratoVO.getResponsavelContrato();
                        }
                        concederDescontoParcelasAoPortadorCupom(movParcelaDao, movProdutoParcelaDao, movParcelaCupomDescontoDao, movParcelaVO, conZW, desconto, numeroCupom, usuarioVO);
                    }
                }
            }
        }
        return totalDescontoParcelas;
    }

    private void concederDescontoParcelasAoPortadorCupom(MovParcela movParcelaDao, MovProdutoParcela movProdutoParcelaDao,
                                                         MovParcelaCupomDesconto movParcelaCupomDescontoDao, MovParcelaVO movParcelaVO,
                                                         Connection conZW,
                                                         double valorDesconto, String numeroCupom, UsuarioVO usuarioVO) throws Exception {

        movParcelaVO = movParcelaDao.consultarPorChavePrimaria(movParcelaVO.getCodigo(), negocio.comuns.utilitarias.Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        movParcelaVO.setMovProdutoParcelaVOs(movProdutoParcelaDao.consultarPorCodigoMovParcela(movParcelaVO.getCodigo(), negocio.comuns.utilitarias.Uteis.NIVELMONTARDADOS_DADOSENTIDADESPRINCIPAIS));
        preencherNumeroCupomMovProduto(movParcelaVO.getMovProdutoParcelaVOs(), numeroCupom, conZW);
        movParcelaVO.setParcelaEscolhida(true);
        if (valorDesconto > Uteis.arredondarForcando2CasasDecimais(movParcelaVO.getValorParcela())) {
            valorDesconto = Uteis.arredondarForcando2CasasDecimais(movParcelaVO.getValorParcela());
        }

        List<MovParcelaVO> parcelasRenegociar = new ArrayList<>();
        parcelasRenegociar.add(movParcelaVO);

        // parcela desconto
        MovParcelaVO parcelaRenegociar = new MovParcelaVO();
        parcelaRenegociar.setDescricao("DESCONTOS");
        parcelaRenegociar.setValorParcela(valorDesconto);
        parcelaRenegociar.setDataVencimento(Calendario.hoje());
        parcelasRenegociar.add(parcelaRenegociar);

        MovParcelaVO parcelaDesconto = new MovParcelaVO();
        parcelaDesconto.setDescricao("");
        parcelaDesconto.setValorParcela(valorDesconto);
        parcelaDesconto.setDataVencimento(Calendario.hoje());

        // Parcelas Renegociadas
        List<MovParcelaVO> parcelasRenegociadas = new ArrayList<>();
        MovParcelaVO novaParcela = (MovParcelaVO) movParcelaVO.getClone(true);
        novaParcela.setDescricao("PARCELA RENEGOCIADA");
        novaParcela.setValorParcela(Uteis.arredondarForcando2CasasDecimais(movParcelaVO.getValorParcela() - valorDesconto));
        novaParcela.setDataRegistro(Calendario.hoje());
        parcelasRenegociadas.add(novaParcela);
        parcelaDesconto.setDescricao("");

        movParcelaDao.renegociarParcelas(parcelasRenegociar, parcelasRenegociadas, parcelaDesconto, new MovParcelaVO(), "DE", false, null, null, 0.0, false, usuarioVO, true, true, true, null, null);

        MovParcelaCupomDescontoVO movParcelaCupomDescontoVO = new MovParcelaCupomDescontoVO();
        movParcelaCupomDescontoVO.setMovParcelaVO(parcelasRenegociadas.get(0));
        movParcelaCupomDescontoVO.setCupomDesconto(numeroCupom);
        String usuario = usuarioVO.getUsername();
        String usuarioOAMD = usuarioVO.getUserOamd();
        usuario = ((usuarioOAMD != null) && (!usuarioOAMD.trim().equals(""))) ? usuario + " - UserOAMD:" + usuarioOAMD : usuario;
        movParcelaCupomDescontoVO.setResponsavelDesconto(usuario);
        movParcelaCupomDescontoVO.setValorDesconto(valorDesconto);
        movParcelaCupomDescontoVO.setDescontoContratoNovo(true);
        movParcelaCupomDescontoDao.incluir(movParcelaCupomDescontoVO);
    }

    private void preencherNumeroCupomMovProduto(List<MovProdutoParcelaVO> listaMovProdutoParcelaVO, String numeroCupomDesconto, Connection conZW) throws Exception {
        PreparedStatement pst = conZW.prepareStatement("update movProduto set numeroCupomDesconto = ? where codigo = ? ");
        for (MovProdutoParcelaVO movProdutoParcelaVO : listaMovProdutoParcelaVO) {
            pst.setString(1, numeroCupomDesconto);
            pst.setInt(2, movProdutoParcelaVO.getMovProduto());
            pst.execute();
        }
    }


    public void cancelarUtilizacaoCupomDescontoOAMD(String numeroCupomDesconto, String chaveZW) throws Exception {
        ExecuteRequestHttpService executeRequestHttpService = new ExecuteRequestHttpService();
        String url = PropsService.getPropertyValue(PropsService.urlOamd) + "/prest/cupomDesconto/cancelarUtilizacaoCupomDescontoOAMD";
        Map<String, String> headers = new HashMap<>();
        headers.put("numeroCupomDesconto", numeroCupomDesconto);
        headers.put("chaveZW", chaveZW);
        try {
            String response = executeRequestHttpService.executeRequestCupomDesconto(url, "", headers);
            new JSONObject(response).get("string").toString().equals("sucesso");
        } catch (Exception e) {
            Logger.getLogger(getClass().getSimpleName()).log(Level.SEVERE, "#### ERRO CupomDesconto.java - cancelarUtilizacaoCupomDescontoOAMD - ####");
            e.printStackTrace();
            throw new Exception("Falha ao cancelar utilização cupom, tente novamente!");
        }
    }

    public List<HistoricoUtilizacaoCupomDescontoVO> consultarHistoricoUtilizacaoCuponsOAMD(CampanhaCupomDescontoVO campanhaCupomDescontoVO, String chaveZW, Integer empresaZW, TipoConsultaCupomDescontoEnum tipoConsultaCupomDescontoEnum, String numeroCupom) throws Exception {
        JSONArray jsonArray;
        List<HistoricoUtilizacaoCupomDescontoVO> historicoCupons = new ArrayList<>();
        ExecuteRequestHttpService executeRequestHttpService = new ExecuteRequestHttpService();
        String url = PropsService.getPropertyValue(PropsService.urlOamd) + "/prest/historicoUtilizacaoCupomDesconto/consultarOAMD";
        Map<String, String> headers = new HashMap<>();
        headers.put("chaveZW", chaveZW);
        headers.put("empresaZW", String.valueOf(empresaZW));
        headers.put("tipoConsultaCupomDescontoEnum", String.valueOf(tipoConsultaCupomDescontoEnum));
        headers.put("numeroCupom", numeroCupom);
        try {
            String response = executeRequestHttpService.executeRequestCupomDesconto(url, campanhaCupomDescontoVO.toJSON(), headers);
            jsonArray = new JSONObject(response).getJSONArray("sucesso");
            montarListaHistoricoCupomDescontoJson(jsonArray, historicoCupons);
        } catch (Exception e) {
            throw new Exception("Falha ao obter cupons, recarregue a pagina.");
        }
        return historicoCupons;
    }

    public String concederPremioCupomDescontoAoAluno(OAMDService oamdService, CampanhaCupomDescontoVO campanhaCupomDescontoVO, List<CupomDescontoVO> listaCupomDesconto, Date dataBaseVencimentoMatricula, UsuarioVO usuarioVO, Integer codigoFavorecido) throws Exception {
        MovParcela movParcelaDao = new MovParcela();
        MovParcelaCupomDesconto movParcelaCupomDescontoDao = new MovParcelaCupomDesconto();
        MovProdutoParcela movProdutoParcelaDao = new MovProdutoParcela();
        Date dataProcessamento = Calendario.hoje();
        Double totalIsencaoMensalide = 0.0;
        Integer totalCuponsUtilizadosParaIsencao = 0;
        StringBuilder resultado = new StringBuilder();
        SimpleDateFormat sdf = new SimpleDateFormat("dd/MM/yyyy");
        System.out.println(Calendario.hoje() + " - INICIO PROCESSO CONCEDER PREMIO ALUNO");
        RedeEmpresa redeEmpresa = new RedeEmpresa(oamdService.getConOAMD());
        Map<String, Connection> mapaConexaoRede = redeEmpresa.obterConexoesRedeEmpresa((String) JSFUtilities.getFromSession("key"));
        try {
            for (CupomDescontoVO cupomDescontoVO : listaCupomDesconto) {

                String ret = identificarAlunoCupom(redeEmpresa, campanhaCupomDescontoVO, cupomDescontoVO, dataProcessamento, mapaConexaoRede, codigoFavorecido);
                if (ret != null) {
                    resultado.append(ret);
                    continue;
                }


                if ((cupomDescontoVO.getCodigoAluno() != null) &&
                        (cupomDescontoVO.getDataPremioAluno() == null)) {

                    if (cupomDescontoVO.getDataPremioPortadorCupom() != null) {
                        if (cupomDescontoVO.getListaPessoaTO().size() == 1) {
                            Connection connection = cupomDescontoVO.getConnection();
                            connection.setAutoCommit(false);
                            try {
                                cupomDescontoVO.setDataProcessamento(dataProcessamento);
                                movParcelaDao.setCon(connection);
                                movParcelaCupomDescontoDao.setCon(connection);
                                movProdutoParcelaDao.setCon(connection);

                                Date debitoEmAbertoMaisAntigo = consultarDebitoEmAbertoMaisAntigo(cupomDescontoVO, dataBaseVencimentoMatricula, connection);
                                if (debitoEmAbertoMaisAntigo != null) {
                                    cupomDescontoVO.setObservacaoProcessamento("O cliente está inadimplente. Vencimento mais antigo: " + sdf.format(debitoEmAbertoMaisAntigo));
                                    gravarObservacaoProcessamentoOAMD(cupomDescontoVO);
                                    continue;
                                }

                                MovParcelaVO movParcelaVO = consultarParcelaMensalidadeMaisAntiga(cupomDescontoVO, dataBaseVencimentoMatricula, connection, true);
                                if (movParcelaVO == null) {
                                    movParcelaVO = consultarParcelaMensalidadeMaisAntiga(cupomDescontoVO, dataBaseVencimentoMatricula, connection, false);
                                }
                                if (movParcelaVO == null) {
                                    cupomDescontoVO.setObservacaoProcessamento("O cliente não tem nenhuma parcela em aberta com vencimento maior ou igual a " + Uteis.getData(dataBaseVencimentoMatricula));
                                    gravarObservacaoProcessamentoOAMD(cupomDescontoVO);
                                    continue;
                                }
                                Double valorDesconto = consultarValorMensalidadeAluno(cupomDescontoVO.getCodigoAluno(), connection);
                                if (valorDesconto == null) {
                                    cupomDescontoVO.setObservacaoProcessamento("Nâo foi possível encontrar o valor do plano mensal do cliente");
                                    gravarObservacaoProcessamentoOAMD(cupomDescontoVO);
                                    continue;
                                }
                                valorDesconto = Uteis.arredondarForcando2CasasDecimais(valorDesconto);

                                movParcelaVO = movParcelaDao.consultarPorChavePrimaria(movParcelaVO.getCodigo(), negocio.comuns.utilitarias.Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                                movParcelaVO.setMovProdutoParcelaVOs(movProdutoParcelaDao.consultarPorCodigoMovParcela(movParcelaVO.getCodigo(), negocio.comuns.utilitarias.Uteis.NIVELMONTARDADOS_DADOSENTIDADESPRINCIPAIS));

                                movParcelaVO.setParcelaEscolhida(true);
                                if (valorDesconto > Uteis.arredondarForcando2CasasDecimais(movParcelaVO.getValorParcela())) {
                                    valorDesconto = Uteis.arredondarForcando2CasasDecimais(movParcelaVO.getValorParcela());
                                }

                                List<MovParcelaVO> parcelasRenegociar = new ArrayList<>();
                                parcelasRenegociar.add(movParcelaVO);

                                // parcela desconto
                                MovParcelaVO parcelaRenegociar = new MovParcelaVO();
                                parcelaRenegociar.setDescricao("DESCONTOS");
                                parcelaRenegociar.setValorParcela(valorDesconto);
                                parcelaRenegociar.setDataVencimento(Calendario.hoje());
                                parcelasRenegociar.add(parcelaRenegociar);

                                MovParcelaVO parcelaDesconto = new MovParcelaVO();
                                parcelaDesconto.setDescricao("");
                                parcelaDesconto.setValorParcela(valorDesconto);
                                parcelaDesconto.setDataVencimento(Calendario.hoje());

                                // Parcelas Renegociadas
                                List<MovParcelaVO> parcelasRenegociadas = new ArrayList<>();
                                MovParcelaVO novaParcela = (MovParcelaVO) movParcelaVO.getClone(true);
                                novaParcela.setDescricao("PARCELA RENEGOCIADA");
                                novaParcela.setValorParcela(Uteis.arredondarForcando2CasasDecimais(movParcelaVO.getValorParcela() - valorDesconto));
                                novaParcela.setDataRegistro(Calendario.hoje());
                                parcelasRenegociadas.add(novaParcela);
                                parcelaDesconto.setDescricao("");

                                movParcelaDao.renegociarParcelas(parcelasRenegociar, parcelasRenegociadas, parcelaDesconto, new MovParcelaVO(), "DE", false, null, null, 0.0, false, usuarioVO, true, true, true, null, null);

                                MovParcelaCupomDescontoVO movParcelaCupomDescontoVO = new MovParcelaCupomDescontoVO();
                                movParcelaCupomDescontoVO.setMovParcelaVO(parcelasRenegociadas.get(0));
                                movParcelaCupomDescontoVO.setCupomDesconto(cupomDescontoVO.getNumeroCupom());
                                String usuario = usuarioVO.getUsername();
                                String usuarioOAMD = usuarioVO.getUserOamd();
                                usuario = ((usuarioOAMD != null) && (!usuarioOAMD.trim().equals(""))) ? usuario + " - UserOAMD:" + usuarioOAMD : usuario;
                                movParcelaCupomDescontoVO.setResponsavelDesconto(usuario);
                                movParcelaCupomDescontoVO.setValorDesconto(valorDesconto);
                                movParcelaCupomDescontoVO.setDescontoContratoNovo(false);
                                movParcelaCupomDescontoDao.incluir(movParcelaCupomDescontoVO);

                                // gravar o premio do aluno
                                cupomDescontoVO.setObservacaoProcessamento("ISENTOU MENSALIDADE MÊS " + sdf.format(movParcelaVO.getDataVencimento()));
                                cupomDescontoVO.setDataPremioAluno(dataProcessamento);
                                cupomDescontoVO.setValorPremioAluno(valorDesconto);
                                gravarPremioAlunoOAMD(cupomDescontoVO);
                                totalCuponsUtilizadosParaIsencao++;
                                totalIsencaoMensalide = Uteis.arredondarForcando2CasasDecimais(totalIsencaoMensalide + valorDesconto);

                                preencherNumeroCupomMovProduto(movParcelaVO.getMovProdutoParcelaVOs(), cupomDescontoVO.getNumeroCupom(), connection);
                                connection.commit();

                            } catch (Exception e) {
                                cupomDescontoVO.setObservacaoProcessamento("Erro ao processar cupom: " + cupomDescontoVO.getNumeroCupom() + " Erro: " + e.getMessage());
                                gravarObservacaoProcessamentoOAMD(cupomDescontoVO);
                                connection.rollback();
                            } finally {
                                connection.setAutoCommit(true);
                            }

                        } else {
                            cupomDescontoVO.setObservacaoProcessamento("O cliente tem contrato ativo em mais de uma unidade. É necessário conceder a isenção da mensalidade de forma manual.");
                            gravarObservacaoProcessamentoOAMD(cupomDescontoVO);
                        }
                    } else {
                        cupomDescontoVO.setObservacaoProcessamento("Não foi possível isentar mensalidade, pois o cupom não foi utilizado para compra de um novo contrato. ");
                        gravarObservacaoProcessamentoOAMD(cupomDescontoVO);

                    }


                }

            }
        } finally {
            movParcelaDao = null;
            movParcelaCupomDescontoDao = null;
            movProdutoParcelaDao = null;
            Set<Map.Entry<String, Connection>> set = mapaConexaoRede.entrySet();
            for (Map.Entry<String, Connection> ent : set) {
                Connection connection = ent.getValue();
                connection.close();
                connection = null;
            }

        }
        if (totalCuponsUtilizadosParaIsencao > 0) {
            if (!resultado.toString().equals("")) {
                resultado.append(" </br> ");
            }
            resultado.append(" Total cupons que isentaram mensalidades: ").append(totalCuponsUtilizadosParaIsencao);
            resultado.append(" </br> Valor Total isenções de mensalidades: ").append(Formatador.formatarValorMonetario(totalIsencaoMensalide));
        } else {
            if (!resultado.toString().equals("")) {
                resultado.append(" </br> ");
            }
            resultado.append(" Não foi isentado nenhuma mensalidade no processamento.");
        }
        oamdService.registrarLogProcessamentoCupomDescontoManualmente(usuarioVO, campanhaCupomDescontoVO.getId(), dataBaseVencimentoMatricula, resultado.toString());
        System.out.println(Calendario.hoje() + " - FIM PROCESSO CONCEDER PREMIO ALUNO");
        return resultado.toString();
    }

    private String identificarAlunoCupom(RedeEmpresa redeEmpresa, CampanhaCupomDescontoVO campanhaCupomDescontoVO, CupomDescontoVO cupomDescontoVO, Date dataProcessamento, Map<String, Connection> mapaConexaoRede, Integer codigoFavorecido) throws Exception {
        try {
            CupomDescontoVO cupomBD = consultarPorNumeroCupomOAMD(cupomDescontoVO.getNumeroCupom(), codigoFavorecido);
            if (cupomBD == null) {
                return "Cupom " + cupomDescontoVO.getNumeroCupom() + " não existe no banco de dados. ";
            }
            if (!cupomBD.getCampanhaCupomDescontoVO().getId().equals(campanhaCupomDescontoVO.getId())) {
                return "Cupom " + cupomDescontoVO.getNumeroCupom() + " não pertence a campanha '" + campanhaCupomDescontoVO.getDescricaoCampanha() + "'.";
            }
            cupomDescontoVO.setCampanhaCupomDescontoVO(campanhaCupomDescontoVO);
            cupomDescontoVO.setCodigoAluno(cupomBD.getCodigoAluno());
            cupomDescontoVO.setDataPremioAluno(cupomBD.getDataPremioAluno());
            cupomDescontoVO.setDataPremioPortadorCupom(cupomBD.getDataPremioPortadorCupom());
            cupomDescontoVO.setNomePortadorCupom(cupomBD.getNomePortadorCupom());
            cupomDescontoVO.setObservacaoProcessamento(cupomBD.getObservacaoProcessamento());
            cupomDescontoVO.setValorPremioAluno(cupomBD.getValorPremioAluno());
            cupomDescontoVO.setValorPremioPortadorCupom(cupomBD.getValorPremioPortadorCupom());
            cupomDescontoVO.setLote(cupomBD.getLote());

            cupomDescontoVO.setListaPessoaTO(new ArrayList<>());
            CupomDescontoVO cupomSituacaoDiferenteAtivo = null;
            Set<Map.Entry<String, Connection>> set = mapaConexaoRede.entrySet();
            for (Map.Entry<String, Connection> ent : set) {
                String chave = ent.getKey();
                Connection connection = ent.getValue();
                PessoaTO pessoaTO = consultarAlunoPorCPF(connection, cupomDescontoVO.getCpfAluno());
                if (pessoaTO != null) {
                    String nomeEmpresa = redeEmpresa.consultarNomeEmpresaDaRedeOAMDPrincipal(chave);
                    if (pessoaTO.getSituacaoCliente().equals("AT")) {
                        pessoaTO.setChaveZW(chave);
                        pessoaTO.setNomeEmpresa(nomeEmpresa);
                        cupomDescontoVO.getListaPessoaTO().add(pessoaTO);
                        gravarIdentificacaoAlunoCupom(chave, cupomDescontoVO, pessoaTO);
                        cupomDescontoVO.setNomeAluno(pessoaTO.getNomeCompleto());
                        cupomDescontoVO.setNomeEmpresaAluno(nomeEmpresa);
                        cupomDescontoVO.setConnection(connection);
                    } else {
                        cupomSituacaoDiferenteAtivo = new CupomDescontoVO();
                        cupomSituacaoDiferenteAtivo.setNomeAluno(pessoaTO.getNomeCompleto());
                        cupomSituacaoDiferenteAtivo.setNomeEmpresaAluno(nomeEmpresa);
                        cupomSituacaoDiferenteAtivo.setChaveAluno(chave);
                        cupomSituacaoDiferenteAtivo.setObservacaoProcessamento("Não foi possível isentar mensalidade, pois o aluno não está com a situação ATIVO.");
                    }
                }
            }
            if (((cupomDescontoVO.getListaPessoaTO() == null) || (cupomDescontoVO.getListaPessoaTO().size() == 0)) && (cupomSituacaoDiferenteAtivo != null)) {
                gravarObservacaoProcessamentoOAMD(cupomSituacaoDiferenteAtivo);
            } else if ((cupomDescontoVO.getNomeAluno() == null) || (cupomDescontoVO.getNomeAluno().trim().equals(""))) {
                cupomDescontoVO.setDataProcessamento(dataProcessamento);
                cupomDescontoVO.setObservacaoProcessamento("CPF não encontrado em nenhuma unidade da rede.");
                gravarObservacaoProcessamentoOAMD(cupomDescontoVO);
            }


        } finally {
            redeEmpresa = null;
        }
        return null;
    }

    private Double consultarValorMensalidadeAluno(Integer codigoCliente, Connection conexao) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("select mov.* \n");
        sql.append("from movProduto mov \n");
        sql.append("inner join SituacaoClienteSinteticoDW dw on dw.codigoPessoa = mov.pessoa and mov.contrato = dw.codigoContrato \n");
        sql.append("inner join produto prod on prod.codigo = mov.produto \n");
        sql.append("where dw.codigoCliente = ").append(codigoCliente);
        sql.append("   and prod.tipoProduto = 'PM' \n");
        sql.append("   and mov.descricao not like '%RATA%' \n");
        sql.append("   and mov.descricao not like '%MATR%' \n");
        sql.append("   and mov.descricao not like '%ADES%' \n");
        sql.append("   and mov.descricao not like '%ANUIDADE%' \n");
        sql.append("   and mov.totalFinal > 0  \n");
        sql.append("limit 1    \n");
        Statement st = conexao.createStatement();
        ResultSet rs = st.executeQuery(sql.toString());
        if (rs.next()) {
            return rs.getDouble("totalFinal");
        }
        return null;
    }

    private Date consultarDebitoEmAbertoMaisAntigo(CupomDescontoVO cupomDescontoVO, Date dataBaseVencimento, Connection conexao) throws Exception {
        StringBuilder sql = new StringBuilder();
        Date dataBaseMenosUmDia = Uteis.somarDias(dataBaseVencimento, -1);
        Date dataBaseMenosTresDias = Uteis.somarDias(dataBaseVencimento, -3);
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        sql.append("select parc.* \n");
        sql.append("from movParcela parc \n");
        sql.append("inner join cliente cli on cli.pessoa = parc.pessoa \n");
        sql.append("where (parc.situacao = 'EA') and  (parc.dataVencimento <= '").append(dataBaseMenosUmDia).append("') \n");
        sql.append(" and not exists (select ri.codigo from remessaitem ri inner join remessa re on ri.remessa = re.codigo where ri.movParcela = parc.codigo and  re.situacaoremessa in (1,4)) \n");
        sql.append("and cli.codigo = ").append(cupomDescontoVO.getCodigoAluno()).append(" \n");
        sql.append(" order by parc.dataVencimento ");
        Statement st = conexao.createStatement();
        ResultSet rs = st.executeQuery(sql.toString());
        if (rs.next()) {
            return rs.getDate("dataVencimento");
        }
        return null;
    }

    private MovParcelaVO consultarParcelaMensalidadeMaisAntiga(CupomDescontoVO cupomDescontoVO, Date dataBaseVencimento, Connection conexao, boolean ignorarParcelasComCupom) throws Exception {
        StringBuilder sql = new StringBuilder();
        Date dataComparar = Uteis.somarDias(dataBaseVencimento, -3);
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        sql.append("select movProdParc.*, mov.precoUnitario as valorMensalidade  \n");
        sql.append("from movProduto mov \n");
        sql.append("inner join movProdutoParcela movProdParc on movProdParc.movProduto = mov.codigo \n");
        sql.append("inner join movParcela parc on parc.codigo = movProdParc.movParcela \n");
        sql.append("inner join produto prod on prod.codigo = mov.produto \n");
        sql.append("inner join SituacaoClienteSinteticoDW dw on dw.codigoPessoa = mov.pessoa and dw.codigoContrato = mov.contrato \n");
        sql.append("where \n");
        sql.append(" mov.totalFinal > 0 \n");
        sql.append("  and prod.tipoProduto = 'PM'  \n");
        sql.append("   and parc.situacao = 'EA' \n");
        if (ignorarParcelasComCupom) {
            sql.append("   and parc.descricao not like '%(SELFFRIEND)%' \n");
        }
        // O débito que estiver em remessa e estiver no prazo de três dias, então isentar o débito. Observação: O retorno da remessa acontece no prazo máximo de três dias.
        sql.append(" and not exists (select ri.codigo from remessaitem ri inner join remessa re on ri.remessa = re.codigo where ri.movParcela = parc.codigo and  re.situacaoremessa in (1,4)) \n");
        sql.append("   and dw.codigoCliente = ").append(cupomDescontoVO.getCodigoAluno()).append(" \n");
        sql.append(" and dataVencimento >= '").append(sdf.format(dataBaseVencimento)).append("' \n");
        sql.append(" order by dataVencimento limit 1  \n");
        Statement st = conexao.createStatement();
        ResultSet rs = st.executeQuery(sql.toString());
        if (rs.next()) {
            MovParcelaVO movParcelaVO = new MovParcelaVO();
            movParcelaVO.setCodigo(rs.getInt("movParcela"));
            movParcelaVO.setValorBaseCalculo(rs.getDouble("valorMensalidade"));
            return movParcelaVO;
        }
        return null;
    }

    private void gravarIdentificacaoAlunoCupom(String chave, CupomDescontoVO cupomDescontoVO, PessoaTO pessoaTO) throws Exception {
        cupomDescontoVO.setCpfAluno(pessoaTO.getCpf());
        cupomDescontoVO.setCodigoAluno(pessoaTO.getCodigoCliente());
        cupomDescontoVO.setNomeAluno(pessoaTO.getNomeCompleto());
        cupomDescontoVO.setChaveAluno(chave);
        alterarCupom(cupomDescontoVO);
    }

    private void alterarCupom(CupomDescontoVO cupomDescontoVO) throws Exception {
        ExecuteRequestHttpService executeRequestHttpService = new ExecuteRequestHttpService();
        String url = PropsService.getPropertyValue(PropsService.urlOamd) + "/prest/cupomDesconto/alterarOAMD";
        try {
            String response = executeRequestHttpService.executeRequestCupomDesconto(url, cupomDescontoVO.toJSON(), new HashMap<>());
            new JSONObject(response).getString("string").contains("sucesso");
        } catch (Exception e) {
            throw new Exception("Falha ao alterar cupom, tente novamente!");
        }
    }

    private void adicionarHistoricoUtilizacaoCupom(CupomDescontoVO cupomDescontoVO, int codContrato) throws Exception {
        ExecuteRequestHttpService executeRequestHttpService = new ExecuteRequestHttpService();
        String url = PropsService.getPropertyValue(PropsService.urlOamd) + "/prest/historicoUtilizacaoCupomDesconto/incluirOAMD";
        HashMap<String, String> headers = new HashMap<>();
        headers.put("contrato", String.valueOf(codContrato));
        try {
            String response = executeRequestHttpService.executeRequestCupomDesconto(url, cupomDescontoVO.toJSON(), headers);
            new JSONObject(response).getString("string").contains("sucesso");
        } catch (Exception e) {
            throw new Exception("Falha ao adicionar historico utilização cupom, tente novamente!");
        }
    }

    public void informarContratoEstornadoHistoricoUtilizacaoCupom(int codContrato, String chaveZW) throws Exception {
        ExecuteRequestHttpService executeRequestHttpService = new ExecuteRequestHttpService();
        String url = PropsService.getPropertyValue(PropsService.urlOamd) + "/prest/historicoUtilizacaoCupomDesconto/informarEstornoOAMD";
        HashMap<String, String> headers = new HashMap<>();
        headers.put("contrato", String.valueOf(codContrato));
        headers.put("chaveZW", chaveZW);
        try {
            String response = executeRequestHttpService.executeRequestCupomDesconto(url, "", headers);
            new JSONObject(response).getString("string").contains("sucesso");
        } catch (Exception e) {
            throw new Exception("Falha ao adicionar historico utilização cupom, tente novamente!");
        }
    }

    public PessoaTO consultarAlunoPorCPF(Connection connection, String cpf) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("select cli.codigo as codigoCliente, p.cfp as cpf, p.nome, dw.situacao  \n");
        sql.append("from pessoa p \n");
        sql.append("inner join cliente cli on cli.pessoa = p.codigo \n");
        sql.append("inner join SituacaoClienteSinteticoDW dw on dw.codigoPessoa = p.codigo \n");
        sql.append("where p.cfp = '").append(cpf).append("'");
        Statement st = connection.createStatement();
        ResultSet rs = st.executeQuery(sql.toString());
        if (rs.next()) {
            PessoaTO pessoaTO = new PessoaTO();
            pessoaTO.setSituacaoCliente(rs.getString("situacao"));
            pessoaTO.setCodigoCliente(rs.getInt("codigoCliente"));
            pessoaTO.setNomeCompleto(rs.getString("nome"));
            pessoaTO.setCpf(rs.getString("cpf"));
            return pessoaTO;
        }
        return null;
    }

    public Integer consultarTotalCupomDescontoJaUtilizadoOAMD(Integer idCampanhaCupomDesconto) throws Exception {
        ExecuteRequestHttpService executeRequestHttpService = new ExecuteRequestHttpService();
        String url = PropsService.getPropertyValue(PropsService.urlOamd) + "/prest/cupomDesconto/consultarTotalCupomDescontoJaUtilizadoOAMD";
        Map<String, String> headers = new HashMap<>();
        headers.put("idCampanhaCupomDesconto", String.valueOf(idCampanhaCupomDesconto));
        try {
            String response = executeRequestHttpService.executeRequestCupomDesconto(url, "", headers);
            return Integer.parseInt(new JSONObject(response).get("sucesso").toString());
        } catch (Exception e) {
            throw new Exception("Falha ao consultar total de cupons, tente novamente!");
        }
    }
}
