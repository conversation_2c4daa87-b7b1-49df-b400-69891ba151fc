/*
 * To change this template, choose Too<PERSON> | Templates
 * and open the template in the editor.
 */
package negocio.facade.jdbc.contrato;

import br.com.pactosolucoes.autorizacaocobranca.modelo.TipoAutorizacaoCobrancaEnum;
import br.com.pactosolucoes.comuns.util.Declaracao;
import br.com.pactosolucoes.estrutura.paginacao.ConfPaginacao;
import negocio.comuns.arquitetura.LogVO;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.enumerador.TiposMensagensEnum;
import negocio.comuns.contrato.ContratoRecorrenciaVO;
import negocio.comuns.contrato.ContratoVO;
import negocio.comuns.contrato.MovProdutoVO;
import negocio.comuns.financeiro.ConvenioCobrancaVO;
import negocio.comuns.financeiro.MovParcelaVO;
import negocio.comuns.financeiro.MovProdutoParcelaVO;
import negocio.comuns.plano.PlanoEmpresaVO;
import negocio.comuns.plano.PlanoRecorrenciaVO;
import negocio.comuns.plano.PlanoVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.Log;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.facade.jdbc.basico.Pessoa;
import negocio.facade.jdbc.financeiro.MovParcela;
import negocio.facade.jdbc.financeiro.RemessaItem;
import negocio.facade.jdbc.plano.Produto;
import negocio.interfaces.contrato.ContratoRecorrenciaInterfaceFacade;
import relatorio.controle.basico.RecorrenciaClienteTO;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

/**
 *
 * author: waller
 */
public class ContratoRecorrencia extends SuperEntidade implements ContratoRecorrenciaInterfaceFacade {

    public static final String sqlCancelados = "SELECT %s FROM contrato c "
            + "INNER JOIN contratooperacao co ON c.codigo = co.contrato AND co.tipooperacao = 'CA' "
            + "inner join usuario usu on usu.codigo = co.responsavel "
            + "INNER JOIN pessoa p ON c.pessoa = p.codigo\n"
            + "INNER JOIN cliente cli ON p.codigo = cli.pessoa\n"
            + "INNER JOIN contratoduracao cd ON c.codigo = cd.contrato\n"
            + "  LEFT JOIN contratocondicaopagamento ccp on ccp.contrato = c.codigo\n "
            + "  LEFT JOIN condicaopagamento cp on cp.codigo = ccp.condicaopagamento \n"
            + "WHERE TRUE %s AND usu.username = 'RECOR' "
            + "AND co.dataoperacao >= '%s' and co.dataoperacao <= '%s' ";
    //J. Alcides: selecionar os contratos em regime de recorrencia
    public static final String sqlNaoRenovados = "SELECT %s FROM contrato c "
            + "LEFT JOIN contratorecorrencia cr ON c.codigo = cr.contrato "
            //Glauco: Inners para consultas específicas
            + "  INNER JOIN pessoa p ON c.pessoa = p.codigo\n"
            + "  INNER JOIN cliente cli ON p.codigo = cli.pessoa\n"
            + "  INNER JOIN contratoduracao cd ON c.codigo = cd.contrato "
            // que não tiveram renovacao
            + "WHERE c.codigo NOT IN (SELECT DISTINCT(contratobaseadorenovacao) FROM contrato) "
            //que deveriam ser renovados automaticamente
            + "AND (cr.renovavelautomaticamente IS TRUE OR c.renovavelautomaticamente IS TRUE)\n"
            // no periodo de ... até ...
            + "AND c.dataprevistarenovar BETWEEN '%s' AND '%s' "
            // e que não possuem operação de cancelamento associada
            + "AND c.codigo NOT IN (SELECT contrato FROM contratooperacao WHERE tipooperacao LIKE 'CA') "
            // e que não estão trancados
            + "AND c.situacao NOT LIKE 'TR' "
            + "AND true %s";
    public static final String sqlAtivos = "select %s from contrato c "
            + "  INNER JOIN pessoa p ON c.pessoa = p.codigo\n"
            + "  INNER JOIN cliente cli ON p.codigo = cli.pessoa\n"
            + "  LEFT JOIN autorizacaocobrancacliente aa on aa.cliente = cli.codigo and aa.ativa \n"
            + "  INNER JOIN contratoduracao cd ON c.codigo = cd.contrato "
            + "  LEFT JOIN contratocondicaopagamento ccp on ccp.contrato = c.codigo "
            + "  LEFT JOIN condicaopagamento cp on cp.codigo = ccp.condicaopagamento "
            + "where TRUE %s and '%s' between c.vigenciaDe and c.vigenciaAteAjustada "
            + "and not exists (select h.contrato from historicocontrato h "
            + "where h.contrato = c.codigo and h.tipohistorico in ('CA','DE','VE') "
            + "and '%s' between h.datainiciosituacao and h.datafinalsituacao) "
            + "and c.codigo not in (select o.contrato from contratooperacao  o "
            + "where o.tipooperacao in ('TR','TV') and '%s' >= o.datainicioefetivacaooperacao and '%s' <= o.datafimefetivacaooperacao) ";

    public static final String sqlAtivosComMovParcela = "select %s from contrato c "
            + "  INNER JOIN pessoa p ON c.pessoa = p.codigo\n"
            + "  INNER JOIN cliente cli ON p.codigo = cli.pessoa\n"
            + "  LEFT JOIN autorizacaocobrancacliente aa on aa.cliente = cli.codigo and aa.ativa \n"
            + "  INNER JOIN contratoduracao cd ON c.codigo = cd.contrato "
            + "  LEFT JOIN contratocondicaopagamento ccp on ccp.contrato = c.codigo "
            + "  LEFT JOIN condicaopagamento cp on cp.codigo = ccp.condicaopagamento "
            + "  LEFT JOIN movparcela movp on movp.contrato = c.codigo  "
            + "where TRUE %s and '%s' between c.vigenciaDe and c.vigenciaAteAjustada "
            + "and not exists (select h.contrato from historicocontrato h "
            + "where h.contrato = c.codigo and h.tipohistorico in ('CA','DE','VE') "
            + "and '%s' between h.datainiciosituacao and h.datafinalsituacao) "
            + "and c.codigo not in (select o.contrato from contratooperacao  o "
            + "where o.tipooperacao in ('TR','TV') and '%s' >= o.datainicioefetivacaooperacao and '%s' <= o.datafimefetivacaooperacao) ";

    public static final String sqlAtivosParcela = "select %s from contrato c \n" +
            "inner join movparcela m2 on\n" +
            "\tm2.contrato = c.codigo\n" +
            "inner join pessoa p on\n" +
            "\tp.codigo = c.pessoa\n" +
            "inner join cliente cli on\n" +
            "\tp.codigo = cli.pessoa\n" +
            "inner join movprodutoparcela mvp on\n" +
            "\tmvp.movparcela = m2.codigo\n" +
            "inner join autorizacaocobrancacliente acb on cli.codigo = acb.cliente AND acb.ativa\n" +
            "inner join conveniocobranca cvc on\n" +
            "\tacb.conveniocobranca = cvc.codigo\n" +
            "inner join movpagamento mpg on\n" +
            "\tmvp.recibopagamento = mpg.recibopagamento\n" +
            "inner join formapagamento fpg on\n" +
            "\tmpg.formapagamento = fpg.codigo\n" +
            "inner join empresa emp on \n" +
            "\tcli.empresa = emp.codigo " +
            "where c.empresa= %s \n" +
            "and m2.datavencimento::date <= '%s' \n" +
            "and m2.datavencimento::date >= '%s' \n" +
            "and m2.situacao = 'PG' \n" +
            "and mvp.valorpago > 0 \n" +
            "and (c.regimerecorrencia or acb.codigo is not null)\n" ;

    public ContratoRecorrencia() throws Exception {
        super();
        setIdEntidade("Contrato");
    }

    public ContratoRecorrencia(Connection conexao) throws Exception {
        super(conexao);
        setIdEntidade("Contrato");
    }

    @Override
    public void incluir(ContratoRecorrenciaVO obj) throws Exception {
        ContratoRecorrenciaVO.validarDados(obj);
        incluir(getIdEntidade());
        /*
         *  private ContratoVO contrato = null;
         private PessoaVO pessoa = null;
         private Integer fidelidade = 0;
         private String numerocartao = "";//apenas os quatro primeiros e os quatro últimos digitos, demais preencher com '*'
         private Integer diavencimentocartao = 0;
         private Integer ultimatransacaoaprovada = 0;
         private Double valormensal = 0.0;
         private Integer diasbloqueioacesso = 0;
         private Integer diascancelamentoautomatico = 0;
         private Boolean renovavelautomaticamente = false;
         private Double valoranuidade = 0.0;
         private Integer diavencimentoanuidade = 0;
         private Integer mesvencimentoanuidade = 0;
         */
        String sql = "INSERT INTO ContratoRecorrencia(contrato, pessoa, "
                + "fidelidade, numerocartao, diavencimentocartao,"
                + "ultimatransacaoaprovada, valormensal, diasbloqueioacesso, "
                + "diascancelamentoautomatico, renovavelautomaticamente, valoranuidade, "
                + "diavencimentoanuidade, mesvencimentoanuidade, dataInutilizada, anuidadeNaParcela, "
                + "parcelaAnuidade, cancelamentoProporcional, qtdDiasCobrarProximaParcela, qtdDiasCobrarAnuidadeTotal, parcelarAnuidade, "
                + "valorMensalNegociado) "
                + "VALUES (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)";

        try (PreparedStatement sqlInserir = con.prepareStatement(sql)) {

            sqlInserir.setInt(1, obj.getContrato().getCodigo());
            sqlInserir.setInt(2, obj.getPessoa().getCodigo());
            sqlInserir.setInt(3, obj.getFidelidade());
            sqlInserir.setString(4, obj.getNumeroCartao());
            sqlInserir.setInt(5, obj.getDiaVencimentoCartao());
            sqlInserir.setString(6, obj.getUltimaTransacaoAprovada());
            sqlInserir.setDouble(7, Uteis.arredondarForcando2CasasDecimais(obj.getValorMensal()));
            sqlInserir.setInt(8, obj.getDiasBloqueioAcesso());
            sqlInserir.setInt(9, obj.getDiasCancelamentoAutomatico());
            sqlInserir.setBoolean(10, obj.getRenovavelAutomaticamente());
            sqlInserir.setDouble(11, Uteis.arredondarForcando2CasasDecimais(obj.getValorAnuidade()));
            sqlInserir.setInt(12, obj.getDiaVencimentoAnuidade());
            sqlInserir.setInt(13, obj.getMesVencimentoAnuidade());
            sqlInserir.setTimestamp(14, Uteis.getDataJDBCTimestamp(obj.getDataInutilizada()));
            sqlInserir.setBoolean(15, obj.isAnuidadeNaParcela());
            sqlInserir.setInt(16, obj.getParcelaAnuidade());
            sqlInserir.setBoolean(17, obj.isCancelamentoProporcional());
            sqlInserir.setInt(18, obj.getQtdDiasCobrarProximaParcela());
            sqlInserir.setInt(19, obj.getQtdDiasCobrarAnuidadeTotal());
            sqlInserir.setBoolean(20, obj.isParcelarAnuidade());
            sqlInserir.setDouble(21, obj.getValorMensal());
            sqlInserir.execute();
        }
        obj.getContrato().setContratoRecorrenciaVO(obj);
        obj.setCodigo(obterValorChavePrimariaCodigo());
        obj.setNovoObj(false);
    }

    @Override
    public void alterar(ContratoRecorrenciaVO obj) throws Exception {
        ContratoRecorrenciaVO.validarDados(obj);
        alterar(getIdEntidade());
        String sql = "UPDATE ContratoRecorrencia "
                + "set contrato=?,pessoa=?,fidelidade=?, numerocartao=?, diavencimentocartao=?,"
                + "ultimatransacaoaprovada=?, valormensal=?, diasbloqueioacesso=?, "
                + "diascancelamentoautomatico=?, renovavelautomaticamente=?, valoranuidade=?, "
                + "diavencimentoanuidade=?, mesvencimentoanuidade=?, dataInutilizada=?, anuidadeNaParcela = ?, "
                + "parcelaAnuidade = ?, cancelamentoProporcional =?, qtdDiasCobrarProximaParcela = ?, qtdDiasCobrarAnuidadeTotal = ?, parcelarAnuidade = ? "
                + "WHERE ((codigo = ?))";

        try (PreparedStatement sqlAlterar = con.prepareStatement(sql)) {
            sqlAlterar.setInt(1, obj.getContrato().getCodigo());
            sqlAlterar.setInt(2, obj.getPessoa().getCodigo());
            sqlAlterar.setInt(3, obj.getFidelidade());
            sqlAlterar.setString(4, obj.getNumeroCartao());
            sqlAlterar.setInt(5, obj.getDiaVencimentoCartao());
            sqlAlterar.setString(6, obj.getUltimaTransacaoAprovada());
            sqlAlterar.setDouble(7, obj.getValorMensal());
            sqlAlterar.setInt(8, obj.getDiasBloqueioAcesso());
            sqlAlterar.setInt(9, obj.getDiasCancelamentoAutomatico());
            sqlAlterar.setBoolean(10, obj.getRenovavelAutomaticamente());
            sqlAlterar.setDouble(11, obj.getValorAnuidade());
            sqlAlterar.setInt(12, obj.getDiaVencimentoAnuidade());
            sqlAlterar.setInt(13, obj.getMesVencimentoAnuidade());
            sqlAlterar.setTimestamp(14, Uteis.getDataJDBCTimestamp(obj.getDataInutilizada()));
            sqlAlterar.setBoolean(15, obj.isAnuidadeNaParcela());
            sqlAlterar.setInt(16, obj.getParcelaAnuidade());
            sqlAlterar.setBoolean(17, obj.isCancelamentoProporcional());
            sqlAlterar.setInt(18, obj.getQtdDiasCobrarProximaParcela());
            sqlAlterar.setInt(19, obj.getQtdDiasCobrarAnuidadeTotal());
            sqlAlterar.setBoolean(20, obj.isParcelarAnuidade());
            sqlAlterar.setInt(21, obj.getCodigo());

            sqlAlterar.execute();
        }
    }

    /**
     * Operação responsável por excluir no BD um objeto da classe
     * <code>ContratoComposicaoVO</code>. Sempre localiza o registro a ser
     * excluído através da chave primária da entidade. Primeiramente verifica a
     * conexão com o banco de dados e a permissão do usuário para realizar esta
     * operacão na entidade. Isto, através da operação
     * <code>excluir</code> da superclasse.
     *
     * @param obj Objeto da classe <code>ContratoComposicaoVO</code> que será
     * removido no banco de dados.
     * @exception Exception Caso haja problemas de conexão ou restrição de
     * acesso.
     */
    @Override
    public void excluir(ContratoRecorrenciaVO obj) throws Exception {
        excluir(getIdEntidade());
        String sql = "DELETE FROM ContratoRecorrencia WHERE ((codigo = ?))";
        try (PreparedStatement sqlExcluir = con.prepareStatement(sql)) {
            sqlExcluir.setInt(1, obj.getCodigo().intValue());
            sqlExcluir.execute();
        }
    }

    private static List montarDadosConsulta(Connection con,
            ResultSet tabelaResultado,
            int nivelMontarDados) throws Exception {
        List vetResultado = new ArrayList();
        while (tabelaResultado.next()) {
            ContratoRecorrenciaVO obj = new ContratoRecorrenciaVO();
            obj = montarDados(con, tabelaResultado, nivelMontarDados);
            vetResultado.add(obj);
        }
        return vetResultado;
    }

    private static ContratoRecorrenciaVO montarDados(Connection con,
            ResultSet ds, int nivelMontarDados) throws Exception {
        /*
         * contrato, pessoa, "
         + "fidelidade, numerocartao, diavencimentocartao,"
         + "ultimatransacaoaprovada, valormensal, diasbloqueioacesso, "
         + "diascancelamentoautomatico, renovavelautomaticamente, valoranuidade, "
         + "diavencimentoanuidade, mesvencimentoanuidade
         */
        ContratoRecorrenciaVO obj = new ContratoRecorrenciaVO();
        obj.setCodigo(ds.getInt("codigo"));
        obj.setFidelidade(ds.getInt("fidelidade"));
        obj.setNumeroCartao(ds.getString("numerocartao"));
        obj.setDiaVencimentoCartao(ds.getInt("diavencimentocartao"));
        obj.setUltimaTransacaoAprovada(ds.getString("ultimatransacaoaprovada"));
        obj.setValorMensal(ds.getDouble("valormensal"));
        obj.setDiasBloqueioAcesso(ds.getInt("diasbloqueioacesso"));
        obj.setDiasCancelamentoAutomatico(ds.getInt("diascancelamentoautomatico"));
        obj.setRenovavelAutomaticamente(ds.getBoolean("renovavelautomaticamente"));
        obj.setValorAnuidade(ds.getDouble("valoranuidade"));
        obj.setDiaVencimentoAnuidade(ds.getInt("diavencimentoanuidade"));
        obj.setMesVencimentoAnuidade(ds.getInt("mesvencimentoanuidade"));
        obj.setDataInutilizada(ds.getTimestamp("dataInutilizada"));
        obj.setAnuidadeNaParcela(ds.getBoolean("anuidadeNaParcela"));
        obj.setParcelaAnuidade(ds.getInt("parcelaAnuidade"));
        obj.setCancelamentoProporcional(ds.getBoolean("cancelamentoProporcional"));
        obj.setQtdDiasCobrarProximaParcela(ds.getInt("qtdDiasCobrarProximaParcela"));
        obj.setQtdDiasCobrarAnuidadeTotal(ds.getInt("qtdDiasCobrarAnuidadeTotal"));
        obj.setParcelarAnuidade(ds.getBoolean("parcelarAnuidade"));
        obj.setValorMensalNegociado(ds.getDouble("valorMensalNegociado"));

        adicionarDadosEntidadesRelacionadas(obj, ds, nivelMontarDados, con);

        return obj;
    }

    private static void adicionarDadosEntidadesRelacionadas(ContratoRecorrenciaVO obj,
            ResultSet dadosSQL, int nivelMontarDados, Connection con) throws Exception {

        if (dadosSQL.getInt("contrato") != 0) {
            Contrato contratoDAO = new Contrato(con);
            try {

                obj.setContrato(contratoDAO.consultarPorChavePrimaria(
                        dadosSQL.getInt("contrato"), nivelMontarDados));
            } finally {
                contratoDAO = null;
            }
        }

        if (dadosSQL.getInt("pessoa") != 0) {
            Pessoa pessoaDAO = new Pessoa(con);
            try {
                obj.setPessoa(pessoaDAO.consultarPorChavePrimaria(
                        dadosSQL.getInt("pessoa"), nivelMontarDados));
            } finally {
                pessoaDAO = null;
            }

        }

    }

    /**
     * Responsável por pesquisar lista de contratos em regime de recorrencia que
     * vencem no dia passado como parametro. author: alcides 10/08/2011
     */
    @SuppressWarnings("unchecked")
    public List<ContratoRecorrenciaVO> consultarContratosVencendo(Date dia,Integer carenciaRenovacao,
            Integer nrDiasRenovacaoAntecipadaAutomatica,
            Integer empresa,
            int nivelMontarDados, Connection con) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT CR.* FROM contratorecorrencia CR inner join contrato C on CR.contrato = C.codigo \n");
        sql.append("inner join plano on plano.codigo = c.plano \n");
        sql.append("inner join planorecorrencia on planorecorrencia.plano = plano.codigo \n");
        sql.append("WHERE  (C.dataprevistarenovar >= ? and C.dataprevistarenovar <= ?) and C.situacao in('IN','AT') \n");
        sql.append("AND ((planorecorrencia.renovavelautomaticamente OR CR.renovavelautomaticamente) AND C.permiterenovacaoautomatica) \n");
        sql.append("AND C.dataRenovarRealizada is null \n");
        sql.append("AND CR.datainutilizada is null \n");
        sql.append("AND C.EMPRESA = ? \n");
        sql.append("AND  not exists (select codigo from contratooperacao where contrato = c.codigo and tipooperacao = 'CA')  \n");
        sql.append("AND  not exists (select codigo from historicocontrato where contrato = c.codigo and tipohistorico = 'DE')  \n");
        sql.append("ORDER BY C.vigenciaAteAjustada \n");

        Declaracao dc = new Declaracao(sql.toString(), con);
        int i = 1;
        dc.setDate(i++, Uteis.getDataJDBC(Uteis.somarDias(dia, -carenciaRenovacao)));
        if (UteisValidacao.emptyNumber(nrDiasRenovacaoAntecipadaAutomatica)) {
            dc.setDate(i++, Uteis.getDataJDBC(dia));
        } else {
            dc.setDate(i++, Uteis.getDataJDBC(Uteis.somarDias(dia, nrDiasRenovacaoAntecipadaAutomatica)));
        }
        dc.setInt(i, empresa);
        try (ResultSet rs = dc.executeQuery()) {
            return montarDadosConsulta(con, rs, nivelMontarDados);
        }

    }

    public List<Integer> consultarIdContratosVencendoRecorrente(Date dia,Integer carenciaRenovacao,
                                                                  Integer nrDiasRenovacaoAntecipadaAutomatica,
                                                                  Integer empresa,
                                                                  Connection con) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT CR.contrato FROM contratorecorrencia CR inner join contrato C on CR.contrato = C.codigo \n");
        sql.append("inner join plano on plano.codigo = c.plano \n");
        sql.append("inner join planorecorrencia on planorecorrencia.plano = plano.codigo \n");
        sql.append("WHERE  (C.dataprevistarenovar >= ? and C.dataprevistarenovar <= ?) and C.situacao in('IN','AT') \n");
        sql.append("AND ((planorecorrencia.renovavelautomaticamente OR CR.renovavelautomaticamente) AND C.permiterenovacaoautomatica) \n");
        sql.append("AND C.dataRenovarRealizada is null \n");
        sql.append("AND CR.datainutilizada is null \n");
        sql.append("AND C.EMPRESA = ? \n");
        sql.append("AND  not exists (select codigo from contratooperacao where contrato = c.codigo and tipooperacao = 'CA')  \n");
        sql.append("AND  not exists (select codigo from historicocontrato where contrato = c.codigo and tipohistorico = 'DE')  \n");
        sql.append("ORDER BY C.vigenciaAteAjustada \n");

        Declaracao dc = new Declaracao(sql.toString(), con);
        int i = 1;
        dc.setDate(i++, Uteis.getDataJDBC(Uteis.somarDias(dia, -carenciaRenovacao)));
        if (UteisValidacao.emptyNumber(nrDiasRenovacaoAntecipadaAutomatica)) {
            dc.setDate(i++, Uteis.getDataJDBC(dia));
        } else {
            dc.setDate(i++, Uteis.getDataJDBC(Uteis.somarDias(dia, nrDiasRenovacaoAntecipadaAutomatica)));
        }
        dc.setInt(i, empresa);

        List<Integer> ListaIdContratos = new ArrayList<>();
        try (ResultSet rs = dc.executeQuery()) {
            while (rs.next()){
                ListaIdContratos.add(rs.getInt("contrato"));
            }
        }
        return ListaIdContratos;

    }

    @SuppressWarnings("unchecked")
    public List<ContratoRecorrenciaVO> consultarContratosPrevistosAnuidade(Date dia,
            Integer empresa, int nivelMontarDados, Connection con) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT distinct CR.*,C.vigenciaAteAjustada FROM contratorecorrencia CR  \n");
        sql.append("inner join contrato C on C.codigo = CR.contrato   \n");
        sql.append("inner join plano on plano.codigo = c.plano   \n");
        sql.append("inner join planorecorrencia on planorecorrencia.plano = plano.codigo   \n");
        sql.append("inner join movproduto mp on mp.contrato = C.codigo  \n");
        sql.append("inner join pessoa pes on pes.codigo = C.pessoa  \n");
        sql.append("left join produto p on p.codigo = mp.produto  \n");
        sql.append("left join (select mp2.pessoa, max(mp2.contrato) as contrato, sum(mp2.totalfinal) as total \n");
        sql.append("		from movproduto mp2  \n");
        sql.append("		inner join produto prod on prod.codigo = mp2.produto \n");
        sql.append("		inner join pessoa p on p.codigo = mp2.pessoa \n");
        sql.append("        inner join movprodutoparcela movpar ON movpar.movproduto = mp2.codigo \n");
        sql.append("        inner join movparcela par ON par.codigo = movpar.movparcela \n");
        sql.append("		where 1 = 1 \n");
        sql.append("		and prod.tipoproduto = 'TA' and mp2.totalfinal > 0.0 \n");
        sql.append("		and mp2.anoreferencia = ? \n");
        sql.append("        AND par.codigo = (SELECT max(movp.movparcela) FROM movprodutoparcela movp inner join movparcela m3 ON m3.codigo = movp.movparcela WHERE movp.movproduto = mp2.codigo AND m3.situacao <> 'CA')\n ");
        sql.append(" group by mp2.pessoa) soma on soma.pessoa = c.pessoa \n");
        sql.append(" left join contratorecorrencia crprodutoAtual on soma.contrato = crprodutoAtual.contrato \n");
        sql.append("WHERE CR.contrato = mp.contrato  \n");
        sql.append("and plano.renovaranuidadeautomaticamente  = 'f'  \n");
        sql.append("AND planorecorrencia.anuidadenaparcela IS FALSE \n");
        sql.append("AND C.importacao IS FALSE \n");
        sql.append("AND C.vigenciaAteAjustada >= ?  \n");
        sql.append("AND C.dataRenovarRealizada is null   \n");
        sql.append("AND CR.datainutilizada is null   \n");
        sql.append("AND coalesce(CR.valorAnuidade,0) > 1  \n");
        sql.append("AND C.codigo not in (select contrato from contratooperacao where tipooperacao in ('CA','DE'))   \n");
        sql.append("AND C.EMPRESA = ? \n");
        sql.append("AND (planorecorrencia.renovavelautomaticamente or CR.renovavelautomaticamente)  \n");
        sql.append("and CR.diaVencimentoAnuidade <= ?  \n");
        sql.append("and CR.mesVencimentoAnuidade = ?  \n");
        sql.append("AND (((soma.total/crprodutoAtual.valorAnuidade) < 0.9) or soma.total is null) \n");
        sql.append("AND NOT EXISTS (select futuro.codigo from movproduto futuro inner join produto prodFuturo on futuro.produto = prodFuturo.codigo   where futuro.pessoa = pes.codigo and  prodFuturo.tipoproduto = 'TA' and (futuro.datalancamento::date >= ('''"+Calendario.getInstance(dia).get(Calendar.YEAR)+"-'||CR.mesVencimentoAnuidade||'-'||CR.diaVencimentoAnuidade||'')::date or  futuro.anoreferencia > ?))");
        //@@@ TEMPORARIO GOLDEN LIFE
        sql.append("AND pes.nome not in ('JOSE VECCHI','VANESSA CRISTINA DA SILVA','WILLIAM RUIZ MARTINS', 'ALEXANDRE MONTEIRO COSTA') \n");
        sql.append("ORDER BY C.vigenciaAteAjustada  \n");


        Declaracao dc = new Declaracao(sql.toString(), con);
        int i = 1;
        dc.setInt(i++, Calendario.getInstance(dia).get(Calendar.YEAR));
        dc.setDate(i++, Uteis.getDataJDBC(dia));
        dc.setInt(i++, empresa);
        dc.setInt(i++, Calendario.getInstance(dia).get(Calendar.DAY_OF_MONTH));
        dc.setInt(i++, Calendario.getInstance(dia).get(Calendar.MONTH) + 1);
        dc.setInt(i++, Calendario.getInstance(dia).get(Calendar.YEAR));
        try (ResultSet rs = dc.executeQuery()) {
            return montarDadosConsulta(con, rs, nivelMontarDados);
        }

    }

    public List<ContratoRecorrenciaVO> consultarContratosRecorrenciaPorPagamento(Date dia, Integer empresa, Integer carenciaRenovacao, int nivelMontarDados, Connection con) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT contratorecorrencia.* FROM MovPagamento mp ");
        sql.append("inner join contrato on contrato.codigo = cast(SPLIT_PART(mp.produtospagos,',',3) as integer) and contrato.dataRenovarRealizada is null and contrato.empresa = ").append(empresa).append(" and contrato.situacao in('IN','AT')").append(" and contrato.dataprevistarenovar >= '").append(Uteis.getDataJDBC(Uteis.obterDataAnterior(dia, (carenciaRenovacao + 1)))).append("' and contrato.dataprevistarenovar < '").append(Uteis.getDataJDBC(dia)).append("' ");
        sql.append("inner join contratorecorrencia on contratorecorrencia.contrato = contrato.codigo and contratorecorrencia.renovavelautomaticamente is true and contratorecorrencia.datainutilizada is null ");
        sql.append("left join contratooperacao on contratooperacao.contrato = contrato.codigo and contratooperacao.tipooperacao in ('CA','DE') ");
        sql.append("WHERE (mp.dataLancamento ::date = '").append(Uteis.getDataJDBC(Uteis.obterDataAnterior(dia, 1))).append("' and SPLIT_PART(mp.produtospagos,',',3) <>'0') and contratooperacao is  null ");
        sql.append("ORDER BY contrato.codigo");
        Declaracao dc = new Declaracao(sql.toString(), con);
        try (ResultSet rs = dc.executeQuery()) {
            return montarDadosConsulta(con, rs, nivelMontarDados);
        }
    }

    public List<ContratoRecorrenciaVO> consultarContratosRecorrenciaPorContrato(Date dia, Integer empresa, Integer carenciaRenovacao, int contrato, int nivelMontarDados, Connection con) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT contratorecorrencia.* FROM contratorecorrencia ");
        sql.append("inner join contrato on contrato.codigo = contratorecorrencia.contrato and contrato.dataRenovarRealizada is null and contrato.empresa = ").append(empresa).append(" and contrato.situacao in('IN','AT')").append(" and contrato.dataprevistarenovar >= '").append(Uteis.getDataJDBC(Uteis.obterDataAnterior(dia, (carenciaRenovacao + 1)))).append("' and contrato.dataprevistarenovar < '").append(Uteis.getDataJDBC(dia)).append("' ");
        sql.append("inner join plano on plano.codigo = contrato.plano ");
        sql.append("inner join planorecorrencia on planorecorrencia.plano = plano.codigo ");
        sql.append("left join contratooperacao on contratooperacao.contrato = contrato.codigo and contratooperacao.tipooperacao in ('CA','DE') ");
        sql.append("WHERE contratorecorrencia.contrato = ").append(contrato).append(" and contratooperacao is  null and contratorecorrencia.renovavelautomaticamente ");
        Declaracao dc = new Declaracao(sql.toString(), con);
        try (ResultSet rs = dc.executeQuery()) {
            return montarDadosConsulta(con, rs, nivelMontarDados);
        }
    }

    @Override
    public ContratoRecorrenciaVO consultarPorContrato(int codigoContrato,
            int nivelMontarDados, Connection con) throws Exception {

        String sql = "select * from contratorecorrencia where contrato = "
                + codigoContrato + " and dataInutilizada is null";
        try (ResultSet rs = ContratoRecorrencia.criarConsulta(sql, con)) {
            if (rs.next()) {
                return montarDados(con, rs, nivelMontarDados);
            } else {
                return null;
            }
        }

    }

    public ContratoRecorrenciaVO consultarPorContratoPegarDiaDebitoAnuidade(int codigoContrato) throws Exception {
        return consultarPorContratoPegarDiaDebitoAnuidade(codigoContrato, this.con);
    }

    public ContratoRecorrenciaVO consultarPorContratoPegarDiaDebitoAnuidade(int codigoContrato, Connection con) throws Exception {

        String sql = "select valoranuidade, diavencimentocartao from contratorecorrencia where contrato = "
                + codigoContrato + " and dataInutilizada is null";
        try (ResultSet rs = ContratoRecorrencia.criarConsulta(sql, con)) {
            if (rs.next()) {
                ContratoRecorrenciaVO obj = new ContratoRecorrenciaVO();
                obj.setDiaVencimentoCartao(rs.getInt("diavencimentocartao"));
                obj.setValorAnuidade(rs.getDouble("valoranuidade"));
                return obj;
            } else {
                return null;
            }
        }

    }

    public ContratoRecorrenciaVO consultarPorContrato(int codigoContrato,
            int nivelMontarDados) throws Exception {
        return consultarPorContrato(codigoContrato, nivelMontarDados, this.con);
    }

    /**
     * Responsável por gravar a data de inutilização do contrato de recorrencia
     * author: alcides 04/08/2011
     */
    public void gravarDataInutilizada(ContratoRecorrenciaVO contratoRecorrencia) throws Exception {
        String sql = "UPDATE contratorecorrencia SET datainutilizada = '" + Uteis.getDataJDBC(contratoRecorrencia.getDataInutilizada())
                + "' WHERE codigo = " + contratoRecorrencia.getCodigo();
        ContratoRecorrencia.executarConsulta(sql, con);
    }

    public void alterarRenovacaoAutomaticaContratoRecorrencia(PlanoVO planoVO, boolean renovarAuto) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("update contratorecorrencia \n");
        sql.append("  set renovavelautomaticamente = ? \n");
        sql.append("from ( \n");
        sql.append("    select codigo \n");
        sql.append("    from contrato c \n");
        sql.append("    where plano = ?  \n");
        sql.append(" and contratoresponsavelrenovacaomatricula  = 0 and contratoresponsavelrematriculamatricula  = 0 \n");
        sql.append(" and situacao <> 'CA' \n");
        sql.append(" and not exists(select codigo from historicocontrato where contrato = c.codigo and tipohistorico = 'DE') \n");
        sql.append(") sql \n");
        sql.append("where sql.codigo = contratorecorrencia.contrato \n");
        try (PreparedStatement pst = con.prepareStatement(sql.toString())) {
            pst.setBoolean(1, renovarAuto);
            pst.setInt(2, planoVO.getCodigo());
            pst.execute();
        }
    }

    /**
     * Contar os contratos ativos em regime de recorrencia author: alcides
     * 15/07/2011
     */
    @Override
    public Integer contarAtivosRecorrentes(Integer codigoEmpresa, Date dataFim, List<Integer> consultores, List<Integer> convenios) throws Exception {
        Integer total = 0;
        try (ResultSet rs = consultarAtivosRecorrentes(codigoEmpresa, "COUNT(distinct c.codigo) AS total", dataFim, consultores, convenios)) {
            if (rs.next()) {
                total = rs.getInt("total");
            }
        }
        return total;
    }

    public Integer contarAdimplentesRecorrentes(Integer codEmpresa, Date date, List<Integer> consultores, List<Integer> convenios)throws Exception {
        Integer total = 0;
        try(ResultSet rs = consultarAdimplentesRecorrentes(codEmpresa,"COUNT(distinct cli.codigo) AS total",date,consultores,convenios, true)){
            if(rs.next()){
                total = rs.getInt("total");
            }
        }
        return total;
    }

    public Double somarAdimplentesRecorrentes(Integer codEmpresa, Date date, List<Integer> consultores, List<Integer> convenios)throws Exception {
        Double total = 0.0;
        try(ResultSet rs = consultarAdimplentesRecorrentes(codEmpresa,"sum(mvp.valorpago) as valor",date,consultores,convenios, true)){
            if(rs.next()){
                total = rs.getDouble("valor");
            }
        }
        return total;
    }

    /**
     * author: alcides 25/10/2011
     */
    private ResultSet consultarAtivosRecorrentes(Integer codigoEmpresa, String campo, Date dataFim, List<Integer> consultores, List<Integer> convenios) throws Exception {
        if (dataFim == null) {
            dataFim = Calendario.hoje();
        }
        String sqlEmpresa = getStringEmpresa(codigoEmpresa);
        String sql = String.format(sqlAtivos,
                campo,
                sqlEmpresa,
                Uteis.getDataFormatoBD(dataFim),
                Uteis.getDataFormatoBD(dataFim),
                Uteis.getDataFormatoBD(dataFim),
                Uteis.getDataFormatoBD(dataFim));
        sql += montarFiltrosConsultores(consultores);
        sql += "  AND\n"
                + "      (\n"
                + "        c.regimerecorrencia IS TRUE\n"
                 + " or cp.tipoconveniocobranca <> 0 \n"
                + "        or (aa.codigo is not null and coalesce((select min(dataalteracao) " +
                "                                                    from log l \n" +
                "                                                   where l.nomeentidade = 'AUTORIZACAOCOBRANCACLIENTE'\n" +
                "                                                     and l.operacao = 'INCLUSÃO'\n" +
                "                                                     and l.pessoa = c.pessoa), null, c.vigenciaDe) <= c.vigenciaAteAjustada)"
                + "      )";
        if(convenios!= null && !convenios.isEmpty()){
            sql += getSqlContratosEmConvenio(convenios,"c.pessoa");
        }

        return ContratoRecorrencia.criarConsulta(sql, con);
    }

    private ResultSet consultarAdimplentesRecorrentes(Integer codigoEmpresa, String campos, Date dataFim, List<Integer> consultores, List<Integer> convenios, boolean contar) throws Exception {
        if (dataFim == null) {
            dataFim = Calendario.hoje();
        }

        StringBuilder sql = new StringBuilder();
        sql.append("select \n");
        sql.append(campos).append(" \n");
        sql.append("from movparcela mp \n");
        sql.append("inner join empresa emp on emp.codigo = mp.empresa \n");
        sql.append("inner join pessoa p on p.codigo = mp.pessoa \n");
        sql.append("inner join cliente cl on cl.pessoa = p.codigo \n");
        sql.append("inner join pagamentomovparcela pm on pm.movparcela = mp.codigo \n");
        sql.append("inner join movpagamento mov on mov.codigo = pm.movpagamento \n");
        sql.append("inner join formapagamento fp on fp.codigo = mov.formapagamento \n");
        sql.append("left join conveniocobranca cc on cc.codigo = mov.conveniocobranca \n");
        sql.append("where mp.situacao = 'PG' \n");
        sql.append("and mp.valorparcela > 0 \n");
        sql.append("and exists(select codigo from autorizacaocobrancacliente au where au.ativa and au.cliente = cl.codigo) \n");
        sql.append("and not exists(select codigo from movparcela where pessoa = p.codigo and situacao = 'EA' and datavencimento::date < '").append(Uteis.getDataFormatoBD(dataFim)).append("') \n");
        sql.append("and mp.datavencimento::date >= '").append(Uteis.getDataFormatoBD(Uteis.obterPrimeiroDiaMes(dataFim))).append("'\n");
        sql.append("and mp.datavencimento::date <= '").append(Uteis.getDataFormatoBD(dataFim)).append("'\n");
        if (!UteisValidacao.emptyNumber(codigoEmpresa)) {
            sql.append("and mp.empresa = ").append(codigoEmpresa).append(" \n");
        }
        if (!UteisValidacao.emptyList(consultores)) {
            StringBuilder filtros = new StringBuilder();
            filtros.append("and exists(select v.codigo from vinculo v where v.tipovinculo = 'CO' and v.cliente = cl.codigo and v.colaborador in (");
            for (Integer i : consultores) {
                filtros.append(", ").append(i.toString());
            }
            filtros.append(")) \n");
            sql.append(filtros.toString().replaceFirst(",", ""));
        }
        if (!UteisValidacao.emptyList(convenios)) {
            sql.append(getSqlContratosEmConvenio(convenios, "p.codigo")).append(" \n");
        }
        if (!contar) {
            sql.append("group by cl.matricula, cl.codigo, mp.codigo, p.nome, emp.nome \n");
            sql.append("order by 2 \n");
        }
        return ContratoRecorrencia.criarConsulta(sql.toString(), con);
    }

    private String getSqlContratosEmConvenio(List<Integer> convenios, String colunaPessoa){
        return " AND EXISTS (SELECT 1 FROM cliente c2 INNER JOIN autorizacaocobrancacliente cli ON cli.cliente = c2.codigo WHERE cli.ativa and cli.conveniocobranca IN (" + Uteis.montarListaIN(convenios) + ") AND c2.pessoa = "+colunaPessoa+") ";
    }

    private String getSqlContratosSemConvenio(List<Integer> convenios, String colunaPessoa){
        if (!UteisValidacao.emptyList(convenios)){
            return " AND NOT EXISTS (SELECT 1 FROM cliente c2 INNER JOIN autorizacaocobrancacliente cli ON cli.cliente = c2.codigo WHERE cli.ativa and cli.conveniocobranca IN (" + Uteis.montarListaIN(convenios) + ") AND c2.pessoa = "+colunaPessoa+") ";
        } else {
            return " AND NOT EXISTS (SELECT 1 FROM cliente c2 INNER JOIN autorizacaocobrancacliente cli ON cli.cliente = c2.codigo WHERE cli.ativa " + "AND c2.pessoa = " + colunaPessoa + ") ";
        }
    }

    /**
     * Contar os contratos não renovados em regime de recorrencia author:
     * alcides 15/07/2011
     */
    @Override
    public Integer contarNaoRenovadosRecorrentes(Integer codigoEmpresa, Date dataFim, List<Integer> consultores, List<Integer> convenios) throws Exception {
        Integer total = 0;
        try (ResultSet rs = consultarNaoRenovadosRecorrentes(codigoEmpresa, "COUNT(DISTINCT(c.codigo)) AS total", dataFim, consultores, convenios)) {
            if (rs.next()) {
                total = rs.getInt("total");
            }
        }
        return total;
    }

    /* (non-Javadoc)
     * @see negocio.interfaces.contrato.ContratoRecorrenciaInterfaceFacade#consultarNaoRenovadosRecorrentes(java.lang.Integer, java.util.Date, java.util.List)
     */
    @SuppressWarnings("unchecked")
    @Override
    public List<RecorrenciaClienteTO> consultarNaoRenovadosRecorrentes(Integer codigoEmpresa, Date dataFim, List<Integer> consultores, List<Integer> convenios, int nivelMontarDados) throws Exception {
        return montarDadosConsulta(consultarNaoRenovadosRecorrentes(codigoEmpresa, "DISTINCT\n"
                + "  cli.matricula,\n"
                + "  cli.codigo AS codcliente,\n"
                + "  p.nome,\n"
                + "  p.cfp AS cpf,\n"
                + "  c.codigo   AS codcontrato,\n"
                + "  c.datalancamento,\n"
                + "  null as datavencimento,\n"
                + "  c.vigenciade,\n"
                + "  c.vigenciaateajustada,\n"
                + "  cd.numeromeses", dataFim, consultores, convenios), false);
    }

    public Integer contarParcelasRecorrenciaSemCartao(Integer codigoEmpresa, Date dataFim, List<Integer> consultores, List<Integer> convenios) throws Exception {
        try (ResultSet rs = consultarContratoscomParcelasRecorrenciaSemCartao(codigoEmpresa, "count(DISTINCT c.codigo) AS total", dataFim, consultores, convenios)) {
            if (rs.next()) {
                return rs.getInt("total");
            }
        }
        return 0;
    }

    public List<RecorrenciaClienteTO> consultarContratosRecorrenciaSemCartao(Integer codigoEmpresa, Date dataFim, List<Integer> consultores, List<Integer> convenios) throws Exception {
        return montarDadosConsulta(consultarContratoscomParcelasRecorrenciaSemCartao(codigoEmpresa, "distinct cli.matricula,\n"
                + "  cli.codigo AS codcliente,\n"
                + "  p.nome,\n"
                + "  p.cfp AS cpf,\n"
                + "  c.codigo   AS codcontrato,\n"
                + "  c.datalancamento,\n"
                + "  min(movp.datavencimento) as datavencimento,\n"
                + "  c.vigenciade,\n"
                + "  c.vigenciaateajustada,\n"
                + "  cd.numeromeses", dataFim, consultores, convenios), false);
    }

    public List<RecorrenciaClienteTO> consultarContratosRecorrenciaParcelaEmAbertoDCC(Integer codigoEmpresa, Date dataFim, List<Integer> convenios) throws Exception {
        return montarDadosConsulta(consultarContratoscomParcelasRecorrenciaParcelaEmAberto(
                codigoEmpresa, "mp.codigo as codigoParcela,si.matricula, si.codigocliente AS codcliente,"
                        + " mp.descricao as descricaoParcela,si.nomecliente as nome, si.codigocontrato AS codcontrato, si.datalancamentocontrato as datalancamento, "
                        + " min(mp.datavencimento) as datavencimento, si.datavigenciade as vigenciade, si.datavigenciaateajustada as vigenciaateajustada , si.cpf, cd.numeromeses, "
                        + "count(mp.codigo) as quantidadeParcelas,mp.valorparcela as valor ",
                dataFim, convenios), true, true);
    }

    public Integer contarPendenciaClienteMensagemCartaoVencido(Integer codigoEmpresa, Date dataFim, List<Integer> consultores) throws Exception {
        try (ResultSet rs = sqlPendenciaClienteMensgemCartaoVencido(codigoEmpresa, "COUNT (distinct clienteMensagem.codigo) as total", dataFim, consultores, null)) {
            if (rs.next()) {
                return rs.getInt("total");
            }
        }
        return 0;
    }

    public ResultSet consultarPendenciaClienteMensagemCartaoVencido(Integer codigoEmpresa, Date dataBaseInicio, Date dataFim, List<Integer> consultores,ConfPaginacao paginacao) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT distinct dw.codigocliente as cli , dw.codigocontrato as codContrato, dw.nomecliente as nome ,dw.nomePlano ,dw.codigopessoa as codPessoa,  \n");
        sql.append("dw.matricula as matriculacli,dw.situacao as situacaoCliente,  \n");
        sql.append("dw.datavigenciade as dataInicio,dw.datavigenciaateajustada as dataFim,cd.numeroMeses as duracaoContrato,ct.nomemodalidades,dw.telefonescliente  \n");
        sql.append("FROM clienteMensagem  \n");
        sql.append("INNER JOIN Situacaoclientesinteticodw dw ON dw.codigocliente = clienteMensagem.cliente \n");
        sql.append("LEFT JOIN Contrato ct ON  ct.codigo = dw.codigocontrato  \n");
        sql.append("LEFT JOIN ContratoDuracao cd ON cd.contrato = ct.codigo  \n");
        sql.append("INNER JOIN autorizacaocobrancacliente aut on aut.cliente = dw.codigocliente \n");
        sql.append("INNER JOIN conveniocobranca conv on aut.conveniocobranca = conv.codigo  \n");
        sql.append("WHERE tipomensagem = 'CV'  \n");
        sql.append("AND (dw.situacao = 'AT' or exists (select codigo from movparcela where situacao = 'EA' and pessoa = dw.codigopessoa))  \n");
        if (codigoEmpresa != 0) {
            sql.append("AND dw.empresacliente IN (" + codigoEmpresa + ")\n");
        }
        sql.append("AND aut.tipoautorizacao  = " + TipoAutorizacaoCobrancaEnum.CARTAOCREDITO.getId() + "\n");
        sql.append("AND aut.ativa = true \n");
        sql.append("AND clientemensagem.mensagem LIKE concat('%', aut.cartaomascaradointerno, '%') \n");
        if(paginacao != null && paginacao.getOrdernar()){
            sql.append(" ORDER BY ").append(paginacao.getColunaOrdenacao()).append(" ").append(paginacao.getDirecaoOrdenacao());
        }
        if(paginacao != null && paginacao.isExistePaginacao()){
            sql.append(" limit ").append(paginacao.getItensPorPagina())
                    .append(" offset ").append((paginacao.getPaginaAtual() * paginacao.getItensPorPagina()));
        }
        return criarConsulta(sql.toString(), con);
    }

    private ResultSet sqlPendenciaClienteMensgemCartaoVencido(Integer codigoEmpresa, String colunas, Date dataFim, List<Integer> consultores,ConfPaginacao paginacao) throws Exception {
        StringBuilder joinCondicaoColab = new StringBuilder();
        StringBuilder condicaoColab = new StringBuilder();
        if (consultores != null && !consultores.isEmpty()) {
            joinCondicaoColab.append("LEFT JOIN vinculo v ON cliente.codigo = v.cliente ");
            condicaoColab.append(" AND v.colaborador in (");
            for (Integer consultor : consultores) {
                condicaoColab.append(consultor).append(",");
            }
            condicaoColab.deleteCharAt(condicaoColab.length() - 1);
            condicaoColab.append(")");
        }
        StringBuilder sqlStr = new StringBuilder();
        String sqlEmpresa = "";
        if (!UteisValidacao.emptyNumber(codigoEmpresa)) {
            sqlEmpresa = "AND cliente.empresa = " + codigoEmpresa;
        }
                sqlStr.append("SELECT " + colunas + " FROM clienteMensagem ");
                sqlStr.append("INNER JOIN cliente ON clienteMensagem.cliente = cliente.codigo " + sqlEmpresa + "\n");
                sqlStr.append(joinCondicaoColab.toString() + "\n");
                sqlStr.append("WHERE tipomensagem = '" + TiposMensagensEnum.CARTAO_VENCIDO.getSigla() + "'\n");
                sqlStr.append(condicaoColab.toString());
                sqlStr.append(" and (cliente.situacao = 'AT' or exists (select codigo from movparcela where situacao = 'EA' and pessoa = cliente.pessoa))");
        if(paginacao != null && paginacao.isExistePaginacao()){
            sqlStr.append(" limit ").append(paginacao.getItensPorPagina())
                    .append(" offset ").append((paginacao.getPaginaAtual() * paginacao.getItensPorPagina()));
        }
        return criarConsulta(sqlStr.toString(), con);
    }

    private List<RecorrenciaClienteTO> montarDadosConsulta(ResultSet tabelaResultado, boolean movparcela) throws Exception {
        return montarDadosConsulta(tabelaResultado, movparcela, false);
    }
    private List<RecorrenciaClienteTO> montarDadosConsulta(ResultSet tabelaResultado, boolean movparcela, boolean camposEspeciais) throws Exception {
        List<RecorrenciaClienteTO> vetResultado = new ArrayList<RecorrenciaClienteTO>();
        while (tabelaResultado.next()) {
            RecorrenciaClienteTO obj = new RecorrenciaClienteTO();
            obj.setMatricula(tabelaResultado.getString("matricula"));
            obj.setCodigoCliente(tabelaResultado.getInt("codcliente"));
            obj.setNomeCliente(tabelaResultado.getString("nome"));
            obj.setCodigoContrato(tabelaResultado.getInt("codcontrato"));
            obj.setDataLancamento(tabelaResultado.getDate("datalancamento"));
            obj.setDataVencimento(tabelaResultado.getDate("datavencimento"));
            obj.setDataInicio(tabelaResultado.getDate("vigenciade"));
            obj.setDataTermino(tabelaResultado.getDate("vigenciaateajustada"));
            obj.setDuracao(tabelaResultado.getInt("numeromeses"));
            obj.setCpf(tabelaResultado.getString("cpf"));
            try{
                obj.setValor(tabelaResultado.getDouble("valor"));
            }catch (Exception ignored){}
            try {
                obj.setCodigoParcela(tabelaResultado.getInt("codigoParcela"));
                obj.setDescricaoParcela(tabelaResultado.getString("descricaoParcela"));
            }catch (Exception ignored){}
            if (movparcela) {
                obj.setNrParcelasEA(tabelaResultado.getInt("quantidadeParcelas"));
            }

            if(camposEspeciais){
                try (ResultSet rsParcela = criarConsulta("select p.prefixo, mpc.contrato, mpc.pessoa, mpc.descricao, mpc.empresa from produto p\n" +
                        "inner join movproduto mp on p.codigo = mp.produto\n" +
                        "inner join movprodutoparcela mpp on mpp.movproduto = mp.codigo\n" +
                        "inner join movparcela mpc on mpp.movparcela = mpc.codigo\n" +
                        "where mpp.movparcela = " + obj.getCodigoParcela(), con)) {
                    if (rsParcela.next()) {
                        obj.setTipo(rsParcela.getString("prefixo"));
                        Integer codigoEmpresa = rsParcela.getInt("empresa");
                        try (ResultSet rsEmpresa = criarConsulta("select nome from empresa where codigo = " + codigoEmpresa, con)) {
                            if (rsEmpresa.next()) {
                                obj.setEmpresa(rsEmpresa.getString("nome"));
                            }
                        }
                    }
                }
            }

            vetResultado.add(obj);
        }

        return vetResultado;
    }
    /**
     * author: alcides 25/10/2011
     */
    private ResultSet consultarNaoRenovadosRecorrentes(Integer codigoEmpresa, String campo, Date dataFim, List<Integer> consultores, List<Integer> convenios) throws Exception {
        String sqlEmpresa = getStringEmpresa(codigoEmpresa);

        String sqlNaoRenovadosConsulta = String.format(sqlNaoRenovados,
                campo,
                Uteis.getDataFormatoBD(Uteis.obterPrimeiroDiaMes(dataFim)),
                Uteis.getDataFormatoBD(dataFim),
                sqlEmpresa);

        sqlNaoRenovadosConsulta += montarFiltrosConsultores(consultores);
        if(convenios != null && !convenios.isEmpty()){
            sqlNaoRenovadosConsulta += getSqlContratosEmConvenio(convenios, "c.pessoa");
        }

        return ContratoRecorrencia.criarConsulta(sqlNaoRenovadosConsulta, con);
    }
    private ResultSet consultarContratoscomParcelasRecorrenciaParcelaEmAberto(Integer codigoEmpresa, String campo, Date dataFim, List<Integer> convenios) throws Exception {
        StringBuilder sqlParcelasEmAberto = new StringBuilder("");
        sqlParcelasEmAberto.append("SELECT\n");
        sqlParcelasEmAberto.append(campo).append("\n");
        sqlParcelasEmAberto.append("FROM MovParcela mp\n");
        sqlParcelasEmAberto.append("  INNER JOIN Situacaoclientesinteticodw si ON si.codigopessoa = mp.pessoa\n");
        sqlParcelasEmAberto.append("  LEFT JOIN contratoduracao cd ON cd.contrato = si.codigocontrato\n");
        sqlParcelasEmAberto.append(" Left Join contratocondicaopagamento ccp on ccp.contrato = mp.contrato\n");
        sqlParcelasEmAberto.append(" Left join condicaopagamento cp on cp.codigo = ccp.condicaopagamento\n");
        sqlParcelasEmAberto.append("WHERE 1 = 1\n");
        if (!UteisValidacao.emptyNumber(codigoEmpresa)) {
            sqlParcelasEmAberto.append("      AND mp.empresa = ").append(codigoEmpresa).append("\n");
        }
        sqlParcelasEmAberto.append("      AND mp.situacao = 'EA'\n");
        if(convenios != null && !convenios.isEmpty()){
            sqlParcelasEmAberto.append(getSqlContratosEmConvenio(convenios,"mp.pessoa"));
        }
        sqlParcelasEmAberto.append("AND mp.datavencimento::DATE >= '").append(Uteis.getData(Calendario.hoje())).append("'\n ");
        sqlParcelasEmAberto.append("      AND (mp.parcelaDCC or mp.regimerecorrencia or coalesce(cp.tipoconveniocobranca, 0) >  0)\n");
        if(!campo.toUpperCase().contains("DISTINCT")){
            sqlParcelasEmAberto.append(" group by mp.codigo,si.matricula, si.codigocliente, nome, codcontrato, datalancamento, vigenciade,");
            sqlParcelasEmAberto.append(" vigenciaateajustada, cd.numeromeses,datavencimento,descricaoParcela,valor,si.cpf\n");
        }

        sqlParcelasEmAberto.append("  order by si.nomecliente \n");
        return ContratoRecorrencia.criarConsulta(sqlParcelasEmAberto.toString(), con);
    }

    private ResultSet consultarContratoscomParcelasRecorrenciaSemCartao(Integer codigoEmpresa, String campo, Date dataFim, List<Integer> consultores, List<Integer> convenios) throws Exception {
        if (dataFim == null) {
            dataFim = Calendario.hoje();
        }
        String sqlEmpresa = getStringEmpresa(codigoEmpresa);
        String sql = String.format(sqlAtivosComMovParcela,
                campo,
                sqlEmpresa,
                Uteis.getDataFormatoBD(dataFim),
                Uteis.getDataFormatoBD(dataFim),
                Uteis.getDataFormatoBD(dataFim),
                Uteis.getDataFormatoBD(dataFim));
        sql += montarFiltrosConsultores(consultores);
        sql += "  AND\n"
                + "      (\n"
                + "        c.regimerecorrencia IS TRUE\n"
                + " or cp.tipoconveniocobranca <> 0 \n"
                + "        or (aa.codigo is not null and coalesce((select min(dataalteracao) " +
                "                                                    from log l \n" +
                "                                                   where l.nomeentidade = 'AUTORIZACAOCOBRANCACLIENTE'\n" +
                "                                                     and l.operacao = 'INCLUSÃO'\n" +
                "                                                     and l.pessoa = c.pessoa), null, c.vigenciaDe) <= c.vigenciaAteAjustada)"
                + "      )";

            sql += getSqlContratosSemConvenio(convenios,"c.pessoa");
            sql += "group by 1,2,3,4,5,10";

        return ContratoRecorrencia.criarConsulta(sql, con);
    }

    /**
     * author: alcides 27/10/2011
     */
    private String montarFiltrosConsultores(List<Integer> consultores) {
        String filtros = "";
        if (consultores != null && !consultores.isEmpty()) {
            StringBuilder sqlFiltro = new StringBuilder();
            sqlFiltro.append(" AND c.consultor IN (");
            for (Integer i : consultores) {
                sqlFiltro.append(", ").append(i.toString());
            }
            sqlFiltro.append(" )");
            filtros = sqlFiltro.toString().replaceFirst(",", "");
        }
        return filtros;
    }

    /**
     * author: alcides 25/10/2011
     */
    private ResultSet consultarCanceladosRecorrentes(Integer codigoEmpresa, String campo, Date dataFim, List<Integer> consultores, List<Integer> convenios) throws Exception {
        String sqlEmpresa = getStringEmpresa(codigoEmpresa);
        String sqlCanceladosConsulta = String.format(sqlCancelados,
                campo, sqlEmpresa,
                Uteis.getDataFormatoBD(Uteis.obterPrimeiroDiaMes(dataFim)),
                Uteis.getDataFormatoBD(dataFim));
        sqlCanceladosConsulta += montarFiltrosConsultores(consultores);
        sqlCanceladosConsulta += "  AND\n"
                + "      (\n"
                + "        c.regimerecorrencia IS TRUE\n"
                + "       or cp.tipoconveniocobranca <> 0)\n";
        if(convenios != null && !convenios.isEmpty()){
            sqlCanceladosConsulta += getSqlContratosEmConvenio(convenios, "c.pessoa");
        }
        return criarConsulta(sqlCanceladosConsulta, con);
    }

    private String getStringEmpresa(Integer codigoEmpresa) {
        String sqlEmpresa = "";
        if (!UteisValidacao.emptyNumber(codigoEmpresa)) {
            sqlEmpresa = "AND c.empresa = " + codigoEmpresa;
        }
        return sqlEmpresa;
    }

    /**
     * Contar os contratos cancelados automaticamente em regime de recorrencia
     * author: alcides 15/07/2011
     */
    @Override
    public Integer contarCanceladosAutomaticamente(Integer codigoEmpresa, Date dataFim, List<Integer> consultores, List<Integer> convenios) throws Exception {
        Integer total = 0;
        try (ResultSet rs = consultarCanceladosRecorrentes(codigoEmpresa, " COUNT(c.codigo) AS total ", dataFim, consultores, convenios)) {
            if (rs.next()) {
                total = rs.getInt("total");
            }
        }
        return total;
    }

    /**
     * author: alcides 16/09/2011
     */
    @SuppressWarnings("unchecked")
    public List<RecorrenciaClienteTO> consultarContratosRecorrencia(Integer codigoEmpresa, Date dataFim, List<Integer> consultores, List<Integer> conveinos, int nivelMontarDados) throws Exception {
        return montarDadosConsulta(consultarAtivosRecorrentes(codigoEmpresa, "distinct cli.matricula,\n"
                + "  cli.codigo AS codcliente,\n"
                + "  p.nome,\n"
                + "  p.cfp AS cpf,\n"
                + "  c.codigo   AS codcontrato,\n"
                + "  c.datalancamento,\n"
                + "  NULL       AS datavencimento,\n"
                + "  c.vigenciade,\n"
                + "  c.vigenciaateajustada,\n"
                + "  cd.numeromeses", dataFim, consultores, conveinos), false);
    }

    public List<RecorrenciaClienteTO> consultarAdimplentesRecorrencia(Integer codigoEmpresa, Date dataFim, List<Integer> consultores, List<Integer> convenios, int nivelMontarDados) throws Exception {
        ResultSet rs = consultarAdimplentesRecorrentes(codigoEmpresa, "distinct cl.matricula,\n" +
                "p.nome,\n" +
                "cl.codigo,\n" +
                "mp.codigo as parcela,\n" +
                "mp.descricao as descricao_parcela,\n" +
                "mp.valorparcela as valor,\n" +
                "cl.situacao,\n" +
                "string_agg(distinct cc.descricao, ' | ') as convenio,\n" +
                "string_agg(distinct fp.descricao, ' | ') as formapagamento,\n" +
                "emp.nome as nomeempresa ", dataFim, consultores, convenios, false);
        List<RecorrenciaClienteTO> recorrenciaClienteTOS = new ArrayList<>();
        while (rs.next()) {
            RecorrenciaClienteTO obj = new RecorrenciaClienteTO() ;
            obj.setMatricula(rs.getString("matricula"));
            obj.setNomeCliente(rs.getString("nome"));
            obj.setCodigoCliente(rs.getInt("codigo"));
            obj.setValor(rs.getDouble("valor"));
            obj.setSituacaoCliente(rs.getString("situacao"));
            obj.setCodigoParcela(rs.getInt("parcela"));
            obj.setDescricaoParcela(rs.getString("descricao_parcela"));
            obj.setDescricaoConvenio(rs.getString("convenio"));
            obj.setDescricaoFormaPagamento(rs.getString("formapagamento"));
            obj.setEmpresa(rs.getString("nomeempresa"));
            recorrenciaClienteTOS.add(obj);
        }
        return recorrenciaClienteTOS;
    }

    /**
     * author: alcides 16/09/2011
     */
    @SuppressWarnings("unchecked")
    public List<RecorrenciaClienteTO> consultarCanceladosRecorrencia(Integer codigoEmpresa, Date dataFim, List<Integer> consultores,List<Integer> convenios, int nivelMontarDados) throws Exception {

        try (ResultSet rs = consultarCanceladosRecorrentes(codigoEmpresa, "   cli.matricula,\n"
                + "  cli.codigo AS codcliente,\n"
                + "  p.nome,\n"
                + "  p.cfp AS cpf,\n"
                + "  c.codigo   AS codcontrato,\n"
                + "  c.datalancamento,\n"
                + "  NULL       AS datavencimento,\n"
                + "  c.vigenciade,\n"
                + "  c.vigenciaateajustada,\n"
                + "  cd.numeromeses ", dataFim, consultores, convenios)) {
            return montarDadosConsulta(rs, false);
        }
    }

    @Override
    public ContratoRecorrenciaVO incluirContratoRecorrencia(
            PlanoRecorrenciaVO recorrencia,
            ContratoVO contratoVO, int diaVencimentoRecorrencia) throws Exception {

        if (contratoVO.getPlano().getRegimeRecorrencia()) {
            PlanoEmpresaVO planoEmpresaVO = contratoVO.getPlano().obterPlanoEmpresa(contratoVO.getEmpresa().getCodigo());

            ContratoRecorrenciaVO contratoRecorrenciaVO = new ContratoRecorrenciaVO();
            contratoRecorrenciaVO.setContrato(contratoVO);
            contratoRecorrenciaVO.setDiaVencimentoAnuidade(recorrencia.getDiaAnuidade());
            contratoRecorrenciaVO.setMesVencimentoAnuidade(recorrencia.getMesAnuidade());
            contratoRecorrenciaVO.setDiaVencimentoCartao(diaVencimentoRecorrencia);
            contratoRecorrenciaVO.setDiasCancelamentoAutomatico(recorrencia.getQtdDiasAposVencimentoCancelamentoAutomatico());
            contratoRecorrenciaVO.setFidelidade(recorrencia.getDuracaoPlano());
            contratoRecorrenciaVO.setPessoa(contratoVO.getPessoa());
            contratoRecorrenciaVO.setRenovavelAutomaticamente(recorrencia.getRenovavelAutomaticamente());
            contratoRecorrenciaVO.setValorMensal(recorrencia.getValorMensal());
            if (planoEmpresaVO != null && !UteisValidacao.emptyNumber(planoEmpresaVO.getValorMensal())) {
                contratoRecorrenciaVO.setValorMensal(planoEmpresaVO.getValorMensal());
            }

            contratoRecorrenciaVO.setValorAnuidade(recorrencia.getValorAnuidade());
            if (planoEmpresaVO != null && !UteisValidacao.emptyNumber(planoEmpresaVO.getValorAnuidade())) {
                contratoRecorrenciaVO.setValorAnuidade(planoEmpresaVO.getValorAnuidade());
            }

            contratoRecorrenciaVO.setAnuidadeNaParcela(recorrencia.isAnuidadeNaParcela());
            contratoRecorrenciaVO.setParcelaAnuidade(recorrencia.getParcelaAnuidade());

            contratoRecorrenciaVO.setCancelamentoProporcional(false);
            if (recorrencia.isCancelamentoProporcional()) {
                if (!recorrencia.isCancelamentoProporcionalSomenteRenovacao() || contratoVO.isContratoRenovacao()) {
                    contratoRecorrenciaVO.setCancelamentoProporcional(true);
                }
            }

            contratoRecorrenciaVO.setQtdDiasCobrarProximaParcela(recorrencia.getQtdDiasCobrarProximaParcela());
            contratoRecorrenciaVO.setQtdDiasCobrarAnuidadeTotal(recorrencia.getQtdDiasCobrarAnuidadeTotal());
            contratoRecorrenciaVO.setParcelarAnuidade(recorrencia.isParcelarAnuidade());
            if (recorrencia.isAnuidadeNaParcela() && !UteisValidacao.emptyNumber(recorrencia.getParcelaAnuidade())) {
                MovParcela movParcelaDAO = new MovParcela(con);
                MovParcelaVO movParcelaVO = movParcelaDAO.consultarPorNumeroContrato(recorrencia.getParcelaAnuidade(), contratoVO.getCodigo());
                movParcelaDAO = null;
                if (movParcelaVO != null) {
                    contratoRecorrenciaVO.setDiaVencimentoAnuidade(Uteis.obterDiaData(movParcelaVO.getDataVencimento()));
                    contratoRecorrenciaVO.setMesVencimentoAnuidade(Uteis.getMesData(movParcelaVO.getDataVencimento()));
                }
            }
            this.incluir(contratoRecorrenciaVO);

            return contratoRecorrenciaVO;
        } else {
            return null;
        }

    }

    public List<RecorrenciaClienteTO> consultarParcelasRecorrencia(Integer codigoEmpresa, String colunas, boolean count, String[] colunasCount, String nrTentativas,
            String situacaoParcela, Date dataInicio, Date dataFim, List<Integer> convenios,
            String descricaoPagamento, String situacaoRemessa, boolean somenteMes,
            boolean somenteForaMes) throws Exception {
        List<RecorrenciaClienteTO> vetResultado = new ArrayList<RecorrenciaClienteTO>();
        RemessaItem remessaItem = new RemessaItem(con);
        try (ResultSet tabelaResultado = remessaItem.consultarTotalizador(codigoEmpresa, colunas, count, colunasCount, nrTentativas, situacaoParcela,
                dataInicio, dataFim, convenios, descricaoPagamento, situacaoRemessa, somenteMes, somenteForaMes, "")) {

            int parcelaAnterior = 0;
            while (tabelaResultado.next()) {
                if(parcelaAnterior != tabelaResultado.getInt("codigoparcela")) {
                    RecorrenciaClienteTO obj = new RecorrenciaClienteTO();
                    obj.setMatricula(tabelaResultado.getString("matricula"));
                    obj.setCodigoCliente(tabelaResultado.getInt("codcliente"));
                    obj.setNomeCliente(tabelaResultado.getString("nome"));
                    obj.setCodigoContrato(tabelaResultado.getInt("codcontrato"));
                    obj.setDataLancamento(tabelaResultado.getDate("datalancamento"));
                    obj.setDataVencimento(tabelaResultado.getDate("datavencimento"));
                    obj.setDataInicio(tabelaResultado.getDate("vigenciade"));
                    obj.setDataTermino(tabelaResultado.getDate("vigenciaateajustada"));
                    obj.setDuracao(tabelaResultado.getInt("numeromeses"));
                    obj.setCodigoParcela(tabelaResultado.getInt("codigoparcela"));
                    obj.setDescricaoParcela(tabelaResultado.getString("descricaoparcela"));
                    vetResultado.add(obj);
                }
                parcelaAnterior = tabelaResultado.getInt("codigoparcela");
            }
        }
        return vetResultado;
    }

    public Integer contarParcelasCanceladas(Integer codigoEmpresa, String colunas, boolean count, String[] colunasCount, String nrTentativas,
            String situacaoParcela, Date dataInicio, Date dataFim, List<Integer> convenios,
            String descricaoPagamento, String situacaoRemessa, boolean somenteMes,
            boolean somenteForaMes) throws Exception {

        RemessaItem remessaItem = new RemessaItem(con);
        try (ResultSet tabelaResultado = remessaItem.consultarTotalizador(codigoEmpresa, colunas, count, colunasCount, nrTentativas, situacaoParcela,
                dataInicio, dataFim, convenios, descricaoPagamento, situacaoRemessa, somenteMes, somenteForaMes, "")) {
            if (tabelaResultado.next()) {
                return tabelaResultado.getInt("qtd");
            }
        }
        return 0;
    }

    public Integer contarParcelasAbertas(Integer codigoEmpresa, Date fim) throws Exception {
        MovParcela movParcela = new MovParcela(con);
        Integer qtdParcelas = 0;
        try (ResultSet tabelaResultado = movParcela.getResultSetParcelasComAutorizacaoCobranca(new ConvenioCobrancaVO(), null, fim, null, null, true, codigoEmpresa, null)) {
            if (tabelaResultado.next()) {
                qtdParcelas += tabelaResultado.getInt("qtd");
            }
            try (ResultSet tabelaResultado2 = movParcela.getResultSetParcelasParaRemessaRepescagem(new ConvenioCobrancaVO(), null, fim, null, null, true, codigoEmpresa, null)) {
                if (tabelaResultado2.next()) {
                    qtdParcelas += tabelaResultado2.getInt("qtd");
                }
            }
        }
        return qtdParcelas;
    }

    public Double somarParcelasAbertas(Integer codigoEmpresa, Date fim) throws Exception {
        MovParcela movParcela = new MovParcela(con);
        Double valorParcelas = 0.0;
        try (ResultSet tabelaResultado = movParcela.getResultSetParcelasComAutorizacaoCobranca(new ConvenioCobrancaVO(), null, fim, null, null, true, codigoEmpresa, null)) {
            if (tabelaResultado.next()) {
                valorParcelas += tabelaResultado.getDouble("total");
            }
            try (ResultSet tabelaResultado2 = movParcela.getResultSetParcelasParaRemessaRepescagem(new ConvenioCobrancaVO(), null, fim, null, null, true, codigoEmpresa, null)) {
                if (tabelaResultado2.next()) {
                    valorParcelas += tabelaResultado2.getDouble("total");
                }
            }
        }
        return valorParcelas;
    }

    public List<RecorrenciaClienteTO> consultarParcelasAbertas(Integer codigoEmpresa, Date fim) throws Exception {
        List<RecorrenciaClienteTO> verResultado = new ArrayList<RecorrenciaClienteTO>();
        try (ResultSet tabelaResultado = consultarParcelasAberto(codigoEmpresa, fim, false)) {
            while (tabelaResultado.next()) {
                RecorrenciaClienteTO obj = new RecorrenciaClienteTO();
                obj.setMatricula(tabelaResultado.getString("matricula"));
                obj.setNomeCliente(tabelaResultado.getString("nome"));
                obj.setValor(tabelaResultado.getDouble("valor"));
                obj.setSituacaoCliente(tabelaResultado.getString("situacao"));
                obj.setDescricaoConvenio(tabelaResultado.getString("convenio"));
                obj.setDescricaoFormaPagamento(tabelaResultado.getString("formapagamento"));
                obj.setEmpresa(tabelaResultado.getString("nomeempresa"));
                verResultado.add(obj);
            }
        }
        return verResultado;
    }

    private ResultSet consultarParcelasAberto(Integer codigoEmpresa, Date fim, boolean count) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT\n");
        if (count) {
            sql.append("  count(mpar.codigo) AS qtd,\n");
            sql.append("  sum(mpar.valorparcela :: NUMERIC) AS total\n");
        } else {
            sql.append("cli.matricula               as matricula,\n");
            sql.append("cli.codigo                  as codcliente,\n");
            sql.append("pes.nome                    as nome,\n");
            sql.append("con.codigo                  as codcontrato,\n");
            sql.append("con.datalancamento          as datalancamento,\n");
            sql.append("con.dataprevistarenovar     as datavencimento,\n");
            sql.append("con.vigenciade              as vigenciade,\n");
            sql.append("con.vigenciaateajustada     as vigenciaateajustada,\n");
            sql.append("cd.numeromeses              as numeromeses\n");
        }
        sql.append("FROM movparcela mpar\n");
        sql.append("  LEFT JOIN contrato con ON mpar.contrato = con.codigo\n");
        sql.append("  LEFT JOIN contratocondicaopagamento cp ON cp.contrato = con.codigo\n");
        if (!count) {
            sql.append("  LEFT JOIN cliente cli ON cli.pessoa = con.pessoa\n");
            sql.append("  LEFT JOIN pessoa pes ON cli.pessoa = pes.codigo\n");
            sql.append("  LEFT JOIN contratoduracao cd ON con.codigo = cd.contrato\n");
        }
        sql.append("WHERE mpar.situacao = 'EA'\n");
        sql.append("      AND mpar.empresa = ").append(codigoEmpresa).append("\n");
        sql.append("      AND mpar.descricao LIKE 'PARCELA%'\n");
        sql.append("      AND datavencimento < '").append(Uteis.getDataJDBC(fim)).append("'\n");
        sql.append("      AND (mpar.regimerecorrencia = TRUE\n");
        sql.append("           OR con.regimerecorrencia = TRUE\n");
        sql.append("           OR con.codigo IN (SELECT\n");
        sql.append("                               contrato\n");
        sql.append("                             FROM contratocondicaopagamento ccp\n");
        sql.append("                               INNER JOIN condicaopagamento cp\n");
        sql.append("                                 ON ccp.condicaopagamento = cp.codigo\n");
        sql.append("                             WHERE cp.tipoconveniocobranca <> 0)\n");
        sql.append(")");

        PreparedStatement ps = getCon().prepareStatement(sql.toString());
        return ps.executeQuery();
    }

    public Integer contarOperacoesSuspeitas(Integer codigoEmpresa, String colunas, boolean count, String[] colunasCount) throws Exception {
        RemessaItem remessaItem = new RemessaItem(con);
        try (ResultSet tabelaResultado = remessaItem.consultarOperacoesSuspeitas(codigoEmpresa, colunas, count, colunasCount)) {
            if (tabelaResultado.next()) {
                return tabelaResultado.getInt("qtd");
            }
        }
        return 0;
    }

    public List<RecorrenciaClienteTO> consultarOperacoesSuspeitas(Integer codigoEmpresa, String colunas, boolean count, String[] colunasCount) throws Exception {
        List<RecorrenciaClienteTO> vetResultado = new ArrayList<RecorrenciaClienteTO>();
        RemessaItem remessaItem = new RemessaItem(con);
        try (ResultSet tabelaResultado = remessaItem.consultarOperacoesSuspeitas(codigoEmpresa, colunas, count, colunasCount)) {

            while (tabelaResultado.next()) {
                RecorrenciaClienteTO obj = new RecorrenciaClienteTO();
                obj.setMatricula(tabelaResultado.getString("matricula"));
                obj.setCodigoCliente(tabelaResultado.getInt("codcliente"));
                obj.setNomeCliente(tabelaResultado.getString("nome"));
                obj.setCodigoContrato(tabelaResultado.getInt("codcontrato"));
                obj.setDataLancamento(tabelaResultado.getDate("datalancamento"));
                obj.setDataVencimento(tabelaResultado.getDate("datavencimento"));
                obj.setDataInicio(tabelaResultado.getDate("vigenciade"));
                obj.setDataTermino(tabelaResultado.getDate("vigenciaateajustada"));
                obj.setDuracao(tabelaResultado.getInt("numeromeses"));
                obj.setCpf(tabelaResultado.getString("cpf"));
                obj.setSuspeita((obj.getCodigoContrato() > 0) ? "CANCELAMENTO DE PARCELA AUTORIZADA" : "ESTORNO DE PARCELA/CONTRATO AUTORIZADO");
                vetResultado.add(obj);
            }
        }
        return vetResultado;
    }

    public Integer contarPendenciaClienteCartaoAVencer(Integer codigoEmpresa, Date dataFim, List<Integer> consultores) throws Exception {
        try (ResultSet rs = sqlPendenciaClienteCartaoAVencer(codigoEmpresa, "COUNT (distinct a.codigo) as total", dataFim, consultores)) {
            if (rs.next()) {
                return rs.getInt("total");
            }
        }
        return 0;
    }

    public ResultSet consultarPendenciaClienteCartaoAVencer(Integer codigoEmpresa, Date dataBaseInicio, Date dataFim, List<Integer> consultores,ConfPaginacao paginacao) throws Exception {

        StringBuilder sql = new StringBuilder();
        sql.append("SELECT \n");
        sql.append("distinct dw.codigocliente as cli , dw.codigocontrato as codContrato, dw.nomecliente as nome ,dw.nomePlano ,dw.codigopessoa as codPessoa, dw.matricula as matriculacli,dw.situacao as situacaoCliente, \n");
        sql.append("dw.datavigenciade as dataInicio,dw.datavigenciaateajustada as dataFim,cd.numeroMeses as duracaoContrato,ct.nomemodalidades,dw.telefonescliente \n");
        sql.append("FROM autorizacaocobrancacliente a \n");
        sql.append("INNER JOIN cliente c ON a.cliente = c.codigo  \n");
        sql.append("INNER JOIN Situacaoclientesinteticodw dw ON dw.codigocliente = a.cliente \n");
        sql.append("LEFT JOIN Contrato ct ON  ct.codigo = dw.codigocontrato \n");
        sql.append("LEFT JOIN ContratoDuracao cd ON cd.contrato = ct.codigo \n");
        sql.append("WHERE a.ativa and a.validadecartao = '").append(Uteis.getDataMesAnoConcatenado(dataFim)).append("'");
        sql.append("AND (c.situacao = 'AT' or exists (select codigo from movparcela where situacao = 'EA' and pessoa = c.pessoa)) \n");
        if(codigoEmpresa != 0){
            sql.append(" AND c.empresa in (").append(codigoEmpresa).append(") \n");
        }
        if(paginacao != null && paginacao.getOrdernar()){
            sql.append(" ORDER BY ").append(paginacao.getColunaOrdenacao()).append(" ").append(paginacao.getDirecaoOrdenacao());
        }
        if(paginacao != null && paginacao.isExistePaginacao()){
            sql.append(" limit ").append(paginacao.getItensPorPagina())
                    .append(" offset ").append((paginacao.getPaginaAtual() * paginacao.getItensPorPagina()));
        }
        return criarConsulta(sql.toString(), con);
    }

    private ResultSet sqlPendenciaClienteCartaoAVencer(Integer codigoEmpresa, String colunas, Date dataFim, List<Integer> consultores) throws Exception {
        StringBuilder joinCondicaoColab = new StringBuilder();
        StringBuilder condicaoColab = new StringBuilder();
        if (consultores != null && !consultores.isEmpty()) {
            joinCondicaoColab.append("LEFT JOIN vinculo v ON c.codigo = v.cliente ");
            condicaoColab.append(" AND v.colaborador in (");
            for (Integer consultor : consultores) {
                condicaoColab.append(consultor).append(",");
            }
            condicaoColab.deleteCharAt(condicaoColab.length() - 1);
            condicaoColab.append(")");
        }

        String sqlEmpresa = getStringEmpresa(codigoEmpresa);

        String sqlStr = "SELECT " + colunas + " FROM autorizacaocobrancacliente a "
                + "INNER JOIN cliente c ON a.cliente = c.codigo " + sqlEmpresa + "\n"
                + joinCondicaoColab.toString() + "\n"
                + "WHERE a.ativa and a.validadecartao = '" + Uteis.getDataMesAnoConcatenado(dataFim) + "'\n"
                + condicaoColab.toString()
                + " and (c.situacao = 'AT' or exists (select codigo from movparcela where situacao = 'EA' and pessoa = c.pessoa))";
        return criarConsulta(sqlStr, con);
    }

    public Integer contarPendenciaClientesMesmoCartao(Integer codigoEmpresa, Date dataBaseInicio, Date dataFim, List<Integer> consultores,boolean somenteAtivos) throws Exception {
        try (ResultSet rs = consultarPendenciaClientesMesmoCartao(codigoEmpresa, true, dataBaseInicio, dataFim, null, null, somenteAtivos, null)) {
            if (rs.next()) {
                return rs.getInt("total");
            }
        }
        return 0;
    }

    public ResultSet consultarPendenciaClientesMesmoCartao(Integer codigoEmpresa, boolean count, Date dataBaseInicio, Date dataFim, String nomeCliente, List<Integer> consultores,Boolean somenteAtivos,ConfPaginacao paginacao) throws Exception {

        StringBuilder joinCondicaoColab = new StringBuilder();
        StringBuilder condicaoColab = new StringBuilder();
        if (consultores != null && !consultores.isEmpty()) {
            joinCondicaoColab.append("INNER JOIN vinculo v ON v.cliente = a.cliente ");
            condicaoColab.append(" AND v.colaborador in (");
            for (Integer consultor : consultores) {
                condicaoColab.append(consultor).append(",");
            }
            condicaoColab.deleteCharAt(condicaoColab.length() - 1);
            condicaoColab.append(")");
        }
        StringBuilder sqlStr = new StringBuilder();

        if (count){
            sqlStr.append("select count(*) as total from( \n");
        }

        sqlStr.append("SELECT distinct dw.codigocliente as cli , dw.codigocontrato as codContrato, a.cartaomascaradointerno as autorizacao, \n");
        sqlStr.append("dw.nomecliente as nome ,dw.nomePlano ,dw.codigopessoa as codPessoa, dw.matricula as matriculacli,dw.situacao as situacaoCliente, \n");
        sqlStr.append("dw.datavigenciade as dataInicio,dw.datavigenciaateajustada as dataFim,cd.numeroMeses as duracaoContrato,ct.nomemodalidades,dw.telefonescliente\n");
        sqlStr.append(",emp.nome as nomeEmpresaCliente \n");
        if(!UteisValidacao.emptyString(nomeCliente)){
            sqlStr.append(", dw.nomecliente LIKE '%").append(nomeCliente).append("%' as nomeLike ");
        }
        sqlStr.append("FROM autorizacaocobrancacliente a \n");
        sqlStr.append(joinCondicaoColab.toString() + "\n");
        sqlStr.append("INNER JOIN Situacaoclientesinteticodw dw ON dw.codigocliente = a.cliente \n");
        sqlStr.append("INNER JOIN empresa emp on emp.codigo = dw.empresacliente \n");

        if (!UteisValidacao.emptyString(nomeCliente)){
            sqlStr.append("INNER JOIN  autorizacaocobrancacliente a3 ON a3.cartaomascaradointerno = a.cartaomascaradointerno \n");
            sqlStr.append("INNER JOIN Situacaoclientesinteticodw dw2 ON dw2.codigocliente = a3.cliente ");
            sqlStr.append("AND dw2.nomecliente ILIKE '%").append(nomeCliente).append("%' \n");
        }
        sqlStr.append("LEFT JOIN Contrato ct ON  ct.codigo = dw.codigocontrato \n");
        sqlStr.append("LEFT JOIN ContratoDuracao cd ON cd.contrato = ct.codigo \n");
        sqlStr.append("WHERE 1=1 ");



        sqlStr.append(" and a.ativa = true");
        sqlStr.append(" and a.cartaomascaradointerno in (\n");
        sqlStr.append("                                    select a.cartaomascaradointerno\n");
        sqlStr.append("                                      from autorizacaocobrancacliente a\n");
        sqlStr.append("                                     inner join Situacaoclientesinteticodw dw on\tdw.codigocliente = a.cliente\n");
        sqlStr.append("                                     inner join cliente c on c.codigo = a.cliente\n");
        sqlStr.append("                                     inner join pessoa p on p.codigo = c.pessoa\n");
        sqlStr.append("                                     where a.ativa = true\n");
        sqlStr.append("                                       and a.cartaomascaradointerno is not null \n");
        sqlStr.append("                                       and a.cartaomascaradointerno != ''\n");
        sqlStr.append("                                       and a.cartaomascaradointerno in (select cartaomascaradointerno\n");
        sqlStr.append("                                                                          from autorizacaocobrancacliente\n");
        sqlStr.append("                                                                         where ativa = true\n");
        sqlStr.append("                                                                           and cartaomascaradointerno in (select cartaomascaradointerno\n");
        sqlStr.append("                                                                                                            from autorizacaocobrancacliente\n");
        sqlStr.append("                                                                                                           where ativa = true)\n");
        sqlStr.append("                                                                         group by cartaomascaradointerno\n");
        sqlStr.append("                                                                        having count(*) > 1))\n");
        if(codigoEmpresa != 0) {
            sqlStr.append("                                                                             and dw.empresacliente = ").append(codigoEmpresa);
        }


        if(somenteAtivos) {
            sqlStr.append(" AND dw.situacao in ('AT')\n");
        }

        sqlStr.append(condicaoColab.toString() + "\n");
        sqlStr.append(" ORDER BY ");
        if(!UteisValidacao.emptyString(nomeCliente)){
            sqlStr.append("autorizacao, nomeLike desc, nome");
        }else{
            if(paginacao != null && paginacao.getOrdernar()){
                if(paginacao.getColunaOrdenacao().equals("autorizacao")){
                    sqlStr.append("autorizacao, nome");
                }else{
                    sqlStr.append(paginacao.getColunaOrdenacao()).append(" ").append(paginacao.getDirecaoOrdenacao());
                }
            }else{
                sqlStr.append("autorizacao, nome");
            }
        }
        if(paginacao != null && paginacao.isExistePaginacao()){
            sqlStr.append(" limit ").append(paginacao.getItensPorPagina())
                    .append(" offset ").append((paginacao.getPaginaAtual() * paginacao.getItensPorPagina()));
        }

        if (count){
            sqlStr.append(") as t");
        }
        return criarConsulta(sqlStr.toString(), con);
    }

    public void alterarDiaVencimento(Integer dia, Integer contrato, String usuarioResponsavel) throws Exception{
        ContratoRecorrencia contratoRecorrenciaDAO;
        Log logDAO;
        try {
            contratoRecorrenciaDAO = new ContratoRecorrencia(con);
            logDAO = new Log(con);

            ContratoRecorrenciaVO contratoRecorrenciaVO = contratoRecorrenciaDAO.consultarPorContrato(contrato, Uteis.NIVELMONTARDADOS_MINIMOS);
            executarConsultaUpdate("update contratorecorrencia set diavencimentocartao = " + dia + " where contrato = " + contrato, con);
            LogVO log = new LogVO();
            log.setNomeEntidade("CONTRATO");
            log.setNomeEntidadeDescricao("CONTRATO RECORRÊNCIA");
            log.setNomeCampo("diaVencimentoCartao");
            log.setValorCampoAnterior(contratoRecorrenciaVO.getDiaVencimentoCartao().toString());
            log.setValorCampoAlterado(dia.toString());
            log.setChavePrimaria(contrato.toString());
            log.setDataAlteracao(Calendario.hoje());
            log.setResponsavelAlteracao(usuarioResponsavel);
            log.setPessoa(contratoRecorrenciaVO.getPessoa().getCodigo());
            log.setOperacao("ALTERACAO DE DATA DE VENCIMENTO CARTÃO");
            logDAO.incluirSemCommit(log);
        } finally {
            contratoRecorrenciaDAO = null;
            logDAO = null;
        }
    }

    public List<MovParcelaVO> alterarDataVencimentoParcelasMensalidade(ContratoVO contrato, int novoDiaVencimento, UsuarioVO usuario, Double valorProRata, int diferencaDiasNovoVencimento, boolean liberarValorProRata, boolean alterarMesProximaParcela) throws Exception {
        MovParcela movParcelaDAO;
        MovProduto movProdutoDAO;
        Produto produtoDAO;
        ContratoRecorrencia contratoRecorrenciaDAO;
        try {
            con.setAutoCommit(false);
            movParcelaDAO = new MovParcela(con);
            movProdutoDAO = new MovProduto(con);
            produtoDAO = new Produto(con);
            contratoRecorrenciaDAO = new ContratoRecorrencia(con);

            Date dataVencimento = Calendario.somarDias(Calendario.hoje(), 1);
            List<MovParcelaVO> parcelasEmAberto = movParcelaDAO.consultarMensalidadesEmAbertoPorContrato(contrato.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS, dataVencimento);

            if (parcelasEmAberto.size() > 0) {
                MovParcelaVO proximaParcela = parcelasEmAberto.get(0);

                int diaVecimentoParcelaAtual = proximaParcela.getDiaVencimento();

                Date novaDataVencimentoParcela = Calendario.setDiaMes(proximaParcela.getDataVencimento(), novoDiaVencimento);
                if (alterarMesProximaParcela){
                    novaDataVencimentoParcela = Calendario.somarMeses(novaDataVencimentoParcela, 1);
                }
                Date inicioVigenciaProdutoProRata = proximaParcela.getDataVencimento();
                Date fimVigenciaProdutoProRata = Calendario.subtrairDias(novaDataVencimentoParcela, 1);
                int anoReferenciaProdutoProRata = Calendario.getAno(novaDataVencimentoParcela);
                String mesReferenciaProdutoProRata = Calendario.getData(novaDataVencimentoParcela, "MM/yyyy");

                String descricao = "PRO RATA - ALTERAÇÃO VENCIMENTO DE " + diaVecimentoParcelaAtual + " PARA " + novoDiaVencimento;

                if (diferencaDiasNovoVencimento > 0) {

                    if (liberarValorProRata) {
                        valorProRata = 0.0;
                    }

                    MovProdutoVO produtoProRata = new MovProdutoVO();
                    if (UteisValidacao.emptyNumber(valorProRata)) {
                        produtoProRata.setSituacao("PG");
                    } else {
                        produtoProRata.setSituacao("EA");
                    }
                    produtoProRata.setDescricao(descricao);
                    produtoProRata.setDataInicioVigencia(inicioVigenciaProdutoProRata);
                    produtoProRata.setDataFinalVigencia(fimVigenciaProdutoProRata);
                    produtoProRata.setAnoReferencia(anoReferenciaProdutoProRata);
                    produtoProRata.setResponsavelLancamento(usuario);
                    produtoProRata.setDataLancamento(Calendario.hoje());
                    produtoProRata.setTotalFinal(valorProRata);
                    produtoProRata.setPrecoUnitario(valorProRata);
                    produtoProRata.setQuantidade(1);
                    produtoProRata.setEmpresa(contrato.getEmpresa());
                    produtoProRata.setPessoa(contrato.getPessoa());
                    produtoProRata.setContrato(contrato);
                    produtoProRata.setProduto(produtoDAO.obterProdutoPadraoProRata());
                    produtoProRata.setMesReferencia(mesReferenciaProdutoProRata);
                    produtoProRata.setAnoReferencia(anoReferenciaProdutoProRata);
                    movProdutoDAO.incluirSemCommit(produtoProRata);

                    MovProdutoParcelaVO relacionamentoProdutoParcela = new MovProdutoParcelaVO();
                    relacionamentoProdutoParcela.setMovProduto(produtoProRata.getCodigo());
                    relacionamentoProdutoParcela.setValorPago(valorProRata);

                    MovParcelaVO parcelaProRata = new MovParcelaVO();
                    parcelaProRata.setDataVencimento(novaDataVencimentoParcela);
                    parcelaProRata.setDescricao("PARCELA " + descricao);
                    parcelaProRata.setResponsavel(usuario);
                    parcelaProRata.setContrato(contrato);
                    parcelaProRata.setValorParcela(valorProRata);
                    if (UteisValidacao.emptyNumber(valorProRata)) {
                        parcelaProRata.setSituacao("PG");
                    } else {
                        parcelaProRata.setSituacao("EA");
                    }
                    parcelaProRata.setDataRegistro(Calendario.hoje());
                    parcelaProRata.setEmpresa(contrato.getEmpresa());
                    parcelaProRata.setPessoa(contrato.getPessoa());
                    parcelaProRata.getMovProdutoParcelaVOs().add(relacionamentoProdutoParcela);
                    movParcelaDAO.incluir(parcelaProRata, false);

                }

                for (MovParcelaVO parcela : parcelasEmAberto) {
                    parcela.setDataVencimento(Calendario.setDiaMes(parcela.getDataVencimento(), novoDiaVencimento));
                }

                List<MovParcelaVO> parcelasSemAlteracaoVencimento = movParcelaDAO.consultarMensalidadesEmAbertoPorContrato(contrato.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS, dataVencimento);
                movParcelaDAO.alterarVencimentoListaParcelas(parcelasEmAberto, parcelasSemAlteracaoVencimento, false, true, usuario.getNome(), "Tela do Cliente", false);
            }
            contratoRecorrenciaDAO.alterarDiaVencimento(novoDiaVencimento, contrato.getCodigo(), usuario.getNome());
            con.commit();

            return parcelasEmAberto;
        }catch (Exception e){
            con.rollback();
            throw e;
        } finally {
            con.setAutoCommit(true);
            movParcelaDAO = null;
            movProdutoDAO = null;
            produtoDAO = null;
            contratoRecorrenciaDAO = null;
        }
    }

    public boolean cancelamentoProporcionalContrato(int codigoContrato) throws Exception {

        String sql = "select cancelamentoproporcional from contratorecorrencia where contrato = "
                + codigoContrato + " and dataInutilizada is null";
        try (ResultSet rs = ContratoRecorrencia.criarConsulta(sql, con)) {
            if (rs.next()) {
                return rs.getBoolean("cancelamentoproporcional");
            }else {
                return false;
            }
        }

    }

}
