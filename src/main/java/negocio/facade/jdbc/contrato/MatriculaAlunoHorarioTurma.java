package negocio.facade.jdbc.contrato;

import br.com.pactosolucoes.agendatotal.json.AgendaTotalJSON;
import br.com.pactosolucoes.agendatotal.json.AgendadoJSON;
import br.com.pactosolucoes.ce.comuns.enumerador.DiaSemana;
import br.com.pactosolucoes.mapaturmas.modelo.AlunoMapaTurmasTO;
import negocio.comuns.arquitetura.LogVO;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.AgendaTotalTO;
import negocio.comuns.basico.AulaDesmarcadaVO;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.basico.PessoaVO;
import negocio.comuns.basico.ReposicaoVO;
import negocio.comuns.contrato.ContratoDuracaoVO;
import negocio.comuns.contrato.ContratoVO;
import negocio.comuns.contrato.MatriculaAlunoHorarioTurmaVO;
import negocio.comuns.plano.ConsultarAlunosTurmaVO;
import negocio.comuns.plano.HorarioTurmaVO;
import negocio.comuns.plano.ModalidadeVO;
import negocio.comuns.plano.TurmaVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.comuns.utilitarias.UtilReflection;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.basico.AulaDesmarcada;
import negocio.facade.jdbc.basico.Cliente;
import negocio.facade.jdbc.basico.Empresa;
import negocio.facade.jdbc.basico.Pessoa;
import negocio.facade.jdbc.basico.Reposicao;
import negocio.facade.jdbc.crm.Feriado;
import negocio.facade.jdbc.plano.HorarioTurma;
import negocio.facade.jdbc.plano.Modalidade;
import negocio.facade.jdbc.plano.Turma;
import negocio.interfaces.basico.AulaDesmarcadaInterfaceFacade;
import negocio.interfaces.basico.ClienteInterfaceFacade;
import negocio.interfaces.basico.EmpresaInterfaceFacade;
import negocio.interfaces.basico.PessoaInterfaceFacade;
import negocio.interfaces.basico.ReposicaoInterfaceFacade;
import negocio.interfaces.contrato.ContratoInterfaceFacade;
import negocio.interfaces.contrato.MatriculaAlunoHorarioTurmaInterfaceFacade;
import negocio.interfaces.crm.FeriadoInterfaceFacade;
import negocio.interfaces.plano.HorarioTurmaInterfaceFacade;
import negocio.interfaces.plano.ModalidadeInterfaceFacade;
import org.apache.commons.lang.SerializationUtils;
import servicos.propriedades.PropsService;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.logging.Level;
import java.util.logging.Logger;

import static org.apache.commons.lang3.StringUtils.isBlank;
import static org.apache.commons.lang3.StringUtils.isNotBlank;

/**
 * 
 * <AUTHOR> Lhoji Shiozawa
 */
public class MatriculaAlunoHorarioTurma extends SuperEntidade implements MatriculaAlunoHorarioTurmaInterfaceFacade {

    private Boolean somarTurmasRetiradasDoContrato = false;
    private Integer codigoModalidade = 0;
    
    public MatriculaAlunoHorarioTurma() throws Exception {
        super();
        setIdEntidade("Contrato");
    }

    public MatriculaAlunoHorarioTurma(Connection conexao) throws Exception {
        super(conexao);
        setIdEntidade("Contrato");
    }

    @Override
    public void incluir(MatriculaAlunoHorarioTurmaVO obj) throws Exception {
        try {
            con.setAutoCommit(false);
            incluirSemComit(obj);
            con.commit();
        } catch (Exception e) {
            obj.setNovoObj(true);
            con.rollback();
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }


    public Date pesquisarDataFimMatriculaTurmaCreditoTreino(Integer codigoContrato, Integer codigoHorarioTurma)throws Exception{
        /*
           Os contratos de venda de credito treino - Horário da Turma não ocupam vaga até a vigência final do contrato, e sim somente até a última data possível de utilização.
         */
        StringBuilder sql = new StringBuilder();
        sql.append("select * \n");
        sql.append("from MatriculaAlunoHorarioTurma  \n");
        sql.append("where contrato = ").append(codigoContrato).append(" and horarioTurma = ").append(codigoHorarioTurma).append(" \n");
        sql.append("order by dataFim desc limit 1  \n");
        try (Statement st = con.createStatement()) {
            try (ResultSet rs = st.executeQuery(sql.toString())) {
                if (rs.next()) {
                    return rs.getDate("dataFim");
                }
            }
        }
        return null;
    }

    @Override
    public List<MatriculaAlunoHorarioTurmaVO> consultarMatriculaPorDataFim(int codigoContrato, Date dataFim) throws Exception {
        String sql = "SELECT * FROM MatriculaAlunoHorarioTurma "
                + "WHERE contrato = " + codigoContrato + " and datafim = '" + dataFim + "'";
        try (Statement sqlConsultar = con.createStatement()) {
            try (ResultSet tabelaResultado = sqlConsultar.executeQuery(sql)) {
                return montarDadosConsulta(tabelaResultado, Uteis.NIVELMONTARDADOS_MINIMOS, getCon());
            }
        }
    }

    public void alterarDataFimMatricula(ContratoVO contratoVO, HorarioTurmaVO horarioTurma, Date dataFimMatriculaPesquisar, Date dataFimMatriculaAlterar)throws Exception{
        StringBuilder sql = new StringBuilder();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        sql.append("update MatriculaAlunoHorarioTurma set dataFim = '").append(sdf.format(dataFimMatriculaAlterar)).append("' ");
        sql.append("where contrato = ").append(contratoVO.getCodigo()).append(" and horarioTurma = ").append(horarioTurma.getCodigo());
        sql.append(" and dataFim = '").append(sdf.format(dataFimMatriculaPesquisar)).append("' ");
        executarConsulta(sql.toString(), con);
    }

    @Override
    public void incluirSemComit(MatriculaAlunoHorarioTurmaVO obj) throws Exception {
        obj.validarDados();
        String sql = "INSERT INTO MatriculaAlunoHorarioTurma( "
                + "empresa, pessoa, contrato, dataInicio, "
                + "dataFim, horarioturma ) "
                + "VALUES ( ?, ?, ?, ?, ?, ? )";
        try (PreparedStatement sqlInserir = con.prepareStatement(sql)) {
            sqlInserir.setInt(1, obj.getEmpresa());
            sqlInserir.setInt(2, obj.getPessoa().getCodigo().intValue());
            sqlInserir.setInt(3, obj.getContrato().getCodigo().intValue());
            sqlInserir.setDate(4, Uteis.getDataJDBC(obj.getDataInicio()));
            sqlInserir.setDate(5, Uteis.getDataJDBC(obj.getDataFim()));
            sqlInserir.setInt(6, obj.getHorarioTurma().getCodigo().intValue());

            sqlInserir.execute();
        }
        obj.setCodigo(obterValorChavePrimariaCodigo());
        obj.setNovoObj(false);
    }

    @Override
    public void alterar(MatriculaAlunoHorarioTurmaVO obj) throws Exception {
        try {
            con.setAutoCommit(false);
            alterarSemCommit(obj);
            con.commit();
        } catch (Exception e) {
            con.rollback();
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    @Override
    public void alterarSemCommit(MatriculaAlunoHorarioTurmaVO obj) throws Exception {
        obj.validarDados();
        String sql = "UPDATE MatriculaAlunoHorarioTurma SET datafim=? WHERE codigo = ?";
        try (PreparedStatement sqlAlterar = con.prepareStatement(sql)) {
            sqlAlterar.setDate(1, Uteis.getDataJDBC(obj.getDataFim()));
            sqlAlterar.setInt(2, obj.getCodigo());
            sqlAlterar.execute();
        }
    }

    @Override
    public void alterarVigenciaSemCommit(MatriculaAlunoHorarioTurmaVO obj) throws Exception {
        obj.validarDados();
        String sql = "UPDATE MatriculaAlunoHorarioTurma SET dataInicio=?, datafim=? WHERE codigo = ?";
        try (PreparedStatement sqlAlterar = con.prepareStatement(sql)) {
            sqlAlterar.setDate(1, Uteis.getDataJDBC(obj.getDataInicio()));
            sqlAlterar.setDate(2, Uteis.getDataJDBC(obj.getDataFim()));
            sqlAlterar.setInt(3, obj.getCodigo());
            sqlAlterar.execute();
        }
    }


    @Override
    public void alterarInicioFimMatricula(MatriculaAlunoHorarioTurmaVO obj) throws Exception {
        try {
            con.setAutoCommit(false);
            alterarSemCommit(obj);
            con.commit();
        } catch (Exception e) {
            con.rollback();
            throw e;
        } finally {
            con.setAutoCommit(true);

        }
    }

    @Override
    public void alterarInicioFimMatriculaSemCommit(MatriculaAlunoHorarioTurmaVO obj) throws Exception {
        obj.validarDados();
        String sql = "UPDATE MatriculaAlunoHorarioTurma SET datainicio=?, datafim=? WHERE codigo = ?";
        try (PreparedStatement sqlAlterar = con.prepareStatement(sql)) {
            sqlAlterar.setDate(1, Uteis.getDataJDBC(obj.getDataInicio()));
            sqlAlterar.setDate(2, Uteis.getDataJDBC(obj.getDataFim()));
            sqlAlterar.setInt(3, obj.getCodigo().intValue());
            sqlAlterar.execute();
        }

    }

    @Override
    public void excluir(MatriculaAlunoHorarioTurmaVO obj) throws Exception {
        try {
            con.setAutoCommit(false);
            excluirSemCommit(obj);
            con.commit();
        } catch (Exception e) {
            con.rollback();
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    @Override
    public void excluirSemCommit(MatriculaAlunoHorarioTurmaVO obj) throws Exception {
        String sql = "DELETE FROM MatriculaAlunoHorarioTurma WHERE codigo = ?";
        try (PreparedStatement sqlExcluir = con.prepareStatement(sql)) {
            sqlExcluir.setInt(1, obj.getCodigo().intValue());
            sqlExcluir.execute();
        }
    }

    @Override
    public MatriculaAlunoHorarioTurmaVO consultarPorChavePrimaria(int codigoPrm, int nivelMontarDados) throws Exception {
        String sql = "SELECT matriculaalunohorarioturma.* FROM MatriculaAlunoHorarioTurma WHERE codigo = ?";
        try (PreparedStatement sqlConsultar = con.prepareStatement(sql)) {
            sqlConsultar.setInt(1, codigoPrm);
            try (ResultSet tabelaResultado = sqlConsultar.executeQuery()) {
                if (!tabelaResultado.next()) {
                    throw new ConsistirException("Dados Não Encontrados ( MatriculaAlunoHorarioTurma ).");
                }
                return montarDados(tabelaResultado, nivelMontarDados, getCon());
            }
        }
    }

    private String sqlPeriodo(Date inicio, Date fim) throws Exception {
        if (inicio == null && fim == null) {
            return "";
        }
        if (fim == null) {
            return " AND datainicio >= '" + Uteis.getDataJDBC(inicio) + "' ";
        }
        return "AND ((datafim >= '" + Uteis.getDataJDBC(inicio) + "' AND datafim <= '" + Uteis.getDataJDBC(fim) + "') OR "
                + "(datainicio >= '" + Uteis.getDataJDBC(inicio) + "' AND datainicio <= '" + Uteis.getDataJDBC(fim) + "') OR "
                + "(datainicio < '" + Uteis.getDataJDBC(inicio) + "' AND datafim > '" + Uteis.getDataJDBC(fim) + "')) ";
    }

    @Override
    public List<MatriculaAlunoHorarioTurmaVO> consultarPorCodigo(int valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        return consultarPorCodigo(valorConsulta, negocio.comuns.utilitarias.Calendario.hoje(), negocio.comuns.utilitarias.Calendario.hoje(), controlarAcesso, nivelMontarDados);
    }

    @Override
    public List<MatriculaAlunoHorarioTurmaVO> consultarPorCodigo(int valorConsulta, Date dataInicial, Date dataFinal, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        String sqlStr = "SELECT matriculaalunohorarioturma.* FROM MatriculaAlunoHorarioTurma "
                + "WHERE codigo >= ? " + sqlPeriodo(dataInicial, dataFinal)
                + "ORDER BY codigo";
        try (PreparedStatement stm = con.prepareStatement(sqlStr)) {
            stm.setInt(1, valorConsulta);
            try (ResultSet tabelaResultado = stm.executeQuery()) {
                return montarDadosConsulta(tabelaResultado, nivelMontarDados, getCon());
            }
        }
    }

    @Override
    public List<MatriculaAlunoHorarioTurmaVO> consultarPorNomePessoa(String valorConsulta, int nivelMontarDados) throws Exception {
        return consultarPorNomePessoa(valorConsulta, negocio.comuns.utilitarias.Calendario.hoje(), negocio.comuns.utilitarias.Calendario.hoje(), nivelMontarDados);
    }

    @Override
    public List<MatriculaAlunoHorarioTurmaVO> consultarPorNomePessoa(String valorConsulta, Date dataInicial, Date dataFinal, int nivelMontarDados) throws Exception {
        String sqlStr = "SELECT matriculaalunohorarioturma.* FROM MatriculaAlunoHorarioTurma "
                + "INNER JOIN pessoa ON matriculaalunohorarioturma.pessoa = pessoa.codigo "
                + "WHERE upper( pessoa.nome ) like('" + valorConsulta.toUpperCase() + "%') "
                + sqlPeriodo(dataInicial, dataFinal)
                + "ORDER BY pessoa.nome";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return montarDadosConsulta(tabelaResultado, nivelMontarDados, getCon());
            }
        }
    }

    @Override
    public List<MatriculaAlunoHorarioTurmaVO> consultarPorIdentificadorTurma(String valorConsulta, int nivelMontarDados) throws Exception {
        return consultarPorIdentificadorTurma(valorConsulta, negocio.comuns.utilitarias.Calendario.hoje(), negocio.comuns.utilitarias.Calendario.hoje(), nivelMontarDados);
    }

    @Override
    public List<MatriculaAlunoHorarioTurmaVO> consultarPorIdentificadorTurma(String valorConsulta, Date dataInicial, Date dataFinal, int nivelMontarDados) throws Exception {
        String sqlStr = "SELECT matriculaalunohorarioturma.* FROM MatriculaAlunoHorarioTurma "
                + "INNER JOIN horarioturma ON horarioturma.codigo = matriculaalunohorarioturma.horarioturma "
                + "INNER JOIN turma ON turma.codigo = horarioturma.turma "
                + "WHERE upper( turma.identificador ) like('" + valorConsulta.toUpperCase() + "%') "
                + sqlPeriodo(dataInicial, dataFinal)
                + "ORDER BY turma.identificador";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return montarDadosConsulta(tabelaResultado, nivelMontarDados, getCon());
            }
        }
    }

    @Override
    public List<MatriculaAlunoHorarioTurmaVO> consultarPorSituacaoContrato(String valorConsulta, int nivelMontarDados) throws Exception {
        return consultarPorSituacaoContrato(valorConsulta, negocio.comuns.utilitarias.Calendario.hoje(), negocio.comuns.utilitarias.Calendario.hoje(), nivelMontarDados);
    }

    @Override
    public List<MatriculaAlunoHorarioTurmaVO> consultarPorSituacaoContrato(String valorConsulta, Date dataInicial, Date dataFinal, int nivelMontarDados) throws Exception {
        String sqlStr = "SELECT matriculaalunohorarioturma.* FROM MatriculaAlunoHorarioTurma "
                + "INNER JOIN contrato ON matriculaalunohorarioturma.contrato = contrato.codigo "
                + "WHERE upper( contrato.situacao ) like('" + valorConsulta.toUpperCase() + "%') "
                + sqlPeriodo(dataInicial, dataFinal)
                + "ORDER BY contrato.situacao";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return montarDadosConsulta(tabelaResultado, nivelMontarDados, getCon());
            }
        }
    }

    @Override
    public List<MatriculaAlunoHorarioTurmaVO> consultarPorCodigoContrato(int valorConsulta, int nivelMontarDados, Boolean somarTurmasRetiradasDoContrato,Integer codigoTurma) throws Exception {
        this.somarTurmasRetiradasDoContrato = somarTurmasRetiradasDoContrato;
        Modalidade modalidade = new Modalidade(con);
        ModalidadeVO modalidadeVO = modalidade.consultarPorTurma(codigoTurma,nivelMontarDados);
        this.codigoModalidade = modalidadeVO.getCodigo();
        return consultarPorCodigoContrato(valorConsulta, negocio.comuns.utilitarias.Calendario.hoje(), negocio.comuns.utilitarias.Calendario.hoje(), nivelMontarDados, null);
    }
    
    @Override
    public List<MatriculaAlunoHorarioTurmaVO> consultarPorCodigoContrato(int valorConsulta, int nivelMontarDados) throws Exception {
        return consultarPorCodigoContrato(valorConsulta, negocio.comuns.utilitarias.Calendario.hoje(), negocio.comuns.utilitarias.Calendario.hoje(), nivelMontarDados, null);
    }

    @Override
    public List<MatriculaAlunoHorarioTurmaVO> consultarPorCodigoContrato(int valorConsulta, Date dataInicial, Date dataFinal,
            int nivelMontarDados, Boolean desconsiderarDataTurmas) throws Exception {
        StringBuilder sqlStr = new StringBuilder();
        sqlStr.append("SELECT matriculaalunohorarioturma.* FROM MatriculaAlunoHorarioTurma \n");
        sqlStr.append("INNER JOIN contrato ON matriculaalunohorarioturma.contrato = contrato.codigo \n");
        if (somarTurmasRetiradasDoContrato){
            sqlStr.append("INNER JOIN horarioturma ht ON ht.codigo =  matriculaalunohorarioturma.horarioturma \n");
            sqlStr.append("INNER JOIN turma tu ON tu.codigo = ht.turma \n");
            sqlStr.append("INNER JOIN modalidade mo ON mo.codigo = tu.modalidade \n");
        }
        sqlStr.append("WHERE contrato.codigo = ").append(valorConsulta);
        if (somarTurmasRetiradasDoContrato){
            sqlStr.append("AND  mo.codigo = ").append(this.codigoModalidade);
        }
        if (desconsiderarDataTurmas != null && desconsiderarDataTurmas) {
            sqlStr.append(" and horarioturma IN( \n");
            sqlStr.append(" SELECT cmht.horarioturma FROM contratomodalidadehorarioturma  cmht \n");
            sqlStr.append(" INNER JOIN contratomodalidadeturma cmt ON cmt.codigo = cmht.contratomodalidadeturma \n");
            sqlStr.append(" INNER JOIN contratomodalidade cm ON cm.codigo = cmt.contratomodalidade \n");
            sqlStr.append(" where cm.contrato = ").append(valorConsulta).append(") ");
        }else{
            if (!somarTurmasRetiradasDoContrato) {
                sqlStr.append(sqlPeriodo(dataInicial, dataFinal));
            }
        }
        sqlStr.append(" ORDER BY MatriculaAlunoHorarioTurma.codigo");
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr.toString())) {
                return montarDadosConsulta(tabelaResultado, nivelMontarDados, getCon());
            }
        }
    }

    public List<MatriculaAlunoHorarioTurmaVO> consultarPorContratos(Set<Integer> contratos) throws Exception {
        if (contratos.isEmpty()) {
            return new ArrayList<MatriculaAlunoHorarioTurmaVO>();
        }
        StringBuilder codigos = new StringBuilder();
        for (Integer cod : contratos) {
            codigos.append(",").append(cod);
        }

        String sqlStr = "SELECT horarioturma.diasemana, horarioturma.professor, horarioturma.horainicial, horarioturma.horafinal, "
                + "horarioturma.identificadorturma, horarioturma.turma, "
                + " matriculaalunohorarioturma.* FROM MatriculaAlunoHorarioTurma "
                + "INNER JOIN contrato ON matriculaalunohorarioturma.contrato = contrato.codigo "
                + "INNER JOIN horarioturma ON matriculaalunohorarioturma.horarioturma = horarioturma.codigo "
                + "WHERE contrato.codigo IN (" + codigos.toString().replaceFirst(",", "") + ") "
                + "ORDER BY MatriculaAlunoHorarioTurma.codigo";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return montarDadosConsulta(tabelaResultado, Uteis.NIVELMONTARDADOS_TELACONSULTAESPECIAL, getCon());
            }
        }
    }

    @Override
    public List<MatriculaAlunoHorarioTurmaVO> consultarMatriculaAtiva(int contrato, Date vigenciaAtual) throws Exception {
        String sql = "SELECT matriculaalunohorarioturma.* FROM MatriculaAlunoHorarioTurma "
                + "WHERE contrato = " + contrato + " and datafim >= '" + vigenciaAtual + "'";
        try (Statement sqlConsultar = con.createStatement()) {
            try (ResultSet tabelaResultado = sqlConsultar.executeQuery(sql)) {
                return montarDadosConsulta(tabelaResultado, Uteis.NIVELMONTARDADOS_MINIMOS, getCon());
            }
        }
    }

    @Override
    public MatriculaAlunoHorarioTurmaVO consultarMatriculaAtivaPorHorarioTurma(int contrato, int horario, Date vigenciaAtual, int nivelMontarDados) throws Exception {
        String sql = "SELECT matriculaalunohorarioturma.* FROM MatriculaAlunoHorarioTurma "
                + "WHERE contrato = " + contrato + " AND horarioturma = " + horario + " AND datafim >= '" + vigenciaAtual + "'";
        try (Statement sqlConsultar = con.createStatement()) {
            try (ResultSet tabelaResultado = sqlConsultar.executeQuery(sql)) {
                if (tabelaResultado.next()) {
                    return montarDados(tabelaResultado, nivelMontarDados, getCon());
                } else {
                    return new MatriculaAlunoHorarioTurmaVO();
                }
            }
        }
    }

    @Override
    public MatriculaAlunoHorarioTurmaVO consultarMatriculaAtivaPorHorarioTurma(final PessoaVO pessoaVO, final int horario, final Date vigenciaAtual, final int nivelMontarDados) throws Exception {
        String sql = "SELECT matriculaalunohorarioturma.* FROM MatriculaAlunoHorarioTurma "
                + "WHERE pessoa = " + pessoaVO.getCodigo() + " AND horarioturma = " + horario + " AND datafim >= '" + vigenciaAtual + "'";
        try (Statement sqlConsultar = con.createStatement();
             ResultSet tabelaResultado = sqlConsultar.executeQuery(sql)) {
            if (tabelaResultado.next()) {
                return montarDados(tabelaResultado, nivelMontarDados, getCon());
            }
            return new MatriculaAlunoHorarioTurmaVO();
        }
    }


    public List<ClienteVO> contarEvasaoPorModalidadeNoPeriodo(Date dataInicio, Date dataFim, ModalidadeVO modalidadeVO, EmpresaVO empresaVO, TurmaVO turmaVO) throws Exception {
        String sql = "select distinct maht.pessoa\n" +
                "from matriculaalunohorarioturma maht\n" +
                "inner join horarioturma ht ON maht.horarioturma = ht.codigo\n" +
                "inner join turma t ON ht.turma = t.codigo\n" +
                "where t.modalidade = ?\n" +
                "and maht.empresa = ?\n" +
                "and datafim BETWEEN ? and ?\n";
        if (turmaVO!=null && !UteisValidacao.emptyNumber(turmaVO.getCodigo())) {
            sql += "and t.codigo = ?\n";
        }
        Map<Integer, ClienteVO> mapClientes = new HashMap<>();

        try (PreparedStatement stm = con.prepareStatement(sql)) {
            int i = 0;
            stm.setInt(++i, modalidadeVO.getCodigo());
            stm.setInt(++i, empresaVO.getCodigo());
            stm.setDate(++i, Uteis.getDataJDBC(dataInicio));
            stm.setDate(++i, Uteis.getDataJDBC(dataFim));
            if(turmaVO !=null && !UteisValidacao.emptyNumber(turmaVO.getCodigo())){
                stm.setInt(++i, turmaVO.getCodigo());
            }


            try (ResultSet rs = stm.executeQuery()) {

                while (rs.next()) {
                    Integer codPessoa = rs.getInt("pessoa");
                    ClienteVO clienteVO = mapClientes.get(codPessoa);
                    if (clienteVO == null) {
                        Cliente clienteDAO = new Cliente(this.con);
                        clienteVO = clienteDAO.consultarPorCodigoPessoa(codPessoa, Uteis.NIVELMONTARDADOS_RELATORIO_SGP);
                        mapClientes.put(codPessoa, clienteVO);
                    }
                }
            }
        }
        return new ArrayList<>(mapClientes.values());
    }

    public List<ClienteVO> contarEvasaoPorVariasModalidadeNoPeriodo(Date dataInicio, Date dataFim, String modalidades, EmpresaVO empresaVO, TurmaVO turma, Integer vezesSemana) throws Exception {
        String sql = "select distinct maht.pessoa\n" +
                "from matriculaalunohorarioturma maht\n" +
                "inner join horarioturma ht ON maht.horarioturma = ht.codigo\n" +
                "inner join turma t ON ht.turma = t.codigo\n" +
                "inner join contratomodalidade cm ON cm.contrato = maht.contrato and cm.modalidade = t.modalidade\n" +
                "inner join contratomodalidadevezessemana cmvs on cmvs.contratomodalidade = cm.codigo\n" +
                "inner join modalidade m ON m.codigo = t.modalidade\n" +
                "where maht.empresa = ?\n" +
                "and datafim BETWEEN ? and ?\n" +
                "and t.modalidade in (" + modalidades + ")" +
                "and (cmvs.nrvezes = ? or ((unaccent(m.nome) ILIKE '%musculacao%') and cmvs.nrvezes = 5))\n";
        if (turma != null && !UteisValidacao.emptyNumber(turma.getCodigo())) {
            sql += " and t.codigo = " + turma.getCodigo();

        }

        Map<Integer, ClienteVO> mapClientes = new HashMap<>();

        try (PreparedStatement stm = con.prepareStatement(sql)) {
            int i = 0;
            stm.setInt(++i, empresaVO.getCodigo());
            stm.setDate(++i, Uteis.getDataJDBC(dataInicio));
            stm.setDate(++i, Uteis.getDataJDBC(dataFim));
            stm.setInt(++i, vezesSemana);

            try (ResultSet rs = stm.executeQuery()) {

                while (rs.next()) {
                    Integer codPessoa = rs.getInt("pessoa");
                    ClienteVO clienteVO = mapClientes.get(codPessoa);
                    if (clienteVO == null) {
                        Cliente clienteDAO = new Cliente(this.con);
                        clienteVO = clienteDAO.consultarPorCodigoPessoa(codPessoa, Uteis.NIVELMONTARDADOS_RELATORIO_SGP);
                        mapClientes.put(codPessoa, clienteVO);
                    }
                }
            }
        }
        return new ArrayList<>(mapClientes.values());
    }

    @Override
    public List<MatriculaAlunoHorarioTurmaVO> consultarPorHorarioTurma(int valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        return consultarPorHorarioTurma(valorConsulta, negocio.comuns.utilitarias.Calendario.hoje(), negocio.comuns.utilitarias.Calendario.hoje(), controlarAcesso, nivelMontarDados);
    }

    @Override
    public List<MatriculaAlunoHorarioTurmaVO> consultarPorHorarioTurma(int valorConsulta, Date dataInicial, Date dataFinal, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        String sqlStr = "SELECT * FROM MatriculaAlunoHorarioTurma "
                + "WHERE horarioTurma = " + valorConsulta
                + sqlPeriodo(dataInicial, dataFinal)
                + "ORDER BY horarioTurma";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return montarDadosConsulta(tabelaResultado, nivelMontarDados, getCon());
            }
        }
    }

    @Override
    public Long consultarPorHorarioTurmaPorPeriodoCount(int valorConsulta, Date dataInicial, Date dataFinal, boolean controlarAcesso) throws Exception {
        String sqlStr = "SELECT count(codigo) FROM MatriculaAlunoHorarioTurma "
                + "WHERE horarioTurma = " + valorConsulta + " "
                + sqlPeriodo(dataInicial, dataFinal);
        try (Statement stm = con.createStatement();
             ResultSet tabelaResultado = stm.executeQuery(sqlStr)){
            if (!tabelaResultado.next()) {
                return new Long(0);
            }
            return (new Long(tabelaResultado.getInt(1)));
        }
    }

    @Override
    public Long consultarPorHorarioTurmaCount(int horarioTurma, boolean controlarAcesso) throws Exception {
        String sqlStr = "SELECT count(codigo) FROM MatriculaAlunoHorarioTurma "
                + "WHERE horarioTurma = " + horarioTurma;
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                if (!tabelaResultado.next()) {
                    return new Long(0);
                }
                return (new Long(tabelaResultado.getInt(1)));
            }
        }
    }

    @Override
    public Integer consultarPorHorarioTurmaACCount(int horarioTurma) throws Exception {
        String sqlStr = "SELECT count(codigo) FROM alunohorarioturma "
                + "WHERE horarioTurma = " + horarioTurma;
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return tabelaResultado.next() ? tabelaResultado.getInt(1) : 0;
            }
        }
    }

    @Override
    public List<MatriculaAlunoHorarioTurmaVO> consultarPorEmpresaContratoModalidade(int empresa, int contrato, int modalidade, int nivelMontarDados) throws Exception {
        return consultarPorEmpresaContratoModalidade(empresa, contrato, modalidade, negocio.comuns.utilitarias.Calendario.hoje(), negocio.comuns.utilitarias.Calendario.hoje(), nivelMontarDados);
    }

    @Override
    public List<MatriculaAlunoHorarioTurmaVO> consultarPorEmpresaContratoModalidade(int empresa, int contrato, int modalidade, Date dataInicial, Date dataFinal, int nivelMontarDados) throws Exception {
        String sql = "SELECT matriculaalunohorarioturma.* FROM MatriculaAlunoHorarioTurma "
                + "INNER JOIN horarioturma ON matriculaalunohorarioturma.horarioturma = horarioturma.codigo "
                + "INNER JOIN turma ON horarioturma.turma = turma.codigo "
                + "INNER JOIN modalidade ON turma.modalidade = modalidade.codigo "
                + "WHERE matriculaalunohorarioturma.empresa = " + empresa + " and "
                + "matriculaalunohorarioturma.contrato = " + contrato + " and "
                + "modalidade.codigo = " + modalidade
                + sqlPeriodo(dataInicial, dataFinal);
        try (Statement sqlConsultar = con.createStatement()) {
            try (ResultSet tabelaResultado = sqlConsultar.executeQuery(sql)) {
                return montarDadosConsulta(tabelaResultado, nivelMontarDados, getCon());
            }
        }
    }

    @Override
    public MatriculaAlunoHorarioTurmaVO consultarPorEmpresaPessoaContratoHorarioTurma(int empresa, int pessoa, int contrato, int horarioturma, int nivelMontarDados) throws Exception {
        return consultarPorEmpresaPessoaContratoHorarioTurma(empresa, pessoa, contrato, horarioturma, negocio.comuns.utilitarias.Calendario.hoje(), negocio.comuns.utilitarias.Calendario.hoje(), nivelMontarDados);
    }

    @Override
    public MatriculaAlunoHorarioTurmaVO consultarPorEmpresaPessoaContratoHorarioTurma(int empresa, int pessoa, int contrato, int horarioturma, Date dataInicial, Date dataFinal, int nivelMontarDados) throws Exception {
        String sql = "SELECT matriculaalunohorarioturma.* FROM MatriculaAlunoHorarioTurma "
                + "WHERE empresa = " + empresa + " and "
                + "pessoa = " + pessoa + " and "
                + "contrato = " + contrato + " and "
                + "horarioturma = " + horarioturma
                + sqlPeriodo(dataInicial, dataFinal);
        try (Statement sqlConsultar = con.createStatement()) {
            try (ResultSet tabelaResultado = sqlConsultar.executeQuery(sql)) {
                if (tabelaResultado.next()) {
                    return montarDados(tabelaResultado, nivelMontarDados, getCon());
                } else {
                    return new MatriculaAlunoHorarioTurmaVO();
                }
            }
        }
    }

    @Override
    public List<ConsultarAlunosTurmaVO> consultarPorHorarioTurma(int horarioTurma, int nivelMontarDados) throws Exception {
        return consultarPorHorarioTurmaPeriodo(horarioTurma, nivelMontarDados, Calendario.hoje(), Calendario.hoje(),false, true);
    }

    @Override
    public List<ConsultarAlunosTurmaVO> consultarPorHorarioTurmaPeriodoGestaoTurma(List<HorarioTurmaVO> listaHorarioTurma,
                                                                        int nivelMontarDados, final Date dataInicio, final Date dataFim, boolean frequencia, boolean consultarReposicoes) throws Exception {
        return consultarPorHorarioTurmaPeriodoGeral(Uteis.retornarCodigos(listaHorarioTurma), nivelMontarDados, dataInicio, dataFim, frequencia, consultarReposicoes);
    }

    @Override
    public List<ConsultarAlunosTurmaVO> consultarPorHorarioTurmaPeriodo(int horarioTurma,
                                                                        int nivelMontarDados, final Date dataInicio, final Date dataFim, boolean frequencia, boolean consultarReposicoes) throws Exception {
        return consultarPorHorarioTurmaPeriodoGeral(Integer.toString(horarioTurma), nivelMontarDados, dataInicio, dataFim, frequencia, consultarReposicoes);
    }


    private List<ConsultarAlunosTurmaVO> consultarPorHorarioTurmaPeriodoGeral(String horarioTurma,
                                                                        int nivelMontarDados, final Date dataInicio, final Date dataFim, boolean frequencia, boolean consultarReposicoes) throws Exception {
        if (UteisValidacao.emptyString(horarioTurma)) {
            return new ArrayList<ConsultarAlunosTurmaVO>();
        }
        List<ConsultarAlunosTurmaVO> objetos = new ArrayList<ConsultarAlunosTurmaVO>();
        String sqlStr = "SELECT * FROM matriculaalunohorarioturma ma "
                + "WHERE ma.horarioturma in (" + horarioTurma + ") ";
        if(frequencia){
            sqlStr +=  " "+sqlPeriodo(dataInicio, dataFim) + " order by pessoa,datainicio";
        } else {
            sqlStr +=  " "+sqlPeriodo(dataInicio, dataInicio);
        }
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                List<Integer> alunosAdicionados = new ArrayList<Integer>();
                while (tabelaResultado.next()) {
                    //Entidade usada para agrupar informações dos alunos de uma turma buscando também dados de cliente e de duração de contrato
                    ConsultarAlunosTurmaVO consultaAlunos = new ConsultarAlunosTurmaVO();

                    MatriculaAlunoHorarioTurmaVO matriculaHorarioTurmaVO = new MatriculaAlunoHorarioTurmaVO();
                    matriculaHorarioTurmaVO = montarDados(tabelaResultado, nivelMontarDados, getCon());
                    if (alunosAdicionados.contains(matriculaHorarioTurmaVO.getPessoa().getCodigo())
                            && Calendario.igual(objetos.get(alunosAdicionados.lastIndexOf(matriculaHorarioTurmaVO.getPessoa().getCodigo())).getMatriculaAlunoHorarioTurmaVO().getDataFim(), Uteis.somarDias(matriculaHorarioTurmaVO.getDataInicio(), -1))) {
                        objetos.get(alunosAdicionados.lastIndexOf(matriculaHorarioTurmaVO.getPessoa().getCodigo())).getMatriculaAlunoHorarioTurmaVO().setDataFim(matriculaHorarioTurmaVO.getDataFim());
                    } else {
                        Cliente clienteDAO = new Cliente(this.con);
                        ClienteVO objCliente = clienteDAO.consultarPorCodigoPessoa(matriculaHorarioTurmaVO.getPessoa().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSENTIDADESPRINCIPAIS);
                        ContratoDuracao contratoDuracaoDAO = new ContratoDuracao(this.con);
                        ContratoDuracaoVO objContratoDuracao = contratoDuracaoDAO.consultarContratoDuracoes(matriculaHorarioTurmaVO.getContrato().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);

                        consultaAlunos.setClienteVO(objCliente);
                        consultaAlunos.setContratoDuracaoVO(objContratoDuracao);
                        consultaAlunos.setMatriculaAlunoHorarioTurmaVO(matriculaHorarioTurmaVO);
                        objetos.add(consultaAlunos);
                        alunosAdicionados.add(matriculaHorarioTurmaVO.getPessoa().getCodigo());
                    }
                }
            }
        }
        if (consultarReposicoes) {
            preencherReposicoes(objetos, horarioTurma, dataInicio, dataFim);
        }
        return objetos;
    }

    private void preencherReposicoes(List<ConsultarAlunosTurmaVO> objetos,
            final String horariosTurma, final Date dataInicio, final Date dataFim) throws Exception {

        StringBuilder sql = new StringBuilder();
        sql.append("SELECT * FROM reposicao \n");
        sql.append("WHERE horarioturma in (").append(horariosTurma).append(") \n");
        sql.append("and (cast (datareposicao as date) = '").append(Uteis.getDataFormatoBD(dataInicio)).append("' \n");
        if (dataFim == null) {
            sql.append(" or (datareposicao >= '").append(Uteis.getDataFormatoBD(dataInicio)).append("'))");
        } else {
            sql.append(" or (datareposicao between '").append(Uteis.getDataFormatoBD(dataInicio)).append("' and '").append(Uteis.getDataFormatoBD(dataFim)).append("'))");
        }

        Reposicao reposicaoDAO = new Reposicao(this.con);
        List<ReposicaoVO> reposicoes = reposicaoDAO.consulta(sql.toString());

        for (ReposicaoVO reposicaoVO : reposicoes) {
            ContratoDuracao contratoDuracaoDAO = new ContratoDuracao();
            ConsultarAlunosTurmaVO consultaAlunos = new ConsultarAlunosTurmaVO();
            consultaAlunos.setClienteVO(reposicaoVO.getCliente());
            ContratoDuracaoVO objContratoDuracao =
                    reposicaoVO.getContrato() == null || UteisValidacao.emptyNumber(reposicaoVO.getContrato().getCodigo()) ?
                            new ContratoDuracaoVO() :
                            contratoDuracaoDAO.consultarContratoDuracoes(reposicaoVO.getContrato().getCodigo(),
                    Uteis.NIVELMONTARDADOS_DADOSBASICOS);

            MatriculaAlunoHorarioTurmaVO matriculaHorarioTurmaVO = getMatriculaHorarioTurma(reposicaoVO, false);
            if(reposicaoVO.getContrato() != null && !UteisValidacao.emptyNumber(reposicaoVO.getContrato().getCodigo())){
                matriculaHorarioTurmaVO.setEmpresa(reposicaoVO.getContrato().getEmpresa().getCodigo());
            }
            consultaAlunos.setMatriculaAlunoHorarioTurmaVO(matriculaHorarioTurmaVO);
            consultaAlunos.setContratoDuracaoVO(objContratoDuracao);
            objetos.add(consultaAlunos);
        }
    }

    @Override
    public void preencherReposicoesFormatoMatriculaAlunoHorarioTurma(final int empresa,
            final int turma, final int colaborador, List<MatriculaAlunoHorarioTurmaVO> matriculas,
            final Date dataInicio, final Date dataFim, List<String> filtroHorarios, List<String> filtrodiasSemana) throws Exception {
        List<ReposicaoVO> reposicoes = new ArrayList<ReposicaoVO>();
        String condicaoProfessor = (colaborador != 0 ? " and ht.professor = " + colaborador + " " : "");
        String condicaoTurma = (turma != 0 ? " and r.turmadestino = " + turma + " " : "");
        StringBuilder condicaoHorarioDia = new StringBuilder();
        boolean inicio = true;
        for (String hora : filtroHorarios) {
            if (inicio) {
                inicio = false;
                condicaoHorarioDia.append(" AND (");
            } else {
                condicaoHorarioDia.append(" OR ");
            }
            condicaoHorarioDia.append(" (ht.horainicial >= '").append(hora.substring(0, 5)).append("' ");
            condicaoHorarioDia.append(" AND ht.horainicial  <= '").append(hora.substring(8, 13)).append("') ");
        }
        if (!inicio) {
            condicaoHorarioDia.append(") ");
            inicio = true;
        }
        for (String dia : filtrodiasSemana) {
            if (inicio) {
                inicio  = false;
                condicaoHorarioDia.append(" AND ht.diasemana IN (");
            } else {
                condicaoHorarioDia.append(",");
            }
            condicaoHorarioDia.append(" '").append(dia.toUpperCase()).append("' ");
        }
        if (!inicio ) {
            condicaoHorarioDia.append(") ");
        }
        Reposicao reposicaoDAO = new Reposicao(this.con);
        reposicoes.addAll(reposicaoDAO.consulta(
                String.format("SELECT r.* FROM reposicao r "
                + "inner join horarioturma ht on ht.codigo = r.horarioturma "
                + "WHERE 1 = 1 "
                + condicaoTurma
                + condicaoProfessor
                + condicaoHorarioDia.toString()
                + "and cast(r.datareposicao as date) between '%s' and '%s' ",
                        Uteis.getDataFormatoBD(dataInicio),
                        Uteis.getDataFormatoBD(dataFim))));

        for (ReposicaoVO reposicaoVO : reposicoes) {
            matriculas.add(getMatriculaHorarioTurma(reposicaoVO, true));
        }
    }

    private MatriculaAlunoHorarioTurmaVO getMatriculaHorarioTurma(ReposicaoVO reposicaoVO, boolean setarEmpresa) {
        MatriculaAlunoHorarioTurmaVO matriculaHorarioTurmaVO = new MatriculaAlunoHorarioTurmaVO();
        if (setarEmpresa)
            matriculaHorarioTurmaVO.setEmpresa(reposicaoVO.getContrato().getEmpresa().getCodigo());
        matriculaHorarioTurmaVO.setContrato(reposicaoVO.getContrato());
        matriculaHorarioTurmaVO.setPessoa(reposicaoVO.getCliente().getPessoa());
        matriculaHorarioTurmaVO.setDataInicio(reposicaoVO.getDataReposicao());
        matriculaHorarioTurmaVO.setDataFim(reposicaoVO.getDataReposicao());
        matriculaHorarioTurmaVO.setHorarioTurma(reposicaoVO.getHorarioTurma());
        matriculaHorarioTurmaVO.setReposicao(reposicaoVO);
        return matriculaHorarioTurmaVO;
    }

    @Override
    public void preencherReposicoesFormatoMatriculaAlunoHorarioTurma(final String horarios,
            List<MatriculaAlunoHorarioTurmaVO> matriculas,
            final Date dataInicio, final Date dataFim) throws Exception {
        List<ReposicaoVO> reposicoes = new ArrayList();

        Reposicao reposicaoDAO = new Reposicao(this.con);
        reposicoes.addAll(reposicaoDAO.consulta(
                String.format("SELECT * FROM reposicao "
                + "WHERE horarioturma in %s "
                + "and cast(datareposicao as date) between '%s' and '%s' ", new Object[]{
                    horarios,
                    Uteis.getDataFormatoBD(dataInicio),
                    Uteis.getDataFormatoBD(dataFim)
                })));

        for (ReposicaoVO reposicaoVO : reposicoes) {
            matriculas.add(getMatriculaHorarioTurma(reposicaoVO, true));
        }
    }

    @Override
    public List<MatriculaAlunoHorarioTurmaVO> consultarPorHorariosTurma(String horariosTurma, int nivelMontarDados, final Date inicio ,final Date fim) throws Exception {
        String sqlStr = "SELECT DISTINCT ma.*, pessoa.nome FROM matriculaalunohorarioturma ma "
                + "INNER JOIN pessoa ON ma.pessoa = pessoa.codigo "
                + "WHERE ma.horarioturma in " + horariosTurma
                + sqlPeriodo(inicio, fim)
                + "ORDER BY pessoa.nome";
        List<MatriculaAlunoHorarioTurmaVO> lista;
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                lista = montarDadosConsulta(tabelaResultado, nivelMontarDados, getCon());
            }
        }
        preencherReposicoesFormatoMatriculaAlunoHorarioTurma(horariosTurma, lista,
                inicio, fim);
        return lista;
    }

    public static List<MatriculaAlunoHorarioTurmaVO> montarDadosConsulta(ResultSet tabelaResultado, int nivelMontarDados, Connection con) throws Exception {
        List<MatriculaAlunoHorarioTurmaVO> vetResultado = new ArrayList<>();
        while (tabelaResultado.next()) {
            vetResultado.add(montarDados(tabelaResultado, nivelMontarDados, con));
        }
        return vetResultado;
    }

    public static MatriculaAlunoHorarioTurmaVO montarDados(ResultSet dadosSQL, int nivelMontarDados, Connection con) throws Exception {
        MatriculaAlunoHorarioTurmaVO obj = new MatriculaAlunoHorarioTurmaVO();
        // dados principais
        obj.setCodigo(dadosSQL.getInt("codigo"));
        obj.setEmpresa(dadosSQL.getInt("empresa"));
        obj.getPessoa().setCodigo(dadosSQL.getInt("pessoa"));
        obj.getContrato().setCodigo(dadosSQL.getInt("contrato"));
        obj.setDataInicio(dadosSQL.getDate("dataInicio"));
        obj.setDataFim(dadosSQL.getDate("dataFim"));
        obj.getHorarioTurma().setCodigo(dadosSQL.getInt("horarioturma"));
        obj.setNovoObj(false);

        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_MINIMOS) {
            return obj;
        }

        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_TELACONSULTAESPECIAL) {

            obj.getHorarioTurma().setDiaSemana(dadosSQL.getString("diasemana"));
            obj.getHorarioTurma().getProfessor().setCodigo(dadosSQL.getInt("professor"));
            obj.getHorarioTurma().setHoraFinal(dadosSQL.getString("horainicial"));
            obj.getHorarioTurma().setHoraInicial(dadosSQL.getString("horafinal"));
            obj.getHorarioTurma().setTurma(dadosSQL.getInt("turma"));
            obj.getHorarioTurma().setIdentificadorTurma(dadosSQL.getString("identificadorturma"));
            return obj;
        }

        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_DADOSBASICOS) {
            montarDadosHorarioTurma(obj, Uteis.NIVELMONTARDADOS_DADOSENTIDADESUBORDINADAS, con);
            return obj;
        }

        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_DADOSENTIDADESPRINCIPAIS) {
            montarDadosPessoa(obj, Uteis.NIVELMONTARDADOS_DADOSBASICOS, con);
            montarDadosContrato(obj, Uteis.NIVELMONTARDADOS_TELACONSULTA, con);
            montarDadosHorarioTurma(obj, Uteis.NIVELMONTARDADOS_DADOSENTIDADESPRINCIPAIS, con);
            return obj;
        }

        montarDadosPessoa(obj, Uteis.NIVELMONTARDADOS_ROBO, con);
        montarDadosContrato(obj, Uteis.NIVELMONTARDADOS_TELACONSULTA, con);
        montarDadosHorarioTurma(obj, Uteis.NIVELMONTARDADOS_TODOS, con);
        return obj;
    }

    public static void montarDadosPessoa(MatriculaAlunoHorarioTurmaVO obj, int nivelMontarDados, Connection con) throws Exception {
        PessoaInterfaceFacade pessoa = new Pessoa(con);
        if (obj.getPessoa().getCodigo().intValue() == 0) {
            obj.setPessoa(new PessoaVO());
        } else {
            obj.setPessoa(pessoa.consultarPorChavePrimaria(obj.getPessoa().getCodigo(), nivelMontarDados));
        }
    }

    public static void montarDadosContrato(MatriculaAlunoHorarioTurmaVO obj, int nivelMontarDados, Connection con) throws Exception {
        ContratoInterfaceFacade contrato = new Contrato(con);
        if (obj.getContrato().getCodigo() == 0) {
            obj.setContrato(new ContratoVO());
        } else {
            obj.setContrato(contrato.consultarPorChavePrimaria(obj.getContrato().getCodigo(), nivelMontarDados));
        }
    }

    public static void montarDadosHorarioTurma(MatriculaAlunoHorarioTurmaVO obj, int nivelMontarDados, Connection con) throws Exception {
        HorarioTurmaInterfaceFacade horarioDao = new HorarioTurma(con);
        if (obj.getHorarioTurma().getCodigo().intValue() == 0) {
            obj.setHorarioTurma(new HorarioTurmaVO());
        } else {
            obj.setHorarioTurma(horarioDao.consultarPorChavePrimaria(obj.getHorarioTurma().getCodigo(), nivelMontarDados));
        }
    }

    @Override
    public void matricularAlunos() throws Exception {
        System.out.println("Iniciando Processo em " + Calendario.hoje());
        processarContratos();
        System.out.println("Processo Terminado em " + Calendario.hoje());
    }

    @Override
    public void matricularAlunosQueNaoPossuemHistorico() throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("select codigo,empresa,pessoa,vigenciade,vigenciaateajustada,contratoresponsavelrenovacaomatricula from contrato ");
        sql.append("where codigo not in (select contrato from matriculaalunohorarioturma) ");
        sql.append("and codigo in (select contrato from contratomodalidade  where codigo in (select contratomodalidade from contratomodalidadeturma)) order by codigo");

        try (ResultSet rs = SuperFacadeJDBC.criarConsulta(sql.toString(), con)) {
            while (rs.next()) {
                try (ResultSet rsTolerancia = SuperFacadeJDBC.criarConsulta("select toleranciaocupacaoturma from empresa where codigo = " + rs.getInt("empresa"), con)) {
                    if (rsTolerancia.next()) {
                        int tol = rsTolerancia.getInt(1);
                        try (ResultSet rsContratoModalidadeHorarioTurma = SuperFacadeJDBC.criarConsulta(
                                "select cmht.* from contratomodalidade cm "
                                        + "inner join contratomodalidadeturma cmt on cmt.contratomodalidade = cm.codigo "
                                        + "inner join contratomodalidadehorarioturma cmht on cmht.contratomodalidadeturma = cmt.codigo "
                                        + "where cm.contrato = " + rs.getInt("codigo"), con)) {
                            while (rsContratoModalidadeHorarioTurma.next()) {
                                MatriculaAlunoHorarioTurmaVO matricula = new MatriculaAlunoHorarioTurmaVO();
                                matricula.setEmpresa(rs.getInt("empresa"));
                                matricula.getPessoa().setCodigo(rs.getInt("pessoa"));
                                matricula.getContrato().setCodigo(rs.getInt("codigo"));
                                matricula.setDataInicio(rs.getDate("vigenciade"));
                                if (rs.getInt("contratoresponsavelrenovacaomatricula") > 0) {
                                    matricula.setDataFim(rs.getDate("vigenciaateajustada"));
                                } else {
                                    matricula.setDataFim(Uteis.somarDias(rs.getDate("vigenciaateajustada"), tol));
                                }
                                matricula.getHorarioTurma().setCodigo(rsContratoModalidadeHorarioTurma.getInt("horarioturma"));
                                incluirSemComit(matricula);
                                Uteis.logar(null, "Inserido matricula para o contrato -> " + matricula.getContrato().getCodigo());
                            }
                        }
                    }
                }
            }
        }
    }

    private void processarContratos() throws Exception {
        con.setAutoCommit(false);
        try {

            String sqlStr = "SELECT contrato.empresa, contrato.pessoa, contrato.codigo as contrato,"
                    + " contrato.vigenciade, contrato.vigenciaateajustada, contrato.contratoresponsavelrenovacaomatricula,"
                    + " empresa.toleranciaocupacaoturma, matriculaalunohorarioturma.codigo as matricula,"
                    + " matriculaalunohorarioturma.horarioturma"
                    + " FROM contrato "
                    + " INNER JOIN matriculaalunohorarioturma   ON matriculaalunohorarioturma.contrato = contrato.codigo"
                    + " INNER JOIN empresa ON contrato.empresa = empresa.codigo"
                    + " WHERE matriculaalunohorarioturma.datafim >= contrato.vigenciaateajustada;";


            try (Statement stm = con.createStatement()) {
                try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                    processarMatriculas(salvarLista(tabelaResultado));
                }
            }
            con.commit();
        } catch (Exception e) {
            con.rollback();
            System.out.println("Abortando Processo... ERRO: " + e.getMessage());
        } finally {
            con.setAutoCommit(true);
        }
    }

    private List<MatriculaAlunoHorarioTurmaVO> salvarLista(ResultSet tabelaResultado) throws Exception {
        List<MatriculaAlunoHorarioTurmaVO> lista = new ArrayList<MatriculaAlunoHorarioTurmaVO>();
        while (tabelaResultado.next()) {
            // realiza uma matricula
            MatriculaAlunoHorarioTurmaVO matricula = new MatriculaAlunoHorarioTurmaVO();
            matricula.setCodigo(tabelaResultado.getInt("matricula"));
            matricula.setEmpresa(tabelaResultado.getInt("empresa"));
            matricula.getPessoa().setCodigo(tabelaResultado.getInt("pessoa"));
            matricula.getContrato().setCodigo(tabelaResultado.getInt("contrato"));
            matricula.setDataInicio(tabelaResultado.getDate("vigenciade"));
            if (tabelaResultado.getInt("contratoresponsavelrenovacaomatricula") > 0) {
                matricula.setDataFim(tabelaResultado.getDate("vigenciaateajustada"));
            } else {
                matricula.setDataFim(Uteis.somarDias(tabelaResultado.getDate("vigenciaateajustada"), tabelaResultado.getInt("toleranciaocupacaoturma")));
            }
            matricula.getHorarioTurma().setCodigo(tabelaResultado.getInt("horarioturma"));
            lista.add(matricula);
        }
        return lista;
    }

    private void processarMatriculas(List<MatriculaAlunoHorarioTurmaVO> lista) throws Exception {
        for (MatriculaAlunoHorarioTurmaVO matricula : lista) {
            try {
                alterarSemCommit(matricula);
            } catch (Exception e) {
                System.out.println("Contrato ignorado " + matricula.getContrato().getCodigo() + "(" + e.getMessage() + ")");
            }
        }
    }
    
    
    public List<AlunoMapaTurmasTO> consultarPorHorarioTurmaPeriodoMapaTurmas(int horarioTurma,
            final Date dataInicio, final Date dataFim) throws Exception {
        List<AlunoMapaTurmasTO> objetos = new ArrayList<AlunoMapaTurmasTO>();
        StringBuilder sqlStr = new StringBuilder();

        sqlStr.append("SELECT DISTINCT(cli.codigo), ");
        sqlStr.append("p.nome, cli.matricula ");
        sqlStr.append("FROM matriculaalunohorarioturma ma ");
        sqlStr.append("INNER JOIN pessoa p ON p.codigo = ma.pessoa ");
        sqlStr.append("INNER JOIN cliente cli ON cli.pessoa = ma.pessoa ");
        sqlStr.append("WHERE ma.horarioturma = ").append(horarioTurma);
        sqlStr.append(" ").append(sqlPeriodo(dataInicio, dataFim));
        sqlStr.append(" ORDER BY nome");

        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr.toString())) {
                int i = 1;
                while (tabelaResultado.next()) {
                    AlunoMapaTurmasTO aluno = new AlunoMapaTurmasTO();
                    aluno.setCodigoCliente(tabelaResultado.getInt("codigo"));
                    aluno.setCodigo(i++);
                    aluno.setNome(tabelaResultado.getString("nome"));
                    aluno.setMatricula(tabelaResultado.getString("matricula"));
                    objetos.add(aluno);
                }
            }
        }
        return objetos;
    }

    public List<AlunoMapaTurmasTO> consultarReposicoesPorHorarioTurmaPeriodoMapaTurmas(int horarioTurma,
                                                                                       final Date dataInicio, final Date dataFim) throws Exception {
        List<AlunoMapaTurmasTO> objetos = new ArrayList<AlunoMapaTurmasTO>();
        StringBuilder sql = new StringBuilder();

        sql.append("select \n");
        sql.append("r.cliente as codigo, \n");
        sql.append("p.nome, \n");
        sql.append("c.matricula \n");
        sql.append("from reposicao r \n");
        sql.append("inner join cliente c on c.codigo = r.cliente \n");
        sql.append("inner join pessoa p on p.codigo = c.pessoa \n");
        sql.append(" where r.horarioturma = ").append(horarioTurma);

        if (dataInicio != null && dataFim != null) {
            sql.append(" AND ((r.datareposicao::date >= '").append(Uteis.getDataJDBC(dataInicio)).append("' AND r.datareposicao::date <= '").append(Uteis.getDataJDBC(dataFim)).append("') OR ");
            sql.append("(r.datareposicao::date >= '").append(Uteis.getDataJDBC(dataInicio)).append("' AND r.datareposicao::date <= '").append(Uteis.getDataJDBC(dataFim)).append("') OR ");
            sql.append("(r.datareposicao::date < '").append(Uteis.getDataJDBC(dataInicio)).append("' AND r.datareposicao::date > '").append(Uteis.getDataJDBC(dataFim)).append("')) ");
        }
        sql.append(" ORDER BY p.nome");

        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sql.toString())) {
                int i = 1;
                while (tabelaResultado.next()) {
                    AlunoMapaTurmasTO aluno = new AlunoMapaTurmasTO();
                    aluno.setCodigoCliente(tabelaResultado.getInt("codigo"));
                    aluno.setCodigo(i++);
                    aluno.setNome(tabelaResultado.getString("nome"));
                    aluno.setMatricula(tabelaResultado.getString("matricula"));
                    objetos.add(aluno);
                }
            }
        }
        return objetos;
    }

    public boolean alunoDesmarcouAula(Integer cliente, int horarioTurma, final Date dataInicio, final Date dataFim) throws Exception {

        StringBuilder sql = new StringBuilder();
        sql.append("select exists (select codigo from auladesmarcada a \n");
        sql.append("where a.horarioturma = ").append(horarioTurma).append(" \n");
        sql.append("and cliente = ").append(cliente).append(" \n");
        sql.append("AND ((a.dataorigem::date >= '").append(Uteis.getDataJDBC(dataInicio)).append("' AND a.dataorigem::date <= '").append(Uteis.getDataJDBC(dataFim)).append("') OR ");
        sql.append("(a.dataorigem::date >= '").append(Uteis.getDataJDBC(dataInicio)).append("' AND a.dataorigem::date <= '").append(Uteis.getDataJDBC(dataFim)).append("') OR ");
        sql.append("(a.dataorigem::date < '").append(Uteis.getDataJDBC(dataInicio)).append("' AND a.dataorigem::date > '").append(Uteis.getDataJDBC(dataFim)).append("')) ");
        sql.append(") as desmarcou ");
        try (ResultSet rs = criarConsulta(sql.toString(), con)) {
            rs.next();
            return rs.getBoolean("desmarcou");
        }
    }

    public boolean validarTemTurmaNoPeriodoOuNaoTemTurmaNenhuma(final Date inicio, final Date fim,
            final int contrato, final int modalidade) throws Exception{
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT (SELECT EXISTS ( \n");
        sql.append(" SELECT mht.* FROM matriculaalunohorarioturma mht \n");
        sql.append(" INNER JOIN horarioturma ht ON ht.codigo = mht.horarioturma \n");
        sql.append(" INNER JOIN turma t ON t.codigo = ht.turma AND t.modalidade = ").append(modalidade);
        sql.append(" AND mht.contrato = ").append(contrato);
        sql.append(" AND ((datafim >= '").append(Uteis.getDataJDBC(inicio)).append("' AND datafim <= '").append(Uteis.getDataJDBC(fim)).append("') OR \n");
        sql.append("(datainicio >= '").append(Uteis.getDataJDBC(inicio)).append("' AND datainicio <= '").append(Uteis.getDataJDBC(fim)).append("') OR \n");
        sql.append("(datainicio < '").append(Uteis.getDataJDBC(inicio)).append("' AND datafim > '").append(Uteis.getDataJDBC(fim)).append("'))) \n");
        sql.append(" OR (SELECT NOT EXISTS (SELECT mht.* FROM matriculaalunohorarioturma mht \n");
        sql.append(" INNER JOIN horarioturma ht ON ht.codigo = mht.horarioturma  \n");
        sql.append(" INNER JOIN turma t ON t.codigo = ht.turma AND t.modalidade = ").append(modalidade);
        sql.append(" AND mht.contrato = ").append(contrato).append(")) \n");
        sql.append(" )  AS validar");
        try (ResultSet rs = criarConsulta(sql.toString(), con)) {
            rs.next();
            return rs.getBoolean("validar");
        }
    }

    private List<MatriculaAlunoHorarioTurmaVO> consultarMatriculaAtivaPorCampo(String nomeCampo, int valorCampo, int empresa,
                                                                              Date dataPesquisa, int nivelMontarDados) throws Exception {
        String sql = "SELECT  math.* "
                + " FROM MatriculaAlunoHorarioTurma math "
                + " WHERE math." + nomeCampo + " = " + valorCampo + " and math.empresa = "+ empresa +" and '"+ Uteis.getDataJDBC(dataPesquisa) + "' between   math.datainicio and math.datafim;";
        try (Statement sqlConsultar = con.createStatement()) {
            try (ResultSet tabelaResultado = sqlConsultar.executeQuery(sql)) {
                return montarDadosConsulta(tabelaResultado, nivelMontarDados, getCon());
            }
        }
    }

    @Override
    public List<MatriculaAlunoHorarioTurmaVO> consultarMatriculaAtivaPorContrato(int contrato, int empresa, Date dataPesquisa, int nivelMontarDados) throws Exception {
        return consultarMatriculaAtivaPorCampo("contrato", contrato, empresa, dataPesquisa, nivelMontarDados);
    }
    
     @Override
    public List<MatriculaAlunoHorarioTurmaVO> consultarMatriculaAtivaPorPessoa(int pessoa, int empresa, Date dataPesquisa, int nivelMontarDados) throws Exception {
         return consultarMatriculaAtivaPorCampo("pessoa", pessoa, empresa, dataPesquisa, nivelMontarDados);
    }
     
    @Override
    public List<AgendadoJSON> consultarHorariosTurmaParaAgenda(Date inicio, Integer empresa) throws Exception{
        List<AgendadoJSON> lista = new ArrayList<AgendadoJSON>();
        StringBuilder sql = new StringBuilder();

        sql.append(" SELECT sc.codigocliente as cliente, sc.situacao, sc.matricula, sc.telefonescliente, sc.nomecliente as nome, sc.codigopessoa as pessoa, \n");
        sql.append(" t.codigo as turma, ma.codigo as matriculahorarioturma,sc.situacaoContrato as situacaoContrato, \n");
        sql.append(" ht.codigo as horarioturma, ma.datainicio, ma.datafim, ma.contrato, p.fotokey FROM matriculaalunohorarioturma ma  \n");
        sql.append(" INNER JOIN horarioturma ht ON ht.codigo = ma.horarioturma\n");
        sql.append(" INNER JOIN turma t ON t.codigo = ht.turma\n");
        sql.append(" INNER JOIN situacaoclientesinteticodw sc ON sc.codigopessoa = ma.pessoa\n");
        sql.append(" INNER JOIN pessoa p ON p.codigo = ma.pessoa\n");
        sql.append(" WHERE ma.datafim >= '").append(Uteis.getDataJDBC(inicio)).append("'");
        sql.append(" AND ht.situacao = 'AT' \n");
        sql.append(" AND t.empresa = ").append(empresa);
        try (ResultSet rs = criarConsulta(sql.toString(), con)) {
            while (rs.next()) {
                AgendadoJSON agendado = new AgendadoJSON();
                agendado.setCodigoCliente(rs.getInt("cliente"));
                agendado.setCodigoContrato(rs.getInt("contrato"));
                agendado.setMatricula(rs.getString("matricula"));
                agendado.setNome(rs.getString("nome"));
                agendado.setTelefones(rs.getString("telefonescliente"));
                agendado.setCodigoPessoa(rs.getInt("pessoa"));
                agendado.setId_agendamento(String.valueOf(rs.getInt("horarioturma")));
                agendado.setInicio(Uteis.getDataAplicandoFormatacao(rs.getDate("datainicio"), "dd/MM/yyyy"));
                agendado.setFim(Uteis.getDataAplicandoFormatacao(rs.getDate("datafim"), "dd/MM/yyyy"));
                agendado.setSituacaoContrato(rs.getString("situacaoContrato"));
                agendado.setSituacao(rs.getString("situacao"));
                agendado.setFotokey(rs.getString("fotokey"));
                lista.add(agendado);
            }
        }
        return lista;
    }
    
    private String obterIdentificadorProximaAulaModalidadeContrato(Integer cliente, Integer contrato, Integer modalidade, Date dataBase)throws Exception{
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT ht.diasemana, maht.horarioturma FROM matriculaalunohorarioturma maht\n");
        sql.append("INNER JOIN horarioturma ht ON ht.codigo = maht.horarioturma\n");
        sql.append("INNER JOIN turma t ON t.codigo = ht.turma\n");
        sql.append("WHERE maht.contrato = ?\n");
        sql.append("AND t.modalidade = ?");
        PreparedStatement pst = con.prepareStatement(sql.toString());
        pst.setInt(1, contrato);
        pst.setInt(2, modalidade);
        String identificador = null;
        Integer difDias = null;
        try (ResultSet rs = pst.executeQuery()) {
            while (rs.next()) {
                DiaSemana diaSemana = DiaSemana.getDiaSemana(rs.getString("diasemana"));
                if (diaSemana != null) {
                    Date proximoDiaSemana = Calendario.proximoDiaSemana(diaSemana.getNumeral(), dataBase);
                    List<Date> diasEntreDatas = Uteis.getDiasEntreDatas(dataBase, proximoDiaSemana);
                    if (difDias == null || diasEntreDatas.size() < difDias) {
                        difDias = diasEntreDatas.size();
                        identificador = rs.getInt("horarioturma") + "_" + Uteis.getData(proximoDiaSemana, "ddMMyy");
                    }
                }
            }
        }
        return identificador;
    }

    public String obterIdentificadorProximaAulaModalidade(Integer cliente, Integer contrato, Integer modalidade)throws Exception{
        String identificadorAula = obterIdentificadorProximaAulaModalidadeContrato(cliente,contrato,modalidade, Calendario.hoje());
        if (aulaJaDesmarcada(identificadorAula,contrato)){
            do {
                Date dataAulaJaReposicao = new SimpleDateFormat("dd/MM/yy").parse(identificadorAula.split("_")[1]);
                identificadorAula = obterIdentificadorProximaAulaModalidadeContrato(cliente,contrato,modalidade, dataAulaJaReposicao);
            }
            while (aulaJaDesmarcada(identificadorAula,contrato));
        }
        return identificadorAula;
    }

    private boolean aulaJaDesmarcada(String identificadorAula, Integer contrato)throws Exception{
        String[] array = identificadorAula.split("_");
        HorarioTurmaVO horarioTurmaVO = new HorarioTurmaVO();
        horarioTurmaVO.setCodigo(Integer.parseInt(array[0]));
        Date dataRepor = new SimpleDateFormat("dd/MM/yy").parse(array[1]);
        AulaDesmarcada aulaDesmarcadaDAO = new AulaDesmarcada(this.con);
        AulaDesmarcadaVO aulaDesmarcadaVO = aulaDesmarcadaDAO.consultarAulaDesmarcada(horarioTurmaVO,dataRepor,contrato, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
        return (aulaDesmarcadaVO != null) && (!UteisValidacao.emptyNumber(aulaDesmarcadaVO.getCodigo()));

    }
    
    private String sqlConsultaMatriculaHorarioTurma(Integer matricula, String diaSemana, Date limite, boolean aulasFuturas, Integer modalidade, Integer tipoModalidade) throws Exception{
        StringBuilder sql = new StringBuilder();
        sql.append(" SELECT t.codigo as turma, t.empresa, t.idademinima as idademinima, t.idademinimameses as idademinimameses, \n");
        sql.append(" t.idademaxima as idademaxima, t.idademaximameses as idademaximameses,\n");
        sql.append(" t.identificador,t.aulaColetiva, m.codigo as modalidade, m.nome as nomemodalidade,\n");
        sql.append(" nt.descricao,\n");
        sql.append(" ht.diasemana,ht.professor,p.nome as nomeprofessor, ht.ambiente, a.descricao as nomeambiente, con.vigenciaateajustada,  \n");
        sql.append(" ht.horainicial, ht.horafinal, ht.nrmaximoaluno, ht.codigo as horarioturma,maht.datainicio,maht.datafim, maht.contrato,con.vigenciade,maht.codigo as codigomatriculaalunohorarioturma,\n");
        sql.append(" p.fotokey as fotoProfessor,\n");
        sql.append(" exists(select codigo from demandahorarioturma dht where dht.cliente = cli.codigo AND dht.horarioturma = ht.codigo) as jaMarcouEuQuero, m.fotokey as fotoModalidade\n");
        sql.append(" FROM matriculaalunohorarioturma maht  \n");
        sql.append(" INNER JOIN cliente cli ON cli.pessoa = maht.pessoa\n");
        sql.append(" INNER JOIN horarioturma ht ON ht.codigo = maht.horarioturma\n");
        if(!UteisValidacao.emptyString(diaSemana)){
            sql.append(" AND ht.diasemana = '").append(diaSemana).append("'\n");
        }
        sql.append(" INNER JOIN colaborador c ON c.codigo = ht.professor\n");
        sql.append(" INNER JOIN pessoa p ON p.codigo = c.pessoa\n");
        sql.append(" INNER JOIN turma t ON t.codigo = ht.turma\n");
        sql.append(" INNER JOIN nivelturma nt ON nt.codigo = ht.nivelturma\n");
        sql.append(" INNER JOIN modalidade m ON t.modalidade = m.codigo AND m.utilizarturma\n");
        sql.append(" INNER JOIN contrato con ON con.codigo = maht.contrato AND con.situacao = 'AT'\n");
        sql.append(" INNER JOIN ambiente a ON a.codigo = ht.ambiente \n");
        sql.append(" WHERE cli.codigomatricula = ").append(matricula);
        if(limite != null){
            sql.append(" AND maht.datafim >= '").append(Uteis.getDataJDBC(limite));
            sql.append("' AND maht.datainicio <= '").append(Uteis.getDataJDBC(limite)).append("' ");
        }
        if (UteisValidacao.notEmptyNumber(tipoModalidade)) {
            sql.append(" and m.tipo = ").append(tipoModalidade);
        } else if (UteisValidacao.notEmptyNumber(modalidade)) {
            sql.append(" and m.codigo = ").append(modalidade);
        }
        sql.append(" ORDER BY maht.datafim");

        return sql.toString();
    }
    
    private String sqlConsultaMatriculaHorarioTurma(Integer matricula,Integer contrato,boolean habilitarSomaDeAulaNaoVigente,List<MatriculaAlunoHorarioTurmaVO> listaMatriculaHorarioTurma) throws Exception{
        StringBuilder matriculaHorarioTurma = new StringBuilder("");
        for (MatriculaAlunoHorarioTurmaVO matriculaAlunoHorarioTurmaVO : listaMatriculaHorarioTurma) {
            if (matriculaHorarioTurma.toString().isEmpty()) {
                matriculaHorarioTurma.append(matriculaAlunoHorarioTurmaVO.getCodigo());
            }else{
                matriculaHorarioTurma.append(",").append(matriculaAlunoHorarioTurmaVO.getCodigo());
            }
        }
        StringBuilder sql = new StringBuilder();
        sql.append(" SELECT t.codigo as turma, t.empresa, t.idademinima as idademinima, t.idademinimameses as idademinimameses, \n");
        sql.append(" t.idademaxima as idademaxima, t.idademaximameses as idademaximameses,\n");
        sql.append(" t.identificador,t.aulaColetiva, m.codigo as modalidade, m.nome as nomemodalidade,\n");
        sql.append(" ht.diasemana,ht.professor,p.nome as nomeprofessor, ht.ambiente, con.vigenciaateajustada,  \n");
        sql.append(" ht.horainicial, ht.horafinal, ht.nrmaximoaluno, ht.codigo as horarioturma,maht.datainicio,maht.datafim, maht.contrato,con.vigenciade,maht.codigo as codigomatriculaalunohorarioturma,t.codigo as turma,\n");
        sql.append(" exists(select codigo from demandahorarioturma dht where dht.cliente = sc.codigocliente AND dht.horarioturma = ht.codigo) as jaMarcouEuQuero\n");
        sql.append(" FROM matriculaalunohorarioturma maht  \n");
        sql.append(" INNER JOIN situacaoclientesinteticodw sc ON maht.pessoa = sc.codigopessoa \n");
        sql.append(" INNER JOIN horarioturma ht ON ht.codigo = maht.horarioturma\n");
        sql.append(" INNER JOIN colaborador c ON c.codigo = ht.professor\n");
        sql.append(" INNER JOIN pessoa p ON p.codigo = c.pessoa\n");
        sql.append(" INNER JOIN turma t ON t.codigo = ht.turma\n");
        sql.append(" INNER JOIN modalidade m ON t.modalidade = m.codigo\n");
        sql.append(" INNER JOIN contrato con ON con.codigo = maht.contrato \n");
        if (habilitarSomaDeAulaNaoVigente) {
            sql.append(" WHERE sc.matricula = ").append(matricula);
            sql.append(" AND con.codigo = ").append(contrato);
        }else{
            sql.append(" WHERE maht.codigo IN (").append(matriculaHorarioTurma.toString()).append(")");
        }
        sql.append(" ORDER BY maht.datafim");
        
        return sql.toString();
    }
    
    public static AgendaTotalJSON montarDadosAgenda(ResultSet rs) throws Exception{
        AgendaTotalJSON item = new AgendaTotalJSON();
        item.setEmpresa(rs.getInt("empresa"));
        item.setAulaCheia(rs.getBoolean("aulaColetiva"));
        try{
            item.setNivel(rs.getString("descricao"));
        }catch(Exception ignored){

        }
        item.setInicio(rs.getString("horainicial"));
        item.setFim(rs.getString("horafinal"));
        item.setTitulo(rs.getString("identificador"));
        item.setTipo(rs.getString("nomemodalidade"));
        item.setCodigoTipo(rs.getInt("modalidade"));
        item.setCodigoLocal(rs.getInt("ambiente"));
        item.setNrVagas(rs.getInt("nrmaximoaluno"));
        item.setResponsavel(rs.getString("nomeprofessor"));
        item.setCodigoResponsavel(rs.getInt("professor"));
        item.setId(String.valueOf(rs.getInt("horarioturma")));
        item.setFimVigencia(rs.getDate("datafim"));
        item.setInicioVigencia(rs.getDate("datainicio"));
        item.setDiaSemana(rs.getString("diasemana"));
        item.setCodigoContrato(rs.getInt("contrato"));
        try{
            item.setUrlVideoYoutube(rs.getString("urlVideoYoutube"));
        }catch (Exception e){
        }

        try {
            item.setJaMarcouEuQuero(rs.getBoolean("jaMarcouEuQuero"));
        } catch (Exception e) {
        }
        try {
            item.setOcupacao(rs.getInt("ocupacao"));
        } catch (Exception e) {
            item.setOcupacao(0);
        }
        try {
            item.setLocal(rs.getString("nomeambiente"));
        } catch (Exception e) {
        }        
        try {
            final String fotoKey = rs.getString("fotoProfessor");
            if (!UteisValidacao.emptyString(fotoKey)) {
                item.setFotoProfessor(String.format("%s/%s", new Object[]{
                    PropsService.getPropertyValue(PropsService.urlFotosNuvem), fotoKey}));
            }
            final String fotoModalidade = rs.getString("fotoModalidade");
            if (!UteisValidacao.emptyString(fotoModalidade)) {
                item.setFotoModalidade(String.format("%s/%s", new Object[]{
                        PropsService.getPropertyValue(PropsService.urlFotosNuvem), fotoModalidade}));
            }
        } catch (Exception e) {
        }        
        return item;
    }
    
    public static AgendaTotalTO montarDadosAgendaNova(ResultSet rs) throws Exception{
        AgendaTotalTO item = new AgendaTotalTO();
        item.setEmpresa(rs.getInt("empresa"));
        item.setAulaCheia(rs.getBoolean("aulaColetiva"));
        item.setInicio(rs.getString("horainicial"));
        item.setFim(rs.getString("horafinal"));
        item.setTitulo(rs.getString("identificador"));
        item.setTipo(rs.getString("nomemodalidade"));
        item.setCodigoTipo(rs.getInt("modalidade"));
        item.setCodigoLocal(rs.getInt("ambiente"));
        item.setNrVagas(rs.getInt("nrmaximoaluno"));
        item.setResponsavel(rs.getString("nomeprofessor"));
        item.setCodigoResponsavel(rs.getInt("professor"));
        item.setId(String.valueOf(rs.getInt("horarioturma")));
        item.setFimVigencia(rs.getDate("datafim"));
        item.setInicioVigencia(rs.getDate("datainicio"));
        item.setDiaSemana(rs.getString("diasemana"));
        try {
            item.setJaMarcouEuQuero(rs.getBoolean("jaMarcouEuQuero"));
            item.setCodigoMatriculaAlunoHorarioTurma(rs.getInt("codigomatriculaalunohorarioturma"));
            item.setTurma(rs.getInt("turma"));
        } catch (Exception e) {
        }
        try {
            item.setOcupacao(rs.getInt("ocupacao"));
        } catch (Exception e) {
        }
        
        return item;
    }
    
    @Override
    public List<AgendaTotalJSON> consultarProximasAulasModalidadesDiferentes(Integer matricula, Map<Date, List<Integer>> mapaDesmarcados)throws Exception{
        List<AgendaTotalJSON> aulas = new ArrayList<AgendaTotalJSON>();
        Map<Integer, Map<Date, AgendaTotalJSON>> mapa;
        try (ResultSet rs = criarConsulta(sqlConsultaMatriculaHorarioTurma(matricula, null, null,false, null, null), con)) {
            mapa = new HashMap<Integer, Map<Date, AgendaTotalJSON>>();
            while (rs.next()) {
                Integer codigoHorarioTurma = rs.getInt("horarioturma");
                AgendaTotalJSON item = montarDadosAgenda(rs);
                Map<Date, AgendaTotalJSON> mapHour = mapa.get(rs.getInt("turma"));
                if (mapHour == null) {
                    mapHour = new HashMap<Date, AgendaTotalJSON>();
                    mapa.put(rs.getInt("turma"), mapHour);
                }
                Date dataReferencia = Calendario.hoje();
                Date proximaAula = null;
                while (proximaAula == null) {
                    Date possivelProximaAula = proximaAula(rs.getString("horainicial"), rs.getDate("datainicio"), rs.getString("diasemana"), dataReferencia);
                    if (mapaDesmarcados.get(Calendario.getDataComHoraZerada(possivelProximaAula)) == null
                            || !mapaDesmarcados.get(Calendario.getDataComHoraZerada(possivelProximaAula)).contains(codigoHorarioTurma)) {
                        proximaAula = possivelProximaAula;
                    } else {
                        dataReferencia = Uteis.somarDias(possivelProximaAula, 1);
                    }
                }
                consultaOcupacoes(proximaAula,item,con);
                item.setInicio(Uteis.getData(proximaAula) + " " + item.getInicio());
                item.setFim(Uteis.getData(proximaAula) + " " + item.getFim());
                mapHour.put(proximaAula, item);
            }
        }
        for(Map<Date,AgendaTotalJSON> mapaData : mapa.values()){
            try {
                List<Date> datas = new ArrayList<Date>(mapaData.keySet());
                Collections.sort(datas);
                aulas.add(mapaData.get(datas.get(0)));
            } catch (Exception e) {
            }
        }
        return aulas;
    }
    
    @Override
    public List<AgendaTotalJSON> consultarProximasAulasAulaCheia(Integer matricula)throws Exception{
        List<AgendaTotalJSON> aulas = new ArrayList<AgendaTotalJSON>();
        Map<Integer, Map<String, AgendaTotalJSON>> mapa;
        try (ResultSet rs = criarConsulta(sqlConsultaAulasAulaCheia(matricula, Calendario.hoje()), con)) {
            mapa = new HashMap<Integer, Map<String, AgendaTotalJSON>>();
            while (rs.next()) {
                AgendaTotalJSON item = montarDadosAgenda(rs);
                Map<String, AgendaTotalJSON> mapHour = mapa.get(rs.getInt("turma"));
                if (mapHour == null) {
                    mapHour = new HashMap<String, AgendaTotalJSON>();
                    mapa.put(rs.getInt("turma"), mapHour);
                }
                item.setInicio(Uteis.getData(rs.getDate("datainicio")) + " " + item.getInicio());
                item.setFim(Uteis.getData(rs.getDate("datainicio")) + " " + item.getFim());
                mapHour.put(Uteis.getDataAplicandoFormatacao(rs.getDate("datainicio"), "ddMMyyyy").concat(rs.getString("horainicial")), item);
            }
        }
        //consultar agendadas em outra unidade
        try {
            String sql = "select valorcampoalterado from log l " +
                    "inner join cliente c on c.pessoa = l.pessoa\n" +
                    "where nomeentidade = 'ALUNO_AULA_OUTRA_UNIDADE' and matricula = '" + matricula + "'";
            try (ResultSet rs = criarConsulta(sql, con)) {
                while (rs.next()) {
                    String valorcampoalterado = rs.getString("valorcampoalterado");
                    String dia = valorcampoalterado.split("início às ")[1];
                    AgendaTotalJSON item = new AgendaTotalJSON();
                    item.setAulaCheia(true);
                    item.setMatricula(matricula);
                    item.setTitulo(valorcampoalterado.split("aula ")[1].split(" com início")[0]);
                    Map<String, AgendaTotalJSON> mapHour = mapa.get(0);
                    if (mapHour == null) {
                        mapHour = new HashMap<String, AgendaTotalJSON>();
                        mapa.put(0, mapHour);
                    }
                    item.setInicio(dia);
                    mapHour.put(Uteis.getDataAplicandoFormatacao(rs.getDate("datainicio"), "ddMMyyyy").concat(rs.getString("horainicial")), item);
                }
            }
        }catch (Exception e){
            Uteis.logar(e, MatriculaAlunoHorarioTurma.class);
        }
        for(Map<String,AgendaTotalJSON> mapaData : mapa.values()){
            try {
                aulas.addAll(mapaData.values());
            } catch (Exception e) {
            }
        }
        return aulas;
    }
    
    private Date proximaAula(String horaInicio, Date inicioMatricula, String diaSemana, Date data){
        DiaSemana diaSemanaEnum = DiaSemana.getDiaSemana(diaSemana);
        if(Calendario.maior(inicioMatricula,data)){
            return Calendario.getDataComHora(Calendario.proximoDiaSemana(diaSemanaEnum.getNumeral(), inicioMatricula), horaInicio);
        }
        DiaSemana diaSemanaHoje = DiaSemana.getDiaSemana(Uteis.obterDiaSemanaData(data));
        Date proximaData = diaSemanaEnum.equals(diaSemanaHoje) ? 
                Calendario.getDataComHora(data, horaInicio)
                : Calendario.getDataComHora(Calendario.proximoDiaSemana(diaSemanaEnum.getNumeral(), data), horaInicio);
        return proximaData.before(data) ?
                Calendario.getDataComHora(Calendario.proximoDiaSemana(diaSemanaEnum.getNumeral(), proximaData), horaInicio) :
                proximaData;
    }

    public List<HorarioTurmaVO>consultarAulasPrevistas(Integer codigoContrato)throws Exception{
        List<HorarioTurmaVO>listaHorarioTurma  = getFacade().getHorarioTurma().consultar(codigoContrato, Uteis.NIVELMONTARDADOS_TODOS);
        List<HorarioTurmaVO> listaAulasPrevistas = new ArrayList<HorarioTurmaVO>();
        try (PreparedStatement pst = con.prepareStatement("select * from matriculaalunohorarioturma where contrato = ? and horarioTurma = ? order by datafim desc limit 1")) {
            for (HorarioTurmaVO horarioTurmaVO : listaHorarioTurma) {
                pst.setInt(1, codigoContrato);
                pst.setInt(2, horarioTurmaVO.getCodigo());
                try (ResultSet rs = pst.executeQuery()) {
                    if (rs.next()) {
                        List<Date> listaDias = Uteis.getDiasEntreDatas(rs.getDate("dataInicio"), rs.getDate("dataFim"));
                        for (Date dia : listaDias) {
                            Calendar diaVerificar = Calendario.getInstance();
                            diaVerificar.setTime(dia);
                            if (horarioTurmaVO.getDiaSemanaNumero() == diaVerificar.get(Calendar.DAY_OF_WEEK)) {
                                Date aula = aulaFutura(horarioTurmaVO, diaVerificar);
                                if (aula != null) {
                                    HorarioTurmaVO objClone = (HorarioTurmaVO) horarioTurmaVO.getClone(true);
                                    objClone.setDataAula(aula);
                                    listaAulasPrevistas.add(objClone);
                                }
                            }
                        }
                    }
                }
            }
        }
        // consultar as aulas futuras marcadas com crédito extra.
        Reposicao reposicaoDAO = new Reposicao(this.con);
        HorarioTurma horarioTurmaDAO = new HorarioTurma(this.con);
        List<ReposicaoVO> listaAulaMarcadaComCreditoExtra = reposicaoDAO.consultarAulaMarcadaComCreditoExtra(codigoContrato, Calendario.hoje(),Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        for (ReposicaoVO reposicaoVO: listaAulaMarcadaComCreditoExtra){
            HorarioTurmaVO horarioTurmaVO = horarioTurmaDAO.consultarPorCodigo(reposicaoVO.getHorarioTurma().getCodigo(), Uteis.NIVELMONTARDADOS_TODOS);
            horarioTurmaVO.setDataAula(reposicaoVO.getDataReposicao());
            listaAulasPrevistas.add(horarioTurmaVO);
        }
        Collections.sort(listaAulasPrevistas, HorarioTurmaVO.COMPARATOR_DATA_AULA);
        return listaAulasPrevistas;
    }

    private Date aulaFutura(HorarioTurmaVO horarioTurmaVO, Calendar diaVerificar){
        String[]horaFinal = horarioTurmaVO.getHoraFinal().split(":");
        diaVerificar.set(Calendar.HOUR_OF_DAY, Integer.parseInt(horaFinal[0]));
        diaVerificar.set(Calendar.MINUTE, Integer.parseInt(horaFinal[1]));
        diaVerificar.set(Calendar.SECOND, 0);
        diaVerificar.set(Calendar.MILLISECOND, 0);
        boolean flag = false;
        if (Calendario.igual(diaVerificar.getTime(), Calendario.hoje())){
            flag = (Calendario.maiorOuIgualComHora(diaVerificar.getTime(), Calendario.hoje()));
        }else{
            flag = (Calendario.maior(diaVerificar.getTime(), Calendario.hoje()));
        }
        return (flag) ? diaVerificar.getTime() : null;
    }
    
    @Override
    public List<AgendaTotalJSON> consultarAulasProximos30dias(Integer matricula,  Date agora,
                                                              Map<Date, List<Integer>> mapaDesmarcados,
                                                              Integer saldo)throws Exception{

        Set<Date> diasFeriados = null;
        Integer empresa = 0;
        EmpresaVO empresaVO = null;
        List<AgendaTotalJSON> aulas = new ArrayList<AgendaTotalJSON>();
        List<AgendaTotalJSON> aulasMarcadas;
        Map<Integer, String> mapadiasSemana;
        Date limite;
        try (ResultSet rs = criarConsulta(sqlConsultaMatriculaHorarioTurma(matricula, null, null,true, null, null), con)) {
            aulasMarcadas = new ArrayList<AgendaTotalJSON>();
            mapadiasSemana = new HashMap<Integer, String>();
            limite = agora;
            while (rs.next()) {
                limite = Uteis.somarDias(rs.getDate("vigenciaateajustada"), 1);
                mapadiasSemana.put(rs.getInt("horarioturma"), rs.getString("diasemana"));
                aulasMarcadas.add(montarDadosAgenda(rs));
                empresa = rs.getInt("empresa");
            }
            if (limite.compareTo(Uteis.somarDias(agora, 30)) > 0) {
                limite = Uteis.somarDias(Calendario.hoje(), 30);
            }
        }
        if (empresa > 0) {
            if (UteisValidacao.notEmptyNumber(empresa)) {
                Feriado feriadoDAO = new Feriado(this.con);
                Empresa empresaDAO = new Empresa(this.con);
                empresaVO = empresaDAO.consultarPorChavePrimaria(empresa, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
                diasFeriados = feriadoDAO.consultarPorPeriodoEmpresaAulaCheia(Calendario.hoje(), limite, empresaVO);
            }
        }

        Integer quantidadeCredtidoDisponivel;
        try (ResultSet qtd = criarConsulta("select saldocreditotreino,codigocliente,matricula  from situacaoclientesinteticodw  where   matricula = " + matricula, con)) {
            quantidadeCredtidoDisponivel = 0;
            while (qtd.next()) {
                try {
                    quantidadeCredtidoDisponivel = qtd.getInt("saldocreditotreino");
                } catch (Exception e) {
                }
            }
        }

        List<Date> diasEntreDatas = Uteis.getDiasEntreDatas(Calendario.hoje(), limite);
        for(Date dia : diasEntreDatas){
            for(AgendaTotalJSON agTmp : aulasMarcadas){
                AgendaTotalJSON ag = (AgendaTotalJSON) SerializationUtils.clone(agTmp);
                if (!UteisValidacao.emptyList(aulas) && (aulas.size() == (quantidadeCredtidoDisponivel - saldo))) {
                    break;
                }

                List<Integer> horariosDesmarcados = mapaDesmarcados.get(Calendario.getDataComHoraZerada(dia));
                DiaSemana diaSemana = DiaSemana.getDiaSemana(Uteis.obterDiaSemanaData(dia));
                Date agDia = Calendario.getDataComHora(dia, ag.getInicio());
                if(agDia.after(agora)
                        && (horariosDesmarcados == null || !horariosDesmarcados.contains(Integer.valueOf(ag.getId())))
                        && Calendario.maiorOuIgual(dia, ag.getInicioVigencia())
                        && Calendario.menorOuIgual(dia, ag.getFimVigencia())
                        && diaSemana.getCodigo().equals(mapadiasSemana.get(Integer.valueOf(ag.getId())))){
                    if (diasFeriados != null ) {
                        if (empresaVO.isPermMarcarAulaFeriado() && isFeriado(dia, diasFeriados)) {
                            final Long horaAbertura = Calendario.pegaHoraEmMilisegundos(empresaVO.getHoraAberturaFeriado());
                            final Long horaFechamento = Calendario.pegaHoraEmMilisegundos(empresaVO.getHoraFechamentoFeriado());
                            final Long horaInicio = Calendario.pegaHoraEmMilisegundos(ag.getInicio());
                            final Long horaFim = Calendario.pegaHoraEmMilisegundos(ag.getFim());

                            if (!isHorarioIntervaloNaoPermitido(horaAbertura, horaFechamento, horaInicio, horaFim)) {
                                consultaOcupacoes(dia,ag,con);
                                aulas.add(new AgendaTotalJSON(dia, ag));
                            }
                        } else {
                            if (!isFeriado(dia, diasFeriados)) {
                                consultaOcupacoes(dia,ag,con);
                                aulas.add(new AgendaTotalJSON(dia, ag));
                            }
                        }
                    }
                }
            }
        }
        return aulas;
    }

    private boolean isFeriado(Date dia, Set<Date> diasFeriados) {
        for (Date dataFeriado : diasFeriados) {
            if (Calendario.dataNoMesmoDiaMes(dataFeriado, dia)) {
                return true;
            }
        }
        return false;
    }

    private boolean isHorarioIntervaloNaoPermitido(final Long horaAbertura, final Long horaFechamento,
                                                   final Long horaInicio, final Long horaFim) {
        return (horaAbertura != null && horaFechamento != null && horaInicio != null && horaFim != null)
                && (horaInicio < horaAbertura || horaInicio  >= horaFechamento || horaFim > horaFechamento);
    }

    @Override
    public List<AgendaTotalJSON> consultarAulasDesmarcadasSemReposicao(Integer matricula, Date inicio, Date fim, Integer modalidade)throws Exception{
        String validaIdade = "";
        List<AgendaTotalJSON> aulas = new ArrayList<AgendaTotalJSON>();
        List<Date> diasFeriados = null;
        EmpresaVO empresaVO = null;
        Integer empresa;
        Integer pessoa = 0;
        try (ResultSet codigoEmpresa = criarConsulta("select p.codigo as codigoPessoa, p.datanasc, c.empresa " +
                "from pessoa as p inner join cliente as c on c.pessoa = p.codigo " +
                "where c.codigomatricula = " + matricula, con)) {
            empresa = 0;
            while (codigoEmpresa.next()) {
                try {
                    pessoa = codigoEmpresa.getInt("codigoPessoa");
                    empresa = codigoEmpresa.getInt("empresa");
                    validaIdade = Uteis.CalculaIdadeComMeses(codigoEmpresa.getDate("datanasc").toString());
                } catch (Exception e) {
                    Logger.getLogger(getClass().getName()).log(Level.SEVERE, null, e);
                }
            }
            if (UteisValidacao.emptyNumber(pessoa)) {
                throw new Exception("Nenhum cliente encontrado.");
            }
        }
        Empresa empresaDAO = new Empresa(this.con);
        Feriado feriadoDAO = new Feriado(this.con);
        empresaVO = empresaDAO.consultarPorChavePrimaria(empresa, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
        if (inicio != null) {
            diasFeriados = feriadoDAO.consultarPorPeriodoEmpresa(inicio, fim, empresaVO);
        }
        StringBuilder consulta = new StringBuilder(sqlConsultaAulasDesmarcadasSemReposicao(matricula, empresaVO));
        if (UteisValidacao.notEmptyNumber(modalidade)) {
            Modalidade modalidadDAO = new Modalidade(this.con);
            ModalidadeVO modalidadeVO = modalidadDAO.consultarPorChavePrimaria(modalidade, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            if (UtilReflection.objetoMaiorQueZero(modalidadeVO, "getCodigo()")) {
                if (UteisValidacao.notEmptyNumber(modalidadeVO.getTipo())) {
                    consulta.append(" \n and m.tipo = ").append(modalidadeVO.getTipo());
                } else {
                    consulta.append(" \n and m.codigo = ").append(modalidadeVO.getCodigo());
                }
            }
        }
        consulta.append(" \n order by ad.dataorigem asc \n");
        try (ResultSet rs = criarConsulta(consulta.toString(), con)) {
            Date dataLimiteAulasRepor = null;
            if (UteisValidacao.notEmptyNumber(empresaVO.getTempoAposFaltaReposicao())) {
                dataLimiteAulasRepor = Uteis.obterDataAnterior(Calendario.hoje(), empresaVO.getTempoAposFaltaReposicao());
            }
            while (rs.next()) {
                String[] split = validaIdade.split("-");
                int idadeAlunoAno = 0;
                int idadeAlunoMes = 0;
                if(isNotBlank(split[0])) {
                    idadeAlunoAno = Integer.parseInt(split[0]);
                    idadeAlunoMes = Integer.parseInt(split[1]);
                }
                boolean isIdadeAlunoValida = validaIdadeAluno(
                        idadeAlunoAno,
                        idadeAlunoMes,
                        rs.getInt("idademinima"),
                        rs.getInt("idademinimameses"),
                        rs.getInt("idademaxima"),
                        rs.getInt("idademaximameses")
                );
                if (isIdadeAlunoValida) {

                    Date date = rs.getDate("dataorigem");

                    if (dataLimiteAulasRepor == null
                            || Calendario.maiorOuIgual(date, dataLimiteAulasRepor)) {
                        if (inicio == null || fim == null
                                || Calendario.igual(date, inicio)
                                || Calendario.igual(date, fim)
                                || Calendario.entre(date, inicio, fim)) {
                            if (diasFeriados != null) {
                                if (empresaVO.isPermMarcarAulaFeriado()) {
                                    final Long horaAbertura = Calendario.pegaHoraEmMilisegundos(empresaVO.getHoraAberturaFeriado());
                                    final Long horaFechamento = Calendario.pegaHoraEmMilisegundos(empresaVO.getHoraFechamentoFeriado());
                                    final Long horaInicio = Calendario.pegaHoraEmMilisegundos(rs.getString("horainicial"));
                                    final Long horaFim = Calendario.pegaHoraEmMilisegundos(rs.getString("horafinal"));

                                    if (!isHorarioIntervaloNaoPermitido(horaAbertura, horaFechamento, horaInicio, horaFim)) {
                                        aulas.add(new AgendaTotalJSON(date, montarDadosAgenda(rs)));
                                    }
                                }
                            } else {
                                aulas.add(new AgendaTotalJSON(date, montarDadosAgenda(rs)));
                            }
                        }
                    }
                }
            }
        }
        return aulas;
    }

    private boolean validaIdadeAluno(int idadeAnoAluno, int idadeMesAluno, int idadeAnoMinima,
                                     int idadeMesMinima, int idadeAnoMaxima, int idadeMesMaxima) throws SQLException {
        if ((idadeAnoAluno < idadeAnoMinima) || (idadeAnoMinima == idadeAnoAluno && idadeMesAluno < idadeMesMinima)) {
            return false;
        } else if ((idadeAnoAluno > idadeAnoMaxima) || idadeAnoAluno == idadeAnoMaxima && idadeMesAluno > idadeMesMaxima){
            return false;
        }
    return true;
    }

    @Override
    public List<AgendaTotalJSON> consultarTurmasAluno(Integer matricula)throws Exception{
        List<AgendaTotalJSON> aulasMarcadas;
        try (ResultSet rs = criarConsulta(sqlConsultaMatriculaHorarioTurma(matricula, null, Calendario.hoje(),false, null, null), con)) {
            aulasMarcadas = new ArrayList<AgendaTotalJSON>();
            while (rs.next()) {
                aulasMarcadas.add(montarDadosAgenda(rs));
            }
        }
        return aulasMarcadas;
    }
    
    
    public String sqlConsultaAulasAulaCheia(Integer matricula, Date dia) throws Exception{
        StringBuilder sql = new StringBuilder();
        sql.append("   SELECT t.codigo as turma, t.empresa,\n");
        sql.append(" t.identificador,t.aulaColetiva, m.codigo as modalidade, m.nome as nomemodalidade,\n" );
        sql.append(" ht.diasemana,ht.professor,p.nome as nomeprofessor, ht.ambiente,  \n" );
        sql.append(" ht.horainicial, ht.horafinal, ht.nrmaximoaluno, ht.codigo as horarioturma,maht.dia as datainicio,maht.dia as datafim, sc.codigocontrato as contrato,\n" );
        sql.append(" p.fotokey as fotoProfessor, \n");
        sql.append(" t.urlVideoYoutube \n");
        sql.append(" FROM alunohorarioturma maht  \n" );
        sql.append(" INNER JOIN situacaoclientesinteticodw sc ON maht.cliente = sc.codigocliente \n" );
        sql.append(" INNER JOIN horarioturma ht ON ht.codigo = maht.horarioturma\n" );
        sql.append(" INNER JOIN colaborador c ON c.codigo = ht.professor\n" );
        sql.append(" INNER JOIN pessoa p ON p.codigo = c.pessoa\n" );
        sql.append(" INNER JOIN turma t ON t.codigo = ht.turma\n" );
        sql.append(" INNER JOIN modalidade m ON t.modalidade = m.codigo\n" );
//        sql.append(" INNER JOIN contrato con ON con.codigo = sc.codigocontrato AND con.situacao = 'AT'\n" );
        sql.append(" WHERE sc.matricula = ").append(matricula);
        sql.append(" and maht.dia >= '").append(Uteis.getDataJDBC(dia)).append("'");
        
        return sql.toString();
    }
    
    @Override
    public List<AgendaTotalJSON> consultarAulasDia(Integer matricula, Date agora, List<AgendadoJSON> reposicoesOrigemDia, Integer modalidade, Integer tipoModalidade) throws Exception {
        Date dia = Calendario.getDataComHoraZerada(agora);
        List<String> aulasComReposicao = new ArrayList<String>();
        for (AgendadoJSON r : reposicoesOrigemDia) {
            aulasComReposicao.add(r.getId_agendamento());
        }
        List<AgendaTotalJSON> aulas = new ArrayList<AgendaTotalJSON>();
        String diaDaSemana = Uteis.obterDiaSemanaData(agora);

        List<AgendaTotalJSON> aulasMarcadas;
        Map<Integer, String> mapadiasSemana;
        AulaDesmarcada aulaDesmarcadaDao;
        try (ResultSet rs = criarConsulta(sqlConsultaMatriculaHorarioTurma(matricula, diaDaSemana, null,false, modalidade, tipoModalidade), con)) {
            aulasMarcadas = new ArrayList<AgendaTotalJSON>();
            mapadiasSemana = new HashMap<Integer, String>();
            aulaDesmarcadaDao = new AulaDesmarcada(con);
            while (rs.next()) {
                if (!(aulaDesmarcadaDao.desmarcouAulaParaNaoPermitirReposicao(rs.getInt("contrato"), rs.getInt("horarioturma"), dia))) {
                    mapadiasSemana.put(rs.getInt("horarioturma"), rs.getString("diasemana"));
                    aulasMarcadas.add(montarDadosAgenda(rs));
                }
            }
        }
        aulaDesmarcadaDao = null;
        for (AgendaTotalJSON ag : aulasMarcadas) {
            DiaSemana diaSemana = DiaSemana.getDiaSemana(Uteis.obterDiaSemanaData(dia));
            Date agDia = Calendario.getDataComHora(dia, ag.getInicio());
            if (agDia.after(dia)
                    && !aulasComReposicao.contains(ag.getId())
                    && Calendario.maiorOuIgual(dia, ag.getInicioVigencia())
                    && Calendario.menorOuIgual(dia, ag.getFimVigencia())
                    && diaSemana.getCodigo().equals(mapadiasSemana.get(Integer.valueOf(ag.getId())))) {
                aulas.add(new AgendaTotalJSON(dia, ag));
            }
        }
        return aulas;
    }
    
    @Override
    public List<MatriculaAlunoHorarioTurmaVO> consultarMatriculasPorMaxDataFim(int contrato) throws Exception {
        String sql = "SELECT maht.* FROM MatriculaAlunoHorarioTurma  maht \n" +
                "WHERE maht.contrato =  " + contrato + " and maht.datafim = (select max(datafim) from MatriculaAlunoHorarioTurma where contrato = maht.contrato); ";
        try (Statement sqlConsultar = con.createStatement()) {
            try (ResultSet tabelaResultado = sqlConsultar.executeQuery(sql)) {
                return montarDadosConsulta(tabelaResultado, Uteis.NIVELMONTARDADOS_MINIMOS, getCon());
            }
        }
    }
    
    public boolean existeMatriculaVigenteNoHorario(final Date dataverificar,
            final int contrato, final int horarioTurma) throws Exception{
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT EXISTS ( \n");
        sql.append(" select * from matriculaalunohorarioturma  where  \n");
        sql.append(" contrato =  ").append(contrato);
        if(!UteisValidacao.emptyNumber(horarioTurma)){
            sql.append(" and horarioturma  = ").append(horarioTurma);
        }    
        sql.append(" AND '").append(Uteis.getDataJDBC(dataverificar)).append("'  between datainicio and datafim");
        sql.append(" )  AS validar");
        try (ResultSet rs = criarConsulta(sql.toString(), con)) {
            rs.next();
            return rs.getBoolean("validar");
        }
    }

    @Override
    public List<AgendaTotalTO> consultarAulas(Integer matricula, Date data, Map<Date, List<Integer>> mapaDesmarcados,Integer contrato,boolean habilitarSomaDeAulaNaoVigente,List<MatriculaAlunoHorarioTurmaVO> listaMatriculaHorarioTurma) throws Exception {
        List<AgendaTotalTO> aulas = new ArrayList<AgendaTotalTO>();
        if (!habilitarSomaDeAulaNaoVigente && listaMatriculaHorarioTurma != null &&  listaMatriculaHorarioTurma.isEmpty()) {
            return aulas;
        }
        List<AgendaTotalTO> aulasMarcadas;
        Map<Integer, String> mapadiasSemana;
        Date inicio;
        try (ResultSet rs = criarConsulta(sqlConsultaMatriculaHorarioTurma(matricula, contrato, habilitarSomaDeAulaNaoVigente, listaMatriculaHorarioTurma), con)) {
            aulasMarcadas = new ArrayList<AgendaTotalTO>();
            mapadiasSemana = new HashMap<Integer, String>();
            inicio = null;
            while (rs.next()) {
                if (inicio == null || Calendario.menor(rs.getDate("vigenciade"), inicio)) {
                    inicio = rs.getDate("vigenciade");
                }
                mapadiasSemana.put(rs.getInt("horarioturma"), rs.getString("diasemana"));
                aulasMarcadas.add(montarDadosAgendaNova(rs));
            }
        }

        if (inicio != null) {
            List<Date> diasEntreDatas = Uteis.getDiasEntreDatas(inicio, Calendario.hoje());
            for(Date dia : diasEntreDatas){
                for(AgendaTotalTO ag : aulasMarcadas){
                    List<Integer> horariosDesmarcados = mapaDesmarcados.get(Calendario.getDataComHoraZerada(dia));
                    DiaSemana diaSemana = DiaSemana.getDiaSemana(Uteis.obterDiaSemanaData(dia));
                    Date agDia = Calendario.getDataComHora(dia, ag.getInicio());
                    if(Calendario.menorOuIgual(agDia, data)
                            && (horariosDesmarcados == null || !horariosDesmarcados.contains(Integer.valueOf(ag.getId())))
                            && Calendario.maiorOuIgual(dia, ag.getInicioVigencia())
                            && Calendario.menorOuIgual(dia, ag.getFimVigencia())
                            && diaSemana.getCodigo().equals(mapadiasSemana.get(Integer.valueOf(ag.getId())))){
                        aulas.add(new AgendaTotalTO(dia, ag));
                    }
                }
            }
        }
        return aulas;
    }

    @Override
    public void inativarMatriculaAlunosHorarioTurmas() throws Exception {
        try {
            con.setAutoCommit(false);
            String sql = "UPDATE matriculaalunohorarioturma SET datafim = NOW() - interval '1 day' WHERE datafim >= NOW()::DATE";
            try (PreparedStatement sqlInativarMatriculaAlunoTurma = con.prepareStatement(sql)) {
                sqlInativarMatriculaAlunoTurma.execute();
            }
            con.commit();
        }catch (Exception e ){

            throw e;
        }
    }

    public String transferirAlunos(List<ConsultarAlunosTurmaVO> listaTransferir, Integer horarioTurmaDestino, UsuarioVO usuarioVO, boolean comitar) throws Exception {
        StringBuilder log = new StringBuilder();
        StringBuilder logErros = new StringBuilder();
        try {
            con.setAutoCommit(false);

            List<ConsultarAlunosTurmaVO> alunosAtuais = consultarPorHorarioTurmaPeriodo(horarioTurmaDestino, Uteis.NIVELMONTARDADOS_DADOSENTIDADESPRINCIPAIS, Calendario.hoje(), null, false, false);

            HorarioTurmaVO horarioTurmaDestinoVO = new HorarioTurma(con).consultarPorChavePrimaria(horarioTurmaDestino, Uteis.NIVELMONTARDADOS_TODOS);
            TurmaVO turmaVO = new Turma(con).consultarPorChavePrimaria(horarioTurmaDestinoVO.getTurma(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            StringBuilder logTurma = new StringBuilder();
            logTurma.append("HORÁRIO DESTINO: \n\n");
            logTurma.append("TURMA: ").append(turmaVO.getDescricao());
            logTurma.append("\nCodigoTurma: ").append(turmaVO.getCodigo());
            logTurma.append("\nCodigoHorarioTurma: ").append(horarioTurmaDestinoVO.getCodigo());
            logTurma.append("\nIdentificador Turma: ").append(turmaVO.getIdentificador());
            logTurma.append("\nProfessor: ").append(horarioTurmaDestinoVO.getProfessor().getPessoa_Apresentar());
            logTurma.append("\nNível: ").append(horarioTurmaDestinoVO.getNivelTurma().getDescricao());
            logTurma.append("\nAmbiente: ").append(horarioTurmaDestinoVO.getAmbiente().getDescricao());
            logTurma.append("\nDia Semana: ").append(horarioTurmaDestinoVO.getDiaSemana_Apresentar());
            logTurma.append("\nHorário: ").append(horarioTurmaDestinoVO.getHoraInicial()).append(" ás ").append(horarioTurmaDestinoVO.getHoraFinal());
            log.append(logTurma.toString());

            log.append("\n\nALUNOS TRANSFERIDOS: \n\n");

            if (UteisValidacao.emptyString(logErros.toString())) {

                Map<Integer, Integer> mapa = new HashMap<Integer, Integer>();
                for (ConsultarAlunosTurmaVO aluno1 : listaTransferir) {
                    Integer alu = mapa.get(aluno1.getClienteVO().getCodigo());
                    if (UteisValidacao.emptyNumber(alu)) {
                        mapa.put(aluno1.getClienteVO().getCodigo(), aluno1.getClienteVO().getCodigo());
                    } else {
                        logErros.append("O aluno ").append(aluno1.getClienteVO().getMatricula()).append(" - ").append(aluno1.getClienteVO().getNome_Apresentar()).append(" foi selecionado mais de uma vez.\n");
                        continue;
                    }

                    for (ConsultarAlunosTurmaVO alunoNoHorario : alunosAtuais) {
                        if (alunoNoHorario.getClienteVO().getCodigo().equals(aluno1.getClienteVO().getCodigo())) {
                            logErros.append("O aluno ").append(aluno1.getClienteVO().getMatricula()).append(" - ").append(aluno1.getClienteVO().getNome_Apresentar()).append(" já está no horário selecionado.\n");
                        }
                    }
                }
            }

            if (UteisValidacao.emptyString(logErros.toString()) && ((alunosAtuais.size() + listaTransferir.size()) > horarioTurmaDestinoVO.getNrMaximoAluno())) {
                logErros.append("Não é possível adicionar os alunos selecionados!\n\nO horário já está com ").append(alunosAtuais.size()).append(" alunos e a capacidade máxima é de ").append(horarioTurmaDestinoVO.getNrMaximoAluno()).append(" alunos !\n\n");
            }

            if (UteisValidacao.emptyString(logErros.toString())) {

                for (ConsultarAlunosTurmaVO aluno : listaTransferir) {

                    if (aluno.getClienteVO().getPessoa().getDataNasc() == null) {
                        logErros.append("O aluno ").append(aluno.getClienteVO().getMatricula()).append(" - ").append(aluno.getClienteVO().getNome_Apresentar()).append(" não tem DATA DE NASCIMENTO preenchida.\n");
                        continue;
                    }

                    Integer mesesAluno = Uteis.getQuantidadeMesesEntreDatas(aluno.getClienteVO().getPessoa().getDataNasc(), Calendario.hoje());
                    Integer mesesIdadeMinimaTurma = Uteis.getMesesIdade(turmaVO.getIdadeMinima()) + turmaVO.getIdadeMinimaMeses();
                    Integer mesesIdadeMaximaTurma = Uteis.getMesesIdade(turmaVO.getIdadeMaxima()) + turmaVO.getIdadeMaximaMeses();

                    if (mesesAluno < mesesIdadeMinimaTurma) {
                        logErros.append("O aluno ").append(aluno.getClienteVO().getMatricula()).append(" - ").append(aluno.getClienteVO().getNome_Apresentar()).append(" não tem a IDADE MÍNIMA permitida.\n");
                        continue;
                    }

                    if (mesesAluno > mesesIdadeMaximaTurma) {
                        logErros.append("O aluno ").append(aluno.getClienteVO().getMatricula()).append(" - ").append(aluno.getClienteVO().getNome_Apresentar()).append(" está acima da IDADE MÁXIMA permitida.\n");
                        continue;
                    }

                    Integer matriculaAlunoHorarioTurma = aluno.getMatriculaAlunoHorarioTurmaVO().getCodigo();
                    Integer pessoa = aluno.getClienteVO().getPessoa().getCodigo();
                    Integer horarioTurma = aluno.getMatriculaAlunoHorarioTurmaVO().getHorarioTurma().getCodigo();

                    Integer contratoModalidadeHorarioTurma = 0;
                    Integer contratoModalidadeTurma = 0;
                    Integer contratoModalidade = 0;
                    boolean temOutrosHorariosNaTurma = false;
                    Integer contratoModalidadeTurmaExistente = 0;
                    Integer contrato = aluno.getMatriculaAlunoHorarioTurmaVO().getContrato().getCodigo();

                    StringBuilder sql = new StringBuilder();
                    sql.append("select  \n");
                    sql.append("c.codigo as contratomodalidadehorarioturma, \n");
                    sql.append("ct.codigo as contratomodalidadeturma, \n");
                    sql.append("co.codigo as contrato, \n");
                    sql.append("exists(select codigo from contratomodalidadehorarioturma where contratomodalidadeturma = ct.codigo and horarioturma <> c.horarioturma) as existeoutrohorarionaturma,\n");
                    sql.append("(select codigo from contratomodalidadeturma where contratomodalidade = cm.codigo and turma = ").append(horarioTurmaDestinoVO.getTurma()).append(") as contratomodalidadeturmaexistente,\n");
                    sql.append("ct.contratomodalidade \n");
                    sql.append("from contratomodalidadehorarioturma  c \n");
                    sql.append("inner join contratomodalidadeturma ct on ct.codigo = c.contratomodalidadeturma \n");
                    sql.append("inner join contratomodalidade cm on cm.codigo = ct.contratomodalidade \n");
                    sql.append("inner join contrato co on co.codigo = cm.contrato \n");
                    sql.append("where co.codigo = ").append(contrato);
                    sql.append(" and co.pessoa = ").append(pessoa).append(" \n");
                    sql.append("and c.horarioturma = ").append(horarioTurma).append(" \n");

                    try (Statement stm = con.createStatement()) {
                        try (ResultSet rs = stm.executeQuery(sql.toString())) {
                            if (rs.next()) {

                                contratoModalidadeHorarioTurma = rs.getInt("contratomodalidadehorarioturma");
                                contratoModalidadeTurma = rs.getInt("contratomodalidadeturma");
                                contratoModalidade = rs.getInt("contratomodalidade");
                                temOutrosHorariosNaTurma = rs.getBoolean("existeoutrohorarionaturma");
                                contratoModalidadeTurmaExistente = rs.getInt("contratomodalidadeturmaexistente");
                            }
                        }
                    }

                    if (UteisValidacao.emptyNumber(contratoModalidadeHorarioTurma) || UteisValidacao.emptyNumber(matriculaAlunoHorarioTurma) || UteisValidacao.emptyNumber(contratoModalidadeTurma)) {
                        logErros.append("Erro ao transferir aluno: ").append(aluno.getClienteVO().getMatricula()).append(" - ").append(aluno.getClienteVO().getNome_Apresentar());
                        continue;
                    } else {
                        if(UteisValidacao.emptyNumber(contratoModalidadeTurmaExistente)){
                            String insertTurma = "insert into contratomodalidadeturma(contratomodalidade, turma) values ("+contratoModalidade+","+ horarioTurmaDestinoVO.getTurma()+") RETURNING codigo;";
                            try (Statement stm = con.createStatement()) {
                                try (ResultSet rsTurma= stm.executeQuery(insertTurma )) {
                                    if (rsTurma.next()) {
                                        contratoModalidadeTurmaExistente += rsTurma.getInt("codigo");
                                    }
                                }
                            }
                        }
                        String insertHorarioTurma = "insert into contratomodalidadehorarioturma(contratomodalidadeturma, horarioturma) values ("+contratoModalidadeTurmaExistente+","+ horarioTurmaDestinoVO.getCodigo()+")";
                        executarConsulta(insertHorarioTurma, con);
                        String deleteHorarioTurma = "delete from contratomodalidadehorarioturma where codigo ="+ contratoModalidadeHorarioTurma;
                        executarConsulta(deleteHorarioTurma, con);
                        if(!temOutrosHorariosNaTurma  && !contratoModalidadeTurmaExistente.equals(contratoModalidadeTurma)) {
                            String deleteTurma = "delete from contratomodalidadeturma where codigo = "+contratoModalidadeTurma;
                            executarConsulta(deleteTurma, con);
                        }
                        String updateMatriculaAluno = "";
                        if(Calendario.menor(aluno.getMatriculaAlunoHorarioTurmaVO().getDataInicio(), Calendario.hoje())){
                            MatriculaAlunoHorarioTurmaVO novoMaht = consultarPorChavePrimaria(matriculaAlunoHorarioTurma, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                            novoMaht.setDataInicio(Calendario.hoje());
                            novoMaht.getHorarioTurma().setCodigo(horarioTurmaDestinoVO.getCodigo());
                            incluirSemComit(novoMaht);
                            updateMatriculaAluno = "update matriculaalunohorarioturma set  datafim = '" +Uteis.getDataJDBC(Uteis.somarDias(Calendario.hoje(), -1))+ "' where codigo = " + matriculaAlunoHorarioTurma;

                        } else {
                            updateMatriculaAluno = "update matriculaalunohorarioturma set horarioturma  = " + horarioTurmaDestinoVO.getCodigo() + " where codigo = " + matriculaAlunoHorarioTurma;
                        }

                        executarConsulta(updateMatriculaAluno, con);

                        StringBuilder logAluno = new StringBuilder();
                        logAluno.append("Matricula: ").append(aluno.getClienteVO().getMatricula()).append(" | Nome: ").append(aluno.getClienteVO().getNome_Apresentar());
                        logAluno.append(" | CodigoTurma: ").append(aluno.getMatriculaAlunoHorarioTurmaVO().getHorarioTurma().getTurma());
                        logAluno.append(" | CodigoHorarioTurma: ").append(aluno.getMatriculaAlunoHorarioTurmaVO().getHorarioTurma().getCodigo());
                        logAluno.append(" | Professor: ").append(aluno.getMatriculaAlunoHorarioTurmaVO().getHorarioTurma().getProfessor().getPessoa_Apresentar());
                        logAluno.append(" | Nível: ").append(aluno.getMatriculaAlunoHorarioTurmaVO().getHorarioTurma().getNivelTurma().getDescricao());
                        logAluno.append(" | Ambiente: ").append(aluno.getMatriculaAlunoHorarioTurmaVO().getHorarioTurma().getAmbiente().getDescricao());
                        logAluno.append(" | Dia Semana: ").append(aluno.getMatriculaAlunoHorarioTurmaVO().getHorarioTurma().getDiaSemana_Apresentar());
                        logAluno.append(" | Horário: ").append(aluno.getMatriculaAlunoHorarioTurmaVO().getHorarioTurma().getHoraInicial()).append(" ás ").append(aluno.getMatriculaAlunoHorarioTurmaVO().getHorarioTurma().getHoraFinal());
                        logAluno.append("\n");
                        log.append(logAluno.toString());

                        inserirLog(logAluno.toString(), logTurma.toString(), contrato, pessoa, usuarioVO, "");

                    }
                }
            }

            if (logErros.length() > 0 || !comitar) {
                throw new Exception(logErros.toString());
            }

            con.commit();
        } catch (Exception e) {
            log.setLength(0);
            log.append("ERRO:").append(e.getMessage());
            con.rollback();
        } finally {
            con.setAutoCommit(true);
        }
        return log.toString();
    }

    private void inserirLog(String horarioAnterior, String horarioAtual, Integer contrato, Integer pessoa, UsuarioVO usuarioVO, String script) throws Exception {
        LogVO log = new LogVO();
        log.setOperacao("MANUTENÇÃO DE TURMA");
        log.setChavePrimaria(contrato.toString());
        log.setDataAlteracao(Calendario.hoje());
        log.setDescricao("MANUTENÇÃO DE TURMA - GESTÃO DE TURMA");
        log.setPessoa(pessoa);
        log.setNomeEntidade("CONTRATO");
        log.setNomeEntidadeDescricao("CONTRATO");
        log.setResponsavelAlteracao(usuarioVO.getNome());
        log.setUserOAMD(usuarioVO.getUserOamd());
        log.setUsuarioVO(usuarioVO);
        log.setNomeCampo("HORARIO TURMA");
        log.setValorCampoAnterior(horarioAnterior);
        log.setValorCampoAlterado(horarioAtual + " \n\n\n\n\n" + script);
        getFacade().getLog().incluirSemCommit(log);
    }


    private String sqlConsultaAulasDesmarcadasSemReposicao(Integer matricula, EmpresaVO empresaVO) throws Exception{
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT t.codigo as turma, t.empresa, t.idademinima, t.idademinimameses, t.idademaxima, t.idademaximameses, \n");
        sql.append("t.identificador,t.aulaColetiva, m.codigo as modalidade, m.nome as nomemodalidade,  \n");
        sql.append("nt.descricao, CASE WHEN (con.vigenciade >= '"+Uteis.getDataJDBC(Calendario.hoje())+"' and ad.contratoanterior is not null) THEN ad.contratoanterior ELSE ad.contrato END as contrato, \n");
        sql.append("ht.diasemana,ht.professor,p.nome as nomeprofessor, ht.ambiente, a.descricao as nomeambiente, con.vigenciaateajustada, \n");
        sql.append(" ht.horainicial, ht.horafinal, ht.nrmaximoaluno, ht.codigo as horarioturma,null as datainicio,null as datafim, con.vigenciade,null as codigomatriculaalunohorarioturma, \n");
        sql.append("p.fotokey as fotoProfessor, \n");
        sql.append("exists(select codigo from demandahorarioturma dht where dht.cliente = sc.codigocliente AND dht.horarioturma = ht.codigo) as jaMarcouEuQuero, \n");
        sql.append("(select count(*) from matriculaalunohorarioturma mat2 where mat2.horarioturma = ht.codigo and current_date between mat2.datainicio and mat2.datafim) as ocupacao \n");
        sql.append(",ad.dataorigem \n");
        sql.append("FROM auladesmarcada ad \n");
        sql.append("INNER JOIN situacaoclientesinteticodw sc ON ad.cliente= sc.codigocliente \n");
        sql.append("INNER JOIN horarioturma ht ON ht.codigo = ad.horarioturma \n");
        sql.append("INNER JOIN colaborador c ON c.codigo = ht.professor \n");
        sql.append("INNER JOIN pessoa p ON p.codigo = c.pessoa \n");
        sql.append("INNER JOIN turma t ON t.codigo = ht.turma \n");
        sql.append("INNER JOIN nivelturma nt ON nt.codigo = ht.nivelturma \n");
        sql.append("INNER JOIN modalidade m ON t.modalidade = m.codigo \n");
        sql.append("INNER JOIN contrato con ON con.codigo = ad.contrato and con.situacao = 'AT' \n");
        sql.append("INNER JOIN ambiente a ON a.codigo = ht.ambiente  \n");
        sql.append("WHERE sc.matricula = ").append(matricula).append(" AND (con.situacao = 'AT' or\n");
        sql.append("ad.contrato = sc.codigocontrato");
        if (empresaVO.isAdicionarAulasDesmarcadasContratoAnterior()) {
            sql.append(" or ad.contratoanterior = sc.codigocontrato\n");
        }
        sql.append(") and ad.datareposicao IS null and permiteReporAulaDesmarcada = true \n");
        sql.append("and m.utilizarturma = true \n");

        return sql.toString();
    }


    public List<ConsultarAlunosTurmaVO> contarEntradaPorModalidadeNoPeriodo(Date dataInicio, Date dataFim, ModalidadeVO modalidadeVO, Integer codEmpresa,TurmaVO turmaVO) throws Exception {
        String sql = "select distinct maht.pessoa \n" +
                "from matriculaalunohorarioturma maht\n" +
                "inner join horarioturma ht ON maht.horarioturma = ht.codigo\n" +
                "inner join turma t ON ht.turma = t.codigo\n" +
                "where t.modalidade = ?\n" +
                "and datainicio BETWEEN ? and ?\n" +
                "and maht.empresa = ?";
        if(turmaVO != null && !UteisValidacao.emptyNumber(turmaVO.getCodigo())){
            sql += " and t.codigo = ?";
        }
        List<ConsultarAlunosTurmaVO> listaConsultaAlunos = new ArrayList<>();
        try (PreparedStatement stm = con.prepareStatement(sql)) {
            int i = 0;
            stm.setInt(++i, modalidadeVO.getCodigo());
            stm.setDate(++i, Uteis.getDataJDBC(dataInicio));
            stm.setDate(++i, Uteis.getDataJDBC(dataFim));
            stm.setInt(++i, codEmpresa);
            if(turmaVO !=null && !UteisValidacao.emptyNumber(turmaVO.getCodigo())){
                stm.setInt(++i, turmaVO.getCodigo());
            }
            try (ResultSet rs = stm.executeQuery()) {
                List<MatriculaAlunoHorarioTurmaVO> matriculas = new ArrayList<>();
                while (rs.next()) {
                    MatriculaAlunoHorarioTurmaVO matriculaAlunoHorarioTurmaVO = new MatriculaAlunoHorarioTurmaVO();
                    PessoaVO pessoaVO = new PessoaVO();
                    pessoaVO.setCodigo(rs.getInt("pessoa"));
                    matriculaAlunoHorarioTurmaVO.setPessoa(pessoaVO);
                    matriculas.add(matriculaAlunoHorarioTurmaVO);
                }
                Map<Integer, ClienteVO> mapClientes = new HashMap<>();

                for (MatriculaAlunoHorarioTurmaVO matriculaAlunoHorarioTurmaVO : matriculas) {
                    Integer codPessoa = matriculaAlunoHorarioTurmaVO.getPessoa().getCodigo();
                    ClienteVO clienteVO = mapClientes.get(codPessoa);
                    if (clienteVO == null) {
                        Cliente clienteDAO = new Cliente(this.con);
                        clienteVO = clienteDAO.consultarPorCodigoPessoa(codPessoa, Uteis.NIVELMONTARDADOS_RELATORIO_SGP);
                        mapClientes.put(codPessoa, clienteVO);
                    }

                    ConsultarAlunosTurmaVO consultaAluno = new ConsultarAlunosTurmaVO();
                    consultaAluno.setClienteVO(clienteVO);
                    consultaAluno.setMatriculaAlunoHorarioTurmaVO(matriculaAlunoHorarioTurmaVO);
                    listaConsultaAlunos.add(consultaAluno);
                }


            }
        }
        return listaConsultaAlunos;
    }

    public List<ConsultarAlunosTurmaVO> contarEntradaPorVariasModalidadeNoPeriodo(Date dataInicio, Date dataFim, String modalidades, Integer codEmpresa, TurmaVO turma, Integer vezesSemana) throws Exception {
        String sql = "select distinct maht.pessoa, maht.datainicio \n" +
                "from matriculaalunohorarioturma maht\n" +
                "inner join horarioturma ht ON maht.horarioturma = ht.codigo\n" +
                "inner join turma t ON ht.turma = t.codigo\n" +
                "inner join contratomodalidade cm ON cm.contrato = maht.contrato and cm.modalidade = t.modalidade\n" +
                "inner join contratomodalidadevezessemana cmvs on cmvs.contratomodalidade = cm.codigo\n" +
                "inner join modalidade m ON m.codigo = t.modalidade\n" +
                "where t.modalidade in ("+modalidades+")\n" +
                "and (cmvs.nrvezes = ? or ((unaccent(m.nome) ILIKE '%musculacao%') and cmvs.nrvezes = 5))\n" +
                "and datainicio BETWEEN ? and ?\n" +
                "and maht.empresa = ?";
        if (turma != null && !UteisValidacao.emptyNumber(turma.getCodigo())) {
            sql += " and t.codigo = " + turma.getCodigo();

        }

        List<ConsultarAlunosTurmaVO> listaConsultaAlunos = new ArrayList<>();
        try (PreparedStatement stm = con.prepareStatement(sql)) {
            int i = 0;
            stm.setInt(++i, vezesSemana);
            stm.setDate(++i, Uteis.getDataJDBC(dataInicio));
            stm.setDate(++i, Uteis.getDataJDBC(dataFim));
            stm.setInt(++i, codEmpresa);
            try (ResultSet rs = stm.executeQuery()) {
                List<MatriculaAlunoHorarioTurmaVO> matriculas = new ArrayList<>();
                while (rs.next()) {
                    MatriculaAlunoHorarioTurmaVO matriculaAlunoHorarioTurmaVO = new MatriculaAlunoHorarioTurmaVO();
                    PessoaVO pessoaVO = new PessoaVO();
                    pessoaVO.setCodigo(rs.getInt("pessoa"));
                    matriculaAlunoHorarioTurmaVO.setDataInicio(rs.getDate("datainicio"));
                    matriculaAlunoHorarioTurmaVO.setPessoa(pessoaVO);
                    matriculas.add(matriculaAlunoHorarioTurmaVO);
                }
                Map<Integer, ClienteVO> mapClientes = new HashMap<>();

                for (MatriculaAlunoHorarioTurmaVO matriculaAlunoHorarioTurmaVO : matriculas) {
                    Integer codPessoa = matriculaAlunoHorarioTurmaVO.getPessoa().getCodigo();
                    ClienteVO clienteVO = mapClientes.get(codPessoa);
                    if (clienteVO == null) {
                        Cliente clienteDAO = new Cliente(this.con);
                        clienteVO = clienteDAO.consultarPorCodigoPessoa(codPessoa, Uteis.NIVELMONTARDADOS_RELATORIO_SGP);
                        mapClientes.put(codPessoa, clienteVO);
                    }

                    ConsultarAlunosTurmaVO consultaAluno = new ConsultarAlunosTurmaVO();
                    consultaAluno.setClienteVO(clienteVO);
                    consultaAluno.setMatriculaAlunoHorarioTurmaVO(matriculaAlunoHorarioTurmaVO);
                    listaConsultaAlunos.add(consultaAluno);
                }


            }
        }
        return listaConsultaAlunos;
    }

    public List<ConsultarAlunosTurmaVO> contarPresencaAnoPorVariasModalidadeNoPeriodo(Date dataInicio, Date dataFim, String modalidades, Integer codEmpresa, TurmaVO turma, Integer vezesSemana) throws Exception {
        String sql = "SELECT maht.pessoa, MIN(maht.datainicio) AS datainicio, MIN(p.datapresenca) AS datapresenca \n" +
                "FROM matriculaalunohorarioturma maht\n" +
                "INNER JOIN presenca p ON p.dadosturma = maht.codigo\n" +
                "INNER JOIN horarioturma ht ON maht.horarioturma = ht.codigo\n" +
                "INNER JOIN turma t ON ht.turma = t.codigo\n" +
                "inner join contratomodalidade cm ON cm.contrato = maht.contrato and cm.modalidade = t.modalidade\n" +
                "inner join contratomodalidadevezessemana cmvs on cmvs.contratomodalidade = cm.codigo\n" +
                "inner join modalidade m ON m.codigo = t.modalidade\n" +
                "WHERE maht.datainicio BETWEEN ? AND ?\n" +
                "AND t.modalidade in ("+modalidades+")\n" +
                "and (cmvs.nrvezes = ? or ((unaccent(m.nome) ILIKE '%musculacao%') and cmvs.nrvezes = 5))\n" +
                "AND p.datapresenca >= ?\n" +
                "AND maht.empresa = ? \n" +
                "GROUP BY maht.pessoa;\n";
        if (turma != null && !UteisValidacao.emptyNumber(turma.getCodigo())) {
            sql = sql.replace("AND maht.empresa = ?", "AND maht.empresa = ?\nAND t.codigo = " + turma.getCodigo() + "\n");
        }

        List<ConsultarAlunosTurmaVO> listaConsultaAlunos = new ArrayList<>();
        try (PreparedStatement stm = con.prepareStatement(sql)) {
            int i = 0;
            stm.setDate(++i, Uteis.getDataJDBC(Uteis.getDate(Uteis.getAnoData(dataInicio)+"-01-01", "yyyy-MM-dd")));
            stm.setDate(++i, Uteis.getDataJDBC(Uteis.getDate(Uteis.getAnoData(dataFim)+"-12-31", "yyyy-MM-dd")));
            stm.setInt(++i, vezesSemana);
            stm.setDate(++i, Uteis.getDataJDBC(Uteis.getDate(Uteis.getAnoData(dataInicio)+"-01-01", "yyyy-MM-dd")));
            stm.setInt(++i, codEmpresa);
            try (ResultSet rs = stm.executeQuery()) {
                List<MatriculaAlunoHorarioTurmaVO> matriculas = new ArrayList<>();
                while (rs.next()) {
                    if (Calendario.igual(rs.getDate("datapresenca"), dataInicio)) {
                        MatriculaAlunoHorarioTurmaVO matriculaAlunoHorarioTurmaVO = new MatriculaAlunoHorarioTurmaVO();
                        PessoaVO pessoaVO = new PessoaVO();
                        pessoaVO.setCodigo(rs.getInt("pessoa"));
                        matriculaAlunoHorarioTurmaVO.setDataInicio(rs.getDate("datainicio"));
                        matriculaAlunoHorarioTurmaVO.setPessoa(pessoaVO);
                        matriculas.add(matriculaAlunoHorarioTurmaVO);
                    }
                }
                Map<Integer, ClienteVO> mapClientes = new HashMap<>();

                for (MatriculaAlunoHorarioTurmaVO matriculaAlunoHorarioTurmaVO : matriculas) {
                    Integer codPessoa = matriculaAlunoHorarioTurmaVO.getPessoa().getCodigo();
                    ClienteVO clienteVO = mapClientes.get(codPessoa);
                    if (clienteVO == null) {
                        Cliente clienteDAO = new Cliente(this.con);
                        clienteVO = clienteDAO.consultarPorCodigoPessoa(codPessoa, Uteis.NIVELMONTARDADOS_RELATORIO_SGP);
                        mapClientes.put(codPessoa, clienteVO);
                    }

                    ConsultarAlunosTurmaVO consultaAluno = new ConsultarAlunosTurmaVO();
                    consultaAluno.setClienteVO(clienteVO);
                    consultaAluno.setMatriculaAlunoHorarioTurmaVO(matriculaAlunoHorarioTurmaVO);
                    listaConsultaAlunos.add(consultaAluno);
                }


            }
        }
        return listaConsultaAlunos;
    }

    public List<ConsultarAlunosTurmaVO> contarPresencaPorVariasModalidadeNoPeriodo(Date dataInicio, Date dataFim, String modalidades, Integer codEmpresa, TurmaVO turma, Integer vezesSemana) throws Exception {
        String sql = "select distinct maht.pessoa, maht.datainicio \n" +
                "from matriculaalunohorarioturma maht\n" +
                "inner join presenca p on p.dadosturma = maht.codigo\n" +
                "inner join horarioturma ht ON maht.horarioturma = ht.codigo\n" +
                "inner join turma t ON ht.turma = t.codigo\n" +
                "inner join contratomodalidade cm ON cm.contrato = maht.contrato and cm.modalidade = t.modalidade\n" +
                "inner join contratomodalidadevezessemana cmvs on cmvs.contratomodalidade = cm.codigo\n" +
                "inner join modalidade m ON m.codigo = t.modalidade\n" +
                "where t.modalidade in ("+modalidades+")\n" +
                "and (cmvs.nrvezes = ? or ((unaccent(m.nome) ILIKE '%musculacao%') and cmvs.nrvezes = 5))\n" +
                "and datapresenca BETWEEN ? and ?\n" +
                "and maht.empresa = ?";
        if (turma != null && !UteisValidacao.emptyNumber(turma.getCodigo())) {
            sql += " and t.codigo = " + turma.getCodigo();

        }

        List<ConsultarAlunosTurmaVO> listaConsultaAlunos = new ArrayList<>();
        try (PreparedStatement stm = con.prepareStatement(sql)) {
            int i = 0;
            stm.setInt(++i, vezesSemana);
            stm.setDate(++i, Uteis.getDataJDBC(dataInicio));
            stm.setDate(++i, Uteis.getDataJDBC(dataFim));
            stm.setInt(++i, codEmpresa);
            try (ResultSet rs = stm.executeQuery()) {
                List<MatriculaAlunoHorarioTurmaVO> matriculas = new ArrayList<>();
                while (rs.next()) {
                    MatriculaAlunoHorarioTurmaVO matriculaAlunoHorarioTurmaVO = new MatriculaAlunoHorarioTurmaVO();
                    PessoaVO pessoaVO = new PessoaVO();
                    pessoaVO.setCodigo(rs.getInt("pessoa"));
                    matriculaAlunoHorarioTurmaVO.setDataInicio(rs.getDate("datainicio"));
                    matriculaAlunoHorarioTurmaVO.setPessoa(pessoaVO);
                    matriculas.add(matriculaAlunoHorarioTurmaVO);
                }
                Map<Integer, ClienteVO> mapClientes = new HashMap<>();

                for (MatriculaAlunoHorarioTurmaVO matriculaAlunoHorarioTurmaVO : matriculas) {
                    Integer codPessoa = matriculaAlunoHorarioTurmaVO.getPessoa().getCodigo();
                    ClienteVO clienteVO = mapClientes.get(codPessoa);
                    if (clienteVO == null) {
                        Cliente clienteDAO = new Cliente(this.con);
                        clienteVO = clienteDAO.consultarPorCodigoPessoa(codPessoa, Uteis.NIVELMONTARDADOS_RELATORIO_SGP);
                        mapClientes.put(codPessoa, clienteVO);
                    }

                    ConsultarAlunosTurmaVO consultaAluno = new ConsultarAlunosTurmaVO();
                    consultaAluno.setClienteVO(clienteVO);
                    consultaAluno.setMatriculaAlunoHorarioTurmaVO(matriculaAlunoHorarioTurmaVO);
                    listaConsultaAlunos.add(consultaAluno);
                }


            }
        }
        return listaConsultaAlunos;
    }

    public static void consultaOcupacoes(Date dia, AgendaTotalJSON agTmp, Connection con) throws Exception {
        int ocupacao;
        String dataDia = Uteis.getDataAplicandoFormatacao(dia, "yyyy-MM-dd");

        try (ResultSet rs = SuperFacadeJDBC.criarConsulta(
                "select\n" +
                        "    (\n" +
                        "    select count(*)\n" +
                        "    from matriculaalunohorarioturma mat2\n" +
                        "    inner join cliente c on c.pessoa = mat2.pessoa\n" +
                        "    left join auladesmarcada ad on ad.cliente = c.codigo\n" +
                        "        and ad.horarioturma = mat2.horarioturma\n" +
                        "        and ad.dataorigem = '" + dataDia + "'\n" +
                        "    where mat2.horarioturma = " + agTmp.getId() + "\n" +
                        "        and '" + dataDia + "' between mat2.datainicio and mat2.datafim\n" +
                        "        and ad.codigo is null\n" +
                        ") + (\n" +
                        "    select count(*)\n" +
                        "    from reposicao rep2\n" +
                        "    where rep2.horarioturma = " + agTmp.getId() + "\n" +
                        "        and rep2.datareposicao = '" + dataDia + "'\n" +
                        ") as ocupacao;"
                , con)) {
            ocupacao = rs.next() ? rs.getInt("ocupacao") : 0;
        }
        agTmp.setOcupacao(ocupacao);
    }

}
