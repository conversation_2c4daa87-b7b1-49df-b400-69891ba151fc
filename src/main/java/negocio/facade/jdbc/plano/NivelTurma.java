package negocio.facade.jdbc.plano;

import java.sql.SQLException;
import negocio.interfaces.plano.*;
import negocio.comuns.plano.NivelTurmaVO;
import negocio.facade.jdbc.arquitetura.*;

import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.PreparedStatement;
import java.sql.Statement;
import java.util.HashMap;
import java.util.List;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Map;
import negocio.comuns.utilitarias.*;
import org.json.JSONArray;
import org.json.JSONObject;
import servicos.http.MetodoHttpEnum;
import servicos.http.RequestHttpService;
import servicos.http.RespostaHttpDTO;
import servicos.integracao.mgb.impl.MgbServiceImpl;
import servicos.legolas.LegolasService;
import servicos.propriedades.PropsService;

import javax.faces.model.SelectItem;

/**
 * Classe de persistência que encapsula todas as operações de manipulação dos dados da classe <code>NivelTurmaVO</code>.
 * Responsável por implementar operações como incluir, alterar, excluir e consultar pertinentes a classe <code>NivelTurmaVO</code>.
 * Encapsula toda a interação com o banco de dados.
 * @see NivelTurmaVO
 * @see SuperEntidade
 */
public class NivelTurma extends SuperEntidade implements NivelTurmaInterfaceFacade {    

    public NivelTurma() throws Exception {
        super();
        setIdEntidade("Turma");
    }

    public NivelTurma(Connection con) throws Exception {
        super(con);
        setIdEntidade("Turma");
    }

    /**
     * Operação responsável por retornar um novo objeto da classe <code>NivelTurmaVO</code>.
     */
    public NivelTurmaVO novo() throws Exception {
        incluir(getIdEntidade());
        NivelTurmaVO obj = new NivelTurmaVO();
        return obj;
    }

    /**
     * Operação responsável por incluir no banco de dados um objeto da classe <code>NivelTurmaVO</code>.
     * Primeiramente valida os dados (<code>validarDados</code>) do objeto. Verifica a conexão com o banco de dados e a permissão do usuário
     * para realizar esta operacão na entidade.
     * Isto, através da operação <code>incluir</code> da superclasse.
     * @param obj  Objeto da classe <code>NivelTurmaVO</code> que será gravado no banco de dados.
     * @exception Exception Caso haja problemas de conexão, restrição de acesso ou validação de dados.
     */
    public void incluir(NivelTurmaVO obj) throws Exception {
        try {
            NivelTurmaVO.validarDados(obj);
            incluir(getIdEntidade());
            obj.realizarUpperCaseDados();
            String sql = "INSERT INTO NivelTurma( descricao, codigomgb ) VALUES ( ?, ? )";
            PreparedStatement sqlInserir = con.prepareStatement(sql);
            sqlInserir.setString(1, obj.getDescricao());
            sqlInserir.setString(2, obj.getCodigoMgb());
            sqlInserir.execute();
            obj.setCodigo(obterValorChavePrimariaCodigo());
            obj.setNovoObj(new Boolean(false));
        } catch (Exception e) {
            throw e;
        }
    }



    /**
     * Operação responsável por alterar no BD os dados de um objeto da classe <code>NivelTurmaVO</code>.
     * Sempre utiliza a chave primária da classe como atributo para localização do registro a ser alterado.
     * Primeiramente valida os dados (<code>validarDados</code>) do objeto. Verifica a conexão com o banco de dados e a permissão do usuário
     * para realizar esta operacão na entidade.
     * Isto, através da operação <code>alterar</code> da superclasse.
     * @param obj    Objeto da classe <code>NivelTurmaVO</code> que será alterada no banco de dados.
     * @exception Exception Caso haja problemas de conexão, restrição de acesso ou validação de dados.
     */
    public void alterar(NivelTurmaVO obj) throws Exception {
        try {
            con.setAutoCommit(false);
            alterarSemCommit(obj);
            con.commit();
        } catch (Exception e) {
            con.rollback();
            con.setAutoCommit(true);
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    public void alterarSemCommit(NivelTurmaVO obj) throws Exception{
        NivelTurmaVO.validarDados(obj);
        alterar(getIdEntidade());
        obj.realizarUpperCaseDados();
        String sql = "UPDATE NivelTurma set descricao=?, codigomgb=? WHERE ((codigo = ?))";
        PreparedStatement sqlAlterar = con.prepareStatement(sql);
        sqlAlterar.setString(1, obj.getDescricao());
        sqlAlterar.setString(2, obj.getCodigoMgb());
        sqlAlterar.setInt(3, obj.getCodigo().intValue());
        sqlAlterar.execute();
    }

    /**
     * Operação responsável por excluir no BD um objeto da classe <code>NivelTurmaVO</code>.
     * Sempre localiza o registro a ser excluído através da chave primária da entidade.
     * Primeiramente verifica a conexão com o banco de dados e a permissão do usuário
     * para realizar esta operacão na entidade.
     * Isto, através da operação <code>excluir</code> da superclasse.
     * @param obj    Objeto da classe <code>NivelTurmaVO</code> que será removido no banco de dados.
     * @exception Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    public void excluir(NivelTurmaVO obj) throws Exception {
        try {
            con.setAutoCommit(false);
            excluir(getIdEntidade());
            String sql = "DELETE FROM NivelTurma WHERE ((codigo = ?))";
            PreparedStatement sqlExcluir = con.prepareStatement(sql);
            sqlExcluir.setInt(1, obj.getCodigo().intValue());
            sqlExcluir.execute();
            con.commit();
        } catch (Exception e) {
            con.rollback();
            con.setAutoCommit(true);
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    /**
     * Responsável por realizar uma consulta de <code>NivelTurma</code> através do valor do atributo 
     * <code>String descricao</code>. Retorna os objetos, com início do valor do atributo idêntico ao parâmetro fornecido.
     * Faz uso da operação <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o List resultante.
     * @param   controlarAcesso Indica se a aplicação deverá verificar se o usuário possui permissão para esta consulta ou não.
     * @return  List Contendo vários objetos da classe <code>NivelTurmaVO</code> resultantes da consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    public List consultarPorDescricao(String valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT * FROM NivelTurma WHERE upper( descricao ) like ?  ORDER BY descricao";
        PreparedStatement ps = con.prepareStatement(sqlStr);
        ps.setString(1, valorConsulta.toUpperCase() + "%");
        ResultSet rs = ps.executeQuery();
        return (montarDadosConsulta(rs, nivelMontarDados));
    }

    /**
     * Responsável por realizar uma consulta de <code>NivelTurma</code> através do valor do atributo 
     * <code>Integer codigo</code>. Retorna os objetos com valores iguais ou superiores ao parâmetro fornecido.
     * Faz uso da operação <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o List resultante.
     * @param   controlarAcesso Indica se a aplicação deverá verificar se o usuário possui permissão para esta consulta ou não.
     * @return  List Contendo vários objetos da classe <code>NivelTurmaVO</code> resultantes da consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    public List consultarPorCodigo(Integer valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT * FROM NivelTurma WHERE codigo >= " + valorConsulta.intValue() + " ORDER BY codigo";
        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sqlStr);
        return (montarDadosConsulta(tabelaResultado, nivelMontarDados));
    }

    /**
     * Responsável por montar os dados de vários objetos, resultantes de uma consulta ao banco de dados (<code>ResultSet</code>).
     * Faz uso da operação <code>montarDados</code> que realiza o trabalho para um objeto por vez.
     * @return  List Contendo vários objetos da classe <code>NivelTurmaVO</code> resultantes da consulta.
     */
    public static List montarDadosConsulta(ResultSet tabelaResultado, int nivelMontarDados) throws Exception {
        List vetResultado = new ArrayList();
        while (tabelaResultado.next()) {
            NivelTurmaVO obj = new NivelTurmaVO();
            obj = montarDados(tabelaResultado, nivelMontarDados);
            vetResultado.add(obj);
        }
        return vetResultado;
    }

    /**
     * Responsável por montar os dados resultantes de uma consulta ao banco de dados (<code>ResultSet</code>)
     * em um objeto da classe <code>NivelTurmaVO</code>.
     * @return  O objeto da classe <code>NivelTurmaVO</code> com os dados devidamente montados.
     */
    public static NivelTurmaVO montarDados(ResultSet dadosSQL, int nivelMontarDados) throws Exception {
        NivelTurmaVO obj = new NivelTurmaVO();
        obj.setCodigo(new Integer(dadosSQL.getInt("codigo")));
        obj.setDescricao(dadosSQL.getString("descricao"));
        obj.setCodigoMgb(dadosSQL.getString("codigomgb"));
        obj.setNovoObj(new Boolean(false));
        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_DADOSBASICOS) {
            return obj;
        }

        return obj;
    }

    /**
     * Operação responsável por localizar um objeto da classe <code>NivelTurmaVO</code>
     * através de sua chave primária. 
     * @exception Exception Caso haja problemas de conexão ou localização do objeto procurado.
     */
    public NivelTurmaVO consultarPorChavePrimaria(Integer codigoPrm, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), false);
        String sql = "SELECT * FROM NivelTurma WHERE codigo = ?";
        PreparedStatement sqlConsultar = con.prepareStatement(sql);
        sqlConsultar.setInt(1, codigoPrm.intValue());
        ResultSet tabelaResultado = sqlConsultar.executeQuery();
        if (!tabelaResultado.next()) {
            throw new ConsistirException("Dados Não Encontrados ( NivelTurma ).");
        }
        return (montarDados(tabelaResultado, nivelMontarDados));
    }    
    
    public List<NivelTurmaVO> consultarPorTurma(Integer valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT distinct nt.* FROM nivelturma nt INNER JOIN horarioturma ht ON ht.nivelturma = nt.codigo  AND ht.turma = "+valorConsulta+" ORDER BY descricao" ;
        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sqlStr);
        return (montarDadosConsulta(tabelaResultado, nivelMontarDados));
    }

    public String consultarJSON(Integer empresa) throws Exception {
        ResultSet rs = getRS();
        Boolean mgb;
        MgbServiceImpl mgbService = new MgbServiceImpl(getCon());
        Map<String, String> objsMgb = new HashMap<>();
        try {
            mgb = mgbService.integradoMgb(empresa);
            if(mgb){
                JSONObject jsonObject = mgbService.consultarNiveis(empresa);
                JSONArray lista = new JSONArray(jsonObject.get("data").toString());
                for (int e = 0; e < lista.length(); e++) {
                    JSONObject obj = lista.getJSONObject(e);
                    objsMgb.put(obj.getString("publicId"), obj.getString("namePTBR"));
                }
            }
        }catch (Exception e){
            mgb = false;
        }
        StringBuilder json = new StringBuilder();
        json.append("{\"aaData\":[");
        boolean dados = false;
        while (rs.next()) {
            dados = true;
            json.append("[\"").append(rs.getString("codigo")).append("\",");
            if(mgb){
                String mgbNivel = UteisValidacao.emptyString(rs.getString("codigomgb"))
                        || objsMgb.get(rs.getString("codigomgb")) == null ? "-" : objsMgb.get(rs.getString("codigomgb"));
                json.append("\"").append(Uteis.normalizarStringJSON(rs.getString("descricao"))).append("\",");
                json.append("\"").append(Uteis.normalizarStringJSON(mgbNivel)).append("\"],");
            }else{
                json.append("\"").append(Uteis.normalizarStringJSON(rs.getString("descricao"))).append("\"],");
            }
        }
        if(dados) {
            json.deleteCharAt(json.toString().length() - 1);
        }
        json.append("]}");
        return json.toString();
    }

    private ResultSet getRS() throws SQLException {
        String sql = "SELECT codigo, descricao, codigomgb FROM nivelturma ORDER BY descricao";
        PreparedStatement sqlConsultar = con.prepareStatement(sql);
        ResultSet rs = sqlConsultar.executeQuery();
        return rs;
    }
    public List consultarParaImpressao(String filtro, String ordem, String campoOrdenacao, int i) throws SQLException {

        ResultSet rs = getRS();
        List lista = new ArrayList();

        while (rs.next()) {

            NivelTurmaVO nivel = new NivelTurmaVO();
            String geral = rs.getString("codigo") + rs.getString("descricao");
            if (geral.toLowerCase().contains(filtro.toLowerCase())) {
                nivel.setCodigo(rs.getInt("codigo"));
                nivel.setDescricao(rs.getString("descricao"));
                lista.add(nivel);
            }
        }
        if (campoOrdenacao.equals("Código")) {
            Ordenacao.ordenarLista(lista, "codigo");
        } else if (campoOrdenacao.equals("Descrição")) {
            Ordenacao.ordenarLista(lista, "descricao");
        }
        if (ordem.contains("desc")) {
            Collections.reverse(lista);
        }
        return lista;

    }

}
