package negocio.facade.jdbc.crm;
import br.com.pactosolucoes.enumeradores.FasesCRMEnum;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.crm.FecharMetaDetalhadoVO;
import negocio.comuns.crm.HistoricoContatoVO;
import negocio.comuns.crm.ObjecaoBICRMTO;
import negocio.comuns.crm.ObjecaoVO;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.Dominios;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.interfaces.crm.ObjecaoInterfaceFacade;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * Classe de persistência que encapsula todas as operações de manipulação dos dados da classe <code>ObjecaoVO</code>.
 * Responsável por implementar operações como incluir, alterar, excluir e consultar pertinentes a classe <code>ObjecaoVO</code>.
 * Encapsula toda a interação com o banco de dados.
 * @see ObjecaoVO
 * @see SuperEntidade
*/
public class Objecao extends SuperEntidade implements ObjecaoInterfaceFacade {

    public Objecao() throws Exception {
        super();
        setIdEntidade("Objecao");
    }

    public Objecao(Connection con) throws Exception {
        super(con);
    }

    /**
     * Operação responsável por retornar um novo objeto da classe <code>ObjecaoVO</code>.
    */
    public ObjecaoVO novo() throws Exception {
        incluirCRM(getIdEntidade());
        return new ObjecaoVO();
    }

    /**
     * Operação responsável por incluir no banco de dados um objeto da classe <code>ObjecaoVO</code>.
     * Primeiramente valida os dados (<code>validarDados</code>) do objeto. Verifica a conexão com o banco de dados e a permissão do usuário
     * para realizar esta operacão na entidade.
     * Isto, através da operação <code>incluir</code> da superclasse.
     * @param obj  Objeto da classe <code>ObjecaoVO</code> que será gravado no banco de dados.
     * @exception Exception Caso haja problemas de conexão, restrição de acesso ou validação de dados.
     */
    public void incluir(ObjecaoVO obj) throws Exception {
        ObjecaoVO.validarDados(obj);
        incluirCRM(getIdEntidade());
        obj.realizarUpperCaseDados();
        String sql = "INSERT INTO Objecao( descricao, grupo, comentario, tipoGrupo,ativo ) VALUES ( ?, ?, ?, ? , ? )";
        PreparedStatement sqlInserir = con.prepareStatement(sql);
        sqlInserir.setString(1, obj.getDescricao());
        sqlInserir.setString(2, obj.getGrupo());
        sqlInserir.setString(3, obj.getComentario());
        sqlInserir.setString(4, obj.getTipoGrupo());
        sqlInserir.setBoolean(5, obj.getAtivo());
        sqlInserir.execute();
        obj.setCodigo(obterValorChavePrimariaCodigo());
        obj.setNovoObj(false);
    }

    /**
     * Operação responsável por alterar no BD os dados de um objeto da classe <code>ObjecaoVO</code>.
     * Sempre utiliza a chave primária da classe como atributo para localização do registro a ser alterado.
     * Primeiramente valida os dados (<code>validarDados</code>) do objeto. Verifica a conexão com o banco de dados e a permissão do usuário
     * para realizar esta operacão na entidade.
     * Isto, através da operação <code>alterar</code> da superclasse.
     * @param obj    Objeto da classe <code>ObjecaoVO</code> que será alterada no banco de dados.
     * @exception Exception Caso haja problemas de conexão, restrição de acesso ou validação de dados.
    */
    public void alterar(ObjecaoVO obj) throws Exception {
        con.setAutoCommit(false);
        try {
            ObjecaoVO.validarDados(obj);
            alterarCRM(getIdEntidade());
            obj.realizarUpperCaseDados();
            String sql = "UPDATE Objecao set descricao=?, grupo=?, comentario=?, tipoGrupo=? , ativo=? WHERE ((codigo = ?))";
            PreparedStatement sqlAlterar = con.prepareStatement(sql);
            sqlAlterar.setString( 1, obj.getDescricao() );
            sqlAlterar.setString( 2, obj.getGrupo() );
            sqlAlterar.setString( 3, obj.getComentario() );
            sqlAlterar.setString( 4, obj.getTipoGrupo() );
            sqlAlterar.setBoolean( 5, obj.getAtivo() );
            sqlAlterar.setInt( 6, obj.getCodigo());
            sqlAlterar.execute();
            con.commit();
        } catch (Exception e) {
            con.rollback();
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    /**
     * Operação responsável por excluir no BD um objeto da classe <code>ObjecaoVO</code>.
     * Sempre localiza o registro a ser excluído através da chave primária da entidade.
     * Primeiramente verifica a conexão com o banco de dados e a permissão do usuário
     * para realizar esta operacão na entidade.
     * Isto, através da operação <code>excluir</code> da superclasse.
     * @param obj    Objeto da classe <code>ObjecaoVO</code> que será removido no banco de dados.
     * @exception Exception Caso haja problemas de conexão ou restrição de acesso.
    */
    public void excluir(ObjecaoVO obj) throws Exception {
        try {
            con.setAutoCommit(false);
            excluirCRM(getIdEntidade());
            String sql = "DELETE FROM Objecao WHERE ((codigo = ?))";
            PreparedStatement sqlExcluir = con.prepareStatement(sql);
            sqlExcluir.setInt( 1, obj.getCodigo());
            sqlExcluir.execute();
            con.commit();
        } catch (Exception e) {
            con.rollback();
            con.setAutoCommit(true);
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    /**
     * Responsável por realizar uma consulta de <code>Objecao</code> através do valor do atributo 
     * <code>Integer codigo</code>. Retorna os objetos com valores iguais ou superiores ao parâmetro fornecido.
     * Faz uso da operação <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o List resultante.
     * @param   controlarAcesso Indica se a aplicação deverá verificar se o usuário possui permissão para esta consulta ou não.
     * @return  List Contendo vários objetos da classe <code>ObjecaoVO</code> resultantes da consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    public List consultarPorCodigo(Integer valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultarCRM(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT * FROM Objecao WHERE codigo = " + valorConsulta + " AND ativo ORDER BY codigo";
        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sqlStr);
        return (montarDadosConsulta(tabelaResultado, nivelMontarDados));
        }

    /**
     * Método responsavel por selecionar uma objeção dependendo do tipoGrupo
     * <AUTHOR>
     */
    public List consultarObjecao(boolean controlarAcesso, String tipoGrupo, int nivelMontarDados) throws Exception {
    	consultarCRM(getIdEntidade(), controlarAcesso);
    	String sqlStr = "SELECT * FROM Objecao WHERE upper(tipoGrupo) like ('" + tipoGrupo.toUpperCase() + "%') AND ativo  ORDER BY codigo";
    	Statement stm = con.createStatement();
    	ResultSet tabelaResultado = stm.executeQuery(sqlStr);
    	return (montarDadosConsulta(tabelaResultado, nivelMontarDados));
    }

    public List<ObjecaoVO> consultarTodas(int nivelMontarDados) throws Exception{
        Statement st = con.createStatement();
        ResultSet rs = st.executeQuery("select * from Objecao");
        return montarDadosConsulta(rs,nivelMontarDados);
    }

    public List consultarObjecaoDefinitivaDesistencia(boolean controlarAcesso, int nivelMontarDados) throws Exception {
    	consultarCRM(getIdEntidade(), controlarAcesso);
    	String sqlStr = "SELECT * FROM Objecao WHERE (upper(tipoGrupo) = 'MD' OR upper(tipoGrupo) = 'OD') AND ativo";
    	Statement stm = con.createStatement();
    	ResultSet tabelaResultado = stm.executeQuery(sqlStr);
    	return (montarDadosConsulta(tabelaResultado, nivelMontarDados));
    }
    
    
    
    public List consultarObjecaoDefinitiva(boolean controlarAcesso, int nivelMontarDados) throws Exception {
    	consultarCRM(getIdEntidade(), controlarAcesso);
    	String sqlStr = "SELECT * FROM Objecao WHERE (upper(tipoGrupo) = 'OB' OR upper(tipoGrupo) = 'OD') AND ativo";
    	Statement stm = con.createStatement();
    	ResultSet tabelaResultado = stm.executeQuery(sqlStr);
    	return (montarDadosConsulta(tabelaResultado, nivelMontarDados));
    }
    
    
    /**
     * Responsável por montar os dados de vários objetos, resultantes de uma consulta ao banco de dados (<code>ResultSet</code>).
     * Faz uso da operação <code>montarDados</code> que realiza o trabalho para um objeto por vez.
     * @return  List Contendo vários objetos da classe <code>ObjecaoVO</code> resultantes da consulta.
    */
    public static List<ObjecaoVO> montarDadosConsulta(ResultSet tabelaResultado, int nivelMontarDados) throws Exception {
        List<ObjecaoVO> vetResultado = new ArrayList<ObjecaoVO>();
        while (tabelaResultado.next()) {
            ObjecaoVO obj = montarDados(tabelaResultado, nivelMontarDados);
            vetResultado.add(obj);
        }
        tabelaResultado = null;
        return vetResultado;
    }

    /**
     * Responsável por montar os dados resultantes de uma consulta ao banco de dados (<code>ResultSet</code>)
     * em um objeto da classe <code>ObjecaoVO</code>.
     * @return  O objeto da classe <code>ObjecaoVO</code> com os dados devidamente montados.
    */
    public static ObjecaoVO montarDados(ResultSet dadosSQL, int nivelMontarDados) throws Exception {
        ObjecaoVO obj = new ObjecaoVO();
        obj.setCodigo( new Integer( dadosSQL.getInt("codigo")));
        obj.setDescricao( dadosSQL.getString("descricao"));
        obj.setTipoGrupo( dadosSQL.getString("tipoGrupo"));
        obj.setGrupo( dadosSQL.getString("grupo"));
        obj.setComentario( dadosSQL.getString("comentario"));
        obj.setAtivo(dadosSQL.getBoolean("ativo"));
        obj.setNovoObj(false);
        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_DADOSBASICOS) {
            return obj;
        }

        return obj;
    }

    /**
     * Operação responsável por localizar um objeto da classe <code>ObjecaoVO</code>
     * através de sua chave primária. 
     * @exception Exception Caso haja problemas de conexão ou localização do objeto procurado.
    */
    public ObjecaoVO consultarPorChavePrimaria( Integer codigoPrm, int nivelMontarDados ) throws Exception {
        consultarCRM(getIdEntidade(), false);
        String sql = "SELECT * FROM Objecao WHERE codigo = ?";
        PreparedStatement sqlConsultar = con.prepareStatement(sql);
        sqlConsultar.setInt(1, codigoPrm);
        ResultSet tabelaResultado = sqlConsultar.executeQuery();
        if (!tabelaResultado.next()) {
            throw new ConsistirException("Dados Não Encontrados ( Objecao ).");
        }
        return (montarDados(tabelaResultado, nivelMontarDados));
    }
    
    
    public List consultarPorDescricao(String valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultarCRM(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT * FROM Objecao WHERE (upper( descricao ) like('" + valorConsulta.toUpperCase() + "%')) AND ativo ORDER BY descricao";
        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sqlStr);
        return (montarDadosConsulta(tabelaResultado, nivelMontarDados));
    }

    public List consultarPorGrupo(String valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception {
    	consultarCRM(getIdEntidade(), controlarAcesso);
    	String sqlStr = "SELECT * FROM Objecao WHERE (upper( grupo ) like('" + valorConsulta.toUpperCase() + "%')) AND ativo ORDER BY grupo";
    	Statement stm = con.createStatement();
    	ResultSet tabelaResultado = stm.executeQuery(sqlStr);
    	return (montarDadosConsulta(tabelaResultado, nivelMontarDados));
    }
    
    /**
     * Operação responsável por obter o último valor gerado para uma chave primária.
     * É utilizada para obter o valor gerado pela SGBD para uma chave primária, 
     * a apresentação do mesmo e a implementação de possíveis relacionamentos. 
     */
    public  Integer obterValorChavePrimariaCodigo() throws Exception {
       inicializar();
        String sqlStr = "SELECT MAX(codigo) FROM Objecao";
        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sqlStr);
        tabelaResultado.next();
        return (tabelaResultado.getInt(1));
    }
    
    /**
	 * Método responsável por limpar a objeção caso ele não 
	 * queira mais fazer uma objeção.
     * @throws Exception 
	 * @throws Exception
	 */
    public void limparObjecao(ObjecaoVO obj) throws Exception{
    	obj.inicializarDados();
    }    
    
    /**
     * Método responsavel por consultar a Objeção validando se venho de telaMetaPerda ou não.
     * @param vindoTelaMetaPerda
     * @return Lista de Objeção
     * @throws Exception
     * <AUTHOR>
     */
    public List consultarObjecao(Boolean vindoTelaMetaPerda) throws Exception{
        if(vindoTelaMetaPerda == null){
            return consultarObjecao(true, "", Uteis.NIVELMONTARDADOS_TODOS);
        }
		if(vindoTelaMetaPerda){
			return consultarObjecaoDefinitivaDesistencia(true, Uteis.NIVELMONTARDADOS_TODOS);
		}else{
			return consultarObjecaoDefinitiva(true, Uteis.NIVELMONTARDADOS_TODOS);
		}
    }
    
    /**
     * Primeiro será validado se a pessoa é um Passivo, Cliente ou Indicado, 
     * depois será validado se o TipoAgendamento não é ligação. Se não for faço uma consulta 
     * em FecharMetaDetalhado  
     * @param colaboradorResponsavel Usuario Logado
     * @param passivo
     * @param indicado
     * @param cliente
     * @param hist
     * @throws Exception
     * <AUTHOR>
     */
    public void executarRegraNegocioParaFecharMetaAlteracao(Date dia, Integer colaboradorResponsavel, Integer passivo, Integer indicado, Integer cliente, HistoricoContatoVO hist) throws Exception {
		if (passivo != 0 && hist.getAgendaVO().getCodigo() != 0 && Uteis.getCompareData(hist.getAgendaVO().getDataLancamento(), dia)==0) {
			//Valido se o agendamento e diferente de ligação pois so com essa condição que é gerado alteração no banco em fecharMeta.
			if(!hist.getAgendaVO().getTipoAgendamento().equals("LI") && hist.getCodigoFecharMetaDetalhado() != 0){
				FecharMetaDetalhadoVO fecharMetaDetalhado = getFacade().getFecharMetaDetalhado().consultarPorChavePrimaria(hist.getCodigoFecharMetaDetalhado(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
				getFacade().getFecharMeta().executarAtualizacaoMetaAtingida(fecharMetaDetalhado, 0, 1L, false, true);					
			}
			getFacade().getFecharMetaDetalhado().excluirPorCodigoPassivoCodigoColaboradorResponsavelDataIdentificador(passivo, colaboradorResponsavel, hist.getDia(), "AG");
		} else if (cliente != 0 && hist.getAgendaVO().getCodigo() != 0 && Uteis.getCompareData(hist.getAgendaVO().getDataLancamento(), dia) == 0) {
			//Valido se o agendamento e diferente de ligação pois so com essa condição que é gerado alteração no banco em fecharMeta.
			if(!hist.getAgendaVO().getTipoAgendamento().equals("LI") && hist.getCodigoFecharMetaDetalhado() != 0){
				FecharMetaDetalhadoVO fecharMetaDetalhado = getFacade().getFecharMetaDetalhado().consultarPorChavePrimaria(hist.getCodigoFecharMetaDetalhado(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
				getFacade().getFecharMeta().executarAtualizacaoMetaAtingida(fecharMetaDetalhado, 0, 1L, false,true);	
			}
			getFacade().getFecharMetaDetalhado().excluirPorCodigoClienteCodigoColaboradorResponsavelDataIdentificador(cliente,colaboradorResponsavel, hist.getDia(), "AG");

		} else if (indicado != 0 && hist.getAgendaVO().getCodigo() != 0 && Uteis.getCompareData(hist.getAgendaVO().getDataLancamento(), dia)==0){
			//Valido se o agendamento e diferente de ligação pois so com essa condição que é gerado alteração no banco em fecharMeta.
			if(!hist.getAgendaVO().getTipoAgendamento().equals("LI") && hist.getCodigoFecharMetaDetalhado() != 0){
				FecharMetaDetalhadoVO fecharMetaDetalhado = getFacade().getFecharMetaDetalhado().consultarPorChavePrimaria(hist.getCodigoFecharMetaDetalhado(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
				getFacade().getFecharMeta().executarAtualizacaoMetaAtingida(fecharMetaDetalhado, 0, 1L, false, true);	
			}
			getFacade().getFecharMetaDetalhado().excluirPorCodigoIndicadoCodigoColaboradorResponsavelDataIdentificador(indicado,colaboradorResponsavel, hist.getDia(), "AG");
		}

	}

    private ResultSet getRS() throws SQLException {

        StringBuilder sql = new StringBuilder("SELECT * FROM objecao;");

        PreparedStatement sqlConsultar = con.prepareStatement(sql.toString());
        ResultSet rs = sqlConsultar.executeQuery();
        return rs;
    }

    public String consultarJSON() throws Exception {
        ResultSet rs = getRS();
        StringBuilder json = new StringBuilder();
        json.append("{\"aaData\":[");
        boolean dados = false;
        while (rs.next()) {
            dados = true;
            json.append("[\"").append(rs.getInt("codigo")).append("\",");
            json.append("\"").append(Uteis.normalizarStringJSON(rs.getString("descricao"))).append("\",");
            json.append("\"").append(Uteis.normalizarStringJSON(rs.getString("grupo"))).append("\",");
            json.append("\"").append(Dominios.getTipoGrupoObjecao().get(((rs.getString("tipogrupo"))))).append("\",");
            json.append("\"").append((rs.getBoolean("ativo") ? "Sim" : "Não")).append("\"],");
        }
        if(dados) {
            json.deleteCharAt(json.toString().length() - 1);
        }
        json.append("]}");
        return json.toString();
    }

    private ResultSet consultaObjecoesBI(Date dataInicio, Date dataFim, List<UsuarioVO> usuarioVOList, boolean porFase) throws SQLException {
        String codigosUsuario = Uteis.retornarCodigos(usuarioVOList);

        StringBuilder sql = new StringBuilder();

        sql.append("select \n");
        if (porFase) {
            sql.append("count(*) as qtd, \n");
            sql.append("fase \n");
        } else {
            sql.append("count(*) as qtd, \n");
            sql.append("ob.codigo, \n");
            sql.append("ob.descricao \n");
        }
        sql.append("from historicocontato hc \n");
        sql.append("inner join objecao ob on ob.codigo = hc.objecao \n");
        sql.append("where objecao is not null \n");
        sql.append("and dia::date between '").append(Uteis.getSQLData(dataInicio)).append("' and '").append(Uteis.getSQLData(dataFim)).append("' \n");
        sql.append("and responsavelcadastro in (").append(codigosUsuario).append(") \n");
        if (porFase) {
            sql.append("and fase in (").append(FasesCRMEnum.retornarSiglas()).append(") \n");
            sql.append("group by fase order by 1 desc \n");
        } else {
            sql.append("group by ob.codigo,ob.descricao order by 1 desc \n");
        }
        Statement sqlConsultar = con.createStatement();
        return sqlConsultar.executeQuery(sql.toString());
    }

    public List<ObjecaoBICRMTO> consultarObjecoesBICRM(Date dataInicio, Date dataFim, List<UsuarioVO> usuarioVOList, boolean porFase) throws Exception {
        ResultSet tabelaResultado = consultaObjecoesBI(dataInicio, dataFim, usuarioVOList, porFase);
        List<ObjecaoBICRMTO> vetResultado = new ArrayList<ObjecaoBICRMTO>();
        while (tabelaResultado.next()) {
            ObjecaoBICRMTO obj = new ObjecaoBICRMTO();
            obj.setQuantidade(tabelaResultado.getInt("qtd"));
            if (porFase) {
                FasesCRMEnum fase = FasesCRMEnum.getFasePorSigla(tabelaResultado.getString("fase"));
                obj.setFase(fase);
                obj.setObjecao(fase.getDescricao());
            } else {
                obj.setCodigo(tabelaResultado.getInt("codigo"));
                obj.setObjecao(tabelaResultado.getString("descricao"));
            }
            vetResultado.add(obj);
        }
        return vetResultado;
    }

}
