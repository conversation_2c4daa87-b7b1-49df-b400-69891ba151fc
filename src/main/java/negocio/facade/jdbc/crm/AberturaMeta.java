package negocio.facade.jdbc.crm;

import br.com.pactosolucoes.comuns.util.Declaracao;
import br.com.pactosolucoes.enumeradores.FasesCRMEnum;
import br.com.pactosolucoes.estrutura.paginacao.ConfPaginacao;
import negocio.comuns.arquitetura.PerfilAcessoVO;
import negocio.comuns.arquitetura.PermissaoAcessoMenuVO;
import negocio.comuns.arquitetura.UsuarioPerfilAcessoVO;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.ColaboradorIndisponivelCrmVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.basico.TipoColaboradorVO;
import negocio.comuns.crm.*;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.DiasDaSemana;
import negocio.comuns.utilitarias.Ordenacao;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.comuns.utilitarias.UtilReflection;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.facade.jdbc.arquitetura.Usuario;
import negocio.facade.jdbc.basico.Empresa;
import negocio.interfaces.crm.AberturaMetaInterfaceFacade;
import org.json.JSONObject;
import servicos.operacoes.RiscoService;

import java.sql.*;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Collections;
import java.util.Date;
import java.util.HashSet;
import java.util.Iterator;
import java.util.List;
import java.util.Set;

/**
 * Classe de persistência que encapsula todas as operações de manipulação dos
 * dados da classe
 * <code>AberturaMetaVO</code>. Responsável por implementar operações como
 * incluir, alterar, excluir e consultar pertinentes a classe
 * <code>AberturaMetaVO</code>. Encapsula toda a interação com o banco de dados.
 *
 * @see AberturaMetaVO
 * @see SuperEntidade
 */
public class AberturaMeta extends SuperEntidade implements AberturaMetaInterfaceFacade {

    private static final long serialVersionUID = -7815745697308679213L;
    private Boolean manterAbertoRichModal;

    public AberturaMeta() throws Exception {
        super();
        setIdEntidade("AberturaMeta");
        setManterAbertoRichModal(false);

    }

    public AberturaMeta(Connection con) throws Exception {
        super(con);
        setIdEntidade("AberturaMeta");
        setManterAbertoRichModal(false);
    }

    /**
     * Responsável por montar os dados de vários objetos, resultantes de uma
     * consulta ao banco de dados (
     * <code>ResultSet</code>). Faz uso da operação
     * <code>montarDados</code> que realiza o trabalho para um objeto por vez.
     *
     * @return List Contendo vários objetos da classe
     * <code>AberturaMetaVO</code> resultantes da consulta.
     */
    public static List<AberturaMetaVO> montarDadosConsulta(ResultSet tabelaResultado, Connection con, int nivelMontarDados) throws Exception {
        List<AberturaMetaVO> vetResultado = new ArrayList<AberturaMetaVO>();
        while (tabelaResultado.next()) {
            AberturaMetaVO obj = new AberturaMetaVO();
            obj = montarDados(tabelaResultado, con, nivelMontarDados);
            vetResultado.add(obj);
        }
        tabelaResultado = null;
        return vetResultado;
    }

    public static JSONObject montarJsonDadosMinimos(ResultSet resultSet) throws SQLException {
        JSONObject json = new JSONObject();

        json.put("codigo", resultSet.getInt("codigo"));
        json.put("dia", resultSet.getTimestamp("dia"));
        json.put("metaEmAberto", resultSet.getBoolean("metaEmAberto"));
        json.put("diaFechamento", resultSet.getTimestamp("diaFechamento"));
        json.put("aberturaRetroativa", resultSet.getBoolean("aberturaretroativa"));

        return json;
    }

    /**
     * Responsável por montar os dados resultantes de uma consulta ao banco de
     * dados (
     * <code>ResultSet</code>) em um objeto da classe
     * <code>AberturaMetaVO</code>.
     *
     * @return O objeto da classe <code>AberturaMetaVO</code> com os dados
     * devidamente montados.
     */
    @SuppressWarnings("unchecked")
    public static AberturaMetaVO montarDados(ResultSet dadosSQL, Connection con, int nivelMontarDados) throws Exception {
        AberturaMetaVO obj = new AberturaMetaVO();
        Usuario usuarioDAO = new Usuario(con);
        FecharMeta fecharMetaDAO = new FecharMeta(con);

        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_TELACONSULTA) {
            obj.setCodigo(dadosSQL.getInt("codigo"));
            obj.setDia(dadosSQL.getTimestamp("dia"));
            Empresa empresa = new Empresa(con);
            obj.setEmpresaVO(empresa.consultarPorChavePrimaria(dadosSQL.getInt("empresa"), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA));

            obj.setDiaFechamento(dadosSQL.getTimestamp("diaFechamento"));
            obj.getColaboradorResponsavel().setCodigo(new Integer(dadosSQL.getInt("colaboradorResponsavel")));
            obj.getResponsavelCadastro().setCodigo(new Integer(dadosSQL.getInt("responsavelCadastro")));
            obj.getResponsavelLiberacaoTrocaColaboradorResponsavel().setCodigo(new Integer(dadosSQL.getInt("responsavelLiberacaoTrocaColaboradorResponsavel")));
            obj.setMetaEmAberto(dadosSQL.getBoolean("metaEmAberto"));
            obj.setNovoObj(false);


            montarDadosColaboradorResponsavel(obj, nivelMontarDados, usuarioDAO);
            montarDadosResponsavelCadastro(obj, nivelMontarDados, usuarioDAO);
            return obj;
        }
        obj.getEmpresaVO().setCodigo(dadosSQL.getInt("empresa"));
        obj.setCodigo(dadosSQL.getInt("codigo"));
        obj.setDia(dadosSQL.getTimestamp("dia"));
        obj.getColaboradorResponsavel().setCodigo(new Integer(dadosSQL.getInt("colaboradorResponsavel")));
        obj.setMetaEmAberto(dadosSQL.getBoolean("metaEmAberto"));
        obj.setAberturaRetroativa(dadosSQL.getBoolean("aberturaretroativa"));
        obj.setJustificativa(dadosSQL.getString("justificativa"));
        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_METACOLABORADORRESPONSAVEL) {
            montarDadosColaboradorResponsavel(obj, nivelMontarDados, usuarioDAO);
            return obj;
        }

        obj.getResponsavelCadastro().setCodigo(new Integer(dadosSQL.getInt("responsavelCadastro")));
        obj.getResponsavelLiberacaoTrocaColaboradorResponsavel().setCodigo(new Integer(dadosSQL.getInt("responsavelLiberacaoTrocaColaboradorResponsavel")));
        obj.setMetaEmAberto(dadosSQL.getBoolean("metaEmAberto"));

        obj.setNovoObj(false);
        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_DADOSBASICOS) {
            return obj;
        }

        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_DADOSENTIDADESPRINCIPAIS) {
            montarDadosColaboradorResponsavel(obj, nivelMontarDados, usuarioDAO);
            montarDadosResponsavelCadastro(obj, nivelMontarDados, usuarioDAO);
            obj.setFecharMetaVosVenda(fecharMetaDAO.consultarFecharMetaVenda(obj.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
            obj.setFecharMetaVosRetencao(fecharMetaDAO.consultarFecharMetaRetencao(obj.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
            obj.setFecharMetaVosLead(fecharMetaDAO.consultarFecharMetaLeads(obj.getCodigo(), Uteis.NIVELMONTARDADOS_METACOLABORADORRESPONSAVEL));
            obj.setFecharMetaVosEstudio(fecharMetaDAO.consultarFecharMeta(obj.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS, FasesCRMEnum.ULTIMAS_SESSOES.getSigla(), FasesCRMEnum.SEM_AGENDAMENTO.getSigla()));
            return obj;
        }

        montarDadosColaboradorResponsavel(obj, nivelMontarDados, usuarioDAO);
        obj.setFecharMetaVosVenda(fecharMetaDAO.consultarFecharMetaVenda(obj.getCodigo(), Uteis.NIVELMONTARDADOS_METACOLABORADORRESPONSAVEL));
        obj.setFecharMetaVosRetencao(fecharMetaDAO.consultarFecharMetaRetencao(obj.getCodigo(), Uteis.NIVELMONTARDADOS_METACOLABORADORRESPONSAVEL));
        obj.setFecharMetaVosLead(fecharMetaDAO.consultarFecharMetaLeads(obj.getCodigo(), Uteis.NIVELMONTARDADOS_METACOLABORADORRESPONSAVEL));

        obj.setFecharMetaVosEstudio(fecharMetaDAO.consultarFecharMeta(obj.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS, FasesCRMEnum.ULTIMAS_SESSOES.getSigla(), FasesCRMEnum.SEM_AGENDAMENTO.getSigla()));
        montarDadosResponsavelCadastro(obj, nivelMontarDados, usuarioDAO);
        return obj;
    }


    public static void montarDadosColaboradorResponsavel(AberturaMetaVO obj, int nivelMontarDados, Usuario usuario) throws Exception {
        if (obj.getColaboradorResponsavel().getCodigo() == 0) {
            obj.setColaboradorResponsavel(new UsuarioVO());
            return;
        }

        obj.setColaboradorResponsavel(usuario.consultarPorChavePrimaria(obj.getColaboradorResponsavel().getCodigo(), nivelMontarDados));
    }

    public static void montarDadosResponsavelCadastro(AberturaMetaVO obj, int nivelMontarDados, Usuario usuario) throws Exception {
        if (obj.getResponsavelCadastro().getCodigo().intValue() == 0) {
            obj.setResponsavelCadastro(new UsuarioVO());
            return;
        }
        obj.setResponsavelCadastro(usuario.consultarPorChavePrimaria(obj.getResponsavelCadastro().getCodigo(), nivelMontarDados));
    }

    /**
     * Operação responsável por retornar um novo objeto da classe
     * <code>AberturaMetaVO</code>.
     */
    public AberturaMetaVO novo() throws Exception {
        incluirCRM(getIdEntidade());
        AberturaMetaVO obj = new AberturaMetaVO();
        setManterAbertoRichModal(false);
        return obj;
    }

    /**
     * Operação responsável por incluir no banco de dados um objeto da classe
     * <code>AberturaMetaVO</code>. Primeiramente valida os dados (
     * <code>validarDados</code>) do objeto. Verifica a conexão com o banco de
     * dados e a permissão do usuário para realizar esta operacão na entidade.
     * Isto, através da operação
     * <code>incluir</code> da superclasse.
     *
     * @param obj Objeto da classe <code>AberturaMetaVO</code> que será gravado
     * no banco de dados.
     * @throws Exception Caso haja problemas de conexão, restrição de acesso ou
     * validação de dados.
     */
    public void incluir(AberturaMetaVO obj) throws Exception {
        try {
            con.setAutoCommit(false);
            incluirSemCommit(obj);
            con.commit();
        } catch (Exception e) {
            obj.setNovoObj(true);
            con.rollback();
            con.setAutoCommit(true);
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    public void incluirSemCommit(AberturaMetaVO obj) throws Exception {
        AberturaMetaVO.validarDados(obj);
        incluirCRM(getIdEntidade());

            if (UteisValidacao.emptyNumber( obj.getCodigo())) {
                obj.realizarUpperCaseDados();
                String sql = "INSERT INTO AberturaMeta( colaboradorResponsavel, responsavelCadastro, responsavelLiberacaoTrocaColaboradorResponsavel, dia, metaEmAberto, diaFechamento, aberturaretroativa, empresa ) VALUES ( ?, ?, ?, ?, ?, ?, ?, ? )";
                PreparedStatement sqlInserir = con.prepareStatement(sql);
                if (obj.getColaboradorResponsavel().getCodigo() != 0) {
                    sqlInserir.setInt(1, obj.getColaboradorResponsavel().getCodigo());
                } else {
                    sqlInserir.setNull(1, 0);
                }
                if (obj.getResponsavelCadastro().getCodigo() != 0) {
                    sqlInserir.setInt(2, obj.getResponsavelCadastro().getCodigo());
                } else {
                    sqlInserir.setNull(2, 0);
                }
                if (obj.getResponsavelLiberacaoTrocaColaboradorResponsavel().getCodigo() != 0 && !obj.getColaboradorResponsavel().getCodigo().equals(obj.getResponsavelCadastro().getCodigo())) {
                    sqlInserir.setInt(3, obj.getResponsavelLiberacaoTrocaColaboradorResponsavel().getCodigo());
                } else {
                    sqlInserir.setNull(3, 0);
                }
                sqlInserir.setDate(4, Uteis.getDataJDBC(obj.getDia()));
                sqlInserir.setBoolean(5, obj.getMetaEmAberto());
                sqlInserir.setTimestamp(6, Uteis.getDataJDBCTimestamp(obj.getDiaFechamento()));
                sqlInserir.setBoolean(7, obj.getAberturaRetroativa());
                sqlInserir.setInt(8, obj.getEmpresaVO().getCodigo());
                sqlInserir.execute();


                obj.setCodigo(obterValorChavePrimariaCodigo());
                getFacade().getFecharMeta().incluirFecharMetaVos(obj.getCodigo(), obj.getFecharMetaVosVenda());
                getFacade().getFecharMeta().incluirFecharMetaVos(obj.getCodigo(), obj.getFecharMetaVosRetencao());
                getFacade().getFecharMeta().incluirFecharMetaVos(obj.getCodigo(), obj.getFecharMetaVosEstudio());
                getFacade().getFecharMeta().incluirFecharMetaVos(obj.getCodigo(), obj.getFecharMetaVosLead());
                obj.setNovoObj(false);
            }
            else{

                getFacade().getFecharMeta().incluirFecharMetaVos(obj.getCodigo(), obj.getFecharMetaVosVenda(), obj.getFaseEspecifica());
                getFacade().getFecharMeta().incluirFecharMetaVos(obj.getCodigo(), obj.getFecharMetaVosRetencao(), obj.getFaseEspecifica());
                getFacade().getFecharMeta().incluirFecharMetaVos(obj.getCodigo(), obj.getFecharMetaVosEstudio(), obj.getFaseEspecifica());
                getFacade().getFecharMeta().incluirFecharMetaVos(obj.getCodigo(), obj.getFecharMetaVosLead(), obj.getFaseEspecifica());
                obj.setNovoObj(false);
            }
    }

    /**
     * Operação responsável por alterar no BD os dados de um objeto da classe
     * <code>AberturaMetaVO</code>. Sempre utiliza a chave primária da classe
     * como atributo para localização do registro a ser alterado. Primeiramente
     * valida os dados (
     * <code>validarDados</code>) do objeto. Verifica a conexão com o banco de
     * dados e a permissão do usuário para realizar esta operacão na entidade.
     * Isto, através da operação
     * <code>alterar</code> da superclasse.
     *
     * @param obj Objeto da classe <code>AberturaMetaVO</code> que será alterada
     * no banco de dados.
     * @throws Exception Caso haja problemas de conexão, restrição de acesso ou
     * validação de dados.
     */
    public void alterar(AberturaMetaVO obj) throws Exception {
        try {
            con.setAutoCommit(false);
            AberturaMetaVO.validarDados(obj);
            alterarCRM(getIdEntidade());
            obj.realizarUpperCaseDados();
            String sql = "UPDATE AberturaMeta SET colaboradorResponsavel=?, responsavelCadastro=?, responsavelLiberacaoTrocaColaboradorResponsavel=?, dia=?, metaEmAberto=?, diaFechamento=? WHERE ((codigo = ?))";
            PreparedStatement sqlAlterar = con.prepareStatement(sql);
            if (obj.getColaboradorResponsavel().getCodigo() != 0) {
                sqlAlterar.setInt(1, obj.getColaboradorResponsavel().getCodigo().intValue());
            } else {
                sqlAlterar.setNull(1, 0);
            }
            if (obj.getResponsavelCadastro().getCodigo() != 0) {
                sqlAlterar.setInt(2, obj.getResponsavelCadastro().getCodigo().intValue());
            } else {
                sqlAlterar.setNull(2, 0);
            }
            if (obj.getResponsavelLiberacaoTrocaColaboradorResponsavel().getCodigo() != 0) {
                sqlAlterar.setInt(3, obj.getResponsavelLiberacaoTrocaColaboradorResponsavel().getCodigo().intValue());
            } else {
                sqlAlterar.setNull(3, 0);
            }
            sqlAlterar.setTimestamp(4, Uteis.getDataJDBCTimestamp(obj.getDia()));
            sqlAlterar.setBoolean(5, obj.getMetaEmAberto());
            sqlAlterar.setTimestamp(6, Uteis.getDataJDBCTimestamp(obj.getDiaFechamento()));

            sqlAlterar.setInt(7, obj.getCodigo().intValue());

            sqlAlterar.execute();

            getFacade().getFecharMeta().alterarFecharMetaVosVindoAberturaMeta(obj.getCodigo(), obj.getFecharMetaVosVenda());
            getFacade().getFecharMeta().alterarFecharMetaVosVindoAberturaMeta(obj.getCodigo(), obj.getFecharMetaVosRetencao());
            con.commit();
        } catch (Exception e) {
            con.rollback();
            con.setAutoCommit(true);
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    /**
     * Método responsavel por alterar apenas o campo FecharMeta da AberturaMeta
     * no momento de fazer o Fechamento do Dia.
     *
     * @param obj
     * @throws Exception
     * <AUTHOR>
     */
    public void alterarSomenteCampoFechaMetaDiaFechamentoJustificativa(AberturaMetaVO obj) throws Exception {
        obj.realizarUpperCaseDados();
        String sql = "UPDATE AberturaMeta SET metaEmAberto=?, diaFechamento=?, justificativa = ? WHERE ((codigo = ?))";
        int i = 0;
        PreparedStatement sqlAlterar = con.prepareStatement(sql);
        sqlAlterar.setBoolean(++i, obj.getMetaEmAberto());
        sqlAlterar.setTimestamp(++i, Uteis.getDataJDBCTimestamp(obj.getDiaFechamento()));
        sqlAlterar.setString(++i, obj.getJustificativa());
        sqlAlterar.setInt(++i, obj.getCodigo());
        sqlAlterar.execute();
        getFacade().getFecharMeta().alterarFecharMetaVos(obj.getCodigo(), obj.getListaFechametoDia());
    }

    /**
     * Operação responsável por excluir no BD um objeto da classe
     * <code>AberturaMetaVO</code>. Sempre localiza o registro a ser excluído
     * através da chave primária da entidade. Primeiramente verifica a conexão
     * com o banco de dados e a permissão do usuário para realizar esta operacão
     * na entidade. Isto, através da operação
     * <code>excluir</code> da superclasse.
     *
     * @param obj Objeto da classe <code>AberturaMetaVO</code> que será removido
     * no banco de dados.
     * @throws Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    public void excluir(AberturaMetaVO obj) throws Exception {
        try {
            con.setAutoCommit(false);
            excluirCRM(getIdEntidade());
            String sql = "DELETE FROM AberturaMeta WHERE ((codigo = ?))";
            PreparedStatement sqlExcluir = con.prepareStatement(sql);
            sqlExcluir.setInt(1, obj.getCodigo().intValue());
            sqlExcluir.execute();
            con.commit();
        } catch (Exception e) {
            con.rollback();
            con.setAutoCommit(true);
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    /**
     * Operação responsável por localizar um objeto da classe
     * <code>AberturaMetaVO</code> através de sua chave primária.
     *
     * @throws Exception Caso haja problemas de conexão ou localização do objeto
     * procurado.
     */
    public AberturaMetaVO consultarPorChavePrimaria(Integer codigoPrm, int nivelMontarDados) throws Exception {
        consultarCRM(getIdEntidade(), false);
        String sql = "SELECT * FROM AberturaMeta WHERE codigo = ?";
        PreparedStatement sqlConsultar = con.prepareStatement(sql);
        sqlConsultar.setInt(1, codigoPrm.intValue());
        ResultSet tabelaResultado = sqlConsultar.executeQuery();
        if (!tabelaResultado.next()) {
            throw new ConsistirException("Dados Não Encontrados ( AberturaMeta ).");
        }
        return (montarDados(tabelaResultado, con, nivelMontarDados));
    }

    public Boolean getManterAbertoRichModal() {
        return manterAbertoRichModal;
    }

    public void setManterAbertoRichModal(Boolean manterAbertoRichModal) {
        this.manterAbertoRichModal = manterAbertoRichModal;
    }

    public Boolean consultarAberturaPorCodigoUsuario(Integer codigoColaborador, Integer empresa, Date dia) throws Exception {
        consultarCRM(getIdEntidade(), false);
        StringBuilder sqlStr = new StringBuilder();
        sqlStr.append("SELECT * FROM AberturaMeta ");
        sqlStr.append(" WHERE ");
        sqlStr.append(" colaboradorresponsavel = " + codigoColaborador);
        sqlStr.append(" and Cast (dia as Date) = Cast( '" + Uteis.getDataJDBC(dia) + "'as Date) ");
        sqlStr.append(" AND empresa = " + empresa);
        PreparedStatement sqlConsultar = con.prepareStatement(sqlStr.toString());
        ResultSet tabelaResultado = sqlConsultar.executeQuery();
        return tabelaResultado.next();
    }

    public AberturaMetaVO consultarAberturaPorCodigoUsuario(Integer codigoUsuario, Date dia, Integer empresa, int nivelMontarDados) throws Exception {
        consultarCRM(getIdEntidade(), false);
        String sql = "SELECT * FROM AberturaMeta WHERE colaboradorresponsavel = " + codigoUsuario + " AND dia >= '" + Uteis.getDataAplicandoFormatacao(dia, "yyyy-MM-dd 00:00:00") + "' and dia <= '" + Uteis.getDataAplicandoFormatacao(dia, "yyyy-MM-dd 23:59:59") + "' AND empresa = " + empresa;
        PreparedStatement sqlConsultar = con.prepareStatement(sql);
        ResultSet tabelaResultado = sqlConsultar.executeQuery();
        if (!tabelaResultado.next()) {
            return new AberturaMetaVO();
        }
        return (montarDados(tabelaResultado, con, nivelMontarDados));
    }

    /**
     * Responsável por realizar uma consulta de
     * <code>AberturaMeta</code> através do valor do atributo
     * <code>Integer codigo</code>. Retorna os objetos com valores iguais ou
     * superiores ao parâmetro fornecido. Faz uso da operação
     * <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o
     * List resultante.
     *
     * @param controlarAcesso Indica se a aplicação deverá verificar se o
     * usuário possui permissão para esta consulta ou não.
     * @return List Contendo vários objetos da classe
     * <code>AberturaMetaVO</code> resultantes da consulta.
     * @throws Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    public List<AberturaMetaVO> consultarPorCodigo(Integer valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultarCRM(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT * FROM AberturaMeta WHERE codigo >= " + valorConsulta.intValue() + " ORDER BY codigo";
        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sqlStr);
        return (montarDadosConsulta(tabelaResultado, con, nivelMontarDados));
    }

    public List<AberturaMetaVO> consultarPorDia(Date prmIni, Date prmFim, Boolean metaEmAberto, boolean controlarAcesso, int nivelMontarDados, Integer empresa, ConfPaginacao confPaginacao) throws Exception {
        //1- CONFIGURANDO A NAVEGACAO DA PAGINACAO
        confPaginacao.configurarNavegacao();
        consultarCRM(getIdEntidade(), controlarAcesso);
        StringBuffer sqlStr = new StringBuffer();
        if (metaEmAberto) {
            sqlStr.append("SELECT * FROM AberturaMeta WHERE ((dia >= '" + Uteis.getDataJDBC(prmIni) + "') and (dia <= '" + Uteis.getDataJDBC(prmFim) + "')) and metaEmAberto = true");
        } else {
            sqlStr.append("SELECT * FROM AberturaMeta WHERE ((dia >= '" + Uteis.getDataJDBC(prmIni) + "') and (dia <= '" + Uteis.getDataJDBC(prmFim) + "')) and metaEmAberto = false");
        }
        if (!UteisValidacao.emptyNumber(empresa)) {
            sqlStr.append(" AND empresa = " + empresa);
        }
        sqlStr.append(" ORDER BY dia,codigo");
        confPaginacao.iniciarPaginacao(this);
        confPaginacao.addPaginacao(sqlStr);

        ResultSet tabelaResultado = confPaginacao.consultaPaginada();
        return (montarDadosConsulta(tabelaResultado, con, nivelMontarDados));
    }

    public List<AberturaMetaVO> consultarMetasAbertaPorDia(Date data, Integer empresa, Integer nivelMontarDados) throws Exception {
        StringBuilder sqlStr = new StringBuilder("SELECT * FROM AberturaMeta WHERE\n");
        sqlStr.append("dia = '").append(Uteis.getDataJDBC(data)).append("'\n");
        sqlStr.append("and empresa = ").append(empresa).append("\n");
        sqlStr.append("and metaEmAberto = true");
        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sqlStr.toString());
        return (montarDadosConsulta(tabelaResultado, con, nivelMontarDados));
    }
    
    public List<AberturaMetaVO> consultarMetasFechadasPorDia(Date data, Integer empresa, Integer nivelMontarDados) throws Exception {
        StringBuilder sqlStr = new StringBuilder("SELECT * FROM AberturaMeta WHERE\n");
        sqlStr.append("dia = '").append(Uteis.getDataJDBC(data)).append("'\n");
        sqlStr.append("and empresa = ").append(empresa).append("\n");
        sqlStr.append("and metaEmAberto = false");
        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sqlStr.toString());
        return (montarDadosConsulta(tabelaResultado, con, nivelMontarDados));
    }

    public Boolean consultarPorDiaAberturaMeta(Date prmIni, Date prmFim, boolean controlarAcesso) throws Exception {
        consultarCRM(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT * FROM AberturaMeta WHERE ((dia >= '" + Uteis.getDataJDBC(prmIni) + "') and (dia <= '" + Uteis.getDataJDBC(prmFim) + "')) ORDER BY dia";
        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sqlStr);
        return tabelaResultado.next();
    }

    /**
     * Método responsavel por consultar os dias que ainda estão sem fechar o dia
     * para o fechamento do Dia
     *
     * @param valorConsulta
     * @param campoConsulta
     * @param dataConsultaFechamentoDia
     * @param metaEmAberto
     * @return
     * @throws Exception
     * <AUTHOR>
     */
    @SuppressWarnings("unchecked")
    public List consultarFechamentoDiaAberturaMeta(String valorConsulta, String campoConsulta, Date dataConsultaFechamentoDia, Boolean metaEmAberto, Integer empresa, ConfPaginacao confPaginacao) throws Exception {
        if (campoConsulta.equals("dia")) {
            return this.consultarPorDia(dataConsultaFechamentoDia, dataConsultaFechamentoDia, metaEmAberto, false, Uteis.NIVELMONTARDADOS_TELACONSULTA, empresa, confPaginacao);
        }
        if (campoConsulta.equals("colaboradorResponsavel")) {
            return this.consultarPorColaboradorResponsavel(valorConsulta, metaEmAberto, false, Uteis.NIVELMONTARDADOS_TELACONSULTA, empresa, confPaginacao);
        }
        if (campoConsulta.equals("responsavelCadastro")) {
            return this.consultarPorResponsavelCadastro(valorConsulta, metaEmAberto, false, Uteis.NIVELMONTARDADOS_TELACONSULTA, empresa, confPaginacao);
        }
        return new ArrayList();
    }

    public List<AberturaMetaVO> consultarPorColaboradorResponsavel(String valorConsulta, Boolean metaEmAberto, boolean controlarAcesso, int nivelMontarDados, Integer empresa, ConfPaginacao confPaginacao) throws Exception {
        consultarCRM(getIdEntidade(), controlarAcesso);
        //1- CONFIGURANDO A NAVEGACAO DA PAGINACAO
        confPaginacao.configurarNavegacao();
        StringBuffer sqlStr = new StringBuffer();
        sqlStr.append(" SELECT * FROM AberturaMeta ");
        sqlStr.append(" INNER JOIN Usuario on usuario.codigo = AberturaMeta.colaboradorResponsavel AND ");
        sqlStr.append(" upper(usuario.nome) like('").append(valorConsulta.toUpperCase()).append("%') ");
        sqlStr.append(" WHERE metaEmAberto = ").append(metaEmAberto);
        if (!UteisValidacao.emptyNumber(empresa)) {
            sqlStr.append(" AND empresa = ").append(empresa);
        }
        sqlStr.append(" ORDER BY AberturaMeta.responsavelCadastro, AberturaMeta.dia, AberturaMeta.codigo");
        confPaginacao.iniciarPaginacao(this);
        confPaginacao.addPaginacao(sqlStr);
        ResultSet tabelaResultado = confPaginacao.consultaPaginada();
        return (montarDadosConsulta(tabelaResultado, con, nivelMontarDados));
    }

    public List<AberturaMetaVO> consultarPorResponsavelCadastro(String valorConsulta, Boolean metaEmAberto, boolean controlarAcesso, int nivelMontarDados, Integer empresa, ConfPaginacao confPaginacao) throws Exception {
        consultarCRM(getIdEntidade(), controlarAcesso);
        //1- CONFIGURANDO A NAVEGACAO DA PAGINACAO
        confPaginacao.configurarNavegacao();
        StringBuffer sqlStr = new StringBuffer();
        sqlStr.append(" SELECT * FROM AberturaMeta ");
        sqlStr.append(" INNER JOIN Usuario on usuario.codigo = AberturaMeta.responsavelCadastro AND ");
        sqlStr.append(" upper(usuario.nome) like('").append(valorConsulta.toUpperCase()).append("%') ");
        sqlStr.append(" WHERE metaEmAberto = ").append(metaEmAberto);
        if (!UteisValidacao.emptyNumber(empresa)) {
            sqlStr.append(" AND empresa = ").append(empresa);
        }
        sqlStr.append(" ORDER BY AberturaMeta.responsavelCadastro, AberturaMeta.dia, AberturaMeta.codigo");
        confPaginacao.iniciarPaginacao(this);
        confPaginacao.addPaginacao(sqlStr);
        ResultSet tabelaResultado = confPaginacao.consultaPaginada();
        return (montarDadosConsulta(tabelaResultado, con, nivelMontarDados));
    }

    /**
     * Método que retorna a qtde de clientes que fazem aniversario por mes e por
     * dia valida ano
     *
     * @return
     * @throws Exception
     */
    public Long consultarCalculoMetaQtdeClienteDataAniversariantePorMesEPorDia(Date data1, Date data2, Integer codColaborador, String codigoClientesObjecoes) throws Exception {
        consultarCRM(getIdEntidade(), false);
        String sql = "SELECT count (*) from pessoa "
                + "inner join cliente on cliente.pessoa = pessoa.codigo "
                + "inner join vinculo on vinculo.cliente = cliente.codigo and "
                + "vinculo.colaborador = '" + codColaborador.intValue() + "' "
                + "WHERE (date_part('month', dataNasc) = date_part('month', timestamp '" + Uteis.getDataJDBC(data1) + "') "
                + "and date_part('day', dataNasc)  = date_part('day', timestamp '" + Uteis.getDataJDBC(data1) + "') "
                + "and date_part('year', dataNasc)  = date_part('year', timestamp '" + Uteis.getDataJDBC(data1) + "') ) "
                + "or (date_part('month', dataNasc) = date_part('month', timestamp '" + Uteis.getDataJDBC(data2) + "') "
                + "and date_part('day', dataNasc)  = date_part('day', timestamp '" + Uteis.getDataJDBC(data2) + "') "
                + "and date_part('year', dataNasc)  = date_part('year', timestamp '" + Uteis.getDataJDBC(data2) + "')) "
                + "and cliente.codigo not in (" + codigoClientesObjecoes + ")";

        Statement sqlConsultar = con.createStatement();
        ResultSet tabelaResultado = sqlConsultar.executeQuery(sql);
        if (!tabelaResultado.next()) {
            return new Long(0);
        }
        return (new Long(tabelaResultado.getInt(1)));
    }

    /**
     * Método que retorna a qtde de clientes que fazem aniversario por mes e por
     * dia valido ano
     *
     * @return
     * @throws Exception
     */
    public Double consultarCalculoMetaQtdeClienteDataAniversariantePorMesEPorDia(Date data, Integer codColaborador, Integer empresa, String tipoVinculos, String codigoClientesObjecoes) throws Exception {
        consultarCRM(getIdEntidade(), false);

        StringBuffer strSQL = new StringBuffer();
        strSQL.append("SELECT count(distinct(cliente.codigo))  from pessoa inner join cliente on cliente.pessoa = pessoa.codigo  \n");
        strSQL.append("inner join vinculo on vinculo.cliente = cliente.codigo and vinculo.colaborador = " + codColaborador.intValue() + " \n");
        if (!tipoVinculos.equals("TODOS")) {
            strSQL.append(" and vinculo.tipovinculo in (").append(tipoVinculos).append(") ");
        }
        strSQL.append("WHERE  \n");
        strSQL.append(" (date_part('month', dataNasc) = date_part('month', timestamp '" + Uteis.getDataJDBC(data) + "') \n");
        strSQL.append("AND date_part('day', dataNasc)  = date_part('day', timestamp '" + Uteis.getDataJDBC(data) + "')) \n");
        strSQL.append("AND cliente.empresa = " + empresa);
        strSQL.append(" AND cliente.codigo not in (").append(codigoClientesObjecoes).append(")");

        Statement sqlConsultar = con.createStatement();
        ResultSet tabelaResultado = sqlConsultar.executeQuery(strSQL.toString());
        if (!tabelaResultado.next()) {
            return new Double(0);
        }
        return (new Double(tabelaResultado.getInt(1)));
    }

    /**
     * Método que retorna a qtde de clientes que fazem aniversario por mes e por
     * dia valido ano
     *
     * @return
     * @throws Exception
     */
    public Double consultarCalculoMetaQtdeClienteVisitanteRecorrente(Date dia, Integer codColaborador, Integer empresa, String tipoVinculos, String codigoClientesObjecoes) throws Exception {
        consultarCRM(getIdEntidade(), false);

        StringBuilder strSQL = new StringBuilder();
        strSQL.append("select  count(distinct(c.codigo)) ");
        strSQL.append("from cliente c inner join historicocontato h \n" +
                " on c.codigo = h.cliente");
        strSQL.append(" inner join vinculo on vinculo.cliente = c.codigo and vinculo.colaborador = " + codColaborador.intValue() + " \n");
        strSQL.append(" where h.fase = 'VR'");
        strSQL.append(" AND h.dia::date >= '").append(Uteis.getDataJDBC(Uteis.somarDias(dia, -7))).append("' \n");
        strSQL.append(" AND c.situacao = 'VI'");
        strSQL.append(" AND c.codigo not in (" + codigoClientesObjecoes + ") ");
        Statement sqlConsultar = con.createStatement();
        ResultSet tabelaResultado = sqlConsultar.executeQuery(strSQL.toString());
        if (!tabelaResultado.next()) {
            return new Double(0);
        }
        return (new Double(tabelaResultado.getInt(1)));
    }

    /**
     * Método que retorna a qtde de clientes que fazem aniversario por mes e por
     * dia valido ano
     *
     * @return
     * @throws Exception
     */
    public Double consultarCalculoMetaQtdeClienteDataAniversariantePorMesEPorDia_original(Date data, Integer codColaborador) throws Exception {
        consultarCRM(getIdEntidade(), false);
        String sql = "SELECT count (*) from pessoa "
                + "inner join cliente on cliente.pessoa = pessoa.codigo ";
        if (codColaborador != null && codColaborador.intValue() > 0) {
            sql += "inner join vinculo on vinculo.cliente = cliente.codigo and vinculo.colaborador = '" + codColaborador.intValue() + "' ";
        }
        sql += "WHERE (date_part('month', dataNasc) = date_part('month', timestamp '" + Uteis.getDataJDBC(data) + "') "
                + "and date_part('day', dataNasc)  = date_part('day', timestamp '" + Uteis.getDataJDBC(data) + "') )";
        Statement sqlConsultar = con.createStatement();
        ResultSet tabelaResultado = sqlConsultar.executeQuery(sql);
        if (!tabelaResultado.next()) {
            return new Double(0);
        }
        return (new Double(tabelaResultado.getInt(1)));
    }

    //    /**
//     * Método que retorna a qtde de clientes que fazem aniversario por mes e por
//     * dia valido ano
//     *
//     * Autor: Pedro Y. Saito
//     * Criado em 17/03/2011
//     */
//    public Double consultarCalculoMetasQtdeClienteDataAniversariantePorMesEPorDia(Date data) throws Exception {
//        consultarCRM(getIdEntidade(), false);
//        String sql =  "SELECT count (*) from pessoa "
//                    + "inner join cliente on cliente.pessoa = pessoa.codigo "
//                    + "inner join vinculo on vinculo.cliente = cliente.codigo "
//                    + "WHERE (date_part('month', dataNasc) = date_part('month', timestamp '" + Uteis.getDataJDBC(data) + "') "
//                    + "and date_part('day', dataNasc)  = date_part('day', timestamp '" + Uteis.getDataJDBC(data) + "') )";
//        Statement sqlConsultar = con.createStatement();
//        ResultSet tabelaResultado = sqlConsultar.executeQuery(sql);
//        if (!tabelaResultado.next()) {
//            return new Double(0);
//        }
//        return (new Double(tabelaResultado.getInt(1)));
//    }
    public Double consultarCalculoMetaQtdeClienteVinteQuatroHoras(Integer codColaborador, Date dia, Integer empresa, String tipoVinculos, String codigoPessoasObjecoes) throws Exception {
        consultarCRM(getIdEntidade(), false);

        StringBuilder strSQL = new StringBuilder();
        strSQL.append("SELECT\n");
        strSQL.append("  count(DISTINCT (sdw.codigocliente))\n");
        strSQL.append("FROM situacaoclientesinteticodw sdw\n");
        strSQL.append("  INNER JOIN pessoa pes\n");
        strSQL.append("    ON pes.codigo = sdw.codigopessoa AND sdw.empresacliente = ").append(empresa).append(" \n");
        strSQL.append("  INNER JOIN vinculo v\n");
        strSQL.append("    ON v.cliente = sdw.codigocliente \n");
        if (!tipoVinculos.equals("TODOS")){
            strSQL.append(" AND v.tipovinculo in (").append(tipoVinculos).append(") \n");
        }
        strSQL.append("       AND v.colaborador = ").append(codColaborador).append("\n");
        strSQL.append("       AND Cast(pes.datacadastro AS DATE) = Cast((SELECT\n");
        strSQL.append("                                                    cast('").append(Uteis.getDataJDBC(dia)).append("' AS DATE) - INTERVAL '1 DAY' AS DATA) AS DATE)\n");
        strSQL.append("WHERE (sdw.situacao = 'VI' OR sdw.situacao = 'IN') AND not exists (select contrato.codigo from contrato  where contrato.pessoa = pes.codigo and contrato.vigenciade >= '").append(Uteis.getDataJDBC(dia)).append("') ");
        strSQL.append(" AND pes.codigo not in (").append(codigoPessoasObjecoes).append(")");

        Statement sqlConsultar = con.createStatement();
        ResultSet tabelaResultado = sqlConsultar.executeQuery(strSQL.toString());
        if (!tabelaResultado.next()) {
            return new Double(0);
        }
        return (new Double(tabelaResultado.getInt(1)));
    }

    public Double consultarCalculoMetaQtdeClienteGymPass(Integer codColaborador, Date dia, Integer empresa, String tipoVinculos, String codigoPessoasObjecoes) throws Exception {
        consultarCRM(getIdEntidade(), false);
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT\n");
        sql.append("count(DISTINCT (sdw.codigocliente))\n");
        sql.append("FROM situacaoclientesinteticodw sdw \n");
        sql.append("INNER JOIN pessoa pes ON pes.codigo = sdw.codigopessoa AND sdw.empresacliente = ").append(empresa).append("\n");
        sql.append("INNER JOIN periodoacessocliente pr ON pr.pessoa = sdw.codigopessoa \n");
        sql.append("INNER JOIN vinculo v ON v.cliente = sdw.codigocliente ").append(" AND v.colaborador = ").append(codColaborador).append("\n");
        if (!tipoVinculos.equals("TODOS")){
            sql.append(" AND v.tipovinculo in (").append(tipoVinculos).append(") \n");
        }
        sql.append("WHERE 1 = 1 \n");
        sql.append("AND coalesce(pr.tokengympass, '') <> ''");
        sql.append("AND pr.tipoacesso = 'PL' \n");
        sql.append("AND pr.datainicioacesso::date = '").append(Uteis.getDataJDBC(Uteis.somarDias(dia, -1))).append("' \n");
        sql.append("AND sdw.codigocliente not in (").append(codigoPessoasObjecoes).append(")");
        Statement sqlConsultar = con.createStatement();
        ResultSet tabelaResultado = sqlConsultar.executeQuery(sql.toString());
        if (!tabelaResultado.next()) {
            return new Double(0);
        }
        return (new Double(tabelaResultado.getInt(1)));
    }

    public Double consultarCalculoMetaQtdeClienteAgendadosParaDiaHoje(Integer codColaboradorResponsavel, Date dia, Integer empresa, String tipoVinculos, String codigoClientesObjecoes) throws Exception {
        consultarCRM(getIdEntidade(), false);
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT count(codigo) from agenda WHERE  cast(dataAgendamento as Date) = cast ('" + Uteis.getDataJDBC(dia));
        sql.append("' as date) and colaboradorResponsavel = " + codColaboradorResponsavel + " and tipoagendamento <> 'LI' ");
        sql.append(" AND empresa = " + empresa);

        Statement sqlConsultar = con.createStatement();
        ResultSet tabelaResultado = sqlConsultar.executeQuery(sql.toString());
        if (!tabelaResultado.next()) {
            return new Double(0);
        }
        return (new Double(tabelaResultado.getInt(1)));
    }

    public Double consultarCalculoMetaQtdeLigacaoAgendadosAmanha(Integer codColaboradorResponsavel, Date dia, Integer empresa, String tipoVinculos, String codigoClientesObjecoes) throws Exception {
        consultarCRM(getIdEntidade(), false);
        String sql = String.format(ConsultasFasesCRM.ligacaoAgendadosAmanha, new Object[]{"COUNT(*)"});
        sql = sql + " AND (agenda.cliente not in (" + codigoClientesObjecoes + ") or (agenda.cliente is null and (agenda.passivo is not null or agenda.indicado is not null)))";
        
        PreparedStatement stm = con.prepareStatement(sql);
        int i = 0;
        stm.setDate(++i, Uteis.getDataJDBC(Uteis.somarCampoData(dia, Calendar.DAY_OF_MONTH, 1)));
        stm.setInt(++i, codColaboradorResponsavel);
        stm.setInt(++i, empresa);

        ResultSet tabelaResultado = stm.executeQuery();
        if (!tabelaResultado.next()) {
            return new Double(0);
        }
        return (new Double(tabelaResultado.getInt(1)));
    }

    /*
     * Metodo que retorna a qtde de cliente que faltam renovar daqui 10 dias.
     */
    public Double consultarCalculoMetaQtdeRenovacao(Integer codColaborador, List<Date> datas, Integer diasPrevistoUmMes, Integer diasPrevistoMaiorUmMes,
            Integer empresa, String tipoVinculos, 
            String codigoClientesObjecoes, Integer saldoLimiteRenovar, boolean entraContratoAutoRRenovavel) throws Exception {
        consultarCRM(getIdEntidade(), false);

        StringBuilder strSQL = new StringBuilder();

        strSQL.append("SELECT count (Distinct (vinculo.cliente)) from contrato \n");
        strSQL.append(" inner join pessoa on  pessoa.codigo = contrato.pessoa \n");
        strSQL.append(" inner join cliente on cliente.pessoa = pessoa.codigo \n");
        strSQL.append(" inner join vinculo on vinculo.cliente = cliente.codigo and vinculo.colaborador = '");
        strSQL.append(codColaborador.intValue()).append("' \n");
        if (!tipoVinculos.equals("TODOS")) {
            strSQL.append(" and vinculo.tipovinculo in (").append(tipoVinculos).append(") ");
        }
        
        strSQL.append(" LEFT JOIN contratoDuracao cd ON cd.contrato = contrato.codigo\n");

        if(!UteisValidacao.emptyNumber(saldoLimiteRenovar)){
            strSQL.append(" LEFT JOIN contratoDuracaoCreditoTreino cdc on cdc.contratoDuracao = cd.codigo\n");
        }
        
        strSQL.append(" left join contratooperacao op on op.contrato = contrato.codigo and op.tipooperacao = 'CA'\n");

        if(!entraContratoAutoRRenovavel) {
            strSQL.append(" inner join plano on contrato.plano = plano.codigo \n");
            strSQL.append(" left join planorecorrencia plr on plr.plano = plano.codigo \n");
            strSQL.append(" left join contratorecorrencia cr  on cr.contrato = contrato.codigo \n");
        }

        strSQL.append(" where op.codigo is null AND cliente.empresa = ").append(empresa);
        strSQL.append("\n AND (").append(UteisValidacao.emptyNumber(saldoLimiteRenovar) ? "" : "(");
        
        String datasFiltro = "";
        String datasFiltroCredito = "";
        for (Date data : datas) {
            datasFiltro += "OR (cd.numeromeses = 1 and dataPrevistaRenovar = (select cast('" + Uteis.getDataJDBC(data)
                    + "' as date) + interval '" + diasPrevistoUmMes + " days' as date)) \n" +
                    "OR (cd.numeromeses > 1 and dataPrevistaRenovar = (select cast(' " + Uteis.getDataJDBC(data) + "' as date) " +
                    "+ interval '" + diasPrevistoMaiorUmMes + " days' as date)\n )";
            datasFiltroCredito += "OR contrato.vigenciade <= '"+Uteis.getDataJDBC(data)+"' ";
        }
        strSQL.append(datasFiltro.replaceFirst("OR", ""));
        if(!UteisValidacao.emptyNumber(saldoLimiteRenovar)){
            strSQL.append(" OR (cdc.quantidadeCreditoDisponivel <= "+saldoLimiteRenovar+" AND contrato.situacao = 'AT' AND (").append(datasFiltroCredito.replaceFirst("OR", "")).append(")) ");
        }
        strSQL.append(")").append(UteisValidacao.emptyNumber(saldoLimiteRenovar) ? "" : ")");
        strSQL.append(" AND datarenovarrealizada IS NULL");
        strSQL.append(" AND cliente.codigo not in (").append(codigoClientesObjecoes).append(")");

        if(!entraContratoAutoRRenovavel) {
            strSQL.append(" AND ((cr.contrato is null and not (plano.renovavelautomaticamente and contrato.renovavelautomaticamente))  or (cr.contrato is  not null and not (plr.renovavelautomaticamente and cr.renovavelautomaticamente)))");
        }
        
        Statement sqlConsultar = con.createStatement();
        ResultSet tabelaResultado = sqlConsultar.executeQuery(strSQL.toString());
        if (!tabelaResultado.next()) {
            return new Double(0);
        }
        return (new Double(tabelaResultado.getInt(1)));
    }

    public Double consultarQtdeClientePosVenda(Integer nrDia, Integer codColaborador, Date dia, Integer empresa, boolean incluirContratosRenovados, String tiposVinculos, String codigoClientesObjecoes) throws Exception {
        consultarCRM(getIdEntidade(), false);

        Statement sqlConsultar = con.createStatement();
        ResultSet tabelaResultado = sqlConsultar.executeQuery(FasesCRMSQL.sqlPosVenda(nrDia, codColaborador, dia, empresa, incluirContratosRenovados, true, tiposVinculos, codigoClientesObjecoes));
        if (!tabelaResultado.next()) {
            return 0.0;
        }
        return tabelaResultado.getDouble(1);
    }

    public Double consultarCalculoMetaQtdePerdasDia(Integer codColaborador, Date dia, Integer nrDiasAposVencimentoContrato, Integer empresa, String tipoVinculos, String codigoClientesObjecoes) throws Exception {
        consultarCRM(getIdEntidade(), false);

        StringBuilder strSQL = new StringBuilder();

        strSQL.append("SELECT count (distinct (situacaoclientesinteticodw.codigocliente)) from situacaoclientesinteticodw ");
        strSQL.append(" inner join contrato on contrato.codigo = situacaoclientesinteticodw.codigocontrato");
        strSQL.append(" inner join vinculo on vinculo.cliente = situacaoclientesinteticodw.codigocliente and vinculo.colaborador = ").append(codColaborador.intValue());
        if (!tipoVinculos.equals("TODOS")) {
            strSQL.append(" and vinculo.tipovinculo in (").append(tipoVinculos).append(") ");
        }
        strSQL.append(" INNER JOIN cliente ON situacaoclientesinteticodw.codigocliente = cliente.codigo ");
        strSQL.append(" where cast (( situacaoclientesinteticodw.datavigenciaateajustada + interval '") //soma mais um, pois deve começar a contar no dia posterior ao último dia do contrato
                .append(nrDiasAposVencimentoContrato + 1).append(" days' ) as date) = cast ('").append(Uteis.getDataJDBC(dia)).append("' as date) ");
        strSQL.append(" and  (contrato.contratoresponsavelrenovacaomatricula + contrato.contratoresponsavelrematriculamatricula) = 0 ");
        strSQL.append(" and cliente.empresa = " + empresa + " AND situacaoclientesinteticodw.situacaocontrato NOT LIKE 'CA'");
        strSQL.append(" and cliente.codigo not in (").append(codigoClientesObjecoes).append(")");
        Statement sqlConsultar = con.createStatement();
        ResultSet tabelaResultado = sqlConsultar.executeQuery(strSQL.toString());
        if (!tabelaResultado.next()) {
            return new Double(0);
        }
        return (new Double(tabelaResultado.getInt(1)));
    }

    /**
     * Joao Alcides 13/02/2012
     */
    public Double consultarCalculoVencidos(Integer codColaborador, Integer empresa, String tipoVinculos, String codigoClientesObjecoes, Date dia) throws Exception {
        StringBuilder strSQL = new StringBuilder();
        strSQL.append("SELECT COUNT(distinct (ss.codigocliente)) FROM situacaoclientesinteticodw ss \n");
        strSQL.append("INNER JOIN vinculo v ON ss.codigocliente = v.cliente \n");
        if (!tipoVinculos.equals("TODOS")) {
            strSQL.append(" and v.tipovinculo in (").append(tipoVinculos).append(") ");
        }
        strSQL.append("INNER JOIN cliente c ON ss.codigocliente = c.codigo \n");
        strSQL.append("WHERE ss.situacaocontrato LIKE 'VE' \n");
        strSQL.append(" AND c.codigo not in (").append(codigoClientesObjecoes).append(")");
        strSQL.append(" AND v.colaborador = " + codColaborador + " \n");
        strSQL.append("AND ( \n");
        strSQL.append(" SELECT COUNT(fm.codigo) FROM fecharmeta fm  \n");
        strSQL.append("INNER JOIN fecharmetadetalhado fmd on fm.codigo = fmd.fecharmeta \n");
        strSQL.append("WHERE fm.identificadormeta  = 'VE' and fmd.cliente = ss.codigocliente \n");
        strSQL.append("and fmd.obtevesucesso = 't' and fm.dataregistro >= ss.datavigenciaateajustada) = 0 \n");
        strSQL.append("AND ( \n");
        strSQL.append("SELECT COUNT(a.codigo) FROM agenda a \n");
        strSQL.append("WHERE a.dataagendamento >= '").append(Uteis.getDataJDBC(dia)).append("' \n");
        strSQL.append("AND a.cliente = ss.codigocliente) = 0 \n");
        strSQL.append("AND c.empresa = " + empresa);

        Statement sqlConsultar = con.createStatement();  
        ResultSet tabelaResultado = sqlConsultar.executeQuery(strSQL.toString());
        if (!tabelaResultado.next()) {
            return new Double(0);
        }
        return (new Double(tabelaResultado.getInt(1)));
    }

    /**
     * Joao Alcides 04/04/2012
     */
    public Double consultarCalculoConversaoAgendados(Integer codColaborador, Date inicio, Date fim, Integer empresa) throws Exception {
        StringBuilder strSQL = new StringBuilder();
        strSQL.append(" SELECT COUNT(*) FROM agenda a ");
        strSQL.append(" INNER JOIN cliente cli ON a.cliente = cli.codigo ");
        strSQL.append(" WHERE colaboradorresponsavel = " + codColaborador);
        strSQL.append(" AND dataagendamento BETWEEN '" + Uteis.getDataJDBC(inicio) + "  00:00:00' AND '" + Uteis.getDataJDBC(fim) + "  23:59:59' ");
        strSQL.append(" AND tipoagendamento IN ('VI','AE')  AND a.empresa = " + empresa);
        strSQL.append(" AND (SELECT COUNT(*) FROM contrato WHERE pessoa = cli.pessoa  ");
        strSQL.append(" AND datalancamento BETWEEN a.dataagendamento AND '" + Uteis.getDataJDBC(fim) + "  23:59:59') = 0 ");
        strSQL.append(" AND (SELECT COUNT(*) FROM fecharmetadetalhado fmd ");
        strSQL.append(" INNER JOIN fecharmeta f ON f.codigo = fmd.fecharmeta  ");
        strSQL.append(" WHERE f.dataregistro BETWEEN '" + Uteis.getDataJDBC(inicio) + " 00:00:00' AND '" + Uteis.getDataJDBC(fim) + "  23:59:59' ");
        strSQL.append(" AND f.identificadormeta LIKE '" + FasesCRMEnum.CONVERSAO_AGENDADOS.getSigla() + "' AND fmd.cliente = cli.codigo) = 0 ");

        Statement sqlConsultar = con.createStatement();
        ResultSet tabelaResultado = sqlConsultar.executeQuery(strSQL.toString());
        if (!tabelaResultado.next()) {
            return new Double(0);
        }
        return (new Double(tabelaResultado.getInt(1)));
    }

    public Double consultarCalculoMetaQtdeRisco(Integer codColaborador, Integer nrRisco, Date meta, Integer empresa, String tipoVinculos, String codigoClientesObjecoes) throws Exception {
        consultarCRM(getIdEntidade(), false);

        Statement sqlConsultar = con.createStatement();
        ResultSet tabelaResultado = sqlConsultar.executeQuery(FasesCRMSQL.sqlGrupoRisco(meta, nrRisco, codColaborador, empresa, true, tipoVinculos, codigoClientesObjecoes));
        if (!tabelaResultado.next()) {
            return 0d;
        }
        return tabelaResultado.getDouble(1);
    }

    public Double consultarCalculoMetaQtdeFaltosos(Integer codColaborador, Date dia, Integer nrFalta, Integer nrFaltaConf, Integer nrDuracaoPlano, Integer empresa, String tipoVinculos, String codigoClientesObjecoes, Integer codCliente) throws Exception {
        //PY. aqui foram feitas as refatorações e adequações para o novo modelo de dados, ou seja, utilizando status e situação do cliente
        //PY. também foi modificado para utilizar as tabelas de relatório sintéticos (DW) que não eram utilizadas anteriormente
        consultarCRM(getIdEntidade(), false);
        Date dataInicioFaltas = Uteis.obterDataAnterior(dia, nrFalta);
        String sql = obterSqlCalculoMetaQtdeFaltosos(codColaborador, dia, nrFaltaConf, nrDuracaoPlano, empresa, tipoVinculos, codigoClientesObjecoes, dataInicioFaltas, codCliente, false);
        Statement sqlConsultar = con.createStatement();
        ResultSet tabelaResultado = sqlConsultar.executeQuery(sql);
        if (!tabelaResultado.next()) {
            return 0.0;
        }
        return ((double) tabelaResultado.getInt(1));
    }

    public String obterSqlCalculoMetaQtdeFaltosos(Integer codColaborador, Date dia, Integer nrFaltaConf, Integer nrDuracaoPlano, Integer empresa, String tipoVinculos, String codigoClientesObjecoes, Date dataInicioFaltas, Integer codCliente, Boolean contarFaltaQuarentena) throws Exception {
        StringBuilder sql = new StringBuilder();
        if(contarFaltaQuarentena){
            sql.append("select tabela.cliente from ");
        }else {
            sql.append("select COUNT(tabela.cliente) from ");
        }
        sql.append(" (select distinct(codigocliente) as cliente, 'CLIENTE' as origem, codigocliente as codigoOrigem , ");
        sql.append(" ((('").append(Uteis.getDataJDBC(dia)).append("' :: DATE) - ((COALESCE(ac.dthrentrada, con.vigenciaDe) :: DATE)+1)) % ").append(nrFaltaConf).append(") as nrfalta, scsdw.datavigenciade, con.situacaocontrato as contratosituacao, ");
        sql.append(" (('").append(Uteis.getDataJDBC(dia)).append("' :: DATE) - ((COALESCE(ac.dthrentrada, con.vigenciaDe) :: DATE)+1))  as nrRealdefalta ");
        sql.append(" from situacaoclientesinteticodw scsdw");
        sql.append(" inner join contrato con on con.codigo = scsdw.codigocontrato ");
        sql.append(" inner join vinculo on vinculo.cliente = codigocliente and vinculo.colaborador = ").append(codColaborador);
        if (!tipoVinculos.equals("TODOS")) {
            sql.append(" and vinculo.tipovinculo in (").append(tipoVinculos).append(") ");
        }
        sql.append(" inner join contratoduracao on contratoduracao.contrato = scsdw.codigocontrato ");
        if (nrDuracaoPlano == 1) {
            sql.append(" and (contratoduracao.numeromeses = 1 or contratoduracao.numeromeses = 2) ");
        } else if (nrDuracaoPlano == 3) {
            sql.append(" and (contratoduracao.numeromeses = 3 or contratoduracao.numeromeses = 4 or contratoduracao.numeromeses = 5) ");
        } else {
            sql.append(" and contratoduracao.numeromeses >= 6 ");
        }
        sql.append("INNER JOIN cliente ON scsdw.codigocliente = cliente.codigo ");
        sql.append("LEFT JOIN acessocliente ac ON cliente.uacodigo = ac.codigo ");
        sql.append(" Where ");
        sql.append(" ('").append(Uteis.getDataJDBC(dia)).append("' :: DATE - COALESCE(ac.dthrentrada, con.vigenciaDe) :: DATE) > 0 ");
        sql.append(" and cliente.codigo not in (").append(codigoClientesObjecoes).append(")");
        sql.append(" and ((scsdw.situacao = 'AT' and scsdw.situacaocontrato <> 'AE') ");
        sql.append(" and ");
        sql.append("(scsdw.situacao = 'AT' and scsdw.situacaocontrato <> 'CR')) ");
        sql.append(" AND cliente.empresa = ").append(empresa);
        if(!UteisValidacao.emptyNumber(codCliente)) {
            sql.append(" AND cliente.codigo = ").append(codCliente);
        }
        sql.append("  ) as tabela  where (tabela.nrfalta = 0) and (tabela.nrRealdefalta > 0) and (tabela.datavigenciade <='").append(Uteis.getDataJDBC(dataInicioFaltas)).append("' or (tabela.contratosituacao = 'RN') ");
        sql.append(") AND   (SELECT COUNT(*) FROM contratooperacao co INNER JOIN contrato c ON c.codigo = co.contrato ");
        sql.append("INNER JOIN cliente cli ON cli.pessoa = c.pessoa WHERE co.tipooperacao  ");
        sql.append("IN ('TR', 'CR', 'AT', 'BC') AND cli.codigo = tabela.cliente ");
        sql.append("AND ( datainicioefetivacaooperacao BETWEEN '").append(Uteis.getDataJDBC(dataInicioFaltas)).append("' AND '").append(Uteis.getDataJDBC(dia)).append("' ");
        sql.append("OR datafimefetivacaooperacao BETWEEN '").append(Uteis.getDataJDBC(dataInicioFaltas)).append("' AND '").append(Uteis.getDataJDBC(dia)).append("' ");
        sql.append("OR ('").append(Uteis.getDataJDBC(dataInicioFaltas)).append("' <= datainicioefetivacaooperacao AND '").append(Uteis.getDataJDBC(dia)).append("' >= datafimefetivacaooperacao))) <= 0");
        return sql.toString();
    }

    /**
     * Método que retorna a qtde de clientes que fazem aniversario por mes e por
     * dia valido ano
     *
     * @return
     * @throws Exception
     */
    public Long consultarQtdeClienteDataAniversariantePorMesEPorDiaSemCodColaborador(Date data1, Date data2) throws Exception {
        consultarCRM(getIdEntidade(), false);
        String sql = "SELECT count (*) from pessoa " + "inner join cliente on cliente.pessoa = pessoa.codigo " + "WHERE (date_part('month', dataNasc) = date_part('month', timestamp '" + Uteis.getDataJDBC(data1) + "') " + "and date_part('day', dataNasc)  = date_part('day', timestamp '" + Uteis.getDataJDBC(data1) + "') " + "and date_part('year', dataNasc)  = date_part('year', timestamp '" + Uteis.getDataJDBC(data1) + "')) " + "or(date_part('month', dataNasc) = date_part('month', timestamp '" + Uteis.getDataJDBC(data2) + "') " + "and date_part('day', dataNasc)  = date_part('day', timestamp '" + Uteis.getDataJDBC(data2) + "')) ";
        Statement sqlConsultar = con.createStatement();
        ResultSet tabelaResultado = sqlConsultar.executeQuery(sql);
        if (!tabelaResultado.next()) {
            return new Long(0);
        }
        return (new Long(tabelaResultado.getInt(1)));
    }

    /**
     * Método que retorna a qtde de clientes 24 horas, que são cliente que se
     * cadastraram no dia anterior e ainda não compraram e são tipo 'VI'
     *
     * @return
     * @throws Exception
     */
    public Long consultarQtdeClienteVinteQuatroHorasSemCodColaborador() throws Exception {
        consultarCRM(getIdEntidade(), false);
        String sql = "SELECT count (cliente.codigo) from pessoa, cliente " + "WHERE cliente.pessoa = pessoa.codigo " + "and cliente.situacao = 'VI' " + "and pessoa.dataCadastro = (select cast(current_date as date ) - interval '1 day' as data) ";
        Statement sqlConsultar = con.createStatement();
        ResultSet tabelaResultado = sqlConsultar.executeQuery(sql);
        if (!tabelaResultado.next()) {
            return new Long(0);
        }
        return (new Long(tabelaResultado.getInt(1)));
    }

    /*
     * Metodo reponsavel por trazer a qtde de clientes que estão agendados para
     * o dia de hoje
     */
    public Long consultarQtdeClienteAgendadosParaDiaHojeSemCodColaborador() throws Exception {
        consultarCRM(getIdEntidade(), false);
        String sql = "SELECT count (dia) from agenda " + "inner join cliente on agenda.cliente = cliente.codigo " + "WHERE dia = current_date";
        Statement sqlConsultar = con.createStatement();
        ResultSet tabelaResultado = sqlConsultar.executeQuery(sql);
        if (!tabelaResultado.next()) {
            return new Long(0);
        }
        return (new Long(tabelaResultado.getInt(1)));
    }

    /*
     * Metodo que retorna a qtde de desistencias que o teve no dia.
     */
    public Long consultarQtdePerdasDiaSemCodColaborador() throws Exception {
        consultarCRM(getIdEntidade(), false);
        String sql = "SELECT count (distinct (situacaoContratoAnaliticodw.cliente)) from situacaoContratoAnaliticodw, cliente " + "where situacaoContratoAnaliticodw.situacao = 'DE' and dia = current_date";
        Statement sqlConsultar = con.createStatement();
        ResultSet tabelaResultado = sqlConsultar.executeQuery(sql);
        if (!tabelaResultado.next()) {
            return new Long(0);
        }
        return (new Long(tabelaResultado.getInt(1)));
    }

    public Long consultarQtdeClientePosVendaSemCodColaborador(Integer nrDia) throws Exception {
        consultarCRM(getIdEntidade(), false);
        String sql = "SELECT count (dataMatricula) from contrato inner join pessoa on pessoa.codigo = contrato.pessoa " + "inner join cliente on cliente.pessoa = pessoa.codigo " + "where contrato.situacao = 'AT' " + "and contrato.situacaoContrato = 'MA' " + "and contrato.dataMatricula = (select cast(current_date as date) - interval '" + nrDia.intValue() + " days' as data)";
        Statement sqlConsultar = con.createStatement();
        ResultSet tabelaResultado = sqlConsultar.executeQuery(sql);
        if (!tabelaResultado.next()) {
            return new Long(0);
        }
        return (new Long(tabelaResultado.getInt(1)));
    }

    public Long consultarQtdeRiscoSemCodColaborador(Integer nrRisco) throws Exception {
        consultarCRM(getIdEntidade(), false);
        String sql = "select count (distinct(risco.cliente)) from risco " + "inner join cliente on cliente.codigo = risco.cliente " + "WHERE cast(dia as Date) = cast (now() as date) or risco.peso > '" + nrRisco.intValue() + "'";
        Statement sqlConsultar = con.createStatement();
        ResultSet tabelaResultado = sqlConsultar.executeQuery(sql);
        if (!tabelaResultado.next()) {
            return new Long(0);
        }
        return (new Long(tabelaResultado.getInt(1)));
    }
    // Consulta alterada*
    // Motivo:Na consulta anterior quando, se um dos colaboradores passados por parâmetros não possuísse uma meta no determinado dia,
    // não era retonado abertura mesmo se o outro colaborador possuísse uma meta aberta pela data determinada
    // ATU => Rafael Carvalhedo
    public Boolean consultarAberturaMetaPorParticipanteGrupo(String tabelaRefente, String codigoColaborador, String sqlDiaInicio, String sqlEmpresa) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append(" SELECT * FROM ").append(tabelaRefente).append("\n");
        sql.append(" WHERE ").append(codigoColaborador).append("\n");
        sql.append(" AND ").append(sqlDiaInicio).append("\n");
        sql.append(" AND ").append(sqlEmpresa);
        Statement sqlConsultar = con.createStatement();
        ResultSet tabelaResultado = sqlConsultar.executeQuery(sql.toString());
        if (!tabelaResultado.next()) {
            return false;
        }
        return true;
    }

    public Long consultarVerificacaoAberturaMetaDia(Integer codigo, Date dia, Integer empresa) throws Exception {
        String sql = "select count(codigo) from aberturaMeta where dia >= '" + Uteis.getDataAplicandoFormatacao(dia, "yyyy-MM-dd 00:00:00") + "' and dia <= '" + Uteis.getDataAplicandoFormatacao(dia, "yyyy-MM-dd 23:59:59") + "' and colaboradorresponsavel = " + codigo + " AND empresa = " + empresa;
        Statement sqlConsultar = con.createStatement();
        ResultSet tabelaResultado = sqlConsultar.executeQuery(sql);
        if (!tabelaResultado.next()) {
            return new Long(0);
        }
        return (new Long(tabelaResultado.getInt(1)));
    }

    public AberturaMetaVO consultar(Integer codigoColaboradorResponsavel, Integer codigoEmpresa, Date dataAbertura, Integer nivelMontarDados) throws Exception{
        String sql= "select * from aberturaMeta where Cast(dia as Date) = ? and colaboradorResponsavel = ? and empresa = ? ";
        PreparedStatement pst = con.prepareStatement(sql);
        pst.setDate(1, Uteis.getDataJDBC(Calendario.getDataComHoraZerada(dataAbertura)));
        pst.setInt(2, codigoColaboradorResponsavel);
        pst.setInt(3, codigoEmpresa);
        ResultSet rs = pst.executeQuery();
        if (rs.next())
            return montarDados(rs,con, nivelMontarDados);
        return null;
    }

    public JSONObject consultarUltimaAberturaMeta() throws Exception{
        String sql = "select * from aberturaMeta order by dia desc limit 1";
        PreparedStatement pst = con.prepareStatement(sql);
        ResultSet rs = pst.executeQuery();
        if (rs.next())
            return montarJsonDadosMinimos(rs);
        return null;
    }

    public AberturaMetaVO consultarUltimaAberturaMeta(Integer codigoColaboradorResponsavel, Integer codigoEmpresa,Integer nivelMontarDados) throws Exception{
        String sql = "select * from aberturaMeta where ColaboradorResponsavel = ? and empresa = ? order by dia desc limit 1";
        PreparedStatement pst = con.prepareStatement(sql);
        pst.setInt(1, codigoColaboradorResponsavel);
        pst.setInt(2, codigoEmpresa);
        ResultSet rs = pst.executeQuery();
        if (rs.next())
            return montarDados(rs,con, nivelMontarDados);
        return null;
    }

    public void validarSeTodosParticipanteMarcadosAbriraoMeta(List<GrupoColaboradorVO> lista, Date dia, Integer empresa) throws Exception {
        String sqlRefenciaTablea = "";
        String sqlCodigoColaborador = "";
        String sqlDiaInicio = "";
        String sqlEmpresa = "";
        int i = 0;
        sqlRefenciaTablea += "aberturaMeta am";
        sqlEmpresa += " am.empresa = " + empresa;
        sqlDiaInicio += " Cast (am.dia as Date) = Cast ('" + Uteis.getDataJDBC(dia) + "' as Date)";
        sqlCodigoColaborador += " am.colaboradorResponsavel in (" ;
        for (GrupoColaboradorVO grupo : lista) {
            for (GrupoColaboradorParticipanteVO participante : grupo.getGrupoColaboradorParticipanteVOs()) {
                if (participante.getGrupoColaboradorParticipanteEscolhido()) {
                    sqlCodigoColaborador += "" + participante.getUsuarioParticipante().getCodigo() + ",";
                    i++;
                }
            }
        }
        sqlCodigoColaborador+=")";
        sqlCodigoColaborador = sqlCodigoColaborador.replace(",)",")");

        if (!consultarAberturaMetaPorParticipanteGrupo(sqlRefenciaTablea, sqlCodigoColaborador, sqlDiaInicio, sqlEmpresa)) {
            throw new ConsistirException("Ainda não foi registrado nenhuma abertura de meta para os participantes selecionados.");
        }

    }

    // ****************** daqui pra baixo so regra de negocio ******************
    @SuppressWarnings("unchecked")
    public void validarPermissaoResponsavelTrocaColaboradorResponsavel(UsuarioVO obj, Integer empresa) throws Exception {
        UsuarioVO usuario = getFacade().getControleAcesso().verificarLoginUsuario(obj.getCodigo().intValue(), obj.getSenha().toUpperCase());
        if (usuario.getUsuarioPerfilAcessoVOs().size() == 0) {
            if (usuario.getAdministrador()) {
                return;
            }
            throw new Exception("O usuário informado não possui perfil de acesso.");
        }
        if (empresa == 0) {
            throw new Exception("Não há nenhuma empresa logada");
        }
        Iterator i = usuario.getUsuarioPerfilAcessoVOs().iterator();
        while (i.hasNext()) {
            UsuarioPerfilAcessoVO usuarioPerfilAcesso = (UsuarioPerfilAcessoVO) i.next();
            if (empresa.equals(usuarioPerfilAcesso.getEmpresa().getCodigo().intValue())) {
                usuarioPerfilAcesso.getPerfilAcesso().setPermissaoVOs(getFacade().getPermissao().consultarPermissaos(usuarioPerfilAcesso.getPerfilAcesso().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
                getFacade().getControleAcesso().verificarPermissaoUsuarioFuncionalidade(usuarioPerfilAcesso.getPerfilAcesso(), usuario, "LiberarTrocarColabordorAberturaDia", "7.39 - Liberar Trocar de Colabordores Abertura Dia");

            }
        }
    }

    public void executarNovaListaMeta(AberturaMetaVO obj, Boolean listaIsVenda) throws Exception {
        if (listaIsVenda) {
            obj.setFecharMetaVosVenda(getFacade().getFecharMeta().consultarFecharMetaVenda(obj.getCodigo(), Uteis.NIVELMONTARDADOS_METACOLABORADORRESPONSAVEL));
        } else {
            obj.setFecharMetaVosRetencao(getFacade().getFecharMeta().consultarFecharMetaRetencao(obj.getCodigo(), Uteis.NIVELMONTARDADOS_METACOLABORADORRESPONSAVEL));
        }

    }
    public void executarNovaListaMetaEstudio(AberturaMetaVO obj) throws Exception {
            obj.setFecharMetaVosEstudio(getFacade().getFecharMeta().consultarFecharMetaEstudio(obj.getCodigo(), Uteis.NIVELMONTARDADOS_METACOLABORADORRESPONSAVEL));
    }
    
    public void executarNovaListaMetaLead(AberturaMetaVO obj) throws Exception {
            obj.setFecharMetaVosLead(getFacade().getFecharMeta().consultarFecharMetaLeads(obj.getCodigo(), Uteis.NIVELMONTARDADOS_METACOLABORADORRESPONSAVEL));
    }

    public void inicializarDadosNovaListaVendaFecharMeta(String identificadorMeta, AberturaMetaVO aberturaMeta) throws Exception {
        FecharMetaVO fecharMeta = new FecharMetaVO();
        fecharMeta.setDataRegistro(aberturaMeta.getDia());
        fecharMeta.setIdentificadorMeta(identificadorMeta);
        aberturaMeta.adicionarObjFecharMetaVenda(fecharMeta);
        fecharMeta = null;
    }

    public void inicializarDadosNovaListaRetencaoFecharMeta(String identificadorMeta, AberturaMetaVO aberturaMeta) throws Exception {
        FecharMetaVO fecharMeta = new FecharMetaVO();
        fecharMeta.setIdentificadorMeta(identificadorMeta);
        aberturaMeta.adicionarObjFecharMetaRetencao(fecharMeta);
        fecharMeta = null;
    }

    /**
     * Metodo responsavel por atualizar as metas quando é clicado no botão
     * atualizar.
     *
     * @throws Exception
     */
    public void atualizarMetaVendaDia(AberturaMetaVO obj, Integer empresa) throws Exception {
        if (UteisValidacao.emptyNumber(obj.getEmpresaVO().getCodigo())) {
            obj.getEmpresaVO().setCodigo(empresa);
        }
        for (FecharMetaVO fecharMetaVo : obj.getFecharMetaVosVenda()) {
            if (fecharMetaVo.getIdentificadorMeta().equals(FasesCRMEnum.INDICACOES.getSigla())
                    || fecharMetaVo.getIdentificadorMeta().equals(FasesCRMEnum.AGENDAMENTO.getSigla())
                    || fecharMetaVo.getIdentificadorMeta().equals(FasesCRMEnum.VINTE_QUATRO_HORAS.getSigla())
                    || fecharMetaVo.getIdentificadorMeta().equals(FasesCRMEnum.VISITANTES_ANTIGOS.getSigla())
                    || fecharMetaVo.getIdentificadorMeta().equals(FasesCRMEnum.VISITA_RECORRENTE.getSigla())
                    || fecharMetaVo.getIdentificadorMeta().equals(FasesCRMEnum.RENOVACAO.getSigla())
                    || fecharMetaVo.getIdentificadorMeta().equals(FasesCRMEnum.DESISTENTES.getSigla())
                    || fecharMetaVo.getIdentificadorMeta().equals(FasesCRMEnum.EX_ALUNOS.getSigla())
                    || fecharMetaVo.getIdentificadorMeta().equals(FasesCRMEnum.LIGACAO_AGENDADOS_AMANHA.getSigla())) {
                fecharMetaVo = getFacade().getFecharMeta().consultarMetaPorDiaPorColaboradorResponsavel(fecharMetaVo.getIdentificadorMeta(), obj.getDia(), obj.getColaboradorResponsavel().getCodigo(), false, Uteis.NIVELMONTARDADOS_DADOSBASICOS, obj.getEmpresaVO().getCodigo());
                if (fecharMetaVo.getCodigo() != 0) {
                    obj.adicionarObjFecharMetaVenda(fecharMetaVo);
                    obj.setExisteMetaParaParticipante(true);
                }
            }
        }
        for (FecharMetaVO fecharMetaVo : obj.getFecharMetaVosEstudio()) {
            if (fecharMetaVo.getIdentificadorMeta().equals(FasesCRMEnum.SEM_AGENDAMENTO.getSigla())
                    || fecharMetaVo.getIdentificadorMeta().equals(FasesCRMEnum.ULTIMAS_SESSOES.getSigla())) {
                fecharMetaVo = getFacade().getFecharMeta().consultarMetaPorDiaPorColaboradorResponsavel(fecharMetaVo.getIdentificadorMeta(), obj.getDia(), obj.getColaboradorResponsavel().getCodigo(), false, Uteis.NIVELMONTARDADOS_DADOSBASICOS, obj.getEmpresaVO().getCodigo());
                if (fecharMetaVo.getCodigo() != 0) {
                    obj.adicionarObjFecharMetaEstudio(fecharMetaVo);
                }
            }
        }
    }

    /**
     * Metodo responsavel por atualizar as metas quando é clicado no botão
     * atualizar.
     */
    public void atualizarMetaRetencaoDia(AberturaMetaVO obj) throws Exception {
        for (FecharMetaVO fecharMetaVo : obj.getFecharMetaVosRetencao()) {
            if (fecharMetaVo.getIdentificadorMeta().equals(FasesCRMEnum.ANIVERSARIANTES.getSigla())
                    || fecharMetaVo.getIdentificadorMeta().equals(FasesCRMEnum.GRUPO_RISCO.getSigla())
                    || fecharMetaVo.getIdentificadorMeta().equals(FasesCRMEnum.FALTOSOS.getSigla())
                    || fecharMetaVo.getIdentificadorMeta().equals(FasesCRMEnum.VENCIDOS.getSigla())
                    || fecharMetaVo.getIdentificadorMeta().equals(FasesCRMEnum.POS_VENDA.getSigla())) {
                fecharMetaVo = getFacade().getFecharMeta().consultarMetaPorDiaPorColaboradorResponsavel(fecharMetaVo.getIdentificadorMeta(), obj.getDia(), obj.getColaboradorResponsavel().getCodigo(), false, Uteis.NIVELMONTARDADOS_DADOSBASICOS, obj.getEmpresaVO().getCodigo());
                if (fecharMetaVo.getCodigo() != 0) {
                    obj.adicionarObjFecharMetaRetencao(fecharMetaVo);
                    obj.setExisteMetaParaParticipante(true);
                }
            }
        }
    }

    public void atualizarMetaEstudioDia(AberturaMetaVO obj) throws Exception {
        for (FecharMetaVO fecharMetaVo : obj.getFecharMetaVosEstudio()) {
            if (fecharMetaVo.getIdentificadorMeta().equals(FasesCRMEnum.SEM_AGENDAMENTO.getSigla())
                    || fecharMetaVo.getIdentificadorMeta().equals(FasesCRMEnum.ULTIMAS_SESSOES.getSigla())) {
                fecharMetaVo = getFacade().getFecharMeta().consultarMetaPorDiaPorColaboradorResponsavel(fecharMetaVo.getIdentificadorMeta(), obj.getDia(), obj.getColaboradorResponsavel().getCodigo(), false, Uteis.NIVELMONTARDADOS_DADOSBASICOS, obj.getEmpresaVO().getCodigo());
                if (fecharMetaVo.getCodigo() != 0) {
                    obj.adicionarObjFecharMetaEstudio(fecharMetaVo);
                    obj.setExisteMetaParaParticipante(true);
                }
            }
        }
    }
    
    /**
     * Metodo que preenche as metas no momento em que é clicado no
     * selectBooleanCheckbox de cada GrupoColaboradorParticipante.
     *
     * @param obj, empresa
     * @throws Exception
     */
    @SuppressWarnings("unchecked")
    public void atualizarMetaVendaPorColaborador(AberturaMetaVO obj, Integer empresa) throws Exception {
        String sql = obterColaboradoresMarcados(obj.getGrupoColaboradorListaVenda());
        if (!sql.trim().equals("")) {
            validarSeTodosParticipanteMarcadosAbriraoMeta(obj.getGrupoColaboradorListaVenda(), obj.getDia(), empresa);
            if (obj.getFecharMetaVosVenda().isEmpty()) {
                executarNovaListaMeta(obj, true);
                obj.setFecharMetaVosVenda(Ordenacao.ordenarLista(obj.getFecharMetaVosVenda(), "ordem"));
            }
            for (FecharMetaVO fecharMeta : obj.getFecharMetaVosVenda()) {
                if (fecharMeta.getIdentificadorMeta().equals(FasesCRMEnum.INDICACOES.getSigla())
                        || fecharMeta.getIdentificadorMeta().equals(FasesCRMEnum.AGENDAMENTO.getSigla())
                        || fecharMeta.getIdentificadorMeta().equals(FasesCRMEnum.VINTE_QUATRO_HORAS.getSigla())
                        || fecharMeta.getIdentificadorMeta().equals(FasesCRMEnum.VISITANTES_ANTIGOS.getSigla())
                        || fecharMeta.getIdentificadorMeta().equals(FasesCRMEnum.VISITA_RECORRENTE.getSigla())
                        || fecharMeta.getIdentificadorMeta().equals(FasesCRMEnum.RENOVACAO.getSigla())
                        || fecharMeta.getIdentificadorMeta().equals(FasesCRMEnum.EX_ALUNOS.getSigla())
                        || fecharMeta.getIdentificadorMeta().equals(FasesCRMEnum.DESISTENTES.getSigla())
                        || fecharMeta.getIdentificadorMeta().equals(FasesCRMEnum.LIGACAO_AGENDADOS_AMANHA.getSigla())) {
                    String identificadorMeta = fecharMeta.getIdentificadorMeta();
                    fecharMeta = atualizarMetaVindoSelecionarColaborador(obj, sql, identificadorMeta);
                    fecharMeta.setIdentificadorMeta(identificadorMeta);
                    try {
                        fecharMeta.setFase(FasesCRMEnum.getFasePorSigla(identificadorMeta));
                    }catch (Exception e){}
                    obj.adicionarObjFecharMetaVenda(fecharMeta);
                    obj.setExisteMetaParaParticipante(true);
                }
            }
        }
        for (FecharMetaVO fecharMetaVo : obj.getFecharMetaVosEstudio()) {
            if (fecharMetaVo.getIdentificadorMeta().equals(FasesCRMEnum.SEM_AGENDAMENTO.getSigla())) {
                fecharMetaVo = getFacade().getFecharMeta().consultarMetaPorDiaPorColaboradorResponsavel(FasesCRMEnum.SEM_AGENDAMENTO.getSigla(), obj.getDia(), obj.getColaboradorResponsavel().getCodigo(), false, Uteis.NIVELMONTARDADOS_DADOSBASICOS, obj.getEmpresaVO().getCodigo());
                obj.adicionarObjFecharMetaEstudio(fecharMetaVo);
            } else if (fecharMetaVo.getIdentificadorMeta().equals(FasesCRMEnum.ULTIMAS_SESSOES.getSigla())) {
                fecharMetaVo = getFacade().getFecharMeta().consultarMetaPorDiaPorColaboradorResponsavel(FasesCRMEnum.ULTIMAS_SESSOES.getSigla(), obj.getDia(), obj.getColaboradorResponsavel().getCodigo(), false, Uteis.NIVELMONTARDADOS_DADOSBASICOS, obj.getEmpresaVO().getCodigo());
                obj.adicionarObjFecharMetaEstudio(fecharMetaVo);
            }
        }
    }

    /**
     * Metodo que preenche as metas no momento em que é clicado no
     * selectBooleanCheckbox de cada GrupoColaboradorParticipante.
     *
     * @param obj aberturaMetaVO
     * @throws Exception
     */
    @SuppressWarnings("unchecked")
    public void atualizarMetaRetencaoPorColaborador(AberturaMetaVO obj, Integer empresa) throws Exception {
        String sql = obterColaboradoresMarcados(obj.getGrupoColaboradorListaRetencao());
        if (!sql.trim().equals("")) {
            validarSeTodosParticipanteMarcadosAbriraoMeta(obj.getGrupoColaboradorListaRetencao(), obj.getDia(), empresa);
            if (obj.getFecharMetaVosRetencao().isEmpty()) {
                executarNovaListaMeta(obj, false);
                obj.setFecharMetaVosRetencao(Ordenacao.ordenarLista(obj.getFecharMetaVosRetencao(), "ordem"));
            }
            for (FecharMetaVO fecharMeta : obj.getFecharMetaVosRetencao()) {
                if (fecharMeta.getIdentificadorMeta().equals(FasesCRMEnum.ANIVERSARIANTES.getSigla())
                        || fecharMeta.getIdentificadorMeta().equals(FasesCRMEnum.GRUPO_RISCO.getSigla())
                        || fecharMeta.getIdentificadorMeta().equals(FasesCRMEnum.FALTOSOS.getSigla())
                        || fecharMeta.getIdentificadorMeta().equals(FasesCRMEnum.VENCIDOS.getSigla())
                        || fecharMeta.getIdentificadorMeta().equals(FasesCRMEnum.POS_VENDA.getSigla())) {
                    String identificadorMeta = fecharMeta.getIdentificadorMeta();
                    fecharMeta = atualizarMetaVindoSelecionarColaborador(obj, sql, fecharMeta.getIdentificadorMeta());
                    fecharMeta.setIdentificadorMeta(identificadorMeta);
                    fecharMeta.setFase(FasesCRMEnum.getFasePorSigla(identificadorMeta));
                    obj.adicionarObjFecharMetaRetencao(fecharMeta);
                    obj.setExisteMetaParaParticipante(true);
                }
            }
        }
    }
    
    public void atualizarMetaEstudioPorColaborador(AberturaMetaVO obj, Integer empresa) throws Exception {
        String sql = obterColaboradoresMarcados(obj.getGrupoColaboradorListaEstudio());
        if (!sql.trim().equals("")) {
            validarSeTodosParticipanteMarcadosAbriraoMeta(obj.getGrupoColaboradorListaEstudio(), obj.getDia(), empresa);
            if (obj.getFecharMetaVosEstudio().isEmpty()) {
                executarNovaListaMetaEstudio(obj);
                obj.setFecharMetaVosEstudio(Ordenacao.ordenarLista(obj.getFecharMetaVosEstudio(), "ordem"));
            }
            for (FecharMetaVO fecharMeta : obj.getFecharMetaVosEstudio()) {
                if (fecharMeta.getIdentificadorMeta().equals(FasesCRMEnum.SEM_AGENDAMENTO.getSigla())
                        || fecharMeta.getIdentificadorMeta().equals(FasesCRMEnum.ULTIMAS_SESSOES.getSigla())) {
                    String identificadorMeta = fecharMeta.getIdentificadorMeta();
                    fecharMeta = atualizarMetaVindoSelecionarColaborador(obj, sql, fecharMeta.getIdentificadorMeta());
                    fecharMeta.setIdentificadorMeta(identificadorMeta);
                    fecharMeta.setFase(FasesCRMEnum.getFasePorSigla(identificadorMeta));
                    obj.adicionarObjFecharMetaEstudio(fecharMeta);
                    obj.setExisteMetaParaParticipante(true);
                }
            }
        }
    }

    public String obterColaboradoresMarcados(List<GrupoColaboradorVO> lista) {
        String sql = "";
        for (GrupoColaboradorVO grupo : lista) {
            for (GrupoColaboradorParticipanteVO participante : grupo.getGrupoColaboradorParticipanteVOs()) {
                if (participante.getGrupoColaboradorParticipanteEscolhido()) {
                    sql += "aberturameta.colaboradorresponsavel = " + participante.getUsuarioParticipante().getCodigo() + "  or ";
                }
            }
        }
        if (!sql.equals("")) {
            int tamanho = sql.length();
            sql = sql.substring(0, (tamanho - 3));
        }
        return sql;
    }

    public FecharMetaVO atualizarMetaVindoSelecionarColaborador(AberturaMetaVO obj, String sqlCP, String identificador) throws Exception {
        return getFacade().getFecharMeta().consultarMetaPorDiaPorColaboradorResponsavel(identificador, obj.getDia(), sqlCP, false, Uteis.NIVELMONTARDADOS_MINIMOS, obj.getEmpresaVO().getCodigo());
    }

    public void validarAberturaMetaQuandoCalcularMetaDia(AberturaMetaVO obj) throws Exception {
        if (Calendario.maior(obj.getDia(), Calendario.hoje())) {
            throw new Exception("O dia da Abertura é posterior ao dia atual !");
        }
    }

    /**
     * Metodo que define qual sera o dia a ser consultado para as metas.
     */
    @SuppressWarnings("unchecked")
    public void executarDefinicaoDataParaCalcularAberturaDia(AberturaMetaVO obj, PermissaoAcessoMenuVO permissoes, boolean estudio, QuarentenaVO quarentenaCRM) throws Exception {
        //Metodo que verifica se o dia da abertura de meta é posterior a data atual
        validarAberturaMetaQuandoCalcularMetaDia(obj);

        //Setando na abertura de meta as configuracoes do sistema CRM
        obj.setConfiguracaoSistemaVO(getFacade().getConfiguracaoSistemaCRM().consultarConfiguracaoSistemaCRM(Uteis.NIVELMONTARDADOS_TODOS));


        //Verificando se o dia definido para abertura de meta e feriado
        if (getFacade().getFeriado().validarFeriadoPorEmpresaParaCalculoAberturaMeta(obj.getEmpresaVO(), obj.getDia())) {
            throw new ConsistirException("A Academia não é aberta nesse dia: " + Uteis.getData(obj.getDia()) + " , pois é feriado.");
        }

        if ((Uteis.getDiaDaSemana(obj.getDia(), DiasDaSemana.SABADO) && !obj.getConfiguracaoSistemaVO().getAbertoSabado())
                || (Uteis.getDiaDaSemana(obj.getDia(), DiasDaSemana.DOMINGO) && !obj.getConfiguracaoSistemaVO().getAbertoDomingo())) {
            throw new ConsistirException("A Academia não é aberta nesse dia: " + obj.getDia_Apresentar());
        }

        executarCalculoMetaAuxiliar(obj, permissoes, quarentenaCRM);
        if (estudio) {
            executarCalculoMetaEstudio(obj, permissoes);
        }


        obj.setFecharMetaVosVenda(Ordenacao.ordenarLista(obj.getFecharMetaVosVenda(), "ordem"));
        obj.setFecharMetaVosRetencao(Ordenacao.ordenarLista(obj.getFecharMetaVosRetencao(), "ordem"));
        obj.setFecharMetaVosEstudio(Ordenacao.ordenarLista(obj.getFecharMetaVosEstudio(), "ordem"));
        obj.setFecharMetaVosLead(Ordenacao.ordenarLista(obj.getFecharMetaVosLead(), "ordem"));

    }

    /**
     * <AUTHOR> 05/12/2011
     */
    private List<Date> obterDatasCalcular(AberturaMetaVO obj, boolean anterior) throws Exception {
        List<Date> diasCalcular = new ArrayList<Date>();
        boolean diaCalcular = true;
        Date dataCalcular = obj.getDia();
        while (diaCalcular) {
            diasCalcular.add(dataCalcular);
            if (anterior) {
                dataCalcular = Uteis.obterDataAnterior(dataCalcular, 1);
            } else //somar um dia
            {
                dataCalcular = Uteis.somarCampoData(dataCalcular, Calendar.DAY_OF_MONTH, 1);
            }
            if (getFacade().getConfiguracaoSistemaCRM().verificarDiaAcademiaAberta(dataCalcular, obj.getConfiguracaoSistemaVO(), obj.getEmpresaVO())) {
                diaCalcular = false;
            }
        }
        return diasCalcular;
    }

    private String verificarCalcularMeta(AberturaMetaVO obj, FasesCRMEnum fase, boolean usarDivisao, List<String> tipos, TiposVinculosFase tiposVinculosFase) throws Exception {
        String tipoVinculo = "";
        if (usarDivisao) {
            for (String tipo : tipos) {
                if (tiposVinculosFase.verificarAberturaColaboradorFase(tipo, fase)) {
                    tipoVinculo = (tipoVinculo.equals("") ? "'" + tipo + "'" : tipoVinculo + ",'" + tipo + "'");
                }
            }
        } else {
            tipoVinculo = "TODOS";
        }
        return tipoVinculo;
    }

    /**
     * Metodo que realiza o calculo das metas para Aniversariantes, Agenda,
     * Faltosos, Perda, PosVenda, Renovacao, Risco, Vinte Quatro Horas e Padrao
     */

    public void executarCalculoMetaLeads(AberturaMetaVO obj, PermissaoAcessoMenuVO permissoes) throws Exception {
        TiposVinculosFase tiposVinculosFase = new TiposVinculosFase(con);
        List<String> tipos = tiposVinculosFase.consultarTiposColaborador(obj.getColaboradorResponsavel().getColaboradorVO().getCodigo());
        Boolean usarDivisao = tiposVinculosFase.verificarUsarDivisao();

        //inicializando variaveis
        obj.setFecharMetaVosLead(new ArrayList<FecharMetaVO>());

        executarCalculoMetaLeadsHoje(obj, verificarCalcularMeta(obj, FasesCRMEnum.LEADS_HOJE, usarDivisao, tipos, tiposVinculosFase));
        executarCalculoMetaLeadsAcumulado(obj, verificarCalcularMeta(obj, FasesCRMEnum.LEADS_ACUMULADAS, usarDivisao, tipos, tiposVinculosFase));
    }

    public  void executarCalculoMetaAuxiliarEspecifico(AberturaMetaVO obj, PermissaoAcessoMenuVO permissoes, QuarentenaVO quarentenaCRM) throws Exception {
        TiposVinculosFase tiposVinculosFase = new TiposVinculosFase(con);
        List<String> tipos = tiposVinculosFase.consultarTiposColaborador(obj.getColaboradorResponsavel().getColaboradorVO().getCodigo());
        Boolean usarDivisao = tiposVinculosFase.verificarUsarDivisao();

        //inicializando variaveis
        obj.setFecharMetaVosRetencao(new ArrayList<FecharMetaVO>());
        obj.setFecharMetaVosVenda(new ArrayList<FecharMetaVO>());
        obj.setFecharMetaVosLead(new ArrayList<FecharMetaVO>());

        List<Date> datasCalcular = null;
        try {
            datasCalcular = obterDatasCalcular(obj, true);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        List<Date> datasCalcularAniversariantes = obterDatasCalcular(obj, false);

        switch (obj.getFaseEspecifica()){

            case ANIVERSARIANTES:
                if (permissoes.getMetaAniversariante()) {
                    executarCalculoMetaAniversariante(obj, verificarCalcularMeta(obj, FasesCRMEnum.ANIVERSARIANTES, usarDivisao, tipos, tiposVinculosFase), datasCalcularAniversariantes);
                }
                break;

            case AGENDAMENTO:
                if (permissoes.getMetaAgendamento()) {
                    executarCalculoMetaAgenda(obj, verificarCalcularMeta(obj, FasesCRMEnum.AGENDAMENTO, usarDivisao, tipos, tiposVinculosFase), datasCalcular, FasesCRMEnum.AGENDAMENTO.getSigla());
                    executarCalculoMetaAgenda(obj, verificarCalcularMeta(obj, FasesCRMEnum.LIGACAO_AGENDADOS_AMANHA, usarDivisao, tipos, tiposVinculosFase), datasCalcular, FasesCRMEnum.LIGACAO_AGENDADOS_AMANHA.getSigla());
                }
                break;
            case  LIGACAO_AGENDADOS_AMANHA:
                if (permissoes.getMetaLigacaoAgendamentoAmanha()) {
                    executarCalculoMetaLigacaoAgendaAmanha(obj, verificarCalcularMeta(obj, FasesCRMEnum.LIGACAO_AGENDADOS_AMANHA, usarDivisao, tipos, tiposVinculosFase), datasCalcularAniversariantes);

                }
                break;

            case FALTOSOS :
                if (permissoes.getMetaFaltosos() && !(quarentenaCRM.getCodigo() != null && quarentenaCRM.isAtiva())) {
                    executarCalculoMetaFaltosos(quarentenaCRM, obj, verificarCalcularMeta(obj, FasesCRMEnum.FALTOSOS, usarDivisao, tipos, tiposVinculosFase), datasCalcular);
                }
                break;
            case DESISTENTES:
                if (permissoes.getMetaPerda()) {
                    executarCalculoMetaPerda(obj, verificarCalcularMeta(obj, FasesCRMEnum.DESISTENTES, usarDivisao, tipos, tiposVinculosFase), datasCalcular);
                }
                break;
            case VENCIDOS:
                if (permissoes.getMetaVencidos()) {
                    executarCalculoVencidos(obj, verificarCalcularMeta(obj, FasesCRMEnum.VENCIDOS, usarDivisao, tipos, tiposVinculosFase));
                }
                break;
            case POS_VENDA :
                if (permissoes.getMetaPosVenda()) {
                    executarCalculoMetaPosVenda(obj, verificarCalcularMeta(obj, FasesCRMEnum.POS_VENDA, usarDivisao, tipos, tiposVinculosFase), datasCalcular);
                }
                break;
            case EX_ALUNOS:
                if (permissoes.getMetaExAlunos()) {
                    executarCalculoMetaExAlunos(obj, verificarCalcularMeta(obj, FasesCRMEnum.EX_ALUNOS, usarDivisao, tipos, tiposVinculosFase), datasCalcular);
                }
                break;
            case RENOVACAO :
                if (permissoes.getMetaRenovacao()) {
                    executarCalculoMetaRenovacao(obj, verificarCalcularMeta(obj, FasesCRMEnum.RENOVACAO, usarDivisao, tipos, tiposVinculosFase), datasCalcular);
                }
                break;
            case GRUPO_RISCO:
                if (permissoes.getMetaGrupoRisco() && !(quarentenaCRM.getCodigo() != null && quarentenaCRM.isAtiva())) {
                    executarCalculoMetaRisco(obj, verificarCalcularMeta(obj, FasesCRMEnum.GRUPO_RISCO, usarDivisao, tipos, tiposVinculosFase));
                }
                break;
            case VINTE_QUATRO_HORAS:
                if (permissoes.getMetaVinteQuatroHoras()) {
                    executarCalculoMetaVinteQuatroHoras(obj, verificarCalcularMeta(obj, FasesCRMEnum.VINTE_QUATRO_HORAS, usarDivisao, tipos, tiposVinculosFase), datasCalcular);
                }
                break;
            case VISITANTES_ANTIGOS:
                if (permissoes.getMetaVisitantesAntigos()) {
                    executarCalculoMetaVisitantesAntigos(obj, verificarCalcularMeta(obj, FasesCRMEnum.VISITANTES_ANTIGOS, usarDivisao, tipos, tiposVinculosFase), datasCalcular);
                }
                break;
            case VISITA_RECORRENTE:
                if (permissoes.getMetaVisitantesAntigos()) {
                    executarCalculoMetaVisitantesRecorrentes(obj, verificarCalcularMeta(obj, FasesCRMEnum.VISITA_RECORRENTE, usarDivisao, tipos, tiposVinculosFase), datasCalcular);
                }
                break;
            case LEADS_HOJE:
                if(obj.getEmpresaVO().utilizaAlgumaIntegracaoLeadsCrm()){
                    executarCalculoMetaLeadsHoje(obj, verificarCalcularMeta(obj, FasesCRMEnum.LEADS_HOJE, usarDivisao, tipos, tiposVinculosFase));
                    executarCalculoMetaLeadsAcumulado(obj, verificarCalcularMeta(obj, FasesCRMEnum.LEADS_ACUMULADAS, usarDivisao, tipos, tiposVinculosFase));
                }
                break;
            case ALUNO_ULTIMO_ACESSO_GYMPASS:
                executarCalculoMetaUltimoAcessoGymPass(obj, verificarCalcularMeta(obj, FasesCRMEnum.ALUNO_ULTIMO_ACESSO_GYMPASS, usarDivisao, tipos, tiposVinculosFase), datasCalcular);
                break;
            case FILA_ESPERA_TURMA_CRM:
                executarCalculoMetaFilaEsperaTurmaCrm(obj, verificarCalcularMeta(obj, FasesCRMEnum.FILA_ESPERA_TURMA_CRM, usarDivisao, tipos, tiposVinculosFase), datasCalcular);
                break;

            case ALUNO_GYMPASS:
                executarCalculoMetaGymPass(obj, verificarCalcularMeta(obj, FasesCRMEnum.ALUNO_GYMPASS, usarDivisao, tipos, tiposVinculosFase), datasCalcular);
                break;
            case INDICACOES:
                executarCalculoMetaPadrao(obj, usarDivisao, tipos, tiposVinculosFase, permissoes);
                break;

        }
    }
    public void executarCalculoMetaAuxiliar(AberturaMetaVO obj, PermissaoAcessoMenuVO permissoes, QuarentenaVO quarentenaCRM) throws Exception {
        TiposVinculosFase tiposVinculosFase = new TiposVinculosFase(con);
        List<String> tipos = tiposVinculosFase.consultarTiposColaborador(obj.getColaboradorResponsavel().getColaboradorVO().getCodigo());
        Boolean usarDivisao = tiposVinculosFase.verificarUsarDivisao();

        //inicializando variaveis
        obj.setFecharMetaVosRetencao(new ArrayList<FecharMetaVO>());
        obj.setFecharMetaVosVenda(new ArrayList<FecharMetaVO>());
        obj.setFecharMetaVosLead(new ArrayList<FecharMetaVO>());

        List<Date> datasCalcular = obterDatasCalcular(obj, true);
        List<Date> datasCalcularAniversariantes = obterDatasCalcular(obj, false);

        if(obj.getFaseEspecifica() == null ) {

            if (permissoes.getMetaAniversariante()) {
                executarCalculoMetaAniversariante(obj, verificarCalcularMeta(obj, FasesCRMEnum.ANIVERSARIANTES, usarDivisao, tipos, tiposVinculosFase), datasCalcularAniversariantes);
            }

            if (permissoes.getMetaAgendamento()) {
                executarCalculoMetaAgenda(obj, verificarCalcularMeta(obj, FasesCRMEnum.AGENDAMENTO, usarDivisao, tipos, tiposVinculosFase), datasCalcular, FasesCRMEnum.AGENDAMENTO.getSigla());
            }

            if (permissoes.getMetaLigacaoAgendamentoAmanha()) {
                executarCalculoMetaLigacaoAgendaAmanha(obj, verificarCalcularMeta(obj, FasesCRMEnum.LIGACAO_AGENDADOS_AMANHA, usarDivisao, tipos, tiposVinculosFase), datasCalcularAniversariantes);
            }

            if (permissoes.getMetaFaltosos() && !(quarentenaCRM.getCodigo() != null && quarentenaCRM.isAtiva())) {
                executarCalculoMetaFaltosos(quarentenaCRM, obj, verificarCalcularMeta(obj, FasesCRMEnum.FALTOSOS, usarDivisao, tipos, tiposVinculosFase), datasCalcular);
            }

            if (permissoes.getMetaPerda()) {
                executarCalculoMetaPerda(obj, verificarCalcularMeta(obj, FasesCRMEnum.DESISTENTES, usarDivisao, tipos, tiposVinculosFase), datasCalcular);
            }

            if (permissoes.getMetaVencidos()) {
                executarCalculoVencidos(obj, verificarCalcularMeta(obj, FasesCRMEnum.VENCIDOS, usarDivisao, tipos, tiposVinculosFase));
            }

            if (permissoes.getMetaPosVenda()) {
                executarCalculoMetaPosVenda(obj, verificarCalcularMeta(obj, FasesCRMEnum.POS_VENDA, usarDivisao, tipos, tiposVinculosFase), datasCalcular);
            }

            if (permissoes.getMetaExAlunos()) {
                executarCalculoMetaExAlunos(obj, verificarCalcularMeta(obj, FasesCRMEnum.EX_ALUNOS, usarDivisao, tipos, tiposVinculosFase), datasCalcular);
            }

            if (permissoes.getMetaRenovacao()) {
                executarCalculoMetaRenovacao(obj, verificarCalcularMeta(obj, FasesCRMEnum.RENOVACAO, usarDivisao, tipos, tiposVinculosFase), datasCalcular);
            }

            if (permissoes.getMetaGrupoRisco() && !(quarentenaCRM.getCodigo() != null && quarentenaCRM.isAtiva())) {
                executarCalculoMetaRisco(obj, verificarCalcularMeta(obj, FasesCRMEnum.GRUPO_RISCO, usarDivisao, tipos, tiposVinculosFase));
            }

            if (permissoes.getMetaVinteQuatroHoras()) {
                executarCalculoMetaVinteQuatroHoras(obj, verificarCalcularMeta(obj, FasesCRMEnum.VINTE_QUATRO_HORAS, usarDivisao, tipos, tiposVinculosFase), datasCalcular);
            }

            if (permissoes.getMetaVisitantesAntigos()) {
                executarCalculoMetaVisitantesAntigos(obj, verificarCalcularMeta(obj, FasesCRMEnum.VISITANTES_ANTIGOS, usarDivisao, tipos, tiposVinculosFase), datasCalcular);
            }

            if (permissoes.getMetaVisitantesAntigos()) {
                executarCalculoMetaVisitantesRecorrentes(obj, verificarCalcularMeta(obj, FasesCRMEnum.VISITA_RECORRENTE, usarDivisao, tipos, tiposVinculosFase), datasCalcularAniversariantes);
            }

            if (obj.getEmpresaVO().utilizaAlgumaIntegracaoLeadsCrm()) {
                executarCalculoMetaLeadsHoje(obj, verificarCalcularMeta(obj, FasesCRMEnum.LEADS_HOJE, usarDivisao, tipos, tiposVinculosFase));
                executarCalculoMetaLeadsAcumulado(obj, verificarCalcularMeta(obj, FasesCRMEnum.LEADS_ACUMULADAS, usarDivisao, tipos, tiposVinculosFase));
            }

            executarCalculoMetaUltimoAcessoGymPass(obj, verificarCalcularMeta(obj, FasesCRMEnum.ALUNO_ULTIMO_ACESSO_GYMPASS, usarDivisao, tipos, tiposVinculosFase), datasCalcular);

            executarCalculoMetaFilaEsperaTurmaCrm(obj, verificarCalcularMeta(obj, FasesCRMEnum.FILA_ESPERA_TURMA_CRM, usarDivisao, tipos, tiposVinculosFase), datasCalcular);

            executarCalculoMetaGymPass(obj, verificarCalcularMeta(obj, FasesCRMEnum.ALUNO_GYMPASS, usarDivisao, tipos, tiposVinculosFase), datasCalcular);

            executarCalculoMetaPadrao(obj, usarDivisao, tipos, tiposVinculosFase, permissoes);
        }
        else{
            executarCalculoMetaAuxiliarEspecifico(obj, permissoes, quarentenaCRM);
        }
    }

    public void executarCalculoMetaAniversariante(AberturaMetaVO obj, String tiposVinculos, List<Date> datas) throws Exception {
        FecharMetaVO fecharMeta = new FecharMetaVO();
        // Regras de Negócio Aniversariante
        fecharMeta.setDataRegistro(obj.getDia());
        fecharMeta.setIdentificadorMeta("AN");
        if (!UteisValidacao.emptyString(tiposVinculos)) {
            for (Date dataCalcular : datas) {
                fecharMeta.setMeta(fecharMeta.getMeta() + consultarCalculoMetaQtdeClienteDataAniversariantePorMesEPorDia(dataCalcular,
                        obj.getColaboradorResponsavel().getColaboradorVO().getCodigo(), obj.getEmpresaVO().getCodigo(), tiposVinculos, obj.getCodigoClientesObjecaoDefinitiva()));
            }
        } else {
            fecharMeta.setMetaCalculada(false);
        }
        fecharMeta.setAberturaMetaVO(obj);
        obj.getFecharMetaVosRetencao().add(fecharMeta);
        fecharMeta = null;

    }

    public void executarCalculoMetaVisitantesRecorrentes(AberturaMetaVO obj, String tiposVinculos, List<Date> datas) throws Exception {
        FecharMetaVO fecharMeta = new FecharMetaVO();
        // Regras de Negócio VISITANTES RECORRENTES
        fecharMeta.setDataRegistro(obj.getDia());
        fecharMeta.setIdentificadorMeta(FasesCRMEnum.VISITA_RECORRENTE.getSigla());
        if (!UteisValidacao.emptyString(tiposVinculos)) {
            for (Date dataCalcular : datas) {
                fecharMeta.setMeta(fecharMeta.getMeta() + consultarCalculoMetaQtdeClienteVisitanteRecorrente(dataCalcular,
                        obj.getColaboradorResponsavel().getColaboradorVO().getCodigo(), obj.getEmpresaVO().getCodigo(), tiposVinculos, obj.getCodigoClientesObjecaoDefinitiva()));
            }
        } else {
            fecharMeta.setMetaCalculada(false);
        }
        fecharMeta.setAberturaMetaVO(obj);
        obj.getFecharMetaVosVenda().add(fecharMeta);
        fecharMeta = null;

    }
    public void executarCalculoMetaVinteQuatroHoras(AberturaMetaVO obj, String tiposVinculos, List<Date> datas) throws Exception {
        // Regras de Negocio 24 Horas
        FecharMetaVO fecharMeta = new FecharMetaVO();
        fecharMeta.setDataRegistro(obj.getDia());
        fecharMeta.setIdentificadorMeta("HO");
        if (!UteisValidacao.emptyString(tiposVinculos)) {
            for (Date dataCalcular : datas) {
                fecharMeta.setMeta(fecharMeta.getMeta() + consultarCalculoMetaQtdeClienteVinteQuatroHoras(obj.getColaboradorResponsavel().getColaboradorVO().getCodigo(), dataCalcular, obj.getEmpresaVO().getCodigo(), tiposVinculos, obj.getCodigoPessoaObjecaoDefinitiva()));
            }
        } else {
            fecharMeta.setMetaCalculada(false);
        }
        fecharMeta.setAberturaMetaVO(obj);
        obj.getFecharMetaVosVenda().add(fecharMeta);
        fecharMeta = null;

    }

    public void executarCalculoMetaGymPass(AberturaMetaVO obj, String tiposVinculos, List<Date> datas) throws Exception {
        FecharMetaVO fecharMeta = new FecharMetaVO();
        fecharMeta.setDataRegistro(obj.getDia());
        fecharMeta.setIdentificadorMeta(FasesCRMEnum.ALUNO_GYMPASS.getSigla());
        if (!UteisValidacao.emptyString(tiposVinculos)) {
            for (Date dataCalcular : datas) {
                fecharMeta.setMeta(fecharMeta.getMeta() + consultarCalculoMetaQtdeClienteGymPass(obj.getColaboradorResponsavel().getColaboradorVO().getCodigo(), dataCalcular, obj.getEmpresaVO().getCodigo(), tiposVinculos, obj.getCodigoClientesObjecaoDefinitiva()));
            }
        } else {
            fecharMeta.setMetaCalculada(false);
        }
        fecharMeta.setAberturaMetaVO(obj);
        obj.getFecharMetaVosVenda().add(fecharMeta);
        fecharMeta = null;

    }

    public void executarCalculoMetaUltimoAcessoGymPass(AberturaMetaVO obj, String tiposVinculos, List<Date> datas) throws Exception {
        FecharMetaVO fecharMeta = new FecharMetaVO();
        fecharMeta.setDataRegistro(obj.getDia());
        fecharMeta.setIdentificadorMeta(FasesCRMEnum.ALUNO_ULTIMO_ACESSO_GYMPASS.getSigla());
        if (!UteisValidacao.emptyString(tiposVinculos)) {
            for (Date dataCalcular : datas) {
                fecharMetaUltimoAcessoGymPass(obj, fecharMeta, obj.getConfiguracaoSistemaVO().getConfiguracaoDiasMetasUltimoAcessoGymp(), dataCalcular, tiposVinculos);
            }
        } else {
            fecharMeta.setMetaCalculada(false);
        }
        fecharMeta.setAberturaMetaVO(obj);
        obj.getFecharMetaVosVenda().add(fecharMeta);
        fecharMeta = null;

    }

    public void executarCalculoMetaFilaEsperaTurmaCrm(AberturaMetaVO obj, String tiposVinculos, List<Date> datas) throws Exception {
        FecharMetaVO fecharMeta = new FecharMetaVO();
        fecharMeta.setDataRegistro(obj.getDia());
        fecharMeta.setIdentificadorMeta(FasesCRMEnum.FILA_ESPERA_TURMA_CRM.getSigla());
        if (!UteisValidacao.emptyString(tiposVinculos)) {
            for (Date dataCalcular : datas) {
                fecharMetaFilaEsperaTurmaCrm(obj, fecharMeta, dataCalcular, tiposVinculos);
            }
        } else {
            fecharMeta.setMetaCalculada(false);
        }
        fecharMeta.setAberturaMetaVO(obj);
        obj.getFecharMetaVosVenda().add(fecharMeta);
        fecharMeta = null;

    }
    public void fecharMetaFilaEsperaTurmaCrm(AberturaMetaVO obj, FecharMetaVO metaUltimogymAluno, Date dia, String tiposVinculos) throws Exception {
            metaUltimogymAluno.setMeta(metaUltimogymAluno.getMeta()
                    + consultarCalculoMetaQtdeGenerico(
                    FasesCRMSQL.sqlFilaEsperaTurmaCRM(dia, obj.getColaboradorResponsavel().getColaboradorVO().getCodigo(),
                            obj.getEmpresaVO().getCodigo(), true, tiposVinculos, obj.getCodigoClientesObjecaoDefinitiva())));
    }
    public void fecharMetaUltimoAcessoGymPass(AberturaMetaVO obj, FecharMetaVO metaUltimogymAluno, List<ConfiguracaoDiasMetasTO> objetos, Date dia, String tiposVinculos) throws Exception {
        for (ConfiguracaoDiasMetasTO tempoGympass : objetos) {
            metaUltimogymAluno.setMeta(metaUltimogymAluno.getMeta()
                    + consultarCalculoMetaQtdeGenerico(
                    FasesCRMSQL.sqlUltimoAcessoGympass(dia, obj.getColaboradorResponsavel().getColaboradorVO().getCodigo(),
                            tempoGympass, obj.getEmpresaVO().getCodigo(), true, tiposVinculos, obj.getCodigoClientesObjecaoDefinitiva())));
        }
    }

    public void executarCalculoMetaAgenda(AberturaMetaVO obj, String tipoVinculos, List<Date> datas, String Identificador) throws Exception {

        // Regras de Negocio Agenda
        FecharMetaVO fecharMeta = new FecharMetaVO();
        fecharMeta.setDataRegistro(obj.getDia());
        fecharMeta.setIdentificadorMeta(Identificador);
        if (!UteisValidacao.emptyString(tipoVinculos)) {
            for (Date dataCalcular : datas) {
                fecharMeta.setMeta(fecharMeta.getMeta() + consultarCalculoMetaQtdeClienteAgendadosParaDiaHoje(obj.getColaboradorResponsavel().getCodigo(), dataCalcular, obj.getEmpresaVO().getCodigo(), tipoVinculos, obj.getCodigoClientesObjecaoDefinitiva()));
            }
        } else {
            fecharMeta.setMetaCalculada(false);
        }

        fecharMeta.setAberturaMetaVO(obj);
        obj.getFecharMetaVosVenda().add(fecharMeta);
        fecharMeta = null;
    }

    public void executarCalculoMetaLigacaoAgendaAmanha(AberturaMetaVO obj, String tipoVinculos, List<Date> datas) throws Exception {

        FecharMetaVO fecharMeta = new FecharMetaVO();
        fecharMeta.setDataRegistro(obj.getDia());
        fecharMeta.setIdentificadorMeta(FasesCRMEnum.LIGACAO_AGENDADOS_AMANHA.getSigla());
        if (!UteisValidacao.emptyString(tipoVinculos)) {
            for (Date dataCalcular : datas) {
                fecharMeta.setMeta(fecharMeta.getMeta() + consultarCalculoMetaQtdeLigacaoAgendadosAmanha(obj.getColaboradorResponsavel().getCodigo(), dataCalcular, obj.getEmpresaVO().getCodigo(), tipoVinculos, obj.getCodigoClientesObjecaoDefinitiva()));
            }
        } else {
            fecharMeta.setMetaCalculada(false);
        }

        fecharMeta.setAberturaMetaVO(obj);
        obj.getFecharMetaVosVenda().add(fecharMeta);
        fecharMeta = null;
    }

    public void executarCalculoMetaRenovacao(AberturaMetaVO obj, String tipoVinculos, List<Date> datas) throws Exception {
        // Regras de Negocio Renovação
        FecharMetaVO fecharMeta = new FecharMetaVO();
        fecharMeta.setDataRegistro(obj.getDia());
        fecharMeta.setIdentificadorMeta("RE");
        if (!UteisValidacao.emptyString(tipoVinculos)) {
            fecharMeta.setMeta(consultarCalculoMetaQtdeRenovacao(
                    obj.getColaboradorResponsavel().getColaboradorVO().getCodigo(), datas,
                    obj.getConfiguracaoSistemaVO().getNrDiasParaClientePreveRenovacao(), obj.getConfiguracaoSistemaVO().getNrDiasParaClientePreveRenovacaoMaiorUmMes(),
                    obj.getEmpresaVO().getCodigo(), tipoVinculos, obj.getCodigoClientesObjecaoDefinitiva(),
                    obj.getConfiguracaoSistemaVO().getNrCreditosTreinoRenovar(), obj.getConfiguracaoSistemaVO().isAutorrenovavelEntraRenovacao()));

        } else {
            fecharMeta.setMetaCalculada(false);
        }
        fecharMeta.setAberturaMetaVO(obj);
        obj.getFecharMetaVosVenda().add(fecharMeta);
        fecharMeta = null;
    }

    public void executarCalculoMetaPerda(AberturaMetaVO obj, String tiposVinculos, List<Date> datasCalcular) throws Exception {

        // Regras de Negocio Perdas
        FecharMetaVO fecharMeta = new FecharMetaVO();
        fecharMeta.setDataRegistro(obj.getDia());
        fecharMeta.setIdentificadorMeta(FasesCRMEnum.DESISTENTES.getSigla());
        if (!UteisValidacao.emptyString(tiposVinculos)) {

            //Joao Alcides : não calcular para meta aberta de forma retroativa
            if (!obj.getAberturaRetroativa()) {
                for (Date dataCalcular : datasCalcular) {
                    fecharMeta.setMeta(fecharMeta.getMeta() + consultarCalculoMetaQtdePerdasDia(obj.getColaboradorResponsavel().getColaboradorVO().getCodigo(), dataCalcular, obj.getConfiguracaoSistemaVO().getNrDiasParaClientePrevePerda(), obj.getEmpresaVO().getCodigo(), tiposVinculos, obj.getCodigoClientesObjecaoDefinitiva()));
                }
            }
        } else {
            fecharMeta.setMetaCalculada(false);
        }
        fecharMeta.setAberturaMetaVO(obj);
        obj.getFecharMetaVosRetencao().add(fecharMeta);
        fecharMeta = null;
    }

    public void executarCalculoVencidos(AberturaMetaVO obj, String tiposVinculos) throws Exception {

        // Regras de Negocio Vencidos
        FecharMetaVO fecharMeta = new FecharMetaVO();
        fecharMeta.setDataRegistro(obj.getDia());
        fecharMeta.setIdentificadorMeta("VE");
        if (!UteisValidacao.emptyString(tiposVinculos)) {
            //Joao Alcides : não calcular para meta aberta de forma retroativa
            if (!obj.getAberturaRetroativa()) {
                fecharMeta.setMeta(consultarCalculoVencidos(obj.getColaboradorResponsavel().getColaboradorVO().getCodigo(), obj.getEmpresaVO().getCodigo(), tiposVinculos, obj.getCodigoClientesObjecaoDefinitiva(),obj.getDia()));
            }
        } else {
            fecharMeta.setMetaCalculada(false);
        }
        fecharMeta.setAberturaMetaVO(obj);
        obj.getFecharMetaVosRetencao().add(fecharMeta);
        fecharMeta = null;
    }

    public void executarCalculoConversaoAgendados(AberturaMetaVO obj, Boolean calcular, List<Date> datas) throws Exception {
        if (datas.isEmpty()) {
            datas.add(obj.getDia());
        }
        Collections.sort(datas);
        Date dataInicio = datas.get(0);
        Date dataFim = datas.get(datas.size() - 1);
        // Regras de Negocio Vencidos
        FecharMetaVO fecharMeta = new FecharMetaVO();
        fecharMeta.setDataRegistro(obj.getDia());
        fecharMeta.setIdentificadorMeta(FasesCRMEnum.CONVERSAO_AGENDADOS.getSigla());
        if (calcular) {
            fecharMeta.setMeta(consultarCalculoConversaoAgendados(obj.getColaboradorResponsavel().getCodigo(),
                    //calcular datas inicial e final para verificação de agendamento
                    getFacade().getConfiguracaoSistemaCRM().obterDataCalculadaDiasUteis(dataInicio, true,
                    obj.getConfiguracaoSistemaVO().getNrDiasPosterioresAgendamento(),
                    obj.getEmpresaVO()),
                    dataFim,
                    obj.getEmpresaVO().getCodigo()));

        } else {
            fecharMeta.setMetaCalculada(false);
        }
        fecharMeta.setAberturaMetaVO(obj);
        obj.getFecharMetaVosVenda().add(fecharMeta);
        fecharMeta = null;
    }

    @SuppressWarnings("unchecked")
    public void executarCalculoMetaPosVenda(AberturaMetaVO obj, String tiposVinculos, List<Date> datas) throws Exception {
        // Regras de Negocio Pós Venda
        FecharMetaVO fecharMeta = new FecharMetaVO();
        fecharMeta.setDataRegistro(obj.getDia());
        fecharMeta.setIdentificadorMeta(FasesCRMEnum.POS_VENDA.getSigla());
        if (!UteisValidacao.emptyString(tiposVinculos) || tiposVinculos.contains(obterTipoColaboradorFasePosVenda(obj))) {
            //Joao Alcides : não calcular para meta aberta de forma retroativa
            if (!obj.getAberturaRetroativa()) {
                for (Date dataCalcular : datas) {
                    fecharMetaPosVendaValidacoes(obj, fecharMeta, obj.getConfiguracaoSistemaVO().getConfiguracaoDiasPosVendaVOs(), dataCalcular, tiposVinculos);
                }
            }
        } else {
            fecharMeta.setMetaCalculada(false);
        }
        fecharMeta.setAberturaMetaVO(obj);
        if(fecharMeta.getMetaCalculada())
            obj.getFecharMetaVosRetencao().add(fecharMeta);
        fecharMeta = null;
    }

    @SuppressWarnings("unchecked")
    public void executarCalculoMetaExAlunos(AberturaMetaVO obj, String tiposVinculos, List<Date> datas) throws Exception {
        FecharMetaVO fecharMeta = new FecharMetaVO();
        fecharMeta.setDataRegistro(obj.getDia());
        fecharMeta.setIdentificadorMeta(FasesCRMEnum.EX_ALUNOS.getSigla());
        if (!UteisValidacao.emptyString(tiposVinculos)) {
            for (Date dataCalcular : datas) {
                fecharMetaExAlunosValidacoes(obj, fecharMeta, obj.getConfiguracaoSistemaVO().getConfiguracaoDiasMetasExAlunos(), dataCalcular, tiposVinculos);
            }
        } else {
            fecharMeta.setMetaCalculada(false);
        }
        fecharMeta.setAberturaMetaVO(obj);
        obj.getFecharMetaVosVenda().add(fecharMeta);
        fecharMeta = null;
    }

    public void fecharMetaExAlunosValidacoes(AberturaMetaVO obj, FecharMetaVO metaExAluno, List<ConfiguracaoDiasMetasTO> objetos, Date dia, String tiposVinculos) throws Exception {
        for (ConfiguracaoDiasMetasTO tempoExAluno : objetos) {
            metaExAluno.setMeta(metaExAluno.getMeta()
                    + consultarCalculoMetaQtdeGenerico(
                    FasesCRMSQL.sqlExAlunos(dia, obj.getColaboradorResponsavel().getColaboradorVO().getCodigo(),
                    tempoExAluno, obj.getEmpresaVO().getCodigo(), true, tiposVinculos, obj.getCodigoClientesObjecaoDefinitiva())));
        }
    }

    public void executarCalculoMetaVisitantesAntigos(AberturaMetaVO obj, String tiposVinculos, List<Date> datas) throws Exception {
        FecharMetaVO fecharMeta = new FecharMetaVO();
        fecharMeta.setDataRegistro(obj.getDia());
        fecharMeta.setIdentificadorMeta(FasesCRMEnum.VISITANTES_ANTIGOS.getSigla());
        if (!UteisValidacao.emptyString(tiposVinculos)) {
            for (Date dataCalcular : datas) {
                fecharMetaVisitantesAntigosValidacoes(obj, fecharMeta, obj.getConfiguracaoSistemaVO().getConfiguracaoDiasMetasVisitantesAntigos(), dataCalcular, tiposVinculos);
            }
        } else {
            fecharMeta.setMetaCalculada(false);
        }
        fecharMeta.setAberturaMetaVO(obj);
        obj.getFecharMetaVosVenda().add(fecharMeta);
        fecharMeta = null;
    }

    public void fecharMetaVisitantesAntigosValidacoes(AberturaMetaVO obj, FecharMetaVO metaCRM, List<ConfiguracaoDiasMetasTO> objetos, Date dia, String tiposVinculos) throws Exception {
        for (ConfiguracaoDiasMetasTO tempoExAluno : objetos) {
            metaCRM.setMeta(metaCRM.getMeta()
                    + consultarCalculoMetaQtdeGenerico(
                    FasesCRMSQL.sqlVisitantesAntigos(dia, obj.getColaboradorResponsavel().getColaboradorVO().getCodigo(),
                    tempoExAluno, obj.getEmpresaVO().getCodigo(), true, tiposVinculos, obj.getCodigoPessoaObjecaoDefinitiva())));
        }
    }

    public void executarCalculoMetaRisco(AberturaMetaVO obj, String tipoVinculos) throws Exception {
        // Regras de Negocio Risco
        FecharMetaVO fecharMeta = new FecharMetaVO();
        fecharMeta.setDataRegistro(obj.getDia());
        fecharMeta.setIdentificadorMeta("RI");
        if (!UteisValidacao.emptyString(tipoVinculos)) {
            //Joao Alcides : não calcular para meta aberta de forma retroativa
            if (!obj.getAberturaRetroativa()) {
                fecharMeta.setMeta(fecharMeta.getMeta() + consultarCalculoMetaQtdeRisco(obj.getColaboradorResponsavel().getColaboradorVO().getCodigo(), obj.getConfiguracaoSistemaVO().getNrRisco(), obj.getDia(), obj.getEmpresaVO().getCodigo(), tipoVinculos, obj.getCodigoClientesObjecaoDefinitiva()));
            }
        } else {
            fecharMeta.setMetaCalculada(false);
        }
        fecharMeta.setAberturaMetaVO(obj);
        obj.getFecharMetaVosRetencao().add(fecharMeta);
        fecharMeta = null;
    }

    /**
     * Metodo realiza o calculo do numero de faltosos
     */
    public void executarCalculoMetaFaltosos(QuarentenaVO quarentenaCRM, AberturaMetaVO obj, String tipoVinculos, List<Date> datas) throws Exception {
        // Regras de Negocio Faltas
        FecharMetaVO fecharMeta = new FecharMetaVO();
        fecharMeta.setDataRegistro(obj.getDia());
        fecharMeta.setIdentificadorMeta("FA");
        if (!UteisValidacao.emptyString(tipoVinculos)) {
            if (!obj.getAberturaRetroativa()) {
                for (Date dataCalcular : datas) {
                    // Joao Alcides : não calcular para meta aberta de forma retroativa
                    fecharMetaFaltososValidacoes(quarentenaCRM, obj, dataCalcular, fecharMeta, tipoVinculos);
                }
            }
        } else {
            fecharMeta.setMetaCalculada(false);
        }
        fecharMeta.setAberturaMetaVO(obj);
        obj.getFecharMetaVosRetencao().add(fecharMeta);
        fecharMeta = null;
    }

    private double calcularMetasConfiguraveis(AberturaMetaVO aberturaMetaVO, ConfiguracaoSistemaCRMVO config, double realizado, double qtdMeta) throws Exception {
        double diasUteisRestantes = Uteis.contarDiasUteis(aberturaMetaVO.getDia(), Uteis.obterUltimoDiaMes(aberturaMetaVO.getDia()), config.isAbertoSabado(), config.isAbertoDomingo());
        Uteis.logarDebug("#Qtd meta:" +qtdMeta+"\t realizado: " + realizado + "\tdiasRestantes: "+diasUteisRestantes +"\t calculado: " + ((qtdMeta - realizado) / diasUteisRestantes) );
        return normalizarResultado((qtdMeta - realizado) / diasUteisRestantes);
    }

    private double normalizarResultado(double resultado) {
        if (resultado <= 0) {
            return 0.0;
        } else if (resultado < 1) {
            return 1.0;
        } else {
            return Math.ceil(resultado);
        }
    }

    public double contarIndicadosNoPeriodo(AberturaMetaVO aberturaMetaVO) throws Exception {
        Date inicioMes = Uteis.obterPrimeiroDiaMes(aberturaMetaVO.getDia());
        Date fimMes = Uteis.obterUltimoDiaMes(aberturaMetaVO.getDia());
        StringBuilder sqlstr = new StringBuilder("");
        sqlstr.append("SELECT\n");
        sqlstr.append("  count(indicado)\n");
        sqlstr.append("FROM fecharmetadetalhado fd\n");
        sqlstr.append("  INNER JOIN fecharmeta fm\n");
        sqlstr.append("    ON fd.fecharmeta = fm.codigo\n");
        sqlstr.append("  INNER JOIN aberturameta am\n");
        sqlstr.append("    ON fm.aberturameta = am.codigo\n");
        sqlstr.append("WHERE 1 = 1\n");
        sqlstr.append("      AND indicado IS NOT null\n");
        sqlstr.append("      AND (dia BETWEEN '").append(Uteis.getDataHoraJDBC(inicioMes, "00:00:00")).append("' AND '").append(Uteis.getDataHoraJDBC(fimMes, "23:59:59")).append("')\n");
        sqlstr.append("      AND colaboradorresponsavel = ").append(aberturaMetaVO.getColaboradorResponsavel().getCodigo()).append(";");
        return contar(sqlstr.toString(), con);
    }

    public double contarConversaoAgendados(AberturaMetaVO aberturaMetaVO) throws Exception {
        Date inicioMes = Uteis.obterPrimeiroDiaMes(aberturaMetaVO.getDia());
        Date fimMes = Uteis.obterUltimoDiaMes(aberturaMetaVO.getDia());
        StringBuilder sqlstr = new StringBuilder("");
        sqlstr.append("SELECT\n");
        sqlstr.append("  count(fd.codigo)\n");
        sqlstr.append("FROM fecharmetadetalhado fd\n");
        sqlstr.append("  INNER JOIN fecharmeta fm\n");
        sqlstr.append("    ON fd.fecharmeta = fm.codigo\n");
        sqlstr.append("  INNER JOIN aberturameta am\n");
        sqlstr.append("    ON fm.aberturameta = am.codigo\n");
        sqlstr.append("WHERE 1 = 1\n");
        sqlstr.append("      AND identificadormeta = 'CV'\n");
        sqlstr.append("      AND (dia BETWEEN '").append(Uteis.getDataHoraJDBC(inicioMes, "00:00:00")).append("' AND '").append(Uteis.getDataHoraJDBC(fimMes, "23:59:59")).append("')\n");
        sqlstr.append("      AND colaboradorresponsavel = ").append(aberturaMetaVO.getColaboradorResponsavel().getCodigo()).append(";");
        return contar(sqlstr.toString(), con);
    }

    public void executarCalculoMetaPadrao(AberturaMetaVO obj, boolean usarDivisao, List<String> tipos, TiposVinculosFase tiposVinculosFase, PermissaoAcessoMenuVO permissoes) throws Exception {
        ConfiguracaoSistemaCRMVO config = getFacade().getConfiguracaoSistemaCRM().consultarPorChavePrimaria(1, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        FecharMetaVO fecharMeta = new FecharMetaVO();
        // Regras de Negocio Indicação
        if (permissoes.getMetaIndicado()) {
            Uteis.logarDebug("Tem permissão para meta indicados");
            fecharMeta.setDataRegistro(obj.getDia());
            fecharMeta.setIdentificadorMeta(FasesCRMEnum.INDICACOES.getSigla());
            String tipoVinculos = verificarCalcularMeta(obj, FasesCRMEnum.INDICACOES, usarDivisao, tipos, tiposVinculosFase);
            if (!UteisValidacao.emptyString(tipoVinculos)) {
                Uteis.logarDebug("Tem tipovinculo para meta indicados");
                fecharMeta.setMeta(calcularMetasConfiguraveis(obj, config, contarIndicadosNoPeriodo(obj), config.getIndicacoesMes()));
                Uteis.logarDebug("Meta indicados calculada=" + fecharMeta.getMeta());
            } else {
                fecharMeta.setMetaCalculada(false);
            }
            fecharMeta.setAberturaMetaVO(obj);
            obj.getFecharMetaVosVenda().add(fecharMeta);
            fecharMeta = new FecharMetaVO();
        }

        if (permissoes.getMetaConversaoAgendados() != null && permissoes.getMetaConversaoAgendados()) {
            fecharMeta.setDataRegistro(obj.getDia());
            fecharMeta.setIdentificadorMeta(FasesCRMEnum.CONVERSAO_AGENDADOS.getSigla());
            String tipoVinculos = verificarCalcularMeta(obj, FasesCRMEnum.CONVERSAO_AGENDADOS, usarDivisao, tipos, tiposVinculosFase);
            if (!UteisValidacao.emptyString(tipoVinculos)) {
                Uteis.logarDebug("Tem tipovinculo para meta CONVERSAO_AGENDADOS");
                 fecharMeta.setMeta(calcularMetasConfiguraveis(obj, config, contarConversaoAgendados(obj), config.getConversaoAgendadosMes()));
            } else {
                fecharMeta.setMetaCalculada(false);
            }    
           
            fecharMeta.setAberturaMetaVO(obj);
            obj.getFecharMetaVosVenda().add(fecharMeta);
            fecharMeta = new FecharMetaVO();
        }

        if (permissoes.getMetaConversaoExAlunos() != null && permissoes.getMetaConversaoExAlunos()) {
            fecharMeta.setDataRegistro(obj.getDia());
            fecharMeta.setIdentificadorMeta(FasesCRMEnum.CONVERSAO_EX_ALUNOS.getSigla());
            String tipoVinculos = verificarCalcularMeta(obj, FasesCRMEnum.CONVERSAO_EX_ALUNOS, usarDivisao, tipos, tiposVinculosFase);
            fecharMeta.setMetaCalculada(!UteisValidacao.emptyString(tipoVinculos));
//            fecharMeta.setMeta(calcularMetasConfiguraveis(obj, config, contarConversaoAgendados(obj), config.getConversaoAgendadosMes()));
            fecharMeta.setAberturaMetaVO(obj);
            obj.getFecharMetaVosVenda().add(fecharMeta);
            fecharMeta = new FecharMetaVO();
        }

        fecharMeta.setDataRegistro(obj.getDia());
        fecharMeta.setIdentificadorMeta(FasesCRMEnum.CONVERSAO_VISITANTES_ANTIGOS.getSigla());
        String tipoVinculos = verificarCalcularMeta(obj, FasesCRMEnum.CONVERSAO_VISITANTES_ANTIGOS, usarDivisao, tipos, tiposVinculosFase);
        fecharMeta.setMetaCalculada(!UteisValidacao.emptyString(tipoVinculos));
        fecharMeta.setAberturaMetaVO(obj);
        obj.getFecharMetaVosVenda().add(fecharMeta);
        fecharMeta = new FecharMetaVO();

        fecharMeta.setDataRegistro(obj.getDia());
        fecharMeta.setIdentificadorMeta(FasesCRMEnum.CONVERSAO_INDICADOS.getSigla());
        tipoVinculos = verificarCalcularMeta(obj, FasesCRMEnum.CONVERSAO_INDICADOS, usarDivisao, tipos, tiposVinculosFase);
        fecharMeta.setMetaCalculada(!UteisValidacao.emptyString(tipoVinculos));
        fecharMeta.setAberturaMetaVO(obj);
        obj.getFecharMetaVosVenda().add(fecharMeta);
        fecharMeta = new FecharMetaVO();

        fecharMeta.setDataRegistro(obj.getDia());
        fecharMeta.setIdentificadorMeta(FasesCRMEnum.CONVERSAO_DESISTENTES.getSigla());
        tipoVinculos = verificarCalcularMeta(obj, FasesCRMEnum.CONVERSAO_DESISTENTES, usarDivisao, tipos, tiposVinculosFase);
        fecharMeta.setMetaCalculada(!UteisValidacao.emptyString(tipoVinculos));
        fecharMeta.setAberturaMetaVO(obj);
        obj.getFecharMetaVosVenda().add(fecharMeta);
        fecharMeta = new FecharMetaVO();
    }

    /**
     * Metodo responsavel pela regra de negocio do Aniversariante:
     * Aniversariantes do Sábado são mostrados na Sexta e aniversariantes do
     * Domingo são mostrados na Segunda. Se abrir o sistema no sábado ou domingo
     * mostra os aniversariantes do próprio dia.
     *
     * @param obj
     * @param fecharMeta
     * @throws Exception
     */
    public void fecharMetaAniversarianteValidacoes(AberturaMetaVO obj, FecharMetaVO fecharMeta) throws Exception {
        Date dataFeriado = Uteis.obterDataFutura2(obj.getDia(), 1);
        String tipoVinculos = "TODOS";
        if (Uteis.getDiaDaSemana(obj.getDia(), DiasDaSemana.SEGUNDA)) {
            if (!obj.getConfiguracaoSistemaVO().getAbertoDomingo()) {
                fecharMeta.setMeta(fecharMeta.getMeta() + consultarCalculoMetaQtdeClienteDataAniversariantePorMesEPorDia(Uteis.obterDataAnterior(obj.getDia(), 1), obj.getColaboradorResponsavel().getColaboradorVO().getCodigo(), obj.getEmpresaVO().getCodigo(), tipoVinculos, obj.getCodigoClientesObjecaoDefinitiva()));
            }
            if (getFacade().getFeriado().consultarPorDiaAndFeriado(dataFeriado, false, Uteis.NIVELMONTARDADOS_DADOSBASICOS)) {
                fecharMeta.setMeta(fecharMeta.getMeta() + consultarCalculoMetaQtdeClienteDataAniversariantePorMesEPorDia(dataFeriado, obj.getColaboradorResponsavel().getColaboradorVO().getCodigo(), obj.getEmpresaVO().getCodigo(), tipoVinculos, obj.getCodigoClientesObjecaoDefinitiva()));
            }
            fecharMeta.setMeta(consultarCalculoMetaQtdeClienteDataAniversariantePorMesEPorDia(obj.getDia(), obj.getColaboradorResponsavel().getColaboradorVO().getCodigo(), obj.getEmpresaVO().getCodigo(), tipoVinculos, obj.getCodigoClientesObjecaoDefinitiva()));
        } else if (Uteis.getDiaDaSemana(obj.getDia(), DiasDaSemana.TERCA) || Uteis.getDiaDaSemana(obj.getDia(), DiasDaSemana.QUARTA)) {

            if (getFacade().getFeriado().consultarPorDiaAndFeriado(dataFeriado, false, Uteis.NIVELMONTARDADOS_DADOSBASICOS)) {
                fecharMeta.setMeta(fecharMeta.getMeta() + consultarCalculoMetaQtdeClienteDataAniversariantePorMesEPorDia(obj.getDia(), dataFeriado, obj.getColaboradorResponsavel().getColaboradorVO().getCodigo(), obj.getCodigoClientesObjecaoDefinitiva()));
            } else {
                fecharMeta.setMeta(consultarCalculoMetaQtdeClienteDataAniversariantePorMesEPorDia(obj.getDia(), obj.getColaboradorResponsavel().getColaboradorVO().getCodigo(), obj.getEmpresaVO().getCodigo(), tipoVinculos, obj.getCodigoClientesObjecaoDefinitiva()));
            }
        } else if (Uteis.getDiaDaSemana(obj.getDia(), DiasDaSemana.QUINTA)) {
            if (getFacade().getFeriado().consultarPorDiaAndFeriado(dataFeriado, false, Uteis.NIVELMONTARDADOS_DADOSBASICOS)) {
                fecharMeta.setMeta(fecharMeta.getMeta() + consultarCalculoMetaQtdeClienteDataAniversariantePorMesEPorDia(obj.getDia(), dataFeriado, obj.getColaboradorResponsavel().getColaboradorVO().getCodigo(), obj.getCodigoClientesObjecaoDefinitiva()));
                if (!obj.getConfiguracaoSistemaVO().getAbertoSabado()) {
                    fecharMeta.setMeta(consultarCalculoMetaQtdeClienteDataAniversariantePorMesEPorDia(Uteis.obterDataFutura2(dataFeriado, 1), obj.getColaboradorResponsavel().getColaboradorVO().getCodigo(), obj.getEmpresaVO().getCodigo(), tipoVinculos, obj.getCodigoClientesObjecaoDefinitiva()));
                }
            } else {
                fecharMeta.setMeta(consultarCalculoMetaQtdeClienteDataAniversariantePorMesEPorDia(obj.getDia(), obj.getColaboradorResponsavel().getColaboradorVO().getCodigo(), obj.getEmpresaVO().getCodigo(), tipoVinculos, obj.getCodigoClientesObjecaoDefinitiva()));
            }

        } else if (Uteis.getDiaDaSemana(obj.getDia(), DiasDaSemana.SEXTA)) {
            if (getFacade().getFeriado().consultarPorDiaAndFeriado(dataFeriado, false, Uteis.NIVELMONTARDADOS_DADOSBASICOS)) {
                fecharMeta.setMeta(fecharMeta.getMeta() + consultarCalculoMetaQtdeClienteDataAniversariantePorMesEPorDia(obj.getDia(), dataFeriado, obj.getColaboradorResponsavel().getColaboradorVO().getCodigo(), obj.getCodigoClientesObjecaoDefinitiva()));
            } else {
                if (!obj.getConfiguracaoSistemaVO().getAbertoSabado()) {
                    fecharMeta.setMeta(fecharMeta.getMeta() + consultarCalculoMetaQtdeClienteDataAniversariantePorMesEPorDia(obj.getDia(), Uteis.obterDataFutura2(obj.getDia(), 1), obj.getColaboradorResponsavel().getColaboradorVO().getCodigo(), obj.getCodigoClientesObjecaoDefinitiva()));
                } else {
                    fecharMeta.setMeta(consultarCalculoMetaQtdeClienteDataAniversariantePorMesEPorDia(obj.getDia(), obj.getColaboradorResponsavel().getColaboradorVO().getCodigo(), obj.getEmpresaVO().getCodigo(), tipoVinculos, obj.getCodigoClientesObjecaoDefinitiva()));
                }
            }

        } else if (Uteis.getDiaDaSemana(obj.getDia(), DiasDaSemana.SABADO) && obj.getConfiguracaoSistemaVO().getAbertoSabado()) {
            fecharMeta.setMeta(consultarCalculoMetaQtdeClienteDataAniversariantePorMesEPorDia(obj.getDia(), obj.getColaboradorResponsavel().getColaboradorVO().getCodigo(), obj.getEmpresaVO().getCodigo(), tipoVinculos, obj.getCodigoClientesObjecaoDefinitiva()));

        } else if (Uteis.getDiaDaSemana(obj.getDia(), DiasDaSemana.DOMINGO) && obj.getConfiguracaoSistemaVO().getAbertoDomingo()) {
            fecharMeta.setMeta(consultarCalculoMetaQtdeClienteDataAniversariantePorMesEPorDia(obj.getDia(), obj.getColaboradorResponsavel().getColaboradorVO().getCodigo(), obj.getEmpresaVO().getCodigo(), tipoVinculos, obj.getCodigoClientesObjecaoDefinitiva()));
        }
        fecharMeta.setAberturaMetaVO(obj);

        obj.getFecharMetaVosRetencao().add(fecharMeta);
    }

    public void fecharMetaPosVendaValidacoes(AberturaMetaVO obj, FecharMetaVO fecharMeta, List<ConfiguracaoDiasPosVendaVO> objetos, Date dia, String tiposVinculos) throws Exception {
        ConfiguracaoSistemaCRMVO config = getFacade().getConfiguracaoSistemaCRM().consultarPorChavePrimaria(1, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        for (ConfiguracaoDiasPosVendaVO configuracaoDiasPosVendaVO : objetos) {
            if (configuracaoDiasPosVendaVO.isAtivo()) {
                if(configuracaoDiasPosVendaVO.getSiglaResponsavelPeloContato().equals("RPF")) {
                    if (UteisValidacao.emptyString(tiposVinculos)) {
                        TiposVinculosFase tiposVinculosFase = new TiposVinculosFase(con);
                        List<String> tipos = tiposVinculosFase.consultarTiposColaborador(obj.getColaboradorResponsavel().getColaboradorVO().getCodigo());
                        Boolean usarDivisao = tiposVinculosFase.verificarUsarDivisao();
                        tiposVinculos = verificarCalcularMeta(obj, FasesCRMEnum.POS_VENDA, usarDivisao, tipos, tiposVinculosFase);
                        if (!UteisValidacao.emptyString(tiposVinculos)) {
                            fecharMeta.setMeta(fecharMeta.getMeta() + consultarQtdeClientePosVenda(
                                    configuracaoDiasPosVendaVO.getNrDia(), obj.getColaboradorResponsavel().getColaboradorVO().getCodigo(), dia,
                                    obj.getEmpresaVO().getCodigo(), config.isIncluirContratosRenovados(), tiposVinculos, obj.getCodigoClientesObjecaoDefinitiva()));
                        }
                    }else{
                        fecharMeta.setMeta(fecharMeta.getMeta() + consultarQtdeClientePosVenda(
                                configuracaoDiasPosVendaVO.getNrDia(), obj.getColaboradorResponsavel().getColaboradorVO().getCodigo(), dia,
                                obj.getEmpresaVO().getCodigo(), config.isIncluirContratosRenovados(), tiposVinculos, obj.getCodigoClientesObjecaoDefinitiva()));
                    }
                }else if(obterTipoColaboradorFasePosVenda(obj).contains(configuracaoDiasPosVendaVO.getSiglaResponsavelPeloContato())){
                    fecharMeta.setMetaCalculada(true);
                    fecharMeta.setMeta(fecharMeta.getMeta() + consultarQtdeClientePosVenda(
                            configuracaoDiasPosVendaVO.getNrDia(), obj.getColaboradorResponsavel().getColaboradorVO().getCodigo(), dia,
                            obj.getEmpresaVO().getCodigo(), config.isIncluirContratosRenovados(), "'"+configuracaoDiasPosVendaVO.getSiglaResponsavelPeloContato()+"'", obj.getCodigoClientesObjecaoDefinitiva()));
                }
            }
        }
    }

    public void fecharMetaFaltososValidacoes(QuarentenaVO quarentenaCRM, AberturaMetaVO obj, Date dia, FecharMetaVO fecharMeta, String tipoVinculos) throws Exception {
        //obter valores de tolerancia de faltas para planos com diferentes durações
        Integer faltaMensal = obj.getConfiguracaoSistemaVO().getNrFaltaPlanoMensal();
        Integer faltaTrimestral = obj.getConfiguracaoSistemaVO().getNrFaltaPlanoTrimestral();
        Integer faltaSemestral = obj.getConfiguracaoSistemaVO().getNrFaltaPlanoAcimaSemestral();
        //verificar se o dia informado é anterior ao dia da abertura da meta
        //isso indica que é uma data auxiliar ou um feriado
        //para garantir que o cálculo represente de fato os faltosos deste dia, adicionar às tolerancias a diferença de dias
        //entre estas duas datas
        if (Calendario.menor(dia, obj.getDia())) {
            int diferenca = (int) Uteis.nrDiasEntreDatas(dia, obj.getDia());
            faltaMensal = faltaMensal + diferenca;
            faltaTrimestral = faltaTrimestral + diferenca;
            faltaSemestral = faltaSemestral + diferenca;
        } else if (Calendario.maior(dia, obj.getDia())) {
            int diferenca = (int) Uteis.nrDiasEntreDatas(dia, obj.getDia());
            faltaMensal = faltaMensal - diferenca;
            faltaTrimestral = faltaTrimestral - diferenca;
            faltaSemestral = faltaSemestral - diferenca;
        }

        QuarentenaVO ultQuarentenaEncerrada = null;
        Quarentena quarentenaDAO = null;
        try {
            quarentenaDAO = new Quarentena(con);
            ultQuarentenaEncerrada = quarentenaDAO.obterUltimaQuarentenaEncerrada(obj.getEmpresaVO().getCodigo());
        }catch (Exception ignore){}finally {
            quarentenaDAO = null;
        }

        if(ultQuarentenaEncerrada.getCodigo() != null && !ultQuarentenaEncerrada.isAtiva() && quarentenaCRM == null){
            contarClienteBuscarDataNovaPosQuarentenaAddMeta(obj, dia, fecharMeta, tipoVinculos, faltaMensal, faltaTrimestral, faltaSemestral, ultQuarentenaEncerrada);
        }else {
            fecharMeta.setMeta(fecharMeta.getMeta() + consultarCalculoMetaQtdeFaltosos(obj.getColaboradorResponsavel().getColaboradorVO().getCodigo(), dia, faltaMensal, obj.getConfiguracaoSistemaVO().getNrFaltaPlanoMensal(), 1, obj.getEmpresaVO().getCodigo(), tipoVinculos, obj.getCodigoClientesObjecaoDefinitiva(), null));
            fecharMeta.setMeta(fecharMeta.getMeta() + consultarCalculoMetaQtdeFaltosos(obj.getColaboradorResponsavel().getColaboradorVO().getCodigo(), dia, faltaTrimestral, obj.getConfiguracaoSistemaVO().getNrFaltaPlanoTrimestral(), 3, obj.getEmpresaVO().getCodigo(), tipoVinculos, obj.getCodigoClientesObjecaoDefinitiva(), null));
            fecharMeta.setMeta(fecharMeta.getMeta() + consultarCalculoMetaQtdeFaltosos(obj.getColaboradorResponsavel().getColaboradorVO().getCodigo(), dia, faltaSemestral, obj.getConfiguracaoSistemaVO().getNrFaltaPlanoAcimaSemestral(), 6, obj.getEmpresaVO().getCodigo(), tipoVinculos, obj.getCodigoClientesObjecaoDefinitiva(), null));
        }
    }

    private void contarClienteBuscarDataNovaPosQuarentenaAddMeta(AberturaMetaVO obj, Date dia, FecharMetaVO fecharMeta, String tipoVinculos, Integer faltaMensal, Integer faltaTrimestral, Integer faltaSemestral, QuarentenaVO ultQuarentenaEncerrada) throws Exception {
        //Metodo responsavel por consultar faltas do cliente em dias atuais, em seguida validar qual data deve ser lançada para que a falta real seja tratada e o aluno entre na meta.
        //Criado para periodo pos quarentena, cliente voltar a entrar na meta com a mesma quantidade de faltas que possuia antes da quarentena ser iniciada.

        ResultSet tabelaResultado;
        int qtdConfigs = 3;
        int nrFalta = 0;
        int nrDuracao = 0;
        while (qtdConfigs > 0) {
            if(qtdConfigs == 3){
                nrFalta = faltaMensal;
                nrDuracao = obj.getConfiguracaoSistemaVO().getNrFaltaPlanoMensal();
            }else if(qtdConfigs == 2){
                nrFalta = faltaTrimestral;
                nrDuracao = obj.getConfiguracaoSistemaVO().getNrFaltaPlanoTrimestral();
            }else if(qtdConfigs == 1){
                nrFalta = faltaSemestral;
                nrDuracao = obj.getConfiguracaoSistemaVO().getNrFaltaPlanoAcimaSemestral();
            }

            Date dataInicioFaltas = Uteis.obterDataAnterior(dia, nrFalta);
            String sqlQuarentenaContar = getFacade().getAberturaMeta().obterSqlCalculoMetaQtdeFaltosos(obj.getColaboradorResponsavel().getColaboradorVO().getCodigo(), dia
                    , nrFalta, nrDuracao, obj.getEmpresaVO().getCodigo(), tipoVinculos, obj.getCodigoClientesObjecaoDefinitiva(), dataInicioFaltas, null, true);
            Statement sqlConsultar = con.createStatement();
            tabelaResultado = sqlConsultar.executeQuery(sqlQuarentenaContar);


            while (tabelaResultado.next()) {
                Integer codCliente = tabelaResultado.getInt("cliente");
                dia = new RiscoService().
                        obterNovaDataPosQuarentenaEncerrada(obj.getEmpresaVO().getCodigo(), codCliente, ultQuarentenaEncerrada);
                if (Calendario.menor(dia, obj.getDia())) {
                    int diferenca = (int) Uteis.nrDiasEntreDatas(dia, obj.getDia());
                    faltaMensal = faltaMensal + diferenca;
                    faltaTrimestral = faltaTrimestral + diferenca;
                    faltaSemestral = faltaSemestral + diferenca;
                } else if (Calendario.maior(dia, obj.getDia())) {
                    int diferenca = (int) Uteis.nrDiasEntreDatas(dia, obj.getDia());
                    faltaMensal = faltaMensal - diferenca;
                    faltaTrimestral = faltaTrimestral - diferenca;
                    faltaSemestral = faltaSemestral - diferenca;
                }


                fecharMeta.setMeta(fecharMeta.getMeta() + consultarCalculoMetaQtdeFaltosos(obj.getColaboradorResponsavel().getColaboradorVO().getCodigo(), dia, faltaMensal, obj.getConfiguracaoSistemaVO().getNrFaltaPlanoMensal(), 1, obj.getEmpresaVO().getCodigo(), tipoVinculos, obj.getCodigoClientesObjecaoDefinitiva(), codCliente));
                fecharMeta.setMeta(fecharMeta.getMeta() + consultarCalculoMetaQtdeFaltosos(obj.getColaboradorResponsavel().getColaboradorVO().getCodigo(), dia, faltaTrimestral, obj.getConfiguracaoSistemaVO().getNrFaltaPlanoTrimestral(), 3, obj.getEmpresaVO().getCodigo(), tipoVinculos, obj.getCodigoClientesObjecaoDefinitiva(), codCliente));
                fecharMeta.setMeta(fecharMeta.getMeta() + consultarCalculoMetaQtdeFaltosos(obj.getColaboradorResponsavel().getColaboradorVO().getCodigo(), dia, faltaSemestral, obj.getConfiguracaoSistemaVO().getNrFaltaPlanoAcimaSemestral(), 6, obj.getEmpresaVO().getCodigo(), tipoVinculos, obj.getCodigoClientesObjecaoDefinitiva(), codCliente));
            }
            qtdConfigs--;
        }
    }

    /**
     * Rotina responsável por gravar no BD os dados editados de um novo objeto
     * da classe
     * <code>AberturaMeta</code>. Caso o objeto seja novo (ainda não gravado no
     * BD) é acionado a operação
     * <code>incluir()</code>. Caso contrário é acionado o
     * <code>alterar()</code>. Se houver alguma inconsistência o objeto não é
     * gravado, sendo re-apresentado para o usuário juntamente com uma mensagem
     * de erro.
     *
     * @throws Exception
     */
    @SuppressWarnings("unchecked")
    public void gravar(AberturaMetaVO aberturaMetaVO, Integer empresa, PermissaoAcessoMenuVO permissoes, boolean estudio, boolean commit) throws Exception {
        //Montando quarentena CRM, onde algumas metas deixam de ser abertas. Ex: Grupo de risco, Faltosos.
        QuarentenaVO quarentenaCRM = null;
        try {
            quarentenaCRM = getFacade().getQuarentena().obterAtiva(aberturaMetaVO.getEmpresaVO().getCodigo());
        }catch (Exception ignore){}

        executarDefinicaoDataParaPreenchimenteAberturaDia(quarentenaCRM, aberturaMetaVO, empresa, permissoes, estudio);

        if (commit) {
            getFacade().getAberturaMeta().incluir(aberturaMetaVO);
        } else {
            getFacade().getAberturaMeta().incluirSemCommit(aberturaMetaVO);
        }

        getFacade().getFecharMetaDetalhado().processarMetasBatidasAnteriormente(aberturaMetaVO.getFecharMetaVosVenda());
        getFacade().getFecharMetaDetalhado().processarMetasBatidasAnteriormente(aberturaMetaVO.getFecharMetaVosRetencao());
        getFacade().getFecharMetaDetalhado().processarMetasBatidasAnteriormente(aberturaMetaVO.getFecharMetaVosEstudio());


        aberturaMetaVO.setFecharMetaVosVenda(Ordenacao.ordenarLista(aberturaMetaVO.getFecharMetaVosVenda(), "ordem"));
        aberturaMetaVO.setFecharMetaVosRetencao(Ordenacao.ordenarLista(aberturaMetaVO.getFecharMetaVosRetencao(), "ordem"));
        aberturaMetaVO.setFecharMetaVosEstudio(Ordenacao.ordenarLista(aberturaMetaVO.getFecharMetaVosEstudio(), "ordem"));
    }

    public void gravarAgendamento(AberturaMetaVO aberturaMetaVO, Integer empresa, PermissaoAcessoMenuVO permissoes, boolean estudio, boolean commit) throws Exception {

        //Montando quarentena CRM, onde algumas metas deixam de ser abertas. Ex: Grupo de risco, Faltosos.
        QuarentenaVO quarentenaCRM = null;
        try {
            quarentenaCRM = getFacade().getQuarentena().obterAtiva(aberturaMetaVO.getEmpresaVO().getCodigo());
        }catch (Exception ignore){}


        List<Date> datasCalcular = obterDatasCalcular(aberturaMetaVO, true);
        List<Date> datasCalcularLigacao = obterDatasCalcular(aberturaMetaVO, false);

        TiposVinculosFase tiposVinculosFase = new TiposVinculosFase(con);
        List<String> tipos = tiposVinculosFase.consultarTiposColaborador(aberturaMetaVO.getColaboradorResponsavel().getColaboradorVO().getCodigo());
        Boolean usarDivisao = tiposVinculosFase.verificarUsarDivisao();

        for (FecharMetaVO obj : aberturaMetaVO.getFecharMetaVosVenda()) {
            if(!obj.getMetaCalculada()){
                continue;
            }
            String tipoVinculos = "";


            //agendados
            if (obj.getIdentificadorMeta().equals(FasesCRMEnum.AGENDAMENTO.getSigla())
                    && permissoes.getMetaAgendamento()) {
                tipoVinculos = verificarCalcularMeta(aberturaMetaVO, FasesCRMEnum.AGENDAMENTO, usarDivisao, tipos, tiposVinculosFase);
                if (!UteisValidacao.emptyString(tipoVinculos)) {
                    for (Date data : datasCalcularLigacao) {
                        getFacade().getFecharMetaDetalhado().consultarClientesAgendados(obj, data, aberturaMetaVO.getColaboradorResponsavel().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS, aberturaMetaVO.getEmpresaVO().getCodigo(), tipoVinculos, aberturaMetaVO.getCodigoClientesObjecaoDefinitiva());
                    }
                }
                obj.setContLigacao(FecharMeta.contarDetalhesFecharMeta(obj.getCodigo(), FecharMeta.CONTAR_LIGACOES, con));
            }

            //ligação agendados
            if (obj.getIdentificadorMeta().equals(FasesCRMEnum.LIGACAO_AGENDADOS_AMANHA.getSigla())
                    && permissoes.getMetaAgendamento()) {
                tipoVinculos = verificarCalcularMeta(aberturaMetaVO, FasesCRMEnum.LIGACAO_AGENDADOS_AMANHA, usarDivisao, tipos, tiposVinculosFase);
                if (!UteisValidacao.emptyString(tipoVinculos)) {
                    for (Date data : datasCalcularLigacao) {
                        getFacade().getFecharMetaDetalhado().consultarClientesLigacaoAgendados(obj, data, aberturaMetaVO.getColaboradorResponsavel().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS, aberturaMetaVO.getEmpresaVO().getCodigo(), tipoVinculos, aberturaMetaVO.getCodigoClientesObjecaoDefinitiva());
                    }
                }
            }

        }

        if (commit) {
            getFacade().getAberturaMeta().incluir(aberturaMetaVO);
        } else {
            getFacade().getAberturaMeta().incluirSemCommit(aberturaMetaVO);
        }

        getFacade().getFecharMetaDetalhado().processarMetasBatidasAnteriormente(aberturaMetaVO.getFecharMetaVosVenda());
        getFacade().getFecharMetaDetalhado().processarMetasBatidasAnteriormente(aberturaMetaVO.getFecharMetaVosRetencao());
        getFacade().getFecharMetaDetalhado().processarMetasBatidasAnteriormente(aberturaMetaVO.getFecharMetaVosEstudio());


        aberturaMetaVO.setFecharMetaVosVenda(Ordenacao.ordenarLista(aberturaMetaVO.getFecharMetaVosVenda(), "ordem"));
        aberturaMetaVO.setFecharMetaVosRetencao(Ordenacao.ordenarLista(aberturaMetaVO.getFecharMetaVosRetencao(), "ordem"));
        aberturaMetaVO.setFecharMetaVosEstudio(Ordenacao.ordenarLista(aberturaMetaVO.getFecharMetaVosEstudio(), "ordem"));
    }

    /**
     * Método responsavel por excluir uma FecharMetaDetalhadaVO pelo codigo de
     * FechaMetaVO
     *
     * @param aberturaMetaVO
     * @throws Exception
     * <AUTHOR>
     */
    public void executarExclusaoMetaDetalhadoParaAlteracao(AberturaMetaVO aberturaMetaVO) throws Exception {
        for (FecharMetaVO obj : aberturaMetaVO.getFecharMetaVosVenda()) {
            getFacade().getFecharMetaDetalhado().excluirFecharMetaDetalhados(obj.getCodigo().intValue());
            obj.setFecharMetaDetalhadoVOs(new ArrayList<FecharMetaDetalhadoVO>());
        }
        for (FecharMetaVO obj : aberturaMetaVO.getFecharMetaVosRetencao()) {
            getFacade().getFecharMetaDetalhado().excluirFecharMetaDetalhados(obj.getCodigo().intValue());
            obj.setFecharMetaDetalhadoVOs(new ArrayList<FecharMetaDetalhadoVO>());
        }

    }

    /**
     * Método que verifica se ja foi feita alguma abertura de Meta.
     *
     * @throws Exception
     */
    public void verificarAberturaMetaDia(Integer codigo, Date dia, Integer empresa) throws Exception {
        Long qtdeAberturaMetaDia;
        qtdeAberturaMetaDia = consultarVerificacaoAberturaMetaDia(codigo, dia, empresa);
        if (qtdeAberturaMetaDia != 0) {
            throw new Exception("A Abertura de Meta já foi cadastrada para esse usúario !");
        }
    }

    /**
     * Metodo que define qual sera o dia a ser consultado para as metas.
     *
     * @param obj
     * @throws Exception
     * <AUTHOR>
     */
    public void executarDefinicaoDataParaPreenchimenteAberturaDia(QuarentenaVO quarentenaCRM, AberturaMetaVO obj, Integer empresa, PermissaoAcessoMenuVO permissoes, boolean estudio) throws Exception {
        preencherFecharMetaDetalhadoVenda(obj, empresa, permissoes);
        preencherFecharMetaDetalhadoRetencao(quarentenaCRM, obj, permissoes);
        preencherFecharMetaDetalhadoLead(obj, empresa, permissoes);
        if (estudio) {
            preencherFecharMetaDetalhadoEstudio(obj, permissoes);
        }

    }

    /*
     * Metodo que verifica o identificador de meta e consulta o codigo do
     * cliente, passivo ou indicação.
     */
    @SuppressWarnings("unchecked")
    public void preencherFecharMetaDetalhadoVenda(AberturaMetaVO aberturaMeta, Integer empresa, PermissaoAcessoMenuVO permissoes) throws Exception {
        TiposVinculosFase tiposVinculosFase = new TiposVinculosFase(con);
        List<String> tipos = tiposVinculosFase.consultarTiposColaborador(aberturaMeta.getColaboradorResponsavel().getColaboradorVO().getCodigo());
        Boolean usarDivisao = tiposVinculosFase.verificarUsarDivisao();

        List<Date> datasCalcular = obterDatasCalcular(aberturaMeta, true);
        List<Date> datasCalcularLigacao = obterDatasCalcular(aberturaMeta, false);

        for (FecharMetaVO obj : aberturaMeta.getFecharMetaVosVenda()) {
            if(!obj.getMetaCalculada()){
                continue;
            }
            String tipoVinculos = "";
            // vinte quatro horas
            if (obj.getIdentificadorMeta().equals(FasesCRMEnum.VINTE_QUATRO_HORAS.getSigla())
                    && permissoes.getMetaVinteQuatroHoras()) {
                tipoVinculos = verificarCalcularMeta(aberturaMeta, FasesCRMEnum.VINTE_QUATRO_HORAS, usarDivisao, tipos, tiposVinculosFase);
                if (!UteisValidacao.emptyString(tipoVinculos)) {
                    for (Date data : datasCalcular) {
                        getFacade().getFecharMetaDetalhado().consultarClientesVinteQuatroHorasBuscandoCodigo(obj, data, aberturaMeta.getColaboradorResponsavel().getColaboradorVO().getCodigo(), Uteis.NIVELMONTARDADOS_FECHARMETADETALHADOANIVERSARIANTE, aberturaMeta.getEmpresaVO().getCodigo(), tipoVinculos, aberturaMeta.getCodigoClientesObjecaoDefinitiva());
                    }
                }
            }

            //Visitantes Antigos
            if (obj.getIdentificadorMeta().equals(FasesCRMEnum.VISITANTES_ANTIGOS.getSigla())
                    && permissoes.getMetaVisitantesAntigos()) {
                tipoVinculos = verificarCalcularMeta(aberturaMeta, FasesCRMEnum.VISITANTES_ANTIGOS, usarDivisao, tipos, tiposVinculosFase);
                if (!UteisValidacao.emptyString(tipoVinculos)) {
                    for (Date data : datasCalcular) {
                        List<ConfiguracaoDiasMetasTO> dias = getFacade().getConfiguracaoSistemaCRM().consultarConfiguracaoDiasMetasPorFase(FasesCRMEnum.VISITANTES_ANTIGOS);
                        for (ConfiguracaoDiasMetasTO dia : dias) {
                            getFacade().getFecharMetaDetalhado().consultarVisitantesAntigos(data, obj, dia, aberturaMeta.getColaboradorResponsavel().getColaboradorVO().getCodigo(), aberturaMeta.getEmpresaVO().getCodigo(), tipoVinculos, aberturaMeta.getCodigoPessoaObjecaoDefinitiva());
                        }
                    }
                    Integer meta = obj.getFecharMetaDetalhadoVOs().size();
                    obj.setMeta(meta.doubleValue());
                }
            }
            //visitantes recorrentes
            if (obj.getIdentificadorMeta().equals(FasesCRMEnum.VISITA_RECORRENTE.getSigla())
                    && permissoes.getMetaVisitantesAntigos()) {
                tipoVinculos = verificarCalcularMeta(aberturaMeta, FasesCRMEnum.VISITA_RECORRENTE, usarDivisao, tipos, tiposVinculosFase);
                if (!UteisValidacao.emptyString(tipoVinculos)) {
                    for (Date data : datasCalcular) {
                        getFacade().getFecharMetaDetalhado().consultarVisitantesRecorrentes(obj, data, aberturaMeta.getColaboradorResponsavel().getColaboradorVO().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS, aberturaMeta.getEmpresaVO().getCodigo(), tipoVinculos, aberturaMeta.getCodigoClientesObjecaoDefinitiva());
                    }
                }
            }

            //agendados
            if (obj.getIdentificadorMeta().equals(FasesCRMEnum.AGENDAMENTO.getSigla())
                    && permissoes.getMetaAgendamento()) {
                tipoVinculos = verificarCalcularMeta(aberturaMeta, FasesCRMEnum.AGENDAMENTO, usarDivisao, tipos, tiposVinculosFase);
                if (!UteisValidacao.emptyString(tipoVinculos)) {
                    for (Date data : datasCalcular) {
                        getFacade().getFecharMetaDetalhado().consultarClientesAgendados(obj, data, aberturaMeta.getColaboradorResponsavel().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS, aberturaMeta.getEmpresaVO().getCodigo(), tipoVinculos, aberturaMeta.getCodigoClientesObjecaoDefinitiva());
                    }
                }
                obj.setContLigacao(FecharMeta.contarDetalhesFecharMeta(obj.getCodigo(), FecharMeta.CONTAR_LIGACOES, con));
            }

            //ligação agendados
            if (obj.getIdentificadorMeta().equals(FasesCRMEnum.LIGACAO_AGENDADOS_AMANHA.getSigla())
                    && permissoes.getMetaAgendamento()) {
                tipoVinculos = verificarCalcularMeta(aberturaMeta, FasesCRMEnum.LIGACAO_AGENDADOS_AMANHA, usarDivisao, tipos, tiposVinculosFase);
                if (!UteisValidacao.emptyString(tipoVinculos)) {
                    for (Date data : datasCalcularLigacao) {
                        getFacade().getFecharMetaDetalhado().consultarClientesLigacaoAgendados(obj, data, aberturaMeta.getColaboradorResponsavel().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS, aberturaMeta.getEmpresaVO().getCodigo(), tipoVinculos, aberturaMeta.getCodigoClientesObjecaoDefinitiva());
                    }
                }
            }

            //renovação
            if (obj.getIdentificadorMeta().equals(FasesCRMEnum.RENOVACAO.getSigla())
                    && permissoes.getMetaRenovacao()) {
                tipoVinculos = verificarCalcularMeta(aberturaMeta, FasesCRMEnum.RENOVACAO, usarDivisao, tipos, tiposVinculosFase);
                Set<Integer> skipped = new HashSet<Integer>();
                if (!UteisValidacao.emptyString(tipoVinculos)) {
                    for (Date data : datasCalcular) {
                        getFacade().getFecharMetaDetalhado().consultarClientesRenovacao(obj, aberturaMeta.getColaboradorResponsavel().getColaboradorVO().getCodigo(),
                                aberturaMeta.getConfiguracaoSistemaVO().getNrDiasParaClientePreveRenovacao(), aberturaMeta.getConfiguracaoSistemaVO().getNrDiasParaClientePreveRenovacaoMaiorUmMes(),
                                data, Uteis.NIVELMONTARDADOS_FECHARMETADETALHADOANIVERSARIANTE, 
                                empresa, tipoVinculos, aberturaMeta.getCodigoClientesObjecaoDefinitiva(), 
                                aberturaMeta.getConfiguracaoSistemaVO().getNrCreditosTreinoRenovar(), skipped, aberturaMeta.getConfiguracaoSistemaVO().isAutorrenovavelEntraRenovacao());
                    }
                }
            }

            //EX-ALUNOS
            if (obj.getIdentificadorMeta().equals(FasesCRMEnum.EX_ALUNOS.getSigla())
                    && permissoes.getMetaExAlunos()) {
                tipoVinculos = verificarCalcularMeta(aberturaMeta, FasesCRMEnum.EX_ALUNOS, usarDivisao, tipos, tiposVinculosFase);
                if (!UteisValidacao.emptyString(tipoVinculos)) {
                    for (Date data : datasCalcular) {
                        List<ConfiguracaoDiasMetasTO> diasExAlunos = getFacade().getConfiguracaoSistemaCRM().consultarConfiguracaoDiasMetasPorFase(FasesCRMEnum.EX_ALUNOS);
                        for (ConfiguracaoDiasMetasTO dia : diasExAlunos) {
                            getFacade().getFecharMetaDetalhado().consultarClientesExAlunos(data, obj, dia, aberturaMeta.getColaboradorResponsavel().getColaboradorVO().getCodigo(), aberturaMeta.getEmpresaVO().getCodigo(), tipoVinculos, aberturaMeta.getCodigoClientesObjecaoDefinitiva());
                        }
                    }
                }
            }

            //FILA ESPERA TURMA CRM
            if (obj.getIdentificadorMeta().equals(FasesCRMEnum.FILA_ESPERA_TURMA_CRM.getSigla())){
                tipoVinculos = verificarCalcularMeta(aberturaMeta, FasesCRMEnum.FILA_ESPERA_TURMA_CRM, usarDivisao, tipos, tiposVinculosFase);
                if (!UteisValidacao.emptyString(tipoVinculos)) {
                    for (Date data : datasCalcular) {
                        getFacade().getFecharMetaDetalhado().consultarFilaEsperaTurmaCRM(data, obj, aberturaMeta.getColaboradorResponsavel().getColaboradorVO().getCodigo(), aberturaMeta.getEmpresaVO().getCodigo(), tipoVinculos, aberturaMeta.getCodigoClientesObjecaoDefinitiva());
                    }
                }
            }

            //ULTIMO ACESSO GYMPSS
            if (obj.getIdentificadorMeta().equals(FasesCRMEnum.ALUNO_ULTIMO_ACESSO_GYMPASS.getSigla())){
                tipoVinculos = verificarCalcularMeta(aberturaMeta, FasesCRMEnum.ALUNO_ULTIMO_ACESSO_GYMPASS, usarDivisao, tipos, tiposVinculosFase);
                if (!UteisValidacao.emptyString(tipoVinculos)) {
                    for (Date data : datasCalcular) {
                        List<ConfiguracaoDiasMetasTO> diaGymPass = getFacade().getConfiguracaoSistemaCRM().consultarConfiguracaoDiasMetasPorFase(FasesCRMEnum.ALUNO_ULTIMO_ACESSO_GYMPASS);
                        for (ConfiguracaoDiasMetasTO dia : diaGymPass) {
                            getFacade().getFecharMetaDetalhado().consultarUltimoAcessoGympass(data, obj, dia, aberturaMeta.getColaboradorResponsavel().getColaboradorVO().getCodigo(), aberturaMeta.getEmpresaVO().getCodigo(), tipoVinculos, aberturaMeta.getCodigoClientesObjecaoDefinitiva());
                        }
                    }
                }
            }
            // GYMPASS
            if (obj.getIdentificadorMeta().equals(FasesCRMEnum.ALUNO_GYMPASS.getSigla())) {
                tipoVinculos = verificarCalcularMeta(aberturaMeta, FasesCRMEnum.ALUNO_GYMPASS, usarDivisao, tipos, tiposVinculosFase);
                if (!UteisValidacao.emptyString(tipoVinculos)) {
                    for (Date data : datasCalcular) {
                        getFacade().getFecharMetaDetalhado().consultarClientesGymPassBuscandoCodigo(obj, data, aberturaMeta.getColaboradorResponsavel().getColaboradorVO().getCodigo(), Uteis.NIVELMONTARDADOS_FECHARMETADETALHADOANIVERSARIANTE, aberturaMeta.getEmpresaVO().getCodigo(), tipoVinculos, aberturaMeta.getCodigoClientesObjecaoDefinitiva());
                    }
                }
            }
        }
    }

    /*
     * Metodo que verifica o identificador de meta e consulta o codigo do
     * cliente, passivo ou indicação.
     */
    public void preencherFecharMetaDetalhadoRetencao(QuarentenaVO quarentenaCRM, AberturaMetaVO aberturaMeta, PermissaoAcessoMenuVO permissoes) throws Exception {
        TiposVinculosFase tiposVinculosFase = new TiposVinculosFase(con);
        List<String> tipos = tiposVinculosFase.consultarTiposColaborador(aberturaMeta.getColaboradorResponsavel().getColaboradorVO().getCodigo());
        Boolean usarDivisao = tiposVinculosFase.verificarUsarDivisao();

        List<Date> datasCalcular = obterDatasCalcular(aberturaMeta, true);
        List<Date> datasCalcularAniversariantes = obterDatasCalcular(aberturaMeta, false);

        for (FecharMetaVO obj : aberturaMeta.getFecharMetaVosRetencao()) {
            if(!obj.getMetaCalculada()){
                if(!obj.getIdentificadorMeta().equals("PV"))
                continue;
            }
            String tipoVinculos = "";
            //pos venda
            if (obj.getIdentificadorMeta().equals("PV") && !aberturaMeta.getAberturaRetroativa()
                    && permissoes.getMetaPosVenda()) {
                tipoVinculos = verificarCalcularMeta(aberturaMeta, FasesCRMEnum.POS_VENDA, usarDivisao, tipos, tiposVinculosFase);
                if (!UteisValidacao.emptyString(tipoVinculos) || !UteisValidacao.emptyString(obterTipoColaboradorFasePosVenda(aberturaMeta))) {
                    for (Date data : datasCalcular) {
                        preencherMetaDetalhadoComCodigoClientePosVenda(aberturaMeta, obj, aberturaMeta.getConfiguracaoSistemaVO().getConfiguracaoDiasPosVendaVOs(), data, tipoVinculos);
                    }
                }
            }
            //aniversariantes
            if (obj.getIdentificadorMeta().equals(FasesCRMEnum.ANIVERSARIANTES.getSigla())
                    && permissoes.getMetaAniversariante()) {
                tipoVinculos = verificarCalcularMeta(aberturaMeta, FasesCRMEnum.ANIVERSARIANTES, usarDivisao, tipos, tiposVinculosFase);
                if (!UteisValidacao.emptyString(tipoVinculos)) {
                    for (Date data : datasCalcularAniversariantes) {
                        getFacade().getFecharMetaDetalhado().consultarClientesAniversariantesBuscandoCodigo(obj, data, aberturaMeta.getColaboradorResponsavel().getColaboradorVO().getCodigo(), Uteis.NIVELMONTARDADOS_FECHARMETADETALHADOANIVERSARIANTE, aberturaMeta.getEmpresaVO().getCodigo(), tipoVinculos, aberturaMeta.getCodigoClientesObjecaoDefinitiva());
                    }
                }
            }

            //perdas
            if (obj.getIdentificadorMeta().equals(FasesCRMEnum.DESISTENTES.getSigla()) && !aberturaMeta.getAberturaRetroativa()
                    && permissoes.getMetaPerda()) {
                tipoVinculos = verificarCalcularMeta(aberturaMeta, FasesCRMEnum.DESISTENTES, usarDivisao, tipos, tiposVinculosFase);
                if (!UteisValidacao.emptyString(tipoVinculos)) {
                    for (Date data : datasCalcular) {
                        getFacade().getFecharMetaDetalhado().consultarPerdasClientes(obj, data, aberturaMeta.getConfiguracaoSistemaVO().getNrDiasParaClientePrevePerda(), aberturaMeta.getColaboradorResponsavel().getColaboradorVO().getCodigo(), Uteis.NIVELMONTARDADOS_FECHARMETADETALHADOANIVERSARIANTE, aberturaMeta.getEmpresaVO().getCodigo(), tipoVinculos, aberturaMeta.getCodigoClientesObjecaoDefinitiva());
                    }
                }
            }

            //risco
            if (obj.getIdentificadorMeta().equals(FasesCRMEnum.GRUPO_RISCO.getSigla()) && !aberturaMeta.getAberturaRetroativa()
                    && permissoes.getMetaGrupoRisco() && !(quarentenaCRM.getCodigo() != null && quarentenaCRM.isAtiva())) {
                tipoVinculos = verificarCalcularMeta(aberturaMeta, FasesCRMEnum.GRUPO_RISCO, usarDivisao, tipos, tiposVinculosFase);
                if (!UteisValidacao.emptyString(tipoVinculos)) {
                    getFacade().getFecharMetaDetalhado().consultarRiscoClientes(obj, aberturaMeta.getColaboradorResponsavel().getColaboradorVO().getCodigo(),
                            aberturaMeta.getConfiguracaoSistemaVO().getNrRisco(), aberturaMeta.getDia(),
                            Uteis.NIVELMONTARDADOS_FECHARMETADETALHADOANIVERSARIANTE, aberturaMeta.getEmpresaVO().getCodigo(), tipoVinculos, aberturaMeta.getCodigoClientesObjecaoDefinitiva());
                }
            }

            //faltosos
            if (obj.getIdentificadorMeta().equals(FasesCRMEnum.FALTOSOS.getSigla()) && !aberturaMeta.getAberturaRetroativa()
                    && permissoes.getMetaFaltosos() && !(quarentenaCRM.getCodigo() != null && quarentenaCRM.isAtiva())) {
                tipoVinculos = verificarCalcularMeta(aberturaMeta, FasesCRMEnum.FALTOSOS, usarDivisao, tipos, tiposVinculosFase);
                if (!UteisValidacao.emptyString(tipoVinculos)) {
                    for (Date data : datasCalcular) {
                        preencherMetaDetalhadoComCodigoClienteFaltosos(quarentenaCRM, aberturaMeta, data, obj, tipoVinculos);
                    }
                }
                Integer metaReal = obj.getFecharMetaDetalhadoVOs().size();
                obj.setMeta(metaReal.doubleValue());
            }
            //vencidos
            if (obj.getIdentificadorMeta().equals(FasesCRMEnum.VENCIDOS.getSigla()) && !aberturaMeta.getAberturaRetroativa()
                    && permissoes.getMetaVencidos()) {
                tipoVinculos = verificarCalcularMeta(aberturaMeta, FasesCRMEnum.VENCIDOS, usarDivisao, tipos, tiposVinculosFase);
                if (!UteisValidacao.emptyString(tipoVinculos)) {
                    getFacade().getFecharMetaDetalhado().consultarVencidos(obj, aberturaMeta.getColaboradorResponsavel().getColaboradorVO().getCodigo(),
                            Uteis.NIVELMONTARDADOS_FECHARMETADETALHADOANIVERSARIANTE, aberturaMeta.getEmpresaVO().getCodigo(), tipoVinculos, aberturaMeta.getCodigoClientesObjecaoDefinitiva());
                }
            }

        }
    }

    public void preencherMetaDetalhadoComCodigoClientePosVenda(AberturaMetaVO obj, FecharMetaVO fecharMeta, List<ConfiguracaoDiasPosVendaVO> objetos, Date dia, String tipoVinculos) throws Exception {
        ConfiguracaoSistemaCRMVO config = getFacade().getConfiguracaoSistemaCRM().consultarPorChavePrimaria(1, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        for (ConfiguracaoDiasPosVendaVO configuracaoDiasPosVendaVO : objetos) {
            if (configuracaoDiasPosVendaVO.isAtivo()) {
                if(configuracaoDiasPosVendaVO.getSiglaResponsavelPeloContato().equals("RPF")) {
                    if(UteisValidacao.emptyString(tipoVinculos)){
                        TiposVinculosFase tiposVinculosFase = new TiposVinculosFase(con);
                        List<String> tipos = tiposVinculosFase.consultarTiposColaborador(obj.getColaboradorResponsavel().getColaboradorVO().getCodigo());
                        Boolean usarDivisao = tiposVinculosFase.verificarUsarDivisao();
                        tipoVinculos = verificarCalcularMeta(obj, FasesCRMEnum.POS_VENDA, usarDivisao, tipos, tiposVinculosFase);
                        if(!UteisValidacao.emptyString(tipoVinculos)){
                            getFacade().getFecharMetaDetalhado().consultarClientesPosVendaBuscandoCodigoPassandoNrDeDias(configuracaoDiasPosVendaVO.getNrDia(), dia,
                                    fecharMeta, configuracaoDiasPosVendaVO.getCodigo(), obj.getColaboradorResponsavel().getColaboradorVO().getCodigo(),
                                    Uteis.NIVELMONTARDADOS_FECHARMETADETALHADOANIVERSARIANTE, obj.getEmpresaVO().getCodigo(), config.isIncluirContratosRenovados(), tipoVinculos, obj.getCodigoClientesObjecaoDefinitiva());
                        }
                    }else{
                        getFacade().getFecharMetaDetalhado().consultarClientesPosVendaBuscandoCodigoPassandoNrDeDias(configuracaoDiasPosVendaVO.getNrDia(), dia,
                                fecharMeta, configuracaoDiasPosVendaVO.getCodigo(), obj.getColaboradorResponsavel().getColaboradorVO().getCodigo(),
                                Uteis.NIVELMONTARDADOS_FECHARMETADETALHADOANIVERSARIANTE, obj.getEmpresaVO().getCodigo(), config.isIncluirContratosRenovados(), tipoVinculos, obj.getCodigoClientesObjecaoDefinitiva());
                    }
                }else if(obterTipoColaboradorFasePosVenda(obj).contains(configuracaoDiasPosVendaVO.getSiglaResponsavelPeloContato())){
                    fecharMeta.setMetaCalculada(true);
                    getFacade().getFecharMetaDetalhado().consultarClientesPosVendaBuscandoCodigoPassandoNrDeDias(configuracaoDiasPosVendaVO.getNrDia(), dia,
                            fecharMeta, configuracaoDiasPosVendaVO.getCodigo(), obj.getColaboradorResponsavel().getColaboradorVO().getCodigo(),
                            Uteis.NIVELMONTARDADOS_FECHARMETADETALHADOANIVERSARIANTE, obj.getEmpresaVO().getCodigo(), config.isIncluirContratosRenovados(), "'"+configuracaoDiasPosVendaVO.getSiglaResponsavelPeloContato()+"'", obj.getCodigoClientesObjecaoDefinitiva());
                }
            }
        }
    }

    private String obterTipoColaboradorFasePosVenda(AberturaMetaVO obj) {
        String tipos = "";
        for(TipoColaboradorVO tipo : obj.getColaboradorResponsavel().getColaboradorVO().getListaTipoColaboradorVOs()){
            if(tipos.isEmpty()){
                    tipos = tipo.getDescricao();
            }else{
                tipos += ","+tipo.getDescricao();
            }
        }
        return tipos;
    }

    public void preencherMetaDetalhadoComCodigoClienteFaltosos(QuarentenaVO quarentenaCRM, AberturaMetaVO obj, Date dia, FecharMetaVO fecharMeta, String tipoVinculos) throws Exception {
        //Joao Alcides : obter valores de tolerancia de faltas para planos com diferentes durações
        Integer faltaMensal = obj.getConfiguracaoSistemaVO().getNrFaltaPlanoMensal();
        Integer faltaTrimestral = obj.getConfiguracaoSistemaVO().getNrFaltaPlanoTrimestral();
        Integer faltaSemestral = obj.getConfiguracaoSistemaVO().getNrFaltaPlanoAcimaSemestral();
        //Joao Alcides : verificar se o dia informado é anterior ao dia da abertura da meta
        //isso indica que é uma data auxiliar ou um feriado
        //para garantir que o cálculo represente de fato os faltosos deste dia, adicionar às tolerancias a diferença de dias
        //entre estas duas datas
        if (Calendario.menor(dia, obj.getDia())) {
            int diferenca = (int) Uteis.nrDiasEntreDatas(dia, obj.getDia());
            faltaMensal = faltaMensal + diferenca;
            faltaTrimestral = faltaTrimestral + diferenca;
            faltaSemestral = faltaSemestral + diferenca;
        } else {
            if (Calendario.maior(dia, obj.getDia())) {
                int diferenca = (int) Uteis.nrDiasEntreDatas(dia, obj.getDia());
                faltaMensal = faltaMensal - diferenca;
                faltaTrimestral = faltaTrimestral - diferenca;
                faltaSemestral = faltaSemestral - diferenca;
            }
        }

        QuarentenaVO ultQuarentenaEncerrada = null;
        Quarentena quarentenaDAO = null;
        try {
            quarentenaDAO = new Quarentena(con);
            ultQuarentenaEncerrada = quarentenaDAO.obterUltimaQuarentenaEncerrada(obj.getEmpresaVO().getCodigo());
        }catch (Exception ignore){}finally {
            quarentenaDAO = null;
        }

        if(ultQuarentenaEncerrada.getCodigo() != null && !ultQuarentenaEncerrada.isAtiva() && quarentenaCRM == null){
            consultarClienteBuscarDataNovaPosQuarentenaAddMeta(obj, dia, fecharMeta, tipoVinculos, faltaMensal, faltaTrimestral, faltaSemestral, ultQuarentenaEncerrada);
        }else {
            getFacade().getFecharMetaDetalhado().consultarCalculoMetaQtdeFaltasos(fecharMeta, dia, obj.getColaboradorResponsavel().getColaboradorVO().getCodigo(), faltaMensal, obj.getConfiguracaoSistemaVO().getNrFaltaPlanoMensal(), 1, Uteis.NIVELMONTARDADOS_FECHARMETADETALHADOANIVERSARIANTE, obj.getEmpresaVO().getCodigo(), tipoVinculos, obj.getCodigoClientesObjecaoDefinitiva(), null);
            getFacade().getFecharMetaDetalhado().consultarCalculoMetaQtdeFaltasos(fecharMeta, dia, obj.getColaboradorResponsavel().getColaboradorVO().getCodigo(), faltaTrimestral, obj.getConfiguracaoSistemaVO().getNrFaltaPlanoTrimestral(), 3, Uteis.NIVELMONTARDADOS_FECHARMETADETALHADOANIVERSARIANTE, obj.getEmpresaVO().getCodigo(), tipoVinculos, obj.getCodigoClientesObjecaoDefinitiva(), null);
            getFacade().getFecharMetaDetalhado().consultarCalculoMetaQtdeFaltasos(fecharMeta, dia, obj.getColaboradorResponsavel().getColaboradorVO().getCodigo(), faltaSemestral, obj.getConfiguracaoSistemaVO().getNrFaltaPlanoAcimaSemestral(), 6, Uteis.NIVELMONTARDADOS_FECHARMETADETALHADOANIVERSARIANTE, obj.getEmpresaVO().getCodigo(), tipoVinculos, obj.getCodigoClientesObjecaoDefinitiva(), null);
        }
    }

    private void consultarClienteBuscarDataNovaPosQuarentenaAddMeta(AberturaMetaVO obj, Date dia, FecharMetaVO fecharMeta, String tipoVinculos, Integer faltaMensal, Integer faltaTrimestral, Integer faltaSemestral, QuarentenaVO ultQuarentenaEncerrada) throws Exception {
        //Metodo responsavel por consultar faltas do cliente em dias atuais, em seguida validar qual data deve ser lançada para que a falta real seja tratada e o aluno entre na meta.
        //Criado para periodo pos quarentena, cliente voltar a entrar na meta com a mesma quantidade de faltas que possuia antes da quarentena ser iniciada.

        ResultSet tabelaResultado;
        int qtdConfigs = 3;
        int nrFalta = 0;
        int nrDuracao = 0;
        while (qtdConfigs > 0) {
            if(qtdConfigs == 3){
                nrFalta = faltaMensal;
                nrDuracao = obj.getConfiguracaoSistemaVO().getNrFaltaPlanoMensal();
            }else if(qtdConfigs == 2){
                nrFalta = faltaTrimestral;
                nrDuracao = obj.getConfiguracaoSistemaVO().getNrFaltaPlanoTrimestral();
            }else if(qtdConfigs == 1){
                nrFalta = faltaSemestral;
                nrDuracao = obj.getConfiguracaoSistemaVO().getNrFaltaPlanoAcimaSemestral();
            }

            Date dataInicioFaltas = Uteis.obterDataAnterior(dia, nrFalta);
            String sqlQuarentena = getFacade().getFecharMetaDetalhado().obterSqlCalculoMetaFaltosos(dia, obj.getColaboradorResponsavel().getColaboradorVO().getCodigo()
                    , nrFalta, nrDuracao, obj.getEmpresaVO().getCodigo(), tipoVinculos, obj.getCodigoClientesObjecaoDefinitiva(), dataInicioFaltas, null);
            Statement sqlConsultar = con.createStatement();
            tabelaResultado = sqlConsultar.executeQuery(sqlQuarentena);


            while (tabelaResultado.next()) {
                Integer codCliente = tabelaResultado.getInt("cliente");
                dia = new RiscoService().
                        obterNovaDataPosQuarentenaEncerrada(obj.getEmpresaVO().getCodigo(), codCliente, ultQuarentenaEncerrada);
                if (Calendario.menor(dia, obj.getDia())) {
                    int diferenca = (int) Uteis.nrDiasEntreDatas(dia, obj.getDia());
                    faltaMensal = faltaMensal + diferenca;
                    faltaTrimestral = faltaTrimestral + diferenca;
                    faltaSemestral = faltaSemestral + diferenca;
                } else {
                    if (Calendario.maior(dia, obj.getDia())) {
                        int diferenca = (int) Uteis.nrDiasEntreDatas(dia, obj.getDia());
                        faltaMensal = faltaMensal - diferenca;
                        faltaTrimestral = faltaTrimestral - diferenca;
                        faltaSemestral = faltaSemestral - diferenca;
                    }
                }

                getFacade().getFecharMetaDetalhado().consultarCalculoMetaQtdeFaltasos(fecharMeta, dia, obj.getColaboradorResponsavel().getColaboradorVO().getCodigo(), faltaMensal, obj.getConfiguracaoSistemaVO().getNrFaltaPlanoMensal(), 1, Uteis.NIVELMONTARDADOS_FECHARMETADETALHADOANIVERSARIANTE, obj.getEmpresaVO().getCodigo(), tipoVinculos, obj.getCodigoClientesObjecaoDefinitiva(), codCliente);
                getFacade().getFecharMetaDetalhado().consultarCalculoMetaQtdeFaltasos(fecharMeta, dia, obj.getColaboradorResponsavel().getColaboradorVO().getCodigo(), faltaTrimestral, obj.getConfiguracaoSistemaVO().getNrFaltaPlanoTrimestral(), 3, Uteis.NIVELMONTARDADOS_FECHARMETADETALHADOANIVERSARIANTE, obj.getEmpresaVO().getCodigo(), tipoVinculos, obj.getCodigoClientesObjecaoDefinitiva(), codCliente);
                getFacade().getFecharMetaDetalhado().consultarCalculoMetaQtdeFaltasos(fecharMeta, dia, obj.getColaboradorResponsavel().getColaboradorVO().getCodigo(), faltaSemestral, obj.getConfiguracaoSistemaVO().getNrFaltaPlanoAcimaSemestral(), 6, Uteis.NIVELMONTARDADOS_FECHARMETADETALHADOANIVERSARIANTE, obj.getEmpresaVO().getCodigo(), tipoVinculos, obj.getCodigoClientesObjecaoDefinitiva(), codCliente);

            }
            qtdConfigs--;
        }
    }

    /**
     * Método responsavel por setar a AberturaMeta no obj FecharMetaVO, caso o
     * usuario não tenha permissão no perfilAcesso.
     *
     * <AUTHOR>
     */
    public void executarInclusaoAberturaMetaFecharMeta(AberturaMetaVO obj) {
        for (FecharMetaVO fecharMetaVenda : obj.getFecharMetaVosVenda()) {
            fecharMetaVenda.setAberturaMetaVO(obj);
        }
        for (FecharMetaVO fecharMetaRetencao : obj.getFecharMetaVosRetencao()) {
            fecharMetaRetencao.setAberturaMetaVO(obj);
        }
    }

    @SuppressWarnings("unchecked")
    public void consultarPerfilAcessoColaborador(AberturaMetaVO obj, EmpresaVO empresa) throws Exception {
        PerfilAcessoVO perfil = new PerfilAcessoVO();
        Iterator i = obj.getColaboradorResponsavel().getUsuarioPerfilAcessoVOs().iterator();
        while (i.hasNext()) {
            UsuarioPerfilAcessoVO usuarioPerfilAcessoVO = (UsuarioPerfilAcessoVO) i.next();
            if (usuarioPerfilAcessoVO.getEmpresa().getCodigo().equals(empresa.getCodigo())) {
                perfil = getFacade().getPerfilAcesso().consultarPorChavePrimaria(usuarioPerfilAcessoVO.getPerfilAcesso().getCodigo(), Uteis.NIVELMONTARDADOS_TODOS);
                break;
            }
        }
        obj.getColaboradorResponsavel().setPermissaoAcessoMenuVO(obj.getColaboradorResponsavel().getPermissaoAcessoMenuVO().montarPermissoesMenu(perfil.getPermissaoVOs(), obj.getColaboradorResponsavel(), perfil.isUnificado()));
    }

    /**
     * Método responsavel por validar se o Usuario logado ja fez a Abertura da
     * meta, se tiver feito consulta a abertura e seta dentro de abertura.
     * Também inicializa a lista de Grupo Colaborador
     *
     * @param usuarioLogado
     * @param abertura
     * @return
     * @throws Exception
     * <AUTHOR>
     */
    @SuppressWarnings("unchecked")
    public AberturaMetaVO validarUsuarioLogadoParaAberturaMeta(UsuarioVO usuarioLogado, AberturaMetaVO abertura, Integer empresa, Boolean gravou) throws Exception {
        // valido se o usuario logado é um colaborador e tem Abertura para hoje
        if (!gravou && usuarioLogado != null && usuarioLogado.getColaboradorVO().getCodigo() != 0
                && getFacade().getAberturaMeta().consultarAberturaPorCodigoUsuario(usuarioLogado.getCodigo(), empresa, Calendario.hoje())) {
            abertura = executarValidacaoAberturaMetaUsuarioParaEleMesmo(usuarioLogado, abertura, empresa);
        }

        //Verificando se o usuario logado tem a permissao de poder visualizar todas as carteiras
        if (verificarPermitirVisualizarTodasCarteiras()) {
            //Realizando a consulta para obter todos os grupos e passando para o metodo que ira
            //realizar a inicializacao dos grupos
            inicializarDadosListaGrupoColaborador(getFacade().getGrupoColaborador().consultarGrupoColaboradorComParticipantes(false, Uteis.NIVELMONTARDADOS_TELACONSULTA, empresa), abertura);
        } else {
            inicializarDadosListaGrupoColaborador(getFacade().getGrupoColaborador().consultarGrupoColaboradorComParticipantesPorCodigoColaboradorPorTipoVisao(usuarioLogado.getColaboradorVO().getCodigo(), "VI", false, Uteis.NIVELMONTARDADOS_TELACONSULTA, empresa), abertura);
            inicializarDadosListaGrupoColaborador(getFacade().getGrupoColaborador().consultarGrupoColaboradorComParticipantesPorCodigoColaboradorPorTipoVisao(usuarioLogado.getColaboradorVO().getCodigo(), "IG", false, Uteis.NIVELMONTARDADOS_TELACONSULTA, empresa), abertura);
            inicializarDadosListaGrupoColaborador(getFacade().getGrupoColaborador().consultarPorResponsavelGrupo(usuarioLogado.getNome(), false, Uteis.NIVELMONTARDADOS_TELACONSULTA, empresa), abertura);

        }

        if (getFacade().getAberturaMeta().consultarAberturaPorCodigoUsuario(usuarioLogado.getCodigo().intValue(), empresa, negocio.comuns.utilitarias.Calendario.hoje())) {
            inicializarDadosApresentarGrupoColaboradorComAbertura(abertura);
        } else {
            inicializarDadosApresentarGrupoColaboradorSemAbertura(abertura);
        }
        return abertura;
    }

    @SuppressWarnings("unchecked")
    public AberturaMetaVO executarValidacaoAberturaMetaUsuarioParaEleMesmo(UsuarioVO usuarioLogadovO, AberturaMetaVO aberturaVO, Integer empresa) throws Exception {
        aberturaVO = getFacade().getAberturaMeta().consultarAberturaPorCodigoUsuario(usuarioLogadovO.getCodigo().intValue(), negocio.comuns.utilitarias.Calendario.hoje(), empresa, Uteis.NIVELMONTARDADOS_TODOS);
        aberturaVO.setExisteMetaParaParticipante(true);
        aberturaVO.setColaboradorResponsavel(usuarioLogadovO);
        aberturaVO.setFecharMetaVosVenda(Ordenacao.ordenarLista(aberturaVO.getFecharMetaVosVenda(), "ordem"));
        aberturaVO.setFecharMetaVosRetencao(Ordenacao.ordenarLista(aberturaVO.getFecharMetaVosRetencao(), "ordem"));
        return aberturaVO;

    }

    @SuppressWarnings("unchecked")
    public AberturaMetaVO inicializarDadosAberturaMetaParaComecarTrabalhar(UsuarioVO usuarioLogado, AberturaMetaVO abertura, Integer empresa) throws Exception {
        abertura = getFacade().getAberturaMeta().consultarPorChavePrimaria(abertura.getCodigo(), Uteis.NIVELMONTARDADOS_TODOS);
        abertura.setExisteMetaParaParticipante(true);
        abertura.setFecharMetaVosVenda(Ordenacao.ordenarLista(abertura.getFecharMetaVosVenda(), "ordem"));
        abertura.setFecharMetaVosRetencao(Ordenacao.ordenarLista(abertura.getFecharMetaVosRetencao(), "ordem"));

        //Verificando se o usuario logado tem a permissao de poder visualizar todas as carteiras
        if (verificarPermitirVisualizarTodasCarteiras()) {
            //Realizando a consulta para obter todos os grupos e passando para o metodo que ira
            //realizar a inicializacao dos grupos
            inicializarDadosListaGrupoColaborador(getFacade().getGrupoColaborador().consultarGrupoColaboradorComParticipantes(false, Uteis.NIVELMONTARDADOS_TELACONSULTA, empresa), abertura);
        } else {
            inicializarDadosListaGrupoColaborador(getFacade().getGrupoColaborador().consultarGrupoColaboradorComParticipantesPorCodigoColaboradorPorTipoVisao(usuarioLogado.getColaboradorVO().getCodigo(), "VI", false, Uteis.NIVELMONTARDADOS_TELACONSULTA, empresa), abertura);
            inicializarDadosListaGrupoColaborador(getFacade().getGrupoColaborador().consultarGrupoColaboradorComParticipantesPorCodigoColaboradorPorTipoVisao(usuarioLogado.getColaboradorVO().getCodigo(), "IG", false, Uteis.NIVELMONTARDADOS_TELACONSULTA, empresa), abertura);
            inicializarDadosListaGrupoColaborador(getFacade().getGrupoColaborador().consultarPorResponsavelGrupo(usuarioLogado.getNome(), false, Uteis.NIVELMONTARDADOS_TELACONSULTA, empresa), abertura);

        }

//    	inicializarDadosListaGrupoColaborador(getFacade().getGrupoColaborador().consultarGrupoColaboradorComParticipantesPorCodigoColaboradorPorTipoVisao(abertura.getColaboradorResponsavel().getColaboradorVO().getCodigo(), "VI", false, Uteis.NIVELMONTARDADOS_TELACONSULTA), abertura);
//    	inicializarDadosListaGrupoColaborador(getFacade().getGrupoColaborador().consultarPorResponsavelGrupo(abertura.getColaboradorResponsavel().getNome(), false, Uteis.NIVELMONTARDADOS_TELACONSULTA), abertura);
//    	inicializarDadosListaGrupoColaborador(getFacade().getGrupoColaborador().consultarGrupoColaboradorComParticipantesPorCodigoColaboradorPorTipoVisao(abertura.getColaboradorResponsavel().getColaboradorVO().getCodigo(), "IG", false, Uteis.NIVELMONTARDADOS_TELACONSULTA), abertura);

        inicializarDadosApresentarGrupoColaboradorComAbertura(abertura);

        return abertura;

    }

    /**
     * Método Responsavel por consultar o Grupo Colaborador e preencher a lista
     * de GrupoColaborador que esta dentro de AberturaMeta.
     *
     * @param abertura
     * @throws Exception
     * <AUTHOR>
     */
    public void inicializarDadosApresentarGrupoColaboradorComAbertura(AberturaMetaVO abertura) throws Exception {
        boolean naoExisteParticipante = false;
        List<ColaboradorIndisponivelCrmVO> listaColaboradorIndisponivel = verificarColaboradorResponsavelSuplente(abertura);
        outer:
        for (GrupoColaboradorVO grupo : abertura.getGrupoColaboradorListaVenda()) {
            if (getFacade().getAberturaMeta().verificarPermitirVisualizarTodasCarteiras()) {
                grupo.setGrupoColaboradorParticipanteVOs(getFacade().getGrupoColaboradorParticipante().consultarGrupoColaboradorParticipantesPorCodigoETipoVisaoDiferenteMontandoUsuario(grupo.getCodigo(), "", Uteis.NIVELMONTARDADOS_ABERTURAMETA));
            } else {
                grupo.setGrupoColaboradorParticipanteVOs(getFacade().getGrupoColaboradorParticipante().consultarGrupoColaboradorParticipantesPorCodigoETipoVisaoDiferenteMontandoUsuario(grupo.getCodigo(), "VI", Uteis.NIVELMONTARDADOS_ABERTURAMETA));
            }

            if (!listaColaboradorIndisponivel.isEmpty()){
                agruparMetasColaboradorIndisponivelParaColaboradorSuplente(listaColaboradorIndisponivel,grupo, abertura);
            }

            for (GrupoColaboradorParticipanteVO parti : grupo.getGrupoColaboradorParticipanteVOs()) {
                if (parti.getUsuarioParticipante().getCodigo().equals(abertura.getColaboradorResponsavel().getCodigo())) {
                    parti.setGrupoColaboradorParticipanteEscolhido(true);
                    abertura.setNomeGrupoColaboradorVenda(grupo.getDescricao());
                    break outer;
                } else {
                    naoExisteParticipante = true;
                }
            }
            if (naoExisteParticipante) {
                grupo.setGrupoColaboradorParticipanteVOs(new ArrayList<GrupoColaboradorParticipanteVO>());
            }
        }



        for (GrupoColaboradorVO grupo : abertura.getGrupoColaboradorListaRetencao()) {
            grupo.setGrupoColaboradorParticipanteVOs(getFacade().getGrupoColaboradorParticipante().consultarGrupoColaboradorParticipantesPorCodigoETipoVisaoDiferenteMontandoUsuario(grupo.getCodigo(), "VI", Uteis.NIVELMONTARDADOS_ABERTURAMETA));

            if (!listaColaboradorIndisponivel.isEmpty()){
                agruparMetasColaboradorIndisponivelParaColaboradorSuplente(listaColaboradorIndisponivel,grupo, abertura);
            }

            for (GrupoColaboradorParticipanteVO parti : grupo.getGrupoColaboradorParticipanteVOs()) {
                if (parti.getUsuarioParticipante().getCodigo().equals(abertura.getColaboradorResponsavel().getCodigo())) {
                    parti.setGrupoColaboradorParticipanteEscolhido(true);
                    abertura.setNomeGrupoColaboradorRetencao(grupo.getDescricao());
                    return;
                } else {
                    naoExisteParticipante = true;
                }
            }
            if (naoExisteParticipante) {
                grupo.setGrupoColaboradorParticipanteVOs(new ArrayList<GrupoColaboradorParticipanteVO>());
            }
        }
        
        for (GrupoColaboradorVO grupo : abertura.getGrupoColaboradorListaEstudio()) {
            grupo.setGrupoColaboradorParticipanteVOs(getFacade().getGrupoColaboradorParticipante().consultarGrupoColaboradorParticipantesPorCodigoETipoVisaoDiferenteMontandoUsuario(grupo.getCodigo(), "VI", Uteis.NIVELMONTARDADOS_ABERTURAMETA));

            if (!listaColaboradorIndisponivel.isEmpty()){
                agruparMetasColaboradorIndisponivelParaColaboradorSuplente(listaColaboradorIndisponivel,grupo, abertura);
            }

            for (GrupoColaboradorParticipanteVO parti : grupo.getGrupoColaboradorParticipanteVOs()) {
                if (parti.getUsuarioParticipante().getCodigo().equals(abertura.getColaboradorResponsavel().getCodigo())) {
                    parti.setGrupoColaboradorParticipanteEscolhido(true);
                    abertura.setNomeGrupoColaboradorEstudio(grupo.getDescricao());
                    return;
                } else {
                    naoExisteParticipante = true;
                }
            }
            if (naoExisteParticipante) {
                grupo.setGrupoColaboradorParticipanteVOs(new ArrayList<GrupoColaboradorParticipanteVO>());
            }
        }

    }

    public void agruparMetasColaboradorIndisponivelParaColaboradorSuplente(List<ColaboradorIndisponivelCrmVO> listaColaboradorIndisponivel, GrupoColaboradorVO grupo, AberturaMetaVO abertura) throws Exception{

        for (ColaboradorIndisponivelCrmVO objC: listaColaboradorIndisponivel){
            for (GrupoColaboradorParticipanteVO objP : grupo.getGrupoColaboradorParticipanteVOs()) {
                if (objP.getColaboradorParticipante().getCodigo().equals(objC.getColaboradorIndisponivelVO().getCodigo())) {
                    objP.setGrupoColaboradorParticipanteEscolhido(true);
                    objP.setColaboradorIndisponivel(true);
                    abertura.setMetasAgrupadasColaboradorIndisponivel(true);
                    abertura.setNomeGrupoColaboradorVenda(grupo.getDescricao());
                }
            }
        }

    }


    public List<ColaboradorIndisponivelCrmVO> verificarColaboradorResponsavelSuplente(AberturaMetaVO obj) throws Exception{
        return getFacade().getColaboradorIndisponivelCrm().consultarPorColaboradorSuplentePorPeriodo(
                obj.getColaboradorResponsavel().getColaboradorVO().getCodigo(),obj.getEmpresaVO().getCodigo(),obj.getDia());

    }

    public void inicializarDadosApresentarGrupoColaboradorSemAbertura(AberturaMetaVO abertura) throws Exception {
        for (GrupoColaboradorVO grupo : abertura.getGrupoColaboradorListaVenda()) {
            if (getFacade().getAberturaMeta().verificarPermitirVisualizarTodasCarteiras()) {
                grupo.setGrupoColaboradorParticipanteVOs(getFacade().getGrupoColaboradorParticipante().consultarGrupoColaboradorParticipantesPorCodigoETipoVisaoDiferenteMontandoUsuario(grupo.getCodigo(), "", Uteis.NIVELMONTARDADOS_ABERTURAMETA));
            } else {
                grupo.setGrupoColaboradorParticipanteVOs(getFacade().getGrupoColaboradorParticipante().consultarGrupoColaboradorParticipantesPorCodigoETipoVisaoDiferenteMontandoUsuario(grupo.getCodigo(), "VI", Uteis.NIVELMONTARDADOS_ABERTURAMETA));
            }

            abertura.setNomeGrupoColaboradorVenda(grupo.getDescricao());
            break;

        }
        for (GrupoColaboradorVO grupo : abertura.getGrupoColaboradorListaRetencao()) {
            if (getFacade().getAberturaMeta().verificarPermitirVisualizarTodasCarteiras()) {
                grupo.setGrupoColaboradorParticipanteVOs(getFacade().getGrupoColaboradorParticipante().consultarGrupoColaboradorParticipantesPorCodigoETipoVisaoDiferenteMontandoUsuario(grupo.getCodigo(), "", Uteis.NIVELMONTARDADOS_ABERTURAMETA));
            } else {
                grupo.setGrupoColaboradorParticipanteVOs(getFacade().getGrupoColaboradorParticipante().consultarGrupoColaboradorParticipantesPorCodigoETipoVisaoDiferenteMontandoUsuario(grupo.getCodigo(), "VI", Uteis.NIVELMONTARDADOS_ABERTURAMETA));
            }

            abertura.setNomeGrupoColaboradorRetencao(grupo.getDescricao());
            return;
        }
        
        for (GrupoColaboradorVO grupo : abertura.getGrupoColaboradorListaEstudio()) {
            if (getFacade().getAberturaMeta().verificarPermitirVisualizarTodasCarteiras()) {
                grupo.setGrupoColaboradorParticipanteVOs(getFacade().getGrupoColaboradorParticipante().consultarGrupoColaboradorParticipantesPorCodigoETipoVisaoDiferenteMontandoUsuario(grupo.getCodigo(), "", Uteis.NIVELMONTARDADOS_ABERTURAMETA));
            } else {
                grupo.setGrupoColaboradorParticipanteVOs(getFacade().getGrupoColaboradorParticipante().consultarGrupoColaboradorParticipantesPorCodigoETipoVisaoDiferenteMontandoUsuario(grupo.getCodigo(), "VI", Uteis.NIVELMONTARDADOS_ABERTURAMETA));
            }

            abertura.setNomeGrupoColaboradorEstudio(grupo.getDescricao());
            return;
        }
    }

    public void inicializarDadosListaGrupoColaborador(List<GrupoColaboradorVO> lista, AberturaMetaVO abertura) throws CloneNotSupportedException {
        for (GrupoColaboradorVO grupo : lista) {
            adicionarGrupoColaborador(abertura.getGrupoColaboradorListaVenda(), grupo);
            GrupoColaboradorVO grupoClonado = grupo.clone();
            adicionarGrupoColaborador(abertura.getGrupoColaboradorListaRetencao(), grupoClonado);
            GrupoColaboradorVO grupoClonadoEstudio = grupo.clone();
            adicionarGrupoColaborador(abertura.getGrupoColaboradorListaEstudio(), grupoClonadoEstudio);
        }
    }

    public void adicionarGrupoColaborador(List<GrupoColaboradorVO> lista, GrupoColaboradorVO obj) {
        int index = 0;
        for (GrupoColaboradorVO grupo : lista) {
            if (grupo.getCodigo().equals(obj.getCodigo().intValue())) {
                lista.set(index, obj);
                return;
            }
            index++;
        }
        lista.add(obj);
    }

    /**
     * Método responsavel por enviar o email apos selecionar as pessoas na tela
     * metaDetalhadaForm
     *
     * @throws Exception
     */
    public void executarEmailColetivo(MalaDiretaVO malaDiretaVO, Date diaMeta, EmpresaVO empresa) throws Exception {
        try {
            con.setAutoCommit(false);
            if (malaDiretaVO.isNovoObj().booleanValue()) {
                malaDiretaVO.setConfiguracaoSistemaCRMVO(getFacade().getConfiguracaoSistemaCRM().consultarPorChavePrimaria(1, Uteis.NIVELMONTARDADOS_DADOSBASICOS));
                getFacade().getMalaDireta().agendarEnvioEmailSemCommit(malaDiretaVO, true, diaMeta, empresa);
            }
            con.commit();
        } catch (Exception e) {
            malaDiretaVO.setNovoObj(true);
            con.rollback();
            con.setAutoCommit(true);
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    public void inicializarDadosNomeParticipanteSelecionado(AberturaMetaVO aberturaMetaVO, List<GrupoColaboradorVO> lista, Boolean isVenda) {
        aberturaMetaVO.setNomeParticipanteSelecionado("");
        aberturaMetaVO.setNomeParticipanteSelecionadoRetencao("");
        for (GrupoColaboradorVO grupo : lista) {
            if (!grupo.getGrupoColaboradorParticipanteVOs().isEmpty()) {
                for (GrupoColaboradorParticipanteVO parti : grupo.getGrupoColaboradorParticipanteVOs()) {
                    if (parti.getGrupoColaboradorParticipanteEscolhido()) {
                        if (isVenda) {
                            if (aberturaMetaVO.getNomeParticipanteSelecionado().equals("")) {
                                aberturaMetaVO.setNomeParticipanteSelecionado(parti.getUsuarioParticipante().getPrimeiroNomeConcatenado());
                            } else {
                                aberturaMetaVO.setNomeParticipanteSelecionado(aberturaMetaVO.getNomeParticipanteSelecionado() + ", " + parti.getUsuarioParticipante().getPrimeiroNomeConcatenado());
                            }
                        } else {
                            if (aberturaMetaVO.getNomeParticipanteSelecionadoRetencao().equals("")) {
                                aberturaMetaVO.setNomeParticipanteSelecionadoRetencao(parti.getUsuarioParticipante().getPrimeiroNomeConcatenado());
                            } else {
                                aberturaMetaVO.setNomeParticipanteSelecionadoRetencao(aberturaMetaVO.getNomeParticipanteSelecionadoRetencao() + ", " + parti.getUsuarioParticipante().getPrimeiroNomeConcatenado());
                            }
                        }

                    }
                }
            }
        }
    }

    public void executarSelecaoTodosFecharMetaDetalhados(FecharMetaVO obj) throws Exception {
        if (obj.getIsAbaSelecionadaHoje()) {
            obj.setTotalizadorSelecionadoHoje(getFacade().getFecharMeta().executarTodosFecharMetaDetalhados(obj, obj.getTotalizadorSelecionadoHoje()));
        } else if (obj.getIsAbaSelecionadaHistorico()) {
            obj.setTotalizadorSelecionadoHistorico(getFacade().getFecharMeta().executarTodosHistorico(obj, obj.getTotalizadorSelecionadoHistorico()));
        }
    }

    /**
     * Método responsavel por adicionar na lista de fechamentoDia as listas de
     * IndicadorVenda e IndicadorRetenção
     *
     * @param aberturaMetaVO
     * @throws Exception
     * <AUTHOR>
     */
    @SuppressWarnings("unchecked")
    public void executarPreenchimentoListaFechamentoDia(AberturaMetaVO aberturaMetaVO) throws Exception {
        Double totalPocentagem = 0.0;
        int cont = 0;
        aberturaMetaVO.setListaFechametoDia(new ArrayList<FecharMetaVO>());
        aberturaMetaVO.getListaFechametoDia().addAll(aberturaMetaVO.getFecharMetaVosRetencao());
        aberturaMetaVO.getListaFechametoDia().addAll(aberturaMetaVO.getFecharMetaVosVenda());
        aberturaMetaVO.getListaFechametoDia().addAll(aberturaMetaVO.getFecharMetaVosEstudio());
        aberturaMetaVO.setListaFechametoDia(Ordenacao.ordenarLista(aberturaMetaVO.getListaFechametoDia(), "ordem"));
        for (FecharMetaVO fecharMetaVO : aberturaMetaVO.getListaFechametoDia()) {
            //fecharMetaVO.setJustificativa("");
            fecharMetaVO.setAberturaMetaVO(aberturaMetaVO);
            totalPocentagem = totalPocentagem + fecharMetaVO.getPorcentagem();
            cont++;
        }
        aberturaMetaVO.setTotalMetaDia(totalPocentagem / cont);
    }

    /**
     * Método responsavel por adicionar a Justificativa na hora de Fechar o Dia
     *
     * @param fecharMetaVO obj FecharMetaJustificativa
     * @param listaJustificativa lista do fechamento dia.
     * <AUTHOR>
     */
    public void adicionarFecharMetaJustificativa(FecharMetaVO fecharMetaVO, List<FecharMetaVO> listaJustificativa) {
        int index = 0;
        for (FecharMetaVO obj : listaJustificativa) {
            if (fecharMetaVO.getIdentificadorMeta().equals(obj.getIdentificadorMeta())) {
                listaJustificativa.set(index, fecharMetaVO);
            }
            index++;
        }
    }

    /**
     * Método responsavel por validar se as justificativas foram preenchidas
     * para fazer a alteração
     *
     * @param lista
     * @throws ConsistirException
     */
    public void validarPreenchimentoJustificativaFechamentoDia(List<FecharMetaVO> lista) throws ConsistirException {
        for (FecharMetaVO fecharMetaVO : lista) {
            if ((fecharMetaVO.getMeta() > fecharMetaVO.getMetaAtingida()) && UteisValidacao.emptyString(fecharMetaVO.getJustificativa().trim())) {
                throw new ConsistirException("Não foram atingidas todas metas, é preciso preencher as justificativas !");
            }
        }
    }

    /**
     * Método responsavel por validar se ja existe uma abertura feita para o dia
     * atual, se não tiver ele não deixa fechar o dia
     *
     * @param obj
     * @throws Exception Caso não tenha sido feita nenhuma abertura de Meta
     * <AUTHOR>
     */
    public void validarAberturaMeta(AberturaMetaVO obj) throws Exception {
        Boolean temAberturaMeta = consultarPorDiaAberturaMeta(obj.getDia(), obj.getDia(), false);
        if (!temAberturaMeta) {
            throw new Exception("Não foi feita nenhuma Abertura de Meta !");
        }
    }

    /**
     * Método responsavel por validar se já foi feito algum fechamento de dia
     * para o dia atual, caso exista ele manda uma exceção
     *
     * @param obj
     * @throws Exception Caso ja exista um Fechamento de dia
     */
    public void validarFechamentoDia(AberturaMetaVO obj) throws Exception {
        //AberturaMetaVO abertura = consultarPorChavePrimaria(obj.getCodigo().intValue(), Uteis.NIVELMONTARDADOS_TODOS);
        if (!obj.getMetaEmAberto()) {
            throw new Exception("A meta do dia já foi fechada, entre em contato com o administrador do sistema !");
        }
    }

    /**
     * Método responsavel por alterar apenas o campo FecharMeta da AberturaMeta
     * no momento de fazer o Fechamento do Dia.
     *
     * @param obj
     * @throws Exception
     * <AUTHOR>
     */
    public AberturaMetaVO persistirAlteracaoFechamentoDiaAberturaMeta(AberturaMetaVO obj) throws Exception {
        validarAberturaMeta(obj);
        validarFechamentoDia(obj);
        validarPreenchimentoJustificativa(obj);
        obj.setMetaEmAberto(false);
        alterarSomenteCampoFechaMetaDiaFechamentoJustificativa(obj);
        return obj;
    }

    public void validarPreenchimentoJustificativa(AberturaMetaVO obj) throws ConsistirException {
        if (!UteisValidacao.emptyString(obj.getJustificativa()) && obj.getJustificativa().trim().length() < 15) {
            throw new ConsistirException("A justificativa está muito curta.");
        }
    }

    public void inicializarEmpresaAberturaMeta(AberturaMetaVO obj, EmpresaVO empresa) {
        obj.setEmpresaVO(empresa);
    }

    /**
     * Metodo responsavel por verificar se o usuario logado tem permissao para
     * utilicar a funcionalidade Visualizar todas carteiras
     * <p/>
     * Autor: Pedro Y. Saito Criado em 16/03/2011
     */
    public boolean verificarPermitirVisualizarTodasCarteiras() throws Exception {
        return verificarPermitirVisualizarTodasCarteiras(null);
    }

    /**
     * Metodo responsavel por verificar se o usuario logado tem permissao para
     * utilicar a funcionalidade Visualizar todas carteiras
     * <p/>
     * Autor: Pedro Y. Saito Criado em 16/03/2011
     */
    public boolean verificarPermitirVisualizarTodasCarteiras(UsuarioVO usuario) throws Exception {
        return verificarPermissaoFuncionalidade("PermitirVisualizarTodasCarteiras", usuario);
    }

    /**
     * Responsável por consultas as metas abertas de um colaborador em
     * determinado período
     *
     * <AUTHOR> 12/05/2011
     */
    public List<AberturaMetaVO> consultarPorResponsavelPeriodo(Boolean aberta, List<Integer> codigos, Date inicio, Date fim, Integer empresa, Integer nivelMontarDados) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT ab.codigo                                         , \n");
        sql.append("       ab.colaboradorresponsavel                         , \n");
        sql.append("       ab.empresa                                        , \n");
        sql.append("       ab.responsavelcadastro                            , \n");
        sql.append("       ab.dia                                            , \n");
        sql.append("       ab.metaemaberto                                   , \n");
        sql.append("       ab.aberturaretroativa                             , \n");
        sql.append("       ab.responsavelliberacaotrocacolaboradorresponsavel, \n");
        sql.append("       ab.diafechamento \n");
        sql.append("FROM   aberturameta ab \n");
        sql.append("       INNER JOIN usuario u \n");
        sql.append("       ON     u.codigo = ab.colaboradorresponsavel \n");
        sql.append("WHERE  dia BETWEEN ? AND ? \n");
        if (codigos != null) {
            sql.append("AND    u.colaborador IN ( ");
            String cods = "";
            if (codigos.isEmpty()) {
                cods = "0";
            } else {
                for (Integer codigo : codigos) {
                    cods += "," + codigo;
                }
                cods = cods.replaceFirst(",", "");
            }
            sql.append(cods + ")\n");
        }

        if (aberta != null) {
            if (aberta) {
                sql.append("AND    ab.metaemaberto IS TRUE");
            } else {
                sql.append("AND    ab.metaemaberto IS NOT TRUE");
            }
        }
        if (empresa != null) {
            sql.append("AND ab.empresa = ?");
        }

        int i = 0;
        Declaracao dc = new Declaracao(sql.toString(), con);
        dc.setDate(++i, Uteis.getDataJDBC(inicio));
        dc.setDate(++i, Uteis.getDataJDBC(fim));
        if (empresa != null) {
            dc.setInt(++i, empresa);
        }
        ResultSet query = dc.executeQuery();
        return montarDadosConsulta(query, con, nivelMontarDados);
    }

    public void executarCalculoMetaEstudio(AberturaMetaVO obj, PermissaoAcessoMenuVO permissoes) throws Exception {
        List<Date> datasCalcular = obterDatasCalcular(obj, true);
        obj.setFecharMetaVosEstudio(new ArrayList<FecharMetaVO>());

        //metas sessões finais
        FecharMetaVO sessoesFinais = montarMetaGenerico(obj, true, datasCalcular, FasesCRMEnum.ULTIMAS_SESSOES, obj.getFecharMetaVosEstudio());
        List<ConfiguracaoDiasMetasTO> diasSessoes = getFacade().getConfiguracaoSistemaCRM().consultarConfiguracaoDiasMetasPorFase(FasesCRMEnum.ULTIMAS_SESSOES);
        for (ConfiguracaoDiasMetasTO nrDias : diasSessoes) {
            sessoesFinais.setMeta(sessoesFinais.getMeta()
                    + consultarCalculoMetaQtdeGenerico(FasesCRMSQL.sqlSessoesFinais(obj.getColaboradorResponsavel().getColaboradorVO().getCodigo(), nrDias.getNrDia(), obj.getEmpresaVO().getCodigo(), true, obj.getCodigoClientesObjecaoDefinitiva())));
        }
        obj.getFecharMetaVosEstudio().add(sessoesFinais);
        //meta sem agendamento
        FecharMetaVO semAgendamento = montarMetaGenerico(obj, true, datasCalcular, FasesCRMEnum.SEM_AGENDAMENTO, obj.getFecharMetaVosEstudio());
        List<ConfiguracaoDiasMetasTO> diasSemAgendamento = getFacade().getConfiguracaoSistemaCRM().consultarConfiguracaoDiasMetasPorFase(FasesCRMEnum.SEM_AGENDAMENTO);
        for (Date data : datasCalcular) {
            for (ConfiguracaoDiasMetasTO nrDias : diasSemAgendamento) {
                semAgendamento.setMeta(semAgendamento.getMeta()
                        + consultarCalculoMetaQtdeGenerico(FasesCRMSQL.sqlSemAgendamento(data,
                        obj.getColaboradorResponsavel().getColaboradorVO().getCodigo(), nrDias, obj.getEmpresaVO().getCodigo(), true, obj.getCodigoClientesObjecaoDefinitiva())));
            }
        }
        obj.getFecharMetaVosEstudio().add(semAgendamento);
    }

    public FecharMetaVO montarMetaGenerico(AberturaMetaVO obj, Boolean calcular, List<Date> datasCalcular,
            FasesCRMEnum fase, List<FecharMetaVO> metas) throws Exception {

        // Regras de Negocio Perdas
        FecharMetaVO fecharMeta = new FecharMetaVO();
        fecharMeta.setFase(fase);
        fecharMeta.setDataRegistro(obj.getDia());
        fecharMeta.setIdentificadorMeta(fase.getSigla());
        fecharMeta.setMetaCalculada(calcular);
        fecharMeta.setAberturaMetaVO(obj);
        return fecharMeta;
    }

    public Double consultarCalculoMetaQtdeGenerico(String sql) throws Exception {
        ResultSet tabelaResultado = criarConsulta(sql, con);
        tabelaResultado.next();
        return ((double) tabelaResultado.getInt(1));
    }

    public void preencherFecharMetaDetalhadoEstudio(AberturaMetaVO aberturaMeta, PermissaoAcessoMenuVO permissoes) throws Exception {
        List<Date> datasCalcular = obterDatasCalcular(aberturaMeta, true);
        for (FecharMetaVO obj : aberturaMeta.getFecharMetaVosEstudio()) {
            if(!obj.getMetaCalculada()){
                continue;
            }

            //sessões finais
            if (obj.getFase().equals(FasesCRMEnum.ULTIMAS_SESSOES)) {
                List<ConfiguracaoDiasMetasTO> diasSessoes = getFacade().getConfiguracaoSistemaCRM().consultarConfiguracaoDiasMetasPorFase(FasesCRMEnum.ULTIMAS_SESSOES);
                for (ConfiguracaoDiasMetasTO dia : diasSessoes) {
                    getFacade().getFecharMetaDetalhado().consultarClientesSessoesFinais(obj, dia.getNrDia(),
                            aberturaMeta.getResponsavelCadastro().getColaboradorVO().getCodigo(), aberturaMeta.getEmpresaVO().getCodigo(), aberturaMeta.getCodigoClientesObjecaoDefinitiva());
                }
            }
            if (obj.getFase().equals(FasesCRMEnum.SEM_AGENDAMENTO)) {
                List<ConfiguracaoDiasMetasTO> diasSemAgendamento = getFacade().getConfiguracaoSistemaCRM().consultarConfiguracaoDiasMetasPorFase(FasesCRMEnum.SEM_AGENDAMENTO);
                for (Date data : datasCalcular) {
                    for (ConfiguracaoDiasMetasTO dia : diasSemAgendamento) {
                        getFacade().getFecharMetaDetalhado().consultarClientesSemAgendamento(data, obj, dia,
                                aberturaMeta.getResponsavelCadastro().getColaboradorVO().getCodigo(), aberturaMeta.getEmpresaVO().getCodigo(), aberturaMeta.getCodigoClientesObjecaoDefinitiva());
                    }
                }
            }
        }
    }

    public AberturaMetaVO consultarAberturaPorFecharMeta(Integer codigoFecharMeta, int nivelMontarDados) throws Exception {
        consultarCRM(getIdEntidade(), false);
        String sql = "select * from aberturameta  where codigo in (select aberturameta from fecharmeta where codigo = " + codigoFecharMeta + ")";
        PreparedStatement sqlConsultar = con.prepareStatement(sql);
        ResultSet tabelaResultado = sqlConsultar.executeQuery();
        if (!tabelaResultado.next()) {
            return new AberturaMetaVO();
        }
        return (montarDados(tabelaResultado, con, nivelMontarDados));
    }
    
    public Date obterDiaAberturaMeta(Integer codigoFecharMeta) throws Exception {
        consultarCRM(getIdEntidade(), false);
        String sql = "select dia from aberturameta  where codigo = " + codigoFecharMeta;
        PreparedStatement sqlConsultar = con.prepareStatement(sql);
        ResultSet tabelaResultado = sqlConsultar.executeQuery();
        if (!tabelaResultado.next()) {
            return null;
        }
        return tabelaResultado.getDate("dia");
    }

    private void executarCalculoMetaLeadsHoje(AberturaMetaVO obj, String tipoVinculos) throws Exception {
        // Regras de Negocio Agenda
        FecharMetaVO fecharMeta = new FecharMetaVO();
        fecharMeta.setDataRegistro(obj.getDia());
        fecharMeta.setIdentificadorMeta(FasesCRMEnum.LEADS_HOJE.getSigla());
        if (!UteisValidacao.emptyString(tipoVinculos)) {
            fecharMeta.setMeta(consultarCalculoMetaLeadHoje(obj.getColaboradorResponsavel().getCodigo(), obj.getEmpresaVO().getCodigo()));
        } else {
            fecharMeta.setMetaCalculada(false);
        }

        fecharMeta.setAberturaMetaVO(obj);
        obj.getFecharMetaVosLead().add(fecharMeta);
        fecharMeta = null;
    }
    
    private void executarCalculoMetaLeadsAcumulado(AberturaMetaVO obj, String tipoVinculos) throws Exception {
        // Regras de Negocio Agenda
        FecharMetaVO fecharMeta = new FecharMetaVO();
        fecharMeta.setDataRegistro(obj.getDia());
        fecharMeta.setIdentificadorMeta(FasesCRMEnum.LEADS_ACUMULADAS.getSigla());
        if (!UteisValidacao.emptyString(tipoVinculos)) {
            fecharMeta.setMeta(consultarCalculoMetaLeadAcumulado(obj.getColaboradorResponsavel().getCodigo(), obj.getEmpresaVO().getCodigo()));
        } else {
            fecharMeta.setMetaCalculada(false);
        }

        fecharMeta.setAberturaMetaVO(obj);
        obj.getFecharMetaVosLead().add(fecharMeta);
        fecharMeta = null;
    }
    
    public Double consultarCalculoMetaLeadAcumulado(Integer codColaboradorResponsavel, Integer empresa) throws Exception {
        consultarCRM(getIdEntidade(), false);
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT count(cl.codigo) from conversaolead cl");
        sql.append(" inner join lead l on cl.lead = l.codigo");
        sql.append(" left join passivo p on p.codigo = l.passivo");
        sql.append(" left join cliente cli on cli.codigo = l.cliente");
        sql.append(" WHERE  cl.responsavel = ").append(codColaboradorResponsavel);
        sql.append(" AND cl.tratada = 'f' ");
        sql.append(" AND cl.datacriacao >= '").append(Uteis.getDataJDBC(Calendario.ontem())).append("'");
        sql.append(" AND cl.datacriacao < '").append(Uteis.getDataJDBC(Calendario.hoje())).append("'");
        sql.append(" AND l.empresa = ").append(empresa);
        sql.append(" AND exists(");
        sql.append(" select codigo from fecharmetadetalhado where codigoorigem = cl.codigo and origem = 'CONVERSAOLEAD'");
        sql.append(" );");

        Statement sqlConsultar = con.createStatement();
        ResultSet tabelaResultado = sqlConsultar.executeQuery(sql.toString());
        if (!tabelaResultado.next()) {
            return new Double(0);
        }
        return (new Double(tabelaResultado.getInt(1)));
    }
    
    public Double consultarCalculoMetaLeadHoje(Integer codColaboradorResponsavel, Integer empresa) throws Exception {
        consultarCRM(getIdEntidade(), false);
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT count(cl.codigo) from conversaolead cl inner join lead l on cl.lead = l.codigo left join passivo p on p.codigo = l.passivo left join cliente cli on cli.codigo = l.cliente  WHERE  cl.responsavel = " + codColaboradorResponsavel + " and cl.tratada = 'f' ");
        sql.append(" AND l.empresa = " + empresa + " AND not exists(select codigo from fecharmetadetalhado where codigoorigem = cl.codigo and origem = 'CONVERSAOLEAD')");

        Statement sqlConsultar = con.createStatement();
        ResultSet tabelaResultado = sqlConsultar.executeQuery(sql.toString());
        if (!tabelaResultado.next()) {
            return new Double(0);
        }
        return (new Double(tabelaResultado.getInt(1)));
    }
    
    public void preencherFecharMetaDetalhadoLead(AberturaMetaVO aberturaMeta, Integer empresa, PermissaoAcessoMenuVO permissoes) throws Exception {
        TiposVinculosFase tiposVinculosFase = new TiposVinculosFase(con);
        List<String> tipos = tiposVinculosFase.consultarTiposColaborador(aberturaMeta.getColaboradorResponsavel().getColaboradorVO().getCodigo());
        Boolean usarDivisao = tiposVinculosFase.verificarUsarDivisao();

        for (FecharMetaVO obj : aberturaMeta.getFecharMetaVosLead()) {
            if(!obj.getMetaCalculada()){
                continue;
            }
            String tipoVinculos = "";
            //Leads hoje
            if (obj.getIdentificadorMeta().equals(FasesCRMEnum.LEADS_HOJE.getSigla())
                    && aberturaMeta.getEmpresaVO().utilizaAlgumaIntegracaoLeadsCrm()) {
                tipoVinculos = verificarCalcularMeta(aberturaMeta, FasesCRMEnum.LEADS_HOJE, usarDivisao, tipos, tiposVinculosFase);
                if (!UteisValidacao.emptyString(tipoVinculos)) {
                    getFacade().getFecharMetaDetalhado().consultarLeadsHoje(obj,  aberturaMeta.getColaboradorResponsavel().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS,aberturaMeta.getEmpresaVO().getCodigo());
                    Integer meta = obj.getFecharMetaDetalhadoVOs().size();
                    obj.setMeta(meta.doubleValue());
                }
            }
            
             //Leads acumulado
            if (obj.getIdentificadorMeta().equals(FasesCRMEnum.LEADS_ACUMULADAS.getSigla())
                    && aberturaMeta.getEmpresaVO().utilizaAlgumaIntegracaoLeadsCrm()) {
                tipoVinculos = verificarCalcularMeta(aberturaMeta, FasesCRMEnum.LEADS_ACUMULADAS, usarDivisao, tipos, tiposVinculosFase);
                if (!UteisValidacao.emptyString(tipoVinculos)) {
                    getFacade().getFecharMetaDetalhado().consultarLeadsAcumulado(obj,  aberturaMeta.getColaboradorResponsavel().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS,aberturaMeta.getEmpresaVO().getCodigo());
                    Integer meta = obj.getFecharMetaDetalhadoVOs().size();
                    obj.setMeta(meta.doubleValue());
                }
            }
        }
    }

    public List<NotificacaoLigacaoAgendadaJSON> consultarAgendamentosLigacao(UsuarioVO usuarioVO, EmpresaVO empresaVO, Date data) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append(" SELECT u.codigo as codigo_usuario, am.codigo as codigo_abertura_meta, am.dia as dia_abertura_meta, fm.identificadormeta, " +
                "fm.codigo as codigo_fechar_meta, fmd.codigo as codigo_fechar_meta_detalhado, fmd.origem, age.codigo as codigo_agendamento, " +
                "age.dataagendamento, age.hora, age.minuto, p.nome as nome_cliente, p.fotokey");
        sql.append(" FROM usuario u");
        sql.append(" INNER JOIN aberturameta am ON am.colaboradorresponsavel = u.codigo");
        sql.append(" INNER JOIN fecharmeta fm ON fm.aberturameta = am.codigo");
        sql.append(" INNER JOIN fecharmetadetalhado fmd ON fmd.fecharmeta = fm.codigo");
        sql.append(" INNER JOIN agenda age ON age.codigo = fmd.codigoorigem");
        sql.append(" INNER JOIN cliente cli ON cli.codigo = age.cliente");
        sql.append(" INNER JOIN pessoa p ON p.codigo = cli.pessoa");
        sql.append(" WHERE u.codigo = ?");
        if (UtilReflection.objetoMaiorQueZero(empresaVO, "getCodigo()")) {
            sql.append(" AND am.empresa = ?");
        }
        sql.append(" AND am.metaemaberto");
        sql.append(" AND am.dia = ?");
        sql.append(" AND fm.identificadormeta = 'AG'");
        sql.append(" AND age.dataagendamento = ?");
        sql.append(" AND age.hora = ?");
        sql.append(" AND age.minuto = ?");

        Integer hora = Uteis.gethoraHH(data);
        Integer minuto = Uteis.getMinutos(data);
        Date dia = Calendario.getDataComHoraZerada(data);

        PreparedStatement stm = con.prepareStatement(sql.toString());
        int i = 1;
        stm.setInt(i++, usuarioVO.getCodigo());
        if (UtilReflection.objetoMaiorQueZero(empresaVO, "getCodigo()")) {
            stm.setInt(i++, empresaVO.getCodigo());
        }
        stm.setDate(i++, Uteis.getDataJDBC(dia));
        stm.setDate(i++, Uteis.getDataJDBC(dia));
        stm.setString(i++, String.format("%02d", hora));
        stm.setString(i++, String.format("%02d", minuto));

        List<NotificacaoLigacaoAgendadaJSON> notificacoes = new ArrayList<NotificacaoLigacaoAgendadaJSON>();

        ResultSet rs = stm.executeQuery();
        while (rs.next()) {
            NotificacaoLigacaoAgendadaJSON json = new NotificacaoLigacaoAgendadaJSON();
            json.setCodigoAberturaMeta(rs.getInt("codigo_abertura_meta"));
            json.setCodigoAgendamento(rs.getInt("codigo_agendamento"));
            json.setCodigoFecharMeta(rs.getInt("codigo_fechar_meta"));
            json.setCodigoFecharMetaDetalhada(rs.getInt("codigo_fechar_meta_detalhado"));
            json.setCodigoUsuario(rs.getInt("codigo_usuario"));
            json.setDataAgendamento(rs.getDate("dataagendamento"));
            json.setHora(rs.getString("hora"));
            json.setMinuto(rs.getString("minuto"));
            json.setNomeCliente(rs.getString("nome_cliente"));
            json.setChaveFotoCliente(rs.getString("fotokey"));
            notificacoes.add(json);
        }

        return notificacoes;
    }

    public List<AberturaMetaVO> consultarMetasAbertaPorDiaPorFase(Date data, Integer empresa, FasesCRMEnum fasesCRMEnum, Integer nivelMontarDados) throws Exception {
        Statement stm = con.createStatement();
        String sqlStr = "SELECT * FROM AberturaMeta\n" +
                "INNER JOIN fecharmeta fm ON fm.aberturameta = aberturameta.codigo\n" +
                "WHERE\n" +
                "dia = '" + Uteis.getDataJDBC(data) + "'\n" +
                "and empresa = " + empresa + "\n" +
                "and metaEmAberto is true\n" +
                "and metacalculada is true\n" +
                "and fm.identificadormeta = '" + fasesCRMEnum.getSigla() + "'";
        ResultSet tabelaResultado = stm.executeQuery(sqlStr);
        return (montarDadosConsulta(tabelaResultado, con, nivelMontarDados));
    }


    public Boolean abriuMetaHoje() throws Exception{
        String sql = "select codigo " +
                "       from aberturaMeta " +
                "      where dia >= '" + Uteis.getDataAplicandoFormatacao(Calendario.hoje(), "yyyy-MM-dd 00:00:00") + "' " +
                "        and dia <= '" + Uteis.getDataAplicandoFormatacao(Calendario.hoje(), "yyyy-MM-dd 23:59:59") + "' " +
                "  limit 1";
        PreparedStatement pst = con.prepareStatement(sql);
        ResultSet rs = pst.executeQuery();
        return rs.next();
    }
}
