/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package negocio.facade.jdbc.crm;

import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.AmostraClienteTO;
import negocio.comuns.crm.MalaDiretaCRMExtraClienteVO;
import negocio.comuns.crm.MalaDiretaCRMExtraColaboradorVO;
import negocio.comuns.crm.MalaDiretaVO;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.interfaces.crm.MalaDiretaCRMExtraColaboradorInterfaceFacade;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.List;

/**
 * Created by <PERSON><PERSON> on 02/12/2015
 */
public class MalaDiretaCRMExtraColaborador extends SuperEntidade implements MalaDiretaCRMExtraColaboradorInterfaceFacade {


    public MalaDiretaCRMExtraColaborador() throws Exception {
        super();
    }

    public MalaDiretaCRMExtraColaborador(Connection conexao) throws Exception {
        super(conexao);
    }

    public static List<MalaDiretaCRMExtraColaboradorVO> montarDadosConsulta(ResultSet tabelaResultado, int nivelMontarDados) throws Exception {
        List<MalaDiretaCRMExtraColaboradorVO> vetResultado = new ArrayList<MalaDiretaCRMExtraColaboradorVO>();
        while (tabelaResultado.next()) {
            MalaDiretaCRMExtraColaboradorVO obj = montarDados(tabelaResultado, nivelMontarDados);
            vetResultado.add(obj);
        }
        return vetResultado;
    }

    public static MalaDiretaCRMExtraColaboradorVO montarDadosBasico(ResultSet dadosSQL) throws Exception {
        MalaDiretaCRMExtraColaboradorVO obj = new MalaDiretaCRMExtraColaboradorVO();
        obj.setNovoObj(false);
        obj.setCodigo(dadosSQL.getInt("codigo"));
        obj.getMalaDiretaVO().setCodigo(dadosSQL.getInt("maladireta"));
        obj.getUsuarioVO().setCodigo(dadosSQL.getInt("usuario"));

        return obj;
    }

    public static MalaDiretaCRMExtraColaboradorVO montarDados(ResultSet dadosSQL, int nivelMontarDados) throws Exception {
        MalaDiretaCRMExtraColaboradorVO obj = montarDadosBasico(dadosSQL);
        return obj;
    }

    public void incluir(MalaDiretaCRMExtraColaboradorVO obj) throws Exception {
        String sql = "INSERT INTO maladiretacrmextracolaborador(maladireta, usuario) VALUES (?, ?)";
        PreparedStatement sqlInserir = con.prepareStatement(sql);
        if (obj.getMalaDiretaVO().getCodigo() != 0) {
            sqlInserir.setInt(1, obj.getMalaDiretaVO().getCodigo());
        } else {
            sqlInserir.setNull(1, 0);
        }

        if (obj.getUsuarioVO().getCodigo() != 0) {
            sqlInserir.setInt(2, obj.getUsuarioVO().getCodigo());
        } else {
            sqlInserir.setNull(2, 0);
        }
        sqlInserir.execute();
        obj.setCodigo(obterValorChavePrimariaCodigo());
        obj.setNovoObj(false);
    }

    public void alterar(MalaDiretaCRMExtraColaboradorVO obj) throws Exception {
        String sql = "UPDATE maladiretacrmextracolaborador set maladireta = ?, usuario = ? WHERE codigo = ?";
        PreparedStatement sqlAlterar = con.prepareStatement(sql);
        if (obj.getMalaDiretaVO().getCodigo() != 0) {
            sqlAlterar.setInt(1, obj.getMalaDiretaVO().getCodigo());
        } else {
            sqlAlterar.setNull(1, 0);
        }

        if (obj.getUsuarioVO().getCodigo() != 0) {
            sqlAlterar.setInt(2, obj.getUsuarioVO().getCodigo());
        } else {
            sqlAlterar.setNull(2, 0);
        }

        sqlAlterar.setInt(3, obj.getCodigo());
        sqlAlterar.execute();
    }

    public void excluir(MalaDiretaCRMExtraColaboradorVO obj) throws Exception {
        excluir(getIdEntidade());
        String sql = "DELETE FROM maladiretacrmextracolaborador WHERE codigo = ?";
        PreparedStatement sqlExcluir = con.prepareStatement(sql);
        sqlExcluir.setInt(1, obj.getCodigo());
        sqlExcluir.execute();
    }

    public void excluirPorMalaDireta(MalaDiretaVO obj) throws Exception {
        String sql = "DELETE FROM maladiretacrmextracolaborador WHERE maladireta = ?";
        PreparedStatement sqlExcluir = con.prepareStatement(sql);
        sqlExcluir.setInt(1, obj.getCodigo());
        sqlExcluir.execute();
    }

    public void incluirListaColaborador(MalaDiretaVO malaDiretaVO, List<UsuarioVO> usuarioVOList) throws Exception {
        if (!malaDiretaVO.isNovoObj()) {
            excluirPorMalaDireta(malaDiretaVO);
        }
        for (UsuarioVO usuario : usuarioVOList) {
            MalaDiretaCRMExtraColaboradorVO crmExtraColaboradorVO = new MalaDiretaCRMExtraColaboradorVO();
            crmExtraColaboradorVO.setMalaDiretaVO(malaDiretaVO);
            crmExtraColaboradorVO.getUsuarioVO().setCodigo(usuario.getCodigo());
            incluir(crmExtraColaboradorVO);
        }
    }

    public List<MalaDiretaCRMExtraColaboradorVO> consultarPorMalaDireta(Integer codMalaDireta, int nivelMontarDados) throws Exception {
        List<MalaDiretaCRMExtraColaboradorVO> objetos = new ArrayList<MalaDiretaCRMExtraColaboradorVO>();
        String sql = "SELECT * FROM maladiretacrmextracolaborador WHERE maladireta = ?";
        PreparedStatement sqlConsulta = con.prepareStatement(sql);
        sqlConsulta.setInt(1, codMalaDireta);
        ResultSet resultado = sqlConsulta.executeQuery();
        while (resultado.next()) {
            MalaDiretaCRMExtraColaboradorVO novoObj = montarDados(resultado, nivelMontarDados);
            objetos.add(novoObj);
        }
        return objetos;
    }
}