package negocio.facade.jdbc.notaFiscal;

import br.com.pactosolucoes.comuns.util.FileUtilities;
import br.com.pactosolucoes.comuns.util.Formatador;
import br.com.pactosolucoes.integracao.enotas.EnotasService;
import br.com.pactosolucoes.integracao.enotas.nfce.NFCeEnotasJSON;
import br.com.pactosolucoes.integracao.enotas.nfe.NFeEnotasJSON;
import br.com.pactosolucoes.integracao.enotas.nfse.EnotasNFSeJSON;
import br.com.pactosolucoes.integracao.enotas.to.InutilizacaoNotaFiscalTO;
import br.com.pactosolucoes.integracao.enotas.to.NotaEnotasTO;
import br.com.pactosolucoes.integracao.notasDelphi.nfce.NFCeDelphiJSON;
import br.com.pactosolucoes.integracao.notasDelphi.nfce.PagamentoNFCeDelphiJSON;
import br.com.pactosolucoes.integracao.notasDelphi.nfce.ProdutoNFCeDelphiJSON;
import br.com.pactosolucoes.integracao.notasDelphi.nfse.LoteNFSeDelphiJSON;
import br.com.pactosolucoes.integracao.notasDelphi.nfse.PagamentoNFSeDelphiJSON;
import br.com.pactosolucoes.integracao.notasDelphi.nfse.ProdutoNFSeDelphiJSON;
import br.com.pactosolucoes.integracao.notasDelphi.nfse.RpsNFSeDelphiJSON;
import br.com.pactosolucoes.oamd.controle.basico.DAO;
import controle.arquitetura.SuperControle;
import negocio.comuns.arquitetura.LogVO;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.basico.ColaboradorVO;
import negocio.comuns.basico.ConfiguracaoNotaFiscalVO;
import negocio.comuns.basico.EmailVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.basico.EnderecoVO;
import negocio.comuns.basico.FornecedorVO;
import negocio.comuns.basico.PessoaVO;
import negocio.comuns.basico.TelefoneVO;
import negocio.comuns.contrato.ContratoVO;
import negocio.comuns.contrato.MovProdutoVO;
import negocio.comuns.crm.ConfiguracaoSistemaCRMVO;
import negocio.comuns.financeiro.CartaoCreditoVO;
import negocio.comuns.financeiro.ChequeVO;
import negocio.comuns.financeiro.FormaPagamentoVO;
import negocio.comuns.financeiro.ItemGestaoNotaFamiliaTO;
import negocio.comuns.financeiro.ItemGestaoNotasTO;
import negocio.comuns.financeiro.MovContaVO;
import negocio.comuns.financeiro.MovPagamentoVO;
import negocio.comuns.financeiro.MovParcelaVO;
import negocio.comuns.financeiro.NFSeEmitidaVO;
import negocio.comuns.financeiro.NotaFiscalConsumidorEletronicaVO;
import negocio.comuns.financeiro.ReciboPagamentoVO;
import negocio.comuns.financeiro.enumerador.TipoFormaPagto;
import negocio.comuns.financeiro.enumerador.TipoRelatorioDF;
import negocio.comuns.nfe.RetornoEnvioNotaFiscalTO;
import negocio.comuns.nfe.enumerador.ResultadoEnvioNFSeEnum;
import negocio.comuns.notaFiscal.AmbienteEmissaoNotaFiscalEnum;
import negocio.comuns.notaFiscal.FiltroNotaFiscalTO;
import negocio.comuns.notaFiscal.LayoutTelaNotaFiscalTO;
import negocio.comuns.notaFiscal.NotaEmitirFormaPagamentoTO;
import negocio.comuns.notaFiscal.NotaEmitirProdutoTO;
import negocio.comuns.notaFiscal.NotaEmitirTO;
import negocio.comuns.notaFiscal.NotaFiscalOperacaoVO;
import negocio.comuns.notaFiscal.NotaFiscalVO;
import negocio.comuns.notaFiscal.NotaPagamentoTO;
import negocio.comuns.notaFiscal.NotaProcessarTO;
import negocio.comuns.notaFiscal.NotaProdutoTO;
import negocio.comuns.notaFiscal.NotaTO;
import negocio.comuns.notaFiscal.SituacaoNotaFiscalEnum;
import negocio.comuns.notaFiscal.StatusEnotasEnum;
import negocio.comuns.notaFiscal.TipoNotaFiscalEnum;
import negocio.comuns.notaFiscal.TotalizadorNotaFiscalTO;
import negocio.comuns.plano.ProdutoVO;
import negocio.comuns.plano.enumerador.TipoProduto;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.Ordenacao;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisEmail;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.comuns.utilitarias.UtilReflection;
import negocio.facade.jdbc.arquitetura.*;
import negocio.facade.jdbc.basico.Cidade;
import negocio.facade.jdbc.basico.Cliente;
import negocio.facade.jdbc.basico.Colaborador;
import negocio.facade.jdbc.basico.ConfiguracaoNotaFiscal;
import negocio.facade.jdbc.basico.ConfiguracaoSistema;
import negocio.facade.jdbc.basico.Empresa;
import negocio.facade.jdbc.basico.Estado;
import negocio.facade.jdbc.basico.Fornecedor;
import negocio.facade.jdbc.basico.NotaFiscalConsumidorEletronica;
import negocio.facade.jdbc.basico.Pais;
import negocio.facade.jdbc.basico.Pessoa;
import negocio.facade.jdbc.contrato.MovProduto;
import negocio.facade.jdbc.financeiro.FormaPagamento;
import negocio.facade.jdbc.financeiro.MovConta;
import negocio.facade.jdbc.financeiro.MovPagamento;
import negocio.facade.jdbc.financeiro.MovParcela;
import negocio.facade.jdbc.financeiro.NFSeEmitida;
import negocio.facade.jdbc.financeiro.ReciboPagamento;
import negocio.facade.jdbc.plano.Produto;
import negocio.facade.jdbc.utilitarias.CacheControl;
import negocio.facade.jdbc.utilitarias.Conexao;
import negocio.interfaces.notaFiscal.NotaFiscalInterfaceFacade;
import org.apache.commons.lang.StringUtils;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;
import servicos.util.ExecuteRequestHttpService;

import java.io.File;
import java.io.IOException;
import java.net.HttpURLConnection;
import java.net.URL;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.TimeZone;
import java.util.logging.Level;
import java.util.logging.Logger;

public class NotaFiscal extends SuperEntidade implements NotaFiscalInterfaceFacade {

    private static final String DELPHI_GRAVARNFSe = ".gravarListaLoteComReferenciaAsync";
    private static final String DELPHI_GRAVARNFCe = ".gravarNFCe";

    private static final String IDENTIFICADOR_RECIBO = "REC_";
    private static final String IDENTIFICADOR_CARTAO = "CAR_";
    private static final String IDENTIFICADOR_CHEQUE = "CHE_";
    private static final String IDENTIFICADOR_MOVPAGAMENTO = "MPA_";
    private static final String IDENTIFICADOR_MOVPRODUTO = "MPO_";
    private static final String IDENTIFICADOR_MOVCONTA = "MCA_";
    private static final String IDENTIFICADOR_FAMILIA = "FAM_";

    public NotaFiscal() throws Exception {
        super();
    }

    public NotaFiscal(Connection con) throws Exception {
        super(con);
    }

    public void incluir(NotaFiscalVO obj) throws Exception {
        try {
            con.setAutoCommit(false);
            incluirSemCommit(obj);
            con.commit();
        } catch (Exception e) {
            con.rollback();
            con.setAutoCommit(true);
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    public void incluirSemCommit(NotaFiscalVO obj) throws Exception {
        String sql = "INSERT INTO NotaFiscal (dataRegistro, dataEmissao, dataAutorizacao, idEmpresaEnotas, idExterno, idReferencia, jsonNota, jsonEnvio, jsonRetorno, jsonEnvioCancelamento, jsonRetornoCancelamento, "
                + "jsonEnvioInutilizar, jsonRetornoInutilizar, statusNota, nomeCliente, razaoSocial, cpfCNPJ, numeroNota, chaveAcesso, tipo, empresa, usuario, "
                + " pessoa, configuracaonotafiscal, nfseEmitida, notaFiscalConsumidorEletronica, sequencialfamilia, excluido, valor, notaFiscalAnterior, notaFiscalNova, idPacto) "
                + "VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
        int i = 0;
        PreparedStatement sqlInserir = con.prepareStatement(sql);
        sqlInserir.setTimestamp(++i, Uteis.getDataJDBCTimestamp(Calendario.hoje()));
        sqlInserir.setTimestamp(++i, Uteis.getDataJDBCTimestamp(obj.getDataEmissao()));
        sqlInserir.setTimestamp(++i, Uteis.getDataJDBCTimestamp(obj.getDataAutorizacao()));
        sqlInserir.setString(++i, obj.getIdEmpresaEnotas());
        sqlInserir.setString(++i, obj.getIdExterno());
        sqlInserir.setString(++i, obj.getIdReferencia());
        sqlInserir.setString(++i, obj.getJsonNota());
        sqlInserir.setString(++i, obj.getJsonEnvio());
        sqlInserir.setString(++i, obj.getJsonRetorno());
        sqlInserir.setString(++i, obj.getJsonEnvioCancelamento());
        sqlInserir.setString(++i, obj.getJsonRetornoCancelamento());
        sqlInserir.setString(++i, obj.getJsonEnvioInutilizar());
        sqlInserir.setString(++i, obj.getJsonRetornoInutilizar());
        sqlInserir.setString(++i, obj.getStatusNota());
        sqlInserir.setString(++i, obj.getNomeCliente());
        sqlInserir.setString(++i, obj.getRazaoSocial());
        sqlInserir.setString(++i, obj.getCpfCnpj());
        sqlInserir.setString(++i, obj.getNumeroNota());
        sqlInserir.setString(++i, obj.getChaveAcesso());
        sqlInserir.setInt(++i, obj.getTipo().getCodigo());
        resolveIntegerNull(sqlInserir, ++i, obj.getEmpresaVO().getCodigo());
        resolveIntegerNull(sqlInserir, ++i, obj.getUsuarioVO().getCodigo());
        resolveIntegerNull(sqlInserir, ++i, obj.getPessoaVO().getCodigo());
        resolveIntegerNull(sqlInserir, ++i, obj.getConfiguracaoNotaFiscalVO().getCodigo());
        resolveIntegerNull(sqlInserir, ++i, obj.getNfSeEmitidaVO().getCodigo());
        resolveIntegerNull(sqlInserir, ++i, obj.getNotaFiscalConsumidorEletronicaVO().getCodigo());
        resolveIntegerNull(sqlInserir, ++i, obj.getSequencialfamilia());
        sqlInserir.setBoolean(++i, obj.isExcluido());
        sqlInserir.setDouble(++i, obj.getValor());
        resolveIntegerNull(sqlInserir, ++i, obj.getNotaFiscalAnterior());
        resolveIntegerNull(sqlInserir, ++i, obj.getNotaFiscalNova());
        sqlInserir.setString(++i, obj.getIdPacto());

        sqlInserir.execute();
        obj.setCodigo(obterValorChavePrimariaCodigo());
        obj.setNovoObj(false);

        NotaFiscalHistorico histDAO = new NotaFiscalHistorico(con);
        histDAO.gerarHistoricoSemCommit(obj.getStatusNota(), "INCLUSÃO", obj.getIdPacto(), obj.getCodigo(), obj.getUsuarioVO());
        histDAO = null;
    }


    public void alterar(NotaFiscalVO obj) throws Exception {
        try {
            con.setAutoCommit(false);
            alterarSemCommit(obj);
            con.commit();
        } catch (Exception e) {
            con.rollback();
            con.setAutoCommit(true);
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    public void alterarSemCommit(NotaFiscalVO obj) throws Exception {
        String sql = "UPDATE NotaFiscal SET dataEmissao = ?, dataAutorizacao = ?, idEmpresaEnotas = ?, idExterno = ?, idReferencia = ?, jsonNota = ?, jsonEnvio = ?, jsonRetorno = ?, "
                + "jsonEnvioCancelamento = ?, jsonRetornoCancelamento = ?, jsonEnvioInutilizar = ?, jsonRetornoInutilizar = ?, "
                + "statusNota = ?, nomeCliente = ?, razaoSocial = ?, cpfCNPJ = ?, numeroNota = ?, chaveAcesso = ?, tipo = ?, "
                + "empresa = ?, usuario = ?, pessoa = ?, configuracaonotafiscal = ?, nfseEmitida = ?, notaFiscalConsumidorEletronica = ?, sequencialfamilia = ?, excluido = ?, "
                + "valor = ?, notaFiscalAnterior = ?, notaFiscalNova = ?, idPacto = ? WHERE codigo = ?";
        int i = 0;
        PreparedStatement sqlAlterar = con.prepareStatement(sql);
        sqlAlterar.setTimestamp(++i, Uteis.getDataJDBCTimestamp(obj.getDataEmissao()));
        sqlAlterar.setTimestamp(++i, Uteis.getDataJDBCTimestamp(obj.getDataAutorizacao()));
        sqlAlterar.setString(++i, obj.getIdEmpresaEnotas());
        sqlAlterar.setString(++i, obj.getIdExterno());
        sqlAlterar.setString(++i, obj.getIdReferencia());
        sqlAlterar.setString(++i, obj.getJsonNota());
        sqlAlterar.setString(++i, obj.getJsonEnvio());
        sqlAlterar.setString(++i, obj.getJsonRetorno());
        sqlAlterar.setString(++i, obj.getJsonEnvioCancelamento());
        sqlAlterar.setString(++i, obj.getJsonRetornoCancelamento());
        sqlAlterar.setString(++i, obj.getJsonEnvioInutilizar());
        sqlAlterar.setString(++i, obj.getJsonRetornoInutilizar());
        sqlAlterar.setString(++i, obj.getStatusNota());
        sqlAlterar.setString(++i, obj.getNomeCliente());
        sqlAlterar.setString(++i, obj.getRazaoSocial());
        sqlAlterar.setString(++i, obj.getCpfCnpj());
        sqlAlterar.setString(++i, obj.getNumeroNota());
        sqlAlterar.setString(++i, obj.getChaveAcesso());
        sqlAlterar.setInt(++i, obj.getTipo().getCodigo());
        resolveIntegerNull(sqlAlterar, ++i, obj.getEmpresaVO().getCodigo());
        resolveIntegerNull(sqlAlterar, ++i, obj.getUsuarioVO().getCodigo());
        resolveIntegerNull(sqlAlterar, ++i, obj.getPessoaVO().getCodigo());
        resolveIntegerNull(sqlAlterar, ++i, obj.getConfiguracaoNotaFiscalVO().getCodigo());
        resolveIntegerNull(sqlAlterar, ++i, obj.getNfSeEmitidaVO().getCodigo());
        resolveIntegerNull(sqlAlterar, ++i, obj.getNotaFiscalConsumidorEletronicaVO().getCodigo());
        resolveIntegerNull(sqlAlterar, ++i, obj.getSequencialfamilia());
        sqlAlterar.setBoolean(++i, obj.isExcluido());
        sqlAlterar.setDouble(++i, obj.getValor());
        resolveIntegerNull(sqlAlterar, ++i, obj.getNotaFiscalAnterior());
        resolveIntegerNull(sqlAlterar, ++i, obj.getNotaFiscalNova());
        sqlAlterar.setString(++i, obj.getIdPacto());

        sqlAlterar.setInt(++i, obj.getCodigo());
        sqlAlterar.execute();

        NotaFiscalHistorico histDAO = new NotaFiscalHistorico(con);
        histDAO.gerarHistoricoSemCommit(obj.getStatusNota(), "ALTERAÇÃO", obj.getIdPacto(), obj.getCodigo(), obj.getUsuarioVO());
        histDAO = null;
    }

    public static NotaFiscalVO montarDados(ResultSet rs, int nivelMontarDados, Connection con) throws Exception {
        NotaFiscalVO obj = new NotaFiscalVO();
        obj.setCodigo(rs.getInt("codigo"));
        obj.setNovoObj(false);
        obj.setDataRegistro(rs.getTimestamp("dataRegistro"));
        obj.setDataEmissao(rs.getTimestamp("dataEmissao"));
        obj.setDataAutorizacao(rs.getTimestamp("dataAutorizacao"));
        obj.setIdEmpresaEnotas(rs.getString("idEmpresaEnotas"));
        obj.setIdExterno(rs.getString("idExterno"));
        obj.setIdReferencia(rs.getString("idReferencia"));
        obj.setIdPacto(rs.getString("idPacto"));
        obj.setJsonNota(rs.getString("jsonNota"));
        obj.setJsonEnvio(rs.getString("jsonEnvio"));
        obj.setJsonRetorno(rs.getString("jsonRetorno"));
        obj.setJsonEnvioCancelamento(rs.getString("jsonEnvioCancelamento"));
        obj.setJsonRetornoCancelamento(rs.getString("jsonRetornoCancelamento"));
        obj.setJsonEnvioInutilizar(rs.getString("jsonEnvioInutilizar"));
        obj.setJsonRetornoInutilizar(rs.getString("jsonRetornoInutilizar"));
        obj.setStatusNota(rs.getString("statusNota"));
        obj.setNomeCliente(rs.getString("nomeCliente"));
        obj.setRazaoSocial(rs.getString("razaoSocial"));
        obj.setCpfCnpj(rs.getString("cpfCNPJ"));
        obj.setNumeroNota(rs.getString("numeroNota"));
        obj.setChaveAcesso(rs.getString("chaveAcesso"));
        obj.setTipo(TipoNotaFiscalEnum.obterPorCodigo(rs.getInt("tipo")));
        obj.getPessoaVO().setCodigo(rs.getInt("pessoa"));
        obj.getNotaFiscalConsumidorEletronicaVO().setCodigo(rs.getInt("notaFiscalConsumidorEletronica"));
        obj.setSequencialfamilia(rs.getInt("sequencialfamilia"));
        obj.setExcluido(rs.getBoolean("excluido"));
        obj.setValor(rs.getDouble("valor"));
        obj.setNotaFiscalAnterior(rs.getInt("notaFiscalAnterior"));
        obj.setNotaFiscalNova(rs.getInt("notaFiscalNova"));
        try {
            obj.setMatricula(rs.getInt("matricula"));
            obj.setContrato(rs.getInt("contrato"));
        } catch (Exception ignored) {
        }

        try {
            obj.getUsuarioVO().setNome(rs.getString("usuario_nome"));
        } catch (Exception ignored) {
        }

        if (nivelMontarDados != Uteis.NIVELMONTARDADOS_ADMINISTRATIVORUNNER_PROCESSARNOTAS) {
            obj.setEmpresaVO(getCachedEmpresa(rs.getInt("empresa"), con));
            obj.getUsuarioVO().setCodigo(rs.getInt("usuario"));
            obj.getConfiguracaoNotaFiscalVO().setCodigo(rs.getInt("configuracaonotafiscal"));
            obj.getNfSeEmitidaVO().setCodigo(rs.getInt("nfseEmitida"));
            montarDadosEmpresa(obj, con, obj.getEmpresaVO().getCodigo());
        }

        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_DADOSBASICOS) {
            return obj;
        }

        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_DADOSENTIDADESPRINCIPAIS) {
            montarDadosConfiguracaoNotaFiscal(obj, Uteis.NIVELMONTARDADOS_MINIMOS, con, obj.getConfiguracaoNotaFiscalVO().getCodigo());
            return obj;
        }

        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_ADMINISTRATIVORUNNER_PROCESSARNOTAS) {
            montarDadosUsuario(obj, con, rs.getInt("usuario"));
            montarDadosEmpresa(obj, con, rs.getInt("empresa"));

            obj.setNfSeEmitidaVO(new NFSeEmitidaVO());
            obj.getNfSeEmitidaVO().setCodigo(rs.getInt("nfseEmitida"));

            montarDadosConfiguracaoNotaFiscal(obj, Uteis.NIVELMONTARDADOS_MINIMOS, con, rs.getInt("configuracaonotafiscal"));
            return obj;
        }

        return obj;
    }

    private static void montarDadosUsuario(NotaFiscalVO obj, Connection con, Integer idUsuario) throws Exception {
        if (UteisValidacao.emptyNumber(idUsuario)) {
            obj.setUsuarioVO(new UsuarioVO());
            return;
        }
        Usuario usuarioDao = new Usuario(con);
        obj.setUsuarioVO(usuarioDao.consultarPorChavePrimaria(idUsuario, Uteis.NIVELMONTARDADOS_TODOS));
        usuarioDao = null;
    }

    private static void montarDadosEmpresa(NotaFiscalVO obj, Connection con, Integer idEmpresa) throws Exception {
        if (UteisValidacao.emptyNumber(idEmpresa)) {
            obj.setEmpresaVO(new EmpresaVO());
            return;
        }
        Empresa empresa = new Empresa(con);
        obj.setEmpresaVO(empresa.consultarPorChavePrimaria(idEmpresa, false, Uteis.NIVELMONTARDADOS_TODOS));
        empresa = null;
    }

    private static void montarDadosConfiguracaoNotaFiscal(NotaFiscalVO obj, int nivelMontarDados, Connection con, Integer idConfiguracaoNotaFiscal) throws Exception {
        if (idConfiguracaoNotaFiscal == 0) {
            obj.setConfiguracaoNotaFiscalVO(new ConfiguracaoNotaFiscalVO());
            return;
        }

        ConfiguracaoNotaFiscal configDAO = new ConfiguracaoNotaFiscal(con);
        obj.setConfiguracaoNotaFiscalVO(configDAO.consultarPorChavePrimaria(idConfiguracaoNotaFiscal, nivelMontarDados));
        configDAO = null;
    }

    public void marcarExcluido(boolean excluido, Integer codNotaFiscal, UsuarioVO usuarioVO) throws Exception {
        String sql = "update NotaFiscal set excluido = ? WHERE codigo = ?";
        PreparedStatement sqlExcluir = con.prepareStatement(sql);
        sqlExcluir.setBoolean(1, excluido);
        sqlExcluir.setInt(2, codNotaFiscal);
        sqlExcluir.execute();

        NotaFiscalHistorico histDAO = new NotaFiscalHistorico(con);
        histDAO.gerarHistoricoSemCommit("EXCLUIR", "", "", codNotaFiscal, usuarioVO);
        histDAO = null;
    }

    public static List<NotaFiscalVO> montarDadosConsulta(ResultSet rs, int nivelMontarDados, Connection con) throws Exception {
        List<NotaFiscalVO> vetResultado = new ArrayList<NotaFiscalVO>();
        while (rs.next()) {
            NotaFiscalVO obj = montarDados(rs, nivelMontarDados, con);
            vetResultado.add(obj);
        }
        return vetResultado;
    }

    public NotaFiscalVO consultarPorChavePrimaria(Integer codigo, int nivelMontarDados) throws Exception {
        String sql = "SELECT * FROM NotaFiscal WHERE codigo = ?";
        PreparedStatement sqlConsultar = con.prepareStatement(sql);
        sqlConsultar.setInt(1, codigo);
        ResultSet rs = sqlConsultar.executeQuery();
        if (!rs.next()) {
            return new NotaFiscalVO();
        }
        return montarDados(rs, nivelMontarDados, con);
    }

    public NotaFiscalVO consultarPorChavePrimariaGestaoNotas(Integer codigo) throws Exception {
        String sql = "SELECT codigo,jsonRetorno,statusNota,tipo,nfseEmitida,notaFiscalConsumidorEletronica FROM NotaFiscal WHERE codigo = ?";
        PreparedStatement sqlConsultar = con.prepareStatement(sql);
        sqlConsultar.setInt(1, codigo);
        ResultSet rs = sqlConsultar.executeQuery();
        if (rs.next()) {
            NotaFiscalVO obj = new NotaFiscalVO();
            obj.setCodigo(rs.getInt("codigo"));
            obj.setNovoObj(false);
            obj.setJsonRetorno(rs.getString("jsonRetorno"));
            obj.setStatusNota(rs.getString("statusNota"));
            obj.setTipo(TipoNotaFiscalEnum.obterPorCodigo(rs.getInt("tipo")));
            obj.getNfSeEmitidaVO().setCodigo(rs.getInt("nfseEmitida"));
            obj.getNotaFiscalConsumidorEletronicaVO().setCodigo(rs.getInt("notaFiscalConsumidorEletronica"));
            return obj;
        }
        return new NotaFiscalVO();
    }

    public NotaFiscalVO consultarPorNFSeEmitida(Integer nfseEmitida, int nivelMontarDados) throws Exception {
        String sql = "SELECT * FROM NotaFiscal WHERE nfseemitida = ? order by codigo desc limit 1";
        PreparedStatement sqlConsultar = con.prepareStatement(sql);
        sqlConsultar.setInt(1, nfseEmitida);
        ResultSet rs = sqlConsultar.executeQuery();
        if (!rs.next()) {
            return new NotaFiscalVO();
        }
        return montarDados(rs, nivelMontarDados, con);
    }

    public NotaFiscalVO consultarPorNotaFiscalConsumidorEletronica(Integer notafiscalconsumidoreletronica, int nivelMontarDados) throws Exception {
        String sql = "SELECT * FROM NotaFiscal WHERE notafiscalconsumidoreletronica = ? order by codigo desc limit 1";
        PreparedStatement sqlConsultar = con.prepareStatement(sql);
        sqlConsultar.setInt(1, notafiscalconsumidoreletronica);
        ResultSet rs = sqlConsultar.executeQuery();
        if (!rs.next()) {
            return new NotaFiscalVO();
        }
        return montarDados(rs, nivelMontarDados, con);
    }

    public NotaFiscalVO consultarPorSequencialFamilia(Integer sequencialFamilia, int nivelMontarDados) throws Exception {
        String sql = "SELECT * FROM NotaFiscal WHERE sequencialFamilia = ?";
        PreparedStatement sqlConsultar = con.prepareStatement(sql);
        sqlConsultar.setInt(1, sequencialFamilia);
        ResultSet rs = sqlConsultar.executeQuery();
        if (!rs.next()) {
            return new NotaFiscalVO();
        }
        return montarDados(rs, nivelMontarDados, con);
    }

    public void processarRetornoErroNaEmissao(String jsonRetorno, Integer codNotaFiscal) throws Exception {
        try {
            con.setAutoCommit(false);

            String statusNota = StatusEnotasEnum.ERRO.getDescricaoEnotas().toUpperCase();

            PreparedStatement update = con.prepareStatement("UPDATE NotaFiscal SET jsonRetorno  = ?, statusNota  = ? WHERE codigo = ?");
            update.setString(1, jsonRetorno);
            update.setString(2, statusNota);
            update.setInt(3, codNotaFiscal);
            update.execute();

            NotaFiscalHistorico histDAO = new NotaFiscalHistorico(con);
            histDAO.gerarHistoricoSemCommit(statusNota, "ERRO EMISSÃO", "", codNotaFiscal, null);
            histDAO = null;

            Integer codNFSeEmitida = 0;
            Integer codNotaFiscalConsumidor = 0;
            Integer sequencialFamilia = 0;
            String sqlStr = "select nfseemitida, notafiscalconsumidoreletronica, sequencialFamilia from notafiscal where codigo = " + codNotaFiscal;
            Statement stm = con.createStatement();
            ResultSet rs = stm.executeQuery(sqlStr);
            if (rs.next()) {
                codNFSeEmitida = rs.getInt("nfseemitida");
                codNotaFiscalConsumidor = rs.getInt("notafiscalconsumidoreletronica");
                sequencialFamilia = rs.getInt("sequencialFamilia");
            }

            if (!UteisValidacao.emptyNumber(sequencialFamilia)) {
                NFSeEmitida nfSeEmitidaDAO = new NFSeEmitida(con);
                nfSeEmitidaDAO.atualizarSituacaoPorSequencialFamilia(SituacaoNotaFiscalEnum.NAO_PROCESSADA, sequencialFamilia);
                nfSeEmitidaDAO = null;
            }

            if (!UteisValidacao.emptyNumber(codNFSeEmitida)) {
                NFSeEmitida nfSeEmitidaDAO = new NFSeEmitida(con);
                nfSeEmitidaDAO.atualizarSituacao(SituacaoNotaFiscalEnum.NAO_PROCESSADA, codNFSeEmitida);
                nfSeEmitidaDAO = null;
            }

            if (!UteisValidacao.emptyNumber(codNotaFiscalConsumidor)) {
                NotaFiscalConsumidorEletronica notaDAO = new NotaFiscalConsumidorEletronica(con);
                notaDAO.atualizarSituacao(SituacaoNotaFiscalEnum.NAO_PROCESSADA, codNotaFiscalConsumidor);
                notaDAO = null;
            }

            con.commit();
        } catch (Exception e) {
            e.printStackTrace();
            con.rollback();
            con.setAutoCommit(true);
            throw e;
        } finally {
            con.setAutoCommit(true);
        }

    }

    public void registrarIdExterno(String idExterno, Integer codNotaFiscal) throws SQLException {
        try (PreparedStatement update = con.prepareStatement("update notafiscal SET idExterno = ? WHERE codigo = ? ")) {
            int i = 0;
            update.setString(++i, idExterno);
            update.setInt(++i, codNotaFiscal);
            update.execute();
        }
    }

    public void processarRetornoWebHookEnotas(String jsonRetorno, String idPacto) throws Exception {

        //retorno ENOTAS webHook
        // https://portal.enotasgw.com.br/knowledge-base/webhooks-e-status-da-nota-fiscal/#tab-con-2

//        {
//  "empresaId": "string",
//  "nfeId": "string",
//  "nfeIdExterno": "string",
//  "nfeStatus": "string",
//  "nfeMotivoStatus": "string",
//  "nfeLinkPdf": "string",
//  "nfeLinkXml": "string",
//  "nfeNumero": "string",
//  "nfeCodigoVerificacao": "string",
//  "nfeNumeroRps": "string",
//  "nfeSerieRps": "string",
//  "nfeDataCompetencia" : date
//}

        //FAZER AQUI TODA A VALIDAÇÃO DO RETORNO DO ENOTAS

        Integer codNotaFiscal = obterCodigoPorIdPacto(idPacto);

        JSONObject jsonObject = new JSONObject(jsonRetorno);
        String statusnota = jsonObject.getString("nfeStatus");
        String nfeId = jsonObject.getString("nfeId");
//            String nfeSerieRps = jsonObject.getString("nfeSerieRps");
//            String nfeNumeroRps = jsonObject.getString("nfeNumeroRps");
        String nfeCodigoVerificacao = "";

        Date dataAutorizacao = null;

        try {
            nfeCodigoVerificacao = jsonObject.getString("nfeCodigoVerificacao");
        } catch (Exception ignored) {
        }

        String nfeNumero = "";
        try {
            nfeNumero = jsonObject.getString("nfeNumero");
        } catch (Exception ignored) {
        }

        try {
            final SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss");
            sdf.setTimeZone(TimeZone.getTimeZone("UTC"));
            dataAutorizacao = sdf.parse(jsonObject.getString("nfeDataAutorizacao"));
        } catch (Exception ex) {
            dataAutorizacao = null;
        }

        try {
            String tipo = jsonObject.getString("tipo");
            if (tipo.equals(TipoNotaFiscalEnum.NFCE.getDescricao())) {

                try {
                    nfeCodigoVerificacao = jsonObject.getString("nfeChaveAcesso");
                } catch (Exception ignored) {
                }

                if (statusnota.toUpperCase().equals(StatusEnotasEnum.CANCELAMENTONEGADO.getDescricaoEnotas().toUpperCase())) {

                    atualizarDadosNotaFiscal(codNotaFiscal, "WEBHOOK", statusnota, nfeId, idPacto, nfeCodigoVerificacao, nfeNumero, dataAutorizacao, jsonRetorno, null, null);

                    //CANCELAMENTO FOI NEGADO VOLTA PARA AUTORIZADA!
                    statusnota = StatusEnotasEnum.AUTORIZADA.getDescricaoEnotas();
                }
            }
        } catch (Exception ignored) {
        }

        atualizarDadosNotaFiscal(codNotaFiscal, "WEBHOOK", statusnota, nfeId, idPacto, nfeCodigoVerificacao, nfeNumero, dataAutorizacao, jsonRetorno, null, null);
    }

    private void atualizarDadosNotaFiscal(Integer codNotaFiscal,
                                          String descricaoAtualizacao,
                                          String statusNota,
                                          String idExterno,
                                          String idPacto,
                                          String chaveAcesso,
                                          String numeroNota,
                                          Date dataAutorizacao,
                                          String jsonRetorno,
                                          String incluirNotaFiscalWebHookHist,
                                          UsuarioVO usuarioVO) throws Exception {
        try {
            con.setAutoCommit(false);

            if (UteisValidacao.emptyString(idExterno)) {
                throw new Exception("idExterno obrigatório para atualização.");
            }

            StringBuilder sql = new StringBuilder();
            sql.append("UPDATE NotaFiscal SET idExterno = ?");

            if (!UteisValidacao.emptyString(jsonRetorno)) {
                sql.append(", jsonRetorno = ? ");
            }

            if (!UteisValidacao.emptyString(statusNota)) {
                sql.append(", statusnota = ? ");
            }

            if (!UteisValidacao.emptyString(numeroNota)) {
                sql.append(", numeronota  = ? ");
            }

            if (!UteisValidacao.emptyString(chaveAcesso)) {
                sql.append(", chaveAcesso  = ? ");
            }

            if (dataAutorizacao != null) {
                sql.append(", dataAutorizacao  = ? ");
            }

            sql.append(" WHERE codigo = ? ");

            PreparedStatement update = con.prepareStatement(sql.toString());
            int i = 0;
            update.setString(++i, idExterno);

            if (!UteisValidacao.emptyString(jsonRetorno)) {
                update.setString(++i, jsonRetorno);
            }

            if (!UteisValidacao.emptyString(statusNota)) {
                update.setString(++i, statusNota);
            }

            if (!UteisValidacao.emptyString(numeroNota)) {
                update.setString(++i, numeroNota);
            }

            if (!UteisValidacao.emptyString(chaveAcesso)) {
                update.setString(++i, chaveAcesso);
            }

            if (dataAutorizacao != null) {
                update.setTimestamp(++i, Uteis.getDataJDBCTimestamp(dataAutorizacao));
            }

            update.setInt(++i, codNotaFiscal);
            update.execute();

            NotaFiscalHistorico histDAO = new NotaFiscalHistorico(con);
            histDAO.gerarHistoricoSemCommit(statusNota, descricaoAtualizacao, idPacto, codNotaFiscal, usuarioVO);
            histDAO = null;

            if (!UteisValidacao.emptyString(incluirNotaFiscalWebHookHist)) {
                incluirNotaFiscalWebHookHistorico(incluirNotaFiscalWebHookHist, "");
            }

            con.commit();
        } catch (Exception e) {
            con.rollback();
            con.setAutoCommit(true);
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    public void atualizarDadosNotaFiscalEnotas(NotaFiscalVO obj, NotaEnotasTO notaEnotasTO, UsuarioVO usuarioVO) throws Exception {

        //verificar se os dados estão certo...
//        idexterno com idReferemcia e codNotaFiscal;

        Integer codNotaFiscal = obj.getCodigo();
        String statusNota = notaEnotasTO.getStatus();
        String idExterno = notaEnotasTO.getIdExterno();
        String idPacto = notaEnotasTO.getIdReferencia();
        String chaveAcesso = notaEnotasTO.getChaveAcesso();
        String numeroNota = notaEnotasTO.getNumero();
        String json = notaEnotasTO.getJson();
        Date dataAutorizacao = notaEnotasTO.getDataAutorizacao();

        atualizarDadosNotaFiscal(codNotaFiscal, "SINCRONIZAÇÃO MANUAL", statusNota, idExterno, idPacto, chaveAcesso, numeroNota, dataAutorizacao, json, json, usuarioVO);
    }

    private void validarInformacoesEnvioDeNota(TipoNotaFiscalEnum tipoNotaFiscalEnum, EmpresaVO empresaVO) throws Exception {

        if (tipoNotaFiscalEnum.equals(TipoNotaFiscalEnum.NFSE) || tipoNotaFiscalEnum.equals(TipoNotaFiscalEnum.NFE)) {
            //VALIDAR INFORMACOES DE NFSE

            if (!empresaVO.getUsarNFSe()) {
                throw new Exception("Empresa não configurada para gerar " + tipoNotaFiscalEnum.getDescricao());
            }

            if (UteisValidacao.emptyNumber(empresaVO.getConfiguracaoNotaFiscalNFSe().getCodigo())) {
                throw new Exception("Empresa não tem configuração de nota fiscal configurada (Configurações da Empresa)!");
            }


        } else if (tipoNotaFiscalEnum.equals(TipoNotaFiscalEnum.NFCE)) {
            //VALIDAR INFORMACOES DE NFCE

            if (!empresaVO.isUsarNFCe()) {
                throw new Exception("Empresa não configurada para gerar " + tipoNotaFiscalEnum.getDescricao());
            }
            if (UteisValidacao.emptyNumber(empresaVO.getConfiguracaoNotaFiscalNFCe().getCodigo())) {
                throw new Exception("Empresa não tem configuração de nota fiscal configurada (Configurações da Empresa)!");
            }
        }
    }

    private FormaPagamentoVO obterFormaPagamentoVO(Integer codFormaPagamento) throws Exception {
        FormaPagamento formaPagamentoDAO = new FormaPagamento(con);
        FormaPagamentoVO formaPagamentoVO = formaPagamentoDAO.consultarPorChavePrimaria(codFormaPagamento, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        formaPagamentoDAO = null;
        return formaPagamentoVO;
    }

    private EmpresaVO obterEmpresaVO(Integer codEmpresa) throws Exception {
        Empresa empresaDAO = new Empresa(con);
        EmpresaVO empresaVO = empresaDAO.consultarPorChavePrimaria(codEmpresa, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        empresaDAO = null;
        return empresaVO;
    }

    private ConfiguracaoNotaFiscalVO obterConfiguracaoNotaFiscalVO(Integer codConfiguracaoNotaFiscal) throws Exception {
        ConfiguracaoNotaFiscal configuracaoNotaFiscalDAO = new ConfiguracaoNotaFiscal(con);
        ConfiguracaoNotaFiscalVO configuracaoNotaFiscalVO = configuracaoNotaFiscalDAO.consultarPorChavePrimaria(codConfiguracaoNotaFiscal, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        configuracaoNotaFiscalDAO = null;
        return configuracaoNotaFiscalVO;
    }

    private void processarMapaNotaEmitir(Map<Integer, NotaEmitirTO> mapaNotas, EmpresaVO empresaVO,
                                         PessoaVO pessoaVO, FornecedorVO fornecedorVO, TipoNotaFiscalEnum tipoNotaFiscalEnum,
                                         String produtosPagos, Double valorBase, TipoFormaPagto tipoFormaPagto, FormaPagamentoVO formaPagamentoVO,
                                         MovProdutoVO movProdutoEmitirVO, MovContaVO movContaVO, String nomePagador, MovPagamentoVO movPagamentoVO) throws Exception {

        String tipoProdutoEmissao = obterTipoProdutoEmissao(empresaVO.getCodigo(), tipoNotaFiscalEnum);

        TipoRelatorioDF tipoGestaoNFSe = TipoRelatorioDF.getTipoRelatorioDF(empresaVO.getTipoGestaoNFSe());
        boolean ignorarMovPagamento = (tipoGestaoNFSe.equals(TipoRelatorioDF.COMPETENCIA_INDEPENDENTE_QUITACAO) || tipoGestaoNFSe.equals(TipoRelatorioDF.COMPETENCIA))
                && empresaVO.isEmiteValorTotalCompetencia();

        //EMISSÃO DE UM MOVPRODUTO - COMPETENCIA
        if (movProdutoEmitirVO != null && !UteisValidacao.emptyNumber(movProdutoEmitirVO.getCodigo())) {
            MovProduto movProdutoDAO = new MovProduto(con);
            movProdutoEmitirVO = movProdutoDAO.consultarPorChavePrimaria(movProdutoEmitirVO.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            movProdutoDAO = null;

            ConfiguracaoNotaFiscalVO configuracaoNotaFiscalVO = obterConfiguracaoNotaFiscal(tipoNotaFiscalEnum, movProdutoEmitirVO, null);

            if (tipoProdutoEmissao.contains(movProdutoEmitirVO.getProduto().getTipoProduto())) {

                NotaEmitirTO notaEmitirTO = obterNotaEmitir(mapaNotas, configuracaoNotaFiscalVO, pessoaVO, fornecedorVO, nomePagador);
                List<NotaEmitirFormaPagamentoTO> listaPagProduto = obterPagamentoMovProduto(movProdutoEmitirVO.getCodigo());

//              caso a empresa emita por competência ou competência independente da quitação, e esteja coonfigurada para emitir o valor total da nota,
//              desconsiderar os pagamentos e emitir a partir dos movProdutos(M2-258)
                if (ignorarMovPagamento || UteisValidacao.emptyNumber(listaPagProduto.size())  ) {
                    adicionarItem(notaEmitirTO, movProdutoEmitirVO, Uteis.arredondarForcando2CasasDecimais(movProdutoEmitirVO.getTotalFinal()), null);
                    mapaNotas.put(configuracaoNotaFiscalVO.getCodigo(), notaEmitirTO);
                } else {
                    Double valorPagoProduto = 0.0;
                    for (NotaEmitirFormaPagamentoTO pagTO : listaPagProduto) {
                        valorPagoProduto += pagTO.getValor();
                    }

                    adicionarItem(notaEmitirTO, movProdutoEmitirVO, Uteis.arredondarForcando2CasasDecimais(valorPagoProduto), null);
                    notaEmitirTO.getFormasPagamento().addAll(listaPagProduto);
                    mapaNotas.put(configuracaoNotaFiscalVO.getCodigo(), notaEmitirTO);
                }
            }
            return;
        }

        //EMISSÃO DE MOVCONTA - CONTA DO FINANCEIRO
        if (movContaVO != null) {
            ConfiguracaoNotaFiscalVO configuracaoNotaFiscalVO = obterConfiguracaoNotaFiscal(tipoNotaFiscalEnum, null, movContaVO);

            NotaEmitirTO notaEmitirTO = obterNotaEmitir(mapaNotas, configuracaoNotaFiscalVO, pessoaVO, fornecedorVO, nomePagador);

            adicionarItem(notaEmitirTO, movProdutoEmitirVO, movContaVO.getValor(), movContaVO);
            notaEmitirTO.setObservacao(movContaVO.getObservacoes());
            notaEmitirTO.getFormasPagamento().addAll(obterPagamentoMovConta(movContaVO));
            notaEmitirTO.setDescricao(movContaVO.getDescricao());
            mapaNotas.put(configuracaoNotaFiscalVO.getCodigo(), notaEmitirTO);
            return;
        }

        //EMISSÃO DE CONTA CORRENTE - NÃO TEM PRODUTOS PAGOS PREENCHIDO
        if (empresaVO.isGerarNFSeContaCorrente() && UteisValidacao.emptyString(produtosPagos)) {
            Integer codConfigEmissao = empresaVO.getConfiguracaoNotaFiscalNFSe().getCodigo();
            ConfiguracaoNotaFiscalVO configuracaoNotaFiscalVO = obterConfiguracaoNotaFiscalVO(codConfigEmissao);

            NotaEmitirTO notaEmitirTO = obterNotaEmitir(mapaNotas, configuracaoNotaFiscalVO, pessoaVO, fornecedorVO, nomePagador);
            notaEmitirTO.setValorNota(Uteis.arredondarForcando2CasasDecimais(valorBase));
            notaEmitirTO.setDescricao("PLANO");

            NotaEmitirProdutoTO produtoTO = new NotaEmitirProdutoTO();
            produtoTO.setQuantidade(1);
            produtoTO.setValorUnitario(Uteis.arredondarForcando2CasasDecimais(valorBase));
            produtoTO.setDescricao("PLANO");
            notaEmitirTO.getProdutos().put(formaPagamentoVO.getCodigo().toString(), produtoTO);

            notaEmitirTO.getFormasPagamento().add(gerarFormaPagamentoNota(formaPagamentoVO.getCodigo(), valorBase, tipoFormaPagto));
            mapaNotas.put(configuracaoNotaFiscalVO.getCodigo(), notaEmitirTO);
            return;
        }

        //CASO NÃO TENHA PRODUTOS PAGOS PREENCHIDO ENTÃO NÃO DEVE FAZER MAIS NADA!
        if (UteisValidacao.emptyString(produtosPagos)) {
            return;
        }

        boolean isValorTotalPreenchido = false;

        //EMISSÃO É REALIZADA DE ACORDO COM OS PRODUTOS PAGOS!
        String[] produtos = produtosPagos.split("\\|");
        for (String prod : produtos) {
            if (UteisValidacao.emptyString(prod) || prod.equals("null")) {
                continue;
            }
            String[] split = prod.split(",");
            Integer codigoMovProduto = Integer.valueOf(split[0]);

            Double valorPagoProduto = 0.0;

            if (TipoRelatorioDF
                    .getTipoRelatorioDF(empresaVO.getTipoGestaoNFSe())
                    .equals(TipoRelatorioDF.FATURAMENTO_DE_CAIXA) &&
                    empresaVO.isEmiteValorTotalFaturamento()) {
                if (!isValorTotalPreenchido) {
                    valorPagoProduto = valorBase;
                    isValorTotalPreenchido = true;
                }
            } else {
                valorPagoProduto = Double.valueOf(split[3]);
            }

            if (UteisValidacao.emptyNumber(valorPagoProduto)) {
                continue;
            }

            MovProduto movProdutoDAO = new MovProduto(con);
            MovProdutoVO movProdutoVO = movProdutoDAO.consultarPorChavePrimaria(codigoMovProduto, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            movProdutoDAO = null;
            ConfiguracaoNotaFiscalVO configuracaoNotaFiscalVO = obterConfiguracaoNotaFiscal(tipoNotaFiscalEnum, movProdutoVO, null);

            if (tipoProdutoEmissao.contains(movProdutoVO.getProduto().getTipoProduto())) {

                Produto produtoDAO = new Produto(con);
                ProdutoVO produtoVO = produtoDAO.consultarPorChavePrimaria(movProdutoVO.getProduto().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                produtoDAO = null;
                movProdutoVO.setProduto(produtoVO);

                NotaEmitirTO notaEmitirTO = obterNotaEmitir(mapaNotas, configuracaoNotaFiscalVO, pessoaVO, fornecedorVO, nomePagador);
                if (movPagamentoVO != null && !UteisValidacao.emptyNumber(movPagamentoVO.getCodigo())) {
                    notaEmitirTO.getListaMovPagamento().add(movPagamentoVO.getCodigo());
                }

                notaEmitirTO.setEmpresaVO(empresaVO);
                adicionarItem(notaEmitirTO, movProdutoVO, Uteis.arredondarForcando2CasasDecimais(valorPagoProduto), null);
                notaEmitirTO.getFormasPagamento().add(gerarFormaPagamentoNota(formaPagamentoVO.getCodigo(), Uteis.arredondarForcando2CasasDecimais(valorPagoProduto), tipoFormaPagto));
                mapaNotas.put(configuracaoNotaFiscalVO.getCodigo(), notaEmitirTO);
            }
        }
    }

    private ConfiguracaoNotaFiscalVO obterConfiguracaoNotaFiscal(TipoNotaFiscalEnum tipoNotaFiscalEnum, MovProdutoVO movProdutoVO, MovContaVO movContaVO) throws Exception {
        Integer codConfigEmissao = 0;

        if (movContaVO != null && !UteisValidacao.emptyNumber(movContaVO.getCodigo())) {

            EmpresaVO empresaVO = obterEmpresaVO(movContaVO.getEmpresaVO().getCodigo());
            if (tipoNotaFiscalEnum.equals(TipoNotaFiscalEnum.NFSE) || tipoNotaFiscalEnum.equals(TipoNotaFiscalEnum.NFE)) {
                codConfigEmissao = empresaVO.getConfiguracaoNotaFiscalNFSe().getCodigo();
            } else if (tipoNotaFiscalEnum.equals(TipoNotaFiscalEnum.NFCE)) {
                codConfigEmissao = empresaVO.getConfiguracaoNotaFiscalNFCe().getCodigo();
            }

        } else if (emitirConfigNotaProdutoBasePlano(movProdutoVO,tipoNotaFiscalEnum)) {
            Produto produtoDAO = new Produto(con);
            ProdutoVO produtoVO = produtoDAO.consultarPorChavePrimaria(movProdutoVO.getContrato().getPlano().getProdutoPadraoGerarParcelasContrato().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            produtoDAO = null;
            codConfigEmissao = produtoVO.getConfiguracaoNotaFiscalNFSe().getCodigo();

            codConfigEmissao = validarConfigNotaFiscal(tipoNotaFiscalEnum, movProdutoVO, codConfigEmissao);

        } else {

            Produto produtoDAO = new Produto(con);
            ProdutoVO produtoVO = produtoDAO.consultarPorChavePrimaria(movProdutoVO.getProduto().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            produtoDAO = null;
            movProdutoVO.setProduto(produtoVO);


            if (tipoNotaFiscalEnum.equals(TipoNotaFiscalEnum.NFSE) || tipoNotaFiscalEnum.equals(TipoNotaFiscalEnum.NFE)) {
                codConfigEmissao = movProdutoVO.getProduto().getConfiguracaoNotaFiscalNFSe().getCodigo();
            } else if (tipoNotaFiscalEnum.equals(TipoNotaFiscalEnum.NFCE)) {
                codConfigEmissao = movProdutoVO.getProduto().getConfiguracaoNotaFiscalNFCe().getCodigo();
            }

            codConfigEmissao = validarConfigNotaFiscal(tipoNotaFiscalEnum, movProdutoVO, codConfigEmissao);


        }

        if (UteisValidacao.emptyNumber(codConfigEmissao)) {
            throw new Exception("Não foi encontrada configuração de emissão de nota fiscal");
        }

        ConfiguracaoNotaFiscalVO configNotaVO = obterConfiguracaoNotaFiscalVO(codConfigEmissao);
        if (!configNotaVO.isAtivo()) {
            throw new Exception("Configuração de emissão de nota fiscal \"" + configNotaVO.getDescricao() + "\" não está ativa!");
        }

        return configNotaVO;
    }

    private Integer obterConfigEmissaoEmpresa(TipoNotaFiscalEnum tipoNotaFiscalEnum, Integer empresa) throws Exception {
        EmpresaVO empresaVO = obterEmpresaVO(empresa);

        Integer codConfigEmissao = 0;
        if (tipoNotaFiscalEnum.equals(TipoNotaFiscalEnum.NFSE) || tipoNotaFiscalEnum.equals(TipoNotaFiscalEnum.NFE)) {
            codConfigEmissao = empresaVO.getConfiguracaoNotaFiscalNFSe().getCodigo();
        } else if (tipoNotaFiscalEnum.equals(TipoNotaFiscalEnum.NFCE)) {
            codConfigEmissao = empresaVO.getConfiguracaoNotaFiscalNFCe().getCodigo();
        }
        return codConfigEmissao;
    }

    private NotaEmitirTO obterNotaEmitir(Map<Integer, NotaEmitirTO> mapaNotas, ConfiguracaoNotaFiscalVO configNotaFiscalVO,
                                         PessoaVO pessoaVO, FornecedorVO fornecedorVO, String nomePagador) throws Exception {
        NotaEmitirTO notaEmitirTO = mapaNotas.get(configNotaFiscalVO.getCodigo());
        if (notaEmitirTO == null) {
            notaEmitirTO = new NotaEmitirTO();
            notaEmitirTO.setConfiguracaoNotaFiscalVO(obterConfiguracaoNotaFiscalVO(configNotaFiscalVO.getCodigo()));
            notaEmitirTO.setPessoaVO(pessoaVO);
            notaEmitirTO.setNomePagador(nomePagador);
            notaEmitirTO.setFornecedorVO(fornecedorVO);
        }
        return notaEmitirTO;
    }

    private NotaEmitirFormaPagamentoTO gerarFormaPagamentoNota(Integer codFormaPagamento, Double valor, TipoFormaPagto tipoFormaPagto) throws Exception {
        FormaPagamentoVO formaPagamentoVO = obterFormaPagamentoVO(codFormaPagamento);

        NotaEmitirFormaPagamentoTO novo = new NotaEmitirFormaPagamentoTO();
        novo.setFormaPagamento(formaPagamentoVO);
        novo.setSiglaTipoFormaPagamento(tipoFormaPagto.getSigla());
        novo.setValor(Uteis.arredondarForcando2CasasDecimais(valor));
        return novo;
    }

    private List<NotaEmitirFormaPagamentoTO> obterPagamentoMovConta(MovContaVO movContaVO) {
        List<NotaEmitirFormaPagamentoTO> listaFormaPagamento = new ArrayList<NotaEmitirFormaPagamentoTO>();
        try {

            String sqlStr = "select * from movcontarateio where movconta = " + movContaVO.getCodigo();
            Statement stm = con.createStatement();
            ResultSet rs = stm.executeQuery(sqlStr);
            while (rs.next()) {
                Integer codformaPagamento = rs.getInt("formapagamento");
                Double valorPago = rs.getDouble("valor");

                FormaPagamentoVO formaPagamentoVO = obterFormaPagamentoVO(codformaPagamento);

                NotaEmitirFormaPagamentoTO novo = new NotaEmitirFormaPagamentoTO();
                novo.setFormaPagamento(formaPagamentoVO);
                novo.setSiglaTipoFormaPagamento(formaPagamentoVO.getTipoFormaPagamento());
                novo.setValor(valorPago);
                listaFormaPagamento.add(novo);
            }
        } catch (Exception e) {
            listaFormaPagamento = new ArrayList<NotaEmitirFormaPagamentoTO>();
        }
        return listaFormaPagamento;
    }

    private void adicionarItem(NotaEmitirTO notaEmitirTO, MovProdutoVO movProdutoVO, Double valorUnitario, MovContaVO movContaVO) throws Exception {
        boolean empresaUsaDescontoNaNota = notaEmitirTO.getEmpresaVO().isGerarNotaFiscalComDesconto()
                && notaEmitirTO.getConfiguracaoNotaFiscalVO().getTipoNotaFiscal().equals(TipoNotaFiscalEnum.NFCE);

        notaEmitirTO.setValorNota(Uteis.arredondarForcando2CasasDecimais(notaEmitirTO.getValorNota() + valorUnitario));

        if (movContaVO != null) {
            Integer produto;
            EmpresaVO empresaVO = obterEmpresaVO(notaEmitirTO.getConfiguracaoNotaFiscalVO().getEmpresaVO().getCodigo());
            if (notaEmitirTO.getConfiguracaoNotaFiscalVO().getTipoNotaFiscal().equals(TipoNotaFiscalEnum.NFCE)) {
                produto = empresaVO.getProdutoEmissaoNFCeFinanceiro();
            } else {
                produto = empresaVO.getProdutoEmissaoNFSeFinanceiro();
            }

            if (UteisValidacao.emptyNumber(produto)) {
                throw new ConsistirException("Configure um produto para emissão na aba \"NFC-e/NFS-e\" nas configurações da empresa.");
            }

            Produto produtoDAO = new Produto(con);
            ProdutoVO produtoVO = produtoDAO.consultarPorChavePrimaria(produto, Uteis.NIVELMONTARDADOS_DADOSBASICOS);

            MovProdutoVO movProdutoConta = new MovProdutoVO();
            movProdutoConta.setCodigo(movContaVO.getCodigo());
            movProdutoConta.setProduto(produtoVO);

            NotaEmitirProdutoTO produtoTO = new NotaEmitirProdutoTO();
            produtoTO.setQuantidade(1);
            produtoTO.setValorUnitario(movContaVO.getValor());
            produtoTO.setDescricao(movContaVO.getDescricao());
            produtoTO.setMovProdutoVO(movProdutoConta);
            notaEmitirTO.getProdutos().put(movContaVO.getCodigo().toString(), produtoTO);
            return;
        }

        Double residuoValorUnitario = 0.0;
        Double valorUnitarioReal = Uteis.arredondarForcando2CasasDecimais(movProdutoVO.getTotalFinal()/movProdutoVO.getQuantidade());
        Double valorDescontoPorUnidade = movProdutoVO.getValorDesconto() > 0.0 ? Uteis.arredondarForcando2CasasDecimais(movProdutoVO.getValorDesconto() / movProdutoVO.getQuantidade()) : 0.0;
        Integer quantidadeReal = movProdutoVO.getQuantidade();
        if(movProdutoVO.getQuantidade() > 1 && !valorUnitario.equals(movProdutoVO.getTotalFinal())){
            quantidadeReal = (int) (valorUnitario / valorUnitarioReal);
            if(UteisValidacao.notEmptyNumber(quantidadeReal)) {
                residuoValorUnitario = Uteis.arredondarForcando2CasasDecimais(valorUnitario - (valorUnitarioReal * quantidadeReal));
            } else {
                residuoValorUnitario = valorUnitario;
            }
        } else {
            if(valorUnitario < valorUnitarioReal){
                quantidadeReal = 0;
                residuoValorUnitario = valorUnitario;
            }
        }
        if(UteisValidacao.notEmptyNumber(quantidadeReal)) {
            adicionarProduto(notaEmitirTO, movProdutoVO, valorUnitarioReal, quantidadeReal, false, empresaUsaDescontoNaNota, valorDescontoPorUnidade);
        }
        if(residuoValorUnitario > 0.0){
            NotaEmitirProdutoTO produtoTO = notaEmitirTO.getProdutos().get(movProdutoVO.getCodigo().toString() + "R");
            if (produtoTO != null) {
                Double valorSomadoResiduo = Uteis.arredondarForcando2CasasDecimais(produtoTO.getValorUnitario() + residuoValorUnitario);
                if(valorSomadoResiduo >= valorUnitarioReal){
                    adicionarProduto(notaEmitirTO, movProdutoVO, valorUnitarioReal, 1, false, empresaUsaDescontoNaNota, valorDescontoPorUnidade);
                    residuoValorUnitario = valorSomadoResiduo - valorUnitarioReal;
                    notaEmitirTO.getProdutos().remove(movProdutoVO.getCodigo().toString() + "R");
                }
            }
            if(residuoValorUnitario > 0.0){
                adicionarProduto(notaEmitirTO, movProdutoVO, residuoValorUnitario, 1, true, empresaUsaDescontoNaNota, valorDescontoPorUnidade);
            }
        }
    }

    private void adicionarProduto(NotaEmitirTO notaEmitirTO, MovProdutoVO movProdutoVO, Double valorUnitarioReal, Integer quantidadeReal, boolean residuo, boolean empresaUsaDescontoNaNota, Double valorDescontoPorUnidade) {

        NotaEmitirProdutoTO produtoTO = notaEmitirTO.getProdutos().get(movProdutoVO.getCodigo().toString() + (residuo ? "R" : "")); // R ao final é para determinar se é referente ao valor residual
        if (produtoTO != null) {
            if(residuo) {
                produtoTO.setValorUnitario(Uteis.arredondarForcando2CasasDecimais(produtoTO.getValorUnitario() +  valorUnitarioReal));
            } else {
                produtoTO.setQuantidade(produtoTO.getQuantidade() + quantidadeReal);
                produtoTO.setDescontos(Uteis.arredondarForcando2CasasDecimais(valorDescontoPorUnidade * produtoTO.getQuantidade()));
                produtoTO.setMovProdutoVO(movProdutoVO);
            }
        } else {
            produtoTO = new NotaEmitirProdutoTO();
            produtoTO.setDescontos(Uteis.arredondarForcando2CasasDecimais(valorDescontoPorUnidade * quantidadeReal));
            produtoTO.setQuantidade(quantidadeReal);
            produtoTO.setValorUnitario(Uteis.arredondarForcando2CasasDecimais(empresaUsaDescontoNaNota ? valorUnitarioReal + valorDescontoPorUnidade : valorUnitarioReal));
            produtoTO.setDescricao(movProdutoVO.getProduto().getDescricao());
            produtoTO.setMovProdutoVO(movProdutoVO);

        }

        String descricaoMovProduto = (movProdutoVO.getProduto().getTipoProduto().equals("PM") && movProdutoVO.getDescricao().contains("- " + movProdutoVO.getMesReferencia()) ?
                movProdutoVO.getDescricao().substring(0, (movProdutoVO.getDescricao().indexOf("- " + movProdutoVO.getMesReferencia()) - 1)) : movProdutoVO.getDescricao());
        String descricao = notaEmitirTO.getDescricao();
        String meses = "";

        if (notaEmitirTO.getConfiguracaoNotaFiscalVO().isApresentarDuracaoPlano() && movProdutoVO.getContrato_Apresentar() > 0) {
            meses = (movProdutoVO.getContrato().getContratoDuracao().getNumeroMeses() == 1) ?
                    movProdutoVO.getContrato().getContratoDuracao().getNumeroMeses() + " MÊS" :
                    movProdutoVO.getContrato().getContratoDuracao().getNumeroMeses() + " MESES";
        }

        if (movProdutoVO.getProduto().getDescricao().equals(descricaoMovProduto)) {
            if (notaEmitirTO.getConfiguracaoNotaFiscalVO().isApresentarCompetencia()) {
                String desc = movProdutoVO.getProduto().getDescricao() + " " + meses + " Compet. " + movProdutoVO.getMesReferencia();
                descricao = desc;
                produtoTO.setDescricao(desc);
            } else {
                descricao = movProdutoVO.getProduto().getDescricao() + " " + meses;
                produtoTO.setDescricao(descricao);
            }
        } else {
            if (notaEmitirTO.getConfiguracaoNotaFiscalVO().isApresentarCompetencia()) {
                String desc = movProdutoVO.getProduto().getDescricao() + " " + descricaoMovProduto + " " + meses + " Compet. " + movProdutoVO.getMesReferencia();
                descricao = desc;
                produtoTO.setDescricao(desc);
            } else {
                descricao = movProdutoVO.getProduto().getDescricao() + " " + descricaoMovProduto + " " + meses;
                produtoTO.setDescricao(descricao);
            }
        }

        if (!UteisValidacao.emptyString(notaEmitirTO.getConfiguracaoNotaFiscalVO().getDescricaoServico())) {
            notaEmitirTO.setDescricao(notaEmitirTO.getConfiguracaoNotaFiscalVO().getDescricaoServico());
        } else {
            if (UteisValidacao.emptyString(notaEmitirTO.getDescricao())) {
                notaEmitirTO.setDescricao(descricao);
            } else {
                notaEmitirTO.setDescricao(notaEmitirTO.getDescricao() + "\n"
                        + descricao);
            }
        }

        notaEmitirTO.getProdutos().put(movProdutoVO.getCodigo().toString() + (residuo ? "R" : ""), produtoTO); // R ao final é para determinar se é referente ao valor residual
    }

    private List<NotaEmitirFormaPagamentoTO> obterPagamentoMovProduto(Integer movProduto) {
        List<NotaEmitirFormaPagamentoTO> listaFormaPagamento = new ArrayList<NotaEmitirFormaPagamentoTO>();
        try {

            String sqlStr = "select \n" +
                    "mp.formapagamento, \n" +
                    "mp.produtospagos \n" +
                    "from movpagamento mp \n" +
                    "where mp.produtospagos like '%|" + movProduto + ",%'";
            Statement stm = con.createStatement();
            ResultSet rs = stm.executeQuery(sqlStr);
            while (rs.next()) {

                Integer codformaPagamento = rs.getInt("formapagamento");
                String produtosPagos = rs.getString("produtospagos");

                FormaPagamentoVO formaPagamentoVO = obterFormaPagamentoVO(codformaPagamento);

                String[] produtos = produtosPagos.split("\\|");
                for (String prod : produtos) {
                    if (UteisValidacao.emptyString(prod) || prod.equals("null")) {
                        continue;
                    }
                    String[] split = prod.split(",");
                    Integer codigoMovProduto = Integer.valueOf(split[0]);
                    if (codigoMovProduto.equals(movProduto)) {
                        Double valorPago = Double.valueOf(split[3]);

                        NotaEmitirFormaPagamentoTO novo = new NotaEmitirFormaPagamentoTO();
                        novo.setFormaPagamento(formaPagamentoVO);
                        novo.setSiglaTipoFormaPagamento(formaPagamentoVO.getTipoFormaPagamento());
                        novo.setValor(Uteis.arredondarForcando2CasasDecimais(valorPago));
                        listaFormaPagamento.add(novo);
                    }
                }
            }
        } catch (Exception e) {
            listaFormaPagamento = new ArrayList<NotaEmitirFormaPagamentoTO>();
        }
        return listaFormaPagamento;
    }

    private Date obterDataCompetencia(EmpresaVO empresa, Date dataEmissao, Date dataCompetencia, Date dataEmissaoGestaoNotas, TipoNotaFiscalEnum notaFiscalTipo) {
        return obterData(empresa, dataEmissao, dataCompetencia, dataEmissaoGestaoNotas, notaFiscalTipo, false, null);
    }

    private Date obterDataEmissao(EmpresaVO empresa, Date dataEmissao, Date dataCompetencia, Date dataEmissaoGestaoNotas, TipoNotaFiscalEnum notaFiscalTipo, Date dataEmissaoRecibo) {
        return obterData(empresa, dataEmissao, dataCompetencia, dataEmissaoGestaoNotas, notaFiscalTipo, true, dataEmissaoRecibo);
    }

    private Date obterData(EmpresaVO empresa, Date dataEmissao, Date dataCompetencia, Date dataEmissaoGestaoNotas, TipoNotaFiscalEnum notaFiscalTipo,
                           boolean obterDataEmissao, Date dataEmissaoRecibo) {

        Date dataEmissaoJSON;
        Date dataCompetenciaJSON;
        if (empresa.isPermiteAlterarDataEmissaoNFSe() && notaFiscalTipo.equals(TipoNotaFiscalEnum.NFSE)) {
            //SE A EMPRESA ESTIVER HABILITADA PARA ALTERAR A DATA DE EMISSAO NO GESTAO DE NOTAS
            if (dataEmissaoGestaoNotas == null) {
                dataEmissaoGestaoNotas = Calendario.hoje();
            }
            dataEmissaoJSON = dataEmissaoGestaoNotas;
            dataCompetenciaJSON = dataEmissaoGestaoNotas;

        } else if ((empresa.getTipoGestaoNFSe() == TipoRelatorioDF.RECEITA.getCodigo()) && empresa.getEmiteNFSEPorDataCompensacao() && dataEmissao != null && dataCompetencia != null) {
            //EMITIR PELA DATA DE COMPENSAÇÃO -- SOMENTE POR RECEITA.
            dataEmissaoJSON = dataEmissao;
            dataCompetenciaJSON = dataCompetencia;
        } else {
            // EM BRASILIA A NOTA FISCAL DE SERVIÇO É SOMENTE NF ENTÃO UTILIZAMOS A MESMA PERMISSÃO DO NFSE
            if (empresa.isPermiteAlterarDataEmissaoNFSe() && dataEmissaoGestaoNotas != null) {
                dataEmissaoJSON = dataEmissaoGestaoNotas;
                dataCompetenciaJSON = dataEmissaoGestaoNotas;
            } else {
                // Se a data do pagamento for anterior a hoje, então a data de emissão é a data do pagamento
                // Pagamentos feitos após 22:00, que é o horário que o processo executa em Produção, estava sendo emitidas com data do dia seguinte
                if (dataEmissaoRecibo != null && Calendario.menor(dataEmissaoRecibo, Calendario.hoje()) &&
                        (Calendario.diferencaEmDias(dataEmissaoRecibo, Calendario.hoje()) <= 1)) {
                    dataEmissaoJSON = dataEmissaoRecibo;
                } else {
                    dataEmissaoJSON = Calendario.hoje();
                }
                dataCompetenciaJSON = Calendario.hoje();
            }
        }

        if (Calendario.igual(dataEmissaoJSON, Calendario.hoje())) {
            //verifica se é igual a data atual para poder colocar a hora se for a data atual
            dataEmissaoJSON = Calendario.hoje();
        }

        if (Calendario.igual(dataCompetenciaJSON, Calendario.hoje())) {
            //verifica se é igual a data atual para poder colocar a hora se for a data atual
            dataCompetenciaJSON = Calendario.hoje();
        }

        if (obterDataEmissao) {
            return dataEmissaoJSON;
        } else {
            return dataCompetenciaJSON;
        }
    }

    private void montarNotaTO(NotaEmitirTO emitirTO) throws Exception {
        boolean notaFamilia = emitirTO.isNotaFamilia();

        ConfiguracaoNotaFiscalVO configNotaFiscalVO = emitirTO.getConfiguracaoNotaFiscalVO();
        ConfiguracaoSistema configuracaoSistemaDAO = new ConfiguracaoSistema(con);
        Boolean usarUsarNomeResponsavelNota = configuracaoSistemaDAO.consultarUsarUsarNomeResponsavelNota();
        configuracaoSistemaDAO = null;

        NotaTO notaTO = new NotaTO();
        notaTO.setChave(emitirTO.getChave());

        if (UteisValidacao.emptyString(notaTO.getChave())) {
            throw new Exception("Chave não informada");
        }

        notaTO.setTipoNotaFiscal(configNotaFiscalVO.getTipoNotaFiscal());
        notaTO.setConfiguracaoNotaFiscalVO(configNotaFiscalVO);
        notaTO.setEmpresaVO(emitirTO.getEmpresaVO());
        notaTO.setUsuarioVO(emitirTO.getUsuarioVO());
        notaTO.setPessoaVO(emitirTO.getPessoaVO());
        notaTO.setNomePagador("");
        notaTO.setSequencialfamilia(emitirTO.getSequencialFamilia());

        if (configNotaFiscalVO.isEnotas()) {
            notaTO.setNotaAmbienteEmissao(configNotaFiscalVO.getAmbienteEmissao().getDescricaoSemAcentuacao());
        }

        PessoaVO pessoaVO = emitirTO.getPessoaVO();
        FornecedorVO fornecedorVO = emitirTO.getFornecedorVO();

        //QUANDO É ENOTAS APÓS SER INCLUIDO O REGISTRO NOTAFISCAL É ADICIONADO O CÓDIGO(NOTAFISCAL) NO IDENTIFICADOR
        notaTO.setNotaIDReferencia(notaTO.getChave() + "_" + configNotaFiscalVO.getCodigo() + "_" + emitirTO.getIdReferencia());

        notaTO.setNotaDtEmissao(emitirTO.getDataEmissao());
        notaTO.setNotaDtCompetencia(emitirTO.getDataCompetencia());

        String descricao = emitirTO.getDescricao();
        String observacao = emitirTO.getObservacao();


        //CALCULAR VALOR TOTAL DA NOTA
        notaTO.setNotaValor(Uteis.arredondarForcando2CasasDecimais(emitirTO.getValorNota()));

        //CALCULAR OS IMPOSTOS
        notaTO.setNotaNaturezaOperacao(configNotaFiscalVO.getNaturezaOperacao());
        if (configNotaFiscalVO.isEnotas()) {
            if (configNotaFiscalVO.getAmbienteEmissao().equals(AmbienteEmissaoNotaFiscalEnum.PRODUCAO)) {
                notaTO.setNotaSerie(configNotaFiscalVO.getConfigProducaoVO().getSerieNFe());
            } else if (configNotaFiscalVO.getAmbienteEmissao().equals(AmbienteEmissaoNotaFiscalEnum.HOMOLOGACAO)) {
                notaTO.setNotaSerie(configNotaFiscalVO.getConfigHomologacaoVO().getSerieNFe());
            }
        } else {
            notaTO.setNotaSerie(configNotaFiscalVO.getSerie());
        }
        notaTO.setNotaIssRetido(configNotaFiscalVO.isIssRetido());
        notaTO.setNotaExigibilidadeISS(configNotaFiscalVO.getExigibilidadeISS().getId());

        notaTO.setNotaAliquotaISS(configNotaFiscalVO.getIss());
        notaTO.setNotaAliquotaPIS(configNotaFiscalVO.getPis());
        notaTO.setNotaAliquotaCOFINS(configNotaFiscalVO.getCofins());
        notaTO.setNotaAliquotaIRRF(configNotaFiscalVO.getIrrf());
        notaTO.setNotaValorCSLL(configNotaFiscalVO.getCsll());

        notaTO.setNotaValorISS(Uteis.arredondarForcando2CasasDecimais(notaTO.getNotaValor() * (notaTO.getNotaAliquotaISS() / 100)));
        notaTO.setNotaValorPIS(Uteis.arredondarForcando2CasasDecimais(notaTO.getNotaValor() * (notaTO.getNotaAliquotaPIS() / 100)));
        notaTO.setNotaValorCOFINS(Uteis.arredondarForcando2CasasDecimais(notaTO.getNotaValor() * (notaTO.getNotaAliquotaCOFINS() / 100)));
        notaTO.setNotaValorIRRF(Uteis.arredondarForcando2CasasDecimais(notaTO.getNotaValor() * (notaTO.getNotaAliquotaIRRF() / 100)));
        notaTO.setNotaValorCSLL(Uteis.arredondarForcando2CasasDecimais(notaTO.getNotaValor() * (notaTO.getNotaValorCSLL() / 100)));

        //valor aproximado de tributos
        notaTO.setNotaPercentualAproximadoTributos(configNotaFiscalVO.getPercentualAproximadoTributos());
        notaTO.setNotaValorAproximadoTributos(Uteis.arredondarForcando2CasasDecimais(notaTO.getNotaValor() * (notaTO.getNotaPercentualAproximadoTributos() / 100)));


        //CÓDIGOS DOS SERVIÇOS
        notaTO.setNotaItemListaServico(configNotaFiscalVO.getCodListaServico());
        notaTO.setNotaCodigoTributacaoMunicipio(configNotaFiscalVO.getCodTributacaoMunicipal());
        notaTO.setNotaCNAE(configNotaFiscalVO.getCnae());


        //DESCOBRIR A DESCRIÇÃO DAS PARCELAS
        if (!notaFamilia && configNotaFiscalVO.isApresentarDescricaoParcela()) {
            Set<Integer> codigoMovProdutos = new HashSet<Integer>();
            String descricaoMovProduto = "";
            boolean primeiroProduto = true;
            for (String cod : emitirTO.getProdutos().keySet()) {
                NotaEmitirProdutoTO produto = emitirTO.getProdutos().get(cod);
                codigoMovProdutos.add(produto.getMovProdutoVO().getCodigo());
                if (primeiroProduto) {
                    descricaoMovProduto = produto.getDescricao();
                    primeiroProduto = false;
                }
            }

            MovParcela movParcelaDAO = new MovParcela(con);
            List<MovParcelaVO> movParcelaVOS = movParcelaDAO.consultarPorListMovProduto(codigoMovProdutos, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            movParcelaDAO = null;

            Set<String> descricaoParcela = new HashSet<String>();
            primeiroProduto = true;
            for (MovParcelaVO mp : movParcelaVOS) {
                if (primeiroProduto) {
                    descricaoParcela.add(descricaoMovProduto);
                    primeiroProduto = false;
                } else {
                    descricaoParcela.add(mp.getDescricao());
                }
            }

            if (!UteisValidacao.emptyString(StringUtils.join(descricaoParcela, "\n"))) {
                descricao = StringUtils.join(descricaoParcela, "\n");
            }
        }

        //ENVIAR A OBSERVAÇÃO APÓS A DESCRIÇÃO DA NOTA
        //CASO CONTRÁRIO ENVIA NO CAMPO OBSERVAÇÃO
        if (configNotaFiscalVO.isEnviarObservacaoNaDescricao()) {
            descricao = descricao + " " + configNotaFiscalVO.getObservacao();
            observacao = "";
        } else {
            observacao = observacao + " " + configNotaFiscalVO.getObservacao();
        }
        //ADICIONAR OBSERVAÇÃO DO CADASTRO DO ALUNO NA NOTA
        if (!UteisValidacao.emptyString(pessoaVO.getObservacaoNota())) {
            if (configNotaFiscalVO.isEnviarObservacaoNaDescricao()) {
                descricao = descricao + pessoaVO.getObservacaoNota();
            } else {
                observacao = pessoaVO.getObservacaoNota();
            }
        }

        boolean usarNomeResponsavelNota = false;
        if (configNotaFiscalVO.getTipoNotaFiscal().equals(TipoNotaFiscalEnum.NFCE)) {
            EmpresaVO empresaVO = obterEmpresaVO(configNotaFiscalVO.getEmpresaVO().getCodigo());
            usarNomeResponsavelNota = empresaVO.isUsarNomeResponsavelNFCe();
        } else {
            ConfiguracaoSistema confDAO = new ConfiguracaoSistema(con);
            usarNomeResponsavelNota = confDAO.usarNomeResponsavelNota();
            confDAO = null;
        }


        //OBTER NOME E CPF/CNPJ PARA A QUAL A NOTA SERÁ GERADA!
        //OBTER DADOS DE EMISSÃO DO CLIENTE
        String CPFCNPJCons = "";
        String razaoSocialCons = "";
        String nomeAluno = "";
        String cfdf = "";
        String inscricaoEstadual = "";
        String inscricaoMunicipal = "";

            if (fornecedorVO != null && !UteisValidacao.emptyNumber(fornecedorVO.getCodigo())) {

            if (UteisValidacao.emptyString(fornecedorVO.getCnpj())) {
                throw new ConsistirException(String.format("Fornecedor %s não possui CNPJ cadastrado.", pessoaVO.getNome()));
            }
            CPFCNPJCons = Formatador.removerMascara(fornecedorVO.getCnpj());
            razaoSocialCons = fornecedorVO.getNomeApresentar();
            nomeAluno = fornecedorVO.getNomeApresentar();

            inscricaoEstadual = fornecedorVO.getInscricaoEstadual().equals("ISENTO") ? fornecedorVO.getInscricaoEstadual() : Formatador.removerMascara(fornecedorVO.getInscricaoEstadual());
            inscricaoMunicipal = fornecedorVO.getInscricaoMunicipal().equals("ISENTO") ? fornecedorVO.getInscricaoMunicipal() : Formatador.removerMascara(fornecedorVO.getInscricaoMunicipal());
            cfdf = Formatador.removerMascara(fornecedorVO.getCfdf());

        } else if (pessoaVO != null && !UteisValidacao.emptyNumber(pessoaVO.getCodigo())) {

                Boolean emitirNotaServicoExtrangeiro = notaTO.getTipoNotaFiscal().equals(TipoNotaFiscalEnum.NFSE) &&
                        !UteisValidacao.emptyString(configNotaFiscalVO.getPaisVO().getNome()) &&
                        !UteisValidacao.emptyString(pessoaVO.getPais().getNome()) &&
                        !pessoaVO.getPais().getNome().toUpperCase().equals(configNotaFiscalVO.getPaisVO().getNome().toUpperCase());

            if (pessoaVO.isEmitirNomeTerceiro()) {

                if (UteisValidacao.emptyString(pessoaVO.getNomeTerceiro())) {
                    throw new ConsistirException(String.format("Cliente %s não possui nome do terceiro cadastrado.", pessoaVO.getNome()));
                } else if (UteisValidacao.emptyString(pessoaVO.getCpfCNPJTerceiro())) {
                    throw new ConsistirException(String.format("Cliente %s não possui CPF do terceiro cadastrado.", pessoaVO.getNome()));
                }

                CPFCNPJCons = Formatador.removerMascara(pessoaVO.getCpfCNPJTerceiro());
                razaoSocialCons = pessoaVO.getNomeTerceiro();
                nomeAluno = pessoaVO.getNome();
                inscricaoEstadual = Formatador.removerMascara(pessoaVO.getInscEstadualTerceiro());
                cfdf = Formatador.removerMascara(pessoaVO.getCfdfTerceiro());

            } else if (pessoaVO.getPessoaJuridica()) {

                if (UteisValidacao.emptyString(pessoaVO.getCnpj())) {
                    throw new ConsistirException(String.format("Cliente %s não possui CNPJ cadastrado.", pessoaVO.getNome()));
                }
                if (!UteisValidacao.emptyString(pessoaVO.getInscEstadual())) {
                    inscricaoEstadual = Formatador.removerMascara(pessoaVO.getInscEstadual());
                }
                if (!UteisValidacao.emptyString(pessoaVO.getInscMunicipal())) {
                    inscricaoMunicipal = Formatador.removerMascara(pessoaVO.getInscMunicipal());
                }

                CPFCNPJCons = Formatador.removerMascara(pessoaVO.getCnpj());
                razaoSocialCons =  !UteisValidacao.emptyString(pessoaVO.getNomeRegistro()) ? pessoaVO.getNomeRegistro() :  pessoaVO.getNome();
                nomeAluno = !UteisValidacao.emptyString(pessoaVO.getNomeRegistro()) ? pessoaVO.getNomeRegistro() :  pessoaVO.getNome();;

            }  else if (!pessoaVO.isEmitirNotaNomeAluno() && pessoaVO.getDataNasc() != null && usarNomeResponsavelNota && (Integer.parseInt(pessoaVO.getIdadePessoa()) < 18)) {
                Cliente clienteDAO = new Cliente(con);
                ClienteVO clienteVO = clienteDAO.consultarPorCodigoPessoa(pessoaVO.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSENTIDADESUBORDINADAS);
                ColaboradorVO colaboradorVO = null;
                if (clienteVO == null || clienteVO.getCodigo() == null || clienteVO.getCodigo() == 0) {
                    Colaborador colaboradorDAO = new Colaborador(con);
                    colaboradorVO = colaboradorDAO.consultarPorCodigoPessoa(pessoaVO.getCodigo(), 0, Uteis.NIVELMONTARDADOS_DADOSENTIDADESUBORDINADAS);
                    colaboradorDAO = null;
                }
                clienteDAO = null;
                if (clienteVO.getPessoaResponsavel() != null && !clienteVO.getPessoaResponsavel().getCodigo().equals(0)) {
                    razaoSocialCons = clienteVO.getPessoaResponsavel().getNome();
                    CPFCNPJCons = Formatador.removerMascara(clienteVO.getPessoaResponsavel().getCfp());
                    nomeAluno = !UteisValidacao.emptyString(clienteVO.getPessoa().getNomeRegistro()) ? clienteVO.getPessoa().getNomeRegistro() :  clienteVO.getPessoa().getNome();
                } else if (clienteVO.getPessoa().isEmitirNotaNomeMae() && !UteisValidacao.emptyString(clienteVO.getPessoa().getNomeMae()) && (!UteisValidacao.emptyString(clienteVO.getPessoa().getCpfMae()) || !UteisValidacao.emptyString(clienteVO.getPessoa().getCfp()))) {
                    razaoSocialCons = clienteVO.getPessoa().getNomeMae();
                    if (!UteisValidacao.emptyString(clienteVO.getPessoa().getCpfMae())) {
                        CPFCNPJCons = Formatador.removerMascara(clienteVO.getPessoa().getCpfMae());
                    } else {
                        CPFCNPJCons = Formatador.removerMascara(clienteVO.getPessoa().getCfp());
                    }
                    nomeAluno = !UteisValidacao.emptyString(clienteVO.getPessoa().getNomeRegistro()) ? clienteVO.getPessoa().getNomeRegistro() :  clienteVO.getPessoa().getNome();
                } else if (!clienteVO.getPessoa().isEmitirNotaNomeMae() && !UteisValidacao.emptyString(clienteVO.getPessoa().getNomePai()) && (!UteisValidacao.emptyString(clienteVO.getPessoa().getCpfPai()) || !UteisValidacao.emptyString(clienteVO.getPessoa().getCfp()))) {
                    razaoSocialCons = clienteVO.getPessoa().getNomePai();
                    if (!UteisValidacao.emptyString(clienteVO.getPessoa().getCpfPai())) {
                        CPFCNPJCons = Formatador.removerMascara(clienteVO.getPessoa().getCpfPai());
                    } else {
                        CPFCNPJCons = Formatador.removerMascara(clienteVO.getPessoa().getCfp());
                    }
                    nomeAluno = !UteisValidacao.emptyString(clienteVO.getPessoa().getNomeRegistro()) ? clienteVO.getPessoa().getNomeRegistro() :  clienteVO.getPessoa().getNome();
                } else if (colaboradorVO != null && colaboradorVO.getCodigo() != null && colaboradorVO.getCodigo() != 0) {
                    String mensagemCpfNaoCadastrado = "";
                    if (usarUsarNomeResponsavelNota) {
                        CPFCNPJCons = Formatador.removerMascara(colaboradorVO.getPessoa().getCpfMae());
                        razaoSocialCons = colaboradorVO.getPessoa().getNomeMae();
                        mensagemCpfNaoCadastrado = String.format("Colaborador %s não possui cpf e nome do responsável cadastrado.", pessoaVO.getNome());
                    } else {
                        CPFCNPJCons = Formatador.removerMascara(colaboradorVO.getPessoa().getCfp());
                        razaoSocialCons = colaboradorVO.getPessoa().getNome();
                        mensagemCpfNaoCadastrado = String.format("Colaborador %s não possui cpf cadastrado.", pessoaVO.getNome());
                    }
                    nomeAluno = !UteisValidacao.emptyString(pessoaVO.getNomeRegistro()) ? pessoaVO.getNomeRegistro() :  pessoaVO.getNome();

                    if (UteisValidacao.emptyString(CPFCNPJCons) || UteisValidacao.emptyString(razaoSocialCons)) {
                        throw new ConsistirException(mensagemCpfNaoCadastrado);
                    }

                } else {
                    throw new ConsistirException(String.format("Cliente %s não possui responsável cadastrado.", pessoaVO.getNome()));
                }
            } else {
                if (UteisValidacao.emptyString(pessoaVO.getCfp()) && UteisValidacao.emptyString(emitirTO.getNumeroNotaManual()) && !emitirNotaServicoExtrangeiro) {
                    throw new ConsistirException(String.format("Cliente %s não possui CPF cadastrado.", pessoaVO.getNome()));
                }
                CPFCNPJCons = emitirNotaServicoExtrangeiro ? "00000000000" : Formatador.removerMascara(pessoaVO.getCfp());
                razaoSocialCons = !UteisValidacao.emptyString(pessoaVO.getNomeRegistro()) ? pessoaVO.getNomeRegistro() :  pessoaVO.getNome();
                nomeAluno = !UteisValidacao.emptyString(pessoaVO.getNomeRegistro()) ? pessoaVO.getNomeRegistro() :  pessoaVO.getNome();
            }
        } else {
            razaoSocialCons = emitirTO.getNomePagador();
            notaTO.setNomePagador(emitirTO.getNomePagador());
        }

        notaTO.setCliRazaoSocial(razaoSocialCons);
        notaTO.setCliCPFCNPJ(CPFCNPJCons);
        notaTO.setCliNomeAluno(nomeAluno);
        notaTO.setCliInscEstadual(inscricaoEstadual);
        notaTO.setCliInscMunicipal(inscricaoMunicipal);
        notaTO.setCliCFDF(cfdf);

        //OBTER DADOS DE EMISSÃO DA EMPRESA
        notaTO.setEmpRazaoSocial(configNotaFiscalVO.getRazaoSocial());
        notaTO.setEmpNomeFantasia(configNotaFiscalVO.getNomeFantasia());
        notaTO.setEmpCNPJ(configNotaFiscalVO.getCnpj());
        notaTO.setEmpInscEstadual(configNotaFiscalVO.getInscricaoEstadual());
        notaTO.setEmpInscMunicipal(configNotaFiscalVO.getInscricaoMunicipal());


        //configuracao
        if (emitirTO.getEmpresaVO().isEmitirDuplicataNFSe() && emitirTO.getListaMovPagamento().size() > 0) {

            for (Integer movPagamento : emitirTO.getListaMovPagamento()) {

                MovPagamento movPagamentoDAO = new MovPagamento(con);
                MovPagamentoVO movPagamentoVO = movPagamentoDAO.consultarPorChavePrimaria(movPagamento, Uteis.NIVELMONTARDADOS_MOVPAGAMENTOSINTETICO);
                movPagamentoDAO = null;

                String descricaoFormaPagamento = "";
                if (configNotaFiscalVO.isUsarDescricaoFormaPagamento()) {
                    descricaoFormaPagamento = movPagamentoVO.getFormaPagamento().getDescricao();
                } else {
                    descricaoFormaPagamento = obterDescricaoTipoFormaPagamento(movPagamentoVO.getFormaPagamento().getTipoFormaPagamento());
                }


                if (movPagamentoVO.getFormaPagamento().getTipoFormaPagamento().equals(TipoFormaPagto.CARTAOCREDITO.getSigla())) {

                    for (CartaoCreditoVO cartaoCreditoVO : movPagamentoVO.getCartaoCreditoVOs()) {
                        NotaPagamentoTO novo = new NotaPagamentoTO();
                        novo.setDescricaoFormaPagamento(descricaoFormaPagamento);
                        novo.setSiglaFormaPagamento(movPagamentoVO.getFormaPagamento().getTipoFormaPagamento());
                        novo.setValor(Uteis.arredondarForcando2CasasDecimais(cartaoCreditoVO.getValor()));
                        novo.setNumero(cartaoCreditoVO.getNrParcela());
                        novo.setDataVencimento(cartaoCreditoVO.getDataOriginal() != null ? cartaoCreditoVO.getDataOriginal() : cartaoCreditoVO.getDataCompensacao());
                        notaTO.getNotaPagamentos().add(novo);
                    }

                } else if (movPagamentoVO.getFormaPagamento().getTipoFormaPagamento().equals(TipoFormaPagto.CHEQUE.getSigla())) {

                    int ic = 1;
                    for (ChequeVO chequeVO : movPagamentoVO.getChequeVOs()) {
                        NotaPagamentoTO novo = new NotaPagamentoTO();
                        novo.setDescricaoFormaPagamento(descricaoFormaPagamento);
                        novo.setSiglaFormaPagamento(movPagamentoVO.getFormaPagamento().getTipoFormaPagamento());
                        novo.setValor(Uteis.arredondarForcando2CasasDecimais(chequeVO.getValor()));
                        novo.setNumero(ic++);
                        novo.setDataVencimento(chequeVO.getDataOriginal() != null ? chequeVO.getDataOriginal() : chequeVO.getDataCompensacao());
                        notaTO.getNotaPagamentos().add(novo);
                    }

                } else if (movPagamentoVO.getFormaPagamento().getTipoFormaPagamento().equals(TipoFormaPagto.CARTAODEBITO.getSigla()) ||
                        movPagamentoVO.getFormaPagamento().getTipoFormaPagamento().equals(TipoFormaPagto.AVISTA.getSigla())) {

                    NotaPagamentoTO novo = new NotaPagamentoTO();
                    novo.setDescricaoFormaPagamento(descricaoFormaPagamento);
                    novo.setSiglaFormaPagamento(movPagamentoVO.getFormaPagamento().getTipoFormaPagamento());
                    novo.setValor(Uteis.arredondarForcando2CasasDecimais(movPagamentoVO.getValor()));
                    novo.setNumero(1);
                    novo.setDataVencimento(movPagamentoVO.getDataPagamento());
                    notaTO.getNotaPagamentos().add(novo);
                }
            }

            if (!UteisValidacao.emptyList(notaTO.getNotaPagamentos())) {
                Ordenacao.ordenarLista(notaTO.getNotaPagamentos(), "dataVencimento");
                int i = 0;
                for (NotaPagamentoTO no : notaTO.getNotaPagamentos()) {
                    no.setNumero(++i);
                }
            }
            notaTO.setEmitirDuplicata(true);

        } else {

            //OBTER FORMA DE PAGAMENTO DA NOTA
            Map<String, Double> map = new HashMap<String, Double>();
            Map<String, String> mapDescricaoSigla = new HashMap<String, String>();

            for (NotaEmitirFormaPagamentoTO obj : emitirTO.getFormasPagamento()) {

                String chaveMap = obj.getSiglaTipoFormaPagamento();
                if (configNotaFiscalVO.isUsarDescricaoFormaPagamento()) {
                    chaveMap = obj.getFormaPagamento().getDescricao();
                } else {
                    chaveMap = (UteisValidacao.emptyString(obterDescricaoTipoFormaPagamento(obj.getSiglaTipoFormaPagamento()))) ?
                            obj.getFormaPagamento().getDescricao() : obterDescricaoTipoFormaPagamento(obj.getSiglaTipoFormaPagamento());
                }

                if (map.containsKey(chaveMap)) {
                    map.put(chaveMap, Uteis.arredondarForcando2CasasDecimais(map.get(chaveMap) + obj.getValor()));
                } else {
                    map.put(chaveMap, obj.getValor());
                    mapDescricaoSigla.put(chaveMap, obj.getSiglaTipoFormaPagamento());
                }
            }

            for (String chaveMap : map.keySet()) {
                NotaPagamentoTO novo = new NotaPagamentoTO();
                novo.setValor(Uteis.arredondarForcando2CasasDecimais(map.get(chaveMap)));
                novo.setDescricaoFormaPagamento(chaveMap);
                novo.setSiglaFormaPagamento(mapDescricaoSigla.get(chaveMap));
                notaTO.getNotaPagamentos().add(novo);
            }

        }

        //OBTER PRODUTOS DA NOTA
        if (UteisValidacao.emptyList(emitirTO.getProdutos().values())) {
            NotaEmitirProdutoTO novo = new NotaEmitirProdutoTO();
            novo.setDescricao("PLANO");
            novo.setQuantidade(1);
            novo.setValorUnitario(Uteis.arredondarForcando2CasasDecimais(notaTO.getNotaValor()));
            emitirTO.getProdutos().put("1", novo);
        }

        boolean empresaUsaDescontoNaNota = notaTO.getEmpresaVO().isGerarNotaFiscalComDesconto()
                && notaTO.getConfiguracaoNotaFiscalVO().getTipoNotaFiscal().equals(TipoNotaFiscalEnum.NFCE);

        for (NotaEmitirProdutoTO produtoTO : emitirTO.getProdutos().values()) {
            NotaProdutoTO notaProdutoTO = new NotaProdutoTO();
            notaProdutoTO.setUnidadeMedida(produtoTO.getUnidadeMedida());
            notaProdutoTO.setCfop(produtoTO.getMovProdutoVO().getProduto().getCfop());
            notaProdutoTO.setCest(produtoTO.getMovProdutoVO().getProduto().getCest());
            notaProdutoTO.setProdutoVO(produtoTO.getMovProdutoVO().getProduto());
            notaProdutoTO.setDescricao(produtoTO.getDescricao().replace("|", ""));
            notaProdutoTO.setQuantidade(produtoTO.getQuantidade());
            notaProdutoTO.setUnidadeMedida(produtoTO.getUnidadeMedida());


            //JIRA PAGAMENTOS-865
            //Somente para o Delphi que  enviado o valortotal no valor unitrio.
            //by Luiz Felipe 06/05/2020
            Double valorUnitario = produtoTO.getValorUnitario();
            if (!configNotaFiscalVO.isEnotas() ) {
                valorUnitario = Uteis.arredondarForcando2CasasDecimais(produtoTO.getValorUnitario() * produtoTO.getQuantidade());
            }
            notaProdutoTO.setValorUnitario(Uteis.arredondarForcando2CasasDecimais(valorUnitario));

            if (empresaUsaDescontoNaNota) {
                notaProdutoTO.setValorDesconto(produtoTO.getDescontos());
            }
            notaProdutoTO.setEnviarPercentualImposto(produtoTO.getMovProdutoVO().getProduto().isEnviarPercentualImposto());
            notaProdutoTO.setPercentualFederal(produtoTO.getMovProdutoVO().getProduto().getPercentualFederal());
            notaProdutoTO.setPercentualEstadual(produtoTO.getMovProdutoVO().getProduto().getPercentualEstadual());
            notaProdutoTO.setPercentualMunicipal(produtoTO.getMovProdutoVO().getProduto().getPercentualMunicipal());
            notaProdutoTO.setIsentoPIS(produtoTO.getMovProdutoVO().getProduto().isIsentoPIS());
            notaProdutoTO.setIsentoCOFINS(produtoTO.getMovProdutoVO().getProduto().isIsentoCOFINS());
            notaProdutoTO.setIsentoICMS(produtoTO.getMovProdutoVO().getProduto().isIsentoICMS());
            if (notaTO.getTipoNotaFiscal().equals(TipoNotaFiscalEnum.NFCE)) {
                notaProdutoTO.setNcm(produtoTO.getMovProdutoVO().getProduto().getNcmNFCe());
            } else {
                notaProdutoTO.setNcm(produtoTO.getMovProdutoVO().getProduto().getNcm());
            }
            notaTO.getNotaProdutos().add(notaProdutoTO);
        }

        //OBTER O ENDERECO DO CLIENTE
        EnderecoVO endereco = new EnderecoVO();
        if (UteisValidacao.emptyString(notaTO.getNomePagador())) {
            if (configNotaFiscalVO.isEnderecoObrigatorio() && pessoaVO.getEnderecoVOs().isEmpty()) {
                if (fornecedorVO == null) {
                    throw new ConsistirException(String.format("Cliente %s não possui endereço cadastrado.", pessoaVO.getNome()));
                } else {
                    throw new ConsistirException(String.format("Fornecedor %s não possui endereço cadastrado.", pessoaVO.getNome()));
                }
            } else if (!configNotaFiscalVO.isEnderecoObrigatorio() && pessoaVO.getEnderecoVOs().isEmpty()) {
                endereco = new EnderecoVO();
            } else {
                endereco = pessoaVO.getEnderecoVOs().get(0);
            }
        }

        notaTO.setCliEndBairro(endereco.getBairro());
        notaTO.setCliEndLogradouro(endereco.getEndereco());
        notaTO.setCliEndCEP(endereco.getCep());
        notaTO.setCliEndNumero(endereco.getNumero());
        String cliEndComplemento = endereco.getComplemento();
        if (!UteisValidacao.emptyNumber(configNotaFiscalVO.getLimiteComplementoEndereco()) && cliEndComplemento.length() > configNotaFiscalVO.getLimiteComplementoEndereco()) {
            cliEndComplemento = cliEndComplemento.substring(0, configNotaFiscalVO.getLimiteComplementoEndereco());
            notaTO.setCliEndComplemento(cliEndComplemento);
        } else {
            notaTO.setCliEndComplemento(cliEndComplemento);
        }

        //OBTER O CIDADE DO CLIENTE
        String uf = "";
        String ufIBGE;
        String pais = "";
        String cidade = "";
        String cidadeIBGE;
        String cfop = "";

        if (notaTO.getTipoNotaFiscal().equals(TipoNotaFiscalEnum.NFSE) &&
                !UteisValidacao.emptyString(configNotaFiscalVO.getPaisVO().getNome()) &&
                !UteisValidacao.emptyString(pessoaVO.getPais().getNome()) &&
                !pessoaVO.getPais().getNome().toUpperCase().equals(configNotaFiscalVO.getPaisVO().getNome().toUpperCase())) {
                uf = "EX";
                ufIBGE = pessoaVO.getEstadoVO().getCodigoIBGE();
                pais = pessoaVO.getPais().getNome();
                cidade = "Exterior";
                notaTO.setCliCPFCNPJ("00000000000");
                notaTO.setCliEndBairro("ex");
                cidadeIBGE = pessoaVO.getCidade().getCodigoIBGE();
                if (notaTO.getTipoNotaFiscal().equals(TipoNotaFiscalEnum.NFSE) || notaTO.getTipoNotaFiscal().equals(TipoNotaFiscalEnum.NFE)) {
                        cfop = "6933";
                }
        } else if ((configNotaFiscalVO.isEnviarNotaCidadeEmpresa() && fornecedorVO == null) || UteisValidacao.emptyString(pessoaVO.getEstadoVO().getSigla())) {
            uf = configNotaFiscalVO.getEstadoVO().getSigla();
            ufIBGE = configNotaFiscalVO.getEstadoVO().getCodigoIBGE();
            pais = configNotaFiscalVO.getPaisVO().getNome();
            cidade = configNotaFiscalVO.getCidadeVO().getNomeSemAcento();
            cidadeIBGE = configNotaFiscalVO.getCidadeVO().getCodigoIBGE();
        } else {
            uf = pessoaVO.getEstadoVO().getSigla();
            ufIBGE = pessoaVO.getEstadoVO().getCodigoIBGE();
            pais = pessoaVO.getPais().getNome();
            cidade = pessoaVO.getCidade().getNomeSemAcento();
            cidadeIBGE = pessoaVO.getCidade().getCodigoIBGE();

            if (notaTO.getTipoNotaFiscal().equals(TipoNotaFiscalEnum.NFSE) || notaTO.getTipoNotaFiscal().equals(TipoNotaFiscalEnum.NFE)) {
                if (!pessoaVO.getEstadoVO().getSigla().equals(configNotaFiscalVO.getEstadoVO().getSigla())) {
                    cfop = "6933"; //se for de outro estado o CFOP é outro
                }
            }
        }
        notaTO.setNotaCFOP(cfop);

        notaTO.setCliEndPais(pais);
        notaTO.setCliEndCidade(cidade);
        notaTO.setCliEndCidadeIBGE(cidadeIBGE);
        notaTO.setCliEndUFEstado(uf);
        notaTO.setCliEndUFEstadoIBGE(ufIBGE);


        //OBTER O ENDERECO DA EMPRESA
        notaTO.setEmpEndBairro(configNotaFiscalVO.getBairro());
        notaTO.setEmpEndLogradouro(configNotaFiscalVO.getLogradouro());
        notaTO.setEmpEndCEP(configNotaFiscalVO.getCep());
        notaTO.setEmpEndNumero(configNotaFiscalVO.getNumero());
        notaTO.setEmpEndComplemento(configNotaFiscalVO.getComplemento());

        //OBTER O CIDADE DA EMPRESA
        notaTO.setEmpEndPais(configNotaFiscalVO.getPaisVO().getNome());
        notaTO.setEmpEndCidade(configNotaFiscalVO.getCidadeVO().getNome());
        notaTO.setEmpEndCidadeIBGE(configNotaFiscalVO.getCidadeVO().getCodigoIBGE());
        notaTO.setEmpEndUFEstado(configNotaFiscalVO.getEstadoVO().getSigla());
        notaTO.setEmpEndUFEstadoIBGE(configNotaFiscalVO.getEstadoVO().getCodigoIBGE());

        //OBTER O EMAIL DA EMPRESA
        notaTO.setEmpEmail(configNotaFiscalVO.getEmail());

        //OBTER O EMAIL DO CLIENTE
        EmailVO emailVO = new EmailVO();
        if (UteisValidacao.emptyString(notaTO.getNomePagador())) {
            if (configNotaFiscalVO.isEmailObrigatorio() && pessoaVO.getEmailVOs().isEmpty()) {
                if (fornecedorVO == null) {
                    throw new ConsistirException(String.format("Cliente %s não possui email cadastrado.", pessoaVO.getNome()));
                } else {
                    throw new ConsistirException(String.format("Fornecedor %s não possui email cadastrado.", pessoaVO.getNome()));
                }
            } else if (!configNotaFiscalVO.isEmailObrigatorio() && pessoaVO.getEmailVOs().isEmpty()) {
                emailVO = new EmailVO();
            } else {
                emailVO = pessoaVO.getEmailVOs().get(0);
            }
        }

        notaTO.setCliEmail(emailVO.getEmail());
        notaTO.setNotaEnviarEmail(configNotaFiscalVO.isEnviarEmailCliente() && !emailVO.getBloqueadoBounce());

        //OBTER O TELEFONE DO CLIENTE
        TelefoneVO telefoneVO;
        if (pessoaVO.getTelefoneVOs().isEmpty()) {
            telefoneVO = new TelefoneVO();
        } else {
            telefoneVO = pessoaVO.getTelefoneVOs().get(0);
        }

        notaTO.setCliTelefone(telefoneVO.getNumero());

        //OBTER O TELEFONE DA EMPRESA
        notaTO.setEmpTelefone(configNotaFiscalVO.getTelefoneComercial());


        //PREENCHER TAGS DA DESCRIÇÃO
        if (!UteisValidacao.emptyString(descricao)) {
            descricao = descricao.replace("[VALOR_NFSE]", Formatador.formatarValorMonetario(notaTO.getNotaValor()));
            descricao = descricao.replace("[VALOR_ISS]", Formatador.formatarValorMonetario(notaTO.getNotaValorISS()));
            descricao = descricao.replace("[VALOR_PIS]", Formatador.formatarValorMonetario(notaTO.getNotaValorPIS()));
            descricao = descricao.replace("[VALOR_COFINS]", Formatador.formatarValorMonetario(notaTO.getNotaValorCOFINS()));
            descricao = descricao.replace("[VALOR_TOTAL_IMPOSTO]", Formatador.formatarValorMonetario(notaTO.getNotaValorISS() + notaTO.getNotaValorPIS() + notaTO.getNotaValorCOFINS()));
            descricao = descricao.replace("[TRIBUTOS_VALOR]", Formatador.formatarValorMonetario(notaTO.getNotaValorAproximadoTributos()));
            descricao = descricao.replace("[TRIBUTOS_PERCENTUAL]", Formatador.formatarValorMonetarioSemMoeda(notaTO.getNotaPercentualAproximadoTributos()) + "%");
            descricao = descricao.replace("[VALOR_FEDERAL]", Formatador.formatarValorMonetario(notaTO.getNotaValor() * (configNotaFiscalVO.getPercentualFederal() / 100.0)));
            descricao = descricao.replace("[VALOR_ESTADUAL]", Formatador.formatarValorMonetario(notaTO.getNotaValor() * (configNotaFiscalVO.getPercentualEstadual() / 100.0)));
            descricao = descricao.replace("[VALOR_MUNICIPAL]", Formatador.formatarValorMonetario(notaTO.getNotaValor() * (configNotaFiscalVO.getPercentualMunicipal() / 100.0)));
        }


        //limitar o tamanho da descrição da nota
        descricao = descricao.replaceFirst("/", "");
        if (!UteisValidacao.emptyNumber(configNotaFiscalVO.getLimiteDescricao()) && descricao.length() > configNotaFiscalVO.getLimiteDescricao()) {
            descricao = descricao.substring(0, configNotaFiscalVO.getLimiteDescricao());
            notaTO.setNotaDescricao(descricao);
        } else {
            notaTO.setNotaDescricao(descricao);
        }


        //PREENCHER TAGS DA OBSERVAÇÃO
        if (!UteisValidacao.emptyString(observacao)) {
            observacao = observacao.replace("[VALOR_NFSE]", Formatador.formatarValorMonetario(notaTO.getNotaValor()));
            observacao = observacao.replace("[VALOR_ISS]", Formatador.formatarValorMonetario(notaTO.getNotaValorISS()));
            observacao = observacao.replace("[VALOR_PIS]", Formatador.formatarValorMonetario(notaTO.getNotaValorPIS()));
            observacao = observacao.replace("[VALOR_COFINS]", Formatador.formatarValorMonetario(notaTO.getNotaValorCOFINS()));
            observacao = observacao.replace("[VALOR_TOTAL_IMPOSTO]", Formatador.formatarValorMonetario(notaTO.getNotaValorISS() + notaTO.getNotaValorPIS() + notaTO.getNotaValorCOFINS()));
            observacao = observacao.replace("[TRIBUTOS_VALOR]", Formatador.formatarValorMonetario(notaTO.getNotaValorAproximadoTributos()));
            observacao = observacao.replace("[TRIBUTOS_PERCENTUAL]", Formatador.formatarValorMonetarioSemMoeda(notaTO.getNotaPercentualAproximadoTributos()) + "%");
            observacao = observacao.replace("[VALOR_FEDERAL]", Formatador.formatarValorMonetario(notaTO.getNotaValor() * (configNotaFiscalVO.getPercentualFederal() / 100.0)));
            observacao = observacao.replace("[VALOR_ESTADUAL]", Formatador.formatarValorMonetario(notaTO.getNotaValor() * (configNotaFiscalVO.getPercentualEstadual() / 100.0)));
            observacao = observacao.replace("[VALOR_MUNICIPAL]", Formatador.formatarValorMonetario(notaTO.getNotaValor() * (configNotaFiscalVO.getPercentualMunicipal() / 100.0)));
        }

        //limitar o tamanho da observação
        if (!UteisValidacao.emptyNumber(configNotaFiscalVO.getLimiteObservacao()) && observacao.length() > configNotaFiscalVO.getLimiteObservacao()) {
            observacao = observacao.substring(0, configNotaFiscalVO.getLimiteObservacao());
        }
        notaTO.setNotaObservacao(observacao);

        emitirTO.setNotaTO(notaTO);
    }


    public void processarNotasProcessarLista(TipoNotaFiscalEnum tipoNotaFiscal, List<NotaProcessarTO> listaNotasProcessar,
                                             Date dtEmissaoGestaoNotas, UsuarioVO usuarioVO) throws Exception {
        try {

            Date d1 = Calendario.hoje();

            if (UteisValidacao.emptyList(listaNotasProcessar)) {
                throw new Exception("A lista de notas está vazia.");
            }

            CacheControl.toggleCache(Empresa.class, true);

            for (NotaProcessarTO notaProcessarTO : listaNotasProcessar) {
                processarNotasProcessar(tipoNotaFiscal, notaProcessarTO, usuarioVO, dtEmissaoGestaoNotas, true);
            }

            Date d2 = negocio.comuns.utilitarias.Calendario.hoje();
            Uteis.logar(null, "ProcessarNotas de NOTAS FISCAIS | Total de notas: " + listaNotasProcessar.size() + " | Tempo total: " + (d2.getTime() - d1.getTime()));
        } catch (Exception e) {
            Uteis.logar(null, "ERRO! processarNotas ERRO: " + e.getMessage());
            throw e;
        } finally {
            CacheControl.clear();
        }
    }

    private void verificarDuplicidadeNFCe(NotaProcessarTO notaProcessarTO) throws Exception {
        NotaFiscalConsumidorEletronica notaFiscalConsumidorEletronicaDAO;
        try {
            notaFiscalConsumidorEletronicaDAO = new NotaFiscalConsumidorEletronica(this.con);

            Integer codigoNotaFiscal = 0;
            Integer reciboPagamento = 0;

            if (notaProcessarTO.getMovPagamentoVO().getReciboPagamento() != null && !UteisValidacao.emptyNumber(notaProcessarTO.getMovPagamentoVO().getReciboPagamento().getCodigo())) {
                reciboPagamento = notaProcessarTO.getMovPagamentoVO().getReciboPagamento().getCodigo();
            } else if (!UteisValidacao.emptyNumber(notaProcessarTO.getReciboPagamentoVO().getCodigo())) {
                reciboPagamento = notaProcessarTO.getReciboPagamentoVO().getCodigo();
            }

            if (!UteisValidacao.emptyNumber(reciboPagamento)) {
                codigoNotaFiscal = notaFiscalConsumidorEletronicaDAO.consultarPorReciboPagamento(reciboPagamento, Uteis.NIVELMONTARDADOS_DADOSBASICOS).getCodigo();
            }

            boolean existeNotaEmitidaParaEsseRecibo = !UteisValidacao.emptyNumber(codigoNotaFiscal);
            if (existeNotaEmitidaParaEsseRecibo) {
                throw new Exception("Já existe nota emitida para esse recibo. Pesquise novamente para atualizar o status.");
            }
        } finally {
            notaFiscalConsumidorEletronicaDAO = null;
        }
    }

    private void processarNotasProcessar(TipoNotaFiscalEnum tipoNotaFiscal, NotaProcessarTO notaProcessarTO,
                                         UsuarioVO usuarioVO, Date dtEmissaoGestaoNotas, boolean controlarTransacao) throws Exception {
        try {
            if (controlarTransacao) {
                con.setAutoCommit(false);
            }

            notaProcessarTO.setSucesso(false);
            notaProcessarTO.setRetorno("");
            notaProcessarTO.setUsuarioVO(usuarioVO);
            if (tipoNotaFiscal.equals(TipoNotaFiscalEnum.NFCE)) {
                verificarDuplicidadeNFCe(notaProcessarTO);
            }

            if (!UteisValidacao.emptyNumber(notaProcessarTO.getMovContaVO().getCodigo())) {

                processarMovConta(tipoNotaFiscal, notaProcessarTO);

                if (tipoNotaFiscal.equals(TipoNotaFiscalEnum.NFCE)) {
                    MovConta movContaDAO = new MovConta(con);
                    movContaDAO.gravarNFCEEmitida(notaProcessarTO.getMovContaVO().getCodigo());
                    movContaDAO = null;
                }

            } else if (!UteisValidacao.emptyNumber(notaProcessarTO.getChequeVO().getCodigo())) {

                processarCheque(tipoNotaFiscal, notaProcessarTO, dtEmissaoGestaoNotas);

            } else if (!UteisValidacao.emptyNumber(notaProcessarTO.getCartaoCreditoVO().getCodigo())) {

                processarCartaoCredito(tipoNotaFiscal, notaProcessarTO, dtEmissaoGestaoNotas);

            } else if (!UteisValidacao.emptyNumber(notaProcessarTO.getMovPagamentoVO().getCodigo())) {

                processarMovPagamento(tipoNotaFiscal, notaProcessarTO, dtEmissaoGestaoNotas);

            } else if (!UteisValidacao.emptyNumber(notaProcessarTO.getMovProdutoVO().getCodigo())) {

                processarMovProduto(tipoNotaFiscal, notaProcessarTO, dtEmissaoGestaoNotas);

            } else if (!UteisValidacao.emptyNumber(notaProcessarTO.getReciboPagamentoVO().getCodigo())) {

                ReciboPagamento reciboPagamentoDAO = new ReciboPagamento(con);
                MovPagamento movPagamentoDAO = new MovPagamento(con);
                notaProcessarTO.setReciboPagamentoVO(reciboPagamentoDAO.consultarPorChavePrimaria(notaProcessarTO.getReciboPagamentoVO().getCodigo(), Uteis.NIVELMONTARDADOS_TELACONSULTA));
                notaProcessarTO.getReciboPagamentoVO().setPagamentosDesteRecibo(movPagamentoDAO.consultarPorCodigoRecibo(notaProcessarTO.getReciboPagamentoVO().getCodigo(), false, Uteis.NIVELMONTARDADOS_TODOS));
                reciboPagamentoDAO = null;
                movPagamentoDAO = null;

                processarReciboPagamento(tipoNotaFiscal, notaProcessarTO, dtEmissaoGestaoNotas);

                if (tipoNotaFiscal.equals(TipoNotaFiscalEnum.NFSE) || tipoNotaFiscal.equals(TipoNotaFiscalEnum.NFE)) {
                    ReciboPagamento reciboDAO = new ReciboPagamento(con);
                    reciboDAO.gravarNFSEEmitida(notaProcessarTO.getReciboPagamentoVO().getCodigo());
                    reciboDAO = null;
                    notaProcessarTO.getReciboPagamentoVO().setNfseEmitida(true);
                }

            }

            notaProcessarTO.setSucesso(true);
            notaProcessarTO.setSituacaoNotaFiscal(SituacaoNotaFiscalEnum.GERADA);
            notaProcessarTO.setRetorno("Dados aguardando envio.");

            if (controlarTransacao) {
                con.commit();
            }
        } catch (Exception e) {
            notaProcessarTO.setSucesso(false);
            notaProcessarTO.setSituacaoNotaFiscal(SituacaoNotaFiscalEnum.ERRO_GERACAO);
            notaProcessarTO.setRetorno(e.getMessage());
            if (controlarTransacao) {
                con.rollback();
                con.setAutoCommit(true);
            }
            if (e.getMessage().contains("Realize novamente a busca")) {
                throw e;
            }
        } finally {
            if (controlarTransacao) {
                con.setAutoCommit(true);
            }
        }
    }

    private ContratoVO obterContratoVOPorMovPagamento(Integer movPagamento) throws SQLException {
        String sql = "select \n" +
                "rp.contrato \n" +
                "from movpagamento mp \n" +
                "inner join recibopagamento rp on rp.codigo = mp.recibopagamento \n" +
                "where mp.codigo = ? order by rp.codigo desc limit 1";
        PreparedStatement pst = con.prepareStatement(sql);
        pst.setInt(1, movPagamento);
        ResultSet rs = pst.executeQuery();
        ContratoVO contratoVO = new ContratoVO();
        if (rs.next()) {
            contratoVO.setCodigo(rs.getInt("contrato"));
        }
        return contratoVO;
    }

    private void processarReciboPagamento(TipoNotaFiscalEnum notaFiscalTipo, NotaProcessarTO notaProcessarTO, Date dataEmissaoGestaoNotas) throws Exception {
        NFSeEmitida nfSeEmitidaDAO;
        try {
            nfSeEmitidaDAO = new NFSeEmitida(this.con);

            ReciboPagamentoVO reciboVO = notaProcessarTO.getReciboPagamentoVO();

            boolean existeNotaEmitida = nfSeEmitidaDAO.existeNFSePorRecibo(reciboVO.getCodigo());
            if (existeNotaEmitida &&  notaFiscalTipo.equals(TipoNotaFiscalEnum.NFSE)) {
                throw new Exception("Houve uma tentativa de enviar notas já emitidas. Realize novamente a busca no Gestão de notas.");
            }

            EmpresaVO empresaVO = obterEmpresaVO(reciboVO.getEmpresa().getCodigo());

            validarInformacoesEnvioDeNota(notaFiscalTipo, empresaVO);

            PessoaVO pessoaVO = reciboVO.getPessoaPagador();
            ContratoVO contratoVO = reciboVO.getContrato();
            Integer codIdentificador = reciboVO.getCodigo();

            String nomePagador = "";
            if (UteisValidacao.emptyNumber(reciboVO.getPessoaPagador().getCodigo())) {
                pessoaVO = null;
                nomePagador = reciboVO.getNomePessoaPagador();
            } else {
                Pessoa pessoaDAO = new Pessoa(con);
                pessoaVO = pessoaDAO.consultarPorChavePrimaria(pessoaVO.getCodigo(), Uteis.NIVELMONTARDADOS_TODOS);
                pessoaDAO = null;
            }

            Date dataEmissao = obterDataEmissao(empresaVO, null, null, dataEmissaoGestaoNotas, notaFiscalTipo, reciboVO.getData());
            Date dataCompetencia = obterDataCompetencia(empresaVO, null, null, dataEmissaoGestaoNotas, notaFiscalTipo);

            Map<Integer, NotaEmitirTO> mapaNotas = new HashMap<Integer, NotaEmitirTO>();
            boolean pagamentoComCreditoCliente = false;
            for (MovPagamentoVO pagamento : reciboVO.getPagamentosDesteRecibo()) {
                if (!pagamento.getCredito()) {
                    String siglaPag = pagamento.getFormaPagamento().getTipoFormaPagamento();
                    String produtosPagos = pagamento.getProdutosPagos();
                    processarMapaNotaEmitir(mapaNotas, empresaVO, pessoaVO, null, notaFiscalTipo, produtosPagos, pagamento.getValor(),
                            TipoFormaPagto.getTipoFormaPagtoSigla(siglaPag), pagamento.getFormaPagamento(), null, null, nomePagador, pagamento);
                } else {
                    pagamentoComCreditoCliente = true;
                }
            }
            if (mapaNotas.isEmpty() && pagamentoComCreditoCliente == true) {
                throw new Exception("Produto(s) pago(s) com crédito do cliente não pode(m) ser enviado(s).");
            }

            processarMapa(mapaNotas, dataEmissao, dataCompetencia, notaProcessarTO, empresaVO, contratoVO, IDENTIFICADOR_RECIBO + codIdentificador);
        } finally {
            nfSeEmitidaDAO = null;
        }
    }

    private void processarMovPagamento(TipoNotaFiscalEnum notaFiscalTipo, NotaProcessarTO notaProcessarTO, Date dataEmissaoGestaoNotas) throws Exception {
        NFSeEmitida nfSeEmitidaDAO;
        try {
            nfSeEmitidaDAO = new NFSeEmitida(this.con);

            MovPagamentoVO movPagamentoVO = notaProcessarTO.getMovPagamentoVO();
            boolean existeNotaEmitida = nfSeEmitidaDAO.existeNFSePorPagamento(movPagamentoVO.getCodigo());
            if (existeNotaEmitida) {
                throw new Exception("Houve uma tentativa de enviar notas já emitidas. Realize novamente a busca no Gestão de notas.");
            }

            movPagamentoVO = new MovPagamento(con).consultarPorChavePrimaria(movPagamentoVO.getCodigo(), Uteis.NIVELMONTARDADOS_PAGAMENTOS_TELA_CLIENTE);

            EmpresaVO empresaVO = obterEmpresaVO(movPagamentoVO.getEmpresa().getCodigo());
            validarInformacoesEnvioDeNota(notaFiscalTipo, empresaVO);

            PessoaVO pessoaVO = movPagamentoVO.getPessoa();
            ContratoVO contratoVO = obterContratoVOPorMovPagamento(movPagamentoVO.getCodigo());
            Integer codIdentificador = movPagamentoVO.getCodigo();
            Pessoa pessoaDAO = new Pessoa(con);
            pessoaVO = pessoaDAO.consultarPorChavePrimaria(pessoaVO.getCodigo(), Uteis.NIVELMONTARDADOS_TODOS);
            pessoaDAO = null;

            Date dataEmissao = obterDataEmissao(empresaVO, movPagamentoVO.getDataPagamento(), movPagamentoVO.getDataPagamento(), dataEmissaoGestaoNotas, notaFiscalTipo, null);
            Date dataCompetencia = obterDataCompetencia(empresaVO, movPagamentoVO.getDataPagamento(), movPagamentoVO.getDataPagamento(), dataEmissaoGestaoNotas, notaFiscalTipo);

            Map<Integer, NotaEmitirTO> mapaNotas = new HashMap<Integer, NotaEmitirTO>();
            String produtosPagos = movPagamentoVO.getProdutosPagos();
            processarMapaNotaEmitir(mapaNotas, empresaVO, pessoaVO, null, notaFiscalTipo, produtosPagos, movPagamentoVO.getValor(),
                    movPagamentoVO.getFormaPagamento().getTipoFormaPagamentoEnum(), movPagamentoVO.getFormaPagamento(), null, null, null, null);

            processarMapa(mapaNotas, dataEmissao, dataCompetencia, notaProcessarTO, empresaVO, contratoVO, IDENTIFICADOR_MOVPAGAMENTO + codIdentificador);
        } finally {
            nfSeEmitidaDAO = null;
        }
    }

    private void processarMovProduto(TipoNotaFiscalEnum tipoNotaFiscalEnum, NotaProcessarTO notaProcessarTO, Date dataEmissaoGestaoNotas) throws Exception {
        NFSeEmitida nfSeEmitidaDAO;
        try {
            nfSeEmitidaDAO = new NFSeEmitida(this.con);

            MovProdutoVO movProdutoVO = notaProcessarTO.getMovProdutoVO();
            boolean existeNotaEmitida = nfSeEmitidaDAO.existeNFSePorProduto(movProdutoVO.getCodigo());
            if (existeNotaEmitida) {
                throw new Exception("Houve uma tentativa de enviar notas já emitidas. Realize novamente a busca no Gestão de notas.");
            }

            EmpresaVO empresaVO = obterEmpresaVO(movProdutoVO.getEmpresa().getCodigo());
            validarInformacoesEnvioDeNota(tipoNotaFiscalEnum, empresaVO);

            boolean naoEmiteNotaNoNomeDoAluno = !movProdutoVO.getPessoa().isEmitirNotaNomeAluno();

            PessoaVO pessoaVO;

            if (naoEmiteNotaNoNomeDoAluno && movProdutoVO.getMovPagamento() != null &&
                    movProdutoVO.getMovPagamento().getPessoa() != null &&
                    movProdutoVO.getMovPagamento().getPessoa().getCodigo() > 0) {
                pessoaVO = movProdutoVO.getMovPagamento().getPessoa();
            } else {
                pessoaVO = movProdutoVO.getPessoa();
            }

            ContratoVO contratoVO = movProdutoVO.getContrato();
            Integer codIdentificador = movProdutoVO.getCodigo();
            Pessoa pessoaDAO = new Pessoa(con);
            pessoaVO = pessoaDAO.consultarPorChavePrimaria(pessoaVO.getCodigo(), Uteis.NIVELMONTARDADOS_TODOS);
            pessoaDAO = null;

            Date dataEmissao = obterDataEmissao(empresaVO, null, null, dataEmissaoGestaoNotas, tipoNotaFiscalEnum, null);
            Date dataCompetencia = obterDataCompetencia(empresaVO, null, null, dataEmissaoGestaoNotas, tipoNotaFiscalEnum);

            Map<Integer, NotaEmitirTO> mapaNotas = new HashMap<Integer, NotaEmitirTO>();
            processarMapaNotaEmitir(mapaNotas, empresaVO, pessoaVO, null, tipoNotaFiscalEnum, "", 0.0,
                    null, null, movProdutoVO, null, null, null);

            processarMapa(mapaNotas, dataEmissao, dataCompetencia, notaProcessarTO, empresaVO, contratoVO, IDENTIFICADOR_MOVPRODUTO + codIdentificador);
        } finally {
            nfSeEmitidaDAO = null;
        }
    }

    private void processarMovConta(TipoNotaFiscalEnum notaFiscalTipo, NotaProcessarTO notaProcessarTO) throws Exception {
        NFSeEmitida nfSeEmitidaDAO;
        Fornecedor fornecedorDAO;
        Pessoa pessoaDAO;
        try {
            pessoaDAO = new Pessoa(con);
            nfSeEmitidaDAO = new NFSeEmitida(this.con);
            fornecedorDAO = new Fornecedor(con);

            MovContaVO movContaVO = notaProcessarTO.getMovContaVO();
            boolean existeNotaEmitida = nfSeEmitidaDAO.existeNFSePorConta(movContaVO.getCodigo());
            if (existeNotaEmitida) {
                throw new Exception("Houve uma tentativa de enviar notas já emitidas. Realize novamente a busca no Gestão de notas.");
            }

            EmpresaVO empresaVO = obterEmpresaVO(movContaVO.getEmpresaVO().getCodigo());
            validarInformacoesEnvioDeNota(notaFiscalTipo, empresaVO);

            FornecedorVO fornecedorVO = fornecedorDAO.consultarPorPessoa(movContaVO.getPessoaVO());
            PessoaVO pessoaVO = pessoaDAO.consultarPorChavePrimaria(movContaVO.getPessoaVO().getCodigo(), Uteis.NIVELMONTARDADOS_TODOS);
            if ((UtilReflection.objetoMaiorQueZero(empresaVO, "getPessoaFinan().getCodigo()")) &&
                    (UtilReflection.objetoMaiorQueZero(pessoaVO, "getCodigo()"))) {
                if (empresaVO.getPessoaFinan().getCodigo().equals(pessoaVO.getCodigo())) {
                    // Neste caso está sendo gerado NFSe para um lançamento onde a pessoa é a própria academia.
                    // Desta forma, atualizar os dados cadastrais antes de gerar a NFSe.
                    pessoaDAO.atualizarDadosCadastraisPessoaEmpresa(pessoaVO, empresaVO);
                    pessoaVO = pessoaDAO.consultarPorChavePrimaria(pessoaVO.getCodigo(), Uteis.NIVELMONTARDADOS_TODOS);
                }
            }

            ContratoVO contratoVO = null;
            Integer codIdentificador = movContaVO.getCodigo();

            Date dataEmissao = notaProcessarTO.getDataEmissaoPrevista();
            Date dataCompetencia = Calendario.hoje();

            Map<Integer, NotaEmitirTO> mapaNotas = new HashMap<Integer, NotaEmitirTO>();
            processarMapaNotaEmitir(mapaNotas, empresaVO, pessoaVO, fornecedorVO, notaFiscalTipo, "", movContaVO.getValor(),
                    null, null, null, movContaVO, null, null);

            processarMapa(mapaNotas, dataEmissao, dataCompetencia, notaProcessarTO, empresaVO, contratoVO, IDENTIFICADOR_MOVCONTA + codIdentificador);
        } finally {
            pessoaDAO = null;
            nfSeEmitidaDAO = null;
            fornecedorDAO = null;
        }
    }

    private void processarCheque(TipoNotaFiscalEnum notaFiscalTipo, NotaProcessarTO notaProcessarTO, Date dataEmissaoGestaoNotas) throws Exception {
        NFSeEmitida nfSeEmitidaDAO;
        MovPagamento movPagamentoDAO;
        Pessoa pessoaDAO;
        try {
            pessoaDAO = new Pessoa(con);
            nfSeEmitidaDAO = new NFSeEmitida(this.con);
            movPagamentoDAO = new MovPagamento(con);

            ChequeVO chequeVO = notaProcessarTO.getChequeVO();
            boolean existeNotaEmitida = nfSeEmitidaDAO.existeNFSePorCheque(chequeVO.getCodigo());
            if (existeNotaEmitida) {
                throw new Exception("Houve uma tentativa de enviar notas já emitidas. Realize novamente a busca no Gestão de notas.");
            }

            MovPagamentoVO movPagamentoVO = movPagamentoDAO.consultarPorChavePrimaria(chequeVO.getMovPagamento(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);

            EmpresaVO empresaVO = obterEmpresaVO(movPagamentoVO.getEmpresa().getCodigo());
            validarInformacoesEnvioDeNota(notaFiscalTipo, empresaVO);

            PessoaVO pessoaVO = movPagamentoVO.getPessoa();
            ContratoVO contratoVO = obterContratoVOPorMovPagamento(movPagamentoVO.getCodigo());
            Integer codIdentificador = chequeVO.getCodigo();

            pessoaVO = pessoaDAO.consultarPorChavePrimaria(pessoaVO.getCodigo(), Uteis.NIVELMONTARDADOS_TODOS);

            Date dataEmissao = obterDataEmissao(empresaVO, chequeVO.getDataCompensacao(), chequeVO.getDataCompensacao(), dataEmissaoGestaoNotas, notaFiscalTipo, null);
            Date dataCompetencia = obterDataCompetencia(empresaVO, chequeVO.getDataCompensacao(), chequeVO.getDataCompensacao(), dataEmissaoGestaoNotas, notaFiscalTipo);

            Map<Integer, NotaEmitirTO> mapaNotas = new HashMap<Integer, NotaEmitirTO>();
            String produtosPagos = chequeVO.getProdutosPagos();
            processarMapaNotaEmitir(mapaNotas, empresaVO, pessoaVO, null, notaFiscalTipo, produtosPagos, chequeVO.getValor(),
                    TipoFormaPagto.CHEQUE, movPagamentoVO.getFormaPagamento(), null, null, null, null);

            processarMapa(mapaNotas, dataEmissao, dataCompetencia, notaProcessarTO, empresaVO, contratoVO, IDENTIFICADOR_CHEQUE + codIdentificador);
        } finally {
            movPagamentoDAO = null;
            nfSeEmitidaDAO = null;
            pessoaDAO = null;
        }
    }

    private void processarCartaoCredito(TipoNotaFiscalEnum notaFiscalTipo, NotaProcessarTO notaProcessarTO, Date dataEmissaoGestaoNotas) throws Exception {
        NFSeEmitida nfSeEmitidaDAO;
        Pessoa pessoaDAO;
        try {
            pessoaDAO = new Pessoa(con);
            nfSeEmitidaDAO = new NFSeEmitida(this.con);

            CartaoCreditoVO cartaoCreditoVO = notaProcessarTO.getCartaoCreditoVO();
            boolean existeNotaEmitida = nfSeEmitidaDAO.existeNFSePorCartao(cartaoCreditoVO.getCodigo());
            if (existeNotaEmitida) {
                throw new Exception("Houve uma tentativa de enviar notas já emitidas. Realize novamente a busca no Gestão de notas.");
            }
            EmpresaVO empresaVO = obterEmpresaVO(cartaoCreditoVO.getMovpagamento().getEmpresa().getCodigo());
            validarInformacoesEnvioDeNota(notaFiscalTipo, empresaVO);

            PessoaVO pessoaVO = cartaoCreditoVO.getMovpagamento().getPessoa();
            ContratoVO contratoVO = obterContratoVOPorMovPagamento(cartaoCreditoVO.getMovpagamento().getCodigo());
            Integer codIdentificador = cartaoCreditoVO.getCodigo();
            pessoaVO = pessoaDAO.consultarPorChavePrimaria(pessoaVO.getCodigo(), Uteis.NIVELMONTARDADOS_TODOS);

            Date dataEmissao = obterDataEmissao(empresaVO, cartaoCreditoVO.getDataCompensacao(), cartaoCreditoVO.getDataCompensacao(), dataEmissaoGestaoNotas, notaFiscalTipo, null);
            Date dataCompetencia = obterDataCompetencia(empresaVO, cartaoCreditoVO.getDataCompensacao(), cartaoCreditoVO.getDataCompensacao(), dataEmissaoGestaoNotas, notaFiscalTipo);

            Map<Integer, NotaEmitirTO> mapaNotas = new HashMap<Integer, NotaEmitirTO>();
            String produtosPagos = cartaoCreditoVO.getProdutosPagos();
            processarMapaNotaEmitir(mapaNotas, empresaVO, pessoaVO, null, notaFiscalTipo, produtosPagos, cartaoCreditoVO.getValor(),
                    TipoFormaPagto.CARTAOCREDITO, cartaoCreditoVO.getMovpagamento().getFormaPagamento(), null, null, null, null);

            processarMapa(mapaNotas, dataEmissao, dataCompetencia, notaProcessarTO, empresaVO, contratoVO, IDENTIFICADOR_CARTAO + codIdentificador);
        } finally {
            pessoaDAO = null;
            nfSeEmitidaDAO = null;
        }
    }

    private void processarMapa(Map<Integer, NotaEmitirTO> mapaNotas, Date dtEmissao, Date dtCompetencia,
                               NotaProcessarTO notaProcessarTO, EmpresaVO empresaVO, ContratoVO contratoVO,
                               String identificador) throws Exception {

        if (mapaNotas.isEmpty()) {
            throw new Exception("Nenhum produto configurado para emissão vendido neste recibo.");
        }

        Collection<Integer> configs = mapaNotas.keySet();
        for (Integer configEmissao : configs) {
            NotaEmitirTO notaEmitirTO = mapaNotas.get(configEmissao);
            //IDENTIFICADOR PARA EVITAR ENVIO DUPLICADO.
            notaEmitirTO.setIdReferencia(identificador);
            notaEmitirTO.setEmpresaVO(empresaVO);
            notaEmitirTO.setUsuarioVO(notaProcessarTO.getUsuarioVO());
            notaEmitirTO.setChave(notaProcessarTO.getChave());
            notaEmitirTO.setDataEmissao(dtEmissao);
            notaEmitirTO.setDataCompetencia(dtCompetencia);
            notaEmitirTO.setNumeroNotaManual(notaProcessarTO.getNumeroNotaManual());

            montarNotaTO(notaEmitirTO);
            gerarNotaEmitida(notaEmitirTO, notaProcessarTO, empresaVO, contratoVO);
        }
    }

    private void gerarNotaEmitida(NotaEmitirTO notaEmitirTO, NotaProcessarTO notaProcessarTO,
                                  EmpresaVO empresaVO, ContratoVO contratoVO) throws Exception {

        NotaTO notaTO = notaEmitirTO.getNotaTO();

        NFSeEmitidaVO nfSeEmitidaVO = gerarNFSeEmitida(notaEmitirTO, notaProcessarTO, empresaVO, contratoVO);
        NotaFiscalConsumidorEletronicaVO notaFiscalConsumidorVO = gerarNFCeEmitida(notaEmitirTO, notaProcessarTO, empresaVO, contratoVO);

        notaTO.setNfSeEmitidaVO(nfSeEmitidaVO);
        notaTO.setNotaFiscalConsumidorEletronicaVO(notaFiscalConsumidorVO);

        if (UteisValidacao.emptyNumber(notaFiscalConsumidorVO.getCodigo()) && UteisValidacao.emptyNumber(nfSeEmitidaVO.getCodigo())) {
            throw new Exception("Erro ao gerar nota fiscal!");
        }

        JSONObject jsonEnvio = null;

        if (!UteisValidacao.emptyString(nfSeEmitidaVO.getNrNotaManual())) {

            //NOTA MANUAL NÃO DEVE GERAR O JSON PARA EVITAR ENVIO ACIDENTAL
            jsonEnvio = new JSONObject();

        } else if (notaTO.getConfiguracaoNotaFiscalVO().isEnotas()) { //GERAÇÃO JSON PARA O notaFiscal

            //INCLUIR REGISTRO PARA ENVIO PARA O ENOTAS
            jsonEnvio = gerarJSONEnvioEnotas(notaTO, notaProcessarTO);

        } else if (!UteisValidacao.emptyNumber(nfSeEmitidaVO.getCodigo())) {

            //GERAÇÃO JSON PARA O DELPHI - NFS-E
            jsonEnvio = gerarJSONEnvioNFSe(notaTO);

        } else if (!UteisValidacao.emptyNumber(notaFiscalConsumidorVO.getCodigo())) {

            //GERAÇÃO JSON PARA O DELPHI - NFC-E
            jsonEnvio = gerarJSONEnvioNFCe(notaTO);

        }

        if (jsonEnvio == null) {
            throw new Exception("Erro ao gerar nota fiscal - Json!");
        }

        if (!UteisValidacao.emptyNumber(notaFiscalConsumidorVO.getCodigo())) {
            NotaFiscalConsumidorEletronica nfceDAO = new NotaFiscalConsumidorEletronica(con);
            notaFiscalConsumidorVO.setJsonEnviar(jsonEnvio.toString());
            notaFiscalConsumidorVO.setIdReferencia(notaTO.getNotaIDReferencia());
            nfceDAO.atualizarJsonEnviarIdReferencia(notaFiscalConsumidorVO);
            notaProcessarTO.getListaNotaFiscalConsumidorVO().add(notaFiscalConsumidorVO);
            nfceDAO = null;
        }

        if (!UteisValidacao.emptyNumber(nfSeEmitidaVO.getCodigo())) {
            NFSeEmitida nfseDAO = new NFSeEmitida(con);
            nfSeEmitidaVO.setJsonEnviar(jsonEnvio.toString());
            nfSeEmitidaVO.setIdReferencia(notaTO.getNotaIDReferencia());
            nfseDAO.atualizarJsonEnviarIdReferencia(nfSeEmitidaVO);
            notaProcessarTO.getListaNFSeEmitidaVO().add(nfSeEmitidaVO);
            nfseDAO = null;
        }
    }

    private NFSeEmitidaVO gerarNFSeEmitida(NotaEmitirTO notaEmitirTO, NotaProcessarTO notaProcessarTO,
                                           EmpresaVO empresaVO, ContratoVO contratoVO) throws Exception {

        if (!notaEmitirTO.getNotaTO().getConfiguracaoNotaFiscalVO().getTipoNotaFiscal().equals(TipoNotaFiscalEnum.NFSE) &&
                !notaEmitirTO.getNotaTO().getConfiguracaoNotaFiscalVO().getTipoNotaFiscal().equals(TipoNotaFiscalEnum.NFE)) {
            return new NFSeEmitidaVO();
        }

        PessoaVO pessoaVO = notaEmitirTO.getNotaTO().getPessoaVO();
        MovPagamentoVO movPagamentoVO = notaProcessarTO.getMovPagamentoVO();
        ReciboPagamentoVO reciboPagamentoVO = notaProcessarTO.getReciboPagamentoVO();
        ChequeVO chequeVO = notaProcessarTO.getChequeVO();
        CartaoCreditoVO cartaoCreditoVO = notaProcessarTO.getCartaoCreditoVO();
        MovProdutoVO movProdutoVO = notaProcessarTO.getMovProdutoVO();
        MovContaVO movContaVO = notaProcessarTO.getMovContaVO();
        NotaTO notaTO = notaEmitirTO.getNotaTO();

        NFSeEmitida nfseDAO = new NFSeEmitida(con);
        boolean existeNotaEmitida = nfseDAO.existeNFSeIdReferencia(notaTO.getNotaIDReferencia());
        if (existeNotaEmitida) {
            throw new Exception("Já existe nota emitida com o identificador (" + notaTO.getNotaIDReferencia() + ")");
        }

        NFSeEmitidaVO nfSeEmitidaVO = new NFSeEmitidaVO();
        nfSeEmitidaVO.setSituacaoNotaFiscal(SituacaoNotaFiscalEnum.GERADA);
        nfSeEmitidaVO.setRecibo(reciboPagamentoVO);
        nfSeEmitidaVO.setCheque(chequeVO);
        nfSeEmitidaVO.setCartaoCredito(cartaoCreditoVO);
        nfSeEmitidaVO.setMovPagamento(movPagamentoVO);
        nfSeEmitidaVO.setMovProdutoVO(movProdutoVO);
        nfSeEmitidaVO.setMovConta(movContaVO);
        nfSeEmitidaVO.setContrato(contratoVO);
        nfSeEmitidaVO.setIdReferencia(notaTO.getNotaIDReferencia());
        nfSeEmitidaVO.setValor(notaTO.getNotaValor());
        nfSeEmitidaVO.setDataReferencia(notaProcessarTO.getDataEmissaoPrevista());
        nfSeEmitidaVO.setDataEmissao(notaTO.getNotaDtEmissao());
        nfSeEmitidaVO.setPessoa(pessoaVO.getCodigo());
        nfSeEmitidaVO.setEmpresa(empresaVO.getCodigo());
        nfSeEmitidaVO.setConfiguracaoNotaFiscalVO(notaTO.getConfiguracaoNotaFiscalVO());
        nfSeEmitidaVO.setJsonEnviar("");
        nfSeEmitidaVO.setDataRegistro(Calendario.hoje());
        nfSeEmitidaVO.setNrNotaManual(notaEmitirTO.getNumeroNotaManual());
        nfSeEmitidaVO.setEnotas(notaTO.getConfiguracaoNotaFiscalVO().isEnotas());
        nfseDAO.incluir(nfSeEmitidaVO);
        return nfSeEmitidaVO;
    }

    private NotaFiscalConsumidorEletronicaVO gerarNFCeEmitida(NotaEmitirTO notaEmitirTO, NotaProcessarTO notaProcessarTO,
                                                              EmpresaVO empresaVO, ContratoVO contratoVO) throws Exception {

        if (!notaEmitirTO.getNotaTO().getConfiguracaoNotaFiscalVO().getTipoNotaFiscal().equals(TipoNotaFiscalEnum.NFCE)) {
            return new NotaFiscalConsumidorEletronicaVO();
        }

        PessoaVO pessoaVO = notaEmitirTO.getPessoaVO();
        MovPagamentoVO movPagamentoVO = notaProcessarTO.getMovPagamentoVO();
        ReciboPagamentoVO reciboPagamentoVO = notaProcessarTO.getReciboPagamentoVO();
        ChequeVO chequeVO = notaProcessarTO.getChequeVO();
        CartaoCreditoVO cartaoCreditoVO = notaProcessarTO.getCartaoCreditoVO();
        MovProdutoVO movProdutoVO = notaProcessarTO.getMovProdutoVO();
        MovContaVO movContaVO = notaProcessarTO.getMovContaVO();
        NotaTO notaTO = notaEmitirTO.getNotaTO();

        NotaFiscalConsumidorEletronica nfceDAO = new NotaFiscalConsumidorEletronica(con);
        boolean existeNotaEmitida = nfceDAO.existeNFCeIdReferencia(notaTO.getNotaIDReferencia());
        if (existeNotaEmitida) {
            throw new Exception("Já existe NFCe emitida com o identificador (" + notaTO.getNotaIDReferencia() + ")");
        }

        NotaFiscalConsumidorEletronicaVO nfceVO = new NotaFiscalConsumidorEletronicaVO();
        nfceVO.setSituacaoNotaFiscal(SituacaoNotaFiscalEnum.GERADA);
        nfceVO.setReciboPagamento(reciboPagamentoVO);
        nfceVO.setCheque(chequeVO);
        nfceVO.setCartaoCredito(cartaoCreditoVO);
        nfceVO.setMovPagamento(movPagamentoVO);
        nfceVO.setMovProduto(movProdutoVO);
        nfceVO.setMovConta(movContaVO);
        nfceVO.setIdReferencia(notaTO.getNotaIDReferencia());
        nfceVO.setValorTotal(notaTO.getNotaValor());
        nfceVO.setPessoaVO(pessoaVO);
        nfceVO.setEmpresa(empresaVO);
        nfceVO.setConfiguracaoNotaFiscalVO(notaTO.getConfiguracaoNotaFiscalVO());
        nfceVO.setJsonEnviar("");
        nfceVO.setDataRegistro(Calendario.hoje());
        nfceVO.setEnotas(notaTO.getConfiguracaoNotaFiscalVO().isEnotas());
        nfceVO.setUsuario(notaProcessarTO.getUsuarioVO());
        nfceDAO.incluir(nfceVO);
        return nfceVO;
    }

    private JSONObject gerarJSONEnvioEnotas(NotaTO notaTO, NotaProcessarTO notaProcessarTO) throws Exception {

        TipoNotaFiscalEnum tipoNotaFiscalEnum = notaTO.getConfiguracaoNotaFiscalVO().getTipoNotaFiscal();
        notaTO.setTipoNotaFiscal(tipoNotaFiscalEnum);

        NotaFiscalVO notaFiscalVO = new NotaFiscalVO(notaTO);
        notaFiscalVO.setNotaFiscalAnterior(notaProcessarTO.getNotaFiscalAnterior());
        incluirSemCommit(notaFiscalVO);
        notaProcessarTO.setNotaFiscalNova(notaFiscalVO.getCodigo());

        if (!UteisValidacao.emptyNumber(notaFiscalVO.getCodigo())) {
            if (!notaTO.getChave().contains("teste") && Uteis.isAmbienteDesenvolvimentoTeste()) {
                // Notas estavam sendo emitidas em ambiente de desenvolvimento,
                // então o Enotas retorna via web hook para o erviço de notas e altera o status da nota se o código local for o mesmo do código da notafiscal local
                // Problema identificado no ticket https://pacto.atlassian.net/browse/AC42-565
                throw new Exception("Não pode gerar nota fiscal em ambiente de desenvolvimento com chave real da empresa. Renomeie a chave da empresa no OAMD e envie novamente!");
            }

            //atualicar identificador com o código da notafiscal !! by Luiz Felipe
            notaFiscalVO.setIdReferencia(notaTO.getNotaIDReferencia() + "_" + notaFiscalVO.getCodigo());

            //NOVO IDENTIFICADOR PARA O ENOTAS.
            //NÃO PODE ULTRAPASSAR 50 CARACTERES
            // by Luiz Felipe 05/02/2020
            notaFiscalVO.setIdPacto(notaTO.getChave() + "_" + notaFiscalVO.getCodigo());
            notaTO.setNotaIDReferencia(notaFiscalVO.getIdPacto());

            JSONObject jsonEnotas = gerarJsonEnvioServicoNotaFiscal(notaFiscalVO, notaTO);
            notaFiscalVO.setJsonEnvio(jsonEnotas.toString());
            atualizarJsonIdReferenciaIdPactoEnvio(notaFiscalVO);
            notaTO.setNotaFiscalVO(notaFiscalVO);
            return jsonEnotas;
        } else {
            throw new Exception("Erro ao gerar nota fiscal!");
        }
    }

    private JSONObject gerarJSONEnvioNFSe(NotaTO notaTO) throws JSONException {
        String urlConfirmacao = Uteis.getUrlAplicacao() + "/retorno/nota?key=" + notaTO.getChave() + "&nfseEmitida=" + notaTO.getNfSeEmitidaVO().getCodigo() + "&idLote=";

        JSONArray itens = new JSONArray();
        JSONArray formasPagamento = new JSONArray();
        for (NotaProdutoTO produtoTO : notaTO.getNotaProdutos()) {
            itens.put(new JSONObject(new ProdutoNFSeDelphiJSON(produtoTO).toJSON()));
        }
        for (NotaPagamentoTO pagamentoTO : notaTO.getNotaPagamentos()) {
            formasPagamento.put(new JSONObject(new PagamentoNFSeDelphiJSON(pagamentoTO).toJSON()));
        }

        JSONObject RPS = new JSONObject(new RpsNFSeDelphiJSON(notaTO).toJSON());
        RPS.put("Itens", itens);
        RPS.put("formasPagamento", formasPagamento);

        JSONObject jsonDelphi = new JSONObject(new LoteNFSeDelphiJSON(notaTO, urlConfirmacao).toJSON());
        jsonDelphi.put("RPS", new JSONArray().put(RPS));

        return jsonDelphi;
    }

    private JSONObject gerarJSONEnvioNFCe(NotaTO notaTO) throws JSONException {

        JSONArray itens = new JSONArray();
        JSONArray formasPagamento = new JSONArray();
        for (NotaProdutoTO produtoTO : notaTO.getNotaProdutos()) {
            itens.put(new JSONObject(new ProdutoNFCeDelphiJSON(produtoTO).toJSONDelphi()));
        }
        for (NotaPagamentoTO pagamentoTO : notaTO.getNotaPagamentos()) {
            formasPagamento.put(new JSONObject(new PagamentoNFCeDelphiJSON(pagamentoTO).toJSON()));
        }

        JSONObject NFCe = new JSONObject(new NFCeDelphiJSON(notaTO).toJSON());
        NFCe.put("itens", itens);
        NFCe.put("formasPagamento", formasPagamento);

        JSONObject jsonDelphi = new JSONObject();
        jsonDelphi.put("NFCe", NFCe);

        return jsonDelphi;
    }

    private JSONObject gerarJsonEnvioServicoNotaFiscal(NotaFiscalVO notaFiscalVO, NotaTO notaTO) {

        JSONObject jsonEnvio = notaFiscalVO.gerarJSONEnvioEnotas(notaTO);

        JSONObject jsonObject = new JSONObject();
        if (notaFiscalVO.getTipo().equals(TipoNotaFiscalEnum.NFE)) {
            jsonObject = new NFeEnotasJSON().obterJSONEnotas(notaTO);
        } else if (notaFiscalVO.getTipo().equals(TipoNotaFiscalEnum.NFSE)) {
            jsonObject = new JSONObject(new EnotasNFSeJSON(notaTO).toJSON());
        } else if (notaFiscalVO.getTipo().equals(TipoNotaFiscalEnum.NFCE)) {
            jsonObject = new NFCeEnotasJSON().obterJSONEnotas(notaTO);
        }
//        System.out.println("#################################");
//        System.out.println("############# JSON ##############");
//        System.out.println("#################################");
//        System.out.println(jsonObject);
        jsonEnvio.put("jsonNota", jsonObject);
        return jsonEnvio;
    }

    private void atualizarJsonIdReferenciaIdPactoEnvio(NotaFiscalVO notaFiscalVO) throws Exception {
        String sql = "UPDATE notafiscal SET jsonEnvio = ?, idReferencia = ?, idPacto = ? WHERE codigo = ?";
        PreparedStatement sqlInserir = con.prepareStatement(sql);
        sqlInserir.setString(1, notaFiscalVO.getJsonEnvio());
        sqlInserir.setString(2, notaFiscalVO.getIdReferencia());
        sqlInserir.setString(3, notaFiscalVO.getIdPacto());
        sqlInserir.setInt(4, notaFiscalVO.getCodigo());
        sqlInserir.execute();
    }

    public void atualizarStatusNota(StatusEnotasEnum statusEnotasEnum, NotaFiscalVO obj) throws Exception {
        String sql = "UPDATE notafiscal SET statusNota = ? WHERE codigo = ?";
        PreparedStatement sqlInserir = con.prepareStatement(sql);
        sqlInserir.setString(1, statusEnotasEnum.getDescricaoEnotas());
        sqlInserir.setInt(2, obj.getCodigo());
        sqlInserir.execute();
    }

    public void atualizarStatusNotaNFSe(StatusEnotasEnum statusEnotasEnum, NFSeEmitidaVO obj) throws Exception {
        String sql = "UPDATE notafiscal SET statusNota = ? WHERE nfseemitida = ?";
        PreparedStatement sqlInserir = con.prepareStatement(sql);
        sqlInserir.setString(1, statusEnotasEnum.getDescricaoEnotas());
        sqlInserir.setInt(2, obj.getCodigo());
        sqlInserir.execute();
    }

    public void atualizarStatusNotaNFCe(StatusEnotasEnum statusEnotasEnum, NotaFiscalConsumidorEletronicaVO obj) throws Exception {
        String sql = "UPDATE notafiscal SET statusNota = ? WHERE notafiscalconsumidoreletronica = ?";
        PreparedStatement sqlInserir = con.prepareStatement(sql);
        sqlInserir.setString(1, statusEnotasEnum.getDescricaoEnotas());
        sqlInserir.setInt(2, obj.getCodigo());
        sqlInserir.execute();
    }

    private FornecedorVO obterFornecedor(PessoaVO pessoa) {
        FornecedorVO forncedor = new FornecedorVO();
        forncedor.setDescricao(pessoa.getNome());
        forncedor.setPessoa(pessoa);
        forncedor.setCnpj(pessoa.getCnpj());
        forncedor.setCfdf(pessoa.getCfdf());
        forncedor.setInscricaoEstadual(pessoa.getInscEstadual());
        return forncedor;
    }

    public void enviarNotasAguardando() {
        Uteis.logarDebug("INICIO | enviarNotasAguardando");
//        System.out.println("#####################################");
//        System.out.println("#### ENTROU NO ENVIAR AGUARDANDO ####");
//        System.out.println("#####################################");
        realizarEnvioNotasDelphiNFSe();
        realizarEnvioNotasDelphiNFCe();
        realizarEnvioEnotasNFSe();
        realizarEnvioEnotasNFCe();
        realizarEnvioEnotasNFSeFamilia();
        realizarEnvioOperacoesServicoNota();
        Uteis.logarDebug("FIM | enviarNotasAguardando");
    }

    private void realizarEnvioOperacoesServicoNota() {
        NotaFiscalOperacao notaOpeDAO;
        try {
            notaOpeDAO = new NotaFiscalOperacao(con);
            notaOpeDAO.realizarEnvioOperacoesServicoNota();
        } catch (Exception ex) {
            Uteis.logar(true, null, "ERRO realizarEnvioOperacoesServicoNota: " + ex.getMessage());
        } finally {
            notaOpeDAO = null;
        }
    }

    public void processarItensIndividual(String chave, TipoNotaFiscalEnum tipoNotaFiscal, UsuarioVO usuarioVO,
                                         List<ItemGestaoNotasTO> itensIndividual,
                                         Date dtEmissaoGestaoNotas, String numeroNotaManual,
                                         TipoRelatorioDF tipoEmissao) throws Exception {

        try {
            Date d1Enviar = Calendario.hoje();

            if (UteisValidacao.emptyString(chave)) {
                throw new Exception("Chave não informada.");
            }

            List<NotaProcessarTO> listaNotasProcessar = new ArrayList<NotaProcessarTO>();
            int codigo = 0;
            for (ItemGestaoNotasTO itemIndividual : itensIndividual) {
                itemIndividual.setCodigo(++codigo);
                listaNotasProcessar.add(new NotaProcessarTO(itemIndividual, numeroNotaManual, tipoEmissao, chave));
            }

            processarNotasProcessarLista(tipoNotaFiscal, listaNotasProcessar, dtEmissaoGestaoNotas, usuarioVO);

            for (NotaProcessarTO processarTO : listaNotasProcessar) {
                for (ItemGestaoNotasTO item : itensIndividual) {
                    if (item.getCodigo().equals(processarTO.getCodigo())) {
                        item.setSelecionado(false);
                        item.setRetorno(processarTO.getRetorno());
                        item.setNfseemitida(processarTO.isSucesso());
                        item.setSituacaoNotaFiscal(processarTO.getSituacaoNotaFiscal());
                        break;
                    }
                }
            }

            Date d2Enviar = Calendario.hoje();
            Uteis.logar(null, "Tempo processarItensIndividual: " + (d2Enviar.getTime() - d1Enviar.getTime()));
        } catch (Exception ex) {
            throw ex;
        } finally {
            enviarNotasAguardando();
        }
    }

    private void realizarEnvioNotasDelphiNFSe() {
        NFSeEmitida nfseDAO;
        try {
            nfseDAO = new NFSeEmitida(con);

            List<NFSeEmitidaVO> listaNotasEnviar = nfseDAO.consultarNotasAguardandoEnvio();

            if (UteisValidacao.emptyList(listaNotasEnviar)) {
                return;
            }

            Uteis.logar(true, null, "### VOU ENVIAR NFSe - Total " + listaNotasEnviar.size());

            JSONArray jsonArray = new JSONArray();

            Set<Integer> codNFSeEmitidas = new HashSet<Integer>();
            for (NFSeEmitidaVO nfSeEmitidaVO : listaNotasEnviar) {
                if (!nfSeEmitidaVO.isEnotas() && !UteisValidacao.emptyString(nfSeEmitidaVO.getJsonEnviar())) {
                    jsonArray.put(new JSONObject(nfSeEmitidaVO.getJsonEnviar()));
                    codNFSeEmitidas.add(nfSeEmitidaVO.getCodigo());
                }
            }

            if (jsonArray.length() > 0) {

                String codigosNFSeEmitida = StringUtils.join(codNFSeEmitidas, ",");

                JSONObject jsonObject = new JSONObject();
                jsonObject.put("listaLotes", jsonArray);

                Date dataEnvio = Calendario.hoje();
                nfseDAO.atualizarDataEnvioSituacao(dataEnvio, SituacaoNotaFiscalEnum.ENVIADA, codigosNFSeEmitida);

                RetornoEnvioNotaFiscalTO retornoEnvioNotaFiscalTO = enviarNFSeDelphi(jsonObject);

                if (!retornoEnvioNotaFiscalTO.getResultadoEnvio().equals(ResultadoEnvioNFSeEnum.OK)) {
                    nfseDAO.atualizarDataEnvioSituacao(null, SituacaoNotaFiscalEnum.GERADA, codigosNFSeEmitida);
                }
            }
        } catch (Exception ex) {
            Uteis.logar(true, null, "ERRO realizarEnvioNotasDelphiNFSe: " + ex.getMessage());
        } finally {
            nfseDAO = null;
        }
    }

    private void realizarEnvioNotasDelphiNFCe() {
        NotaFiscalConsumidorEletronica notaDAO;
        try {
            notaDAO = new NotaFiscalConsumidorEletronica(con);

            List<NotaFiscalConsumidorEletronicaVO> listaNotasEnviar = notaDAO.consultarNotasAguardandoEnvio();
            if (UteisValidacao.emptyList(listaNotasEnviar)) {
                return;
            }

            Uteis.logar(true, null, "### VOU ENVIAR NFCe - Total " + listaNotasEnviar.size());

            for (NotaFiscalConsumidorEletronicaVO obj : listaNotasEnviar) {
                try {
                    if (!obj.isEnotas() && !UteisValidacao.emptyString(obj.getJsonEnviar())) {
                        obj.setDataEnvio(Calendario.hoje());

                        RetornoEnvioNotaFiscalTO retornoEnvioNotaFiscalTO = enviarRetornoNFCeDelphi(obj);

                        if (retornoEnvioNotaFiscalTO.getResultadoEnvio().equals(ResultadoEnvioNFSeEnum.OK)) {
                            obj.setResultadoEnvio("");
                            obj.setSituacaoNotaFiscal(SituacaoNotaFiscalEnum.PROCESSADA);
                            notaDAO.atualizarIdNFCe(SituacaoNotaFiscalEnum.PROCESSADA, retornoEnvioNotaFiscalTO.getIdLote(), obj);
                        } else {
                            obj.setSituacaoNotaFiscal(SituacaoNotaFiscalEnum.NAO_PROCESSADA);
                            obj.setResultadoEnvio(retornoEnvioNotaFiscalTO.getMensagem());
                            notaDAO.atualizarIdNFCe(SituacaoNotaFiscalEnum.NAO_PROCESSADA, retornoEnvioNotaFiscalTO.getIdLote(), obj);
                        }
                        notaDAO.atualizarDataEnvioSituacaoResultadoEnvio(obj);
                    }
                } catch (Exception ignored) {
                }
            }
        } catch (Exception ex) {
            Uteis.logar(true, null, "ERRO realizarEnvioNotasDelphiNFCe: " + ex.getMessage());
        } finally {
            notaDAO = null;
        }
    }

    private RetornoEnvioNotaFiscalTO enviarNFSeDelphi(JSONObject jsonObject) {
        Date d1Enviar = Calendario.hoje();
        RetornoEnvioNotaFiscalTO retornoEnvioNotaFiscalTO = new RetornoEnvioNotaFiscalTO();
        try {
            String retorno = executaRequisicaoRestDelphi(DELPHI_GRAVARNFSe, jsonObject.toString());

            JSONObject obj = new JSONObject(retorno);
            retornoEnvioNotaFiscalTO.setIdLote(obj.getInt("Result"));
            retornoEnvioNotaFiscalTO.setMensagem(obj.getString("mensagemRetorno"));
            retornoEnvioNotaFiscalTO.setResultadoEnvio(ResultadoEnvioNFSeEnum.getTipo(obj.getInt("resultado")));
        } catch (Exception e) {
            retornoEnvioNotaFiscalTO.setResultadoEnvio(ResultadoEnvioNFSeEnum.ERROINESPERADO);
            retornoEnvioNotaFiscalTO.setMensagem(e.getMessage());

        }
        Date d2Enviar = Calendario.hoje();
        Uteis.logar(null, "Tempo Enviar Notas: " + (d2Enviar.getTime() - d1Enviar.getTime()));
        return retornoEnvioNotaFiscalTO;
    }

    private String executaRequisicaoRestDelphi(String metodo, String json) throws IOException {
        Map<String, String> header = new HashMap<String, String>();
        header.put("Content-Type", "application/json");
        return ExecuteRequestHttpService.executeHttpRequestNFSe(Uteis.getUrlServiceNFSeRest() + metodo, header, json, ExecuteRequestHttpService.METODO_POST, "UTF-8", "UTF-8");
    }

    private RetornoEnvioNotaFiscalTO enviarRetornoNFCeDelphi(NotaFiscalConsumidorEletronicaVO notaVO) throws Exception {
        RetornoEnvioNotaFiscalTO retornoEnvioNotaFiscalTO = realizarEnvioNFCe(notaVO.getJsonEnviar(), notaVO.getEmpresa());
        if (retornoEnvioNotaFiscalTO.getResultadoEnvio().equals(ResultadoEnvioNFSeEnum.OK)) {
            NotaFiscalConsumidorEletronica notaFiscalConsumidorEletronicaDAO = new NotaFiscalConsumidorEletronica(con);
            notaFiscalConsumidorEletronicaDAO.atualizarIdNFCe(SituacaoNotaFiscalEnum.PROCESSADA, retornoEnvioNotaFiscalTO.getIdLote(), notaVO);
            notaFiscalConsumidorEletronicaDAO = null;
            retornoEnvioNotaFiscalTO.setMensagem("Dados enviados com sucesso.");
        }
        return retornoEnvioNotaFiscalTO;
    }

    private RetornoEnvioNotaFiscalTO realizarEnvioNFCe(String json, EmpresaVO empresaVO) {
        RetornoEnvioNotaFiscalTO retornoEnvioNotaFiscalTO = new RetornoEnvioNotaFiscalTO();
        try {
            Uteis.logar(null, "Solicitar gravarNFCe CNPJ: " + Formatador.removerMascara(empresaVO.getCNPJ()));
            String retorno = executaRequisicaoRestDelphi(DELPHI_GRAVARNFCe, json);

            JSONObject obj = new JSONObject(retorno);
            retornoEnvioNotaFiscalTO.setIdLote(obj.getInt("Result"));
            retornoEnvioNotaFiscalTO.setMensagem(obj.getString("mensagemRetorno"));
            retornoEnvioNotaFiscalTO.setResultadoEnvio(ResultadoEnvioNFSeEnum.getTipo(obj.getInt("resultado")));
        } catch (Exception e) {
            retornoEnvioNotaFiscalTO.setResultadoEnvio(ResultadoEnvioNFSeEnum.ERROINESPERADO);
            retornoEnvioNotaFiscalTO.setMensagem(e.getMessage());

        }
        return retornoEnvioNotaFiscalTO;
    }

    public void incluirLogEmisao(String json, Integer empresa) {
        try {
            String sql = "INSERT INTO logemissaonotafiscal(dataRegistro, json, empresa) VALUES (?, ?, ?)";
            PreparedStatement sqlInserir = con.prepareStatement(sql);
            sqlInserir.setTimestamp(1, Uteis.getDataJDBCTimestamp(Calendario.hoje()));
            sqlInserir.setString(2, json);
            sqlInserir.setInt(3, empresa);
            sqlInserir.execute();
        } catch (Exception ignored) {
        }
    }

    public NotaProcessarTO gerarNotaReciboPagamento(TipoNotaFiscalEnum tipoNotaFiscalEnum,
                                                    ReciboPagamentoVO reciboPagamentoVO,
                                                    UsuarioVO usuarioVO, String chave) throws Exception {
        NotaProcessarTO notaProcessarTO = new NotaProcessarTO(chave);
        notaProcessarTO.setReciboPagamentoVO(reciboPagamentoVO);
        processarNotasProcessar(tipoNotaFiscalEnum, notaProcessarTO, usuarioVO, null, true);
        enviarNotasAguardando();
        return notaProcessarTO;
    }

    public NotaProcessarTO gerarNotaMovConta(TipoNotaFiscalEnum tipoNotaFiscalEnum,
                                             MovContaVO movContaVO,
                                             UsuarioVO usuarioVO, String chave) throws Exception {
        NotaProcessarTO notaProcessarTO = new NotaProcessarTO(chave);
        notaProcessarTO.setMovContaVO(movContaVO);
        processarNotasProcessar(tipoNotaFiscalEnum, notaProcessarTO, usuarioVO, null, true);
        enviarNotasAguardando();
        return notaProcessarTO;
    }

    public NotaProcessarTO gerarNotaMovConta(TipoNotaFiscalEnum tipoNotaFiscalEnum,
                                             MovContaVO movContaVO,
                                             UsuarioVO usuarioVO, String chave, Date dataEmissao) throws Exception {
        NotaProcessarTO notaProcessarTO = new NotaProcessarTO(chave);
        notaProcessarTO.setMovContaVO(movContaVO);
        notaProcessarTO.setDataEmissaoPrevista(dataEmissao);
        processarNotasProcessar(tipoNotaFiscalEnum, notaProcessarTO, usuarioVO, null, true);
        enviarNotasAguardando();
        return notaProcessarTO;
    }

    public NotaProcessarTO gerarNotaCartaoCredito(TipoNotaFiscalEnum tipoNotaFiscalEnum,
                                                  CartaoCreditoVO cartaoCreditoVO,
                                                  UsuarioVO usuarioVO, String chave) throws Exception {
        NotaProcessarTO notaProcessarTO = new NotaProcessarTO(chave);
        notaProcessarTO.setCartaoCreditoVO(cartaoCreditoVO);
        processarNotasProcessar(tipoNotaFiscalEnum, notaProcessarTO, usuarioVO, null, true);
        enviarNotasAguardando();
        return notaProcessarTO;
    }

    public NotaProcessarTO gerarNotaCheque(TipoNotaFiscalEnum tipoNotaFiscalEnum,
                                           ChequeVO chequeVO,
                                           UsuarioVO usuarioVO, String chave) throws Exception {
        NotaProcessarTO notaProcessarTO = new NotaProcessarTO(chave);
        notaProcessarTO.setChequeVO(chequeVO);
        processarNotasProcessar(tipoNotaFiscalEnum, notaProcessarTO, usuarioVO, null, true);
        enviarNotasAguardando();
        return notaProcessarTO;
    }

    public NotaProcessarTO gerarNotaMovPagamento(TipoNotaFiscalEnum tipoNotaFiscalEnum,
                                                 MovPagamentoVO movPagamentoVO,
                                                 UsuarioVO usuarioVO, String chave) throws Exception {
        NotaProcessarTO notaProcessarTO = new NotaProcessarTO(chave);
        notaProcessarTO.setMovPagamentoVO(movPagamentoVO);
        processarNotasProcessar(tipoNotaFiscalEnum, notaProcessarTO, usuarioVO, null, true);
        enviarNotasAguardando();
        return notaProcessarTO;
    }

    public NotaProcessarTO gerarNotaMovProduto(TipoNotaFiscalEnum tipoNotaFiscalEnum,
                                               MovProdutoVO movProdutoVO,
                                               UsuarioVO usuarioVO, String chave) throws Exception {
        NotaProcessarTO notaProcessarTO = new NotaProcessarTO(chave);
        notaProcessarTO.setMovProdutoVO(movProdutoVO);
        processarNotasProcessar(tipoNotaFiscalEnum, notaProcessarTO, usuarioVO, null, true);
        enviarNotasAguardando();
        return notaProcessarTO;
    }

    private String obterTipoProdutoEmissao(Integer empresa, TipoNotaFiscalEnum tipoNotaFiscalEnum) throws Exception {
        EmpresaVO empresaVO = obterEmpresaVO(empresa);

        String tipoProdutoEmissao = empresaVO.getTipoProdutoEmissaoNFSe();
        if (tipoNotaFiscalEnum.equals(TipoNotaFiscalEnum.NFCE)) {
            tipoProdutoEmissao = empresaVO.getTipoProdutoEmissaoNFCe();

            //se estiver em branco quer dizer que são todos!!! by Luiz Felipe
            if (UteisValidacao.emptyString(tipoProdutoEmissao)) {
                StringBuilder tipos = new StringBuilder();
                for (TipoProduto tipoProduto : TipoProduto.values()) {
                    if (tipos.toString().equals("")) {
                        tipos.append(tipoProduto.getCodigo());
                    } else {
                        tipos.append("|").append(tipoProduto.getCodigo());
                    }
                }
                tipoProdutoEmissao = tipos.toString();
            }
        }
        return tipoProdutoEmissao;
    }

    private String obterDescricaoTipoFormaPagamento(String sigla) {
        if (sigla == null) {
            return "";
        }
        if (sigla.equals("CA")) {
            return "Cartão de Crédito";
        }
        if (sigla.equals("CD")) {
            return "Cartão de Débito";
        }
        if (sigla.equals("CH")) {
            return "Cheque";
        }
        if (sigla.equals("AV")) {
            return "Dinheiro";
        }
        if (sigla.equals("CC")) {
            return "Conta Corrente";
        }
        if (sigla.equals("BB")) {
            return "Boleto Bancario";
        }
        if (sigla.equals("CO")) {
            return "Convenio";
        }
        if (sigla.equals("PD")) {
            return "Pagamento Digital";
        }
        if (sigla.equals("LO")) {
            return "Lote";
        }
        return "";
    }

    private List<NotaFiscalVO> consultarNotasFamiliaAguardandoEnvioEnotas() throws Exception {
        String sql = "SELECT * FROM notafiscal WHERE coalesce(sequencialFamilia,0) > 0 and UPPER(statusNota) = ? order by codigo ";
        PreparedStatement stm = con.prepareStatement(sql);
        stm.setString(1, StatusEnotasEnum.GERADA.getDescricaoEnotas().toUpperCase());
        ResultSet tabelaResultado = stm.executeQuery();
        List<NotaFiscalVO> notas = new ArrayList<>();
        while (tabelaResultado.next()) {
            notas.add(montarDados(tabelaResultado, Uteis.NIVELMONTARDADOS_DADOSBASICOS, con));
        }
        return notas;
    }

    private void realizarEnvioEnotasNFSeFamilia() {
        try {

            List<NotaFiscalVO> listaNotas = consultarNotasFamiliaAguardandoEnvioEnotas();

            if (UteisValidacao.emptyList(listaNotas)) {
                return;
            }

            Uteis.logar(null, "### ENVIAR Grupo eNotas | NFSe | Total Notas " + listaNotas.size());

            NFSeEmitida nfseDAO = new NFSeEmitida(con);
            EnotasService enotasService = new EnotasService(con);

            for (NotaFiscalVO notaFiscalVO : listaNotas) {
                try {
                    nfseDAO.atualizarSituacaoPorSequencialFamilia(SituacaoNotaFiscalEnum.ENVIADA, notaFiscalVO.getSequencialfamilia());

                    StatusEnotasEnum statusEnotasEnum = StatusEnotasEnum.AGUARDANDOAUTORIZACAO;
                    atualizarStatusNota(statusEnotasEnum, notaFiscalVO);

                    boolean enviou = enotasService.enviarNotaFiscalFamilia(notaFiscalVO);

                    if (!enviou) {
                        nfseDAO.atualizarSituacaoPorSequencialFamilia(SituacaoNotaFiscalEnum.GERADA, notaFiscalVO.getSequencialfamilia());
                        atualizarStatusNota(StatusEnotasEnum.GERADA, notaFiscalVO);
                    } else {
                        NotaFiscalHistorico histDAO = new NotaFiscalHistorico(con);
                        histDAO.gerarHistoricoSemCommit(statusEnotasEnum.getDescricaoEnotas(), "ENVIADA", notaFiscalVO.getIdPacto(), notaFiscalVO.getCodigo(), null);
                        histDAO = null;
                    }
                } catch (Exception ex) {
                    ex.printStackTrace();
                }
            }
            enotasService = null;
            nfseDAO = null;
        } catch (Exception ex) {
            Uteis.logar(true, null, "ERRO realizarEnvioEnotasNFSeGrupo: " + ex.getMessage());
        }
    }

    public void retentativaEnvioEnotasNFSe() {
        HttpURLConnection connection;
        URL url;
        List<String> listaCodigosNotasAtualizar = new ArrayList<>();
        String urlBaseConsulta = Uteis.getUrlServicoNotaFiscal() + "/nf/consultar?chave=%s&notafiscal=%s";
        String sql = "UPDATE nfseemitida SET situacaonotafiscal = 1  WHERE codigo in (%s)";

        try {
            NFSeEmitida nfseDAO = new NFSeEmitida(con);
            List<NFSeEmitidaVO> listaNfeEmitidasComSituacaoEnviada = nfseDAO.consultarNotasEnviadasEnotas();

            for (NFSeEmitidaVO nfe : listaNfeEmitidasComSituacaoEnviada) {
                JSONObject jsonEnviar = new JSONObject(nfe.getJsonEnviar());
                String chave = jsonEnviar.optString("chave", null);
                Integer notafiscal = nfe.getNotaFiscalVO().getCodigo();

                if (chave == null) {
                    Uteis.logar("Não foi encontrado a chave no json da nota", jsonEnviar);
                    continue;
                }

                url = new URL(String.format(urlBaseConsulta, chave, notafiscal));
                connection = (HttpURLConnection) url.openConnection();
                connection.setRequestMethod("GET");

                if (connection.getResponseCode() == 404) {
                    listaCodigosNotasAtualizar.add(nfe.getCodigo().toString());
                } else {
                    listaNfeEmitidasComSituacaoEnviada.remove(nfe);
                }
            }

            if (listaCodigosNotasAtualizar != null && listaCodigosNotasAtualizar.size() > 0) {
                String codigosNotasAtualizar = String.join(",", listaCodigosNotasAtualizar);
                sql = String.format(sql, codigosNotasAtualizar);
                Statement update = con.createStatement();
                update.executeUpdate(sql);

                realizarEnvioEnotasNFSe(listaNfeEmitidasComSituacaoEnviada);
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    private void realizarEnvioEnotasNFSe() {
        realizarEnvioEnotasNFSe(null);
    }

    private void realizarEnvioEnotasNFSe(List<NFSeEmitidaVO> nfSeEmitidaVOList) {
        try {

            NFSeEmitida nfseDAO = new NFSeEmitida(con);

            List<NFSeEmitidaVO> lista;
            if (nfSeEmitidaVOList != null && nfSeEmitidaVOList.size() > 0) {
                lista = nfSeEmitidaVOList;
            } else {
                lista = nfseDAO.consultarNotasAguardandoEnvioEnotas();
            }
            if (UteisValidacao.emptyList(lista)) {
                nfseDAO = null;
                return;
            }

            Uteis.logar(null, "### ENVIAR eNotas | NFSe | Total Notas " + lista.size());

            EnotasService enotasService = new EnotasService(con);
            for (NFSeEmitidaVO obj : lista) {
                try {
                    obj.setSituacaoNotaFiscal(SituacaoNotaFiscalEnum.ENVIADA);
                    nfseDAO.atualizarSituacao(obj.getSituacaoNotaFiscal(), obj.getCodigo());
                    Logger.getLogger(NotaFiscal.class.getName()).log(Level.SEVERE,
                            "#### NOTA FISCAL =" + obj.getNotaFiscalVO().getCodigo() +
                                 "; NFSE EMITIDA:" + obj.getCodigo() +
                                 "; SITUAÇÃO: " + obj.getSituacaoNotaFiscal().getDescricao() +
                                 "; STATUS: " + obj.getNotaFiscalVO().getStatusNota());

                    StatusEnotasEnum statusEnotasEnum = StatusEnotasEnum.AGUARDANDOAUTORIZACAO;
                    atualizarStatusNotaNFSe(statusEnotasEnum, obj);
                    Logger.getLogger(NotaFiscal.class.getName()).log(Level.SEVERE,
                            "#### NOTA FISCAL =" + obj.getNotaFiscalVO().getCodigo() +
                                    "; NFSE EMITIDA:" + obj.getCodigo() +
                                    "; SITUAÇÃO: " + obj.getSituacaoNotaFiscal().getDescricao() +
                                    "; STATUS: " + obj.getNotaFiscalVO().getStatusNota());

                    enotasService.enviarNotaFiscalNFSe(obj);
                    Logger.getLogger(NotaFiscal.class.getName()).log(Level.SEVERE,
                            "#### NOTA FISCAL =" + obj.getNotaFiscalVO().getCodigo() +
                                    "; NFSE EMITIDA:" + obj.getCodigo() +
                                    "; SITUAÇÃO: " + obj.getSituacaoNotaFiscal().getDescricao() +
                                    "; STATUS: " + obj.getNotaFiscalVO().getStatusNota() +
                                    "; LOG APÓS ENVIO SERVIÇO DE NOTAS.");
                    if (obj.getSituacaoNotaFiscal().equals(SituacaoNotaFiscalEnum.GERADA)) {
                        nfseDAO.atualizarSituacao(obj.getSituacaoNotaFiscal(), obj.getCodigo());
                        atualizarStatusNotaNFSe(StatusEnotasEnum.GERADA, obj);
                        Logger.getLogger(NotaFiscal.class.getName()).log(Level.SEVERE,
                                "#### NOTA FISCAL =" + obj.getNotaFiscalVO().getCodigo() +
                                        "; NFSE EMITIDA:" + obj.getCodigo() +
                                        "; SITUAÇÃO: " + obj.getSituacaoNotaFiscal().getDescricao() +
                                        "; STATUS: " + obj.getNotaFiscalVO().getStatusNota() +
                                        "; DEVE ESTAR COMO SITUAÇÃO GERADA.");
                    } else {
                        Logger.getLogger(NotaFiscal.class.getName()).log(Level.SEVERE,
                                "#### NOTA FISCAL =" + obj.getNotaFiscalVO().getCodigo() +
                                        "; NFSE EMITIDA:" + obj.getCodigo() +
                                        "; SITUAÇÃO: " + obj.getSituacaoNotaFiscal().getDescricao() +
                                        "; STATUS: " + obj.getNotaFiscalVO().getStatusNota() +
                                        "; ENTRADA NO HISTÓRICO.");
                        NotaFiscalHistorico histDAO = new NotaFiscalHistorico(con);
                        histDAO.gerarHistoricoSemCommit(statusEnotasEnum.getDescricaoEnotas(), "ENVIADA", obj.getIdReferencia(), null, null);
                        histDAO = null;
                    }
                } catch (Exception ex) {
                    ex.printStackTrace();
                    Logger.getLogger(NotaFiscal.class.getName()).log(Level.SEVERE,
                            "#### NOTA FISCAL =" + obj.getNotaFiscalVO().getCodigo() +
                                    "; NFSE EMITIDA:" + obj.getCodigo() +
                                    "; SITUAÇÃO: " + obj.getSituacaoNotaFiscal().getDescricao() +
                                    "; STATUS: " + obj.getNotaFiscalVO().getStatusNota() +
                                    "; EXCEPTION: " + ex.getMessage());
                }
            }
            enotasService = null;
            nfseDAO = null;
        } catch (Exception ex) {
            Uteis.logar(true, null, "ERRO realizarEnvioEnotasNFSe: " + ex.getMessage());
        }
    }

    private void realizarEnvioEnotasNFCe() {
        try {

            NotaFiscalConsumidorEletronica notaDAO = new NotaFiscalConsumidorEletronica(con);

            List<NotaFiscalConsumidorEletronicaVO> lista = notaDAO.consultarNotasAguardandoEnvioEnotas();
            if (UteisValidacao.emptyList(lista)) {
                notaDAO = null;
                return;
            }

            Uteis.logar(null, "### ENVIAR eNotas | NFCe | Total Notas " + lista.size());

            EnotasService enotasService = new EnotasService(con);
            for (NotaFiscalConsumidorEletronicaVO obj : lista) {
                try {
                    obj.setSituacaoNotaFiscal(SituacaoNotaFiscalEnum.ENVIADA);
                    notaDAO.atualizarSituacao(obj.getSituacaoNotaFiscal(), obj.getCodigo());

                    StatusEnotasEnum statusEnotasEnum = StatusEnotasEnum.AGUARDANDOAUTORIZACAO;
                    atualizarStatusNotaNFCe(statusEnotasEnum, obj);

                    enotasService.enviarNotaFiscalNFCe(obj);
                    if (obj.getSituacaoNotaFiscal().equals(SituacaoNotaFiscalEnum.GERADA)) {
                        notaDAO.atualizarSituacao(obj.getSituacaoNotaFiscal(), obj.getCodigo());
                        atualizarStatusNotaNFCe(StatusEnotasEnum.GERADA, obj);
                    } else {
                        NotaFiscalHistorico histDAO = new NotaFiscalHistorico(con);
                        histDAO.gerarHistoricoSemCommit(statusEnotasEnum.getDescricaoEnotas(), "ENVIADA", obj.getIdReferencia(), null, null);
                        histDAO = null;
                    }
                } catch (Exception ex) {
                    ex.printStackTrace();
                }

            }
            enotasService = null;
            notaDAO = null;
        } catch (Exception ex) {
            Uteis.logar(true, null, "ERRO realizarEnvioEnotasNFCe: " + ex.getMessage());
        }
    }

    public void incluirNotaFiscalWebHookHistorico(String retorno, String heareds) throws Exception {
        String sql = "INSERT INTO NotaFiscalWebHookHistorico (dataRegistro, retorno, header) VALUES (?, ?, ?)";
        int i = 0;
        try (PreparedStatement sqlInserir = con.prepareStatement(sql)) {
            sqlInserir.setTimestamp(++i, Uteis.getDataJDBCTimestamp(Calendario.hoje()));
            sqlInserir.setString(++i, retorno);
            sqlInserir.setString(++i, heareds);
            sqlInserir.execute();
        }
    }

    public void criarEstruturaEnotas() {
        try {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("CREATE TABLE NotaFiscalWebHookHistorico\n" +
                    "(codigo serial PRIMARY KEY,\n" +
                    "dataRegistro timestamp without time zone, \n" +
                    "retorno text);", con);

            SuperFacadeJDBC.executarUpdateExecutarProcessos("DROP TABLE notafiscal;", con);

            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE nfseEmitida add column sequencialFamilia INTEGER;", con);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE nfseEmitidahistorico add column sequencialFamilia INTEGER;", con);

            SuperFacadeJDBC.executarUpdateExecutarProcessos("CREATE TABLE NotaFiscal\n" +
                    "(\n" +
                    "codigo serial PRIMARY KEY,\n" +
                    "dataRegistro timestamp without time zone, \n" +
                    "dataEmissao timestamp without time zone, \n" +
                    "dataAutorizacao timestamp without time zone, \n" +
                    "IdEmpresaEnotas CHARACTER VARYING(255),\n" +
                    "tipo INTEGER,\n" +
                    "empresa INTEGER,\n" +
                    "usuario INTEGER,\n" +
                    "pessoa INTEGER,\n" +
                    "idExterno CHARACTER VARYING(255),\n" +
                    "idReferencia CHARACTER VARYING(255),\n" +
                    "jsonNota text,\n" +
                    "jsonEnvio text,\n" +
                    "jsonRetorno text,\n" +
                    "jsonEnvioCancelamento text,\n" +
                    "jsonRetornoCancelamento text,\n" +
                    "jsonEnvioInutilizar text,\n" +
                    "jsonRetornoInutilizar text,\n" +
                    "statusNota CHARACTER VARYING(150),\n" +
                    "nomeCliente CHARACTER VARYING(150),\n" +
                    "razaoSocial CHARACTER VARYING(150),\n" +
                    "cpfCNPJ CHARACTER VARYING(20),\n" +
                    "numeroNota CHARACTER VARYING(255),\n" +
                    "chaveAcesso CHARACTER VARYING(255),\n" +
                    "ConfiguracaoNotaFiscal INTEGER,\n" +
                    "nfseEmitida INTEGER,\n" +
                    "notaFiscalConsumidorEletronica INTEGER,\n" +
                    "sequencialfamilia INTEGER,\n" +
                    "excluido boolean default false,\n" +
                    "CONSTRAINT fk_NotaFiscal_Empresa FOREIGN KEY (empresa) \n" +
                    "REFERENCES empresa (codigo) MATCH SIMPLE \n" +
                    "ON UPDATE NO ACTION ON DELETE RESTRICT" +
                    ");", con);

            SuperFacadeJDBC.executarUpdateExecutarProcessos("CREATE TABLE NotaFiscalOperacao\n" +
                    "(\n" +
                    "codigo serial PRIMARY KEY,\n" +
                    "dataRegistro timestamp without time zone, \n" +
                    "operacao INTEGER,\n" +
                    "chave CHARACTER VARYING(255),\n" +
                    "IdEmpresaEnotas CHARACTER VARYING(255),\n" +
                    "usuario INTEGER,\n" +
                    "notafiscal INTEGER,\n" +
                    "justificativa text,\n" +
                    "status INTEGER,\n" +
                    "jsonRetorno text,\n" +
                    "statusRetorno CHARACTER VARYING(255),\n" +
                    "numeroInicial INTEGER,\n" +
                    "numeroFinal INTEGER,\n" +
                    "CONSTRAINT fk_NotaFiscalOperacao_NotaFiscal FOREIGN KEY (notafiscal) \n" +
                    "REFERENCES notafiscal (codigo) MATCH SIMPLE \n" +
                    "ON UPDATE NO ACTION ON DELETE RESTRICT" +
                    ");", con);

            SuperFacadeJDBC.executarUpdateExecutarProcessos("CREATE TABLE NotaFiscalHistorico\n" +
                    "(\n" +
                    "codigo serial PRIMARY KEY,\n" +
                    "dataRegistro timestamp without time zone, \n" +
                    "descricao CHARACTER VARYING(255),\n" +
                    "observacao CHARACTER VARYING(255),\n" +
                    "usuario INTEGER,\n" +
                    "notafiscal INTEGER,\n" +
                    "CONSTRAINT fk_NotaFiscalHistorico_NotaFiscal FOREIGN KEY (notafiscal) \n" +
                    "REFERENCES notafiscal (codigo) MATCH SIMPLE \n" +
                    "ON UPDATE NO ACTION ON DELETE RESTRICT" +
                    ");", con);

            SuperFacadeJDBC.executarUpdateExecutarProcessos("CREATE TABLE NotaFiscalFamilia\n" +
                    "(\n" +
                    "codigo serial PRIMARY KEY,\n" +
                    "sequencialFamilia INTEGER,\n" +
                    "nfseEmitida INTEGER,\n" +
                    "notafiscal INTEGER,\n" +
                    "CONSTRAINT fk_NotaFiscalFamilia_NotaFiscal FOREIGN KEY (notafiscal) \n" +
                    "REFERENCES notafiscal (codigo) MATCH SIMPLE \n" +
                    "ON UPDATE NO ACTION ON DELETE RESTRICT" +
                    ");", con);

        } catch (Exception ex) {
            Uteis.logar(null, "Erro criarEstruturaEnotas! | " + ex.getMessage());
        }
    }

    public List<NotaFiscalVO> consultarNotasFiscais(FiltroNotaFiscalTO filtroNotaFiscalTO,
                                                    Integer limit, Integer offset) throws Exception {
        try {
            CacheControl.toggleCache(ConfiguracaoNotaFiscal.class, true);
            CacheControl.toggleCache(Cidade.class, true);
            CacheControl.toggleCache(Estado.class, true);
            CacheControl.toggleCache(Pais.class, true);

            StringBuilder sqlStr = new StringBuilder();
            sqlStr.append("SELECT \n");
            sqlStr.append("u.nome as usuario_nome, \n");
            sqlStr.append("n.* \n");
            sqlStr.append("FROM NotaFiscal n \n");
            sqlStr.append("LEFT JOIN usuario u on u.codigo = n.usuario \n");
            sqlStr.append("WHERE 1 = 1 \n");

            sqlStr.append(obterSQLFiltrosConsultaNotas(filtroNotaFiscalTO));

            sqlStr.append(" ORDER BY n.dataRegistro desc \n");
            if (!UteisValidacao.emptyNumber(limit)) {
                sqlStr.append(" limit ").append(limit).append(" offset ").append(offset);
            }

            Statement stm = con.createStatement();
            ResultSet rs = stm.executeQuery(sqlStr.toString());
            return montarDadosConsulta(rs, Uteis.NIVELMONTARDADOS_DADOSENTIDADESPRINCIPAIS, con);
        } finally {
            CacheControl.clear();
        }
    }

    public List<NotaFiscalVO> consultarNotasFiscaisSincronizar(FiltroNotaFiscalTO filtroNotaFiscalTO) throws Exception {
        try {
            CacheControl.toggleCache(ConfiguracaoNotaFiscal.class, true);
            CacheControl.toggleCache(Cidade.class, true);
            CacheControl.toggleCache(Estado.class, true);
            CacheControl.toggleCache(Pais.class, true);

            StringBuilder sqlStr = new StringBuilder();
            sqlStr.append("SELECT \n");
            sqlStr.append("u.nome as usuario_nome, \n");
            sqlStr.append("n.* \n");
            sqlStr.append("FROM NotaFiscal n \n");
            sqlStr.append("LEFT JOIN usuario u on u.codigo = n.usuario \n");
            sqlStr.append("WHERE n.statusnota not ilike '").append(StatusEnotasEnum.AUTORIZADA.getDescricaoEnotas().toUpperCase()).append("' \n");

            sqlStr.append(obterSQLFiltrosConsultaNotas(filtroNotaFiscalTO));

            sqlStr.append(" ORDER BY n.dataRegistro desc \n");

            Statement stm = con.createStatement();
            ResultSet rs = stm.executeQuery(sqlStr.toString());
            return montarDadosConsulta(rs, Uteis.NIVELMONTARDADOS_DADOSENTIDADESPRINCIPAIS, con);
        } finally {
            CacheControl.clear();
        }
    }

    public List<NotaFiscalVO> consultarNotasFiscais(FiltroNotaFiscalTO filtroNotaFiscalTO, Set<Integer> codigos, int nivelMontarDados) throws Exception {
        try {
            CacheControl.toggleCache(ConfiguracaoNotaFiscal.class, true);
            CacheControl.toggleCache(Cidade.class, true);
            CacheControl.toggleCache(Estado.class, true);
            CacheControl.toggleCache(Pais.class, true);

            StringBuilder sqlStr = new StringBuilder();
            sqlStr.append("SELECT \n");
            sqlStr.append("u.nome as usuario_nome, \n");
            sqlStr.append("n.*, ne.contrato , sw.matricula \n");
            sqlStr.append("FROM NotaFiscal n \n");
            sqlStr.append("LEFT JOIN usuario u on u.codigo = n.usuario \n");
            sqlStr.append("LEFT join nfseemitida ne on ne.codigo = n.nfseemitida  \n");
            sqlStr.append("LEFT JOIN situacaoclientesinteticodw sw on n.pessoa = sw.codigopessoa  \n");
            sqlStr.append("WHERE 1 = 1 \n");
            sqlStr.append(obterSQLFiltrosConsultaNotas(filtroNotaFiscalTO));

            if (!UteisValidacao.emptyList(codigos)) {
                StringBuilder list = new StringBuilder();
                for (Integer cod : codigos) {
                    list.append(",").append(cod);
                }
                sqlStr.append(" AND n.codigo in (").append(list.toString().replaceFirst(",", "")).append(") \n");
            }

            Statement stm = con.createStatement();
            ResultSet rs = stm.executeQuery(sqlStr.toString());
            return montarDadosConsulta(rs, nivelMontarDados, con);
        } finally {
            CacheControl.clear();
        }
    }

    private StringBuilder obterSQLFiltrosConsultaNotas(FiltroNotaFiscalTO filtroNotaFiscalTO) {
        StringBuilder sqlStr = new StringBuilder();
        sqlStr.append(" \n");

        if (!filtroNotaFiscalTO.getTipoNota().equals(TipoNotaFiscalEnum.TODAS.getCodigo())) {
            sqlStr.append(" AND n.tipo = ").append(filtroNotaFiscalTO.getTipoNota()).append(" \n");
        }

//        if (!UteisValidacao.emptyString(filtroNotaFiscalTO.getIdEmpresaEnotas())) {
//            sqlStr.append(" AND IdEmpresaEnotas ilike '").append(filtroNotaFiscalTO.getIdEmpresaEnotas()).append("' \n");
//        }

        if (!UteisValidacao.emptyNumber(filtroNotaFiscalTO.getConfiguracaoNotaFiscal())) {
            sqlStr.append(" AND n.configuracaonotafiscal = ").append(filtroNotaFiscalTO.getConfiguracaoNotaFiscal()).append(" \n");
        }

        if (filtroNotaFiscalTO.getDataAutorizacaoInicio() != null) {
            sqlStr.append(" AND n.dataAutorizacao::date >= '").append(Uteis.getDataFormatoBD(filtroNotaFiscalTO.getDataAutorizacaoInicio())).append("' \n");
        }
        if (filtroNotaFiscalTO.getDataAutorizacaoFim() != null) {
            sqlStr.append(" AND n.dataAutorizacao::date <= '").append(Uteis.getDataFormatoBD(filtroNotaFiscalTO.getDataAutorizacaoFim())).append("' \n");
        }

        if (filtroNotaFiscalTO.getDataEmissaoInicio() != null) {
            sqlStr.append(" AND n.dataEmissao::date >= '").append(Uteis.getDataFormatoBD(filtroNotaFiscalTO.getDataEmissaoInicio())).append("' \n");
        }
        if (filtroNotaFiscalTO.getDataEmissaoFim() != null) {
            sqlStr.append(" AND n.dataEmissao::date <= '").append(Uteis.getDataFormatoBD(filtroNotaFiscalTO.getDataEmissaoFim())).append("' \n");
        }

        if (filtroNotaFiscalTO.getDataRegistroInicio() != null) {
            sqlStr.append(" AND n.dataregistro::date >= '").append(Uteis.getDataFormatoBD(filtroNotaFiscalTO.getDataRegistroInicio())).append("' \n");
        }
        if (filtroNotaFiscalTO.getDataRegistroFim() != null) {
            sqlStr.append(" AND n.dataregistro::date <= '").append(Uteis.getDataFormatoBD(filtroNotaFiscalTO.getDataRegistroFim())).append("' \n");
        }

        if (!filtroNotaFiscalTO.isApresentarExcluidas()) {
            sqlStr.append(" AND n.excluido = false \n");
        }

        if (!UteisValidacao.emptyString(filtroNotaFiscalTO.getStatusNota())) {
            sqlStr.append(" AND n.statusNota ilike '").append(filtroNotaFiscalTO.getStatusNota()).append("' \n");
        }

        if (!UteisValidacao.emptyString(filtroNotaFiscalTO.getRazaoSocial())) {
            sqlStr.append(" AND (n.razaoSocial ilike '").append(filtroNotaFiscalTO.getRazaoSocial()).append("%' ");
            sqlStr.append(" OR n.nomecliente ilike '").append(filtroNotaFiscalTO.getRazaoSocial()).append("%') \n");
        }

        if (!UteisValidacao.emptyString(filtroNotaFiscalTO.getCpfCnpjMascarado(false))) {
            sqlStr.append(" AND (n.cpfcnpj ilike '").append(filtroNotaFiscalTO.getCpfCnpjMascarado(false)).append("' ");
            sqlStr.append(" OR n.cpfcnpj ilike '").append(filtroNotaFiscalTO.getCpfCnpjMascarado(true)).append("') \n");
        }

        if (!UteisValidacao.emptyString(filtroNotaFiscalTO.getNumeroNota())) {
            sqlStr.append(" AND n.numeronota ilike '").append(filtroNotaFiscalTO.getNumeroNota()).append("' \n");
        }

        if (!UteisValidacao.emptyNumber(filtroNotaFiscalTO.getCodNotaFiscal())) {
            sqlStr.append(" AND n.codigo = ").append(filtroNotaFiscalTO.getCodNotaFiscal()).append(" \n");
        }

        if (!UteisValidacao.emptyNumber(filtroNotaFiscalTO.getEmpresa())) {
            sqlStr.append(" AND n.empresa = ").append(filtroNotaFiscalTO.getEmpresa()).append(" \n");
        }

        if (filtroNotaFiscalTO.isApresentarNotaAlteradaStatusManual()) {
            sqlStr.append(" AND n.alteradostatusmanual \n");
        }

        return sqlStr;
    }

    public List<TotalizadorNotaFiscalTO> consultarNotasFiscaisTotalizador(FiltroNotaFiscalTO filtroNotaFiscalTO) throws Exception {
        StringBuilder sqlStr = new StringBuilder();
        sqlStr.append("SELECT \n");
        sqlStr.append("n.statusnota as status,\n");
        sqlStr.append("count(n.codigo) as qtd,\n");
        sqlStr.append("sum(n.valor::numeric) as total \n");
        sqlStr.append("FROM NotaFiscal n \n");
        sqlStr.append("WHERE 1 = 1 \n");

        sqlStr.append(obterSQLFiltrosConsultaNotas(filtroNotaFiscalTO));

        sqlStr.append("group by 1 \n");
        sqlStr.append("order by 1 \n");

        List<TotalizadorNotaFiscalTO> lista = new ArrayList<>();
        try (PreparedStatement stm = con.prepareStatement(sqlStr.toString())) {
            try (ResultSet rs = stm.executeQuery()) {
                while (rs.next()) {
                    TotalizadorNotaFiscalTO dto = new TotalizadorNotaFiscalTO();
                    dto.setQuantidade(rs.getInt("qtd"));
                    dto.setValor(rs.getDouble("total"));
                    StatusEnotasEnum statusEnotasEnum = StatusEnotasEnum.obterPorDescricaoEnotas(rs.getString("status"));
                    dto.setDescricao(statusEnotasEnum.getDescricaoApresentar().toUpperCase());
                    dto.setCss(statusEnotasEnum.getCss());
                    dto.setTitle(statusEnotasEnum.getHint());
                    lista.add(dto);
                }
            }
        }
        return lista;
    }

    public Set<Integer> codigosNotas(FiltroNotaFiscalTO filtroNotaFiscalTO) throws SQLException {
        StringBuilder sqlStr = new StringBuilder();
        sqlStr.append("SELECT  \n");
        sqlStr.append("n.codigo as nota \n");
        sqlStr.append("FROM NotaFiscal n \n");
        sqlStr.append("WHERE 1 = 1 \n");
        sqlStr.append(obterSQLFiltrosConsultaNotas(filtroNotaFiscalTO));

        Set<Integer> notas = new HashSet<>();
        try (PreparedStatement stm = con.prepareStatement(sqlStr.toString())) {
            try (ResultSet rs = stm.executeQuery()) {
                while (rs.next()) {
                    notas.add(rs.getInt("nota"));
                }
            }
        }
        return notas;
    }

    public LayoutTelaNotaFiscalTO obterLayoutNotaFiscal(FiltroNotaFiscalTO filtroNotaFiscalTO) throws SQLException {
        StringBuilder sqlStr = new StringBuilder();
        sqlStr.append("SELECT  \n");
        sqlStr.append("count(n.codigo) as total, \n");
        sqlStr.append("sum (case when n.statusnota ilike 'Autorizada' then 1 else 0 end) as podeCancelar, \n");
        sqlStr.append("sum (case when n.tipo = 0 then 1 else 0 end) as tipoNFE, \n");
        sqlStr.append("sum (case when n.tipo = 1 then 1 else 0 end) as tipoNFSE, \n");
        sqlStr.append("sum (case when n.tipo = 2 then 1 else 0 end) as tipoNFCE \n");
        sqlStr.append("FROM NotaFiscal n \n");
        sqlStr.append("WHERE 1 = 1 \n");
        sqlStr.append(obterSQLFiltrosConsultaNotas(filtroNotaFiscalTO));

        try (PreparedStatement stm = con.prepareStatement(sqlStr.toString())) {
            try (ResultSet rs = stm.executeQuery()) {
                if (rs.next()) {
                    Integer total = rs.getInt("total");
                    Integer podeCancelar = rs.getInt("podeCancelar");
                    Integer tipoNFE = rs.getInt("tipoNFE");
                    Integer tipoNFSE = rs.getInt("tipoNFSE");
                    Integer tipoNFCE = rs.getInt("tipoNFCE");

                    LayoutTelaNotaFiscalTO dto = new LayoutTelaNotaFiscalTO();
                    dto.setApresentarCancelar(!UteisValidacao.emptyNumber(podeCancelar));
                    dto.setApresentarRPS(!UteisValidacao.emptyNumber(tipoNFSE));
                    dto.setApresentarInutilizar(!UteisValidacao.emptyNumber(total));

                    Set<Integer> tiposNota = new HashSet<>();
                    if (!UteisValidacao.emptyNumber(tipoNFE)) {
                        tiposNota.add(0);
                    }
                    if (!UteisValidacao.emptyNumber(tipoNFSE)) {
                        tiposNota.add(1);
                    }
                    if (!UteisValidacao.emptyNumber(tipoNFCE)) {
                        tiposNota.add(2);
                    }
                    dto.setApresentarFiltroTipoNota(tiposNota.size() > 1);
                    return dto;
                }
            }
        }
        return new LayoutTelaNotaFiscalTO();
    }

    public Double consultarValorTotal(Integer empresa, Integer configNotaFiscal, StatusEnotasEnum statusEnotasEnum, Date dataInicial, Date dataFinal) throws Exception {

        StringBuilder sqlStr = new StringBuilder();
        sqlStr.append("select  \n");
        sqlStr.append("sum(valor) as valorTotal \n");
        sqlStr.append("from notafiscal \n");
        sqlStr.append("where 1 = 1 \n");

        if (dataInicial != null) {
            sqlStr.append("AND dataEmissao::date >= '").append(Uteis.getDataFormatoBD(dataInicial)).append("' \n");
        }
        if (dataFinal != null) {
            sqlStr.append("AND dataEmissao::date <= '").append(Uteis.getDataFormatoBD(dataFinal)).append("' \n");
        }

        if (statusEnotasEnum != null) {
            sqlStr.append("AND statusNota ilike '").append(statusEnotasEnum.getDescricaoEnotas()).append("' \n");
        }

        if (!UteisValidacao.emptyNumber(empresa)) {
            sqlStr.append("AND empresa = ").append(empresa).append(" \n");
        }

        if (!UteisValidacao.emptyNumber(configNotaFiscal)) {
            sqlStr.append("AND configuracaonotafiscal = ").append(configNotaFiscal).append(" \n");
        }

        Statement stm = con.createStatement();
        ResultSet rs = stm.executeQuery(sqlStr.toString());
        if (rs.next()) {
            return rs.getDouble("valorTotal");
        }
        return 0.0;
    }

    public Map<String, Integer> obterMapa(Integer empresa, String tipoNotaFiscal,
                                          Date dataRegistroInicial, Date dataRegistroFinal) throws Exception {

        StringBuilder sqlStr = new StringBuilder();
        sqlStr.append("select  \n");
        sqlStr.append("statusnota, \n");
        sqlStr.append("count(codigo) as qtd  \n");
        sqlStr.append("from ( \n");
        sqlStr.append("select  \n");
        sqlStr.append("nf.codigo, \n");
        sqlStr.append("nf.tipo, \n");
        sqlStr.append("nf.statusnota, \n");
        sqlStr.append("nf.dataRegistro, \n");
        sqlStr.append("nf.empresa \n");
        sqlStr.append("from notafiscal nf \n");
        sqlStr.append("inner join nfseemitida ne on ne.codigo = nf.nfseemitida \n");
        sqlStr.append("union \n");
        sqlStr.append("select  \n");
        sqlStr.append("nf.codigo, \n");
        sqlStr.append("nf.tipo, \n");
        sqlStr.append("nf.statusnota, \n");
        sqlStr.append("nf.dataRegistro, \n");
        sqlStr.append("nf.empresa \n");
        sqlStr.append("from notafiscal nf \n");
        sqlStr.append("inner join notafiscalconsumidoreletronica nc on nc.codigo = nf.notafiscalconsumidoreletronica \n");
        sqlStr.append(") as sql \n");
        sqlStr.append("where 1 = 1 \n");

        if (dataRegistroInicial != null) {
            sqlStr.append(" AND dataRegistro::date >= '").append(Uteis.getDataFormatoBD(dataRegistroInicial)).append("' \n");
        }
        if (dataRegistroFinal != null) {
            sqlStr.append(" AND dataRegistro::date <= '").append(Uteis.getDataFormatoBD(dataRegistroFinal)).append("' \n");
        }

        if (!UteisValidacao.emptyString(tipoNotaFiscal)) {
            sqlStr.append(" AND tipo in (").append(tipoNotaFiscal).append(") \n");
        }

        if (!UteisValidacao.emptyNumber(empresa)) {
            sqlStr.append(" AND empresa = ").append(empresa).append(" \n");
        }

        sqlStr.append(" group by 1 \n");
        Statement stm = con.createStatement();
        ResultSet rs = stm.executeQuery(sqlStr.toString());
        Map<String, Integer> map = new HashMap<>();
        while (rs.next()) {
            map.put(rs.getString("statusnota"), rs.getInt("qtd"));
        }
        return map;
    }

    public Integer obterCodigoPorIdPacto(String idPacto) throws Exception {
        PreparedStatement sqlConsultar = con.prepareStatement("select codigo from notafiscal where (idPacto ilike '" + idPacto + "' or idreferencia ilike '" + idPacto + "') order by codigo desc limit 1");
        ResultSet rs = sqlConsultar.executeQuery();
        Integer codNotaFiscal = 0;
        if (rs.next()) {
            codNotaFiscal = rs.getInt("codigo");
        }

        if (UteisValidacao.emptyNumber(codNotaFiscal)) {
            throw new Exception("Nota fiscal não localizada com o idPacto " + idPacto);
        }
        return codNotaFiscal;
    }

    public NotaEnotasTO consultarNotaEnotas(NotaFiscalVO obj) throws Exception {
        EnotasService enotasService = new EnotasService(con);
        NotaEnotasTO notaEnotasTO = enotasService.consultarNota(obj);
        enotasService = null;
        return notaEnotasTO;
    }

    public InutilizacaoNotaFiscalTO consultarInutilizacaoNotaEnotas(NotaFiscalVO notaFiscalVO) throws Exception {
        EnotasService enotasService = new EnotasService(con);
        NotaFiscalOperacao notaFiscalOperacaoService = new NotaFiscalOperacao(con);
        NotaFiscalOperacaoVO notaFiscalOperacaoVO = notaFiscalOperacaoService.consultarUltimaSoliciacaoInutilizacao(notaFiscalVO);
        InutilizacaoNotaFiscalTO inutilizacaoNotaFiscalTO = enotasService.consultarInutilizacao(notaFiscalOperacaoVO);
        notaFiscalOperacaoService = null;
        enotasService = null;
        return inutilizacaoNotaFiscalTO;
    }

    public static void main(String args[]) throws Exception {
        String chave = "teste";
        if (args.length > 0) {
            chave = args[0];
            Uteis.logar(null, "Obter conexão para chave: " + chave);
        }

//        Connection con = DriverManager.getConnection("**************************************", "zillyonweb", "pactodb");

        Uteis.debug = true;
        Connection con = new DAO().obterConexaoEspecifica(chave);
        Conexao.guardarConexaoForJ2SE(chave, con);
        NotaFiscal notaFiscal = new NotaFiscal(con);
        notaFiscal.criarEstruturaEnotas();
    }

    public String enviarEmailNotaFiscal(String emailEnviar, NotaFiscalVO obj, UsuarioVO usuarioVO, String observacaoHistorico) throws Exception {
        try {

            if (UteisValidacao.emptyString(emailEnviar)) {
                throw new Exception("Informe um e-mail.");
            }
            if (!UteisEmail.getValidEmail(emailEnviar)) {
                throw new Exception("O email " + emailEnviar.toUpperCase() + " é inválido.");
            }

            ConfiguracaoSistemaCRMVO configuracaoSistemaCRMVO = SuperControle.getConfiguracaoSMTPNoReply();
            UteisEmail uteis = new UteisEmail();
            String assunto = "Emissão de " + obj.getTipo().getDescricao();
            if (obj.getStatusNotaEnum().equals(StatusEnotasEnum.CANCELADA)) {
                assunto = "Cancelamento de " + obj.getTipo().getDescricao();
            }
            uteis.novo(assunto, configuracaoSistemaCRMVO);
            uteis.enviarEmail(emailEnviar, "", gerarCorpoEmailNotaFiscal(obj).toString(), "", configuracaoSistemaCRMVO.getIntegracaoPacto(), configuracaoSistemaCRMVO.preparaEnvioSendy());

            NotaFiscalHistorico historicoDAO = new NotaFiscalHistorico(con);
            historicoDAO.gerarHistoricoSemCommit("E-MAIL" + observacaoHistorico, emailEnviar, "", obj.getCodigo(), usuarioVO);
            historicoDAO = null;

            return "E-mail enviado com sucesso para " + emailEnviar.toUpperCase();
        } catch (Exception ex) {
            if (ex.getMessage().toLowerCase().startsWith("informe um") ||
                    ex.getMessage().toLowerCase().startsWith("o email")) {
                throw ex;
            } else if (ex.getMessage().toLowerCase().contains("daily message limit")) {
                throw new Exception("Infelizmente seu limite de mensagens diário foi atingido, por favor troque de conta ou espere até amanhã.");
            } else {
                throw new Exception("Erro no Envio de Email. Verifique as Configurações e Tente Novamente: " + ex.getMessage());
            }
        }
    }

    private StringBuilder gerarCorpoEmailNotaFiscal(NotaFiscalVO obj) throws Exception {
        File arq = new File(getClass().getResource("/br/com/pactosolucoes/comuns/util/resources/emailPactoNotaFiscal.txt").toURI());
        StringBuilder texto = FileUtilities.readContentFile(arq.getAbsolutePath());
        String aux = texto.toString()
                .replaceAll("#EMPRESA_RAZAO_SOCIAL", obj.getEmpresaRazaoSocial().toUpperCase())
                .replaceAll("#EMPRESA_NOME_FANTASIA", obj.getEmpresaNomeFantasia().toUpperCase())
                .replaceAll("#EMPRESA_CNPJ", obj.getEmpresaCNPJ())
                .replaceAll("#EMPRESA_TELEFONE", obj.getEmpresaTelefone())
                .replaceAll("#RAZAO_SOCIAL", obj.getRazaoSocial().toUpperCase())
                .replaceAll("#TIPO_NOTA_EMAIL", obj.getTipo().getDescricaoEmail() + " (" + obj.getTipo().getDescricao() + ")")
                .replaceAll("#URL_PDF", obj.getLinkPDF())
                .replaceAll("#URL_XML", obj.getLinkXML());
        return new StringBuilder(aux);
    }

    private JSONObject gerarJSONNotaFamilia(NotaEmitirTO notaEmitirTO) throws Exception {

        NotaTO notaTO = notaEmitirTO.getNotaTO();
        JSONObject jsonEnvio = null;

        if (notaTO.getConfiguracaoNotaFiscalVO().isEnotas()) { //GERAÇÃO JSON PARA O notaFiscal
            //INCLUIR REGISTRO PARA ENVIO PARA O ENOTAS

            jsonEnvio = gerarJSONEnvioEnotas(notaTO, new NotaProcessarTO(""));


        } else { //GERAÇÃO JSON PARA O DELPHI - NFS-E

            jsonEnvio = gerarJSONEnvioNFSe(notaTO);

        }

        if (jsonEnvio == null) {
            throw new Exception("Erro ao gerar nota fiscal - Json!");
        }

        return jsonEnvio;
    }

    public class RetornoNFSe {
        private Double valor = 0.0;
        private StringBuilder descricao = new StringBuilder();
        private boolean algumServico = false;
        private List<NotaEmitirFormaPagamentoTO> formasPagamento;

        public Double getValor() {
            return valor;
        }

        public void setValor(Double valor) {
            this.valor = valor;
        }

        public StringBuilder getDescricao() {
            return descricao;
        }

        public void setDescricao(StringBuilder descricao) {
            this.descricao = descricao;
        }

        public boolean isAlgumServico() {
            return algumServico;
        }

        public void setAlgumServico(boolean algumServico) {
            this.algumServico = algumServico;
        }

        public List<NotaEmitirFormaPagamentoTO> getFormasPagamento() {
            if (formasPagamento == null) {
                formasPagamento = new ArrayList<NotaEmitirFormaPagamentoTO>();
            }
            return formasPagamento;
        }

        public void setFormasPagamento(List<NotaEmitirFormaPagamentoTO> formasPagamento) {
            this.formasPagamento = formasPagamento;
        }
    }


    ///INICIA NOTA FAMILIA
    ///INICIA NOTA FAMILIA
    ///INICIA NOTA FAMILIA
    ///INICIA NOTA FAMILIA
    ///INICIA NOTA FAMILIA
    ///INICIA NOTA FAMILIA
    ///INICIA NOTA FAMILIA
    ///INICIA NOTA FAMILIA
    ///INICIA NOTA FAMILIA
    ///INICIA NOTA FAMILIA
    ///INICIA NOTA FAMILIA
    ///INICIA NOTA FAMILIA


    public void processarItensFamilia(ConfiguracaoNotaFiscalVO configNotaFiscalVO,
                                      List<ItemGestaoNotaFamiliaTO> notasEmitir, TipoRelatorioDF tipoRelatorioDF,
                                      EmpresaVO empresaVO, UsuarioVO usuarioVO, Date dataEmissaoGestaoNotas,
                                      String chave) throws Exception {
        try {

            if (UteisValidacao.emptyString(chave)) {
                throw new Exception("Chave não informada.");
            }

            if (UteisValidacao.emptyNumber(configNotaFiscalVO.getCodigo())) {
                throw new Exception("Configuração de Emissão não informada.");
            }

            configNotaFiscalVO = obterConfiguracaoNotaFiscalVO(configNotaFiscalVO.getCodigo());

            for (ItemGestaoNotaFamiliaTO itemFamiliaTO : notasEmitir) {
                try {
                    con.setAutoCommit(false);

                    itemFamiliaTO.setJsonNotaEnviar(null);

                    for (ItemGestaoNotasTO itemNota : itemFamiliaTO.getReciboPagamentoVOs()) {
                        if (itemNota.isSelecionado()) {
                            if (tipoRelatorioDF.equals(TipoRelatorioDF.FATURAMENTO_DE_CAIXA)) {
                                ReciboPagamento reciboPagamentoDAO = new ReciboPagamento(con);
                                itemNota.setReciboPagamentoVO(reciboPagamentoDAO.consultarPorChavePrimaria(itemNota.getReciboPagamentoVO().getCodigo(), Uteis.NIVELMONTARDADOS_TELACONSULTA));
                                reciboPagamentoDAO = null;

                                MovPagamento movPagamentoDAO = new MovPagamento(con);
                                itemNota.getReciboPagamentoVO().setPagamentosDesteRecibo(movPagamentoDAO.consultarPorCodigoRecibo(itemNota.getReciboPagamentoVO().getCodigo(), false, Uteis.NIVELMONTARDADOS_TODOS));
                                movPagamentoDAO = null;
                            }
                        }
                    }

                    processarItemFamilia(configNotaFiscalVO, itemFamiliaTO, tipoRelatorioDF, dataEmissaoGestaoNotas, usuarioVO, chave);

                    if (itemFamiliaTO.getJsonNotaEnviar() != null) {

                        itemFamiliaTO.setNfseemitida(true);
                        itemFamiliaTO.setSelecionado(false);
                        itemFamiliaTO.setSituacaoNotaFiscal(SituacaoNotaFiscalEnum.GERADA);
                        itemFamiliaTO.setRetorno("Dados enviados com sucesso.");

                    } else {

                        itemFamiliaTO.setNfseemitida(false);
                        itemFamiliaTO.setSelecionado(false);
                        itemFamiliaTO.setSituacaoNotaFiscal(SituacaoNotaFiscalEnum.ERRO_GERACAO);
                    }


                    con.commit();
                } catch (Exception e) {
                    itemFamiliaTO.setJsonNotaEnviar(null);
                    itemFamiliaTO.setRetorno(e.getMessage());
                    con.rollback();
                    con.setAutoCommit(true);
                } finally {
                    con.setAutoCommit(true);
                }
            }

            enviarNotasAguardando();
        } catch (Exception e) {
            Uteis.logar(null, "ERRO! processarItensGrupo ERRO: " + e.getMessage());
            throw e;
        }
    }

    private void processarItemFamilia(ConfiguracaoNotaFiscalVO configNotaFiscalVO, ItemGestaoNotaFamiliaTO itemFamiliaTO,
                                      TipoRelatorioDF tipoRelatorioDF, Date dtEmissaoGestaoNotas, UsuarioVO usuarioVO, String chave) throws Exception {

        EmpresaVO empresaVO = obterEmpresaVO(itemFamiliaTO.getClientePagador().getEmpresa().getCodigo());

        validarInformacoesEnvioDeNota(configNotaFiscalVO.getTipoNotaFiscal(), empresaVO);

        NotaEmitirTO notaEmitirTO = new NotaEmitirTO();
        notaEmitirTO.setChave(chave);
        notaEmitirTO.setConfiguracaoNotaFiscalVO(configNotaFiscalVO);
        notaEmitirTO.setEmpresaVO(empresaVO);
        notaEmitirTO.setUsuarioVO(usuarioVO);

        PessoaVO pessoa = itemFamiliaTO.getClientePagador().getPessoa();
        StringBuilder descricao = new StringBuilder();
        boolean algumServico = false;
        String observacao = configNotaFiscalVO.getObservacao();

        for (ItemGestaoNotasTO itemNota : itemFamiliaTO.getReciboPagamentoVOs()) {
            if (itemNota.isSelecionado() && !itemNota.getNfseemitida()) {
                RetornoNFSe retornoNFSe = montarNFSeFamilia(notaEmitirTO, itemNota, empresaVO, tipoRelatorioDF);
                notaEmitirTO.setValorNota(notaEmitirTO.getValorNota() + retornoNFSe.getValor());
                algumServico = retornoNFSe.isAlgumServico();
                descricao.append(retornoNFSe.getDescricao());
                itemNota.setNfseemitida(true);
                notaEmitirTO.getFormasPagamento().addAll(retornoNFSe.getFormasPagamento());
            }
        }

        if (configNotaFiscalVO.isEnviarObservacaoNaDescricao()) {
            descricao.append(configNotaFiscalVO.getObservacao());
            observacao = null;
        }

        if (!algumServico) {
            throw new Exception("Nenhum serviço vendido neste recibo.");
        }

        Date dataEmissao = obterDataEmissao(empresaVO, null, null, dtEmissaoGestaoNotas, configNotaFiscalVO.getTipoNotaFiscal(), null);
        Date dataCompetencia = obterDataCompetencia(empresaVO, null, null, dtEmissaoGestaoNotas, configNotaFiscalVO.getTipoNotaFiscal());

        ConfiguracaoSistema configSistemaDAO = new ConfiguracaoSistema(con);
        Integer sequencialNotaFamilia = configSistemaDAO.obterSequencialNotaFamilia();

        notaEmitirTO.setSequencialFamilia(sequencialNotaFamilia);
        notaEmitirTO.setDataEmissao(dataEmissao);
        notaEmitirTO.setDataCompetencia(dataCompetencia);
        notaEmitirTO.setDescricao(descricao.toString());
        notaEmitirTO.setObservacao(observacao);
        notaEmitirTO.setFornecedorVO(obterFornecedor(pessoa));
        notaEmitirTO.setPessoaVO(pessoa);
        notaEmitirTO.setIdReferencia(IDENTIFICADOR_FAMILIA + sequencialNotaFamilia); //IDENTIFICADOR PARA EVITAR ENVIO DUPLICADO.
        notaEmitirTO.setDataEmissao(dataEmissao);
        notaEmitirTO.setDataCompetencia(dataCompetencia);

        montarNotaTO(notaEmitirTO);

        JSONObject jsonObject = gerarJSONNotaFamilia(notaEmitirTO);
        itemFamiliaTO.setJsonNotaEnviar(jsonObject);

        gravarNFSeEmitidaFamilia(notaEmitirTO, itemFamiliaTO, empresaVO, tipoRelatorioDF, notaEmitirTO.getValorNota());

        configSistemaDAO.incrementarSequencialNotaFamilia();

        if (!UteisValidacao.emptyNumber(notaEmitirTO.getNotaTO().getNotaFiscalVO().getCodigo())) {
            NotaFiscalFamilia familiaDAO = new NotaFiscalFamilia(con);
            familiaDAO.incluirLista(sequencialNotaFamilia, notaEmitirTO.getNotaTO().getNotaFiscalVO(), itemFamiliaTO.getNfSeEmitidaLista());
            familiaDAO = null;
        }

        if (!configNotaFiscalVO.isEnotas()) {
            Integer nfseEmitida = itemFamiliaTO.getNfSeEmitidaVO().getCodigo();

            String urlConfirmacao = Uteis.getUrlAplicacao() + "/retorno/nota?key=" + chave + "&nfseEmitida=" + nfseEmitida + "&idLote=";
            itemFamiliaTO.getJsonNotaEnviar().put("urlConfirmacao", urlConfirmacao);

            NFSeEmitida nfse = new NFSeEmitida(con);
            nfse.atualizarDataReferenciaJsonEnviar(itemFamiliaTO.getDataReferenciaItem(), itemFamiliaTO.getJsonNotaEnviar().toString(), nfseEmitida);
            nfse = null;
        }
        configSistemaDAO = null;
    }

    private RetornoNFSe montarNFSeFamilia(NotaEmitirTO notaEmitirTO, ItemGestaoNotasTO itemNota, EmpresaVO empresaVO, TipoRelatorioDF tipoRelatorioDF) throws Exception {

        String tipoProdutoEmissao = obterTipoProdutoEmissao(empresaVO.getCodigo(), notaEmitirTO.getConfiguracaoNotaFiscalVO().getTipoNotaFiscal());

        RetornoNFSe retornoNFSe = null;
        switch (tipoRelatorioDF) {
            case FATURAMENTO_DE_CAIXA: {
                retornoNFSe = montarNFSeFamiliaFaturamentoCaixa(notaEmitirTO, itemNota, empresaVO);
                break;
            }
            case FATURAMENTO:
            case COMPETENCIA: {
                MovProduto movProdutoDAO = new MovProduto(con);
                itemNota.setMovProdutoVO(movProdutoDAO.consultarPorChavePrimaria(itemNota.getMovProdutoVO().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
                movProdutoDAO = null;
                if (tipoProdutoEmissao.contains(itemNota.getMovProdutoVO().getProduto().getTipoProduto())) {
                    retornoNFSe = new RetornoNFSe();
                    retornoNFSe.setAlgumServico(true);
                    retornoNFSe.setValor(retornoNFSe.getValor() + itemNota.getMovProdutoVO().getTotalFinal());
                    retornoNFSe.getDescricao().append(adicionarItemNotaFamilia(notaEmitirTO, itemNota.getMovProdutoVO(), itemNota.getMovProdutoVO().getTotalFinal(), empresaVO, itemNota.getMovProdutoVO().getPessoa(), true));
                    retornoNFSe.getFormasPagamento().addAll(obterPagamentoMovProduto(itemNota.getMovProdutoVO().getCodigo()));
                }
                break;
            }
            case RECEITA: {
                retornoNFSe = montarNFSeFamiliaReceita(notaEmitirTO, itemNota, empresaVO);
                break;
            }
        }

        return retornoNFSe;
    }

    private RetornoNFSe montarNFSeFamiliaReceita(NotaEmitirTO notaEmitirTO, ItemGestaoNotasTO itemNota, EmpresaVO empresaVO) throws Exception {

        String tipoProdutoEmissao = obterTipoProdutoEmissao(empresaVO.getCodigo(), notaEmitirTO.getConfiguracaoNotaFiscalVO().getTipoNotaFiscal());

        Integer codMovPagamento = 0;

        RetornoNFSe retornoNFSe = new RetornoNFSe();
        String produtosPagos;
        if (itemNota.getChequeVO() != null) {
            produtosPagos = itemNota.getChequeVO().getProdutosPagos();
            codMovPagamento = itemNota.getChequeVO().getMovPagamento();
        } else if (itemNota.getCartaoCreditoVO() != null) {
            produtosPagos = itemNota.getCartaoCreditoVO().getProdutosPagos();
            codMovPagamento = itemNota.getCartaoCreditoVO().getMovpagamento().getCodigo();
        } else if (itemNota.getMovPagamentoVO() != null) {
            produtosPagos = itemNota.getMovPagamentoVO().getProdutosPagos();
            FormaPagamento formaPagamentoDAO = new FormaPagamento(con);
            itemNota.getMovPagamentoVO().setFormaPagamento(formaPagamentoDAO.consultarPorChavePrimaria(itemNota.getMovPagamentoVO().getFormaPagamento().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
            formaPagamentoDAO = null;
            codMovPagamento = itemNota.getMovPagamentoVO().getCodigo();
        } else {
            throw new ConsistirException("O item da nota não está associado a nenhum pagamento.");
        }

        MovPagamento movPagamentoDAO = new MovPagamento(con);
        MovPagamentoVO movPagamentoVO = movPagamentoDAO.consultarPorChavePrimaria(codMovPagamento, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        movPagamentoDAO = null;

        if (UteisValidacao.emptyString(produtosPagos)) {
            throw new ConsistirException("Este pagamento não está associado a produtos.");
        }
        String[] produtos = produtosPagos.split("\\|");
        for (String prod : produtos) {
            if (UteisValidacao.emptyString(prod) || prod.equals("null")) {
                continue;
            }
            String[] split = prod.split(",");
            Integer codigoMovProduto = Integer.valueOf(split[0]);
            MovProduto movProdutoDAO = new MovProduto(con);
            itemNota.setMovProdutoVO(movProdutoDAO.consultarPorChavePrimaria(codigoMovProduto, Uteis.NIVELMONTARDADOS_DADOSBASICOS));
            movProdutoDAO = null;
            if (tipoProdutoEmissao.contains(itemNota.getMovProdutoVO().getProduto().getTipoProduto())) {

                Double valorPago = Double.valueOf(split[3]);

                retornoNFSe.setAlgumServico(true);
                retornoNFSe.setValor(retornoNFSe.getValor() + valorPago);
                retornoNFSe.getDescricao().append(adicionarItemNotaFamilia(notaEmitirTO, itemNota.getMovProdutoVO(), valorPago, empresaVO, itemNota.getMovProdutoVO().getPessoa(), false));
            }
        }

        NotaEmitirFormaPagamentoTO novo = new NotaEmitirFormaPagamentoTO();
        novo.setFormaPagamento(movPagamentoVO.getFormaPagamento());
        novo.setSiglaTipoFormaPagamento(movPagamentoVO.getFormaPagamento().getTipoFormaPagamento());
        novo.setValor(retornoNFSe.getValor());
        retornoNFSe.getFormasPagamento().add(novo);
        return retornoNFSe;
    }

    private RetornoNFSe montarNFSeFamiliaFaturamentoCaixa(NotaEmitirTO notaEmitirTO, ItemGestaoNotasTO itemNota, EmpresaVO empresaVO) throws Exception {
        RetornoNFSe retornoNFSe = new RetornoNFSe();
        String tipoProdutoEmissao = obterTipoProdutoEmissao(empresaVO.getCodigo(), notaEmitirTO.getConfiguracaoNotaFiscalVO().getTipoNotaFiscal());
        for (MovPagamentoVO pagamento : itemNota.getReciboPagamentoVO().getPagamentosDesteRecibo()) {
            if (!pagamento.getCredito()) {
                String produtosPagos = pagamento.getProdutosPagos();
                if (UteisValidacao.emptyString(produtosPagos)) {
                    throw new ConsistirException("Este pagamento não está associado a produtos.");
                }
                String[] produtos = produtosPagos.split("\\|");
                for (String prod : produtos) {
                    if (UteisValidacao.emptyString(prod) || prod.equals("null")) {
                        continue;
                    }
                    String[] split = prod.split(",");
                    Integer codigoMovProduto = Integer.valueOf(split[0]);
                    MovProduto movProdutoDAO = new MovProduto(con);
                    MovProdutoVO movProdutoVO = movProdutoDAO.consultarPorChavePrimaria(codigoMovProduto, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                    movProdutoDAO = null;
                    if (tipoProdutoEmissao.contains(movProdutoVO.getProduto().getTipoProduto())) {
                        Double valorPago = Double.valueOf(split[3]);
                        retornoNFSe.setAlgumServico(true);
                        retornoNFSe.setValor(retornoNFSe.getValor() + valorPago);
                        retornoNFSe.getDescricao().append(adicionarItemNotaFamilia(notaEmitirTO, movProdutoVO, valorPago, empresaVO, movProdutoVO.getPessoa(), false));
                        retornoNFSe.getFormasPagamento().addAll(obterPagamentoMovProduto(movProdutoVO.getCodigo()));
                    }
                }
            }
        }
        return retornoNFSe;
    }

    private String adicionarItemNotaFamilia(NotaEmitirTO notaEmitirTO, MovProdutoVO movProduto, Double valorUnitario,
                                            EmpresaVO empresa, PessoaVO pessoa, boolean itemPorCliente) {
        String identificador = null;
        String descricao = "";
        if (itemPorCliente) {
            identificador = movProduto.getCodigo().toString() + (pessoa != null ? pessoa.getCodigo().toString() : "");
        } else {
            identificador = movProduto.getProduto().getCodigo().toString() + (pessoa != null ? pessoa.getCodigo().toString() : "");
        }

        NotaEmitirProdutoTO item = notaEmitirTO.getProdutos().get(identificador);

        if (item == null) {
            item = new NotaEmitirProdutoTO();

            item.setValorUnitario(valorUnitario);
            item.setQuantidade(1);
            item.setDescricao("");
            item.setMovProdutoVO(movProduto);

            String descricaoMovProduto = (movProduto.getProduto().getTipoProduto().equals("PM") ? movProduto.getDescricao().substring(0, (movProduto.getDescricao().indexOf("- " + movProduto.getMesReferencia()) - 1)) : movProduto.getDescricao());
            String meses = "";
            if (movProduto.getContrato_Apresentar() > 0) {
                meses = (movProduto.getContrato().getContratoDuracao().getNumeroMeses() == 1) ?
                        movProduto.getContrato().getContratoDuracao().getNumeroMeses() + " MÊS" :
                        movProduto.getContrato().getContratoDuracao().getNumeroMeses() + " MESES";
            }
            if (movProduto.getProduto().getDescricao().equals(descricaoMovProduto)) {
                if (empresa.getTipoGestaoNFSe() == TipoRelatorioDF.COMPETENCIA.getCodigo()) {
                    descricao = movProduto.getProduto().getDescricao() + " " + meses + " Compet. " + movProduto.getMesReferencia();
                } else {
                    descricao = movProduto.getProduto().getDescricao() + " " + meses;
                }
            } else {
                if (empresa.getTipoGestaoNFSe() == TipoRelatorioDF.COMPETENCIA.getCodigo()) {
                    descricao = movProduto.getProduto().getDescricao() + " " + descricaoMovProduto + " " + meses + " Compet. " + movProduto.getMesReferencia();
                } else {
                    descricao = movProduto.getProduto().getDescricao() + " " + descricaoMovProduto + " " + meses;
                }
            }
            if (pessoa != null) {
                if (empresa.isEmitirNomeAlunoNotaFamilia()) {
                    item.setDescricao(descricao + " - " + pessoa.getNomeAbreviado());
                } else {
                    item.setDescricao(descricao);
                }
            } else {
                item.setDescricao(descricao);
            }

            notaEmitirTO.getProdutos().put(identificador, item);
        } else {
            item.setValorUnitario(item.getValorUnitario() + valorUnitario);
        }
        return descricao;
    }

    private void gravarNFSeEmitidaFamilia(NotaEmitirTO notaEmitirTO, ItemGestaoNotaFamiliaTO itemFamiliaTO,
                                          EmpresaVO empresa, TipoRelatorioDF tipoRelatorioDF, Double valor) throws Exception {

        NFSeEmitida nfseDao = new NFSeEmitida(con);
        ReciboPagamento reciboPagamentoDao = new ReciboPagamento(con);
        NFSeEmitidaVO nfSeEmitidaVO = null;
        MovPagamento movPagamentoDao = new MovPagamento(con);

        String tipoProdutoEmissao = obterTipoProdutoEmissao(empresa.getCodigo(), notaEmitirTO.getConfiguracaoNotaFiscalVO().getTipoNotaFiscal());

        for (ItemGestaoNotasTO itemNota : itemFamiliaTO.getReciboPagamentoVOs()) {
            if (itemNota.isSelecionado()) {
                nfSeEmitidaVO = null;
                if ((tipoRelatorioDF == TipoRelatorioDF.COMPETENCIA) || (tipoRelatorioDF == TipoRelatorioDF.FATURAMENTO)) {
                    if (tipoProdutoEmissao.contains(itemNota.getMovProdutoVO().getProduto().getTipoProduto())) {
                        nfSeEmitidaVO = nfseDao.consultaPorProduto(itemNota.getMovProdutoVO().getCodigo());
                    } else {
                        continue;
                    }
                } else if (tipoRelatorioDF == TipoRelatorioDF.FATURAMENTO_DE_CAIXA) {
                    reciboPagamentoDao.gravarNFSEEmitida(itemNota.getReciboPagamentoVO().getCodigo());
                    itemNota.getReciboPagamentoVO().setNfseEmitida(true);
                    nfSeEmitidaVO = nfseDao.consultarPorRecibo(itemNota.getReciboPagamentoVO().getCodigo());
                } else if (tipoRelatorioDF == TipoRelatorioDF.RECEITA) {
                    if (itemNota.getChequeVO() != null) {
                        nfSeEmitidaVO = nfseDao.consultaPorCheque(itemNota.getChequeVO().getObterTodosChequesComposicao());
                    } else if (itemNota.getCartaoCreditoVO() != null) {
                        nfSeEmitidaVO = nfseDao.consultaPorCartao(itemNota.getCartaoCreditoVO().getObterTodosCartoesComposicao());
                    } else if (itemNota.getMovPagamentoVO() != null) {
                        nfSeEmitidaVO = nfseDao.consultaPorPagamento(itemNota.getMovPagamentoVO().getCodigo());
                    }
                }

                if (nfSeEmitidaVO == null) {
                    nfSeEmitidaVO = new NFSeEmitidaVO();
                    if ((tipoRelatorioDF == TipoRelatorioDF.COMPETENCIA) || (tipoRelatorioDF == TipoRelatorioDF.FATURAMENTO)) {
                        nfSeEmitidaVO.setMovProdutoVO(itemNota.getMovProdutoVO());
                        nfSeEmitidaVO.setContrato(itemNota.getMovProdutoVO().getContrato());
                    } else if (tipoRelatorioDF == TipoRelatorioDF.FATURAMENTO_DE_CAIXA) {
                        nfSeEmitidaVO.setRecibo(itemNota.getReciboPagamentoVO());
                        nfSeEmitidaVO.setContrato(itemNota.getReciboPagamentoVO().getContrato());
                    } else if (tipoRelatorioDF == TipoRelatorioDF.RECEITA) {
                        if (itemNota.getChequeVO() != null) {
                            MovPagamentoVO movPagamentoVO = movPagamentoDao.consultarPorChavePrimaria(itemNota.getChequeVO().getMovPagamento(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                            nfSeEmitidaVO.setCheque(itemNota.getChequeVO());
                            nfSeEmitidaVO.setContrato(movPagamentoVO.getReciboPagamento().getContrato());
                        } else if (itemNota.getCartaoCreditoVO() != null) {
                            nfSeEmitidaVO.setCartaoCredito(itemNota.getCartaoCreditoVO());
                            nfSeEmitidaVO.setContrato(itemNota.getCartaoCreditoVO().getMovpagamento().getReciboPagamento().getContrato());
                        } else if (itemNota.getMovPagamentoVO() != null) {
                            nfSeEmitidaVO.setMovPagamento(itemNota.getMovPagamentoVO());
                            if (!UtilReflection.objetoMaiorQueZero(itemNota, "getMovPagamentoVO().getReciboPagamento().getContrato().getCodigo()")) {
                                ReciboPagamento reciboPagamentoDAO = new ReciboPagamento(con);
                                itemNota.getMovPagamentoVO().setReciboPagamento(reciboPagamentoDAO.consultarPorCodigoMovPagamento(itemNota.getMovPagamentoVO().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
                                reciboPagamentoDAO = null;
                            }
                            nfSeEmitidaVO.setContrato(itemNota.getMovPagamentoVO().getReciboPagamento().getContrato());
                        }
                    }

                    NotaTO notaTO = notaEmitirTO.getNotaTO();

                    nfSeEmitidaVO.setIdRps(null);
                    nfSeEmitidaVO.setValor(valor);
                    nfSeEmitidaVO.setSituacaoNotaFiscal(SituacaoNotaFiscalEnum.GERADA);
                    nfSeEmitidaVO.setIdReferencia(notaTO.getNotaIDReferencia());
                    nfSeEmitidaVO.setDataRegistro(Calendario.hoje());
                    nfSeEmitidaVO.setDataEmissao(notaTO.getNotaDtEmissao());
                    nfSeEmitidaVO.setPessoa(notaEmitirTO.getPessoaVO().getCodigo());
                    nfSeEmitidaVO.setEmpresa(empresa.getCodigo());
                    nfSeEmitidaVO.setNotaFamilia(true);
                    nfSeEmitidaVO.setEnotas(notaTO.getConfiguracaoNotaFiscalVO().isEnotas());
                    nfSeEmitidaVO.setConfiguracaoNotaFiscalVO(notaEmitirTO.getConfiguracaoNotaFiscalVO());
                    nfSeEmitidaVO.setDataEnvio(null);
                    nfSeEmitidaVO.setSequencialFamilia(notaTO.getSequencialfamilia());
                    nfseDao.incluir(nfSeEmitidaVO);

                }

                itemFamiliaTO.getNfSeEmitidaLista().add(nfSeEmitidaVO);
                itemFamiliaTO.setNfSeEmitidaVO(nfSeEmitidaVO);

            }
        }
        nfseDao = null;
        reciboPagamentoDao = null;
        movPagamentoDao = null;
    }

    ///FIM NOTA FAMILIA
    ///FIM NOTA FAMILIA
    ///FIM NOTA FAMILIA
    ///FIM NOTA FAMILIA
    ///FIM NOTA FAMILIA
    ///FIM NOTA FAMILIA
    ///FIM NOTA FAMILIA
    ///FIM NOTA FAMILIA
    ///FIM NOTA FAMILIA
    ///FIM NOTA FAMILIA
    ///FIM NOTA FAMILIA
    ///FIM NOTA FAMILIA

    public void reenviarNotaNFSe(NotaFiscalVO notaFiscalReenviar, String chave,
                                 Date dtEmissao, UsuarioVO usuarioVO) throws Exception {

        try {
            con.setAutoCommit(false);

            if (UteisValidacao.emptyString(chave)) {
                throw new Exception("Chave não informada.");
            }
            if (Calendario.maior(dtEmissao, Calendario.hoje())) {
                throw new ConsistirException("A DATA DE EMISSÃO não pode superior a data atual!");
            }

            notaFiscalReenviar = consultarPorChavePrimaria(notaFiscalReenviar.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);

            NotaProcessarTO notaProcessarTO = null;
            if (!UteisValidacao.emptyNumber(notaFiscalReenviar.getNfSeEmitidaVO().getCodigo())) {
                //NFSE

                NFSeEmitida nfSeEmitidaDAO = new NFSeEmitida(con);
                NFSeEmitidaVO nfSeEmitidaVO = nfSeEmitidaDAO.consultarPorChavePrimaria(notaFiscalReenviar.getNfSeEmitidaVO().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSENTIDADESUBORDINADAS);
                nfSeEmitidaDAO = null;
                if (nfSeEmitidaVO == null) {
                    throw new Exception("Por favor, reemita a nota pelo Gestão de Notas.");
                }
                if (nfSeEmitidaVO.isNotaFamilia()) {
                    throw new Exception("Nota em grupo não permite reenvio.");
                }

                //EXCLUR A NOTA! PARA REENVIAR
                NFSeEmitida nfSeEmitida1DAO = new NFSeEmitida(con);
                nfSeEmitida1DAO.excluirComLogEnotasSemCommit(notaFiscalReenviar.getCodigo(), nfSeEmitidaVO, usuarioVO);
                nfSeEmitida1DAO = null;


                Empresa empresaDAO = new Empresa(con);
                EmpresaVO empresaVO = empresaDAO.consultarPorChavePrimaria(notaFiscalReenviar.getEmpresaVO().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                empresaDAO = null;
                TipoRelatorioDF tipoEmissao = empresaVO.getTipoGestaoNFSeEnum();

                notaProcessarTO = new NotaProcessarTO(notaFiscalReenviar.getCodigo(), nfSeEmitidaVO, tipoEmissao, chave);
                processarNotasProcessar(notaFiscalReenviar.getTipo(), notaProcessarTO, usuarioVO, dtEmissao, false);

                if (!notaProcessarTO.isSucesso()) {
                    throw new Exception(notaProcessarTO.getRetorno());
                }


            } else if (!UteisValidacao.emptyNumber(notaFiscalReenviar.getNotaFiscalConsumidorEletronicaVO().getCodigo())) {
                //NFCE

                NotaFiscalConsumidorEletronica notaDAO = new NotaFiscalConsumidorEletronica(con);
                NotaFiscalConsumidorEletronicaVO notaVO = notaDAO.consultarPorChavePrimaria(notaFiscalReenviar.getNotaFiscalConsumidorEletronicaVO().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSENTIDADESUBORDINADAS);
                notaDAO = null;
                if (notaVO == null) {
                    throw new Exception("Por favor, reemita a nota pelo Gestão de Notas.");
                }

                //EXCLUR A NOTA! PARA REENVIAR
                NotaFiscalConsumidorEletronica nota1DAO = new NotaFiscalConsumidorEletronica(con);
                nota1DAO.excluirComLogEnotasSemCommit(notaFiscalReenviar.getCodigo(), notaVO, usuarioVO);
                nota1DAO = null;

                if (!UteisValidacao.emptyNumber(notaVO.getReciboPagamento().getCodigo())) {
                    notaProcessarTO = new NotaProcessarTO(chave);
                    notaProcessarTO.setNotaFiscalAnterior(notaFiscalReenviar.getCodigo());
                    notaProcessarTO.setReciboPagamentoVO(notaVO.getReciboPagamento());
                    processarNotasProcessar(notaFiscalReenviar.getTipo(), notaProcessarTO, usuarioVO, null, false);
                } else {
                    throw new Exception("Por favor, reemita a nota pelo Gestão de NFC-e.");
                }

                if (!notaProcessarTO.isSucesso()) {
                    throw new Exception(notaProcessarTO.getRetorno());
                }

            } else {
                throw new Exception("Por favor, reemita a nota pelo Gestão de Notas.");
            }

            atualizarNotaFiscalNova(notaProcessarTO.getNotaFiscalNova(), notaFiscalReenviar.getCodigo());

            notaFiscalReenviar.setStatusNota(StatusEnotasEnum.REENVIADA.getDescricaoEnotas());
            atualizarStatusNota(StatusEnotasEnum.REENVIADA, notaFiscalReenviar);

            NotaFiscalHistorico histDAO = new NotaFiscalHistorico(con);
            histDAO.gerarHistoricoSemCommit(notaFiscalReenviar.getStatusNota(), "REENVIAR NOTA", notaFiscalReenviar.getIdPacto(), notaFiscalReenviar.getCodigo(), usuarioVO);
            histDAO = null;

            con.commit();
        } catch (Exception ex) {
            con.rollback();
            con.setAutoCommit(true);
            throw ex;
        } finally {
            con.setAutoCommit(true);
        }

        enviarNotasAguardando();
    }

    public void atualizarNotaFiscalNova(Integer notaFiscalNova, Integer codNotaFiscal) throws Exception {
        String sql = "UPDATE notafiscal SET notaFiscalNova = ? WHERE codigo = ?";
        PreparedStatement sqlInserir = con.prepareStatement(sql);
        sqlInserir.setInt(1, notaFiscalNova);
        sqlInserir.setInt(2, codNotaFiscal);
        sqlInserir.execute();
    }

    private StringBuilder obterConsultaSQL(Integer pessoa, boolean count, Integer max, Integer index, String orderBY) {
        StringBuilder sql = new StringBuilder();
        if (count) {
            sql.append("SELECT count(*) \n");
        } else {
            sql.append("SELECT * \n");
        }

        sql.append("FROM notafiscal \n");
        sql.append("WHERE 1 = 1  \n");
        sql.append("AND excluido = false \n");

        if (!UteisValidacao.emptyNumber(pessoa)) {
            sql.append("AND pessoa = ").append(pessoa).append(" \n");
        }

        if (!count) {
            //ORDER BY
            if (UteisValidacao.emptyString(orderBY)) {
                sql.append(" ORDER BY codigo \n");
            } else {
                sql.append(" ORDER BY ").append(orderBY).append(" \n");
            }

            //limit
            if (!UteisValidacao.emptyNumber(max)) {
                sql.append(" LIMIT ").append(max).append(" \n");
            }

            //offset
            if (!UteisValidacao.emptyNumber(index)) {
                sql.append(" OFFSET ").append(index).append(" \n");
            }
        }
        return sql;
    }

    public List<NotaFiscalVO> consultarNotas(Integer pessoa, Integer max, Integer index, String orderBY, int nivelMontarDados) throws Exception {
        StringBuilder sql = obterConsultaSQL(pessoa, false, max, index, orderBY);
        Statement stm = con.createStatement();
        ResultSet rs = stm.executeQuery(sql.toString());
        return montarDadosConsulta(rs, nivelMontarDados, con);
    }

    public Integer consultarNotasTotal(Integer pessoa) throws Exception {
        StringBuilder sql = obterConsultaSQL(pessoa, true, null, null, null);
        return SuperFacadeJDBC.contar(sql.toString(), con);
    }

    public List<NotaFiscalVO> consultarNotasPorCodigos(String valorConsulta, int nivelMontarDados) throws Exception {
        String sqlStr = "SELECT * FROM notafiscal WHERE codigo in (" + valorConsulta + ")";
        Statement stm = con.createStatement();
        ResultSet rs = stm.executeQuery(sqlStr.toString());
        return montarDadosConsulta(rs, nivelMontarDados, con);
    }

    public String emitirNFCeRecibo(Integer recibo, Integer usuario, String key, String origemEmissaoNFCe) throws Exception {
        ReciboPagamento reciboPagamentoDAO;
        NotaFiscalConsumidorEletronica notaFiscalConsumidorEletronicaDAO;
        Empresa empresaDAO;
        Usuario usuarioDAO;
        Colaborador colaboradorDAO;
        try {
            reciboPagamentoDAO = new ReciboPagamento(this.con);
            notaFiscalConsumidorEletronicaDAO = new NotaFiscalConsumidorEletronica(this.con);
            empresaDAO = new Empresa(this.con);
            usuarioDAO = new Usuario(this.con);
            colaboradorDAO = new Colaborador(this.con);

            if (UteisValidacao.emptyNumber(usuario)) {
                throw new Exception("Usuário não informado");
            }
            if (UteisValidacao.emptyString(key)) {
                throw new Exception("Chave não informada");
            }

            ReciboPagamentoVO reciboPagamentoVO = reciboPagamentoDAO.consultarPorCodigo(recibo, Uteis.NIVELMONTARDADOS_TELACONSULTA);
            NotaFiscalConsumidorEletronicaVO notaExiste = notaFiscalConsumidorEletronicaDAO.consultarPorReciboPagamento(reciboPagamentoVO.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            if (!UteisValidacao.emptyNumber(notaExiste.getCodigo())) {
                throw new Exception("Já existe NFC-e emitida para esse recibo.");
            }

            UsuarioVO usuarioVO = usuarioDAO.consultarPorChavePrimaria(usuario, Uteis.NIVELMONTARDADOS_DADOSBASICOS);

            NotaProcessarTO notaProcessarTO = gerarNotaReciboPagamento(TipoNotaFiscalEnum.NFCE, reciboPagamentoVO, usuarioVO, key);
            if (!notaProcessarTO.isSucesso()) {
                throw new Exception(notaProcessarTO.getRetorno());
            }

            try {
                EmpresaVO empresaVO = empresaDAO.consultarPorChavePrimaria(reciboPagamentoVO.getEmpresa().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                LogVO obj = new LogVO();
                obj.setChavePrimaria(reciboPagamentoVO.getCodigo().toString());
                obj.setNomeEntidade("CLIENTE");
                obj.setNomeEntidadeDescricao("Cliente - NFC-E");
                obj.setOperacao("EMISSÃO DE NOTA FISCAL CONSUMIDOR");
                obj.setResponsavelAlteracao(usuarioVO.getNome());
                obj.setUserOAMD(usuarioVO.getUserOamd());
                obj.setNomeCampo("MENSAGEM");
                obj.setValorCampoAlterado("EMISSÃO DE NOTA FISCAL CONSUMIDOR - RECIBO " + reciboPagamentoVO.getCodigo() + " - VALOR " + empresaVO.getMoeda() + " " + reciboPagamentoVO.getValorTotal() + "");
                obj.setDataAlteracao(negocio.comuns.utilitarias.Calendario.hoje());
                if ("COLABORADOR".equals(origemEmissaoNFCe)) {
                    ColaboradorVO colaboradorVO = colaboradorDAO.consultarPorCodigoPessoa(
                            reciboPagamentoVO.getPessoaPagador().getCodigo(),
                            reciboPagamentoVO.getEmpresa().getCodigo(),
                            Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                    obj.setNomeEntidade("COLABORADOR");
                    obj.setNomeEntidadeDescricao("Colaborador - NFC-E");
                    obj.setChavePrimaria(colaboradorVO.getCodigo().toString());
                }
                registrarLogObjetoVO(obj, reciboPagamentoVO.getPessoaPagador().getCodigo(), this.con);
            } catch (Exception e) {
                registrarLogErroObjetoVO("CLIENTE", reciboPagamentoVO.getPessoaPagador().getCodigo(), "ERRO AO REGISTRAR LOG EMITIR NFC-E", usuarioVO.getNome(), usuarioVO.getUserOamd(), this.con);
                e.printStackTrace();
            }

            return notaProcessarTO.getRetorno();
        } finally {
            reciboPagamentoDAO = null;
            notaFiscalConsumidorEletronicaDAO = null;
            empresaDAO = null;
            usuarioDAO = null;
            colaboradorDAO = null;
        }
    }

    public String emitirNFSeRecibo(Integer recibo, Integer usuario, String key) throws Exception {
        ReciboPagamento reciboPagamentoDAO;
        Empresa empresaDAO;
        Usuario usuarioDAO;
        MovPagamento movPagamentoDAO;
        Log logDAO;
        try {
            reciboPagamentoDAO = new ReciboPagamento(this.con);
            empresaDAO = new Empresa(this.con);
            usuarioDAO = new Usuario(this.con);
            movPagamentoDAO = new MovPagamento(this.con);
            logDAO = new Log(this.con);

            if (UteisValidacao.emptyNumber(usuario)) {
                throw new Exception("Usuário não informado");
            }
            if (UteisValidacao.emptyString(key)) {
                throw new Exception("Chave não informada");
            }

            ReciboPagamentoVO reciboPagamentoVO = reciboPagamentoDAO.consultarPorChavePrimaria(recibo, Uteis.NIVELMONTARDADOS_TODOS);
            reciboPagamentoVO.setPagamentosDesteRecibo(movPagamentoDAO.consultarPorCodigoRecibo(reciboPagamentoVO.getCodigo(), false, Uteis.NIVELMONTARDADOS_TODOS));
            UsuarioVO usuarioVO = usuarioDAO.consultarPorChavePrimaria(usuario, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            EmpresaVO empresaVO = empresaDAO.consultarPorChavePrimaria(reciboPagamentoVO.getEmpresa().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);

            NotaProcessarTO notaProcessarTO = gerarNotaReciboPagamento(TipoNotaFiscalEnum.NFSE, reciboPagamentoVO, usuarioVO, key);
            if (notaProcessarTO.isSucesso()) {
                try {
                    LogVO obj = new LogVO();
                    obj.setChavePrimaria(reciboPagamentoVO.getCodigo().toString());
                    obj.setNomeEntidade("CLIENTE");
                    obj.setNomeEntidadeDescricao("Cliente - NFSE");
                    obj.setOperacao("EMISSÃO DE NOTA");
                    obj.setResponsavelAlteracao(usuarioVO.getNome());
                    obj.setUserOAMD(usuarioVO.getUserOamd());
                    obj.setNomeCampo("MENSAGEM");
                    obj.setValorCampoAlterado("EMISSÃO DE NOTA - RECIBO " + reciboPagamentoVO.getCodigo() + " - VALOR " + empresaVO.getMoeda() + " " + reciboPagamentoVO.getValorTotal() + "");
                    obj.setDataAlteracao(Calendario.hoje());
                    registrarLogObjetoVO(obj, reciboPagamentoVO.getPessoaPagador().getCodigo(), this.con);
                } catch (Exception e) {
                    registrarLogErroObjetoVO("CLIENTE", reciboPagamentoVO.getPessoaPagador().getCodigo(), "ERRO AO EMITIR NFSE", usuarioVO.getNome(), usuarioVO.getUserOamd(), this.con);
                    e.printStackTrace();
                }
            } else {
                throw new Exception(notaProcessarTO.getRetorno());
            }
            return notaProcessarTO.getRetorno();
        } finally {
            reciboPagamentoDAO = null;
            empresaDAO = null;
            usuarioDAO = null;
            movPagamentoDAO = null;
        }
    }

    private Boolean emitirConfigNotaProdutoBasePlano(MovProdutoVO movProdutoVO,TipoNotaFiscalEnum tipoNotaFiscalEnum){
        return  !UteisValidacao.emptyObject(movProdutoVO) && (!UteisValidacao.emptyObject(movProdutoVO.getContrato()) && !UteisValidacao.emptyNumber(movProdutoVO.getContrato().getCodigo()))
                && (!UteisValidacao.emptyObject(movProdutoVO.getContrato().getPlano()) && !UteisValidacao.emptyNumber(movProdutoVO.getContrato().getPlano().getCodigo()))
                && (!UteisValidacao.emptyObject(movProdutoVO.getContrato().getPlano().getProdutoPadraoGerarParcelasContrato()) && !UteisValidacao.emptyNumber(movProdutoVO.getContrato().getPlano().getProdutoPadraoGerarParcelasContrato().getCodigo()))
                && tipoNotaFiscalEnum.equals(TipoNotaFiscalEnum.NFSE);
    }

    private Integer validarConfigNotaFiscal(TipoNotaFiscalEnum tipoNotaFiscalEnum, MovProdutoVO movProdutoVO, Integer codConfigEmissao) throws Exception{
        if (!UteisValidacao.emptyNumber(codConfigEmissao)) {
            ConfiguracaoNotaFiscalVO configNotaVO = obterConfiguracaoNotaFiscalVO(codConfigEmissao);
            if (!configNotaVO.getEmpresaVO().getCodigo().equals(movProdutoVO.getEmpresa().getCodigo())) {
                //se a configuração do produto for diferente da empresa pegar a config da empresa..
                //ticket JIRA PAGAMENTOS-866
                //by Luiz Felipe 06/05/2020
                codConfigEmissao = obterConfigEmissaoEmpresa(tipoNotaFiscalEnum, movProdutoVO.getEmpresa().getCodigo());
                configNotaVO = obterConfiguracaoNotaFiscalVO(codConfigEmissao);
            }

            if (!configNotaVO.getEmpresaVO().getCodigo().equals(movProdutoVO.getEmpresa().getCodigo())) {
                throw new ConsistirException("A empresa da configuração de emissão no cadastro do produto não é a mesma do MovProduto que está sendo enviado.");
            }

            if (!configNotaVO.isAtivo()) {
                codConfigEmissao = 0;
            }
        }

        if (UteisValidacao.emptyNumber(codConfigEmissao)) {

            EmpresaVO empresaVO = obterEmpresaVO(movProdutoVO.getEmpresa().getCodigo());

            if (tipoNotaFiscalEnum.equals(TipoNotaFiscalEnum.NFSE) || tipoNotaFiscalEnum.equals(TipoNotaFiscalEnum.NFE)) {
                codConfigEmissao = empresaVO.getConfiguracaoNotaFiscalNFSe().getCodigo();
            } else if (tipoNotaFiscalEnum.equals(TipoNotaFiscalEnum.NFCE)) {
                codConfigEmissao = empresaVO.getConfiguracaoNotaFiscalNFCe().getCodigo();
            }
        }

        return codConfigEmissao;
    }

    public void atualizarStatusNotaManualmenteSemCommit(StatusEnotasEnum statusEnotasEnum, NotaFiscalVO notaFiscalVO,String motivoAlteracaoStatus) throws Exception {
        String sql = "UPDATE notafiscal SET statusNota = ? , alteradostatusmanual = ?  WHERE codigo = ?";
        PreparedStatement sqlAlterar = con.prepareStatement(sql);
        sqlAlterar.setString(1, statusEnotasEnum.getDescricaoEnotas());
        sqlAlterar.setBoolean(2, Boolean.TRUE);
        sqlAlterar.setInt(3, notaFiscalVO.getCodigo());
        sqlAlterar.execute();

        NotaFiscalHistorico histDAO = new NotaFiscalHistorico(con);
        histDAO.gerarHistoricoSemCommit("Alteração Manual - " + statusEnotasEnum.getDescricaoEnotas(), motivoAlteracaoStatus, notaFiscalVO.getIdPacto(), notaFiscalVO.getCodigo(), getUsuarioLogadoControleAcesso());
        histDAO = null;


    }

    public void atualizarStatusNotaManualmente(StatusEnotasEnum statusEnotasEnum, NotaFiscalVO notaFiscalVO,String motivoAlteracaoStatus) throws Exception {
        try {
            con.setAutoCommit(false);
            atualizarStatusNotaManualmenteSemCommit(statusEnotasEnum,notaFiscalVO,motivoAlteracaoStatus);
            con.commit();
        } catch (Exception e) {
            con.rollback();
            con.setAutoCommit(true);
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

}
