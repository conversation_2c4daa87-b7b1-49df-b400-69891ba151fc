package negocio.facade.jdbc.basico;

import br.com.pactosolucoes.comuns.util.Formatador;
import br.com.pactosolucoes.comuns.util.JSFUtilities;
import br.com.pactosolucoes.enumeradores.SituacaoClienteEnum;
import br.com.pactosolucoes.estrutura.paginacao.ConfPaginacao;
import br.com.pactosolucoes.estrutura.paginacao.PreparedStatementPersonalizado;
import negocio.comuns.arquitetura.LogVO;
import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.basico.ColaboradorVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.basico.MovimentoContaCorrenteClienteComposicaoVO;
import negocio.comuns.basico.MovimentoContaCorrenteClienteFiltroVO;
import negocio.comuns.basico.MovimentoContaCorrenteClienteVO;
import negocio.comuns.basico.PessoaVO;
import negocio.comuns.contrato.ContratoOperacaoVO;
import negocio.comuns.contrato.MovProdutoVO;
import negocio.comuns.financeiro.CartaoCreditoVO;
import negocio.comuns.financeiro.ChequeVO;
import negocio.comuns.financeiro.FormaPagamentoVO;
import negocio.comuns.financeiro.ItemVendaAvulsaVO;
import negocio.comuns.financeiro.MovPagamentoVO;
import negocio.comuns.financeiro.MovParcelaVO;
import negocio.comuns.financeiro.MovProdutoParcelaVO;
import negocio.comuns.financeiro.NFSeEmitidaVO;
import negocio.comuns.financeiro.PagamentoMovParcelaVO;
import negocio.comuns.financeiro.ReciboDevolucaoVO;
import negocio.comuns.financeiro.ReciboPagamentoVO;
import negocio.comuns.financeiro.VendaAvulsaVO;
import negocio.comuns.financeiro.enumerador.TipoOperacaoContaCorrenteEnum;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.Ordenacao;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.comuns.utilitarias.UtilReflection;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.facade.jdbc.arquitetura.Usuario;
import negocio.facade.jdbc.arquitetura.ZillyonWebFacade;
import negocio.facade.jdbc.contrato.ContratoOperacao;
import negocio.facade.jdbc.contrato.MovProduto;
import negocio.facade.jdbc.financeiro.*;
import negocio.facade.jdbc.plano.Produto;
import negocio.facade.jdbc.utilitarias.Conexao;
import negocio.interfaces.arquitetura.LogInterfaceFacade;
import negocio.interfaces.basico.MovimentoContaCorrenteClienteInterfaceFacade;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import negocio.comuns.contrato.ContratoVO;
import negocio.comuns.financeiro.EstornoMovProdutoVO;
import org.json.JSONArray;
import org.json.JSONObject;

/**
 * Classe de persistência que encapsula todas as operações de manipulação dos
 * dados da classe
 * <code>MovimentoContaCorrenteClienteVO</code>. Responsável por implementar
 * operações como incluir, alterar, excluir e consultar pertinentes a classe
 * <code>MovimentoContaCorrenteClienteVO</code>. Encapsula toda a interação com
 * o banco de dados.
 *
 * @see MovimentoContaCorrenteClienteVO
 * @see SuperEntidade
 */
public class MovimentoContaCorrenteCliente extends SuperEntidade implements MovimentoContaCorrenteClienteInterfaceFacade {
    
    public MovimentoContaCorrenteCliente() throws Exception {
        super();
        setIdEntidade("MovimentoContaCorrenteCliente");
    }
    
    public MovimentoContaCorrenteCliente(Connection con) throws Exception {
        super(con);
        setIdEntidade("MovimentoContaCorrenteCliente");
    }

    /**
     * Operação responsável por retornar um novo objeto da classe
     * <code>MovimentoContaCorrenteClienteVO</code>.
     */
    public MovimentoContaCorrenteClienteVO novo() throws Exception {
        incluir(getIdEntidade());
        MovimentoContaCorrenteClienteVO obj = new MovimentoContaCorrenteClienteVO();
        return obj;
    }

    /* (non-Javadoc)
     * @see negocio.interfaces.basico.MovimentoContaCorrenteClienteInterfaceFacade#incluir(negocio.comuns.basico.MovimentoContaCorrenteClienteVO)
     */
    public void incluir(MovimentoContaCorrenteClienteVO obj) throws Exception {
        this.incluirSemCommit(obj, false);
    }

    /**
     * Operação responsável por incluir no banco de dados um objeto da classe
     * <code>MovimentoContaCorrenteClienteVO</code>. Primeiramente valida os
     * dados (
     * <code>validarDados</code>) do objeto. Verifica a conexão com o banco de
     * dados e a permissão do usuário para realizar esta operacão na entidade.
     * Isto, através da operação
     * <code>incluir</code> da superclasse.
     *
     * @param obj Objeto da classe <code>MovimentoContaCorrenteClienteVO</code>
     * que será gravado no banco de dados.
     * @exception Exception Caso haja problemas de conexão, restrição de acesso
     * ou validação de dados.
     */
    public void incluirSemCommit(MovimentoContaCorrenteClienteVO obj) throws Exception {
        this.incluirSemCommit(obj, false);
    }

    /**
     * Operação responsável por incluir no banco de dados um objeto da classe
     * <code>MovimentoContaCorrenteClienteVO</code>. Primeiramente valida os
     * dados (
     * <code>validarDados</code>) do objeto. Verifica a conexão com o banco de
     * dados e a permissão do usuário para realizar esta operacão na entidade.
     * Isto, através da operação
     * <code>incluir</code> da superclasse.
     *
     * @param obj Objeto da classe <code>MovimentoContaCorrenteClienteVO</code>
     * que será gravado no banco de dados.
     * @exception Exception Caso haja problemas de conexão, restrição de acesso
     * ou validação de dados.
     */
    public void incluirSemCommit(MovimentoContaCorrenteClienteVO obj, boolean centralEventos) throws Exception {
        try {
            MovimentoContaCorrenteClienteVO.validarDados(obj);
            if (centralEventos) {
//            	incluirObj(getIdEntidade());
            } else {
                incluir(getIdEntidade());
            }
//            MovimentoContaCorrenteClienteVO atual = consultarPorCodigoPessoa(obj.getPessoa().getCodigo(), nivelMontarDados')
            obj.realizarUpperCaseDados();
            String sql = "INSERT INTO MovimentoContaCorrenteCliente( pessoa, saldoAtual, descricao, dataRegistro, responsavelAutorizacao, valor, reciboPagamento, contratoOperacao, tipoMovimentacao) VALUES (  ?, ?, ?, ?, ?, ?, ?, ?, ? )";
            try (PreparedStatement sqlInserir = con.prepareStatement(sql)) {
                if (obj.getPessoa().getCodigo().intValue() != 0) {
                    sqlInserir.setInt(1, obj.getPessoa().getCodigo().intValue());
                } else {
                    sqlInserir.setNull(1, 0);
                }

                sqlInserir.setDouble(2, Uteis.arredondarForcando2CasasDecimaisMantendoSinal(obj.getSaldoAtual()));
                sqlInserir.setString(3, obj.getDescricao());
                sqlInserir.setTimestamp(4, Uteis.getDataJDBCTimestamp(obj.getDataRegistro()));
                if (obj.getResponsavelAutorizacao().getCodigo().intValue() != 0) {
                    sqlInserir.setInt(5, obj.getResponsavelAutorizacao().getCodigo().intValue());
                } else {
                    sqlInserir.setNull(5, 0);
                }

                sqlInserir.setDouble(6, Uteis.arredondarForcando2CasasDecimaisMantendoSinal(obj.getValor()));
                //Alcides: adicionei aqui verificações de valores nulos para o Central de Eventos
                if (obj.getReciboPagamentoVO() != null
                        && obj.getReciboPagamentoVO().getCodigo() != null
                        && obj.getReciboPagamentoVO().getCodigo().intValue() != 0) {
                    sqlInserir.setInt(7, obj.getReciboPagamentoVO().getCodigo().intValue());
                } else {
                    sqlInserir.setNull(7, 0);
                }
                if (obj.getContratoOperacaoVO() != null
                        && obj.getContratoOperacaoVO().getCodigo() != null
                        && obj.getContratoOperacaoVO().getCodigo().intValue() != 0) {
                    sqlInserir.setInt(8, obj.getContratoOperacaoVO().getCodigo().intValue());
                } else {
                    sqlInserir.setNull(8, 0);
                }
                sqlInserir.setString(9, obj.getTipoMovimentacao());
                sqlInserir.execute();
            }
            obj.setCodigo(obterValorChavePrimariaCodigo());
            if (!obj.getMovPagamentosVOs().isEmpty()) {
                MovimentoContaCorrenteClienteComposicao movDAO = new MovimentoContaCorrenteClienteComposicao(con);
                movDAO.adicionarComposicao(obj, false);
                movDAO = null;
            }
            obj.setNovoObj(false);

//			//INICIO - REGISTRANDO LOG DE MODIFICACOES
//			obj.setNovoObj(new Boolean(true));
//			registrarLogObjetoVO(obj, obj.getCodigo().intValue(), "MOVIMENTOCONTACORRENTECLIENTE", obj.getPessoa().getCodigo());
//			obj.setNovoObj(new Boolean(false));
//			//FIM - REGISTRANDO LOG DE MODIFICACOES
        } catch (Exception e) {
            throw e;
        }
        
    }

    /**
     * Operação responsável por alterar no BD os dados de um objeto da classe
     * <code>MovimentoContaCorrenteClienteVO</code>. Sempre utiliza a chave
     * primária da classe como atributo para localização do registro a ser
     * alterado. Primeiramente valida os dados (
     * <code>validarDados</code>) do objeto. Verifica a conexão com o banco de
     * dados e a permissão do usuário para realizar esta operacão na entidade.
     * Isto, através da operação
     * <code>alterar</code> da superclasse.
     *
     * @param obj Objeto da classe <code>MovimentoContaCorrenteClienteVO</code>
     * que será alterada no banco de dados.
     * @exception Exception Caso haja problemas de conexão, restrição de acesso
     * ou validação de dados.
     */
    public void alterar(MovimentoContaCorrenteClienteVO obj, boolean centralEventos) throws Exception {
        try {
            alterarSemCommit(obj, centralEventos);
        } catch (Exception e) {
            throw e;
        }
    }

    /* (non-Javadoc)
     * @see negocio.interfaces.basico.MovimentoContaCorrenteClienteInterfaceFacade#alterar(negocio.comuns.basico.MovimentoContaCorrenteClienteVO)
     */
    public void alterar(MovimentoContaCorrenteClienteVO obj) throws Exception {
        this.alterar(obj, false);
        
    }

    /**
     * Operação responsável por alterar no BD os dados de um objeto da classe
     * <code>MovimentoContaCorrenteClienteVO</code>. Sempre utiliza a chave
     * primária da classe como atributo para localização do registro a ser
     * alterado. Primeiramente valida os dados (
     * <code>validarDados</code>) do objeto. Verifica a conexão com o banco de
     * dados e a permissão do usuário para realizar esta operacão na entidade.
     * Isto, através da operação
     * <code>alterar</code> da superclasse.
     *
     * @param obj Objeto da classe <code>MovimentoContaCorrenteClienteVO</code>
     * que será alterada no banco de dados.
     * @exception Exception Caso haja problemas de conexão, restrição de acesso
     * ou validação de dados.
     */
    public void alterarSemCommit(MovimentoContaCorrenteClienteVO obj, boolean centralEventos) throws Exception {
        try {
            
            MovimentoContaCorrenteClienteVO.validarDados(obj);
            if (centralEventos) {
//            	alterarObj(getIdEntidade());
            } else {
                alterar(getIdEntidade());
            }
            obj.realizarUpperCaseDados();
            String sql = "UPDATE MovimentoContaCorrenteCliente set pessoa=?, saldoAtual=?, descricao=?, dataRegistro=?,  responsavelAutorizacao=?, valor=?, reciboPagamento=?, contratoOperacao=?, tipoMovimentacao=? WHERE ((codigo = ?))";
            try (PreparedStatement sqlAlterar = con.prepareStatement(sql)) {
                if (obj.getPessoa().getCodigo().intValue() != 0) {
                    sqlAlterar.setInt(1, obj.getPessoa().getCodigo().intValue());
                } else {
                    sqlAlterar.setNull(1, 0);
                }

                sqlAlterar.setDouble(2, Uteis.arredondarForcando2CasasDecimaisMantendoSinal(obj.getSaldoAtual()));
                sqlAlterar.setString(3, obj.getDescricao());
                sqlAlterar.setDate(4, Uteis.getDataJDBC(obj.getDataRegistro()));
                if (obj.getResponsavelAutorizacao().getCodigo().intValue() != 0) {
                    sqlAlterar.setInt(5, obj.getResponsavelAutorizacao().getCodigo().intValue());
                } else {
                    sqlAlterar.setNull(5, 0);
                }

                sqlAlterar.setDouble(6, Uteis.arredondarForcando2CasasDecimaisMantendoSinal(obj.getValor()));
                if (obj.getReciboPagamentoVO().getCodigo().intValue() != 0) {
                    sqlAlterar.setInt(7, obj.getReciboPagamentoVO().getCodigo().intValue());
                } else {
                    sqlAlterar.setNull(7, 0);
                }
                if (obj.getContratoOperacaoVO().getCodigo().intValue() != 0) {
                    sqlAlterar.setInt(8, obj.getContratoOperacaoVO().getCodigo().intValue());
                } else {
                    sqlAlterar.setNull(8, 0);
                }
                sqlAlterar.setString(9, obj.getTipoMovimentacao());
                sqlAlterar.setInt(10, obj.getCodigo().intValue());
                sqlAlterar.execute();
            }
        } catch (Exception e) {
            throw e;
        }
    }
    
    public void excluir(MovimentoContaCorrenteClienteVO obj) throws Exception {
        this.excluir(obj, false);
    }

    /**
     * Operação responsável por excluir no BD um objeto da classe
     * <code>MovimentoContaCorrenteClienteVO</code>. Sempre localiza o registro
     * a ser excluído através da chave primária da entidade. Primeiramente
     * verifica a conexão com o banco de dados e a permissão do usuário para
     * realizar esta operacão na entidade. Isto, através da operação
     * <code>excluir</code> da superclasse.
     *
     * @param obj Objeto da classe <code>MovimentoContaCorrenteClienteVO</code>
     * que será removido no banco de dados.
     * @exception Exception Caso haja problemas de conexão ou restrição de
     * acesso.
     */
    public void excluir(MovimentoContaCorrenteClienteVO obj, boolean centralEventos) throws Exception {
        try {
            if (centralEventos) {
//            	excluirObj(getIdEntidade());
            } else {
                excluir(getIdEntidade());
            }
            String sql = "DELETE FROM MovimentoContaCorrenteCliente WHERE ((codigo = ?))";
            try (PreparedStatement sqlExcluir = con.prepareStatement(sql)) {
                sqlExcluir.setInt(1, obj.getCodigo().intValue());
                sqlExcluir.execute();
            }
        } catch (Exception e) {
            throw e;
        }
    }

    /**
     * Responsável por realizar uma consulta de
     * <code>MovimentoContaCorrenteCliente</code> através do valor do atributo
     * <code>situacao</code> da classe
     * <code>Colaborador</code> Faz uso da operação
     * <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o
     * List resultante.
     *
     * @return List Contendo vários objetos da
     * classe <code>MovimentoContaCorrenteClienteVO</code> resultantes da
     * consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de
     * acesso.
     */
    public MovimentoContaCorrenteClienteVO consultarPorCodigoPessoa(Integer valorConsulta, int nivelMontarDados) throws Exception {
        //consultar(getIdEntidade(), true);
        String sqlStr = "SELECT MAX(codigo) FROM MovimentoContaCorrenteCliente WHERE MovimentoContaCorrenteCliente.pessoa = " + valorConsulta.intValue();
        int codigo;
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                if (!tabelaResultado.next()) {
                    return null;
                }
                codigo = tabelaResultado.getInt(1);
            }
        }
        if (codigo == 0) {
            return null;
        }
        return (consultarPorChavePrimaria(codigo, nivelMontarDados));
    }

    /**
     * Responsável por realizar uma consulta de
     * <code>MovimentoContaCorrenteCliente</code> através do valor do atributo
     * <code>Date dataRegistro</code>. Retorna os objetos com valores
     * pertecentes ao período informado por parâmetro. Faz uso da operação
     * <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o
     * List resultante.
     *
     * @param controlarAcesso Indica se a aplicação deverá verificar se o
     * usuário possui permissão para esta consulta ou não.
     * @return List Contendo vários objetos da
     * classe <code>MovimentoContaCorrenteClienteVO</code> resultantes da
     * consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de
     * acesso.
     */
    public List consultarPorDataRegistro(Date prmIni, Date prmFim, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT * FROM MovimentoContaCorrenteCliente WHERE ((dataRegistro >= '" + Uteis.getDataJDBC(prmIni) + "') and (dataRegistro <= '" + Uteis.getDataJDBC(prmFim) + "')) ORDER BY dataRegistro";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados, con));
            }
        }
    }

    /**
     * Responsável por realizar uma consulta de
     * <code>MovimentoContaCorrenteCliente</code> através do valor do atributo
     * <code>String descricao</code>. Retorna os objetos, com início do valor do
     * atributo idêntico ao parâmetro fornecido. Faz uso da operação
     * <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o
     * List resultante.
     *
     * @param controlarAcesso Indica se a aplicação deverá verificar se o
     * usuário possui permissão para esta consulta ou não.
     * @return List Contendo vários objetos da
     * classe <code>MovimentoContaCorrenteClienteVO</code> resultantes da
     * consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de
     * acesso.
     */
    public List consultarPorDescricao(String valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT * FROM MovimentoContaCorrenteCliente WHERE upper( descricao ) like('" + valorConsulta.toUpperCase() + "%') ORDER BY descricao";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados, con));
            }
        }
    }

    /**
     * Responsável por realizar uma consulta de
     * <code>MovimentoContaCorrenteCliente</code> através do valor do atributo
     * <code>Double saldoAtual</code>. Retorna os objetos com valores iguais ou
     * superiores ao parâmetro fornecido. Faz uso da operação
     * <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o
     * List resultante.
     *
     * @param controlarAcesso Indica se a aplicação deverá verificar se o
     * usuário possui permissão para esta consulta ou não.
     * @return List Contendo vários objetos da
     * classe <code>MovimentoContaCorrenteClienteVO</code> resultantes da
     * consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de
     * acesso.
     */
    public List consultarPorSaldoAtual(Double valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT * FROM MovimentoContaCorrenteCliente WHERE saldoAtual >= " + valorConsulta.doubleValue() + " ORDER BY saldoAtual";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados, con));
            }
        }
    }
    
    public Double consultarSaldoAtual(Integer codPessoa) throws Exception {
        String sql = "SELECT saldoAtual FROM MovimentoContaCorrenteCliente WHERE pessoa = ?"
                + " ORDER BY codigo DESC limit 1";
        try (PreparedStatement stm = con.prepareStatement(sql)) {
            stm.setInt(1, codPessoa);
            try (ResultSet resultTabela = stm.executeQuery()) {
                if (!resultTabela.next()) {
                    return 0.0;
                } else {
                    return resultTabela.getDouble(1);
                }
            }
        }
    }

    /**
     * Responsável por realizar uma consulta de
     * <code>MovimentoContaCorrenteCliente</code> através do valor do atributo
     * <code>Double saldoAnteior</code>. Retorna os objetos com valores iguais
     * ou superiores ao parâmetro fornecido. Faz uso da operação
     * <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o
     * List resultante.
     *
     * @param controlarAcesso Indica se a aplicação deverá verificar se o
     * usuário possui permissão para esta consulta ou não.
     * @return List Contendo vários objetos da
     * classe <code>MovimentoContaCorrenteClienteVO</code> resultantes da
     * consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de
     * acesso.
     */
    public List consultarPorSaldoAnteior(Double valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT * FROM MovimentoContaCorrenteCliente WHERE saldoAnteior >= " + valorConsulta.doubleValue() + " ORDER BY saldoAnteior";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados, con));
            }
        }
    }

    /**
     * Responsável por realizar uma consulta de
     * <code>MovimentoContaCorrenteCliente</code> através do valor do atributo
     * <code>matricula</code> da classe
     * <code>Cliente</code> Faz uso da operação
     * <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o
     * List resultante.
     *
     * @return List Contendo vários objetos da
     * classe <code>MovimentoContaCorrenteClienteVO</code> resultantes da
     * consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de
     * acesso.
     */
    public List consultarPorMatriculaCliente(String valorConsulta, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), true);
        String sqlStr = "SELECT MovimentoContaCorrenteCliente.* FROM MovimentoContaCorrenteCliente, Cliente WHERE MovimentoContaCorrenteCliente.cliente = Cliente.codigo and upper( Cliente.matricula ) like('" + valorConsulta.toUpperCase() + "%') ORDER BY Cliente.matricula";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return montarDadosConsulta(tabelaResultado, nivelMontarDados, con);
            }
        }
    }

    /**
     * Responsável por realizar uma consulta de
     * <code>MovimentoContaCorrenteCliente</code> através do valor do atributo
     * <code>matricula</code> da classe
     * <code>Cliente</code> Faz uso da operação
     * <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o
     * List resultante.
     *
     * @return List Contendo vários objetos da
     * classe <code>MovimentoContaCorrenteClienteVO</code> resultantes da
     * consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de
     * acesso.
     */
    public List consultarPorNomeCliente(String valorConsulta, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), true);
        String sqlStr = "SELECT MovimentoContaCorrenteCliente.* FROM MovimentoContaCorrenteCliente,Pessoa "
                + "WHERE MovimentoContaCorrenteCliente.pessoa = pessoa.codigo and  upper( pessoa.nome ) "
                + "like('" + valorConsulta.toUpperCase() + "%') ORDER BY MovimentoContaCorrenteCliente.dataregistro desc, pessoa.nome, MovimentoContaCorrenteCliente.codigo";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return montarDadosConsulta(tabelaResultado, nivelMontarDados, con);
            }
        }
    }

    /**
     * Responsável por realizar uma consulta de
     * <code>MovimentoContaCorrenteCliente</code> através do valor do atributo
     * <code>Integer codigo</code>. Retorna os objetos com valores iguais ou
     * superiores ao parâmetro fornecido. Faz uso da operação
     * <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o
     * List resultante.
     *
     * @param controlarAcesso Indica se a aplicação deverá verificar se o
     * usuário possui permissão para esta consulta ou não.
     * @return List Contendo vários objetos da
     * classe <code>MovimentoContaCorrenteClienteVO</code> resultantes da
     * consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de
     * acesso.
     */
    public List consultarPorCodigo(Integer valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT * FROM MovimentoContaCorrenteCliente WHERE codigo >= " + valorConsulta.intValue() + " ORDER BY codigo";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados, con));
            }
        }
    }
    
    public List consultarPorCodigoPessoa(Integer valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT * FROM MovimentoContaCorrenteCliente WHERE MovimentoContaCorrenteCliente.pessoa = " + valorConsulta.intValue() + " ORDER BY codigo";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados, con));
            }
        }
    }

    /**
     * Responsável por realizar uma consulta de
     * <code>MovimentoContaCorrenteCliente</code> através do valor do atributo
     * <code>Integer codigo</code>. Retorna os objetos com valores iguais ou
     * superiores ao parâmetro fornecido. Faz uso da operação
     * <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o
     * List resultante.
     *
     * @param controlarAcesso Indica se a aplicação deverá verificar se o
     * usuário possui permissão para esta consulta ou não.
     * @return List Contendo vários objetos da
     * classe <code>MovimentoContaCorrenteClienteVO</code> resultantes da
     * consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de
     * acesso.
     */
    public List consultarPorCodigoReciboPagamento(Integer valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT * FROM MovimentoContaCorrenteCliente WHERE recibopagamento = " + valorConsulta.intValue() + " ORDER BY codigo";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados, con));
            }
        }
    }

    /**
     * Responsável por realizar uma consulta de
     * <code>MovimentoContaCorrenteCliente</code> através do valor do atributo
     * <code>Integer codigo</code>. Retorna os objetos com valores iguais ou
     * superiores ao parâmetro fornecido. Faz uso da operação
     * <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o
     * List resultante.
     *
     * @param controlarAcesso Indica se a aplicação deverá verificar se o
     * usuário possui permissão para esta consulta ou não.
     * @return List Contendo vários objetos da
     * classe <code>MovimentoContaCorrenteClienteVO</code> resultantes da
     * consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de
     * acesso.
     */
    public List consultarPorCodigoContratoOperacao(Integer valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT * FROM MovimentoContaCorrenteCliente WHERE contratooperacao = " + valorConsulta.intValue() + " ORDER BY codigo";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados, con));
            }
        }
    }

    public ResultSet contarPendenciasClienteDevendoContaCorrente(int empresa, final String colaboradores) throws Exception {
        return contarPendenciasClienteDevendoContaCorrente(empresa,colaboradores, null);
    }
    
    public ResultSet contarPendenciasClienteDevendoContaCorrente(int empresa, final String colaboradores, Date dataBaseInicio) throws Exception {
        int qtde = 0;
        consultar(getIdEntidade(), false);
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT COUNT(distinct codigo) AS qtd, SUM(saldoatual) AS total  FROM (");
        sql.append("SELECT distinct cliente.codigo , mov.saldoatual   FROM movimentocontacorrentecliente mov ");
        sql.append("INNER JOIN cliente ON cliente.pessoa = mov.pessoa X ");
        sql.append("JOIN  (select pessoa, max(codigo) as maxCodigo\n");
        sql.append("from movimentocontacorrentecliente \n");
        sql.append("  group by pessoa\n");
        sql.append(") as sqlMax on sqlMax.maxCodigo = mov.codigo\n");
        sql.append(" WHERE mov.saldoatual < 0 ");

        if(empresa != 0){
            sql.append("AND cliente.empresa = ? ");
        }

        // filtra a consulta pelos colaboradores
        if (!UteisValidacao.emptyString(colaboradores)) {
            sql = new StringBuilder(sql.toString().replaceAll("X", "INNER JOIN vinculo ON vinculo.cliente = cliente.codigo "));
            sql.append(" AND (" + colaboradores + ")");
        } else {
            sql = new StringBuilder(sql.toString().replaceAll("X", ""));
        }
        if (dataBaseInicio != null) {//Data limite inicial != null
            sql.append(" AND EXISTS(SELECT pes.codigo FROM pessoa pes WHERE pes.codigo = cliente.pessoa AND pes.datacadastro >= '" + Uteis.getDataJDBC(dataBaseInicio) + " 00:00:00') \n");
        }
        sql.append(") as consulta");
        PreparedStatement ps = con.prepareStatement(sql.toString());

        if(empresa != 0){
            ps.setInt(1, empresa);
        }

        return ps.executeQuery();
    }
    
    public ResultSet contarPendenciasClienteCreditoContaCorrente(int empresa, final String colaboradores, Date dataBaseInicio) throws Exception {
        consultar(getIdEntidade(), false);
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT COUNT(distinct codigo) AS qtd, SUM(saldoatual) AS total  FROM (");
        sql.append("SELECT distinct cliente.codigo , mov.saldoatual   FROM movimentocontacorrentecliente mov ");
        sql.append("INNER JOIN cliente ON cliente.pessoa = mov.pessoa X ");
        sql.append("JOIN  (select pessoa, max(codigo) as maxCodigo\n");
        sql.append("from movimentocontacorrentecliente \n");
        sql.append("  group by pessoa\n");
        sql.append(") as sqlMax on sqlMax.maxCodigo = mov.codigo\n");
        sql.append(" WHERE mov.saldoatual > 0 ");

        if(empresa != 0){
            sql.append("AND cliente.empresa = ? ");
        }

        // filtra a consulta pelos colaboradores
        if (!UteisValidacao.emptyString(colaboradores)) {
            sql = new StringBuilder(sql.toString().replaceAll("X", "INNER JOIN vinculo ON vinculo.cliente = cliente.codigo "));
            sql.append(" AND (" + colaboradores + ")");
        } else {
            sql = new StringBuilder(sql.toString().replaceAll("X", ""));
        }
        if (dataBaseInicio != null) {//Data limite inicial != null
            sql.append(" AND EXISTS(SELECT pes.codigo FROM pessoa pes WHERE pes.codigo = cliente.pessoa AND pes.datacadastro >= '" + Uteis.getDataJDBC(dataBaseInicio) + " 00:00:00') \n");
        }
        sql.append(") as consulta");
        PreparedStatement ps = con.prepareStatement(sql.toString());

        if(empresa != 0){
            ps.setInt(1, empresa);
        }

        return ps.executeQuery();
    }
    
    public ResultSet consultarPendenciasClienteDevendoContaCorrente(int empresa, final String colaboradores, ConfPaginacao paginacao, Date dataBaseInicio) throws Exception {
        int qtde = 0;
        consultar(getIdEntidade(), false);
        StringBuilder sqlStr = new StringBuilder();
        sqlStr.append(" SELECT * FROM (\n");
        sqlStr.append("SELECT DISTINCT ON (dw.codigocliente)dw.codigocliente AS cli,mov.dataregistro,dw.nomePlano as nomePlanoPrincipal,mov.saldoatual as saldoAtual,\n");
        sqlStr.append("mov.dataregistro as lancamento,dw.nomecliente as nome,dw.situacao as situacaoCliente,mov.pessoa as codPessoa,cd.numeroMeses as duracaoContratoPrincipal,dw.matricula as matriculaCli,\n");
        sqlStr.append("dw.telefonescliente, dw.codigocontrato as codContrato,dw.datavigenciade as dataInicio,dw.datavigenciaateajustada as dataFim,cd.numeromeses as duracaoContrato,ct.nomemodalidades,dw.nomePlano,dw.cpf");
        sqlStr.append(" FROM movimentocontacorrentecliente AS mov ");
        sqlStr.append(" INNER JOIN Situacaoclientesinteticodw dw on dw.codigopessoa = mov.pessoa \nX\n");
        sqlStr.append(" LEFT JOIN Contrato ct ON   ct.codigo = dw.codigocontrato \n");
        sqlStr.append("LEFT JOIN ContratoDuracao cd ON cd.contrato = ct.codigo\n");
        sqlStr.append("WHERE mov.codigo = (SELECT codigo FROM movimentocontacorrentecliente WHERE movimentocontacorrentecliente.pessoa = mov.pessoa ");
        sqlStr.append("ORDER BY movimentocontacorrentecliente.codigo DESC ");
        sqlStr.append("LIMIT 1) AND ");
        sqlStr.append("mov.saldoatual < -0 ");

        if(empresa != 0){
            sqlStr.append("AND dw.empresacliente = ? ");
        }

        // filtra a consulta pelos colaboradores
        if (!UteisValidacao.emptyString(colaboradores)) {
            String i = sqlStr.toString().replaceAll("X", "INNER JOIN vinculo ON vinculo.cliente = dw.codigocliente ");
            sqlStr = new StringBuilder(i);
            sqlStr.append(" AND (" + colaboradores + ")");
        } else {
            String i = sqlStr.toString().replaceAll("X", "");
            sqlStr = new StringBuilder(i);
        }

        if (dataBaseInicio != null) {
            sqlStr.append(" AND mov.dataregistro >= '" + Uteis.getData(dataBaseInicio) + " 00:00:00'");
        }

        sqlStr.append(" GROUP BY cli,lancamento,dw.telefonescliente,saldoAtual,codContrato,nome,dataInicio,dataFim,codPessoa,duracaoContrato,nomemodalidades,");
        sqlStr.append("nomePlano,mov.dataregistro,dw.nomePlano,situacaoCliente,duracaoContrato,matriculaCli,cpf");
        sqlStr.append(" ) as consulta \n");
        if (paginacao != null && paginacao.getOrdernar()) {
            sqlStr.append(" ORDER BY ").append(paginacao.getColunaOrdenacao()).append(" ").append(paginacao.getDirecaoOrdenacao());
        }
        if (paginacao != null && paginacao.isExistePaginacao()) {
            sqlStr.append(" limit ").append(paginacao.getItensPorPagina()).append(" offset ").append((paginacao.getPaginaAtual() * paginacao.getItensPorPagina()));
        }
        PreparedStatement sql = con.prepareStatement(sqlStr.toString());

        if(empresa != 0){
            sql.setInt(1, empresa);
        }

        return sql.executeQuery();
    }
    
    public String consultarPendenciasClienteCreditoContaCorrenteJSON(int empresa, final String colaboradores, Date dataBaseInicio) throws Exception {
        StringBuilder json = new StringBuilder();
        json.append("{\"aaData\":[");
        boolean dados = false;

        ResultSet rs = consultarPendenciasClienteCreditoContaCorrente(empresa, colaboradores, null, dataBaseInicio);

        while (rs.next()) {
            dados = true;
            json.append("[\"").append(rs.getString("cli")).append("\",");
            json.append("").append(Integer.parseInt(rs.getString("matriculacli"))).append(",");
            json.append("\"").append(Uteis.normalizarStringJSON(rs.getString("nome"))).append("\",");
            json.append("\"").append(rs.getString("cpf")).append("\",");
            json.append("\"").append(SituacaoClienteEnum.getSituacaoCliente(rs.getString("situacaoCliente")) != null ? SituacaoClienteEnum.getSituacaoCliente(rs.getString("situacaoCliente")).getDescricao() : "").append("\",");
            json.append("\"").append(rs.getString("moeda") + " " + Formatador.formatarValorMonetarioSemMoeda(rs.getDouble("saldoatual"))).append("\",");
            json.append("\"").append(rs.getDate("lancamento")).append("\",");
            json.append("\"").append(Uteis.normalizarStringJSON(rs.getString("nomePlano"))).append("\",");
            json.append("\"").append(rs.getString("duracaoContrato")).append("\",");
            json.append("\"").append(Uteis.normalizarStringJSON(UteisValidacao.emptyString(rs.getString("telefonescliente")) ? "-" : rs.getString("telefonescliente"))).append("\",");
            json.append("").append(rs.getDouble("saldoatual")).append("],");
        }

        if (dados) {
            json.deleteCharAt(json.toString().length() - 1);
        }
        json.append("]}");
        return json.toString();
    }
    public ResultSet consultarPendenciasClienteCreditoContaCorrente(int empresa, final String colaboradores, ConfPaginacao paginacao) throws Exception {
        return consultarPendenciasClienteCreditoContaCorrente(empresa, colaboradores, paginacao, null);
    }
    
    public ResultSet consultarPendenciasClienteCreditoContaCorrente(int empresa, final String colaboradores, ConfPaginacao paginacao, Date dataBaseInicio) throws Exception {
        consultar(getIdEntidade(), false);
        StringBuilder sqlStr = new StringBuilder();
        sqlStr.append("SELECT\n");
        sqlStr.append("  cliente.codigo AS cli,\n");
        sqlStr.append("  empresa.moeda,\n");
        sqlStr.append("  cliente.matricula as matriculacli,\n");
        sqlStr.append("  pes.nome,\n");
        sqlStr.append("  pes.codigo as codPessoa,\n");
        sqlStr.append("  cliente.situacao as situacaoCliente,\n");
        sqlStr.append("  mov.saldoatual,\n");
        sqlStr.append("  mov.dataregistro as lancamento,\n");
        sqlStr.append("  situacaoclientesinteticodw.nomePlano,\n");
        sqlStr.append("  situacaoclientesinteticodw.codigocontrato as codContrato,\n");
        sqlStr.append("  situacaoclientesinteticodw.datavigenciade as dataInicio,\n");
        sqlStr.append("  situacaoclientesinteticodw.datavigenciaateajustada as dataFim,\n");
        sqlStr.append("  situacaoclientesinteticodw.modalidades  as nomeModalidades,\n");
        sqlStr.append("  situacaoclientesinteticodw.duracaocontratomeses as duracaoContrato,\n");
        sqlStr.append("  situacaoclientesinteticodw.telefonescliente as telefonescliente,\n");
        sqlStr.append("  situacaoclientesinteticodw.cpf as cpf\n");
        sqlStr.append("FROM movimentocontacorrentecliente AS mov\n");
        sqlStr.append("  INNER JOIN cliente ON cliente.pessoa = mov.pessoa\n");
        sqlStr.append("  INNER JOIN empresa on cliente.empresa = empresa\n");
        sqlStr.append("  LEFT JOIN situacaoclientesinteticodw on situacaoclientesinteticodw.codigocliente = cliente.codigo\n");
        sqlStr.append("  LEFT JOIN pessoa pes ON pes.codigo = cliente.pessoa X ");
        sqlStr.append("WHERE mov.codigo = (SELECT codigo FROM movimentocontacorrentecliente ");
        sqlStr.append("WHERE movimentocontacorrentecliente.pessoa = mov.pessoa ");
        sqlStr.append("ORDER BY movimentocontacorrentecliente.codigo DESC ");
        sqlStr.append("LIMIT 1) AND ");
        sqlStr.append("mov.saldoatual > 0 ");

        if(empresa != 0){
            sqlStr.append(" AND cliente.empresa = ? ");
        }

        if (dataBaseInicio != null) {
            sqlStr.append(" AND mov.dataregistro >= '" + Uteis.getDataJDBC(dataBaseInicio) + " 00:00:00' \n");
        }

        // filtra a consulta pelos colaboradores
        if (!UteisValidacao.emptyString(colaboradores)) {
            String i = sqlStr.toString().replaceAll("X", "INNER JOIN vinculo ON vinculo.cliente = cliente.codigo ");
            sqlStr = new StringBuilder(i);
            sqlStr.append(" AND (" + colaboradores + ")");
        } else {
            String i = sqlStr.toString().replaceAll("X", "");
            sqlStr = new StringBuilder(i);
        }
        sqlStr.append("GROUP BY empresa.moeda,situacaoclientesinteticodw.codigocontrato, situacaoclientesinteticodw.datavigenciade,situacaoclientesinteticodw.datavigenciaateajustada, situacaoclientesinteticodw.modalidades, cliente.codigo, cliente.matricula, pes.nome,pes.codigo, cliente.situacao, saldoatual,lancamento, situacaoclientesinteticodw.nomePlano, situacaoclientesinteticodw.duracaocontratomeses,situacaoclientesinteticodw.telefonescliente,situacaoclientesinteticodw.cpf");
        
        if (paginacao != null && paginacao.isExistePaginacao()) {
            sqlStr.append(" limit ").append(paginacao.getItensPorPagina()).append(" offset ").append((paginacao.getPaginaAtual() * paginacao.getItensPorPagina()));
        }
        PreparedStatement sql = con.prepareStatement(sqlStr.toString());

        if(empresa != 0){
            sql.setInt(1, empresa);
        }

        return sql.executeQuery();
    }

    /**
     * Responsável por montar os dados de vários objetos, resultantes de uma
     * consulta ao banco de dados (
     * <code>ResultSet</code>). Faz uso da operação
     * <code>montarDados</code> que realiza o trabalho para um objeto por vez.
     *
     * @return List Contendo vários objetos da
     * classe <code>MovimentoContaCorrenteClienteVO</code> resultantes da
     * consulta.
     */
    public static List montarDadosConsulta(ResultSet tabelaResultado, int nivelMontarDados, Connection con) throws Exception {
        List vetResultado = new ArrayList();
        while (tabelaResultado.next()) {
            MovimentoContaCorrenteClienteVO obj = montarDados(tabelaResultado, nivelMontarDados, con);
            vetResultado.add(obj);
        }
        return vetResultado;
    }

    /**
     * Responsável por montar os dados resultantes de uma consulta ao banco de
     * dados (
     * <code>ResultSet</code>) em um objeto da classe
     * <code>MovimentoContaCorrenteClienteVO</code>.
     *
     * @return O objeto da classe <code>MovimentoContaCorrenteClienteVO</code>
     * com os dados devidamente montados.
     */
    public static MovimentoContaCorrenteClienteVO montarDados(ResultSet dadosSQL, int nivelMontarDados, Connection con) throws Exception {
        MovimentoContaCorrenteClienteVO obj = new MovimentoContaCorrenteClienteVO();
        obj.setCodigo(new Integer(dadosSQL.getInt("codigo")));
        obj.getPessoa().setCodigo(new Integer(dadosSQL.getInt("pessoa")));
        obj.setSaldoAtual(new Double(dadosSQL.getDouble("saldoAtual")));
        obj.setDescricao(dadosSQL.getString("descricao"));
        obj.getReciboPagamentoVO().setCodigo(new Integer(dadosSQL.getInt("recibopagamento")));
        obj.setDataRegistro(dadosSQL.getTimestamp("dataRegistro"));
        obj.setTipoMovimentacao(dadosSQL.getString("tipoMovimentacao"));
        obj.getResponsavelAutorizacao().setCodigo(new Integer(dadosSQL.getInt("responsavelAutorizacao")));
        obj.setValor(new Double(dadosSQL.getDouble("valor")));
        obj.setNovoObj(new Boolean(false));
        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_DADOSBASICOS) {
            return obj;
        }
        
        montarDadosPessoa(obj, Uteis.NIVELMONTARDADOS_DADOSBASICOS, con);
        montarDadosContratoOperacao(obj, Uteis.NIVELMONTARDADOS_DADOSBASICOS, con);
        montarDadosResponsavelAutorizacao(obj, Uteis.NIVELMONTARDADOS_TODOS, con);
        montarDadosListaMovPagamentos(obj, Uteis.NIVELMONTARDADOS_DADOSBASICOS, con);
        return obj;
    }
    
    private static void montarDadosListaMovPagamentos(
            MovimentoContaCorrenteClienteVO obj,
            int nivelmontardadosDadosbasicos, Connection con) throws Exception {
        List<MovPagamentoVO> movPagamentos = new ArrayList<MovPagamentoVO>();
        MovimentoContaCorrenteClienteComposicao composicaoDao = new MovimentoContaCorrenteClienteComposicao(con);
        MovPagamento movPagamentoDao = new MovPagamento(con);
        List<MovimentoContaCorrenteClienteComposicaoVO> composicao = composicaoDao.consultarPorCodigoMovimento(obj.getCodigo(), false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        Iterator i = composicao.iterator();
        while (i.hasNext()) {
            MovimentoContaCorrenteClienteComposicaoVO moviCom = (MovimentoContaCorrenteClienteComposicaoVO) i.next();
            MovPagamentoVO movpagamento = null;
            movpagamento = movPagamentoDao.consultarPorChavePrimaria(moviCom.getMovpagamento(), Uteis.NIVELMONTARDADOS_TODOS);
            movpagamento.setNovoObj(false);
            movPagamentos.add(movpagamento);
        }
        obj.setMovPagamentosVOs(movPagamentos);
        
        
    }
    
    private ResultSet consultarMovimentoComposicao(Integer movimento) throws SQLException {
        String sql = "SELECT * FROM MovimentoContaCorrenteClienteComposicao WHERE movimentocontacorrentecliente = ?";
        PreparedStatement sqlConsultar = con.prepareStatement(sql);
        sqlConsultar.setInt(1, movimento.intValue());
        ResultSet composicao = sqlConsultar.executeQuery();

        return composicao;
    }

    /**
     * Operação responsável por montar os dados de um objeto da classe
     * <code>ColaboradorVO</code> relacionado ao objeto
     * <code>MovimentoContaCorrenteClienteVO</code>. Faz uso da chave primária
     * da classe
     * <code>ColaboradorVO</code> para realizar a consulta.
     *
     * @param obj Objeto no qual será montado os dados consultados.
     */
    public static void montarDadosResponsavelAutorizacao(MovimentoContaCorrenteClienteVO obj, int nivelMontarDados, Connection con) throws Exception {
        if (obj.getResponsavelAutorizacao().getCodigo().intValue() == 0) {
            obj.setResponsavelAutorizacao(new UsuarioVO());
            return;
        }
        Usuario usuario = new Usuario(con);
        obj.setResponsavelAutorizacao(usuario.consultarPorChavePrimaria(obj.getResponsavelAutorizacao().getCodigo(), nivelMontarDados));
    }

    /**
     * Operação responsável por montar os dados de um objeto da classe
     * <code>ClienteVO</code> relacionado ao objeto
     * <code>MovimentoContaCorrenteClienteVO</code>. Faz uso da chave primária
     * da classe
     * <code>ClienteVO</code> para realizar a consulta.
     *
     * @param obj Objeto no qual será montado os dados consultados.
     */
    public static void montarDadosPessoa(MovimentoContaCorrenteClienteVO obj, int nivelMontarDados, Connection con) throws Exception {
        if (obj.getPessoa().getCodigo().intValue() == 0) {
            obj.setPessoa(new PessoaVO());
            return;
        }
        Pessoa pessoaDao = new Pessoa(con);
        obj.setPessoa(pessoaDao.consultarPorChavePrimaria(obj.getPessoa().getCodigo(), nivelMontarDados));
    }

    /**
     * Operação responsável por montar os dados de um objeto da classe
     * <code>ClienteVO</code> relacionado ao objeto
     * <code>MovimentoContaCorrenteClienteVO</code>. Faz uso da chave primária
     * da classe
     * <code>ClienteVO</code> para realizar a consulta.
     *
     * @param obj Objeto no qual será montado os dados consultados.
     */
    public static void montarDadosReciboPagamento(MovimentoContaCorrenteClienteVO obj, int nivelMontarDados, Connection con) throws Exception {
        if (obj.getReciboPagamentoVO().getCodigo().intValue() == 0) {
            obj.setReciboPagamentoVO(new ReciboPagamentoVO());
            return;
        }
        ReciboPagamento recibo = new ReciboPagamento(con);
        obj.setReciboPagamentoVO(recibo.consultarPorChavePrimaria(obj.getReciboPagamentoVO().getCodigo(), nivelMontarDados));
    }

    /**
     * Operação responsável por montar os dados de um objeto da classe
     * <code>ClienteVO</code> relacionado ao objeto
     * <code>MovimentoContaCorrenteClienteVO</code>. Faz uso da chave primária
     * da classe
     * <code>ClienteVO</code> para realizar a consulta.
     *
     * @param obj Objeto no qual será montado os dados consultados.
     */
    public static void montarDadosContratoOperacao(MovimentoContaCorrenteClienteVO obj, int nivelMontarDados, Connection con) throws Exception {
        if (obj.getContratoOperacaoVO().getCodigo().intValue() == 0) {
            obj.setContratoOperacaoVO(new ContratoOperacaoVO());
            return;
        }
        ContratoOperacao contratoOperacao = new ContratoOperacao(con);
        obj.setContratoOperacaoVO(contratoOperacao.consultarPorChavePrimaria(obj.getContratoOperacaoVO().getCodigo(), nivelMontarDados));
    }

    /**
     * Operação responsável por localizar um objeto da classe
     * <code>MovimentoContaCorrenteClienteVO</code> através de sua chave
     * primária.
     *
     * @exception Exception Caso haja problemas de conexão ou localização do
     * objeto procurado.
     */
    public MovimentoContaCorrenteClienteVO consultarPorChavePrimaria(Integer codigoPrm, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), false);
        String sql = "SELECT * FROM MovimentoContaCorrenteCliente WHERE codigo = ?";
        try (PreparedStatement sqlConsultar = con.prepareStatement(sql)) {
            sqlConsultar.setInt(1, codigoPrm.intValue());
            try (ResultSet tabelaResultado = sqlConsultar.executeQuery()) {
                if (!tabelaResultado.next()) {
                    throw new ConsistirException("Dados Não Encontrados ( MovimentoContaCorrenteCliente ).");
                }
                return (montarDados(tabelaResultado, nivelMontarDados, con));
            }
        }
    }

    /**
     * Metodo responsavel por retornar uma consulta paginada em banco, de acordo
     * com os filtros passados
     *
     * Autor: Carla Criado em 13/01/2011
     */
    @SuppressWarnings("unchecked")
    public List consultarPaginado(MovimentoContaCorrenteClienteFiltroVO filtro, ConfPaginacao confPaginacao) throws Exception {
        consultar(getIdEntidade(), filtro.isControlarAcesso());

        //1- CONFIGURANDO A NAVEGACAO DA PAGINACAO
        confPaginacao.configurarNavegacao();

        //sql principal
        StringBuffer sqlSelect = new StringBuffer("SELECT movimentocontacorrentecliente.* FROM movimentocontacorrentecliente");

        //sql inner joins
        associacoes(filtro, sqlSelect);

        //2 - CONFIGURA A INICIALIZACAO DA PAGINACAO PARA ESTA DAO.
        confPaginacao.iniciarPaginacao(this);

        //a utilização dos filtros deve ser feita utilizando confPaginacao.getSqlFiltro()
        filtros(filtro, confPaginacao.getSqlFiltro());
        if (!"".equals(confPaginacao.getSqlFiltro().toString())) {
            sqlSelect.append(" WHERE ");
            sqlSelect.append(confPaginacao.getSqlFiltro().toString());
        }

        //concatena ordenações
        ordenacao(filtro, sqlSelect);

//		System.out.println("sql: " + sqlSelect);

        //3 - ADICIONA PAGINACAO NA CONSULTA
        confPaginacao.addPaginacao(sqlSelect);

        //seta os valores dos filtros
        filtrosValoresStatement(filtro, confPaginacao.getStm());

        //4 - REALIZA A CONSULTA COM PAGINACAO
        try (ResultSet tabelaResultado = confPaginacao.consultaPaginada()) {
            return (montarDadosConsulta(tabelaResultado, filtro.getNivelMontarDados(), con));
        }
    }

    /**
     * Atribuindo valores para os filtros no Statement para o metodo
     * consultarPaginado(ReciboPagamentoFiltroVO filtro, ConfPaginacao
     * confPaginacao)
     *
     * Autora: Carla Criado em 14/01/2011
     */
    private void filtrosValoresStatement(MovimentoContaCorrenteClienteFiltroVO filtro, PreparedStatementPersonalizado stm) throws SQLException, Exception {
        int i = 0;
        //INICIO - SETANDO PARAMETROS PARA "filtros(ReciboPagamentoFiltroVO filtro, StringBuffer sqlFiltro)"
        if (filtro != null && filtro.getMovContaCorrenteClienteVO() != null) {
            /**
             * Consulta por nome de cliente*
             */
            if (filtro.getMovContaCorrenteClienteVO().getPessoa() != null) {
                if (filtro.getMovContaCorrenteClienteVO().getPessoa().getNome() != null && !"".equals(filtro.getMovContaCorrenteClienteVO().getPessoa().getNome())) {
                    stm.setString(i++, filtro.getMovContaCorrenteClienteVO().getPessoa().getNome() + "%");
                }
            }
            /**
             * Consulta por codigo*
             */
            if (filtro.getMovContaCorrenteClienteVO().getCodigo() > 0) {
                stm.setInt(i++, filtro.getMovContaCorrenteClienteVO().getCodigo());
            }
            /**
             * Consulta por descricao*
             */
            if (filtro.getMovContaCorrenteClienteVO().getDescricao() != null && !"".equals(filtro.getMovContaCorrenteClienteVO().getDescricao())) {
                stm.setString(i++, filtro.getMovContaCorrenteClienteVO().getDescricao() + "%");
            }
            /**
             * Consulta por data de registro*
             */
            if (filtro.getMovContaCorrenteClienteVO().getDataRegistro() != null) {
                Date valorData = filtro.getMovContaCorrenteClienteVO().getDataRegistro();
                stm.setDate(i++, Uteis.getDataJDBC(valorData));
                stm.setDate(i++, Uteis.getDataJDBC(valorData));
            }
            if (filtro != null && filtro.getCodigoPessoa() > 0) {
                stm.setInt(i++, filtro.getCodigoPessoa());
            }
            if (filtro.getEmpresaVO() != null && filtro.getEmpresaVO().getCodigo() > 0) {
                stm.setInt(i++, filtro.getEmpresaVO().getCodigo());
            }

            //FIM - SETANDO PARAMETROS PARA "filtros(ReciboPagamentoFiltroVO filtro, StringBuffer sqlFiltro)"
        }
    }

    /**
     * Definindo o tipo de ordenacao para o metodo
     * consultarPaginado(ReciboPagamentoFiltroVO filtro, ConfPaginacao
     * confPaginacao)
     *
     * Autora: Carla Criado em 14/01/2011
     */
    private void ordenacao(MovimentoContaCorrenteClienteFiltroVO filtro, StringBuffer sql) {
        sql.append(" ORDER BY movimentocontacorrentecliente.codigo");
    }

    /**
     * Definindo as condicoes da clausa WHERE para o metodo
     * consultarPaginado(ClienteFiltroVO filtro, ConfPaginacao confPaginacao)
     *
     * Autora: Carla Criado em 14/01/2011
     */
    private void filtros(MovimentoContaCorrenteClienteFiltroVO filtro, StringBuffer sqlFiltro) {
        /**
         * Consulta por nome de cliente*
         */
        if (filtro != null && filtro.getMovContaCorrenteClienteVO().getPessoa() != null && !filtro.getMovContaCorrenteClienteVO().getPessoa().getNome().trim().isEmpty()) {
            sqlFiltro.append(" pessoa.nome ilike ?");
        } /**
         * Consulta por codigo*
         */
        else if (filtro != null && filtro.getMovContaCorrenteClienteVO().getCodigo() > 0) {
            sqlFiltro.append(" movimentocontacorrentecliente.codigo  = ?");
        } /**
         * Consulta por descricao*
         */
        else if (filtro != null && !filtro.getMovContaCorrenteClienteVO().getDescricao().trim().isEmpty()) {
            sqlFiltro.append(" movimentocontacorrentecliente.descricao ilike ?");
        }/**
         * Consulta por data de registro*
         */
        else if (filtro != null && filtro.getMovContaCorrenteClienteVO().getDataRegistro() != null) {
            sqlFiltro.append(" dataRegistro BETWEEN ? AND ?");
        } else if (filtro != null && filtro.getCodigoPessoa() > 0) {
            sqlFiltro.append(" movimentocontacorrentecliente.pessoa  = ?");
        }
        if (filtro != null && filtro.getEmpresaVO() != null && filtro.getEmpresaVO().getCodigo() > 0) {
            if (!"".equals(sqlFiltro.toString())) {
                sqlFiltro.append(" AND empresa.codigo = ?");
            } else {
                sqlFiltro.append(" empresa.codigo  = ?");
            }
        }
        
    }

    /**
     * Definindo os Inner Join e Left Join para o metodo
     * consultarPaginado(ReciboPagamentoFiltroVO filtro, ConfPaginacao
     * confPaginacao)
     *
     * Autora: Carla Criado em 14/01/2011
     */
    private void associacoes(MovimentoContaCorrenteClienteFiltroVO filtro, StringBuffer sql) {
        if (filtro != null && filtro.getMovContaCorrenteClienteVO() != null && filtro.getMovContaCorrenteClienteVO().getPessoa() != null) {
            sql.append(" inner join pessoa on movimentocontacorrentecliente.pessoa = pessoa.codigo ");
            if (filtro != null && filtro.getEmpresaVO() != null && filtro.getEmpresaVO().getCodigo() > 0) {
                sql.append(" inner join cliente on cliente.pessoa = pessoa.codigo ");
                sql.append(" inner join empresa on cliente.empresa = empresa.codigo ");
            }
        }
    }

    /**
     * Metodo responsavel por gerenciar a adição de novos movimentos
     *
     * Autora: Marcos Criado em 14/01/2013
     */
    public void adicionarmovimento(MovimentoContaCorrenteClienteVO novo)
            throws Exception {
        
        MovimentoContaCorrenteClienteVO atual = consultarPorCodigoPessoa(novo.getPessoa().getCodigo(), Uteis.NIVELMONTARDADOS_TODOS);
        if (atual != null) {
            novo.setSaldoAnterior(Uteis.arredondarForcando2CasasDecimaisMantendoSinal(atual.getSaldoAtual()));
            atual.setMovPagamentosVOs(Ordenacao.ordenarLista(atual.getMovPagamentosVOs(), "dataLancamento"));
        } else {
            novo.setSaldoAnterior(0.0);
        }
        
        if (atual == null || atual.getSaldoAtual() == 0) { // saldo zerado
            if (novo.getTipoMovimentacao().equals("CR")) {
                novo.setSaldoAtual(novo.getValor());
            } else {
                novo.setSaldoAtual(novo.getValor() * (-1));
            }
            incluirSemCommit(novo);
        } else if (atual.getSaldoAtual() > 0) { //saldo positivo
            if (novo.getTipoMovimentacao().equals("CR")) {// credito
                novo.setSaldoAtual(Uteis.arredondarForcando2CasasDecimaisMantendoSinal(novo.getValor() + atual.getSaldoAtual()));
                incluirSemCommit(novo);

                MovimentoContaCorrenteClienteComposicao movDAO = new MovimentoContaCorrenteClienteComposicao(con);
                movDAO.alterarComposicao(novo.getCodigo(), atual.getCodigo());
                movDAO = null;
                
            } else { // debito
                novo.setSaldoAtual(Uteis.arredondarForcando2CasasDecimaisMantendoSinal(atual.getSaldoAtual() - novo.getValor()));
                novo.setMovPagamentosVOs(atualizarPagamentos(atual.getMovPagamentosVOs(), novo.getMovPagamentosVOs(), false));
                incluirSemCommit(novo);
            }
            
        } else { // saldo negativo
            if (atual.getMovPagamentosVOs() == null || atual.getMovPagamentosVOs().isEmpty()) { //debito não justificado
                Cliente clienteDAO = new Cliente(con);
                ClienteVO cliente = clienteDAO.consultarPorCodigoPessoa(atual.getPessoa().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                clienteDAO = null;
                VendaAvulsaVO vendaMovimento = gerarProdutoPagamentoDebito(Uteis.arredondarForcando2CasasDecimais(atual.getSaldoAtual()), cliente, TipoOperacaoContaCorrenteEnum.TOCC_Receber, novo.getResponsavelAutorizacao());
                gerarPagamentoMovimentoNaoJustificado(vendaMovimento);
                atual = consultarPorCodigoPessoa(novo.getPessoa().getCodigo(), Uteis.NIVELMONTARDADOS_TODOS);
            }
            if (!novo.getTipoMovimentacao().equals("CR")) {//debito
                novo.setSaldoAtual(Uteis.arredondarForcando2CasasDecimaisMantendoSinal(atual.getSaldoAtual() - novo.getValor()));
                incluirSemCommit(novo);
                MovimentoContaCorrenteClienteComposicao movDAO = new MovimentoContaCorrenteClienteComposicao(con);
                movDAO.alterarComposicao(novo.getCodigo(), atual.getCodigo());
                movDAO = null;
            } else { //credito
                novo.setSaldoAtual(Uteis.arredondarForcando2CasasDecimaisMantendoSinal(atual.getSaldoAtual() + novo.getValor()));
                novo.setMovPagamentosVOs(atualizarPagamentos(novo.getMovPagamentosVOs(), atual.getMovPagamentosVOs(), false));
                incluirSemCommit(novo);
            }
        }
        
    }
    
    public void gerarDebito(MovimentoContaCorrenteClienteVO movimento) throws Exception {
        incluir(getIdEntidade());
        ClienteVO cliente = getFacade().getCliente().consultarPorCodigoPessoa(movimento.getPessoa().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        VendaAvulsaVO vendaMovimento = gerarProdutoPagamentoDebito(Uteis.arredondarForcando2CasasDecimais(movimento.getValor()), cliente, TipoOperacaoContaCorrenteEnum.TOCC_Acerto, movimento.getResponsavelAutorizacao());
        gerarPagamentoAcertoConta(vendaMovimento, movimento);

        vendaMovimento.setObjetoVOAntesAlteracao(new VendaAvulsaVO());
        vendaMovimento.setNovoObj(true);
        registrarLogObjetoVO(vendaMovimento, vendaMovimento.getCodigo().intValue(), "MOVIMENTOCONTACORRENTECLIENTE",
                vendaMovimento.getCliente().getPessoa().getCodigo());
        
    }
    
    private void gerarPagamentoAcertoConta(
            VendaAvulsaVO vendaMovimento, MovimentoContaCorrenteClienteVO movimento) throws Exception {
        
        MovPagamentoVO contaCorrente = new MovPagamentoVO();
        contaCorrente.setValor(vendaMovimento.getValorTotal());
        contaCorrente.setValorTotal(vendaMovimento.getValorTotal());
        contaCorrente.setFormaPagamento(getFacade().getFormaPagamento().consultarPorTipoFormaPagamentoAtiva("CC", false, Uteis.NIVELMONTARDADOS_DADOSBASICOS));
        contaCorrente.setResponsavelPagamento(vendaMovimento.getResponsavel());
        contaCorrente.setMovPagamentoEscolhida(true);
        contaCorrente.setNrParcelaCartaoCredito(0);
        contaCorrente.setNomePagador(vendaMovimento.getCliente().getPessoa().getNome());
        contaCorrente.setDataLancamento(vendaMovimento.getDataRegistro());
        contaCorrente.setDataPagamento(vendaMovimento.getDataRegistro());
        contaCorrente.setPessoa(vendaMovimento.getCliente().getPessoa());
        contaCorrente.setDataQuitacao(vendaMovimento.getDataRegistro());
        contaCorrente.setEmpresa(vendaMovimento.getEmpresa());
        contaCorrente.setCredito(false);
        
        List<MovPagamentoVO> pagamentos = new ArrayList<MovPagamentoVO>();
        List<MovParcelaVO> parcelas = new ArrayList<MovParcelaVO>();
        
        MovParcelaVO movParcela = getFacade().getMovParcela().consultarPorCodigoVendaAvulsa(vendaMovimento.getCodigo().intValue(),
                "EA",
                false, Uteis.NIVELMONTARDADOS_MINIMOS);
        movParcela.setSituacao("EA");
        
        parcelas.add(movParcela);
        pagamentos.add(contaCorrente);

        MovPagamento movPagamentoDAO = new MovPagamento(con);
        movPagamentoDAO.incluirListaPagamento(pagamentos, parcelas, movimento, null, false, 0.0);
        movPagamentoDAO = null;
    }
    
    private void gerarPagamentoMovimentoNaoJustificado(
            VendaAvulsaVO vendaMovimento) throws Exception {
        MovimentoContaCorrenteClienteVO movCCVO = new MovimentoContaCorrenteClienteVO();
        movCCVO.setDataRegistro(negocio.comuns.utilitarias.Calendario.hoje());
        movCCVO.setDescricao("PAGAMENTO DEBITO INJUSTIFICADO");
        movCCVO.setPessoa(vendaMovimento.getCliente().getPessoa());
        movCCVO.setResponsavelAutorizacao(vendaMovimento.getResponsavel());
        movCCVO.setTipoMovimentacao("DE");
        movCCVO.setValor(vendaMovimento.getValorTotal());
        
        MovPagamentoVO contaCorrente = new MovPagamentoVO();
        contaCorrente.setValor(vendaMovimento.getValorTotal());
        contaCorrente.setValorTotal(vendaMovimento.getValorTotal());
        FormaPagamento formaPagamentoDAO = new FormaPagamento(con);
        contaCorrente.setFormaPagamento(formaPagamentoDAO.consultarPorTipoFormaPagamentoAtiva("CC", false, Uteis.NIVELMONTARDADOS_DADOSBASICOS));
        formaPagamentoDAO = null;
        contaCorrente.setResponsavelPagamento(vendaMovimento.getResponsavel());
        contaCorrente.setMovPagamentoEscolhida(true);
        contaCorrente.setNrParcelaCartaoCredito(0);
        contaCorrente.setNomePagador(vendaMovimento.getCliente().getPessoa().getNome());
        contaCorrente.setDataLancamento(vendaMovimento.getDataRegistro());
        contaCorrente.setDataPagamento(vendaMovimento.getDataRegistro());
        contaCorrente.setPessoa(vendaMovimento.getCliente().getPessoa());
        contaCorrente.setDataQuitacao(vendaMovimento.getDataRegistro());
        contaCorrente.setEmpresa(vendaMovimento.getEmpresa());
        contaCorrente.setCredito(false);
        
        List<MovPagamentoVO> pagamentos = new ArrayList<MovPagamentoVO>();
        List<MovParcelaVO> parcelas = new ArrayList<MovParcelaVO>();

        MovParcela movParcelaDAO = new MovParcela(con);
        MovParcelaVO movParcela = movParcelaDAO.consultarPorCodigoVendaAvulsa(vendaMovimento.getCodigo(), "EA", false, Uteis.NIVELMONTARDADOS_MINIMOS);
        movParcelaDAO = null;

        movParcela.setSituacao("EA");
        
        parcelas.add(movParcela);
        pagamentos.add(contaCorrente);

        MovPagamento movPagamentoDAO = new MovPagamento(con);
        movPagamentoDAO.incluirListaPagamento(pagamentos, parcelas, movCCVO, null, false, 0.0, false,null);
        movPagamentoDAO = null;
    }
    
    public VendaAvulsaVO gerarProdutoPagamentoCredito(Double valor, ClienteVO cliente, TipoOperacaoContaCorrenteEnum tipo, UsuarioVO responsavel, String descricao) throws Exception {
        return gerarProdutoPagamentoCredito(valor, cliente, tipo, responsavel, descricao, negocio.comuns.utilitarias.Calendario.hoje(), cliente.getEmpresa());
    }
    
    public VendaAvulsaVO gerarProdutoPagamentoCredito(Double valor, ClienteVO cliente, TipoOperacaoContaCorrenteEnum tipo, UsuarioVO responsavel, String descricao, Date data, EmpresaVO empresaVO) throws Exception {
        EmpresaVO empresa = cliente.getEmpresa();
        if(empresa == null || empresa.getCodigo() == null || empresa.getCodigo() == 0){
            empresa = empresaVO;
        }
        return gerarProdutoPagamentoCredito(valor, cliente, null, tipo, responsavel, descricao, negocio.comuns.utilitarias.Calendario.hoje(), negocio.comuns.utilitarias.Calendario.hoje(), empresa);
    }
    
    public VendaAvulsaVO gerarProdutoPagamentoCredito(Double valor, ClienteVO cliente, ColaboradorVO colaboradorVO, TipoOperacaoContaCorrenteEnum tipo,
                                                      UsuarioVO responsavel, String descricao, Date data, Date dataVencimento, EmpresaVO empresaVO) throws Exception {
        VendaAvulsaVO vendaAvulsaVO = new VendaAvulsaVO();
        
        vendaAvulsaVO.setProdutoDebitoContaCorrente(true);
        vendaAvulsaVO.setTipoComprador("CI");
        if (cliente != null) {
            vendaAvulsaVO.setCliente(cliente);
        } else if (colaboradorVO != null) {
            vendaAvulsaVO.setColaborador(colaboradorVO);
        }
        vendaAvulsaVO.setDataRegistro(data);
        vendaAvulsaVO.setEmpresa(empresaVO);
        ItemVendaAvulsaVO item = new ItemVendaAvulsaVO();
        item.setDataVenda(data);
        item.setQuantidade(1);
        item.setUsuarioVO(responsavel);
        String descricaoProduto = MovimentoContaCorrenteClienteVO.DESCRICAO_DEPOSITO_CONTA_ALUNO;
        Produto produtoDAO = new Produto(con);
        item.setProduto(produtoDAO.criarOuConsultarExisteProdutoPorTipo(
                descricaoProduto, "CC", 0.0));
        produtoDAO = null;
        item.setValorParcial(valor);
        item.getProduto().setValorFinal(valor);
        // como o pagamento é referente a um único produto (débito
        // da conta corrente)
        // seto-o como o valor do produto criado acima
        vendaAvulsaVO.setValorTotal(valor);
        vendaAvulsaVO.setNomeComprador(vendaAvulsaVO.getCliente().getPessoa().getNome());
        vendaAvulsaVO.getItemVendaAvulsaVOs().add(item);
        vendaAvulsaVO.setResponsavel(item.getUsuarioVO());
        vendaAvulsaVO.setDescricaoAdicional("CC - " + descricao);
        vendaAvulsaVO.setVencimentoPrimeiraParcela(dataVencimento);

        VendaAvulsa vendaAvulsaDAO = new VendaAvulsa(con);
        vendaAvulsaDAO.incluirSemCommit(vendaAvulsaVO, false, dataVencimento);
        vendaAvulsaDAO = null;
        return vendaAvulsaVO;
    }
    
    public VendaAvulsaVO gerarProdutoPagamentoDebito(Double valor, ClienteVO cliente, TipoOperacaoContaCorrenteEnum tipo, UsuarioVO responsavel) throws Exception {
        //é setado com o valor zero, mesmo tendo recebido o debito da conta

        MovimentoContaCorrenteClienteVO atual = consultarPorCodigoPessoa(cliente.getPessoa().getCodigo(), Uteis.NIVELMONTARDADOS_TODOS);
        MovimentoContaCorrenteClienteVO movCCVO;
        VendaAvulsaVO vendaAvulsaVO = new VendaAvulsaVO();
        vendaAvulsaVO.setTipoComprador("CI");
        vendaAvulsaVO.setCliente(cliente);
        vendaAvulsaVO.setDataRegistro(Calendario.hoje());
        Empresa empresaDAO = new Empresa(con);
        vendaAvulsaVO.setEmpresa(empresaDAO.consultarPorCodigo(vendaAvulsaVO.getCliente().getEmpresa().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
        empresaDAO = null;
        ItemVendaAvulsaVO item = new ItemVendaAvulsaVO();
        item.setDataVenda(Calendario.hoje());
        item.setQuantidade(1);
        item.setUsuarioVO(responsavel);
        String tipoLancCC = "";
        String descLancCC = "";
        String descricaoProduto = "";
        String codigoPagamentos = "";
        if (atual != null && atual.getSaldoAtual() < 0 && !atual.getMovPagamentosVOs().isEmpty() && tipo != TipoOperacaoContaCorrenteEnum.TOCC_Acerto) {
            Iterator i = atual.getMovPagamentosVOs().iterator();
            while (i.hasNext()) {
                MovPagamentoVO pagamento = (MovPagamentoVO) i.next();
                if (codigoPagamentos.equals("")) {
                    codigoPagamentos += pagamento.getCodigo();
                } else {
                    codigoPagamentos += "," + pagamento.getCodigo();
                }
            }
        }
        
        
        if (tipo == TipoOperacaoContaCorrenteEnum.TOCC_Receber) {
            if (Uteis.arredondarForcando2CasasDecimais(atual.getSaldoAtual()) != Uteis.arredondarForcando2CasasDecimais(valor)) {
                throw new ConsistirException("Pagamentos vinculados ao débito foram alterados. Por favor, atualize os dados do aluno e se necessário tente fazer essa operação novamente.");                
            }
            descricaoProduto = MovimentoContaCorrenteClienteVO.DESCRICAO_PAGAMENTO_SALDO_DEVEDOR;
            Produto produtoDAO = new Produto(con);
            item.setProduto(produtoDAO.criarOuConsultarExisteProdutoPorTipo(
                    descricaoProduto,
                    "MC",
                    0.0));
            produtoDAO = null;
            valor *= (1);
            descLancCC = MovimentoContaCorrenteClienteVO.DESCRICAO_PAGAMENTO_SALDO_DEVEDOR;
            tipoLancCC = "CR";
            item.getProduto().setValorFinal(valor);
            item.setMovpagamentos(codigoPagamentos);
            vendaAvulsaVO.setNomeComprador(vendaAvulsaVO.getCliente().getPessoa().getNome());
            movCCVO = new MovimentoContaCorrenteClienteVO();
            movCCVO.setDataRegistro(Calendario.hoje());
            movCCVO.setDescricao(descLancCC);
            movCCVO.setPessoa(cliente.getPessoa());
            movCCVO.setResponsavelAutorizacao(responsavel);
            movCCVO.setTipoMovimentacao(tipoLancCC);
            movCCVO.setValor(valor);
            movCCVO.setSaldoAtual(0.0);
            movCCVO.setSaldoAnterior(Uteis.arredondarForcando2CasasDecimaisMantendoSinal(atual.getSaldoAtual()));

            MovimentoContaCorrenteCliente movDAO = new MovimentoContaCorrenteCliente(con);
            movDAO.incluirSemCommit(movCCVO, false);
            movDAO = null;
        } else if (tipo == TipoOperacaoContaCorrenteEnum.TOCC_Acerto) {
            descricaoProduto = MovimentoContaCorrenteClienteVO.DESCRICAO_ACERTO_CONTA_ALUNO;
            Produto produtoDAO = new Produto(con);
            item.setProduto(produtoDAO.criarOuConsultarExisteProdutoPorTipo(
                    descricaoProduto,
                    "AC",
                    0.0));
            produtoDAO = null;
            descLancCC = MovimentoContaCorrenteClienteVO.DESCRICAO_ACERTO_CONTA_ALUNO;
            tipoLancCC = "DR";
            item.getProduto().setValorFinal(valor);
            vendaAvulsaVO.setNomeComprador(vendaAvulsaVO.getCliente().getPessoa().getNome());
        }
        vendaAvulsaVO.getItemVendaAvulsaVOs().add(item);
        vendaAvulsaVO.setResponsavel(item.getUsuarioVO());
        vendaAvulsaVO.setDescricaoAdicional(
                descricaoProduto
                + " " + vendaAvulsaVO.getEmpresa().getMoeda()
                + " " + valor);

        VendaAvulsa vendaAvulsaDAO = new VendaAvulsa(con);
        vendaAvulsaDAO.incluirSemCommit(vendaAvulsaVO, false, Calendario.hoje());
        vendaAvulsaDAO = null;
        
        return vendaAvulsaVO;
        
    }

    /**
     * Método responsável pelo registro do LOG de alteração do objeto
     * selecionado, esse registro é feito de acordo com as annotations que foram
     * adicionadas no VO do objeto. No método é gerado uma lista com objetos do
     * tipo LogVO, onde cada objeto desta lista é incluida na tabela Log no BD
     * (Banco de Dados) com a sua operação.
     *
     * @param ObjetoVO Objeto a ser validado para geração o LOG.
     * <AUTHOR> Cantarelli dos Santos
     */
    @SuppressWarnings("unchecked")
    public static void registrarLogObjetoVO1(SuperVO ObjetoVO, Integer codigoCliente, String nomeEntidade, int codPessoa) throws Exception {
        try {
            LogInterfaceFacade logFacade = getFacade().getLog();
            List lista = ObjetoVO.gerarLogAlteracaoObjetoVO();
            Iterator i = lista.iterator();
            while (i.hasNext()) {
                LogVO log = (LogVO) i.next();
                log.setChavePrimaria(codigoCliente.toString());
                log.setNomeEntidade(nomeEntidade);
                log.setPessoa(codPessoa);
                logFacade.incluirSemCommit(log);
            }
        } catch (Exception e) {
            throw e;
        }
    }
    
    public List<MovPagamentoVO> atualizarPagamentos(List<MovPagamentoVO> creditos,
            List<MovPagamentoVO> debitos, boolean pagarDebito) throws Exception {
        List<MovPagamentoVO> movDebitos = null; // armazena os pagamentos que devem ser adicionados a conta corrente
        Iterator i;
        MovPagamentoVO debito;
        MovPagamentoVO credito;
        while (!debitos.isEmpty() && !creditos.isEmpty()) {
            debito = debitos.get(0);
//			Iterator j = creditos.iterator();
            credito = creditos.get(0);
            MovPagamentoVO novo = null;
            
            if (Uteis.arredondarForcando2CasasDecimais(credito.getValor().doubleValue()) == Uteis.arredondarForcando2CasasDecimais(debito.getValor().doubleValue())) {
                creditos.remove(credito);
                debitos.remove(debito);
                if (credito.getFormaPagamento().getTipoFormaPagamento().equals("CC") && !credito.getCredito()) {
                    credito.setDataLancamento(debito.getDataLancamento());
                    credito.setDataAlteracaoManual(debito.getDataAlteracaoManual());
                    credito.setDataPagamento(debito.getDataPagamento());
                    credito.setDataQuitacao(debito.getDataQuitacao());
                }
                alterarPagamentos(credito, null, debito);
                
            } else if (credito.getValor().doubleValue() > debito.getValor().doubleValue()) {
                
                
                credito.setValor(Uteis.arredondarForcando2CasasDecimais(debito.getValor()));
                if (credito.getFormaPagamento().getTipoFormaPagamento().equals("CH")) {
                    credito.setChequeVOs(Ordenacao.ordenarLista(credito.getChequeVOs(), "dataCompensacao"));

                    MovPagamento movPagamentoDAO = new MovPagamento(con);
                    credito = movPagamentoDAO.atualizarListaCheques(credito);
                    movPagamentoDAO = null;
                }
                if (credito.getFormaPagamento().getTipoFormaPagamento().equals("CA")) {
                    credito.setCartaoCreditoVOs(Ordenacao.ordenarLista(credito.getCartaoCreditoVOs(), "dataCompensacao"));

                    MovPagamento movPagamentoDAO = new MovPagamento(con);
                    credito = movPagamentoDAO.atualizarListaCartaoCredito(credito);
                    movPagamentoDAO = null;
                }
                novo = (MovPagamentoVO) credito.getClone(true);
                novo.setValor(Uteis.arredondarForcando2CasasDecimaisMantendoSinal(credito.getValorTotal() - credito.getValor()));
                credito.setValorTotal(credito.getValor());
                novo.setValorTotal(novo.getValor());
                if (credito.getFormaPagamento().getTipoFormaPagamento().equals("CH")) {
                    MovPagamento movPagamentoDAO = new MovPagamento(con);
                    credito = movPagamentoDAO.retiraChequesCancelados(credito);
                    novo = movPagamentoDAO.atualizarChequeMovimentoCC(novo);
                    movPagamentoDAO = null;
                }
                if (credito.getFormaPagamento().getTipoFormaPagamento().equals("CA")) {
                    MovPagamento movPagamentoDAO = new MovPagamento(con);
                    credito = movPagamentoDAO.retiraCartoesCancelados(credito);
                    novo = movPagamentoDAO.atualizarCartaoMovimentoCC(novo);
                    movPagamentoDAO = null;
                }
                novo.setNovoObj(true);
                novo.setPagamentoMovParcelaVOs(new ArrayList<PagamentoMovParcelaVO>());
                credito.setPagamentoMovParcelaVOs(new ArrayList<PagamentoMovParcelaVO>());

                MovPagamento movPagamentoDAO = new MovPagamento(con);
                movPagamentoDAO.incluirSemCommit(novo);
                movPagamentoDAO.alterarSemCommit(credito);
                movPagamentoDAO = null;

                creditos.remove(credito);
                creditos.add(novo);
                debitos.remove(debito);
                alterarPagamentos(credito, null, debito);
                
            } else if (credito.getValor() < debito.getValor()) {
                novo = (MovPagamentoVO) debito.getClone(true);
                novo.setValor(Uteis.arredondarForcando2CasasDecimaisMantendoSinal(debito.getValor() - credito.getValor()));
                novo.setValorTotal(novo.getValor());
                novo.setNovoObj(true);

                MovPagamento movPagamentoDAO = new MovPagamento(con);
                movPagamentoDAO.incluirSemCommit(novo);
                movPagamentoDAO = null;

                creditos.remove(credito);
                debitos.remove(debito);
                debitos.add(novo);
                alterarPagamentos(credito, novo, debito);
            }
            if (credito.getFormaPagamento().getTipoFormaPagamento().equals("CC") && !credito.getCredito() && pagarDebito) {
                if (movDebitos == null) {
                    movDebitos = new ArrayList<MovPagamentoVO>();
                }
                movDebitos.add(credito);
            }
        }
        if (!debitos.isEmpty()) {
            creditos.addAll(debitos);
        }
        if (pagarDebito) {
            if (movDebitos != null && !movDebitos.isEmpty()) {
                return movDebitos;
            } else {
                return null;
            }
        }
        return creditos;
    }
    
    public static void registrarLogErroObjetoVO(String nomeEntidade, int codPessoa, String msg, String responsavel, String userOamd) throws Exception {
        try {
            LogInterfaceFacade logFacade = getFacade().getLog();
            
            LogVO log = new LogVO();
            log.setNomeCampo("Erro");
            log.setChavePrimaria("");
            log.setNomeEntidade(nomeEntidade);
            log.setPessoa(codPessoa);
            log.setValorCampoAnterior(msg);
            log.setValorCampoAlterado(msg);
            log.setOperacao("ERRO AO CRIAR LOG");
            log.setResponsavelAlteracao(responsavel);
            log.setUserOAMD(userOamd);
            logFacade.incluirSemCommit(log);
            
            
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
    
    private void alterarPagamentos(MovPagamentoVO credito, MovPagamentoVO novo,
            MovPagamentoVO debito) throws Exception {
        ReciboPagamentoVO recibo = null;
        if (debito.getReciboPagamento() != null && debito.getReciboPagamento().getCodigo() > 0) {
            ReciboPagamento reciboPagamentoDAO = new ReciboPagamento(con);
            recibo = reciboPagamentoDAO.consultarPorChavePrimaria(debito.getReciboPagamento().getCodigo(), Uteis.NIVELMONTARDADOS_TODOS);
            reciboPagamentoDAO = null;
        }

        PagamentoMovParcela pagamentoMovParcelaDAO = new PagamentoMovParcela(con);
        List pagParcelasDebito = pagamentoMovParcelaDAO.consultarPagamentoMovParcelas(debito.getCodigo(), Uteis.NIVELMONTARDADOS_TODOS);
        List pagParcelasCredito = pagamentoMovParcelaDAO.consultarPagamentoMovParcelas(credito.getCodigo(), Uteis.NIVELMONTARDADOS_TODOS);
        pagamentoMovParcelaDAO = null;
        credito.setPagamentoMovParcelaVOs(new ArrayList<PagamentoMovParcelaVO>());
        
        if (novo != null && novo.getCodigo() > 0) {
            novo.setPagamentoMovParcelaVOs(new ArrayList<PagamentoMovParcelaVO>());
            Iterator i = pagParcelasDebito.iterator();
            Double restante = credito.getValor();
            while (i.hasNext()) {
                PagamentoMovParcelaVO parDebito = (PagamentoMovParcelaVO) i.next();
                if (restante > 0) {
                    restante -= parDebito.getValorPago();
                    if (restante < 0) {
                        PagamentoMovParcelaVO parNova = (PagamentoMovParcelaVO) parDebito.getClone(true);
                        parDebito.setValorPago(parDebito.getValorPago() + restante);
                        parNova.setMovPagamento(novo.getCodigo());
                        parNova.setValorPago(restante * (-1));
                        parNova.setNovoObj(true);
                        novo.setReciboPagamento(parNova.getReciboPagamento());
                        novo.getPagamentoMovParcelaVOs().add(parNova);
                        parDebito.setMovPagamento(credito.getCodigo());
                        if (credito.getReciboPagamento().getCodigo() == 0 || credito.getReciboPagamento() == null) {
                            credito.setReciboPagamento(parDebito.getReciboPagamento());
                        }
                        credito.getPagamentoMovParcelaVOs().add(parDebito);
                        
                    } else {
                        parDebito.setMovPagamento(credito.getCodigo());
                        credito.setReciboPagamento(parDebito.getReciboPagamento());
                        credito.getPagamentoMovParcelaVOs().add(parDebito);
                    }
                } else {
                    parDebito.setMovPagamento(novo.getCodigo());
                    novo.setReciboPagamento(parDebito.getReciboPagamento());
                    novo.getPagamentoMovParcelaVOs().add(parDebito);
                }
                
            }
            debito.setReciboPagamento(new ReciboPagamentoVO());
            debito.setPagamentoMovParcelaVOs(new ArrayList<PagamentoMovParcelaVO>());
            NFSeEmitida nfSeEmitida = new NFSeEmitida(con);
            NFSeEmitidaVO notaEmitida = nfSeEmitida.consultaPorPagamento(debito.getCodigo());
            if (notaEmitida != null) {
                boolean alterarValor = notaEmitida.getValor() > 0.0 ;
                nfSeEmitida.excluir(notaEmitida);
                notaEmitida.setValor(alterarValor ? novo.getValor() : 0.0);
                notaEmitida.setMovPagamento(novo);
                nfSeEmitida.incluir(notaEmitida);
                notaEmitida.setValor(alterarValor ? credito.getValor() : 0.0);
                notaEmitida.setMovPagamento(credito);
                nfSeEmitida.incluir(notaEmitida);
            }

            MovPagamento movPagamentoDAO = new MovPagamento(con);
            movPagamentoDAO.alterarSemCommit(novo);
            movPagamentoDAO.alterarSemCommit(credito);
            movPagamentoDAO.excluirSemCommit(debito);
            movPagamentoDAO = null;
            
        } else {
            
            Iterator i = pagParcelasDebito.iterator();
            while (i.hasNext()) {
                PagamentoMovParcelaVO parDebito = (PagamentoMovParcelaVO) i.next();
                parDebito.setMovPagamento(credito.getCodigo());
                if (credito.getReciboPagamento().getCodigo() == 0 || credito.getReciboPagamento() == null) {
                    credito.setReciboPagamento(parDebito.getReciboPagamento());
                }
                credito.getPagamentoMovParcelaVOs().add(parDebito);
            }
            debito.setReciboPagamento(new ReciboPagamentoVO());
            debito.setPagamentoMovParcelaVOs(new ArrayList<PagamentoMovParcelaVO>());

            MovPagamento movPagameDAO = new MovPagamento(con);
            movPagameDAO.alterarSemCommit(credito);
            movPagameDAO = null;
            
            NFSeEmitida nfSeEmitida = new NFSeEmitida(con);
            NFSeEmitidaVO notaEmitida = nfSeEmitida.consultaPorPagamento(debito.getCodigo());
            if (notaEmitida != null) {
                nfSeEmitida.atualizarMovPagamento(debito.getCodigo(), credito.getCodigo());
            }
            nfSeEmitida = null;

            MovPagamento movPagamentoDAO = new MovPagamento(con);
            movPagamentoDAO.excluirSemCommit(debito);
            movPagamentoDAO = null;
            
        }

        MovPagamento movPagamentoDAO = new MovPagamento(con);
        movPagamentoDAO.setarProdutosPagos(credito.getReciboPagamento().getCodigo());
        movPagamentoDAO = null;
        
    }
    
    public Integer obterValorChavePrimariaCodigo() throws Exception {
        return Conexao.obterUltimoCodigoGeradoTabela(con, this.getClass().getSimpleName());
    }
    
    @Override
    public void pagarProdutosDebito(MovimentoContaCorrenteClienteVO credito, boolean b) throws NumberFormatException, Exception {
        String[] pagamentosDebito = credito.getPagamentosDebito().split(",");
        List debitos = new ArrayList<MovPagamentoVO>();
        MovimentoContaCorrenteClienteVO atual;
        for (int i = 0; i < pagamentosDebito.length; i++) {
            MovPagamento movPagamentoDAO = new MovPagamento(con);
            MovPagamentoVO debito = movPagamentoDAO.consultarPorChavePrimaria(Integer.parseInt(pagamentosDebito[i]), Uteis.NIVELMONTARDADOS_TODOS);
            movPagamentoDAO = null;
            debitos.add(debito);
        }
        List movDebitos = atualizarPagamentos(credito.getMovPagamentosVOs(), debitos, true);
        if (movDebitos != null && !movDebitos.isEmpty()) {
            credito.setValor(0.0);
            credito.setDescricao("PAGAR PARCELAS");
            credito.setTipoMovimentacao("DE");
            Iterator i = movDebitos.iterator();
            
            while (i.hasNext()) {
                MovPagamentoVO mov = (MovPagamentoVO) i.next();
                credito.setValor(mov.getValor() + credito.getValor());
            }
            credito.setMovPagamentosVOs(movDebitos);
            adicionarmovimento(credito);
        }
    }
    
    @Override
    public void estornarCreditosMovpagamento(Integer movPagCodigo, UsuarioVO responsavel, Integer recibo)
            throws Exception {
        FormaPagamentoVO contaCorrente = getFacade().getFormaPagamento().consultarPorTipoFormaPagamentoAtiva("CC", false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        List creditos = getFacade().getMovPagamento().consultarCreditosDependentes(movPagCodigo, Uteis.NIVELMONTARDADOS_TODOS);
        Iterator i = creditos.iterator();
        List debitos = new ArrayList<MovPagamentoVO>();
        Double valorDebito = 0.0;
        PessoaVO pessoa = new PessoaVO();
        while (i.hasNext()) {
            MovPagamentoVO pagamento = (MovPagamentoVO) i.next();
            if (pagamento.getFormaPagamento().getTipoFormaPagamento().equals("CC") && !pagamento.getCredito()) { //pagamento já está na conta corrente e não pode ser adicionado novamente
                pagamento.setMovPagamentoOrigemCredito(0);
                getFacade().getMovPagamento().alterarSemCommit(pagamento);
            } else if (pagamento.getReciboPagamento().getCodigo() != 0) { // pagamento está associado a um recibo
                if (pagamento.getReciboPagamento().getCodigo().equals(recibo)) {
                    getFacade().getMovPagamento().excluirSemCommit(pagamento);
                } else {
                    ReciboPagamentoVO reciboPagamento = getFacade().getReciboPagamento().consultarPorChavePrimaria(pagamento.getReciboPagamento().getCodigo(), Uteis.NIVELMONTARDADOS_TELACONSULTAESPECIAL);
                    pagamento.setMovPagamentoOrigemCredito(0);
                    if (pagamento.getFormaPagamento().getTipoFormaPagamento().equals("CH")) {
                        getFacade().getCheque().excluirPagamentoCheques(pagamento.getCodigo());
                    } else if (pagamento.getFormaPagamento().getTipoFormaPagamento().equals("CA")) {
                        getFacade().getCartaoCredito().excluirCartaoCreditos(pagamento.getCodigo());
                        pagamento.getOperadoraCartaoVO().setCodigo(0);
                        pagamento.setNrParcelaCartaoCredito(0);
                    }
                    pagamento.setFormaPagamento(contaCorrente);
                    pagamento.setCredito(false);
                    pagamento.setDataQuitacao(reciboPagamento.getData());
                    pagamento.setDataLancamento(reciboPagamento.getData());
                    pagamento.setDataPagamento(reciboPagamento.getData());
                    pagamento.setMovconta(null);
                    getFacade().getMovPagamento().alterarSemCommit(pagamento);
                    getFacade().getMovPagamento().ajustarMovContaPagamentoCC(pagamento);
                    debitos.add(pagamento);
                    valorDebito += Uteis.arredondarForcando2CasasDecimais(pagamento.getValor());
                    pessoa = pagamento.getPessoa();
                    reciboPagamento = null;
                }
            } else { // pagamento ainda está na contacorrente
                MovimentoContaCorrenteClienteComposicaoVO composicao = getFacade().getMovimentoContaCorrenteClienteComposicao().consultarPorCodigoMovPagamento(pagamento.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                if(composicao!=null) {
                    MovimentoContaCorrenteClienteVO atual = consultarPorChavePrimaria(composicao.getMovimento(), Uteis.NIVELMONTARDADOS_TODOS);
                    getFacade().getMovimentoContaCorrenteClienteComposicao().excluirComposicaoMovimento(atual.getCodigo());
                    List novosPagamentos = new ArrayList<MovPagamentoVO>();
                    for (MovPagamentoVO pagMovimento : atual.getMovPagamentosVOs()) {
                        if (pagMovimento.getCodigo().intValue() != pagamento.getCodigo().intValue()) {
                            novosPagamentos.add(pagMovimento);
                        }
                    }
                    atual.setMovPagamentosVOs(novosPagamentos);
                    atual.setValor(pagamento.getValor());
                    atual.setSaldoAnterior(atual.getSaldoAtual());
                    atual.setSaldoAtual(Uteis.arredondarForcando2CasasDecimaisMantendoSinal(atual.getSaldoAtual() - atual.getValor()));
                    atual.setTipoMovimentacao("DE");
                    atual.setDescricao("ESTORNO DO RECIBO: " + recibo);
                    getFacade().getMovPagamento().excluirSemCommit(pagamento);
                    atual.setResponsavelAutorizacao(responsavel);
                    incluirSemCommit(atual);
                }
            }
            
        }
        if (!debitos.isEmpty()) {
            MovimentoContaCorrenteClienteVO debito = new MovimentoContaCorrenteClienteVO();
            debito.setValor(valorDebito);
            debito.setDescricao("ESTORNO DO RECIBO: " + recibo);
            debito.setTipoMovimentacao("DE");
            debito.setMovPagamentosVOs(debitos);
            debito.setPessoa(pessoa);
            debito.setResponsavelAutorizacao(responsavel);
            adicionarmovimento(debito);
        }
        
    }
    
    public void incluirTransferenciaCredito(MovimentoContaCorrenteClienteVO depositante, MovimentoContaCorrenteClienteVO beneficiado, MovimentoContaCorrenteClienteVO atual) throws Exception {
        try {
            incluir(getIdEntidade());
            con.setAutoCommit(false);
            if (Uteis.arredondarForcando2CasasDecimais(atual.getSaldoAtual()) == atual.getValorTransferencia()) {
                beneficiado.setMovPagamentosVOs(atual.getMovPagamentosVOs());
            } else {
                distribuirPagamentos(depositante, beneficiado, atual);
            }
            incluirSemCommit(depositante);
            adicionarmovimento(beneficiado);
            con.commit();
        } catch (Exception e) {
            con.rollback();
            con.setAutoCommit(true);
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }
    
    public void incluirParcelaParcialDebito(MovimentoContaCorrenteClienteVO movCCC, MovimentoContaCorrenteClienteVO atual) throws Exception {
        try {
            incluir(getIdEntidade());
            con.setAutoCommit(false);
            Iterator<MovPagamentoVO> i = atual.getMovPagamentosVOs().iterator();
            MovPagamentoVO existente = null;
            while (i.hasNext()) {
                MovPagamentoVO pagamento = i.next();
                try {
                    existente = getFacade().getMovPagamento().consultarPorChavePrimaria(pagamento.getCodigo(), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
                    if (existente.getCodigo().equals(0) || !existente.getValor().equals(pagamento.getValor())) {
                        throw new ConsistirException("");
                    }
                } catch (Exception e) {
                    throw new ConsistirException("Pagamentos vinculados ao débito foram alterados. Por favor, atualize os dados do aluno e se necessário tente fazer essa operação novamente.");
                }
                
            }
            if (Uteis.arredondarForcando2CasasDecimais(atual.getSaldoAtual() * (-1)) > atual.getValorParcelaDebito() && !atual.getMovPagamentosVOs().isEmpty()) {
                distribuirPagamentosParcelaDebito(movCCC, atual);
            }
            incluirSemCommit(movCCC);
            gerarParcelaDebitoParcial(atual);
            con.commit();
        } catch (Exception e) {
            con.rollback();
            con.setAutoCommit(true);
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }
    
    private void gerarParcelaDebitoParcial(MovimentoContaCorrenteClienteVO atual) throws Exception {
        // TODO Auto-generated method stub
        VendaAvulsaVO vendaAvulsaVO = new VendaAvulsaVO();
        vendaAvulsaVO.setTipoComprador("CI");
        vendaAvulsaVO.setCliente(getFacade().getCliente().consultarPorCodigoPessoa(atual.getPessoa().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
        vendaAvulsaVO.setDataRegistro(negocio.comuns.utilitarias.Calendario.hoje());
        vendaAvulsaVO.setEmpresa(getFacade().getEmpresa().consultarPorCodigo(vendaAvulsaVO.getCliente().getEmpresa().getCodigo(),Uteis.NIVELMONTARDADOS_DADOSBASICOS));
        ItemVendaAvulsaVO item = new ItemVendaAvulsaVO();
        item.setDataVenda(negocio.comuns.utilitarias.Calendario.hoje());
        item.setQuantidade(1);
        item.setUsuarioVO(atual.getResponsavelAutorizacao());
        String tipoLancCC = "";
        String descLancCC = "";
        String descricaoProduto = "";
        String codigoPagamentos = "";
        if (atual != null && !atual.getMovPagamentosVOs().isEmpty()) {
            Iterator i = atual.getMovPagamentosVOs().iterator();
            while (i.hasNext()) {
                MovPagamentoVO pagamento = (MovPagamentoVO) i.next();
                if (codigoPagamentos.equals("")) {
                    codigoPagamentos += pagamento.getCodigo();
                } else {
                    codigoPagamentos += "," + pagamento.getCodigo();
                }
            }
        }

        descricaoProduto = MovimentoContaCorrenteClienteVO.DESCRICAO_PAGAMENTO_SALDO_DEVEDOR;
        Produto produtoDAO = new Produto(con);
        item.setProduto(produtoDAO.criarOuConsultarExisteProdutoPorTipo(
                descricaoProduto,
                "MC",
                0.0));
        produtoDAO = null;
        descLancCC = MovimentoContaCorrenteClienteVO.DESCRICAO_PAGAMENTO_SALDO_DEVEDOR;
        tipoLancCC = "CR";
        item.getProduto().setValorFinal(atual.getValorParcelaDebito());
        item.setMovpagamentos(codigoPagamentos);
        vendaAvulsaVO.setNomeComprador(vendaAvulsaVO.getCliente().getPessoa().getNome());
        
        vendaAvulsaVO.getItemVendaAvulsaVOs().add(item);
        vendaAvulsaVO.setResponsavel(item.getUsuarioVO());
        vendaAvulsaVO.setDescricaoAdicional(
                descricaoProduto
                + " - " + vendaAvulsaVO.getEmpresa().getMoeda()
                + " " + atual.getValorParcelaDebito());
        
        getFacade().getVendaAvulsa().incluirSemCommit(vendaAvulsaVO, false, Calendario.hoje());
        
    }
    
    private void distribuirPagamentosParcelaDebito(
            MovimentoContaCorrenteClienteVO movCCC,
            MovimentoContaCorrenteClienteVO atual) throws Exception {
        // TODO Auto-generated method stub
        Iterator<MovPagamentoVO> i = atual.getMovPagamentosVOs().iterator();
        Double valor = atual.getValorParcelaDebito();
        List<MovPagamentoVO> pagamentosParcela = new ArrayList<MovPagamentoVO>();
        while (i.hasNext()) {
            MovPagamentoVO pagamento = i.next();
            if (Uteis.arredondarForcando2CasasDecimais(pagamento.getValor()) <= valor && valor > 0.0) {
                valor = Uteis.arredondarForcando2CasasDecimais(valor - pagamento.getValor());
                pagamentosParcela.add(pagamento);
            } else if (valor > 0.0) {
                pagamento.setValor(Uteis.arredondarForcando2CasasDecimais(pagamento.getValor() - valor));
                MovPagamentoVO novo = (MovPagamentoVO) pagamento.getClone(true);
                novo.setValor(Uteis.arredondarForcando2CasasDecimaisMantendoSinal(pagamento.getValorTotal() - pagamento.getValor()));
                pagamento.setValorTotal(pagamento.getValor());
                novo.setValorTotal(novo.getValor());
                novo.setPagamentoMovParcelaVOs(new ArrayList<PagamentoMovParcelaVO>());
                
                List pagParcelasDebito = Ordenacao.ordenarLista(pagamento.getPagamentoMovParcelaVOs(), "movParcela");
                pagamento.setPagamentoMovParcelaVOs(new ArrayList<PagamentoMovParcelaVO>());
                
                Iterator p = pagParcelasDebito.iterator();
                Double restante = valor;
                while (p.hasNext()) {
                    PagamentoMovParcelaVO parDebito = (PagamentoMovParcelaVO) p.next();
                    if (restante > 0) {
                        restante = Uteis.arredondarForcando2CasasDecimaisMantendoSinal(restante - parDebito.getValorPago());
                        if (restante < 0) {
                            PagamentoMovParcelaVO parNova = (PagamentoMovParcelaVO) parDebito.getClone(true);
                            parNova.setValorPago(Uteis.arredondarForcando2CasasDecimais(parDebito.getValorPago() + restante));
                            parDebito.setValorPago(restante * (-1));
                            parNova.setNovoObj(true);
                            novo.getPagamentoMovParcelaVOs().add(parNova);
                            pagamento.getPagamentoMovParcelaVOs().add(parDebito);
                            
                        } else {
                            novo.getPagamentoMovParcelaVOs().add(parDebito);
                        }
                    } else {
                        pagamento.getPagamentoMovParcelaVOs().add(parDebito);
                    }
                    
                }
                getFacade().getMovPagamento().incluirSemCommit(novo);
                getFacade().getMovPagamento().alterarSemCommit(pagamento);
                
                movCCC.getMovPagamentosVOs().add(pagamento);
                pagamentosParcela.add(novo);
                valor = 0.0;
                
            } else {
                movCCC.getMovPagamentosVOs().add(pagamento);
            }
            
        }
        atual.setMovPagamentosVOs(new ArrayList<MovPagamentoVO>());
        atual.getMovPagamentosVOs().addAll(pagamentosParcela);
    }
    
    public void distribuirPagamentos(
            MovimentoContaCorrenteClienteVO depositante,
            MovimentoContaCorrenteClienteVO beneficiado, MovimentoContaCorrenteClienteVO atual) throws Exception {
        Iterator i = atual.getMovPagamentosVOs().iterator();
        Double valor = atual.getValorTransferencia();
        while (i.hasNext()) {
            MovPagamentoVO pagamento = (MovPagamentoVO) i.next();
            if (Uteis.arredondarForcando2CasasDecimais(pagamento.getValor()) <= valor && valor > 0.0) {
                valor = Uteis.arredondarForcando2CasasDecimais(valor - pagamento.getValor());
                beneficiado.getMovPagamentosVOs().add(pagamento);
            } else if (valor > 0.0) {
                pagamento.setValor(Uteis.arredondarForcando2CasasDecimais(pagamento.getValor() - valor));
                if (pagamento.getFormaPagamento().getTipoFormaPagamento().equals("CH")) {
                    pagamento.setChequeVOs(Ordenacao.ordenarLista(pagamento.getChequeVOs(), "dataCompensacao"));
                    pagamento = getFacade().getMovPagamento().atualizarListaCheques(pagamento);
                }
                if (pagamento.getFormaPagamento().getTipoFormaPagamento().equals("CA")) {
                    pagamento.setCartaoCreditoVOs(Ordenacao.ordenarLista(pagamento.getCartaoCreditoVOs(), "dataCompensacao"));
                    pagamento = getFacade().getMovPagamento().atualizarListaCartaoCredito(pagamento);
                }
                MovPagamentoVO novo = (MovPagamentoVO) pagamento.getClone(true);
                novo.setValor(Uteis.arredondarForcando2CasasDecimaisMantendoSinal(pagamento.getValorTotal() - pagamento.getValor()));
                pagamento.setValorTotal(pagamento.getValor());
                novo.setValorTotal(novo.getValor());
                if (pagamento.getFormaPagamento().getTipoFormaPagamento().equals("CH")) {
                    pagamento = getFacade().getMovPagamento().retiraChequesCancelados(pagamento);
                    novo = getFacade().getMovPagamento().atualizarChequeMovimentoCC(novo);
                }
                if (pagamento.getFormaPagamento().getTipoFormaPagamento().equals("CA")) {
                    pagamento = getFacade().getMovPagamento().retiraCartoesCancelados(pagamento);
                    novo = getFacade().getMovPagamento().atualizarCartaoMovimentoCC(novo);
                }
                
                getFacade().getMovPagamento().incluirSemCommit(novo);
                getFacade().getMovPagamento().alterarSemCommit(pagamento);
                depositante.getMovPagamentosVOs().add(pagamento);
                beneficiado.getMovPagamentosVOs().add(novo);
                valor = 0.0;
                
            } else {
                depositante.getMovPagamentosVOs().add(pagamento);
            }
            
        }
    }
    
    public void gerarParcelaCredito(MovimentoContaCorrenteClienteVO movcco) throws Exception {
        incluir(getIdEntidade());
        ClienteVO clienteVO = getFacade().getCliente().consultarPorCodigoPessoa(movcco.getPessoa().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        VendaAvulsaVO vendaAvulsaVO = getFacade().getMovimentoContaCorrenteCliente().gerarProdutoPagamentoCredito(movcco.getValor(), clienteVO, null, movcco.getResponsavelAutorizacao(), movcco.getDescricao());
        
        
        List<MovParcelaVO> listaMovParcelaAcertoCC = new ArrayList<MovParcelaVO>();
        MovParcelaVO movParcela = new MovParcelaVO();
        movParcela.setSituacao("EA");
        movParcela.setMovimentoCC(true);
        listaMovParcelaAcertoCC.add(movParcela);

        // Setando o controlador de Venda Avulsa no contexto da
        // aplicação pra ser usado na tela de pagamento
        JSFUtilities.setManagedBeanValue(
                "VendaAvulsaControle.vendaAvulsaVO", vendaAvulsaVO);

        // }
        // LOG - INICIO

        try {
            vendaAvulsaVO.setObjetoVOAntesAlteracao(new VendaAvulsaVO());
            vendaAvulsaVO.setNovoObj(true);
            registrarLogObjetoVO1(vendaAvulsaVO, vendaAvulsaVO.getCodigo().intValue(), "VENDAAVULSA",
                    vendaAvulsaVO.getCliente().getPessoa().getCodigo());
            
        } catch (Exception e) {
            registrarLogErroObjetoVO("VENDAAVULSA", vendaAvulsaVO.getCliente().getPessoa().getCodigo(),
                    "ERRO AO GERAR LOG DE VENDA AVULSA", movcco.getResponsavelAutorizacao().getNome(), movcco.getResponsavelAutorizacao().getUserOamd());
            e.printStackTrace();
            
        }
        
        
    }

    public String consultarJSON(String codPessoa, String moeda) throws Exception {
        JSONObject aaData = new JSONObject();
        try (ResultSet rs = getPS(codPessoa).executeQuery()) {
            JSONArray lista = new JSONArray();
            while (rs.next()){
                Date dataRegistro = rs.getTimestamp("dataregistro");
                JSONArray itemLista = new JSONArray();
                itemLista.put(rs.getString("codigo"));
                itemLista.put(rs.getString("cliente"));
                itemLista.put(rs.getString("descricao"));
                itemLista.put(rs.getString("responsavel"));
                itemLista.put(Uteis.getDataAplicandoFormatacao(dataRegistro, "dd/MM/yyyy HH:mm:ss"));
                itemLista.put(rs.getString("tipomovimentacao"));
                itemLista.put(moeda+" " + Formatador.formatarValorMonetarioSemMoeda(rs.getDouble("valor")));
                itemLista.put((rs.getDouble("saldoatual") < 0.00 ? "-" : "") +moeda+" " + Formatador.formatarValorMonetarioSemMoeda(rs.getDouble("saldoatual")));
                lista.put(itemLista);
            }
            aaData.put("aaData", lista);
        }
        return  aaData.toString();
    }
    
    private PreparedStatement getPS(String codPessoa) throws SQLException {
        StringBuilder sql = new StringBuilder("SELECT mccc.codigo, " + "replace(mccc.descricao,'\t', '') as descricao,  " + "  pe.nome AS cliente,\n" + "  mccc.dataregistro, \n" + "  usr.username AS responsavel,\n" + "  mccc.tipomovimentacao,\n" + "  mccc.valor,\n" + "  mccc.saldoatual\n" + "FROM movimentocontacorrentecliente mccc\n" + "  LEFT JOIN pessoa pe ON mccc.pessoa = pe.codigo\n" + "  LEFT JOIN usuario usr ON mccc.responsavelautorizacao = usr.codigo\n");
        if (codPessoa != null && !codPessoa.equals("0") && !UteisValidacao.emptyString(codPessoa)) {
            sql.append("WHERE pe.codigo = ").append(codPessoa).append("\n");
        }
        sql.append("ORDER BY mccc.codigo DESC");
        return con.prepareStatement(sql.toString());
    }
    
    @Override
    public List consultarParaImpressao(String filtro, String ordem, String campoOrdenacao, int i, String codPessoa) throws SQLException {
        List lista;
        try (ResultSet rs = getPS(codPessoa).executeQuery()) {
            lista = new ArrayList();
            while (rs.next()) {
                MovimentoContaCorrenteClienteVO movContaCliente = new MovimentoContaCorrenteClienteVO();
                String geral = rs.getString("codigo") + rs.getString("descricao") + rs.getString("cliente") + rs.getString("dataregistro") + rs.getString("responsavel") + rs.getString("tipomovimentacao") + rs.getString("valor") + rs.getString("saldoatual");
                if (geral.toLowerCase().contains(filtro.toLowerCase())) {
                    movContaCliente.setCodigo(rs.getInt("codigo"));
                    movContaCliente.setDescricao(rs.getString("descricao"));
                    movContaCliente.getPessoa().setNome(rs.getString("cliente"));
                    Date dataRegistro = rs.getTimestamp("dataregistro");
                    movContaCliente.setDataHoraRegistro(Uteis.getDataAplicandoFormatacao(dataRegistro, "dd/MM/yyyy HH:mm:ss"));
                    movContaCliente.setUsuarioVO(new UsuarioVO());
                    movContaCliente.getUsuarioVO().setUsername(rs.getString("responsavel"));
                    movContaCliente.setTipoMovimentacao(rs.getString("tipomovimentacao"));
                    movContaCliente.setValor(rs.getDouble("valor"));
                    movContaCliente.setSaldoAtual(rs.getDouble("saldoatual"));
                    lista.add(movContaCliente);
                }
            }
        }
        if (campoOrdenacao.equals("Cód")) {
            Ordenacao.ordenarLista(lista, "codigo");
        } else if (campoOrdenacao.equals("Cliente")) {
            Ordenacao.ordenarLista(lista, "pessoa_Apresentar");
        } else if (campoOrdenacao.equals("Descrição")) {
            Ordenacao.ordenarLista(lista, "descricao");
        } else if (campoOrdenacao.equals("Responsável")) {
            Ordenacao.ordenarLista(lista, "responsavel_Apresentar");
        } else if (campoOrdenacao.equals("Data")) {
            Ordenacao.ordenarLista(lista, "dataRegistro");
        } else if (campoOrdenacao.equals("Tipo")) {
            Ordenacao.ordenarLista(lista, "tipoMovimentacao");
        } else if (campoOrdenacao.equals("Valor")) {
            Ordenacao.ordenarLista(lista, "valor_Apresentar");
        } else if (campoOrdenacao.equals("Saldo Atual")) {
            Ordenacao.ordenarLista(lista, "saldoAtual_Apresentar");
        }
        if (ordem.contains("desc")) {
            Collections.reverse(lista);
        }
        return lista;
        
    }
    
    @Override
    public ReciboDevolucaoVO ajustarSaldoContaCorrenteCliente(ClienteVO cliente, MovimentoContaCorrenteClienteVO ultimoMovimento,
                                                              Double valorDevolver, UsuarioVO usuario, Date dataRegistro, boolean devolverRestanteDinheiro,
                                                              Double novoValorConta, boolean lancarSaidaFinanceiro) throws Exception {
        try {
            con.setAutoCommit(false);
            
            MovimentoContaCorrenteClienteVO movimentacao = new MovimentoContaCorrenteClienteVO();
            movimentacao.setPessoa(ultimoMovimento.getPessoa());
            movimentacao.setSaldoAtual(devolverRestanteDinheiro ? 0.0 : (ultimoMovimento.getSaldoAtual() - valorDevolver));
            movimentacao.setResponsavelAutorizacao(usuario);
            movimentacao.setDataRegistro(dataRegistro);
            movimentacao.setDescricao("AJUSTE DE SALDO");
            if (ultimoMovimento.getSaldoAtual() > 0.0) {
                //devolver
                movimentacao.setValor(devolverRestanteDinheiro ? ultimoMovimento.getSaldoAtual() : valorDevolver);
                movimentacao.setTipoMovimentacao("DE");
                
                List<ChequeVO> chequesDevolvidos = new ArrayList<ChequeVO>();
                List<CartaoCreditoVO> cartoesDevolvidos = new ArrayList<CartaoCreditoVO>();

                ZillyonWebFacade zwFacade = new ZillyonWebFacade(con);
                ReciboDevolucao reciboDevDao = new ReciboDevolucao(con);
                List<Integer> movPagamentosRec = new ArrayList<Integer>();
                Double valorDevolucaoDinheiro = 0.0;
                for (MovPagamentoVO pagamento : ultimoMovimento.getMovPagamentosVOs()) {
                    boolean adicionarRecibo = false;
                    MovPagamentoVO movPagCancelado = (MovPagamentoVO) pagamento.getClone(pagamento);
                    movPagCancelado.setCodigo(0);
                    movPagCancelado.setValorTotal(0.0);
                    movPagCancelado.setValor(0.0);
                    if (pagamento.getFormaPagamento().getTipoFormaPagamento().equals("CA")) {
                        movPagCancelado.setCartaoCreditoVOs(new ArrayList<CartaoCreditoVO>());
                        for (CartaoCreditoVO cartao : pagamento.getCartaoCreditoVOs()) {
                            if (cartao.isCartaoEscolhido()) {
                                cartao.setSituacao("CA");
                                CartaoCreditoVO cartaoClone = (CartaoCreditoVO) cartao.getClone(true);
                                cartaoClone.setComposicao(null);
                                movPagCancelado.getCartaoCreditoVOs().add(cartaoClone);
                                movPagCancelado.setValorTotal(Uteis.arredondarForcando2CasasDecimais(cartaoClone.getValor() + movPagCancelado.getValorTotal()));
                                adicionarRecibo = true;
                                NFSeEmitidaVO nfSeEmitidaVO = getFacade().getNFSeEmitida().consultarPorCartaoCredito(cartao.getCodigo());
                                if (UtilReflection.objetoMaiorQueZero(nfSeEmitidaVO, "getCodigo()")) {
                                    cartaoClone.setNfSeEmitidaCartaoExcluido(nfSeEmitidaVO);
                                }
                                pagamento.getListaCartaoExcluirVinculoComNFSe().add(cartao);
                            }
                        }
                        if (movPagCancelado.getValorTotal() > 0.0) {
                            if (movPagCancelado.getValorTotal() == pagamento.getValor()) {
                                getFacade().getMovPagamento().alterarSemCommit(pagamento);
                                cartoesDevolvidos.addAll(pagamento.getCartaoCreditoVOs());
                            } else {
                                pagamento.setValor(Uteis.arredondarForcando2CasasDecimais(pagamento.getValor() - movPagCancelado.getValorTotal()));
                                pagamento.setValorTotal(pagamento.getValor());
                                pagamento = getFacade().getMovPagamento().retiraCartoesCancelados((MovPagamentoVO) pagamento.getClone(pagamento));
                                getFacade().getMovPagamento().alterarSemCommit(pagamento);
                                movimentacao.getMovPagamentosVOs().add(pagamento);
                                getFacade().getMovPagamento().incluirSemCommit(movPagCancelado);
                                cartoesDevolvidos.addAll(movPagCancelado.getCartaoCreditoVOs());
                            }
                        } else {
                            movimentacao.getMovPagamentosVOs().add(pagamento);
                        }
                    } else if (pagamento.getFormaPagamento().getTipoFormaPagamento().equals("CH")) {
                        
                        movPagCancelado.setChequeVOs(new ArrayList<ChequeVO>());
                        for (ChequeVO cheque : pagamento.getChequeVOs()) {
                            if (cheque.getChequeEscolhido()) {
                                cheque.setSituacao("CA");
                                ChequeVO chequeClone = (ChequeVO) cheque.getClone(true);
                                chequeClone.setComposicao(null);
                                movPagCancelado.getChequeVOs().add(chequeClone);
                                movPagCancelado.setValorTotal(Uteis.arredondarForcando2CasasDecimais(chequeClone.getValor() + movPagCancelado.getValorTotal()));
                                adicionarRecibo = true;
                                NFSeEmitidaVO nfSeEmitidaVO = getFacade().getNFSeEmitida().consultaPorCheque(cheque.getCodigo());
                                if (UtilReflection.objetoMaiorQueZero(nfSeEmitidaVO, "getCodigo()")) {
                                    chequeClone.setNfSeEmitidaChequeExcluido(nfSeEmitidaVO);
                                }
                                pagamento.getListaChequeExcluirVinculoComNFSe().add(cheque);
                            }
                            
                        }
                        if (movPagCancelado.getValorTotal() > 0.0) {
                            if (Uteis.arredondarForcando2CasasDecimais(movPagCancelado.getValorTotal()) == Uteis.arredondarForcando2CasasDecimais(pagamento.getValor())) {
                                getFacade().getMovPagamento().alterarSemCommit(pagamento);
                                chequesDevolvidos.addAll(pagamento.getChequeVOs());
                            } else {
                                pagamento.setValor(Uteis.arredondarForcando2CasasDecimais(pagamento.getValor() - movPagCancelado.getValorTotal()));
                                pagamento.setValorTotal(pagamento.getValor());
                                pagamento = getFacade().getMovPagamento().retiraChequesCancelados((MovPagamentoVO) pagamento.getClone(pagamento));
                                getFacade().getMovPagamento().alterarSemCommit(pagamento);
                                movimentacao.getMovPagamentosVOs().add(pagamento);
                                getFacade().getMovPagamento().incluirSemCommit(movPagCancelado);
                                chequesDevolvidos.addAll(movPagCancelado.getChequeVOs());
                            }
                        } else {
                            movimentacao.getMovPagamentosVOs().add(pagamento);
                        }
                    } else if (pagamento.getMovPagamentoEscolhidaFinan()) {
                        valorDevolucaoDinheiro += pagamento.getValorReceberOuDevolverContaCorrente();
                        if ((Uteis.arredondarForcando2CasasDecimais(pagamento.getValor()) != Uteis.arredondarForcando2CasasDecimais(pagamento.getValorReceberOuDevolverContaCorrente())) && !devolverRestanteDinheiro) {
                            movPagCancelado.setValorTotal(pagamento.getValorReceberOuDevolverContaCorrente());
                            pagamento.setValor(Uteis.arredondarForcando2CasasDecimais(pagamento.getValor() - pagamento.getValorReceberOuDevolverContaCorrente()));
                            pagamento.setValorTotal(pagamento.getValor());
                            getFacade().getMovPagamento().alterarSemCommit(pagamento);
                            getFacade().getMovPagamento().incluirSemCommit(movPagCancelado);
                            movimentacao.getMovPagamentosVOs().add(pagamento);
                        }
                        adicionarRecibo = true;
                    } else if (!pagamento.getMovPagamentoEscolhidaFinan() && !devolverRestanteDinheiro) {
                        movimentacao.getMovPagamentosVOs().add(pagamento);
                    }
                    
                    if (adicionarRecibo || devolverRestanteDinheiro) {
                        if (UteisValidacao.emptyNumber(movPagCancelado.getCodigo())) {
                            movPagamentosRec.add(pagamento.getCodigo());
                            if (!UteisValidacao.emptyNumber(pagamento.getMovPagamentoOrigemCredito())) {
                                movPagamentosRec.add(pagamento.getMovPagamentoOrigemCredito());
                            }
                        } else {
                            movPagamentosRec.add(movPagCancelado.getCodigo());
                            if (!UteisValidacao.emptyNumber(movPagCancelado.getMovPagamentoOrigemCredito())) {
                                movPagamentosRec.add(movPagCancelado.getMovPagamentoOrigemCredito());
                            }
                            if (devolverRestanteDinheiro) {
                                movPagamentosRec.add(pagamento.getCodigo());
                            }
                        }
                    }
                }
                if (devolverRestanteDinheiro) {
                    valorDevolucaoDinheiro += (ultimoMovimento.getSaldoAtual() - valorDevolver);
                }
//                ultimoMovimento.setValorParcelaDebito(valorDevolver)
//                gerarDebito(movimentacao);
                incluirSemCommit(movimentacao);
//                adicionarmovimento(movimentacao);
                ReciboDevolucaoVO reciboDevolucao = new ReciboDevolucaoVO();
                reciboDevolucao.montarReciboDevolucaoCC(chequesDevolvidos, cartoesDevolvidos, valorDevolucaoDinheiro, cliente, usuario, ultimoMovimento.getSaldoAtual(), movPagamentosRec);
                if (valorDevolucaoDinheiro > 0.0) {
                    reciboDevolucao.setProdDevolucao(zwFacade.gerarMovProdutoDevolucaoCredito(cliente, usuario, valorDevolucaoDinheiro, lancarSaidaFinanceiro).getCodigo());
                }
                if (reciboDevolucao.getValorRecebiveis() > 0.0) {
                    reciboDevolucao.setProdRecebiveis(zwFacade.gerarMovProdutoDevolucaoRecebiveis(cliente.getEmpresa(), cliente.getPessoa(), usuario, reciboDevolucao).getCodigo());
                }
                reciboDevDao.incluirSemCommit(reciboDevolucao, con);
                
                
                con.commit();
                return reciboDevolucao;
            } else {
                //cancelar parcelas pagas pelo débito
                movimentacao.setValor(Uteis.arredondarForcando2CasasDecimais((-1 * (ultimoMovimento.getSaldoAtual())) - novoValorConta));
                movimentacao.setTipoMovimentacao("CR");
                List<MovPagamentoVO> movPagamentosCancelar = new ArrayList<MovPagamentoVO>();
                Map<Integer, String> produtoPagosPagamento = new HashMap<Integer, String>();
                List<Integer> recibos = new ArrayList<Integer>();
                Ordenacao.ordenarLista(ultimoMovimento.getMovPagamentosVOs(), "dataLancamento");
                if (novoValorConta != 0.00) {
                    valorDevolver = movimentacao.getValor();
                    for (MovPagamentoVO pagamentoVO : ultimoMovimento.getMovPagamentosVOs()) {
                        MovPagamentoVO aux = (MovPagamentoVO) pagamentoVO.getClone(true);
                        aux.setProdutosPagos("");
                        aux.setProdutosPagos("");
                        if (!recibos.contains(pagamentoVO.getReciboPagamento().getCodigo())) {
                            recibos.add(pagamentoVO.getReciboPagamento().getCodigo());
                        }
                        aux.setReciboPagamento(new ReciboPagamentoVO());
                        aux.setPagamentoMovParcelaVOs(new ArrayList<Object>());
                        if (Uteis.arredondarForcando2CasasDecimais(pagamentoVO.getValor()) < valorDevolver) {
                            getFacade().getMovPagamento().incluirSemCommit(aux);
                            movPagamentosCancelar.add(aux);
                            valorDevolver -= Uteis.arredondarForcando2CasasDecimais(valorDevolver - aux.getValor());
                            produtoPagosPagamento.put(aux.getCodigo(), pagamentoVO.getProdutosPagos());
                        } else {
                            aux.setValor(valorDevolver);
                            aux.setValorTotal(valorDevolver);
                            getFacade().getMovPagamento().incluirSemCommit(aux);
                            movPagamentosCancelar.add(aux);
                            produtoPagosPagamento.put(aux.getCodigo(), pagamentoVO.getProdutosPagos());
                            break;
                        }
                    }
                    if (!movPagamentosCancelar.isEmpty()) {
                        movimentacao.getMovPagamentosVOs().addAll(movPagamentosCancelar);
                    }
                    adicionarmovimento(movimentacao);
                    if (!movPagamentosCancelar.isEmpty()) {
                        ultimoMovimento.setMovPagamentosVOs(new ArrayList<MovPagamentoVO>());
                        for (MovPagamentoVO pagamentoVO : movPagamentosCancelar) {
                            ultimoMovimento.getMovPagamentosVOs().add(getFacade().getMovPagamento().consultarPorChavePrimaria(pagamentoVO.getCodigo(), Uteis.NIVELMONTARDADOS_TODOS));
                        }
                    }
                    
                } else {
                    movimentacao.setSaldoAtual(0.0);
                    incluirSemCommit(movimentacao);
                    for (MovPagamentoVO pagamentoVO : ultimoMovimento.getMovPagamentosVOs()) {
                        produtoPagosPagamento.put(pagamentoVO.getCodigo(), pagamentoVO.getProdutosPagos());
                        if (!recibos.contains(pagamentoVO.getReciboPagamento().getCodigo())) {
                            recibos.add(pagamentoVO.getReciboPagamento().getCodigo());
                        }
                    }
                }
                
                cancelarProdutosPagosComCredito(produtoPagosPagamento, ultimoMovimento);
                for (MovPagamentoVO pagamentoVO : ultimoMovimento.getMovPagamentosVOs()) {
                    pagamentoVO.setValor(0.0);
                    getFacade().getMovPagamento().alterarSemCommit(pagamentoVO);
                }
                
                ProdutosPagosServico produtosPagosServico = new ProdutosPagosServico();
                for (Integer codigoRecibo : recibos) {
                    produtosPagosServico.setarProdutosPagos(getCon(), codigoRecibo);
                }
                con.commit();
                return null;
            }
        } catch (Exception e) {
            con.rollback();
            e.printStackTrace();
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }
    
    public void cancelarProdutosPagosComCredito(Map<Integer, String> produtosPagosPagamento, MovimentoContaCorrenteClienteVO ultimoMovimento) throws Exception {
        Map<Integer, Double> produtosCancelar = new HashMap<Integer, Double>();
        Map<Integer, Map<Integer, Double>> produtosCancelarRecibo = new HashMap<Integer, Map<Integer, Double>>();
        MovProduto movprodutoDao = new MovProduto(con);
        List<Integer> orderProdutos = new ArrayList<Integer>();
        MovProdutoParcela movParDao = new MovProdutoParcela(con);
        for (MovPagamentoVO pagamento : ultimoMovimento.getMovPagamentosVOs()) {
            String produtosPagos = produtosPagosPagamento.get(pagamento.getCodigo());
            Double valorProdCancelar = Uteis.arredondarForcando2CasasDecimais(pagamento.getValor());
            if (produtosPagos != null && !produtosPagos.isEmpty()) {
                String[] prodPagos = produtosPagos.split("\\|");
                for (String prod : prodPagos) {
                    if (prod == null || prod.isEmpty()) {
                        continue;
                    }
                    String[] proddados = prod.split(",");
                    Double valorPagoProd = Double.valueOf(proddados[3]);
                    
                    if (Uteis.arredondarForcando2CasasDecimais(valorProdCancelar) < Uteis.arredondarForcando2CasasDecimais(valorPagoProd)) {
                        valorPagoProd = valorProdCancelar;
                    }
                    if (produtosCancelar.containsKey(Integer.valueOf(proddados[0]))) {
                        produtosCancelar.put(Integer.valueOf(proddados[0]), Uteis.arredondarForcando2CasasDecimais(valorPagoProd + produtosCancelar.get(Integer.valueOf(proddados[0]))));
                        if (produtosCancelarRecibo.get(Integer.valueOf(proddados[0])).containsKey(pagamento.getReciboPagamento().getCodigo())) {
                            produtosCancelarRecibo.get(Integer.valueOf(proddados[0])).put(pagamento.getReciboPagamento().getCodigo(), Uteis.arredondarForcando2CasasDecimais(valorPagoProd + produtosCancelarRecibo.get(Integer.valueOf(proddados[0])).get(pagamento.getReciboPagamento().getCodigo())));
                        } else {
                            produtosCancelarRecibo.get(Integer.valueOf(proddados[0])).put(pagamento.getReciboPagamento().getCodigo(), valorPagoProd);
                        }
                    } else {
                        produtosCancelar.put(Integer.valueOf(proddados[0]), Uteis.arredondarForcando2CasasDecimais(valorPagoProd));
                        Map<Integer, Double> produtosRecibo = new HashMap<Integer, Double>();
                        produtosRecibo.put(pagamento.getReciboPagamento().getCodigo(), valorPagoProd);
                        produtosCancelarRecibo.put(Integer.valueOf(proddados[0]), produtosRecibo);
                    }
                    if (!orderProdutos.contains(Integer.valueOf(proddados[0]))) { //só para tentar manter uma order no processamento, importante pra gerar os produtos pagos
                        orderProdutos.add(Integer.valueOf(proddados[0]));
                    }
                    valorProdCancelar = Uteis.arredondarForcando2CasasDecimais(valorProdCancelar - valorPagoProd);
                    if (valorProdCancelar == 0.0) {
                        break;
                    }
                }
            }
        }
        for (Integer codigoProd : orderProdutos) {
            MovProdutoVO movProd = movprodutoDao.consultarPorChavePrimaria(Integer.valueOf(codigoProd), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            Double valorPago = produtosCancelar.get(codigoProd);
            if (Uteis.arredondarForcando2CasasDecimais(valorPago) < Uteis.arredondarForcando2CasasDecimais(movProd.getTotalFinal())) {
                List<MovProdutoParcelaVO> movprodutoParcelas = movParDao.consultarPorCodigoMovProdutos(movProd.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                MovProdutoVO clone = (MovProdutoVO) movProd.getClone(false);
                movProd.setTotalFinal(Uteis.arredondarForcando2CasasDecimais(movProd.getTotalFinal() - valorPago));
                movProd.setPrecoUnitario(Uteis.arredondarForcando2CasasDecimais(movProd.getTotalFinal() / movProd.getQuantidade()));
                movProd.setValorFaturado(Uteis.arredondarForcando2CasasDecimais(movProd.getValorFaturado() - valorPago));
                movprodutoDao.alterarSemCommit(movProd);
                clone.setTotalFinal(valorPago);
                clone.setValorFaturado(valorPago);
                clone.setPrecoUnitario(Uteis.arredondarForcando2CasasDecimais(clone.getPrecoUnitario() - movProd.getPrecoUnitario()));
                clone.setSituacao("CA");
                movprodutoDao.incluirSemCommit(clone);
                Ordenacao.ordenarLista(movprodutoParcelas, "codigo");
                Collections.reverse(movprodutoParcelas);
                for (Integer codigoRecibo : produtosCancelarRecibo.get(codigoProd).keySet()) {
                    Double valorRecibo = produtosCancelarRecibo.get(codigoProd).get(codigoRecibo);
                    for (MovProdutoParcelaVO prodPar : movprodutoParcelas) {
                        
                        if (prodPar.getReciboPagamento().getCodigo().equals(codigoRecibo)) {
                            if (Uteis.arredondarForcando2CasasDecimais(prodPar.getValorPago()) <= Uteis.arredondarForcando2CasasDecimais(valorRecibo)) {
                                prodPar.setMovProduto(clone.getCodigo());
                                valorRecibo = Uteis.arredondarForcando2CasasDecimais(valorRecibo - prodPar.getValorPago());
                                valorPago = Uteis.arredondarForcando2CasasDecimais(valorPago - valorRecibo);
                                movParDao.alterar(prodPar);
                            } else {
                                MovProdutoParcelaVO mppClone = (MovProdutoParcelaVO) prodPar.getClone(true);
                                prodPar.setValorPago(Uteis.arredondarForcando2CasasDecimais(prodPar.getValorPago() - valorRecibo));
                                movParDao.alterar(prodPar);
                                mppClone.setValorPago(valorRecibo);
                                mppClone.setMovProduto(clone.getCodigo());
                                movParDao.incluir(mppClone);
                                valorPago = Uteis.arredondarForcando2CasasDecimais(valorPago - valorRecibo);
                                valorRecibo = 0.0;
                                
                            }
                            if (valorRecibo == 0.0) {
                                break;
                            }
                        }
                    }
                }
            } else {
                movProd.setSituacao("CA");
                movprodutoDao.alterarSemCommit(movProd);
            }
        }
    }
    
    public void deletarComposicao(Integer movimento, Integer movpagamento) throws Exception {
        executarConsulta("DELETE FROM movimentocontacorrenteclientecomposicao where movimentocontacorrentecliente = " + movimento
                + " and movpagamento = " + movpagamento, con);
    }
    
    public void inserirComposicao(Integer movimento, Integer movpagamento) throws Exception {
        executarConsulta("INSERT INTO movimentocontacorrenteclientecomposicao (movimentocontacorrentecliente, movpagamento) "
                + " VALUES (" + movimento + "," + movpagamento + ")", con);
    }
    
    public MovimentoContaCorrenteClienteVO consultarPorMovPagamento(Integer movpagamento, int nivelMontarDados) throws Exception {
        String sql = "select * from movimentocontacorrentecliente   where codigo in (select max(movimentocontacorrentecliente) from movimentocontacorrenteclientecomposicao  where movpagamento  = ?)";
        try (PreparedStatement sqlConsultar = con.prepareStatement(sql)) {
            sqlConsultar.setInt(1, movpagamento);
            try (ResultSet tabelaResultado = sqlConsultar.executeQuery()) {
                if (!tabelaResultado.next()) {
                    throw new ConsistirException("Dados Não Encontrados ( MovimentoContaCorrenteCliente ).");
                }
                return (montarDados(tabelaResultado, nivelMontarDados, con));
            }
        }
    }

    public boolean ajustarDebitoAlteracaoManutencaoHorario(MovPagamentoVO pagamentoatual, MovPagamentoVO devolvido, String tipoProduto, ContratoVO contrato, UsuarioVO usuario) throws Exception {
        try {
            MovimentoContaCorrenteClienteVO movimentoPagagamento = null;
            MovimentoContaCorrenteClienteVO movimentoAtual = null;
            try {                
                movimentoPagagamento = consultarPorMovPagamento(pagamentoatual.getCodigo(), Uteis.NIVELMONTARDADOS_TODOS);
                movimentoAtual = consultarPorCodigoPessoa(movimentoPagagamento.getPessoa().getCodigo(), Uteis.NIVELMONTARDADOS_TODOS);
            } catch (Exception e) {
                
            }
            boolean removerPagamento = Uteis.arredondarForcando2CasasDecimais(pagamentoatual.getValor()) == 0.0;
            if (movimentoPagagamento != null && movimentoAtual != null && movimentoAtual.getCodigo().equals(movimentoPagagamento.getCodigo())) {
                MovimentoContaCorrenteClienteVO novo = new MovimentoContaCorrenteClienteVO();
                novo.setPessoa(pagamentoatual.getPessoa());
                novo.setTipoMovimentacao("CR");
                novo.setDescricao("Estorno de débito por causa de " + (tipoProduto.equals("MM") ? "Manutenção de Modalidade" : "Alteração de Horário") + " no contrato " + contrato.getCodigo());
                novo.setResponsavelAutorizacao(usuario);
                novo.setValor(devolvido.getValor());
                novo.setSaldoAnterior(movimentoAtual.getSaldoAtual());
                novo.setSaldoAtual(Uteis.arredondarForcando2CasasDecimaisMantendoSinal(movimentoAtual.getSaldoAtual() + devolvido.getValor()));
                if (removerPagamento) {
                    for (MovPagamentoVO pag : movimentoAtual.getMovPagamentosVOs()) {
                        if (!pag.getCodigo().equals(pagamentoatual.getCodigo())) {
                            novo.getMovPagamentosVOs().add(pag);
                        }
                    }
                } else {
                    novo.setMovPagamentosVOs(movimentoAtual.getMovPagamentosVOs());
                }
                novo.setDataRegistro(Calendario.hoje());
                incluirSemCommit(novo);
            } else {
                MovParcelaVO parcela = getFacade().getMovParcela().consultarPorMovPagamentoContaCorrente(pagamentoatual.getCodigo().toString(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                if (parcela != null) {
                    parcela.setMovProdutoParcelaVOs(getFacade().getMovProdutoParcela().consultarPorCodigoMovParcela(parcela.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSENTIDADESPRINCIPAIS));
                    String[] pagamentosDebito = parcela.getMovPagamentoCC().split(",");
                    for (int j = 0; j < pagamentosDebito.length; j++) {
                        if (pagamentoatual.getCodigo().equals(Integer.parseInt(pagamentosDebito[j]))) {
                            if (pagamentosDebito.length > 1  || !removerPagamento) {
                                String novoMovPagamentoCC = "";
                                if(removerPagamento){
                                    for (int k = 0; k < pagamentosDebito.length; k++) {
                                        if (Integer.parseInt(pagamentosDebito[k]) != pagamentoatual.getCodigo().intValue()) {
                                            if (novoMovPagamentoCC.equals("")) {
                                                novoMovPagamentoCC = pagamentosDebito[k];
                                            } else {
                                                novoMovPagamentoCC += "," + pagamentosDebito[k];
                                            }

                                        }
                                    }
                                    
                                } else {
                                    novoMovPagamentoCC = parcela.getMovPagamentoCC();
                                }  
                                parcela.setMovPagamentoCC(novoMovPagamentoCC);
                                Double novoValor = Uteis.arredondarForcando2CasasDecimaisMantendoSinal(parcela.getValorParcela() - devolvido.getValor());
                                Iterator p = parcela.getMovProdutoParcelaVOs().iterator();
                                while (p.hasNext()) {
                                    MovProdutoParcelaVO produtoParcela = (MovProdutoParcelaVO) p.next();
                                    produtoParcela.setValorPago(novoValor);
                                    MovProdutoVO produto = getFacade().getMovProduto().consultarPorChavePrimaria(produtoParcela.getMovProduto(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                                    produto.setTotalFinal(novoValor);
                                    produto.setPrecoUnitario(novoValor);
                                    produto.setMovpagamentocc(novoMovPagamentoCC);
                                    getFacade().getMovProduto().alterarSemCommit(produto);
                                    getFacade().getMovProdutoParcela().alterar(produtoParcela);
                                }
                                parcela.setValorParcela(novoValor);
                                getFacade().getMovParcela().alterarSemCommit(parcela);
                                break;
                            } else {
                                MovProdutoVO produto = new MovProdutoVO();
                                try {
                                    produto = (MovProdutoVO) getFacade().getMovProduto().consultarPorCodigoParcela(parcela.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS).get(0);
                                } catch (Exception e) {
                                }
                                if(!UteisValidacao.emptyNumber(produto.getCodigo())){
                                    if (Uteis.arredondarForcando2CasasDecimais(produto.getTotalFinal()) > Uteis.arredondarForcando2CasasDecimais(pagamentoatual.getValorTotal())) {
                                        Iterator p = produto.getMovProdutoParcelaVOs().iterator();
                                        boolean produtopago = true;
                                        while (p.hasNext()) {
                                            MovProdutoParcelaVO produtoParcela = (MovProdutoParcelaVO) p.next();
                                            if (produtoParcela.getMovParcela().equals(parcela.getCodigo())) {
                                                produto.setPrecoUnitario(Uteis.arredondarForcando2CasasDecimaisMantendoSinal(produto.getTotalFinal() - pagamentoatual.getValorTotal()));
                                                produto.setTotalFinal(produto.getPrecoUnitario());
                                                getFacade().getMovProdutoParcela().excluir(produtoParcela);
                                                
                                            } else if(UteisValidacao.emptyNumber(produtoParcela.getReciboPagamento().getCodigo())) {
                                                produtopago = false;
                                            }
                                        }
                                        if(produtopago){
                                            produto.setSituacao("PG");
                                        }
                                        getFacade().getMovProduto().alterarSemCommit(produto);
                                    } else {
                                        EstornoMovProdutoVO estornoProduto = new EstornoMovProdutoVO();
                                        estornoProduto.getClienteVO().setPessoa(produto.getPessoa());
                                        estornoProduto.setMovProdutoVO(produto);
                                        estornoProduto.getListaMovProduto().add(produto);
                                        estornoProduto.setResponsavelEstorno(usuario);
                                        getFacade().getMovProduto().estornarMovProdutoSemCommit(estornoProduto, null);
                                    }
                                }
                                getFacade().getMovParcela().excluirSemCommit(parcela);
                                getFacade().getClienteMensagem().excluirClienteMensagemPorMovParcela(parcela.getCodigo());
                                break;
                            }
                        }
                    }
                }
            }
        } catch (Exception e) {
            return false;
        }
        return true;
        
    }
}
