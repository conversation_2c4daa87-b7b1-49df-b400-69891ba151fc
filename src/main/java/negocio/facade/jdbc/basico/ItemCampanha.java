/*
 * To change this template, choose Too<PERSON> | Templates
 * and open the template in the editor.
 */
package negocio.facade.jdbc.basico;

import negocio.comuns.CampanhaDuracaoVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.basico.ItemCampanhaVO;
import negocio.comuns.basico.enumerador.TipoItemCampanhaEnum;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.facade.jdbc.plano.Plano;
import negocio.facade.jdbc.plano.PlanoDuracao;
import negocio.facade.jdbc.plano.Produto;
import negocio.facade.jdbc.plano.Turma;
import negocio.interfaces.basico.ItemCampanhaInterfaceFacade;
import relatorio.negocio.comuns.basico.ListaPaginadaTO;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
public class ItemCampanha extends SuperEntidade implements ItemCampanhaInterfaceFacade {

    public ItemCampanha() throws Exception {
    }

    public ItemCampanha(Connection conexao) throws Exception {
        super(conexao);
    }

    @Override
    public void incluir(ItemCampanhaVO obj) throws Exception {
        incluir(getIdEntidade());
        StringBuilder sql = new StringBuilder();
        if (obj.getCampanha() != null && UteisValidacao.notEmptyNumber(obj.getCampanha().getCodigo())) {
            sql.append(" INSERT INTO itemcampanha(chaveestrangeira, tipoitem, duracao, pontos, empresa, campanha)");
            sql.append(" VALUES(?, ?, ?, ?, ?, ?)");
        } else if (obj.getEmpresa() == null) {
            sql.append(" INSERT INTO itemcampanha(chaveestrangeira, tipoitem, duracao, pontos)");
            sql.append(" VALUES(?, ?, ?, ?)");
        } else {
            sql.append(" INSERT INTO itemcampanha(chaveestrangeira, tipoitem, duracao, pontos, empresa)");
            sql.append(" VALUES(?, ?, ?, ?, ?)");
        }
        PreparedStatement sqlInserir = con.prepareStatement(sql.toString());
        sqlInserir.setInt(1, obj.getChaveestrangeira());
        sqlInserir.setInt(2, obj.getTipoItemCampanha().getCodigo());
        sqlInserir.setInt(3, obj.getDuracao());
        sqlInserir.setInt(4, obj.getPontos());
        if (obj.getEmpresa() != null)
            sqlInserir.setInt(5, obj.getEmpresa().getCodigo());
        if (obj.getCampanha() != null && UteisValidacao.notEmptyNumber(obj.getCampanha().getCodigo())) {
            sqlInserir.setInt(6, obj.getCampanha().getCodigo());
        }
        sqlInserir.execute();
        obj.setCodigo(obterValorChavePrimariaCodigo());
    }

    @Override
    public void alterar(ItemCampanhaVO obj) throws Exception {
        try {
            con.setAutoCommit(false);
            alterar(getIdEntidade());
            alterarSemCommit(obj);
            con.commit();
        } catch (Exception e) {
            con.rollback();
            con.setAutoCommit(true);
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    @Override
    public void excluir(boolean permissao, ItemCampanhaVO obj) throws Exception {
        try {
            if (permissao)
                excluir(getIdEntidade());
            con.setAutoCommit(false);
            excluirSemCommit(obj);
            con.commit();
        } catch (Exception e) {
            con.rollback();
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    public void excluirSemCommit(ItemCampanhaVO obj) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("DELETE FROM itemcampanha WHERE codigo = ? AND empresa = ?");
        PreparedStatement sqlExcluir = con.prepareStatement(sql.toString());
        sqlExcluir.setInt(1, obj.getCodigo());
        sqlExcluir.setInt(2, obj.getEmpresa().getCodigo());
        sqlExcluir.execute();
    }

    @Override
    public void alterarItemCampanhaIndicacao(List<ItemCampanhaVO> itensCampanhaVO) throws Exception {
        try {
            excluir(getIdEntidade());
            con.setAutoCommit(false);
            StringBuilder sql = new StringBuilder();

            sql.append(" DELETE FROM itemcampanha WHERE tipoitem=" + itensCampanhaVO.get(0).getTipoItemCampanha().getCodigo());
            sql.append(" AND empresa in (");
            List<Integer> empresas = new ArrayList<Integer>();
            for (ItemCampanhaVO itemCampanhaVO : itensCampanhaVO) {
                if (!empresas.contains(itemCampanhaVO.getEmpresa().getCodigo())) {
                    empresas.add(itemCampanhaVO.getEmpresa().getCodigo());
                    sql.append(itemCampanhaVO.getEmpresa().getCodigo() + ",");
                }
            }

            PreparedStatement statement = con.prepareStatement(sql.toString().substring(0, sql.lastIndexOf(",")) + ")");
            statement.execute();

            for (ItemCampanhaVO itemCampanhaVO : itensCampanhaVO) {
                incluir(itemCampanhaVO);
            }
            con.commit();
        } catch (Exception e) {
            con.rollback();
            con.setAutoCommit(true);
            throw e;
        }
    }

    @Override
    public List<ItemCampanhaVO> listaItemCampanha(CampanhaDuracaoVO campanha, Integer nivelMontarDados) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT * from itemcampanha\n");
        sql.append("WHERE campanha =").append(campanha.getCodigo()).append("\n");
        if (campanha.getEmpresa() != null) {
            sql.append("AND empresa=").append(campanha.getEmpresa().getCodigo()).append("\n");
        }

        try (Statement stm = con.createStatement();
             ResultSet tabelaResultado = stm.executeQuery(sql.toString())) {
            return montarDadosConsulta(tabelaResultado, nivelMontarDados, con);
        }
    }


    @Override
    public List<ItemCampanhaVO> consultaTotalizadorBiPontoOrderDesc(Date dataInicial, Date dataFinal, EmpresaVO empresa) throws Exception {
        List<ItemCampanhaVO> listaItemCampanha = new ArrayList<ItemCampanhaVO>();
        for (TipoItemCampanhaEnum tipoItemCampanhaEnum : TipoItemCampanhaEnum.getValuesEnun()) {
            ItemCampanhaVO itemCampanhaVO = new ItemCampanhaVO();
            itemCampanhaVO.setEmpresa(empresa);
            itemCampanhaVO.setTipoItemCampanha(tipoItemCampanhaEnum);
            itemCampanhaVO.setPontos(getFacade().getHistoricoPontos().obterPontosTotalPorTipoItemCampanha(itemCampanhaVO, dataInicial, dataFinal));
            listaItemCampanha.add(itemCampanhaVO);
        }
        listaItemCampanha.sort((inte1, inte2) -> inte2.getPontos().compareTo(inte1.getPontos()));
        return listaItemCampanha;
    }

    @Override
    public void alterarSemCommit(ItemCampanhaVO obj) throws Exception {


        alterar(getIdEntidade());
        StringBuilder sql = new StringBuilder();
        sql.append(" UPDATE itemcampanha set duracao = ?, pontos = ?, diasativos = ? " +
                " where codigo = ? and tipoitem = ? "
                + (UteisValidacao.notEmptyNumber(obj.getChaveestrangeira()) ? " AND chaveestrangeira=?" : "")
                + (obj.getEmpresa() != null && obj.getEmpresa().getCodigo() != 0 ? " AND empresa=?" : ""));
        PreparedStatement sqlAlterar = con.prepareStatement(sql.toString());
        int i = 1;
        sqlAlterar.setInt(i, obj.getDuracao());
        sqlAlterar.setInt(++i, obj.getPontos());

        if(obj.getDiasDaSemanaAtivos().isEmpty() || obj.getDiasDaSemanaAtivos().size() == 7) {
            sqlAlterar.setString(++i, "SEG,TER,QUA,QUI,SEX,SAB,DOM");
        } else {
            String diasAtivos = String.join(",", obj.getDiasDaSemanaAtivos());
            sqlAlterar.setString(++i, diasAtivos);
        }


        sqlAlterar.setInt(++i, obj.getCodigo());
        sqlAlterar.setInt(++i, obj.getTipoItemCampanha().getCodigo());
        if (UteisValidacao.notEmptyNumber(obj.getChaveestrangeira()))
            sqlAlterar.setInt(++i, obj.getChaveestrangeira());
        if (obj.getEmpresa() != null && obj.getEmpresa().getCodigo() != 0)
            sqlAlterar.setInt(++i, obj.getEmpresa().getCodigo());
        sqlAlterar.execute();
    }

    @Override
    public void alterarOuIncluir(ItemCampanhaVO itemCampanhaPlano) throws Exception {
        if (UteisValidacao.notEmptyNumber(itemCampanhaPlano.getCodigo()) || validarItemJaCadastradoCampanha(itemCampanhaPlano)) {
            alterar(itemCampanhaPlano);
        } else
            incluir(itemCampanhaPlano);
    }

    @Override
    public List<Integer> itensRemoverIndicaoEConvertida(EmpresaVO empresaSelecionada) throws Exception {
        StringBuilder sql = new StringBuilder();

        sql.append(" SELECT codigo FROM itemcampanha ");
        sql.append(" WHERE tipoitem in (" + TipoItemCampanhaEnum.INDICACAO_CONVERTIDA.getCodigo() + "," + TipoItemCampanhaEnum.INDICACAO.getCodigo() + ")");
        sql.append(" AND empresa = " + empresaSelecionada.getCodigo());
        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sql.toString());
        List<Integer> codigosExistentes = new ArrayList<>();
        while (tabelaResultado.next()) {
            codigosExistentes.add(tabelaResultado.getInt("codigo"));
        }
        return codigosExistentes;
    }


    public List<ItemCampanhaVO> montarDadosConsulta(ResultSet tabelaResultado, int nivelMontarDados, Connection con) throws SQLException, Exception {
        List<ItemCampanhaVO> listaItemCampanha = new ArrayList<ItemCampanhaVO>();
        while (tabelaResultado.next()) {
            ItemCampanhaVO itemCampanhaVO = montarDados(tabelaResultado, nivelMontarDados, con);
            listaItemCampanha.add(itemCampanhaVO);
        }
        return listaItemCampanha;
    }

    public ItemCampanhaVO montarDados(ResultSet tabelaResultado, int nivelMontarDados, Connection con) throws SQLException, Exception {
        Empresa empresa = new Empresa(con);
        ItemCampanhaVO itemCampanha = new ItemCampanhaVO();
        itemCampanha.setCodigo(tabelaResultado.getInt("codigo"));
        itemCampanha.setChaveestrangeira(tabelaResultado.getInt("chaveestrangeira"));
        itemCampanha.setDuracao(tabelaResultado.getInt("duracao"));
        itemCampanha.setTipoItemCampanha(TipoItemCampanhaEnum.getTipo(tabelaResultado.getInt("tipoitem")));
        itemCampanha.setEmpresa(empresa.consultarPorCodigo(tabelaResultado.getInt("empresa"), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
        itemCampanha.setPontos(tabelaResultado.getInt("pontos"));
        itemCampanha.setCampanha(new CampanhaDuracaoVO());
        itemCampanha.getCampanha().setCodigo(tabelaResultado.getInt("campanha"));
        try {
            String diasAtivos = tabelaResultado.getString("diasativos");
            List<String> diasAtivosArray = Arrays.asList(diasAtivos.split(","));
            itemCampanha.setDiasDaSemanaAtivos(diasAtivosArray);
        } catch (Exception ignored) {

        }
        if (!UteisValidacao.emptyNumber(itemCampanha.getChaveestrangeira()) && nivelMontarDados != Uteis.NIVELMONTARDADOS_DADOSBASICOS) {
            if (itemCampanha.getTipoItemCampanha() == TipoItemCampanhaEnum.PRODUTO) {
                Produto produto = new Produto(con);
                itemCampanha.setProdutoReferencia(produto.consultarPorChavePrimaria(itemCampanha.getChaveestrangeira(), Uteis.NIVELMONTARDADOS_TODOS));
                produto = null;
            }
            if (itemCampanha.getTipoItemCampanha() == TipoItemCampanhaEnum.PLANO) {
                Plano plano = new Plano(con);
                itemCampanha.setPlanoReferencia(plano.consultarPorChavePrimaria(itemCampanha.getChaveestrangeira(), Uteis.NIVELMONTARDADOS_TODOS));
                plano = null;
            }
            if (itemCampanha.getTipoItemCampanha() == TipoItemCampanhaEnum.AULA) {
                Turma aula = new Turma(con);
                itemCampanha.setAulaReferencia(aula.consultarPorCodigoTurmaColetiva(itemCampanha.getChaveestrangeira()));
                aula = null;
            }
            if (itemCampanha.getTipoItemCampanha() == TipoItemCampanhaEnum.ACESSO) {
                itemCampanha.setAcessoReferencia(empresa.consultarPorChavePrimaria(itemCampanha.getChaveestrangeira(), Uteis.NIVELMONTARDADOS_TODOS));

            }
            if (itemCampanha.getTipoItemCampanha() == TipoItemCampanhaEnum.ACESSOCHUVA) {
                itemCampanha.setAcessoReferencia(empresa.consultarPorChavePrimaria(itemCampanha.getChaveestrangeira(), Uteis.NIVELMONTARDADOS_TODOS));
            }
            if (itemCampanha.getTipoItemCampanha() == TipoItemCampanhaEnum.ACESSOFRIO) {
                itemCampanha.setAcessoReferencia(empresa.consultarPorChavePrimaria(itemCampanha.getChaveestrangeira(), Uteis.NIVELMONTARDADOS_TODOS));
            }
            if (itemCampanha.getTipoItemCampanha() == TipoItemCampanhaEnum.ACESSOCALOR) {
                itemCampanha.setAcessoReferencia(empresa.consultarPorChavePrimaria(itemCampanha.getChaveestrangeira(), Uteis.NIVELMONTARDADOS_TODOS));
            }
            if (itemCampanha.getTipoItemCampanha() == TipoItemCampanhaEnum.PLANODURACAO) {
                PlanoDuracao planoDuracao = new PlanoDuracao(con);
                try {
                    itemCampanha.setPlanoDuracaoReferencia(planoDuracao.consultarPorChavePrimaria(itemCampanha.getChaveestrangeira(), Uteis.NIVELMONTARDADOS_TODOS));
                } catch (ConsistirException ex) {
                    itemCampanha.setItemEstrangeiroExcluido(true);
                }
                planoDuracao = null;
            }
        }
        empresa = null;
        return itemCampanha;
    }

    @Override
    public List<ItemCampanhaVO> listaItemSemCampanha() throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT * from itemcampanha WHERE campanha is null limit 15");
        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sql.toString());
        return montarDadosConsulta(tabelaResultado, Uteis.NIVELMONTARDADOS_TODOS, con);
    }

    @Override
    public boolean validarItemJaCadastradoChaveEstrangeira(Integer codigo, Integer tipoitem, Integer empresa) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT * from itemcampanha where chaveestrangeira = " + codigo + " and tipoitem = " + tipoitem + (empresa == null ? "" : " and empresa=" + empresa));
        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sql.toString());
        if (!tabelaResultado.next()) {
            return false;
        } else {
            return true;
        }
    }

    @Override
    public boolean validarItemJaCadastradoCampanha(ItemCampanhaVO itemCampanha) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT * FROM itemcampanha WHERE " + (itemCampanha.getChaveestrangeira() != null ? " chaveestrangeira=" + itemCampanha.getChaveestrangeira() + "  AND " : "")
                + (itemCampanha.getCampanha() != null && UteisValidacao.notEmptyNumber(itemCampanha.getCampanha().getCodigo()) ? " codigo = " + itemCampanha.getCodigo() + " AND " : "")
                + ("tipoitem =" + itemCampanha.getTipoItemCampanha().getCodigo() + " AND empresa=" + itemCampanha.getEmpresa().getCodigo()));
        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sql.toString());
        if (!tabelaResultado.next()) {
            return false;
        } else {
            itemCampanha.setCodigo(tabelaResultado.getInt("codigo"));
            return true;
        }
    }

    @Override
    public ItemCampanhaVO consultarPorCampanhaChaveEstrangeira(Integer campanha, Integer chaveEstrangeria, int tipoitem, int nivelMontarDados) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT * FROM itemcampanha WHERE chaveestrangeira = " + chaveEstrangeria + (UteisValidacao.notEmptyNumber(campanha) ? " AND campanha = " + campanha : "") + " AND tipoitem =" + tipoitem);
        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sql.toString());
        if (!tabelaResultado.next()) {
            return new ItemCampanhaVO();
        }
        return montarDados(tabelaResultado, nivelMontarDados, con);
    }

    @Override
    public Integer consultarPorCampanhaVigenteChaveEstrangeira(Integer tipoitem, Integer empresa) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append(" select sum(pontos) total from itemcampanha i " +
                " inner join campanhaduracao c on i.campanha=c.codigo " +
                " where " +
                " now() between c.datainicial and c.datafinal " +
                " and i.chaveestrangeira =0 " +
                " and i.tipoitem = " + tipoitem +
                " and i.empresa=" + empresa);
        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sql.toString());
        if (!tabelaResultado.next()) {
            return 0;
        }
        return tabelaResultado.getInt("total");
    }

    /***
     *
     * @param empresa
     * @param chaveEstrangeira aceita NULL ou valor , Se passar NULL desconsidera
     * @param tipoitem
     * @return
     * @throws Exception
     */
    @Override
    public ItemCampanhaVO consultarPorChaveEstrangeira(Integer empresa, Integer chaveEstrangeira, Integer tipoitem) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT * FROM itemcampanha WHERE campanha is null and " + (chaveEstrangeira != null ? " chaveestrangeira = " + chaveEstrangeira + " and" : ""));
        sql.append(" tipoitem = " + tipoitem);
        sql.append(empresa != null ? " and (empresa is null or empresa = " + empresa + ")" : "");
        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sql.toString());
        if (!tabelaResultado.next()) {
            return new ItemCampanhaVO();
        }
        return montarDados(tabelaResultado, Uteis.NIVELMONTARDADOS_DADOSBASICOS, con);
    }

    @Override
    public ItemCampanhaVO consultarUnicosTipoXEmpresa(ItemCampanhaVO itemCampanha) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT * FROM itemcampanha WHERE " + (UteisValidacao.notEmptyNumber(itemCampanha.getEmpresa().getCodigo()) ? " empresa = " + itemCampanha.getEmpresa().getCodigo() + " and " : "")
                + " tipoitem = " + itemCampanha.getTipoItemCampanha().getCodigo()
                + (itemCampanha.getCampanha() == null ? " AND campanha is null " : (UteisValidacao.notEmptyNumber(itemCampanha.getCampanha().getCodigo()) ? " AND campanha=" + itemCampanha.getCampanha().getCodigo() : "")));
        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sql.toString());
        if (!tabelaResultado.next()) {
            return new ItemCampanhaVO();
        }
        return montarDados(tabelaResultado, Uteis.NIVELMONTARDADOS_DADOSBASICOS, con);
    }

    @Override
    public List<ItemCampanhaVO> listarItensPorTipo(Integer tipoItemCampanha, Integer empresa) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT * from itemcampanha WHERE  tipoitem = " + tipoItemCampanha + (empresa != 0 ? " and empresa=" + empresa : ""));
        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sql.toString());
        return montarDadosConsulta(tabelaResultado, Uteis.NIVELMONTARDADOS_DADOSBASICOS, con);
    }

    @Override
    public List<ItemCampanhaVO> listaItemCampanhaVigentePontuadoPlano(Integer empresa, Integer tipoItemCampanha, int nivelmontardadosTodos, ListaPaginadaTO paginadorListaPlanosCP) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append(" select i.codigo, i.duracao, i.pontos, i.campanha, i.chaveestrangeira, i.tipoitem, i.empresa, i.diasativos " +
                " from itemcampanha i " +
                " where  i.pontos>0 " +
                " and i.tipoitem = " + TipoItemCampanhaEnum.PLANO.getCodigo() +
                " and i.campanha is null " + (UteisValidacao.notEmptyNumber(empresa) ? " and i.empresa = " + empresa : "") +
                " or ( " +
                " i.chaveestrangeira in ( " +
                "  select distinct  " +
                "  p.codigo as chaveestrangeira " +
                "  from itemcampanha i2 " +
                "  inner join planoduracao pd on pd.codigo = i2.chaveestrangeira " +
                "  inner join plano p on pd.plano = p.codigo " + (UteisValidacao.notEmptyNumber(empresa) ? " and p.empresa = " + empresa : "") +
                "  where i2.pontos>0 and i2.tipoitem = " + TipoItemCampanhaEnum.PLANODURACAO.getCodigo() + " and i2.campanha is null " + (UteisValidacao.notEmptyNumber(empresa) ? " and i2.empresa = " + empresa : "") + ") " +
                " and i.tipoitem=" + TipoItemCampanhaEnum.PLANO.getCodigo() +
                (empresa != 0 ? " and i.empresa=" + empresa : "") + " )");

        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(" SELECT count(*) totalLinhas  from (" + sql.toString() + ") agrupaETotaliza ");
        if (tabelaResultado.next())
            paginadorListaPlanosCP.setCount(tabelaResultado.getInt("totalLinhas"));


        sql.append(" LIMIT " + paginadorListaPlanosCP.getLimit());
        sql.append(" OFFSET " + paginadorListaPlanosCP.getOffset());
        tabelaResultado = stm.executeQuery(sql.toString());

        List<ItemCampanhaVO> listaItemCampanha = new ArrayList<>();
        while (tabelaResultado.next()) {
            ItemCampanhaVO itemCampanhaVO = montarDados(tabelaResultado, nivelmontardadosTodos, con);
            listaItemCampanha.add(itemCampanhaVO);
        }
        return listaItemCampanha;
    }

    @Override
    public List<ItemCampanhaVO> listaItemCampanhaVigentePontuadoPorTipoPaginada(Integer empresa, Integer tipoItemCampanha, Integer nivelMontarDados, ListaPaginadaTO listaPaginadaTO) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append(" from itemcampanha i ");
        if (tipoItemCampanha == TipoItemCampanhaEnum.PRODUTO.getCodigo()) {
            sql.append(" INNER JOIN produto p ON i.chaveestrangeira=p.codigo ");
            sql.append(" AND NOT p.desativado AND p.tipoproduto NOT IN ('AC', 'AH', 'CC', 'DC', 'DV', 'RD', 'CH', 'AT', 'MM', 'PM', 'CD', 'QU', 'CP', 'DE', 'DR', 'TN', 'FR', 'TR', 'TP') ");
            sql.append(" AND (NOW() BETWEEN p.datainiciovigencia AND p.datafinalvigenciafixa OR (p.datainiciovigencia IS NULL AND p.datafinalvigenciafixa IS NULL)) ");
        } else if (tipoItemCampanha == TipoItemCampanhaEnum.AULA.getCodigo()) {
            sql.append(" INNER JOIN turma t ON  i.chaveestrangeira=t.codigo ");
            sql.append(" AND i.empresa=t.empresa ");
            sql.append(" AND NOW() BETWEEN t.datainicialvigencia AND t.datafinalvigencia ");
        }

        sql.append(" WHERE i.pontos > 0 AND " + (UteisValidacao.notEmptyNumber(empresa) ? " i.empresa =" + empresa + " AND " : ""));
        sql.append(" tipoitem =" + tipoItemCampanha);
        sql.append(" AND campanha IS NULL ");


        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(" SELECT count(*) totalLinhas " + sql.toString());
        if (tabelaResultado.next())
            listaPaginadaTO.setCount(tabelaResultado.getInt("totalLinhas"));

        sql.append(" LIMIT " + listaPaginadaTO.getLimit());
        sql.append(" OFFSET " + listaPaginadaTO.getOffset());

        tabelaResultado = stm.executeQuery(" SELECT * " + sql.toString());

        return montarDadosConsulta(tabelaResultado, nivelMontarDados, con);
    }


    @Override
    public void alterarPontos(Integer codigo, Integer pontos, Integer tipoitem) throws Exception {
        alterar(getIdEntidade());
        String sql = "update itemcampanha set pontos = ? where chaveestrangeira = ? and tipoitem = ?";
        PreparedStatement sqlAlterar = con.prepareStatement(sql);
        sqlAlterar.setInt(1, pontos);
        sqlAlterar.setInt(2, codigo);
        sqlAlterar.setInt(3, tipoitem);
        sqlAlterar.execute();
    }

    @Override
    public Boolean existeItemCampanha(Integer chaveEstrangeira, int tipoitem) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT * FROM itemcampanha WHERE chaveestrangeira = " + chaveEstrangeira + " and tipoitem =" + tipoitem);
        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sql.toString());
        if (!tabelaResultado.next()) {
            return false;
        }
        return true;

    }

}
